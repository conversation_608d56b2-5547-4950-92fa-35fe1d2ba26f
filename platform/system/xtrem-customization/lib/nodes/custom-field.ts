import { decorators, Node, Reference } from '@sage/xtrem-core';
import * as xtremMetadata from '@sage/xtrem-metadata';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremCustomization from '../index';

export interface CustomFieldEnumValue {
    name: string;
    title: string;
}

@decorators.node<CustomField>({
    isPublished: true,
    storage: 'sql',
    isCached: true,
    canCreate: true,
    canRead: true,
    canUpdate: true,
    canDeleteMany: true,
    canDelete: true,
    indexes: [
        {
            orderBy: { node: +1, name: +1 },
            isUnique: true,
            isNaturalKey: true,
        },
    ],
})
export class CustomField extends Node {
    @decorators.referenceProperty<CustomField, 'node'>({
        isStored: true,
        isPublished: true,
        node: () => xtremMetadata.nodes.MetaNodeFactory,
        isFrozen: true,
    })
    readonly node: Reference<xtremMetadata.nodes.MetaNodeFactory>;

    @decorators.stringProperty<CustomField, 'name'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.name,
        isFrozen: true,
    })
    readonly name: Promise<string>;

    @decorators.stringProperty<CustomField, 'title'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.localizedName,
        dependsOn: ['name'],
        defaultValue() {
            return this.name;
        },
    })
    readonly title: Promise<string>;

    @decorators.enumProperty<CustomField, 'dataType'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMetadata.enums.MetaPropertyTypeDataType,
        isFrozen: true,
    })
    readonly dataType: Promise<xtremMetadata.enums.MetaPropertyType>;

    @decorators.referenceProperty<CustomField, 'targetNode'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => xtremMetadata.nodes.MetaNodeFactory,
        isFrozen: true,
    })
    readonly targetNode: Reference<xtremMetadata.nodes.MetaNodeFactory | null>;

    @decorators.jsonProperty<CustomField, 'enumValues'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
    })
    readonly enumValues: Promise<CustomFieldEnumValue[] | null>;

    @decorators.enumProperty<CustomField, 'componentType'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremCustomization.enums.customFieldComponentTypeDataType,
    })
    readonly componentType: Promise<xtremCustomization.enums.CustomFieldComponentType>;

    @decorators.jsonProperty<CustomField, 'componentAttributes'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
    })
    readonly componentAttributes: Promise<any | null>;

    @decorators.referenceProperty<CustomField, 'anchorProperty'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => xtremMetadata.nodes.MetaNodeProperty,
        dependsOn: ['node'],
        filters: {
            lookup: {
                factory() {
                    return this.node;
                },
            },
            control: {
                factory() {
                    return this.node;
                },
            },
        },
    })
    readonly anchorProperty: Reference<xtremMetadata.nodes.MetaNodeProperty | null>;

    @decorators.enumProperty<CustomField, 'anchorPosition'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremCustomization.enums.customFieldAnchorPositionDataType,
        defaultValue() {
            return 'before';
        },
    })
    readonly anchorPosition: Promise<xtremCustomization.enums.CustomFieldAnchorPosition>;

    @decorators.enumArrayProperty<CustomField, 'destinationTypes'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        dataType: () => xtremCustomization.enums.customFieldDestinationTypeDataType,
    })
    readonly destinationTypes: Promise<xtremCustomization.enums.CustomFieldDestinationType[] | null>;

    @decorators.booleanProperty<CustomField, 'isActive'>({
        isStored: true,
        isPublished: true,
        defaultValue: true,
        provides: ['isActive'],
    })
    readonly isActive: Promise<boolean>;
}
