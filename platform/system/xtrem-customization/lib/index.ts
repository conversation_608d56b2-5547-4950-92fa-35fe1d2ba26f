import '@sage/xtrem-authorization';
import { CoreHooks } from '@sage/xtrem-core';
import '@sage/xtrem-metadata';
import * as activities from './activities/_index';
import * as enums from './enums/_index';
import * as nodes from './nodes/_index';
import * as services from './services/_index';

export { activities, enums, nodes, services };

CoreHooks.customizationManager = services.customizationManager;
