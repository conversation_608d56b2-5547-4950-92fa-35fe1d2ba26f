import { Activity } from '@sage/xtrem-core';
import { CustomField } from '../nodes/custom-field';

export const customField = new Activity({
    description: 'Custom field',
    node: () => CustomField,
    __filename,
    permissions: ['read', 'manage'],
    operationGrants: {
        manage: [
            {
                operations: ['read', 'create', 'update', 'delete'],
                on: [() => CustomField],
            },
        ],
    },
});
