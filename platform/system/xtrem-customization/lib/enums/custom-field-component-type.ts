import { EnumDataType } from '@sage/xtrem-core';

export enum CustomFieldComponentTypeEnum {
    checkboxField = 1,
    switchField = 2,
    textField = 3,
    dateField = 4,
    numericField = 5,
    selectField = 6,
}

export type CustomFieldComponentType = keyof typeof CustomFieldComponentTypeEnum;

export const customFieldComponentTypeDataType = new EnumDataType<CustomFieldComponentType>({
    enum: CustomFieldComponentTypeEnum,
    filename: __filename,
});
