import { EnumDataType } from '@sage/xtrem-core';

export enum CustomFieldDestinationTypeEnum {
    page = 1,
    navigationBar = 2,
    lookup = 3,
}

export type CustomFieldDestinationType = keyof typeof CustomFieldDestinationTypeEnum;

export const customFieldDestinationTypeDataType = new EnumDataType<CustomFieldDestinationType>({
    enum: CustomFieldDestinationTypeEnum,
    filename: __filename,
});
