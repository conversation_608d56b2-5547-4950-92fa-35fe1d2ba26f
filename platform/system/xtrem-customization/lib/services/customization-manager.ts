import { Context, getNameWithoutPackage } from '@sage/xtrem-core';
import { MetaCustomFields } from '@sage/xtrem-shared';
import * as xtremCustomization from '..';

export const customizationManager = {
    getWizardUrl(): string {
        return '@sage/xtrem-customization/CustomFieldWizard';
    },

    async getNodesCustomFields(context: Context, nodeNames: string[]): Promise<MetaCustomFields> {
        // only keep the last name. a/b/c/d -> d
        const namesWithoutPackage = nodeNames.map(getNameWithoutPackage);

        const customFields = await context.select(
            xtremCustomization.nodes.CustomField,
            {
                name: true,
                title: true,
                dataType: true,
                targetNode: { name: true },
                enumValues: true,
                componentType: true,
                componentAttributes: true,
                anchorProperty: { name: true },
                anchorPosition: true,
                destinationTypes: true,
                node: { name: true },
            },
            {
                filter: { node: { name: { _in: namesWithoutPackage } }, isActive: true },
            },
        );

        const getFullName = (name: string): string => {
            const factory = context.application.tryToGetFactoryByName(name);
            return factory ? factory.fullName : name;
        };

        return customFields.reduce((r, customField) => {
            const nodeFullName = getFullName(customField.node.name);
            r[nodeFullName] = r[nodeFullName] ?? [];
            r[nodeFullName].push({
                name: customField.name,
                title: customField.title,
                dataType: customField.dataType,
                targetNodeName: customField.targetNode?.name,
                enumValues: customField.enumValues || undefined,
                componentType: customField.componentType,
                componentAttributes: JSON.stringify(customField.componentAttributes || {}),
                anchorPropertyName: customField.anchorProperty?.name,
                anchorPosition: customField.anchorPosition,
                destinationTypes: customField.destinationTypes || undefined,
            });
            return r;
        }, {} as MetaCustomFields);
    },

    getCustomizableReferencedNodeName(context: Context, fullNodeName: string): string[] {
        const nameWithoutPackage = getNameWithoutPackage(fullNodeName);
        const factory = context.application.tryToGetFactoryByName(nameWithoutPackage);
        if (!factory) return [];

        const result = [] as string[];

        factory.mutableProperties.forEach(prop => {
            if (prop.isForeignNodeProperty()) {
                if (prop.targetFactory.isCustomizable) result.push(prop.targetFactory.fullName);
                result.push(...this.getCustomizableReferencedNodeName(context, prop.targetFactory.fullName));
            }
        });

        return result;
    },

    /**
     * Gets the UI metadata for custom fields
     */
    getCustomFields(
        context: Context,
        nodeNames: string[],
        options?: { includeMutableChildren: boolean },
    ): Promise<MetaCustomFields> {
        const allNodesNames = [...nodeNames];
        if (options?.includeMutableChildren) {
            allNodesNames.forEach(nodeName => {
                const referencedNodes = this.getCustomizableReferencedNodeName(context, nodeName);
                allNodesNames.push(...referencedNodes);
            });
        }
        return this.getNodesCustomFields(context, allNodesNames);
    },
};
