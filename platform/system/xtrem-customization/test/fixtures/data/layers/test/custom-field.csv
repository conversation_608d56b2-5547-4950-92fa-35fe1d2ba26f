"node";"name";"title";"data_type";"component_type";"anchor_position";"destination_types";"is_active"
"TestDocument";"comment";"{ ""base"": ""cool comment"" }";"string";"textField";"before";"[""page""]";"Y"
"TestDocument";"commentDate";"{ ""base"": ""comment date"" }";"date";"dateField";"before";"[""page""]";"Y"
"TestDocument";"size";"{ ""base"": ""document size"" }";"decimal";"numericField";"before";"[""page""]";"Y"
"TestDocumentLine";"amount";"{ ""base"": ""amount"" }";"decimal";"textField";"before";"[""page"", ""lookup""]";"Y"
"TestCustomFields";"activeField";"{ ""base"": ""active"" }";"string";"textField";"before";"[""page""]";"Y"
"TestCustomFields";"inactiveField";"{ ""base"": ""inactive field"" }";"string";"textField";"before";"[""page""]";"N"
