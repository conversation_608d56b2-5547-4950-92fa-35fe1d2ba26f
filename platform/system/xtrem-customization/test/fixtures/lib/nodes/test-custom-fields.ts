import { decorators, Node } from '@sage/xtrem-core';
import * as test from '../../test-application';

@decorators.node<TestCustomFields>({
    storage: 'sql',
    isPublished: true,
    canCreate: true,
    canUpdate: true,
    indexes: [
        {
            orderBy: { id: 1 },
            isUnique: true,
            isNaturalKey: true,
        },
    ],
})
export class TestCustomFields extends Node {
    @decorators.stringProperty<TestCustomFields, 'id'>({
        isStored: true,
        isPublished: true,
        dataType: () => test.dataTypes.customFieldId,
    })
    readonly id: Promise<string>;

    @decorators.stringProperty<TestCustomFields, 'name'>({
        isStored: true,
        isPublished: true,
        dataType: () => test.dataTypes.text,
    })
    readonly name: Promise<string>;
}
