import { Collection, date, decorators, Node } from '@sage/xtrem-core';
import * as test from '../../test-application';

@decorators.node<TestDocument>({
    storage: 'sql',
    isPublished: true,
    canCreate: true,
    canUpdate: true,
    indexes: [
        {
            orderBy: { id: 1 },
            isUnique: true,
            isNaturalKey: true,
        },
    ],
})
export class TestDocument extends Node {
    @decorators.stringProperty<TestDocument, 'id'>({
        isStored: true,
        isPublished: true,
        dataType: () => test.dataTypes.documentId,
    })
    readonly id: Promise<string>;

    @decorators.dateProperty<TestDocument, 'date'>({
        isStored: true,
        isPublished: true,
    })
    readonly date: Promise<date>;

    @decorators.collectionProperty<TestDocument, 'lines'>({
        isPublished: true,
        isVital: true,
        node: () => test.nodes.TestDocumentLine,
        reverseReference: 'document',
    })
    readonly lines: Collection<test.nodes.TestDocumentLine>;
}
