import { CoreHooks, Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import '../../fixtures/test-application';

const expectedTestDocumentFields = [
    {
        name: 'comment',
        title: 'cool comment',
        dataType: 'string',
        targetNodeName: undefined,
        enumValues: undefined,
        componentType: 'textField',
        componentAttributes: '{}',
        anchorPosition: 'before',
        anchorPropertyName: undefined,
        destinationTypes: ['page'],
    },
    {
        name: 'commentDate',
        title: 'comment date',
        dataType: 'date',
        targetNodeName: undefined,
        enumValues: undefined,
        componentType: 'dateField',
        componentAttributes: '{}',
        anchorPosition: 'before',
        anchorPropertyName: undefined,
        destinationTypes: ['page'],
    },
    {
        name: 'size',
        title: 'document size',
        dataType: 'decimal',
        targetNodeName: undefined,
        enumValues: undefined,
        componentType: 'numericField',
        componentAttributes: '{}',
        anchorPosition: 'before',
        anchorPropertyName: undefined,
        destinationTypes: ['page'],
    },
];

export const expectedTestDocumentLineFields = [
    {
        name: 'amount',
        title: 'amount',
        dataType: 'decimal',
        targetNodeName: undefined,
        enumValues: undefined,
        componentType: 'textField',
        componentAttributes: '{}',
        anchorPosition: 'before',
        anchorPropertyName: undefined,
        destinationTypes: ['page', 'lookup'],
    },
];

export const expectedTestCustomFields = [
    {
        name: 'activeField',
        title: 'active',
        dataType: 'string',
        targetNodeName: undefined,
        enumValues: undefined,
        componentType: 'textField',
        componentAttributes: '{}',
        anchorPosition: 'before',
        anchorPropertyName: undefined,
        destinationTypes: ['page'],
    },
];

describe('metadata endpoint', () => {
    it('gets custom fields with customization manager', async () => {
        // Read the metadata for TestDocument and TestDocumentLine
        await Test.withReadonlyContext(async context => {
            const customFields = await CoreHooks.customizationManager.getCustomFields(context, [
                '@sage/xtrem-customization/TestDocument',
                '@sage/xtrem-customization/TestDocumentLine',
            ]);
            assert.deepEqual(customFields, {
                '@sage/xtrem-customization/TestDocument': expectedTestDocumentFields,
                '@sage/xtrem-customization/TestDocumentLine': expectedTestDocumentLineFields,
            });
        });
    });

    it('gets custom fields with customization manager (includeMutableChildren option)', async () => {
        // Read the metadata for TestDocument and TestDocumentLine
        await Test.withReadonlyContext(async context => {
            const customFields = await CoreHooks.customizationManager.getCustomFields(
                context,
                ['@sage/xtrem-customization/TestDocument'],
                { includeMutableChildren: true },
            );
            assert.deepEqual(customFields, {
                '@sage/xtrem-customization/TestDocument': expectedTestDocumentFields,
                '@sage/xtrem-customization/TestDocumentLine': expectedTestDocumentLineFields,
            });
        });
    });

    it('gets custom fields without inactive field', async () => {
        await Test.withReadonlyContext(async context => {
            const customFields = await CoreHooks.customizationManager.getCustomFields(context, [
                '@sage/xtrem-customization/TestCustomFields',
            ]);
            assert.deepEqual(customFields, {
                '@sage/xtrem-customization/TestCustomFields': expectedTestCustomFields,
            });
        });
    });
});
