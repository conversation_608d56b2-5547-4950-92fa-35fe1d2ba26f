{"no parameters": {"input": {"queryParams": "", "selector": ""}, "output": {"edges": [{"cursor": "[\"DOC1\"]#55", "node": {"_customData": "{\"size\":\"5\",\"comment\":\"I love it\",\"commentDate\":\"2023-06-21\"}", "id": "DOC1"}}, {"cursor": "[\"DOC100\"]#07", "node": {"_customData": "{\"size\":\"15\",\"comment\":\"I don't care\",\"commentDate\":\"2023-06-21\"}", "id": "DOC100"}}, {"cursor": "[\"DOC101\"]#28", "node": {"_customData": "{\"comment\":\"I don't care either way\",\"commentDate\":\"2023-06-21\"}", "id": "DOC101"}}, {"cursor": "[\"DOC2\"]#82", "node": {"_customData": "{}", "id": "DOC2"}}, {"cursor": "[\"DOC3\"]#65", "node": {"_customData": "{\"size\":\"10\",\"comment\":\"I hate it\",\"commentDate\":\"2023-06-21\"}", "id": "DOC3"}}, {"cursor": "[\"DOC4\"]#16", "node": {"_customData": "{\"size\":\"20\",\"comment\":\"I dunno\",\"commentDate\":\"2023-06-22\"}", "id": "DOC4"}}]}}, "filter with regex": {"input": {"queryParams": "(filter: \"{ _customData: { comment: { _regex: 'hate' } } }\")", "selector": ""}, "output": {"edges": [{"cursor": "[\"DOC3\"]#65", "node": {"_customData": "{\"size\":\"10\",\"comment\":\"I hate it\",\"commentDate\":\"2023-06-21\"}", "id": "DOC3"}}]}}, "filter with _in": {"input": {"queryParams": "(filter: \"{ _customData: { comment: { _in: ['I hate it'] } } }\")", "selector": ""}, "output": {"edges": [{"cursor": "[\"DOC3\"]#65", "node": {"_customData": "{\"size\":\"10\",\"comment\":\"I hate it\",\"commentDate\":\"2023-06-21\"}", "id": "DOC3"}}]}}, "customData selector": {"input": {"queryParams": "", "selector": "(selector: \"comment\")"}, "output": {"edges": [{"cursor": "[\"DOC1\"]#55", "node": {"_customData": "\"I love it\"", "id": "DOC1"}}, {"cursor": "[\"DOC100\"]#07", "node": {"_customData": "\"I don't care\"", "id": "DOC100"}}, {"cursor": "[\"DOC101\"]#28", "node": {"_customData": "\"I don't care either way\"", "id": "DOC101"}}, {"cursor": "[\"DOC2\"]#82", "node": {"_customData": null, "id": "DOC2"}}, {"cursor": "[\"DOC3\"]#65", "node": {"_customData": "\"I hate it\"", "id": "DOC3"}}, {"cursor": "[\"DOC4\"]#16", "node": {"_customData": "\"I dunno\"", "id": "DOC4"}}]}}, "order by numeric field": {"input": {"queryParams": "(orderBy: \"{ _customData: { size: 1 } }\")", "selector": "(selector: \"size\")"}, "output": {"edges": [{"cursor": "[null,\"DOC101\"]#92", "node": {"_customData": null, "id": "DOC101"}}, {"cursor": "[null,\"DOC2\"]#15", "node": {"_customData": null, "id": "DOC2"}}, {"cursor": "[\"5\",\"DOC1\"]#33", "node": {"_customData": "\"5\"", "id": "DOC1"}}, {"cursor": "[\"10\",\"DOC3\"]#23", "node": {"_customData": "\"10\"", "id": "DOC3"}}, {"cursor": "[\"15\",\"DOC100\"]#42", "node": {"_customData": "\"15\"", "id": "DOC100"}}, {"cursor": "[\"20\",\"DOC4\"]#43", "node": {"_customData": "\"20\"", "id": "DOC4"}}]}}, "page with cursor(after)": {"input": {"queryParams": "(orderBy: \"{ _customData: { size: 1 } }\", after: \"[\\\"10\\\",\\\"DOC3\\\"]#23\")", "selector": "(selector: \"size\")"}, "output": {"edges": [{"cursor": "[\"15\",\"DOC100\"]#42", "node": {"_customData": "\"15\"", "id": "DOC100"}}, {"cursor": "[\"20\",\"DOC4\"]#43", "node": {"_customData": "\"20\"", "id": "DOC4"}}]}}, "page with cursor(before)": {"input": {"queryParams": "(orderBy: \"{ _customData: { size: 1 } }\", last:20, before: \"[\\\"10\\\",\\\"DOC3\\\"]#23\")", "selector": "(selector: \"size\")"}, "output": {"edges": [{"cursor": "[null,\"DOC101\"]#92", "node": {"_customData": null, "id": "DOC101"}}, {"cursor": "[null,\"DOC2\"]#15", "node": {"_customData": null, "id": "DOC2"}}, {"cursor": "[\"5\",\"DOC1\"]#33", "node": {"_customData": "\"5\"", "id": "DOC1"}}]}}}