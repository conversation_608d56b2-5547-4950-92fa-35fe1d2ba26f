{"group document lines by commentDate (custom field) and aggregate quantities (normal field)": {"input": {"queryParams": "", "group": "group { document { _customData(selector: \"commentDate\", by: day) } }", "values": "values { quantity { min, sum, max } }"}, "output": {"edges": [{"node": {"group": {"document": {"_customData": null}}, "values": {"quantity": {"max": "26", "min": "22", "sum": "48"}}}}, {"node": {"group": {"document": {"_customData": "\"2023-06-21\""}}, "values": {"quantity": {"max": "33", "min": "15", "sum": "97"}}}}]}}, "group document lines by commentDate (custom field) and aggregate amounts (custom field)": {"input": {"queryParams": "", "group": "group { document { _customData(selector: \"commentDate\", by: day) } }", "values": "values { _customData(selector: \"amount\") { min, sum, max } }"}, "output": {"edges": [{"node": {"group": {"document": {"_customData": null}}, "values": {"_customData": {"max": "2600", "min": "2200", "sum": "4800"}}}}, {"node": {"group": {"document": {"_customData": "\"2023-06-21\""}}, "values": {"_customData": {"max": "3300", "min": "1500", "sum": "9700"}}}}]}}, "group document lines by commentDate (custom field) and aggregate amounts (custom field) - aliases variant": {"input": {"queryParams": "", "group": "group { document { commentDate: _customData(selector: \"commentDate\", by: day) } }", "values": "values { amount: _customData(selector: \"amount\") { min, sum, max } }"}, "output": {"edges": [{"node": {"group": {"document": {"commentDate": null}}, "values": {"amount": {"max": "2600", "min": "2200", "sum": "4800"}}}}, {"node": {"group": {"document": {"commentDate": "\"2023-06-21\""}}, "values": {"amount": {"max": "3300", "min": "1500", "sum": "9700"}}}}]}}, "group document lines by date and aggregate amounts (custom field)": {"input": {"queryParams": "", "group": "group { document { date(by: day) } }", "values": "values { _customData(selector: \"amount\") { min, sum, max } }"}, "output": {"edges": [{"node": {"group": {"document": {"date": "2023-06-07"}}, "values": {"_customData": {"max": "1800", "min": "1500", "sum": "3300"}}}}, {"node": {"group": {"document": {"date": "2023-06-08"}}, "values": {"_customData": {"max": "3300", "min": "2200", "sum": "11200"}}}}]}}}