{"group document by date and aggregate size (custom field)": {"input": {"queryParams": "", "group": "group { date(by: day) }", "values": "values { _customData(selector: \"size\") { min, sum, avg, max } }"}, "output": {"edges": [{"node": {"group": {"date": "2023-06-07"}, "values": {"_customData": {"avg": "10", "max": "15", "min": "5", "sum": "20"}}}}, {"node": {"group": {"date": "2023-06-08"}, "values": {"_customData": {"avg": "15", "max": "20", "min": "10", "sum": "30"}}}}]}}}