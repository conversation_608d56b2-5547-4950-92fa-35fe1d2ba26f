declare module '@sage/xtrem-customization-api-partial' {
    import type { Package as SageXtremAuthorization$Package } from '@sage/xtrem-authorization-api';
    import type { Package as SageXtremCommunication$Package } from '@sage/xtrem-communication-api';
    import type {
        MetaNodeFactory,
        MetaNodeProperty,
        Package as SageXtremMetadata$Package,
    } from '@sage/xtrem-metadata-api';
    import type { Package as SageXtremSystem$Package, User } from '@sage/xtrem-system-api';
    import type {
        AggregateQueryOperation,
        AggregateReadOperation,
        AsyncOperation,
        ClientNode,
        ClientNodeInput,
        CreateOperation,
        DeleteOperation,
        GetDefaultsOperation,
        GetDuplicateOperation,
        QueryOperation,
        ReadOperation,
        UpdateByIdOperation,
        UpdateOperation,
        integer,
    } from '@sage/xtrem-client';
    export interface CustomFieldAnchorPosition$Enum {
        before: 1;
        after: 2;
    }
    export type CustomFieldAnchorPosition = keyof CustomFieldAnchorPosition$Enum;
    export interface CustomFieldComponentType$Enum {
        checkboxField: 1;
        switchField: 2;
        textField: 3;
        dateField: 4;
        numericField: 5;
        selectField: 6;
    }
    export type CustomFieldComponentType = keyof CustomFieldComponentType$Enum;
    export interface CustomFieldDestinationType$Enum {
        page: 1;
        navigationBar: 2;
        lookup: 3;
    }
    export type CustomFieldDestinationType = keyof CustomFieldDestinationType$Enum;
    export interface CustomField extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        node: MetaNodeFactory;
        name: string;
        title: string;
        dataType: MetaPropertyType;
        targetNode: MetaNodeFactory;
        enumValues: string;
        componentType: CustomFieldComponentType;
        componentAttributes: string;
        anchorProperty: MetaNodeProperty;
        anchorPosition: CustomFieldAnchorPosition;
        destinationTypes: CustomFieldDestinationType[];
        isActive: boolean;
    }
    export interface CustomFieldInput extends ClientNodeInput {
        node?: integer | string;
        name?: string;
        title?: string;
        dataType?: MetaPropertyType;
        targetNode?: integer | string;
        enumValues?: string;
        componentType?: CustomFieldComponentType;
        componentAttributes?: string;
        anchorProperty?: integer | string;
        anchorPosition?: CustomFieldAnchorPosition;
        destinationTypes?: CustomFieldDestinationType[];
        isActive?: boolean | string;
    }
    export interface CustomFieldBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        node: MetaNodeFactory;
        name: string;
        title: string;
        dataType: MetaPropertyType;
        targetNode: MetaNodeFactory;
        enumValues: any;
        componentType: CustomFieldComponentType;
        componentAttributes: any;
        anchorProperty: MetaNodeProperty;
        anchorPosition: CustomFieldAnchorPosition;
        destinationTypes: CustomFieldDestinationType[];
        isActive: boolean;
    }
    export interface CustomField$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface CustomField$Lookups {
        node: QueryOperation<MetaNodeFactory>;
        targetNode: QueryOperation<MetaNodeFactory>;
        anchorProperty: QueryOperation<MetaNodeProperty>;
    }
    export interface CustomField$Operations {
        query: QueryOperation<CustomField>;
        read: ReadOperation<CustomField>;
        aggregate: {
            read: AggregateReadOperation<CustomField>;
            query: AggregateQueryOperation<CustomField>;
        };
        create: CreateOperation<CustomFieldInput, CustomField>;
        getDuplicate: GetDuplicateOperation<CustomField>;
        update: UpdateOperation<CustomFieldInput, CustomField>;
        updateById: UpdateByIdOperation<CustomFieldInput, CustomField>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: CustomField$AsyncOperations;
        lookups(dataOrId: string | { data: CustomFieldInput }): CustomField$Lookups;
        getDefaults: GetDefaultsOperation<CustomField>;
    }
    export interface Package {
        '@sage/xtrem-customization/CustomField': CustomField$Operations;
    }
    export interface GraphApi
        extends Package,
            SageXtremAuthorization$Package,
            SageXtremCommunication$Package,
            SageXtremMetadata$Package,
            SageXtremSystem$Package {}
}
declare module '@sage/xtrem-customization-api' {
    export type * from '@sage/xtrem-customization-api-partial';
}
declare module '@sage/xtrem-authorization-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-customization-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-communication-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-customization-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-metadata-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-customization-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-system-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-customization-api';
    export interface GraphApi extends GraphApiExtension {}
}
