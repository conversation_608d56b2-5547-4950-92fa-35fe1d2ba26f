export interface AttributeValue {
    value: string;
}

export interface EnumValue {
    value: string;
}

export interface RemoteEnum {
    name: string;
    title: string;
}

export interface EnumAttributes {
    enum: Record<string, string | number>;
    name: string;
    type: string;
    title: string;
    values: EnumValues[];
    enumName: string;
    filename: string;
    isDefault: boolean;
}
export interface EnumValues {
    title: string;
    value: string;
}
export interface EnumObject {
    attributes: string;
}
