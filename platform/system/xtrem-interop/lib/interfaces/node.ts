export interface Mapping {
    localProperty: string;
    _id: string;
    direction: string;
    id: string;
    kind: string;
    remoteProperty: string;
}
export interface RemoteNode {
    name: string;
    title: string;
}

export interface FilteredObject {
    [key: string]: any;
}
export interface ReferenceItem {
    localProperty: string;
    _id: string;
}

export interface RemoteDataTypeInfo {
    name: string;
    title: string;
    attributes: object;
}

export interface RemoteNodeProperty {
    name: string;
    title: string;
    type: string;
    dataType?: string | null;
    isStored: boolean;
    targetNode?: string | null;
    isTransientInput: boolean;
    isStoredOutput: boolean;
    isRequired: boolean;
    isNullable: boolean;
    isCustom: boolean;
    canSort: boolean;
    canFilter: boolean;
    isOnInputType: boolean;
    isOnOutputType: boolean;
    isMutable: boolean;
    isVitalParent: boolean;
}

export interface RemoteNodeOperation {
    name: string;
    title: string;
    action?: string | null;
    kind: string;
    signature: { parameters: any[]; return: any };
    isMutation: boolean;
}

export interface RemoteNodeInfo {
    name: string;
    storage: string;
    packageName: string;
    title: string;
    isPlatformNode: boolean;
    naturalKey: string[] | null;
    properties: RemoteNodeProperty[];
    operations: RemoteNodeOperation[];
}
