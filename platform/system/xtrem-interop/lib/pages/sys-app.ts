import { Graph<PERSON><PERSON>, SysApp as SysAppNode } from '@sage/xtrem-interop-api';
import { setApplicativePageCrudActions } from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import * as ui from '@sage/xtrem-ui';
import { interop } from '../menu-items/interop';

@ui.decorators.page<SysApp, SysAppNode>({
    title: 'Connected applications',
    objectTypeSingular: 'Connected application',
    objectTypePlural: 'Connected applications',
    menuItem: interop,
    node: '@sage/xtrem-interop/SysApp',
    mode: 'tabs',
    module: 'xtrem-interop',
    idField() {
        return `- ${this.name.value}`;
    },
    createAction() {
        return this.$standardNewAction;
    },

    businessActions() {
        return [this.refreshActiveState, this.syncAllNodes, this.$standardCancelAction, this.$standardSaveAction];
    },

    onLoad() {
        setApplicativePageCrudActions({
            page: this,
            isDirty: false,
            save: this.$standardSaveAction,
            cancel: this.$standardCancelAction,
            duplicate: this.$standardDuplicateAction,
            remove: this.$standardDeleteAction,
        });
    },

    onDirtyStateUpdated(isDirty: boolean) {
        setApplicativePageCrudActions({
            page: this,
            isDirty,
            save: this.$standardSaveAction,
            cancel: this.$standardCancelAction,
            duplicate: this.$standardDuplicateAction,
            remove: this.$standardDeleteAction,
        });
    },

    navigationPanel: {
        optionsMenu: [
            { title: 'All', graphQLFilter: {} },
            { title: 'Active', graphQLFilter: { isActive: true } },
            { title: 'Inactive', graphQLFilter: { isActive: false } },
        ],

        canFilter: true,
        orderBy: { name: 1 },
        listItem: {
            title: ui.nestedFields.text({ bind: { name: true }, title: 'Name' }),
            line2: ui.nestedFields.text({ bind: { version: true }, title: 'Version' }),
            titleRight: ui.nestedFields.text({ bind: { title: true }, title: 'Title' }),
            line2Right: ui.nestedFields.switch({ bind: { isActive: true }, title: 'Active' }),
            isAlive: ui.nestedFields.checkbox({ bind: { isAlive: true }, title: 'Alive' }),
            isConnector: ui.nestedFields.checkbox({ bind: { isConnector: true }, title: 'Connector' }),
        },
    },
})
export class SysApp extends ui.Page<GraphApi> {
    // ============ Main Section and Blocks ================
    @ui.decorators.section<SysApp>({
        isOpen: true,
    })
    mainSection: ui.containers.Section;

    @ui.decorators.block<SysApp>({
        parent() {
            return this.mainSection;
        },
    })
    statusBlock: ui.containers.Block;

    @ui.decorators.block<SysApp>({
        parent() {
            return this.mainSection;
        },
    })
    mainBlock: ui.containers.Block;

    @ui.decorators.block<SysApp>({
        parent() {
            return this.mainSection;
        },
        title: 'Technical',
        isTitleHidden: true,
        width: 'large',
    })
    technicalBlock: ui.containers.Block;

    // ============ Screen Fields ================

    @ui.decorators.textField<SysApp>({
        title: 'Name',
        parent() {
            return this.mainBlock;
        },
        isReadOnly: true,
    })
    name: ui.fields.Text;

    @ui.decorators.textField<SysApp>({
        title: 'Title',
        parent() {
            return this.mainBlock;
        },
        isReadOnly: true,
    })
    title: ui.fields.Text;

    @ui.decorators.textField<SysApp>({
        title: 'Version',
        parent() {
            return this.mainBlock;
        },
        isReadOnly: true,
    })
    version: ui.fields.Text;

    @ui.decorators.selectField<SysApp>({
        title: 'Interop package',
        parent() {
            return this.technicalBlock;
        },
        options: ['xtrem-interop', 'xtrem-x3-interop'],
        isReadOnly: true,
    })
    interopPackage: ui.fields.Select;

    @ui.decorators.checkboxField<SysApp>({
        title: 'Connector',
        parent() {
            return this.technicalBlock;
        },
        width: 'small',
        isReadOnly: true,
    })
    isConnector: ui.fields.Checkbox;

    @ui.decorators.switchField<SysApp>({
        title: 'Active',
        parent() {
            return this.statusBlock;
        },
        width: 'small',
        // validation(value) {
        //     if (value) {
        //         return this.checkOtherActiveApps();
        //     }
        // }
    })
    isActive: ui.fields.Switch;

    @ui.decorators.checkboxField<SysApp>({
        title: 'Alive',
        isReadOnly: true,
        parent() {
            return this.statusBlock;
        },
        width: 'small',
    })
    isAlive: ui.fields.Checkbox;

    // ============ Page Actions ================
    @ui.decorators.pageAction<SysApp>({
        title: 'Refresh active state',
        async onClick() {
            await this.$.graph.node('@sage/xtrem-interop/SysApp').mutations.refreshState(true, {}).execute();
            this.$.showToast('state has been refreshed (but not the page)');
        },
    })
    refreshActiveState: ui.PageAction;

    @ui.decorators.pageAction<SysApp>({
        title: 'Sync all',
        isDisabled() {
            return !this.isActive.value;
        },
        access: { node: '@sage/xtrem-interop/SysSynchronizationTarget', bind: 'synchronize' },
        async onClick() {
            await this.$.graph
                .node('@sage/xtrem-interop/SysSynchronizationTarget')
                .asyncOperations.synchronize.start(
                    { trackingId: true },
                    {
                        remoteApp: `#${this.name.value}`,
                        localNodeNames: [],
                        skipDependencies: false,
                    },
                )
                .execute();
            this.$.showToast(ui.localize('@sage/xtrem-interop/pages__sys_app__sync_all_message', 'Sync started.'), {
                timeout: 3000,
                type: 'info',
            });
        },
    })
    syncAllNodes: ui.PageAction;
}
