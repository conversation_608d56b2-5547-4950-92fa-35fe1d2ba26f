import { Datetime } from '@sage/xtrem-date-time';
import { Graph<PERSON>pi, SysNodeMapping, SysNodeTransformation as SysNodeTransformationNode } from '@sage/xtrem-interop-api';
import type { MetaNodeFactory } from '@sage/xtrem-metadata-api';
import type { GraphApi as SchedulerGraph<PERSON>pi } from '@sage/xtrem-scheduler-api';
import { recurring } from '@sage/xtrem-scheduler/build/lib/client-functions/job-execution';
import { scheduleWizard } from '@sage/xtrem-scheduler/build/lib/client-functions/job-schedule';
import { NodeDetails, Property, TreeElement } from '@sage/xtrem-shared';
import {
    setApplicativePageCrudActions,
    setOrderOfPageHeaderDropDownActions,
    setOrderOfPageHeaderQuickActions,
    setOrderOfPageTableHeaderBusinessActions,
} from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import * as ui from '@sage/xtrem-ui';
import * as _ from 'lodash';
import type { Mapping, RemoteNode, RemoteNodeProperty } from '../interfaces/node';
import { interop } from '../menu-items/interop';

@ui.decorators.page<SysNodeTransformation, SysNodeTransformationNode>({
    title: 'Synchronization transforms',
    objectTypeSingular: 'Data transformation',
    objectTypePlural: 'Data transformations',
    menuItem: interop,
    node: '@sage/xtrem-interop/SysNodeTransformation',
    mode: 'tabs',
    module: 'xtrem-interop',
    idField() {
        return `- ${this.id.value}`;
    },

    createAction() {
        return this.$standardNewAction;
    },

    headerQuickActions() {
        return setOrderOfPageHeaderQuickActions({
            duplicate: [this.$standardDuplicateAction],
        });
    },

    headerDropDownActions() {
        return setOrderOfPageHeaderDropDownActions({
            remove: [this.$standardDeleteAction],
        });
    },

    businessActions() {
        return [this.$standardCancelAction, this.$standardSaveAction, this.syncNow, this.scheduleSync];
    },

    headerSection() {
        return this.mainSection;
    },

    async onLoad() {
        setApplicativePageCrudActions({
            page: this,
            isDirty: false,
            save: this.$standardSaveAction,
            cancel: this.$standardCancelAction,
            duplicate: this.$standardDuplicateAction,
            remove: this.$standardDeleteAction,
        });

        // In creation mode the initial value is null even if shown as active. So, let's initialize it.
        if (!this.$.recordId) {
            await this.$.fetchDefaults(['isActive']);
        }

        if (this.localNode.value) {
            this.setLocalNodeFullName();
            this.populateLocalPropertyTree(this.localNodeFullName);
        }

        this.updateAvailabilityOfPageElements();
    },

    onDirtyStateUpdated(isDirty: boolean) {
        setApplicativePageCrudActions({
            page: this,
            isDirty,
            save: this.$standardSaveAction,
            cancel: this.$standardCancelAction,
            duplicate: this.$standardDuplicateAction,
            remove: this.$standardDeleteAction,
        });
    },

    navigationPanel: {
        optionsMenu: [
            { title: 'Active', graphQLFilter: { isActive: true } },
            { title: 'Inactive', graphQLFilter: { isActive: false } },
            { title: 'All', graphQLFilter: {} },
        ],
        listItem: {
            title: ui.nestedFields.text({ bind: { id: true }, title: 'Mapping ID' }),
            titleRight: ui.nestedFields.switch({ bind: { isActive: true }, title: 'Active' }),
            name: ui.nestedFields.text({ bind: { remoteApp: { name: true } }, title: 'Remote app' }),
            remoteNodeName: ui.nestedFields.text({ bind: { remoteNodeName: true }, title: 'Remote entity' }),
            remoteAppVersion: ui.nestedFields.text({ bind: { remoteAppVersion: true }, title: 'Remote app version' }),
            localNode: ui.nestedFields.text({ bind: { localNode: { name: true } }, title: 'Local entity' }),
            lastError: ui.nestedFields.text({ bind: { lastError: true }, title: 'Last Error' }),
            lastSync: ui.nestedFields.text({ bind: { lastSync: true }, title: 'Last sync' }),
        },
        dropdownActions: [
            ui.menuSeparator(),
            {
                icon: 'sync',
                title: 'Sync now',
                onClick(rowId: string) {
                    this.$.showToast(`Sync now - row ${rowId}`);
                },
            },
            ui.menuSeparator(),
            {
                icon: 'none',
                title: 'Schedule sync',
                onClick(rowId: string) {
                    this.$.showToast(`Schedule sync - row ${rowId}`);
                },
            },
            ui.menuSeparator(),
            {
                icon: 'duplicate',
                title: 'Duplicate',
                onClick(rowId: string) {
                    this.$.showToast(`Duplicate - row ${rowId}`);
                    this.$standardDuplicateAction.execute(false, rowId);
                },
            },
            ui.menuSeparator(),
            {
                icon: 'file_generic',
                title: 'Display log',
                onClick(rowId: string) {
                    this.$.showToast(`Display log - row ${rowId}`);
                },
            },
        ],
        inlineActions: [
            {
                title: 'Duplicate',
                icon: 'duplicate',
                onClick(rowId: string) {
                    this.$.showToast(`${rowId}`);
                    this.$standardDuplicateAction.execute(false, rowId);
                },
            },
        ],
    },
})
export class SysNodeTransformation extends ui.Page<GraphApi> {
    localNodeFullName: string;

    // ============ Main Section and Blocks ================
    @ui.decorators.section<SysNodeTransformation>({})
    mainSection: ui.containers.Section;

    @ui.decorators.block<SysNodeTransformation>({
        parent() {
            return this.mainSection;
        },
    })
    statusBlock: ui.containers.Block;

    @ui.decorators.block<SysNodeTransformation>({
        parent() {
            return this.mainSection;
        },
    })
    localBlock: ui.containers.Block;

    @ui.decorators.block<SysNodeTransformation>({
        parent() {
            return this.mainSection;
        },
    })
    remoteBlock: ui.containers.Block;

    // ============ Mapping Section and Blocks =====================
    @ui.decorators.section<SysNodeTransformation>({
        title: 'Mapping',
    })
    mappingSection: ui.containers.Section;

    @ui.decorators.block<SysNodeTransformation>({
        parent() {
            return this.mappingSection;
        },
        title: 'Mapping table',
    })
    mappingBlock: ui.containers.Block;

    // ============ Sync Section and Blocks =====================
    @ui.decorators.section<SysNodeTransformation>({
        title: 'Sync',
    })
    syncSection: ui.containers.Section;

    @ui.decorators.block<SysNodeTransformation>({
        parent() {
            return this.syncSection;
        },
    })
    syncDetailsBlock: ui.containers.Block;

    // ========= Local Properties Section and Blocks =============
    @ui.decorators.section<SysNodeTransformation>({
        title: 'Local properties',
        isHidden: true,
    })
    localPropertiesSection: ui.containers.Section;

    @ui.decorators.block<SysNodeTransformation>({
        parent() {
            return this.localPropertiesSection;
        },
        title: 'Local properties',
        isTitleHidden: true,
        width: 'medium',
    })
    localPropertiesBlock: ui.containers.Block;

    // ========= Remote Properties Section and Blocks =============
    @ui.decorators.section<SysNodeTransformation>({
        title: 'Remote properties',
        isHidden: true,
    })
    remotePropertiesSection: ui.containers.Section;

    @ui.decorators.block<SysNodeTransformation>({
        parent() {
            return this.remotePropertiesSection;
        },
        title: 'Remote properties',
        width: 'medium',
    })
    remotePropertiesBlock: ui.containers.Block;

    // ============ dialog section / blocks  ===============

    @ui.decorators.section<SysNodeTransformation>({
        title: 'Select remote entity',
        isHidden: true,
    })
    dialogSection: ui.containers.Section;

    @ui.decorators.block<SysNodeTransformation>({
        parent() {
            return this.dialogSection;
        },
        title: 'Remote entity',
    })
    dialogBlock: ui.containers.Block;

    // ============= Hidden fields ==============
    @ui.decorators.textField<SysNodeTransformation>({
        bind: '_vendor.name',
    })
    vendor: ui.fields.Text;

    // ============= Screen Fields Status Block ==============

    @ui.decorators.switchField<SysNodeTransformation>({
        parent() {
            return this.statusBlock;
        },
        title: 'Active',
        width: 'small',
        onChange() {
            this.updateAvailabilityOfPageElements();
        },
    })
    isActive: ui.fields.Switch;

    // ============= Screen Fields Local Block ==============

    @ui.decorators.textField<SysNodeTransformation>({
        parent() {
            return this.localBlock;
        },
        title: 'Mapping ID',
        isMandatory: true,
    })
    id: ui.fields.Text;

    @ui.decorators.referenceField<SysNodeTransformation, MetaNodeFactory>({
        parent() {
            return this.localBlock;
        },
        title: 'Local entity',
        isMandatory: true,
        isTransient: false,
        width: 'medium',
        node: '@sage/xtrem-metadata/MetaNodeFactory',
        valueField: 'name',
        orderBy: {
            name: 1,
        },
        columns: [
            ui.nestedFields.text({ bind: 'name', title: 'name' }),
            ui.nestedFields.text({ bind: 'title', title: 'title' }),
            ui.nestedFields.text({
                bind: { package: { name: true } },
                title: 'Package',
            }),
            ui.nestedFields.text({ bind: 'storage', title: 'storage', isHidden: true }),
        ],
        async onChange() {
            if (this.localNode.value) {
                this.setLocalNodeFullName();
                this.populateLocalPropertyTree(this.localNodeFullName);
            }
            this.updateAvailabilityOfPageElements();
        },
    })
    localNode: ui.fields.Reference<MetaNodeFactory>;

    // ============= Screen Fields Remote Block ==============
    @ui.decorators.referenceField<SysNodeTransformation>({
        parent() {
            return this.remoteBlock;
        },
        filter() {
            return {
                isActive: true,
            };
        },
        title: 'Remote app',
        isMandatory: true,
        width: 'large',
        node: '@sage/xtrem-interop/SysApp',
        valueField: 'name',
        onChange() {
            this.updateAvailabilityOfPageElements();
        },
    })
    remoteApp: ui.fields.Reference;

    @ui.decorators.textField<SysNodeTransformation>({
        parent() {
            return this.remoteBlock;
        },
        title: 'Remote version',
        bind: 'remoteAppVersion',
        isMandatory: true,
    })
    remoteVersion: ui.fields.Text;

    @ui.decorators.textField<SysNodeTransformation>({
        parent() {
            return this.remoteBlock;
        },
        title: 'Remote entity',
        isMandatory: true,
        isReadOnly: true,
        onChange() {
            this.updateAvailabilityOfPageElements();
        },
    })
    remoteNodeName: ui.fields.Text;

    @ui.decorators.buttonField<SysNodeTransformation>({
        isTransient: true,
        width: 'small',
        parent() {
            return this.remoteBlock;
        },
        map() {
            return 'Select remote entity';
        },
        async onClick() {
            if (this.remoteApp.value) {
                await this.populateRemoteNodeTable(this.remoteApp.value?.name);
                this.dialogSection.isHidden = false;
                await this.$.dialog.custom('info', this.dialogSection, {
                    size: 'large',
                    resolveOnCancel: true,
                    cancelButton: { isHidden: false },
                    acceptButton: { isHidden: false },
                });
                this.dialogSection.isHidden = true;
                this.updateAvailabilityOfPageElements();
            }
        },
    })
    selectRemoteEntity: ui.fields.Button;

    @ui.decorators.nodeBrowserTreeField<SysNodeTransformation>({
        parent() {
            return this.localPropertiesBlock;
        },
        isTransient: true,
    })
    localPropertyTree: ui.fields.NodeBrowserTree;

    @ui.decorators.nodeBrowserTreeField<SysNodeTransformation>({
        parent() {
            return this.remotePropertiesBlock;
        },
        node: '@sage/xtrem-interop/SysRemoteMetadata',
        isTransient: true,
        async fetchItems(parent: Property) {
            const remoteApp: string | null = this.remoteApp.value?.name;
            const remoteNode: string | null = this.remoteNodeName.value;
            const remoteNodeProperties: Partial<RemoteNodeProperty>[] = (
                await this.getRemoteNodeProperties(remoteApp, remoteNode)
            )[0].properties;

            const treeProperties: Property[] = remoteNodeProperties.map(property => {
                const name = property.name ?? '';
                const kind = property.targetNode ? 'OBJECT' : 'SCALAR';
                const key = parent.key ? `${parent.key}.${name}` : name;
                const canBeExpanded = kind === 'OBJECT';

                return {
                    data: {
                        ...property,
                        name,
                        enumType: null as any,
                        canSort: property.canSort ?? false,
                        canFilter: property.canFilter ?? false,
                        type: property.type ?? '',
                        targetNode: property.targetNode ?? '',
                        isCustom: property.isCustom ?? false,
                        dataType: property.dataType ?? '',
                        isOnInputType: property.isOnInputType ?? false,
                        isOnOutputType: property.isOnOutputType ?? false,
                        isMutable: property.isMutable ?? false,
                        kind,
                        label: name,
                        isStored: property.isStored ?? false,
                    },
                    key,
                    id: key,
                    labelKey: key,
                    labelPath: key,
                    label: name,
                    canBeExpanded,
                    canBeSelected: true,
                };
            });

            return Promise.resolve(treeProperties);
        },
    })
    remotePropertyTree: ui.fields.NodeBrowserTree;

    @ui.decorators.tableField<SysNodeTransformation, SysNodeMapping>({
        parent() {
            return this.mappingBlock;
        },
        node: '@sage/xtrem-interop/SysNodeMapping',
        canAddNewLine: true,
        canExport: true,
        canFilter: true,
        canSelect: false,
        canResizeColumns: true,
        isTitleHidden: true,
        isFullWidth: true,
        orderBy: { localProperty: 1 },
        bind: 'map',
        columns: [
            ui.nestedFields.text({
                bind: 'localProperty',
                canFilter: true,
                title: 'Local value',
            }),
            ui.nestedFields.dropdownList({
                title: 'Mapping type',
                canFilter: true,
                bind: 'kind',
                optionType: '@sage/xtrem-interop/NodeMappingKind',
            }),
            ui.nestedFields.text({
                bind: 'remoteProperty',
                canFilter: true,
                title: 'Remote value',
            }),
        ],
        dropdownActions: [
            {
                icon: 'edit',
                title: 'Edit remote property',
                isDisabled(_rowId, rowData) {
                    return !SysNodeTransformation.MappingTypeHasRemoteProperty(rowData.kind);
                },
                async onClick(_id: string) {
                    this.updateRemoteNodeTree();
                    this.remotePropertiesSection.isHidden = false;
                    await this.$.dialog
                        .custom('info', this.remotePropertiesSection, { size: 'large' })
                        .then(async () => {
                            this.syncMappingTableWithRemotePropertySelection(_id);
                        })
                        .finally(() => {
                            this.remotePropertiesSection.isHidden = true;
                        });
                },
            },
            {
                title: 'Refresh record',
                icon: 'refresh',
                async onClick(rowId) {
                    this.mappingTable.refreshRecord(rowId);
                },
            },
            {
                icon: 'minus',
                title: 'Remove',
                isDestructive: true,
                async onClick(rowId: any) {
                    this.mappingTable.removeRecord(rowId);
                },
            },
        ],
        headerBusinessActions() {
            return setOrderOfPageTableHeaderBusinessActions({
                actions: [this.selectFromLocalProperties],
            });
        },
    })
    mappingTable: ui.fields.Table;

    @ui.decorators.dateField<SysNodeTransformation>({
        parent() {
            return this.syncDetailsBlock;
        },
        title: 'Last Sync',
        bind: 'lastSync',
        isReadOnly: true,
    })
    lastSyncDate: ui.fields.Date;

    @ui.decorators.dateField<SysNodeTransformation>({
        parent() {
            return this.syncDetailsBlock;
        },
        title: 'Last Sync Error',
        bind: 'lastError',
        isReadOnly: true,
    })
    lastErrorDate: ui.fields.Date;

    @ui.decorators.textAreaField<SysNodeTransformation>({
        parent() {
            return this.syncDetailsBlock;
        },
        isTransient: true,
        isReadOnly: true,
        title: 'Synchronization results',
        isFullWidth: true,
        rows: 20,
    })
    synchronizeResult: ui.fields.TextArea;

    @ui.decorators.tableField<SysNodeTransformation>({
        canFilter: true,
        canExport: true,
        canResizeColumns: true,
        parent() {
            return this.dialogSection;
        },
        canAddNewLine: true,
        isPhantomRowDisabled: true,
        isTransient: true,
        canSelect: false,
        isFullWidth: true,
        bind: 'name',
        isReadOnly: true,
        isChangeIndicatorDisabled: true,

        async onRowClick(_recordId, rowItem) {
            this.remoteNodeName.value = rowItem.name;
        },
        columns: [
            ui.nestedFields.text({
                canFilter: true,
                bind: 'name',
                title: 'Name',
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                bind: 'title',
                title: 'Title',
                isReadOnly: true,
            }),
        ],
    })
    listOfRemoteNodes: ui.fields.Table;

    // ============ Page Actions =====================

    @ui.decorators.pageAction<SysNodeTransformation>({
        icon: 'search',
        title: 'Add lines from local properties',
        async onClick() {
            this.updateLocalNodeTree();
            this.localPropertiesSection.isHidden = false;
            await this.$.dialog
                .custom('info', this.localPropertiesSection, { size: 'large' })
                .then(async () => {
                    this.syncMappingTableWithLocalPropertySelection();
                })
                .finally(() => {
                    this.localPropertiesSection.isHidden = true;
                });
        },
    })
    selectFromLocalProperties: ui.PageAction;

    @ui.decorators.pageAction<SysNodeTransformation>({
        title: 'Sync now',
        access: { node: '@sage/xtrem-interop/SysSynchronizationTarget', bind: 'synchronize' },
        async onClick() {
            const remoteApp = this.remoteApp.value?.name;
            const localNodeName = this.localNode.value?.name;
            if (!remoteApp || !localNodeName) return;
            try {
                const results = await this.$.graph
                    .node('@sage/xtrem-interop/SysSynchronizationTarget')
                    .asyncOperations.synchronize.runToCompletion(
                        { localNodeName: true, created: true, updated: true, skipped: true },
                        {
                            remoteApp: `#${remoteApp}`,
                            localNodeNames: [`#${localNodeName}`],
                            skipDependencies: false,
                        },
                    )
                    .execute();
                this.synchronizeResult.value = `${Datetime.now(true)}: ${JSON.stringify(results)}`;
            } catch (error) {
                try {
                    this.synchronizeResult.value = `Synchronization failed:\n${JSON.stringify(JSON.parse(error.message), null, 2)}`;
                } catch {
                    this.synchronizeResult.value = `Synchronization failed:\n${error.message}`;
                }
            }
        },
    })
    syncNow: ui.PageAction;

    @ui.decorators.pageAction<SysNodeTransformation>({
        title: 'Schedule synchronization',
        access: { node: '@sage/xtrem-interop/SysSynchronizationTarget', bind: 'synchronize' },
        async onClick() {
            const remoteApp = this.remoteApp.value?.name;
            const localNodeName = this.localNode.value?.name;
            if (!remoteApp || !localNodeName) return;
            /** as unknown as graphApi ... because actually we are not able to import api of the package only  */
            await scheduleWizard(
                this as unknown as ui.Page<SchedulerGraphApi>,
                {
                    jobSchedule: [recurring],
                    operationKey: 'SysSynchronizationTarget|synchronize|start',
                    additionalParameters: {
                        remoteApp: `#${remoteApp}`,
                        localNodeNames: [`#${localNodeName}`],
                        skipDependencies: false,
                    },
                    isOperationHidden: true,
                    isParametersHidden: true,
                },
                { noRedirect: true },
            );
        },
    })
    scheduleSync: ui.PageAction;

    //  =========== Page Functions ===============

    /**
     * Updates the local node tree with the mapping table.
     */
    private updateLocalNodeTree(): void {
        const mappings: Mapping[] = JSON.parse(JSON.stringify(this.mappingTable.value));
        const reorderProperties = (obj: Mapping): Mapping => {
            const { localProperty, ...rest } = obj;
            return { localProperty, ...rest };
        };
        const reorderedArray = mappings.map(reorderProperties);
        type Dict<T> = { [key: string]: T };
        const mappings2: Dict<Mapping> = Object.fromEntries(reorderedArray.map(item => [item.localProperty, item]));
        const properties2 = _.mapValues(mappings2, (_from, key) => ({ key }) as TreeElement<NodeDetails>);
        this.localPropertyTree.value = properties2;
    }

    private setLocalNodeFullName(): void {
        this.localNodeFullName = this.localNode.value
            ? `${this.localNode.value.package?.name}/${this.localNode.value?.name}`
            : '';
    }

    private populateLocalPropertyTree(fullNodePath: string): void {
        this.localPropertyTree.node = fullNodePath;
    }

    /**
     * Updates the remote node tree with the mapping table.
     */
    private updateRemoteNodeTree(): void {
        const mappings: Mapping[] = JSON.parse(JSON.stringify(this.mappingTable.value));
        const reorderProperties = (obj: Mapping): Mapping => {
            const { remoteProperty, ...rest } = obj;
            return { remoteProperty, ...rest };
        };
        const reorderedArray = mappings.map(reorderProperties);
        type Dict<T> = { [key: string]: T };
        const mappings2: Dict<Mapping> = Object.fromEntries(reorderedArray.map(item => [item.remoteProperty, item]));
        const properties2 = _.mapValues(mappings2, (_from, key) => ({ key }) as TreeElement<NodeDetails>);
        this.remotePropertyTree.value = properties2;
    }

    /**
     * Retrieves information about remote nodes from the server.
     * @param remoteApp The name of the remote application.
     * @returns A promise that resolves to the remote node information.
     */
    getRemoteNodes(remoteApp: string): Promise<RemoteNode[]> {
        try {
            return this.$.graph
                .node('@sage/xtrem-interop/SysRemoteMetadata')
                .queries.getNodeInfo(
                    {
                        name: true,
                        title: true,
                    },
                    {
                        app: `#${remoteApp}`,
                    },
                )
                .execute();
        } catch (error) {
            this.messageRemoteSystemNotAvailable(remoteApp, error);
        }
        return Promise.resolve([]);
    }

    /**
     * Gets the properties of the given remote app and node.
     * @param remoteApp
     * @param remoteNode
     * @returns Properties from a remote node
     */
    async getRemoteNodeProperties(
        remoteApp: string,
        remoteNode: string,
    ): Promise<{ properties: Partial<RemoteNodeProperty>[] }[]> {
        try {
            return await this.$.graph
                .node('@sage/xtrem-interop/SysRemoteMetadata')
                .queries.getNodeInfo(
                    {
                        properties: {
                            name: true,
                            title: true,
                            type: true,
                            dataType: true,
                            isStored: true,
                            targetNode: true,
                            isTransientInput: true,
                            isStoredOutput: true,
                            isRequired: true,
                            isNullable: true,
                            isCustom: true,
                            canSort: true,
                            canFilter: true,
                            isOnInputType: true,
                            isOnOutputType: true,
                            isMutable: true,
                            isVitalParent: true,
                        },
                    },
                    {
                        name: remoteNode,
                        app: `#${remoteApp}`,
                    },
                )
                .execute();
        } catch (error) {
            this.messageRemoteSystemNotAvailable(remoteApp, error);
        }
        return Promise.resolve([]);
    }

    /**
     * Populates the remote node table with the nodes of the remote application.
     * @param remoteApp
     */
    async populateRemoteNodeTable(remoteApp: string): Promise<void> {
        this.$.loader.isHidden = false;
        this.listOfRemoteNodes.value = [];
        try {
            const remoteNodes = await this.getRemoteNodes(remoteApp);
            remoteNodes.forEach((node: RemoteNode) => {
                this.listOfRemoteNodes.addRecord({
                    name: node.name,
                    title: node.title,
                });
            });
            this.listOfRemoteNodes.isDirty = false;
        } catch (error) {
            this.messageRemoteSystemNotAvailable(remoteApp, error);
        }
        this.$.loader.isHidden = true;
    }

    /**
     * Synchronizes the mapping table with the local property selection.
     */
    syncMappingTableWithLocalPropertySelection(): void {
        const selectedProperties = Object.values(this.localPropertyTree.value || {}).map(currentValue => ({
            name: currentValue.key,
        }));

        const mappedProperties = Object.values(this.mappingTable.value || {}).map(currentValue => ({
            localProperty: currentValue.localProperty,
            _id: currentValue._id,
        }));

        const mappedValues = new Set(mappedProperties.map(item => item.localProperty));
        const missingProperties = selectedProperties.filter(item => !mappedValues.has(item.name));
        missingProperties.forEach((item: { name: string }) => {
            this.mappingTable.addRecord({
                localProperty: item.name,
                kind: 'path_to_path',
                remoteProperty: '',
            });
        });

        const selectedValues = new Set(selectedProperties.map(item => item.name));
        const removeValues = mappedProperties.filter(item => !selectedValues.has(item.localProperty));
        removeValues.forEach((item: { _id: string }) => {
            this.mappingTable.removeRecord(item._id);
        });
    }

    /**
     * Synchronizes the mapping table with the remote property selection.
     */
    syncMappingTableWithRemotePropertySelection(preferredRowSysId = ''): void {
        try {
            const selectedProperties = Object.values(this.remotePropertyTree.value || {}).map(currentValue => ({
                name: currentValue.key,
            }));
            const mappedProperties = Object.values(this.mappingTable.value || {}).map(currentValue => ({
                remoteProperty: currentValue.remoteProperty,
                _id: currentValue._id,
            }));
            const mappedValues = new Set(mappedProperties.map(item => item.remoteProperty));
            const missingProperties = selectedProperties.filter(item => !mappedValues.has(item.name));

            // Empty remote properties on lines that are not checked in the remote property tree.
            this.mappingTable.value.forEach(line => {
                if (
                    line.remoteProperty.length &&
                    SysNodeTransformation.MappingTypeHasRemoteProperty(line.kind) &&
                    !selectedProperties.find(item => item.name === line.remoteProperty)
                ) {
                    line.remoteProperty = '';
                    this.mappingTable.setRecordValue(line);
                }
            });

            // If only one new property was additionally checked and a row id given use this line to fill in
            // the remote property.
            if (preferredRowSysId.length && missingProperties.length === 1) {
                const line = this.mappingTable.getRecordValue(preferredRowSysId);

                if (SysNodeTransformation.MappingTypeHasRemoteProperty(line.kind)) {
                    line.remoteProperty = missingProperties.shift().name;
                    this.mappingTable.setRecordValue(line);
                }
            }

            // Fill lines with empty remote properties.
            this.mappingTable.value.forEach(line => {
                if (!line.remoteProperty?.length && SysNodeTransformation.MappingTypeHasRemoteProperty(line.kind)) {
                    line.remoteProperty = missingProperties.shift().name;
                    this.mappingTable.setRecordValue(line);
                }
            });
            if (missingProperties.length) {
                throw new Error(
                    ui.localize(
                        '@sage/xtrem-interop/pages__sys_node_transformation__too_many_remote_properties',
                        'Too many remote properties selected to be assigned. Check the results to confirm.',
                    ),
                );
            }
        } catch (error) {
            this.$.showToast(error.message, { type: 'error', timeout: 10000 });
        }
    }

    /**
     *  Returns true if the mapping type has a remote property (ends with 'to_path').
     */
    static MappingTypeHasRemoteProperty(kind: string): boolean {
        return ['path_to_path', 'constant_to_path', 'function_to_path'].includes(kind);
    }

    /**
     *  Show a popup error message telling the user that data could not be retrieved from remote system.
     */
    messageRemoteSystemNotAvailable(remoteApp: string, error: Error): void {
        this.$.showToast(
            ui.localize(
                '@sage/xtrem-interop/pages__sys_node_transformation__remote_not_available',
                'Remote data was not retrieved. The remote system might not be available.\n\nOriginal message:\n\n{{originalMessage}}',
                {
                    originalMessage: `${remoteApp}: ${error.message}`,
                },
            ),
            { type: 'error', timeout: 10000 },
        );
    }

    /**
     *  Updates the availability of page elements based on the current state of the page.
     */
    updateAvailabilityOfPageElements(): void {
        // Enable/disabled all sync buttons based on node storage type
        const syncDisable =
            !this.isActive.value ||
            !this.localNode.value ||
            this.localNode.value.storage !== 'sql' ||
            !this.remoteNodeName.value;
        this.syncNow.isDisabled = syncDisable;
        this.scheduleSync.isDisabled = syncDisable;

        // Disable the remote entity selection button if the remote app is not set.
        this.selectRemoteEntity.isDisabled = !this.remoteApp.value;
    }
}
