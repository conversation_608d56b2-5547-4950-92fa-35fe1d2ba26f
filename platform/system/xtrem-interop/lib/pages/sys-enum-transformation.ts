import {
    Graph<PERSON><PERSON>,
    <PERSON>ys<PERSON><PERSON>,
    SysEnumMapping,
    SysEnumTransformation as SysEnumTransformationNode,
} from '@sage/xtrem-interop-api';
import { MetaDataType } from '@sage/xtrem-metadata-api';
import {
    setApplicativePageCrudActions,
    setOrderOfPageHeaderDropDownActions,
    setOrderOfPageHeaderQuickActions,
} from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import * as ui from '@sage/xtrem-ui';
import { AttributeValue, EnumAttributes, EnumObject, EnumValue, EnumValues, RemoteEnum } from '../interfaces/enum';
import { interop } from '../menu-items/interop';

let currentLocalEnumValues: string[] = [];
let currentRemoteEnumValues: string[] = [];

@ui.decorators.page<SysEnumTransformation, SysEnumTransformationNode>({
    title: 'Enum transforms',
    objectTypeSingular: 'Enum transformation',
    objectTypePlural: 'Enum transformations',
    menuItem: interop,
    node: '@sage/xtrem-interop/SysEnumTransformation',
    mode: 'tabs',
    module: 'xtrem-interop',
    idField() {
        return `- ${this.localEnum.value?.title}`;
    },
    createAction() {
        return this.$standardNewAction;
    },

    businessActions() {
        return [this.$standardCancelAction, this.$standardSaveAction];
    },

    headerQuickActions() {
        return setOrderOfPageHeaderQuickActions({
            duplicate: [this.$standardDuplicateAction],
        });
    },

    headerDropDownActions() {
        return setOrderOfPageHeaderDropDownActions({
            remove: [this.$standardDeleteAction],
        });
    },

    async onLoad() {
        setApplicativePageCrudActions({
            page: this,
            isDirty: false,
            save: this.$standardSaveAction,
            cancel: this.$standardCancelAction,
            duplicate: this.$standardDuplicateAction,
            remove: this.$standardDeleteAction,
        });

        if (this.localEnum.value && this.localEnum.value.attributes) {
            currentLocalEnumValues = await SysEnumTransformation.setLocalEnumSelectList(
                this.localEnum.value.attributes,
            );
        }

        if (this.remoteEnum.value && this.remoteApp.value?.name) {
            currentRemoteEnumValues = await SysEnumTransformation.setRemoteEnumSelectList(
                await this.getRemoteDataTypeAttributes2(this.remoteApp.value?.name, 'enum', this.remoteEnum.value),
            );
        }

        this.updateAvailabilityOfPageElements();
    },

    onDirtyStateUpdated(isDirty: boolean) {
        setApplicativePageCrudActions({
            page: this,
            isDirty,
            save: this.$standardSaveAction,
            cancel: this.$standardCancelAction,
            duplicate: this.$standardDuplicateAction,
            remove: this.$standardDeleteAction,
        });
    },

    navigationPanel: {
        optionsMenu: [
            { title: 'Active', graphQLFilter: { isActive: true } },
            { title: 'Inactive', graphQLFilter: { isActive: false } },
            { title: 'All', graphQLFilter: {} },
        ],
        listItem: {
            titleRight: ui.nestedFields.text({ bind: { id: true }, title: 'Mapping ID' }),
            title: ui.nestedFields.reference({ bind: { localEnum: true }, title: 'Local Enum', tunnelPage: undefined }),
            line2: ui.nestedFields.switch({ bind: { isActive: true }, title: 'Active' }),
            remoteEnum: ui.nestedFields.text({ bind: { remoteEnum: true }, title: 'Remote enum' }),
            remoteApp: ui.nestedFields.text({ bind: { remoteApp: { name: true } }, title: 'Remote app' }),
            remoteAppVersion: ui.nestedFields.text({ bind: { remoteAppVersion: true }, title: 'Remote app version' }),
        },
        inlineActions: [
            {
                title: 'Duplicate',
                icon: 'duplicate',
                onClick(rowId: string) {
                    this.$.showToast(`${rowId}`);
                    this.$standardDuplicateAction.execute(false, rowId);
                },
            },
        ],
    },
})
export class SysEnumTransformation extends ui.Page<GraphApi, SysEnumTransformationNode> {
    // ============ Main Section and Blocks ================
    @ui.decorators.section<SysEnumTransformation>({})
    mainSection: ui.containers.Section;

    @ui.decorators.block<SysEnumTransformation>({
        parent() {
            return this.mainSection;
        },
    })
    statusBlock: ui.containers.Block;

    @ui.decorators.block<SysEnumTransformation>({
        parent() {
            return this.mainSection;
        },
    })
    mainBlock: ui.containers.Block;

    @ui.decorators.block<SysEnumTransformation>({
        parent() {
            return this.mainSection;
        },
    })
    remoteBlock: ui.containers.Block;

    @ui.decorators.block<SysEnumTransformation>({
        title: 'Enum mapping',
        parent() {
            return this.mainSection;
        },
    })
    mappingBlock: ui.containers.Block;

    // ============ Dialog section / blocks  ===============
    @ui.decorators.section<SysEnumTransformation>({
        title: 'Select remote enum',
        isHidden: true,
    })
    remoteEnumDialogSection: ui.containers.Section;

    @ui.decorators.block<SysEnumTransformation>({
        parent() {
            return this.remoteEnumDialogSection;
        },
    })
    remoteEnumDialogBlock: ui.containers.Block;

    // ============= Screen Fields Status Block ==============
    @ui.decorators.switchField<SysEnumTransformation>({
        parent() {
            return this.statusBlock;
        },
        title: 'Active',
        width: 'small',
    })
    isActive: ui.fields.Switch;

    // ============= Screen Fields Main Block ==============
    @ui.decorators.textField<SysEnumTransformation>({
        parent() {
            return this.mainBlock;
        },
        title: 'Mapping ID',
        isMandatory: true,
    })
    id: ui.fields.Text;

    @ui.decorators.referenceField<SysEnumTransformation, MetaDataType>({
        parent() {
            return this.mainBlock;
        },
        title: 'local enum',
        isMandatory: true,
        width: 'large',
        node: '@sage/xtrem-metadata/MetaDataType',
        valueField: 'name',
        bind: 'localEnum',
        filter: { isActive: true },
        async onChange() {
            if (this.localEnum.value && this.localEnum.value.attributes) {
                currentLocalEnumValues = await SysEnumTransformation.setLocalEnumSelectList(
                    this.localEnum.value.attributes,
                );
            }
        },
        columns: [
            ui.nestedFields.text({
                bind: 'title',
                title: 'Title',
            }),
            ui.nestedFields.text({
                bind: 'name',
                title: 'name',
            }),
            ui.nestedFields.technical({
                bind: 'attributes',
            }),
        ],
    })
    localEnum: ui.fields.Reference<MetaDataType>;

    // ============= Screen Fields Remote Block ==============
    @ui.decorators.referenceField<SysEnumTransformation, SysApp>({
        parent() {
            return this.remoteBlock;
        },
        filter: { isActive: true },
        title: 'Remote app',
        isMandatory: true,
        width: 'large',
        node: '@sage/xtrem-interop/SysApp',
        valueField: 'name',
        bind: 'remoteApp',
        onChange() {
            this.updateAvailabilityOfPageElements();
        },
    })
    remoteApp: ui.fields.Reference<SysApp>;

    @ui.decorators.textField<SysEnumTransformation>({
        parent() {
            return this.remoteBlock;
        },
        title: 'Remote app version',
        // bind: 'remoteAppVersion',
        isMandatory: true,
    })
    remoteAppVersion: ui.fields.Text;

    @ui.decorators.textField<SysEnumTransformation>({
        parent() {
            return this.remoteBlock;
        },
        title: 'Remote enum',
        isMandatory: true,
        isReadOnly: true,
    })
    remoteEnum: ui.fields.Text;

    @ui.decorators.buttonField<SysEnumTransformation>({
        isTransient: true,
        width: 'small',
        parent() {
            return this.remoteBlock;
        },
        map() {
            return 'Select remote enum';
        },
        async onClick() {
            if (this.remoteApp.value?.name) {
                const remoteEnums = await this.getRemoteDataType(this.remoteApp.value?.name, 'enum');
                remoteEnums.forEach((item: RemoteEnum) => {
                    this.remoteEnumTable.addRecord({
                        name: item.name,
                        title: item.title,
                    });
                });
            }
            this.remoteEnumDialogSection.isHidden = false;

            await this.$.dialog.custom('info', this.remoteEnumDialogSection, {
                size: 'large',
                resolveOnCancel: true,
                cancelButton: { isHidden: false },
                acceptButton: { isHidden: false },
            });

            this.remoteEnumDialogSection.isHidden = true;
        },
    })
    displayRemoteNodesDialog: ui.fields.Button;

    // ============= Screen Fields Dialog Block ==============
    @ui.decorators.tableField<SysEnumTransformation>({
        parent() {
            return this.remoteEnumDialogBlock;
        },
        isTransient: true,
        canExport: false,
        canSelect: false,
        canFilter: true,
        isChangeIndicatorDisabled: true,
        canUserHideColumns: false,
        canAddNewLine: true,
        isPhantomRowDisabled: true,
        bind: 'name',
        async onRowClick(_recordId, rowItem) {
            this.remoteEnum.value = rowItem.name;
            if (this.remoteEnum.value && this.remoteApp.value?.name) {
                currentRemoteEnumValues = await SysEnumTransformation.setRemoteEnumSelectList(
                    await this.getRemoteDataTypeAttributes2(this.remoteApp.value?.name, 'enum', this.remoteEnum.value),
                );
            }
        },
        columns: [
            ui.nestedFields.text({
                bind: 'title',
                title: 'Title',
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                bind: 'name',
                title: 'name',
                isReadOnly: true,
            }),
        ],
    })
    remoteEnumTable: ui.fields.Table;

    // ============= Screen Fields Mapping  Block ==============
    @ui.decorators.tableField<SysEnumTransformation>({
        parent() {
            return this.mappingBlock;
        },
        node: '@sage/xtrem-interop/SysEnumMapping',
        canAddNewLine: true,
        canExport: true,
        canSelect: false,
        canResizeColumns: true,
        isTitleHidden: true,
        isFullWidth: true,
        bind: 'map',

        columns: [
            ui.nestedFields.select({
                title: 'Local value',
                bind: 'localEnumValue',
                options() {
                    return currentLocalEnumValues;
                },
            }),

            ui.nestedFields.select({
                bind: 'remoteEnumValue',
                title: 'Remote value',
                options() {
                    return currentRemoteEnumValues;
                },
            }),
        ],
        dropdownActions: [
            {
                title: 'Refresh record',
                icon: 'refresh',
                async onClick(rowId) {
                    await this.mappingTable.refreshRecord(rowId);
                },
            },
            ui.menuSeparator(),
            {
                icon: 'minus',
                title: 'Remove',
                isDestructive: true,
                onClick(rowId) {
                    this.mappingTable.removeRecord(rowId);
                },
            },
        ],
    })
    mappingTable: ui.fields.Table<SysEnumMapping>;

    /**
     * Get the local data type values
     * @param data
     * @returns
     */
    static async getLocalAttributeValues(data: { values: EnumValue[] }): Promise<EnumValue[]> {
        const enumValues: EnumValue[] = data.values.map(value => {
            return {
                value: value.value,
            };
        });
        return enumValues;
    }

    /**
     * Returns remote records for a data type
     * @param remoteApp
     * @param dataType
     * @returns list of remote enums
     */
    async getRemoteDataType(remoteApp: string, dataType: string): Promise<RemoteEnum[]> {
        try {
            const results = await this.$.graph
                .node('@sage/xtrem-interop/SysRemoteMetadata')
                .queries.getDataTypeInfo(
                    {
                        name: true,
                        title: true,
                    },
                    {
                        app: `#${remoteApp}`,
                        type: dataType,
                    },
                )
                .execute();

            if (!results.length) {
                this.messageRemoteDatatypeNotFound();
                return [];
            }
            return results;
        } catch (error) {
            this.messageRemoteSystemNotAvailable(remoteApp, error);
        }
        return [];
    }

    /**
     * Set the local enum select list
     * @param attributeData
     * @returns list of values
     */
    static async setLocalEnumSelectList(attributeData: string): Promise<string[]> {
        const attributesValues: AttributeValue[] = await SysEnumTransformation.getLocalAttributeValues(
            JSON.parse(attributeData),
        );

        const list: string[] = attributesValues.map(({ value }) => value);
        return list;
    }

    /**
     *  Set the remote enum select list
     * @param attributesData
     * @returns list of values
     */
    static async setRemoteEnumSelectList(enumData: EnumObject[]): Promise<string[]> {
        let valueArray: string[] = [];
        if (enumData.length > 0) {
            const parsedAttributes: EnumAttributes = JSON.parse(enumData[0].attributes);
            const valuesArray: EnumValues[] = parsedAttributes.values;
            valueArray = valuesArray.map(item => item.value);
        }
        return valueArray;
    }

    /**
     * @param remoteApp
     * @param dataType
     * @param dataTypeName
     * @returns attributes of the remote data type
     */
    async getRemoteDataTypeAttributes2(
        remoteApp: string,
        dataType: string,
        dataTypeName: string,
    ): Promise<EnumObject[]> {
        try {
            return await this.$.graph
                .node('@sage/xtrem-interop/SysRemoteMetadata')
                .queries.getDataTypeInfo(
                    {
                        attributes: true,
                    },
                    {
                        app: `#${remoteApp}`,
                        type: dataType,
                        name: dataTypeName,
                    },
                )
                .execute();
        } catch (error) {
            this.messageRemoteSystemNotAvailable(remoteApp, error);
        }
        return [];
    }

    /**
     *  Show a popup error message telling the user that data could not be retrieved from remote system.
     */
    messageRemoteSystemNotAvailable(remoteApp: string, error: Error): void {
        this.$.showToast(
            ui.localize(
                '@sage/xtrem-interop/pages__sys_node_transformation__remote_not_available',
                'Remote data was not retrieved. The remote system might not be available.\n\nOriginal message:\n\n{{originalMessage}}',
                {
                    originalMessage: `${remoteApp}: ${error.message}`,
                },
            ),
            { type: 'error', timeout: 10000 },
        );
    }

    /**
     *  Show a popup error message telling the user that data types were not found in the remote system.
     */
    messageRemoteDatatypeNotFound(): void {
        this.$.showToast(
            ui.localize(
                '@sage/xtrem-interop/pages__sys_enum_transformation__remote_data_type_not_found',
                'No remote data type found.',
            ),
            { type: 'error', timeout: 10000 },
        );
    }

    /**
     *  Updates the availability of page elements based on the current state of the page.
     */
    updateAvailabilityOfPageElements(): void {
        // Disable the remote enum selection button if the remote app is not set.
        this.displayRemoteNodesDialog.isDisabled = !this.remoteApp.value;
    }
}
