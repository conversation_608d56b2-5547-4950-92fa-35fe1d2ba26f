import { Application, Context, CoreHooks, InteropAppInfo, Logger } from '@sage/xtrem-core';
import { InfrastructureHelper } from '@sage/xtrem-infrastructure-adapter';
import { InteropInterceptor } from './classes/interop-interceptor';
import { SysAppRefresh } from './classes/sys-app-refresh';
import { SysApp } from './nodes/sys-app';

export * as activities from './activities';
export * as dataTypes from './data-types';
export * as enums from './enums';
export * as functions from './functions';
export * as interfaces from './interfaces';
export * as menuItems from './menu-items';
export * as nodes from './nodes';
export * as serviceOptions from './service-options';

Object.assign(CoreHooks.interopManager, {
    async getInteropAppInfo(context: Context, app: string): Promise<InteropAppInfo> {
        const node = await context.read(SysApp, { name: app });
        return node.toInteropAppInfo();
    },

    getTenantApps(tenantId: string): Promise<string[] | null> {
        return InfrastructureHelper.getTenantApps(tenantId);
    },

    /**
     * Fix the activation of SysNodeTransformation, SysEnumTransformation and SysOperationTransformation at the end of upgrades
     * @param _context
     */
    async fixInteropTransformationsActivation(context: Context, options?: { logger?: Logger }): Promise<void> {
        const logger = options?.logger || context.logger;

        logger.info(`Updating node transformations activation...`);
        // This SQL script is based on https://github.com/Sage-ERP-X3/xtrem/pull/25257
        let result = await context.executeSql(
            `UPDATE  ${context.schemaName}.sys_node_transformation nt
            SET is_active = sa.is_active
            FROM ${context.schemaName}.sys_app sa
                WHERE nt.remote_app = sa._id
                AND nt._tenant_id = sa._tenant_id
                AND nt.is_active <> sa.is_active
                AND NOT EXISTS(SELECT 1
                    FROM ${context.schemaName}.sys_node_transformation nt2
                    LEFT JOIN  ${context.schemaName}.sys_vendor v ON v._id = nt2._vendor
                        WHERE nt2.local_node = nt.local_node
                        AND nt._tenant_id = nt2._tenant_id
                        AND (v.name IS NULL OR v.name <> 'sage')
                        AND nt2.is_active = true)`,
            [],

            {
                allowUnsafe: true,
            },
        );

        logger.info(`Node transformations activation complete ${JSON.stringify(result)}`);

        logger.info(`Updating enum transformations activation...`);

        result = await context.executeSql(
            `UPDATE  ${context.schemaName}.sys_enum_transformation et
            SET is_active = sa.is_active
            FROM ${context.schemaName}.sys_app sa
                WHERE et.remote_app = sa._id
                AND et._tenant_id = sa._tenant_id
                AND et.is_active <> sa.is_active
                AND NOT EXISTS(SELECT 1
                    FROM ${context.schemaName}.sys_enum_transformation et2
                    LEFT JOIN  ${context.schemaName}.sys_vendor v ON v._id = et2._vendor
                        WHERE et2.local_enum = et.local_enum
                        AND et._tenant_id = et2._tenant_id
                        AND (v.name IS NULL OR v.name <> 'sage')
                        AND et2.is_active = true)`,
            [],
            {
                allowUnsafe: true,
            },
        );

        logger.info(`Enum transformations activation complete ${JSON.stringify(result)}`);

        logger.info(`Updating node operation transformations activation...`);

        result = await context.executeSql(
            `UPDATE  ${context.schemaName}.sys_operation_transformation ot
            SET is_active = sa.is_active
            FROM ${context.schemaName}.sys_app sa
                WHERE ot.remote_app = sa._id
                AND ot._tenant_id = sa._tenant_id
                AND ot.is_active <> sa.is_active
                AND NOT EXISTS(SELECT 1
                    FROM ${context.schemaName}.sys_operation_transformation ot2
                    LEFT JOIN  ${context.schemaName}.sys_vendor v ON v._id = ot2._vendor
                        WHERE ot2.local_node = ot.local_node
                        and ot2.local_operation_name = ot.local_operation_name
                        AND ot._tenant_id = ot2._tenant_id
                        AND (v.name IS NULL OR v.name <> 'sage')
                        AND ot2.is_active = true)`,
            [],
            {
                allowUnsafe: true,
            },
        );

        logger.info(`Node operation transformations activation complete ${JSON.stringify(result)}`);
    },
} as Partial<typeof CoreHooks.interopManager>);

export function startService(application: Application): void {
    SysAppRefresh.init(); // Call the init() function
    InteropInterceptor.interceptInteropOperations(application);
}
