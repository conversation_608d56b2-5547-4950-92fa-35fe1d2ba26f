import { Context, NodeFactory } from '@sage/xtrem-core';
import { InteropTransform } from '../classes/interop-transform';

/**
 * This method checks if there is a mapping for the given local property in the active transform.
 * @param context
 * @param localFactory The local factory of the node.
 * @param localProperty Property to check for mapping.
 */
export async function hasActiveTransformMapping(
    context: Context,
    localFactory: NodeFactory,
    localProperty: string,
): Promise<boolean> {
    const transform = await InteropTransform.findNodeTransform(context, localFactory);
    const mapping = transform?.mappings[localProperty];
    return !!mapping;
}
