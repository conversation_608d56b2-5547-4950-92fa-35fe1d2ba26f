import { Context, StaticThis } from '@sage/xtrem-core';
import * as xtremInterop from '../index';

export interface UpdateActiveMappingsFunctionParameters {
    nodeToUpdate: StaticThis<xtremInterop.nodes.SysEnumTransformation | xtremInterop.nodes.SysNodeTransformation>;
    remoteAppId: string;
    active: boolean;
    version?: string;
}
/**
 * This method updates the isActive flag on the SysEnumTransformation node for the given remote app'
 * @param context
 * @param parameters includes nodeToUpdate, remoteAppId, active, and version
 */
export async function updateActiveMappings(
    context: Context,
    parameters: UpdateActiveMappingsFunctionParameters,
): Promise<boolean> {
    const where: { remoteApp: string; remoteAppVersion?: string } = { remoteApp: `#${parameters.remoteAppId}` };
    if (parameters.version) {
        where.remoteAppVersion = parameters.version;
    }
    await context.bulkUpdate(parameters.nodeToUpdate, {
        set: { isActive: parameters.active },
        where,
    });

    return true;
}
