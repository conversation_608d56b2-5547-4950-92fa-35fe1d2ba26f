/* eslint-disable class-methods-use-this */
/* eslint-disable @typescript-eslint/no-unused-vars */
import {
    AnyR<PERSON><PERSON>,
    AnyRecordWithId,
    AnyValue,
    AsyncArrayReader,
    AsyncGenericReader,
    AsyncReader,
    AsyncResponse,
    ConfigManager,
    Context,
    DateValue,
    Dict,
    Extend,
    ExternalStorageManager,
    ForeignNodeProperty,
    InternalPropertyJoin,
    JoinLiteralValue,
    Node,
    NodeExternalQueryOptions,
    NodeFactory,
    NodeQueryOptions,
    OrderBy,
    OrderByClause,
    Property,
    PropertyAndValue,
    RecordPaging,
    ValidationContext,
    asyncArray,
    isScalar,
    parseCursor,
} from '@sage/xtrem-core';
import * as _ from 'lodash';
import { logger } from './interop-base-node-client';
import { Mapping, Payload } from './interop-mapper';
import { InteropNodeMutationClient } from './interop-node-mutation-client';
import { InteropNodeQueryClient, QueryReturnType, RemoteQueryOptions } from './interop-node-query-client';
import { InteropTransform, NodeTransformData } from './interop-transform';

export class InteropStorageManager<T extends Node> implements ExternalStorageManager<T> {
    factory: NodeFactory;

    private get naturalKey(): string[] {
        if (!this.factory.naturalKey) {
            throw new Error(`${this.factory.name}: natural key is not defined`);
        }

        return this.factory.naturalKey;
    }

    async insert(node: Extend<T>, cx: ValidationContext): Promise<AnyRecordWithId> {
        // if cx.path has entries then we are getting called from the save action of a reference or collection and
        // should ignore as the save of the collection is managed from the save on the main node in a single graphql request...
        // TODO: verify if this will always be the case, will the local vital relationship map to a remote vital relationship, always?
        if (cx.path.length > 0) return Promise.resolve({ _id: String(node._id) });

        const context = node.$.context;
        const transform = await InteropTransform.findNodeTransform(context, this.factory);
        const mutationClient = new InteropNodeMutationClient({
            ...transform,
        });

        const result = await mutationClient.create(node, cx);

        result._id = this.getId(context, result);

        return result as AnyRecordWithId;
    }

    async update(node: Extend<T>, cx: ValidationContext): Promise<AnyRecord[]> {
        // if cx.path has entries then we are getting called from the save action of a reference or collection and
        // should ignore as the save of the collection is managed from the save on the main node in a single graphql request...
        // TODO: verify if this will always be the case, will the local vital relationship map to a remote vital relationship, always?
        if (cx.path.length > 0) return Promise.resolve([]);
        const context = node.$.context;
        const transform = await InteropTransform.findNodeTransform(context, this.factory);
        const mutationClient = new InteropNodeMutationClient({
            ...transform,
        });

        const result = await mutationClient.update(node, cx);

        return [result];
    }

    async delete(node: Extend<T>, cx: ValidationContext): Promise<number> {
        // if cx.path has entries then we are getting called from the save action of a reference or collection and
        // should ignore as the save of the collection is managed from the save on the main node in a single graphql request...
        // TODO: verify if this will always be the case, will the local vital relationship map to a remote vital relationship, always?
        if (cx.path.length > 0) return Promise.resolve(0);

        const context = node.$.context;
        const transform = await InteropTransform.findNodeTransform(context, this.factory);
        const mutationClient = new InteropNodeMutationClient({
            ...transform,
        });

        return mutationClient.delete(node, cx);
    }

    /**
     * Walk object and remove proeprties that are not mapped to remote paths
     * @param context
     * @param transform
     * @param obj
     * @param options
     * @returns
     */
    static extractComputedPropertiesFromObject(
        context: Context,
        transform: NodeTransformData,
        obj: any,
        options?: {
            path?: string[];
            prevousMapping?: Mapping;
            previousProperty?: Property;
            fromCollection?: boolean;
        },
    ): Promise<any> {
        const checkFromCollection = (currentPath: string[]): void => {
            if (options?.fromCollection) {
                throw new Error(
                    `${currentPath.join('.')}: A nested collection filter cannot use properties that are not mapped to remote paths`,
                );
            }
        };
        return asyncArray(Object.keys(obj)).reduce(async (r, key) => {
            const currentPath = options?.path ? [...options.path, key] : [key];
            const property = transform.localFactory.propertiesByName[key];
            const value = obj[key];

            if (property) {
                const mapping = InteropTransform.getPropertyMapping(transform, property);
                if (!mapping) return r;
                if (mapping.kind !== 'path') {
                    r[key] = value;
                    return r;
                }
                if (isScalar(value)) {
                    return r;
                }

                const nextTransform = property.isForeignNodeProperty()
                    ? await InteropTransform.findNodeTransform(context, property.targetFactory, {
                          remoteAppName: transform.remoteAppName,
                          remoteAppVersion: transform.remoteAppVersion,
                      })
                    : transform;

                let nextValue = { ...value };
                let quantifiers = [] as string[];
                if (property.isCollectionProperty()) {
                    quantifiers = Object.keys(nextValue).filter(k => /^_(atMost|atLeast|every|none)$/.test(k));
                    nextValue = _.omit(nextValue, quantifiers);
                }

                const result = await this.extractComputedPropertiesFromObject(context, nextTransform, value, {
                    path: currentPath,
                    prevousMapping: mapping,
                    fromCollection: property.isCollectionProperty(),
                    previousProperty: property,
                });

                if (Object.keys(result).length > 0) {
                    checkFromCollection(currentPath);
                    if (property.isCollectionProperty()) {
                        // Add the quantifiers back to the result for collection property
                        quantifiers.forEach(q => {
                            result[q] = value[q];
                        });
                    }
                    r[key] = result;
                }

                return r;
            }

            if (key === '_or' || key === '_and') {
                if (Array.isArray(value)) {
                    const result = await asyncArray(value)
                        .map((v: any) =>
                            this.extractComputedPropertiesFromObject(context, transform, v, {
                                path: currentPath,
                                prevousMapping: options?.prevousMapping,
                                fromCollection: options?.fromCollection,
                            }),
                        )
                        .filter((v: any) => Object.keys(v).length > 0)
                        .toArray();

                    if (result.length > 0) {
                        checkFromCollection(currentPath);
                        r[key] = result;
                    }
                }
                return r;
            }

            // We have an operator and we do not need operators on path mapped properties
            if (options?.prevousMapping && options.prevousMapping.kind === 'path') {
                return r;
            }

            if (isScalar(value)) {
                r[key] = value;
                return r;
            }

            const result = await this.extractComputedPropertiesFromObject(context, transform, value, {
                path: currentPath,
                prevousMapping: options?.prevousMapping,
                fromCollection: options?.fromCollection,
                previousProperty: options?.previousProperty,
            });
            if (Object.keys(result).length > 0) {
                checkFromCollection(currentPath);
                r[key] = result;
            }
            return r;
        }, {} as any);
    }

    static async extractComputedPagingOptions(
        context: Context,
        transform: NodeTransformData,
        options: NodeQueryOptions,
    ): Promise<NodeQueryOptions> {
        const { filter, orderBy } = options;

        const computedFilter = filter
            ? await this.extractComputedPropertiesFromObject(context, transform, filter)
            : undefined;
        const computeOrderBy = orderBy
            ? await this.extractComputedPropertiesFromObject(context, transform, orderBy)
            : undefined;

        const result = {} as NodeQueryOptions;

        if (computedFilter && !_.isEmpty(computedFilter)) result.filter = computedFilter;
        if (computeOrderBy && !_.isEmpty(computeOrderBy)) result.orderBy = computeOrderBy;

        return result;
    }

    private setIds(context: Context, records: Payload[], options: NodeExternalQueryOptions<T>): AnyRecord[] {
        return records.map(r => {
            if (!options?.aggregate && r) {
                r._id = this.getId(context, r);
            }
            return r;
        });
    }

    private async pageRawRecords(
        context: Context,
        rawRecords: Payload[],
        options: NodeExternalQueryOptions<T>,
    ): Promise<Payload[]> {
        logger.verbose(() => `query result before paging: ${JSON.stringify(rawRecords)}`);

        // We apply paging options here as there may be some properties that are computed when the
        // mapping is applied, and we need to apply the paging options for these relevant properties
        const transform = await InteropTransform.findNodeTransform(context, this.factory);
        const pagingOptions = await InteropStorageManager.extractComputedPagingOptions(context, transform, options);
        logger.verbose(() => `query pagingOptions before paging: ${JSON.stringify(pagingOptions)}`);

        if (Object.keys(pagingOptions).length > 0) {
            const profiler = logger.verbose(
                () => `computed properties paging options: ${JSON.stringify(pagingOptions)}`,
            );
            const pagedRecords = (
                await RecordPaging.applyPagingOptions(
                    this.factory,
                    context,
                    rawRecords,
                    async (propContext: Context, property: Property, value: AnyRecord) => {
                        const propValue = value[property.name];

                        if (property.isReferenceProperty()) {
                            if (typeof propValue === 'string' || typeof propValue === 'number') {
                                const node = await propContext.read(property.targetFactory.nodeConstructor, {
                                    _id: propValue,
                                });
                                return node.$.getRawPropertyValues();
                            }

                            if (propValue instanceof Node) {
                                return propValue.$.getRawPropertyValues();
                            }
                        }

                        if (property.isDateProperty() && typeof propValue === 'string') {
                            return DateValue.parse(propValue);
                        }

                        return propValue;
                    },
                    pagingOptions,
                )
            ).items;

            profiler.success(`query result after paging: ${JSON.stringify(pagedRecords)}`);

            return pagedRecords;
        }

        return rawRecords;
    }

    private async getRawReader(
        context: Context,
        options: NodeExternalQueryOptions<T> & {
            remotePagingOptions?: {
                after: string;
            };
        },
    ): Promise<{ payload: Payload[]; endCursor: string | null; hasNextPage: boolean }> {
        const transform = await InteropTransform.findNodeTransform(context, this.factory);
        const queryClient = new InteropNodeQueryClient({
            ...transform,
        });

        const config = ConfigManager.current;

        const maxPageSize = config.interop?.remoteQuery?.maxNodesPerPage ?? 1000;

        let endCursor: string | null = null;

        if (options.first == null && options.last == null && !options.count) {
            // A first/last paging option is not provided, we are probably in a context.query/context.queryAggregate
            // call, we need to fetch all records
            // We are probably loading a collection, so we need to fetch all records
            const newOptions: RemoteQueryOptions<T> = {
                ...options,
                first: maxPageSize,
            };

            if (options.remotePagingOptions?.after) {
                newOptions.remotePagingOptions = {
                    ...(newOptions.remotePagingOptions ?? {}),
                    after: options.remotePagingOptions.after,
                };
            }

            const rawQueryResult = await queryClient.queryRemote(context, transform, newOptions);

            const rawQueryRecords = [...rawQueryResult.payload];

            let hasNextPage = rawQueryResult.pageInfo.hasNextPage;

            const maxNodesPerQuery = config.interop?.remoteQuery?.maxNodesPerQuery ?? 100_000;
            if (rawQueryRecords.length >= maxNodesPerQuery) {
                throw new Error(
                    `Too many records returned from remote query (>${maxNodesPerQuery}), please use a better filter to limit the number of records returned`,
                );
            }
            // Initial remote query's pageInfo says we have another page we need to repeat until this is not the case
            while (hasNextPage) {
                // add endCursor as the after argument to the next query
                const nextOptions: RemoteQueryOptions<T> = {
                    ...newOptions,
                    remotePagingOptions: {
                        after: rawQueryResult.pageInfo.endCursor,
                    },
                };

                const nextRawQueryResult: QueryReturnType = await queryClient.queryRemote(
                    context,
                    transform,
                    nextOptions,
                );

                if (rawQueryRecords.length >= maxNodesPerQuery) {
                    throw new Error(
                        `Too many records returned from remote query (>${maxNodesPerQuery}), please use a better filter to limit the number of records returned`,
                    );
                }

                rawQueryRecords.push(...nextRawQueryResult.payload);

                hasNextPage = nextRawQueryResult.pageInfo.hasNextPage;
                endCursor = nextRawQueryResult.pageInfo.endCursor ?? null;
            }

            return { payload: rawQueryRecords, endCursor, hasNextPage: false };
        }

        const queryResult = await queryClient.queryRemote<T>(context, transform, options);
        return {
            payload: queryResult.payload,
            endCursor: queryResult.pageInfo.endCursor ?? null,
            hasNextPage: queryResult.pageInfo.hasNextPage,
        };
    }

    query(context: Context, options: NodeExternalQueryOptions<T>): AsyncReader<any> {
        let rawRecords: Payload[] | null = null;

        let count = 0;

        let expectedCount = options.first ?? options.last ?? 0;

        const config = ConfigManager.current;

        const maxPageSize = config.interop?.remoteQuery?.maxNodesPerPage ?? 1000;

        let nextPageSize = Math.min(maxPageSize, (options.first ?? options.last ?? 1) * 2);

        let initialCount = 0;

        let endCursor: string | null = null;

        const read = async (): Promise<AnyRecord | undefined> => {
            const fetchMoreRecords = async (): Promise<Payload[]> => {
                logger.verbose(() => `Loading more records after ${endCursor}`);

                if (!endCursor) return [];

                const optionsCopy: NodeExternalQueryOptions<T> & {
                    remotePagingOptions?: {
                        after: string;
                    };
                } = { ...options };

                optionsCopy.remotePagingOptions = {
                    after: endCursor,
                };

                if (optionsCopy.first && optionsCopy.first > 0) optionsCopy.first = nextPageSize;
                if (optionsCopy.last && optionsCopy.last > 0) optionsCopy.last = nextPageSize;
                // Try to fetch more records
                let nextRawReaderResult = await this.getRawReader(context, optionsCopy);

                if (nextRawReaderResult.payload.length === 0) {
                    return [];
                }

                while (nextRawReaderResult.hasNextPage) {
                    let nextRawRecords = nextRawReaderResult.payload;

                    nextRawRecords = this.setIds(context, nextRawRecords, options);

                    // Set the endCursor to use as the after for the next fetch
                    endCursor = nextRawReaderResult.endCursor;
                    // eslint-disable-next-line @typescript-eslint/no-loop-func
                    logger.verbose(() => `Loading more records after ${endCursor}`);

                    const pagedNextRawRecords = await this.pageRawRecords(context, nextRawRecords, options);

                    // We have records after paging
                    if (pagedNextRawRecords.length > 0) {
                        return pagedNextRawRecords;
                    }

                    nextPageSize = Math.min(maxPageSize, (optionsCopy.first ?? optionsCopy.last ?? 1) * 2);

                    // there are no more records return quickly
                    if (!endCursor) return [];

                    optionsCopy.remotePagingOptions = {
                        after: endCursor,
                    };

                    if (optionsCopy.first && optionsCopy.first > 0) optionsCopy.first = nextPageSize;
                    if (optionsCopy.last && optionsCopy.last > 0) optionsCopy.last = nextPageSize;

                    // After paging we have no records, we need to fetch more records
                    // until we get some results or hasNextPage is false
                    nextRawReaderResult = await this.getRawReader(context, optionsCopy);
                }

                nextPageSize = Math.min(maxPageSize, (optionsCopy.first ?? optionsCopy.last ?? 1) * 2);
                return this.pageRawRecords(context, nextRawReaderResult.payload, options);
            };

            try {
                if (rawRecords == null) {
                    // Read first page
                    const rawReaderResult = await this.getRawReader(context, { ...options });
                    rawRecords = rawReaderResult.payload;
                    endCursor = rawReaderResult.endCursor;

                    initialCount = rawRecords.length;
                    rawRecords = this.setIds(context, rawRecords, options);
                    if (!options.aggregate) {
                        rawRecords = await this.pageRawRecords(context, rawRecords, options);
                    }

                    if (options.singleResultRequest || expectedCount === 0 || options.count) {
                        expectedCount = rawRecords.length;
                    } else if (rawRecords.length === 0 && !options.aggregate) {
                        rawRecords = await fetchMoreRecords();
                    }
                }

                if (count === 0) {
                    logger.verbose(() => 'query result ready');
                }

                if (count === expectedCount) return undefined;

                let val = rawRecords[count];

                if (val !== undefined) {
                    count += 1;
                } else if (
                    !options.aggregate &&
                    !options.singleResultRequest &&
                    initialCount > 0 &&
                    expectedCount > count
                ) {
                    // We are not at the end of the records and we have not reached the expected count
                    // We need to fetch more records
                    // Example: the query was for the first 10 records, but we only got 5
                    // We could be at the end of the records, but we need to check if there are more records,
                    // as we have applied manual paging on the result from the remote
                    // i.e. the remote query could have returned 10 records but the additional manual paging may have reduced it to 7
                    // we need query the records after the endCursor to see if we can fetch 3 more records.
                    const transform = await InteropTransform.findNodeTransform(context, this.factory);
                    const pagingOptions = await InteropStorageManager.extractComputedPagingOptions(
                        context,
                        transform,
                        options,
                    );

                    if (!pagingOptions.filter) {
                        return undefined;
                    }

                    if (!endCursor) {
                        return undefined;
                    }

                    // Try to fetch more records
                    const nextRecords = await fetchMoreRecords();
                    if (nextRecords.length === 0) {
                        return undefined;
                    }
                    // fill rawRecords with next page and continue reading
                    rawRecords.push(...nextRecords);
                    val = rawRecords[count];
                }

                return val;
            } catch (err) {
                logger.error(() => `Error '${err.message}`);
                throw err;
            }
        };

        const stop = (): void => {
            logger.verbose(() => `${count} records read`);
        };

        if (options.count) {
            return new AsyncArrayReader(async () => {
                const transform = await InteropTransform.findNodeTransform(context, this.factory);
                const pagingOptions = await InteropStorageManager.extractComputedPagingOptions(
                    context,
                    transform,
                    options,
                );
                if (pagingOptions.filter) {
                    // We have computed properties in the filter, therefore we cannot rely of the remote total count as
                    // manual paging will be applied
                    // We need to fetch all records and count them after paging is applied
                    const fullReader = new AsyncGenericReader<any>({ read, stop });
                    return [{ totalCount: (await fullReader.readAll()).length }];
                }

                const countResult = (await this.getRawReader(context, options)).payload;
                if (countResult.length === 0) {
                    return [{ totalCount: 0 }];
                }
                return countResult;
            }).map(record => {
                if (record.totalCount) {
                    return Number(record.totalCount);
                }
                return 0;
            }) as unknown as AsyncReader<T>;
        }

        return new AsyncGenericReader<any>({ read, stop });
    }

    mapRecordIn(record: any): any {
        return record;
    }

    mapAggregateRecordIn(record: any): any {
        return record;
    }

    private getReverseReferenceFilter(property: Property & { reverseReference: string | undefined }): Dict<string> {
        if (!property.reverseReference) throw new Error(`${property.name}: reverse reference is not defined`);
        const reverseReference = property.reverseReference;

        return { [reverseReference]: '_id' };
    }

    getReferenceJoin(propertyName: string): InternalPropertyJoin<T> {
        const property = this.factory.findProperty(propertyName);
        if (!property.isReferenceProperty()) throw new Error(`${propertyName}: is not a reference property`);
        if (property.isVital || property.isAssociation) {
            return this.getReverseReferenceFilter(property);
        }
        // If the decorator has an explicit join attribute, return it
        if (property.decorator.join) return property.decorator.join as InternalPropertyJoin<T>;
        // Join must between this column and the targetFactory's _id

        return { _id: property.name };
    }

    getCollectionJoin(propertyName: string): InternalPropertyJoin<T> {
        const property = this.factory.findProperty(propertyName);
        if (!property.isCollectionProperty()) throw new Error(`${propertyName}: is not a collection property`);
        if (property.isVital || property.isAssociation) {
            return this.getReverseReferenceFilter(property);
        }
        return {};
    }

    private static buildJoinEntry(
        node: Node,
        data: any,
        rootProperty: ForeignNodeProperty,
        property: ForeignNodeProperty,
        member: any,
        path: string[],
    ): AsyncResponse<AnyValue> {
        if (typeof member === 'string') {
            const value = data[member] ?? node.$.getRawPropertyValue(member);
            if (typeof value === 'object' && value !== null) {
                return property.targetFactory.getNaturalKeyStringValue(node.$.context, value as AnyRecord);
            }

            if (value === undefined && member === '_id') {
                // the _id is not set on the state as yet, use the node id, this will call getId and set the _id for future
                // joins
                return node._id;
            }

            return value;
        }

        if (typeof member === 'function') {
            return member.call(node);
        }

        if (member instanceof JoinLiteralValue) {
            return member.value;
        }

        if (typeof member === 'object') {
            if (member === null) return null;
            if (Object.keys(member).every(k => !!property.targetFactory.propertiesByName[k]))
                return asyncArray(Object.keys(member)).reduce(async (r, k) => {
                    const val = member[k];
                    if (
                        typeof val === 'object' &&
                        !(member instanceof JoinLiteralValue || typeof member === 'function' || member === null)
                    ) {
                        const targetProperty = property.targetFactory.findProperty(k);
                        if (!targetProperty.isForeignNodeProperty())
                            throw targetProperty.logicError(`Invalid join element ${k}`);
                        r[k] = await this.buildJoinEntry(node, data[property.name], rootProperty, targetProperty, val, [
                            ...path,
                            k,
                        ]);
                    } else {
                        r[k] = await this.buildJoinEntry(node, data[property.name], rootProperty, property, val, [
                            ...path,
                            k,
                        ]);
                    }
                    return r;
                }, {} as AnyRecord);
        }

        throw rootProperty.logicError(
            `invalid join member. Expected string or function, got ${typeof member} for ${path.join('.')}`,
        );
    }

    getJoinValues(node: Extend<T>, data: any, propertyName: string, _index: number): AsyncResponse<Dict<any>> {
        const property = this.factory.findProperty(propertyName);
        if ((property.isReferenceProperty() || property.isCollectionProperty()) && property.join) {
            const { join } = property;

            return asyncArray(Object.keys(join)).reduce(async (r, k) => {
                const member = join[k];
                r[k] = await InteropStorageManager.buildJoinEntry(node, data, property, property, member, [
                    property.name,
                    k,
                ]);
                return r;
            }, {} as AnyRecord);
        }

        return {};
    }

    convertOrderBy(orderBy: any): OrderByClause[] {
        const clauses: OrderByClause[] = [];
        const convertOne = (prevPath: string[], obj: any, factory: NodeFactory): void => {
            Object.keys(obj).forEach(key => {
                const path = [...prevPath, key];
                const property = factory.findProperty(key);
                const val = obj[key];

                if (val && typeof val === 'object' && property.isForeignNodeProperty())
                    convertOne(path, val, property.targetFactory);
                else if (val === -1 || val === 1) {
                    clauses.push({
                        property,
                        path,
                        direction: val,
                        sql: '1',
                        columnAlias: '',
                    });
                } else throw new Error(`${path}: invalid value: ${val})`);
            });
        };
        convertOne([], orderBy, this.factory);
        return clauses;
    }

    parseOrderBy(context: Context, orderBy: OrderBy<Node> | undefined): OrderByClause[] {
        if (!orderBy) return [];

        const flatOrderBy = InteropTransform.flattenObject(orderBy);
        return Object.keys(flatOrderBy).map(key => {
            const propertyPath = key.split('.');
            const { property } = InteropTransform.walkPropertyPath(context, this.factory, propertyPath);
            const direction = flatOrderBy[key];
            return {
                path: propertyPath,
                property,
                direction,
                sql: '',
            };
        });
    }

    parseCursor(orderByClauses: OrderByClause[], value: string): PropertyAndValue[] {
        return parseCursor(
            orderByClauses.map(o => o.property),
            value,
        );
    }

    // Offset transient _id so that it does not interfere with generated ids from payload
    private _lastTransientId = -1000000000;

    allocateTransientId(): string {
        this._lastTransientId -= 1;
        return String(this._lastTransientId);
    }

    getKeyValues(context: Context, values: any, options?: { allocateTransient: boolean }): Dict<any> {
        const keyValues = this.naturalKey.reduce((r, k) => {
            r[k] = values[k];
            return r;
        }, {} as Dict<any>);

        if (keyValues._id === undefined && options?.allocateTransient) keyValues._id = context.allocateTransientId();
        return keyValues;
    }

    isKeyPropertyTransient(_propertyName: string, value: any): boolean {
        const numberValue = Number(value);
        if (Number.isFinite(numberValue) && numberValue < 0) return true;
        return false;
    }

    isReverseReferenceProperty(_propertyName: string): boolean {
        return false;
    }

    private static constructDefaultOrderBy(factory: NodeFactory): OrderBy<Node> {
        if (!factory.naturalKey) return { _id: 1 };
        return factory.naturalKey.reduce((r, k) => {
            const property = factory.findProperty(k);
            if (property.isReferenceProperty() && property.targetFactory.naturalKey) {
                r[k] = this.constructDefaultOrderBy(property.targetFactory);
            } else {
                r[k] = 1;
            }
            return r;
        }, {} as AnyRecord);
    }

    #defaultOrderBy: OrderBy<Node>;

    get defaultOrderBy(): OrderBy<Node> {
        if (this.#defaultOrderBy) return this.#defaultOrderBy;
        this.#defaultOrderBy = InteropStorageManager.constructDefaultOrderBy(this.factory);
        return this.#defaultOrderBy;
    }

    /**
     * Gets the natural key value as a string
     * This will be used for the _id value
     *
     * @param context
     * @param values
     * @returns
     */
    getId(context: Context, values: Dict<any>): string {
        let allKeysFound = true;
        const result = this.naturalKey.reduce((r, k, i) => {
            if (values[k] === undefined) allKeysFound = false;
            let currVal = String(values[k] ?? '');
            const property = this.factory.findProperty(k);
            if (property.isReferenceProperty() && typeof values[k] === 'object' && values[k] !== null) {
                const { targetFactory } = property;
                if (targetFactory.storage === 'external') {
                    if (!targetFactory.externalStorageManager) {
                        throw new Error(`${targetFactory.name}: external storage manager is not defined`);
                    }
                    currVal = targetFactory.externalStorageManager.getId(context, values[k]);
                } else {
                    if (!targetFactory.naturalKey) throw targetFactory.logicError('Missing natural key');
                    const naturalKeyValue = _.pick(values[k], targetFactory.naturalKey);
                    currVal = Object.values(InteropTransform.flattenObject(naturalKeyValue)).join('|');
                }
            }
            if (property.isReferenceProperty() && typeof currVal === 'string' && currVal.startsWith('#')) {
                currVal = currVal.substring(1);
            }
            if (this.factory.keyProperties.length === 1 || i === 0) return currVal;
            return `${r}|${currVal}`;
        }, '');

        if (!allKeysFound) return String(context.allocateTransientId());
        return result;
    }

    canCreate(canCreate: boolean): boolean {
        return canCreate;
    }

    canUpdate(canUpdate: boolean): boolean {
        return canUpdate;
    }

    canDelete(canDelete: boolean): boolean {
        return canDelete;
    }

    canDeleteMany(canDeleteMany: boolean): boolean {
        return canDeleteMany;
    }
}
