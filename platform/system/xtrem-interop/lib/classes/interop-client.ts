import {
    AnyValue,
    AsyncResponse,
    Context,
    Dict,
    GraphQlResponse,
    InteropGraphqlClient,
    LogicError,
    json5Stringify,
} from '@sage/xtrem-core';
import { ExecutionResult } from 'graphql';
import * as _ from 'lodash';
import { AppInfo } from '../nodes/sys-app';

export type Fetcher = <T = unknown>(query: string) => Promise<T>;

export interface InteropClientOptions {
    readonly remoteAppName: string;
    readonly remoteInteropPackageName: string;
    readonly remoteIsConnector: boolean;
}

interface GetAppInfoResponse {
    [packageName: string]: {
        sysApp: {
            getAppInfo: AppInfo;
        };
    };
}

export class InteropClient {
    constructor(readonly options: InteropClientOptions) {}

    get remoteAppName(): string {
        return this.options.remoteAppName;
    }

    get remoteIsConnector(): boolean {
        return this.options.remoteIsConnector;
    }

    get remoteInteropPackageName(): string {
        return this.options.remoteInteropPackageName;
    }

    get remoteInteropGraphqlPackageName(): string {
        return _.camelCase(this.remoteInteropPackageName);
    }

    /**
     * Sends a GraphQL request to the server.
     *
     * @template ResponseDataT The type of the response data.
     * @param {Context} context The context object.
     * @param {string} query The GraphQL query string.
     * @param {Dict<any>} variables The variables to be passed with the query (default: {}).
     * @param {Dict<any>} config Additional configuration options (default: {}).
     * @returns {Promise<GraphQlResponse<ExecutionResult<ResponseDataT>>>} A promise that resolves to the GraphQL response.
     * @internal (used by unit test)
     */
    fetch<ResponseDataT>(
        context: Context,
        query: string,
        variables: Dict<any> = {},
        config: Dict<any> = {},
    ): Promise<GraphQlResponse<ExecutionResult<ResponseDataT>>> {
        return InteropGraphqlClient.sendGraphqlRequest(context, {
            appName: this.remoteAppName,
            scope: 'synchronization',
            query,
            variables,
            config,
        });
    }

    protected logicError(message: string): LogicError {
        return new LogicError(`${this.remoteAppName}: ${message}`);
    }

    protected getCachedValue<T extends AnyValue>(
        context: Context,
        key: string,
        getValue: () => AsyncResponse<T>,
    ): Promise<T> {
        const category = `RemoteApp/${this.remoteInteropGraphqlPackageName}`;
        // A connector app is a special case, as we do not know how often it is updated, we cache the data for 10 minutes,
        // otherwise for 1 hour
        const ttlInSeconds = this.remoteIsConnector ? 600 : 3600;
        return context.getCachedValue({
            category,
            key,
            getValue: async () => {
                return { value: await getValue() };
            },
            cacheInMemory: true,
            ttlInSeconds,
        });
    }

    getAppInfo(context: Context): Promise<AppInfo> {
        return this.getCachedValue(context, 'getAppInfo', async () => {
            const packageName = this.remoteInteropGraphqlPackageName;
            const result = await this.fetch<GetAppInfoResponse>(
                context,
                `{ ${packageName} { sysApp { getAppInfo { name, version, metadataPackage } } } }`,
            );
            const body = result.data;
            const appData = body.data?.[packageName]?.sysApp.getAppInfo;
            if (!appData) {
                const errors = body.errors ?? body.extensions;
                const message = errors ? json5Stringify(errors) : 'No errors returned';
                throw this.logicError(`getAppInfo query did not return any data: ${message}`);
            }
            return appData;
        });
    }

    static stringifyObjectParameter(value: any): string {
        return JSON.stringify(JSON.stringify(value));
    }
}
