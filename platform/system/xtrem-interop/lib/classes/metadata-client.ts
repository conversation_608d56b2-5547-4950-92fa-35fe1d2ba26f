import { Context, Dict, Logger } from '@sage/xtrem-core';
import * as xtremMetadata from '@sage/xtrem-metadata';
import { MetaCustomField } from '@sage/xtrem-shared';
import * as _ from 'lodash';
import {
    RemoteDataTypeInfo,
    RemoteNodeInfo,
    RemoteNodeOperation,
    RemoteNodeProperty,
} from '../nodes/sys-remote-metadata';
import { InteropClient } from './interop-client';

type DataTypeMetadataResult = {
    [packageName: string]: {
        metaDataType: {
            query: {
                edges: {
                    node: {
                        name: string;
                        title: string;
                        attributes: string;
                    };
                }[];
            };
        };
    };
};

type NodeMetadataResult = {
    [packageName: string]: {
        metaNodeFactory: {
            query: {
                edges: {
                    node: {
                        name: string;
                        title: string;
                        package: { name: string };
                        storage: string;
                        customFields: string;
                        isPlatformNode: boolean;
                        naturalKey: string | null;
                        properties?: {
                            query: {
                                edges: {
                                    node: {
                                        name: string;
                                        title: string;
                                        type: string;
                                        dataType: { name: string } | null;
                                        isStored: boolean;
                                        targetFactory: { name: string } | null;
                                        isTransientInput: boolean;
                                        isStoredOutput: boolean;
                                        isRequired: boolean;
                                        isNullable: boolean;
                                        canSort: boolean;
                                        canFilter: boolean;
                                        isOnInputType: boolean;
                                        isOnOutputType: boolean;
                                        isMutable: boolean;
                                        isVitalParent: boolean;
                                    };
                                }[];
                            };
                        };
                        operations?: {
                            query: {
                                edges: {
                                    node: {
                                        name: string;
                                        title: string;
                                        action?: string;
                                        kind: xtremMetadata.enums.MetaOperationKind;
                                        signature: string;
                                        isMutation: boolean;
                                    };
                                }[];
                            };
                        };
                    };
                }[];
            };
        };
    };
};

const logger = Logger.getLogger(__filename, 'metadata');

export class MetadataClient extends InteropClient {
    getDataTypeInfo(context: Context, name?: string, type?: string): Promise<RemoteDataTypeInfo[]> {
        const key = `getDataTypeInfo/${name ?? ''}/${type ?? ''}`;
        return this.getCachedValue(context, key, async () => {
            const appInfo = await this.getAppInfo(context);
            const packageName = _.camelCase(appInfo.metadataPackage);

            const filter: Dict<any> = {};
            if (name) filter.name = name;
            if (type) filter.type = type;

            let queryOptions = '(first: 1000';

            if (Object.keys(filter).length > 0) {
                queryOptions += ` ,filter: "${JSON.stringify(filter).replace(/"/g, '\\"')}"`;
            }

            queryOptions += ')';

            const query = `{
                    ${packageName} {
                        metaDataType {
                            query${queryOptions} {
                                edges {
                                    node {
                                            name,
                                            title,
                                            attributes
                                        }
                                }
                            }
                        }
                    }
            }`;

            logger.verbose(
                () => `Fetching data type info for ${packageName} with query:\n${JSON.stringify(query, null, 2)}`,
            );

            const result = await this.fetch<DataTypeMetadataResult>(context, query);

            const dataTypeDataResults = result.data.data?.[packageName]?.metaDataType.query.edges;

            return (
                dataTypeDataResults?.map(dataTypeData => {
                    const attributes = JSON.parse(dataTypeData.node.attributes);
                    const title = attributes.title[context.currentLocale] ?? attributes.title.base;
                    const values = Array.isArray(attributes.values)
                        ? attributes.values.map((value: any) => {
                              return {
                                  ...value,
                                  title: value.title[context.currentLocale] ?? value.title.base,
                              };
                          })
                        : attributes.values;
                    return {
                        name: dataTypeData.node.name,
                        title: dataTypeData.node.title,
                        attributes: {
                            ...attributes,
                            values,
                            title,
                        },
                    };
                }) ?? []
            );
        });
    }

    getNodeInfo(context: Context, name?: string, options?: { filter?: Dict<any> }): Promise<RemoteNodeInfo[]> {
        const key = `getNodeInfo/${name ?? ''}`;
        return this.getCachedValue(context, key, async () => {
            const appInfo = await this.getAppInfo(context);
            const packageName = _.camelCase(appInfo.metadataPackage);

            let filter: Dict<any> = {
                isActive: true,
                isPublished: true,
            };
            if (name) filter.name = name;
            if (options?.filter) filter = { _and: [filter, options.filter] };

            const queryOptions = `(first: 1000 ,filter: "${JSON.stringify(filter).replace(/"/g, '\\"')}")`;

            const query = `{
                    ${packageName} {
                        metaNodeFactory {
                            query${queryOptions} {
                                edges {
                                    node {
                                        name,
                                        title,
                                        storage,
                                        isPlatformNode,
                                        naturalKey,
                                        package {
                                            name
                                        },
                                        customFields
                                        operations {
                                            query(first: 1000, filter: "{\\"isPublished\\":true}") {
                                                edges {
                                                    node {
                                                        name
                                                        title
                                                        kind
                                                        action
                                                        signature
                                                        isMutation
                                                    }
                                                }
                                            }
                                        }
                                        properties {
                                            query(first: 1000, filter: "{\\"isPublished\\":true}") {
                                                edges {
                                                    node {
                                                        name
                                                        title
                                                        type
                                                        isStored
                                                        isTransientInput
                                                        isStoredOutput
                                                        isRequired
                                                        isNullable
                                                        targetFactory {
                                                            name
                                                        }
                                                        dataType {
                                                            name
                                                        }
                                                        canSort
                                                        canFilter
                                                        isOnInputType
                                                        isOnOutputType
                                                        isMutable
                                                        isVitalParent
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
            }`;

            logger.verbose(
                () => `Fetching node info for ${packageName} with query:\n${JSON.stringify(query, null, 2)}`,
            );
            const result = await this.fetch<NodeMetadataResult>(context, query);

            const nodeDataResults = result.data.data?.[packageName]?.metaNodeFactory.query.edges ?? [];

            // System properties are not exposed on the metadata node, so we need to add them manually
            const systemProperties = (storage: string): RemoteNodeProperty[] => [
                {
                    name: '_id',
                    title: 'ID',
                    type: 'string',
                    isStored: storage === 'sql',
                    isTransientInput: false,
                    isStoredOutput: false,
                    isRequired: false,
                    isNullable: false,
                    isCustom: false,
                    canSort: true,
                    canFilter: true,
                    isOnInputType: true,
                    isOnOutputType: true,
                    isMutable: false,
                    isVitalParent: false,
                } as RemoteNodeProperty,
            ];

            return nodeDataResults.map(nodeData => {
                const properties: RemoteNodeProperty[] = nodeData.node.properties?.query.edges
                    ? nodeData.node.properties?.query.edges.map(propertyData => {
                          return {
                              name: propertyData.node.name,
                              title: propertyData.node.title,
                              type: propertyData.node.type,
                              dataType: propertyData.node.dataType?.name,
                              isStored: propertyData.node.isStored,
                              targetNode: propertyData.node.targetFactory?.name,
                              isTransientInput: propertyData.node.isTransientInput,
                              isStoredOutput: propertyData.node.isStoredOutput,
                              isRequired: propertyData.node.isRequired,
                              isNullable: propertyData.node.isNullable,
                              isCustom: false,
                              canSort: propertyData.node.canSort,
                              canFilter: propertyData.node.canFilter,
                              isOnInputType: propertyData.node.isOnInputType,
                              isOnOutputType: propertyData.node.isOnOutputType,
                              isMutable: propertyData.node.isMutable,
                              isVitalParent: propertyData.node.isVitalParent,
                          };
                      })
                    : [];

                const operations: RemoteNodeOperation[] = nodeData.node.operations?.query.edges
                    ? nodeData.node.operations.query.edges.map(operationData => {
                          return {
                              name: operationData.node.name,
                              title: operationData.node.title,
                              kind: operationData.node.kind,
                              action: operationData.node.action,
                              signature: JSON.parse(operationData.node.signature),
                              isMutation: operationData.node.isMutation,
                          };
                      })
                    : [];

                // Apply the system properties defined above to the node properties if they are not provided already.
                const propertyNames = properties.map(property => property.name);
                systemProperties(nodeData.node.storage).forEach(systemProperty => {
                    if (!propertyNames.includes(systemProperty.name)) {
                        properties.unshift(systemProperty);
                    }
                });

                if (nodeData.node.customFields) {
                    const customFields = JSON.parse(nodeData.node.customFields);
                    if (Array.isArray(customFields)) {
                        (customFields as MetaCustomField[]).forEach(customField => {
                            properties.push({
                                name: customField.name,
                                title: customField.title,
                                type: customField.dataType,
                                dataType: '',
                                canSort: true,
                                canFilter: true,
                                isCustom: true,
                                targetNode: '',
                                isOnInputType: true,
                                isOnOutputType: true,
                                isMutable: false,
                                isStored: true,
                                isTransientInput: false,
                                isStoredOutput: false,
                                isRequired: false,
                                isNullable: true,
                                isVitalParent: false,
                            });
                        });
                    }
                }

                return {
                    name: nodeData.node.name,
                    storage: nodeData.node.storage,
                    packageName: nodeData.node.package.name,
                    title: nodeData.node.title,
                    isPlatformNode: nodeData.node.isPlatformNode,
                    naturalKey:
                        nodeData.node.naturalKey == null ? null : (JSON.parse(nodeData.node.naturalKey) as string[]),
                    properties,
                    operations,
                };
            });
        });
    }

    async findNodeInfo(context: Context, name: string): Promise<RemoteNodeInfo> {
        const remoteNodeInfos = await this.getNodeInfo(context, name);

        if (remoteNodeInfos.length === 0) throw new Error(`${this.remoteAppName}: Node ${name} not found`);

        return remoteNodeInfos[0];
    }
}
