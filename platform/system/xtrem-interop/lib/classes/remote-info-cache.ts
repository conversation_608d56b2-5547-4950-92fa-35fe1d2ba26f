import { Context } from '@sage/xtrem-core';
import { InteropClient, InteropClientOptions } from './interop-client';

export interface RemoteInfo extends InteropClientOptions {
    remoteAppVersion: string;
}

export class RemoteInfoCache {
    static async fetchRemoteInfo(context: Context, options: InteropClientOptions): Promise<RemoteInfo> {
        const client = new InteropClient(options);
        // getAppInfo manages the caching of this information
        const remoteAppVersion = (await client.getAppInfo(context)).version;
        return { ...options, remoteAppVersion };
    }
}
