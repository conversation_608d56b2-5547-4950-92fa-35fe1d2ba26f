import * as xtremCommunication from '@sage/xtrem-communication';
import {
    AnyNode,
    AnyRecord,
    Application,
    asyncArray,
    BaseCollection,
    Context,
    Dict,
    ForeignNodeProperty,
    getFactoryDependsOn,
    integer,
    json5Stringify,
    Logger,
    Node,
    NodeCreateData,
    NodeFactory,
    SyncInfo,
} from '@sage/xtrem-core';
import { decimal, SystemError } from '@sage/xtrem-shared';
import * as _ from 'lodash';
import { SysSynchronizationState } from '../nodes/sys-synchronization-state';
import { InteropClientOptions } from './interop-client';
import { InteropMapper, Mapping, Mappings, Payload, Selector } from './interop-mapper';
import { InteropTransform, NodeTransformData } from './interop-transform';
import { RemoteInfoCache } from './remote-info-cache';
import { SysSynchronizationClient } from './sys-synchronization-client';

/**
 * Represents the synchronization counters for a local node.
 */
export interface SysSyncCounters {
    localNodeName: string;
    created: integer;
    updated: integer;
    skipped: integer;
    errors?: { key: string; error: RecordSyncError }[];
    synchronizerError?: string;
}

interface RecordSyncError {
    message: string;
    sourceSyncTick: number;
    localNodeName: string;
    remoteAppName: string;
    remoteNodeName: string;
}

function stringifyCounters(counters: SysSyncCounters): string {
    return `created: ${counters.created}, updated: ${counters.updated}, skipped: ${counters.skipped}, errors: ${counters.errors?.length}`;
}

/**
 * The logger instance for the synchronization process.
 */
const logger = Logger.getLogger(__filename, 'synchronization-process');

interface SpanContext {
    localNodeName?: string;
    remoteAppName?: string;
    remoteNodeName?: string;
}
interface LogMessageOptions {
    spanContext?: SpanContext;
    data?: any;
    error?: Error;
    duplexLog?: boolean;
}
/**
 * Represents a synchronization process for a specific remote application.
 */
export class SynchronizationProcess {
    /**
     * Constructs a new instance of the SynchronizationProcess class.
     * @param options The options for the synchronization process.
     * @param options.remoteAppName The name of the remote application.
     * @param options.remoteInteropPackageName The name of the remote interop package.
     * @param options.forceSyncState Indicates whether to force synchronization state update. We use this to ignore errors in the synchronization process.
     */
    private constructor(
        private readonly options: InteropClientOptions & { forceSyncState?: boolean; fullSync?: boolean },
    ) {}

    private stringifyError(error: Error | AggregateError): string {
        const err = error as any;
        // AxiosError is not instance of AggregateError
        if (err.name === 'AggregateError') {
            if (err.code === 'ECONNREFUSED') {
                return `${this.options.remoteAppName} connection refused`;
            }
            return err.message || err.errors.map((e: any) => e.message || e.code).join(', ');
        }
        return err.message || err.code || 'unknown error';
    }

    /**
     * Logs a message to the batch log.
     * @param context
     * @param level
     * @param messageIn
     * @param data
     */
    private static async logMessage(
        context: Context,
        level: xtremCommunication.enums.LogLevel,
        messageIn: string,
        options: LogMessageOptions = {},
    ): Promise<void> {
        if (options.error != null || options.duplexLog) {
            logger.log(level, () => (options.error ? `${messageIn}\n${options.error.stack}` : messageIn));
        }

        await context.batch.logMessage(
            level,
            options.error ? `${messageIn}, ${options.error.message}` : messageIn,
            _.pick(options, 'spanContext', 'data'),
        );
    }

    /**
     * Retrieves the start tick for synchronization from the SysSynchronizationState node.
     *
     * @param context - The context object.
     * @param factory - The node factory.
     * @returns A promise that resolves to the start tick, to 0 if this is the first synchronization pass
     */
    private static async getStartTick(context: Context, factory: NodeFactory, remoteApp: string): Promise<decimal> {
        const syncStates = await context.select(
            SysSynchronizationState,
            { syncTick: true },
            { filter: { node: { name: factory.name }, remoteApp: `#${remoteApp}` } },
        );
        return Number(syncStates[0]?.syncTick || 0);
    }

    /**
     * Creates a synchronization state.
     *
     * @param context The context object.
     * @param payload The payload data for creating the synchronization state.
     * @returns A promise that resolves when the synchronization state is created.
     */
    private static async createSynchronizationState(
        context: Context,
        payload: NodeCreateData<SysSynchronizationState>,
    ): Promise<void> {
        const syncState = await context.create(SysSynchronizationState, payload);
        await syncState.$.save();
    }

    /**
     * Updates the synchronization state for a given node.
     * @param context The context object.
     * @param payload The payload containing the node ID and the data to update.
     * @returns A promise that resolves when the synchronization state is updated.
     */
    private static async updateSynchronizationState(
        context: Context,
        payload: NodeCreateData<SysSynchronizationState> & { node: string; remoteApp: string },
    ): Promise<void> {
        const syncState = await context.read(
            SysSynchronizationState,
            { node: payload.node, remoteApp: payload.remoteApp },
            { forUpdate: true },
        );
        await syncState.$.set(payload);
        await syncState.$.save();
    }

    /**
     * Verifies the references of the transformed item. If the target of a reference property is in error, an error is thrown,
     * and we skip the curret row.
     * @param transform
     * @param transformed
     * @param naturalKeyValue
     */
    private verifyItemReferences(
        transform: NodeTransformData,
        transformed: NodeCreateData<AnyNode> & { _syncInfo: SyncInfo },
        naturalKeyValue: string,
    ): void {
        transform.localFactory.properties
            .filter(
                property =>
                    (property.type === 'reference' || property.type === 'referenceArray') &&
                    transformed[property.name] != null,
            )
            .forEach(property => {
                const value = transformed[property.name];
                if (property.isReferenceProperty() || property.isReferenceArrayProperty()) {
                    const errors = this.recordsInErrorByFactory[property.targetFactory.name] ?? [];

                    if (Array.isArray(value)) {
                        if (value.some(v => errors.some(e => e.key === v))) {
                            throw property.inputError(
                                `cannot synchronize: ${property.name} reference failed to synchronize. record key=${naturalKeyValue}, reference keys=${errors
                                    .filter(e => value.includes(e.key))
                                    .map(e => e.key)
                                    .join(',')}`,
                            );
                        }
                    } else if (errors.some(e => e.key === value)) {
                        throw property.inputError(
                            `cannot synchronize: ${property.name} reference failed to synchronize. record key=${naturalKeyValue}, reference key=${value}`,
                        );
                    }
                }
            });
    }

    /**
     * Manages the error that occurred while synchronizing an item.
     * @param context
     * @param localFactoryName
     * @param remoteAppName
     * @param remoteNodeName
     * @param naturalKeyValue
     * @param sourceSyncTick
     * @param error
     * @param counters
     */
    private manageSynchronizeItemError(
        localNodeName: string,
        remoteAppName: string,
        remoteNodeName: string,
        naturalKeyValue: string,
        sourceSyncTick: number,
        error: Error,
        counters: SysSyncCounters,
    ): void {
        this.recordsInErrorByFactory[localNodeName] = this.recordsInErrorByFactory[localNodeName] ?? [];
        this.recordsInErrorByFactory[localNodeName].push({
            key: naturalKeyValue,
            error: {
                message: error.message,
                sourceSyncTick,
                localNodeName,
                remoteAppName,
                remoteNodeName,
            },
        });

        // do not log the record error in the batch log, because it can generate a lot of logs
        counters.skipped += 1;
        counters.errors = this.recordsInErrorByFactory[localNodeName];
    }

    private static removeFrozenProperties(
        node: Node,
        transformed: NodeCreateData<AnyNode>,
    ): Promise<NodeCreateData<AnyNode>> {
        return asyncArray(Object.keys(transformed)).reduce(async (r, k) => {
            const property = node.$.factory.findProperty(k);
            if (await node.$.isPropertyFrozen(property)) {
                return r;
            }

            const propertyValue = transformed[k];

            if (propertyValue == null) {
                r[k] = transformed[k];
                return r;
            }

            if (property.isReferenceProperty() && property.isMutable) {
                const referenceValue = node.$.get(k);
                if (referenceValue != null && referenceValue instanceof Node && typeof propertyValue === 'object') {
                    r[k] = this.removeFrozenProperties(referenceValue, propertyValue as NodeCreateData<AnyNode>);
                    return r;
                }
            }

            if (property.isCollectionProperty() && property.isMutable) {
                const collectionValue = node.$.get(k);
                if (
                    collectionValue != null &&
                    collectionValue instanceof BaseCollection &&
                    Array.isArray(propertyValue)
                ) {
                    r[k] = await asyncArray(propertyValue)
                        .map(async (v: NodeCreateData<AnyNode>) => {
                            const collectionNode = await collectionValue.find(
                                async c =>
                                    (await c.$.getNaturalKeyValue()) ===
                                    (await property.targetFactory.getNaturalKeyStringValue(node.$.context, v)),
                            );
                            if (collectionNode == null) return v;
                            return this.removeFrozenProperties(collectionNode, v);
                        })
                        .toArray();
                    return r;
                }
            }

            r[k] = transformed[k];
            return r;
        }, {} as NodeCreateData<AnyNode>);
    }

    /**
     * Synchronizes an item from the synchronization feed, using the provided context, transform, payload, and counters.
     * Counter values are updated based on the result of the synchronization.
     *
     * @param context - The context object.
     * @param transform - The synchronization transform.
     * @param payload - The payload data.
     * @param counters - The synchronization counters.
     * @returns A promise that resolves when the synchronization is complete.
     */
    private static async synchronizeItem(
        context: Context,
        transform: NodeTransformData,
        transformed: NodeCreateData<AnyNode> & { _syncInfo: SyncInfo },
        naturalKeyValue: string,
        counters: SysSyncCounters,
    ): Promise<void> {
        const nodeConstructor = transform.localFactory.nodeConstructor;

        // Create or update the local instance with the transformed payload
        // Updates the counters too.
        let instance = await context.tryRead(nodeConstructor, { _id: `#${naturalKeyValue}` }, { forUpdate: true });
        if (!instance) {
            instance = await context.create(nodeConstructor, transformed);
            await instance.$.save();
            counters.created += 1;
        } else if ((await instance._syncInfo).sourceSyncTick !== transformed._syncInfo.sourceSyncTick) {
            await instance.$.set(await this.removeFrozenProperties(instance, transformed));
            await instance.$.save();
            counters.updated += 1;
        } else {
            counters.skipped += 1;
        }
    }

    private recordsInErrorByFactory: Dict<{ key: string; error: RecordSyncError }[]> = {};

    private static getRemoteNodeMappingsSelector(
        context: Context,
        factory: NodeFactory,
        remoteAppName: string,
        remoteAppVersion: string,
        mappings: Mappings,
        options?: { fromNaturalKey?: boolean },
    ): Promise<Selector> {
        return InteropMapper.getMappingsSelector(
            mappings,
            async (localPath: string, remotePath: string, result: Dict<Selector>) => {
                const { property } = InteropTransform.walkPropertyPath(context, factory, localPath.split('.'));
                if (property.isForeignNodeProperty()) {
                    const { targetFactory } = property;
                    const targetTransform = await InteropTransform.findNodeTransform(context, targetFactory, {
                        remoteAppName,
                        remoteAppVersion,
                    });

                    if (property.isMutable && !options?.fromNaturalKey) {
                        const selector = await this.getRemoteNodeMappingsSelector(
                            context,
                            targetFactory,
                            remoteAppName,
                            remoteAppVersion,
                            targetTransform.mappings,
                        );
                        _.set(result, remotePath, selector);
                    } else {
                        if (!targetFactory.naturalKey) throw new Error(`${targetFactory.name}: Missing natural key`);
                        const naturalKeyMappings = targetFactory.naturalKey.reduce((r, k) => {
                            const mapping = targetTransform.mappings[k];
                            if (mapping == null)
                                throw Error(`${targetFactory.name}: Missing natural key mapping for ${k}`);
                            r[k] = mapping;
                            return r;
                        }, {} as Mappings);
                        const selector = await this.getRemoteNodeMappingsSelector(
                            context,
                            targetFactory,
                            remoteAppName,
                            remoteAppVersion,
                            naturalKeyMappings,
                            { fromNaturalKey: true },
                        );
                        _.set(result, remotePath, selector);
                    }
                } else {
                    _.set(result, remotePath, true);
                }
            },
        );
    }

    private static async transformForeignNodePropertyValue(
        context: Context,
        transform: NodeTransformData,
        propertyValue: any,
        property: ForeignNodeProperty,
        mapping: Mapping,
    ): Promise<any> {
        if (property.isMutable) {
            const targetTransform = await InteropTransform.findNodeTransform(context, property.targetFactory, {
                remoteAppName: transform.remoteAppName,
                remoteAppVersion: transform.remoteAppVersion,
            });
            return this.transformInboundNodeValues(context, targetTransform, propertyValue);
        }

        if (property.isVitalParent || property.isAssociationParent) {
            return `#${await property.targetFactory.getNaturalKeyStringValue(context, propertyValue)}`;
        }

        return InteropTransform.getInboundReferenceValue(context, property, propertyValue, mapping);
    }

    static async transformInboundNodeValues(
        context: Context,
        transform: NodeTransformData,
        payload: Payload,
    ): Promise<Payload> {
        const mappedPayload = await InteropMapper.applyMappings(payload, transform.mappings);

        return asyncArray(Object.keys(mappedPayload)).reduce(async (r, k) => {
            const property = transform.localFactory.findProperty(k);

            const propertyValue = mappedPayload[k];
            if (property.isReferenceProperty()) {
                const mapping = InteropTransform.getPropertyMapping(transform, property);
                const targetNaturalKey = property.targetFactory.naturalKey;
                if (!targetNaturalKey) throw property.systemError(`missing natural key`);

                if (!mapping) {
                    if (
                        targetNaturalKey.every(t => {
                            return propertyValue[t] != null;
                        })
                    ) {
                        r[k] = `#${await property.targetFactory.getNaturalKeyStringValue(context, propertyValue)}`;
                        return r;
                    }
                    throw Error(`Missing mapping for ${transform.localFactory.name}.${k}`);
                }

                r[k] = await this.transformForeignNodePropertyValue(
                    context,
                    transform,
                    propertyValue,
                    property,
                    mapping,
                );
            } else if (property.isReferenceArrayProperty() || (property.isCollectionProperty() && property.isMutable)) {
                const mapping = InteropTransform.getPropertyMapping(transform, property);
                if (!mapping) throw Error(`Missing mapping for ${transform.localFactory.name}.${k}`);
                r[k] = await asyncArray(propertyValue)
                    .map((v: Payload) => {
                        return this.transformForeignNodePropertyValue(context, transform, v, property, mapping);
                    })
                    .toArray();
            } else if (!property.isCollectionProperty()) {
                r[k] = InteropTransform.transformInboundPropertyValue(context, property, propertyValue);
            }
            return r;
        }, {} as Payload);
    }

    /**
     * Synchronizes a local factory with the remote app.
     *
     * @param context - The context object.
     * @param localFactory - The local factory to synchronize.
     * @returns A promise that resolves to the synchronization counters.
     */
    private async synchronizeLocalFactory(
        context: Context,
        localFactory: NodeFactory,
        feedOptions: { filter?: AnyRecord },
    ): Promise<SysSyncCounters> {
        const { options } = this;
        const remoteAppName = options.remoteAppName;
        logger.info(`${localFactory.name}: starting synchronization from ${remoteAppName} app`);

        const client = new SysSynchronizationClient(options);
        const remoteAppVersion = (await RemoteInfoCache.fetchRemoteInfo(context, options)).remoteAppVersion;
        const transform = await InteropTransform.findNodeTransform(context, localFactory, {
            remoteAppName,
            remoteAppVersion,
        });
        // local to remote
        const sourceSelector = await SynchronizationProcess.getRemoteNodeMappingsSelector(
            context,
            localFactory,
            remoteAppName,
            remoteAppVersion,
            transform.mappings,
        );

        if (sourceSelector === true) {
            throw localFactory.systemError('cannot synchronize: no remote selector');
        }
        const selector = { ...sourceSelector, _syncTick: true, _updateStamp: true };
        const hasFilter = !!feedOptions.filter && Object.keys(feedOptions.filter).length > 0;
        const startTick =
            !hasFilter && !options.fullSync
                ? await SynchronizationProcess.getStartTick(context, localFactory, remoteAppName)
                : 0;
        const initialCount = 2;
        const nodeName = transform.remoteNodeName;

        // Initialize the counters
        const counters: SysSyncCounters = {
            localNodeName: transform.localFactory.name,
            created: 0,
            updated: 0,
            skipped: 0,
        };

        const message = `${localFactory.name}<-${nodeName}: starting synchronization feed from ${remoteAppName} app, initialCount=${initialCount}`;
        const spanContext = { localNodeName: localFactory.name, remoteAppName, remoteNodeName: nodeName };
        // Get the synchronization feed and process each item
        await SynchronizationProcess.logMessage(context, 'info', message, {
            duplexLog: true,
            spanContext,
        });

        // Get the natural key and value for the local factory
        const naturalKey = transform.localFactory.naturalKey;
        if (!naturalKey) throw transform.localFactory.logicError('no natural key');

        let { filter } = feedOptions;

        if (transform.filter) {
            if (filter) {
                filter = { _and: [filter, transform.filter] };
            } else {
                filter = transform.filter;
            }
        }

        const feed = await client.getFeed(context, { nodeName, startTick, selector, initialCount, filter });

        await feed.reader.forEach(async (payload, i) => {
            const transformedData = await SynchronizationProcess.transformInboundNodeValues(
                context,
                transform,
                payload,
            );
            const transformed: NodeCreateData<AnyNode> & { _syncInfo: SyncInfo } = {
                ...transformedData,
                _syncInfo: {
                    sourceSyncTick: Number(payload._syncTick),
                    sourceUpdateStamp: payload._updateStamp,
                },
            };
            const naturalKeyValue = naturalKey.map(propertyName => transformed[propertyName]).join('|');

            await context
                .runInWritableContext(childContext => {
                    this.verifyItemReferences(transform, transformed, naturalKeyValue);
                    return SynchronizationProcess.synchronizeItem(
                        childContext,
                        transform,
                        transformed,
                        naturalKeyValue,
                        counters,
                    );
                })
                .catch(error => {
                    this.manageSynchronizeItemError(
                        localFactory.name,
                        remoteAppName,
                        nodeName,
                        naturalKeyValue,
                        transformed._syncInfo.sourceSyncTick,
                        error,
                        counters,
                    );
                });

            // Log and update the counters every 100 records knowning i starts at 0, if there are less than 100 records then there will be a log at the end with the counters
            if (i % 100 === 0) {
                await SynchronizationProcess.logMessage(
                    context,
                    'info',
                    `${localFactory.name}<-${nodeName}: processed ${i + 1} items from ${remoteAppName} app: ${stringifyCounters(counters)}`,
                    {
                        duplexLog: true,
                        spanContext,
                    },
                );
            }
        });

        // We only update the synchronization state if there are no filters, and if there are no errors in the synchronization process
        // TODO: do we want to allow the synchronization state to be forced updated if there is a filter?
        const manageSyncState =
            !hasFilter && (options.forceSyncState || this.recordsInErrorByFactory[localFactory.name] == null);

        if (manageSyncState) {
            // Update the synchronization state
            const notificationId = context.getContextValue('notificationId');
            const lastNotificationState = notificationId
                ? await context.read(xtremCommunication.nodes.SysNotificationState, { notificationId })
                : null;

            const syncStatePayload = {
                node: `#${localFactory.name}`,
                remoteApp: `#${remoteAppName}`,
                syncTick: feed.nextTick,
                lastNotificationState,
            };
            await context.runInWritableContext(async childContext => {
                const syncState = await childContext.tryRead(SysSynchronizationState, {
                    node: syncStatePayload.node,
                    remoteApp: syncStatePayload.remoteApp,
                });

                if (syncState) {
                    await SynchronizationProcess.updateSynchronizationState(childContext, syncStatePayload);
                } else {
                    await SynchronizationProcess.createSynchronizationState(childContext, syncStatePayload);
                }
            });
        }
        await SynchronizationProcess.logMessage(
            context,
            'info',
            `${localFactory.name}<-${nodeName}: completed synchronization from ${remoteAppName} app: \n${stringifyCounters(counters)}`,
            {
                duplexLog: true,
                spanContext,
                data: counters,
            },
        );

        return counters;
    }

    /**
     * Retrieves all synchronizable factories from the given application.
     *
     * @param application - The application instance.
     * @returns An array of synchronizable NodeFactory instances.
     */
    private static getAllSynchronizableFactories(application: Application): NodeFactory[] {
        return application.getAllSortedFactories().filter(factory => factory.isSynchronized);
    }

    private getDependentFactories(factory: NodeFactory, allFactories: NodeFactory[]): NodeFactory[] {
        const dependentFactories = getFactoryDependsOn(factory, allFactories).map(dep =>
            factory.application.getFactoryByName(dep),
        );
        // Add dependent factories of the vital properties
        factory.vitalProperties.forEach(prop => {
            dependentFactories.push(...this.getDependentFactories(prop.targetFactory, allFactories));
        });
        return _.uniq(
            dependentFactories.filter(dep => dep !== factory && dep.isSynchronized && allFactories.includes(dep)),
        );
    }

    /**
     * Synchronizes local factories.
     *
     * @param context - The context object.
     * @param targetFactories - The local factories to synchronize.
     * @param options - The synchronization options.
     * @param options.skipDependencies - Flag indicating whether to skip dependencies.
     * @returns A promise that resolves to an array of SysSyncCounters.
     */
    private async synchronizeTargetFactories(
        context: Context,
        targetFactories: NodeFactory[],
        { skipDependencies, filtersByNode }: { skipDependencies: boolean; filtersByNode: Dict<AnyRecord> },
    ): Promise<SysSyncCounters[]> {
        // Get all factories if skipDependencies is false, otherwise get only the local factories
        const allFactories = skipDependencies
            ? targetFactories
            : SynchronizationProcess.getAllSynchronizableFactories(context.application);

        // Create a map of synchronizers for each factory
        const synchronizers = {} as Dict<Promise<SysSyncCounters>>;

        let totalCount = 0;
        let successCount = 0;
        let errorCount = 0;

        // Create a factory synchronizer, if it doesn't exist
        const makeSynchronizer = async (factory: NodeFactory): Promise<SysSyncCounters> => {
            if (!synchronizers[factory.name]) {
                totalCount += 1;

                const dependencyPromises = this.getDependentFactories(factory, allFactories).map(dep =>
                    makeSynchronizer(dep),
                );

                // update progress as totalCount has changed
                await context.batch.updateProgress({
                    successCount,
                    errorCount,
                    totalCount,
                });

                // The synchronizer must wait for all its dependencies to be synchronized
                synchronizers[factory.name] = Promise.all(dependencyPromises)
                    .then(async () => {
                        const filter = filtersByNode[factory.name];
                        const result = await this.synchronizeLocalFactory(context, factory, { filter });

                        if (this.recordsInErrorByFactory[factory.name] != null) {
                            errorCount += 1;
                        } else {
                            successCount += 1;
                        }
                        // update progress as successCount has changed
                        await context.batch.updateProgress({
                            successCount,
                            errorCount,
                            totalCount,
                        });
                        return result;
                    })
                    .catch(e => {
                        return {
                            localNodeName: factory.name,
                            created: 0,
                            updated: 0,
                            skipped: 0,
                            synchronizerError: this.stringifyError(e),
                        };
                    });
            }
            return synchronizers[factory.name];
        };

        // Create a synchronizer for each local factory
        await asyncArray(targetFactories).forEach(async factory => {
            await makeSynchronizer(factory);
        });

        // Wait for all synchronizers to complete
        const results = await Promise.all(Object.values(synchronizers));
        const synchronizerErrors = results.filter(r => r.synchronizerError).map(r => r.synchronizerError);
        if (synchronizerErrors.length) {
            throw new SystemError(json5Stringify(results));
        }

        // update progress as all promises have been resolved
        await context.batch.updateProgress({
            successCount,
            errorCount,
            totalCount,
        });

        return results;
    }

    /**
     * Retrieves the local factories based on the given application and local node names.
     * If local node names are provided, it returns the factories with matching names.
     * Otherwise, it returns all synchronizable factories of the application.
     *
     * @param application - The application object.
     * @param localNodeNames - The names of the local nodes.
     * @returns An array of NodeFactory objects representing the local factories.
     */
    static getTargetFactories(application: Application, localNodeNames: string[]): NodeFactory[] {
        return localNodeNames.length > 0
            ? localNodeNames.map(name => application.getFactoryByName(name))
            : this.getAllSynchronizableFactories(application);
    }

    /**
     * Runs the synchronization process.
     *
     * @param context - The context object.
     * @param options - The options for the synchronization process.
     * @param options.remoteAppName - The name of the remote application.
     * @param options.localNodeNames - The names of the local nodes.
     * @param options.skipDependencies - Indicates whether to skip dependencies.
     * @param options.forceSyncState Indicates whether to force synchronization state update. We use this to ignore errors in the synchronization process.
     * @param options.fullSync Indicates whether to perform a full synchronization.
     * @returns A promise that resolves to an array of SysSyncCounters.
     */
    static async run(
        context: Context,
        {
            remoteAppName,
            remoteInteropPackageName,
            remoteIsConnector,
            localNodeNames,
            skipDependencies,
            forceSyncState,
            fullSync,
            filtersByNode,
        }: {
            remoteAppName: string;
            remoteInteropPackageName: string;
            remoteIsConnector: boolean;
            localNodeNames: string[];
            skipDependencies: boolean;
            forceSyncState?: boolean;
            fullSync?: boolean;
            filtersByNode: Dict<AnyRecord>;
        },
    ): Promise<SysSyncCounters[]> {
        await SynchronizationProcess.logMessage(
            context,
            'info',
            `Starting synchronization from ${remoteAppName} app for nodes \n${localNodeNames.join(', ')}`,
            { spanContext: { remoteAppName } },
        );
        const process = new SynchronizationProcess({
            remoteAppName,
            remoteInteropPackageName,
            remoteIsConnector,
            forceSyncState,
            fullSync,
        });
        const targetFactories = this.getTargetFactories(context.application, localNodeNames);
        const spanContext = { remoteAppName };
        try {
            const result = await process.synchronizeTargetFactories(context, targetFactories, {
                skipDependencies,
                filtersByNode,
            });

            await SynchronizationProcess.logMessage(
                context,
                'result',
                `Synchronization from ${remoteAppName} app completed: \n${result.map(r => stringifyCounters(r)).join('\n')}`,
                { spanContext, data: result },
            );
            return result;
        } catch (error) {
            await context.batch.end();
            await SynchronizationProcess.logMessage(
                context,
                'error',
                `Synchronization from ${remoteAppName} app failed: ${error.message}`,
                { spanContext },
            );

            throw error;
        }
    }
}
