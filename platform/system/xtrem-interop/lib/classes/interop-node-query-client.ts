import {
    AnyRecord,
    Context,
    Diagnose,
    Dict,
    GraphQlResponse,
    Node,
    NodeExternalQueryOptions,
    Property,
    PropertyAndValue,
    ValidationSeverity,
    asyncArray,
    parseCursor,
} from '@sage/xtrem-core';
import { ExecutionResult } from 'graphql';
import { camelCase } from 'lodash';
import { RemoteNodeInfo } from '../nodes/sys-remote-metadata';
import { FirstQueryResult, InteropBaseNodeClient, QueryResult, logger } from './interop-base-node-client';
import { InteropClient } from './interop-client';
import { Payload, Selector } from './interop-mapper';
import { InteropTransform, NodeTransformData, PathTransformData } from './interop-transform';
import { MetadataClient } from './metadata-client';
import _ = require('lodash');

export interface QueryReturnType {
    payload: AnyRecord[];
    pageInfo: {
        hasNextPage: boolean;
        endCursor?: string;
    };
}

export interface RemoteQueryOptions<T extends Node> extends NodeExternalQueryOptions<T> {
    remotePagingOptions?: {
        after?: string;
    };
}

export class InteropNodeQueryClient extends InteropBaseNodeClient {
    private static getCursorPropertiesAndValues(
        context: Context,
        transform: NodeTransformData,
        cursor: string,
        orderBy: AnyRecord = {},
    ): Promise<PropertyAndValue[]> {
        const flattenedOrderBy = InteropTransform.flattenObject(orderBy);

        const cursorProperties = Object.keys(flattenedOrderBy).map(key => {
            return InteropTransform.walkPropertyPath(context, transform.localFactory, key.split('.'));
        });

        const cursorValues = parseCursor(
            cursorProperties.map(cp => cp.property),
            cursor,
        );

        return asyncArray(cursorValues).reduce(async (r, cursorValue, i) => {
            if (cursorValue.property.isReferenceProperty() && cursorValue.property.targetFactory.storage === 'sql') {
                const { targetFactory } = cursorValue.property;
                const orderByNode = await context.read(targetFactory.nodeConstructor, {
                    _id: Number(cursorValue.value),
                });
                const naturalKeyValue = await targetFactory.getNaturalKeyValueFromNode(orderByNode);
                const flattenedNaturalKeyValues = InteropTransform.flattenObject(naturalKeyValue);

                Object.keys(flattenedNaturalKeyValues).forEach(key => {
                    const naturalKeyProperty = InteropTransform.walkPropertyPath(
                        context,
                        targetFactory,
                        key.split('.'),
                    );
                    r.push({ property: naturalKeyProperty.property, value: flattenedNaturalKeyValues[key] });
                });
            } else if (cursorValue.property.isReferenceProperty()) {
                const { targetFactory } = cursorValue.property;
                const naturalKeyValue =
                    typeof cursorValue.value === 'string'
                        ? targetFactory.parseNodeId(cursorValue.value)
                        : cursorValue.value;
                const flattenedNaturalKeyValues = InteropTransform.flattenObject(naturalKeyValue);

                Object.keys(flattenedNaturalKeyValues).forEach(key => {
                    const naturalKeyProperty = InteropTransform.walkPropertyPath(
                        context,
                        targetFactory,
                        key.split('.'),
                    );
                    r.push({ property: naturalKeyProperty.property, value: flattenedNaturalKeyValues[key] });
                });
            } else if (cursorValue.property.name === '_id') {
                const cursorProperty = cursorProperties[i];
                const targetFactory = cursorProperty.factory;
                const naturalKeyValue = targetFactory.parseNodeId(cursorValue.value);
                const flattenedNaturalKeyValues = InteropTransform.flattenObject(naturalKeyValue);

                Object.keys(flattenedNaturalKeyValues).forEach(key => {
                    const naturalKeyProperty = InteropTransform.walkPropertyPath(
                        context,
                        targetFactory,
                        key.split('.'),
                    );
                    r.push({ property: naturalKeyProperty.property, value: flattenedNaturalKeyValues[key] });
                });
            } else {
                r.push(cursorValue);
            }

            return r;
        }, [] as PropertyAndValue[]);
    }

    static async getQueryArgs(
        context: Context,
        transform: NodeTransformData,
        queryOptions?: RemoteQueryOptions<Node>,
    ): Promise<string> {
        const querArgsList = [];
        const orderByProperties = [] as { property: Property; propertyPath: string; direction: 1 | -1 }[];
        if (queryOptions?.orderBy) {
            const remoteOrderBy = await InteropTransform.transformOrderBy(
                context,
                transform.remoteAppName,
                transform.remoteAppVersion,
                transform.localFactory,
                queryOptions.orderBy,
                orderByProperties,
            );

            querArgsList.push(`orderBy: ${InteropClient.stringifyObjectParameter(remoteOrderBy)}`);
        }

        const filters: any[] = queryOptions?.filters
            ? [...queryOptions.filters.filter(f => Object.keys(f).length > 0)]
            : [];

        if (transform.filter) {
            filters.push(transform.filter);
        }

        const addCursorFilter = async (cursor: string, cursorOperation: 'before' | 'after'): Promise<void> => {
            const cursorValues = await this.getCursorPropertiesAndValues(
                context,
                transform,
                cursor,
                queryOptions?.orderBy,
            );

            if (cursorValues.length !== orderByProperties.length) {
                throw new Error('Cursor does not match order by properties');
            }

            filters.push(InteropTransform.getCursorFilter(orderByProperties, cursorValues, cursorOperation));
        };

        if (queryOptions?.before) await addCursorFilter(queryOptions.before, 'before');
        if (queryOptions?.after) await addCursorFilter(queryOptions.after, 'after');

        const filter = filters.length
            ? await InteropTransform.transformFilter(
                  context,
                  transform.remoteAppName,
                  transform.remoteAppVersion,
                  transform.localFactory,
                  { _and: filters },
              )
            : undefined;

        if (filter) {
            querArgsList.push(`filter: ${InteropClient.stringifyObjectParameter(filter)}`);
        }

        if (queryOptions?.first) {
            querArgsList.push(`first: ${queryOptions.first}`);
        }

        if (queryOptions?.last) {
            querArgsList.push(`last: ${queryOptions.last}`);
        }

        if (queryOptions?.remotePagingOptions?.after) {
            querArgsList.push(`after: "${queryOptions.remotePagingOptions.after.replace(/"/g, '\\"')}"`);
        }

        let querArgs = '';
        if (querArgsList.length > 0) {
            querArgs = `(${querArgsList.join(',')})`;
        }

        return querArgs;
    }

    static async getQueryBody(
        context: Context,
        transform: NodeTransformData,
        remoteNodeInfo: RemoteNodeInfo,
        metadataClient: MetadataClient,
        queryOptions?: RemoteQueryOptions<Node>,
    ): Promise<string> {
        if (queryOptions?.count) {
            return 'totalCount';
        }
        const selector =
            queryOptions?.selector && queryOptions.selector !== true
                ? await InteropTransform.transformSelector(
                      context,
                      transform.remoteAppName,
                      transform.remoteAppVersion,
                      transform.localFactory,
                      queryOptions.selector as Dict<Selector>,
                  )
                : await InteropTransform.getRemoteNodeMappingsSelector(
                      context,
                      transform.localFactory,
                      transform.mappings,
                      transform.remoteAppName,
                      transform.remoteAppVersion,
                  );
        const strSelector = await InteropTransform.transformSelectorForQuery(
            context,
            remoteNodeInfo,
            selector,
            metadataClient,
        );
        return `edges { node { ${strSelector} } } pageInfo { hasNextPage  endCursor }`;
    }

    static async getAggregateQueryBody<T extends Node>(
        context: Context,
        transform: NodeTransformData,
        queryOptions?: RemoteQueryOptions<T>,
    ): Promise<string> {
        if (!queryOptions?.aggregate) throw new Error('Aggregate not defined');
        const { aggregate } = queryOptions;
        const { groups, values } = aggregate;

        const walkObject = (obj: any, suffix = ''): string => {
            let stringResult = '';
            Object.keys(obj).forEach(key => {
                const value = obj[key];
                if (typeof value === 'object') {
                    stringResult += ` ${key} { ${walkObject(value)} } `;
                } else {
                    stringResult += ` ${key}${value} ${suffix}`;
                }
            });
            return stringResult;
        };

        let result = '';

        const pathTransforms: Dict<PathTransformData> = {};

        if (groups.length > 0) {
            const groupBy = {} as any;
            await asyncArray(groups).forEach(async group => {
                const groupLocalPath = group.path.join('.');
                if (!pathTransforms[groupLocalPath]) {
                    pathTransforms[groupLocalPath] = await InteropTransform.transformPath(
                        context,
                        transform.localFactory,
                        transform.remoteAppName,
                        transform.remoteAppVersion,
                        group.path,
                    );
                }
                const pathTransform = pathTransforms[groupLocalPath];

                const groupValues = [];
                if (group.groupedBy) {
                    groupValues.push(`by: ${group.groupedBy}`);
                }
                if (
                    pathTransform.property.isJsonProperty() &&
                    pathTransform.remainingPath &&
                    pathTransform.remainingPath.length > 0
                ) {
                    groupValues.push(`selector: "${pathTransform.remainingPath.join('.')}"`);
                }
                _.set(
                    groupBy,
                    pathTransform.remotePath.join('.'),
                    groupValues.length > 0 ? `(${groupValues.join(',')})` : '',
                );
            });

            const groupString = walkObject(groupBy);

            // group { document { _customData(selector: \"commentDate\", by: day) } }
            result += `group { ${groupString}  } `;
        }

        if (values.length > 0) {
            const valuesObj = {} as any;
            await asyncArray(values).forEach(async value => {
                const valueLocalPath = value.path.join('.');
                if (!pathTransforms[valueLocalPath]) {
                    pathTransforms[valueLocalPath] = await InteropTransform.transformPath(
                        context,
                        transform.localFactory,
                        transform.remoteAppName,
                        transform.remoteAppVersion,
                        value.path,
                    );
                }

                const pathTransform = pathTransforms[valueLocalPath];

                const valueOperator = value.operator;
                if (!valueOperator) throw new Error(`${valueLocalPath}: missing aggregate operator`);

                let currVal = _.get(valuesObj, pathTransform.remotePath.join('.'), '');

                if (
                    pathTransform.property.isJsonProperty() &&
                    pathTransform.remainingPath &&
                    pathTransform.remainingPath.length > 0 &&
                    currVal === ''
                ) {
                    currVal += `(selector: "${pathTransform.remainingPath.join('.')}") { `;
                }
                if (currVal === '') {
                    currVal += ` { `;
                }
                currVal += ` ${valueOperator} `;
                _.set(valuesObj, pathTransform.remotePath.join('.'), currVal);
            });
            const valuesString = walkObject(valuesObj, '}');
            result += ` values { ${valuesString}  }  `;
        }

        // values { _customData(selector: \"size\") { min, sum, avg, max } }

        return `edges { node { ${result} } } pageInfo { hasNextPage endCursor}`;
    }

    static async transformAggregatePayload<T extends Node>(
        context: Context,
        transform: NodeTransformData,
        payload: Payload,
        queryOptions?: RemoteQueryOptions<T>,
    ): Promise<Payload> {
        if (!queryOptions?.aggregate) throw new Error('Aggregate not defined');
        const { aggregate } = queryOptions;
        const { groups, values } = aggregate;

        const result: Payload = {};

        const pathTransforms: Dict<PathTransformData> = {};

        if (groups.length > 0) {
            await asyncArray(groups).forEach(async group => {
                const groupLocalPath = group.path.join('.');
                if (!pathTransforms[groupLocalPath]) {
                    pathTransforms[groupLocalPath] = await InteropTransform.transformPath(
                        context,
                        transform.localFactory,
                        transform.remoteAppName,
                        transform.remoteAppVersion,
                        group.path,
                    );
                }
                const pathTransform = pathTransforms[groupLocalPath];
                const remoteValue = InteropTransform.transformInboundPropertyValue(
                    context,
                    pathTransform.property,
                    _.get(payload.group, pathTransform.remotePath.join('.')),
                );
                _.set(result, `group.${groupLocalPath}`, remoteValue);
            });
        }

        if (values.length > 0) {
            await asyncArray(values).forEach(async value => {
                const valueLocalPath = value.path.join('.');
                if (!pathTransforms[valueLocalPath]) {
                    pathTransforms[valueLocalPath] = await InteropTransform.transformPath(
                        context,
                        transform.localFactory,
                        transform.remoteAppName,
                        transform.remoteAppVersion,
                        value.path,
                    );
                }

                const pathTransform = pathTransforms[valueLocalPath];
                const remoteValue = _.get(payload.values, pathTransform.remotePath.join('.'));
                _.set(result, `values.${valueLocalPath}`, remoteValue);
            });
        }

        return result;
    }

    static async transformAggregateQueryResult<T extends Node>(
        context: Context,
        result: Payload,
        queryResult: QueryResult,
        transform: NodeTransformData,
        queryOptions: RemoteQueryOptions<T>,
    ): Promise<AnyRecord[]> {
        if (queryResult.queryAggregate == null) {
            if (result.data.errors) {
                throw new Error(`${transform.remoteAppName}: Query failed, ${result.data.errors}`);
            }

            throw new Error(`${transform.remoteAppName}: Query failed, no data returned`);
        }

        const fullData = await asyncArray(queryResult.queryAggregate.edges)
            .map(edge => InteropNodeQueryClient.transformAggregatePayload(context, transform, edge.node, queryOptions))
            .toArray();

        if (queryResult.query?.pageInfo)
            logger.verbose(() => `Page Info ${JSON.stringify(queryResult.query?.pageInfo, null, 2)}`);
        return fullData ?? [];
    }

    static checkNodeQueryResultForErrors(context: Context, result: GraphQlResponse<ExecutionResult<Payload>>): boolean {
        const errors = result.data.errors;

        if (errors) {
            // TODO: review if this is the correct way to handle errors
            // should we log the remote error and just throw a generic error?
            // what do we do with the remote paths in the diagnoses?
            if (Array.isArray(errors)) {
                errors.forEach(err => {
                    context.addDiagnoseAtPath(ValidationSeverity.error, [''], err.message);
                    if (err.extensions?.diagnoses && Array.isArray(err.extensions.diagnoses)) {
                        err.extensions.diagnoses.forEach((d: Diagnose) => {
                            switch (d.severity) {
                                case ValidationSeverity.exception:
                                case ValidationSeverity.error:
                                case ValidationSeverity.warn:
                                    context.addDiagnoseAtPath(d.severity, [''], d.message);
                                    break;
                                default:
                                    context.addDiagnoseAtPath(ValidationSeverity.info, [''], d.message);
                                    break;
                            }
                        });
                    }
                });
            }
            return false;
        }

        return true;
    }

    async queryAggregateRemoteCollection(
        context: Context,
        transform: NodeTransformData,
        queryOptions: RemoteQueryOptions<Node>,
        querArgs: string,
        queryBody: string,
    ): Promise<AnyRecord[]> {
        if (!queryOptions.collection) throw new Error('Collection not defined');

        const parentFactory = queryOptions.collection.sourceNode.$.factory;
        const parentNode = queryOptions.collection.sourceNode;
        const remoteNaturalKey = await InteropTransform.getRemoteNaturalValue(
            this.metadataClient,
            transform.remoteAppVersion,
            parentNode,
        );
        const parentClient = new InteropNodeQueryClient({
            remoteAppName: this.remoteAppName,
            remoteInteropPackageName: this.remoteInteropPackageName,
            remoteIsConnector: this.remoteIsConnector,
            localFactory: parentFactory,
        });

        const parentRemoteNodeInfo = await parentClient.getRemoteNodeInfo(context);
        const parentTransform = await InteropTransform.findNodeTransform(context, parentFactory);
        const remoteParentPackageName = camelCase(parentRemoteNodeInfo.packageName.split('/').pop() ?? '');
        const remoteParentNodeName = camelCase(parentTransform.remoteNodeName);
        const collectionMapping = InteropTransform.getPropertyMapping(
            parentTransform,
            queryOptions.collection.property,
        );
        if (!collectionMapping) {
            throw new Error(
                `${transform.remoteAppName}: Collection mapping for ${parentFactory.name}.${queryOptions.collection.property.name} not found`,
            );
        }

        if (collectionMapping.kind !== 'path') {
            throw new Error(
                `${transform.remoteAppName}: Collection mapping for ${parentFactory.name}.${queryOptions.collection.property.name} not a path`,
            );
        }

        const collectionPropertyName = collectionMapping.value;

        const parentFilter = `"{\\"_id\\":\\"#${remoteNaturalKey}\\"}"`;

        const query = `{
                    ${remoteParentPackageName} {
                        ${remoteParentNodeName} {
                            query(filter: ${parentFilter}) {
                                edges {
                                    node {
                                        ${collectionPropertyName} {
                                            queryAggregate${querArgs} {
                                                ${queryBody}
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }`;

        logger.verbose(() => `Querying remote node ${query}`);

        const result = await this.fetch<FirstQueryResult>(context, query);

        InteropNodeQueryClient.checkNodeQueryResultForErrors(context, result);

        const data = result.data.data as FirstQueryResult;

        logger.verbose(() => `Remote collection query result ${JSON.stringify(data, null, 2)}`);

        const parentResult = data?.[remoteParentPackageName]?.[remoteParentNodeName]?.query?.edges?.[0]?.node;

        const queryResult = parentResult?.[collectionPropertyName] as QueryResult;

        return InteropNodeQueryClient.transformAggregateQueryResult(
            context,
            result,
            queryResult,
            transform,
            queryOptions,
        );
    }

    async queryAggregateRemote<T extends Node>(
        context: Context,
        transform: NodeTransformData,
        queryOptions?: RemoteQueryOptions<T>,
    ): Promise<QueryReturnType> {
        if (!queryOptions?.aggregate) throw new Error('Aggregate not defined');

        const querArgs = await InteropNodeQueryClient.getQueryArgs(context, transform, queryOptions);

        const queryBody = await InteropNodeQueryClient.getAggregateQueryBody(context, transform, queryOptions);

        if (queryOptions?.collection) {
            return this.queryRemoteCollection(context, transform, queryOptions, querArgs, queryBody);
        }

        const remoteNodeInfo = await this.getRemoteNodeInfo(context);
        const packageName = camelCase(remoteNodeInfo.packageName.split('/').slice(-1)[0] ?? '');
        const nodeName = camelCase(transform.remoteNodeName);

        const query = `{
                ${packageName} {
                    ${nodeName} {
                        queryAggregate${querArgs} {
                            ${queryBody}
                        }
                    }
                }
        } `;

        logger.verbose(() => `Querying remote node ${query}`);

        const result = await this.fetch<FirstQueryResult>(context, query);

        InteropNodeQueryClient.checkNodeQueryResultForErrors(context, result);

        const data = result.data.data as FirstQueryResult;

        logger.verbose(() => `Remote aggregate query result ${JSON.stringify(data, null, 2)}`);

        const queryResult = data[packageName][nodeName];
        return {
            payload: await InteropNodeQueryClient.transformAggregateQueryResult(
                context,
                result,
                queryResult,
                transform,
                queryOptions,
            ),
            pageInfo: queryResult.queryAggregate?.pageInfo ?? { hasNextPage: false },
        };
    }

    static async transformQueryResult<T extends Node>(
        context: Context,
        queryResult: QueryResult,
        transform: NodeTransformData,
        queryOptions?: RemoteQueryOptions<T>,
    ): Promise<AnyRecord[]> {
        if (!queryOptions?.count && queryResult.query?.edges == null) {
            return [];
        }

        const fullData = queryOptions?.count
            ? [queryResult.query ?? { totalCount: 0 }]
            : await asyncArray(queryResult.query?.edges ?? [])
                  .map(async edge => {
                      const transformedEdge = await InteropTransform.transformRemoteNodeResult(transform, edge.node);

                      return InteropTransform.transformInboundNodeValues(context, transform, transformedEdge);
                  })
                  .toArray();

        logger.verbose(() => `Transformed query result ${JSON.stringify(fullData, null, 2)}`);
        if (queryResult.query?.pageInfo)
            logger.verbose(() => `Page Info ${JSON.stringify(queryResult.query?.pageInfo, null, 2)}`);

        return fullData ?? [];
    }

    async queryRemoteCollection(
        context: Context,
        transform: NodeTransformData,
        queryOptions: RemoteQueryOptions<Node>,
        querArgs: string,
        queryBody: string,
    ): Promise<QueryReturnType> {
        if (!queryOptions.collection) throw new Error('Collection not defined');

        const parentFactory = queryOptions.collection.sourceNode.$.factory;
        const parentNode = queryOptions.collection.sourceNode;
        const remoteNaturalKey = await InteropTransform.getRemoteNaturalValue(
            this.metadataClient,
            transform.remoteAppVersion,
            parentNode,
        );
        const parentClient = new InteropNodeQueryClient({
            remoteAppName: this.remoteAppName,
            remoteInteropPackageName: this.remoteInteropPackageName,
            remoteIsConnector: this.remoteIsConnector,
            localFactory: parentFactory,
        });

        const parentRemoteNodeInfo = await parentClient.getRemoteNodeInfo(context);
        const parentTransform = await InteropTransform.findNodeTransform(context, parentFactory);
        const remoteParentPackageName = camelCase(parentRemoteNodeInfo.packageName.split('/').pop() ?? '');
        const remoteParentNodeName = camelCase(parentTransform.remoteNodeName);
        const collectionMapping = InteropTransform.getPropertyMapping(
            parentTransform,
            queryOptions.collection.property,
        );
        if (!collectionMapping) {
            throw new Error(
                `${transform.remoteAppName}: Collection mapping for ${parentFactory.name}.${queryOptions.collection.property.name} not found`,
            );
        }

        if (collectionMapping.kind !== 'path') {
            throw new Error(
                `${transform.remoteAppName}: Collection mapping for ${parentFactory.name}.${queryOptions.collection.property.name} not a path`,
            );
        }

        const collectionPropertyName = collectionMapping.value;

        const parentFilter = `"{\\"_id\\":\\"#${remoteNaturalKey}\\"}"`;

        const query = `{
                    ${remoteParentPackageName} {
                        ${remoteParentNodeName} {
                            query(filter: ${parentFilter}) {
                                edges {
                                    node {
                                        ${collectionPropertyName} {
                                            query${querArgs} {
                                                ${queryBody}
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }`;

        logger.verbose(() => `Querying remote node ${query}`);

        const result = await this.fetch<FirstQueryResult>(context, query);

        InteropNodeQueryClient.checkNodeQueryResultForErrors(context, result);

        const data = result.data.data as FirstQueryResult;

        logger.verbose(() => `Remote collection query result ${JSON.stringify(data, null, 2)}`);

        if (
            data[remoteParentPackageName][remoteParentNodeName].query == null ||
            data[remoteParentPackageName][remoteParentNodeName].query?.edges.length === 0
        ) {
            return { payload: [], pageInfo: { hasNextPage: false } };
        }

        const parentResult = data[remoteParentPackageName][remoteParentNodeName].query?.edges?.[0]?.node;

        const queryResult = parentResult?.[collectionPropertyName] as QueryResult;

        return {
            payload: await InteropNodeQueryClient.transformQueryResult(context, queryResult, transform, queryOptions),
            pageInfo: queryResult.query?.pageInfo ?? { hasNextPage: false },
        };
    }

    async queryRemote<T extends Node>(
        context: Context,
        transform: NodeTransformData,
        queryOptions?: RemoteQueryOptions<T>,
    ): Promise<QueryReturnType> {
        if (queryOptions?.aggregate) {
            return this.queryAggregateRemote(context, transform, queryOptions);
        }
        const querArgs = await InteropNodeQueryClient.getQueryArgs(context, transform, queryOptions);
        const remoteNodeInfo = await this.getRemoteNodeInfo(context);
        const queryBody = await InteropNodeQueryClient.getQueryBody(
            context,
            transform,
            remoteNodeInfo,
            this.metadataClient,
            queryOptions,
        );

        if (queryOptions?.collection) {
            return this.queryRemoteCollection(context, transform, queryOptions, querArgs, queryBody);
        }

        const packageName = camelCase(remoteNodeInfo.packageName.split('/').slice(-1)[0] ?? '');
        const nodeName = camelCase(transform.remoteNodeName);

        const query = `{
                ${packageName} {
                    ${nodeName} {
                        query${querArgs} {
                            ${queryBody}
                        }
                    }
                }
            }`;

        logger.verbose(() => `Querying remote node ${query}`);

        const result = await this.fetch<FirstQueryResult>(context, query);

        InteropNodeQueryClient.checkNodeQueryResultForErrors(context, result);

        const data = result.data.data as FirstQueryResult;

        logger.verbose(() => `Remote query result ${JSON.stringify(data, null, 2)}`);

        const queryResult = data[packageName][nodeName];

        return {
            payload: await InteropNodeQueryClient.transformQueryResult(context, queryResult, transform, queryOptions),
            pageInfo: queryResult.query?.pageInfo ?? { hasNextPage: false },
        };
    }
}
