import {
    Context,
    Diagnose,
    GraphQlResponse,
    Node,
    ValidationContext,
    ValidationSeverity,
    asyncArray,
} from '@sage/xtrem-core';
import { ExecutionResult } from 'graphql';
import { camelCase } from 'lodash';
import { RemoteNodeInfo, RemoteNodeProperty } from '../nodes';
import { InteropBaseNodeClient, logger } from './interop-base-node-client';
import { Mappings, Payload } from './interop-mapper';
import { InteropTransform, NodeTransformData } from './interop-transform';
import { MetadataClient } from './metadata-client';

export class InteropNodeMutationClient extends InteropBaseNodeClient {
    static async transformInputPropertyData(
        context: Context,
        metadataClient: MetadataClient,
        property: RemoteNodeProperty,
        value: any,
    ): Promise<string> {
        const { name } = property;

        if (value === null) return `${name}: null`;

        if (property.type === 'reference') {
            if (typeof value === 'string') return `${name}: ${JSON.stringify(value)}`;
            if (!property.targetNode)
                throw new Error(`${metadataClient.remoteAppName}: Reference property ${name} has no target node`);
            const targetRemoteNodeInfo = await metadataClient.findNodeInfo(context, property.targetNode);
            return `${name}: ${await this.transformInputData(context, metadataClient, targetRemoteNodeInfo, value)}`;
        }

        if (['collection', 'referenceArray'].includes(property.type) && Array.isArray(value)) {
            if (!property.targetNode)
                throw new Error(
                    `${metadataClient.remoteAppName}: ${property.type} property ${name} has no target node`,
                );
            const targetRemoteNodeInfo = await metadataClient.findNodeInfo(context, property.targetNode);
            return `${name}: [${await asyncArray(value)
                .map(v => this.transformInputData(context, metadataClient, targetRemoteNodeInfo, v))
                .join(', ')}]`;
        }

        if (property.type === 'json') {
            return `${name}: ${this.stringifyObjectParameter(value)}`;
        }

        if (property.type === 'enum' || property.type === 'string') {
            return `${name}: ${JSON.stringify(value)}`;
        }

        if ((property.type === 'enumArray' || property.type === 'stringArray') && Array.isArray(value)) {
            return `${name}: [${value.map(v => JSON.stringify(v)).join(', ')}]`;
        }

        if (property.type === 'binaryStream' || property.type === 'textStream') {
            return `${name}: { value: ${JSON.stringify(value.value)} }`;
        }

        if (property.type.endsWith('Array')) {
            return `${name}: ${JSON.stringify(value)}`;
        }

        if (property.type === 'decimalArray' && Array.isArray(value)) {
            return `${name}: [${value.map(v => JSON.stringify(String(v))).join(', ')}]`;
        }

        if (['decimal', 'date', 'datetime', 'time'].includes(property.type) || property.type.endsWith('Range')) {
            return `${name}: ${JSON.stringify(String(value))}`;
        }

        return `${name}: ${String(value)}`;
    }

    static async transformInputData(
        context: Context,
        metadataClient: MetadataClient,
        remoteNodeInfo: RemoteNodeInfo,
        data: any,
    ): Promise<string> {
        if (data === null) return 'null';
        const propertyValues = await asyncArray(Object.keys(data))
            .map(k => {
                const value = data[k];
                if (k === '_action') return `${k}: ${JSON.stringify(value)}`;

                const property = remoteNodeInfo.properties.find(p => p.name === k);

                if (!property) {
                    throw new Error(
                        `${metadataClient.remoteAppName}: Property ${remoteNodeInfo}.${k} not found in remote node`,
                    );
                }

                return this.transformInputPropertyData(context, metadataClient, property, value);
            })
            .toArray();

        return `{ ${propertyValues.join(', ')} }`;
    }

    private async getSelector(
        context: Context,
        transform: NodeTransformData,
        remoteNodeInfo: RemoteNodeInfo,
    ): Promise<string> {
        const selectorMappings = this.localFactory.properties
            .filter(prop => {
                const mapping = InteropTransform.getPropertyMapping(transform, prop);
                return (
                    prop.isStored &&
                    !prop.isVital &&
                    !prop.isCollectionProperty() &&
                    mapping != null &&
                    mapping.kind === 'path'
                );
            })
            .reduce((r, prop) => {
                const mapping = InteropTransform.getPropertyMapping(transform, prop);
                if (!mapping) {
                    throw this.localFactory.logicError(`Mapping not found for natural key ${prop.name}`);
                }

                r[prop.name] = mapping;
                return r;
            }, {} as Mappings);

        const selector = await InteropTransform.getRemoteNodeMappingsSelector(
            context,
            this.localFactory,
            selectorMappings,
            transform.remoteAppName,
            transform.remoteAppVersion,
        );

        return InteropTransform.transformSelectorForQuery(context, remoteNodeInfo, selector, this.metadataClient);
    }

    static checkNodeOperationResultForErrors(
        result: GraphQlResponse<ExecutionResult<Payload>>,
        cx: ValidationContext,
    ): boolean {
        const errors = result.data.errors;

        if (errors) {
            // TODO: review if this is the correct way to handle errors
            // should we log the remote error and just throw a generic error?
            // what do we do with the remote paths in the diagnoses?
            if (Array.isArray(errors)) {
                errors.forEach(err => {
                    cx.addError(err.message);
                    if (err.extensions?.diagnoses && Array.isArray(err.extensions.diagnoses)) {
                        err.extensions.diagnoses.forEach((d: Diagnose) => {
                            switch (d.severity) {
                                case ValidationSeverity.exception:
                                case ValidationSeverity.error:
                                    cx.addError(d.message);
                                    break;
                                case ValidationSeverity.warn:
                                    cx.addWarning(d.message);
                                    break;
                                default:
                                    cx.addInfo(d.message);
                                    break;
                            }
                        });
                    }
                });
            }
            return false;
        }

        return true;
    }

    async create(node: Node, cx: ValidationContext): Promise<Payload> {
        const context = node.$.context;
        const transform = await InteropTransform.findNodeTransform(context, this.localFactory);
        const remoteNodeInfo = await this.getRemoteNodeInfo(context);
        const packageName = camelCase(remoteNodeInfo.packageName.split('/').slice(-1)[0] ?? '');
        const nodeName = camelCase(transform.remoteNodeName);

        const nodeCreateData = await InteropTransform.transformOutboundNodeValues(
            context,
            transform,
            node,
            this.metadataClient,
        );

        if (!remoteNodeInfo.naturalKey) {
            throw new Error(`${transform.remoteAppName}: Node ${transform.remoteNodeName} has no natural key`);
        }

        if (!this.localFactory.naturalKey) throw this.logicError('Local factory has no natural key');

        const selectorStr = await this.getSelector(context, transform, remoteNodeInfo);

        const query = `mutation {
                            ${packageName}  {
                                ${nodeName} {
                                    create(data: ${await InteropNodeMutationClient.transformInputData(context, this.metadataClient, remoteNodeInfo, nodeCreateData)}) {
                                        ${selectorStr}
                                    }
                                }
                            }
                        }`;

        logger.verbose(() => `mutation(create) \n ${query}`);

        const result = await this.fetch<Payload>(context, query);

        logger.verbose(() => `mutation(create) raw result \n${JSON.stringify(result.data)}`);

        const checkErrors = InteropNodeMutationClient.checkNodeOperationResultForErrors(result, cx);

        if (!checkErrors) {
            throw new Error(`${transform.remoteAppName}: Error creating remote node`);
        }

        const data = result.data.data;

        if (!data?.[packageName]?.[nodeName]?.create) {
            throw new Error(`${transform.remoteAppName}: Error creating remote node`);
        }
        const createResult = data[packageName][nodeName].create;

        const transformed = await InteropTransform.transformRemoteNodeResult(transform, createResult);

        return InteropTransform.transformInboundNodeValues(context, transform, transformed);
    }

    async update(node: Node, cx: ValidationContext): Promise<Payload> {
        const context = node.$.context;
        const transform = await InteropTransform.findNodeTransform(context, this.localFactory);
        const remoteNodeInfo = await this.getRemoteNodeInfo(context);
        const packageName = camelCase(remoteNodeInfo.packageName.split('/').slice(-1)[0] ?? '');
        const nodeName = camelCase(transform.remoteNodeName);

        const nodeUpdateData = await InteropTransform.transformOutboundNodeValues(
            context,
            transform,
            node,
            this.metadataClient,
        );

        nodeUpdateData._id = `#${await InteropTransform.getRemoteNaturalValue(this.metadataClient, transform.remoteAppVersion, node)}`;

        if (!remoteNodeInfo.naturalKey) {
            throw new Error(`${transform.remoteAppName}: Node ${transform.remoteNodeName} has no natural key`);
        }

        if (!this.localFactory.naturalKey) throw this.logicError('Local factory has no natural key');

        const selectorStr = await this.getSelector(context, transform, remoteNodeInfo);

        const query = `mutation {
                            ${packageName}  {
                                ${nodeName} {
                                    update(data: ${await InteropNodeMutationClient.transformInputData(context, this.metadataClient, remoteNodeInfo, nodeUpdateData)}) {
                                        ${selectorStr}
                                    }
                                }
                            }
                        }`;

        logger.verbose(() => `mutation(update) \n ${query}`);

        const result = await this.fetch<Payload>(context, query);

        logger.verbose(() => `mutation(update) raw result \n${JSON.stringify(result.data)}`);

        const checkErrors = InteropNodeMutationClient.checkNodeOperationResultForErrors(result, cx);

        if (!checkErrors) {
            throw new Error(`${transform.remoteAppName}: Error updating remote node`);
        }

        const data = result.data.data;

        if (!data?.[packageName]?.[nodeName]?.update) {
            throw new Error(`${transform.remoteAppName}: Error updating remote node`);
        }
        const updateResult = data[packageName][nodeName].update;

        const transformed = await InteropTransform.transformRemoteNodeResult(transform, updateResult);

        return InteropTransform.transformInboundNodeValues(context, transform, transformed);
    }

    async delete(node: Node, cx: ValidationContext): Promise<number> {
        const context = node.$.context;
        const transform = await InteropTransform.findNodeTransform(context, this.localFactory);
        const remoteNodeInfo = await this.getRemoteNodeInfo(context);
        const packageName = camelCase(remoteNodeInfo.packageName.split('/').slice(-1)[0] ?? '');
        const nodeName = camelCase(transform.remoteNodeName);

        const remoteNaturalKey = await InteropTransform.getRemoteNaturalValue(
            this.metadataClient,
            transform.remoteAppVersion,
            node,
        );

        const query = `mutation {
                            ${packageName}  {
                                ${nodeName} {
                                    delete(_id: "#${remoteNaturalKey}")
                                }
                            }
                        }`;

        logger.verbose(() => `mutation(delete) \n ${query}`);

        const result = await this.fetch<Payload>(context, query);

        logger.verbose(() => `mutation(delete) raw result \n${JSON.stringify(result.data)}`);

        const checkErrors = InteropNodeMutationClient.checkNodeOperationResultForErrors(result, cx);

        if (!checkErrors) {
            return 0;
        }

        const data = result.data.data;

        if (!data?.[packageName]?.[nodeName]?.delete) {
            throw new Error(`${transform.remoteAppName}: Error deleting remote node`);
        }
        const deleteResult = data[packageName][nodeName].delete;

        return deleteResult ? Number(deleteResult) : 0;
    }
}
