import {
    AnyValue,
    AsyncResponse,
    ConfigManager,
    Context,
    CoreHooks,
    Dict,
    NodeFactory,
    Property,
    ValidationContext,
    asyncArray,
    getAsync,
} from '@sage/xtrem-core';
import { Interpreter } from '@sage/xtrem-js';
import * as _ from 'lodash';
import { RemoteNodeInfo, RemoteNodeProperty } from '../nodes';
import { logger } from './interop-base-node-client';
import { InteropTransform } from './interop-transform';
import { MetadataClient } from './metadata-client';

export type Payload = Dict<any>;

/**
 * A remote path string, in dot notation. For example 'lines..item.name'
 * Arrays are followed by '..', and the index is not specified.
 */
export type PathString = string;

export type MappingKind = 'path' | 'constant' | 'function';

export type ValueMapType = (value: AnyValue) => AnyValue;
export type GetValuesType = (value: AnyValue) => AnyValue[];

export type Mapping = {
    kind: MappingKind;
    value: string;
    valueMap?: ValueMapType;
    getValues?: GetValuesType;
};

export type Selector = Dict<Selector> | true;

/**
 * A set of mappings for the transform
 * The keys are the local paths, in dot notation.
 * The values are a Mapping object, which can be: a path, a constant, or a function.
 *
 * For example, assuming that the remote item data is normalized in the remote payload (we get it from an 'item' reference),
 * and denormalized into the local payload (we want to store it directly under 'lines', as 'productCode' and 'productName'):
 * {
 *  'lines.productName': { kind: 'path', value: 'lines.item.name' },
 *  'lines.productCode': { kind: 'path', value: 'lines.item.code' },
 * }
 * Note: in this example, the 'lines' collection has the same name in the remote and local payloads, but they could have different names.
 */
export type Mappings = {
    [path: PathString]: Mapping;
};

export class InteropMapper {
    private static async getValueFromPath(payload: Payload, path: string[]): Promise<any> {
        const [key, ...rest] = path;
        // We have encountered an array indicator at the start of the path, below we handle the array case in the path
        if (key === '') {
            if (!Array.isArray(payload)) throw new Error(`Expected an array at path ${key}`);
            return asyncArray(payload)
                .map(item => this.getValueFromPath(item, rest))
                .toArray();
        }
        const value = await getAsync(payload, key);
        if (rest.length === 0) return value;
        if (value == null) return value;
        // If the next element in the path is an array indicator, we handle the array case within the path
        if (rest[0] === '') {
            if (!Array.isArray(value)) throw new Error(`Expected an array at path ${key}`);
            return asyncArray(value)
                .map(item => {
                    // If the remainder of the path after the '' is empty, return the item itself
                    const [, ...arrayRest] = rest;
                    if (arrayRest.length === 0) return item;
                    return this.getValueFromPath(item, arrayRest);
                })
                .toArray();
        }
        if (typeof value !== 'object') throw new Error(`Expected an object at path ${key}`);
        return this.getValueFromPath(value, rest);
    }

    private static getValue(payload: Payload, mapping: Mapping): AsyncResponse<any> {
        switch (mapping.kind) {
            case 'path': {
                const valuePath = mapping.value.split('.');
                return this.getValueFromPath(payload, valuePath);
            }
            case 'constant':
                return mapping.value;
            case 'function': {
                try {
                    const codeExecutionResult = Interpreter.safeEval(mapping.value, {
                        timeout: ConfigManager.current?.security?.jsEval?.timeoutInMillis ?? 500,
                        sandbox: { payload },
                    });
                    return codeExecutionResult.value;
                } catch (error) {
                    // If the code execution fails, we return undefined
                    // We don't want to break the transform.
                    logger.verbose(() => `Error executing function mapping: ${mapping.value}, ${error}`);
                    return undefined;
                }
            }
            default:
                throw new Error(`Unknown mapping kind: ${mapping.kind}`);
        }
    }

    private static convertValueToPath(value: Payload, path: string[], result: Dict<any>): Dict<any> {
        const [key, ...rest] = path;
        if (value == null) return result;

        // The first element in the path is the array indicator, below we handle the array case in the path
        if (key === '') {
            if (!Array.isArray(value)) throw new Error(`Expected an array at path ${key}`);
            if (!Array.isArray(result)) {
                // eslint-disable-next-line no-param-reassign
                result = [];
            }

            return _.merge(
                result,
                value.map((item, i) => {
                    return this.convertValueToPath(item, rest, result[i] || {});
                }),
            );
        }

        if (rest.length === 0) {
            if (result[key] !== undefined) throw new Error(`Duplicate key ${key}`);
            result[key] = value;
            return result;
        }

        // If the next element in the path is an array indicator, we handle the array case within the path
        if (rest[0] === '') {
            if (value === undefined) return result;
            if (!Array.isArray(value)) throw new Error(`Expected an array at path ${key}`);
            result[key] = value.map((item, i) => {
                const [, ...arrayRest] = rest;
                if (arrayRest.length === 0) return item;
                return this.convertValueToPath(item, arrayRest, result[key] ? result[key][i] : {});
            });
            return result;
        }
        if (result[key] && typeof result[key] !== 'object') throw new Error(`Expected an object at path ${key}`);
        result[key] = this.convertValueToPath(value, rest, result[key] || {});
        return result;
    }

    private static fillConstants(result: Dict<any>, path: string[], value: any): void {
        const [key, ...rest] = path;
        if (rest.length === 0) {
            if (result[key] !== undefined) throw new Error(`Duplicate key ${key}`);
            result[key] = value;
            return;
        }

        // We have more path to process, so we ensure the key in the result exists
        result[key] ??= {};

        // Next element in the path is a collection indicator, we handle the array case within the path
        if (rest[0] === '') {
            if (!Array.isArray(result[key])) throw new Error(`Expected an array at path ${key}`);
            result[key].forEach((item: any) => this.fillConstants(item, rest.slice(1), value));
            return;
        }
        if (typeof result[key] !== 'object') throw new Error(`Expected an object at path ${key}`);
        this.fillConstants(result[key], rest, value);
    }

    static async applyMappings(payload: Payload, mappings: Mappings): Promise<Payload> {
        let result = {} as Payload;
        await asyncArray(
            Object.entries(mappings).filter(([, mapping]) => ['path', 'function'].includes(mapping.kind)),
        ).forEach(async ([pathStr, mapping]) => {
            let value = await this.getValue(payload, mapping);

            if (mapping.valueMap) {
                value = mapping.valueMap(value);
            }

            result = this.convertValueToPath(value, pathStr.split('.'), result);
        });

        Object.entries(mappings)
            .filter(([, mapping]) => mapping.kind === 'constant')
            .forEach(([pathStr, mapping]) => {
                this.fillConstants(result, pathStr.split('.'), mapping.value);
            });
        return result;
    }

    private static prepareMapping(factory: NodeFactory, path: string[]): string {
        const [key, ...rest] = path;
        if (rest.length === 0) return key;
        const property = factory.findProperty(key);
        if (property.isCollectionProperty()) {
            return `${key}..${this.prepareMapping(property.targetFactory, rest[0] === '' ? rest.slice(1) : rest)}`;
        }
        if (rest[0] === '') throw new Error(`Expected a collection at path ${key}`);
        if (property.isReferenceProperty()) {
            return `${key}.${this.prepareMapping(property.targetFactory, rest)}`;
        }
        return `${key}.${this.prepareMapping(factory, rest)}`;
    }

    static prepareMappings(factory: NodeFactory, mappings: Mappings): Mappings {
        return _.mapKeys(mappings, (_value, key) => this.prepareMapping(factory, key.split('.'))) as Mappings;
    }

    private static verifyNaturalKeyPath(cx: ValidationContext, factory: NodeFactory, path: string[]): void {
        const [name, ...rest] = path;
        const naturalKey = factory.naturalKey;
        if (!naturalKey) {
            cx.addError(`Invalid transform mapping: natural key missing on ${factory.name}`);
            return;
        }

        const property = factory.findProperty(name);
        if (rest.length > 0) {
            if (!property.isReferenceProperty()) {
                cx.addError(`Invalid transform mapping: ${name} is not a reference property`);
                return;
            }
            this.verifyNaturalKeyPath(cx, property.targetFactory, rest);
        }
    }

    /**
     * Verifies that the mapping is valid.
     * @param cx the validation context
     * @param factory the local factory at the current level
     * @param path the full path of the local property (not modified when we recurse)
     *
     * Note: we don't verify the remote path, because we do not have its metadata.
     */
    private static async verifyPath(cx: ValidationContext, factory: NodeFactory, path: string[]): Promise<void> {
        const [name, ...rest] = path;
        const property = factory.findProperty(name);
        if (property.isForeignNodeProperty()) {
            // Error is thrown if transform does not exist
            await InteropTransform.findNodeTransform(cx.context, property.targetFactory).catch(error => {
                if (rest.length === 0) {
                    throw error;
                }
            });
        }

        if (property.isReferenceProperty()) {
            if (rest.length > 0) {
                this.verifyNaturalKeyPath(cx, property.targetFactory, rest);
            }
        } else if (property.isCollectionProperty()) {
            if (rest.length > 0) {
                cx.addError(
                    `Invalid transform mapping: cannot map to properties of a collection, ${property.targetFactory.name} should have its own mapping.`,
                );
            }
        } else if (property.name === '_customData') {
            const customFields = await CoreHooks.customizationManager.getCustomFields(cx.context, [factory.fullName]);
            if (customFields == null) {
                cx.addError(`Invalid transform mapping: _customData is not supported for ${factory.name}`);
            } else if (rest.length !== 1) {
                cx.addError(
                    `Invalid transform mapping: unexpected property name '${rest.join('.')}' under ${property.fullName}`,
                );
            } else {
                const customField = customFields[factory.fullName].find(field => field.name === rest[0]);
                if (!customField) {
                    cx.addError(`Invalid transform mapping: unknown custom field ${rest[0]}`);
                }
            }
        } else if (rest.length > 0) {
            cx.addError(`Invalid transform mapping: unexpected property name under ${property.fullName}`);
        }
    }

    private static async verifyPathMapping(
        cx: ValidationContext,
        factory: NodeFactory,
        remoteNodeName: string,
        pathStr: string,
        mapping: Mapping,
        metadataClient: MetadataClient,
    ): Promise<void> {
        const localProperty = this.findPropertyFromPath(
            pathStr.split('.').filter(p => p !== ''),
            factory,
        );

        const remotePropertyPath = mapping.value.split('.').filter(p => p !== '');

        if (localProperty.isReferenceProperty()) {
            // Error is thrown if transform does not exist
            if (localProperty.targetFactory.name !== factory.name)
                await InteropTransform.findNodeTransform(cx.context, localProperty.targetFactory);
            const remotePropertyInfo = await this.findRemotePropertyFromPath(
                cx.context,
                remotePropertyPath,
                remoteNodeName,
                metadataClient,
            );
            if (remotePropertyInfo.property.type !== 'reference') {
                if (!localProperty.targetFactory.naturalKey) {
                    cx.addError(
                        `Invalid remote property path: ${mapping.value}. ${localProperty.targetFactory.name} does not have a natural key.`,
                    );
                }

                if (
                    !(
                        localProperty.targetFactory.naturalKey?.length === 1 &&
                        localProperty.targetFactory.findProperty(localProperty.targetFactory.naturalKey[0]).type ===
                            remotePropertyInfo.property.type
                    )
                ) {
                    cx.addError(`Invalid remote property path: ${mapping.value}`);
                }
            }
        }

        if (localProperty.isCollectionProperty()) {
            // Error is thrown if transform does not exist
            if (localProperty.targetFactory.name !== factory.name)
                await InteropTransform.findNodeTransform(cx.context, localProperty.targetFactory);
            const remotePropertyInfo = await this.findRemotePropertyFromPath(
                cx.context,
                remotePropertyPath,
                remoteNodeName,
                metadataClient,
            );

            if (remotePropertyInfo.property.type !== 'collection') {
                cx.addError(`Invalid remote property path: ${mapping.value}`);
            }
        }
    }

    private static async controlMapping(
        cx: ValidationContext,
        factory: NodeFactory,
        remoteNodeName: string,
        pathStr: string,
        mapping: Mapping,
        metadataClient: MetadataClient,
    ): Promise<void> {
        await this.verifyPath(cx, factory, pathStr.split('.'));

        // Basic validation on values
        const keyGroups = pathStr.split('..');
        if (mapping.kind === 'path') {
            const valueGroups = mapping.value.split('..');
            if (valueGroups.length !== keyGroups.length)
                cx.at('mappings').addError(`Different number of arrays in key ${pathStr} and value ${mapping.value}`);
            if (valueGroups.some(group => group === ''))
                cx.at('mappings').addError(`Missing property name in ${mapping.value}`);

            await this.verifyPathMapping(cx, factory, remoteNodeName, pathStr, mapping, metadataClient);
        }
    }

    static async controlMappings(
        cx: ValidationContext,
        factory: NodeFactory,
        remoteNodeName: string,
        mappings: Mappings,
        metadataClient: MetadataClient,
    ): Promise<void> {
        await Promise.all(
            Object.entries(mappings).map(([pathStr, mapping]) =>
                this.controlMapping(cx, factory, remoteNodeName, pathStr, mapping, metadataClient),
            ),
        );
    }

    /**
     * Using the mappings supplied construct the remote selector
     * @param mappings
     * @param setSelector optional callback to set the selector
     * @returns
     */
    static async getMappingsSelector(
        mappings: Mappings,
        setSelector?: (localPath: string, remotePath: string, result: Dict<Selector>) => AsyncResponse<void>,
    ): Promise<Selector> {
        const pathMappings = Object.entries(mappings).filter(entry => entry[1].kind === 'path');
        if (pathMappings.length === 0) return true;

        const result = {} as Dict<Selector>;
        await asyncArray(pathMappings).forEach(async entry => {
            const [keyPath, mapping] = entry;
            const localPath = keyPath.replace(/\.\./g, '.').replace(/^\./, '');
            const remotePath = mapping.value.replace(/\.\./g, '.').replace(/^\./, '');
            if (setSelector) {
                await setSelector(localPath, remotePath, result);
            } else {
                _.set(result, remotePath, true);
            }
        });

        return result;
    }

    static formatSelector(selector: Selector): string {
        if (selector === true) return '';
        const inner = Object.entries(selector)
            .map(([key, item]) => `${key}${this.formatSelector(item)}`)
            .join(' ');
        return `{ ${inner} }`;
    }

    static findPropertyFromPath(localPath: string[], targetFactory: NodeFactory, pathSoFar: string[] = []): Property {
        const [name, ...rest] = localPath;
        pathSoFar.push(name);
        const property = targetFactory.findProperty(name);
        if (rest.length === 0) return property;
        if (property.isForeignNodeProperty()) {
            return this.findPropertyFromPath(rest, property.targetFactory, pathSoFar);
        }
        if (property.isJsonProperty()) return property;

        throw new Error(`Invalid path: ${pathSoFar.join('.')}`);
    }

    static async findRemotePropertyFromPath(
        context: Context,
        remotePath: string[],
        remoteNodeName: string,
        metadataClient: MetadataClient,
        pathSoFar: string[] = [],
    ): Promise<{ property: RemoteNodeProperty; remoteNodeInfo: RemoteNodeInfo }> {
        const [name, ...rest] = remotePath;
        pathSoFar.push(name);
        const remoteNodeInfo = await metadataClient.findNodeInfo(context, remoteNodeName);
        const property = remoteNodeInfo.properties.find(p => p.name === name);
        if (!property) {
            throw new Error(`Invalid remote property path: ${pathSoFar.join('.')}`);
        }
        if (rest.length === 0) return { property, remoteNodeInfo };
        if (property.type === 'reference') {
            if (!property.targetNode) {
                throw new Error(`Invalid remote property path: ${pathSoFar.join('.')}. Target node missing.`);
            }
            return this.findRemotePropertyFromPath(context, rest, property.targetNode, metadataClient, pathSoFar);
        }

        if (property.type === 'collection') {
            if (!property.targetNode) {
                throw new Error(`Invalid remote property path: ${pathSoFar.join('.')}. Target node missing.`);
            }
            if (rest[0] !== '') {
                throw new Error(
                    `Invalid remote property path: ${pathSoFar.join('.')}. Invalid structure of collection path.`,
                );
            }
            return this.findRemotePropertyFromPath(context, rest, property.targetNode, metadataClient, pathSoFar);
        }

        if (property.type === 'json') return { remoteNodeInfo, property };

        if (rest.length > 0) throw new Error(`Invalid path: ${pathSoFar.join('.')}, remaining path ${rest.join('.')}`);

        return { remoteNodeInfo, property };
    }
}
