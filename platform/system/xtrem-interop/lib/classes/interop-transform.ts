import {
    AnyR<PERSON>ord,
    AnyValue,
    AsyncResponse,
    BaseCollection,
    BinaryStream,
    ConfigManager,
    Context,
    DateValue,
    Datetime,
    Decimal,
    Dict,
    EnumDataType,
    ForeignNodeProperty,
    InteropAppHealthMonitor,
    LocalizeLocale,
    Node,
    NodeFactory,
    Property,
    PropertyAndValue,
    TextStream,
    Uuid,
    asyncArray,
} from '@sage/xtrem-core';
import { DateRange, DatetimeRange, Time } from '@sage/xtrem-date-time';
import { Interpreter } from '@sage/xtrem-js';
import * as _ from 'lodash';
import * as semver from 'semver';
import { NodeMappingKind } from '../enums';
import { RemoteNodeInfo, RemoteNodeProperty, SysEnumTransformation, SysNodeTransformation } from '../nodes';
import { logger } from './interop-base-node-client';
import { InteropClientOptions } from './interop-client';
import {
    GetValuesType,
    InteropMapper,
    Mapping,
    MappingKind,
    Mappings,
    Payload,
    Selector,
    ValueMapType,
} from './interop-mapper';
import { MetadataClient } from './metadata-client';
import { RemoteInfoCache } from './remote-info-cache';

/**
 * Defines how a remote node will be transformed into a local node during synchronization.
 */
export interface NodeTransformData extends InteropClientOptions {
    localFactory: NodeFactory;
    remoteAppVersion: string;
    remoteNodeName: string;
    mappings: Mappings;
    remoteMappings: Mappings;
    filter: AnyRecord | null;
}

export interface EnumTransformData {
    localEnum: string;
    remoteAppName: string;
    remoteAppVersion: string;
    remoteEnum: string;
    localValueMap: ValueMapType;
    remoteValueMap: ValueMapType;
    localGetValues: GetValuesType;
    remoteGetValues: GetValuesType;
}

export interface PathTransformData {
    property: Property;
    factory: NodeFactory;
    localPath: string[];
    remotePath: string[];
    remainingPath?: string[];
}

export class InteropTransform {
    static async getTransformFromList<
        T extends {
            remoteAppVersion: string;
            remoteApp: { name: string; isConnector: boolean; interopPackage: string };
        },
    >(context: Context, transforms: T[], options?: { remoteAppVersion?: string }): Promise<T | null> {
        // Select the transform that matches the remote app version
        const transform = await asyncArray(transforms)
            .filter(async t => {
                // do not include transforms for apps that are not alive to avoid errors
                if (!(await InteropAppHealthMonitor.isAppAlive(t.remoteApp.name))) return false;

                const actualRemoteAppVersion =
                    options?.remoteAppVersion ??
                    (
                        await RemoteInfoCache.fetchRemoteInfo(context, {
                            remoteAppName: t.remoteApp.name,
                            remoteIsConnector: t.remoteApp.isConnector,
                            remoteInteropPackageName: t.remoteApp.interopPackage,
                        })
                    ).remoteAppVersion;

                // Version is a range so we use satifies to verify if the version is in the range
                if (!semver.valid(t.remoteAppVersion)) {
                    return semver.satisfies(actualRemoteAppVersion, t.remoteAppVersion);
                }

                //  We have solid version A.B.C so we compare the version to see if it is less than the actual remote app version
                return semver.lte(t.remoteAppVersion, actualRemoteAppVersion);
            })
            .sort((a, b) => {
                const aVersion = !semver.valid(a.remoteAppVersion)
                    ? semver.minVersion(a.remoteAppVersion)
                    : a.remoteAppVersion;
                const bVersion = !semver.valid(b.remoteAppVersion)
                    ? semver.minVersion(b.remoteAppVersion)
                    : b.remoteAppVersion;
                if (aVersion == null || bVersion == null) {
                    // One of the versions is not valid so we cannot compare them,
                    // we will return 1 to make sure we do not select this transform
                    return 1;
                }

                // We want to sort in descending order so we switch the order of the comparison
                // to get the latest version first
                return semver.compare(bVersion, aVersion);
            })
            .at(0);

        if (transform) {
            const remoteAppVersion =
                options?.remoteAppVersion ??
                (
                    await RemoteInfoCache.fetchRemoteInfo(context, {
                        remoteAppName: transform.remoteApp.name,
                        remoteIsConnector: transform.remoteApp.isConnector,
                        remoteInteropPackageName: transform.remoteApp.interopPackage,
                    })
                ).remoteAppVersion;

            Object.assign(transform, { remoteAppVersion });
        }

        return transform ?? null;
    }

    static async findEnumTransform(
        context: Context,
        localEnum: string,
        localFactory: NodeFactory,
        options: { remoteAppName: string; remoteAppVersion: string },
    ): Promise<EnumTransformData> {
        // Get all transforms for the remote app and local enum
        const transforms = await context.select(
            SysEnumTransformation,
            {
                remoteApp: { name: true, isConnector: true, interopPackage: true },
                remoteAppVersion: true,
                remoteEnum: true,
                map: { localEnumValue: true, remoteEnumValue: true },
            },
            {
                filter: {
                    isActive: true,
                    remoteApp: { name: options.remoteAppName },
                    localEnum: { name: localEnum },
                },
            },
        );

        // Select the transform that matches the remote app version
        const selectedTransform = await this.getTransformFromList(context, transforms, {
            remoteAppVersion: options.remoteAppVersion,
        });
        if (!selectedTransform) {
            throw localFactory.systemError(
                `no enum transform(${localEnum}) for remote app ${options.remoteAppName} at version ${options.remoteAppVersion}`,
            );
        }

        const metadataClient = new MetadataClient({
            remoteAppName: options.remoteAppName,
            remoteInteropPackageName: selectedTransform.remoteApp.interopPackage,
            remoteIsConnector: selectedTransform.remoteApp.isConnector,
        });

        const remoteEnum = await metadataClient.getDataTypeInfo(context, selectedTransform.remoteEnum);

        if (remoteEnum.length === 0) {
            throw localFactory.systemError(`no remote enum found for ${selectedTransform.remoteEnum}`);
        }

        const enumObj = (remoteEnum[0].attributes as any).enum as Dict<number | string>;

        const localEnumDataType = context.application.dataTypes[localEnum] as EnumDataType;

        return {
            localEnum,
            remoteAppName: options.remoteAppName,
            remoteAppVersion: selectedTransform.remoteAppVersion,
            remoteEnum: selectedTransform.remoteEnum,
            localValueMap: (value: AnyValue) => {
                return selectedTransform.map.find(m => m.remoteEnumValue === value)?.localEnumValue ?? value;
            },
            remoteValueMap: (value: AnyValue) => {
                return selectedTransform.map.find(m => m.localEnumValue === value)?.remoteEnumValue ?? value;
            },
            localGetValues: (value: AnyValue) => {
                return (
                    selectedTransform.map
                        .filter(m => m.remoteEnumValue === value)
                        ?.map(m => m.localEnumValue)
                        .filter(m => {
                            const numVal = localEnumDataType.enumValues[m];
                            return numVal != null;
                        })
                        .sort((a, b) => {
                            const numValA = localEnumDataType.enumValues[a];
                            const numValB = localEnumDataType.enumValues[b];

                            return numValA - numValB;
                        }) ?? []
                );
            },
            remoteGetValues: (value: AnyValue) => {
                return (
                    selectedTransform.map
                        .filter(m => m.localEnumValue === value)
                        ?.map(m => m.remoteEnumValue)
                        .filter(m => {
                            const numVal = enumObj[m];
                            return numVal != null && typeof numVal === 'number';
                        })
                        .sort((a, b) => {
                            const numValA = enumObj[a] as number;
                            const numValB = enumObj[b] as number;

                            return numValA - numValB;
                        }) ?? []
                );
            },
        };
    }

    static async getValueMapping(
        context: Context,
        factory: NodeFactory,
        path: string[],
        remoteAppName: string,
        remoteAppVersion: string,
    ): Promise<
        | {
              localValueMap: ValueMapType;
              remoteValueMap: ValueMapType;
              localGetValues: GetValuesType;
              remoteGetValues: GetValuesType;
          }
        | undefined
    > {
        const [name, ...rest] = path;

        if (rest[0] === '') {
            rest.shift();
        }

        const property = factory.findProperty(name);
        if ((property.isCollectionProperty() || property.isReferenceProperty()) && rest.length > 0) {
            return this.getValueMapping(context, property.targetFactory, rest, remoteAppName, remoteAppVersion);
        }

        if (property.isEnumProperty()) {
            const transform = await this.findEnumTransform(context, property.dataType.name ?? '', factory, {
                remoteAppName,
                remoteAppVersion,
            });
            return {
                localValueMap: transform.localValueMap,
                remoteValueMap: transform.remoteValueMap,
                localGetValues: transform.localGetValues,
                remoteGetValues: transform.remoteGetValues,
            };
        }

        return undefined;
    }

    static constructMappings(
        localFactory: NodeFactory,
        nodeMappings: { kind: NodeMappingKind; localProperty: string; remoteProperty: string }[],
    ): Mappings {
        const mappings = nodeMappings
            .filter(m => m.kind.startsWith('path_to_'))
            .reduce((r, k) => {
                const mapKind = k.kind;
                let kind: MappingKind;
                switch (mapKind) {
                    case 'path_to_path':
                        kind = 'path';
                        break;
                    case 'path_to_constant':
                        kind = 'constant';
                        break;
                    case 'path_to_function':
                        kind = 'function';
                        break;
                    default:
                        throw new Error(`Unknown mapping kind: ${mapKind}`);
                }
                const { localProperty, remoteProperty } = k;
                r[localProperty] = { kind, value: remoteProperty };

                return r;
            }, {} as Mappings);

        if (Object.keys(mappings).length === 0) return mappings;

        return InteropMapper.prepareMappings(localFactory, mappings);
    }

    static constructRemoteMappings(
        nodeMappings: { kind: NodeMappingKind; localProperty: string; remoteProperty: string }[],
    ): Mappings {
        return nodeMappings
            .filter(m => m.kind.endsWith('_to_path'))
            .reduce((r, k) => {
                const mapKind = k.kind;
                let kind: MappingKind;
                switch (mapKind) {
                    case 'path_to_path':
                        kind = 'path';
                        break;
                    case 'constant_to_path':
                        kind = 'constant';
                        break;
                    case 'function_to_path':
                        kind = 'function';
                        break;
                    default:
                        throw new Error(`Unknown mapping kind: ${mapKind}`);
                }
                const { localProperty, remoteProperty } = k;
                if (r[remoteProperty]) {
                    // this can happen when a remote property is mapped to multiple local properties/paths
                    // in this the first mapping will be used as the main remote mapping for the remote property
                    // we will still add the remote mapping to the mappings object, related to the local property/path it is mapped to
                    r[`${remoteProperty}~${localProperty}`] = { kind, value: localProperty };
                } else {
                    r[remoteProperty] = { kind, value: localProperty };
                }

                return r;
            }, {} as Mappings);
    }

    /**
     * Retrieves the synchronization transform for a given context, local factory, and remote app version.
     * @param context The context object.
     * @param localFactory The local factory object.
     * @param options
     * @returns A promise that resolves to a InteropNodeTransformation object.
     * @throws Throws an error if no transform is found or if there are multiple transforms.
     */
    static async findNodeTransform(
        context: Context,
        localFactory: NodeFactory,
        options?: {
            remoteAppName: string;
            remoteAppVersion: string;
        },
    ): Promise<NodeTransformData> {
        const filter: AnyRecord = {
            isActive: true,
            localNode: { name: localFactory.name },
        };

        if (options?.remoteAppName) {
            filter.remoteApp = { name: options.remoteAppName };
        }

        // Get all transforms for the remote app and local node
        const selectResult = await context.select(
            SysNodeTransformation,
            {
                remoteApp: { name: true, isConnector: true, interopPackage: true },
                remoteNodeName: true,
                remoteAppVersion: true,
                map: { kind: true, localProperty: true, remoteProperty: true },
                filter: true,
            },
            {
                filter,
            },
        );

        const transforms = selectResult.map(t => {
            return {
                remoteApp: t.remoteApp,
                remoteNodeName: t.remoteNodeName,
                remoteAppVersion: t.remoteAppVersion,
                mappings: this.constructMappings(localFactory, t.map),
                remoteMappings: this.constructRemoteMappings(t.map),
                filter: t.filter,
            };
        });

        // Select the transform that matches the remote app version
        const selectedTransform = await this.getTransformFromList(context, transforms, options);
        if (!selectedTransform) {
            throw localFactory.systemError(`no transform found for node`);
        }

        selectedTransform.mappings = await asyncArray(Object.keys(selectedTransform.mappings)).reduce(async (r, k) => {
            const value = selectedTransform.mappings[k];
            const valueMapping = await this.getValueMapping(
                context,
                localFactory,
                k.split('.'),
                selectedTransform.remoteApp.name,
                selectedTransform.remoteAppVersion,
            );
            value.valueMap = valueMapping?.localValueMap;
            value.getValues = valueMapping?.localGetValues;
            r[k] = value;
            return r;
        }, {} as Mappings);

        selectedTransform.remoteMappings = await asyncArray(Object.keys(selectedTransform.remoteMappings)).reduce(
            async (r, k) => {
                const value = selectedTransform.remoteMappings[k];
                if (value.kind === 'path') {
                    const valueMapping = await this.getValueMapping(
                        context,
                        localFactory,
                        value.value.split('.'),
                        selectedTransform.remoteApp.name,
                        selectedTransform.remoteAppVersion,
                    );
                    value.valueMap = valueMapping?.remoteValueMap;

                    value.getValues = valueMapping?.remoteGetValues;
                }
                r[k] = value;
                return r;
            },
            {} as Mappings,
        );

        return {
            localFactory,
            remoteAppName: selectedTransform.remoteApp.name,
            remoteAppVersion: selectedTransform.remoteAppVersion,
            remoteNodeName: selectedTransform.remoteNodeName,
            mappings: selectedTransform.mappings,
            remoteMappings: selectedTransform.remoteMappings,
            filter: selectedTransform.filter,
            remoteIsConnector: selectedTransform.remoteApp.isConnector,
            remoteInteropPackageName: selectedTransform.remoteApp.interopPackage,
        };
    }

    static walkPropertyPath(
        context: Context,
        factory: NodeFactory,
        propertyPath: string[],
    ): { property: Property; factory: NodeFactory } {
        const [name, ...rest] = propertyPath;

        const property = factory.findProperty(name);

        if (rest.length === 0) return { property, factory };

        if (property.isForeignNodeProperty()) {
            return this.walkPropertyPath(context, property.targetFactory, rest);
        }

        if (property.isTextStreamProperty() || property.isBinaryStreamProperty()) {
            if (rest.length === 1 && rest[0] === 'value') {
                return { property, factory };
            }
        }

        if (!property.isJsonProperty()) {
            throw new Error(`Invalid property type in path: ${property.name}`);
        }

        return { property, factory };
    }

    static async transformPath(
        context: Context,
        factory: NodeFactory,
        remoteAppName: string,
        remoteAppVersion: string,
        propertyPath: string[],
        prevLocalPath: string[] = [],
        prevRemotePath: string[] = [],
    ): Promise<PathTransformData> {
        const [name, ...rest] = propertyPath;

        const property = factory.findProperty(name);

        const transform = await this.findNodeTransform(context, factory, { remoteAppName, remoteAppVersion });

        const remoteMapping = InteropTransform.getPropertyMapping(transform, property);

        if (!remoteMapping) throw Error(`Missing mapping for ${factory.name}.${name}`);
        if (remoteMapping.kind !== 'path')
            throw Error(`${factory.name}.${name}: property used in path needs to have a path mapping`);

        const remotePath = [...prevRemotePath, ...remoteMapping.value.split('.').filter(p => p !== '')];

        const localPath = [...prevLocalPath, name];

        if (rest.length === 0) return { property, factory, remotePath, localPath };

        if (property.isForeignNodeProperty()) {
            return this.transformPath(
                context,
                property.targetFactory,
                remoteAppName,
                remoteAppVersion,
                rest,
                localPath,
                remotePath,
            );
        }

        if (property.isJsonProperty()) {
            return { property, factory, remotePath, localPath, remainingPath: rest };
        }

        throw new Error(`${factory.name}.${name}: Invalid property type in path ${propertyPath} `);
    }

    static getRemoteNodeMappingsSelector(
        context: Context,
        factory: NodeFactory,
        mappings: Mappings,
        remoteAppName: string,
        remoteAppVersion: string,
    ): Promise<Selector> {
        return InteropMapper.getMappingsSelector(
            mappings,
            async (localPath: string, remotePath: string, result: Dict<Selector>) => {
                const { property } = this.walkPropertyPath(context, factory, localPath.split('.'));
                if (property.isForeignNodeProperty()) {
                    const { targetFactory } = property;

                    const transform = await this.findNodeTransform(context, targetFactory, {
                        remoteAppName,
                        remoteAppVersion,
                    });
                    if (!targetFactory.naturalKey) throw new Error(`${targetFactory.name}: Missing natural key`);
                    const naturalKeyMappings = targetFactory.naturalKey.reduce((r, k) => {
                        const mapping = transform.mappings[k];
                        if (mapping == null) throw Error(`${targetFactory.name}: Missing natural key mapping for ${k}`);
                        r[k] = mapping;
                        return r;
                    }, {} as Mappings);
                    const selector = await this.getRemoteNodeMappingsSelector(
                        context,
                        targetFactory,
                        naturalKeyMappings,
                        remoteAppName,
                        remoteAppVersion,
                    );
                    if (property.isCollectionProperty()) {
                        _.set(result, `${remotePath}.query.edges.node`, selector);
                    } else if (property.isReferenceProperty()) {
                        if (_.has(result, remotePath)) {
                            // The reference is already in the selector so we merge the two selectors
                            const refSelector = _.get(result, remotePath);
                            _.set(result, remotePath, _.merge(refSelector, selector));
                        } else {
                            _.set(result, remotePath, selector);
                        }
                    } else {
                        _.set(result, remotePath, selector);
                    }
                } else {
                    _.set(result, remotePath, true);
                }
            },
        );
    }

    static defaultOrderBy(factory: NodeFactory, direction: 1 | -1): any {
        if (!factory.naturalKey) return { _id: direction };
        return factory.naturalKey.reduce((r, k) => {
            const property = factory.findProperty(k);
            if (property.isReferenceProperty()) {
                r[k] = this.defaultOrderBy(property.targetFactory, direction);
            } else {
                r[k] = direction;
            }
            return r;
        }, {} as AnyRecord);
    }

    static async transformOrderBy(
        context: Context,
        remoteAppName: string,
        remoteAppVersion: string,
        factory: NodeFactory,
        orderBy: any,
        cursorProperties: { property: Property; propertyPath: string; direction: 1 | -1 }[],
        currentPath = '',
    ): Promise<AnyRecord> {
        const transform = await this.findNodeTransform(context, factory, { remoteAppName, remoteAppVersion });
        const remoteOrderBy: AnyRecord = {};
        const orderByCopy = { ...orderBy };
        if ('_id' in orderBy) {
            const defaultOrderBy = this.defaultOrderBy(factory, orderBy._id);
            _.merge(orderByCopy, defaultOrderBy);
            delete orderByCopy._id;
        }
        await asyncArray(Object.keys(orderByCopy)).forEach(async key => {
            const mapping = transform.mappings[key];
            const value = orderByCopy[key];
            if (!mapping) throw Error(`Missing mapping for ${factory.name}.${key}`);
            // Mapping is constant or function so we address it later
            if (mapping.kind !== 'path') return;

            const property = factory.findProperty(key);
            const propertyPath = currentPath ? `${currentPath}.${key}` : key;
            const remotePath = mapping.value;

            if (value === 1 || value === -1) {
                if (property.isReferenceProperty()) {
                    const { targetFactory } = property;
                    const targetOrderBy = await this.transformOrderBy(
                        context,
                        remoteAppName,
                        remoteAppVersion,
                        targetFactory,
                        this.defaultOrderBy(targetFactory, value),
                        cursorProperties,
                        currentPath ? `${currentPath}.${key}` : key,
                    );
                    _.set(remoteOrderBy, remotePath, targetOrderBy);
                } else {
                    _.set(remoteOrderBy, remotePath, value);
                    cursorProperties.push({ property, propertyPath, direction: value });
                }
            } else if (property.isForeignNodeProperty()) {
                const { targetFactory } = property;
                const targetOrderBy = await this.transformOrderBy(
                    context,
                    remoteAppName,
                    remoteAppVersion,
                    targetFactory,
                    value,
                    cursorProperties,
                    currentPath ? `${currentPath}.${key}` : key,
                );
                _.set(remoteOrderBy, remotePath, targetOrderBy);
            }
        });

        return remoteOrderBy;
    }

    static transformValue(value: any, fetcher?: GetValuesType): any[] {
        if (fetcher) {
            const result = fetcher(value);
            if (result === undefined) throw Error(`Missing mapping for ${value}`);
            return result;
        }
        return [value];
    }

    static transformFilterOperator(
        context: Context,
        remoteAppName: string,
        remoteAppVersion: string,
        factory: NodeFactory,
        operator: string,
        value: any,
        mapping?: Mapping,
    ): Promise<any> {
        switch (operator) {
            case '_and':
            case '_or':
            case '_nor': {
                if (!Array.isArray(value)) throw Error(`Invalid value for ${operator}`);
                return asyncArray(value)
                    .map((v: any) => this.transformFilter(context, remoteAppName, remoteAppVersion, factory, v))
                    .filter((v: any) => Object.keys(v).length > 0)
                    .toArray();
            }
            case '_in':
            case '_nin':
                if (!Array.isArray(value)) throw Error(`Invalid value for ${operator}`);
                if (mapping)
                    return Promise.resolve(
                        value.reduce((r, k) => {
                            if (!mapping.getValues) {
                                r.push(k);
                                return r;
                            }
                            const remoteVals = this.transformValue(k, mapping.getValues);
                            r.push(...remoteVals);
                            return r;
                        }, []),
                    );
                return Promise.resolve(value);
            case '_not':
                return this.transformFilter(context, remoteAppName, remoteAppVersion, factory, value);
            case '_atLeast':
            case '_atMost':
            case '_none':
            case '_every':
            case '_regex':
            case '_options':
                return value;
            default:
                if (Array.isArray(value)) {
                    return Promise.resolve(
                        value.reduce((r, k) => {
                            if (!mapping) {
                                r.push(k);
                                return r;
                            }
                            const remoteVals = this.transformValue(k, mapping.getValues);

                            r.push(...remoteVals);
                            return r;
                        }, []),
                    );
                }
                if (mapping) {
                    // if the array returned is more than one value, we manage it in the calling function
                    // e.g. _gt, _lt, _eq, _ne
                    return Promise.resolve(this.transformValue(value, mapping.getValues));
                }
                return value;
        }
    }

    static async parseNodeId(factory: NodeFactory, value: string): Promise<Dict<AnyValue>> {
        if (typeof value === 'string') {
            const result = factory.parseNodeId(value);
            const flatResult = InteropTransform.flattenObject(result);
            const parsedResult = {} as Dict<AnyValue>;
            await asyncArray(Object.keys(flatResult)).forEach(async k => {
                const val = flatResult[k];
                if (val instanceof Node) {
                    _.set(parsedResult, k, await factory.getNaturalKeyValueFromNode(val));
                } else {
                    _.set(parsedResult, k, val);
                }
            });
            return parsedResult;
        }
        throw new Error(`${factory.name}: Cannot parse value ${value}.`);
    }

    static async transformReferenceFilter(
        context: Context,
        remoteAppName: string,
        remoteAppVersion: string,
        metadataClient: MetadataClient,
        targetFactory: NodeFactory,
        value: AnyValue,
    ): Promise<string | AnyRecord | null> {
        if (value == null) {
            return Promise.resolve(null);
        }

        if (targetFactory.storage === 'sql' && Number.isFinite(value)) {
            const node = await context.read(targetFactory.nodeConstructor, { _id: value as number });
            return `#${await this.getRemoteNaturalValue(metadataClient, remoteAppVersion, node)}`;
        }

        if (typeof value === 'string') {
            return this.transformFilter(
                context,
                remoteAppName,
                remoteAppVersion,
                targetFactory,
                await this.parseNodeId(targetFactory, value),
            );
        }

        if (value instanceof Node) {
            return `#${await this.getRemoteNaturalValue(metadataClient, remoteAppVersion, value)}`;
        }

        return this.transformFilter(context, remoteAppName, remoteAppVersion, targetFactory, value as AnyRecord);
    }

    private static transformJsonFilter(
        transform: NodeTransformData,
        filter: any,
        remoteFilter: any,
        key: string,
        value: any,
    ): void {
        const jsonMappings = Object.keys(transform.mappings).filter(mappingKey => mappingKey.startsWith(`${key}.`));
        const jsonFilterTracker: any = {};
        // itterate over all json mappings and set the values in the remote filter
        jsonMappings.forEach(jsonMapping => {
            const jsonValue = _.get(filter, jsonMapping);
            if (jsonValue) {
                const jsonMappingValue = transform.mappings[jsonMapping];
                if (!jsonMappingValue) throw Error(`Missing mapping for ${transform.localFactory.name}.${jsonMapping}`);
                if (jsonMappingValue.kind !== 'path')
                    throw Error(
                        `${transform.localFactory.name}.${jsonMapping}: property used in filter needs to have a path mapping`,
                    );
                const remoteJsonPath = jsonMappingValue.value;
                _.set(remoteFilter, remoteJsonPath, jsonValue);
                _.set(jsonFilterTracker, jsonMapping, jsonValue);
            }
        });

        // if the json filter tracker is not equal to the json filter, then some attributes do not have mappings
        // and we throw.
        if (!_.isEqual(jsonFilterTracker[key], value)) {
            throw new Error(
                `Invalid JSON filter for ${transform.localFactory.name}.${key}, some attribute do not have mappings`,
            );
        }
    }

    static getCursorFilter(
        cursorElements: { property: Property; propertyPath: string; direction: 1 | -1 }[],
        cursorValues: PropertyAndValue[],
        operation: 'after' | 'before',
    ): any {
        /**
         * This algorithm is based on the one we use on a SQL query to translate a cursor to a where clause.
         */
        const parts = cursorElements.map((cursorElement, i) => {
            const ands = [] as any[];
            for (let j = 0; j < i; j += 1) {
                const element = cursorElements[j];
                const { value } = cursorValues[j];
                let eqFilterEntry = _.set({}, `${element.propertyPath}._eq`, value);
                if (element.property.isNullable) {
                    eqFilterEntry = { _or: [eqFilterEntry, _.set({}, `${element.propertyPath}._eq`, null)] };
                }
                ands.push(eqFilterEntry);
            }

            let filterOperation = cursorElement.direction > 0 ? '_gt' : '_lt';
            if (operation === 'before') {
                filterOperation = cursorElement.direction > 0 ? '_lt' : '_gt';
            }

            const mainValue = cursorValues[i].value;
            let mainFilter = _.set({}, `${cursorElement.propertyPath}.${filterOperation}`, mainValue);
            if (cursorElement.property.isNullable) {
                if (filterOperation === '_lt' && mainValue != null && mainValue !== '') {
                    mainFilter = { _or: [mainFilter, _.set({}, `${cursorElement.propertyPath}._eq`, null)] };
                } else if (filterOperation === '_gt' && (mainValue === null || mainValue === '')) {
                    mainFilter = { _or: [mainFilter, _.set({}, `${cursorElement.propertyPath}._ne`, null)] };
                }
            }
            ands.push(mainFilter);

            return { _and: ands };
        });

        return { _or: parts };
    }

    static async transformIdFilter(
        context: Context,
        remoteAppName: string,
        remoteAppVersion: string,
        factory: NodeFactory,
        metadataClient: MetadataClient,
        value: any,
    ): Promise<AnyRecord> {
        if (typeof value === 'string') {
            // We will use the local natural key as the filter and hope that it returns unique on the remote
            const parsedNodeId = await this.parseNodeId(factory, value);
            return this.transformFilter(context, remoteAppName, remoteAppVersion, factory, parsedNodeId);
        }

        if (value instanceof Node) {
            const naturalKeyValue = await factory.getNaturalKeyValueFromNode(value);
            return this.transformFilter(context, remoteAppName, remoteAppVersion, factory, naturalKeyValue);
        }

        if (typeof value === 'number') {
            const node = await context.read(factory.nodeConstructor, { _id: value });
            const naturalKeyValue = await factory.getNaturalKeyValueFromNode(node);
            return this.transformFilter(context, remoteAppName, remoteAppVersion, factory, naturalKeyValue);
        }

        if (typeof value === 'object') {
            return asyncArray(Object.keys(value)).reduce(async (r, k) => {
                switch (k) {
                    case '_eq':
                    case '_ne': {
                        const v = await this.transformIdFilter(
                            context,
                            remoteAppName,
                            remoteAppVersion,
                            factory,
                            metadataClient,
                            value[k],
                        );

                        const flattened = this.flattenObject(v);
                        Object.keys(flattened).forEach(fk => {
                            _.set(r, `${fk}.${k}`, flattened[fk]);
                        });

                        return r;
                    }
                    case '_gt':
                    case '_gte':
                    case '_lt':
                    case '_lte': {
                        const v = await this.transformIdFilter(
                            context,
                            remoteAppName,
                            remoteAppVersion,
                            factory,
                            metadataClient,
                            value[k],
                        );

                        const flattened = this.flattenObject(v);
                        const keys = Object.keys(flattened);
                        const parts: any = keys.map((fk, i) => {
                            const ands = [] as any[];
                            for (let j = 0; j < i; j += 1) {
                                const eqFk = keys[j];
                                const flattenedValue = flattened[eqFk];
                                const eqFilterEntry = _.set({}, `${fk}._eq`, flattenedValue);
                                ands.push(eqFilterEntry);
                            }

                            const mainKey = keys[i];
                            const mainValue = flattened[mainKey];
                            const mainFilter = _.set({}, `${mainKey}.${k}`, mainValue);

                            ands.push(mainFilter);

                            return { _and: ands };
                        });

                        if (k.endsWith('e')) {
                            const eq = {};
                            keys.forEach(fk => {
                                _.set(eq, `${fk}._eq`, flattened[fk]);
                            });
                            parts.push(eq);
                        }

                        _.set(r, '_or', parts);

                        return r;
                    }
                    case '_in':
                    case '_nin': {
                        if (!Array.isArray(value[k])) throw Error(`Invalid value in _id filter ${k}: ${value[k]}`);

                        const values = await asyncArray(value[k])
                            .map(arrVal =>
                                this.transformIdFilter(
                                    context,
                                    remoteAppName,
                                    remoteAppVersion,
                                    factory,
                                    metadataClient,
                                    arrVal,
                                ),
                            )
                            .toArray();

                        const resultMap = new Map();
                        values.forEach(v => {
                            const flattened = this.flattenObject(v);
                            Object.keys(flattened).forEach(fk => {
                                const arr = resultMap.get(fk) ?? [];
                                arr.push(flattened[fk]);
                                resultMap.set(fk, arr);
                            });
                        });

                        resultMap.forEach((v, fk) => {
                            _.set(r, `${fk}.${k}`, v);
                        });

                        return r;
                    }
                    default:
                        throw Error(`Invalid value in _id filter ${k}: ${value[k]}`);
                }
            }, {} as AnyRecord);
        }

        throw Error(`Invalid value for _id filter`);
    }

    static async transformFilter(
        context: Context,
        remoteAppName: string,
        remoteAppVersion: string,
        factory: NodeFactory,
        filter: any,
        remoteMapping?: Mapping,
    ): Promise<AnyRecord> {
        const transform = await this.findNodeTransform(context, factory, { remoteAppName, remoteAppVersion });
        const remoteFilter = {};

        await asyncArray(Object.keys(filter)).forEach(async key => {
            const value = filter[key];
            const property = factory.propertiesByName[key];

            if (value == null) {
                const propertyMapping = property
                    ? InteropTransform.getPropertyMapping(transform, property)
                    : transform.mappings[key];
                if (!propertyMapping) {
                    if (key.startsWith('_') && !property) {
                        _.set(remoteFilter, key, value);
                        return;
                    }
                    throw Error(`Missing mapping for ${factory.name}.${key}`);
                }
                // Mapping is constant or function so we address it later
                if (propertyMapping.kind !== 'path') return;
                const remotePath = propertyMapping.value;
                _.set(remoteFilter, remotePath, value);
                return;
            }

            if (key.startsWith('_') && !property) {
                // Operator
                const operatorValue = await this.transformFilterOperator(
                    context,
                    remoteAppName,
                    remoteAppVersion,
                    factory,
                    key,
                    value,
                    remoteMapping,
                );
                if ((Array.isArray(operatorValue) && operatorValue.length > 0) || !Array.isArray(operatorValue)) {
                    if (Array.isArray(operatorValue)) {
                        switch (key) {
                            case '_gt':
                            case '_gte': {
                                // The operatorValue list should be sorted in ascending order already
                                // List could be [1,2,3]
                                // We want to get the least value
                                // As we are looking for values greater than the least value
                                // foo>1, includes values that satisfies (foo>1 or foo>2 or foo>3)

                                const firstValue = operatorValue.shift();
                                _.set(remoteFilter, key, firstValue);
                                break;
                            }
                            case '_lt':
                            case '_lte': {
                                // The operatorValue list should be sorted in ascending order already
                                // List could be [1,2,3]
                                // We want to get the greatest value
                                // As we are looking for values greater than the least value
                                // foo<3, includes values that satisfies (foo<1 or foo<2 or foo<3)

                                const lastValue = operatorValue.pop();
                                _.set(remoteFilter, key, lastValue);
                                break;
                            }
                            case '_eq': {
                                if (operatorValue.length === 1) {
                                    _.set(remoteFilter, key, operatorValue[0]);
                                    break;
                                }
                                // We have more than one value so we need to use _in instead of _eq
                                _.set(remoteFilter, '_in', operatorValue);
                                break;
                            }
                            case '_ne': {
                                if (operatorValue.length === 1) {
                                    _.set(remoteFilter, key, operatorValue[0]);
                                    break;
                                }
                                // We have more than one value so we need to use _nin instead of _ne
                                _.set(remoteFilter, '_nin', operatorValue);
                                break;
                            }
                            default:
                                _.set(remoteFilter, key, operatorValue);
                                break;
                        }
                    } else {
                        _.set(remoteFilter, key, operatorValue);
                    }
                }
            } else if (property) {
                const metadataClient = new MetadataClient({
                    remoteAppName,
                    remoteInteropPackageName: transform.remoteInteropPackageName,
                    remoteIsConnector: transform.remoteIsConnector,
                });

                if (property.isJsonProperty() && typeof value === 'object') {
                    this.transformJsonFilter(transform, filter, remoteFilter, key, value);
                    return;
                }

                if (key === '_id') {
                    if (typeof value === 'string' || value instanceof Node || typeof value === 'number') {
                        const idTransform = await this.transformIdFilter(
                            context,
                            remoteAppName,
                            remoteAppVersion,
                            factory,
                            metadataClient,
                            value,
                        );
                        _.merge(remoteFilter, idTransform);
                    } else {
                        // _id filter value is an object so we need to iterate over the keys one at a time
                        // resolve each operator (_eq, _ne, _gt, ...) and merge the results
                        const idResults = await asyncArray(Object.keys(value))
                            .map(k =>
                                this.transformIdFilter(
                                    context,
                                    remoteAppName,
                                    remoteAppVersion,
                                    factory,
                                    metadataClient,
                                    { [k]: value[k] },
                                ),
                            )
                            .toArray();

                        // The _id filter had multiple keys so we need to merge them with an _and operator
                        _.set(remoteFilter, '_and', idResults);
                    }
                    return;
                }

                const propertyMapping = InteropTransform.getPropertyMapping(transform, property);
                if (!propertyMapping) throw Error(`Missing mapping for ${factory.name}.${key}`);
                // Mapping is constant or function so we address it later, when we manually page the result
                // Note this will cause issues if the property is a reference property and the natural key is mapped to function/constant
                // This is a limitation of the current implementation
                if (propertyMapping.kind !== 'path') return;
                const remotePath = propertyMapping.value;

                const remotePropertyMapping = InteropTransform.getRemotePropertyMapping(
                    transform,
                    property,
                    remotePath,
                );

                if (property.isReferenceProperty()) {
                    const { targetFactory } = property;
                    const referenceValue = await this.transformReferenceFilter(
                        context,
                        remoteAppName,
                        remoteAppVersion,
                        metadataClient,
                        targetFactory,
                        value ?? null,
                    );
                    _.set(remoteFilter, remotePath, referenceValue);
                } else if (property.isReferenceArrayProperty()) {
                    const { targetFactory } = property;
                    if (!Array.isArray(value))
                        throw property.systemError('Reference array filter value needs to be an array');
                    const referenceValue = await asyncArray(value)
                        .map(v =>
                            this.transformReferenceFilter(
                                context,
                                remoteAppName,
                                remoteAppVersion,
                                metadataClient,
                                targetFactory,
                                v,
                            ),
                        )
                        .toArray();
                    _.set(remoteFilter, remotePath, referenceValue);
                } else if (property.isCollectionProperty()) {
                    if (typeof value !== 'object')
                        throw property.systemError('Collection filter value needs to be an object');
                    const collectionValue = await this.transformFilter(
                        context,
                        remoteAppName,
                        remoteAppVersion,
                        property.targetFactory,
                        value,
                    );
                    _.set(remoteFilter, remotePath, collectionValue);
                } else if (typeof value === 'object') {
                    const filterValue = await this.transformFilter(
                        context,
                        remoteAppName,
                        remoteAppVersion,
                        factory,
                        value,
                        remotePropertyMapping,
                    );
                    _.set(remoteFilter, remotePath, filterValue);
                } else {
                    const transformedValues = this.transformValue(value, remotePropertyMapping.getValues);
                    if (transformedValues.length > 1) {
                        // single value is mapped to many so direct assignment is not possible
                        // we can use _in to fetch all relevant results
                        _.set(remoteFilter, remotePath, { _in: transformedValues });
                    } else {
                        _.set(remoteFilter, remotePath, transformedValues[0]);
                    }
                }
            }
        });

        return remoteFilter;
    }

    static async transformSelector(
        context: Context,
        remoteAppName: string,
        remoteAppVersion: string,
        factory: NodeFactory,
        localSelector: Dict<Selector>,
    ): Promise<Selector> {
        const transform = await this.findNodeTransform(context, factory, { remoteAppName, remoteAppVersion });

        const resultSelector = {};

        await asyncArray(Object.keys(localSelector)).forEach(async key => {
            const localPath = key;
            const localValue = localSelector[key];
            const remoteMapping = transform.mappings[localPath];
            if (!remoteMapping) throw Error(`Missing mapping for ${factory.name}.${localPath}`);
            if (remoteMapping.kind !== 'path') return;
            const property = factory.findProperty(localPath);
            const remotePath = remoteMapping.value;
            if (property.isForeignNodeProperty()) {
                const { targetFactory } = property;
                if (typeof localValue !== 'object')
                    throw Error(
                        `Selector of remote reference/collection ${factory.name}.${localPath} needs to be an object`,
                    );
                const targetSelector = await this.transformSelector(
                    context,
                    remoteAppName,
                    remoteAppVersion,
                    targetFactory,
                    localValue,
                );
                _.set(resultSelector, remotePath, targetSelector);
            } else {
                _.set(resultSelector, remotePath, true);
            }
            _.set(resultSelector, remotePath, localSelector[key]);
        });

        return resultSelector;
    }

    /**
     * Explodes a node into an object with all its properties n levels deep.
     * @param node
     * @param depth
     * @returns
     */
    static async explodeNode(node: Node, maxDepth = 3): Promise<AnyRecord> {
        const result = { ...node.$.getRawPropertyValues() };

        // Prevent infinite recursion, explode only up to 3 levels deep
        // We need to measure the performance impact of this
        if (maxDepth === 0) return result;
        const { factory } = node.$;

        await asyncArray(
            factory.properties.filter(p => p.isPublished && !p.isSystemProperty && p.isForeignNodeProperty()),
        ).forEach(async p => {
            const propertyValue = await node.$.get(p.name);

            if (p.isReferenceProperty() && propertyValue != null) {
                result[p.name] = await this.explodeNode(propertyValue as Node, maxDepth - 1);
            } else if (p.isReferenceArrayProperty()) {
                result[p.name] = await asyncArray(propertyValue as Node[])
                    .map(n => this.explodeNode(n, maxDepth - 1))
                    .toArray();
            } else if (p.isCollectionProperty() && p.isMutable) {
                // Explode only mutable collections
                result[p.name] = await (propertyValue as BaseCollection)
                    .map(n => this.explodeNode(n, maxDepth - 1))
                    .toArray();
            }
        });

        return result;
    }

    static async getRemoteNaturalValue(
        metadataClient: MetadataClient,
        remoteAppVersion: string,
        node: Node,
    ): Promise<string> {
        const { factory } = node.$;
        const transform = await InteropTransform.findNodeTransform(node.$.context, factory, {
            remoteAppName: metadataClient.remoteAppName,
            remoteAppVersion,
        });

        const remoteNodeInfo = await metadataClient.findNodeInfo(node.$.context, transform.remoteNodeName);

        const remoteNaturalKey = remoteNodeInfo.naturalKey;

        if (!remoteNaturalKey || remoteNaturalKey.length === 0)
            throw Error(`Remote node missing natural key: ${transform.remoteNodeName}`);

        return (
            await asyncArray(remoteNaturalKey)
                .map(async p => {
                    const remoteMapping = transform.remoteMappings[p];
                    if (!remoteMapping)
                        throw Error(
                            `Remote node missing natural key mapping: ${transform.remoteNodeName} for remote natural key property ${p} needs to be mapped to a local property`,
                        );

                    if (remoteMapping.kind === 'constant') {
                        return remoteMapping.value;
                    }

                    if (remoteMapping.kind === 'function') {
                        // TODO: this implementation only considers computations on scalar properties, if a property is deep or computed
                        // this will not work. We will need to find a way to evaluate the function in the context of the node.
                        return Interpreter.safeEval(remoteMapping.value, {
                            timeout: ConfigManager.current?.security?.jsEval?.timeoutInMillis ?? 500,
                            sandbox: { payload: await InteropTransform.explodeNode(node) },
                        }).value;
                    }

                    const value = await node.$.get(remoteMapping.value);

                    if (value instanceof Node) {
                        return this.getRemoteNaturalValue(metadataClient, remoteAppVersion, value);
                    }

                    return String(value);
                })
                .toArray()
        ).join('|');
    }

    static transformRemoteNodeResult(transform: NodeTransformData, result: Payload): Promise<Payload> {
        return InteropMapper.applyMappings(result, transform.mappings);
    }

    static transformInboundPropertyValue(context: Context, property: Property, val: any): AnyValue {
        if (val == null) return val;

        switch (property.type) {
            case 'boolean':
                return typeof val === 'boolean' ? val : val === 'true';
            case 'enum':
            case 'integer':
            case 'short':
                return val;
            case 'string':
                if (typeof val !== 'string') return String(val);
                return val;
            case 'decimal':
                return Decimal.make(val);
            case 'date':
                if (typeof val === 'string') {
                    return DateValue.parse(val);
                }
                throw new Error(`Invalid date value: ${val}`);
            case 'dateRange':
                return DateRange.parse(val);
            case 'datetimeRange':
                return DatetimeRange.parse(val);
            case 'time':
                return Time.parse(val, context.currentLocale as LocalizeLocale);
            case 'datetime':
                return Datetime.parse(val);
            case 'uuid':
                return Uuid.fromString(val as string);
            case 'binaryStream':
                return BinaryStream.fromBuffer(Buffer.from((val as { value: string }).value, 'base64'));
            case 'textStream':
                return TextStream.fromString((val as { value: string }).value);
            default:
                return val;
        }
    }

    static parseInboundReferenceFunctionValue(
        context: Context,
        referenceProperty: ForeignNodeProperty,
        v: string | string[],
    ): Promise<string | number> {
        const { targetFactory } = referenceProperty;
        const keys = Array.isArray(v) ? v : v.split('|').reverse();

        if (!targetFactory.naturalKey) throw Error(`Missing natural key for ${targetFactory.name}`);

        return asyncArray(targetFactory.naturalKey)
            .map(async k => {
                const property = targetFactory.findProperty(k);
                if (property.isReferenceProperty()) {
                    const refKey = await this.parseInboundReferenceFunctionValue(context, property, keys);
                    return refKey;
                }
                return keys.pop();
            })
            .join('|');
    }

    static async getInboundReferenceValue(
        context: Context,
        referenceProperty: ForeignNodeProperty,
        v: Payload,
        propertyMapping: Mapping,
    ): Promise<string | number | null> {
        const { targetFactory } = referenceProperty;
        if (v == null) return v;
        if (
            Object.values(this.flattenObject(v)).every(
                naturalKeyValue => naturalKeyValue === '' || naturalKeyValue == null,
            )
        ) {
            return null;
        }

        if (propertyMapping.kind === 'path') {
            const targetTransform = await InteropTransform.findNodeTransform(context, targetFactory);
            let transformData = await this.transformRemoteNodeResult(targetTransform, v);
            transformData = await asyncArray(Object.keys(transformData)).reduce(async (r, k) => {
                if (typeof transformData[k] !== 'object' || transformData[k] === null) {
                    r[k] = transformData[k];
                    return r;
                }
                const property = targetFactory.findProperty(k);
                if (property.isReferenceProperty()) {
                    const referenceTransform = await InteropTransform.findNodeTransform(
                        context,
                        property.targetFactory,
                    );
                    r[k] = await this.transformInboundNodeValues(
                        context,
                        referenceTransform,
                        await this.transformRemoteNodeResult(referenceTransform, transformData[k]),
                    );
                } else if (property.isReferenceArrayProperty()) {
                    const referenceTransform = await InteropTransform.findNodeTransform(
                        context,
                        property.targetFactory,
                    );
                    r[k] = await asyncArray(transformData[k])
                        .map(async (referenceValue: Payload) => {
                            const refVal = await this.transformInboundNodeValues(
                                context,
                                referenceTransform,
                                await this.transformRemoteNodeResult(referenceTransform, referenceValue),
                            );

                            if (property.targetFactory.storage === 'sql') {
                                if (!property.targetFactory.naturalKey)
                                    throw targetFactory.logicError('Missing natural key');
                                return targetFactory.getNaturalKeyStringValue(
                                    context,
                                    _.pick(refVal, property.targetFactory.naturalKey),
                                );
                            }

                            return refVal;
                        })
                        .toArray();
                } else {
                    r[k] = transformData[k];
                }

                return r;
            }, {} as Payload);

            if (targetFactory.storage === 'sql') {
                if (!targetFactory.naturalKey) throw targetFactory.logicError('Missing natural key');
                return `#${await targetFactory.getNaturalKeyStringValue(context, _.pick(transformData, targetFactory.naturalKey))}`;
            }

            if (!targetFactory.externalStorageManager)
                throw targetFactory.logicError('Missing external storage manager');
            return targetFactory.externalStorageManager.getId(context, transformData);
        }

        if (propertyMapping.kind === 'function' && targetFactory.storage === 'external') {
            return this.parseInboundReferenceFunctionValue(context, referenceProperty, String(v));
        }

        if (String(v) === '') return null;

        return targetFactory.storage !== 'external' ? `#${v}` : String(v);
    }

    static getPropertyMapping(transform: NodeTransformData, property: Property): Mapping | undefined {
        const propertyName = property.name;
        let mapping = transform.mappings[propertyName];
        if (!mapping && (property.isTextStreamProperty() || property.isBinaryStreamProperty())) {
            mapping = transform.mappings[`${propertyName}.value`];
        }
        return mapping;
    }

    static getRemotePropertyMapping(transform: NodeTransformData, property: Property, remotePath: string): Mapping {
        const propertyName = property.name;
        const remotePropertyMapping =
            transform.remoteMappings[`${remotePath}~${propertyName}`] ?? transform.remoteMappings[remotePath];
        if (!remotePropertyMapping)
            throw Error(`Missing remote mapping for ${property.fullName} =>${remotePath}~${propertyName}`);
        return remotePropertyMapping;
    }

    static transformInboundNodeValues(
        context: Context,
        transform: NodeTransformData,
        result: Payload,
    ): Promise<Payload> {
        return asyncArray(Object.keys(result)).reduce(async (r, k) => {
            const property = transform.localFactory.findProperty(k);
            const mapping = this.getPropertyMapping(transform, property);
            if (!mapping) throw Error(`Missing mapping for ${transform.localFactory.name}.${k}`);

            let localValue: any;
            logger.verbose(
                () =>
                    `transformInboundNodeValues: transform: ${transform.localFactory.name} property: ${property.name} isReference: ${property.isReferenceProperty()}`,
            );
            if (property.isReferenceProperty()) {
                localValue = await this.getInboundReferenceValue(context, property, result[k], mapping);
            } else if (property.isReferenceArrayProperty()) {
                localValue = await asyncArray(result[k])
                    .map((v: Payload) => {
                        return this.getInboundReferenceValue(context, property, v, mapping);
                    })
                    .toArray();
            } else {
                localValue = this.transformInboundPropertyValue(context, property, result[k]);
            }
            _.set(r, k, localValue);
            return r;
        }, {} as Payload);
    }

    static async getOutboundReferenceValue(
        context: Context,
        transform: NodeTransformData,
        metadataClient: MetadataClient,
        remoteProperty: RemoteNodeProperty,
        localProperty: Property,
        localValue: Node,
        path: string[] = [],
    ): Promise<Payload | string> {
        if (remoteProperty.isMutable) {
            if (!localProperty.isReferenceProperty())
                throw new Error(`Invalid reference property: ${localProperty.name}`);
            const targetTransform = await this.findNodeTransform(context, localProperty.targetFactory, {
                remoteAppName: transform.remoteAppName,
                remoteAppVersion: transform.remoteAppVersion,
            });

            return this.transformOutboundNodeValues(context, targetTransform, localValue, metadataClient, path);
        }
        const idValue = localValue._id;
        if (Number.isFinite(Number(idValue)) && Number(idValue) < 0) return String(idValue);

        const remoteNaturalKeyVal = await this.getRemoteNaturalValue(
            metadataClient,
            transform.remoteAppVersion,
            localValue,
        );
        return `#${remoteNaturalKeyVal}`;
    }

    static transformOutboundPropertyValue(remoteProperty: RemoteNodeProperty, val: any): AnyValue {
        if (val == null) return val;

        switch (remoteProperty.type) {
            case 'boolean':
                return typeof val === 'boolean' ? val : val === 'true';
            case 'enum':
            case 'integer':
            case 'short':
            case 'string':
                return val;
            case 'decimal':
            case 'date':
            case 'dateRange':
            case 'datetimeRange':
            case 'time':
            case 'datetime':
            case 'uuid':
                return String(val);
            case 'binaryStream':
                return { value: (val as BinaryStream).value };
            case 'textStream':
                return { value: (val as TextStream).value };
            case 'json':
                return JSON.stringify(val);
            default:
                return val;
        }
    }

    static async transformOutboundMappedPropertyValue(
        context: Context,
        transform: NodeTransformData,
        node: Node,
        metadataClient: MetadataClient,
        remoteProperty: RemoteNodeProperty,
        path: string[] = [],
    ): Promise<any> {
        const remoteMapping = transform.remoteMappings[remoteProperty.name];
        if (!remoteMapping) {
            return undefined;
        }
        if (remoteMapping.kind === 'path') {
            const localPath = remoteMapping.value.split('.');
            const localPropertyDetails = this.walkPropertyPath(context, transform.localFactory, localPath);
            const propertyTransform = await this.findNodeTransform(context, localPropertyDetails.factory, {
                remoteAppName: transform.remoteAppName,
                remoteAppVersion: transform.remoteAppVersion,
            });
            const localValue = await node.$.get(remoteMapping.value);
            if (localValue == null) {
                if (remoteProperty.isNullable || remoteProperty.isMutable) {
                    return undefined;
                }
                throw Error(
                    `${transform.remoteAppName}: Missing value for ${transform.localFactory.name}.${remoteMapping.value}`,
                );
            }

            if (remoteProperty.type === 'reference') {
                if (remoteProperty.isVitalParent && path.length > 0) return undefined;
                return this.getOutboundReferenceValue(
                    context,
                    propertyTransform,
                    metadataClient,
                    remoteProperty,
                    localPropertyDetails.property,
                    localValue as Node,
                    [...path, ...localPath],
                );
            }

            if (remoteProperty.type === 'referenceArray') {
                if (Array.isArray(localValue)) {
                    return asyncArray(localValue)
                        .map((v: Node) => {
                            return this.getOutboundReferenceValue(
                                context,
                                propertyTransform,
                                metadataClient,
                                remoteProperty,
                                localPropertyDetails.property,
                                v,
                                [...path, ...localPath],
                            );
                        })
                        .toArray();
                }
            }

            if (remoteProperty.type === 'collection') {
                if (localValue instanceof BaseCollection) {
                    if (!localPropertyDetails.property.isCollectionProperty())
                        throw new Error(`Invalid reference property: ${localPropertyDetails.property.name}`);
                    const targetTransform = await this.findNodeTransform(
                        context,
                        localPropertyDetails.property.targetFactory,
                        {
                            remoteAppName: transform.remoteAppName,
                            remoteAppVersion: transform.remoteAppVersion,
                        },
                    );
                    return localValue
                        .map((v: Node) => {
                            return InteropTransform.transformOutboundNodeValues(
                                context,
                                targetTransform,
                                v,
                                metadataClient,
                                [...path, ...localPath],
                            );
                        })
                        .toArray();
                }
            }

            if (remoteMapping.valueMap) {
                return this.transformOutboundPropertyValue(remoteProperty, remoteMapping.valueMap(localValue));
            }

            return this.transformOutboundPropertyValue(remoteProperty, localValue);
        }

        if (remoteMapping.kind === 'function') {
            // TODO: this implementation only considers computations on scalar properties, if a property is deep or computed
            // this will not work. We will need to find a way to evaluate the function in the context of the node.
            return Interpreter.safeEval(remoteMapping.value, {
                timeout: ConfigManager.current?.security?.jsEval?.timeoutInMillis ?? 500,
                sandbox: { payload: await InteropTransform.explodeNode(node) },
            }).value;
        }
        return remoteMapping.value;
    }

    static async transformOutboundNodeValues(
        context: Context,
        transform: NodeTransformData,
        node: Node,
        metadataClient: MetadataClient,
        path: string[] = [],
    ): Promise<Payload> {
        const remoteNodeInfo = await metadataClient.findNodeInfo(context, transform.remoteNodeName);
        const remoteProperties = remoteNodeInfo.properties.filter(
            property => property.isStored || property.isTransientInput || property.isMutable,
        );
        const result = await asyncArray(remoteProperties).reduce(async (r, remoteProperty) => {
            const remoteValue = await this.transformOutboundMappedPropertyValue(
                context,
                transform,
                node,
                metadataClient,
                remoteProperty,
                path,
            );

            if (remoteValue !== undefined) {
                _.set(r, remoteProperty.name, remoteValue);
            }

            return r;
        }, {} as Payload);

        const idValue = node._id;
        if (Number.isFinite(Number(idValue)) && Number(idValue) < 0) result._id = String(idValue);

        return result;
    }

    static transformSelectorForQuery(
        context: Context,
        remoteNodeInfo: RemoteNodeInfo,
        s: Selector,
        metadataClient: MetadataClient,
    ): AsyncResponse<string> {
        if (s === true) return '';

        return asyncArray(Object.keys(s)).reduce(async (r, k) => {
            const property = remoteNodeInfo.properties.find(p => p.name === k);

            if (property?.type === 'collection') {
                // Collection properties are a bit more complex as they have query, edges, and node.
                // The collection should load itself its own node query
                // TODO: implement later if needed
                return r;
            }
            const v = s[k];
            if (typeof v === 'object') {
                let targetRemoteNodeInfo = remoteNodeInfo;
                if (property?.targetNode) {
                    targetRemoteNodeInfo = await metadataClient.findNodeInfo(context, property?.targetNode);
                }

                return `${r} ${k} { ${await this.transformSelectorForQuery(context, targetRemoteNodeInfo, v, metadataClient)} }`;
            }
            return `${r} ${k}`;
        }, '');
    }

    static flattenObject(obj: any, prefix = ''): any {
        const flattened: any = {};
        Object.keys(obj).forEach(k => {
            const path = prefix ? `${prefix}.${k}` : k;
            if (typeof obj[k] === 'object' && obj[k] !== null) {
                Object.assign(flattened, this.flattenObject(obj[k], path));
            } else {
                flattened[path] = obj[k];
            }
        });

        return flattened;
    }
}
