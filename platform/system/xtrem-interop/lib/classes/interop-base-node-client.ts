import { AnyRecord, Context, Dict, Logger, NodeFactory } from '@sage/xtrem-core';
import { RemoteNodeInfo } from '../nodes/sys-remote-metadata';
import { InteropClient, InteropClientOptions } from './interop-client';
import { InteropTransform, NodeTransformData } from './interop-transform';
import { MetadataClient } from './metadata-client';
import { RemoteInfoCache } from './remote-info-cache';

export interface BaseNodeClientOptions extends InteropClientOptions {
    localFactory: NodeFactory;
}

export type QueryResult = {
    [q in 'query' | 'queryAggregate']: {
        totalCount: number;
        edges: {
            node: Dict<QueryResult | AnyRecord>;
        }[];
        pageInfo: {
            hasNextPage: boolean;
            endCursor?: string;
        };
    } | null;
};

export interface FirstQueryResult {
    [packageName: string]: {
        [nodeName: string]: QueryResult;
    };
}

export const logger = Logger.getLogger(__filename, 'node-client');

export class InteropBaseNodeClient extends InteropClient {
    constructor(private readonly baseNodeClientOptions: BaseNodeClientOptions) {
        super(baseNodeClientOptions);
    }

    get localFactory(): NodeFactory {
        return this.baseNodeClientOptions.localFactory;
    }

    #metadataClient: MetadataClient;

    get metadataClient(): MetadataClient {
        if (this.#metadataClient) return this.#metadataClient;

        this.#metadataClient = new MetadataClient({
            remoteAppName: this.remoteAppName,
            remoteInteropPackageName: this.remoteInteropPackageName,
            remoteIsConnector: this.remoteIsConnector,
        });

        return this.#metadataClient;
    }

    private async getNodeTransform(context: Context): Promise<NodeTransformData> {
        const remoteAppVersion = (await RemoteInfoCache.fetchRemoteInfo(context, this.options)).remoteAppVersion;
        return InteropTransform.findNodeTransform(context, this.localFactory, {
            remoteAppName: this.remoteAppName,
            remoteAppVersion,
        });
    }

    async getRemoteNodeInfo(context: Context): Promise<RemoteNodeInfo> {
        const transform = await this.getNodeTransform(context);

        return this.metadataClient.findNodeInfo(context, transform.remoteNodeName);
    }
}
