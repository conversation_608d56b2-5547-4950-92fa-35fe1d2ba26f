/* eslint-disable no-restricted-syntax */
import {
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON>ync<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON>ync<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    AsyncReader,
    Context,
    Dict,
    GraphQlResponse,
    InteropGraphqlClient,
    LogicError,
    asyncArrayReader,
    decimal,
    integer,
} from '@sage/xtrem-core';
import { ExecutionPatchResult, ExecutionResult } from 'graphql';
import { IncomingMessage } from 'http';
import { InteropClient } from './interop-client';
import { Payload } from './interop-mapper';

export type ClientSelector<T = unknown> = true | { [K in keyof T]?: ClientSelector<T> };

export interface FirstResult {
    [packageName: string]: {
        sysSynchronizationSource: {
            getSynchronizationFeed: {
                nextTick: decimal;
                feed: string[];
            };
        };
    };
}

export type FirstPart = ExecutionResult<FirstResult>;
export type PatchPart = ExecutionPatchResult<string>;
export type AsyncPartsGenerator = AsyncGenerator<FirstPart, PatchPart>;

export type AsyncPartsData = AsyncPartsGenerator | FirstPart;

export type AsyncHttpGenerator = AsyncGenerator<Buffer> & IncomingMessage;

export interface SysSynchronizationFeed<PayloadT extends Payload> {
    nextTick: decimal;
    reader: AsyncReader<PayloadT>;
}

export class SysSynchronizationClient extends InteropClient {
    // eslint-disable-next-line class-methods-use-this
    protected override logicError(message: string): LogicError {
        return new LogicError(`Synchronization failed: ${message}`);
    }

    /**
     * Fetches a stream of data from the GraphQL server.
     *
     * @param context - The context object.
     * @param query - The GraphQL query string (with one or more @stream directives).
     * @param variables - The variables to be passed with the query (default: {}).
     * @param config - The configuration options for the request (default: {}).
     * @returns A promise that resolves to a GraphQlResponse containing either an AsyncPartsGenerator or AsyncHttpGenerator.
     * @internal (used by unit test)
     */
    fetchStream(
        context: Context,
        query: string,
        variables: Dict<any> = {},
        config: Dict<any> = {},
    ): Promise<GraphQlResponse<AsyncPartsGenerator | AsyncHttpGenerator>> {
        return InteropGraphqlClient.sendStreamGraphqlRequest(context, {
            appName: this.remoteAppName,
            scope: 'synchronization',
            query,
            variables,
            config,
        });
    }

    /**
     * Parses a multipart stream and returns a generator that yields the parsed parts.
     * @param buffersGenerator The AsyncGenerator that emits the bytes of the multipart HTTP stream.
     * @param boundary The boundary string used to separate the parts in the stream.
     * @returns An AsyncGenerator that yields the parsed parts of the multipart stream.
     */
    private static parseMultipartStream(
        buffersGenerator: AsyncGenerator<Buffer>,
        boundary: string,
    ): AsyncPartsGenerator {
        const separatorRe = RegExp(`\r\n--${boundary}\r\n`);

        async function* generator(): AsyncGenerator<any> {
            const parsePart = (part: string): any => {
                const [partHeaders, partBody] = part.split(/\r?\n\r?\n/);
                // Last part of our unit test has no headers -- investigate why...
                const json = partBody ?? partHeaders;
                return JSON.parse(json);
            };
            let tail = '';
            let isFirst = true;
            for await (const chunk of buffersGenerator) {
                const str = `${tail}${chunk.toString()}`;
                const parts = str.split(separatorRe);
                for (let i = 0; i < parts.length - 1; i += 1) {
                    if (isFirst) {
                        if (parts[i] !== '') throw new LogicError('First part should not be empty');
                        isFirst = false;
                    } else {
                        yield parsePart(parts[i]);
                    }
                }
                tail = parts[parts.length - 1];
            }
            yield parsePart(tail);
        }
        return generator();
    }

    /**
     * Parse a string entry as JSON (JSON.parse with error handling)
     *
     * @param str - The string entry.
     * @param index - The index of the entry.
     * @returns The parsed object.
     */
    private parseJsonEntry(str: string, index: integer): any {
        if (typeof str !== 'string')
            throw this.logicError(`getFeed query returned bad data: ${typeof str}, index=${index}`);
        try {
            return JSON.parse(str);
        } catch (err) {
            throw this.logicError(`getFeed query returned bad JSON data: ${str}, index=${index}`);
        }
    }

    /**
     * Retrieves a feed from a parts generator .
     *
     * @template PayloadT - The type of the payload.
     * @param {AsyncPartsGenerator} partsGenerator - The generator to retrieve the feed from.
     * @param {decimal} startTick - The starting tick.
     * @returns a promise that resolves to synchronization feed.
     */
    private async getFeedFromPartsGenerator<PayloadT extends Payload>(
        partsGenerator: AsyncPartsGenerator,
        startTick: decimal,
    ): Promise<SysSynchronizationFeed<PayloadT>> {
        const firstChunk = (await partsGenerator.next()) as IteratorResult<FirstPart>;
        if (firstChunk.done) {
            return { nextTick: startTick, reader: new AsyncArrayReader<PayloadT>(() => []) };
        }

        const firstData =
            firstChunk.value.data?.[this.remoteInteropGraphqlPackageName]?.sysSynchronizationSource
                .getSynchronizationFeed;
        if (!firstData)
            throw this.logicError(`getFeed query failed: ${firstChunk.value.errors?.map(error => error.toJSON())}`);

        const nextTick = Number(firstData.nextTick);

        const reader = new AsyncGenericReader({
            async read() {
                if (firstData.feed.length > 0) {
                    return firstData.feed.shift();
                }
                const nextChunk = (await partsGenerator.next()) as IteratorResult<PatchPart>;
                if (nextChunk.done) return undefined;
                const data = nextChunk.value.data;
                if (!data) {
                    // TODO: handle errors
                    return undefined;
                }
                return data;
            },
            stop() {
                // TODO: call a cancellation mutation???
            },
        }).map(this.parseJsonEntry.bind(this));

        return { nextTick, reader };
    }

    /**
     * Returns a synchronization feed from a single part.
     * @template PayloadT - The type of the payload.
     * @param part - The part.
     * @returns A synchronization feed.
     */
    private getFeedFromSinglePart<PayloadT extends Payload>(part: FirstPart): SysSynchronizationFeed<PayloadT> {
        if (!part.data) throw new LogicError('Unexpected synchronization result');
        const fullData =
            part.data?.[this.remoteInteropGraphqlPackageName]?.sysSynchronizationSource.getSynchronizationFeed;
        if (!fullData) throw this.logicError(`getFeed query failed: ${part.errors?.map(error => error.message)}`);
        return {
            nextTick: Number(fullData.nextTick),
            reader: asyncArrayReader(fullData.feed.map(this.parseJsonEntry.bind(this))),
        };
    }

    /**
     * Returns a synchronization feed from the JSON readable stream of an HTTP response.
     * @template PayloadT The type of the payload.
     * @param buffersGenerator The generator to read the bytes from.
     * @returns a promise that resolves to synchronization feed.
     */
    private async getFeedFromBufferGenerator<PayloadT extends Payload>(
        buffersGenerator: AsyncGenerator<Buffer>,
    ): Promise<SysSynchronizationFeed<PayloadT>> {
        const chunks = [];
        for await (const chunk of buffersGenerator) {
            chunks.push(chunk);
        }
        const body = Buffer.concat(chunks).toString('utf8');
        const json = JSON.parse(body);

        return this.getFeedFromSinglePart(json);
    }

    /**
     * Returns a feed from an HTTP response
     *
     * @param headers - The headers of the HTTP response.
     * @param buffersGenerator - The data of the HTTP response.
     * @param startTick - The starting tick value.
     * @returns A promise that resolves to a synchronization feed.
     */
    private getFeedFromHttpResponse<PayloadT extends Payload>(
        headers: GraphQlResponse['headers'],
        buffersGenerator: AsyncGenerator<Buffer>,
        startTick: decimal,
    ): Promise<SysSynchronizationFeed<PayloadT>> {
        // Header and data come from a response from a real HTTP call with an @stream graphql query.
        const contentType = headers['content-type'];
        if (!contentType) throw new LogicError('No content type in response');

        if (contentType.startsWith('multipart/mixed')) {
            // The response contains several parts
            const boundary = /boundary="(.*)"/.exec(contentType)?.[1];
            if (!boundary) throw new LogicError('No boundary in content type');

            // Parse the multipart stream as a generator of parts.
            const generator = SysSynchronizationClient.parseMultipartStream(buffersGenerator, boundary);
            // This generator generates the same parts as what we get from a direct call to the graphql middleware.
            // Transform it to our feed format.
            return this.getFeedFromPartsGenerator(generator, startTick);
        }
        if (contentType.startsWith('application/json')) {
            // The response contains only one part.
            return this.getFeedFromBufferGenerator(buffersGenerator);
        }
        throw new LogicError(`Unexpected response type: ${contentType}`);
    }

    /**
     * Returns a feed from a direct call to the GraphQL middleware.
     *
     * @template PayloadT - The type of the payload.
     * @param data - The data received from the direct call.
     * @param startTick - The starting tick value.
     * @returns A promise that resolves to a synchronization feed.
     */
    private getFeedFromDirectCall<PayloadT extends Payload>(
        data: AsyncPartsGenerator | FirstPart,
        startTick: decimal,
    ): Promise<SysSynchronizationFeed<PayloadT>> {
        const dataAsGenerator = data as AsyncPartsGenerator;
        if (typeof dataAsGenerator?.next === 'function') {
            // data is a generator for multiple parts
            return this.getFeedFromPartsGenerator(dataAsGenerator, startTick);
        }

        // data contains a single part
        return Promise.resolve(this.getFeedFromSinglePart(data as FirstPart));
    }

    /**
     * Retrieves a synchronization feed for a given node.
     * @param context The context object.
     * @param nodeName The name of the node.
     * @param startTick The starting tick.
     * @param selector The client selector.
     * @param initialCount The initial count.
     * @returns A promise that resolves to a synchronization feed.
     */
    async getFeed<PayloadT extends Payload>(
        context: Context,
        {
            nodeName,
            startTick,
            selector,
            initialCount,
            filter,
        }: {
            nodeName: string;
            startTick: decimal;
            initialCount: integer;
            selector: ClientSelector;
            filter?: AnyRecord;
        },
    ): Promise<SysSynchronizationFeed<PayloadT>> {
        const filterString = filter ? `,\n filter: ${InteropClient.stringifyObjectParameter(filter)}` : '';
        const result = await this.fetchStream(
            context,
            `{
                ${this.remoteInteropGraphqlPackageName} {
                    sysSynchronizationSource {
                        getSynchronizationFeed(
                            node: "#${nodeName}",
                            startTick: "${startTick}",
                            selector: ${InteropClient.stringifyObjectParameter(selector)}${filterString}) {
                            nextTick,
                            feed @stream(initialCount: ${initialCount})
                        }
                    }
                }
            }`,
            {},
            { responseType: 'stream' },
        );

        const data = result.data;
        if (data instanceof IncomingMessage) {
            // normal path: the result is a response from a real HTTP call
            return this.getFeedFromHttpResponse(result.headers, data, startTick);
        }
        // special path for sys-synchronization-test (calls the graphql middleware directly)
        return this.getFeedFromDirectCall(data, startTick);
    }
}
