import { Application, InteropAppHealthMonitor, Logger, gracefulShutdown } from '@sage/xtrem-core';
import { InfrastructureHelper } from '@sage/xtrem-infrastructure-adapter';
import * as xtremSystem from '@sage/xtrem-system';
import { SysApp } from '../nodes/sys-app';

export class SysAppRefresh {
    private static readonly logger = Logger.getLogger(__filename, 'sys-app-refresh');

    /**
     * Timer used for refreshing the list of active apps for each tenant.
     */
    private static refreshTimer: NodeJS.Timeout | null = null;

    /**
     * Refreshes the active tenant apps for the given application.
     *
     * @param application - The application to refresh the apps for.
     * @returns A promise that resolves when the apps are refreshed.
     */
    static async refreshApps(application: Application): Promise<void> {
        this.logger.info('Refreshing apps...');
        const tenantIds = (
            await application.asRoot.withReadonlyContext(null, context =>
                context.select(xtremSystem.nodes.SysTenant, { tenantId: true }, { filter: {} }),
            )
        ).map(tenant => tenant.tenantId);

        tenantIds.forEach(async tenantId => {
            const apps = await InfrastructureHelper.getTenantApps(tenantId);
            if (apps == null) {
                this.stop();
                return;
            }
            this.logger.verbose(() => `Active apps for tenant ${tenantId}: ${apps.join(', ')}`);
            // refresh the health (alive) status of the apps (for loop for performance)
            // eslint-disable-next-line no-restricted-syntax
            for (const app of apps) {
                await InteropAppHealthMonitor.isAppAlive(app, { force: true });
            }
            await application.asRoot.withCommittedContext(
                tenantId,
                async context => {
                    await context.bulkUpdate(SysApp, { set: { isActive: true }, where: { name: { _in: apps } } });
                    await context.bulkUpdate(SysApp, { set: { isActive: false }, where: { name: { _nin: apps } } });
                },
                { description: () => "Refresh app's active states" },
            );
        });
        this.logger.info('Refreshing apps (done)');
    }

    /**
     * Stops the app refresh timer.
     */
    private static stop(): void {
        this.logger.info('Stopping app refresh timer');
        if (this.refreshTimer) clearInterval(this.refreshTimer);
        this.refreshTimer = null;
    }

    // Start the app refresh timer when the application is ready
    static init(): void {
        Application.emitter.once('listen', application => {
            this.logger.info('Starting app refresh timer');
            // refresh every hour
            this.refreshTimer = setInterval(
                () => this.refreshApps(application).catch(err => this.logger.error(err.stack)),
                1000 * 60 * 60,
            );
        });

        // Stop the app refresh timer when the application is stopped
        gracefulShutdown.on('stop', this.stop.bind(this));
    }
}
