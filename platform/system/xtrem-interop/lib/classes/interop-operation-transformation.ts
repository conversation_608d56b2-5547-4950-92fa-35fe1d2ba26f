import { LogicError, NodeFactory, ValidationContext } from '@sage/xtrem-core';
import { InteropClientOptions } from './interop-client';
import { InteropMapper, Mappings, Payload } from './interop-mapper';

export interface OperationMappings {
    /** Parameter mappings. Keys are remote paths starting with a parameter name, values are local paths or constants */
    parameterMappings: Mappings;
    /** Return mappings. Keys are local paths, values are remote paths or constants */
    returnMappings: Mappings;
}
/**
 * Defines how a remote node will be transformed into a local node during synchronization.
 */
export interface OperationTransformData extends InteropClientOptions {
    localFactory: NodeFactory;
    localOperationName: string;
    remoteAppVersion: string;
    remotePackageName: string;
    remoteNodeName: string;
    remoteOperationName: string;
    mappings: OperationMappings;
}

/**
 * @internal
 * Helper class to validate and execute synchronization transforms.
 */
export class InteropOperationTransformation {
    readonly #transform: OperationTransformData;

    constructor(transform: OperationTransformData) {
        this.#transform = transform;
    }

    get mappings(): OperationMappings {
        return this.#transform.mappings;
    }

    // eslint-disable-next-line class-methods-use-this, @typescript-eslint/no-unused-vars
    control(_cx: ValidationContext): void {
        // TODO
    }

    transformArgs(localArgs: Payload): Promise<Payload> {
        if (!this.mappings.parameterMappings) throw new LogicError('No parameter mappings found');
        return InteropMapper.applyMappings(localArgs, this.mappings.parameterMappings);
    }

    transformReturn(remoteReturn: Payload): Promise<Payload> {
        if (!this.mappings.returnMappings) throw new LogicError('No return mappings found');
        return InteropMapper.applyMappings(remoteReturn, this.mappings.returnMappings);
    }
}
