import { Application, Context, Dict, NodeFactory, PlainOperationDecorator } from '@sage/xtrem-core';
import * as _ from 'lodash';
import * as semver from 'semver';
import { SysOperationTransformation } from '../nodes';
import { InteropOperationClient } from './interop-operation-client';
import { OperationTransformData } from './interop-operation-transformation';
import { RemoteInfoCache } from './remote-info-cache';

export class InteropInterceptor {
    private static async getTransformations(
        context: Context,
        factory: NodeFactory,
        operationName: string,
    ): Promise<OperationTransformData[]> {
        const transforms = await context.select(
            SysOperationTransformation,
            {
                remoteApp: {
                    name: true,
                    interopPackage: true,
                    isConnector: true,
                },
                remoteAppVersion: true,
                remotePackageName: true,
                remoteNodeName: true,
                remoteOperationName: true,
                mappings: true,
            },
            {
                filter: {
                    isActive: true,
                    localNode: { name: factory.name },
                    localOperationName: operationName,
                },
            },
        );
        return transforms.map(transform => {
            return {
                localFactory: factory,
                localOperationName: operationName,
                remoteAppName: transform.remoteApp.name,
                remoteInteropPackageName: transform.remoteApp.interopPackage,
                remoteIsConnector: transform.remoteApp.isConnector,
                ...transform,
            };
        });
    }

    private static async getTransformationData(
        context: Context,
        factory: NodeFactory,
        operationName: string,
    ): Promise<OperationTransformData | null> {
        const transformations = await this.getTransformations(context, factory, operationName);
        if (transformations.length === 0) return null;
        const remoteClientOptions = transformations[0];
        const remoteAppName = remoteClientOptions.remoteAppName;
        const remoteAppVersion = (await RemoteInfoCache.fetchRemoteInfo(context, remoteClientOptions)).remoteAppVersion;
        const selectedTransforms = transformations.filter(t => semver.satisfies(remoteAppVersion, t.remoteAppVersion));
        if (selectedTransforms.length === 0)
            throw new Error(
                `no transformation found for ${factory.name}.${operationName} and app ${remoteAppName} at version ${remoteAppVersion}`,
            );
        if (selectedTransforms.length > 1)
            throw new Error(
                `multiple transformations found for ${factory.name}.${operationName} and app ${remoteAppName} at version ${remoteAppVersion}`,
            );
        return selectedTransforms[0];
    }

    private static callRemote(context: Context, transformationData: OperationTransformData, args: Dict<any>): any {
        const client = new InteropOperationClient(transformationData);
        return client.execute(context, args);
    }

    private static wrapOperation(factory: NodeFactory, operation: PlainOperationDecorator, method: Function): Function {
        return async function wrapped(this: any, context: Context, ...args: any[]): Promise<any> {
            const transformationData = await InteropInterceptor.getTransformationData(context, factory, operation.name);
            if (!transformationData) return method.call(this, context, ...args);

            const argsDict = _.zipObject(
                operation.parameters.map(p => p.name),
                args,
            );
            return InteropInterceptor.callRemote(context, transformationData, argsDict);
        };
    }

    static interceptInteropOperations(application: Application): void {
        application.getAllFactories().forEach(factory => {
            [...factory.queries, ...factory.mutations]
                .filter(operation => !!operation.isInterop)
                .forEach(operation => {
                    const { name } = operation;
                    const ctor = factory.nodeConstructor as unknown as Dict<Function>;
                    const method = ctor[name];
                    if (!ctor[name]) {
                        throw factory.logicError(`missing interop method: ${name}`);
                    }
                    ctor[name] = this.wrapOperation(factory, operation, method);
                });
        });
    }
}
