import {
    Context,
    Dict,
    FriendlyParameter,
    Logger,
    Node,
    NodeFactory,
    Parameter,
    PlainOperationDecorator,
    asyncArray,
    getAsync,
} from '@sage/xtrem-core';
import * as _ from 'lodash';
import { RemoteNodeInfo, RemoteNodeOperation } from '../nodes';
import { InteropClient } from './interop-client';
import { InteropMapper, Mappings, Payload, Selector } from './interop-mapper';
import { InteropOperationTransformation, OperationTransformData } from './interop-operation-transformation';
import { InteropTransform } from './interop-transform';
import { MetadataClient } from './metadata-client';
import { RemoteInfoCache } from './remote-info-cache';

const logger = Logger.getLogger(__filename, 'operation');

interface ParameterInfo {
    type: string;
    remainingPath: string[];
    node?: string;
    dataType?: string;
    isArray?: boolean;
}

export class InteropOperationClient extends InteropClient {
    constructor(private readonly transformationData: OperationTransformData) {
        super(transformationData);
    }

    get remoteNodeName(): string {
        return this.transformationData.remoteNodeName;
    }

    get remotePackageName(): string {
        return this.transformationData.remotePackageName;
    }

    get remoteOperationName(): string {
        return this.transformationData.remoteOperationName;
    }

    #metadataClient: MetadataClient;

    get metadataClient(): MetadataClient {
        if (this.#metadataClient) return this.#metadataClient;

        this.#metadataClient = new MetadataClient({
            remoteAppName: this.remoteAppName,
            remoteInteropPackageName: this.remoteInteropPackageName,
            remoteIsConnector: this.remoteIsConnector,
        });

        return this.#metadataClient;
    }

    #remoteNodeInfo: RemoteNodeInfo;

    async getRemoteNodeInfo(context: Context): Promise<RemoteNodeInfo> {
        if (this.#remoteNodeInfo) return this.#remoteNodeInfo;

        this.#remoteNodeInfo = await this.metadataClient.findNodeInfo(context, this.remoteNodeName);

        return this.#remoteNodeInfo;
    }

    #remoteOperation: RemoteNodeOperation;

    async getRemoteOperation(context: Context): Promise<RemoteNodeOperation> {
        if (this.#remoteOperation) return this.#remoteOperation;
        const remoteNodeInfo = await this.getRemoteNodeInfo(context);
        const remoteOperation = remoteNodeInfo.operations.find(op => op.name === this.remoteOperationName);
        if (!remoteOperation)
            throw new Error(
                `${this.remoteAppName}: Operation ${this.remoteOperationName} not found in ${this.remoteNodeName}`,
            );
        this.#remoteOperation = remoteOperation;
        return this.#remoteOperation;
    }

    private get localFactory(): NodeFactory {
        return this.transformationData.localFactory;
    }

    private get localOperationName(): string {
        return this.transformationData.localOperationName;
    }

    private get localOperation(): PlainOperationDecorator {
        const operation =
            this.localFactory.queries.find(op => op.name === this.localOperationName) ??
            this.localFactory.mutations.find(op => op.name === this.localOperationName);

        if (!operation) throw this.logicError(`Local operation ${this.localOperationName} not found`);

        return operation;
    }

    #transform: InteropOperationTransformation;

    get transform(): InteropOperationTransformation {
        if (this.#transform) return this.#transform;
        this.#transform = new InteropOperationTransformation(this.transformationData);
        return this.#transform;
    }

    #selector: Selector;

    async getSelector(context: Context): Promise<Selector> {
        if (this.#selector) return this.#selector;
        this.#selector = await this.getRemoteOperationReturnSelector(context, this.transform.mappings.returnMappings);
        return this.#selector;
    }

    walkParameterPath(context: Context, parameterPath: string[], parameter: Parameter): ParameterInfo {
        const [name, ...rest] = parameterPath;

        switch (parameter.type) {
            case 'enum': {
                const dataType = parameter.dataType ? parameter.dataType() : undefined;
                if (!dataType) throw this.logicError(`dataType missing on enum parameter`);

                return { type: 'enum', dataType: dataType.name, remainingPath: rest };
            }
            case 'object': {
                if (name && parameter.properties) {
                    const objectProperty = parameter.properties[name];

                    if (!objectProperty) throw this.logicError(`Parameter object property ${name} not found`);

                    if (typeof objectProperty === 'string') {
                        // probably json or object or scalar. We will discover it later when mapping the response
                        return { type: objectProperty, remainingPath: rest };
                    }

                    if (
                        rest.length === 0 ||
                        objectProperty.type === 'reference' ||
                        objectProperty.type === 'instance'
                    ) {
                        return {
                            type: objectProperty.type,
                            remainingPath: rest,
                            node:
                                objectProperty.type === 'reference' || objectProperty.type === 'instance'
                                    ? objectProperty.node().name
                                    : undefined,
                        };
                    }

                    return this.walkParameterPath(context, rest, objectProperty);
                }
                return { type: 'object', remainingPath: rest };
            }

            case 'array':
                if (rest.length > 0 && parameter.item) {
                    if (typeof parameter.item === 'string') {
                        // probably json or object or scalar. We will discover it later when mapping the response
                        return { type: parameter.item, remainingPath: rest, isArray: true };
                    }

                    if (name !== '') {
                        this.logicError(
                            `Invalid path ${parameterPath.join('.')} for array parameter ${parameter.name}`,
                        );
                    }

                    if (parameter.item.type === 'object') {
                        return this.walkParameterPath(context, parameterPath, parameter.item);
                    }

                    return this.walkParameterPath(context, rest, parameter.item);
                }
                return { type: 'array', remainingPath: rest };
            case 'reference':
            case 'instance': {
                const targetFactory = context.application.getFactoryByConstructor(parameter.node());
                return { type: parameter.type, remainingPath: parameterPath, node: targetFactory.name };
            }
            default:
                return { type: parameter.type, remainingPath: rest };
        }
    }

    walkRemoteParameterPath(parameterPath: string[], parameter: FriendlyParameter): ParameterInfo {
        const [, ...rest] = parameterPath;

        if (typeof parameter === 'string')
            throw new Error(`Cannot walk path on ${parameter} parameter ${parameterPath}`);

        switch (parameter.type) {
            case 'enum': {
                const dataType = parameter.dataType ? parameter.dataType : undefined;
                if (!dataType) throw this.logicError(`dataType missing on enum parameter`);

                return { type: 'enum', dataType, remainingPath: rest };
            }
            case 'object': {
                if (rest.length > 0 && parameter.properties) {
                    const [objectPropertyName, ...objectRest] = rest;
                    const objectProperty = parameter.properties[objectPropertyName];

                    if (!objectProperty)
                        throw this.logicError(`Parameter object property ${objectPropertyName} not found`);

                    if (typeof objectProperty === 'string') {
                        // probably json or object or scalar. We will discover it later when mapping the response
                        return { type: objectProperty, remainingPath: objectRest };
                    }

                    if (objectRest.length === 0) {
                        return {
                            type: objectProperty.type,
                            remainingPath: objectRest,
                            node: objectProperty.node,
                        };
                    }

                    return this.walkRemoteParameterPath(objectRest, objectProperty);
                }
                return { type: 'object', remainingPath: rest };
            }

            case 'array':
                if (rest.length > 0 && parameter.item) {
                    const [arrayIndex, ...arrayRest] = rest;
                    if (typeof parameter.item === 'string') {
                        // probably json or object or scalar. We will discover it later when mapping the response
                        return { type: parameter.item, remainingPath: arrayRest, isArray: true };
                    }

                    if (arrayIndex !== '') {
                        this.logicError(
                            `Invalid path ${parameterPath.join('.')} for array parameter ${parameter.name}`,
                        );
                    }

                    return this.walkRemoteParameterPath(arrayRest, parameter.item);
                }
                return { type: 'array', remainingPath: rest };
            case 'reference':
            case 'instance': {
                return { type: parameter.type, remainingPath: rest, node: parameter.node };
            }
            default:
                return { type: parameter.type, remainingPath: rest };
        }
    }

    findLocalParameterFromPath(context: Context, localPath: string[], parameters: Parameter[]): ParameterInfo {
        const [name, ...rest] = localPath;

        if (name === '') {
            const arrayParamter = parameters[0];
            if (arrayParamter.type !== 'array')
                throw this.logicError(
                    `Parameter ${localPath} has an array in the mapping path, but cannot find array parameter in operation definition`,
                );
            if (typeof arrayParamter.item === 'string')
                return { type: arrayParamter.item, remainingPath: rest, isArray: true };
            return this.walkParameterPath(context, rest, arrayParamter.item);
        }
        let parameter = parameters.find(param => param.name === name);
        if (name === '$$value') parameter = parameters[0];
        if (!parameter) throw new Error(`Parameter ${name} not found`);
        if (rest.length === 0)
            return {
                type: parameter.type,
                remainingPath: [],
                isArray: parameter.type === 'array',
                node:
                    parameter.type === 'reference' || parameter.type === 'instance' ? parameter.node().name : undefined,
            };
        return this.walkParameterPath(context, rest, parameter);
    }

    async getRemoteOperationReturnSelector(context: Context, returnMappings: Mappings): Promise<Selector> {
        const returnSignature = (await this.getRemoteOperation(context)).signature.return;

        const complexTypes = ['object', 'reference', 'instance', 'array'];

        const remotePaths = Object.values(returnMappings).filter(mapping => mapping.kind === 'path');

        if (
            typeof returnSignature === 'string' ||
            (typeof returnSignature !== 'string' && !complexTypes.includes(returnSignature.type))
        ) {
            if (remotePaths.length > 1 || !remotePaths.every(path => path.value === '$$value'))
                throw new Error(
                    `${this.remoteAppName}: Operation ${this.remoteOperationName} in ${this.remoteNodeName} return type is not complex, but path mappings are present`,
                );
            return true;
        }

        const localOperation = this.localOperation;

        if (typeof localOperation.return === 'string') {
            throw new Error(`${this.localOperationName}: Return type is not complex`);
        }

        if (localOperation.return.type === 'array' && typeof localOperation.return.item === 'string') {
            return true;
        }

        const setReturnParameters = (parameter: Parameter): Parameter[] => {
            if (parameter.type === 'object') {
                return Object.entries(parameter.properties).map(([name, property]) => {
                    if (typeof property === 'string') {
                        return {
                            name,
                            type: property,
                        } as Parameter;
                    }
                    return {
                        ...property,
                        name,
                    };
                });
            }

            if (parameter.type === 'array' && typeof parameter.item !== 'string') {
                return setReturnParameters(parameter.item);
            }

            return [parameter];
        };

        const returnParameters = setReturnParameters(localOperation.return);

        return InteropMapper.getMappingsSelector(
            returnMappings,
            async (localPath: string, remotePath: string, result: Dict<Selector>) => {
                let parameterInfo = this.findLocalParameterFromPath(context, localPath.split('.'), returnParameters);
                const remoteParameter =
                    returnSignature.type === 'array' && returnSignature.item
                        ? this.walkRemoteParameterPath(remotePath.split('.'), returnSignature.item)
                        : this.walkRemoteParameterPath(remotePath.split('.'), returnSignature);
                if (
                    ['reference', 'instance'].includes(parameterInfo.type) &&
                    parameterInfo.node &&
                    parameterInfo.remainingPath.length > 0
                ) {
                    const walkRemainingNodePath = (factory: NodeFactory, propertyPath: string[]): ParameterInfo => {
                        const [propertyName, ...propertyRest] = propertyPath;
                        const property = factory.findProperty(propertyName);

                        if (propertyRest.length === 0)
                            return {
                                type: property.type,
                                remainingPath: [],
                                node: property.isForeignNodeProperty() ? property.targetFactory.name : undefined,
                                dataType: property.dataType?.name,
                                isArray: property.isArrayProperty(),
                            };

                        if (!property.isForeignNodeProperty()) {
                            throw new Error(`Property ${propertyName} is not a foreign node property`);
                        }

                        const nextFactory = property.targetFactory;

                        return walkRemainingNodePath(nextFactory, propertyRest);
                    };

                    parameterInfo = walkRemainingNodePath(
                        context.application.getFactoryByName(parameterInfo.node),
                        parameterInfo.remainingPath,
                    );
                }

                if (['reference', 'instance'].includes(parameterInfo.type) && parameterInfo.node) {
                    if (!['reference', 'instance'].includes(remoteParameter.type))
                        throw this.logicError(
                            `${localPath}: Expected parameter to be mapped to a reference or instance, but got ${remoteParameter.type}`,
                        );

                    const targetFactory = context.application.getFactoryByName(parameterInfo.node);
                    const remoteAppVersion = (await RemoteInfoCache.fetchRemoteInfo(context, this.options))
                        .remoteAppVersion;
                    const transform = await InteropTransform.findNodeTransform(context, targetFactory, {
                        remoteAppName: this.remoteAppName,
                        remoteAppVersion,
                    });

                    if (remoteParameter.node !== transform.remoteNodeName) {
                        throw this.logicError(
                            `remote parameter ${remotePath} expects node ${remoteParameter.node}, but local parameter ${localPath} is mapped to node ${transform.remoteNodeName}`,
                        );
                    }

                    if (!targetFactory.naturalKey) throw new Error(`${targetFactory.name}: Missing natural key`);
                    const naturalKeyMappings = targetFactory.naturalKey.reduce((r, k) => {
                        const mapping = transform.mappings[k];
                        if (mapping == null) throw Error(`${targetFactory.name}: Missing natural key mapping for ${k}`);
                        r[k] = mapping;
                        return r;
                    }, {} as Mappings);
                    const selector = await InteropTransform.getRemoteNodeMappingsSelector(
                        context,
                        targetFactory,
                        naturalKeyMappings,
                        this.remoteAppName,
                        remoteAppVersion,
                    );
                    if (remotePath === '$$value') {
                        _.merge(result, selector);
                    } else {
                        _.set(result, remotePath, selector);
                    }
                } else {
                    _.set(result, remotePath, true);
                }
            },
        );
    }

    static async getNodeValue(
        context: Context,
        parameterInfo: ParameterInfo,
        localPath: string,
        value: any,
    ): Promise<Node> {
        if (!parameterInfo.node) throw new Error(`${localPath}: Missing target node`);
        const targetFactory = context.application.getFactoryByName(parameterInfo.node);
        const naturalKey = targetFactory.naturalKey;
        if (naturalKey == null) throw new Error(`${targetFactory.name}: Missing natural key`);
        const naturalKeyValues = await asyncArray(naturalKey)
            .map(k => getAsync(value, k))
            .toArray();
        if (naturalKeyValues.length !== naturalKey.length) throw new Error(`${localPath}: Missing natural key values`);
        const filter = { _id: `#${naturalKeyValues.join('|')}` };

        return context.read(targetFactory.nodeConstructor, filter);
    }

    static async setNodeParameter(
        context: Context,
        result: any,
        parameterInfo: ParameterInfo,
        localPath: string,
        value: any,
    ): Promise<void> {
        if (parameterInfo.type === 'reference' || parameterInfo.type === 'instance') {
            if (typeof value !== 'object') {
                throw new Error(`${localPath}: Missing natural key values in ${parameterInfo.type} parameter`);
            }

            const node = await InteropOperationClient.getNodeValue(context, parameterInfo, localPath, value);
            _.set(result, localPath, node);
        }
    }

    async walkResultPath(
        context: Context,
        result: any,
        returnParameters: Parameter[],
        localPath: string,
        path: string[],
        parentResult: any,
        valuePath: string[] = [],
        pathSoFar: string[] = [],
    ): Promise<void> {
        const [name, ...rest] = path;

        // array
        if (name === '') {
            if (!parentResult) return;
            if (!Array.isArray(parentResult)) throw new Error(`${pathSoFar.join('.')}: Expected array`);
            await asyncArray(parentResult).forEach(async (item, index) => {
                await this.walkResultPath(
                    context,
                    result,
                    returnParameters,
                    localPath,
                    rest,
                    item,
                    [...valuePath, String(index)],
                    [...pathSoFar, name],
                );
            });
            return;
        }

        const currentPath = [...pathSoFar, name];
        const currentValuePath = [...valuePath, name];

        const parameterInfo = this.findLocalParameterFromPath(context, currentPath, returnParameters);

        const value = (await getAsync(parentResult, name)) as any;

        if (value == null) return;

        await InteropOperationClient.setNodeParameter(
            context,
            result,
            parameterInfo,
            currentValuePath.join('.'),
            value,
        );

        if (rest.length !== 0 && parameterInfo.type !== 'reference' && parameterInfo.type !== 'instance') {
            await this.walkResultPath(
                context,
                result,
                returnParameters,
                localPath,
                rest,
                parentResult[name],
                currentValuePath,
                currentPath,
            );
        }
    }

    async parseResult(context: Context, result: Payload): Promise<Payload> {
        if (result.$$value !== undefined && (typeof result.$$value !== 'object' || result.$$value === null))
            return result.$$value;
        const localPaths = Object.keys(this.transform.mappings.returnMappings);
        const localOperation = this.localOperation;

        if (typeof localOperation.return === 'string') return result;
        let returnParameters = [localOperation.return];
        if (localOperation.return.type === 'object') {
            returnParameters = Object.entries(localOperation.return.properties).map(([name, property]) => {
                if (typeof property === 'string') {
                    return {
                        name,
                        type: property,
                    } as Parameter;
                }
                return {
                    ...property,
                    name,
                };
            });
        }

        await asyncArray(localPaths).forEach(async localPath => {
            await this.walkResultPath(context, result, returnParameters, localPath, localPath.split('.'), result);
        });

        if (result.$$value !== undefined) return result.$$value;

        return result;
    }

    async parseArguments(context: Context, args: Dict<any>): Promise<string> {
        const remoteAppVersion = (await RemoteInfoCache.fetchRemoteInfo(context, this.options)).remoteAppVersion;

        return (
            await asyncArray(Object.entries(args))
                .map(async ([key, value]) => {
                    // if the value is an array or object, we need to recurse as we do not want to stringify it
                    if (_.isArray(value)) {
                        return `${key}: [${(
                            await asyncArray(value)
                                .map(async v => {
                                    if (v instanceof Node) {
                                        return `"#${await InteropTransform.getRemoteNaturalValue(this.metadataClient, remoteAppVersion, v)}"`;
                                    }
                                    return typeof v === 'object' ? this.parseArguments(context, v) : JSON.stringify(v);
                                })
                                .toArray()
                        ).join(',')}]`;
                    }
                    if (value instanceof Node) {
                        return `${key}: "#${await InteropTransform.getRemoteNaturalValue(this.metadataClient, remoteAppVersion, value)}"`;
                    }
                    if (typeof value === 'object' && value != null)
                        return `${key}: { ${await this.parseArguments(context, value)} }`;
                    return `${key}: ${JSON.stringify(value)}`;
                })
                .toArray()
        ).join(', ');
    }

    private async constructGraphQLRequest(context: Context, localArgs: Dict<any>): Promise<string> {
        const remoteArgs = await this.transform.transformArgs(localArgs);
        const selector = await this.getSelector(context);

        const remoteArgsStr = await this.parseArguments(context, remoteArgs);
        const parenthesizedArgsStr = remoteArgsStr ? `(${remoteArgsStr})` : '';

        const selectorStr = InteropMapper.formatSelector(selector);
        const remotePackageKey = _.camelCase(this.remotePackageName);
        const remoteNodeKey = _.camelCase(this.remoteNodeName);

        // TODO: async mutations?
        const { isMutation } = await this.getRemoteOperation(context);

        const prefix = isMutation ? 'mutation' : 'query';

        return `${prefix} {
            ${remotePackageKey} {
                ${remoteNodeKey} {
                    ${this.remoteOperationName}${parenthesizedArgsStr} ${selectorStr}
                }
            }
        }`;
    }

    /**
     * Retrieves a synchronization feed for a given node.
     * @param context The context object.
     * @param nodeName The name of the node.
     * @param startTick The starting tick.
     * @param selector The client selector.
     * @param initialCount The initial count.
     * @returns A promise that resolves to a synchronization feed.
     */
    async execute(context: Context, localArgs: Dict<any>): Promise<Payload> {
        const graphqlRequest = await this.constructGraphQLRequest(context, localArgs);

        logger.verbose(() => `Sending interop query to ${this.remoteAppName}: ${graphqlRequest}`);
        const result = await this.fetch<Payload>(context, graphqlRequest, {});
        logger.verbose(() => `Interop response from ${this.remoteAppName}: ${JSON.stringify(result.data)}`);

        const data = result.data;

        const remotePackageKey = _.camelCase(this.remotePackageName);
        const remoteNodeKey = _.camelCase(this.remoteNodeName);
        let returnPayload = data?.data?.[remotePackageKey]?.[remoteNodeKey]?.[this.remoteOperationName];
        if (returnPayload == null)
            throw new Error(`No data returned from ${this.remoteNodeName}.${this.remoteOperationName}: ${data.errors}`);
        // TODO: Confirm if $$value is good enough when we have a simple result
        const selector = await this.getSelector(context);
        if (
            Object.values(this.transform.mappings.returnMappings).find(
                mapping => mapping.kind === 'path' && mapping.value === '$$value',
            )
        ) {
            returnPayload = { $$value: returnPayload };
        } else if (selector === true) {
            throw new Error(`No return mappings found for simple return value`);
        }

        const parsedResult = await this.parseResult(context, await this.transform.transformReturn(returnPayload));
        logger.verbose(() => `Parsed result: ${JSON.stringify(parsedResult)}`);

        return parsedResult;
    }
}
