import { Node, Reference, decorators } from '@sage/xtrem-core';
import * as xtremInterop from '../index';

/**
 * A transformation between a remote node and a local node.
 */
@decorators.node<SysNodeMapping>({
    storage: 'sql',
    isPublished: true,
    canRead: true,
    canCreate: false,
    canUpdate: false,
    canDelete: true,
    canDeleteMany: true,
    isSetupNode: true,
    isCached: true,
    isVitalCollectionChild: true,
    serviceOptions: () => [xtremInterop.serviceOptions.synchronizationServiceOption],
})
export class SysNodeMapping extends Node {
    @decorators.referenceProperty<SysNodeMapping, 'transform'>({
        isStored: true,
        isPublished: true,
        isVitalParent: true,
        isRequired: true,
        node: () => xtremInterop.nodes.SysNodeTransformation,
    })
    readonly transform: Reference<xtremInterop.nodes.SysNodeTransformation>;

    @decorators.stringProperty<SysNodeMapping, 'localProperty'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        dataType: () => xtremInterop.dataTypes.mappingValue,
    })
    readonly localProperty: Promise<string>;

    @decorators.stringProperty<SysNodeMapping, 'remoteProperty'>({
        isStored: true,
        isPublished: true,

        dataType: () => xtremInterop.dataTypes.mappingValue,
    })
    readonly remoteProperty: Promise<string>;

    @decorators.enumProperty<SysNodeMapping, 'kind'>({
        isStored: true,
        isPublished: true,
        defaultValue: 'path_to_path',
        lookupAccess: true,
        isRequired: true,
        dataType: () => xtremInterop.enums.NodeMappingKindDataType,
    })
    readonly kind: Promise<xtremInterop.enums.NodeMappingKind>;
}
