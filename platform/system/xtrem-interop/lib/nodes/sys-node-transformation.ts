import { AnyRecord, Collection, Context, Node, Reference, date, decorators } from '@sage/xtrem-core';
import * as xtremMetadata from '@sage/xtrem-metadata';
import * as xtremSystem from '@sage/xtrem-system';
import { InteropMapper, Mappings } from '../classes/interop-mapper';
import { InteropTransform } from '../classes/interop-transform';
import { MetadataClient } from '../classes/metadata-client';
import * as xtremInterop from '../index';
import { UpdateActiveMappingsParameters } from './sys-enum-transformation';

/**
 * A transformation between a remote node and a local node.
 */
@decorators.node<SysNodeTransformation>({
    storage: 'sql',
    isPublished: true,
    canRead: true,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canDeleteMany: true,
    canDuplicate: true,
    isSetupNode: true,
    isCached: true,
    serviceOptions: () => [xtremInterop.serviceOptions.synchronizationServiceOption],
    indexes: [
        { orderBy: { localNode: 1, remoteApp: 1, remoteAppVersion: 1, id: 1 }, isUnique: true, isNaturalKey: true },
    ],
    async controlEnd(cx) {
        if (await this.isActive) {
            const factory = this.$.context.application.getFactoryByName(await (await this.localNode).name);
            const app = await this.remoteApp;
            const metadataClient = new MetadataClient({
                remoteAppName: await app.name,
                remoteInteropPackageName: await app.interopPackage,
                remoteIsConnector: await app.isConnector,
            });
            await InteropMapper.controlMappings(
                cx,
                factory,
                await this.remoteNodeName,
                await this.mappings,
                metadataClient,
            );
        }
    },
})
export class SysNodeTransformation extends Node {
    /** The id of the transform */
    @decorators.stringProperty<SysNodeTransformation, 'id'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.id,
        duplicatedValue: '',
        duplicateRequiresPrompt: true,
    })
    readonly id: Promise<string>;

    /** Is the transform active? */
    @decorators.booleanProperty<SysNodeTransformation, 'isActive'>({
        isStored: true,
        isPublished: true,
        defaultValue: true,
        provides: ['isActive'],
        isOwnedByCustomer: true,
        duplicatedValue: false,
    })
    readonly isActive: Promise<boolean>;

    /** The remote app */
    @decorators.referenceProperty<SysNodeTransformation, 'remoteApp'>({
        isStored: true,
        isPublished: true,
        node: () => xtremInterop.nodes.SysApp,
    })
    readonly remoteApp: Reference<xtremInterop.nodes.SysApp>;

    /** The version of the remote app */
    @decorators.stringProperty<SysNodeTransformation, 'remoteAppVersion'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.version,
    })
    readonly remoteAppVersion: Promise<string>;

    /** The name of the remote node */
    @decorators.stringProperty<SysNodeTransformation, 'remoteNodeName'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.name,
    })
    readonly remoteNodeName: Promise<string>;

    /** The node factory which is synchronized */
    @decorators.referenceProperty<SysNodeTransformation, 'localNode'>({
        isStored: true,
        isPublished: true,
        node: () => xtremMetadata.nodes.MetaNodeFactory,
    })
    readonly localNode: Reference<xtremMetadata.nodes.MetaNodeFactory>;

    /** The transform mappings */
    @decorators.jsonProperty<SysNodeTransformation, 'mappings'>({
        isPublished: true,
        async computeValue() {
            const nodeMappings = await this.map
                .map(async m => {
                    return {
                        kind: await m.kind,
                        localProperty: await m.localProperty,
                        remoteProperty: await m.remoteProperty,
                    };
                })
                .toArray();
            const factory = this.$.context.application.getFactoryByName(await (await this.localNode).name);
            return InteropTransform.constructMappings(factory, nodeMappings);
        },
    })
    readonly mappings: Promise<Mappings>;

    /** The transform mappings */
    @decorators.jsonProperty<SysNodeTransformation, 'remoteMappings'>({
        isPublished: true,
        async computeValue() {
            const nodeMappings = await this.map
                .map(async m => {
                    return {
                        kind: await m.kind,
                        localProperty: await m.localProperty,
                        remoteProperty: await m.remoteProperty,
                    };
                })
                .toArray();
            return InteropTransform.constructRemoteMappings(nodeMappings);
        },
    })
    readonly remoteMappings: Promise<Mappings>;

    /** The transform mappings */
    @decorators.jsonProperty<SysNodeTransformation, 'filter'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
    })
    readonly filter: Promise<AnyRecord | null>;

    @decorators.dateProperty<SysNodeTransformation, 'lastError'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        duplicatedValue: null,
    })
    readonly lastError: Promise<date>;

    @decorators.dateProperty<SysNodeTransformation, 'lastSync'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        duplicatedValue: null,
    })
    readonly lastSync: Promise<date>;

    @decorators.collectionProperty<SysNodeTransformation, 'map'>({
        isPublished: true,
        isVital: true,
        node: () => xtremInterop.nodes.SysNodeMapping,
        reverseReference: 'transform',
    })
    readonly map: Collection<xtremInterop.nodes.SysNodeMapping>;

    /**
     * This method updates the isActive flag on the SysEnumTransformation node for the given remote app'
     * @param context
     * @param parameters includes remoteAppId, active, and version
     */
    @decorators.mutation<typeof SysNodeTransformation, 'updateActiveMappings'>({
        isPublished: true,
        parameters: [
            {
                name: 'parameters',
                type: 'object',
                isMandatory: true,
                properties: {
                    remoteAppId: {
                        type: 'string',
                        isMandatory: true,
                    },
                    active: {
                        type: 'boolean',
                        isMandatory: true,
                    },
                    version: {
                        type: 'string',
                        isMandatory: false,
                    },
                },
            },
        ],
        return: {
            type: 'boolean',
        },
    })
    static updateActiveMappings(context: Context, parameters: UpdateActiveMappingsParameters): Promise<boolean> {
        return xtremInterop.functions.updateActiveMappings(context, {
            nodeToUpdate: SysNodeTransformation,
            remoteAppId: parameters.remoteAppId,
            active: parameters.active,
            version: parameters.version,
        });
    }

    @decorators.query<typeof SysNodeTransformation, 'hasActiveMapping'>({
        isPublished: true,
        parameters: [
            {
                name: 'localNode',
                type: 'string',
            },
            {
                name: 'propertyId',
                type: 'string',
            },
        ],
        return: {
            type: 'boolean',
        },
    })
    static hasActiveMapping(context: Context, localNode: string, propertyId: string): Promise<boolean> {
        const factory = context.application.getFactoryByName(localNode);
        return xtremInterop.functions.hasActiveTransformMapping(context, factory, propertyId);
    }
}
