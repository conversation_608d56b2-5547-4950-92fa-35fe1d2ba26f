import { Context, FriendlyParameter, Node, decorators } from '@sage/xtrem-core';
import * as xtremMetadata from '@sage/xtrem-metadata';
import { Dict } from '@sage/xtrem-shared';
import { MetadataClient } from '../classes/metadata-client';
import * as xtremInterop from '../index';
import { SysApp } from './sys-app';

export interface RemoteDataTypeInfo {
    name: string;
    title: string;
    attributes: object;
}

export interface RemoteNodeProperty {
    name: string;
    title: string;
    type: string;
    dataType?: string | null;
    isStored: boolean;
    targetNode?: string | null;
    isTransientInput: boolean;
    isStoredOutput: boolean;
    isRequired: boolean;
    isNullable: boolean;
    isCustom: boolean;
    canSort: boolean;
    canFilter: boolean;
    isOnInputType: boolean;
    isOnOutputType: boolean;
    isMutable: boolean;
    isVitalParent: boolean;
}

export interface RemoteNodeOperation {
    name: string;
    title: string;
    action?: string | null;
    kind: xtremMetadata.enums.MetaOperationKind;
    signature: { parameters: FriendlyParameter[]; return: FriendlyParameter };
    isMutation: boolean;
}

export interface RemoteNodeInfo {
    name: string;
    storage: string;
    packageName: string;
    title: string;
    isPlatformNode: boolean;
    naturalKey: string[] | null;
    properties: RemoteNodeProperty[];
    operations: RemoteNodeOperation[];
}

@decorators.node<SysRemoteMetadata>({
    isPublished: true,
    serviceOptions: () => [xtremInterop.serviceOptions.synchronizationServiceOption],
})
export class SysRemoteMetadata extends Node {
    /**
     *  Returns the remote dataType metadata.
     *
     * @param context the context
     * @param app remote application
     * @param name dataType name
     * @param type dataType type
     * @returns list of dataType metadata
     */
    @decorators.query<typeof SysRemoteMetadata, 'getDataTypeInfo'>({
        isPublished: true,
        parameters: [
            {
                name: 'app',
                type: 'reference',
                node: () => SysApp,
                isMandatory: true,
            },
            {
                name: 'name',
                type: 'string',
            },
            {
                name: 'type',
                type: 'string',
            },
        ],
        return: {
            type: 'array',
            item: {
                type: 'object',
                properties: {
                    name: 'string',
                    title: 'string',
                    attributes: 'json',
                },
            },
        },
    })
    static async getDataTypeInfo(
        context: Context,
        app: SysApp,
        name?: string,
        type?: string,
    ): Promise<RemoteDataTypeInfo[]> {
        const client = new MetadataClient({
            remoteAppName: await app.name,
            remoteInteropPackageName: await app.interopPackage,
            remoteIsConnector: await app.isConnector,
        });

        return client.getDataTypeInfo(context, name, type);
    }

    /**
     *  Returns the remote node metadata.
     *
     * @param context the context
     * @param app remote application
     * @param name node name
     * @returns list of dataType metadata
     */
    @decorators.query<typeof SysRemoteMetadata, 'getNodeInfo'>({
        isPublished: true,
        parameters: [
            {
                name: 'app',
                type: 'reference',
                node: () => SysApp,
                isMandatory: true,
            },
            {
                name: 'name',
                type: 'string',
            },
            {
                name: 'filter',
                type: 'json',
            },
        ],
        return: {
            type: 'array',
            item: {
                type: 'object',
                properties: {
                    name: 'string',
                    title: 'string',
                    packageName: 'string',
                    naturalKey: { type: 'array', item: 'string' },
                    properties: {
                        type: 'array',
                        item: {
                            type: 'object',
                            properties: {
                                name: 'string',
                                title: 'string',
                                type: 'string',
                                dataType: 'string',
                                isStored: 'boolean',
                                targetNode: 'string',
                                isTransientInput: 'boolean',
                                isStoredOutput: 'boolean',
                                isRequired: 'boolean',
                                isNullable: 'boolean',
                                isCustom: 'boolean',
                                canSort: 'boolean',
                                canFilter: 'boolean',
                                isOnInputType: 'boolean',
                                isOnOutputType: 'boolean',
                                isMutable: 'boolean',
                                isVitalParent: 'boolean',
                            },
                        },
                    },
                    operations: {
                        type: 'array',
                        item: {
                            type: 'object',
                            properties: {
                                name: 'string',
                                title: 'string',
                                kind: 'string',
                                action: 'string',
                                isMutation: 'boolean',
                                signature: {
                                    type: 'object',
                                    properties: {
                                        parameters: {
                                            type: 'array',
                                            item: {
                                                type: 'object',
                                                properties: {
                                                    name: 'string',
                                                    type: 'string',
                                                },
                                            },
                                        },
                                        return: 'json',
                                    },
                                },
                            },
                        },
                    },
                },
            },
        },
    })
    static async getNodeInfo(
        context: Context,
        app: SysApp,
        name?: string,
        filter?: Dict<any>,
    ): Promise<RemoteNodeInfo[]> {
        const client = new MetadataClient({
            remoteAppName: await app.name,
            remoteInteropPackageName: await app.interopPackage,
            remoteIsConnector: await app.isConnector,
        });

        return client.getNodeInfo(context, name, {
            filter,
        });
    }
}
