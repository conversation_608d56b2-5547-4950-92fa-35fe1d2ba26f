import { Collection, Context, EnumDataType, Node, Reference, decorators } from '@sage/xtrem-core';
import * as xtremMetadata from '@sage/xtrem-metadata';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremInterop from '../index';

export interface UpdateActiveMappingsParameters {
    remoteAppId: string;
    active: boolean;
    version?: string;
}

/**
 * A transformation between a remote node and a local node.
 */
@decorators.node<SysEnumTransformation>({
    storage: 'sql',
    isPublished: true,
    canRead: true,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canDeleteMany: true,
    canDuplicate: true,
    isSetupNode: true,
    isCached: true,
    serviceOptions: () => [xtremInterop.serviceOptions.synchronizationServiceOption],
    indexes: [
        {
            orderBy: { localEnum: 1, remoteApp: 1, remoteAppVersion: 1, id: 1 },
            isUnique: true,
            isNaturalKey: true,
        },
    ],
    async controlEnd(cx) {
        const enumDataType = this.$.context.application.dataTypes[await (await this.localEnum).name];

        if (enumDataType instanceof EnumDataType) {
            await this.map.forEach(async mapping => {
                const localValue = await mapping.localEnumValue;
                if (!enumDataType.values.includes(localValue)) {
                    cx.addError(
                        `Invalid enum mapping: ${localValue} is not a valid value for enum ${enumDataType.name}`,
                    );
                }
            });
        }
    },
})
export class SysEnumTransformation extends Node {
    /** The id of the transform */
    @decorators.stringProperty<SysEnumTransformation, 'id'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.id,
        async duplicatedValue() {
            return `${await this.id}-copy`;
        },
        duplicateRequiresPrompt: true,
    })
    readonly id: Promise<string>;

    /** The node factory which is synchronized */
    @decorators.referenceProperty<SysEnumTransformation, 'localEnum'>({
        isStored: true,
        isPublished: true,
        node: () => xtremMetadata.nodes.MetaDataType,
        filters: {
            lookup: {
                type: 'enum',
            },
            control: {
                type: 'enum',
            },
        },
        async control(cx, value) {
            const enumDataTypeName = await value.name;
            const enumDataType = this.$.context.application.dataTypes[enumDataTypeName];

            if (!enumDataType || !(enumDataType instanceof EnumDataType)) {
                cx.addError(`Invalid enum transformation: ${enumDataTypeName} is not an enum`);
            }
        },
    })
    readonly localEnum: Reference<xtremMetadata.nodes.MetaDataType>;

    /** Is the transform active? */
    @decorators.booleanProperty<SysEnumTransformation, 'isActive'>({
        isStored: true,
        isPublished: true,
        defaultValue: true,
        provides: ['isActive'],
        isOwnedByCustomer: true,
        duplicatedValue: false,
    })
    readonly isActive: Promise<boolean>;

    /** The remote app */
    @decorators.referenceProperty<SysEnumTransformation, 'remoteApp'>({
        isStored: true,
        isPublished: true,
        node: () => xtremInterop.nodes.SysApp,
    })
    readonly remoteApp: Reference<xtremInterop.nodes.SysApp>;

    /** The version of the remote app */
    @decorators.stringProperty<SysEnumTransformation, 'remoteAppVersion'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.version,
    })
    readonly remoteAppVersion: Promise<string>;

    /** The name of the remote node */
    @decorators.stringProperty<SysEnumTransformation, 'remoteEnum'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.name,
    })
    readonly remoteEnum: Promise<string>;

    @decorators.collectionProperty<SysEnumTransformation, 'map'>({
        isPublished: true,
        isVital: true,
        node: () => xtremInterop.nodes.SysEnumMapping,
        reverseReference: 'transform',
    })
    readonly map: Collection<xtremInterop.nodes.SysEnumMapping>;

    /**
     * This method updates the isActive flag on the SysEnumTransformation node for the given remote app'
     * @param context
     * @param parameters includes remoteAppId, active, and version
     */
    @decorators.mutation<typeof SysEnumTransformation, 'updateActiveMappings'>({
        isPublished: true,
        parameters: [
            {
                name: 'parameters',
                type: 'object',
                isMandatory: true,
                properties: {
                    remoteAppId: {
                        type: 'string',
                        isMandatory: true,
                    },
                    active: {
                        type: 'boolean',
                        isMandatory: true,
                    },
                    version: {
                        type: 'string',
                        isMandatory: false,
                    },
                },
            },
        ],
        return: {
            type: 'boolean',
        },
    })
    static updateActiveMappings(context: Context, parameters: UpdateActiveMappingsParameters): Promise<boolean> {
        return xtremInterop.functions.updateActiveMappings(context, {
            nodeToUpdate: SysEnumTransformation,
            remoteAppId: parameters.remoteAppId,
            active: parameters.active,
            version: parameters.version,
        });
    }
}
