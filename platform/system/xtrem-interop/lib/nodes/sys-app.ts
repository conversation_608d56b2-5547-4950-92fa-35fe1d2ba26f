import {
    ConfigManager,
    Context,
    InteropAppHealthMonitor,
    InteropAppInfo,
    Node,
    NodeStatus,
    decorators,
} from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import { RemoteInfoCache } from '../classes/remote-info-cache';
import { SysAppRefresh } from '../classes/sys-app-refresh';
import * as xtremInterop from '../index';

export interface AppInfo {
    name: string;
    version: string;
    metadataPackage: string;
}

@decorators.node<SysApp>({
    isPublished: true,
    storage: 'sql',
    isCached: true,
    isSetupNode: true,
    canExport: false,
    canCreate: true,
    canRead: true,
    canUpdate: true,
    canDeleteMany: true,
    serviceOptions: () => [xtremInterop.serviceOptions.synchronizationServiceOption],
    indexes: [
        {
            orderBy: { name: +1 },
            isUnique: true,
            isNaturalKey: true,
        },
    ],
    async saveEnd() {
        const isActive = await this.isActive;
        if (
            this.$.status === NodeStatus.added ||
            (this.$.status === NodeStatus.modified && isActive !== (await (await this.$.old).isActive))
        ) {
            const name = await this.name;
            await xtremInterop.nodes.SysEnumTransformation.updateActiveMappings(this.$.context, {
                remoteAppId: name,
                active: isActive,
            });
            await xtremInterop.nodes.SysNodeTransformation.updateActiveMappings(this.$.context, {
                remoteAppId: name,
                active: isActive,
            });
        }
    },
})
export class SysApp extends Node {
    @decorators.stringProperty<SysApp, 'name'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremSystem.dataTypes.name,
        control(cx, value) {
            if (!/^[a-z][a-z0-9_]*$/.test(value)) {
                cx.addError(`Invalid app name: expected snake_case, got ${value}`);
            }
        },
    })
    readonly name: Promise<string>;

    @decorators.stringProperty<SysApp, 'version'>({
        isPublished: true,
        lookupAccess: true,
        async computeValue() {
            const { app } = ConfigManager.current;
            const name = await this.name;
            if (app === name) {
                return this.$.context.application.rootAbout.version;
            }

            if (!(await this.isAlive)) {
                return '';
            }

            const remoteInfo = await RemoteInfoCache.fetchRemoteInfo(this.$.context, {
                remoteAppName: name,
                remoteInteropPackageName: await this.interopPackage,
                remoteIsConnector: await this.isConnector,
            }).catch(error => {
                this.$.context.logger.error(() => `Failed to fetch remote app version for ${name}, ${error}`);
                return null;
            });

            return remoteInfo?.remoteAppVersion ?? '';
        },
        dataType: () => xtremSystem.dataTypes.version,
    })
    readonly version: Promise<string>;

    @decorators.stringProperty<SysApp, 'title'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremSystem.dataTypes.localizedName,
    })
    readonly title: Promise<string>;

    @decorators.booleanProperty<SysApp, 'isConnector'>({
        isStored: true,
        isPublished: true,
    })
    readonly isConnector: Promise<boolean>;

    @decorators.stringProperty<SysApp, 'interopPackage'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.name,
    })
    readonly interopPackage: Promise<string>;

    @decorators.booleanProperty<SysApp, 'isActive'>({
        isStored: true,
        isPublished: true,
        provides: ['isActive'],
        isOwnedByCustomer: true,
    })
    readonly isActive: Promise<boolean>;

    @decorators.booleanProperty<SysApp, 'isAlive'>({
        isPublished: true,
        async computeValue() {
            return InteropAppHealthMonitor.isAppAlive(await this.name);
        },
    })
    readonly isAlive: Promise<boolean>;

    /**
     * This method creates or updates role-activity'
     * @param context
     * @param activities
     * @param role
     * @param roleId
     * @param roleName
     */
    @decorators.mutation<typeof SysApp, 'refreshState'>({
        isPublished: true,
        parameters: [],
        return: {
            type: 'boolean',
        },
    })
    static async refreshState(context: Context): Promise<boolean> {
        await SysAppRefresh.refreshApps(context.application);

        return true;
    }

    /**
     *  Returns the remote app's version.
     *
     * @param context the context
     * @returns a synchronization feed
     */
    @decorators.query<typeof SysApp, 'getAppInfo'>({
        isPublished: true,
        parameters: [],
        return: {
            type: 'object',
            properties: {
                name: 'string',
                version: 'string',
                metadataPackage: 'string',
            },
        },
    })
    static getAppInfo(context: Context): Promise<AppInfo> {
        const { appName, version } = context.application.rootAbout;
        return Promise.resolve({ name: appName || '', version, metadataPackage: 'xtrem-metadata' });
    }

    async toInteropAppInfo(): Promise<InteropAppInfo> {
        return {
            name: await this.name,
            title: await this.title,
            isConnector: await this.isConnector,
            interopPackage: await this.interopPackage,
            isActive: await this.isActive,
        };
    }
}
