import * as xtremCommunication from '@sage/xtrem-communication';
import { Collection, Node, Reference, _syncTickDataType, datetime, decimal, decorators } from '@sage/xtrem-core';
import * as xtremMetadata from '@sage/xtrem-metadata';
import * as xtremInterop from '../index';

/**
 * The synchronization state of node factories.
 *
 * This table contains one record for each node factory which has been synchronized at least once.
 * The node factory must be a _synchronization target_.
 * The `syncTick` property controls incremental synchronization.
 */
@decorators.node<SysSynchronizationState>({
    storage: 'sql',
    isPublished: true,
    canRead: true,
    canDeleteMany: true,
    isPlatformNode: true,
    serviceOptions: () => [xtremInterop.serviceOptions.synchronizationServiceOption],
    indexes: [{ orderBy: { remoteApp: 1, node: 1 }, isUnique: true, isNaturalKey: true }],
})
export class SysSynchronizationState extends Node {
    /** The remote app */
    @decorators.referenceProperty<SysSynchronizationState, 'remoteApp'>({
        isStored: true,
        isPublished: true,
        node: () => xtremInterop.nodes.SysApp,
    })
    readonly remoteApp: Reference<xtremInterop.nodes.SysApp>;

    /** The node factory which is synchronized */
    @decorators.referenceProperty<SysSynchronizationState, 'node'>({
        isStored: true,
        isPublished: true,
        node: () => xtremMetadata.nodes.MetaNodeFactory,
    })
    readonly node: Reference<xtremMetadata.nodes.MetaNodeFactory>;

    /**
     * The synchronization tick that controls incremental synchronization.
     * It is returned by the last synchronization pass and
     * it will be passed as `startTick` in the next synchronization pass.
     */
    @decorators.decimalProperty<SysSynchronizationState, 'syncTick'>({
        isStored: true,
        isPublished: true,
        dataType: () => _syncTickDataType,
    })
    readonly syncTick: Promise<decimal>;

    /** The timestamp of the last synchronization that completed successfully */
    @decorators.datetimeProperty<SysSynchronizationState, 'succcessStamp'>({
        isStored: true,
        isPublished: true,
        // null iff all the sync passes have failed so far
        isNullable: true,
    })
    readonly succcessStamp: Promise<datetime>;

    /** Reference to the notification state of the last synchronization attempt on this node */
    @decorators.referenceProperty<SysSynchronizationState, 'lastNotificationState'>({
        isStored: true,
        isPublished: true,
        // TODO: nullable is for unit tests - see if we can remove it later
        isNullable: true,
        node: () => xtremCommunication.nodes.SysNotificationState,
    })
    readonly lastNotificationState: Reference<xtremCommunication.nodes.SysNotificationState | null>;

    /** Collection of the non-purged notification states for synchronization attempts on this node  */
    @decorators.collectionProperty<SysSynchronizationState, 'notificationStates'>({
        isPublished: true,
        node: () => xtremCommunication.nodes.SysNotificationState,
        getFilter() {
            // TODO: filter on the node
            return { topic: 'SysSynchronizationTarget/synchronize/start' };
        },
    })
    readonly notificationStates: Collection<xtremCommunication.nodes.SysNotificationState>;
}
