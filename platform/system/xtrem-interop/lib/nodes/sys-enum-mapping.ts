import { Node, Reference, decorators } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremInterop from '../index';

/**
 * A transformation between a remote node and a local node.
 */
@decorators.node<SysEnumMapping>({
    storage: 'sql',
    isPublished: true,
    canRead: true,
    canCreate: false,
    canUpdate: false,
    canDelete: true,
    canDeleteMany: true,
    isSetupNode: true,
    isCached: true,
    isVitalCollectionChild: true,
    serviceOptions: () => [xtremInterop.serviceOptions.synchronizationServiceOption],
    indexes: [
        { orderBy: { transform: 1, localEnumValue: 1, remoteEnumValue: 1 }, isUnique: true, isNaturalKey: true },
        { orderBy: { transform: 1 } },
    ],
})
export class SysEnumMapping extends Node {
    @decorators.referenceProperty<SysEnumMapping, 'transform'>({
        isStored: true,
        isPublished: true,
        isVitalParent: true,
        isRequired: true,
        node: () => xtremInterop.nodes.SysEnumTransformation,
    })
    readonly transform: Reference<xtremInterop.nodes.SysEnumTransformation>;

    @decorators.stringProperty<SysEnumMapping, 'localEnumValue'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        dataType: () => xtremSystem.dataTypes.name,
    })
    readonly localEnumValue: Promise<string>;

    @decorators.stringProperty<SysEnumMapping, 'remoteEnumValue'>({
        isStored: true,
        isPublished: true,

        dataType: () => xtremSystem.dataTypes.name,
    })
    readonly remoteEnumValue: Promise<string>;
}
