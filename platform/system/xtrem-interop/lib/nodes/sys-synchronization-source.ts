import { Context, Node, _syncTickDataType, decimal, decorators, friendlyJsonParse } from '@sage/xtrem-core';
import { Decimal } from '@sage/xtrem-decimal';
import * as xtremMetadata from '@sage/xtrem-metadata';
import * as xtremInterop from '../index';

/**
 * Synchronization feed returned by the SysSynchronizationSource.getSynchronizationFeed method
 */
export interface SynchronizationFeed {
    /** Tick for the next synchronization pass */
    nextTick: decimal;
    /** The returned feed. Each entry is a JSON string containing the record payload */
    feed: AsyncGenerator<string>;
}

@decorators.node<SysSynchronizationSource>({
    isPublished: true,
    serviceOptions: () => [xtremInterop.serviceOptions.synchronizationServiceOption],
})
export class SysSynchronizationSource extends Node {
    /**
     *  Returns a synchronization feed for a synchronization remote node.
     *
     * @param context the context
     * @param node the synchronization remote node
     * @param startTick the start tick for incremental synchronization
     * @param selector the selector that specifies the properties included in the response
     * @returns a synchronization feed
     */
    @decorators.query<typeof SysSynchronizationSource, 'getSynchronizationFeed'>({
        isPublished: true,
        parameters: [
            {
                name: 'node',
                type: 'reference',
                node: () => xtremMetadata.nodes.MetaNodeFactory,
                isMandatory: true,
            },
            { name: 'startTick', type: 'decimal', dataType: () => _syncTickDataType },
            { name: 'selector', type: 'string' },
            { name: 'filter', type: 'string' },
        ],
        return: {
            type: 'object',
            properties: {
                nextTick: { type: 'decimal', dataType: () => _syncTickDataType },
                feed: {
                    type: 'array',
                    item: 'string',
                },
            },
        },
    })
    static async getSynchronizationFeed(
        context: Context,
        node: xtremMetadata.nodes.MetaNodeFactory,
        startTick: Decimal,
        selector: string,
        filter: string,
    ): Promise<SynchronizationFeed> {
        const factory = context.application.getFactoryByName(await node.name);
        if (!factory.isSynchronizable)
            throw factory.logicError('cannot get synchronization feed: factory is not a synchronization remote');
        const nextTickResult = await context.executeSql<{ pg_snapshot_xmin: number }[]>(
            `SELECT pg_snapshot_xmin(pg_current_snapshot())`,
            [],
        );
        const nextTick = nextTickResult[0]?.pg_snapshot_xmin;
        if (!nextTick) throw factory.systemError('failed to get nextTick');

        const selectorObject = friendlyJsonParse(selector);
        const syncTickFilter = { _syncTick: { _gte: startTick } };
        const filterObject = filter ? friendlyJsonParse(filter) : undefined;
        const selectFilter =
            filterObject && Object.keys(filterObject).length > 0
                ? { _and: [syncTickFilter, filterObject] }
                : syncTickFilter;

        const reader = await context.getSelectReader(factory.nodeConstructor, selectorObject, {
            filter: selectFilter,
            returnReferencesAsNaturalKey: true,
        });
        const feed = reader.map(item => JSON.stringify(item)).toAsyncGenerator();

        // Test code to generate a large feed and test with several chunks
        // let data = await reader.map(item => JSON.stringify(item)).readAll();
        // for (let i = 0; i < 8; i += 1) {
        //     data = [...data, ...data];
        // }
        // const feed = new AsyncArrayReader(() => data).toAsyncGenerator();

        return { nextTick, feed };
    }
}
