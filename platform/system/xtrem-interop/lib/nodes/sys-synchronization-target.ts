import { Context, Node, asyncArray, decorators, friendlyJsonParse } from '@sage/xtrem-core';
import * as xtremMetadata from '@sage/xtrem-metadata';
import { SynchronizationProcess, SysSyncCounters } from '../classes/synchronization-process';
import * as xtremInterop from '../index';

/**
 * This node provides the `synchronize` async mutation.
 */
@decorators.node<SysSynchronizationTarget>({
    isPublished: true,
    serviceOptions: () => [xtremInterop.serviceOptions.synchronizationServiceOption],
})
export class SysSynchronizationTarget extends Node {
    /**
     * Synchronizes the remote app with the local nodes.
     * If an empty array is passed as localNodeNames, all the synchronizable local nodes will be synchronized.
     * Dependencies are synchronized by default. You can override this behavior by setting skipDependencies to true.
     *
     * @param context - The context object.
     * @param remoteApp - The remote app to synchronize.
     * @param localNodeNames - The local nodes to synchronize with. Defaults to an empty array.
     * @param skipDependencies - Indicates whether to skip synchronizing dependencies. Defaults to false.
     * @param forceSyncState Indicates whether to force synchronization state update. We use this to ignore errors in the synchronization process.
     * @returns A promise that resolves to an array of SysSyncCounters.
     */
    @decorators.asyncMutation<typeof SysSynchronizationTarget, 'synchronize'>({
        isPublished: true,
        isSchedulable: true,
        parameters: [
            { name: 'remoteApp', type: 'reference', node: () => xtremInterop.nodes.SysApp },
            {
                name: 'localNodeNames',
                type: 'array',
                item: {
                    type: 'reference',
                    node: () => xtremMetadata.nodes.MetaNodeFactory,
                },
            },
            { name: 'skipDependencies', type: 'boolean' },
            { name: 'forceSyncState', type: 'boolean' },
            { name: 'fullSync', type: 'boolean' },
            { name: 'filtersByNode', type: 'string' },
        ],
        return: {
            type: 'array',
            item: {
                type: 'object',
                properties: {
                    localNodeName: 'string',
                    created: 'integer',
                    updated: 'integer',
                    skipped: 'integer',
                },
            },
        },
        startsReadOnly: true,
    })
    static async synchronize(
        context: Context,
        remoteApp: xtremInterop.nodes.SysApp,
        localNodeNames: xtremMetadata.nodes.MetaNodeFactory[] = [],
        skipDependencies = false,
        forceSyncState = false,
        fullSync = false,
        filtersByNode = '{}',
    ): Promise<SysSyncCounters[]> {
        const remoteAppName = await remoteApp.name;
        const remoteInteropPackageName = await remoteApp.interopPackage;
        const remoteIsConnector = await remoteApp.isConnector;
        const localNodeNamesParameter = await asyncArray(localNodeNames)
            .map(localNode => localNode.name)
            .toArray();

        return SynchronizationProcess.run(context, {
            remoteAppName,
            remoteInteropPackageName,
            remoteIsConnector,
            localNodeNames: localNodeNamesParameter,
            skipDependencies,
            forceSyncState,
            fullSync,
            filtersByNode: friendlyJsonParse(filtersByNode),
        });
    }
}
