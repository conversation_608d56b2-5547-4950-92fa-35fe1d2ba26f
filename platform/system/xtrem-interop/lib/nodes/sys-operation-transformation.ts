import { Node, Reference, decorators } from '@sage/xtrem-core';
import * as xtremMetadata from '@sage/xtrem-metadata';
import * as xtremSystem from '@sage/xtrem-system';
import { OperationMappings } from '../classes/interop-operation-transformation';
import * as xtremInterop from '../index';

/**
 * A transformation between a remote graphql query or mutation and a local one.
 */
@decorators.node<SysOperationTransformation>({
    storage: 'sql',
    isPublished: true,
    canRead: true,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canDeleteMany: true,
    isSetupNode: true,
    serviceOptions: () => [xtremInterop.serviceOptions.synchronizationServiceOption],
    indexes: [
        { orderBy: { id: 1 }, isUnique: true, isNaturalKey: true },
        { orderBy: { localNode: 1, remoteApp: 1, remoteAppVersion: 1 } },
    ],
})
export class SysOperationTransformation extends Node {
    /** The id of the transform */
    @decorators.stringProperty<SysOperationTransformation, 'id'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.id,
    })
    readonly id: Promise<string>;

    /** Is the transform active? */
    @decorators.booleanProperty<SysOperationTransformation, 'isActive'>({
        isStored: true,
        isPublished: true,
        defaultValue: true,
        provides: ['isActive'],
        isOwnedByCustomer: true,
    })
    readonly isActive: Promise<boolean>;

    /** The remote app */
    @decorators.referenceProperty<SysOperationTransformation, 'remoteApp'>({
        isStored: true,
        isPublished: true,
        node: () => xtremInterop.nodes.SysApp,
    })
    readonly remoteApp: Reference<xtremInterop.nodes.SysApp>;

    /** The version of the remote app */
    @decorators.stringProperty<SysOperationTransformation, 'remoteAppVersion'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.version,
    })
    readonly remoteAppVersion: Promise<string>;

    /** The name of the remote node */
    @decorators.stringProperty<SysOperationTransformation, 'remotePackageName'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.name,
    })
    readonly remotePackageName: Promise<string>;

    /** The name of the remote node */
    @decorators.stringProperty<SysOperationTransformation, 'remoteNodeName'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.name,
    })
    readonly remoteNodeName: Promise<string>;

    /** The name of the remote node */
    @decorators.stringProperty<SysOperationTransformation, 'remoteOperationName'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.name,
    })
    readonly remoteOperationName: Promise<string>;

    /** The node factory which is synchronized */
    @decorators.referenceProperty<SysOperationTransformation, 'localNode'>({
        isStored: true,
        isPublished: true,
        node: () => xtremMetadata.nodes.MetaNodeFactory,
        filters: {
            lookup: {
                isSynchronized: true,
            },
        },
    })
    readonly localNode: Reference<xtremMetadata.nodes.MetaNodeFactory>;

    /** The name of the source node */
    @decorators.stringProperty<SysOperationTransformation, 'localOperationName'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.name,
    })
    readonly localOperationName: Promise<string>;

    /** The transform mappings */
    @decorators.jsonProperty<SysOperationTransformation, 'mappings'>({
        isStored: true,
        isPublished: true,
        control() {
            // TODO
        },
    })
    readonly mappings: Promise<OperationMappings>;
}
