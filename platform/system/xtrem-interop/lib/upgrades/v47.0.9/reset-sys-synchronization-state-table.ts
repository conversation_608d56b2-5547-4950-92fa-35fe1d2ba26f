import { CustomSqlAction } from '@sage/xtrem-system';

export const resetSysSynchronizationStateTable = new CustomSqlAction({
    description: 'Resets the sys_synchronization_state table',
    fixes: {
        tables: ['sys_synchronization_state'],
        notNullableColumns: [{ table: 'sys_synchronization_state', column: 'remote_app' }],
    },
    body: async helper => {
        await helper.executeSql(`DELETE FROM ${helper.schemaName}.sys_synchronization_state;`);
    },
});
