{"@sage/xtrem-interop/activity__sys_app__name": "", "@sage/xtrem-interop/activity__sys_enum_synchronization__name": "", "@sage/xtrem-interop/activity__sys_synchronization__name": "", "@sage/xtrem-interop/data_types__id__name": "", "@sage/xtrem-interop/data_types__mapping_value__name": "", "@sage/xtrem-interop/data_types__node_mapping_kind_enum__name": "", "@sage/xtrem-interop/data_types__price__name": "", "@sage/xtrem-interop/data_types__quantity__name": "", "@sage/xtrem-interop/data_types__test_local_status_enum__name": "", "@sage/xtrem-interop/data_types__test_remote_status_enum__name": "", "@sage/xtrem-interop/enums__node_mapping_kind__constant_to_path": "", "@sage/xtrem-interop/enums__node_mapping_kind__function_to_path": "", "@sage/xtrem-interop/enums__node_mapping_kind__path_to_constant": "", "@sage/xtrem-interop/enums__node_mapping_kind__path_to_function": "", "@sage/xtrem-interop/enums__node_mapping_kind__path_to_path": "", "@sage/xtrem-interop/enums__test_local_status__inProgress": "", "@sage/xtrem-interop/enums__test_local_status__start": "", "@sage/xtrem-interop/enums__test_local_status__stop": "", "@sage/xtrem-interop/enums__test_local_status__track": "", "@sage/xtrem-interop/enums__test_remote_status__begin": "", "@sage/xtrem-interop/enums__test_remote_status__end": "", "@sage/xtrem-interop/enums__test_remote_status__inProgress": "", "@sage/xtrem-interop/menu_item__interop": "", "@sage/xtrem-interop/nodes__sys_app__mutation__refreshState": "", "@sage/xtrem-interop/nodes__sys_app__mutation__refreshState__failed": "", "@sage/xtrem-interop/nodes__sys_app__node_name": "", "@sage/xtrem-interop/nodes__sys_app__property__interopPackage": "", "@sage/xtrem-interop/nodes__sys_app__property__isActive": "", "@sage/xtrem-interop/nodes__sys_app__property__isAlive": "", "@sage/xtrem-interop/nodes__sys_app__property__isConnector": "", "@sage/xtrem-interop/nodes__sys_app__property__name": "", "@sage/xtrem-interop/nodes__sys_app__property__title": "", "@sage/xtrem-interop/nodes__sys_app__property__version": "", "@sage/xtrem-interop/nodes__sys_app__query__getAppInfo": "", "@sage/xtrem-interop/nodes__sys_app__query__getAppInfo__failed": "", "@sage/xtrem-interop/nodes__sys_enum_mapping__asyncMutation__asyncExport": "", "@sage/xtrem-interop/nodes__sys_enum_mapping__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-interop/nodes__sys_enum_mapping__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-interop/nodes__sys_enum_mapping__node_name": "", "@sage/xtrem-interop/nodes__sys_enum_mapping__property__localEnumValue": "", "@sage/xtrem-interop/nodes__sys_enum_mapping__property__remoteEnumValue": "", "@sage/xtrem-interop/nodes__sys_enum_mapping__property__transform": "", "@sage/xtrem-interop/nodes__sys_enum_transformation__asyncMutation__asyncExport": "", "@sage/xtrem-interop/nodes__sys_enum_transformation__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-interop/nodes__sys_enum_transformation__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-interop/nodes__sys_enum_transformation__mutation__updateActiveMappings": "", "@sage/xtrem-interop/nodes__sys_enum_transformation__mutation__updateActiveMappings__failed": "", "@sage/xtrem-interop/nodes__sys_enum_transformation__mutation__updateActiveMappings__parameter__parameters": "", "@sage/xtrem-interop/nodes__sys_enum_transformation__node_name": "", "@sage/xtrem-interop/nodes__sys_enum_transformation__property__id": "", "@sage/xtrem-interop/nodes__sys_enum_transformation__property__isActive": "", "@sage/xtrem-interop/nodes__sys_enum_transformation__property__localEnum": "", "@sage/xtrem-interop/nodes__sys_enum_transformation__property__map": "", "@sage/xtrem-interop/nodes__sys_enum_transformation__property__remoteApp": "", "@sage/xtrem-interop/nodes__sys_enum_transformation__property__remoteAppVersion": "", "@sage/xtrem-interop/nodes__sys_enum_transformation__property__remoteEnum": "", "@sage/xtrem-interop/nodes__sys_node_mapping__asyncMutation__asyncExport": "", "@sage/xtrem-interop/nodes__sys_node_mapping__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-interop/nodes__sys_node_mapping__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-interop/nodes__sys_node_mapping__node_name": "", "@sage/xtrem-interop/nodes__sys_node_mapping__property__kind": "", "@sage/xtrem-interop/nodes__sys_node_mapping__property__localProperty": "", "@sage/xtrem-interop/nodes__sys_node_mapping__property__remoteProperty": "", "@sage/xtrem-interop/nodes__sys_node_mapping__property__transform": "", "@sage/xtrem-interop/nodes__sys_node_transformation__asyncMutation__asyncExport": "", "@sage/xtrem-interop/nodes__sys_node_transformation__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-interop/nodes__sys_node_transformation__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-interop/nodes__sys_node_transformation__mutation__updateActiveMappings": "", "@sage/xtrem-interop/nodes__sys_node_transformation__mutation__updateActiveMappings__failed": "", "@sage/xtrem-interop/nodes__sys_node_transformation__mutation__updateActiveMappings__parameter__parameters": "", "@sage/xtrem-interop/nodes__sys_node_transformation__node_name": "", "@sage/xtrem-interop/nodes__sys_node_transformation__property__filter": "", "@sage/xtrem-interop/nodes__sys_node_transformation__property__id": "", "@sage/xtrem-interop/nodes__sys_node_transformation__property__isActive": "", "@sage/xtrem-interop/nodes__sys_node_transformation__property__lastError": "", "@sage/xtrem-interop/nodes__sys_node_transformation__property__lastSync": "", "@sage/xtrem-interop/nodes__sys_node_transformation__property__localNode": "", "@sage/xtrem-interop/nodes__sys_node_transformation__property__map": "", "@sage/xtrem-interop/nodes__sys_node_transformation__property__mappings": "", "@sage/xtrem-interop/nodes__sys_node_transformation__property__remoteApp": "", "@sage/xtrem-interop/nodes__sys_node_transformation__property__remoteAppVersion": "", "@sage/xtrem-interop/nodes__sys_node_transformation__property__remoteMappings": "", "@sage/xtrem-interop/nodes__sys_node_transformation__property__remoteNodeName": "", "@sage/xtrem-interop/nodes__sys_node_transformation__query__hasActiveMapping": "", "@sage/xtrem-interop/nodes__sys_node_transformation__query__hasActiveMapping__failed": "", "@sage/xtrem-interop/nodes__sys_node_transformation__query__hasActiveMapping__parameter__localNode": "", "@sage/xtrem-interop/nodes__sys_node_transformation__query__hasActiveMapping__parameter__propertyId": "", "@sage/xtrem-interop/nodes__sys_operation_transformation__asyncMutation__asyncExport": "", "@sage/xtrem-interop/nodes__sys_operation_transformation__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-interop/nodes__sys_operation_transformation__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-interop/nodes__sys_operation_transformation__node_name": "", "@sage/xtrem-interop/nodes__sys_operation_transformation__property__id": "", "@sage/xtrem-interop/nodes__sys_operation_transformation__property__isActive": "", "@sage/xtrem-interop/nodes__sys_operation_transformation__property__localNode": "", "@sage/xtrem-interop/nodes__sys_operation_transformation__property__localOperationName": "", "@sage/xtrem-interop/nodes__sys_operation_transformation__property__mappings": "", "@sage/xtrem-interop/nodes__sys_operation_transformation__property__remoteApp": "", "@sage/xtrem-interop/nodes__sys_operation_transformation__property__remoteAppVersion": "", "@sage/xtrem-interop/nodes__sys_operation_transformation__property__remoteNodeName": "", "@sage/xtrem-interop/nodes__sys_operation_transformation__property__remoteOperationName": "", "@sage/xtrem-interop/nodes__sys_operation_transformation__property__remotePackageName": "", "@sage/xtrem-interop/nodes__sys_remote_metadata__node_name": "", "@sage/xtrem-interop/nodes__sys_remote_metadata__query__getDataTypeInfo": "", "@sage/xtrem-interop/nodes__sys_remote_metadata__query__getDataTypeInfo__failed": "", "@sage/xtrem-interop/nodes__sys_remote_metadata__query__getDataTypeInfo__parameter__app": "", "@sage/xtrem-interop/nodes__sys_remote_metadata__query__getDataTypeInfo__parameter__name": "", "@sage/xtrem-interop/nodes__sys_remote_metadata__query__getDataTypeInfo__parameter__type": "", "@sage/xtrem-interop/nodes__sys_remote_metadata__query__getNodeInfo": "", "@sage/xtrem-interop/nodes__sys_remote_metadata__query__getNodeInfo__failed": "", "@sage/xtrem-interop/nodes__sys_remote_metadata__query__getNodeInfo__parameter__app": "", "@sage/xtrem-interop/nodes__sys_remote_metadata__query__getNodeInfo__parameter__filter": "", "@sage/xtrem-interop/nodes__sys_remote_metadata__query__getNodeInfo__parameter__name": "", "@sage/xtrem-interop/nodes__sys_synchronization_source__node_name": "", "@sage/xtrem-interop/nodes__sys_synchronization_source__query__getSynchronizationFeed": "", "@sage/xtrem-interop/nodes__sys_synchronization_source__query__getSynchronizationFeed__failed": "", "@sage/xtrem-interop/nodes__sys_synchronization_source__query__getSynchronizationFeed__parameter__filter": "", "@sage/xtrem-interop/nodes__sys_synchronization_source__query__getSynchronizationFeed__parameter__node": "", "@sage/xtrem-interop/nodes__sys_synchronization_source__query__getSynchronizationFeed__parameter__selector": "", "@sage/xtrem-interop/nodes__sys_synchronization_source__query__getSynchronizationFeed__parameter__startTick": "", "@sage/xtrem-interop/nodes__sys_synchronization_state__asyncMutation__asyncExport": "", "@sage/xtrem-interop/nodes__sys_synchronization_state__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-interop/nodes__sys_synchronization_state__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-interop/nodes__sys_synchronization_state__node_name": "", "@sage/xtrem-interop/nodes__sys_synchronization_state__property__lastNotificationState": "", "@sage/xtrem-interop/nodes__sys_synchronization_state__property__node": "", "@sage/xtrem-interop/nodes__sys_synchronization_state__property__notificationStates": "", "@sage/xtrem-interop/nodes__sys_synchronization_state__property__remoteApp": "", "@sage/xtrem-interop/nodes__sys_synchronization_state__property__succcessStamp": "", "@sage/xtrem-interop/nodes__sys_synchronization_state__property__syncTick": "", "@sage/xtrem-interop/nodes__sys_synchronization_target__asyncMutation__synchronize": "", "@sage/xtrem-interop/nodes__sys_synchronization_target__asyncMutation__synchronize__failed": "", "@sage/xtrem-interop/nodes__sys_synchronization_target__asyncMutation__synchronize__parameter__filtersByNode": "", "@sage/xtrem-interop/nodes__sys_synchronization_target__asyncMutation__synchronize__parameter__forceSyncState": "", "@sage/xtrem-interop/nodes__sys_synchronization_target__asyncMutation__synchronize__parameter__fullSync": "", "@sage/xtrem-interop/nodes__sys_synchronization_target__asyncMutation__synchronize__parameter__localNodeNames": "", "@sage/xtrem-interop/nodes__sys_synchronization_target__asyncMutation__synchronize__parameter__remoteApp": "", "@sage/xtrem-interop/nodes__sys_synchronization_target__asyncMutation__synchronize__parameter__skipDependencies": "", "@sage/xtrem-interop/nodes__sys_synchronization_target__node_name": "", "@sage/xtrem-interop/package__name": "", "@sage/xtrem-interop/pages__sys_app____navigationPanel__listItem__isAlive__title": "", "@sage/xtrem-interop/pages__sys_app____navigationPanel__listItem__isConnector__title": "", "@sage/xtrem-interop/pages__sys_app____navigationPanel__listItem__line2__title": "", "@sage/xtrem-interop/pages__sys_app____navigationPanel__listItem__line2Right__title": "", "@sage/xtrem-interop/pages__sys_app____navigationPanel__listItem__title__title": "", "@sage/xtrem-interop/pages__sys_app____navigationPanel__listItem__titleRight__title": "", "@sage/xtrem-interop/pages__sys_app____navigationPanel__optionsMenu__title": "", "@sage/xtrem-interop/pages__sys_app____navigationPanel__optionsMenu__title__2": "", "@sage/xtrem-interop/pages__sys_app____navigationPanel__optionsMenu__title__3": "", "@sage/xtrem-interop/pages__sys_app____objectTypePlural": "", "@sage/xtrem-interop/pages__sys_app____objectTypeSingular": "", "@sage/xtrem-interop/pages__sys_app____title": "", "@sage/xtrem-interop/pages__sys_app__interopPackage____title": "", "@sage/xtrem-interop/pages__sys_app__isActive____title": "", "@sage/xtrem-interop/pages__sys_app__isAlive____title": "", "@sage/xtrem-interop/pages__sys_app__isConnector____title": "", "@sage/xtrem-interop/pages__sys_app__name____title": "", "@sage/xtrem-interop/pages__sys_app__refreshActiveState____title": "", "@sage/xtrem-interop/pages__sys_app__sync_all_message": "", "@sage/xtrem-interop/pages__sys_app__syncAllNodes____title": "", "@sage/xtrem-interop/pages__sys_app__technicalBlock____title": "", "@sage/xtrem-interop/pages__sys_app__title____title": "", "@sage/xtrem-interop/pages__sys_app__version____title": "", "@sage/xtrem-interop/pages__sys_enum_transformation____navigationPanel__inlineActions__title": "", "@sage/xtrem-interop/pages__sys_enum_transformation____navigationPanel__listItem__line2__title": "", "@sage/xtrem-interop/pages__sys_enum_transformation____navigationPanel__listItem__remoteApp__title": "", "@sage/xtrem-interop/pages__sys_enum_transformation____navigationPanel__listItem__remoteAppVersion__title": "", "@sage/xtrem-interop/pages__sys_enum_transformation____navigationPanel__listItem__remoteEnum__title": "", "@sage/xtrem-interop/pages__sys_enum_transformation____navigationPanel__listItem__title__title": "", "@sage/xtrem-interop/pages__sys_enum_transformation____navigationPanel__listItem__titleRight__title": "", "@sage/xtrem-interop/pages__sys_enum_transformation____navigationPanel__optionsMenu__title": "", "@sage/xtrem-interop/pages__sys_enum_transformation____navigationPanel__optionsMenu__title__2": "", "@sage/xtrem-interop/pages__sys_enum_transformation____navigationPanel__optionsMenu__title__3": "", "@sage/xtrem-interop/pages__sys_enum_transformation____objectTypePlural": "", "@sage/xtrem-interop/pages__sys_enum_transformation____objectTypeSingular": "", "@sage/xtrem-interop/pages__sys_enum_transformation____title": "", "@sage/xtrem-interop/pages__sys_enum_transformation__id____title": "", "@sage/xtrem-interop/pages__sys_enum_transformation__isActive____title": "", "@sage/xtrem-interop/pages__sys_enum_transformation__localEnum____columns__title__name": "", "@sage/xtrem-interop/pages__sys_enum_transformation__localEnum____columns__title__title": "", "@sage/xtrem-interop/pages__sys_enum_transformation__localEnum____title": "", "@sage/xtrem-interop/pages__sys_enum_transformation__mappingBlock____title": "", "@sage/xtrem-interop/pages__sys_enum_transformation__mappingTable____columns__title__localEnumValue": "", "@sage/xtrem-interop/pages__sys_enum_transformation__mappingTable____columns__title__remoteEnumValue": "", "@sage/xtrem-interop/pages__sys_enum_transformation__mappingTable____dropdownActions__title": "", "@sage/xtrem-interop/pages__sys_enum_transformation__mappingTable____dropdownActions__title__2": "", "@sage/xtrem-interop/pages__sys_enum_transformation__remote_data_type_not_found": "", "@sage/xtrem-interop/pages__sys_enum_transformation__remoteApp____title": "", "@sage/xtrem-interop/pages__sys_enum_transformation__remoteAppVersion____title": "", "@sage/xtrem-interop/pages__sys_enum_transformation__remoteEnum____title": "", "@sage/xtrem-interop/pages__sys_enum_transformation__remoteEnumDialogSection____title": "", "@sage/xtrem-interop/pages__sys_enum_transformation__remoteEnumTable____columns__title__name": "", "@sage/xtrem-interop/pages__sys_enum_transformation__remoteEnumTable____columns__title__title": "", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__dropdownActions__title": "", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__dropdownActions__title__2": "", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__dropdownActions__title__3": "", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__dropdownActions__title__4": "", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__inlineActions__title": "", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__listItem__lastError__title": "", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__listItem__lastSync__title": "", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__listItem__localNode__title": "", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__listItem__name__title": "", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__listItem__remoteAppVersion__title": "", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__listItem__remoteNodeName__title": "", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__listItem__title__title": "", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__listItem__titleRight__title": "", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__optionsMenu__title": "", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__optionsMenu__title__2": "", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__optionsMenu__title__3": "", "@sage/xtrem-interop/pages__sys_node_transformation____objectTypePlural": "", "@sage/xtrem-interop/pages__sys_node_transformation____objectTypeSingular": "", "@sage/xtrem-interop/pages__sys_node_transformation____title": "", "@sage/xtrem-interop/pages__sys_node_transformation__dialogBlock____title": "", "@sage/xtrem-interop/pages__sys_node_transformation__dialogSection____title": "", "@sage/xtrem-interop/pages__sys_node_transformation__id____title": "", "@sage/xtrem-interop/pages__sys_node_transformation__isActive____title": "", "@sage/xtrem-interop/pages__sys_node_transformation__lastErrorDate____title": "", "@sage/xtrem-interop/pages__sys_node_transformation__lastSyncDate____title": "", "@sage/xtrem-interop/pages__sys_node_transformation__listOfRemoteNodes____columns__title__name": "", "@sage/xtrem-interop/pages__sys_node_transformation__listOfRemoteNodes____columns__title__title": "", "@sage/xtrem-interop/pages__sys_node_transformation__localNode____columns__title__name": "", "@sage/xtrem-interop/pages__sys_node_transformation__localNode____columns__title__package__name": "", "@sage/xtrem-interop/pages__sys_node_transformation__localNode____columns__title__storage": "", "@sage/xtrem-interop/pages__sys_node_transformation__localNode____columns__title__title": "", "@sage/xtrem-interop/pages__sys_node_transformation__localNode____title": "", "@sage/xtrem-interop/pages__sys_node_transformation__localPropertiesBlock____title": "", "@sage/xtrem-interop/pages__sys_node_transformation__localPropertiesSection____title": "", "@sage/xtrem-interop/pages__sys_node_transformation__mappingBlock____title": "", "@sage/xtrem-interop/pages__sys_node_transformation__mappingSection____title": "", "@sage/xtrem-interop/pages__sys_node_transformation__mappingTable____columns__title__kind": "", "@sage/xtrem-interop/pages__sys_node_transformation__mappingTable____columns__title__localProperty": "", "@sage/xtrem-interop/pages__sys_node_transformation__mappingTable____columns__title__remoteProperty": "", "@sage/xtrem-interop/pages__sys_node_transformation__mappingTable____dropdownActions__title": "", "@sage/xtrem-interop/pages__sys_node_transformation__mappingTable____dropdownActions__title__2": "", "@sage/xtrem-interop/pages__sys_node_transformation__mappingTable____dropdownActions__title__3": "", "@sage/xtrem-interop/pages__sys_node_transformation__remote_not_available": "", "@sage/xtrem-interop/pages__sys_node_transformation__remoteApp____title": "", "@sage/xtrem-interop/pages__sys_node_transformation__remoteNodeName____title": "", "@sage/xtrem-interop/pages__sys_node_transformation__remotePropertiesBlock____title": "", "@sage/xtrem-interop/pages__sys_node_transformation__remotePropertiesSection____title": "", "@sage/xtrem-interop/pages__sys_node_transformation__remoteVersion____title": "", "@sage/xtrem-interop/pages__sys_node_transformation__scheduleSync____title": "", "@sage/xtrem-interop/pages__sys_node_transformation__selectFromLocalProperties____title": "", "@sage/xtrem-interop/pages__sys_node_transformation__synchronizeResult____title": "", "@sage/xtrem-interop/pages__sys_node_transformation__syncNow____title": "", "@sage/xtrem-interop/pages__sys_node_transformation__syncSection____title": "", "@sage/xtrem-interop/pages__sys_node_transformation__too_many_remote_properties": "", "@sage/xtrem-interop/permission__create__name": "", "@sage/xtrem-interop/permission__default_instance__name": "", "@sage/xtrem-interop/permission__delete__name": "", "@sage/xtrem-interop/permission__manage__name": "", "@sage/xtrem-interop/permission__read__name": "", "@sage/xtrem-interop/permission__update__name": "", "@sage/xtrem-interop/service_options__synchronization_service_option__name": ""}