{"@sage/xtrem-interop/activity__sys_app__name": "System-Anwendung", "@sage/xtrem-interop/activity__sys_enum_synchronization__name": "Enum Systemsynchronisation", "@sage/xtrem-interop/activity__sys_synchronization__name": "Systemsynchronisation", "@sage/xtrem-interop/data_types__id__name": "ID", "@sage/xtrem-interop/data_types__mapping_value__name": "Zuordnungswert", "@sage/xtrem-interop/data_types__node_mapping_kind_enum__name": "Enum <PERSON> Node-Mapping", "@sage/xtrem-interop/data_types__price__name": "Pre<PERSON>", "@sage/xtrem-interop/data_types__quantity__name": "<PERSON><PERSON>", "@sage/xtrem-interop/data_types__test_local_status_enum__name": "Enum Test lokaler Status", "@sage/xtrem-interop/data_types__test_remote_status_enum__name": "Enum Remote-Status Test", "@sage/xtrem-interop/enums__node_mapping_kind__constant_to_path": "Konst<PERSON> zu Pfad", "@sage/xtrem-interop/enums__node_mapping_kind__function_to_path": "Funk<PERSON> zu Pfad", "@sage/xtrem-interop/enums__node_mapping_kind__path_to_constant": "Pfad zu Konstante", "@sage/xtrem-interop/enums__node_mapping_kind__path_to_function": "Pfad zu Funktion", "@sage/xtrem-interop/enums__node_mapping_kind__path_to_path": "<PERSON><PERSON><PERSON> zu Pfad", "@sage/xtrem-interop/enums__test_local_status__inProgress": "In Bearbeitung", "@sage/xtrem-interop/enums__test_local_status__start": "Starten", "@sage/xtrem-interop/enums__test_local_status__stop": "<PERSON><PERSON><PERSON>", "@sage/xtrem-interop/enums__test_local_status__track": "Verfolgen", "@sage/xtrem-interop/enums__test_remote_status__begin": "<PERSON><PERSON><PERSON>", "@sage/xtrem-interop/enums__test_remote_status__end": "<PERSON>den", "@sage/xtrem-interop/enums__test_remote_status__inProgress": "In Bearbeitung", "@sage/xtrem-interop/menu_item__interop": "Interop", "@sage/xtrem-interop/nodes__synchronization_state__property__secondaryLink": "Sekundärer Link", "@sage/xtrem-interop/nodes__synchronization_state__property__secondaryText": "Sekundärer Text", "@sage/xtrem-interop/nodes__sys_app__asyncMutation__asyncExport": "Export", "@sage/xtrem-interop/nodes__sys_app__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-interop/nodes__sys_app__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-interop/nodes__sys_app__mutation__refreshState": "Status aktualisieren", "@sage/xtrem-interop/nodes__sys_app__mutation__refreshState__failed": "Status aktualisieren fehlgeschlagen.", "@sage/xtrem-interop/nodes__sys_app__node_name": "System-Anwendung", "@sage/xtrem-interop/nodes__sys_app__property__interopPackage": "Interop-Paket", "@sage/xtrem-interop/nodes__sys_app__property__isActive": "Ist aktiv", "@sage/xtrem-interop/nodes__sys_app__property__isAlive": "Ist verbunden", "@sage/xtrem-interop/nodes__sys_app__property__isConnector": "Ist Connector", "@sage/xtrem-interop/nodes__sys_app__property__name": "Name", "@sage/xtrem-interop/nodes__sys_app__property__title": "Titel", "@sage/xtrem-interop/nodes__sys_app__property__version": "Version", "@sage/xtrem-interop/nodes__sys_app__query__getAppInfo": "Andwendungsinformationen abrufen", "@sage/xtrem-interop/nodes__sys_app__query__getAppInfo__failed": "Anwendungsinformationen abrufen fehlgeschlagen.", "@sage/xtrem-interop/nodes__sys_enum_mapping__asyncMutation__asyncExport": "Export", "@sage/xtrem-interop/nodes__sys_enum_mapping__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-interop/nodes__sys_enum_mapping__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-interop/nodes__sys_enum_mapping__node_name": "Zuordnung System-Enum", "@sage/xtrem-interop/nodes__sys_enum_mapping__property__localEnumValue": "Wert lokales <PERSON>um", "@sage/xtrem-interop/nodes__sys_enum_mapping__property__remoteEnumValue": "Wert Remote-Enum", "@sage/xtrem-interop/nodes__sys_enum_mapping__property__transform": "Transformation", "@sage/xtrem-interop/nodes__sys_enum_transformation__asyncMutation__asyncExport": "Export", "@sage/xtrem-interop/nodes__sys_enum_transformation__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-interop/nodes__sys_enum_transformation__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-interop/nodes__sys_enum_transformation__mutation__updateActiveMappings": "Aktive Zuordnungen aktualisieren", "@sage/xtrem-interop/nodes__sys_enum_transformation__mutation__updateActiveMappings__failed": "Aktive Zuordnungen aktualisieren fehlgeschlagen.", "@sage/xtrem-interop/nodes__sys_enum_transformation__mutation__updateActiveMappings__parameter__parameters": "Parameter", "@sage/xtrem-interop/nodes__sys_enum_transformation__node_name": "Transformation System-Enum", "@sage/xtrem-interop/nodes__sys_enum_transformation__property__id": "ID", "@sage/xtrem-interop/nodes__sys_enum_transformation__property__isActive": "Ist aktiv", "@sage/xtrem-interop/nodes__sys_enum_transformation__property__localEnum": "Enum lokal", "@sage/xtrem-interop/nodes__sys_enum_transformation__property__map": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-interop/nodes__sys_enum_transformation__property__remoteApp": "Remote-Anwendung", "@sage/xtrem-interop/nodes__sys_enum_transformation__property__remoteAppVersion": "Version Remote-Anwendung", "@sage/xtrem-interop/nodes__sys_enum_transformation__property__remoteEnum": "Enum remote", "@sage/xtrem-interop/nodes__sys_node_mapping__asyncMutation__asyncExport": "Export", "@sage/xtrem-interop/nodes__sys_node_mapping__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-interop/nodes__sys_node_mapping__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-interop/nodes__sys_node_mapping__node_name": "Zuordnung System-Node", "@sage/xtrem-interop/nodes__sys_node_mapping__property__kind": "<PERSON><PERSON>", "@sage/xtrem-interop/nodes__sys_node_mapping__property__localProperty": "Lokale Eigenschaft", "@sage/xtrem-interop/nodes__sys_node_mapping__property__remoteProperty": "Remote-Eigenschaft", "@sage/xtrem-interop/nodes__sys_node_mapping__property__transform": "Transformation", "@sage/xtrem-interop/nodes__sys_node_transformation__asyncMutation__asyncExport": "Export", "@sage/xtrem-interop/nodes__sys_node_transformation__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-interop/nodes__sys_node_transformation__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-interop/nodes__sys_node_transformation__mutation__updateActiveMappings": "Aktive Zuordnungen aktualisieren", "@sage/xtrem-interop/nodes__sys_node_transformation__mutation__updateActiveMappings__failed": "Aktive Zuordnungen aktualisieren fehlgeschlagen.", "@sage/xtrem-interop/nodes__sys_node_transformation__mutation__updateActiveMappings__parameter__parameters": "Parameter", "@sage/xtrem-interop/nodes__sys_node_transformation__node_name": "Transformation System-Node", "@sage/xtrem-interop/nodes__sys_node_transformation__property__filter": "Filter", "@sage/xtrem-interop/nodes__sys_node_transformation__property__id": "ID", "@sage/xtrem-interop/nodes__sys_node_transformation__property__isActive": "Ist aktiv", "@sage/xtrem-interop/nodes__sys_node_transformation__property__lastError": "<PERSON><PERSON><PERSON>", "@sage/xtrem-interop/nodes__sys_node_transformation__property__lastSync": "Letzte Synchronisierung", "@sage/xtrem-interop/nodes__sys_node_transformation__property__localNode": "Lokaler Node", "@sage/xtrem-interop/nodes__sys_node_transformation__property__map": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-interop/nodes__sys_node_transformation__property__mappings": "Z<PERSON><PERSON>nungen", "@sage/xtrem-interop/nodes__sys_node_transformation__property__remoteApp": "Remote-Anwendung", "@sage/xtrem-interop/nodes__sys_node_transformation__property__remoteAppVersion": "Remote-Anwendungsversion", "@sage/xtrem-interop/nodes__sys_node_transformation__property__remoteMappings": "Remote-Zuordnungen", "@sage/xtrem-interop/nodes__sys_node_transformation__property__remoteNodeName": "Remote-Node-Name", "@sage/xtrem-interop/nodes__sys_node_transformation__query__hasActiveMapping": "Hat aktive Zuordnung", "@sage/xtrem-interop/nodes__sys_node_transformation__query__hasActiveMapping__failed": "Hat aktive Zuordnung fehlgeschlagen.", "@sage/xtrem-interop/nodes__sys_node_transformation__query__hasActiveMapping__parameter__localNode": "Lokaler Node", "@sage/xtrem-interop/nodes__sys_node_transformation__query__hasActiveMapping__parameter__propertyId": "Eigenschaft", "@sage/xtrem-interop/nodes__sys_operation_transformation__asyncMutation__asyncExport": "Export", "@sage/xtrem-interop/nodes__sys_operation_transformation__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-interop/nodes__sys_operation_transformation__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-interop/nodes__sys_operation_transformation__node_name": "Transformation Systemvorgang", "@sage/xtrem-interop/nodes__sys_operation_transformation__property__id": "ID", "@sage/xtrem-interop/nodes__sys_operation_transformation__property__isActive": "Ist aktiv", "@sage/xtrem-interop/nodes__sys_operation_transformation__property__localNode": "Lokaler Node", "@sage/xtrem-interop/nodes__sys_operation_transformation__property__localOperationName": "Lokaler Vorgangsname", "@sage/xtrem-interop/nodes__sys_operation_transformation__property__mappings": "Z<PERSON><PERSON>nungen", "@sage/xtrem-interop/nodes__sys_operation_transformation__property__remoteApp": "Remote-Anwendung", "@sage/xtrem-interop/nodes__sys_operation_transformation__property__remoteAppVersion": "Remote-Anwendungsversion", "@sage/xtrem-interop/nodes__sys_operation_transformation__property__remoteNodeName": "Remote-Node-Name", "@sage/xtrem-interop/nodes__sys_operation_transformation__property__remoteOperationName": "Remote-Vorgangsname", "@sage/xtrem-interop/nodes__sys_operation_transformation__property__remotePackageName": "Remote-Paketname", "@sage/xtrem-interop/nodes__sys_remote_metadata__node_name": "Remote-Metadaten System", "@sage/xtrem-interop/nodes__sys_remote_metadata__query__getDataTypeInfo": "Datentypinformationen abrufen", "@sage/xtrem-interop/nodes__sys_remote_metadata__query__getDataTypeInfo__failed": "Datentypinformationen abrufen fehlgeschlagen.", "@sage/xtrem-interop/nodes__sys_remote_metadata__query__getDataTypeInfo__parameter__app": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-interop/nodes__sys_remote_metadata__query__getDataTypeInfo__parameter__name": "Name", "@sage/xtrem-interop/nodes__sys_remote_metadata__query__getDataTypeInfo__parameter__type": "<PERSON><PERSON>", "@sage/xtrem-interop/nodes__sys_remote_metadata__query__getNodeInfo": "Node-Informationen abrufen", "@sage/xtrem-interop/nodes__sys_remote_metadata__query__getNodeInfo__failed": "Node-Informationen abrufen fehlgeschlagen.", "@sage/xtrem-interop/nodes__sys_remote_metadata__query__getNodeInfo__parameter__app": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-interop/nodes__sys_remote_metadata__query__getNodeInfo__parameter__filter": "Filter", "@sage/xtrem-interop/nodes__sys_remote_metadata__query__getNodeInfo__parameter__name": "Name", "@sage/xtrem-interop/nodes__sys_synchronization_client__node_name": "Client Systemsynchronisation", "@sage/xtrem-interop/nodes__sys_synchronization_source__node_name": "Ursprung Systemsynchronisation", "@sage/xtrem-interop/nodes__sys_synchronization_source__query__getAppVersion": "Anwendungsversion abrufen", "@sage/xtrem-interop/nodes__sys_synchronization_source__query__getSynchronizationFeed": "Synchronisationsfeed abrufen", "@sage/xtrem-interop/nodes__sys_synchronization_source__query__getSynchronizationFeed__failed": "Synchronisationsfeed abrufen fehlgeschlagen.", "@sage/xtrem-interop/nodes__sys_synchronization_source__query__getSynchronizationFeed__parameter__filter": "Filter", "@sage/xtrem-interop/nodes__sys_synchronization_source__query__getSynchronizationFeed__parameter__node": "Node", "@sage/xtrem-interop/nodes__sys_synchronization_source__query__getSynchronizationFeed__parameter__selector": "Auswahl", "@sage/xtrem-interop/nodes__sys_synchronization_source__query__getSynchronizationFeed__parameter__startTick": "Starttakt", "@sage/xtrem-interop/nodes__sys_synchronization_state__asyncMutation__asyncExport": "Export", "@sage/xtrem-interop/nodes__sys_synchronization_state__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-interop/nodes__sys_synchronization_state__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-interop/nodes__sys_synchronization_state__node_name": "Status Systemsynchronisation", "@sage/xtrem-interop/nodes__sys_synchronization_state__property__lastNotificationState": "Status letzte Benachrichtigung", "@sage/xtrem-interop/nodes__sys_synchronization_state__property__node": "Node", "@sage/xtrem-interop/nodes__sys_synchronization_state__property__notificationStates": "Benachrichtigungsstatuswerte", "@sage/xtrem-interop/nodes__sys_synchronization_state__property__remoteApp": "Remote-Anwendung", "@sage/xtrem-interop/nodes__sys_synchronization_state__property__succcessStamp": "Erfolgsstempel", "@sage/xtrem-interop/nodes__sys_synchronization_state__property__syncTick": "Synchronisationstakt", "@sage/xtrem-interop/nodes__sys_synchronization_target__asyncMutation__synchronize": "Remote-Anwendungsdaten synchronisieren", "@sage/xtrem-interop/nodes__sys_synchronization_target__asyncMutation__synchronize__failed": "Synchronisieren fehlgeschlagen.", "@sage/xtrem-interop/nodes__sys_synchronization_target__asyncMutation__synchronize__parameter__filtersByNode": "<PERSON>lter nach Node", "@sage/xtrem-interop/nodes__sys_synchronization_target__asyncMutation__synchronize__parameter__forceSyncState": "Systemsynchronisation erzwingen", "@sage/xtrem-interop/nodes__sys_synchronization_target__asyncMutation__synchronize__parameter__fullSync": "Vollständige Synchronisation", "@sage/xtrem-interop/nodes__sys_synchronization_target__asyncMutation__synchronize__parameter__localNodeNames": "Lokale Nodes", "@sage/xtrem-interop/nodes__sys_synchronization_target__asyncMutation__synchronize__parameter__remoteApp": "Remote-Anwendung", "@sage/xtrem-interop/nodes__sys_synchronization_target__asyncMutation__synchronize__parameter__skipDependencies": "Abhängigkeiten überspringen", "@sage/xtrem-interop/nodes__sys_synchronization_target__node_name": "Ziel Systemsynchronisation", "@sage/xtrem-interop/package__name": "Interop", "@sage/xtrem-interop/pages__integration____navigationPanel__listItem__line11__title": "Verknüpfung", "@sage/xtrem-interop/pages__integration____navigationPanel__listItem__line12__title": "Verknüpfung", "@sage/xtrem-interop/pages__integration____navigationPanel__listItem__line2__title": "Dr<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-interop/pages__sys_app____navigationPanel__listItem__isAlive__title": "Verbunden", "@sage/xtrem-interop/pages__sys_app____navigationPanel__listItem__isConnector__title": "Connector", "@sage/xtrem-interop/pages__sys_app____navigationPanel__listItem__line2__title": "Version", "@sage/xtrem-interop/pages__sys_app____navigationPanel__listItem__line2Right__title": "Aktiv", "@sage/xtrem-interop/pages__sys_app____navigationPanel__listItem__title__title": "Name", "@sage/xtrem-interop/pages__sys_app____navigationPanel__listItem__titleRight__title": "Titel", "@sage/xtrem-interop/pages__sys_app____navigationPanel__optionsMenu__title": "Alle", "@sage/xtrem-interop/pages__sys_app____navigationPanel__optionsMenu__title__2": "Aktiv", "@sage/xtrem-interop/pages__sys_app____navigationPanel__optionsMenu__title__3": "Inaktiv", "@sage/xtrem-interop/pages__sys_app____objectTypePlural": "Verbundene Anwendungen", "@sage/xtrem-interop/pages__sys_app____objectTypeSingular": "Verbundene Anwendung", "@sage/xtrem-interop/pages__sys_app____title": "Verbundene Anwendungen", "@sage/xtrem-interop/pages__sys_app__interopPackage____title": "Interop-Paket", "@sage/xtrem-interop/pages__sys_app__isActive____title": "Aktiv", "@sage/xtrem-interop/pages__sys_app__isAlive____title": "Verbunden", "@sage/xtrem-interop/pages__sys_app__isConnector____title": "Connector", "@sage/xtrem-interop/pages__sys_app__name____title": "Name", "@sage/xtrem-interop/pages__sys_app__refreshActiveState____title": "Aktiven Status aktualisieren", "@sage/xtrem-interop/pages__sys_app__sync_all_message": "Synchronisierung gestartet.", "@sage/xtrem-interop/pages__sys_app__syncAllNodes____title": "Alle synchronisieren", "@sage/xtrem-interop/pages__sys_app__technicalBlock____title": "Technisch", "@sage/xtrem-interop/pages__sys_app__title____title": "Titel", "@sage/xtrem-interop/pages__sys_app__version____title": "Version", "@sage/xtrem-interop/pages__sys_enum_transformation____navigationPanel__inlineActions__title": "Duplizieren", "@sage/xtrem-interop/pages__sys_enum_transformation____navigationPanel__listItem__line2__title": "Aktiv", "@sage/xtrem-interop/pages__sys_enum_transformation____navigationPanel__listItem__remoteApp__title": "Remote-Anwendung", "@sage/xtrem-interop/pages__sys_enum_transformation____navigationPanel__listItem__remoteAppVersion__title": "Version Remote-Anwendung", "@sage/xtrem-interop/pages__sys_enum_transformation____navigationPanel__listItem__remoteEnum__title": "Enum remote", "@sage/xtrem-interop/pages__sys_enum_transformation____navigationPanel__listItem__title__title": "Enum lokal", "@sage/xtrem-interop/pages__sys_enum_transformation____navigationPanel__listItem__titleRight__title": "Zuordnungs-ID", "@sage/xtrem-interop/pages__sys_enum_transformation____navigationPanel__optionsMenu__title": "Aktiv", "@sage/xtrem-interop/pages__sys_enum_transformation____navigationPanel__optionsMenu__title__2": "Inaktiv", "@sage/xtrem-interop/pages__sys_enum_transformation____navigationPanel__optionsMenu__title__3": "Alle", "@sage/xtrem-interop/pages__sys_enum_transformation____objectTypePlural": "Enum-Transformationen", "@sage/xtrem-interop/pages__sys_enum_transformation____objectTypeSingular": "Enum-Transformation", "@sage/xtrem-interop/pages__sys_enum_transformation____title": "Enum-Transformationen", "@sage/xtrem-interop/pages__sys_enum_transformation__id____title": "Zuordnungs-ID", "@sage/xtrem-interop/pages__sys_enum_transformation__isActive____title": "Aktiv", "@sage/xtrem-interop/pages__sys_enum_transformation__localEnum____columns__title__name": "Name", "@sage/xtrem-interop/pages__sys_enum_transformation__localEnum____columns__title__title": "Titel", "@sage/xtrem-interop/pages__sys_enum_transformation__localEnum____title": "Enum lokal", "@sage/xtrem-interop/pages__sys_enum_transformation__mappingBlock____title": "Zuordnung System-Enum", "@sage/xtrem-interop/pages__sys_enum_transformation__mappingTable____columns__title__localEnumValue": "Lokaler Wert", "@sage/xtrem-interop/pages__sys_enum_transformation__mappingTable____columns__title__remoteEnumValue": "Remote-Wert", "@sage/xtrem-interop/pages__sys_enum_transformation__mappingTable____dropdownActions__title": "Datensatz aktualisieren", "@sage/xtrem-interop/pages__sys_enum_transformation__mappingTable____dropdownActions__title__2": "Entfernen", "@sage/xtrem-interop/pages__sys_enum_transformation__remote_data_type_not_found": "<PERSON><PERSON>-Datentyp gefunden.", "@sage/xtrem-interop/pages__sys_enum_transformation__remoteApp____title": "Remote-Anwendung", "@sage/xtrem-interop/pages__sys_enum_transformation__remoteAppVersion____title": "Version Remote-Anwendung", "@sage/xtrem-interop/pages__sys_enum_transformation__remoteEnum____title": "Enum remote", "@sage/xtrem-interop/pages__sys_enum_transformation__remoteEnumDialogSection____title": "Remote-enum auswählen", "@sage/xtrem-interop/pages__sys_enum_transformation__remoteEnumTable____columns__title__name": "Name", "@sage/xtrem-interop/pages__sys_enum_transformation__remoteEnumTable____columns__title__title": "Titel", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__dropdownActions__title": "Jetzt synchronisieren", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__dropdownActions__title__2": "Synchronisierung planen", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__dropdownActions__title__3": "Duplizieren", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__dropdownActions__title__4": "Protokoll anzeigen", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__inlineActions__title": "Duplizieren", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__listItem__lastError__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__listItem__lastSync__title": "Letzte Synchronisierung", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__listItem__localNode__title": "Lokale Entität", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__listItem__name__title": "Remote-Anwendung", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__listItem__remoteAppVersion__title": "Version Remote-Anwendung", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__listItem__remoteNodeName__title": "Remote-Entität", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__listItem__title__title": "Zuordnungs-ID", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__listItem__titleRight__title": "Aktiv", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__optionsMenu__title": "Aktiv", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__optionsMenu__title__2": "Inaktiv", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__optionsMenu__title__3": "Alle", "@sage/xtrem-interop/pages__sys_node_transformation____objectTypePlural": "Daten-Transformationen", "@sage/xtrem-interop/pages__sys_node_transformation____objectTypeSingular": "Daten-Transformation", "@sage/xtrem-interop/pages__sys_node_transformation____title": "Transformationen Synchronisation", "@sage/xtrem-interop/pages__sys_node_transformation__dialogBlock____title": "Remote-Entität", "@sage/xtrem-interop/pages__sys_node_transformation__dialogSection____title": "Remote-Entität auswählen", "@sage/xtrem-interop/pages__sys_node_transformation__id____title": "Zuordnungs-ID", "@sage/xtrem-interop/pages__sys_node_transformation__isActive____title": "Aktiv", "@sage/xtrem-interop/pages__sys_node_transformation__lastErrorDate____title": "Letzter Synchronisierungsfehler", "@sage/xtrem-interop/pages__sys_node_transformation__lastSyncDate____title": "Letzte Synchronisierung", "@sage/xtrem-interop/pages__sys_node_transformation__listOfRemoteNodes____columns__title__name": "Name", "@sage/xtrem-interop/pages__sys_node_transformation__listOfRemoteNodes____columns__title__title": "Titel", "@sage/xtrem-interop/pages__sys_node_transformation__localNode____columns__title__name": "Datensatztyp", "@sage/xtrem-interop/pages__sys_node_transformation__localNode____columns__title__package__name": "<PERSON><PERSON>", "@sage/xtrem-interop/pages__sys_node_transformation__localNode____columns__title__storage": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-interop/pages__sys_node_transformation__localNode____columns__title__title": "Datensatztyp", "@sage/xtrem-interop/pages__sys_node_transformation__localNode____title": "Lokale Entität", "@sage/xtrem-interop/pages__sys_node_transformation__localProperties____helperText": "Wählen Sie die Eigenschaften aus, die Sie synchronisieren möchten.", "@sage/xtrem-interop/pages__sys_node_transformation__localPropertiesBlock____title": "Lokale Eigenschaften", "@sage/xtrem-interop/pages__sys_node_transformation__localPropertiesSection____title": "Lokale Eigenschaften", "@sage/xtrem-interop/pages__sys_node_transformation__mappingBlock____title": "Zuordnungstabelle", "@sage/xtrem-interop/pages__sys_node_transformation__mappingSection____title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-interop/pages__sys_node_transformation__mappingTable____columns__title__kind": "Zuordnungstyp", "@sage/xtrem-interop/pages__sys_node_transformation__mappingTable____columns__title__localProperty": "Lokaler Wert", "@sage/xtrem-interop/pages__sys_node_transformation__mappingTable____columns__title__remoteProperty": "Remote-Wert", "@sage/xtrem-interop/pages__sys_node_transformation__mappingTable____dropdownActions__title": "Remote-Eigenschaft bearbeiten", "@sage/xtrem-interop/pages__sys_node_transformation__mappingTable____dropdownActions__title__2": "Datensatz aktualisieren", "@sage/xtrem-interop/pages__sys_node_transformation__mappingTable____dropdownActions__title__3": "Entfernen", "@sage/xtrem-interop/pages__sys_node_transformation__remote_not_available": "Die Remote-Daten wurden nicht abferufen. Das Remote-System ist möglicherweise nicht verfügbar.\n\nUrsprüngliche Nachricht:\n\n{{originalMessage}}", "@sage/xtrem-interop/pages__sys_node_transformation__remoteApp____title": "Remote-Anwendung", "@sage/xtrem-interop/pages__sys_node_transformation__remoteNodeName____title": "Remote-Entität", "@sage/xtrem-interop/pages__sys_node_transformation__remoteNodeNameName____title": "Remote-Entität", "@sage/xtrem-interop/pages__sys_node_transformation__remotePropertiesBlock____title": "Remote-Eigenschaften", "@sage/xtrem-interop/pages__sys_node_transformation__remotePropertiesSection____title": "Remote-Eigenschaften", "@sage/xtrem-interop/pages__sys_node_transformation__remoteVersion____title": "Remote-Version", "@sage/xtrem-interop/pages__sys_node_transformation__scheduleSync____title": "Synchronisierung einplanen", "@sage/xtrem-interop/pages__sys_node_transformation__selectFromLocalProperties____title": "Zeilen aus lokalen Eigenschaften hinzufügen", "@sage/xtrem-interop/pages__sys_node_transformation__synchronizeResult____title": "Ergebnisse Synchronisierung", "@sage/xtrem-interop/pages__sys_node_transformation__syncNow____title": "Jetzt synchronisieren", "@sage/xtrem-interop/pages__sys_node_transformation__syncSection____title": "Sync.", "@sage/xtrem-interop/pages__sys_node_transformation__too_many_remote_properties": "<PERSON><PERSON> wurden zu viele Remote-Eigenschaften für die Zuweisung ausgewählt. Überprüfen Sie die Ergebnisse zur Bestätigung.", "@sage/xtrem-interop/permission__create__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-interop/permission__default_instance__name": "Standardinstanz", "@sage/xtrem-interop/permission__delete__name": "Löschen", "@sage/xtrem-interop/permission__manage__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-interop/permission__read__name": "<PERSON><PERSON>", "@sage/xtrem-interop/permission__update__name": "Aktualisieren", "@sage/xtrem-interop/service_options__synchronization_service_option__name": "Synchronisierungsdienstoption"}