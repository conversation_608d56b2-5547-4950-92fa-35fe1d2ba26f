{"@sage/xtrem-interop/activity__sys_app__name": "Aplicación del sistema", "@sage/xtrem-interop/activity__sys_enum_synchronization__name": "Sys enum synchronization", "@sage/xtrem-interop/activity__sys_synchronization__name": "Sincronización de sistema", "@sage/xtrem-interop/data_types__id__name": "Id.", "@sage/xtrem-interop/data_types__mapping_value__name": "Mapping value", "@sage/xtrem-interop/data_types__node_mapping_kind_enum__name": "Node mapping kind enum", "@sage/xtrem-interop/data_types__price__name": "Precio", "@sage/xtrem-interop/data_types__quantity__name": "Cantidad", "@sage/xtrem-interop/data_types__test_local_status_enum__name": "Test local status enum", "@sage/xtrem-interop/data_types__test_remote_status_enum__name": "Test remote status enum", "@sage/xtrem-interop/enums__node_mapping_kind__constant_to_path": "Constante con ruta", "@sage/xtrem-interop/enums__node_mapping_kind__function_to_path": "Función con ruta", "@sage/xtrem-interop/enums__node_mapping_kind__path_to_constant": "<PERSON>uta con constante", "@sage/xtrem-interop/enums__node_mapping_kind__path_to_function": "Ruta con función", "@sage/xtrem-interop/enums__node_mapping_kind__path_to_path": "Ruta con ruta", "@sage/xtrem-interop/enums__test_local_status__inProgress": "En curso", "@sage/xtrem-interop/enums__test_local_status__start": "Iniciado", "@sage/xtrem-interop/enums__test_local_status__stop": "Cancelado", "@sage/xtrem-interop/enums__test_local_status__track": "En seguimiento", "@sage/xtrem-interop/enums__test_remote_status__begin": "Iniciado", "@sage/xtrem-interop/enums__test_remote_status__end": "Finalizado", "@sage/xtrem-interop/enums__test_remote_status__inProgress": "En curso", "@sage/xtrem-interop/menu_item__interop": "Interoperabilidad", "@sage/xtrem-interop/nodes__synchronization_state__property__secondaryLink": "<PERSON><PERSON><PERSON><PERSON> secunda<PERSON>", "@sage/xtrem-interop/nodes__synchronization_state__property__secondaryText": "Texto secundario", "@sage/xtrem-interop/nodes__sys_app__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-interop/nodes__sys_app__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-interop/nodes__sys_app__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-interop/nodes__sys_app__mutation__refreshState": "Actualizar estado", "@sage/xtrem-interop/nodes__sys_app__mutation__refreshState__failed": "Error al actualizar el estado", "@sage/xtrem-interop/nodes__sys_app__node_name": "Aplicación del sistema", "@sage/xtrem-interop/nodes__sys_app__property__interopPackage": "Paquete de interoperabilidad", "@sage/xtrem-interop/nodes__sys_app__property__isActive": "Activa", "@sage/xtrem-interop/nodes__sys_app__property__isAlive": "Conectada", "@sage/xtrem-interop/nodes__sys_app__property__isConnector": "Conector", "@sage/xtrem-interop/nodes__sys_app__property__name": "Nombre", "@sage/xtrem-interop/nodes__sys_app__property__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-interop/nodes__sys_app__property__version": "Versión", "@sage/xtrem-interop/nodes__sys_app__query__getAppInfo": "Obtener información de aplicación", "@sage/xtrem-interop/nodes__sys_app__query__getAppInfo__failed": "Error al obtener información de la aplicación", "@sage/xtrem-interop/nodes__sys_enum_mapping__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-interop/nodes__sys_enum_mapping__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-interop/nodes__sys_enum_mapping__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-interop/nodes__sys_enum_mapping__node_name": "Mapeo de enumeración de sistema", "@sage/xtrem-interop/nodes__sys_enum_mapping__property__localEnumValue": "Valor de enumeración local", "@sage/xtrem-interop/nodes__sys_enum_mapping__property__remoteEnumValue": "Valor de enumeración remota", "@sage/xtrem-interop/nodes__sys_enum_mapping__property__transform": "Transformación", "@sage/xtrem-interop/nodes__sys_enum_transformation__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-interop/nodes__sys_enum_transformation__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-interop/nodes__sys_enum_transformation__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-interop/nodes__sys_enum_transformation__mutation__updateActiveMappings": "Actualizar mapeos activos", "@sage/xtrem-interop/nodes__sys_enum_transformation__mutation__updateActiveMappings__failed": "Error al actualizar los mapeos activos", "@sage/xtrem-interop/nodes__sys_enum_transformation__mutation__updateActiveMappings__parameter__parameters": "Parámetros", "@sage/xtrem-interop/nodes__sys_enum_transformation__node_name": "Transformación de enumeración de sistema", "@sage/xtrem-interop/nodes__sys_enum_transformation__property__id": "Id.", "@sage/xtrem-interop/nodes__sys_enum_transformation__property__isActive": "Activa", "@sage/xtrem-interop/nodes__sys_enum_transformation__property__localEnum": "Enumeración local", "@sage/xtrem-interop/nodes__sys_enum_transformation__property__map": "Mapeo", "@sage/xtrem-interop/nodes__sys_enum_transformation__property__remoteApp": "Aplicación remota", "@sage/xtrem-interop/nodes__sys_enum_transformation__property__remoteAppVersion": "Versión de aplicación remota", "@sage/xtrem-interop/nodes__sys_enum_transformation__property__remoteEnum": "Enumeración remota", "@sage/xtrem-interop/nodes__sys_node_mapping__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-interop/nodes__sys_node_mapping__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-interop/nodes__sys_node_mapping__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-interop/nodes__sys_node_mapping__node_name": "Mapeo de nodo de sistema", "@sage/xtrem-interop/nodes__sys_node_mapping__property__kind": "Tipo", "@sage/xtrem-interop/nodes__sys_node_mapping__property__localProperty": "Propiedad local", "@sage/xtrem-interop/nodes__sys_node_mapping__property__remoteProperty": "Propiedad remota", "@sage/xtrem-interop/nodes__sys_node_mapping__property__transform": "Transformación", "@sage/xtrem-interop/nodes__sys_node_transformation__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-interop/nodes__sys_node_transformation__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-interop/nodes__sys_node_transformation__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-interop/nodes__sys_node_transformation__mutation__updateActiveMappings": "Actualizar mapeos activos", "@sage/xtrem-interop/nodes__sys_node_transformation__mutation__updateActiveMappings__failed": "Error al actualizar los mapeos activos", "@sage/xtrem-interop/nodes__sys_node_transformation__mutation__updateActiveMappings__parameter__parameters": "Parámetros", "@sage/xtrem-interop/nodes__sys_node_transformation__node_name": "Transformación de nodo de sistema", "@sage/xtrem-interop/nodes__sys_node_transformation__property__filter": "Filtro", "@sage/xtrem-interop/nodes__sys_node_transformation__property__id": "Id.", "@sage/xtrem-interop/nodes__sys_node_transformation__property__isActive": "Activa", "@sage/xtrem-interop/nodes__sys_node_transformation__property__lastError": "<PERSON><PERSON><PERSON> error", "@sage/xtrem-interop/nodes__sys_node_transformation__property__lastSync": "Última sincronización", "@sage/xtrem-interop/nodes__sys_node_transformation__property__localNode": "Nodo local", "@sage/xtrem-interop/nodes__sys_node_transformation__property__map": "Mapeo", "@sage/xtrem-interop/nodes__sys_node_transformation__property__mappings": "Mapeos", "@sage/xtrem-interop/nodes__sys_node_transformation__property__remoteApp": "Aplicación remota", "@sage/xtrem-interop/nodes__sys_node_transformation__property__remoteAppVersion": "Versión de aplicación remota", "@sage/xtrem-interop/nodes__sys_node_transformation__property__remoteMappings": "Mapeos re<PERSON>s", "@sage/xtrem-interop/nodes__sys_node_transformation__property__remoteNodeName": "Nombre de nodo remoto", "@sage/xtrem-interop/nodes__sys_node_transformation__query__hasActiveMapping": "Mapeo activo", "@sage/xtrem-interop/nodes__sys_node_transformation__query__hasActiveMapping__failed": "Error con mapeo activo", "@sage/xtrem-interop/nodes__sys_node_transformation__query__hasActiveMapping__parameter__localNode": "Nodo local", "@sage/xtrem-interop/nodes__sys_node_transformation__query__hasActiveMapping__parameter__propertyId": "<PERSON><PERSON><PERSON> de propiedad", "@sage/xtrem-interop/nodes__sys_operation_transformation__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-interop/nodes__sys_operation_transformation__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-interop/nodes__sys_operation_transformation__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-interop/nodes__sys_operation_transformation__node_name": "Transformación de operación de sistema", "@sage/xtrem-interop/nodes__sys_operation_transformation__property__id": "Id.", "@sage/xtrem-interop/nodes__sys_operation_transformation__property__isActive": "Activa", "@sage/xtrem-interop/nodes__sys_operation_transformation__property__localNode": "Nodo local", "@sage/xtrem-interop/nodes__sys_operation_transformation__property__localOperationName": "Nombre de operación local", "@sage/xtrem-interop/nodes__sys_operation_transformation__property__mappings": "Mapeos", "@sage/xtrem-interop/nodes__sys_operation_transformation__property__remoteApp": "Aplicación remota", "@sage/xtrem-interop/nodes__sys_operation_transformation__property__remoteAppVersion": "Versión de aplicación remota", "@sage/xtrem-interop/nodes__sys_operation_transformation__property__remoteNodeName": "Nombre de nodo remoto", "@sage/xtrem-interop/nodes__sys_operation_transformation__property__remoteOperationName": "Nombre de operación remota", "@sage/xtrem-interop/nodes__sys_operation_transformation__property__remotePackageName": "Nombre de paquete remoto", "@sage/xtrem-interop/nodes__sys_remote_metadata__node_name": "Metadatos de sistema remoto", "@sage/xtrem-interop/nodes__sys_remote_metadata__query__getDataTypeInfo": "Obtener información de tipo de datos", "@sage/xtrem-interop/nodes__sys_remote_metadata__query__getDataTypeInfo__failed": "Error al obtener información del tipo de datos", "@sage/xtrem-interop/nodes__sys_remote_metadata__query__getDataTypeInfo__parameter__app": "Aplicación", "@sage/xtrem-interop/nodes__sys_remote_metadata__query__getDataTypeInfo__parameter__name": "Nombre", "@sage/xtrem-interop/nodes__sys_remote_metadata__query__getDataTypeInfo__parameter__type": "Tipo", "@sage/xtrem-interop/nodes__sys_remote_metadata__query__getNodeInfo": "Obtener información de nodo", "@sage/xtrem-interop/nodes__sys_remote_metadata__query__getNodeInfo__failed": "Error al obtener información del nodo", "@sage/xtrem-interop/nodes__sys_remote_metadata__query__getNodeInfo__parameter__app": "Aplicación", "@sage/xtrem-interop/nodes__sys_remote_metadata__query__getNodeInfo__parameter__filter": "Filtro", "@sage/xtrem-interop/nodes__sys_remote_metadata__query__getNodeInfo__parameter__name": "Nombre", "@sage/xtrem-interop/nodes__sys_synchronization_client__node_name": "Cliente de sincronización de sistema", "@sage/xtrem-interop/nodes__sys_synchronization_source__node_name": "Origen de sincronización de sistema", "@sage/xtrem-interop/nodes__sys_synchronization_source__query__getAppVersion": "Obtener versión de aplicación", "@sage/xtrem-interop/nodes__sys_synchronization_source__query__getSynchronizationFeed": "Obtener lista de sincronización", "@sage/xtrem-interop/nodes__sys_synchronization_source__query__getSynchronizationFeed__failed": "Error al obtener la lista de sincronización", "@sage/xtrem-interop/nodes__sys_synchronization_source__query__getSynchronizationFeed__parameter__filter": "Filtro", "@sage/xtrem-interop/nodes__sys_synchronization_source__query__getSynchronizationFeed__parameter__node": "Nodo", "@sage/xtrem-interop/nodes__sys_synchronization_source__query__getSynchronizationFeed__parameter__selector": "Selector", "@sage/xtrem-interop/nodes__sys_synchronization_source__query__getSynchronizationFeed__parameter__startTick": "Marca de inicio", "@sage/xtrem-interop/nodes__sys_synchronization_state__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-interop/nodes__sys_synchronization_state__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-interop/nodes__sys_synchronization_state__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-interop/nodes__sys_synchronization_state__node_name": "Estado de sincronización de sistema", "@sage/xtrem-interop/nodes__sys_synchronization_state__property__lastNotificationState": "Último estado de notificación", "@sage/xtrem-interop/nodes__sys_synchronization_state__property__node": "Nodo", "@sage/xtrem-interop/nodes__sys_synchronization_state__property__notificationStates": "Estados de notificación", "@sage/xtrem-interop/nodes__sys_synchronization_state__property__remoteApp": "Aplicación remota", "@sage/xtrem-interop/nodes__sys_synchronization_state__property__succcessStamp": "Marca de confirmación", "@sage/xtrem-interop/nodes__sys_synchronization_state__property__syncTick": "Marca de sincronización", "@sage/xtrem-interop/nodes__sys_synchronization_target__asyncMutation__synchronize": "Sincronizar datos de aplicación remota", "@sage/xtrem-interop/nodes__sys_synchronization_target__asyncMutation__synchronize__failed": "Error de sincronización", "@sage/xtrem-interop/nodes__sys_synchronization_target__asyncMutation__synchronize__parameter__filtersByNode": "Filtros por nodo", "@sage/xtrem-interop/nodes__sys_synchronization_target__asyncMutation__synchronize__parameter__forceSyncState": "Forzar estado de sincronización", "@sage/xtrem-interop/nodes__sys_synchronization_target__asyncMutation__synchronize__parameter__fullSync": "Sincronización completa", "@sage/xtrem-interop/nodes__sys_synchronization_target__asyncMutation__synchronize__parameter__localNodeNames": "Nodos locales", "@sage/xtrem-interop/nodes__sys_synchronization_target__asyncMutation__synchronize__parameter__remoteApp": "Aplicación remota", "@sage/xtrem-interop/nodes__sys_synchronization_target__asyncMutation__synchronize__parameter__skipDependencies": "Dependencias omitidas", "@sage/xtrem-interop/nodes__sys_synchronization_target__node_name": "Destino de sincronización de sistema", "@sage/xtrem-interop/package__name": "Interoperabilidad", "@sage/xtrem-interop/pages__integration____navigationPanel__listItem__line11__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-interop/pages__integration____navigationPanel__listItem__line12__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-interop/pages__integration____navigationPanel__listItem__line2__title": "Aplicación de terceros", "@sage/xtrem-interop/pages__sys_app____navigationPanel__listItem__isAlive__title": "Conectada", "@sage/xtrem-interop/pages__sys_app____navigationPanel__listItem__isConnector__title": "Conector", "@sage/xtrem-interop/pages__sys_app____navigationPanel__listItem__line2__title": "Versión", "@sage/xtrem-interop/pages__sys_app____navigationPanel__listItem__line2Right__title": "Activa", "@sage/xtrem-interop/pages__sys_app____navigationPanel__listItem__title__title": "Nombre", "@sage/xtrem-interop/pages__sys_app____navigationPanel__listItem__titleRight__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-interop/pages__sys_app____navigationPanel__optionsMenu__title": "<PERSON><PERSON>", "@sage/xtrem-interop/pages__sys_app____navigationPanel__optionsMenu__title__2": "Activas", "@sage/xtrem-interop/pages__sys_app____navigationPanel__optionsMenu__title__3": "Inactivas", "@sage/xtrem-interop/pages__sys_app____objectTypePlural": "Aplicaciones conectadas", "@sage/xtrem-interop/pages__sys_app____objectTypeSingular": "Aplicación conectada", "@sage/xtrem-interop/pages__sys_app____title": "Aplicaciones conectadas", "@sage/xtrem-interop/pages__sys_app__interopPackage____title": "Paquete de interoperabilidad", "@sage/xtrem-interop/pages__sys_app__isActive____title": "Activa", "@sage/xtrem-interop/pages__sys_app__isAlive____title": "Conectada", "@sage/xtrem-interop/pages__sys_app__isConnector____title": "Conector", "@sage/xtrem-interop/pages__sys_app__name____title": "Nombre", "@sage/xtrem-interop/pages__sys_app__refreshActiveState____title": "Actualizar estado", "@sage/xtrem-interop/pages__sys_app__sync_all_message": "La sincronización se ha iniciado.", "@sage/xtrem-interop/pages__sys_app__syncAllNodes____title": "<PERSON><PERSON><PERSON><PERSON><PERSON> todo", "@sage/xtrem-interop/pages__sys_app__technicalBlock____title": "<PERSON><PERSON> t<PERSON>", "@sage/xtrem-interop/pages__sys_app__title____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-interop/pages__sys_app__version____title": "Versión", "@sage/xtrem-interop/pages__sys_enum_transformation____navigationPanel__inlineActions__title": "Duplicar", "@sage/xtrem-interop/pages__sys_enum_transformation____navigationPanel__listItem__line2__title": "Activa", "@sage/xtrem-interop/pages__sys_enum_transformation____navigationPanel__listItem__remoteApp__title": "Aplicación remota", "@sage/xtrem-interop/pages__sys_enum_transformation____navigationPanel__listItem__remoteAppVersion__title": "Versión de aplicación remota", "@sage/xtrem-interop/pages__sys_enum_transformation____navigationPanel__listItem__remoteEnum__title": "Enumeración remota", "@sage/xtrem-interop/pages__sys_enum_transformation____navigationPanel__listItem__title__title": "Enumeración local", "@sage/xtrem-interop/pages__sys_enum_transformation____navigationPanel__listItem__titleRight__title": "Id. de mapeo", "@sage/xtrem-interop/pages__sys_enum_transformation____navigationPanel__optionsMenu__title": "Activas", "@sage/xtrem-interop/pages__sys_enum_transformation____navigationPanel__optionsMenu__title__2": "Inactivas", "@sage/xtrem-interop/pages__sys_enum_transformation____navigationPanel__optionsMenu__title__3": "<PERSON><PERSON>", "@sage/xtrem-interop/pages__sys_enum_transformation____objectTypePlural": "Transformaciones de enumeración", "@sage/xtrem-interop/pages__sys_enum_transformation____objectTypeSingular": "Transformación de enumeración", "@sage/xtrem-interop/pages__sys_enum_transformation____title": "Transformaciones de enumeración", "@sage/xtrem-interop/pages__sys_enum_transformation__id____title": "Id. de mapeo", "@sage/xtrem-interop/pages__sys_enum_transformation__isActive____title": "Activa", "@sage/xtrem-interop/pages__sys_enum_transformation__localEnum____columns__title__name": "Nombre", "@sage/xtrem-interop/pages__sys_enum_transformation__localEnum____columns__title__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-interop/pages__sys_enum_transformation__localEnum____title": "Enumeración local", "@sage/xtrem-interop/pages__sys_enum_transformation__mappingBlock____title": "Mapeo de enumeración", "@sage/xtrem-interop/pages__sys_enum_transformation__mappingTable____columns__title__localEnumValue": "Valor local", "@sage/xtrem-interop/pages__sys_enum_transformation__mappingTable____columns__title__remoteEnumValue": "<PERSON><PERSON> remoto", "@sage/xtrem-interop/pages__sys_enum_transformation__mappingTable____dropdownActions__title": "Actualizar registro", "@sage/xtrem-interop/pages__sys_enum_transformation__mappingTable____dropdownActions__title__2": "<PERSON><PERSON><PERSON>", "@sage/xtrem-interop/pages__sys_enum_transformation__remote_data_type_not_found": "No se ha encontrado ningún tipo de datos remotos.", "@sage/xtrem-interop/pages__sys_enum_transformation__remoteApp____title": "Aplicación remota", "@sage/xtrem-interop/pages__sys_enum_transformation__remoteAppVersion____title": "Versión de aplicación remota", "@sage/xtrem-interop/pages__sys_enum_transformation__remoteEnum____title": "Enumeración remota", "@sage/xtrem-interop/pages__sys_enum_transformation__remoteEnumDialogSection____title": "Seleccionar enumeración remota", "@sage/xtrem-interop/pages__sys_enum_transformation__remoteEnumTable____columns__title__name": "Nombre", "@sage/xtrem-interop/pages__sys_enum_transformation__remoteEnumTable____columns__title__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__dropdownActions__title": "Sincronizar", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__dropdownActions__title__2": "Programar sincronización", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__dropdownActions__title__3": "Duplicar", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__dropdownActions__title__4": "<PERSON>rar traza", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__inlineActions__title": "Duplicar", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__listItem__lastError__title": "<PERSON><PERSON><PERSON> error", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__listItem__lastSync__title": "Última sincronización", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__listItem__localNode__title": "Entidad local", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__listItem__name__title": "Aplicación remota", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__listItem__remoteAppVersion__title": "Versión de aplicación remota", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__listItem__remoteNodeName__title": "Entidad remota", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__listItem__title__title": "Id. de mapeo", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__listItem__titleRight__title": "Activa", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__optionsMenu__title": "Activas", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__optionsMenu__title__2": "Inactivas", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__optionsMenu__title__3": "<PERSON><PERSON>", "@sage/xtrem-interop/pages__sys_node_transformation____objectTypePlural": "Transformaciones de datos", "@sage/xtrem-interop/pages__sys_node_transformation____objectTypeSingular": "Transformación de datos", "@sage/xtrem-interop/pages__sys_node_transformation____title": "Transformaciones de sincronización", "@sage/xtrem-interop/pages__sys_node_transformation__dialogBlock____title": "Entidad remota", "@sage/xtrem-interop/pages__sys_node_transformation__dialogSection____title": "Seleccionar entidad remota", "@sage/xtrem-interop/pages__sys_node_transformation__id____title": "Id. de mapeo", "@sage/xtrem-interop/pages__sys_node_transformation__isActive____title": "Activa", "@sage/xtrem-interop/pages__sys_node_transformation__lastErrorDate____title": "Último error de sincronización", "@sage/xtrem-interop/pages__sys_node_transformation__lastSyncDate____title": "Última sincronización", "@sage/xtrem-interop/pages__sys_node_transformation__listOfRemoteNodes____columns__title__name": "Nombre", "@sage/xtrem-interop/pages__sys_node_transformation__listOfRemoteNodes____columns__title__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-interop/pages__sys_node_transformation__localNode____columns__title__name": "Tipo de registro", "@sage/xtrem-interop/pages__sys_node_transformation__localNode____columns__title__package__name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-interop/pages__sys_node_transformation__localNode____columns__title__storage": "Almacenamiento", "@sage/xtrem-interop/pages__sys_node_transformation__localNode____columns__title__title": "Tipo de registro", "@sage/xtrem-interop/pages__sys_node_transformation__localNode____title": "Entidad local", "@sage/xtrem-interop/pages__sys_node_transformation__localProperties____helperText": "Selecciona las propiedades que quieras sincronizar.", "@sage/xtrem-interop/pages__sys_node_transformation__localPropertiesBlock____title": "Propiedades locales", "@sage/xtrem-interop/pages__sys_node_transformation__localPropertiesSection____title": "Propiedades locales", "@sage/xtrem-interop/pages__sys_node_transformation__mappingBlock____title": "Tabla de mapeo", "@sage/xtrem-interop/pages__sys_node_transformation__mappingSection____title": "Mapeo", "@sage/xtrem-interop/pages__sys_node_transformation__mappingTable____columns__title__kind": "Tipo de mapeo", "@sage/xtrem-interop/pages__sys_node_transformation__mappingTable____columns__title__localProperty": "Valor local", "@sage/xtrem-interop/pages__sys_node_transformation__mappingTable____columns__title__remoteProperty": "<PERSON><PERSON> remoto", "@sage/xtrem-interop/pages__sys_node_transformation__mappingTable____dropdownActions__title": "<PERSON><PERSON> propiedad remota", "@sage/xtrem-interop/pages__sys_node_transformation__mappingTable____dropdownActions__title__2": "Actualizar registro", "@sage/xtrem-interop/pages__sys_node_transformation__mappingTable____dropdownActions__title__3": "<PERSON><PERSON><PERSON>", "@sage/xtrem-interop/pages__sys_node_transformation__remote_not_available": "No se han recuperado los datos remotos. Es posible que el sistema remoto no esté disponible.\n\nMensaje original:\n\n{{originalMessage}}", "@sage/xtrem-interop/pages__sys_node_transformation__remoteApp____title": "Aplicación remota", "@sage/xtrem-interop/pages__sys_node_transformation__remoteNodeName____title": "Entidad remota", "@sage/xtrem-interop/pages__sys_node_transformation__remoteNodeNameName____title": "Entidad remota", "@sage/xtrem-interop/pages__sys_node_transformation__remotePropertiesBlock____title": "Propiedades remotas", "@sage/xtrem-interop/pages__sys_node_transformation__remotePropertiesSection____title": "Propiedades remotas", "@sage/xtrem-interop/pages__sys_node_transformation__remoteVersion____title": "Versión remota", "@sage/xtrem-interop/pages__sys_node_transformation__scheduleSync____title": "Programar sincronización", "@sage/xtrem-interop/pages__sys_node_transformation__selectFromLocalProperties____title": "Añadir líneas de propiedades locales", "@sage/xtrem-interop/pages__sys_node_transformation__synchronizeResult____title": "Resultados de sincronización", "@sage/xtrem-interop/pages__sys_node_transformation__syncNow____title": "Sincronizar", "@sage/xtrem-interop/pages__sys_node_transformation__syncSection____title": "Sincronización", "@sage/xtrem-interop/pages__sys_node_transformation__too_many_remote_properties": "Se han seleccionado demasiadas propiedades remotas. Comprueba que la asignación es correcta.", "@sage/xtrem-interop/permission__create__name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-interop/permission__default_instance__name": "Instancia por defecto", "@sage/xtrem-interop/permission__delete__name": "Eliminar", "@sage/xtrem-interop/permission__manage__name": "Gestionar", "@sage/xtrem-interop/permission__read__name": "<PERSON><PERSON>", "@sage/xtrem-interop/permission__update__name": "Actualizar", "@sage/xtrem-interop/service_options__synchronization_service_option__name": "Opción de servicio de sincronización"}