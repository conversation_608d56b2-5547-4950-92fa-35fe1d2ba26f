{"@sage/xtrem-interop/activity__sys_app__name": "App système", "@sage/xtrem-interop/activity__sys_enum_synchronization__name": "Synchronisation enum sys", "@sage/xtrem-interop/activity__sys_synchronization__name": "Synchronisation système", "@sage/xtrem-interop/data_types__id__name": "Code", "@sage/xtrem-interop/data_types__mapping_value__name": "Valeur de mapping", "@sage/xtrem-interop/data_types__node_mapping_kind_enum__name": "Enum type mapping node", "@sage/xtrem-interop/data_types__price__name": "Prix", "@sage/xtrem-interop/data_types__quantity__name": "Quantité", "@sage/xtrem-interop/data_types__test_local_status_enum__name": "Test enum statut local", "@sage/xtrem-interop/data_types__test_remote_status_enum__name": "Test enum statut distant", "@sage/xtrem-interop/enums__node_mapping_kind__constant_to_path": "Constante vers chemin", "@sage/xtrem-interop/enums__node_mapping_kind__function_to_path": "Fonction vers chemin", "@sage/xtrem-interop/enums__node_mapping_kind__path_to_constant": "Chemin vers constante", "@sage/xtrem-interop/enums__node_mapping_kind__path_to_function": "Chemin vers fonction", "@sage/xtrem-interop/enums__node_mapping_kind__path_to_path": "Chemin vers chemin", "@sage/xtrem-interop/enums__test_local_status__inProgress": "En cours", "@sage/xtrem-interop/enums__test_local_status__start": "D<PERSON>but", "@sage/xtrem-interop/enums__test_local_status__stop": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-interop/enums__test_local_status__track": "<PERSON><PERSON><PERSON>", "@sage/xtrem-interop/enums__test_remote_status__begin": "D<PERSON>but", "@sage/xtrem-interop/enums__test_remote_status__end": "Fin", "@sage/xtrem-interop/enums__test_remote_status__inProgress": "En cours", "@sage/xtrem-interop/menu_item__interop": "Interop", "@sage/xtrem-interop/nodes__synchronization_state__property__secondaryLink": "Lien secondaire", "@sage/xtrem-interop/nodes__synchronization_state__property__secondaryText": "Texte secondaire", "@sage/xtrem-interop/nodes__sys_app__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-interop/nodes__sys_app__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-interop/nodes__sys_app__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-interop/nodes__sys_app__mutation__refreshState": "Ra<PERSON><PERSON><PERSON><PERSON> le statut", "@sage/xtrem-interop/nodes__sys_app__mutation__refreshState__failed": "L'actualisation du statut a échoué.", "@sage/xtrem-interop/nodes__sys_app__node_name": "App système", "@sage/xtrem-interop/nodes__sys_app__property__interopPackage": "Package interop", "@sage/xtrem-interop/nodes__sys_app__property__isActive": "Active", "@sage/xtrem-interop/nodes__sys_app__property__isAlive": "Active", "@sage/xtrem-interop/nodes__sys_app__property__isConnector": "Connecteur", "@sage/xtrem-interop/nodes__sys_app__property__name": "Nom", "@sage/xtrem-interop/nodes__sys_app__property__title": "Intitulé", "@sage/xtrem-interop/nodes__sys_app__property__version": "Version", "@sage/xtrem-interop/nodes__sys_app__query__getAppInfo": "Obtenir info application", "@sage/xtrem-interop/nodes__sys_app__query__getAppInfo__failed": "La récupération des informations de l'application a échoué.", "@sage/xtrem-interop/nodes__sys_enum_mapping__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-interop/nodes__sys_enum_mapping__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-interop/nodes__sys_enum_mapping__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-interop/nodes__sys_enum_mapping__node_name": "Mapping enum sys", "@sage/xtrem-interop/nodes__sys_enum_mapping__property__localEnumValue": "Valeur enum local", "@sage/xtrem-interop/nodes__sys_enum_mapping__property__remoteEnumValue": "Valeur enum distant", "@sage/xtrem-interop/nodes__sys_enum_mapping__property__transform": "Transformation", "@sage/xtrem-interop/nodes__sys_enum_transformation__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-interop/nodes__sys_enum_transformation__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-interop/nodes__sys_enum_transformation__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-interop/nodes__sys_enum_transformation__mutation__updateActiveMappings": "Mettre à jour mappings actifs", "@sage/xtrem-interop/nodes__sys_enum_transformation__mutation__updateActiveMappings__failed": "La mise à jour des mappings actifs a échoué.", "@sage/xtrem-interop/nodes__sys_enum_transformation__mutation__updateActiveMappings__parameter__parameters": "Paramètres", "@sage/xtrem-interop/nodes__sys_enum_transformation__node_name": "Transformation enum système", "@sage/xtrem-interop/nodes__sys_enum_transformation__property__id": "Code", "@sage/xtrem-interop/nodes__sys_enum_transformation__property__isActive": "Active", "@sage/xtrem-interop/nodes__sys_enum_transformation__property__localEnum": "Enum local", "@sage/xtrem-interop/nodes__sys_enum_transformation__property__map": "Mapping", "@sage/xtrem-interop/nodes__sys_enum_transformation__property__remoteApp": "App distante", "@sage/xtrem-interop/nodes__sys_enum_transformation__property__remoteAppVersion": "Version de l'app distante", "@sage/xtrem-interop/nodes__sys_enum_transformation__property__remoteEnum": "Enum distant", "@sage/xtrem-interop/nodes__sys_node_mapping__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-interop/nodes__sys_node_mapping__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-interop/nodes__sys_node_mapping__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-interop/nodes__sys_node_mapping__node_name": "Mapping node sys", "@sage/xtrem-interop/nodes__sys_node_mapping__property__kind": "Type", "@sage/xtrem-interop/nodes__sys_node_mapping__property__localProperty": "Propriété locale", "@sage/xtrem-interop/nodes__sys_node_mapping__property__remoteProperty": "Propriété distante", "@sage/xtrem-interop/nodes__sys_node_mapping__property__transform": "Transformation", "@sage/xtrem-interop/nodes__sys_node_transformation__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-interop/nodes__sys_node_transformation__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-interop/nodes__sys_node_transformation__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-interop/nodes__sys_node_transformation__mutation__updateActiveMappings": "Mettre à jour mappings actifs", "@sage/xtrem-interop/nodes__sys_node_transformation__mutation__updateActiveMappings__failed": "La mise à jour des mappings actifs a échoué.", "@sage/xtrem-interop/nodes__sys_node_transformation__mutation__updateActiveMappings__parameter__parameters": "Paramètres", "@sage/xtrem-interop/nodes__sys_node_transformation__node_name": "Transformation node système", "@sage/xtrem-interop/nodes__sys_node_transformation__property__filter": "Filtre", "@sage/xtrem-interop/nodes__sys_node_transformation__property__id": "Code", "@sage/xtrem-interop/nodes__sys_node_transformation__property__isActive": "Active", "@sage/xtrem-interop/nodes__sys_node_transformation__property__lastError": "<PERSON><PERSON><PERSON> erreur", "@sage/xtrem-interop/nodes__sys_node_transformation__property__lastSync": "<PERSON><PERSON><PERSON> sync", "@sage/xtrem-interop/nodes__sys_node_transformation__property__localNode": "Node local", "@sage/xtrem-interop/nodes__sys_node_transformation__property__map": "Mapping", "@sage/xtrem-interop/nodes__sys_node_transformation__property__mappings": "Mappings", "@sage/xtrem-interop/nodes__sys_node_transformation__property__remoteApp": "Application distante", "@sage/xtrem-interop/nodes__sys_node_transformation__property__remoteAppVersion": "Version de l'application distante", "@sage/xtrem-interop/nodes__sys_node_transformation__property__remoteMappings": "Mappings distants", "@sage/xtrem-interop/nodes__sys_node_transformation__property__remoteNodeName": "Nom du node distant", "@sage/xtrem-interop/nodes__sys_node_transformation__query__hasActiveMapping": "Mapping actif disponible", "@sage/xtrem-interop/nodes__sys_node_transformation__query__hasActiveMapping__failed": "Le mapping actif a échoué.", "@sage/xtrem-interop/nodes__sys_node_transformation__query__hasActiveMapping__parameter__localNode": "Node local", "@sage/xtrem-interop/nodes__sys_node_transformation__query__hasActiveMapping__parameter__propertyId": "Code propriété", "@sage/xtrem-interop/nodes__sys_operation_transformation__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-interop/nodes__sys_operation_transformation__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-interop/nodes__sys_operation_transformation__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-interop/nodes__sys_operation_transformation__node_name": "Transformation d'opération système", "@sage/xtrem-interop/nodes__sys_operation_transformation__property__id": "Code", "@sage/xtrem-interop/nodes__sys_operation_transformation__property__isActive": "Active", "@sage/xtrem-interop/nodes__sys_operation_transformation__property__localNode": "Node local", "@sage/xtrem-interop/nodes__sys_operation_transformation__property__localOperationName": "Nom d'opération local", "@sage/xtrem-interop/nodes__sys_operation_transformation__property__mappings": "Mappings", "@sage/xtrem-interop/nodes__sys_operation_transformation__property__remoteApp": "Application distante", "@sage/xtrem-interop/nodes__sys_operation_transformation__property__remoteAppVersion": "Version de l'application distante", "@sage/xtrem-interop/nodes__sys_operation_transformation__property__remoteNodeName": "Nom de node distant", "@sage/xtrem-interop/nodes__sys_operation_transformation__property__remoteOperationName": "Nom d'opération distante", "@sage/xtrem-interop/nodes__sys_operation_transformation__property__remotePackageName": "Nom de package distant", "@sage/xtrem-interop/nodes__sys_remote_metadata__node_name": "Métadonnées de système distant", "@sage/xtrem-interop/nodes__sys_remote_metadata__query__getDataTypeInfo": "Obtenir info type données", "@sage/xtrem-interop/nodes__sys_remote_metadata__query__getDataTypeInfo__failed": "Échec de l'obtention des informations de type de données.", "@sage/xtrem-interop/nodes__sys_remote_metadata__query__getDataTypeInfo__parameter__app": "Application", "@sage/xtrem-interop/nodes__sys_remote_metadata__query__getDataTypeInfo__parameter__name": "Nom", "@sage/xtrem-interop/nodes__sys_remote_metadata__query__getDataTypeInfo__parameter__type": "Type", "@sage/xtrem-interop/nodes__sys_remote_metadata__query__getNodeInfo": "Obtenir info node", "@sage/xtrem-interop/nodes__sys_remote_metadata__query__getNodeInfo__failed": "La récupération des informations de node a échoué.", "@sage/xtrem-interop/nodes__sys_remote_metadata__query__getNodeInfo__parameter__app": "App", "@sage/xtrem-interop/nodes__sys_remote_metadata__query__getNodeInfo__parameter__filter": "Filtre", "@sage/xtrem-interop/nodes__sys_remote_metadata__query__getNodeInfo__parameter__name": "Nom", "@sage/xtrem-interop/nodes__sys_synchronization_source__node_name": "Source de synchronisation système", "@sage/xtrem-interop/nodes__sys_synchronization_source__query__getSynchronizationFeed": "Obtenir flux de synchronisation", "@sage/xtrem-interop/nodes__sys_synchronization_source__query__getSynchronizationFeed__failed": "La récupération du flux de synchronisation a échoué.", "@sage/xtrem-interop/nodes__sys_synchronization_source__query__getSynchronizationFeed__parameter__filter": "Filtre", "@sage/xtrem-interop/nodes__sys_synchronization_source__query__getSynchronizationFeed__parameter__node": "Node", "@sage/xtrem-interop/nodes__sys_synchronization_source__query__getSynchronizationFeed__parameter__selector": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-interop/nodes__sys_synchronization_source__query__getSynchronizationFeed__parameter__startTick": "Coche de début", "@sage/xtrem-interop/nodes__sys_synchronization_state__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-interop/nodes__sys_synchronization_state__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-interop/nodes__sys_synchronization_state__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-interop/nodes__sys_synchronization_state__node_name": "Statut de synchronisation système", "@sage/xtrem-interop/nodes__sys_synchronization_state__property__lastNotificationState": "Dernier statut de notification", "@sage/xtrem-interop/nodes__sys_synchronization_state__property__node": "Node", "@sage/xtrem-interop/nodes__sys_synchronization_state__property__notificationStates": "Statuts de notification", "@sage/xtrem-interop/nodes__sys_synchronization_state__property__remoteApp": "App distante", "@sage/xtrem-interop/nodes__sys_synchronization_state__property__succcessStamp": "<PERSON><PERSON><PERSON> de succès", "@sage/xtrem-interop/nodes__sys_synchronization_state__property__syncTick": "Coche de synchronisation", "@sage/xtrem-interop/nodes__sys_synchronization_target__asyncMutation__synchronize": "Synchroniser données applicatives distantes", "@sage/xtrem-interop/nodes__sys_synchronization_target__asyncMutation__synchronize__failed": "La synchronisation a échoué.", "@sage/xtrem-interop/nodes__sys_synchronization_target__asyncMutation__synchronize__parameter__filtersByNode": "Filtres par node", "@sage/xtrem-interop/nodes__sys_synchronization_target__asyncMutation__synchronize__parameter__forceSyncState": "Forcer le statut de synchronisation", "@sage/xtrem-interop/nodes__sys_synchronization_target__asyncMutation__synchronize__parameter__fullSync": "Sync totale", "@sage/xtrem-interop/nodes__sys_synchronization_target__asyncMutation__synchronize__parameter__localNodeNames": "Nodes locaux", "@sage/xtrem-interop/nodes__sys_synchronization_target__asyncMutation__synchronize__parameter__remoteApp": "Application distante", "@sage/xtrem-interop/nodes__sys_synchronization_target__asyncMutation__synchronize__parameter__skipDependencies": "Ignorer les dépendances", "@sage/xtrem-interop/nodes__sys_synchronization_target__node_name": "Cible de synchronisation système", "@sage/xtrem-interop/package__name": "Interop", "@sage/xtrem-interop/pages__sys_app____navigationPanel__listItem__isAlive__title": "Active", "@sage/xtrem-interop/pages__sys_app____navigationPanel__listItem__isConnector__title": "Connecteur", "@sage/xtrem-interop/pages__sys_app____navigationPanel__listItem__line2__title": "Version", "@sage/xtrem-interop/pages__sys_app____navigationPanel__listItem__line2Right__title": "Active", "@sage/xtrem-interop/pages__sys_app____navigationPanel__listItem__title__title": "Nom", "@sage/xtrem-interop/pages__sys_app____navigationPanel__listItem__titleRight__title": "Intitulé", "@sage/xtrem-interop/pages__sys_app____navigationPanel__optionsMenu__title": "Toutes", "@sage/xtrem-interop/pages__sys_app____navigationPanel__optionsMenu__title__2": "Active", "@sage/xtrem-interop/pages__sys_app____navigationPanel__optionsMenu__title__3": "Inactive", "@sage/xtrem-interop/pages__sys_app____objectTypePlural": "Applications connectées", "@sage/xtrem-interop/pages__sys_app____objectTypeSingular": "Application connectée", "@sage/xtrem-interop/pages__sys_app____title": "Applications connectées", "@sage/xtrem-interop/pages__sys_app__interopPackage____title": "Package interop", "@sage/xtrem-interop/pages__sys_app__isActive____title": "Active", "@sage/xtrem-interop/pages__sys_app__isAlive____title": "Active", "@sage/xtrem-interop/pages__sys_app__isConnector____title": "Connecteur", "@sage/xtrem-interop/pages__sys_app__name____title": "Nom", "@sage/xtrem-interop/pages__sys_app__refreshActiveState____title": "Rafraîchir le statut actif", "@sage/xtrem-interop/pages__sys_app__sync_all_message": "Sync démarrée", "@sage/xtrem-interop/pages__sys_app__syncAllNodes____title": "Tout sync", "@sage/xtrem-interop/pages__sys_app__technicalBlock____title": "Technique", "@sage/xtrem-interop/pages__sys_app__title____title": "Intitulé", "@sage/xtrem-interop/pages__sys_app__version____title": "Version", "@sage/xtrem-interop/pages__sys_enum_transformation____navigationPanel__inlineActions__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-interop/pages__sys_enum_transformation____navigationPanel__listItem__line2__title": "Active", "@sage/xtrem-interop/pages__sys_enum_transformation____navigationPanel__listItem__remoteApp__title": "App distante", "@sage/xtrem-interop/pages__sys_enum_transformation____navigationPanel__listItem__remoteAppVersion__title": "Version app distante", "@sage/xtrem-interop/pages__sys_enum_transformation____navigationPanel__listItem__remoteEnum__title": "Enum distant", "@sage/xtrem-interop/pages__sys_enum_transformation____navigationPanel__listItem__title__title": "Enum local", "@sage/xtrem-interop/pages__sys_enum_transformation____navigationPanel__listItem__titleRight__title": "Code mapping", "@sage/xtrem-interop/pages__sys_enum_transformation____navigationPanel__optionsMenu__title": "Active", "@sage/xtrem-interop/pages__sys_enum_transformation____navigationPanel__optionsMenu__title__2": "Inactive", "@sage/xtrem-interop/pages__sys_enum_transformation____navigationPanel__optionsMenu__title__3": "Toutes", "@sage/xtrem-interop/pages__sys_enum_transformation____objectTypePlural": "Transformations enums", "@sage/xtrem-interop/pages__sys_enum_transformation____objectTypeSingular": "Transformation enums", "@sage/xtrem-interop/pages__sys_enum_transformation____title": "Transformations enums", "@sage/xtrem-interop/pages__sys_enum_transformation__id____title": "Code mapping", "@sage/xtrem-interop/pages__sys_enum_transformation__isActive____title": "Active", "@sage/xtrem-interop/pages__sys_enum_transformation__localEnum____columns__title__name": "Nom", "@sage/xtrem-interop/pages__sys_enum_transformation__localEnum____columns__title__title": "Intitulé", "@sage/xtrem-interop/pages__sys_enum_transformation__localEnum____title": "Enum local", "@sage/xtrem-interop/pages__sys_enum_transformation__mappingBlock____title": "Mapping enum", "@sage/xtrem-interop/pages__sys_enum_transformation__mappingTable____columns__title__localEnumValue": "Valeur locale", "@sage/xtrem-interop/pages__sys_enum_transformation__mappingTable____columns__title__remoteEnumValue": "<PERSON>ur distante", "@sage/xtrem-interop/pages__sys_enum_transformation__mappingTable____dropdownActions__title": "Rafraîchir l'enregistrement", "@sage/xtrem-interop/pages__sys_enum_transformation__mappingTable____dropdownActions__title__2": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-interop/pages__sys_enum_transformation__remote_data_type_not_found": "Aucun type de donnes distant trouvé.", "@sage/xtrem-interop/pages__sys_enum_transformation__remoteApp____title": "App distante", "@sage/xtrem-interop/pages__sys_enum_transformation__remoteAppVersion____title": "Version app distante", "@sage/xtrem-interop/pages__sys_enum_transformation__remoteEnum____title": "Enum distant", "@sage/xtrem-interop/pages__sys_enum_transformation__remoteEnumDialogSection____title": "Sélectionner l'enum distant", "@sage/xtrem-interop/pages__sys_enum_transformation__remoteEnumTable____columns__title__name": "Nom", "@sage/xtrem-interop/pages__sys_enum_transformation__remoteEnumTable____columns__title__title": "Intitulé", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__dropdownActions__title": "Sync maintenant", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__dropdownActions__title__2": "Planifier sync", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__dropdownActions__title__3": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__dropdownActions__title__4": "Afficher la trace", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__inlineActions__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__listItem__lastError__title": "<PERSON><PERSON><PERSON> erreur", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__listItem__lastSync__title": "<PERSON><PERSON><PERSON> sync", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__listItem__localNode__title": "Entité locale", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__listItem__name__title": "App distante", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__listItem__remoteAppVersion__title": "Version app distante", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__listItem__remoteNodeName__title": "Entité distante", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__listItem__title__title": "Code mapping", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__listItem__titleRight__title": "Active", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__optionsMenu__title": "Active", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__optionsMenu__title__2": "Inactive", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__optionsMenu__title__3": "Toutes", "@sage/xtrem-interop/pages__sys_node_transformation____objectTypePlural": "Transformations de données", "@sage/xtrem-interop/pages__sys_node_transformation____objectTypeSingular": "Transformation de données", "@sage/xtrem-interop/pages__sys_node_transformation____title": "Transformations de synchronisation", "@sage/xtrem-interop/pages__sys_node_transformation__dialogBlock____title": "Entité distante", "@sage/xtrem-interop/pages__sys_node_transformation__dialogSection____title": "Sélectionner l'entité distante", "@sage/xtrem-interop/pages__sys_node_transformation__id____title": "Code mapping", "@sage/xtrem-interop/pages__sys_node_transformation__isActive____title": "Active", "@sage/xtrem-interop/pages__sys_node_transformation__lastErrorDate____title": "<PERSON><PERSON><PERSON> dernière sync", "@sage/xtrem-interop/pages__sys_node_transformation__lastSyncDate____title": "<PERSON><PERSON><PERSON> sync", "@sage/xtrem-interop/pages__sys_node_transformation__listOfRemoteNodes____columns__title__name": "Nom", "@sage/xtrem-interop/pages__sys_node_transformation__listOfRemoteNodes____columns__title__title": "Intitulé", "@sage/xtrem-interop/pages__sys_node_transformation__localNode____columns__title__name": "Type enregistrement", "@sage/xtrem-interop/pages__sys_node_transformation__localNode____columns__title__package__name": "Package", "@sage/xtrem-interop/pages__sys_node_transformation__localNode____columns__title__storage": "Stockage", "@sage/xtrem-interop/pages__sys_node_transformation__localNode____columns__title__title": "Type enregistrement", "@sage/xtrem-interop/pages__sys_node_transformation__localNode____title": "Entité locale", "@sage/xtrem-interop/pages__sys_node_transformation__localProperties____helperText": "Sélectionnez les propriétés que vous souhaitez synchroniser.", "@sage/xtrem-interop/pages__sys_node_transformation__localPropertiesBlock____title": "Propriétés locales", "@sage/xtrem-interop/pages__sys_node_transformation__localPropertiesSection____title": "Propriétés locales", "@sage/xtrem-interop/pages__sys_node_transformation__mappingBlock____title": "Table de mapping", "@sage/xtrem-interop/pages__sys_node_transformation__mappingSection____title": "Mapping", "@sage/xtrem-interop/pages__sys_node_transformation__mappingTable____columns__title__kind": "Type de mapping", "@sage/xtrem-interop/pages__sys_node_transformation__mappingTable____columns__title__localProperty": "Valeur locale", "@sage/xtrem-interop/pages__sys_node_transformation__mappingTable____columns__title__remoteProperty": "<PERSON>ur distante", "@sage/xtrem-interop/pages__sys_node_transformation__mappingTable____dropdownActions__title": "Modifier proprié<PERSON> distante", "@sage/xtrem-interop/pages__sys_node_transformation__mappingTable____dropdownActions__title__2": "Rafraîchir l'enregistrement", "@sage/xtrem-interop/pages__sys_node_transformation__mappingTable____dropdownActions__title__3": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-interop/pages__sys_node_transformation__remote_not_available": "Les données distantes n'ont pas été récupérées. Le système distant peut être indisponible.\n\nMessage d'origine :\n\n{{originalMessage}}", "@sage/xtrem-interop/pages__sys_node_transformation__remoteApp____title": "App distante", "@sage/xtrem-interop/pages__sys_node_transformation__remoteNodeName____title": "Entité distante", "@sage/xtrem-interop/pages__sys_node_transformation__remotePropertiesBlock____title": "Propriétés distantes", "@sage/xtrem-interop/pages__sys_node_transformation__remotePropertiesSection____title": "Propriétés distantes", "@sage/xtrem-interop/pages__sys_node_transformation__remoteVersion____title": "Version distante", "@sage/xtrem-interop/pages__sys_node_transformation__scheduleSync____title": "Planifier synchronisation", "@sage/xtrem-interop/pages__sys_node_transformation__selectFromLocalProperties____title": "Ajouter des lignes à partir des propriétés locales", "@sage/xtrem-interop/pages__sys_node_transformation__synchronizeResult____title": "Résultats de synchronisation", "@sage/xtrem-interop/pages__sys_node_transformation__syncNow____title": "Sync maintenant", "@sage/xtrem-interop/pages__sys_node_transformation__syncSection____title": "Sync", "@sage/xtrem-interop/pages__sys_node_transformation__too_many_remote_properties": "Trop de propriétés distantes sélectionnées à affecter. Vérifiez les résultats pour confirmer.", "@sage/xtrem-interop/permission__create__name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-interop/permission__default_instance__name": "Instance par défaut", "@sage/xtrem-interop/permission__delete__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-interop/permission__manage__name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-interop/permission__read__name": "Lecture", "@sage/xtrem-interop/permission__update__name": "Mettre à jour", "@sage/xtrem-interop/service_options__synchronization_service_option__name": "Option de service de synchronisation"}