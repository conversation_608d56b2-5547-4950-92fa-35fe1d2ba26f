{"@sage/xtrem-interop/activity__sys_app__name": "系统应用", "@sage/xtrem-interop/activity__sys_enum_synchronization__name": "系统枚举同步", "@sage/xtrem-interop/activity__sys_synchronization__name": "系统同步", "@sage/xtrem-interop/data_types__id__name": "ID", "@sage/xtrem-interop/data_types__mapping_value__name": "匹配值", "@sage/xtrem-interop/data_types__node_mapping_kind_enum__name": "节点匹配种类枚举", "@sage/xtrem-interop/data_types__price__name": "价格", "@sage/xtrem-interop/data_types__quantity__name": "数量", "@sage/xtrem-interop/data_types__test_local_status_enum__name": "测试本地状态枚举", "@sage/xtrem-interop/data_types__test_remote_status_enum__name": "测试远程状态枚举", "@sage/xtrem-interop/enums__node_mapping_kind__constant_to_path": "常量至路径", "@sage/xtrem-interop/enums__node_mapping_kind__function_to_path": "功能至路径", "@sage/xtrem-interop/enums__node_mapping_kind__path_to_constant": "路径至常量", "@sage/xtrem-interop/enums__node_mapping_kind__path_to_function": "路径至功能", "@sage/xtrem-interop/enums__node_mapping_kind__path_to_path": "路径至路径", "@sage/xtrem-interop/enums__test_local_status__inProgress": "处理中", "@sage/xtrem-interop/enums__test_local_status__start": "开始", "@sage/xtrem-interop/enums__test_local_status__stop": "停止", "@sage/xtrem-interop/enums__test_local_status__track": "追踪", "@sage/xtrem-interop/enums__test_remote_status__begin": "开始", "@sage/xtrem-interop/enums__test_remote_status__end": "结束", "@sage/xtrem-interop/enums__test_remote_status__inProgress": "处理中", "@sage/xtrem-interop/menu_item__interop": "互操作", "@sage/xtrem-interop/nodes__synchronization_state__property__secondaryLink": "次要链接", "@sage/xtrem-interop/nodes__synchronization_state__property__secondaryText": "次要文本", "@sage/xtrem-interop/nodes__sys_app__asyncMutation__asyncExport": "导出", "@sage/xtrem-interop/nodes__sys_app__asyncMutation__asyncExport__parameter__filter": "筛选", "@sage/xtrem-interop/nodes__sys_app__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-interop/nodes__sys_app__mutation__refreshState": "刷新状态", "@sage/xtrem-interop/nodes__sys_app__mutation__refreshState__failed": "刷新状态失败。", "@sage/xtrem-interop/nodes__sys_app__node_name": "系统应用", "@sage/xtrem-interop/nodes__sys_app__property__interopPackage": "互操作程序包", "@sage/xtrem-interop/nodes__sys_app__property__isActive": "是激活的", "@sage/xtrem-interop/nodes__sys_app__property__isAlive": "是活跃的", "@sage/xtrem-interop/nodes__sys_app__property__isConnector": "是连接器", "@sage/xtrem-interop/nodes__sys_app__property__name": "名称", "@sage/xtrem-interop/nodes__sys_app__property__title": "标题", "@sage/xtrem-interop/nodes__sys_app__property__version": "版本", "@sage/xtrem-interop/nodes__sys_app__query__getAppInfo": "获取应用程序信息", "@sage/xtrem-interop/nodes__sys_app__query__getAppInfo__failed": "获取应用信息失败。", "@sage/xtrem-interop/nodes__sys_enum_mapping__asyncMutation__asyncExport": "导出", "@sage/xtrem-interop/nodes__sys_enum_mapping__asyncMutation__asyncExport__parameter__filter": "筛选", "@sage/xtrem-interop/nodes__sys_enum_mapping__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-interop/nodes__sys_enum_mapping__node_name": "系统枚举匹配", "@sage/xtrem-interop/nodes__sys_enum_mapping__property__localEnumValue": "本地枚举值", "@sage/xtrem-interop/nodes__sys_enum_mapping__property__remoteEnumValue": "远程枚举值", "@sage/xtrem-interop/nodes__sys_enum_mapping__property__transform": "转换", "@sage/xtrem-interop/nodes__sys_enum_transformation__asyncMutation__asyncExport": "导出", "@sage/xtrem-interop/nodes__sys_enum_transformation__asyncMutation__asyncExport__parameter__filter": "筛选", "@sage/xtrem-interop/nodes__sys_enum_transformation__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-interop/nodes__sys_enum_transformation__mutation__updateActiveMappings": "更新激活的匹配", "@sage/xtrem-interop/nodes__sys_enum_transformation__mutation__updateActiveMappings__failed": "更新激活匹配失败。", "@sage/xtrem-interop/nodes__sys_enum_transformation__mutation__updateActiveMappings__parameter__parameters": "参数", "@sage/xtrem-interop/nodes__sys_enum_transformation__node_name": "系统枚举转换", "@sage/xtrem-interop/nodes__sys_enum_transformation__property__id": "ID", "@sage/xtrem-interop/nodes__sys_enum_transformation__property__isActive": "是激活的", "@sage/xtrem-interop/nodes__sys_enum_transformation__property__localEnum": "本地枚举", "@sage/xtrem-interop/nodes__sys_enum_transformation__property__map": "地图", "@sage/xtrem-interop/nodes__sys_enum_transformation__property__remoteApp": "远程应用", "@sage/xtrem-interop/nodes__sys_enum_transformation__property__remoteAppVersion": "远程应用版本", "@sage/xtrem-interop/nodes__sys_enum_transformation__property__remoteEnum": "远程枚举", "@sage/xtrem-interop/nodes__sys_node_mapping__asyncMutation__asyncExport": "导出", "@sage/xtrem-interop/nodes__sys_node_mapping__asyncMutation__asyncExport__parameter__filter": "筛选", "@sage/xtrem-interop/nodes__sys_node_mapping__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-interop/nodes__sys_node_mapping__node_name": "系统节点匹配", "@sage/xtrem-interop/nodes__sys_node_mapping__property__kind": "种类", "@sage/xtrem-interop/nodes__sys_node_mapping__property__localProperty": "本地属性", "@sage/xtrem-interop/nodes__sys_node_mapping__property__remoteProperty": "远程属性", "@sage/xtrem-interop/nodes__sys_node_mapping__property__transform": "转换", "@sage/xtrem-interop/nodes__sys_node_transformation__asyncMutation__asyncExport": "导出", "@sage/xtrem-interop/nodes__sys_node_transformation__asyncMutation__asyncExport__parameter__filter": "筛选", "@sage/xtrem-interop/nodes__sys_node_transformation__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-interop/nodes__sys_node_transformation__mutation__updateActiveMappings": "更新激活的匹配", "@sage/xtrem-interop/nodes__sys_node_transformation__mutation__updateActiveMappings__failed": "更新激活匹配失败。", "@sage/xtrem-interop/nodes__sys_node_transformation__mutation__updateActiveMappings__parameter__parameters": "参数", "@sage/xtrem-interop/nodes__sys_node_transformation__node_name": "系统节点转换", "@sage/xtrem-interop/nodes__sys_node_transformation__property__filter": "筛选", "@sage/xtrem-interop/nodes__sys_node_transformation__property__id": "ID", "@sage/xtrem-interop/nodes__sys_node_transformation__property__isActive": "是激活的", "@sage/xtrem-interop/nodes__sys_node_transformation__property__lastError": "上次出错", "@sage/xtrem-interop/nodes__sys_node_transformation__property__lastSync": "上次同步", "@sage/xtrem-interop/nodes__sys_node_transformation__property__localNode": "本地节点", "@sage/xtrem-interop/nodes__sys_node_transformation__property__map": "地图", "@sage/xtrem-interop/nodes__sys_node_transformation__property__mappings": "匹配", "@sage/xtrem-interop/nodes__sys_node_transformation__property__remoteApp": "远程应用程序", "@sage/xtrem-interop/nodes__sys_node_transformation__property__remoteAppVersion": "远程应用程序版本", "@sage/xtrem-interop/nodes__sys_node_transformation__property__remoteMappings": "远程匹配", "@sage/xtrem-interop/nodes__sys_node_transformation__property__remoteNodeName": "远程节点名称", "@sage/xtrem-interop/nodes__sys_node_transformation__query__hasActiveMapping": "具有激活的匹配", "@sage/xtrem-interop/nodes__sys_node_transformation__query__hasActiveMapping__failed": "激活匹配失败。", "@sage/xtrem-interop/nodes__sys_node_transformation__query__hasActiveMapping__parameter__localNode": "本地节点", "@sage/xtrem-interop/nodes__sys_node_transformation__query__hasActiveMapping__parameter__propertyId": "属性ID", "@sage/xtrem-interop/nodes__sys_operation_transformation__asyncMutation__asyncExport": "导出", "@sage/xtrem-interop/nodes__sys_operation_transformation__asyncMutation__asyncExport__parameter__filter": "筛选", "@sage/xtrem-interop/nodes__sys_operation_transformation__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-interop/nodes__sys_operation_transformation__node_name": "系统操作转换", "@sage/xtrem-interop/nodes__sys_operation_transformation__property__id": "ID", "@sage/xtrem-interop/nodes__sys_operation_transformation__property__isActive": "是激活的", "@sage/xtrem-interop/nodes__sys_operation_transformation__property__localNode": "本地节点", "@sage/xtrem-interop/nodes__sys_operation_transformation__property__localOperationName": "本地操作名称", "@sage/xtrem-interop/nodes__sys_operation_transformation__property__mappings": "匹配", "@sage/xtrem-interop/nodes__sys_operation_transformation__property__remoteApp": "远程应用程序", "@sage/xtrem-interop/nodes__sys_operation_transformation__property__remoteAppVersion": "远程应用程序版本", "@sage/xtrem-interop/nodes__sys_operation_transformation__property__remoteNodeName": "远程节点名称", "@sage/xtrem-interop/nodes__sys_operation_transformation__property__remoteOperationName": "远程操作名称", "@sage/xtrem-interop/nodes__sys_operation_transformation__property__remotePackageName": "远程程序包名称", "@sage/xtrem-interop/nodes__sys_remote_metadata__node_name": "系统远程元数据", "@sage/xtrem-interop/nodes__sys_remote_metadata__query__getDataTypeInfo": "获取数据类型信息", "@sage/xtrem-interop/nodes__sys_remote_metadata__query__getDataTypeInfo__failed": "获取数据类型信息失败。", "@sage/xtrem-interop/nodes__sys_remote_metadata__query__getDataTypeInfo__parameter__app": "应用程序", "@sage/xtrem-interop/nodes__sys_remote_metadata__query__getDataTypeInfo__parameter__name": "名称", "@sage/xtrem-interop/nodes__sys_remote_metadata__query__getDataTypeInfo__parameter__type": "类型", "@sage/xtrem-interop/nodes__sys_remote_metadata__query__getNodeInfo": "获取节点信息", "@sage/xtrem-interop/nodes__sys_remote_metadata__query__getNodeInfo__failed": "获取节点信息失败。", "@sage/xtrem-interop/nodes__sys_remote_metadata__query__getNodeInfo__parameter__app": "应用", "@sage/xtrem-interop/nodes__sys_remote_metadata__query__getNodeInfo__parameter__filter": "筛选", "@sage/xtrem-interop/nodes__sys_remote_metadata__query__getNodeInfo__parameter__name": "名称", "@sage/xtrem-interop/nodes__sys_synchronization_client__node_name": "系统同步客户", "@sage/xtrem-interop/nodes__sys_synchronization_source__node_name": "系统同步资源", "@sage/xtrem-interop/nodes__sys_synchronization_source__query__getAppVersion": "获取应用程序版本", "@sage/xtrem-interop/nodes__sys_synchronization_source__query__getSynchronizationFeed": "获取同步源", "@sage/xtrem-interop/nodes__sys_synchronization_source__query__getSynchronizationFeed__failed": "获取同步源失败。", "@sage/xtrem-interop/nodes__sys_synchronization_source__query__getSynchronizationFeed__parameter__filter": "筛选", "@sage/xtrem-interop/nodes__sys_synchronization_source__query__getSynchronizationFeed__parameter__node": "节点", "@sage/xtrem-interop/nodes__sys_synchronization_source__query__getSynchronizationFeed__parameter__selector": "选择器", "@sage/xtrem-interop/nodes__sys_synchronization_source__query__getSynchronizationFeed__parameter__startTick": "开始标记", "@sage/xtrem-interop/nodes__sys_synchronization_state__asyncMutation__asyncExport": "导出", "@sage/xtrem-interop/nodes__sys_synchronization_state__asyncMutation__asyncExport__parameter__filter": "筛选", "@sage/xtrem-interop/nodes__sys_synchronization_state__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-interop/nodes__sys_synchronization_state__node_name": "系统同步状态", "@sage/xtrem-interop/nodes__sys_synchronization_state__property__lastNotificationState": "最后的通知状态", "@sage/xtrem-interop/nodes__sys_synchronization_state__property__node": "节点", "@sage/xtrem-interop/nodes__sys_synchronization_state__property__notificationStates": "通知状态", "@sage/xtrem-interop/nodes__sys_synchronization_state__property__remoteApp": "远程应用", "@sage/xtrem-interop/nodes__sys_synchronization_state__property__succcessStamp": "成功标记", "@sage/xtrem-interop/nodes__sys_synchronization_state__property__syncTick": "同步标记", "@sage/xtrem-interop/nodes__sys_synchronization_target__asyncMutation__synchronize": "同步远程应用数据", "@sage/xtrem-interop/nodes__sys_synchronization_target__asyncMutation__synchronize__failed": "同步失败。", "@sage/xtrem-interop/nodes__sys_synchronization_target__asyncMutation__synchronize__parameter__filtersByNode": "按节点筛选", "@sage/xtrem-interop/nodes__sys_synchronization_target__asyncMutation__synchronize__parameter__forceSyncState": "强制同步状态", "@sage/xtrem-interop/nodes__sys_synchronization_target__asyncMutation__synchronize__parameter__fullSync": "完全同步", "@sage/xtrem-interop/nodes__sys_synchronization_target__asyncMutation__synchronize__parameter__localNodeNames": "本地节点", "@sage/xtrem-interop/nodes__sys_synchronization_target__asyncMutation__synchronize__parameter__remoteApp": "远程应用程序", "@sage/xtrem-interop/nodes__sys_synchronization_target__asyncMutation__synchronize__parameter__skipDependencies": "跳过依赖项", "@sage/xtrem-interop/nodes__sys_synchronization_target__node_name": "系统同步目标", "@sage/xtrem-interop/package__name": "互操作", "@sage/xtrem-interop/pages__integration____navigationPanel__listItem__line11__title": "链接", "@sage/xtrem-interop/pages__integration____navigationPanel__listItem__line12__title": "链接", "@sage/xtrem-interop/pages__integration____navigationPanel__listItem__line2__title": "第三方应用程序", "@sage/xtrem-interop/pages__sys_app____navigationPanel__listItem__isAlive__title": "活跃的", "@sage/xtrem-interop/pages__sys_app____navigationPanel__listItem__isConnector__title": "连接器", "@sage/xtrem-interop/pages__sys_app____navigationPanel__listItem__line2__title": "版本", "@sage/xtrem-interop/pages__sys_app____navigationPanel__listItem__line2Right__title": "激活", "@sage/xtrem-interop/pages__sys_app____navigationPanel__listItem__title__title": "名称", "@sage/xtrem-interop/pages__sys_app____navigationPanel__listItem__titleRight__title": "标题", "@sage/xtrem-interop/pages__sys_app____navigationPanel__optionsMenu__title": "全部", "@sage/xtrem-interop/pages__sys_app____navigationPanel__optionsMenu__title__2": "激活", "@sage/xtrem-interop/pages__sys_app____navigationPanel__optionsMenu__title__3": "未激活", "@sage/xtrem-interop/pages__sys_app____objectTypePlural": "连接的应用", "@sage/xtrem-interop/pages__sys_app____objectTypeSingular": "连接的应用", "@sage/xtrem-interop/pages__sys_app____title": "连接的应用", "@sage/xtrem-interop/pages__sys_app__interopPackage____title": "互操作程序包", "@sage/xtrem-interop/pages__sys_app__isActive____title": "激活", "@sage/xtrem-interop/pages__sys_app__isAlive____title": "活跃的", "@sage/xtrem-interop/pages__sys_app__isConnector____title": "连接器", "@sage/xtrem-interop/pages__sys_app__name____title": "名称", "@sage/xtrem-interop/pages__sys_app__refreshActiveState____title": "刷新激活状态", "@sage/xtrem-interop/pages__sys_app__sync_all_message": "同步开始", "@sage/xtrem-interop/pages__sys_app__syncAllNodes____title": "同步全部", "@sage/xtrem-interop/pages__sys_app__technicalBlock____title": "技术", "@sage/xtrem-interop/pages__sys_app__title____title": "标题", "@sage/xtrem-interop/pages__sys_app__version____title": "版本", "@sage/xtrem-interop/pages__sys_enum_transformation____navigationPanel__inlineActions__title": "复制", "@sage/xtrem-interop/pages__sys_enum_transformation____navigationPanel__listItem__line2__title": "激活", "@sage/xtrem-interop/pages__sys_enum_transformation____navigationPanel__listItem__remoteApp__title": "远程应用", "@sage/xtrem-interop/pages__sys_enum_transformation____navigationPanel__listItem__remoteAppVersion__title": "远程应用版本", "@sage/xtrem-interop/pages__sys_enum_transformation____navigationPanel__listItem__remoteEnum__title": "远程枚举", "@sage/xtrem-interop/pages__sys_enum_transformation____navigationPanel__listItem__title__title": "本地枚举", "@sage/xtrem-interop/pages__sys_enum_transformation____navigationPanel__listItem__titleRight__title": "匹配ID", "@sage/xtrem-interop/pages__sys_enum_transformation____navigationPanel__optionsMenu__title": "激活", "@sage/xtrem-interop/pages__sys_enum_transformation____navigationPanel__optionsMenu__title__2": "未激活", "@sage/xtrem-interop/pages__sys_enum_transformation____navigationPanel__optionsMenu__title__3": "全部", "@sage/xtrem-interop/pages__sys_enum_transformation____objectTypePlural": "枚举转换", "@sage/xtrem-interop/pages__sys_enum_transformation____objectTypeSingular": "枚举转换", "@sage/xtrem-interop/pages__sys_enum_transformation____title": "枚举转换", "@sage/xtrem-interop/pages__sys_enum_transformation__id____title": "匹配ID", "@sage/xtrem-interop/pages__sys_enum_transformation__isActive____title": "激活", "@sage/xtrem-interop/pages__sys_enum_transformation__localEnum____columns__title__name": "名称", "@sage/xtrem-interop/pages__sys_enum_transformation__localEnum____columns__title__title": "标题", "@sage/xtrem-interop/pages__sys_enum_transformation__localEnum____title": "本地枚举", "@sage/xtrem-interop/pages__sys_enum_transformation__mappingBlock____title": "枚举匹配", "@sage/xtrem-interop/pages__sys_enum_transformation__mappingTable____columns__title__localEnumValue": "本地值", "@sage/xtrem-interop/pages__sys_enum_transformation__mappingTable____columns__title__remoteEnumValue": "远程值", "@sage/xtrem-interop/pages__sys_enum_transformation__mappingTable____dropdownActions__title": "刷新记录", "@sage/xtrem-interop/pages__sys_enum_transformation__mappingTable____dropdownActions__title__2": "移除", "@sage/xtrem-interop/pages__sys_enum_transformation__remote_data_type_not_found": "找不到远程数据类型。", "@sage/xtrem-interop/pages__sys_enum_transformation__remoteApp____title": "远程应用", "@sage/xtrem-interop/pages__sys_enum_transformation__remoteAppVersion____title": "远程应用版本", "@sage/xtrem-interop/pages__sys_enum_transformation__remoteEnum____title": "远程枚举", "@sage/xtrem-interop/pages__sys_enum_transformation__remoteEnumDialogSection____title": "选择远程枚举", "@sage/xtrem-interop/pages__sys_enum_transformation__remoteEnumTable____columns__title__name": "名称", "@sage/xtrem-interop/pages__sys_enum_transformation__remoteEnumTable____columns__title__title": "标题", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__dropdownActions__title": "立即同步", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__dropdownActions__title__2": "排程同步", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__dropdownActions__title__3": "复制", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__dropdownActions__title__4": "显示日志", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__inlineActions__title": "复制", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__listItem__lastError__title": "上次出错", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__listItem__lastSync__title": "上次同步", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__listItem__localNode__title": "本地实体", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__listItem__name__title": "远程应用", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__listItem__remoteAppVersion__title": "远程应用版本", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__listItem__remoteNodeName__title": "远程实体", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__listItem__title__title": "匹配ID", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__listItem__titleRight__title": "激活", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__optionsMenu__title": "激活", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__optionsMenu__title__2": "未激活", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__optionsMenu__title__3": "全部", "@sage/xtrem-interop/pages__sys_node_transformation____objectTypePlural": "数据转换", "@sage/xtrem-interop/pages__sys_node_transformation____objectTypeSingular": "数据转换", "@sage/xtrem-interop/pages__sys_node_transformation____title": "同步转换", "@sage/xtrem-interop/pages__sys_node_transformation__dialogBlock____title": "远程实体", "@sage/xtrem-interop/pages__sys_node_transformation__dialogSection____title": "选择远程实体", "@sage/xtrem-interop/pages__sys_node_transformation__id____title": "匹配ID", "@sage/xtrem-interop/pages__sys_node_transformation__isActive____title": "激活", "@sage/xtrem-interop/pages__sys_node_transformation__lastErrorDate____title": "上次同步出错", "@sage/xtrem-interop/pages__sys_node_transformation__lastSyncDate____title": "上次同步", "@sage/xtrem-interop/pages__sys_node_transformation__listOfRemoteNodes____columns__title__name": "名称", "@sage/xtrem-interop/pages__sys_node_transformation__listOfRemoteNodes____columns__title__title": "标题", "@sage/xtrem-interop/pages__sys_node_transformation__localNode____columns__title__name": "记录类型", "@sage/xtrem-interop/pages__sys_node_transformation__localNode____columns__title__package__name": "程序包", "@sage/xtrem-interop/pages__sys_node_transformation__localNode____columns__title__storage": "存储", "@sage/xtrem-interop/pages__sys_node_transformation__localNode____columns__title__title": "记录类型", "@sage/xtrem-interop/pages__sys_node_transformation__localNode____title": "本地实体", "@sage/xtrem-interop/pages__sys_node_transformation__localProperties____helperText": "选择您想同步的属性", "@sage/xtrem-interop/pages__sys_node_transformation__localPropertiesBlock____title": "本地属性", "@sage/xtrem-interop/pages__sys_node_transformation__localPropertiesSection____title": "本地属性", "@sage/xtrem-interop/pages__sys_node_transformation__mappingBlock____title": "匹配表", "@sage/xtrem-interop/pages__sys_node_transformation__mappingSection____title": "匹配", "@sage/xtrem-interop/pages__sys_node_transformation__mappingTable____columns__title__kind": "匹配类型", "@sage/xtrem-interop/pages__sys_node_transformation__mappingTable____columns__title__localProperty": "本地值", "@sage/xtrem-interop/pages__sys_node_transformation__mappingTable____columns__title__remoteProperty": "远程值", "@sage/xtrem-interop/pages__sys_node_transformation__mappingTable____dropdownActions__title": "编辑远程属性", "@sage/xtrem-interop/pages__sys_node_transformation__mappingTable____dropdownActions__title__2": "刷新记录", "@sage/xtrem-interop/pages__sys_node_transformation__mappingTable____dropdownActions__title__3": "移除", "@sage/xtrem-interop/pages__sys_node_transformation__remote_not_available": "未检索到远程数据。远程系统可能不可用。\n\n原始邮件：\n\n{{originalMessage}}", "@sage/xtrem-interop/pages__sys_node_transformation__remoteApp____title": "远程应用", "@sage/xtrem-interop/pages__sys_node_transformation__remoteNodeName____title": "远程实体", "@sage/xtrem-interop/pages__sys_node_transformation__remoteNodeNameName____title": "远程实体", "@sage/xtrem-interop/pages__sys_node_transformation__remotePropertiesBlock____title": "远程属性", "@sage/xtrem-interop/pages__sys_node_transformation__remotePropertiesSection____title": "远程属性", "@sage/xtrem-interop/pages__sys_node_transformation__remoteVersion____title": "远程版本", "@sage/xtrem-interop/pages__sys_node_transformation__scheduleSync____title": "排程同步", "@sage/xtrem-interop/pages__sys_node_transformation__selectFromLocalProperties____title": "从本地属性中添加行", "@sage/xtrem-interop/pages__sys_node_transformation__synchronizeResult____title": "同步结果", "@sage/xtrem-interop/pages__sys_node_transformation__syncNow____title": "立即同步", "@sage/xtrem-interop/pages__sys_node_transformation__syncSection____title": "同步", "@sage/xtrem-interop/pages__sys_node_transformation__too_many_remote_properties": "选择的远程属性过多，无法分配。检查结果以确认。", "@sage/xtrem-interop/permission__create__name": "创建", "@sage/xtrem-interop/permission__default_instance__name": "默认实例", "@sage/xtrem-interop/permission__delete__name": "删除", "@sage/xtrem-interop/permission__manage__name": "管理", "@sage/xtrem-interop/permission__read__name": "读取", "@sage/xtrem-interop/permission__update__name": "更新", "@sage/xtrem-interop/service_options__synchronization_service_option__name": "同步服务选项"}