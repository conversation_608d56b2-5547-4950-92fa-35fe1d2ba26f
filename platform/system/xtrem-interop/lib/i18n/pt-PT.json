{"@sage/xtrem-interop/activity__sys_app__name": "App de sistema", "@sage/xtrem-interop/activity__sys_enum_synchronization__name": "Sincronização do enum Sys", "@sage/xtrem-interop/activity__sys_synchronization__name": "Sincronização do sistema", "@sage/xtrem-interop/data_types__id__name": "ID", "@sage/xtrem-interop/data_types__mapping_value__name": "Valor do mapeamento", "@sage/xtrem-interop/data_types__node_mapping_kind_enum__name": "Tipo de mapeamento de nó enum", "@sage/xtrem-interop/data_types__price__name": "Preço", "@sage/xtrem-interop/data_types__quantity__name": "Quantidade", "@sage/xtrem-interop/data_types__test_local_status_enum__name": "Testar enum de status local", "@sage/xtrem-interop/data_types__test_remote_status_enum__name": "Testar enum de status remoto", "@sage/xtrem-interop/enums__node_mapping_kind__constant_to_path": "Constante para caminho", "@sage/xtrem-interop/enums__node_mapping_kind__function_to_path": "Função para caminho", "@sage/xtrem-interop/enums__node_mapping_kind__path_to_constant": "<PERSON><PERSON><PERSON> para a constante", "@sage/xtrem-interop/enums__node_mapping_kind__path_to_function": "Caminho para a função", "@sage/xtrem-interop/enums__node_mapping_kind__path_to_path": "Caminho para caminho", "@sage/xtrem-interop/enums__test_local_status__inProgress": "Em-curso", "@sage/xtrem-interop/enums__test_local_status__start": "Iniciar", "@sage/xtrem-interop/enums__test_local_status__stop": "<PERSON><PERSON>", "@sage/xtrem-interop/enums__test_local_status__track": "<PERSON><PERSON><PERSON>", "@sage/xtrem-interop/enums__test_remote_status__begin": "Início", "@sage/xtrem-interop/enums__test_remote_status__end": "Fim", "@sage/xtrem-interop/enums__test_remote_status__inProgress": "Em-curso", "@sage/xtrem-interop/menu_item__interop": "Interoperabilidade", "@sage/xtrem-interop/nodes__synchronization_state__property__secondaryLink": "<PERSON>", "@sage/xtrem-interop/nodes__synchronization_state__property__secondaryText": "Texto secundário", "@sage/xtrem-interop/nodes__sys_app__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-interop/nodes__sys_app__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-interop/nodes__sys_app__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-interop/nodes__sys_app__mutation__refreshState": "Atualizar status", "@sage/xtrem-interop/nodes__sys_app__mutation__refreshState__failed": "A atualização do status falhou.", "@sage/xtrem-interop/nodes__sys_app__node_name": "App de sistema", "@sage/xtrem-interop/nodes__sys_app__property__interopPackage": "Pacote de interoperabilidade", "@sage/xtrem-interop/nodes__sys_app__property__isActive": "Está ativo(a)", "@sage/xtrem-interop/nodes__sys_app__property__isAlive": "Está vivo", "@sage/xtrem-interop/nodes__sys_app__property__isConnector": "<PERSON> um conector", "@sage/xtrem-interop/nodes__sys_app__property__name": "Nome", "@sage/xtrem-interop/nodes__sys_app__property__title": "Titulo", "@sage/xtrem-interop/nodes__sys_app__property__version": "Vers<PERSON>", "@sage/xtrem-interop/nodes__sys_app__query__getAppInfo": "Obter informações sobre a aplicação", "@sage/xtrem-interop/nodes__sys_app__query__getAppInfo__failed": "Falha ao obter informações da aplicação.", "@sage/xtrem-interop/nodes__sys_enum_mapping__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-interop/nodes__sys_enum_mapping__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-interop/nodes__sys_enum_mapping__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-interop/nodes__sys_enum_mapping__node_name": "Mapeamento do enum sys", "@sage/xtrem-interop/nodes__sys_enum_mapping__property__localEnumValue": "Valor enum local", "@sage/xtrem-interop/nodes__sys_enum_mapping__property__remoteEnumValue": "Valor enum remoto", "@sage/xtrem-interop/nodes__sys_enum_mapping__property__transform": "Transformar", "@sage/xtrem-interop/nodes__sys_enum_transformation__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-interop/nodes__sys_enum_transformation__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-interop/nodes__sys_enum_transformation__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-interop/nodes__sys_enum_transformation__mutation__updateActiveMappings": "Atualizar mapeamentos ativos", "@sage/xtrem-interop/nodes__sys_enum_transformation__mutation__updateActiveMappings__failed": "A atualização dos mapeamentos ativos falhou.", "@sage/xtrem-interop/nodes__sys_enum_transformation__mutation__updateActiveMappings__parameter__parameters": "Parâmetros", "@sage/xtrem-interop/nodes__sys_enum_transformation__node_name": "Transformação de enum do sistema", "@sage/xtrem-interop/nodes__sys_enum_transformation__property__id": "ID", "@sage/xtrem-interop/nodes__sys_enum_transformation__property__isActive": "Está ativo(a)", "@sage/xtrem-interop/nodes__sys_enum_transformation__property__localEnum": "Enum local", "@sage/xtrem-interop/nodes__sys_enum_transformation__property__map": "Mapa", "@sage/xtrem-interop/nodes__sys_enum_transformation__property__remoteApp": "Aplicativo remoto", "@sage/xtrem-interop/nodes__sys_enum_transformation__property__remoteAppVersion": "Versão do aplicativo remoto", "@sage/xtrem-interop/nodes__sys_enum_transformation__property__remoteEnum": "<PERSON><PERSON> remoto", "@sage/xtrem-interop/nodes__sys_node_mapping__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-interop/nodes__sys_node_mapping__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-interop/nodes__sys_node_mapping__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-interop/nodes__sys_node_mapping__node_name": "Mapeamento do node sys", "@sage/xtrem-interop/nodes__sys_node_mapping__property__kind": "Tipo", "@sage/xtrem-interop/nodes__sys_node_mapping__property__localProperty": "Propriedade local", "@sage/xtrem-interop/nodes__sys_node_mapping__property__remoteProperty": "<PERSON><PERSON><PERSON><PERSON> remota", "@sage/xtrem-interop/nodes__sys_node_mapping__property__transform": "Transformar", "@sage/xtrem-interop/nodes__sys_node_transformation__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-interop/nodes__sys_node_transformation__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-interop/nodes__sys_node_transformation__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-interop/nodes__sys_node_transformation__mutation__updateActiveMappings": "Atualizar mapeamentos ativos", "@sage/xtrem-interop/nodes__sys_node_transformation__mutation__updateActiveMappings__failed": "A atualização dos mapeamentos ativos falhou.", "@sage/xtrem-interop/nodes__sys_node_transformation__mutation__updateActiveMappings__parameter__parameters": "Parâmetros", "@sage/xtrem-interop/nodes__sys_node_transformation__node_name": "Transformação do nó do sistema", "@sage/xtrem-interop/nodes__sys_node_transformation__property__filter": "Filtro", "@sage/xtrem-interop/nodes__sys_node_transformation__property__id": "ID", "@sage/xtrem-interop/nodes__sys_node_transformation__property__isActive": "Está ativo(a)", "@sage/xtrem-interop/nodes__sys_node_transformation__property__lastError": "<PERSON><PERSON><PERSON> erro", "@sage/xtrem-interop/nodes__sys_node_transformation__property__lastSync": "Última sincronização", "@sage/xtrem-interop/nodes__sys_node_transformation__property__localNode": "Nó (node) local", "@sage/xtrem-interop/nodes__sys_node_transformation__property__map": "Mapa", "@sage/xtrem-interop/nodes__sys_node_transformation__property__mappings": "Mapeamentos", "@sage/xtrem-interop/nodes__sys_node_transformation__property__remoteApp": "Aplicação remota", "@sage/xtrem-interop/nodes__sys_node_transformation__property__remoteAppVersion": "Versão da aplicação remota", "@sage/xtrem-interop/nodes__sys_node_transformation__property__remoteMappings": "Mapeamentos remotos", "@sage/xtrem-interop/nodes__sys_node_transformation__property__remoteNodeName": "Nome do nó remoto", "@sage/xtrem-interop/nodes__sys_node_transformation__query__hasActiveMapping": "Tem mapeamento ativo", "@sage/xtrem-interop/nodes__sys_node_transformation__query__hasActiveMapping__failed": "Falha no mapeamento ativo.", "@sage/xtrem-interop/nodes__sys_node_transformation__query__hasActiveMapping__parameter__localNode": "Nó (node) local", "@sage/xtrem-interop/nodes__sys_node_transformation__query__hasActiveMapping__parameter__propertyId": "<PERSON> da propriedade", "@sage/xtrem-interop/nodes__sys_operation_transformation__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-interop/nodes__sys_operation_transformation__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-interop/nodes__sys_operation_transformation__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-interop/nodes__sys_operation_transformation__node_name": "Transformação da operação do sistema", "@sage/xtrem-interop/nodes__sys_operation_transformation__property__id": "ID", "@sage/xtrem-interop/nodes__sys_operation_transformation__property__isActive": "Está ativo(a)", "@sage/xtrem-interop/nodes__sys_operation_transformation__property__localNode": "Nó local", "@sage/xtrem-interop/nodes__sys_operation_transformation__property__localOperationName": "Nome da operação local", "@sage/xtrem-interop/nodes__sys_operation_transformation__property__mappings": "Mapeamentos", "@sage/xtrem-interop/nodes__sys_operation_transformation__property__remoteApp": "Aplicação remota", "@sage/xtrem-interop/nodes__sys_operation_transformation__property__remoteAppVersion": "Versão da aplicação remota", "@sage/xtrem-interop/nodes__sys_operation_transformation__property__remoteNodeName": "Nome do nó remoto", "@sage/xtrem-interop/nodes__sys_operation_transformation__property__remoteOperationName": "Nome da operação remota", "@sage/xtrem-interop/nodes__sys_operation_transformation__property__remotePackageName": "Nome do pacote remoto", "@sage/xtrem-interop/nodes__sys_remote_metadata__node_name": "Metadados remotos do sistema", "@sage/xtrem-interop/nodes__sys_remote_metadata__query__getDataTypeInfo": "Obter  informações sobre o tipo de dados", "@sage/xtrem-interop/nodes__sys_remote_metadata__query__getDataTypeInfo__failed": "Falha na obtenção de informações sobre o tipo de dados.", "@sage/xtrem-interop/nodes__sys_remote_metadata__query__getDataTypeInfo__parameter__app": "Aplicação", "@sage/xtrem-interop/nodes__sys_remote_metadata__query__getDataTypeInfo__parameter__name": "Nome", "@sage/xtrem-interop/nodes__sys_remote_metadata__query__getDataTypeInfo__parameter__type": "Tipo", "@sage/xtrem-interop/nodes__sys_remote_metadata__query__getNodeInfo": "Obter  informações sobre o nó", "@sage/xtrem-interop/nodes__sys_remote_metadata__query__getNodeInfo__failed": "Falha na obtenção de informações do nó.", "@sage/xtrem-interop/nodes__sys_remote_metadata__query__getNodeInfo__parameter__app": "Aplicação", "@sage/xtrem-interop/nodes__sys_remote_metadata__query__getNodeInfo__parameter__filter": "Filtro", "@sage/xtrem-interop/nodes__sys_remote_metadata__query__getNodeInfo__parameter__name": "Nome", "@sage/xtrem-interop/nodes__sys_synchronization_source__node_name": "Fonte de sincronização do sistema", "@sage/xtrem-interop/nodes__sys_synchronization_source__query__getAppVersion": "Obter a versão da aplicação", "@sage/xtrem-interop/nodes__sys_synchronization_source__query__getSynchronizationFeed": "Obter feed de sincronização", "@sage/xtrem-interop/nodes__sys_synchronization_source__query__getSynchronizationFeed__failed": "Falha na obtenção do feed de sincronização.", "@sage/xtrem-interop/nodes__sys_synchronization_source__query__getSynchronizationFeed__parameter__filter": "Filtro", "@sage/xtrem-interop/nodes__sys_synchronization_source__query__getSynchronizationFeed__parameter__node": "<PERSON><PERSON> (node)", "@sage/xtrem-interop/nodes__sys_synchronization_source__query__getSynchronizationFeed__parameter__selector": "<PERSON><PERSON><PERSON>", "@sage/xtrem-interop/nodes__sys_synchronization_source__query__getSynchronizationFeed__parameter__startTick": "Marca de início", "@sage/xtrem-interop/nodes__sys_synchronization_state__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-interop/nodes__sys_synchronization_state__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-interop/nodes__sys_synchronization_state__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-interop/nodes__sys_synchronization_state__node_name": "Status da sincronização do sistema", "@sage/xtrem-interop/nodes__sys_synchronization_state__property__lastNotificationState": "Status da última notificação", "@sage/xtrem-interop/nodes__sys_synchronization_state__property__node": "<PERSON><PERSON> (node)", "@sage/xtrem-interop/nodes__sys_synchronization_state__property__notificationStates": "A notificação informa", "@sage/xtrem-interop/nodes__sys_synchronization_state__property__remoteApp": "Aplicativo remoto", "@sage/xtrem-interop/nodes__sys_synchronization_state__property__succcessStamp": "<PERSON><PERSON> de sucesso (timestamp)", "@sage/xtrem-interop/nodes__sys_synchronization_state__property__syncTick": "Marca de sincronização", "@sage/xtrem-interop/nodes__sys_synchronization_target__asyncMutation__synchronize": "Sincronizar", "@sage/xtrem-interop/nodes__sys_synchronization_target__asyncMutation__synchronize__failed": "A sincronização falhou.", "@sage/xtrem-interop/nodes__sys_synchronization_target__asyncMutation__synchronize__parameter__filtersByNode": "Filtros por nó", "@sage/xtrem-interop/nodes__sys_synchronization_target__asyncMutation__synchronize__parameter__forceSyncState": "Forçar status de sincronização", "@sage/xtrem-interop/nodes__sys_synchronization_target__asyncMutation__synchronize__parameter__fullSync": "Sincronização total", "@sage/xtrem-interop/nodes__sys_synchronization_target__asyncMutation__synchronize__parameter__localNodeNames": "Nós (nodes) locais", "@sage/xtrem-interop/nodes__sys_synchronization_target__asyncMutation__synchronize__parameter__remoteApp": "Aplicação remota", "@sage/xtrem-interop/nodes__sys_synchronization_target__asyncMutation__synchronize__parameter__skipDependencies": "Ignorar dependências", "@sage/xtrem-interop/nodes__sys_synchronization_target__node_name": "Destino da sincronização do sistema", "@sage/xtrem-interop/package__name": "Interoperabilidade", "@sage/xtrem-interop/pages__integration____navigationPanel__listItem__line11__title": "Link", "@sage/xtrem-interop/pages__integration____navigationPanel__listItem__line12__title": "Link", "@sage/xtrem-interop/pages__integration____navigationPanel__listItem__line2__title": "Aplicação de terceiros", "@sage/xtrem-interop/pages__sys_app____navigationPanel__listItem__isAlive__title": "Vivo", "@sage/xtrem-interop/pages__sys_app____navigationPanel__listItem__isConnector__title": "Conectar", "@sage/xtrem-interop/pages__sys_app____navigationPanel__listItem__line2__title": "Vers<PERSON>", "@sage/xtrem-interop/pages__sys_app____navigationPanel__listItem__line2Right__title": "Ativo", "@sage/xtrem-interop/pages__sys_app____navigationPanel__listItem__title__title": "Nome", "@sage/xtrem-interop/pages__sys_app____navigationPanel__listItem__titleRight__title": "Titulo", "@sage/xtrem-interop/pages__sys_app____navigationPanel__optionsMenu__title": "<PERSON><PERSON>", "@sage/xtrem-interop/pages__sys_app____navigationPanel__optionsMenu__title__2": "Ativo", "@sage/xtrem-interop/pages__sys_app____navigationPanel__optionsMenu__title__3": "Inativo", "@sage/xtrem-interop/pages__sys_app____objectTypePlural": "Aplicações <PERSON>", "@sage/xtrem-interop/pages__sys_app____objectTypeSingular": "Aplicação conectada", "@sage/xtrem-interop/pages__sys_app____title": "Aplicações <PERSON>", "@sage/xtrem-interop/pages__sys_app__interopPackage____title": "Pacote de interoperabilidade", "@sage/xtrem-interop/pages__sys_app__isActive____title": "Ativo", "@sage/xtrem-interop/pages__sys_app__isAlive____title": "Vivo", "@sage/xtrem-interop/pages__sys_app__isConnector____title": "Conectar", "@sage/xtrem-interop/pages__sys_app__name____title": "Nome", "@sage/xtrem-interop/pages__sys_app__refreshActiveState____title": "Atualizar estado ativo", "@sage/xtrem-interop/pages__sys_app__sync_all_message": "Sync started.", "@sage/xtrem-interop/pages__sys_app__syncAllNodes____title": "Sin<PERSON><PERSON><PERSON>r tudo", "@sage/xtrem-interop/pages__sys_app__technicalBlock____title": "Técnico", "@sage/xtrem-interop/pages__sys_app__title____title": "Titulo", "@sage/xtrem-interop/pages__sys_app__version____title": "Vers<PERSON>", "@sage/xtrem-interop/pages__sys_enum_transformation____navigationPanel__inlineActions__title": "Dup<PERSON><PERSON>", "@sage/xtrem-interop/pages__sys_enum_transformation____navigationPanel__listItem__line2__title": "Ativo", "@sage/xtrem-interop/pages__sys_enum_transformation____navigationPanel__listItem__remoteApp__title": "Aplicativo remoto", "@sage/xtrem-interop/pages__sys_enum_transformation____navigationPanel__listItem__remoteAppVersion__title": "Versão do aplicativo remoto", "@sage/xtrem-interop/pages__sys_enum_transformation____navigationPanel__listItem__remoteEnum__title": "<PERSON><PERSON> remoto", "@sage/xtrem-interop/pages__sys_enum_transformation____navigationPanel__listItem__title__title": "Enum local", "@sage/xtrem-interop/pages__sys_enum_transformation____navigationPanel__listItem__titleRight__title": "ID de mapeamento", "@sage/xtrem-interop/pages__sys_enum_transformation____navigationPanel__optionsMenu__title": "Ativo", "@sage/xtrem-interop/pages__sys_enum_transformation____navigationPanel__optionsMenu__title__2": "Inativo", "@sage/xtrem-interop/pages__sys_enum_transformation____navigationPanel__optionsMenu__title__3": "<PERSON><PERSON>", "@sage/xtrem-interop/pages__sys_enum_transformation____objectTypePlural": "Transformações Enum", "@sage/xtrem-interop/pages__sys_enum_transformation____objectTypeSingular": "Transformação Enum", "@sage/xtrem-interop/pages__sys_enum_transformation____title": "Transformações Enum", "@sage/xtrem-interop/pages__sys_enum_transformation__id____title": "ID de mapeamento", "@sage/xtrem-interop/pages__sys_enum_transformation__isActive____title": "Ativo", "@sage/xtrem-interop/pages__sys_enum_transformation__localEnum____columns__title__name": "Nome", "@sage/xtrem-interop/pages__sys_enum_transformation__localEnum____columns__title__title": "Titulo", "@sage/xtrem-interop/pages__sys_enum_transformation__localEnum____title": "Enum local", "@sage/xtrem-interop/pages__sys_enum_transformation__mappingBlock____title": "Mapeamento de enumeração", "@sage/xtrem-interop/pages__sys_enum_transformation__mappingTable____columns__title__localEnumValue": "Valor local", "@sage/xtrem-interop/pages__sys_enum_transformation__mappingTable____columns__title__remoteEnumValue": "<PERSON><PERSON> remoto", "@sage/xtrem-interop/pages__sys_enum_transformation__mappingTable____dropdownActions__title": "Atualizar registro", "@sage/xtrem-interop/pages__sys_enum_transformation__mappingTable____dropdownActions__title__2": "Eliminar", "@sage/xtrem-interop/pages__sys_enum_transformation__remote_data_type_not_found": "Não foi encontrado nenhum tipo de dados remoto.", "@sage/xtrem-interop/pages__sys_enum_transformation__remoteApp____title": "Aplicativo remoto", "@sage/xtrem-interop/pages__sys_enum_transformation__remoteAppVersion____title": "Versão da aplicação remota", "@sage/xtrem-interop/pages__sys_enum_transformation__remoteEnum____title": "<PERSON><PERSON> remoto", "@sage/xtrem-interop/pages__sys_enum_transformation__remoteEnumDialogSection____title": "Selecione enumeração remota", "@sage/xtrem-interop/pages__sys_enum_transformation__remoteEnumTable____columns__title__name": "Nome", "@sage/xtrem-interop/pages__sys_enum_transformation__remoteEnumTable____columns__title__title": "Titulo", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__dropdownActions__title": "Sincronize agora", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__dropdownActions__title__2": "Agendar sincronização", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__dropdownActions__title__3": "Dup<PERSON><PERSON>", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__dropdownActions__title__4": "Mostrar o log", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__inlineActions__title": "Dup<PERSON><PERSON>", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__listItem__lastError__title": "<PERSON><PERSON><PERSON> erro", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__listItem__lastSync__title": "Última sincronização", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__listItem__localNode__title": "Entidade local", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__listItem__name__title": "Aplicação  remota", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__listItem__remoteAppVersion__title": "Versão do aplicativo remoto", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__listItem__remoteNodeName__title": "Entidade remota", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__listItem__title__title": "ID de mapeamento", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__listItem__titleRight__title": "Ativo", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__optionsMenu__title": "Ativo", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__optionsMenu__title__2": "Inativo", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__optionsMenu__title__3": "<PERSON><PERSON>", "@sage/xtrem-interop/pages__sys_node_transformation____objectTypePlural": "Transformações de dados", "@sage/xtrem-interop/pages__sys_node_transformation____objectTypeSingular": "Transformação de dados", "@sage/xtrem-interop/pages__sys_node_transformation____title": "Transformações de sincronização", "@sage/xtrem-interop/pages__sys_node_transformation__dialogBlock____title": "Entidade remota", "@sage/xtrem-interop/pages__sys_node_transformation__dialogSection____title": "Selecione entidade remota", "@sage/xtrem-interop/pages__sys_node_transformation__id____title": "ID de mapeamento", "@sage/xtrem-interop/pages__sys_node_transformation__isActive____title": "Ativo", "@sage/xtrem-interop/pages__sys_node_transformation__lastErrorDate____title": "Último erro de sincronização", "@sage/xtrem-interop/pages__sys_node_transformation__lastSyncDate____title": "Última sincronização", "@sage/xtrem-interop/pages__sys_node_transformation__listOfRemoteNodes____columns__title__name": "Nome", "@sage/xtrem-interop/pages__sys_node_transformation__listOfRemoteNodes____columns__title__title": "Titulo", "@sage/xtrem-interop/pages__sys_node_transformation__localNode____columns__title__name": "Tipo de registro", "@sage/xtrem-interop/pages__sys_node_transformation__localNode____columns__title__package__name": "Pacote (package)", "@sage/xtrem-interop/pages__sys_node_transformation__localNode____columns__title__storage": "Armazenamento", "@sage/xtrem-interop/pages__sys_node_transformation__localNode____columns__title__title": "Tipo de registo", "@sage/xtrem-interop/pages__sys_node_transformation__localNode____title": "Entidade local", "@sage/xtrem-interop/pages__sys_node_transformation__localProperties____helperText": "<PERSON><PERSON><PERSON><PERSON> as propriedades que deseja sincronizar", "@sage/xtrem-interop/pages__sys_node_transformation__localPropertiesBlock____title": "Propriedades locais", "@sage/xtrem-interop/pages__sys_node_transformation__localPropertiesSection____title": "Propriedades locais", "@sage/xtrem-interop/pages__sys_node_transformation__mappingBlock____title": "Tabela de mapeamento", "@sage/xtrem-interop/pages__sys_node_transformation__mappingSection____title": "Mapeamento", "@sage/xtrem-interop/pages__sys_node_transformation__mappingTable____columns__title__kind": "Tipo de mapeamento", "@sage/xtrem-interop/pages__sys_node_transformation__mappingTable____columns__title__localProperty": "Valor local", "@sage/xtrem-interop/pages__sys_node_transformation__mappingTable____columns__title__remoteProperty": "<PERSON><PERSON> remoto", "@sage/xtrem-interop/pages__sys_node_transformation__mappingTable____dropdownActions__title": "<PERSON><PERSON> propried<PERSON> remota", "@sage/xtrem-interop/pages__sys_node_transformation__mappingTable____dropdownActions__title__2": "Atualizar registro", "@sage/xtrem-interop/pages__sys_node_transformation__mappingTable____dropdownActions__title__3": "Eliminar", "@sage/xtrem-interop/pages__sys_node_transformation__remote_not_available": "Os dados remotos não foram recuperados. O sistema remoto pode não estar disponível.\n\nMensagem original:\n\n{{originalMessage}}", "@sage/xtrem-interop/pages__sys_node_transformation__remoteApp____title": "Aplicação remota", "@sage/xtrem-interop/pages__sys_node_transformation__remoteNodeName____title": "Entidade remota", "@sage/xtrem-interop/pages__sys_node_transformation__remoteNodeNameName____title": "Entidade remota", "@sage/xtrem-interop/pages__sys_node_transformation__remotePropertiesBlock____title": "Propriedades remotas", "@sage/xtrem-interop/pages__sys_node_transformation__remotePropertiesSection____title": "Propriedades remotas", "@sage/xtrem-interop/pages__sys_node_transformation__remoteVersion____title": "<PERSON><PERSON><PERSON> remota", "@sage/xtrem-interop/pages__sys_node_transformation__scheduleSync____title": "Sincronização da agenda", "@sage/xtrem-interop/pages__sys_node_transformation__selectFromLocalProperties____title": "Adicionar l<PERSON> de propriedades locais", "@sage/xtrem-interop/pages__sys_node_transformation__synchronizeResult____title": "Resultados da sincronização", "@sage/xtrem-interop/pages__sys_node_transformation__syncNow____title": "Sincronizar agora", "@sage/xtrem-interop/pages__sys_node_transformation__syncSection____title": "Sincronizar", "@sage/xtrem-interop/pages__sys_node_transformation__too_many_remote_properties": "<PERSON><PERSON><PERSON><PERSON> propriedades remotas selecionadas para serem atribuídas. Verifique os resultados para confirmar.", "@sage/xtrem-interop/permission__create__name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-interop/permission__default_instance__name": "Instância predefinida", "@sage/xtrem-interop/permission__delete__name": "Eliminar", "@sage/xtrem-interop/permission__manage__name": "Gestão", "@sage/xtrem-interop/permission__read__name": "<PERSON>r", "@sage/xtrem-interop/permission__update__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-interop/service_options__synchronization_service_option__name": "Opção do serviço de sincronização"}