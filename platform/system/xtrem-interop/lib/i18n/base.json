{"@sage/xtrem-interop/activity__sys_app__name": "Sys app", "@sage/xtrem-interop/activity__sys_enum_synchronization__name": "Sys enum synchronization", "@sage/xtrem-interop/activity__sys_synchronization__name": "Sys synchronization", "@sage/xtrem-interop/data_types__id__name": "Id", "@sage/xtrem-interop/data_types__mapping_value__name": "Mapping value", "@sage/xtrem-interop/data_types__node_mapping_kind_enum__name": "Node mapping kind enum", "@sage/xtrem-interop/data_types__price__name": "Price", "@sage/xtrem-interop/data_types__quantity__name": "Quantity", "@sage/xtrem-interop/data_types__test_local_status_enum__name": "Test local status enum", "@sage/xtrem-interop/data_types__test_remote_status_enum__name": "Test remote status enum", "@sage/xtrem-interop/enums__node_mapping_kind__constant_to_path": "Constant to path", "@sage/xtrem-interop/enums__node_mapping_kind__function_to_path": "Function to path", "@sage/xtrem-interop/enums__node_mapping_kind__path_to_constant": "Path to constant", "@sage/xtrem-interop/enums__node_mapping_kind__path_to_function": "Path to function", "@sage/xtrem-interop/enums__node_mapping_kind__path_to_path": "Path to path", "@sage/xtrem-interop/enums__test_local_status__inProgress": "In progress", "@sage/xtrem-interop/enums__test_local_status__start": "Start", "@sage/xtrem-interop/enums__test_local_status__stop": "Stop", "@sage/xtrem-interop/enums__test_local_status__track": "Track", "@sage/xtrem-interop/enums__test_remote_status__begin": "<PERSON><PERSON>", "@sage/xtrem-interop/enums__test_remote_status__end": "End", "@sage/xtrem-interop/enums__test_remote_status__inProgress": "In progress", "@sage/xtrem-interop/menu_item__interop": "Interop", "@sage/xtrem-interop/nodes__sys_app__mutation__refreshState": "Refresh state", "@sage/xtrem-interop/nodes__sys_app__mutation__refreshState__failed": "Refresh state failed.", "@sage/xtrem-interop/nodes__sys_app__node_name": "Sys app", "@sage/xtrem-interop/nodes__sys_app__property__interopPackage": "Interop package", "@sage/xtrem-interop/nodes__sys_app__property__isActive": "Is active", "@sage/xtrem-interop/nodes__sys_app__property__isAlive": "Is alive", "@sage/xtrem-interop/nodes__sys_app__property__isConnector": "Is connector", "@sage/xtrem-interop/nodes__sys_app__property__name": "Name", "@sage/xtrem-interop/nodes__sys_app__property__title": "Title", "@sage/xtrem-interop/nodes__sys_app__property__version": "Version", "@sage/xtrem-interop/nodes__sys_app__query__getAppInfo": "Get app info", "@sage/xtrem-interop/nodes__sys_app__query__getAppInfo__failed": "Get app info failed.", "@sage/xtrem-interop/nodes__sys_enum_mapping__asyncMutation__asyncExport": "Export", "@sage/xtrem-interop/nodes__sys_enum_mapping__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-interop/nodes__sys_enum_mapping__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-interop/nodes__sys_enum_mapping__node_name": "Sys enum mapping", "@sage/xtrem-interop/nodes__sys_enum_mapping__property__localEnumValue": "Local enum value", "@sage/xtrem-interop/nodes__sys_enum_mapping__property__remoteEnumValue": "Remote enum value", "@sage/xtrem-interop/nodes__sys_enum_mapping__property__transform": "Transform", "@sage/xtrem-interop/nodes__sys_enum_transformation__asyncMutation__asyncExport": "Export", "@sage/xtrem-interop/nodes__sys_enum_transformation__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-interop/nodes__sys_enum_transformation__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-interop/nodes__sys_enum_transformation__mutation__updateActiveMappings": "Update active mappings", "@sage/xtrem-interop/nodes__sys_enum_transformation__mutation__updateActiveMappings__failed": "Update active mappings failed.", "@sage/xtrem-interop/nodes__sys_enum_transformation__mutation__updateActiveMappings__parameter__parameters": "Parameters", "@sage/xtrem-interop/nodes__sys_enum_transformation__node_name": "Sys enum transformation", "@sage/xtrem-interop/nodes__sys_enum_transformation__property__id": "Id", "@sage/xtrem-interop/nodes__sys_enum_transformation__property__isActive": "Is active", "@sage/xtrem-interop/nodes__sys_enum_transformation__property__localEnum": "Local enum", "@sage/xtrem-interop/nodes__sys_enum_transformation__property__map": "Map", "@sage/xtrem-interop/nodes__sys_enum_transformation__property__remoteApp": "Remote app", "@sage/xtrem-interop/nodes__sys_enum_transformation__property__remoteAppVersion": "Remote app version", "@sage/xtrem-interop/nodes__sys_enum_transformation__property__remoteEnum": "Remote enum", "@sage/xtrem-interop/nodes__sys_node_mapping__asyncMutation__asyncExport": "Export", "@sage/xtrem-interop/nodes__sys_node_mapping__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-interop/nodes__sys_node_mapping__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-interop/nodes__sys_node_mapping__node_name": "Sys node mapping", "@sage/xtrem-interop/nodes__sys_node_mapping__property__kind": "Kind", "@sage/xtrem-interop/nodes__sys_node_mapping__property__localProperty": "Local property", "@sage/xtrem-interop/nodes__sys_node_mapping__property__remoteProperty": "Remote property", "@sage/xtrem-interop/nodes__sys_node_mapping__property__transform": "Transform", "@sage/xtrem-interop/nodes__sys_node_transformation__asyncMutation__asyncExport": "Export", "@sage/xtrem-interop/nodes__sys_node_transformation__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-interop/nodes__sys_node_transformation__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-interop/nodes__sys_node_transformation__mutation__updateActiveMappings": "Update active mappings", "@sage/xtrem-interop/nodes__sys_node_transformation__mutation__updateActiveMappings__failed": "Update active mappings failed.", "@sage/xtrem-interop/nodes__sys_node_transformation__mutation__updateActiveMappings__parameter__parameters": "Parameters", "@sage/xtrem-interop/nodes__sys_node_transformation__node_name": "Sys node transformation", "@sage/xtrem-interop/nodes__sys_node_transformation__property__filter": "Filter", "@sage/xtrem-interop/nodes__sys_node_transformation__property__id": "Id", "@sage/xtrem-interop/nodes__sys_node_transformation__property__isActive": "Is active", "@sage/xtrem-interop/nodes__sys_node_transformation__property__lastError": "Last error", "@sage/xtrem-interop/nodes__sys_node_transformation__property__lastSync": "Last sync", "@sage/xtrem-interop/nodes__sys_node_transformation__property__localNode": "Local node", "@sage/xtrem-interop/nodes__sys_node_transformation__property__map": "Map", "@sage/xtrem-interop/nodes__sys_node_transformation__property__mappings": "Mappings", "@sage/xtrem-interop/nodes__sys_node_transformation__property__remoteApp": "Remote app", "@sage/xtrem-interop/nodes__sys_node_transformation__property__remoteAppVersion": "Remote app version", "@sage/xtrem-interop/nodes__sys_node_transformation__property__remoteMappings": "Remote mappings", "@sage/xtrem-interop/nodes__sys_node_transformation__property__remoteNodeName": "Remote node name", "@sage/xtrem-interop/nodes__sys_node_transformation__query__hasActiveMapping": "Has active mapping", "@sage/xtrem-interop/nodes__sys_node_transformation__query__hasActiveMapping__failed": "Has active mapping failed.", "@sage/xtrem-interop/nodes__sys_node_transformation__query__hasActiveMapping__parameter__localNode": "Local node", "@sage/xtrem-interop/nodes__sys_node_transformation__query__hasActiveMapping__parameter__propertyId": "Property id", "@sage/xtrem-interop/nodes__sys_operation_transformation__asyncMutation__asyncExport": "Export", "@sage/xtrem-interop/nodes__sys_operation_transformation__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-interop/nodes__sys_operation_transformation__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-interop/nodes__sys_operation_transformation__node_name": "Sys operation transformation", "@sage/xtrem-interop/nodes__sys_operation_transformation__property__id": "Id", "@sage/xtrem-interop/nodes__sys_operation_transformation__property__isActive": "Is active", "@sage/xtrem-interop/nodes__sys_operation_transformation__property__localNode": "Local node", "@sage/xtrem-interop/nodes__sys_operation_transformation__property__localOperationName": "Local operation name", "@sage/xtrem-interop/nodes__sys_operation_transformation__property__mappings": "Mappings", "@sage/xtrem-interop/nodes__sys_operation_transformation__property__remoteApp": "Remote app", "@sage/xtrem-interop/nodes__sys_operation_transformation__property__remoteAppVersion": "Remote app version", "@sage/xtrem-interop/nodes__sys_operation_transformation__property__remoteNodeName": "Remote node name", "@sage/xtrem-interop/nodes__sys_operation_transformation__property__remoteOperationName": "Remote operation name", "@sage/xtrem-interop/nodes__sys_operation_transformation__property__remotePackageName": "Remote package name", "@sage/xtrem-interop/nodes__sys_remote_metadata__node_name": "Sys remote metadata", "@sage/xtrem-interop/nodes__sys_remote_metadata__query__getDataTypeInfo": "Get data type info", "@sage/xtrem-interop/nodes__sys_remote_metadata__query__getDataTypeInfo__failed": "Get data type info failed.", "@sage/xtrem-interop/nodes__sys_remote_metadata__query__getDataTypeInfo__parameter__app": "App", "@sage/xtrem-interop/nodes__sys_remote_metadata__query__getDataTypeInfo__parameter__name": "Name", "@sage/xtrem-interop/nodes__sys_remote_metadata__query__getDataTypeInfo__parameter__type": "Type", "@sage/xtrem-interop/nodes__sys_remote_metadata__query__getNodeInfo": "Get node info", "@sage/xtrem-interop/nodes__sys_remote_metadata__query__getNodeInfo__failed": "Get node info failed.", "@sage/xtrem-interop/nodes__sys_remote_metadata__query__getNodeInfo__parameter__app": "App", "@sage/xtrem-interop/nodes__sys_remote_metadata__query__getNodeInfo__parameter__filter": "Filter", "@sage/xtrem-interop/nodes__sys_remote_metadata__query__getNodeInfo__parameter__name": "Name", "@sage/xtrem-interop/nodes__sys_synchronization_source__node_name": "Sys synchronization source", "@sage/xtrem-interop/nodes__sys_synchronization_source__query__getSynchronizationFeed": "Get synchronization feed", "@sage/xtrem-interop/nodes__sys_synchronization_source__query__getSynchronizationFeed__failed": "Get synchronization feed failed.", "@sage/xtrem-interop/nodes__sys_synchronization_source__query__getSynchronizationFeed__parameter__filter": "Filter", "@sage/xtrem-interop/nodes__sys_synchronization_source__query__getSynchronizationFeed__parameter__node": "Node", "@sage/xtrem-interop/nodes__sys_synchronization_source__query__getSynchronizationFeed__parameter__selector": "Selector", "@sage/xtrem-interop/nodes__sys_synchronization_source__query__getSynchronizationFeed__parameter__startTick": "Start tick", "@sage/xtrem-interop/nodes__sys_synchronization_state__asyncMutation__asyncExport": "Export", "@sage/xtrem-interop/nodes__sys_synchronization_state__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-interop/nodes__sys_synchronization_state__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-interop/nodes__sys_synchronization_state__node_name": "Sys synchronization state", "@sage/xtrem-interop/nodes__sys_synchronization_state__property__lastNotificationState": "Last notification state", "@sage/xtrem-interop/nodes__sys_synchronization_state__property__node": "Node", "@sage/xtrem-interop/nodes__sys_synchronization_state__property__notificationStates": "Notification states", "@sage/xtrem-interop/nodes__sys_synchronization_state__property__remoteApp": "Remote app", "@sage/xtrem-interop/nodes__sys_synchronization_state__property__succcessStamp": "Succcess stamp", "@sage/xtrem-interop/nodes__sys_synchronization_state__property__syncTick": "Sync tick", "@sage/xtrem-interop/nodes__sys_synchronization_target__asyncMutation__synchronize": "Synchronize", "@sage/xtrem-interop/nodes__sys_synchronization_target__asyncMutation__synchronize__failed": "Synchronize failed.", "@sage/xtrem-interop/nodes__sys_synchronization_target__asyncMutation__synchronize__parameter__filtersByNode": "Filters by node", "@sage/xtrem-interop/nodes__sys_synchronization_target__asyncMutation__synchronize__parameter__forceSyncState": "Force sync state", "@sage/xtrem-interop/nodes__sys_synchronization_target__asyncMutation__synchronize__parameter__fullSync": "Full sync", "@sage/xtrem-interop/nodes__sys_synchronization_target__asyncMutation__synchronize__parameter__localNodeNames": "Local node names", "@sage/xtrem-interop/nodes__sys_synchronization_target__asyncMutation__synchronize__parameter__remoteApp": "Remote app", "@sage/xtrem-interop/nodes__sys_synchronization_target__asyncMutation__synchronize__parameter__skipDependencies": "Skip dependencies", "@sage/xtrem-interop/nodes__sys_synchronization_target__node_name": "Sys synchronization target", "@sage/xtrem-interop/package__name": "Sage xtrem interop", "@sage/xtrem-interop/pages__sys_app____navigationPanel__listItem__isAlive__title": "Alive", "@sage/xtrem-interop/pages__sys_app____navigationPanel__listItem__isConnector__title": "Connector", "@sage/xtrem-interop/pages__sys_app____navigationPanel__listItem__line2__title": "Version", "@sage/xtrem-interop/pages__sys_app____navigationPanel__listItem__line2Right__title": "Active", "@sage/xtrem-interop/pages__sys_app____navigationPanel__listItem__title__title": "Name", "@sage/xtrem-interop/pages__sys_app____navigationPanel__listItem__titleRight__title": "Title", "@sage/xtrem-interop/pages__sys_app____navigationPanel__optionsMenu__title": "All", "@sage/xtrem-interop/pages__sys_app____navigationPanel__optionsMenu__title__2": "Active", "@sage/xtrem-interop/pages__sys_app____navigationPanel__optionsMenu__title__3": "Inactive", "@sage/xtrem-interop/pages__sys_app____objectTypePlural": "Connected applications", "@sage/xtrem-interop/pages__sys_app____objectTypeSingular": "Connected application", "@sage/xtrem-interop/pages__sys_app____title": "Connected applications", "@sage/xtrem-interop/pages__sys_app__interopPackage____title": "Interop package", "@sage/xtrem-interop/pages__sys_app__isActive____title": "Active", "@sage/xtrem-interop/pages__sys_app__isAlive____title": "Alive", "@sage/xtrem-interop/pages__sys_app__isConnector____title": "Connector", "@sage/xtrem-interop/pages__sys_app__name____title": "Name", "@sage/xtrem-interop/pages__sys_app__refreshActiveState____title": "Refresh active state", "@sage/xtrem-interop/pages__sys_app__sync_all_message": "Sync started.", "@sage/xtrem-interop/pages__sys_app__syncAllNodes____title": "Sync all", "@sage/xtrem-interop/pages__sys_app__technicalBlock____title": "Technical", "@sage/xtrem-interop/pages__sys_app__title____title": "Title", "@sage/xtrem-interop/pages__sys_app__version____title": "Version", "@sage/xtrem-interop/pages__sys_enum_transformation____navigationPanel__inlineActions__title": "Duplicate", "@sage/xtrem-interop/pages__sys_enum_transformation____navigationPanel__listItem__line2__title": "Active", "@sage/xtrem-interop/pages__sys_enum_transformation____navigationPanel__listItem__remoteApp__title": "Remote app", "@sage/xtrem-interop/pages__sys_enum_transformation____navigationPanel__listItem__remoteAppVersion__title": "Remote app version", "@sage/xtrem-interop/pages__sys_enum_transformation____navigationPanel__listItem__remoteEnum__title": "Remote enum", "@sage/xtrem-interop/pages__sys_enum_transformation____navigationPanel__listItem__title__title": "Local Enum", "@sage/xtrem-interop/pages__sys_enum_transformation____navigationPanel__listItem__titleRight__title": "Mapping ID", "@sage/xtrem-interop/pages__sys_enum_transformation____navigationPanel__optionsMenu__title": "Active", "@sage/xtrem-interop/pages__sys_enum_transformation____navigationPanel__optionsMenu__title__2": "Inactive", "@sage/xtrem-interop/pages__sys_enum_transformation____navigationPanel__optionsMenu__title__3": "All", "@sage/xtrem-interop/pages__sys_enum_transformation____objectTypePlural": "Enum transformations", "@sage/xtrem-interop/pages__sys_enum_transformation____objectTypeSingular": "Enum transformation", "@sage/xtrem-interop/pages__sys_enum_transformation____title": "<PERSON><PERSON> transforms", "@sage/xtrem-interop/pages__sys_enum_transformation__id____title": "Mapping ID", "@sage/xtrem-interop/pages__sys_enum_transformation__isActive____title": "Active", "@sage/xtrem-interop/pages__sys_enum_transformation__localEnum____columns__title__name": "name", "@sage/xtrem-interop/pages__sys_enum_transformation__localEnum____columns__title__title": "Title", "@sage/xtrem-interop/pages__sys_enum_transformation__localEnum____title": "local enum", "@sage/xtrem-interop/pages__sys_enum_transformation__mappingBlock____title": "Enum mapping", "@sage/xtrem-interop/pages__sys_enum_transformation__mappingTable____columns__title__localEnumValue": "Local value", "@sage/xtrem-interop/pages__sys_enum_transformation__mappingTable____columns__title__remoteEnumValue": "Remote value", "@sage/xtrem-interop/pages__sys_enum_transformation__mappingTable____dropdownActions__title": "Refresh record", "@sage/xtrem-interop/pages__sys_enum_transformation__mappingTable____dropdownActions__title__2": "Remove", "@sage/xtrem-interop/pages__sys_enum_transformation__remote_data_type_not_found": "No remote data type found.", "@sage/xtrem-interop/pages__sys_enum_transformation__remoteApp____title": "Remote app", "@sage/xtrem-interop/pages__sys_enum_transformation__remoteAppVersion____title": "Remote app version", "@sage/xtrem-interop/pages__sys_enum_transformation__remoteEnum____title": "Remote enum", "@sage/xtrem-interop/pages__sys_enum_transformation__remoteEnumDialogSection____title": "Select remote enum", "@sage/xtrem-interop/pages__sys_enum_transformation__remoteEnumTable____columns__title__name": "name", "@sage/xtrem-interop/pages__sys_enum_transformation__remoteEnumTable____columns__title__title": "Title", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__dropdownActions__title": "Sync now", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__dropdownActions__title__2": "Schedule sync", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__dropdownActions__title__3": "Duplicate", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__dropdownActions__title__4": "Display log", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__inlineActions__title": "Duplicate", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__listItem__lastError__title": "Last Error", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__listItem__lastSync__title": "Last sync", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__listItem__localNode__title": "Local entity", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__listItem__name__title": "Remote app", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__listItem__remoteAppVersion__title": "Remote app version", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__listItem__remoteNodeName__title": "Remote entity", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__listItem__title__title": "Mapping ID", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__listItem__titleRight__title": "Active", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__optionsMenu__title": "Active", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__optionsMenu__title__2": "Inactive", "@sage/xtrem-interop/pages__sys_node_transformation____navigationPanel__optionsMenu__title__3": "All", "@sage/xtrem-interop/pages__sys_node_transformation____objectTypePlural": "Data transformations", "@sage/xtrem-interop/pages__sys_node_transformation____objectTypeSingular": "Data transformation", "@sage/xtrem-interop/pages__sys_node_transformation____title": "Synchronization transforms", "@sage/xtrem-interop/pages__sys_node_transformation__dialogBlock____title": "Remote entity", "@sage/xtrem-interop/pages__sys_node_transformation__dialogSection____title": "Select remote entity", "@sage/xtrem-interop/pages__sys_node_transformation__id____title": "Mapping ID", "@sage/xtrem-interop/pages__sys_node_transformation__isActive____title": "Active", "@sage/xtrem-interop/pages__sys_node_transformation__lastErrorDate____title": "Last Sync Error", "@sage/xtrem-interop/pages__sys_node_transformation__lastSyncDate____title": "Last Sync", "@sage/xtrem-interop/pages__sys_node_transformation__listOfRemoteNodes____columns__title__name": "Name", "@sage/xtrem-interop/pages__sys_node_transformation__listOfRemoteNodes____columns__title__title": "Title", "@sage/xtrem-interop/pages__sys_node_transformation__localNode____columns__title__name": "name", "@sage/xtrem-interop/pages__sys_node_transformation__localNode____columns__title__package__name": "Package", "@sage/xtrem-interop/pages__sys_node_transformation__localNode____columns__title__storage": "storage", "@sage/xtrem-interop/pages__sys_node_transformation__localNode____columns__title__title": "title", "@sage/xtrem-interop/pages__sys_node_transformation__localNode____title": "Local entity", "@sage/xtrem-interop/pages__sys_node_transformation__localPropertiesBlock____title": "Local properties", "@sage/xtrem-interop/pages__sys_node_transformation__localPropertiesSection____title": "Local properties", "@sage/xtrem-interop/pages__sys_node_transformation__mappingBlock____title": "Mapping table", "@sage/xtrem-interop/pages__sys_node_transformation__mappingSection____title": "Mapping", "@sage/xtrem-interop/pages__sys_node_transformation__mappingTable____columns__title__kind": "Mapping type", "@sage/xtrem-interop/pages__sys_node_transformation__mappingTable____columns__title__localProperty": "Local value", "@sage/xtrem-interop/pages__sys_node_transformation__mappingTable____columns__title__remoteProperty": "Remote value", "@sage/xtrem-interop/pages__sys_node_transformation__mappingTable____dropdownActions__title": "Edit remote property", "@sage/xtrem-interop/pages__sys_node_transformation__mappingTable____dropdownActions__title__2": "Refresh record", "@sage/xtrem-interop/pages__sys_node_transformation__mappingTable____dropdownActions__title__3": "Remove", "@sage/xtrem-interop/pages__sys_node_transformation__remote_not_available": "Remote data was not retrieved. The remote system might not be available.\n\nOriginal message:\n\n{{originalMessage}}", "@sage/xtrem-interop/pages__sys_node_transformation__remoteApp____title": "Remote app", "@sage/xtrem-interop/pages__sys_node_transformation__remoteNodeName____title": "Remote entity", "@sage/xtrem-interop/pages__sys_node_transformation__remotePropertiesBlock____title": "Remote properties", "@sage/xtrem-interop/pages__sys_node_transformation__remotePropertiesSection____title": "Remote properties", "@sage/xtrem-interop/pages__sys_node_transformation__remoteVersion____title": "Remote version", "@sage/xtrem-interop/pages__sys_node_transformation__scheduleSync____title": "Schedule synchronization", "@sage/xtrem-interop/pages__sys_node_transformation__selectFromLocalProperties____title": "Add lines from local properties", "@sage/xtrem-interop/pages__sys_node_transformation__synchronizeResult____title": "Synchronization results", "@sage/xtrem-interop/pages__sys_node_transformation__syncNow____title": "Sync now", "@sage/xtrem-interop/pages__sys_node_transformation__syncSection____title": "Sync", "@sage/xtrem-interop/pages__sys_node_transformation__too_many_remote_properties": "Too many remote properties selected to be assigned. Check the results to confirm.", "@sage/xtrem-interop/permission__create__name": "Create", "@sage/xtrem-interop/permission__default_instance__name": "Default instance", "@sage/xtrem-interop/permission__delete__name": "Delete", "@sage/xtrem-interop/permission__manage__name": "Manage", "@sage/xtrem-interop/permission__read__name": "Read", "@sage/xtrem-interop/permission__update__name": "Update", "@sage/xtrem-interop/service_options__synchronization_service_option__name": "Synchronization service option"}