import { Activity } from '@sage/xtrem-core';
import { SysNodeTransformation, SysRemoteMetadata, SysSynchronizationTarget } from '../nodes';

export const sysSynchronization = new Activity({
    description: 'Configuration of synchronization',
    node: () => SysNodeTransformation,
    __filename,
    permissions: ['read', 'create', 'update', 'delete', 'defaultInstance'],
    operationGrants: {
        read: [
            {
                on: [() => SysSynchronizationTarget],
                operations: ['synchronize'],
            },
            {
                on: [() => SysNodeTransformation],
                operations: ['hasActiveMapping'],
            },
            {
                on: [() => SysRemoteMetadata],
                operations: ['getNodeInfo'],
            },
        ],
        create: [
            {
                on: [() => SysNodeTransformation],
                operations: ['hasActiveMapping', 'updateActiveMappings'],
            },
        ],
        update: [
            {
                on: [() => SysNodeTransformation],
                operations: ['hasActiveMapping', 'updateActiveMappings'],
            },
        ],
    },
});
