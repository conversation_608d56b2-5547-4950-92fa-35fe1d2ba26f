import { Activity } from '@sage/xtrem-core';
import { SysEnumTransformation, SysRemoteMetadata, SysSynchronizationTarget } from '../nodes';

export const sysEnumSynchronization = new Activity({
    description: 'Configuration of enum synchronization',
    node: () => SysEnumTransformation,
    __filename,
    permissions: ['read', 'create', 'update', 'delete'],
    operationGrants: {
        read: [
            {
                on: [() => SysSynchronizationTarget],
                operations: ['synchronize'],
            },
            {
                on: [() => SysRemoteMetadata],
                operations: ['getDataTypeInfo'],
            },
        ],
        create: [
            {
                on: [() => SysEnumTransformation],
                operations: ['updateActiveMappings'],
            },
            {
                on: [() => SysRemoteMetadata],
                operations: ['getDataTypeInfo'],
            },
        ],
        update: [
            {
                on: [() => SysEnumTransformation],
                operations: ['updateActiveMappings'],
            },
            {
                on: [() => SysRemoteMetadata],
                operations: ['getDataTypeInfo'],
            },
        ],
    },
});
