import { Activity } from '@sage/xtrem-core';
import { SysApp } from '../nodes';

export const sysApp = new Activity({
    description: 'Interop apps',
    node: () => SysApp,
    __filename,
    permissions: ['read', 'manage'],
    operationGrants: {
        read: [
            {
                operations: ['getAppInfo'],
                on: [() => SysApp],
            },
        ],
        manage: [
            {
                operations: ['read', 'update', 'create', 'getAppInfo', 'refreshState'],
                on: [() => SysApp],
            },
        ],
    },
});
