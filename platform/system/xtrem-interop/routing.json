{"@sage/xtrem-interop": [{"topic": "SysEnumMapping/asyncExport/start", "queue": "import-export", "sourceFileName": "sys-enum-mapping.ts"}, {"topic": "SysEnumTransformation/asyncExport/start", "queue": "import-export", "sourceFileName": "sys-enum-transformation.ts"}, {"topic": "SysNodeMapping/asyncExport/start", "queue": "import-export", "sourceFileName": "sys-node-mapping.ts"}, {"topic": "SysNodeTransformation/asyncExport/start", "queue": "import-export", "sourceFileName": "sys-node-transformation.ts"}, {"topic": "SysOperationTransformation/asyncExport/start", "queue": "import-export", "sourceFileName": "sys-operation-transformation.ts"}, {"topic": "SysRemoteMetadata/asyncExport/start", "queue": "import-export", "sourceFileName": "sys-remote-metadata.ts"}, {"topic": "SysSynchronizationSource/asyncExport/start", "queue": "import-export", "sourceFileName": "sys-synchronization-source.ts"}, {"topic": "SysSynchronizationState/asyncExport/start", "queue": "import-export", "sourceFileName": "sys-synchronization-state.ts"}, {"topic": "SysSynchronizationTarget/asyncExport/start", "queue": "import-export", "sourceFileName": "sys-synchronization-target.ts"}, {"topic": "SysSynchronizationTarget/synchronize/start", "queue": "interop", "sourceFileName": "sys-synchronization-target.ts"}]}