declare module '@sage/xtrem-interop-api-partial' {
    import type { Package as SageXtremAuditing$Package } from '@sage/xtrem-auditing-api';
    import type { Package as SageXtremAuthorization$Package } from '@sage/xtrem-authorization-api';
    import type {
        Package as SageXtremCommunication$Package,
        SysNotificationState,
    } from '@sage/xtrem-communication-api';
    import type { Package as SageXtremCustomization$Package } from '@sage/xtrem-customization-api';
    import type { Package as SageXtremDashboard$Package } from '@sage/xtrem-dashboard-api';
    import type { MetaDataType, MetaNodeFactory, Package as SageXtremMetadata$Package } from '@sage/xtrem-metadata-api';
    import type { Package as SageXtremRouting$Package } from '@sage/xtrem-routing-api';
    import type { Package as SageXtremScheduler$Package } from '@sage/xtrem-scheduler-api';
    import type { Package as SageXtremSystem$Package, SysVendor, User } from '@sage/xtrem-system-api';
    import type { Package as SageXtremWorkflow$Package } from '@sage/xtrem-workflow-api';
    import type {
        AggregateQueryOperation,
        AggregateReadOperation,
        AsyncOperation,
        ClientCollection,
        ClientNode,
        ClientNodeInput,
        CreateOperation,
        DeleteOperation,
        DuplicateOperation,
        GetDefaultsOperation,
        GetDuplicateOperation,
        Operation as Node$Operation,
        QueryOperation,
        ReadOperation,
        UpdateByIdOperation,
        UpdateOperation,
        VitalClientNode,
        VitalClientNodeInput,
        decimal,
        integer,
    } from '@sage/xtrem-client';
    export interface NodeMappingKind$Enum {
        path_to_path: 0;
        path_to_constant: 1;
        path_to_function: 2;
        constant_to_path: 3;
        function_to_path: 4;
    }
    export type NodeMappingKind = keyof NodeMappingKind$Enum;
    export interface SysApp extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        name: string;
        version: string;
        title: string;
        isConnector: boolean;
        interopPackage: string;
        isActive: boolean;
        isAlive: boolean;
    }
    export interface SysAppInput extends ClientNodeInput {
        _vendor?: integer | string;
        name?: string;
        title?: string;
        isConnector?: boolean | string;
        interopPackage?: string;
        isActive?: boolean | string;
    }
    export interface SysAppBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        name: string;
        version: string;
        title: string;
        isConnector: boolean;
        interopPackage: string;
        isActive: boolean;
        isAlive: boolean;
    }
    export interface SysApp$Queries {
        getAppInfo: Node$Operation<
            {},
            {
                name: string;
                version: string;
                metadataPackage: string;
            }
        >;
    }
    export interface SysApp$Mutations {
        refreshState: Node$Operation<{}, boolean>;
    }
    export interface SysApp$Lookups {
        _vendor: QueryOperation<SysVendor>;
    }
    export interface SysApp$Operations {
        query: QueryOperation<SysApp>;
        read: ReadOperation<SysApp>;
        aggregate: {
            read: AggregateReadOperation<SysApp>;
            query: AggregateQueryOperation<SysApp>;
        };
        queries: SysApp$Queries;
        create: CreateOperation<SysAppInput, SysApp>;
        getDuplicate: GetDuplicateOperation<SysApp>;
        update: UpdateOperation<SysAppInput, SysApp>;
        updateById: UpdateByIdOperation<SysAppInput, SysApp>;
        mutations: SysApp$Mutations;
        lookups(dataOrId: string | { data: SysAppInput }): SysApp$Lookups;
        getDefaults: GetDefaultsOperation<SysApp>;
    }
    export interface SysEnumMapping extends VitalClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        transform: SysEnumTransformation;
        localEnumValue: string;
        remoteEnumValue: string;
    }
    export interface SysEnumMappingInput extends VitalClientNodeInput {
        _vendor?: integer | string;
        localEnumValue?: string;
        remoteEnumValue?: string;
    }
    export interface SysEnumMappingBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        transform: SysEnumTransformation;
        localEnumValue: string;
        remoteEnumValue: string;
    }
    export interface SysEnumMapping$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface SysEnumMapping$Lookups {
        _vendor: QueryOperation<SysVendor>;
    }
    export interface SysEnumMapping$Operations {
        query: QueryOperation<SysEnumMapping>;
        read: ReadOperation<SysEnumMapping>;
        aggregate: {
            read: AggregateReadOperation<SysEnumMapping>;
            query: AggregateQueryOperation<SysEnumMapping>;
        };
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: SysEnumMapping$AsyncOperations;
        lookups(dataOrId: string | { data: SysEnumMappingInput }): SysEnumMapping$Lookups;
        getDefaults: GetDefaultsOperation<SysEnumMapping>;
    }
    export interface SysEnumTransformation extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        id: string;
        localEnum: MetaDataType;
        isActive: boolean;
        remoteApp: SysApp;
        remoteAppVersion: string;
        remoteEnum: string;
        map: ClientCollection<SysEnumMapping>;
    }
    export interface SysEnumTransformationInput extends ClientNodeInput {
        _vendor?: integer | string;
        id?: string;
        localEnum?: integer | string;
        isActive?: boolean | string;
        remoteApp?: integer | string;
        remoteAppVersion?: string;
        remoteEnum?: string;
        map?: Partial<SysEnumMappingInput>[];
    }
    export interface SysEnumTransformationBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        id: string;
        localEnum: MetaDataType;
        isActive: boolean;
        remoteApp: SysApp;
        remoteAppVersion: string;
        remoteEnum: string;
        map: ClientCollection<SysEnumMappingBinding>;
    }
    export interface SysEnumTransformation$Mutations {
        updateActiveMappings: Node$Operation<
            {
                parameters: {
                    remoteAppId: string;
                    active: boolean | string;
                    version?: string;
                };
            },
            boolean
        >;
    }
    export interface SysEnumTransformation$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface SysEnumTransformation$Lookups {
        _vendor: QueryOperation<SysVendor>;
        localEnum: QueryOperation<MetaDataType>;
        remoteApp: QueryOperation<SysApp>;
    }
    export interface SysEnumTransformation$Operations {
        query: QueryOperation<SysEnumTransformation>;
        read: ReadOperation<SysEnumTransformation>;
        aggregate: {
            read: AggregateReadOperation<SysEnumTransformation>;
            query: AggregateQueryOperation<SysEnumTransformation>;
        };
        create: CreateOperation<SysEnumTransformationInput, SysEnumTransformation>;
        getDuplicate: GetDuplicateOperation<SysEnumTransformation>;
        duplicate: DuplicateOperation<string, SysEnumTransformationInput, SysEnumTransformation>;
        update: UpdateOperation<SysEnumTransformationInput, SysEnumTransformation>;
        updateById: UpdateByIdOperation<SysEnumTransformationInput, SysEnumTransformation>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        mutations: SysEnumTransformation$Mutations;
        asyncOperations: SysEnumTransformation$AsyncOperations;
        lookups(dataOrId: string | { data: SysEnumTransformationInput }): SysEnumTransformation$Lookups;
        getDefaults: GetDefaultsOperation<SysEnumTransformation>;
    }
    export interface SysNodeMapping extends VitalClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        transform: SysNodeTransformation;
        localProperty: string;
        remoteProperty: string;
        kind: NodeMappingKind;
    }
    export interface SysNodeMappingInput extends VitalClientNodeInput {
        _vendor?: integer | string;
        localProperty?: string;
        remoteProperty?: string;
        kind?: NodeMappingKind;
    }
    export interface SysNodeMappingBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        transform: SysNodeTransformation;
        localProperty: string;
        remoteProperty: string;
        kind: NodeMappingKind;
    }
    export interface SysNodeMapping$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface SysNodeMapping$Lookups {
        _vendor: QueryOperation<SysVendor>;
    }
    export interface SysNodeMapping$Operations {
        query: QueryOperation<SysNodeMapping>;
        read: ReadOperation<SysNodeMapping>;
        aggregate: {
            read: AggregateReadOperation<SysNodeMapping>;
            query: AggregateQueryOperation<SysNodeMapping>;
        };
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: SysNodeMapping$AsyncOperations;
        lookups(dataOrId: string | { data: SysNodeMappingInput }): SysNodeMapping$Lookups;
        getDefaults: GetDefaultsOperation<SysNodeMapping>;
    }
    export interface SysNodeTransformation extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        id: string;
        isActive: boolean;
        remoteApp: SysApp;
        remoteAppVersion: string;
        remoteNodeName: string;
        localNode: MetaNodeFactory;
        mappings: string;
        remoteMappings: string;
        filter: string;
        lastError: string;
        lastSync: string;
        map: ClientCollection<SysNodeMapping>;
    }
    export interface SysNodeTransformationInput extends ClientNodeInput {
        _vendor?: integer | string;
        id?: string;
        isActive?: boolean | string;
        remoteApp?: integer | string;
        remoteAppVersion?: string;
        remoteNodeName?: string;
        localNode?: integer | string;
        filter?: string;
        lastError?: string;
        lastSync?: string;
        map?: Partial<SysNodeMappingInput>[];
    }
    export interface SysNodeTransformationBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        id: string;
        isActive: boolean;
        remoteApp: SysApp;
        remoteAppVersion: string;
        remoteNodeName: string;
        localNode: MetaNodeFactory;
        mappings: any;
        remoteMappings: any;
        filter: any;
        lastError: string;
        lastSync: string;
        map: ClientCollection<SysNodeMappingBinding>;
    }
    export interface SysNodeTransformation$Queries {
        hasActiveMapping: Node$Operation<
            {
                localNode?: string;
                propertyId?: string;
            },
            boolean
        >;
    }
    export interface SysNodeTransformation$Mutations {
        updateActiveMappings: Node$Operation<
            {
                parameters: {
                    remoteAppId: string;
                    active: boolean | string;
                    version?: string;
                };
            },
            boolean
        >;
    }
    export interface SysNodeTransformation$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface SysNodeTransformation$Lookups {
        _vendor: QueryOperation<SysVendor>;
        remoteApp: QueryOperation<SysApp>;
        localNode: QueryOperation<MetaNodeFactory>;
    }
    export interface SysNodeTransformation$Operations {
        query: QueryOperation<SysNodeTransformation>;
        read: ReadOperation<SysNodeTransformation>;
        aggregate: {
            read: AggregateReadOperation<SysNodeTransformation>;
            query: AggregateQueryOperation<SysNodeTransformation>;
        };
        queries: SysNodeTransformation$Queries;
        create: CreateOperation<SysNodeTransformationInput, SysNodeTransformation>;
        getDuplicate: GetDuplicateOperation<SysNodeTransformation>;
        duplicate: DuplicateOperation<string, SysNodeTransformationInput, SysNodeTransformation>;
        update: UpdateOperation<SysNodeTransformationInput, SysNodeTransformation>;
        updateById: UpdateByIdOperation<SysNodeTransformationInput, SysNodeTransformation>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        mutations: SysNodeTransformation$Mutations;
        asyncOperations: SysNodeTransformation$AsyncOperations;
        lookups(dataOrId: string | { data: SysNodeTransformationInput }): SysNodeTransformation$Lookups;
        getDefaults: GetDefaultsOperation<SysNodeTransformation>;
    }
    export interface SysOperationTransformation extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        id: string;
        isActive: boolean;
        remoteApp: SysApp;
        remoteAppVersion: string;
        remotePackageName: string;
        remoteNodeName: string;
        remoteOperationName: string;
        localNode: MetaNodeFactory;
        localOperationName: string;
        mappings: string;
    }
    export interface SysOperationTransformationInput extends ClientNodeInput {
        _vendor?: integer | string;
        id?: string;
        isActive?: boolean | string;
        remoteApp?: integer | string;
        remoteAppVersion?: string;
        remotePackageName?: string;
        remoteNodeName?: string;
        remoteOperationName?: string;
        localNode?: integer | string;
        localOperationName?: string;
        mappings?: string;
    }
    export interface SysOperationTransformationBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        id: string;
        isActive: boolean;
        remoteApp: SysApp;
        remoteAppVersion: string;
        remotePackageName: string;
        remoteNodeName: string;
        remoteOperationName: string;
        localNode: MetaNodeFactory;
        localOperationName: string;
        mappings: any;
    }
    export interface SysOperationTransformation$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface SysOperationTransformation$Lookups {
        _vendor: QueryOperation<SysVendor>;
        remoteApp: QueryOperation<SysApp>;
        localNode: QueryOperation<MetaNodeFactory>;
    }
    export interface SysOperationTransformation$Operations {
        query: QueryOperation<SysOperationTransformation>;
        read: ReadOperation<SysOperationTransformation>;
        aggregate: {
            read: AggregateReadOperation<SysOperationTransformation>;
            query: AggregateQueryOperation<SysOperationTransformation>;
        };
        create: CreateOperation<SysOperationTransformationInput, SysOperationTransformation>;
        getDuplicate: GetDuplicateOperation<SysOperationTransformation>;
        update: UpdateOperation<SysOperationTransformationInput, SysOperationTransformation>;
        updateById: UpdateByIdOperation<SysOperationTransformationInput, SysOperationTransformation>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: SysOperationTransformation$AsyncOperations;
        lookups(dataOrId: string | { data: SysOperationTransformationInput }): SysOperationTransformation$Lookups;
        getDefaults: GetDefaultsOperation<SysOperationTransformation>;
    }
    export interface SysRemoteMetadata extends ClientNode {}
    export interface SysRemoteMetadataInput extends ClientNodeInput {}
    export interface SysRemoteMetadataBinding extends ClientNode {}
    export interface SysRemoteMetadata$Queries {
        getDataTypeInfo: Node$Operation<
            {
                app: string;
                name?: string;
                type?: string;
            },
            {
                name: string;
                title: string;
                attributes: string;
            }[]
        >;
        getNodeInfo: Node$Operation<
            {
                app: string;
                name?: string;
                filter?: string;
            },
            {
                name: string;
                title: string;
                packageName: string;
                naturalKey: string[];
                properties: {
                    name: string;
                    title: string;
                    type: string;
                    dataType: string;
                    isStored: boolean;
                    targetNode: string;
                    isTransientInput: boolean;
                    isStoredOutput: boolean;
                    isRequired: boolean;
                    isNullable: boolean;
                    isCustom: boolean;
                    canSort: boolean;
                    canFilter: boolean;
                    isOnInputType: boolean;
                    isOnOutputType: boolean;
                    isMutable: boolean;
                    isVitalParent: boolean;
                }[];
                operations: {
                    name: string;
                    title: string;
                    kind: string;
                    action: string;
                    isMutation: boolean;
                    signature: {
                        parameters: {
                            name: string;
                            type: string;
                        }[];
                        return: string;
                    };
                }[];
            }[]
        >;
    }
    export interface SysRemoteMetadata$Operations {
        queries: SysRemoteMetadata$Queries;
        getDefaults: GetDefaultsOperation<SysRemoteMetadata>;
    }
    export interface SysSynchronizationSource extends ClientNode {}
    export interface SysSynchronizationSourceInput extends ClientNodeInput {}
    export interface SysSynchronizationSourceBinding extends ClientNode {}
    export interface SysSynchronizationSource$Queries {
        getSynchronizationFeed: Node$Operation<
            {
                node: string;
                startTick?: decimal | string;
                selector?: string;
                filter?: string;
            },
            {
                nextTick: string;
                feed: string[];
            }
        >;
    }
    export interface SysSynchronizationSource$Operations {
        queries: SysSynchronizationSource$Queries;
        getDefaults: GetDefaultsOperation<SysSynchronizationSource>;
    }
    export interface SysSynchronizationState extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        remoteApp: SysApp;
        node: MetaNodeFactory;
        syncTick: string;
        succcessStamp: string;
        lastNotificationState: SysNotificationState;
        notificationStates: ClientCollection<SysNotificationState>;
    }
    export interface SysSynchronizationStateInput extends ClientNodeInput {
        remoteApp?: integer | string;
        node?: integer | string;
        syncTick?: decimal | string;
        succcessStamp?: string;
        lastNotificationState?: integer | string;
    }
    export interface SysSynchronizationStateBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        remoteApp: SysApp;
        node: MetaNodeFactory;
        syncTick: string;
        succcessStamp: string;
        lastNotificationState: SysNotificationState;
        notificationStates: ClientCollection<SysNotificationState>;
    }
    export interface SysSynchronizationState$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface SysSynchronizationState$Lookups {
        remoteApp: QueryOperation<SysApp>;
        node: QueryOperation<MetaNodeFactory>;
        lastNotificationState: QueryOperation<SysNotificationState>;
    }
    export interface SysSynchronizationState$Operations {
        query: QueryOperation<SysSynchronizationState>;
        read: ReadOperation<SysSynchronizationState>;
        aggregate: {
            read: AggregateReadOperation<SysSynchronizationState>;
            query: AggregateQueryOperation<SysSynchronizationState>;
        };
        asyncOperations: SysSynchronizationState$AsyncOperations;
        lookups(dataOrId: string | { data: SysSynchronizationStateInput }): SysSynchronizationState$Lookups;
        getDefaults: GetDefaultsOperation<SysSynchronizationState>;
    }
    export interface SysSynchronizationTarget extends ClientNode {}
    export interface SysSynchronizationTargetInput extends ClientNodeInput {}
    export interface SysSynchronizationTargetBinding extends ClientNode {}
    export interface SysSynchronizationTarget$AsyncOperations {
        synchronize: AsyncOperation<
            {
                remoteApp?: string;
                localNodeNames?: string[];
                skipDependencies?: boolean | string;
                forceSyncState?: boolean | string;
                fullSync?: boolean | string;
                filtersByNode?: string;
            },
            {
                localNodeName: string;
                created: integer;
                updated: integer;
                skipped: integer;
            }[]
        >;
    }
    export interface SysSynchronizationTarget$Operations {
        asyncOperations: SysSynchronizationTarget$AsyncOperations;
    }
    export interface Package {
        '@sage/xtrem-interop/SysApp': SysApp$Operations;
        '@sage/xtrem-interop/SysEnumMapping': SysEnumMapping$Operations;
        '@sage/xtrem-interop/SysEnumTransformation': SysEnumTransformation$Operations;
        '@sage/xtrem-interop/SysNodeMapping': SysNodeMapping$Operations;
        '@sage/xtrem-interop/SysNodeTransformation': SysNodeTransformation$Operations;
        '@sage/xtrem-interop/SysOperationTransformation': SysOperationTransformation$Operations;
        '@sage/xtrem-interop/SysRemoteMetadata': SysRemoteMetadata$Operations;
        '@sage/xtrem-interop/SysSynchronizationSource': SysSynchronizationSource$Operations;
        '@sage/xtrem-interop/SysSynchronizationState': SysSynchronizationState$Operations;
        '@sage/xtrem-interop/SysSynchronizationTarget': SysSynchronizationTarget$Operations;
    }
    export interface GraphApi
        extends Package,
            SageXtremAuditing$Package,
            SageXtremAuthorization$Package,
            SageXtremCommunication$Package,
            SageXtremCustomization$Package,
            SageXtremDashboard$Package,
            SageXtremMetadata$Package,
            SageXtremRouting$Package,
            SageXtremScheduler$Package,
            SageXtremSystem$Package,
            SageXtremWorkflow$Package {}
}
declare module '@sage/xtrem-interop-api' {
    export type * from '@sage/xtrem-interop-api-partial';
}
declare module '@sage/xtrem-auditing-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-interop-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-authorization-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-interop-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-communication-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-interop-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-customization-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-interop-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-dashboard-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-interop-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-metadata-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-interop-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-routing-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-interop-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-scheduler-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-interop-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-system-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-interop-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-workflow-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-interop-api';
    export interface GraphApi extends GraphApiExtension {}
}
