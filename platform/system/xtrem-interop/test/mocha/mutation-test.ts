import { DateValue, Test, asyncArray } from '@sage/xtrem-core';
import * as xtremMetadata from '@sage/xtrem-metadata';
import { assert } from 'chai';
import { describe } from 'mocha';
import { SysApp, SysSynchronizationTarget } from '../../lib/nodes';
import { synchronizationServiceOption } from '../../lib/service-options/synchronization-service-option';
import { TestExternalDocument } from '../fixtures/lib/nodes/_index';

async function syncData(): Promise<void> {
    // Run a first synchronization pass
    await Test.withReadonlyContext(
        async context => {
            const testApp = await context.read(SysApp, { name: 'test' });
            const localNodes = await asyncArray(['TestTargetItem'])
                .map(name => context.read(xtremMetadata.nodes.MetaNodeFactory, { name }))
                .toArray();
            return SysSynchronizationTarget.synchronize(context, testApp, localNodes);
        },
        // set source to customMutation to enable runInWritableContext calls
        { source: 'customMutation', testActiveServiceOptions: [synchronizationServiceOption] },
    );
}

describe('Mutation tests', () => {
    before(async () => {
        await syncData();
        process.env.XTREM_INTEROP_TEST = '2';
    });

    it('Can create external node ', async () => {
        await Test.withContext(async context => {
            const node = await context.create(TestExternalDocument, {
                number: 'FOO-99',
                date: DateValue.parse('2023-12-10'),
                status: 'start',
                lines: [
                    { _sortValue: 100, quantity: 10, item: '#ITEM-01' },
                    { _sortValue: 200, quantity: 20, item: '#ITEM-02' },
                    { _sortValue: 300, quantity: 30, item: '#ITEM-03' },
                ],
            });
            await node.$.save();
        });

        await Test.withContext(async context => {
            const node = await context.read(TestExternalDocument, { number: 'FOO-99' });
            assert.equal((await node.date).value, 20231210);
            assert.equal(await node.status, 'start');
        });
    });

    it('Cannot create external node with invalid remote data', async () => {
        await Test.withContext(async context => {
            const node = await context.create(TestExternalDocument, {
                number: 'FOO-XX',
                date: DateValue.parse('2023-12-10'),
                status: 'start',
                lines: [
                    { _sortValue: 100, quantity: -10, item: '#ITEM-01' },
                    { _sortValue: 200, quantity: 20, item: '#ITEM-02' },
                    { _sortValue: 300, quantity: 30, item: '#ITEM-03' },
                ],
            });
            await assert.isRejected(node.$.save(), 'test: Error creating remote node');

            assert.deepEqual(context.diagnoses, [
                { severity: 3, path: [], message: 'The record was not created.' },
                {
                    message: 'value must not be less than 0',
                    path: [],
                    severity: 3,
                },
            ]);
        });
    });

    it('Can update external node ', async () => {
        await Test.withContext(async context => {
            const node = await context.read(TestExternalDocument, { number: 'DOC-01' }, { forUpdate: true });
            await node.$.set({
                date: DateValue.parse('2024-01-01'),
                status: 'inProgress',
                lines: [{ _sortValue: 100, quantity: 10, item: '#ITEM-01', _action: 'update' }],
            });
            await node.$.save();
        });

        await Test.withContext(async context => {
            const node = await context.read(TestExternalDocument, { number: 'DOC-01' }, { forUpdate: true });
            assert.equal((await node.date).value, 20240101);
            assert.equal(await node.status, 'inProgress');
            const line = await node.lines.takeOne(async l => (await l._sortValue) === 100);
            assert.equal(await line?.quantity, 10);
            assert.equal(await (await line?.item)?.code, 'ITEM-01');
        });
    });

    it('Can delete external node ', async () => {
        await Test.withContext(async context => {
            const node = await context.create(TestExternalDocument, {
                number: 'DELETE-99',
                date: DateValue.parse('2023-12-10'),
                status: 'start',
                lines: [
                    { _sortValue: 100, quantity: 10, item: '#ITEM-01' },
                    { _sortValue: 200, quantity: 20, item: '#ITEM-02' },
                    { _sortValue: 300, quantity: 30, item: '#ITEM-03' },
                ],
            });
            await node.$.save();
        });

        await Test.withContext(async context => {
            const node = await context.read(TestExternalDocument, { number: 'DELETE-99' });
            assert.equal((await node.date).value, 20231210);
            assert.equal(await node.status, 'start');
        });

        await Test.withContext(async context => {
            const node = await context.read(TestExternalDocument, { number: 'DELETE-99' }, { forUpdate: true });
            await node.$.delete();
        });

        await Test.withContext(async context => {
            const node = await context.tryRead(TestExternalDocument, { number: 'DELETE-99' });
            assert.isNull(node);
        });
    });
});
