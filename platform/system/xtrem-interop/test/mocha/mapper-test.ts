import { assert } from 'chai';
import { describe } from 'mocha';
import { InteropMapper, Mappings } from '../../lib/classes/interop-mapper';

describe('Mapper', () => {
    it('Can map complex object', async () => {
        const inputData = {
            number: 'DOC01',
            date: '2023-12-07',
            address: {
                town: 'Palo Alto',
                country: { code: 'USA' },
            },
            sourceLines: [
                {
                    product: 'P1',
                    quantity: 3,
                    taxLines: [
                        { code: 'VAT', rate: 0.2 },
                        { code: 'GST', rate: 0.1 },
                    ],
                },
                {
                    product: 'P2',
                    quantity: 10,
                },
            ],
        };

        const mappings: Mappings = {
            id: { kind: 'path', value: 'number' },
            date: { kind: 'path', value: 'date' },
            'address.city': { kind: 'path', value: 'address.town' },
            'address.country': { kind: 'path', value: 'address.country.code' },
            'lines..item.origin': { kind: 'constant', value: 'web shop' },
            'lines..item.code': { kind: 'path', value: 'sourceLines..product' },
            'lines..quantity': { kind: 'path', value: 'sourceLines..quantity' },
            'lines..taxes..taxCode': { kind: 'path', value: 'sourceLines..taxLines..code' },
            'lines..taxes..rate': { kind: 'path', value: 'sourceLines..taxLines..rate' },
        };

        const expectedData = {
            id: 'DOC01',
            date: '2023-12-07',
            address: {
                city: 'Palo Alto',
                country: 'USA',
            },
            lines: [
                {
                    item: { code: 'P1', origin: 'web shop' },
                    quantity: 3,
                    taxes: [
                        { taxCode: 'VAT', rate: 0.2 },
                        { taxCode: 'GST', rate: 0.1 },
                    ],
                },
                {
                    item: { code: 'P2', origin: 'web shop' },
                    quantity: 10,
                },
            ],
        };

        const outputData = await InteropMapper.applyMappings(inputData, mappings);
        assert.deepEqual(outputData, expectedData);
    });
});
