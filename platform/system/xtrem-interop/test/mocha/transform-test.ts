import { Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import { InteropMapper, Mappings } from '../../lib/classes/interop-mapper';
import { InteropTransform } from '../../lib/classes/interop-transform';
import { MetadataClient } from '../../lib/classes/metadata-client';
import { TestExternalDocument } from '../fixtures/lib/nodes/test-external-document';

const testMappings: Mappings = {
    number: { kind: 'path', value: 'id' },
    'address.city': { kind: 'path', value: 'address.town' },
    'address.country': { kind: 'path', value: 'address.country.code' },
    'lines..item': { kind: 'path', value: 'sourceLines..product' },
    'lines..quantity': { kind: 'path', value: 'sourceLines..quantity' },
    requestedDeliveryDate: { kind: 'constant', value: '2025-12-29' },
    'site.id': { kind: 'constant', value: '501' },
    'soldToCustomer.businessEntity.id': { kind: 'constant', value: 'DE072' },
};

describe('Transform test', () => {
    it('can transform a payload', async () => {
        const sourcePayload = {
            id: 'DOC01',
            address: {
                town: 'Palo Alto',
                country: { code: 'USA' },
            },
            requestedDeliveryDate: '2027-01-18',
            site: { id: '504' },
            soldToCustomer: {
                businessEntity: { id: 'SA021' },
            },
            sourceLines: [
                {
                    product: 'P1',
                    quantity: 3,
                },
                {
                    product: 'P2',
                    quantity: 10,
                },
            ],
        };
        const targetPayload = await InteropMapper.applyMappings(sourcePayload, testMappings);
        assert.deepEqual(targetPayload, {
            number: 'DOC01',
            address: {
                city: 'Palo Alto',
                country: 'USA',
            },
            requestedDeliveryDate: '2025-12-29',
            site: { id: '501' },
            soldToCustomer: {
                businessEntity: { id: 'DE072' },
            },
            lines: [
                {
                    item: 'P1',
                    quantity: 3,
                },
                {
                    item: 'P2',
                    quantity: 10,
                },
            ],
        });
    });

    it('can generate a selector', async () => {
        const selector = await InteropMapper.getMappingsSelector(testMappings);
        assert.deepEqual(selector, {
            id: true,
            address: { town: true, country: { code: true } },
            sourceLines: { product: true, quantity: true },
        });
    });

    it('Can tranform local node to remote payload ', async () => {
        await Test.withContext(async context => {
            const doc = await context.read(TestExternalDocument, { number: 'DOC-03' });
            const factory = context.application.getFactoryByConstructor(TestExternalDocument);
            const transform = await InteropTransform.findNodeTransform(context, factory);
            const metadataClient = new MetadataClient({
                remoteAppName: transform.remoteAppName,
                remoteInteropPackageName: transform.remoteInteropPackageName,
                remoteIsConnector: transform.remoteIsConnector,
            });
            const result = await InteropTransform.transformOutboundNodeValues(context, transform, doc, metadataClient);

            assert.deepEqual(result, {
                date: '2023-12-11',
                description: 'customText-03',
                id: 'DOC-03',
                lines: [
                    {
                        _sortValue: 100,
                        item: '#ITEM-01',
                        quantity: '3',
                    },
                ],
                status: 'inProgress',
            });
        });
    });
});
