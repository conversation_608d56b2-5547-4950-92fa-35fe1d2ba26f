import { Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as _ from 'lodash';
import { describe } from 'mocha';
import { SysApp } from '../../lib/nodes';
import { SysRemoteMetadata } from '../../lib/nodes/sys-remote-metadata';
import { synchronizationServiceOption } from '../../lib/service-options';

describe('Metadata tests', () => {
    it('Can get remote enum definition', () =>
        Test.withContext(
            async context => {
                const testApp = await context.read(SysApp, { name: 'test' });
                const result = await SysRemoteMetadata.getDataTypeInfo(context, testApp, 'testRemoteStatus', undefined);
                assert.deepEqual(
                    result.map(r => ({ ...r, attributes: _.omit(r.attributes, ['filename']) })),
                    [
                        {
                            name: 'testRemoteStatus',
                            title: 'Test remote status enum',
                            attributes: {
                                enum: {
                                    '1': 'begin',
                                    '2': 'end',
                                    '3': 'inProgress',
                                    begin: 1,
                                    end: 2,
                                    inProgress: 3,
                                },
                                isDefault: false,
                                enumName: 'TestRemoteStatus',
                                name: 'testRemoteStatus',
                                title: 'Test remote status enum',
                                type: 'enum',
                                values: [
                                    {
                                        title: 'Begin',
                                        value: 'begin',
                                    },
                                    {
                                        title: 'End',
                                        value: 'end',
                                    },
                                    {
                                        title: 'In Progress',
                                        value: 'inProgress',
                                    },
                                ],
                            },
                        },
                    ],
                );
            },
            { source: 'customMutation', testActiveServiceOptions: [synchronizationServiceOption], today: '2023-12-07' },
        ));

    it('Can get remote node definition', () =>
        Test.withContext(
            async context => {
                const testApp = await context.read(SysApp, { name: 'test' });
                const result = await SysRemoteMetadata.getNodeInfo(context, testApp, 'TestSourceItem', {
                    isPlatformNode: false,
                    naturalKey: { _ne: null },
                });
                assert.deepEqual(result, [
                    {
                        name: 'TestSourceItem',
                        storage: 'sql',
                        title: 'Test Source Item',
                        packageName: '@sage/xtrem-interop',
                        isPlatformNode: false,
                        naturalKey: ['code'],
                        properties: [
                            {
                                canFilter: true,
                                canSort: true,
                                isCustom: false,
                                isMutable: false,
                                isNullable: false,
                                isOnInputType: true,
                                isOnOutputType: true,
                                isRequired: false,
                                isStored: true,
                                isStoredOutput: false,
                                isTransientInput: false,
                                isVitalParent: false,
                                name: '_id',
                                title: 'ID',
                                type: 'string',
                            },
                            {
                                canFilter: true,
                                canSort: true,
                                dataType: 'code',
                                isCustom: false,
                                isMutable: false,
                                isNullable: false,
                                isOnInputType: true,
                                isOnOutputType: true,
                                isRequired: false,
                                isStored: true,
                                isStoredOutput: false,
                                isTransientInput: false,
                                isVitalParent: false,
                                name: 'code',
                                targetNode: undefined,
                                title: 'Code',
                                type: 'string',
                            },
                            {
                                canFilter: true,
                                canSort: true,
                                dataType: 'price',
                                isCustom: false,
                                isMutable: false,
                                isNullable: false,
                                isOnInputType: true,
                                isOnOutputType: true,
                                isRequired: false,
                                isStored: true,
                                isStoredOutput: false,
                                isTransientInput: false,
                                isVitalParent: false,
                                name: 'price',
                                targetNode: undefined,
                                title: 'Price',
                                type: 'decimal',
                            },
                        ],
                        operations: [
                            {
                                kind: 'asyncTrackerQuery',
                                name: 'asyncExport',
                                action: 'track',
                                signature: {
                                    parameters: [
                                        {
                                            isMandatory: true,
                                            name: 'trackingId',
                                            type: 'string',
                                        },
                                    ],
                                    return: {
                                        properties: {
                                            errorMessage: 'string',
                                            logMessages: {
                                                item: {
                                                    properties: {
                                                        level: 'string',
                                                        message: 'string',
                                                    },
                                                    type: 'object',
                                                },
                                                type: 'array',
                                            },
                                            result: 'string',
                                            status: 'string',
                                        },
                                        type: 'object',
                                    },
                                },
                                title: 'Async Export',
                                isMutation: true,
                            },
                            {
                                action: null,
                                isMutation: true,
                                kind: 'mutation',
                                name: 'getPrice',
                                signature: {
                                    parameters: [
                                        {
                                            name: 'remoteP1',
                                            properties: {
                                                item: {
                                                    node: 'TestSourceItem',
                                                    type: 'reference',
                                                },
                                            },
                                            type: 'object',
                                        },
                                    ],
                                    return: 'integer',
                                },
                                title: 'Get Price',
                            },
                            {
                                action: null,
                                isMutation: true,
                                kind: 'mutation',
                                name: 'getItem',
                                signature: {
                                    parameters: [
                                        {
                                            name: 'itemCode',
                                            type: 'string',
                                        },
                                    ],
                                    return: {
                                        node: 'TestSourceItem',
                                        type: 'instance',
                                    },
                                },
                                title: 'Get Item',
                            },
                            {
                                action: 'requestUserNotification',
                                isMutation: true,
                                kind: 'asyncMutation',
                                name: 'asyncExport',
                                signature: {
                                    parameters: [
                                        {
                                            isMandatory: true,
                                            name: 'trackingId',
                                            type: 'string',
                                        },
                                    ],
                                    return: {
                                        type: 'boolean',
                                    },
                                },
                                title: 'Async Export',
                            },

                            {
                                kind: 'asyncMutation',
                                name: 'asyncExport',
                                action: 'start',
                                signature: {
                                    parameters: [
                                        {
                                            name: 'id',
                                            type: 'string',
                                        },
                                        {
                                            name: 'filter',
                                            type: 'string',
                                        },
                                    ],
                                    return: {
                                        properties: {
                                            trackingId: 'string',
                                        },
                                        type: 'object',
                                    },
                                },
                                title: 'Async Export',
                                isMutation: true,
                            },
                            {
                                kind: 'asyncMutation',
                                name: 'asyncExport',
                                action: 'stop',
                                signature: {
                                    parameters: [
                                        {
                                            isMandatory: true,
                                            name: 'trackingId',
                                            type: 'string',
                                        },
                                        {
                                            name: 'reason',
                                            type: 'string',
                                        },
                                    ],
                                    return: {
                                        type: 'boolean',
                                    },
                                },
                                title: 'Async Export',
                                isMutation: true,
                            },
                        ],
                    },
                ]);
            },
            { source: 'customMutation', testActiveServiceOptions: [synchronizationServiceOption], today: '2023-12-07' },
        ));
});
