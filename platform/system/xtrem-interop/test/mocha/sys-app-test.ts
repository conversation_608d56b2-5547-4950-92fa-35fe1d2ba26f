import { Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import { describe } from 'mocha';
import { SysApp, SysEnumTransformation, SysNodeTransformation } from '../../lib/nodes';
import { synchronizationServiceOption } from '../../lib/service-options/synchronization-service-option';

describe('Check update of active mappings', () => {
    it('Make app inactive and check the mappings', async () => {
        await Test.withContext(
            async context => {
                const nodeMapping = await context.read(SysNodeTransformation, {
                    _id: '#TestTargetDocument|test|1.0.0|TestTargetDocumentFromTest',
                });
                assert.equal(await nodeMapping.isActive, true);
                const enumMapping = await context.read(SysEnumTransformation, {
                    _id: '#testLocalStatus|test|1.0.0|LOCAL_STATUS_TEST',
                });
                assert.equal(await enumMapping.isActive, true);

                const app = await context.read(SysApp, { name: 'test' }, { forUpdate: true });
                await app.$.set({ isActive: false });
                await app.$.save();

                const nodeMapping2 = await context.read(SysNodeTransformation, {
                    _id: '#TestTargetDocument|test|1.0.0|TestTargetDocumentFromTest',
                });
                assert.equal(await nodeMapping2.isActive, false);
                const enumMapping2 = await context.read(SysEnumTransformation, {
                    _id: '#testLocalStatus|test|1.0.0|LOCAL_STATUS_TEST',
                });
                assert.equal(await enumMapping2.isActive, false);
            },
            { testActiveServiceOptions: [synchronizationServiceOption] },
        );
    });
});
