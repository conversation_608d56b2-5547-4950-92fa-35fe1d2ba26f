import { Context, DateValue, Datetime, Dict, NodeCreateData, Test, asyncArray } from '@sage/xtrem-core';
import * as xtremMetadata from '@sage/xtrem-metadata';
import { assert } from 'chai';
import * as _ from 'lodash';
import { describe } from 'mocha';
import { Mapping } from '../../lib/classes/interop-mapper';
import { InteropNodeQueryClient } from '../../lib/classes/interop-node-query-client';
import { InteropTransform } from '../../lib/classes/interop-transform';
import { SysSyncCounters } from '../../lib/classes/synchronization-process';
import { SysSynchronizationClient } from '../../lib/classes/sys-synchronization-client';
import { SysApp, SysNodeTransformation, SysSynchronizationState } from '../../lib/nodes';
import { SysSynchronizationTarget } from '../../lib/nodes/sys-synchronization-target';
import { synchronizationServiceOption } from '../../lib/service-options';
import { TestLocalStatus, TestRemoteStatus, testRemoteStatus } from '../fixtures/lib/enums/_index';
import { TestExternalDocument, TestSourceItem, TestTargetItem } from '../fixtures/lib/nodes/_index';
import { TestLocalMutation } from '../fixtures/lib/nodes/test-local-mutation';
import { TestSourceDocument } from '../fixtures/lib/nodes/test-source-document';
import { TestTargetDocument } from '../fixtures/lib/nodes/test-target-document';

interface SynchronizationOptions {
    skipDependencies?: boolean;
    ignoreSkipped?: boolean;
    forceSyncState?: boolean;
    fullSync?: boolean;
}

type NodeDataTransformer = (doc: any) => any;

function pad0(i: number, n: number): string {
    const s = `0000${i}`;
    return s.substring(s.length - n);
}

const testItemData = Array.from(Array(5).keys()).map(i => {
    return {
        id: `ITEM-${pad0(i + 1, 2)}`,
        price: (i + 1) * 10,
    };
});

const testDocumentData = Array.from(Array(20).keys()).map(i => {
    const statusNum = (i % testRemoteStatus.values.length) + 1;
    const status: TestRemoteStatus = testRemoteStatus.stringValue(statusNum);

    return {
        id: `DOC-${pad0(i + 1, 2)}`,
        date: '2023-12-07',
        status,
        lines:
            (i + 1) % 4 === 0
                ? ['4', '5', '6', '7'].map((quantity, j) => ({
                      quantity,
                      item: `#ITEM-0${j + 1}`,
                  }))
                : [
                      {
                          quantity: `${(i % 4) + 1}`,
                          item: '#ITEM-01',
                      },
                  ],
        _customData: { customText: `customText-${pad0(i + 1, 2)}` },
    };
});

const statusMap: Dict<TestLocalStatus> = {
    begin: 'start',
    end: 'stop',
    inProgress: 'inProgress',
};

async function createDocumentTransformWithCustom(
    context: Context,
    id: string,
    customMapping: Dict<Mapping>,
): Promise<SysNodeTransformation> {
    const transform = await context.create(SysNodeTransformation, {
        id,
        isActive: true,
        remoteApp: { name: 'test' },
        remoteAppVersion: '1.0.0',
        remoteNodeName: 'TestSourceDocument',
        localNode: { name: 'TestTargetDocument' },
        mappings: {
            number: { kind: 'path', value: 'id' },
            date: { kind: 'path', value: 'date' },
            status: { kind: 'path', value: 'status' },
            'lines..quantity': { kind: 'path', value: 'lines..quantity' },
            'lines..item': { kind: 'path', value: 'lines..item' },
            ...customMapping,
        },
    });
    await transform.$.save();
    return transform;
}

describe('Interop tests', () => {
    it('Can read source feed', () =>
        Test.withContext(async context => {
            const result = await new SysSynchronizationClient({
                remoteAppName: 'test',
                remoteInteropPackageName: 'xtrem-interop',
                remoteIsConnector: false,
            }).getFeed(context, {
                nodeName: 'TestSourceDocument',
                startTick: 0,
                initialCount: 6,
                selector: { id: true, date: true, status: true, lines: { item: true, quantity: true } },
            });

            assert.isAbove(Number(result.nextTick), 0);
            assert.deepEqual(
                await result.reader.readAll(),
                testDocumentData.map(doc => _.omit(doc, '_customData')),
            );
        }));

    it('Can read source feed with custom', () =>
        Test.withContext(async context => {
            const result = await new SysSynchronizationClient({
                remoteAppName: 'test',
                remoteInteropPackageName: 'xtrem-interop',
                remoteIsConnector: false,
            }).getFeed(context, {
                nodeName: 'TestSourceDocument',
                startTick: 0,
                initialCount: 6,
                selector: {
                    id: true,
                    date: true,
                    status: true,
                    lines: { item: true, quantity: true },
                    _customData: { customText: true },
                },
            });

            assert.isAbove(Number(result.nextTick), 0);
            assert.deepEqual(await result.reader.readAll(), testDocumentData);
        }));

    it('Can create mappings with custom property', () =>
        Test.withContext(
            async context => {
                await createDocumentTransformWithCustom(context, 'transformTest1', {
                    '_customData.customText': { kind: 'path', value: 'description' },
                });
                await createDocumentTransformWithCustom(context, 'transformTest2', {
                    description: { kind: 'path', value: '_customData.customText' },
                });
                await createDocumentTransformWithCustom(context, 'transformTest3', {
                    '_customData.customText': { kind: 'path', value: '_customData.customText' },
                });
            },
            { source: 'customMutation', testActiveServiceOptions: [synchronizationServiceOption] },
        ));

    it('Can read source feed with filter', () =>
        Test.withContext(async context => {
            const result = await new SysSynchronizationClient({
                remoteAppName: 'test',
                remoteInteropPackageName: 'xtrem-interop',
                remoteIsConnector: false,
            }).getFeed(context, {
                nodeName: 'TestSourceDocument',
                startTick: 0,
                initialCount: 6,
                selector: { id: true, date: true, status: true, lines: { item: true, quantity: true } },
                filter: { id: { _eq: 'DOC-03' } },
            });

            assert.isAbove(Number(result.nextTick), 0);
            const results = await result.reader.readAll();
            assert.equal(results.length, 1);
            assert.equal(results[0].id, 'DOC-03');
        }));

    async function synchronize(
        expectedCounters: SysSyncCounters[],
        localNodeNames: string[] = [],
        {
            skipDependencies = false,
            ignoreSkipped = false,
            forceSyncState = false,
            fullSync = false,
        }: SynchronizationOptions = {},
    ) {
        // Start with readonly context, like graphql mutation
        const counters = await Test.withReadonlyContext(
            async context => {
                const testApp = await context.read(SysApp, { name: 'test' });
                const localNodes = await asyncArray(localNodeNames)
                    .map(name => context.read(xtremMetadata.nodes.MetaNodeFactory, { name }))
                    .toArray();
                return SysSynchronizationTarget.synchronize(
                    context,
                    testApp,
                    localNodes,
                    skipDependencies,
                    forceSyncState,
                    fullSync,
                );
            },
            // set source to customMutation to enable runInWritableContext calls
            { source: 'customMutation', testActiveServiceOptions: [synchronizationServiceOption] },
        );
        if (ignoreSkipped) {
            counters.forEach((counter, i) => {
                counter.skipped = expectedCounters[i].skipped;
            });
        }

        assert.deepEqual(_.sortBy(counters, 'localNodeName'), _.sortBy(expectedCounters, 'localNodeName'));
    }

    async function checkTargets(
        sourceData: {
            id: string;
            description?: string;
            date: string;
            status: TestRemoteStatus;
            lines: { quantity: string }[];
            _customData?: object;
        }[],
        transformer: Dict<NodeDataTransformer> = {},
    ) {
        const selector = Object.fromEntries(transformer ? Object.keys(transformer).map(k => [k, true]) : []);
        await Test.withReadonlyContext(async context => {
            const targetDocumentsRaw = await context.select(
                TestTargetDocument,
                {
                    number: true,
                    date: true,
                    status: true,
                    lines: { quantity: true },
                    _syncInfo: true,
                    ...selector,
                },
                { filter: {} },
            );
            targetDocumentsRaw.forEach(doc => {
                assert.isAbove(doc._syncInfo.sourceSyncTick, 0);
                assert.isNotNull(Datetime.parse(doc._syncInfo.sourceUpdateStamp));
            });
            const targetDocuments = targetDocumentsRaw.map(doc => _.omit(doc, '_syncInfo'));

            const expectedTargets = sourceData.map(doc => {
                const status = statusMap[doc.status];
                const data = {
                    number: doc.id,
                    date: DateValue.parse(doc.date),
                    status,
                    lines: doc.lines.map(line => ({ quantity: +line.quantity })),
                } as NodeCreateData<TestTargetDocument>;

                Object.keys(transformer || {}).forEach((key: keyof typeof data) => {
                    data[key] = transformer[key](doc);
                });
                return data;
            });
            assert.deepEqual(targetDocuments, expectedTargets as typeof targetDocuments);
        });
    }

    it('Can do full synchronize', async () => {
        const testDocumentDataWithoutCustomData = testDocumentData.map(doc => _.omit(doc, '_customData'));

        // Check that target documents table is empty
        await checkTargets([]);

        // Run a first synchronization pass
        await synchronize([
            { localNodeName: 'TestTargetItem', created: testItemData.length, updated: 0, skipped: 0 },
            { localNodeName: 'TestTargetDocument', created: testDocumentData.length, updated: 0, skipped: 0 },
        ]);

        // Check that the target documents have been created
        await checkTargets(testDocumentDataWithoutCustomData);

        // Run a second synchronization pass nothing should be done
        await synchronize([
            { localNodeName: 'TestTargetItem', created: 0, updated: 0, skipped: 0 },
            { localNodeName: 'TestTargetDocument', created: 0, updated: 0, skipped: 0 },
        ]);

        // Check that the target documents are still valid
        await checkTargets(testDocumentDataWithoutCustomData);

        // Run a full synchronization pass everything should be updated
        await synchronize(
            [
                { localNodeName: 'TestTargetItem', created: 0, updated: 0, skipped: testItemData.length },
                { localNodeName: 'TestTargetDocument', created: 0, updated: 0, skipped: testDocumentData.length },
            ],
            [],
            { fullSync: true },
        );

        // Check that the target documents are still valid
        await checkTargets(testDocumentDataWithoutCustomData);

        // Reset the local data
        await Test.withCommittedContext(context => context.deleteMany(SysSynchronizationState, {}));
        await Test.withCommittedContext(context => context.deleteMany(TestTargetDocument, {}));
        await Test.withCommittedContext(context => context.deleteMany(TestTargetItem, {}));
    });

    it('Can synchronize document node', async () => {
        // Check that target documents table is empty
        await checkTargets([]);

        // Run a first synchronization pass
        await synchronize([
            { localNodeName: 'TestTargetItem', created: testItemData.length, updated: 0, skipped: 0 },
            { localNodeName: 'TestTargetDocument', created: testDocumentData.length, updated: 0, skipped: 0 },
        ]);

        const testDocumentDataWithoutCustomData = testDocumentData.map(doc => _.omit(doc, '_customData'));

        // Check that the target documents have been created
        await checkTargets(testDocumentDataWithoutCustomData);

        const newPayload = {
            id: 'DOC-99',
            date: DateValue.parse('2023-12-10'),
            status: 'begin' as TestRemoteStatus,
            lines: [{ _sortValue: 100, quantity: 10, item: '#ITEM-01' }],
        };
        const updatePayload = { date: DateValue.parse('2023-12-11') };

        // Update DOC-03 and update DOC-99
        await Test.withCommittedContext(async context => {
            const doc1 = await context.create(TestSourceDocument, newPayload);
            await doc1.$.save();

            const doc2 = await context.read(TestSourceDocument, { id: 'DOC-03' }, { forUpdate: true });
            await doc2.$.set(updatePayload);
            await doc2.$.save();

            // Test filter on transform, we have the filter {"price": { "_gte": 10 }}
            const item1 = await context.create(TestSourceItem, { code: 'NOSYNC1', price: 5 });
            await item1.$.save();
        });

        // Run a second synchronization pass
        await synchronize(
            [
                { localNodeName: 'TestTargetItem', created: 0, updated: 0, skipped: 0 },
                { localNodeName: 'TestTargetDocument', created: 1, updated: 1, skipped: 0 },
            ],
            [],
            // if any transaction is committed in between the skipped value will be different but not relevant
            { ignoreSkipped: true },
        );

        const modifiedSourceData = [
            ...testDocumentDataWithoutCustomData,
            { id: 'DOC-99', date: '2023-12-10', status: 'begin' as TestRemoteStatus, lines: [{ quantity: '10' }] },
        ];
        modifiedSourceData[2] = {
            id: 'DOC-03',
            date: '2023-12-11',
            status: 'inProgress' as TestRemoteStatus,
            lines: [{ quantity: '3' }],
        };

        await checkTargets(modifiedSourceData);

        // Run another synchronization pass with only TestTargetDocument and its dependencies
        await synchronize(
            [
                { localNodeName: 'TestTargetItem', created: 0, updated: 0, skipped: 0 },
                { localNodeName: 'TestTargetDocument', created: 0, updated: 0, skipped: 0 },
            ],
            ['TestTargetDocument'],
            { ignoreSkipped: true },
        );

        // Run a third synchronization pass with only TestTargetDocument and none of its dependencies
        await synchronize(
            [{ localNodeName: 'TestTargetDocument', created: 0, updated: 0, skipped: 0 }],
            ['TestTargetDocument'],
            { skipDependencies: true, ignoreSkipped: true },
        );

        // Run a fourth synchronization pass with only TestTargetDocument and mapping custom properties
        await Test.withCommittedContext(async context => {
            const transform = await context.read(
                SysNodeTransformation,
                { id: 'TestTargetDocumentFromTest' },
                { forUpdate: true },
            );

            await transform.$.set({
                map: [
                    {
                        _action: 'create',
                        localProperty: '_customData.customText',
                        kind: 'path_to_path',
                        remoteProperty: 'description',
                    },
                ],
            });
            await transform.$.save();

            const doc3 = await context.read(TestSourceDocument, { id: 'DOC-03' }, { forUpdate: true });
            await doc3.$.set({ description: 'customText-03' });
            await doc3.$.save();
        });
        await synchronize(
            [{ localNodeName: 'TestTargetDocument', created: 0, updated: 1, skipped: 0 }],
            ['TestTargetDocument'],
            { skipDependencies: true, ignoreSkipped: true },
        );
        const modifiedSourceDataWithCustom = modifiedSourceData.map(doc => {
            if (doc.id === 'DOC-03') {
                return { ...doc, description: 'customText-03' };
            }
            return doc;
        });
        await checkTargets(modifiedSourceDataWithCustom, {
            _customData: doc => (doc.description ? { customText: doc.description } : {}),
        });

        // Run a fifth synchronization pass with only TestTargetDocument and using a function kind mapping
        await Test.withCommittedContext(async context => {
            const transform = await context.read(
                SysNodeTransformation,
                { id: 'TestTargetDocumentFromTest' },
                { forUpdate: true },
            );
            await transform.$.set({
                map: [
                    {
                        _action: 'create',
                        localProperty: 'description',
                        kind: 'path_to_function',
                        // eslint-disable-next-line no-template-curly-in-string
                        remoteProperty: 'return `${payload.status.toUpperCase()} ${payload.description || ""}`;',
                    },
                ],
            });
            await transform.$.save();

            const doc1 = await context.read(TestSourceDocument, { id: 'DOC-01' }, { forUpdate: true });
            await doc1.$.set({ description: 'DOC-01 for function' });
            await doc1.$.save();
        });
        await synchronize(
            [{ localNodeName: 'TestTargetDocument', created: 0, updated: 1, skipped: 0 }],
            ['TestTargetDocument'],
            { skipDependencies: true, ignoreSkipped: true },
        );
        const modifiedSourceDatabyFunction = modifiedSourceData.map(doc => {
            if (doc.id === 'DOC-01') {
                return { ...doc, description: 'DOC-01 for function' };
            }
            return doc;
        });
        await checkTargets(modifiedSourceDatabyFunction, {
            description: doc => (doc.description ? `${doc.status.toUpperCase()} ${doc.description}` : ''),
        });

        // Reset the SysSynchronizationState table
        await Test.withCommittedContext(context => context.deleteMany(SysSynchronizationState, {}));
        // Check that all records have been skipped (testData + 1 created by previous sync pass)
        await synchronize([
            { localNodeName: 'TestTargetItem', created: 0, updated: 0, skipped: testItemData.length },
            { localNodeName: 'TestTargetDocument', created: 0, updated: 0, skipped: testDocumentData.length + 1 },
        ]);
    });

    it('Can call remote mutation', () =>
        Test.withContext(async context => {
            const result = await TestLocalMutation.localMutate(context, 3, 'hello');
            assert.deepEqual(result, { localR1: 6, localR2: '--- hello ---' });
        }));

    it('Can call complex remote mutation', () =>
        Test.withContext(
            async context => {
                const result = await TestLocalMutation.localComplexMutate(context, { localP1: 3, localP2: 'hello' });

                assert.deepEqual(result, { innerObject: { localR1: 6, localR2: '--- hello ---', sum: 6 } });
            },
            { today: '2023-12-07' },
        ));

    it('Can call remote mutation with simple remote and local return typing', () =>
        Test.withContext(async context => {
            const result = await TestLocalMutation.localSimpleMutate(context, 3, 7);
            assert.equal(result, 10);
        }));

    // We need to do this test here after the sync to fill the local item table
    it('Can call remote mutation with reference parameter', () =>
        Test.withContext(async context => {
            const item = await context.read(TestTargetItem, { code: 'ITEM-03' });
            const result = await TestTargetItem.getRemotePrice(context, { item });
            assert.equal(result, 30);
        }));

    // We need to do this test here after the sync to fill the local item table
    it('Can call remote mutation with reference result', () =>
        Test.withContext(async context => {
            const item = await context.read(TestTargetItem, { code: 'ITEM-03' });
            const result = await TestTargetItem.getItem(context, item);
            assert.equal(await result.code, 'ITEM-03');
        }));

    it('Can query remote node', async () => {
        await Test.withContext(async context => {
            const factory = context.application.getFactoryByConstructor(TestTargetItem);
            const transform = await InteropTransform.findNodeTransform(context, factory);

            const queryClient = new InteropNodeQueryClient({
                ...transform,
            });

            const results = (
                await queryClient.queryRemote<TestTargetItem>(context, transform, {
                    filters: [{ unitPrice: { _in: [10, 20, 30] } }],
                    selector: { code: true, unitPrice: true },
                    first: 1,
                    orderBy: { unitPrice: -1 },
                })
            ).payload;

            assert.equal(results.length, 1);
            assert.equal(results[0].code, 'ITEM-03');
            assert.equal(String(results[0].unitPrice), '30');
        });
    });

    it('Can query remote node with cursor', async () => {
        await Test.withContext(async context => {
            const factory = context.application.getFactoryByConstructor(TestTargetItem);
            const item = await context.read(TestTargetItem, { code: 'ITEM-02' });
            const orderBy = { unitPrice: 1 } as any;
            const cursor = await item.$.getCursorValue(orderBy);
            const transform = await InteropTransform.findNodeTransform(context, factory);

            const queryClient = new InteropNodeQueryClient({
                ...transform,
            });

            const results = (
                await queryClient.queryRemote<TestTargetItem>(context, transform, {
                    filters: [{ unitPrice: { _in: [10, 20, 30] } }],
                    after: cursor,
                    selector: { code: true, unitPrice: true },
                    first: 1,
                    orderBy,
                })
            ).payload;

            assert.equal(results.length, 1);
            assert.equal(results[0].code, 'ITEM-03');
            assert.equal(String(results[0].unitPrice), '30');
        });
    });

    it('Can query remote totalCount', async () => {
        await Test.withContext(async context => {
            const factory = context.application.getFactoryByConstructor(TestTargetItem);
            const transform = await InteropTransform.findNodeTransform(context, factory);

            const queryClient = new InteropNodeQueryClient({
                ...transform,
            });

            const results = (
                await queryClient.queryRemote<TestTargetItem>(context, transform, {
                    filters: [{ unitPrice: { _in: [10, 20, 30] } }],
                    count: true,
                    orderBy: { unitPrice: -1 },
                })
            ).payload;

            assert.deepEqual(results, [{ totalCount: 3 }]);
        });
    });

    it('Can query aggregate on remote node ', async () => {
        await Test.withContext(async context => {
            const factory = context.application.getFactoryByConstructor(TestTargetItem);
            const transform = await InteropTransform.findNodeTransform(context, factory);

            const queryClient = new InteropNodeQueryClient({
                ...transform,
            });

            let results = (
                await queryClient.queryRemote<TestTargetItem>(context, transform, {
                    filters: [{ code: { _in: ['ITEM-01', 'ITEM-02', 'ITEM-03'] } }],
                    orderBy: { unitPrice: -1 },
                    aggregate: {
                        groups: [{ path: ['code'] }],
                        values: [
                            { path: ['unitPrice'], operator: 'min' },
                            { path: ['unitPrice'], operator: 'max' },
                            { path: ['unitPrice'], operator: 'avg' },
                            { path: ['unitPrice'], operator: 'sum' },
                        ],
                    },
                })
            ).payload;

            assert.deepEqual(results, [
                {
                    group: {
                        code: 'ITEM-03',
                    },
                    values: {
                        unitPrice: {
                            avg: '30',
                            max: '30',
                            min: '30',
                            sum: '30',
                        },
                    },
                },
                {
                    group: {
                        code: 'ITEM-02',
                    },
                    values: {
                        unitPrice: {
                            avg: '20',
                            max: '20',
                            min: '20',
                            sum: '20',
                        },
                    },
                },
                {
                    group: {
                        code: 'ITEM-01',
                    },
                    values: {
                        unitPrice: {
                            avg: '10',
                            max: '10',
                            min: '10',
                            sum: '10',
                        },
                    },
                },
            ]);

            results = (
                await queryClient.queryRemote<TestTargetItem>(context, transform, {
                    filters: [{ code: { _in: ['ITEM-01', 'ITEM-02', 'ITEM-03'] } }],
                    orderBy: { code: -1 },
                    aggregate: {
                        groups: [{ path: ['code'], groupedBy: 'value' }],
                        values: [],
                    },
                })
            ).payload;

            assert.deepEqual(results, [
                {
                    group: {
                        code: 'ITEM-03',
                    },
                },
                {
                    group: {
                        code: 'ITEM-02',
                    },
                },
                {
                    group: {
                        code: 'ITEM-01',
                    },
                },
            ]);
        });
    });

    it('Can query external node ', async () => {
        await Test.withContext(async context => {
            const results = await context
                .query(TestExternalDocument, {
                    filter: { number: { _in: ['DOC-01', 'DOC-02', 'DOC-03'] } },
                })
                .map(async result => {
                    const p = await result.$.payload({ withNaturalKeyWhenThunk: true });
                    // _id is typed to number so cast to any
                    return p as any;
                })
                .toArray();
            assert.equal(
                JSON.stringify(results),
                JSON.stringify([
                    {
                        number: 'DOC-01',
                        description: 'DOC-01 for function',
                        status: 'start',
                        date: new DateValue(20231207),
                        lines: [
                            {
                                _sortValue: 100,
                                document: { number: 'DOC-01' },
                                item: { code: 'ITEM-01' },
                                quantity: '1',
                            },
                        ],
                    },
                    {
                        number: 'DOC-02',
                        description: '',
                        status: 'stop',
                        date: new DateValue(20231207),
                        lines: [
                            {
                                _sortValue: 100,
                                document: { number: 'DOC-02' },
                                item: { code: 'ITEM-01' },
                                quantity: '2',
                            },
                        ],
                    },
                    {
                        number: 'DOC-03',
                        description: 'customText-03',
                        status: 'inProgress',
                        date: new DateValue(20231211),
                        lines: [
                            {
                                _sortValue: 100,
                                document: { number: 'DOC-03' },
                                item: { code: 'ITEM-01' },
                                quantity: '3',
                            },
                        ],
                    },
                ]),
            );
        });
    });

    it('Can query aggregate external node ', async () => {
        await Test.withContext(async context => {
            const results = await context
                .queryAggregate(TestExternalDocument, {
                    filter: { number: { _in: ['DOC-01', 'DOC-02', 'DOC-03'] } },
                    group: { status: { _by: 'value' } },
                    values: { status: { distinctCount: true } },
                })
                .toArray();
            assert.equal(
                JSON.stringify(results),
                JSON.stringify([
                    { group: { status: 'begin' }, values: { status: { distinctCount: 1 } } },
                    { group: { status: 'end' }, values: { status: { distinctCount: 1 } } },
                    { group: { status: 'inProgress' }, values: { status: { distinctCount: 1 } } },
                ]),
            );
        });
    });
});
