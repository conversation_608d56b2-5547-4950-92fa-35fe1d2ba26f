Can create a transform with valid mappings:
  executionMode: normal
  envConfigs:
    testActiveServiceOptions: ['synchronizationServiceOption']
  input:
    properties:
      id: TEST
      localNode: '#TestTargetDocument'
      remoteApp: '#test'
      remoteAppVersion: '1.0.0'
      remoteNodeName: 'TestSourceDocument'
      map:
        [
          { 'localProperty': 'number', 'kind': 'path_to_path', 'remoteProperty': 'id' },
          { 'localProperty': 'lines', 'kind': 'path_to_path', 'remoteProperty': 'lines' },
        ]
  output:
    id: TEST
    isActive: true
