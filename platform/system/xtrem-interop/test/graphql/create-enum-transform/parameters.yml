Can create a enum transform with valid mappings:
  executionMode: normal
  envConfigs:
    testActiveServiceOptions: ['synchronizationServiceOption']
  input:
    properties:
      localEnum: '#testRemoteStatus'
      remoteApp: '#test'
      remoteAppVersion: '2.0.0'
      remoteEnum: 'TestSourceDocument'
      map:
        [
          { 'localEnumValue': 'begin', 'remoteEnumValue': 'start' },
          { 'localEnumValue': 'end', 'remoteEnumValue': 'stop' },
          { 'localEnumValue': 'inProgress', 'remoteEnumValue': 'inProgress' },
          { 'localEnumValue': 'inProgress', 'remoteEnumValue': 'track' },
        ]
  output:
    localEnum:
      name: testRemoteStatus
    isActive: true
    map:
      query:
        edges:
          [
            { 'node': { 'localEnumValue': 'begin', 'remoteEnumValue': 'start' } },
            { 'node': { 'localEnumValue': 'end', 'remoteEnumValue': 'stop' } },
            { 'node': { 'localEnumValue': 'inProgress', 'remoteEnumValue': 'inProgress' } },
            { 'node': { 'localEnumValue': 'inProgress', 'remoteEnumValue': 'track' } },
          ]
