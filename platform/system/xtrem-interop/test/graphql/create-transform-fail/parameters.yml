Throws if transform references an app which is not registered:
  executionMode: normal
  envConfigs:
    testActiveServiceOptions: ['synchronizationServiceOption']
  input:
    properties:
      id: TEST
      localNode: '#TestTargetDocument'
      remoteApp: '#unknown'
      remoteAppVersion: '1.0.0'
      remoteNodeName: 'TestSourceDocument'
      map: []
  output:
    errors:
      - message: 'The record was not created.'
        extensions:
          code: operation-error
          diagnoses:
            - message: 'SysApp: record not found: {"_id":"#unknown"}'
              path: []
              severity: 4
        locations: [{ column: 13, line: 4 }]
        path: ['xtremInterop', 'sysNodeTransformation', 'create']

Throws if transform has mappings on a non vital reference, outside of the natural key:
  executionMode: normal
  envConfigs:
    testActiveServiceOptions: ['synchronizationServiceOption']
  input:
    properties:
      id: TEST
      localNode: '#TestTargetDocumentLine'
      remoteApp: '#test'
      remoteAppVersion: '1.0.0'
      remoteNodeName: 'TestSourceDocumentLine'
      map:
        [
          { 'localProperty': 'item.code', 'kind': 'path_to_path', 'remoteProperty': 'item.code' },
          { 'localProperty': 'item.name', 'kind': 'path_to_path', 'remoteProperty': 'item.name' },
        ]
  output:
    errors:
      - extensions:
          code: operation-error
          diagnoses:
            - message: 'TestTargetItem: TestTargetItem.name : property not found'
              path: []
              severity: 4
        message: 'The record was not created.'
        locations: [{ column: 13, line: 4 }]
        path: ['xtremInterop', 'sysNodeTransformation', 'create']

Throws if transform does not have mappings on a vital reference:
  executionMode: normal
  envConfigs:
    testActiveServiceOptions: ['synchronizationServiceOption']
  input:
    properties:
      id: TEST
      localNode: '#TestTargetDocument'
      remoteApp: '#test'
      remoteAppVersion: '1.0.0'
      remoteNodeName: 'TestSourceDocument'
      map: [{ 'localProperty': 'address', 'kind': 'path_to_path', 'remoteProperty': 'address' }]
  output:
    errors:
      - extensions:
          code: operation-error
          diagnoses:
            - message: 'TestTargetDocumentAddress: no transform found for node'
              path: []
              severity: 4
        message: 'The record was not created.'
        locations: [{ column: 13, line: 4 }]
        path: ['xtremInterop', 'sysNodeTransformation', 'create']

Throws if transform has mappings on a non vital collection:
  executionMode: normal
  envConfigs:
    testActiveServiceOptions: ['synchronizationServiceOption']
  input:
    properties:
      id: TEST
      localNode: '#TestTargetDocument'
      remoteApp: '#test'
      remoteAppVersion: '1.0.0'
      remoteNodeName: 'TestSourceDocument'
      map: [{ 'localProperty': 'allAddresses..city', 'kind': 'path_to_path', 'remoteProperty': 'address.city' }]
  output:
    errors:
      - extensions:
          code: operation-error
          diagnoses:
            - message: 'Invalid transform mapping: cannot map to properties of a collection, TestTargetDocumentAddress should have its own mapping.'
              path: []
              severity: 3
            - message: 'Different number of arrays in key allAddresses..city and value address.city'
              path: ['mappings']
              severity: 3
        message: 'The record was not created.'
        locations: [{ column: 13, line: 4 }]
        path: ['xtremInterop', 'sysNodeTransformation', 'create']

Throws if transform does not have mappings on a vital collection:
  executionMode: normal
  envConfigs:
    testActiveServiceOptions: ['synchronizationServiceOption']
  input:
    properties:
      id: TEST
      localNode: '#TestTargetDocument'
      remoteApp: '#test'
      remoteAppVersion: '1.0.0'
      remoteNodeName: 'TestSourceDocument'
      map: [{ 'localProperty': 'lines..', 'kind': 'path_to_path', 'remoteProperty': 'lines..' }]
  output:
    errors:
      - extensions:
          code: operation-error
          diagnoses:
            - message: 'Invalid transform mapping: cannot map to properties of a collection, TestTargetDocumentLine should have its own mapping.'
              path: []
              severity: 3
            - message: 'Missing property name in lines..'
              path: ['mappings']
              severity: 3
        message: 'The record was not created.'
        locations: [{ column: 13, line: 4 }]
        path: ['xtremInterop', 'sysNodeTransformation', 'create']
