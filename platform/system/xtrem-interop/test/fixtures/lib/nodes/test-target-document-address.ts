import { decorators, Node, Reference } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import * as test from '../../test-application';

@decorators.node<TestTargetDocumentAddress>({
    storage: 'sql',
    isPublished: true,
    isVitalReferenceChild: true,
})
export class TestTargetDocumentAddress extends Node {
    @decorators.referenceProperty<TestTargetDocumentAddress, 'document'>({
        isVitalParent: true,
        isStored: true,
        isPublished: true,
        node: () => test.nodes.TestTargetDocument,
    })
    readonly document: Reference<test.nodes.TestTargetDocument>;

    @decorators.stringProperty<TestTargetDocumentAddress, 'city'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.name,
    })
    readonly city: Promise<string>;

    @decorators.stringProperty<TestTargetDocumentAddress, 'country'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.name,
    })
    readonly country: Promise<string>;
}
