import { decimal, decorators, Node, Reference } from '@sage/xtrem-core';
import * as test from '../../test-application';

@decorators.node<TestTargetDocumentLine>({
    storage: 'sql',
    isPublished: true,
    isVitalCollectionChild: true,
})
export class TestTargetDocumentLine extends Node {
    @decorators.referenceProperty<TestTargetDocumentLine, 'document'>({
        isVitalParent: true,
        isStored: true,
        isPublished: true,
        node: () => test.nodes.TestTargetDocument,
    })
    readonly document: Reference<test.nodes.TestTargetDocument>;

    @decorators.referenceProperty<TestTargetDocumentLine, 'item'>({
        isStored: true,
        isPublished: true,
        node: () => test.nodes.TestTargetItem,
    })
    readonly item: Reference<test.nodes.TestTargetItem>;

    @decorators.decimalProperty<TestTargetDocumentLine, 'quantity'>({
        isStored: true,
        isPublished: true,
        dataType: () => test.dataTypes.quantity,
    })
    readonly quantity: Promise<decimal>;
}
