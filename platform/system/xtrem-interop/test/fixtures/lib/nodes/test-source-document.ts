import { Collection, date, decorators, Node } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import * as test from '../../test-application';

@decorators.node<TestSourceDocument>({
    storage: 'sql',
    isPublished: true,
    isSynchronizable: true,
    indexes: [{ orderBy: { id: 1 }, isUnique: true, isNaturalKey: true }],
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canRead: true,
})
export class TestSourceDocument extends Node {
    @decorators.stringProperty<TestSourceDocument, 'id'>({
        isStored: true,
        isPublished: true,
        dataType: () => test.dataTypes._id,
    })
    readonly id: Promise<string>;

    @decorators.stringProperty<TestSourceDocument, 'description'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.description,
    })
    readonly description: Promise<string>;

    @decorators.enumProperty<TestSourceDocument, 'status'>({
        isStored: true,
        isPublished: true,
        dataType: () => test.enums.testRemoteStatus,
    })
    readonly status: Promise<test.enums.TestRemoteStatus>;

    @decorators.dateProperty<TestSourceDocument, 'date'>({
        isStored: true,
        isPublished: true,
    })
    readonly date: Promise<date>;

    @decorators.collectionProperty<TestSourceDocument, 'lines'>({
        isPublished: true,
        isVital: true,
        node: () => test.nodes.TestSourceDocumentLine,
        reverseReference: 'document',
    })
    readonly lines: Collection<test.nodes.TestSourceDocumentLine>;
}
