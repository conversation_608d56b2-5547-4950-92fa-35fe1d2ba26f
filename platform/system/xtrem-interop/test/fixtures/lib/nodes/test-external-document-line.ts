import { decimal, decorators, Node, Reference } from '@sage/xtrem-core';
import { InteropStorageManager } from '../../../../lib/classes/interop-storage-manager';
import * as test from '../../test-application';

@decorators.node<TestExternalDocumentLine>({
    storage: 'external',
    isPublished: true,
    externalStorageManager: new InteropStorageManager(),
    isVitalCollectionChild: true,
    canRead: true,
})
export class TestExternalDocumentLine extends Node {
    @decorators.referenceProperty<TestExternalDocumentLine, 'document'>({
        isVitalParent: true,
        isStored: true,
        isPublished: true,
        node: () => test.nodes.TestExternalDocument,
    })
    readonly document: Reference<test.nodes.TestExternalDocument>;

    @decorators.referenceProperty<TestExternalDocumentLine, 'item'>({
        isStored: true,
        isPublished: true,
        node: () => test.nodes.TestTargetItem,
    })
    readonly item: Reference<test.nodes.TestTargetItem>;

    @decorators.decimalProperty<TestExternalDocumentLine, 'quantity'>({
        isStored: true,
        isPublished: true,
        dataType: () => test.dataTypes.quantity,
    })
    readonly quantity: Promise<decimal>;
}
