import { Context, decorators, Node } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';

@decorators.node<TestRemoteMutation>({
    storage: 'json',
    isPublished: true,
})
export class TestRemoteMutation extends Node {
    @decorators.mutation<typeof TestRemoteMutation, 'remoteMutate'>({
        isPublished: true,
        parameters: [
            {
                name: 'remoteP1',
                type: 'integer',
                isMandatory: true,
            },
            {
                name: 'remoteP2',
                type: 'string',
                dataType: () => xtremSystem.dataTypes.code,
            },
        ],
        return: {
            type: 'object',
            properties: {
                remoteR1: {
                    type: 'integer',
                },
                remoteR2: {
                    type: 'string',
                },
            },
        },
    })
    static remoteMutate(
        _context: Context,
        remoteP1: number,
        remoteP2: string,
    ): Promise<{ remoteR1: number; remoteR2: string }> {
        return Promise.resolve({
            remoteR1: 2 * remoteP1,
            remoteR2: `--- ${remoteP2} ---`,
        });
    }

    @decorators.mutation<typeof TestRemoteMutation, 'remoteComplexMutate'>({
        isPublished: true,
        parameters: [
            {
                name: 'outerObject',
                type: 'object',
                properties: {
                    innerObject: {
                        type: 'object',
                        properties: {
                            remoteP1: { type: 'integer' },
                            remoteP2: { type: 'string' },
                            nums: {
                                type: 'array',
                                item: {
                                    type: 'integer',
                                },
                            },
                        },
                    },
                },
            },
        ],
        return: {
            type: 'object',
            properties: {
                remoteR1: {
                    type: 'integer',
                },
                remoteR2: {
                    type: 'string',
                },
                num: {
                    type: 'integer',
                },
            },
        },
    })
    static remoteComplexMutate(
        _context: Context,
        localObject: { innerObject: { remoteP1: number; remoteP2: string; nums: number[] } },
    ): Promise<{ remoteR1: number; remoteR2: string; num: number }> {
        return Promise.resolve({
            remoteR1: 2 * localObject.innerObject.remoteP1,
            remoteR2: `--- ${localObject.innerObject.remoteP2} ---`,
            num: localObject.innerObject.nums.reduce((acc, val) => acc + val, 0),
        });
    }

    @decorators.mutation<typeof TestRemoteMutation, 'remoteSimpleMutate'>({
        isPublished: true,
        isInterop: true,
        parameters: [
            {
                name: 'remoteP1',
                type: 'integer',
                isMandatory: true,
            },
            {
                name: 'remoteP2',
                type: 'integer',
                dataType: () => xtremSystem.dataTypes.code,
            },
        ],
        return: 'integer',
    })
    static remoteSimpleMutate(context: Context, remoteP1: number, remoteP2: number): Promise<number> {
        return Promise.resolve(remoteP1 + remoteP2);
    }
}
