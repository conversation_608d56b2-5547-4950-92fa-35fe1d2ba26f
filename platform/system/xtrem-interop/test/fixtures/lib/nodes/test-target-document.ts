import { Collection, date, decorators, Node, Reference } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import * as test from '../../test-application';

@decorators.node<TestTargetDocument>({
    storage: 'sql',
    isPublished: true,
    isSynchronized: true,
    canDeleteMany: true,
    indexes: [{ orderBy: { number: 1 }, isUnique: true, isNaturalKey: true }],
})
export class TestTargetDocument extends Node {
    @decorators.stringProperty<TestTargetDocument, 'number'>({
        isStored: true,
        isPublished: true,
        dataType: () => test.dataTypes._id,
    })
    readonly number: Promise<string>;

    @decorators.stringProperty<TestTargetDocument, 'description'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.description,
    })
    readonly description: Promise<string>;

    @decorators.enumProperty<TestTargetDocument, 'status'>({
        isStored: true,
        isPublished: true,
        dataType: () => test.enums.testLocalStatus,
    })
    readonly status: Promise<test.enums.TestLocalStatus>;

    @decorators.dateProperty<TestTargetDocument, 'date'>({
        isStored: true,
        isPublished: true,
    })
    readonly date: Promise<date>;

    @decorators.collectionProperty<TestTargetDocument, 'lines'>({
        isPublished: true,
        isVital: true,
        node: () => test.nodes.TestTargetDocumentLine,
        reverseReference: 'document',
    })
    readonly lines: Collection<test.nodes.TestTargetDocumentLine>;

    @decorators.referenceProperty<TestTargetDocument, 'address'>({
        isPublished: true,
        isVital: true,
        node: () => test.nodes.TestTargetDocumentAddress,
        reverseReference: 'document',
        isFrozen: true,
    })
    readonly address: Reference<test.nodes.TestTargetDocumentAddress>;

    // Non vital collection
    @decorators.collectionProperty<TestTargetDocument, 'allAddresses'>({
        isPublished: true,
        node: () => test.nodes.TestTargetDocumentAddress,
        reverseReference: 'document',
    })
    readonly allAddresses: Collection<test.nodes.TestTargetDocumentAddress>;
}
