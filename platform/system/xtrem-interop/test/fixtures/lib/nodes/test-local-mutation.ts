import { Context, decorators, LogicError, Node } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const use = (..._args: any[]) => {};

@decorators.node<TestLocalMutation>({
    storage: 'json',
    isPublished: true,
})
export class TestLocalMutation extends Node {
    @decorators.mutation<typeof TestLocalMutation, 'localMutate'>({
        isPublished: true,
        isInterop: true,
        parameters: [
            {
                name: 'localP1',
                type: 'integer',
                isMandatory: true,
            },
            {
                name: 'localP2',
                type: 'string',
                dataType: () => xtremSystem.dataTypes.code,
            },
        ],
        return: {
            type: 'object',
            properties: {
                localR1: {
                    type: 'integer',
                },
                localR2: {
                    type: 'string',
                },
            },
        },
    })
    static localMutate(
        context: Context,
        localP1: number,
        localP2: string,
    ): Promise<{ localR1: number; localR2: string }> {
        use(context, localP1, localP2);
        throw new LogicError('mutation mapping not found');
    }

    @decorators.mutation<typeof TestLocalMutation, 'localComplexMutate'>({
        isPublished: true,
        isInterop: true,
        parameters: [
            {
                name: 'params',
                type: 'object',
                properties: { localP1: { type: 'integer' }, localP2: { type: 'string' } },
            },
        ],
        return: {
            type: 'object',
            properties: {
                innerObject: {
                    type: 'object',
                    properties: {
                        localR1: { type: 'integer' },
                        localR2: { type: 'string' },
                        sum: { type: 'integer' },
                    },
                },
            },
        },
    })
    static localComplexMutate(
        context: Context,
        params: { localP1: number; localP2: string },
    ): Promise<{ innerObject: { localR1: number; localR2: string; sum: number } }> {
        use(context, params);
        throw new LogicError('mutation mapping not found');
    }

    @decorators.mutation<typeof TestLocalMutation, 'localSimpleMutate'>({
        isPublished: true,
        isInterop: true,
        parameters: [
            {
                name: 'localP1',
                type: 'integer',
                isMandatory: true,
            },
            {
                name: 'localP2',
                type: 'integer',
                dataType: () => xtremSystem.dataTypes.code,
            },
        ],
        return: 'integer',
    })
    static localSimpleMutate(context: Context, localP1: number, localP2: number): Promise<number> {
        use(context, localP1, localP2);
        throw new LogicError('mutation mapping not found');
    }
}
