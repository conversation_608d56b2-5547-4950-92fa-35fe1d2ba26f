import { Context, decimal, decorators, LogicError, Node } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import * as test from '../../test-application';

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const use = (..._args: any[]) => {};

@decorators.node<TestTargetItem>({
    storage: 'sql',
    isPublished: true,
    isSynchronized: true,
    canDeleteMany: true,
    indexes: [{ orderBy: { code: 1 }, isUnique: true, isNaturalKey: true }],
})
export class TestTargetItem extends Node {
    @decorators.stringProperty<TestTargetItem, 'code'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.code,
    })
    readonly code: Promise<string>;

    @decorators.decimalProperty<TestTargetItem, 'unitPrice'>({
        isStored: true,
        isPublished: true,
        dataType: () => test.dataTypes.price,
    })
    readonly unitPrice: Promise<decimal>;

    @decorators.mutation<typeof TestTargetItem, 'getRemotePrice'>({
        isPublished: true,
        isInterop: true,
        parameters: [
            {
                name: 'localP1',
                type: 'object',
                properties: { item: { type: 'reference', node: () => TestTargetItem } },
            },
        ],
        return: 'integer',
    })
    static getRemotePrice(context: Context, localP1: { item: TestTargetItem }): Promise<number> {
        use(context, localP1);
        throw new LogicError('mutation mapping not found');
    }

    @decorators.mutation<typeof TestTargetItem, 'getItem'>({
        isPublished: true,
        isInterop: true,
        parameters: [
            {
                name: 'item',
                type: 'instance',
                node: () => TestTargetItem,
            },
        ],
        return: {
            type: 'instance',
            node: () => TestTargetItem,
        },
    })
    static getItem(context: Context, item: TestTargetItem): Promise<TestTargetItem> {
        use(context, item);
        throw new LogicError('mutation mapping not found');
    }
}
