import { Collection, date, decorators, Node } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import { InteropStorageManager } from '../../../../lib/classes/interop-storage-manager';
import * as test from '../../test-application';

@decorators.node<TestExternalDocument>({
    storage: 'external',
    isPublished: true,
    externalStorageManager: new InteropStorageManager(),
    indexes: [{ orderBy: { number: 1 }, isUnique: true, isNaturalKey: true }],
    canRead: true,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
})
export class TestExternalDocument extends Node {
    @decorators.stringProperty<TestExternalDocument, 'number'>({
        isStored: true,
        isPublished: true,
        dataType: () => test.dataTypes._id,
    })
    readonly number: Promise<string>;

    @decorators.stringProperty<TestExternalDocument, 'description'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.description,
    })
    readonly description: Promise<string>;

    @decorators.enumProperty<TestExternalDocument, 'status'>({
        isStored: true,
        isPublished: true,
        dataType: () => test.enums.testLocalStatus,
    })
    readonly status: Promise<test.enums.TestLocalStatus>;

    @decorators.dateProperty<TestExternalDocument, 'date'>({
        isStored: true,
        isPublished: true,
    })
    readonly date: Promise<date>;

    @decorators.collectionProperty<TestExternalDocument, 'lines'>({
        isPublished: true,
        isVital: true,
        node: () => test.nodes.TestExternalDocumentLine,
        reverseReference: 'document',
    })
    readonly lines: Collection<test.nodes.TestExternalDocumentLine>;
}
