import { Context, decimal, decorators, Node } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import * as test from '../../test-application';

@decorators.node<TestSourceItem>({
    storage: 'sql',
    isPublished: true,
    isSynchronizable: true,
    indexes: [{ orderBy: { code: +1 }, isUnique: true, isNaturalKey: true }],
})
export class TestSourceItem extends Node {
    @decorators.stringProperty<TestSourceItem, 'code'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.code,
    })
    readonly code: Promise<string>;

    @decorators.decimalProperty<TestSourceItem, 'price'>({
        isStored: true,
        isPublished: true,
        dataType: () => test.dataTypes.price,
    })
    readonly price: Promise<decimal>;

    @decorators.mutation<typeof TestSourceItem, 'getPrice'>({
        isPublished: true,
        isInterop: true,
        parameters: [
            {
                name: 'remoteP1',
                type: 'object',
                properties: { item: { type: 'reference', node: () => TestSourceItem } },
            },
        ],
        return: 'integer',
    })
    static getPrice(_context: Context, remoteP1: { item: TestSourceItem }): Promise<number> {
        return remoteP1.item.price;
    }

    @decorators.mutation<typeof TestSourceItem, 'getItem'>({
        isPublished: true,
        isInterop: true,
        parameters: [
            {
                name: 'itemCode',
                type: 'string',
            },
        ],
        return: {
            type: 'instance',
            node: () => TestSourceItem,
        },
    })
    static getItem(context: Context, itemCode: string): Promise<TestSourceItem> {
        return context.read(TestSourceItem, { code: itemCode });
    }
}
