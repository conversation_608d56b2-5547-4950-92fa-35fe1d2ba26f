import { decimal, decorators, Node, Reference } from '@sage/xtrem-core';
import * as test from '../../test-application';

@decorators.node<TestSourceDocumentLine>({
    storage: 'sql',
    isPublished: true,
    isVitalCollectionChild: true,
})
export class TestSourceDocumentLine extends Node {
    @decorators.referenceProperty<TestSourceDocumentLine, 'document'>({
        isVitalParent: true,
        isStored: true,
        isPublished: true,
        node: () => test.nodes.TestSourceDocument,
    })
    readonly document: Reference<test.nodes.TestSourceDocument>;

    @decorators.referenceProperty<TestSourceDocumentLine, 'item'>({
        isStored: true,
        isPublished: true,
        node: () => test.nodes.TestSourceItem,
    })
    readonly item: Reference<test.nodes.TestSourceItem>;

    @decorators.decimalProperty<TestSourceDocumentLine, 'quantity'>({
        isStored: true,
        isPublished: true,
        dataType: () => test.dataTypes.quantity,
        async control(cx, value) {
            await cx.error.if(value).is.less.than(0);
        },
    })
    readonly quantity: Promise<decimal>;
}
