import sinon = require('sinon');
import { InteropAppHealthMonitor, Test } from '@sage/xtrem-core';
import { startService } from '../../lib';
import { InteropClient } from '../../lib/classes/interop-client';
import { SysSynchronizationClient } from '../../lib/classes/sys-synchronization-client';
import { graphqlSetup } from './setup';

export async function beforeTest(): Promise<void> {
    startService(Test.application);

    const graphqlHelper = await graphqlSetup({ application: Test.application });
    sinon
        .stub(InteropClient.prototype, 'fetch')
        .callsFake(async (_context, query) => ({ data: await graphqlHelper.executeAny(query) }) as any);
    sinon
        .stub(SysSynchronizationClient.prototype, 'fetchStream')
        .callsFake(async (_context, query) => ({ data: await graphqlHelper.executeAny(query) }) as any);

    const rootAbout = Test.application.rootAbout;
    sinon
        .stub(Test.application, 'rootAbout')
        .get(() => ({ ...rootAbout, appName: 'test', version: '1.0.0', metadataPackage: 'xtrem-metadata' }));

    // Stub the isAppAlive method to always return true for the 'test' app or false for any other app
    sinon
        .stub(InteropAppHealthMonitor, 'isAppAlive')
        .callsFake((appName: string) => Promise.resolve(appName === 'test'));
}
