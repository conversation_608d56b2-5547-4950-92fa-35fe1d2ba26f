{"parameterMappings": {"outerObject.innerObject.remoteP1": {"kind": "path", "value": "params.localP1"}, "outerObject.innerObject.remoteP2": {"kind": "path", "value": "params.localP2"}, "outerObject.innerObject.nums": {"kind": "constant", "value": [1, 2, 3]}}, "returnMappings": {"innerObject.localR1": {"kind": "path", "value": "remoteR1"}, "innerObject.localR2": {"kind": "path", "value": "remoteR2"}, "innerObject.sum": {"kind": "path", "value": "num"}}}