# see https://confluence.sage.com/display/X3ST/SonarQube for more informations

# sonar server credentials. If local https://localhost:9000
sonar.host.url=<your-sonar-url>

# sonar server credentials.  
# sonar.login=<sonar-user-api-token>
  
# Must be unique in a given SonarQube instance. example : @sage/x3-finance-common
sonar.projectKey=<add-project-key-here>
    
# Path is relative to the sonar-project.properties file. Replace "" by "/" on Windows.
sonar.sources=lib
  
  
#send code coverage to sonarqube. 
sonar.typescript.lcov.reportPaths=coverage/lcov.info
  
#exclude Test Files.
#sonar.exclusions= */**/*.test.ts, test/, */**/*config.ts
  