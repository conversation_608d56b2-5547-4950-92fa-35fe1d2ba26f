{"@sage/xtrem-metadata": [{"topic": "MetaActivity/asyncExport/start", "queue": "import-export", "sourceFileName": "meta-activity.ts"}, {"topic": "MetaActivityPermission/asyncExport/start", "queue": "import-export", "sourceFileName": "meta-activity-permission.ts"}, {"topic": "MetaDataType/asyncExport/start", "queue": "import-export", "sourceFileName": "meta-data-type.ts"}, {"topic": "MetaNodeFactory/asyncExport/start", "queue": "import-export", "sourceFileName": "meta-node-factory.ts"}, {"topic": "MetaNodeOperation/asyncExport/start", "queue": "import-export", "sourceFileName": "meta-node-operation.ts"}, {"topic": "MetaNodeProperty/asyncExport/start", "queue": "import-export", "sourceFileName": "meta-node-property.ts"}, {"topic": "MetaPackage/asyncExport/start", "queue": "import-export", "sourceFileName": "meta-package.ts"}, {"topic": "MetaServiceOption/asyncExport/start", "queue": "import-export", "sourceFileName": "meta-service-option.ts"}]}