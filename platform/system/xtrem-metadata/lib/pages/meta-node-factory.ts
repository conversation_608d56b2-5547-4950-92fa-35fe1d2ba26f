import { Filter } from '@sage/xtrem-client';
import {
    Graph<PERSON><PERSON>,
    MetaNodeFactory as MetaNodeFactoryNode,
    MetaNodeOperation,
    MetaPackage,
} from '@sage/xtrem-metadata-api';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.page<MetaNodeFactory>({
    title: 'Node factory',
    objectTypeSingular: 'Node factory',
    objectTypePlural: 'Node factories',
    mode: 'tabs',
    priority: 100,
    node: '@sage/xtrem-metadata/MetaNodeFactory',
    navigationPanel: {
        listItem: {
            line2: ui.nestedFields.text({
                bind: { package: { name: true } },
                title: 'Package',
            }),
            title: ui.nestedFields.text({
                bind: 'name',
                title: 'Name',
            }),
        },
        optionsMenu: [
            {
                title: 'Can schedule',
                graphQLFilter: (): Filter<MetaNodeFactoryNode> => {
                    return { operations: { _atLeast: 1, isSchedulable: true } };
                },
            },
            {
                title: 'Can execute',
                graphQLFilter: (): Filter<MetaNodeFactoryNode> => {
                    return { operations: { _atLeast: 1, isSchedulable: false, kind: 'asyncMutation' } };
                },
            },
            {
                title: 'All nodes with operations',
                graphQLFilter: (): Filter<MetaNodeFactoryNode> => {
                    return { operations: { _atLeast: 1 } };
                },
            },
            {
                title: 'All nodes',
                graphQLFilter: {},
            },
            {
                title: 'Abstract nodes',
                graphQLFilter: (): Filter<MetaNodeFactoryNode> => {
                    return { isAbstract: true };
                },
            },
        ],
    },
})
export class MetaNodeFactory extends ui.Page<GraphApi> {
    @ui.decorators.section<MetaNodeFactory>({
        title: 'General',
        isTitleHidden: true,
    })
    generalSection: ui.containers.Section;

    @ui.decorators.block<MetaNodeFactory>({
        parent() {
            return this.generalSection;
        },
    })
    generalBlock: ui.containers.Block;

    @ui.decorators.textField<MetaNodeFactory>({
        parent() {
            return this.generalBlock;
        },
        title: 'Name',
        width: 'medium',
        isReadOnly: true,
    })
    name: ui.fields.Text;

    @ui.decorators.switchField<MetaNodeFactory>({
        parent() {
            return this.generalBlock;
        },
        title: 'Active',
        width: 'small',
        isReadOnly: true,
    })
    isActive: ui.fields.Switch;

    @ui.decorators.separatorField<MetaNodeFactory>({
        parent() {
            return this.generalBlock;
        },
        isFullWidth: true,
    })
    separator: ui.fields.Separator;

    @ui.decorators.referenceField<MetaNodeFactory>({
        parent() {
            return this.generalBlock;
        },
        valueField: 'name',
        node: '@sage/xtrem-metadata/MetaPackage',
        title: 'Package',
        width: 'large',
        isReadOnly: true,
    })
    package: ui.fields.Reference<MetaPackage>;

    @ui.decorators.checkboxField<MetaNodeFactory>({
        parent() {
            return this.generalBlock;
        },
        title: 'Published',
        isReadOnly: true,
    })
    isPublished: ui.fields.Checkbox;

    @ui.decorators.checkboxField<MetaNodeFactory>({
        parent() {
            return this.generalBlock;
        },
        title: 'Abstract',
        isReadOnly: true,
    })
    isAbstract: ui.fields.Checkbox;

    @ui.decorators.textField<MetaNodeFactory>({
        parent() {
            return this.generalBlock;
        },
        bind: { extends: { name: true } },
        title: 'Extends',
        isReadOnly: true,
    })
    extends: ui.fields.Text;

    @ui.decorators.tableField<MetaNodeFactory, MetaNodeOperation>({
        canSelect: false,
        isReadOnly: true,
        node: '@sage/xtrem-metadata/MetaNodeOperation',
        title: 'Operations',
        parent() {
            return this.generalSection;
        },
        columns: [
            ui.nestedFields.text({ bind: 'name', title: 'Name' }),
            ui.nestedFields.text({ bind: 'kind', title: 'Kind' }),
            ui.nestedFields.text({ bind: 'action', title: 'Action' }),
        ],
    })
    operations: ui.fields.Table<MetaNodeOperation>;
}
