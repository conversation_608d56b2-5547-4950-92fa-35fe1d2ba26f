import { Application, Node, StaticThis } from '@sage/xtrem-core';
import * as xtremMetadata from '../index';
import { MetadataUpgrade } from './metadata-upgrade';

export const metadataManager = {
    /**
     * Synchronize the metadata to the database
     */
    upgradeMetadata(application: Application, options?: { fullReload?: boolean; onlyCreate?: boolean }): Promise<void> {
        return new MetadataUpgrade(application).upgrade(options);
    },

    getMetaNodeFactoryConstructor(): StaticThis<Node> | null {
        return xtremMetadata.nodes.MetaNodeFactory;
    },
};
