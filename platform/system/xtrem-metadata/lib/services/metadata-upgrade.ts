/**
 * This module implements the upgrade process for metadata.
 * This upgrade is performed after schema creation (to get the initial metadata), and at the end of every schema upgrade.
 *
 * It takes its input form the Application object and its sub objects (NodeFactory, Property, ...), and it updates
 * the contents of the meta_... tables (Meta... nodes).
 *
 * First, its reads digest information (_id + natural key + isActive + checksum) from all the meta_ tables.
 *
 * Then it scans the Application object and its sub objects. It compares them to the digests collected earlier, using
 * the natural key to match them.
 *
 * If the natural key of the new element does not match any existing digest, it inserts it.
 * If it matches but the digest does not match, it updates the existing record with the new element.
 * At the end it checks _ids that existed before (found in the digests) and don't exist any more, and its marks these
 * elements as isActive: false.
 *
 * The insert process is somewhat tricky because we need the _ids of all the non nullable references to insert.
 * We do it in two passes. In the first pass we insert the records with their nullable references set to null, and
 * we add their new digests to the artifact.digests dict.
 * In a second pass we patch all the records that have non null values in at least one of their nullable references.
 *
 * Metadata elements are not deleted because they may be referenced from other nodes. Instead we mark them as inactive.
 * We'll need post-metadata-upgrade actions to deal with these cases (automatically find a replacement for an inactive
 * reference, or just alert the tenant admin that something must be reconfigured).
 */
import {
    AnyRecord,
    AnyValue,
    Application,
    asyncArray,
    basicProfiler,
    DataType,
    Dict,
    EnumDataType,
    EnumSqlContext,
    friendlyOperationSignature,
    integer,
    Logger,
    Node,
    NodeFactory,
    PlainOperationDecorator,
    Property,
    ReadTableSqlContext,
    ServiceOption,
    StaticThis,
} from '@sage/xtrem-core';
import * as i18n from '@sage/xtrem-i18n';
import { localizedText } from '@sage/xtrem-i18n';
import { createDictionary, LogicError, supportedLocales } from '@sage/xtrem-shared';
import * as crypto from 'crypto';
import * as _ from 'lodash';
import { parse as parsePgArray } from 'postgres-array';
import * as xtremMetadata from '../index';

const notNull = <T>(x: T): NonNullable<T> => x as NonNullable<T>;

const logger = new Logger(__filename, 'upgrade');

/** The digest of a metadata record */
interface Digest {
    // fields read from the meta table
    _id: integer;
    checksum: string;
    isActive: boolean;

    // _naturalKey is computed from the natural key properties
    _naturalKey: string;

    // _foundInPayoad is set during the insert pass, on all the digests for which we have a matching payload.
    _foundInPayloads: boolean;

    // _inserted is set if the payload was inserted into the meta table.
    _inserted: boolean;

    [K: string]: AnyValue;
}

/** Interface that all the Meta... nodes must implemnent */
export interface MetaNode extends Node {
    readonly checksum: Promise<string>;
    readonly isActive: Promise<boolean>;
}

type Payload = Dict<AnyValue>;

/** Counters that we gather during the upgrade and that we log at the end */
interface ArtifactCounters {
    /** number of new records inserted for this artifact */
    inserted: integer;
    /** number of records that were partially updated with nullable reference values after insert */
    updatedAfterInsert: integer;
    /** number of records that existed before and have been updated */
    updated: integer;
    /** number of records that have been marked as deleted because they existed before and don't any more */
    markedDeleted: integer;
}

/** An Artifact is a structure that gathers the information for a given meta-node (MetaNodeFactory, MetaNodeProperty, ...) */
interface Artifact {
    /** The name of the node constructor ('MetaNodeFactory', 'MetaNodeProperty', ...) */
    name: string;
    /** The node factory for the meta-node */
    metaFactory: NodeFactory;
    /** The list of names of simple (non reference, non collection) properties of the meta-node */
    simplePropertyNames: string[];
    /**  The list of names of reference properties of the meta-node*/
    referenceNames: string[];
    /**  The list of names of non-nullable reference properties of the meta-node*/
    nonNullableReferenceNames: string[];
    /**  The list of names of nullable reference properties of the meta-node*/
    nullableReferenceNames: string[];
    /** The list of property names for insert */
    insertPropertyNames: string[];

    /** The digests that we read from the table, or that we added during the insert pass */
    digestsById: Dict<Digest>;
    digestsByNaturalKey: Dict<Digest>;

    /** The payloads that we collected when scanning the application object */
    payloads: Payload[];
    /** The counters */
    counters: ArtifactCounters;
}

type ArtifactPropertyNames =
    | 'simplePropertyNames'
    | 'referenceNames'
    | 'nonNullableReferenceNames'
    | 'nullableReferenceNames'
    | 'insertPropertyNames';

/** Intermediate structure to collect the args that we pass to SQL bulk statements */
interface BulkData {
    /** The names of the properties that we pass to the bulk operation */
    propertyNames: string[];
    /** The digests of the records inserted or updated by the bulk operation */
    digests: Digest[];
    /** The args.  There is one arg per property name.
     * Each arg is an array of values, one per digest.
     */
    args: AnyValue[][];
}

interface MetaFactoryBinding {
    name: string;
    factory: NodeFactory;
}

/**
 * Class that implements the metadata upgrade process
 */
export class MetadataUpgrade {
    /** The artifacts for the meta-node tables that we have to update during upgrade */
    #artifacts: Dict<Artifact>;

    constructor(private readonly application: Application) {}

    #factoryStorageEnumValues: string[] = [];

    #propertyTypeEnumValues: string[] = [];

    #operationActionEnumValues: string[] = [];

    #operationKindEnumValues: string[] = [];

    async init(): Promise<void> {
        if (this.#artifacts) return;

        const constructors = [
            xtremMetadata.nodes.MetaPackage,
            xtremMetadata.nodes.MetaActivity,
            xtremMetadata.nodes.MetaActivityPermission,
            xtremMetadata.nodes.MetaServiceOption,
            xtremMetadata.nodes.MetaNodeFactory,
            xtremMetadata.nodes.MetaNodeProperty,
            xtremMetadata.nodes.MetaNodeOperation,
            xtremMetadata.nodes.MetaDataType,
        ];

        const metaFactoryBindings: MetaFactoryBinding[] = constructors.map(nodeConstructor => ({
            name: _.camelCase(nodeConstructor.name),
            factory: this.application.getFactoryByConstructor(nodeConstructor),
        }));

        // We need to ensure the table exist before proceeding because the upgrade rely on the database schema.
        // During an upgrade from a old version the new meta tables may not exist and then the UpgradeMetaAction will fail.
        const nonExistingMeta: string[] = [];
        await this.application.asRoot.withCommittedContext(
            null,
            context =>
                asyncArray(metaFactoryBindings).forEach(async binding => {
                    const exists = await binding.factory.table.tableExists(context);
                    if (!exists) {
                        nonExistingMeta.push(binding.name);
                    } else {
                        const tableSchema = await new ReadTableSqlContext(this.application).readSchema(
                            binding.factory.table.name,
                            {
                                skipForeignKeys: true,
                                skipIndexes: true,
                                skipSecurity: true,
                                skipSequences: true,
                            },
                        );
                        const deletedColumns = tableSchema.columns?.filter(
                            col => !binding.factory.table.columns.some(c => c.columnName === col.name),
                        );
                        if (deletedColumns?.length) {
                            await binding.factory.table.dropColumns(
                                context,
                                deletedColumns.map(col => col.name),
                            );
                        }
                    }
                }),
            { unsafeApplyToAllTenants: true, description: () => 'MetadataUpgrade.ensureTableExists' },
        );

        const validMetaFactoryBindings = metaFactoryBindings.filter(binding => !nonExistingMeta.includes(binding.name));
        this.#artifacts = createDictionary<Artifact>();
        await asyncArray(validMetaFactoryBindings).forEach(async binding => {
            this.#artifacts[binding.name] = await MetadataUpgrade.createArtifact(binding);
        });

        this.#factoryStorageEnumValues = await MetadataUpgrade.getEnumValues(
            this.application,
            xtremMetadata.enums.storageDataType,
        );
        this.#propertyTypeEnumValues = await MetadataUpgrade.getEnumValues(
            this.application,
            xtremMetadata.enums.MetaPropertyTypeDataType,
        );
        this.#operationActionEnumValues = await MetadataUpgrade.getEnumValues(
            this.application,
            xtremMetadata.enums.operationActionDataType,
        );
        this.#operationKindEnumValues = await MetadataUpgrade.getEnumValues(
            this.application,
            xtremMetadata.enums.operationKindDataType,
        );
    }

    static async getEnumAttributes(sqlContext: EnumSqlContext, enumTypeName: string): Promise<string[]> {
        return parsePgArray(await sqlContext.getEnumAttributes(enumTypeName), v => v);
    }

    static getEnumValues(application: Application, enumDataType: EnumDataType): Promise<string[]> {
        const enumSqlContext = new EnumSqlContext(application);
        return MetadataUpgrade.getEnumAttributes(enumSqlContext, enumDataType.getEnumType().name);
    }

    private static keepProperty(property: Property): boolean {
        return !property.isSystemProperty;
    }

    /**
     * Builds the list of property names for an artifact.
     * We build these lists once when we create the artifact.
     * It speeds up a bit the processing as we don't need to filter the lists for every record that we process.
     */
    private static async getPropertyLists(metaFactory: NodeFactory): Promise<Pick<Artifact, ArtifactPropertyNames>> {
        const simplePropertyNames = [] as string[];
        const referenceNames = [] as string[];
        const nonNullableReferenceNames = [] as string[];
        const nullableReferenceNames = [] as string[];
        const insertPropertyNames = ['checksum'] as string[];
        // Get columns from database
        // Only select columns actually in the database, because the DB might be out of sync
        // with the factory during the upgrade process
        const tableSchema = await new ReadTableSqlContext(metaFactory.application).readSchema(metaFactory.table.name, {
            skipForeignKeys: true,
            skipIndexes: true,
            skipSecurity: true,
            skipSequences: true,
        });
        // The list of columns will not include base table columns in the case where the factory is for a sub node
        const columns = tableSchema.columns?.map(col => col.name);

        metaFactory.properties
            .filter(
                property =>
                    this.keepProperty(property) &&
                    property.isStored &&
                    property.name !== 'checksum' &&
                    !property.name.startsWith('_') &&
                    !!property.columnName &&
                    (columns ?? []).includes(property.columnName),
            )
            .forEach(property => {
                insertPropertyNames.push(property.name);
                if (property.isReferenceProperty()) {
                    referenceNames.push(property.name);
                    if (property.isNullable) {
                        nullableReferenceNames.push(property.name);
                    } else {
                        nonNullableReferenceNames.push(property.name);
                    }
                } else {
                    simplePropertyNames.push(property.name);
                }
            });
        return {
            simplePropertyNames,
            referenceNames,
            nonNullableReferenceNames,
            nullableReferenceNames,
            insertPropertyNames,
        };
    }

    /**
     * Creates and initializes an artifact
     */
    private static async createArtifact(factoryBinding: MetaFactoryBinding): Promise<Artifact> {
        const { name, factory: metaFactory } = factoryBinding;
        const lists = await MetadataUpgrade.getPropertyLists(metaFactory);
        return {
            name,
            metaFactory,
            ...lists,
            digestsById: {},
            digestsByNaturalKey: {},
            payloads: [],
            counters: {
                inserted: 0,
                updatedAfterInsert: 0,
                updated: 0,
                markedDeleted: 0,
            },
        };
    }

    /** Returns the natural key of a digest */
    private getDigestNaturalKey(artifact: Artifact, digest: Digest): string {
        if (!artifact.metaFactory.naturalKey) throw new LogicError('natural key missing');
        return artifact.metaFactory.naturalKey
            .map(name => {
                const property = artifact.metaFactory.findProperty(name);
                if (property.isReferenceProperty()) {
                    const targetArtifact = this.findArtifact(property.targetFactory);
                    const targetId = digest[name] as integer;
                    const targetDigest = targetArtifact.digestsById[targetId];
                    if (!targetDigest)
                        throw new LogicError(
                            `${property.fullName}: target digest not found: ${targetArtifact.name}.${targetId}`,
                        );

                    return this.getDigestNaturalKey(targetArtifact, targetDigest);
                }
                return digest[name];
            })
            .join('|');
    }

    /**
     * Creates a digest for a node that we read from the meta-table
     */
    private getDigest(artifact: Artifact, digest: Digest, extra?: { inserted: boolean }): Digest {
        digest._naturalKey = this.getDigestNaturalKey(artifact, digest);
        digest._inserted = !!extra?.inserted;
        return digest;
    }

    /**
     * The list of all the properties (not their names) present in the artifact digest.
     */
    private static getDigestProperties(artifact: Artifact): Property[] {
        return ['_id', 'checksum', 'isActive', ...notNull(artifact.metaFactory.naturalKey)].map(name =>
            artifact.metaFactory.findProperty(name),
        );
    }

    /**
     * Reads the digests for a metadata node factory (MetaNodeFactory, MetaNodeProperty, ...)
     *
     * The digests contain _id + natural key + checksum
     *
     * This method returns a Dict of the digests indexed by their natural key.
     */
    private readDigest(artifact: Artifact): Promise<void> {
        const naturalKey = artifact.metaFactory.naturalKey;
        /* istanbul ignore next */
        if (!naturalKey) throw new LogicError(`${artifact.metaFactory.name}; cannot sync metadata, no natural key`);
        const digestProperties = MetadataUpgrade.getDigestProperties(artifact);
        const selector = digestProperties.reduce((r, k) => {
            r[k.name] = true;
            return r;
        }, {} as Dict<true>);

        return this.application.withReadonlyContext(
            null,
            async context => {
                const digests = (
                    await context.select(artifact.metaFactory.nodeConstructor, selector, { filter: {} })
                ).map(digest => this.getDigest(artifact, digest as unknown as Digest));

                // build the key as concatenation of natural key values.
                const ids = digests.map(digest => digest._id);
                artifact.digestsById = _.zipObject(ids, digests);
                const keys = digests.map(digest => digest._naturalKey);
                artifact.digestsByNaturalKey = _.zipObject(keys, digests);
            },
            { description: () => 'MetadataUpgrade.readDigest' },
        );
    }

    /**
     * Reads all the digests for all the artifacts
     * We read them sequentially because natural keys may reference other nodes
     * (for example MetaNodeProperty natural key references MetaNodeFactory)
     */
    private async readDigests(): Promise<void> {
        await asyncArray(Object.values(this.#artifacts)).forEach(artifact => this.readDigest(artifact));
    }

    /** Adds a checksum to a payload */
    static addChecksum<T>(payload: T): T & { checksum: string } {
        const json = JSON.stringify(payload);
        return { ...payload, checksum: crypto.createHash('SHA256').update(json).digest('base64') };
    }

    private static getOperationLocalizedTitleKey(factory: NodeFactory, op: PlainOperationDecorator): string {
        const packageName = op.definingPackage?.name || '';
        return i18n.getKey({
            isExtension: packageName !== factory.package.name,
            nodeName: factory.name,
            packageName,
            operation: { name: op.name, kind: op.operationKind },
        });
    }

    private static getLocalizedTitles(key: string, name: string): Dict<string> {
        return _.zipObject(
            supportedLocales,
            supportedLocales.map(locale => {
                try {
                    return localizedText(key, name, {}, locale);
                } catch {
                    // TODO: log level should be warning here but it generates a lot of noise in the log
                    // so it is better to use verbose for now
                    logger.verbose(() => `${key}: missing localized title for ${locale}`);
                    return _.startCase(name);
                }
            }),
        );
    }

    /** Returns the natural key of a reference inside a payload */
    private static getReferencePayloadValue(metaValue: AnyValue): AnyValue {
        if (!metaValue) return null;
        if (metaValue instanceof NodeFactory || metaValue instanceof DataType) return metaValue.name;
        if (metaValue instanceof Property) return `${metaValue.factory.name}|${metaValue.name}`;
        return (metaValue as AnyRecord).name;
    }

    /**
     * Transforms a metaObject (a NodeFactory, a Property, ...) into a payload.
     * The _id of the payload is not set (as the database will allocated it when we insert) and
     * the references are set to their natural key values rather than to _id values.
     */
    private static createPayload(artifact: Artifact, metaObject: unknown): Dict<AnyValue> {
        return {
            ..._.pick(metaObject as Dict<AnyValue>, artifact.simplePropertyNames),
            ..._.mapValues(
                _.pick(metaObject as Dict<AnyValue>, artifact.referenceNames),
                MetadataUpgrade.getReferencePayloadValue,
            ),
            isActive: true,
        };
    }

    /**
     * Extracts the metadata from the `application` object and builds the payload arrays that will
     * be upserted into the metadata tables (MetaNodeFactory, MetaNodeProperty, ...)
     */
    private fillPayloads(): void {
        const packages = this.application.getPackages();
        const factories = this.application.getAllFactories().filter(factory => {
            // We check if the storage type is in the enum on the DB if not we are in the first pass of the upgrade
            // the normal upgrade will create the enum value and run a second pass of the metadata upgrade
            // so we can safely ignore the missing storage type here
            return factory.storage == null || this.#factoryStorageEnumValues.includes(factory.storage);
        });
        const dataTypes = this.application.dataTypes;
        const artifacts = this.#artifacts;

        const localize = (localizationKey: string, name: string): Dict<string> => {
            const titles = MetadataUpgrade.getLocalizedTitles(localizationKey, name);
            if (!titles) throw new LogicError('no titles');
            return titles;
        };
        packages.forEach(currentPackage => {
            const packagePayload = MetadataUpgrade.createPayload(this.#artifacts.metaPackage, currentPackage);
            packagePayload.name = currentPackage.name;
            packagePayload.title = MetadataUpgrade.getLocalizedTitles(
                currentPackage.getLocalizedTitleKey(),
                currentPackage.name,
            );
            artifacts.metaPackage.payloads.push(MetadataUpgrade.addChecksum(packagePayload));

            Object.values(currentPackage.serviceOptions).forEach(serviceOption => {
                const serviceOptionPayload = MetadataUpgrade.createPayload(
                    this.#artifacts.metaServiceOption,
                    serviceOption,
                );
                serviceOptionPayload.title = MetadataUpgrade.getLocalizedTitles(
                    serviceOption.getLocalizedTitleKey(),
                    serviceOption.name,
                );
                serviceOptionPayload.package = currentPackage.name;
                artifacts.metaServiceOption.payloads.push(MetadataUpgrade.addChecksum(serviceOptionPayload));
            });
        });

        const jsonStringArray = (strings: string[]): string => {
            const formatted = strings.map(s => `"${s}"`).join(',');
            return `[${formatted}]`;
        };

        const jsonServiceOption = (serviceOptions: ServiceOption[]): string => {
            return jsonStringArray(serviceOptions.map(serviceOption => serviceOption.name));
        };

        factories.forEach(factory => {
            const factoryPayload = MetadataUpgrade.createPayload(this.#artifacts.metaNodeFactory, factory);
            factoryPayload.title = MetadataUpgrade.getLocalizedTitles(factory.getLocalizedTitleKey(), factory.name);
            factoryPayload.package = factory.package.name;
            factoryPayload.isClearedByReset = !!factoryPayload.isClearedByReset;
            if (!factoryPayload.storage) {
                factoryPayload.storage = 'json';
            }
            factoryPayload.serviceOptionNames = jsonServiceOption(factory.serviceOptions);
            factoryPayload.naturalKey = factory.naturalKey ? jsonStringArray(factory.naturalKey) : null;
            factoryPayload.notifies = factory.notifies ? jsonStringArray(factory.notifies) : null;

            logger.debug(() => `Filling factory - ${factory.name}`);
            artifacts.metaNodeFactory.payloads.push(MetadataUpgrade.addChecksum(factoryPayload));
        });

        if (this.#artifacts.metaDataType) {
            Object.entries(dataTypes)
                .filter(([, dataType]) => {
                    // We check if the data type is in the enum on the DB if not we are in the first pass of the upgrade
                    // the normal upgrade will create the enum value and run a second pass of the metadata upgrade
                    if (!this.#propertyTypeEnumValues.includes(dataType.type)) {
                        logger.info(
                            `Skipping data type ${dataType.name} as it's type is not in the property types enum on the database: ${dataType.type}`,
                        );
                        return false;
                    }
                    return true;
                })
                .forEach(([dataTypeName, dataType]) => {
                    const dataTypePayload = MetadataUpgrade.createPayload(this.#artifacts.metaDataType, dataType);
                    dataTypePayload.definingPackage = dataType.pack;
                    dataTypePayload.name = dataTypeName;
                    dataTypePayload.serviceOptionNames = jsonServiceOption(dataType?.serviceOptions || []);
                    dataTypePayload.type = dataType.type;
                    const attributes = dataType.getMetaData({
                        application: this.application,
                        localize,
                    });
                    dataTypePayload.attributes = {
                        ...dataType?.dataTypeOptions?.(),
                        ...attributes,
                    };
                    dataTypePayload.title = MetadataUpgrade.getLocalizedTitles(
                        dataType.getLocalizedTitleKey(),
                        dataTypePayload.name,
                    );
                    artifacts.metaDataType.payloads.push(MetadataUpgrade.addChecksum(dataTypePayload));
                });
        }

        factories.forEach(factory => {
            factory.properties
                .filter(property => {
                    if (!this.#propertyTypeEnumValues.includes(property.type)) {
                        logger.info(
                            `Skipping property ${property.name} of factory ${factory.name} as its type is not in the property types enum on the database: ${property.type}`,
                        );
                        return false;
                    }
                    return MetadataUpgrade.keepProperty(property);
                })
                .forEach(property => {
                    const propertyPayload = MetadataUpgrade.createPayload(this.#artifacts.metaNodeProperty, property);
                    propertyPayload.title = MetadataUpgrade.getLocalizedTitles(
                        property.getLocalizedTitleKey(),
                        property.name,
                    );
                    propertyPayload.package = property.definingPackage.name;
                    propertyPayload.serviceOptionNames = jsonServiceOption(property.serviceOptions);
                    logger.debug(() => `Filling factory property- ${factory.name}.${property.name}`);
                    artifacts.metaNodeProperty.payloads.push(MetadataUpgrade.addChecksum(propertyPayload));
                });
            [...factory.queries, ...factory.mutations]
                .filter(operation => {
                    // Filter out operations that are not in the operationKinds or operationActions
                    // if the kind or type is not defined we are in the first pass of the upgrade
                    // the normal upgrade will create the enum value and run a second pass of the metadata upgrade
                    if (
                        !(
                            this.#operationKindEnumValues.includes(operation.operationKind) &&
                            (operation.action == null || this.#operationActionEnumValues.includes(operation.action))
                        )
                    ) {
                        logger.info(
                            `Skipping operation ${factory.name}.${operation.name} as its kind or action is not in the operation kinds or actions enum on the database: kind=${operation.operationKind}, action=${operation.action}`,
                        );
                        return false;
                    }
                    return true;
                })
                .forEach(operation => {
                    const operationPayload = MetadataUpgrade.createPayload(
                        this.#artifacts.metaNodeOperation,
                        operation,
                    );
                    const serviceOptions = operation.serviceOptions?.() || [];

                    const titleKey = MetadataUpgrade.getOperationLocalizedTitleKey(
                        factory,
                        operation as PlainOperationDecorator,
                    );
                    operationPayload.title = MetadataUpgrade.getLocalizedTitles(titleKey, operation.name);
                    operationPayload.factory = factory.name;
                    operationPayload.kind = operation.operationKind;
                    operationPayload.isSchedulable = !!operation.isSchedulable;
                    operationPayload.isMutation = !!factory.mutations.find(op => op.name === operation.name);
                    operationPayload.signature = friendlyOperationSignature(operation);
                    operationPayload.serviceOptionNames = jsonServiceOption(serviceOptions);
                    operationPayload.package = operation.definingPackage?.name || '';
                    operationPayload.isPublished = !!operation.isPublished;
                    logger.debug(
                        () =>
                            `Filling factory ${operation.operationKind} - ${factory.name}.${operation.name}.${operation.action}`,
                    );

                    artifacts.metaNodeOperation.payloads.push(MetadataUpgrade.addChecksum(operationPayload));
                });
        });

        if (this.#artifacts.metaActivity) {
            const activities = this.application.activities;
            Object.values(activities).forEach(activity => {
                const activityPayload = MetadataUpgrade.createPayload(this.#artifacts.metaActivity, activity);
                activityPayload.name = activity.name;
                activityPayload.title = MetadataUpgrade.getLocalizedTitles(
                    activity.getLocalizedTitleKey(),
                    activity.name,
                );
                activityPayload.package = activity.package;
                artifacts.metaActivity.payloads.push(MetadataUpgrade.addChecksum(activityPayload));

                activity.permissions.forEach(permission => {
                    const permissionPayload = MetadataUpgrade.createPayload(
                        this.#artifacts.metaActivityPermission,
                        permission,
                    );
                    permissionPayload.title = MetadataUpgrade.getLocalizedTitles(
                        activity.getPermissionLocalizedTitleKey(permission),
                        permission,
                    );
                    permissionPayload.package = activity.package;
                    permissionPayload.activity = activity.name;
                    permissionPayload.name = permission;
                    artifacts.metaActivityPermission.payloads.push(MetadataUpgrade.addChecksum(permissionPayload));
                });
            });
        }
    }

    /** Finds the artifact (MetaNodeFactory, MetaNodeProperty, ...) for one of the meta-node factories */
    private findArtifact(metaFactory: NodeFactory): Artifact {
        const artifact = this.#artifacts[_.camelCase(metaFactory.name)];
        /* istanbul ignore next */
        if (!artifact) throw new LogicError(`invalid artifact: ${metaFactory.name}`);
        return artifact;
    }

    /**
     * Resolves a cross-refence in a payload, by looking up its natural key into the target artifact's digests.
     * `value` is the natural key of the referenced record.
     */
    private getReferenceId(artifact: Artifact, propertyName: string, value: AnyValue): AnyValue {
        const property = artifact.metaFactory.findProperty(propertyName);
        /* istanbul ignore next */
        if (!property.isReferenceProperty()) throw new LogicError(`${property.fullName}: not a reference property`);
        const targetArtifact = this.findArtifact(property.targetFactory);
        return targetArtifact.digestsByNaturalKey[value as string]?._id;
    }

    /**
     * Gets the natural key from the payload.
     * All the references are natural key values in the payload. So we can just concatenate with '|'.
     */
    private static getPayloadNaturalKey(metaFactory: NodeFactory, payload: Payload): string {
        return notNull(metaFactory.naturalKey)
            .map(name => payload[name])
            .join('|');
    }

    /** Returns the SQL type for a given property - for now, only handle the types used by metadata nodes */
    private getSqlType(property: Property): string {
        switch (property.type) {
            case 'boolean':
                return 'BOOL';
            case 'string':
                if (property.isLocalized) return 'JSONB';
                return 'TEXT';
            case 'integer':
            case 'reference':
                return 'INT8';
            case 'json':
                return 'JSON';
            case 'referenceArray':
                // see https://stackoverflow.com/questions/9159440/array-of-arrays-in-postgresql
                throw new LogicError(`${property.fullName}: not supported - incompatible with unnest`);
            case 'enum': {
                const dataType = property.dataType as EnumDataType;
                return `${this.application.schemaName}.${dataType.getEnumType().name}`;
            }
            default:
                throw new LogicError(`${property.fullName}: unhandled property type: ${property.type}`);
        }
    }

    /** Utility to preserve leading underscore in snake_case */
    private static snakeCase(name: string): string {
        return name[0] === '_' ? `_${_.snakeCase(name)}` : _.snakeCase(name);
    }

    /** Create a BulkData object in which we will push the parameter values */
    private static newBulkData(propertyNames: string[]): BulkData {
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        return { propertyNames, digests: [], args: propertyNames.map(_unused => []) };
    }

    /** Adds a record payload to the BulkData object */
    private addToBulkData(artifact: Artifact, digest: Digest, payload: Payload, bulkData: BulkData): void {
        bulkData.propertyNames.forEach((propertyName, i) => {
            const property = artifact.metaFactory.findProperty(propertyName);
            let value = propertyName === '_id' ? digest._id : (payload[propertyName] ?? null);
            if (property.isReferenceProperty()) value = this.getReferenceId(artifact, propertyName, value) ?? null;

            bulkData.args[i].push(value);
        });
        bulkData.digests.push(digest);
    }

    /** Creates the bulk data for a bulk insert */
    private getInsertBulkData(artifact: Artifact): BulkData {
        const bulkData = MetadataUpgrade.newBulkData(artifact.insertPropertyNames);
        const digestProperties = MetadataUpgrade.getDigestProperties(artifact);
        const digests = [] as Digest[];

        artifact.payloads.forEach(payload => {
            const naturalKey = MetadataUpgrade.getPayloadNaturalKey(artifact.metaFactory, payload);
            if (!artifact.digestsByNaturalKey[naturalKey]) {
                const digest = _.pick(
                    payload,
                    digestProperties.map(prop => prop.name),
                ) as Digest;
                digest._naturalKey = naturalKey;
                digest._inserted = true;
                digest._foundInPayloads = true;
                artifact.digestsByNaturalKey[digest._naturalKey] = digest;
                artifact.counters.inserted += 1;

                this.addToBulkData(artifact, digest, payload, bulkData);
                digests.push(digest);
            } else {
                artifact.digestsByNaturalKey[naturalKey]._foundInPayloads = true;
            }
        });

        return bulkData;
    }

    /** Returns the SQL statement for a bulk insert */
    private getBulkInsertSql(artifact: Artifact): string {
        const fullTableName = `${this.application.schemaName}.${artifact.metaFactory.tableName}`;
        const columnNames = artifact.insertPropertyNames.map(name => MetadataUpgrade.snakeCase(name));

        // See https://stackoverflow.com/questions/7019831/bulk-batch-update-upsert-in-postgresql
        const parameters = artifact.insertPropertyNames.map(
            (propertyName, j) =>
                `unnest($${j + 1}::${this.getSqlType(artifact.metaFactory.findProperty(propertyName))}[])`,
        );

        return `/*RAW*/INSERT INTO ${fullTableName} (${columnNames}) VALUES (${parameters}) RETURNING _id`;
    }

    /** Executes the bulk insert */
    private async bulkInsert(artifact: Artifact, bulkData: BulkData): Promise<void> {
        const end = bulkData.digests.length;
        if (end === 0) return;

        const insertSql = this.getBulkInsertSql(artifact);

        await this.application.asRoot.withCommittedContext(
            null,
            async context => {
                const chunkSize = 5000;
                for (let chunk = 0; chunk < end; chunk += chunkSize) {
                    const chunkArgs = bulkData.args.map(arg => arg.slice(chunk, chunk + chunkSize));

                    const results = await context.executeSql<{ rows: { _id: integer }[] }>(insertSql, chunkArgs);
                    bulkData.digests.slice(chunk, chunk + chunkSize).forEach((digest, i) => {
                        digest._id = results.rows[i]?._id;
                        artifact.digestsById[digest._id] = digest;
                    });
                }
            },
            { unsafeApplyToAllTenants: true, description: () => 'MetadataUpgrade.bulkInsert' },
        );
    }

    /**
     * Inserts all the payloads that don't match any digest.
     * The digests of the inserted payloads are generated and registered in artifact.digests.
     */
    private async insertMissingEntries(artifact: Artifact): Promise<void> {
        const bulkData = this.getInsertBulkData(artifact);
        await this.bulkInsert(artifact, bulkData);
    }

    /** Creates the bulk data for a bulk update */
    private getUpdateBulkData(
        artifact: Artifact,
        options?: { fullReload?: boolean },
    ): {
        partialUpdateBulkData: BulkData;
        fullUpdateBulkData: BulkData;
    } {
        const partialUpdateBulkData = MetadataUpgrade.newBulkData(['_id', ...artifact.nullableReferenceNames]);
        const fullUpdateBulkData = MetadataUpgrade.newBulkData([
            '_id',
            ...artifact.simplePropertyNames,
            ...artifact.referenceNames,
            'checksum',
        ]);

        // insert the missing factories to get their ids
        artifact.payloads.forEach(payload => {
            const naturalKey = MetadataUpgrade.getPayloadNaturalKey(artifact.metaFactory, payload);
            const digest = artifact.digestsByNaturalKey[naturalKey];
            if (!digest)
                /* istanbul ignore next */
                throw new LogicError(`${artifact.metaFactory.name}/${naturalKey}: cannot update: digest missing`);

            if (digest._inserted) {
                // Record was just inserted, update the missing nullable references
                // If all nullable references are undefined we can just return
                if (artifact.nullableReferenceNames.every(name => payload[name] == null)) return;

                // Update the missing references
                this.addToBulkData(artifact, digest, payload, partialUpdateBulkData);
                artifact.counters.updatedAfterInsert += 1;
            } else {
                // Record existed before
                // If digest matches record did not change
                if (digest.checksum === payload.checksum && !options?.fullReload) return;

                // Update it in full
                this.addToBulkData(artifact, digest, payload, fullUpdateBulkData);
                artifact.counters.updated += 1;
            }
        });
        return { partialUpdateBulkData, fullUpdateBulkData };
    }

    /** Returns the SQL statement for a bulk update */
    private getBulkUpdateSql(artifact: Artifact, propertyNames: string[]): string {
        const fullTableName = `${this.application.schemaName}.${artifact.metaFactory.tableName}`;
        const columnNames = propertyNames.map(name => MetadataUpgrade.snakeCase(name));
        const typeNames = propertyNames.map(name => this.getSqlType(artifact.metaFactory.findProperty(name)));

        // See https://stackoverflow.com/questions/7019831/bulk-batch-update-upsert-in-postgresql
        return `UPDATE ${fullTableName}
        SET ${columnNames.slice(1).map(name => `${name} = data_table.new_${name}`)}
        FROM (SELECT unnest($1::int8[]) AS _id,
                ${columnNames.map((name, i) => `unnest($${i + 1}::${typeNames[i]}[]) AS new_${name}`)}
            ) AS data_table
        WHERE ${fullTableName}._id = data_table._id;`;
    }

    /** Executes the bulk update */
    private async bulkUpdate(artifact: Artifact, bulkData: BulkData): Promise<void> {
        const { propertyNames, args } = bulkData;
        if (propertyNames.length === 1) return;

        const updateSql = this.getBulkUpdateSql(artifact, propertyNames);

        await this.application.asRoot.withCommittedContext(
            null,
            async context => {
                await context.executeSql(updateSql, args);
            },
            { unsafeApplyToAllTenants: true, description: () => 'MetadataUpgrade.bulkUpdate' },
        );
    }

    /**
     * Updates all the payloads.
     *
     * If the payload was inserted in the previous pass we just update its nullable references (and we skip if they
     * are all null).
     *
     * Otherwise, the payload existed before and we update all its fields, but we compare the old (digest)
     * and new (payload) checksums to skip the records that did not change.
     */
    private async updateEntries(artifact: Artifact, options?: { fullReload?: boolean }): Promise<void> {
        const { partialUpdateBulkData, fullUpdateBulkData } = this.getUpdateBulkData(artifact, options);

        await this.bulkUpdate(artifact, partialUpdateBulkData);
        await this.bulkUpdate(artifact, fullUpdateBulkData);
    }

    /**
     * Sets isActive to false on all digests that did not match any payload.
     */
    private async deactivateDeletedEntries(artifact: Artifact): Promise<void> {
        // The newly
        const deletedEntries = Object.values(artifact.digestsByNaturalKey)
            .filter(digest => !digest._foundInPayloads && digest.isActive)
            .map(digest => ({ _id: digest._id, checksum: digest.checksum }));

        if (deletedEntries.length === 0) return;

        await this.application.asRoot.withCommittedContext(
            null,
            async context => {
                let count = 0;
                await asyncArray(deletedEntries).forEach(async deletedEntry => {
                    try {
                        await context.bulkUpdate(artifact.metaFactory.nodeConstructor as StaticThis<MetaNode>, {
                            set: { isActive: false, checksum: deletedEntry.checksum },
                            where: { _id: deletedEntry._id },
                        });
                        count += 1;
                    } catch (error) {
                        logger.error(error.stack);
                    }
                });

                if (count !== deletedEntries.length)
                    /* istanbul ignore next */
                    throw new LogicError(
                        `${artifact.name}: deleted count mismatch: ${count} !== ${deletedEntries.length}`,
                    );
                artifact.counters.markedDeleted += count;
            },
            { unsafeApplyToAllTenants: true, description: () => 'MetadataUpgrade.deactivateDeletedEntries' },
        );
    }

    /**
     * Sets isActive to true on all digests that is in the payload but is not active in the db.
     */
    private async reactivateEntries(artifact: Artifact): Promise<void> {
        // The newly
        const reactivatedEntries = Object.values(artifact.digestsByNaturalKey)
            .filter(digest => digest._foundInPayloads && !digest.isActive)
            .map(digest => ({ _id: digest._id, checksum: digest.checksum }));

        if (reactivatedEntries.length === 0) return;

        await this.application.asRoot.withCommittedContext(
            null,
            async context => {
                let count = 0;
                await asyncArray(reactivatedEntries).forEach(async reactivatedEntry => {
                    try {
                        await context.bulkUpdate(artifact.metaFactory.nodeConstructor as StaticThis<MetaNode>, {
                            set: { isActive: true, checksum: reactivatedEntry.checksum },
                            where: { _id: reactivatedEntry._id },
                        });
                        count += 1;
                    } catch (error) {
                        logger.error(error.stack);
                    }
                });

                if (count !== reactivatedEntries.length)
                    /* istanbul ignore next */
                    throw new LogicError(
                        `${artifact.name}: reactivation count mismatch: ${count} !== ${reactivatedEntries.length}`,
                    );
                artifact.counters.updated += count;
            },
            { unsafeApplyToAllTenants: true, description: () => 'MetadataUpgrade.reactivateEntries' },
        );
    }

    /** Formats a counter */
    private static formatCounter(artifact: Artifact): string {
        return `${artifact.name}: ${Object.keys(artifact.counters)
            .map(name => `${name} ${(artifact.counters as unknown as Dict<integer>)[name]}`)
            .join(', ')}`;
    }

    invalidateCaches(artifacts: Artifact[]): Promise<void> {
        return this.application.asRoot.withCommittedContext(null, context =>
            asyncArray(artifacts).forEach(artifact => artifact.metaFactory.invalidateCache(context)),
        );
    }

    /**
     * Upgrades all the metadata records of all the metadata artifacts.
     *
     * This is run at the end of schema creation and upgrade
     */
    async upgrade(options?: { fullReload?: boolean; onlyCreate?: boolean }): Promise<void> {
        await this.init();
        await basicProfiler.measure('metadata-upgrade', async () => {
            await basicProfiler.measure('read-digests', () => this.readDigests());
            await basicProfiler.measure('fill-payloads', () => this.fillPayloads());

            const artifacts = Object.values(this.#artifacts);
            // Insert sequentially
            await basicProfiler.measure('insert', () =>
                asyncArray(artifacts).forEach(artifact => this.insertMissingEntries(artifact)),
            );

            if (!options?.onlyCreate) {
                // Update / deactivate / reactivate entries
                await basicProfiler.measure('update entries', () =>
                    asyncArray(artifacts).forEach(artifact => this.updateEntries(artifact, options)),
                );
                await basicProfiler.measure('deactivate deleted entries', () =>
                    asyncArray(artifacts).forEach(artifact => this.deactivateDeletedEntries(artifact)),
                );
                await basicProfiler.measure('reactivate entries', () =>
                    asyncArray(artifacts).forEach(artifact => this.reactivateEntries(artifact)),
                );
            }

            // We use special bulk insert/update statements that bypass node caching logic.
            // So we invalidate the caches that were populated when we read the artifact records.
            await this.invalidateCaches(artifacts);

            Object.values(artifacts).forEach(artifact =>
                logger.info(`metadata upgrade: ${MetadataUpgrade.formatCounter(artifact)}`),
            );
        });
        await basicProfiler.walk((path, item) => {
            if (path[0] === 'metadata-upgrade') logger.info(`${path.join('/')}: ${item.duration} ms`);
        });
    }
}
