import { ReferenceDataType, StringDataType } from '@sage/xtrem-core';
import { MetaNodeFactory } from '../nodes/meta-node-factory';
import { MetaPackage } from '../nodes/meta-package';

export const checksum = new StringDataType({ maxLength: 80 });

export const metaNodeFactory = new ReferenceDataType({
    reference: () => MetaNodeFactory,
    isDefault: true,
    lookup: {
        valuePath: 'name',
        helperTextPath: 'title',
        columnPaths: ['name', 'title', 'isActive'],
    },
});

export const metaPackage = new ReferenceDataType({
    reference: () => MetaPackage,
    isDefault: true,
    lookup: {
        valuePath: 'name',
        helperTextPath: 'title',
        columnPaths: ['name', 'title', 'isActive'],
    },
});
