{"@sage/xtrem-metadata/data_types__checksum__name": "Checksum", "@sage/xtrem-metadata/data_types__meta_node_factory__name": "Meta node factory", "@sage/xtrem-metadata/data_types__meta_operation_action_enum__name": "Meta operation action enum", "@sage/xtrem-metadata/data_types__meta_operation_kind_enum__name": "Meta operation kind enum", "@sage/xtrem-metadata/data_types__meta_package__name": "Meta package", "@sage/xtrem-metadata/data_types__meta_property_type_enum__name": "Meta property type enum", "@sage/xtrem-metadata/data_types__meta_storage_enum__name": "Meta storage enum", "@sage/xtrem-metadata/enums__meta_operation_action__requestUserNotification": "Request user notification", "@sage/xtrem-metadata/enums__meta_operation_action__start": "Start", "@sage/xtrem-metadata/enums__meta_operation_action__stop": "Stop", "@sage/xtrem-metadata/enums__meta_operation_action__track": "Track", "@sage/xtrem-metadata/enums__meta_operation_kind__asyncMutation": "Async mutation", "@sage/xtrem-metadata/enums__meta_operation_kind__asyncTrackerQuery": "Async tracker query", "@sage/xtrem-metadata/enums__meta_operation_kind__bulkMutation": "Bulk mutation", "@sage/xtrem-metadata/enums__meta_operation_kind__mutation": "Mutation", "@sage/xtrem-metadata/enums__meta_operation_kind__query": "Query", "@sage/xtrem-metadata/enums__meta_property_type__binaryStream": "Binary stream", "@sage/xtrem-metadata/enums__meta_property_type__boolean": "Boolean", "@sage/xtrem-metadata/enums__meta_property_type__byte": "Byte", "@sage/xtrem-metadata/enums__meta_property_type__collection": "Collection", "@sage/xtrem-metadata/enums__meta_property_type__date": "Date", "@sage/xtrem-metadata/enums__meta_property_type__dateRange": "Date range", "@sage/xtrem-metadata/enums__meta_property_type__datetime": "Datetime", "@sage/xtrem-metadata/enums__meta_property_type__datetimeRange": "Datetime range", "@sage/xtrem-metadata/enums__meta_property_type__decimal": "Decimal", "@sage/xtrem-metadata/enums__meta_property_type__decimalRange": "Decimal range", "@sage/xtrem-metadata/enums__meta_property_type__double": "Double", "@sage/xtrem-metadata/enums__meta_property_type__enum": "Enum", "@sage/xtrem-metadata/enums__meta_property_type__enumArray": "Enum array", "@sage/xtrem-metadata/enums__meta_property_type__float": "Float", "@sage/xtrem-metadata/enums__meta_property_type__integer": "Integer", "@sage/xtrem-metadata/enums__meta_property_type__integerArray": "Integer array", "@sage/xtrem-metadata/enums__meta_property_type__integerRange": "Integer range", "@sage/xtrem-metadata/enums__meta_property_type__json": "Json", "@sage/xtrem-metadata/enums__meta_property_type__jsonReference": "Json reference", "@sage/xtrem-metadata/enums__meta_property_type__reference": "Reference", "@sage/xtrem-metadata/enums__meta_property_type__referenceArray": "Reference array", "@sage/xtrem-metadata/enums__meta_property_type__short": "Short", "@sage/xtrem-metadata/enums__meta_property_type__string": "String", "@sage/xtrem-metadata/enums__meta_property_type__stringArray": "String array", "@sage/xtrem-metadata/enums__meta_property_type__textStream": "Text stream", "@sage/xtrem-metadata/enums__meta_property_type__time": "Time", "@sage/xtrem-metadata/enums__meta_property_type__uuid": "<PERSON><PERSON>", "@sage/xtrem-metadata/enums__meta_storage__external": "External", "@sage/xtrem-metadata/enums__meta_storage__json": "Json", "@sage/xtrem-metadata/enums__meta_storage__sql": "Sql", "@sage/xtrem-metadata/node-extensions__sys_notification_state_extension__property__operation": "Operation", "@sage/xtrem-metadata/nodes__meta_activity__asyncMutation__asyncExport": "Export", "@sage/xtrem-metadata/nodes__meta_activity__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-metadata/nodes__meta_activity__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-metadata/nodes__meta_activity__node_name": "Meta activity", "@sage/xtrem-metadata/nodes__meta_activity__property__checksum": "Checksum", "@sage/xtrem-metadata/nodes__meta_activity__property__isActive": "Is active", "@sage/xtrem-metadata/nodes__meta_activity__property__name": "Name", "@sage/xtrem-metadata/nodes__meta_activity__property__package": "Package", "@sage/xtrem-metadata/nodes__meta_activity__property__permissions": "Permissions", "@sage/xtrem-metadata/nodes__meta_activity__property__title": "Title", "@sage/xtrem-metadata/nodes__meta_activity_permission__asyncMutation__asyncExport": "Export", "@sage/xtrem-metadata/nodes__meta_activity_permission__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-metadata/nodes__meta_activity_permission__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-metadata/nodes__meta_activity_permission__node_name": "Meta activity permission", "@sage/xtrem-metadata/nodes__meta_activity_permission__property__activity": "Activity", "@sage/xtrem-metadata/nodes__meta_activity_permission__property__checksum": "Checksum", "@sage/xtrem-metadata/nodes__meta_activity_permission__property__isActive": "Is active", "@sage/xtrem-metadata/nodes__meta_activity_permission__property__name": "Name", "@sage/xtrem-metadata/nodes__meta_activity_permission__property__package": "Package", "@sage/xtrem-metadata/nodes__meta_activity_permission__property__title": "Title", "@sage/xtrem-metadata/nodes__meta_data_type__asyncMutation__asyncExport": "Export", "@sage/xtrem-metadata/nodes__meta_data_type__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-metadata/nodes__meta_data_type__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-metadata/nodes__meta_data_type__node_name": "Meta data type", "@sage/xtrem-metadata/nodes__meta_data_type__property__attributes": "Attributes", "@sage/xtrem-metadata/nodes__meta_data_type__property__checksum": "Checksum", "@sage/xtrem-metadata/nodes__meta_data_type__property__definingPackage": "Defining package", "@sage/xtrem-metadata/nodes__meta_data_type__property__isActive": "Is active", "@sage/xtrem-metadata/nodes__meta_data_type__property__name": "Name", "@sage/xtrem-metadata/nodes__meta_data_type__property__serviceOptionNames": "Service option names", "@sage/xtrem-metadata/nodes__meta_data_type__property__serviceOptions": "Service options", "@sage/xtrem-metadata/nodes__meta_data_type__property__title": "Title", "@sage/xtrem-metadata/nodes__meta_data_type__property__type": "Type", "@sage/xtrem-metadata/nodes__meta_node_factory__asyncMutation__asyncExport": "Export", "@sage/xtrem-metadata/nodes__meta_node_factory__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-metadata/nodes__meta_node_factory__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-metadata/nodes__meta_node_factory__node_name": "Meta node factory", "@sage/xtrem-metadata/nodes__meta_node_factory__property__checksum": "Checksum", "@sage/xtrem-metadata/nodes__meta_node_factory__property__customFields": "Custom fields", "@sage/xtrem-metadata/nodes__meta_node_factory__property__extends": "Extends", "@sage/xtrem-metadata/nodes__meta_node_factory__property__isAbstract": "Is abstract", "@sage/xtrem-metadata/nodes__meta_node_factory__property__isActive": "Is active", "@sage/xtrem-metadata/nodes__meta_node_factory__property__isClearedByReset": "Is cleared by reset", "@sage/xtrem-metadata/nodes__meta_node_factory__property__isCustomizable": "Is customizable", "@sage/xtrem-metadata/nodes__meta_node_factory__property__isPlatformNode": "Is platform node", "@sage/xtrem-metadata/nodes__meta_node_factory__property__isPublished": "Is published", "@sage/xtrem-metadata/nodes__meta_node_factory__property__isSetupNode": "Is setup node", "@sage/xtrem-metadata/nodes__meta_node_factory__property__isSharedByAllTenants": "Is shared by all tenants", "@sage/xtrem-metadata/nodes__meta_node_factory__property__isSynchronizable": "Is synchronizable", "@sage/xtrem-metadata/nodes__meta_node_factory__property__isSynchronized": "Is synchronized", "@sage/xtrem-metadata/nodes__meta_node_factory__property__isVitalCollectionChild": "Is vital collection child", "@sage/xtrem-metadata/nodes__meta_node_factory__property__isVitalReferenceChild": "Is vital reference child", "@sage/xtrem-metadata/nodes__meta_node_factory__property__name": "Name", "@sage/xtrem-metadata/nodes__meta_node_factory__property__naturalKey": "Natural key", "@sage/xtrem-metadata/nodes__meta_node_factory__property__notifies": "Notifies", "@sage/xtrem-metadata/nodes__meta_node_factory__property__operations": "Operations", "@sage/xtrem-metadata/nodes__meta_node_factory__property__package": "Package", "@sage/xtrem-metadata/nodes__meta_node_factory__property__properties": "Properties", "@sage/xtrem-metadata/nodes__meta_node_factory__property__serviceOptionNames": "Service option names", "@sage/xtrem-metadata/nodes__meta_node_factory__property__serviceOptions": "Service options", "@sage/xtrem-metadata/nodes__meta_node_factory__property__storage": "Storage", "@sage/xtrem-metadata/nodes__meta_node_factory__property__title": "Title", "@sage/xtrem-metadata/nodes__meta_node_operation__asyncMutation__asyncExport": "Export", "@sage/xtrem-metadata/nodes__meta_node_operation__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-metadata/nodes__meta_node_operation__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-metadata/nodes__meta_node_operation__node_name": "Meta node operation", "@sage/xtrem-metadata/nodes__meta_node_operation__property__action": "Action", "@sage/xtrem-metadata/nodes__meta_node_operation__property__checksum": "Checksum", "@sage/xtrem-metadata/nodes__meta_node_operation__property__factory": "Factory", "@sage/xtrem-metadata/nodes__meta_node_operation__property__isActive": "Is active", "@sage/xtrem-metadata/nodes__meta_node_operation__property__isMutation": "Is mutation", "@sage/xtrem-metadata/nodes__meta_node_operation__property__isPublished": "Is published", "@sage/xtrem-metadata/nodes__meta_node_operation__property__isSchedulable": "Is schedulable", "@sage/xtrem-metadata/nodes__meta_node_operation__property__kind": "Kind", "@sage/xtrem-metadata/nodes__meta_node_operation__property__name": "Name", "@sage/xtrem-metadata/nodes__meta_node_operation__property__package": "Package", "@sage/xtrem-metadata/nodes__meta_node_operation__property__parameters": "Parameters", "@sage/xtrem-metadata/nodes__meta_node_operation__property__return": "Return", "@sage/xtrem-metadata/nodes__meta_node_operation__property__serviceOptionNames": "Service option names", "@sage/xtrem-metadata/nodes__meta_node_operation__property__serviceOptions": "Service options", "@sage/xtrem-metadata/nodes__meta_node_operation__property__signature": "Signature", "@sage/xtrem-metadata/nodes__meta_node_operation__property__title": "Title", "@sage/xtrem-metadata/nodes__meta_node_operation__property__topic": "Topic", "@sage/xtrem-metadata/nodes__meta_node_property__asyncMutation__asyncExport": "Export", "@sage/xtrem-metadata/nodes__meta_node_property__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-metadata/nodes__meta_node_property__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-metadata/nodes__meta_node_property__node_name": "Meta node property", "@sage/xtrem-metadata/nodes__meta_node_property__property__canFilter": "Can filter", "@sage/xtrem-metadata/nodes__meta_node_property__property__canSort": "Can sort", "@sage/xtrem-metadata/nodes__meta_node_property__property__checksum": "Checksum", "@sage/xtrem-metadata/nodes__meta_node_property__property__dataType": "Data type", "@sage/xtrem-metadata/nodes__meta_node_property__property__dependencyIndex": "Dependency index", "@sage/xtrem-metadata/nodes__meta_node_property__property__factory": "Factory", "@sage/xtrem-metadata/nodes__meta_node_property__property__isActive": "Is active", "@sage/xtrem-metadata/nodes__meta_node_property__property__isInherited": "Is inherited", "@sage/xtrem-metadata/nodes__meta_node_property__property__isMutable": "Is mutable", "@sage/xtrem-metadata/nodes__meta_node_property__property__isNullable": "Is nullable", "@sage/xtrem-metadata/nodes__meta_node_property__property__isOnInputType": "Is on input type", "@sage/xtrem-metadata/nodes__meta_node_property__property__isOnOutputType": "Is on output type", "@sage/xtrem-metadata/nodes__meta_node_property__property__isPublished": "Is published", "@sage/xtrem-metadata/nodes__meta_node_property__property__isRequired": "Is required", "@sage/xtrem-metadata/nodes__meta_node_property__property__isStored": "Is stored", "@sage/xtrem-metadata/nodes__meta_node_property__property__isStoredOutput": "Is stored output", "@sage/xtrem-metadata/nodes__meta_node_property__property__isTransientInput": "Is transient input", "@sage/xtrem-metadata/nodes__meta_node_property__property__isVital": "Is vital", "@sage/xtrem-metadata/nodes__meta_node_property__property__isVitalParent": "Is vital parent", "@sage/xtrem-metadata/nodes__meta_node_property__property__name": "Name", "@sage/xtrem-metadata/nodes__meta_node_property__property__package": "Package", "@sage/xtrem-metadata/nodes__meta_node_property__property__reverseReference": "Reverse reference", "@sage/xtrem-metadata/nodes__meta_node_property__property__serviceOptionNames": "Service option names", "@sage/xtrem-metadata/nodes__meta_node_property__property__serviceOptions": "Service options", "@sage/xtrem-metadata/nodes__meta_node_property__property__targetFactory": "Target factory", "@sage/xtrem-metadata/nodes__meta_node_property__property__title": "Title", "@sage/xtrem-metadata/nodes__meta_node_property__property__type": "Type", "@sage/xtrem-metadata/nodes__meta_package__asyncMutation__asyncExport": "Export", "@sage/xtrem-metadata/nodes__meta_package__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-metadata/nodes__meta_package__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-metadata/nodes__meta_package__node_name": "Meta package", "@sage/xtrem-metadata/nodes__meta_package__property__checksum": "Checksum", "@sage/xtrem-metadata/nodes__meta_package__property__isActive": "Is active", "@sage/xtrem-metadata/nodes__meta_package__property__name": "Name", "@sage/xtrem-metadata/nodes__meta_package__property__title": "Title", "@sage/xtrem-metadata/nodes__meta_service_option__asyncMutation__asyncExport": "Export", "@sage/xtrem-metadata/nodes__meta_service_option__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-metadata/nodes__meta_service_option__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-metadata/nodes__meta_service_option__node_name": "Meta service option", "@sage/xtrem-metadata/nodes__meta_service_option__property__checksum": "Checksum", "@sage/xtrem-metadata/nodes__meta_service_option__property__isActive": "Is active", "@sage/xtrem-metadata/nodes__meta_service_option__property__isHidden": "Is hidden", "@sage/xtrem-metadata/nodes__meta_service_option__property__isSubscribable": "Is subscribable", "@sage/xtrem-metadata/nodes__meta_service_option__property__name": "Name", "@sage/xtrem-metadata/nodes__meta_service_option__property__package": "Package", "@sage/xtrem-metadata/nodes__meta_service_option__property__title": "Title", "@sage/xtrem-metadata/package__name": "Sage xtrem metadata", "@sage/xtrem-metadata/pages__meta_node_factory____navigationPanel__listItem__line2__title": "Package", "@sage/xtrem-metadata/pages__meta_node_factory____navigationPanel__listItem__title__title": "Name", "@sage/xtrem-metadata/pages__meta_node_factory____navigationPanel__optionsMenu__title": "Can schedule", "@sage/xtrem-metadata/pages__meta_node_factory____navigationPanel__optionsMenu__title__2": "Can execute", "@sage/xtrem-metadata/pages__meta_node_factory____navigationPanel__optionsMenu__title__3": "All nodes with operations", "@sage/xtrem-metadata/pages__meta_node_factory____navigationPanel__optionsMenu__title__4": "All nodes", "@sage/xtrem-metadata/pages__meta_node_factory____navigationPanel__optionsMenu__title__5": "Abstract nodes", "@sage/xtrem-metadata/pages__meta_node_factory____objectTypePlural": "Node factories", "@sage/xtrem-metadata/pages__meta_node_factory____objectTypeSingular": "Node factory", "@sage/xtrem-metadata/pages__meta_node_factory____title": "Node factory", "@sage/xtrem-metadata/pages__meta_node_factory__extends____title": "Extends", "@sage/xtrem-metadata/pages__meta_node_factory__generalSection____title": "General", "@sage/xtrem-metadata/pages__meta_node_factory__isAbstract____title": "Abstract", "@sage/xtrem-metadata/pages__meta_node_factory__isActive____title": "Active", "@sage/xtrem-metadata/pages__meta_node_factory__isPublished____title": "Published", "@sage/xtrem-metadata/pages__meta_node_factory__name____title": "Name", "@sage/xtrem-metadata/pages__meta_node_factory__operations____columns__title__action": "Action", "@sage/xtrem-metadata/pages__meta_node_factory__operations____columns__title__kind": "Kind", "@sage/xtrem-metadata/pages__meta_node_factory__operations____columns__title__name": "Name", "@sage/xtrem-metadata/pages__meta_node_factory__operations____title": "Operations", "@sage/xtrem-metadata/pages__meta_node_factory__package____title": "Package", "@sage/xtrem-metadata/xtrem-object-invalid": "{{xtremObject}} is not a node."}