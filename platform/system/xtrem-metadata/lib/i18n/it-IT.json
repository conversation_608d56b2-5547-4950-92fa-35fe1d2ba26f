{"@sage/xtrem-metadata/data_types__checksum__name": "", "@sage/xtrem-metadata/data_types__meta_node_factory__name": "", "@sage/xtrem-metadata/data_types__meta_operation_action_enum__name": "", "@sage/xtrem-metadata/data_types__meta_operation_kind_enum__name": "", "@sage/xtrem-metadata/data_types__meta_package__name": "", "@sage/xtrem-metadata/data_types__meta_property_type_enum__name": "", "@sage/xtrem-metadata/data_types__meta_storage_enum__name": "", "@sage/xtrem-metadata/enums__meta_operation_action__requestUserNotification": "", "@sage/xtrem-metadata/enums__meta_operation_action__start": "", "@sage/xtrem-metadata/enums__meta_operation_action__stop": "", "@sage/xtrem-metadata/enums__meta_operation_action__track": "", "@sage/xtrem-metadata/enums__meta_operation_kind__asyncMutation": "", "@sage/xtrem-metadata/enums__meta_operation_kind__asyncTrackerQuery": "", "@sage/xtrem-metadata/enums__meta_operation_kind__bulkMutation": "", "@sage/xtrem-metadata/enums__meta_operation_kind__mutation": "", "@sage/xtrem-metadata/enums__meta_operation_kind__query": "", "@sage/xtrem-metadata/enums__meta_property_type__binaryStream": "", "@sage/xtrem-metadata/enums__meta_property_type__boolean": "", "@sage/xtrem-metadata/enums__meta_property_type__byte": "", "@sage/xtrem-metadata/enums__meta_property_type__collection": "", "@sage/xtrem-metadata/enums__meta_property_type__date": "", "@sage/xtrem-metadata/enums__meta_property_type__dateRange": "", "@sage/xtrem-metadata/enums__meta_property_type__datetime": "", "@sage/xtrem-metadata/enums__meta_property_type__datetimeRange": "", "@sage/xtrem-metadata/enums__meta_property_type__decimal": "", "@sage/xtrem-metadata/enums__meta_property_type__decimalRange": "", "@sage/xtrem-metadata/enums__meta_property_type__double": "", "@sage/xtrem-metadata/enums__meta_property_type__enum": "", "@sage/xtrem-metadata/enums__meta_property_type__enumArray": "", "@sage/xtrem-metadata/enums__meta_property_type__float": "", "@sage/xtrem-metadata/enums__meta_property_type__integer": "", "@sage/xtrem-metadata/enums__meta_property_type__integerArray": "", "@sage/xtrem-metadata/enums__meta_property_type__integerRange": "", "@sage/xtrem-metadata/enums__meta_property_type__json": "", "@sage/xtrem-metadata/enums__meta_property_type__jsonReference": "", "@sage/xtrem-metadata/enums__meta_property_type__reference": "", "@sage/xtrem-metadata/enums__meta_property_type__referenceArray": "", "@sage/xtrem-metadata/enums__meta_property_type__short": "", "@sage/xtrem-metadata/enums__meta_property_type__string": "", "@sage/xtrem-metadata/enums__meta_property_type__stringArray": "", "@sage/xtrem-metadata/enums__meta_property_type__textStream": "", "@sage/xtrem-metadata/enums__meta_property_type__time": "", "@sage/xtrem-metadata/enums__meta_property_type__uuid": "", "@sage/xtrem-metadata/enums__meta_storage__external": "", "@sage/xtrem-metadata/enums__meta_storage__json": "", "@sage/xtrem-metadata/enums__meta_storage__sql": "", "@sage/xtrem-metadata/node-extensions__sys_notification_state_extension__property__operation": "", "@sage/xtrem-metadata/nodes__meta_activity__asyncMutation__asyncExport": "", "@sage/xtrem-metadata/nodes__meta_activity__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-metadata/nodes__meta_activity__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-metadata/nodes__meta_activity__node_name": "", "@sage/xtrem-metadata/nodes__meta_activity__property__checksum": "", "@sage/xtrem-metadata/nodes__meta_activity__property__isActive": "", "@sage/xtrem-metadata/nodes__meta_activity__property__name": "", "@sage/xtrem-metadata/nodes__meta_activity__property__package": "", "@sage/xtrem-metadata/nodes__meta_activity__property__permissions": "", "@sage/xtrem-metadata/nodes__meta_activity__property__title": "", "@sage/xtrem-metadata/nodes__meta_activity_permission__asyncMutation__asyncExport": "", "@sage/xtrem-metadata/nodes__meta_activity_permission__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-metadata/nodes__meta_activity_permission__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-metadata/nodes__meta_activity_permission__node_name": "", "@sage/xtrem-metadata/nodes__meta_activity_permission__property__activity": "", "@sage/xtrem-metadata/nodes__meta_activity_permission__property__checksum": "", "@sage/xtrem-metadata/nodes__meta_activity_permission__property__isActive": "", "@sage/xtrem-metadata/nodes__meta_activity_permission__property__name": "", "@sage/xtrem-metadata/nodes__meta_activity_permission__property__package": "", "@sage/xtrem-metadata/nodes__meta_activity_permission__property__title": "", "@sage/xtrem-metadata/nodes__meta_data_type__asyncMutation__asyncExport": "", "@sage/xtrem-metadata/nodes__meta_data_type__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-metadata/nodes__meta_data_type__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-metadata/nodes__meta_data_type__node_name": "", "@sage/xtrem-metadata/nodes__meta_data_type__property__attributes": "", "@sage/xtrem-metadata/nodes__meta_data_type__property__checksum": "", "@sage/xtrem-metadata/nodes__meta_data_type__property__definingPackage": "", "@sage/xtrem-metadata/nodes__meta_data_type__property__isActive": "", "@sage/xtrem-metadata/nodes__meta_data_type__property__name": "", "@sage/xtrem-metadata/nodes__meta_data_type__property__serviceOptionNames": "", "@sage/xtrem-metadata/nodes__meta_data_type__property__serviceOptions": "", "@sage/xtrem-metadata/nodes__meta_data_type__property__title": "", "@sage/xtrem-metadata/nodes__meta_data_type__property__type": "", "@sage/xtrem-metadata/nodes__meta_node_factory__asyncMutation__asyncExport": "", "@sage/xtrem-metadata/nodes__meta_node_factory__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-metadata/nodes__meta_node_factory__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-metadata/nodes__meta_node_factory__node_name": "", "@sage/xtrem-metadata/nodes__meta_node_factory__property__checksum": "", "@sage/xtrem-metadata/nodes__meta_node_factory__property__customFields": "", "@sage/xtrem-metadata/nodes__meta_node_factory__property__extends": "", "@sage/xtrem-metadata/nodes__meta_node_factory__property__isAbstract": "", "@sage/xtrem-metadata/nodes__meta_node_factory__property__isActive": "", "@sage/xtrem-metadata/nodes__meta_node_factory__property__isClearedByReset": "", "@sage/xtrem-metadata/nodes__meta_node_factory__property__isCustomizable": "", "@sage/xtrem-metadata/nodes__meta_node_factory__property__isPlatformNode": "", "@sage/xtrem-metadata/nodes__meta_node_factory__property__isPublished": "", "@sage/xtrem-metadata/nodes__meta_node_factory__property__isSetupNode": "", "@sage/xtrem-metadata/nodes__meta_node_factory__property__isSharedByAllTenants": "", "@sage/xtrem-metadata/nodes__meta_node_factory__property__isSynchronizable": "", "@sage/xtrem-metadata/nodes__meta_node_factory__property__isSynchronized": "", "@sage/xtrem-metadata/nodes__meta_node_factory__property__isVitalCollectionChild": "", "@sage/xtrem-metadata/nodes__meta_node_factory__property__isVitalReferenceChild": "", "@sage/xtrem-metadata/nodes__meta_node_factory__property__name": "", "@sage/xtrem-metadata/nodes__meta_node_factory__property__naturalKey": "", "@sage/xtrem-metadata/nodes__meta_node_factory__property__notifies": "", "@sage/xtrem-metadata/nodes__meta_node_factory__property__operations": "", "@sage/xtrem-metadata/nodes__meta_node_factory__property__package": "", "@sage/xtrem-metadata/nodes__meta_node_factory__property__properties": "", "@sage/xtrem-metadata/nodes__meta_node_factory__property__serviceOptionNames": "", "@sage/xtrem-metadata/nodes__meta_node_factory__property__serviceOptions": "", "@sage/xtrem-metadata/nodes__meta_node_factory__property__storage": "", "@sage/xtrem-metadata/nodes__meta_node_factory__property__title": "", "@sage/xtrem-metadata/nodes__meta_node_operation__asyncMutation__asyncExport": "", "@sage/xtrem-metadata/nodes__meta_node_operation__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-metadata/nodes__meta_node_operation__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-metadata/nodes__meta_node_operation__node_name": "", "@sage/xtrem-metadata/nodes__meta_node_operation__property__action": "", "@sage/xtrem-metadata/nodes__meta_node_operation__property__checksum": "", "@sage/xtrem-metadata/nodes__meta_node_operation__property__factory": "", "@sage/xtrem-metadata/nodes__meta_node_operation__property__isActive": "", "@sage/xtrem-metadata/nodes__meta_node_operation__property__isMutation": "", "@sage/xtrem-metadata/nodes__meta_node_operation__property__isPublished": "", "@sage/xtrem-metadata/nodes__meta_node_operation__property__isSchedulable": "", "@sage/xtrem-metadata/nodes__meta_node_operation__property__kind": "", "@sage/xtrem-metadata/nodes__meta_node_operation__property__name": "", "@sage/xtrem-metadata/nodes__meta_node_operation__property__package": "", "@sage/xtrem-metadata/nodes__meta_node_operation__property__parameters": "", "@sage/xtrem-metadata/nodes__meta_node_operation__property__return": "", "@sage/xtrem-metadata/nodes__meta_node_operation__property__serviceOptionNames": "", "@sage/xtrem-metadata/nodes__meta_node_operation__property__serviceOptions": "", "@sage/xtrem-metadata/nodes__meta_node_operation__property__signature": "", "@sage/xtrem-metadata/nodes__meta_node_operation__property__title": "", "@sage/xtrem-metadata/nodes__meta_node_operation__property__topic": "", "@sage/xtrem-metadata/nodes__meta_node_property__asyncMutation__asyncExport": "", "@sage/xtrem-metadata/nodes__meta_node_property__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-metadata/nodes__meta_node_property__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-metadata/nodes__meta_node_property__node_name": "", "@sage/xtrem-metadata/nodes__meta_node_property__property__canFilter": "", "@sage/xtrem-metadata/nodes__meta_node_property__property__canSort": "", "@sage/xtrem-metadata/nodes__meta_node_property__property__checksum": "", "@sage/xtrem-metadata/nodes__meta_node_property__property__dataType": "", "@sage/xtrem-metadata/nodes__meta_node_property__property__dependencyIndex": "", "@sage/xtrem-metadata/nodes__meta_node_property__property__factory": "", "@sage/xtrem-metadata/nodes__meta_node_property__property__isActive": "", "@sage/xtrem-metadata/nodes__meta_node_property__property__isInherited": "", "@sage/xtrem-metadata/nodes__meta_node_property__property__isMutable": "", "@sage/xtrem-metadata/nodes__meta_node_property__property__isNullable": "", "@sage/xtrem-metadata/nodes__meta_node_property__property__isOnInputType": "", "@sage/xtrem-metadata/nodes__meta_node_property__property__isOnOutputType": "", "@sage/xtrem-metadata/nodes__meta_node_property__property__isPublished": "", "@sage/xtrem-metadata/nodes__meta_node_property__property__isRequired": "", "@sage/xtrem-metadata/nodes__meta_node_property__property__isStored": "", "@sage/xtrem-metadata/nodes__meta_node_property__property__isStoredOutput": "", "@sage/xtrem-metadata/nodes__meta_node_property__property__isTransientInput": "", "@sage/xtrem-metadata/nodes__meta_node_property__property__isVital": "", "@sage/xtrem-metadata/nodes__meta_node_property__property__isVitalParent": "", "@sage/xtrem-metadata/nodes__meta_node_property__property__name": "", "@sage/xtrem-metadata/nodes__meta_node_property__property__package": "", "@sage/xtrem-metadata/nodes__meta_node_property__property__reverseReference": "", "@sage/xtrem-metadata/nodes__meta_node_property__property__serviceOptionNames": "", "@sage/xtrem-metadata/nodes__meta_node_property__property__serviceOptions": "", "@sage/xtrem-metadata/nodes__meta_node_property__property__targetFactory": "", "@sage/xtrem-metadata/nodes__meta_node_property__property__title": "", "@sage/xtrem-metadata/nodes__meta_node_property__property__type": "", "@sage/xtrem-metadata/nodes__meta_package__asyncMutation__asyncExport": "", "@sage/xtrem-metadata/nodes__meta_package__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-metadata/nodes__meta_package__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-metadata/nodes__meta_package__node_name": "", "@sage/xtrem-metadata/nodes__meta_package__property__checksum": "", "@sage/xtrem-metadata/nodes__meta_package__property__isActive": "", "@sage/xtrem-metadata/nodes__meta_package__property__name": "", "@sage/xtrem-metadata/nodes__meta_package__property__title": "", "@sage/xtrem-metadata/nodes__meta_service_option__asyncMutation__asyncExport": "", "@sage/xtrem-metadata/nodes__meta_service_option__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-metadata/nodes__meta_service_option__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-metadata/nodes__meta_service_option__node_name": "", "@sage/xtrem-metadata/nodes__meta_service_option__property__checksum": "", "@sage/xtrem-metadata/nodes__meta_service_option__property__isActive": "", "@sage/xtrem-metadata/nodes__meta_service_option__property__isHidden": "", "@sage/xtrem-metadata/nodes__meta_service_option__property__isSubscribable": "", "@sage/xtrem-metadata/nodes__meta_service_option__property__name": "", "@sage/xtrem-metadata/nodes__meta_service_option__property__package": "", "@sage/xtrem-metadata/nodes__meta_service_option__property__title": "", "@sage/xtrem-metadata/package__name": "", "@sage/xtrem-metadata/pages__meta_node_factory____navigationPanel__listItem__line2__title": "", "@sage/xtrem-metadata/pages__meta_node_factory____navigationPanel__listItem__title__title": "", "@sage/xtrem-metadata/pages__meta_node_factory____navigationPanel__optionsMenu__title": "", "@sage/xtrem-metadata/pages__meta_node_factory____navigationPanel__optionsMenu__title__2": "", "@sage/xtrem-metadata/pages__meta_node_factory____navigationPanel__optionsMenu__title__3": "", "@sage/xtrem-metadata/pages__meta_node_factory____navigationPanel__optionsMenu__title__4": "", "@sage/xtrem-metadata/pages__meta_node_factory____navigationPanel__optionsMenu__title__5": "", "@sage/xtrem-metadata/pages__meta_node_factory____objectTypePlural": "", "@sage/xtrem-metadata/pages__meta_node_factory____objectTypeSingular": "", "@sage/xtrem-metadata/pages__meta_node_factory____title": "", "@sage/xtrem-metadata/pages__meta_node_factory__extends____title": "", "@sage/xtrem-metadata/pages__meta_node_factory__generalSection____title": "", "@sage/xtrem-metadata/pages__meta_node_factory__isAbstract____title": "", "@sage/xtrem-metadata/pages__meta_node_factory__isActive____title": "", "@sage/xtrem-metadata/pages__meta_node_factory__isPublished____title": "", "@sage/xtrem-metadata/pages__meta_node_factory__name____title": "", "@sage/xtrem-metadata/pages__meta_node_factory__operations____columns__title__action": "", "@sage/xtrem-metadata/pages__meta_node_factory__operations____columns__title__kind": "", "@sage/xtrem-metadata/pages__meta_node_factory__operations____columns__title__name": "", "@sage/xtrem-metadata/pages__meta_node_factory__operations____title": "", "@sage/xtrem-metadata/pages__meta_node_factory__package____title": "", "@sage/xtrem-metadata/xtrem-object-invalid": ""}