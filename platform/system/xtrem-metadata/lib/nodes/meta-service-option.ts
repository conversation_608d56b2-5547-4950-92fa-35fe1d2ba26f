import { decorators, Node, Reference } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremMetadata from '../index';
import { MetaNode } from '../services/metadata-upgrade';

@decorators.node<MetaServiceOption>({
    isPublished: true,
    storage: 'sql',
    isSharedByAllTenants: true,
    isCached: true,
    isPlatformNode: true,
    canCreate: true,
    canRead: true,
    canUpdate: true,
    canDeleteMany: true,
    indexes: [
        {
            orderBy: { name: +1 },
            isUnique: true,
            isNaturalKey: true,
        },
    ],
    isFrozen: true,
    authorizedBy: () => {
        return { sites: null, accessCodes: null, status: 'authorized' };
    },
})
export class MetaServiceOption extends Node implements MetaNode {
    @decorators.stringProperty<MetaServiceOption, 'name'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.name,
    })
    readonly name: Promise<string>;

    @decorators.stringProperty<MetaServiceOption, 'title'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.localizedName,
    })
    readonly title: Promise<string>;

    @decorators.referenceProperty<MetaServiceOption, 'package'>({
        isStored: true,
        isPublished: true,
        node: () => xtremMetadata.nodes.MetaPackage,
    })
    readonly package: Reference<xtremMetadata.nodes.MetaPackage>;

    @decorators.booleanProperty<MetaServiceOption, 'isSubscribable'>({
        isStored: true,
        isPublished: true,
    })
    readonly isSubscribable: Promise<boolean>;

    @decorators.booleanProperty<MetaServiceOption, 'isActive'>({
        isStored: true,
        isPublished: true,
    })
    readonly isActive: Promise<boolean>;

    @decorators.booleanProperty<MetaServiceOption, 'isHidden'>({
        isStored: true,
        isPublished: true,
    })
    readonly isHidden: Promise<boolean>;

    @decorators.stringProperty<MetaServiceOption, 'checksum'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMetadata.dataTypes.checksum,
    })
    readonly checksum: Promise<string>;
}
