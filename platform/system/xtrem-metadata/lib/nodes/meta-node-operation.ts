import { decorators, Node, PlainParameter, Reference } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremMetadata from '..';
import { MetaNode } from '../services/metadata-upgrade';

@decorators.node<MetaNodeOperation>({
    isPublished: true,
    storage: 'sql',
    isSharedByAllTenants: true,
    isCached: true,
    isPlatformNode: true,
    isVitalCollectionChild: true,
    canCreate: true,
    canRead: true,
    canUpdate: true,
    canDeleteMany: true,
    indexes: [
        {
            orderBy: { factory: +1, name: +1, action: +1 },
            isUnique: true,
            isNaturalKey: true,
        },
    ],
    isFrozen: true,
})
export class MetaNodeOperation extends Node implements MetaNode {
    @decorators.referenceProperty<MetaNodeOperation, 'factory'>({
        isStored: true,
        isPublished: true,
        isVitalParent: true,
        node: () => xtremMetadata.nodes.MetaNodeFactory,
    })
    readonly factory: Reference<xtremMetadata.nodes.MetaNodeFactory>;

    @decorators.stringProperty<MetaNodeOperation, 'name'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.name,
    })
    readonly name: Promise<string>;

    @decorators.stringProperty<MetaNodeOperation, 'title'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.localizedName,
    })
    readonly title: Promise<string>;

    @decorators.referenceProperty<MetaNodeOperation, 'package'>({
        isStored: true,
        isPublished: true,
        node: () => xtremMetadata.nodes.MetaPackage,
        // TODO: remove
        isNullable: true,
    })
    readonly package: Reference<xtremMetadata.nodes.MetaPackage>;

    @decorators.jsonProperty<MetaNodeOperation, 'serviceOptionNames'>({
        isStored: true,
        isPublished: true,
    })
    readonly serviceOptionNames: Promise<string[]>;

    @decorators.referenceArrayProperty<MetaNodeOperation, 'serviceOptions'>({
        isPublished: true,
        dependsOn: ['serviceOptionNames'],
        node: () => xtremMetadata.nodes.MetaServiceOption,
        async computeValue() {
            const serviceOptionNames = await this.serviceOptionNames;
            return this.$.context
                .query(xtremMetadata.nodes.MetaServiceOption, {
                    filter: { name: { _in: serviceOptionNames } },
                })
                .toArray();
        },
    })
    readonly serviceOptions: Promise<xtremMetadata.nodes.MetaServiceOption[]>;

    @decorators.booleanProperty<MetaNodeOperation, 'isActive'>({
        isStored: true,
        isPublished: true,
        provides: ['isActive'],
    })
    readonly isActive: Promise<boolean>;

    @decorators.enumProperty<MetaNodeOperation, 'kind'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMetadata.enums.operationKindDataType,
    })
    readonly kind: Promise<xtremMetadata.enums.MetaOperationKind>;

    @decorators.enumProperty<MetaNodeOperation, 'action'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        allowedInUniqueIndex: true,
        dataType: () => xtremMetadata.enums.operationActionDataType,
    })
    readonly action: Promise<xtremMetadata.enums.MetaOperationAction | null>;

    @decorators.booleanProperty<MetaNodeOperation, 'isPublished'>({
        isStored: true,
        isPublished: true,
    })
    readonly isPublished: Promise<boolean>;

    @decorators.booleanProperty<MetaNodeOperation, 'isSchedulable'>({
        isStored: true,
        isPublished: true,
    })
    readonly isSchedulable: Promise<boolean>;

    @decorators.booleanProperty<MetaNodeOperation, 'isMutation'>({
        isStored: true,
        isPublished: true,
    })
    readonly isMutation: Promise<boolean>;

    @decorators.jsonProperty<MetaNodeOperation, 'parameters'>({
        isPublished: true,
        async getValue() {
            return (await this.signature).parameters;
        },
    })
    readonly parameters: Promise<PlainParameter[]>;

    @decorators.jsonProperty<MetaNodeOperation, 'return'>({
        isPublished: true,
        async getValue() {
            return (await this.signature).return;
        },
    })
    readonly return: Promise<object>;

    @decorators.jsonProperty<MetaNodeOperation, 'signature'>({
        isStored: true,
        isPublished: true,
    })
    readonly signature: Promise<{ parameters: PlainParameter[]; return: object }>;

    @decorators.stringProperty<MetaNodeOperation, 'checksum'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMetadata.dataTypes.checksum,
    })
    readonly checksum: Promise<string>;

    @decorators.stringProperty<MetaNodeOperation, 'topic'>({
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.name,
        async getValue() {
            return `${await (await this.factory).name}/${await this.name}`;
        },
    })
    readonly topic: Promise<string>;
}
