import { decorators, Node, Reference } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremMetadata from '../index';
import { MetaNode } from '../services/metadata-upgrade';

@decorators.node<MetaActivityPermission>({
    isPublished: true,
    storage: 'sql',
    isSharedByAllTenants: true,
    isCached: true,
    isPlatformNode: true,
    isVitalCollectionChild: true,
    canCreate: true,
    canRead: true,
    canUpdate: true,
    canDeleteMany: true,
    indexes: [
        {
            orderBy: { activity: +1, name: +1 },
            isUnique: true,
            isNaturalKey: true,
        },
    ],
    isFrozen: true,
})
export class MetaActivityPermission extends Node implements MetaNode {
    @decorators.referenceProperty<MetaActivityPermission, 'activity'>({
        isStored: true,
        isPublished: true,
        isVitalParent: true,
        node: () => xtremMetadata.nodes.MetaActivity,
    })
    readonly activity: Reference<xtremMetadata.nodes.MetaActivity>;

    @decorators.stringProperty<MetaActivityPermission, 'name'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.name,
    })
    readonly name: Promise<string>;

    @decorators.stringProperty<MetaActivityPermission, 'title'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.localizedName,
    })
    readonly title: Promise<string>;

    @decorators.referenceProperty<MetaActivityPermission, 'package'>({
        isStored: true,
        isPublished: true,
        node: () => xtremMetadata.nodes.MetaPackage,
        // TODO: remove
        isNullable: true,
    })
    readonly package: Reference<xtremMetadata.nodes.MetaPackage>;

    @decorators.booleanProperty<MetaActivityPermission, 'isActive'>({
        isStored: true,
        isPublished: true,
        provides: ['isActive'],
    })
    readonly isActive: Promise<boolean>;

    @decorators.stringProperty<MetaActivityPermission, 'checksum'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMetadata.dataTypes.checksum,
    })
    readonly checksum: Promise<string>;
}
