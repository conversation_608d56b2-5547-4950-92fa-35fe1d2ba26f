import { BusinessRuleError, Collection, CoreHooks, decorators, Dict, Node, Reference } from '@sage/xtrem-core';
import { MetaCustomField } from '@sage/xtrem-shared';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremMetadata from '../index';
import { MetaNode } from '../services/metadata-upgrade';

@decorators.node<MetaNodeFactory>({
    isPublished: true,
    storage: 'sql',
    isSharedByAllTenants: true,
    isCached: true,
    isPlatformNode: true,
    canCreate: true,
    canRead: true,
    canUpdate: true,
    canDeleteMany: true,
    indexes: [
        {
            orderBy: { name: +1 },
            isUnique: true,
            isNaturalKey: true,
        },
    ],
    isFrozen: true,
    authorizedBy: () => {
        return { sites: null, accessCodes: null, status: 'authorized' };
    },
})
export class MetaNodeFactory extends Node implements MetaNode {
    @decorators.stringProperty<MetaNodeFactory, 'name'>({
        isStored: true,
        isPublished: true,

        dataType: () => xtremSystem.dataTypes.name,
    })
    readonly name: Promise<string>;

    @decorators.stringProperty<MetaNodeFactory, 'title'>({
        isStored: true,
        isPublished: true,

        dataType: () => xtremSystem.dataTypes.localizedName,
    })
    readonly title: Promise<string>;

    @decorators.referenceProperty<MetaNodeFactory, 'package'>({
        isStored: true,
        isPublished: true,

        node: () => xtremMetadata.nodes.MetaPackage,
        // TODO: remove
        isNullable: true,
    })
    readonly package: Reference<xtremMetadata.nodes.MetaPackage>;

    @decorators.booleanProperty<MetaNodeFactory, 'isActive'>({
        isStored: true,
        isPublished: true,
        provides: ['isActive'],
    })
    readonly isActive: Promise<boolean>;

    @decorators.booleanProperty<MetaNodeFactory, 'isPublished'>({
        isStored: true,
        isPublished: true,
    })
    readonly isPublished: Promise<boolean>;

    @decorators.booleanProperty<MetaNodeFactory, 'isAbstract'>({
        isStored: true,
        isPublished: true,
    })
    readonly isAbstract: Promise<boolean>;

    @decorators.referenceProperty<MetaNodeFactory, 'extends'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => MetaNodeFactory,
    })
    readonly extends: Reference<MetaNodeFactory>;

    @decorators.enumProperty<MetaNodeFactory, 'storage'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMetadata.enums.storageDataType,
    })
    readonly storage: Promise<xtremMetadata.enums.MetaStorage>;

    @decorators.booleanProperty<MetaNodeFactory, 'isSharedByAllTenants'>({
        isStored: true,
        isPublished: true,
    })
    readonly isSharedByAllTenants: Promise<boolean>;

    @decorators.booleanProperty<MetaNodeFactory, 'isSetupNode'>({
        isStored: true,
        isPublished: true,
    })
    readonly isSetupNode: Promise<boolean>;

    @decorators.booleanProperty<MetaNodeFactory, 'isPlatformNode'>({
        isStored: true,
        isPublished: true,
    })
    readonly isPlatformNode: Promise<boolean>;

    @decorators.booleanProperty<MetaNodeFactory, 'isVitalReferenceChild'>({
        isStored: true,
        isPublished: true,
    })
    readonly isVitalReferenceChild: Promise<boolean>;

    @decorators.booleanProperty<MetaNodeFactory, 'isVitalCollectionChild'>({
        isStored: true,
        isPublished: true,
    })
    readonly isVitalCollectionChild: Promise<boolean>;

    @decorators.booleanProperty<MetaNodeFactory, 'isCustomizable'>({
        isStored: true,
        isPublished: true,
    })
    readonly isCustomizable: Promise<boolean>;

    @decorators.booleanProperty<MetaNodeFactory, 'isSynchronizable'>({
        isStored: true,
        isPublished: true,
    })
    readonly isSynchronizable: Promise<boolean>;

    @decorators.booleanProperty<MetaNodeFactory, 'isSynchronized'>({
        isStored: true,
        isPublished: true,
    })
    readonly isSynchronized: Promise<boolean>;

    @decorators.booleanProperty<MetaNodeFactory, 'isClearedByReset'>({
        isStored: true,
        isPublished: true,
    })
    readonly isClearedByReset: Promise<boolean>;

    @decorators.stringProperty<MetaNodeFactory, 'checksum'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMetadata.dataTypes.checksum,
    })
    readonly checksum: Promise<string>;

    @decorators.jsonProperty<MetaNodeFactory, 'serviceOptionNames'>({
        isStored: true,
        isPublished: true,
    })
    readonly serviceOptionNames: Promise<Dict<string>>;

    @decorators.referenceArrayProperty<MetaNodeFactory, 'serviceOptions'>({
        isPublished: true,
        node: () => xtremMetadata.nodes.MetaServiceOption,
        isNullable: true,
        async computeValue() {
            return this.$.context.query(xtremMetadata.nodes.MetaServiceOption, {
                filter: { name: { _in: Object.values(await this.serviceOptionNames) } },
            });
        },
    })
    readonly serviceOptions: Promise<xtremMetadata.nodes.MetaServiceOption[] | null>;

    @decorators.jsonProperty<MetaNodeFactory, 'naturalKey'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
    })
    readonly naturalKey: Promise<string[] | null>;

    @decorators.jsonProperty<MetaNodeFactory, 'customFields'>({
        isPublished: true,
        async computeValue() {
            if (CoreHooks.customizationManager) {
                const name = await this.name;
                const factory = this.$.context.application.tryToGetFactoryByName(name);
                if (!factory) return [];
                const { fullName } = factory;
                const customFieldDict = await CoreHooks.customizationManager.getCustomFields(this.$.context, [
                    fullName,
                ]);
                return customFieldDict?.[fullName] ?? [];
            }
            return [];
        },
    })
    readonly customFields: Promise<MetaCustomField[]>;

    @decorators.jsonProperty<MetaNodeFactory, 'notifies'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
    })
    readonly notifies: Promise<string[] | null>;

    // canXxx flags
    // indexes

    @decorators.collectionProperty<MetaNodeFactory, 'properties'>({
        isPublished: true,
        isVital: true,
        node: () => xtremMetadata.nodes.MetaNodeProperty,
        reverseReference: 'factory',
    })
    readonly properties: Collection<xtremMetadata.nodes.MetaNodeProperty>;

    @decorators.collectionProperty<MetaNodeFactory, 'operations'>({
        isPublished: true,
        isVital: true,
        node: () => xtremMetadata.nodes.MetaNodeOperation,
        reverseReference: 'factory',
    })
    readonly operations: Collection<xtremMetadata.nodes.MetaNodeOperation>;

    async getNode(): Promise<typeof Node> {
        const node = this.$.context.application.tryToGetFactoryByName(await this.name)?.nodeConstructor;
        if (!node) {
            throw new BusinessRuleError(
                this.$.context.localize('@sage/xtrem-metadata/xtrem-object-invalid', '{{xtremObject}} is not a node.', {
                    xtremObject: await this.name,
                }),
            );
        }
        return node;
    }
}
