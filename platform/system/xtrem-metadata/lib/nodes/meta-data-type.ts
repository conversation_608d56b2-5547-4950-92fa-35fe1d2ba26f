import {
    DecimalDataTypeOptions,
    Dict,
    EnumDataTypeOptions,
    JsonDataTypeOptions,
    Node,
    Reference,
    ReferenceDataTypeOptions,
    StreamDataTypeOptions,
    StringDataTypeOptions,
    decorators,
} from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremMetadata from '../index';
import { MetaNode } from '../services/metadata-upgrade';

type DataTypeAttributes =
    | (
          | StringDataTypeOptions
          | DecimalDataTypeOptions
          | JsonDataTypeOptions
          | StreamDataTypeOptions
          | EnumDataTypeOptions
          | ReferenceDataTypeOptions<Node, Node>
      )
    | null;
@decorators.node<MetaDataType>({
    isPublished: true,
    storage: 'sql',
    isSharedByAllTenants: true,
    isCached: true,
    isPlatformNode: true,
    canCreate: true,
    canRead: true,
    canUpdate: true,
    canDeleteMany: true,
    indexes: [
        {
            orderBy: { name: +1 },
            isUnique: true,
            isNaturalKey: true,
        },
    ],
    isFrozen: true,
})
export class MetaDataType extends Node implements MetaNode {
    @decorators.referenceProperty<MetaDataType, 'definingPackage'>({
        isStored: true,
        isPublished: true,
        node: () => xtremMetadata.nodes.MetaPackage,
    })
    readonly definingPackage: Reference<xtremMetadata.nodes.MetaPackage>;

    @decorators.stringProperty<MetaDataType, 'name'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.name,
    })
    readonly name: Promise<string>;

    @decorators.stringProperty<MetaDataType, 'title'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.localizedTitle,
    })
    readonly title: Promise<string>;

    @decorators.enumProperty<MetaDataType, 'type'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMetadata.enums.MetaPropertyTypeDataType,
    })
    readonly type: Promise<xtremMetadata.enums.MetaPropertyType>;

    @decorators.jsonProperty<MetaDataType, 'attributes'>({
        isStored: true,
        isNullable: true,
        isPublished: true,
    })
    readonly attributes: Promise<DataTypeAttributes>;

    @decorators.jsonProperty<MetaDataType, 'serviceOptionNames'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
    })
    readonly serviceOptionNames: Promise<Dict<string>>;

    @decorators.referenceArrayProperty<MetaDataType, 'serviceOptions'>({
        isPublished: true,
        isNullable: true,
        node: () => xtremMetadata.nodes.MetaServiceOption,
        async computeValue() {
            return this.$.context.query(xtremMetadata.nodes.MetaServiceOption, {
                filter: { name: { _in: Object.values(await this.serviceOptionNames) } },
            });
        },
    })
    readonly serviceOptions: Promise<xtremMetadata.nodes.MetaServiceOption[]>;

    @decorators.booleanProperty<MetaDataType, 'isActive'>({
        isStored: true,
        isPublished: true,
        provides: ['isActive'],
    })
    readonly isActive: Promise<boolean>;

    @decorators.stringProperty<MetaDataType, 'checksum'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMetadata.dataTypes.checksum,
    })
    readonly checksum: Promise<string>;
}
