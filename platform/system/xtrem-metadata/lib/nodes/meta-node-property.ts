import { decorators, Dict, integer, Node, Reference } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremMetadata from '../index';
import { MetaNode } from '../services/metadata-upgrade';
import { MetaDataType } from './meta-data-type';

@decorators.node<MetaNodeProperty>({
    isPublished: true,
    storage: 'sql',
    isSharedByAllTenants: true,
    isCached: true,
    isPlatformNode: true,
    isVitalCollectionChild: true,
    canCreate: true,
    canRead: true,
    canUpdate: true,
    canDeleteMany: true,
    indexes: [
        {
            orderBy: { factory: +1, name: +1 },
            isUnique: true,
            isNaturalKey: true,
        },
    ],
    isFrozen: true,
})
export class MetaNodeProperty extends Node implements MetaNode {
    @decorators.referenceProperty<MetaNodeProperty, 'factory'>({
        isStored: true,
        isPublished: true,
        isVitalParent: true,
        node: () => xtremMetadata.nodes.MetaNodeFactory,
    })
    readonly factory: Reference<xtremMetadata.nodes.MetaNodeFactory>;

    @decorators.stringProperty<MetaNodeProperty, 'name'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.name,
    })
    readonly name: Promise<string>;

    @decorators.stringProperty<MetaNodeProperty, 'title'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.localizedName,
    })
    readonly title: Promise<string>;

    @decorators.referenceProperty<MetaNodeProperty, 'package'>({
        isStored: true,
        isPublished: true,
        node: () => xtremMetadata.nodes.MetaPackage,
        // TODO: remove
        isNullable: true,
    })
    readonly package: Reference<xtremMetadata.nodes.MetaPackage>;

    @decorators.jsonProperty<MetaNodeProperty, 'serviceOptionNames'>({
        isStored: true,
        isPublished: true,
    })
    readonly serviceOptionNames: Promise<Dict<string>>;

    @decorators.referenceArrayProperty<MetaNodeProperty, 'serviceOptions'>({
        isPublished: true,
        node: () => xtremMetadata.nodes.MetaServiceOption,
        async computeValue() {
            return this.$.context.query(xtremMetadata.nodes.MetaServiceOption, {
                filter: { name: { _in: Object.values(await this.serviceOptionNames) } },
            });
        },
    })
    readonly serviceOptions: Promise<xtremMetadata.nodes.MetaServiceOption[]>;

    @decorators.booleanProperty<MetaNodeProperty, 'isActive'>({
        isStored: true,
        isPublished: true,
        provides: ['isActive'],
    })
    readonly isActive: Promise<boolean>;

    @decorators.enumProperty<MetaNodeProperty, 'type'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMetadata.enums.MetaPropertyTypeDataType,
    })
    readonly type: Promise<xtremMetadata.enums.MetaPropertyType>;

    @decorators.referenceProperty<MetaNodeProperty, 'dataType'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => MetaDataType,
    })
    readonly dataType: Reference<MetaDataType>;

    @decorators.integerProperty<MetaNodeProperty, 'dependencyIndex'>({
        isStored: true,
        isPublished: true,
    })
    readonly dependencyIndex: Promise<integer>;

    @decorators.booleanProperty<MetaNodeProperty, 'isPublished'>({
        isStored: true,
        isPublished: true,
    })
    readonly isPublished: Promise<boolean>;

    @decorators.booleanProperty<MetaNodeProperty, 'isStored'>({
        isStored: true,
        isPublished: true,
    })
    readonly isStored: Promise<boolean>;

    @decorators.booleanProperty<MetaNodeProperty, 'isTransientInput'>({
        isStored: true,
        isPublished: true,
    })
    readonly isTransientInput: Promise<boolean>;

    @decorators.booleanProperty<MetaNodeProperty, 'isStoredOutput'>({
        isStored: true,
        isPublished: true,
    })
    readonly isStoredOutput: Promise<boolean>;

    @decorators.booleanProperty<MetaNodeProperty, 'isRequired'>({
        isStored: true,
        isPublished: true,
    })
    readonly isRequired: Promise<boolean>;

    @decorators.booleanProperty<MetaNodeProperty, 'isNullable'>({
        isStored: true,
        isPublished: true,
    })
    readonly isNullable: Promise<boolean>;

    @decorators.referenceProperty<MetaNodeProperty, 'targetFactory'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => xtremMetadata.nodes.MetaNodeFactory,
    })
    readonly targetFactory: Reference<xtremMetadata.nodes.MetaNodeFactory>;

    @decorators.referenceProperty<MetaNodeProperty, 'reverseReference'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => MetaNodeProperty,
    })
    readonly reverseReference: Reference<MetaNodeProperty>;

    @decorators.booleanProperty<MetaNodeProperty, 'isVital'>({
        isStored: true,
        isPublished: true,
    })
    readonly isVital: Promise<boolean>;

    @decorators.booleanProperty<MetaNodeProperty, 'isVitalParent'>({
        isStored: true,
        isPublished: true,
    })
    readonly isVitalParent: Promise<boolean>;

    @decorators.booleanProperty<MetaNodeProperty, 'isInherited'>({
        isStored: true,
        isPublished: true,
    })
    readonly isInherited: Promise<boolean>;

    @decorators.booleanProperty<MetaNodeProperty, 'canSort'>({
        isStored: true,
        isPublished: true,
    })
    readonly canSort: Promise<boolean>;

    @decorators.booleanProperty<MetaNodeProperty, 'canFilter'>({
        isStored: true,
        isPublished: true,
    })
    readonly canFilter: Promise<boolean>;

    @decorators.booleanProperty<MetaNodeProperty, 'isOnInputType'>({
        isStored: true,
        isPublished: true,
    })
    readonly isOnInputType: Promise<boolean>;

    @decorators.booleanProperty<MetaNodeProperty, 'isOnOutputType'>({
        isStored: true,
        isPublished: true,
    })
    readonly isOnOutputType: Promise<boolean>;

    @decorators.booleanProperty<MetaNodeProperty, 'isMutable'>({
        isStored: true,
        isPublished: true,
    })
    readonly isMutable: Promise<boolean>;

    @decorators.stringProperty<MetaNodeProperty, 'checksum'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMetadata.dataTypes.checksum,
    })
    readonly checksum: Promise<string>;
}
