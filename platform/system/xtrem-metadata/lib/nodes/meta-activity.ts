import { Collection, decorators, Node, Reference } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremMetadata from '../index';
import { MetaNode } from '../services/metadata-upgrade';

@decorators.node<MetaActivity>({
    isPublished: true,
    storage: 'sql',
    isSharedByAllTenants: true,
    isCached: true,
    isPlatformNode: true,
    canCreate: true,
    canRead: true,
    canUpdate: true,
    canDeleteMany: true,
    indexes: [
        {
            orderBy: { name: +1 },
            isUnique: true,
            isNaturalKey: true,
        },
    ],
    isFrozen: true,
    authorizedBy: () => {
        return { sites: null, accessCodes: null, status: 'authorized' };
    },
})
export class MetaActivity extends Node implements MetaNode {
    @decorators.stringProperty<MetaActivity, 'name'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.name,
    })
    readonly name: Promise<string>;

    @decorators.stringProperty<MetaActivity, 'title'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.localizedName,
    })
    readonly title: Promise<string>;

    @decorators.referenceProperty<MetaActivity, 'package'>({
        isStored: true,
        isPublished: true,
        node: () => xtremMetadata.nodes.MetaPackage,
        // TODO: remove
        isNullable: true,
    })
    readonly package: Reference<xtremMetadata.nodes.MetaPackage>;

    @decorators.booleanProperty<MetaActivity, 'isActive'>({
        isStored: true,
        isPublished: true,
        provides: ['isActive'],
    })
    readonly isActive: Promise<boolean>;

    @decorators.stringProperty<MetaActivity, 'checksum'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMetadata.dataTypes.checksum,
    })
    readonly checksum: Promise<string>;

    @decorators.collectionProperty<MetaActivity, 'permissions'>({
        isPublished: true,
        isVital: true,
        node: () => xtremMetadata.nodes.MetaActivityPermission,
        reverseReference: 'activity',
    })
    readonly permissions: Collection<xtremMetadata.nodes.MetaActivityPermission>;
}
