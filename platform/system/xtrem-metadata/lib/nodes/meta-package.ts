import { decorators, Node } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremMetadata from '../index';
import { MetaNode } from '../services/metadata-upgrade';

@decorators.node<MetaPackage>({
    isPublished: true,
    storage: 'sql',
    isSharedByAllTenants: true,
    isCached: true,
    isPlatformNode: true,
    canCreate: true,
    canRead: true,
    canUpdate: true,
    canDeleteMany: true,
    indexes: [
        {
            orderBy: { name: +1 },
            isUnique: true,
            isNaturalKey: true,
        },
    ],
    isFrozen: true,
    authorizedBy: () => {
        return { sites: null, accessCodes: null, status: 'authorized' };
    },
})
export class MetaPackage extends Node implements MetaNode {
    @decorators.stringProperty<MetaPackage, 'name'>({
        isStored: true,
        isPublished: true,

        dataType: () => xtremSystem.dataTypes.name,
    })
    readonly name: Promise<string>;

    @decorators.stringProperty<MetaPackage, 'title'>({
        isStored: true,
        isPublished: true,

        dataType: () => xtremSystem.dataTypes.localizedName,
    })
    readonly title: Promise<string>;

    @decorators.booleanProperty<MetaPackage, 'isActive'>({
        isStored: true,
        isPublished: true,
    })
    readonly isActive: Promise<boolean>;

    @decorators.stringProperty<MetaPackage, 'checksum'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMetadata.dataTypes.checksum,
    })
    readonly checksum: Promise<string>;
}
