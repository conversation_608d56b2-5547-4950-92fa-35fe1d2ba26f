import * as xtremCommunication from '@sage/xtrem-communication';
import { decorators, NodeExtension, Reference } from '@sage/xtrem-core';
import * as xtremMetadata from '..';

@decorators.nodeExtension<SysNotificationStateExtension>({
    extends: () => xtremCommunication.nodes.SysNotificationState,
    async saveBegin() {
        const envelope = await this.envelope;
        if (envelope && !(await this.operation)) {
            const { topic } = envelope.attributes;
            const topicArray = topic.split('/');
            const factory = this.$.context.application.tryToGetFactoryByName(topicArray[0]);
            if (factory) {
                const operation = [...factory.queries, ...factory.mutations].find(op => op.name === topicArray[1]);
                if (operation) {
                    /** operation natural key is factory, name, action */
                    await this.$.set({
                        operation: `${factory.name}|${operation.name}|${topicArray[2]}`,
                    });
                }
            }
        }
    },
})
export class SysNotificationStateExtension extends NodeExtension<xtremCommunication.nodes.SysNotificationState> {
    @decorators.referenceProperty<SysNotificationStateExtension, 'operation'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => xtremMetadata.nodes.MetaNodeOperation,
        filters: {
            lookup: {
                async serviceOptionNames() {
                    const activeServiceOptions = await this.$.context.activeServiceOptions;
                    const activeServiceOptionNames = activeServiceOptions.map(
                        activeServiceOption => activeServiceOption.name,
                    );
                    return { _containedBy: activeServiceOptionNames };
                },
            },
        },
    })
    readonly operation: Reference<xtremMetadata.nodes.MetaNodeOperation | null>;
}

declare module '@sage/xtrem-communication/lib/nodes/sys-notification-state' {
    export interface SysNotificationState extends SysNotificationStateExtension {}
}
