import { NodeUpdateData, Test } from '@sage/xtrem-core';
import { addBundleLocalizationKeys } from '@sage/xtrem-i18n';
import { Dict } from '@sage/xtrem-shared';
import { assert } from 'chai';
import * as fs from 'fs';
import * as _ from 'lodash';
import * as fsp from 'path';
import * as xtremMetadata from '../../../lib';

const expectedPayload = {
    name: 'TestDocument',
    isActive: true,
    storage: 'sql',
    isPublished: true,
    isAbstract: false,
    isVitalCollectionChild: false,
    isVitalReferenceChild: false,
    isClearedByReset: true,
    properties: [
        {
            name: 'id',
            type: 'string',
            isActive: true,
            isPublished: true,
            isNullable: false,
            isStored: true,
            isVital: false,
            isVitalParent: false,
            reverseReference: null,
        },
        {
            name: 'tool',
            serviceOptionNames: ['testTools'],
            isActive: true,
            type: 'string',
            isPublished: true,
            isStored: true,
        },
        {
            name: 'date',
            type: 'date',
            isActive: true,
            isPublished: true,
            isNullable: false,
            isStored: true,
            isVital: false,
            isVitalParent: false,
            reverseReference: null,
        },
        {
            name: 'lines',
            type: 'collection',
            isActive: true,
            isPublished: true,
            isNullable: false,
            isVital: true,
            isVitalParent: false,
            reverseReference: null,
        },
    ],
    operations: [],
};

const loadI18nDir = (i18nDir: string) => {
    if (fs.existsSync(i18nDir)) {
        fs.readdirSync(i18nDir)
            .filter(f => f.endsWith('.json'))
            .forEach(f => {
                const locale = f.replace('.json', '');
                // eslint-disable-next-line global-require, import/no-dynamic-require
                const content = require(fsp.join(i18nDir, f)) as Dict<string>;
                const data: any = {};
                data[locale] = content;
                addBundleLocalizationKeys(data);
            });
    }
};

// load i18n files for xtrem-core test
loadI18nDir(fsp.resolve('test', 'fixtures', 'lib', 'i18n'));

describe('metadata upgrade', () => {
    it('can restore metadata with upgrade', async () => {
        // Read the metadata for factory TestDocument and test that it matches the expected payload above
        await Test.withReadonlyContext(async context => {
            const documentFactory = await context.read(xtremMetadata.nodes.MetaNodeFactory, { name: 'TestDocument' });
            const payload = await documentFactory.$.payload();
            assert.deepEqual(payload, _.merge(payload, expectedPayload));
            return payload;
        });

        // We are going to corrupt the metadata by updating documentFactory with this modified payload
        // We have to recompute the checksum so that the metadata upgrade recognises it as modified.
        const corruptedPayload = xtremMetadata.services.MetadataUpgrade.addChecksum({
            ...expectedPayload,
            // modify the isPublished flag
            isPublished: false,
            // only keep the first property
            properties: expectedPayload.properties.slice(0, 1),
        }) as NodeUpdateData<xtremMetadata.nodes.MetaNodeFactory>;

        // Corrupt the metadata
        await Test.withCommittedContext(
            async context => {
                const documentFactory = await context.read(
                    xtremMetadata.nodes.MetaNodeFactory,
                    { name: 'TestDocument' },
                    { forUpdate: true },
                );
                await documentFactory.$.set(corruptedPayload);
                await documentFactory.$.save();
            },
            { testMode: true, bypassFrozen: true },
        );

        // Check that we have indeed written the corrupted records.
        await Test.withReadonlyContext(async context => {
            const documentFactory = await context.read(xtremMetadata.nodes.MetaNodeFactory, { name: 'TestDocument' });
            const payload = await documentFactory.$.payload();
            assert.deepEqual(await documentFactory.$.payload(), _.merge(payload, corruptedPayload));
        });

        // Run an upgrade
        await Test.withCommittedContext(async context => {
            await new xtremMetadata.services.MetadataUpgrade(context.application).upgrade();
        });

        // Read and check that the upgrade has restored the expected payload
        await Test.withReadonlyContext(async context => {
            const documentFactory = await context.read(xtremMetadata.nodes.MetaNodeFactory, { name: 'TestDocument' });
            const payload = await documentFactory.$.payload();
            assert.deepEqual(await documentFactory.$.payload(), _.merge(payload, expectedPayload));
        });
    });
});
