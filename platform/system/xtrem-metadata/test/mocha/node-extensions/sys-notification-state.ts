import * as xtremCommunication from '@sage/xtrem-communication';
import { Test } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import { assert } from 'chai';

describe('Sys notification state', () => {
    it('Sys notification state - creation with envelope', async () => {
        await Test.withCommittedContext(async context => {
            const notificationId = xtremSystem.functions.generateNanoId();
            const originId = xtremSystem.functions.generateNanoId();
            const resumeToken = context.getContextValue('resumeToken') || '';

            const newNotificationState = await context.create(xtremCommunication.nodes.SysNotificationState, {
                envelope: {
                    payload: {},
                    attributes: {
                        topic: 'MetaNodeFactory/asyncExport/start',
                        tenantId: context.tenantId || '',
                        userEmail: (await context.user)?.email || '',
                        login: '',
                        locale: 'EN',
                        notificationId,
                        originId,
                        resumeToken,
                    },
                },
            });

            assert.isTrue(
                await newNotificationState.$.trySave(),
                context.diagnoses.map(diag => diag.message).join('\n'),
            );
        });
    });
});
