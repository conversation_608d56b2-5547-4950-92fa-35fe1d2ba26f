import { Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremMetadata from '../../../lib';

describe('MetaNodeOperation', () => {
    it('Can get description translated ', async () => {
        await Test.withReadonlyContext(async context => {
            const firstOperation = await context
                .query(xtremMetadata.nodes.MetaNodeOperation, { first: 1, filter: { name: 'purge' } })
                .at(0);

            assert.equal(await firstOperation?.title, 'Purge');
        });
    });
});
