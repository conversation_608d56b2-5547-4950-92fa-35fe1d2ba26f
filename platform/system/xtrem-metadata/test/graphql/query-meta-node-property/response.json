{"data": {"xtremMetadata": {"metaNodeProperty": {"query": {"edges": [{"node": {"name": "_sortValue", "title": "Sort value", "type": "integer", "package": {"name": "@sage/xtrem-metadata"}, "dataType": null, "isActive": true, "isNullable": false, "isRequired": false, "isPublished": true, "isStored": true, "isStoredOutput": false, "isTransientInput": false, "isVital": false, "isVitalParent": false, "targetFactory": null, "isInherited": false, "canSort": true, "canFilter": true, "isOnInputType": true, "isOnOutputType": true, "isMutable": false}}, {"node": {"name": "action", "title": "Action", "type": "enum", "package": {"name": "@sage/xtrem-metadata"}, "dataType": {"name": "operationActionDataType"}, "isActive": true, "isNullable": true, "isRequired": false, "isPublished": true, "isStored": true, "isStoredOutput": false, "isTransientInput": false, "isVital": false, "isVitalParent": false, "targetFactory": null, "isInherited": false, "canSort": true, "canFilter": true, "isOnInputType": true, "isOnOutputType": true, "isMutable": false}}, {"node": {"name": "checksum", "title": "Checksum", "type": "string", "package": {"name": "@sage/xtrem-metadata"}, "dataType": {"name": "checksum"}, "isActive": true, "isNullable": false, "isRequired": false, "isPublished": true, "isStored": true, "isStoredOutput": false, "isTransientInput": false, "isVital": false, "isVitalParent": false, "targetFactory": null, "isInherited": false, "canSort": true, "canFilter": true, "isOnInputType": true, "isOnOutputType": true, "isMutable": false}}, {"node": {"name": "factory", "title": "Factory", "type": "reference", "package": {"name": "@sage/xtrem-metadata"}, "dataType": null, "isActive": true, "isNullable": false, "isRequired": true, "isPublished": true, "isStored": true, "isStoredOutput": false, "isTransientInput": false, "isVital": false, "isVitalParent": true, "targetFactory": {"name": "MetaNodeFactory"}, "isInherited": false, "canSort": true, "canFilter": true, "isOnInputType": true, "isOnOutputType": true, "isMutable": false}}, {"node": {"name": "isActive", "title": "Active", "type": "boolean", "package": {"name": "@sage/xtrem-metadata"}, "dataType": null, "isActive": true, "isNullable": false, "isRequired": false, "isPublished": true, "isStored": true, "isStoredOutput": false, "isTransientInput": false, "isVital": false, "isVitalParent": false, "targetFactory": null, "isInherited": false, "canSort": true, "canFilter": true, "isOnInputType": true, "isOnOutputType": true, "isMutable": false}}, {"node": {"name": "isMutation", "title": "Is mutation", "type": "boolean", "package": {"name": "@sage/xtrem-metadata"}, "dataType": null, "isActive": true, "isNullable": false, "isRequired": false, "isPublished": true, "isStored": true, "isStoredOutput": false, "isTransientInput": false, "isVital": false, "isVitalParent": false, "targetFactory": null, "isInherited": false, "canSort": true, "canFilter": true, "isOnInputType": true, "isOnOutputType": true, "isMutable": false}}, {"node": {"name": "isPublished", "title": "Published", "type": "boolean", "package": {"name": "@sage/xtrem-metadata"}, "dataType": null, "isActive": true, "isNullable": false, "isRequired": false, "isPublished": true, "isStored": true, "isStoredOutput": false, "isTransientInput": false, "isVital": false, "isVitalParent": false, "targetFactory": null, "isInherited": false, "canSort": true, "canFilter": true, "isOnInputType": true, "isOnOutputType": true, "isMutable": false}}, {"node": {"name": "isSchedulable", "title": "Schedulable", "type": "boolean", "package": {"name": "@sage/xtrem-metadata"}, "dataType": null, "isActive": true, "isNullable": false, "isRequired": false, "isPublished": true, "isStored": true, "isStoredOutput": false, "isTransientInput": false, "isVital": false, "isVitalParent": false, "targetFactory": null, "isInherited": false, "canSort": true, "canFilter": true, "isOnInputType": true, "isOnOutputType": true, "isMutable": false}}, {"node": {"name": "kind", "title": "Kind", "type": "enum", "package": {"name": "@sage/xtrem-metadata"}, "dataType": {"name": "operationKindDataType"}, "isActive": true, "isNullable": false, "isRequired": false, "isPublished": true, "isStored": true, "isStoredOutput": false, "isTransientInput": false, "isVital": false, "isVitalParent": false, "targetFactory": null, "isInherited": false, "canSort": true, "canFilter": true, "isOnInputType": true, "isOnOutputType": true, "isMutable": false}}, {"node": {"name": "name", "title": "Name", "type": "string", "package": {"name": "@sage/xtrem-metadata"}, "dataType": {"name": "name"}, "isActive": true, "isNullable": false, "isRequired": false, "isPublished": true, "isStored": true, "isStoredOutput": false, "isTransientInput": false, "isVital": false, "isVitalParent": false, "targetFactory": null, "isInherited": false, "canSort": true, "canFilter": true, "isOnInputType": true, "isOnOutputType": true, "isMutable": false}}, {"node": {"name": "package", "title": "Package", "type": "reference", "package": {"name": "@sage/xtrem-metadata"}, "dataType": null, "isActive": true, "isNullable": true, "isRequired": false, "isPublished": true, "isStored": true, "isStoredOutput": false, "isTransientInput": false, "isVital": false, "isVitalParent": false, "targetFactory": {"name": "MetaPackage"}, "isInherited": false, "canSort": true, "canFilter": true, "isOnInputType": true, "isOnOutputType": true, "isMutable": false}}, {"node": {"name": "parameters", "title": "Parameters", "type": "json", "package": {"name": "@sage/xtrem-metadata"}, "dataType": null, "isActive": true, "isNullable": false, "isRequired": false, "isPublished": true, "isStored": false, "isStoredOutput": false, "isTransientInput": false, "isVital": false, "isVitalParent": false, "targetFactory": null, "isInherited": false, "canSort": true, "canFilter": true, "isOnInputType": false, "isOnOutputType": true, "isMutable": false}}, {"node": {"name": "return", "title": "Return", "type": "json", "package": {"name": "@sage/xtrem-metadata"}, "dataType": null, "isActive": true, "isNullable": false, "isRequired": false, "isPublished": true, "isStored": false, "isStoredOutput": false, "isTransientInput": false, "isVital": false, "isVitalParent": false, "targetFactory": null, "isInherited": false, "canSort": true, "canFilter": true, "isOnInputType": false, "isOnOutputType": true, "isMutable": false}}, {"node": {"name": "serviceOptionNames", "title": "Service option names", "type": "json", "package": {"name": "@sage/xtrem-metadata"}, "dataType": null, "isActive": true, "isNullable": false, "isRequired": false, "isPublished": true, "isStored": true, "isStoredOutput": false, "isTransientInput": false, "isVital": false, "isVitalParent": false, "targetFactory": null, "isInherited": false, "canSort": true, "canFilter": true, "isOnInputType": true, "isOnOutputType": true, "isMutable": false}}, {"node": {"name": "serviceOptions", "title": "Service options", "type": "referenceArray", "package": {"name": "@sage/xtrem-metadata"}, "dataType": null, "isActive": true, "isNullable": false, "isRequired": false, "isPublished": true, "isStored": false, "isStoredOutput": false, "isTransientInput": false, "isVital": false, "isVitalParent": false, "targetFactory": {"name": "MetaServiceOption"}, "isInherited": false, "canSort": false, "canFilter": false, "isOnInputType": false, "isOnOutputType": true, "isMutable": false}}, {"node": {"name": "signature", "title": "Signature", "type": "json", "package": {"name": "@sage/xtrem-metadata"}, "dataType": null, "isActive": true, "isNullable": false, "isRequired": false, "isPublished": true, "isStored": true, "isStoredOutput": false, "isTransientInput": false, "isVital": false, "isVitalParent": false, "targetFactory": null, "isInherited": false, "canSort": true, "canFilter": true, "isOnInputType": true, "isOnOutputType": true, "isMutable": false}}, {"node": {"name": "title", "title": "Title", "type": "string", "package": {"name": "@sage/xtrem-metadata"}, "dataType": {"name": "localizedName"}, "isActive": true, "isNullable": false, "isRequired": false, "isPublished": true, "isStored": true, "isStoredOutput": false, "isTransientInput": false, "isVital": false, "isVitalParent": false, "targetFactory": null, "isInherited": false, "canSort": true, "canFilter": true, "isOnInputType": true, "isOnOutputType": true, "isMutable": false}}, {"node": {"name": "topic", "title": "Topic", "type": "string", "package": {"name": "@sage/xtrem-metadata"}, "dataType": {"name": "name"}, "isActive": true, "isNullable": false, "isRequired": false, "isPublished": true, "isStored": false, "isStoredOutput": false, "isTransientInput": false, "isVital": false, "isVitalParent": false, "targetFactory": null, "isInherited": false, "canSort": true, "canFilter": true, "isOnInputType": false, "isOnOutputType": true, "isMutable": false}}]}}}}}