query {
    xtremMetadata {
        metaNodeProperty {
            query(filter: "{ factory: { name: 'MetaNodeOperation' } }") {
                edges {
                    node {
                        name
                        title
                        type
                        package {
                            name
                        }
                        dataType {
                            name
                        }
                        isActive
                        isNullable
                        isRequired
                        isPublished
                        isStored
                        isStoredOutput
                        isTransientInput
                        isVital
                        isVitalParent
                        targetFactory {
                            name
                        }
                        isInherited
                        canSort
                        canFilter
                        isOnInputType
                        isOnOutputType
                        isMutable
                    }
                }
            }
        }
    }
}
