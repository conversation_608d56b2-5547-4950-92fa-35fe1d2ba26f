{"all-dogs": {"input": {"nodeName": "testDog", "args": ""}, "output": {"nodeName": "testDog", "edges": [{"node": {"name": "<PERSON><PERSON>", "_factory": {"name": "TestDog", "title": "Test Dog"}}}, {"node": {"name": "snoopy", "_factory": {"name": "TestDog", "title": "Test Dog"}}}]}}, "all-mammals": {"input": {"nodeName": "<PERSON><PERSON><PERSON><PERSON>", "args": ""}, "output": {"nodeName": "<PERSON><PERSON><PERSON><PERSON>", "edges": [{"node": {"name": "cappuccino", "_factory": {"name": "TestCat", "title": "Test Cat"}}}, {"node": {"name": "garfield", "_factory": {"name": "TestCat", "title": "Test Cat"}}}, {"node": {"name": "<PERSON><PERSON>", "_factory": {"name": "TestDog", "title": "Test Dog"}}}, {"node": {"name": "snoopy", "_factory": {"name": "TestDog", "title": "Test Dog"}}}]}}, "all-animals": {"input": {"nodeName": "testAnimal", "args": ""}, "output": {"nodeName": "testAnimal", "edges": [{"node": {"name": "cappuccino", "_factory": {"name": "TestCat", "title": "Test Cat"}}}, {"node": {"name": "dracula", "_factory": {"name": "TestMosquito", "title": "Test Mosquito"}}}, {"node": {"name": "garfield", "_factory": {"name": "TestCat", "title": "Test Cat"}}}, {"node": {"name": "<PERSON><PERSON>", "_factory": {"name": "TestDog", "title": "Test Dog"}}}, {"node": {"name": "snoopy", "_factory": {"name": "TestDog", "title": "Test Dog"}}}]}}, "mammals-filtered-on-dog": {"input": {"nodeName": "<PERSON><PERSON><PERSON><PERSON>", "args": "(filter: \"{ _factory: { title: 'Test Dog' } }\")"}, "output": {"nodeName": "<PERSON><PERSON><PERSON><PERSON>", "edges": [{"node": {"name": "<PERSON><PERSON>", "_factory": {"name": "TestDog", "title": "Test Dog"}}}, {"node": {"name": "snoopy", "_factory": {"name": "TestDog", "title": "Test Dog"}}}]}}, "animals-filtered-on-dog": {"input": {"nodeName": "testAnimal", "args": "(filter: \"{ _factory: { title: 'Test Dog' } }\")"}, "output": {"nodeName": "testAnimal", "edges": [{"node": {"name": "<PERSON><PERSON>", "_factory": {"name": "TestDog", "title": "Test Dog"}}}, {"node": {"name": "snoopy", "_factory": {"name": "TestDog", "title": "Test Dog"}}}]}}, "animals-ordered-by-factory-title": {"input": {"nodeName": "testAnimal", "args": "(orderBy: \"{ _factory: { title: -1 }, name: 1 }\")"}, "output": {"nodeName": "testAnimal", "edges": [{"node": {"name": "dracula", "_factory": {"name": "TestMosquito", "title": "Test Mosquito"}}}, {"node": {"name": "<PERSON><PERSON>", "_factory": {"name": "TestDog", "title": "Test Dog"}}}, {"node": {"name": "snoopy", "_factory": {"name": "TestDog", "title": "Test Dog"}}}, {"node": {"name": "cappuccino", "_factory": {"name": "TestCat", "title": "Test Cat"}}}, {"node": {"name": "garfield", "_factory": {"name": "TestCat", "title": "Test Cat"}}}]}}}