{"data": {"xtremMetadata": {"metaNodeFactory": {"query": {"edges": [{"node": {"name": "MetaActivity", "title": "Meta activity", "customFields": "[]", "isPlatformNode": true, "naturalKey": "[\"name\"]"}}, {"node": {"name": "MetaActivityPermission", "title": "Meta activity permission", "customFields": "[]", "isPlatformNode": true, "naturalKey": "[\"activity\",\"name\"]"}}, {"node": {"name": "MetaDataType", "title": "Meta data type", "customFields": "[]", "isPlatformNode": true, "naturalKey": "[\"name\"]"}}, {"node": {"name": "MetaNodeFactory", "title": "Node", "customFields": "[]", "isPlatformNode": true, "naturalKey": "[\"name\"]"}}, {"node": {"name": "MetaNodeOperation", "title": "Operation", "customFields": "[]", "isPlatformNode": true, "naturalKey": "[\"factory\",\"name\",\"action\"]"}}, {"node": {"name": "MetaNodeProperty", "title": "Property", "customFields": "[]", "isPlatformNode": true, "naturalKey": "[\"factory\",\"name\"]"}}, {"node": {"name": "MetaPackage", "title": "Package", "customFields": "[]", "isPlatformNode": true, "naturalKey": "[\"name\"]"}}, {"node": {"name": "MetaServiceOption", "title": "Service option", "customFields": "[]", "isPlatformNode": true, "naturalKey": "[\"name\"]"}}, {"node": {"customFields": "[]", "isPlatformNode": true, "name": "SysNotification", "naturalKey": null, "title": "System notification"}}, {"node": {"name": "SysNotificationHistory", "title": "System notification history", "customFields": "[]", "isPlatformNode": true, "naturalKey": null}}, {"node": {"name": "SysNotificationLogEntry", "title": "System notification log entry", "customFields": "[]", "isPlatformNode": true, "naturalKey": "[\"sysNotificationState\",\"_sortValue\"]"}}, {"node": {"name": "SysNotificationState", "title": "System notification state", "customFields": "[]", "isPlatformNode": true, "naturalKey": "[\"notificationId\"]"}}]}}}}}