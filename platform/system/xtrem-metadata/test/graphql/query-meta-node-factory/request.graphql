query {
    xtremMetadata {
        metaNodeFactory {
            query(filter: "{ name: { _regex: '^(Meta|SysNotification)' } }") {
                edges {
                    node {
                        name
                        title
                        customFields
                        isPlatformNode
                        naturalKey
                    }
                }
            }
        }
    }
}
