{"data": {"xtremMetadata": {"metaNodeOperation": {"query": {"edges": [{"node": {"kind": "asyncMutation", "name": "asyncExport", "signature": "{\"return\":{\"type\":\"object\",\"properties\":{\"trackingId\":\"string\"}},\"parameters\":[{\"name\":\"id\",\"type\":\"string\"},{\"name\":\"filter\",\"type\":\"string\"}]}", "action": "start", "title": "Export", "isMutation": true}}, {"node": {"kind": "asyncMutation", "name": "asyncExport", "signature": "{\"return\":{\"type\":\"boolean\"},\"parameters\":[{\"name\":\"trackingId\",\"type\":\"string\",\"isMandatory\":true},{\"name\":\"reason\",\"type\":\"string\"}]}", "action": "stop", "title": "Export", "isMutation": true}}, {"node": {"kind": "asyncMutation", "name": "asyncExport", "signature": "{\"return\":{\"type\":\"boolean\"},\"parameters\":[{\"name\":\"trackingId\",\"type\":\"string\",\"isMandatory\":true}]}", "action": "requestUserNotification", "title": "Export", "isMutation": true}}, {"node": {"kind": "mutation", "name": "logPageVisit", "signature": "{\"return\":{\"item\":\"string\",\"type\":\"array\"},\"parameters\":[{\"name\":\"path\",\"type\":\"string\",\"isMandatory\":true}]}", "action": null, "title": "Log page visit", "isMutation": true}}, {"node": {"kind": "mutation", "name": "sendWelcomeMail", "signature": "{\"return\":{\"type\":\"boolean\"},\"parameters\":[{\"item\":{\"node\":\"User\",\"type\":\"reference\"},\"name\":\"users\",\"type\":\"array\",\"isMandatory\":true},{\"name\":\"isAdmin\",\"type\":\"boolean\",\"isMandatory\":true}]}", "action": null, "title": "Send welcome email", "isMutation": true}}, {"node": {"kind": "mutation", "name": "setDemoPersona", "signature": "{\"return\":{\"type\":\"boolean\",\"isMandatory\":true},\"parameters\":[{\"name\":\"email\",\"type\":\"string\",\"isMandatory\":true}]}", "action": null, "title": "Set demo persona", "isMutation": true}}, {"node": {"kind": "mutation", "name": "updateClientSettings", "signature": "{\"return\":{\"node\":\"User\",\"type\":\"instance\"},\"parameters\":[{\"item\":{\"type\":\"object\",\"properties\":{\"_id\":\"integer\",\"title\":\"string\",\"_action\":\"string\",\"content\":\"string\",\"screenId\":\"string\",\"elementId\":\"string\",\"description\":\"string\"}},\"name\":\"clientSettings\",\"type\":\"array\",\"isMandatory\":true}]}", "action": null, "title": "Update client settings", "isMutation": true}}]}}}}}