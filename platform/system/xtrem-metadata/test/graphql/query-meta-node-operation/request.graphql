query {
    xtremMetadata {
        metaNodeOperation {
            query(filter: "{ kind: { _in: ['asyncMutation', 'mutation'] }, factory: { name: 'User' } }") {
                edges {
                    node {
                        kind
                        name
                        action
                        title
                        signature
                        isMutation
                    }
                }
            }
        }
    }
}
