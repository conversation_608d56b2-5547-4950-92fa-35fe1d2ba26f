import { accessRightsManagerMock, Context } from '@sage/xtrem-core';
import * as dataTypes from './data-types/_index';
import * as enums from './enums/_index';
import * as nodes from './nodes/_index';
import * as serviceOptions from './service-options';

// The `xtrem test` command checks for a test/fixtures/test-application.ts file (this file)
// and combines its exports with the lib/index.ts exports before running the mocha unit tests.

Context.accessRightsManager = accessRightsManagerMock;

export { dataTypes, enums, nodes, serviceOptions };
