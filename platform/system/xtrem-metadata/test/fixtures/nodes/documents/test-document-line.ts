import { decimal, decorators, Node, Reference } from '@sage/xtrem-core';
import * as test from '../../test-application';

@decorators.node<TestDocumentLine>({
    storage: 'sql',
    isPublished: true,
    isVitalCollectionChild: true,
})
export class TestDocumentLine extends Node {
    @decorators.referenceProperty<TestDocumentLine, 'document'>({
        isVitalParent: true,
        isStored: true,
        isPublished: true,
        node: () => test.nodes.TestDocument,
    })
    readonly document: Reference<test.nodes.TestDocument>;

    @decorators.decimalProperty<TestDocumentLine, 'quantity'>({
        isStored: true,
        isPublished: true,
        dataType: () => test.dataTypes.testQuantity,
    })
    readonly quantity: Promise<decimal>;
}
