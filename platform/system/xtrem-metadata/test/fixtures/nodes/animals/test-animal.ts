import { decorators, Node } from '@sage/xtrem-core';
import * as test from '../../test-application';

@decorators.node<TestAnimal>({
    isAbstract: true,
    storage: 'sql',
    isPublished: true,
    indexes: [{ orderBy: { name: 1 }, isUnique: true, isNaturalKey: true }],
})
export class TestAnimal extends Node {
    @decorators.stringProperty<TestAnimal, 'name'>({
        isStored: true,
        isPublished: true,
        dataType: () => test.dataTypes.testId,
    })
    readonly name: Promise<string>;
}
