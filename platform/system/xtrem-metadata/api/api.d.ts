declare module '@sage/xtrem-metadata-api-partial' {
    import type {
        Package as SageXtremCommunication$Package,
        SysNotificationLogEntry,
        SysNotificationLogEntryBinding,
        SysNotificationLogEntryInput,
        SysNotificationStateInput,
    } from '@sage/xtrem-communication-api';
    import type { Package as SageXtremSystem$Package, User } from '@sage/xtrem-system-api';
    import type {
        AggregateQueryOperation,
        AggregateReadOperation,
        AsyncOperation,
        ClientCollection,
        ClientNode,
        ClientNodeInput,
        CreateOperation,
        GetDefaultsOperation,
        GetDuplicateOperation,
        QueryOperation,
        ReadOperation,
        UpdateByIdOperation,
        UpdateOperation,
        VitalClientNode,
        VitalClientNodeInput,
        integer,
    } from '@sage/xtrem-client';
    export interface MetaOperationAction$Enum {
        start: 1;
        stop: 2;
        track: 3;
        requestUserNotification: 4;
    }
    export type MetaOperationAction = keyof MetaOperationAction$Enum;
    export interface MetaOperationKind$Enum {
        query: 1;
        mutation: 2;
        asyncMutation: 3;
        asyncTrackerQuery: 4;
        bulkMutation: 5;
    }
    export type MetaOperationKind = keyof MetaOperationKind$Enum;
    export interface MetaPropertyType$Enum {
        boolean: 1;
        string: 2;
        byte: 3;
        short: 4;
        integer: 5;
        decimal: 5;
        float: 7;
        double: 8;
        enum: 9;
        date: 10;
        time: 11;
        datetime: 12;
        uuid: 13;
        binaryStream: 14;
        textStream: 15;
        json: 16;
        reference: 17;
        collection: 18;
        jsonReference: 19;
        integerArray: 20;
        enumArray: 21;
        referenceArray: 22;
        stringArray: 23;
        integerRange: 24;
        decimalRange: 25;
        dateRange: 26;
        datetimeRange: 27;
    }
    export type MetaPropertyType = keyof MetaPropertyType$Enum;
    export interface MetaStorage$Enum {
        sql: 1;
        json: 2;
        external: 3;
    }
    export type MetaStorage = keyof MetaStorage$Enum;
    export interface MetaActivity extends ClientNode {
        _factory: MetaNodeFactory;
        name: string;
        title: string;
        package: MetaPackage;
        isActive: boolean;
        checksum: string;
        permissions: ClientCollection<MetaActivityPermission>;
    }
    export interface MetaActivityInput extends ClientNodeInput {
        name?: string;
        title?: string;
        package?: integer | string;
        isActive?: boolean | string;
        checksum?: string;
        permissions?: Partial<MetaActivityPermissionInput>[];
    }
    export interface MetaActivityBinding extends ClientNode {
        _factory: MetaNodeFactory;
        name: string;
        title: string;
        package: MetaPackage;
        isActive: boolean;
        checksum: string;
        permissions: ClientCollection<MetaActivityPermissionBinding>;
    }
    export interface MetaActivity$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface MetaActivity$Lookups {
        package: QueryOperation<MetaPackage>;
    }
    export interface MetaActivity$Operations {
        query: QueryOperation<MetaActivity>;
        read: ReadOperation<MetaActivity>;
        aggregate: {
            read: AggregateReadOperation<MetaActivity>;
            query: AggregateQueryOperation<MetaActivity>;
        };
        create: CreateOperation<MetaActivityInput, MetaActivity>;
        getDuplicate: GetDuplicateOperation<MetaActivity>;
        update: UpdateOperation<MetaActivityInput, MetaActivity>;
        updateById: UpdateByIdOperation<MetaActivityInput, MetaActivity>;
        asyncOperations: MetaActivity$AsyncOperations;
        lookups(dataOrId: string | { data: MetaActivityInput }): MetaActivity$Lookups;
        getDefaults: GetDefaultsOperation<MetaActivity>;
    }
    export interface MetaActivityPermission extends VitalClientNode {
        _factory: MetaNodeFactory;
        activity: MetaActivity;
        name: string;
        title: string;
        package: MetaPackage;
        isActive: boolean;
        checksum: string;
    }
    export interface MetaActivityPermissionInput extends VitalClientNodeInput {
        name?: string;
        title?: string;
        package?: integer | string;
        isActive?: boolean | string;
        checksum?: string;
    }
    export interface MetaActivityPermissionBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        activity: MetaActivity;
        name: string;
        title: string;
        package: MetaPackage;
        isActive: boolean;
        checksum: string;
    }
    export interface MetaActivityPermission$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface MetaActivityPermission$Lookups {
        package: QueryOperation<MetaPackage>;
    }
    export interface MetaActivityPermission$Operations {
        query: QueryOperation<MetaActivityPermission>;
        read: ReadOperation<MetaActivityPermission>;
        aggregate: {
            read: AggregateReadOperation<MetaActivityPermission>;
            query: AggregateQueryOperation<MetaActivityPermission>;
        };
        create: CreateOperation<MetaActivityPermissionInput, MetaActivityPermission>;
        getDuplicate: GetDuplicateOperation<MetaActivityPermission>;
        update: UpdateOperation<MetaActivityPermissionInput, MetaActivityPermission>;
        updateById: UpdateByIdOperation<MetaActivityPermissionInput, MetaActivityPermission>;
        asyncOperations: MetaActivityPermission$AsyncOperations;
        lookups(dataOrId: string | { data: MetaActivityPermissionInput }): MetaActivityPermission$Lookups;
        getDefaults: GetDefaultsOperation<MetaActivityPermission>;
    }
    export interface MetaDataType extends ClientNode {
        _factory: MetaNodeFactory;
        definingPackage: MetaPackage;
        name: string;
        title: string;
        type: MetaPropertyType;
        attributes: string;
        serviceOptionNames: string;
        serviceOptions: MetaServiceOption[];
        isActive: boolean;
        checksum: string;
    }
    export interface MetaDataTypeInput extends ClientNodeInput {
        definingPackage?: integer | string;
        name?: string;
        title?: string;
        type?: MetaPropertyType;
        attributes?: string;
        serviceOptionNames?: string;
        isActive?: boolean | string;
        checksum?: string;
    }
    export interface MetaDataTypeBinding extends ClientNode {
        _factory: MetaNodeFactory;
        definingPackage: MetaPackage;
        name: string;
        title: string;
        type: MetaPropertyType;
        attributes: any;
        serviceOptionNames: any;
        serviceOptions: MetaServiceOption[];
        isActive: boolean;
        checksum: string;
    }
    export interface MetaDataType$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface MetaDataType$Lookups {
        definingPackage: QueryOperation<MetaPackage>;
    }
    export interface MetaDataType$Operations {
        query: QueryOperation<MetaDataType>;
        read: ReadOperation<MetaDataType>;
        aggregate: {
            read: AggregateReadOperation<MetaDataType>;
            query: AggregateQueryOperation<MetaDataType>;
        };
        create: CreateOperation<MetaDataTypeInput, MetaDataType>;
        getDuplicate: GetDuplicateOperation<MetaDataType>;
        update: UpdateOperation<MetaDataTypeInput, MetaDataType>;
        updateById: UpdateByIdOperation<MetaDataTypeInput, MetaDataType>;
        asyncOperations: MetaDataType$AsyncOperations;
        lookups(dataOrId: string | { data: MetaDataTypeInput }): MetaDataType$Lookups;
        getDefaults: GetDefaultsOperation<MetaDataType>;
    }
    export interface MetaNodeFactory extends ClientNode {
        _factory: MetaNodeFactory;
        name: string;
        title: string;
        package: MetaPackage;
        isActive: boolean;
        isPublished: boolean;
        isAbstract: boolean;
        extends: MetaNodeFactory;
        storage: MetaStorage;
        isSharedByAllTenants: boolean;
        isSetupNode: boolean;
        isPlatformNode: boolean;
        isVitalReferenceChild: boolean;
        isVitalCollectionChild: boolean;
        isCustomizable: boolean;
        isSynchronizable: boolean;
        isSynchronized: boolean;
        isClearedByReset: boolean;
        checksum: string;
        serviceOptionNames: string;
        serviceOptions: MetaServiceOption[];
        naturalKey: string;
        customFields: string;
        notifies: string;
        properties: ClientCollection<MetaNodeProperty>;
        operations: ClientCollection<MetaNodeOperation>;
    }
    export interface MetaNodeFactoryInput extends ClientNodeInput {
        name?: string;
        title?: string;
        package?: integer | string;
        isActive?: boolean | string;
        isPublished?: boolean | string;
        isAbstract?: boolean | string;
        extends?: integer | string;
        storage?: MetaStorage;
        isSharedByAllTenants?: boolean | string;
        isSetupNode?: boolean | string;
        isPlatformNode?: boolean | string;
        isVitalReferenceChild?: boolean | string;
        isVitalCollectionChild?: boolean | string;
        isCustomizable?: boolean | string;
        isSynchronizable?: boolean | string;
        isSynchronized?: boolean | string;
        isClearedByReset?: boolean | string;
        checksum?: string;
        serviceOptionNames?: string;
        naturalKey?: string;
        notifies?: string;
        properties?: Partial<MetaNodePropertyInput>[];
        operations?: Partial<MetaNodeOperationInput>[];
    }
    export interface MetaNodeFactoryBinding extends ClientNode {
        _factory: MetaNodeFactory;
        name: string;
        title: string;
        package: MetaPackage;
        isActive: boolean;
        isPublished: boolean;
        isAbstract: boolean;
        extends: MetaNodeFactory;
        storage: MetaStorage;
        isSharedByAllTenants: boolean;
        isSetupNode: boolean;
        isPlatformNode: boolean;
        isVitalReferenceChild: boolean;
        isVitalCollectionChild: boolean;
        isCustomizable: boolean;
        isSynchronizable: boolean;
        isSynchronized: boolean;
        isClearedByReset: boolean;
        checksum: string;
        serviceOptionNames: any;
        serviceOptions: MetaServiceOption[];
        naturalKey: any;
        customFields: any;
        notifies: any;
        properties: ClientCollection<MetaNodePropertyBinding>;
        operations: ClientCollection<MetaNodeOperationBinding>;
    }
    export interface MetaNodeFactory$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface MetaNodeFactory$Lookups {
        package: QueryOperation<MetaPackage>;
        extends: QueryOperation<MetaNodeFactory>;
    }
    export interface MetaNodeFactory$Operations {
        query: QueryOperation<MetaNodeFactory>;
        read: ReadOperation<MetaNodeFactory>;
        aggregate: {
            read: AggregateReadOperation<MetaNodeFactory>;
            query: AggregateQueryOperation<MetaNodeFactory>;
        };
        create: CreateOperation<MetaNodeFactoryInput, MetaNodeFactory>;
        getDuplicate: GetDuplicateOperation<MetaNodeFactory>;
        update: UpdateOperation<MetaNodeFactoryInput, MetaNodeFactory>;
        updateById: UpdateByIdOperation<MetaNodeFactoryInput, MetaNodeFactory>;
        asyncOperations: MetaNodeFactory$AsyncOperations;
        lookups(dataOrId: string | { data: MetaNodeFactoryInput }): MetaNodeFactory$Lookups;
        getDefaults: GetDefaultsOperation<MetaNodeFactory>;
    }
    export interface MetaNodeOperation extends VitalClientNode {
        _factory: MetaNodeFactory;
        factory: MetaNodeFactory;
        name: string;
        title: string;
        package: MetaPackage;
        serviceOptionNames: string;
        serviceOptions: MetaServiceOption[];
        isActive: boolean;
        kind: MetaOperationKind;
        action: MetaOperationAction;
        isPublished: boolean;
        isSchedulable: boolean;
        isMutation: boolean;
        parameters: string;
        return: string;
        signature: string;
        checksum: string;
        topic: string;
    }
    export interface MetaNodeOperationInput extends VitalClientNodeInput {
        name?: string;
        title?: string;
        package?: integer | string;
        serviceOptionNames?: string;
        isActive?: boolean | string;
        kind?: MetaOperationKind;
        action?: MetaOperationAction;
        isPublished?: boolean | string;
        isSchedulable?: boolean | string;
        isMutation?: boolean | string;
        signature?: string;
        checksum?: string;
    }
    export interface MetaNodeOperationBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        factory: MetaNodeFactory;
        name: string;
        title: string;
        package: MetaPackage;
        serviceOptionNames: any;
        serviceOptions: MetaServiceOption[];
        isActive: boolean;
        kind: MetaOperationKind;
        action: MetaOperationAction;
        isPublished: boolean;
        isSchedulable: boolean;
        isMutation: boolean;
        parameters: any;
        return: any;
        signature: any;
        checksum: string;
        topic: string;
    }
    export interface MetaNodeOperation$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface MetaNodeOperation$Lookups {
        package: QueryOperation<MetaPackage>;
    }
    export interface MetaNodeOperation$Operations {
        query: QueryOperation<MetaNodeOperation>;
        read: ReadOperation<MetaNodeOperation>;
        aggregate: {
            read: AggregateReadOperation<MetaNodeOperation>;
            query: AggregateQueryOperation<MetaNodeOperation>;
        };
        create: CreateOperation<MetaNodeOperationInput, MetaNodeOperation>;
        getDuplicate: GetDuplicateOperation<MetaNodeOperation>;
        update: UpdateOperation<MetaNodeOperationInput, MetaNodeOperation>;
        updateById: UpdateByIdOperation<MetaNodeOperationInput, MetaNodeOperation>;
        asyncOperations: MetaNodeOperation$AsyncOperations;
        lookups(dataOrId: string | { data: MetaNodeOperationInput }): MetaNodeOperation$Lookups;
        getDefaults: GetDefaultsOperation<MetaNodeOperation>;
    }
    export interface MetaNodeProperty extends VitalClientNode {
        _factory: MetaNodeFactory;
        factory: MetaNodeFactory;
        name: string;
        title: string;
        package: MetaPackage;
        serviceOptionNames: string;
        serviceOptions: MetaServiceOption[];
        isActive: boolean;
        type: MetaPropertyType;
        dataType: MetaDataType;
        dependencyIndex: integer;
        isPublished: boolean;
        isStored: boolean;
        isTransientInput: boolean;
        isStoredOutput: boolean;
        isRequired: boolean;
        isNullable: boolean;
        targetFactory: MetaNodeFactory;
        reverseReference: MetaNodeProperty;
        isVital: boolean;
        isVitalParent: boolean;
        isInherited: boolean;
        canSort: boolean;
        canFilter: boolean;
        isOnInputType: boolean;
        isOnOutputType: boolean;
        isMutable: boolean;
        checksum: string;
    }
    export interface MetaNodePropertyInput extends VitalClientNodeInput {
        name?: string;
        title?: string;
        package?: integer | string;
        serviceOptionNames?: string;
        isActive?: boolean | string;
        type?: MetaPropertyType;
        dataType?: integer | string;
        dependencyIndex?: integer | string;
        isPublished?: boolean | string;
        isStored?: boolean | string;
        isTransientInput?: boolean | string;
        isStoredOutput?: boolean | string;
        isRequired?: boolean | string;
        isNullable?: boolean | string;
        targetFactory?: integer | string;
        reverseReference?: integer | string;
        isVital?: boolean | string;
        isVitalParent?: boolean | string;
        isInherited?: boolean | string;
        canSort?: boolean | string;
        canFilter?: boolean | string;
        isOnInputType?: boolean | string;
        isOnOutputType?: boolean | string;
        isMutable?: boolean | string;
        checksum?: string;
    }
    export interface MetaNodePropertyBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        factory: MetaNodeFactory;
        name: string;
        title: string;
        package: MetaPackage;
        serviceOptionNames: any;
        serviceOptions: MetaServiceOption[];
        isActive: boolean;
        type: MetaPropertyType;
        dataType: MetaDataType;
        dependencyIndex: integer;
        isPublished: boolean;
        isStored: boolean;
        isTransientInput: boolean;
        isStoredOutput: boolean;
        isRequired: boolean;
        isNullable: boolean;
        targetFactory: MetaNodeFactory;
        reverseReference: MetaNodeProperty;
        isVital: boolean;
        isVitalParent: boolean;
        isInherited: boolean;
        canSort: boolean;
        canFilter: boolean;
        isOnInputType: boolean;
        isOnOutputType: boolean;
        isMutable: boolean;
        checksum: string;
    }
    export interface MetaNodeProperty$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface MetaNodeProperty$Lookups {
        package: QueryOperation<MetaPackage>;
        dataType: QueryOperation<MetaDataType>;
        targetFactory: QueryOperation<MetaNodeFactory>;
        reverseReference: QueryOperation<MetaNodeProperty>;
    }
    export interface MetaNodeProperty$Operations {
        query: QueryOperation<MetaNodeProperty>;
        read: ReadOperation<MetaNodeProperty>;
        aggregate: {
            read: AggregateReadOperation<MetaNodeProperty>;
            query: AggregateQueryOperation<MetaNodeProperty>;
        };
        create: CreateOperation<MetaNodePropertyInput, MetaNodeProperty>;
        getDuplicate: GetDuplicateOperation<MetaNodeProperty>;
        update: UpdateOperation<MetaNodePropertyInput, MetaNodeProperty>;
        updateById: UpdateByIdOperation<MetaNodePropertyInput, MetaNodeProperty>;
        asyncOperations: MetaNodeProperty$AsyncOperations;
        lookups(dataOrId: string | { data: MetaNodePropertyInput }): MetaNodeProperty$Lookups;
        getDefaults: GetDefaultsOperation<MetaNodeProperty>;
    }
    export interface MetaPackage extends ClientNode {
        _factory: MetaNodeFactory;
        name: string;
        title: string;
        isActive: boolean;
        checksum: string;
    }
    export interface MetaPackageInput extends ClientNodeInput {
        name?: string;
        title?: string;
        isActive?: boolean | string;
        checksum?: string;
    }
    export interface MetaPackageBinding extends ClientNode {
        _factory: MetaNodeFactory;
        name: string;
        title: string;
        isActive: boolean;
        checksum: string;
    }
    export interface MetaPackage$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface MetaPackage$Operations {
        query: QueryOperation<MetaPackage>;
        read: ReadOperation<MetaPackage>;
        aggregate: {
            read: AggregateReadOperation<MetaPackage>;
            query: AggregateQueryOperation<MetaPackage>;
        };
        create: CreateOperation<MetaPackageInput, MetaPackage>;
        getDuplicate: GetDuplicateOperation<MetaPackage>;
        update: UpdateOperation<MetaPackageInput, MetaPackage>;
        updateById: UpdateByIdOperation<MetaPackageInput, MetaPackage>;
        asyncOperations: MetaPackage$AsyncOperations;
        getDefaults: GetDefaultsOperation<MetaPackage>;
    }
    export interface MetaServiceOption extends ClientNode {
        _factory: MetaNodeFactory;
        name: string;
        title: string;
        package: MetaPackage;
        isSubscribable: boolean;
        isActive: boolean;
        isHidden: boolean;
        checksum: string;
    }
    export interface MetaServiceOptionInput extends ClientNodeInput {
        name?: string;
        title?: string;
        package?: integer | string;
        isSubscribable?: boolean | string;
        isActive?: boolean | string;
        isHidden?: boolean | string;
        checksum?: string;
    }
    export interface MetaServiceOptionBinding extends ClientNode {
        _factory: MetaNodeFactory;
        name: string;
        title: string;
        package: MetaPackage;
        isSubscribable: boolean;
        isActive: boolean;
        isHidden: boolean;
        checksum: string;
    }
    export interface MetaServiceOption$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface MetaServiceOption$Lookups {
        package: QueryOperation<MetaPackage>;
    }
    export interface MetaServiceOption$Operations {
        query: QueryOperation<MetaServiceOption>;
        read: ReadOperation<MetaServiceOption>;
        aggregate: {
            read: AggregateReadOperation<MetaServiceOption>;
            query: AggregateQueryOperation<MetaServiceOption>;
        };
        create: CreateOperation<MetaServiceOptionInput, MetaServiceOption>;
        getDuplicate: GetDuplicateOperation<MetaServiceOption>;
        update: UpdateOperation<MetaServiceOptionInput, MetaServiceOption>;
        updateById: UpdateByIdOperation<MetaServiceOptionInput, MetaServiceOption>;
        asyncOperations: MetaServiceOption$AsyncOperations;
        lookups(dataOrId: string | { data: MetaServiceOptionInput }): MetaServiceOption$Lookups;
        getDefaults: GetDefaultsOperation<MetaServiceOption>;
    }
    export interface SysNotificationStateExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        notificationId: string;
        isRead: boolean;
        originId: string;
        operationName: string;
        parameterValues: string;
        topic: string;
        replyId: string;
        locale: string;
        status: NotificationStatus;
        result: string;
        message: string;
        progress: string;
        progressBarPercent: string;
        timeStarted: string;
        timeEnded: string;
        notificationContext: string;
        user: User;
        isUserNotificationRequested: boolean;
        logs: ClientCollection<SysNotificationLogEntry>;
        operation: MetaNodeOperation;
    }
    export interface SysNotificationStateInputExtension {
        notificationId?: string;
        isRead?: boolean | string;
        originId?: string;
        operationName?: string;
        parameterValues?: string;
        topic?: string;
        replyId?: string;
        locale?: string;
        status?: NotificationStatus;
        result?: string;
        message?: string;
        progress?: string;
        timeStarted?: string;
        timeEnded?: string;
        notificationContext?: string;
        user?: integer | string;
        isUserNotificationRequested?: boolean | string;
        logs?: Partial<SysNotificationLogEntryInput>[];
        operation?: integer | string;
    }
    export interface SysNotificationStateBindingExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        notificationId: string;
        isRead: boolean;
        originId: string;
        operationName: string;
        parameterValues: any;
        topic: string;
        replyId: string;
        locale: string;
        status: NotificationStatus;
        result: any;
        message: string;
        progress: any;
        progressBarPercent: string;
        timeStarted: string;
        timeEnded: string;
        notificationContext: any;
        user: User;
        isUserNotificationRequested: boolean;
        logs: ClientCollection<SysNotificationLogEntryBinding>;
        operation: MetaNodeOperation;
    }
    export interface SysNotificationStateExtension$Lookups {
        operation: QueryOperation<MetaNodeOperation>;
    }
    export interface SysNotificationStateExtension$Operations {
        lookups(dataOrId: string | { data: SysNotificationStateInput }): SysNotificationStateExtension$Lookups;
    }
    export interface Package {
        '@sage/xtrem-metadata/MetaActivity': MetaActivity$Operations;
        '@sage/xtrem-metadata/MetaActivityPermission': MetaActivityPermission$Operations;
        '@sage/xtrem-metadata/MetaDataType': MetaDataType$Operations;
        '@sage/xtrem-metadata/MetaNodeFactory': MetaNodeFactory$Operations;
        '@sage/xtrem-metadata/MetaNodeOperation': MetaNodeOperation$Operations;
        '@sage/xtrem-metadata/MetaNodeProperty': MetaNodeProperty$Operations;
        '@sage/xtrem-metadata/MetaPackage': MetaPackage$Operations;
        '@sage/xtrem-metadata/MetaServiceOption': MetaServiceOption$Operations;
    }
    export interface GraphApi extends Package, SageXtremCommunication$Package, SageXtremSystem$Package {}
}
declare module '@sage/xtrem-metadata-api' {
    export type * from '@sage/xtrem-metadata-api-partial';
}
declare module '@sage/xtrem-communication-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-metadata-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-system-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-metadata-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-communication-api-partial' {
    import type {
        SysNotificationStateBindingExtension,
        SysNotificationStateExtension,
        SysNotificationStateExtension$Lookups,
        SysNotificationStateExtension$Operations,
        SysNotificationStateInputExtension,
    } from '@sage/xtrem-metadata-api';
    export interface SysNotificationState extends SysNotificationStateExtension {}
    export interface SysNotificationStateBinding extends SysNotificationStateBindingExtension {}
    export interface SysNotificationStateInput extends SysNotificationStateInputExtension {}
    export interface SysNotificationState$Lookups extends SysNotificationStateExtension$Lookups {}
    export interface SysNotificationState$Operations extends SysNotificationStateExtension$Operations {}
}
