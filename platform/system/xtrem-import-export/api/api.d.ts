declare module '@sage/xtrem-import-export-api-partial' {
    import type { Package as SageXtremAuditing$Package } from '@sage/xtrem-auditing-api';
    import type { Package as SageXtremAuthorization$Package } from '@sage/xtrem-authorization-api';
    import type { Package as SageXtremCommunication$Package } from '@sage/xtrem-communication-api';
    import type { Package as SageXtremCustomization$Package } from '@sage/xtrem-customization-api';
    import type { Package as SageXtremDashboard$Package } from '@sage/xtrem-dashboard-api';
    import type { MetaNodeFactory, Package as SageXtremMetadata$Package } from '@sage/xtrem-metadata-api';
    import type { Package as SageXtremRouting$Package } from '@sage/xtrem-routing-api';
    import type { Package as SageXtremScheduler$Package } from '@sage/xtrem-scheduler-api';
    import type { Package as SageXtremSystem$Package, SysVendor, User, UserPreferences } from '@sage/xtrem-system-api';
    import type { Package as SageXtremUpload$Package, UploadedFile } from '@sage/xtrem-upload-api';
    import type { Package as SageXtremWorkflow$Package } from '@sage/xtrem-workflow-api';
    import type {
        AggregateQueryOperation,
        AggregateReadOperation,
        AsyncOperation,
        ClientNode,
        ClientNodeInput,
        CreateOperation,
        DeleteOperation,
        GetDefaultsOperation,
        GetDuplicateOperation,
        Operation as Node$Operation,
        QueryOperation,
        ReadOperation,
        TextStream,
        UpdateByIdOperation,
        UpdateOperation,
        integer,
    } from '@sage/xtrem-client';
    export interface DisplayedPropertyType$Enum {
        mainKey: 1;
        normalProperty: 2;
        vitalCollection: 3;
        vitalReference: 4;
        collection: 5;
        reference: 6;
    }
    export type DisplayedPropertyType = keyof DisplayedPropertyType$Enum;
    export interface FileFormat$Enum {
        csv: 1;
        xlsx: 2;
    }
    export type FileFormat = keyof FileFormat$Enum;
    export interface ImportStatus$Enum {
        inProgress: 1;
        finished: 2;
        rejected: 3;
        pending: 4;
        failed: 5;
    }
    export type ImportStatus = keyof ImportStatus$Enum;
    export interface TemplateUse$Enum {
        importOnly: 1;
        exportOnly: 2;
        importAndExport: 3;
    }
    export type TemplateUse = keyof TemplateUse$Enum;
    export interface ImportExportTemplate extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        id: string;
        name: string;
        description: string;
        nodeName: string;
        isActive: boolean;
        isDefault: boolean;
        templateUse: TemplateUse;
        defaultParameters: string;
        csvTemplate: string;
    }
    export interface ImportExportTemplateInput extends ClientNodeInput {
        _vendor?: integer | string;
        id?: string;
        name?: string;
        description?: string;
        nodeName?: string;
        isActive?: boolean | string;
        isDefault?: boolean | string;
        templateUse?: TemplateUse;
        defaultParameters?: string;
        csvTemplate?: string;
    }
    export interface ImportExportTemplateBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        id: string;
        name: string;
        description: string;
        nodeName: string;
        isActive: boolean;
        isDefault: boolean;
        templateUse: TemplateUse;
        defaultParameters: any;
        csvTemplate: any;
    }
    export interface ImportExportTemplate$Queries {
        getFieldsByFactory: Node$Operation<
            {
                factoryName: string;
                level?: integer | string;
                withSortValue?: boolean | string;
                isExportOnly?: boolean | string;
            },
            string[]
        >;
        getNodeList: Node$Operation<
            {
                filter?: string;
            },
            {
                name: string;
                canCreate: boolean;
                canUpdate: boolean;
                canImport: boolean;
                canExport: boolean;
            }[]
        >;
        getUserImportExportPreferences: Node$Operation<{}, UserPreferences>;
        getDefaultCsvTemplate: Node$Operation<
            {
                nodeName: string;
                level?: integer | string;
                isExportWithDefinition?: boolean | string;
                withSortValue?: boolean | string;
            },
            {
                csvTemplate: TextStream;
                technical: TextStream;
                canImport: boolean;
                canExport: boolean;
            }
        >;
        getTechnicalInformation: Node$Operation<
            {
                property: {
                    name?: string;
                    factory?: {
                        name?: string;
                    };
                };
                level?: integer | string;
                parentProperty: {
                    name?: string;
                    factory?: {
                        name?: string;
                    };
                };
                isExportOnly?: boolean | string;
            },
            {
                path: string;
                displayedPropertyType: DisplayedPropertyType;
                dataType: string;
                description: string;
                locale: string;
            }
        >;
    }
    export interface ImportExportTemplate$AsyncOperations {
        batchImport: AsyncOperation<
            {
                templateId: string;
                uploadedFileId: string;
                options?: {
                    doInsert?: boolean | string;
                    doUpdate?: boolean | string;
                    dryRun?: boolean | string;
                    maxErrorCount?: integer | string;
                };
            },
            ImportResult
        >;
        exportByTemplateId: AsyncOperation<
            {
                templateId: string;
                outputFormat: FileFormat;
                filter: string;
            },
            string
        >;
        exportByTemplateDefinition: AsyncOperation<
            {
                templateDefinition: {
                    path?: string;
                    title?: string;
                }[];
                nodeName: string;
                outputFormat: FileFormat;
                filter: string;
                orderBy: string;
            },
            string
        >;
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface ImportExportTemplate$Lookups {
        _vendor: QueryOperation<SysVendor>;
    }
    export interface ImportExportTemplate$Operations {
        query: QueryOperation<ImportExportTemplate>;
        read: ReadOperation<ImportExportTemplate>;
        aggregate: {
            read: AggregateReadOperation<ImportExportTemplate>;
            query: AggregateQueryOperation<ImportExportTemplate>;
        };
        queries: ImportExportTemplate$Queries;
        create: CreateOperation<ImportExportTemplateInput, ImportExportTemplate>;
        getDuplicate: GetDuplicateOperation<ImportExportTemplate>;
        update: UpdateOperation<ImportExportTemplateInput, ImportExportTemplate>;
        updateById: UpdateByIdOperation<ImportExportTemplateInput, ImportExportTemplate>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: ImportExportTemplate$AsyncOperations;
        lookups(dataOrId: string | { data: ImportExportTemplateInput }): ImportExportTemplate$Lookups;
        getDefaults: GetDefaultsOperation<ImportExportTemplate>;
    }
    export interface ImportResult extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        startStamp: string;
        endStamp: string;
        filename: string;
        rowsProcessed: integer;
        numberOfRowsInError: integer;
        rowsInError: TextStream;
        status: ImportStatus;
        dryRun: boolean;
        doUpdate: boolean;
        doInsert: boolean;
        maxErrorCount: integer;
        uploadedFile: UploadedFile;
        uploadRejectReason: string;
        generalError: string;
        importExportTemplate: ImportExportTemplate;
        parameters: string;
        notificationId: string;
    }
    export interface ImportResultInput extends ClientNodeInput {
        startStamp?: string;
        endStamp?: string;
        rowsProcessed?: integer | string;
        numberOfRowsInError?: integer | string;
        rowsInError?: TextStream;
        status?: ImportStatus;
        dryRun?: boolean | string;
        doUpdate?: boolean | string;
        doInsert?: boolean | string;
        maxErrorCount?: integer | string;
        uploadedFile?: integer | string;
        generalError?: string;
        importExportTemplate?: integer | string;
        parameters?: string;
        notificationId?: string;
    }
    export interface ImportResultBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        startStamp: string;
        endStamp: string;
        filename: string;
        rowsProcessed: integer;
        numberOfRowsInError: integer;
        rowsInError: TextStream;
        status: ImportStatus;
        dryRun: boolean;
        doUpdate: boolean;
        doInsert: boolean;
        maxErrorCount: integer;
        uploadedFile: UploadedFile;
        uploadRejectReason: string;
        generalError: string;
        importExportTemplate: ImportExportTemplate;
        parameters: any;
        notificationId: string;
    }
    export interface ImportResult$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface ImportResult$Lookups {
        uploadedFile: QueryOperation<UploadedFile>;
        importExportTemplate: QueryOperation<ImportExportTemplate>;
    }
    export interface ImportResult$Operations {
        query: QueryOperation<ImportResult>;
        read: ReadOperation<ImportResult>;
        aggregate: {
            read: AggregateReadOperation<ImportResult>;
            query: AggregateQueryOperation<ImportResult>;
        };
        create: CreateOperation<ImportResultInput, ImportResult>;
        getDuplicate: GetDuplicateOperation<ImportResult>;
        update: UpdateOperation<ImportResultInput, ImportResult>;
        updateById: UpdateByIdOperation<ImportResultInput, ImportResult>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: ImportResult$AsyncOperations;
        lookups(dataOrId: string | { data: ImportResultInput }): ImportResult$Lookups;
        getDefaults: GetDefaultsOperation<ImportResult>;
    }
    export interface Package {
        '@sage/xtrem-import-export/ImportExportTemplate': ImportExportTemplate$Operations;
        '@sage/xtrem-import-export/ImportResult': ImportResult$Operations;
    }
    export interface GraphApi
        extends Package,
            SageXtremAuditing$Package,
            SageXtremAuthorization$Package,
            SageXtremCommunication$Package,
            SageXtremCustomization$Package,
            SageXtremDashboard$Package,
            SageXtremMetadata$Package,
            SageXtremRouting$Package,
            SageXtremScheduler$Package,
            SageXtremSystem$Package,
            SageXtremUpload$Package,
            SageXtremWorkflow$Package {}
}
declare module '@sage/xtrem-import-export-api' {
    export type * from '@sage/xtrem-import-export-api-partial';
}
declare module '@sage/xtrem-auditing-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-import-export-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-authorization-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-import-export-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-communication-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-import-export-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-customization-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-import-export-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-dashboard-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-import-export-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-metadata-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-import-export-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-routing-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-import-export-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-scheduler-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-import-export-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-system-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-import-export-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-upload-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-import-export-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-workflow-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-import-export-api';
    export interface GraphApi extends GraphApiExtension {}
}
