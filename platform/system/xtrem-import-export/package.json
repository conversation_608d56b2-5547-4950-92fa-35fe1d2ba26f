{"name": "@sage/xtrem-import-export", "description": "XTREM import export", "version": "58.0.2", "xtrem": {"isPlatform": true, "isHidden": true, "isService": true, "scope": "importexport", "queue": "import-export"}, "keywords": ["xtrem-import-export"], "author": "Sage", "license": "UNLICENSED", "publishConfig": {"registry": "https://pkgs.dev.azure.com/Sage-LiveServices/_packaging/Sage-ERP/npm/registry/"}, "repository": {"type": "git", "url": "git://github.com/Sage-ERP-X3/xtrem.git"}, "main": "build/index.js", "files": ["build", "data", "routing.json"], "typings": "build/package-definition.d.ts", "dependencies": {"@aws-sdk/client-s3": "^3.826.0", "@aws-sdk/client-sso-oidc": "^3.826.0", "@aws-sdk/client-sts": "^3.826.0", "@aws-sdk/credential-provider-node": "^3.826.0", "@aws-sdk/credential-providers": "^3.826.0", "@aws-sdk/s3-request-presigner": "^3.826.0", "@sage/xtrem-async-helper": "workspace:*", "@sage/xtrem-authorization": "workspace:*", "@sage/xtrem-client": "workspace:*", "@sage/xtrem-communication": "workspace:*", "@sage/xtrem-core": "workspace:*", "@sage/xtrem-customization": "workspace:*", "@sage/xtrem-date-time": "workspace:*", "@sage/xtrem-decimal": "workspace:*", "@sage/xtrem-file-storage": "^6.1.5", "@sage/xtrem-infrastructure-adapter": "workspace:*", "@sage/xtrem-metadata": "workspace:*", "@sage/xtrem-shared": "workspace:*", "@sage/xtrem-system": "workspace:*", "@sage/xtrem-ui": "workspace:*", "@sage/xtrem-ui-plugin-monaco": "workspace:*", "@sage/xtrem-upload": "workspace:*", "csv-parse": "^6.0.0", "csv-stringify": "^6.4.0", "exceljs": "^4.4.0", "lodash": "^4.17.21"}, "devDependencies": {"@sage/xtrem-cli": "workspace:*", "@sage/xtrem-cli-bundle-dev": "workspace:*", "@sage/xtrem-import-export-api": "workspace:*", "@sage/xtrem-metadata-api": "workspace:*", "@sage/xtrem-system-api": "workspace:*", "@types/chai": "^4.3.6", "@types/chai-as-promised": "^7.1.6", "@types/lodash": "^4.14.198", "@types/mocha": "^10.0.1", "@types/node": "^22.10.2", "chai": "^4.3.10", "chai-as-promised": "^7.1.1", "mocha": "^10.8.2"}, "scripts": {"build": "xtrem compile", "build:api": "xtrem build --only-api-client", "build:binary": "xtrem compile --binary --prod", "build:cache": "turbo run build --concurrency=${XTREM_CONCURRENCY:=10}", "clean": "rm -rf build", "extract:demo:data": "xtrem layers --extract demo", "extract:qa:data": "xtrem layers --extract qa", "lint": "xtrem lint", "load:demo:data": "xtrem layers --load setup,demo", "load:qa:data": "xtrem layers --load setup,qa", "load:setup:data": "xtrem layers --load setup", "load:test:data": "xtrem layers --load setup,test", "qa:cucumber": "xtrem test test/cucumber/* --integration --noTimeout --layers=qa", "qa:cucumber:browser": "xtrem test test/cucumber/* --integration --browser --noTimeout --layers=qa", "sonarqube:scanner": "pnpm test:ci && pnpm dlx sonarqube-scanner && rm -rf coverage .nyc_output .scannerwork junit-report-*.xml", "start": "xtrem start", "test": "cross-env TZ=CET xtrem test --noTimeout --unit --graphql --layers=test", "test:ci": "cross-env TZ=CET xtrem test --noTimeout --unit --ci --layers=test", "test:graphql": "xtrem test --noTimeout --graphql --layers=test", "test:smoke": "xtrem test 'test/cucumber/smoke-test*.feature' --integration --layers=test", "test:smoke:ci": "xtrem test 'test/cucumber/smoke-test*.feature' --integration --layers=test --ci", "test:smoke:static": "xtrem test test/cucumber/smoke-test-static.feature --integration --layers=test", "test:smoke:static:ci": "xtrem test test/cucumber/smoke-test-static.feature --integration --layers=test --ci", "test:unit": "xtrem test --noTimeout --unit --layers=test", "xtrem": "xtrem"}, "c8": {"cache": false, "all": true, "extension": [".ts", ".tsx"], "sourceMap": true, "instrument": true, "reporter": ["text-summary", "clover", "json", "lcov"], "include": ["lib/**/*.ts"], "exclude": ["test/**/*", "data/**/*"]}}