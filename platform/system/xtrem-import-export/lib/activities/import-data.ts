import { Activity } from '@sage/xtrem-core';
import * as xtremUpload from '@sage/xtrem-upload';
import * as xtremImportExport from '../index';

export const importData = new Activity({
    description: 'Data import',
    node: () => xtremImportExport.nodes.ImportResult,
    __filename,
    permissions: ['read', 'batchImport'],
    operationGrants: {
        read: [
            { operations: ['read'], on: [() => xtremImportExport.nodes.ImportResult] },
            { operations: ['getNodeList'], on: [() => xtremImportExport.nodes.ImportExportTemplate] },
        ],
        batchImport: [
            { operations: ['read', 'create'], on: [() => xtremImportExport.nodes.ImportResult] },
            { operations: ['create', 'update'], on: [() => xtremUpload.nodes.UploadedFile] },
            { operations: ['batchImport', 'getNodeList'], on: [() => xtremImportExport.nodes.ImportExportTemplate] },
        ],
    },
});
