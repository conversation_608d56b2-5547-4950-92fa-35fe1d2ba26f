import { Activity } from '@sage/xtrem-core';
import { ImportExportTemplate } from '../nodes/import-export-template';
import { ImportResult } from '../nodes/import-result';

export const importExportTemplate = new Activity({
    description: 'Import Export template',
    node: () => ImportExportTemplate,
    __filename,
    permissions: ['read', 'manage'],
    operationGrants: {
        manage: [
            {
                operations: [
                    'read',
                    'create',
                    'update',
                    'delete',
                    'getFieldsByFactory',
                    'getDefaultCsvTemplate',
                    'getNodeList',
                    'getTechnicalInformation',
                    'exportByTemplateId',
                    'exportByTemplateDefinition',
                ],
                on: [() => ImportExportTemplate],
            },
            {
                operations: ['lookup'],
                on: [() => ImportResult],
            },
        ],
        read: [
            {
                operations: [
                    'read',
                    'getFieldsByFactory',
                    'getDefaultCsvTemplate',
                    'getNodeList',
                    'getTechnicalInformation',
                    'getUserImportExportPreferences',
                ],
                on: [() => ImportExportTemplate],
            },
            {
                operations: ['lookup'],
                on: [() => ImportResult],
            },
        ],
    },
});
