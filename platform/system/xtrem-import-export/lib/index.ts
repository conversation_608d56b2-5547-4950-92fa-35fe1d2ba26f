import '@sage/xtrem-communication';
import { CoreHooks } from '@sage/xtrem-core';
import * as activities from './activities/_index';
import * as dataTypes from './data-types/_index';
import * as enums from './enums/_index';
import * as functions from './functions/_index';
import { importExportManager } from './import-export';
import * as menuItems from './menu-items/index';
import * as nodes from './nodes/_index';
import * as sharedFunctions from './shared-functions';

export { uploadTestFile } from './nodes/utils';
export { activities, dataTypes, enums, functions, menuItems, nodes, sharedFunctions };

CoreHooks.importExportManager = importExportManager;
