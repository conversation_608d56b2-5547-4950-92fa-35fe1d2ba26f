{"@sage/xtrem-import-export/activity__import_data__name": "Import de données", "@sage/xtrem-import-export/activity__import_export_template__name": "Modèle import/export", "@sage/xtrem-import-export/confirm-import": "Confirmer l'import", "@sage/xtrem-import-export/confirm-import-question": "Voulez-vous importer le fichier ?", "@sage/xtrem-import-export/data_types__csv_stream_type__name": "Type flux CSV", "@sage/xtrem-import-export/data_types__csv_template_text_stream_type__name": "Type de flux de texte du modèle CSV", "@sage/xtrem-import-export/data_types__displayed_property_type_enum__name": "Enum type propriété affichée", "@sage/xtrem-import-export/data_types__file_format_enum__name": "Enum format fichier", "@sage/xtrem-import-export/data_types__html_data_type__name": "Type de données HTML", "@sage/xtrem-import-export/data_types__import_status_enum__name": "Enum du statut d'import", "@sage/xtrem-import-export/data_types__name_data_type__name": "Type de données nom", "@sage/xtrem-import-export/data_types__rows_text_stream_type__name": "Type flux texte ligne", "@sage/xtrem-import-export/data_types__short_string_data_type__name": "Type de données chaîne courte", "@sage/xtrem-import-export/data_types__template_use_enum__name": "Enum utilisation modèle", "@sage/xtrem-import-export/data_types__test_enum_enum__name": "Enum enum test", "@sage/xtrem-import-export/default_parameters-decimal_point-invalid_value": "Le point décimal n'est pas valide : {{value}}.", "@sage/xtrem-import-export/default_parameters-delimiter-invalid_value": "Le séparateur n'est pas valide : {{value}}.", "@sage/xtrem-import-export/edit-create-line": "Insérer une colonne modèle", "@sage/xtrem-import-export/enums__displayed_property_type__collection": "Collection", "@sage/xtrem-import-export/enums__displayed_property_type__mainKey": "Clé principale", "@sage/xtrem-import-export/enums__displayed_property_type__normalProperty": "Propriété normale", "@sage/xtrem-import-export/enums__displayed_property_type__reference": "Référence", "@sage/xtrem-import-export/enums__displayed_property_type__vitalCollection": "Collection vitale", "@sage/xtrem-import-export/enums__displayed_property_type__vitalReference": "Réfé<PERSON><PERSON> vitale", "@sage/xtrem-import-export/enums__file_format__csv": "CSV", "@sage/xtrem-import-export/enums__file_format__xlsx": "XLSX", "@sage/xtrem-import-export/enums__import_status__failed": "Échec", "@sage/xtrem-import-export/enums__import_status__finished": "Exécuté", "@sage/xtrem-import-export/enums__import_status__inProgress": "En cours", "@sage/xtrem-import-export/enums__import_status__pending": "En attente", "@sage/xtrem-import-export/enums__import_status__rejected": "<PERSON><PERSON><PERSON>", "@sage/xtrem-import-export/enums__template_use__exportOnly": "Export uniquement", "@sage/xtrem-import-export/enums__template_use__importAndExport": "Import et export", "@sage/xtrem-import-export/enums__template_use__importOnly": "Import uiquement", "@sage/xtrem-import-export/enums__test_enum__value1": "Valeur 1", "@sage/xtrem-import-export/enums__test_enum__value2": "Valeur 2", "@sage/xtrem-import-export/enums__test_enum__value3": "Valeur 3", "@sage/xtrem-import-export/function__export_send_client_notification_description": "Fiches exportées : {{numberOfRecords}}.", "@sage/xtrem-import-export/function__export_send_client_notification_title_fail": "Cet export utilisant ce modèle s'est achevé avec une erreur : {{templateId}}.", "@sage/xtrem-import-export/function__export_send_client_notification_title_success": "Cet export utilisant ce modèle s'est correctement déroulé : {{templateId}}.", "@sage/xtrem-import-export/functions__csv_to_json__header_disabled_by_a_service_option": "L'en-tête {{csvHeader}} est désactivé par une option de service.", "@sage/xtrem-import-export/functions__csv_to_json__header_is_an_encrypted_string": "Cet en-tête n'est pas une chaîne cryptée : {{csvHeader}}.", "@sage/xtrem-import-export/functions__csv_to_json__invalid_custom_field_column_header": "Champ personnalis<PERSON> incorrect dans l'en-tête de colonne : {{csvHeader}}.", "@sage/xtrem-import-export/functions__csv_to_json__missing_property_header": "Propriété {{propertyName}} introuvable dans l'hôte livré {{factoryName}}.", "@sage/xtrem-import-export/invalid-reference-header-invalid-natural-key": "Les propriétés clés de l'en-tête {{name}} ne sont pas valides. Modifiez la clé en {{key}}.", "@sage/xtrem-import-export/invalid-reference-header-no-natural-key": "L'en-tête {{name}} ne peut pas être compris dans le modèle d'import. Le node cible {{target}} doit posséder un index de clés naturelles.", "@sage/xtrem-import-export/invalid-reference-header-unexpected-index-on-natural-key": "L'en-tête de colonne {{name}} est incorrect. Supprimer '({{columns}})' après le nom de la propriété.", "@sage/xtrem-import-export/menu_item__import": "Import", "@sage/xtrem-import-export/menu_item__import-data": "Import", "@sage/xtrem-import-export/menu_item__import-export": "Import et export", "@sage/xtrem-import-export/missing-natural-key-header": "La clé naturelle pour le node {{node}} ne contient pas des propriétés qui ne sont pas comprises dans le modèle. Ajoutez les propriétés manquantes : {{missing}}", "@sage/xtrem-import-export/no-file-to-import": "Aucun fichier à importer", "@sage/xtrem-import-export/no-template-selected": "Sélectionner un modèle existant pour chaque fichier à importer", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__batchImport": "Import batch", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__batchImport__failed": "L'import batch a échoué.", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__batchImport__parameter__options": "Options", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__batchImport__parameter__templateId": "Code du modèle", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__batchImport__parameter__uploadedFileId": "Code du fichier téléchargé", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__exportByTemplateDefinition": "Export par définition de modèle", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__exportByTemplateDefinition__failed": "L'export par définition de modèle a échoué.", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__exportByTemplateDefinition__parameter__filter": "Filtre", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__exportByTemplateDefinition__parameter__nodeName": "Nom du node", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__exportByTemplateDefinition__parameter__orderBy": "Ordre/Commande par", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__exportByTemplateDefinition__parameter__outputFormat": "Format de sortie", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__exportByTemplateDefinition__parameter__templateDefinition": "Définition du modèle", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__exportByTemplateId": "Export par code modèle", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__exportByTemplateId__failed": "L'export par modèle a échoué.", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__exportByTemplateId__parameter__filter": "Filtre", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__exportByTemplateId__parameter__outputFormat": "Format de sortie", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__exportByTemplateId__parameter__templateId": "Code modèle", "@sage/xtrem-import-export/nodes__import_export_template__node_name": "Modèle import/export", "@sage/xtrem-import-export/nodes__import_export_template__property__canImport": "Import possible", "@sage/xtrem-import-export/nodes__import_export_template__property__csvTemplate": "Modèle CSV", "@sage/xtrem-import-export/nodes__import_export_template__property__defaultParameters": "Paramètres par défaut", "@sage/xtrem-import-export/nodes__import_export_template__property__description": "Description", "@sage/xtrem-import-export/nodes__import_export_template__property__id": "Code", "@sage/xtrem-import-export/nodes__import_export_template__property__isActive": "Active", "@sage/xtrem-import-export/nodes__import_export_template__property__isDefault": "<PERSON><PERSON> <PERSON><PERSON>", "@sage/xtrem-import-export/nodes__import_export_template__property__name": "Nom", "@sage/xtrem-import-export/nodes__import_export_template__property__nodeName": "Nom du node", "@sage/xtrem-import-export/nodes__import_export_template__property__templateUse": "Utilisation du modèle", "@sage/xtrem-import-export/nodes__import_export_template__query__getDefaultCsvTemplate": "O<PERSON><PERSON>r le modèle CSV par défaut", "@sage/xtrem-import-export/nodes__import_export_template__query__getDefaultCsvTemplate__failed": "La récupération du modèle CSV par défaut  a échoué.", "@sage/xtrem-import-export/nodes__import_export_template__query__getDefaultCsvTemplate__parameter__isExportWithDefinition": "Export avec définition", "@sage/xtrem-import-export/nodes__import_export_template__query__getDefaultCsvTemplate__parameter__level": "Niveau", "@sage/xtrem-import-export/nodes__import_export_template__query__getDefaultCsvTemplate__parameter__nodeName": "Nom du node", "@sage/xtrem-import-export/nodes__import_export_template__query__getDefaultCsvTemplate__parameter__withSortValue": "Avec valeur de tri", "@sage/xtrem-import-export/nodes__import_export_template__query__getDisplayedPropertyTypeFromName": "Obtenir le type de propriété affichée à partir du nom", "@sage/xtrem-import-export/nodes__import_export_template__query__getDisplayedPropertyTypeFromName__parameter__factoryName": "Nom livré", "@sage/xtrem-import-export/nodes__import_export_template__query__getDisplayedPropertyTypeFromName__parameter__level": "Niveau", "@sage/xtrem-import-export/nodes__import_export_template__query__getDisplayedPropertyTypeFromName__parameter__propertyName": "Nom de propriété", "@sage/xtrem-import-export/nodes__import_export_template__query__getFieldsByFactory": "Obtenir champs par édition livrée", "@sage/xtrem-import-export/nodes__import_export_template__query__getFieldsByFactory__failed": "La récupération des champs livrés a échoué.", "@sage/xtrem-import-export/nodes__import_export_template__query__getFieldsByFactory__parameter__factoryName": "Nom livré", "@sage/xtrem-import-export/nodes__import_export_template__query__getFieldsByFactory__parameter__isExportOnly": "Export uniquement", "@sage/xtrem-import-export/nodes__import_export_template__query__getFieldsByFactory__parameter__level": "Niveau", "@sage/xtrem-import-export/nodes__import_export_template__query__getFieldsByFactory__parameter__withSortValue": "Avec valeur de tri", "@sage/xtrem-import-export/nodes__import_export_template__query__getNodeList": "Obtenir la liste des nodes", "@sage/xtrem-import-export/nodes__import_export_template__query__getNodeList__failed": "La récupération de la liste de nodes a échoué.", "@sage/xtrem-import-export/nodes__import_export_template__query__getNodeList__parameter__filter": "Filtre", "@sage/xtrem-import-export/nodes__import_export_template__query__getPropertyDataTypeFromName": "Obtenir le type de données de propriété à partir du nom", "@sage/xtrem-import-export/nodes__import_export_template__query__getPropertyDataTypeFromName__parameter__factoryName": "Nom livré", "@sage/xtrem-import-export/nodes__import_export_template__query__getPropertyDataTypeFromName__parameter__propertyName": "Nom de propriété", "@sage/xtrem-import-export/nodes__import_export_template__query__getPropertyDescriptionFromName": "Obtenir la description de la propriété à partir du nom", "@sage/xtrem-import-export/nodes__import_export_template__query__getPropertyDescriptionFromName__parameter__factoryName": "Nom livré", "@sage/xtrem-import-export/nodes__import_export_template__query__getPropertyDescriptionFromName__parameter__propertyName": "Nom de propriété", "@sage/xtrem-import-export/nodes__import_export_template__query__getPropertyLocaleFromName": "Obtenir les paramètres régionaux de la propriété à partir du nom", "@sage/xtrem-import-export/nodes__import_export_template__query__getPropertyLocaleFromName__parameter__factoryName": "Nom livré", "@sage/xtrem-import-export/nodes__import_export_template__query__getPropertyLocaleFromName__parameter__propertyName": "Nom de propriété", "@sage/xtrem-import-export/nodes__import_export_template__query__getPropertyPathFromName": "Obt<PERSON><PERSON> le chemin de la propriété à partir du nom", "@sage/xtrem-import-export/nodes__import_export_template__query__getPropertyPathFromName__parameter__factoryName": "Nom livré", "@sage/xtrem-import-export/nodes__import_export_template__query__getPropertyPathFromName__parameter__level": "Niveau", "@sage/xtrem-import-export/nodes__import_export_template__query__getPropertyPathFromName__parameter__parentNonVitalReferencePath": "Chemin de référence parent non vital", "@sage/xtrem-import-export/nodes__import_export_template__query__getPropertyPathFromName__parameter__propertyName": "Nom de propriété", "@sage/xtrem-import-export/nodes__import_export_template__query__getTechnicalInformation": "Obtenir informations techniques", "@sage/xtrem-import-export/nodes__import_export_template__query__getTechnicalInformation__failed": "La récupération des informations techniques a échoué.", "@sage/xtrem-import-export/nodes__import_export_template__query__getTechnicalInformation__parameter__isExportOnly": "Export uniquement", "@sage/xtrem-import-export/nodes__import_export_template__query__getTechnicalInformation__parameter__level": "Niveau", "@sage/xtrem-import-export/nodes__import_export_template__query__getTechnicalInformation__parameter__parentProperty": "Propriété parente", "@sage/xtrem-import-export/nodes__import_export_template__query__getTechnicalInformation__parameter__property": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-import-export/nodes__import_export_template__query__getUserImportExportPreferences": "Obtenir préférences import export utilisateur", "@sage/xtrem-import-export/nodes__import_export_template__query__getUserImportExportPreferences__failed": "Échec de l'obtention des préférences d'import export utilisateur.", "@sage/xtrem-import-export/nodes__import_result__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-import-export/nodes__import_result__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-import-export/nodes__import_result__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-import-export/nodes__import_result__node_name": "Résultat d'import", "@sage/xtrem-import-export/nodes__import_result__property__doInsert": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-import-export/nodes__import_result__property__doUpdate": "Mettre à jour", "@sage/xtrem-import-export/nodes__import_result__property__dryRun": "Test", "@sage/xtrem-import-export/nodes__import_result__property__endStamp": "Horodatage de fin", "@sage/xtrem-import-export/nodes__import_result__property__filename": "Nom de fi<PERSON>er", "@sage/xtrem-import-export/nodes__import_result__property__generalError": "<PERSON><PERSON><PERSON>", "@sage/xtrem-import-export/nodes__import_result__property__importExportTemplate": "Modèle import/export", "@sage/xtrem-import-export/nodes__import_result__property__maxErrorCount": "Nombre maximum d'erreurs d'import autorisées", "@sage/xtrem-import-export/nodes__import_result__property__notificationId": "Code de notification", "@sage/xtrem-import-export/nodes__import_result__property__numberOfRowsInError": "Nombre de lignes en erreur", "@sage/xtrem-import-export/nodes__import_result__property__parameters": "Paramètres", "@sage/xtrem-import-export/nodes__import_result__property__rowsInError": "Lignes avec erreurs", "@sage/xtrem-import-export/nodes__import_result__property__rowsProcessed": "<PERSON><PERSON><PERSON>", "@sage/xtrem-import-export/nodes__import_result__property__startStamp": "Démarrer horodatage", "@sage/xtrem-import-export/nodes__import_result__property__status": "Statut", "@sage/xtrem-import-export/nodes__import_result__property__uploadedFile": "<PERSON><PERSON><PERSON>", "@sage/xtrem-import-export/nodes__import_result__property__uploadRejectReason": "Télécharger motif rejet", "@sage/xtrem-import-export/nodes__import_template__property__setupId": "Code paramétrage", "@sage/xtrem-import-export/nodes__parsed_result_bucket__node_name": "Compartiment de résultats analysé", "@sage/xtrem-import-export/package__name": "Import", "@sage/xtrem-import-export/page__import_export_template__cannot_move_further": "Vous ne pouvez pas bouger la ligne davantage.", "@sage/xtrem-import-export/page__import_export_template__cannot_move_group_main_key": "La ligne courante représente la clé principale du groupe. Vous ne pouvez pas la bouger.", "@sage/xtrem-import-export/page__import_export_template__cannot_move_outside_parent_group": "<PERSON><PERSON> <PERSON> conserver la ligne au sein du groupe parent.", "@sage/xtrem-import-export/page__import_export_template__confirm_deletion": "Confirmer la <PERSON>", "@sage/xtrem-import-export/page__import_export_template__confirm_deletion_of_sublevel": "Vous êtes sur le point de supprimer un sous-niveau et toutes les saisies associées. Supprimer ?", "@sage/xtrem-import-export/page__import_export_template__line_values_not_found": "Valeurs de lignes introuvables", "@sage/xtrem-import-export/page__import_export_template__moving_line_up_down": "Bouger la ligne vers le haut ou vers le bas", "@sage/xtrem-import-export/pages__export_bulk_mutation_configuration____title": "Sélectionner un modèle d'export", "@sage/xtrem-import-export/pages__export_bulk_mutation_configuration__id____columns__title__description": "Description", "@sage/xtrem-import-export/pages__export_bulk_mutation_configuration__id____columns__title__id": "Code", "@sage/xtrem-import-export/pages__export_bulk_mutation_configuration__id____columns__title__name": "Nom du modèle", "@sage/xtrem-import-export/pages__export_bulk_mutation_configuration__id____title": "Modèle d'import", "@sage/xtrem-import-export/pages__import_data____title": "Import de données", "@sage/xtrem-import-export/pages__import_data__continueAfterErrorOption____title": "Con<PERSON>uer et ignorer l'erreur", "@sage/xtrem-import-export/pages__import_data__csvFile____title": "Sélectionner un fichier", "@sage/xtrem-import-export/pages__import_data__downloadSection____title": "Télécharger", "@sage/xtrem-import-export/pages__import_data__executeImport____title": "Import", "@sage/xtrem-import-export/pages__import_data__fileBlock____title": "Fichier CSV", "@sage/xtrem-import-export/pages__import_data__fileField____title": "Résultat d'import", "@sage/xtrem-import-export/pages__import_data__generalSection____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-import-export/pages__import_data__importAction____title": "Import", "@sage/xtrem-import-export/pages__import_data__importData____columns__title__filename": "Nom de fi<PERSON>er", "@sage/xtrem-import-export/pages__import_data__importData____columns__title__nodeName": "Nom du node", "@sage/xtrem-import-export/pages__import_data__importData____columns__title__template": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-import-export/pages__import_data__importData____columns__title__templateDescription": "Description du modèle", "@sage/xtrem-import-export/pages__import_data__importData____columns__title__templateName": "Nom du modèle", "@sage/xtrem-import-export/pages__import_data__importData____title": "Fichiers à importer", "@sage/xtrem-import-export/pages__import_data__importResults____columns__title__dryRun": "Test", "@sage/xtrem-import-export/pages__import_data__importResults____columns__title__endStamp": "Heure de fin", "@sage/xtrem-import-export/pages__import_data__importResults____columns__title__filename": "Nom de fi<PERSON>er", "@sage/xtrem-import-export/pages__import_data__importResults____columns__title__generalError": "<PERSON><PERSON><PERSON>'<PERSON>", "@sage/xtrem-import-export/pages__import_data__importResults____columns__title__numberOfRowsInError": "Nombre d'erreurs", "@sage/xtrem-import-export/pages__import_data__importResults____columns__title__rowsProcessed": "<PERSON><PERSON><PERSON>", "@sage/xtrem-import-export/pages__import_data__importResults____columns__title__startStamp": "<PERSON><PERSON> d<PERSON>", "@sage/xtrem-import-export/pages__import_data__importResults____columns__title__status": "Statut", "@sage/xtrem-import-export/pages__import_data__importResults____dropdownActions__title": "Télécharger", "@sage/xtrem-import-export/pages__import_data__importResults____title": "Résultats", "@sage/xtrem-import-export/pages__import_data__insertOption____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-import-export/pages__import_data__maximumNumberOfErrors____title": "Nombre d'erreurs maximum", "@sage/xtrem-import-export/pages__import_data__optionsBlock____title": "Options", "@sage/xtrem-import-export/pages__import_data__refreshRelationMapping____title": "Actualiser", "@sage/xtrem-import-export/pages__import_data__resultsSection____title": "Résultats d'import", "@sage/xtrem-import-export/pages__import_data__test____title": "Import test", "@sage/xtrem-import-export/pages__import_data__updateOption____title": "Mettre à jour", "@sage/xtrem-import-export/pages__import_data_page__testImportAction____title": "Test", "@sage/xtrem-import-export/pages__import_export_template____navigationPanel__listItem__idLine__title": "Code", "@sage/xtrem-import-export/pages__import_export_template____navigationPanel__listItem__line3__title": "Description", "@sage/xtrem-import-export/pages__import_export_template____navigationPanel__listItem__Line4__title": "Description", "@sage/xtrem-import-export/pages__import_export_template____navigationPanel__listItem__name__title": "Nom", "@sage/xtrem-import-export/pages__import_export_template____navigationPanel__listItem__title__title": "Code", "@sage/xtrem-import-export/pages__import_export_template____navigationPanel__listItem__titleLeft__title": "Description", "@sage/xtrem-import-export/pages__import_export_template____navigationPanel__listItem__titleRight__title": "Type", "@sage/xtrem-import-export/pages__import_export_template____navigationPanel__optionsMenu__title": "Tous", "@sage/xtrem-import-export/pages__import_export_template____navigationPanel__optionsMenu__title__2": "Modèles d'import et d'export", "@sage/xtrem-import-export/pages__import_export_template____navigationPanel__optionsMenu__title__3": "Modèles d'import", "@sage/xtrem-import-export/pages__import_export_template____navigationPanel__optionsMenu__title__4": "Modèles d'export", "@sage/xtrem-import-export/pages__import_export_template____objectTypePlural": "Modèles d'import et d'export", "@sage/xtrem-import-export/pages__import_export_template____objectTypeSingular": "Modèle d'import et d'export", "@sage/xtrem-import-export/pages__import_export_template____title": "Modèle d'import et d'export", "@sage/xtrem-import-export/pages__import_export_template___id____title": "Code", "@sage/xtrem-import-export/pages__import_export_template__canImport____title": "Importer le modèle", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__columns__currentProperty__name__title": "Livré", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__columns__currentProperty__name__title__2": "Livré cible", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__columns__parentProperty__name__title": "Livré propriété parent", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__columns__parentProperty__name__title__2": "Livré cible propriété parent", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__title__currentProperty__factory__name": "Livré", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__title__currentProperty__name": "<PERSON><PERSON>", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__title__currentProperty__targetFactory__name": "Livré cible", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__title__dataType": "Type données", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__title__description": "Description", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__title__factory__name": "Livré", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__title__isCustom": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__title__isExportOnly": "Export uniquement", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__title__isMandatory": "Obligatoire", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__title__locale": "Paramètres régionaux", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__title__parentProperty__factory__name": "Livré parent", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__title__parentProperty__name": "Propriété parente", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__title__parentProperty__targetFactory__name": "Livré cible parent", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__title__path": "Chemin", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__title__targetFactory__name": "Livré cible", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____dropdownActions__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____inlineActions__title": "Remonter", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____inlineActions__title__2": "Descendre", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____inlineActions__title__3": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____title": "Détails", "@sage/xtrem-import-export/pages__import_export_template__columnsTableBlock____title": "Colonnes modèles", "@sage/xtrem-import-export/pages__import_export_template__copyToClipboard____title": "Copier dans le presse-papiers", "@sage/xtrem-import-export/pages__import_export_template__csvTemplateDisplay____helperText": "Code source du modèle", "@sage/xtrem-import-export/pages__import_export_template__csvTemplateDisplay____title": "Modèle CSV", "@sage/xtrem-import-export/pages__import_export_template__customSave____title": "Enregistrer", "@sage/xtrem-import-export/pages__import_export_template__dateFormat____title": "Format date", "@sage/xtrem-import-export/pages__import_export_template__decimalPoint____title": "Point décimal", "@sage/xtrem-import-export/pages__import_export_template__defaultParametersBlock____title": "Options de formatage", "@sage/xtrem-import-export/pages__import_export_template__delimiter____title": "Séparateur", "@sage/xtrem-import-export/pages__import_export_template__description____title": "Description", "@sage/xtrem-import-export/pages__import_export_template__downloadSection____title": "Télécharger", "@sage/xtrem-import-export/pages__import_export_template__downloadTemplate____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-import-export/pages__import_export_template__downloadTemplateSection____title": "Télécharger", "@sage/xtrem-import-export/pages__import_export_template__escapeCharacter____title": "Caractère d'espace", "@sage/xtrem-import-export/pages__import_export_template__exportData____title": "Exporter les données", "@sage/xtrem-import-export/pages__import_export_template__exportSettingsBlock____title": "Exporter les paramètres", "@sage/xtrem-import-export/pages__import_export_template__exportTemplatePreviewBlock____title": "Modèle d'import", "@sage/xtrem-import-export/pages__import_export_template__fileField____title": "Résultat d'import", "@sage/xtrem-import-export/pages__import_export_template__fillGridFromClipboard____title": "<PERSON><PERSON><PERSON> le presse-papier dans le tableau", "@sage/xtrem-import-export/pages__import_export_template__generalSection____title": "Général", "@sage/xtrem-import-export/pages__import_export_template__generateTemplate____title": "<PERSON><PERSON><PERSON><PERSON> le modèle", "@sage/xtrem-import-export/pages__import_export_template__hasDataTypeLine____title": "Type de données", "@sage/xtrem-import-export/pages__import_export_template__hasDescriptionLine____title": "Description", "@sage/xtrem-import-export/pages__import_export_template__hasLocaleLine____title": "Paramètres régionaux", "@sage/xtrem-import-export/pages__import_export_template__historySection____title": "Historique d'import", "@sage/xtrem-import-export/pages__import_export_template__id____title": "Code", "@sage/xtrem-import-export/pages__import_export_template__importData____title": "Import de données", "@sage/xtrem-import-export/pages__import_export_template__importResults____columns__title__dryRun": "Test", "@sage/xtrem-import-export/pages__import_export_template__importResults____columns__title__endStamp": "Horodatage de fin", "@sage/xtrem-import-export/pages__import_export_template__importResults____columns__title__filename": "Nom de fi<PERSON>er", "@sage/xtrem-import-export/pages__import_export_template__importResults____columns__title__numberOfRowsInError": "Lignes en erreur", "@sage/xtrem-import-export/pages__import_export_template__importResults____columns__title__rowsProcessed": "<PERSON><PERSON><PERSON>", "@sage/xtrem-import-export/pages__import_export_template__importResults____columns__title__startStamp": "Horodatage de début", "@sage/xtrem-import-export/pages__import_export_template__importResults____columns__title__status": "Statut", "@sage/xtrem-import-export/pages__import_export_template__importResults____dropdownActions__title": "Télécharger", "@sage/xtrem-import-export/pages__import_export_template__importResults____title": "Résultats", "@sage/xtrem-import-export/pages__import_export_template__importSettingsBlock____title": "Importer les paramètres", "@sage/xtrem-import-export/pages__import_export_template__isActive____title": "Actif", "@sage/xtrem-import-export/pages__import_export_template__isDefault____title": "Modèle par défaut pour l'export", "@sage/xtrem-import-export/pages__import_export_template__name____title": "Nom", "@sage/xtrem-import-export/pages__import_export_template__nodeName____title": "Nom du node", "@sage/xtrem-import-export/pages__import_export_template__nodeProperty____columns__title__factory__name": "Livré", "@sage/xtrem-import-export/pages__import_export_template__nodeProperty____columns__title__targetFactory__name": "Livré cible", "@sage/xtrem-import-export/pages__import_export_template__nodeProperty____title": "Sélectionner pour ajouter un champ", "@sage/xtrem-import-export/pages__import_export_template__omitDataType____title": "Ne pas mentionner de type de données", "@sage/xtrem-import-export/pages__import_export_template__omitDescription____title": "Ne pas mentionner de description", "@sage/xtrem-import-export/pages__import_export_template__quoteCharacter____title": "Caractère de guillem<PERSON>", "@sage/xtrem-import-export/pages__import_export_template__refreshRelationMapping____title": "Actualiser", "@sage/xtrem-import-export/pages__import_export_template__resetGrid____title": "Réinitialiser le tableau", "@sage/xtrem-import-export/pages__import_export_template__templateUse____title": "Utilisation du modèle", "@sage/xtrem-import-export/pages__import_export_template_type__export": "Exporter", "@sage/xtrem-import-export/pages__import_export_template_type__import_and_export": "Import et export", "@sage/xtrem-import-export/pages__import_template__setupId____title": "ID", "@sage/xtrem-import-export/permission__batch_import__name": "Import batch", "@sage/xtrem-import-export/permission__manage__name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-import-export/permission__read__name": "Lecture", "@sage/xtrem-import-export/select-file": "Sélectionner un fichier CSV", "@sage/xtrem-import-export/validation-error": "Erreur de validation"}