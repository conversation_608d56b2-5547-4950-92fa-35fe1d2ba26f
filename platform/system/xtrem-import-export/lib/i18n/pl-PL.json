{"@sage/xtrem-import-export/activity__import_data__name": "", "@sage/xtrem-import-export/activity__import_export_template__name": "", "@sage/xtrem-import-export/confirm-import": "Potwierdź import", "@sage/xtrem-import-export/confirm-import-question": "Chcesz zaimportować plik?", "@sage/xtrem-import-export/data_types__csv_stream_type__name": "", "@sage/xtrem-import-export/data_types__csv_template_text_stream_type__name": "", "@sage/xtrem-import-export/data_types__displayed_property_type_enum__name": "", "@sage/xtrem-import-export/data_types__file_format_enum__name": "", "@sage/xtrem-import-export/data_types__html_data_type__name": "", "@sage/xtrem-import-export/data_types__import_status_enum__name": "", "@sage/xtrem-import-export/data_types__name_data_type__name": "", "@sage/xtrem-import-export/data_types__rows_text_stream_type__name": "", "@sage/xtrem-import-export/data_types__short_string_data_type__name": "", "@sage/xtrem-import-export/data_types__template_use_enum__name": "", "@sage/xtrem-import-export/data_types__test_enum_enum__name": "", "@sage/xtrem-import-export/default_parameters-decimal_point-invalid_value": "", "@sage/xtrem-import-export/edit-create-line": "", "@sage/xtrem-import-export/enums__displayed_property_type__collection": "", "@sage/xtrem-import-export/enums__displayed_property_type__mainKey": "", "@sage/xtrem-import-export/enums__displayed_property_type__normalProperty": "", "@sage/xtrem-import-export/enums__displayed_property_type__reference": "", "@sage/xtrem-import-export/enums__displayed_property_type__vitalCollection": "", "@sage/xtrem-import-export/enums__displayed_property_type__vitalReference": "", "@sage/xtrem-import-export/enums__file_format__csv": "", "@sage/xtrem-import-export/enums__file_format__xlsx": "", "@sage/xtrem-import-export/enums__import_status__failed": "Failed", "@sage/xtrem-import-export/enums__import_status__finished": "Completed", "@sage/xtrem-import-export/enums__import_status__inProgress": "W toku", "@sage/xtrem-import-export/enums__import_status__pending": "Pending", "@sage/xtrem-import-export/enums__import_status__rejected": "Rejected", "@sage/xtrem-import-export/enums__template_use__exportOnly": "", "@sage/xtrem-import-export/enums__template_use__importAndExport": "", "@sage/xtrem-import-export/enums__template_use__importOnly": "", "@sage/xtrem-import-export/enums__test_enum__value1": "Wartość 1", "@sage/xtrem-import-export/enums__test_enum__value2": "Wartość 2", "@sage/xtrem-import-export/enums__test_enum__value3": "Wartość 3", "@sage/xtrem-import-export/function__export_send_client_notification_description": "", "@sage/xtrem-import-export/function__export_send_client_notification_title_fail": "", "@sage/xtrem-import-export/function__export_send_client_notification_title_success": "", "@sage/xtrem-import-export/functions__csv_to_json__header_disabled_by_a_service_option": "", "@sage/xtrem-import-export/functions__csv_to_json__header_is_an_encrypted_string": "", "@sage/xtrem-import-export/functions__csv_to_json__invalid_custom_field_column_header": "", "@sage/xtrem-import-export/functions__csv_to_json__missing_property_header": "", "@sage/xtrem-import-export/invalid-reference-header-no-natural-key": "The {{name}} header cannot be included in the import template. The {{target}} target node needs to have a natural key index.", "@sage/xtrem-import-export/invalid-reference-header-unexpected-index-on-natural-key": "The {{name}} column header is not valid. Remove '({{columns}})' after the property name.", "@sage/xtrem-import-export/menu_item__import-data": "Import", "@sage/xtrem-import-export/menu_item__import-export": "", "@sage/xtrem-import-export/missing-natural-key-header": "The natural key for the {{node}} node is missing properties that are not included in the template. Add the missing properties: {{missing}}.", "@sage/xtrem-import-export/no-file-to-import": "Brak plików do zaimportowania.", "@sage/xtrem-import-export/no-template-selected": "<PERSON><PERSON><PERSON><PERSON> istniejący szablon dla każdego pliku do zaimportowania.", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__asyncExport": "", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__batchImport": "", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__batchImport__failed": "", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__batchImport__parameter__options": "", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__batchImport__parameter__templateId": "", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__batchImport__parameter__uploadedFileId": "", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__exportByTemplateDefinition": "", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__exportByTemplateDefinition__failed": "", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__exportByTemplateDefinition__parameter__filter": "", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__exportByTemplateDefinition__parameter__nodeName": "", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__exportByTemplateDefinition__parameter__orderBy": "", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__exportByTemplateDefinition__parameter__outputFormat": "", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__exportByTemplateDefinition__parameter__templateDefinition": "", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__exportByTemplateId": "", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__exportByTemplateId__failed": "", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__exportByTemplateId__parameter__filter": "", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__exportByTemplateId__parameter__outputFormat": "", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__exportByTemplateId__parameter__templateId": "", "@sage/xtrem-import-export/nodes__import_export_template__node_name": "", "@sage/xtrem-import-export/nodes__import_export_template__property__csvTemplate": "", "@sage/xtrem-import-export/nodes__import_export_template__property__defaultParameters": "", "@sage/xtrem-import-export/nodes__import_export_template__property__description": "", "@sage/xtrem-import-export/nodes__import_export_template__property__id": "", "@sage/xtrem-import-export/nodes__import_export_template__property__isActive": "", "@sage/xtrem-import-export/nodes__import_export_template__property__isDefault": "", "@sage/xtrem-import-export/nodes__import_export_template__property__name": "", "@sage/xtrem-import-export/nodes__import_export_template__property__nodeName": "", "@sage/xtrem-import-export/nodes__import_export_template__property__templateUse": "", "@sage/xtrem-import-export/nodes__import_export_template__query__getDefaultCsvTemplate": "", "@sage/xtrem-import-export/nodes__import_export_template__query__getDefaultCsvTemplate__failed": "", "@sage/xtrem-import-export/nodes__import_export_template__query__getDefaultCsvTemplate__parameter__isExportWithDefinition": "", "@sage/xtrem-import-export/nodes__import_export_template__query__getDefaultCsvTemplate__parameter__level": "", "@sage/xtrem-import-export/nodes__import_export_template__query__getDefaultCsvTemplate__parameter__nodeName": "", "@sage/xtrem-import-export/nodes__import_export_template__query__getDefaultCsvTemplate__parameter__withSortValue": "", "@sage/xtrem-import-export/nodes__import_export_template__query__getFieldsByFactory": "", "@sage/xtrem-import-export/nodes__import_export_template__query__getFieldsByFactory__failed": "", "@sage/xtrem-import-export/nodes__import_export_template__query__getFieldsByFactory__parameter__factoryName": "", "@sage/xtrem-import-export/nodes__import_export_template__query__getFieldsByFactory__parameter__isExportOnly": "", "@sage/xtrem-import-export/nodes__import_export_template__query__getFieldsByFactory__parameter__level": "", "@sage/xtrem-import-export/nodes__import_export_template__query__getFieldsByFactory__parameter__withSortValue": "", "@sage/xtrem-import-export/nodes__import_export_template__query__getNodeList": "", "@sage/xtrem-import-export/nodes__import_export_template__query__getNodeList__failed": "", "@sage/xtrem-import-export/nodes__import_export_template__query__getNodeList__parameter__filter": "", "@sage/xtrem-import-export/nodes__import_export_template__query__getTechnicalInformation": "", "@sage/xtrem-import-export/nodes__import_export_template__query__getTechnicalInformation__failed": "", "@sage/xtrem-import-export/nodes__import_export_template__query__getTechnicalInformation__parameter__isExportOnly": "", "@sage/xtrem-import-export/nodes__import_export_template__query__getTechnicalInformation__parameter__level": "", "@sage/xtrem-import-export/nodes__import_export_template__query__getTechnicalInformation__parameter__parentProperty": "", "@sage/xtrem-import-export/nodes__import_export_template__query__getTechnicalInformation__parameter__property": "", "@sage/xtrem-import-export/nodes__import_export_template__query__getUserImportExportPreferences": "", "@sage/xtrem-import-export/nodes__import_export_template__query__getUserImportExportPreferences__failed": "", "@sage/xtrem-import-export/nodes__import_result__asyncMutation__asyncExport": "", "@sage/xtrem-import-export/nodes__import_result__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-import-export/nodes__import_result__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-import-export/nodes__import_result__node_name": "Import result", "@sage/xtrem-import-export/nodes__import_result__property__doInsert": "Insert", "@sage/xtrem-import-export/nodes__import_result__property__doUpdate": "Update", "@sage/xtrem-import-export/nodes__import_result__property__dryRun": "Dry run", "@sage/xtrem-import-export/nodes__import_result__property__endStamp": "End stamp", "@sage/xtrem-import-export/nodes__import_result__property__filename": "File name", "@sage/xtrem-import-export/nodes__import_result__property__generalError": "General error", "@sage/xtrem-import-export/nodes__import_result__property__importExportTemplate": "", "@sage/xtrem-import-export/nodes__import_result__property__maxErrorCount": "Maximum number of import errors allowed", "@sage/xtrem-import-export/nodes__import_result__property__notificationId": "", "@sage/xtrem-import-export/nodes__import_result__property__numberOfRowsInError": "Number of rows in error", "@sage/xtrem-import-export/nodes__import_result__property__parameters": "", "@sage/xtrem-import-export/nodes__import_result__property__rowsInError": "Rows with errors", "@sage/xtrem-import-export/nodes__import_result__property__rowsProcessed": "Rows processed", "@sage/xtrem-import-export/nodes__import_result__property__startStamp": "Start stamp", "@sage/xtrem-import-export/nodes__import_result__property__status": "Status", "@sage/xtrem-import-export/nodes__import_result__property__uploadedFile": "Uploaded file", "@sage/xtrem-import-export/nodes__import_result__property__uploadRejectReason": "Upload reject reason", "@sage/xtrem-import-export/package__name": "Import", "@sage/xtrem-import-export/page__import_export_template__cannot_move_further": "", "@sage/xtrem-import-export/page__import_export_template__cannot_move_group_main_key": "", "@sage/xtrem-import-export/page__import_export_template__cannot_move_outside_parent_group": "", "@sage/xtrem-import-export/page__import_export_template__confirm_deletion": "", "@sage/xtrem-import-export/page__import_export_template__confirm_deletion_of_sublevel": "", "@sage/xtrem-import-export/page__import_export_template__line_values_not_found": "", "@sage/xtrem-import-export/page__import_export_template__moving_line_up_down": "", "@sage/xtrem-import-export/pages__export_bulk_mutation_configuration____title": "", "@sage/xtrem-import-export/pages__export_bulk_mutation_configuration__id____columns__title__description": "", "@sage/xtrem-import-export/pages__export_bulk_mutation_configuration__id____columns__title__id": "", "@sage/xtrem-import-export/pages__export_bulk_mutation_configuration__id____columns__title__name": "", "@sage/xtrem-import-export/pages__export_bulk_mutation_configuration__id____title": "", "@sage/xtrem-import-export/pages__import_data____title": "", "@sage/xtrem-import-export/pages__import_data__continueAfterErrorOption____title": "", "@sage/xtrem-import-export/pages__import_data__csvFile____title": "", "@sage/xtrem-import-export/pages__import_data__downloadSection____title": "", "@sage/xtrem-import-export/pages__import_data__executeImport____title": "", "@sage/xtrem-import-export/pages__import_data__fileBlock____title": "", "@sage/xtrem-import-export/pages__import_data__fileField____title": "", "@sage/xtrem-import-export/pages__import_data__generalSection____title": "", "@sage/xtrem-import-export/pages__import_data__importData____columns__title__filename": "", "@sage/xtrem-import-export/pages__import_data__importData____columns__title__nodeName": "", "@sage/xtrem-import-export/pages__import_data__importData____columns__title__template": "", "@sage/xtrem-import-export/pages__import_data__importData____columns__title__templateDescription": "", "@sage/xtrem-import-export/pages__import_data__importData____columns__title__templateName": "", "@sage/xtrem-import-export/pages__import_data__importData____title": "", "@sage/xtrem-import-export/pages__import_data__importResults____columns__title__dryRun": "", "@sage/xtrem-import-export/pages__import_data__importResults____columns__title__endStamp": "", "@sage/xtrem-import-export/pages__import_data__importResults____columns__title__filename": "", "@sage/xtrem-import-export/pages__import_data__importResults____columns__title__generalError": "", "@sage/xtrem-import-export/pages__import_data__importResults____columns__title__numberOfRowsInError": "", "@sage/xtrem-import-export/pages__import_data__importResults____columns__title__rowsProcessed": "", "@sage/xtrem-import-export/pages__import_data__importResults____columns__title__startStamp": "", "@sage/xtrem-import-export/pages__import_data__importResults____columns__title__status": "", "@sage/xtrem-import-export/pages__import_data__importResults____dropdownActions__title": "", "@sage/xtrem-import-export/pages__import_data__importResults____title": "", "@sage/xtrem-import-export/pages__import_data__insertOption____title": "", "@sage/xtrem-import-export/pages__import_data__maximumNumberOfErrors____title": "", "@sage/xtrem-import-export/pages__import_data__optionsBlock____title": "", "@sage/xtrem-import-export/pages__import_data__refreshRelationMapping____title": "", "@sage/xtrem-import-export/pages__import_data__resultsSection____title": "", "@sage/xtrem-import-export/pages__import_data__test____title": "", "@sage/xtrem-import-export/pages__import_data__updateOption____title": "", "@sage/xtrem-import-export/pages__import_export_template____navigationPanel__listItem__line3__title": "", "@sage/xtrem-import-export/pages__import_export_template____navigationPanel__listItem__name__title": "", "@sage/xtrem-import-export/pages__import_export_template____navigationPanel__listItem__title__title": "", "@sage/xtrem-import-export/pages__import_export_template____navigationPanel__listItem__titleRight__title": "", "@sage/xtrem-import-export/pages__import_export_template____navigationPanel__optionsMenu__title": "", "@sage/xtrem-import-export/pages__import_export_template____navigationPanel__optionsMenu__title__2": "", "@sage/xtrem-import-export/pages__import_export_template____navigationPanel__optionsMenu__title__3": "", "@sage/xtrem-import-export/pages__import_export_template____navigationPanel__optionsMenu__title__4": "", "@sage/xtrem-import-export/pages__import_export_template____objectTypePlural": "", "@sage/xtrem-import-export/pages__import_export_template____objectTypeSingular": "", "@sage/xtrem-import-export/pages__import_export_template____title": "", "@sage/xtrem-import-export/pages__import_export_template___id____title": "", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__columns__currentProperty__name__title": "", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__columns__currentProperty__name__title__2": "", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__columns__parentProperty__name__title": "", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__columns__parentProperty__name__title__2": "", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__title__currentProperty__factory__name": "", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__title__currentProperty__name": "", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__title__currentProperty__targetFactory__name": "", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__title__dataType": "", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__title__description": "", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__title__isCustom": "", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__title__isExportOnly": "", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__title__isMandatory": "", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__title__locale": "", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__title__parentProperty__factory__name": "", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__title__parentProperty__name": "", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__title__parentProperty__targetFactory__name": "", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__title__path": "", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____dropdownActions__title": "", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____inlineActions__title": "", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____inlineActions__title__2": "", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____inlineActions__title__3": "", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____title": "", "@sage/xtrem-import-export/pages__import_export_template__columnsTableBlock____title": "", "@sage/xtrem-import-export/pages__import_export_template__csvTemplateDisplay____helperText": "", "@sage/xtrem-import-export/pages__import_export_template__csvTemplateDisplay____title": "", "@sage/xtrem-import-export/pages__import_export_template__customSave____title": "", "@sage/xtrem-import-export/pages__import_export_template__decimalPoint____title": "", "@sage/xtrem-import-export/pages__import_export_template__defaultParametersBlock____title": "", "@sage/xtrem-import-export/pages__import_export_template__delimiter____title": "", "@sage/xtrem-import-export/pages__import_export_template__description____title": "", "@sage/xtrem-import-export/pages__import_export_template__downloadSection____title": "", "@sage/xtrem-import-export/pages__import_export_template__downloadTemplate____title": "", "@sage/xtrem-import-export/pages__import_export_template__downloadTemplateSection____title": "", "@sage/xtrem-import-export/pages__import_export_template__escapeCharacter____title": "", "@sage/xtrem-import-export/pages__import_export_template__exportData____title": "", "@sage/xtrem-import-export/pages__import_export_template__exportTemplatePreviewBlock____title": "", "@sage/xtrem-import-export/pages__import_export_template__fileField____title": "", "@sage/xtrem-import-export/pages__import_export_template__generalSection____title": "", "@sage/xtrem-import-export/pages__import_export_template__generateTemplate____title": "", "@sage/xtrem-import-export/pages__import_export_template__hasDataTypeLine____title": "", "@sage/xtrem-import-export/pages__import_export_template__hasDescriptionLine____title": "", "@sage/xtrem-import-export/pages__import_export_template__hasLocaleLine____title": "", "@sage/xtrem-import-export/pages__import_export_template__historySection____title": "", "@sage/xtrem-import-export/pages__import_export_template__id____title": "", "@sage/xtrem-import-export/pages__import_export_template__importData____title": "", "@sage/xtrem-import-export/pages__import_export_template__importResults____columns__title__dryRun": "", "@sage/xtrem-import-export/pages__import_export_template__importResults____columns__title__endStamp": "", "@sage/xtrem-import-export/pages__import_export_template__importResults____columns__title__filename": "", "@sage/xtrem-import-export/pages__import_export_template__importResults____columns__title__numberOfRowsInError": "", "@sage/xtrem-import-export/pages__import_export_template__importResults____columns__title__rowsProcessed": "", "@sage/xtrem-import-export/pages__import_export_template__importResults____columns__title__startStamp": "", "@sage/xtrem-import-export/pages__import_export_template__importResults____columns__title__status": "", "@sage/xtrem-import-export/pages__import_export_template__importResults____dropdownActions__title": "", "@sage/xtrem-import-export/pages__import_export_template__importResults____title": "", "@sage/xtrem-import-export/pages__import_export_template__isActive____title": "", "@sage/xtrem-import-export/pages__import_export_template__isDefault____title": "", "@sage/xtrem-import-export/pages__import_export_template__name____title": "", "@sage/xtrem-import-export/pages__import_export_template__nodeName____title": "", "@sage/xtrem-import-export/pages__import_export_template__nodeProperty____columns__title__factory__name": "", "@sage/xtrem-import-export/pages__import_export_template__nodeProperty____columns__title__targetFactory__name": "", "@sage/xtrem-import-export/pages__import_export_template__nodeProperty____title": "", "@sage/xtrem-import-export/pages__import_export_template__quoteCharacter____title": "", "@sage/xtrem-import-export/pages__import_export_template__refreshRelationMapping____title": "", "@sage/xtrem-import-export/pages__import_export_template__resetGrid____title": "", "@sage/xtrem-import-export/pages__import_export_template__templateUse____title": "", "@sage/xtrem-import-export/pages__import_template__setupId____title": "ID", "@sage/xtrem-import-export/permission__batch_import__name": "", "@sage/xtrem-import-export/permission__manage__name": "", "@sage/xtrem-import-export/permission__read__name": "", "@sage/xtrem-import-export/select-file": "Wybierz plik CSV", "@sage/xtrem-import-export/validation-error": "Błąd zatwierdzania"}