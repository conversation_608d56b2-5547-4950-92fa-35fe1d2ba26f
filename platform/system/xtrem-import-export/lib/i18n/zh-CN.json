{"@sage/xtrem-import-export/activity__import_data__name": "导入数据", "@sage/xtrem-import-export/activity__import_export_template__name": "导入导出模板", "@sage/xtrem-import-export/confirm-import": "确认导入", "@sage/xtrem-import-export/confirm-import-question": "您想要导入这个文件吗？", "@sage/xtrem-import-export/data_types__csv_stream_type__name": "CSV流类型", "@sage/xtrem-import-export/data_types__csv_template_text_stream_type__name": "CSV模板文本流类型", "@sage/xtrem-import-export/data_types__displayed_property_type_enum__name": "显示的属性类型枚举", "@sage/xtrem-import-export/data_types__file_format_enum__name": "文件格式枚举", "@sage/xtrem-import-export/data_types__html_data_type__name": "HTML数据类型", "@sage/xtrem-import-export/data_types__import_status_enum__name": "导入状态枚举", "@sage/xtrem-import-export/data_types__name_data_type__name": "名称数据类型", "@sage/xtrem-import-export/data_types__rows_text_stream_type__name": "行文本流类型", "@sage/xtrem-import-export/data_types__short_string_data_type__name": "短字符串数据类型", "@sage/xtrem-import-export/data_types__template_use_enum__name": "模板使用枚举", "@sage/xtrem-import-export/data_types__test_enum_enum__name": "测试枚举枚举", "@sage/xtrem-import-export/default_parameters-decimal_point-invalid_value": "小数点无效：{{value}}。", "@sage/xtrem-import-export/default_parameters-delimiter-invalid_value": "分隔符无效：{{value}}。", "@sage/xtrem-import-export/edit-create-line": "插入模板列", "@sage/xtrem-import-export/enums__displayed_property_type__collection": "收集", "@sage/xtrem-import-export/enums__displayed_property_type__mainKey": "主密钥", "@sage/xtrem-import-export/enums__displayed_property_type__normalProperty": "普通属性", "@sage/xtrem-import-export/enums__displayed_property_type__reference": "参考", "@sage/xtrem-import-export/enums__displayed_property_type__vitalCollection": "重要收集", "@sage/xtrem-import-export/enums__displayed_property_type__vitalReference": "重要参考", "@sage/xtrem-import-export/enums__file_format__csv": "CSV", "@sage/xtrem-import-export/enums__file_format__xlsx": "XLS", "@sage/xtrem-import-export/enums__import_status__failed": "失败", "@sage/xtrem-import-export/enums__import_status__finished": "已完成", "@sage/xtrem-import-export/enums__import_status__inProgress": "处理中", "@sage/xtrem-import-export/enums__import_status__pending": "待处理", "@sage/xtrem-import-export/enums__import_status__rejected": "已拒绝", "@sage/xtrem-import-export/enums__template_use__exportOnly": "仅导出", "@sage/xtrem-import-export/enums__template_use__importAndExport": "导入和导出", "@sage/xtrem-import-export/enums__template_use__importOnly": "仅导入", "@sage/xtrem-import-export/enums__test_enum__value1": "值1", "@sage/xtrem-import-export/enums__test_enum__value2": "值2", "@sage/xtrem-import-export/enums__test_enum__value3": "值3", "@sage/xtrem-import-export/function__export_send_client_notification_description": "导出的记录{{numberOfRecords}}。", "@sage/xtrem-import-export/function__export_send_client_notification_title_fail": "使用该模板的导出结束时出错：{{templateId}}。", "@sage/xtrem-import-export/function__export_send_client_notification_title_success": "使用该模板导出的结束成功：{{templateId}}。", "@sage/xtrem-import-export/functions__csv_to_json__header_disabled_by_a_service_option": "服务选项禁用了表头{{csvHeader}}", "@sage/xtrem-import-export/functions__csv_to_json__header_is_an_encrypted_string": "该表头是一个加密的字符串：{{csvHeader}}。", "@sage/xtrem-import-export/functions__csv_to_json__invalid_custom_field_column_header": "列表头中的无效自定义字段：{{csvHeader}}。", "@sage/xtrem-import-export/functions__csv_to_json__missing_property_header": "没有在主工厂{{factoryName}}中找到属性{{propertyName}}。", "@sage/xtrem-import-export/invalid-reference-header-invalid-natural-key": "{{name}}表头键属性无效。请更改键为{{key}}。", "@sage/xtrem-import-export/invalid-reference-header-no-natural-key": "导入模板中无法包含{{name}}表头。目标节点{{target}}需要有一个自然键索引。", "@sage/xtrem-import-export/invalid-reference-header-unexpected-index-on-natural-key": "{{name}}列标题无效。删除属性名称后的“（{{columns}}）”。", "@sage/xtrem-import-export/menu_item__import": "导入", "@sage/xtrem-import-export/menu_item__import-data": "导入", "@sage/xtrem-import-export/menu_item__import-export": "导入和导出", "@sage/xtrem-import-export/missing-natural-key-header": "{{node}}节点的自然键缺少模板中未包含的属性。请添加缺失属性：{{missing}}。", "@sage/xtrem-import-export/no-file-to-import": "没有待导入的文件。", "@sage/xtrem-import-export/no-template-selected": "为每个待导入的文件选择一个现有的模板。", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__asyncExport": "导出", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__asyncExport__parameter__filter": "筛选", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__batchImport": "批次导入", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__batchImport__failed": "批量导入失败。", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__batchImport__parameter__options": "选项", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__batchImport__parameter__templateId": "模板ID", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__batchImport__parameter__uploadedFileId": "已上传文件ID", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__exportByTemplateDefinition": "按模板定义导出", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__exportByTemplateDefinition__failed": "按模板定义导出失败。", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__exportByTemplateDefinition__parameter__filter": "筛选", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__exportByTemplateDefinition__parameter__nodeName": "节点名称", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__exportByTemplateDefinition__parameter__orderBy": "排序方式", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__exportByTemplateDefinition__parameter__outputFormat": "输出格式", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__exportByTemplateDefinition__parameter__templateDefinition": "模板定义", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__exportByTemplateId": "按模板ID导出", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__exportByTemplateId__failed": "按模板ID导出失败。", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__exportByTemplateId__parameter__filter": "筛选", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__exportByTemplateId__parameter__outputFormat": "输出格式", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__exportByTemplateId__parameter__templateId": "模板ID", "@sage/xtrem-import-export/nodes__import_export_template__node_name": "导入导出模板", "@sage/xtrem-import-export/nodes__import_export_template__property__canImport": "可导入", "@sage/xtrem-import-export/nodes__import_export_template__property__csvTemplate": "CSV模板", "@sage/xtrem-import-export/nodes__import_export_template__property__defaultParameters": "默认参数", "@sage/xtrem-import-export/nodes__import_export_template__property__description": "描述", "@sage/xtrem-import-export/nodes__import_export_template__property__id": "ID", "@sage/xtrem-import-export/nodes__import_export_template__property__isActive": "是激活的", "@sage/xtrem-import-export/nodes__import_export_template__property__isDefault": "是默认的", "@sage/xtrem-import-export/nodes__import_export_template__property__name": "名称", "@sage/xtrem-import-export/nodes__import_export_template__property__nodeName": "节点名称", "@sage/xtrem-import-export/nodes__import_export_template__property__templateUse": "模板使用", "@sage/xtrem-import-export/nodes__import_export_template__query__getDefaultCsvTemplate": "获取默认的CSV模板", "@sage/xtrem-import-export/nodes__import_export_template__query__getDefaultCsvTemplate__failed": "获取默认的CSV模板失败。", "@sage/xtrem-import-export/nodes__import_export_template__query__getDefaultCsvTemplate__parameter__isExportWithDefinition": "导出时是否包含定义", "@sage/xtrem-import-export/nodes__import_export_template__query__getDefaultCsvTemplate__parameter__level": "层级", "@sage/xtrem-import-export/nodes__import_export_template__query__getDefaultCsvTemplate__parameter__nodeName": "节点名称", "@sage/xtrem-import-export/nodes__import_export_template__query__getDefaultCsvTemplate__parameter__withSortValue": "带有排序值", "@sage/xtrem-import-export/nodes__import_export_template__query__getDisplayedPropertyTypeFromName": "从名称中获取已显示属性类型", "@sage/xtrem-import-export/nodes__import_export_template__query__getDisplayedPropertyTypeFromName__parameter__factoryName": "工厂名称", "@sage/xtrem-import-export/nodes__import_export_template__query__getDisplayedPropertyTypeFromName__parameter__level": "层级", "@sage/xtrem-import-export/nodes__import_export_template__query__getDisplayedPropertyTypeFromName__parameter__propertyName": "属性名称", "@sage/xtrem-import-export/nodes__import_export_template__query__getFieldsByFactory": "按工厂获取字段", "@sage/xtrem-import-export/nodes__import_export_template__query__getFieldsByFactory__failed": "按工厂获取字段失败。", "@sage/xtrem-import-export/nodes__import_export_template__query__getFieldsByFactory__parameter__factoryName": "工厂名称", "@sage/xtrem-import-export/nodes__import_export_template__query__getFieldsByFactory__parameter__isExportOnly": "仅导出吗？", "@sage/xtrem-import-export/nodes__import_export_template__query__getFieldsByFactory__parameter__level": "层级", "@sage/xtrem-import-export/nodes__import_export_template__query__getFieldsByFactory__parameter__withSortValue": "带有排序值", "@sage/xtrem-import-export/nodes__import_export_template__query__getNodeList": "获取节点列表", "@sage/xtrem-import-export/nodes__import_export_template__query__getNodeList__failed": "获取节点列表失败。", "@sage/xtrem-import-export/nodes__import_export_template__query__getNodeList__parameter__filter": "筛选", "@sage/xtrem-import-export/nodes__import_export_template__query__getPropertyDataTypeFromName": "从名称中获取属性数据类型", "@sage/xtrem-import-export/nodes__import_export_template__query__getPropertyDataTypeFromName__parameter__factoryName": "工厂名称", "@sage/xtrem-import-export/nodes__import_export_template__query__getPropertyDataTypeFromName__parameter__propertyName": "属性名称", "@sage/xtrem-import-export/nodes__import_export_template__query__getPropertyDescriptionFromName": "从名称中获取属性描述", "@sage/xtrem-import-export/nodes__import_export_template__query__getPropertyDescriptionFromName__parameter__factoryName": "工厂名称", "@sage/xtrem-import-export/nodes__import_export_template__query__getPropertyDescriptionFromName__parameter__propertyName": "属性名称", "@sage/xtrem-import-export/nodes__import_export_template__query__getPropertyLocaleFromName": "从名称中获取属性区域设置", "@sage/xtrem-import-export/nodes__import_export_template__query__getPropertyLocaleFromName__parameter__factoryName": "工厂名称", "@sage/xtrem-import-export/nodes__import_export_template__query__getPropertyLocaleFromName__parameter__propertyName": "属性名称", "@sage/xtrem-import-export/nodes__import_export_template__query__getPropertyPathFromName": "从名称中获取属性路径", "@sage/xtrem-import-export/nodes__import_export_template__query__getPropertyPathFromName__parameter__factoryName": "工厂名称", "@sage/xtrem-import-export/nodes__import_export_template__query__getPropertyPathFromName__parameter__level": "层级", "@sage/xtrem-import-export/nodes__import_export_template__query__getPropertyPathFromName__parameter__parentNonVitalReferencePath": "父件非重要参考路径", "@sage/xtrem-import-export/nodes__import_export_template__query__getPropertyPathFromName__parameter__propertyName": "属性名称", "@sage/xtrem-import-export/nodes__import_export_template__query__getTechnicalInformation": "获取技术信息", "@sage/xtrem-import-export/nodes__import_export_template__query__getTechnicalInformation__failed": "获取技术信息失败。", "@sage/xtrem-import-export/nodes__import_export_template__query__getTechnicalInformation__parameter__isExportOnly": "仅导出吗？", "@sage/xtrem-import-export/nodes__import_export_template__query__getTechnicalInformation__parameter__level": "层级", "@sage/xtrem-import-export/nodes__import_export_template__query__getTechnicalInformation__parameter__parentProperty": "主属性", "@sage/xtrem-import-export/nodes__import_export_template__query__getTechnicalInformation__parameter__property": "属性", "@sage/xtrem-import-export/nodes__import_export_template__query__getUserImportExportPreferences": "获取用户导入导出首选项", "@sage/xtrem-import-export/nodes__import_export_template__query__getUserImportExportPreferences__failed": "获取用户导入导出首选项失败。", "@sage/xtrem-import-export/nodes__import_result__asyncMutation__asyncExport": "导出", "@sage/xtrem-import-export/nodes__import_result__asyncMutation__asyncExport__parameter__filter": "筛选", "@sage/xtrem-import-export/nodes__import_result__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-import-export/nodes__import_result__node_name": "导入结果", "@sage/xtrem-import-export/nodes__import_result__property__doInsert": "插入", "@sage/xtrem-import-export/nodes__import_result__property__doUpdate": "更新", "@sage/xtrem-import-export/nodes__import_result__property__dryRun": "试运行", "@sage/xtrem-import-export/nodes__import_result__property__endStamp": "结束标记", "@sage/xtrem-import-export/nodes__import_result__property__filename": "文件名称", "@sage/xtrem-import-export/nodes__import_result__property__generalError": "一般错误", "@sage/xtrem-import-export/nodes__import_result__property__importExportTemplate": "导入导出模板", "@sage/xtrem-import-export/nodes__import_result__property__maxErrorCount": "允许导入错误的最大数", "@sage/xtrem-import-export/nodes__import_result__property__notificationId": "通知ID", "@sage/xtrem-import-export/nodes__import_result__property__numberOfRowsInError": "错误的行数", "@sage/xtrem-import-export/nodes__import_result__property__parameters": "参数", "@sage/xtrem-import-export/nodes__import_result__property__rowsInError": "有错误的行", "@sage/xtrem-import-export/nodes__import_result__property__rowsProcessed": "已处理的行", "@sage/xtrem-import-export/nodes__import_result__property__startStamp": "开始标记", "@sage/xtrem-import-export/nodes__import_result__property__status": "状态", "@sage/xtrem-import-export/nodes__import_result__property__uploadedFile": "已上传文件", "@sage/xtrem-import-export/nodes__import_result__property__uploadRejectReason": "上传拒绝原因", "@sage/xtrem-import-export/nodes__import_template__property__setupId": "设置ID", "@sage/xtrem-import-export/nodes__parsed_result_bucket__node_name": "解析结果时段", "@sage/xtrem-import-export/package__name": "导入", "@sage/xtrem-import-export/page__import_export_template__cannot_move_further": "您不能再移动该行。", "@sage/xtrem-import-export/page__import_export_template__cannot_move_group_main_key": "当前行是该组的主密钥。您不能移动它。", "@sage/xtrem-import-export/page__import_export_template__cannot_move_outside_parent_group": "您需要将该行保留在其父组内。", "@sage/xtrem-import-export/page__import_export_template__confirm_deletion": "确认删除", "@sage/xtrem-import-export/page__import_export_template__confirm_deletion_of_sublevel": "您将删除子层级及其所有的条目。继续吗？", "@sage/xtrem-import-export/page__import_export_template__line_values_not_found": "未找到行值。", "@sage/xtrem-import-export/page__import_export_template__moving_line_up_down": "向上或向下移动行", "@sage/xtrem-import-export/pages__export_bulk_mutation_configuration____title": "选择导出模板", "@sage/xtrem-import-export/pages__export_bulk_mutation_configuration__id____columns__title__description": "描述", "@sage/xtrem-import-export/pages__export_bulk_mutation_configuration__id____columns__title__id": "ID", "@sage/xtrem-import-export/pages__export_bulk_mutation_configuration__id____columns__title__name": "模板名称", "@sage/xtrem-import-export/pages__export_bulk_mutation_configuration__id____title": "导出模板", "@sage/xtrem-import-export/pages__import_data____title": "数据导入", "@sage/xtrem-import-export/pages__import_data__continueAfterErrorOption____title": "继续并忽略该错误", "@sage/xtrem-import-export/pages__import_data__csvFile____title": "选择文件", "@sage/xtrem-import-export/pages__import_data__downloadSection____title": "下载", "@sage/xtrem-import-export/pages__import_data__executeImport____title": "导入", "@sage/xtrem-import-export/pages__import_data__fileBlock____title": "CSV文件", "@sage/xtrem-import-export/pages__import_data__fileField____title": "导入结果", "@sage/xtrem-import-export/pages__import_data__generalSection____title": "数据", "@sage/xtrem-import-export/pages__import_data__importAction____title": "导入", "@sage/xtrem-import-export/pages__import_data__importData____columns__title__filename": "文件名称", "@sage/xtrem-import-export/pages__import_data__importData____columns__title__nodeName": "节点名称", "@sage/xtrem-import-export/pages__import_data__importData____columns__title__template": "模板", "@sage/xtrem-import-export/pages__import_data__importData____columns__title__templateDescription": "模板描述", "@sage/xtrem-import-export/pages__import_data__importData____columns__title__templateName": "模板名称", "@sage/xtrem-import-export/pages__import_data__importData____title": "待导入的文件", "@sage/xtrem-import-export/pages__import_data__importResults____columns__title__dryRun": "测试", "@sage/xtrem-import-export/pages__import_data__importResults____columns__title__endStamp": "结束时间", "@sage/xtrem-import-export/pages__import_data__importResults____columns__title__filename": "文件名称", "@sage/xtrem-import-export/pages__import_data__importResults____columns__title__generalError": "错误明细", "@sage/xtrem-import-export/pages__import_data__importResults____columns__title__numberOfRowsInError": "错误数量", "@sage/xtrem-import-export/pages__import_data__importResults____columns__title__rowsProcessed": "已处理的行", "@sage/xtrem-import-export/pages__import_data__importResults____columns__title__startStamp": "开始时间", "@sage/xtrem-import-export/pages__import_data__importResults____columns__title__status": "状态", "@sage/xtrem-import-export/pages__import_data__importResults____dropdownActions__title": "下载", "@sage/xtrem-import-export/pages__import_data__importResults____title": "结果", "@sage/xtrem-import-export/pages__import_data__insertOption____title": "插入", "@sage/xtrem-import-export/pages__import_data__maximumNumberOfErrors____title": "最大错误数", "@sage/xtrem-import-export/pages__import_data__optionsBlock____title": "选项", "@sage/xtrem-import-export/pages__import_data__refreshRelationMapping____title": "刷新", "@sage/xtrem-import-export/pages__import_data__resultsSection____title": "导入结果", "@sage/xtrem-import-export/pages__import_data__test____title": "测试导入", "@sage/xtrem-import-export/pages__import_data__updateOption____title": "更新", "@sage/xtrem-import-export/pages__import_data_page__testImportAction____title": "测试", "@sage/xtrem-import-export/pages__import_export_template____navigationPanel__listItem__idLine__title": "ID", "@sage/xtrem-import-export/pages__import_export_template____navigationPanel__listItem__line3__title": "描述", "@sage/xtrem-import-export/pages__import_export_template____navigationPanel__listItem__Line4__title": "描述", "@sage/xtrem-import-export/pages__import_export_template____navigationPanel__listItem__name__title": "名称", "@sage/xtrem-import-export/pages__import_export_template____navigationPanel__listItem__title__title": "ID", "@sage/xtrem-import-export/pages__import_export_template____navigationPanel__listItem__titleLeft__title": "描述", "@sage/xtrem-import-export/pages__import_export_template____navigationPanel__listItem__titleRight__title": "类型", "@sage/xtrem-import-export/pages__import_export_template____navigationPanel__optionsMenu__title": "全部", "@sage/xtrem-import-export/pages__import_export_template____navigationPanel__optionsMenu__title__2": "导入导出模板", "@sage/xtrem-import-export/pages__import_export_template____navigationPanel__optionsMenu__title__3": "导入模板", "@sage/xtrem-import-export/pages__import_export_template____navigationPanel__optionsMenu__title__4": "导出模板", "@sage/xtrem-import-export/pages__import_export_template____objectTypePlural": "导入导出模板", "@sage/xtrem-import-export/pages__import_export_template____objectTypeSingular": "导入导出模板", "@sage/xtrem-import-export/pages__import_export_template____title": "导入导出模板", "@sage/xtrem-import-export/pages__import_export_template___id____title": "ID", "@sage/xtrem-import-export/pages__import_export_template__canImport____title": "导入模板", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__columns__currentProperty__name__title": "工厂", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__columns__currentProperty__name__title__2": "目标工厂", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__columns__parentProperty__name__title": "主属性工厂", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__columns__parentProperty__name__title__2": "主属性目标工厂", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__title__currentProperty__factory__name": "工厂", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__title__currentProperty__name": "字段", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__title__currentProperty__targetFactory__name": "目标工厂", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__title__dataType": "数据类型", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__title__description": "描述", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__title__factory__name": "工厂", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__title__isCustom": "自定义", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__title__isExportOnly": "仅导出", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__title__isMandatory": "必填", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__title__locale": "区域设置", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__title__parentProperty__factory__name": "主工厂", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__title__parentProperty__name": "主属性", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__title__parentProperty__targetFactory__name": "主目标工厂", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__title__path": "路径", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__title__targetFactory__name": "目标工厂", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____dropdownActions__title": "删除", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____inlineActions__title": "上移", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____inlineActions__title__2": "下移", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____inlineActions__title__3": "插入", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____title": "明细", "@sage/xtrem-import-export/pages__import_export_template__columnsTableBlock____title": "模板列", "@sage/xtrem-import-export/pages__import_export_template__copyToClipboard____title": "复制到剪贴板", "@sage/xtrem-import-export/pages__import_export_template__csvTemplateDisplay____helperText": "模板源代码", "@sage/xtrem-import-export/pages__import_export_template__csvTemplateDisplay____title": "CSV模板", "@sage/xtrem-import-export/pages__import_export_template__customSave____title": "保存", "@sage/xtrem-import-export/pages__import_export_template__dateFormat____title": "日期格式", "@sage/xtrem-import-export/pages__import_export_template__decimalPoint____title": "小数点", "@sage/xtrem-import-export/pages__import_export_template__defaultParametersBlock____title": "格式化选项", "@sage/xtrem-import-export/pages__import_export_template__delimiter____title": "分隔符", "@sage/xtrem-import-export/pages__import_export_template__description____title": "描述", "@sage/xtrem-import-export/pages__import_export_template__downloadSection____title": "下载", "@sage/xtrem-import-export/pages__import_export_template__downloadTemplate____title": "模板", "@sage/xtrem-import-export/pages__import_export_template__downloadTemplateSection____title": "下载", "@sage/xtrem-import-export/pages__import_export_template__escapeCharacter____title": "转义字符", "@sage/xtrem-import-export/pages__import_export_template__exportData____title": "导出数据", "@sage/xtrem-import-export/pages__import_export_template__exportSettingsBlock____title": "导出设置", "@sage/xtrem-import-export/pages__import_export_template__exportTemplatePreviewBlock____title": "导出模板", "@sage/xtrem-import-export/pages__import_export_template__fileField____title": "导入结果", "@sage/xtrem-import-export/pages__import_export_template__fillGridFromClipboard____title": "将剪贴板复制到表格", "@sage/xtrem-import-export/pages__import_export_template__generalSection____title": "常规", "@sage/xtrem-import-export/pages__import_export_template__generateTemplate____title": "生成模板", "@sage/xtrem-import-export/pages__import_export_template__hasDataTypeLine____title": "数据类型", "@sage/xtrem-import-export/pages__import_export_template__hasDescriptionLine____title": "描述", "@sage/xtrem-import-export/pages__import_export_template__hasLocaleLine____title": "区域设置", "@sage/xtrem-import-export/pages__import_export_template__historySection____title": "导入历史", "@sage/xtrem-import-export/pages__import_export_template__id____title": "ID", "@sage/xtrem-import-export/pages__import_export_template__importData____title": "导入数据", "@sage/xtrem-import-export/pages__import_export_template__importResults____columns__title__dryRun": "测试", "@sage/xtrem-import-export/pages__import_export_template__importResults____columns__title__endStamp": "结束标记", "@sage/xtrem-import-export/pages__import_export_template__importResults____columns__title__filename": "文件名称", "@sage/xtrem-import-export/pages__import_export_template__importResults____columns__title__numberOfRowsInError": "有错误的行", "@sage/xtrem-import-export/pages__import_export_template__importResults____columns__title__rowsProcessed": "已处理的行", "@sage/xtrem-import-export/pages__import_export_template__importResults____columns__title__startStamp": "开始标记", "@sage/xtrem-import-export/pages__import_export_template__importResults____columns__title__status": "状态", "@sage/xtrem-import-export/pages__import_export_template__importResults____dropdownActions__title": "下载", "@sage/xtrem-import-export/pages__import_export_template__importResults____title": "结果", "@sage/xtrem-import-export/pages__import_export_template__importSettingsBlock____title": "导入设置", "@sage/xtrem-import-export/pages__import_export_template__isActive____title": "激活的", "@sage/xtrem-import-export/pages__import_export_template__isDefault____title": "默认导出模板", "@sage/xtrem-import-export/pages__import_export_template__name____title": "名称", "@sage/xtrem-import-export/pages__import_export_template__nodeName____title": "节点名称", "@sage/xtrem-import-export/pages__import_export_template__nodeProperty____columns__title__factory__name": "工厂", "@sage/xtrem-import-export/pages__import_export_template__nodeProperty____columns__title__targetFactory__name": "目标工厂", "@sage/xtrem-import-export/pages__import_export_template__nodeProperty____title": "选择待添加的字段", "@sage/xtrem-import-export/pages__import_export_template__omitDataType____title": "省略数据类型", "@sage/xtrem-import-export/pages__import_export_template__omitDescription____title": "省略描述", "@sage/xtrem-import-export/pages__import_export_template__quoteCharacter____title": "引用字符", "@sage/xtrem-import-export/pages__import_export_template__refreshRelationMapping____title": "刷新", "@sage/xtrem-import-export/pages__import_export_template__resetGrid____title": "重新设置表格", "@sage/xtrem-import-export/pages__import_export_template__templateUse____title": "模板使用", "@sage/xtrem-import-export/pages__import_export_template_type__export": "导出", "@sage/xtrem-import-export/pages__import_export_template_type__import_and_export": "导入和导出", "@sage/xtrem-import-export/pages__import_template__setupId____title": "ID", "@sage/xtrem-import-export/permission__batch_import__name": "批次导入", "@sage/xtrem-import-export/permission__manage__name": "管理", "@sage/xtrem-import-export/permission__read__name": "读取", "@sage/xtrem-import-export/select-file": "选择一个CSV文件", "@sage/xtrem-import-export/validation-error": "审核错误"}