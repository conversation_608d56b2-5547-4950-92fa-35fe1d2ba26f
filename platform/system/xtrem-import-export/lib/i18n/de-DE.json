{"@sage/xtrem-import-export/activity__import_data__name": "Daten importieren", "@sage/xtrem-import-export/activity__import_export_template__name": "Import-/Export-Vorlage", "@sage/xtrem-import-export/confirm-import": "Import bestätigen", "@sage/xtrem-import-export/confirm-import-question": "Soll die Datei importiert werden?", "@sage/xtrem-import-export/data_types__csv_stream_type__name": "CSV-Stream-Typ", "@sage/xtrem-import-export/data_types__csv_template_text_stream_type__name": "Stream-Typ CSV-Vorlagentext", "@sage/xtrem-import-export/data_types__displayed_property_type_enum__name": "Enum angezeigter Eigenschaftentyp", "@sage/xtrem-import-export/data_types__file_format_enum__name": "Enum Dateiformat", "@sage/xtrem-import-export/data_types__html_data_type__name": "Datentyp HTML", "@sage/xtrem-import-export/data_types__import_status_enum__name": "Enum Importstatus", "@sage/xtrem-import-export/data_types__name_data_type__name": "Datentyp Name", "@sage/xtrem-import-export/data_types__rows_text_stream_type__name": "Streamtyp Text Zeilen", "@sage/xtrem-import-export/data_types__short_string_data_type__name": "Datentyp kurze Zeichenkette", "@sage/xtrem-import-export/data_types__template_use_enum__name": "Enum Verwendung Vorlage", "@sage/xtrem-import-export/data_types__test_enum_enum__name": "Enum Test-Enum", "@sage/xtrem-import-export/default_parameters-decimal_point-invalid_value": "Das Dezimaltrennzeichen {{value}} ist nicht gültig.", "@sage/xtrem-import-export/default_parameters-delimiter-invalid_value": "Das Trennzeichen {{value}} ist nicht gültig.", "@sage/xtrem-import-export/edit-create-line": "Vorlagenspalte einfügen", "@sage/xtrem-import-export/enums__displayed_property_type__collection": "Collection", "@sage/xtrem-import-export/enums__displayed_property_type__mainKey": "Hauptschlüssel", "@sage/xtrem-import-export/enums__displayed_property_type__normalProperty": "Normale Eigenschaft", "@sage/xtrem-import-export/enums__displayed_property_type__reference": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-import-export/enums__displayed_property_type__vitalCollection": "Vitale Collection", "@sage/xtrem-import-export/enums__displayed_property_type__vitalReference": "<PERSON><PERSON>", "@sage/xtrem-import-export/enums__file_format__csv": "CSV", "@sage/xtrem-import-export/enums__file_format__xlsx": "XLSX", "@sage/xtrem-import-export/enums__import_status__failed": "Fehlgeschlagen", "@sage/xtrem-import-export/enums__import_status__finished": "Abgeschlossen", "@sage/xtrem-import-export/enums__import_status__inProgress": "In Bearbeitung", "@sage/xtrem-import-export/enums__import_status__pending": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-import-export/enums__import_status__rejected": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-import-export/enums__template_use__exportOnly": "Nur Export", "@sage/xtrem-import-export/enums__template_use__importAndExport": "Import und Export", "@sage/xtrem-import-export/enums__template_use__importOnly": "Nur Import", "@sage/xtrem-import-export/enums__test_enum__value1": "Wert 1", "@sage/xtrem-import-export/enums__test_enum__value2": "Wert 2", "@sage/xtrem-import-export/enums__test_enum__value3": "Wert 3", "@sage/xtrem-import-export/function__export_send_client_notification_description": "Datensätze exportier: {{numberOfRecords}}.", "@sage/xtrem-import-export/function__export_send_client_notification_title_fail": "Der Export mit dieser Vorlage wurde mit einem Fehler beendet: {{templateId}}.", "@sage/xtrem-import-export/function__export_send_client_notification_title_success": "Der Export mit dieser Vorlage wurde erfolgreich beendet: {{templateId}}.", "@sage/xtrem-import-export/functions__csv_to_json__header_disabled_by_a_service_option": "Kopfzeile {{csvHeader}} ist durch eine Dienstoption deaktiviert", "@sage/xtrem-import-export/functions__csv_to_json__header_is_an_encrypted_string": "Diese Kopfzeile ist eine verschlüsselte Zeichenfolge: {{csvHeader}}.", "@sage/xtrem-import-export/functions__csv_to_json__invalid_custom_field_column_header": "Ungültiges benutzerdefiniertes Feld in der Spaltenkopfzeile: {{csvHeader}}.", "@sage/xtrem-import-export/functions__csv_to_json__missing_property_header": "Die Eigenschaft {{propertyName}} wurde im Host-Standard {{factoryName}} nicht gefunden.", "@sage/xtrem-import-export/invalid-reference-header-no-natural-key": "Die Kofpzeile {{name}} kann nicht in der Importvorlage enthalten sein. Der Ziel-Node {{target}} muss einen natürlichen Schlüsselindex haben.", "@sage/xtrem-import-export/invalid-reference-header-unexpected-index-on-natural-key": "Die Spaltenkopfzeile {{name}} ist nicht gültig. Entfernen Sie '({{columns}})' nach dem Eigenschaftennamen.", "@sage/xtrem-import-export/menu_item__import": "Import", "@sage/xtrem-import-export/menu_item__import-data": "Import", "@sage/xtrem-import-export/menu_item__import-export": "Import und Export", "@sage/xtrem-import-export/missing-natural-key-header": "Für den natürlichen Schlüssel für den Node {{node}} fehlen Eigenschaften, die nicht in der Vorlage enthalten sind. Fügen Sie die fehlenden Eigenschaften hinzu: {{missing}}.", "@sage/xtrem-import-export/no-file-to-import": "<PERSON><PERSON> zum Importieren.", "@sage/xtrem-import-export/no-template-selected": "<PERSON>ä<PERSON><PERSON> Sie für jede zu importierende Datei eine bestehende Vorlage aus.", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__asyncExport": "Export", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__batchImport": "Batchimport", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__batchImport__failed": "Batchimport fehlgeschlagen.", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__batchImport__parameter__options": "Optionen", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__batchImport__parameter__templateId": "Vorlagen-ID", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__batchImport__parameter__uploadedFileId": "ID hochgeladene Datei", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__exportByTemplateDefinition": "Export nach Vorlagendefinition", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__exportByTemplateDefinition__failed": "Export nach Vorlagendefinition fehlgeschlagen.", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__exportByTemplateDefinition__parameter__filter": "Filter", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__exportByTemplateDefinition__parameter__nodeName": "Node-Name", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__exportByTemplateDefinition__parameter__orderBy": "<PERSON><PERSON><PERSON> von", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__exportByTemplateDefinition__parameter__outputFormat": "Ausgabeformat", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__exportByTemplateDefinition__parameter__templateDefinition": "Vorlagendefinition", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__exportByTemplateId": "Export nach Vorlagen-ID", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__exportByTemplateId__failed": "Export nach Vorlagen-ID fehlgeschlagen.", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__exportByTemplateId__parameter__filter": "Filter", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__exportByTemplateId__parameter__outputFormat": "Ausgabeformat", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__exportByTemplateId__parameter__templateId": "Vorlagen-ID", "@sage/xtrem-import-export/nodes__import_export_template__node_name": "Import-/Export-Vorlage", "@sage/xtrem-import-export/nodes__import_export_template__property__canImport": "Kann importieren", "@sage/xtrem-import-export/nodes__import_export_template__property__csvTemplate": "CSV-Vorlage", "@sage/xtrem-import-export/nodes__import_export_template__property__defaultParameters": "Standardparameter", "@sage/xtrem-import-export/nodes__import_export_template__property__description": "Bezeichnung", "@sage/xtrem-import-export/nodes__import_export_template__property__id": "ID", "@sage/xtrem-import-export/nodes__import_export_template__property__isActive": "Ist aktiv", "@sage/xtrem-import-export/nodes__import_export_template__property__isDefault": "Ist Standard", "@sage/xtrem-import-export/nodes__import_export_template__property__name": "Name", "@sage/xtrem-import-export/nodes__import_export_template__property__nodeName": "Node-Name", "@sage/xtrem-import-export/nodes__import_export_template__property__templateUse": "Verwendung Vorlage", "@sage/xtrem-import-export/nodes__import_export_template__query__getDefaultCsvTemplate": "Standard-CSV-Vorlage abrufen", "@sage/xtrem-import-export/nodes__import_export_template__query__getDefaultCsvTemplate__failed": "Standard-CSV-Vorlage abrufen fehlgeschlagen.", "@sage/xtrem-import-export/nodes__import_export_template__query__getDefaultCsvTemplate__parameter__isExportWithDefinition": "Ist Export mit Definition", "@sage/xtrem-import-export/nodes__import_export_template__query__getDefaultCsvTemplate__parameter__level": "<PERSON><PERSON><PERSON>", "@sage/xtrem-import-export/nodes__import_export_template__query__getDefaultCsvTemplate__parameter__nodeName": "Node-Name", "@sage/xtrem-import-export/nodes__import_export_template__query__getDefaultCsvTemplate__parameter__withSortValue": "<PERSON><PERSON>", "@sage/xtrem-import-export/nodes__import_export_template__query__getDisplayedPropertyTypeFromName": "Angezeigten Eigenschaftentyp aus Name abrufen", "@sage/xtrem-import-export/nodes__import_export_template__query__getDisplayedPropertyTypeFromName__parameter__factoryName": "Standardname", "@sage/xtrem-import-export/nodes__import_export_template__query__getDisplayedPropertyTypeFromName__parameter__level": "Level", "@sage/xtrem-import-export/nodes__import_export_template__query__getDisplayedPropertyTypeFromName__parameter__propertyName": "Eigenschaftenname", "@sage/xtrem-import-export/nodes__import_export_template__query__getFieldsByFactory": "Felder nach Standard abrufen", "@sage/xtrem-import-export/nodes__import_export_template__query__getFieldsByFactory__failed": "Felder nach Standard abrufen fehlgeschlagen.", "@sage/xtrem-import-export/nodes__import_export_template__query__getFieldsByFactory__parameter__factoryName": "Standardname", "@sage/xtrem-import-export/nodes__import_export_template__query__getFieldsByFactory__parameter__isExportOnly": "Ist nur Export", "@sage/xtrem-import-export/nodes__import_export_template__query__getFieldsByFactory__parameter__level": "<PERSON><PERSON><PERSON>", "@sage/xtrem-import-export/nodes__import_export_template__query__getFieldsByFactory__parameter__withSortValue": "<PERSON><PERSON>", "@sage/xtrem-import-export/nodes__import_export_template__query__getNodeList": "Nodeliste abrufen", "@sage/xtrem-import-export/nodes__import_export_template__query__getNodeList__failed": "Nodeliste abrufen fehlgeschlagen.", "@sage/xtrem-import-export/nodes__import_export_template__query__getNodeList__parameter__filter": "Filter", "@sage/xtrem-import-export/nodes__import_export_template__query__getPropertyDataTypeFromName": "Eigenschaftendatentyp aus Name abrufen", "@sage/xtrem-import-export/nodes__import_export_template__query__getPropertyDataTypeFromName__parameter__factoryName": "Standardname", "@sage/xtrem-import-export/nodes__import_export_template__query__getPropertyDataTypeFromName__parameter__propertyName": "Eigenschaftenname", "@sage/xtrem-import-export/nodes__import_export_template__query__getPropertyDescriptionFromName": "Eigenschaftenbezeichnung aus Name abrufen", "@sage/xtrem-import-export/nodes__import_export_template__query__getPropertyDescriptionFromName__parameter__factoryName": "Standardname", "@sage/xtrem-import-export/nodes__import_export_template__query__getPropertyDescriptionFromName__parameter__propertyName": "Eigenschaftenname", "@sage/xtrem-import-export/nodes__import_export_template__query__getPropertyLocaleFromName": "Eigenschaftengebietsschema aus Name abrufen", "@sage/xtrem-import-export/nodes__import_export_template__query__getPropertyLocaleFromName__parameter__factoryName": "Standardname", "@sage/xtrem-import-export/nodes__import_export_template__query__getPropertyLocaleFromName__parameter__propertyName": "Eigenschaftenname", "@sage/xtrem-import-export/nodes__import_export_template__query__getPropertyPathFromName": "Eigenschaftenpfad aus Name abrufen", "@sage/xtrem-import-export/nodes__import_export_template__query__getPropertyPathFromName__parameter__factoryName": "Standardname", "@sage/xtrem-import-export/nodes__import_export_template__query__getPropertyPathFromName__parameter__level": "Level", "@sage/xtrem-import-export/nodes__import_export_template__query__getPropertyPathFromName__parameter__parentNonVitalReferencePath": "Übergeordneter nicht vitaler Referenzpfad", "@sage/xtrem-import-export/nodes__import_export_template__query__getPropertyPathFromName__parameter__propertyName": "Eigenschaftenname", "@sage/xtrem-import-export/nodes__import_export_template__query__getTechnicalInformation": "Technische Informationen abrufen", "@sage/xtrem-import-export/nodes__import_export_template__query__getTechnicalInformation__failed": "Technische Informationen abrufen fehlgeschlagen.", "@sage/xtrem-import-export/nodes__import_export_template__query__getTechnicalInformation__parameter__isExportOnly": "Ist nur Export", "@sage/xtrem-import-export/nodes__import_export_template__query__getTechnicalInformation__parameter__level": "<PERSON><PERSON><PERSON>", "@sage/xtrem-import-export/nodes__import_export_template__query__getTechnicalInformation__parameter__parentProperty": "Übergeordnete Eigenschaft", "@sage/xtrem-import-export/nodes__import_export_template__query__getTechnicalInformation__parameter__property": "Eigenschaft", "@sage/xtrem-import-export/nodes__import_export_template__query__getUserImportExportPreferences": "Einstellungen Import Export Benutzer abrufen", "@sage/xtrem-import-export/nodes__import_export_template__query__getUserImportExportPreferences__failed": "Einstellungen Import Export Benutzer abrufen fehlgeschlagen.", "@sage/xtrem-import-export/nodes__import_result__asyncMutation__asyncExport": "Export", "@sage/xtrem-import-export/nodes__import_result__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-import-export/nodes__import_result__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-import-export/nodes__import_result__node_name": "Import-<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-import-export/nodes__import_result__property__doInsert": "Einfügen", "@sage/xtrem-import-export/nodes__import_result__property__doUpdate": "Aktualisierung", "@sage/xtrem-import-export/nodes__import_result__property__dryRun": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-import-export/nodes__import_result__property__endStamp": "Stempel Ende", "@sage/xtrem-import-export/nodes__import_result__property__filename": "Dateiname", "@sage/xtrem-import-export/nodes__import_result__property__generalError": "Allgemeiner Fehler", "@sage/xtrem-import-export/nodes__import_result__property__importExportTemplate": "Import-/Export-Vorlage", "@sage/xtrem-import-export/nodes__import_result__property__maxErrorCount": "Maximale Anzahl zulässiger Importfehler", "@sage/xtrem-import-export/nodes__import_result__property__notificationId": "Benachrichtigungs-ID", "@sage/xtrem-import-export/nodes__import_result__property__numberOfRowsInError": "<PERSON><PERSON><PERSON> fehlerhafte Zeilen", "@sage/xtrem-import-export/nodes__import_result__property__parameters": "Parameter", "@sage/xtrem-import-export/nodes__import_result__property__rowsInError": "Fehlerhafte Zeilen", "@sage/xtrem-import-export/nodes__import_result__property__rowsProcessed": "Verarbeitete Zeilen", "@sage/xtrem-import-export/nodes__import_result__property__startStamp": "Stempel Start", "@sage/xtrem-import-export/nodes__import_result__property__status": "Status", "@sage/xtrem-import-export/nodes__import_result__property__uploadedFile": "<PERSON><PERSON> ho<PERSON>", "@sage/xtrem-import-export/nodes__import_result__property__uploadRejectReason": "Grund Ablehnung Upload", "@sage/xtrem-import-export/nodes__import_template__property__setupId": "ID Einstellungen", "@sage/xtrem-import-export/nodes__parsed_result_bucket__node_name": "Analysierter Ergebnisbucket", "@sage/xtrem-import-export/package__name": "Import", "@sage/xtrem-import-export/page__import_export_template__cannot_move_further": "Sie können die Zeile nicht weiter verschieben.", "@sage/xtrem-import-export/page__import_export_template__cannot_move_group_main_key": "Die aktuelle Zeile ist der Hauptschlüssel für die Gruppe. Sie kann nicht entfernt werden.", "@sage/xtrem-import-export/page__import_export_template__cannot_move_outside_parent_group": "Die Zeile muss in der übergeordneten Gruppe bleiben.", "@sage/xtrem-import-export/page__import_export_template__confirm_deletion": "Löschen bestätigen", "@sage/xtrem-import-export/page__import_export_template__confirm_deletion_of_sublevel": "Sie sind dabei, eine Unterebene und alle Einträge zu löschen. Fortfahren?", "@sage/xtrem-import-export/page__import_export_template__line_values_not_found": "Zeilenwerte nicht gefunden.", "@sage/xtrem-import-export/page__import_export_template__moving_line_up_down": "<PERSON><PERSON><PERSON> nach oben oder nach unten verschieben", "@sage/xtrem-import-export/pages__export_bulk_mutation_configuration____title": "Exportvorlage auswählen", "@sage/xtrem-import-export/pages__export_bulk_mutation_configuration__id____columns__title__description": "Bezeichnung", "@sage/xtrem-import-export/pages__export_bulk_mutation_configuration__id____columns__title__id": "ID", "@sage/xtrem-import-export/pages__export_bulk_mutation_configuration__id____columns__title__name": "<PERSON><PERSON><PERSON>nname", "@sage/xtrem-import-export/pages__export_bulk_mutation_configuration__id____title": "Exportvorlage", "@sage/xtrem-import-export/pages__import_data____title": "Datenimport", "@sage/xtrem-import-export/pages__import_data__continueAfterErrorOption____title": "Fortfahren und Fehler ignorieren", "@sage/xtrem-import-export/pages__import_data__csvFile____title": "<PERSON>i ausw<PERSON>hlen", "@sage/xtrem-import-export/pages__import_data__downloadSection____title": "Download", "@sage/xtrem-import-export/pages__import_data__executeImport____title": "Import", "@sage/xtrem-import-export/pages__import_data__fileBlock____title": "CSV-Datei", "@sage/xtrem-import-export/pages__import_data__fileField____title": "Import-<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-import-export/pages__import_data__generalSection____title": "Daten", "@sage/xtrem-import-export/pages__import_data__importAction____title": "Import", "@sage/xtrem-import-export/pages__import_data__importData____columns__title__filename": "Dateiname", "@sage/xtrem-import-export/pages__import_data__importData____columns__title__nodeName": "Node-Name", "@sage/xtrem-import-export/pages__import_data__importData____columns__title__template": "Vorlage", "@sage/xtrem-import-export/pages__import_data__importData____columns__title__templateDescription": "Vorlagenbezeichnung", "@sage/xtrem-import-export/pages__import_data__importData____columns__title__templateName": "<PERSON><PERSON><PERSON>nname", "@sage/xtrem-import-export/pages__import_data__importData____title": "<PERSON><PERSON> import<PERSON><PERSON><PERSON>", "@sage/xtrem-import-export/pages__import_data__importResults____columns__title__dryRun": "Test", "@sage/xtrem-import-export/pages__import_data__importResults____columns__title__endStamp": "Endzeit", "@sage/xtrem-import-export/pages__import_data__importResults____columns__title__filename": "Dateiname", "@sage/xtrem-import-export/pages__import_data__importResults____columns__title__generalError": "Fehlerdetail", "@sage/xtrem-import-export/pages__import_data__importResults____columns__title__numberOfRowsInError": "<PERSON><PERSON><PERSON>", "@sage/xtrem-import-export/pages__import_data__importResults____columns__title__rowsProcessed": "Verarbeitete Zeilen", "@sage/xtrem-import-export/pages__import_data__importResults____columns__title__startStamp": "Startzeit", "@sage/xtrem-import-export/pages__import_data__importResults____columns__title__status": "Status", "@sage/xtrem-import-export/pages__import_data__importResults____dropdownActions__title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-import-export/pages__import_data__importResults____title": "Ergebnisse", "@sage/xtrem-import-export/pages__import_data__insertOption____title": "Einfügen", "@sage/xtrem-import-export/pages__import_data__maximumNumberOfErrors____title": "Maximale Anzahl an Fehlern", "@sage/xtrem-import-export/pages__import_data__optionsBlock____title": "Optionen", "@sage/xtrem-import-export/pages__import_data__refreshRelationMapping____title": "Aktualisieren", "@sage/xtrem-import-export/pages__import_data__resultsSection____title": "Import-Ergebnisse", "@sage/xtrem-import-export/pages__import_data__test____title": "Test-Import", "@sage/xtrem-import-export/pages__import_data__updateOption____title": "Aktualisierung", "@sage/xtrem-import-export/pages__import_data_page__importDataBlock____title": "<PERSON><PERSON> import<PERSON><PERSON><PERSON>", "@sage/xtrem-import-export/pages__import_data_page__testImportAction____title": "Test", "@sage/xtrem-import-export/pages__import_export_template____navigationPanel__listItem__idLine__title": "ID", "@sage/xtrem-import-export/pages__import_export_template____navigationPanel__listItem__line3__title": "Bezeichnung", "@sage/xtrem-import-export/pages__import_export_template____navigationPanel__listItem__Line4__title": "Bezeichnung", "@sage/xtrem-import-export/pages__import_export_template____navigationPanel__listItem__name__title": "Name", "@sage/xtrem-import-export/pages__import_export_template____navigationPanel__listItem__title__title": "ID", "@sage/xtrem-import-export/pages__import_export_template____navigationPanel__listItem__titleLeft__title": "Bezeichnung", "@sage/xtrem-import-export/pages__import_export_template____navigationPanel__listItem__titleRight__title": "<PERSON><PERSON>", "@sage/xtrem-import-export/pages__import_export_template____navigationPanel__optionsMenu__title": "Alle", "@sage/xtrem-import-export/pages__import_export_template____navigationPanel__optionsMenu__title__2": "Import- und Export-Vorlagen", "@sage/xtrem-import-export/pages__import_export_template____navigationPanel__optionsMenu__title__3": "Importvorlagen", "@sage/xtrem-import-export/pages__import_export_template____navigationPanel__optionsMenu__title__4": "Exportvorlagen", "@sage/xtrem-import-export/pages__import_export_template____objectTypePlural": "Import- und Export-Vorlagen", "@sage/xtrem-import-export/pages__import_export_template____objectTypeSingular": "Import- und Export-Vorlage", "@sage/xtrem-import-export/pages__import_export_template____title": "Import- und Export-Vorlage", "@sage/xtrem-import-export/pages__import_export_template___id____title": "ID", "@sage/xtrem-import-export/pages__import_export_template__canImport____title": "Importvorlage", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__columns__currentProperty__name__title": "Standard", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__columns__currentProperty__name__title__2": "Zielstandard", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__columns__parentProperty__name__title": "Übergeordnete Eigenschaft Standard", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__columns__parentProperty__name__title__2": "Übergeordnete Eigenschaft Zielstandard", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__title__currentProperty__factory__name": "Standard", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__title__currentProperty__name": "<PERSON><PERSON>", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__title__currentProperty__targetFactory__name": "Zielstandard", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__title__dataType": "Datentyp", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__title__description": "Bezeichnung", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__title__factory__name": "Standard", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__title__isCustom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__title__isExportOnly": "Nur Export", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__title__isMandatory": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__title__locale": "Gebietsschema", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__title__parentProperty__factory__name": "Übergeordneter Standard", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__title__parentProperty__name": "Übergeordnete Eigenschaft", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__title__parentProperty__targetFactory__name": "Übergeordneter Zielstandard", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__title__path": "Pfad", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__title__targetFactory__name": "Zielstandard", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____dropdownActions__title": "Löschen", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____inlineActions__title": "Nach oben", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____inlineActions__title__2": "Nach unten", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____inlineActions__title__3": "Einfügen", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____title": "Details", "@sage/xtrem-import-export/pages__import_export_template__columnsTableBlock____title": "Vorlagenspalten", "@sage/xtrem-import-export/pages__import_export_template__copyToClipboard____title": "In Zwischenablage kopieren", "@sage/xtrem-import-export/pages__import_export_template__csvTemplateDisplay____helperText": "Vorlagenquellcode", "@sage/xtrem-import-export/pages__import_export_template__csvTemplateDisplay____title": "CSV-Vorlage", "@sage/xtrem-import-export/pages__import_export_template__customSave____title": "Speichern", "@sage/xtrem-import-export/pages__import_export_template__dateFormat____title": "Datumsformat", "@sage/xtrem-import-export/pages__import_export_template__decimalPoint____title": "Dezimaltrennzeichen", "@sage/xtrem-import-export/pages__import_export_template__defaultParametersBlock____title": "Formatierungsoptionen", "@sage/xtrem-import-export/pages__import_export_template__delimiter____title": "Trennzeichen", "@sage/xtrem-import-export/pages__import_export_template__description____title": "Bezeichnung", "@sage/xtrem-import-export/pages__import_export_template__downloadSection____title": "Download", "@sage/xtrem-import-export/pages__import_export_template__downloadTemplate____title": "Vorlage", "@sage/xtrem-import-export/pages__import_export_template__downloadTemplateSection____title": "Download", "@sage/xtrem-import-export/pages__import_export_template__escapeCharacter____title": "Escape-<PERSON><PERSON><PERSON>", "@sage/xtrem-import-export/pages__import_export_template__exportData____title": "Daten exportieren", "@sage/xtrem-import-export/pages__import_export_template__exportSettingsBlock____title": "Exporteinstellungen", "@sage/xtrem-import-export/pages__import_export_template__exportTemplatePreviewBlock____title": "Exportvorlage", "@sage/xtrem-import-export/pages__import_export_template__fileField____title": "Import-<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-import-export/pages__import_export_template__fillGridFromClipboard____title": "Zwischenablage in Tabelle kopieren", "@sage/xtrem-import-export/pages__import_export_template__generalSection____title": "Allgemein", "@sage/xtrem-import-export/pages__import_export_template__generateTemplate____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-import-export/pages__import_export_template__hasDataTypeLine____title": "Datentyp", "@sage/xtrem-import-export/pages__import_export_template__hasDescriptionLine____title": "Bezeichnung", "@sage/xtrem-import-export/pages__import_export_template__hasLocaleLine____title": "Gebietsschema", "@sage/xtrem-import-export/pages__import_export_template__historySection____title": "Import<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-import-export/pages__import_export_template__id____title": "ID", "@sage/xtrem-import-export/pages__import_export_template__importData____title": "Daten importieren", "@sage/xtrem-import-export/pages__import_export_template__importResults____columns__title__dryRun": "Test", "@sage/xtrem-import-export/pages__import_export_template__importResults____columns__title__endStamp": "Stempel Ende", "@sage/xtrem-import-export/pages__import_export_template__importResults____columns__title__filename": "Dateiname", "@sage/xtrem-import-export/pages__import_export_template__importResults____columns__title__numberOfRowsInError": "Fehlerhafte Zeilen", "@sage/xtrem-import-export/pages__import_export_template__importResults____columns__title__rowsProcessed": "Verarbeitete Zeilen", "@sage/xtrem-import-export/pages__import_export_template__importResults____columns__title__startStamp": "Stempel Start", "@sage/xtrem-import-export/pages__import_export_template__importResults____columns__title__status": "Status", "@sage/xtrem-import-export/pages__import_export_template__importResults____dropdownActions__title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-import-export/pages__import_export_template__importResults____title": "Ergebnisse", "@sage/xtrem-import-export/pages__import_export_template__importSettingsBlock____title": "Importeinstellungen", "@sage/xtrem-import-export/pages__import_export_template__isActive____title": "Aktiv", "@sage/xtrem-import-export/pages__import_export_template__isDefault____title": "Standardvorlage für Export", "@sage/xtrem-import-export/pages__import_export_template__name____title": "Name", "@sage/xtrem-import-export/pages__import_export_template__nodeName____title": "Node-Name", "@sage/xtrem-import-export/pages__import_export_template__nodeProperty____columns__title__factory__name": "Standard", "@sage/xtrem-import-export/pages__import_export_template__nodeProperty____columns__title__targetFactory__name": "Zielstandard", "@sage/xtrem-import-export/pages__import_export_template__nodeProperty____title": "Auswählen um Feld hinzuzufügen", "@sage/xtrem-import-export/pages__import_export_template__omitDataType____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-import-export/pages__import_export_template__omitDescription____title": "Bezeichnung auslassen", "@sage/xtrem-import-export/pages__import_export_template__quoteCharacter____title": "Anführungszeichen", "@sage/xtrem-import-export/pages__import_export_template__refreshRelationMapping____title": "Aktualisieren", "@sage/xtrem-import-export/pages__import_export_template__resetGrid____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-import-export/pages__import_export_template__templateUse____title": "Verwendung Vorlage", "@sage/xtrem-import-export/pages__import_export_template_type__export": "Export", "@sage/xtrem-import-export/pages__import_export_template_type__import_and_export": "Import und Export", "@sage/xtrem-import-export/pages__import_template__setupId____title": "ID", "@sage/xtrem-import-export/permission__batch_import__name": "Batchimport", "@sage/xtrem-import-export/permission__manage__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-import-export/permission__read__name": "<PERSON><PERSON>", "@sage/xtrem-import-export/select-file": "CSV-<PERSON><PERSON>", "@sage/xtrem-import-export/validation-error": "Freigabefehler"}