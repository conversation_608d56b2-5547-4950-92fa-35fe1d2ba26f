{"@sage/xtrem-import-export/activity__import_data__name": "Import data", "@sage/xtrem-import-export/activity__import_export_template__name": "Import export template", "@sage/xtrem-import-export/confirm-import": "Confirm import", "@sage/xtrem-import-export/confirm-import-question": "Do you want to import the file?", "@sage/xtrem-import-export/data_types__csv_stream_type__name": "Csv stream type", "@sage/xtrem-import-export/data_types__csv_template_text_stream_type__name": "Csv template text stream type", "@sage/xtrem-import-export/data_types__displayed_property_type_enum__name": "Displayed property type enum", "@sage/xtrem-import-export/data_types__file_format_enum__name": "File format enum", "@sage/xtrem-import-export/data_types__html_data_type__name": "Html data type", "@sage/xtrem-import-export/data_types__import_status_enum__name": "Import status enum", "@sage/xtrem-import-export/data_types__name_data_type__name": "Name data type", "@sage/xtrem-import-export/data_types__rows_text_stream_type__name": "Rows text stream type", "@sage/xtrem-import-export/data_types__short_string_data_type__name": "Short string data type", "@sage/xtrem-import-export/data_types__template_use_enum__name": "Template use enum", "@sage/xtrem-import-export/data_types__test_enum_enum__name": "Test enum enum", "@sage/xtrem-import-export/default_parameters-decimal_point-invalid_value": "The decimal point is not valid: {{value}}.", "@sage/xtrem-import-export/edit-create-line": "Insert template column", "@sage/xtrem-import-export/enums__displayed_property_type__collection": "Collection", "@sage/xtrem-import-export/enums__displayed_property_type__mainKey": "Main key", "@sage/xtrem-import-export/enums__displayed_property_type__normalProperty": "Normal property", "@sage/xtrem-import-export/enums__displayed_property_type__reference": "Reference", "@sage/xtrem-import-export/enums__displayed_property_type__vitalCollection": "Vital collection", "@sage/xtrem-import-export/enums__displayed_property_type__vitalReference": "Vital reference", "@sage/xtrem-import-export/enums__file_format__csv": "Csv", "@sage/xtrem-import-export/enums__file_format__xlsx": "Xlsx", "@sage/xtrem-import-export/enums__import_status__failed": "Failed", "@sage/xtrem-import-export/enums__import_status__finished": "Finished", "@sage/xtrem-import-export/enums__import_status__inProgress": "In progress", "@sage/xtrem-import-export/enums__import_status__pending": "Pending", "@sage/xtrem-import-export/enums__import_status__rejected": "Rejected", "@sage/xtrem-import-export/enums__template_use__exportOnly": "Export only", "@sage/xtrem-import-export/enums__template_use__importAndExport": "Import and export", "@sage/xtrem-import-export/enums__template_use__importOnly": "Import only", "@sage/xtrem-import-export/enums__test_enum__value1": "Value 1", "@sage/xtrem-import-export/enums__test_enum__value2": "Value 2", "@sage/xtrem-import-export/enums__test_enum__value3": "Value 3", "@sage/xtrem-import-export/function__export_send_client_notification_description": "Records exported: {{numberOfRecords}}.", "@sage/xtrem-import-export/function__export_send_client_notification_title_fail": "The export with this template ended in error: {{templateId}}.", "@sage/xtrem-import-export/function__export_send_client_notification_title_success": "The export with this template ended successfully: {{templateId}}.", "@sage/xtrem-import-export/functions__csv_to_json__header_disabled_by_a_service_option": "Header {{csvHeader}} is disabled by a service option", "@sage/xtrem-import-export/functions__csv_to_json__header_is_an_encrypted_string": "This header is an encrypted string: {{csvHeader}}.", "@sage/xtrem-import-export/functions__csv_to_json__invalid_custom_field_column_header": "Invalid custom field in column header: {{csvHeader}}.", "@sage/xtrem-import-export/functions__csv_to_json__missing_property_header": "Property {{propertyName}} not found in the host factory {{factoryName}}.", "@sage/xtrem-import-export/invalid-reference-header-no-natural-key": "The {{name}} header cannot be included in the import template. The {{target}} target node needs to have a natural key index.", "@sage/xtrem-import-export/invalid-reference-header-unexpected-index-on-natural-key": "The {{name}} column header is not valid. Remove '({{columns}})' after the property name.", "@sage/xtrem-import-export/menu_item__import-data": "Import", "@sage/xtrem-import-export/menu_item__import-export": "Import and export", "@sage/xtrem-import-export/missing-natural-key-header": "The natural key for the {{node}} node is missing properties that are not included in the template. Add the missing properties: {{missing}}.", "@sage/xtrem-import-export/no-file-to-import": "There are no files to import", "@sage/xtrem-import-export/no-template-selected": "Please select an existing template for each file to import", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__asyncExport": "Export", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__batchImport": "Batch import", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__batchImport__failed": "Batch import failed.", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__batchImport__parameter__options": "Options", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__batchImport__parameter__templateId": "Template id", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__batchImport__parameter__uploadedFileId": "Uploaded file id", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__exportByTemplateDefinition": "Export by template definition", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__exportByTemplateDefinition__failed": "Export by template definition failed.", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__exportByTemplateDefinition__parameter__filter": "Filter", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__exportByTemplateDefinition__parameter__nodeName": "Node name", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__exportByTemplateDefinition__parameter__orderBy": "Order by", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__exportByTemplateDefinition__parameter__outputFormat": "Output format", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__exportByTemplateDefinition__parameter__templateDefinition": "Template definition", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__exportByTemplateId": "Export by template id", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__exportByTemplateId__failed": "Export by template id failed.", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__exportByTemplateId__parameter__filter": "Filter", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__exportByTemplateId__parameter__outputFormat": "Output format", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__exportByTemplateId__parameter__templateId": "Template id", "@sage/xtrem-import-export/nodes__import_export_template__node_name": "Import export template", "@sage/xtrem-import-export/nodes__import_export_template__property__csvTemplate": "Csv template", "@sage/xtrem-import-export/nodes__import_export_template__property__defaultParameters": "Default parameters", "@sage/xtrem-import-export/nodes__import_export_template__property__description": "Description", "@sage/xtrem-import-export/nodes__import_export_template__property__id": "Id", "@sage/xtrem-import-export/nodes__import_export_template__property__isActive": "Is active", "@sage/xtrem-import-export/nodes__import_export_template__property__isDefault": "Is default", "@sage/xtrem-import-export/nodes__import_export_template__property__name": "Name", "@sage/xtrem-import-export/nodes__import_export_template__property__nodeName": "Node name", "@sage/xtrem-import-export/nodes__import_export_template__property__templateUse": "Template use", "@sage/xtrem-import-export/nodes__import_export_template__query__getDefaultCsvTemplate": "Get default csv template", "@sage/xtrem-import-export/nodes__import_export_template__query__getDefaultCsvTemplate__failed": "Get default csv template failed.", "@sage/xtrem-import-export/nodes__import_export_template__query__getDefaultCsvTemplate__parameter__isExportWithDefinition": "Is export with definition", "@sage/xtrem-import-export/nodes__import_export_template__query__getDefaultCsvTemplate__parameter__level": "Level", "@sage/xtrem-import-export/nodes__import_export_template__query__getDefaultCsvTemplate__parameter__nodeName": "Node name", "@sage/xtrem-import-export/nodes__import_export_template__query__getDefaultCsvTemplate__parameter__withSortValue": "With sort value", "@sage/xtrem-import-export/nodes__import_export_template__query__getFieldsByFactory": "Get fields by factory", "@sage/xtrem-import-export/nodes__import_export_template__query__getFieldsByFactory__failed": "Get fields by factory failed.", "@sage/xtrem-import-export/nodes__import_export_template__query__getFieldsByFactory__parameter__factoryName": "Factory name", "@sage/xtrem-import-export/nodes__import_export_template__query__getFieldsByFactory__parameter__isExportOnly": "Is export only", "@sage/xtrem-import-export/nodes__import_export_template__query__getFieldsByFactory__parameter__level": "Level", "@sage/xtrem-import-export/nodes__import_export_template__query__getFieldsByFactory__parameter__withSortValue": "With sort value", "@sage/xtrem-import-export/nodes__import_export_template__query__getNodeList": "Get node list", "@sage/xtrem-import-export/nodes__import_export_template__query__getNodeList__failed": "Get node list failed.", "@sage/xtrem-import-export/nodes__import_export_template__query__getNodeList__parameter__filter": "Filter", "@sage/xtrem-import-export/nodes__import_export_template__query__getTechnicalInformation": "Get technical information", "@sage/xtrem-import-export/nodes__import_export_template__query__getTechnicalInformation__failed": "Get technical information failed.", "@sage/xtrem-import-export/nodes__import_export_template__query__getTechnicalInformation__parameter__isExportOnly": "Is export only", "@sage/xtrem-import-export/nodes__import_export_template__query__getTechnicalInformation__parameter__level": "Level", "@sage/xtrem-import-export/nodes__import_export_template__query__getTechnicalInformation__parameter__parentProperty": "Parent property", "@sage/xtrem-import-export/nodes__import_export_template__query__getTechnicalInformation__parameter__property": "Property", "@sage/xtrem-import-export/nodes__import_export_template__query__getUserImportExportPreferences": "Get user import export preferences", "@sage/xtrem-import-export/nodes__import_export_template__query__getUserImportExportPreferences__failed": "Get user import export preferences failed.", "@sage/xtrem-import-export/nodes__import_result__asyncMutation__asyncExport": "Export", "@sage/xtrem-import-export/nodes__import_result__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-import-export/nodes__import_result__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-import-export/nodes__import_result__node_name": "Import result", "@sage/xtrem-import-export/nodes__import_result__property__doInsert": "Do insert", "@sage/xtrem-import-export/nodes__import_result__property__doUpdate": "Do update", "@sage/xtrem-import-export/nodes__import_result__property__dryRun": "Dry run", "@sage/xtrem-import-export/nodes__import_result__property__endStamp": "End stamp", "@sage/xtrem-import-export/nodes__import_result__property__filename": "Filename", "@sage/xtrem-import-export/nodes__import_result__property__generalError": "General error", "@sage/xtrem-import-export/nodes__import_result__property__importExportTemplate": "Import export template", "@sage/xtrem-import-export/nodes__import_result__property__maxErrorCount": "Max error count", "@sage/xtrem-import-export/nodes__import_result__property__notificationId": "Notification id", "@sage/xtrem-import-export/nodes__import_result__property__numberOfRowsInError": "Number of rows in error", "@sage/xtrem-import-export/nodes__import_result__property__parameters": "Parameters", "@sage/xtrem-import-export/nodes__import_result__property__rowsInError": "Rows in error", "@sage/xtrem-import-export/nodes__import_result__property__rowsProcessed": "Rows processed", "@sage/xtrem-import-export/nodes__import_result__property__startStamp": "Start stamp", "@sage/xtrem-import-export/nodes__import_result__property__status": "Status", "@sage/xtrem-import-export/nodes__import_result__property__uploadedFile": "Uploaded file", "@sage/xtrem-import-export/nodes__import_result__property__uploadRejectReason": "Upload reject reason", "@sage/xtrem-import-export/package__name": "Sage xtrem import export", "@sage/xtrem-import-export/page__import_export_template__cannot_move_further": "You cannot move the line any further.", "@sage/xtrem-import-export/page__import_export_template__cannot_move_group_main_key": "The current line is the main key for the group. You cannot move it.", "@sage/xtrem-import-export/page__import_export_template__cannot_move_outside_parent_group": "You need to keep the line within its parent group.", "@sage/xtrem-import-export/page__import_export_template__confirm_deletion": "Confirm deletion", "@sage/xtrem-import-export/page__import_export_template__confirm_deletion_of_sublevel": "You are about to delete a sublevel and all its entries. Continue?", "@sage/xtrem-import-export/page__import_export_template__line_values_not_found": "Line values not found.", "@sage/xtrem-import-export/page__import_export_template__moving_line_up_down": "Move line up or down", "@sage/xtrem-import-export/pages__export_bulk_mutation_configuration____title": "Select export template", "@sage/xtrem-import-export/pages__export_bulk_mutation_configuration__id____columns__title__description": "Description", "@sage/xtrem-import-export/pages__export_bulk_mutation_configuration__id____columns__title__id": "ID", "@sage/xtrem-import-export/pages__export_bulk_mutation_configuration__id____columns__title__name": "Template name", "@sage/xtrem-import-export/pages__export_bulk_mutation_configuration__id____title": "Export template", "@sage/xtrem-import-export/pages__import_data____title": "Data import", "@sage/xtrem-import-export/pages__import_data__continueAfterErrorOption____title": "Continue and ignore the error", "@sage/xtrem-import-export/pages__import_data__csvFile____title": "Select file", "@sage/xtrem-import-export/pages__import_data__downloadSection____title": "Download", "@sage/xtrem-import-export/pages__import_data__executeImport____title": "Import", "@sage/xtrem-import-export/pages__import_data__fileBlock____title": "CSV file", "@sage/xtrem-import-export/pages__import_data__fileField____title": "Import result", "@sage/xtrem-import-export/pages__import_data__generalSection____title": "Data", "@sage/xtrem-import-export/pages__import_data__importData____columns__title__filename": "File name", "@sage/xtrem-import-export/pages__import_data__importData____columns__title__nodeName": "Node name", "@sage/xtrem-import-export/pages__import_data__importData____columns__title__template": "Template", "@sage/xtrem-import-export/pages__import_data__importData____columns__title__templateDescription": "Template description", "@sage/xtrem-import-export/pages__import_data__importData____columns__title__templateName": "Template name", "@sage/xtrem-import-export/pages__import_data__importData____title": "Files to import", "@sage/xtrem-import-export/pages__import_data__importResults____columns__title__dryRun": "Test", "@sage/xtrem-import-export/pages__import_data__importResults____columns__title__endStamp": "End time", "@sage/xtrem-import-export/pages__import_data__importResults____columns__title__filename": "File name", "@sage/xtrem-import-export/pages__import_data__importResults____columns__title__generalError": "Error detail", "@sage/xtrem-import-export/pages__import_data__importResults____columns__title__numberOfRowsInError": "Number of errors", "@sage/xtrem-import-export/pages__import_data__importResults____columns__title__rowsProcessed": "Rows processed", "@sage/xtrem-import-export/pages__import_data__importResults____columns__title__startStamp": "Start time", "@sage/xtrem-import-export/pages__import_data__importResults____columns__title__status": "Status", "@sage/xtrem-import-export/pages__import_data__importResults____dropdownActions__title": "Download", "@sage/xtrem-import-export/pages__import_data__importResults____title": "Results", "@sage/xtrem-import-export/pages__import_data__insertOption____title": "Insert", "@sage/xtrem-import-export/pages__import_data__maximumNumberOfErrors____title": "Maximum number of errors", "@sage/xtrem-import-export/pages__import_data__optionsBlock____title": "Options", "@sage/xtrem-import-export/pages__import_data__refreshRelationMapping____title": "Refresh", "@sage/xtrem-import-export/pages__import_data__resultsSection____title": "Import results", "@sage/xtrem-import-export/pages__import_data__test____title": "Test import", "@sage/xtrem-import-export/pages__import_data__updateOption____title": "Update", "@sage/xtrem-import-export/pages__import_export_template____navigationPanel__listItem__line3__title": "Description", "@sage/xtrem-import-export/pages__import_export_template____navigationPanel__listItem__name__title": "Name", "@sage/xtrem-import-export/pages__import_export_template____navigationPanel__listItem__title__title": "ID", "@sage/xtrem-import-export/pages__import_export_template____navigationPanel__listItem__titleRight__title": "Type", "@sage/xtrem-import-export/pages__import_export_template____navigationPanel__optionsMenu__title": "All", "@sage/xtrem-import-export/pages__import_export_template____navigationPanel__optionsMenu__title__2": "Import and export templates", "@sage/xtrem-import-export/pages__import_export_template____navigationPanel__optionsMenu__title__3": "Import templates", "@sage/xtrem-import-export/pages__import_export_template____navigationPanel__optionsMenu__title__4": "Export templates", "@sage/xtrem-import-export/pages__import_export_template____objectTypePlural": "Import and Export templates", "@sage/xtrem-import-export/pages__import_export_template____objectTypeSingular": "Import and Export template", "@sage/xtrem-import-export/pages__import_export_template____title": "Import and Export template", "@sage/xtrem-import-export/pages__import_export_template___id____title": "_id", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__columns__currentProperty__name__title": "Factory", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__columns__currentProperty__name__title__2": "Target factory", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__columns__parentProperty__name__title": "Parent property factory", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__columns__parentProperty__name__title__2": "Parent property target factory", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__title__currentProperty__factory__name": "Factory", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__title__currentProperty__name": "Field", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__title__currentProperty__targetFactory__name": "Target factory", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__title__dataType": "Data type", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__title__description": "Description", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__title__isCustom": "Custom", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__title__isExportOnly": "Export only", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__title__isMandatory": "Mandatory", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__title__locale": "Locale", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__title__parentProperty__factory__name": "Parent factory", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__title__parentProperty__name": "Parent property", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__title__parentProperty__targetFactory__name": "Parent target factory", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__title__path": "Path", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____dropdownActions__title": "Delete", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____inlineActions__title": "Move up", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____inlineActions__title__2": "Move down", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____inlineActions__title__3": "Insert", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____title": "Details", "@sage/xtrem-import-export/pages__import_export_template__columnsTableBlock____title": "Template columns", "@sage/xtrem-import-export/pages__import_export_template__csvTemplateDisplay____helperText": "Source code: Template", "@sage/xtrem-import-export/pages__import_export_template__csvTemplateDisplay____title": "CSV preview", "@sage/xtrem-import-export/pages__import_export_template__customSave____title": "Save", "@sage/xtrem-import-export/pages__import_export_template__decimalPoint____title": "Decimal point", "@sage/xtrem-import-export/pages__import_export_template__defaultParametersBlock____title": "Formatting options", "@sage/xtrem-import-export/pages__import_export_template__delimiter____title": "Delimiter", "@sage/xtrem-import-export/pages__import_export_template__description____title": "Description", "@sage/xtrem-import-export/pages__import_export_template__downloadSection____title": "Download", "@sage/xtrem-import-export/pages__import_export_template__downloadTemplate____title": "Template", "@sage/xtrem-import-export/pages__import_export_template__downloadTemplateSection____title": "Download", "@sage/xtrem-import-export/pages__import_export_template__escapeCharacter____title": "Escape character", "@sage/xtrem-import-export/pages__import_export_template__exportData____title": "Export data", "@sage/xtrem-import-export/pages__import_export_template__exportTemplatePreviewBlock____title": "Export template", "@sage/xtrem-import-export/pages__import_export_template__fileField____title": "Import Result", "@sage/xtrem-import-export/pages__import_export_template__generalSection____title": "General", "@sage/xtrem-import-export/pages__import_export_template__generateTemplate____title": "Generate template", "@sage/xtrem-import-export/pages__import_export_template__hasDataTypeLine____title": "Data type", "@sage/xtrem-import-export/pages__import_export_template__hasDescriptionLine____title": "Description", "@sage/xtrem-import-export/pages__import_export_template__hasLocaleLine____title": "Locale", "@sage/xtrem-import-export/pages__import_export_template__historySection____title": "Import history", "@sage/xtrem-import-export/pages__import_export_template__id____title": "ID", "@sage/xtrem-import-export/pages__import_export_template__importData____title": "Import data", "@sage/xtrem-import-export/pages__import_export_template__importResults____columns__title__dryRun": "Test", "@sage/xtrem-import-export/pages__import_export_template__importResults____columns__title__endStamp": "End stamp", "@sage/xtrem-import-export/pages__import_export_template__importResults____columns__title__filename": "File name", "@sage/xtrem-import-export/pages__import_export_template__importResults____columns__title__numberOfRowsInError": "Rows in error", "@sage/xtrem-import-export/pages__import_export_template__importResults____columns__title__rowsProcessed": "Rows processed", "@sage/xtrem-import-export/pages__import_export_template__importResults____columns__title__startStamp": "Start stamp", "@sage/xtrem-import-export/pages__import_export_template__importResults____columns__title__status": "Status", "@sage/xtrem-import-export/pages__import_export_template__importResults____dropdownActions__title": "Download", "@sage/xtrem-import-export/pages__import_export_template__importResults____title": "Results", "@sage/xtrem-import-export/pages__import_export_template__isActive____title": "Active", "@sage/xtrem-import-export/pages__import_export_template__isDefault____title": "Default template for export", "@sage/xtrem-import-export/pages__import_export_template__name____title": "Name", "@sage/xtrem-import-export/pages__import_export_template__nodeName____title": "Node name", "@sage/xtrem-import-export/pages__import_export_template__nodeProperty____columns__title__factory__name": "Factory", "@sage/xtrem-import-export/pages__import_export_template__nodeProperty____columns__title__targetFactory__name": "Target factory", "@sage/xtrem-import-export/pages__import_export_template__nodeProperty____title": "Select to add field", "@sage/xtrem-import-export/pages__import_export_template__quoteCharacter____title": "Quote character", "@sage/xtrem-import-export/pages__import_export_template__refreshRelationMapping____title": "Refresh", "@sage/xtrem-import-export/pages__import_export_template__resetGrid____title": "Reset grid", "@sage/xtrem-import-export/pages__import_export_template__templateUse____title": "Template use", "@sage/xtrem-import-export/permission__batch_import__name": "Batch import", "@sage/xtrem-import-export/permission__manage__name": "Manage", "@sage/xtrem-import-export/permission__read__name": "Read", "@sage/xtrem-import-export/select-file": "Please select a CSV file", "@sage/xtrem-import-export/validation-error": "Validation error"}