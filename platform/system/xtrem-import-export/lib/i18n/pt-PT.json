{"@sage/xtrem-import-export/activity__import_data__name": "Importar dados", "@sage/xtrem-import-export/activity__import_export_template__name": "Modelo de importação/exportação", "@sage/xtrem-import-export/confirm-import": "Confirmar importação", "@sage/xtrem-import-export/confirm-import-question": "Deseja importar o arquivo?", "@sage/xtrem-import-export/data_types__csv_stream_type__name": "Tipo de fluxo CSV", "@sage/xtrem-import-export/data_types__csv_template_text_stream_type__name": "Tipo de fluxo de texto do modelo CSV", "@sage/xtrem-import-export/data_types__displayed_property_type_enum__name": "Enum do tipo de propriedade apresentada", "@sage/xtrem-import-export/data_types__file_format_enum__name": "Formato ficheiro enum", "@sage/xtrem-import-export/data_types__html_data_type__name": "Tipo de dados HTML", "@sage/xtrem-import-export/data_types__import_status_enum__name": "Enum status importanção", "@sage/xtrem-import-export/data_types__name_data_type__name": "Nome tipo de dados", "@sage/xtrem-import-export/data_types__rows_text_stream_type__name": "Tipo de fluxo de texto de linhas", "@sage/xtrem-import-export/data_types__short_string_data_type__name": "Tipo de dados de string curta", "@sage/xtrem-import-export/data_types__template_use_enum__name": "Enum do modelo de utilização", "@sage/xtrem-import-export/data_types__test_enum_enum__name": "Enum enum teste", "@sage/xtrem-import-export/default_parameters-decimal_point-invalid_value": "O ponto decimal {{value}} não é válido.", "@sage/xtrem-import-export/default_parameters-delimiter-invalid_value": "O delimitador {{value}} não é válido.", "@sage/xtrem-import-export/edit-create-line": "Inserir coluna modelo", "@sage/xtrem-import-export/enums__displayed_property_type__collection": "Coleção", "@sage/xtrem-import-export/enums__displayed_property_type__mainKey": "Chave principal", "@sage/xtrem-import-export/enums__displayed_property_type__normalProperty": "Propriedade normal", "@sage/xtrem-import-export/enums__displayed_property_type__reference": "Referência", "@sage/xtrem-import-export/enums__displayed_property_type__vitalCollection": "Coleção vital", "@sage/xtrem-import-export/enums__displayed_property_type__vitalReference": "Referência vital", "@sage/xtrem-import-export/enums__file_format__csv": "CSV", "@sage/xtrem-import-export/enums__file_format__xlsx": "XLSX", "@sage/xtrem-import-export/enums__import_status__failed": "Fal<PERSON>", "@sage/xtrem-import-export/enums__import_status__finished": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-import-export/enums__import_status__inProgress": "Em-curso", "@sage/xtrem-import-export/enums__import_status__pending": "Pendente", "@sage/xtrem-import-export/enums__import_status__rejected": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-import-export/enums__template_use__exportOnly": "Apenas exportação", "@sage/xtrem-import-export/enums__template_use__importAndExport": "Importação e exportação", "@sage/xtrem-import-export/enums__template_use__importOnly": "Apenas importação", "@sage/xtrem-import-export/enums__test_enum__value1": "Valor 1", "@sage/xtrem-import-export/enums__test_enum__value2": "Valor 2", "@sage/xtrem-import-export/enums__test_enum__value3": "Valor 3", "@sage/xtrem-import-export/function__export_send_client_notification_description": "Registos exportados: {{numberOfRecords}}.", "@sage/xtrem-import-export/function__export_send_client_notification_title_fail": "A exportação com este modelo terminou com um erro: {{templateId}}.", "@sage/xtrem-import-export/function__export_send_client_notification_title_success": "A exportação com este modelo foi concluída com exito: {{templateId}}.", "@sage/xtrem-import-export/functions__csv_to_json__header_disabled_by_a_service_option": "Header {{csvHeader}} is disabled by a service option", "@sage/xtrem-import-export/functions__csv_to_json__header_is_an_encrypted_string": "Este cabeçalho é uma string criptografada: {{csvHeader}}.", "@sage/xtrem-import-export/functions__csv_to_json__invalid_custom_field_column_header": "Campo personalizado inválido no cabeçalho da coluna: {{csvHeader}}.", "@sage/xtrem-import-export/functions__csv_to_json__missing_property_header": "Propriedade {{propertyName}} não encontrado na host factory {{factoryName}}.", "@sage/xtrem-import-export/invalid-reference-header-invalid-natural-key": "As propriedades chave para o cabeçalho {{name}} não são válidas. Alterar a chave para {{{key}}.", "@sage/xtrem-import-export/invalid-reference-header-no-natural-key": "O cabeçalho {{name}} não pode ser incluído no modelo de importação. O nó de destino {{target}} precisa de ter um índice de chave natural.", "@sage/xtrem-import-export/invalid-reference-header-unexpected-index-on-natural-key": "O título da coluna {{name}} não é válido. Remover '({{columns}})' após o nome da propriedade.", "@sage/xtrem-import-export/menu_item__import": "Importação", "@sage/xtrem-import-export/menu_item__import-data": "Importação", "@sage/xtrem-import-export/menu_item__import-export": "Importação e exportação", "@sage/xtrem-import-export/missing-natural-key-header": "A chave natural para o nó {{{node}} tem propriedades em falta que não estão incluídas no modelo. Acrescentar as propriedades em falta: {{missing}}}.", "@sage/xtrem-import-export/no-file-to-import": "Nenhum arquivo para importar.", "@sage/xtrem-import-export/no-template-selected": "Selecione um modelo existente para cada arquivo a ser importado.", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__batchImport": "Importação \"Batch\"", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__batchImport__failed": "A importação batch falhou", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__batchImport__parameter__options": "Opções", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__batchImport__parameter__templateId": "ID do modelo", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__batchImport__parameter__uploadedFileId": "ID do ficheiro carregado", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__exportByTemplateDefinition": "Exportar por definição de modelo", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__exportByTemplateDefinition__failed": "Falha na exportação por definição do modelo.", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__exportByTemplateDefinition__parameter__filter": "Filtro", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__exportByTemplateDefinition__parameter__nodeName": "Nome do nó (node)", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__exportByTemplateDefinition__parameter__orderBy": "Ordenar por", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__exportByTemplateDefinition__parameter__outputFormat": "Outro formato", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__exportByTemplateDefinition__parameter__templateDefinition": "Definição do modelo", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__exportByTemplateId": "Exportar pelo template ID", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__exportByTemplateId__failed": "Falha na exportação por id de modelo.", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__exportByTemplateId__parameter__filter": "Filtro", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__exportByTemplateId__parameter__outputFormat": "Outro formato", "@sage/xtrem-import-export/nodes__import_export_template__asyncMutation__exportByTemplateId__parameter__templateId": "Template ID", "@sage/xtrem-import-export/nodes__import_export_template__node_name": "Modelo de importação/exportação", "@sage/xtrem-import-export/nodes__import_export_template__property__canImport": "Pode importar", "@sage/xtrem-import-export/nodes__import_export_template__property__csvTemplate": "Modelo CSV", "@sage/xtrem-import-export/nodes__import_export_template__property__defaultParameters": "Parâmetros por padrão", "@sage/xtrem-import-export/nodes__import_export_template__property__description": "Descrição", "@sage/xtrem-import-export/nodes__import_export_template__property__id": "ID", "@sage/xtrem-import-export/nodes__import_export_template__property__isActive": "Está ativo", "@sage/xtrem-import-export/nodes__import_export_template__property__isDefault": "Está predefinido", "@sage/xtrem-import-export/nodes__import_export_template__property__name": "Nome", "@sage/xtrem-import-export/nodes__import_export_template__property__nodeName": "Nome do nó (node)", "@sage/xtrem-import-export/nodes__import_export_template__property__templateUse": "Utilização do modelo", "@sage/xtrem-import-export/nodes__import_export_template__query__getDefaultCsvTemplate": "Obter modelo CSV predefinido", "@sage/xtrem-import-export/nodes__import_export_template__query__getDefaultCsvTemplate__failed": "Falha na obtenção do modelo CSV predefinido.", "@sage/xtrem-import-export/nodes__import_export_template__query__getDefaultCsvTemplate__parameter__isExportWithDefinition": "É exportar com definição", "@sage/xtrem-import-export/nodes__import_export_template__query__getDefaultCsvTemplate__parameter__level": "Nível", "@sage/xtrem-import-export/nodes__import_export_template__query__getDefaultCsvTemplate__parameter__nodeName": "Nome do nó (node)", "@sage/xtrem-import-export/nodes__import_export_template__query__getDefaultCsvTemplate__parameter__withSortValue": "Com valor de ordenação", "@sage/xtrem-import-export/nodes__import_export_template__query__getDisplayedPropertyTypeFromName": "Obter o tipo de propriedade apresentado a partir do nome", "@sage/xtrem-import-export/nodes__import_export_template__query__getDisplayedPropertyTypeFromName__parameter__factoryName": "Nome de fábrica", "@sage/xtrem-import-export/nodes__import_export_template__query__getDisplayedPropertyTypeFromName__parameter__level": "Nível", "@sage/xtrem-import-export/nodes__import_export_template__query__getDisplayedPropertyTypeFromName__parameter__propertyName": "Nome da propriedade", "@sage/xtrem-import-export/nodes__import_export_template__query__getFieldsByFactory": "Obter campos por fábrica", "@sage/xtrem-import-export/nodes__import_export_template__query__getFieldsByFactory__failed": "A obtenção de campos de fábrica falhou.", "@sage/xtrem-import-export/nodes__import_export_template__query__getFieldsByFactory__parameter__factoryName": "Nome de fábrica", "@sage/xtrem-import-export/nodes__import_export_template__query__getFieldsByFactory__parameter__isExportOnly": "É apenas para exportação", "@sage/xtrem-import-export/nodes__import_export_template__query__getFieldsByFactory__parameter__level": "Nível", "@sage/xtrem-import-export/nodes__import_export_template__query__getFieldsByFactory__parameter__withSortValue": "Com o valor de ordenação", "@sage/xtrem-import-export/nodes__import_export_template__query__getNodeList": "Obter lista de nós (node)", "@sage/xtrem-import-export/nodes__import_export_template__query__getNodeList__failed": "A obtenção da lista de nós falhou.", "@sage/xtrem-import-export/nodes__import_export_template__query__getNodeList__parameter__filter": "Filtro", "@sage/xtrem-import-export/nodes__import_export_template__query__getPropertyDataTypeFromName": "Obter o tipo de dados da propriedade a partir do nome", "@sage/xtrem-import-export/nodes__import_export_template__query__getPropertyDataTypeFromName__parameter__factoryName": "Nome de fábrica", "@sage/xtrem-import-export/nodes__import_export_template__query__getPropertyDataTypeFromName__parameter__propertyName": "Nome da propriedade", "@sage/xtrem-import-export/nodes__import_export_template__query__getPropertyDescriptionFromName": "Obter a descrição da propriedade a partir do nome", "@sage/xtrem-import-export/nodes__import_export_template__query__getPropertyDescriptionFromName__parameter__factoryName": "Nome de fábrica", "@sage/xtrem-import-export/nodes__import_export_template__query__getPropertyDescriptionFromName__parameter__propertyName": "Nome da propriedade", "@sage/xtrem-import-export/nodes__import_export_template__query__getPropertyLocaleFromName": "Obter o \"locale\" da propriedade a partir do nome", "@sage/xtrem-import-export/nodes__import_export_template__query__getPropertyLocaleFromName__parameter__factoryName": "Nome de fábrica", "@sage/xtrem-import-export/nodes__import_export_template__query__getPropertyLocaleFromName__parameter__propertyName": "Nome da propriedade", "@sage/xtrem-import-export/nodes__import_export_template__query__getPropertyPathFromName": "Obter o caminho da propriedade a partir do nome", "@sage/xtrem-import-export/nodes__import_export_template__query__getPropertyPathFromName__parameter__factoryName": "Nome de fábrica", "@sage/xtrem-import-export/nodes__import_export_template__query__getPropertyPathFromName__parameter__level": "Nível", "@sage/xtrem-import-export/nodes__import_export_template__query__getPropertyPathFromName__parameter__parentNonVitalReferencePath": "Principal - Trajetória de referência não vital", "@sage/xtrem-import-export/nodes__import_export_template__query__getPropertyPathFromName__parameter__propertyName": "Nome da propriedade", "@sage/xtrem-import-export/nodes__import_export_template__query__getTechnicalInformation": "Obtenha informações técnicas", "@sage/xtrem-import-export/nodes__import_export_template__query__getTechnicalInformation__failed": "Obtençao de  informações técnicas.", "@sage/xtrem-import-export/nodes__import_export_template__query__getTechnicalInformation__parameter__isExportOnly": "É apenas para exportação", "@sage/xtrem-import-export/nodes__import_export_template__query__getTechnicalInformation__parameter__level": "Nível", "@sage/xtrem-import-export/nodes__import_export_template__query__getTechnicalInformation__parameter__parentProperty": "<PERSON><PERSON><PERSON>ade principal", "@sage/xtrem-import-export/nodes__import_export_template__query__getTechnicalInformation__parameter__property": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-import-export/nodes__import_export_template__query__getUserImportExportPreferences": "Obter as preferências de importação e exportação do utilizador", "@sage/xtrem-import-export/nodes__import_export_template__query__getUserImportExportPreferences__failed": "Falha na obtenção das preferências de importação e exportação de utilizadores.", "@sage/xtrem-import-export/nodes__import_result__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-import-export/nodes__import_result__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-import-export/nodes__import_result__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-import-export/nodes__import_result__node_name": "Importar resultado", "@sage/xtrem-import-export/nodes__import_result__property__doInsert": "Inserir", "@sage/xtrem-import-export/nodes__import_result__property__doUpdate": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-import-export/nodes__import_result__property__dryRun": "Dry run", "@sage/xtrem-import-export/nodes__import_result__property__endStamp": "Carimbo final", "@sage/xtrem-import-export/nodes__import_result__property__filename": "Nome arquivo", "@sage/xtrem-import-export/nodes__import_result__property__generalError": "<PERSON><PERSON> geral", "@sage/xtrem-import-export/nodes__import_result__property__importExportTemplate": "Modelo de importação/exportação", "@sage/xtrem-import-export/nodes__import_result__property__maxErrorCount": "Número máximo de erros de importação permitido", "@sage/xtrem-import-export/nodes__import_result__property__notificationId": "ID da Notificação", "@sage/xtrem-import-export/nodes__import_result__property__numberOfRowsInError": "Número de linhas em erro", "@sage/xtrem-import-export/nodes__import_result__property__parameters": "Parâmetros", "@sage/xtrem-import-export/nodes__import_result__property__rowsInError": "Linhas com erros", "@sage/xtrem-import-export/nodes__import_result__property__rowsProcessed": "<PERSON><PERSON>", "@sage/xtrem-import-export/nodes__import_result__property__startStamp": "In<PERSON>ar <PERSON>", "@sage/xtrem-import-export/nodes__import_result__property__status": "Status", "@sage/xtrem-import-export/nodes__import_result__property__uploadedFile": "Carregamento de arquivo", "@sage/xtrem-import-export/nodes__import_result__property__uploadRejectReason": "Carregar rejeição de motivo", "@sage/xtrem-import-export/nodes__import_template__property__setupId": "ID da configuração", "@sage/xtrem-import-export/nodes__parsed_result_bucket__node_name": "Recipiente de resultados analisados", "@sage/xtrem-import-export/package__name": "Importação", "@sage/xtrem-import-export/page__import_export_template__cannot_move_further": "Não é possível deslocar mais a linha.", "@sage/xtrem-import-export/page__import_export_template__cannot_move_group_main_key": "A linha atual é a chave principal do grupo. Não é possível deslocá-la.", "@sage/xtrem-import-export/page__import_export_template__cannot_move_outside_parent_group": "<PERSON> necessário manter a linha dentro do seu grupo principal.", "@sage/xtrem-import-export/page__import_export_template__confirm_deletion": "Confirmar elimina<PERSON>", "@sage/xtrem-import-export/page__import_export_template__confirm_deletion_of_sublevel": "Est<PERSON> prestes a eliminar um subnível e todas as suas entradas. Continuar?", "@sage/xtrem-import-export/page__import_export_template__line_values_not_found": "Valores de linha não encontrados.", "@sage/xtrem-import-export/page__import_export_template__moving_line_up_down": "Mover a linha para cima ou para baixo", "@sage/xtrem-import-export/pages__export_bulk_mutation_configuration____title": "Selecionar o modelo de exportação", "@sage/xtrem-import-export/pages__export_bulk_mutation_configuration__id____columns__title__description": "Descrição", "@sage/xtrem-import-export/pages__export_bulk_mutation_configuration__id____columns__title__id": "ID", "@sage/xtrem-import-export/pages__export_bulk_mutation_configuration__id____columns__title__name": "Nome do modelo", "@sage/xtrem-import-export/pages__export_bulk_mutation_configuration__id____title": "Modelo de exportação", "@sage/xtrem-import-export/pages__import_data____title": "Importação de dados", "@sage/xtrem-import-export/pages__import_data__continueAfterErrorOption____title": "Continuar e ignorar o erro", "@sage/xtrem-import-export/pages__import_data__csvFile____title": "Selecione o ficheiro", "@sage/xtrem-import-export/pages__import_data__downloadSection____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-import-export/pages__import_data__executeImport____title": "Importação", "@sage/xtrem-import-export/pages__import_data__fileBlock____title": "arquivo CSV", "@sage/xtrem-import-export/pages__import_data__fileField____title": "Importar resultado", "@sage/xtrem-import-export/pages__import_data__generalSection____title": "<PERSON><PERSON>", "@sage/xtrem-import-export/pages__import_data__importAction____title": "Importação", "@sage/xtrem-import-export/pages__import_data__importData____columns__title__filename": "Nome do ficheiro", "@sage/xtrem-import-export/pages__import_data__importData____columns__title__nodeName": "Nome do nó (node)", "@sage/xtrem-import-export/pages__import_data__importData____columns__title__template": "<PERSON><PERSON>", "@sage/xtrem-import-export/pages__import_data__importData____columns__title__templateDescription": "Descrição do modelo", "@sage/xtrem-import-export/pages__import_data__importData____columns__title__templateName": "Nome do modelo", "@sage/xtrem-import-export/pages__import_data__importData____title": "Ficheiros para importar", "@sage/xtrem-import-export/pages__import_data__importResults____columns__title__dryRun": "<PERSON>e", "@sage/xtrem-import-export/pages__import_data__importResults____columns__title__endStamp": "Hora fim", "@sage/xtrem-import-export/pages__import_data__importResults____columns__title__filename": "<PERSON><PERSON>", "@sage/xtrem-import-export/pages__import_data__importResults____columns__title__generalError": "Detalhe do erro", "@sage/xtrem-import-export/pages__import_data__importResults____columns__title__numberOfRowsInError": "Quantidade de erros", "@sage/xtrem-import-export/pages__import_data__importResults____columns__title__rowsProcessed": "<PERSON><PERSON>", "@sage/xtrem-import-export/pages__import_data__importResults____columns__title__startStamp": "Hora iní<PERSON>", "@sage/xtrem-import-export/pages__import_data__importResults____columns__title__status": "Status", "@sage/xtrem-import-export/pages__import_data__importResults____dropdownActions__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-import-export/pages__import_data__importResults____title": "Resul<PERSON><PERSON>", "@sage/xtrem-import-export/pages__import_data__insertOption____title": "Inserir", "@sage/xtrem-import-export/pages__import_data__maximumNumberOfErrors____title": "Número máximo de erros", "@sage/xtrem-import-export/pages__import_data__optionsBlock____title": "Opções", "@sage/xtrem-import-export/pages__import_data__refreshRelationMapping____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-import-export/pages__import_data__resultsSection____title": "Importar resultados", "@sage/xtrem-import-export/pages__import_data__test____title": "Importação de testes", "@sage/xtrem-import-export/pages__import_data__updateOption____title": "Atualização", "@sage/xtrem-import-export/pages__import_data_page__testImportAction____title": "<PERSON>e", "@sage/xtrem-import-export/pages__import_export_template____navigationPanel__listItem__line3__title": "Descrição", "@sage/xtrem-import-export/pages__import_export_template____navigationPanel__listItem__Line4__title": "Descrição", "@sage/xtrem-import-export/pages__import_export_template____navigationPanel__listItem__name__title": "Nome", "@sage/xtrem-import-export/pages__import_export_template____navigationPanel__listItem__title__title": "ID", "@sage/xtrem-import-export/pages__import_export_template____navigationPanel__listItem__titleRight__title": "Tipo", "@sage/xtrem-import-export/pages__import_export_template____navigationPanel__optionsMenu__title": "<PERSON><PERSON>", "@sage/xtrem-import-export/pages__import_export_template____navigationPanel__optionsMenu__title__2": "Modelos de importação/exportação", "@sage/xtrem-import-export/pages__import_export_template____navigationPanel__optionsMenu__title__3": "Modelos de importação", "@sage/xtrem-import-export/pages__import_export_template____navigationPanel__optionsMenu__title__4": "Modelos de exportação", "@sage/xtrem-import-export/pages__import_export_template____objectTypePlural": "Modelos de importação/exportação", "@sage/xtrem-import-export/pages__import_export_template____objectTypeSingular": "Modelo de importação/exportação", "@sage/xtrem-import-export/pages__import_export_template____title": "Modelo de importação/exportação", "@sage/xtrem-import-export/pages__import_export_template___id____title": "ID", "@sage/xtrem-import-export/pages__import_export_template__canImport____title": "Importar modelo", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__columns__currentProperty__name__title": "Fábrica", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__columns__currentProperty__name__title__2": "Fábrica-alvo", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__columns__parentProperty__name__title": "Propriedade principal (fábrica)", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__columns__parentProperty__name__title__2": "Propriedade principal (fabrica-alvo)", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__title__currentProperty__factory__name": "Fábrica", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__title__currentProperty__name": "Campo", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__title__currentProperty__targetFactory__name": "Fábrica-alvo", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__title__dataType": "Tipo de dados", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__title__description": "Descrição", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__title__factory__name": "Fábrica", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__title__isCustom": "Personalização", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__title__isExportOnly": "Apenas exportação", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__title__isMandatory": "Obrigatorio", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__title__locale": "Parametros regionais (Locale)", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__title__parentProperty__factory__name": "Fábrica principal", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__title__parentProperty__name": "<PERSON><PERSON><PERSON>ade principal", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__title__parentProperty__targetFactory__name": "Fábrica-alvo principal", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__title__path": "<PERSON><PERSON><PERSON>", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____columns__title__targetFactory__name": "Fábrica-alvo", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____dropdownActions__title": "Eliminar", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____inlineActions__title": "Mover para cima", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____inlineActions__title__2": "Mover para baixo", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____inlineActions__title__3": "Inserir", "@sage/xtrem-import-export/pages__import_export_template__columnsTable____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-import-export/pages__import_export_template__columnsTableBlock____title": "Colunas do modelo", "@sage/xtrem-import-export/pages__import_export_template__copyToClipboard____title": "Copiar para clipbooard", "@sage/xtrem-import-export/pages__import_export_template__csvTemplateDisplay____helperText": "Código-fonte do modelo", "@sage/xtrem-import-export/pages__import_export_template__csvTemplateDisplay____title": "Modelo CSV", "@sage/xtrem-import-export/pages__import_export_template__customSave____title": "Guardar", "@sage/xtrem-import-export/pages__import_export_template__dateFormat____title": "Form. data", "@sage/xtrem-import-export/pages__import_export_template__decimalPoint____title": "Ponto decimal", "@sage/xtrem-import-export/pages__import_export_template__defaultParametersBlock____title": "Opções do formato", "@sage/xtrem-import-export/pages__import_export_template__delimiter____title": "Delimitador", "@sage/xtrem-import-export/pages__import_export_template__description____title": "Descrição", "@sage/xtrem-import-export/pages__import_export_template__downloadSection____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-import-export/pages__import_export_template__downloadTemplate____title": "<PERSON><PERSON>", "@sage/xtrem-import-export/pages__import_export_template__downloadTemplateSection____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-import-export/pages__import_export_template__escapeCharacter____title": "Caractere de Escape (esc)", "@sage/xtrem-import-export/pages__import_export_template__exportData____title": "Importar dados", "@sage/xtrem-import-export/pages__import_export_template__exportSettingsBlock____title": "Configurações de exportação", "@sage/xtrem-import-export/pages__import_export_template__exportTemplatePreviewBlock____title": "Modelo de exportação", "@sage/xtrem-import-export/pages__import_export_template__fileField____title": "Importar resultado", "@sage/xtrem-import-export/pages__import_export_template__fillGridFromClipboard____title": "Copiar a área de transferência para a grade", "@sage/xtrem-import-export/pages__import_export_template__generalSection____title": "G<PERSON>", "@sage/xtrem-import-export/pages__import_export_template__generateTemplate____title": "<PERSON><PERSON>r um modelo", "@sage/xtrem-import-export/pages__import_export_template__hasDataTypeLine____title": "Tipo de dados", "@sage/xtrem-import-export/pages__import_export_template__hasDescriptionLine____title": "Descrição", "@sage/xtrem-import-export/pages__import_export_template__hasLocaleLine____title": "Parametros regionais (Locale)", "@sage/xtrem-import-export/pages__import_export_template__historySection____title": "Histórico de importação", "@sage/xtrem-import-export/pages__import_export_template__id____title": "ID", "@sage/xtrem-import-export/pages__import_export_template__importData____title": "Importar dados", "@sage/xtrem-import-export/pages__import_export_template__importResults____columns__title__dryRun": "<PERSON>e", "@sage/xtrem-import-export/pages__import_export_template__importResults____columns__title__endStamp": "Carimbo final (timestamp)", "@sage/xtrem-import-export/pages__import_export_template__importResults____columns__title__filename": "Nome do ficheiro", "@sage/xtrem-import-export/pages__import_export_template__importResults____columns__title__numberOfRowsInError": "Linhas com erro", "@sage/xtrem-import-export/pages__import_export_template__importResults____columns__title__rowsProcessed": "<PERSON><PERSON>", "@sage/xtrem-import-export/pages__import_export_template__importResults____columns__title__startStamp": "In<PERSON>ar <PERSON>", "@sage/xtrem-import-export/pages__import_export_template__importResults____columns__title__status": "Status", "@sage/xtrem-import-export/pages__import_export_template__importResults____dropdownActions__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-import-export/pages__import_export_template__importResults____title": "Resul<PERSON><PERSON>", "@sage/xtrem-import-export/pages__import_export_template__importSettingsBlock____title": "Configurações de importação", "@sage/xtrem-import-export/pages__import_export_template__isActive____title": "Ativo", "@sage/xtrem-import-export/pages__import_export_template__isDefault____title": "Modelo predefinido para exportação", "@sage/xtrem-import-export/pages__import_export_template__name____title": "Nome", "@sage/xtrem-import-export/pages__import_export_template__nodeName____title": "Nome do nó (node)", "@sage/xtrem-import-export/pages__import_export_template__nodeProperty____columns__title__factory__name": "Fábrica", "@sage/xtrem-import-export/pages__import_export_template__nodeProperty____columns__title__targetFactory__name": "Fábrica-alvo", "@sage/xtrem-import-export/pages__import_export_template__nodeProperty____title": "Selecionar para adicionar o campo", "@sage/xtrem-import-export/pages__import_export_template__omitDataType____title": "Omitir o tipo de dados", "@sage/xtrem-import-export/pages__import_export_template__omitDescription____title": "Omitir a descrição", "@sage/xtrem-import-export/pages__import_export_template__quoteCharacter____title": "Caractere de Aspas (\")", "@sage/xtrem-import-export/pages__import_export_template__refreshRelationMapping____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-import-export/pages__import_export_template__resetGrid____title": "Repor a grade", "@sage/xtrem-import-export/pages__import_export_template__templateUse____title": "Utilização do modelo", "@sage/xtrem-import-export/pages__import_export_template_type__export": "Exportar", "@sage/xtrem-import-export/pages__import_export_template_type__import_and_export": "Importação e exportação", "@sage/xtrem-import-export/pages__import_template__setupId____title": "ID", "@sage/xtrem-import-export/permission__batch_import__name": "Importação \"Batch\"", "@sage/xtrem-import-export/permission__manage__name": "Gestão", "@sage/xtrem-import-export/permission__read__name": "<PERSON>r", "@sage/xtrem-import-export/select-file": "Selecione um arquivo CSV", "@sage/xtrem-import-export/validation-error": "Erro de validação"}