import { asyncArray } from '@sage/xtrem-async-helper';
import { ExtractEdgesPartial, integer } from '@sage/xtrem-client';
import { DisplayedPropertyType, GraphApi, ImportResult } from '@sage/xtrem-import-export-api';
import { MetaNodeFactory, MetaNodeProperty, MetaPropertyType } from '@sage/xtrem-metadata-api';
import {
    setOrderOfPageBusinessActions,
    setOrderOfPageHeaderDropDownActions,
    setOrderOfPageTableHeaderBusinessActions,
} from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import { colorfulPillPattern } from '@sage/xtrem-system/build/lib/client-functions/color-pattern';
import * as ui from '@sage/xtrem-ui';
import type { MonacoPluginProperties } from '@sage/xtrem-ui-plugin-monaco';
import { camelCase } from 'lodash';
import { importMenuItem } from '../menu-items/import-menu-item';
import { NodeListResultElement } from '../shared-functions';

interface CsvTemplateContent {
    _id: string;
    path: string;
    dataType: string;
    description: string;
    locale: string;
    isCustom: boolean;
    parentNonVitalReferencePath: string;
}
interface CsvTemplateContentTechnical extends CsvTemplateContent {
    rank: integer;
    groupLevel: integer;
    displayedPropertyType: DisplayedPropertyType;
    isMandatory: boolean;
    currentProperty: ExtractEdgesPartial<MetaNodeProperty>;
    isExportOnly: boolean;
    parentProperty: ExtractEdgesPartial<MetaNodeProperty>;
    parentPropertyPath: string;
    parentPropertyRowId: string;
}
const pushToParentProperties = (
    parentProperties: {
        parentProperty: ExtractEdgesPartial<MetaNodeProperty>;
        parentPropertyFullPath: string;
        parentPropertyRowId: string;
    }[],
    newParentProperty: ExtractEdgesPartial<MetaNodeProperty>,
    newParentPropertyFullPath: string,
    newParentPropertyRowId: string,
) => {
    if (!newParentProperty) return;
    const notFound = !parentProperties.find(
        elt =>
            elt.parentProperty?.factory?.name === newParentProperty.factory?.name &&
            elt.parentProperty?.targetFactory?.name === newParentProperty.targetFactory?.name &&
            elt.parentProperty.name === newParentProperty.name &&
            elt.parentPropertyFullPath === newParentPropertyFullPath &&
            elt.parentPropertyRowId === newParentPropertyRowId,
    );
    if (notFound)
        parentProperties.push({
            parentProperty: newParentProperty,
            parentPropertyFullPath: newParentPropertyFullPath,
            parentPropertyRowId: newParentPropertyRowId,
        });
};

const getDisplayedPropertyType = (metaNodeProperty: ExtractEdgesPartial<MetaNodeProperty>): DisplayedPropertyType => {
    if (metaNodeProperty.isVital) return metaNodeProperty.type === 'collection' ? 'vitalCollection' : 'vitalReference';
    if (metaNodeProperty.type === 'reference') return 'reference';

    return 'normalProperty';
};

let defaultDelimiter = ';';

@ui.decorators.page<ImportExportTemplate>({
    title: 'Import and Export template',
    objectTypeSingular: 'Import and Export template',
    objectTypePlural: 'Import and Export templates',
    node: '@sage/xtrem-import-export/ImportExportTemplate',
    module: 'importexport',
    menuItem: importMenuItem,
    priority: 10,
    mode: 'tabs',
    createAction() {
        return this.$standardNewAction;
    },
    headerDropDownActions() {
        return setOrderOfPageHeaderDropDownActions({
            remove: [this.$standardDeleteAction],
            dropDownBusinessActions: [this.$standardOpenRecordHistoryAction],
        });
    },
    async onLoad() {
        await this.getDefaultUserPreferences();
        await this.setUserPreferences();
        if (this.$.queryParameters._id) {
            this.nodeName.isDisabled = true;
            this.$.loader.isHidden = false;
            const templateUse = this.templateUse.value ?? '';
            this.manageIsDefaultSwitch(templateUse === 'importOnly');
            await this.loadImportResults();
            // Filling the transient columnsTable grid with values from the csvTemplate:
            await this.fillColumnsTableFromCsvTemplate(1, true);
            this.$.loader.isHidden = true;
            this.setCsvTemplateDisplay();
            // Read mode
            this.enablePageActions({
                add: true,
                cancel: false,
                delete: true,
                save: false,
                generateTemplate: this.csvTemplate.value && this.csvTemplate.value.length > 0,
                importData: ['importOnly', 'importAndExport'].includes(templateUse),
            });
            this.nodeName.isReadOnly = true;
            this.$.setPageClean();
        } else {
            // The page is empty: Creation mode
            this.nodeName.isDisabled = false;
            this.enablePageActions({
                add: true,
                cancel: false,
                delete: false,
                save: false,
                generateTemplate: false,
                importData: false,
            });
        }
        this.decimalPoint.value = this.defaultParameters.value
            ? JSON.parse(this.defaultParameters.value).decimalPoint
            : '.';
        this.quoteCharacter.value = this.defaultParameters.value
            ? JSON.parse(this.defaultParameters.value).quoteCharacter
            : "'";
        this.escapeCharacter.value = this.defaultParameters.value
            ? JSON.parse(this.defaultParameters.value).escapeCharacter
            : '\\';

        this.xtremNodeList = await this.loadXtremNodes();
        this.nodeName.options = this.xtremNodeList.map(node => node.name);
    },
    navigationPanel: {
        listItem: {
            name: ui.nestedFields.text({ bind: 'name', title: 'Name' }),
            title: ui.nestedFields.text({ bind: 'id', title: 'ID' }),
            line2: ui.nestedFields.text({ bind: 'nodeName' }),
            line3: ui.nestedFields.text({
                bind: 'description',
                title: 'Description',
            }),
            titleRight: ui.nestedFields.label({
                bind: 'templateUse',
                title: 'Type',
                optionType: '@sage/xtrem-import-export/TemplateUse',
                isMandatory: true,
                backgroundColor(value) {
                    if (value) {
                        return colorfulPillPattern.filledInformation.backgroundColor;
                    }
                    return colorfulPillPattern.filledNeutral.backgroundColor;
                },
                borderColor(value) {
                    if (value) {
                        return colorfulPillPattern.filledInformation.borderColor;
                    }
                    return colorfulPillPattern.filledNeutral.borderColor;
                },
                color(value) {
                    if (value) {
                        return colorfulPillPattern.filledInformation.textColor;
                    }
                    return colorfulPillPattern.filledNeutral.textColor;
                },
            }),
        },
        optionsMenu: [
            { title: 'All', graphQLFilter: {} },
            {
                title: 'Import and export templates',
                graphQLFilter: { templateUse: 'importAndExport' },
            },
            {
                title: 'Import templates',
                graphQLFilter: { templateUse: 'importOnly' },
            },
            {
                title: 'Export templates',
                graphQLFilter: { templateUse: 'exportOnly' },
            },
        ],
    },

    onDirtyStateUpdated(isPageDirty: boolean) {
        if (isPageDirty) {
            // Update mode
            this.enablePageActions({
                add: false,
                cancel: true,
                delete: false,
                save: true,
                generateTemplate: !!(this.csvTemplate.value && this.csvTemplate.value.length > 0),
                importData: false,
            });
        } else {
            // The user cancelled the previous operation
            this.enablePageActions({
                add: true,
                cancel: false,
                delete: !!this.$.queryParameters._id,
                save: false,
                generateTemplate: !!(this.csvTemplate.value && this.csvTemplate.value.length > 0),
                importData: ['importOnly', 'importAndExport'].includes(this.templateUse.value || ''),
            });
        }
    },
    businessActions() {
        return setOrderOfPageBusinessActions({
            save: this.customSave,
            cancel: this.$standardCancelAction,
            businessActions: [this.generateTemplate, this.importData, this.exportData],
        });
    },
})
export class ImportExportTemplate extends ui.Page<GraphApi> {
    xtremNodeList: NodeListResultElement[];

    @ui.decorators.section<ImportExportTemplate>({
        title: 'General',
        isTitleHidden: true,
    })
    generalSection: ui.containers.Section;

    @ui.decorators.block<ImportExportTemplate>({
        parent() {
            return this.generalSection;
        },
    })
    switchBlock: ui.containers.Block;

    @ui.decorators.block<ImportExportTemplate>({
        parent() {
            return this.generalSection;
        },
    })
    mainBlock: ui.containers.Block;

    @ui.decorators.switchField<ImportExportTemplate>({
        parent() {
            return this.switchBlock;
        },
        title: 'Active',
    })
    isActive: ui.fields.Switch;

    @ui.decorators.switchField<ImportExportTemplate>({
        parent() {
            return this.switchBlock;
        },
        title: 'Default template for export',
    })
    isDefault: ui.fields.Switch;

    @ui.decorators.textField<ImportExportTemplate>({
        parent() {
            return this.mainBlock;
        },
        title: '_id',
        isHidden: true,
    })
    _id: ui.fields.Text;

    @ui.decorators.selectField<ImportExportTemplate>({
        parent() {
            return this.mainBlock;
        },
        title: 'Node name',
        isMandatory: true,
        async onChange() {
            await this.getDefaultCsvTemplate(true);
        },
        fetchesDefaults: true,
    })
    nodeName: ui.fields.Select;

    @ui.decorators.textField<ImportExportTemplate>({
        parent() {
            return this.mainBlock;
        },
        title: 'Name',
        isMandatory: true,
    })
    name: ui.fields.Text;

    @ui.decorators.textField<ImportExportTemplate>({
        parent() {
            return this.mainBlock;
        },
        title: 'ID',
        isMandatory: true,
    })
    id: ui.fields.Text;

    @ui.decorators.dropdownListField<ImportExportTemplate>({
        parent() {
            return this.mainBlock;
        },
        title: 'Template use',
        optionType: '@sage/xtrem-import-export/TemplateUse',
        async onChange() {
            const templateUse = this.templateUse.value;
            this.manageIsDefaultSwitch(templateUse === 'importOnly');
        },
    })
    templateUse: ui.fields.DropdownList;

    @ui.decorators.textField<ImportExportTemplate>({
        parent() {
            return this.mainBlock;
        },
        title: 'Description',
        isFullWidth: true,
    })
    description: ui.fields.Text;

    @ui.decorators.block<ImportExportTemplate>({
        parent() {
            return this.generalSection;
        },
        title: 'Formatting options',
        isHidden: true,
    })
    defaultParametersBlock: ui.containers.Block;

    @ui.decorators.textField<ImportExportTemplate>({
        parent() {
            return this.defaultParametersBlock;
        },
        fetchesDefaults: true,
        isHidden: true,
    })
    defaultParameters: ui.fields.Text;

    @ui.decorators.textField<ImportExportTemplate>({
        parent() {
            return this.defaultParametersBlock;
        },
        title: 'Delimiter',
        isTransient: true,
    })
    delimiter: ui.fields.Text;

    @ui.decorators.textField<ImportExportTemplate>({
        parent() {
            return this.defaultParametersBlock;
        },
        title: 'Decimal point',
        isTransient: true,
    })
    decimalPoint: ui.fields.Text;

    @ui.decorators.textField<ImportExportTemplate>({
        parent() {
            return this.defaultParametersBlock;
        },
        title: 'Quote character',
        isTransient: true,
    })
    quoteCharacter: ui.fields.Text;

    @ui.decorators.textField<ImportExportTemplate>({
        parent() {
            return this.defaultParametersBlock;
        },
        title: 'Escape character',
        isTransient: true,
    })
    escapeCharacter: ui.fields.Text;

    @ui.decorators.block<ImportExportTemplate>({
        parent() {
            return this.generalSection;
        },
        title: 'Template columns',
    })
    columnsTableBlock: ui.containers.Block;

    @ui.decorators.referenceField<ImportExportTemplate, MetaNodeProperty>({
        title: 'Select to add field',
        node: '@sage/xtrem-metadata/MetaNodeProperty',
        valueField: 'name',
        isTransient: true,
        isHidden: true,
        parent() {
            return this.columnsTableBlock;
        },
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'type' }),
            ui.nestedFields.checkbox({ bind: 'isActive', isHidden: true }),
            ui.nestedFields.checkbox({ bind: 'isVital', isHidden: true }),
            ui.nestedFields.checkbox({ bind: 'isVitalParent', isHidden: true }),
            ui.nestedFields.checkbox({ bind: 'isStored', isHidden: true }),
            ui.nestedFields.checkbox({ bind: 'isRequired', isHidden: true }),
            ui.nestedFields.checkbox({ bind: 'isPublished', isHidden: true }),
            ui.nestedFields.checkbox({ bind: 'isNullable', isHidden: true }),
            ui.nestedFields.checkbox({ bind: 'isTransientInput', isHidden: true }),
            ui.nestedFields.reference<ImportExportTemplate, MetaNodeProperty, MetaNodeFactory>({
                title: 'Factory',
                node: '@sage/xtrem-metadata/MetaNodeFactory',
                bind: 'factory',
                valueField: 'name',
                columns: [ui.nestedFields.text({ bind: 'name' })],
            }),
            ui.nestedFields.reference<ImportExportTemplate, MetaNodeProperty, MetaNodeFactory>({
                title: 'Target factory',
                node: '@sage/xtrem-metadata/MetaNodeFactory',
                bind: 'targetFactory',
                valueField: 'name',
                columns: [ui.nestedFields.text({ bind: 'name' })],
            }),
        ],
        onCloseLookupDialog() {},
        async onChange() {
            if (this._isNewLineInserted) {
                this.$.loader.isHidden = false;
                this._newLineInsertedValue.currentProperty = {
                    name: this.nodeProperty.value?.name,
                    factory: {
                        name: this.nodeProperty.value?.factory?.name,
                    },
                    targetFactory: {
                        name: this.nodeProperty.value?.targetFactory?.name,
                    },
                };
                const technicalInformation = await this._getTechnicalInformation(
                    this.nodeProperty.value,
                    this._newLineInsertedValue.groupLevel || 0,
                    this._newLineInsertedValue.parentProperty,
                    this._newLineInsertedValue.isExportOnly,
                );
                this._newLineInsertedValue.displayedPropertyType =
                    technicalInformation.displayedPropertyType || 'normalProperty';
                this._newLineInsertedValue.groupLevel = this._getGroupLevel(this._newLineInsertedValue);
                this._newLineInsertedValue.isMandatory = this.nodeProperty.value?.isRequired;
                this._newLineInsertedValue.dataType = technicalInformation.dataType || this.nodeProperty.value?.type;
                this._newLineInsertedValue.description = technicalInformation.description || '';
                this._newLineInsertedValue.locale = technicalInformation.locale || '';
                this._newLineInsertedValue.isCustom = this._newLineInsertedValue.path?.startsWith('_customData(');

                if (this._newLineInsertedValue.currentProperty?.targetFactory?.name) {
                    this._newLineInsertedValue.parentPropertyRowId =
                        this._getRowId(
                            this._newLineInsertedValue.parentProperty?.name,
                            this._newLineInsertedValue.rank,
                        ) ||
                        this._newLineInsertedValue.parentPropertyRowId ||
                        '';
                    const _parentPropertyPath =
                        this._newLineInsertedValue.parentPropertyRowId &&
                        this.columnsTable.getRecordValue(this._newLineInsertedValue.parentPropertyRowId)
                            ?.parentPropertyPath;
                    this._newLineInsertedValue.parentPropertyPath = (
                        _parentPropertyPath ||
                        this._newLineInsertedValue.parentPropertyPath ||
                        ''
                    ).concat('.', this._newLineInsertedValue.currentProperty?.name || '');
                }
                this._newLineInsertedValue.path = technicalInformation.path || '';
                if (this._newLineInsertedValue.isExportOnly) {
                    this._newLineInsertedValue.path = this._newLineInsertedValue.parentPropertyPath;
                    if (
                        !['reference', 'collection', 'vitalReference', 'vitalCollection'].includes(
                            this._newLineInsertedValue.displayedPropertyType || 'normal',
                        )
                    ) {
                        this._newLineInsertedValue.path = this._newLineInsertedValue.path.concat(
                            '.',
                            this._newLineInsertedValue.currentProperty?.name || '',
                        );
                    }
                }

                // Setting the path without collisions
                const pathWithoutCollisions: Array<string> = [];
                const paths = [
                    ...this.columnsTable.value.map(line =>
                        this._getNameWithoutCollision(pathWithoutCollisions, line.path || ''),
                    ),
                ];
                this._newLineInsertedValue.path = this._getNameWithoutCollision(paths, this._newLineInsertedValue.path);

                this.columnsTable.addRecord(this._newLineInsertedValue);
                this.$.loader.isHidden = true;

                this.setCsvTemplateDisplay();
                this._isNewLineInserted = false;
                this._newLineInsertedValue = {};
            }
        },
        additionalLookupRecords() {
            if (!this.customColumnsTableArray.length) return [];

            const existingProperties =
                (this.columnsTable.value as ui.PartialCollectionValue<CsvTemplateContentTechnical>[]).map(
                    (elt: ui.PartialCollectionValue<CsvTemplateContentTechnical>) =>
                        `${elt.currentProperty?.factory?.name}.${elt.currentProperty?.name}`,
                ) || [];

            return this.customColumnsTableArray.filter(
                item => !existingProperties.includes(`${item.factory?.name}.${item.name}`),
            );
        },
    })
    nodeProperty: ui.fields.Reference<MetaNodeProperty>;

    _nodePropertyDefaultFilter = { _or: [{ isStored: true }, { isVital: true }], isVitalParent: false };

    _isNewLineInserted = false;

    _newLineInsertedValue: ui.PartialCollectionValue<CsvTemplateContentTechnical> = {};

    // eslint-disable-next-line class-methods-use-this
    _getNameWithoutCollision(names: Array<string>, name: string): string {
        for (let i = 0; ; i += 1) {
            const nameWithoutCollision = i === 0 ? name : `${name}#${i}`;
            if (!names.includes(nameWithoutCollision)) {
                names.push(nameWithoutCollision);
                return nameWithoutCollision;
            }
        }
    }

    async _getTechnicalInformation(
        property: ExtractEdgesPartial<MetaNodeProperty> | null,
        level = 1,
        parentProperty: ExtractEdgesPartial<MetaNodeProperty> | null = null,
        isExportOnly = false,
    ): Promise<{
        path: string;
        displayedPropertyType: DisplayedPropertyType | '';
        dataType: string;
        description: string;
        locale: string;
    }> {
        return this.$.graph
            .node('@sage/xtrem-import-export/ImportExportTemplate')
            .queries.getTechnicalInformation(
                { path: true, displayedPropertyType: true, dataType: true, description: true, locale: true },
                {
                    property: { name: property?.name || '', factory: { name: property?.factory?.name || '' } },
                    level,
                    parentProperty: {
                        name: parentProperty?.name || '',
                        factory: { name: parentProperty?.factory?.name || '' },
                    },
                    isExportOnly,
                },
            )
            .execute();
    }

    _getGroupLevel(rowItem: ui.PartialCollectionValue<CsvTemplateContentTechnical>) {
        // Unfortunately, the grid is not sorted accorded to the rank (after an insertion), so we need to work on a rank presorted grid
        const sortedGrid = this.columnsTable.value.sort((a, b) => (a.rank && b.rank ? a.rank - b.rank : 0));

        const previousRank = Math.max(
            ...(sortedGrid as ui.PartialCollectionValue<CsvTemplateContentTechnical>[])
                .filter(elt => elt.rank && elt.rank < (rowItem.rank || 0))
                .map(elt => elt.rank || 0),
        );
        const previousLine = (sortedGrid as ui.PartialCollectionValue<CsvTemplateContentTechnical>[]).find(
            elt => elt.rank && elt.rank === previousRank,
        );
        let groupLevel = previousLine ? previousLine.groupLevel || 0 : 0;
        // The vital references have already a group level defined
        if (['vitalReference', 'vitalCollection'].includes(rowItem.displayedPropertyType || '')) return groupLevel + 1;
        // The non vital references do not have a group level
        // When adding a property for a non vital reference, we need to set the group level, both for the non vital parent and for the current property
        // The tricky part is that we need to only do it once for the non vital parent (in case of adding multiple sub properties to it)
        if (previousLine && ['reference', 'collection'].includes(previousLine.displayedPropertyType || '')) {
            const saveGroupLevel = groupLevel;
            if (previousLine.groupLevel) {
                // This case may happen when we added a sub-property, but also in case of the non vital being part of a vital group level
                // We need to differentiate the 2 use cases, by searching if another line has the same parent property and with the same group level
                const hasSubProperties = (sortedGrid as ui.PartialCollectionValue<CsvTemplateContentTechnical>[]).some(
                    elt =>
                        elt.groupLevel === previousLine.groupLevel &&
                        elt.parentProperty?.name === previousLine.currentProperty?.name &&
                        elt.parentProperty?.factory?.name === previousLine.currentProperty?.targetFactory?.name,
                );
                if (!hasSubProperties) {
                    groupLevel += 1;
                }
            } else {
                groupLevel += 1;
            }
            // In case of increasing the groupLevel, we need to also set it on the non vital parent property
            if (saveGroupLevel !== groupLevel) {
                previousLine.groupLevel = groupLevel;
                this.columnsTable.addOrUpdateRecordValue(previousLine);
            }
        }
        return groupLevel;
    }

    async _getFieldsByFactory(
        factoryName: string,
        level = 1,
        withSortValue = false,
        isExportOnly = false,
    ): Promise<string[]> {
        const result = await this.$.graph
            .node('@sage/xtrem-import-export/ImportExportTemplate')
            .queries.getFieldsByFactory(true, {
                factoryName,
                level,
                withSortValue,
                isExportOnly,
            })
            .execute()
            .then((properties: string[]) => {
                return properties;
            })
            .catch(error => this.$.showToast(error.message, { timeout: 10000, type: 'error' }));

        return result || [];
    }

    @ui.decorators.tableField<ImportExportTemplate, CsvTemplateContentTechnical>({
        parent() {
            return this.columnsTableBlock;
        },
        title: 'Details',
        isTitleHidden: true,
        canAddNewLine: true,
        isPhantomRowDisabled: true,
        hasLineNumbers: true,
        canUserHideColumns: false,
        canFilter: true,
        canSelect: false,
        isHelperTextHidden: true,
        pageSize: 25,
        displayMode: ui.fields.TableDisplayMode.comfortable,
        canResizeColumns: true,
        isTransient: true,
        isChangeIndicatorDisabled: true,
        node: '@sage/xtrem-metadata/MetaNodeProperty',
        orderBy: {
            rank: +1,
        },
        columns: [
            ui.nestedFields.numeric({ bind: '_id', isHidden: true }),
            ui.nestedFields.numeric({ bind: 'rank', isHidden: true }),
            ui.nestedFields.text({ bind: 'displayedPropertyType', isHidden: false }),
            ui.nestedFields.numeric({ bind: 'groupLevel', isHidden: true }),
            ui.nestedFields.text({ bind: 'parentPropertyRowId', isHidden: true }),
            ui.nestedFields.text({ bind: 'parentPropertyPath', isHidden: true }),
            ui.nestedFields.reference<ImportExportTemplate, CsvTemplateContentTechnical, MetaNodeProperty>({
                title: 'Parent property',
                node: '@sage/xtrem-metadata/MetaNodeProperty',
                bind: 'parentProperty',
                valueField: 'name',
                isReadOnly: true,
                isHidden: true,
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.reference<ImportExportTemplate, MetaNodeProperty, MetaNodeFactory>({
                        title: 'Parent property factory',
                        node: '@sage/xtrem-metadata/MetaNodeFactory',
                        bind: 'factory',
                        valueField: 'name',
                    }),
                    ui.nestedFields.reference<ImportExportTemplate, MetaNodeProperty, MetaNodeFactory>({
                        title: 'Parent property target factory',
                        node: '@sage/xtrem-metadata/MetaNodeFactory',
                        bind: 'targetFactory',
                        valueField: 'name',
                    }),
                ],
            }),
            ui.nestedFields.reference<ImportExportTemplate, CsvTemplateContentTechnical, MetaNodeProperty>({
                title: 'Parent factory',
                node: '@sage/xtrem-metadata/MetaNodeProperty',
                bind: 'parentProperty',
                valueField: { factory: { name: true } },
                isHidden: true,
            }),
            ui.nestedFields.reference<ImportExportTemplate, CsvTemplateContentTechnical, MetaNodeProperty>({
                title: 'Parent target factory',
                node: '@sage/xtrem-metadata/MetaNodeProperty',
                bind: 'parentProperty',
                valueField: { targetFactory: { name: true } },
                isHidden: true,
            }),
            ui.nestedFields.label({
                bind: 'groupLevel',
                size: 'small',
                width: 'medium',
                map(_value, rowValue: ui.PartialCollectionValue<CsvTemplateContentTechnical>) {
                    const addGroupLevelName = (groupLevelName: string, factoryName: string): string => {
                        if (!groupLevelName) return factoryName;
                        return factoryName ? ''.concat(factoryName, '->', groupLevelName) : groupLevelName;
                    };
                    let groupLevel = '';
                    groupLevel = addGroupLevelName(groupLevel, rowValue.currentProperty?.targetFactory?.name || '');
                    groupLevel = addGroupLevelName(groupLevel, rowValue.currentProperty?.factory?.name || '');
                    groupLevel = addGroupLevelName(groupLevel, rowValue.parentProperty?.factory?.name || '');
                    // Let's search for the root parent property
                    if (rowValue.parentPropertyRowId && rowValue.parentPropertyRowId !== '') {
                        let parentRowValue: ui.PartialCollectionValue<CsvTemplateContentTechnical> | null =
                            this.columnsTable.getRecordValue(rowValue.parentPropertyRowId || '');
                        let continueSearch = !!parentRowValue;
                        while (continueSearch) {
                            continueSearch = false;
                            groupLevel = addGroupLevelName(
                                groupLevel,
                                parentRowValue?.parentProperty?.factory?.name || '',
                            );
                            if (parentRowValue?.parentPropertyRowId) {
                                parentRowValue = this.columnsTable.getRecordValue(parentRowValue.parentPropertyRowId);
                                continueSearch = !!parentRowValue?.parentPropertyRowId;
                            }
                        }
                    }
                    return rowValue.isCustom ? `${groupLevel} (Custom)` : groupLevel;
                },
            }),
            ui.nestedFields.reference<ImportExportTemplate, CsvTemplateContentTechnical, MetaNodeProperty>({
                title: 'Factory',
                node: '@sage/xtrem-metadata/MetaNodeProperty',
                bind: 'currentProperty',
                valueField: { factory: { name: true } },
                isHidden: true,
            }),
            ui.nestedFields.reference<ImportExportTemplate, CsvTemplateContentTechnical, MetaNodeProperty>({
                title: 'Target factory',
                node: '@sage/xtrem-metadata/MetaNodeProperty',
                bind: 'currentProperty',
                valueField: { targetFactory: { name: true } },
                isHidden: true,
            }),
            ui.nestedFields.checkbox({ bind: 'isMandatory', title: 'Mandatory', isReadOnly: true }),
            ui.nestedFields.checkbox({ bind: 'isCustom', title: 'Custom', isReadOnly: true }),
            ui.nestedFields.checkbox({ bind: 'isExportOnly', title: 'Export only', isReadOnly: true }),
            ui.nestedFields.reference<ImportExportTemplate, CsvTemplateContentTechnical, MetaNodeProperty>({
                title: 'Field',
                node: '@sage/xtrem-metadata/MetaNodeProperty',
                bind: 'currentProperty',
                valueField: 'name',
                isReadOnly: true,
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.reference<ImportExportTemplate, MetaNodeProperty, MetaNodeFactory>({
                        title: 'Factory',
                        node: '@sage/xtrem-metadata/MetaNodeFactory',
                        bind: 'factory',
                        valueField: 'name',
                    }),
                    ui.nestedFields.reference<ImportExportTemplate, MetaNodeProperty, MetaNodeFactory>({
                        title: 'Target factory',
                        node: '@sage/xtrem-metadata/MetaNodeFactory',
                        bind: 'targetFactory',
                        valueField: 'name',
                    }),
                ],
            }),
            ui.nestedFields.text({ title: 'Path', bind: 'path', isHidden: true }),
            ui.nestedFields.text({ title: 'Data type', bind: 'dataType', isReadOnly: true }),
            ui.nestedFields.text({ title: 'Description', bind: 'description', isReadOnly: true }),
            ui.nestedFields.text({ title: 'Locale', bind: 'locale' }),
        ],
        headerBusinessActions() {
            return setOrderOfPageTableHeaderBusinessActions({
                actions: [this.resetGrid],
            });
        },
        inlineActions: [
            {
                icon: 'arrow_up',
                title: 'Move up',
                async onClick(rowId: string) {
                    const { isDisabled, message } = this._checkCanMoveUp(rowId);
                    if (isDisabled) {
                        await this.$.dialog.message(
                            'error',
                            ui.localize(
                                '@sage/xtrem-import-export/page__import_export_template__moving_line_up_down',
                                'Move line up or down',
                            ),
                            message,
                        );
                        return;
                    }
                    const line = this.columnsTable.getRecordValue(rowId);
                    if (line && line.rank) {
                        const previousRank = Math.max(
                            ...(this.columnsTable.value as ui.PartialCollectionValue<CsvTemplateContentTechnical>[])
                                .map(elt => elt.rank || 0)
                                .filter(rank => rank < line.rank),
                        );
                        const previousLine = (
                            this.columnsTable.value as ui.PartialCollectionValue<CsvTemplateContentTechnical>[]
                        ).find(elt => elt.rank && elt.rank === previousRank);
                        if (previousLine) {
                            previousLine.rank = line.rank;
                            this.columnsTable.addOrUpdateRecordValue(previousLine);
                        }
                        line.rank = previousRank;
                        this.columnsTable.addOrUpdateRecordValue(line);
                        this.columnsTable.redraw();
                        // As the onChange is not triggered, we need to manually update the csv preview
                        this.setCsvTemplateDisplay();
                    }
                },
            },
            {
                icon: 'arrow_down',
                title: 'Move down',
                async onClick(rowId: string) {
                    const { isDisabled, message } = this._checkCanMoveDown(rowId);
                    if (isDisabled) {
                        await this.$.dialog.message(
                            'error',
                            ui.localize(
                                '@sage/xtrem-import-export/page__import_export_template__moving_line_up_down',
                                'Move line up or down',
                            ),
                            message,
                        );
                        return;
                    }
                    const line = this.columnsTable.getRecordValue(rowId);
                    if (line && line.rank) {
                        const nextRank = Math.min(
                            ...(this.columnsTable.value as ui.PartialCollectionValue<CsvTemplateContentTechnical>[])
                                .map(elt => elt.rank || 0)
                                .filter(rank => rank > line.rank),
                        );
                        const nextLine = (
                            this.columnsTable.value as ui.PartialCollectionValue<CsvTemplateContentTechnical>[]
                        ).find(elt => elt.rank && elt.rank === nextRank);
                        if (nextLine) {
                            nextLine.rank = line.rank;
                            this.columnsTable.addOrUpdateRecordValue(nextLine);
                        }
                        line.rank = nextRank;
                        this.columnsTable.addOrUpdateRecordValue(line);
                        this.columnsTable.redraw();
                        // As the onChange is not triggered, we need to manually update the csv preview
                        this.setCsvTemplateDisplay();
                    }
                },
            },
            {
                icon: 'plus',
                title: 'Insert',
                async onClick(rowId: string) {
                    const line = this.columnsTable.getRecordValue(rowId);
                    const newLine = {} as ui.PartialCollectionValue<CsvTemplateContentTechnical>;
                    if (line) {
                        newLine._id = Math.max(
                            ...(
                                this.columnsTable.value as ui.PartialCollectionValue<CsvTemplateContentTechnical>[]
                            ).map(elt => (elt._id && +elt._id + 10) || 0),
                        ).toString();
                        // Getting the next rank after the current line so that we can assign a new value in between for the inserted line
                        const nextRank = Math.min(
                            ...(this.columnsTable.value as ui.PartialCollectionValue<CsvTemplateContentTechnical>[])
                                .map(elt => elt.rank || Number.MAX_SAFE_INTEGER)
                                .filter(rank => rank > (line.rank || 0)),
                        );
                        newLine.rank = (line.rank || 0) + Math.round((nextRank - (line.rank || 0)) / 2);
                        newLine.currentProperty = {
                            name: '',
                            factory: {
                                name:
                                    line.currentProperty?.targetFactory && line.currentProperty?.targetFactory.name
                                        ? line.currentProperty?.targetFactory.name
                                        : line.currentProperty?.factory?.name,
                            },
                            targetFactory: {},
                        };
                        newLine.parentPropertyPath = line.parentPropertyPath || line.currentProperty?.name;
                        newLine.parentPropertyRowId = line.parentPropertyRowId || line._id;
                        // Get existing fields in the grid for the factory
                        const existingFields =
                            (this.columnsTable.value as ui.PartialCollectionValue<CsvTemplateContentTechnical>[])
                                .filter(
                                    (elt: ui.PartialCollectionValue<CsvTemplateContentTechnical>) =>
                                        elt.currentProperty?.factory?.name === newLine.currentProperty?.factory?.name &&
                                        (!elt.isExportOnly ||
                                            (elt.isExportOnly &&
                                                ((!elt.parentPropertyPath && !newLine.parentPropertyPath) ||
                                                    !!(
                                                        elt.parentPropertyPath &&
                                                        newLine.parentPropertyPath &&
                                                        elt.parentPropertyPath === newLine.parentPropertyPath
                                                    ) ||
                                                    !!(
                                                        elt.isExportOnly &&
                                                        elt.parentProperty?.name ===
                                                            (line.parentProperty?.name || line.currentProperty?.name)
                                                    )))),
                                )
                                .map(
                                    (elt: ui.PartialCollectionValue<CsvTemplateContentTechnical>) =>
                                        elt.currentProperty?.name,
                                ) || [];

                        // Get all fields for the factory
                        // We do it this way, because this list will have only active fields, taking service options etc into account
                        // We limit the lookup list to these remaining fields
                        const factoryFields = await this._getFieldsByFactory(
                            newLine.currentProperty?.factory?.name || '',
                            1,
                            true,
                            line.isExportOnly || line.displayedPropertyType === 'reference',
                        );
                        const remainingFields = factoryFields.filter(property => !existingFields.includes(property));

                        this.nodeProperty.filter = {
                            ...this._nodePropertyDefaultFilter,
                            factory: { name: newLine?.currentProperty?.factory?.name },
                            name: {
                                _in: remainingFields,
                            },
                        };

                        this._isNewLineInserted = true;
                        this._newLineInsertedValue = newLine;
                        // Inserted properties from a non vital reference/collection or an already export only property will be flagged as 'isExportOnly'
                        this._newLineInsertedValue.isExportOnly =
                            (line.displayedPropertyType &&
                                ['reference', 'collection'].includes(line.displayedPropertyType)) ||
                            line.isExportOnly;
                        if (this._newLineInsertedValue.isExportOnly) {
                            this._newLineInsertedValue.parentProperty = line.currentProperty;
                            // In case the line is inserted from one bearing a non ref property
                            if (line.displayedPropertyType === 'normalProperty') {
                                this._newLineInsertedValue.parentProperty = line.parentProperty;
                            }
                        }
                        this.nodeProperty.openDialog();
                        // Inserting the new line will be done in the onChange of the nodeProperty, based on the boolean isNewLineInserted
                    }
                },
                isDisabled(_recordId: string, rowItem: ui.PartialCollectionValue<CsvTemplateContentTechnical>) {
                    return (
                        this.templateUse.value === 'importOnly' &&
                        ['reference', 'collection'].includes(rowItem.displayedPropertyType || '')
                    );
                },
            },
        ],
        onChange() {
            this.setCsvTemplateDisplay();
        },
        dropdownActions: [
            {
                icon: 'bin',
                title: 'Delete',
                isDestructive: true,
                async onClick(rowId: string, rowItem: ui.PartialCollectionValue<CsvTemplateContentTechnical>) {
                    // If removing a vital reference/collection main key, a confirmation is asked and all related lines will be removed
                    let removeAllRelated = false;
                    if (
                        rowItem.displayedPropertyType &&
                        ['vitalCollection', 'vitalReference', 'reference', 'collection'].includes(
                            rowItem.displayedPropertyType,
                        )
                    ) {
                        const options: ui.dialogs.DialogOptions = {
                            acceptButton: {
                                text: 'Continue',
                            },
                            cancelButton: {
                                text: 'Cancel',
                            },
                        };
                        removeAllRelated = await this.$.dialog
                            .confirmation(
                                'warn',
                                ui.localize(
                                    '@sage/xtrem-import-export/page__import_export_template__confirm_deletion',
                                    'Confirm deletion',
                                ),
                                ui.localize(
                                    '@sage/xtrem-import-export/page__import_export_template__confirm_deletion_of_sublevel',
                                    'You are about to delete a sublevel and all its entries. Continue?',
                                ),
                                options,
                            )
                            .then(() => true)
                            .catch(err => {
                                if (err) {
                                    throw err;
                                }
                                return false;
                            });

                        if (removeAllRelated) {
                            this.$.loader.isHidden = false;
                            const idsToRemove = this._getRelatedIdsToRemove(rowItem);
                            idsToRemove.forEach(id => this.columnsTable.removeRecord(id));
                            this.columnsTable.removeRecord(rowId);
                            this.setCsvTemplateDisplay();
                            this.$.loader.isHidden = true;
                        }
                    } else {
                        this.columnsTable.removeRecord(rowId);
                        this.setCsvTemplateDisplay();
                    }
                },
            },
        ],
        sidebar: {
            headerDropdownActions: [],
            headerQuickActions: [],
            title(_id, recordValue) {
                if (!recordValue || +recordValue._id < 0) {
                    return ui.localize('@sage/xtrem-import-export/edit-create-line', 'Insert template column');
                }
                return `${recordValue.path} - ${recordValue.description}`;
            },
            layout() {
                return {
                    mainSection: {
                        title: 'Property',
                        blocks: {
                            mainBlock: {
                                fields: ['path', 'dataType', 'description', 'locale'],
                            },
                        },
                    },
                };
            },
        },
    })
    columnsTable: ui.fields.Table<CsvTemplateContentTechnical>;

    _getRelatedIdsToRemove(rowItem: ui.PartialCollectionValue<CsvTemplateContentTechnical>) {
        if (!rowItem.currentProperty?.targetFactory?.name) return [];
        const ids: Array<string> = [];
        const factoryToDelete = rowItem.currentProperty?.targetFactory?.name;
        const factoriesToDelete: Array<string> = [];
        let currentFactory = rowItem.currentProperty?.targetFactory?.name;
        // Unfortunately, the grid is not sorted accorded to the rank (after an insertion), so we need to work on a rank presorted grid
        const sortedGrid = this.columnsTable.value.sort((a, b) => (a.rank && b.rank ? a.rank - b.rank : 0));
        // Selecting all the next lines that are related to the current target factory
        let nextRank = rowItem.rank || -1;
        if (nextRank > 0) {
            let continueSearch = true;
            while (continueSearch) {
                // eslint-disable-next-line @typescript-eslint/no-loop-func
                const line = sortedGrid.find(elt => elt.rank && elt.rank > nextRank);
                if (line?.currentProperty?.factory?.name === currentFactory) {
                    if (line.rank) {
                        nextRank = line.rank;
                    } else {
                        continueSearch = false;
                    }
                    ids.push(line._id);
                    if (
                        line.currentProperty?.targetFactory?.name &&
                        line.displayedPropertyType &&
                        ['vitalReference', 'vitalCollection', 'reference', 'collection'].includes(
                            line.displayedPropertyType,
                        )
                    ) {
                        // Next properties are on a vital sub-level, we need to also remove them
                        factoriesToDelete.push(currentFactory);
                        factoriesToDelete.push(line.currentProperty?.targetFactory.name);
                        currentFactory = line.currentProperty?.targetFactory.name;
                    }
                } else {
                    continueSearch = false;
                    // Once done with the eventual vitals of the sub-level, we come back to each factoriesToDelete
                    // Use case: a vital node having multiple vitals on the same group level
                    // If all properties were added, the loop will self-stop
                    if (currentFactory !== factoryToDelete) {
                        continueSearch = true;
                        currentFactory = factoriesToDelete.pop() || '';
                        if (currentFactory === '') continueSearch = false;
                    }
                }
            }
        }
        return ids;
    }

    _checkCanMoveUp(rowId: string): { isDisabled: boolean; message: string } {
        const rowItem: ui.PartialCollectionValue<CsvTemplateContentTechnical> | null =
            this.columnsTable.getRecordValue(rowId);
        if (!rowItem)
            return {
                isDisabled: false,
                message: ui.localize(
                    '@sage/xtrem-import-export/page__import_export_template__line_values_not_found',
                    'Line values not found.',
                ),
            };
        // We don't allow the following cases:
        // - main key is always on the first line, therefor the second line cannot be moved up
        // - a line from a sub-level cannot be moved above the sub-level's main key
        // - a sub-level main key line cannot be moved up or down (to avoid complexity)
        const previousRank = Math.max(
            ...(this.columnsTable.value as ui.PartialCollectionValue<CsvTemplateContentTechnical>[])
                .map(elt => elt.rank || 0)
                .filter(rank => rank < (rowItem.rank || 0)),
        );
        const previousLine = (this.columnsTable.value as ui.PartialCollectionValue<CsvTemplateContentTechnical>[]).find(
            elt => elt.rank && elt.rank === previousRank,
        );
        if (
            previousLine &&
            previousLine.currentProperty?.factory?.name &&
            rowItem.currentProperty?.factory?.name &&
            ['vitalReference', 'vitalCollection', 'reference', 'collection'].includes(
                previousLine.displayedPropertyType || '',
            ) &&
            (previousLine.currentProperty?.factory?.name !== rowItem.currentProperty?.factory?.name ||
                previousLine.currentProperty?.targetFactory?.name)
        )
            return {
                isDisabled: true,
                message: ui.localize(
                    '@sage/xtrem-import-export/page__import_export_template__cannot_move_outside_parent_group',
                    'You need to keep the line within its parent group.',
                ),
            };
        const isSubLevelMainKey = !!(
            rowItem.groupLevel &&
            rowItem.groupLevel > 0 &&
            rowItem.currentProperty?.factory?.name &&
            rowItem.currentProperty?.targetFactory?.name &&
            rowItem.displayedPropertyType &&
            ['vitalReference', 'vitalCollection', 'reference', 'collection'].includes(rowItem.displayedPropertyType)
        );
        if (isSubLevelMainKey) {
            return {
                isDisabled: true,
                message: ui.localize(
                    '@sage/xtrem-import-export/page__import_export_template__cannot_move_group_main_key',
                    'The current line is the main key for the group. You cannot move it.',
                ),
            };
        }
        if (
            rowItem.rank === 0 ||
            rowItem.rank ===
                Math.min(
                    ...(this.columnsTable.value as ui.PartialCollectionValue<CsvTemplateContentTechnical>[]).map(elt =>
                        elt.rank && elt.rank > 0 ? elt.rank : Number.MAX_SAFE_INTEGER,
                    ),
                )
        )
            return {
                isDisabled: true,
                message: ui.localize(
                    '@sage/xtrem-import-export/page__import_export_template__cannot_move_further',
                    'You cannot move the line any further.',
                ),
            };
        return { isDisabled: false, message: '' };
    }

    _checkCanMoveDown(rowId: string): { isDisabled: boolean; message: string } {
        const rowItem: ui.PartialCollectionValue<CsvTemplateContentTechnical> | null =
            this.columnsTable.getRecordValue(rowId);
        if (!rowItem)
            return {
                isDisabled: false,
                message: ui.localize(
                    '@sage/xtrem-import-export/page__import_export_template__line_values_not_found',
                    'Line values not found.',
                ),
            };
        // We don't allow the following cases:
        // - main key is always on the first line, therefor the first line cannot be moved down
        // - a line from a sub-level cannot be moved outside the sub-level's group of properties
        // - a sub-level main key line cannot be moved up or down (to avoid complexity)

        const nextRank = Math.min(
            ...(this.columnsTable.value as ui.PartialCollectionValue<CsvTemplateContentTechnical>[])
                .map(elt => elt.rank || Number.MAX_SAFE_INTEGER)
                .filter(rank => rank > (rowItem.rank || 0)),
        );
        const nextLine = (this.columnsTable.value as ui.PartialCollectionValue<CsvTemplateContentTechnical>[]).find(
            elt => elt.rank && elt.rank === nextRank,
        );
        if (
            nextLine &&
            nextLine.currentProperty?.factory?.name &&
            rowItem.currentProperty?.factory?.name &&
            ['vitalReference', 'vitalCollection', 'reference', 'collection'].includes(
                nextLine.displayedPropertyType || '',
            ) &&
            (nextLine.currentProperty?.factory.name !== rowItem.currentProperty?.factory.name ||
                nextLine.currentProperty?.targetFactory?.name)
        )
            return {
                isDisabled: true,
                message: ui.localize(
                    '@sage/xtrem-import-export/page__import_export_template__cannot_move_outside_parent_group',
                    'You need to keep the line within its parent group.',
                ),
            };
        const isSubLevelMainKey = !!(
            rowItem.groupLevel &&
            rowItem.groupLevel > 0 &&
            rowItem.currentProperty?.factory?.name &&
            rowItem.currentProperty?.targetFactory?.name &&
            rowItem.displayedPropertyType &&
            ['vitalReference', 'vitalCollection', 'reference', 'collection'].includes(rowItem.displayedPropertyType)
        );
        if (isSubLevelMainKey) {
            return {
                isDisabled: true,
                message: ui.localize(
                    '@sage/xtrem-import-export/page__import_export_template__cannot_move_group_main_key',
                    'The current line is the main key for the group. You cannot move it.',
                ),
            };
        }
        if (
            rowItem.rank === 0 ||
            rowItem.rank ===
                Math.max(
                    ...(this.columnsTable.value as ui.PartialCollectionValue<CsvTemplateContentTechnical>[]).map(
                        elt => elt.rank || 0,
                    ),
                )
        )
            return {
                isDisabled: true,
                message: ui.localize(
                    '@sage/xtrem-import-export/page__import_export_template__cannot_move_further',
                    'You cannot move the line any further.',
                ),
            };
        return { isDisabled: false, message: '' };
    }

    @ui.decorators.block<ImportExportTemplate>({
        parent() {
            return this.generalSection;
        },
        title: 'Export template',
        width: 'extra-large',
    })
    exportTemplatePreviewBlock: ui.containers.Block;

    @ui.decorators.checkboxField<ImportExportTemplate>({
        parent() {
            return this.exportTemplatePreviewBlock;
        },
        title: 'Data type',
        isTransient: true,
        onChange() {
            this.setCsvTemplateDisplay();
        },
    })
    hasDataTypeLine: ui.fields.Checkbox;

    @ui.decorators.checkboxField<ImportExportTemplate>({
        parent() {
            return this.exportTemplatePreviewBlock;
        },
        title: 'Description',
        isTransient: true,
        onChange() {
            this.setCsvTemplateDisplay();
        },
    })
    hasDescriptionLine: ui.fields.Checkbox;

    @ui.decorators.checkboxField<ImportExportTemplate>({
        parent() {
            return this.exportTemplatePreviewBlock;
        },
        title: 'Locale',
        isTransient: true,
        onChange() {
            this.setCsvTemplateDisplay();
        },
    })
    hasLocaleLine: ui.fields.Checkbox;

    @ui.decorators.pluginField<ImportExportTemplate, MonacoPluginProperties>({
        isTransient: true,
        isReadOnly: true,
        title: 'CSV preview',
        isTitleHidden: true,
        helperText: 'Source code: Template',
        pluginPackage: '@sage/xtrem-ui-plugin-monaco',
        isFullWidth: true,
        language: 'text',
        height: 100,
        parent() {
            return this.exportTemplatePreviewBlock;
        },
    })
    csvTemplateDisplay: ui.fields.Plugin<MonacoPluginProperties>;

    @ui.decorators.textField<ImportExportTemplate>({
        parent() {
            return this.exportTemplatePreviewBlock;
        },
        isHidden: true,
    })
    csvTemplate: ui.fields.Text;

    @ui.decorators.pageAction<ImportExportTemplate>({
        title: 'Save',
        async onClick() {
            // The standard save action will call the getSerializedValues which will customize the payload to save
            await this.$standardSaveAction.execute(true);
            this.$.setPageClean();
            this.$.router.refresh();
        },
    })
    customSave: ui.PageAction;

    // This is called by the standard save action to allow re-working the payload
    getSerializedValues() {
        const { $detailPanel, ...values } = this.$.values;
        const sortedGrid = this.columnsTable.value.sort((a, b) => (a.rank && b.rank ? a.rank - b.rank : 0));
        values.csvTemplate = JSON.stringify({
            data: sortedGrid.map(
                line =>
                    ({
                        _id: line._id,
                        path: line.path,
                        dataType: line.dataType,
                        description: line.description,
                        locale: line.locale,
                        isCustom: line.isCustom,
                        parentNonVitalReferencePath: line.parentNonVitalReferencePath,
                    }) as CsvTemplateContent,
            ),
        });
        return values;
    }

    @ui.decorators.section<ImportExportTemplate>({
        isHidden: true,
        title: 'Download',
    })
    downloadTemplateSection: ui.containers.Section;

    @ui.decorators.block<ImportExportTemplate>({
        parent() {
            return this.downloadTemplateSection;
        },
    })
    downloadTemplateBlock: ui.containers.Block;

    @ui.decorators.fileField<ImportExportTemplate>({
        isTransient: true,
        fileTypes: 'text/plain',
        text: 'template.csv',
        title: 'Template',
        parent() {
            return this.downloadTemplateBlock;
        },
    })
    downloadTemplate: ui.fields.File;

    @ui.decorators.section<ImportExportTemplate>({
        title: 'Import history',
        isTitleHidden: true,
    })
    historySection: ui.containers.Section;

    @ui.decorators.pageAction<ImportExportTemplate>({
        icon: 'refresh',
        title: 'Refresh',
        async onClick() {
            await this.loadImportResults();
        },
    })
    refreshRelationMapping: ui.PageAction;

    @ui.decorators.tableField<ImportExportTemplate, ImportResult>({
        node: '@sage/xtrem-import-export/ImportResult',
        isTransient: true,
        canSelect: false,
        title: 'Results',
        parent() {
            return this.historySection;
        },
        columns: [
            ui.nestedFields.text<ImportExportTemplate>({
                bind: 'filename',
                title: 'File name',
                isReadOnly: true,
                canFilter: true,
            }),
            ui.nestedFields.label<ImportExportTemplate>({
                bind: 'startStamp',
                title: 'Start stamp',
                size: 'large',
                borderColor: ui.tokens.colorsYang100,
                map(_fieldValue) {
                    return `${_fieldValue ? new Date(_fieldValue).toLocaleString() : ''}`;
                },
                canFilter: true,
            }),
            ui.nestedFields.label<ImportExportTemplate>({
                bind: 'endStamp',
                title: 'End stamp',
                size: 'large',
                borderColor: ui.tokens.colorsYang100,
                map(_fieldValue) {
                    return `${_fieldValue ? new Date(_fieldValue).toLocaleString() : ''}`;
                },
                canFilter: true,
            }),
            ui.nestedFields.label<ImportExportTemplate>({
                bind: 'status',
                title: 'Status',
                size: 'large',
                optionType: '@sage/xtrem-import-export/ImportStatus',
                canFilter: true,
            }),
            ui.nestedFields.checkbox<ImportExportTemplate>({
                bind: 'dryRun',
                title: 'Test',
                isReadOnly: true,
                canFilter: true,
            }),
            ui.nestedFields.numeric<ImportExportTemplate>({
                bind: 'rowsProcessed',
                title: 'Rows processed',
                isReadOnly: true,
                canFilter: true,
            }),
            ui.nestedFields.numeric<ImportExportTemplate>({
                bind: 'numberOfRowsInError',
                title: 'Rows in error',
                isReadOnly: true,
                canFilter: true,
            }),
        ],
        dropdownActions: [
            {
                icon: 'download',
                title: 'Download',
                async onClick(_rowId: string, data: ui.PartialCollectionValue<ImportResult>) {
                    this.downloadSection.isHidden = false;
                    this.fileField.text = data.filename || '';
                    this.fileField.value = { value: btoa(unescape(encodeURIComponent(data.rowsInError?.value || ''))) };
                    await this.$.dialog.custom('info', this.downloadSection, {
                        size: 'medium',
                        resolveOnCancel: true,
                        cancelButton: { isHidden: false },
                        acceptButton: {
                            isHidden: false,
                        },
                    });
                    this.downloadSection.isHidden = true;
                },
            },
        ],
        fieldActions() {
            return [this.refreshRelationMapping];
        },
    })
    importResults: ui.fields.Table<ImportResult>;

    @ui.decorators.section<ImportExportTemplate>({
        isHidden: true,
        title: 'Download',
    })
    downloadSection: ui.containers.Section;

    @ui.decorators.block<ImportExportTemplate>({
        parent() {
            return this.downloadSection;
        },
    })
    downloadBlock: ui.containers.Block;

    @ui.decorators.fileField<ImportExportTemplate>({
        isTransient: true,
        fileTypes: 'application/json',
        text: 'fileName',
        title: 'Import Result',
        parent() {
            return this.downloadBlock;
        },
    })
    fileField: ui.fields.File;

    @ui.decorators.pageAction<ImportExportTemplate>({
        title: 'Generate template',
        async onError(error) {
            this.downloadTemplateSection.isHidden = true;
            if (error) this.$.showToast(error.message, { type: 'error' });
        },
        async onClick() {
            this.downloadTemplateSection.isHidden = false;
            const text = `template${this.name.value || this.nodeName.value || ''}`;
            this.downloadTemplate.text = `${camelCase(text)}.csv`;
            this.downloadTemplate.value = {
                value: btoa(unescape(encodeURIComponent(this.csvTemplateDisplay.value || ''))),
            };
            await this.$.dialog.custom('info', this.downloadTemplateSection, {
                size: 'medium',
                cancelButton: { isHidden: false },
                acceptButton: { isHidden: false },
            });
            this.downloadTemplateSection.isHidden = true;
        },
    })
    generateTemplate: ui.PageAction;

    @ui.decorators.pageAction<ImportExportTemplate>({
        title: 'Import data',
        async onClick() {
            this.$.router.goTo('@sage/xtrem-import-export/ImportData', {});
        },
    })
    importData: ui.PageAction;

    @ui.decorators.pageAction<ImportExportTemplate>({
        title: 'Export data',
        isHidden: true,
    })
    exportData: ui.PageAction;

    @ui.decorators.pageAction<ImportExportTemplate>({
        title: 'Reset grid',
        async onClick() {
            this.$.loader.isHidden = false;
            await this.getDefaultCsvTemplate(true);
            this.$.loader.isHidden = true;
        },
    })
    resetGrid: ui.PageAction;

    customColumnsTableArray: (ui.PartialCollectionValue<MetaNodeProperty> & { _id: string })[] = [];

    _generateColumnsTableArray(csvTemplateData: {
        csvTemplate: { value: string };
        technical: { value: string } | null;
        canImport: boolean;
        canExport: boolean;
    }): Partial<CsvTemplateContentTechnical>[] {
        let columnsTableArray: Partial<CsvTemplateContentTechnical>[] = [];
        const withTechnicalData = !!(csvTemplateData.technical && csvTemplateData.technical.value);
        if (csvTemplateData) {
            const templateTable = csvTemplateData.csvTemplate.value.split('\n');
            this.generateTemplate.isDisabled = false;
            if (csvTemplateData.canImport && csvTemplateData.canExport) {
                this.templateUse.isDisabled = false;
            } else if (csvTemplateData.canExport) {
                this.templateUse.value = 'exportOnly';
                this.templateUse.isDisabled = true;
            } else if (csvTemplateData.canImport) {
                this.templateUse.value = 'importOnly';
                this.templateUse.isDisabled = true;
            }

            const paths = templateTable[0].split(defaultDelimiter);
            const datatypes = templateTable[1].split(defaultDelimiter);
            const descriptions = templateTable[2].split(defaultDelimiter);
            const locales = templateTable[3].split(defaultDelimiter);

            columnsTableArray = Array.from({
                length: paths.length - 1,
            });
            const technicalColumnsTableArray: Partial<CsvTemplateContentTechnical>[] = withTechnicalData
                ? Array.from({
                      length: paths.length - 1,
                  })
                : [];
            for (let i = 0; i < paths.length - 1; i += 1) {
                columnsTableArray[i] = {
                    _id: (i * 10).toString(),
                    path: paths[i],
                    dataType: datatypes[i],
                    description: descriptions[i],
                    locale: locales[i],
                };
                if (withTechnicalData)
                    technicalColumnsTableArray[i] = {
                        _id: (i * 10).toString(),
                        ...this._setTechnicalValues(i, csvTemplateData, columnsTableArray, technicalColumnsTableArray),
                    };
                if (withTechnicalData) {
                    columnsTableArray[i] = { ...columnsTableArray[i], ...technicalColumnsTableArray[i] };
                }
            }
        }
        this.customColumnsTableArray = columnsTableArray
            .filter(column => column.path && column.path.startsWith('_customData('))
            .map(item => {
                return {
                    _id: `-${item._id}`,
                    path: item.path,
                    name: item.currentProperty?.name || '',
                    factory: {
                        name: item.currentProperty?.factory?.name || '',
                    },
                    type: item.dataType as unknown as MetaPropertyType,
                    displayedPropertyType: item.displayedPropertyType,
                    isVitalParent: false,
                    isStored: true,
                };
            });
        return columnsTableArray;
    }

    _getRowId = (parentPropertyName: string, currentRank: number): string => {
        let result = '';
        const possibleParentLines = this.columnsTable.value
            .filter(line => line.rank < currentRank && line.currentProperty?.name === parentPropertyName)
            .sort((a, b) => b.rank - a.rank);
        if (possibleParentLines.length) {
            result = possibleParentLines[0]._id;
        }
        return result;
    };

    async fillColumnsTableFromCsvTemplate(level = 1, withSortValue = false) {
        if (!this.csvTemplate.value) return;
        const columnsTableArray: Partial<CsvTemplateContentTechnical>[] = JSON.parse(this.csvTemplate.value).data;
        // Fetching the default template that would be generated for a new entry (contains all properties, references, vital and non vital)
        // We'll use this to re-build the technical properties in the grid displaying the template under readable format
        // We don't take into consideration entries from the default if they are not present in the current saved csvTemplate
        const defaultCsvTemplateData = await this._getDefaultCsvTemplateData(level, withSortValue);
        const defaultColumnsTableArray = this._generateColumnsTableArray(defaultCsvTemplateData);
        let i = 0;
        let lastGridEntry = {} as ui.PartialCollectionValue<CsvTemplateContentTechnical>;
        const parentProperties: {
            parentProperty: ExtractEdgesPartial<MetaNodeProperty>;
            parentPropertyFullPath: string;
            parentPropertyRowId: string;
        }[] = [];

        let parentPropertyPath = '';
        let parentPropertyRowId = '';
        let parentProperty: ui.PartialCollectionValue<MetaNodeProperty> = {};

        await asyncArray(columnsTableArray).forEach(async (line: Partial<CsvTemplateContentTechnical>) => {
            // Find item in the available fields from server
            // We do not display inactive fields based on service option, inactive custom fields etc.
            const templateItem = defaultColumnsTableArray.find(item => item.path === line.path);
            if (templateItem) {
                line.rank = i * 1000;
                i += 1;
                // In order to being able to add non vital sub-properties, we need to store the parent property and its path
                const parentReference = this._getParentPropertyForReference(templateItem, line.rank);
                if (parentReference) {
                    parentProperty = parentReference.parentProperty;
                    parentPropertyRowId = parentReference.parentPropertyRowId;
                    parentPropertyPath = parentReference.parentPropertyPath;
                }
                // We store the entry as it may be used for non-vitals (last value is corresponding to the non vital parent)
                lastGridEntry = {
                    ...templateItem,
                    dataType: line.dataType,
                    description: templateItem.description,
                    locale: line.locale,
                    path: line.path,
                    rank: line.rank,
                    _id: line._id,
                    parentProperty,
                    parentPropertyPath,
                    parentPropertyRowId,
                };
                this.columnsTable.addOrUpdateRecordValue(lastGridEntry);
                parentProperties.length = 0;
                parentProperty = {};
                parentPropertyPath = '';
                parentPropertyRowId = '';
            } else {
                const result = await this.fillColumnsTableFromCsvTemplateForNonVitals(
                    i,
                    line,
                    lastGridEntry,
                    parentProperty,
                    parentPropertyPath,
                    parentPropertyRowId,
                    parentProperties,
                );
                i = result.i;
                parentProperty = result.parentProperty;
                parentPropertyPath = result.parentPropertyPath;
                parentPropertyRowId = result.parentPropertyRowId;
            }
        });
        // A second loop is needed to set the group level for the non vital related sub-properties
        this.columnsTable.value
            .filter(line => line.isExportOnly)
            .forEach(line => {
                line.groupLevel = this._getGroupLevel(line);
                this.columnsTable.addOrUpdateRecordValue(line);
            });
    }

    _factoryProperties = [] as [{ factoryName: string; properties: ui.PartialCollectionValue<MetaNodeProperty>[] }?];

    async _getPropertyFromFactory(
        propertyName: string,
        factoryName: string,
    ): Promise<ui.PartialCollectionValue<MetaNodeProperty>> {
        if (this._factoryProperties.length) {
            const propertyFromFactory = this._factoryProperties
                .find(factory => factory?.factoryName === factoryName)
                ?.properties.find(prop => prop.name === propertyName);
            if (propertyFromFactory) return propertyFromFactory;
        }
        const result = (
            await this.$.graph
                .node('@sage/xtrem-metadata/MetaNodeProperty')
                .query(
                    ui.queryUtils.edgesSelector<MetaNodeProperty>(
                        {
                            name: true,
                            type: true,
                            isActive: true,
                            isVital: true,
                            isVitalParent: true,
                            isStored: true,
                            isRequired: true,
                            isPublished: true,
                            isNullable: true,
                            isTransientInput: true,
                            factory: {
                                name: true,
                            },
                            targetFactory: {
                                name: true,
                            },
                        },
                        {
                            first: 1,
                            filter: { factory: { name: factoryName }, name: propertyName },
                        },
                    ),
                )
                .execute()
        ).edges.map((edge: any) => {
            return edge.node;
        });

        const entryToPush = this._factoryProperties.find(factory => factory?.factoryName === factoryName);
        if (entryToPush) {
            entryToPush.properties.push(result[0]);
            return result[0];
        }
        this._factoryProperties.push({ factoryName, properties: [result[0]] });
        return result[0];
    }

    private fillColumnsTableFromCsvTemplateForNonVitals = async (
        i: integer,
        line: Partial<CsvTemplateContentTechnical>,
        lastGridEntry: Partial<CsvTemplateContentTechnical>,
        parentProperty: ui.PartialCollectionValue<MetaNodeProperty>,
        parentPropertyPath: string,
        parentPropertyRowId: string,
        parentProperties: {
            parentProperty: ExtractEdgesPartial<MetaNodeProperty>;
            parentPropertyFullPath: string;
            parentPropertyRowId: string;
        }[],
    ): Promise<{
        i: integer;
        parentProperty: ui.PartialCollectionValue<MetaNodeProperty>;
        parentPropertyPath: string;
        parentPropertyRowId: string;
    }> => {
        let _parentPropertyPath = parentPropertyPath;
        let _parentPropertyRowId = parentPropertyRowId;
        let _parentProperty: ui.PartialCollectionValue<MetaNodeProperty> = { ...parentProperty };

        // The entires not found in the default template may be related to non vital ref sub-properties
        // These are only used in export (have to be flagged as isExportOnly)
        // And are prefixed with the parent non vital ref
        // We need to compute all the technical properties as they are not sent in the default template
        // The parent non vital property is stored in the last value of lastGridEntry
        const pathComponents: string[] = line.path?.replace(/^[*,!,#,/]*/, '').split('.') || [];
        if (pathComponents.length > 1) {
            const currentPropertyNameFromPath = pathComponents[pathComponents.length - 1];
            const currentParentPropertyNameFromPath = pathComponents[Math.max(0, pathComponents.length - 2)];
            if (
                currentPropertyNameFromPath &&
                currentParentPropertyNameFromPath &&
                lastGridEntry.currentProperty?.factory?.name &&
                lastGridEntry.currentProperty?.targetFactory?.name
            ) {
                line.rank = i * 1000;
                // We only enter once per root non vital reference property
                if (parentProperties.length === 0) {
                    let parentFactoryName = lastGridEntry.currentProperty?.factory?.name;
                    // If group level is >0, it means we are on a sub-level, so we need to find the parent property
                    if (lastGridEntry.groupLevel && lastGridEntry.groupLevel > 0) {
                        parentFactoryName =
                            this.columnsTable.value
                                .filter(
                                    elt =>
                                        elt.currentProperty.name === currentParentPropertyNameFromPath &&
                                        elt.rank < line.rank,
                                )
                                .sort((a, b) => b.rank - a.rank)[0]?.currentProperty?.factory?.name ||
                            parentFactoryName;
                    }
                    // Root non vital for the first run
                    _parentProperty = await this._getPropertyFromFactory(
                        currentParentPropertyNameFromPath,
                        parentFactoryName,
                    );
                    _parentPropertyPath = currentParentPropertyNameFromPath;
                    _parentPropertyRowId = lastGridEntry._id || '';
                    pushToParentProperties(
                        parentProperties,
                        _parentProperty,
                        _parentPropertyPath,
                        _parentPropertyRowId,
                    );
                }
                if (_parentProperty?.name && _parentProperty.name !== currentParentPropertyNameFromPath) {
                    const parentFactoryName =
                        parentProperties.find(
                            factory => factory.parentProperty.name === currentParentPropertyNameFromPath,
                        )?.parentProperty?.factory?.name ||
                        parentProperties[parentProperties.length - 1].parentProperty?.targetFactory?.name ||
                        '';
                    _parentProperty =
                        parentProperties.find(prop => prop.parentProperty.name === _parentProperty?.factory?.name)
                            ?.parentProperty || {};
                    if (!_parentProperty?.name) {
                        _parentProperty = await this._getPropertyFromFactory(
                            currentParentPropertyNameFromPath,
                            parentFactoryName,
                        );
                    }
                    _parentPropertyRowId = this._getRowId(currentParentPropertyNameFromPath, line.rank);
                    _parentPropertyPath =
                        (_parentPropertyRowId &&
                            this.columnsTable.getRecordValue(_parentPropertyRowId)?.parentPropertyPath) ||
                        currentParentPropertyNameFromPath;

                    // The parent property may be a reference, so we add it's target factory in the graph names
                    pushToParentProperties(
                        parentProperties,
                        _parentProperty,
                        _parentPropertyPath,
                        _parentPropertyRowId,
                    );
                }
                const currentProperty = await this._getPropertyFromFactory(
                    currentPropertyNameFromPath,
                    _parentProperty.targetFactory?.name || '',
                );
                if (currentProperty.targetFactory?.name) {
                    _parentPropertyRowId = this._getRowId(_parentProperty.name || '', line.rank);
                    _parentPropertyPath = (
                        (_parentPropertyRowId &&
                            this.columnsTable.getRecordValue(_parentPropertyRowId)?.parentPropertyPath) ||
                        _parentPropertyPath
                    ).concat('.', currentPropertyNameFromPath);
                    pushToParentProperties(
                        parentProperties,
                        currentProperty,
                        _parentPropertyPath,
                        _parentPropertyRowId,
                    );
                }

                this.columnsTable.addOrUpdateRecordValue({
                    currentProperty: {
                        name: currentProperty.name,
                        factory: {
                            name: currentProperty.factory?.name,
                        },
                        targetFactory: {
                            name: currentProperty.targetFactory?.name,
                        },
                    },
                    groupLevel: 0,
                    isMandatory: pathComponents[0].startsWith('*') || pathComponents[0].startsWith('!'),
                    displayedPropertyType: getDisplayedPropertyType(currentProperty),
                    isExportOnly: true,
                    parentProperty: _parentProperty,
                    parentPropertyPath: _parentPropertyPath,
                    parentPropertyRowId: _parentPropertyRowId,
                    dataType: line.dataType,
                    description: line.description,
                    locale: line.locale,
                    path: line.path,
                    rank: line.rank,
                    _id: line._id,
                });
            }
        }
        return {
            i: i + 1,
            parentProperty: _parentProperty,
            parentPropertyPath: _parentPropertyPath,
            parentPropertyRowId: _parentPropertyRowId,
        };
    };

    async fillColumnsTableFromServerDefaultCsvTemplate(resetGrid = false) {
        const csvTemplateData = await this._getDefaultCsvTemplateData();
        const columnsTableArray = this._generateColumnsTableArray(csvTemplateData);
        if (resetGrid) {
            this.columnsTable.value = [];
            columnsTableArray.forEach(elt => {
                // In order to being able to add non vital sub-properties, we need to store the parent property and its path
                const parentReference = this._getParentPropertyForReference(elt);
                if (parentReference) {
                    elt.parentProperty = parentReference.parentProperty;
                    elt.parentPropertyRowId = parentReference.parentPropertyRowId;
                    elt.parentPropertyPath = parentReference.parentPropertyPath;
                }
                this.columnsTable.addOrUpdateRecordValue(elt as unknown as any);
            });
        } else {
            const existingIds = this.columnsTable.value.map(line => line._id);
            columnsTableArray
                .filter(elt => elt._id && existingIds.includes(elt._id))
                .forEach(elt => this.columnsTable.addOrUpdateRecordValue(elt as unknown as any));
        }
    }

    _getParentPropertyForReference(
        line: Partial<CsvTemplateContentTechnical>,
        lineRank?: number,
    ): {
        parentProperty: ui.PartialCollectionValue<MetaNodeProperty>;
        parentPropertyRowId: string;
        parentPropertyPath: string;
    } {
        let parentPropertyPath = '';
        let parentProperty = {} as ui.PartialCollectionValue<MetaNodeProperty>;
        let parentPropertyRowId = '';
        const rank = lineRank ?? line.rank;
        if (
            ['reference', 'collection', 'vitalReference', 'vitalCollection'].includes(line.displayedPropertyType) &&
            line.currentProperty?.factory?.name &&
            line.currentProperty.factory.name !== this.nodeName.value
        ) {
            const possibleParentLines = this.columnsTable.value
                .filter(
                    col =>
                        col.currentProperty.targetFactory.name === line.currentProperty.factory.name && col.rank < rank,
                )
                .sort((a, b) => b.rank - a.rank);
            const parentLine: Partial<CsvTemplateContentTechnical> = possibleParentLines[0];

            if (parentLine) {
                parentPropertyPath = ''.concat(
                    parentLine.parentPropertyPath
                        ? parentLine.parentPropertyPath.replace(/^[*,!,#,/]*/, '')
                        : parentLine.path.replace(/^[*,!,#,/]*/, ''),
                    '.',
                    line.currentProperty.name,
                );
                parentPropertyRowId = parentLine._id;
                parentProperty = parentLine.currentProperty;
            }
        }
        return { parentProperty, parentPropertyRowId, parentPropertyPath };
    }

    async _getDefaultCsvTemplateData(level = 1, withSortValue = true) {
        return this.$.graph
            .node('@sage/xtrem-import-export/ImportExportTemplate')
            .queries.getDefaultCsvTemplate(
                {
                    csvTemplate: {
                        value: true,
                    },
                    technical: {
                        value: true,
                    },
                    canImport: true,
                    canExport: true,
                },
                {
                    nodeName: this.nodeName.value || '',
                    level,
                    withSortValue,
                },
            )
            .execute();
    }

    async getDefaultCsvTemplate(resetGrid = false) {
        await this.fillColumnsTableFromServerDefaultCsvTemplate(resetGrid);
        this.csvTemplate.value = JSON.stringify({ data: this.columnsTable.value });
        this.setCsvTemplateDisplay();
        if (!this.name.value) this.name.value = this.nodeName.value;
        if (!this.id.value) this.id.value = this.nodeName.value;
    }

    // eslint-disable-next-line class-methods-use-this
    _setTechnicalValues(
        i: number,
        csvTemplateData: any,
        columnsTableArray: Partial<CsvTemplateContentTechnical>[] = [],
        technicalColumnsTableArray: Partial<CsvTemplateContentTechnical>[] = [],
    ) {
        // Technical properties, always separated by default delimiter for parsing issues
        const technicalTable = csvTemplateData.technical.value.split('\n');
        const properties = technicalTable[0].split(defaultDelimiter);
        const displayedPropertyTypes = technicalTable[1].split(defaultDelimiter);

        // By default, we set the same group level as the one from the previous line
        let groupLevel =
            i > 0 && technicalColumnsTableArray[i - 1].groupLevel ? technicalColumnsTableArray[i - 1].groupLevel : 0;
        if (columnsTableArray[i].dataType === 'reference') {
            if (columnsTableArray[i].path?.startsWith('/')) {
                groupLevel = columnsTableArray[i].path ? columnsTableArray[i].path.lastIndexOf('/') + 1 : 0;
            } else {
                groupLevel =
                    i > 0 && technicalColumnsTableArray[i - 1].groupLevel
                        ? technicalColumnsTableArray[i - 1].groupLevel
                        : 0;
            }
        }
        if (columnsTableArray[i].dataType === 'collection') {
            if (columnsTableArray[i].path?.startsWith('#')) {
                groupLevel =
                    columnsTableArray[i].path !== undefined ? columnsTableArray[i].path.lastIndexOf('#') + 1 : 0;
            } else {
                groupLevel =
                    i > 0 && technicalColumnsTableArray[i - 1].groupLevel
                        ? technicalColumnsTableArray[i - 1].groupLevel
                        : 0;
            }
        }
        return {
            rank: i * 100,
            isMandatory: columnsTableArray[i].path?.startsWith('*') || columnsTableArray[i].path?.startsWith('!'),
            isCustom: columnsTableArray[i].path?.startsWith('_customData('),
            groupLevel,
            currentProperty: JSON.parse(properties[i]),
            displayedPropertyType: displayedPropertyTypes[i],
        };
    }

    private async getDefaultUserPreferences(): Promise<void> {
        const defaultPreferencesValues = await this.$.graph
            .node('@sage/xtrem-system/UserPreferences')
            .getDefaults(
                {
                    importExportDelimiter: true,
                    importExportDateFormat: true,
                },
                {
                    data: {},
                },
            )
            .execute();
        defaultDelimiter = defaultPreferencesValues.importExportDelimiter || defaultDelimiter;
    }

    private async setUserPreferences(): Promise<void> {
        this.delimiter.value = defaultDelimiter;

        const userPreferences = await this.$.graph
            .node('@sage/xtrem-import-export/ImportExportTemplate')
            .queries.getUserImportExportPreferences({ importExportDelimiter: true }, {})
            .execute();
        if (userPreferences) this.delimiter.value = userPreferences.importExportDelimiter || defaultDelimiter;
    }

    // eslint-disable-next-line class-methods-use-this
    formatCsvTemplateDisplay(inputArray: string[], delimiter: string): string {
        return `${inputArray.join(delimiter)}\n`;
    }

    // eslint-disable-next-line class-methods-use-this
    prepareCsvTemplateElement(input: string): string {
        return input.startsWith('"') ? input : `"${input}"`;
    }

    setCsvTemplateDisplay() {
        if (this.columnsTable.value.length > 0) {
            const tableLength = this.columnsTable.value.length;
            const paths = Array.from({ length: tableLength }) as string[];
            const dataTypes = Array.from({ length: tableLength }) as string[];
            const descriptions = Array.from({ length: tableLength }) as string[];
            const locales = Array.from({ length: tableLength }) as string[];
            const _ids = Array.from({ length: tableLength });
            const sortedGrid = this.columnsTable.value.sort((a, b) => (a.rank && b.rank ? a.rank - b.rank : 0));
            for (let i = 0; i < sortedGrid.length; i += 1) {
                _ids[i] = sortedGrid[i]._id;
                paths[i] = this.prepareCsvTemplateElement(`${sortedGrid[i].path}`);
                dataTypes[i] = this.prepareCsvTemplateElement(`${sortedGrid[i].dataType}`);
                descriptions[i] = this.prepareCsvTemplateElement(`${sortedGrid[i].description}`);
                locales[i] = this.prepareCsvTemplateElement(`${sortedGrid[i].locale}`);
            }
            const delimiter = this.delimiter.value || defaultDelimiter;
            // Empty element for the last column
            paths.push('""');
            this.csvTemplateDisplay.value = this.formatCsvTemplateDisplay(paths, delimiter);
            if (this.hasDataTypeLine.value) {
                dataTypes.push('"IGNORE"');
                this.csvTemplateDisplay.value = `${this.csvTemplateDisplay.value}${this.formatCsvTemplateDisplay(dataTypes, delimiter)}`;
            }
            if (this.hasDescriptionLine.value) {
                descriptions.push('"IGNORE"');
                this.csvTemplateDisplay.value = `${this.csvTemplateDisplay.value}${this.formatCsvTemplateDisplay(descriptions, delimiter)}`;
            }
            if (this.hasLocaleLine.value) {
                locales.push('"IGNORE"');
                this.csvTemplateDisplay.value = `${this.csvTemplateDisplay.value}${this.formatCsvTemplateDisplay(locales, delimiter)}`;
            }
        }
    }

    /**
     * Get the list of Xtrem Nodes
     * return : : { name: string }[]
     */
    async loadXtremNodes(): Promise<NodeListResultElement[]> {
        return (await this.$.graph
            .node('@sage/xtrem-import-export/ImportExportTemplate')
            .queries.getNodeList(
                { name: true, canCreate: true, canUpdate: true, canImport: true, canExport: true },
                { filter: '' },
            )
            .execute()) as NodeListResultElement[];
    }

    // eslint-disable-next-line class-methods-use-this
    enablePageAction(pageAction: ui.PageAction, enable: boolean) {
        if (pageAction.isDisabled === enable) {
            pageAction.isDisabled = !enable;
        }
    }

    manageIsDefaultSwitch(isImportOnly: boolean) {
        if (isImportOnly) {
            this.isDefault.value = false;
            this.isDefault.isHidden = true;
        } else {
            this.isDefault.isHidden = false;
        }
    }

    enablePageActions(options: {
        add: boolean;
        cancel: boolean;
        delete: boolean;
        save: boolean;
        generateTemplate: boolean;
        importData: boolean;
    }) {
        this.enablePageAction(this.$standardCancelAction, options.cancel);
        this.enablePageAction(this.$standardDeleteAction, options.delete);
        this.enablePageAction(this.customSave, options.save);
        this.enablePageAction(this.$standardNewAction, options.add);
        this.enablePageAction(this.generateTemplate, options.generateTemplate);
        this.enablePageAction(this.importData, options.importData);
    }

    async loadImportResults(): Promise<void> {
        const result = await this.$.graph
            .node('@sage/xtrem-import-export/ImportResult')
            .query(
                ui.queryUtils.edgesSelector(
                    {
                        _id: true,
                        importExportTemplate: {
                            _id: true,
                            id: true,
                            name: true,
                            description: true,
                            nodeName: true,
                        },
                        status: true,
                        dryRun: true,
                        filename: true,
                        generalError: true,
                        rowsProcessed: true,
                        numberOfRowsInError: true,
                        startStamp: true,
                        endStamp: true,
                        rowsInError: { value: true },
                    },
                    {
                        first: 100,
                        orderBy: { _updateStamp: -1 },
                        filter: { importExportTemplate: this.$.queryParameters._id },
                    },
                ),
            )
            .execute();

        this.importResults.value = result.edges.map((edge: any) => {
            return edge.node;
        });
    }
}
