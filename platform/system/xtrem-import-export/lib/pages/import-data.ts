import { extractEdges, ExtractEdges } from '@sage/xtrem-client';
import { GraphApi, ImportExportTemplate } from '@sage/xtrem-import-export-api';
import * as ui from '@sage/xtrem-ui';
import { PillColor as PillColorImportExportState } from '../client-functions/_index';
import { importMenuItem } from '../menu-items/import-menu-item';
import { NodeListResultElement } from '../shared-functions';

interface ImportDataTableInterface {
    template: string;
    filename: string;
}

@ui.decorators.page<ImportData>({
    title: 'Data import',
    module: 'importexport',
    isTransient: true,
    skipDirtyCheck: true,
    mode: 'tabs',
    menuItem: importMenuItem,
    priority: 20,
    access: { node: '@sage/xtrem-import-export/ImportResult' },
    businessActions() {
        return [this.executeImport];
    },

    onDirtyStateUpdated(isPageDirty: boolean) {
        this.enablePageBusinessActions({
            executeImport: isPageDirty,
        });
    },

    async onLoad() {
        // Load list of templates
        this.loadXtremTemplates();

        // Load list of nodes and there create and update options
        this.loadXtremNodes();

        // Disable import button
        this.enablePageBusinessActions({
            executeImport: false,
        });

        // Reset the options to the defaults
        this.resetOptions();
    },
})
export class ImportData extends ui.Page<GraphApi> {
    private templates: ExtractEdges<ImportExportTemplate[]>;

    private nodes: { name: string; canCreate: boolean; canUpdate: boolean }[];

    /**
     * Get the list of Xtrem Nodes
     */
    async loadXtremNodes(): Promise<void> {
        this.nodes = (await this.$.graph
            .node('@sage/xtrem-import-export/ImportExportTemplate')
            .queries.getNodeList(
                { name: true, canCreate: true, canUpdate: true, canImport: true, canExport: true },
                { filter: '' },
            )
            .execute()) as NodeListResultElement[];
    }

    /**
     * Get the list of Xtrem Nodes
     */
    async loadXtremTemplates(): Promise<void> {
        this.templates = extractEdges<ImportExportTemplate>(
            await this.$.graph
                .node('@sage/xtrem-import-export/ImportExportTemplate')
                .query(
                    ui.queryUtils.edgesSelector<ImportExportTemplate>(
                        {
                            id: true,
                            name: true,
                            nodeName: true,
                            isActive: true,
                            description: true,
                        },
                        {
                            first: -1,
                            filter: { isActive: true, templateUse: { _in: ['importOnly', 'importAndExport'] } },
                        },
                    ),
                )
                .execute(),
        );
    }

    private resetOptions(): void {
        this.insertOption.value = true;
        this.updateOption.value = false;
        this.continueAfterErrorOption.value = true;
        this.maximumNumberOfErrors.value = '10';
        this.maximumNumberOfErrors.isHidden = false;
        this.insertOption.isDisabled = false;
        this.updateOption.isDisabled = false;
    }

    @ui.decorators.section<ImportData>({
        title: 'Data',
        isTitleHidden: true,
    })
    generalSection: ui.containers.Section;

    @ui.decorators.block<ImportData>({
        title: 'CSV file',
        parent() {
            return this.generalSection;
        },
    })
    fileBlock: ui.containers.Block;

    @ui.decorators.fileDepositField<ImportData>({
        parent() {
            return this.fileBlock;
        },
        title: 'Select file',
        node: '@sage/xtrem-upload/UploadedFile',
        fileTypes: 'text/csv' /* ,application/zip'*/,
        async onChange() {
            // Clear table and reset options to the defaults
            this.importData.value = [
                {
                    template: '',
                    filename: this.csvFile.value?.filename || '',
                },
            ] as ui.PartialNodeWithId<ImportDataTableInterface>[];
            this.resetOptions();

            this.enablePageBusinessActions({
                executeImport: false,
            });
        },
        kind: 'upload',
    })
    csvFile: ui.fields.FileDeposit;

    @ui.decorators.block<ImportData>({
        title: 'Options',
        parent() {
            return this.generalSection;
        },
    })
    optionsBlock: ui.containers.Block;

    @ui.decorators.checkboxField<ImportData>({
        title: 'Insert',
        parent() {
            return this.optionsBlock;
        },
    })
    insertOption: ui.fields.Checkbox;

    @ui.decorators.checkboxField<ImportData>({
        title: 'Update',
        parent() {
            return this.optionsBlock;
        },
    })
    updateOption: ui.fields.Checkbox;

    @ui.decorators.checkboxField<ImportData>({
        title: 'Test import',
        parent() {
            return this.optionsBlock;
        },
    })
    test: ui.fields.Checkbox;

    @ui.decorators.checkboxField<ImportData>({
        title: 'Continue and ignore the error',
        parent() {
            return this.optionsBlock;
        },
        onChange() {
            this.maximumNumberOfErrors.isHidden = this.continueAfterErrorOption.value !== true;
        },
    })
    continueAfterErrorOption: ui.fields.Checkbox;

    @ui.decorators.textField<ImportData>({
        parent() {
            return this.optionsBlock;
        },
        isHidden: true,
        title: 'Maximum number of errors',
    })
    maximumNumberOfErrors: ui.fields.Text;

    @ui.decorators.tableField<ImportData, ImportDataTableInterface & { _id: string }>({
        isTransient: true,
        canSelect: false,
        title: 'Files to import',
        parent() {
            return this.generalSection;
        },
        columns: [
            ui.nestedFields.select<ImportData>({
                bind: 'template',
                title: 'Template',
                canFilter: true,
                options() {
                    return this.templates.map((template: any) => {
                        return template.id;
                    });
                },
                onChange(_id, rowData) {
                    const template = this.templates.find(templateElement => templateElement.id === rowData.template);
                    if (template) {
                        rowData.templateDescription = template.description;
                        rowData.templateName = template.name;
                        rowData.nodeName = template.nodeName;
                        // Find the node info that template relates to
                        const node = this.nodes.find(n => n.name === template.nodeName);
                        // If creation is disbaled on the node we set the insertOption to false and disable the checkbox
                        if (!node?.canCreate) {
                            this.insertOption.value = false;
                            this.insertOption.isDisabled = true;
                        } else if (this.insertOption.isDisabled) {
                            this.insertOption.isDisabled = false;
                        }

                        // If update is disbaled on the node we set the updateOption to false and disable the checkbox
                        if (!node?.canUpdate) {
                            this.updateOption.value = false;
                            this.updateOption.isDisabled = true;
                        } else if (this.updateOption.isDisabled) {
                            this.updateOption.isDisabled = false;
                        }

                        // If both insert and update is disabled, then disable the import button
                        this.enablePageBusinessActions({
                            executeImport: !(this.insertOption.isDisabled && this.updateOption.isDisabled),
                        });
                        this.importData.setRecordValue(rowData);
                    }
                    this.loadImportResults();
                },
            }),
            ui.nestedFields.text<ImportData>({
                bind: 'templateName',
                title: 'Template name',
                isReadOnly: true,
                canFilter: true,
            }),
            ui.nestedFields.text<ImportData>({
                bind: 'nodeName',
                title: 'Node name',
                isReadOnly: true,
                canFilter: true,
            }),
            ui.nestedFields.text<ImportData>({
                bind: 'templateDescription',
                title: 'Template description',
                isReadOnly: true,
                canFilter: true,
            }),
            ui.nestedFields.text<ImportData>({
                bind: 'filename',
                title: 'File name',
                isReadOnly: true,
                canFilter: true,
            }),
        ],
    })
    importData: ui.fields.Table;

    @ui.decorators.section<ImportData>({
        title: 'Import results',
        isTitleHidden: true,
    })
    resultsSection: ui.containers.Section;

    async loadImportResults(): Promise<void> {
        const templateIds = this.importData.value.map(i => i.template);
        if (templateIds.length > 0) {
            const result = await this.$.graph
                .node('@sage/xtrem-import-export/ImportResult')
                .query(
                    ui.queryUtils.edgesSelector(
                        {
                            _id: true,
                            importExportTemplate: {
                                _id: true,
                                id: true,
                                name: true,
                                description: true,
                                nodeName: true,
                            },
                            status: true,
                            dryRun: true,
                            filename: true,
                            generalError: true,
                            rowsProcessed: true,
                            numberOfRowsInError: true,
                            startStamp: true,
                            endStamp: true,
                            rowsInError: { value: true },
                        },
                        {
                            first: 100,
                            orderBy: { _updateStamp: -1 },
                            filter: { importExportTemplate: { id: { _in: templateIds } } },
                        },
                    ),
                )
                .execute();

            this.importResults.value = result.edges.map((edge: any) => {
                return edge.node;
            });
        }
    }

    @ui.decorators.pageAction<ImportData>({
        icon: 'refresh',
        title: 'Refresh',
        async onClick() {
            await this.loadImportResults();
        },
    })
    refreshRelationMapping: ui.PageAction;

    @ui.decorators.tableField<ImportData>({
        node: '@sage/xtrem-import-export/ImportResult',
        isTransient: true,
        canSelect: false,
        title: 'Results',
        parent() {
            return this.resultsSection;
        },
        columns: [
            ui.nestedFields.text<ImportData>({
                bind: 'filename',
                title: 'File name',
                isReadOnly: true,
                canFilter: true,
            }),
            ui.nestedFields.label<ImportData>({
                bind: 'startStamp',
                title: 'Start time',
                size: 'large',
                borderColor: ui.tokens.colorsYang100,
                map(_fieldValue) {
                    return `${_fieldValue ? new Date(_fieldValue).toLocaleString() : ''}`;
                },
                canFilter: true,
            }),
            ui.nestedFields.label<ImportData>({
                bind: 'endStamp',
                title: 'End time',
                size: 'large',
                borderColor: ui.tokens.colorsYang100,
                map(_fieldValue) {
                    return `${_fieldValue ? new Date(_fieldValue).toLocaleString() : ''}`;
                },
                canFilter: true,
            }),
            ui.nestedFields.label<ImportData>({
                bind: 'status',
                title: 'Status',
                canFilter: true,
                size: 'large',
                optionType: '@sage/xtrem-import-export/ImportStatus',
                style: (_id, rowValue) =>
                    PillColorImportExportState.getLabelColorByStatus('ImportStatus', rowValue.status),
            }),
            ui.nestedFields.checkbox<ImportData>({
                bind: 'dryRun',
                title: 'Test',
                isReadOnly: true,
                canFilter: true,
            }),
            ui.nestedFields.text<ImportData>({
                bind: 'generalError',
                title: 'Error detail',
                isReadOnly: true,
                onClick(_id, result: any) {
                    this.$.dialog.message(
                        'info',
                        ui.localize(
                            '@sage/xtrem-import-export/pages__import_data__importResults____columns__title__generalError',
                            'Error detail',
                        ),
                        result.generalError,
                    );
                },
            }),
            ui.nestedFields.numeric<ImportData>({
                bind: 'rowsProcessed',
                title: 'Rows processed',
                isReadOnly: true,
                canFilter: true,
            }),
            ui.nestedFields.numeric<ImportData>({
                bind: 'numberOfRowsInError',
                title: 'Number of errors',
                isReadOnly: true,
                canFilter: true,
            }),
        ],
        dropdownActions: [
            {
                icon: 'download',
                title: 'Download',
                async onClick(_rowId: any, data: any) {
                    this.downloadSection.isHidden = false;
                    this.fileField.text = data.filename;
                    this.fileField.value = {
                        value: btoa(unescape(encodeURIComponent(data.rowsInError.value))),
                    };
                    try {
                        await this.$.dialog.custom('info', this.downloadSection, {
                            size: 'medium',
                            cancelButton: { isHidden: true },
                            acceptButton: {
                                isHidden: false,
                            },
                        });
                    } catch {
                        // Intentionally empty.
                    }

                    this.downloadSection.isHidden = true;
                },
            },
        ],
        fieldActions() {
            return [this.refreshRelationMapping];
        },
    })
    importResults: ui.fields.Table;

    /*
    validateFields checks that mandatory fields were set.
    */
    async validateFields(): Promise<boolean> {
        await this.fileBlock.validate();
        if (this.csvFile.value == null) {
            this.$.dialog.message(
                'error',
                ui.localize('@sage/xtrem-import-export/validation-error', 'Validation error'),
                ui.localize('@sage/xtrem-import-export/select-file', 'Please select a CSV file'),
            );
            return false;
        }

        if (!this.importData.value.length) {
            this.$.dialog.message(
                'error',
                ui.localize('@sage/xtrem-import-export/validation-error', 'Validation error'),
                ui.localize('@sage/xtrem-import-export/no-file-to-import', 'There are no files to import'),
            );
            return false;
        }

        if (this.importData.value.some(rowData => !rowData.template.length)) {
            this.$.dialog.message(
                'error',
                ui.localize('@sage/xtrem-import-export/validation-error', 'Validation error'),
                ui.localize(
                    '@sage/xtrem-import-export/no-template-selected',
                    'Please select an existing template for each file to import',
                ),
            );
            return false;
        }
        return true;
    }

    /*
    import triggers the import mutation if everything is ok.
     */
    async import(test = false): Promise<void> {
        if (await this.validateFields()) {
            if (
                test ||
                (await this.$.dialog
                    .confirmation(
                        'info',
                        ui.localize('@sage/xtrem-import-export/confirm-import', 'Confirm import'),
                        ui.localize(
                            '@sage/xtrem-import-export/confirm-import-question',
                            'Do you want to import the file?',
                        ),
                    )
                    .then(() => true)
                    .catch(() => false))
            ) {
                const importData = this.importData.value[0];

                await this.$.graph
                    .node('@sage/xtrem-import-export/ImportExportTemplate')
                    .asyncOperations.batchImport.start(
                        {
                            trackingId: true,
                        },
                        {
                            templateId: importData.template,
                            uploadedFileId: this.csvFile.value._id,
                            options: {
                                doInsert: this.insertOption.value,
                                doUpdate: this.updateOption.value,
                                dryRun: test,
                                maxErrorCount:
                                    this.continueAfterErrorOption.value === true
                                        ? parseInt(this.maximumNumberOfErrors.value, 10)
                                        : 1,
                            },
                        },
                    )
                    .execute();
                this.$.showToast(`${this.csvFile.value.filename} has been submitted for processing.`, {
                    timeout: 10000,
                    type: 'info',
                });

                await this.loadImportResults();
            }
        }
    }

    @ui.decorators.section<ImportData>({
        isHidden: true,
        title: 'Download',
    })
    downloadSection: ui.containers.Section;

    @ui.decorators.block<ImportData>({
        parent() {
            return this.downloadSection;
        },
    })
    downloadBlock: ui.containers.Block;

    @ui.decorators.fileField<ImportData>({
        isTransient: true,
        fileTypes: 'application/json',
        text: 'fileName',
        title: 'Import result',
        parent() {
            return this.downloadBlock;
        },
    })
    fileField: ui.fields.File;

    @ui.decorators.pageAction<ImportData>({
        title: 'Import',
        async onClick() {
            await this.import(!!this.test.value);
        },
    })
    executeImport: ui.PageAction;

    // eslint-disable-next-line class-methods-use-this
    enablePageBusinessAction(pageAction: ui.PageAction, enable: boolean) {
        if (pageAction.isDisabled === enable) {
            pageAction.isDisabled = !enable;
        }
    }

    enablePageBusinessActions(options: { executeImport: boolean }) {
        this.enablePageBusinessAction(this.executeImport, options.executeImport);
    }
}
