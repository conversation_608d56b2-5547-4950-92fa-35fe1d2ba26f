import { GraphApi, ImportExportTemplate } from '@sage/xtrem-import-export-api';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.page<ExportBulkMutationConfiguration>({
    title: 'Select export template',
    isTransient: true,
    businessActions() {
        return [this.$standardCancelAction, this.$standardDialogConfirmationAction];
    },
    async onLoad(): Promise<void> {
        const { node, contextPage } = this.$.queryParameters;
        if (!node) {
            throw new Error(
                'Cannot list export templates because the node name or context page is not provided in the query arguments',
            );
        }

        const nodeName = String(node).split('/')[2];
        if (!nodeName) {
            throw new Error('The provided node name is invalid');
        }

        this.id.filter = { nodeName };

        // Query the list of templates for the context page from the server
        const result = await this.$.graph.raw(
            `{pages(filter:{exactMatch:true,packageOrPage: "${contextPage}"}){exportTemplatesByNode{name,exportTemplates{id,name,isDefault}}}}`,
            false,
            true,
        );

        // Find the template for the main page node from the list of templates
        const exportTemplates: Array<{ id: string; name: string; isDefault: boolean }> =
            result.pages?.[0]?.exportTemplatesByNode?.find((t: any) => t.name === node)?.exportTemplates;

        if (!exportTemplates || exportTemplates.length === 0) {
            throw new Error('No export templates were found.');
        }

        // Set default template based on isDefault flag
        const defaultTemplate = exportTemplates.find(t => t.isDefault) || exportTemplates[0];

        // Find the import template node that belongs to the default id
        const importExportTemplateNode = this.$.graph.node('@sage/xtrem-import-export/ImportExportTemplate');
        const defaultQueryResult = await importExportTemplateNode
            .query(
                ui.queryUtils.edgesSelector(
                    {
                        _id: true,
                        id: true,
                        name: true,
                        description: true,
                    },
                    {
                        filter: { id: defaultTemplate.id },
                    },
                ),
            )
            .execute();

        this.id.value = defaultQueryResult.edges[0].node;
    },
})
export class ExportBulkMutationConfiguration extends ui.Page<GraphApi> {
    getSerializedValues() {
        // We only return the ID field of the selected template, not the full object
        const { id } = this.id.value;
        return { id };
    }

    @ui.decorators.section<ExportBulkMutationConfiguration>({
        isTitleHidden: true,
    })
    section: ui.containers.Section;

    @ui.decorators.block<ExportBulkMutationConfiguration>({
        isTitleHidden: true,
        parent() {
            return this.section;
        },
    })
    block: ui.containers.Block;

    @ui.decorators.referenceField<ExportBulkMutationConfiguration, ImportExportTemplate>({
        node: '@sage/xtrem-import-export/ImportExportTemplate',
        parent() {
            return this.block;
        },
        orderBy: {
            name: 1,
        },
        columns: [
            ui.nestedFields.text({
                bind: 'name',
                title: 'Template name',
            }),
            ui.nestedFields.text({
                bind: 'description',
                title: 'Description',
            }),
            ui.nestedFields.text({
                bind: 'id',
                title: 'ID',
                isHidden: true,
            }),
        ],
        isFullWidth: true,
        isMandatory: true,
        title: 'Export template',
        valueField: 'name',
        helperTextField: 'description',
        minLookupCharacters: 0,
    })
    id: ui.fields.Reference<ImportExportTemplate>;
}
