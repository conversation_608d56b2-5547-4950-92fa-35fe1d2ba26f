import { BatchContext } from '@sage/xtrem-communication';
import {
    AccessRights,
    asyncArray,
    Context,
    datetime,
    decorators,
    integer,
    Node,
    Reference,
    TextStream,
} from '@sage/xtrem-core';
import { InfrastructureHelper } from '@sage/xtrem-infrastructure-adapter';
import { DataInputError } from '@sage/xtrem-shared';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremUpload from '@sage/xtrem-upload';
import { Readable } from 'stream';
import { allowedDecimalPoint, ImportExportParameters } from '../functions/common';
import * as xtremImportExport from '../index';
import { ImportExportTemplate } from './import-export-template';
import { logger } from './utils';

@decorators.node<ImportResult>({
    package: 'xtrem-import-export',
    storage: 'sql',
    isPublished: true,
    canCreate: true,
    canRead: true,
    canUpdate: true,
    canDelete: true,
    canDeleteMany: true,
    isPlatformNode: true,
})
export class ImportResult extends Node {
    @decorators.datetimeProperty<ImportResult, 'startStamp'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
    })
    readonly startStamp: Promise<datetime>;

    @decorators.datetimeProperty<ImportResult, 'endStamp'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
    })
    readonly endStamp: Promise<datetime>;

    @decorators.stringProperty<ImportResult, 'filename'>({
        isPublished: true,
        dataType: () => xtremImportExport.dataTypes.nameDataType,
        async getValue() {
            return (await (await this.uploadedFile).filename) ?? '';
        },
        lookupAccess: true,
    })
    readonly filename: Promise<string>;

    @decorators.integerProperty<ImportResult, 'rowsProcessed'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
    })
    readonly rowsProcessed: Promise<integer>;

    @decorators.integerProperty<ImportResult, 'numberOfRowsInError'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
    })
    readonly numberOfRowsInError: Promise<integer>;

    @decorators.textStreamProperty<ImportResult, 'rowsInError'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremImportExport.dataTypes.rowsTextStreamType,
        lookupAccess: true,
    })
    readonly rowsInError: Promise<TextStream>;

    @decorators.enumProperty<ImportResult, 'status'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremImportExport.enums.importStatusEnumDataType,
        lookupAccess: true,
    })
    readonly status: Promise<xtremImportExport.enums.ImportStatus>;

    @decorators.booleanProperty<ImportResult, 'dryRun'>({
        isStored: true,
        isPublished: true,
        defaultValue() {
            return false;
        },
        lookupAccess: true,
    })
    readonly dryRun: Promise<boolean>;

    @decorators.booleanProperty<ImportResult, 'doUpdate'>({
        isStored: true,
        isPublished: true,
        defaultValue() {
            return false;
        },
    })
    readonly doUpdate: Promise<boolean>;

    @decorators.booleanProperty<ImportResult, 'doInsert'>({
        isStored: true,
        isPublished: true,
        defaultValue() {
            return true;
        },
    })
    readonly doInsert: Promise<boolean>;

    @decorators.integerProperty<ImportResult, 'maxErrorCount'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
    })
    readonly maxErrorCount: Promise<integer | null>;

    @decorators.referenceProperty<ImportResult, 'uploadedFile'>({
        isStored: true,
        isPublished: true,
        node: () => xtremUpload.nodes.UploadedFile,
    })
    readonly uploadedFile: Reference<xtremUpload.nodes.UploadedFile>;

    @decorators.stringProperty<ImportResult, 'uploadRejectReason'>({
        isPublished: true,
        async getValue() {
            return (await this.uploadedFile).rejectReason;
        },
        dataType: () => xtremSystem.dataTypes.shortDescription,
    })
    readonly uploadRejectReason: Promise<string>;

    @decorators.stringProperty<ImportResult, 'generalError'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.description,
        lookupAccess: true,
    })
    readonly generalError: Promise<string>;

    @decorators.referenceProperty<ImportResult, 'importExportTemplate'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => xtremImportExport.nodes.ImportExportTemplate,
        lookupAccess: true,
    })
    readonly importExportTemplate: Reference<xtremImportExport.nodes.ImportExportTemplate | null>;

    // TODO: refactoring to remove parameters if not used anymore
    @decorators.jsonProperty<ImportResult, 'parameters'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        dependsOn: ['importExportTemplate'],
        control(cx, val) {
            if (val) {
                if (!allowedDecimalPoint.includes(val.decimalPoint)) {
                    cx.error.addLocalized(
                        '@sage/xtrem-import-export/default_parameters-decimal_point-invalid_value',
                        'The decimal point is not valid: {{value}}.',
                        {
                            value: val.decimalPoint,
                        },
                    );
                }
            }
        },
        async defaultValue() {
            return (await this.importExportTemplate)?.defaultParameters ?? null;
        },
    })
    readonly parameters: Promise<ImportExportParameters | null>;

    @decorators.stringProperty<ImportResult, 'notificationId'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.id,
    })
    readonly notificationId: Promise<string>;

    /**
     * Notification listener that will process the import
     * @param context
     * @param payload
     * @returns
     */
    @decorators.notificationListener<typeof ImportResult>({
        topic: 'UploadedFile/processUpload',
        startsReadOnly: true,
        onError: async (context, envelope, error) => {
            logger.error(() => error.stack || error.message);
            await context.runInWritableContext(async writableContext => {
                const importResultForUpdate = (
                    await writableContext
                        .query(ImportResult, {
                            filter: { uploadedFile: envelope.payload.uploadedFileId },
                            forUpdate: true,
                        })
                        .toArray()
                )[0];

                // If errors are not set on result then this is a global error and we just create error file.
                // Possible global errors;
                // - be parsing the CSV
                // - factory create/update validation
                let rowsInError = await importResultForUpdate.rowsInError;
                if (!rowsInError.value) {
                    rowsInError = new TextStream(`error\n${error.message}`, 'text/csv');
                }

                await importResultForUpdate?.$.set({ status: 'rejected', rowsInError });

                await importResultForUpdate?.$.save();
            });
        },
    })
    static async onProcessUpload(context: Context, payload: any): Promise<void> {
        const importResults = await context
            .query(ImportResult, { filter: { uploadedFile: payload.uploadedFileId } })
            .toArray();

        logger.info(
            `[Import ?] processing import for uploaded file ${payload.uploadedFileId} got ${importResults.length} import result`,
        );
        if (importResults.length === 0) {
            return;
        }
        if (importResults.length > 1) {
            logger.error(
                `expecting only 1 result for upload file ${payload.uploadedFileId}, got ${importResults.length}`,
            );
            throw new Error(`multiple import results exist for the uploaded file ${payload.uploadedFileId}`);
        }
        const importResult = importResults[0];
        const importResultStatus = await importResult.status;

        // If there no result record, then the import is not started or has been cancelled
        // or the upload does not pertain to an import.
        if (importResultStatus !== 'pending') return;

        await context.batch.logMessage(
            'info',
            `[Import ${importResult?._id}] processing import for uploaded file ${payload.uploadedFileId} with import status '${importResultStatus}' transaction ${context.transaction.debugId}`,
        );

        // We found the result
        const uploadedFile = await importResult.uploadedFile;
        const uploadedFileKey = await uploadedFile.key;
        const uploadedFileStatus = await uploadedFile.status;

        logger.info(
            `[Import ${importResult._id}] processing uploaded file ${uploadedFile._id}/${uploadedFileKey} with status '${uploadedFileStatus}'`,
        );

        if (uploadedFileStatus === 'uploaded') {
            throw new Error(`unexpected uploaded file status 'uploaded' for ${payload.uploadedFileId}`);
        }

        // If the import is still queued and the uploaded file has been verified
        if (uploadedFileStatus === 'verified') {
            // Mark the import as in progress, so no subsequent notifications pick it up for processing
            const previousStatus = await context.runInWritableContext(async writableContext => {
                const importResultForUpdate = (
                    await writableContext
                        .query(ImportResult, {
                            filter: { uploadedFile: payload.uploadedFileId },
                            forUpdate: true,
                        })
                        .toArray()
                )[0];
                const status = await importResultForUpdate.status;
                if (status !== 'inProgress') {
                    await importResultForUpdate?.$.set({
                        status: 'inProgress',
                    });
                    await importResultForUpdate?.$.save();
                }
                return status;
            });

            if (previousStatus === 'inProgress') {
                logger.warn(
                    `[Import ${importResult._id}] file ${uploadedFile._id}/${uploadedFileKey} is already in progress. This one will be ignored.`,
                );
                return;
            }

            const contextId = await uploadedFile?.key;
            // Read file from S3
            const s3ReadResult =
                contextId &&
                (await InfrastructureHelper.readAsyncUploadResult(context, contextId, 'CONTEXT_FILE_UPLOAD'));
            if (!s3ReadResult)
                throw new DataInputError(`Unable retrieve contents of file ${await uploadedFile?.filename}.`);

            const trackingId = await importResult.notificationId;
            if (!trackingId) {
                context.logger.warn(
                    `invalid batch context call: notificationId missing, status '${uploadedFileStatus}'`,
                );
            }

            // We need to check the access rights for the user and load the whole user access rights because we are
            // in batch context (not the initial graphQL context)
            const controlledOperations = [];
            if (await importResult.doInsert) controlledOperations.push('create');
            if (await importResult.doUpdate) controlledOperations.push('update');
            const nodeName = await (await importResult.importExportTemplate)?.nodeName;
            if (nodeName) {
                await asyncArray(controlledOperations).forEach(operationName =>
                    AccessRights.checkOperationAccessRight(context, {
                        nodeName,
                        operationName,
                    }),
                );
            }

            const batchContext = new BatchContext(context, trackingId ?? '');
            try {
                await ImportExportTemplate.importCsv(
                    context,
                    batchContext,
                    s3ReadResult.body as Readable,
                    importResult,
                );
                await batchContext.logMessage('result', 'Import complete', { data: importResult });
            } catch (error) {
                await batchContext.logMessage('error', error.message);
                throw error;
            } finally {
                // TODO: improve the visual progress bar to show the progress, here is a hack to 100%
                await batchContext.updateProgress({
                    totalCount: 1,
                    successCount: 1,
                });
                await batchContext.end();
            }
        }

        if (uploadedFileStatus === 'rejected') {
            await context.runInWritableContext(async writableContext => {
                const importResultForUpdate = (
                    await writableContext
                        .query(ImportResult, {
                            filter: { uploadedFile: payload.uploadedFileId },
                            forUpdate: true,
                        })
                        .toArray()
                )[0];
                await importResultForUpdate?.$.set({
                    status: 'rejected',
                    generalError: await uploadedFile.rejectReason,
                });

                await importResultForUpdate?.$.save();
            });

            const trackingId = await importResult.notificationId;
            if (!trackingId) {
                context.logger.warn(
                    `invalid batch context call: notificationId missing, status '${uploadedFileStatus}'`,
                );
            }
            const batchContext = new BatchContext(context, trackingId ?? '');
            await batchContext.logMessage(
                'exception',
                `[import ${importResult._id}] batchImport uploaded file ${payload.uploadedFileId} status ${uploadedFileStatus}`,
            );
            // TODO: improve the visual progress bar to show the progress, here is a hack to 100%
            await batchContext.updateProgress({
                totalCount: 1,
                errorCount: 1,
            });
            await batchContext.end();
        }
    }
}
