import { DataInputError, datetime, Logger, Test } from '@sage/xtrem-core';
import { FileTimeToLive } from '@sage/xtrem-file-storage';
import { InfrastructureHelper } from '@sage/xtrem-infrastructure-adapter';
import * as xtremUpload from '@sage/xtrem-upload';
import * as fs from 'fs';
import * as path from 'path';
import { FileFormat } from '../enums/file-format';

export const logger = Logger.getLogger(__filename, 'import');

export async function uploadTestFile(
    filePath: string,
    userEmail = '<EMAIL>',
): Promise<{ uploadedFileId: number; contextId: string; uploadUrl: string }> {
    const fileContents = fs.readFileSync(filePath, 'utf8');

    const { uploadUrl, uploadedFileId, contextId } = await Test.withCommittedContext(
        async context => {
            // Create uploaded file entry
            const uploadedFile = await context.create(xtremUpload.nodes.UploadedFile, {
                kind: 'upload',
                contentLength: fileContents.length,
                filename: path.basename(filePath),
                lastModified: datetime.now(),
                mimeType: 'application/xls',
            });
            await uploadedFile.$.save();

            // Create the test CSV to the location where the can pick it later, after verfication
            await InfrastructureHelper.createFile(
                context,
                `uploads/${await uploadedFile.key}`,
                '',
                Buffer.from(fileContents),
                FileTimeToLive.Expire1Day,
            );
            return {
                uploadUrl: await uploadedFile.uploadUrl,
                uploadedFileId: uploadedFile._id,
                contextId: await uploadedFile.key,
            };
        },
        { userEmail, description: () => 'uploadTestFile' },
    );

    // In test mode the uploadUrl will be a local path
    // This will mimic the UI using the presigned URL to upload the CSV

    return { uploadedFileId, contextId, uploadUrl };
}

export function checkOutputFormat(outputFormat: FileFormat): FileFormat {
    if (!outputFormat) return 'csv';
    if (outputFormat !== 'csv' && outputFormat !== 'xlsx')
        throw new DataInputError('Only CSV and XLSX format is supported for export');
    return outputFormat;
}
