import * as xtremCommunication from '@sage/xtrem-communication';
import { BatchContext } from '@sage/xtrem-communication';
import {
    AccessRights,
    AnyNode,
    AnyRecord,
    ConfigManager,
    Context,
    CoreHooks,
    CsvTemplateContent,
    Diagnose,
    HeaderDefinition,
    LocalizeLocale,
    Node,
    NodeFactory,
    NodePayloadData,
    NodeSelectOptions,
    NodeSelector,
    NodeStatus,
    Property,
    TextStream,
    TypeName,
    UserAccess,
    ValidationSeverity,
    asyncArray,
    customFieldTypeDefaultValue,
    datetime,
    decorators,
} from '@sage/xtrem-core';
import * as xtremDateTime from '@sage/xtrem-date-time';
import { FileTimeToLive } from '@sage/xtrem-file-storage';
import * as xtremInfrastructureAdapter from '@sage/xtrem-infrastructure-adapter';
import { DataInputError, unwrapError } from '@sage/xtrem-shared';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremUpload from '@sage/xtrem-upload';
import * as fs from 'fs';
import * as lodash from 'lodash';
import * as fsp from 'path';
import { Readable } from 'stream';
import { FileFormat, fileFormatEnumDataType } from '../enums/file-format';
import { ImportStatus } from '../enums/import-status';
import {
    ImportCsvResult,
    ImportExportParameters,
    LoadInfo,
    ParsedResultBucket,
    allowedDecimalPoint,
    defaultDelimiter,
    getUserImportExportDateFormat,
    getUserImportExportDelimiter,
    isDataInputError,
    isDatabaseError,
    sleepMillis,
} from '../functions/common';
import { ImportHeader, parseHeadersWithCustomFields } from '../functions/csv-to-json';
import {
    SelfReferencingProperty,
    fixSelfReferencingData,
    generateExportData,
    prepareSelfReferencingData,
    sendClientExportNotification,
    stringifyCsv,
} from '../functions/export-utils';
import { parseCsvStreamTransform } from '../functions/parse-csv';
import * as xtremImportExport from '../index';
import { NodeListResultElement } from '../shared-functions/import-export-types';
import { logger } from './utils';

const userAccess = {
    unavailable: { sites: [], accessCodes: [], status: 'unavailable' } as UserAccess,
    unauthorized: { sites: [], accessCodes: [], status: 'unauthorized' } as UserAccess,
    full: { sites: null, accessCodes: null, status: 'authorized' } as UserAccess,
};

@decorators.node<ImportExportTemplate>({
    package: 'xtrem-import-export',
    storage: 'sql',
    isPublished: true,
    canCreate: true,
    canRead: true,
    canUpdate: true,
    canDelete: true,
    indexes: [{ orderBy: { id: 1 }, isUnique: true, isNaturalKey: true }],
    isSetupNode: true,
    isCached: true,
    async saveBegin() {
        const isDefault = await this.isDefault;
        const templateUse = await this.templateUse;
        if (isDefault) {
            if (['exportOnly', 'importAndExport'].includes(templateUse)) {
                if (
                    (this.$.status === NodeStatus.modified && (await (await this.$.old).isDefault) === false) ||
                    this.$.status === NodeStatus.added
                ) {
                    const nodeName = await this.nodeName;

                    await this.$.context.bulkUpdate(xtremImportExport.nodes.ImportExportTemplate, {
                        set: { isDefault: false },
                        where: {
                            nodeName,
                            templateUse: {
                                _in: ['exportOnly', 'importAndExport'],
                            },
                        },
                    });
                }
            } else {
                await this.$.set({ isDefault: false });
            }
        }
    },
})
export class ImportExportTemplate extends Node {
    @decorators.stringProperty<ImportExportTemplate, 'id'>({
        isStored: true,
        isPublished: true,
        isNotEmpty: true,
        isFrozen: true,
        dataType: () => xtremSystem.dataTypes.id,
        lookupAccess: true,
    })
    readonly id: Promise<string>;

    @decorators.stringProperty<ImportExportTemplate, 'name'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.localizedName,
        lookupAccess: true,
    })
    readonly name: Promise<string>;

    @decorators.stringProperty<ImportExportTemplate, 'description'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.localizedDescription,
        lookupAccess: true,
    })
    readonly description: Promise<string>;

    @decorators.stringProperty<ImportExportTemplate, 'nodeName'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.name,
        isFrozen: true,
        lookupAccess: true,
    })
    readonly nodeName: Promise<string>;

    @decorators.booleanProperty<ImportExportTemplate, 'isActive'>({
        isStored: true,
        isPublished: true,
        defaultValue: true,
        provides: ['isActive'],
        isOwnedByCustomer: true,
        lookupAccess: true,
    })
    readonly isActive: Promise<boolean>;

    @decorators.booleanProperty<ImportExportTemplate, 'isDefault'>({
        isStored: true,
        isPublished: true,
        defaultValue: false,
        isOwnedByCustomer: true,
    })
    readonly isDefault: Promise<boolean>;

    @decorators.jsonProperty<ImportExportTemplate, 'csvTemplate'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        dependsOn: ['defaultParameters'],
        async control(cx, val) {
            // TODO: refactoring to remove defaultParameters if not used anymore
            const parameters = await this.defaultParameters;
            if (!parameters) throw new Error('Import parameters cannot be null');

            const rootFactory = this.$.context.application.getFactoryByName(await this.nodeName);
            const templateHeaders = await parseHeadersWithCustomFields(
                this.$.context,
                this.$.context.batch,
                true, // this value is true because when we save/ modify a template, the property must be active
                rootFactory,
                val.data,
            );
            const naturalKeyHeaderPropertyNames = [] as string[];
            templateHeaders
                .filter(header => header.csvName !== '_ignore' && header.property !== undefined)
                .forEach(header => {
                    let factory = rootFactory;

                    if (header.path.length > 0) {
                        header.path.forEach((p, i) => {
                            const prop = factory.findProperty(p);
                            // Will always enter the if, using it for typing to get targetFactory
                            const stopIndex = header.isArrayOrVitalReference
                                ? header.path.length - 2
                                : header.path.length - 1;
                            if (prop.isForeignNodeProperty() && i <= stopIndex) {
                                factory = prop.targetFactory;
                            }
                        });
                    }

                    if (header.propertyName !== '_customData') {
                        // Finds property and throws an error if the property is not found
                        const property = factory.findProperty(header.propertyName);

                        // property of the root factory and property a member of the natural key
                        // We are collecting these to see that all the properties of the root natural key are provided
                        if (rootFactory.naturalKey?.includes(header.propertyName) && header.path.length === 0) {
                            naturalKeyHeaderPropertyNames.push(header.propertyName);
                        }

                        // Control non vital references
                        if (!property.isMutable && !property.isVital && property.isReferenceProperty()) {
                            // If the target factory has a natural key we don't allow any list of properties after the property name
                            if (property.targetFactory.naturalKey) {
                                if (header.keyColumns && header.keyColumns?.length > 0) {
                                    cx.addDiagnose(
                                        ValidationSeverity.error,
                                        this.$.context.localize(
                                            '@sage/xtrem-import-export/invalid-reference-header-unexpected-index-on-natural-key',
                                            "The {{name}} column header is not valid. Remove '({{columns}})' after the property name.",
                                            {
                                                name: header.csvName,
                                                columns: header.keyColumns.join(),
                                            },
                                        ),
                                    );
                                }
                            } else if (header.keyColumns?.join() !== '_id') {
                                // Otherwise the target factory does not have a natural key. We only accept it if followed by `(_id)`.
                                // Note: the message is stricter than that because the `(_id)` syntax is only a temporary workaround,
                                // and we should not advertise it too loud.
                                // It will go away once we fix the missing natural keys.
                                cx.addDiagnose(
                                    ValidationSeverity.error,
                                    this.$.context.localize(
                                        '@sage/xtrem-import-export/invalid-reference-header-no-natural-key',
                                        'The {{name}} header cannot be included in the import template. The {{target}} target node needs to have a natural key index.',
                                        {
                                            name: header.csvName,
                                            target: property.targetFactory.name,
                                        },
                                    ),
                                );
                            }
                        }
                    }
                });

            if (
                rootFactory.naturalKey &&
                !rootFactory.naturalKey
                    .filter(key => key !== '_sortValue')
                    .every(key => naturalKeyHeaderPropertyNames.includes(key))
            ) {
                cx.addDiagnose(
                    ValidationSeverity.warn,
                    this.$.context.localize(
                        '@sage/xtrem-import-export/missing-natural-key-header',
                        'The natural key for the {{node}} node is missing properties that are not included in the template. Add the missing properties: {{missing}}.',
                        {
                            node: rootFactory.name,
                            missing: rootFactory.naturalKey
                                .filter(key => !naturalKeyHeaderPropertyNames.includes(key))
                                .join(),
                        },
                    ),
                );
            }
        },
    })
    readonly csvTemplate: Promise<{ data: CsvTemplateContent[] }>;

    @decorators.enumProperty<ImportExportTemplate, 'templateUse'>({
        isPublished: true,
        isStored: true,
        isRequired: true,
        dataType: () => xtremImportExport.enums.templateUseDataType,
        dependsOn: ['nodeName'],
        lookupAccess: true,
        async defaultValue() {
            if (await this.nodeName) {
                const factory = this.$.context.application.getFactoryByName(await this.nodeName);
                return factory?.canCreate || (factory?.canUpdate && factory?.naturalKey)
                    ? 'importAndExport'
                    : 'exportOnly';
            }
            return 'importAndExport';
        },
    })
    readonly templateUse: Promise<xtremImportExport.enums.TemplateUse>;

    // TODO: refactoring to remove defaultParameters if not used anymore
    @decorators.jsonProperty<ImportExportTemplate, 'defaultParameters'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        control(cx, val) {
            if (val) {
                if (!allowedDecimalPoint.includes(val.decimalPoint)) {
                    cx.error.addLocalized(
                        '@sage/xtrem-import-export/default_parameters-decimal_point-invalid_value',
                        'The decimal point is not valid: {{value}}.',
                        {
                            value: val.decimalPoint,
                        },
                    );
                }
            }
        },
        defaultValue() {
            return {
                decimalPoint: '.',
                quoteCharacter: "'",
                escapeCharacter: '\\',
            };
        },
    })
    readonly defaultParameters: Promise<ImportExportParameters>;

    @decorators.query<typeof ImportExportTemplate, 'getFieldsByFactory'>({
        isPublished: true,
        parameters: [
            { name: 'factoryName', type: 'string', isMandatory: true },
            { name: 'level', type: 'integer', isMandatory: false },
            { name: 'withSortValue', type: 'boolean', isMandatory: false },
            { name: 'isExportOnly', type: 'boolean', isMandatory: false },
        ],
        return: {
            type: 'array',
            item: 'string',
        },
    })
    static async getFieldsByFactory(
        context: Context,
        factoryName: string,
        level = 1,
        withSortValue = true,
        isExportOnly = false,
    ): Promise<string[]> {
        if (factoryName === '') return Promise.resolve([]);

        const properties = await asyncArray(
            xtremImportExport.functions.getFactoryProperties(
                context.application.getFactoryByName(factoryName),
                level,
                false,
                withSortValue,
            ),
        )
            .filter(async prop => !!(await prop.isEnabledByServiceOptions(context)))
            .filter(
                prop =>
                    !(prop.isStringProperty() && prop.isStoredEncrypted) &&
                    (!isExportOnly || (isExportOnly && !prop.isCollectionProperty())),
            )
            .map(item => item.name)
            .toArray();

        const customProps = await xtremImportExport.functions.getFactoryCustomFields(context, factoryName);

        return Promise.resolve([...properties, ...customProps]);
    }

    @decorators.query<typeof ImportExportTemplate, 'getNodeList'>({
        isPublished: true,
        parameters: [{ name: 'filter', type: 'string', isMandatory: false }],
        return: {
            type: 'array',
            item: {
                type: 'object',
                properties: {
                    name: 'string',
                    canCreate: 'boolean',
                    canUpdate: 'boolean',
                    canImport: 'boolean',
                    canExport: 'boolean',
                },
            },
            isMandatory: true,
        },
    })
    static getNodeList(context: Context, filter?: string): NodeListResultElement[] {
        const factoryList = context.application.getAllFactories();
        return (
            factoryList
                .filter(
                    factory =>
                        !factory.isAbstract &&
                        (factory.canCreate || (factory.canUpdate && !!factory.naturalKey) || factory.canExport) &&
                        (filter ? factory.name.match(filter) || factory.table?.name.match(filter) : true),
                )
                // TODO: consider also filtering out nodes that are not published
                .map(factory => {
                    return {
                        name: factory.name,
                        canCreate: factory.canCreate,
                        canUpdate: factory.canUpdate && !!factory.naturalKey,
                        canImport: factory.canCreate || (factory.canUpdate && !!factory.naturalKey),
                        canExport: factory.canExport,
                    };
                })
                .sort((elt1, elt2) => {
                    return elt1.name.localeCompare(
                        elt2.name,
                        context.locales?.map(locale => (locale === 'base' ? 'en' : locale)),
                    );
                })
        );
    }

    @decorators.query<typeof ImportExportTemplate, 'getUserImportExportPreferences'>({
        isPublished: true,
        parameters: [],
        return: { type: 'instance', node: () => xtremSystem.nodes.UserPreferences },
    })
    static async getUserImportExportPreferences(context: Context): Promise<xtremSystem.nodes.UserPreferences | null> {
        const userInfo = await context.user;
        if (userInfo) {
            const user = await xtremSystem.nodes.User.fromUserInfo(context, userInfo);
            const userPreferences = await user.preferences;
            if (userPreferences) return userPreferences;
        }
        return null;
    }

    @decorators.query<typeof ImportExportTemplate, 'getDefaultCsvTemplate'>({
        isPublished: true,
        parameters: [
            {
                name: 'nodeName',
                type: 'string',
                isMandatory: true,
            },
            {
                name: 'level',
                type: 'integer',
                isMandatory: false,
            },
            {
                name: 'isExportWithDefinition',
                type: 'boolean',
                isMandatory: false,
            },
            {
                name: 'withSortValue',
                type: 'boolean',
                isMandatory: false,
            },
        ],
        return: {
            type: 'object',
            properties: {
                csvTemplate: 'textStream',
                technical: 'textStream',
                canImport: 'boolean',
                canExport: 'boolean',
            },
        },
    })
    static async getDefaultCsvTemplate(
        context: Context,
        nodeName: string,
        level = 1,
        isExportWithDefinition = false,
        withSortValue = false,
    ): Promise<{ csvTemplate: TextStream; technical: TextStream; canImport: boolean; canExport: boolean }> {
        const factory = context.application.getFactoryByName(nodeName);

        const headers = await xtremImportExport.functions.getHeaders(
            context,
            factory,
            level,
            isExportWithDefinition,
            withSortValue,
        );

        const nameWithoutCollisions = new Set<string>();
        const names = [
            ...headers.map(header =>
                xtremImportExport.sharedFunctions.getNameWithoutCollision(nameWithoutCollisions, header.name),
            ),
            ...[''], // Let's add an empty header that matches IGNORE
        ].join(defaultDelimiter);
        const types = [...headers.map(header => header.type), ...['IGNORE']].join(defaultDelimiter);
        const descriptions = [...headers.map(header => header.description), ...['IGNORE']].join(defaultDelimiter);
        const locales = [...headers.map(header => header.locale), ...['IGNORE']].join(defaultDelimiter);
        const isCustoms = [...headers.map(header => header.isCustom), ...['IGNORE']].join(defaultDelimiter);

        // Technical properties
        const displayedPropertyTypes = [
            ...headers.map(header => header.technical.displayedPropertyType),
            ...['IGNORE'],
        ].join(defaultDelimiter); // need to be separated by default delimiter because if the delimiter is comma, we are not able to parse it
        const properties = [
            ...headers.map(header =>
                JSON.stringify({
                    name: header.technical.property.name,
                    factory: {
                        name: header.technical.property.factory?.name,
                    },
                    targetFactory: {
                        name: header.technical.property.targetFactory?.name || '',
                    },
                }),
            ),
            ...['IGNORE'],
        ].join(defaultDelimiter);
        return {
            csvTemplate: new TextStream([names, types, descriptions, locales, isCustoms].join('\n'), 'text/csv'),
            technical: new TextStream([properties, displayedPropertyTypes].join('\n'), 'text/csv'),
            canImport: factory.canCreate || (factory.canUpdate && !!factory.naturalKey),
            canExport: factory.canExport,
        };
    }

    @decorators.query<typeof ImportExportTemplate, 'getTechnicalInformation'>({
        isPublished: true,
        parameters: [
            {
                name: 'property',
                type: 'object',
                properties: {
                    name: {
                        type: 'string',
                    },
                    factory: {
                        type: 'object',
                        properties: {
                            name: {
                                type: 'string',
                            },
                        },
                    },
                },
                isMandatory: true,
            },
            {
                name: 'level',
                type: 'integer',
                isMandatory: false,
            },
            {
                name: 'parentProperty',
                type: 'object',
                properties: {
                    name: {
                        type: 'string',
                    },
                    factory: {
                        type: 'object',
                        properties: {
                            name: {
                                type: 'string',
                            },
                        },
                    },
                },
                isMandatory: true,
            },
            {
                name: 'isExportOnly',
                type: 'boolean',
                isMandatory: false,
            },
        ],
        return: {
            type: 'object',
            name: 'technical',
            properties: {
                path: {
                    type: 'string',
                },
                displayedPropertyType: {
                    type: 'enum',
                    dataType: () => xtremImportExport.enums.displayedPropertyTypeDataType,
                },
                dataType: {
                    type: 'string',
                },
                description: {
                    type: 'string',
                },
                locale: {
                    type: 'string',
                },
            },
        },
    })
    static async getTechnicalInformation(
        context: Context,
        property: { name: string; factory: { name: string } },
        level = 1,
        parentProperty: { name: string; factory: { name: string } } | null = null,
        isExportOnly = false,
    ): Promise<{
        path: string;
        displayedPropertyType: xtremImportExport.enums.DisplayedPropertyType | '';
        dataType: string;
        description: string;
        locale: string;
    }> {
        const propertyInstance = xtremImportExport.functions.getPropertyInstance(
            context,
            property.name,
            property.factory.name,
        );
        const parentPropertyInstance = xtremImportExport.functions.getPropertyInstance(
            context,
            parentProperty?.name || '',
            parentProperty?.factory?.name || '',
        );
        const dateFormat = await getUserImportExportDateFormat(context);
        if (!propertyInstance) {
            // If not found as property on factory, check custom fields and return as customData
            const factory = context.application.tryGetFactoryByTableName(property.factory.name);
            const customFieldsDict = factory
                ? await CoreHooks.customizationManager.getCustomFields(context, [factory.fullName])
                : {};
            if (factory && customFieldsDict) {
                const customField =
                    (customFieldsDict[factory.fullName] ?? []).find(field => field.name === property.name) || null;
                return {
                    path: customField ? `_customData(${customField?.name})` : '',
                    displayedPropertyType: 'normalProperty',
                    dataType: customField?.dataType || '',
                    description: customField
                        ? xtremImportExport.functions.getCustomPropertyDescription(context, customField, dateFormat)
                        : '',
                    locale: '', // to add localization in next iteration
                };
            }
            const customProps = await xtremImportExport.functions.getFactoryCustomFields(
                context,
                property.factory.name,
            );
            return {
                path: customProps.includes(property.name) ? `_customData(${property.name})` : '',
                displayedPropertyType: 'normalProperty',
                dataType: 'string',
                description: property.name,
                locale: '', // to add localization in next iteration
            };
        }

        const result: {
            path: string;
            displayedPropertyType: xtremImportExport.enums.DisplayedPropertyType | '';
            dataType: string;
            description: string;
            locale: string;
        } = {
            path: xtremImportExport.functions.getPropertyPath(
                propertyInstance,
                level,
                parentPropertyInstance,
                isExportOnly,
            ),
            displayedPropertyType: xtremImportExport.functions.getDisplayedPropertyType(
                propertyInstance,
                level,
                isExportOnly,
            ),
            dataType: xtremImportExport.functions.getPropertyDataType(propertyInstance),
            description: await xtremImportExport.functions.getPropertyDescription(
                context,
                propertyInstance,
                dateFormat,
            ),
            locale: xtremImportExport.functions.getPropertyLocale(context, propertyInstance),
        };
        return result;
    }

    /**
     * Get a list operations based on the options passed
     * @param options
     */
    private static getControlledOperations(options?: { doInsert?: boolean; doUpdate?: boolean }): string[] {
        const controlledOperations = [];
        if (options?.doInsert) controlledOperations.push('create');
        if (options?.doUpdate) controlledOperations.push('update');
        return controlledOperations;
    }

    /**
     * Validate if the current users can execute the import
     * @param context
     * @param factory
     * @param options
     */
    private static async validateAccess(
        context: Context,
        factory: NodeFactory,
        options?: { doInsert?: boolean; doUpdate?: boolean },
    ): Promise<void> {
        const controlledOperations = this.getControlledOperations(options);
        await asyncArray(controlledOperations).forEach(operation =>
            context.checkThatNodeOperationIsAuthorized(factory.name, operation),
        );
    }

    /**
     * Create a record from the readonly context
     * @param context
     * @param importExportTemplate
     * @param doInsert
     * @param doUpdate
     * @param dryRun
     * @param maxErrorCount
     * @param version
     * @param importStatus
     * @param uploadedFileId
     * @returns
     */
    static createResult(
        context: Context,
        importExportTemplate: ImportExportTemplate,
        doInsert: boolean,
        doUpdate: boolean,
        dryRun: boolean,
        maxErrorCount: number,
        importStatus: ImportStatus,
        uploadedFileId?: string,
        notificationId?: string,
        generalError?: string,
    ): Promise<xtremImportExport.nodes.ImportResult> {
        // set importResult status inProgress
        const startStamp = datetime.now(true);
        return context.runInWritableContext(async importContext => {
            const result = await importContext.create(xtremImportExport.nodes.ImportResult, {
                importExportTemplate: importExportTemplate._id,
                startStamp,
                filename: `${importExportTemplate._id}--${startStamp}.csv`,
                status: importStatus,
                dryRun,
                doInsert,
                doUpdate,
                maxErrorCount,
                uploadedFile: uploadedFileId,
                notificationId,
                generalError,
            });
            await result.$.save();
            return result;
        });
    }

    /**
     * Mutation to initiate an async import job
     * @param context
     * @param templateId
     * @param uploadedFileId
     * @param options
     * @returns
     */
    @decorators.asyncMutation<typeof ImportExportTemplate, 'batchImport'>({
        isPublished: true,
        parameters: [
            { name: 'templateId', type: 'string', isMandatory: true },
            { name: 'uploadedFileId', type: 'string', isMandatory: true },
            {
                name: 'options',
                type: 'object',
                isMandatory: false,
                properties: {
                    doInsert: { type: 'boolean' },
                    doUpdate: { type: 'boolean' },
                    dryRun: { type: 'boolean' },
                    maxErrorCount: { type: 'integer' },
                },
            },
        ],
        return: {
            type: 'instance',
            node(): typeof xtremImportExport.nodes.ImportResult {
                return xtremImportExport.nodes.ImportResult;
            },
        },
        startsReadOnly: true,
    })
    static async batchImport(
        context: Context,
        templateId: string,
        uploadedFileId: string,
        options?: {
            doInsert?: boolean;
            doUpdate?: boolean;
            dryRun?: boolean;
            maxErrorCount?: number;
        },
    ): Promise<xtremImportExport.nodes.ImportResult> {
        const doInsert = options?.doInsert ?? true;
        const doUpdate = options?.doUpdate ?? false;
        const dryRun = options?.dryRun || false;
        const maxErrorCount = options?.maxErrorCount || 0;

        // Read template to verify it exists
        const importExportTemplate = await context.read(xtremImportExport.nodes.ImportExportTemplate, {
            id: templateId,
        });

        if (!['importOnly', 'importAndExport'].includes(await importExportTemplate.templateUse))
            throw new DataInputError(`The ${templateId} template can be used for export only`);

        const notificationId = context.getContextValue('notificationId');
        const factory = context.application.getFactoryByName(await importExportTemplate.nodeName);

        try {
            await this.validateAccess(context, factory, { doInsert, doUpdate });
        } catch (error) {
            await this.createResult(
                context,
                importExportTemplate,
                doInsert,
                doUpdate,
                dryRun,
                maxErrorCount,
                'rejected',
                uploadedFileId,
                notificationId,
                error.message,
            );
            throw error;
        }

        const otherResults = context.query(xtremImportExport.nodes.ImportResult, {
            filter: { uploadedFile: uploadedFileId },
        });

        if ((await otherResults.length) > 0) throw new DataInputError('The uploaded file has already been imported.');

        if (!doInsert && !doUpdate) {
            const generalError = 'Insert and Update flags must not be simultaneously false';
            await context.batch.logMessage('exception', generalError);

            // TODO: improve the visual progress bar to show the progress, here is a hack to 100%
            await context.batch.updateProgress({
                totalCount: 1,
                errorCount: 1,
            });
            await context.batch.end();

            await this.createResult(
                context,
                importExportTemplate,
                doInsert,
                doUpdate,
                dryRun,
                maxErrorCount,
                'rejected',
                uploadedFileId,
                notificationId,
                generalError,
            );

            throw new DataInputError(generalError);
        }

        const importResult = await this.createResult(
            context,
            importExportTemplate,
            doInsert,
            doUpdate,
            dryRun,
            maxErrorCount,
            'pending',
            uploadedFileId,
            notificationId,
        );

        const uploadedFile = await importResult.uploadedFile;
        const uploadedFileStatus = await uploadedFile.status;
        await context.batch.logMessage(
            'info',
            `[import ${importResult._id}] batchImport uploaded file ${uploadedFileId} status ${uploadedFileStatus}`,
        );
        // If the uploaded file is verified (virus scan complete and passed) then we can initiate the processing of the data
        if (uploadedFileStatus === 'verified') {
            await context.runInWritableContext(writableContext =>
                writableContext.notify('UploadedFile/processUpload', { uploadedFileId }),
            );
        } else if (uploadedFileStatus === 'rejected') {
            // The file uploaded has been rejected, we cannot proceed with this import, mark as rejected by upload
            const importResultId = importResult._id;
            await context.runInWritableContext(async writableContext => {
                const importResultForUpdate = await writableContext.tryRead(
                    xtremImportExport.nodes.ImportResult,
                    { _id: importResultId },
                    { forUpdate: true },
                );

                if (!importResultForUpdate) return;

                await context.batch.logMessage(
                    'exception',
                    `[import ${importResult._id}] batchImport uploaded file ${uploadedFileId} status ${uploadedFileStatus}`,
                );

                // TODO: improve the visual progress bar to show the progress, here is a hack to 100%
                await context.batch.updateProgress({
                    totalCount: 1,
                    errorCount: 1,
                });
                await context.batch.end();

                await importResultForUpdate.$.set({
                    status: 'rejected',
                    generalError: await uploadedFile.rejectReason,
                });

                await importResultForUpdate.$.save();
            });
        }

        return importResult;
    }

    static checkFactory(factory: NodeFactory, options: { doInsert: boolean; doUpdate: boolean }) {
        if (
            (options.doInsert && !factory.canCreate) ||
            (options.doUpdate && !factory.canUpdate) ||
            (options.doUpdate && !factory.naturalKey)
        ) {
            throw new DataInputError(`Node ${factory.name} cannot be imported`);
        }
    }

    static validateCsvHeaders(
        dataHeaders: ImportHeader[],
        templateHeaders: ImportHeader[],
        templateName: string,
        options: { doInsert: boolean; doUpdate: boolean },
    ): void {
        // The data file may not contain all the nested headers (level > 1).
        // So we trim templateHeaders to remove the headers for nested properties that are omitted in the data file.
        // For example, if the data file does not have contacts for a business partner, we remove the contact headers from the template headers
        const trimmedTemplateHeaders = templateHeaders.filter(
            header => !header.propertyName || dataHeaders.some(h => h.path.join() === header.path.join()),
        );

        const headerKey = (header: ImportHeader) => {
            // if the column is a non vital ref property (e.g propertyName === '' && csvName ===' customer.name')
            // we need to remove the local postfixed name to get the key (e.g customer.description(en-US) => customer.description)
            const key = header.propertyName === '' ? header.csvName.replace(/\([^()]*\)/g, '') : header.propertyName;
            return [...header.path, key].join('.');
        };

        // Verify that all headers marked as natural keys in the import template are present in the csv file.
        const missingNaturalKeyFields = trimmedTemplateHeaders
            .filter(header => header.belongsToNaturalKey && !dataHeaders.some(h => headerKey(h) === headerKey(header)))
            .map(header => [...header.path, header.csvName].join('.'));

        if (
            missingNaturalKeyFields.length > 0 &&
            !(missingNaturalKeyFields.every(missingKey => missingKey.includes('_sortValue')) && options.doInsert)
        ) {
            throw new DataInputError(
                `invalid csv header: natural keys field(s) ${missingNaturalKeyFields} of template ${templateName} header not found in csv file`,
            );
        }

        const missingMandatoryFields = trimmedTemplateHeaders
            .filter(
                header =>
                    !header.belongsToNaturalKey &&
                    header.isMandatory &&
                    !dataHeaders.some(h => headerKey(h) === headerKey(header)),
            )
            .map(header => [...header.path, header.csvName].join());

        // Verify that all headers marked as mandatory in the import template are present in the csv file in creation mode.
        if (missingMandatoryFields.length > 0 && options.doInsert) {
            throw new DataInputError(
                `invalid csv header: mandatory field(s) ${missingMandatoryFields} of template ${templateName} header not found in csv file`,
            );
        }

        // Verify that all headers marked as mandatory in the csv file are present in the import template
        const missingFieldsInTemplate = dataHeaders
            .filter(
                header =>
                    header.csvName !== '_ignore' &&
                    header.csvName !== '#' &&
                    !trimmedTemplateHeaders.some(h => headerKey(h) === headerKey(header)),
            )
            .map(header => header.csvName);

        if (missingFieldsInTemplate.length > 0) {
            throw new DataInputError(
                `invalid csv header: ${missingFieldsInTemplate} not found in template ${templateName}`,
            );
        }
    }

    private static addError(row: AnyRecord, error: string, propertyName = 'global') {
        const errorMessage = row._error ? `${row._error}\\n` : '';
        row._error = `${errorMessage}${propertyName}: ${error}`;
    }

    private static addErrorToRow(rows: AnyRecord[], diagnose: Diagnose) {
        // path may be :
        // - ['p1'] if the error is on property p1 of the main record (rows[0])
        // - ['p1','3','p2'] if p1 is a collection property and the error is on property p2 of the 4th element of p1
        // - ['p1','3','p2','1','p3'] if the error is deeper into the document
        const path = [...diagnose.path];

        // N.B. this rule is not always respected...
        // Let's look for property's name and _id:
        let propertyName = '';
        let _id = rows[0]._id;
        for (let i = path.length - 1, count = 0; i >= 0 && count < 2; i -= 1, count += 1) {
            if (/^-{0,1}\d+$/.test(path[i])) {
                _id = parseInt(path[i], 10);
            } else {
                propertyName = path[i];
            }
        }
        const row = rows.find(r => r._id === _id) || rows[0];

        ImportExportTemplate.addError(row, diagnose.message, propertyName);
    }

    private static async fixData(
        context: Context,
        factory: NodeFactory,
        data: AnyRecord,
        options: {
            forUpdate: boolean;
            selfReferenceProperties: SelfReferencingProperty[];
            node?: Node;
        },
    ): Promise<void> {
        const { forUpdate, selfReferenceProperties, node } = options;

        await asyncArray(factory.properties).forEach(async property => {
            const value = data[property.name] as any;
            // Custom data
            if (value != null && property.name === '_customData') {
                await ImportExportTemplate.manageCustomData(context, factory, property, data, forUpdate);
            }

            // If value was missing because the column was missing, leave it undefined.
            // Insert will apply the defaultValue rule and update will preserve the old value.
            if (value === undefined) return;

            if (value === null) {
                // Cell was present but empty
                if (forUpdate) {
                    // Make a special case for localized string properties because property.getTypeDefaultValue()
                    // returns an empty object {} instead of an empty string '' in this case.
                    if (property.isLocalized && property.isNotEmpty)
                        throw new DataInputError(`${property.name}: empty value is not allowed for locale "base"`);
                    data[property.name] = property.isLocalized ? '' : property.getTypeDefaultValue();
                } else {
                    // Set the default to undefined to fire the defaultValue rule, if any.
                    data[property.name] = undefined;
                }
            } else if (property.isVital) {
                if (property.isReferenceProperty()) {
                    await this.fixData(context, property.targetFactory, value as unknown as AnyRecord, {
                        forUpdate,
                        selfReferenceProperties,
                    });
                } else if (property.isCollectionProperty() && Array.isArray(value)) {
                    // Does this collection have a self referencing property?
                    await prepareSelfReferencingData(context, value, {
                        property,
                        forUpdate,
                        selfReferenceProperties,
                        node,
                    });
                    // Second loop to fix the data of the collection elements and replace the natural key with the actual _id of the node
                    await asyncArray(value).forEach(async element => {
                        // we need to replace the natural key of the self referencing property with the actual _id of the node
                        if (selfReferenceProperties.length > 0)
                            fixSelfReferencingData(selfReferenceProperties, element, value);
                        await this.fixData(context, property.targetFactory, element, {
                            forUpdate,
                            selfReferenceProperties,
                        });
                    });
                }
            } else if (property.isLocalized) {
                ImportExportTemplate.manageLocalizedString(data, property, forUpdate);
            }
        });
    }

    private static async manageCustomData(
        context: Context,
        factory: NodeFactory,
        property: Property,
        data: AnyRecord,
        forUpdate: boolean,
    ): Promise<void> {
        if (!factory.isCustomizable) return;
        const value = data[property.name] as any;
        const customFieldsDict = await CoreHooks.customizationManager.getCustomFields(context, [factory.fullName]);
        if (customFieldsDict) {
            const customFields = customFieldsDict[factory.fullName] ?? [];

            Object.keys(value).forEach((customFieldKey: string) => {
                if (value[customFieldKey] === null) {
                    const customField = customFields.find(element => element.name === customFieldKey);
                    const isMandatory = JSON.parse(customField?.componentAttributes).isMandatory ?? false;
                    // Cell was present but empty
                    if (forUpdate) {
                        value[customFieldKey] = customFieldTypeDefaultValue(
                            customField?.dataType as TypeName,
                            isMandatory,
                        );
                    } else {
                        if (isMandatory) throw new DataInputError(`Mandatory field ${customFieldKey} value missing`);
                        value[customFieldKey] = undefined;
                    }
                }
            });
            data[property.name] = value;
        }
    }

    private static manageLocalizedString(data: AnyRecord, property: Property, forUpdate: boolean) {
        const value = data[property.name] as any;
        Object.keys(value).forEach(key => {
            if (typeof value[key] === 'string' && value[key] === '') {
                if (forUpdate) {
                    if (key === 'base' && property.isNotEmpty)
                        throw new DataInputError(`${property.name}: empty value is not allowed for locale "base"`);
                } else {
                    // For create, just delete the key and its value
                    delete value[key];
                }
            }
        });

        if (Object.keys(value).length === 0) {
            // In creation mode, a localizedString with empty JSON input raises error if isNotEmpty is true
            if (
                !forUpdate &&
                property.isNotEmpty &&
                property.defaultValue === undefined &&
                property.deferredDefaultValue === undefined
            )
                throw new DataInputError(`${property.name}: empty value not allowed`);
            // if isNotEmpty is false, set to undefined for defaultValue or keep db values
            data[property.name] = undefined;
        }
    }

    private static async loadRecord(
        context: Context,
        factory: NodeFactory,
        options: {
            doInsert: boolean;
            doUpdate: boolean;
            headers: string[];
            payload: xtremImportExport.functions.DataObject;
            selfReferenceProperties?: SelfReferencingProperty[];
        },
    ) {
        const { doInsert, doUpdate, payload, selfReferenceProperties } = options;

        // Copy payload to workingPayload to avoid modifying the original payload
        // (in case of error in the bucket, we re-inject again the same payload)
        const workingPayload = { ...payload };

        let node: Node | null = null;
        const naturalKeyProperties = factory.naturalKey || [];
        const key = lodash.pick(workingPayload, naturalKeyProperties);
        if (doUpdate) {
            if (naturalKeyProperties.length !== Object.keys(key).length)
                throw new DataInputError(
                    `Cannot update record. Missing key values (${naturalKeyProperties
                        .filter(keyPropName => !Object.keys(key).includes(keyPropName))
                        .join()})`,
                );
            node = await context.tryRead(factory.nodeConstructor, key, {
                forUpdate: true,
            });
            delete workingPayload._id;
        }

        if (node) {
            await this.fixData(context, factory, workingPayload, {
                forUpdate: true,
                selfReferenceProperties: selfReferenceProperties ?? [],
                node,
            });
            // remove isVitalParent value from the workingPayload in update case
            if (factory.isVitalChild) delete workingPayload[factory.vitalParentProperty.name];

            // delete natural key properties from the workingPayload in update case
            Object.keys(key).forEach(keyName => delete workingPayload[keyName]);

            await node.$.set(workingPayload);
        } else {
            if (!doInsert) throw new DataInputError('Cannot update record not found');
            await this.fixData(context, factory, workingPayload, {
                forUpdate: false,
                selfReferenceProperties: selfReferenceProperties ?? [],
            });
            node = await context.create(factory.nodeConstructor, workingPayload);
        }
        await node.$.save();
    }

    private static async loadRecordBucket(importContext: Context, jsonBucket: ParsedResultBucket, loadInfo: LoadInfo) {
        const { factory, doInsert, doUpdate, headers, selfReferenceProperties } = loadInfo;
        await asyncArray(jsonBucket.parsedResult).forEach(async ({ payload, rows }, i) => {
            try {
                jsonBucket.currentRows = rows;
                await this.loadRecord(importContext, factory, {
                    doInsert,
                    doUpdate,
                    headers,
                    payload,
                    selfReferenceProperties,
                });
            } catch (error) {
                // catch any validation errors and rethrow db errors
                const unwrappedError = unwrapError(error);
                const isDbError = isDatabaseError(unwrappedError);
                const isDataInError = isDataInputError(unwrappedError);
                // serialization error we throw it to be managed
                if (isDbError && unwrappedError.code === '40001') {
                    throw error;
                }
                logger.debug(() => `Failed to import:${JSON.stringify(payload, null, '\t')}`);
                logger.error(`Error while importing payload:\n${error.stack}`);

                if (importContext.diagnoses.length === 0) {
                    rows[0]._error = error.message;
                } else {
                    importContext.diagnoses.forEach(diagnose => ImportExportTemplate.addErrorToRow(rows, diagnose));
                    importContext.resetDiagnoses();
                }
                jsonBucket.failedRows.push(...rows);
                jsonBucket.objectsInError.push(i);
                if (isDbError || isDataInError) {
                    // we have to rethrow the error to rollback the transaction
                    throw error;
                }
            }
            jsonBucket.rowsProcessed += [...new Set(rows.map(row => row.$lineNumber))].length;
        });
    }

    private static async loadRecordBucketInWritableContext(
        context: Context,
        jsonBucket: ParsedResultBucket,
        loadInfo: LoadInfo,
    ) {
        const { dryRun } = loadInfo;
        await context.runInWritableContext(
            importContext =>
                importContext.withLocalizedTextAsJson(async () => {
                    await ImportExportTemplate.loadRecordBucket(importContext, jsonBucket, loadInfo);
                    // We have to throw here as the context will try to end the transaction, and
                    // there will still be writable nodes marked as unsaved, this will throw a irrelevant error
                    // and pollute the logs
                    if (jsonBucket.objectsInError.length > 0) {
                        throw new Error(`json bucket has errors impacting ${jsonBucket.objectsInError.length} objects`);
                    }
                }),
            { noCommit: dryRun, source: 'import' },
        );
    }

    static async tryLoadRecordBucket(
        context: Context,
        batchContext: BatchContext,
        result: ImportCsvResult,
        jsonBucket: ParsedResultBucket,
        loadInfo: LoadInfo,
        rowsSanitizedInBuckets: AnyRecord[],
    ): Promise<boolean> {
        const { maxErrorCount } = loadInfo;
        try {
            await this.loadRecordBucketInWritableContext(context, jsonBucket, loadInfo);
            result.rowsProcessed += jsonBucket.rowsProcessed;
            await batchContext.logMessage(
                'info',
                `[Import ${jsonBucket.importResultId}] processed ${jsonBucket.rowsProcessed} rows for '${loadInfo.factory.name}' (total: ${result.rowsProcessed}, errors: ${result.failedRows.length})`,
            );
        } catch (error) {
            if (jsonBucket.objectsInError.length === 0) {
                await batchContext.logMessage('error', `Error while importing:${error.stack}`);
                if (!jsonBucket.currentRows?.[0]._error) {
                    jsonBucket.currentRows[0]._error = error.message;
                }
            }
        }
        if (jsonBucket.objectsInError.length > 0) {
            const linesFailed = [...new Set(jsonBucket.failedRows.map(fr => fr.$lineNumber))];
            const failedRows = linesFailed.map((lineFailed: number) => {
                const _error = [
                    ...new Set(
                        jsonBucket.failedRows
                            .filter(failedRow => failedRow.$lineNumber === lineFailed)
                            .map(element => element._error),
                    ),
                ].join(' ');
                const csvRow = rowsSanitizedInBuckets.find(row => row.$lineNumber === lineFailed);
                delete csvRow?.$lineNumber;
                return { ...csvRow, _error };
            });
            result.failedRows = result.failedRows.concat(failedRows);
            result.rowsProcessed += [...new Set(jsonBucket.failedRows.map(fr => fr.$lineNumber))].length;
            result.errorCount += jsonBucket.failedRows.filter(r => r._error).length;
            if (maxErrorCount && result.errorCount >= maxErrorCount) {
                // stop looping
                return false;
            }
            // now remove the failed rows from the bucket and retry
            return this.tryLoadRecordBucket(
                context,
                batchContext,
                result,
                jsonBucket.clean(),
                loadInfo,
                rowsSanitizedInBuckets,
            );
        }
        jsonBucket.reset();
        return true;
    }

    /**
     * Import CSV data from Readable stream
     * @internal
     * @param context
     * @param stream
     * @param importResult
     * @returns
     */
    static async importCsv(
        context: Context,
        batchContext: BatchContext,
        stream: Readable,
        importResult: xtremImportExport.nodes.ImportResult,
    ): Promise<xtremImportExport.nodes.ImportResult> {
        const importResultId = importResult._id;
        const delimiter = await getUserImportExportDelimiter(context);
        const { headers, failedRows, rowsProcessed, status, generalError, dryRun } = await parseCsvStreamTransform(
            stream,
            context,
            batchContext,
            importResult,
        );
        return context
            .runInWritableContext(async writableContext => {
                const importResultEnd = (await writableContext.tryRead(
                    xtremImportExport.nodes.ImportResult,
                    {
                        _id: importResultId,
                    },
                    { forUpdate: true },
                )) as xtremImportExport.nodes.ImportResult;

                if (importResultEnd) {
                    const endStamp = datetime.now(true);
                    const failedRowsArray = failedRows.map(row => Object.values(row) as string[]);
                    await importResultEnd.$.set({
                        numberOfRowsInError: failedRows.filter(row => row._error !== '').length,
                        rowsProcessed,
                        endStamp,
                        rowsInError: new TextStream(stringifyCsv([headers, ...failedRowsArray], delimiter), 'text/csv'),
                        status,
                        generalError,
                        dryRun,
                    });
                    await importResultEnd.$.save();
                    return importResultEnd;
                }
                return null;
            })
            .then(res => res || importResult);
    }

    static async generateExportWithQueryReader(
        context: Context,
        template: ImportExportTemplate,
        selectOption: NodeSelectOptions<AnyNode>,
        doTransformHeader = false,
        targetFormat: 'csv' | 'xlsx' = 'csv',
    ): Promise<void> {
        const batchContext = context.batch;
        let exportPath = '';
        const templateName = await template.name;
        const locale = (await context.user)?.locale;
        const dateTimeNow = xtremDateTime.datetime.now();
        const timestamp = dateTimeNow.format(
            locale ? (locale.replace('_', '-') as LocalizeLocale) : 'base',
            'YYYY-MM-DD-HH-mm-ss',
        );
        const filename = `${lodash.kebabCase(await template.name)}-${timestamp}.${targetFormat}`;
        const _objectKey = `csv-exports/${filename}`;
        try {
            const csvExportRoot = fsp.join(context.tenantId ?? '.', 'csv/exports');
            exportPath = `${csvExportRoot}/${dateTimeNow.value}`;
            fs.mkdirSync(exportPath, { recursive: true });
            const filePath = fsp.join(exportPath, filename);
            const outputStream = fs.createWriteStream(filePath);

            const objectCount = await generateExportData(
                context,
                template,
                outputStream,
                selectOption,
                doTransformHeader,
                undefined,
                targetFormat,
            );
            const { result, objectKey } = await context.runInWritableContext(writableContext => {
                return this.createUploadedFile(writableContext, {
                    kind: 'upload',
                    objectKey: _objectKey,
                    filename,
                    templateName,
                    timestamp,
                });
            });

            await xtremInfrastructureAdapter.InfrastructureHelper.createFile(
                context,
                objectKey,
                `Generated ${targetFormat} - ${templateName}`,
                fs.createReadStream(filePath),
                FileTimeToLive.Expire10Days,
            );

            await batchContext.updateProgress({
                totalCount: objectCount,
                successCount: objectCount,
            });
            await batchContext.logMessage('info', `Processed ${objectCount} ${await template.nodeName}(s)`);
            const uploadedFile = await result;
            await batchContext.logMessage('result', 'Export completed', {
                data: {
                    downloadUrl: uploadedFile.downloadUrl,
                    filename: uploadedFile.filename,
                },
            });

            await sendClientExportNotification(context, {
                status: 'success',
                numberOfRecords: objectCount,
                templateId: await template.id,
                downloadUrl: uploadedFile.downloadUrl,
            });
        } catch (error) {
            // cleanup UploadedFile record
            await context.runInWritableContext(async writableContext => {
                const fileExist = await writableContext.tryRead(
                    xtremUpload.nodes.UploadedFile,
                    { key: _objectKey },
                    { forUpdate: true },
                );
                if (fileExist) {
                    await fileExist.$.delete();
                }
                return true;
            });
            throw error;
        } finally {
            // cleanup
            if (fs.existsSync(exportPath)) fs.rmSync(exportPath, { recursive: true });
        }
    }

    /**
     *
     * @param context
     * @param parameters
     * @private
     */
    static async createUploadedFile(
        context: Context,
        parameters: {
            kind: xtremUpload.enums.UploadedFileKind;
            objectKey: string;
            filename: string;
            templateName: string;
            timestamp: string;
            isArchive?: boolean;
        },
    ): Promise<{ result: Promise<NodePayloadData<xtremUpload.nodes.UploadedFile>>; objectKey: string }> {
        const { objectKey, filename, templateName, timestamp, isArchive } = parameters;
        const maxRetryUploadedFileCreation = ConfigManager.current.exportCsv?.maxRetryUploadedFileCreation ?? 3;
        const createUploadedFileNode = async (key: string, keyFilename: string) => {
            const uploadFileNode = await context.create(xtremUpload.nodes.UploadedFile, {
                key,
                kind: 'upload',
                status: 'verified',
                canSkipAntivirusScan: true,
                filename: keyFilename,
                mimeType: isArchive ? 'application/zip' : 'application/csv',
            });
            await uploadFileNode.$.save();
            return uploadFileNode.$.payload();
        };
        let count = 0;
        let success = false;
        let result;
        let _filename = filename;
        let _objectKey = objectKey;
        while (!success && count <= maxRetryUploadedFileCreation) {
            const fileExist = await context.tryRead(xtremUpload.nodes.UploadedFile, { key: _objectKey });
            if (!fileExist) {
                result = await createUploadedFileNode(_objectKey, _filename);
                success = true;
            } else {
                count += 1;
                await sleepMillis(1000);
                _filename = `${lodash.kebabCase(templateName)}-${timestamp}-(${count}).${isArchive ? 'zip' : 'csv'}`;
                _objectKey = `print-output/${_filename}`;
            }
        }
        if (!success) throw new Error(`UploadedFile creation has failed after ${maxRetryUploadedFileCreation} retries`);
        return { result: result as Promise<NodePayloadData<xtremUpload.nodes.UploadedFile>>, objectKey: _objectKey };
    }

    /**
     *
     * @param context
     * @param parameters
     * @private
     */
    static async getSelectorFromTemplate(template: ImportExportTemplate): Promise<NodeSelector<AnyNode>> {
        const result: string[] = [];
        const csvTemplateContent = (await template.csvTemplate).data;
        csvTemplateContent.forEach(element => {
            result.push(`{ ${element.path} : true }`);
        });
        return result.join(',') as unknown as NodeSelector<AnyNode>;
    }

    /**
     * Retrieves the user access for exporting a template by its ID.
     * @param context The context object.
     * @param templateId The ID of the template.
     * @returns A promise that resolves to the user access for exporting the template.
     * @throws {DataInputError} If the template is not found or cannot be used for export.
     */
    static async getUserAccessOnExportById(context: Context, templateId: string): Promise<UserAccess> {
        try {
            const template = await context.read(xtremImportExport.nodes.ImportExportTemplate, {
                id: templateId,
            });
            if (!['exportOnly', 'importAndExport'].includes(await template.templateUse)) return userAccess.unauthorized;
            const factory = context.application.getFactoryByName(await template.nodeName);
            if (!factory.canExport) throw new Error(`${factory.name}: Export is not supported for this node`);
            return await AccessRights.getUserAccessOnOperations(context, [
                {
                    nodeName: await template.nodeName,
                    operationName: 'read',
                },
            ]);
        } catch (e) {
            throw new DataInputError(`The template ${templateId} is not found`);
        }
    }

    /**
     * Checks if a template can be exported by its ID.
     * @param context - The context object.
     * @param templateId - The ID of the template.
     * @param outputFormat - The desired output format.
     * @param _filter - The filter string.
     * @param options - The export options.
     * @returns A promise that resolves to the user access information.
     */
    static async canExportById(
        options: { action: string; trackingId: string },
        context: Context,
        templateId: string,
    ): Promise<UserAccess> {
        if (options) {
            if (options.action === 'start') {
                if (templateId) {
                    return ImportExportTemplate.getUserAccessOnExportById(context, templateId);
                }
                return userAccess.unauthorized;
            }
            if (options.trackingId) {
                // Look for the tracker to get the nodeName
                const tracker = await context.tryRead(xtremCommunication.nodes.SysNotificationState, {
                    notificationId: options.trackingId,
                });
                if (!tracker) return userAccess.unauthorized;

                // If tracker found then look for template id
                const params = await tracker.parameterValues;
                const paramsTemplateId = params.templateId as string;

                if (paramsTemplateId) return ImportExportTemplate.getUserAccessOnExportById(context, paramsTemplateId);
                return userAccess.unauthorized;
            }
            // action should be stop or track but without tracking id => unauthorized
            return userAccess.unauthorized;
        }
        // if there is no options => unauthorized
        return userAccess.unauthorized;
    }

    /**
     * Retrieves the user access level for exporting a node by its definition.
     * @param context - The context object.
     * @param nodeName - The name of the node.
     * @returns A Promise that resolves to the user access level.
     * @throws {DataInputError} If the factory for the given node name is not found.
     */
    static getUserAccessOnExportByDefinition(context: Context, nodeName: string): Promise<UserAccess> {
        try {
            const factory = context.application.getFactoryByName(nodeName);
            if (!factory.canExport) throw new Error(`${factory.name}: Export is not supported for this node`);
        } catch (e) {
            throw new DataInputError(e.message);
        }
        return AccessRights.getUserAccessOnOperations(context, [{ nodeName, operationName: 'read' }]);
    }

    /**
     * Determines if the export can be performed based on the provided template definition and options.
     * @param context - The context object.
     * @param templateDefinition - The header definition array.
     * @param nodeName - The name of the node.
     * @param _outputFormat - The output file format.
     * @param _filter - The filter string.
     * @param _orderBy - The order by string.
     * @param options - The export options.
     * @returns A promise that resolves to the user access information.
     */
    static async canExportByDefinition(
        options: { action: string; trackingId: string },
        context: Context,
        _templateDefinition: HeaderDefinition[],
        nodeName: string,
    ): Promise<UserAccess> {
        if (options) {
            if (options.action === 'start') {
                if (nodeName) {
                    return ImportExportTemplate.getUserAccessOnExportByDefinition(context, nodeName);
                }
                // If there is no nodeName => unauthorized
                return userAccess.unauthorized;
            }
            if (options.trackingId) {
                // Look for the tracker to get the nodeName
                const tracker = await context.tryRead(xtremCommunication.nodes.SysNotificationState, {
                    notificationId: options.trackingId,
                });
                if (!tracker) return userAccess.unauthorized;

                // If tracker found then look for node name
                const params = await tracker.parameterValues;
                const paramsNodeName = params.nodeName as string;

                if (paramsNodeName)
                    return ImportExportTemplate.getUserAccessOnExportByDefinition(context, paramsNodeName);
                return userAccess.unauthorized;
            }
            // action should be stop or track but without tracking id => unauthorized
            return userAccess.unauthorized;
        }
        // if there is no options => unauthorized
        return userAccess.unauthorized;
    }

    /**
     * Mutation to initiate an async export job
     * @param context
     * @param templateId
     * @param outputFormat
     * @param filter
     * @returns
     */
    @decorators.asyncMutation<typeof ImportExportTemplate, 'exportByTemplateId'>({
        isPublished: true,
        isGlobal: true,
        authorizedBy: 'canExportById',
        parameters: [
            { name: 'templateId', type: 'string', isMandatory: true },
            {
                name: 'outputFormat',
                type: 'enum',
                dataType: () => fileFormatEnumDataType,
                isMandatory: true,
            },
            { name: 'filter', type: 'string', isMandatory: true },
        ],
        return: { type: 'string' },
        queue: 'import-export',
        startsReadOnly: true,
    })
    static exportByTemplateId(
        context: Context,
        templateId: string,
        outputFormat: FileFormat,
        filter: string,
    ): Promise<string> {
        return CoreHooks.importExportManager.executeExport(context, templateId, outputFormat, filter, '{}');
    }

    /**
     *
     * @param context
     * @param templateDefinition
     * @param nodeName
     * @param outputFormat
     * @param filter
     * @param orderBy
     * @returns
     */
    @decorators.asyncMutation<typeof ImportExportTemplate, 'exportByTemplateDefinition'>({
        isPublished: true,
        isGlobal: true,
        authorizedBy: 'canExportByDefinition',
        parameters: [
            {
                name: 'templateDefinition',
                type: 'array',
                item: {
                    type: 'object',
                    properties: {
                        path: 'string',
                        title: 'string',
                    },
                },
                isMandatory: true,
            },
            { name: 'nodeName', type: 'string', isMandatory: true },
            {
                name: 'outputFormat',
                type: 'enum',
                dataType: () => fileFormatEnumDataType,
                isMandatory: true,
            },
            { name: 'filter', type: 'string', isMandatory: true },
            { name: 'orderBy', type: 'string', isMandatory: true },
        ],
        return: { type: 'string' },
        queue: 'import-export',
        startsReadOnly: true,
    })
    static exportByTemplateDefinition(
        context: Context,
        templateDefinition: HeaderDefinition[],
        nodeName: string,
        outputFormat: FileFormat,
        filter: string,
        orderBy: string,
    ): Promise<string> {
        return CoreHooks.importExportManager.executeExportByDefinition(
            context,
            templateDefinition,
            nodeName,
            outputFormat,
            filter,
            orderBy,
        );
    }
}
