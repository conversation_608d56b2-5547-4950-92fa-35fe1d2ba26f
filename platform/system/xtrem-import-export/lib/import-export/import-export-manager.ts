import {
    AccessRights,
    Context,
    CsvTemplateContent,
    DataInputError,
    Dict,
    ExportTemplate,
    HeaderDefinition,
    PagingFilter,
    PagingOrderBy,
    getNameWithoutPackage,
} from '@sage/xtrem-core';
import { FileFormat } from '../enums/_index';
import { sendClientExportNotification } from '../functions/export-utils';
import * as xtremImportExport from '../index';
import { checkOutputFormat } from '../nodes/utils';

export const importExportManager = {
    async executeExport(
        context: Context,
        id: string,
        outputFormat: FileFormat,
        filter: string,
        orderBy: string,
    ): Promise<string> {
        const batchContext = context.batch;
        checkOutputFormat(outputFormat);
        try {
            if (!id) throw new Error('Import Export template cannot be null');

            const template = await context.read(xtremImportExport.nodes.ImportExportTemplate, {
                id,
            });

            if (!['exportOnly', 'importAndExport'].includes(await template.templateUse))
                throw new Error(`The ${await template.name} template can be used for import only`);

            const factory = context.application.getFactoryByName(await template.nodeName);
            if (!factory.canExport) throw new Error(`${factory.name}: Export is not supported for this node`);

            const filterParsed = await PagingFilter.parseFilter(context, factory, filter);
            const orderByParsed = await PagingOrderBy.parseOrderBy(context, factory, orderBy);
            const user = await context.user;
            await batchContext.logMessage(
                'info',
                `userEmail: ${user?.email}, templateId: ${id}, templateName: ${await template.name}`,
            );

            // We need to check the access rights for the user and load the whole user access rights because we are
            // in batch context (not the initial graphQL context)
            await AccessRights.checkOperationAccessRight(context, {
                nodeName: await template.nodeName,
                operationName: 'read',
            });

            await xtremImportExport.nodes.ImportExportTemplate.generateExportWithQueryReader(context, template, {
                filter: filterParsed,
                orderBy: orderByParsed,
            });
        } catch (error) {
            await batchContext.logMessage('error', error.message);
            await batchContext.updateProgress({
                totalCount: 1,
                errorCount: 1,
            });
            await sendClientExportNotification(context, {
                status: 'error',
                rejectReason: error.message,
                templateId: id,
            });
            throw error;
        } finally {
            await batchContext.end();
        }
        return 'Finished';
    },

    async executeExportByDefinition(
        context: Context,
        templateDefinition: HeaderDefinition[],
        nodeName: string,
        outputFormat: FileFormat,
        filter: string,
        orderBy: string,
    ): Promise<string> {
        const batchContext = context.batch;
        try {
            checkOutputFormat(outputFormat);
            const factory = context.application.getFactoryByName(nodeName);
            if (!factory.canExport) throw new Error(`${factory.name}: Export is not supported for this node`);

            const headers = await xtremImportExport.functions.getHeaders(context, factory, 1, true, false);
            const csvTemplateContent: CsvTemplateContent[] = [];
            let currentId = 1;

            templateDefinition.forEach(element => {
                const path = element.path.startsWith('_customData.')
                    ? `_customData(${element.path.replace('_customData.', '')})`
                    : element.path;

                const header = headers.find(
                    // Remove the '*' and '!' characters at the beginning of the header name and also (_id) if it exists
                    elt => elt.name.replace(/^[*!]/, '').replace('(_id)', '') === path.split('.')[0],
                );

                if (!header) throw new DataInputError(`Invalid path '${path}' for ${nodeName} node`);

                csvTemplateContent.push({
                    _id: currentId,
                    path,
                    description: element.title,
                    dataType: header.type,
                    locale: '',
                    isCustom: header.isCustom,
                });
                currentId += 1;
            });

            const templateId = `${nodeName}-${Date.now()}`;
            const templateForExportPayload = {
                id: templateId,
                name: templateId,
                code: templateId,
                nodeName,
                description: `Temporary template for ${nodeName}`,
                csvTemplate: {
                    data: csvTemplateContent,
                },
            };
            const template = await context.create(
                xtremImportExport.nodes.ImportExportTemplate,
                templateForExportPayload,
                {
                    isTransient: true,
                },
            );

            const filterParsed = await PagingFilter.parseFilter(context, factory, filter);
            const orderByParsed = await PagingOrderBy.parseOrderBy(context, factory, orderBy);

            const user = await context.user;
            await batchContext.logMessage(
                'info',
                `userEmail: ${user?.email}, templateId: ${templateId}, templateName: Temporary template for ${nodeName}`,
            );

            // We need to check the access rights for the user and load the whole user access rights because we are
            // in batch context (not the initial graphQL context)
            await AccessRights.checkOperationAccessRight(context, {
                nodeName: await template.nodeName,
                operationName: 'read',
            });

            await xtremImportExport.nodes.ImportExportTemplate.generateExportWithQueryReader(
                context,
                template,
                {
                    filter: filterParsed,
                    orderBy: orderByParsed,
                },
                true,
                outputFormat,
            );
        } catch (error) {
            await batchContext.updateProgress({
                totalCount: 1,
                errorCount: 1,
            });
            await sendClientExportNotification(context, {
                status: 'error',
                rejectReason: error.message,
                templateId: nodeName,
            });
            throw error;
        } finally {
            await batchContext.end();
        }
        return 'Finished';
    },

    async getNodeExportTemplates(context: Context, nodeNames: string[]): Promise<Dict<ExportTemplate[]>> {
        const namesWithoutPackage = nodeNames.map(getNameWithoutPackage);
        const filterNodeNames = namesWithoutPackage.filter(nodeName => {
            const factory = context.application.getFactoryByName(nodeName);
            return factory.canExport;
        });

        const exportTemplates =
            filterNodeNames.length === 0
                ? []
                : await context.select(
                      xtremImportExport.nodes.ImportExportTemplate,
                      { nodeName: true, id: true, name: true, isDefault: true },
                      {
                          filter: {
                              nodeName: { _in: filterNodeNames },
                              isActive: true,
                              templateUse: {
                                  _in: ['exportOnly', 'importAndExport'],
                              },
                          },
                      },
                  );

        return namesWithoutPackage.reduce(
            (r, nodeName) => {
                const factory = context.application.getFactoryByName(nodeName);
                if (!factory.canExport) {
                    r[factory.fullName] = [];
                    return r;
                }
                r[factory.fullName] = exportTemplates.filter(template => template.nodeName === nodeName);
                return r;
            },
            {} as Dict<ExportTemplate[]>,
        );
    },
    getExportPageUrl(): string {
        return '@sage/xtrem-import-export/ExportBulkMutationConfiguration';
    },
};
