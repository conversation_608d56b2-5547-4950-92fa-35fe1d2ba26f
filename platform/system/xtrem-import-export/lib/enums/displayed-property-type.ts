import { EnumDataType } from '@sage/xtrem-core';

export enum DisplayedPropertyTypeEnum {
    mainKey = 1,
    normalProperty,
    vitalCollection,
    vitalReference,
    collection,
    reference,
}

export type DisplayedPropertyType = keyof typeof DisplayedPropertyTypeEnum;

export const displayedPropertyTypeDataType = new EnumDataType<DisplayedPropertyType>({
    enum: DisplayedPropertyTypeEnum,
    filename: __filename,
});
