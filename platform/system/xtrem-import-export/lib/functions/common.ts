import {
    asyncArray,
    Context,
    CoreHooks,
    DatabaseError,
    DataInputError,
    EnumDataType,
    NodeFactory,
    Property,
    ReferenceProperty,
} from '@sage/xtrem-core';
import { MetaCustomField } from '@sage/xtrem-shared';
import * as xtremSystem from '@sage/xtrem-system';
import { UserImportExportDateFormat } from '@sage/xtrem-system-api';
import * as lodash from 'lodash';
import { promisify } from 'util';
import { ImportStatus } from '../enums/import-status';
import * as xtremImportExport from '../index';
import { ParsedResult } from './csv-to-json';
import { SelfReferencingProperty } from './export-utils';

/** @internal */
export const allowedDecimalPoint = [',', '.'];
/** @internal */
export const defaultDelimiter = ';';
export interface NodeExportOption {
    exportId?: string;
    rootDir?: string;
    location?: string;
    directoryName?: string;
}

export interface LoadInfo {
    factory: NodeFactory;
    maxErrorCount: number | null;
    doInsert: boolean;
    doUpdate: boolean;
    headers: string[];
    chunkSize: number;
    dryRun?: boolean;
    selfReferenceProperties: SelfReferencingProperty[];
}

export function isDatabaseError(error: Error): error is DatabaseError {
    return error instanceof DatabaseError;
}

export function isDataInputError(error: Error): error is DataInputError {
    return error instanceof DataInputError;
}

export class ParsedResultBucket {
    #parsedResults: ParsedResult[] = [];

    objectsInError: number[] = [];

    rowsProcessed = 0;

    currentRows: xtremImportExport.dataTypes.ImportResultRow[] = [];

    failedRows: xtremImportExport.dataTypes.ImportResultRow[] = [];

    constructor(readonly importResultId: number) {}

    get parsedResult() {
        return this.#parsedResults;
    }

    get size() {
        return this.#parsedResults.length;
    }

    push(json: ParsedResult): ParsedResultBucket {
        this.#parsedResults.push(json);
        return this;
    }

    clean(): ParsedResultBucket {
        const parsedResult = this.#parsedResults.filter((_unused, i) => !this.objectsInError.includes(i));
        this.reset();
        this.#parsedResults = parsedResult;
        return this;
    }

    reset(): ParsedResultBucket {
        this.#parsedResults = [];
        this.objectsInError = [];
        this.currentRows = [];
        this.failedRows = [];
        this.rowsProcessed = 0;
        return this;
    }
}

export interface ImportCsvResult {
    headers: string[];
    status: ImportStatus;
    rowsProcessed: number;
    failedRows: xtremImportExport.dataTypes.ImportResultRow[];
    errorCount: number;
    generalError?: string;
    dryRun?: boolean;
}

export interface ImportExportParameters {
    decimalPoint: string;
    quoteCharacter: string;
    escapeCharacter: string;
}

export function isPropertyMandatory(prop: Property): boolean {
    // if prop has a default value rule, it is not mandatory (even if it is _required_ by the UI)
    if (prop.defaultValue) return false;
    // if prop is required mark it as mandatory
    if (
        prop.isRequired ||
        (prop.isStringProperty() && prop.isNotEmpty) ||
        (!prop.isNullable && prop.getTypeDefaultValue() == null)
    ) {
        return true;
    }
    // otherwise prop.getTypeDefaultValue will provide a valid value (type-wise)
    return false;
}

interface Header {
    name: string;
    type: string;
    description: string;
    locale: string;
    isCustom: boolean;
}

interface HeaderWithTechnical extends Header {
    technical: {
        property: Partial<{
            name: string;
            factory: {
                name: string;
            } | null;
            targetFactory: {
                name: string;
            } | null;
        }>;
        displayedPropertyType: xtremImportExport.enums.DisplayedPropertyType;
    };
}

export function getPropertyInstance(context: Context, propertyName: string, factoryName: string): Property | null {
    if (!propertyName || propertyName === '') return null;
    if (!factoryName || factoryName === '') return null;
    // Cannot read from Property as it is abstract
    const currentFactory = context.application.getFactoryByName(factoryName);
    if (currentFactory) {
        return currentFactory.properties.find(prop => prop.name === propertyName) ?? null;
    }
    return null;
}

const isExportWithDefinitionAllowedSystemPropertiesList = ['_id', '_createStamp', '_updateStamp'];

export function getFactoryProperties(
    factory: NodeFactory,
    level = 1,
    isExportWithDefinition = false,
    withSortValue = true,
): Property[] {
    return factory.properties.filter(prop => {
        if (
            prop.isStored ||
            (!prop.isStored && isExportWithDefinition) ||
            prop.isCollectionProperty() ||
            prop.isReferenceProperty() ||
            prop.isTransientInput ||
            prop.delegatesTo ||
            prop.isMutable
        ) {
            if (prop.isCollectionProperty() && !prop.isVital) return false;
            if (prop.isReferenceProperty()) {
                if (prop.isMutable && prop.targetFactory.isContentAddressable) {
                    return !factory.properties.some(
                        property => property.delegatesTo && property.getDelegatingInfo().reference.name === prop.name,
                    );
                }
                if (!prop.isVital && !prop.targetFactory.naturalKey) return isExportWithDefinition;
            }
            if (
                (!prop.isOutputOnly || isExportWithDefinition) &&
                (!prop.isSystemProperty ||
                    (isExportWithDefinitionAllowedSystemPropertiesList.includes(prop.name) &&
                        isExportWithDefinition)) &&
                !(prop.isVitalParent && level > 1) &&
                (withSortValue || (!withSortValue && prop.name !== '_sortValue'))
            )
                return true;
        }
        return false;
    });
}

export async function getFactoryCustomFields(context: Context, factoryName: string): Promise<string[]> {
    const factory = context.application.tryGetFactoryByTableName(factoryName);
    if (!factory) return Promise.resolve([]);
    const { fullName } = factory;
    const customFieldsDict = await CoreHooks.customizationManager.getCustomFields(context, [fullName]);
    if (customFieldsDict) return customFieldsDict[fullName]?.map(item => item.name) || Promise.resolve([]);
    return Promise.resolve([]);
}

export async function getHeaders(
    context: Context,
    factory: NodeFactory,
    level = 1,
    isExportWithDefinition = false,
    withSortValue = false,
): Promise<HeaderWithTechnical[]> {
    const dateFormat = await getUserImportExportDateFormat(context);
    const nextLevel = [] as Property[];
    const isCustom = false;
    let headersList = await asyncArray(getFactoryProperties(factory, level, isExportWithDefinition, withSortValue))
        .filter(async prop => !!(await prop.isEnabledByServiceOptions(context)))
        .filter(prop => !(prop.isStringProperty() && prop.isStoredEncrypted))
        .filter(prop => {
            if (isExportWithDefinition && prop.isReferenceProperty() && prop.isVital) return true;
            if (prop.isVital || prop.isMutable) {
                nextLevel.push(prop);
                return false;
            }
            return true;
        })
        .map(async (property: Property) => {
            let prefix = '';
            if (property.factory.naturalKey && property.factory.naturalKey.includes(property.name)) {
                // Keys in each node will be prefixed with !
                prefix = '!';
            } else if (isPropertyMandatory(property)) {
                // Mandatory properties are prefixed with *
                prefix = '*';
            }
            let suffix = '';
            if (property.isReferenceProperty()) {
                if (!property.isVital && !property.targetFactory.naturalKey) {
                    suffix = '(_id)';
                }
            }

            return {
                name: `${prefix}${property.name}${suffix}`,
                type: getPropertyDataType(property),
                description: await getPropertyDescription(context, property, dateFormat),
                locale: getPropertyLocale(context, property),
                isCustom,
                technical: {
                    property: {
                        name: property.name,
                        factory: {
                            name: property.factory.name,
                        },
                        targetFactory: {
                            name:
                                property.isReferenceProperty() || property.isCollectionProperty()
                                    ? property.targetFactory.name
                                    : '',
                        },
                    },
                    displayedPropertyType: getDisplayedPropertyType(property, level),
                },
            } as HeaderWithTechnical;
        })
        .toArray();

    // custom fields
    const customFieldsDict = await CoreHooks.customizationManager.getCustomFields(context, [factory.fullName]);
    if (customFieldsDict) {
        const otherResults = customFieldsDict[factory.fullName] ?? [];

        otherResults.forEach(customField => {
            const anchorIndex = headersList.findIndex(property =>
                ['!', '*'].includes(property.name.substring(0, 1))
                    ? property.name.substring(1) === customField.anchorPropertyName
                    : property.name === customField.anchorPropertyName,
            );
            const insertPosition = customField.anchorPosition === 'before' ? anchorIndex : anchorIndex + 1;

            const element = {
                name: `_customData(${customField.name})`,
                type: customField.dataType,
                description: getCustomPropertyDescription(context, customField, dateFormat),
                locale: '', // to add localization in next iteration
                isCustom: true,
                technical: {
                    property: { name: customField.name, factory: { name: factory.name }, targetFactory: null },
                    displayedPropertyType: 'normalProperty',
                },
            } as HeaderWithTechnical;
            headersList.splice(insertPosition, 0, element);
        });
    }

    if (!isExportWithDefinition)
        await asyncArray(nextLevel).forEach(async (property: Property) => {
            const targetFactory = property.isForeignNodeProperty() ? property.targetFactory : undefined;
            if (!targetFactory) throw property.logicError('missing target factory');
            const prefix = property.isCollectionProperty() ? '#' : '/';

            headersList.push({
                name: `${prefix.repeat(level)}${property.name}`,
                type: property.type,
                description: await getPropertyDescription(context, property, dateFormat),
                locale: getPropertyLocale(context, property),
                isCustom,
                technical: {
                    property: {
                        name: property.name,
                        factory: {
                            name: property.factory.name,
                        },
                        targetFactory: {
                            name:
                                property.isReferenceProperty() || property.isCollectionProperty()
                                    ? property.targetFactory.name
                                    : '',
                        },
                    },
                    displayedPropertyType: getDisplayedPropertyType(property, level),
                },
            });
            headersList = headersList.concat(
                await getHeaders(context, targetFactory, level + 1, isExportWithDefinition, withSortValue),
            );
        });

    return headersList;
}

export function getDisplayedPropertyType(
    property: Property,
    level = 1,
    isExportOnly = false,
): xtremImportExport.enums.DisplayedPropertyType {
    let displayedPropertyType: xtremImportExport.enums.DisplayedPropertyType = 'normalProperty';
    if (property.isVital) {
        displayedPropertyType = property.type === 'collection' ? 'vitalCollection' : 'vitalReference';
    } else {
        if (!isExportOnly && property.factory.naturalKey && property.factory.naturalKey.includes(property.name)) {
            displayedPropertyType = level === 1 ? 'mainKey' : 'reference';
        }
        if (property.type === 'reference') {
            displayedPropertyType = 'reference';
        }
    }
    return displayedPropertyType;
}

export function getPropertyDataType(property: Property): string {
    switch (property.type) {
        case 'string':
            return property.isLocalized ? 'localized text' : property.type;
        case 'enum':
            return `enum(${(property.dataType as EnumDataType).values.join(',')})`;
        case 'enumArray':
            return `enumArray(${(property.dataType as EnumDataType).values.join(',')})`;
        default:
            break;
    }
    return property.type;
}

export function getPropertyPath(
    property: Property,
    level = 1,
    parentProperty: Property | null = null,
    isExportOnly = false,
): string {
    let prefix = '';
    let suffix = '';
    if (!property.isVital) {
        if (property.factory.naturalKey && property.factory.naturalKey.includes(property.name)) {
            // Keys in each node will be prefixed with !
            prefix = '!';
        } else if (isPropertyMandatory(property)) {
            // Mandatory properties are prefixed with *
            prefix = '*';
        }
        if (property.isReferenceProperty()) {
            if (!property.isVital && !property.targetFactory.naturalKey) {
                suffix = '(_id)';
            }
        }
    } else {
        prefix = (property.isCollectionProperty() ? '#' : '/').repeat(level);
        // Vitals of a non-vital parent reference
    }
    // For non-vital parent references
    if (
        parentProperty &&
        (isExportOnly ||
            (parentProperty.isReferenceProperty() && !parentProperty.isVital) ||
            parentProperty.isCollectionProperty())
    ) {
        prefix = `${prefix}${parentProperty.name}.`;
    }
    return `${prefix}${property.name}${suffix}`;
}

function getUserImportExportDateFormatDescription(context: Context, dateFormat: UserImportExportDateFormat): string {
    return context.localizeEnumMember('@sage/xtrem-system/UserImportExportDateFormat', dateFormat);
}
// eslint-disable-next-line require-await
export async function getPropertyDescription(
    context: Context,
    property: Property,
    dateFormat: UserImportExportDateFormat,
): Promise<string> {
    const description = lodash.snakeCase(property.name).replace(/_/g, ' ');
    switch (property.type) {
        case 'string':
            return property.isLocalized ? getLocalizedPropertyDescription(context, description) : description;

        case 'boolean':
            return `${description} (${
                typeof property.defaultValue === 'boolean' && property.defaultValue === true
                    ? 'true/false'
                    : 'false/true'
            })`;
        case 'date':
            return `${description} (${getUserImportExportDateFormatDescription(context, dateFormat)})`;
        case 'datetime':
            return `${description} (${getUserImportExportDateFormatDescription(context, dateFormat)}Thh:mm:ssZ)`;
        case 'reference':
            return getReferencePropertyDescription(property as ReferenceProperty, description);
        default:
            return description;
    }
}

export function getCustomPropertyDescription(
    context: Context,
    property: MetaCustomField,
    dateFormat: UserImportExportDateFormat,
): string {
    const description = lodash.snakeCase(property.name).replace(/_/g, ' ');
    switch (property.dataType) {
        case 'boolean':
            return `${description} (false/true)`;
        case 'date':
            return `${description} (${getUserImportExportDateFormatDescription(context, dateFormat)})`;
        default:
            return description;
    }
}

export async function getLocalizedPropertyDescription(context: Context, description: string): Promise<string> {
    return `"default locale: ${
        context.defaultLocale
    }, other locales can be specified with (locale-name), for example ""${description} (de-DE)"". Available locales are (${await asyncArray(
        await context.query(xtremSystem.nodes.Locale).toArray(),
    )
        .map(locale => locale.id)
        .join(', ')}, ...). You can also duplicate the columns to import several translations"`;
}

export async function getUserImportExportDelimiter(context: Context): Promise<string> {
    const userInfo = await context.user;
    if (userInfo) {
        const user = await xtremSystem.nodes.User.fromUserInfo(context, userInfo);
        const userPreferences = await user.preferences;
        if (userPreferences) {
            const importExportDelimiter = await userPreferences.importExportDelimiter;
            return importExportDelimiter === '' ? defaultDelimiter : importExportDelimiter;
        }
    }
    // The default one is ";""
    return defaultDelimiter;
}

export async function getUserImportExportDateFormat(context: Context): Promise<UserImportExportDateFormat> {
    const userInfo = await context.user;
    if (userInfo) {
        const user = await xtremSystem.nodes.User.fromUserInfo(context, userInfo);
        const userPreferences = await user.preferences;
        if (userPreferences) return userPreferences.importExportDateFormat;
    }
    return 'isoDash';
}

export function getPropertyLocale(context: Context, property: Property): string {
    return property.isLocalized ? context.defaultLocale : '';
}

export function getReferencePropertyDescription(property: ReferenceProperty, description: string): string {
    const targetFactory = property.targetFactory;
    if (!targetFactory.naturalKey) return description;
    return `${description} (#${targetFactory.naturalKey.join('|')})`;
}

export async function sleepMillis(ms: number): Promise<void> {
    await promisify(setTimeout)(ms);
}
