import { Batch<PERSON>ontext } from '@sage/xtrem-communication';
import {
    AnyR<PERSON>ord,
    AnyValue,
    Context,
    CoreHooks,
    CsvTemplateContent,
    Dict,
    NodeFactory,
    Property,
    ValidationSeverity,
    asyncArray,
} from '@sage/xtrem-core';
import { Decimal } from '@sage/xtrem-decimal';
import { DataInputError, createPlainObject } from '@sage/xtrem-shared';
import { UserImportExportDateFormat } from '@sage/xtrem-system/lib/enums/user-import-export-date-format';
import * as lodash from 'lodash';
import { getUserImportExportDateFormat } from './common';
import { convertDateFormat } from './export-utils';

/**
 * Header information for each column of the CSV file
 */
export interface ImportHeader {
    /** Header name returned by the cvs parser. For example: #lines, item(id) */
    csvName: string;
    /** Property name in the json payload. For example: lines, item */
    propertyName: string;
    /** Is the columns mandatory? */
    isMandatory: boolean;
    /** Does the column belong to the index */
    belongsToNaturalKey: boolean;
    /** Is the column the first one of an array of sub objects? (column header starting with #) */
    isArray: boolean;
    /** Is the column the first one of an array of sub objects? (column header starting with /) */
    isVitalReference: boolean;
    /** is an array of a vital reference */
    isArrayOrVitalReference: boolean;
    /** is it an exported property only (often path from a non-vital reference: nonVitalProp.nonVitalSubProp.... etc.) */
    isExportOnly?: boolean;
    /** is it an date type custom data*/
    isCustomDataDateType?: boolean;
    /** path in the json payload */
    path: string[];
    /** locale of localized string*/
    locale: string;
    /** Title of the header if we need to replace the standard one by marked up one (for export by definition) */
    title: string;
    /** Keys for the subobject: for example ['code', 'name'] if csvName is "item(code|name)". Applies only to references */
    keyColumns: string[];

    /** The property */
    property?: Property;

    parseCellValue(value: string): AnyValue;
}

// Regexp to split the header name into 4 parts (before, jsonName and keys).
// - before contains the leading # or * or / (# and / may be repeated): (#*|\*)
// - propertyName is the name of the property: (\w+)
// - keys is an optional parenthesized list of keys separated by '|' or ',' (only present in reference properties) or a single locale:
//      (?:\(([a-z][a-zA-Z-]+|_?[a-z][a-zA-Z0-9]*(?:\|_?[a-z][a-zA-Z0-9]*)*)\))?
// - suffix (which prevents name collisions and is not captured here): (?:#.*!)?
const csvHeaderRegex =
    /^(#*|\/*|\*|!)(\w+)(?:\(([a-z][a-zA-Z-]+|_?[a-z][a-zA-Z0-9]*(?:[|,]_?[a-z][a-zA-Z0-9]*)*)\))?(?:#.*)?$/;

async function getHeaderPropertyFromFlattenedCsv(
    batchContext: BatchContext,
    doCheckValidProperty: boolean,
    factory: NodeFactory,
    path: string[],
    propertyName: string,
): Promise<Property | undefined> {
    if (propertyName === '_ignore') return undefined;
    try {
        if (path.length === 0) return factory.findProperty(propertyName, { includeSystemProperties: true });
    } catch (error) {
        if (doCheckValidProperty) {
            throw error;
        }
        const msg = batchContext.context.localize(
            '@sage/xtrem-import-export/functions__csv_to_json__missing_property_header',
            'Property {{propertyName}} not found in the host factory {{factoryName}}.',
            {
                propertyName,
                factoryName: factory.name,
            },
        );
        await batchContext.logMessage('warning', msg);
        batchContext.context.addDiagnoseAtPath(ValidationSeverity.warn, ['csvtemplate'], msg);
        return undefined;
    }
    const [firstName, ...remainingPath] = path;
    const property = factory.findProperty(firstName, { includeSystemProperties: true });
    if (!property.isForeignNodeProperty())
        throw new Error(`${property.fullName}: invalid header sub-path: ${remainingPath}`);
    return getHeaderPropertyFromFlattenedCsv(
        batchContext,
        doCheckValidProperty,
        property.targetFactory,
        remainingPath,
        propertyName,
    );
}

async function getNonVitalReferencePropertyHeader(
    batchContext: BatchContext,
    doCheckValidProperty: boolean,
    factory: NodeFactory,
    path: string[],
): Promise<Property | undefined> {
    const cleanedPath = path.map(elt => elt.replace(/[*#!]/g, ''));
    try {
        if (cleanedPath.length === 1) {
            const propertyFinal = factory.findProperty(cleanedPath[0], { includeSystemProperties: true });
            if (
                !(await propertyFinal.isAuthorized(batchContext.context)) ||
                !(await propertyFinal.isEnabledByServiceOptions(batchContext.context))
            )
                return undefined;
            return propertyFinal;
        }
    } catch (error) {
        if (doCheckValidProperty) {
            throw error;
        }
        const msg = batchContext.context.localize(
            '@sage/xtrem-import-export/functions__csv_to_json__missing_property_header',
            'Property {{propertyName}} not found in the host factory {{factoryName}}.',
            {
                propertyName: cleanedPath[0],
                factoryName: factory.name,
            },
        );
        await batchContext.logMessage('warning', msg);
        batchContext.context.addDiagnoseAtPath(ValidationSeverity.warn, ['csvtemplate'], msg);
        return undefined;
    }
    const [firstName, ...remainingPath] = cleanedPath;
    const property = factory.findProperty(firstName, { includeSystemProperties: true });
    if (!property.isForeignNodeProperty())
        throw new Error(`${property.fullName}: invalid header sub-path: ${remainingPath}`);
    if (
        !(await property.isAuthorized(batchContext.context)) ||
        !(await property.isEnabledByServiceOptions(batchContext.context))
    )
        return undefined;
    return getNonVitalReferencePropertyHeader(
        batchContext,
        doCheckValidProperty,
        property.targetFactory,
        remainingPath,
    );
}

function getHostFactoryOfCustomField(factory: NodeFactory, path: string[], customField: string): NodeFactory {
    if (path.length === 0) return factory;
    const [firstName, ...remainingPath] = path;
    const property = factory.findProperty(firstName, { includeSystemProperties: true });
    if (!property.isForeignNodeProperty())
        throw new Error(`${property.fullName}: invalid header sub-path: ${remainingPath}`);
    return getHostFactoryOfCustomField(property.targetFactory, remainingPath, customField);
}

function parseCustomFieldCellValue(
    dataType: string,
    value: string | null,
    dateFormat: UserImportExportDateFormat,
): AnyValue {
    if (value == null) {
        return null;
    }
    switch (dataType) {
        case 'boolean':
            return value.toLowerCase() === 'true';
        case 'string':
            return value;
        case 'date': {
            const convertedResult = convertDateFormat(value, dateFormat, 'isoDash');
            if (convertedResult.match(/([12]\d{3}-(0[1-9]|1[0-2])-(0[1-9]|[12]\d|3[01]))/g)) return convertedResult;
            throw new Error(`Unsupported date value ${value}`);
        }
        case 'enum':
            return value;
        case 'integer':
        case 'short':
            if (Number.isInteger(Number(value))) return value;
            throw new Error(`Unsupported short/integer value ${value}`);
        case 'float':
        case 'double':
            if (Number.isFinite(Number(value))) return value;
            throw new Error(`Unsupported float/double value ${value}`);
        case 'decimal':
            if (Decimal.isDecimal(Decimal.make(value))) return value;
            throw new Error(`Unsupported decimal value ${value}`);
        // to enable with multi select
        // case 'enumArray':
        //     if (Array.isArray(value)) return value;
        // throw new Error(`Unsupported array value ${value}`);
        default:
            throw new Error('Unsupported value');
    }
}

function parseDatetimeCellValue(dateFormat: UserImportExportDateFormat, value: string | null): AnyValue {
    if (value == null) {
        return null;
    }
    if (value.indexOf('T') > 0) {
        const datetimeUserFormatStrings = value.split('T');
        const dateIsoString = convertDateFormat(datetimeUserFormatStrings[0], dateFormat, 'isoDash');
        const timeIsoString = datetimeUserFormatStrings[1];
        return `${dateIsoString}T${timeIsoString}`;
    }
    return convertDateFormat(value, dateFormat, 'isoDash');
}

function parseDatetimeRangCellValue(dateFormat: UserImportExportDateFormat, value: string | null): AnyValue {
    if (value == null) {
        return null;
    }
    const datetimeRangeUserFormat = value.toString().replace(/\.\d{3}/g, '') as string;
    const datetimeRangeUserFormatStrings = datetimeRangeUserFormat
        .substring(1, datetimeRangeUserFormat.length - 1)
        .split(',');

    const startDatetimeIsoString = parseDatetimeCellValue(dateFormat, datetimeRangeUserFormatStrings[0]);
    const endDatetimeIsoString = parseDatetimeCellValue(dateFormat, datetimeRangeUserFormatStrings[1]);

    return `${datetimeRangeUserFormat.substring(0, 1)}${startDatetimeIsoString},${endDatetimeIsoString}${datetimeRangeUserFormat.substring(datetimeRangeUserFormat.length - 1)}`;
}

async function isValidProperty(
    context: Context,
    property: Property | undefined,
    doCheckValidProperty: boolean,
    csvHeader: string,
): Promise<void> {
    if (property?.isStringProperty() && property.isStoredEncrypted)
        // Raise error if this is from the csv file
        throw new DataInputError(
            context.localize(
                '@sage/xtrem-import-export/functions__csv_to_json__header_is_an_encrypted_string',
                'This header is an encrypted string: {{csvHeader}}.',
                {
                    csvHeader,
                },
            ),
        );

    if (property && !(await property.isEnabledByServiceOptions(context))) {
        if (doCheckValidProperty) {
            // Raise error if this is from the csv file
            throw new DataInputError(
                context.localize(
                    '@sage/xtrem-import-export/functions__csv_to_json__header_disabled_by_a_service_option',
                    'Header {{csvHeader}} is disabled by a service option',
                    {
                        csvHeader,
                    },
                ),
            );
        }
    }
}

async function getLocaleForProperty(
    context: Context,
    propertyExport: Property | undefined,
    csvHeaderLocale: string,
): Promise<string> {
    if (propertyExport?.isLocalized) {
        const userLocale = (await context.user)?.locale;
        const contextLocale = context.currentLocale;
        const locale = userLocale || (contextLocale ?? 'base');
        return csvHeaderLocale !== '' ? csvHeaderLocale : locale;
    }
    return '';
}

async function parseHeaderWithCustomField(
    context: Context,
    batchContext: BatchContext,
    doCheckValidProperty: boolean,
    factory: NodeFactory,
    csvHeader: CsvTemplateContent,
    path: string[],
    isExport = false,
): Promise<ImportHeader> {
    const csvName = csvHeader.path;
    // Extract the header name parts and make sure the format is valid
    const tokens = csvHeader.path.match(csvHeaderRegex);
    const undefinedPropertyReturnValue = {
        csvName,
        propertyName: '',
        isMandatory: false,
        belongsToNaturalKey: false,
        isArray: false,
        isVitalReference: false,
        isArrayOrVitalReference: false,
        isExportOnly: false,
        isCustomDataDateType: false,
        path,
        locale: '',
        title: csvHeader.description,
        property: undefined,
        keyColumns: [],
        // Xtrem-core expects _id to be a number:
        parseCellValue: csvName === '_id' ? (value: string) => parseInt(value, 10) : (value: string) => value,
    };
    // case of # column to mark the transition
    if (csvHeader.path === '#') return undefinedPropertyReturnValue;
    let locale = '';
    if (csvHeader.path.includes('.')) {
        if (isExport) {
            const propertyExport = await getNonVitalReferencePropertyHeader(
                batchContext,
                doCheckValidProperty,
                factory,
                csvHeader.path.split('.'),
            );

            await isValidProperty(context, propertyExport, doCheckValidProperty, csvHeader.path);

            locale = await getLocaleForProperty(context, propertyExport, csvHeader.locale);

            // case of reference detail for export
            return { ...undefinedPropertyReturnValue, property: propertyExport, locale, isExportOnly: true };
        }
        // case of informational reference detail for import
        return undefinedPropertyReturnValue;
    }

    // If no match, error
    if (tokens == null) throw new DataInputError(`invalid CSV column header syntax: ${csvHeader.path}`);
    // first element of tokens is the entire match, we remove it with slice to extract the 3 parts we are interested in
    const [before, propertyName, keys] = tokens.slice(1);

    // if header starts with # the property is an array.
    const isArray = before[0] === '#';

    // if header starts with / the property is vital reference.
    const isVitalReference = before[0] === '/';

    const isArrayOrVitalReference = isArray || isVitalReference;

    const isExportOnly = false;
    // depth changes to number of hash signs (before.length) if header is an array
    const newDepth = isArrayOrVitalReference ? before.length : path.length;

    // depth cannot increase by more than 1
    if (newDepth > path.length + 1) throw new DataInputError(`invalid depth in column header: ${csvHeader.path}`);

    const newPath = isArrayOrVitalReference ? [...path.slice(0, newDepth - 1), propertyName] : path;

    const dateFormat = await getUserImportExportDateFormat(context);

    if (propertyName === '_customData') {
        const hostFactoryOfCustomField = getHostFactoryOfCustomField(
            factory,
            isArrayOrVitalReference ? path.slice(0, newDepth - 1) : path,
            keys,
        );
        const customFieldsDict = await CoreHooks.customizationManager.getCustomFields(context, [
            hostFactoryOfCustomField.fullName,
        ]);
        if (customFieldsDict) {
            const otherResults = customFieldsDict[hostFactoryOfCustomField.fullName];
            const customField = otherResults.find(element => element.name === keys);
            if (customField) {
                const isMandatory = JSON.parse(customField?.componentAttributes).isMandatory ?? false;
                return {
                    csvName,
                    propertyName,
                    isMandatory,
                    belongsToNaturalKey: false,
                    isArray: false,
                    isVitalReference: false,
                    isArrayOrVitalReference: false,
                    isExportOnly: false,
                    isCustomDataDateType: customField?.dataType === 'date',
                    path: newPath,
                    property: undefined,
                    locale: '',
                    title: csvHeader.description,
                    keyColumns: [keys],
                    parseCellValue: (value: string) =>
                        parseCustomFieldCellValue(customField?.dataType, value, dateFormat),
                };
            }
        }
        if (doCheckValidProperty) {
            // Raise error if this is from the csv file
            throw new DataInputError(
                batchContext.context.localize(
                    '@sage/xtrem-import-export/functions__csv_to_json__invalid_custom_field_column_header',
                    'Invalid custom field in column header: {{csvHeader}}.',
                    {
                        csvHeader: csvHeader.path,
                    },
                ),
            );
        }
        // Ignore property if this is from the template node
        return undefinedPropertyReturnValue;
    }

    const property = await getHeaderPropertyFromFlattenedCsv(
        batchContext,
        doCheckValidProperty,
        factory,
        isArrayOrVitalReference ? path.slice(0, newDepth - 1) : path,
        propertyName,
    );

    await isValidProperty(context, property, doCheckValidProperty, csvHeader.path);

    if (property === undefined) {
        // Ignore property if this is from the template node
        return undefinedPropertyReturnValue;
    }

    locale = await getLocaleForProperty(context, property, csvHeader.locale);

    const commonParts = {
        csvName,
        propertyName,
        isMandatory: before === '*' || before === '!',
        belongsToNaturalKey: before === '!',
        isArray,
        isVitalReference,
        isExportOnly,
        isArrayOrVitalReference,
        path: newPath,
        title: csvHeader.description,
        property,
        locale,
        keyColumns: keys?.split(','),
    };

    if (property.isDateProperty() || property.isDatetimeProperty())
        return {
            ...commonParts,
            parseCellValue: (value: string) => parseDatetimeCellValue(dateFormat, value),
        };

    if (property.isDateRangeProperty() || property.isDatetimeRangeProperty())
        return {
            ...commonParts,
            parseCellValue: (value: string) => parseDatetimeRangCellValue(dateFormat, value),
        };

    return {
        ...commonParts,
        // Xtrem-core expects _id to be a number:
        parseCellValue: csvName === '_id' ? (value: string) => parseInt(value, 10) : (value: string) => value,
    };
}

/**
 * Parses all the headers of the CSV file
 */
export function parseHeadersWithCustomFields(
    context: Context,
    batchContext: BatchContext,
    doCheckValidProperty: boolean,
    factory: NodeFactory,
    csvHeaders: CsvTemplateContent[],
    isExport = false,
): Promise<ImportHeader[]> {
    let path: string[] = [];
    return asyncArray(csvHeaders.filter(csvHeader => csvHeader.path !== '_error'))
        .map(async csvHeader => {
            const header = await parseHeaderWithCustomField(
                context,
                batchContext,
                doCheckValidProperty,
                factory,
                csvHeader,
                path,
                isExport,
            );
            path = header.path;
            return header;
        })
        .toArray();
}

export type DataObject = Dict<AnyValue>;
export interface ParsedResult {
    payload: DataObject;
    rows: AnyRecord[];
}

/**
 * Intermediate row structure produced by buildIntermediateRow.
 */
export interface ImportRow {
    /** Header of first column with a header prefixed by # where we found a value, if any. */
    arrayOrReferenceHeader?: ImportHeader;
    /** JSON bject which contains the values found in the row */
    object: DataObject;
    csvRow: AnyRecord;
}

/**
 * Case of flattened csv format:
 * Converts a CSV row into an IntermediateRow.
 * It collects all the values of the row into a single object (with property names as keys)
 */
export function buildImportRowFromFlattenedCsv(
    row: AnyRecord,
    lineNumberInCsv: number,
    headers: ImportHeader[],
): ImportRow[] {
    const result: ImportRow[] = [];
    // The object that will gather all the values found in the row
    // We use object with a null prototype to protect against potential prototype pollution
    let object = createPlainObject<DataObject>();
    let arrayOrReferenceHeader;
    let partialRow = createPlainObject<AnyRecord>();
    let mandatoryProperties: string[] = [];

    // Scan the entire row
    // eslint-disable-next-line no-restricted-syntax
    for (const header of headers) {
        if (!header.isArrayOrVitalReference) {
            const value = row[header.csvName] as string;
            // Custom field
            if (header.propertyName === '_customData' && object[header.propertyName]) {
                object[header.propertyName] = {
                    ...(object[header.propertyName] as object),
                    ...(parseCellValueFromFlattenedCsv(header, object[header.propertyName], value) as object),
                };
            } else if (header.propertyName !== '') {
                object[header.propertyName] = parseCellValueFromFlattenedCsv(
                    header,
                    object[header.propertyName],
                    value,
                );
            }
            partialRow[header.csvName] = row[header.csvName];
            if (header.isMandatory) mandatoryProperties.push(header.propertyName);
        } else {
            if (lodash.values(object).some(x => x != null)) {
                checkInvalidRow(object, mandatoryProperties, lineNumberInCsv);
                result.push({
                    arrayOrReferenceHeader,
                    object,
                    csvRow: { ...partialRow, $lineNumber: lineNumberInCsv },
                });
            }
            arrayOrReferenceHeader = header;
            object = createPlainObject<DataObject>();
            partialRow = createPlainObject<AnyRecord>();
            mandatoryProperties = [];
        }
    }
    if (lodash.values(object).some(x => x != null))
        result.push({ arrayOrReferenceHeader, object, csvRow: { ...partialRow, $lineNumber: lineNumberInCsv } });
    // delete error field if exists to avoid import of already imported csv file to concatenate
    // eventually errors with previous errors of previous import
    if ('_error' in row) {
        delete row._error;
    }
    // return the IntermediateRow
    return result;
}

/**
 * Case of flattened csv format: Parses the value of cell. Takes care of reference keys.
 */
function parseCellValueFromFlattenedCsv(header: ImportHeader, currentValue: any, value: string): AnyValue {
    // References
    if (header.property?.isReferenceProperty() && !header.property.isVital) {
        if (!value) return null;
        // keyColumns are either `(_id)` or empty.
        // If empty, the reference has a natural key so we just prefix the value with `#`.
        return header.keyColumns?.join() === '_id' ? parseInt(value, 10) : `#${value}`;
    }

    // Localized proeprty
    if (header.property?.isLocalized && header.keyColumns) {
        let parsedValue = null;

        parsedValue =
            value === null
                ? lodash.zipObject(header.keyColumns, ''.split('|'))
                : lodash.zipObject(header.keyColumns, value.split('|'));
        if (currentValue !== undefined) {
            // A repeated column means that it is a localized text and not a reference:
            // If necessary transform the string into an object containing a base key
            // If currentValue is a string the previous header had no keyColumns/locales/languages defined,
            // therefore this was the base column
            let localizeText;
            if (currentValue === null) {
                localizeText = { base: '' };
            } else {
                localizeText = typeof currentValue === 'string' ? { base: currentValue } : currentValue;
            }

            return {
                ...localizeText,
                ...parsedValue,
            };
        }
        return parsedValue;
    }

    // Custom field
    if (header.propertyName === '_customData') {
        return { [header.keyColumns[0]]: header.parseCellValue(value) };
    }

    // Otherwise just return the value
    return header.parseCellValue(value);
}

/**
 * Case of flattened csv format: check if there is any missing mandatory property
 */
function checkInvalidRow(rowObject: Dict<AnyValue>, mandatoryPropertiesList: string[], lineNumberInCsvFile: number) {
    if (mandatoryPropertiesList.some(prop => rowObject[prop] == null)) {
        throw new DataInputError(`invalid CSV row  at line ${lineNumberInCsvFile}: mandatory value missing`);
    }
}
