/* eslint-disable no-restricted-syntax */
import {
    AnyNode,
    AnyValue,
    Collection,
    ConfigManager,
    Context,
    Datetime,
    Logger,
    Node,
    NodeFactory,
    NodeSelectOptions,
    NodeSelector,
    Property,
    asyncArray,
} from '@sage/xtrem-core';
import { InitialNotificationAction, NotificationLevel } from '@sage/xtrem-shared';
import { UserImportExportDateFormat } from '@sage/xtrem-system-api';
import { stringify } from 'csv-stringify/sync';
import { stream as excelStream } from 'exceljs';
import { Writable } from 'stream';
import { ImportExportTemplate } from '../nodes/_index';
import { getUserImportExportDateFormat, getUserImportExportDelimiter } from './common';
import { ImportHeader, parseHeadersWithCustomFields } from './csv-to-json';
import { exportHistogram } from './export-metrics';

const logger = Logger.getLogger(__filename, 'export-utils');

export function stringifyCsv(chunks: string[][], delimiter: string) {
    return stringify(chunks, { quoted: true, delimiter });
}

function escapeFormula(value: string): string {
    // relax the escape_formulas option https://csv.js.org/stringify/options/escape_formulas/
    // in favor of a dedicated method applied to string values.
    // It would have been better to use the cast option of the stringify method but we cannot because all values are converted to string
    // see https://owasp.org/www-community/attacks/CSV_Injection
    if (/^\s*[=+@\t\n-]/.test(value)) {
        return `'${value}`;
    }
    return value;
}

async function _generateExportData(
    context: Context,
    template: ImportExportTemplate,
    outputStream: Writable,
    selectOption: NodeSelectOptions<AnyNode>,
    doTransformHeader: boolean,
    selector?: NodeSelector<AnyNode>,
    targetFormat: 'csv' | 'xlsx' = 'csv',
): Promise<number> {
    try {
        let objectCount = 0;

        if (!selector) {
            objectCount = await buildJsonObjectFromHeader(
                context,
                template,
                outputStream,
                selectOption,
                doTransformHeader,
                targetFormat,
            );
        } else {
            logger.error('NYI export with selector');
        }

        logger.verbose(
            () =>
                `The document is generated locally. Final ${targetFormat.toUpperCase()} principal node count is : ${objectCount}`,
        );
        return objectCount;
    } catch (err) {
        logger.error(`Failed to generate export in ${targetFormat.toUpperCase()}:`);
        logger.error(err);
        throw err;
    }
}

export async function generateExportData(
    context: Context,
    template: ImportExportTemplate,
    exportStream: Writable,
    selectOption: NodeSelectOptions<AnyNode>,
    doTransformHeader: boolean,
    selector?: NodeSelector<AnyNode>,
    targetFormat: 'csv' | 'xlsx' = 'csv',
): Promise<number> {
    const templateName = await template.name;
    return exportHistogram.withMetrics({ templateName, method: `generate_${targetFormat}` }, () =>
        _generateExportData(context, template, exportStream, selectOption, doTransformHeader, selector, targetFormat),
    );
}

function findNextArrayOrVitalReferenceAtTheSameDepth(templateHeaders: ImportHeader[], depth: number): ImportHeader[] {
    const result: ImportHeader[] = [];
    templateHeaders.forEach(templateHeader => {
        const collectionDepth = /^#*/.exec(templateHeader.csvName);
        const vitalRefDepth = /^\/*/.exec(templateHeader.csvName);
        if (
            (collectionDepth != null && collectionDepth[0].length === depth) ||
            (vitalRefDepth != null && vitalRefDepth[0].length === depth)
        )
            result.push(templateHeader);
    });
    return result;
}

/**
    This function transforms a structure with this format:
        [
            [header]
            [line 1]
            [line 2]
        ]
    to
        [
            [header ,['']x times prefix , line 1, ['']x times postfix ]
        ]
*/

function restoreLines(nodeLines: string[][], prefix: number, postfix: number) {
    const result: string[][] = [];
    const parent = nodeLines[0];
    if (nodeLines.length === 1) {
        result.push(parent);
    } else {
        for (let i = 1; i < nodeLines.length; i += 1) {
            result.push([...parent, ...nodeLines[i]]);
        }
    }
    result.forEach(nodeLineElem => nodeLineElem.unshift(...(Array.from({ length: prefix }).fill('') as string[])));
    result.forEach(nodeLineElem => nodeLineElem.push(...(Array.from({ length: postfix }).fill('') as string[])));
    return result;
}

async function buildCsvFileContents(
    context: Context,
    factory: NodeFactory,
    templateHeaders: ImportHeader[],
    exportStream: Writable,
    selectOption: NodeSelectOptions<AnyNode>,
    doTransformHeader: boolean,
): Promise<number> {
    // Get the chunksize
    const chunkSize = ConfigManager.current.exportCsv?.chunkSize || 10000;
    let chunks: string[][] = [];
    // Get the max size for upload file
    const maxUploadSize = ConfigManager.getSizeLimit('maxUploadSize');
    let totalSize = 0;
    let objectCount = 0;

    // Write the header first
    const headerToReturn = templateHeaders.map((header: ImportHeader) => {
        if (doTransformHeader) return header.title;
        if (header.locale !== '') return `${header.csvName}(${header.locale})`;
        return header.csvName;
    });

    const delimiter = await getUserImportExportDelimiter(context);
    const dateFormat = await getUserImportExportDateFormat(context);

    const headerString = stringifyCsv([headerToReturn], delimiter);
    totalSize += Buffer.byteLength(headerString);
    exportStream.write(headerString);

    // Then the content
    await context.withLocalizedTextAsJson(async () => {
        await context.queryWithReader(
            factory.nodeConstructor,
            { filter: selectOption.filter, orderBy: selectOption.orderBy },
            async reader => {
                await reader.forEach(async node => {
                    // For each result from the query, we build the output
                    const resultArray: string[][] = [];
                    objectCount += 1;
                    await combineHeaderAndLines([node], templateHeaders, resultArray, 0, 0, dateFormat);
                    if (chunks.length > chunkSize) {
                        const content = stringifyCsv(chunks, delimiter);
                        totalSize += Buffer.byteLength(content);
                        // Before write to the stream, we check if the total is still under limit
                        if (totalSize > maxUploadSize)
                            throw Error(`Output file size is over the limit ${maxUploadSize} bytes`);
                        exportStream.write(content);
                        chunks = [];
                    }
                    chunks = [...chunks, ...resultArray];
                });
            },
        );
    });
    // Left over
    if (chunks.length > 0) {
        const content = stringifyCsv(chunks, delimiter);
        totalSize += Buffer.byteLength(content);
        // Before write to the stream, we check if the total is still under limit
        if (totalSize > maxUploadSize) throw Error(`Output file size is over the limit ${maxUploadSize} bytes`);
        exportStream.write(content);
    }
    return objectCount;
}

function getExcelNumberFormatForProperty(property?: Property): string | undefined {
    switch (property?.type) {
        case 'integer':
            return '0';
        case 'decimal':
        case 'float':
        case 'double':
            return '0.00';
        default:
            return undefined;
    }
}
function getExcelWidthForProperty(property?: Property): number {
    switch (property?.type) {
        case 'string':
            return 30;
        case 'decimal':
        case 'float':
        case 'double':
            return 10;
        default:
            return 20;
    }
}

async function buildXlsxFileContents(
    context: Context,
    factory: NodeFactory,
    templateHeaders: ImportHeader[],
    exportStream: Writable,
    selectOption: NodeSelectOptions<AnyNode>,
): Promise<number> {
    const chunkSize = ConfigManager.current.exportCsv?.chunkSize || 10000;
    const dateFormat = await getUserImportExportDateFormat(context);

    let objectCount = 0;
    const workbook = new excelStream.xlsx.WorkbookWriter({ stream: exportStream, useStyles: true });
    const worksheet = workbook.addWorksheet(factory.name);
    worksheet.columns = templateHeaders.map((templateHeader, index) => ({
        header: templateHeader.title || templateHeader.propertyName,
        key: String(index + 1),
        numFmt: getExcelNumberFormatForProperty(templateHeader.property),
        width: getExcelWidthForProperty(templateHeader.property),
    }));

    let resultArray: string[][] = [];
    // Then the content
    await context.withLocalizedTextAsJson(async () => {
        await context.queryWithReader(
            factory.nodeConstructor,
            { filter: selectOption.filter, orderBy: selectOption.orderBy },
            async reader => {
                await reader.forEach(async node => {
                    objectCount += 1;
                    resultArray = [];
                    await combineHeaderAndLines([node], templateHeaders, resultArray, 0, 0, dateFormat, 'xlsx');
                    worksheet.addRow(resultArray[0]);

                    if (resultArray.length % chunkSize === 0) {
                        worksheet.commit();
                    }
                });
            },
        );
    });

    worksheet.commit();

    await workbook.commit();
    return objectCount;
}

export async function buildJsonObjectFromHeader(
    context: Context,
    template: ImportExportTemplate,
    exportStream: Writable,
    selectOption: NodeSelectOptions<AnyNode>,
    doTransformHeader: boolean,
    targetFormat: 'csv' | 'xlsx' = 'csv',
): Promise<number> {
    const templateData = (await template.csvTemplate).data;
    if (templateData.length > 0) {
        const factory = context.application.getFactoryByName(await template.nodeName);
        const templateHeaders = await parseHeadersWithCustomFields(
            context,
            context.batch,
            false,
            factory,
            templateData,
            true,
        );

        switch (targetFormat) {
            case 'csv':
                return buildCsvFileContents(
                    context,
                    factory,
                    templateHeaders,
                    exportStream,
                    selectOption,
                    doTransformHeader,
                );
            case 'xlsx':
                return buildXlsxFileContents(context, factory, templateHeaders, exportStream, selectOption);
            default:
                throw new Error(`Unsupported target format: ${targetFormat}`);
        }
    }
    return 0;
}

async function getNodePropertyValue(currentNode: Node, propertyName: string): Promise<AnyValue> {
    let node: Node = currentNode;
    const propertyNameArray = propertyName.split('.');
    while (propertyNameArray.length > 1) {
        node = (await node?.$?.getValue(propertyNameArray[0])) as AnyNode;
        if (node == null) return node;
        propertyNameArray.shift();
    }
    return node.$.getValue(propertyNameArray[0]);
}

function convertDatetimeRangePropertyValueToCsv(
    value: any,
    dateFormat: UserImportExportDateFormat,
    dateOnly: boolean,
): string {
    if (!value) return '';
    // For example, we can have [2022-01-01T00:00:00.000Z,2022-12-31T23:59:59.000Z]
    // we need to remove microsecond parts
    const datetimeRangeIso = value.toString().replace(/\.\d{3}/g, '') as string;
    // Then split into start and end part then convert them into user format
    const datetimeRangeIsoStrings = datetimeRangeIso.substring(1, datetimeRangeIso.length - 1).split(',');
    // By default the date format is isoDash yyyy-mm-dd
    // So we need to convert it into the user format
    const startDatetimeUserFormatStrings = convertDatetimePropertyValueToCsv(
        datetimeRangeIsoStrings[0],
        dateFormat,
        dateOnly,
    );
    const endDatetimeUserFormatStrings = convertDatetimePropertyValueToCsv(
        datetimeRangeIsoStrings[1],
        dateFormat,
        dateOnly,
    );

    return `${datetimeRangeIso.substring(0, 1)}${startDatetimeUserFormatStrings},${endDatetimeUserFormatStrings}${datetimeRangeIso.substring(datetimeRangeIso.length - 1)}`;
}

export function convertDateFormat(
    dateString: string,
    dateFormatFrom: UserImportExportDateFormat,
    dateFormatTo: UserImportExportDateFormat,
): string {
    if (dateFormatFrom === dateFormatTo) return dateString;
    let parts: string[];
    let day: string;
    let month: string;
    let year: string;
    switch (dateFormatFrom) {
        case 'europeanDash':
            parts = dateString.split('-');
            [day, month, year] = parts;
            break;
        case 'europeanSlash':
            parts = dateString.split('/');
            [day, month, year] = parts;
            break;
        case 'usDash':
            parts = dateString.split('-');
            [month, day, year] = parts;
            break;
        case 'usSlash':
            parts = dateString.split('/');
            [month, day, year] = parts;
            break;
        case 'isoSlash':
            parts = dateString.split('/');
            [year, month, day] = parts;
            break;
        case 'isoDash':
            parts = dateString.split('-');
            [year, month, day] = parts;
            break;
        default:
            throw new Error(
                `Invalid original date format for ${dateFormatFrom}. Check the user preference date format for import export.`,
            );
    }
    if (parts.length !== 3) {
        throw new Error(
            `Invalid original date format for ${dateFormatFrom}. Check the user preference date format for import export.`,
        );
    }
    switch (dateFormatTo) {
        case 'europeanDash':
            return `${day}-${month}-${year}`;
        case 'europeanSlash':
            return `${day}/${month}/${year}`;
        case 'usDash':
            return `${month}-${day}-${year}`;
        case 'usSlash':
            return `${month}/${day}/${year}`;
        case 'isoSlash':
            return `${year}/${month}/${day}`;
        case 'isoDash':
            return `${year}-${month}-${day}`;
        default:
            throw new Error(
                `Invalid destination date format for ${dateFormatTo}. Check the user preference date format for import export.`,
            );
    }
}

function convertDatetimePropertyValueToCsv(
    value: any,
    dateFormat: UserImportExportDateFormat,
    dateOnly: boolean,
): string {
    if (!value) return '';
    const datetimeISOString = Datetime.fromValue(value).toString();
    if (datetimeISOString.indexOf('T') > 0) {
        const datetimeISOStrings = Datetime.fromValue(value).toString().split('T');
        const dateString = convertDateFormat(datetimeISOStrings[0], 'isoDash', dateFormat);
        const timeString =
            datetimeISOStrings[1].indexOf('.') > 0 ? `${datetimeISOStrings[1].split('.')[0]}Z` : datetimeISOStrings[1];
        return dateOnly ? dateString : `${dateString}T${timeString}`;
    }
    return convertDateFormat(datetimeISOString, 'isoDash', dateFormat);
}

function convertNumberPropertyValueToCsv(property: Property, value: any): string {
    if (!value && !property.isNullable) return '0';
    if (!value) return '';
    // eslint-disable-next-line @sage/redos/no-vulnerable
    return typeof value === 'string' && value?.indexOf('.') > 0 ? value.replace(/\.?0+$/, '') : value.toString();
}

function convertLocalizedStringPropertyValueToCsv(templateHeader: ImportHeader, value: any, locales: string[]): string {
    if (templateHeader.locale && (value as string).startsWith('{') && (value as string).endsWith('}')) {
        const strings = JSON.parse(value as string);
        if (strings[templateHeader.locale] === undefined) {
            const locale = locales.find(l => strings[l]?.length);
            if (!locale) return '';
            return strings[locale];
        }
        return strings[templateHeader.locale];
    }
    return value;
}

function convertCustomDataPropertyValueToCsv(templateHeader: ImportHeader, value: any): string {
    if (value && templateHeader.keyColumns?.length === 1) {
        return value[templateHeader.keyColumns[0]] != null ? value[templateHeader.keyColumns[0]].toString() : '';
    }
    return value;
}

function convertCustomDataDateValueToCsv(
    templateHeader: ImportHeader,
    value: any,
    dateFormat: UserImportExportDateFormat,
): string {
    if (value && templateHeader.keyColumns?.length === 1) {
        const input = value[templateHeader.keyColumns[0]];
        return input != null ? convertDatetimePropertyValueToCsv(input.toString(), dateFormat, true) : '';
    }
    return value;
}
/**
 * Convert the value which was read from SQL to a CSV value.
 * If the property is a stream property the value will be stored into a separate file and replaced by
 * a file:filename string in the CSV (unless it is a very short string without newlines).
 */
function serializeValueToTargetFormat(
    templateHeader: ImportHeader,
    value: any,
    locales: string[],
    dateFormat: UserImportExportDateFormat,
    targetFormat: 'csv' | 'xlsx' = 'csv',
): any {
    const property = templateHeader.property;
    if (property) {
        if (property.isBooleanProperty()) {
            return value ? 'TRUE' : 'FALSE';
        }
        if (property.isIntegerProperty() || property.isDecimalProperty() || property.isFloatingPointProperty()) {
            const result = convertNumberPropertyValueToCsv(property, value);
            if (targetFormat === 'xlsx') {
                return Number(result);
            }
            return result;
        }
        if (property.isDatetimeProperty()) {
            return convertDatetimePropertyValueToCsv(value, dateFormat, false);
        }
        if (property.isTextStreamProperty() || property.isBinaryStreamProperty()) {
            return value ? escapeFormula(value.toString()) : '';
        }

        if (property.isEnumProperty() && !property.isNullable && !value) {
            return property.dataType.values[0];
        }
        if (property.isLocalized) {
            return escapeFormula(convertLocalizedStringPropertyValueToCsv(templateHeader, value, locales));
        }
        if (property.isJsonProperty() && !property.isNullable && !value) {
            return '{}';
        }
        if (property.isArrayProperty() && !property.isNullable && !value) {
            return '[]';
        }

        if (property.isDateProperty()) {
            const result = convertDatetimePropertyValueToCsv(value, dateFormat, true);
            if (targetFormat === 'xlsx') {
                return new Date(result);
            }
            return result;
        }

        if (property.isDatetimeRangeProperty()) {
            return convertDatetimeRangePropertyValueToCsv(value, dateFormat, false);
        }
        if (property.isDateRangeProperty()) {
            return convertDatetimeRangePropertyValueToCsv(value, dateFormat, true);
        }
    } else if (templateHeader.propertyName === '_customData') {
        if (templateHeader.isCustomDataDateType) {
            return convertCustomDataDateValueToCsv(templateHeader, value, dateFormat);
        }
        return convertCustomDataPropertyValueToCsv(templateHeader, value);
    } else if (value == null) {
        return '';
    }
    return escapeFormula(value);
}

/**
 * This function manage collection or vital references of a node, each time the current node contains vital ref
 * or collection we proceed recursively the node
 * @param templateHeaders
 * @param independentCollectionsOrVitalReference
 * @param currentNode
 * @param nodeLinesIn
 * @param nodes
 * @param postfix
 * @returns
 */

async function manageVitalCollectionOrVitalReference(
    templateHeaders: ImportHeader[],
    independentCollectionsOrVitalReference: ImportHeader[],
    currentNode: Node,
    nodeLinesIn: string[][],
    nodes: Node[],
    dateFormat: UserImportExportDateFormat,
    targetFormat: 'csv' | 'xlsx' = 'csv',
): Promise<string[][]> {
    let newPostFix;
    let nodeLines = nodeLinesIn;
    let mergedLine = false;
    for (let i = 0; i < independentCollectionsOrVitalReference.length; i += 1) {
        const collectionOrVitalRefPosition = templateHeaders.findIndex(
            templateHeader => templateHeader.csvName === independentCollectionsOrVitalReference[i].csvName,
        );

        // Determine prefix in case of independent collections or vital refs
        let firstCollectionPosition = collectionOrVitalRefPosition;
        if (i > 0)
            firstCollectionPosition = templateHeaders.findIndex(
                templateHeader => templateHeader.csvName === independentCollectionsOrVitalReference[0].csvName,
            );

        const newPrefix = collectionOrVitalRefPosition - firstCollectionPosition;

        // Prepare for postfix  in case of independent collections or vital refs
        let nextCollectionOrVitalReferencePosition = templateHeaders.length;
        if (i < independentCollectionsOrVitalReference.length - 1)
            nextCollectionOrVitalReferencePosition = templateHeaders.findIndex(
                templateHeader => templateHeader.csvName === independentCollectionsOrVitalReference[i + 1].csvName,
            );

        const restOfTemplateHeader = templateHeaders.slice(
            collectionOrVitalRefPosition + 1,
            nextCollectionOrVitalReferencePosition,
        );
        newPostFix = templateHeaders.length - nextCollectionOrVitalReferencePosition;
        if (independentCollectionsOrVitalReference[i].isArray) {
            // Collections
            const currentCollection = await (
                (await currentNode.$.get(independentCollectionsOrVitalReference[i].propertyName)) as Collection<AnyNode>
            ).toArray();

            if (currentCollection.length !== 0) {
                // from the the first sub level, we prepare the header for the next level
                if (nodes.length > 1 && !mergedLine) {
                    nodeLines = restoreLines(nodeLines, 0, 0);
                    mergedLine = true;
                }
                for (const nodeInCollection of currentCollection) {
                    await combineHeaderAndLines(
                        [...nodes, nodeInCollection],
                        restOfTemplateHeader,
                        nodeLines,
                        newPrefix,
                        newPostFix,
                        dateFormat,
                        targetFormat,
                    );
                }
            } else if (nodes.length > 1 && !mergedLine && newPostFix === 0) {
                // fill the blank for empty collection place if last group
                nodeLines[nodeLines.length - 1] = [
                    ...nodeLines[nodeLines.length - 1],
                    ...(Array.from({ length: newPrefix + restOfTemplateHeader.length + 1 }).fill('') as string[]),
                ];
            }
        } else {
            // Vital Ref
            const currentVitalRef = (await getNodePropertyValue(
                currentNode,
                independentCollectionsOrVitalReference[i].propertyName,
            )) as AnyNode;
            if (currentVitalRef != null) {
                if (nodes.length > 1 && !mergedLine) {
                    nodeLines = restoreLines(nodeLines, 0, 0);
                    mergedLine = true;
                }

                await combineHeaderAndLines(
                    [...nodes, currentVitalRef],
                    restOfTemplateHeader,
                    nodeLines,
                    newPrefix,
                    newPostFix,
                    dateFormat,
                );
            } else if (nodes.length > 1 && !mergedLine && newPostFix === 0) {
                // fill the blank for empty vital ref place if last group
                nodeLines[nodeLines.length - 1] = [
                    ...nodeLines[nodeLines.length - 1],
                    ...(Array.from({ length: newPrefix + restOfTemplateHeader.length + 1 }).fill('') as string[]),
                ];
            }
        }
    }
    return nodeLines;
}

/**
    This function read the whole node then return a structure that we build with this format:
        [
            [header]
            [line 1]
            [line 2]
        ]
    Then the restoreLines will convert it to
        [
            [header , line 1]
            [header , line 2]
        ]
    Please note that each time we have a collection or vital reference, we have to recursive through that node with
    the same strategic

     * @param nodes
     * @param templateHeaders
     * @param result
     * @param prefix
     * @param postfix
     */
async function combineHeaderAndLines(
    nodes: Node[],
    templateHeaders: ImportHeader[],
    result: string[][],
    prefix: number,
    postfix: number,
    dateFormat: UserImportExportDateFormat,
    targetFormat: 'csv' | 'xlsx' = 'csv',
) {
    const depth = nodes.length;
    const currentNode = nodes[nodes.length - 1];
    const independentCollectionsOrVitalReference = findNextArrayOrVitalReferenceAtTheSameDepth(templateHeaders, depth);
    const nodeLine: string[] = [];
    const nodeLines: string[][] = [];
    let lastNodePropertyIndex = templateHeaders.length;

    if (independentCollectionsOrVitalReference.length > 0) {
        lastNodePropertyIndex = templateHeaders.findIndex(
            templateHeader => templateHeader.csvName === independentCollectionsOrVitalReference[0].csvName,
        );
    }
    for (let i = 0; i < lastNodePropertyIndex; i += 1) {
        const templateHeader = templateHeaders[i];
        const isExportOnly = !!templateHeader.isExportOnly;

        templateHeader.csvName = templateHeader.csvName.replace(/[*#!]/g, '');
        if (!templateHeader.isArrayOrVitalReference) {
            let propertyName =
                templateHeader.propertyName !== '' ? templateHeader.propertyName : templateHeader.csvName;
            // for soldToCustomer.billToAddress, soldToCustomer.billToAddress.isPrimary we need to first dig until the last property and fetch value according to it
            // from a reference property, we may have a path to it's property or another reference vital or not
            // Use cases and their expected behavior:
            // - property (other then reference): we need to fetch the value from the current node, by removing the path from the property name
            // - reference or vital reference: we need to handle it the same way as a reference from the main node
            propertyName = preparePropertyName(propertyName, isExportOnly, currentNode, nodes);
            const col = await getNodePropertyValue(currentNode, propertyName);
            if (col == null || (!templateHeader.property && templateHeader.propertyName !== '_customData')) {
                nodeLine.push('');
            } else if (templateHeader.property?.isReferenceProperty()) {
                if (templateHeader.property.targetFactory.naturalKey) {
                    const naturalKeysValues = await (col as AnyNode).$.getNaturalKeyValue();
                    nodeLine.push(naturalKeysValues);
                } else {
                    nodeLine.push((col as AnyNode)._id.toString());
                }
            } else {
                const value = serializeValueToTargetFormat(
                    templateHeader,
                    col,
                    [...currentNode.$.context.locales, ...['base']],
                    dateFormat,
                    targetFormat,
                );
                nodeLine.push(value);
            }

            // no more header
            if (i === lastNodePropertyIndex - 1) {
                if (nodes.length > 1 && nodeLines.length === 0) nodeLines.push(['']);
                nodeLines.push(nodeLine);
            }
        } else {
            throw Error('Impossible array or vital reference');
        }
    }
    const completedNodeLines = await manageVitalCollectionOrVitalReference(
        templateHeaders,
        independentCollectionsOrVitalReference,
        currentNode,
        nodeLines,
        nodes,
        dateFormat,
        targetFormat,
    );

    if (completedNodeLines.length > 1) {
        result.push(...restoreLines(completedNodeLines, prefix, postfix));
    } else {
        nodeLines.push(Array.from({ length: templateHeaders.length - lastNodePropertyIndex }).fill('') as string[]);
        result.push(...restoreLines(nodeLines, prefix, postfix));
    }
}

/**
 * This function prepares the property name in order to get it's value from the currentNode instance
 * hence, removing the part of it's path that is not relevant to the currentNode
 * Example:
 * currentNode=baseNode
 *    (in this case, the property path allow to get the value from the baseNode instance directly)
 *     hence the tests on currentNode.$.factory !== nodes[0].$.factory &&
 *      propertyPath='currentNode.node1.property'
 *      propertyPath='currentNode.node1.node2'
 *      propertyPath='currentNode.node1.node2.property'
 * currentNode=vital reference/collection from the baseNode
 *    (in this case, the property path starts from the baseNode instance (lines)
 *    and the currentNode instance is the line instance), so we need to remove the lines part from the property path,
 *    hence the slicing of the propertyName)
 *      propertyPath='lines.node1.property' => becomes propertyPath='node1.property'
 *      propertyPath='lines.node1.node2' => becomes propertyPath='node1.node2'
 *      propertyPath='lines.node1.node2.property' => becomes propertyPath='node1.node2.property'
 * currentNode=vital reference/collection from another vital reference/collection from the baseNode,
 *    (in this case, the property path starts from the baseNode instance (lines) and continues with a vital reference/collection of that line
 *    and the currentNode instance is the sub-line instance), so we need to remove the lines/sub-lines part from the property path,
 *    hence the slicing of the propertyName)
 *      propertyPath='lines.taxes.node1.property'
 *      propertyPath='lines.taxes..node1.node2'
 *      propertyPath='lines.taxes.node1.node2.property'
 * @param propertyName
 * @param isExportOnly
 * @param currentNode
 * @param nodes
 * @returns
 */
function preparePropertyName(propertyName: string, isExportOnly: boolean, currentNode: Node, nodes: Node[]): string {
    let preparedPropertyName = propertyName;
    if (
        propertyName.includes('.') &&
        isExportOnly &&
        currentNode.$.factory !== nodes[0].$.factory &&
        nodes.length > 1
    ) {
        if (nodes.length > 1) {
            preparedPropertyName = propertyName
                .split('.')
                .slice(nodes.length - 1)
                .reduce((acc, val) => acc.concat('.', val));
        }
    }
    return preparedPropertyName;
}
export async function sendClientExportNotification(
    context: Context,
    data: {
        status: NotificationLevel;
        templateId: string;
        numberOfRecords?: number;
        rejectReason?: string;
        downloadUrl?: string;
    },
) {
    const { downloadUrl, templateId, status, numberOfRecords, rejectReason } = data;
    const historyLink = context.batch.notificationStateLink;
    const isNotSuccessful = status === 'error';

    const actions: InitialNotificationAction[] = isNotSuccessful
        ? [
              {
                  link: historyLink,
                  title: 'History',
                  icon: 'link',
                  style: 'tertiary',
              },
          ]
        : [
              {
                  link: `${downloadUrl}`,
                  title: 'Download file',
                  icon: 'link',
                  style: 'secondary',
              },
              {
                  link: historyLink,
                  title: 'History',
                  icon: 'link',
                  style: 'tertiary',
              },
          ];

    const title = isNotSuccessful
        ? context.localize(
              '@sage/xtrem-import-export/function__export_send_client_notification_title_fail',
              'The export with this template ended in error: {{templateId}}.',
              { templateId },
          )
        : context.localize(
              '@sage/xtrem-import-export/function__export_send_client_notification_title_success',
              'The export with this template ended successfully: {{templateId}}.',
              { templateId },
          );

    const description = isNotSuccessful
        ? (rejectReason as string)
        : context.localize(
              '@sage/xtrem-import-export/function__export_send_client_notification_description',
              'Records exported: {{numberOfRecords}}.',
              { numberOfRecords },
          );

    await context.notifyUser({
        title,
        icon: 'download',
        description,
        level: status,
        shouldDisplayToast: true,
        actions,
    });
}

export interface SelfReferencingProperty {
    property: Property;
    naturalKey: string[];
}

const isSelfReference = (property: Property, collectionProperty: Property) => {
    return (
        collectionProperty.isCollectionProperty() &&
        !property.isSystemProperty &&
        property.isReferenceProperty() &&
        !property.isVitalParent &&
        property.targetFactory.name === collectionProperty.targetFactory.name &&
        property.factory.naturalKey
    );
};

/**
 * This function finds self referencing properties of collections for a given factory
 * @param factory
 * @returns a list of self referencing properties of @type {SelfReferencingProperty} or null if no self referencing properties are found
 */
export function findSelfReferencingProperties(factory: NodeFactory): SelfReferencingProperty[] {
    const selfReferenceProperties: SelfReferencingProperty[] = [];
    factory.properties
        .filter(prop => prop.isCollectionProperty())
        .forEach(prop => {
            // Even it's filtered, we check again to avoid linter error on prop.targetFactory reference
            if (prop.isCollectionProperty())
                prop.targetFactory.properties.forEach(collProp => {
                    if (collProp.isCollectionProperty() && collProp.targetFactory.name !== prop.targetFactory.name) {
                        const _selfReferenceProperties = findSelfReferencingProperties(collProp.targetFactory);
                        if (_selfReferenceProperties)
                            _selfReferenceProperties.forEach(elt => selfReferenceProperties.push(elt));
                    }
                    if (isSelfReference(collProp, prop))
                        selfReferenceProperties.push({
                            property: collProp,
                            naturalKey: collProp.factory.naturalKey ?? [],
                        });
                });
        });
    return selfReferenceProperties;
}

/**
 * This function finds self referencing properties in a collection, adds _id for already existing lines (in update mode)
 * and generates a transient _id for new lines.
 * @param context
 * @param property
 * @param values
 * @param forUpdate
 * @param node
 * @returns Promise<{ name: string; naturalKey: string[] }[] | null> which will contain a tuple of name of the self referencing
 * property and an array of the natural key
 */
export async function prepareSelfReferencingData(
    context: Context,
    values: any[],
    options: {
        property: Property;
        selfReferenceProperties: SelfReferencingProperty[];
        forUpdate: boolean;
        node?: Node;
    },
): Promise<void> {
    const { forUpdate, node, property, selfReferenceProperties } = options;

    if (!values) return;
    if (!selfReferenceProperties.length) return;
    if (!property.isCollectionProperty()) return;
    if (!property.targetFactory.vitalParentProperty) return;
    if (!(property.factory.naturalKey && property.factory.naturalKey.length > 0)) return;

    // In update mode : first loop to allocate an _id to for existing collection elements (from the database)
    if (forUpdate && node) {
        const parentProperty = property.targetFactory.vitalParentProperty;
        await context.application.asRoot.withReadonlyContext(
            node.$.context.tenantId,
            async readOnlyContext => {
                // This loop will find the existing lines in the database and add their _id to the imported value element
                // It loops once for all the lines of a collection...
                await readOnlyContext.queryWithReader(
                    property.targetFactory.nodeConstructor,
                    { filter: { [parentProperty.name]: node._id } },
                    async reader => {
                        await reader.forEach(async line => {
                            // Add existing line's _id to the imported value element
                            const lineValueIndex = await asyncArray(values).findIndex(
                                async elt => +elt._sortValue === (await line._sortValue) && !elt._id,
                            );
                            if (lineValueIndex > -1) values[lineValueIndex]._id = line._id;
                        });
                    },
                );
            },
            { source: 'customMutation' },
        );
    }
    // For inserted lines (in update or insert import mode) : first loop to allocate an _id to all collection elements without _id
    values
        .filter(elt => !elt._id)
        .forEach(element => {
            element._id = context.allocateTransientId();
        });
}

/**
 * For each self referencing property, this function will fix the self referencing data by replacing the natural key with the _id of the referenced line
 * @param selfReferenceProperties
 * @param element
 * @param values
 * @returns
 */
export function fixSelfReferencingData(
    selfReferenceProperties: SelfReferencingProperty[],
    element: any,
    values: any[],
) {
    if (!selfReferenceProperties.length) return;
    // We loop through each self referencing property and replace the natural key with the _id of the referenced line
    // There should not be more the one self referencing property in a collection...
    selfReferenceProperties
        ?.filter(selfRefProp => element[selfRefProp.property.name] && selfRefProp.naturalKey)
        .forEach(selfRefProp => {
            const propData = element[selfRefProp.property.name];
            if (selfRefProp.naturalKey && propData && typeof propData === 'string' && propData.startsWith('#')) {
                const naturalKeyValues = propData.slice(1).split('|');
                const referencedValueLine = values.find(v =>
                    naturalKeyValues.every((nk, i) =>
                        i === 0 ? true : selfRefProp.naturalKey && v[selfRefProp.naturalKey[i]] === nk,
                    ),
                );
                if (referencedValueLine) element[selfRefProp.property.name] = referencedValueLine._id;
            }
        });
}
