import { BatchContext } from '@sage/xtrem-communication';
import { AnyR<PERSON>ord, ConfigManager, Context, CsvTemplateContent } from '@sage/xtrem-core';
import { DataInputError } from '@sage/xtrem-shared';
import { parse } from 'csv-parse';
import * as _ from 'lodash';
import { Readable, finished } from 'stream';
import { promisify } from 'util';
import { ImportExportTemplate } from '../nodes/import-export-template';
import { ImportResult } from '../nodes/import-result';
import { logger } from '../nodes/utils';
import { ImportCsvResult, LoadInfo, ParsedResultBucket, getUserImportExportDelimiter } from './common';
import {
    DataObject,
    ImportHeader,
    ImportRow,
    buildImportRowFromFlattenedCsv,
    parseHeadersWithCustomFields,
} from './csv-to-json';
import { findSelfReferencingProperties } from './export-utils';
/**
 * CsvContents output of parsed csv content or file (with headers and included rows )
 */
export interface CsvContents {
    headers: string[];
    rows: AnyRecord[];
}

export interface RecordRowObject {
    previousImportRows: ImportRow[];
    importRows: ImportRow[];
    object: DataObject | null;
    ancestors: any[];
    csvRows: AnyRecord[];
    loop: boolean;
    rowsSanitizedInBuckets?: AnyRecord[];
}
/**
 * parseEntireCsvStream function which takes a readable stream (as input) coming from a csv file
 * or stringified csv content and transform into CsvContents.
 * Please note, we use this function to read entire file at once, so it is not used to proceed the file but only in the
 * case that we need for mocha tests or when we need rapidly to read quickly the headers from a text for example
 */
export async function parseEntireCsvStream(stream: Readable, delimiter: string): Promise<CsvContents> {
    const contents: CsvContents = { headers: [], rows: [] };
    const parser = stream.pipe(
        parse({
            delimiter,
            columns: (headers: string[]) => {
                if (!headers[headers.length - 1].length) headers[headers.length - 1] = '_ignore';
                contents.headers = headers;
                return headers;
            },
        }),
    );

    parser.on('readable', () => {
        let rowSanitize;
        // eslint-disable-next-line no-cond-assign
        while ((rowSanitize = parser.read()) !== null) {
            rowSanitize = _.mapValues(rowSanitize, (val: string) => (val !== '' ? unescapeFormulaForInput(val) : null));
            contents.rows.push(rowSanitize);
        }
    });
    // Catch any error
    parser.on('error', (err: any) => {
        logger.error(err.message);
    });

    await promisify(finished)(parser);

    return contents;
}

/**
 * remove escape character for formula
 */
export function unescapeFormulaForInput(text: string): string {
    return text.replace(/^'([-=+@\t\r])/, '$1');
}

/**
 * parseCsvText function which takes path of csv file as input and transforms it into  a readable stream
 * before calling parseEntireCsvStream function documented above
 */
export function parseCsvText(text: string, delimiter: string): Promise<CsvContents> {
    return parseEntireCsvStream((Readable as any).from(text), delimiter);
}

/**
 * isNewRow function to determine if  an import row is a new row to consider or not (document level will be repeated
 * in the csv for every of its line)
 */
function isNewRow(importRows: ImportRow[], row: ImportRow) {
    return (
        importRows.findIndex(element =>
            _.isEqual(_.omit(row.csvRow, ['$lineNumber']), _.omit(element.csvRow, ['$lineNumber'])),
        ) < 0
    );
}

/**
 * manageArrayOrReferenceHeader function to build the payload object from an import row
 */
function manageArrayOrReferenceHeader(
    arrayOrReferenceHeader: ImportHeader,
    importRow: ImportRow,
    recordRowObject: RecordRowObject,
    objectIn: DataObject | null,
    rowSanitized: any,
) {
    let object = objectIn;
    // Pop the ancestors until we get the parent which holds the array or the reference
    while (recordRowObject.ancestors.length >= arrayOrReferenceHeader.path.length) {
        object = recordRowObject.ancestors.pop();
    }

    // We are processing a subobject. It cannot be the first row so object should not be null.
    if (!object) throw new DataInputError(`invalid CSV row at line ${rowSanitized.$lineNumber}: parent object is null`);

    // Depth cannot increase by more than 1 at a time. This was already checked by parseHeader but we check again.
    if (arrayOrReferenceHeader.path.length !== recordRowObject.ancestors.length + 1)
        throw new DataInputError('array header depth increases by more than 1');

    if (arrayOrReferenceHeader.isArray) {
        // Create the array if this is the first element that we are adding to it
        if (!object[arrayOrReferenceHeader.propertyName]) object[arrayOrReferenceHeader.propertyName] = [];

        const array = object[arrayOrReferenceHeader.propertyName];
        if (!Array.isArray(array))
            throw new DataInputError(
                `line ${rowSanitized.$lineNumber}: value of ${arrayOrReferenceHeader.propertyName} is not an array`,
            );

        array.push(importRow.object);
    } else {
        // Create the reference
        object[arrayOrReferenceHeader.propertyName] = importRow.object;
    }
    // Push the parent into the ancestors.
    recordRowObject.ancestors.push(object);
    return object;
}

/**
 * recordRow function to insert/update the database with the payload object
 * Transforms the array of CSV rows into an array of results.
 * Each result is a DataObject which may contain other DataObjects.
 * Each document may contain lines, sublines, comments, etc.
 */

async function recordRow(
    context: Context,
    batchContext: BatchContext,
    recordRowObject: RecordRowObject,
    jsonBucket: ParsedResultBucket,
    result: ImportCsvResult,
    loadInfo: LoadInfo,
    rowSanitized: any,
) {
    let object = recordRowObject.object;
    let isNewRowStarted = false;
    let rowsSanitizedInBuckets = recordRowObject.rowsSanitizedInBuckets ?? [];

    // eslint-disable-next-line no-restricted-syntax
    for (const importRow of recordRowObject.importRows) {
        if (isNewRowStarted || isNewRow(recordRowObject.previousImportRows, importRow)) {
            isNewRowStarted = true;

            const arrayOrReferenceHeader = importRow.arrayOrReferenceHeader;
            if (arrayOrReferenceHeader) {
                // case of array or reference header
                manageArrayOrReferenceHeader(arrayOrReferenceHeader, importRow, recordRowObject, object, rowSanitized);
            } else {
                // We are processing a top-level object.
                // All the ancestors of the previous object can be discarded.
                recordRowObject.ancestors = [];

                // Reallocate a new array for the rows
                recordRowObject.csvRows = [];

                if (jsonBucket.size > 0 && jsonBucket.size % loadInfo.chunkSize === 0) {
                    // write record in a single transaction by chunks to improve performance
                    recordRowObject.loop = await ImportExportTemplate.tryLoadRecordBucket(
                        context,
                        batchContext,
                        result,
                        jsonBucket,
                        loadInfo,
                        rowsSanitizedInBuckets,
                    );
                    rowsSanitizedInBuckets = [];
                }
                // Just push row.object into the results
                jsonBucket.push({
                    payload: importRow.object,
                    rows: recordRowObject.csvRows,
                });
            }
            // Update object, the object produced by the previous row.
            object = importRow.object;
            // push the current row into current CsvRows array
            recordRowObject.csvRows.push(importRow.csvRow);
        }
    }
    rowsSanitizedInBuckets.push(rowSanitized);
    recordRowObject.rowsSanitizedInBuckets = rowsSanitizedInBuckets;
    recordRowObject.object = object;
    return recordRowObject;
}

/**
 * parseCsvStreamTransform function which takes a readable stream (as input) coming from a csv file
 * or stringified csv content by line by line and transform into json CsvContents then insert or update the database.
 * WARNING: it presently only works with separator ; (hardcoded)
 */
export async function parseCsvStreamTransform(
    stream: Readable,
    context: Context,
    batchContext: BatchContext,
    importResult: ImportResult,
): Promise<ImportCsvResult> {
    const contents: CsvContents = { headers: [], rows: [] };
    let templateContents: CsvTemplateContent[] = [];
    const result = {
        headers: [],
        status: 'finished',
        rowsProcessed: 0,
        failedRows: [],
        errorCount: 0,
        generalError: '',
    } as ImportCsvResult;
    let dataHeaders: ImportHeader[] = [];

    let lineNumber = 0;
    const chunkSize = ConfigManager.current.importCsv?.chunkSize ?? 10;
    const dryRun = await importResult.dryRun;
    const doInsert = await importResult.doInsert;
    const doUpdate = await importResult.doUpdate;
    const maxErrorCount = await importResult.maxErrorCount;
    const importExportTemplate = await importResult.importExportTemplate;

    const filename = await importResult.filename;
    const uploadedFile = await importResult.uploadedFile;
    const uploadedFileKey = await uploadedFile.key;

    try {
        result.dryRun = dryRun;
        if (!importExportTemplate) throw new Error('Import Export template cannot be null');
        const factory = context.application.getFactoryByName(await importExportTemplate.nodeName);

        await batchContext.logMessage(
            'info',
            `[Import ${importResult._id}] Started for '${filename}' uploaded file ${uploadedFile._id}/${uploadedFileKey}`,
        );

        ImportExportTemplate.checkFactory(factory, { doInsert, doUpdate });

        const csvTemplate = (await importExportTemplate.csvTemplate).data;
        // TODO: refactoring to remove the parameters if not used anymore
        const parameters = await importResult.parameters;
        if (!parameters) throw new Error('Import parameters cannot be null');

        const numberOfPaths = csvTemplate.length;

        const delimiter = await getUserImportExportDelimiter(context);

        const paths = Array.from({ length: numberOfPaths });
        for (let i = 0; i < numberOfPaths; i += 1) {
            paths[i] = csvTemplate[i].path;
        }
        const templateCsv = await parseCsvText(`${paths.join(delimiter)}`, delimiter);

        const parser = stream.pipe(
            parse({
                delimiter,
                bom: true,
                columns: (headers: string[]) => {
                    if (!headers[headers.length - 1].length) headers[headers.length - 1] = '_ignore';
                    contents.headers = headers;
                    templateContents = headers.map(header => {
                        return {
                            path: header,
                            _id: 0,
                            dataType: '',
                            description: '',
                            locale: '',
                        };
                    });
                    return headers;
                },
            }),
        );

        // doCheckValidProperty is set to false because we are in the import process
        // and here the check is on the template itself.
        // Hence we do not throw error if one of it column is deleted or deactivated or access is not granted.
        const templateHeaders = await parseHeadersWithCustomFields(context, batchContext, false, factory, csvTemplate);
        let loop = true;
        const jsonBucket: ParsedResultBucket = new ParsedResultBucket(importResult._id);
        // Find self referencing properties in the factory's collections (we need to know if a property is a reference to the same collection)
        const selfReferenceProperties = findSelfReferencingProperties(factory);
        const loadInfo = {
            maxErrorCount,
            factory,
            doInsert,
            doUpdate,
            headers: templateCsv.headers,
            dryRun,
            chunkSize,
            selfReferenceProperties,
        };
        // The object produced by the previous row
        let object: DataObject | null = null;
        // The ancestors of object.
        let ancestors: any[] = [];
        // The array including rows involved by the produced result
        let csvRows: AnyRecord[] = [];

        let previousImportRows: ImportRow[] = [];

        let rowsSanitizedInBuckets: AnyRecord[] = [];

        // eslint-disable-next-line no-restricted-syntax
        for await (const record of parser) {
            if (lineNumber === 0) {
                // First line is headers line
                dataHeaders = await parseHeadersWithCustomFields(
                    context,
                    batchContext,
                    true,
                    factory,
                    templateContents,
                );

                ImportExportTemplate.validateCsvHeaders(dataHeaders, templateHeaders, await importExportTemplate.name, {
                    doInsert,
                    doUpdate,
                });
            }
            lineNumber += 1;
            // We skip all ignored line marked with 'IGNORE'
            if (record._ignore !== 'IGNORE') {
                // Data
                const rowSanitized = _.mapValues(record, (val: string) =>
                    val !== '' ? unescapeFormulaForInput(val) : null,
                );

                // Build different node payload from dead line
                const importRows = buildImportRowFromFlattenedCsv(rowSanitized, lineNumber, dataHeaders);

                // Then insert/update the database
                const recordRowObject = await recordRow(
                    context,
                    batchContext,
                    { previousImportRows, importRows, object, ancestors, csvRows, loop, rowsSanitizedInBuckets },
                    jsonBucket,
                    result,
                    loadInfo,
                    { ...rowSanitized, $lineNumber: lineNumber },
                );

                previousImportRows = recordRowObject.importRows;
                object = recordRowObject.object;
                ancestors = recordRowObject.ancestors;
                csvRows = recordRowObject.csvRows;
                loop = recordRowObject.loop;
                rowsSanitizedInBuckets = recordRowObject.rowsSanitizedInBuckets ?? [];
                if (!loop) break;
            }
        }
        if (loop && jsonBucket.size > 0) {
            await ImportExportTemplate.tryLoadRecordBucket(
                context,
                batchContext,
                result,
                jsonBucket,
                loadInfo,
                rowsSanitizedInBuckets,
            );
        }

        result.failedRows = result.failedRows.map((row: any) => _.mapValues(row, val => (val == null ? '' : val)));
        result.headers = contents.headers.includes('_error') ? contents.headers : [...contents.headers, '_error'];
        await batchContext.logMessage(
            'info',
            `[Import ${importResult._id}] Ended for '${filename}' uploaded file ${uploadedFile._id}/${uploadedFileKey}: ${result.rowsProcessed} rows, ${result.errorCount} errors`,
        );
        await context.runInWritableContext(
            importContext => importContext.notify('xtremImportExport/completed', { nodeName: factory.name }),
            { noCommit: dryRun, source: 'import' },
        );
    } catch (e) {
        logger.error(
            `General error while importing ${importResult._id} uploaded file ${uploadedFile._id}/${uploadedFileKey}: ${e.stack}`,
        );
        await batchContext.logMessage(
            'error',
            `General error while importing ${importResult._id} uploaded file ${uploadedFile._id}/${uploadedFileKey}: ${e.message}`,
        );
        result.generalError = e.message;
        result.status = 'failed';
    }

    return result;
}
