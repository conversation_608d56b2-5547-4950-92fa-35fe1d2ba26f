import { ImportStatus } from '@sage/xtrem-import-export-api';
import { ColoredElement } from '@sage/xtrem-system-api';
import {
    colorfulPillPattern,
    colorfulPillPatternDefaulted,
} from '@sage/xtrem-system/build/lib/client-functions/color-pattern';

function importExportStatusColor(status: ImportStatus, coloredElement: ColoredElement) {
    switch (status) {
        case 'inProgress':
            return colorfulPillPattern.filledInformation[coloredElement];
        case 'finished':
            return colorfulPillPattern.filledClosing[coloredElement];
        case 'rejected':
            return colorfulPillPattern.filledNegative[coloredElement];
        case 'pending':
            return colorfulPillPattern.filledCaution[coloredElement];
        default:
            return colorfulPillPattern.default[coloredElement];
    }
}

export function getLabelColorByStatus(
    enumEntry: string,
    status?: ImportStatus | null,
): {
    backgroundColor: string;
    borderColor: string;
    textColor: string;
} {
    const getColor = (coloredElement: ColoredElement) => {
        switch (enumEntry) {
            case 'ImportStatus':
                return importExportStatusColor(status as ImportStatus, coloredElement);

            default:
                return colorfulPillPatternDefaulted(coloredElement);
        }
    };

    return {
        backgroundColor: getColor('backgroundColor'),
        borderColor: getColor('borderColor'),
        textColor: getColor('textColor'),
    };
}

export function setBooleanStatusColors(booleanEntry: string, status: boolean, coloredElement: ColoredElement): string {
    switch (booleanEntry) {
        default:
            return colorfulPillPatternDefaulted(coloredElement);
    }
}
