import { StringDataType, TextStreamDataType } from '@sage/xtrem-core';

export const csvTemplateTextStreamType = new TextStreamDataType({
    maxLength: 32768,

    allowedContentTypes: ['text/plain'],
});

export const rowsTextStreamType = new TextStreamDataType({
    maxLength: 20000000,

    allowedContentTypes: ['text/plain', 'text/csv'],
});

export const csvStreamType = new TextStreamDataType({
    maxLength: 20000000,

    allowedContentTypes: ['text/csv'],
});

export const nameDataType = new StringDataType({ maxLength: 80 });
