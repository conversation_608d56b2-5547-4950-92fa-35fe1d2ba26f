{"extends": "../../tsconfig-base.json", "compilerOptions": {"outDir": "build", "rootDir": ".", "baseUrl": "."}, "include": ["index.ts", "application.ts", "lib/**/*", "test/**/*.ts", "test/**/*.json", "api/api.d.ts"], "exclude": ["lib/pages/**/*", "lib/widgets/**/*", "lib/page-extensions/**/*", "lib/page-fragments/**/*", "lib/stickers/**/*", "lib/i18n/**/*", "**/*.feature", "**/*.png", "lib/client-functions/**/*", "node_modules"], "references": [{"path": "../../shared/xtrem-async-helper"}, {"path": "../xtrem-authorization"}, {"path": "../../front-end/xtrem-client"}, {"path": "../xtrem-communication"}, {"path": "../../back-end/xtrem-core"}, {"path": "../xtrem-customization"}, {"path": "../../shared/xtrem-date-time"}, {"path": "../../shared/xtrem-decimal"}, {"path": "../xtrem-infrastructure-adapter"}, {"path": "../xtrem-metadata"}, {"path": "../../shared/xtrem-shared"}, {"path": "../xtrem-system"}, {"path": "../../front-end/xtrem-ui"}, {"path": "../../front-end/xtrem-ui-plugin-monaco"}, {"path": "../xtrem-upload"}, {"path": "../../cli/xtrem-cli"}, {"path": "api"}, {"path": "../xtrem-system/api"}]}