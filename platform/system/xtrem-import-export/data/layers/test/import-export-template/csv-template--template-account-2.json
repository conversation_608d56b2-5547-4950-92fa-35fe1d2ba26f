{"data": [{"_id": "0", "path": "id", "locale": "", "dataType": "string", "description": "id"}, {"_id": "10", "path": "chartOfAccount(name)", "locale": "", "dataType": "string", "description": "chartOfAccount(name)"}, {"_id": "20", "path": "isActive", "locale": "", "dataType": "boolean", "description": "isActive"}, {"_id": "30", "path": "isControl", "locale": "", "dataType": "boolean", "description": "isControl"}, {"_id": "40", "path": "name", "locale": "", "dataType": "string", "description": "name"}, {"_id": "50", "path": "isDirectPostingForbidden", "locale": "", "dataType": "boolean", "description": "isDirectPostingForbidden"}, {"_id": "60", "path": "taxManagement", "locale": "", "dataType": "reference", "description": "taxManagement"}]}