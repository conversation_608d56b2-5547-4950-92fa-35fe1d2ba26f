{"data": [{"_id": "0", "path": "!id", "locale": "", "dataType": "string", "isCustom": false, "description": "id"}, {"_id": "10", "path": "*name", "locale": "", "dataType": "string", "isCustom": false, "description": "name"}, {"_id": "20", "path": "description", "locale": "", "dataType": "string", "isCustom": false, "description": "description"}, {"_id": "30", "path": "isActive", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is active (false/true)"}, {"_id": "40", "path": "*legalCompany", "locale": "", "dataType": "reference", "isCustom": false, "description": "legal company (#id)"}, {"_id": "50", "path": "*businessEntity", "locale": "", "dataType": "reference", "isCustom": false, "description": "business entity (#id)"}, {"_id": "60", "path": "isFinance", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is finance (false/true)"}, {"_id": "70", "path": "isPurchase", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is purchase (false/true)"}, {"_id": "80", "path": "isInventory", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is inventory (false/true)"}, {"_id": "90", "path": "isSales", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is sales (false/true)"}, {"_id": "100", "path": "isManufacturing", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is manufacturing (false/true)"}, {"_id": "110", "path": "isProjectManagement", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is project management (false/true)"}, {"_id": "120", "path": "primaryAddress", "locale": "", "dataType": "reference", "isCustom": false, "description": "primary address (#businessEntity|_sortValue)"}, {"_id": "130", "path": "financialSite", "locale": "", "dataType": "reference", "isCustom": false, "description": "financial site (#id)"}, {"_id": "140", "path": "isLocationManaged", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is location managed (false/true)"}, {"_id": "150", "path": "defaultLocation", "locale": "", "dataType": "reference", "isCustom": false, "description": "default location (#id|locationZone)"}, {"_id": "160", "path": "sequenceNumberId", "locale": "", "dataType": "string", "isCustom": false, "description": "sequence number id"}, {"_id": "165", "path": "timeZone", "locale": "", "dataType": "string", "isCustom": false, "description": "time zone"}, {"_id": "170", "path": "defaultStockStatus", "locale": "", "dataType": "reference", "isCustom": false, "description": "default stock status (#id)"}, {"_id": "180", "path": "isPurchaseRequisitionApprovalManaged", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is purchase requisition approval managed (true/false)"}, {"_id": "190", "path": "purchaseRequisitionDefaultApprover", "locale": "", "dataType": "reference", "isCustom": false, "description": "purchase requisition default approver (#email)"}, {"_id": "200", "path": "purchaseRequisitionSubstituteApprover", "locale": "", "dataType": "reference", "isCustom": false, "description": "purchase requisition substitute approver (#email)"}, {"_id": "210", "path": "isPurchaseOrderApprovalManaged", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is purchase order approval managed (true/false)"}, {"_id": "220", "path": "purchaseOrderDefaultApprover", "locale": "", "dataType": "reference", "isCustom": false, "description": "purchase order default approver (#email)"}, {"_id": "230", "path": "purchaseOrderSubstituteApprover", "locale": "", "dataType": "reference", "isCustom": false, "description": "purchase order substitute approver (#email)"}, {"_id": "240", "path": "isSalesReturnRequestApprovalManaged", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is sales return request approval managed (true/false)"}, {"_id": "250", "path": "salesReturnRequestDefaultApprover", "locale": "", "dataType": "reference", "isCustom": false, "description": "sales return request default approver (#email)"}, {"_id": "260", "path": "salesReturnRequestSubstituteApprover", "locale": "", "dataType": "reference", "isCustom": false, "description": "sales return request substitute approver (#email)"}]}