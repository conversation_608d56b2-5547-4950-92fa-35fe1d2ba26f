{"data": [{"_id": "0", "path": "!id", "locale": "", "dataType": "string", "isCustom": false, "description": "id"}, {"_id": "10", "path": "isActive", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is active (true/false)"}, {"_id": "20", "path": "*name", "locale": "", "dataType": "string", "isCustom": false, "description": "name"}, {"_id": "30", "path": "description", "locale": "", "dataType": "string", "isCustom": false, "description": "description"}, {"_id": "40", "path": "*legislation", "locale": "", "dataType": "reference", "isCustom": false, "description": "legislation (#id)"}, {"_id": "50", "path": "chartOfAccount", "locale": "", "dataType": "reference", "isCustom": false, "description": "chart of account (#id)"}, {"_id": "60", "path": "siren", "locale": "", "dataType": "string", "isCustom": false, "description": "siren"}, {"_id": "70", "path": "naf", "locale": "", "dataType": "string", "isCustom": false, "description": "naf"}, {"_id": "80", "path": "rcs", "locale": "", "dataType": "string", "isCustom": false, "description": "rcs"}, {"_id": "90", "path": "legalForm", "locale": "", "dataType": "enum(SARL,EURL,SELARL,SA,SAS,SASU,SNC,SCP)", "isCustom": false, "description": "legal form"}, {"_id": "100", "path": "*country", "locale": "", "dataType": "reference", "isCustom": false, "description": "country (#id)"}, {"_id": "110", "path": "*currency", "locale": "", "dataType": "reference", "isCustom": false, "description": "currency (#id)"}, {"_id": "120", "path": "sequenceNumberId", "locale": "", "dataType": "string", "isCustom": false, "description": "sequence number id"}, {"_id": "130", "path": "customerOnHoldCheck", "locale": "", "dataType": "enum(blocking,warning,none)", "isCustom": false, "description": "customer on hold check"}, {"_id": "140", "path": "postingClass", "locale": "", "dataType": "reference", "isCustom": false, "description": "posting class (#id)"}, {"_id": "150", "path": "taxEngine", "locale": "", "dataType": "enum(avalaraAvaTax,genericTaxCalculation)", "isCustom": false, "description": "tax engine"}, {"_id": "160", "path": "doStockPosting", "locale": "", "dataType": "boolean", "isCustom": false, "description": "do stock posting (false/true)"}, {"_id": "170", "path": "doWipPosting", "locale": "", "dataType": "boolean", "isCustom": false, "description": "do wip posting (false/true)"}, {"_id": "180", "path": "doArPosting", "locale": "", "dataType": "boolean", "isCustom": false, "description": "do ar posting (false/true)"}, {"_id": "190", "path": "doApPosting", "locale": "", "dataType": "boolean", "isCustom": false, "description": "do ap posting (false/true)"}, {"_id": "200", "path": "doNonAbsorbedPosting", "locale": "", "dataType": "boolean", "isCustom": false, "description": "do non absorbed posting (false/true)"}, {"_id": "210", "path": "doUpdateArAmountPaid", "locale": "", "dataType": "boolean", "isCustom": false, "description": "do update ar amount paid (false/true)"}, {"_id": "220", "path": "serviceFabricExternalId", "locale": "", "dataType": "string", "isCustom": false, "description": "service fabric external id"}, {"_id": "230", "path": "serviceFabricCompanyId", "locale": "", "dataType": "string", "isCustom": false, "description": "service fabric company id"}, {"_id": "240", "path": "#addresses", "locale": "", "dataType": "collection", "isCustom": false, "description": "addresses"}, {"_id": "250", "path": "isActive#1", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is active (true/false)"}, {"_id": "260", "path": "*name#1", "locale": "", "dataType": "string", "isCustom": false, "description": "name"}, {"_id": "270", "path": "addressLine1", "locale": "", "dataType": "string", "isCustom": false, "description": "address line 1"}, {"_id": "280", "path": "addressLine2", "locale": "", "dataType": "string", "isCustom": false, "description": "address line 2"}, {"_id": "290", "path": "city", "locale": "", "dataType": "string", "isCustom": false, "description": "city"}, {"_id": "300", "path": "region", "locale": "", "dataType": "string", "isCustom": false, "description": "region"}, {"_id": "310", "path": "postcode", "locale": "", "dataType": "string", "isCustom": false, "description": "postcode"}, {"_id": "320", "path": "country", "locale": "", "dataType": "reference", "isCustom": false, "description": "country (#id)"}, {"_id": "330", "path": "locationPhoneNumber", "locale": "", "dataType": "string", "isCustom": false, "description": "location phone number"}, {"_id": "340", "path": "isPrimary", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is primary (false/true)"}, {"_id": "350", "path": "##contacts", "locale": "", "dataType": "collection", "isCustom": false, "description": "contacts"}, {"_id": "360", "path": "isActive#2", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is active (false/true)"}, {"_id": "370", "path": "*title", "locale": "", "dataType": "enum(ms,mr,dr,mrs)", "isCustom": false, "description": "title"}, {"_id": "380", "path": "*firstName", "locale": "", "dataType": "string", "isCustom": false, "description": "first name"}, {"_id": "390", "path": "*lastName", "locale": "", "dataType": "string", "isCustom": false, "description": "last name"}, {"_id": "400", "path": "preferredName", "locale": "", "dataType": "string", "isCustom": false, "description": "preferred name"}, {"_id": "410", "path": "role", "locale": "", "dataType": "enum(mainContact,commercialContact,financialContact)", "isCustom": false, "description": "role"}, {"_id": "420", "path": "position", "locale": "", "dataType": "string", "isCustom": false, "description": "position"}, {"_id": "430", "path": "locationPhoneNumber#1", "locale": "", "dataType": "string", "isCustom": false, "description": "location phone number"}, {"_id": "440", "path": "email", "locale": "", "dataType": "string", "isCustom": false, "description": "email"}, {"_id": "450", "path": "isPrimary#1", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is primary (false/true)"}, {"_id": "460", "path": "#attributeTypes", "locale": "", "dataType": "collection", "isCustom": false, "description": "attribute types"}, {"_id": "470", "path": "*attributeType", "locale": "", "dataType": "reference", "isCustom": false, "description": "attribute type (#id)"}, {"_id": "480", "path": "isRequired", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is required (true/false)"}, {"_id": "490", "path": "#dimensionTypes", "locale": "", "dataType": "collection", "isCustom": false, "description": "dimension types"}, {"_id": "500", "path": "*dimensionType", "locale": "", "dataType": "reference", "isCustom": false, "description": "dimension type (#docProperty)"}, {"_id": "510", "path": "isRequired#1", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is required (true/false)"}, {"_id": "520", "path": "#defaultAttributes", "locale": "", "dataType": "collection", "isCustom": false, "description": "default attributes"}, {"_id": "530", "path": "!attributeType", "locale": "", "dataType": "reference", "isCustom": false, "description": "attribute type (#id)"}, {"_id": "540", "path": "!dimensionDefinitionLevel", "locale": "", "dataType": "enum(manufacturingDirect,salesDirect,stockDirect,purchasingDirect,manufacturingOrderToOrder,purchasingOrderToOrder)", "isCustom": false, "description": "dimension definition level"}, {"_id": "550", "path": "masterDataDefault", "locale": "", "dataType": "enum(site,sourceDocument,customer,supplier)", "isCustom": false, "description": "master data default"}, {"_id": "560", "path": "#defaultDimensions", "locale": "", "dataType": "collection", "isCustom": false, "description": "default dimensions"}, {"_id": "570", "path": "!dimensionType", "locale": "", "dataType": "reference", "isCustom": false, "description": "dimension type (#docProperty)"}, {"_id": "580", "path": "!dimensionDefinitionLevel#1", "locale": "", "dataType": "enum(manufacturing,sales)", "isCustom": false, "description": "dimension definition level"}, {"_id": "590", "path": "masterDataDefault#1", "locale": "", "dataType": "enum(site,sourceDocument,customer,supplier)", "isCustom": false, "description": "master data default"}]}