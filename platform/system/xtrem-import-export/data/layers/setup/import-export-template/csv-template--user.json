{"data": [{"_id": "0", "path": "!email", "locale": "", "dataType": "string", "isCustom": false, "description": "email"}, {"_id": "10", "path": "isFirstAdminUser", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is first admin user (false/true)"}, {"_id": "20", "path": "*firstName", "locale": "", "dataType": "string", "isCustom": false, "description": "first name"}, {"_id": "30", "path": "*lastName", "locale": "", "dataType": "string", "isCustom": false, "description": "last name"}, {"_id": "40", "path": "isActive", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is active (true/false)"}, {"_id": "50", "path": "photo", "locale": "", "dataType": "binaryStream", "isCustom": false, "description": "photo"}, {"_id": "60", "path": "userType", "locale": "", "dataType": "enum(application,system)", "isCustom": false, "description": "user type"}, {"_id": "70", "path": "isAdministrator", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is administrator (false/true)"}, {"_id": "80", "path": "isApiUser", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is api user (false/true)"}, {"_id": "90", "path": "intacctId", "locale": "", "dataType": "string", "isCustom": false, "description": "intacct id"}, {"_id": "100", "path": "recordNo", "locale": "", "dataType": "integer", "isCustom": false, "description": "record no"}, {"_id": "110", "path": "isIntacct", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is intacct (false/true)"}, {"_id": "115", "path": "isDemoPersona", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is demo persona (false/true)"}, {"_id": "120", "path": "/preferences", "locale": "", "dataType": "reference", "isCustom": false, "description": "preferences (#user)"}, {"_id": "130", "path": "isWelcomeMailSent", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is welcome mail sent (false/true)"}, {"_id": "140", "path": "/navigation", "locale": "", "dataType": "reference", "isCustom": false, "description": "navigation (#user)"}, {"_id": "150", "path": "history", "locale": "", "dataType": "stringArray", "isCustom": false, "description": "history"}, {"_id": "160", "path": "bookmarks", "locale": "", "dataType": "stringArray", "isCustom": false, "description": "bookmarks"}, {"_id": "170", "path": "/billingRole", "locale": "", "dataType": "reference", "isCustom": false, "description": "billing role (#user)"}, {"_id": "180", "path": "*role", "locale": "", "dataType": "reference", "isCustom": false, "description": "role (#id)"}, {"_id": "190", "path": "#authorizationGroup", "locale": "", "dataType": "collection", "isCustom": false, "description": "authorization group"}, {"_id": "200", "path": "isActive#1", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is active (true/false)"}, {"_id": "210", "path": "*group", "locale": "", "dataType": "reference", "isCustom": false, "description": "group (#id)"}]}