{"data": [{"_id": "0", "path": "!name", "locale": "", "dataType": "string", "isCustom": false, "description": "name"}, {"_id": "10", "path": "*description", "locale": "", "dataType": "string", "isCustom": false, "description": "description"}, {"_id": "20", "path": "*parentPackage", "locale": "", "dataType": "string", "isCustom": false, "description": "parent package"}, {"_id": "30", "path": "activeTemplate", "locale": "", "dataType": "reference", "isCustom": false, "description": "active template (#name)"}, {"_id": "40", "path": "reportType", "locale": "", "dataType": "enum(printedDocument,email)", "isCustom": false, "description": "report type"}, {"_id": "50", "path": "isFactory", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is factory (false/true)"}, {"_id": "60", "path": "#variables", "locale": "", "dataType": "collection", "isCustom": false, "description": "variables"}, {"_id": "70", "path": "!_sortValue", "locale": "", "dataType": "integer", "isCustom": false, "description": "sort value"}, {"_id": "80", "path": "*name", "locale": "", "dataType": "string", "isCustom": false, "description": "name"}, {"_id": "90", "path": "title", "locale": "base", "dataType": "localized text", "isCustom": false, "description": "\"default locale: base, other locales can be specified with (locale-name), for example \"\"title (de-DE)\"\". Available locales are (en-GB, fr-FR, ...). You can also duplicate the columns to import several translations\""}, {"_id": "100", "path": "isMandatory", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is mandatory (false/true)"}, {"_id": "110", "path": "type", "locale": "", "dataType": "enum(boolean,string,byte,short,integer,decimal,float,double,enum,date,time,datetime,uuid,binaryStream,textStream,json,reference,collection,jsonReference,integerArray,enumArray,referenceArray,stringArray,integerRange,decimalRange,dateRange,datetimeRange)", "isCustom": false, "description": "type"}, {"_id": "120", "path": "dataType", "locale": "", "dataType": "reference", "isCustom": false, "description": "data type (#name)"}]}