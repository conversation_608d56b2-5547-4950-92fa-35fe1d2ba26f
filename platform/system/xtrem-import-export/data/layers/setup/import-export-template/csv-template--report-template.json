{"data": [{"_id": "0", "path": "!name", "locale": "", "dataType": "string", "isCustom": false, "description": "name"}, {"_id": "10", "path": "htmlTemplate", "locale": "", "dataType": "textStream", "isCustom": false, "description": "html template"}, {"_id": "20", "path": "attachmentTemplate", "locale": "", "dataType": "textStream", "isCustom": false, "description": "attachment template"}, {"_id": "30", "path": "attachmentName", "locale": "", "dataType": "string", "isCustom": false, "description": "attachment name"}, {"_id": "40", "path": "attachmentMimeType", "locale": "", "dataType": "string", "isCustom": false, "description": "attachment mime type"}, {"_id": "50", "path": "headerHtmlTemplate", "locale": "", "dataType": "textStream", "isCustom": false, "description": "header html template"}, {"_id": "60", "path": "footerHtmlTemplate", "locale": "", "dataType": "textStream", "isCustom": false, "description": "footer html template"}, {"_id": "70", "path": "styleSheet", "locale": "", "dataType": "textStream", "isCustom": false, "description": "style sheet"}, {"_id": "80", "path": "query", "locale": "", "dataType": "textStream", "isCustom": false, "description": "query"}, {"_id": "90", "path": "code", "locale": "", "dataType": "textStream", "isCustom": false, "description": "code"}, {"_id": "100", "path": "report", "locale": "", "dataType": "reference", "isCustom": false, "description": "report (#name)"}, {"_id": "110", "path": "isFactory", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is factory (false/true)"}, {"_id": "120", "path": "isExpertDocument", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is expert document (false/true)"}, {"_id": "130", "path": "baseLocale", "locale": "", "dataType": "enum(ar_SA,de_DE,en_GB,en_US,es_ES,fr_FR,it_IT,pl_PL,pt_BR,pt_PT,zh_CN)", "isCustom": false, "description": "base locale"}, {"_id": "140", "path": "defaultPaperFormat", "locale": "", "dataType": "enum(letter,legal,tabloid,ledger,a0,a1,a2,a3,a4,a5,a6)", "isCustom": false, "description": "default paper format"}, {"_id": "150", "path": "defaultPageOrientation", "locale": "", "dataType": "enum(portrait,landscape)", "isCustom": false, "description": "default page orientation"}, {"_id": "160", "path": "defaultLeftMargin", "locale": "", "dataType": "decimal", "isCustom": false, "description": "default left margin"}, {"_id": "170", "path": "defaultRightMargin", "locale": "", "dataType": "decimal", "isCustom": false, "description": "default right margin"}, {"_id": "180", "path": "defaultTopMargin", "locale": "", "dataType": "decimal", "isCustom": false, "description": "default top margin"}, {"_id": "190", "path": "defaultBottomMargin", "locale": "", "dataType": "decimal", "isCustom": false, "description": "default bottom margin"}, {"_id": "200", "path": "isDefaultHeaderFooter", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is default header footer (false/true)"}, {"_id": "210", "path": "/reportWizard", "locale": "", "dataType": "reference", "isCustom": false, "description": "report wizard (#reportTemplate)"}, {"_id": "220", "path": "*id", "locale": "", "dataType": "string", "isCustom": false, "description": "id"}, {"_id": "230", "path": "*name", "locale": "", "dataType": "string", "isCustom": false, "description": "name"}, {"_id": "240", "path": "*description", "locale": "", "dataType": "string", "isCustom": false, "description": "description"}, {"_id": "250", "path": "*dataSource", "locale": "", "dataType": "reference", "isCustom": false, "description": "data source (#name)"}, {"_id": "260", "path": "selectedProperties", "locale": "", "dataType": "json", "isCustom": false, "description": "selected properties"}, {"_id": "270", "path": "filters", "locale": "", "dataType": "json", "isCustom": false, "description": "filters"}, {"_id": "280", "path": "parameters", "locale": "", "dataType": "json", "isCustom": false, "description": "parameters"}, {"_id": "290", "path": "content", "locale": "", "dataType": "json", "isCustom": false, "description": "content"}, {"_id": "300", "path": "templateType", "locale": "", "dataType": "enum(list,form,advanced)", "isCustom": false, "description": "template type"}, {"_id": "310", "path": "#translatableTexts", "locale": "", "dataType": "collection", "isCustom": false, "description": "translatable texts"}, {"_id": "320", "path": "!_sortValue", "locale": "", "dataType": "integer", "isCustom": false, "description": "sort value"}, {"_id": "330", "path": "originalText", "locale": "", "dataType": "reference", "isCustom": false, "description": "original text (#reportTemplate|_sortValue)"}, {"_id": "340", "path": "hash", "locale": "", "dataType": "string", "isCustom": false, "description": "hash"}, {"_id": "350", "path": "locale", "locale": "", "dataType": "enum(ar_SA,de_DE,en_GB,en_US,es_ES,fr_FR,it_IT,pl_PL,pt_BR,pt_PT,zh_CN)", "isCustom": false, "description": "locale"}, {"_id": "360", "path": "isBaseLocale", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is base locale (false/true)"}, {"_id": "370", "path": "text", "locale": "", "dataType": "string", "isCustom": false, "description": "text"}]}