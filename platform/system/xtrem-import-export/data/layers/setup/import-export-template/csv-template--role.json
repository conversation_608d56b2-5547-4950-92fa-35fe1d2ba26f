{"data": [{"_id": "0", "path": "isActive", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is active (true/false)"}, {"_id": "10", "path": "name", "locale": "en-US", "dataType": "localized text", "isCustom": false, "description": "\"default locale: en-US, other locales can be specified with (locale-name), for example \"\"name (de-DE)\"\". Available locales are (en-US, ...). You can also duplicate the columns to import several translations\""}, {"_id": "20", "path": "description", "locale": "", "dataType": "string", "isCustom": false, "description": "description"}, {"_id": "30", "path": "!id", "locale": "", "dataType": "string", "isCustom": false, "description": "id"}, {"_id": "40", "path": "isBillingRole", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is billing role (false/true)"}, {"_id": "50", "path": "#roles", "locale": "", "dataType": "collection", "isCustom": false, "description": "roles"}, {"_id": "60", "path": "!_sortValue", "locale": "", "dataType": "integer", "isCustom": false, "description": "sort value"}, {"_id": "70", "path": "*role", "locale": "", "dataType": "reference", "isCustom": false, "description": "role (#id)"}, {"_id": "80", "path": "#activities", "locale": "", "dataType": "collection", "isCustom": false, "description": "activities"}, {"_id": "90", "path": "_sortValue", "locale": "", "dataType": "integer", "isCustom": false, "description": "sort value"}, {"_id": "100", "path": "!activity", "locale": "", "dataType": "reference", "isCustom": false, "description": "activity (#name)"}, {"_id": "110", "path": "hasAllPermissions", "locale": "", "dataType": "boolean", "isCustom": false, "description": "has all permissions (false/true)"}, {"_id": "120", "path": "permissions", "locale": "", "dataType": "stringArray", "isCustom": false, "description": "permissions"}, {"_id": "130", "path": "isActive#1", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is active (false/true)"}]}