{"data": [{"_id": "0", "path": "!id", "locale": "", "dataType": "string", "isCustom": false, "description": "id"}, {"_id": "10", "path": "name", "locale": "en-US", "dataType": "localized text", "isCustom": false, "description": "\"default locale: en-US, other locales can be specified with (locale-name), for example \"\"name (de-DE)\"\". Available locales are (en-US, ...). You can also duplicate the columns to import several translations\""}, {"_id": "20", "path": "#groupRoles", "locale": "", "dataType": "collection", "isCustom": false, "description": "group roles"}, {"_id": "30", "path": "!_sortValue", "locale": "", "dataType": "integer", "isCustom": false, "description": "sort value"}, {"_id": "40", "path": "*role", "locale": "", "dataType": "reference", "isCustom": false, "description": "role (#id)"}, {"_id": "50", "path": "#groupSites", "locale": "", "dataType": "collection", "isCustom": false, "description": "group sites"}, {"_id": "60", "path": "!_sortValue#1", "locale": "", "dataType": "integer", "isCustom": false, "description": "sort value"}, {"_id": "70", "path": "*siteGroup", "locale": "", "dataType": "reference", "isCustom": false, "description": "site group (#id)"}]}