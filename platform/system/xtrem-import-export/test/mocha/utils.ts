import { Context, CsvTemplateContent, NodeCreateData, Test, TextStream } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import { assert } from 'chai';
import * as lodash from 'lodash';
import { TemplateUse } from '../../lib/enums/template-use';
import { defaultDelimiter } from '../../lib/functions/common';
import { ImportExportTemplate } from '../../lib/nodes/_index';
import { TestItem, TestItemRef, TestItemRefNoNaturalKey, TestNonVitalRef } from '../fixtures/lib/nodes';

export async function createImportExportTemplate(
    context: Context,
    importExportTemplateData: {
        id: string;
        name: string;
        description: string;
        nodeName: string;
        isDefault?: boolean;
        templateUse?: TemplateUse;
        csvTemplate: { data: CsvTemplateContent[] };
    },
): Promise<ImportExportTemplate> {
    const importExportTemplate = await context.create(ImportExportTemplate, importExportTemplateData);
    await importExportTemplate.$.save();
    return importExportTemplate;
}

export async function createTestItem(
    context: Context,
    data: { name: string; code: string; ref: number },
): Promise<TestItem> {
    const item = await context.create(TestItem, data);
    await item.$.save();
    return item;
}

export async function createTestItemNoNaturalKey(
    context: Context,
    data: { refName: string; refCode: string },
): Promise<TestItemRefNoNaturalKey> {
    const item = await context.create(TestItemRefNoNaturalKey, data);
    await item.$.save();
    return item;
}

export async function createTestItemRef(
    context: Context,
    data: { refName: string; refCode: string },
): Promise<TestItemRef> {
    const item = await context.create(TestItemRef, data);
    await item.$.save();
    return item;
}

export async function createTestNonVitalRef(
    context: Context,
    data: NodeCreateData<TestNonVitalRef>,
): Promise<TestNonVitalRef> {
    const testNonVitalRef = await context.create(TestNonVitalRef, data);
    await testNonVitalRef.$.save();
    return testNonVitalRef;
}

export const templateTestCustomField = {
    id: 'templateTestCustomField',
    name: 'templateTestCustomField',
    nodeName: 'TestCustomField',
    description: '1 description',
    csvTemplate: new TextStream(
        [
            '_customData(customBoolean)',
            '_customData(customDecimalNumeric)',
            '_customData(customDoubleNumeric)',
            '_customData(customFloatNumeric)',
            '_customData(customIntegerNumeric)',
            '_customData(customString)',
            '!name',
            '_customData(customSelect)',
            '_customData(customDate)',
        ].join(defaultDelimiter),
        'text/plain',
    ),
};

export const templateTestTextStream = {
    id: 'templateTestTextStream',
    name: 'templateTestTextStream',
    nodeName: 'TestTextStream',
    description: '1 description',
    csvTemplate: new TextStream(['!name', 'html'].join(defaultDelimiter), 'text/plain'),
};

export const templateTestServiceOptionActive = {
    id: 'templateTestServiceOption',
    name: 'templateTestServiceOption',
    nodeName: 'TestServiceOption',
    description: '1 description',
    csvTemplate: new TextStream(['!name', 'code', '#lines', '*label'].join(defaultDelimiter), 'text/plain'),
};

export const templateTestServiceOptionInactive = {
    id: 'templateTestServiceOption',
    name: 'templateTestServiceOption',
    nodeName: 'TestServiceOption',
    description: '1 description',
    csvTemplate: new TextStream(['!name'].join(defaultDelimiter), 'text/plain'),
};

export const templateTestAccessAuthorized = {
    id: 'templateTestAccessAuthorized',
    name: 'templateTestAccessAuthorized',
    nodeName: 'TestAccessAuthorized',
    description: '1 description',
    csvTemplate: new TextStream(['!name', 'code', 'ref'].join(defaultDelimiter), 'text/plain'),
};

export const templateTestDoc = {
    id: 'templateTestDoc',
    name: 'templateTestDoc',
    nodeName: 'TestDoc',
    description: '1 description',
    csvTemplate: new TextStream(
        [
            '!name',
            '*code',
            'description',
            'stringArrayVal',
            'shortVal',
            'integerVal',
            'integerArrayVal',
            'decimalVal',
            'booleanVal',
            'jsonVal',
            'datetimeVal',
            'dateRangeVal',
            'datetimeRangeVal',
            'enumVal',
            'enumArrayVal',
            'transientInput',
            '#lines',
            '*label',
            '##sublines',
            '*label#1',
            'description#1',
            'item',
            '//testContentAddressable',
            '*name',
            '*description',
            '#comments',
            'text',
            '*name#1',
            '*description#1',
        ].join(defaultDelimiter),
        'text/plain',
    ),
};

export const importExportTemplateTestTextStream = {
    id: 'templateTestTextStream',
    name: 'templateTestTextStream',
    nodeName: 'TestTextStream',
    description: '1 description',
    csvTemplate: {
        data: [
            { _id: 0, path: '!name', dataType: 'string', description: '!name', locale: '' },
            { _id: 10, path: 'html', dataType: 'textStream', description: 'html', locale: '' },
        ],
    },
};

export const importExportTemplateTestDocRefDefaultValue = {
    id: 'templateTestDocRefDefaultValue',
    name: 'templateTestDocRefDefaultValue',
    nodeName: 'TestDocRefDefaultValue',
    description: '1 description',
    csvTemplate: {
        data: [
            { _id: 0, path: '!name', dataType: 'string', description: '!name', locale: '' },
            { _id: 10, path: '*code', dataType: 'string', description: '*code', locale: '' },
            { _id: 20, path: 'description', dataType: 'string', description: 'description', locale: '' },
            { _id: 30, path: 'stringArrayVal', dataType: 'stringArrayVal', description: 'stringArrayVal', locale: '' },
            { _id: 40, path: 'shortVal', dataType: 'shortVal', description: 'shortVal', locale: '' },
            { _id: 50, path: 'integerVal', dataType: 'integer', description: 'integerVal', locale: '' },
            { _id: 60, path: 'integerArrayVal', dataType: 'integerArray', description: 'integerArrayVal', locale: '' },
            { _id: 70, path: 'decimalVal', dataType: 'decimal', description: 'decimalVal', locale: '' },
            { _id: 80, path: 'booleanVal', dataType: 'boolean', description: 'booleanVal', locale: '' },
            { _id: 90, path: 'jsonVal', dataType: 'json', description: 'jsonVal', locale: '' },
            { _id: 100, path: 'datetimeVal', dataType: 'datetime', description: 'datetimeVal', locale: '' },
            {
                _id: 110,
                path: 'datetimeRangeVal',
                dataType: 'datetimeRange',
                description: 'datetimeRangeVal',
                locale: '',
            },
            {
                _id: 115,
                path: 'dateRangeVal',
                dataType: 'dateRange',
                description: 'dateRangeVal',
                locale: '',
            },
            { _id: 120, path: 'enumVal', dataType: 'enum', description: 'enumVal', locale: '' },
            { _id: 130, path: 'enumArrayVal', dataType: 'enumArray', description: 'enumArrayVal', locale: '' },
            { _id: 140, path: 'transientInput', dataType: 'transientInput', description: 'transientInput', locale: '' },
            {
                _id: 150,
                path: 'itemWithDefaultValueDocLevel',
                dataType: 'reference',
                description: 'itemWithDefaultValueDocLevel',
                locale: '',
            },
            { _id: 170, path: '#lines', dataType: 'collection', description: '#lines', locale: '' },
            { _id: 180, path: '*label#1', dataType: 'string', description: '*label#1', locale: '' },
            { _id: 190, path: 'description#1', dataType: 'string', description: 'description#1', locale: '' },
            { _id: 200, path: 'item', dataType: 'reference', description: 'item', locale: '' },
            {
                _id: 205,
                path: 'itemWithDefaultValueLineLevel',
                dataType: 'reference',
                description: 'itemWithDefaultValueLineLevel',
                locale: '',
            },
        ],
    },
};

export const importExportTemplateTestDocComment = {
    id: 'templateTDComment',
    name: 'templateTDComment',
    nodeName: 'TestDocComment',
    description: '1 description',
    csvTemplate: {
        data: [
            { _id: 0, path: '_sortValue', dataType: 'integer', description: '!name', locale: '' },
            { _id: 10, path: '!parentDoc', dataType: 'reference', description: 'parentDoc', locale: '' },
            { _id: 20, path: 'text', dataType: 'string', description: 'text', locale: '' },
            { _id: 30, path: 'name', dataType: 'string', description: 'name', locale: '' },
            { _id: 40, path: 'description#2', dataType: 'string', description: 'description#2', locale: '' },
        ],
    },
};

export const importExportTemplateTestDocVitalRef = {
    id: 'templateTestDocVitalRef',
    name: 'templateTestDocVitalRef',
    nodeName: 'TestDocVitalRef',
    description: '1 description',
    csvTemplate: {
        data: [
            { _id: 0, path: '!name', dataType: 'string', description: '!name', locale: '' },
            { _id: 10, path: '*code', dataType: 'string', description: 'code', locale: '' },
            {
                _id: 20,
                path: '/vitalRef',
                dataType: 'reference',
                description: '/vitalRef',
                locale: '',
            },
            { _id: 30, path: '*childRefName', dataType: 'string', description: '*childRefName', locale: '' },
            { _id: 40, path: 'description', dataType: 'string', description: 'description', locale: '' },
        ],
    },
};

export const importExportTemplateTestVitalRefChild = {
    id: 'templateTestVitalRefChild',
    name: 'templateTestVitalRefChild',
    nodeName: 'TestDocVitalRefChild',
    description: '1 description',
    csvTemplate: {
        data: [
            { _id: 10, path: '!parentDoc', dataType: 'reference', description: 'parentDoc', locale: '' },
            { _id: 20, path: '!childRefName', dataType: 'string', description: '!childRefName', locale: '' },
            { _id: 30, path: 'description', dataType: 'string', description: 'description', locale: '' },
        ],
    },
};

export const importExportTemplateTestDoc = {
    id: 'templateTestDoc',
    name: 'templateTestDoc',
    nodeName: 'TestDoc',
    description: '1 description',
    csvTemplate: {
        data: [
            { _id: 0, path: '!name', dataType: 'string', description: '!name', locale: '' },
            { _id: 10, path: '*code', dataType: 'string', description: '*code', locale: '' },
            { _id: 20, path: 'description', dataType: 'string', description: 'description', locale: '' },
            { _id: 30, path: 'stringArrayVal', dataType: 'stringArrayVal', description: 'stringArrayVal', locale: '' },
            { _id: 40, path: 'shortVal', dataType: 'shortVal', description: 'shortVal', locale: '' },
            { _id: 50, path: 'integerVal', dataType: 'integer', description: 'integerVal', locale: '' },
            { _id: 60, path: 'integerArrayVal', dataType: 'integerArray', description: 'integerArrayVal', locale: '' },
            { _id: 70, path: 'decimalVal', dataType: 'decimal', description: 'decimalVal', locale: '' },
            { _id: 80, path: 'booleanVal', dataType: 'boolean', description: 'booleanVal', locale: '' },
            { _id: 90, path: 'jsonVal', dataType: 'json', description: 'jsonVal', locale: '' },
            { _id: 100, path: 'datetimeVal', dataType: 'datetime', description: 'datetimeVal', locale: '' },
            {
                _id: 110,
                path: 'datetimeRangeVal',
                dataType: 'datetimeRange',
                description: 'datetimeRangeVal',
                locale: '',
            },
            {
                _id: 115,
                path: 'dateRangeVal',
                dataType: 'dateRange',
                description: 'dateRangeVal',
                locale: '',
            },
            { _id: 120, path: 'enumVal', dataType: 'enum', description: 'enumVal', locale: '' },
            { _id: 130, path: 'enumArrayVal', dataType: 'enumArray', description: 'enumArrayVal', locale: '' },
            { _id: 140, path: 'transientInput', dataType: 'transientInput', description: 'transientInput', locale: '' },
            { _id: 150, path: '#lines', dataType: 'collection', description: '#lines', locale: '' },
            { _id: 160, path: '*label', dataType: 'string', description: '*label', locale: '' },
            { _id: 170, path: '##sublines', dataType: 'collection', description: '##sublines', locale: '' },
            { _id: 180, path: '*label#1', dataType: 'string', description: '*label#1', locale: '' },
            { _id: 190, path: 'description#1', dataType: 'string', description: 'description#1', locale: '' },
            { _id: 200, path: 'item', dataType: 'reference', description: 'item', locale: '' },
            {
                _id: 210,
                path: '//testContentAddressable',
                dataType: 'reference',
                description: '//testContentAddressable',
                locale: '',
            },
            { _id: 220, path: '*name', dataType: 'string', description: '*name', locale: '' },
            { _id: 230, path: '*description', dataType: 'string', description: '*description', locale: '' },
            { _id: 240, path: '#comments', dataType: 'collection', description: '#comments', locale: '' },
            { _id: 250, path: 'text', dataType: 'string', description: 'text', locale: '' },
            { _id: 260, path: 'name', dataType: 'string', description: 'name', locale: '' },
            { _id: 270, path: 'description#2', dataType: 'string', description: 'description#2', locale: '' },
        ],
    },
};

export const importExportTemplateTestDocWithFrozenProperties = {
    id: 'templateTestDocWFP',
    name: 'templateTestDocWFP',
    nodeName: 'TestDocWithFrozenProperties',
    description: '1 description',
    csvTemplate: {
        data: [
            { _id: 0, path: '!name', dataType: 'string', description: '!name', locale: '' },
            { _id: 10, path: '*code', dataType: 'string', description: '*code', locale: '' },
            { _id: 20, path: 'description', dataType: 'string', description: 'description', locale: '' },
            { _id: 30, path: 'stringArrayVal', dataType: 'stringArrayVal', description: 'stringArrayVal', locale: '' },
            { _id: 40, path: 'shortVal', dataType: 'shortVal', description: 'shortVal', locale: '' },
            { _id: 50, path: 'integerVal', dataType: 'integer', description: 'integerVal', locale: '' },
            { _id: 60, path: 'integerArrayVal', dataType: 'integerArray', description: 'integerArrayVal', locale: '' },
            { _id: 70, path: 'decimalVal', dataType: 'decimal', description: 'decimalVal', locale: '' },
            { _id: 80, path: 'booleanVal', dataType: 'boolean', description: 'booleanVal', locale: '' },
            { _id: 90, path: 'jsonVal', dataType: 'json', description: 'jsonVal', locale: '' },
            { _id: 100, path: 'datetimeVal', dataType: 'datetime', description: 'datetimeVal', locale: '' },
            {
                _id: 110,
                path: 'datetimeRangeVal',
                dataType: 'datetimeRange',
                description: 'datetimeRangeVal',
                locale: '',
            },
            {
                _id: 115,
                path: 'dateRangeVal',
                dataType: 'dateRange',
                description: 'dateRangeVal',
                locale: '',
            },
            { _id: 120, path: 'enumVal', dataType: 'enum', description: 'enumVal', locale: '' },
            { _id: 130, path: 'enumArrayVal', dataType: 'enumArray', description: 'enumArrayVal', locale: '' },
            { _id: 140, path: 'transientInput', dataType: 'transientInput', description: 'transientInput', locale: '' },
            {
                _id: 145,
                path: 'itemWithDefaultValue',
                dataType: 'reference',
                description: 'itemWithDefaultValue',
                locale: '',
            },
            { _id: 150, path: '#lines', dataType: 'collection', description: '#lines', locale: '' },
            { _id: 160, path: '*label', dataType: 'string', description: '*label', locale: '' },
            { _id: 170, path: '##sublines', dataType: 'collection', description: '##sublines', locale: '' },
            { _id: 180, path: '*label#1', dataType: 'string', description: '*label#1', locale: '' },
            { _id: 190, path: 'description#1', dataType: 'string', description: 'description#1', locale: '' },
            { _id: 200, path: 'item', dataType: 'reference', description: 'item', locale: '' },
            {
                _id: 210,
                path: '//testContentAddressable',
                dataType: 'reference',
                description: '//testContentAddressable',
                locale: '',
            },
            { _id: 220, path: '*name', dataType: 'string', description: '*name', locale: '' },
            { _id: 230, path: '*description', dataType: 'string', description: '*description', locale: '' },
            { _id: 240, path: '#comments', dataType: 'collection', description: '#comments', locale: '' },
            { _id: 250, path: 'text', dataType: 'string', description: 'text', locale: '' },
            { _id: 260, path: 'name', dataType: 'string', description: 'name', locale: '' },
            { _id: 270, path: 'description#2', dataType: 'string', description: 'description#2', locale: '' },
        ],
    },
};

export const importExportTemplateTestDocWithSortValue = {
    id: 'templateTestDocSortValue',
    name: 'templateTestDocSortValue',
    nodeName: 'TestDocWithNonVitalRef',
    description: '1 description',
    csvTemplate: {
        data: [
            { _id: 0, path: '!name', dataType: 'string', description: '!name', locale: '' },
            { _id: 10, path: '*code', dataType: 'string', description: '*code', locale: '' },
            { _id: 140, path: '#lines', dataType: 'collection', description: '#lines', locale: '' },
            { _id: 150, path: '!_sortValue', dataType: 'integer', description: '_sortValue', locale: '' },
            { _id: 160, path: '*label', dataType: 'string', description: '*label', locale: '' },
            { _id: 170, path: '##subLines', dataType: 'collection', description: '##subLines', locale: '' },
            { _id: 175, path: '!_sortValue#1', dataType: 'integer', description: '_sortValue#1', locale: '' },
            { _id: 180, path: '*label#1', dataType: 'string', description: '*label#1', locale: '' },
            { _id: 190, path: 'description#1', dataType: 'string', description: 'description#1', locale: '' },
        ],
    },
};

export const importExportTemplateTestDocWithNonVitalRef = {
    id: 'templateTestDocWithNonVitalRef',
    name: 'templateTestDocWithNonVitalRef',
    nodeName: 'TestDocWithNonVitalRef',
    description: '1 description',
    csvTemplate: {
        data: [
            { _id: 0, path: '!name', dataType: 'string', description: '!name', locale: '' },
            { _id: 10, path: '*code', dataType: 'string', description: '*code', locale: '' },
            { _id: 20, path: 'description', dataType: 'string', description: 'description', locale: '' },
            { _id: 30, path: 'stringArrayVal', dataType: 'stringArrayVal', description: 'stringArrayVal', locale: '' },
            { _id: 40, path: 'shortVal', dataType: 'shortVal', description: 'shortVal', locale: '' },
            { _id: 50, path: 'integerVal', dataType: 'integer', description: 'integerVal', locale: '' },
            { _id: 60, path: 'integerArrayVal', dataType: 'integerArray', description: 'integerArrayVal', locale: '' },
            { _id: 70, path: 'decimalVal', dataType: 'decimal', description: 'decimalVal', locale: '' },
            { _id: 80, path: 'booleanVal', dataType: 'boolean', description: 'booleanVal', locale: '' },
            { _id: 90, path: 'jsonVal', dataType: 'json', description: 'jsonVal', locale: '' },
            { _id: 100, path: 'datetimeVal', dataType: 'datetime', description: 'datetimeVal', locale: '' },
            {
                _id: 110,
                path: 'dateRangeVal',
                dataType: 'dateRange',
                description: 'dateRangeVal',
                locale: '',
            },
            {
                _id: 120,
                path: 'datetimeRangeVal',
                dataType: 'datetimeRange',
                description: 'datetimeRangeVal',
                locale: '',
            },
            { _id: 130, path: 'enumVal', dataType: 'enum', description: 'enumVal', locale: '' },
            { _id: 140, path: 'enumArrayVal', dataType: 'enumArray', description: 'enumArrayVal', locale: '' },
            { _id: 150, path: 'transientInput', dataType: 'transientInput', description: 'transientInput', locale: '' },
            { _id: 160, path: 'nonVitalRef', dataType: 'reference', description: 'nonVitalRef', locale: '' },
            { _id: 161, path: 'nonVitalRef.name', dataType: 'string', description: 'name', locale: '' },
            { _id: 162, path: 'nonVitalRef.code', dataType: 'string', description: 'code', locale: '' },
            {
                _id: 163,
                path: 'nonVitalRef.description',
                dataType: 'localized text',
                description:
                    '"default locale: en-US, other locales can be specified with (locale-name), for example ""name (de-DE)"". Available locales are (en-GB, en-US, fr-FR, ...). You can also duplicate the columns to import several translations"',
                locale: 'en-US',
            },
            {
                _id: 164,
                path: 'nonVitalRef.stringArrayVal',
                dataType: 'string',
                description: 'string array val',
                locale: '',
            },
            { _id: 165, path: 'nonVitalRef.shortVal', dataType: 'short', description: 'short val', locale: '' },
            { _id: 166, path: 'nonVitalRef.integerVal', dataType: 'integer', description: 'integer val', locale: '' },
            {
                _id: 167,
                path: 'nonVitalRef.integerArrayVal',
                dataType: 'integer',
                description: 'integer array val',
                locale: '',
            },
            { _id: 168, path: 'nonVitalRef.decimalVal', dataType: 'decimal', description: 'decimal val', locale: '' },
            {
                _id: 169,
                path: 'nonVitalRef.booleanVal',
                dataType: 'boolean',
                description: 'boolean val (false/true)',
                locale: '',
            },
            { _id: 170, path: 'nonVitalRef.jsonVal', dataType: 'json', description: 'json val', locale: '' },
            {
                _id: 171,
                path: 'nonVitalRef.dateRangeVal',
                dataType: 'dateRange',
                description: 'date range val',
                locale: '',
            },
            {
                _id: 172,
                path: 'nonVitalRef.datetimeRangeVal',
                dataType: 'dateTimeRange',
                description: 'datetime range val',
                locale: '',
            },
            {
                _id: 173,
                path: 'nonVitalRef.enumVal',
                dataType: 'enum(value1,value2,value3)',
                description: 'enum val',
                locale: '',
            },
            { _id: 174, path: 'nonVitalRef.enumArrayVal', dataType: 'enum', description: 'enum array val', locale: '' },
            {
                _id: 175,
                path: 'nonVitalRef.subRef',
                dataType: 'reference',
                description: 'sub ref (#subRefName)',
                locale: '',
            },
            {
                _id: 176,
                path: 'nonVitalRef.subRefNoNaturalKey',
                dataType: 'reference',
                description: 'sub ref no natural key (_id)',
                locale: '',
            },
            {
                _id: 179,
                path: '/testContentAddressable',
                dataType: 'reference',
                description: '/testContentAddressable',
                locale: '',
            },
            { _id: 180, path: '*name', dataType: 'string', description: '*name', locale: '' },
            { _id: 190, path: '*description', dataType: 'string', description: '*description', locale: '' },
            { _id: 200, path: '#lines', dataType: 'collection', description: '#lines', locale: '' },
            { _id: 201, path: '_sortValue', dataType: 'integer', description: '_sortValue', locale: '' },
            {
                _id: 205,
                path: 'testDocLineWithNonVitalRef',
                dataType: 'reference',
                description: 'testDocLineWithNonVitalRef',
                locale: '',
            },
            { _id: 210, path: '*label', dataType: 'string', description: '*label', locale: '' },
            { _id: 220, path: 'nonVitalRef#1', dataType: 'reference', description: 'nonVitalRef#1', locale: '' },
            { _id: 221, path: 'lines.nonVitalRef.name', dataType: 'string', description: 'name', locale: '' },
            { _id: 222, path: 'lines.nonVitalRef.code', dataType: 'string', description: 'code', locale: '' },
            {
                _id: 223,
                path: 'lines.nonVitalRef.description',
                dataType: 'localized text',
                description:
                    '"default locale: en-US, other locales can be specified with (locale-name), for example ""name (de-DE)"". Available locales are (en-GB, en-US, fr-FR, ...). You can also duplicate the columns to import several translations"',
                locale: 'en-US',
            },
            {
                _id: 224,
                path: 'lines.nonVitalRef.stringArrayVal',
                dataType: 'string',
                description: 'string array val',
                locale: '',
            },
            { _id: 225, path: 'lines.nonVitalRef.shortVal', dataType: 'short', description: 'short val', locale: '' },
            {
                _id: 226,
                path: 'lines.nonVitalRef.integerVal',
                dataType: 'integer',
                description: 'integer val',
                locale: '',
            },
            {
                _id: 227,
                path: 'lines.nonVitalRef.integerArrayVal',
                dataType: 'integer',
                description: 'integer array val',
                locale: '',
            },
            {
                _id: 228,
                path: 'lines.nonVitalRef.decimalVal',
                dataType: 'decimal',
                description: 'decimal val',
                locale: '',
            },
            {
                _id: 229,
                path: 'lines.nonVitalRef.booleanVal',
                dataType: 'boolean',
                description: 'boolean val (false/true)',
                locale: '',
            },
            { _id: 230, path: 'lines.nonVitalRef.jsonVal', dataType: 'json', description: 'json val', locale: '' },
            {
                _id: 231,
                path: 'lines.nonVitalRef.dateRangeVal',
                dataType: 'dateRange',
                description: 'date range val',
                locale: '',
            },
            {
                _id: 232,
                path: 'lines.nonVitalRef.datetimeRangeVal',
                dataType: 'dateTimeRange',
                description: 'datetime range val',
                locale: '',
            },
            {
                _id: 233,
                path: 'lines.nonVitalRef.enumVal',
                dataType: 'enum(value1,value2,value3)',
                description: 'enum val',
                locale: '',
            },
            {
                _id: 234,
                path: 'lines.nonVitalRef.enumArrayVal',
                dataType: 'enum',
                description: 'enum array val',
                locale: '',
            },
            {
                _id: 235,
                path: 'lines.nonVitalRef.subRef',
                dataType: 'reference',
                description: 'sub ref (#subRefName)',
                locale: '',
            },
            {
                _id: 236,
                path: 'lines.nonVitalRef.subRefNoNaturalKey',
                dataType: 'reference',
                description: 'sub ref no natural key (_id)',
                locale: '',
            },
            {
                _id: 239,
                path: '//testContentAddressable',
                dataType: 'reference',
                description: '//testContentAddressable',
                locale: '',
            },
            { _id: 240, path: '*name#1', dataType: 'string', description: '*name#1', locale: '' },
            { _id: 250, path: '*description#1', dataType: 'string', description: '*description#1', locale: '' },
            { _id: 260, path: '##subLines', dataType: 'collection', description: '##subLines', locale: '' },
            { _id: 261, path: '_sortValue#1', dataType: 'integer', description: '_sortValue#1', locale: '' },
            {
                _id: 265,
                path: 'testDocSubLineWithNonVitalRef',
                dataType: 'reference',
                description: 'testDocSubLineWithNonVitalRef',
                locale: '',
            },
            { _id: 270, path: '*label#1', dataType: 'string', description: '*label#1', locale: '' },
            { _id: 280, path: 'description#1', dataType: 'string', description: 'description#1', locale: '' },
            { _id: 290, path: 'item', dataType: 'reference', description: 'item', locale: '' },
            { _id: 300, path: 'nonVitalRef#2', dataType: 'reference', description: 'nonVitalRef#2', locale: '' },
            { _id: 301, path: 'lines.subLines.nonVitalRef.name', dataType: 'string', description: 'name', locale: '' },
            { _id: 302, path: 'lines.subLines.nonVitalRef.code', dataType: 'string', description: 'code', locale: '' },
            {
                _id: 303,
                path: 'lines.subLines.nonVitalRef.description',
                dataType: 'localized text',
                description:
                    '"default locale: en-US, other locales can be specified with (locale-name), for example ""name (de-DE)"". Available locales are (en-GB, en-US, fr-FR, ...). You can also duplicate the columns to import several translations"',
                locale: 'en-US',
            },
            {
                _id: 304,
                path: 'lines.subLines.nonVitalRef.stringArrayVal',
                dataType: 'string',
                description: 'string array val',
                locale: '',
            },
            {
                _id: 305,
                path: 'lines.subLines.nonVitalRef.shortVal',
                dataType: 'short',
                description: 'short val',
                locale: '',
            },
            {
                _id: 306,
                path: 'lines.subLines.nonVitalRef.integerVal',
                dataType: 'integer',
                description: 'integer val',
                locale: '',
            },
            {
                _id: 307,
                path: 'lines.subLines.nonVitalRef.integerArrayVal',
                dataType: 'integer',
                description: 'integer array val',
                locale: '',
            },
            {
                _id: 308,
                path: 'lines.subLines.nonVitalRef.decimalVal',
                dataType: 'decimal',
                description: 'decimal val',
                locale: '',
            },
            {
                _id: 309,
                path: 'lines.subLines.nonVitalRef.booleanVal',
                dataType: 'boolean',
                description: 'boolean val (false/true)',
                locale: '',
            },
            {
                _id: 310,
                path: 'lines.subLines.nonVitalRef.jsonVal',
                dataType: 'json',
                description: 'json val',
                locale: '',
            },
            {
                _id: 311,
                path: 'lines.subLines.nonVitalRef.dateRangeVal',
                dataType: 'dateRange',
                description: 'date range val',
                locale: '',
            },
            {
                _id: 312,
                path: 'lines.subLines.nonVitalRef.datetimeRangeVal',
                dataType: 'dateTimeRange',
                description: 'datetime range val',
                locale: '',
            },
            {
                _id: 313,
                path: 'lines.subLines.nonVitalRef.enumVal',
                dataType: 'enum(value1,value2,value3)',
                description: 'enum val',
                locale: '',
            },
            {
                _id: 314,
                path: 'lines.subLines.nonVitalRef.enumArrayVal',
                dataType: 'enum',
                description: 'enum array val',
                locale: '',
            },
            {
                _id: 315,
                path: 'lines.subLines.nonVitalRef.subRef',
                dataType: 'reference',
                description: 'sub ref (#subRefName)',
                locale: '',
            },
            {
                _id: 316,
                path: 'lines.subLines.nonVitalRef.subRefNoNaturalKey',
                dataType: 'reference',
                description: 'sub ref no natural key (_id)',
                locale: '',
            },
            {
                _id: 319,
                path: '///testContentAddressable',
                dataType: 'reference',
                description: '///testContentAddressable',
                locale: '',
            },
            { _id: 320, path: '*name#2', dataType: 'string', description: '*name#2', locale: '' },
            { _id: 330, path: '*description#2', dataType: 'string', description: '*description#2', locale: '' },
        ],
    },
};

export const importExportTemplateTestDocWithoutMandatoryFields = {
    id: 'templateTestDoc',
    name: 'templateTestDoc',
    nodeName: 'TestDoc',
    description: '1 description',
    csvTemplate: {
        data: [
            { _id: 0, path: '!name', dataType: 'string', description: '!name', locale: '' },
            { _id: 10, path: '*code', dataType: 'string', description: '*code', locale: '' },
            { _id: 20, path: 'description', dataType: 'string', description: 'description', locale: '' },
            { _id: 30, path: 'stringArrayVal', dataType: 'stringArrayVal', description: 'stringArrayVal', locale: '' },
            { _id: 40, path: 'shortVal', dataType: 'shortVal', description: 'shortVal', locale: '' },
            { _id: 50, path: 'integerVal', dataType: 'integer', description: 'integerVal', locale: '' },
            { _id: 60, path: 'integerArrayVal', dataType: 'integerArray', description: 'integerArrayVal', locale: '' },
            { _id: 70, path: 'decimalVal', dataType: 'decimal', description: 'decimalVal', locale: '' },
            { _id: 80, path: 'booleanVal', dataType: 'boolean', description: 'booleanVal', locale: '' },
            { _id: 90, path: 'jsonVal', dataType: 'json', description: 'jsonVal', locale: '' },
            { _id: 100, path: 'datetimeVal', dataType: 'datetime', description: 'datetimeVal', locale: '' },
            {
                _id: 110,
                path: 'datetimeRangeVal',
                dataType: 'datetimeRange',
                description: 'datetimeRangeVal',
                locale: '',
            },
            {
                _id: 115,
                path: 'dateRangeVal',
                dataType: 'dateRange',
                description: 'dateRangeVal',
                locale: '',
            },
            { _id: 120, path: 'enumVal', dataType: 'enum', description: 'enumVal', locale: '' },
            { _id: 130, path: 'enumArrayVal', dataType: 'enumArray', description: 'enumArrayVal', locale: '' },
            { _id: 140, path: 'transientInput', dataType: 'transientInput', description: 'transientInput', locale: '' },
            { _id: 150, path: '#lines', dataType: 'collection', description: '#lines', locale: '' },
            { _id: 160, path: '*label', dataType: 'string', description: '*label', locale: '' },
            { _id: 170, path: '##sublines', dataType: 'collection', description: '##sublines', locale: '' },
            { _id: 180, path: '*label#1', dataType: 'string', description: '*label#1', locale: '' },
            { _id: 190, path: 'description#1', dataType: 'string', description: 'description#1', locale: '' },
            { _id: 200, path: 'item', dataType: 'reference', description: 'item', locale: '' },
            {
                _id: 210,
                path: '//testContentAddressable',
                dataType: 'reference',
                description: '//testContentAddressable',
                locale: '',
            },
            { _id: 220, path: '*name', dataType: 'string', description: '*name', locale: '' },
            { _id: 230, path: '*description', dataType: 'string', description: '*description', locale: '' },
            { _id: 240, path: '#comments', dataType: 'collection', description: '#comments', locale: '' },
            { _id: 250, path: 'text', dataType: 'string', description: 'text', locale: '' },
            { _id: 260, path: 'name', dataType: 'string', description: 'name', locale: '' },
            { _id: 270, path: 'description#2', dataType: 'string', description: 'description#2', locale: '' },
        ],
    },
};

export function omitSomeTestDocProperties(value: any): any {
    return lodash.omit(value, ['datetimeVal', 'dateRangeVal', 'datetimeRangeVal', 'decimalVal']);
}

export function compareDocuments(actual: any, expected: any): void {
    // 1/ first global check
    assert.deepEqual(omitSomeTestDocProperties(actual), omitSomeTestDocProperties(expected));
    // 2/ check datetime
    assert.equal(actual.datetimeVal.toString(), expected.datetimeVal);
    // 3/ check datetimeRange
    assert.equal(actual.datetimeRangeVal.toString(), expected.datetimeRangeVal);
    // 4/ check dateRangeVal
    assert.equal(actual.dateRangeVal.toString(), expected.dateRangeVal);
    // 5/ check decimal
    assert.equal(actual.decimalVal, expected.decimalVal);
}

export async function resetTenantLocales(locales: NodeCreateData<xtremSystem.nodes.Locale>[]): Promise<void> {
    await Test.withCommittedContext(
        async cx => {
            await cx.deleteMany(xtremSystem.nodes.Locale, {});
            await Context.localizationManager.createTenantLocale(cx, locales[0].id!);
        },
        { locale: locales[0].id },
    );
    // Let's create the tenant'locale:
    await Test.withCommittedContext(
        async cx => {
            for (let i = 1; i < locales.length; i += 1) {
                const newLocale = await cx.create(xtremSystem.nodes.Locale, locales[i]);
                await newLocale.$.save();
            }
        },
        { locale: locales[0].id },
    );
}
