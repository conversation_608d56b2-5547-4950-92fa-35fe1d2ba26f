import { assert } from 'chai';
import * as fs from 'fs';
import { defaultDelimiter } from '../../../lib/functions/_index';
import { stringifyCsv } from '../../../lib/functions/export-utils';

describe('Generating stringified csv from json', () => {
    it('should create a stringified csv file', () => {
        const headers = [
            '*name',
            'description',
            '#lines',
            'label',
            '##sublines',
            'label#1',
            'description#1',
            'item(code,name,ref)',
            '#comments',
            'text',
        ];
        const json = JSON.parse(fs.readFileSync('./test/fixtures/csv/functions/json-to-csv.json', 'utf8'));
        const content = Object.keys(json).map(key => json[key]);
        const text = fs.readFileSync('./test/fixtures/csv/functions/json-to-csv-success.txt', 'utf8');
        const result = stringifyCsv([[...headers, '_error'], content], defaultDelimiter);
        assert.equal(result, text);
    });
});
