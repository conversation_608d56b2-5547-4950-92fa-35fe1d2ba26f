import { Application, asyncArray, ConditionVariable, Dict, PubSub, Test } from '@sage/xtrem-core';
import * as xtremUpload from '@sage/xtrem-upload';
import { assert } from 'chai';
import * as fs from 'fs';
import * as path from 'path';
import { ImportStatus } from '../../../lib/enums/import-status';
import { defaultDelimiter, parseEntireCsvStream } from '../../../lib/functions/_index';
import { ImportExportTemplate, ImportResult } from '../../../lib/nodes/_index';
import { uploadTestFile } from '../../../lib/nodes/utils';
import { TestLocalizedText } from '../../fixtures/lib/nodes';
import { createImportExportTemplate } from '../utils';

interface TestCasePayload {
    contextValue: { replyTopic: string };
    result: 'success' | 'rejected' | 'timeout';
    reason?: string;
}

interface SpyContextOptions {
    upload: boolean;
    import: boolean;
}

interface SpyImportContext {
    options: SpyContextOptions;
    waitCondition?: ConditionVariable;
    abort: () => void;
}

class Conditions {
    uploaded = Test.createConditionVariable('uploaded');

    imported = Test.createConditionVariable('imported');

    notifyAll() {
        this.uploaded.notifyAll();
        this.imported.notifyAll();
    }
}

let conditions: Conditions | null;

function startSpyImport(options: SpyContextOptions): SpyImportContext {
    if (conditions != null) {
        throw new Error('An import spying is already in progress');
    }
    if (!options.upload && !options.import) {
        return { options, abort: () => {} };
    }

    conditions = new Conditions();

    const oldImportCsv = ImportExportTemplate.importCsv;
    const oldProcessUpload = ImportResult.onProcessUpload;

    function spyImportCsv(...args: any[]): Promise<ImportResult> {
        const promise = oldImportCsv.apply(ImportExportTemplate, args) as Promise<ImportResult>;
        return promise.finally(() => {
            ImportExportTemplate.importCsv = oldImportCsv;
            conditions?.imported.notifyAll();
        });
    }

    function spyProcessUpload(...args: any[]): Promise<void> {
        const promise = oldProcessUpload.apply(ImportResult, args) as Promise<void>;
        return promise.finally(() => {
            ImportResult.onProcessUpload = oldProcessUpload;
            conditions?.uploaded.notifyAll();
        });
    }

    let waitCondition: ConditionVariable | undefined;
    if (options.upload) {
        waitCondition = conditions.uploaded;
        ImportResult.onProcessUpload = spyProcessUpload;
    }
    if (options.import) {
        waitCondition = conditions.imported;
        ImportExportTemplate.importCsv = spyImportCsv;
    }

    return {
        options,
        waitCondition,
        abort() {
            ImportExportTemplate.importCsv = oldImportCsv;
            ImportResult.onProcessUpload = oldProcessUpload;
            conditions?.notifyAll();
        },
    };
}

async function waitForImportComplete(spyContext: SpyImportContext, payload: TestCasePayload) {
    if (payload.result !== 'success' && spyContext.options.import) {
        spyContext.abort();
    }
    if (conditions && spyContext.waitCondition) {
        await spyContext.waitCondition.wait();
    }
    conditions = null;
}

let conditionTestQueuesCleared: ConditionVariable;

Application.emitter.on('testQueuesCleared', () => {
    if (conditionTestQueuesCleared) conditionTestQueuesCleared.notifyAll();
});

describe('Async import', () => {
    const testCases: Dict<{ payload: TestCasePayload }> = {
        './test/fixtures/csv/localized-string/import-insert-scenario-1.csv': {
            payload: {
                contextValue: { replyTopic: 'UploadedFile/InfrastructureComplete' },
                result: 'success',
            },
        },
        './test/fixtures/csv/localized-string/import-update-scenario-1.csv': {
            payload: {
                contextValue: { replyTopic: 'UploadedFile/InfrastructureComplete' },
                result: 'success',
            },
        },
        './test/fixtures/csv/localized-string/import-insert-scenario-2.csv': {
            payload: {
                contextValue: { replyTopic: 'UploadedFile/InfrastructureComplete' },
                result: 'success',
            },
        },
        './test/fixtures/csv/localized-string/import-update-scenario-2.csv': {
            payload: {
                contextValue: { replyTopic: 'UploadedFile/InfrastructureComplete' },
                result: 'success',
            },
        },
        './test/fixtures/csv/localized-string/import-insert-scenario-3.csv': {
            payload: {
                contextValue: { replyTopic: 'UploadedFile/InfrastructureComplete' },
                result: 'success',
            },
        },
        './test/fixtures/csv/localized-string/import-update-scenario-3.csv': {
            payload: {
                contextValue: { replyTopic: 'UploadedFile/InfrastructureComplete' },
                result: 'success',
            },
        },
        './test/fixtures/csv/localized-string/import-insert-scenario-4.csv': {
            payload: {
                contextValue: { replyTopic: 'UploadedFile/InfrastructureComplete' },
                result: 'success',
            },
        },
        './test/fixtures/csv/localized-string/import-update-scenario-4.csv': {
            payload: {
                contextValue: { replyTopic: 'UploadedFile/InfrastructureComplete' },
                result: 'success',
            },
        },
        './test/fixtures/csv/localized-string/import-insert-scenario-5.csv': {
            payload: {
                contextValue: { replyTopic: 'UploadedFile/InfrastructureComplete' },
                result: 'success',
            },
        },
        './test/fixtures/csv/localized-string/import-update-scenario-5.csv': {
            payload: {
                contextValue: { replyTopic: 'UploadedFile/InfrastructureComplete' },
                result: 'success',
            },
        },
        './test/fixtures/csv/localized-string/import-insert-scenario-6.csv': {
            payload: {
                contextValue: { replyTopic: 'UploadedFile/InfrastructureComplete' },
                result: 'timeout',
            },
        },
        './test/fixtures/csv/localized-string/import-update-scenario-6.csv': {
            payload: {
                contextValue: { replyTopic: 'UploadedFile/InfrastructureComplete' },
                result: 'rejected',
                reason: 'Virus detected in uploaded file',
            },
        },
    };

    const scenarios = Object.keys(testCases);

    async function cleanUp(): Promise<void> {
        conditionTestQueuesCleared = Test.createConditionVariable('conditionTestQueuesCleared');
        await Test.withCommittedContext(async context => {
            await cleanTables();

            const csvDirectory = path.join(__dirname, `../../../../${context.tenantId}`);
            if (fs.existsSync(csvDirectory))
                fs.rmSync(path.join(__dirname, `../../../../${context.tenantId}`), { recursive: true });

            await PubSub.publish(context, 'clear_test_notification_queues', {
                tenantId: null,
                applicationName: Test.application.name,
            });
        });
        // Wait for the clearing of test notification queue
        await conditionTestQueuesCleared.wait();
    }

    async function cleanTables(): Promise<void> {
        await Test.withCommittedContext(async context => {
            await context.deleteMany(ImportResult, {});
            await context.deleteMany(xtremUpload.nodes.UploadedFile, { kind: 'upload' });
            await context.deleteMany(TestLocalizedText, {});
        });
    }

    async function initTemplate(): Promise<void> {
        await Test.withCommittedContext(context =>
            asyncArray(scenarios).forEach(async scenario => {
                const scenarioId = path.basename(scenario, '.csv');
                const templateTestLocalizedText = {
                    id: scenarioId,
                    name: scenarioId,
                    code: scenarioId,
                    nodeName: 'TestLocalizedText',
                    description: 'description',
                    csvTemplate: {
                        data: [
                            { _id: 0, path: '!name', dataType: 'string', description: '!name', locale: '' },
                            { _id: 10, path: 'description', dataType: 'string', description: '!name', locale: '' },
                            { _id: 20, path: 'description', dataType: 'string', description: '!name', locale: 'en' },
                            { _id: 30, path: 'description', dataType: 'string', description: '!name', locale: 'en-US' },
                            { _id: 40, path: 'description', dataType: 'string', description: '!name', locale: 'en-UK' },
                            { _id: 50, path: 'description', dataType: 'string', description: '!name', locale: 'fr-FR' },
                        ],
                    },
                };

                await createImportExportTemplate(context, templateTestLocalizedText);
            }),
        );
    }

    function triggerBatchImport(
        scenario: string,
        uploadedFileId: number,
        options: { doInsert?: boolean; doUpdate?: boolean },
    ): Promise<ImportResult> {
        return Test.withReadonlyContext(
            context => {
                const scenarioId = path.basename(scenario, '.csv');

                return ImportExportTemplate.batchImport(context, scenarioId, String(uploadedFileId), options);
            },
            { source: 'customMutation' },
        );
    }

    function mockScanResultListener(
        uploadedFileId: number,
        payload: TestCasePayload,
        skipNotify = false,
    ): Promise<void> {
        return Test.withCommittedContext(
            async context => {
                const uploadedFile = await context.read(
                    xtremUpload.nodes.UploadedFile,
                    { _id: uploadedFileId },
                    { forUpdate: true },
                );

                // See https://confluence.sage.com/pages/viewpage.action?spaceKey=XTREEM&title=Async+Context+Module
                if (payload.result !== 'success') {
                    let rejectReason = payload.reason;
                    if (!rejectReason) rejectReason = `Upload rejected: ${payload.result}`;
                    await uploadedFile.$.set({ status: 'rejected', rejectReason });
                } else {
                    await uploadedFile.$.set({ status: 'verified' });
                }
                await uploadedFile.$.save();
                if (!skipNotify)
                    await context.notify('UploadedFile/processUpload', { uploadedFileId: uploadedFile._id });
            },
            { source: 'listener' },
        );
    }

    async function verifyUploadedFileStatus(
        uploadedFileId: number,
        expectedStatus: xtremUpload.enums.UploadStatus,
        uploadUrl: string,
        reason?: string,
    ): Promise<void> {
        await Test.withReadonlyContext(async context => {
            const uploadedFile = await context.read(xtremUpload.nodes.UploadedFile, { _id: uploadedFileId });

            assert.equal(await uploadedFile.status, expectedStatus);
            if (expectedStatus === 'rejected') {
                assert.equal(await uploadedFile.rejectReason, reason);
            }
        });
    }

    function getResultStatus(uploadedFileId: number): Promise<ImportStatus> {
        return Test.withReadonlyContext(async context => {
            const result = (
                await context
                    .query(ImportResult, {
                        filter: { uploadedFile: uploadedFileId },
                    })
                    .toArray()
            )[0];

            assert.isDefined(result);
            return result.status;
        });
    }

    async function verifyTestLocalizedText(name: string, exists: boolean): Promise<void> {
        await Test.withReadonlyContext(async context => {
            const result = await context.tryRead(TestLocalizedText, { name });
            if (!exists) {
                assert.isNull(result);
            } else {
                assert.isDefined(result);
                assert.isNotNull(result);
            }
        });
    }

    before(() => initTemplate());

    scenarios.forEach(scenario => {
        /**
         * Upload file
         * Trigger batch import mutation
         * Mock uploaded file virus scan result listener
         * Process upload
         */
        it(`Can batch import with virus scan result returned after trigger - ${scenario}`, async () => {
            await cleanUp();
            const payload = testCases[scenario].payload;
            const spyContext = startSpyImport({ upload: true, import: payload.result === 'success' });
            const fileStream = fs.createReadStream(scenario, 'utf8');
            const parsedCsv = await parseEntireCsvStream(fileStream, defaultDelimiter);

            const uploadResult = await uploadTestFile(scenario);
            const doInsert = true;
            const doUpdate = scenario.indexOf('-update-') >= 0;

            // Trigger the batch import mutation
            // - the result should only be create with pending status as the uploaded file is not processed
            await triggerBatchImport(scenario, uploadResult.uploadedFileId, { doInsert, doUpdate });
            assert.equal(await getResultStatus(uploadResult.uploadedFileId), 'pending');

            // Mock the listener that updates the UploadedFile instance and notifies on topic 'UploadedFile/processUpload'
            // Verify the expected status based on the mocked infrastructure response
            await mockScanResultListener(uploadResult.uploadedFileId, payload);
            const expectedStatus = payload.result === 'success' ? 'verified' : 'rejected';
            await verifyUploadedFileStatus(
                uploadResult.uploadedFileId,
                expectedStatus,
                uploadResult.uploadUrl,
                payload.reason || `Upload rejected: ${payload.result}`,
            );
            await waitForImportComplete(spyContext, payload);
            const statusAfterWait = await getResultStatus(uploadResult.uploadedFileId);

            // The import is now done, we have to verify the result
            const expectedEndResultStatus = payload.result === 'success' ? 'finished' : 'rejected';
            assert.equal(statusAfterWait, expectedEndResultStatus);
            // Every row in the CSV exists in the databases. if insert was allowed for the scenario and the upload
            // was successful
            await asyncArray(parsedCsv.rows).forEach(async row => {
                const name = row['!name'] as string;
                await verifyTestLocalizedText(name, doInsert && payload.result === 'success');
            });

            // As we clear the test table at the end of the test, we check that the number of rows expected to be processed
            // are the number of rows in the database
            await Test.withReadonlyContext(async context => {
                const results = await context.query(TestLocalizedText, {}).toArray();
                if (doInsert && payload.result === 'success') {
                    assert.equal(results.length, parsedCsv.rows.length);
                } else {
                    assert.equal(results.length, 0);
                }
            });
        });

        /**
         * Upload file
         * Mock uploaded file virus scan result listener
         * Trigger batch import mutation
         * Process upload
         */
        it(`Can batch import with virus scan result returned before trigger - ${scenario}`, async () => {
            await cleanUp();
            const payload = testCases[scenario].payload;
            const spyContext = startSpyImport({ upload: false, import: payload.result === 'success' });
            const fileStream = fs.createReadStream(scenario, 'utf8');
            const parsedCsv = await parseEntireCsvStream(fileStream, defaultDelimiter);
            const uploadResult = await uploadTestFile(scenario);
            const doInsert = true;
            const doUpdate = scenario.indexOf('-update-') >= 0;

            // Mock the listener that updates the UploadedFile instance and notifies on topic 'UploadedFile/processUpload'
            // Verify the expected status based on the mocked infrastructure response. As we are doing this first the 'UploadedFile/processUpload'
            // notify will not initiate the processing of the import as there is not result record as yet.
            await mockScanResultListener(uploadResult.uploadedFileId, payload, true);
            const expectedStatus = payload.result === 'success' ? 'verified' : 'rejected';
            await verifyUploadedFileStatus(
                uploadResult.uploadedFileId,
                expectedStatus,
                uploadResult.uploadUrl,
                payload.reason || `Upload rejected: ${payload.result}`,
            );

            // No rows should exist for the UploadedFile instance in the ImportResult node
            await Test.withReadonlyContext(async context => {
                const results = await context
                    .query(ImportResult, { filter: { uploadedFile: uploadResult.uploadedFileId } })
                    .toArray();

                assert.equal(results.length, 0);
            });

            // Trigger the batch import mutation
            // - the result will be create with pending status, but this time there will be a notify on 'UploadedFile/processUpload' (again)
            //   The listener will pick up the request and process the import or reject
            await triggerBatchImport(scenario, uploadResult.uploadedFileId, { doInsert, doUpdate });

            // Verify the status of the ImportResult record. It will be pending if the upload was verified and move to inProgress once the
            // import begins
            const expectedBatchImportResultStatus = payload.result === 'success' ? 'pending' : 'rejected';
            assert.equal(await getResultStatus(uploadResult.uploadedFileId), expectedBatchImportResultStatus);

            await waitForImportComplete(spyContext, payload);
            const statusAfterWait = await getResultStatus(uploadResult.uploadedFileId);

            // The notification listener has done its work and the processing is complete
            const expectedEndResultStatus = payload.result === 'success' ? 'finished' : 'rejected';
            assert.equal(statusAfterWait, expectedEndResultStatus);

            // Every row in the CSV exists in the databases. if insert was allowed for the scenario and the upload
            // was successful
            await asyncArray(parsedCsv.rows).forEach(async row => {
                const name = row['!name'] as string;
                await verifyTestLocalizedText(name, doInsert && payload.result === 'success');
            });

            // As we clear the test table at the end of the test, we check that the number of rows expected to be processed
            // are the number of rows in the database
            await Test.withReadonlyContext(async context => {
                const results = await context.query(TestLocalizedText, {}).toArray();
                if (doInsert && payload.result === 'success') {
                    assert.equal(results.length, parsedCsv.rows.length);
                } else {
                    assert.equal(results.length, 0);
                }
            });
        });
    });

    after(async () => {
        await cleanUp();
        await Test.withCommittedContext(async context => {
            await asyncArray(scenarios.map(scenario => path.basename(scenario, '.csv'))).forEach(async id => {
                const template = await context.tryRead(ImportExportTemplate, { id }, { forUpdate: true });
                if (template) await template.$.delete();
            });
        });
    });
});
