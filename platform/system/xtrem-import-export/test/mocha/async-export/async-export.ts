import {
    Context,
    CoreHooks,
    CsvTemplateContent,
    Datetime,
    NodeCreateData,
    Paging<PERSON><PERSON><PERSON>,
    PagingOrderBy,
    Test,
} from '@sage/xtrem-core';
import * as xtremUpload from '@sage/xtrem-upload';
import { assert } from 'chai';
import * as fs from 'fs';
import { ImportExportTemplate, ImportResult } from '../../../lib/nodes/_index';
import { uploadTestFile } from '../../../lib/nodes/utils';
import {
    TestDoc,
    TestDocWithNonVitalRef,
    TestItem,
    TestItemRef,
    TestItemWithEncryptedString,
    TestNonVitalRef,
    TestNonVitalSubRef,
    TestNonVitalSubRefNoNaturalKey,
} from '../../fixtures/lib/nodes';
import {
    compareDocuments,
    createImportExportTemplate,
    createTestItem,
    createTestItemRef,
    importExportTemplateTestDoc,
    importExportTemplateTestDocWithNonVitalRef,
} from '../utils';

describe('Test asyncExport', () => {
    async function importTestDoc(
        context: Context,
        csvPath: string,
        importExportTemplate: {
            id: string;
            name: string;
            description: string;
            nodeName: string;
            csvTemplate: { data: CsvTemplateContent[] };
        },
        options?: {
            doUpdate?: boolean;
            maxErrorCount?: number;
            importExportTemplate?: ImportExportTemplate;
        },
    ): Promise<ImportResult> {
        const user = await context.user;
        const doImport = async (template: ImportExportTemplate): Promise<ImportResult> => {
            const stream = fs.createReadStream(csvPath, 'utf8');
            const uploadedFile = await uploadTestFile(csvPath, user?.email);
            const importResult = await ImportExportTemplate.createResult(
                context,
                template,
                true,
                !!options?.doUpdate,
                false,
                options?.maxErrorCount || 1,
                'inProgress',
                String(uploadedFile.uploadedFileId),
            );
            return ImportExportTemplate.importCsv(context, context.batch, stream, importResult);
        };
        let template = options?.importExportTemplate;
        if (options?.doUpdate && !template) {
            template = await context.read(ImportExportTemplate, { id: importExportTemplate.id });
            return doImport(template);
        }
        if (!template) {
            await context.runInWritableContext(async ctx => {
                template = await _createImportExportTemplate(ctx, importExportTemplate);
                await _createItems(ctx);
                if ((await template.id) === 'templateTestDocWithNonVitalRef') {
                    await _createItemForNonVitalRefImport(ctx);
                }
            });
        }
        template = await context.read(ImportExportTemplate, { id: importExportTemplate.id });
        return doImport(template);
    }

    async function exportTestDoc(
        context: Context,
        template: ImportExportTemplate,
        filter: string,
        orderBy: string,
    ): Promise<void> {
        const factory = context.application.getFactoryByName(await template.nodeName);

        const filterParsed = await PagingFilter.parseFilter(context, factory, filter);
        const orderByParsed = await PagingOrderBy.parseOrderBy(context, factory, orderBy);
        await ImportExportTemplate.generateExportWithQueryReader(context, template, {
            filter: filterParsed,
            orderBy: orderByParsed,
        });
    }

    const _createImportExportTemplate = async (
        ctx: Context,
        importExportTemplate: {
            id: string;
            name: string;
            description: string;
            nodeName: string;
            csvTemplate: { data: CsvTemplateContent[] };
        },
    ) => {
        const template = await createImportExportTemplate(ctx, importExportTemplate);
        assert.deepEqual(template.$.context.diagnoses, []);
        return template;
    };

    const _createItems = async (ctx: Context) => {
        const itemRefData = { refCode: 'COD3_REF', refName: 'NAM1_REF' };
        const itemRef = await createTestItemRef(ctx, itemRefData);

        assert.deepEqual(itemRef.$.context.diagnoses, []);

        const itemData = { code: 'COD3', name: 'NAM1', ref: itemRef._id };
        const item = await createTestItem(ctx, itemData);
        await ctx.read(TestItem, itemData);

        assert.deepEqual(item.$.context.diagnoses, []);
    };

    const _createItemForNonVitalRefImport = async (ctx: Context) => {
        const testNonVitalSubRefData = JSON.parse(
            fs.readFileSync('./test/fixtures/create-data/test-non-vital-sub-ref-create-data.json', 'utf8'),
        );
        const testNonVitalSubRef = await ctx.create(TestNonVitalSubRef, testNonVitalSubRefData);
        await testNonVitalSubRef.$.save();

        await ctx.read(TestNonVitalSubRef, { _id: testNonVitalSubRef._id });
        assert.deepEqual(testNonVitalSubRef.$.context.diagnoses, []);
        assert.equal(await testNonVitalSubRef.subRefName, testNonVitalSubRefData.subRefName);

        let testNonVitalSubRefNoNaturalKey = await ctx.tryRead(TestNonVitalSubRefNoNaturalKey, { _id: 1 });
        if (!testNonVitalSubRefNoNaturalKey?._id) {
            const testNonVitalSubRefNoNaturalKeyData = JSON.parse(
                fs.readFileSync(
                    './test/fixtures/create-data/test-non-vital-sub-ref-no-natural-key-create-data.json',
                    'utf8',
                ),
            );
            testNonVitalSubRefNoNaturalKey = await ctx.create(
                TestNonVitalSubRefNoNaturalKey,
                testNonVitalSubRefNoNaturalKeyData,
            );
            await testNonVitalSubRefNoNaturalKey.$.save();

            await ctx.read(TestNonVitalSubRef, { _id: testNonVitalSubRef._id });
            assert.deepEqual(testNonVitalSubRefNoNaturalKey.$.context.diagnoses, []);
            assert.equal(
                await testNonVitalSubRefNoNaturalKey.subRefName,
                testNonVitalSubRefNoNaturalKeyData.subRefName,
            );
        }
        const testNonVitalRefData = JSON.parse(
            fs.readFileSync('./test/fixtures/create-data/test-non-vital-ref-create-data.json', 'utf8'),
        );
        // Adding the sub-ref without natural key
        testNonVitalRefData.subRefNoNaturalKey = { _id: testNonVitalSubRefNoNaturalKey._id };
        const testNonVitalRef = await ctx.create(TestNonVitalRef, testNonVitalRefData);
        await testNonVitalRef.$.save();

        await ctx.read(TestNonVitalRef, { _id: testNonVitalRef._id });
        assert.deepEqual(testNonVitalRef.$.context.diagnoses, []);
        assert.equal(await testNonVitalRef.name, testNonVitalRefData.name);
        assert.equal(
            await (
                await testNonVitalRef.subRefNoNaturalKey
            )?.subRefName,
            await testNonVitalSubRefNoNaturalKey.subRefName,
        );
        assert.equal(await (await testNonVitalRef.subRef)?.subRefName, await testNonVitalSubRef.subRefName);
    };

    it('Should import then export successfully', async () => {
        const csvPath = './test/fixtures/csv/functions/success.csv';
        await Test.withReadonlyContext(
            async context => {
                const imported = await importTestDoc(context, csvPath, importExportTemplateTestDoc);
                assert.equal(await imported.numberOfRowsInError, 0);

                // check if TestDoc instance (with name doc 1) is created
                // and  if related TestDocLines, TestDocSublines instances or records are created
                const testDoc1 = await context.read(TestDoc, { name: 'doc 1' });
                const expected = JSON.parse(
                    fs.readFileSync('./test/fixtures/csv/import-export-template/payload-success.json', 'utf8'),
                );

                compareDocuments(await testDoc1.$.payload({ withoutCustomData: true }), expected);

                const template = await context.read(ImportExportTemplate, { id: importExportTemplateTestDoc.id });
                await exportTestDoc(context, template, '{name:{_regex:"doc",_options:"i"},_id:{_nin:[]}}', '{name:1}');
                const uploadedFile = (
                    await context
                        .query(xtremUpload.nodes.UploadedFile, {
                            filter: { kind: 'upload' },
                            orderBy: { _id: -1 },
                            first: 1,
                        })
                        .toArray()
                )[0];
                const uploadUrl = await uploadedFile.uploadUrl;
                assert.notEqual(uploadUrl, '', 'UploadUrl could not be computed');
                const input = fs.readFileSync(csvPath, 'utf8');
                const exported = fs
                    .readFileSync(uploadUrl.replace('tenant-data/', '').replace('uploads/', ''), 'utf8')
                    .replaceAll('\n', '\r\n');
                assert.equal(input, exported);
                await context.runInWritableContext(async ctx => {
                    await ctx.delete(TestDoc, { name: 'doc 1' });
                    await ctx.delete(TestDoc, { name: 'doc 2' });
                    await ctx.delete(TestDoc, { name: 'doc 3' });
                    await ctx.delete(TestDoc, { name: 'doc 4' });
                    await ctx.delete(TestItem, { name: 'NAM1' });
                    await ctx.delete(TestItemRef, { refName: 'NAM1_REF' });
                    await ctx.delete(ImportResult, { importExportTemplate: template._id });
                    await ctx.delete(ImportExportTemplate, { id: 'templateTestDoc' });
                });
            },
            { source: 'listener', testMode: true },
        );
    });

    it('Should set a general error if the user tries to export with the import export template is import only', () =>
        Test.withContext(async context => {
            const importExportTemplate = await context.create(ImportExportTemplate, {
                id: 'templateTestDocImportOnly',
                name: 'templateTestDoc',
                nodeName: 'TestDoc',
                description: '1 description',
                templateUse: 'importOnly',
                csvTemplate: {
                    data: [
                        { _id: 0, path: '!name', dataType: 'string', description: '!name', locale: '' },
                        { _id: 20, path: 'description', dataType: 'string', description: 'description', locale: '' },
                        { _id: 150, path: '#lines', dataType: 'collection', description: '#lines', locale: '' },
                        { _id: 160, path: '!label', dataType: 'string', description: '!label', locale: '' },
                        { _id: 170, path: '##sublines', dataType: 'collection', description: '##sublines', locale: '' },
                        { _id: 180, path: '*label#1', dataType: 'string', description: '*label#1', locale: '' },
                        {
                            _id: 190,
                            path: 'description#1',
                            dataType: 'string',
                            description: 'description#1',
                            locale: '',
                        },
                        { _id: 200, path: 'item', dataType: 'reference', description: 'item', locale: '' },
                        { _id: 210, path: '#comments', dataType: 'collection', description: '#comments', locale: '' },
                        { _id: 220, path: 'text', dataType: 'string', description: 'text', locale: '' },
                    ],
                },
            });
            assert.deepEqual(importExportTemplate.$.context.diagnoses, []);
            await importExportTemplate.$.save();
            await assert.isRejected(
                CoreHooks.importExportManager.executeExport(context, await importExportTemplate.id, '', '{}', '{}'),
                'The templateTestDoc template can be used for import only',
            );
        }));

    it('Should escape formula', () =>
        Test.withReadonlyContext(
            async context => {
                await context.runInWritableContext(async ctx => {
                    const item1 = await ctx.create(TestItemWithEncryptedString, { formula: '=1', name: 'NAM1' });
                    await item1.$.save();

                    const item2 = await ctx.create(TestItemWithEncryptedString, { formula: '@1', name: 'NAM2' });
                    await item2.$.save();
                    const importExportTemplate = await createImportExportTemplate(ctx, {
                        id: 'templateEncryptedString',
                        name: 'templateEncryptedString',
                        nodeName: 'TestItemWithEncryptedString',
                        description: '1 description',
                        csvTemplate: {
                            data: [
                                { _id: 10, path: '!name', dataType: 'string', description: '!name', locale: '' },
                                { _id: 20, path: 'formula', dataType: 'string', description: 'name', locale: '' },
                            ],
                        },
                    });
                    assert.deepEqual(importExportTemplate.$.context.diagnoses, []);
                    await importExportTemplate.$.save();
                });
                const template = await context.read(ImportExportTemplate, { id: 'templateEncryptedString' });
                await exportTestDoc(context, template, '{name:{_regex:"NAM",_options:"i"},_id:{_nin:[]}}', '{name:1}');
                const uploadedFile = (
                    await context
                        .query(xtremUpload.nodes.UploadedFile, {
                            filter: { kind: 'upload' },
                            orderBy: { _id: -1 },
                            first: 1,
                        })
                        .toArray()
                )[0];
                const uploadUrl = await uploadedFile.uploadUrl;
                assert.notEqual(uploadUrl, '', 'UploadUrl could not be computed');
                const expected = `"!name";"formula"
"NAM1";"'=1"
"NAM2";"'@1"
`;
                const exported = fs.readFileSync(uploadUrl.replace('tenant-data/', '').replace('uploads/', ''), 'utf8');
                assert.equal(expected, exported);
                await context.runInWritableContext(async ctx => {
                    await ctx.delete(TestItemWithEncryptedString, { name: 'NAM1' });
                    await ctx.delete(TestItemWithEncryptedString, { name: 'NAM2' });
                    await ctx.delete(ImportExportTemplate, { id: 'templateEncryptedString' });
                });
            },
            { source: 'listener', testMode: true },
        ));
    it('Should import then export successfully with non vital paths', async () => {
        const csvPath = './test/fixtures/csv/functions/success-non-vital-ref.csv';
        await Test.withReadonlyContext(
            async context => {
                const imported = await importTestDoc(context, csvPath, importExportTemplateTestDocWithNonVitalRef);
                assert.equal(await imported.numberOfRowsInError, 0);

                // check if TestDocWithNonVitalRef instance (with name 'doc 1 non vital paths') is created
                // and  if related TestDocLineWithNonVitalRef, TestDocSubLineWithNonVitalRef instances or records are created
                const testDoc1 = await context.read(TestDocWithNonVitalRef, { name: 'doc 1 non vital paths' });
                const expected = JSON.parse(
                    fs.readFileSync(
                        './test/fixtures/csv/import-export-template/payload-success-non-vital-ref.json',
                        'utf8',
                    ),
                );

                compareDocuments(await testDoc1.$.payload({ withoutCustomData: true }), expected);

                const template = await context.read(ImportExportTemplate, {
                    id: importExportTemplateTestDocWithNonVitalRef.id,
                });
                await exportTestDoc(context, template, '{name:{_regex:"doc",_options:"i"},_id:{_nin:[]}}', '{name:1}');
                const uploadedFile = (
                    await context
                        .query(xtremUpload.nodes.UploadedFile, {
                            filter: { kind: 'upload' },
                            orderBy: { _id: -1 },
                            first: 1,
                        })
                        .toArray()
                )[0];
                const uploadUrl = await uploadedFile.uploadUrl;
                assert.notEqual(uploadUrl, '', 'UploadUrl could not be computed');
                const input = fs.readFileSync(csvPath, 'utf8');
                const exported = fs
                    .readFileSync(uploadUrl.replace('tenant-data/', '').replace('uploads/', ''), 'utf8')
                    .replaceAll('\n', '\r\n');
                await context.runInWritableContext(async ctx => {
                    await ctx.delete(TestDocWithNonVitalRef, { name: 'doc 1 non vital paths' });
                    await ctx.delete(TestNonVitalRef, { _id: '#nonVitalRef1|non vital ref 1|non vital sub-ref 1' });
                    await ctx.delete(TestNonVitalSubRef, { subRefName: 'non vital sub-ref 1' });
                    await ctx.delete(TestNonVitalSubRefNoNaturalKey, {
                        _id: 1,
                    });
                    await ctx.delete(TestItem, { name: 'NAM1' });
                    await ctx.delete(TestItemRef, { refName: 'NAM1_REF' });
                    await ctx.delete(ImportResult, { importExportTemplate: template._id });
                    await ctx.delete(ImportExportTemplate, { _id: template._id });
                });
                assert.equal(exported, input);
            },
            { source: 'listener', testMode: true },
        );
    });
    it('Should import then export successfully a node with vital collection which self-reference itself', async () => {
        const csvPath = './test/fixtures/csv/functions/success-non-vital-ref-self-referencing-collection.csv';
        await Test.withReadonlyContext(
            async context => {
                const imported = await importTestDoc(context, csvPath, importExportTemplateTestDocWithNonVitalRef);
                assert.equal(await imported.numberOfRowsInError, 0);

                // check if TestDocWithNonVitalRef instance (with name 'doc 1 non vital paths') is created
                // and  if related TestDocLineWithNonVitalRef, TestDocSubLineWithNonVitalRef instances or records are created
                const testDoc1 = await context.read(TestDocWithNonVitalRef, { name: 'doc 1 non vital paths' });
                const expected = JSON.parse(
                    fs.readFileSync(
                        './test/fixtures/csv/import-export-template/payload-success-non-vital-ref-self-referencing-collection.json',
                        'utf8',
                    ),
                );
                assert.isNotNull((await (await testDoc1.lines?.at(1))?.testDocLineWithNonVitalRef)?._id);
                assert.equal(
                    await (
                        await (
                            await testDoc1.lines?.at(1)
                        )?.testDocLineWithNonVitalRef
                    )?.label,
                    'line 1.1',
                );
                assert.equal(
                    (await testDoc1.lines.at(0))?._id,
                    (await (await testDoc1.lines?.at(1))?.testDocLineWithNonVitalRef)?._id,
                );

                const importResult = await testDoc1.$.payload({ withoutCustomData: true });
                compareDocuments(importResult, expected);

                const template = await context.read(ImportExportTemplate, {
                    id: importExportTemplateTestDocWithNonVitalRef.id,
                });
                await exportTestDoc(context, template, '{name:{_regex:"doc",_options:"i"},_id:{_nin:[]}}', '{name:1}');
                const uploadedFile = (
                    await context
                        .query(xtremUpload.nodes.UploadedFile, {
                            filter: { kind: 'upload' },
                            orderBy: { _id: -1 },
                            first: 1,
                        })
                        .toArray()
                )[0];
                const uploadUrl = await uploadedFile.uploadUrl;
                assert.notEqual(uploadUrl, '', 'UploadUrl could not be computed');
                const input = fs.readFileSync(csvPath, 'utf8');
                const exported = fs
                    .readFileSync(uploadUrl.replace('tenant-data/', '').replace('uploads/', ''), 'utf8')
                    .replaceAll('\n', '\r\n');
                await context.runInWritableContext(async ctx => {
                    await ctx.delete(TestDocWithNonVitalRef, { name: 'doc 1 non vital paths' });
                    await ctx.delete(TestNonVitalRef, { _id: '#nonVitalRef1|non vital ref 1|non vital sub-ref 1' });
                    await ctx.delete(TestNonVitalSubRef, { subRefName: 'non vital sub-ref 1' });
                    await ctx.delete(TestNonVitalSubRefNoNaturalKey, {
                        _id: 1,
                    });
                    await ctx.delete(TestItem, { name: 'NAM1' });
                    await ctx.delete(TestItemRef, { refName: 'NAM1_REF' });
                    await ctx.delete(ImportResult, { importExportTemplate: template._id });
                    await ctx.delete(ImportExportTemplate, { _id: template._id });
                });
                assert.equal(exported, input);
            },
            { source: 'listener', testMode: true },
        );
    });
    it('Should import in update mode successfully a node with vital collection which self-reference itself', async () => {
        const payloadPath =
            './test/fixtures/csv/import-export-template/payload-create-non-vital-ref-self-referencing-collection.json';
        const csvPathUpdate =
            './test/fixtures/csv/functions/success-non-vital-ref-self-referencing-collection-update.csv';
        await Test.withReadonlyContext(
            async context => {
                const payload: NodeCreateData<TestDocWithNonVitalRef> = JSON.parse(
                    fs.readFileSync(payloadPath, 'utf8'),
                );
                let template: ImportExportTemplate | null = null;
                await context.runInWritableContext(async ctx => {
                    payload.datetimeVal = Datetime.make(2022, 2, 18, 13, 16, 5);
                    template = await _createImportExportTemplate(ctx, importExportTemplateTestDocWithNonVitalRef);
                    await _createItems(ctx);
                    await _createItemForNonVitalRefImport(ctx);
                    const testDoc = await ctx.create(TestDocWithNonVitalRef, payload);
                    assert.deepEqual(testDoc.$.context.diagnoses, []);
                    await testDoc.$.save();
                    assert.deepEqual(testDoc.$.context.diagnoses, []);
                });
                // Testing import in update mode
                const importedUpdate = await importTestDoc(
                    context,
                    csvPathUpdate,
                    importExportTemplateTestDocWithNonVitalRef,
                    { doUpdate: true },
                );
                assert.equal(await importedUpdate.numberOfRowsInError, 0);

                // check if TestDocWithNonVitalRef instance (with name 'doc 1 non vital paths') is created
                // and  if related TestDocLineWithNonVitalRef, TestDocSubLineWithNonVitalRef instances or records are created
                const testDocUpdate = await context.read(TestDocWithNonVitalRef, { name: 'doc 1 non vital paths' });

                assert.isNotNull((await (await testDocUpdate.lines?.at(1))?.testDocLineWithNonVitalRef)?._id);
                assert.equal(await testDocUpdate.code, 'code1 update');
                assert.equal(await testDocUpdate.description, 'description doc 1 non vital paths update');
                assert.isTrue(!!(await (await testDocUpdate.lines?.at(1))?.testDocLineWithNonVitalRef)?._id);
                assert.equal(
                    (await testDocUpdate.lines.at(0))?._id,
                    (await (await testDocUpdate.lines?.at(1))?.testDocLineWithNonVitalRef)?._id,
                );
                assert.equal(await (await testDocUpdate.lines?.at(0))?.label, 'line 1.1 update');
                assert.equal(await (await testDocUpdate.lines?.at(1))?.label, 'line 1.2 update');

                await context.runInWritableContext(async ctx => {
                    await ctx.delete(TestDocWithNonVitalRef, { name: 'doc 1 non vital paths' });
                    await ctx.delete(TestNonVitalRef, { _id: '#nonVitalRef1|non vital ref 1|non vital sub-ref 1' });
                    await ctx.delete(TestNonVitalSubRef, { subRefName: 'non vital sub-ref 1' });
                    await ctx.delete(TestNonVitalSubRefNoNaturalKey, {
                        _id: 1,
                    });
                    await ctx.delete(TestItem, { name: 'NAM1' });
                    await ctx.delete(TestItemRef, { refName: 'NAM1_REF' });
                    if (template?._id) {
                        await ctx.delete(ImportResult, { importExportTemplate: template._id });
                        await ctx.delete(ImportExportTemplate, { _id: template._id });
                    }
                });
            },
            { source: 'listener', testMode: true },
        );
    });
});

describe('Test asyncExport with user preferences', () => {
    async function importTestDoc(
        context: Context,
        csvPath: string,
        importExportTemplate: {
            id: string;
            name: string;
            description: string;
            nodeName: string;
            csvTemplate: { data: CsvTemplateContent[] };
        },
        options?: {
            doUpdate?: boolean;
            maxErrorCount?: number;
            importExportTemplate?: ImportExportTemplate;
        },
    ): Promise<ImportResult> {
        const user = await context.user;
        const doImport = async (template: ImportExportTemplate): Promise<ImportResult> => {
            const stream = fs.createReadStream(csvPath, 'utf8');
            const uploadedFile = await uploadTestFile(csvPath, user?.email);
            const importResult = await ImportExportTemplate.createResult(
                context,
                template,
                true,
                !!options?.doUpdate,
                false,
                options?.maxErrorCount || 1,
                'inProgress',
                String(uploadedFile.uploadedFileId),
            );
            return ImportExportTemplate.importCsv(context, context.batch, stream, importResult);
        };
        let template = options?.importExportTemplate;
        if (options?.doUpdate && !template) {
            template = await context.read(ImportExportTemplate, { id: importExportTemplate.id });
            return doImport(template);
        }
        if (!template) {
            await context.runInWritableContext(async ctx => {
                template = await _createImportExportTemplate(ctx, importExportTemplate);
                await _createItems(ctx);
                if ((await template.id) === 'templateTestDocWithNonVitalRef') {
                    await _createItemForNonVitalRefImport(ctx);
                }
            });
        }
        template = await context.read(ImportExportTemplate, { id: importExportTemplate.id });
        return doImport(template);
    }

    async function exportTestDoc(
        context: Context,
        template: ImportExportTemplate,
        filter: string,
        orderBy: string,
    ): Promise<void> {
        const factory = context.application.getFactoryByName(await template.nodeName);

        const filterParsed = await PagingFilter.parseFilter(context, factory, filter);
        const orderByParsed = await PagingOrderBy.parseOrderBy(context, factory, orderBy);
        await ImportExportTemplate.generateExportWithQueryReader(context, template, {
            filter: filterParsed,
            orderBy: orderByParsed,
        });
    }

    const _createImportExportTemplate = async (
        ctx: Context,
        importExportTemplate: {
            id: string;
            name: string;
            description: string;
            nodeName: string;
            csvTemplate: { data: CsvTemplateContent[] };
        },
    ) => {
        const template = await createImportExportTemplate(ctx, importExportTemplate);
        assert.deepEqual(template.$.context.diagnoses, []);
        return template;
    };

    const _createItems = async (ctx: Context) => {
        const itemRefData = { refCode: 'COD3_REF', refName: 'NAM1_REF' };
        const itemRef = await createTestItemRef(ctx, itemRefData);

        assert.deepEqual(itemRef.$.context.diagnoses, []);

        const itemData = { code: 'COD3', name: 'NAM1', ref: itemRef._id };
        const item = await createTestItem(ctx, itemData);
        await ctx.read(TestItem, itemData);

        assert.deepEqual(item.$.context.diagnoses, []);
    };

    const _createItemForNonVitalRefImport = async (ctx: Context) => {
        const testNonVitalSubRefData = JSON.parse(
            fs.readFileSync('./test/fixtures/create-data/test-non-vital-sub-ref-create-data.json', 'utf8'),
        );
        const testNonVitalSubRef = await ctx.create(TestNonVitalSubRef, testNonVitalSubRefData);
        await testNonVitalSubRef.$.save();

        await ctx.read(TestNonVitalSubRef, { _id: testNonVitalSubRef._id });
        assert.deepEqual(testNonVitalSubRef.$.context.diagnoses, []);
        assert.equal(await testNonVitalSubRef.subRefName, testNonVitalSubRefData.subRefName);

        let testNonVitalSubRefNoNaturalKey = await ctx.tryRead(TestNonVitalSubRefNoNaturalKey, { _id: 1 });
        if (!testNonVitalSubRefNoNaturalKey?._id) {
            const testNonVitalSubRefNoNaturalKeyData = JSON.parse(
                fs.readFileSync(
                    './test/fixtures/create-data/test-non-vital-sub-ref-no-natural-key-create-data.json',
                    'utf8',
                ),
            );
            testNonVitalSubRefNoNaturalKey = await ctx.create(
                TestNonVitalSubRefNoNaturalKey,
                testNonVitalSubRefNoNaturalKeyData,
            );
            await testNonVitalSubRefNoNaturalKey.$.save();

            await ctx.read(TestNonVitalSubRef, { _id: testNonVitalSubRef._id });
            assert.deepEqual(testNonVitalSubRefNoNaturalKey.$.context.diagnoses, []);
            assert.equal(
                await testNonVitalSubRefNoNaturalKey.subRefName,
                testNonVitalSubRefNoNaturalKeyData.subRefName,
            );
        }
        const testNonVitalRefData = JSON.parse(
            fs.readFileSync('./test/fixtures/create-data/test-non-vital-ref-create-data.json', 'utf8'),
        );
        // Adding the sub-ref without natural key
        testNonVitalRefData.subRefNoNaturalKey = { _id: testNonVitalSubRefNoNaturalKey._id };
        const testNonVitalRef = await ctx.create(TestNonVitalRef, testNonVitalRefData);
        await testNonVitalRef.$.save();

        await ctx.read(TestNonVitalRef, { _id: testNonVitalRef._id });
        assert.deepEqual(testNonVitalRef.$.context.diagnoses, []);
        assert.equal(await testNonVitalRef.name, testNonVitalRefData.name);
        assert.equal(
            await (
                await testNonVitalRef.subRefNoNaturalKey
            )?.subRefName,
            await testNonVitalSubRefNoNaturalKey.subRefName,
        );
        assert.equal(await (await testNonVitalRef.subRef)?.subRefName, await testNonVitalSubRef.subRefName);
    };

    it('[comma-usslash] Should import then export successfully', async () => {
        const csvPath = './test/fixtures/csv/user-preferences/comma-usslash/success.csv';
        await Test.withReadonlyContext(
            async context => {
                const imported = await importTestDoc(context, csvPath, importExportTemplateTestDoc);
                assert.equal(await imported.numberOfRowsInError, 0);

                // check if TestDoc instance (with name doc 1) is created
                // and  if related TestDocLines, TestDocSublines instances or records are created
                const testDoc1 = await context.read(TestDoc, { name: 'doc 1' });
                const expected = JSON.parse(
                    fs.readFileSync('./test/fixtures/csv/import-export-template/payload-success.json', 'utf8'),
                );

                compareDocuments(await testDoc1.$.payload({ withoutCustomData: true }), expected);

                const template = await context.read(ImportExportTemplate, { id: importExportTemplateTestDoc.id });
                await exportTestDoc(context, template, '{name:{_regex:"doc",_options:"i"},_id:{_nin:[]}}', '{name:1}');
                const uploadedFile = (
                    await context
                        .query(xtremUpload.nodes.UploadedFile, {
                            filter: { kind: 'upload' },
                            orderBy: { _id: -1 },
                            first: 1,
                        })
                        .toArray()
                )[0];
                const uploadUrl = await uploadedFile.uploadUrl;
                assert.notEqual(uploadUrl, '', 'UploadUrl could not be computed');
                const input = fs.readFileSync(csvPath, 'utf8');
                const exported = fs
                    .readFileSync(uploadUrl.replace('tenant-data/', '').replace('uploads/', ''), 'utf8')
                    .replaceAll('\n', '\r\n');
                assert.equal(input, exported);
                await context.runInWritableContext(async ctx => {
                    await ctx.delete(TestDoc, { name: 'doc 1' });
                    await ctx.delete(TestDoc, { name: 'doc 2' });
                    await ctx.delete(TestDoc, { name: 'doc 3' });
                    await ctx.delete(TestDoc, { name: 'doc 4' });
                    await ctx.delete(TestItem, { name: 'NAM1' });
                    await ctx.delete(TestItemRef, { refName: 'NAM1_REF' });
                    await ctx.delete(ImportResult, { importExportTemplate: template._id });
                    await ctx.delete(ImportExportTemplate, { id: 'templateTestDoc' });
                });
            },
            { source: 'listener', userEmail: '<EMAIL>', testMode: true },
        );
    });

    it('[comma-usslash] Should escape formula', () =>
        Test.withReadonlyContext(
            async context => {
                await context.runInWritableContext(async ctx => {
                    const item1 = await ctx.create(TestItemWithEncryptedString, { formula: '=1', name: 'NAM1' });
                    await item1.$.save();

                    const item2 = await ctx.create(TestItemWithEncryptedString, { formula: '@1', name: 'NAM2' });
                    await item2.$.save();
                    const importExportTemplate = await createImportExportTemplate(ctx, {
                        id: 'templateEncryptedString',
                        name: 'templateEncryptedString',
                        nodeName: 'TestItemWithEncryptedString',
                        description: '1 description',
                        csvTemplate: {
                            data: [
                                { _id: 10, path: '!name', dataType: 'string', description: '!name', locale: '' },
                                { _id: 20, path: 'formula', dataType: 'string', description: 'name', locale: '' },
                            ],
                        },
                    });
                    assert.deepEqual(importExportTemplate.$.context.diagnoses, []);
                    await importExportTemplate.$.save();
                });
                const template = await context.read(ImportExportTemplate, { id: 'templateEncryptedString' });
                await exportTestDoc(context, template, '{name:{_regex:"NAM",_options:"i"},_id:{_nin:[]}}', '{name:1}');
                const uploadedFile = (
                    await context
                        .query(xtremUpload.nodes.UploadedFile, {
                            filter: { kind: 'upload' },
                            orderBy: { _id: -1 },
                            first: 1,
                        })
                        .toArray()
                )[0];
                const uploadUrl = await uploadedFile.uploadUrl;
                assert.notEqual(uploadUrl, '', 'UploadUrl could not be computed');
                const expected = `"!name","formula"
"NAM1","'=1"
"NAM2","'@1"
`;
                const exported = fs.readFileSync(uploadUrl.replace('tenant-data/', '').replace('uploads/', ''), 'utf8');
                assert.equal(expected, exported);
                await context.runInWritableContext(async ctx => {
                    await ctx.delete(TestItemWithEncryptedString, { name: 'NAM1' });
                    await ctx.delete(TestItemWithEncryptedString, { name: 'NAM2' });
                    await ctx.delete(ImportExportTemplate, { id: 'templateEncryptedString' });
                });
            },
            { source: 'listener', userEmail: '<EMAIL>', testMode: true },
        ));
    it('[comma-usslash] Should import then export successfully with non vital paths', async () => {
        const csvPath = './test/fixtures/csv/user-preferences/comma-usslash/success-non-vital-ref.csv';
        await Test.withReadonlyContext(
            async context => {
                const imported = await importTestDoc(context, csvPath, importExportTemplateTestDocWithNonVitalRef);
                assert.equal(await imported.numberOfRowsInError, 0);

                // check if TestDocWithNonVitalRef instance (with name 'doc 1 non vital paths') is created
                // and  if related TestDocLineWithNonVitalRef, TestDocSubLineWithNonVitalRef instances or records are created
                const testDoc1 = await context.read(TestDocWithNonVitalRef, { name: 'doc 1 non vital paths' });
                const expected = JSON.parse(
                    fs.readFileSync(
                        './test/fixtures/csv/import-export-template/payload-success-non-vital-ref.json',
                        'utf8',
                    ),
                );

                compareDocuments(await testDoc1.$.payload({ withoutCustomData: true }), expected);

                const template = await context.read(ImportExportTemplate, {
                    id: importExportTemplateTestDocWithNonVitalRef.id,
                });
                await exportTestDoc(context, template, '{name:{_regex:"doc",_options:"i"},_id:{_nin:[]}}', '{name:1}');
                const uploadedFile = (
                    await context
                        .query(xtremUpload.nodes.UploadedFile, {
                            filter: { kind: 'upload' },
                            orderBy: { _id: -1 },
                            first: 1,
                        })
                        .toArray()
                )[0];
                const uploadUrl = await uploadedFile.uploadUrl;
                assert.notEqual(uploadUrl, '', 'UploadUrl could not be computed');
                const input = fs.readFileSync(csvPath, 'utf8');
                const exported = fs
                    .readFileSync(uploadUrl.replace('tenant-data/', '').replace('uploads/', ''), 'utf8')
                    .replaceAll('\n', '\r\n');
                await context.runInIsolatedContext(async ctx => {
                    await ctx.delete(TestDocWithNonVitalRef, { name: 'doc 1 non vital paths' });
                    await ctx.delete(TestNonVitalRef, { _id: '#nonVitalRef1|non vital ref 1|non vital sub-ref 1' });
                    await ctx.delete(TestNonVitalSubRef, { subRefName: 'non vital sub-ref 1' });
                    await ctx.delete(TestNonVitalSubRefNoNaturalKey, {
                        _id: 1,
                    });
                    await ctx.delete(TestItem, { name: 'NAM1' });
                    await ctx.delete(TestItemRef, { refName: 'NAM1_REF' });
                    await ctx.delete(ImportResult, { importExportTemplate: template._id });
                    await ctx.delete(ImportExportTemplate, { _id: template._id });
                });
                assert.equal(exported, input);
            },
            { source: 'listener', userEmail: '<EMAIL>', testMode: true },
        );
    });
    it('[pipe-europeanDash] Should import then export successfully', async () => {
        const csvPath = './test/fixtures/csv/user-preferences/pipe-europeandash/success.csv';
        await Test.withReadonlyContext(
            async context => {
                const imported = await importTestDoc(context, csvPath, importExportTemplateTestDoc);
                assert.equal(await imported.numberOfRowsInError, 0);

                // check if TestDoc instance (with name doc 1) is created
                // and  if related TestDocLines, TestDocSublines instances or records are created
                const testDoc1 = await context.read(TestDoc, { name: 'doc 1' });
                const expected = JSON.parse(
                    fs.readFileSync('./test/fixtures/csv/import-export-template/payload-success.json', 'utf8'),
                );

                compareDocuments(await testDoc1.$.payload({ withoutCustomData: true }), expected);

                const template = await context.read(ImportExportTemplate, { id: importExportTemplateTestDoc.id });
                await exportTestDoc(context, template, '{name:{_regex:"doc",_options:"i"},_id:{_nin:[]}}', '{name:1}');
                const uploadedFile = (
                    await context
                        .query(xtremUpload.nodes.UploadedFile, {
                            filter: { kind: 'upload' },
                            orderBy: { _id: -1 },
                            first: 1,
                        })
                        .toArray()
                )[0];
                const uploadUrl = await uploadedFile.uploadUrl;
                assert.notEqual(uploadUrl, '', 'UploadUrl could not be computed');
                const input = fs.readFileSync(csvPath, 'utf8');
                const exported = fs
                    .readFileSync(uploadUrl.replace('tenant-data/', '').replace('uploads/', ''), 'utf8')
                    .replaceAll('\n', '\r\n');
                assert.equal(input, exported);
                await context.runInWritableContext(async ctx => {
                    await ctx.delete(TestDoc, { name: 'doc 1' });
                    await ctx.delete(TestDoc, { name: 'doc 2' });
                    await ctx.delete(TestDoc, { name: 'doc 3' });
                    await ctx.delete(TestDoc, { name: 'doc 4' });
                    await ctx.delete(TestItem, { name: 'NAM1' });
                    await ctx.delete(TestItemRef, { refName: 'NAM1_REF' });
                    await ctx.delete(ImportResult, { importExportTemplate: template._id });
                    await ctx.delete(ImportExportTemplate, { id: 'templateTestDoc' });
                });
            },
            { source: 'listener', userEmail: '<EMAIL>', testMode: true },
        );
    });

    it('[pipe-europeanDash] Should escape formula', () =>
        Test.withReadonlyContext(
            async context => {
                await context.runInWritableContext(async ctx => {
                    const item1 = await ctx.create(TestItemWithEncryptedString, { formula: '=1', name: 'NAM1' });
                    await item1.$.save();

                    const item2 = await ctx.create(TestItemWithEncryptedString, { formula: '@1', name: 'NAM2' });
                    await item2.$.save();
                    const importExportTemplate = await createImportExportTemplate(ctx, {
                        id: 'templateEncryptedString',
                        name: 'templateEncryptedString',
                        nodeName: 'TestItemWithEncryptedString',
                        description: '1 description',
                        csvTemplate: {
                            data: [
                                { _id: 10, path: '!name', dataType: 'string', description: '!name', locale: '' },
                                { _id: 20, path: 'formula', dataType: 'string', description: 'name', locale: '' },
                            ],
                        },
                    });
                    assert.deepEqual(importExportTemplate.$.context.diagnoses, []);
                    await importExportTemplate.$.save();
                });
                const template = await context.read(ImportExportTemplate, { id: 'templateEncryptedString' });
                await exportTestDoc(context, template, '{name:{_regex:"NAM",_options:"i"},_id:{_nin:[]}}', '{name:1}');
                const uploadedFile = (
                    await context
                        .query(xtremUpload.nodes.UploadedFile, {
                            filter: { kind: 'upload' },
                            orderBy: { _id: -1 },
                            first: 1,
                        })
                        .toArray()
                )[0];
                const uploadUrl = await uploadedFile.uploadUrl;
                assert.notEqual(uploadUrl, '', 'UploadUrl could not be computed');
                const expected = `"!name"|"formula"
"NAM1"|"'=1"
"NAM2"|"'@1"
`;
                const exported = fs.readFileSync(uploadUrl.replace('tenant-data/', '').replace('uploads/', ''), 'utf8');
                assert.equal(expected, exported);
                await context.runInWritableContext(async ctx => {
                    await ctx.delete(TestItemWithEncryptedString, { name: 'NAM1' });
                    await ctx.delete(TestItemWithEncryptedString, { name: 'NAM2' });
                    await ctx.delete(ImportExportTemplate, { id: 'templateEncryptedString' });
                });
            },
            { source: 'listener', userEmail: '<EMAIL>', testMode: true },
        ));
    it('[pipe-europeanDash] Should import then export successfully with non vital paths', async () => {
        const csvPath = './test/fixtures/csv/user-preferences/pipe-europeandash/success-non-vital-ref.csv';
        await Test.withReadonlyContext(
            async context => {
                const imported = await importTestDoc(context, csvPath, importExportTemplateTestDocWithNonVitalRef);
                assert.equal(await imported.numberOfRowsInError, 0);

                // check if TestDocWithNonVitalRef instance (with name 'doc 1 non vital paths') is created
                // and  if related TestDocLineWithNonVitalRef, TestDocSubLineWithNonVitalRef instances or records are created
                const testDoc1 = await context.read(TestDocWithNonVitalRef, { name: 'doc 1 non vital paths' });
                const expected = JSON.parse(
                    fs.readFileSync(
                        './test/fixtures/csv/import-export-template/payload-success-non-vital-ref.json',
                        'utf8',
                    ),
                );

                compareDocuments(await testDoc1.$.payload({ withoutCustomData: true }), expected);

                const template = await context.read(ImportExportTemplate, {
                    id: importExportTemplateTestDocWithNonVitalRef.id,
                });
                await exportTestDoc(context, template, '{name:{_regex:"doc",_options:"i"},_id:{_nin:[]}}', '{name:1}');
                const uploadedFile = (
                    await context
                        .query(xtremUpload.nodes.UploadedFile, {
                            filter: { kind: 'upload' },
                            orderBy: { _id: -1 },
                            first: 1,
                        })
                        .toArray()
                )[0];
                const uploadUrl = await uploadedFile.uploadUrl;
                assert.notEqual(uploadUrl, '', 'UploadUrl could not be computed');
                const input = fs.readFileSync(csvPath, 'utf8');
                const exported = fs
                    .readFileSync(uploadUrl.replace('tenant-data/', '').replace('uploads/', ''), 'utf8')
                    .replaceAll('\n', '\r\n');
                await context.runInWritableContext(async ctx => {
                    await ctx.delete(TestDocWithNonVitalRef, { name: 'doc 1 non vital paths' });
                    await ctx.delete(TestNonVitalRef, { _id: '#nonVitalRef1|non vital ref 1|non vital sub-ref 1' });
                    await ctx.delete(TestNonVitalSubRef, { subRefName: 'non vital sub-ref 1' });
                    await ctx.delete(TestNonVitalSubRefNoNaturalKey, {
                        _id: 1,
                    });
                    await ctx.delete(TestItem, { name: 'NAM1' });
                    await ctx.delete(TestItemRef, { refName: 'NAM1_REF' });
                    await ctx.delete(ImportResult, { importExportTemplate: template._id });
                    await ctx.delete(ImportExportTemplate, { _id: template._id });
                });
                assert.equal(exported, input);
            },
            { source: 'listener', userEmail: '<EMAIL>', testMode: true },
        );
    });
});
