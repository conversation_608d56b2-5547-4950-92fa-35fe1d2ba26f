import { Application, BatchLogEntry, ConditionVariable, Test, sleepMillis } from '@sage/xtrem-core';
import { assert } from 'chai';
import { FileFormat } from '../../../lib/enums/file-format';

let conditionNotificationProcessed: ConditionVariable;

Application.emitter.on('notificationProcessed', () => {
    if (conditionNotificationProcessed) conditionNotificationProcessed.notifyAll();
});

describe('Async mutation exportByTemplateId', () => {
    const testGlobalExportByIdWithGraphQl = async (
        format: FileFormat,
        expectedPayload: { status: string; result: string | null; errorMessage: string | null },
    ) => {
        conditionNotificationProcessed = Test.createConditionVariable('conditionNotificationProcessed');

        const submitted = await Test.withReadonlyContext(
            context =>
                Test.graphql<{
                    global: { exportByTemplateId: { start: { trackingId: string } } };
                }>(
                    context,
                    `mutation { global { exportByTemplateId {  start(templateId: "User", outputFormat: "${format}", filter: "{}") { trackingId } } } }`,
                ),
            { userEmail: '<EMAIL>' },
        );
        assert.isObject(submitted);
        const trackingId = submitted.data.global.exportByTemplateId.start.trackingId;
        assert.isString(trackingId);

        await conditionNotificationProcessed.wait();

        for (let i = 0; i < 20; i += 1) {
            const polled = await Test.withReadonlyContext(
                context =>
                    Test.graphql<{
                        global: {
                            exportByTemplateId: {
                                track: { status: string; result: string | null; errorMessage: string | null };
                            };
                        };
                    }>(
                        context,
                        `query { global { exportByTemplateId { track(trackingId: "${trackingId}") { status, result, errorMessage } } } }`,
                    ),
                { userEmail: '<EMAIL>' },
            );
            assert.isObject(polled);

            const tracker = polled.data.global.exportByTemplateId.track;
            if (tracker.status === 'pending' || tracker.status === 'running') {
                await sleepMillis(10);
            } else {
                assert.deepEqual(tracker, expectedPayload);
                return;
            }
        }
        assert.fail('polling failed');
    };

    const testExportByIdWithGraphQl = async (
        format: FileFormat,
        expectedPayload: { status: string; result: string | null; errorMessage: string | null },
    ) => {
        conditionNotificationProcessed = Test.createConditionVariable('conditionNotificationProcessed');

        const submitted = await Test.withReadonlyContext(
            context =>
                Test.graphql<{
                    xtremImportExport: {
                        importExportTemplate: { exportByTemplateId: { start: { trackingId: string } } };
                    };
                }>(
                    context,
                    `mutation { xtremImportExport { importExportTemplate { exportByTemplateId {  start(templateId: "User", outputFormat: "${format}", filter: "{}") { trackingId } } } } }`,
                ),
            { userEmail: '<EMAIL>' },
        );
        assert.isObject(submitted);
        const trackingId = submitted.data.xtremImportExport.importExportTemplate.exportByTemplateId.start.trackingId;
        assert.isString(trackingId);

        await conditionNotificationProcessed.wait();

        for (let i = 0; i < 20; i += 1) {
            const polled = await Test.withReadonlyContext(
                context =>
                    Test.graphql<{
                        xtremImportExport: {
                            importExportTemplate: {
                                exportByTemplateId: {
                                    track: { status: string; result: string | null; errorMessage: string | null };
                                };
                            };
                        };
                    }>(
                        context,
                        `query { xtremImportExport{ importExportTemplate { exportByTemplateId { track(trackingId: "${trackingId}") { status, result, errorMessage } } } } }`,
                    ),
                { userEmail: '<EMAIL>' },
            );
            assert.isObject(polled);

            const tracker = polled.data.xtremImportExport.importExportTemplate.exportByTemplateId.track;
            if (tracker.status === 'pending' || tracker.status === 'running') {
                await sleepMillis(10);
            } else {
                assert.deepEqual(tracker, expectedPayload);
                return;
            }
        }
        assert.fail('polling failed');
    };

    it('can execute exportByTemplateId async mutation with global graphql namespace (success)', async () => {
        await testGlobalExportByIdWithGraphQl('csv', {
            status: 'success',
            result: 'Finished',
            errorMessage: null,
        });
    });

    it('can execute exportByTemplateId async mutation with global graphql namespace with XLSX output type (success)', async () => {
        await testGlobalExportByIdWithGraphQl('xlsx', {
            status: 'success',
            result: 'Finished',
            errorMessage: null,
        });
    });

    it('can execute exportByTemplateId async mutation with standard graphql namespace (success)', async () => {
        await testExportByIdWithGraphQl('csv', {
            status: 'success',
            result: 'Finished',
            errorMessage: null,
        });
    });

    it('can execute exportByTemplateId async mutation with standard graphql namespace (success) with XLSX output type', async () => {
        await testExportByIdWithGraphQl('xlsx', {
            status: 'success',
            result: 'Finished',
            errorMessage: null,
        });
    });
});

describe('Async mutation exportByTemplateDefinition', () => {
    const testGlobalExportByDefinitionWithGraphQl = async (
        format: FileFormat,
        nodeName: string,
        expectedPayload: { status: string; result: string | null; errorMessage: string | null },
    ) => {
        conditionNotificationProcessed = Test.createConditionVariable('conditionNotificationProcessed');

        const submitted = await Test.withReadonlyContext(
            context =>
                Test.graphql<{
                    global: { exportByTemplateDefinition: { start: { trackingId: string } } };
                }>(
                    context,
                    `mutation {
                        global {
                          exportByTemplateDefinition {
                            start(
                              templateDefinition: [
                                {path: "email", title: "Email"},
                                {path: "firstName", title: "First Name"},
                                {path: "lastName", title: "Last Name"},
                                {path: "userType", title: "User type"},
                                {path: "isActive", title: "Is active (false/true)"},
                                {path: "navigation.history", title: "Navigation History"},
                                {path: "navigation.bookmarks", title: "Navigation Bookmarks"}]
                              nodeName: "${nodeName}"
                              outputFormat: "${format}"
                              filter: "{email:{_regex:'ab',_options:'i'},_id:{_nin:[]}}"
                            ) {
                              trackingId
                            }
                          }
                        }
                      }`,
                ),
            { userEmail: '<EMAIL>' },
        );
        assert.isObject(submitted);
        const trackingId = submitted.data.global.exportByTemplateDefinition.start.trackingId;
        assert.isString(trackingId);

        await conditionNotificationProcessed.wait();

        for (let i = 0; i < 20; i += 1) {
            const polled = await Test.withReadonlyContext(
                context =>
                    Test.graphql<{
                        global: {
                            exportByTemplateDefinition: {
                                track: { status: string; result: string | null; errorMessage: string | null };
                            };
                        };
                    }>(
                        context,
                        `query { global { exportByTemplateDefinition { track(trackingId: "${trackingId}") { status, result, errorMessage } } } }`,
                    ),
                { userEmail: '<EMAIL>' },
            );
            assert.isObject(polled);

            const tracker = polled.data.global.exportByTemplateDefinition.track;
            if (tracker.status === 'pending' || tracker.status === 'running') {
                await sleepMillis(10);
            } else {
                assert.deepEqual(tracker, expectedPayload);
                return;
            }
        }
        assert.fail('polling failed');
    };

    const testExportByDefinitionWithGraphQl = async (
        format: FileFormat,
        nodeName: string,
        expectedPayload: {
            status: string;
            result: string | null;
            errorMessage: string | null;
            logMessages: BatchLogEntry[];
        },
    ) => {
        conditionNotificationProcessed = Test.createConditionVariable('conditionNotificationProcessed');

        const submitted = await Test.withReadonlyContext(
            context =>
                Test.graphql<{
                    xtremImportExport: {
                        importExportTemplate: { exportByTemplateDefinition: { start: { trackingId: string } } };
                    };
                }>(
                    context,
                    `mutation {
                        xtremImportExport {
                          importExportTemplate {
                            exportByTemplateDefinition {
                              start(
                                templateDefinition:[{path: "id", title:"ID"},
                                {path: "name", title:"Name"},
                                {path: "description", title:"Desciption" },
                                {path: "isActive",title:"Active"},
                                {path: "legalCompany",title:"Legal Company"},
                                {path: "legalCompany.name",  title:"legal Company name"},
                                {path: "legalCompany.siren", title:"legal Company Siren"}]
                                nodeName: "${nodeName}"
                                outputFormat: "${format}"
                                filter: "{id:{_regex:'US',_options:'i'},_id:{_nin:[]}}"
                              ) {
                                trackingId
                              }
                            }
                          }
                        }
                      }`,
                ),
            { userEmail: '<EMAIL>' },
        );
        assert.isObject(submitted);
        if (submitted.data.xtremImportExport.importExportTemplate.exportByTemplateDefinition.start === null) {
            assert.deepEqual((submitted as any).errors[0].message, expectedPayload.errorMessage);
        } else {
            const trackingId =
                submitted.data.xtremImportExport.importExportTemplate.exportByTemplateDefinition.start.trackingId;
            assert.isString(trackingId);

            await conditionNotificationProcessed.wait();

            for (let i = 0; i < 20; i += 1) {
                const polled = await Test.withReadonlyContext(
                    context =>
                        Test.graphql<{
                            xtremImportExport: {
                                importExportTemplate: {
                                    exportByTemplateDefinition: {
                                        track: {
                                            status: string;
                                            result: string | null;
                                            errorMessage: string | null;
                                            logMessages: BatchLogEntry[] | null;
                                        };
                                    };
                                };
                            };
                        }>(
                            context,
                            `query { xtremImportExport{ importExportTemplate { exportByTemplateDefinition { track(trackingId: "${trackingId}") {
                                status, result, errorMessage, logMessages { level, message }
                            } } } } }`,
                        ),
                    { userEmail: '<EMAIL>' },
                );
                assert.isObject(polled);

                const tracker = polled.data.xtremImportExport.importExportTemplate.exportByTemplateDefinition.track;
                tracker.logMessages =
                    tracker.logMessages?.filter(
                        (log: BatchLogEntry) => log.level === 'error' || log.level === 'exception',
                    ) ?? null;
                if (tracker.status === 'pending' || tracker.status === 'running') {
                    await sleepMillis(10);
                } else {
                    assert.deepEqual(tracker, expectedPayload);
                    return;
                }
            }
            assert.fail('polling failed');
        }
    };

    it('can execute exportByTemplateDefinition async mutation with global graphql namespace (success)', async () => {
        await testGlobalExportByDefinitionWithGraphQl('csv', 'User', {
            status: 'success',
            result: 'Finished',
            errorMessage: null,
        });
    });

    it('can execute exportByTemplateDefinition async mutation with global graphql namespace with XLSX output type (success)', async () => {
        await testGlobalExportByDefinitionWithGraphQl('xlsx', 'User', {
            status: 'success',
            result: 'Finished',
            errorMessage: null,
        });
    });

    it('can execute exportByTemplateDefinition async mutation with standard graphql namespace (success)', async () => {
        await testExportByDefinitionWithGraphQl('csv', 'Site', {
            status: 'success',
            result: 'Finished',
            errorMessage: null,
            logMessages: [],
        });
    });

    it('can execute exportByTemplateDefinition async mutation with standard graphql namespace with XLSX output type (success)', async () => {
        await testExportByDefinitionWithGraphQl('xlsx', 'Site', {
            status: 'success',
            result: 'Finished',
            errorMessage: null,
            logMessages: [],
        });
    });

    it('can execute exportByTemplateDefinition async mutation with standard graphql namespace (error on node name)', async () => {
        await testExportByDefinitionWithGraphQl('csv', 'SalesOrder', {
            status: 'error',
            result: null,
            // TODO: error message should be asyncMutation, not mutation
            errorMessage: 'Export by template definition failed.',
            logMessages: [
                {
                    level: 'error',
                    message: "Factory 'SalesOrder' could not be found.",
                },
            ],
        });
    });
    it('can execute exportByTemplateDefinition async mutation with standard graphql namespace (error on path name)', async () => {
        await testExportByDefinitionWithGraphQl('csv', 'Company', {
            status: 'error',
            result: null,
            errorMessage: 'Export by template definition failed.',
            logMessages: [
                {
                    level: 'exception',
                    message: "Invalid path 'name' for Company node",
                },
            ],
        });
    });
});
