import * as xtremAuthorization from '@sage/xtrem-authorization';
import { asyncArray, Context, CsvTemplateContent, Dict, NodeCreateData, PagingFilter, Test } from '@sage/xtrem-core';
import * as xtremCustomization from '@sage/xtrem-customization';
import { date } from '@sage/xtrem-date-time';
import * as xtremMetaData from '@sage/xtrem-metadata';
import * as xtremSystem from '@sage/xtrem-system';
import { serviceOptions } from '@sage/xtrem-system';
import * as xtremUpload from '@sage/xtrem-upload';
import { assert } from 'chai';
import * as fs from 'fs';
import * as lodash from 'lodash';
import * as path from 'path';
import { Readable } from 'stream';
import { defaultDelimiter } from '../../../lib/functions/common';
import { ImportExportTemplate, ImportResult } from '../../../lib/nodes/_index';
import { uploadTestFile } from '../../../lib/nodes/utils';
import {
    TestActivity,
    TestCustomField,
    TestDoc,
    TestDocRefDefaultValue,
    TestDocVitalRef,
    TestDocWithFrozenProperties,
    TestDocWithNonVitalRef,
    TestItem,
    TestLocalizedText,
    TestLocalizedTextNotEmpty,
    TestTextStream,
} from '../../fixtures/lib/nodes';
import { TestServiceOption } from '../../fixtures/lib/nodes/test-service-option';
import {
    compareDocuments,
    createImportExportTemplate,
    createTestItem,
    createTestItemRef,
    importExportTemplateTestDoc,
    importExportTemplateTestDocComment,
    importExportTemplateTestDocRefDefaultValue,
    importExportTemplateTestDocVitalRef,
    importExportTemplateTestDocWithFrozenProperties,
    importExportTemplateTestDocWithoutMandatoryFields,
    importExportTemplateTestDocWithSortValue,
    importExportTemplateTestTextStream,
    importExportTemplateTestVitalRefChild,
    resetTenantLocales,
    templateTestAccessAuthorized,
    templateTestCustomField,
    templateTestDoc,
    templateTestServiceOptionActive,
    templateTestServiceOptionInactive,
} from '../utils';

describe('Create ImportExportTemplate instance', () => {
    function checkImportHtmlTextStream(
        context: Context,
        expectedNodes: NodeCreateData<TestTextStream>[],
    ): Promise<void> {
        return asyncArray(expectedNodes).forEach(async expected => {
            const node = await context.tryRead(TestTextStream, { name: expected.name });
            if (node) {
                const actual = await node.$.payload({ withoutCustomData: true });
                assert.strictEqual(actual.name, expected.name);
                assert.strictEqual(actual.html?.value, expected.html?.value);
            } else {
                assert.isNull(expected.html);
            }
        });
    }

    async function importTestDoc(
        context: Context,
        csvPath: string,
        importExportTemplate: {
            id: string;
            name: string;
            description: string;
            nodeName: string;
            csvTemplate: { data: CsvTemplateContent[] };
        },
        options?: {
            // doInsert?: boolean;
            doUpdate?: boolean;
            maxErrorCount?: number;
            importExportTemplate?: ImportExportTemplate;
        },
    ): Promise<ImportResult> {
        const user = await context.user;
        const doImport = async (template: ImportExportTemplate): Promise<ImportResult> => {
            const stream = fs.createReadStream(csvPath, 'utf8');
            const uploadedFile = await uploadTestFile(csvPath, user?.email);
            const importResult = await ImportExportTemplate.createResult(
                context,
                template,
                true,
                !!options?.doUpdate,
                false,
                options?.maxErrorCount || 1,
                'inProgress',
                String(uploadedFile.uploadedFileId),
            );
            return ImportExportTemplate.importCsv(context, context.batch, stream, importResult);
        };
        let template = options?.importExportTemplate;
        if (options?.doUpdate && !template) {
            template = await context.read(ImportExportTemplate, { id: importExportTemplate.id });
            return doImport(template);
        }
        if (!template) {
            template = await createImportExportTemplate(context, importExportTemplate);
            assert.deepEqual(template.$.context.diagnoses, []);

            const itemRefData = { refCode: 'COD3_REF', refName: 'NAM1_REF' };
            const itemRef = await createTestItemRef(context, itemRefData);

            const itemData = { code: 'COD3', name: 'NAM1', ref: itemRef._id };
            const item = await createTestItem(context, itemData);
            await context.read(TestItem, itemData);

            assert.deepEqual(item.$.context.diagnoses, []);

            const itemRefData2 = { refCode: 'COD2_REF', refName: 'NAM2_REF' };
            const itemRef2 = await createTestItemRef(context, itemRefData2);

            const itemData2 = { code: 'COD2', name: 'NAM2', ref: itemRef2._id };
            const item2 = await createTestItem(context, itemData2);
            await context.read(TestItem, itemData2);

            assert.deepEqual(item2.$.context.diagnoses, []);
        }
        return doImport(template);
    }

    it('Should create an instance of ImportExportTemplate success', async () => {
        const csvPath = './test/fixtures/csv/functions/success.csv';
        await Test.withUncommittedContext(async context => {
            const imported = await importTestDoc(context, csvPath, importExportTemplateTestDoc);
            assert.equal(await imported.numberOfRowsInError, 0);

            // check if TestDoc instance (with name doc 1) is created
            // and  if related TestDocLines, TestDocSublines instances or records are created
            const testDoc1 = await context.read(TestDoc, { name: 'doc 1' });
            const expected = JSON.parse(
                fs.readFileSync('./test/fixtures/csv/import-export-template/payload-success.json', 'utf8'),
            );
            compareDocuments(await testDoc1.$.payload({ withoutCustomData: true }), expected);
        });
    });

    it('Should create an instance of ImportExportTemplate success-without-description', async () => {
        const csvPath = './test/fixtures/csv/functions/success-without-description.csv';
        await Test.withContext(async context => {
            const imported = await importTestDoc(context, csvPath, importExportTemplateTestDocWithoutMandatoryFields);
            assert.equal(await imported.numberOfRowsInError, 0);

            // check if TestDoc instance (with name doc 1) is created
            // and  if related TestDocLines, TestDocSublines instances or records are created
            const testDoc1 = await context.read(TestDoc, { name: 'doc 1' });
            const expected = JSON.parse(
                fs.readFileSync(
                    './test/fixtures/csv/import-export-template/payload-success-with-default-description.json',
                    'utf8',
                ),
            );
            compareDocuments(await testDoc1.$.payload({ withoutCustomData: true }), expected);
        });
    });

    it('Should create an instance of ImportExportTemplate success-with-empty-hash-column', async () => {
        const csvPath = './test/fixtures/csv/functions/success-with-empty-hash-column.csv';
        await Test.withContext(async context => {
            const imported = await importTestDoc(context, csvPath, importExportTemplateTestDocWithoutMandatoryFields);
            assert.equal(await imported.numberOfRowsInError, 0);

            // check if TestDoc instance (with name doc 1) is created
            // and  if related TestDocLines, TestDocSublines instances or records are created
            const testDoc1 = await context.read(TestDoc, { name: 'doc 1' });
            const expected = JSON.parse(
                fs.readFileSync('./test/fixtures/csv/import-export-template/payload-success.json', 'utf8'),
            );
            compareDocuments(await testDoc1.$.payload({ withoutCustomData: true }), expected);
        });
    });

    it('Should default empty field', async () => {
        const csvPath = './test/fixtures/csv/functions/success-with-empty-field.csv';
        await Test.withContext(async context => {
            await importTestDoc(context, csvPath, importExportTemplateTestDoc);

            // check if TestDoc instance (with name doc 1) is created
            // and  if related TestDocLines, TestDocSublines instances or records are created and empty fields defaulted
            const testDoc1 = await context.read(TestDoc, { name: 'doc 1' });
            const expected = JSON.parse(
                fs.readFileSync(
                    './test/fixtures/csv/import-export-template/payload-success-with-empty-field.json',
                    'utf8',
                ),
            );
            compareDocuments(await testDoc1.$.payload({ withoutCustomData: true }), expected);
        });
    });

    it('Should parse json value', async () => {
        const csvPath = './test/fixtures/csv/functions/success.csv';
        await Test.withContext(async context => {
            await importTestDoc(context, csvPath, importExportTemplateTestDoc);

            // check if TestDoc instance (with name doc 1) is created
            // and  if related TestDocLines, TestDocSublines instances or records are created
            const testDoc1 = await context.read(TestDoc, { name: 'doc 1' });
            const expected = JSON.parse(
                fs.readFileSync('./test/fixtures/csv/import-export-template/payload-success.json', 'utf8'),
            );
            compareDocuments(await testDoc1.$.payload({ withoutCustomData: true }), expected);
        });
    });

    it('Create template with isDefault true', async () => {
        await Test.withContext(async context => {
            let template1 = await createImportExportTemplate(context, {
                id: 'testItem1',
                name: 'testItem',
                nodeName: 'TestItem',
                description: 'Test Item',
                isDefault: true,
                csvTemplate: {
                    data: [
                        { _id: 0, path: '!name', dataType: 'string', description: '!name', locale: '' },
                        { _id: 10, path: '*code', dataType: 'string', description: '*code', locale: '' },
                        {
                            _id: 20,
                            path: '!ref',
                            dataType: 'string',
                            description: '!ref',
                            locale: '',
                        },
                    ],
                },
            });

            assert.deepEqual(template1.$.context.diagnoses, []);
            assert.equal(await template1.isDefault, true);
            assert.equal(await template1.templateUse, 'importAndExport');

            let template2 = await createImportExportTemplate(context, {
                id: 'testItem2',
                name: 'testItem',
                nodeName: 'TestItem',
                description: 'Test Item',
                isDefault: true,
                csvTemplate: {
                    data: [
                        { _id: 0, path: '!name', dataType: 'string', description: '!name', locale: '' },
                        { _id: 10, path: '*code', dataType: 'string', description: '*code', locale: '' },
                        {
                            _id: 20,
                            path: '!ref',
                            dataType: 'string',
                            description: '!ref',
                            locale: '',
                        },
                    ],
                },
            });

            assert.deepEqual(template2.$.context.diagnoses, []);
            assert.equal(await template2.isDefault, true);
            assert.equal(await template2.templateUse, 'importAndExport');

            await Test.rollbackCache(context);
            template1 = await context.read(ImportExportTemplate, { id: 'testItem1' }, { forUpdate: true });
            assert.equal(await template1.isDefault, false);

            const template3 = await createImportExportTemplate(context, {
                id: 'testItem3',
                name: 'testItem',
                nodeName: 'TestItem',
                description: 'Test Item',
                isDefault: true,
                templateUse: 'importOnly',
                csvTemplate: {
                    data: [
                        { _id: 0, path: '!name', dataType: 'string', description: '!name', locale: '' },
                        { _id: 10, path: '*code', dataType: 'string', description: '*code', locale: '' },
                        {
                            _id: 20,
                            path: '!ref',
                            dataType: 'string',
                            description: '!ref',
                            locale: '',
                        },
                    ],
                },
            });

            assert.deepEqual(template3.$.context.diagnoses, []);
            assert.equal(await template3.isDefault, false);
            assert.equal(await template3.templateUse, 'importOnly');

            await Test.rollbackCache(context);
            template2 = await context.read(ImportExportTemplate, { id: 'testItem2' }, { forUpdate: true });
            assert.equal(await template2.isDefault, true);

            let template4 = await createImportExportTemplate(context, {
                id: 'testItem4',
                name: 'testItem',
                nodeName: 'TestItem',
                description: 'Test Item',
                isDefault: true,
                templateUse: 'exportOnly',
                csvTemplate: {
                    data: [
                        { _id: 0, path: '!name', dataType: 'string', description: '!name', locale: '' },
                        { _id: 10, path: '*code', dataType: 'string', description: '*code', locale: '' },
                        {
                            _id: 20,
                            path: '!ref',
                            dataType: 'string',
                            description: '!ref',
                            locale: '',
                        },
                    ],
                },
            });

            assert.deepEqual(template4.$.context.diagnoses, []);
            assert.equal(await template4.isDefault, true);
            assert.equal(await template4.templateUse, 'exportOnly');

            await Test.rollbackCache(context);
            template1 = await context.read(ImportExportTemplate, { id: 'testItem1' }, { forUpdate: true });
            assert.equal(await template1.isDefault, false);

            template2 = await context.read(ImportExportTemplate, { id: 'testItem2' }, { forUpdate: true });
            assert.equal(await template2.isDefault, false);

            await template2.$.set({ isDefault: true });
            await template2.$.save();

            template4 = await context.read(ImportExportTemplate, { id: 'testItem4' }, { forUpdate: true });
            assert.equal(await template4.isDefault, false);
        });
    });

    it('Should fail if csv file header property not found in node', async () => {
        await Test.withContext(async context => {
            await assert.isRejected(
                createImportExportTemplate(context, {
                    id: 'testItem',
                    name: 'testItem',
                    nodeName: 'TestItem',
                    description: 'Test Item',
                    csvTemplate: {
                        data: [
                            { _id: 0, path: '!name', dataType: 'string', description: '!name', locale: '' },
                            { _id: 10, path: '*code', dataType: 'string', description: '*code', locale: '' },
                            { _id: 20, path: 'label', dataType: 'string', description: 'label', locale: '' },
                        ],
                    },
                }),
                'The record was not created.',
            );

            assert.deepEqual(context.diagnoses, [
                {
                    severity: 4,
                    path: ['csvTemplate'],
                    message: 'TestItem: TestItem.label : property not found',
                },
            ]);
        });
    });

    it('Should fail if csv file header reference has invalid keys', async () => {
        await Test.withContext(async context => {
            await assert.isRejected(
                createImportExportTemplate(context, {
                    id: 'testItem',
                    name: 'testItem',
                    nodeName: 'TestItem',
                    description: 'Test Item',
                    csvTemplate: {
                        data: [
                            { _id: 0, path: '!name', dataType: 'string', description: '!name', locale: '' },
                            { _id: 10, path: '!code', dataType: 'string', description: '!code', locale: '' },
                            {
                                _id: 20,
                                path: '!ref(refCode)',
                                dataType: 'string',
                                description: '!ref(refCode)',
                                locale: '',
                            },
                        ],
                    },
                }),
                'The record was not created.',
            );

            assert.deepEqual(context.diagnoses, [
                {
                    severity: 3,
                    path: ['csvTemplate'],
                    message:
                        "The !ref(refCode) column header is not valid. Remove '(refCode)' after the property name.",
                },
            ]);
        });
    });

    it('Should raise warning if csv file headerfield(s) property not found in node and invalid keys are passed for reference', async () => {
        await Test.withContext(async context => {
            await createImportExportTemplate(context, {
                id: 'testItem',
                name: 'testItem',
                nodeName: 'TestItem',
                description: 'Test Item',
                csvTemplate: {
                    data: [
                        { _id: 0, path: '!name', dataType: 'string', description: '!name', locale: '' },
                        { _id: 10, path: '!code', dataType: 'string', description: '!code', locale: '' },
                    ],
                },
            });
            assert.deepEqual(context.diagnoses, [
                {
                    severity: 2,
                    path: ['csvTemplate'],
                    message:
                        'The natural key for the TestItem node is missing properties that are not included in the template. Add the missing properties: ref.',
                },
            ]);
        });
    });

    it('Should fail if csv file headerfield(s) not found in node and invalid keys are passed for reference', async () => {
        await Test.withContext(async context => {
            await assert.isRejected(
                createImportExportTemplate(context, {
                    id: 'testItem',
                    name: 'testItem',
                    nodeName: 'TestItem',
                    description: 'Test Item',
                    csvTemplate: {
                        data: [
                            { _id: 0, path: '!name', dataType: 'string', description: '!name', locale: '' },
                            { _id: 10, path: '!code', dataType: 'string', description: '!code', locale: '' },
                            {
                                _id: 20,
                                path: '!ref(refCode)',
                                dataType: 'string',
                                description: '!ref(refCode)',
                                locale: '',
                            },
                            {
                                _id: 30,
                                path: 'refNoNatural',
                                dataType: 'string',
                                description: 'refNoNatural',
                                locale: '',
                            },
                        ],
                    },
                }),
                'The record was not created.',
            );

            assert.deepEqual(context.diagnoses, [
                {
                    message:
                        "The !ref(refCode) column header is not valid. Remove '(refCode)' after the property name.",
                    path: ['csvTemplate'],
                    severity: 3,
                },
                {
                    message:
                        'The refNoNatural header cannot be included in the import template. The TestItemRefNoNaturalKey target node needs to have a natural key index.',
                    path: ['csvTemplate'],
                    severity: 3,
                },
            ]);
        });
    });

    it('Should create an instance of ImportExportTemplate ignoring _error column', async () => {
        const csvPath = './test/fixtures/csv/import-export-template/ignore-error-column.csv';
        await Test.withContext(async context => {
            const importResult = await importTestDoc(context, csvPath, importExportTemplateTestDoc);
            assert.deepEqual(importResult.$.context.diagnoses, []);
            const result = (await importResult.rowsInError).value.split('\n');
            assert.deepEqual(result[1], '');
        });
    });

    it('Should set a general error if the user tries to import with the import export template is export only', () =>
        Test.withContext(async context => {
            const importExportTemplate = await context.create(ImportExportTemplate, {
                id: 'templateTestDoc',
                name: 'templateTestDoc',
                nodeName: 'TestDoc',
                description: '1 description',
                templateUse: 'exportOnly',
                csvTemplate: {
                    data: [
                        { _id: 0, path: '!name', dataType: 'string', description: '!name', locale: '' },
                        { _id: 20, path: 'description', dataType: 'string', description: 'description', locale: '' },
                        { _id: 150, path: '#lines', dataType: 'collection', description: '#lines', locale: '' },
                        { _id: 160, path: '!label', dataType: 'string', description: '!label', locale: '' },
                        { _id: 170, path: '##sublines', dataType: 'collection', description: '##sublines', locale: '' },
                        { _id: 180, path: '*label#1', dataType: 'string', description: '*label#1', locale: '' },
                        {
                            _id: 190,
                            path: 'description#1',
                            dataType: 'string',
                            description: 'description#1',
                            locale: '',
                        },
                        { _id: 200, path: 'item', dataType: 'reference', description: 'item', locale: '' },
                        { _id: 210, path: '#comments', dataType: 'collection', description: '#comments', locale: '' },
                        { _id: 220, path: 'text', dataType: 'string', description: 'text', locale: '' },
                    ],
                },
            });
            assert.deepEqual(importExportTemplate.$.context.diagnoses, []);
            await importExportTemplate.$.save();
            const csvPath = './test/fixtures/csv/functions/success.csv';
            const uploadedFile = await uploadTestFile(csvPath);

            await assert.isRejected(
                ImportExportTemplate.batchImport(context, 'templateTestDoc', String(uploadedFile.uploadedFileId), {
                    doInsert: true,
                }),
                'The templateTestDoc template can be used for export only',
            );
        }));

    it('Should set a general error if the user tries to create import export template with encrypted properties', () =>
        Test.withContext(async context => {
            const importExportTemplate = await context.create(ImportExportTemplate, {
                id: 'templateTestDoc',
                name: 'templateTestDoc',
                nodeName: 'TestDoc',
                description: '1 description',
                templateUse: 'exportOnly',
                csvTemplate: {
                    data: [
                        { _id: 0, path: '!name', dataType: 'string', description: '!name', locale: '' },
                        { _id: 20, path: 'description', dataType: 'string', description: 'description', locale: '' },
                        { _id: 150, path: '#lines', dataType: 'collection', description: '#lines', locale: '' },
                        { _id: 160, path: '!label', dataType: 'string', description: '!label', locale: '' },
                        { _id: 170, path: '##sublines', dataType: 'collection', description: '##sublines', locale: '' },
                        { _id: 180, path: '*label#1', dataType: 'string', description: '*label#1', locale: '' },
                        {
                            _id: 190,
                            path: 'description#1',
                            dataType: 'string',
                            description: 'description#1',
                            locale: '',
                        },
                        { _id: 200, path: 'item', dataType: 'reference', description: 'item', locale: '' },
                        { _id: 210, path: '#comments', dataType: 'collection', description: '#comments', locale: '' },
                        { _id: 220, path: 'text', dataType: 'string', description: 'text', locale: '' },
                    ],
                },
            });
            assert.deepEqual(importExportTemplate.$.context.diagnoses, []);
            await importExportTemplate.$.save();
            const csvPath = './test/fixtures/csv/functions/success.csv';
            const uploadedFile = await uploadTestFile(csvPath);

            await assert.isRejected(
                ImportExportTemplate.batchImport(context, 'templateTestDoc', String(uploadedFile.uploadedFileId), {
                    doInsert: true,
                }),
                'The templateTestDoc template can be used for export only',
            );
        }));

    it('Should set a "The record was not created." error if encrypted field(s) is included in import export template', () =>
        Test.withContext(async context => {
            await assert.isRejected(
                createImportExportTemplate(context, {
                    id: 'templateEncryptedString',
                    name: 'templateEncryptedString',
                    nodeName: 'TestItemWithEncryptedString',
                    description: '1 description',
                    csvTemplate: {
                        data: [
                            { _id: 10, path: '!name', dataType: 'string', description: '!name', locale: '' },
                            { _id: 20, path: 'code', dataType: 'string', description: 'code', locale: '' },
                        ],
                    },
                }),
                'The record was not created.',
            );
        }));

    it('Should set a general error if ImportExportTemplate csvTemplate header does not match downloaded csv header file', async () => {
        const csvPath = './test/fixtures/csv/functions/wrong-header.csv';
        await Test.withContext(async context => {
            const res = await importTestDoc(context, csvPath, importExportTemplateTestDoc);
            assert.strictEqual(
                await res.generalError,
                'invalid csv header: mandatory field(s) *code of template templateTestDoc header not found in csv file',
            );
        });
    });

    it('Should fill error field only for failed row of document', async () => {
        const templateTestDoc2 = {
            ...importExportTemplateTestDoc,
            id: 'templateTestDoc2',
            name: 'templateTestDoc2',
        };

        const csvPath = './test/fixtures/csv/import-export-template/corresponding-row-error.csv';
        const importExportTemplate = await Test.withCommittedContext(async context => {
            const template = await createImportExportTemplate(context, templateTestDoc2);
            assert.deepEqual(template.$.context.diagnoses, []);
            return template;
        });
        await Test.withReadonlyContext(
            async context => {
                const importResult = await importTestDoc(context, csvPath, importExportTemplateTestDoc, {
                    maxErrorCount: 10,
                    importExportTemplate,
                });
                assert.strictEqual(await importResult.rowsProcessed, 16);
                assert.strictEqual(await importResult.numberOfRowsInError, 2);
                const expected = JSON.parse(
                    fs.readFileSync('./test/fixtures/csv/import-export-template/corresponding-row-error.json', 'utf8'),
                );
                let result = (await importResult.rowsInError).value.split('\n');
                result.pop();
                assert.isTrue(result.length > 0);
                result = result.map(row => {
                    const rowArray = row.split(defaultDelimiter);
                    return rowArray[rowArray.length - 1];
                });
                assert.deepEqual(result, expected);
            },
            {
                source: 'listener',
                testMode: true,
            },
        );
    });

    it('throws if user is not authorized', () =>
        Test.withContext(
            async context => {
                const csvPath = './test/fixtures/csv/functions/success.csv';
                const user = await context.user;
                const uploadedFile = await uploadTestFile(csvPath, user?.email);

                await assert.isRejected(
                    ImportExportTemplate.batchImport(context, 'testUserTemplate', String(uploadedFile.uploadedFileId), {
                        doInsert: true,
                    }),
                    'You cannot perform this operation User.create',
                );
            },
            {
                user: { email: '<EMAIL>' },
                testActiveServiceOptions: [xtremAuthorization.serviceOptions.authorizationServiceOption],
            },
        ));

    it('does not throw if user is authorized', () =>
        Test.withContext(
            async context => {
                const csvPath = './test/fixtures/csv/functions/success.csv';
                await importTestDoc(context, csvPath, importExportTemplateTestDoc);
            },
            {
                user: { email: '<EMAIL>' },
                testActiveServiceOptions: [xtremAuthorization.serviceOptions.authorizationServiceOption],
            },
        ));

    it('Should set a general error if canCreate is set to false', () =>
        Test.withContext(async context => {
            const template = await context.read(ImportExportTemplate, { id: 'testCannotCreateTemplate' });
            const stream = Readable.from(Buffer.from(''));
            const csvPath = './test/fixtures/csv/functions/success.csv';
            const user = await context.user;
            const uploadedFile = await uploadTestFile(csvPath, user?.email);
            const importResult = await ImportExportTemplate.createResult(
                context,
                template,
                true,
                false,
                false,
                1,
                'inProgress',
                String(uploadedFile.uploadedFileId),
            );
            const res = await ImportExportTemplate.importCsv(context, context.batch, stream, importResult);
            assert.strictEqual(await res.generalError, 'Node TestCannotCreate cannot be imported');
        }, {}));

    it('Should set a general error if canUpdate is true but the node does not have a natural key', () =>
        Test.withContext(async context => {
            const importExportTemplate = await context.read(ImportExportTemplate, { id: 'testCannotCreateTemplate' });
            const stream = Readable.from(Buffer.from(''));
            const csvPath = './test/fixtures/csv/functions/success.csv';
            const user = await context.user;
            const uploadedFile = await uploadTestFile(csvPath, user?.email);
            const importResult = await ImportExportTemplate.createResult(
                context,
                importExportTemplate,
                false, // doCreate
                true, // doUpdate
                false, // dryRun
                1,
                'inProgress',
                String(uploadedFile.uploadedFileId),
            );
            const res = await ImportExportTemplate.importCsv(context, context.batch, stream, importResult);
            assert.strictEqual(await res.generalError, 'Node TestCannotCreate cannot be imported');
        }, {}));

    it('Should update an instance of ImportExportTemplate at first level', async () => {
        const csvPath = './test/fixtures/csv/functions/success.csv';
        const csvUpdatePath = './test/fixtures/csv/functions/update-success.csv';
        await Test.withContext(async context => {
            await importTestDoc(context, csvPath, importExportTemplateTestDoc);

            // check if TestDoc instance (with name doc 1) is created
            // and  if related TestDocLines, TestDocSublines instances or records are created
            await context.withLocalizedTextAsJson(async () => {
                const testDoc = await context.read(TestDoc, { name: 'doc 1' });
                const expected = JSON.parse(
                    fs.readFileSync(
                        './test/fixtures/csv/import-export-template/payload-success-with-json-as-string.json',
                        'utf8',
                    ),
                );
                compareDocuments(await testDoc.$.payload({ withoutCustomData: true }), expected);
            });

            const importResult = await importTestDoc(context, csvUpdatePath, importExportTemplateTestDoc, {
                doUpdate: true,
            });
            assert.deepEqual(importResult.$.context.diagnoses, []);

            await context.withLocalizedTextAsJson(async () => {
                const testDoc = await context.read(TestDoc, { name: 'doc 1' });
                assert.equal(await testDoc.description, '{"base":"description doc 1bis"}');
            });
        });
    });

    it('Should update an instance of ImportExportTemplate at first level', async () => {
        const csvPath = './test/fixtures/csv/functions/success.csv';
        const csvUpdatePath = './test/fixtures/csv/functions/update-frozen-success.csv';
        const csvUpdateErrorPath = './test/fixtures/csv/functions/update-frozen-error';
        await Test.withContext(async context => {
            await importTestDoc(context, csvPath, importExportTemplateTestDocWithFrozenProperties);

            // check if TestDoc instance (with name doc 1) is created
            // and  if related TestDocLines, TestDocSublines instances or records are created
            await context.withLocalizedTextAsJson(async () => {
                const testDoc = await context.read(TestDocWithFrozenProperties, { name: 'doc 1' });
                const expected = JSON.parse(
                    fs.readFileSync(
                        './test/fixtures/csv/import-export-template/payload-success-with-frozen-properties.json',
                        'utf8',
                    ),
                );
                compareDocuments(await testDoc.$.payload({ withoutCustomData: true }), expected);
            });

            let importResult = await importTestDoc(
                context,
                csvUpdatePath,
                importExportTemplateTestDocWithFrozenProperties,
                {
                    doUpdate: true,
                },
            );
            assert.deepEqual(importResult.$.context.diagnoses, []);

            await context.withLocalizedTextAsJson(async () => {
                const testDoc = await context.read(TestDocWithFrozenProperties, { name: 'doc 1' });
                assert.equal(await testDoc.description, '{"base": "description doc 1"}');
            });

            importResult = await importTestDoc(
                context,
                `${csvUpdateErrorPath}-1.csv`,
                importExportTemplateTestDocWithFrozenProperties,
                {
                    doUpdate: true,
                },
            );
            assert.strictEqual(await importResult.numberOfRowsInError, 1);
            assert.strictEqual(
                (await importResult.rowsInError).value,
                `"!name";"*code";"description(base)";"stringArrayVal";"shortVal";"integerVal";"integerArrayVal";"decimalVal";"booleanVal";"jsonVal";"datetimeVal";"datetimeRangeVal";"dateRangeVal";"enumVal";"enumArrayVal";"transientInput";"_error"
"doc 1";"code1";"description doc 1bis";"[""string 1"",""string 2"",""string 3""]";"1";"123";"[1,2,3,4]";"1.234";"TRUE";"{}";"2022-02-18T13:16:00Z";"[2022-01-01T00:00:00Z,2022-12-31T23:59:59Z]";"[2022-01-01,2022-12-31)";"value2";"[""value1"",""value2""]";"   ";"TestDocWithFrozenProperties.description: cannot set value on frozen property"\n`,
            );
            importResult = await importTestDoc(
                context,
                `${csvUpdateErrorPath}-2.csv`,
                importExportTemplateTestDocWithFrozenProperties,
                {
                    doUpdate: true,
                },
            );
            assert.strictEqual(await importResult.numberOfRowsInError, 1);
            assert.strictEqual(
                (await importResult.rowsInError).value,
                `"!name";"*code";"description";"stringArrayVal";"shortVal";"integerVal";"integerArrayVal";"decimalVal";"booleanVal";"jsonVal";"datetimeVal";"datetimeRangeVal";"dateRangeVal";"enumVal";"enumArrayVal";"_error"
"doc 1";"code1";"description doc 1";"[""string 1"",""string 2"",""string 0""]";"1";"123";"[1,2,3,4]";"1.234";"TRUE";"{}";"2022-02-18T13:16:00Z";"[2022-01-01T00:00:00Z,2022-12-31T23:59:59Z]";"[2022-01-01,2022-12-31)";"value2";"[""value1"",""value2""]";"TestDocWithFrozenProperties.stringArrayVal: cannot set value on frozen property"\n`,
            );
            importResult = await importTestDoc(
                context,
                `${csvUpdateErrorPath}-3.csv`,
                importExportTemplateTestDocWithFrozenProperties,
                {
                    doUpdate: true,
                },
            );
            assert.strictEqual(await importResult.numberOfRowsInError, 1);
            assert.strictEqual(
                (await importResult.rowsInError).value,
                `"!name";"*code";"description";"stringArrayVal";"shortVal";"integerVal";"integerArrayVal";"decimalVal";"booleanVal";"jsonVal";"datetimeVal";"datetimeRangeVal";"dateRangeVal";"enumVal";"enumArrayVal";"_error"
"doc 1";"code1";"description doc 1";"[""string 1"",""string 2"",""string 3""]";"3";"123";"[1,2,3,4]";"1.234";"TRUE";"{}";"2022-02-18T13:16:00Z";"[2022-01-01T00:00:00Z,2022-12-31T23:59:59Z]";"[2022-01-01,2022-12-31)";"value2";"[""value1"",""value2""]";"TestDocWithFrozenProperties.shortVal: cannot set value on frozen property"\n`,
            );
            importResult = await importTestDoc(
                context,
                `${csvUpdateErrorPath}-4.csv`,
                importExportTemplateTestDocWithFrozenProperties,
                {
                    doUpdate: true,
                },
            );
            assert.strictEqual(await importResult.numberOfRowsInError, 1);
            assert.strictEqual(
                (await importResult.rowsInError).value,
                `"!name";"*code";"description";"stringArrayVal";"shortVal";"integerVal";"integerArrayVal";"decimalVal";"booleanVal";"jsonVal";"datetimeVal";"datetimeRangeVal";"dateRangeVal";"enumVal";"enumArrayVal";"_error"
"doc 1";"code1";"description doc 1";"[""string 1"",""string 2"",""string 3""]";"1";"1234";"[1,2,3,4]";"1.234";"TRUE";"{}";"2022-02-18T13:16:00Z";"[2022-01-01T00:00:00Z,2022-12-31T23:59:59Z]";"[2022-01-01,2022-12-31)";"value2";"[""value1"",""value2""]";"TestDocWithFrozenProperties.integerVal: cannot set value on frozen property"\n`,
            );
            importResult = await importTestDoc(
                context,
                `${csvUpdateErrorPath}-5.csv`,
                importExportTemplateTestDocWithFrozenProperties,
                {
                    doUpdate: true,
                },
            );
            assert.strictEqual(await importResult.numberOfRowsInError, 1);
            assert.strictEqual(
                (await importResult.rowsInError).value,
                `"!name";"*code";"description";"stringArrayVal";"shortVal";"integerVal";"integerArrayVal";"decimalVal";"booleanVal";"jsonVal";"datetimeVal";"datetimeRangeVal";"dateRangeVal";"enumVal";"enumArrayVal";"_error"
"doc 1";"code1";"description doc 1";"[""string 1"",""string 2"",""string 3""]";"1";"123";"[1,2,3,4,5]";"1.234";"TRUE";"{}";"2022-02-18T13:16:00Z";"[2022-01-01T00:00:00Z,2022-12-31T23:59:59Z]";"[2022-01-01,2022-12-31)";"value2";"[""value1"",""value2""]";"TestDocWithFrozenProperties.integerArrayVal: cannot set value on frozen property"\n`,
            );
            importResult = await importTestDoc(
                context,
                `${csvUpdateErrorPath}-6.csv`,
                importExportTemplateTestDocWithFrozenProperties,
                {
                    doUpdate: true,
                },
            );
            assert.strictEqual(await importResult.numberOfRowsInError, 1);
            assert.strictEqual(
                (await importResult.rowsInError).value,
                `"!name";"*code";"description";"stringArrayVal";"shortVal";"integerVal";"integerArrayVal";"decimalVal";"booleanVal";"jsonVal";"datetimeVal";"datetimeRangeVal";"dateRangeVal";"enumVal";"enumArrayVal";"_error"
"doc 1";"code1";"description doc 1";"[""string 1"",""string 2"",""string 3""]";"1";"123";"[1,2,3,4]";"1.2346";"TRUE";"{}";"2022-02-18T13:16:00Z";"[2022-01-01T00:00:00Z,2022-12-31T23:59:59Z]";"[2022-01-01,2022-12-31)";"value2";"[""value1"",""value2""]";"TestDocWithFrozenProperties.decimalVal: cannot set value on frozen property"\n`,
            );
            importResult = await importTestDoc(
                context,
                `${csvUpdateErrorPath}-7.csv`,
                importExportTemplateTestDocWithFrozenProperties,
                {
                    doUpdate: true,
                },
            );
            assert.strictEqual(await importResult.numberOfRowsInError, 1);
            assert.strictEqual(
                (await importResult.rowsInError).value,
                `"!name";"*code";"description";"stringArrayVal";"shortVal";"integerVal";"integerArrayVal";"decimalVal";"booleanVal";"jsonVal";"datetimeVal";"datetimeRangeVal";"dateRangeVal";"enumVal";"enumArrayVal";"_error"
"doc 1";"code1";"description doc 1";"[""string 1"",""string 2"",""string 3""]";"1";"123";"[1,2,3,4]";"1.234";"FALSE";"{}";"2022-02-18T13:16:00Z";"[2022-01-01T00:00:00Z,2022-12-31T23:59:59Z]";"[2022-01-01,2022-12-31)";"value2";"[""value1"",""value2""]";"TestDocWithFrozenProperties.booleanVal: cannot set value on frozen property"\n`,
            );
            importResult = await importTestDoc(
                context,
                `${csvUpdateErrorPath}-8.csv`,
                importExportTemplateTestDocWithFrozenProperties,
                {
                    doUpdate: true,
                },
            );
            assert.strictEqual(await importResult.numberOfRowsInError, 1);
            assert.strictEqual(
                (await importResult.rowsInError).value,
                `"!name";"*code";"description";"stringArrayVal";"shortVal";"integerVal";"integerArrayVal";"decimalVal";"booleanVal";"jsonVal";"datetimeVal";"datetimeRangeVal";"dateRangeVal";"enumVal";"enumArrayVal";"_error"
"doc 1";"code1";"description doc 1";"[""string 1"",""string 2"",""string 3""]";"1";"123";"[1,2,3,4]";"1.234";"TRUE";"{a:""b""}";"2022-02-18T13:16:00Z";"[2022-01-01T00:00:00Z,2022-12-31T23:59:59Z]";"[2022-01-01,2022-12-31)";"value2";"[""value1"",""value2""]";"TestDocWithFrozenProperties.jsonVal: cannot set value on frozen property"\n`,
            );
            importResult = await importTestDoc(
                context,
                `${csvUpdateErrorPath}-9.csv`,
                importExportTemplateTestDocWithFrozenProperties,
                {
                    doUpdate: true,
                },
            );
            assert.strictEqual(await importResult.numberOfRowsInError, 1);
            assert.strictEqual(
                (await importResult.rowsInError).value,
                `"!name";"*code";"description";"stringArrayVal";"shortVal";"integerVal";"integerArrayVal";"decimalVal";"booleanVal";"jsonVal";"datetimeVal";"datetimeRangeVal";"dateRangeVal";"enumVal";"enumArrayVal";"_error"
"doc 1";"code1";"description doc 1";"[""string 1"",""string 2"",""string 3""]";"1";"123";"[1,2,3,4]";"1.234";"TRUE";"{}";"2023-02-18T13:16:00Z";"[2022-01-01T00:00:00Z,2022-12-31T23:59:59Z]";"[2022-01-01,2022-12-31)";"value2";"[""value1"",""value2""]";"TestDocWithFrozenProperties.datetimeVal: cannot set value on frozen property"\n`,
            );
            importResult = await importTestDoc(
                context,
                `${csvUpdateErrorPath}-10.csv`,
                importExportTemplateTestDocWithFrozenProperties,
                {
                    doUpdate: true,
                },
            );
            assert.strictEqual(await importResult.numberOfRowsInError, 1);
            assert.strictEqual(
                (await importResult.rowsInError).value,
                `"!name";"*code";"description";"stringArrayVal";"shortVal";"integerVal";"integerArrayVal";"decimalVal";"booleanVal";"jsonVal";"datetimeVal";"datetimeRangeVal";"dateRangeVal";"enumVal";"enumArrayVal";"_error"
"doc 1";"code1";"description doc 1";"[""string 1"",""string 2"",""string 3""]";"1";"123";"[1,2,3,4]";"1.234";"TRUE";"{}";"2022-02-18T13:16:00Z";"[2020-01-01T00:00:00Z,2022-12-31T23:59:59Z]";"[2022-01-01,2022-12-31)";"value2";"[""value1"",""value2""]";"TestDocWithFrozenProperties.datetimeRangeVal: cannot set value on frozen property"\n`,
            );
            importResult = await importTestDoc(
                context,
                `${csvUpdateErrorPath}-11.csv`,
                importExportTemplateTestDocWithFrozenProperties,
                {
                    doUpdate: true,
                },
            );
            assert.strictEqual(await importResult.numberOfRowsInError, 1);
            assert.strictEqual(
                (await importResult.rowsInError).value,
                `"!name";"*code";"description";"stringArrayVal";"shortVal";"integerVal";"integerArrayVal";"decimalVal";"booleanVal";"jsonVal";"datetimeVal";"datetimeRangeVal";"dateRangeVal";"enumVal";"enumArrayVal";"_error"
"doc 1";"code1";"description doc 1";"[""string 1"",""string 2"",""string 3""]";"1";"123";"[1,2,3,4]";"1.234";"TRUE";"{}";"2022-02-18T13:16:00Z";"[2022-01-01T00:00:00Z,2022-12-31T23:59:59Z]";"[2020-01-01,2022-12-31)";"value2";"[""value1"",""value2""]";"TestDocWithFrozenProperties.dateRangeVal: cannot set value on frozen property"\n`,
            );
            importResult = await importTestDoc(
                context,
                `${csvUpdateErrorPath}-12.csv`,
                importExportTemplateTestDocWithFrozenProperties,
                {
                    doUpdate: true,
                },
            );
            assert.strictEqual(await importResult.numberOfRowsInError, 1);
            assert.strictEqual(
                (await importResult.rowsInError).value,
                `"!name";"*code";"description";"stringArrayVal";"shortVal";"integerVal";"integerArrayVal";"decimalVal";"booleanVal";"jsonVal";"datetimeVal";"datetimeRangeVal";"dateRangeVal";"enumVal";"enumArrayVal";"_error"
"doc 1";"code1";"description doc 1";"[""string 1"",""string 2"",""string 3""]";"1";"123";"[1,2,3,4]";"1.234";"TRUE";"{}";"2022-02-18T13:16:00Z";"[2022-01-01T00:00:00Z,2022-12-31T23:59:59Z]";"[2022-01-01,2022-12-31)";"value1";"[""value1"",""value2""]";"TestDocWithFrozenProperties.enumVal: cannot set value on frozen property"\n`,
            );
            importResult = await importTestDoc(
                context,
                `${csvUpdateErrorPath}-13.csv`,
                importExportTemplateTestDocWithFrozenProperties,
                {
                    doUpdate: true,
                },
            );
            assert.strictEqual(await importResult.numberOfRowsInError, 1);
            assert.strictEqual(
                (await importResult.rowsInError).value,
                `"!name";"*code";"description";"stringArrayVal";"shortVal";"integerVal";"integerArrayVal";"decimalVal";"booleanVal";"jsonVal";"datetimeVal";"datetimeRangeVal";"dateRangeVal";"enumVal";"enumArrayVal";"_error"
"doc 1";"code1";"description doc 1";"[""string 1"",""string 2"",""string 3""]";"1";"123";"[1,2,3,4]";"1.234";"TRUE";"{}";"2022-02-18T13:16:00Z";"[2022-01-01T00:00:00Z,2022-12-31T23:59:59Z]";"[2022-01-01,2022-12-31)";"value2";"[""value1""]";"TestDocWithFrozenProperties.enumArrayVal: cannot set value on frozen property"\n`,
            );
            importResult = await importTestDoc(
                context,
                `${csvUpdateErrorPath}-14.csv`,
                importExportTemplateTestDocWithFrozenProperties,
                {
                    doUpdate: true,
                },
            );
            assert.strictEqual(await importResult.numberOfRowsInError, 1);
            assert.strictEqual(
                (await importResult.rowsInError).value,
                `"!name";"*code";"itemWithDefaultValue";"_error"
"doc 1";"code1";"COD2|NAM2|NAM2_REF";"TestDocWithFrozenProperties.itemWithDefaultValue: cannot set value on frozen property"\n`,
            );
        });
    });

    it('Should import - creation then update', async () => {
        await Test.withContext(async context => {
            const csvPath = './test/fixtures/csv/functions/success-with-default-values.csv';
            const imported = await importTestDoc(context, csvPath, importExportTemplateTestDocRefDefaultValue);
            assert.equal(await imported.numberOfRowsInError, 0);

            // check if TestDocRefDefaultValue instance (with name doc 1) is created
            // and  if related TestDocLineRefDefaultValue instances or records are created
            const testDoc1 = await context.read(TestDocRefDefaultValue, { name: 'doc 1' });
            const expected = JSON.parse(
                fs.readFileSync(
                    './test/fixtures/csv/import-export-template/payload-success-with-default-value.json',
                    'utf8',
                ),
            );
            compareDocuments(await testDoc1.$.payload({ withoutCustomData: true }), expected);

            const itemDocLineName = await (await testDoc1.itemWithDefaultValueDocLevel)?.name;
            assert.equal(itemDocLineName, 'NAM1');
            const lines = await testDoc1.lines.toArray();
            if (lines) {
                const itemWithDefaultValueLineLevelName = await (await lines[0].itemWithDefaultValueLineLevel)?.name;
                assert.equal(itemWithDefaultValueLineLevelName, 'NAM1');
            } else {
                throw new Error('Lines should be present');
            }

            // without column in csv
            let csvPathUpdate = './test/fixtures/csv/functions/update-with-default-values-without-column.csv';
            let updated = await importTestDoc(context, csvPathUpdate, importExportTemplateTestDocRefDefaultValue, {
                doUpdate: true,
            });
            assert.equal(await updated.numberOfRowsInError, 0);

            // check if TestDocRefDefaultValue instance (with name doc 1) is updated
            // and  if related TestDocLineRefDefaultValue instances or records are updated
            let testDoc1Updated = await context.read(TestDocRefDefaultValue, { name: 'doc 1' }, { forUpdate: true });
            let updateExpected = JSON.parse(
                fs.readFileSync(
                    './test/fixtures/csv/import-export-template/upload-success-with-default-value-without-column.json',
                    'utf8',
                ),
            );
            compareDocuments(await testDoc1Updated.$.payload({ withoutCustomData: true }), updateExpected);

            let itemDocLineUpdated = await testDoc1Updated.itemWithDefaultValueDocLevel;
            assert.equal(itemDocLineUpdated, null);
            let linesUpdated = await testDoc1Updated.lines.toArray();
            if (linesUpdated) {
                const itemWithDefaultValueLineLevelName = await (
                    await linesUpdated[0].itemWithDefaultValueLineLevel
                )?.name;
                assert.equal(itemWithDefaultValueLineLevelName, 'NAM1');
            } else {
                throw new Error('Lines should be present');
            }

            // with column in csv
            csvPathUpdate = './test/fixtures/csv/functions/update-with-default-values-with-column.csv';
            updated = await importTestDoc(context, csvPathUpdate, importExportTemplateTestDocRefDefaultValue, {
                doUpdate: true,
            });
            assert.equal(await updated.numberOfRowsInError, 0);

            testDoc1Updated = await context.read(TestDocRefDefaultValue, { name: 'doc 1' }, { forUpdate: true });
            updateExpected = JSON.parse(
                fs.readFileSync(
                    './test/fixtures/csv/import-export-template/upload-success-with-default-value-with-column.json',
                    'utf8',
                ),
            );
            compareDocuments(await testDoc1Updated.$.payload({ withoutCustomData: true }), updateExpected);

            itemDocLineUpdated = await testDoc1Updated.itemWithDefaultValueDocLevel;
            assert.equal(itemDocLineUpdated, null);
            linesUpdated = await testDoc1Updated.lines.toArray();
            if (linesUpdated) {
                const itemWithDefaultValueLineLevel = await linesUpdated[0].itemWithDefaultValueLineLevel;
                assert.equal(itemWithDefaultValueLineLevel, null);
            } else {
                throw new Error('Lines should be present');
            }
        });
    });

    it('Should import - with html content', async () => {
        await Test.withContext(async context => {
            const csvPath = './test/fixtures/csv/functions/html-text-stream.csv';
            const imported = await importTestDoc(context, csvPath, importExportTemplateTestTextStream);
            assert.equal(await imported.numberOfRowsInError, 1);

            const expected = JSON.parse(
                fs.readFileSync('./test/fixtures/csv/import-export-template/payload-html-text-stream.json', 'utf8'),
            );

            await checkImportHtmlTextStream(context, expected);
        });
    });

    it('Should import vital collection child successfully in both insert/update cases', async () => {
        const csvDocPath = './test/fixtures/csv/functions/success.csv';
        const csvDocCommentPath = './test/fixtures/csv/functions/test-doc-comment.csv';
        const csvUpdateDocCommentPath = './test/fixtures/csv/functions/update-test-doc-comment.csv';
        async function importTestDocComment(
            context: Context,
            csvPath: string,
            importExportTemplate: {
                id: string;
                name: string;
                description: string;
                nodeName: string;
                csvTemplate: { data: CsvTemplateContent[] };
            },
            options?: {
                // doInsert?: boolean;
                doUpdate?: boolean;
                maxErrorCount?: number;
                importExportTemplate?: ImportExportTemplate;
            },
        ): Promise<ImportResult> {
            const user = await context.user;
            const doImport = async (template: ImportExportTemplate): Promise<ImportResult> => {
                const stream = fs.createReadStream(csvPath, 'utf8');
                const uploadedFile = await uploadTestFile(csvPath, user?.email);
                const importResult = await ImportExportTemplate.createResult(
                    context,
                    template,
                    true,
                    !!options?.doUpdate,
                    false,
                    options?.maxErrorCount || 1,
                    'inProgress',
                    String(uploadedFile.uploadedFileId),
                );
                return ImportExportTemplate.importCsv(context, context.batch, stream, importResult);
            };
            let template = options?.importExportTemplate;
            if (options?.doUpdate && !template) {
                template = await context.read(ImportExportTemplate, { id: importExportTemplate.id });
                return doImport(template);
            }
            if (!template) {
                template = await createImportExportTemplate(context, importExportTemplate);
                assert.deepEqual(template.$.context.diagnoses, []);
            }
            return doImport(template);
        }

        await Test.withContext(async context => {
            const importedDoc = await importTestDoc(context, csvDocPath, importExportTemplateTestDoc);
            assert.equal(await importedDoc.numberOfRowsInError, 0);

            // check if TestDoc instance (with name doc 1) is created
            // and  if related TestDocLines, TestDocSublines instances or records are created
            const testDoc1 = await context.read(TestDoc, { name: 'doc 1' }, { forUpdate: true });
            const expected = JSON.parse(
                fs.readFileSync('./test/fixtures/csv/import-export-template/payload-success.json', 'utf8'),
            );
            compareDocuments(await testDoc1.$.payload({ withoutCustomData: true }), expected);

            const importedComment = await importTestDocComment(
                context,
                csvDocCommentPath,
                importExportTemplateTestDocComment,
            );
            assert.equal(await importedComment.numberOfRowsInError, 0);

            const testDoc1CommentAdded = await context.read(TestDoc, { name: 'doc 1' }, { forUpdate: true });

            assert.equal(await testDoc1CommentAdded.comments.length, 4);

            let comments = await testDoc1CommentAdded.comments.toArray();
            if (comments) {
                const comment1 = await comments[0].text;
                assert.equal(comment1, 'hello');
                const comment2 = await comments[1].text;
                assert.equal(comment2, 'world');
                const comment3 = await comments[2].text;
                assert.equal(comment3, 'This is a test document comment');
                const comment4 = await comments[3].text;
                assert.equal(comment4, 'This is another test document comment');
            } else {
                throw new Error('Lines should be present');
            }

            const importedCommentUpdated = await importTestDocComment(
                context,
                csvUpdateDocCommentPath,
                importExportTemplateTestDocComment,
                {
                    doUpdate: true,
                },
            );
            assert.equal(await importedCommentUpdated.numberOfRowsInError, 0);

            const testDoc1CommentAddedUpdated = await context.read(TestDoc, { name: 'doc 1' }, { forUpdate: true });

            assert.equal(await testDoc1CommentAddedUpdated.comments.length, 4);

            comments = await testDoc1CommentAddedUpdated.comments.toArray();
            if (comments) {
                const comment1 = await comments[0].text;
                assert.equal(comment1, 'hello updated');
                const comment2 = await comments[1].text;
                assert.equal(comment2, 'world updated');
                const comment3 = await comments[2].text;
                assert.equal(comment3, 'This is a test document comment');
                const comment4 = await comments[3].text;
                assert.equal(comment4, 'This is another test document comment');
            } else {
                throw new Error('Lines should be present');
            }
        });
    });

    it('Should import vital collection child without _sortValue in insert mode and reject in update mode', async () => {
        const csvDocPath = './test/fixtures/csv/functions/test-doc-without-sort-value.csv';
        const csvUpdateDocPath = './test/fixtures/csv/functions/update-test-doc-without-sort-value.csv';
        async function importTestDocWithoutSortValue(
            context: Context,
            csvPath: string,
            importExportTemplate: {
                id: string;
                name: string;
                description: string;
                nodeName: string;
                csvTemplate: { data: CsvTemplateContent[] };
            },
            options?: {
                doInsert?: boolean;
                doUpdate?: boolean;
                maxErrorCount?: number;
                importExportTemplate?: ImportExportTemplate;
            },
        ): Promise<ImportResult> {
            const user = await context.user;
            const doImport = async (template: ImportExportTemplate): Promise<ImportResult> => {
                const stream = fs.createReadStream(csvPath, 'utf8');
                const uploadedFile = await uploadTestFile(csvPath, user?.email);
                const importResult = await ImportExportTemplate.createResult(
                    context,
                    template,
                    !!options?.doInsert,
                    !!options?.doUpdate,
                    false,
                    options?.maxErrorCount || 1,
                    'inProgress',
                    String(uploadedFile.uploadedFileId),
                );
                return ImportExportTemplate.importCsv(context, context.batch, stream, importResult);
            };
            let template = options?.importExportTemplate;
            if (options?.doUpdate && !template) {
                template = await context.read(ImportExportTemplate, { id: importExportTemplate.id });
                return doImport(template);
            }
            if (!template) {
                template = await createImportExportTemplate(context, importExportTemplate);
                assert.deepEqual(template.$.context.diagnoses, []);
            }
            return doImport(template);
        }

        await Test.withContext(async context => {
            const importedDoc = await importTestDocWithoutSortValue(
                context,
                csvDocPath,
                importExportTemplateTestDocWithSortValue,
                {
                    doInsert: true,
                },
            );
            assert.equal(await importedDoc.numberOfRowsInError, 0);

            const testDocWithoutSortValue = await context.read(
                TestDocWithNonVitalRef,
                { name: 'doc 1 without _sortValue' },
                { forUpdate: true },
            );

            assert.equal(await testDocWithoutSortValue.lines.length, 2);

            const lines = await testDocWithoutSortValue.lines.toArray();
            if (lines) {
                assert.equal(await lines[0].subLines.length, 2);
                const subLines1 = await lines[0].subLines.toArray();
                assert.equal(await subLines1[0].label, 'subLine 1.1.1');
                assert.equal(await subLines1[1].label, 'subLine 1.1.2');

                assert.equal(await lines[1].subLines.length, 3);
                const subLines2 = await lines[1].subLines.toArray();
                assert.equal(await subLines2[0].label, 'subLine 1.2.1');
                assert.equal(await subLines2[1].label, 'subLine 1.2.2');
                assert.equal(await subLines2[2].label, 'subLine 1.2.3');
            } else {
                throw new Error('Lines should be present');
            }

            const importedDocUpdated = await importTestDocWithoutSortValue(
                context,
                csvUpdateDocPath,
                importExportTemplateTestDocWithSortValue,
                {
                    doUpdate: true,
                },
            );
            assert.strictEqual(
                await importedDocUpdated.generalError,
                'invalid csv header: natural keys field(s) lines.!_sortValue,lines.subLines.!_sortValue#1 of template templateTestDocSortValue header not found in csv file',
            );
        });
    });

    it('Should import direct vital collection child without _sortValue in insert mode and reject in update mode', async () => {
        const csvDocPath = './test/fixtures/csv/functions/success.csv';
        const csvDocCommentPath = './test/fixtures/csv/functions/test-doc-comment-without-sort-value.csv';
        const csvUpdateDocCommentPath = './test/fixtures/csv/functions/update-test-doc-comment-without-sort-value.csv';
        async function importTestDocComment(
            context: Context,
            csvPath: string,
            importExportTemplate: {
                id: string;
                name: string;
                description: string;
                nodeName: string;
                csvTemplate: { data: CsvTemplateContent[] };
            },
            options?: {
                // doInsert?: boolean;
                doUpdate?: boolean;
                maxErrorCount?: number;
                importExportTemplate?: ImportExportTemplate;
            },
        ): Promise<ImportResult> {
            const user = await context.user;
            const doImport = async (template: ImportExportTemplate): Promise<ImportResult> => {
                const stream = fs.createReadStream(csvPath, 'utf8');
                const uploadedFile = await uploadTestFile(csvPath, user?.email);
                const importResult = await ImportExportTemplate.createResult(
                    context,
                    template,
                    true,
                    !!options?.doUpdate,
                    false,
                    options?.maxErrorCount || 1,
                    'inProgress',
                    String(uploadedFile.uploadedFileId),
                );
                return ImportExportTemplate.importCsv(context, context.batch, stream, importResult);
            };
            let template = options?.importExportTemplate;
            if (options?.doUpdate && !template) {
                template = await context.read(ImportExportTemplate, { id: importExportTemplate.id });
                return doImport(template);
            }
            if (!template) {
                template = await createImportExportTemplate(context, importExportTemplate);
                assert.deepEqual(template.$.context.diagnoses, []);
            }
            return doImport(template);
        }

        await Test.withContext(async context => {
            const importedDoc = await importTestDoc(context, csvDocPath, importExportTemplateTestDoc);
            assert.equal(await importedDoc.numberOfRowsInError, 0);

            // check if TestDoc instance (with name doc 1) is created
            // and  if related TestDocLines, TestDocSublines instances or records are created
            const testDoc1 = await context.read(TestDoc, { name: 'doc 1' }, { forUpdate: true });
            const expected = JSON.parse(
                fs.readFileSync('./test/fixtures/csv/import-export-template/payload-success.json', 'utf8'),
            );
            compareDocuments(await testDoc1.$.payload({ withoutCustomData: true }), expected);

            const importedComment = await importTestDocComment(
                context,
                csvDocCommentPath,
                importExportTemplateTestDocComment,
            );
            assert.equal(await importedComment.numberOfRowsInError, 0);

            const testDoc1CommentAdded = await context.read(TestDoc, { name: 'doc 1' }, { forUpdate: true });

            assert.equal(await testDoc1CommentAdded.comments.length, 4);

            const comments = await testDoc1CommentAdded.comments.toArray();
            if (comments) {
                const comment1 = await comments[0].text;
                assert.equal(comment1, 'hello');
                const comment2 = await comments[1].text;
                assert.equal(comment2, 'world');
                const comment3 = await comments[2].text;
                assert.equal(comment3, 'This is a test document comment');
                const comment4 = await comments[3].text;
                assert.equal(comment4, 'This is another test document comment');
            } else {
                throw new Error('Lines should be present');
            }

            const importedCommentUpdated = await importTestDocComment(
                context,
                csvUpdateDocCommentPath,
                importExportTemplateTestDocComment,
                {
                    doUpdate: true,
                },
            );
            assert.strictEqual(await importedCommentUpdated.numberOfRowsInError, 1);
            assert.strictEqual(
                (await importedCommentUpdated.rowsInError).value,
                '"!parentDoc";"text";"name";"description#2";"_error"\n"doc 1";"hello updated";"name 1";"description 1";"Cannot update record. Missing key values (_sortValue)"\n',
            );
        });
    });

    it('Should import vital child ref successfully in both insert/update cases', async () => {
        const csvDocPath = './test/fixtures/csv/functions/test-doc-with-vital-ref.csv';
        const csvDocRefPath = './test/fixtures/csv/functions/test-vital-ref-child.csv';
        const csvUpdateDocRefPath = './test/fixtures/csv/functions/test-vital-ref-child-update.csv';
        async function importTestDocWithVitalRef(
            context: Context,
            csvPath: string,
            importExportTemplate: {
                id: string;
                name: string;
                description: string;
                nodeName: string;
                csvTemplate: { data: CsvTemplateContent[] };
            },
            options?: {
                // doInsert?: boolean;
                doUpdate?: boolean;
                maxErrorCount?: number;
                importExportTemplate?: ImportExportTemplate;
            },
        ): Promise<ImportResult> {
            const user = await context.user;
            const doImport = async (template: ImportExportTemplate): Promise<ImportResult> => {
                const stream = fs.createReadStream(csvPath, 'utf8');
                const uploadedFile = await uploadTestFile(csvPath, user?.email);
                const importResult = await ImportExportTemplate.createResult(
                    context,
                    template,
                    true,
                    !!options?.doUpdate,
                    false,
                    options?.maxErrorCount || 1,
                    'inProgress',
                    String(uploadedFile.uploadedFileId),
                );
                return ImportExportTemplate.importCsv(context, context.batch, stream, importResult);
            };
            let template = options?.importExportTemplate;
            if (options?.doUpdate && !template) {
                template = await context.read(ImportExportTemplate, { id: importExportTemplate.id });
                return doImport(template);
            }
            if (!template) {
                template = await createImportExportTemplate(context, importExportTemplate);
                assert.deepEqual(template.$.context.diagnoses, []);
            }
            return doImport(template);
        }

        await Test.withContext(async context => {
            const importedDoc = await importTestDocWithVitalRef(
                context,
                csvDocPath,
                importExportTemplateTestDocVitalRef,
            );
            assert.equal(await importedDoc.numberOfRowsInError, 0);

            // check if TestDoc instance (with name doc 1) is created
            // and  if related TestDocLines, TestDocSublines instances or records are created
            const testDocVitalRef1 = await context.read(TestDocVitalRef, { name: 'doc 1' }, { forUpdate: true });
            const expected = JSON.parse(
                fs.readFileSync(
                    './test/fixtures/csv/import-export-template/payload-success-with-vital-ref.json',
                    'utf8',
                ),
            );
            assert.deepEqual(await testDocVitalRef1.$.payload({ withoutCustomData: true }), expected);

            const importedVitalRef = await importTestDocWithVitalRef(
                context,
                csvDocRefPath,
                importExportTemplateTestVitalRefChild,
            );
            assert.equal(await importedVitalRef.numberOfRowsInError, 0);

            const testDocVitalRef3Added = await context.read(TestDocVitalRef, { name: 'doc 3' }, { forUpdate: true });

            assert.equal(await (await testDocVitalRef3Added.vitalRef)?.childRefName, 'This is a vital ref child 3');
            assert.equal(await (await testDocVitalRef3Added.vitalRef)?.description, 'description 3');

            const testDocVitalRefUpdated = await importTestDocWithVitalRef(
                context,
                csvUpdateDocRefPath,
                importExportTemplateTestVitalRefChild,
                {
                    doUpdate: true,
                },
            );
            assert.equal(await testDocVitalRefUpdated.numberOfRowsInError, 0);

            const testDocVitalRef1Updated = await context.read(TestDocVitalRef, { name: 'doc 1' }, { forUpdate: true });

            assert.equal(
                await (
                    await testDocVitalRef1Updated.vitalRef
                )?.childRefName,
                'This is a vital ref child 1 bis',
            );
            assert.equal(await (await testDocVitalRef1Updated.vitalRef)?.description, 'description 1 bis');

            const testDocVitalRef2Updated = await context.read(TestDocVitalRef, { name: 'doc 2' }, { forUpdate: true });

            assert.equal(await (await testDocVitalRef2Updated.vitalRef)?.childRefName, 'This is a vital ref child 2');
            assert.equal(await (await testDocVitalRef2Updated.vitalRef)?.description, 'description 2 bis');
        });
    });
});

describe('Generate import-export templates', () => {
    it('Generate TestDoc import-export template', async () => {
        // Let's create the tenant'locale:
        await Test.withCommittedContext(cx => Context.localizationManager.createTenantLocale(cx, 'en-US'));

        await Test.withContext(
            async context => {
                // Let's add de-DE locale:
                await context.setDefaultLocale();
                await asyncArray(['de-DE']).forEach(async locale => {
                    const newLocale = await context.create(xtremSystem.nodes.Locale, {
                        id: locale,
                    });
                    await newLocale.$.save();
                });

                const template = await ImportExportTemplate.getDefaultCsvTemplate(context, 'TestDoc');
                assert.notEqual(template.csvTemplate.value.length, 0);
                const headerLines = template.csvTemplate.value!.split('\n');
                assert.equal(headerLines.length, 5);
                assert.equal(headerLines[0], `${templateTestDoc.csvTemplate};`);

                const names = headerLines[0].split(defaultDelimiter);
                const types = headerLines[1].split(defaultDelimiter);
                const descriptions = headerLines[2].split(defaultDelimiter);
                assert.notEqual(names.length, 0);
                assert.equal(types.length, names.length);
                assert.equal(descriptions.length, names.length);

                assert.deepEqual(
                    names.reduce(
                        (r, k, i) => {
                            r[k] = {
                                type: types[i],
                                description: descriptions[i],
                            };
                            return r;
                        },
                        {} as Dict<{ type: string; description: string }>,
                    ),
                    {
                        '!name': {
                            type: 'string',
                            description: 'name',
                        },
                        '*code': {
                            type: 'string',
                            description: 'code',
                        },
                        description: {
                            type: 'localized text',
                            description:
                                '"default locale: en-US, other locales can be specified with (locale-name), for example ""description (de-DE)"". Available locales are (de-DE, en-GB, en-US, fr-FR, ...). You can also duplicate the columns to import several translations"',
                        },
                        stringArrayVal: {
                            type: 'stringArray',
                            description: 'string array val',
                        },
                        shortVal: {
                            type: 'short',
                            description: 'short val',
                        },
                        integerVal: {
                            type: 'integer',
                            description: 'integer val',
                        },
                        integerArrayVal: {
                            type: 'integerArray',
                            description: 'integer array val',
                        },
                        decimalVal: {
                            type: 'decimal',
                            description: 'decimal val',
                        },
                        booleanVal: {
                            type: 'boolean',
                            description: 'boolean val (false/true)',
                        },
                        jsonVal: {
                            type: 'json',
                            description: 'json val',
                        },
                        datetimeVal: {
                            type: 'datetime',
                            description: 'datetime val (yyyy-mm-ddThh:mm:ssZ)',
                        },
                        datetimeRangeVal: {
                            type: 'datetimeRange',
                            description: 'datetime range val',
                        },
                        dateRangeVal: {
                            type: 'dateRange',
                            description: 'date range val',
                        },
                        enumVal: {
                            type: 'enum(value1,value2,value3)',
                            description: 'enum val',
                        },
                        enumArrayVal: {
                            type: 'enumArray(value1,value2,value3)',
                            description: 'enum array val',
                        },
                        transientInput: {
                            description: 'transient input',
                            type: 'string',
                        },
                        '#lines': {
                            type: 'collection',
                            description: 'lines',
                        },
                        '*label': {
                            type: 'string',
                            description: 'label',
                        },
                        '##sublines': {
                            type: 'collection',
                            description: 'sublines',
                        },
                        '*label#1': {
                            type: 'string',
                            description: 'label',
                        },
                        'description#1': {
                            type: 'string',
                            description: 'description',
                        },
                        item: {
                            type: 'reference',
                            description: 'item (#code|name|ref)',
                        },
                        '//testContentAddressable': {
                            description: 'test content addressable',
                            type: 'reference',
                        },
                        '*name': {
                            description: 'name',
                            type: 'string',
                        },
                        '*description': {
                            description: 'description',
                            type: 'string',
                        },
                        '#comments': {
                            type: 'collection',
                            description: 'comments',
                        },
                        '*name#1': {
                            description: 'name',
                            type: 'string',
                        },
                        '*description#1': {
                            description: 'description',
                            type: 'string',
                        },
                        text: {
                            type: 'string',
                            description: 'text',
                        },
                        '': {
                            description: 'IGNORE',
                            type: 'IGNORE',
                        },
                    },
                );
            },
            { locale: 'en-US' },
        );
    });
});

describe('Import localized texts', () => {
    async function importScenarioNotEmpty(
        context: Context,
        scenario: string,
        options: { doInsert?: boolean; doUpdate?: boolean },
    ): Promise<TestLocalizedTextNotEmpty> {
        const fileContents = fs.readFileSync(scenario, 'utf8');
        const lines = fileContents.split('\n');
        const scenarioName = lines[1].split(defaultDelimiter)[0];

        const scenarioId = lodash.camelCase(path.basename(scenario, '.csv'));
        const templateTestLocalizedTextNotEmpty = {
            id: scenarioId,
            name: scenarioId,
            code: scenarioId,
            nodeName: 'TestLocalizedTextNotEmpty',
            description: 'description',
            csvTemplate: {
                data: [
                    { _id: 0, path: '!name', dataType: 'string', description: '!name', locale: '' },
                    { _id: 10, path: 'description', dataType: 'string', description: 'description', locale: '' },
                    { _id: 20, path: 'description', dataType: 'string', description: 'description', locale: 'en-US' },
                ],
            },
        };

        const importExportTemplate = await createImportExportTemplate(context, templateTestLocalizedTextNotEmpty);
        const stream = fs.createReadStream(scenario, 'utf8');
        const user = await context.user;
        const uploadedFile = await uploadTestFile(scenario, user?.email);
        let importResult = await ImportExportTemplate.createResult(
            context,
            importExportTemplate,
            true,
            !!options?.doUpdate,
            false,
            1,
            'inProgress',
            String(uploadedFile.uploadedFileId),
        );

        importResult = await ImportExportTemplate.importCsv(context, context.batch, stream, importResult);
        assert.equal(await importResult.rowsProcessed, 1);
        assert.equal(await importResult.numberOfRowsInError, 0);
        return context.withLocalizedTextAsJson(() =>
            // forUpdate to make sure to fetch the text from the database and not from memory
            context.read(TestLocalizedTextNotEmpty, { name: scenarioName }, { forUpdate: true }),
        );
    }

    async function importScenario(
        context: Context,
        scenario: string,
        options: { doInsert?: boolean; doUpdate?: boolean },
    ): Promise<TestLocalizedTextNotEmpty> {
        const fileContents = fs.readFileSync(scenario, 'utf8');
        const lines = fileContents.split('\n');
        const scenarioName = lines[1].split(defaultDelimiter)[0];

        const scenarioId = lodash.camelCase(path.basename(scenario, '.csv'));
        const templateTestLocalizedText = {
            id: scenarioId,
            name: scenarioId,
            code: scenarioId,
            nodeName: 'TestLocalizedText',
            description: 'description',
            csvTemplate: {
                data: [
                    { _id: 0, path: '!name', dataType: 'string', description: '!name', locale: '' },
                    { _id: 10, path: 'description', dataType: 'string', description: 'description', locale: '' },
                    { _id: 20, path: 'description', dataType: 'string', description: 'description', locale: 'en-US' },
                ],
            },
        };

        const importExportTemplate = await createImportExportTemplate(context, templateTestLocalizedText);
        const stream = fs.createReadStream(scenario, 'utf8');
        const user = await context.user;
        const uploadedFile = await uploadTestFile(scenario, user?.email);
        let importResult = await ImportExportTemplate.createResult(
            context,
            importExportTemplate,
            true,
            !!options?.doUpdate,
            false,
            1,
            'inProgress',
            String(uploadedFile.uploadedFileId),
        );

        importResult = await ImportExportTemplate.importCsv(context, context.batch, stream, importResult);
        assert.equal(await importResult.rowsProcessed, 1);
        assert.equal(await importResult.numberOfRowsInError, 0);
        return context.withLocalizedTextAsJson(() =>
            // forUpdate to make sure to fetch the text from the database and not from memory
            context.read(TestLocalizedText, { name: scenarioName }, { forUpdate: true }),
        );
    }

    it('Import localized text (scenario 1)', async () => {
        await Test.withContext(
            async context => {
                // Tenant language = fr
                await resetTenantLocales([
                    { id: 'fr-FR', isLanguageMasterLocale: true },
                    { id: 'en-US', isLanguageMasterLocale: true },
                    { id: 'de-DE', isLanguageMasterLocale: true },
                ]);
                await context.withLocalizedTextAsJson(async () => {
                    // In import file Description, Description (en-US)
                    // Create in table Description (fr), Description (en-US), Description (en)
                    const insertedLocalizedText = await importScenarioNotEmpty(
                        context,
                        './test/fixtures/csv/localized-string/import-insert-scenario-1.csv',
                        {
                            doInsert: true,
                        },
                    );
                    assert.deepEqual(JSON.parse(await insertedLocalizedText.description), {
                        base: 'ceci est un texte localisé',
                        'en-US': 'this is a localized text',
                        en: 'this is a localized text',
                    });

                    // Update in the table Description (fr), Description (en-US)
                    const updatedLocalizedText = await importScenarioNotEmpty(
                        context,
                        './test/fixtures/csv/localized-string/import-update-scenario-1.csv',
                        {
                            doUpdate: true,
                        },
                    );
                    assert.deepEqual(JSON.parse(await updatedLocalizedText.description), {
                        base: 'ceci est un texte localisé (updated)',
                        'en-US': 'this is a localized text (updated)',
                        en: 'this is a localized text',
                    });
                });
            },
            {
                locale: 'fr-FR',
            },
        );
    });

    it('Import localized text (scenario 2)', async () => {
        await Test.withContext(
            async context => {
                await resetTenantLocales([
                    { id: 'fr-FR', isLanguageMasterLocale: true },
                    { id: 'en-US', isLanguageMasterLocale: true },
                    { id: 'de-DE', isLanguageMasterLocale: true },
                ]);
                await context.withLocalizedTextAsJson(async () => {
                    // In import file Description (en-US)
                    // Create in table Description (en-US) Description (en) Description (fr)
                    const insertedLocalizedText = await importScenarioNotEmpty(
                        context,
                        './test/fixtures/csv/localized-string/import-insert-scenario-2.csv',
                        {
                            doInsert: true,
                        },
                    );
                    assert.deepEqual(JSON.parse(await insertedLocalizedText.description), {
                        'en-US': 'this is a localized text',
                        en: 'this is a localized text',
                        base: 'this is a localized text',
                    });

                    // Update in the table Description (en-US)
                    const updatedLocalizedText = await importScenarioNotEmpty(
                        context,
                        './test/fixtures/csv/localized-string/import-update-scenario-2.csv',
                        {
                            doUpdate: true,
                        },
                    );
                    assert.deepEqual(JSON.parse(await updatedLocalizedText.description), {
                        'en-US': 'this is a localized text (updated)',
                        en: 'this is a localized text',
                        base: 'this is a localized text',
                    });
                });
            },
            {
                locale: 'fr-FR',
            },
        );
    });

    it('Import localized text (scenario 3)', async () => {
        await Test.withContext(
            async context => {
                await resetTenantLocales([
                    { id: 'fr-FR', isLanguageMasterLocale: true },
                    { id: 'en-US', isLanguageMasterLocale: true },
                    { id: 'de-DE', isLanguageMasterLocale: true },
                ]);
                await context.withLocalizedTextAsJson(async () => {
                    // In import file Description
                    // Create in table Description (fr)
                    const insertedLocalizedText = await importScenarioNotEmpty(
                        context,
                        './test/fixtures/csv/localized-string/import-insert-scenario-3.csv',
                        {
                            doInsert: true,
                        },
                    );
                    assert.deepEqual(JSON.parse(await insertedLocalizedText.description), {
                        base: 'ceci est un texte localisé',
                    });

                    // Update in the table Description (fr)
                    const updatedLocalizedText = await importScenarioNotEmpty(
                        context,
                        './test/fixtures/csv/localized-string/import-update-scenario-3.csv',
                        {
                            doUpdate: true,
                        },
                    );
                    assert.deepEqual(JSON.parse(await updatedLocalizedText.description), {
                        base: 'ceci est un texte localisé (updated)',
                    });
                });
            },
            {
                locale: 'fr-FR',
            },
        );
    });

    it('Import localized text (scenario 4)', async () => {
        await Test.withContext(
            async context => {
                // Tenant language = fr
                // In import file Description (fr-FR)
                // Create in table Description (fr-FR) Description (fr)
                await resetTenantLocales([
                    { id: 'fr-FR', isLanguageMasterLocale: true },
                    { id: 'en-US', isLanguageMasterLocale: true },
                    { id: 'de-DE', isLanguageMasterLocale: true },
                ]);
                await context.withLocalizedTextAsJson(async () => {
                    const insertedLocalizedText = await importScenarioNotEmpty(
                        context,
                        './test/fixtures/csv/localized-string/import-insert-scenario-4.csv',
                        {
                            doInsert: true,
                        },
                    );
                    assert.deepEqual(JSON.parse(await insertedLocalizedText.description), {
                        'fr-FR': 'ceci est un texte localisé',
                        fr: 'ceci est un texte localisé',
                        base: 'ceci est un texte localisé',
                    });

                    // Update in the table Description (fr-FR)
                    const updatedLocalizedText = await importScenarioNotEmpty(
                        context,
                        './test/fixtures/csv/localized-string/import-update-scenario-4.csv',
                        {
                            doUpdate: true,
                        },
                    );
                    assert.deepEqual(JSON.parse(await updatedLocalizedText.description), {
                        'fr-FR': 'ceci est un texte localisé (updated)',
                        fr: 'ceci est un texte localisé',
                        base: 'ceci est un texte localisé',
                    });
                });
            },
            {
                locale: 'fr-FR',
            },
        );
    });

    it('Import localized text (scenario 5)', async () => {
        await Test.withContext(
            async context => {
                // Tenant language = fr
                // In import file Description (fr-FR)
                // Create in table Description (fr-FR) Description (fr)
                await context.withLocalizedTextAsJson(async () => {
                    await resetTenantLocales([
                        { id: 'fr-FR', isLanguageMasterLocale: true },
                        { id: 'en-US', isLanguageMasterLocale: true },
                        { id: 'de-DE', isLanguageMasterLocale: true },
                    ]);
                    const insertedLocalizedText = await importScenarioNotEmpty(
                        context,
                        './test/fixtures/csv/localized-string/import-insert-scenario-5.csv',
                        {
                            doInsert: true,
                        },
                    );
                    assert.deepEqual(JSON.parse(await insertedLocalizedText.description), {
                        'en-UK': 'I wish I had a biscuit',
                        en: 'I wish I had a biscuit',
                        'en-US': 'I want a cookie',
                        base: 'I wish I had a biscuit',
                    });

                    // Description (en-UK) Description (en-US)
                    const updatedLocalizedText = await importScenarioNotEmpty(
                        context,
                        './test/fixtures/csv/localized-string/import-update-scenario-5.csv',
                        {
                            doUpdate: true,
                        },
                    );
                    assert.deepEqual(JSON.parse(await updatedLocalizedText.description), {
                        'en-UK': 'I wish I had a biscuit (updated)',
                        en: 'I wish I had a biscuit',
                        'en-US': 'I want a cookie (updated)',
                        base: 'I wish I had a biscuit',
                    });
                });
            },
            {
                locale: 'fr-FR',
            },
        );
    });

    it('Import localized text (scenario 6)', async () => {
        await Test.withContext(
            async context => {
                // Tenant language = fr
                // In import file Description (fr-FR)
                // Create in table Description (fr-FR) Description (fr)
                await resetTenantLocales([
                    { id: 'fr-FR', isLanguageMasterLocale: true },
                    { id: 'en-US', isLanguageMasterLocale: true },
                    { id: 'de-DE', isLanguageMasterLocale: true },
                ]);
                await context.withLocalizedTextAsJson(async () => {
                    const insertedLocalizedText = await importScenarioNotEmpty(
                        context,
                        './test/fixtures/csv/localized-string/import-insert-scenario-6.csv',
                        {
                            doInsert: true,
                        },
                    );
                    assert.deepEqual(JSON.parse(await insertedLocalizedText.description), {
                        en: 'I want a cookie',
                        base: 'I want a cookie',
                    });

                    // Description (en) Description (fr)
                    const updatedLocalizedText = await importScenarioNotEmpty(
                        context,
                        './test/fixtures/csv/localized-string/import-update-scenario-6.csv',
                        {
                            doUpdate: true,
                        },
                    );

                    assert.deepEqual(JSON.parse(await updatedLocalizedText.description), {
                        en: 'I want a cookie (updated)',
                        base: 'I want a cookie',
                    });
                });
            },
            {
                locale: 'fr-FR',
            },
        );
    });

    it('Import localized text (scenario 7)', async () => {
        await Test.withContext(
            async context => {
                // Tenant language = fr
                // In import file Description (fr-FR)
                // Create in table Description (fr-FR) Description (fr)
                await resetTenantLocales([
                    { id: 'fr-FR', isLanguageMasterLocale: true },
                    { id: 'en-US', isLanguageMasterLocale: true },
                    { id: 'de-DE', isLanguageMasterLocale: true },
                ]);
                await context.withLocalizedTextAsJson(async () => {
                    const insertedLocalizedText = await importScenarioNotEmpty(
                        context,
                        './test/fixtures/csv/localized-string/import-insert-scenario-7.csv',
                        {
                            doInsert: true,
                        },
                    );
                    assert.deepEqual(JSON.parse(await insertedLocalizedText.description), {
                        base: 'ceci est un texte localisé',
                        'en-US': 'this is a localized text',
                        en: 'this is a localized text',
                    });

                    // Description (en) Description (fr)
                    const updatedLocalizedText = await importScenarioNotEmpty(
                        context,
                        './test/fixtures/csv/localized-string/import-update-scenario-7.csv',
                        {
                            doUpdate: true,
                        },
                    );

                    assert.deepEqual(JSON.parse(await updatedLocalizedText.description), {
                        base: 'ceci est un texte localisé (updated)',
                    });
                });
            },
            {
                locale: 'fr-FR',
            },
        );
    });

    it('Import localized text (scenario 8)', async () => {
        await Test.withContext(
            async context => {
                // Tenant language = fr
                // In import file Description (fr-FR)
                // Create in table Description (fr-FR) Description (fr)
                await resetTenantLocales([
                    { id: 'fr-FR', isLanguageMasterLocale: true },
                    { id: 'en-US', isLanguageMasterLocale: true },
                    { id: 'de-DE', isLanguageMasterLocale: true },
                ]);
                await context.withLocalizedTextAsJson(async () => {
                    const insertedLocalizedText = await importScenarioNotEmpty(
                        context,
                        './test/fixtures/csv/localized-string/import-insert-scenario-8.csv',
                        {
                            doInsert: true,
                        },
                    );
                    assert.deepEqual(JSON.parse(await insertedLocalizedText.description), {
                        base: 'ceci est un texte localisé',
                        'en-US': 'this is a localized text',
                        en: 'this is a localized text',
                    });
                    const scenario = './test/fixtures/csv/localized-string/import-update-scenario-8.csv';

                    const scenarioId = lodash.camelCase(path.basename(scenario, '.csv'));
                    const templateTestLocalizedTextNotEmpty = {
                        id: scenarioId,
                        name: scenarioId,
                        code: scenarioId,
                        nodeName: 'TestLocalizedTextNotEmpty',
                        description: 'description',
                        csvTemplate: {
                            data: [
                                { _id: 0, path: '!name', dataType: 'string', description: '!name', locale: '' },
                                {
                                    _id: 10,
                                    path: 'description',
                                    dataType: 'string',
                                    description: 'description',
                                    locale: '',
                                },
                                {
                                    _id: 20,
                                    path: 'description',
                                    dataType: 'string',
                                    description: 'description',
                                    locale: 'en-US',
                                },
                            ],
                        },
                    };

                    const importExportTemplate = await createImportExportTemplate(
                        context,
                        templateTestLocalizedTextNotEmpty,
                    );
                    const stream = fs.createReadStream(scenario, 'utf8');
                    const user = await context.user;
                    const uploadedFile = await uploadTestFile(scenario, user?.email);
                    let importResult = await ImportExportTemplate.createResult(
                        context,
                        importExportTemplate,
                        true,
                        true,
                        false,
                        1,
                        'inProgress',
                        String(uploadedFile.uploadedFileId),
                    );

                    importResult = await ImportExportTemplate.importCsv(context, context.batch, stream, importResult);

                    assert.strictEqual(await importResult.numberOfRowsInError, 1);
                    assert.strictEqual(
                        (await importResult.rowsInError).value,
                        '"!name";"description";"_error"\n"importScenario1";;"description: empty value is not allowed for locale ""base"""\n',
                    );
                });
            },
            {
                locale: 'fr-FR',
            },
        );
    });

    it('Import localized text (scenario 8) notEmpty false', async () => {
        await Test.withContext(
            async context => {
                // Tenant language = fr
                // In import file Description (fr-FR)
                // Create in table Description (fr-FR) Description (fr)
                await resetTenantLocales([
                    { id: 'fr-FR', isLanguageMasterLocale: true },
                    { id: 'en-US', isLanguageMasterLocale: true },
                    { id: 'de-DE', isLanguageMasterLocale: true },
                ]);
                await context.withLocalizedTextAsJson(async () => {
                    const insertedLocalizedText = await importScenario(
                        context,
                        './test/fixtures/csv/localized-string/import-insert-scenario-8.csv',
                        {
                            doInsert: true,
                        },
                    );
                    assert.deepEqual(JSON.parse(await insertedLocalizedText.description), {
                        base: 'ceci est un texte localisé',
                        'en-US': 'this is a localized text',
                        en: 'this is a localized text',
                    });

                    // Description (en) Description (fr)
                    const updatedLocalizedText = await importScenario(
                        context,
                        './test/fixtures/csv/localized-string/import-update-scenario-8.csv',
                        {
                            doUpdate: true,
                        },
                    );

                    assert.deepEqual(JSON.parse(await updatedLocalizedText.description), {
                        base: '',
                    });
                });
            },
            {
                locale: 'fr-FR',
            },
        );
    });

    it('Import localized text (scenario 9)', async () => {
        await Test.withContext(
            async context => {
                // Tenant language = fr
                // In import file Description (fr-FR)
                // Create in table Description (fr-FR) Description (fr)
                await resetTenantLocales([
                    { id: 'fr-FR', isLanguageMasterLocale: true },
                    { id: 'en-US', isLanguageMasterLocale: true },
                    { id: 'de-DE', isLanguageMasterLocale: true },
                ]);
                await context.withLocalizedTextAsJson(async () => {
                    const insertedLocalizedText = await importScenarioNotEmpty(
                        context,
                        './test/fixtures/csv/localized-string/import-insert-scenario-9.csv',
                        {
                            doInsert: true,
                        },
                    );
                    assert.deepEqual(JSON.parse(await insertedLocalizedText.description), {
                        base: 'ceci est un texte localisé',
                        'en-US': 'this is a localized text',
                        en: 'this is a localized text',
                    });

                    // Description (en) Description (fr)
                    const updatedLocalizedText = await importScenarioNotEmpty(
                        context,
                        './test/fixtures/csv/localized-string/import-update-scenario-9.csv',
                        {
                            doUpdate: true,
                        },
                    );

                    assert.deepEqual(JSON.parse(await updatedLocalizedText.description), {
                        base: 'ceci est un texte localisé',
                        'en-US': '',
                        en: 'this is a localized text',
                    });
                });
            },
            {
                locale: 'fr-FR',
            },
        );
    });

    it('Import localized text (scenario 10)', async () => {
        await Test.withContext(
            async context => {
                // Tenant language = fr
                // In import file Description (fr-FR)
                // Create in table Description (fr-FR) Description (fr)
                await resetTenantLocales([
                    { id: 'fr-FR', isLanguageMasterLocale: true },
                    { id: 'en-US', isLanguageMasterLocale: true },
                    { id: 'de-DE', isLanguageMasterLocale: true },
                ]);
                await context.withLocalizedTextAsJson(async () => {
                    const insertedLocalizedText = await importScenarioNotEmpty(
                        context,
                        './test/fixtures/csv/localized-string/import-insert-scenario-10.csv',
                        {
                            doInsert: true,
                        },
                    );
                    assert.deepEqual(JSON.parse(await insertedLocalizedText.description), {
                        base: 'ceci est un texte localisé',
                        'en-US': 'this is a localized text',
                        en: 'this is a localized text',
                    });

                    const scenario = './test/fixtures/csv/localized-string/import-update-scenario-10.csv';

                    const scenarioId = lodash.camelCase(path.basename(scenario, '.csv'));
                    const templateTestLocalizedTextNotEmpty = {
                        id: scenarioId,
                        name: scenarioId,
                        code: scenarioId,
                        nodeName: 'TestLocalizedTextNotEmpty',
                        description: 'description',
                        csvTemplate: {
                            data: [
                                { _id: 0, path: '!name', dataType: 'string', description: '!name', locale: '' },
                                {
                                    _id: 10,
                                    path: 'description',
                                    dataType: 'string',
                                    description: 'description',
                                    locale: '',
                                },
                                {
                                    _id: 20,
                                    path: 'description',
                                    dataType: 'string',
                                    description: 'description',
                                    locale: 'en-US',
                                },
                            ],
                        },
                    };

                    const importExportTemplate = await createImportExportTemplate(
                        context,
                        templateTestLocalizedTextNotEmpty,
                    );
                    const stream = fs.createReadStream(scenario, 'utf8');
                    const user = await context.user;
                    const uploadedFile = await uploadTestFile(scenario, user?.email);
                    let importResult = await ImportExportTemplate.createResult(
                        context,
                        importExportTemplate,
                        true,
                        true,
                        false,
                        1,
                        'inProgress',
                        String(uploadedFile.uploadedFileId),
                    );

                    importResult = await ImportExportTemplate.importCsv(context, context.batch, stream, importResult);

                    assert.strictEqual(await importResult.numberOfRowsInError, 1);
                    assert.strictEqual(
                        (await importResult.rowsInError).value,
                        '"!name";"description";"description(en-US)";"_error"\n"importScenario1";;"this is a localized text (updated)";"description: empty value is not allowed for locale ""base"""\n',
                    );
                });
            },
            {
                locale: 'fr-FR',
            },
        );
    });

    it('Import localized text (scenario 11)', async () => {
        await Test.withContext(
            async context => {
                // Tenant language = fr
                // In import file Description (fr-FR)
                // Create in table Description (fr-FR) Description (fr)
                await resetTenantLocales([
                    { id: 'fr-FR', isLanguageMasterLocale: true },
                    { id: 'en-US', isLanguageMasterLocale: true },
                    { id: 'de-DE', isLanguageMasterLocale: true },
                ]);
                await context.withLocalizedTextAsJson(async () => {
                    const scenario = './test/fixtures/csv/localized-string/import-insert-scenario-11.csv';

                    const scenarioId = lodash.camelCase(path.basename(scenario, '.csv'));
                    const templateTestLocalizedTextNotEmpty = {
                        id: scenarioId,
                        name: scenarioId,
                        code: scenarioId,
                        nodeName: 'TestLocalizedTextNotEmpty',
                        description: 'description',
                        csvTemplate: {
                            data: [
                                { _id: 0, path: '!name', dataType: 'string', description: '!name', locale: '' },
                                {
                                    _id: 10,
                                    path: 'description',
                                    dataType: 'string',
                                    description: 'description',
                                    locale: '',
                                },
                                {
                                    _id: 20,
                                    path: 'description',
                                    dataType: 'string',
                                    description: 'description',
                                    locale: 'en-US',
                                },
                            ],
                        },
                    };

                    const importExportTemplate = await createImportExportTemplate(
                        context,
                        templateTestLocalizedTextNotEmpty,
                    );
                    const stream = fs.createReadStream(scenario, 'utf8');
                    const user = await context.user;
                    const uploadedFile = await uploadTestFile(scenario, user?.email);
                    let importResult = await ImportExportTemplate.createResult(
                        context,
                        importExportTemplate,
                        true,
                        false,
                        false,
                        1,
                        'inProgress',
                        String(uploadedFile.uploadedFileId),
                    );

                    importResult = await ImportExportTemplate.importCsv(context, context.batch, stream, importResult);

                    assert.strictEqual(await importResult.numberOfRowsInError, 1);
                    assert.strictEqual(
                        (await importResult.rowsInError).value,
                        '"!name";"description";"description(en-US)";"_error"\n"importScenario1";;;"description: empty value not allowed"\n',
                    );
                });
            },
            {
                locale: 'fr-FR',
            },
        );
    });
    it('Import localized text (scenario 12)', async () => {
        await Test.withContext(
            async context => {
                // Tenant language = fr
                // In import file Description (fr-FR)
                // Create in table Description (fr-FR) Description (fr)
                await resetTenantLocales([
                    { id: 'fr-FR', isLanguageMasterLocale: true },
                    { id: 'en-US', isLanguageMasterLocale: true },
                    { id: 'de-DE', isLanguageMasterLocale: true },
                ]);
                await context.withLocalizedTextAsJson(async () => {
                    const insertedLocalizedText = await importScenarioNotEmpty(
                        context,
                        './test/fixtures/csv/localized-string/import-insert-scenario-12.csv',
                        {
                            doInsert: true,
                        },
                    );
                    assert.deepEqual(JSON.parse(await insertedLocalizedText.description), {
                        base: 'test',
                        en: 'test',
                        'en-US': 'test',
                    });
                });
            },
            {
                locale: 'fr-FR',
            },
        );
    });
    it('Import localized text (scenario 13)', async () => {
        await Test.withContext(
            async context => {
                // Tenant language = fr
                // In import file Description (fr-FR)
                // Create in table Description (fr-FR) Description (fr)
                await resetTenantLocales([
                    { id: 'fr-FR', isLanguageMasterLocale: true },
                    { id: 'en-US', isLanguageMasterLocale: true },
                    { id: 'de-DE', isLanguageMasterLocale: true },
                ]);
                await context.withLocalizedTextAsJson(async () => {
                    const scenario = './test/fixtures/csv/localized-string/import-insert-scenario-13.csv';

                    const scenarioId = lodash.camelCase(path.basename(scenario, '.csv'));
                    const templateTestLocalizedTextNotEmpty = {
                        id: scenarioId,
                        name: scenarioId,
                        code: scenarioId,
                        nodeName: 'TestLocalizedTextNotEmpty',
                        description: 'description',
                        csvTemplate: {
                            data: [
                                { _id: 0, path: '!name', dataType: 'string', description: '!name', locale: '' },
                                {
                                    _id: 10,
                                    path: 'description',
                                    dataType: 'string',
                                    description: 'description',
                                    locale: '',
                                },
                                {
                                    _id: 20,
                                    path: 'description',
                                    dataType: 'string',
                                    description: 'description',
                                    locale: 'en-US',
                                },
                            ],
                        },
                    };

                    const importExportTemplate = await createImportExportTemplate(
                        context,
                        templateTestLocalizedTextNotEmpty,
                    );
                    const stream = fs.createReadStream(scenario, 'utf8');
                    const user = await context.user;
                    const uploadedFile = await uploadTestFile(scenario, user?.email);
                    let importResult = await ImportExportTemplate.createResult(
                        context,
                        importExportTemplate,
                        true,
                        false,
                        false,
                        1,
                        'inProgress',
                        String(uploadedFile.uploadedFileId),
                    );

                    importResult = await ImportExportTemplate.importCsv(context, context.batch, stream, importResult);

                    assert.strictEqual(await importResult.numberOfRowsInError, 1);
                    assert.strictEqual(
                        (await importResult.rowsInError).value,
                        '"!name";"_error"\n"importScenario1";"description: string cannot be empty"\n',
                    );
                });
            },
            {
                locale: 'fr-FR',
            },
        );
    });

    it('Import localized text (scenario 10) notEmpty false', async () => {
        await Test.withContext(
            async context => {
                // Tenant language = fr
                // In import file Description (fr-FR)
                // Create in table Description (fr-FR) Description (fr)
                await resetTenantLocales([
                    { id: 'fr-FR', isLanguageMasterLocale: true },
                    { id: 'en-US', isLanguageMasterLocale: true },
                    { id: 'de-DE', isLanguageMasterLocale: true },
                ]);
                await context.withLocalizedTextAsJson(async () => {
                    const insertedLocalizedText = await importScenario(
                        context,
                        './test/fixtures/csv/localized-string/import-insert-scenario-10.csv',
                        {
                            doInsert: true,
                        },
                    );
                    assert.deepEqual(JSON.parse(await insertedLocalizedText.description), {
                        base: 'ceci est un texte localisé',
                        'en-US': 'this is a localized text',
                        en: 'this is a localized text',
                    });

                    // Description (en) Description (fr)
                    const updatedLocalizedText = await importScenario(
                        context,
                        './test/fixtures/csv/localized-string/import-update-scenario-10.csv',
                        {
                            doUpdate: true,
                        },
                    );

                    assert.deepEqual(JSON.parse(await updatedLocalizedText.description), {
                        base: '',
                        'en-US': 'this is a localized text (updated)',
                        en: 'this is a localized text',
                    });
                });
            },
            {
                locale: 'fr-FR',
            },
        );
    });

    it('Import localized text (scenario 11) notEmpty false', async () => {
        await Test.withContext(
            async context => {
                // Tenant language = fr
                // In import file Description (fr-FR)
                // Create in table Description (fr-FR) Description (fr)
                await resetTenantLocales([
                    { id: 'fr-FR', isLanguageMasterLocale: true },
                    { id: 'en-US', isLanguageMasterLocale: true },
                    { id: 'de-DE', isLanguageMasterLocale: true },
                ]);
                await context.withLocalizedTextAsJson(async () => {
                    const insertedLocalizedText = await importScenario(
                        context,
                        './test/fixtures/csv/localized-string/import-insert-scenario-11.csv',
                        {
                            doInsert: true,
                        },
                    );
                    assert.deepEqual(JSON.parse(await insertedLocalizedText.description), {});
                });
            },
            {
                locale: 'fr-FR',
            },
        );
    });
    it('Import localized text (scenario 12) notEmpty false', async () => {
        await Test.withContext(
            async context => {
                // Tenant language = fr
                // In import file Description (fr-FR)
                // Create in table Description (fr-FR) Description (fr)
                await resetTenantLocales([
                    { id: 'fr-FR', isLanguageMasterLocale: true },
                    { id: 'en-US', isLanguageMasterLocale: true },
                    { id: 'de-DE', isLanguageMasterLocale: true },
                ]);
                await context.withLocalizedTextAsJson(async () => {
                    const insertedLocalizedText = await importScenario(
                        context,
                        './test/fixtures/csv/localized-string/import-insert-scenario-12.csv',
                        {
                            doInsert: true,
                        },
                    );
                    assert.deepEqual(JSON.parse(await insertedLocalizedText.description), {
                        base: 'test',
                        'en-US': 'test',
                        en: 'test',
                    });
                });
            },
            {
                locale: 'fr-FR',
            },
        );
    });
    it('Import localized text (scenario 13) notEmpty false', async () => {
        await Test.withContext(
            async context => {
                // Tenant language = fr
                // In import file Description (fr-FR)
                // Create in table Description (fr-FR) Description (fr)
                await resetTenantLocales([
                    { id: 'fr-FR', isLanguageMasterLocale: true },
                    { id: 'en-US', isLanguageMasterLocale: true },
                    { id: 'de-DE', isLanguageMasterLocale: true },
                ]);
                await context.withLocalizedTextAsJson(async () => {
                    const insertedLocalizedText = await importScenario(
                        context,
                        './test/fixtures/csv/localized-string/import-insert-scenario-13.csv',
                        {
                            doInsert: true,
                        },
                    );
                    assert.deepEqual(JSON.parse(await insertedLocalizedText.description), {});
                });
            },
            {
                locale: 'fr-FR',
            },
        );
    });
});

describe('Import custom field values', () => {
    async function createCustomFields(context: Context, isMandatoryCustomFields?: boolean) {
        const nodeMetaDataArray = await context
            .query(xtremMetaData.nodes.MetaNodeFactory, { filter: { name: 'TestCustomField' } })
            .toArray();
        const nodeMetaData = nodeMetaDataArray[0];

        const metaNodePropertyArray = await context
            .query(xtremMetaData.nodes.MetaNodeProperty, { filter: { factory: nodeMetaData, name: 'name' } })
            .toArray();
        const metaNodeProperty = metaNodePropertyArray[0];

        // create custom fields

        // string - text field
        let inputCustomField = {
            node: nodeMetaData._id,
            dataType: 'string',
            anchorPosition: 'before',
            anchorProperty: metaNodeProperty._id,
            destinationTypes: ['page'],
            componentType: 'textField',
            name: 'customString',
            componentAttributes: { isMandatory: isMandatoryCustomFields },
        } as NodeCreateData<xtremCustomization.nodes.CustomField>;

        let customField = await context.create(xtremCustomization.nodes.CustomField, inputCustomField);
        await customField.$.save();

        // boolean - checkbox
        inputCustomField = {
            node: nodeMetaData,
            dataType: 'boolean',
            anchorPosition: 'before',
            anchorProperty: metaNodeProperty,
            destinationTypes: ['page'],
            componentType: 'checkboxField',
            name: 'customBoolean',
            componentAttributes: { isMandatory: isMandatoryCustomFields },
        } as NodeCreateData<xtremCustomization.nodes.CustomField>;

        customField = await context.create(xtremCustomization.nodes.CustomField, inputCustomField);
        await customField.$.save();

        // decimal - numericField
        inputCustomField = {
            node: nodeMetaData,
            dataType: 'decimal',
            anchorPosition: 'before',
            anchorProperty: metaNodeProperty,
            destinationTypes: ['page'],
            componentType: 'numericField',
            name: 'customDecimalNumeric',
            componentAttributes: { isMandatory: isMandatoryCustomFields },
        } as NodeCreateData<xtremCustomization.nodes.CustomField>;

        customField = await context.create(xtremCustomization.nodes.CustomField, inputCustomField);
        await customField.$.save();

        // integer - numericField
        inputCustomField = {
            node: nodeMetaData,
            dataType: 'integer',
            anchorPosition: 'before',
            anchorProperty: metaNodeProperty,
            destinationTypes: ['page'],
            componentType: 'numericField',
            name: 'customIntegerNumeric',
            componentAttributes: { isMandatory: isMandatoryCustomFields },
        } as NodeCreateData<xtremCustomization.nodes.CustomField>;

        customField = await context.create(xtremCustomization.nodes.CustomField, inputCustomField);
        await customField.$.save();

        // Double - numericField
        inputCustomField = {
            node: nodeMetaData,
            dataType: 'double',
            anchorPosition: 'before',
            anchorProperty: metaNodeProperty,
            destinationTypes: ['page'],
            componentType: 'numericField',
            name: 'customDoubleNumeric',
            componentAttributes: { isMandatory: isMandatoryCustomFields },
        } as NodeCreateData<xtremCustomization.nodes.CustomField>;

        customField = await context.create(xtremCustomization.nodes.CustomField, inputCustomField);
        await customField.$.save();

        // Float - numericField
        inputCustomField = {
            node: nodeMetaData,
            dataType: 'float',
            anchorPosition: 'before',
            anchorProperty: metaNodeProperty,
            destinationTypes: ['page'],
            componentType: 'numericField',
            name: 'customFloatNumeric',
            componentAttributes: { isMandatory: isMandatoryCustomFields },
        } as NodeCreateData<xtremCustomization.nodes.CustomField>;

        customField = await context.create(xtremCustomization.nodes.CustomField, inputCustomField);
        await customField.$.save();
        // date - dateField
        inputCustomField = {
            node: nodeMetaData,
            dataType: 'date',
            anchorPosition: 'after',
            anchorProperty: metaNodeProperty,
            destinationTypes: ['page'],
            componentType: 'dateField',
            name: 'customDate',
            componentAttributes: { isMandatory: isMandatoryCustomFields },
        } as NodeCreateData<xtremCustomization.nodes.CustomField>;

        customField = await context.create(xtremCustomization.nodes.CustomField, inputCustomField);
        await customField.$.save();

        // enum - selectField
        inputCustomField = {
            node: nodeMetaData,
            dataType: 'enum',
            anchorPosition: 'after',
            anchorProperty: metaNodeProperty,
            destinationTypes: ['page'],
            componentType: 'selectField',
            name: 'customSelect',
            componentAttributes: {
                options: [
                    { _id: 1, isActive: true, displayedName: 'displayedName1', technicalName: 'technicalName1' },
                    { _id: 2, isActive: true, displayedName: 'displayedName2', technicalName: 'technicalName2' },
                ],
                isMandatory: isMandatoryCustomFields,
            },
        } as NodeCreateData<xtremCustomization.nodes.CustomField>;

        customField = await context.create(xtremCustomization.nodes.CustomField, inputCustomField);
        await customField.$.save();

        // to enable when the component is ready
        // enumArray - multiDropdownField
        // inputCustomField = {
        //     node: nodeMetaData,
        //     dataType: 'enumArray',
        //     anchorPosition: 'before',
        //     anchorProperty: metaNodeProperty,
        //     destinationTypes: ['page'],
        //     componentType: 'multiDropdownField',
        //     name: 'customMultiDropDown',
        //     componentAttributes: {
        //         options: [
        //             { _id: 3, isActive: true, displayedName: 'displayedName3', technicalName: 'technicalName3' },
        //             { _id: 4, isActive: true, displayedName: 'displayedName4', technicalName: 'technicalName4' },
        //         ],
        //     },
        // } as NodeCreateData<xtremCustomization.nodes.CustomField>;

        // customField = await context.create(xtremCustomization.nodes.CustomField, inputCustomField);
        // await customField.$.save();
    }

    function createTemplate(context: Context, scenarioId: string, isMandatoryCustomFields?: boolean) {
        const templateTestCustomFields = {
            id: scenarioId,
            name: scenarioId,
            code: scenarioId,
            nodeName: 'TestCustomField',
            description: 'description',
            csvTemplate: {
                data: [
                    { _id: 0, path: '!name', dataType: 'string', description: '!name', locale: '', isCustom: false },
                    {
                        _id: 10,
                        path: isMandatoryCustomFields ? '*_customData(customString)' : '_customData(customString)',
                        dataType: 'string',
                        description: 'customString',
                        locale: '',
                        isCustom: true,
                    },
                    {
                        _id: 20,
                        path: isMandatoryCustomFields ? '*_customData(customBoolean)' : '_customData(customBoolean)',
                        dataType: 'boolean',
                        description: 'customBoolean',
                        locale: '',
                        isCustom: true,
                    },
                    {
                        _id: 30,
                        path: isMandatoryCustomFields ? '*_customData(customDate)' : '_customData(customDate)',
                        dataType: 'date',
                        description: 'customDate',
                        locale: '',
                        isCustom: true,
                    },
                    {
                        _id: 40,
                        path: isMandatoryCustomFields
                            ? '*_customData(customDecimalNumeric)'
                            : '_customData(customDecimalNumeric)',
                        dataType: 'decimal',
                        description: 'customDecimalNumeric',
                        locale: '',
                        isCustom: true,
                    },
                    // to enable when the component is ready
                    // {
                    //     _id: 50,
                    //     path: '_customData(customMultiDropDown)',
                    //     dataType: 'enumArray',
                    //     description: 'customMultiDropDown',
                    //     locale: '',
                    //     isCustom: true,
                    // },
                    {
                        _id: 60,
                        path: isMandatoryCustomFields ? '*_customData(customSelect)' : '_customData(customSelect)',
                        dataType: 'enum',
                        description: 'customSelect',
                        locale: '',
                        isCustom: true,
                    },
                    {
                        _id: 70,
                        path: isMandatoryCustomFields
                            ? '*_customData(customIntegerNumeric)'
                            : '_customData(customIntegerNumeric)',
                        dataType: 'integer',
                        description: 'customIntegerNumeric',
                        locale: '',
                        isCustom: true,
                    },
                    {
                        _id: 80,
                        path: isMandatoryCustomFields
                            ? '*_customData(customFloatNumeric)'
                            : '_customData(customFloatNumeric)',
                        dataType: 'float',
                        description: 'customFloatNumeric',
                        locale: '',
                        isCustom: true,
                    },
                    {
                        _id: 90,
                        path: isMandatoryCustomFields
                            ? '*_customData(customDoubleNumeric)'
                            : '_customData(customDoubleNumeric)',
                        dataType: 'double',
                        description: 'customDoubleNumeric',
                        locale: '',
                        isCustom: true,
                    },
                ],
            },
        };

        return createImportExportTemplate(context, templateTestCustomFields);
    }

    async function createResult(
        context: Context,
        scenario: string,
        importExportTemplate: ImportExportTemplate,
        options: { doInsert?: boolean; doUpdate?: boolean },
    ) {
        const user = await context.user;
        const uploadedFile = await uploadTestFile(scenario, user?.email);
        return ImportExportTemplate.createResult(
            context,
            importExportTemplate,
            true,
            !!options?.doUpdate,
            false,
            1,
            'inProgress',
            String(uploadedFile.uploadedFileId),
        );
    }
    async function importScenario(
        context: Context,
        scenario: string,
        options: {
            doInsert?: boolean;
            doUpdate?: boolean;
            doCreateCustomFields?: boolean;
            isMandatoryCustomFields?: boolean;
            delimiter?: string;
        },
    ): Promise<TestCustomField> {
        const fileContents = fs.readFileSync(scenario, 'utf8');
        const lines = fileContents.split('\n');
        const scenarioName = lines[1].split(options.delimiter ?? defaultDelimiter)[0].replaceAll('"', '');
        if (options.doCreateCustomFields) {
            await context.runInWritableContext(async ctx => {
                await createCustomFields(ctx, options.isMandatoryCustomFields);
            });
        }
        const scenarioId = lodash.camelCase(path.basename(scenario, '.csv'));

        const stream = fs.createReadStream(scenario, 'utf8');
        let importExportTemplate = await context.tryRead(ImportExportTemplate, { id: scenarioId });
        if (!importExportTemplate) {
            await context.runInWritableContext(async ctx => {
                importExportTemplate = await createTemplate(ctx, scenarioId, options.isMandatoryCustomFields);
            });
        }
        let importResult = await createResult(context, scenario, importExportTemplate!, options);

        importResult = await ImportExportTemplate.importCsv(context, context.batch, stream, importResult);
        assert.equal(await importResult.rowsProcessed, 1);
        assert.equal(await importResult.numberOfRowsInError, 0);
        return context.withLocalizedTextAsJson(() =>
            // forUpdate to make sure to fetch the text from the database and not from memory
            context.runInWritableContext(ctx => {
                return ctx.read(TestCustomField, { name: scenarioName }, { forUpdate: true });
            }),
        );
    }

    it('Import custom data (ok) scenario 1', async () => {
        await Test.withReadonlyContext(
            async context => {
                const csvPathInsert = './test/fixtures/csv/custom-field/import-insert-scenario-1.csv';
                await context.withLocalizedTextAsJson(async () => {
                    // In import file with custom fields
                    const insertedCustomField = await importScenario(context, csvPathInsert, {
                        doInsert: true,
                        doCreateCustomFields: true,
                    });
                    assert.deepEqual(await insertedCustomField._customData, {
                        customBoolean: true,
                        customDate: '2023-07-26',
                        // to enable when the component is ready
                        // customMultiDropDown: '[technicalName3]',
                        customDecimalNumeric: '-3.14',
                        customDoubleNumeric: '1.234',
                        customFloatNumeric: '100.10',
                        customIntegerNumeric: '100',
                        customSelect: 'technicalName1',
                        customString: 'custom string',
                    });
                });
                const scenarioId = lodash.camelCase(path.basename(csvPathInsert, '.csv'));
                const template = await context.read(ImportExportTemplate, { id: scenarioId });

                const factory = context.application.getFactoryByName(await template.nodeName);

                const filterParsed = await PagingFilter.parseFilter(context, factory, '{}');

                await ImportExportTemplate.generateExportWithQueryReader(context, template, {
                    filter: filterParsed,
                });

                const uploadedFile = (
                    await context
                        .query(xtremUpload.nodes.UploadedFile, {
                            filter: { kind: 'upload' },
                            orderBy: { _id: -1 },
                            first: 1,
                        })
                        .toArray()
                )[0];
                const uploadUrl = await uploadedFile.uploadUrl;
                assert.notEqual(uploadUrl, '', 'UploadUrl could not be computed');
                const input = fs.readFileSync(csvPathInsert, 'utf8');
                const exported = fs.readFileSync(uploadUrl.replace('tenant-data/', '').replace('uploads/', ''), 'utf8');
                assert.equal(exported, input);
                await context.withLocalizedTextAsJson(async () => {
                    let csvPathUpdate = './test/fixtures/csv/custom-field/import-update-scenario-1-step1.csv';
                    // Update in the table Description (fr), Description (en-US)
                    let updatedLocalizedText = await importScenario(context, csvPathUpdate, {
                        doUpdate: true,
                        doCreateCustomFields: false,
                    });
                    assert.deepEqual(await updatedLocalizedText._customData, {
                        customBoolean: true,
                        customDate: '2023-07-26',
                        // to enable when the component is ready
                        // customMultiDropDown: '[technicalName3,technicalName4]',
                        customDecimalNumeric: '-3.14',
                        customDoubleNumeric: '1.234',
                        customFloatNumeric: '100.10',
                        customIntegerNumeric: '100',
                        customSelect: 'technicalName2',
                        customString: 'custom string',
                    });

                    csvPathUpdate = './test/fixtures/csv/custom-field/import-update-scenario-1-step2.csv';
                    // Update in the table Description (fr), Description (en-US)
                    updatedLocalizedText = await importScenario(context, csvPathUpdate, {
                        doUpdate: true,
                        doCreateCustomFields: false,
                    });
                    assert.deepEqual(await updatedLocalizedText._customData, {
                        customBoolean: false,
                        customDate: null,
                        // to enable when the component is ready
                        // customMultiDropDown: '[technicalName3,technicalName4]',
                        customDecimalNumeric: '0',
                        customDoubleNumeric: 0,
                        customFloatNumeric: 0,
                        customIntegerNumeric: 0,
                        customSelect: null,
                        customString: '',
                    });
                });
                await context.runInWritableContext(async ctx => {
                    await ctx.delete(TestCustomField, { name: 'importScenario1' });
                    await ctx.deleteMany(xtremCustomization.nodes.CustomField, {});
                });
            },
            { source: 'listener', testMode: true },
        );
    });

    it('Import custom data (ok) secnario 2', async () => {
        await Test.withReadonlyContext(
            async context => {
                const csvPathInsert = './test/fixtures/csv/custom-field/import-insert-scenario-2.csv';
                await context.withLocalizedTextAsJson(async () => {
                    // In import file with custom fields
                    const insertedCustomField = await importScenario(context, csvPathInsert, {
                        doInsert: true,
                        doCreateCustomFields: true,
                    });
                    assert.deepEqual(await insertedCustomField._customData, {});
                });
                const scenarioId = lodash.camelCase(path.basename(csvPathInsert, '.csv'));
                const template = await context.read(ImportExportTemplate, { id: scenarioId });

                const factory = context.application.getFactoryByName(await template.nodeName);

                const filterParsed = await PagingFilter.parseFilter(context, factory, '{}');

                await ImportExportTemplate.generateExportWithQueryReader(context, template, {
                    filter: filterParsed,
                });

                const uploadedFile = (
                    await context
                        .query(xtremUpload.nodes.UploadedFile, {
                            filter: { kind: 'upload' },
                            orderBy: { _id: -1 },
                            first: 1,
                        })
                        .toArray()
                )[0];
                const uploadUrl = await uploadedFile.uploadUrl;
                assert.notEqual(uploadUrl, '', 'UploadUrl could not be computed');
                const input = fs.readFileSync(csvPathInsert, 'utf8');
                const exported = fs.readFileSync(uploadUrl.replace('tenant-data/', '').replace('uploads/', ''), 'utf8');
                assert.equal(exported, input);

                const csvPathUpdate = './test/fixtures/csv/custom-field/import-update-scenario-2.csv';
                await context.withLocalizedTextAsJson(async () => {
                    // Update in the table Description (fr), Description (en-US)
                    const updatedLocalizedText = await importScenario(context, csvPathUpdate, {
                        doUpdate: true,
                        doCreateCustomFields: false,
                    });
                    assert.deepEqual(await updatedLocalizedText._customData, {
                        customBoolean: true,
                        customDate: '2023-07-26',
                        // to enable when the component is ready
                        // customMultiDropDown: '[technicalName3,technicalName4]',
                        customDecimalNumeric: '-3.14',
                        customDoubleNumeric: '1.234',
                        customFloatNumeric: '100.10',
                        customIntegerNumeric: '100',
                        customSelect: 'technicalName1',
                        customString: 'custom string',
                    });
                });
                await context.runInWritableContext(async ctx => {
                    await ctx.delete(TestCustomField, { name: 'importScenario1' });
                    await ctx.deleteMany(xtremCustomization.nodes.CustomField, {});
                    await ctx.delete(ImportResult, { importExportTemplate: template._id });
                    await ctx.delete(ImportExportTemplate, { _id: template._id });
                });
            },
            { source: 'listener', testMode: true },
        );
    });

    it('Import custom data (ok) scenario 1 with isMandatory true', () =>
        Test.withReadonlyContext(
            async context => {
                const csvPathInsert = './test/fixtures/csv/custom-field/import-insert-scenario-1-mandatory.csv';
                await context.withLocalizedTextAsJson(async () => {
                    // In import file with custom fields
                    const insertedCustomField = await importScenario(context, csvPathInsert, {
                        doInsert: true,
                        doCreateCustomFields: true,
                        isMandatoryCustomFields: true,
                    });
                    assert.deepEqual(await insertedCustomField._customData, {
                        customBoolean: true,
                        customDate: '2023-07-26',
                        // to enable when the component is ready
                        // customMultiDropDown: '[technicalName3]',
                        customDecimalNumeric: '-3.14',
                        customDoubleNumeric: '1.234',
                        customFloatNumeric: '100.10',
                        customIntegerNumeric: '100',
                        customSelect: 'technicalName1',
                        customString: 'custom string',
                    });
                });
                const scenarioId = lodash.camelCase(path.basename(csvPathInsert, '.csv'));
                const template = await context.read(ImportExportTemplate, { id: scenarioId });

                const factory = context.application.getFactoryByName(await template.nodeName);

                const filterParsed = await PagingFilter.parseFilter(context, factory, '{}');

                await ImportExportTemplate.generateExportWithQueryReader(context, template, {
                    filter: filterParsed,
                });

                const uploadedFile = (
                    await context
                        .query(xtremUpload.nodes.UploadedFile, {
                            filter: { kind: 'upload' },
                            orderBy: { _id: -1 },
                            first: 1,
                        })
                        .toArray()
                )[0];
                const uploadUrl = await uploadedFile.uploadUrl;
                assert.notEqual(uploadUrl, '', 'UploadUrl could not be computed');
                const input = fs.readFileSync(csvPathInsert, 'utf8');
                const exported = fs.readFileSync(uploadUrl.replace('tenant-data/', '').replace('uploads/', ''), 'utf8');
                assert.equal(exported, input);
                await context.runInWritableContext(async ctx => {
                    await ctx.delete(ImportResult, { importExportTemplate: template._id });
                    await ctx.delete(ImportExportTemplate, { _id: template._id });
                });
                await context.withLocalizedTextAsJson(async () => {
                    let csvPathUpdate = './test/fixtures/csv/custom-field/import-update-scenario-1-step1.csv';
                    // Update in the table Description (fr), Description (en-US)
                    let updatedLocalizedText = await importScenario(context, csvPathUpdate, {
                        doUpdate: true,
                        doCreateCustomFields: false,
                    });
                    assert.deepEqual(await updatedLocalizedText._customData, {
                        customBoolean: true,
                        customDate: '2023-07-26',
                        // to enable when the component is ready
                        // customMultiDropDown: '[technicalName3,technicalName4]',
                        customDecimalNumeric: '-3.14',
                        customDoubleNumeric: '1.234',
                        customFloatNumeric: '100.10',
                        customIntegerNumeric: '100',
                        customSelect: 'technicalName2',
                        customString: 'custom string',
                    });

                    csvPathUpdate = './test/fixtures/csv/custom-field/import-update-scenario-1-step2.csv';
                    // Update in the table Description (fr), Description (en-US)
                    updatedLocalizedText = await importScenario(context, csvPathUpdate, {
                        doUpdate: true,
                        doCreateCustomFields: false,
                    });
                    assert.deepEqual(await updatedLocalizedText._customData, {
                        customBoolean: false,
                        customDate: date.today().toString(),
                        // to enable when the component is ready
                        // customMultiDropDown: '[technicalName3,technicalName4]',
                        customDecimalNumeric: '0',
                        customDoubleNumeric: 0,
                        customFloatNumeric: 0,
                        customIntegerNumeric: 0,
                        customSelect: 1,
                        customString: '',
                    });
                });
                await context.runInWritableContext(async ctx => {
                    await ctx.delete(TestCustomField, { name: 'importScenario1' });
                    await ctx.deleteMany(xtremCustomization.nodes.CustomField, {});
                });
            },
            {
                source: 'listener',
                testMode: true,
            },
        ));

    it('Import custom data (ok) secnario 2 with isMandatory true', async () => {
        await Test.withContext(async context => {
            await context.withLocalizedTextAsJson(async () => {
                const scenario = './test/fixtures/csv/custom-field/import-insert-scenario-2-mandatory.csv';
                // In import file with custom fields
                const options = {
                    doInsert: true,
                    doCreateCustomFields: true,
                    isMandatoryCustomFields: true,
                };

                await createCustomFields(context, true);

                const scenarioId = lodash.camelCase(path.basename(scenario, '.csv'));
                const importExportTemplate = await createTemplate(context, scenarioId);

                const stream = fs.createReadStream(scenario, 'utf8');
                let importResult = await createResult(context, scenario, importExportTemplate, options);
                importResult = await ImportExportTemplate.importCsv(context, context.batch, stream, importResult);
                assert.strictEqual(await importResult.numberOfRowsInError, 1);
                const errorValue = (await importResult.rowsInError).toString();
                assert.equal(
                    errorValue.substring(errorValue.length - 44, errorValue.length - 2),
                    'Mandatory field customString value missing',
                );
            });
        });
    });

    it('throws if unknown custom field in csv', async () => {
        await Test.withContext(async context => {
            await context.withLocalizedTextAsJson(async () => {
                // In import file with custom fields
                const scenario = './test/fixtures/csv/custom-field/import-wrong-custom-field.csv';
                const options = {
                    doInsert: true,
                    doCreateCustomFields: true,
                };

                await createCustomFields(context);

                const scenarioId = lodash.camelCase(path.basename(scenario, '.csv'));
                const importExportTemplate = await createTemplate(context, scenarioId);

                const stream = fs.createReadStream(scenario, 'utf8');
                let importResult = await createResult(context, scenario, importExportTemplate, options);
                importResult = await ImportExportTemplate.importCsv(context, context.batch, stream, importResult);
                assert.strictEqual(
                    await importResult.generalError,
                    'Invalid custom field in column header: _customData(customMultiDropDown).',
                );
            });
        });
    });

    it('throws if deactivated custom field in csv', async () => {
        await Test.withContext(async context => {
            await context.withLocalizedTextAsJson(async () => {
                // In import file with custom fields
                const scenario = './test/fixtures/csv/custom-field/import-deactivated-custom-field.csv';
                const options = {
                    doInsert: true,
                    doCreateCustomFields: true,
                };

                await createCustomFields(context);

                const scenarioId = lodash.camelCase(path.basename(scenario, '.csv'));
                const importExportTemplate = await createTemplate(context, scenarioId);

                const stream = fs.createReadStream(scenario, 'utf8');
                let importResult = await createResult(context, scenario, importExportTemplate, options);

                const customSelectFields = await context
                    .query(xtremCustomization.nodes.CustomField, { filter: { name: 'customSelect' }, forUpdate: true })
                    .toArray();
                const customSelectField = customSelectFields[0];
                await customSelectField.$.set({ isActive: false });
                await customSelectField.$.save();

                importResult = await ImportExportTemplate.importCsv(context, context.batch, stream, importResult);

                assert.strictEqual(
                    await importResult.generalError,
                    'Invalid custom field in column header: _customData(customSelect).',
                );
            });
        });
    });

    it('Import custom data (ok) if deactivated field is in the template but not in the csv', async () => {
        await Test.withContext(async context => {
            await context.withLocalizedTextAsJson(async () => {
                // In import file with custom fields
                const scenario = './test/fixtures/csv/custom-field/import-no-deactivated-custom-field.csv';
                const options = {
                    doInsert: true,
                    doCreateCustomFields: true,
                };

                await createCustomFields(context);

                const scenarioId = lodash.camelCase(path.basename(scenario, '.csv'));
                const importExportTemplate = await createTemplate(context, scenarioId);

                const stream = fs.createReadStream(scenario, 'utf8');
                let importResult = await createResult(context, scenario, importExportTemplate, options);

                const customSelectFields = await context
                    .query(xtremCustomization.nodes.CustomField, { filter: { name: 'customSelect' }, forUpdate: true })
                    .toArray();
                const customSelectField = customSelectFields[0];
                await customSelectField.$.set({ isActive: false });
                await customSelectField.$.save();

                importResult = await ImportExportTemplate.importCsv(context, context.batch, stream, importResult);

                assert.strictEqual(await importResult.generalError, '');
                const insertedCustomField = await context.read(
                    TestCustomField,
                    { name: 'importScenario1' },
                    { forUpdate: true },
                );

                assert.deepEqual(await insertedCustomField._customData, {
                    customBoolean: true,
                    customDate: '2023-07-26',
                    customDecimalNumeric: '-3.14',
                    customDoubleNumeric: '1.234',
                    customFloatNumeric: '100.10',
                    customIntegerNumeric: '100',
                    customString: 'custom string',
                });
            });
        });
    });

    it('throws if wrong format value custom field in csv', async () => {
        await Test.withContext(async context => {
            await context.withLocalizedTextAsJson(async () => {
                // wrong date
                let scenario = './test/fixtures/csv/custom-field/import-insert-wrong-date.csv';
                const options = {
                    doInsert: true,
                    doCreateCustomFields: true,
                };

                await createCustomFields(context);

                const scenarioId = lodash.camelCase(path.basename(scenario, '.csv'));
                const importExportTemplate = await createTemplate(context, scenarioId);

                let stream = fs.createReadStream(scenario, 'utf8');
                let importResult = await createResult(context, scenario, importExportTemplate, options);
                importResult = await ImportExportTemplate.importCsv(context, context.batch, stream, importResult);
                assert.strictEqual(await importResult.generalError, 'Unsupported date value 2023-02-32');

                // wrong decimal format
                scenario = './test/fixtures/csv/custom-field/import-insert-wrong-decimal.csv';

                stream = fs.createReadStream(scenario, 'utf8');
                importResult = await createResult(context, scenario, importExportTemplate, options);
                importResult = await ImportExportTemplate.importCsv(context, context.batch, stream, importResult);
                assert.strictEqual(await importResult.generalError, '[DecimalError] Invalid argument: aaa');

                // wrong float format
                scenario = './test/fixtures/csv/custom-field/import-insert-wrong-float.csv';

                stream = fs.createReadStream(scenario, 'utf8');
                importResult = await createResult(context, scenario, importExportTemplate, options);
                importResult = await ImportExportTemplate.importCsv(context, context.batch, stream, importResult);
                assert.strictEqual(await importResult.generalError, 'Unsupported float/double value 100.10x');

                // wrong double format
                scenario = './test/fixtures/csv/custom-field/import-insert-wrong-double.csv';

                stream = fs.createReadStream(scenario, 'utf8');
                importResult = await createResult(context, scenario, importExportTemplate, options);
                importResult = await ImportExportTemplate.importCsv(context, context.batch, stream, importResult);
                assert.strictEqual(await importResult.generalError, 'Unsupported float/double value 1.234a');

                // wrong integer format
                scenario = './test/fixtures/csv/custom-field/import-insert-wrong-integer.csv';

                stream = fs.createReadStream(scenario, 'utf8');
                importResult = await createResult(context, scenario, importExportTemplate, options);
                importResult = await ImportExportTemplate.importCsv(context, context.batch, stream, importResult);
                assert.strictEqual(await importResult.generalError, 'Unsupported short/integer value 100.10');
            });
        });
    });

    it('throws if unknown custom field in the template creation', async () => {
        await Test.withContext(async context => {
            await context.withLocalizedTextAsJson(async () => {
                const scenario = 'header-wrong-custom-field';

                await createCustomFields(context);

                const templateTestCustomFields = {
                    id: scenario,
                    name: scenario,
                    code: scenario,
                    nodeName: 'TestCustomField',
                    description: 'description',
                    csvTemplate: {
                        data: [
                            {
                                _id: 0,
                                path: '!name',
                                dataType: 'string',
                                description: '!name',
                                locale: '',
                                isCustom: false,
                            },
                            {
                                _id: 10,
                                path: '_customData(customString)',
                                dataType: 'string',
                                description: 'customString',
                                locale: '',
                                isCustom: true,
                            },
                            {
                                _id: 20,
                                path: '_customData(customFail)',
                                dataType: 'boolean',
                                description: 'customFail',
                                locale: '',
                                isCustom: true,
                            },
                        ],
                    },
                };

                await assert.isRejected(
                    createImportExportTemplate(context, templateTestCustomFields),
                    'The record was not created.',
                );
            });
        });
    });

    it('the template creation - check isCustom', async () => {
        await Test.withContext(
            async context => {
                await context.withLocalizedTextAsJson(async () => {
                    // In import file with custom fields
                    await createCustomFields(context);

                    const template = await ImportExportTemplate.getDefaultCsvTemplate(context, 'TestCustomField');
                    assert.notEqual(template.csvTemplate.value.length, 0);
                    const headerLines = template.csvTemplate.value!.split('\n');
                    assert.equal(headerLines.length, 5);
                    assert.equal(headerLines[0], `${templateTestCustomField.csvTemplate};`);

                    const names = headerLines[0].split(defaultDelimiter);
                    const types = headerLines[1].split(defaultDelimiter);
                    const descriptions = headerLines[2].split(defaultDelimiter);
                    const isCustoms = headerLines[4].split(defaultDelimiter);

                    assert.notEqual(names.length, 0);
                    assert.equal(types.length, names.length);
                    assert.equal(descriptions.length, names.length);
                    assert.equal(descriptions.length, isCustoms.length);

                    assert.deepEqual(
                        names.reduce(
                            (r, k, i) => {
                                r[k] = {
                                    type: types[i],
                                    description: descriptions[i],
                                    isCustom: isCustoms[i],
                                };
                                return r;
                            },
                            {} as Dict<{ type: string; description: string; isCustom: string }>,
                        ),
                        {
                            '!name': {
                                type: 'string',
                                description: 'name',
                                isCustom: 'false',
                            },
                            '_customData(customBoolean)': {
                                description: 'custom boolean (false/true)',
                                type: 'boolean',
                                isCustom: 'true',
                            },
                            '_customData(customDate)': {
                                description: 'custom date (yyyy-mm-dd)',
                                type: 'date',
                                isCustom: 'true',
                            },
                            '_customData(customDecimalNumeric)': {
                                description: 'custom decimal numeric',
                                type: 'decimal',
                                isCustom: 'true',
                            },
                            '_customData(customDoubleNumeric)': {
                                description: 'custom double numeric',
                                type: 'double',
                                isCustom: 'true',
                            },
                            '_customData(customFloatNumeric)': {
                                description: 'custom float numeric',
                                type: 'float',
                                isCustom: 'true',
                            },
                            '_customData(customIntegerNumeric)': {
                                description: 'custom integer numeric',
                                type: 'integer',
                                isCustom: 'true',
                            },
                            '_customData(customSelect)': {
                                description: 'custom select',
                                type: 'enum',
                                isCustom: 'true',
                            },
                            '_customData(customString)': {
                                description: 'custom string',
                                type: 'string',
                                isCustom: 'true',
                            },
                            '': {
                                description: 'IGNORE',
                                type: 'IGNORE',
                                isCustom: 'IGNORE',
                            },
                        },
                    );
                });
            },
            { locale: 'en-US' },
        );
    });

    it('Import custom data (ok) scenario 1 with isMandatory true - Jane Doe', () =>
        Test.withReadonlyContext(
            async context => {
                const csvPathInsert = './test/fixtures/csv/custom-field/import-insert-scenario-1-jane-doe.csv';
                await context.withLocalizedTextAsJson(async () => {
                    // In import file with custom fields
                    const insertedCustomField = await importScenario(context, csvPathInsert, {
                        doInsert: true,
                        doCreateCustomFields: true,
                        isMandatoryCustomFields: true,
                        delimiter: ',',
                    });
                    assert.deepEqual(await insertedCustomField._customData, {
                        customBoolean: true,
                        customDate: '2023-07-26',
                        // to enable when the component is ready
                        // customMultiDropDown: '[technicalName3]',
                        customDecimalNumeric: '-3.14',
                        customDoubleNumeric: '1.234',
                        customFloatNumeric: '100.10',
                        customIntegerNumeric: '100',
                        customSelect: 'technicalName1',
                        customString: 'custom string',
                    });
                });
                const scenarioId = lodash.camelCase(path.basename(csvPathInsert, '.csv'));
                const template = await context.read(ImportExportTemplate, { id: scenarioId });

                const factory = context.application.getFactoryByName(await template.nodeName);

                const filterParsed = await PagingFilter.parseFilter(context, factory, '{}');

                await ImportExportTemplate.generateExportWithQueryReader(context, template, {
                    filter: filterParsed,
                });

                const uploadedFile = (
                    await context
                        .query(xtremUpload.nodes.UploadedFile, {
                            filter: { kind: 'upload' },
                            orderBy: { _id: -1 },
                            first: 1,
                        })
                        .toArray()
                )[0];
                const uploadUrl = await uploadedFile.uploadUrl;
                assert.notEqual(uploadUrl, '', 'UploadUrl could not be computed');
                const input = fs.readFileSync(csvPathInsert, 'utf8');
                const exported = fs.readFileSync(uploadUrl.replace('tenant-data/', '').replace('uploads/', ''), 'utf8');
                assert.equal(exported, input);

                await context.withLocalizedTextAsJson(async () => {
                    const csvPathUpdate = './test/fixtures/csv/custom-field/import-update-scenario-1-s1-jane-doe.csv';
                    const updatedLocalizedText = await importScenario(context, csvPathUpdate, {
                        doUpdate: true,
                        doCreateCustomFields: false,
                        delimiter: ',',
                    });
                    assert.deepEqual(await updatedLocalizedText._customData, {
                        customBoolean: true,
                        customDate: '2023-07-26',
                        // to enable when the component is ready
                        // customMultiDropDown: '[technicalName3,technicalName4]',
                        customDecimalNumeric: '-3.14',
                        customDoubleNumeric: '1.234',
                        customFloatNumeric: '100.10',
                        customIntegerNumeric: '100',
                        customSelect: 'technicalName2',
                        customString: 'custom string',
                    });
                });
            },
            {
                source: 'listener',
                testMode: true,
                userEmail: '<EMAIL>',
            },
        ));
    it('Import custom data (ok) scenario 1 with isMandatory true - Thierry Henry', () =>
        Test.withReadonlyContext(
            async context => {
                const csvPathInsert = './test/fixtures/csv/custom-field/import-insert-scenario-1-jane-doe.csv';
                const scenarioId = lodash.camelCase(path.basename(csvPathInsert, '.csv'));
                const template = await context.read(ImportExportTemplate, { id: scenarioId });

                const factory = context.application.getFactoryByName(await template.nodeName);

                const filterParsed = await PagingFilter.parseFilter(context, factory, '{}');

                await ImportExportTemplate.generateExportWithQueryReader(context, template, {
                    filter: filterParsed,
                });

                const uploadedFile = (
                    await context
                        .query(xtremUpload.nodes.UploadedFile, {
                            filter: { kind: 'upload' },
                            orderBy: { _id: -1 },
                            first: 1,
                        })
                        .toArray()
                )[0];
                const uploadUrl = await uploadedFile.uploadUrl;
                assert.notEqual(uploadUrl, '', 'UploadUrl could not be computed');
                const input = fs.readFileSync(
                    './test/fixtures/csv/custom-field/import-insert-scenario-1-thierry.csv',
                    'utf8',
                );
                const exported = fs.readFileSync(uploadUrl.replace('tenant-data/', '').replace('uploads/', ''), 'utf8');
                assert.equal(exported, input);

                await context.withLocalizedTextAsJson(async () => {
                    const csvPathUpdate = './test/fixtures/csv/custom-field/import-update-scenario-1-s1-thierry.csv';
                    const updatedLocalizedText = await importScenario(context, csvPathUpdate, {
                        doUpdate: true,
                        doCreateCustomFields: false,
                        delimiter: '|',
                    });
                    assert.deepEqual(await updatedLocalizedText._customData, {
                        customBoolean: true,
                        customDate: '2023-07-29',
                        // to enable when the component is ready
                        // customMultiDropDown: '[technicalName3,technicalName4]',
                        customDecimalNumeric: '-3.14',
                        customDoubleNumeric: '1.234',
                        customFloatNumeric: '100.10',
                        customIntegerNumeric: '100',
                        customSelect: 'technicalName2',
                        customString: 'custom string',
                    });
                });

                await ImportExportTemplate.generateExportWithQueryReader(context, template, {
                    filter: filterParsed,
                });

                const uploadedFile2 = (
                    await context
                        .query(xtremUpload.nodes.UploadedFile, {
                            filter: { kind: 'upload' },
                            orderBy: { _id: -1 },
                            first: 1,
                        })
                        .toArray()
                )[0];
                const uploadUrl2 = await uploadedFile2.uploadUrl;
                assert.notEqual(uploadUrl2, '', 'UploadUrl could not be computed');
                const input2 = fs.readFileSync(
                    './test/fixtures/csv/custom-field/import-update-scenario-1-s1-thierry-expected.csv',
                    'utf8',
                );
                const exported2 = fs.readFileSync(
                    uploadUrl2.replace('tenant-data/', '').replace('uploads/', ''),
                    'utf8',
                );
                assert.equal(exported2, input2);

                const csvPathUpdate2 = './test/fixtures/csv/custom-field/import-update-scenario-1-s2-thierry.csv';

                const updatedLocalizedText2 = await importScenario(context, csvPathUpdate2, {
                    doUpdate: true,
                    doCreateCustomFields: false,
                    delimiter: '|',
                });
                assert.deepEqual(await updatedLocalizedText2._customData, {
                    customBoolean: false,
                    customDate: date.today().toString(),
                    // to enable when the component is ready
                    // customMultiDropDown: '[technicalName3,technicalName4]',
                    customDecimalNumeric: '0',
                    customDoubleNumeric: 0,
                    customFloatNumeric: 0,
                    customIntegerNumeric: 0,
                    customSelect: 1,
                    customString: '',
                });

                await context.runInWritableContext(async ctx => {
                    await ctx.delete(ImportResult, { importExportTemplate: template._id });
                    await ctx.delete(ImportExportTemplate, { _id: template._id });
                    await ctx.delete(TestCustomField, { name: 'importScenario1' });
                    await ctx.deleteMany(xtremCustomization.nodes.CustomField, {});
                });
            },
            {
                source: 'listener',
                testMode: true,
                userEmail: '<EMAIL>',
            },
        ));
});

describe('Test getDefaultCsvTemplate with service options', () => {
    function createTemplate(context: Context, scenarioId: string) {
        const templateTestCustomFields = {
            id: scenarioId,
            name: scenarioId,
            code: scenarioId,
            nodeName: 'TestServiceOption',
            description: 'description',
            csvTemplate: {
                data: [
                    { _id: 0, path: '!name', dataType: 'string', description: '!name', locale: '' },
                    { _id: 20, path: 'code', dataType: 'string', description: 'description', locale: '' },
                    { _id: 30, path: '#lines', dataType: 'collection', description: '#lines', locale: '' },
                    { _id: 40, path: 'label', dataType: 'string', description: 'description', locale: '' },
                ],
            },
        };

        return createImportExportTemplate(context, templateTestCustomFields);
    }

    async function createResult(
        context: Context,
        scenario: string,
        importExportTemplate: ImportExportTemplate,
        options: { doInsert?: boolean; doUpdate?: boolean },
    ) {
        const user = await context.user;
        const uploadedFile = await uploadTestFile(scenario, user?.email);
        return ImportExportTemplate.createResult(
            context,
            importExportTemplate,
            true,
            !!options?.doUpdate,
            false,
            1,
            'inProgress',
            String(uploadedFile.uploadedFileId),
        );
    }

    async function importScenario(
        context: Context,
        scenario: string,
        options: { doInsert?: boolean; doUpdate?: boolean },
    ): Promise<TestServiceOption> {
        const fileContents = fs.readFileSync(scenario, 'utf8');
        const lines = fileContents.split('\n');
        const scenarioName = lines[1].split(defaultDelimiter)[0];

        const scenarioId = lodash.camelCase(path.basename(scenario, '.csv'));
        const importExportTemplate = await createTemplate(context, scenarioId);

        const stream = fs.createReadStream(scenario, 'utf8');
        let importResult = await createResult(context, scenario, importExportTemplate, options);
        importResult = await ImportExportTemplate.importCsv(context, context.batch, stream, importResult);
        assert.equal(await importResult.rowsProcessed, 1);
        assert.equal(await importResult.numberOfRowsInError, 0);
        return context.withLocalizedTextAsJson(() =>
            // forUpdate to make sure to fetch the text from the database and not from memory
            context.read(TestServiceOption, { name: scenarioName }, { forUpdate: true }),
        );
    }

    it('can getDefaultCsvTemplate when isDemoTenant inactive', async () => {
        await Test.withContext(
            async context => {
                const template = await ImportExportTemplate.getDefaultCsvTemplate(context, 'TestServiceOption');
                assert.notEqual(template.csvTemplate.value.length, 0);
                const headerLines = template.csvTemplate.value.split('\n');
                assert.equal(headerLines.length, 5);
                assert.equal(headerLines[0], `${templateTestServiceOptionInactive.csvTemplate};`);

                const names = headerLines[0].split(defaultDelimiter);
                const types = headerLines[1].split(defaultDelimiter);
                const descriptions = headerLines[2].split(defaultDelimiter);
                const isCustoms = headerLines[4].split(defaultDelimiter);

                assert.notEqual(names.length, 0);
                assert.equal(types.length, names.length);
                assert.equal(descriptions.length, names.length);
                assert.equal(descriptions.length, isCustoms.length);
            },
            {
                testActiveServiceOptions: [],
            },
        );
    });

    it('can getDefaultCsvTemplate when isDemoTenant active', async () => {
        await Test.withContext(
            async context => {
                const template = await ImportExportTemplate.getDefaultCsvTemplate(context, 'TestServiceOption');
                assert.notEqual(template.csvTemplate.value.length, 0);
                const headerLines = template.csvTemplate.value.split('\n');
                assert.equal(headerLines.length, 5);
                assert.equal(headerLines[0], `${templateTestServiceOptionActive.csvTemplate};`);

                const names = headerLines[0].split(defaultDelimiter);
                const types = headerLines[1].split(defaultDelimiter);
                const descriptions = headerLines[2].split(defaultDelimiter);
                const isCustoms = headerLines[4].split(defaultDelimiter);

                assert.notEqual(names.length, 0);
                assert.equal(types.length, names.length);
                assert.equal(descriptions.length, names.length);
                assert.equal(descriptions.length, isCustoms.length);
            },
            {
                testActiveServiceOptions: [serviceOptions.isDemoTenant],
            },
        );
    });

    it('Import data when isDemoTenant active (ok)', async () => {
        await Test.withContext(
            async context => {
                await context.withLocalizedTextAsJson(async () => {
                    const insertedTestServiceOption = await importScenario(
                        context,
                        './test/fixtures/csv/import-export-template/import-with-service-option-header.csv',
                        {
                            doInsert: true,
                        },
                    );
                    assert.equal(await insertedTestServiceOption.name, 'doc 1');
                    assert.equal(await insertedTestServiceOption.code, 'code1');
                    const lines = await insertedTestServiceOption.lines.toArray();
                    assert.equal(await lines[0].label, 'label test');
                });
            },
            {
                testActiveServiceOptions: [serviceOptions.isDemoTenant],
            },
        );
    });

    it('throws if when isDemoTenant inactive', async () => {
        await Test.withContext(async context => {
            await context.withLocalizedTextAsJson(async () => {
                await assert.isRejected(
                    importScenario(
                        context,
                        './test/fixtures/csv/import-export-template/import-with-service-option-header.csv',
                        {
                            doInsert: true,
                        },
                    ),
                    'The record was not created.',
                );
                assert.deepEqual(context.diagnoses, [
                    {
                        severity: 4,
                        path: ['csvTemplate'],
                        message: 'Header code is disabled by a service option',
                    },
                ]);
            });
        });
    });

    it('throws if when isDemoTenant inactive while importing with the deactivated header', async () => {
        await Test.withContext(
            async context => {
                const scenario = './test/fixtures/csv/import-export-template/import-with-service-option-header.csv';
                const options = {
                    doInsert: true,
                };

                const scenarioId = lodash.camelCase(path.basename(scenario, '.csv'));
                const importExportTemplate = await createTemplate(context, scenarioId);

                const stream = fs.createReadStream(scenario, 'utf8');
                let importResult = await createResult(context, scenario, importExportTemplate, options);

                await context.serviceOptionManager.deactivateServiceOptions(context, [serviceOptions.isDemoTenant]);
                importResult = await ImportExportTemplate.importCsv(context, context.batch, stream, importResult);
                assert.strictEqual(await importResult.generalError, 'Header code is disabled by a service option');
            },
            {
                testActiveServiceOptions: [serviceOptions.isDemoTenant],
            },
        );
    });

    it('Should pass if when isDemoTenant inactive while importing without the deactivated header', async () => {
        await Test.withContext(
            async context => {
                const scenario = './test/fixtures/csv/import-export-template/import-without-service-option.csv';
                const options = {
                    doInsert: true,
                };

                const scenarioId = lodash.camelCase(path.basename(scenario, '.csv'));
                const importExportTemplate = await createTemplate(context, scenarioId);

                const stream = fs.createReadStream(scenario, 'utf8');
                let importResult = await createResult(context, scenario, importExportTemplate, options);

                await context.serviceOptionManager.deactivateServiceOptions(context, [serviceOptions.isDemoTenant]);
                importResult = await ImportExportTemplate.importCsv(context, context.batch, stream, importResult);

                assert.equal(await importResult.rowsProcessed, 1);
                assert.equal(await importResult.numberOfRowsInError, 0);
                const insertedTestServiceOption = await context.withLocalizedTextAsJson(() =>
                    // forUpdate to make sure to fetch the text from the database and not from memory
                    context.read(TestServiceOption, { name: 'doc 1' }, { forUpdate: true }),
                );

                assert.equal(await insertedTestServiceOption.name, 'doc 1');
                assert.equal(await insertedTestServiceOption.code, '');
                const lines = await insertedTestServiceOption.lines.toArray();
                assert.equal(lines.length, 0);
                assert.strictEqual(await importResult.generalError, '');
            },
            {
                testActiveServiceOptions: [serviceOptions.isDemoTenant],
            },
        );
    });
});

describe('Test import export with authorization', () => {
    async function createTemplate(context: Context, scenarioId: string) {
        const templateTestActivityExport = {
            id: `${scenarioId}Export`,
            name: scenarioId,
            code: scenarioId,
            nodeName: 'TestActivity',
            description: 'description',
            csvTemplate: {
                data: [
                    { _id: 0, path: '!name', dataType: 'string', description: '!name', locale: '' },
                    { _id: 20, path: 'code', dataType: 'string', description: 'code', locale: '' },
                    { _id: 30, path: 'ref', dataType: 'reference', description: 'ref', locale: '' },
                    { _id: 40, path: 'ref.name', dataType: 'string', description: 'name', locale: '' },
                    { _id: 50, path: 'ref.code', dataType: 'string', description: 'code', locale: '' },
                    { _id: 50, path: 'ref.description', dataType: 'string', description: 'description', locale: '' },
                ],
            },
        };

        await createImportExportTemplate(context, templateTestActivityExport);

        const templateTestActivity = {
            id: scenarioId,
            name: scenarioId,
            code: scenarioId,
            nodeName: 'TestActivity',
            description: 'description',
            csvTemplate: {
                data: [
                    { _id: 0, path: '!name', dataType: 'string', description: '!name', locale: '' },
                    { _id: 20, path: 'code', dataType: 'string', description: 'code', locale: '' },
                    { _id: 30, path: 'ref', dataType: 'reference', description: 'ref', locale: '' },
                ],
            },
        };

        return createImportExportTemplate(context, templateTestActivity);
    }

    async function createResult(
        context: Context,
        scenario: string,
        importExportTemplate: ImportExportTemplate,
        options: { doInsert?: boolean; doUpdate?: boolean },
    ) {
        const user = await context.user;
        const uploadedFile = await uploadTestFile(scenario, user?.email);
        return ImportExportTemplate.createResult(
            context,
            importExportTemplate,
            true,
            !!options?.doUpdate,
            false,
            1,
            'inProgress',
            String(uploadedFile.uploadedFileId),
        );
    }

    async function exportTestDoc(context: Context, scenario: string, filter: string): Promise<void> {
        const scenarioId = lodash.camelCase(path.basename(scenario, '.csv'));
        const template = await context.read(ImportExportTemplate, { id: `${scenarioId}Export` });
        const factory = context.application.getFactoryByName(await template.nodeName);

        const filterParsed = await PagingFilter.parseFilter(context, factory, filter);
        await ImportExportTemplate.generateExportWithQueryReader(context, template, { filter: filterParsed });
    }

    async function importScenario(
        context: Context,
        scenario: string,
        options: { doInsert?: boolean; doUpdate?: boolean },
    ): Promise<TestActivity> {
        const fileContents = fs.readFileSync(scenario, 'utf8');
        const lines = fileContents.split('\n');
        const scenarioName = lines[1].split(defaultDelimiter)[0];

        const scenarioId = lodash.camelCase(path.basename(scenario, '.csv'));
        const importExportTemplate = await createTemplate(context, scenarioId);

        const stream = fs.createReadStream(scenario, 'utf8');
        let importResult = await createResult(context, scenario, importExportTemplate, options);
        importResult = await ImportExportTemplate.importCsv(context, context.batch, stream, importResult);
        assert.equal(await importResult.rowsProcessed, 1);
        assert.equal(await importResult.numberOfRowsInError, 0);
        return context.withLocalizedTextAsJson(() =>
            // forUpdate to make sure to fetch the text from the database and not from memory
            context.read(TestActivity, { name: scenarioName }, { forUpdate: true }),
        );
    }

    it('can getDefaultCsvTemplate when access is granted', async () => {
        await Test.withContext(
            async context => {
                const template = await ImportExportTemplate.getDefaultCsvTemplate(context, 'TestActivity');
                assert.notEqual(template.csvTemplate.value.length, 0);
                const headerLines = template.csvTemplate.value.split('\n');
                assert.equal(headerLines.length, 5);
                assert.equal(headerLines[0], `${templateTestAccessAuthorized.csvTemplate};`);

                const names = headerLines[0].split(defaultDelimiter);
                const types = headerLines[1].split(defaultDelimiter);
                const descriptions = headerLines[2].split(defaultDelimiter);
                const isCustoms = headerLines[4].split(defaultDelimiter);

                assert.notEqual(names.length, 0);
                assert.equal(types.length, names.length);
                assert.equal(descriptions.length, names.length);
                assert.equal(descriptions.length, isCustoms.length);
            },
            {
                user: { email: '<EMAIL>' },
                testActiveServiceOptions: [xtremAuthorization.serviceOptions.authorizationServiceOption],
            },
        );
    });

    it('can getDefaultCsvTemplate when access is not granted (should be the same as granted because we check reference node access only for export)', async () => {
        await Test.withContext(
            async context => {
                const template = await ImportExportTemplate.getDefaultCsvTemplate(context, 'TestActivity');
                assert.notEqual(template.csvTemplate.value.length, 0);
                const headerLines = template.csvTemplate.value.split('\n');
                assert.equal(headerLines.length, 5);
                assert.equal(headerLines[0], `${templateTestAccessAuthorized.csvTemplate};`);

                const names = headerLines[0].split(defaultDelimiter);
                const types = headerLines[1].split(defaultDelimiter);
                const descriptions = headerLines[2].split(defaultDelimiter);
                const isCustoms = headerLines[4].split(defaultDelimiter);

                assert.notEqual(names.length, 0);
                assert.equal(types.length, names.length);
                assert.equal(descriptions.length, names.length);
                assert.equal(descriptions.length, isCustoms.length);
            },
            {
                user: { email: '<EMAIL>' },
                testActiveServiceOptions: [xtremAuthorization.serviceOptions.authorizationServiceOption],
            },
        );
    });

    it('Import data then export when access is granted', async () => {
        const csvPath = './test/fixtures/csv/import-export-template/export-read-granted.csv';
        await Test.withContext(
            async context => {
                await context.withLocalizedTextAsJson(async () => {
                    const insertedTestActivity = await importScenario(
                        context,
                        './test/fixtures/csv/import-export-template/import-with-access.csv',
                        {
                            doInsert: true,
                        },
                    );
                    assert.equal(await insertedTestActivity.name, 'doc 1');
                    assert.equal(await insertedTestActivity.code, 'code1');
                    assert.equal(await (await insertedTestActivity.ref)?.name, 'ref 1');
                    assert.equal(await (await insertedTestActivity.ref)?.code, 'refcode 1');
                    assert.equal(await (await insertedTestActivity.ref)?.description, 'refdescription');

                    await exportTestDoc(
                        context,
                        './test/fixtures/csv/import-export-template/import-with-access.csv',
                        '{}',
                    );
                    const uploadedFile = (
                        await context
                            .query(xtremUpload.nodes.UploadedFile, {
                                filter: { kind: 'upload' },
                                orderBy: { _id: -1 },
                                first: 1,
                            })
                            .toArray()
                    )[0];
                    const uploadUrl = await uploadedFile.uploadUrl;
                    assert.notEqual(uploadUrl, '', 'UploadUrl could not be computed');
                    const input = fs.readFileSync(csvPath, 'utf8');
                    const exported = fs.readFileSync(
                        uploadUrl.replace('tenant-data/', '').replace('uploads/', ''),
                        'utf8',
                    );
                    assert.equal(exported, input);
                });
            },
            {
                user: { email: '<EMAIL>' },
                testActiveServiceOptions: [xtremAuthorization.serviceOptions.authorizationServiceOption],
            },
        );
    });

    it('Import data then export when access is not granted', async () => {
        const csvPath = './test/fixtures/csv/import-export-template/export-read-not-granted.csv';
        await Test.withContext(
            async context => {
                await context.withLocalizedTextAsJson(async () => {
                    const insertedTestActivity = await importScenario(
                        context,
                        './test/fixtures/csv/import-export-template/import-with-access.csv',
                        {
                            doInsert: true,
                        },
                    );
                    assert.equal(await insertedTestActivity.name, 'doc 1');
                    assert.equal(await insertedTestActivity.code, 'code1');
                    assert.equal(await (await insertedTestActivity.ref)?.name, 'ref 1');
                    assert.equal(await (await insertedTestActivity.ref)?.code, 'refcode 1');
                    assert.equal(await (await insertedTestActivity.ref)?.description, 'refdescription');

                    await exportTestDoc(
                        context,
                        './test/fixtures/csv/import-export-template/import-with-access.csv',
                        '{}',
                    );
                    const uploadedFile = (
                        await context
                            .query(xtremUpload.nodes.UploadedFile, {
                                filter: { kind: 'upload' },
                                orderBy: { _id: -1 },
                                first: 1,
                            })
                            .toArray()
                    )[0];
                    const uploadUrl = await uploadedFile.uploadUrl;
                    assert.notEqual(uploadUrl, '', 'UploadUrl could not be computed');
                    const input = fs.readFileSync(csvPath, 'utf8');
                    const exported = fs.readFileSync(
                        uploadUrl.replace('tenant-data/', '').replace('uploads/', ''),
                        'utf8',
                    );
                    assert.equal(exported, input);
                });
            },
            {
                user: { email: '<EMAIL>' },
                testActiveServiceOptions: [xtremAuthorization.serviceOptions.authorizationServiceOption],
            },
        );
    });

    it('Import data then export when access is granted without the deactivated header', async () => {
        const csvPath = './test/fixtures/csv/import-export-template/export-read-not-granted-2.csv';
        await Test.withContext(
            async context => {
                const scenario = './test/fixtures/csv/import-export-template/import-without-access.csv';
                const options = {
                    doInsert: true,
                };

                const scenarioId = lodash.camelCase(path.basename(scenario, '.csv'));
                const importExportTemplate = await createTemplate(context, scenarioId);

                const stream = fs.createReadStream(scenario, 'utf8');
                let importResult = await createResult(context, scenario, importExportTemplate, options);

                importResult = await ImportExportTemplate.importCsv(context, context.batch, stream, importResult);

                assert.equal(await importResult.rowsProcessed, 1);
                assert.equal(await importResult.numberOfRowsInError, 0);
                const insertedTestActivity = await context.withLocalizedTextAsJson(() =>
                    // forUpdate to make sure to fetch the text from the database and not from memory
                    context.read(TestActivity, { name: 'doc 1' }, { forUpdate: true }),
                );

                assert.equal(await insertedTestActivity.name, 'doc 1');
                assert.equal(await insertedTestActivity.ref, null);
                assert.strictEqual(await importResult.generalError, '');

                await exportTestDoc(
                    context,
                    './test/fixtures/csv/import-export-template/import-without-access.csv',
                    '{}',
                );
                const uploadedFile = (
                    await context
                        .query(xtremUpload.nodes.UploadedFile, {
                            filter: { kind: 'upload' },
                            orderBy: { _id: -1 },
                            first: 1,
                        })
                        .toArray()
                )[0];
                const uploadUrl = await uploadedFile.uploadUrl;
                assert.notEqual(uploadUrl, '', 'UploadUrl could not be computed');
                const input = fs.readFileSync(csvPath, 'utf8');
                const exported = fs.readFileSync(uploadUrl.replace('tenant-data/', '').replace('uploads/', ''), 'utf8');
                assert.equal(exported, input);
            },
            {
                user: { email: '<EMAIL>' },
                testActiveServiceOptions: [xtremAuthorization.serviceOptions.authorizationServiceOption],
            },
        );
    });

    it('Import data then export when access is not granted without the deactivated header', async () => {
        const csvPath = './test/fixtures/csv/import-export-template/export-read-not-granted-3.csv';
        await Test.withContext(
            async context => {
                const scenario = './test/fixtures/csv/import-export-template/import-without-access.csv';
                const options = {
                    doInsert: true,
                };

                const scenarioId = lodash.camelCase(path.basename(scenario, '.csv'));
                const importExportTemplate = await createTemplate(context, scenarioId);

                const stream = fs.createReadStream(scenario, 'utf8');
                let importResult = await createResult(context, scenario, importExportTemplate, options);

                importResult = await ImportExportTemplate.importCsv(context, context.batch, stream, importResult);

                assert.equal(await importResult.rowsProcessed, 1);
                assert.equal(await importResult.numberOfRowsInError, 0);
                const insertedTestActivity = await context.withLocalizedTextAsJson(() =>
                    // forUpdate to make sure to fetch the text from the database and not from memory
                    context.read(TestActivity, { name: 'doc 1' }, { forUpdate: true }),
                );

                assert.equal(await insertedTestActivity.name, 'doc 1');
                assert.equal(await insertedTestActivity.ref, null);
                assert.strictEqual(await importResult.generalError, '');

                await exportTestDoc(
                    context,
                    './test/fixtures/csv/import-export-template/import-without-access.csv',
                    '{}',
                );
                const uploadedFile = (
                    await context
                        .query(xtremUpload.nodes.UploadedFile, {
                            filter: { kind: 'upload' },
                            orderBy: { _id: -1 },
                            first: 1,
                        })
                        .toArray()
                )[0];
                const uploadUrl = await uploadedFile.uploadUrl;
                assert.notEqual(uploadUrl, '', 'UploadUrl could not be computed');
                const input = fs.readFileSync(csvPath, 'utf8');
                const exported = fs.readFileSync(uploadUrl.replace('tenant-data/', '').replace('uploads/', ''), 'utf8');
                assert.equal(exported, input);
            },
            {
                user: { email: '<EMAIL>' },
                testActiveServiceOptions: [xtremAuthorization.serviceOptions.authorizationServiceOption],
            },
        );
    });
});
