{"name": "doc 1", "code": "code1", "description": "default", "stringArrayVal": [], "transientInput": "", "enumVal": "value1", "enumArrayVal": null, "shortVal": 0, "integerVal": 0, "integerArrayVal": [], "decimalVal": 0, "booleanVal": false, "jsonVal": {}, "datetimeVal": "2022-02-18T13:16:00.000Z", "dateRangeVal": "[2022-01-01,2022-12-31)", "datetimeRangeVal": "[2022-01-01T00:00:00.000Z,2022-12-31T23:59:59.000Z]", "lines": [{"_sortValue": 10, "label": "line 1.1", "sublines": [{"_sortValue": 10, "label": "subline 1.1.1", "description": "", "item": {}, "parentDocline": {}}, {"_sortValue": 20, "label": "subline 1.1.2", "description": "", "item": null, "parentDocline": {}}], "testContentAddressable": null, "parentDoc": {}}, {"_sortValue": 20, "label": "line 1.2", "sublines": [{"_sortValue": 10, "label": "subline 1.2.1", "description": "", "item": null, "parentDocline": {}}, {"_sortValue": 20, "label": "subline 1.2.2", "description": "", "item": null, "parentDocline": {}}, {"_sortValue": 30, "label": "subline 1.2.3", "description": "", "item": null, "parentDocline": {}}], "testContentAddressable": null, "parentDoc": {}}], "comments": [{"_sortValue": 10, "text": "hello", "parentDoc": {}, "description": "description 1", "name": "name 1", "testContentAddressableDelegated": {"description": "description 1", "name": "name 1"}}, {"_sortValue": 20, "text": "world", "parentDoc": {}, "description": "description 2", "name": "name 2", "testContentAddressableDelegated": {"description": "description 2", "name": "name 2"}}]}