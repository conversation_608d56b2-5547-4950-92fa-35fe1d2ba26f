{"name": "doc 1", "code": "code1", "description": "{\"base\":\"description doc 1\"}", "stringArrayVal": ["string 1", "string 2", "string 3"], "transientInput": "", "enumVal": "value2", "enumArrayVal": ["value1", "value2"], "shortVal": 1, "integerVal": 123, "integerArrayVal": [1, 2, 3, 4], "decimalVal": "1.234", "booleanVal": true, "jsonVal": {}, "datetimeVal": "2022-02-18T13:16:00.000Z", "dateRangeVal": "[2022-01-01,2022-12-31)", "datetimeRangeVal": "[2022-01-01T00:00:00.000Z,2022-12-31T23:59:59.000Z]", "lines": [{"_sortValue": 10, "label": "line 1.1", "sublines": [{"_sortValue": 10, "label": "subline 1.1.1", "description": "description 1.1.1", "item": {}, "parentDocline": {}}, {"_sortValue": 20, "label": "subline 1.1.2", "description": "description 1.1.2", "item": null, "parentDocline": {}}], "testContentAddressable": null, "parentDoc": {}}, {"_sortValue": 20, "label": "line 1.2", "sublines": [{"_sortValue": 10, "label": "subline 1.2.1", "description": "description 1.2.1", "item": null, "parentDocline": {}}, {"_sortValue": 20, "label": "subline 1.2.2", "description": "description 1.2.2", "item": null, "parentDocline": {}}, {"_sortValue": 30, "label": "subline 1.2.3", "description": "description 1.2.3", "item": null, "parentDocline": {}}], "testContentAddressable": null, "parentDoc": {}}], "comments": [{"_sortValue": 10, "text": "hello", "parentDoc": {}, "description": "description 1", "name": "name 1", "testContentAddressableDelegated": {"description": "description 1", "name": "name 1"}}, {"_sortValue": 20, "text": "world", "parentDoc": {}, "description": "description 2", "name": "name 2", "testContentAddressableDelegated": {"description": "description 2", "name": "name 2"}}]}