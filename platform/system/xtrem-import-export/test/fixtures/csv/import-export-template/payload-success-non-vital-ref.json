{"name": "doc 1 non vital paths", "code": "code1", "description": "description doc 1 non vital paths", "stringArrayVal": ["string 1", "string 2", "string 3"], "enumVal": "value2", "enumArrayVal": ["value1", "value2"], "shortVal": 1, "integerVal": 123, "integerArrayVal": [1, 2, 3, 4], "decimalVal": "1.234", "booleanVal": true, "jsonVal": {}, "datetimeVal": "2022-02-18T13:16:00.000Z", "datetimeRangeVal": "[2022-01-01T00:00:00.000Z,2022-12-31T23:59:59.000Z]", "dateRangeVal": "[2022-01-01,2022-12-31)", "transientInput": "", "testContentAddressable": {"description": "testContentAddressable description 1", "name": "testContentAddressable name 1"}, "nonVitalRef": {}, "lines": [{"_sortValue": 10, "label": "line 1.1", "nonVitalRef": {}, "subLines": [{"_sortValue": 10, "label": "subLine 1.1.1", "description": "description 1.1.1", "item": {}, "nonVitalRef": {}, "parentDocLine": {}, "testContentAddressable": {"description": "testContentAddressable sub-line 1 description 1", "name": "testContentAddressable sub-line 1 name 1"}, "testDocSubLineWithNonVitalRef": null}, {"_sortValue": 20, "label": "subLine 1.1.2", "description": "description 1.1.2", "item": null, "nonVitalRef": {}, "parentDocLine": {}, "testContentAddressable": {"description": "testContentAddressable sub-line 2 description 1", "name": "testContentAddressable sub-line 2 name 1"}, "testDocSubLineWithNonVitalRef": null}], "testContentAddressable": {"description": "testContentAddressable line 1 description 1", "name": "testContentAddressable line 1 name 1"}, "parentDoc": {}, "testDocLineWithNonVitalRef": null}, {"_sortValue": 20, "label": "line 1.2", "nonVitalRef": {}, "subLines": [{"_sortValue": 10, "label": "subLine 1.2.1", "description": "description 1.2.1", "item": null, "nonVitalRef": {}, "parentDocLine": {}, "testContentAddressable": {"description": "testContentAddressable sub-line 1 description 1", "name": "testContentAddressable sub-line 1 name 1"}, "testDocSubLineWithNonVitalRef": null}, {"_sortValue": 20, "label": "subLine 1.2.2", "description": "description 1.2.2", "item": null, "nonVitalRef": {}, "parentDocLine": {}, "testContentAddressable": {"description": "testContentAddressable sub-line 2 description 1", "name": "testContentAddressable sub-line 2 name 1"}, "testDocSubLineWithNonVitalRef": null}, {"_sortValue": 30, "label": "subLine 1.2.3", "description": "description 1.2.3", "item": null, "nonVitalRef": {}, "parentDocLine": {}, "testContentAddressable": {"description": "testContentAddressable sub-line 3 description 1", "name": "testContentAddressable sub-line 3 name 1"}, "testDocSubLineWithNonVitalRef": null}], "testContentAddressable": {"description": "testContentAddressable line 2 description 1", "name": "testContentAddressable line 2 name 1"}, "parentDoc": {}, "testDocLineWithNonVitalRef": null}]}