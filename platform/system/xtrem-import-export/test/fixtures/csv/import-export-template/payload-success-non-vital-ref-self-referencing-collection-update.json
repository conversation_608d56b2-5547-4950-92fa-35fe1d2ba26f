{"name": "doc 1 non vital paths", "code": "code1 update", "description": "description doc 1 non vital paths update", "stringArrayVal": ["string 1 update", "string 2 update", "string 3 update"], "enumVal": "value2", "enumArrayVal": ["value1", "value2"], "shortVal": 2, "integerVal": 1234, "integerArrayVal": [1, 2, 3, 4, 5], "decimalVal": "1.2345", "booleanVal": false, "jsonVal": {}, "datetimeVal": "2022-02-18T13:16:05.000Z", "datetimeRangeVal": "[2022-01-01T00:05:00.000Z,2022-12-31T23:59:59.000Z]", "dateRangeVal": "[2022-01-05,2022-12-31)", "transientInput": "", "testContentAddressable": {"description": "testContentAddressable description 1", "name": "testContentAddressable name 1"}, "nonVitalRef": {}, "lines": [{"_sortValue": 10, "label": "line 1.1 update", "nonVitalRef": {}, "subLines": [{"_sortValue": 10, "label": "subLine 1.1.1 update", "description": "description 1.1.1 update", "item": {}, "nonVitalRef": {}, "parentDocLine": {}, "testContentAddressable": {"description": "testContentAddressable sub-line 1 description 1", "name": "testContentAddressable sub-line 1 name 1"}, "testDocSubLineWithNonVitalRef": null}, {"_sortValue": 20, "label": "subLine 1.1.2 update", "description": "description 1.1.2 update", "item": null, "nonVitalRef": {}, "parentDocLine": {}, "testContentAddressable": {"description": "testContentAddressable sub-line 2 description 1", "name": "testContentAddressable sub-line 2 name 1"}, "testDocSubLineWithNonVitalRef": null}], "testContentAddressable": {"description": "testContentAddressable line 1 description 1", "name": "testContentAddressable line 1 name 1"}, "parentDoc": {}, "testDocLineWithNonVitalRef": null}, {"_sortValue": 20, "label": "line 1.2 update", "nonVitalRef": {}, "subLines": [{"_sortValue": 10, "label": "subLine 1.2.1 update", "description": "description 1.2.1 update", "item": null, "nonVitalRef": {}, "parentDocLine": {}, "testContentAddressable": {"description": "testContentAddressable sub-line 1 description 1", "name": "testContentAddressable sub-line 1 name 1"}, "testDocSubLineWithNonVitalRef": null}, {"_sortValue": 20, "label": "subLine 1.2.2 update", "description": "description 1.2.2 update", "item": null, "nonVitalRef": {}, "parentDocLine": {}, "testContentAddressable": {"description": "testContentAddressable sub-line 2 description 1", "name": "testContentAddressable sub-line 2 name 1"}, "testDocSubLineWithNonVitalRef": null}, {"_sortValue": 30, "label": "subLine 1.2.3 update", "description": "description 1.2.3 update", "item": null, "nonVitalRef": {}, "parentDocLine": {}, "testContentAddressable": {"description": "testContentAddressable sub-line 3 description 1", "name": "testContentAddressable sub-line 3 name 1"}, "testDocSubLineWithNonVitalRef": null}], "testContentAddressable": {"description": "testContentAddressable line 2 description 1", "name": "testContentAddressable line 2 name 1"}, "parentDoc": {}, "testDocLineWithNonVitalRef": {}}]}