"!name";"*code";"description(base)";"stringArrayVal";"shortVal";"integerVal";"integerArrayVal";"decimalVal";"booleanVal";"jsonVal";"datetimeVal";"datetimeRangeVal";"dateRangeVal";"enumVal";"enumArrayVal";"transientInput";"#lines";"*label";"##sublines";"*label#1";"description#1";"item";"//testContentAddressable";"*name";"*description";"#comments";"text";"name";"description#2"
"doc 1";"code1";"description doc 1";"[""string 1"",""string 2"",""string 3""]";"1";"123";"[1,2,3,4]";"1.234";"TRUE";"{}";"2022-02-18T13:16:00Z";"[2022-01-01T00:00:00Z,2022-12-31T23:59:59Z]";"[2022-01-01,2022-12-31)";"value2";"[""value1"",""value2""]";;;"line 1.1";;"subline 1.1.1";"description 1.1.1";"COD3|NAM1|NAM1_REF";;;;;;;
"doc 1";"code1";"description doc 1";"[""string 1"",""string 2"",""string 3""]";"1";"123";"[1,2,3,4]";"1.234";"TRUE";"{}";"2022-02-18T13:16:00Z";"[2022-01-01T00:00:00Z,2022-12-31T23:59:59Z]";"[2022-01-01,2022-12-31)";"value2";"[""value1"",""value2""]";;;"line 1.1";;"subline 1.1.2";"description 1.1.2";;;;;;;;
"doc 1";"code1";"description doc 1";"[""string 1"",""string 2"",""string 3""]";"1";"123";"[1,2,3,4]";"1.234";"TRUE";"{}";"2022-02-18T13:16:00Z";"[2022-01-01T00:00:00Z,2022-12-31T23:59:59Z]";"[2022-01-01,2022-12-31)";"value2";"[""value1"",""value2""]";;;"line 1.2";;"subline 1.2.1";"description 1.2.1";;;;;;;;
"doc 1";"code1";"description doc 1";"[""string 1"",""string 2"",""string 3""]";"1";"123";"[1,2,3,4]";"1.234";"TRUE";"{}";"2022-02-18T13:16:00Z";"[2022-01-01T00:00:00Z,2022-12-31T23:59:59Z]";"[2022-01-01,2022-12-31)";"value2";"[""value1"",""value2""]";;;"line 1.2";;"subline 1.2.2";"description 1.2.2";;;;;;;;
"doc 1";"code1";"description doc 1";"[""string 1"",""string 2"",""string 3""]";"1";"123";"[1,2,3,4]";"1.234";"TRUE";"{}";"2022-02-18T13:16:00Z";"[2022-01-01T00:00:00Z,2022-12-31T23:59:59Z]";"[2022-01-01,2022-12-31)";"value2";"[""value1"",""value2""]";;;"line 1.2";;"subline 1.2.3";"description 1.2.3";;;;;;;;
"doc 1";"code1";"description doc 1";"[""string 1"",""string 2"",""string 3""]";"1";"123";"[1,2,3,4]";"1.234";"TRUE";"{}";"2022-02-18T13:16:00Z";"[2022-01-01T00:00:00Z,2022-12-31T23:59:59Z]";"[2022-01-01,2022-12-31)";"value2";"[""value1"",""value2""]";;;;;;;;;;;;"hello";"name 1";"description 1"
"doc 1";"code1";"description doc 1";"[""string 1"",""string 2"",""string 3""]";"1";"123";"[1,2,3,4]";"1.234";"TRUE";"{}";"2022-02-18T13:16:00Z";"[2022-01-01T00:00:00Z,2022-12-31T23:59:59Z]";"[2022-01-01,2022-12-31)";"value2";"[""value1"",""value2""]";;;;;;;;;;;;"world";"name 2";"description 2"
"doc 2";"code2";"'@description doc 2";"[""string 1"",""string 2"",""string 3""]";"2";"-234";"[2,3,4,5]";"2.345";"FALSE";"{""a"":1,""b"":1}";"2022-02-18T14:26:00Z";"(2022-01-01T00:00:00Z,2022-12-31T23:59:59Z)";"[2022-01-01,2022-12-31)";"value3";"[""value1"",""value2""]";;;"line 2.1";;"subline 2.1.1";"description 2.1.1";;;;;;;;
"doc 3";"code3";"'=SUM(1+1)";"[""string 1"",""string 2"",""string 3""]";"3";"345";"[3,4,5,6]";"3.456";"TRUE";"{""a"":2,""b"":2}";"2022-02-18T15:36:00Z";"[2022-01-01T00:00:00Z,2022-12-31T23:59:59Z)";"[2022-01-01,2022-12-31)";"value1";"[""value1"",""value2""]";;;"line 3.1 (no sub lines)";;;;;;"testContentAddressable name";"testContentAddressable *description";;;;
"doc 4";"code4";"description doc 4 (no lines)";"[""string 1"",""string 2"",""string 3""]";"4";"456";"[1,2,3,4,5,6,7]";"4.567";"FALSE";"{""a"":3,""b"":3}";"2022-02-18T16:46:00Z";"(2022-01-01T00:00:00Z,2022-12-31T23:59:59Z]";"[2022-01-01,2022-12-31)";"value2";"[""value1"",""value2""]";;;;;;;;;;;;;;
