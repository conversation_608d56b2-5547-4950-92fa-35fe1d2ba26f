"!name";"*code";"description(base)";"stringArrayVal";"shortVal";"integerVal";"integerArrayVal";"decimalVal";"booleanVal";"jsonVal";"datetimeVal";"dateRangeVal";"datetimeRangeVal";"enumVal";"enumArrayVal";"transientInput";"nonVitalRef";"nonVitalRef.name";"nonVitalRef.code";"nonVitalRef.description(en-US)";"nonVitalRef.stringArrayVal";"nonVitalRef.shortVal";"nonVitalRef.integerVal";"nonVitalRef.integerArrayVal";"nonVitalRef.decimalVal";"nonVitalRef.booleanVal";"nonVitalRef.jsonVal";"nonVitalRef.dateRangeVal";"nonVitalRef.datetimeRangeVal";"nonVitalRef.enumVal";"nonVitalRef.enumArrayVal";"nonVitalRef.subRef";"nonVitalRef.subRefNoNaturalKey";"/testContentAddressable";"*name";"*description";"#lines";"_sortValue";"testDocLineWithNonVitalRef";"*label";"nonVitalRef#1";"lines.nonVitalRef.name";"lines.nonVitalRef.code";"lines.nonVitalRef.description(en-US)";"lines.nonVitalRef.stringArrayVal";"lines.nonVitalRef.shortVal";"lines.nonVitalRef.integerVal";"lines.nonVitalRef.integerArrayVal";"lines.nonVitalRef.decimalVal";"lines.nonVitalRef.booleanVal";"lines.nonVitalRef.jsonVal";"lines.nonVitalRef.dateRangeVal";"lines.nonVitalRef.datetimeRangeVal";"lines.nonVitalRef.enumVal";"lines.nonVitalRef.enumArrayVal";"lines.nonVitalRef.subRef";"lines.nonVitalRef.subRefNoNaturalKey";"//testContentAddressable";"*name#1";"*description#1";"##subLines";"_sortValue#1";"testDocSubLineWithNonVitalRef";"*label#1";"description#1";"item";"nonVitalRef#2";"lines.subLines.nonVitalRef.name";"lines.subLines.nonVitalRef.code";"lines.subLines.nonVitalRef.description(en-US)";"lines.subLines.nonVitalRef.stringArrayVal";"lines.subLines.nonVitalRef.shortVal";"lines.subLines.nonVitalRef.integerVal";"lines.subLines.nonVitalRef.integerArrayVal";"lines.subLines.nonVitalRef.decimalVal";"lines.subLines.nonVitalRef.booleanVal";"lines.subLines.nonVitalRef.jsonVal";"lines.subLines.nonVitalRef.dateRangeVal";"lines.subLines.nonVitalRef.datetimeRangeVal";"lines.subLines.nonVitalRef.enumVal";"lines.subLines.nonVitalRef.enumArrayVal";"lines.subLines.nonVitalRef.subRef";"lines.subLines.nonVitalRef.subRefNoNaturalKey";"///testContentAddressable";"*name#2";"*description#2"
"doc 1 non vital paths";"code1 update";"description doc 1 non vital paths update";"[""string 1 update"",""string 2 update"",""string 3 update""]";"2";"1234";"[1,2,3,4,5]";"1.2345";"FALSE";"{}";"2022-02-18T13:16:05Z";"[2022-01-05,2022-12-31)";"[2022-01-01T00:05:00Z,2022-12-31T23:59:59Z]";"value2";"[""value1"",""value2""]";;"nonVitalRef1|non vital ref 1|non vital sub-ref 1";"non vital ref 1";"nonVitalRef1";;"[""non vital ref string 1"",""non vital ref string 2"",""non vital ref string 3""]";"1";"123";"[1,2,3,4]";"1.234";"TRUE";"{""key1"":""nonVitalRef1Value1"",""key2"":""nonVitalRef1Value2""}";"[2022-01-01,2023-01-01)";"[2022-01-01T00:00:00Z,2022-12-31T23:59:59Z]";"value2";"[""value1"",""value2""]";"non vital sub-ref 1";"1";;"testContentAddressable name 1";"testContentAddressable description 1";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;
"doc 1 non vital paths";"code1 update";"description doc 1 non vital paths update";"[""string 1 update"",""string 2 update"",""string 3 update""]";"2";"1234";"[1,2,3,4,5]";"1.2345";"FALSE";"{}";"2022-02-18T13:16:05Z";"[2022-01-05,2022-12-31)";"[2022-01-01T00:05:00Z,2022-12-31T23:59:59Z]";"value2";"[""value1"",""value2""]";;"nonVitalRef1|non vital ref 1|non vital sub-ref 1";"non vital ref 1";"nonVitalRef1";;"[""non vital ref string 1"",""non vital ref string 2"",""non vital ref string 3""]";"1";"123";"[1,2,3,4]";"1.234";"TRUE";"{""key1"":""nonVitalRef1Value1"",""key2"":""nonVitalRef1Value2""}";"[2022-01-01,2023-01-01)";"[2022-01-01T00:00:00Z,2022-12-31T23:59:59Z]";"value2";"[""value1"",""value2""]";"non vital sub-ref 1";"1";;;;;"10";;"line 1.1 update";"nonVitalRef1|non vital ref 1|non vital sub-ref 1";"non vital ref 1";"nonVitalRef1";;"[""non vital ref string 1"",""non vital ref string 2"",""non vital ref string 3""]";"1";"123";"[1,2,3,4]";"1.234";"TRUE";"{""key1"":""nonVitalRef1Value1"",""key2"":""nonVitalRef1Value2""}";"[2022-01-01,2023-01-01)";"[2022-01-01T00:00:00Z,2022-12-31T23:59:59Z]";"value2";"[""value1"",""value2""]";"non vital sub-ref 1";"1";;"testContentAddressable line 1 name 1";"testContentAddressable line 1 description 1";;;;;;;;;;;;;;;;;;;;;;;;;;
"doc 1 non vital paths";"code1 update";"description doc 1 non vital paths update";"[""string 1 update"",""string 2 update"",""string 3 update""]";"2";"1234";"[1,2,3,4,5]";"1.2345";"FALSE";"{}";"2022-02-18T13:16:05Z";"[2022-01-05,2022-12-31)";"[2022-01-01T00:05:00Z,2022-12-31T23:59:59Z]";"value2";"[""value1"",""value2""]";;"nonVitalRef1|non vital ref 1|non vital sub-ref 1";"non vital ref 1";"nonVitalRef1";;"[""non vital ref string 1"",""non vital ref string 2"",""non vital ref string 3""]";"1";"123";"[1,2,3,4]";"1.234";"TRUE";"{""key1"":""nonVitalRef1Value1"",""key2"":""nonVitalRef1Value2""}";"[2022-01-01,2023-01-01)";"[2022-01-01T00:00:00Z,2022-12-31T23:59:59Z]";"value2";"[""value1"",""value2""]";"non vital sub-ref 1";"1";;;;;"10";;"line 1.1 update";"nonVitalRef1|non vital ref 1|non vital sub-ref 1";"non vital ref 1";"nonVitalRef1";;"[""non vital ref string 1"",""non vital ref string 2"",""non vital ref string 3""]";"1";"123";"[1,2,3,4]";"1.234";"TRUE";"{""key1"":""nonVitalRef1Value1"",""key2"":""nonVitalRef1Value2""}";"[2022-01-01,2023-01-01)";"[2022-01-01T00:00:00Z,2022-12-31T23:59:59Z]";"value2";"[""value1"",""value2""]";"non vital sub-ref 1";"1";;;;;"10";;"subLine 1.1.1 update";"description 1.1.1 update";"COD3|NAM1|NAM1_REF";"nonVitalRef1|non vital ref 1|non vital sub-ref 1";"non vital ref 1";"nonVitalRef1";;"[""non vital ref string 1"",""non vital ref string 2"",""non vital ref string 3""]";"1";"123";"[1,2,3,4]";"1.234";"TRUE";"{""key1"":""nonVitalRef1Value1"",""key2"":""nonVitalRef1Value2""}";"[2022-01-01,2023-01-01)";"[2022-01-01T00:00:00Z,2022-12-31T23:59:59Z]";"value2";"[""value1"",""value2""]";"non vital sub-ref 1";"1";;"testContentAddressable sub-line 1 name 1";"testContentAddressable sub-line 1 description 1"
"doc 1 non vital paths";"code1 update";"description doc 1 non vital paths update";"[""string 1 update"",""string 2 update"",""string 3 update""]";"2";"1234";"[1,2,3,4,5]";"1.2345";"FALSE";"{}";"2022-02-18T13:16:05Z";"[2022-01-05,2022-12-31)";"[2022-01-01T00:05:00Z,2022-12-31T23:59:59Z]";"value2";"[""value1"",""value2""]";;"nonVitalRef1|non vital ref 1|non vital sub-ref 1";"non vital ref 1";"nonVitalRef1";;"[""non vital ref string 1"",""non vital ref string 2"",""non vital ref string 3""]";"1";"123";"[1,2,3,4]";"1.234";"TRUE";"{""key1"":""nonVitalRef1Value1"",""key2"":""nonVitalRef1Value2""}";"[2022-01-01,2023-01-01)";"[2022-01-01T00:00:00Z,2022-12-31T23:59:59Z]";"value2";"[""value1"",""value2""]";"non vital sub-ref 1";"1";;;;;"10";;"line 1.1 update";"nonVitalRef1|non vital ref 1|non vital sub-ref 1";"non vital ref 1";"nonVitalRef1";;"[""non vital ref string 1"",""non vital ref string 2"",""non vital ref string 3""]";"1";"123";"[1,2,3,4]";"1.234";"TRUE";"{""key1"":""nonVitalRef1Value1"",""key2"":""nonVitalRef1Value2""}";"[2022-01-01,2023-01-01)";"[2022-01-01T00:00:00Z,2022-12-31T23:59:59Z]";"value2";"[""value1"",""value2""]";"non vital sub-ref 1";"1";;;;;"20";;"subLine 1.1.2 update";"description 1.1.2 update";;"nonVitalRef1|non vital ref 1|non vital sub-ref 1";"non vital ref 1";"nonVitalRef1";;"[""non vital ref string 1"",""non vital ref string 2"",""non vital ref string 3""]";"1";"123";"[1,2,3,4]";"1.234";"TRUE";"{""key1"":""nonVitalRef1Value1"",""key2"":""nonVitalRef1Value2""}";"[2022-01-01,2023-01-01)";"[2022-01-01T00:00:00Z,2022-12-31T23:59:59Z]";"value2";"[""value1"",""value2""]";"non vital sub-ref 1";"1";;"testContentAddressable sub-line 2 name 1";"testContentAddressable sub-line 2 description 1"
"doc 1 non vital paths";"code1 update";"description doc 1 non vital paths update";"[""string 1 update"",""string 2 update"",""string 3 update""]";"2";"1234";"[1,2,3,4,5]";"1.2345";"FALSE";"{}";"2022-02-18T13:16:05Z";"[2022-01-05,2022-12-31)";"[2022-01-01T00:05:00Z,2022-12-31T23:59:59Z]";"value2";"[""value1"",""value2""]";;"nonVitalRef1|non vital ref 1|non vital sub-ref 1";"non vital ref 1";"nonVitalRef1";;"[""non vital ref string 1"",""non vital ref string 2"",""non vital ref string 3""]";"1";"123";"[1,2,3,4]";"1.234";"TRUE";"{""key1"":""nonVitalRef1Value1"",""key2"":""nonVitalRef1Value2""}";"[2022-01-01,2023-01-01)";"[2022-01-01T00:00:00Z,2022-12-31T23:59:59Z]";"value2";"[""value1"",""value2""]";"non vital sub-ref 1";"1";;;;;"20";"doc 1 non vital paths|10";"line 1.2 update";"nonVitalRef1|non vital ref 1|non vital sub-ref 1";"non vital ref 1";"nonVitalRef1";;"[""non vital ref string 1"",""non vital ref string 2"",""non vital ref string 3""]";"1";"123";"[1,2,3,4]";"1.234";"TRUE";"{""key1"":""nonVitalRef1Value1"",""key2"":""nonVitalRef1Value2""}";"[2022-01-01,2023-01-01)";"[2022-01-01T00:00:00Z,2022-12-31T23:59:59Z]";"value2";"[""value1"",""value2""]";"non vital sub-ref 1";"1";;"testContentAddressable line 2 name 1";"testContentAddressable line 2 description 1";;;;;;;;;;;;;;;;;;;;;;;;;;
"doc 1 non vital paths";"code1 update";"description doc 1 non vital paths update";"[""string 1 update"",""string 2 update"",""string 3 update""]";"2";"1234";"[1,2,3,4,5]";"1.2345";"FALSE";"{}";"2022-02-18T13:16:05Z";"[2022-01-05,2022-12-31)";"[2022-01-01T00:05:00Z,2022-12-31T23:59:59Z]";"value2";"[""value1"",""value2""]";;"nonVitalRef1|non vital ref 1|non vital sub-ref 1";"non vital ref 1";"nonVitalRef1";;"[""non vital ref string 1"",""non vital ref string 2"",""non vital ref string 3""]";"1";"123";"[1,2,3,4]";"1.234";"TRUE";"{""key1"":""nonVitalRef1Value1"",""key2"":""nonVitalRef1Value2""}";"[2022-01-01,2023-01-01)";"[2022-01-01T00:00:00Z,2022-12-31T23:59:59Z]";"value2";"[""value1"",""value2""]";"non vital sub-ref 1";"1";;;;;"20";"doc 1 non vital paths|10";"line 1.2 update";"nonVitalRef1|non vital ref 1|non vital sub-ref 1";"non vital ref 1";"nonVitalRef1";;"[""non vital ref string 1"",""non vital ref string 2"",""non vital ref string 3""]";"1";"123";"[1,2,3,4]";"1.234";"TRUE";"{""key1"":""nonVitalRef1Value1"",""key2"":""nonVitalRef1Value2""}";"[2022-01-01,2023-01-01)";"[2022-01-01T00:00:00Z,2022-12-31T23:59:59Z]";"value2";"[""value1"",""value2""]";"non vital sub-ref 1";"1";;;;;"10";;"subLine 1.2.1 update";"description 1.2.1 update";;"nonVitalRef1|non vital ref 1|non vital sub-ref 1";"non vital ref 1";"nonVitalRef1";;"[""non vital ref string 1"",""non vital ref string 2"",""non vital ref string 3""]";"1";"123";"[1,2,3,4]";"1.234";"TRUE";"{""key1"":""nonVitalRef1Value1"",""key2"":""nonVitalRef1Value2""}";"[2022-01-01,2023-01-01)";"[2022-01-01T00:00:00Z,2022-12-31T23:59:59Z]";"value2";"[""value1"",""value2""]";"non vital sub-ref 1";"1";;"testContentAddressable sub-line 1 name 1";"testContentAddressable sub-line 1 description 1"
"doc 1 non vital paths";"code1 update";"description doc 1 non vital paths update";"[""string 1 update"",""string 2 update"",""string 3 update""]";"2";"1234";"[1,2,3,4,5]";"1.2345";"FALSE";"{}";"2022-02-18T13:16:05Z";"[2022-01-05,2022-12-31)";"[2022-01-01T00:05:00Z,2022-12-31T23:59:59Z]";"value2";"[""value1"",""value2""]";;"nonVitalRef1|non vital ref 1|non vital sub-ref 1";"non vital ref 1";"nonVitalRef1";;"[""non vital ref string 1"",""non vital ref string 2"",""non vital ref string 3""]";"1";"123";"[1,2,3,4]";"1.234";"TRUE";"{""key1"":""nonVitalRef1Value1"",""key2"":""nonVitalRef1Value2""}";"[2022-01-01,2023-01-01)";"[2022-01-01T00:00:00Z,2022-12-31T23:59:59Z]";"value2";"[""value1"",""value2""]";"non vital sub-ref 1";"1";;;;;"20";"doc 1 non vital paths|10";"line 1.2 update";"nonVitalRef1|non vital ref 1|non vital sub-ref 1";"non vital ref 1";"nonVitalRef1";;"[""non vital ref string 1"",""non vital ref string 2"",""non vital ref string 3""]";"1";"123";"[1,2,3,4]";"1.234";"TRUE";"{""key1"":""nonVitalRef1Value1"",""key2"":""nonVitalRef1Value2""}";"[2022-01-01,2023-01-01)";"[2022-01-01T00:00:00Z,2022-12-31T23:59:59Z]";"value2";"[""value1"",""value2""]";"non vital sub-ref 1";"1";;;;;"20";;"subLine 1.2.2 update";"description 1.2.2 update";;"nonVitalRef1|non vital ref 1|non vital sub-ref 1";"non vital ref 1";"nonVitalRef1";;"[""non vital ref string 1"",""non vital ref string 2"",""non vital ref string 3""]";"1";"123";"[1,2,3,4]";"1.234";"TRUE";"{""key1"":""nonVitalRef1Value1"",""key2"":""nonVitalRef1Value2""}";"[2022-01-01,2023-01-01)";"[2022-01-01T00:00:00Z,2022-12-31T23:59:59Z]";"value2";"[""value1"",""value2""]";"non vital sub-ref 1";"1";;"testContentAddressable sub-line 2 name 1";"testContentAddressable sub-line 2 description 1"
"doc 1 non vital paths";"code1 update";"description doc 1 non vital paths update";"[""string 1 update"",""string 2 update"",""string 3 update""]";"2";"1234";"[1,2,3,4,5]";"1.2345";"FALSE";"{}";"2022-02-18T13:16:05Z";"[2022-01-05,2022-12-31)";"[2022-01-01T00:05:00Z,2022-12-31T23:59:59Z]";"value2";"[""value1"",""value2""]";;"nonVitalRef1|non vital ref 1|non vital sub-ref 1";"non vital ref 1";"nonVitalRef1";;"[""non vital ref string 1"",""non vital ref string 2"",""non vital ref string 3""]";"1";"123";"[1,2,3,4]";"1.234";"TRUE";"{""key1"":""nonVitalRef1Value1"",""key2"":""nonVitalRef1Value2""}";"[2022-01-01,2023-01-01)";"[2022-01-01T00:00:00Z,2022-12-31T23:59:59Z]";"value2";"[""value1"",""value2""]";"non vital sub-ref 1";"1";;;;;"20";"doc 1 non vital paths|10";"line 1.2 update";"nonVitalRef1|non vital ref 1|non vital sub-ref 1";"non vital ref 1";"nonVitalRef1";;"[""non vital ref string 1"",""non vital ref string 2"",""non vital ref string 3""]";"1";"123";"[1,2,3,4]";"1.234";"TRUE";"{""key1"":""nonVitalRef1Value1"",""key2"":""nonVitalRef1Value2""}";"[2022-01-01,2023-01-01)";"[2022-01-01T00:00:00Z,2022-12-31T23:59:59Z]";"value2";"[""value1"",""value2""]";"non vital sub-ref 1";"1";;;;;"30";;"subLine 1.2.3 update";"description 1.2.3 update";;"nonVitalRef1|non vital ref 1|non vital sub-ref 1";"non vital ref 1";"nonVitalRef1";;"[""non vital ref string 1"",""non vital ref string 2"",""non vital ref string 3""]";"1";"123";"[1,2,3,4]";"1.234";"TRUE";"{""key1"":""nonVitalRef1Value1"",""key2"":""nonVitalRef1Value2""}";"[2022-01-01,2023-01-01)";"[2022-01-01T00:00:00Z,2022-12-31T23:59:59Z]";"value2";"[""value1"",""value2""]";"non vital sub-ref 1";"1";;"testContentAddressable sub-line 3 name 1";"testContentAddressable sub-line 3 description 1"
