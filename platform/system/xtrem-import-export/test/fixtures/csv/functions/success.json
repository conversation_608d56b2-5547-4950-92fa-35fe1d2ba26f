[{"payload": {"name": "doc 1", "code": "code1", "description": "description doc 1", "stringArrayVal": "['string 1','string 2','string 3']", "enumVal": "value2", "enumArrayVal": "['value1', 'value2']", "integerVal": "123", "shortVal": "1", "integerArrayVal": "[1,2,3,4]", "decimalVal": "1.234", "booleanVal": "true", "jsonVal": "{}", "datetimeVal": "2022-02-18T13:16:00Z", "dateRangeVal": "[2022-01-01,2022-12-31)", "datetimeRangeVal": "[2022-01-01T00:00:00Z,2022-12-31T23:59:59Z]", "lines": [{"label": "line 1.1", "sublines": [{"label": "subline 1.1.1", "description": "description 1.1.1", "item": "#COD3|NAM1|NAM1_REF"}, {"label": "subline 1.1.2", "description": "description 1.1.2", "item": null}]}, {"label": "line 1.2", "sublines": [{"label": "subline 1.2.1", "description": "description 1.2.1", "item": null}, {"label": "subline 1.2.2", "description": "description 1.2.2", "item": null}, {"label": "subline 1.2.3", "description": "description 1.2.3", "item": null}]}], "comments": [{"text": "hello", "description": "description 1", "name": "name 1"}, {"text": "world", "description": "description 2", "name": "name 2"}]}, "rows": [{"!name": "doc 1", "!code": "code1", "description": "description doc 1", "stringArrayVal": "['string 1','string 2','string 3']", "enumVal": "value2", "enumArrayVal": "['value1', 'value2']", "integerVal": "123", "shortVal": "1", "integerArrayVal": "[1,2,3,4]", "decimalVal": "1.234", "booleanVal": "true", "jsonVal": "{}", "datetimeVal": "2022-02-18T13:16:00Z", "dateRangeVal": "[2022-01-01,2022-12-31)", "datetimeRangeVal": "[2022-01-01T00:00:00Z,2022-12-31T23:59:59Z]", "$lineNumber": 0}, {"label": "line 1.1", "$lineNumber": 0}, {"label#1": "subline 1.1.1", "description#1": "description 1.1.1", "item(code|name|ref)": "COD3|NAM1|NAM1_REF", "$lineNumber": 0}, {"label#1": "subline 1.1.2", "description#1": "description 1.1.2", "item(code|name|ref)": null, "$lineNumber": 1}, {"label": "line 1.2", "$lineNumber": 2}, {"label#1": "subline 1.2.1", "description#1": "description 1.2.1", "item(code|name|ref)": null, "$lineNumber": 2}, {"label#1": "subline 1.2.2", "description#1": "description 1.2.2", "item(code|name|ref)": null, "$lineNumber": 3}, {"label#1": "subline 1.2.3", "description#1": "description 1.2.3", "item(code|name|ref)": null, "$lineNumber": 4}, {"text": "hello", "description#2": "description 1", "name": "name 1", "$lineNumber": 4}, {"text": "world", "description#2": "description 2", "name": "name 2", "$lineNumber": 5}]}, {"payload": {"name": "doc 2", "code": "code2", "description": "description doc 2", "stringArrayVal": "['string 1','string 2','string 3']", "enumVal": "value3", "enumArrayVal": "['value1', 'value2']", "integerVal": "234", "shortVal": "2", "integerArrayVal": "[2,3,4,5]", "decimalVal": "2.345", "booleanVal": "false", "jsonVal": "{a:1,b:1}", "datetimeVal": "2022-02-18T14:26:00Z", "dateRangeVal": "(2022-01-01,2022-12-31)", "datetimeRangeVal": "(2022-01-01T00:00:00Z,2022-12-31T23:59:59Z)", "lines": [{"label": "line 2.1", "sublines": [{"label": "subline 2.1.1", "description": "description 2.1.1", "item": null}]}]}, "rows": [{"!name": "doc 2", "!code": "code2", "description": "description doc 2", "stringArrayVal": "['string 1','string 2','string 3']", "enumVal": "value3", "enumArrayVal": "['value1', 'value2']", "integerVal": "234", "shortVal": "2", "integerArrayVal": "[2,3,4,5]", "decimalVal": "2.345", "booleanVal": "false", "jsonVal": "{a:1,b:1}", "datetimeVal": "2022-02-18T14:26:00Z", "dateRangeVal": "(2022-01-01,2022-12-31)", "datetimeRangeVal": "(2022-01-01T00:00:00Z,2022-12-31T23:59:59Z)", "$lineNumber": 6}, {"label": "line 2.1", "$lineNumber": 6}, {"label#1": "subline 2.1.1", "description#1": "description 2.1.1", "item(code|name|ref)": null, "$lineNumber": 6}]}, {"payload": {"name": "doc 3", "code": "code3", "description": "description doc 3", "stringArrayVal": "['string 1','string 2','string 3']", "enumVal": "value1", "enumArrayVal": "['value1', 'value2']", "integerVal": "345", "shortVal": "3", "integerArrayVal": "[3,4,5,6]", "decimalVal": "3.456", "booleanVal": "true", "jsonVal": "{a:2,b:2}", "datetimeVal": "2022-02-18T15:36:00Z", "dateRangeVal": "[2022-01-01,2022-12-31)", "datetimeRangeVal": "[2022-01-01T00:00:00Z,2022-12-31T23:59:59Z)", "lines": [{"label": "line 3.1 (no sub lines)", "testContentAddressable": {"description": "testContentAddressable *description", "name": "testContentAddressable name"}}]}, "rows": [{"!name": "doc 3", "!code": "code3", "description": "description doc 3", "stringArrayVal": "['string 1','string 2','string 3']", "enumVal": "value1", "enumArrayVal": "['value1', 'value2']", "integerVal": "345", "shortVal": "3", "integerArrayVal": "[3,4,5,6]", "decimalVal": "3.456", "booleanVal": "true", "jsonVal": "{a:2,b:2}", "datetimeVal": "2022-02-18T15:36:00Z", "dateRangeVal": "[2022-01-01,2022-12-31)", "datetimeRangeVal": "[2022-01-01T00:00:00Z,2022-12-31T23:59:59Z)", "$lineNumber": 7}, {"label": "line 3.1 (no sub lines)", "$lineNumber": 7}, {"$lineNumber": 7, "*description": "testContentAddressable *description", "*name": "testContentAddressable name"}]}, {"payload": {"name": "doc 4", "code": "code4", "description": "description doc 4 (no lines)", "stringArrayVal": "['string 1','string 2','string 3']", "enumVal": "value2", "enumArrayVal": "['value1', 'value2']", "integerVal": "456", "shortVal": "4", "integerArrayVal": "[1,2,3,4,5,6,7]", "decimalVal": "4.567", "booleanVal": "false", "jsonVal": "{a:3,b:3}", "datetimeVal": "2022-02-18T16:46:00Z", "dateRangeVal": "(2022-01-01,2022-12-31]", "datetimeRangeVal": "(2022-01-01T00:00:00Z,2022-12-31T23:59:59Z]"}, "rows": [{"!name": "doc 4", "!code": "code4", "description": "description doc 4 (no lines)", "stringArrayVal": "['string 1','string 2','string 3']", "enumVal": "value2", "enumArrayVal": "['value1', 'value2']", "integerVal": "456", "shortVal": "4", "integerArrayVal": "[1,2,3,4,5,6,7]", "decimalVal": "4.567", "booleanVal": "false", "jsonVal": "{a:3,b:3}", "datetimeVal": "2022-02-18T16:46:00Z", "dateRangeVal": "(2022-01-01,2022-12-31]", "datetimeRangeVal": "(2022-01-01T00:00:00Z,2022-12-31T23:59:59Z]", "$lineNumber": 8}]}]