!name;!code;description;stringArrayVal;enumVal;enumArrayVal;integerVal;shortVal;integerArrayVal;decimalVal;booleanVal;jsonVal;datetimeVal;datetimeRangeVal;dateRangeVal;#lines;label;##sublines;label#1;description#1;item(code|name|ref);//testContentAddressable;*name;*description;#comments;text;name;description#2
doc 1;code1;;;;;;;;;;;2022-02-18T13:16:00Z;[2022-01-01T00:00:00Z,2022-12-31T23:59:59Z];[2022-01-01,2022-12-31);1;line 1.1;1;subline 1.1.1;;COD3|NAM1|NAM1_REF;;;;;;;
doc 1;code1;;;;;;;;;;;2022-02-18T13:16:00Z;[2022-01-01T00:00:00Z,2022-12-31T23:59:59Z];[2022-01-01,2022-12-31);1;line 1.1;2;subline 1.1.2;;;;;;;;;
doc 1;code1;;;;;;;;;;;2022-02-18T13:16:00Z;[2022-01-01T00:00:00Z,2022-12-31T23:59:59Z];[2022-01-01,2022-12-31);2;line 1.2;1;subline 1.2.1;;;;;;;;;
doc 1;code1;;;;;;;;;;;2022-02-18T13:16:00Z;[2022-01-01T00:00:00Z,2022-12-31T23:59:59Z];[2022-01-01,2022-12-31);2;line 1.2;2;subline 1.2.2;;;;;;;;;
doc 1;code1;;;;;;;;;;;2022-02-18T13:16:00Z;[2022-01-01T00:00:00Z,2022-12-31T23:59:59Z];[2022-01-01,2022-12-31);2;line 1.2;3;subline 1.2.3;;;;;;1;hello;name 1;description 1
doc 1;code1;;;;;;;;;;;2022-02-18T13:16:00Z;[2022-01-01T00:00:00Z,2022-12-31T23:59:59Z];[2022-01-01,2022-12-31);2;line 1.2;3;subline 1.2.3;;;;;;2;world;name 2;description 2
