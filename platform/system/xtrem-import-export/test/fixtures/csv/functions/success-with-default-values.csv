!name;!code;description;stringArrayVal;enumVal;enumArrayVal;integerVal;shortVal;integerArrayVal;decimalVal;booleanVal;jsonVal;itemWithDefaultValueDocLevel(code|name|ref);datetimeVal;datetimeRangeVal;dateRangeVal;#lines;label#1;description#1;item(code|name|ref)
doc 1;code1;description doc 1;['string 1','string 2','string 3'];value2;['value1', 'value2'];123;1;[1,2,3,4];1.234;true;{};;2022-02-18T13:16:00Z;[2022-01-01T00:00:00Z,2022-12-31T23:59:59Z];[2022-02-18,2022-12-31);1;line 1.1;description 1.1;COD3|NAM1|NAM1_REF
doc 1;code1;description doc 1;['string 1','string 2','string 3'];value2;['value1', 'value2'];123;1;[1,2,3,4];1.234;true;{};;2022-02-18T13:16:00Z;[2022-01-01T00:00:00Z,2022-12-31T23:59:59Z];[2022-02-18,2022-12-31);2;line 1.2;description 1.2;
doc 2;code2;description doc 2;['string 1','string 2','string 3'];value3;['value1', 'value2'];234;2;[2,3,4,5];2.345;false;{a:1,b:1};;2022-02-18T14:26:00Z;(2022-01-01T00:00:00Z,2022-12-31T23:59:59Z);[2022-02-18,2022-12-31);1;line 2.1;description 2.1;
doc 3;code3;description doc 3;['string 1','string 2','string 3'];value1;['value1', 'value2'];345;3;[3,4,5,6];3.456;true;{a:2,b:2};;2022-02-18T15:36:00Z;[2022-01-01T00:00:00Z,2022-12-31T23:59:59Z);[2022-02-18,2022-12-31);;;;
doc 4;code4;description doc 4 (no lines);['string 1','string 2','string 3'];value2;['value1', 'value2'];456;4;[1,2,3,4,5,6,7];4.567;false;{a:3,b:3};;2022-02-18T16:46:00Z;(2022-01-01T00:00:00Z,2022-12-31T23:59:59Z];[2022-02-18,2022-12-31);;;;
