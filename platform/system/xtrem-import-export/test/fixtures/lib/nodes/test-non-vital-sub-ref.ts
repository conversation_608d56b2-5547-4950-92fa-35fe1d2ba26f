import {
    dateRange,
    datetime,
    datetimeRange,
    decimal,
    DecimalDataType,
    decorators,
    integer,
    Node,
    short,
    StringArrayDataType,
    StringDataType,
} from '@sage/xtrem-core';
import { TestEnum, testEnumDataType } from '../enums';

const decimalDataType = new DecimalDataType({ precision: 9, scale: 3 });
const nameDataType = new StringDataType({ maxLength: 80 });

@decorators.node<TestNonVitalSubRef>({
    isPublished: true,
    storage: 'sql',
    canRead: true,
    canCreate: true,
    canDelete: true,
    canUpdate: true,
    isCustomizable: true,
    indexes: [
        { orderBy: { subRefName: +1 }, isUnique: true, isNaturalKey: true },
        { orderBy: { subRefCode: +1 }, isUnique: true },
    ],
})
export class TestNonVitalSubRef extends Node {
    @decorators.stringProperty<TestNonVitalSubRef, 'subRefName'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringDataType({ maxLength: 80 }),
    })
    readonly subRefName: Promise<string>;

    @decorators.stringProperty<TestNonVitalSubRef, 'subRefCode'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringDataType({ maxLength: 80 }),
        isRequired: false,
    })
    readonly subRefCode: Promise<string>;

    @decorators.stringProperty<TestNonVitalSubRef, 'description'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringDataType({ maxLength: 100, isLocalized: true }),
        async control(cx, val): Promise<void> {
            await cx.error.if(val).is.equal.to('description');
        },
        defaultValue() {
            return 'default';
        },
    })
    readonly description: Promise<string>;

    @decorators.stringArrayProperty<TestNonVitalSubRef, 'stringArrayVal'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringArrayDataType({ maxLength: 50 }),
    })
    readonly stringArrayVal: Promise<string[]>;

    @decorators.shortProperty<TestNonVitalSubRef, 'shortVal'>({
        isStored: true,
        isPublished: true,
    })
    readonly shortVal: Promise<short>;

    @decorators.integerProperty<TestNonVitalSubRef, 'integerVal'>({
        isStored: true,
        isPublished: true,
    })
    readonly integerVal: Promise<integer>;

    @decorators.integerArrayProperty<TestNonVitalSubRef, 'integerArrayVal'>({
        isStored: true,
        isPublished: true,
    })
    readonly integerArrayVal: Promise<integer[]>;

    @decorators.decimalProperty<TestNonVitalSubRef, 'decimalVal'>({
        isStored: true,
        isPublished: true,
        dataType: () => decimalDataType,
    })
    readonly decimalVal: Promise<decimal>;

    @decorators.booleanProperty<TestNonVitalSubRef, 'booleanVal'>({
        isStored: true,
        isPublished: true,
    })
    readonly booleanVal: Promise<boolean>;

    @decorators.jsonProperty<TestNonVitalSubRef, 'jsonVal'>({
        isStored: true,
        isPublished: true,
    })
    readonly jsonVal: Promise<boolean>;

    @decorators.datetimeProperty<TestNonVitalSubRef, 'datetimeVal'>({
        isStored: true,
        isPublished: true,
    })
    readonly datetimeVal: Promise<datetime>;

    @decorators.dateRangeProperty<TestNonVitalSubRef, 'dateRangeVal'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly dateRangeVal: Promise<dateRange>;

    @decorators.datetimeRangeProperty<TestNonVitalSubRef, 'datetimeRangeVal'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly datetimeRangeVal: Promise<datetimeRange>;

    @decorators.enumProperty<TestNonVitalSubRef, 'enumVal'>({
        dataType: () => testEnumDataType,
        isStored: true,
        isPublished: true,
    })
    readonly enumVal: Promise<TestEnum>;

    @decorators.enumArrayProperty<TestNonVitalSubRef, 'enumArrayVal'>({
        dataType: () => testEnumDataType,
        isStored: true,
        isPublished: true,
        isNullable: true,
    })
    readonly enumArrayVal: Promise<TestEnum[]>;

    @decorators.stringProperty<TestNonVitalSubRef, 'transientInput'>({
        isPublished: true,
        isTransientInput: true,
        dataType: () => nameDataType,
    })
    readonly transientInput: Promise<string>;
}
