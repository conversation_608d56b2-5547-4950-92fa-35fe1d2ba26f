import { decorators, Node, StringDataType } from '@sage/xtrem-core';

@decorators.node<TestContentAddressable>({
    storage: 'sql',
    isPublished: true,
    isContentAddressable: true,
    canCreate: true,
    canRead: true,
    canUpdate: true,
})
export class TestContentAddressable extends Node {
    @decorators.stringProperty<TestContentAddressable, 'name'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringDataType({ maxLength: 80 }),
        isRequired: true,
    })
    readonly name: Promise<string>;

    @decorators.stringProperty<TestContentAddressable, 'description'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringDataType({ maxLength: 80 }),
        isRequired: true,
    })
    readonly description: Promise<string>;
}
