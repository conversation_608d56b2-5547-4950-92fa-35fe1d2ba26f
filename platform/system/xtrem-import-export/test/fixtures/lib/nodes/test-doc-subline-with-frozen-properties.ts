import { decorators, Node, Reference, StringDataType } from '@sage/xtrem-core';
import { TestDocLineWithFrozenProperties } from './test-doc-line-with-frozen-properties';
import { TestItem } from './test-item';

@decorators.node<TestDocSublineWithFrozenProperties>({
    isPublished: true,
    storage: 'sql',
    canRead: true,
    canCreate: true,
    canDelete: true,
    canUpdate: true,
    isVitalCollectionChild: true,
    indexes: [{ orderBy: { label: +1 }, isUnique: true }],
})
export class TestDocSublineWithFrozenProperties extends Node {
    @decorators.stringProperty<TestDocSublineWithFrozenProperties, 'label'>({
        isPublished: true,
        isStored: true,
        isRequired: true,
        dataType: () => new StringDataType({ maxLength: 80 }),
        async control(cx): Promise<void> {
            await cx.error.if(await this.label).is.equal.to('bad');
        },
    })
    readonly label: Promise<string>;

    @decorators.stringProperty<TestDocSublineWithFrozenProperties, 'description'>({
        isPublished: true,
        isStored: true,
        async control(cx, val): Promise<void> {
            await cx.error.if(val).is.equal.to('bad');
        },

        dataType: () => new StringDataType({ maxLength: 100 }),
    })
    readonly description: Promise<string>;

    @decorators.referenceProperty<TestDocSublineWithFrozenProperties, 'item'>({
        isStored: true,
        isPublished: true,
        node: () => TestItem,
        isNullable: true,
    })
    readonly item: Reference<TestItem | null>;

    @decorators.referenceProperty<TestDocSublineWithFrozenProperties, 'parentDocline'>({
        node: () => TestDocLineWithFrozenProperties,
        isVitalParent: true,
        isStored: true,
        isPublished: true,
    })
    readonly parentDocline: Reference<TestDocLineWithFrozenProperties>;
}
