import {
    Collection,
    dateRange,
    datetime,
    datetimeRange,
    decimal,
    DecimalDataType,
    decorators,
    integer,
    Node,
    short,
    StringArrayDataType,
    StringDataType,
} from '@sage/xtrem-core';
import { TestEnum, testEnumDataType } from '../enums';
import { TestDocComment } from './test-doc-comment';
import { TestDocLine } from './test-doc-line';

const decimalDataType = new DecimalDataType({ precision: 9, scale: 3 });
const nameDataType = new StringDataType({ maxLength: 80 });

@decorators.node<TestDoc>({
    isPublished: true,
    storage: 'sql',
    canRead: true,
    canCreate: true,
    canDelete: true,
    canUpdate: true,
    indexes: [
        { orderBy: { name: +1 }, isUnique: true, isNaturalKey: true },
        { orderBy: { code: +1 }, isUnique: true },
    ],
})
export class TestDoc extends Node {
    @decorators.stringProperty<TestDoc, 'name'>({
        isPublished: true,
        isStored: true,
        dataType: () => nameDataType,
        isRequired: true,
        async control(cx, val): Promise<void> {
            await cx.error.if(val).is.equal.to('bad');
        },
    })
    readonly name: Promise<string>;

    @decorators.stringProperty<TestDoc, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => nameDataType,
        isRequired: true,
    })
    readonly code: Promise<string>;

    @decorators.stringProperty<TestDoc, 'description'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringDataType({ maxLength: 100, isLocalized: true }),
        async control(cx, val): Promise<void> {
            await cx.error.if(val).is.equal.to('description');
        },
        defaultValue() {
            return 'default';
        },
    })
    readonly description: Promise<string>;

    @decorators.stringArrayProperty<TestDoc, 'stringArrayVal'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringArrayDataType({ maxLength: 10 }),
    })
    readonly stringArrayVal: Promise<string[]>;

    @decorators.shortProperty<TestDoc, 'shortVal'>({
        isStored: true,
        isPublished: true,
    })
    readonly shortVal: Promise<short>;

    @decorators.integerProperty<TestDoc, 'integerVal'>({
        isStored: true,
        isPublished: true,
    })
    readonly integerVal: Promise<integer>;

    @decorators.integerArrayProperty<TestDoc, 'integerArrayVal'>({
        isStored: true,
        isPublished: true,
    })
    readonly integerArrayVal: Promise<integer[]>;

    @decorators.decimalProperty<TestDoc, 'decimalVal'>({
        isStored: true,
        isPublished: true,
        dataType: () => decimalDataType,
    })
    readonly decimalVal: Promise<decimal>;

    @decorators.booleanProperty<TestDoc, 'booleanVal'>({
        isStored: true,
        isPublished: true,
    })
    readonly booleanVal: Promise<boolean>;

    @decorators.jsonProperty<TestDoc, 'jsonVal'>({
        isStored: true,
        isPublished: true,
    })
    readonly jsonVal: Promise<boolean>;

    @decorators.datetimeProperty<TestDoc, 'datetimeVal'>({
        isStored: true,
        isPublished: true,
    })
    readonly datetimeVal: Promise<datetime>;

    @decorators.dateRangeProperty<TestDoc, 'dateRangeVal'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly dateRangeVal: Promise<dateRange>;

    @decorators.datetimeRangeProperty<TestDoc, 'datetimeRangeVal'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly datetimeRangeVal: Promise<datetimeRange>;

    @decorators.enumProperty<TestDoc, 'enumVal'>({
        dataType: () => testEnumDataType,
        isStored: true,
        isPublished: true,
    })
    readonly enumVal: Promise<TestEnum>;

    @decorators.enumArrayProperty<TestDoc, 'enumArrayVal'>({
        dataType: () => testEnumDataType,
        isStored: true,
        isPublished: true,
        isNullable: true,
    })
    readonly enumArrayVal: Promise<TestEnum[]>;

    @decorators.collectionProperty<TestDoc, 'lines'>({
        node: () => TestDocLine,
        isPublished: true,
        isVital: true,
        reverseReference: 'parentDoc',
    })
    readonly lines: Collection<TestDocLine>;

    @decorators.collectionProperty<TestDoc, 'comments'>({
        node: () => TestDocComment,
        isPublished: true,

        isVital: true,
        reverseReference: 'parentDoc',
    })
    readonly comments: Collection<TestDocComment>;

    @decorators.stringProperty<TestDoc, 'transientInput'>({
        isPublished: true,
        isTransientInput: true,
        dataType: () => nameDataType,
    })
    readonly transientInput: Promise<string>;
}
