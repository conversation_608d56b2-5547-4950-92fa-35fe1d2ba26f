import {
    Collection,
    dateRange,
    datetime,
    datetimeRange,
    decimal,
    DecimalDataType,
    decorators,
    integer,
    Node,
    Reference,
    short,
    StringArrayDataType,
    StringDataType,
} from '@sage/xtrem-core';
import { TestEnum, testEnumDataType } from '../enums';
import { TestContentAddressable } from './test-content-addressable';
import { TestDocLineWithNonVitalRef } from './test-doc-line-with-non-vital-ref';
import { TestNonVitalRef } from './test-non-vital-ref';

const decimalDataType = new DecimalDataType({ precision: 9, scale: 3 });
const nameDataType = new StringDataType({ maxLength: 80 });

@decorators.node<TestDocWithNonVitalRef>({
    isPublished: true,
    storage: 'sql',
    canRead: true,
    canCreate: true,
    canDelete: true,
    canUpdate: true,
    indexes: [
        { orderBy: { name: +1 }, isUnique: true, isNaturalKey: true },
        { orderBy: { code: +1 }, isUnique: true },
    ],
})
export class TestDocWithNonVitalRef extends Node {
    @decorators.stringProperty<TestDocWithNonVitalRef, 'name'>({
        isPublished: true,
        isStored: true,
        dataType: () => nameDataType,
        isRequired: true,
        async control(cx, val): Promise<void> {
            await cx.error.if(val).is.equal.to('bad');
        },
    })
    readonly name: Promise<string>;

    @decorators.stringProperty<TestDocWithNonVitalRef, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => nameDataType,
        isRequired: true,
    })
    readonly code: Promise<string>;

    @decorators.stringProperty<TestDocWithNonVitalRef, 'description'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringDataType({ maxLength: 100, isLocalized: true }),
        async control(cx, val): Promise<void> {
            await cx.error.if(val).is.equal.to('description');
        },
        defaultValue() {
            return 'default';
        },
    })
    readonly description: Promise<string>;

    @decorators.stringArrayProperty<TestDocWithNonVitalRef, 'stringArrayVal'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringArrayDataType({ maxLength: 30 }),
    })
    readonly stringArrayVal: Promise<string[]>;

    @decorators.shortProperty<TestDocWithNonVitalRef, 'shortVal'>({
        isStored: true,
        isPublished: true,
    })
    readonly shortVal: Promise<short>;

    @decorators.integerProperty<TestDocWithNonVitalRef, 'integerVal'>({
        isStored: true,
        isPublished: true,
    })
    readonly integerVal: Promise<integer>;

    @decorators.integerArrayProperty<TestDocWithNonVitalRef, 'integerArrayVal'>({
        isStored: true,
        isPublished: true,
    })
    readonly integerArrayVal: Promise<integer[]>;

    @decorators.decimalProperty<TestDocWithNonVitalRef, 'decimalVal'>({
        isStored: true,
        isPublished: true,
        dataType: () => decimalDataType,
    })
    readonly decimalVal: Promise<decimal>;

    @decorators.booleanProperty<TestDocWithNonVitalRef, 'booleanVal'>({
        isStored: true,
        isPublished: true,
    })
    readonly booleanVal: Promise<boolean>;

    @decorators.jsonProperty<TestDocWithNonVitalRef, 'jsonVal'>({
        isStored: true,
        isPublished: true,
    })
    readonly jsonVal: Promise<boolean>;

    @decorators.datetimeProperty<TestDocWithNonVitalRef, 'datetimeVal'>({
        isStored: true,
        isPublished: true,
    })
    readonly datetimeVal: Promise<datetime>;

    @decorators.dateRangeProperty<TestDocWithNonVitalRef, 'dateRangeVal'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly dateRangeVal: Promise<dateRange>;

    @decorators.datetimeRangeProperty<TestDocWithNonVitalRef, 'datetimeRangeVal'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly datetimeRangeVal: Promise<datetimeRange>;

    @decorators.enumProperty<TestDocWithNonVitalRef, 'enumVal'>({
        dataType: () => testEnumDataType,
        isStored: true,
        isPublished: true,
    })
    readonly enumVal: Promise<TestEnum>;

    @decorators.enumArrayProperty<TestDocWithNonVitalRef, 'enumArrayVal'>({
        dataType: () => testEnumDataType,
        isStored: true,
        isPublished: true,
        isNullable: true,
    })
    readonly enumArrayVal: Promise<TestEnum[]>;

    @decorators.collectionProperty<TestDocWithNonVitalRef, 'lines'>({
        node: () => TestDocLineWithNonVitalRef,
        isPublished: true,
        isVital: true,
        reverseReference: 'parentDoc',
    })
    readonly lines: Collection<TestDocLineWithNonVitalRef>;

    /** editable content  */
    @decorators.referenceProperty<TestDocWithNonVitalRef, 'testContentAddressable'>({
        isPublished: true,
        isStored: true,
        isMutable: true,
        isNullable: true,
        node: () => TestContentAddressable,
    })
    readonly testContentAddressable: Reference<TestContentAddressable | null>;

    @decorators.stringProperty<TestDocWithNonVitalRef, 'transientInput'>({
        isPublished: true,
        isTransientInput: true,
        dataType: () => nameDataType,
    })
    readonly transientInput: Promise<string>;

    @decorators.referenceProperty<TestDocWithNonVitalRef, 'nonVitalRef'>({
        isPublished: true,
        node: () => TestNonVitalRef,
        isStored: true,
        isNullable: true,
        allowedInUniqueIndex: true,
    })
    readonly nonVitalRef: Reference<TestNonVitalRef | null>;
}
