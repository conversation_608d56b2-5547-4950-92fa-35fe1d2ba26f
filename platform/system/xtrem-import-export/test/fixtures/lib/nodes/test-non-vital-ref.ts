import {
    Collection,
    dateRange,
    datetime,
    datetimeRange,
    decimal,
    DecimalDataType,
    decorators,
    integer,
    Node,
    Reference,
    short,
    StringArrayDataType,
    StringDataType,
} from '@sage/xtrem-core';
import { TestEnum, testEnumDataType } from '../enums';
import { TestNonVitalRefComment } from './test-non-vital-ref-comment';
import { TestNonVitalSubRef } from './test-non-vital-sub-ref';
import { TestNonVitalSubRefNoNaturalKey } from './test-non-vital-sub-ref-no-natural-key';

const decimalDataType = new DecimalDataType({ precision: 9, scale: 3 });
const nameDataType = new StringDataType({ maxLength: 80 });

@decorators.node<TestNonVitalRef>({
    isPublished: true,
    storage: 'sql',
    canRead: true,
    canCreate: true,
    canDelete: true,
    canUpdate: true,
    isCustomizable: true,
    indexes: [
        { orderBy: { code: +1, name: +1, subRef: +1 }, isUnique: true, isNaturalKey: true },
        { orderBy: { name: +1 }, isUnique: true },
    ],
})
export class TestNonVitalRef extends Node {
    @decorators.stringProperty<TestNonVitalRef, 'name'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringDataType({ maxLength: 80 }),
    })
    readonly name: Promise<string>;

    @decorators.stringProperty<TestNonVitalRef, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringDataType({ maxLength: 80 }),
        isRequired: false,
    })
    readonly code: Promise<string>;

    @decorators.stringProperty<TestNonVitalRef, 'description'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringDataType({ maxLength: 100, isLocalized: true }),
        async control(cx, val): Promise<void> {
            await cx.error.if(val).is.equal.to('description');
        },
        defaultValue() {
            return 'default';
        },
    })
    readonly description: Promise<string>;

    @decorators.stringArrayProperty<TestNonVitalRef, 'stringArrayVal'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringArrayDataType({ maxLength: 50 }),
    })
    readonly stringArrayVal: Promise<string[]>;

    @decorators.shortProperty<TestNonVitalRef, 'shortVal'>({
        isStored: true,
        isPublished: true,
    })
    readonly shortVal: Promise<short>;

    @decorators.integerProperty<TestNonVitalRef, 'integerVal'>({
        isStored: true,
        isPublished: true,
    })
    readonly integerVal: Promise<integer>;

    @decorators.integerArrayProperty<TestNonVitalRef, 'integerArrayVal'>({
        isStored: true,
        isPublished: true,
    })
    readonly integerArrayVal: Promise<integer[]>;

    @decorators.decimalProperty<TestNonVitalRef, 'decimalVal'>({
        isStored: true,
        isPublished: true,
        dataType: () => decimalDataType,
    })
    readonly decimalVal: Promise<decimal>;

    @decorators.booleanProperty<TestNonVitalRef, 'booleanVal'>({
        isStored: true,
        isPublished: true,
    })
    readonly booleanVal: Promise<boolean>;

    @decorators.jsonProperty<TestNonVitalRef, 'jsonVal'>({
        isStored: true,
        isPublished: true,
    })
    readonly jsonVal: Promise<boolean>;

    @decorators.datetimeProperty<TestNonVitalRef, 'datetimeVal'>({
        isStored: true,
        isPublished: true,
    })
    readonly datetimeVal: Promise<datetime>;

    @decorators.dateRangeProperty<TestNonVitalRef, 'dateRangeVal'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly dateRangeVal: Promise<dateRange>;

    @decorators.datetimeRangeProperty<TestNonVitalRef, 'datetimeRangeVal'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly datetimeRangeVal: Promise<datetimeRange>;

    @decorators.enumProperty<TestNonVitalRef, 'enumVal'>({
        dataType: () => testEnumDataType,
        isStored: true,
        isPublished: true,
    })
    readonly enumVal: Promise<TestEnum>;

    @decorators.enumArrayProperty<TestNonVitalRef, 'enumArrayVal'>({
        dataType: () => testEnumDataType,
        isStored: true,
        isPublished: true,
        isNullable: true,
    })
    readonly enumArrayVal: Promise<TestEnum[]>;

    @decorators.collectionProperty<TestNonVitalRef, 'comments'>({
        node: () => TestNonVitalRefComment,
        isPublished: true,
        isVital: true,
        reverseReference: 'parentNonVitalRef',
    })
    readonly comments: Collection<TestNonVitalRefComment>;

    @decorators.stringProperty<TestNonVitalRef, 'transientInput'>({
        isPublished: true,
        isTransientInput: true,
        dataType: () => nameDataType,
    })
    readonly transientInput: Promise<string>;

    @decorators.referenceProperty<TestNonVitalRef, 'subRef'>({
        isPublished: true,
        node: () => TestNonVitalSubRef,
        isStored: true,
        isNullable: true,
        allowedInUniqueIndex: true,
    })
    readonly subRef: Reference<TestNonVitalSubRef | null>;

    @decorators.referenceProperty<TestNonVitalRef, 'subRefNoNaturalKey'>({
        isPublished: true,
        node: () => TestNonVitalSubRefNoNaturalKey,
        isStored: true,
        isNullable: true,
    })
    readonly subRefNoNaturalKey: Reference<TestNonVitalSubRefNoNaturalKey | null>;
}
