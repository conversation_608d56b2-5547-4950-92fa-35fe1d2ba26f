import { decorators, Node, Reference, StringDataType } from '@sage/xtrem-core';
import { TestItemRef } from './test-item-ref';
import { TestItemRefNoNaturalKey } from './test-item-ref-no-nautural-key';

@decorators.node<TestItem>({
    isPublished: true,
    storage: 'sql',
    canRead: true,
    canCreate: true,
    canDelete: true,
    canUpdate: true,
    indexes: [
        { orderBy: { code: +1, name: +1, ref: +1 }, isUnique: true, isNaturalKey: true },
        { orderBy: { name: +1 }, isUnique: true },
    ],
})
export class TestItem extends Node {
    @decorators.stringProperty<TestItem, 'name'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringDataType({ maxLength: 80 }),
    })
    readonly name: Promise<string>;

    @decorators.stringProperty<TestItem, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringDataType({ maxLength: 80 }),
        isRequired: false,
    })
    readonly code: Promise<string>;

    @decorators.referenceProperty<TestItem, 'ref'>({
        isPublished: true,
        node: () => TestItemRef,
        isStored: true,
        isNullable: true,
        allowedInUniqueIndex: true,
    })
    readonly ref: Reference<TestItemRef | null>;

    @decorators.referenceProperty<TestItem, 'refNoNatural'>({
        isPublished: true,
        node: () => TestItemRefNoNaturalKey,
        isStored: true,
        isNullable: true,
    })
    readonly refNoNatural: Reference<TestItemRefNoNaturalKey | null>;
}
