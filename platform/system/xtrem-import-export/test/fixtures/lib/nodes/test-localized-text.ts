import { decorators, Node, StringDataType } from '@sage/xtrem-core';

@decorators.node<TestLocalizedText>({
    isPublished: true,
    storage: 'sql',
    canRead: true,
    canCreate: true,
    canDelete: true,
    canDeleteMany: true,
    canUpdate: true,
    indexes: [{ orderBy: { name: +1 }, isUnique: true, isNaturalKey: true }],
})
export class TestLocalizedText extends Node {
    @decorators.stringProperty<TestLocalizedText, 'name'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringDataType({ maxLength: 80 }),
        isRequired: true,
    })
    readonly name: Promise<string>;

    @decorators.stringProperty<TestLocalizedText, 'description'>({
        isPublished: true,
        isNotEmpty: false,
        isStored: true,
        dataType: () => new StringDataType({ maxLength: 250, isLocalized: true }),
    })
    readonly description: Promise<string>;
}
