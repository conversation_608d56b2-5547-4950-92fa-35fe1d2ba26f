import { decorators, Node, StringDataType } from '@sage/xtrem-core';

@decorators.node<TestItemRef>({
    isPublished: true,
    storage: 'sql',
    canRead: true,
    canCreate: true,
    canDelete: true,
    canUpdate: true,
    indexes: [
        { orderBy: { refName: +1 }, isUnique: true, isNaturalKey: true },
        { orderBy: { refCode: +1 }, isUnique: true },
    ],
})
export class TestItemRef extends Node {
    @decorators.stringProperty<TestItemRef, 'refName'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringDataType({ maxLength: 80 }),
    })
    readonly refName: Promise<string>;

    @decorators.stringProperty<TestItemRef, 'refCode'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringDataType({ maxLength: 80 }),
        isRequired: false,
    })
    readonly refCode: Promise<string>;
}
