import { decorators, Node, Reference, StringDataType } from '@sage/xtrem-core';
import { TestDocVitalRef } from './test-doc-vital-ref';

@decorators.node<TestDocVitalRefChild>({
    isPublished: true,
    storage: 'sql',
    canRead: true,
    canCreate: true,
    canDelete: true,
    canUpdate: true,
    isCustomizable: true,
    isVitalReferenceChild: true,
})
export class TestDocVitalRefChild extends Node {
    @decorators.stringProperty<TestDocVitalRefChild, 'childRefName'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringDataType({ maxLength: 80 }),
    })
    readonly childRefName: Promise<string>;

    @decorators.referenceProperty<TestDocVitalRefChild, 'parentDoc'>({
        node: () => TestDocVitalRef,
        isPublished: true,
        isVitalParent: true,
        isStored: true,
    })
    readonly parentDoc: Reference<TestDocVitalRef>;

    @decorators.stringProperty<TestDocVitalRefChild, 'description'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringDataType({ maxLength: 100, isLocalized: true }),
        async control(cx, val): Promise<void> {
            await cx.error.if(val).is.equal.to('description');
        },
        defaultValue() {
            return 'default';
        },
    })
    readonly description: Promise<string>;
}
