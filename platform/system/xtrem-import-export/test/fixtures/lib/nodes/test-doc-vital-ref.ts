import { decorators, Node, Reference, StringDataType } from '@sage/xtrem-core';
import { TestDocVitalRefChild } from './test-vital-ref-child';

@decorators.node<TestDocVitalRef>({
    isPublished: true,
    storage: 'sql',
    canRead: true,
    canCreate: true,
    canDelete: true,
    canUpdate: true,
    isCustomizable: true,
    indexes: [{ orderBy: { name: +1 }, isUnique: true, isNaturalKey: true }],
})
export class TestDocVitalRef extends Node {
    @decorators.stringProperty<TestDocVitalRef, 'name'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringDataType({ maxLength: 80 }),
    })
    readonly name: Promise<string>;

    @decorators.stringProperty<TestDocVitalRef, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringDataType({ maxLength: 80 }),
        isRequired: false,
    })
    readonly code: Promise<string>;

    @decorators.referenceProperty<TestDocVitalRef, 'vitalRef'>({
        isPublished: true,
        node: () => TestDocVitalRefChild,
        reverseReference: 'parentDoc',
        isNullable: true,
        isVital: true,
    })
    readonly vitalRef: Reference<TestDocVitalRefChild | null>;
}
