import { decorators, Node, Reference, StringDataType } from '@sage/xtrem-core';
import { TestContentAddressable } from './test-content-addressable';
import { TestDocLineWithNonVitalRef } from './test-doc-line-with-non-vital-ref';
import { TestItem } from './test-item';
import { TestNonVitalRef } from './test-non-vital-ref';

@decorators.node<TestDocSubLineWithNonVitalRef>({
    isPublished: true,
    storage: 'sql',
    canRead: true,
    canCreate: true,
    canDelete: true,
    canUpdate: true,
    isVitalCollectionChild: true,
    indexes: [{ orderBy: { label: +1 }, isUnique: true }],
})
export class TestDocSubLineWithNonVitalRef extends Node {
    @decorators.stringProperty<TestDocSubLineWithNonVitalRef, 'label'>({
        isPublished: true,
        isStored: true,
        isRequired: true,
        dataType: () => new StringDataType({ maxLength: 80 }),
        async control(cx): Promise<void> {
            await cx.error.if(await this.label).is.equal.to('bad');
        },
    })
    readonly label: Promise<string>;

    @decorators.stringProperty<TestDocSubLineWithNonVitalRef, 'description'>({
        isPublished: true,
        isStored: true,
        async control(cx, val): Promise<void> {
            await cx.error.if(val).is.equal.to('bad');
        },

        dataType: () => new StringDataType({ maxLength: 100 }),
    })
    readonly description: Promise<string>;

    @decorators.referenceProperty<TestDocSubLineWithNonVitalRef, 'item'>({
        isStored: true,
        isPublished: true,
        node: () => TestItem,
        isNullable: true,
    })
    readonly item: Reference<TestItem | null>;

    @decorators.referenceProperty<TestDocSubLineWithNonVitalRef, 'parentDocLine'>({
        node: () => TestDocLineWithNonVitalRef,
        isVitalParent: true,
        isStored: true,
        isPublished: true,
    })
    readonly parentDocLine: Reference<TestDocLineWithNonVitalRef>;

    @decorators.referenceProperty<TestDocSubLineWithNonVitalRef, 'testDocSubLineWithNonVitalRef'>({
        node: () => TestDocSubLineWithNonVitalRef,
        isStored: true,
        isPublished: true,
        isNullable: true,
    })
    readonly testDocSubLineWithNonVitalRef: Reference<TestDocSubLineWithNonVitalRef | null>;

    /** editable content  */
    @decorators.referenceProperty<TestDocSubLineWithNonVitalRef, 'testContentAddressable'>({
        isPublished: true,
        isStored: true,
        isMutable: true,
        isNullable: true,
        node: () => TestContentAddressable,
    })
    readonly testContentAddressable: Reference<TestContentAddressable | null>;

    @decorators.referenceProperty<TestDocSubLineWithNonVitalRef, 'nonVitalRef'>({
        isPublished: true,
        node: () => TestNonVitalRef,
        isStored: true,
        isNullable: true,
        allowedInUniqueIndex: true,
    })
    readonly nonVitalRef: Reference<TestNonVitalRef | null>;
}
