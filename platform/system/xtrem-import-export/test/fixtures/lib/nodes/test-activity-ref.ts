import { decorators, Node, StringDataType } from '@sage/xtrem-core';
import { nameDataType } from '../../../../lib/data-types/_index';

@decorators.node<TestActivityRef>({
    isPublished: true,
    storage: 'sql',
    canRead: true,
    canCreate: true,
    canDelete: true,
    canUpdate: true,
    indexes: [{ orderBy: { name: +1 }, isUnique: true, isNaturalKey: true }],
})
export class TestActivityRef extends Node {
    @decorators.stringProperty<TestActivityRef, 'name'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringDataType({ maxLength: 80 }),
        isRequired: true,
    })
    readonly name: Promise<string>;

    @decorators.stringProperty<TestActivityRef, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => nameDataType,
    })
    readonly code: Promise<string>;

    @decorators.stringProperty<TestActivityRef, 'description'>({
        isPublished: true,
        isStored: true,
        lookupAccess: true,
        dataType: () => nameDataType,
    })
    readonly description: Promise<string>;
}
