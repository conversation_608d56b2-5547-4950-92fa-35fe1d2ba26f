import { decorators, Node, StringDataType } from '@sage/xtrem-core';

@decorators.node<TestItemRefNoNaturalKey>({
    isPublished: true,
    storage: 'sql',
    canRead: true,
    canCreate: true,
    canDelete: true,
    canUpdate: true,
    indexes: [
        { orderBy: { refName: +1 }, isUnique: true },
        { orderBy: { refCode: +1 }, isUnique: true },
    ],
})
export class TestItemRefNoNaturalKey extends Node {
    @decorators.stringProperty<TestItemRefNoNaturalKey, 'refName'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringDataType({ maxLength: 80 }),
    })
    readonly refName: Promise<string>;

    @decorators.stringProperty<TestItemRefNoNaturalKey, 'refCode'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringDataType({ maxLength: 80 }),
        isRequired: false,
    })
    readonly refCode: Promise<string>;
}
