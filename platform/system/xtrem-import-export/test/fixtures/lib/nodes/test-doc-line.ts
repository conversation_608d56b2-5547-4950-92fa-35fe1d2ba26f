import { Collection, decorators, Node, Reference, StringDataType } from '@sage/xtrem-core';
import { TestContentAddressable } from './test-content-addressable';
import { TestDoc } from './test-doc';
import { TestDocSubline } from './test-doc-subline';

@decorators.node<TestDocLine>({
    isPublished: true,
    storage: 'sql',
    canRead: true,
    canCreate: true,
    canDelete: true,
    canUpdate: true,
    isVitalCollectionChild: true,
    indexes: [{ orderBy: { label: +1 }, isUnique: true }],
})
export class TestDocLine extends Node {
    @decorators.stringProperty<TestDocLine, 'label'>({
        isPublished: true,
        isStored: true,
        isRequired: true,
        dataType: () => new StringDataType({ maxLength: 30 }),
    })
    readonly label: Promise<string>;

    @decorators.collectionProperty<TestDocLine, 'sublines'>({
        node: () => TestDocSubline,

        isPublished: true,
        isVital: true,

        reverseReference: 'parentDocline',
    })
    readonly sublines: Collection<TestDocSubline>;

    @decorators.referenceProperty<TestDocLine, 'parentDoc'>({
        node: () => TestDoc,
        isVitalParent: true,
        isStored: true,
        isPublished: true,
    })
    readonly parentDoc: Reference<TestDoc>;

    /** editable content  */
    @decorators.referenceProperty<TestDocLine, 'testContentAddressable'>({
        isPublished: true,
        isStored: true,
        isMutable: true,
        isNullable: true,
        node: () => TestContentAddressable,
    })
    readonly testContentAddressable: Reference<TestContentAddressable | null>;
}
