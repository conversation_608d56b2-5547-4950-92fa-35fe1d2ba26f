import { decorators, Node, StringDataType } from '@sage/xtrem-core';

@decorators.node<TestLocalizedTextNotEmpty>({
    isPublished: true,
    storage: 'sql',
    canRead: true,
    canCreate: true,
    canDelete: true,
    canDeleteMany: true,
    canUpdate: true,
    indexes: [{ orderBy: { name: +1 }, isUnique: true, isNaturalKey: true }],
})
export class TestLocalizedTextNotEmpty extends Node {
    @decorators.stringProperty<TestLocalizedTextNotEmpty, 'name'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringDataType({ maxLength: 80 }),
        isRequired: true,
    })
    readonly name: Promise<string>;

    @decorators.stringProperty<TestLocalizedTextNotEmpty, 'description'>({
        isPublished: true,
        isNotEmpty: true,
        isStored: true,
        dataType: () => new StringDataType({ maxLength: 250, isLocalized: true }),
    })
    readonly description: Promise<string>;
}
