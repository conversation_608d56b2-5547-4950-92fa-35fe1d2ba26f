import { decorators, Node, Reference, StringDataType } from '@sage/xtrem-core';
import { TestContentAddressable } from './test-content-addressable';
import { TestDocWithFrozenProperties } from './test-doc-with-frozen-properties';

@decorators.node<TestDocCommentWithFrozenProperties>({
    isPublished: true,
    storage: 'sql',
    canRead: true,
    canCreate: true,
    canDelete: true,
    canUpdate: true,
    isVitalCollectionChild: true,
})
export class TestDocCommentWithFrozenProperties extends Node {
    @decorators.stringProperty<TestDocCommentWithFrozenProperties, 'text'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringDataType({ maxLength: 80 }),
    })
    readonly text: Promise<string>;

    @decorators.referenceProperty<TestDocCommentWithFrozenProperties, 'parentDoc'>({
        node: () => TestDocWithFrozenProperties,
        isPublished: true,
        isVitalParent: true,
        isStored: true,
    })
    readonly parentDoc: Reference<TestDocWithFrozenProperties>;

    /** editable content  */
    @decorators.referenceProperty<TestDocCommentWithFrozenProperties, 'testContentAddressableDelegated'>({
        isPublished: true,
        isStored: true,
        isMutable: true,
        node: () => TestContentAddressable,
    })
    readonly testContentAddressableDelegated: Reference<TestContentAddressable | null>;

    @decorators.stringProperty<TestDocCommentWithFrozenProperties, 'name'>({
        isPublished: true,
        delegatesTo: { testContentAddressableDelegated: 'name' },
    })
    readonly name: Promise<string>;

    @decorators.stringProperty<TestDocCommentWithFrozenProperties, 'description'>({
        isPublished: true,
        delegatesTo: { testContentAddressableDelegated: 'description' },
    })
    readonly description: Promise<string>;
}
