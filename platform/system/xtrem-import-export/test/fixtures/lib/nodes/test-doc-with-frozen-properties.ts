import {
    Collection,
    dateRange,
    datetime,
    datetimeRange,
    decimal,
    DecimalDataType,
    decorators,
    integer,
    Node,
    Reference,
    short,
    StringArrayDataType,
    StringDataType,
} from '@sage/xtrem-core';
import { TestEnum, testEnumDataType } from '../enums';
import { TestDocCommentWithFrozenProperties } from './test-doc-comment-with-frozen-properties';
import { TestDocLineWithFrozenProperties } from './test-doc-line-with-frozen-properties';
import { TestItem } from './test-item';

const decimalDataType = new DecimalDataType({ precision: 9, scale: 3 });
const nameDataType = new StringDataType({ maxLength: 80 });

@decorators.node<TestDocWithFrozenProperties>({
    isPublished: true,
    storage: 'sql',
    canRead: true,
    canCreate: true,
    canDelete: true,
    canUpdate: true,
    indexes: [
        { orderBy: { name: +1 }, isUnique: true, isNaturalKey: true },
        { orderBy: { code: +1 }, isUnique: true },
    ],
})
export class TestDocWithFrozenProperties extends Node {
    @decorators.stringProperty<TestDocWithFrozenProperties, 'name'>({
        isPublished: true,
        isStored: true,
        isFrozen: true,
        dataType: () => nameDataType,
        isRequired: true,
        async control(cx, val): Promise<void> {
            await cx.error.if(val).is.equal.to('bad');
        },
    })
    readonly name: Promise<string>;

    @decorators.stringProperty<TestDocWithFrozenProperties, 'code'>({
        isPublished: true,
        isStored: true,
        isFrozen: true,
        dataType: () => nameDataType,
        isRequired: true,
    })
    readonly code: Promise<string>;

    @decorators.stringProperty<TestDocWithFrozenProperties, 'description'>({
        isPublished: true,
        isStored: true,
        isFrozen: true,
        dataType: () => new StringDataType({ maxLength: 100, isLocalized: true }),
        async control(cx, val): Promise<void> {
            await cx.error.if(val).is.equal.to('description');
        },
        defaultValue() {
            return 'default';
        },
    })
    readonly description: Promise<string>;

    @decorators.stringArrayProperty<TestDocWithFrozenProperties, 'stringArrayVal'>({
        isPublished: true,
        isStored: true,
        isFrozen: true,
        dataType: () => new StringArrayDataType({ maxLength: 10 }),
    })
    readonly stringArrayVal: Promise<string[]>;

    @decorators.shortProperty<TestDocWithFrozenProperties, 'shortVal'>({
        isStored: true,
        isPublished: true,
        isFrozen: true,
    })
    readonly shortVal: Promise<short>;

    @decorators.integerProperty<TestDocWithFrozenProperties, 'integerVal'>({
        isStored: true,
        isPublished: true,
        isFrozen: true,
    })
    readonly integerVal: Promise<integer>;

    @decorators.integerArrayProperty<TestDocWithFrozenProperties, 'integerArrayVal'>({
        isStored: true,
        isPublished: true,
        isFrozen: true,
    })
    readonly integerArrayVal: Promise<integer[]>;

    @decorators.decimalProperty<TestDocWithFrozenProperties, 'decimalVal'>({
        isStored: true,
        isPublished: true,
        dataType: () => decimalDataType,
        isFrozen: true,
    })
    readonly decimalVal: Promise<decimal>;

    @decorators.booleanProperty<TestDocWithFrozenProperties, 'booleanVal'>({
        isStored: true,
        isPublished: true,
        isFrozen: true,
    })
    readonly booleanVal: Promise<boolean>;

    @decorators.jsonProperty<TestDocWithFrozenProperties, 'jsonVal'>({
        isStored: true,
        isPublished: true,
        isFrozen: true,
    })
    readonly jsonVal: Promise<boolean>;

    @decorators.datetimeProperty<TestDocWithFrozenProperties, 'datetimeVal'>({
        isStored: true,
        isPublished: true,
        isFrozen: true,
    })
    readonly datetimeVal: Promise<datetime>;

    @decorators.dateRangeProperty<TestDocWithFrozenProperties, 'dateRangeVal'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        isFrozen: true,
    })
    readonly dateRangeVal: Promise<dateRange>;

    @decorators.datetimeRangeProperty<TestDocWithFrozenProperties, 'datetimeRangeVal'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        isFrozen: true,
    })
    readonly datetimeRangeVal: Promise<datetimeRange>;

    @decorators.enumProperty<TestDocWithFrozenProperties, 'enumVal'>({
        dataType: () => testEnumDataType,
        isStored: true,
        isPublished: true,
        isFrozen: true,
    })
    readonly enumVal: Promise<TestEnum>;

    @decorators.enumArrayProperty<TestDocWithFrozenProperties, 'enumArrayVal'>({
        dataType: () => testEnumDataType,
        isStored: true,
        isPublished: true,
        isNullable: true,
        isFrozen: true,
    })
    readonly enumArrayVal: Promise<TestEnum[]>;

    @decorators.collectionProperty<TestDocWithFrozenProperties, 'lines'>({
        node: () => TestDocLineWithFrozenProperties,
        isPublished: true,
        isVital: true,
        reverseReference: 'parentDoc',
    })
    readonly lines: Collection<TestDocLineWithFrozenProperties>;

    @decorators.collectionProperty<TestDocWithFrozenProperties, 'comments'>({
        node: () => TestDocCommentWithFrozenProperties,
        isPublished: true,
        isVital: true,
        reverseReference: 'parentDoc',
    })
    readonly comments: Collection<TestDocCommentWithFrozenProperties>;

    @decorators.stringProperty<TestDocWithFrozenProperties, 'transientInput'>({
        isPublished: true,
        isTransientInput: true,
        dataType: () => nameDataType,
    })
    readonly transientInput: Promise<string>;

    @decorators.referenceProperty<TestDocWithFrozenProperties, 'itemWithDefaultValue'>({
        isStored: true,
        isPublished: true,
        isFrozen: true,
        node: () => TestItem,
        defaultValue() {
            return this.$.context.read(TestItem, { name: 'NAM1' });
        },
    })
    readonly itemWithDefaultValue: Reference<TestItem>;
}
