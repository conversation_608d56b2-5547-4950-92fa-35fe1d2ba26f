import { decorators, Node, Reference, StringDataType } from '@sage/xtrem-core';
import { serviceOptions } from '@sage/xtrem-system';
import { TestServiceOption } from './test-service-option';

const { isDemoTenant } = serviceOptions;

@decorators.node<TestServiceOptionLine>({
    serviceOptions: () => [isDemoTenant],
    isPublished: true,
    storage: 'sql',
    canRead: true,
    canCreate: true,
    canDelete: true,
    canUpdate: true,
    isVitalCollectionChild: true,
    indexes: [{ orderBy: { label: +1 }, isUnique: true }],
})
export class TestServiceOptionLine extends Node {
    @decorators.stringProperty<TestServiceOptionLine, 'label'>({
        isPublished: true,
        isStored: true,
        isRequired: true,
        dataType: () => new StringDataType({ maxLength: 30 }),
    })
    readonly label: Promise<string>;

    @decorators.referenceProperty<TestServiceOptionLine, 'testServiceOption'>({
        node: () => TestServiceOption,
        isVitalParent: true,
        isStored: true,
        isPublished: true,
    })
    readonly testServiceOption: Reference<TestServiceOption>;
}
