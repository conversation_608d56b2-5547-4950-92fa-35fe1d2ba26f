import { decorators, Node, Reference, StringDataType } from '@sage/xtrem-core';
import { TestDocRefDefaultValue } from './test-doc-ref-default-value';
import { TestItem } from './test-item';

@decorators.node<TestDocLineRefDefaultValue>({
    isPublished: true,
    storage: 'sql',
    canRead: true,
    canCreate: true,
    canDelete: true,
    canUpdate: true,
    isVitalCollectionChild: true,
    indexes: [{ orderBy: { label: +1 }, isUnique: true }],
})
export class TestDocLineRefDefaultValue extends Node {
    @decorators.stringProperty<TestDocLineRefDefaultValue, 'label'>({
        isPublished: true,
        isStored: true,
        isRequired: true,
        dataType: () => new StringDataType({ maxLength: 80 }),
        async control(cx): Promise<void> {
            await cx.error.if(await this.label).is.equal.to('bad');
        },
    })
    readonly label: Promise<string>;

    @decorators.stringProperty<TestDocLineRefDefaultValue, 'description'>({
        isPublished: true,
        isStored: true,
        async control(cx, val): Promise<void> {
            await cx.error.if(val).is.equal.to('bad');
        },

        dataType: () => new StringDataType({ maxLength: 100 }),
    })
    readonly description: Promise<string>;

    @decorators.referenceProperty<TestDocLineRefDefaultValue, 'item'>({
        isStored: true,
        isPublished: true,
        node: () => TestItem,
        isNullable: true,
    })
    readonly item: Reference<TestItem | null>;

    @decorators.referenceProperty<TestDocLineRefDefaultValue, 'parentDoc'>({
        node: () => TestDocRefDefaultValue,
        isVitalParent: true,
        isStored: true,
        isPublished: true,
    })
    readonly parentDoc: Reference<TestDocRefDefaultValue>;

    @decorators.referenceProperty<TestDocLineRefDefaultValue, 'itemWithDefaultValueLineLevel'>({
        isStored: true,
        isPublished: true,
        node: () => TestItem,
        isNullable: true,
        defaultValue() {
            return this.$.context.read(TestItem, { name: 'NAM1' });
        },
    })
    readonly itemWithDefaultValueLineLevel: Reference<TestItem | null>;
}
