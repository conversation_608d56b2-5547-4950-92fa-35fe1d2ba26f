import { Collection, decorators, Node, Reference, StringDataType } from '@sage/xtrem-core';
import { TestContentAddressable } from './test-content-addressable';
import { TestDocSublineWithFrozenProperties } from './test-doc-subline-with-frozen-properties';
import { TestDocWithFrozenProperties } from './test-doc-with-frozen-properties';

@decorators.node<TestDocLineWithFrozenProperties>({
    isPublished: true,
    storage: 'sql',
    canRead: true,
    canCreate: true,
    canDelete: true,
    canUpdate: true,
    isVitalCollectionChild: true,
    indexes: [{ orderBy: { label: +1 }, isUnique: true }],
})
export class TestDocLineWithFrozenProperties extends Node {
    @decorators.stringProperty<TestDocLineWithFrozenProperties, 'label'>({
        isPublished: true,
        isStored: true,
        isRequired: true,
        dataType: () => new StringDataType({ maxLength: 30 }),
    })
    readonly label: Promise<string>;

    @decorators.collectionProperty<TestDocLineWithFrozenProperties, 'sublines'>({
        node: () => TestDocSublineWithFrozenProperties,
        isPublished: true,
        isVital: true,
        reverseReference: 'parentDocline',
    })
    readonly sublines: Collection<TestDocSublineWithFrozenProperties>;

    @decorators.referenceProperty<TestDocLineWithFrozenProperties, 'parentDoc'>({
        node: () => TestDocWithFrozenProperties,
        isVitalParent: true,
        isStored: true,
        isPublished: true,
    })
    readonly parentDoc: Reference<TestDocWithFrozenProperties>;

    /** editable content  */
    @decorators.referenceProperty<TestDocLineWithFrozenProperties, 'testContentAddressable'>({
        isPublished: true,
        isStored: true,
        isMutable: true,
        isNullable: true,
        node: () => TestContentAddressable,
    })
    readonly testContentAddressable: Reference<TestContentAddressable | null>;
}
