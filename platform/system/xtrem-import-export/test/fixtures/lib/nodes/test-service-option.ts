import { Collection, decorators, Node, StringDataType } from '@sage/xtrem-core';
import { serviceOptions } from '@sage/xtrem-system';
import { nameDataType } from '../../../../lib/data-types/_index';
import { TestServiceOptionLine } from './test-service-option-line';

const { isDemoTenant } = serviceOptions;

@decorators.node<TestServiceOption>({
    isPublished: true,
    storage: 'sql',
    canRead: true,
    canCreate: true,
    canDelete: true,
    canUpdate: true,
    indexes: [{ orderBy: { name: +1 }, isUnique: true, isNaturalKey: true }],
})
export class TestServiceOption extends Node {
    @decorators.stringProperty<TestServiceOption, 'name'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringDataType({ maxLength: 80 }),
        isRequired: true,
    })
    readonly name: Promise<string>;

    @decorators.stringProperty<TestServiceOption, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => nameDataType,
        serviceOptions: () => [isDemoTenant],
    })
    readonly code: Promise<string>;

    @decorators.collectionProperty<TestServiceOption, 'lines'>({
        node: () => TestServiceOptionLine,
        isPublished: true,
        isVital: true,
        reverseReference: 'testServiceOption',
    })
    readonly lines: Collection<TestServiceOptionLine>;
}
