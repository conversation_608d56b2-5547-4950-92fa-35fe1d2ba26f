import { decorators, Node, Reference, StringDataType } from '@sage/xtrem-core';
import { TestContentAddressable } from './test-content-addressable';
import { TestDoc } from './test-doc';

@decorators.node<TestDocComment>({
    isPublished: true,
    storage: 'sql',
    canRead: true,
    canCreate: true,
    canDelete: true,
    canUpdate: true,
    isVitalCollectionChild: true,
})
export class TestDocComment extends Node {
    @decorators.stringProperty<TestDocComment, 'text'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringDataType({ maxLength: 80 }),
    })
    readonly text: Promise<string>;

    @decorators.referenceProperty<TestDocComment, 'parentDoc'>({
        node: () => TestDoc,
        isPublished: true,
        isVitalParent: true,
        isStored: true,
    })
    readonly parentDoc: Reference<TestDoc>;

    /** editable content  */
    @decorators.referenceProperty<TestDocComment, 'testContentAddressableDelegated'>({
        isPublished: true,
        isStored: true,
        isMutable: true,
        node: () => TestContentAddressable,
    })
    readonly testContentAddressableDelegated: Reference<TestContentAddressable | null>;

    @decorators.stringProperty<TestDocComment, 'name'>({
        isPublished: true,
        delegatesTo: { testContentAddressableDelegated: 'name' },
    })
    readonly name: Promise<string>;

    @decorators.stringProperty<TestDocComment, 'description'>({
        isPublished: true,
        delegatesTo: { testContentAddressableDelegated: 'description' },
    })
    readonly description: Promise<string>;
}
