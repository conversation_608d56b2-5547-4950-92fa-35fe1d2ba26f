import { decorators, Node, StringDataType } from '@sage/xtrem-core';
import { nameDataType } from '../../../../lib/data-types/_index';
import { TestActivityRef } from './test-activity-ref';

@decorators.node<TestActivity>({
    isPublished: true,
    storage: 'sql',
    canRead: true,
    canCreate: true,
    canDelete: true,
    canUpdate: true,
    indexes: [{ orderBy: { name: +1 }, isUnique: true, isNaturalKey: true }],
})
export class TestActivity extends Node {
    @decorators.stringProperty<TestActivity, 'name'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringDataType({ maxLength: 80 }),
        isRequired: true,
    })
    readonly name: Promise<string>;

    @decorators.stringProperty<TestActivity, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => nameDataType,
    })
    readonly code: Promise<string>;

    @decorators.referenceProperty<TestActivity, 'ref'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        node: () => TestActivityRef,
    })
    readonly ref: Promise<TestActivityRef | null>;
}
