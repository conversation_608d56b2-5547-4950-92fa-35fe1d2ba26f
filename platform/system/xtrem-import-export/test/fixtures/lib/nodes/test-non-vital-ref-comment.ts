import { decorators, Node, Reference, StringDataType } from '@sage/xtrem-core';
import { TestContentAddressable } from './test-content-addressable';
import { TestNonVitalRef } from './test-non-vital-ref';

@decorators.node<TestNonVitalRefComment>({
    isPublished: true,
    storage: 'sql',
    canRead: true,
    canCreate: true,
    canDelete: true,
    canUpdate: true,
    isVitalCollectionChild: true,
})
export class TestNonVitalRefComment extends Node {
    @decorators.stringProperty<TestNonVitalRefComment, 'text'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringDataType({ maxLength: 80 }),
    })
    readonly text: Promise<string>;

    @decorators.referenceProperty<TestNonVitalRefComment, 'parentNonVitalRef'>({
        node: () => TestNonVitalRef,
        isPublished: true,
        isVitalParent: true,
        isStored: true,
    })
    readonly parentNonVitalRef: Reference<TestNonVitalRef>;

    /** editable content  */
    @decorators.referenceProperty<TestNonVitalRefComment, 'testContentAddressableDelegated'>({
        isPublished: true,
        isStored: true,
        isMutable: true,
        node: () => TestContentAddressable,
    })
    readonly testContentAddressableDelegated: Reference<TestContentAddressable | null>;

    @decorators.stringProperty<TestNonVitalRefComment, 'name'>({
        isPublished: true,
        delegatesTo: { testContentAddressableDelegated: 'name' },
    })
    readonly name: Promise<string>;

    @decorators.stringProperty<TestNonVitalRefComment, 'description'>({
        isPublished: true,
        delegatesTo: { testContentAddressableDelegated: 'description' },
    })
    readonly description: Promise<string>;
}
