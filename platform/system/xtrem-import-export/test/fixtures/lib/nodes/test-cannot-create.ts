import { decorators, Node, StringDataType } from '@sage/xtrem-core';

@decorators.node<TestCannotCreate>({
    isPublished: true,
    storage: 'sql',
    canRead: true,
    // if canCreate is false the node cannot be imported
    canCreate: false,
    canDelete: true,
    canUpdate: true,
    indexes: [{ orderBy: { name: +1 }, isUnique: true }],
})
export class TestCannotCreate extends Node {
    @decorators.stringProperty<TestCannotCreate, 'name'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringDataType({ maxLength: 80 }),
    })
    readonly name: Promise<string>;

    @decorators.stringProperty<TestCannotCreate, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringDataType({ maxLength: 80 }),
        isRequired: false,
    })
    readonly code: Promise<string>;
}
