import {
    dateRange,
    datetime,
    datetimeRange,
    decimal,
    DecimalDataType,
    decorators,
    integer,
    Node,
    short,
    StringArrayDataType,
    StringDataType,
} from '@sage/xtrem-core';
import { TestEnum, testEnumDataType } from '../enums';

const decimalDataType = new DecimalDataType({ precision: 9, scale: 3 });
const nameDataType = new StringDataType({ maxLength: 80 });

@decorators.node<TestNonVitalSubRefNoNaturalKey>({
    isPublished: true,
    storage: 'sql',
    canRead: true,
    canCreate: true,
    canDelete: true,
    canUpdate: true,
    isCustomizable: true,
    indexes: [
        { orderBy: { subRefName: +1 }, isUnique: true },
        { orderBy: { subRefCode: +1 }, isUnique: true },
    ],
})
export class TestNonVitalSubRefNoNaturalKey extends Node {
    @decorators.stringProperty<TestNonVitalSubRefNoNaturalKey, 'subRefName'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringDataType({ maxLength: 80 }),
    })
    readonly subRefName: Promise<string>;

    @decorators.stringProperty<TestNonVitalSubRefNoNaturalKey, 'subRefCode'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringDataType({ maxLength: 80 }),
        isRequired: false,
    })
    readonly subRefCode: Promise<string>;

    @decorators.stringProperty<TestNonVitalSubRefNoNaturalKey, 'description'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringDataType({ maxLength: 100, isLocalized: true }),
        async control(cx, val): Promise<void> {
            await cx.error.if(val).is.equal.to('description');
        },
        defaultValue() {
            return 'default';
        },
    })
    readonly description: Promise<string>;

    @decorators.stringArrayProperty<TestNonVitalSubRefNoNaturalKey, 'stringArrayVal'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringArrayDataType({ maxLength: 50 }),
    })
    readonly stringArrayVal: Promise<string[]>;

    @decorators.shortProperty<TestNonVitalSubRefNoNaturalKey, 'shortVal'>({
        isStored: true,
        isPublished: true,
    })
    readonly shortVal: Promise<short>;

    @decorators.integerProperty<TestNonVitalSubRefNoNaturalKey, 'integerVal'>({
        isStored: true,
        isPublished: true,
    })
    readonly integerVal: Promise<integer>;

    @decorators.integerArrayProperty<TestNonVitalSubRefNoNaturalKey, 'integerArrayVal'>({
        isStored: true,
        isPublished: true,
    })
    readonly integerArrayVal: Promise<integer[]>;

    @decorators.decimalProperty<TestNonVitalSubRefNoNaturalKey, 'decimalVal'>({
        isStored: true,
        isPublished: true,
        dataType: () => decimalDataType,
    })
    readonly decimalVal: Promise<decimal>;

    @decorators.booleanProperty<TestNonVitalSubRefNoNaturalKey, 'booleanVal'>({
        isStored: true,
        isPublished: true,
    })
    readonly booleanVal: Promise<boolean>;

    @decorators.jsonProperty<TestNonVitalSubRefNoNaturalKey, 'jsonVal'>({
        isStored: true,
        isPublished: true,
    })
    readonly jsonVal: Promise<boolean>;

    @decorators.datetimeProperty<TestNonVitalSubRefNoNaturalKey, 'datetimeVal'>({
        isStored: true,
        isPublished: true,
    })
    readonly datetimeVal: Promise<datetime>;

    @decorators.dateRangeProperty<TestNonVitalSubRefNoNaturalKey, 'dateRangeVal'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly dateRangeVal: Promise<dateRange>;

    @decorators.datetimeRangeProperty<TestNonVitalSubRefNoNaturalKey, 'datetimeRangeVal'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly datetimeRangeVal: Promise<datetimeRange>;

    @decorators.enumProperty<TestNonVitalSubRefNoNaturalKey, 'enumVal'>({
        dataType: () => testEnumDataType,
        isStored: true,
        isPublished: true,
    })
    readonly enumVal: Promise<TestEnum>;

    @decorators.enumArrayProperty<TestNonVitalSubRefNoNaturalKey, 'enumArrayVal'>({
        dataType: () => testEnumDataType,
        isStored: true,
        isPublished: true,
        isNullable: true,
    })
    readonly enumArrayVal: Promise<TestEnum[]>;

    @decorators.stringProperty<TestNonVitalSubRefNoNaturalKey, 'transientInput'>({
        isPublished: true,
        isTransientInput: true,
        dataType: () => nameDataType,
    })
    readonly transientInput: Promise<string>;
}
