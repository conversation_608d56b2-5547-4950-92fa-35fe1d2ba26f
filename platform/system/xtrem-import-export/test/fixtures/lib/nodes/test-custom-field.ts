import { decorators, Node, StringDataType } from '@sage/xtrem-core';

@decorators.node<TestCustomField>({
    isPublished: true,
    storage: 'sql',
    canRead: true,
    canCreate: true,
    canDelete: true,
    canDeleteMany: true,
    canUpdate: true,
    isCustomizable: true,
    indexes: [{ orderBy: { name: +1 }, isUnique: true, isNaturalKey: true }],
})
export class TestCustomField extends Node {
    @decorators.stringProperty<TestCustomField, 'name'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringDataType({ maxLength: 80 }),
        isRequired: true,
    })
    readonly name: Promise<string>;
}
