import { Collection, decorators, Node, Reference, StringDataType } from '@sage/xtrem-core';
import { TestContentAddressable } from './test-content-addressable';
import { TestDocSubLineWithNonVitalRef } from './test-doc-sub-line-with-non-vital-ref';
import { TestDocWithNonVitalRef } from './test-doc-with-non-vital-ref';
import { TestNonVitalRef } from './test-non-vital-ref';

@decorators.node<TestDocLineWithNonVitalRef>({
    isPublished: true,
    storage: 'sql',
    canRead: true,
    canCreate: true,
    canDelete: true,
    canUpdate: true,
    isVitalCollectionChild: true,
    indexes: [{ orderBy: { label: +1 }, isUnique: true }],
})
export class TestDocLineWithNonVitalRef extends Node {
    @decorators.stringProperty<TestDocLineWithNonVitalRef, 'label'>({
        isPublished: true,
        isStored: true,
        isRequired: true,
        dataType: () => new StringDataType({ maxLength: 30 }),
    })
    readonly label: Promise<string>;

    @decorators.collectionProperty<TestDocLineWithNonVitalRef, 'subLines'>({
        node: () => TestDocSubLineWithNonVitalRef,
        isPublished: true,
        isVital: true,
        reverseReference: 'parentDocLine',
    })
    readonly subLines: Collection<TestDocSubLineWithNonVitalRef>;

    @decorators.referenceProperty<TestDocLineWithNonVitalRef, 'parentDoc'>({
        node: () => TestDocWithNonVitalRef,
        isVitalParent: true,
        isStored: true,
        isPublished: true,
    })
    readonly parentDoc: Reference<TestDocWithNonVitalRef>;

    @decorators.referenceProperty<TestDocLineWithNonVitalRef, 'testDocLineWithNonVitalRef'>({
        node: () => TestDocLineWithNonVitalRef,
        isStored: true,
        isPublished: true,
        isNullable: true,
    })
    readonly testDocLineWithNonVitalRef: Reference<TestDocLineWithNonVitalRef | null>;

    /** editable content  */
    @decorators.referenceProperty<TestDocLineWithNonVitalRef, 'testContentAddressable'>({
        isPublished: true,
        isStored: true,
        isMutable: true,
        isNullable: true,
        node: () => TestContentAddressable,
    })
    readonly testContentAddressable: Reference<TestContentAddressable | null>;

    @decorators.referenceProperty<TestDocLineWithNonVitalRef, 'nonVitalRef'>({
        isPublished: true,
        node: () => TestNonVitalRef,
        isStored: true,
        isNullable: true,
        allowedInUniqueIndex: true,
    })
    readonly nonVitalRef: Reference<TestNonVitalRef | null>;
}
