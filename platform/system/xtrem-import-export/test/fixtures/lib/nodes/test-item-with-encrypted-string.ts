import { decorators, Node, StringDataType } from '@sage/xtrem-core';
import { dataTypes } from '@sage/xtrem-system';

@decorators.node<TestItemWithEncryptedString>({
    isPublished: true,
    storage: 'sql',
    canRead: true,
    canCreate: true,
    canDelete: true,
    canUpdate: true,
    indexes: [{ orderBy: { name: +1 }, isUnique: true }],
})
export class TestItemWithEncryptedString extends Node {
    @decorators.stringProperty<TestItemWithEncryptedString, 'name'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringDataType({ maxLength: 80 }),
    })
    readonly name: Promise<string>;

    @decorators.stringProperty<TestItemWithEncryptedString, 'code'>({
        isPublished: true,
        isStored: true,
        isStoredEncrypted: true,
        dataType: () => dataTypes.password,
        isRequired: false,
    })
    readonly code: Promise<string>;

    @decorators.stringProperty<TestItemWithEncryptedString, 'formula'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringDataType({ maxLength: 180 }),
    })
    readonly formula: Promise<string>;
}
