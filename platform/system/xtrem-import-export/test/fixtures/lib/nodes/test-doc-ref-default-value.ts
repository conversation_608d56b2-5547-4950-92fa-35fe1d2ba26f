import {
    Collection,
    DecimalDataType,
    Node,
    Reference,
    StringArrayDataType,
    StringDataType,
    dateRange,
    datetime,
    datetimeRange,
    decimal,
    decorators,
    integer,
    short,
} from '@sage/xtrem-core';
import { TestEnum, testEnumDataType } from '../enums';
import { TestDocLineRefDefaultValue } from './test-doc-line-ref-default-value';
import { TestItem } from './test-item';

const decimalDataType = new DecimalDataType({ precision: 9, scale: 3 });
const nameDataType = new StringDataType({ maxLength: 80 });

@decorators.node<TestDocRefDefaultValue>({
    isPublished: true,
    storage: 'sql',
    canRead: true,
    canCreate: true,
    canDelete: true,
    canUpdate: true,
    indexes: [
        { orderBy: { name: +1 }, isUnique: true, isNaturalKey: true },
        { orderBy: { code: +1 }, isUnique: true },
    ],
})
export class TestDocRefDefaultValue extends Node {
    @decorators.stringProperty<TestDocRefDefaultValue, 'name'>({
        isPublished: true,
        isStored: true,
        dataType: () => nameDataType,
        isRequired: true,
        async control(cx, val): Promise<void> {
            await cx.error.if(val).is.equal.to('bad');
        },
    })
    readonly name: Promise<string>;

    @decorators.stringProperty<TestDocRefDefaultValue, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => nameDataType,
        isRequired: true,
    })
    readonly code: Promise<string>;

    @decorators.stringProperty<TestDocRefDefaultValue, 'description'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringDataType({ maxLength: 100, isLocalized: true }),
        async control(cx, val): Promise<void> {
            await cx.error.if(val).is.equal.to('description');
        },
        defaultValue() {
            return 'default';
        },
    })
    readonly description: Promise<string>;

    @decorators.stringArrayProperty<TestDocRefDefaultValue, 'stringArrayVal'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringArrayDataType({ maxLength: 10 }),
    })
    readonly stringArrayVal: Promise<string[]>;

    @decorators.shortProperty<TestDocRefDefaultValue, 'shortVal'>({
        isStored: true,
        isPublished: true,
    })
    readonly shortVal: Promise<short>;

    @decorators.integerProperty<TestDocRefDefaultValue, 'integerVal'>({
        isStored: true,
        isPublished: true,
    })
    readonly integerVal: Promise<integer>;

    @decorators.integerArrayProperty<TestDocRefDefaultValue, 'integerArrayVal'>({
        isStored: true,
        isPublished: true,
    })
    readonly integerArrayVal: Promise<integer[]>;

    @decorators.decimalProperty<TestDocRefDefaultValue, 'decimalVal'>({
        isStored: true,
        isPublished: true,
        dataType: () => decimalDataType,
    })
    readonly decimalVal: Promise<decimal>;

    @decorators.booleanProperty<TestDocRefDefaultValue, 'booleanVal'>({
        isStored: true,
        isPublished: true,
    })
    readonly booleanVal: Promise<boolean>;

    @decorators.jsonProperty<TestDocRefDefaultValue, 'jsonVal'>({
        isStored: true,
        isPublished: true,
    })
    readonly jsonVal: Promise<boolean>;

    @decorators.datetimeProperty<TestDocRefDefaultValue, 'datetimeVal'>({
        isStored: true,
        isPublished: true,
    })
    readonly datetimeVal: Promise<datetime>;

    @decorators.datetimeRangeProperty<TestDocRefDefaultValue, 'datetimeRangeVal'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly datetimeRangeVal: Promise<datetimeRange>;

    @decorators.dateRangeProperty<TestDocRefDefaultValue, 'dateRangeVal'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly dateRangeVal: Promise<dateRange>;

    @decorators.enumProperty<TestDocRefDefaultValue, 'enumVal'>({
        dataType: () => testEnumDataType,
        isStored: true,
        isPublished: true,
    })
    readonly enumVal: Promise<TestEnum>;

    @decorators.enumArrayProperty<TestDocRefDefaultValue, 'enumArrayVal'>({
        dataType: () => testEnumDataType,
        isStored: true,
        isPublished: true,
        isNullable: true,
    })
    readonly enumArrayVal: Promise<TestEnum[]>;

    @decorators.collectionProperty<TestDocRefDefaultValue, 'lines'>({
        node: () => TestDocLineRefDefaultValue,
        isPublished: true,

        isVital: true,
        reverseReference: 'parentDoc',
    })
    readonly lines: Collection<TestDocLineRefDefaultValue>;

    @decorators.stringProperty<TestDocRefDefaultValue, 'transientInput'>({
        isPublished: true,
        isTransientInput: true,
        dataType: () => nameDataType,
    })
    readonly transientInput: Promise<string>;

    @decorators.referenceProperty<TestDocRefDefaultValue, 'itemWithDefaultValueDocLevel'>({
        isStored: true,
        isPublished: true,
        node: () => TestItem,
        isNullable: true,
        defaultValue() {
            return this.$.context.read(TestItem, { name: 'NAM1' });
        },
    })
    readonly itemWithDefaultValueDocLevel: Reference<TestItem | null>;
}
