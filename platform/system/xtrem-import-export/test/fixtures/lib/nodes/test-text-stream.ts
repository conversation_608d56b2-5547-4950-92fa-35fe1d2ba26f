import { decorators, Node, TextStream } from '@sage/xtrem-core';
import { htmlDataType, nameDataType } from '../data-types/_index';

@decorators.node<TestTextStream>({
    isPublished: true,
    storage: 'sql',
    canRead: true,
    canCreate: true,
    canDelete: true,
    canUpdate: true,
    indexes: [{ orderBy: { name: +1 }, isUnique: true, isNaturalKey: true }],
})
export class TestTextStream extends Node {
    @decorators.stringProperty<TestTextStream, 'name'>({
        isPublished: true,
        isStored: true,
        dataType: () => nameDataType,
    })
    readonly name: Promise<string>;

    @decorators.textStreamProperty<TestTextStream, 'html'>({
        isPublished: true,
        isStored: true,
        dataType: () => htmlDataType,
    })
    readonly html: Promise<TextStream>;
}
