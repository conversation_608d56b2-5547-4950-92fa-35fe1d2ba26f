{"subRefName": "non vital sub-ref 1", "subRefCode": "nonVitalSubRef1", "description": "description non vital sub-ref 1", "stringArrayVal": ["non vital sub-ref 1 string 1", "non vital sub-ref 1 string 2", "non vital sub-ref 1 string 3"], "shortVal": 1, "integerVal": 123, "integerArrayVal": [1, 2, 3, 4], "decimalVal": "1.234", "booleanVal": true, "jsonVal": {"key1": "nonVitalSubRef1Value1", "key2": "nonVitalSubRef1Value2"}, "dateRangeVal": "[2022-01-01,2022-12-31]", "datetimeRangeVal": "[2022-01-01T00:00:00.000Z,2022-12-31T23:59:59.000Z]", "enumVal": "value3", "enumArrayVal": ["value2", "value3"]}