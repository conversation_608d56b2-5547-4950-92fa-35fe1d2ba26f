{"name": "non vital ref 1", "code": "nonVitalRef1", "description": "description non vital ref 1", "stringArrayVal": ["non vital ref string 1", "non vital ref string 2", "non vital ref string 3"], "shortVal": 1, "integerVal": 123, "integerArrayVal": [1, 2, 3, 4], "decimalVal": "1.234", "booleanVal": true, "jsonVal": {"key1": "nonVitalRef1Value1", "key2": "nonVitalRef1Value2"}, "dateRangeVal": "[2022-01-01,2022-12-31]", "datetimeRangeVal": "[2022-01-01T00:00:00.000Z,2022-12-31T23:59:59.000Z]", "enumVal": "value2", "enumArrayVal": ["value1", "value2"], "comments": [{"_sortValue": 10, "text": "hello", "parentNonVitalRef": {}, "description": "description 1", "name": "name 1", "testContentAddressableDelegated": {"description": "description 1", "name": "name 1"}}, {"_sortValue": 20, "text": "world", "parentNonVitalRef": {}, "description": "description 2", "name": "name 2", "testContentAddressableDelegated": {"description": "description 2", "name": "name 2"}}], "subRef": "non vital sub-ref 1", "subRefNoNaturalKey": null}