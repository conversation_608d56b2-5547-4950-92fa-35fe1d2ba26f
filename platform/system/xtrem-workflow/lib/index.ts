import '@sage/xtrem-auditing';
import '@sage/xtrem-communication';
import { Application, CoreHooks, InstanceDataValidationResultLine } from '@sage/xtrem-core';
import * as activities from './activities/_index';
import { startService } from './engine';
import { logger } from './engine/logger';
import { WorkflowManager } from './engine/workflow-manager';
import * as enums from './enums/index';
import * as menuItems from './menu-items/index';
import * as nodes from './nodes/index';
import * as serviceOptions from './service-options/index';
import * as workflowSteps from './workflow-steps/index';

export { WorkflowConfigPageData } from './client-functions';
export {
    FlowNode,
    WorkflowAction,
    WorkflowEngine,
    WorkflowEvent,
    WorkflowProcessState,
    WorkflowProcessWrapper,
    WorkflowStepFactory,
} from './engine';
export { logger } from './engine/logger';
export {
    WorkflowActionConfig,
    WorkflowCondition,
    WorkflowEventConfig,
    WorkflowGraphqlActionConfig,
    WorkflowParameter,
} from './shared-functions';
export { GraphqlAction, NotificationEvent } from './workflow-steps';
export type { Request } from './workflow-steps/helpers/graphql-helper';
export { activities, enums, menuItems, nodes, serviceOptions, startService, workflowSteps };
CoreHooks.createWorkflowManager = (application: Application) => new WorkflowManager(application);

// Note: we need a dedicated validator for WorkflowDefinition nodes to set the extraInfo to the stepId
CoreHooks.getDataValidationManager().registerValidator(
    nodes.WorkflowDefinition,
    async (context, workflowDefinition) => {
        const diagram = await workflowDefinition.diagram;
        const data = await diagram.data;
        const workflowErrors = await nodes.WorkflowDiagram.controlDiagram(context, data.nodes, data.edges);
        if (workflowErrors == null || workflowErrors.length === 0) return [];

        const warnings: InstanceDataValidationResultLine[] = [];
        if (await workflowDefinition.isActive) {
            // An error was found for this workflow definition, we need to disable it
            // Note: we can't update the instance and save it because the save will fail as the workflow is not valid
            // so we need to disable it manually
            await context.executeSql(
                `UPDATE ${context.schemaName}.workflow_definition SET is_active = false WHERE _id = $1 AND _tenant_id = $2`,
                [workflowDefinition._id, context.tenantId],
            );
            const message = context.localize(
                '@sage/xtrem-workflow/invalid-workflow-is-disabled',
                'The workflow {{id}} has an error and cannot run. You need to fix the error before it can be activated again.',
                { id: await workflowDefinition.id },
            );
            logger.warn(message);
            warnings.push({
                message,
                severity: 'warning',
                path: 'diagram',
            });
        }

        return [
            ...warnings,
            ...workflowErrors.map(
                error =>
                    ({
                        message: error.message,
                        extraInfo: { stepId: error.stepId },
                        severity: 'error',
                        path: 'diagram',
                    }) as InstanceDataValidationResultLine,
            ),
        ];
    },
);
