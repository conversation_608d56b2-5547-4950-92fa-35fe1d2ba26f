import { Graph<PERSON>pi, MetaNodeFactory } from '@sage/xtrem-metadata-api';
import { LocalizedText, getTextForLocale, mergeLocalizedText, variableFilters } from '@sage/xtrem-shared';
import * as ui from '@sage/xtrem-ui';
import { WorkflowDefinition as WorkflowDefinitionApi } from '@sage/xtrem-workflow-api';
import { omit } from 'lodash';
import { WorkflowConfigPageData, getInputData, restoreNodeFieldFromString } from '../client-functions';

@ui.decorators.page<WorkflowActionDeleteEntity, WorkflowDefinitionApi>({
    title: 'Action configuration',
    subtitle: 'Delete an entity',
    mode: 'tabs',
    businessActions() {
        if (this.$.queryParameters.isReadOnly) {
            return [this.$standardCancelAction];
        }
        return [this.$standardDialogConfirmationAction];
    },
    async onLoad() {
        await this.onLoad();
    },
    isDisabled() {
        // Only return true if isReadOnly exists and is true, otherwise return false
        return !!this.$.queryParameters.isReadOnly;
    },
})
export class WorkflowActionDeleteEntity extends ui.Page<GraphApi> {
    @ui.decorators.section<WorkflowActionDeleteEntity>({
        isTitleHidden: true,
        title: 'Basic Details',
        isDisabled() {
            return !!this.$.queryParameters.isReadOnly;
        },
    })
    mainSection: ui.containers.Section;

    @ui.decorators.block<WorkflowActionDeleteEntity>({
        parent() {
            return this.mainSection;
        },
    })
    mainBlock: ui.containers.Block;

    /** Only show in read only mode */
    @ui.decorators.messageField<WorkflowActionDeleteEntity>({
        parent() {
            return this.mainBlock;
        },
        variant: 'error',
        isTransient: true,
        isHidden() {
            if (this.$.queryParameters.isReadOnly && this.$.queryParameters.errorMessage) {
                return false;
            }
            return true;
        },
    })
    readOnlyErrorMessage: ui.fields.Message;

    @ui.decorators.textField<WorkflowActionDeleteEntity>({
        title: 'Action title',
        helperText: 'Title displayed in the workflow diagram.',
        isFullWidth: true,
        isTransient: true,
        parent() {
            return this.mainBlock;
        },
    })
    title: ui.fields.Text;

    // Note: localizedTitle is a JSON object that contains the translations (something like { 'en-US': 'Hello', 'fr-FR': 'Bonjour' })
    // title is only editing the translation for the current locale
    @ui.decorators.technicalJsonField<WorkflowActionDeleteEntity>({})
    localizedTitle: ui.fields.TechnicalJson<LocalizedText>;

    @ui.decorators.textField<WorkflowActionDeleteEntity>({})
    subtitle: ui.fields.Text;

    @ui.decorators.textField<WorkflowActionDeleteEntity>({})
    node: ui.fields.Text;

    @ui.decorators.referenceField<WorkflowActionDeleteEntity, MetaNodeFactory>({
        parent() {
            return this.mainBlock;
        },
        orderBy: {
            name: 1,
        },
        columns: [
            ui.nestedFields.text({ bind: 'name', title: 'Record type', isHidden: true }),
            ui.nestedFields.text({ bind: 'title', title: 'Record type' }),
            ui.nestedFields.text({
                bind: { package: { name: true } },
                title: 'Package',
            }),
        ],
        isMandatory: true,
        node: '@sage/xtrem-metadata/MetaNodeFactory',
        title: 'Record type',
        valueField: 'title',
        helperTextField: { package: { name: true } },
        minLookupCharacters: 0,
        isFullWidth: true,
        onChange() {
            if (this.dataEntity.value) {
                const entityName = `${this.dataEntity.value.package?.name}/${this.dataEntity.value.name}`;
                this.subtitle.value = entityName;
                this.node.value = entityName;
            } else {
                this.subtitle.value = '';
                this.node.value = '';
            }
        },
    })
    dataEntity: ui.fields.Reference<MetaNodeFactory>;

    @ui.decorators.selectField<WorkflowActionDeleteEntity>({
        parent() {
            return this.mainBlock;
        },
        isFullWidth: true,
        isMandatory: true,
        title: 'Record ID variable',
        options() {
            return getInputData(this)
                .inputVariables.filter(variableFilters.isReference(this.dataEntity.value?.name ?? ''))
                .map(r => r.path);
        },
        map(v) {
            return getInputData(this).inputVariables.find(r => r.path === v)?.title ?? '';
        },
    })
    recordIdToDelete: ui.fields.Select;

    async onLoad(): Promise<void> {
        await restoreNodeFieldFromString(this, this.dataEntity, this.node.value);
        const locale = this.$.locale;
        this.title.value = getTextForLocale(this.localizedTitle.value ?? {}, locale);
        if (this.$.queryParameters.isReadOnly && this.$.queryParameters.errorMessage) {
            this.readOnlyErrorMessage.value = this.$.queryParameters.errorMessage as string;
        }
    }

    getSerializedValues(): WorkflowConfigPageData<any> {
        const locale = this.$.locale;
        const localizedTitle = mergeLocalizedText(this.localizedTitle.value ?? {}, this.title.value ?? '', locale);

        return {
            ...omit(this.$.values, ['title']),
            localizedTitle,
        };
    }
}
