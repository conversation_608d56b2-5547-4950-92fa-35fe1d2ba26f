import { Graph<PERSON>pi } from '@sage/xtrem-metadata-api';
import {
    LocalizedText,
    WorkflowVariable,
    getTextForLocale,
    mergeLocalizedText,
    titleCase,
    variableFilters,
} from '@sage/xtrem-shared';
import * as ui from '@sage/xtrem-ui';
import { WorkflowDefinition as WorkflowDefinitionApi } from '@sage/xtrem-workflow-api';
import { omit, pick } from 'lodash';
import { WorkflowConfigPageData, getInputData } from '../client-functions';
import { getSelectVariablesButtonDecorator } from '../client-functions/select-variables-button';
import {
    formatSelectedVariables,
    getStepVariablesForSerialization,
    getVariablesSummaryDecorator,
    tidyVariables,
    transformVariablesFromSerialization,
} from '../client-functions/variable-utils';
import { CalculateActionConfig, WorkflowCalculation } from '../shared-functions/calculate-action-config';

@ui.decorators.page<WorkflowActionCalculate, WorkflowDefinitionApi>({
    title: 'Action configuration',
    subtitle: 'Calculate',
    mode: 'tabs',
    onLoad() {
        this._onLoad();
    },
    businessActions() {
        if (this.$.queryParameters.isReadOnly) {
            return [this.$standardCancelAction];
        }
        return [this.$standardDialogConfirmationAction];
    },
})
export class WorkflowActionCalculate extends ui.Page<GraphApi> {
    @ui.decorators.section<WorkflowActionCalculate>({
        isTitleHidden: true,
        title: 'Basic Details',
        isDisabled() {
            return !!this.$.queryParameters.isReadOnly;
        },
    })
    mainSection: ui.containers.Section;

    @ui.decorators.block<WorkflowActionCalculate>({
        parent() {
            return this.mainSection;
        },
    })
    mainBlock: ui.containers.Block;

    /** Only show in read only mode */
    @ui.decorators.messageField<WorkflowActionCalculate>({
        parent() {
            return this.mainBlock;
        },
        variant: 'error',
        isTransient: true,
        isHidden() {
            if (this.$.queryParameters.isReadOnly && this.$.queryParameters.errorMessage) {
                return false;
            }
            return true;
        },
    })
    readOnlyErrorMessage: ui.fields.Message;

    @ui.decorators.textField<WorkflowActionCalculate>({
        title: 'Action title',
        helperText: 'Title displayed in the workflow diagram.',
        isFullWidth: true,
        isTransient: true,
        parent() {
            return this.mainBlock;
        },
    })
    title: ui.fields.Text;

    // Note: localizedTitle is a JSON object that contains the translations (something like { 'en-US': 'Hello', 'fr-FR': 'Bonjour' })
    // title is only editing the translation for the current locale
    @ui.decorators.technicalJsonField<WorkflowActionCalculate>({})
    localizedTitle: ui.fields.TechnicalJson<LocalizedText>;

    @ui.decorators.textField<WorkflowActionCalculate>({})
    subtitle: ui.fields.Text;

    @ui.decorators.technicalJsonField<WorkflowActionCalculate>({})
    stepVariables: ui.fields.TechnicalJson<WorkflowVariable[]>;

    @ui.decorators.staticContentField<WorkflowActionCalculate>({
        ...getVariablesSummaryDecorator(),
        parent() {
            return this.mainBlock;
        },
    })
    variablesSummary: ui.fields.StaticContent;

    @ui.decorators.buttonField<WorkflowActionCalculate>({
        ...getSelectVariablesButtonDecorator<WorkflowActionCalculate>({
            getSelectedVariables: page => page.currentVariables,
            getOldRootPaths: page => page.oldRootPaths,
            setSelectedVariables: (page, variables) => page._updateCurrentVariables(variables),
        }),
        parent() {
            return this.mainBlock;
        },
    })
    selectVariablesButton: ui.fields.Button;

    @ui.decorators.podCollectionField<WorkflowActionCalculate>({
        parent() {
            return this.mainBlock;
        },
        title: 'Calculation',
        isTitleHidden: true,
        isFullWidth: true,
        recordWidth: 'large',
        recordTitle(_, recordValue) {
            const stepIndex = this.calculationSteps.value?.findIndex(s => s._id === recordValue?._id);
            if (stepIndex === -1) {
                return 'Calculation step';
            }
            return `Calculation step ${stepIndex + 1}`;
        },
        columns: [
            ui.nestedFields.select({
                bind: 'operation',
                title: 'Operation',
                options: ['add', 'subtract', 'divide', 'multiply'],
                width: 'small',
                isDisabled(_, recordValue) {
                    return this.calculationSteps.value?.[0]._id === recordValue?._id;
                },
            }),
            ui.nestedFields.switch({
                bind: 'isVariable',
                title: 'Is variable?',
                width: 'small',
            }),
            ui.nestedFields.select({
                title: 'Component variable',
                bind: 'variable',
                isHelperTextHidden: true,
                width: 'medium',
                options(this: WorkflowActionCalculate) {
                    return this.currentVariables.filter(variableFilters.isNumeric).map(r => r.path);
                },
                map(this: WorkflowActionCalculate, v: any) {
                    return this.currentVariables.find(r => r.path === v)?.title ?? '';
                },
                isHidden(_id, rowValue) {
                    return !rowValue?.isVariable;
                },
            }),
            ui.nestedFields.numeric({
                title: 'Component value',
                bind: 'value',
                width: 'medium',
                scale: 4,
                isHelperTextHidden: true,
                isHidden(_id, rowValue) {
                    return !!rowValue?.isVariable;
                },
            }),
        ],
        canAddRecord: true,
        canRemoveRecord: true,
        addButtonText: 'Add a calculation step',
    })
    calculationSteps: ui.fields.PodCollection;

    @ui.decorators.textField<WorkflowActionCalculate>({
        parent() {
            return this.mainBlock;
        },
        isFullWidth: true,
        isMandatory: true,
        title: 'Output variable name',
    })
    outputVariableName: ui.fields.Text;

    // Private variables

    currentVariables: WorkflowVariable[] = [];

    oldRootPaths: string[];

    // Methods

    private _updateCurrentVariables(variables: WorkflowVariable[]): void {
        const { inputVariables } = getInputData(this);
        this.currentVariables = tidyVariables([...inputVariables, ...this.stepVariables.value, ...variables]);
        this.variablesSummary.value = formatSelectedVariables(this.currentVariables);
        // force refill of variable select fields options in calculationSteps pod collection
        this.calculationSteps.value = [...(this.calculationSteps.value ?? [])];
    }

    private _onLoad(): void {
        const { oldRootPaths } = getInputData(this);
        this.oldRootPaths = oldRootPaths;
        this.stepVariables.value = transformVariablesFromSerialization(this.stepVariables.value ?? []);
        this._updateCurrentVariables([]);
        const locale = this.$.locale;
        this.title.value = getTextForLocale(this.localizedTitle.value ?? {}, locale);
        if (this.$.queryParameters.isReadOnly && this.$.queryParameters.errorMessage) {
            this.readOnlyErrorMessage.value = this.$.queryParameters.errorMessage as string;
        }
    }

    private getUsedVariablePaths(): string[] {
        const usedPaths = [] as string[];
        this.calculationSteps.value?.forEach(step => {
            if (step.isVariable) {
                usedPaths.push(step.variable ?? '');
            }
        });
        return usedPaths;
    }

    getSerializedValues(): WorkflowConfigPageData<CalculateActionConfig> {
        const locale = this.$.locale;
        const outputVariableName = `${this.outputVariableName.value}`;
        const calculationSteps = this.calculationSteps.value?.map(v => omit(v, '_id')) as WorkflowCalculation[];
        const usedPaths = this.getUsedVariablePaths();
        const stepVariables = [
            ...getStepVariablesForSerialization(this.currentVariables, usedPaths),
            {
                path: outputVariableName,
                title: titleCase(outputVariableName),
                type: 'Float',
            },
        ];
        const localizedTitle = mergeLocalizedText(this.localizedTitle.value ?? {}, this.title.value ?? '', locale);

        return {
            ...pick(this.$.values, 'subtitle', 'outputVariableName'),
            calculationSteps,
            stepVariables,
            localizedTitle,
        };
    }
}
