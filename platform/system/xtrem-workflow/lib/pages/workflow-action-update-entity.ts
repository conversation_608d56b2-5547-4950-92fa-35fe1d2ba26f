import { <PERSON>raph<PERSON><PERSON> } from '@sage/xtrem-metadata-api';
import {
    LocalizeLocale,
    LocalizedText,
    LogicError,
    WorkflowVariable,
    getTextForLocale,
    mergeLocalizedText,
} from '@sage/xtrem-shared';
import * as ui from '@sage/xtrem-ui';
import { WorkflowDefinition as WorkflowDefinitionApi } from '@sage/xtrem-workflow-api';
import { difference, pick } from 'lodash';
import {
    NodeDetailsFetcher,
    PropertyDetails,
    PropertyDetailsFetcher,
    WorkflowConfigPageData,
    getInputData,
} from '../client-functions';
import { getUsedVariablePathsFromDynamicPod } from '../client-functions/dynamic-pod-utils';
import { getSelectVariablesButtonDecorator } from '../client-functions/select-variables-button';
import {
    UpdatableVariable,
    convertUpdateRulesToPodValue,
    fillUpdateRulesPodFromVariables,
} from '../client-functions/update-rule-utils';
import {
    convertPathForSerialization,
    convertPathFromSerialization,
    getDynamicSelectProperties,
    getNestedDynamicSelectProperties,
    getStepVariablesForSerialization,
    hideStaticContentFields,
    idTitle,
    setOptionsForDynamicSelect,
    tidyVariables,
    transformVariablesFromSerialization,
    validateDynamicVariablesPickers,
} from '../client-functions/variable-utils';
import { UpdateRule } from '../shared-functions/update-entity-action-config';

@ui.decorators.page<WorkflowActionUpdateEntity, WorkflowDefinitionApi>({
    title: 'Action configuration',
    subtitle: 'Update an entity',
    mode: 'tabs',
    businessActions() {
        if (this.$.queryParameters.isReadOnly) {
            return [this.$standardCancelAction];
        }
        return [this.$standardDialogConfirmationAction];
    },
    async onLoad() {
        await this._onLoad();
    },
    isDisabled() {
        // Only return true if isReadOnly exists and is true, otherwise return false
        return !!this.$.queryParameters.isReadOnly;
    },
    validation() {
        if (this._variablesToUpdate.length === 0) {
            return ui.localize(
                '@sage/xtrem-workflow/workflow_error_no_property_to_update',
                'You need to add a property to update.',
            );
        }
        return undefined;
    },
})
export class WorkflowActionUpdateEntity extends ui.Page<GraphApi> {
    @ui.decorators.section<WorkflowActionUpdateEntity>({
        title: 'Data selection',
        isDisabled() {
            return !!this.$.queryParameters.isReadOnly;
        },
    })
    mainSection: ui.containers.Section;

    @ui.decorators.block<WorkflowActionUpdateEntity>({
        parent() {
            return this.mainSection;
        },
    })
    mainBlock: ui.containers.Block;

    /** Only show in read only mode */
    @ui.decorators.messageField<WorkflowActionUpdateEntity>({
        parent() {
            return this.mainBlock;
        },
        variant: 'error',
        isTransient: true,
        isHidden() {
            if (this.$.queryParameters.isReadOnly && this.$.queryParameters.errorMessage) {
                return false;
            }
            return true;
        },
    })
    readOnlyErrorMessage: ui.fields.Message;

    @ui.decorators.textField<WorkflowActionUpdateEntity>({
        title: 'Action title',
        helperText: 'Title displayed in the workflow diagram.',
        isFullWidth: true,
        isTransient: true,
        parent() {
            return this.mainBlock;
        },
    })
    title: ui.fields.Text;

    // Note: localizedTitle is a JSON object that contains the translations (something like { 'en-US': 'Hello', 'fr-FR': 'Bonjour' })
    // title is only editing the translation for the current locale
    @ui.decorators.technicalJsonField<WorkflowActionUpdateEntity>({})
    localizedTitle: ui.fields.TechnicalJson<LocalizedText>;

    @ui.decorators.textField<WorkflowActionUpdateEntity>({})
    subtitle: ui.fields.Text;

    @ui.decorators.dynamicSelectField<WorkflowActionUpdateEntity>({
        parent() {
            return this.mainBlock;
        },
        isFullWidth: true,
        isMandatory: true,
        title: 'Record to update',
        ...getDynamicSelectProperties<WorkflowActionUpdateEntity>({
            mode: 'select',
            getVariables: page => page.currentVariables,
            selectableVariableFilter: (_page, variable) =>
                WorkflowActionUpdateEntity._getVariablesFilterForPathToUpdate(variable),
            getOldRootPaths: page => page.oldRootPaths,
            updateCurrentVariables: (page, variables) => page._updateCurrentVariables(variables),
            options: {
                onlyShowIdsAndReferences: true,
            },
        }),
        async onChange() {
            await this._computeUpdatableVariables();
            this._variablesToUpdate = [];
            this.updateRulesPod.value = [];
            await this.fillUpdateRulesPodFromVariables();
        },
    })
    pathToUpdate: ui.fields.DynamicSelect;

    @ui.decorators.textField<WorkflowActionUpdateEntity>({ isHidden: true })
    entityName: ui.fields.Text;

    @ui.decorators.buttonField<WorkflowActionUpdateEntity>({
        ...getSelectVariablesButtonDecorator<WorkflowActionUpdateEntity>({
            getFromVariables: page => {
                const recToUpdateVar = page._getPathToUpdateAsVariable();
                if (recToUpdateVar == null) return [];
                return [recToUpdateVar];
            },
            getSelectedVariables: page => {
                return page._variablesToUpdate;
            },
            setSelectedVariables: async (page, variables, rootVariable) => {
                page.pathToUpdate.value = rootVariable.path;
                const packageName = await ui.fetchNodePackageName(rootVariable.node?.replace(/\._id$/, '') ?? '');
                page.entityName.value = `${packageName}/${rootVariable.node}`;
                const variablesToUpdate = tidyVariables(
                    variables.filter(variable => variable.path !== rootVariable.path),
                );
                page._variablesToUpdate = variablesToUpdate;
                await page._onVariablesToUpdateChanged();
            },
            getRestrictedVariables: page => page._updatableVariables,
            getOldRootPaths: () => [],
            selectionMode: 'propertiesToUpdate',
        }),
        map() {
            return ui.localize('@sage/xtrem-workflow/add-properties', 'Select properties to update');
        },
        isHidden() {
            return this.pathToUpdate.value == null;
        },
        isFullWidth: true,
        parent() {
            return this.mainBlock;
        },
    })
    selectVariablesToUpdateButton: ui.fields.Button;

    @ui.decorators.block<WorkflowActionUpdateEntity>({
        parent() {
            return this.mainSection;
        },
    })
    propertyValuesBlock: ui.containers.Block;

    @ui.decorators.technicalJsonField<WorkflowActionUpdateEntity>({})
    stepVariables: ui.fields.TechnicalJson<WorkflowVariable[]>;

    @ui.decorators.technicalJsonField<WorkflowActionUpdateEntity>({})
    updateRules: ui.fields.TechnicalJson<UpdateRule[]>;

    @ui.decorators.dynamicPodField<WorkflowActionUpdateEntity>({
        title: 'New values',
        isTransient: true,
        isFullWidth: true,
        columns: [],
        async onChange() {
            await this.fillUpdateRulesPodFromVariables();
        },
        parent() {
            return this.propertyValuesBlock;
        },
        isHidden() {
            return this._variablesToUpdate.length === 0;
        },
    })
    updateRulesPod: ui.fields.DynamicPod;

    // Private variables

    private currentVariables: WorkflowVariable[] = [];

    private oldRootPaths: string[];

    private _updatableVariables: UpdatableVariable[] = [];

    private _nodeDetailsFetcher = new NodeDetailsFetcher();

    private _propertyDetailsFetcher = new PropertyDetailsFetcher();

    private _variablesToUpdate: WorkflowVariable[] = [];

    /**
     * The property details for the node to update
     */
    private _propertyDetails: PropertyDetails[];

    // Fill methods

    private _getPathToUpdateAsVariable(): WorkflowVariable | null {
        if (this.pathToUpdate.value == null) return null;
        return this.currentVariables.find(v => v.path === this.pathToUpdate.value) ?? null;
    }

    private async _computeUpdatableVariables(): Promise<void> {
        const varToUpdate = this._getPathToUpdateAsVariable();
        if (varToUpdate == null || varToUpdate.node == null) {
            this._updatableVariables = [];
            return;
        }
        if (!varToUpdate.path.endsWith('._id')) {
            throw new Error(`Invalid path for path to update: ${varToUpdate.path}`);
        }

        const prefix = this.getVariablePrefix();
        this._propertyDetails = (await this._propertyDetailsFetcher.fetch(this, this.getNodeName() ?? '')).properties;

        const allProperties = await this._nodeDetailsFetcher.fetch(varToUpdate.node);
        this._updatableVariables = (
            Object.values(allProperties)
                .map(property => {
                    if (property.type === '_OutputTextStream') {
                        // Streams can't be updated
                        return null;
                    }

                    const path = property.name;
                    if (!property.isCustom) {
                        if (!property.isOnInputType) {
                            // Filter out non-writable properties
                            return null;
                        }
                        if (path.startsWith('_')) {
                            // Filter out system properties
                            return null;
                        }
                    }
                    const updatableVariable: UpdatableVariable = {
                        title: property.label,
                        path: `${prefix}${path}`,
                        type: property.type !== 'Enum' && property.node != null ? 'IntReference' : property.type,
                        node: property.node,
                        enumType: property.enumType ?? undefined,
                        enumValues: property.enumValues ?? undefined,
                        nodeFullName: property.targetNode,
                        isCustom: property.isCustom,
                    };
                    return updatableVariable;
                })
                .filter(property => property != null) as UpdatableVariable[]
        ).sort((a, b) => a.title.localeCompare(b.title));
    }

    // Returns the name (without package prefix) of the node to update
    private getNodeName(): string | null {
        const pathToUpdate = this.pathToUpdate.value;
        const variable = this.currentVariables.filter(v => v.path === pathToUpdate);
        return variable[0]?.node ?? null;
    }

    private getVariablePrefix(): string {
        return this.pathToUpdate.value?.replace(/\._id$/, '.') ?? '';
    }

    /**
     * Return the title prefix i.e. the title (without the 🆔) of the record to update. Something like 'Sales order / '
     * @returns
     */
    private _getTitlePrefix(): string {
        const v = this._getPathToUpdateAsVariable();
        if (v == null) return '';
        return v.title.replace(idTitle, '');
    }

    private async _updateCurrentVariables(variables: WorkflowVariable[]): Promise<void> {
        // Retrieve the output variables from the previous steps
        const { inputVariables } = getInputData(this);
        this.currentVariables = tidyVariables([...inputVariables, ...this.stepVariables.value, ...variables]);
        this._setOptionsForDynamicSelects();
    }

    /**
     * Compute the components to display in this.updateRulesPod according to the selected variables-to-update
     */
    private async fillUpdateRulesPodFromVariables(): Promise<void> {
        const variablesToUpdate = this._variablesToUpdate.map(variable => ({
            ...variable,
            // Only keep the last part of the path
            // If we are updating "SalesOrder / billToCustomer", then the path of all the variables from this._variablesToUpdate
            // will start with '"salesOrder.billToCustomer.'
            // Exception for custom fields where we also want to keep the '_customData.' part
            path: variable.path
                .split('.')
                .slice(variable.isCustom ? -2 : -1)
                .join('.'),
        }));
        const titlePrefix = this._getTitlePrefix();

        // If we remove a variable-to-update, data from this obsolete variable will remain in the pod.value
        // We have to clean it
        const allowedKeys = variablesToUpdate.reduce((total, variable) => {
            const key = variable.path.replace('_customData.', '_customData_');
            total.push(key);
            total.push(`${key}/isVariable`);
            return total;
        }, [] as string[]);
        const actualKeys = Object.keys(this.updateRulesPod.value);
        const keysToRemove = difference(actualKeys, allowedKeys);
        keysToRemove.forEach(key => {
            delete this.updateRulesPod.value[key];
        });

        const getVariableTitle = (variable: WorkflowVariable): string => {
            // Remove the title prefix from the variable title (except for the xxx._id variables)
            if (variable.path.endsWith('._id')) return variable.title;
            if (!variable.title.startsWith(titlePrefix)) return variable.title;
            return variable.title.substring(titlePrefix.length);
        };

        fillUpdateRulesPodFromVariables({
            dynamicPod: this.updateRulesPod,
            variablesToUpdate,
            locale: (this.$.locale || 'en-US') as LocalizeLocale,
            propertyDetails: this._propertyDetails,
            getPropertiesForDynamicSelects: (filter: (_page: ui.Page, variable: WorkflowVariable) => boolean) => {
                return {
                    ...getNestedDynamicSelectProperties<WorkflowActionUpdateEntity>({
                        mode: 'select',
                        getVariables: page => page.currentVariables.map(v => ({ ...v, title: getVariableTitle(v) })),
                        selectableVariableFilter: filter,
                        getOldRootPaths: page => page.oldRootPaths,
                        updateCurrentVariables: (page, variables) => page._updateCurrentVariables(variables),
                    }),
                };
            },
            getPropertiesForDynamicInputs: (filter: (_page: ui.Page, variable: WorkflowVariable) => boolean) => {
                return getNestedDynamicSelectProperties<WorkflowActionUpdateEntity>({
                    mode: 'input',
                    getVariables: page => page.currentVariables.map(v => ({ ...v, title: getVariableTitle(v) })),
                    selectableVariableFilter: filter,
                    getOldRootPaths: page => page.oldRootPaths,
                    updateCurrentVariables: (page, variables) => page._updateCurrentVariables(variables),
                });
            },
        });
    }

    private onEntityNameChanged(): void {
        if (this.pathToUpdate.value) {
            this.selectVariablesToUpdateButton.isHidden = false;
            this.propertyValuesBlock.isDisabled = !!this.currentVariables.length;
        } else {
            if (hideStaticContentFields) {
                this.selectVariablesToUpdateButton.isHidden = true;
            }
            this.propertyValuesBlock.isDisabled = true;
        }
    }

    /**
     * This function is called when the user changes the list of variables to update
     */
    private async _onVariablesToUpdateChanged(): Promise<void> {
        if (this._variablesToUpdate.length) {
            this.propertyValuesBlock.isDisabled = false;
        } else {
            this.propertyValuesBlock.isDisabled = true;
        }
        await this.fillUpdateRulesPodFromVariables();
    }

    private async _onLoad(): Promise<void> {
        const { oldRootPaths } = getInputData(this);
        this.oldRootPaths = oldRootPaths;
        this.updateRules.value = (this.updateRules.value ?? []).map(rule => {
            const r: UpdateRule = {
                ...rule,
                destinationPath: convertPathFromSerialization(rule.destinationPath),
            };
            if (r.sourceValue != null && typeof r.sourceValue === 'string') {
                r.sourceValue = convertPathFromSerialization(r.sourceValue);
            }
            return r;
        });
        this.stepVariables.value = transformVariablesFromSerialization(this.stepVariables.value ?? []);
        this.updateRulesPod.value = this.updateRulesPod.value || {};

        await this._updateCurrentVariables([]);
        await this._computeUpdatableVariables();
        const prefix = this.getVariablePrefix();
        this._variablesToUpdate = this.updateRules.value
            .map(rule => {
                // Caution: destinationPath is only a relative path (relative to the pathToUpdate)
                return this._updatableVariables.find(v => v.path === `${prefix}${rule.destinationPath}`);
            })
            .filter(v => v) as WorkflowVariable[];

        this.updateRulesPod.value = await convertUpdateRulesToPodValue(
            this,
            prefix,
            this.updateRules.value,
            this._updatableVariables,
            this._propertyDetails,
        );
        this._setOptionsForDynamicSelects();

        this.onEntityNameChanged();
        await this._onVariablesToUpdateChanged();

        const locale = this.$.locale;
        this.title.value = getTextForLocale(this.localizedTitle.value ?? {}, locale);
        await this._validateFields();
        if (this.$.queryParameters.isReadOnly && this.$.queryParameters.errorMessage) {
            this.readOnlyErrorMessage.value = this.$.queryParameters.errorMessage as string;
        }
    }

    /**
     * Validate the fields of the page.
     */
    private async _validateFields(): Promise<void> {
        await validateDynamicVariablesPickers([this.updateRulesPod, this.pathToUpdate]);
    }

    /**
     * Set the options for the dynamic select fields.
     */
    private _setOptionsForDynamicSelects(): void {
        setOptionsForDynamicSelect(
            this.pathToUpdate,
            this.currentVariables.filter(WorkflowActionUpdateEntity._getVariablesFilterForPathToUpdate),
        );
    }

    /**
     * The filter to apply to the variables list for the "Record to update" path
     */
    private static _getVariablesFilterForPathToUpdate(variable: WorkflowVariable): boolean {
        return variable.path.endsWith('._id');
    }

    // Serialize methods

    /**
     * Returns the paths of the variables used by the action that should be serialized in the action's payload.
     */
    private _getUsedVariablePathsForSerialization(): string[] {
        if (!this.pathToUpdate.value) return [];
        const variableNamesToCheck = this._variablesToUpdate.map(variable => {
            const lastPathPart = variable.path.split('.').pop();
            if (lastPathPart == null) throw new LogicError(`Could not parse path ${variable.path}`);
            return variable.isCustom ? `_customData_${lastPathPart}` : lastPathPart;
        });
        return [
            this.pathToUpdate.value,
            ...getUsedVariablePathsFromDynamicPod(this.updateRulesPod, variableNamesToCheck),
        ];
    }

    /**
     * Compute the payload for the update rules that will be serialized in the diagram
     */
    private _computeUpdateRulesToSerialize(): UpdateRule[] {
        const prefix = this.getVariablePrefix();
        const podValues = this.updateRulesPod.value;
        return this._variablesToUpdate.map(variable => {
            const varName = variable.path.substring(prefix.length);
            const keyForPod = varName.replace('_customData.', '_customData_');
            const rule: UpdateRule = {
                destinationPath: convertPathForSerialization(varName),
                isSourceVariable: podValues[`${keyForPod}/isVariable`] ?? false,
            };
            const podValue = podValues[keyForPod];
            if (podValue == null) return rule;
            if (!rule.isSourceVariable && variable.type === 'IntReference') {
                // When an instance was manually selected, only store its _id, not the whole object
                rule.sourceValue = podValue._id;
            } else {
                rule.sourceValue = podValue;
                if (typeof rule.sourceValue === 'string') {
                    // rule.sourceValue is a string that may contain {{path1}}, {{path2}}, etc.
                    rule.sourceValue = convertPathForSerialization(rule.sourceValue);
                }
            }
            return rule;
        });
    }

    getSerializedValues(): WorkflowConfigPageData<any> {
        const locale = this.$.locale;
        const localizedTitle = mergeLocalizedText(this.localizedTitle.value ?? {}, this.title.value ?? '', locale);
        const usedPaths = this._getUsedVariablePathsForSerialization();
        const stepVariables = getStepVariablesForSerialization(this.currentVariables, usedPaths);
        const updateRules = this._computeUpdateRulesToSerialize();

        return {
            ...pick(this.$.values, 'subtitle', 'pathToUpdate', 'entityName'),
            stepVariables,
            updateRules,
            localizedTitle,
        };
    }
}
