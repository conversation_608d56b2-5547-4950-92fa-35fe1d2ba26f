import { G<PERSON><PERSON><PERSON><PERSON>, MetaNodeFactory } from '@sage/xtrem-metadata-api';
import { LocalizedText, WorkflowVariable, getTextForLocale, mergeLocalizedText } from '@sage/xtrem-shared';
import * as ui from '@sage/xtrem-ui';
import { WorkflowDefinition as WorkflowDefinitionApi } from '@sage/xtrem-workflow-api';
import { camelCase, pick } from 'lodash';
import {
    WorkflowConfigPageData,
    convertFilterEditorValueToWorkflowConditions,
    convertParametersAndConditionsToFieldEditorProperties,
    restoreNodeFieldFromString,
} from '../client-functions';
import { getSelectVariablesButtonDecorator } from '../client-functions/select-variables-button';
import {
    NodeFactoryData,
    convertVariableForSerialization,
    formatSelectedVariables,
    getNodeFactoryVariables,
    getVariablesSummaryDecorator,
    tidyVariables,
    transformVariablesFromSerialization,
} from '../client-functions/variable-utils';
import { WorkflowCondition } from '../shared-functions/workflow-config-types';
import { EntityUpdatedEventConfig } from '../shared-functions/workflow-event-configs';

function getEntityName(nodeFactoryValue: { package?: { name?: string }; name?: string } | null): string {
    return nodeFactoryValue ? `${nodeFactoryValue.package?.name}/${nodeFactoryValue.name}` : '';
}

@ui.decorators.page<WorkflowEventEntityUpdated, WorkflowDefinitionApi>({
    title: 'Trigger configuration',
    subtitle: 'Entity updated',
    mode: 'tabs',
    businessActions() {
        if (this.$.queryParameters.isReadOnly) {
            return [this.$standardCancelAction];
        }
        return [this.$standardDialogConfirmationAction];
    },
    async onLoad() {
        return this._onLoad();
    },
    isDisabled() {
        // Only return true if isReadOnly exists and is true, otherwise return false
        return !!this.$.queryParameters.isReadOnly;
    },
})
export class WorkflowEventEntityUpdated extends ui.Page<GraphApi> {
    @ui.decorators.section<WorkflowEventEntityUpdated>({
        isTitleHidden: true,
        title: 'Configuration',
        isDisabled() {
            return !!this.$.queryParameters.isReadOnly;
        },
    })
    mainSection: ui.containers.Section;

    @ui.decorators.block<WorkflowEventEntityUpdated>({
        parent() {
            return this.mainSection;
        },
    })
    mainBlock: ui.containers.Block;

    /** Only show in read only mode */
    @ui.decorators.messageField<WorkflowEventEntityUpdated>({
        parent() {
            return this.mainBlock;
        },
        variant: 'error',
        isTransient: true,
        isHidden() {
            if (this.$.queryParameters.isReadOnly && this.$.queryParameters.errorMessage) {
                return false;
            }
            return true;
        },
    })
    readOnlyErrorMessage: ui.fields.Message;

    /** Title field */
    @ui.decorators.textField<WorkflowEventEntityUpdated>({
        title: 'Trigger title',
        helperText: 'Title displayed in the workflow diagram.',
        isFullWidth: true,
        isTransient: true,
        parent() {
            return this.mainBlock;
        },
    })
    title: ui.fields.Text;

    // Note: localizedTitle is a JSON object that contains the translations (something like { 'en-US': 'Hello', 'fr-FR': 'Bonjour' })
    // title is only editing the translation for the current locale
    @ui.decorators.technicalJsonField<WorkflowEventEntityUpdated>({})
    localizedTitle: ui.fields.TechnicalJson<LocalizedText>;

    /** Bound field for the subtitle, coupled with the title field */
    @ui.decorators.textField<WorkflowEventEntityUpdated>({})
    subtitle: ui.fields.Text;

    /** Bound field coupled with transient nodeFactory field */
    @ui.decorators.textField<WorkflowEventEntityUpdated>({})
    entityName: ui.fields.Text;

    /** Transient field to select node factory */
    @ui.decorators.referenceField<WorkflowEventEntityUpdated, MetaNodeFactory>({
        parent() {
            return this.mainBlock;
        },
        filter: {
            notifies: {
                _contains: 'updated',
            },
            isAbstract: false,
        },
        orderBy: {
            name: 1,
        },
        columns: [
            ui.nestedFields.text({ bind: 'name', title: 'Record type', isHidden: true }),
            ui.nestedFields.text({ bind: 'title', title: 'Record type' }),
            ui.nestedFields.text({
                bind: { package: { name: true } },
                title: 'Package',
            }),
            ui.nestedFields.technical({ bind: 'naturalKey' }),
        ],
        isTransient: true,
        isMandatory: true,
        node: '@sage/xtrem-metadata/MetaNodeFactory',
        title: 'Record type',
        valueField: 'title',
        helperTextField: { package: { name: true } },
        minLookupCharacters: 0,
        isFullWidth: true,
        async onChange() {
            if (this.nodeFactory.value) {
                const entityName = getEntityName(this.nodeFactory.value);
                if (!this.title.value)
                    this.title.value = ui.localize(
                        '@sage/xtrem-workflow/workflow-entity-updated-default-title',
                        '{{factoryName}} updated',
                        { factoryName: this.nodeFactory.value.title },
                    );

                this.subtitle.value = '';
                this.entityName.value = entityName;
            } else {
                this.title.value = '';
                this.subtitle.value = '';
                this.entityName.value = '';
            }
            await this.selectFactoryVariables([]);
            this.conditions.value = [];
            this.fillFilters([]);
        },
    })
    nodeFactory: ui.fields.Reference<MetaNodeFactory>;

    @ui.decorators.section<WorkflowEventEntityUpdated>({
        isTitleHidden: true,
        title: 'Condition',
        isDisabled() {
            return !!this.$.queryParameters.isReadOnly;
        },
    })
    conditionSection: ui.containers.Section;

    @ui.decorators.block<WorkflowEventEntityUpdated>({
        parent() {
            return this.conditionSection;
        },
    })
    conditionBlock: ui.containers.Block;

    @ui.decorators.technicalJsonField<WorkflowEventEntityUpdated>({})
    stepVariables: ui.fields.TechnicalJson<WorkflowVariable[]>;

    @ui.decorators.staticContentField<WorkflowEventEntityUpdated>({
        ...getVariablesSummaryDecorator(),
        parent() {
            return this.conditionBlock;
        },
    })
    variablesSummary: ui.fields.StaticContent;

    @ui.decorators.buttonField<WorkflowEventEntityUpdated>({
        ...getSelectVariablesButtonDecorator<WorkflowEventEntityUpdated>({
            getSelectedVariables: page => page.currentVariables,
            setSelectedVariables: (page, variables) => page._updateCurrentVariables(variables),
            getOldRootPaths: page => [page.getRootPath()],
        }),
        parent() {
            return this.conditionBlock;
        },
    })
    selectVariablesButton: ui.fields.Button;

    @ui.decorators.technicalJsonField<WorkflowEventEntityUpdated>({})
    conditions: ui.fields.TechnicalJson<WorkflowCondition[]>;

    /** Transient field to enter filters */
    @ui.decorators.filterEditorField<WorkflowEventEntityUpdated>({
        parent() {
            return this.conditionBlock;
        },
        automaticColumnsSpacing: true,
        isTransient: true,
        isDisabled: true,
        isFullWidth: true,
        parameterMode: 'usage',
        title: 'Condition',
        isParentColumnHidden: true,
        helperText:
            'When all conditions are true the execution continues on the path below. If any of the conditions are evaluated to false, the execution continues to the right',
    })
    filters: ui.fields.FilterEditor;

    // Private variables
    private factoryVariables: WorkflowVariable[] = [];

    private currentVariables: WorkflowVariable[] = [];

    // Methods that fill the transient fields based on their associated bound fields.

    private async fillNodeFactory(): Promise<void> {
        await restoreNodeFieldFromString(this, this.nodeFactory, this.entityName.value);
    }

    async selectFactoryVariables(stepVariables: WorkflowVariable[]): Promise<void> {
        if (!this.nodeFactory.value) {
            this.factoryVariables = [];
            this.currentVariables = [];
            this.selectVariablesButton.isDisabled = true;
            this.variablesSummary.value = '';
        } else {
            const prefix = camelCase(this.nodeFactory.value.name ?? '');
            this.factoryVariables = await getNodeFactoryVariables({
                prefix,
                nodeFactory: this.nodeFactory.value as NodeFactoryData,
                locale: this.$.locale ?? 'en-US',
                withOldValues: true,
            });
            const variables = [...this.factoryVariables, ...stepVariables];
            this.currentVariables = tidyVariables([...variables]);

            this.selectVariablesButton.isDisabled = false;
            this.variablesSummary.value = formatSelectedVariables(this.currentVariables);
        }
    }

    private _updateCurrentVariables(variables: WorkflowVariable[]): void {
        this.currentVariables = tidyVariables([...variables, ...this.stepVariables.value]);
        this.variablesSummary.value = formatSelectedVariables(this.currentVariables);
        // Update the filter parameters
        const filtersData = convertParametersAndConditionsToFieldEditorProperties({
            parameters: this.currentVariables,
            conditions: this.conditions.value ?? [],
        });
        Object.assign(this.filters, filtersData);
    }

    private fillFilters(conditions: WorkflowCondition[]): void {
        const filtersData = convertParametersAndConditionsToFieldEditorProperties({
            conditions,
            parameters: this.currentVariables ?? [],
        });
        Object.assign(this.filters, filtersData);
    }

    private async _onLoad(): Promise<void> {
        this.stepVariables.value = transformVariablesFromSerialization(this.stepVariables.value ?? []);
        await this.fillNodeFactory();
        await this.selectFactoryVariables(this.stepVariables.value ?? []);
        this._updateCurrentVariables([]);

        const conditions = this.conditions.value ?? [];
        this.fillFilters(conditions);
        const locale = this.$.locale;
        this.title.value = getTextForLocale(this.localizedTitle.value ?? {}, locale);
        if (this.$.queryParameters.isReadOnly && this.$.queryParameters.errorMessage) {
            this.readOnlyErrorMessage.value = this.$.queryParameters.errorMessage as string;
        }
    }

    // Methods that serialize the values of transient fields.

    private getConditions(): WorkflowCondition[] {
        return convertFilterEditorValueToWorkflowConditions(this.filters.value);
    }

    getRootPath(): string {
        return `${camelCase(this.nodeFactory.value?.name ?? '')}._id`;
    }

    getSerializedValues(): WorkflowConfigPageData<EntityUpdatedEventConfig> {
        const nodeName = this.nodeFactory.value?.name ?? '<unknown>';
        const topic = `${nodeName}/updated`;
        const conditions = this.getConditions();
        const locale = this.$.locale;
        const localizedTitle = mergeLocalizedText(this.localizedTitle.value ?? {}, this.title.value ?? '', locale);

        return {
            ...pick(this.$.values, 'subtitle', 'entityName', 'outputVariableName'),
            topic,
            conditions,
            stepVariables: this.currentVariables.map(convertVariableForSerialization),
            oldRootPaths: [this.getRootPath()],
            localizedTitle,
        };
    }
}
