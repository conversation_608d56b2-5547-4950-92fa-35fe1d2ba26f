import { Dict, LocalizedText, getTextForLocale, mergeLocalizedText } from '@sage/xtrem-shared';
import * as ui from '@sage/xtrem-ui';
import { WorkflowDefinition as WorkflowDefinitionApi } from '@sage/xtrem-workflow-api';
import { omit } from 'lodash';
import { WorkflowConfigPageData } from '../client-functions';

@ui.decorators.page<WorkflowActionWait, WorkflowDefinitionApi>({
    title: 'Action configuration',
    subtitle: 'Wait',
    businessActions() {
        if (this.$.queryParameters.isReadOnly) {
            return [this.$standardCancelAction];
        }
        return [this.$standardDialogConfirmationAction];
    },
    onLoad() {
        this.onLoad();
    },
    isDisabled() {
        // Only return true if isReadOnly exists and is true, otherwise return false
        return !!this.$.queryParameters.isReadOnly;
    },
})
export class WorkflowActionWait extends ui.Page {
    // eslint-disable-next-line class-methods-use-this
    private getTimeUnits(): Dict<string> {
        return {
            ms: ui.localize('@sage/xtrem-workflow/time-unit-ms', 'Milliseconds'),
            s: ui.localize('@sage/xtrem-workflow/time-unit-s', 'Seconds'),
            m: ui.localize('@sage/xtrem-workflow/time-unit-m', 'Minutes'),
            h: ui.localize('@sage/xtrem-workflow/time-unit-h', 'Hours'),
            d: ui.localize('@sage/xtrem-workflow/time-unit-d', 'Days'),
        };
    }

    private updateSubtitle(): void {
        this.subtitle.value = `${this.quantity.value} ${this.unit.value}`;
    }

    @ui.decorators.section<WorkflowActionWait>({
        isTitleHidden: true,
        title: 'Basic Details',
        isDisabled() {
            return !!this.$.queryParameters.isReadOnly;
        },
    })
    mainSection: ui.containers.Section;

    @ui.decorators.block<WorkflowActionWait>({
        parent() {
            return this.mainSection;
        },
    })
    mainBlock: ui.containers.Block;

    /** Only show in read only mode */
    @ui.decorators.messageField<WorkflowActionWait>({
        parent() {
            return this.mainBlock;
        },
        variant: 'error',
        isTransient: true,
        isHidden() {
            if (this.$.queryParameters.isReadOnly && this.$.queryParameters.errorMessage) {
                return false;
            }
            return true;
        },
    })
    readOnlyErrorMessage: ui.fields.Message;

    @ui.decorators.textField<WorkflowActionWait>({
        title: 'Action title',
        helperText: 'Title displayed in the workflow diagram.',
        isFullWidth: true,
        isTransient: true,
        parent() {
            return this.mainBlock;
        },
    })
    title: ui.fields.Text;

    // Note: localizedTitle is a JSON object that contains the translations (something like { 'en-US': 'Hello', 'fr-FR': 'Bonjour' })
    // title is only editing the translation for the current locale
    @ui.decorators.technicalJsonField<WorkflowActionWait>({})
    localizedTitle: ui.fields.TechnicalJson<LocalizedText>;

    @ui.decorators.textField<WorkflowActionWait>({})
    subtitle: ui.fields.Text;

    @ui.decorators.numericField<WorkflowActionWait>({
        parent() {
            return this.mainBlock;
        },
        title: 'Time quantity',
        isMandatory: true,
        onChange() {
            this.updateSubtitle();
        },
    })
    quantity: ui.fields.Numeric;

    @ui.decorators.dropdownListField<WorkflowActionWait>({
        parent() {
            return this.mainBlock;
        },
        title: 'Unit',
        isMandatory: true,
        onChange() {
            this.updateSubtitle();
        },
        options() {
            return Object.keys(this.getTimeUnits());
        },
        map(v: string) {
            return this.getTimeUnits()[v] || v;
        },
    })
    unit: ui.fields.DropdownList;

    // Methods

    onLoad(): void {
        this.quantity.value = this.quantity.value || 1;
        this.unit.value = this.unit.value || 's';
        const locale = this.$.locale;
        this.title.value = getTextForLocale(this.localizedTitle.value ?? {}, locale);
        if (this.$.queryParameters.isReadOnly && this.$.queryParameters.errorMessage) {
            this.readOnlyErrorMessage.value = this.$.queryParameters.errorMessage as string;
        }
    }

    getSerializedValues(): WorkflowConfigPageData<any> {
        const locale = this.$.locale;
        const localizedTitle = mergeLocalizedText(this.localizedTitle.value ?? {}, this.title.value ?? '', locale);

        return {
            ...omit(this.$.values, ['title']),
            localizedTitle,
        };
    }
}
