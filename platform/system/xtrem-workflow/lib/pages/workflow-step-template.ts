import { GraphApi } from '@sage/xtrem-system-api';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.page<WorkflowStepTemplate>({
    node: '@sage/xtrem-workflow/WorkflowStepTemplate',
    title: 'Workflow step template',
    objectTypePlural: 'Workflow step templates',
    mode: 'default',
    module: 'system',
    // Restore later menuItem: automation,

    async onLoad() {
        await this._onLoad();
    },
    headerDropDownActions() {
        return [this.$standardDeleteAction];
    },
    businessActions() {
        if (this.$.queryParameters.isReadOnly) {
            return [this.$standardCancelAction];
        }
        return [this.$standardSaveAction, this.$standardCancelAction];
    },
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.text({ bind: 'title', title: 'Title' }),
            line2: ui.nestedFields.text({ bind: 'description', title: 'Description' }),
        },
    },
})
export class WorkflowStepTemplate extends ui.Page<GraphApi> {
    @ui.decorators.section<WorkflowStepTemplate>({
        title: 'General',
        isTitleHidden: true,
        isDisabled() {
            return !!this.$.queryParameters.isReadOnly;
        },
    })
    generalSection: ui.containers.Section;

    @ui.decorators.block<WorkflowStepTemplate>({
        parent() {
            return this.generalSection;
        },
    })
    generalBlock: ui.containers.Block;

    /** Only show in read only mode */
    @ui.decorators.messageField<WorkflowStepTemplate>({
        parent() {
            return this.generalBlock;
        },
        variant: 'error',
        isTransient: true,
        isHidden() {
            if (this.$.queryParameters.isReadOnly && this.$.queryParameters.errorMessage) {
                return false;
            }
            return true;
        },
    })
    readOnlyErrorMessage: ui.fields.Message;

    @ui.decorators.technicalJsonField<WorkflowStepTemplate>({})
    variant: ui.fields.TechnicalJson<string>;

    @ui.decorators.technicalJsonField<WorkflowStepTemplate>({})
    stepDescriptor: ui.fields.TechnicalJson<string>;

    @ui.decorators.technicalJsonField<WorkflowStepTemplate>({})
    configData: ui.fields.TechnicalJson<any>;

    @ui.decorators.textField<WorkflowStepTemplate>({
        parent() {
            return this.generalBlock;
        },
        title: 'Title',
        isFullWidth: true,
    })
    title: ui.fields.Text;

    @ui.decorators.textField<WorkflowStepTemplate>({
        parent() {
            return this.generalBlock;
        },
        title: 'Description',
        isFullWidth: true,
    })
    description: ui.fields.Text;

    @ui.decorators.switchField<WorkflowStepTemplate>({
        parent() {
            return this.generalBlock;
        },
        width: 'small',
        title: 'Active',
    })
    isActive: ui.fields.Switch;

    @ui.decorators.block<WorkflowStepTemplate>({
        parent() {
            return this.generalSection;
        },
    })
    uiBlock: ui.containers.Block;

    @ui.decorators.dropdownListField<WorkflowStepTemplate>({
        parent() {
            return this.uiBlock;
        },
        width: 'small',
        isFullWidth: true,
        optionType: '@sage/xtrem-core/WorkflowStepIcon',
        title: 'Icon',
    })
    icon: ui.fields.DropdownList;

    @ui.decorators.block<WorkflowStepTemplate>({
        parent() {
            return this.generalSection;
        },
    })
    editBlock: ui.containers.Block;

    @ui.decorators.buttonField<WorkflowStepTemplate>({
        title: 'Edit the action',
        isTransient: true,
        width: 'extra-large',
        map() {
            return 'Edit';
        },
        parent() {
            return this.editBlock;
        },
        async onClick() {
            await this._editAction();
        },
    })
    editActionButton: ui.fields.Button;

    private async _editAction(): Promise<void> {
        if (this._configurationPage == null) return;
        this.configData.value = JSON.stringify(
            await this.$.dialog.page(
                this._configurationPage,
                {},
                { values: JSON.parse(this.configData.value || '{}'), rightAligned: true, size: 'extra-large' },
            ),
        );
    }

    private _configurationPage: string | undefined;

    private async _onLoad(): Promise<void> {
        const stepDescriptor = JSON.parse(this.stepDescriptor.value || '{}');
        this._configurationPage = stepDescriptor.ui?.configurationPage;
        if (this.$.queryParameters.isReadOnly && this.$.queryParameters.errorMessage) {
            this.readOnlyErrorMessage.value = this.$.queryParameters.errorMessage as string;
        }
    }
}
