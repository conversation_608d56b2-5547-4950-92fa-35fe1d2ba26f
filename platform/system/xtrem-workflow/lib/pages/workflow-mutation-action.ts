import { asyncArray } from '@sage/xtrem-async-helper';
import { withoutEdges } from '@sage/xtrem-client';
import { GraphApi, MetaNodeOperation } from '@sage/xtrem-metadata-api';
import { LocalizedText, WorkflowVariable, getTextForLocale, mergeLocalizedText } from '@sage/xtrem-shared';
import {
    DynamicParameter,
    addParametersToDynamicPod,
} from '@sage/xtrem-system/build/lib/client-functions/dynamic-pod-helper';
import * as ui from '@sage/xtrem-ui';
import { WorkflowDefinition as WorkflowDefinitionApi } from '@sage/xtrem-workflow-api';
import { omit, pick } from 'lodash';
import {
    PropertyDetailsFetcher,
    WorkflowConfigPageData,
    convertDynamicPodValueToWorkflowParameters,
    convertWorkflowParametersToDynamicPodValue,
    getInputData,
    getUsedVariablePathsFromDynamicPod,
} from '../client-functions';
import {
    convertPathForSerialization,
    convertPathFromSerialization,
    getNestedDynamicSelectProperties,
    getStepVariablesForSerialization,
    tidyVariables,
    transformVariablesFromSerialization,
    validateDynamicVariablesPickers,
    variableIsAssignableFrom,
} from '../client-functions/variable-utils';
import { WorkflowMutationParameterType } from '../enums/workflow-mutation-parameter-type';
import {
    WorkflowMutationActionConfig,
    WorkflowMutationArgument,
} from '../shared-functions/workflow-mutation-action-config';

interface TypedItem {
    type: string;
    /**
     * When type is 'reference', the name of the referenced node
     */
    node?: string;
    /**
     * When type is 'enum', the name of the referenced enum
     */
    enumType?: string;
}

interface DynamicPodItem extends TypedItem {
    name: string;
    isMandatory: boolean;
}

// GraphQL types that can be mapped with WorkflowMutationParameterType
type WorkflowMutationParameterGraphQlType =
    | 'Int'
    | 'Decimal'
    | 'String'
    | 'Boolean'
    | 'Date'
    | 'IntReference'
    | 'Enum'
    | 'Other';

@ui.decorators.page<WorkflowMutationAction, WorkflowDefinitionApi>({
    title: 'Action configuration',
    subtitle: 'Execute a graphQL mutation',
    mode: 'tabs',
    businessActions() {
        if (this.$.queryParameters.isReadOnly) {
            return [this.$standardCancelAction];
        }
        return [this.$standardDialogConfirmationAction];
    },
    async onLoad() {
        await this._onLoad();
    },
    isDisabled() {
        // Only return true if isReadOnly exists and is true, otherwise return false
        return !!this.$.queryParameters.isReadOnly;
    },
})
export class WorkflowMutationAction extends ui.Page<GraphApi> {
    @ui.decorators.section<WorkflowMutationAction>({
        title: 'Main',
        isDisabled() {
            return !!this.$.queryParameters.isReadOnly;
        },
    })
    mainSection: ui.containers.Section;

    @ui.decorators.block<WorkflowMutationAction>({
        parent() {
            return this.mainSection;
        },
    })
    mainBlock: ui.containers.Block;

    /** Only show in read only mode */
    @ui.decorators.messageField<WorkflowMutationAction>({
        parent() {
            return this.mainBlock;
        },
        variant: 'error',
        isTransient: true,
        isHidden() {
            if (this.$.queryParameters.isReadOnly && this.$.queryParameters.errorMessage) {
                return false;
            }
            return true;
        },
    })
    readOnlyErrorMessage: ui.fields.Message;

    @ui.decorators.section<WorkflowMutationAction>({
        isTitleHidden: true,
        title: 'Mutation',
        isHidden(): boolean {
            return !this._isAdvancedServiceOptionEnabled || this.showAdvancedOptions.value !== true;
        },
        isDisabled() {
            return !!this.$.queryParameters.isReadOnly;
        },
    })
    mutationSection: ui.containers.Section;

    @ui.decorators.block<WorkflowMutationAction>({
        parent() {
            return this.mutationSection;
        },
    })
    mutationBlock: ui.containers.Block;

    @ui.decorators.section<WorkflowMutationAction>({
        isTitleHidden: true,
        title: 'Registration',
        isHidden(): boolean {
            return !this._isAdvancedServiceOptionEnabled || this.showAdvancedOptions.value !== true;
        },
        isDisabled() {
            return !!this.$.queryParameters.isReadOnly;
        },
    })
    registrationSection: ui.containers.Section;

    @ui.decorators.block<WorkflowMutationAction>({
        parent() {
            return this.registrationSection;
        },
    })
    registrationBlock: ui.containers.Block;

    // Note: localizedTitle is a JSON object that contains the translations (something like { 'en-US': 'Hello', 'fr-FR': 'Bonjour' })
    // title is only editing the translation for the current locale
    @ui.decorators.technicalJsonField<WorkflowMutationAction>({})
    localizedTitle: ui.fields.TechnicalJson<LocalizedText>;

    @ui.decorators.textField<WorkflowMutationAction>({
        title: 'Action title',
        helperText: 'Title displayed in the workflow diagram.',
        isFullWidth: true,
        isTransient: true,
        parent() {
            return this.mainBlock;
        },
    })
    title: ui.fields.Text;

    @ui.decorators.technicalJsonField<WorkflowMutationAction>({})
    actionParameters: ui.fields.TechnicalJson<any>;

    @ui.decorators.dynamicPodField<WorkflowMutationAction>({
        title: 'Parameters',
        isFullWidth: true,
        columns: [],
        async onChange() {
            await this._fillActionParametersPod();
        },
        parent() {
            return this.mainBlock;
        },
        isHidden(): boolean {
            if (this._parsedMutationObject == null || this._parsedMutationObject.arguments.length === 0) return true;
            if (
                !this._parsedMutationObject.arguments.some(p => {
                    const origin = this.mutationArgumentsPod.value?.[`${p.name}/origin`];
                    if (origin !== 'fromParameter') {
                        // Only show parameters for arguments set as 'fromParameter'
                        return false;
                    }
                    return true;
                })
            ) {
                // There is no 'fromParameter' argument (or its definition is not valid): hide the 'Parameters' title
                return true;
            }
            return false;
        },
    })
    actionParametersPod: ui.fields.DynamicPod;

    @ui.decorators.checkboxField<WorkflowMutationAction>({
        title: 'Show advanced options',
        helperText: 'Show the advanced options of the action\n\nWARNING: this is reserved for advanced users',
        isFullWidth: true,
        isTransient: true,
        parent() {
            return this.mainBlock;
        },
        isHidden(): boolean {
            return !this._isAdvancedServiceOptionEnabled;
        },
    })
    showAdvancedOptions: ui.fields.Checkbox;

    @ui.decorators.textField<WorkflowMutationAction>({
        parent() {
            return this.mainBlock;
        },
        isFullWidth: true,
        isMandatory: true,
        title: 'Output variable name',
    })
    outputVariableName: ui.fields.Text;

    @ui.decorators.textField<WorkflowMutationAction>({})
    subtitle: ui.fields.Text;

    @ui.decorators.referenceField<WorkflowMutationAction, MetaNodeOperation>({
        parent() {
            return this.mutationBlock;
        },
        title: 'Mutation',
        node: '@sage/xtrem-metadata/MetaNodeOperation',
        valueField: 'name',
        helperTextField: { factory: { name: true } },
        minLookupCharacters: 3,
        lookupDialogTitle: 'Select a mutation',
        isFullWidth: true,
        isMandatory: true,
        isTransient: true,
        filter: {
            action: { _in: ['start', null as any] },
            kind: { _in: ['mutation', 'asyncMutation'] },
            isActive: true,
        },
        async onChange() {
            const mutation = this.mutation.value;
            if (mutation == null) await this._resolveMutation(null);
            else {
                await this._resolveMutation(
                    WorkflowMutationAction._getMutationNaturalKey(mutation as unknown as Partial<MetaNodeOperation>),
                );
            }
        },
        columns: [
            ui.nestedFields.text({ title: 'Node', bind: { factory: { name: true } } }),
            ui.nestedFields.text({ title: 'Mutation', bind: 'name' }),
        ],
        isReadOnly: false,
    })
    mutation: ui.fields.Reference<MetaNodeOperation>;

    @ui.decorators.technicalJsonField<WorkflowMutationAction>({})
    mutationArguments: ui.fields.TechnicalJson<WorkflowMutationArgument[]>;

    @ui.decorators.dynamicPodField<WorkflowMutationAction>({
        title: 'Arguments',
        isFullWidth: true,
        columns: [],
        async onChange() {
            await this._fillMutationArgsPod();
            await this._fillActionParametersPod();
        },
        isHidden(): boolean {
            return this.mutation.value == null;
        },
        parent() {
            return this.mutationBlock;
        },
    })
    mutationArgumentsPod: ui.fields.DynamicPod;

    @ui.decorators.textField<WorkflowMutationAction>({
        title: 'Selector',
        isFullWidth: true,
        parent() {
            return this.mutationBlock;
        },
    })
    selector: ui.fields.Text;

    @ui.decorators.technicalJsonField<WorkflowMutationAction>({})
    mutationNaturalKey: ui.fields.TechnicalJson<string>;

    @ui.decorators.technicalJsonField<WorkflowMutationAction>({})
    stepVariables: ui.fields.TechnicalJson<WorkflowVariable[]>;

    // Note: localizedConfigDescription is a JSON object that contains the translations (something like { 'en-US': 'Hello', 'fr-FR': 'Bonjour' })
    // configDescription is only editing the translation for the current locale
    @ui.decorators.technicalJsonField<WorkflowMutationAction>({})
    localizedConfigDescription: ui.fields.TechnicalJson<LocalizedText>;

    @ui.decorators.textField<WorkflowMutationAction>({
        title: 'Description',
        isHelperTextHidden: true,
        isFullWidth: true,
        isTransient: true,
        parent() {
            return this.registrationBlock;
        },
    })
    configDescription: ui.fields.Text;

    @ui.decorators.checkboxField<WorkflowMutationAction>({
        title: 'Setup node',
        helperText: 'Register the action as a setup node',
        isFullWidth: true,
        isTransient: true,
        parent() {
            return this.registrationBlock;
        },
    })
    configRegisterAsSetupNode: ui.fields.Checkbox;

    @ui.decorators.buttonField<WorkflowMutationAction>({
        title: 'Register the action',
        map() {
            return 'Register';
        },
        parent() {
            return this.registrationBlock;
        },
        async onClick() {
            await this._registerAction();
        },
        onError: () => {
            return 'The registration failed';
        },
    })
    registerActionButton: ui.fields.Button;

    // Private variables

    private currentVariables: WorkflowVariable[] = [];

    private oldRootPaths: string[];

    private _propertyDetailsFetcher = new PropertyDetailsFetcher();

    /**
     * The mutation to invoke
     */
    private _mutationObject: Partial<MetaNodeOperation> | undefined;

    /**
     * Whether the 'workflow advanced' service options is enabled
     */
    private _isAdvancedServiceOptionEnabled: boolean = false;

    private _parsedMutationObject:
        | {
              arguments: WorkflowMutationArgument[];
              returnType?: TypedItem;
          }
        | undefined;

    // Fill methods

    private async _registerAction(): Promise<void> {
        const {
            stepVariables,
            mutationNaturalKey,
            mutationArguments,
            actionParameters,
            localizedTitle,
            localizedConfigDescription,
        } = this._getSerializedValuesFromTransientFields();

        if (localizedTitle == null || localizedTitle.base == null || localizedTitle.base.length === 0) {
            await this.$.dialog.message(
                'error',
                ui.localize('@sage/xtrem-workflow/workflow-dialog-title-error', 'Error'),
                ui.localize('@sage/xtrem-workflow/workflow-error-no-title', 'Title is missing'),
            );
            return;
        }
        if (
            localizedConfigDescription == null ||
            localizedConfigDescription.base == null ||
            localizedConfigDescription.base.length === 0
        ) {
            await this.$.dialog.message(
                'error',
                ui.localize('@sage/xtrem-workflow/workflow-dialog-title-error', 'Error'),
                ui.localize('@sage/xtrem-workflow/workflow-error-no-description', 'Description is missing'),
            );
            return;
        }

        const configData = {
            stepVariables,
            actionParameters,
            mutationArguments,
            selector: this.selector.value,
            outputVariableName: this.outputVariableName.value,
            localizedTitle,
            mutationNaturalKey,
        };
        // Invoke the 'registerMutationAction' mutation on WorkflowStepConfiguration
        await this.$.graph
            .node('@sage/xtrem-workflow/WorkflowStepTemplate')
            .mutations.registerStepConfiguration(
                {
                    _id: true,
                },
                {
                    stepConstructor: 'mutation',
                    asSetupNode: this.configRegisterAsSetupNode.value || false,
                    factory: `#${this._mutationObject?.factory?.name}`,
                    localizedTitle: JSON.stringify(localizedTitle),
                    localizedDescription: JSON.stringify(localizedConfigDescription),
                    configData: JSON.stringify(configData),
                },
            )
            .execute();
        await this.$.dialog.message('info', 'Success', 'The registration succeeded');
    }

    /**
     * Returns the NaturalKey of a mutation
     * @param mutation
     * @returns
     */
    private static _getMutationNaturalKey(mutation: Partial<MetaNodeOperation>): string {
        const parts: string[] = [];
        // Note : mutation.factory and mutation.name cannot be null but
        // Partial<MetaNodeOperation> made them nullable
        parts.push(mutation.factory?.name || '');
        parts.push(mutation.name || '');
        if (mutation?.kind === 'asyncMutation') parts.push('start');
        return parts.join('|');
    }

    private async _resolveMutation(naturalKey: string | null): Promise<void> {
        this._mutationObject = undefined;
        if (naturalKey == null) {
            this.mutation.value = null;
            this.mutationNaturalKey.value = '';
            await this._fillMutationArgsPod();
            return;
        }

        const result = await this.$.graph
            .node('@sage/xtrem-metadata/MetaNodeOperation')
            .query(
                ui.queryUtils.edgesSelector(
                    {
                        _id: true,
                        name: true,
                        parameters: true,
                        return: true,
                        factory: { name: true },
                        kind: true,
                        action: true,
                    },
                    {
                        filter: { _id: `#${naturalKey}` },
                    },
                ),
            )
            .execute();

        const r = withoutEdges(result);
        this.mutation.value = r[0];
        this._mutationObject = r[0] as Partial<MetaNodeOperation>;

        const podValue = this.mutationArgumentsPod.value || {};
        if (this._mutationObject == null) {
            this._parsedMutationObject = undefined;
        } else {
            const validateType = async (parsedItem: any): Promise<void> => {
                if (parsedItem.type !== 'enum') return;
                // Note: in the parameters of the mutation, the dataType is stored but in client side, we need the enum itself
                const enumResult = await this.$.graph
                    .node('@sage/xtrem-metadata/MetaDataType')
                    .query(
                        ui.queryUtils.edgesSelector(
                            {
                                attributes: true,
                            },
                            {
                                filter: { name: parsedItem.dataType },
                            },
                        ),
                    )
                    .execute();

                const dataTypeAttrs = JSON.parse(withoutEdges(enumResult)[0].attributes);
                parsedItem.enumType = dataTypeAttrs.enumName;
            };

            const paramsAsString = this._mutationObject.parameters;

            const mutationArgs =
                paramsAsString == null
                    ? []
                    : await asyncArray(JSON.parse(paramsAsString) as any[])
                          .map(async parsedArg => {
                              await validateType(parsedArg);
                              const arg = {
                                  isMandatory: parsedArg.isMandatory,
                                  origin: podValue[`${parsedArg.name}/origin`],
                                  value: podValue[parsedArg.name],
                                  name: parsedArg.name,
                                  type: parsedArg.type,
                                  node: parsedArg.node,
                                  enumType: parsedArg.enumType,
                              };
                              if (parsedArg.type === 'boolean') {
                                  // Boolean arguments cannot be mandatory as users must be able to choose 'false'
                                  arg.isMandatory = false; // boolean arguments are never mandatory
                              }
                              return arg;
                          })
                          .toArray();

            const returnTypeAsString = this._mutationObject.return;
            let mutationReturnType: TypedItem | undefined;
            if (returnTypeAsString != null) {
                const parsedReturnType = JSON.parse(returnTypeAsString);
                await validateType(parsedReturnType);
                mutationReturnType = {
                    type: parsedReturnType.type,
                    node: parsedReturnType.node,
                    enumType: parsedReturnType.enumType,
                };
            }
            this._parsedMutationObject = { arguments: mutationArgs, returnType: mutationReturnType };
        }

        await this._fillMutationArgsPod();
    }

    /**
     * Fill the dynamic pod that contains the parameters of the action
     */
    private async _fillActionParametersPod(): Promise<void> {
        const argsPodValue = this.mutationArgumentsPod.value || {};
        // Only create action parameters for arguments not set as 'FromParameter'
        const actionParams =
            this._parsedMutationObject == null
                ? []
                : this._parsedMutationObject.arguments.filter(arg => {
                      const origin = argsPodValue[`${arg.name}/origin`];
                      return origin === 'fromParameter';
                  });

        await this._fillDynamicPod(this.actionParametersPod, actionParams, 'actionParameters');
    }

    /**
     * Fill the dynamic pod that contains the arguments of the mutation
     */
    private async _fillMutationArgsPod(): Promise<void> {
        await this._fillDynamicPod(
            this.mutationArgumentsPod,
            this._parsedMutationObject?.arguments || [],
            'mutationArguments',
        );
    }

    /**
     * Maps a WorkflowMutationParameterType to its GraphQL equivalent
     */
    private static _mapParameterTypeToGraphQLType(
        parameterType: WorkflowMutationParameterType,
    ): WorkflowMutationParameterGraphQlType {
        switch (parameterType) {
            case 'integer':
                return 'Int';
            case 'decimal':
                return 'Decimal';
            case 'string':
                return 'String';
            case 'boolean':
                return 'Boolean';
            case 'date':
                return 'Date';
            case 'reference':
                return 'IntReference';
            case 'enum':
                return 'Enum';
            default:
                return 'Other';
        }
    }

    /**
     * Fill a dynamic pod
     */
    private async _fillDynamicPod(
        dynamicPod: ui.fields.DynamicPod,
        items: DynamicPodItem[],
        type: 'actionParameters' | 'mutationArguments',
    ): Promise<void> {
        const locale = this.$.locale ?? 'en-US';

        const dynamicPodValue = dynamicPod.value || {};

        const getPropertiesForDynamicSelects = (
            filter: (_page: ui.Page, variable: WorkflowVariable) => boolean,
        ): any => {
            return getNestedDynamicSelectProperties<WorkflowMutationAction>({
                mode: 'select',
                getVariables: page => page.currentVariables,
                selectableVariableFilter: filter,
                getOldRootPaths: page => page.oldRootPaths,
                updateCurrentVariables: (page, variables) => page._updateCurrentVariables(variables),
            });
        };

        const getPropertiesForDynamicInputs = (
            filter: (_page: ui.Page, variable: WorkflowVariable) => boolean,
        ): any => {
            return getNestedDynamicSelectProperties<WorkflowMutationAction>({
                mode: 'input',
                getVariables: page => page.currentVariables,
                selectableVariableFilter: filter,
                getOldRootPaths: page => page.oldRootPaths,
                updateCurrentVariables: (page, variables) => page._updateCurrentVariables(variables),
            });
        };

        const keysToKeep: string[] = [];

        const podParameters = await asyncArray(items).reduce(
            async (parametersToDisplay: DynamicParameter[], argumentOrParameter: DynamicPodItem) => {
                const matchesType = (variable: WorkflowVariable): boolean => {
                    // Notes:
                    // - the type of arguments is something like string, decimal, integer, boolean, ...
                    // - the type of variables is something like String, Int, IntReference, ...
                    return variableIsAssignableFrom(
                        {
                            ...argumentOrParameter,
                            type: WorkflowMutationAction._mapParameterTypeToGraphQLType(
                                argumentOrParameter.type as WorkflowMutationParameterType,
                            ),
                        },
                        variable,
                    );
                };

                let originSelectorId = '';
                let originSelectorField: DynamicParameter | undefined;
                let selectedOrigin: any;

                if (type === 'mutationArguments') {
                    originSelectorId = `${argumentOrParameter.name}/origin`;
                    selectedOrigin = dynamicPodValue[originSelectorId];
                    // Show a comboBox for (fromParameter/fromVariable/manual)
                    originSelectorField = {
                        _id: originSelectorId,
                        type: 'enum',
                        name: originSelectorId,
                        title: argumentOrParameter.name,
                        isFullWidth: selectedOrigin == null || selectedOrigin === 'fromParameter',
                        enumType: '@sage/xtrem-workflow/WorkflowMutationArgumentOrigin',
                        isMandatory: true,
                        dataType: {
                            name: originSelectorId,
                            attributes: {
                                width: 'small', // ignored if isFullWidth is true
                                onChange: () => {
                                    // Reset the value
                                    dynamicPod.value = omit(dynamicPod.value, argumentOrParameter.name);
                                },
                            },
                        },
                    } as DynamicParameter;
                } else {
                    // Show a comboBox for isManuallyEntered
                    originSelectorId = `${argumentOrParameter.name}/isVariable`;
                    // Note: the checkbox is reversed
                    selectedOrigin = !dynamicPodValue[originSelectorId];

                    originSelectorField = {
                        _id: originSelectorId,
                        type: 'boolean',
                        name: originSelectorId,
                        title: 'Enter manually',
                        dataType: {
                            name: originSelectorId,
                            attributes: {
                                width: 'small',
                                isValueReversed: true,
                                onChange: () => {
                                    // Reset the value
                                    dynamicPod.value = omit(dynamicPod.value, argumentOrParameter.name);
                                },
                            },
                        },
                    } as DynamicParameter;
                }
                keysToKeep.push(originSelectorId);
                keysToKeep.push(argumentOrParameter.name);

                // This field is only used when the 'Enter manually' checkbox is checked
                const fieldWhenManual = async (): Promise<DynamicParameter | null> => {
                    if (argumentOrParameter.type === 'string') {
                        // Use a dynamicSelect in input mode for string variables
                        return {
                            _id: argumentOrParameter.name,
                            name: argumentOrParameter.name,
                            title: argumentOrParameter.name,
                            type: 'dynamicSelect',
                            dataType: {
                                name: argumentOrParameter.name,
                                attributes: getPropertiesForDynamicInputs(
                                    (_page: ui.Page, variable: WorkflowVariable) => matchesType(variable),
                                ),
                            },
                        };
                    }
                    const manualField = {
                        _id: argumentOrParameter.name,
                        title:
                            type === 'mutationArguments' && selectedOrigin === 'manual'
                                ? 'Value'
                                : argumentOrParameter.name,
                        ...argumentOrParameter,
                    } as DynamicParameter;

                    if (argumentOrParameter.type === 'reference') {
                        const nodeDataType = (
                            await this._propertyDetailsFetcher.fetch(this, argumentOrParameter.node || '')
                        ).dataType;
                        manualField.dataType = { attributes: nodeDataType } as any;
                    } else if (argumentOrParameter.type === 'enum') {
                        (manualField as any).enumType = argumentOrParameter.enumType;
                    }
                    return manualField;
                };
                const variableSelection: DynamicParameter = {
                    _id: argumentOrParameter.name,
                    title:
                        type === 'mutationArguments' && selectedOrigin === 'fromVariable'
                            ? 'Variable'
                            : argumentOrParameter.name,
                    ...argumentOrParameter,
                    type: 'dynamicSelect',
                    dataType: {
                        name: argumentOrParameter.name,
                        attributes: getPropertiesForDynamicSelects((_page: ui.Page, variable: WorkflowVariable) => {
                            return matchesType(variable);
                        }),
                    },
                };

                if (type === 'actionParameters') {
                    if (selectedOrigin) {
                        const manualField = await fieldWhenManual();
                        if (manualField == null) {
                            // Do not display the field if the node is not set (same for the checkbox)
                            return parametersToDisplay;
                        }
                        parametersToDisplay.push(manualField);
                    } else {
                        parametersToDisplay.push(variableSelection);
                    }
                    // Add the checkbox
                    parametersToDisplay.push(originSelectorField);
                } else {
                    // Pod for the mutation arguments
                    // Add the combo
                    parametersToDisplay.push(originSelectorField);

                    switch (selectedOrigin) {
                        case 'fromVariable':
                            // Add the field for variable selection
                            parametersToDisplay.push(variableSelection);
                            break;
                        case 'manual': {
                            // Add the field for manual entry
                            const manualField = await fieldWhenManual();
                            if (manualField) parametersToDisplay.push(manualField);
                            break;
                        }
                        default:
                            // The origin is 'fromParameter' or the field is not set (or the parameter's definition not valid)
                            // Nothing to display
                            break;
                    }
                }
                return parametersToDisplay;
            },
            [] as DynamicParameter[],
        );

        // Update the pod value to remove the fields that are not in keysToKeep
        dynamicPod.value = Object.keys(dynamicPod.value).reduce(
            (acc, key) => {
                if (keysToKeep.includes(key)) {
                    acc[key] = dynamicPod.value[key];
                }
                return acc;
            },
            {} as ui.fields.DynamicPod['value'],
        );
        addParametersToDynamicPod({
            dynamicPod,
            locale,
            parameters: podParameters,
            isFullWidth: false,
        });
    }

    private async _updateCurrentVariables(variables: WorkflowVariable[]): Promise<void> {
        // Retrieve the output variables from the previous steps
        const { inputVariables } = getInputData(this);

        this.currentVariables = tidyVariables([...inputVariables, ...this.stepVariables.value, ...variables]);
    }

    /**
     * Converts workflow parameters (stored in the payload of an action) into values for a DynamicPod
     * @param parameters the workflow parameters to convert
     */
    private static _convertWorkflowMutationArgumentsToDynamicPodValue(
        parameters: WorkflowMutationArgument[],
    ): ui.fields.DynamicPod['value'] {
        const value = {} as ui.fields.DynamicPod['value'];
        parameters.forEach(parameter => {
            value[`${parameter.name}/origin`] = parameter.origin;
            value[parameter.name] = parameter.value;
            if (typeof value[parameter.name] === 'string') {
                value[parameter.name] = convertPathFromSerialization(value[parameter.name]);
            }
        });
        return value;
    }

    private async _onLoad(): Promise<void> {
        const { oldRootPaths } = getInputData(this);
        this._isAdvancedServiceOptionEnabled = this.$.isServiceOptionEnabled('workflowAdvanced');
        this.oldRootPaths = oldRootPaths;
        this.selector.value = this.selector.value || '{ _id }';
        this.actionParameters.value = this.actionParameters.value ?? [];
        this.outputVariableName.value = this.outputVariableName.value || 'mutationResult';

        this.stepVariables.value = transformVariablesFromSerialization(this.stepVariables.value ?? []);

        const locale = this.$.locale;
        this.title.value = getTextForLocale(this.localizedTitle.value ?? {}, locale);

        this.mutationArgumentsPod.value = WorkflowMutationAction._convertWorkflowMutationArgumentsToDynamicPodValue(
            (this.mutationArguments.value ?? []).map(a => ({ ...a, isVariable: a.origin === 'fromVariable' })),
        );

        this.actionParametersPod.value = convertWorkflowParametersToDynamicPodValue(this.actionParameters.value ?? []);

        await this._updateCurrentVariables([]);

        await this._resolveMutation(this.mutationNaturalKey.value);

        await this._fillMutationArgsPod();
        await this._fillActionParametersPod();

        await this._validateFields();
        if (this.$.queryParameters.isReadOnly && this.$.queryParameters.errorMessage) {
            this.readOnlyErrorMessage.value = this.$.queryParameters.errorMessage as string;
        }
    }

    /**
     * Validate the fields of the page.
     */
    private async _validateFields(): Promise<void> {
        await validateDynamicVariablesPickers([this.mutationArgumentsPod, this.actionParametersPod]);
    }

    // Serialize methods

    /**
     * Returns the paths of the variables used by the action that should be serialized in the action's payload.
     */
    private _getUsedVariablePathsForSerialization(): string[] {
        const isVariableCheckerForArguments = (variableName: string, dynamicPodValues: any): boolean =>
            dynamicPodValues[`${variableName}/origin`] === 'fromVariable';

        const isVariableCheckerForParameters = (variableName: string, dynamicPodValues: any): boolean =>
            !!dynamicPodValues[`${variableName}/isVariable`];

        const mutationArgsNames = (this._parsedMutationObject?.arguments || []).map(a => a.name);

        return [
            ...getUsedVariablePathsFromDynamicPod(this.mutationArgumentsPod, mutationArgsNames, {
                isVariableChecker: isVariableCheckerForArguments,
            }),
            ...getUsedVariablePathsFromDynamicPod(this.actionParametersPod, mutationArgsNames, {
                isVariableChecker: isVariableCheckerForParameters,
            }),
        ];
    }

    /**
     * Converts values of a DynamicPod into workflow parameters (that will be stored in the payload of an action)
     * @param podValue the value of the DynamicPod
     */
    private _convertDynamicPodValueToWorkflowMutationArguments(
        podValue: ui.fields.DynamicPod['value'],
    ): WorkflowMutationArgument[] {
        if (!podValue) return [];
        return (this._parsedMutationObject?.arguments || []).map(arg => {
            const origin = podValue[`${arg.name}/origin`];
            const getValue = (): any => {
                switch (origin) {
                    case 'fromVariable':
                        return podValue[arg.name];
                    case 'manual': {
                        const v = podValue[arg.name];
                        return v === 'string' ? convertPathForSerialization(v) : v;
                    }
                    default:
                        return undefined;
                }
            };

            return {
                name: arg.name,
                type: arg.type,
                node: arg.node,
                value: getValue(),
                origin,
                isMandatory: arg.isMandatory,
            };
        });
    }

    /**
     * Returns the values to serialize
     */
    private _getSerializedValuesFromTransientFields(): Pick<
        WorkflowMutationActionConfig,
        'mutationNaturalKey' | 'mutationArguments' | 'actionParameters' | 'stepVariables'
    > & { localizedTitle: LocalizedText; localizedConfigDescription: LocalizedText } {
        const locale = this.$.locale;
        const usedPaths = this._getUsedVariablePathsForSerialization();

        return {
            stepVariables: getStepVariablesForSerialization(this.currentVariables, usedPaths),
            mutationNaturalKey:
                this._mutationObject == null ? '' : WorkflowMutationAction._getMutationNaturalKey(this._mutationObject),
            mutationArguments: this._convertDynamicPodValueToWorkflowMutationArguments(this.mutationArgumentsPod.value),
            actionParameters: convertDynamicPodValueToWorkflowParameters(this.actionParametersPod.value),
            localizedTitle: mergeLocalizedText(this.localizedTitle.value ?? {}, this.title.value ?? '', locale),
            localizedConfigDescription: mergeLocalizedText(
                this.localizedConfigDescription.value ?? {},
                this.configDescription.value ?? '',
                locale,
            ),
        };
    }

    getSerializedValues(): WorkflowConfigPageData<WorkflowMutationActionConfig> {
        const { stepVariables, mutationNaturalKey, mutationArguments, actionParameters, localizedTitle } =
            this._getSerializedValuesFromTransientFields();

        return {
            ...pick(this.$.values, 'subtitle', 'selector', 'outputVariableName'),
            localizedTitle,
            stepVariables,
            mutationNaturalKey,
            mutationArguments,
            actionParameters,
            selector: this.selector.value ?? '{_id}',
        };
    }
}
