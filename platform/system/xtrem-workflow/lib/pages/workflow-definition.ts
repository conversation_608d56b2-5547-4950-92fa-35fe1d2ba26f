import { IdType, WorkflowError } from '@sage/xtrem-shared';
import { User } from '@sage/xtrem-system-api';
import { setApplicativePageCrudActions } from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import { confirmDialogToBoolean } from '@sage/xtrem-system/build/lib/client-functions/utils';
import { CurrentUser, getExecutionUser } from '@sage/xtrem-system/lib/client-functions/user';
import * as ui from '@sage/xtrem-ui';
import { ClientNode } from '@sage/xtrem-ui';
import { WorkflowFieldValue } from '@sage/xtrem-ui/build/lib/component/field/workflow/workflow-types';
import { GraphApi, WorkflowDefinition as WorkflowDefinitionNode, WorkflowProcess } from '@sage/xtrem-workflow-api';
import { pick } from 'lodash';
import { logEventPillColor, processStatusPillColor } from '../client-functions/pill-color';
import { automation } from '../menu-items/automation';
import { WorkflowLogEntry } from '../shared-functions/workflow-log';

@ui.decorators.page<WorkflowDefinition, WorkflowDefinitionNode>({
    title: 'Workflow designer',
    objectTypeSingular: 'Workflow',
    objectTypePlural: 'Workflows',
    idField() {
        return this.name;
    },
    node: '@sage/xtrem-workflow/WorkflowDefinition',
    mode: 'tabs',
    menuItem: automation,
    headerSection() {
        return this.headerSection;
    },
    createAction() {
        return this.$standardNewAction;
    },
    headerDropDownActions() {
        return [this.deleteWorkflowDefinition];
    },
    headerQuickActions() {
        return [this.$standardDuplicateAction];
    },
    async onLoad() {
        await this._onLoad();
    },
    businessActions() {
        return [this.$standardSaveAction];
    },
    onDirtyStateUpdated(isDirty: boolean) {
        setApplicativePageCrudActions({
            page: this,
            isDirty,
            save: this.$standardSaveAction,
        });
    },
    navigationPanel: {
        optionsMenu: [{ title: 'All Workflows', graphQLFilter: {} }],
        inlineActions: [
            {
                title: 'Duplicate',
                icon: 'duplicate',
                id: 'duplicate',
                async onClick(rowId: string) {
                    await this.$standardDuplicateAction.execute(false, rowId);
                },
            },
        ],
        listItem: {
            title: ui.nestedFields.text({ bind: 'name', title: 'Name' }),
            titleRight: ui.nestedFields.switch({ bind: 'isActive', title: 'Active' }),
            id: ui.nestedFields.text({ bind: 'id', title: 'ID', isHiddenOnMainField: true }),
            flow: ui.nestedFields.technical({ bind: { diagram: { data: true } } }),
            triggerType: ui.nestedFields.label({
                bind: 'triggerType' as any,
                title: 'Trigger type',
                isHiddenOnMainField: true,
                isTransient: true,
                map(_, recordValue) {
                    const triggerNode = JSON.parse(recordValue.diagram.data || '{}')?.nodes?.[0];
                    return triggerNode?.data?.title || triggerNode?.type || null;
                },
            }),
            triggerDetails: ui.nestedFields.label({
                bind: 'triggerDetails' as any,
                title: 'Trigger details',
                isHiddenOnMainField: true,
                isTransient: true,
                map(_, recordValue) {
                    const triggerNode = JSON.parse(recordValue.diagram.data || '{}')?.nodes?.[0];
                    return triggerNode?.data?.subtitle || null;
                },
            }),
        },
    },
})
export class WorkflowDefinition extends ui.Page<GraphApi, WorkflowDefinitionNode> {
    private _currentUser: CurrentUser | null;

    @ui.decorators.pageAction<WorkflowDefinition>({
        icon: 'bin',
        title: 'Delete',
        isDestructive: true,
        onError() {
            this.$.loader.isHidden = true;
        },
        async onClick() {
            if (this._id.value && (await this._canDelete(this._id.value))) {
                await this.$standardDeleteAction.execute();
            }
        },
    })
    deleteWorkflowDefinition: ui.PageAction;

    @ui.decorators.section<WorkflowDefinition>({
        isTitleHidden: true,
        title: 'Header',
    })
    headerSection: ui.containers.Section;

    @ui.decorators.block<WorkflowDefinition>({
        parent() {
            return this.headerSection;
        },
    })
    mainBlock: ui.containers.Block;

    @ui.decorators.textField<WorkflowDefinition>({
        parent() {
            return this.mainBlock;
        },
        isHidden: true,
    })
    _id: ui.fields.Text;

    @ui.decorators.textField<WorkflowDefinition>({
        parent() {
            return this.mainBlock;
        },
        title: 'ID',
        maxLength: 180,
    })
    id: ui.fields.Text;

    @ui.decorators.textField<WorkflowDefinition>({
        parent() {
            return this.mainBlock;
        },
        title: 'Name',
        width: 'small',
        isMandatory: true,
        maxLength: 180,
    })
    name: ui.fields.Text;

    @ui.decorators.switchField<WorkflowDefinition>({
        parent() {
            return this.mainBlock;
        },
        width: 'small',
        title: 'Active',
    })
    isActive: ui.fields.Switch;

    @ui.decorators.dropdownListField<WorkflowDefinition>({
        parent() {
            return this.mainBlock;
        },
        title: 'Status',
        optionType: '@sage/xtrem-workflow/WorkflowDefinitionStatus',
        width: 'small',
        onChange() {
            this.testUser.isHidden = this.status.value !== 'test';
        },
        isHidden() {
            return !this._currentUser?.isAdministrator;
        },
    })
    status: ui.fields.DropdownList;

    @ui.decorators.referenceField<WorkflowDefinition, User>({
        parent() {
            return this.mainBlock;
        },
        title: 'Test user',
        node: '@sage/xtrem-system/User',
        tunnelPage: '@sage/xtrem-authorization/User',
        valueField: 'displayName',
        minLookupCharacters: 3,
        lookupDialogTitle: 'Select a user',
        helperTextField: 'email',
        isMandatory: false,
        isHidden() {
            // Only administrators can override the execution user
            return !this._currentUser?.isAdministrator;
        },
        columns: [
            ui.nestedFields.text({ title: 'Last name', bind: 'lastName' }),
            ui.nestedFields.text({ title: 'First name', bind: 'firstName' }),
            ui.nestedFields.text({ title: 'Email', bind: 'email' }),
        ],
        isReadOnly: false,
    })
    testUser: ui.fields.Reference<User>;

    @ui.decorators.section<WorkflowDefinition>({
        title: 'Designer',
        isTitleHidden: true,
    })
    designerSection: ui.containers.Section;

    @ui.decorators.workflowField<WorkflowDefinition>({
        parent() {
            return this.designerSection;
        },
        bind: { diagram: { data: true } },
        title: 'Workflow',
        isFullWidth: true,
        validation(value) {
            // Send the workflow to the server for validation
            return this._controlDiagram(value);
        },
    })
    flow: ui.fields.Workflow;

    /**
     * Checks a workflow (server-side validation).
     */
    private async _controlDiagram(diagram: WorkflowFieldValue): Promise<WorkflowError[] | undefined> {
        if (diagram == null) return undefined;
        const { nodes, edges } = diagram;
        if (nodes == null || nodes.length === 0 || edges == null || edges.length === 0) return undefined;

        const nodesData = nodes.map(node => pick(node, 'id', 'type', 'data'));
        const result = await this.$.graph
            .node('@sage/xtrem-workflow/WorkflowDiagram')
            .mutations.controlDiagram(
                { stepId: true, message: true },
                {
                    nodes: JSON.stringify(nodesData),
                    edges: JSON.stringify(edges),
                },
            )
            .execute();
        return result ?? undefined;
    }

    @ui.decorators.referenceField<WorkflowDefinition>({
        isTitleHidden: true,
        node: '@sage/xtrem-workflow/WorkflowDiagram',
        isTransient: true,
        valueField: '_id',
        validation() {
            if (this.flow.value == null || this.flow.value.nodes.length === 0) {
                return ui.localize('@sage/xtrem-workflow/workflow_error_empty_workflow', 'You need to add a trigger.');
            }
            return undefined;
        },
    })
    diagram: ui.fields.Reference;

    @ui.decorators.section<WorkflowDefinition>({
        title: 'Log',
        isTitleHidden: true,
    })
    executionSection: ui.containers.Section;

    @ui.decorators.block<WorkflowDefinition>({
        parent() {
            return this.executionSection;
        },
    })
    executionBlock: ui.containers.Block;

    @ui.decorators.tableField<WorkflowDefinition, WorkflowProcess>({
        parent() {
            return this.executionBlock;
        },
        title: 'Logs',
        bind: 'processes',
        canSelect: true,
        isReadOnly: true,
        fieldActions() {
            return [this.refreshProcesses];
        },
        onRowUnselected() {
            this.clearEventLog();
            this.clearVariables();
        },
        onRowSelected(_rowId, selectedRow) {
            this.processes.value
                .filter(row => row._id !== _rowId)
                .forEach(row => this.processes.unselectRecord(row._id));
            this.fillEventLog(selectedRow);
            this.fillVariables(selectedRow);
        },
        node: '@sage/xtrem-workflow/WorkflowProcess',

        orderBy: { startedAt: -1 },
        columns: [
            ui.nestedFields.reference({
                node: '@sage/xtrem-system/User',
                bind: 'triggeringUser',
                valueField: 'displayName',
                title: 'Triggered by',
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                title: 'Errors',
                bind: 'errorMessages',
                isReadOnly: true,
            }),
            ui.nestedFields.datetime({
                title: 'Start time',
                bind: 'startedAt',
                isReadOnly: true,
            }),
            ui.nestedFields.numeric({ isReadOnly: true, bind: 'duration', title: 'Duration', postfix: 'seconds' }),
            ui.nestedFields.label({
                title: 'Status',
                bind: 'status',
                optionType: '@sage/xtrem-workflow/WorkflowProcessStatus',
                backgroundColor: status => processStatusPillColor(status, 'backgroundColor'),
                borderColor: status => processStatusPillColor(status, 'borderColor'),
                color: status => processStatusPillColor(status, 'textColor'),
            }),
            ui.nestedFields.technical({
                bind: 'eventLog',
            }),
            ui.nestedFields.technical({
                bind: 'variables',
            }),
        ],
        dropdownActions: [
            {
                title: 'View details',
                icon: 'view',
                onClick(rowId) {
                    this.$.router.goTo('@sage/xtrem-workflow/WorkflowProcess', {
                        _id: rowId,
                    });
                },
            },
        ],
    })
    processes: ui.fields.Table<WorkflowProcess, WorkflowDefinition>;

    @ui.decorators.tableField<WorkflowDefinition, WorkflowLogEntry & ClientNode>({
        parent() {
            return this.executionBlock;
        },
        title: 'Event log',
        canSelect: false,
        isReadOnly: true,
        isTransient: true,
        fieldActions() {
            return [];
        },
        orderBy: { timestamp: 1 },

        columns: [
            ui.nestedFields.text({
                title: 'Time stamp',
                bind: 'timestamp',
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                title: 'Step',
                bind: 'stepId',
                isReadOnly: true,
            }),
            ui.nestedFields.label({
                title: 'Status',
                bind: 'event',
                optionType: '@sage/xtrem-workflow/WorkflowProcessStatus',
                backgroundColor: status => logEventPillColor(status, 'backgroundColor'),
                borderColor: status => logEventPillColor(status, 'borderColor'),
                color: status => logEventPillColor(status, 'textColor'),
            }),
            ui.nestedFields.text({
                title: 'Message',
                bind: 'message',
                isReadOnly: true,
            }),
        ],
        dropdownActions: [],
    })
    eventLog: ui.fields.Table<WorkflowLogEntry & ClientNode>;

    @ui.decorators.textAreaField<WorkflowDefinition>({
        parent() {
            return this.executionBlock;
        },
        title: 'Variable details',
        isReadOnly: true,
        isTransient: true,
        isFullWidth: true,
        rows: 12,
    })
    variables: ui.fields.TextArea;

    @ui.decorators.pageAction<WorkflowDefinition>({
        icon: 'refresh',
        title: 'Refresh',
        async onClick() {
            await this.processes.refresh();
        },
    })
    refreshProcesses: ui.PageAction;

    private async _onLoad(): Promise<void> {
        setApplicativePageCrudActions({
            page: this,
            isDirty: false,
            save: this.$standardSaveAction,
        });
        this._currentUser = await getExecutionUser(this);
        if (this.status.value == null) {
            // New workflow
            this.testUser.value = this._currentUser;
            this.status.value = 'test';
            // Hide the ID when creating a new workflow
            this.id.isHidden = true;
        }
        this.testUser.isHidden = !this._currentUser?.isAdministrator || this.status.value !== 'test';

        await this.flow.validate();
    }

    private fillEventLog(selectedRow: WorkflowProcess): void {
        const entries = (selectedRow ? JSON.parse(selectedRow.eventLog) : []) as WorkflowLogEntry[];
        this.eventLog.value = entries.map((entry, i) => ({ _id: (i + 1).toString(), ...entry }));
    }

    private clearEventLog(): void {
        this.eventLog.value = [];
    }

    private fillVariables(selectedRow: WorkflowProcess): void {
        const indent = (depth: number): string => ' '.repeat(8 * depth);
        // Quick and dirty yaml-like formatting
        const format = (value: any, depth = 0): string => {
            if (!value) return value;
            if (typeof value === 'string') return `'${value}'`;
            if (typeof value !== 'object') return String(value);
            if (Array.isArray(value)) return value.map(v => `\n${indent(depth)}- ${format(v, depth + 1)}`).join('\n');
            const properties = Object.entries(value).map(
                ([key, v]) => `${indent(depth)}${key}: ${format(v, depth + 1)}`,
            );
            return `\n${properties.join('\n')}`;
        };
        const variables = selectedRow ? JSON.parse(selectedRow.variables) : {};
        this.variables.value = format(variables).trim();
    }

    private clearVariables(): void {
        this.variables.value = '';
    }

    private async _canDelete(definitionId: IdType): Promise<boolean> {
        const processes = await this.$.graph
            .node('@sage/xtrem-workflow/WorkflowProcess')
            .query({
                __args: { filter: JSON.stringify({ definition: definitionId }) },
                totalCount: true,
            } as any)
            .execute();

        if (processes.totalCount === 0) return true;

        return confirmDialogToBoolean(
            this.$.dialog.confirmation(
                'warn',
                ui.localize('@sage/xtrem-workflow/workflow-delete-definition-confirm-title', 'Confirm delete'),
                ui.localize(
                    '@sage/xtrem-workflow/workflow-delete-definition-confirm-body',
                    'You are about to delete {{totalCount}} running processes. Delete these processes or cancel?',
                    { totalCount: processes.totalCount },
                ),
            ),
        );
    }
}
