import { MetaNodeFactory } from '@sage/xtrem-metadata-api';
import {
    LocalizedText,
    WorkflowVariableType,
    getTextForLocale,
    mergeLocalizedText,
    titleCase,
} from '@sage/xtrem-shared';
import * as ui from '@sage/xtrem-ui';
import { WorkflowDefinition as WorkflowDefinitionApi } from '@sage/xtrem-workflow-api';
import { omit } from 'lodash';
import { WorkflowConfigPageData } from '../client-functions';

const topic = 'WorkflowProcess/testStarted';

interface ParameterType {
    _id: string;
    name: string;
    type: WorkflowVariableType;
    node?: MetaNodeFactory;
}

@ui.decorators.page<WorkflowEventTestStarted, WorkflowDefinitionApi>({
    title: 'Trigger configuration',
    subtitle: 'Test started',
    businessActions() {
        if (this.$.queryParameters.isReadOnly) {
            return [this.$standardCancelAction];
        }
        return [this.$standardDialogConfirmationAction];
    },
    onLoad() {
        this.onLoad();
    },
    isDisabled() {
        // Only return true if isReadOnly exists and is true, otherwise return false
        return !!this.$.queryParameters.isReadOnly;
    },
})
export class WorkflowEventTestStarted extends ui.Page {
    @ui.decorators.section<WorkflowEventTestStarted>({
        isTitleHidden: true,
        title: 'Basic Details',
        isDisabled() {
            return !!this.$.queryParameters.isReadOnly;
        },
    })
    mainSection: ui.containers.Section;

    @ui.decorators.block<WorkflowEventTestStarted>({
        parent() {
            return this.mainSection;
        },
    })
    mainBlock: ui.containers.Block;

    /** Only show in read only mode */
    @ui.decorators.messageField<WorkflowEventTestStarted>({
        parent() {
            return this.mainBlock;
        },
        variant: 'error',
        isTransient: true,
        isHidden() {
            if (this.$.queryParameters.isReadOnly && this.$.queryParameters.errorMessage) {
                return false;
            }
            return true;
        },
    })
    readOnlyErrorMessage: ui.fields.Message;

    @ui.decorators.textField<WorkflowEventTestStarted>({
        title: 'Trigger title',
        helperText: 'Title displayed in the workflow diagram.',
        isFullWidth: true,
        isTransient: true,
        parent() {
            return this.mainBlock;
        },
    })
    title: ui.fields.Text;

    // Note: localizedTitle is a JSON object that contains the translations (something like { 'en-US': 'Hello', 'fr-FR': 'Bonjour' })
    // title is only editing the translation for the current locale
    @ui.decorators.technicalJsonField<WorkflowEventTestStarted>({})
    localizedTitle: ui.fields.TechnicalJson<LocalizedText>;

    @ui.decorators.section<WorkflowEventTestStarted>({
        isTitleHidden: true,
        title: 'Parameters',
        isDisabled() {
            return !!this.$.queryParameters.isReadOnly;
        },
    })
    parametersSection: ui.containers.Section;

    @ui.decorators.podCollectionField<WorkflowEventTestStarted>({
        parent() {
            return this.parametersSection;
        },
        title: 'Test parameters',
        isTitleHidden: true,
        isFullWidth: true,
        recordWidth: 'large',
        recordTitle(_, recordValue) {
            const index = this.parameters.value?.findIndex(s => s._id === recordValue?._id);
            if (index === -1) {
                return 'Parameter';
            }
            return `Parameter ${index + 1}`;
        },
        columns: [
            ui.nestedFields.text({
                title: 'Parameter name',
                bind: 'name',
                width: 'medium',
                isHelperTextHidden: true,
                isMandatory: true,
            }),
            ui.nestedFields.select({
                bind: 'type',
                title: 'Type',
                options: ['String', 'Boolean', 'Int', 'Decimal', 'Date', 'Datetime', 'IntReference'],
                width: 'medium',
                isHelperTextHidden: true,
                isMandatory: true,
            }),
            ui.nestedFields.reference({
                bind: 'node',
                title: 'Node',
                node: '@sage/xtrem-metadata/MetaNodeFactory',
                isFullWidth: true,
                placeholder: 'Select referenced node',
                minLookupCharacters: 0,
                lookupDialogTitle: 'Select referenced node',
                valueField: 'name',
                isMandatory: true,
                columns: [
                    ui.nestedFields.text({ bind: 'name', title: 'Record type', isHidden: true }),
                    ui.nestedFields.text({ bind: 'title', title: 'Record type' }),
                    ui.nestedFields.text({
                        bind: { package: { name: true } },
                        title: 'Package',
                    }),
                    ui.nestedFields.technical({ bind: 'naturalKey' }),
                ],
                isHidden(_id, record) {
                    return record?.type !== 'IntReference';
                },
                isHelperTextHidden: true,
            }),
        ],
        canAddRecord: true,
        canRemoveRecord: true,
        addButtonText: 'Add a parameter',
    })
    parameters: ui.fields.PodCollection<ParameterType>;

    // Methods

    onLoad(): void {
        const locale = this.$.locale;
        this.title.value = getTextForLocale(this.localizedTitle.value ?? {}, locale);
        this.parameters.value = (this.parameters.value ?? []).map(parameter => {
            return {
                ...parameter,
                node: parameter.node == null ? undefined : ({ name: parameter.node } as any),
            };
        });
        if (this.$.queryParameters.isReadOnly && this.$.queryParameters.errorMessage) {
            this.readOnlyErrorMessage.value = this.$.queryParameters.errorMessage as string;
        }
    }

    getSerializedValues(): WorkflowConfigPageData<any> {
        const locale = this.$.locale;
        const localizedTitle = mergeLocalizedText(this.localizedTitle.value ?? {}, this.title.value ?? '', locale);

        const stepVariables = this.parameters.value.map(parameter => {
            return {
                path: `test.parameters.${parameter.name}`,
                title: titleCase(parameter.name),
                type: parameter.type,
                node: parameter.node?.name,
            };
        });

        const parameters = this.parameters.value.map(parameter => {
            const out = { name: parameter.name, type: parameter.type } as any;
            if (parameter.type === 'IntReference') {
                out.node = parameter.node?.name;
            }
            return out;
        });

        return {
            ...omit(this.$.values, 'title'),
            topic,
            parameters,
            stepVariables,
            localizedTitle,
        };
    }
}
