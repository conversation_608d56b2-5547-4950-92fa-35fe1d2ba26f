import { Graph<PERSON>pi, MetaNodeFactory } from '@sage/xtrem-metadata-api';
import { LocalizedText, WorkflowVariable, getTextForLocale, mergeLocalizedText } from '@sage/xtrem-shared';
import * as ui from '@sage/xtrem-ui';
import { WorkflowDefinition as WorkflowDefinitionApi } from '@sage/xtrem-workflow-api';
import { camelCase, pick } from 'lodash';
import { WorkflowConfigPageData, restoreNodeFieldFromString } from '../client-functions';
import {
    NodeFactoryData,
    convertVariableForSerialization,
    getNodeFactoryVariables,
} from '../client-functions/variable-utils';

@ui.decorators.page<WorkflowActionReadEntity, WorkflowDefinitionApi>({
    title: 'Action configuration',
    subtitle: 'Read a record',
    businessActions() {
        if (this.$.queryParameters.isReadOnly) {
            return [this.$standardCancelAction];
        }
        return [this.$standardDialogConfirmationAction];
    },
    async onLoad() {
        await this.onLoad();
    },
    isDisabled() {
        // Only return true if isReadOnly exists and is true, otherwise return false
        return !!this.$.queryParameters.isReadOnly;
    },
})
export class WorkflowActionReadEntity extends ui.Page<GraphApi> {
    @ui.decorators.section<WorkflowActionReadEntity>({
        isTitleHidden: true,
        title: 'Basic Details',
        isDisabled() {
            return !!this.$.queryParameters.isReadOnly;
        },
    })
    mainSection: ui.containers.Section;

    @ui.decorators.block<WorkflowActionReadEntity>({
        parent() {
            return this.mainSection;
        },
    })
    mainBlock: ui.containers.Block;

    /** Only show in read only mode */
    @ui.decorators.messageField<WorkflowActionReadEntity>({
        parent() {
            return this.mainBlock;
        },
        variant: 'error',
        isTransient: true,
        isHidden() {
            if (this.$.queryParameters.isReadOnly && this.$.queryParameters.errorMessage) {
                return false;
            }
            return true;
        },
    })
    readOnlyErrorMessage: ui.fields.Message;

    @ui.decorators.textField<WorkflowActionReadEntity>({
        title: 'Action title',
        helperText: 'Title displayed in the workflow diagram.',
        isFullWidth: true,
        isTransient: true,
        parent() {
            return this.mainBlock;
        },
    })
    title: ui.fields.Text;

    // Note: localizedTitle is a JSON object that contains the translations (something like { 'en-US': 'Hello', 'fr-FR': 'Bonjour' })
    // title is only editing the translation for the current locale
    @ui.decorators.technicalJsonField<WorkflowActionReadEntity>({})
    localizedTitle: ui.fields.TechnicalJson<LocalizedText>;

    @ui.decorators.textField<WorkflowActionReadEntity>({})
    subtitle: ui.fields.Text;

    @ui.decorators.textField<WorkflowActionReadEntity>({})
    entityName: ui.fields.Text;

    @ui.decorators.referenceField<WorkflowActionReadEntity, MetaNodeFactory>({
        parent() {
            return this.mainBlock;
        },
        orderBy: {
            name: 1,
        },
        columns: [
            ui.nestedFields.text({ bind: 'name', title: 'Record type', isHidden: true }),
            ui.nestedFields.text({ bind: 'title', title: 'Record type' }),
            ui.nestedFields.text({
                bind: { package: { name: true } },
                title: 'Package',
            }),
            ui.nestedFields.technical({ bind: 'naturalKey' }),
        ],
        isMandatory: true,
        isTransient: true,
        node: '@sage/xtrem-metadata/MetaNodeFactory',
        title: 'Record type',
        valueField: 'title',
        helperTextField: { package: { name: true } },
        minLookupCharacters: 0,
        isFullWidth: true,
        async onChange() {
            if (this.nodeFactory.value) {
                const entityName = `${this.nodeFactory.value.package?.name}/${this.nodeFactory.value.name}`;
                this.subtitle.value = '';
                this.entityName.value = entityName;
                this.outputVariableName.value = camelCase(this.nodeFactory.value.name);
            } else {
                this.subtitle.value = '';
                this.entityName.value = '';
            }
            await this.onNodeFactoryChange();
        },
    })
    nodeFactory: ui.fields.Reference<MetaNodeFactory>;

    @ui.decorators.textField<WorkflowActionReadEntity>({
        parent() {
            return this.mainBlock;
        },
        isFullWidth: true,
        isMandatory: true,
        title: 'Record key',
    })
    recordKey: ui.fields.Text;

    @ui.decorators.switchField<WorkflowActionReadEntity>({
        parent() {
            return this.mainBlock;
        },
        isFullWidth: true,
        title: 'Fail if not found',
        helperText: 'Raise an error if no record is found.',
    })
    failIfNotFound: ui.fields.Switch;

    @ui.decorators.textField<WorkflowActionReadEntity>({
        parent() {
            return this.mainBlock;
        },
        isFullWidth: true,
        isMandatory: true,
        title: 'Output variable name',
    })
    outputVariableName: ui.fields.Text;

    // Private variables
    private currentVariables: WorkflowVariable[] = [];

    private async onNodeFactoryChange(): Promise<void> {
        if (this.nodeFactory.value) {
            this.currentVariables = await getNodeFactoryVariables({
                prefix: this.outputVariableName.value ?? camelCase(this.nodeFactory.value.name),
                nodeFactory: this.nodeFactory.value as NodeFactoryData,
                locale: this.$.locale ?? 'en-US',
            });
        } else {
            this.currentVariables = [];
        }
    }

    async onLoad(): Promise<void> {
        await restoreNodeFieldFromString(this, this.nodeFactory, this.entityName.value);
        await this.onNodeFactoryChange();
        if (this.failIfNotFound.value == null) {
            this.failIfNotFound.value = true;
        }
        const locale = this.$.locale;
        this.title.value = getTextForLocale(this.localizedTitle.value ?? {}, locale);
        if (this.$.queryParameters.isReadOnly && this.$.queryParameters.errorMessage) {
            this.readOnlyErrorMessage.value = this.$.queryParameters.errorMessage as string;
        }
    }

    getSerializedValues(): WorkflowConfigPageData<any> {
        const locale = this.$.locale;
        const localizedTitle = mergeLocalizedText(this.localizedTitle.value ?? {}, this.title.value ?? '', locale);
        return {
            ...pick(this.$.values, 'subtitle', 'entityName', 'recordKey', 'failIfNotFound', 'outputVariableName'),
            stepVariables: this.currentVariables.map(convertVariableForSerialization),
            localizedTitle,
        };
    }
}
