import { Graph<PERSON>pi, MetaNodeFactory } from '@sage/xtrem-metadata-api';
import { LocalizedText, WorkflowVariable, getTextForLocale, mergeLocalizedText } from '@sage/xtrem-shared';
import * as ui from '@sage/xtrem-ui';
import { WorkflowDefinition as WorkflowDefinitionApi } from '@sage/xtrem-workflow-api';
import { camelCase, omit } from 'lodash';
import { WorkflowConfigPageData, restoreNodeFieldFromString } from '../client-functions';

@ui.decorators.page<WorkflowEventEntityDeleted, WorkflowDefinitionApi>({
    title: 'Trigger configuration',
    subtitle: 'Entity deleted',
    businessActions() {
        if (this.$.queryParameters.isReadOnly) {
            return [this.$standardCancelAction];
        }
        return [this.$standardDialogConfirmationAction];
    },
    async onLoad() {
        await this.onLoad();
    },
    isDisabled() {
        // Only return true if isReadOnly exists and is true, otherwise return false
        return !!this.$.queryParameters.isReadOnly;
    },
})
export class WorkflowEventEntityDeleted extends ui.Page<GraphApi> {
    @ui.decorators.section<WorkflowEventEntityDeleted>({
        isTitleHidden: true,
        title: 'Basic Details',
        isDisabled() {
            return !!this.$.queryParameters.isReadOnly;
        },
    })
    mainSection: ui.containers.Section;

    @ui.decorators.textField<WorkflowEventEntityDeleted>({
        title: 'Trigger title',
        helperText: 'Title displayed in the workflow diagram.',
        isFullWidth: true,
        isTransient: true,
        parent() {
            return this.mainBlock;
        },
    })
    title: ui.fields.Text;

    // Note: localizedTitle is a JSON object that contains the translations (something like { 'en-US': 'Hello', 'fr-FR': 'Bonjour' })
    // title is only editing the translation for the current locale
    @ui.decorators.technicalJsonField<WorkflowEventEntityDeleted>({})
    localizedTitle: ui.fields.TechnicalJson<LocalizedText>;

    @ui.decorators.block<WorkflowEventEntityDeleted>({
        parent() {
            return this.mainSection;
        },
    })
    mainBlock: ui.containers.Block;

    /** Only show in read only mode */
    @ui.decorators.messageField<WorkflowEventEntityDeleted>({
        parent() {
            return this.mainBlock;
        },
        variant: 'error',
        isTransient: true,
        isHidden() {
            if (this.$.queryParameters.isReadOnly && this.$.queryParameters.errorMessage) {
                return false;
            }
            return true;
        },
    })
    readOnlyErrorMessage: ui.fields.Message;

    @ui.decorators.textField<WorkflowEventEntityDeleted>({})
    subtitle: ui.fields.Text;

    @ui.decorators.textField<WorkflowEventEntityDeleted>({})
    node: ui.fields.Text;

    @ui.decorators.referenceField<WorkflowEventEntityDeleted, MetaNodeFactory>({
        parent() {
            return this.mainBlock;
        },
        orderBy: {
            name: 1,
        },
        columns: [
            ui.nestedFields.text({ bind: 'name', title: 'Record type', isHidden: true }),
            ui.nestedFields.text({ bind: 'title', title: 'Record type' }),
            ui.nestedFields.text({
                bind: { package: { name: true } },
                title: 'Package',
            }),
        ],
        isMandatory: true,
        node: '@sage/xtrem-metadata/MetaNodeFactory',
        title: 'Record type',
        valueField: 'title',
        helperTextField: { package: { name: true } },
        minLookupCharacters: 0,
        isFullWidth: true,
        onChange() {
            if (this.dataEntity.value) {
                const entityName = `${this.dataEntity.value.package?.name}/${this.dataEntity.value.name}`;
                this.outputVariableName.value = camelCase(this.dataEntity.value.name);
                if (!this.title.value)
                    this.title.value = ui.localize(
                        '@sage/xtrem-workflow/workflow-entity-deleted-default-title',
                        '{{factoryName}} deleted',
                        { factoryName: this.dataEntity.value.title },
                    );
                this.subtitle.value = entityName;
                this.node.value = entityName;
            } else {
                this.subtitle.value = '';
                this.node.value = '';
            }
        },
    })
    dataEntity: ui.fields.Reference<MetaNodeFactory>;

    @ui.decorators.textField<WorkflowEventEntityDeleted>({
        parent() {
            return this.mainBlock;
        },
        isFullWidth: true,
        isMandatory: true,
        title: 'Output variable name',
    })
    outputVariableName: ui.fields.Text;

    async onLoad(): Promise<void> {
        await restoreNodeFieldFromString(this, this.dataEntity, this.node.value);
        const locale = this.$.locale;
        this.title.value = getTextForLocale(this.localizedTitle.value ?? {}, locale);
        if (this.$.queryParameters.isReadOnly && this.$.queryParameters.errorMessage) {
            this.readOnlyErrorMessage.value = this.$.queryParameters.errorMessage as string;
        }
    }

    getSerializedValues(): WorkflowConfigPageData<any> {
        const locale = this.$.locale;
        const localizedTitle = mergeLocalizedText(this.localizedTitle.value ?? {}, this.title.value ?? '', locale);

        const nodeName = this.dataEntity.value?.name ?? '<unknown>';
        const topic = `${nodeName}/deleted`;
        const stepVariables: WorkflowVariable[] = [
            {
                path: `${this.outputVariableName.value}._id`,
                title: `Deleted ${nodeName} ID`,
                type: 'String',
            },
        ];

        return {
            ...omit(this.$.values, 'title'),
            topic,
            stepVariables,
            localizedTitle,
        };
    }
}
