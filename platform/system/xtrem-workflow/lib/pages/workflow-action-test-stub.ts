import { Graph<PERSON>pi } from '@sage/xtrem-metadata-api';
import { LocalizedText, WorkflowVariable, getTextForLocale, mergeLocalizedText } from '@sage/xtrem-shared';
import * as ui from '@sage/xtrem-ui';
import { WorkflowDefinition as WorkflowDefinitionApi } from '@sage/xtrem-workflow-api';
import { omit } from 'lodash';
import { WorkflowConfigPageData } from '../client-functions';

@ui.decorators.page<WorkflowActionTestStub, WorkflowDefinitionApi>({
    title: 'Action configuration',
    subtitle: 'Test stub',
    mode: 'tabs',
    businessActions() {
        if (this.$.queryParameters.isReadOnly) {
            return [this.$standardCancelAction];
        }
        return [this.$standardDialogConfirmationAction];
    },
    onLoad() {
        this.onLoad();
    },
    isDisabled() {
        // Only return true if isReadOnly exists and is true, otherwise return false
        return !!this.$.queryParameters.isReadOnly;
    },
})
export class WorkflowActionTestStub extends ui.Page<GraphApi> {
    @ui.decorators.section<WorkflowActionTestStub>({
        isTitleHidden: true,
        title: 'Basic Details',
        isDisabled() {
            return !!this.$.queryParameters.isReadOnly;
        },
    })
    mainSection: ui.containers.Section;

    @ui.decorators.block<WorkflowActionTestStub>({
        parent() {
            return this.mainSection;
        },
    })
    mainBlock: ui.containers.Block;

    /** Only show in read only mode */
    @ui.decorators.messageField<WorkflowActionTestStub>({
        parent() {
            return this.mainBlock;
        },
        variant: 'error',
        isTransient: true,
        isHidden() {
            if (this.$.queryParameters.isReadOnly && this.$.queryParameters.errorMessage) {
                return false;
            }
            return true;
        },
    })
    readOnlyErrorMessage: ui.fields.Message;

    @ui.decorators.textField<WorkflowActionTestStub>({
        title: 'Action title',
        helperText: 'Title displayed in the workflow diagram.',
        isFullWidth: true,
        isTransient: true,
        parent() {
            return this.mainBlock;
        },
    })
    title: ui.fields.Text;

    // Note: localizedTitle is a JSON object that contains the translations (something like { 'en-US': 'Hello', 'fr-FR': 'Bonjour' })
    // title is only editing the translation for the current locale
    @ui.decorators.technicalJsonField<WorkflowActionTestStub>({})
    localizedTitle: ui.fields.TechnicalJson<LocalizedText>;

    @ui.decorators.textField<WorkflowActionTestStub>({})
    subtitle: ui.fields.Text;

    @ui.decorators.textField<WorkflowActionTestStub>({
        parent() {
            return this.mainBlock;
        },
        isFullWidth: true,
        isMandatory: true,
        title: 'Output variable name',
    })
    outputVariableName: ui.fields.Text;

    onLoad(): void {
        const locale = this.$.locale;
        this.title.value = getTextForLocale(this.localizedTitle.value ?? {}, locale);
        if (this.$.queryParameters.isReadOnly && this.$.queryParameters.errorMessage) {
            this.readOnlyErrorMessage.value = this.$.queryParameters.errorMessage as string;
        }
    }

    getSerializedValues(): WorkflowConfigPageData<any> {
        const locale = this.$.locale;
        const localizedTitle = mergeLocalizedText(this.localizedTitle.value ?? {}, this.title.value ?? '', locale);

        const titleArg = this.title.value ? ` ({${this.title.value}})` : '';
        const stepVariables: WorkflowVariable[] = [
            {
                path: `${this.outputVariableName.value}`,
                title: `End test result${titleArg}`,
                type: 'Float',
            },
        ];

        return {
            stepVariables,
            ...omit(this.$.values, 'title'),
            localizedTitle,
        };
    }
}
