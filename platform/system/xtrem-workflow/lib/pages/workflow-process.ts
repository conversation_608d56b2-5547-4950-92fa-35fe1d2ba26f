import * as ui from '@sage/xtrem-ui';
import { WorkflowProcess as WorkflowProcessApi } from '@sage/xtrem-workflow-api';
import { logEventPillColor, processStatusPillColor } from '../client-functions/pill-color';
import { automation } from '../menu-items/automation';
import { WorkflowLogEntry } from '../shared-functions/workflow-log';

@ui.decorators.page<WorkflowProcess, WorkflowProcessApi>({
    module: '',
    title: 'Workflow logs',
    objectTypeSingular: 'Workflow log',
    objectTypePlural: 'Workflow logs',
    menuItem: automation,
    idField() {
        return `${this.workflow.value} - Run #${this._id.value}`;
    },
    node: '@sage/xtrem-workflow/WorkflowProcess',
    mode: 'tabs',
    businessActions() {
        return [this.goToDesigner];
    },
    headerSection() {
        return this.headerSection;
    },
    onLoad() {
        this.flow.eventLogs = this.eventLog.value;
        this.fillEventLog();
        this.fillVariables();
    },
    navigationPanel: {
        bulkActions: [{ mutation: 'asyncExport', title: 'Export', icon: 'export', buttonType: 'primary' }],
        optionsMenu: [{ title: 'All Processes', graphQLFilter: {} }],
        orderBy: {
            startedAt: -1,
        },
        listItem: {
            title: ui.nestedFields.text({ bind: '_id', title: 'ID' }),
            triggeredBy: ui.nestedFields.reference({
                node: '@sage/xtrem-system/User',
                valueField: 'displayName',
                title: 'Triggered by',
                bind: 'triggeringUser',
                isReadOnly: true,
            }),
            line2: ui.nestedFields.reference({
                node: '@sage/xtrem-workflow/WorkflowDefinition',
                valueField: 'name',
                title: 'Workflow',
                bind: 'definition',
            }),
            titleRight: ui.nestedFields.label({
                title: 'Status',
                bind: 'status',
                optionType: '@sage/xtrem-workflow/WorkflowProcessStatus',
                backgroundColor: status => processStatusPillColor(status, 'backgroundColor'),
                borderColor: status => processStatusPillColor(status, 'borderColor'),
                color: status => processStatusPillColor(status, 'textColor'),
            }),
            errorMessage: ui.nestedFields.text({ bind: 'errorMessages', title: 'Error reason' }),
            duration: ui.nestedFields.numeric({ bind: 'duration', title: 'Duration', postfix: 'seconds' }),
            startedAt: ui.nestedFields.datetime({ bind: 'startedAt', title: 'Start time' }),

            flow: ui.nestedFields.technical({ bind: { diagram: { data: true } } }),
        },
    },
})
export class WorkflowProcess extends ui.Page {
    @ui.decorators.pageAction<WorkflowProcess>({
        title: 'Go to the designer',
        onClick() {
            this.$.router.goTo('@sage/xtrem-workflow/WorkflowDefinition', {
                _id: String(this.workflowDefinitionId.value),
            });
        },
    })
    goToDesigner: ui.PageAction;

    @ui.decorators.section<WorkflowProcess>({
        isTitleHidden: true,
        title: 'Header',
    })
    headerSection: ui.containers.Section;

    @ui.decorators.block<WorkflowProcess>({
        parent() {
            return this.headerSection;
        },
    })
    headerBlock: ui.containers.Block;

    @ui.decorators.messageField<WorkflowProcess>({
        parent() {
            return this.headerBlock;
        },
        isTransient: true,
        isMarkdown: true,
        isHidden() {
            // If the workflow definition ID and the process flow ID are the same, then the process is the latest version so we hide the message
            return this.definitionDiagramId.value === this.processFlowId.value;
        },
        content: '**You are viewing an older version of this workflow.**',
    })
    olderVersionMessage: ui.fields.Message;

    @ui.decorators.block<WorkflowProcess>({
        parent() {
            return this.headerSection;
        },
    })
    mainBlock: ui.containers.Block;

    @ui.decorators.textField<WorkflowProcess>({
        parent() {
            return this.mainBlock;
        },
        isReadOnly: true,
        title: 'ID',
    })
    _id: ui.fields.Text;

    @ui.decorators.textField<WorkflowProcess>({
        parent() {
            return this.mainBlock;
        },
        isReadOnly: true,
        isHidden: true,
    })
    id: ui.fields.Text;

    @ui.decorators.textField<WorkflowProcess>({
        parent() {
            return this.mainBlock;
        },
        isReadOnly: true,
        isHidden: true,
        bind: { diagram: { _id: true } },
    })
    processFlowId: ui.fields.Text;

    @ui.decorators.textField<WorkflowProcess>({
        parent() {
            return this.mainBlock;
        },
        isReadOnly: true,
        isHidden: true,
        bind: { definition: { diagram: { _id: true } } },
    })
    definitionDiagramId: ui.fields.Text;

    @ui.decorators.textField<WorkflowProcess>({
        parent() {
            return this.mainBlock;
        },
        isReadOnly: true,
        title: 'Workflow',
        bind: { definition: { name: true } },
        isHidden: true,
    })
    workflow: ui.fields.Text;

    @ui.decorators.textField<WorkflowProcess>({
        parent() {
            return this.mainBlock;
        },
        isHidden: true,
        isReadOnly: true,
        title: 'Workflow definition ID',
        bind: { definition: { _id: true } },
    })
    workflowDefinitionId: ui.fields.Text;

    @ui.decorators.referenceField<WorkflowProcess>({
        parent() {
            return this.mainBlock;
        },
        title: 'Triggered by',
        isReadOnly: true,
        node: '@sage/xtrem-system/User',
        valueField: 'displayName',
    })
    triggeringUser: ui.fields.Reference;

    @ui.decorators.numericField<WorkflowProcess>({
        parent() {
            return this.mainBlock;
        },
        isReadOnly: true,
        title: 'Duration',
        bind: 'duration',
        postfix: 'seconds',
    })
    duration: ui.fields.Numeric;

    @ui.decorators.labelField<WorkflowProcess>({
        parent() {
            return this.mainBlock;
        },
        title: 'Status',
        backgroundColor: status => processStatusPillColor(status, 'backgroundColor'),
        borderColor: status => processStatusPillColor(status, 'borderColor'),
        color: status => processStatusPillColor(status, 'textColor'),
    })
    status: ui.fields.Label;

    @ui.decorators.dateTimeField<WorkflowProcess>({
        parent() {
            return this.mainBlock;
        },
        isReadOnly: true,
        isHidden: true,
        title: 'Start time',
    })
    startedAt: ui.fields.Datetime;

    @ui.decorators.dateTimeField<WorkflowProcess>({
        parent() {
            return this.mainBlock;
        },
        isReadOnly: true,
        isHidden: true,
        title: 'End time',
    })
    completedAt: ui.fields.Datetime;

    @ui.decorators.section<WorkflowProcess>({
        title: 'Diagram',
    })
    designerSection: ui.containers.Section;

    @ui.decorators.workflowField<WorkflowProcess>({
        parent() {
            return this.designerSection;
        },
        bind: { diagram: { data: true } },
        title: 'Workflow',
        isFullWidth: true,
        isReadOnly: true,
    })
    flow: ui.fields.Workflow;

    @ui.decorators.section<WorkflowProcess>({
        title: 'Log details',
    })
    eventLogsSection: ui.containers.Section;

    @ui.decorators.tableField<WorkflowProcess>({
        parent() {
            return this.eventLogsSection;
        },
        title: 'Event log',
        isReadOnly: true,
        canSelect: false,
        isTransient: true,
        fieldActions() {
            return [];
        },
        orderBy: { timestamp: 1 },

        columns: [
            ui.nestedFields.text({
                title: 'Step',
                bind: 'step',
                isReadOnly: true,
            }),
            ui.nestedFields.label({
                title: 'Status',
                bind: 'event',
                optionType: '@sage/xtrem-workflow/WorkflowProcessStatus',
                backgroundColor: status => logEventPillColor(status, 'backgroundColor'),
                borderColor: status => logEventPillColor(status, 'borderColor'),
                color: status => logEventPillColor(status, 'textColor'),
            }),
            ui.nestedFields.datetime({
                title: 'Time stamp',
                bind: 'timestamp',
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                title: 'Message',
                bind: 'message',
                isReadOnly: true,
            }),
        ],
    })
    tableLogs: ui.fields.Table;

    @ui.decorators.section<WorkflowProcess>({
        title: 'Variables',
    })
    variablesSection: ui.containers.Section;

    @ui.decorators.block<WorkflowProcess>({
        parent() {
            return this.variablesSection;
        },
    })
    variablesBlock: ui.containers.Block;

    @ui.decorators.textAreaField<WorkflowProcess>({
        parent() {
            return this.variablesBlock;
        },
        bind: 'variables',
        isReadOnly: true,
        isTransient: true,
        isFullWidth: true,
        rows: 12,
    })
    variablesDetail: ui.fields.TextArea;

    @ui.decorators.technicalJsonField<WorkflowProcess>({})
    eventLog: ui.fields.TechnicalJson;

    @ui.decorators.technicalJsonField<WorkflowProcess>({})
    variables: ui.fields.TechnicalJson;

    @ui.decorators.referenceField<WorkflowProcess>({
        node: '@sage/xtrem-workflow/WorkflowDiagram',
        isTransient: true,
        valueField: '_id',
    })
    diagram: ui.fields.Reference;

    private fillEventLog(): void {
        const entries = this.eventLog.value as WorkflowLogEntry[];
        const flow = this.flow.value;
        if (!flow) {
            return;
        }
        const nodes = flow.nodes;
        const locale = this.$.locale || 'base';
        const stepMap = new Map(nodes.map(node => [node.id, node]));
        this.tableLogs.value = entries.map((entry, i) => ({
            _id: (i + 1).toString(),
            ...entry,
            step: stepMap.get(entry.stepId)?.data.localizedTitle?.[locale] ?? entry.stepId,
        }));
    }

    private fillVariables(): void {
        const indent = (depth: number): string => ' '.repeat(8 * depth);
        // Quick and dirty yaml-like formatting
        const format = (value: any, depth = 0): string => {
            if (!value) return value;
            if (typeof value === 'string') return `'${value}'`;
            if (typeof value !== 'object') return String(value);
            if (Array.isArray(value)) return value.map(v => `\n${indent(depth)}- ${format(v, depth + 1)}`).join('\n');
            const properties = Object.entries(value).map(
                ([key, v]) => `${indent(depth)}${key}: ${format(v, depth + 1)}`,
            );
            return `\n${properties.join('\n')}`;
        };
        if (this.variables.value) {
            this.variablesDetail.value = format(this.variables.value).trim();
        }
    }
}
