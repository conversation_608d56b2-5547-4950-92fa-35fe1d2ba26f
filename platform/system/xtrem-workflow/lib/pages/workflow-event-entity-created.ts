import { G<PERSON><PERSON><PERSON><PERSON>, MetaNodeFactory } from '@sage/xtrem-metadata-api';
import { LocalizedText, WorkflowVariable, getTextForLocale, mergeLocalizedText } from '@sage/xtrem-shared';
import * as ui from '@sage/xtrem-ui';
import { WorkflowDefinition as WorkflowDefinitionApi } from '@sage/xtrem-workflow-api';
import { camelCase, pick } from 'lodash';
import {
    WorkflowConfigPageData,
    convertFilterEditorValueToWorkflowConditions,
    convertParametersAndConditionsToFieldEditorProperties,
    restoreNodeFieldFromString,
} from '../client-functions';
import { getSelectVariablesButtonDecorator } from '../client-functions/select-variables-button';
import {
    NodeFactoryData,
    convertVariableForSerialization,
    formatSelectedVariables,
    getNodeFactoryVariables,
    getVariablesSummaryDecorator,
    tidyVariables,
    transformVariablesFromSerialization,
} from '../client-functions/variable-utils';
import { WorkflowCondition } from '../shared-functions/workflow-config-types';
import { EntityCreatedEventConfig } from '../shared-functions/workflow-event-configs';

function getEntityName(nodeFactoryValue: { package?: { name?: string }; name?: string } | null): string {
    return nodeFactoryValue ? `${nodeFactoryValue.package?.name}/${nodeFactoryValue.name}` : '';
}

@ui.decorators.page<WorkflowEventEntityCreated, WorkflowDefinitionApi>({
    title: 'Trigger configuration',
    subtitle: 'Entity created',
    mode: 'tabs',
    businessActions() {
        if (this.$.queryParameters.isReadOnly) {
            return [this.$standardCancelAction];
        }
        return [this.$standardDialogConfirmationAction];
    },
    isDisabled() {
        // Only return true if isReadOnly exists and is true, otherwise return false
        return !!this.$.queryParameters.isReadOnly;
    },
    async onLoad() {
        return this._onLoad();
    },
})
export class WorkflowEventEntityCreated extends ui.Page<GraphApi> {
    @ui.decorators.section<WorkflowEventEntityCreated>({
        isTitleHidden: true,
        title: 'Configuration',
        isDisabled() {
            return !!this.$.queryParameters.isReadOnly;
        },
    })
    mainSection: ui.containers.Section;

    @ui.decorators.block<WorkflowEventEntityCreated>({
        parent() {
            return this.mainSection;
        },
    })
    mainBlock: ui.containers.Block;

    /** Only show in read-only mode */
    @ui.decorators.messageField<WorkflowEventEntityCreated>({
        parent() {
            return this.mainBlock;
        },
        variant: 'error',
        isTransient: true,
        isHidden() {
            if (this.$.queryParameters.isReadOnly && this.$.queryParameters.errorMessage) {
                return false;
            }
            return true;
        },
    })
    readOnlyErrorMessage: ui.fields.Message;

    /** Title field */
    @ui.decorators.textField<WorkflowEventEntityCreated>({
        title: 'Trigger title',
        helperText: 'Title displayed in the workflow diagram.',
        isFullWidth: true,
        isTransient: true,
        parent() {
            return this.mainBlock;
        },
    })
    title: ui.fields.Text;

    // Note: localizedTitle is a JSON object that contains the translations (something like { 'en-US': 'Hello', 'fr-FR': 'Bonjour' })
    // title is only editing the translation for the current locale
    @ui.decorators.technicalJsonField<WorkflowEventEntityCreated>({})
    localizedTitle: ui.fields.TechnicalJson<LocalizedText>;

    /** Bound field for the subtitle, coupled with the title field */
    @ui.decorators.textField<WorkflowEventEntityCreated>({})
    subtitle: ui.fields.Text;

    /** Bound field coupled with transient nodeFactory field */
    @ui.decorators.textField<WorkflowEventEntityCreated>({})
    entityName: ui.fields.Text;

    /** Transient field to select node factory */
    @ui.decorators.referenceField<WorkflowEventEntityCreated, MetaNodeFactory>({
        parent() {
            return this.mainBlock;
        },
        filter: {
            notifies: {
                _contains: 'created',
            },
            isAbstract: false,
        },
        orderBy: {
            name: 1,
        },
        columns: [
            ui.nestedFields.text({ bind: 'name', title: 'Record type', isHidden: true }),
            ui.nestedFields.text({ bind: 'title', title: 'Record type' }),
            ui.nestedFields.text({
                bind: { package: { name: true } },
                title: 'Package',
            }),
            ui.nestedFields.technical({ bind: 'naturalKey' }),
        ],
        isTransient: true,
        isMandatory: true,
        node: '@sage/xtrem-metadata/MetaNodeFactory',
        title: 'Record type',
        valueField: 'title',
        helperTextField: { package: { name: true } },
        minLookupCharacters: 0,
        isFullWidth: true,
        async onChange() {
            if (this.nodeFactory.value) {
                const entityName = getEntityName(this.nodeFactory.value);
                if (!this.title.value)
                    this.title.value = ui.localize(
                        '@sage/xtrem-workflow/workflow-entity-created-default-title',
                        '{{factoryName}} created',
                        { factoryName: this.nodeFactory.value.title },
                    );
                this.subtitle.value = '';
                this.entityName.value = entityName;
            } else {
                this.title.value = '';
                this.subtitle.value = '';
                this.entityName.value = '';
            }
            await this.selectFactoryVariables([]);
            this.conditions.value = [];
            this.fillFilters([]);
        },
    })
    nodeFactory: ui.fields.Reference<MetaNodeFactory>;

    @ui.decorators.section<WorkflowEventEntityCreated>({
        isTitleHidden: true,
        title: 'Condition',
        isDisabled() {
            return !!this.$.queryParameters.isReadOnly;
        },
    })
    conditionSection: ui.containers.Section;

    @ui.decorators.block<WorkflowEventEntityCreated>({
        parent() {
            return this.conditionSection;
        },
    })
    conditionBlock: ui.containers.Block;

    @ui.decorators.technicalJsonField<WorkflowEventEntityCreated>({})
    stepVariables: ui.fields.TechnicalJson<WorkflowVariable[]>;

    @ui.decorators.technicalJsonField<WorkflowEventEntityCreated>({})
    conditions: ui.fields.TechnicalJson<WorkflowCondition[]>;

    @ui.decorators.staticContentField<WorkflowEventEntityCreated>({
        ...getVariablesSummaryDecorator(),
        parent() {
            return this.conditionBlock;
        },
    })
    variablesSummary: ui.fields.StaticContent;

    @ui.decorators.buttonField<WorkflowEventEntityCreated>({
        ...getSelectVariablesButtonDecorator<WorkflowEventEntityCreated>({
            getSelectedVariables: page => page.currentVariables,
            setSelectedVariables: (page, variables) => page._updateCurrentVariables(variables),
            getOldRootPaths: () => [],
        }),
        parent() {
            return this.conditionBlock;
        },
    })
    selectVariablesButton: ui.fields.Button;

    /** Transient field to enter filters */
    @ui.decorators.filterEditorField<WorkflowEventEntityCreated>({
        parent() {
            return this.conditionBlock;
        },
        automaticColumnsSpacing: true,
        isTransient: true,
        isDisabled: true,
        isFullWidth: true,
        parameterMode: 'usage',
        title: 'Condition',
        isParentColumnHidden: true,
        helperText:
            'When all conditions are true the execution continues on the path below. If any of the conditions are evaluated to false, the execution continues to the right',
    })
    filters: ui.fields.FilterEditor;

    // Private fields
    private factoryVariables: WorkflowVariable[] = [];

    private currentVariables: WorkflowVariable[] = [];

    // Methods that fill the transient fields based on their associated bound fields.

    private async fillNodeFactory(): Promise<void> {
        await restoreNodeFieldFromString(this, this.nodeFactory, this.entityName.value);
    }

    async selectFactoryVariables(stepVariables: WorkflowVariable[]): Promise<void> {
        if (!this.nodeFactory.value) {
            this.factoryVariables = [];
            this.currentVariables = [];
            this.selectVariablesButton.isDisabled = true;
            this.variablesSummary.value = '';
        } else {
            const prefix = camelCase(this.nodeFactory.value.name ?? '');
            this.factoryVariables = await getNodeFactoryVariables({
                prefix,
                nodeFactory: this.nodeFactory.value as NodeFactoryData,
                locale: this.$.locale ?? 'en-US',
            });
            this.currentVariables = tidyVariables([...this.factoryVariables, ...stepVariables]);
            this.selectVariablesButton.isDisabled = false;
            this.variablesSummary.value = formatSelectedVariables(this.currentVariables);
        }
    }

    private _updateCurrentVariables(variables: WorkflowVariable[]): void {
        this.currentVariables = tidyVariables([...variables, ...this.stepVariables.value]);
        this.variablesSummary.value = formatSelectedVariables(this.currentVariables);
        // Update the filter parameters
        const filtersData = convertParametersAndConditionsToFieldEditorProperties({
            parameters: this.currentVariables,
            conditions: this.conditions.value ?? [],
        });
        Object.assign(this.filters, filtersData);
    }

    private fillFilters(conditions: WorkflowCondition[]): void {
        const filtersData = convertParametersAndConditionsToFieldEditorProperties({
            conditions,
            parameters: this.currentVariables ?? [],
        });
        Object.assign(this.filters, filtersData);
    }

    private async _onLoad(): Promise<void> {
        this.stepVariables.value = transformVariablesFromSerialization(this.stepVariables.value ?? []);
        await this.fillNodeFactory();
        await this.selectFactoryVariables(this.stepVariables.value ?? []);
        this._updateCurrentVariables([]);

        const conditions = this.conditions.value ?? [];
        this.fillFilters(conditions);

        const locale = this.$.locale;
        this.title.value = getTextForLocale(this.localizedTitle.value ?? {}, locale);
        if (this.$.queryParameters.isReadOnly && this.$.queryParameters.errorMessage) {
            this.readOnlyErrorMessage.value = this.$.queryParameters.errorMessage as string;
        }
    }

    // Methods that serialize the values of transient fields.

    private getConditions(): WorkflowCondition[] {
        return convertFilterEditorValueToWorkflowConditions(this.filters.value);
    }

    getSerializedValues(): WorkflowConfigPageData<EntityCreatedEventConfig> {
        const locale = this.$.locale;
        const nodeName = this.nodeFactory.value?.name ?? '<unknown>';
        const topic = `${nodeName}/created`;
        const conditions = this.getConditions();
        const localizedTitle = mergeLocalizedText(this.localizedTitle.value ?? {}, this.title.value ?? '', locale);

        return {
            ...pick(this.$.values, 'subtitle', 'entityName'),
            topic,
            conditions,
            stepVariables: this.currentVariables.map(convertVariableForSerialization),
            localizedTitle,
        };
    }
}
