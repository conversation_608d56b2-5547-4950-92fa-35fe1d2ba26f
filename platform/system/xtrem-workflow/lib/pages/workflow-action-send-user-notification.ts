import { getTextForLocale, IconType, LocalizedText, mergeLocalizedText, WorkflowVariable } from '@sage/xtrem-shared';
import { SysClientNotificationActionStyle } from '@sage/xtrem-system-api';
import * as ui from '@sage/xtrem-ui';
import { GraphApi, WorkflowDefinition as WorkflowDefinitionApi } from '@sage/xtrem-workflow-api';
import { omit, pick } from 'lodash';
import { getInputData, validateInputVariablesInField, WorkflowConfigPageData } from '../client-functions';
import {
    collectEmbeddedPaths,
    getDynamicSelectProperties,
    getNestedDynamicSelectProperties,
    getStepVariablesForSerialization,
    setOptionsForDynamicSelect,
    tidyVariables,
    transformVariablesFromSerialization,
    validateDynamicVariablesPickers,
} from '../client-functions/variable-utils';
import { iconNames } from '../shared-functions/icon-names';
import { SendUserNotificationActionRecipient } from '../shared-functions/send-user-notification-config';

@ui.decorators.page<WorkflowActionSendUserNotification, WorkflowDefinitionApi>({
    title: 'Action configuration',
    subtitle: 'Notify a user',
    mode: 'tabs',
    businessActions() {
        if (this.$.queryParameters.isReadOnly) {
            return [this.$standardCancelAction];
        }
        return [this.$standardDialogConfirmationAction];
    },
    async onLoad() {
        await this._onLoad();
    },
    isDisabled() {
        // Only return true if isReadOnly exists and is true, otherwise return false
        return !!this.$.queryParameters.isReadOnly;
    },
})
export class WorkflowActionSendUserNotification extends ui.Page<GraphApi> {
    @ui.decorators.section<WorkflowActionSendUserNotification>({
        isTitleHidden: true,
        title: 'General',
        isDisabled() {
            return !!this.$.queryParameters.isReadOnly;
        },
    })
    mainSection: ui.containers.Section;

    @ui.decorators.block<WorkflowActionSendUserNotification>({
        parent() {
            return this.mainSection;
        },
    })
    mainBlock: ui.containers.Block;

    /** Only show in read only mode */
    @ui.decorators.messageField<WorkflowActionSendUserNotification>({
        parent() {
            return this.mainBlock;
        },
        variant: 'error',
        isTransient: true,
        isHidden() {
            if (this.$.queryParameters.isReadOnly && this.$.queryParameters.errorMessage) {
                return false;
            }
            return true;
        },
    })
    readOnlyErrorMessage: ui.fields.Message;

    @ui.decorators.textField<WorkflowActionSendUserNotification>({
        title: 'Action title',
        helperText: 'Title displayed in the workflow diagram.',
        isFullWidth: true,
        isTransient: true,
        parent() {
            return this.mainBlock;
        },
    })
    title: ui.fields.Text;

    // Note: localizedTitle is a JSON object that contains the translations (something like { 'en-US': 'Hello', 'fr-FR': 'Bonjour' })
    // title is only editing the translation for the current locale
    @ui.decorators.technicalJsonField<WorkflowActionSendUserNotification>({})
    localizedTitle: ui.fields.TechnicalJson<LocalizedText>;

    @ui.decorators.textField<WorkflowActionSendUserNotification>({})
    subtitle: ui.fields.Text;

    @ui.decorators.technicalJsonField<WorkflowActionSendUserNotification>({})
    stepVariables: ui.fields.TechnicalJson<WorkflowVariable[]>;

    @ui.decorators.dynamicSelectField<WorkflowActionSendUserNotification>({
        parent() {
            return this.mainBlock;
        },
        isFullWidth: true,
        isTransient: true,
        isMandatory: true,
        title: 'Notification title',
        ...getDynamicSelectProperties<WorkflowActionSendUserNotification>({
            getVariables: page => page.currentVariables,
            selectableVariableFilter: () => true,
            getOldRootPaths: page => page.oldRootPaths,
            updateCurrentVariables: (page, variables) => page._updateCurrentVariables(variables),
        }),
    })
    notificationTitle: ui.fields.DynamicSelect;

    // Note: localizedNotificationTitle is a JSON object that contains the translations (something like { 'en-US': 'Hello', 'fr-FR': 'Bonjour' })
    // notificationTitle is only editing the translation for the current locale
    @ui.decorators.technicalJsonField<WorkflowActionSendUserNotification>({})
    localizedNotificationTitle: ui.fields.TechnicalJson<LocalizedText>;

    @ui.decorators.dynamicSelectField<WorkflowActionSendUserNotification>({
        title: 'Notification description',
        parent() {
            return this.mainBlock;
        },
        isFullWidth: true,
        isTransient: true,
        isMandatory: true,
        ...getDynamicSelectProperties<WorkflowActionSendUserNotification>({
            getVariables: page => page.currentVariables,
            selectableVariableFilter: () => true,
            getOldRootPaths: page => page.oldRootPaths,
            updateCurrentVariables: (page, variables) => page._updateCurrentVariables(variables),
        }),
    })
    notificationDescription: ui.fields.DynamicSelect;

    // Note: localizedNotificationDescription is a JSON object that contains the translations (something like { 'en-US': 'Hello', 'fr-FR': 'Bonjour' })
    // notificationDescription is only editing the translation for the current locale
    @ui.decorators.technicalJsonField<WorkflowActionSendUserNotification>({})
    localizedNotificationDescription: ui.fields.TechnicalJson<LocalizedText>;

    @ui.decorators.dropdownListField<WorkflowActionSendUserNotification>({
        parent() {
            return this.mainBlock;
        },
        title: 'Type',
        optionType: '@sage/xtrem-system/SysClientNotificationLevel',
    })
    level: ui.fields.DropdownList;

    @ui.decorators.dropdownListField<WorkflowActionSendUserNotification>({
        parent() {
            return this.mainBlock;
        },
        title: 'Icon',
        options: iconNames,
        onChange() {
            this.icon.icon = this.icon.value || undefined;
        },
    })
    icon: ui.fields.DropdownList<IconType>;

    @ui.decorators.checkboxField<WorkflowActionSendUserNotification>({
        parent() {
            return this.mainBlock;
        },
        title: 'Is urgent?',
        helperText: 'Urgent notifications are displayed as pop-up messages',
    })
    shouldDisplayToast: ui.fields.Checkbox;

    @ui.decorators.section<WorkflowActionSendUserNotification>({
        isTitleHidden: true,
        title: 'Recipients',
        isDisabled() {
            return !!this.$.queryParameters.isReadOnly;
        },
    })
    recipientsSection: ui.containers.Section;

    @ui.decorators.block<WorkflowActionSendUserNotification>({
        isTitleHidden: true,
        parent() {
            return this.recipientsSection;
        },
    })
    recipientsBlock: ui.containers.Block;

    @ui.decorators.podCollectionField<WorkflowActionSendUserNotification>({
        parent() {
            return this.recipientsSection;
        },
        title: 'Recipients',
        isTitleHidden: true,
        isFullWidth: true,
        recordWidth: 'large',
        addButtonText: 'Add a recipient',
        recordTitle(_, recordValue) {
            const index = this.recipients.value?.findIndex(s => s._id === recordValue?._id);
            if (index === -1) {
                return 'User';
            }
            return `User ${index + 1}`;
        },
        onRecordAdded(_id, data) {
            // It's more likely that the user will select a user from the list than to select a variable
            data.isManuallySet = true;
        },
        columns: [
            ui.nestedFields.dynamicSelect({
                title: 'User',
                bind: 'user',
                isMandatory: true,
                width: 'extra-large',
                ...getNestedDynamicSelectProperties<WorkflowActionSendUserNotification>({
                    mode: 'select',
                    getVariables: page => page.currentVariables,
                    selectableVariableFilter: (_page, variable) => variable.node === 'User',
                    getOldRootPaths: page => page.oldRootPaths,
                    updateCurrentVariables: (page, variables) => page._updateCurrentVariables(variables),
                }),
                validation(value: string, rowValue): string | undefined {
                    if (!(rowValue != null && !rowValue.isManuallySet)) {
                        // The field is hidden
                        return undefined;
                    }
                    return validateInputVariablesInField('select', this.currentVariables, value);
                },
                isHidden(_id, rowValue) {
                    return !(rowValue != null && !rowValue.isManuallySet);
                },
            }),
            ui.nestedFields.reference({
                title: 'User',
                bind: 'user',
                node: '@sage/xtrem-system/User',
                width: 'extra-large',
                placeholder: 'Select user',
                minLookupCharacters: 0,
                lookupDialogTitle: 'Select user',
                valueField: 'email',
                isMandatory: true,
                columns: [
                    ui.nestedFields.text({ title: 'Last name', bind: 'lastName' }),
                    ui.nestedFields.text({ title: 'First name', bind: 'firstName' }),
                    ui.nestedFields.text({ title: 'Email', bind: 'email' }),
                ],
                isHidden(_id, rowValue) {
                    return rowValue != null && !rowValue.isManuallySet;
                },
                isHelperTextHidden: true,
            }),
            ui.nestedFields.checkbox({
                bind: 'isManuallySet',
                title: 'Enter manually',
                width: 'small',
            }),
        ],
        canAddRecord: true,
        canRemoveRecord: true,
    })
    recipients: ui.fields.PodCollection<
        SendUserNotificationActionRecipient & {
            _id: string;
        }
    >;

    @ui.decorators.section<WorkflowActionSendUserNotification>({
        isTitleHidden: true,
        title: 'Actions',
        isDisabled() {
            return !!this.$.queryParameters.isReadOnly;
        },
    })
    actionsSection: ui.containers.Section;

    @ui.decorators.block<WorkflowActionSendUserNotification>({
        isTitleHidden: true,
        parent() {
            return this.actionsSection;
        },
    })
    actionsBlock: ui.containers.Block;

    @ui.decorators.podCollectionField<WorkflowActionSendUserNotification>({
        isFullWidth: true,
        canAddRecord: true,
        canRemoveRecord: true,
        recordWidth: 'large',
        isTransient: true,
        parent() {
            return this.actionsSection;
        },
        recordTitle(_, recordValue) {
            const index = this.actionsForLocale.value?.findIndex(s => s._id === recordValue?._id);
            if (index === -1) {
                return 'Action';
            }
            return `Action ${index + 1}`;
        },
        columns: [
            ui.nestedFields.dynamicSelect({
                bind: 'title',
                title: 'Title',
                isFullWidth: true,
                ...getNestedDynamicSelectProperties<WorkflowActionSendUserNotification>({
                    getVariables: page => page.currentVariables,
                    selectableVariableFilter: () => true,
                    getOldRootPaths: page => page.oldRootPaths,
                    updateCurrentVariables: (page, variables) => page._updateCurrentVariables(variables),
                }),
            }),
            ui.nestedFields.dynamicSelect({
                bind: 'link',
                title: 'Link',
                isFullWidth: true,
                ...getNestedDynamicSelectProperties<WorkflowActionSendUserNotification>({
                    getVariables: page => page.currentVariables,
                    selectableVariableFilter: () => true,
                    getOldRootPaths: page => page.oldRootPaths,
                    updateCurrentVariables: (page, variables) => page._updateCurrentVariables(variables),
                }),
            }),
            ui.nestedFields.dropdownList({
                bind: 'style',
                title: 'Button style',
                optionType: '@sage/xtrem-system/SysClientNotificationActionStyle',
                isFullWidth: true,
            }),
            ui.nestedFields.numeric({
                bind: '_originalIndex',
                title: 'Original index',
                isFullWidth: true,
                isHidden: true,
            }),
        ],
    })
    actionsForLocale: ui.fields.PodCollection<{
        _id: string;
        _originalIndex: number;
        title: string;
        link: string;
        style: SysClientNotificationActionStyle;
    }>;

    // Note: actions is a JSON object that contains the original actions from the db.
    // actions[xx].localizedTitle contains the all the translations (something like { 'en-US': 'Hello', 'fr-FR': 'Bonjour' })
    // actionsForLocale is editing a mapping of an action where actionsForLocale.title is the translation for the current locale of actions[xx].localizedTitle
    @ui.decorators.technicalJsonField<WorkflowActionSendUserNotification>({})
    actions: ui.fields.TechnicalJson<
        {
            _id: string;
            localizedTitle: LocalizedText;
            link: string;
            style: SysClientNotificationActionStyle;
        }[]
    >;

    // Private variables

    private currentVariables: WorkflowVariable[] = [];

    private oldRootPaths: string[];

    // Methods

    private async _onLoad(): Promise<void> {
        const { oldRootPaths } = getInputData(this);
        this.oldRootPaths = oldRootPaths;
        this.stepVariables.value = transformVariablesFromSerialization(this.stepVariables.value ?? []);
        this._updateCurrentVariables([]);

        this._setOptionsForDynamicSelects();

        const locale = this.$.locale;
        this.notificationTitle.value = getTextForLocale(this.localizedNotificationTitle.value ?? {}, locale);
        this.notificationDescription.value = getTextForLocale(
            this.localizedNotificationDescription.value ?? {},
            locale,
        );
        this.title.value = getTextForLocale(this.localizedTitle.value ?? {}, locale);

        this.actionsForLocale.value = this.actions.value?.map((action, idx) => {
            return {
                ...action,
                title: getTextForLocale(action.localizedTitle, locale) || '',
                // Store the original index to be able to reconcile the actions in getSerializedValues
                _originalIndex: idx,
            };
        });
        await this._validateFields();
        if (this.$.queryParameters.isReadOnly && this.$.queryParameters.errorMessage) {
            this.readOnlyErrorMessage.value = this.$.queryParameters.errorMessage as string;
        }
    }

    /**
     * Validate the fields of the page.
     */
    private async _validateFields(): Promise<void> {
        await validateDynamicVariablesPickers([
            this.actionsForLocale,
            this.recipients,
            this.notificationTitle,
            this.notificationDescription,
        ]);
    }

    /**
     * Set the options for the dynamic select fields.
     */
    private _setOptionsForDynamicSelects(): void {
        setOptionsForDynamicSelect(this.notificationTitle, this.currentVariables);
        setOptionsForDynamicSelect(this.notificationDescription, this.currentVariables);
    }

    private _updateCurrentVariables(variables: WorkflowVariable[]): void {
        const { inputVariables } = getInputData(this);
        this.currentVariables = tidyVariables([...inputVariables, ...this.stepVariables.value, ...variables]);
        this.recipients.value = [...(this.recipients.value ?? [])];
        this.actionsForLocale.value = [...(this.actionsForLocale.value ?? [])];
        this._setOptionsForDynamicSelects();
    }

    /**
     * Returns the paths of the variables used by the action.
     */
    private _getUsedVariablePaths(): string[] {
        const usedPaths = [] as string[];
        usedPaths.push(...collectEmbeddedPaths(this.notificationTitle.value ?? ''));
        usedPaths.push(...collectEmbeddedPaths(this.notificationDescription.value ?? ''));
        this.recipients.value?.forEach(recipient => {
            if (!recipient.isManuallySet && recipient.user && typeof recipient.user === 'string') {
                usedPaths.push(recipient.user);
            }
        });
        this.actionsForLocale.value?.forEach(action => {
            usedPaths.push(...collectEmbeddedPaths(action.title ?? ''));
            usedPaths.push(...collectEmbeddedPaths(action.link ?? ''));
        });
        return usedPaths;
    }

    getSerializedValues(): WorkflowConfigPageData<any> {
        const locale = this.$.locale;
        const recipients = this.recipients.value?.map(recipient => pick(recipient, 'user', 'isManuallySet')) ?? [];

        const actions = this.actionsForLocale.value.map(action => {
            if (action._originalIndex == null) {
                // This action was created during the session
                return { ...omit(action, ['_id']), localizedTitle: { [locale as string]: action.title } };
            }
            const originalAction = this.actions.value?.[action._originalIndex];
            return {
                ...omit(action, ['_id', '_originalIndex']),
                localizedTitle: mergeLocalizedText(originalAction.localizedTitle ?? {}, action.title ?? '', locale),
            };
        });

        const usedPaths = this._getUsedVariablePaths();
        const stepVariables = getStepVariablesForSerialization(this.currentVariables, usedPaths);

        const localizedNotificationTitle = mergeLocalizedText(
            this.localizedNotificationTitle.value ?? {},
            this.notificationTitle.value ?? '',
            locale,
        );
        const localizedNotificationDescription = mergeLocalizedText(
            this.localizedNotificationDescription.value ?? {},
            this.notificationDescription.value ?? '',
            locale,
        );
        const localizedTitle = mergeLocalizedText(this.localizedTitle.value ?? {}, this.title.value ?? '', locale);

        return {
            ...omit(this.$.values, ['actionsForLocale', 'notificationTitle', 'notificationDescription', 'title']),
            stepVariables,
            recipients,
            actions,
            localizedNotificationTitle,
            localizedNotificationDescription,
            localizedTitle,
        };
    }
}
