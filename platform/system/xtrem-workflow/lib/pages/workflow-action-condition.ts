import { Dict, LocalizedText, WorkflowVariable, getTextForLocale, mergeLocalizedText } from '@sage/xtrem-shared';
import * as ui from '@sage/xtrem-ui';
import { GraphApi, WorkflowDefinition as WorkflowDefinitionApi } from '@sage/xtrem-workflow-api';
import { pick } from 'lodash';
import {
    convertFilterEditorValueToWorkflowConditions,
    convertParametersAndConditionsToFieldEditorProperties,
    getInputData,
} from '../client-functions';
import { getSelectVariablesFilterDecorator } from '../client-functions/select-variables-filter';
import {
    convertPathForSerialization,
    convertPathFromSerialization,
    getStepVariablesForSerialization,
    tidyVariables,
    transformVariablesFromSerialization,
} from '../client-functions/variable-utils';
import { WorkflowCondition } from '../shared-functions/workflow-config-types';

@ui.decorators.page<WorkflowActionCondition, WorkflowDefinitionApi>({
    title: 'Condition configuration',
    businessActions() {
        if (this.$.queryParameters.isReadOnly) {
            return [this.$standardCancelAction];
        }
        return [this.$standardDialogConfirmationAction];
    },
    onLoad() {
        this._onLoad();
    },
})
export class WorkflowActionCondition extends ui.Page<GraphApi> {
    @ui.decorators.section<WorkflowActionCondition>({
        isTitleHidden: true,
        title: 'Basic Details',
        isDisabled() {
            return !!this.$.queryParameters.isReadOnly;
        },
    })
    mainSection: ui.containers.Section;

    @ui.decorators.block<WorkflowActionCondition>({
        parent() {
            return this.mainSection;
        },
    })
    mainBlock: ui.containers.Block;

    /** Only show in read only mode */
    @ui.decorators.messageField<WorkflowActionCondition>({
        parent() {
            return this.mainBlock;
        },
        variant: 'error',
        isTransient: true,
        isHidden() {
            if (this.$.queryParameters.isReadOnly && this.$.queryParameters.errorMessage) {
                return false;
            }
            return true;
        },
    })
    readOnlyErrorMessage: ui.fields.Message;

    @ui.decorators.textField<WorkflowActionCondition>({
        title: 'Condition title',
        helperText: 'Title displayed in the workflow diagram.',
        isFullWidth: true,
        isTransient: true,
        parent() {
            return this.mainBlock;
        },
    })
    title: ui.fields.Text;

    @ui.decorators.block<WorkflowActionCondition>({
        parent() {
            return this.mainSection;
        },
        title: 'Setup branches',
    })
    branchesBlock: ui.containers.Block;

    @ui.decorators.checkboxField<WorkflowActionCondition>({
        parent() {
            return this.branchesBlock;
        },
        title: 'Add "if true" branch',
        noPadding: true,
    })
    ifTrueBranch: ui.fields.Checkbox;

    @ui.decorators.checkboxField<WorkflowActionCondition>({
        parent() {
            return this.branchesBlock;
        },
        title: 'Add "if false" branch',
        noPadding: true,
    })
    ifFalseBranch: ui.fields.Checkbox;

    // Note: localizedTitle is a JSON object that contains the translations (something like { 'en-US': 'Hello', 'fr-FR': 'Bonjour' })
    // title is only editing the translation for the current locale
    @ui.decorators.technicalJsonField<WorkflowActionCondition>({})
    localizedTitle: ui.fields.TechnicalJson<LocalizedText>;

    @ui.decorators.textField<WorkflowActionCondition>({})
    subtitle: ui.fields.Text;

    @ui.decorators.technicalJsonField<WorkflowActionCondition>({})
    conditions: ui.fields.TechnicalJson<WorkflowCondition[]>;

    @ui.decorators.technicalJsonField<WorkflowActionCondition>({})
    stepVariables: ui.fields.TechnicalJson<WorkflowVariable[]>;

    @ui.decorators.block<WorkflowActionCondition>({
        parent() {
            return this.mainSection;
        },
        title: 'Condition definition',
    })
    variablesBlock: ui.containers.Block;

    @ui.decorators.filterEditorField<WorkflowActionCondition>({
        ...getSelectVariablesFilterDecorator<WorkflowActionCondition>({
            getSelectedVariables: page => page.currentVariables,
            setSelectedVariables: (page, variables) => page._updateCurrentVariables(variables),
            getOldRootPaths: page => page.oldRootPaths,
        }),
        parent() {
            return this.variablesBlock;
        },
        automaticColumnsSpacing: true,
        isTransient: true,
        isFullWidth: true,
        mode: 'pod',
        parameterMode: 'usage',
        title: 'Condition',
        isParentColumnHidden: true,
        helperText:
            'When all conditions are true the execution continues to the left. If any of the conditions are evaluated to false, the execution continues to the right',
        onChange() {
            this.conditions.value = convertFilterEditorValueToWorkflowConditions(this.filters.value);
        },
    })
    filters: ui.fields.FilterEditor;

    // Private variables
    private currentVariables: WorkflowVariable[];

    private oldRootPaths: string[];

    private _updateCurrentVariables(variables: WorkflowVariable[]): void {
        const { inputVariables } = getInputData(this);
        this.currentVariables = tidyVariables([...inputVariables, ...this.stepVariables.value, ...variables]);

        // Update the filter parameters
        const filtersData = convertParametersAndConditionsToFieldEditorProperties({
            parameters: this.currentVariables,
            conditions: this.conditions.value ?? [],
        });
        Object.assign(this.filters, filtersData);
    }

    private _onLoad(): void {
        const { oldRootPaths } = getInputData(this);
        this.oldRootPaths = oldRootPaths;
        this.stepVariables.value = transformVariablesFromSerialization(this.stepVariables.value ?? []);
        this._updateCurrentVariables([]);

        this.conditions.value = (this.conditions.value ?? []).map(condition => ({
            ...condition,
            path: convertPathFromSerialization(condition.path),
        }));

        const filtersData = convertParametersAndConditionsToFieldEditorProperties({
            conditions: this.conditions.value,
            parameters: this.currentVariables,
        });
        Object.assign(this.filters, filtersData);
        const locale = this.$.locale;
        this.title.value = getTextForLocale(this.localizedTitle.value ?? {}, locale);
        if (this.$.queryParameters.isReadOnly && this.$.queryParameters.errorMessage) {
            this.readOnlyErrorMessage.value = this.$.queryParameters.errorMessage as string;
        }
    }

    private getUsedVariablePaths(): string[] {
        const usedPaths = [] as string[];
        this.conditions.value?.forEach(condition => {
            usedPaths.push(condition.path);
            if (condition.useParameter) {
                usedPaths.push(condition.value as string);
            }
        });
        return usedPaths;
    }

    getSerializedValues(): Dict<any> {
        const usedPaths = this.getUsedVariablePaths();
        const stepVariables = getStepVariablesForSerialization(this.currentVariables, usedPaths);
        const locale = this.$.locale;
        const localizedTitle = mergeLocalizedText(this.localizedTitle.value ?? {}, this.title.value ?? '', locale);
        const conditions = (this.conditions.value ?? []).map(condition => ({
            ...condition,
            path: convertPathForSerialization(condition.path),
        }));

        return {
            ...pick(this.$.values, 'subtitle', 'ifTrueBranch', 'ifFalseBranch'),
            stepVariables,
            localizedTitle,
            conditions,
        };
    }
}
