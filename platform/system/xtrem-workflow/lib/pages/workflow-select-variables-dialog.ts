import { Graph<PERSON>pi } from '@sage/xtrem-metadata-api';
import { variableFilters, WorkflowVariable } from '@sage/xtrem-shared';
import * as ui from '@sage/xtrem-ui';
import { mapNodeDetailsToTreeProperty } from '@sage/xtrem-ui';
import { NodeDetails } from '@sage/xtrem-ui/build/lib/service/node-information-service';
import { WorkflowDefinition as WorkflowDefinitionApi } from '@sage/xtrem-workflow-api';
import {
    convertNodeBrowserTreeValueToVariables,
    convertVariablesToNodeBrowserTreeValue,
    NodeDetailsFetcher,
} from '../client-functions';
import {
    getOldRelativePropertyPaths,
    getOldVariables,
    stripIdIconFromTitle,
    tidyVariables,
} from '../client-functions/variable-utils';

@ui.decorators.page<WorkflowSelectVariablesDialog, WorkflowDefinitionApi>({
    title: '',
    mode: 'tabs',
    isTransient: true,
    async onLoad() {
        await this.onLoad();
    },
    businessActions() {
        if (this.$.queryParameters.isReadOnly) {
            return [this.$standardCancelAction];
        }
        return [this.ok];
    },
})
export class WorkflowSelectVariablesDialog extends ui.Page<GraphApi> {
    @ui.decorators.section<WorkflowSelectVariablesDialog>({
        isTitleHidden: false,
        title() {
            if (this._selectionMode === 'propertiesToUpdate')
                return ui.localize('@sage/xtrem-workflow/add-properties', 'Select properties to update');
            return ui.localize('@sage/xtrem-workflow/add-variables', 'Add variables');
        },
        isDisabled() {
            return !!this.$.queryParameters.isReadOnly;
        },
    })
    mainSection: ui.containers.Section;

    @ui.decorators.block<WorkflowSelectVariablesDialog>({
        parent() {
            return this.mainSection;
        },
    })
    mainBlock: ui.containers.Block;

    /** Only show in read only mode */
    @ui.decorators.messageField<WorkflowSelectVariablesDialog>({
        parent() {
            return this.mainBlock;
        },
        variant: 'error',
        isTransient: true,
        isHidden() {
            if (this.$.queryParameters.isReadOnly && this.$.queryParameters.errorMessage) {
                return false;
            }
            return true;
        },
    })
    readOnlyErrorMessage: ui.fields.Message;

    @ui.decorators.dropdownListField<WorkflowSelectVariablesDialog>({
        title: 'Select from ...',
        isHelperTextHidden: true,
        parent() {
            return this.mainBlock;
        },
        options: [],
        map(path) {
            const variable = this.fromVariables.find(v => v.path === path);
            if (variable == null) throw new Error(`invalid 'from' variable: path=${path}`);
            return stripIdIconFromTitle(variable.title);
        },
        async onChange() {
            await this.fillSelectedProperties();
        },
        isReadOnly() {
            return this._selectionMode === 'propertiesToUpdate';
        },
    })
    fromPath: ui.fields.DropdownList;

    /** Transient field to select node properties */
    @ui.decorators.nodeBrowserTreeField<WorkflowSelectVariablesDialog>({
        parent() {
            return this.mainBlock;
        },
        isTitleHidden: true,
        isFullWidth: true,

        helperText: 'Only the selected properties are requested from the database.',
        async fetchItems(parentProperty) {
            const nodeName = parentProperty.data.type;
            const propertiesDict = await this._nodeDetailsFetcher.fetch(nodeName);
            const filteredProperties = Object.values(propertiesDict)
                .map(property => {
                    if (this._restrictedPropertyNames != null) {
                        if (!this._restrictedPropertyNames.includes(property.name)) {
                            // Filter out properties that are not in the list of restricted properties
                            return null;
                        }
                    }
                    if (property.type === '_OutputTextStream') {
                        // Streams can't be selected
                        return null;
                    }

                    if (this._options.onlyShowIdsAndReferences) {
                        if (property.name !== '_id' && property.kind !== 'OBJECT') {
                            // Only show _ids and references
                            return null;
                        }
                    }

                    if (this._selectionMode === 'propertiesToUpdate') {
                        // Note: we always keep the custom fields
                        if (!property.isCustom) {
                            if (!property.isOnInputType) {
                                // Filter out non-writable properties
                                return null;
                            }

                            if (property.name.startsWith('_')) {
                                // Filter out system properties
                                return null;
                            }
                        }

                        if (property.kind === 'OBJECT')
                            // References are not expandable when we select the properties to update
                            // replace reference to OBJECT by SCALAR so that we can select directly
                            return {
                                ...property,
                                kind: 'SCALAR',
                                type: 'IntReference',
                            } as NodeDetails;
                    }
                    return {
                        ...property,
                        label: property.isCustom ? `${property.label} (custom)` : property.label,
                    };
                })
                .filter(property => property != null);
            return mapNodeDetailsToTreeProperty(parentProperty, filteredProperties);
        },
        async onChange() {
            // Refill the filters field with the current conditions
            // This will update the filter's parameters and selected properties,
            // and remove the filters that are not in the selected properties
            // Question: maybe we should prompt the user to confirm the removal of filters?
            const relativeVariables = await convertNodeBrowserTreeValueToVariables(
                this._nodeDetailsFetcher,
                this.selectedProperties.value,
                {
                    entityName: this.rootEntityName,
                },
            );
            if (this._selectionMode === 'variables' && !relativeVariables.find(v => v.path === '_id')) {
                // Find a better way to prevent deselection of _id
                throw new Error(
                    ui.localize(
                        '@sage/xtrem-workflow/workflow-error-cannot-unselect-root-property',
                        'Cannot unselect root _id property',
                    ),
                );
            }
            // Keep the variables that are not under this.rootPrefix
            const previousVariables = this.selectedVariables.filter(
                variable => !variable.path.startsWith(this.rootPrefix),
            );
            // Get the new list of variables under this.rootPrefix
            const newVariables = relativeVariables.map(variable => ({
                ...variable,
                path: `${this.rootPrefix}${variable.path}`,
                title: variable.title ? `${this.rootTitle} / ${variable.title}` : this.rootTitle,
            }));
            this.selectedVariables = [...previousVariables, ...newVariables];
        },
    })
    selectedProperties: ui.fields.NodeBrowserTree;

    @ui.decorators.pageAction<WorkflowSelectVariablesDialog>({
        async onClick() {
            const selectedVariables = tidyVariables([
                ...this.selectedVariables,
                ...getOldVariables(this.selectedVariables, this.rootPrefix, this.oldRelativePropertyPaths),
            ]);
            const selectedRootVariable = this.fromVariables.find(v => v.path === this.fromPath.value);
            this.$.finish({
                selectedVariables,
                selectedRootVariable,
            });
        },
        title: 'OK',
    })
    ok: ui.PageAction;

    // Private fields

    private rootEntityName = '';

    private oldRelativePropertyPaths: string[] = [];

    private rootPrefix = '';

    private rootTitle = '';

    private selectedVariables: WorkflowVariable[] = [];

    /**
     * The from variables that can be used in the 'Select from'
     */
    private fromVariables: WorkflowVariable[] = [];

    /**
     * The name of the properties that can be proposed (or undefined if all the properties are allowed)
     * This list is only used when the selection mode is 'propertiesToUpdate'
     */
    private _restrictedPropertyNames: string[] | undefined;

    private oldRootPaths: string[];

    private _nodeDetailsFetcher = new NodeDetailsFetcher();

    /**
     * Whether the selection is for properties or for variables
     */
    private _selectionMode: 'variables' | 'propertiesToUpdate';

    private _options: {
        /** Only show _ids and references */
        onlyShowIdsAndReferences?: true;
    } = {};

    // Methods

    private async fillOldPropertyNames(): Promise<void> {
        const fromVariable = this.fromVariables.find(v => v.path === this.fromPath.value);
        if (!fromVariable?.node) throw new Error(`invalid from variable: path=${this.fromPath.value}`);
        this.oldRelativePropertyPaths = this.oldRootPaths.includes(fromVariable.path)
            ? await getOldRelativePropertyPaths(this._nodeDetailsFetcher, fromVariable.node)
            : [];
    }

    async fillSelectedProperties(): Promise<void> {
        const fromVariable = this.fromVariables.find(v => v.path === this.fromPath.value);
        if (fromVariable == null) return;
        this.rootPrefix = fromVariable.path.replace(/\._id$/, '.');
        this.rootTitle = stripIdIconFromTitle(fromVariable.title);
        const packageName = await ui.fetchNodePackageName(fromVariable.node ?? '');
        this.rootEntityName = `${packageName}/${fromVariable.node}`;
        await this.fillOldPropertyNames();
        this.selectedProperties.node = this.rootEntityName;

        const relativeVariables = this.selectedVariables
            .filter(v => v.path.startsWith(this.rootPrefix))
            .map(v => ({
                ...v,
                path: v.path.slice(this.rootPrefix.length),
            }));
        this.selectedProperties.value = convertVariablesToNodeBrowserTreeValue(relativeVariables);
    }

    async onLoad(): Promise<void> {
        const {
            fromVariables: fromVariablesAsString,
            inputVariables,
            restrictedVariables,
            oldRootPaths,
            selectionMode,
            options,
        } = this.$.queryParameters;
        this.selectedVariables = JSON.parse(inputVariables as string);
        const { isReference, and, not, isOld } = variableFilters;
        this._selectionMode = selectionMode as 'variables' | 'propertiesToUpdate';
        this._options = JSON.parse((options as string) ?? '{}');
        this.fromVariables =
            this._selectionMode === 'propertiesToUpdate'
                ? // use the fromVariables provided by the caller (there can be only one)
                  JSON.parse(fromVariablesAsString as string)
                : // only keep, from the selectedVariables, the references that are not old
                  this.selectedVariables.filter(and(isReference(), not(isOld)));
        this.fromPath.options = this.fromVariables.map(v => v.path);
        this.oldRootPaths = JSON.parse(oldRootPaths as string);
        if (this.fromVariables.length === 1) {
            // Preselect the only root available
            this.fromPath.value = this.fromVariables[0].path;
            await this.fillSelectedProperties();
        }
        this._restrictedPropertyNames = undefined;
        if (this._selectionMode === 'propertiesToUpdate') {
            const fromVariables = this.fromVariables;
            if (fromVariables == null || fromVariables.length !== 1) {
                throw new Error(
                    `Invalid from variables in 'propertiesToUpdate' selection mode: length=${this.fromVariables.length}, expected=1 `,
                );
            }
            if (restrictedVariables != null) {
                const vars = JSON.parse(restrictedVariables as string) as WorkflowVariable[];
                // When selectionMode is 'propertiesToUpdate', there is 1 and only 1 possible fromVariable
                const rootPath = fromVariables[0].path;
                if (!rootPath.endsWith('._id')) {
                    throw new Error(`Invalid fromVariable: path=${rootPath}`);
                }
                const rootPathPrefix = rootPath.slice(0, -3); // Remove the '._id' suffix
                this._restrictedPropertyNames = vars.map(v => {
                    // In the selectionMode 'propertiesToUpdate', all the restrictedVariables must be under the same root (under fromVariables[0])
                    if (!v.path.startsWith(rootPathPrefix)) {
                        throw new Error(`Invalid variable: path=${v.path}, expected to start with ${rootPathPrefix}`);
                    }
                    return v.path.slice(rootPathPrefix.length);
                });
            }
        }
        if (this.$.queryParameters.isReadOnly && this.$.queryParameters.errorMessage) {
            this.readOnlyErrorMessage.value = this.$.queryParameters.errorMessage as string;
        }
    }
}
