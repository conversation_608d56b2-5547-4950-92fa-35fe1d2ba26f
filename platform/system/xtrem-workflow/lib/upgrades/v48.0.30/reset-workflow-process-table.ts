import { CustomSqlAction } from '@sage/xtrem-system';

export const resetWorkflowProcessTable = new CustomSqlAction({
    description: 'Resets the workflow_process table',
    fixes: {
        tables: ['workflow_process'],
        notNullableColumns: [{ table: 'workflow_process', column: 'execution_user' }],
    },
    body: async helper => {
        await helper.executeSql(`DELETE FROM ${helper.schemaName}.workflow_process;`);
    },
});
