import { CustomSqlAction } from '@sage/xtrem-system';

export const fixNullColumnValues = [
    new CustomSqlAction({
        description: 'Fix null values in workflow_definition.draft_user',
        fixes: { notNullableColumns: [{ table: 'workflow_definition', column: 'draft_user' }] },
        body: async helper => {
            await helper.executeSql(
                `UPDATE ${helper.schemaName}.workflow_definition SET draft_user = _create_user`,
                [],
            );
        },
    }),
    new CustomSqlAction({
        description: 'Fix null values in workflow_process.triggering_user',
        fixes: { notNullableColumns: [{ table: 'workflow_process', column: 'triggering_user' }] },
        body: async helper => {
            await helper.executeSql(
                `UPDATE ${helper.schemaName}.workflow_process SET triggering_user = _create_user`,
                [],
            );
        },
    }),
];
