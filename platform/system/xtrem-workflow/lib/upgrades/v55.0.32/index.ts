import { SchemaEnumUpgradeAction, SchemaRenamePropertyAction, UpgradeSuite } from '@sage/xtrem-system';
import { WorkflowDefinitionStatusDataType } from '../../enums/workflow-definition-status';
import { WorkflowDefinition } from '../../nodes/workflow-definition';

export const upgradeSuite = new UpgradeSuite({
    actions: [
        new SchemaRenamePropertyAction({
            node: () => WorkflowDefinition,
            oldPropertyName: 'draftUser',
            newPropertyName: 'testUser',
        }),
        new SchemaEnumUpgradeAction({
            description: 'WorkflowDefinitionEnum: draft -> test',
            dataType: WorkflowDefinitionStatusDataType,
            valuesMapping: {
                test: 'draft',
                production: 'production',
            },
            membersToDelete: {
                draft: 'test',
            },
        }),
    ],
});
