{"@sage/xtrem-workflow/activity__workflow_definition__name": "Definición de flujo de trabajo", "@sage/xtrem-workflow/activity__workflow_process__name": "Proceso de flujo de trabajo", "@sage/xtrem-workflow/add-properties": "<PERSON><PERSON><PERSON><PERSON><PERSON> propiedades por actualizar", "@sage/xtrem-workflow/add-variables": "<PERSON><PERSON>dir variables", "@sage/xtrem-workflow/data_types__test_decimal_data_type__name": "Test decimal data type", "@sage/xtrem-workflow/data_types__test_enum_enum__name": "Test enum enum", "@sage/xtrem-workflow/data_types__test_string_data_type__name": "Test string data type", "@sage/xtrem-workflow/data_types__workflow_definition_status_enum__name": "Workflow definition status enum", "@sage/xtrem-workflow/data_types__workflow_mutation_argument_origin_enum__name": "Workflow mutation argument origin enum", "@sage/xtrem-workflow/data_types__workflow_mutation_parameter_type_enum__name": "Workflow mutation parameter type enum", "@sage/xtrem-workflow/data_types__workflow_process_status_enum__name": "Workflow process status enum", "@sage/xtrem-workflow/enums__test_enum__value1": "Valor 1", "@sage/xtrem-workflow/enums__test_enum__value2": "Valor 2", "@sage/xtrem-workflow/enums__test_enum__value3": "Valor 3", "@sage/xtrem-workflow/enums__workflow_definition_status__production": "Producción", "@sage/xtrem-workflow/enums__workflow_definition_status__test": "Prueba", "@sage/xtrem-workflow/enums__workflow_mutation_argument_origin__fromParameter": "Desde pará<PERSON>ro", "@sage/xtrem-workflow/enums__workflow_mutation_argument_origin__fromVariable": "Desde variable", "@sage/xtrem-workflow/enums__workflow_mutation_argument_origin__manual": "Manual", "@sage/xtrem-workflow/enums__workflow_mutation_parameter_type__boolean": "<PERSON><PERSON><PERSON>", "@sage/xtrem-workflow/enums__workflow_mutation_parameter_type__date": "<PERSON><PERSON>", "@sage/xtrem-workflow/enums__workflow_mutation_parameter_type__decimal": "Decimal", "@sage/xtrem-workflow/enums__workflow_mutation_parameter_type__enum": "Enumeración", "@sage/xtrem-workflow/enums__workflow_mutation_parameter_type__integer": "Número entero", "@sage/xtrem-workflow/enums__workflow_mutation_parameter_type__other": "<PERSON><PERSON><PERSON>", "@sage/xtrem-workflow/enums__workflow_mutation_parameter_type__reference": "Referencia", "@sage/xtrem-workflow/enums__workflow_mutation_parameter_type__string": "Cadena", "@sage/xtrem-workflow/enums__workflow_process_status__cancelled": "Cancelado", "@sage/xtrem-workflow/enums__workflow_process_status__error": "Error", "@sage/xtrem-workflow/enums__workflow_process_status__running": "En curso", "@sage/xtrem-workflow/enums__workflow_process_status__shutDown": "Interrumpido", "@sage/xtrem-workflow/enums__workflow_process_status__skipped": "Omitido", "@sage/xtrem-workflow/enums__workflow_process_status__success": "Finalizado", "@sage/xtrem-workflow/enums__workflow_process_status__suspended": "Suspendido", "@sage/xtrem-workflow/error-no-test-user-in-test-mode": "El usuario de prueba es obligatorio en los flujos de prueba.", "@sage/xtrem-workflow/invalid-workflow-is-disabled": "", "@sage/xtrem-workflow/menu_item__automation": "Automatización", "@sage/xtrem-workflow/menu_item__designer": "Diseño", "@sage/xtrem-workflow/menu_item__process": "Proceso", "@sage/xtrem-workflow/nodes__test_config_locale_error_message": "Mensaje de error localizado", "@sage/xtrem-workflow/nodes__workflow_definition__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-workflow/nodes__workflow_definition__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-workflow/nodes__workflow_definition__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-workflow/nodes__workflow_definition__node_name": "Definición de flujo de trabajo", "@sage/xtrem-workflow/nodes__workflow_definition__property__diagram": "Diagrama", "@sage/xtrem-workflow/nodes__workflow_definition__property__flow": "F<PERSON>jo", "@sage/xtrem-workflow/nodes__workflow_definition__property__id": "Id.", "@sage/xtrem-workflow/nodes__workflow_definition__property__isActive": "Activo", "@sage/xtrem-workflow/nodes__workflow_definition__property__name": "Nombre", "@sage/xtrem-workflow/nodes__workflow_definition__property__processes": "Procesos", "@sage/xtrem-workflow/nodes__workflow_definition__property__startTopic": "Tema de inicio", "@sage/xtrem-workflow/nodes__workflow_definition__property__status": "Estado", "@sage/xtrem-workflow/nodes__workflow_definition__property__testUser": "Usuario de prueba", "@sage/xtrem-workflow/nodes__workflow_diagram__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-workflow/nodes__workflow_diagram__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-workflow/nodes__workflow_diagram__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-workflow/nodes__workflow_diagram__mutation__controlDiagram": "Controlar diagrama", "@sage/xtrem-workflow/nodes__workflow_diagram__mutation__controlDiagram__failed": "Error al controlar el diagrama", "@sage/xtrem-workflow/nodes__workflow_diagram__mutation__controlDiagram__parameter__edges": "<PERSON><PERSON>", "@sage/xtrem-workflow/nodes__workflow_diagram__mutation__controlDiagram__parameter__nodes": "Nodos", "@sage/xtrem-workflow/nodes__workflow_diagram__node_name": "Diagrama de flujo de trabajo", "@sage/xtrem-workflow/nodes__workflow_diagram__property__data": "Datos", "@sage/xtrem-workflow/nodes__workflow_diagram__property__version": "Versión", "@sage/xtrem-workflow/nodes__workflow_process__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-workflow/nodes__workflow_process__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-workflow/nodes__workflow_process__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-workflow/nodes__workflow_process__node_name": "Proceso de flujo de trabajo", "@sage/xtrem-workflow/nodes__workflow_process__property__completedAt": "Fin", "@sage/xtrem-workflow/nodes__workflow_process__property__containerId": "<PERSON>d. de contenedor", "@sage/xtrem-workflow/nodes__workflow_process__property__definition": "Definición", "@sage/xtrem-workflow/nodes__workflow_process__property__diagram": "Diagrama", "@sage/xtrem-workflow/nodes__workflow_process__property__duration": "Duración", "@sage/xtrem-workflow/nodes__workflow_process__property__errorMessages": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-workflow/nodes__workflow_process__property__eventLog": "Traza de desencadenante", "@sage/xtrem-workflow/nodes__workflow_process__property__id": "Id.", "@sage/xtrem-workflow/nodes__workflow_process__property__originId": "Id. de origen", "@sage/xtrem-workflow/nodes__workflow_process__property__startedAt": "<PERSON><PERSON>o", "@sage/xtrem-workflow/nodes__workflow_process__property__startNotificationId": "Id. de notificación de inicio", "@sage/xtrem-workflow/nodes__workflow_process__property__state": "Estado", "@sage/xtrem-workflow/nodes__workflow_process__property__status": "Estado", "@sage/xtrem-workflow/nodes__workflow_process__property__triggeringUser": "Usuario de desencadenante", "@sage/xtrem-workflow/nodes__workflow_process__property__variables": "Variables", "@sage/xtrem-workflow/nodes__workflow_step_template__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-workflow/nodes__workflow_step_template__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-workflow/nodes__workflow_step_template__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-workflow/nodes__workflow_step_template__mutation__registerStepConfiguration": "Registrar configuración de paso", "@sage/xtrem-workflow/nodes__workflow_step_template__mutation__registerStepConfiguration__failed": "Error al registrar la configuración del paso", "@sage/xtrem-workflow/nodes__workflow_step_template__mutation__registerStepConfiguration__parameter__asSetupNode": "Nodo de configuración", "@sage/xtrem-workflow/nodes__workflow_step_template__mutation__registerStepConfiguration__parameter__configData": "Datos de configuración", "@sage/xtrem-workflow/nodes__workflow_step_template__mutation__registerStepConfiguration__parameter__factory": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-workflow/nodes__workflow_step_template__mutation__registerStepConfiguration__parameter__localizedDescription": "Descripción localizada", "@sage/xtrem-workflow/nodes__workflow_step_template__mutation__registerStepConfiguration__parameter__localizedTitle": "Título localizado", "@sage/xtrem-workflow/nodes__workflow_step_template__mutation__registerStepConfiguration__parameter__stepConstructor": "Const<PERSON>ctor de pasos", "@sage/xtrem-workflow/nodes__workflow_step_template__node_name": "Plantilla de paso de flujo de trabajo", "@sage/xtrem-workflow/nodes__workflow_step_template__property__color": "Color", "@sage/xtrem-workflow/nodes__workflow_step_template__property__configData": "Datos de configuración", "@sage/xtrem-workflow/nodes__workflow_step_template__property__description": "Descripción", "@sage/xtrem-workflow/nodes__workflow_step_template__property__factory": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-workflow/nodes__workflow_step_template__property__icon": "Icono", "@sage/xtrem-workflow/nodes__workflow_step_template__property__isActive": "Activa", "@sage/xtrem-workflow/nodes__workflow_step_template__property__serviceOptions": "Opciones de servicio", "@sage/xtrem-workflow/nodes__workflow_step_template__property__stepConstructor": "Const<PERSON>ctor de pasos", "@sage/xtrem-workflow/nodes__workflow_step_template__property__stepDescriptor": "Descriptor de pasos", "@sage/xtrem-workflow/nodes__workflow_step_template__property__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-workflow/nodes__workflow_step_template__property__variant": "<PERSON><PERSON><PERSON>", "@sage/xtrem-workflow/package__name": "Flujo de trabajo", "@sage/xtrem-workflow/pages__workflow_action_calculate____subtitle": "Calcular", "@sage/xtrem-workflow/pages__workflow_action_calculate____title": "Configuración de acción", "@sage/xtrem-workflow/pages__workflow_action_calculate__calculationSteps____addButtonText": "<PERSON><PERSON><PERSON> paso de <PERSON>", "@sage/xtrem-workflow/pages__workflow_action_calculate__calculationSteps____columns__title__isVariable": "Variable", "@sage/xtrem-workflow/pages__workflow_action_calculate__calculationSteps____columns__title__operation": "Operación", "@sage/xtrem-workflow/pages__workflow_action_calculate__calculationSteps____columns__title__value": "Valor de componente", "@sage/xtrem-workflow/pages__workflow_action_calculate__calculationSteps____columns__title__variable": "Variable de componente", "@sage/xtrem-workflow/pages__workflow_action_calculate__calculationSteps____title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-workflow/pages__workflow_action_calculate__mainSection____title": "Datos básicos", "@sage/xtrem-workflow/pages__workflow_action_calculate__outputVariableName____title": "Nombre de variable de salida", "@sage/xtrem-workflow/pages__workflow_action_calculate__title____helperText": "Título en el diagrama del flujo de trabajo", "@sage/xtrem-workflow/pages__workflow_action_calculate__title____title": "Título de acción", "@sage/xtrem-workflow/pages__workflow_action_condition____title": "Configuración de condición", "@sage/xtrem-workflow/pages__workflow_action_condition__branchesBlock____title": "Ramas de configuración", "@sage/xtrem-workflow/pages__workflow_action_condition__filters____helperText": "Si todas las condiciones son verdaderas, la ejecución continúa hacia la izquierda. Si hay alguna condición falsa, la ejecución continúa hacia la derecha.", "@sage/xtrem-workflow/pages__workflow_action_condition__filters____title": "Condición", "@sage/xtrem-workflow/pages__workflow_action_condition__ifFalseBranch____title": "<PERSON><PERSON><PERSON> \"si falso\"", "@sage/xtrem-workflow/pages__workflow_action_condition__ifTrueBranch____title": "<PERSON><PERSON><PERSON> \"si verdadero\"", "@sage/xtrem-workflow/pages__workflow_action_condition__mainSection____title": "Datos básicos", "@sage/xtrem-workflow/pages__workflow_action_condition__title____helperText": "Título en el diagrama del flujo de trabajo", "@sage/xtrem-workflow/pages__workflow_action_condition__title____title": "Título de condición", "@sage/xtrem-workflow/pages__workflow_action_condition__variablesBlock____title": "Definición de condición", "@sage/xtrem-workflow/pages__workflow_action_delete_entity____subtitle": "Eliminar entidad", "@sage/xtrem-workflow/pages__workflow_action_delete_entity____title": "Configuración de acción", "@sage/xtrem-workflow/pages__workflow_action_delete_entity__dataEntity____columns__title__name": "Tipo de registro", "@sage/xtrem-workflow/pages__workflow_action_delete_entity__dataEntity____columns__title__package__name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-workflow/pages__workflow_action_delete_entity__dataEntity____columns__title__title": "Tipo de registro", "@sage/xtrem-workflow/pages__workflow_action_delete_entity__dataEntity____title": "Tipo de registro", "@sage/xtrem-workflow/pages__workflow_action_delete_entity__mainSection____title": "Datos básicos", "@sage/xtrem-workflow/pages__workflow_action_delete_entity__recordIdToDelete____title": "Variable de id. de registro", "@sage/xtrem-workflow/pages__workflow_action_delete_entity__title____helperText": "Título en el diagrama del flujo de trabajo", "@sage/xtrem-workflow/pages__workflow_action_delete_entity__title____title": "Título de acción", "@sage/xtrem-workflow/pages__workflow_action_read_entity____subtitle": "<PERSON>r registro", "@sage/xtrem-workflow/pages__workflow_action_read_entity____title": "Configuración de acción", "@sage/xtrem-workflow/pages__workflow_action_read_entity__failIfNotFound____helperText": "Generar un error si no se encuentra el registro.", "@sage/xtrem-workflow/pages__workflow_action_read_entity__failIfNotFound____title": "Error si no se encuentra", "@sage/xtrem-workflow/pages__workflow_action_read_entity__mainSection____title": "Datos básicos", "@sage/xtrem-workflow/pages__workflow_action_read_entity__nodeFactory____columns__title__name": "Tipo de registro", "@sage/xtrem-workflow/pages__workflow_action_read_entity__nodeFactory____columns__title__package__name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-workflow/pages__workflow_action_read_entity__nodeFactory____columns__title__title": "Tipo de registro", "@sage/xtrem-workflow/pages__workflow_action_read_entity__nodeFactory____title": "Tipo de registro", "@sage/xtrem-workflow/pages__workflow_action_read_entity__outputVariableName____title": "Nombre de variable de salida", "@sage/xtrem-workflow/pages__workflow_action_read_entity__recordKey____title": "Clave de registro", "@sage/xtrem-workflow/pages__workflow_action_read_entity__title____helperText": "Título en el diagrama del flujo de trabajo", "@sage/xtrem-workflow/pages__workflow_action_read_entity__title____title": "Título de acción", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification____subtitle": "Notificar usuario", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification____title": "Configuración de acción", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__actionsForLocale____columns__title___originalIndex": "Índice original", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__actionsForLocale____columns__title__link": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__actionsForLocale____columns__title__style": "Tipo de acción", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__actionsForLocale____columns__title__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__actionsSection____title": "Acciones", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__icon____title": "Icono", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__level____title": "Tipo", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__mainSection____title": "General", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__notificationDescription____title": "Descripción de notificación", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__notificationTitle____title": "Título de notificación", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__recipients____addButtonText": "<PERSON><PERSON><PERSON>", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__recipients____columns__columns__user__email__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__recipients____columns__columns__user__email__title__2": "Nombre", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__recipients____columns__columns__user__email__title__3": "E-mail", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__recipients____columns__lookupDialogTitle__user__email": "Se<PERSON><PERSON><PERSON>r usuario", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__recipients____columns__placeholder__user__email": "Se<PERSON><PERSON><PERSON>r usuario", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__recipients____columns__title__isManuallySet": "Entrada manual", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__recipients____columns__title__user": "Usuario", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__recipients____columns__title__user__email": "Usuario", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__recipients____title": "Des<PERSON><PERSON><PERSON>", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__recipientsSection____title": "Des<PERSON><PERSON><PERSON>", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__shouldDisplayToast____helperText": "Las notificaciones urgentes se muestran como mensajes emergentes.", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__shouldDisplayToast____title": "Urgente", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__title____helperText": "Título en el diagrama del flujo de trabajo", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__title____title": "Título de acción", "@sage/xtrem-workflow/pages__workflow_action_test_stub____subtitle": "Usar código auxiliar para pruebas", "@sage/xtrem-workflow/pages__workflow_action_test_stub____title": "Configuración de acción", "@sage/xtrem-workflow/pages__workflow_action_test_stub__mainSection____title": "Datos básicos", "@sage/xtrem-workflow/pages__workflow_action_test_stub__outputVariableName____title": "Nombre de variable de salida", "@sage/xtrem-workflow/pages__workflow_action_test_stub__title____helperText": "Título en el diagrama del flujo de trabajo", "@sage/xtrem-workflow/pages__workflow_action_test_stub__title____title": "Título de acción", "@sage/xtrem-workflow/pages__workflow_action_update_entity____subtitle": "Actualizar entidad", "@sage/xtrem-workflow/pages__workflow_action_update_entity____title": "Configuración de acción", "@sage/xtrem-workflow/pages__workflow_action_update_entity__mainSection____title": "Selección de datos", "@sage/xtrem-workflow/pages__workflow_action_update_entity__pathToUpdate____title": "Registro por actualizar", "@sage/xtrem-workflow/pages__workflow_action_update_entity__title____helperText": "Título en el diagrama del flujo de trabajo", "@sage/xtrem-workflow/pages__workflow_action_update_entity__title____title": "Título de acción", "@sage/xtrem-workflow/pages__workflow_action_update_entity__updateRulesPod____title": "Valores nuevos", "@sage/xtrem-workflow/pages__workflow_action_wait____subtitle": "<PERSON><PERSON><PERSON>", "@sage/xtrem-workflow/pages__workflow_action_wait____title": "Configuración de acción", "@sage/xtrem-workflow/pages__workflow_action_wait__mainSection____title": "Datos básicos", "@sage/xtrem-workflow/pages__workflow_action_wait__quantity____title": "Cantidad de tiempo", "@sage/xtrem-workflow/pages__workflow_action_wait__title____helperText": "Título en el diagrama del flujo de trabajo", "@sage/xtrem-workflow/pages__workflow_action_wait__title____title": "Título de acción", "@sage/xtrem-workflow/pages__workflow_action_wait__unit____title": "Unidad", "@sage/xtrem-workflow/pages__workflow_definition____navigationPanel__inlineActions__title__duplicate": "Duplicar", "@sage/xtrem-workflow/pages__workflow_definition____navigationPanel__listItem__id__title": "Id.", "@sage/xtrem-workflow/pages__workflow_definition____navigationPanel__listItem__title__title": "Nombre", "@sage/xtrem-workflow/pages__workflow_definition____navigationPanel__listItem__titleRight__title": "Activo", "@sage/xtrem-workflow/pages__workflow_definition____navigationPanel__listItem__triggerDetails__title": "Detalles de desencadenante", "@sage/xtrem-workflow/pages__workflow_definition____navigationPanel__listItem__triggerType__title": "Tipo de desencadenante", "@sage/xtrem-workflow/pages__workflow_definition____navigationPanel__optionsMenu__title": "Todos los flujos", "@sage/xtrem-workflow/pages__workflow_definition____objectTypePlural": "Flujos de trabajo", "@sage/xtrem-workflow/pages__workflow_definition____objectTypeSingular": "Flujo de trabajo", "@sage/xtrem-workflow/pages__workflow_definition____title": "Diseñador de flujos de trabajo", "@sage/xtrem-workflow/pages__workflow_definition__deleteWorkflowDefinition____title": "Eliminar", "@sage/xtrem-workflow/pages__workflow_definition__designerSection____title": "Diseñador", "@sage/xtrem-workflow/pages__workflow_definition__eventLog____columns__title__event": "", "@sage/xtrem-workflow/pages__workflow_definition__eventLog____columns__title__message": "", "@sage/xtrem-workflow/pages__workflow_definition__eventLog____columns__title__stepId": "", "@sage/xtrem-workflow/pages__workflow_definition__eventLog____columns__title__timestamp": "", "@sage/xtrem-workflow/pages__workflow_definition__eventLog____title": "", "@sage/xtrem-workflow/pages__workflow_definition__executionSection____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-workflow/pages__workflow_definition__flow____title": "Flujo de trabajo", "@sage/xtrem-workflow/pages__workflow_definition__headerSection____title": "Cabecera", "@sage/xtrem-workflow/pages__workflow_definition__id____title": "Id.", "@sage/xtrem-workflow/pages__workflow_definition__isActive____title": "Activo", "@sage/xtrem-workflow/pages__workflow_definition__name____title": "Nombre", "@sage/xtrem-workflow/pages__workflow_definition__processes____columns__postfix__duration": "segundos", "@sage/xtrem-workflow/pages__workflow_definition__processes____columns__title__duration": "Duración", "@sage/xtrem-workflow/pages__workflow_definition__processes____columns__title__errorMessages": "Errores", "@sage/xtrem-workflow/pages__workflow_definition__processes____columns__title__startedAt": "Hora de inicio", "@sage/xtrem-workflow/pages__workflow_definition__processes____columns__title__status": "Estado", "@sage/xtrem-workflow/pages__workflow_definition__processes____columns__title__triggeringUser__displayName": "Usuario", "@sage/xtrem-workflow/pages__workflow_definition__processes____dropdownActions__title": "Ver registro", "@sage/xtrem-workflow/pages__workflow_definition__processes____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-workflow/pages__workflow_definition__refreshProcesses____title": "Actualizar", "@sage/xtrem-workflow/pages__workflow_definition__status____title": "Estado", "@sage/xtrem-workflow/pages__workflow_definition__testUser____columns__title__email": "E-mail", "@sage/xtrem-workflow/pages__workflow_definition__testUser____columns__title__firstName": "Nombre", "@sage/xtrem-workflow/pages__workflow_definition__testUser____columns__title__lastName": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-workflow/pages__workflow_definition__testUser____lookupDialogTitle": "Se<PERSON><PERSON><PERSON>r usuario", "@sage/xtrem-workflow/pages__workflow_definition__testUser____title": "Usuario de prueba", "@sage/xtrem-workflow/pages__workflow_definition__variables____title": "", "@sage/xtrem-workflow/pages__workflow_event_entity_created____subtitle": "Entidad creada", "@sage/xtrem-workflow/pages__workflow_event_entity_created____title": "Configuración de desencadenante", "@sage/xtrem-workflow/pages__workflow_event_entity_created__conditionSection____title": "Condición", "@sage/xtrem-workflow/pages__workflow_event_entity_created__filters____helperText": "Si todas las condiciones son verdaderas, la ejecución continúa hacia abajo. Si hay alguna condición falsa, la ejecución continúa hacia la derecha.", "@sage/xtrem-workflow/pages__workflow_event_entity_created__filters____title": "Condición", "@sage/xtrem-workflow/pages__workflow_event_entity_created__mainSection____title": "Configuración", "@sage/xtrem-workflow/pages__workflow_event_entity_created__nodeFactory____columns__title__name": "Tipo de registro", "@sage/xtrem-workflow/pages__workflow_event_entity_created__nodeFactory____columns__title__package__name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-workflow/pages__workflow_event_entity_created__nodeFactory____columns__title__title": "Tipo de registro", "@sage/xtrem-workflow/pages__workflow_event_entity_created__nodeFactory____title": "Tipo de registro", "@sage/xtrem-workflow/pages__workflow_event_entity_created__title____helperText": "Título en el diagrama del flujo de trabajo", "@sage/xtrem-workflow/pages__workflow_event_entity_created__title____title": "Título de desencadenante", "@sage/xtrem-workflow/pages__workflow_event_entity_deleted____subtitle": "Entidad eliminada", "@sage/xtrem-workflow/pages__workflow_event_entity_deleted____title": "Configuración de desencadenante", "@sage/xtrem-workflow/pages__workflow_event_entity_deleted__dataEntity____columns__title__name": "Tipo de registro", "@sage/xtrem-workflow/pages__workflow_event_entity_deleted__dataEntity____columns__title__package__name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-workflow/pages__workflow_event_entity_deleted__dataEntity____columns__title__title": "Tipo de registro", "@sage/xtrem-workflow/pages__workflow_event_entity_deleted__dataEntity____title": "Tipo de registro", "@sage/xtrem-workflow/pages__workflow_event_entity_deleted__mainSection____title": "Datos básicos", "@sage/xtrem-workflow/pages__workflow_event_entity_deleted__outputVariableName____title": "Nombre de variable de salida", "@sage/xtrem-workflow/pages__workflow_event_entity_deleted__title____helperText": "Título en el diagrama del flujo de trabajo", "@sage/xtrem-workflow/pages__workflow_event_entity_deleted__title____title": "Título de desencadenante", "@sage/xtrem-workflow/pages__workflow_event_entity_updated____subtitle": "Entidad actualizada", "@sage/xtrem-workflow/pages__workflow_event_entity_updated____title": "Configuración de desencadenante", "@sage/xtrem-workflow/pages__workflow_event_entity_updated__conditionSection____title": "Condición", "@sage/xtrem-workflow/pages__workflow_event_entity_updated__filters____helperText": "Si todas las condiciones son verdaderas, la ejecución continúa hacia abajo. Si hay alguna condición falsa, la ejecución continúa hacia la derecha.", "@sage/xtrem-workflow/pages__workflow_event_entity_updated__filters____title": "Condición", "@sage/xtrem-workflow/pages__workflow_event_entity_updated__mainSection____title": "Configuración", "@sage/xtrem-workflow/pages__workflow_event_entity_updated__nodeFactory____columns__title__name": "Tipo de registro", "@sage/xtrem-workflow/pages__workflow_event_entity_updated__nodeFactory____columns__title__package__name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-workflow/pages__workflow_event_entity_updated__nodeFactory____columns__title__title": "Tipo de registro", "@sage/xtrem-workflow/pages__workflow_event_entity_updated__nodeFactory____title": "Tipo de registro", "@sage/xtrem-workflow/pages__workflow_event_entity_updated__title____helperText": "Título en el diagrama del flujo de trabajo", "@sage/xtrem-workflow/pages__workflow_event_entity_updated__title____title": "Título de desencadenante", "@sage/xtrem-workflow/pages__workflow_event_test_started____subtitle": "Prueba iniciada", "@sage/xtrem-workflow/pages__workflow_event_test_started____title": "Configuración de desencadenante", "@sage/xtrem-workflow/pages__workflow_event_test_started__mainSection____title": "Datos básicos", "@sage/xtrem-workflow/pages__workflow_event_test_started__parameters____addButtonText": "<PERSON><PERSON><PERSON>", "@sage/xtrem-workflow/pages__workflow_event_test_started__parameters____columns__columns__node__name__title": "Tipo de registro", "@sage/xtrem-workflow/pages__workflow_event_test_started__parameters____columns__columns__node__name__title__2": "Tipo de registro", "@sage/xtrem-workflow/pages__workflow_event_test_started__parameters____columns__columns__node__name__title__3": "<PERSON><PERSON><PERSON>", "@sage/xtrem-workflow/pages__workflow_event_test_started__parameters____columns__lookupDialogTitle__node__name": "<PERSON><PERSON><PERSON><PERSON><PERSON> nodo referenciado", "@sage/xtrem-workflow/pages__workflow_event_test_started__parameters____columns__placeholder__node__name": "<PERSON><PERSON><PERSON><PERSON><PERSON> nodo referenciado", "@sage/xtrem-workflow/pages__workflow_event_test_started__parameters____columns__title__name": "Nombre de parámetro", "@sage/xtrem-workflow/pages__workflow_event_test_started__parameters____columns__title__node__name": "Nodo", "@sage/xtrem-workflow/pages__workflow_event_test_started__parameters____columns__title__type": "Tipo", "@sage/xtrem-workflow/pages__workflow_event_test_started__parameters____title": "Parámetros de prueba", "@sage/xtrem-workflow/pages__workflow_event_test_started__parametersSection____title": "Parámetros", "@sage/xtrem-workflow/pages__workflow_event_test_started__title____helperText": "Título en el diagrama del flujo de trabajo", "@sage/xtrem-workflow/pages__workflow_event_test_started__title____title": "Título de desencadenante", "@sage/xtrem-workflow/pages__workflow_mutation_action____subtitle": "Ejecutar mutación GraphQL", "@sage/xtrem-workflow/pages__workflow_mutation_action____title": "Configuración de acción", "@sage/xtrem-workflow/pages__workflow_mutation_action__actionParametersPod____title": "Parámetros", "@sage/xtrem-workflow/pages__workflow_mutation_action__configDescription____title": "Descripción", "@sage/xtrem-workflow/pages__workflow_mutation_action__configRegisterAsSetupNode____helperText": "Registrar la acción como un nodo de configuración", "@sage/xtrem-workflow/pages__workflow_mutation_action__configRegisterAsSetupNode____title": "Nodo de configuración", "@sage/xtrem-workflow/pages__workflow_mutation_action__mainSection____title": "Principal", "@sage/xtrem-workflow/pages__workflow_mutation_action__mutation____columns__title__factory__name": "Nodo", "@sage/xtrem-workflow/pages__workflow_mutation_action__mutation____columns__title__name": "Mutación", "@sage/xtrem-workflow/pages__workflow_mutation_action__mutation____lookupDialogTitle": "Seleccionar mutación", "@sage/xtrem-workflow/pages__workflow_mutation_action__mutation____title": "Ejecutar mutación", "@sage/xtrem-workflow/pages__workflow_mutation_action__mutationArgumentsPod____title": "Argumentos", "@sage/xtrem-workflow/pages__workflow_mutation_action__mutationSection____title": "Mutación", "@sage/xtrem-workflow/pages__workflow_mutation_action__outputVariableName____title": "Nombre de variable de salida", "@sage/xtrem-workflow/pages__workflow_mutation_action__registerActionButton____title": "Registrar acción", "@sage/xtrem-workflow/pages__workflow_mutation_action__registrationSection____title": "Registro", "@sage/xtrem-workflow/pages__workflow_mutation_action__selector____title": "Selector", "@sage/xtrem-workflow/pages__workflow_mutation_action__showAdvancedOptions____helperText": "Se muestran las opciones avanzadas de la acción.\n\nAVISO: reservado para usuarios avanzados.", "@sage/xtrem-workflow/pages__workflow_mutation_action__showAdvancedOptions____title": "Mostrar opciones avanzadas", "@sage/xtrem-workflow/pages__workflow_mutation_action__title____helperText": "Título en el diagrama del flujo de trabajo", "@sage/xtrem-workflow/pages__workflow_mutation_action__title____title": "Título de acción", "@sage/xtrem-workflow/pages__workflow_process____navigationPanel__bulkActions__title": "", "@sage/xtrem-workflow/pages__workflow_process____navigationPanel__listItem__duration__postfix": "", "@sage/xtrem-workflow/pages__workflow_process____navigationPanel__listItem__duration__title": "", "@sage/xtrem-workflow/pages__workflow_process____navigationPanel__listItem__errorMessage__title": "", "@sage/xtrem-workflow/pages__workflow_process____navigationPanel__listItem__line2__title": "", "@sage/xtrem-workflow/pages__workflow_process____navigationPanel__listItem__startedAt__title": "", "@sage/xtrem-workflow/pages__workflow_process____navigationPanel__listItem__title__title": "", "@sage/xtrem-workflow/pages__workflow_process____navigationPanel__listItem__titleRight__title": "", "@sage/xtrem-workflow/pages__workflow_process____navigationPanel__listItem__triggeredBy__title": "", "@sage/xtrem-workflow/pages__workflow_process____navigationPanel__optionsMenu__title": "Todos los procesos", "@sage/xtrem-workflow/pages__workflow_process____objectTypePlural": "Trazas de flujos de trabajo", "@sage/xtrem-workflow/pages__workflow_process____objectTypeSingular": "Traza de flujo de trabajo", "@sage/xtrem-workflow/pages__workflow_process____title": "Trazas de flujos de trabajo", "@sage/xtrem-workflow/pages__workflow_process___id____title": "", "@sage/xtrem-workflow/pages__workflow_process__completedAt____title": "Hora de fin", "@sage/xtrem-workflow/pages__workflow_process__designerSection____title": "", "@sage/xtrem-workflow/pages__workflow_process__duration____postfix": "", "@sage/xtrem-workflow/pages__workflow_process__duration____title": "", "@sage/xtrem-workflow/pages__workflow_process__eventLogsSection____title": "", "@sage/xtrem-workflow/pages__workflow_process__flow____title": "Flujo de trabajo", "@sage/xtrem-workflow/pages__workflow_process__goToDesigner____title": "", "@sage/xtrem-workflow/pages__workflow_process__headerSection____title": "Cabecera", "@sage/xtrem-workflow/pages__workflow_process__olderVersionMessage____content": "", "@sage/xtrem-workflow/pages__workflow_process__startedAt____title": "Hora de inicio", "@sage/xtrem-workflow/pages__workflow_process__status____title": "Estado", "@sage/xtrem-workflow/pages__workflow_process__tableLogs____columns__title__event": "", "@sage/xtrem-workflow/pages__workflow_process__tableLogs____columns__title__message": "", "@sage/xtrem-workflow/pages__workflow_process__tableLogs____columns__title__step": "", "@sage/xtrem-workflow/pages__workflow_process__tableLogs____columns__title__timestamp": "", "@sage/xtrem-workflow/pages__workflow_process__tableLogs____title": "", "@sage/xtrem-workflow/pages__workflow_process__triggeringUser____title": "Usuario", "@sage/xtrem-workflow/pages__workflow_process__variablesSection____title": "", "@sage/xtrem-workflow/pages__workflow_process__workflow____title": "Flujo de trabajo", "@sage/xtrem-workflow/pages__workflow_process__workflowDefinitionId____title": "Id. de definición de flujo de trabajo", "@sage/xtrem-workflow/pages__workflow_select_variables_dialog____title": "", "@sage/xtrem-workflow/pages__workflow_select_variables_dialog__fromPath____title": "Selecciona<PERSON>", "@sage/xtrem-workflow/pages__workflow_select_variables_dialog__ok____title": "Aceptar", "@sage/xtrem-workflow/pages__workflow_select_variables_dialog__selectedProperties____helperText": "Solo se solicitarán a la base de datos las propiedades seleccionadas.", "@sage/xtrem-workflow/pages__workflow_step_template____navigationPanel__listItem__line2__title": "Descripción", "@sage/xtrem-workflow/pages__workflow_step_template____navigationPanel__listItem__title__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-workflow/pages__workflow_step_template____objectTypePlural": "Plantillas de pasos de flujo de trabajo", "@sage/xtrem-workflow/pages__workflow_step_template____title": "Plantilla de paso de flujo de trabajo", "@sage/xtrem-workflow/pages__workflow_step_template__description____title": "Descripción", "@sage/xtrem-workflow/pages__workflow_step_template__editActionButton____title": "Editar acción", "@sage/xtrem-workflow/pages__workflow_step_template__generalSection____title": "General", "@sage/xtrem-workflow/pages__workflow_step_template__icon____title": "Icono", "@sage/xtrem-workflow/pages__workflow_step_template__isActive____title": "Activa", "@sage/xtrem-workflow/pages__workflow_step_template__title____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-workflow/permission__manage__name": "Gestionar", "@sage/xtrem-workflow/permission__read__name": "<PERSON><PERSON>", "@sage/xtrem-workflow/service_options__workflow__name": "Flujo de trabajo", "@sage/xtrem-workflow/service_options__workflow_advanced__name": "Flujo de trabajo avanzado", "@sage/xtrem-workflow/service_options__workflow_option__name": "Opción de flujo de trabajo", "@sage/xtrem-workflow/time-unit-d": "Días", "@sage/xtrem-workflow/time-unit-h": "<PERSON><PERSON>", "@sage/xtrem-workflow/time-unit-m": "<PERSON><PERSON><PERSON>", "@sage/xtrem-workflow/time-unit-ms": "Milisegundos", "@sage/xtrem-workflow/time-unit-s": "<PERSON><PERSON><PERSON>", "@sage/xtrem-workflow/workflow_error_empty_workflow": "Añade un desencadenante.", "@sage/xtrem-workflow/workflow_error_no_property_to_update": "<PERSON><PERSON><PERSON> una propiedad.", "@sage/xtrem-workflow/workflow_mutation_action_argument_not_found": "La acción se refiere al argumento \"{{argumentName}}\", que no existe en la mutación \"{{mutationName}}\".", "@sage/xtrem-workflow/workflow_mutation_argument_not_found": "El argumento \"{{argument}}\" no está definido en la configuración de la acción.", "@sage/xtrem-workflow/workflow_mutation_argument_type_mismatch": "El tipo \"{{expectedType}}\" del argumento \"{{argument}}\" no coincide con el esperado: \"{{actualType}}\".", "@sage/xtrem-workflow/workflow_unknown_mutation": "La mutación \"{{mutationName}}\" no existe.", "@sage/xtrem-workflow/workflow-delete-definition-confirm-body": "", "@sage/xtrem-workflow/workflow-delete-definition-confirm-title": "", "@sage/xtrem-workflow/workflow-dialog-title-error": "", "@sage/xtrem-workflow/workflow-entity-created-condition-not-an-array": "\"condition\" no es una matriz.", "@sage/xtrem-workflow/workflow-entity-created-default-title": "Entidad {{factoryName}} creada", "@sage/xtrem-workflow/workflow-entity-created-no-property-selection": "La configuración del desencadenante no es válida: la propiedad es obligatoria cuando hay un filtro.", "@sage/xtrem-workflow/workflow-entity-created-step-variables-not-an-array": "\"stepVariables\" no es una matriz.", "@sage/xtrem-workflow/workflow-entity-deleted-default-title": "Entidad {{factoryName}} eliminada", "@sage/xtrem-workflow/workflow-entity-updated-condition-not-an-array": "\"condition\" no es una matriz.", "@sage/xtrem-workflow/workflow-entity-updated-default-title": "Entidad {{factoryName}} actualizada", "@sage/xtrem-workflow/workflow-entity-updated-no-property-selection": "La configuración del desencadenante no es válida: la propiedad es obligatoria cuando hay un filtro.", "@sage/xtrem-workflow/workflow-entity-updated-step-variables-not-an-array": "\"stepVariables\" no es una matriz.", "@sage/xtrem-workflow/workflow-error-cannot-unselect-root-property": "", "@sage/xtrem-workflow/workflow-error-event-cant-have-a-parent": "", "@sage/xtrem-workflow/workflow-error-flow-has-cycle": "", "@sage/xtrem-workflow/workflow-error-more-than-one-start-node": "", "@sage/xtrem-workflow/workflow-error-no-description": "", "@sage/xtrem-workflow/workflow-error-no-parent": "", "@sage/xtrem-workflow/workflow-error-no-start-node": "", "@sage/xtrem-workflow/workflow-error-no-title": "", "@sage/xtrem-workflow/workflow-error-start-topics-forbidden": "", "@sage/xtrem-workflow/workflow-error-too-few-outputs": "", "@sage/xtrem-workflow/workflow-error-too-many-outputs": "", "@sage/xtrem-workflow/workflow-send-user-notification-error-no-notification-description": "La descripción de la notificación es obligatoria.", "@sage/xtrem-workflow/workflow-send-user-notification-error-no-notification-title": "El título de la notificación es obligatorio.", "@sage/xtrem-workflow/workflow-variable-not-found": "La variable {{variableName}} no existe.", "@sage/xtrem-workflow/workflow-variable-not-found-with-suggestion": "La variable {{variableName}} no existe. ¿Quieres utilizar {{closestVariable}}?"}