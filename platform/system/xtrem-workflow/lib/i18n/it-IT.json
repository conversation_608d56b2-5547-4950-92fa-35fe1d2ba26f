{"@sage/xtrem-workflow/activity__workflow_definition__name": "", "@sage/xtrem-workflow/activity__workflow_process__name": "", "@sage/xtrem-workflow/add-properties": "", "@sage/xtrem-workflow/add-variables": "", "@sage/xtrem-workflow/data_types__test_decimal_data_type__name": "", "@sage/xtrem-workflow/data_types__test_enum_enum__name": "", "@sage/xtrem-workflow/data_types__test_string_data_type__name": "", "@sage/xtrem-workflow/data_types__workflow_definition_status_enum__name": "", "@sage/xtrem-workflow/data_types__workflow_mutation_argument_origin_enum__name": "", "@sage/xtrem-workflow/data_types__workflow_mutation_parameter_type_enum__name": "", "@sage/xtrem-workflow/data_types__workflow_process_status_enum__name": "", "@sage/xtrem-workflow/enums__test_enum__value1": "", "@sage/xtrem-workflow/enums__test_enum__value2": "", "@sage/xtrem-workflow/enums__test_enum__value3": "", "@sage/xtrem-workflow/enums__workflow_definition_status__production": "", "@sage/xtrem-workflow/enums__workflow_definition_status__test": "", "@sage/xtrem-workflow/enums__workflow_mutation_argument_origin__fromParameter": "", "@sage/xtrem-workflow/enums__workflow_mutation_argument_origin__fromVariable": "", "@sage/xtrem-workflow/enums__workflow_mutation_argument_origin__manual": "", "@sage/xtrem-workflow/enums__workflow_mutation_parameter_type__boolean": "", "@sage/xtrem-workflow/enums__workflow_mutation_parameter_type__date": "", "@sage/xtrem-workflow/enums__workflow_mutation_parameter_type__decimal": "", "@sage/xtrem-workflow/enums__workflow_mutation_parameter_type__enum": "", "@sage/xtrem-workflow/enums__workflow_mutation_parameter_type__integer": "", "@sage/xtrem-workflow/enums__workflow_mutation_parameter_type__other": "", "@sage/xtrem-workflow/enums__workflow_mutation_parameter_type__reference": "", "@sage/xtrem-workflow/enums__workflow_mutation_parameter_type__string": "", "@sage/xtrem-workflow/enums__workflow_process_status__cancelled": "", "@sage/xtrem-workflow/enums__workflow_process_status__error": "", "@sage/xtrem-workflow/enums__workflow_process_status__running": "", "@sage/xtrem-workflow/enums__workflow_process_status__shutDown": "", "@sage/xtrem-workflow/enums__workflow_process_status__skipped": "", "@sage/xtrem-workflow/enums__workflow_process_status__success": "", "@sage/xtrem-workflow/enums__workflow_process_status__suspended": "", "@sage/xtrem-workflow/error-no-test-user-in-test-mode": "", "@sage/xtrem-workflow/invalid-workflow-is-disabled": "", "@sage/xtrem-workflow/menu_item__automation": "", "@sage/xtrem-workflow/menu_item__designer": "", "@sage/xtrem-workflow/menu_item__process": "", "@sage/xtrem-workflow/nodes__test_config_locale_error_message": "", "@sage/xtrem-workflow/nodes__workflow_definition__asyncMutation__asyncExport": "", "@sage/xtrem-workflow/nodes__workflow_definition__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-workflow/nodes__workflow_definition__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-workflow/nodes__workflow_definition__node_name": "", "@sage/xtrem-workflow/nodes__workflow_definition__property__diagram": "", "@sage/xtrem-workflow/nodes__workflow_definition__property__flow": "", "@sage/xtrem-workflow/nodes__workflow_definition__property__id": "", "@sage/xtrem-workflow/nodes__workflow_definition__property__isActive": "", "@sage/xtrem-workflow/nodes__workflow_definition__property__name": "", "@sage/xtrem-workflow/nodes__workflow_definition__property__processes": "", "@sage/xtrem-workflow/nodes__workflow_definition__property__startTopic": "", "@sage/xtrem-workflow/nodes__workflow_definition__property__status": "", "@sage/xtrem-workflow/nodes__workflow_definition__property__testUser": "", "@sage/xtrem-workflow/nodes__workflow_diagram__asyncMutation__asyncExport": "", "@sage/xtrem-workflow/nodes__workflow_diagram__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-workflow/nodes__workflow_diagram__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-workflow/nodes__workflow_diagram__mutation__controlDiagram": "", "@sage/xtrem-workflow/nodes__workflow_diagram__mutation__controlDiagram__failed": "", "@sage/xtrem-workflow/nodes__workflow_diagram__mutation__controlDiagram__parameter__edges": "", "@sage/xtrem-workflow/nodes__workflow_diagram__mutation__controlDiagram__parameter__nodes": "", "@sage/xtrem-workflow/nodes__workflow_diagram__node_name": "", "@sage/xtrem-workflow/nodes__workflow_diagram__property__data": "", "@sage/xtrem-workflow/nodes__workflow_diagram__property__version": "", "@sage/xtrem-workflow/nodes__workflow_process__asyncMutation__asyncExport": "", "@sage/xtrem-workflow/nodes__workflow_process__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-workflow/nodes__workflow_process__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-workflow/nodes__workflow_process__node_name": "", "@sage/xtrem-workflow/nodes__workflow_process__property__completedAt": "", "@sage/xtrem-workflow/nodes__workflow_process__property__containerId": "", "@sage/xtrem-workflow/nodes__workflow_process__property__definition": "", "@sage/xtrem-workflow/nodes__workflow_process__property__diagram": "", "@sage/xtrem-workflow/nodes__workflow_process__property__duration": "", "@sage/xtrem-workflow/nodes__workflow_process__property__errorMessages": "", "@sage/xtrem-workflow/nodes__workflow_process__property__eventLog": "", "@sage/xtrem-workflow/nodes__workflow_process__property__id": "", "@sage/xtrem-workflow/nodes__workflow_process__property__originId": "", "@sage/xtrem-workflow/nodes__workflow_process__property__startedAt": "", "@sage/xtrem-workflow/nodes__workflow_process__property__startNotificationId": "", "@sage/xtrem-workflow/nodes__workflow_process__property__state": "", "@sage/xtrem-workflow/nodes__workflow_process__property__status": "", "@sage/xtrem-workflow/nodes__workflow_process__property__triggeringUser": "", "@sage/xtrem-workflow/nodes__workflow_process__property__variables": "", "@sage/xtrem-workflow/nodes__workflow_step_template__asyncMutation__asyncExport": "", "@sage/xtrem-workflow/nodes__workflow_step_template__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-workflow/nodes__workflow_step_template__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-workflow/nodes__workflow_step_template__mutation__registerStepConfiguration": "", "@sage/xtrem-workflow/nodes__workflow_step_template__mutation__registerStepConfiguration__failed": "", "@sage/xtrem-workflow/nodes__workflow_step_template__mutation__registerStepConfiguration__parameter__asSetupNode": "", "@sage/xtrem-workflow/nodes__workflow_step_template__mutation__registerStepConfiguration__parameter__configData": "", "@sage/xtrem-workflow/nodes__workflow_step_template__mutation__registerStepConfiguration__parameter__factory": "", "@sage/xtrem-workflow/nodes__workflow_step_template__mutation__registerStepConfiguration__parameter__localizedDescription": "", "@sage/xtrem-workflow/nodes__workflow_step_template__mutation__registerStepConfiguration__parameter__localizedTitle": "", "@sage/xtrem-workflow/nodes__workflow_step_template__mutation__registerStepConfiguration__parameter__stepConstructor": "", "@sage/xtrem-workflow/nodes__workflow_step_template__node_name": "", "@sage/xtrem-workflow/nodes__workflow_step_template__property__color": "", "@sage/xtrem-workflow/nodes__workflow_step_template__property__configData": "", "@sage/xtrem-workflow/nodes__workflow_step_template__property__description": "", "@sage/xtrem-workflow/nodes__workflow_step_template__property__factory": "", "@sage/xtrem-workflow/nodes__workflow_step_template__property__icon": "", "@sage/xtrem-workflow/nodes__workflow_step_template__property__isActive": "", "@sage/xtrem-workflow/nodes__workflow_step_template__property__serviceOptions": "", "@sage/xtrem-workflow/nodes__workflow_step_template__property__stepConstructor": "", "@sage/xtrem-workflow/nodes__workflow_step_template__property__stepDescriptor": "", "@sage/xtrem-workflow/nodes__workflow_step_template__property__title": "", "@sage/xtrem-workflow/nodes__workflow_step_template__property__variant": "", "@sage/xtrem-workflow/package__name": "", "@sage/xtrem-workflow/pages__workflow_action_calculate____subtitle": "", "@sage/xtrem-workflow/pages__workflow_action_calculate____title": "", "@sage/xtrem-workflow/pages__workflow_action_calculate__calculationSteps____addButtonText": "", "@sage/xtrem-workflow/pages__workflow_action_calculate__calculationSteps____columns__title__isVariable": "", "@sage/xtrem-workflow/pages__workflow_action_calculate__calculationSteps____columns__title__operation": "", "@sage/xtrem-workflow/pages__workflow_action_calculate__calculationSteps____columns__title__value": "", "@sage/xtrem-workflow/pages__workflow_action_calculate__calculationSteps____columns__title__variable": "", "@sage/xtrem-workflow/pages__workflow_action_calculate__calculationSteps____title": "", "@sage/xtrem-workflow/pages__workflow_action_calculate__mainSection____title": "", "@sage/xtrem-workflow/pages__workflow_action_calculate__outputVariableName____title": "", "@sage/xtrem-workflow/pages__workflow_action_calculate__title____helperText": "", "@sage/xtrem-workflow/pages__workflow_action_calculate__title____title": "", "@sage/xtrem-workflow/pages__workflow_action_condition____title": "", "@sage/xtrem-workflow/pages__workflow_action_condition__branchesBlock____title": "", "@sage/xtrem-workflow/pages__workflow_action_condition__filters____helperText": "", "@sage/xtrem-workflow/pages__workflow_action_condition__filters____title": "", "@sage/xtrem-workflow/pages__workflow_action_condition__ifFalseBranch____title": "", "@sage/xtrem-workflow/pages__workflow_action_condition__ifTrueBranch____title": "", "@sage/xtrem-workflow/pages__workflow_action_condition__mainSection____title": "", "@sage/xtrem-workflow/pages__workflow_action_condition__title____helperText": "", "@sage/xtrem-workflow/pages__workflow_action_condition__title____title": "", "@sage/xtrem-workflow/pages__workflow_action_condition__variablesBlock____title": "", "@sage/xtrem-workflow/pages__workflow_action_delete_entity____subtitle": "", "@sage/xtrem-workflow/pages__workflow_action_delete_entity____title": "", "@sage/xtrem-workflow/pages__workflow_action_delete_entity__dataEntity____columns__title__name": "", "@sage/xtrem-workflow/pages__workflow_action_delete_entity__dataEntity____columns__title__package__name": "", "@sage/xtrem-workflow/pages__workflow_action_delete_entity__dataEntity____columns__title__title": "", "@sage/xtrem-workflow/pages__workflow_action_delete_entity__dataEntity____title": "", "@sage/xtrem-workflow/pages__workflow_action_delete_entity__mainSection____title": "", "@sage/xtrem-workflow/pages__workflow_action_delete_entity__recordIdToDelete____title": "", "@sage/xtrem-workflow/pages__workflow_action_delete_entity__title____helperText": "", "@sage/xtrem-workflow/pages__workflow_action_delete_entity__title____title": "", "@sage/xtrem-workflow/pages__workflow_action_read_entity____subtitle": "", "@sage/xtrem-workflow/pages__workflow_action_read_entity____title": "", "@sage/xtrem-workflow/pages__workflow_action_read_entity__failIfNotFound____helperText": "", "@sage/xtrem-workflow/pages__workflow_action_read_entity__failIfNotFound____title": "", "@sage/xtrem-workflow/pages__workflow_action_read_entity__mainSection____title": "", "@sage/xtrem-workflow/pages__workflow_action_read_entity__nodeFactory____columns__title__name": "", "@sage/xtrem-workflow/pages__workflow_action_read_entity__nodeFactory____columns__title__package__name": "", "@sage/xtrem-workflow/pages__workflow_action_read_entity__nodeFactory____columns__title__title": "", "@sage/xtrem-workflow/pages__workflow_action_read_entity__nodeFactory____title": "", "@sage/xtrem-workflow/pages__workflow_action_read_entity__outputVariableName____title": "", "@sage/xtrem-workflow/pages__workflow_action_read_entity__recordKey____title": "", "@sage/xtrem-workflow/pages__workflow_action_read_entity__title____helperText": "", "@sage/xtrem-workflow/pages__workflow_action_read_entity__title____title": "", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification____subtitle": "", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification____title": "", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__actionsForLocale____columns__title___originalIndex": "", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__actionsForLocale____columns__title__link": "", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__actionsForLocale____columns__title__style": "", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__actionsForLocale____columns__title__title": "", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__actionsSection____title": "", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__icon____title": "", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__level____title": "", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__mainSection____title": "", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__notificationDescription____title": "", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__notificationTitle____title": "", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__recipients____addButtonText": "", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__recipients____columns__columns__user__email__title": "", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__recipients____columns__columns__user__email__title__2": "", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__recipients____columns__columns__user__email__title__3": "", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__recipients____columns__lookupDialogTitle__user__email": "", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__recipients____columns__placeholder__user__email": "", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__recipients____columns__title__isManuallySet": "", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__recipients____columns__title__user": "", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__recipients____columns__title__user__email": "", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__recipients____title": "", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__recipientsSection____title": "", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__shouldDisplayToast____helperText": "", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__shouldDisplayToast____title": "", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__title____helperText": "", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__title____title": "", "@sage/xtrem-workflow/pages__workflow_action_test_stub____subtitle": "", "@sage/xtrem-workflow/pages__workflow_action_test_stub____title": "", "@sage/xtrem-workflow/pages__workflow_action_test_stub__mainSection____title": "", "@sage/xtrem-workflow/pages__workflow_action_test_stub__outputVariableName____title": "", "@sage/xtrem-workflow/pages__workflow_action_test_stub__title____helperText": "", "@sage/xtrem-workflow/pages__workflow_action_test_stub__title____title": "", "@sage/xtrem-workflow/pages__workflow_action_update_entity____subtitle": "", "@sage/xtrem-workflow/pages__workflow_action_update_entity____title": "", "@sage/xtrem-workflow/pages__workflow_action_update_entity__mainSection____title": "", "@sage/xtrem-workflow/pages__workflow_action_update_entity__pathToUpdate____title": "", "@sage/xtrem-workflow/pages__workflow_action_update_entity__title____helperText": "", "@sage/xtrem-workflow/pages__workflow_action_update_entity__title____title": "", "@sage/xtrem-workflow/pages__workflow_action_update_entity__updateRulesPod____title": "", "@sage/xtrem-workflow/pages__workflow_action_wait____subtitle": "", "@sage/xtrem-workflow/pages__workflow_action_wait____title": "", "@sage/xtrem-workflow/pages__workflow_action_wait__mainSection____title": "", "@sage/xtrem-workflow/pages__workflow_action_wait__quantity____title": "", "@sage/xtrem-workflow/pages__workflow_action_wait__title____helperText": "", "@sage/xtrem-workflow/pages__workflow_action_wait__title____title": "", "@sage/xtrem-workflow/pages__workflow_action_wait__unit____title": "", "@sage/xtrem-workflow/pages__workflow_definition____navigationPanel__inlineActions__title__duplicate": "", "@sage/xtrem-workflow/pages__workflow_definition____navigationPanel__listItem__id__title": "", "@sage/xtrem-workflow/pages__workflow_definition____navigationPanel__listItem__title__title": "", "@sage/xtrem-workflow/pages__workflow_definition____navigationPanel__listItem__titleRight__title": "", "@sage/xtrem-workflow/pages__workflow_definition____navigationPanel__listItem__triggerDetails__title": "", "@sage/xtrem-workflow/pages__workflow_definition____navigationPanel__listItem__triggerType__title": "", "@sage/xtrem-workflow/pages__workflow_definition____navigationPanel__optionsMenu__title": "", "@sage/xtrem-workflow/pages__workflow_definition____objectTypePlural": "", "@sage/xtrem-workflow/pages__workflow_definition____objectTypeSingular": "", "@sage/xtrem-workflow/pages__workflow_definition____title": "", "@sage/xtrem-workflow/pages__workflow_definition__deleteWorkflowDefinition____title": "", "@sage/xtrem-workflow/pages__workflow_definition__designerSection____title": "", "@sage/xtrem-workflow/pages__workflow_definition__eventLog____columns__title__event": "", "@sage/xtrem-workflow/pages__workflow_definition__eventLog____columns__title__message": "", "@sage/xtrem-workflow/pages__workflow_definition__eventLog____columns__title__stepId": "", "@sage/xtrem-workflow/pages__workflow_definition__eventLog____columns__title__timestamp": "", "@sage/xtrem-workflow/pages__workflow_definition__eventLog____title": "", "@sage/xtrem-workflow/pages__workflow_definition__executionSection____title": "", "@sage/xtrem-workflow/pages__workflow_definition__flow____title": "", "@sage/xtrem-workflow/pages__workflow_definition__headerSection____title": "", "@sage/xtrem-workflow/pages__workflow_definition__id____title": "", "@sage/xtrem-workflow/pages__workflow_definition__isActive____title": "", "@sage/xtrem-workflow/pages__workflow_definition__name____title": "", "@sage/xtrem-workflow/pages__workflow_definition__processes____columns__postfix__duration": "", "@sage/xtrem-workflow/pages__workflow_definition__processes____columns__title__duration": "", "@sage/xtrem-workflow/pages__workflow_definition__processes____columns__title__errorMessages": "", "@sage/xtrem-workflow/pages__workflow_definition__processes____columns__title__startedAt": "", "@sage/xtrem-workflow/pages__workflow_definition__processes____columns__title__status": "", "@sage/xtrem-workflow/pages__workflow_definition__processes____columns__title__triggeringUser__displayName": "", "@sage/xtrem-workflow/pages__workflow_definition__processes____dropdownActions__title": "", "@sage/xtrem-workflow/pages__workflow_definition__processes____title": "", "@sage/xtrem-workflow/pages__workflow_definition__refreshProcesses____title": "", "@sage/xtrem-workflow/pages__workflow_definition__status____title": "", "@sage/xtrem-workflow/pages__workflow_definition__testUser____columns__title__email": "", "@sage/xtrem-workflow/pages__workflow_definition__testUser____columns__title__firstName": "", "@sage/xtrem-workflow/pages__workflow_definition__testUser____columns__title__lastName": "", "@sage/xtrem-workflow/pages__workflow_definition__testUser____lookupDialogTitle": "", "@sage/xtrem-workflow/pages__workflow_definition__testUser____title": "", "@sage/xtrem-workflow/pages__workflow_definition__variables____title": "", "@sage/xtrem-workflow/pages__workflow_event_entity_created____subtitle": "", "@sage/xtrem-workflow/pages__workflow_event_entity_created____title": "", "@sage/xtrem-workflow/pages__workflow_event_entity_created__conditionSection____title": "", "@sage/xtrem-workflow/pages__workflow_event_entity_created__filters____helperText": "", "@sage/xtrem-workflow/pages__workflow_event_entity_created__filters____title": "", "@sage/xtrem-workflow/pages__workflow_event_entity_created__mainSection____title": "", "@sage/xtrem-workflow/pages__workflow_event_entity_created__nodeFactory____columns__title__name": "", "@sage/xtrem-workflow/pages__workflow_event_entity_created__nodeFactory____columns__title__package__name": "", "@sage/xtrem-workflow/pages__workflow_event_entity_created__nodeFactory____columns__title__title": "", "@sage/xtrem-workflow/pages__workflow_event_entity_created__nodeFactory____title": "", "@sage/xtrem-workflow/pages__workflow_event_entity_created__title____helperText": "", "@sage/xtrem-workflow/pages__workflow_event_entity_created__title____title": "", "@sage/xtrem-workflow/pages__workflow_event_entity_deleted____subtitle": "", "@sage/xtrem-workflow/pages__workflow_event_entity_deleted____title": "", "@sage/xtrem-workflow/pages__workflow_event_entity_deleted__dataEntity____columns__title__name": "", "@sage/xtrem-workflow/pages__workflow_event_entity_deleted__dataEntity____columns__title__package__name": "", "@sage/xtrem-workflow/pages__workflow_event_entity_deleted__dataEntity____columns__title__title": "", "@sage/xtrem-workflow/pages__workflow_event_entity_deleted__dataEntity____title": "", "@sage/xtrem-workflow/pages__workflow_event_entity_deleted__mainSection____title": "", "@sage/xtrem-workflow/pages__workflow_event_entity_deleted__outputVariableName____title": "", "@sage/xtrem-workflow/pages__workflow_event_entity_deleted__title____helperText": "", "@sage/xtrem-workflow/pages__workflow_event_entity_deleted__title____title": "", "@sage/xtrem-workflow/pages__workflow_event_entity_updated____subtitle": "", "@sage/xtrem-workflow/pages__workflow_event_entity_updated____title": "", "@sage/xtrem-workflow/pages__workflow_event_entity_updated__conditionSection____title": "", "@sage/xtrem-workflow/pages__workflow_event_entity_updated__filters____helperText": "", "@sage/xtrem-workflow/pages__workflow_event_entity_updated__filters____title": "", "@sage/xtrem-workflow/pages__workflow_event_entity_updated__mainSection____title": "", "@sage/xtrem-workflow/pages__workflow_event_entity_updated__nodeFactory____columns__title__name": "", "@sage/xtrem-workflow/pages__workflow_event_entity_updated__nodeFactory____columns__title__package__name": "", "@sage/xtrem-workflow/pages__workflow_event_entity_updated__nodeFactory____columns__title__title": "", "@sage/xtrem-workflow/pages__workflow_event_entity_updated__nodeFactory____title": "", "@sage/xtrem-workflow/pages__workflow_event_entity_updated__title____helperText": "", "@sage/xtrem-workflow/pages__workflow_event_entity_updated__title____title": "", "@sage/xtrem-workflow/pages__workflow_event_test_started____subtitle": "", "@sage/xtrem-workflow/pages__workflow_event_test_started____title": "", "@sage/xtrem-workflow/pages__workflow_event_test_started__mainSection____title": "", "@sage/xtrem-workflow/pages__workflow_event_test_started__parameters____addButtonText": "", "@sage/xtrem-workflow/pages__workflow_event_test_started__parameters____columns__columns__node__name__title": "", "@sage/xtrem-workflow/pages__workflow_event_test_started__parameters____columns__columns__node__name__title__2": "", "@sage/xtrem-workflow/pages__workflow_event_test_started__parameters____columns__columns__node__name__title__3": "", "@sage/xtrem-workflow/pages__workflow_event_test_started__parameters____columns__lookupDialogTitle__node__name": "", "@sage/xtrem-workflow/pages__workflow_event_test_started__parameters____columns__placeholder__node__name": "", "@sage/xtrem-workflow/pages__workflow_event_test_started__parameters____columns__title__name": "", "@sage/xtrem-workflow/pages__workflow_event_test_started__parameters____columns__title__node__name": "", "@sage/xtrem-workflow/pages__workflow_event_test_started__parameters____columns__title__type": "", "@sage/xtrem-workflow/pages__workflow_event_test_started__parameters____title": "", "@sage/xtrem-workflow/pages__workflow_event_test_started__parametersSection____title": "", "@sage/xtrem-workflow/pages__workflow_event_test_started__title____helperText": "", "@sage/xtrem-workflow/pages__workflow_event_test_started__title____title": "", "@sage/xtrem-workflow/pages__workflow_mutation_action____subtitle": "", "@sage/xtrem-workflow/pages__workflow_mutation_action____title": "", "@sage/xtrem-workflow/pages__workflow_mutation_action__actionParametersPod____title": "", "@sage/xtrem-workflow/pages__workflow_mutation_action__configDescription____title": "", "@sage/xtrem-workflow/pages__workflow_mutation_action__configRegisterAsSetupNode____helperText": "", "@sage/xtrem-workflow/pages__workflow_mutation_action__configRegisterAsSetupNode____title": "", "@sage/xtrem-workflow/pages__workflow_mutation_action__mainSection____title": "", "@sage/xtrem-workflow/pages__workflow_mutation_action__mutation____columns__title__factory__name": "", "@sage/xtrem-workflow/pages__workflow_mutation_action__mutation____columns__title__name": "", "@sage/xtrem-workflow/pages__workflow_mutation_action__mutation____lookupDialogTitle": "", "@sage/xtrem-workflow/pages__workflow_mutation_action__mutation____title": "", "@sage/xtrem-workflow/pages__workflow_mutation_action__mutationArgumentsPod____title": "", "@sage/xtrem-workflow/pages__workflow_mutation_action__mutationSection____title": "", "@sage/xtrem-workflow/pages__workflow_mutation_action__outputVariableName____title": "", "@sage/xtrem-workflow/pages__workflow_mutation_action__registerActionButton____title": "", "@sage/xtrem-workflow/pages__workflow_mutation_action__registrationSection____title": "", "@sage/xtrem-workflow/pages__workflow_mutation_action__selector____title": "", "@sage/xtrem-workflow/pages__workflow_mutation_action__showAdvancedOptions____helperText": "", "@sage/xtrem-workflow/pages__workflow_mutation_action__showAdvancedOptions____title": "", "@sage/xtrem-workflow/pages__workflow_mutation_action__title____helperText": "", "@sage/xtrem-workflow/pages__workflow_mutation_action__title____title": "", "@sage/xtrem-workflow/pages__workflow_process____navigationPanel__bulkActions__title": "", "@sage/xtrem-workflow/pages__workflow_process____navigationPanel__listItem__duration__postfix": "", "@sage/xtrem-workflow/pages__workflow_process____navigationPanel__listItem__duration__title": "", "@sage/xtrem-workflow/pages__workflow_process____navigationPanel__listItem__errorMessage__title": "", "@sage/xtrem-workflow/pages__workflow_process____navigationPanel__listItem__line2__title": "", "@sage/xtrem-workflow/pages__workflow_process____navigationPanel__listItem__startedAt__title": "", "@sage/xtrem-workflow/pages__workflow_process____navigationPanel__listItem__title__title": "", "@sage/xtrem-workflow/pages__workflow_process____navigationPanel__listItem__titleRight__title": "", "@sage/xtrem-workflow/pages__workflow_process____navigationPanel__listItem__triggeredBy__title": "", "@sage/xtrem-workflow/pages__workflow_process____navigationPanel__optionsMenu__title": "", "@sage/xtrem-workflow/pages__workflow_process____objectTypePlural": "", "@sage/xtrem-workflow/pages__workflow_process____objectTypeSingular": "", "@sage/xtrem-workflow/pages__workflow_process____title": "", "@sage/xtrem-workflow/pages__workflow_process___id____title": "", "@sage/xtrem-workflow/pages__workflow_process__completedAt____title": "", "@sage/xtrem-workflow/pages__workflow_process__designerSection____title": "", "@sage/xtrem-workflow/pages__workflow_process__duration____postfix": "", "@sage/xtrem-workflow/pages__workflow_process__duration____title": "", "@sage/xtrem-workflow/pages__workflow_process__eventLogsSection____title": "", "@sage/xtrem-workflow/pages__workflow_process__flow____title": "", "@sage/xtrem-workflow/pages__workflow_process__goToDesigner____title": "", "@sage/xtrem-workflow/pages__workflow_process__headerSection____title": "", "@sage/xtrem-workflow/pages__workflow_process__olderVersionMessage____content": "", "@sage/xtrem-workflow/pages__workflow_process__startedAt____title": "", "@sage/xtrem-workflow/pages__workflow_process__status____title": "", "@sage/xtrem-workflow/pages__workflow_process__tableLogs____columns__title__event": "", "@sage/xtrem-workflow/pages__workflow_process__tableLogs____columns__title__message": "", "@sage/xtrem-workflow/pages__workflow_process__tableLogs____columns__title__step": "", "@sage/xtrem-workflow/pages__workflow_process__tableLogs____columns__title__timestamp": "", "@sage/xtrem-workflow/pages__workflow_process__tableLogs____title": "", "@sage/xtrem-workflow/pages__workflow_process__triggeringUser____title": "", "@sage/xtrem-workflow/pages__workflow_process__variablesSection____title": "", "@sage/xtrem-workflow/pages__workflow_process__workflow____title": "", "@sage/xtrem-workflow/pages__workflow_process__workflowDefinitionId____title": "", "@sage/xtrem-workflow/pages__workflow_select_variables_dialog____title": "", "@sage/xtrem-workflow/pages__workflow_select_variables_dialog__fromPath____title": "", "@sage/xtrem-workflow/pages__workflow_select_variables_dialog__ok____title": "", "@sage/xtrem-workflow/pages__workflow_select_variables_dialog__selectedProperties____helperText": "", "@sage/xtrem-workflow/pages__workflow_step_template____navigationPanel__listItem__line2__title": "", "@sage/xtrem-workflow/pages__workflow_step_template____navigationPanel__listItem__title__title": "", "@sage/xtrem-workflow/pages__workflow_step_template____objectTypePlural": "", "@sage/xtrem-workflow/pages__workflow_step_template____title": "", "@sage/xtrem-workflow/pages__workflow_step_template__description____title": "", "@sage/xtrem-workflow/pages__workflow_step_template__editActionButton____title": "", "@sage/xtrem-workflow/pages__workflow_step_template__generalSection____title": "", "@sage/xtrem-workflow/pages__workflow_step_template__icon____title": "", "@sage/xtrem-workflow/pages__workflow_step_template__isActive____title": "", "@sage/xtrem-workflow/pages__workflow_step_template__title____title": "", "@sage/xtrem-workflow/permission__manage__name": "", "@sage/xtrem-workflow/permission__read__name": "", "@sage/xtrem-workflow/service_options__workflow__name": "", "@sage/xtrem-workflow/service_options__workflow_advanced__name": "", "@sage/xtrem-workflow/service_options__workflow_option__name": "", "@sage/xtrem-workflow/time-unit-d": "", "@sage/xtrem-workflow/time-unit-h": "", "@sage/xtrem-workflow/time-unit-m": "", "@sage/xtrem-workflow/time-unit-ms": "", "@sage/xtrem-workflow/time-unit-s": "", "@sage/xtrem-workflow/workflow_error_empty_workflow": "", "@sage/xtrem-workflow/workflow_error_no_property_to_update": "", "@sage/xtrem-workflow/workflow_mutation_action_argument_not_found": "", "@sage/xtrem-workflow/workflow_mutation_argument_not_found": "", "@sage/xtrem-workflow/workflow_mutation_argument_type_mismatch": "", "@sage/xtrem-workflow/workflow_unknown_mutation": "", "@sage/xtrem-workflow/workflow-delete-definition-confirm-body": "", "@sage/xtrem-workflow/workflow-delete-definition-confirm-title": "", "@sage/xtrem-workflow/workflow-dialog-title-error": "", "@sage/xtrem-workflow/workflow-entity-created-condition-not-an-array": "", "@sage/xtrem-workflow/workflow-entity-created-default-title": "", "@sage/xtrem-workflow/workflow-entity-created-no-property-selection": "", "@sage/xtrem-workflow/workflow-entity-created-step-variables-not-an-array": "", "@sage/xtrem-workflow/workflow-entity-deleted-default-title": "", "@sage/xtrem-workflow/workflow-entity-updated-condition-not-an-array": "", "@sage/xtrem-workflow/workflow-entity-updated-default-title": "", "@sage/xtrem-workflow/workflow-entity-updated-no-property-selection": "", "@sage/xtrem-workflow/workflow-entity-updated-step-variables-not-an-array": "", "@sage/xtrem-workflow/workflow-error-cannot-unselect-root-property": "", "@sage/xtrem-workflow/workflow-error-event-cant-have-a-parent": "", "@sage/xtrem-workflow/workflow-error-flow-has-cycle": "", "@sage/xtrem-workflow/workflow-error-more-than-one-start-node": "", "@sage/xtrem-workflow/workflow-error-no-description": "", "@sage/xtrem-workflow/workflow-error-no-parent": "", "@sage/xtrem-workflow/workflow-error-no-start-node": "", "@sage/xtrem-workflow/workflow-error-no-title": "", "@sage/xtrem-workflow/workflow-error-start-topics-forbidden": "", "@sage/xtrem-workflow/workflow-error-too-few-outputs": "", "@sage/xtrem-workflow/workflow-error-too-many-outputs": "", "@sage/xtrem-workflow/workflow-send-user-notification-error-no-notification-description": "", "@sage/xtrem-workflow/workflow-send-user-notification-error-no-notification-title": "", "@sage/xtrem-workflow/workflow-variable-not-found": "", "@sage/xtrem-workflow/workflow-variable-not-found-with-suggestion": ""}