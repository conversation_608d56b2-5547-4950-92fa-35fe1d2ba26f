{"@sage/xtrem-workflow/activity__workflow_definition__name": "Workflow-Definition", "@sage/xtrem-workflow/activity__workflow_process__name": "Workflow-Prozess", "@sage/xtrem-workflow/add-properties": "Eigenschaften für die Aktualisierung hinzufügen", "@sage/xtrem-workflow/add-variables": "Variablen hinzufügen", "@sage/xtrem-workflow/data_types__test_decimal_data_type__name": "Datentyp Dezimal Test", "@sage/xtrem-workflow/data_types__test_enum_enum__name": "Enum Test-Enum", "@sage/xtrem-workflow/data_types__test_string_data_type__name": "Datentyp Test-String", "@sage/xtrem-workflow/data_types__workflow_definition_status_enum__name": "Enum Status Workflow-Definition", "@sage/xtrem-workflow/data_types__workflow_mutation_argument_origin_enum__name": "Enum Ursprung Argument Mutation Workflow", "@sage/xtrem-workflow/data_types__workflow_mutation_parameter_type_enum__name": "Enum Parametertyp Mutation Workflow", "@sage/xtrem-workflow/data_types__workflow_process_status_enum__name": "Enum Status Workflow-Prozess", "@sage/xtrem-workflow/enums__test_enum__value1": "Wert 1", "@sage/xtrem-workflow/enums__test_enum__value2": "Wert 2", "@sage/xtrem-workflow/enums__test_enum__value3": "Wert 3", "@sage/xtrem-workflow/enums__workflow_definition_status__production": "Produktiv", "@sage/xtrem-workflow/enums__workflow_definition_status__test": "Test", "@sage/xtrem-workflow/enums__workflow_mutation_argument_origin__fromParameter": "<PERSON>", "@sage/xtrem-workflow/enums__workflow_mutation_argument_origin__fromVariable": "<PERSON>", "@sage/xtrem-workflow/enums__workflow_mutation_argument_origin__manual": "<PERSON><PERSON>", "@sage/xtrem-workflow/enums__workflow_mutation_parameter_type__boolean": "Boolean", "@sage/xtrem-workflow/enums__workflow_mutation_parameter_type__date": "Datum", "@sage/xtrem-workflow/enums__workflow_mutation_parameter_type__decimal": "Dezimal", "@sage/xtrem-workflow/enums__workflow_mutation_parameter_type__enum": "Enum", "@sage/xtrem-workflow/enums__workflow_mutation_parameter_type__integer": "Integer", "@sage/xtrem-workflow/enums__workflow_mutation_parameter_type__other": "Sonstige", "@sage/xtrem-workflow/enums__workflow_mutation_parameter_type__reference": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-workflow/enums__workflow_mutation_parameter_type__string": "String", "@sage/xtrem-workflow/enums__workflow_process_status__cancelled": "Abgebrochen", "@sage/xtrem-workflow/enums__workflow_process_status__error": "<PERSON><PERSON>", "@sage/xtrem-workflow/enums__workflow_process_status__running": "Läuft", "@sage/xtrem-workflow/enums__workflow_process_status__shutDown": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-workflow/enums__workflow_process_status__skipped": "Übersprungen", "@sage/xtrem-workflow/enums__workflow_process_status__success": "Erfolgreich", "@sage/xtrem-workflow/enums__workflow_process_status__suspended": "Ausgesetzt", "@sage/xtrem-workflow/error-no-test-user-in-test-mode": "<PERSON>n der Status den Wert Test hat, müssen Sie einen Testbenutzer angeben.", "@sage/xtrem-workflow/invalid-workflow-is-disabled": "Der Workflow {{id}} hat einen <PERSON> und kann nicht ausgeführt werden. Sie müssen den Fehler beheben, bevor er wieder aktiviert werden kann.", "@sage/xtrem-workflow/menu_item__automation": "Automatisierung", "@sage/xtrem-workflow/menu_item__designer": "Design", "@sage/xtrem-workflow/menu_item__process": "Prozess", "@sage/xtrem-workflow/nodes__test_config_locale_error_message": "Lokalisierte Fehlermeldung.", "@sage/xtrem-workflow/nodes__workflow_definition__asyncMutation__asyncExport": "Export", "@sage/xtrem-workflow/nodes__workflow_definition__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-workflow/nodes__workflow_definition__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-workflow/nodes__workflow_definition__node_name": "Workflow-Definition", "@sage/xtrem-workflow/nodes__workflow_definition__property__diagram": "Diagramm", "@sage/xtrem-workflow/nodes__workflow_definition__property__flow": "Bewegung", "@sage/xtrem-workflow/nodes__workflow_definition__property__id": "ID", "@sage/xtrem-workflow/nodes__workflow_definition__property__isActive": "Ist aktiv", "@sage/xtrem-workflow/nodes__workflow_definition__property__name": "Name", "@sage/xtrem-workflow/nodes__workflow_definition__property__processes": "Prozesse", "@sage/xtrem-workflow/nodes__workflow_definition__property__startTopic": "Start Thema", "@sage/xtrem-workflow/nodes__workflow_definition__property__status": "Status", "@sage/xtrem-workflow/nodes__workflow_definition__property__testUser": "Test-<PERSON><PERSON><PERSON>", "@sage/xtrem-workflow/nodes__workflow_diagram__asyncMutation__asyncExport": "Export", "@sage/xtrem-workflow/nodes__workflow_diagram__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-workflow/nodes__workflow_diagram__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-workflow/nodes__workflow_diagram__mutation__controlDiagram": "Prüfdiagramm", "@sage/xtrem-workflow/nodes__workflow_diagram__mutation__controlDiagram__failed": "Prüfdiagramm fehlgeschlagen.", "@sage/xtrem-workflow/nodes__workflow_diagram__mutation__controlDiagram__parameter__edges": "<PERSON><PERSON><PERSON>", "@sage/xtrem-workflow/nodes__workflow_diagram__mutation__controlDiagram__parameter__nodes": "Nodes", "@sage/xtrem-workflow/nodes__workflow_diagram__node_name": "Workflow-Diagramm", "@sage/xtrem-workflow/nodes__workflow_diagram__property__data": "Daten", "@sage/xtrem-workflow/nodes__workflow_diagram__property__version": "Version", "@sage/xtrem-workflow/nodes__workflow_process__asyncMutation__asyncExport": "Export", "@sage/xtrem-workflow/nodes__workflow_process__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-workflow/nodes__workflow_process__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-workflow/nodes__workflow_process__node_name": "Workflow-Prozess", "@sage/xtrem-workflow/nodes__workflow_process__property__completedAt": "Abgeschlossen um", "@sage/xtrem-workflow/nodes__workflow_process__property__containerId": "Container-ID", "@sage/xtrem-workflow/nodes__workflow_process__property__definition": "Definition", "@sage/xtrem-workflow/nodes__workflow_process__property__diagram": "Diagramm", "@sage/xtrem-workflow/nodes__workflow_process__property__duration": "<PERSON><PERSON>", "@sage/xtrem-workflow/nodes__workflow_process__property__errorMessages": "Fehlermeldungen", "@sage/xtrem-workflow/nodes__workflow_process__property__eventLog": "<PERSON><PERSON>ignisp<PERSON><PERSON>ll", "@sage/xtrem-workflow/nodes__workflow_process__property__id": "ID", "@sage/xtrem-workflow/nodes__workflow_process__property__originId": "Ursprüngliche ID", "@sage/xtrem-workflow/nodes__workflow_process__property__startedAt": "Gestartet um", "@sage/xtrem-workflow/nodes__workflow_process__property__startNotificationId": "Start Benachrichtigungs-ID", "@sage/xtrem-workflow/nodes__workflow_process__property__state": "Status", "@sage/xtrem-workflow/nodes__workflow_process__property__status": "Status", "@sage/xtrem-workflow/nodes__workflow_process__property__triggeringUser": "<PERSON><PERSON><PERSON>l<PERSON>", "@sage/xtrem-workflow/nodes__workflow_process__property__variables": "Variablen", "@sage/xtrem-workflow/nodes__workflow_step_template__asyncMutation__asyncExport": "Export", "@sage/xtrem-workflow/nodes__workflow_step_template__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-workflow/nodes__workflow_step_template__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-workflow/nodes__workflow_step_template__mutation__registerStepConfiguration": "Schrittkonfiguration registrieren", "@sage/xtrem-workflow/nodes__workflow_step_template__mutation__registerStepConfiguration__failed": "Schrittkonfiguration registrieren fehlgeschlagen.", "@sage/xtrem-workflow/nodes__workflow_step_template__mutation__registerStepConfiguration__parameter__asSetupNode": "Als Setup-Node", "@sage/xtrem-workflow/nodes__workflow_step_template__mutation__registerStepConfiguration__parameter__configData": "Konfigurationsdaten", "@sage/xtrem-workflow/nodes__workflow_step_template__mutation__registerStepConfiguration__parameter__factory": "Standard", "@sage/xtrem-workflow/nodes__workflow_step_template__mutation__registerStepConfiguration__parameter__localizedDescription": "Lokalisierte Bezeichnung", "@sage/xtrem-workflow/nodes__workflow_step_template__mutation__registerStepConfiguration__parameter__localizedTitle": "Lokalisierter Titel", "@sage/xtrem-workflow/nodes__workflow_step_template__mutation__registerStepConfiguration__parameter__stepConstructor": "Schritt-Konstruktor", "@sage/xtrem-workflow/nodes__workflow_step_template__node_name": "Vorlage Workflow-Schritt", "@sage/xtrem-workflow/nodes__workflow_step_template__property__color": "Farbe", "@sage/xtrem-workflow/nodes__workflow_step_template__property__configData": "Konfigurationsdaten", "@sage/xtrem-workflow/nodes__workflow_step_template__property__description": "Bezeichnung", "@sage/xtrem-workflow/nodes__workflow_step_template__property__factory": "Standard", "@sage/xtrem-workflow/nodes__workflow_step_template__property__icon": "Symbol", "@sage/xtrem-workflow/nodes__workflow_step_template__property__isActive": "Ist aktiv", "@sage/xtrem-workflow/nodes__workflow_step_template__property__serviceOptions": "Dienstoptionen", "@sage/xtrem-workflow/nodes__workflow_step_template__property__stepConstructor": "Schritt-Konstruktor", "@sage/xtrem-workflow/nodes__workflow_step_template__property__stepDescriptor": "Schritt-Deskriptor", "@sage/xtrem-workflow/nodes__workflow_step_template__property__title": "Titel", "@sage/xtrem-workflow/nodes__workflow_step_template__property__variant": "<PERSON><PERSON><PERSON>", "@sage/xtrem-workflow/package__name": "Workflow", "@sage/xtrem-workflow/pages__workflow_action_calculate____subtitle": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-workflow/pages__workflow_action_calculate____title": "Konfiguration der Aktion", "@sage/xtrem-workflow/pages__workflow_action_calculate__calculationSteps____addButtonText": "Einen Berechnungsschritt hinzufügen", "@sage/xtrem-workflow/pages__workflow_action_calculate__calculationSteps____columns__title__isVariable": "Ist Variable?", "@sage/xtrem-workflow/pages__workflow_action_calculate__calculationSteps____columns__title__operation": "Vorgang", "@sage/xtrem-workflow/pages__workflow_action_calculate__calculationSteps____columns__title__value": "Wert der Komponente", "@sage/xtrem-workflow/pages__workflow_action_calculate__calculationSteps____columns__title__variable": "Variable der Komponente", "@sage/xtrem-workflow/pages__workflow_action_calculate__calculationSteps____title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-workflow/pages__workflow_action_calculate__mainSection____title": "Grundlegende Details", "@sage/xtrem-workflow/pages__workflow_action_calculate__outputVariableName____title": "Output Variablenname", "@sage/xtrem-workflow/pages__workflow_action_calculate__title____helperText": "<PERSON><PERSON><PERSON>, der im Workflow-Diagramm angezeigt wird.", "@sage/xtrem-workflow/pages__workflow_action_calculate__title____title": "Titel der Aktion", "@sage/xtrem-workflow/pages__workflow_action_condition____title": "Konfiguration der Bedingung", "@sage/xtrem-workflow/pages__workflow_action_condition__branchesBlock____title": "Setup-Verzweigungen", "@sage/xtrem-workflow/pages__workflow_action_condition__filters____helperText": "Wenn alle Bedingungen erfüllt sind, wird die Ausführung nach links fortgesetzt. Wenn eine der Bedingungen als falsch bewertet wird, wird die Ausführung nach rechts fortgesetzt.", "@sage/xtrem-workflow/pages__workflow_action_condition__filters____title": "Bedingung", "@sage/xtrem-workflow/pages__workflow_action_condition__ifFalseBranch____title": "\"if false\"-Verzweigung hinzufügen", "@sage/xtrem-workflow/pages__workflow_action_condition__ifTrueBranch____title": "\"if true\"-Verzweigung hinzufügen", "@sage/xtrem-workflow/pages__workflow_action_condition__mainSection____title": "Grundlegende Details", "@sage/xtrem-workflow/pages__workflow_action_condition__title____helperText": "<PERSON><PERSON><PERSON>, der im Workflow-Diagramm angezeigt wird.", "@sage/xtrem-workflow/pages__workflow_action_condition__title____title": "Titel der Bedingung", "@sage/xtrem-workflow/pages__workflow_action_condition__variablesBlock____title": "Definition der Bedingung", "@sage/xtrem-workflow/pages__workflow_action_delete_entity____subtitle": "Eine Entität löschen", "@sage/xtrem-workflow/pages__workflow_action_delete_entity____title": "Konfiguration der Aktion", "@sage/xtrem-workflow/pages__workflow_action_delete_entity__dataEntity____columns__title__name": "Datensatztyp", "@sage/xtrem-workflow/pages__workflow_action_delete_entity__dataEntity____columns__title__package__name": "<PERSON><PERSON>", "@sage/xtrem-workflow/pages__workflow_action_delete_entity__dataEntity____columns__title__title": "Datensatztyp", "@sage/xtrem-workflow/pages__workflow_action_delete_entity__dataEntity____title": "Datensatztyp", "@sage/xtrem-workflow/pages__workflow_action_delete_entity__mainSection____title": "Grundlegende Details", "@sage/xtrem-workflow/pages__workflow_action_delete_entity__recordIdToDelete____title": "Variable Datensatz-ID", "@sage/xtrem-workflow/pages__workflow_action_delete_entity__title____helperText": "<PERSON><PERSON><PERSON>, der im Workflow-Diagramm angezeigt wird.", "@sage/xtrem-workflow/pages__workflow_action_delete_entity__title____title": "Titel der Aktion", "@sage/xtrem-workflow/pages__workflow_action_read_entity____subtitle": "<PERSON>en Date<PERSON>atz lesen", "@sage/xtrem-workflow/pages__workflow_action_read_entity____title": "Konfiguration der Aktion", "@sage/xtrem-workflow/pages__workflow_action_read_entity__failIfNotFound____helperText": "Einen Fehler ausgeben, wenn kein Datensatz gefunden wird.", "@sage/xtrem-workflow/pages__workflow_action_read_entity__failIfNotFound____title": "Fehlschlagen wenn nicht gefunden", "@sage/xtrem-workflow/pages__workflow_action_read_entity__mainSection____title": "Grundlegende Details", "@sage/xtrem-workflow/pages__workflow_action_read_entity__nodeFactory____columns__title__name": "Datensatztyp", "@sage/xtrem-workflow/pages__workflow_action_read_entity__nodeFactory____columns__title__package__name": "<PERSON><PERSON>", "@sage/xtrem-workflow/pages__workflow_action_read_entity__nodeFactory____columns__title__title": "Datensatztyp", "@sage/xtrem-workflow/pages__workflow_action_read_entity__nodeFactory____title": "Datensatztyp", "@sage/xtrem-workflow/pages__workflow_action_read_entity__outputVariableName____title": "Output Variablenname", "@sage/xtrem-workflow/pages__workflow_action_read_entity__recordKey____title": "Datensatz-Schlüssel", "@sage/xtrem-workflow/pages__workflow_action_read_entity__title____helperText": "<PERSON><PERSON><PERSON>, der im Workflow-Diagramm angezeigt wird.", "@sage/xtrem-workflow/pages__workflow_action_read_entity__title____title": "Titel der Aktion", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification____subtitle": "Einen Benutzer benachrichtigen", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification____title": "Konfiguration der Aktion", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__actionsForLocale____columns__title___originalIndex": "Ursprünglicher Index", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__actionsForLocale____columns__title__link": "Verknüpfung", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__actionsForLocale____columns__title__style": "Schaltflächenstil", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__actionsForLocale____columns__title__title": "Titel", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__actionsSection____title": "Aktionen", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__icon____title": "Symbol", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__level____title": "<PERSON><PERSON>", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__mainSection____title": "Allgemein", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__notificationDescription____title": "Bezeichnung der Benachrichtigung", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__notificationTitle____title": "Titel der Benachrichtigung", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__recipients____addButtonText": "Einen Empfänger hinzufügen", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__recipients____columns__columns__user__email__title": "Nachname", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__recipients____columns__columns__user__email__title__2": "<PERSON><PERSON><PERSON>", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__recipients____columns__columns__user__email__title__3": "E-Mail", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__recipients____columns__lookupDialogTitle__user__email": "Benutzer auswählen", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__recipients____columns__placeholder__user__email": "Benutzer auswählen", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__recipients____columns__title__isManuallySet": "<PERSON><PERSON>", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__recipients____columns__title__user": "<PERSON><PERSON><PERSON>", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__recipients____columns__title__user__email": "<PERSON><PERSON><PERSON>", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__recipients____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__recipientsSection____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__shouldDisplayToast____helperText": "Dringende Benachrichtigungen werden als Pop-up-Meldungen angezeigt", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__shouldDisplayToast____title": "Ist dringend?", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__title____helperText": "<PERSON><PERSON><PERSON>, der im Workflow-Diagramm angezeigt wird.", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__title____title": "Titel der Aktion", "@sage/xtrem-workflow/pages__workflow_action_test_stub____subtitle": "Test-Stub", "@sage/xtrem-workflow/pages__workflow_action_test_stub____title": "Konfiguration der Aktion", "@sage/xtrem-workflow/pages__workflow_action_test_stub__mainSection____title": "Grundlegende Details", "@sage/xtrem-workflow/pages__workflow_action_test_stub__outputVariableName____title": "Output Variablenname", "@sage/xtrem-workflow/pages__workflow_action_test_stub__title____helperText": "<PERSON><PERSON><PERSON>, der im Workflow-Diagramm angezeigt wird.", "@sage/xtrem-workflow/pages__workflow_action_test_stub__title____title": "Titel der Aktion", "@sage/xtrem-workflow/pages__workflow_action_update_entity____subtitle": "Eine Entität aktualisieren", "@sage/xtrem-workflow/pages__workflow_action_update_entity____title": "Konfiguration der Aktion", "@sage/xtrem-workflow/pages__workflow_action_update_entity__mainSection____title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-workflow/pages__workflow_action_update_entity__pathToUpdate____title": "Zu aktualisierender Datensatz", "@sage/xtrem-workflow/pages__workflow_action_update_entity__title____helperText": "<PERSON><PERSON><PERSON>, der im Workflow-Diagramm angezeigt wird.", "@sage/xtrem-workflow/pages__workflow_action_update_entity__title____title": "Titel der Aktion", "@sage/xtrem-workflow/pages__workflow_action_update_entity__updateRulesPod____title": "Neue Werte", "@sage/xtrem-workflow/pages__workflow_action_wait____subtitle": "<PERSON><PERSON>", "@sage/xtrem-workflow/pages__workflow_action_wait____title": "Konfiguration der Aktion", "@sage/xtrem-workflow/pages__workflow_action_wait__mainSection____title": "Grundlegende Details", "@sage/xtrem-workflow/pages__workflow_action_wait__quantity____title": "Menge Zeit", "@sage/xtrem-workflow/pages__workflow_action_wait__title____helperText": "<PERSON><PERSON><PERSON>, der im Workflow-Diagramm angezeigt wird.", "@sage/xtrem-workflow/pages__workflow_action_wait__title____title": "Titel der Aktion", "@sage/xtrem-workflow/pages__workflow_action_wait__unit____title": "Einheit", "@sage/xtrem-workflow/pages__workflow_definition____navigationPanel__inlineActions__title__duplicate": "Duplizieren", "@sage/xtrem-workflow/pages__workflow_definition____navigationPanel__listItem__id__title": "ID", "@sage/xtrem-workflow/pages__workflow_definition____navigationPanel__listItem__title__title": "Name", "@sage/xtrem-workflow/pages__workflow_definition____navigationPanel__listItem__titleRight__title": "Aktiv", "@sage/xtrem-workflow/pages__workflow_definition____navigationPanel__listItem__triggerDetails__title": "Details Auslöser", "@sage/xtrem-workflow/pages__workflow_definition____navigationPanel__listItem__triggerType__title": "<PERSON><PERSON>", "@sage/xtrem-workflow/pages__workflow_definition____navigationPanel__optionsMenu__title": "Alle Workflows", "@sage/xtrem-workflow/pages__workflow_definition____objectTypePlural": "Workflows", "@sage/xtrem-workflow/pages__workflow_definition____objectTypeSingular": "Workflow", "@sage/xtrem-workflow/pages__workflow_definition____title": "Workflow-Designer", "@sage/xtrem-workflow/pages__workflow_definition__deleteWorkflowDefinition____title": "Löschen", "@sage/xtrem-workflow/pages__workflow_definition__designerSection____title": "Designer", "@sage/xtrem-workflow/pages__workflow_definition__eventLog____columns__title__event": "", "@sage/xtrem-workflow/pages__workflow_definition__eventLog____columns__title__message": "", "@sage/xtrem-workflow/pages__workflow_definition__eventLog____columns__title__stepId": "", "@sage/xtrem-workflow/pages__workflow_definition__eventLog____columns__title__timestamp": "", "@sage/xtrem-workflow/pages__workflow_definition__eventLog____title": "", "@sage/xtrem-workflow/pages__workflow_definition__executionSection____title": "Protokoll", "@sage/xtrem-workflow/pages__workflow_definition__flow____title": "Workflow", "@sage/xtrem-workflow/pages__workflow_definition__headerSection____title": "Kopfzeile", "@sage/xtrem-workflow/pages__workflow_definition__id____title": "ID", "@sage/xtrem-workflow/pages__workflow_definition__isActive____title": "Aktiv", "@sage/xtrem-workflow/pages__workflow_definition__name____title": "Name", "@sage/xtrem-workflow/pages__workflow_definition__processes____columns__postfix__duration": "Sekunden", "@sage/xtrem-workflow/pages__workflow_definition__processes____columns__title__duration": "<PERSON><PERSON>", "@sage/xtrem-workflow/pages__workflow_definition__processes____columns__title__errorMessages": "<PERSON><PERSON>", "@sage/xtrem-workflow/pages__workflow_definition__processes____columns__title__startedAt": "Startzeit", "@sage/xtrem-workflow/pages__workflow_definition__processes____columns__title__status": "Status", "@sage/xtrem-workflow/pages__workflow_definition__processes____columns__title__triggeringUser__displayName": "Ausgelöst durch", "@sage/xtrem-workflow/pages__workflow_definition__processes____dropdownActions__title": "Details anzeigen", "@sage/xtrem-workflow/pages__workflow_definition__processes____title": "Protokolle", "@sage/xtrem-workflow/pages__workflow_definition__refreshProcesses____title": "Aktualisieren", "@sage/xtrem-workflow/pages__workflow_definition__status____title": "Status", "@sage/xtrem-workflow/pages__workflow_definition__testUser____columns__title__email": "E-Mail", "@sage/xtrem-workflow/pages__workflow_definition__testUser____columns__title__firstName": "<PERSON><PERSON><PERSON>", "@sage/xtrem-workflow/pages__workflow_definition__testUser____columns__title__lastName": "Nachname", "@sage/xtrem-workflow/pages__workflow_definition__testUser____lookupDialogTitle": "Einen Benutzer auswählen", "@sage/xtrem-workflow/pages__workflow_definition__testUser____title": "Test-<PERSON><PERSON><PERSON>", "@sage/xtrem-workflow/pages__workflow_definition__variables____title": "", "@sage/xtrem-workflow/pages__workflow_event_entity_created____subtitle": "Entität erstellt", "@sage/xtrem-workflow/pages__workflow_event_entity_created____title": "Konfiguration Auslöser", "@sage/xtrem-workflow/pages__workflow_event_entity_created__conditionSection____title": "Bedingung", "@sage/xtrem-workflow/pages__workflow_event_entity_created__filters____helperText": "Wenn alle Bedingungen erfüllt sind, wird die Ausführung auf dem Pfad unten fortgesetzt. Wenn eine der Bedingungen als falsch bewertet wird, wird die Ausführung nach rechts fortgesetzt.", "@sage/xtrem-workflow/pages__workflow_event_entity_created__filters____title": "Bedingung", "@sage/xtrem-workflow/pages__workflow_event_entity_created__mainSection____title": "Konfiguration", "@sage/xtrem-workflow/pages__workflow_event_entity_created__nodeFactory____columns__title__name": "Datensatztyp", "@sage/xtrem-workflow/pages__workflow_event_entity_created__nodeFactory____columns__title__package__name": "<PERSON><PERSON>", "@sage/xtrem-workflow/pages__workflow_event_entity_created__nodeFactory____columns__title__title": "Datensatztyp", "@sage/xtrem-workflow/pages__workflow_event_entity_created__nodeFactory____title": "Datensatztyp", "@sage/xtrem-workflow/pages__workflow_event_entity_created__title____helperText": "<PERSON><PERSON><PERSON>, der im Workflow-Diagramm angezeigt wird.", "@sage/xtrem-workflow/pages__workflow_event_entity_created__title____title": "Titel Auslöser", "@sage/xtrem-workflow/pages__workflow_event_entity_deleted____subtitle": "Entität gelöscht", "@sage/xtrem-workflow/pages__workflow_event_entity_deleted____title": "Konfiguration Auslöser", "@sage/xtrem-workflow/pages__workflow_event_entity_deleted__dataEntity____columns__title__name": "Datensatztyp", "@sage/xtrem-workflow/pages__workflow_event_entity_deleted__dataEntity____columns__title__package__name": "<PERSON><PERSON>", "@sage/xtrem-workflow/pages__workflow_event_entity_deleted__dataEntity____columns__title__title": "Datensatztyp", "@sage/xtrem-workflow/pages__workflow_event_entity_deleted__dataEntity____title": "Datensatztyp", "@sage/xtrem-workflow/pages__workflow_event_entity_deleted__mainSection____title": "Grundlegende Details", "@sage/xtrem-workflow/pages__workflow_event_entity_deleted__outputVariableName____title": "Output Variablenname", "@sage/xtrem-workflow/pages__workflow_event_entity_deleted__title____helperText": "<PERSON><PERSON><PERSON>, der im Workflow-Diagramm angezeigt wird.", "@sage/xtrem-workflow/pages__workflow_event_entity_deleted__title____title": "Titel Auslöser", "@sage/xtrem-workflow/pages__workflow_event_entity_updated____subtitle": "Entität aktualisiert", "@sage/xtrem-workflow/pages__workflow_event_entity_updated____title": "Konfiguration Auslöser", "@sage/xtrem-workflow/pages__workflow_event_entity_updated__conditionSection____title": "Bedingung", "@sage/xtrem-workflow/pages__workflow_event_entity_updated__filters____helperText": "Wenn alle Bedingungen erfüllt sind, wird die Ausführung auf dem Pfad unten fortgesetzt. Wenn eine der Bedingungen als falsch bewertet wird, wird die Ausführung nach rechts fortgesetzt.", "@sage/xtrem-workflow/pages__workflow_event_entity_updated__filters____title": "Bedingung", "@sage/xtrem-workflow/pages__workflow_event_entity_updated__mainSection____title": "Konfiguration", "@sage/xtrem-workflow/pages__workflow_event_entity_updated__nodeFactory____columns__title__name": "Datensatztyp", "@sage/xtrem-workflow/pages__workflow_event_entity_updated__nodeFactory____columns__title__package__name": "<PERSON><PERSON>", "@sage/xtrem-workflow/pages__workflow_event_entity_updated__nodeFactory____columns__title__title": "Datensatztyp", "@sage/xtrem-workflow/pages__workflow_event_entity_updated__nodeFactory____title": "Datensatztyp", "@sage/xtrem-workflow/pages__workflow_event_entity_updated__title____helperText": "<PERSON><PERSON><PERSON>, der im Workflow-Diagramm angezeigt wird.", "@sage/xtrem-workflow/pages__workflow_event_entity_updated__title____title": "Titel Auslöser", "@sage/xtrem-workflow/pages__workflow_event_test_started____subtitle": "Test gestartet", "@sage/xtrem-workflow/pages__workflow_event_test_started____title": "Konfiguration Auslöser", "@sage/xtrem-workflow/pages__workflow_event_test_started__mainSection____title": "Grundlegende Details", "@sage/xtrem-workflow/pages__workflow_event_test_started__parameters____addButtonText": "Einen Parameter hinzufügen", "@sage/xtrem-workflow/pages__workflow_event_test_started__parameters____columns__columns__node__name__title": "Datensatztyp", "@sage/xtrem-workflow/pages__workflow_event_test_started__parameters____columns__columns__node__name__title__2": "Datensatztyp", "@sage/xtrem-workflow/pages__workflow_event_test_started__parameters____columns__columns__node__name__title__3": "<PERSON><PERSON>", "@sage/xtrem-workflow/pages__workflow_event_test_started__parameters____columns__lookupDialogTitle__node__name": "Referenzierten Node auswählen", "@sage/xtrem-workflow/pages__workflow_event_test_started__parameters____columns__placeholder__node__name": "Referenzierten Node auswählen", "@sage/xtrem-workflow/pages__workflow_event_test_started__parameters____columns__title__name": "Parametername", "@sage/xtrem-workflow/pages__workflow_event_test_started__parameters____columns__title__node__name": "Node", "@sage/xtrem-workflow/pages__workflow_event_test_started__parameters____columns__title__type": "<PERSON><PERSON>", "@sage/xtrem-workflow/pages__workflow_event_test_started__parameters____title": "Test-Parameter", "@sage/xtrem-workflow/pages__workflow_event_test_started__parametersSection____title": "Parameter", "@sage/xtrem-workflow/pages__workflow_event_test_started__title____helperText": "<PERSON><PERSON><PERSON>, der im Workflow-Diagramm angezeigt wird.", "@sage/xtrem-workflow/pages__workflow_event_test_started__title____title": "Titel Auslöser", "@sage/xtrem-workflow/pages__workflow_mutation_action____subtitle": "Eine graphQL-Mutation ausführen", "@sage/xtrem-workflow/pages__workflow_mutation_action____title": "Konfiguration der Aktion", "@sage/xtrem-workflow/pages__workflow_mutation_action__actionParametersPod____title": "Parameter", "@sage/xtrem-workflow/pages__workflow_mutation_action__configDescription____title": "Bezeichnung", "@sage/xtrem-workflow/pages__workflow_mutation_action__configRegisterAsSetupNode____helperText": "Die Aktion als Setup-Node registrieren", "@sage/xtrem-workflow/pages__workflow_mutation_action__configRegisterAsSetupNode____title": "Setup-Node", "@sage/xtrem-workflow/pages__workflow_mutation_action__mainSection____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-workflow/pages__workflow_mutation_action__mutation____columns__title__factory__name": "Node", "@sage/xtrem-workflow/pages__workflow_mutation_action__mutation____columns__title__name": "Mutation", "@sage/xtrem-workflow/pages__workflow_mutation_action__mutation____lookupDialogTitle": "Eine Mutation auswählen", "@sage/xtrem-workflow/pages__workflow_mutation_action__mutation____title": "Mutation", "@sage/xtrem-workflow/pages__workflow_mutation_action__mutationArgumentsPod____title": "Argumente", "@sage/xtrem-workflow/pages__workflow_mutation_action__mutationSection____title": "Mutation", "@sage/xtrem-workflow/pages__workflow_mutation_action__outputVariableName____title": "Output Variablenname", "@sage/xtrem-workflow/pages__workflow_mutation_action__registerActionButton____title": "Die Aktion registrieren", "@sage/xtrem-workflow/pages__workflow_mutation_action__registrationSection____title": "Registrierung", "@sage/xtrem-workflow/pages__workflow_mutation_action__selector____title": "Auswahl", "@sage/xtrem-workflow/pages__workflow_mutation_action__showAdvancedOptions____helperText": "Die erweiterten Optionen der Aktion anzeigen\n\nACHTUNG: Dies ist für fortgeschrittene Benutzer reservieret", "@sage/xtrem-workflow/pages__workflow_mutation_action__showAdvancedOptions____title": "Erweiterte Optionen einblenden", "@sage/xtrem-workflow/pages__workflow_mutation_action__title____helperText": "<PERSON><PERSON><PERSON>, der im Workflow-Diagramm angezeigt wird.", "@sage/xtrem-workflow/pages__workflow_mutation_action__title____title": "Titel der Aktion", "@sage/xtrem-workflow/pages__workflow_process____navigationPanel__bulkActions__title": "", "@sage/xtrem-workflow/pages__workflow_process____navigationPanel__listItem__duration__postfix": "", "@sage/xtrem-workflow/pages__workflow_process____navigationPanel__listItem__duration__title": "", "@sage/xtrem-workflow/pages__workflow_process____navigationPanel__listItem__errorMessage__title": "", "@sage/xtrem-workflow/pages__workflow_process____navigationPanel__listItem__line2__title": "", "@sage/xtrem-workflow/pages__workflow_process____navigationPanel__listItem__startedAt__title": "", "@sage/xtrem-workflow/pages__workflow_process____navigationPanel__listItem__title__title": "", "@sage/xtrem-workflow/pages__workflow_process____navigationPanel__listItem__titleRight__title": "", "@sage/xtrem-workflow/pages__workflow_process____navigationPanel__listItem__triggeredBy__title": "", "@sage/xtrem-workflow/pages__workflow_process____navigationPanel__optionsMenu__title": "Alle Prozesse", "@sage/xtrem-workflow/pages__workflow_process____objectTypePlural": "Workflow-Protokolle", "@sage/xtrem-workflow/pages__workflow_process____objectTypeSingular": "Workflow-Protokoll", "@sage/xtrem-workflow/pages__workflow_process____title": "Workflow-Protokolle", "@sage/xtrem-workflow/pages__workflow_process___id____title": "", "@sage/xtrem-workflow/pages__workflow_process__completedAt____title": "Endzeit", "@sage/xtrem-workflow/pages__workflow_process__designerSection____title": "", "@sage/xtrem-workflow/pages__workflow_process__duration____postfix": "", "@sage/xtrem-workflow/pages__workflow_process__duration____title": "", "@sage/xtrem-workflow/pages__workflow_process__eventLogsSection____title": "", "@sage/xtrem-workflow/pages__workflow_process__flow____title": "Workflow", "@sage/xtrem-workflow/pages__workflow_process__goToDesigner____title": "", "@sage/xtrem-workflow/pages__workflow_process__headerSection____title": "Kopfzeile", "@sage/xtrem-workflow/pages__workflow_process__olderVersionMessage____content": "", "@sage/xtrem-workflow/pages__workflow_process__startedAt____title": "Startzeit", "@sage/xtrem-workflow/pages__workflow_process__status____title": "Status", "@sage/xtrem-workflow/pages__workflow_process__tableLogs____columns__title__event": "", "@sage/xtrem-workflow/pages__workflow_process__tableLogs____columns__title__message": "", "@sage/xtrem-workflow/pages__workflow_process__tableLogs____columns__title__step": "", "@sage/xtrem-workflow/pages__workflow_process__tableLogs____columns__title__timestamp": "", "@sage/xtrem-workflow/pages__workflow_process__tableLogs____title": "", "@sage/xtrem-workflow/pages__workflow_process__triggeringUser____title": "Ausgelöst durch", "@sage/xtrem-workflow/pages__workflow_process__variablesSection____title": "", "@sage/xtrem-workflow/pages__workflow_process__workflow____title": "Workflow", "@sage/xtrem-workflow/pages__workflow_process__workflowDefinitionId____title": "ID Workflow-Definition", "@sage/xtrem-workflow/pages__workflow_select_variables_dialog____title": "", "@sage/xtrem-workflow/pages__workflow_select_variables_dialog__fromPath____title": "Auswahl aus ...", "@sage/xtrem-workflow/pages__workflow_select_variables_dialog__ok____title": "OK", "@sage/xtrem-workflow/pages__workflow_select_variables_dialog__selectedProperties____helperText": "Nur die ausgewählten Eigenschaften werden aus der Datenbank abgefragt.", "@sage/xtrem-workflow/pages__workflow_step_template____navigationPanel__listItem__line2__title": "Bezeichnung", "@sage/xtrem-workflow/pages__workflow_step_template____navigationPanel__listItem__title__title": "Titel", "@sage/xtrem-workflow/pages__workflow_step_template____objectTypePlural": "Vorlagen Workflow-Schritt", "@sage/xtrem-workflow/pages__workflow_step_template____title": "Vorlage Workflow-Schritt", "@sage/xtrem-workflow/pages__workflow_step_template__description____title": "Bezeichnung", "@sage/xtrem-workflow/pages__workflow_step_template__editActionButton____title": "Die Aktion bearbeiten", "@sage/xtrem-workflow/pages__workflow_step_template__generalSection____title": "Allgemein", "@sage/xtrem-workflow/pages__workflow_step_template__icon____title": "Symbol", "@sage/xtrem-workflow/pages__workflow_step_template__isActive____title": "Aktiv", "@sage/xtrem-workflow/pages__workflow_step_template__title____title": "Titel", "@sage/xtrem-workflow/permission__manage__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-workflow/permission__read__name": "<PERSON><PERSON>", "@sage/xtrem-workflow/service_options__workflow__name": "Workflow", "@sage/xtrem-workflow/service_options__workflow_advanced__name": "Workflow erweitert", "@sage/xtrem-workflow/service_options__workflow_option__name": "Workflow-Option", "@sage/xtrem-workflow/time-unit-d": "Tage", "@sage/xtrem-workflow/time-unit-h": "Stunden", "@sage/xtrem-workflow/time-unit-m": "Minuten", "@sage/xtrem-workflow/time-unit-ms": "Millisekunden", "@sage/xtrem-workflow/time-unit-s": "Sekunden", "@sage/xtrem-workflow/workflow_error_empty_workflow": "<PERSON>e müssen einen Auslöser hinzufügen.", "@sage/xtrem-workflow/workflow_error_no_property_to_update": "Sie müssen eine zu aktualisierende Eigenschaft hinzufügen.", "@sage/xtrem-workflow/workflow_mutation_action_argument_not_found": "Die Aktion bezieht sich auf das Argument {{argumentName}}, das in der Mutation {{mutationName}} nicht existiert.", "@sage/xtrem-workflow/workflow_mutation_argument_not_found": "Das Mutationsargument ist in der Aktionskonfiguration nicht definiert: {{argument}}.", "@sage/xtrem-workflow/workflow_mutation_argument_type_mismatch": "Das Mutationsargument hat eine Typinkongruenz: {{argument}}. <PERSON><PERSON><PERSON><PERSON>: {{expectedType}}, er<PERSON><PERSON>: {{actualType}}.", "@sage/xtrem-workflow/workflow_unknown_mutation": "Die Mutation existiert nicht: {{mutationName}}.", "@sage/xtrem-workflow/workflow-delete-definition-confirm-body": "", "@sage/xtrem-workflow/workflow-delete-definition-confirm-title": "", "@sage/xtrem-workflow/workflow-dialog-title-error": "", "@sage/xtrem-workflow/workflow-entity-created-condition-not-an-array": "conditions ist kein <PERSON>y", "@sage/xtrem-workflow/workflow-entity-created-default-title": "{{factoryName}} erstellt", "@sage/xtrem-workflow/workflow-entity-created-no-property-selection": "Ungültige Ereigniskonfiguration: Auswahl einer Eigenschaft ist erforderlich, wenn ein Filter angegeben ist", "@sage/xtrem-workflow/workflow-entity-created-step-variables-not-an-array": "stepVariables ist kein <PERSON>y", "@sage/xtrem-workflow/workflow-entity-deleted-default-title": "{{factoryName}} gelöscht", "@sage/xtrem-workflow/workflow-entity-updated-condition-not-an-array": "conditions ist kein <PERSON>y", "@sage/xtrem-workflow/workflow-entity-updated-default-title": "{{factoryName}} aktualisiert", "@sage/xtrem-workflow/workflow-entity-updated-no-property-selection": "Ungültige Ereigniskonfiguration: Auswahl einer Eigenschaft ist erforderlich, wenn ein Filter angegeben ist", "@sage/xtrem-workflow/workflow-entity-updated-step-variables-not-an-array": "stepVariables ist kein <PERSON>y", "@sage/xtrem-workflow/workflow-error-cannot-unselect-root-property": "", "@sage/xtrem-workflow/workflow-error-event-cant-have-a-parent": "", "@sage/xtrem-workflow/workflow-error-flow-has-cycle": "", "@sage/xtrem-workflow/workflow-error-more-than-one-start-node": "", "@sage/xtrem-workflow/workflow-error-no-description": "", "@sage/xtrem-workflow/workflow-error-no-parent": "", "@sage/xtrem-workflow/workflow-error-no-start-node": "", "@sage/xtrem-workflow/workflow-error-no-title": "", "@sage/xtrem-workflow/workflow-error-start-topics-forbidden": "", "@sage/xtrem-workflow/workflow-error-too-few-outputs": "", "@sage/xtrem-workflow/workflow-error-too-many-outputs": "", "@sage/xtrem-workflow/workflow-send-user-notification-error-no-notification-description": "Bezeichnung der Benachrichtigung ist erforderlich", "@sage/xtrem-workflow/workflow-send-user-notification-error-no-notification-title": "Titel der Benachrichtigung ist erforderlich", "@sage/xtrem-workflow/workflow-variable-not-found": "Die Variable ist nicht vorhanden: {{variableName}}.", "@sage/xtrem-workflow/workflow-variable-not-found-with-suggestion": "Die Variable ist nicht vorhanden: {{variableName}}. <PERSON><PERSON><PERSON> Si<PERSON>: {{closestVariable}}?"}