{"@sage/xtrem-workflow/activity__workflow_definition__name": "工作流定义", "@sage/xtrem-workflow/activity__workflow_process__name": "工作流进程", "@sage/xtrem-workflow/add-properties": "选择要更新的属性", "@sage/xtrem-workflow/add-variables": "添加变量", "@sage/xtrem-workflow/data_types__test_decimal_data_type__name": "测试小数数据类型", "@sage/xtrem-workflow/data_types__test_enum_enum__name": "测试枚举", "@sage/xtrem-workflow/data_types__test_string_data_type__name": "测试字符串数据类型", "@sage/xtrem-workflow/data_types__workflow_definition_status_enum__name": "工作流定义状态枚举", "@sage/xtrem-workflow/data_types__workflow_mutation_argument_origin_enum__name": "工作流变更参数来源枚举", "@sage/xtrem-workflow/data_types__workflow_mutation_parameter_type_enum__name": "工作流变更参数类型枚举", "@sage/xtrem-workflow/data_types__workflow_process_status_enum__name": "工作流进程状态枚举", "@sage/xtrem-workflow/enums__test_enum__value1": "值1", "@sage/xtrem-workflow/enums__test_enum__value2": "值2", "@sage/xtrem-workflow/enums__test_enum__value3": "值3", "@sage/xtrem-workflow/enums__workflow_definition_status__production": "生产", "@sage/xtrem-workflow/enums__workflow_definition_status__test": "测试", "@sage/xtrem-workflow/enums__workflow_mutation_argument_origin__fromParameter": "自参数", "@sage/xtrem-workflow/enums__workflow_mutation_argument_origin__fromVariable": "自变量", "@sage/xtrem-workflow/enums__workflow_mutation_argument_origin__manual": "手动", "@sage/xtrem-workflow/enums__workflow_mutation_parameter_type__boolean": "布尔型", "@sage/xtrem-workflow/enums__workflow_mutation_parameter_type__date": "日期", "@sage/xtrem-workflow/enums__workflow_mutation_parameter_type__decimal": "小数", "@sage/xtrem-workflow/enums__workflow_mutation_parameter_type__enum": "枚举", "@sage/xtrem-workflow/enums__workflow_mutation_parameter_type__integer": "整数", "@sage/xtrem-workflow/enums__workflow_mutation_parameter_type__other": "其他", "@sage/xtrem-workflow/enums__workflow_mutation_parameter_type__reference": "参考", "@sage/xtrem-workflow/enums__workflow_mutation_parameter_type__string": "字符串", "@sage/xtrem-workflow/enums__workflow_process_status__cancelled": "已取消", "@sage/xtrem-workflow/enums__workflow_process_status__error": "错误", "@sage/xtrem-workflow/enums__workflow_process_status__running": "运行中", "@sage/xtrem-workflow/enums__workflow_process_status__shutDown": "关闭", "@sage/xtrem-workflow/enums__workflow_process_status__skipped": "跳过", "@sage/xtrem-workflow/enums__workflow_process_status__success": "成功", "@sage/xtrem-workflow/enums__workflow_process_status__suspended": "暂停", "@sage/xtrem-workflow/error-no-test-user-in-test-mode": "当状态为测试时，您需要提供一个测试用户。", "@sage/xtrem-workflow/invalid-workflow-is-disabled": "", "@sage/xtrem-workflow/menu_item__automation": "自动", "@sage/xtrem-workflow/menu_item__designer": "设计", "@sage/xtrem-workflow/menu_item__process": "进程", "@sage/xtrem-workflow/nodes__test_config_locale_error_message": "本地化报错消息。", "@sage/xtrem-workflow/nodes__workflow_definition__asyncMutation__asyncExport": "导出", "@sage/xtrem-workflow/nodes__workflow_definition__asyncMutation__asyncExport__parameter__filter": "筛选", "@sage/xtrem-workflow/nodes__workflow_definition__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-workflow/nodes__workflow_definition__node_name": "工作流定义", "@sage/xtrem-workflow/nodes__workflow_definition__property__diagram": "图表", "@sage/xtrem-workflow/nodes__workflow_definition__property__flow": "流程", "@sage/xtrem-workflow/nodes__workflow_definition__property__id": "ID", "@sage/xtrem-workflow/nodes__workflow_definition__property__isActive": "是激活的", "@sage/xtrem-workflow/nodes__workflow_definition__property__name": "名称", "@sage/xtrem-workflow/nodes__workflow_definition__property__processes": "进程", "@sage/xtrem-workflow/nodes__workflow_definition__property__startTopic": "开始主题", "@sage/xtrem-workflow/nodes__workflow_definition__property__status": "状态", "@sage/xtrem-workflow/nodes__workflow_definition__property__testUser": "测试用户", "@sage/xtrem-workflow/nodes__workflow_diagram__asyncMutation__asyncExport": "导出", "@sage/xtrem-workflow/nodes__workflow_diagram__asyncMutation__asyncExport__parameter__filter": "筛选", "@sage/xtrem-workflow/nodes__workflow_diagram__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-workflow/nodes__workflow_diagram__mutation__controlDiagram": "控制图", "@sage/xtrem-workflow/nodes__workflow_diagram__mutation__controlDiagram__failed": "控制图失败。", "@sage/xtrem-workflow/nodes__workflow_diagram__mutation__controlDiagram__parameter__edges": "边线", "@sage/xtrem-workflow/nodes__workflow_diagram__mutation__controlDiagram__parameter__nodes": "节点", "@sage/xtrem-workflow/nodes__workflow_diagram__node_name": "工作流程图", "@sage/xtrem-workflow/nodes__workflow_diagram__property__data": "数据", "@sage/xtrem-workflow/nodes__workflow_diagram__property__version": "版本", "@sage/xtrem-workflow/nodes__workflow_process__asyncMutation__asyncExport": "导出", "@sage/xtrem-workflow/nodes__workflow_process__asyncMutation__asyncExport__parameter__filter": "筛选", "@sage/xtrem-workflow/nodes__workflow_process__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-workflow/nodes__workflow_process__node_name": "工作流进程", "@sage/xtrem-workflow/nodes__workflow_process__property__completedAt": "已完成于", "@sage/xtrem-workflow/nodes__workflow_process__property__containerId": "集装箱ID", "@sage/xtrem-workflow/nodes__workflow_process__property__definition": "定义", "@sage/xtrem-workflow/nodes__workflow_process__property__diagram": "图表", "@sage/xtrem-workflow/nodes__workflow_process__property__duration": "持续时间", "@sage/xtrem-workflow/nodes__workflow_process__property__errorMessages": "报错消息", "@sage/xtrem-workflow/nodes__workflow_process__property__eventLog": "事件日志", "@sage/xtrem-workflow/nodes__workflow_process__property__id": "ID", "@sage/xtrem-workflow/nodes__workflow_process__property__originId": "原始ID", "@sage/xtrem-workflow/nodes__workflow_process__property__startedAt": "开始于", "@sage/xtrem-workflow/nodes__workflow_process__property__startNotificationId": "开始通知ID", "@sage/xtrem-workflow/nodes__workflow_process__property__state": "状态", "@sage/xtrem-workflow/nodes__workflow_process__property__status": "状态", "@sage/xtrem-workflow/nodes__workflow_process__property__triggeringUser": "触发用户", "@sage/xtrem-workflow/nodes__workflow_process__property__variables": "变量", "@sage/xtrem-workflow/nodes__workflow_step_template__asyncMutation__asyncExport": "导出", "@sage/xtrem-workflow/nodes__workflow_step_template__asyncMutation__asyncExport__parameter__filter": "筛选", "@sage/xtrem-workflow/nodes__workflow_step_template__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-workflow/nodes__workflow_step_template__mutation__registerStepConfiguration": "注册步骤配置", "@sage/xtrem-workflow/nodes__workflow_step_template__mutation__registerStepConfiguration__failed": "注册步骤配置失败。", "@sage/xtrem-workflow/nodes__workflow_step_template__mutation__registerStepConfiguration__parameter__asSetupNode": "作为设置节点", "@sage/xtrem-workflow/nodes__workflow_step_template__mutation__registerStepConfiguration__parameter__configData": "配置数据", "@sage/xtrem-workflow/nodes__workflow_step_template__mutation__registerStepConfiguration__parameter__factory": "工厂", "@sage/xtrem-workflow/nodes__workflow_step_template__mutation__registerStepConfiguration__parameter__localizedDescription": "本地化描述", "@sage/xtrem-workflow/nodes__workflow_step_template__mutation__registerStepConfiguration__parameter__localizedTitle": "本地化标题", "@sage/xtrem-workflow/nodes__workflow_step_template__mutation__registerStepConfiguration__parameter__stepConstructor": "步骤构造", "@sage/xtrem-workflow/nodes__workflow_step_template__node_name": "工作流步骤模板", "@sage/xtrem-workflow/nodes__workflow_step_template__property__color": "颜色", "@sage/xtrem-workflow/nodes__workflow_step_template__property__configData": "配置数据", "@sage/xtrem-workflow/nodes__workflow_step_template__property__description": "描述", "@sage/xtrem-workflow/nodes__workflow_step_template__property__factory": "工厂", "@sage/xtrem-workflow/nodes__workflow_step_template__property__icon": "图标", "@sage/xtrem-workflow/nodes__workflow_step_template__property__isActive": "是激活的", "@sage/xtrem-workflow/nodes__workflow_step_template__property__serviceOptions": "服务选项", "@sage/xtrem-workflow/nodes__workflow_step_template__property__stepConstructor": "步骤构造", "@sage/xtrem-workflow/nodes__workflow_step_template__property__stepDescriptor": "步骤描述符", "@sage/xtrem-workflow/nodes__workflow_step_template__property__title": "标题", "@sage/xtrem-workflow/nodes__workflow_step_template__property__variant": "变量", "@sage/xtrem-workflow/package__name": "工作流", "@sage/xtrem-workflow/pages__workflow_action_calculate____subtitle": "计算", "@sage/xtrem-workflow/pages__workflow_action_calculate____title": "操作配置", "@sage/xtrem-workflow/pages__workflow_action_calculate__calculationSteps____addButtonText": "添加计算步骤", "@sage/xtrem-workflow/pages__workflow_action_calculate__calculationSteps____columns__title__isVariable": "是变量吗？", "@sage/xtrem-workflow/pages__workflow_action_calculate__calculationSteps____columns__title__operation": "工序", "@sage/xtrem-workflow/pages__workflow_action_calculate__calculationSteps____columns__title__value": "组件值", "@sage/xtrem-workflow/pages__workflow_action_calculate__calculationSteps____columns__title__variable": "组件变量", "@sage/xtrem-workflow/pages__workflow_action_calculate__calculationSteps____title": "计算", "@sage/xtrem-workflow/pages__workflow_action_calculate__mainSection____title": "基本明细", "@sage/xtrem-workflow/pages__workflow_action_calculate__outputVariableName____title": "输出变量名称", "@sage/xtrem-workflow/pages__workflow_action_calculate__title____helperText": "工作流程图中显示的标题。", "@sage/xtrem-workflow/pages__workflow_action_calculate__title____title": "操作标题", "@sage/xtrem-workflow/pages__workflow_action_condition____title": "条件配置", "@sage/xtrem-workflow/pages__workflow_action_condition__branchesBlock____title": "设置分支", "@sage/xtrem-workflow/pages__workflow_action_condition__filters____helperText": "当所有条件均为真时，继续向左执行。如果任何一个条件被评估为假，继续向右执行。", "@sage/xtrem-workflow/pages__workflow_action_condition__filters____title": "条件", "@sage/xtrem-workflow/pages__workflow_action_condition__ifFalseBranch____title": "添加“如果为假”分支", "@sage/xtrem-workflow/pages__workflow_action_condition__ifTrueBranch____title": "添加“如果为真”分支", "@sage/xtrem-workflow/pages__workflow_action_condition__mainSection____title": "基本明细", "@sage/xtrem-workflow/pages__workflow_action_condition__title____helperText": "工作流程图中显示的标题。", "@sage/xtrem-workflow/pages__workflow_action_condition__title____title": "条件标题", "@sage/xtrem-workflow/pages__workflow_action_condition__variablesBlock____title": "条件定义", "@sage/xtrem-workflow/pages__workflow_action_delete_entity____subtitle": "删除实体", "@sage/xtrem-workflow/pages__workflow_action_delete_entity____title": "操作配置", "@sage/xtrem-workflow/pages__workflow_action_delete_entity__dataEntity____columns__title__name": "记录类型", "@sage/xtrem-workflow/pages__workflow_action_delete_entity__dataEntity____columns__title__package__name": "程序包", "@sage/xtrem-workflow/pages__workflow_action_delete_entity__dataEntity____columns__title__title": "记录类型", "@sage/xtrem-workflow/pages__workflow_action_delete_entity__dataEntity____title": "记录类型", "@sage/xtrem-workflow/pages__workflow_action_delete_entity__mainSection____title": "基本明细", "@sage/xtrem-workflow/pages__workflow_action_delete_entity__recordIdToDelete____title": "记录ID变量", "@sage/xtrem-workflow/pages__workflow_action_delete_entity__title____helperText": "工作流程图中显示的标题。", "@sage/xtrem-workflow/pages__workflow_action_delete_entity__title____title": "操作标题", "@sage/xtrem-workflow/pages__workflow_action_read_entity____subtitle": "读取记录", "@sage/xtrem-workflow/pages__workflow_action_read_entity____title": "操作配置", "@sage/xtrem-workflow/pages__workflow_action_read_entity__failIfNotFound____helperText": "如果未找到记录，则触发错误。", "@sage/xtrem-workflow/pages__workflow_action_read_entity__failIfNotFound____title": "如果未找到，则失败", "@sage/xtrem-workflow/pages__workflow_action_read_entity__mainSection____title": "基本明细", "@sage/xtrem-workflow/pages__workflow_action_read_entity__nodeFactory____columns__title__name": "记录类型", "@sage/xtrem-workflow/pages__workflow_action_read_entity__nodeFactory____columns__title__package__name": "程序包", "@sage/xtrem-workflow/pages__workflow_action_read_entity__nodeFactory____columns__title__title": "记录类型", "@sage/xtrem-workflow/pages__workflow_action_read_entity__nodeFactory____title": "记录类型", "@sage/xtrem-workflow/pages__workflow_action_read_entity__outputVariableName____title": "输出变量名称", "@sage/xtrem-workflow/pages__workflow_action_read_entity__recordKey____title": "记录关键字", "@sage/xtrem-workflow/pages__workflow_action_read_entity__title____helperText": "工作流程图中显示的标题。", "@sage/xtrem-workflow/pages__workflow_action_read_entity__title____title": "操作标题", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification____subtitle": "通知用户", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification____title": "操作配置", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__actionsForLocale____columns__title___originalIndex": "来源索引", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__actionsForLocale____columns__title__link": "链接", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__actionsForLocale____columns__title__style": "按钮样式", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__actionsForLocale____columns__title__title": "标题", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__actionsSection____title": "操作", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__icon____title": "图标", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__level____title": "类型", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__mainSection____title": "常规", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__notificationDescription____title": "通知描述", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__notificationTitle____title": "通知标题", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__recipients____addButtonText": "添加收件人", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__recipients____columns__columns__user__email__title": "姓氏", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__recipients____columns__columns__user__email__title__2": "名字", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__recipients____columns__columns__user__email__title__3": "Email", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__recipients____columns__lookupDialogTitle__user__email": "选择用户", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__recipients____columns__placeholder__user__email": "选择用户", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__recipients____columns__title__isManuallySet": "手动输入", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__recipients____columns__title__user": "用户", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__recipients____columns__title__user__email": "用户", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__recipients____title": "收件人", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__recipientsSection____title": "收件人", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__shouldDisplayToast____helperText": "紧急通知显示为弹窗消息", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__shouldDisplayToast____title": "紧急吗？", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__title____helperText": "工作流程图中显示的标题。", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__title____title": "操作标题", "@sage/xtrem-workflow/pages__workflow_action_test_stub____subtitle": "测试存根", "@sage/xtrem-workflow/pages__workflow_action_test_stub____title": "操作配置", "@sage/xtrem-workflow/pages__workflow_action_test_stub__mainSection____title": "基本明细", "@sage/xtrem-workflow/pages__workflow_action_test_stub__outputVariableName____title": "输出变量名称", "@sage/xtrem-workflow/pages__workflow_action_test_stub__title____helperText": "工作流程图中显示的标题。", "@sage/xtrem-workflow/pages__workflow_action_test_stub__title____title": "操作标题", "@sage/xtrem-workflow/pages__workflow_action_update_entity____subtitle": "更新实体", "@sage/xtrem-workflow/pages__workflow_action_update_entity____title": "操作配置", "@sage/xtrem-workflow/pages__workflow_action_update_entity__mainSection____title": "数据选择", "@sage/xtrem-workflow/pages__workflow_action_update_entity__pathToUpdate____title": "待更新记录", "@sage/xtrem-workflow/pages__workflow_action_update_entity__title____helperText": "工作流程图中显示的标题。", "@sage/xtrem-workflow/pages__workflow_action_update_entity__title____title": "操作标题", "@sage/xtrem-workflow/pages__workflow_action_update_entity__updateRulesPod____title": "新的值", "@sage/xtrem-workflow/pages__workflow_action_wait____subtitle": "等待", "@sage/xtrem-workflow/pages__workflow_action_wait____title": "操作配置", "@sage/xtrem-workflow/pages__workflow_action_wait__mainSection____title": "基本明细", "@sage/xtrem-workflow/pages__workflow_action_wait__quantity____title": "时间量", "@sage/xtrem-workflow/pages__workflow_action_wait__title____helperText": "工作流程图中显示的标题。", "@sage/xtrem-workflow/pages__workflow_action_wait__title____title": "操作标题", "@sage/xtrem-workflow/pages__workflow_action_wait__unit____title": "单位", "@sage/xtrem-workflow/pages__workflow_definition____navigationPanel__inlineActions__title__duplicate": "复制", "@sage/xtrem-workflow/pages__workflow_definition____navigationPanel__listItem__id__title": "ID", "@sage/xtrem-workflow/pages__workflow_definition____navigationPanel__listItem__title__title": "名称", "@sage/xtrem-workflow/pages__workflow_definition____navigationPanel__listItem__titleRight__title": "激活", "@sage/xtrem-workflow/pages__workflow_definition____navigationPanel__listItem__triggerDetails__title": "触发明细", "@sage/xtrem-workflow/pages__workflow_definition____navigationPanel__listItem__triggerType__title": "触发类型", "@sage/xtrem-workflow/pages__workflow_definition____navigationPanel__optionsMenu__title": "所有工作流", "@sage/xtrem-workflow/pages__workflow_definition____objectTypePlural": "工作流", "@sage/xtrem-workflow/pages__workflow_definition____objectTypeSingular": "工作流", "@sage/xtrem-workflow/pages__workflow_definition____title": "工作流设计器", "@sage/xtrem-workflow/pages__workflow_definition__deleteWorkflowDefinition____title": "删除", "@sage/xtrem-workflow/pages__workflow_definition__designerSection____title": "设计器", "@sage/xtrem-workflow/pages__workflow_definition__eventLog____columns__title__event": "", "@sage/xtrem-workflow/pages__workflow_definition__eventLog____columns__title__message": "", "@sage/xtrem-workflow/pages__workflow_definition__eventLog____columns__title__stepId": "", "@sage/xtrem-workflow/pages__workflow_definition__eventLog____columns__title__timestamp": "", "@sage/xtrem-workflow/pages__workflow_definition__eventLog____title": "", "@sage/xtrem-workflow/pages__workflow_definition__executionSection____title": "日志", "@sage/xtrem-workflow/pages__workflow_definition__flow____title": "工作流", "@sage/xtrem-workflow/pages__workflow_definition__headerSection____title": "表头", "@sage/xtrem-workflow/pages__workflow_definition__id____title": "ID", "@sage/xtrem-workflow/pages__workflow_definition__isActive____title": "激活", "@sage/xtrem-workflow/pages__workflow_definition__name____title": "名称", "@sage/xtrem-workflow/pages__workflow_definition__processes____columns__postfix__duration": "秒", "@sage/xtrem-workflow/pages__workflow_definition__processes____columns__title__duration": "持续时间", "@sage/xtrem-workflow/pages__workflow_definition__processes____columns__title__errorMessages": "错误", "@sage/xtrem-workflow/pages__workflow_definition__processes____columns__title__startedAt": "开始时间", "@sage/xtrem-workflow/pages__workflow_definition__processes____columns__title__status": "状态", "@sage/xtrem-workflow/pages__workflow_definition__processes____columns__title__triggeringUser__displayName": "触发于", "@sage/xtrem-workflow/pages__workflow_definition__processes____dropdownActions__title": "查看明细", "@sage/xtrem-workflow/pages__workflow_definition__processes____title": "日志", "@sage/xtrem-workflow/pages__workflow_definition__refreshProcesses____title": "刷新", "@sage/xtrem-workflow/pages__workflow_definition__status____title": "状态", "@sage/xtrem-workflow/pages__workflow_definition__testUser____columns__title__email": "Email", "@sage/xtrem-workflow/pages__workflow_definition__testUser____columns__title__firstName": "名字", "@sage/xtrem-workflow/pages__workflow_definition__testUser____columns__title__lastName": "姓氏", "@sage/xtrem-workflow/pages__workflow_definition__testUser____lookupDialogTitle": "选择一名用户", "@sage/xtrem-workflow/pages__workflow_definition__testUser____title": "测试用户", "@sage/xtrem-workflow/pages__workflow_definition__variables____title": "", "@sage/xtrem-workflow/pages__workflow_event_entity_created____subtitle": "已创建实体", "@sage/xtrem-workflow/pages__workflow_event_entity_created____title": "触发配置", "@sage/xtrem-workflow/pages__workflow_event_entity_created__conditionSection____title": "条件", "@sage/xtrem-workflow/pages__workflow_event_entity_created__filters____helperText": "当所有条件均为真时，继续沿下方路径执行。若任何一个条件被评估为假，则转向右执行。", "@sage/xtrem-workflow/pages__workflow_event_entity_created__filters____title": "条件", "@sage/xtrem-workflow/pages__workflow_event_entity_created__mainSection____title": "配置", "@sage/xtrem-workflow/pages__workflow_event_entity_created__nodeFactory____columns__title__name": "记录类型", "@sage/xtrem-workflow/pages__workflow_event_entity_created__nodeFactory____columns__title__package__name": "程序包", "@sage/xtrem-workflow/pages__workflow_event_entity_created__nodeFactory____columns__title__title": "记录类型", "@sage/xtrem-workflow/pages__workflow_event_entity_created__nodeFactory____title": "记录类型", "@sage/xtrem-workflow/pages__workflow_event_entity_created__title____helperText": "工作流程图中显示的标题。", "@sage/xtrem-workflow/pages__workflow_event_entity_created__title____title": "触发标题", "@sage/xtrem-workflow/pages__workflow_event_entity_deleted____subtitle": "已删除实体", "@sage/xtrem-workflow/pages__workflow_event_entity_deleted____title": "触发配置", "@sage/xtrem-workflow/pages__workflow_event_entity_deleted__dataEntity____columns__title__name": "记录类型", "@sage/xtrem-workflow/pages__workflow_event_entity_deleted__dataEntity____columns__title__package__name": "程序包", "@sage/xtrem-workflow/pages__workflow_event_entity_deleted__dataEntity____columns__title__title": "记录类型", "@sage/xtrem-workflow/pages__workflow_event_entity_deleted__dataEntity____title": "记录类型", "@sage/xtrem-workflow/pages__workflow_event_entity_deleted__mainSection____title": "基本明细", "@sage/xtrem-workflow/pages__workflow_event_entity_deleted__outputVariableName____title": "输出变量名称", "@sage/xtrem-workflow/pages__workflow_event_entity_deleted__title____helperText": "工作流程图中显示的标题。", "@sage/xtrem-workflow/pages__workflow_event_entity_deleted__title____title": "触发标题", "@sage/xtrem-workflow/pages__workflow_event_entity_updated____subtitle": "已更新实体", "@sage/xtrem-workflow/pages__workflow_event_entity_updated____title": "触发配置", "@sage/xtrem-workflow/pages__workflow_event_entity_updated__conditionSection____title": "条件", "@sage/xtrem-workflow/pages__workflow_event_entity_updated__filters____helperText": "当所有条件均为真时，继续沿下方路径执行。若任何一个条件被评估为假，则转向右执行。", "@sage/xtrem-workflow/pages__workflow_event_entity_updated__filters____title": "条件", "@sage/xtrem-workflow/pages__workflow_event_entity_updated__mainSection____title": "配置", "@sage/xtrem-workflow/pages__workflow_event_entity_updated__nodeFactory____columns__title__name": "记录类型", "@sage/xtrem-workflow/pages__workflow_event_entity_updated__nodeFactory____columns__title__package__name": "程序包", "@sage/xtrem-workflow/pages__workflow_event_entity_updated__nodeFactory____columns__title__title": "记录类型", "@sage/xtrem-workflow/pages__workflow_event_entity_updated__nodeFactory____title": "记录类型", "@sage/xtrem-workflow/pages__workflow_event_entity_updated__title____helperText": "工作流程图中显示的标题。", "@sage/xtrem-workflow/pages__workflow_event_entity_updated__title____title": "触发标题", "@sage/xtrem-workflow/pages__workflow_event_test_started____subtitle": "测试开始", "@sage/xtrem-workflow/pages__workflow_event_test_started____title": "触发配置", "@sage/xtrem-workflow/pages__workflow_event_test_started__mainSection____title": "基本明细", "@sage/xtrem-workflow/pages__workflow_event_test_started__parameters____addButtonText": "添加参数", "@sage/xtrem-workflow/pages__workflow_event_test_started__parameters____columns__columns__node__name__title": "记录类型", "@sage/xtrem-workflow/pages__workflow_event_test_started__parameters____columns__columns__node__name__title__2": "记录类型", "@sage/xtrem-workflow/pages__workflow_event_test_started__parameters____columns__columns__node__name__title__3": "程序包", "@sage/xtrem-workflow/pages__workflow_event_test_started__parameters____columns__lookupDialogTitle__node__name": "选择参考节点", "@sage/xtrem-workflow/pages__workflow_event_test_started__parameters____columns__placeholder__node__name": "选择参考节点", "@sage/xtrem-workflow/pages__workflow_event_test_started__parameters____columns__title__name": "参数名称", "@sage/xtrem-workflow/pages__workflow_event_test_started__parameters____columns__title__node__name": "节点", "@sage/xtrem-workflow/pages__workflow_event_test_started__parameters____columns__title__type": "类型", "@sage/xtrem-workflow/pages__workflow_event_test_started__parameters____title": "测试参数", "@sage/xtrem-workflow/pages__workflow_event_test_started__parametersSection____title": "参数", "@sage/xtrem-workflow/pages__workflow_event_test_started__title____helperText": "工作流程图中显示的标题。", "@sage/xtrem-workflow/pages__workflow_event_test_started__title____title": "触发标题", "@sage/xtrem-workflow/pages__workflow_mutation_action____subtitle": "执行graphQL变更", "@sage/xtrem-workflow/pages__workflow_mutation_action____title": "操作配置", "@sage/xtrem-workflow/pages__workflow_mutation_action__actionParametersPod____title": "参数", "@sage/xtrem-workflow/pages__workflow_mutation_action__configDescription____title": "描述", "@sage/xtrem-workflow/pages__workflow_mutation_action__configRegisterAsSetupNode____helperText": "将操作注册为设置节点", "@sage/xtrem-workflow/pages__workflow_mutation_action__configRegisterAsSetupNode____title": "设置节点", "@sage/xtrem-workflow/pages__workflow_mutation_action__mainSection____title": "主要", "@sage/xtrem-workflow/pages__workflow_mutation_action__mutation____columns__title__factory__name": "节点", "@sage/xtrem-workflow/pages__workflow_mutation_action__mutation____columns__title__name": "变更", "@sage/xtrem-workflow/pages__workflow_mutation_action__mutation____lookupDialogTitle": "选择一个变更", "@sage/xtrem-workflow/pages__workflow_mutation_action__mutation____title": "变更", "@sage/xtrem-workflow/pages__workflow_mutation_action__mutationArgumentsPod____title": "参数", "@sage/xtrem-workflow/pages__workflow_mutation_action__mutationSection____title": "变更", "@sage/xtrem-workflow/pages__workflow_mutation_action__outputVariableName____title": "输出变量名称", "@sage/xtrem-workflow/pages__workflow_mutation_action__registerActionButton____title": "注册该操作", "@sage/xtrem-workflow/pages__workflow_mutation_action__registrationSection____title": "注册", "@sage/xtrem-workflow/pages__workflow_mutation_action__selector____title": "选择器", "@sage/xtrem-workflow/pages__workflow_mutation_action__showAdvancedOptions____helperText": "显示操作的高级选项\n\n警告：此功能仅供高级用户使用", "@sage/xtrem-workflow/pages__workflow_mutation_action__showAdvancedOptions____title": "显示高级选项", "@sage/xtrem-workflow/pages__workflow_mutation_action__title____helperText": "工作流程图中显示的标题。", "@sage/xtrem-workflow/pages__workflow_mutation_action__title____title": "操作标题", "@sage/xtrem-workflow/pages__workflow_process____navigationPanel__bulkActions__title": "", "@sage/xtrem-workflow/pages__workflow_process____navigationPanel__listItem__duration__postfix": "", "@sage/xtrem-workflow/pages__workflow_process____navigationPanel__listItem__duration__title": "", "@sage/xtrem-workflow/pages__workflow_process____navigationPanel__listItem__errorMessage__title": "", "@sage/xtrem-workflow/pages__workflow_process____navigationPanel__listItem__line2__title": "", "@sage/xtrem-workflow/pages__workflow_process____navigationPanel__listItem__startedAt__title": "", "@sage/xtrem-workflow/pages__workflow_process____navigationPanel__listItem__title__title": "", "@sage/xtrem-workflow/pages__workflow_process____navigationPanel__listItem__titleRight__title": "", "@sage/xtrem-workflow/pages__workflow_process____navigationPanel__listItem__triggeredBy__title": "", "@sage/xtrem-workflow/pages__workflow_process____navigationPanel__optionsMenu__title": "所有进程", "@sage/xtrem-workflow/pages__workflow_process____objectTypePlural": "工作流日志", "@sage/xtrem-workflow/pages__workflow_process____objectTypeSingular": "工作流日志", "@sage/xtrem-workflow/pages__workflow_process____title": "工作流日志", "@sage/xtrem-workflow/pages__workflow_process___id____title": "", "@sage/xtrem-workflow/pages__workflow_process__completedAt____title": "结束时间", "@sage/xtrem-workflow/pages__workflow_process__designerSection____title": "", "@sage/xtrem-workflow/pages__workflow_process__duration____postfix": "", "@sage/xtrem-workflow/pages__workflow_process__duration____title": "", "@sage/xtrem-workflow/pages__workflow_process__eventLogsSection____title": "", "@sage/xtrem-workflow/pages__workflow_process__flow____title": "工作流", "@sage/xtrem-workflow/pages__workflow_process__goToDesigner____title": "", "@sage/xtrem-workflow/pages__workflow_process__headerSection____title": "表头", "@sage/xtrem-workflow/pages__workflow_process__olderVersionMessage____content": "", "@sage/xtrem-workflow/pages__workflow_process__startedAt____title": "开始时间", "@sage/xtrem-workflow/pages__workflow_process__status____title": "状态", "@sage/xtrem-workflow/pages__workflow_process__tableLogs____columns__title__event": "", "@sage/xtrem-workflow/pages__workflow_process__tableLogs____columns__title__message": "", "@sage/xtrem-workflow/pages__workflow_process__tableLogs____columns__title__step": "", "@sage/xtrem-workflow/pages__workflow_process__tableLogs____columns__title__timestamp": "", "@sage/xtrem-workflow/pages__workflow_process__tableLogs____title": "", "@sage/xtrem-workflow/pages__workflow_process__triggeringUser____title": "触发于", "@sage/xtrem-workflow/pages__workflow_process__variablesSection____title": "", "@sage/xtrem-workflow/pages__workflow_process__workflow____title": "工作流", "@sage/xtrem-workflow/pages__workflow_process__workflowDefinitionId____title": "工作流定义ID", "@sage/xtrem-workflow/pages__workflow_select_variables_dialog____title": "", "@sage/xtrem-workflow/pages__workflow_select_variables_dialog__fromPath____title": "选择自...", "@sage/xtrem-workflow/pages__workflow_select_variables_dialog__ok____title": "确定", "@sage/xtrem-workflow/pages__workflow_select_variables_dialog__selectedProperties____helperText": "仅从数据库中请求所选的属性。", "@sage/xtrem-workflow/pages__workflow_step_template____navigationPanel__listItem__line2__title": "描述", "@sage/xtrem-workflow/pages__workflow_step_template____navigationPanel__listItem__title__title": "标题", "@sage/xtrem-workflow/pages__workflow_step_template____objectTypePlural": "工作流步骤模板", "@sage/xtrem-workflow/pages__workflow_step_template____title": "工作流步骤模板", "@sage/xtrem-workflow/pages__workflow_step_template__description____title": "描述", "@sage/xtrem-workflow/pages__workflow_step_template__editActionButton____title": "编辑操作", "@sage/xtrem-workflow/pages__workflow_step_template__generalSection____title": "常规", "@sage/xtrem-workflow/pages__workflow_step_template__icon____title": "图标", "@sage/xtrem-workflow/pages__workflow_step_template__isActive____title": "激活", "@sage/xtrem-workflow/pages__workflow_step_template__title____title": "标题", "@sage/xtrem-workflow/permission__manage__name": "管理", "@sage/xtrem-workflow/permission__read__name": "读取", "@sage/xtrem-workflow/service_options__workflow__name": "工作流", "@sage/xtrem-workflow/service_options__workflow_advanced__name": "工作流高级", "@sage/xtrem-workflow/service_options__workflow_option__name": "工作流选项", "@sage/xtrem-workflow/time-unit-d": "天", "@sage/xtrem-workflow/time-unit-h": "时", "@sage/xtrem-workflow/time-unit-m": "分", "@sage/xtrem-workflow/time-unit-ms": "毫秒", "@sage/xtrem-workflow/time-unit-s": "秒", "@sage/xtrem-workflow/workflow_error_empty_workflow": "您需要添加触发。", "@sage/xtrem-workflow/workflow_error_no_property_to_update": "您需要添加一个待更新的属性。", "@sage/xtrem-workflow/workflow_mutation_action_argument_not_found": "该操作引用的参数：{{argumentName}}，其不存在于变更：{{mutationName}}。", "@sage/xtrem-workflow/workflow_mutation_argument_not_found": "操作配置中未定义变更参数：{{argument}}。", "@sage/xtrem-workflow/workflow_mutation_argument_type_mismatch": "变更参数的类型不匹配：{{argument}}。期望:{{expectedType}}，实际：{{actualType}}。", "@sage/xtrem-workflow/workflow_unknown_mutation": "变更不存在：{{mutationName}}。", "@sage/xtrem-workflow/workflow-delete-definition-confirm-body": "", "@sage/xtrem-workflow/workflow-delete-definition-confirm-title": "", "@sage/xtrem-workflow/workflow-dialog-title-error": "", "@sage/xtrem-workflow/workflow-entity-created-condition-not-an-array": "条件不是一个数列", "@sage/xtrem-workflow/workflow-entity-created-default-title": "{{factoryName}}已创建", "@sage/xtrem-workflow/workflow-entity-created-no-property-selection": "无效的事件配置：当提供了筛选时，必须选择属性。", "@sage/xtrem-workflow/workflow-entity-created-step-variables-not-an-array": "stepVariables不是一个数列", "@sage/xtrem-workflow/workflow-entity-deleted-default-title": "{{factoryName}}已删除", "@sage/xtrem-workflow/workflow-entity-updated-condition-not-an-array": "条件不是一个数列", "@sage/xtrem-workflow/workflow-entity-updated-default-title": "{{factoryName}}已更新", "@sage/xtrem-workflow/workflow-entity-updated-no-property-selection": "无效的事件配置：当提供了筛选时，必须选择属性", "@sage/xtrem-workflow/workflow-entity-updated-step-variables-not-an-array": "stepVariables不是一个数列", "@sage/xtrem-workflow/workflow-error-cannot-unselect-root-property": "", "@sage/xtrem-workflow/workflow-error-event-cant-have-a-parent": "", "@sage/xtrem-workflow/workflow-error-flow-has-cycle": "", "@sage/xtrem-workflow/workflow-error-more-than-one-start-node": "", "@sage/xtrem-workflow/workflow-error-no-description": "", "@sage/xtrem-workflow/workflow-error-no-parent": "", "@sage/xtrem-workflow/workflow-error-no-start-node": "", "@sage/xtrem-workflow/workflow-error-no-title": "", "@sage/xtrem-workflow/workflow-error-start-topics-forbidden": "", "@sage/xtrem-workflow/workflow-error-too-few-outputs": "", "@sage/xtrem-workflow/workflow-error-too-many-outputs": "", "@sage/xtrem-workflow/workflow-send-user-notification-error-no-notification-description": "通知描述是必填项", "@sage/xtrem-workflow/workflow-send-user-notification-error-no-notification-title": "通知标题是必填项", "@sage/xtrem-workflow/workflow-variable-not-found": "变量不存在：{{variableName}}。", "@sage/xtrem-workflow/workflow-variable-not-found-with-suggestion": "变量不存在：{{variableName}}。您是否想说{{closestVariable}}？"}