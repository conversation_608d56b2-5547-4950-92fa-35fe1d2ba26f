{"@sage/xtrem-workflow/activity__workflow_definition__name": "Définition de workflow", "@sage/xtrem-workflow/activity__workflow_process__name": "Process de workflow", "@sage/xtrem-workflow/add-properties": "Sélectionner des propriétés à mettre à jour", "@sage/xtrem-workflow/add-variables": "Ajouter des variables", "@sage/xtrem-workflow/data_types__test_decimal_data_type__name": "Type de données décimale de test", "@sage/xtrem-workflow/data_types__test_enum_enum__name": "Enum enum test", "@sage/xtrem-workflow/data_types__test_string_data_type__name": "Type de données chaîne de test", "@sage/xtrem-workflow/data_types__workflow_definition_status_enum__name": "Enum de statut de définition de workflow", "@sage/xtrem-workflow/data_types__workflow_mutation_argument_origin_enum__name": "Enum d'originie d'argument de mutation de workflow", "@sage/xtrem-workflow/data_types__workflow_mutation_parameter_type_enum__name": "Enum de type de paramètre de mutation de workflow", "@sage/xtrem-workflow/data_types__workflow_process_status_enum__name": "Enum statut de traitement de workflow", "@sage/xtrem-workflow/enums__test_enum__value1": "Valeur 1", "@sage/xtrem-workflow/enums__test_enum__value2": "Valeur 2", "@sage/xtrem-workflow/enums__test_enum__value3": "Valeur 3", "@sage/xtrem-workflow/enums__workflow_definition_status__production": "Production", "@sage/xtrem-workflow/enums__workflow_definition_status__test": "Test", "@sage/xtrem-workflow/enums__workflow_mutation_argument_origin__fromParameter": "Origine paramètre", "@sage/xtrem-workflow/enums__workflow_mutation_argument_origin__fromVariable": "Origine variable", "@sage/xtrem-workflow/enums__workflow_mutation_argument_origin__manual": "<PERSON><PERSON>", "@sage/xtrem-workflow/enums__workflow_mutation_parameter_type__boolean": "Booléen", "@sage/xtrem-workflow/enums__workflow_mutation_parameter_type__date": "Date", "@sage/xtrem-workflow/enums__workflow_mutation_parameter_type__decimal": "Décimale", "@sage/xtrem-workflow/enums__workflow_mutation_parameter_type__enum": "Enum", "@sage/xtrem-workflow/enums__workflow_mutation_parameter_type__integer": "<PERSON><PERSON>", "@sage/xtrem-workflow/enums__workflow_mutation_parameter_type__other": "Autres", "@sage/xtrem-workflow/enums__workflow_mutation_parameter_type__reference": "Référence", "@sage/xtrem-workflow/enums__workflow_mutation_parameter_type__string": "<PERSON><PERSON><PERSON>", "@sage/xtrem-workflow/enums__workflow_process_status__cancelled": "<PERSON><PERSON><PERSON>", "@sage/xtrem-workflow/enums__workflow_process_status__error": "<PERSON><PERSON><PERSON>", "@sage/xtrem-workflow/enums__workflow_process_status__running": "En cours d'exécution", "@sage/xtrem-workflow/enums__workflow_process_status__shutDown": "Fermeture", "@sage/xtrem-workflow/enums__workflow_process_status__skipped": "Ignoré", "@sage/xtrem-workflow/enums__workflow_process_status__success": "Su<PERSON>ès", "@sage/xtrem-workflow/enums__workflow_process_status__suspended": "Interrompu", "@sage/xtrem-workflow/error-no-test-user-in-test-mode": "Lorsque le statut a la valeur Test, vous devez fournir un utilisateur de test.", "@sage/xtrem-workflow/invalid-workflow-is-disabled": "Le workflow {{id}} a généré une erreur et ne peut pas s'exécuter. V<PERSON> devez résoudre l'erreur avant de pouvoir réactiver le workflow.", "@sage/xtrem-workflow/menu_item__automation": "Automatisation", "@sage/xtrem-workflow/menu_item__designer": "Design", "@sage/xtrem-workflow/menu_item__process": "Traitement", "@sage/xtrem-workflow/nodes__test_config_locale_error_message": "Message d'erreur localisé.", "@sage/xtrem-workflow/nodes__workflow_definition__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-workflow/nodes__workflow_definition__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-workflow/nodes__workflow_definition__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-workflow/nodes__workflow_definition__node_name": "Définition de workflow", "@sage/xtrem-workflow/nodes__workflow_definition__property__diagram": "Diagramme", "@sage/xtrem-workflow/nodes__workflow_definition__property__flow": "Flux", "@sage/xtrem-workflow/nodes__workflow_definition__property__id": "Code", "@sage/xtrem-workflow/nodes__workflow_definition__property__isActive": "Actif", "@sage/xtrem-workflow/nodes__workflow_definition__property__name": "Nom", "@sage/xtrem-workflow/nodes__workflow_definition__property__processes": "Processus", "@sage/xtrem-workflow/nodes__workflow_definition__property__startTopic": "Sujet de départ", "@sage/xtrem-workflow/nodes__workflow_definition__property__status": "Statut", "@sage/xtrem-workflow/nodes__workflow_definition__property__testUser": "Utilisateur test", "@sage/xtrem-workflow/nodes__workflow_diagram__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-workflow/nodes__workflow_diagram__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-workflow/nodes__workflow_diagram__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-workflow/nodes__workflow_diagram__mutation__controlDiagram": "Diagramme de vérification", "@sage/xtrem-workflow/nodes__workflow_diagram__mutation__controlDiagram__failed": "Échec du diagramme de vérification", "@sage/xtrem-workflow/nodes__workflow_diagram__mutation__controlDiagram__parameter__edges": "Bordures", "@sage/xtrem-workflow/nodes__workflow_diagram__mutation__controlDiagram__parameter__nodes": "Nodes", "@sage/xtrem-workflow/nodes__workflow_diagram__node_name": "Diagramme de workflow", "@sage/xtrem-workflow/nodes__workflow_diagram__property__data": "<PERSON><PERSON><PERSON>", "@sage/xtrem-workflow/nodes__workflow_diagram__property__version": "Version", "@sage/xtrem-workflow/nodes__workflow_process__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-workflow/nodes__workflow_process__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-workflow/nodes__workflow_process__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-workflow/nodes__workflow_process__node_name": "Process de workflow", "@sage/xtrem-workflow/nodes__workflow_process__property__completedAt": "Réalisé à", "@sage/xtrem-workflow/nodes__workflow_process__property__containerId": "Code contenant", "@sage/xtrem-workflow/nodes__workflow_process__property__definition": "Définition", "@sage/xtrem-workflow/nodes__workflow_process__property__diagram": "Diagramme", "@sage/xtrem-workflow/nodes__workflow_process__property__duration": "<PERSON><PERSON><PERSON>", "@sage/xtrem-workflow/nodes__workflow_process__property__errorMessages": "Messages d’erreur", "@sage/xtrem-workflow/nodes__workflow_process__property__eventLog": "Trace des événements", "@sage/xtrem-workflow/nodes__workflow_process__property__id": "Code", "@sage/xtrem-workflow/nodes__workflow_process__property__originId": "Code d'origine", "@sage/xtrem-workflow/nodes__workflow_process__property__startedAt": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-workflow/nodes__workflow_process__property__startNotificationId": "Code de notification de début", "@sage/xtrem-workflow/nodes__workflow_process__property__state": "État", "@sage/xtrem-workflow/nodes__workflow_process__property__status": "Statut", "@sage/xtrem-workflow/nodes__workflow_process__property__triggeringUser": "Util<PERSON><PERSON><PERSON>", "@sage/xtrem-workflow/nodes__workflow_process__property__variables": "Variables", "@sage/xtrem-workflow/nodes__workflow_step_template__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-workflow/nodes__workflow_step_template__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-workflow/nodes__workflow_step_template__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-workflow/nodes__workflow_step_template__mutation__registerStepConfiguration": "Enregistrer la configuration d'étape", "@sage/xtrem-workflow/nodes__workflow_step_template__mutation__registerStepConfiguration__failed": "L'enregistrement de la configuration d'étape a échoué.", "@sage/xtrem-workflow/nodes__workflow_step_template__mutation__registerStepConfiguration__parameter__asSetupNode": "Comme node d'installation", "@sage/xtrem-workflow/nodes__workflow_step_template__mutation__registerStepConfiguration__parameter__configData": "Données de configuration", "@sage/xtrem-workflow/nodes__workflow_step_template__mutation__registerStepConfiguration__parameter__factory": "Livré", "@sage/xtrem-workflow/nodes__workflow_step_template__mutation__registerStepConfiguration__parameter__localizedDescription": "Description localisée", "@sage/xtrem-workflow/nodes__workflow_step_template__mutation__registerStepConfiguration__parameter__localizedTitle": "Intitulé localisé", "@sage/xtrem-workflow/nodes__workflow_step_template__mutation__registerStepConfiguration__parameter__stepConstructor": "Constructeur d'étape", "@sage/xtrem-workflow/nodes__workflow_step_template__node_name": "Modèle d'étape de workflow", "@sage/xtrem-workflow/nodes__workflow_step_template__property__color": "<PERSON><PERSON><PERSON>", "@sage/xtrem-workflow/nodes__workflow_step_template__property__configData": "Données de configuration", "@sage/xtrem-workflow/nodes__workflow_step_template__property__description": "Description", "@sage/xtrem-workflow/nodes__workflow_step_template__property__factory": "Livré", "@sage/xtrem-workflow/nodes__workflow_step_template__property__icon": "Icône", "@sage/xtrem-workflow/nodes__workflow_step_template__property__isActive": "Actif", "@sage/xtrem-workflow/nodes__workflow_step_template__property__serviceOptions": "Options de service", "@sage/xtrem-workflow/nodes__workflow_step_template__property__stepConstructor": "Constructeur d'étape", "@sage/xtrem-workflow/nodes__workflow_step_template__property__stepDescriptor": "Descripteur d'é<PERSON>pe", "@sage/xtrem-workflow/nodes__workflow_step_template__property__title": "Intitulé", "@sage/xtrem-workflow/nodes__workflow_step_template__property__variant": "<PERSON><PERSON><PERSON>", "@sage/xtrem-workflow/package__name": "Workflow", "@sage/xtrem-workflow/pages__workflow_action_calculate____subtitle": "Calculer", "@sage/xtrem-workflow/pages__workflow_action_calculate____title": "Configuration de l'action", "@sage/xtrem-workflow/pages__workflow_action_calculate__calculationSteps____addButtonText": "Ajouter une étape de calcul", "@sage/xtrem-workflow/pages__workflow_action_calculate__calculationSteps____columns__title__isVariable": "Variable ?", "@sage/xtrem-workflow/pages__workflow_action_calculate__calculationSteps____columns__title__operation": "Opération", "@sage/xtrem-workflow/pages__workflow_action_calculate__calculationSteps____columns__title__value": "<PERSON><PERSON> <PERSON> composan<PERSON>", "@sage/xtrem-workflow/pages__workflow_action_calculate__calculationSteps____columns__title__variable": "Variable de composant", "@sage/xtrem-workflow/pages__workflow_action_calculate__calculationSteps____title": "Calcul", "@sage/xtrem-workflow/pages__workflow_action_calculate__mainSection____title": "<PERSON>é<PERSON> de base", "@sage/xtrem-workflow/pages__workflow_action_calculate__outputVariableName____title": "Nom de variable de sortie", "@sage/xtrem-workflow/pages__workflow_action_calculate__title____helperText": "Intitulé affiché dans le graphique de workflow.", "@sage/xtrem-workflow/pages__workflow_action_calculate__title____title": "Intitulé d'action", "@sage/xtrem-workflow/pages__workflow_action_condition____title": "Configuration de la condition", "@sage/xtrem-workflow/pages__workflow_action_condition__branchesBlock____title": "Branches d'installation", "@sage/xtrem-workflow/pages__workflow_action_condition__filters____helperText": "Lorsque toutes les conditions sont vérifiées, l'exécution continue à gauche. Si l'une des conditions est considérée comme non vérifiée, l'exécution continue à droite.", "@sage/xtrem-workflow/pages__workflow_action_condition__filters____title": "Condition", "@sage/xtrem-workflow/pages__workflow_action_condition__ifFalseBranch____title": "Ajouter branche \"if false\"", "@sage/xtrem-workflow/pages__workflow_action_condition__ifTrueBranch____title": "Ajouter branche \"if true\"", "@sage/xtrem-workflow/pages__workflow_action_condition__mainSection____title": "<PERSON>é<PERSON> de base", "@sage/xtrem-workflow/pages__workflow_action_condition__title____helperText": "Intitulé affiché dans le graphique de workflow.", "@sage/xtrem-workflow/pages__workflow_action_condition__title____title": "Intitulé de condition", "@sage/xtrem-workflow/pages__workflow_action_condition__variablesBlock____title": "Définition de condition", "@sage/xtrem-workflow/pages__workflow_action_delete_entity____subtitle": "Supprimer une entité", "@sage/xtrem-workflow/pages__workflow_action_delete_entity____title": "Configuration d'action", "@sage/xtrem-workflow/pages__workflow_action_delete_entity__dataEntity____columns__title__name": "Type enregistrement", "@sage/xtrem-workflow/pages__workflow_action_delete_entity__dataEntity____columns__title__package__name": "Package", "@sage/xtrem-workflow/pages__workflow_action_delete_entity__dataEntity____columns__title__title": "Type enregistrement", "@sage/xtrem-workflow/pages__workflow_action_delete_entity__dataEntity____title": "Type enregistrement", "@sage/xtrem-workflow/pages__workflow_action_delete_entity__mainSection____title": "<PERSON>é<PERSON> de base", "@sage/xtrem-workflow/pages__workflow_action_delete_entity__recordIdToDelete____title": "Variable de code d'enregistrement", "@sage/xtrem-workflow/pages__workflow_action_delete_entity__title____helperText": "Intitulé affiché dans le graphique de workflow.", "@sage/xtrem-workflow/pages__workflow_action_delete_entity__title____title": "Intitulé d'action", "@sage/xtrem-workflow/pages__workflow_action_read_entity____subtitle": "Lire un enregistrement", "@sage/xtrem-workflow/pages__workflow_action_read_entity____title": "Configuration d'action", "@sage/xtrem-workflow/pages__workflow_action_read_entity__failIfNotFound____helperText": "<PERSON><PERSON><PERSON><PERSON> une erreur en cas d'absence d'enregistrement.", "@sage/xtrem-workflow/pages__workflow_action_read_entity__failIfNotFound____title": "Échec si introuvable", "@sage/xtrem-workflow/pages__workflow_action_read_entity__mainSection____title": "<PERSON>é<PERSON> de base", "@sage/xtrem-workflow/pages__workflow_action_read_entity__nodeFactory____columns__title__name": "Type enregistrement", "@sage/xtrem-workflow/pages__workflow_action_read_entity__nodeFactory____columns__title__package__name": "Package", "@sage/xtrem-workflow/pages__workflow_action_read_entity__nodeFactory____columns__title__title": "Type enregistrement", "@sage/xtrem-workflow/pages__workflow_action_read_entity__nodeFactory____title": "Type enregistrement", "@sage/xtrem-workflow/pages__workflow_action_read_entity__outputVariableName____title": "Nom variable sortie", "@sage/xtrem-workflow/pages__workflow_action_read_entity__recordKey____title": "Clé d'enregistrement", "@sage/xtrem-workflow/pages__workflow_action_read_entity__title____helperText": "Intitulé affiché dans le graphique de workflow.", "@sage/xtrem-workflow/pages__workflow_action_read_entity__title____title": "Intitulé d'action", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification____subtitle": "Notifier un utilisateur", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification____title": "Configuration d'action", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__actions____columns__helperText__link": "", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__actions____columns__helperText__title": "", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__actions____columns__title__link": "", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__actions____columns__title__style": "", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__actions____columns__title__title": "", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__actionsForLocale____columns__title___originalIndex": "Index d'origine", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__actionsForLocale____columns__title__link": "<PERSON><PERSON>", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__actionsForLocale____columns__title__style": "Style de bouton", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__actionsForLocale____columns__title__title": "Intitulé", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__actionsSection____title": "Actions", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__icon____title": "Icône", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__level____title": "Type", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__mainSection____title": "Général", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__notificationDescription____title": "Description de notification", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__notificationTitle____title": "Intitulé de notification", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__recipients____addButtonText": "Ajouter un destinataire", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__recipients____columns__columns__user__email__title": "Nom de famille", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__recipients____columns__columns__user__email__title__2": "Prénom", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__recipients____columns__columns__user__email__title__3": "E-mail", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__recipients____columns__lookupDialogTitle__user__email": "Sélectionner l'utilisateur", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__recipients____columns__placeholder__user__email": "Sélectionner l'utilisateur", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__recipients____columns__title__isManuallySet": "<PERSON><PERSON><PERSON><PERSON> man<PERSON>", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__recipients____columns__title__user": "Utilisa<PERSON>ur", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__recipients____columns__title__user__email": "Utilisa<PERSON>ur", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__recipients____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__recipientsSection____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__shouldDisplayToast____helperText": "Les notifications urgentes sont affichées sous forme de messages pop-up.", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__shouldDisplayToast____title": "Urgent ?", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__title____helperText": "Intitulé affiché dans le graphique de workflow.", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__title____title": "Intitulé d'action", "@sage/xtrem-workflow/pages__workflow_action_test_stub____subtitle": "Stub de test", "@sage/xtrem-workflow/pages__workflow_action_test_stub____title": "Configuration d'action", "@sage/xtrem-workflow/pages__workflow_action_test_stub__mainSection____title": "<PERSON>é<PERSON> de base", "@sage/xtrem-workflow/pages__workflow_action_test_stub__outputVariableName____title": "Nom variable sortie", "@sage/xtrem-workflow/pages__workflow_action_test_stub__title____helperText": "Intitulé affiché dans le graphique de workflow.", "@sage/xtrem-workflow/pages__workflow_action_test_stub__title____title": "Intitulé d'action", "@sage/xtrem-workflow/pages__workflow_action_update_entity____subtitle": "Mettre à jour une entité", "@sage/xtrem-workflow/pages__workflow_action_update_entity____title": "Configuration d'action", "@sage/xtrem-workflow/pages__workflow_action_update_entity__mainSection____title": "Sélection de données", "@sage/xtrem-workflow/pages__workflow_action_update_entity__pathToUpdate____title": "Enregistrement à mettre à jour", "@sage/xtrem-workflow/pages__workflow_action_update_entity__title____helperText": "Intitulé affiché dans le graphique de workflow.", "@sage/xtrem-workflow/pages__workflow_action_update_entity__title____title": "Intitulé d'action", "@sage/xtrem-workflow/pages__workflow_action_update_entity__updateRulesPod____title": "Nouvelles valeurs", "@sage/xtrem-workflow/pages__workflow_action_wait____subtitle": "<PERSON><PERSON><PERSON>", "@sage/xtrem-workflow/pages__workflow_action_wait____title": "Configuration d'action", "@sage/xtrem-workflow/pages__workflow_action_wait__mainSection____title": "<PERSON>é<PERSON> de base", "@sage/xtrem-workflow/pages__workflow_action_wait__quantity____title": "Quantité de temps", "@sage/xtrem-workflow/pages__workflow_action_wait__title____helperText": "Intitulé affiché dans le graphique de workflow.", "@sage/xtrem-workflow/pages__workflow_action_wait__title____title": "Intitulé d'action", "@sage/xtrem-workflow/pages__workflow_action_wait__unit____title": "Unité", "@sage/xtrem-workflow/pages__workflow_definition____navigationPanel__inlineActions__title__duplicate": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-workflow/pages__workflow_definition____navigationPanel__listItem__id__title": "Code", "@sage/xtrem-workflow/pages__workflow_definition____navigationPanel__listItem__title__title": "Nom", "@sage/xtrem-workflow/pages__workflow_definition____navigationPanel__listItem__titleRight__title": "Actif", "@sage/xtrem-workflow/pages__workflow_definition____navigationPanel__listItem__triggerDetails__title": "<PERSON><PERSON><PERSON> d<PERSON>eurs", "@sage/xtrem-workflow/pages__workflow_definition____navigationPanel__listItem__triggerType__title": "Type de déclencheur", "@sage/xtrem-workflow/pages__workflow_definition____navigationPanel__optionsMenu__title": "Tous les workflows", "@sage/xtrem-workflow/pages__workflow_definition____objectTypePlural": "Workflows", "@sage/xtrem-workflow/pages__workflow_definition____objectTypeSingular": "Workflow", "@sage/xtrem-workflow/pages__workflow_definition____title": "Designer de workflow", "@sage/xtrem-workflow/pages__workflow_definition__deleteWorkflowDefinition____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-workflow/pages__workflow_definition__designerSection____title": "Designer", "@sage/xtrem-workflow/pages__workflow_definition__eventLog____columns__title__event": "", "@sage/xtrem-workflow/pages__workflow_definition__eventLog____columns__title__message": "", "@sage/xtrem-workflow/pages__workflow_definition__eventLog____columns__title__stepId": "", "@sage/xtrem-workflow/pages__workflow_definition__eventLog____columns__title__timestamp": "", "@sage/xtrem-workflow/pages__workflow_definition__eventLog____title": "", "@sage/xtrem-workflow/pages__workflow_definition__executionSection____title": "Trace", "@sage/xtrem-workflow/pages__workflow_definition__flow____title": "Workflow", "@sage/xtrem-workflow/pages__workflow_definition__headerSection____title": "<PERSON>-tête", "@sage/xtrem-workflow/pages__workflow_definition__id____title": "Code", "@sage/xtrem-workflow/pages__workflow_definition__isActive____title": "Actif", "@sage/xtrem-workflow/pages__workflow_definition__name____title": "Nom", "@sage/xtrem-workflow/pages__workflow_definition__processes____columns__postfix__duration": "secondes", "@sage/xtrem-workflow/pages__workflow_definition__processes____columns__title__duration": "<PERSON><PERSON><PERSON>", "@sage/xtrem-workflow/pages__workflow_definition__processes____columns__title__errorMessages": "<PERSON><PERSON><PERSON>", "@sage/xtrem-workflow/pages__workflow_definition__processes____columns__title__startedAt": "<PERSON><PERSON> début", "@sage/xtrem-workflow/pages__workflow_definition__processes____columns__title__status": "Statut", "@sage/xtrem-workflow/pages__workflow_definition__processes____columns__title__triggeringUser__displayName": "Déclenché par", "@sage/xtrem-workflow/pages__workflow_definition__processes____dropdownActions__title": "Aff<PERSON>r les détails", "@sage/xtrem-workflow/pages__workflow_definition__processes____title": "Traces", "@sage/xtrem-workflow/pages__workflow_definition__refreshProcesses____title": "Actualiser", "@sage/xtrem-workflow/pages__workflow_definition__status____title": "Statut", "@sage/xtrem-workflow/pages__workflow_definition__testUser____columns__title__email": "E-mail", "@sage/xtrem-workflow/pages__workflow_definition__testUser____columns__title__firstName": "Prénom", "@sage/xtrem-workflow/pages__workflow_definition__testUser____columns__title__lastName": "Nom de famille", "@sage/xtrem-workflow/pages__workflow_definition__testUser____lookupDialogTitle": "Sélectionner l'utilisateur", "@sage/xtrem-workflow/pages__workflow_definition__testUser____title": "Utilisateur test", "@sage/xtrem-workflow/pages__workflow_definition__variables____title": "", "@sage/xtrem-workflow/pages__workflow_event_entity_created____subtitle": "Entité cré<PERSON>", "@sage/xtrem-workflow/pages__workflow_event_entity_created____title": "Configuration de déclenchement", "@sage/xtrem-workflow/pages__workflow_event_entity_created__conditionSection____title": "Condition", "@sage/xtrem-workflow/pages__workflow_event_entity_created__filters____helperText": "Lorsque toutes les conditions sont vérifiées, l'exécution continue sur le chemin ci-dessous. Si l'une des conditions est considérée comme non vérifiée, l'exécution continue à droite.", "@sage/xtrem-workflow/pages__workflow_event_entity_created__filters____title": "Condition", "@sage/xtrem-workflow/pages__workflow_event_entity_created__mainSection____title": "Configuration", "@sage/xtrem-workflow/pages__workflow_event_entity_created__nodeFactory____columns__title__name": "Type enregistrement", "@sage/xtrem-workflow/pages__workflow_event_entity_created__nodeFactory____columns__title__package__name": "Package", "@sage/xtrem-workflow/pages__workflow_event_entity_created__nodeFactory____columns__title__title": "Type enregistrement", "@sage/xtrem-workflow/pages__workflow_event_entity_created__nodeFactory____title": "Type enregistrement", "@sage/xtrem-workflow/pages__workflow_event_entity_created__title____helperText": "Intitulé affiché dans le graphique de workflow.", "@sage/xtrem-workflow/pages__workflow_event_entity_created__title____title": "Intitulé de déclencheur", "@sage/xtrem-workflow/pages__workflow_event_entity_deleted____subtitle": "Entité supprimée", "@sage/xtrem-workflow/pages__workflow_event_entity_deleted____title": "Configuration de déclenchement", "@sage/xtrem-workflow/pages__workflow_event_entity_deleted__dataEntity____columns__title__name": "Type enregistrement", "@sage/xtrem-workflow/pages__workflow_event_entity_deleted__dataEntity____columns__title__package__name": "Package", "@sage/xtrem-workflow/pages__workflow_event_entity_deleted__dataEntity____columns__title__title": "Type enregistrement", "@sage/xtrem-workflow/pages__workflow_event_entity_deleted__dataEntity____title": "Type enregistrement", "@sage/xtrem-workflow/pages__workflow_event_entity_deleted__mainSection____title": "<PERSON>é<PERSON> de base", "@sage/xtrem-workflow/pages__workflow_event_entity_deleted__outputVariableName____title": "Nom variable sortie", "@sage/xtrem-workflow/pages__workflow_event_entity_deleted__title____helperText": "Intitulé affiché dans le graphique de workflow.", "@sage/xtrem-workflow/pages__workflow_event_entity_deleted__title____title": "Intitulé de déclencheur", "@sage/xtrem-workflow/pages__workflow_event_entity_updated____subtitle": "Entité mise à jour", "@sage/xtrem-workflow/pages__workflow_event_entity_updated____title": "Configuration de déclenchement", "@sage/xtrem-workflow/pages__workflow_event_entity_updated__conditionSection____title": "Condition", "@sage/xtrem-workflow/pages__workflow_event_entity_updated__filters____helperText": "Lorsque toutes les conditions sont vérifiées, l'exécution continue sur le chemin ci-dessous. Si l'une des conditions est considérée comme non vérifiée, l'exécution continue à droite.", "@sage/xtrem-workflow/pages__workflow_event_entity_updated__filters____title": "Condition", "@sage/xtrem-workflow/pages__workflow_event_entity_updated__mainSection____title": "Configuration", "@sage/xtrem-workflow/pages__workflow_event_entity_updated__nodeFactory____columns__title__name": "Type enregistrement", "@sage/xtrem-workflow/pages__workflow_event_entity_updated__nodeFactory____columns__title__package__name": "Package", "@sage/xtrem-workflow/pages__workflow_event_entity_updated__nodeFactory____columns__title__title": "Type enregistrement", "@sage/xtrem-workflow/pages__workflow_event_entity_updated__nodeFactory____title": "Type enregistrement", "@sage/xtrem-workflow/pages__workflow_event_entity_updated__title____helperText": "Intitulé affiché dans le graphique de workflow.", "@sage/xtrem-workflow/pages__workflow_event_entity_updated__title____title": "Intitulé de déclencheur", "@sage/xtrem-workflow/pages__workflow_event_test_started____subtitle": "Test démarré", "@sage/xtrem-workflow/pages__workflow_event_test_started____title": "Configuration de déclenchement", "@sage/xtrem-workflow/pages__workflow_event_test_started__mainSection____title": "<PERSON>é<PERSON> de base", "@sage/xtrem-workflow/pages__workflow_event_test_started__parameters____addButtonText": "Ajouter un paramètre", "@sage/xtrem-workflow/pages__workflow_event_test_started__parameters____columns__columns__node__name__title": "Type enregistrement", "@sage/xtrem-workflow/pages__workflow_event_test_started__parameters____columns__columns__node__name__title__2": "Type enregistrement", "@sage/xtrem-workflow/pages__workflow_event_test_started__parameters____columns__columns__node__name__title__3": "Package", "@sage/xtrem-workflow/pages__workflow_event_test_started__parameters____columns__lookupDialogTitle__node__name": "Sélectionner le node référencé", "@sage/xtrem-workflow/pages__workflow_event_test_started__parameters____columns__placeholder__node__name": "Sélectionner le node référencé", "@sage/xtrem-workflow/pages__workflow_event_test_started__parameters____columns__title__name": "Nom du paramètre", "@sage/xtrem-workflow/pages__workflow_event_test_started__parameters____columns__title__node__name": "Node", "@sage/xtrem-workflow/pages__workflow_event_test_started__parameters____columns__title__type": "Type", "@sage/xtrem-workflow/pages__workflow_event_test_started__parameters____title": "Paramètre de test", "@sage/xtrem-workflow/pages__workflow_event_test_started__parametersSection____title": "Paramètres", "@sage/xtrem-workflow/pages__workflow_event_test_started__title____helperText": "Intitulé affiché dans le graphique de workflow.", "@sage/xtrem-workflow/pages__workflow_event_test_started__title____title": "Intitulé de déclencheur", "@sage/xtrem-workflow/pages__workflow_mutation_action____subtitle": "Exécuter une mutation graphQL", "@sage/xtrem-workflow/pages__workflow_mutation_action____title": "Configuration d'action", "@sage/xtrem-workflow/pages__workflow_mutation_action__actionParametersPod____title": "Parameters", "@sage/xtrem-workflow/pages__workflow_mutation_action__configDescription____title": "Description", "@sage/xtrem-workflow/pages__workflow_mutation_action__configRegisterAsSetupNode____helperText": "Enregistrer l'action en tant que node d'installation", "@sage/xtrem-workflow/pages__workflow_mutation_action__configRegisterAsSetupNode____title": "Node d'installation", "@sage/xtrem-workflow/pages__workflow_mutation_action__mainSection____title": "<PERSON><PERSON>", "@sage/xtrem-workflow/pages__workflow_mutation_action__mutation____columns__title__factory__name": "Node", "@sage/xtrem-workflow/pages__workflow_mutation_action__mutation____columns__title__name": "Mutation", "@sage/xtrem-workflow/pages__workflow_mutation_action__mutation____lookupDialogTitle": "Sélectionner une mutation", "@sage/xtrem-workflow/pages__workflow_mutation_action__mutation____title": "Mutation", "@sage/xtrem-workflow/pages__workflow_mutation_action__mutationArgumentsPod____title": "Arguments", "@sage/xtrem-workflow/pages__workflow_mutation_action__mutationSection____title": "Mutation", "@sage/xtrem-workflow/pages__workflow_mutation_action__outputVariableName____title": "Nom variable sortie", "@sage/xtrem-workflow/pages__workflow_mutation_action__registerActionButton____title": "Enregistrer l'action", "@sage/xtrem-workflow/pages__workflow_mutation_action__registrationSection____title": "Enregistrement", "@sage/xtrem-workflow/pages__workflow_mutation_action__selector____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-workflow/pages__workflow_mutation_action__showAdvancedOptions____helperText": "Affiche les options avancées de cette action.\n\nATTENTION : ceci est réservé aux utilisateurs avancés.", "@sage/xtrem-workflow/pages__workflow_mutation_action__showAdvancedOptions____title": "Afficher la sélection avancée", "@sage/xtrem-workflow/pages__workflow_mutation_action__title____helperText": "Intitulé affiché dans le graphique de workflow.", "@sage/xtrem-workflow/pages__workflow_mutation_action__title____title": "Intitulé d'action", "@sage/xtrem-workflow/pages__workflow_process____navigationPanel__bulkActions__title": "", "@sage/xtrem-workflow/pages__workflow_process____navigationPanel__listItem__duration__postfix": "", "@sage/xtrem-workflow/pages__workflow_process____navigationPanel__listItem__duration__title": "", "@sage/xtrem-workflow/pages__workflow_process____navigationPanel__listItem__errorMessage__title": "", "@sage/xtrem-workflow/pages__workflow_process____navigationPanel__listItem__line2__title": "", "@sage/xtrem-workflow/pages__workflow_process____navigationPanel__listItem__startedAt__title": "", "@sage/xtrem-workflow/pages__workflow_process____navigationPanel__listItem__title__title": "", "@sage/xtrem-workflow/pages__workflow_process____navigationPanel__listItem__titleRight__title": "", "@sage/xtrem-workflow/pages__workflow_process____navigationPanel__listItem__triggeredBy__title": "", "@sage/xtrem-workflow/pages__workflow_process____navigationPanel__optionsMenu__title": "Tous les processus", "@sage/xtrem-workflow/pages__workflow_process____objectTypePlural": "Traces de workflow", "@sage/xtrem-workflow/pages__workflow_process____objectTypeSingular": "Trace de workflow", "@sage/xtrem-workflow/pages__workflow_process____title": "Traces de workflow", "@sage/xtrem-workflow/pages__workflow_process___id____title": "", "@sage/xtrem-workflow/pages__workflow_process__completedAt____title": "Heure de fin", "@sage/xtrem-workflow/pages__workflow_process__designerSection____title": "", "@sage/xtrem-workflow/pages__workflow_process__duration____postfix": "", "@sage/xtrem-workflow/pages__workflow_process__duration____title": "", "@sage/xtrem-workflow/pages__workflow_process__eventLogsSection____title": "", "@sage/xtrem-workflow/pages__workflow_process__flow____title": "Workflow", "@sage/xtrem-workflow/pages__workflow_process__goToDesigner____title": "", "@sage/xtrem-workflow/pages__workflow_process__headerSection____title": "<PERSON>-tête", "@sage/xtrem-workflow/pages__workflow_process__olderVersionMessage____content": "", "@sage/xtrem-workflow/pages__workflow_process__startedAt____title": "<PERSON><PERSON> d<PERSON>", "@sage/xtrem-workflow/pages__workflow_process__status____title": "Statut", "@sage/xtrem-workflow/pages__workflow_process__tableLogs____columns__title__event": "", "@sage/xtrem-workflow/pages__workflow_process__tableLogs____columns__title__message": "", "@sage/xtrem-workflow/pages__workflow_process__tableLogs____columns__title__step": "", "@sage/xtrem-workflow/pages__workflow_process__tableLogs____columns__title__timestamp": "", "@sage/xtrem-workflow/pages__workflow_process__tableLogs____title": "", "@sage/xtrem-workflow/pages__workflow_process__triggeringUser____title": "Déclenché par", "@sage/xtrem-workflow/pages__workflow_process__variablesSection____title": "", "@sage/xtrem-workflow/pages__workflow_process__workflow____title": "Workflow", "@sage/xtrem-workflow/pages__workflow_process__workflowDefinitionId____title": "Code de définition de workflow", "@sage/xtrem-workflow/pages__workflow_select_variables_dialog____title": "", "@sage/xtrem-workflow/pages__workflow_select_variables_dialog__fromPath____title": "Sélectionner à partir de ...", "@sage/xtrem-workflow/pages__workflow_select_variables_dialog__ok____title": "OK", "@sage/xtrem-workflow/pages__workflow_select_variables_dialog__selectedProperties____helperText": "Seules les propriétés sélectionnées sont demandées par la base de données.", "@sage/xtrem-workflow/pages__workflow_step_template____navigationPanel__listItem__line2__title": "Description", "@sage/xtrem-workflow/pages__workflow_step_template____navigationPanel__listItem__title__title": "Intitulé", "@sage/xtrem-workflow/pages__workflow_step_template____objectTypePlural": "Modèles d'étape de workflow", "@sage/xtrem-workflow/pages__workflow_step_template____title": "Modèle d'étape de workflow", "@sage/xtrem-workflow/pages__workflow_step_template__description____title": "Description", "@sage/xtrem-workflow/pages__workflow_step_template__editActionButton____title": "Modifier l'action", "@sage/xtrem-workflow/pages__workflow_step_template__generalSection____title": "Général", "@sage/xtrem-workflow/pages__workflow_step_template__icon____title": "Icône", "@sage/xtrem-workflow/pages__workflow_step_template__isActive____title": "Actif", "@sage/xtrem-workflow/pages__workflow_step_template__title____title": "Intitulé", "@sage/xtrem-workflow/permission__manage__name": "Gestion", "@sage/xtrem-workflow/permission__read__name": "Lecture", "@sage/xtrem-workflow/service_options__workflow__name": "Workflow", "@sage/xtrem-workflow/service_options__workflow_advanced__name": "Workflow avancé", "@sage/xtrem-workflow/service_options__workflow_option__name": "Option de workflow", "@sage/xtrem-workflow/time-unit-d": "Jours", "@sage/xtrem-workflow/time-unit-h": "<PERSON><PERSON>", "@sage/xtrem-workflow/time-unit-m": "Minutes", "@sage/xtrem-workflow/time-unit-ms": "Millisecondes", "@sage/xtrem-workflow/time-unit-s": "Secondes", "@sage/xtrem-workflow/workflow_error_empty_workflow": "<PERSON><PERSON> devez ajouter un déclencheur.", "@sage/xtrem-workflow/workflow_error_no_property_to_update": "<PERSON><PERSON> de<PERSON> ajouter une propriété à mettre à jour.", "@sage/xtrem-workflow/workflow_mutation_action_argument_not_found": "L'action se rapporte à l'argument : {{argumentName}}. <PERSON> dernier n'appartient pas à la mutation : {{mutationName}}.", "@sage/xtrem-workflow/workflow_mutation_argument_not_found": "L'argument de mutation n'est pas défini dans la configuration de l'action : {{argument}}", "@sage/xtrem-workflow/workflow_mutation_argument_type_mismatch": "L'argument de mutation présente une différence de type : {{argument}}. Attendu : {{expectedType}}, obtenu : {{actualType}}.", "@sage/xtrem-workflow/workflow_unknown_mutation": "La mutation n'existe pas : {{mutationName}}.", "@sage/xtrem-workflow/workflow-delete-definition-confirm-body": "", "@sage/xtrem-workflow/workflow-delete-definition-confirm-title": "", "@sage/xtrem-workflow/workflow-dialog-title-error": "", "@sage/xtrem-workflow/workflow-entity-created-condition-not-an-array": "conditions n'est pas un tableau", "@sage/xtrem-workflow/workflow-entity-created-default-title": "Création : {{factoryName}}", "@sage/xtrem-workflow/workflow-entity-created-no-property-selection": "Configuration d'événement invalide : la sélection de propriété est nécessaire lorsqu'un filtre est fourni.", "@sage/xtrem-workflow/workflow-entity-created-step-variables-not-an-array": "stepVariables n'est pas un tableau", "@sage/xtrem-workflow/workflow-entity-deleted-default-title": "Suppression : {{factoryName}}", "@sage/xtrem-workflow/workflow-entity-updated-condition-not-an-array": "conditions n'est pas un tableau", "@sage/xtrem-workflow/workflow-entity-updated-default-title": "Mise à jour : {{factoryName}}", "@sage/xtrem-workflow/workflow-entity-updated-no-property-selection": "Configuration d'événement invalide : la sélection de propriété est nécessaire lorsqu'un filtre est fourni", "@sage/xtrem-workflow/workflow-entity-updated-step-variables-not-an-array": "stepVariables n'est pas un tableau", "@sage/xtrem-workflow/workflow-error-cannot-unselect-root-property": "", "@sage/xtrem-workflow/workflow-error-event-cant-have-a-parent": "", "@sage/xtrem-workflow/workflow-error-flow-has-cycle": "", "@sage/xtrem-workflow/workflow-error-more-than-one-start-node": "", "@sage/xtrem-workflow/workflow-error-no-description": "", "@sage/xtrem-workflow/workflow-error-no-parent": "", "@sage/xtrem-workflow/workflow-error-no-start-node": "", "@sage/xtrem-workflow/workflow-error-no-title": "", "@sage/xtrem-workflow/workflow-error-start-topics-forbidden": "", "@sage/xtrem-workflow/workflow-error-too-few-outputs": "", "@sage/xtrem-workflow/workflow-error-too-many-outputs": "", "@sage/xtrem-workflow/workflow-send-user-notification-error-no-notification-description": "Une description de notification est demandée.", "@sage/xtrem-workflow/workflow-send-user-notification-error-no-notification-title": "Un intitulé de notification est demandé.", "@sage/xtrem-workflow/workflow-variable-not-found": "L variable n'existe pas : {{variableName}}.", "@sage/xtrem-workflow/workflow-variable-not-found-with-suggestion": "La variable n'existe pas : {{variableName}}. Faites-vous référence à : {{closestVariable}} ?"}