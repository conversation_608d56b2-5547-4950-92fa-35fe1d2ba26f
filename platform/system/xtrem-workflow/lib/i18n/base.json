{"@sage/xtrem-workflow/activity__workflow_definition__name": "Workflow definition", "@sage/xtrem-workflow/activity__workflow_process__name": "Workflow process", "@sage/xtrem-workflow/add-properties": "Select properties to update", "@sage/xtrem-workflow/add-variables": "Add variables", "@sage/xtrem-workflow/data_types__test_decimal_data_type__name": "Test decimal data type", "@sage/xtrem-workflow/data_types__test_enum_enum__name": "Test enum enum", "@sage/xtrem-workflow/data_types__test_string_data_type__name": "Test string data type", "@sage/xtrem-workflow/data_types__workflow_definition_status_enum__name": "Workflow definition status enum", "@sage/xtrem-workflow/data_types__workflow_mutation_argument_origin_enum__name": "Workflow mutation argument origin enum", "@sage/xtrem-workflow/data_types__workflow_mutation_parameter_type_enum__name": "Workflow mutation parameter type enum", "@sage/xtrem-workflow/data_types__workflow_process_status_enum__name": "Workflow process status enum", "@sage/xtrem-workflow/enums__test_enum__value1": "Value 1", "@sage/xtrem-workflow/enums__test_enum__value2": "Value 2", "@sage/xtrem-workflow/enums__test_enum__value3": "Value 3", "@sage/xtrem-workflow/enums__workflow_definition_status__production": "Production", "@sage/xtrem-workflow/enums__workflow_definition_status__test": "Test", "@sage/xtrem-workflow/enums__workflow_mutation_argument_origin__fromParameter": "From parameter", "@sage/xtrem-workflow/enums__workflow_mutation_argument_origin__fromVariable": "From variable", "@sage/xtrem-workflow/enums__workflow_mutation_argument_origin__manual": "Manual", "@sage/xtrem-workflow/enums__workflow_mutation_parameter_type__boolean": "Boolean", "@sage/xtrem-workflow/enums__workflow_mutation_parameter_type__date": "Date", "@sage/xtrem-workflow/enums__workflow_mutation_parameter_type__decimal": "Decimal", "@sage/xtrem-workflow/enums__workflow_mutation_parameter_type__enum": "Enum", "@sage/xtrem-workflow/enums__workflow_mutation_parameter_type__integer": "Integer", "@sage/xtrem-workflow/enums__workflow_mutation_parameter_type__other": "Other", "@sage/xtrem-workflow/enums__workflow_mutation_parameter_type__reference": "Reference", "@sage/xtrem-workflow/enums__workflow_mutation_parameter_type__string": "String", "@sage/xtrem-workflow/enums__workflow_process_status__cancelled": "Cancelled", "@sage/xtrem-workflow/enums__workflow_process_status__error": "Error", "@sage/xtrem-workflow/enums__workflow_process_status__running": "Running", "@sage/xtrem-workflow/enums__workflow_process_status__shutDown": "Shut down", "@sage/xtrem-workflow/enums__workflow_process_status__skipped": "Skipped", "@sage/xtrem-workflow/enums__workflow_process_status__success": "Success", "@sage/xtrem-workflow/enums__workflow_process_status__suspended": "Suspended", "@sage/xtrem-workflow/error-no-test-user-in-test-mode": "When the status is Test, you need to provide a test user.", "@sage/xtrem-workflow/invalid-workflow-is-disabled": "The workflow {{id}} has an error and cannot run. You need to fix the error before it can be activated again.", "@sage/xtrem-workflow/menu_item__automation": "Automation", "@sage/xtrem-workflow/menu_item__designer": "Design", "@sage/xtrem-workflow/menu_item__process": "Process", "@sage/xtrem-workflow/nodes__test_config_locale_error_message": "Localized error message.", "@sage/xtrem-workflow/nodes__workflow_definition__asyncMutation__asyncExport": "Export", "@sage/xtrem-workflow/nodes__workflow_definition__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-workflow/nodes__workflow_definition__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-workflow/nodes__workflow_definition__node_name": "Workflow definition", "@sage/xtrem-workflow/nodes__workflow_definition__property__diagram": "Diagram", "@sage/xtrem-workflow/nodes__workflow_definition__property__flow": "Flow", "@sage/xtrem-workflow/nodes__workflow_definition__property__id": "Id", "@sage/xtrem-workflow/nodes__workflow_definition__property__isActive": "Is active", "@sage/xtrem-workflow/nodes__workflow_definition__property__name": "Name", "@sage/xtrem-workflow/nodes__workflow_definition__property__processes": "Processes", "@sage/xtrem-workflow/nodes__workflow_definition__property__startTopic": "Start topic", "@sage/xtrem-workflow/nodes__workflow_definition__property__status": "Status", "@sage/xtrem-workflow/nodes__workflow_definition__property__testUser": "Test user", "@sage/xtrem-workflow/nodes__workflow_diagram__asyncMutation__asyncExport": "Export", "@sage/xtrem-workflow/nodes__workflow_diagram__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-workflow/nodes__workflow_diagram__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-workflow/nodes__workflow_diagram__mutation__controlDiagram": "Control diagram", "@sage/xtrem-workflow/nodes__workflow_diagram__mutation__controlDiagram__failed": "Control diagram failed.", "@sage/xtrem-workflow/nodes__workflow_diagram__mutation__controlDiagram__parameter__edges": "<PERSON>s", "@sage/xtrem-workflow/nodes__workflow_diagram__mutation__controlDiagram__parameter__nodes": "Nodes", "@sage/xtrem-workflow/nodes__workflow_diagram__node_name": "Workflow diagram", "@sage/xtrem-workflow/nodes__workflow_diagram__property__data": "Data", "@sage/xtrem-workflow/nodes__workflow_diagram__property__version": "Version", "@sage/xtrem-workflow/nodes__workflow_process__asyncMutation__asyncExport": "Export", "@sage/xtrem-workflow/nodes__workflow_process__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-workflow/nodes__workflow_process__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-workflow/nodes__workflow_process__node_name": "Workflow process", "@sage/xtrem-workflow/nodes__workflow_process__property__completedAt": "Completed at", "@sage/xtrem-workflow/nodes__workflow_process__property__containerId": "Container id", "@sage/xtrem-workflow/nodes__workflow_process__property__definition": "Definition", "@sage/xtrem-workflow/nodes__workflow_process__property__diagram": "Diagram", "@sage/xtrem-workflow/nodes__workflow_process__property__duration": "Duration", "@sage/xtrem-workflow/nodes__workflow_process__property__errorMessages": "Error messages", "@sage/xtrem-workflow/nodes__workflow_process__property__eventLog": "Event log", "@sage/xtrem-workflow/nodes__workflow_process__property__id": "Id", "@sage/xtrem-workflow/nodes__workflow_process__property__originId": "Origin id", "@sage/xtrem-workflow/nodes__workflow_process__property__startedAt": "Started at", "@sage/xtrem-workflow/nodes__workflow_process__property__startNotificationId": "Start notification id", "@sage/xtrem-workflow/nodes__workflow_process__property__state": "State", "@sage/xtrem-workflow/nodes__workflow_process__property__status": "Status", "@sage/xtrem-workflow/nodes__workflow_process__property__triggeringUser": "Triggering user", "@sage/xtrem-workflow/nodes__workflow_process__property__variables": "Variables", "@sage/xtrem-workflow/nodes__workflow_step_template__asyncMutation__asyncExport": "Export", "@sage/xtrem-workflow/nodes__workflow_step_template__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-workflow/nodes__workflow_step_template__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-workflow/nodes__workflow_step_template__mutation__registerStepConfiguration": "Register step configuration", "@sage/xtrem-workflow/nodes__workflow_step_template__mutation__registerStepConfiguration__failed": "Register step configuration failed.", "@sage/xtrem-workflow/nodes__workflow_step_template__mutation__registerStepConfiguration__parameter__asSetupNode": "As setup node", "@sage/xtrem-workflow/nodes__workflow_step_template__mutation__registerStepConfiguration__parameter__configData": "Config data", "@sage/xtrem-workflow/nodes__workflow_step_template__mutation__registerStepConfiguration__parameter__factory": "Factory", "@sage/xtrem-workflow/nodes__workflow_step_template__mutation__registerStepConfiguration__parameter__localizedDescription": "Localized description", "@sage/xtrem-workflow/nodes__workflow_step_template__mutation__registerStepConfiguration__parameter__localizedTitle": "Localized title", "@sage/xtrem-workflow/nodes__workflow_step_template__mutation__registerStepConfiguration__parameter__stepConstructor": "Step constructor", "@sage/xtrem-workflow/nodes__workflow_step_template__node_name": "Workflow step template", "@sage/xtrem-workflow/nodes__workflow_step_template__property__color": "Color", "@sage/xtrem-workflow/nodes__workflow_step_template__property__configData": "Config data", "@sage/xtrem-workflow/nodes__workflow_step_template__property__description": "Description", "@sage/xtrem-workflow/nodes__workflow_step_template__property__factory": "Factory", "@sage/xtrem-workflow/nodes__workflow_step_template__property__icon": "Icon", "@sage/xtrem-workflow/nodes__workflow_step_template__property__isActive": "Is active", "@sage/xtrem-workflow/nodes__workflow_step_template__property__serviceOptions": "Service options", "@sage/xtrem-workflow/nodes__workflow_step_template__property__stepConstructor": "Step constructor", "@sage/xtrem-workflow/nodes__workflow_step_template__property__stepDescriptor": "Step descriptor", "@sage/xtrem-workflow/nodes__workflow_step_template__property__title": "Title", "@sage/xtrem-workflow/nodes__workflow_step_template__property__variant": "<PERSON><PERSON><PERSON>", "@sage/xtrem-workflow/package__name": "Sage xtrem workflow", "@sage/xtrem-workflow/pages__workflow_action_calculate____subtitle": "Calculate", "@sage/xtrem-workflow/pages__workflow_action_calculate____title": "Action configuration", "@sage/xtrem-workflow/pages__workflow_action_calculate__calculationSteps____addButtonText": "Add a calculation step", "@sage/xtrem-workflow/pages__workflow_action_calculate__calculationSteps____columns__title__isVariable": "Is variable?", "@sage/xtrem-workflow/pages__workflow_action_calculate__calculationSteps____columns__title__operation": "Operation", "@sage/xtrem-workflow/pages__workflow_action_calculate__calculationSteps____columns__title__value": "Component value", "@sage/xtrem-workflow/pages__workflow_action_calculate__calculationSteps____columns__title__variable": "Component variable", "@sage/xtrem-workflow/pages__workflow_action_calculate__calculationSteps____title": "Calculation", "@sage/xtrem-workflow/pages__workflow_action_calculate__mainSection____title": "Basic Details", "@sage/xtrem-workflow/pages__workflow_action_calculate__outputVariableName____title": "Output variable name", "@sage/xtrem-workflow/pages__workflow_action_calculate__title____helperText": "Title displayed in the workflow diagram.", "@sage/xtrem-workflow/pages__workflow_action_calculate__title____title": "Action title", "@sage/xtrem-workflow/pages__workflow_action_condition____title": "Condition configuration", "@sage/xtrem-workflow/pages__workflow_action_condition__branchesBlock____title": "Setup branches", "@sage/xtrem-workflow/pages__workflow_action_condition__filters____helperText": "When all conditions are true the execution continues to the left. If any of the conditions are evaluated to false, the execution continues to the right", "@sage/xtrem-workflow/pages__workflow_action_condition__filters____title": "Condition", "@sage/xtrem-workflow/pages__workflow_action_condition__ifFalseBranch____title": "Add \"if false\" branch", "@sage/xtrem-workflow/pages__workflow_action_condition__ifTrueBranch____title": "Add \"if true\" branch", "@sage/xtrem-workflow/pages__workflow_action_condition__mainSection____title": "Basic Details", "@sage/xtrem-workflow/pages__workflow_action_condition__title____helperText": "Title displayed in the workflow diagram.", "@sage/xtrem-workflow/pages__workflow_action_condition__title____title": "Condition title", "@sage/xtrem-workflow/pages__workflow_action_condition__variablesBlock____title": "Condition definition", "@sage/xtrem-workflow/pages__workflow_action_delete_entity____subtitle": "Delete an entity", "@sage/xtrem-workflow/pages__workflow_action_delete_entity____title": "Action configuration", "@sage/xtrem-workflow/pages__workflow_action_delete_entity__dataEntity____columns__title__name": "Record type", "@sage/xtrem-workflow/pages__workflow_action_delete_entity__dataEntity____columns__title__package__name": "Package", "@sage/xtrem-workflow/pages__workflow_action_delete_entity__dataEntity____columns__title__title": "Record type", "@sage/xtrem-workflow/pages__workflow_action_delete_entity__dataEntity____title": "Record type", "@sage/xtrem-workflow/pages__workflow_action_delete_entity__mainSection____title": "Basic Details", "@sage/xtrem-workflow/pages__workflow_action_delete_entity__recordIdToDelete____title": "Record ID variable", "@sage/xtrem-workflow/pages__workflow_action_delete_entity__title____helperText": "Title displayed in the workflow diagram.", "@sage/xtrem-workflow/pages__workflow_action_delete_entity__title____title": "Action title", "@sage/xtrem-workflow/pages__workflow_action_read_entity____subtitle": "Read a record", "@sage/xtrem-workflow/pages__workflow_action_read_entity____title": "Action configuration", "@sage/xtrem-workflow/pages__workflow_action_read_entity__failIfNotFound____helperText": "Raise an error if no record is found.", "@sage/xtrem-workflow/pages__workflow_action_read_entity__failIfNotFound____title": "Fail if not found", "@sage/xtrem-workflow/pages__workflow_action_read_entity__mainSection____title": "Basic Details", "@sage/xtrem-workflow/pages__workflow_action_read_entity__nodeFactory____columns__title__name": "Record type", "@sage/xtrem-workflow/pages__workflow_action_read_entity__nodeFactory____columns__title__package__name": "Package", "@sage/xtrem-workflow/pages__workflow_action_read_entity__nodeFactory____columns__title__title": "Record type", "@sage/xtrem-workflow/pages__workflow_action_read_entity__nodeFactory____title": "Record type", "@sage/xtrem-workflow/pages__workflow_action_read_entity__outputVariableName____title": "Output variable name", "@sage/xtrem-workflow/pages__workflow_action_read_entity__recordKey____title": "Record key", "@sage/xtrem-workflow/pages__workflow_action_read_entity__title____helperText": "Title displayed in the workflow diagram.", "@sage/xtrem-workflow/pages__workflow_action_read_entity__title____title": "Action title", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification____subtitle": "Notify a user", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification____title": "Action configuration", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__actionsForLocale____columns__title___originalIndex": "Original index", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__actionsForLocale____columns__title__link": "Link", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__actionsForLocale____columns__title__style": "Button style", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__actionsForLocale____columns__title__title": "Title", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__actionsSection____title": "Actions", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__icon____title": "Icon", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__level____title": "Type", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__mainSection____title": "General", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__notificationDescription____title": "Notification description", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__notificationTitle____title": "Notification title", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__recipients____addButtonText": "Add a recipient", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__recipients____columns__columns__user__email__title": "Last name", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__recipients____columns__columns__user__email__title__2": "First name", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__recipients____columns__columns__user__email__title__3": "Email", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__recipients____columns__lookupDialogTitle__user__email": "Select user", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__recipients____columns__placeholder__user__email": "Select user", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__recipients____columns__title__isManuallySet": "Enter manually", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__recipients____columns__title__user": "User", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__recipients____columns__title__user__email": "User", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__recipients____title": "Recipients", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__recipientsSection____title": "Recipients", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__shouldDisplayToast____helperText": "Urgent notifications are displayed as pop-up messages", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__shouldDisplayToast____title": "Is urgent?", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__title____helperText": "Title displayed in the workflow diagram.", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__title____title": "Action title", "@sage/xtrem-workflow/pages__workflow_action_test_stub____subtitle": "Test stub", "@sage/xtrem-workflow/pages__workflow_action_test_stub____title": "Action configuration", "@sage/xtrem-workflow/pages__workflow_action_test_stub__mainSection____title": "Basic Details", "@sage/xtrem-workflow/pages__workflow_action_test_stub__outputVariableName____title": "Output variable name", "@sage/xtrem-workflow/pages__workflow_action_test_stub__title____helperText": "Title displayed in the workflow diagram.", "@sage/xtrem-workflow/pages__workflow_action_test_stub__title____title": "Action title", "@sage/xtrem-workflow/pages__workflow_action_update_entity____subtitle": "Update an entity", "@sage/xtrem-workflow/pages__workflow_action_update_entity____title": "Action configuration", "@sage/xtrem-workflow/pages__workflow_action_update_entity__mainSection____title": "Data selection", "@sage/xtrem-workflow/pages__workflow_action_update_entity__pathToUpdate____title": "Record to update", "@sage/xtrem-workflow/pages__workflow_action_update_entity__title____helperText": "Title displayed in the workflow diagram.", "@sage/xtrem-workflow/pages__workflow_action_update_entity__title____title": "Action title", "@sage/xtrem-workflow/pages__workflow_action_update_entity__updateRulesPod____title": "New values", "@sage/xtrem-workflow/pages__workflow_action_wait____subtitle": "Wait", "@sage/xtrem-workflow/pages__workflow_action_wait____title": "Action configuration", "@sage/xtrem-workflow/pages__workflow_action_wait__mainSection____title": "Basic Details", "@sage/xtrem-workflow/pages__workflow_action_wait__quantity____title": "Time quantity", "@sage/xtrem-workflow/pages__workflow_action_wait__title____helperText": "Title displayed in the workflow diagram.", "@sage/xtrem-workflow/pages__workflow_action_wait__title____title": "Action title", "@sage/xtrem-workflow/pages__workflow_action_wait__unit____title": "Unit", "@sage/xtrem-workflow/pages__workflow_definition____navigationPanel__inlineActions__title__duplicate": "Duplicate", "@sage/xtrem-workflow/pages__workflow_definition____navigationPanel__listItem__id__title": "ID", "@sage/xtrem-workflow/pages__workflow_definition____navigationPanel__listItem__title__title": "Name", "@sage/xtrem-workflow/pages__workflow_definition____navigationPanel__listItem__titleRight__title": "Active", "@sage/xtrem-workflow/pages__workflow_definition____navigationPanel__listItem__triggerDetails__title": "Trigger details", "@sage/xtrem-workflow/pages__workflow_definition____navigationPanel__listItem__triggerType__title": "Trigger type", "@sage/xtrem-workflow/pages__workflow_definition____navigationPanel__optionsMenu__title": "All Workflows", "@sage/xtrem-workflow/pages__workflow_definition____objectTypePlural": "Workflows", "@sage/xtrem-workflow/pages__workflow_definition____objectTypeSingular": "Workflow", "@sage/xtrem-workflow/pages__workflow_definition____title": "Workflow designer", "@sage/xtrem-workflow/pages__workflow_definition__deleteWorkflowDefinition____title": "Delete", "@sage/xtrem-workflow/pages__workflow_definition__designerSection____title": "Designer", "@sage/xtrem-workflow/pages__workflow_definition__eventLog____columns__title__event": "Status", "@sage/xtrem-workflow/pages__workflow_definition__eventLog____columns__title__message": "Message", "@sage/xtrem-workflow/pages__workflow_definition__eventLog____columns__title__stepId": "Step", "@sage/xtrem-workflow/pages__workflow_definition__eventLog____columns__title__timestamp": "Time stamp", "@sage/xtrem-workflow/pages__workflow_definition__eventLog____title": "Event log", "@sage/xtrem-workflow/pages__workflow_definition__executionSection____title": "Log", "@sage/xtrem-workflow/pages__workflow_definition__flow____title": "Workflow", "@sage/xtrem-workflow/pages__workflow_definition__headerSection____title": "Header", "@sage/xtrem-workflow/pages__workflow_definition__id____title": "ID", "@sage/xtrem-workflow/pages__workflow_definition__isActive____title": "Active", "@sage/xtrem-workflow/pages__workflow_definition__name____title": "Name", "@sage/xtrem-workflow/pages__workflow_definition__processes____columns__postfix__duration": "seconds", "@sage/xtrem-workflow/pages__workflow_definition__processes____columns__title__duration": "Duration", "@sage/xtrem-workflow/pages__workflow_definition__processes____columns__title__errorMessages": "Errors", "@sage/xtrem-workflow/pages__workflow_definition__processes____columns__title__startedAt": "Start time", "@sage/xtrem-workflow/pages__workflow_definition__processes____columns__title__status": "Status", "@sage/xtrem-workflow/pages__workflow_definition__processes____columns__title__triggeringUser__displayName": "Triggered by", "@sage/xtrem-workflow/pages__workflow_definition__processes____dropdownActions__title": "View details", "@sage/xtrem-workflow/pages__workflow_definition__processes____title": "Logs", "@sage/xtrem-workflow/pages__workflow_definition__refreshProcesses____title": "Refresh", "@sage/xtrem-workflow/pages__workflow_definition__status____title": "Status", "@sage/xtrem-workflow/pages__workflow_definition__testUser____columns__title__email": "Email", "@sage/xtrem-workflow/pages__workflow_definition__testUser____columns__title__firstName": "First name", "@sage/xtrem-workflow/pages__workflow_definition__testUser____columns__title__lastName": "Last name", "@sage/xtrem-workflow/pages__workflow_definition__testUser____lookupDialogTitle": "Select a user", "@sage/xtrem-workflow/pages__workflow_definition__testUser____title": "Test user", "@sage/xtrem-workflow/pages__workflow_definition__variables____title": "Variable details", "@sage/xtrem-workflow/pages__workflow_event_entity_created____subtitle": "Entity created", "@sage/xtrem-workflow/pages__workflow_event_entity_created____title": "Trigger configuration", "@sage/xtrem-workflow/pages__workflow_event_entity_created__conditionSection____title": "Condition", "@sage/xtrem-workflow/pages__workflow_event_entity_created__filters____helperText": "When all conditions are true the execution continues on the path below. If any of the conditions are evaluated to false, the execution continues to the right", "@sage/xtrem-workflow/pages__workflow_event_entity_created__filters____title": "Condition", "@sage/xtrem-workflow/pages__workflow_event_entity_created__mainSection____title": "Configuration", "@sage/xtrem-workflow/pages__workflow_event_entity_created__nodeFactory____columns__title__name": "Record type", "@sage/xtrem-workflow/pages__workflow_event_entity_created__nodeFactory____columns__title__package__name": "Package", "@sage/xtrem-workflow/pages__workflow_event_entity_created__nodeFactory____columns__title__title": "Record type", "@sage/xtrem-workflow/pages__workflow_event_entity_created__nodeFactory____title": "Record type", "@sage/xtrem-workflow/pages__workflow_event_entity_created__title____helperText": "Title displayed in the workflow diagram.", "@sage/xtrem-workflow/pages__workflow_event_entity_created__title____title": "Trigger title", "@sage/xtrem-workflow/pages__workflow_event_entity_deleted____subtitle": "Entity deleted", "@sage/xtrem-workflow/pages__workflow_event_entity_deleted____title": "Trigger configuration", "@sage/xtrem-workflow/pages__workflow_event_entity_deleted__dataEntity____columns__title__name": "Record type", "@sage/xtrem-workflow/pages__workflow_event_entity_deleted__dataEntity____columns__title__package__name": "Package", "@sage/xtrem-workflow/pages__workflow_event_entity_deleted__dataEntity____columns__title__title": "Record type", "@sage/xtrem-workflow/pages__workflow_event_entity_deleted__dataEntity____title": "Record type", "@sage/xtrem-workflow/pages__workflow_event_entity_deleted__mainSection____title": "Basic Details", "@sage/xtrem-workflow/pages__workflow_event_entity_deleted__outputVariableName____title": "Output variable name", "@sage/xtrem-workflow/pages__workflow_event_entity_deleted__title____helperText": "Title displayed in the workflow diagram.", "@sage/xtrem-workflow/pages__workflow_event_entity_deleted__title____title": "Trigger title", "@sage/xtrem-workflow/pages__workflow_event_entity_updated____subtitle": "Entity updated", "@sage/xtrem-workflow/pages__workflow_event_entity_updated____title": "Trigger configuration", "@sage/xtrem-workflow/pages__workflow_event_entity_updated__conditionSection____title": "Condition", "@sage/xtrem-workflow/pages__workflow_event_entity_updated__filters____helperText": "When all conditions are true the execution continues on the path below. If any of the conditions are evaluated to false, the execution continues to the right", "@sage/xtrem-workflow/pages__workflow_event_entity_updated__filters____title": "Condition", "@sage/xtrem-workflow/pages__workflow_event_entity_updated__mainSection____title": "Configuration", "@sage/xtrem-workflow/pages__workflow_event_entity_updated__nodeFactory____columns__title__name": "Record type", "@sage/xtrem-workflow/pages__workflow_event_entity_updated__nodeFactory____columns__title__package__name": "Package", "@sage/xtrem-workflow/pages__workflow_event_entity_updated__nodeFactory____columns__title__title": "Record type", "@sage/xtrem-workflow/pages__workflow_event_entity_updated__nodeFactory____title": "Record type", "@sage/xtrem-workflow/pages__workflow_event_entity_updated__title____helperText": "Title displayed in the workflow diagram.", "@sage/xtrem-workflow/pages__workflow_event_entity_updated__title____title": "Trigger title", "@sage/xtrem-workflow/pages__workflow_event_test_started____subtitle": "Test started", "@sage/xtrem-workflow/pages__workflow_event_test_started____title": "Trigger configuration", "@sage/xtrem-workflow/pages__workflow_event_test_started__mainSection____title": "Basic Details", "@sage/xtrem-workflow/pages__workflow_event_test_started__parameters____addButtonText": "Add a parameter", "@sage/xtrem-workflow/pages__workflow_event_test_started__parameters____columns__columns__node__name__title": "Record type", "@sage/xtrem-workflow/pages__workflow_event_test_started__parameters____columns__columns__node__name__title__2": "Record type", "@sage/xtrem-workflow/pages__workflow_event_test_started__parameters____columns__columns__node__name__title__3": "Package", "@sage/xtrem-workflow/pages__workflow_event_test_started__parameters____columns__lookupDialogTitle__node__name": "Select referenced node", "@sage/xtrem-workflow/pages__workflow_event_test_started__parameters____columns__placeholder__node__name": "Select referenced node", "@sage/xtrem-workflow/pages__workflow_event_test_started__parameters____columns__title__name": "Parameter name", "@sage/xtrem-workflow/pages__workflow_event_test_started__parameters____columns__title__node__name": "Node", "@sage/xtrem-workflow/pages__workflow_event_test_started__parameters____columns__title__type": "Type", "@sage/xtrem-workflow/pages__workflow_event_test_started__parameters____title": "Test parameters", "@sage/xtrem-workflow/pages__workflow_event_test_started__parametersSection____title": "Parameters", "@sage/xtrem-workflow/pages__workflow_event_test_started__title____helperText": "Title displayed in the workflow diagram.", "@sage/xtrem-workflow/pages__workflow_event_test_started__title____title": "Trigger title", "@sage/xtrem-workflow/pages__workflow_mutation_action____subtitle": "Execute a graphQL mutation", "@sage/xtrem-workflow/pages__workflow_mutation_action____title": "Action configuration", "@sage/xtrem-workflow/pages__workflow_mutation_action__actionParametersPod____title": "Parameters", "@sage/xtrem-workflow/pages__workflow_mutation_action__configDescription____title": "Description", "@sage/xtrem-workflow/pages__workflow_mutation_action__configRegisterAsSetupNode____helperText": "Register the action as a setup node", "@sage/xtrem-workflow/pages__workflow_mutation_action__configRegisterAsSetupNode____title": "Setup node", "@sage/xtrem-workflow/pages__workflow_mutation_action__mainSection____title": "Main", "@sage/xtrem-workflow/pages__workflow_mutation_action__mutation____columns__title__factory__name": "Node", "@sage/xtrem-workflow/pages__workflow_mutation_action__mutation____columns__title__name": "Mutation", "@sage/xtrem-workflow/pages__workflow_mutation_action__mutation____lookupDialogTitle": "Select a mutation", "@sage/xtrem-workflow/pages__workflow_mutation_action__mutation____title": "Mutation", "@sage/xtrem-workflow/pages__workflow_mutation_action__mutationArgumentsPod____title": "Arguments", "@sage/xtrem-workflow/pages__workflow_mutation_action__mutationSection____title": "Mutation", "@sage/xtrem-workflow/pages__workflow_mutation_action__outputVariableName____title": "Output variable name", "@sage/xtrem-workflow/pages__workflow_mutation_action__registerActionButton____title": "Register the action", "@sage/xtrem-workflow/pages__workflow_mutation_action__registrationSection____title": "Registration", "@sage/xtrem-workflow/pages__workflow_mutation_action__selector____title": "Selector", "@sage/xtrem-workflow/pages__workflow_mutation_action__showAdvancedOptions____helperText": "Show the advanced options of the action\n\nWARNING: this is reserved for advanced users", "@sage/xtrem-workflow/pages__workflow_mutation_action__showAdvancedOptions____title": "Show advanced options", "@sage/xtrem-workflow/pages__workflow_mutation_action__title____helperText": "Title displayed in the workflow diagram.", "@sage/xtrem-workflow/pages__workflow_mutation_action__title____title": "Action title", "@sage/xtrem-workflow/pages__workflow_process____navigationPanel__bulkActions__title": "Export", "@sage/xtrem-workflow/pages__workflow_process____navigationPanel__listItem__duration__postfix": "seconds", "@sage/xtrem-workflow/pages__workflow_process____navigationPanel__listItem__duration__title": "Duration", "@sage/xtrem-workflow/pages__workflow_process____navigationPanel__listItem__errorMessage__title": "Error reason", "@sage/xtrem-workflow/pages__workflow_process____navigationPanel__listItem__line2__title": "Workflow", "@sage/xtrem-workflow/pages__workflow_process____navigationPanel__listItem__startedAt__title": "Start time", "@sage/xtrem-workflow/pages__workflow_process____navigationPanel__listItem__title__title": "ID", "@sage/xtrem-workflow/pages__workflow_process____navigationPanel__listItem__titleRight__title": "Status", "@sage/xtrem-workflow/pages__workflow_process____navigationPanel__listItem__triggeredBy__title": "Triggered by", "@sage/xtrem-workflow/pages__workflow_process____navigationPanel__optionsMenu__title": "All Processes", "@sage/xtrem-workflow/pages__workflow_process____objectTypePlural": "Workflow logs", "@sage/xtrem-workflow/pages__workflow_process____objectTypeSingular": "Workflow log", "@sage/xtrem-workflow/pages__workflow_process____title": "Workflow logs", "@sage/xtrem-workflow/pages__workflow_process___id____title": "ID", "@sage/xtrem-workflow/pages__workflow_process__completedAt____title": "End time", "@sage/xtrem-workflow/pages__workflow_process__designerSection____title": "Diagram", "@sage/xtrem-workflow/pages__workflow_process__duration____postfix": "seconds", "@sage/xtrem-workflow/pages__workflow_process__duration____title": "Duration", "@sage/xtrem-workflow/pages__workflow_process__eventLogsSection____title": "Log details", "@sage/xtrem-workflow/pages__workflow_process__flow____title": "Workflow", "@sage/xtrem-workflow/pages__workflow_process__goToDesigner____title": "Go to the designer", "@sage/xtrem-workflow/pages__workflow_process__headerSection____title": "Header", "@sage/xtrem-workflow/pages__workflow_process__olderVersionMessage____content": "**You are viewing an older version of this workflow.**", "@sage/xtrem-workflow/pages__workflow_process__startedAt____title": "Start time", "@sage/xtrem-workflow/pages__workflow_process__status____title": "Status", "@sage/xtrem-workflow/pages__workflow_process__tableLogs____columns__title__event": "Status", "@sage/xtrem-workflow/pages__workflow_process__tableLogs____columns__title__message": "Message", "@sage/xtrem-workflow/pages__workflow_process__tableLogs____columns__title__step": "Step", "@sage/xtrem-workflow/pages__workflow_process__tableLogs____columns__title__timestamp": "Time stamp", "@sage/xtrem-workflow/pages__workflow_process__tableLogs____title": "Event log", "@sage/xtrem-workflow/pages__workflow_process__triggeringUser____title": "Triggered by", "@sage/xtrem-workflow/pages__workflow_process__variablesSection____title": "Variables", "@sage/xtrem-workflow/pages__workflow_process__workflow____title": "Workflow", "@sage/xtrem-workflow/pages__workflow_process__workflowDefinitionId____title": "Workflow definition ID", "@sage/xtrem-workflow/pages__workflow_select_variables_dialog____title": "", "@sage/xtrem-workflow/pages__workflow_select_variables_dialog__fromPath____title": "Select from ...", "@sage/xtrem-workflow/pages__workflow_select_variables_dialog__ok____title": "OK", "@sage/xtrem-workflow/pages__workflow_select_variables_dialog__selectedProperties____helperText": "Only the selected properties are requested from the database.", "@sage/xtrem-workflow/pages__workflow_step_template____navigationPanel__listItem__line2__title": "Description", "@sage/xtrem-workflow/pages__workflow_step_template____navigationPanel__listItem__title__title": "Title", "@sage/xtrem-workflow/pages__workflow_step_template____objectTypePlural": "Workflow step templates", "@sage/xtrem-workflow/pages__workflow_step_template____title": "Workflow step template", "@sage/xtrem-workflow/pages__workflow_step_template__description____title": "Description", "@sage/xtrem-workflow/pages__workflow_step_template__editActionButton____title": "Edit the action", "@sage/xtrem-workflow/pages__workflow_step_template__generalSection____title": "General", "@sage/xtrem-workflow/pages__workflow_step_template__icon____title": "Icon", "@sage/xtrem-workflow/pages__workflow_step_template__isActive____title": "Active", "@sage/xtrem-workflow/pages__workflow_step_template__title____title": "Title", "@sage/xtrem-workflow/permission__manage__name": "Manage", "@sage/xtrem-workflow/permission__read__name": "Read", "@sage/xtrem-workflow/service_options__workflow__name": "Workflow", "@sage/xtrem-workflow/service_options__workflow_advanced__name": "Workflow advanced", "@sage/xtrem-workflow/service_options__workflow_option__name": "Workflow option", "@sage/xtrem-workflow/time-unit-d": "Days", "@sage/xtrem-workflow/time-unit-h": "Hours", "@sage/xtrem-workflow/time-unit-m": "Minutes", "@sage/xtrem-workflow/time-unit-ms": "Milliseconds", "@sage/xtrem-workflow/time-unit-s": "Seconds", "@sage/xtrem-workflow/workflow_error_empty_workflow": "You need to add a trigger.", "@sage/xtrem-workflow/workflow_error_no_property_to_update": "You need to add a property to update.", "@sage/xtrem-workflow/workflow_mutation_action_argument_not_found": "The action refers to the argument: {{argumentName}}, that does not exist in the mutation: {{mutationName}}.", "@sage/xtrem-workflow/workflow_mutation_argument_not_found": "The mutation argument is not defined in the action configuration: {{argument}}.", "@sage/xtrem-workflow/workflow_mutation_argument_type_mismatch": "The mutation argument has a type mismatch: {{argument}}. Expected: {{expectedType}}, got: {{actualType}}.", "@sage/xtrem-workflow/workflow_unknown_mutation": "The mutation does not exist: {{mutationName}}.", "@sage/xtrem-workflow/workflow-delete-definition-confirm-body": "You are about to delete {{totalCount}} running processes. Delete these processes or cancel?", "@sage/xtrem-workflow/workflow-delete-definition-confirm-title": "Confirm delete", "@sage/xtrem-workflow/workflow-dialog-title-error": "Error", "@sage/xtrem-workflow/workflow-entity-created-condition-not-an-array": "conditions is not an array", "@sage/xtrem-workflow/workflow-entity-created-default-title": "{{factoryName}} created", "@sage/xtrem-workflow/workflow-entity-created-no-property-selection": "Invalid event configuration: property selection is required when filter is provided", "@sage/xtrem-workflow/workflow-entity-created-step-variables-not-an-array": "stepVariables is not an array", "@sage/xtrem-workflow/workflow-entity-deleted-default-title": "{{factoryName}} deleted", "@sage/xtrem-workflow/workflow-entity-updated-condition-not-an-array": "conditions is not an array", "@sage/xtrem-workflow/workflow-entity-updated-default-title": "{{factoryName}} updated", "@sage/xtrem-workflow/workflow-entity-updated-no-property-selection": "Invalid event configuration: property selection is required when filter is provided", "@sage/xtrem-workflow/workflow-entity-updated-step-variables-not-an-array": "stepVariables is not an array", "@sage/xtrem-workflow/workflow-error-cannot-unselect-root-property": "Cannot unselect root _id property", "@sage/xtrem-workflow/workflow-error-event-cant-have-a-parent": "An event cannot have a parent.", "@sage/xtrem-workflow/workflow-error-flow-has-cycle": "Flow has a cycle on node: {{nodeId}}", "@sage/xtrem-workflow/workflow-error-more-than-one-start-node": "Flow has more than one start nodes: {{nodeIds}}", "@sage/xtrem-workflow/workflow-error-no-description": "Description is missing", "@sage/xtrem-workflow/workflow-error-no-parent": "An action needs to have at least one parent.", "@sage/xtrem-workflow/workflow-error-no-start-node": "The flow does not have any start node.", "@sage/xtrem-workflow/workflow-error-no-title": "Title is missing", "@sage/xtrem-workflow/workflow-error-start-topics-forbidden": "Start topics are not allowed in action nodes.", "@sage/xtrem-workflow/workflow-error-too-few-outputs": "Node has too few outputs: at least {{minOutputs}} expected, got {{outputEdges}}.", "@sage/xtrem-workflow/workflow-error-too-many-outputs": "Node has too many outputs: at most {{maxOutputs}} expected, got {{outputEdges}}.", "@sage/xtrem-workflow/workflow-send-user-notification-error-no-notification-description": "Notification description is required", "@sage/xtrem-workflow/workflow-send-user-notification-error-no-notification-title": "Notification title is required", "@sage/xtrem-workflow/workflow-variable-not-found": "Variable does not exist: {{variableName}}.", "@sage/xtrem-workflow/workflow-variable-not-found-with-suggestion": "Variable does not exist: {{variableName}}. Did you mean: {{closestVariable}}?"}