{"@sage/xtrem-workflow/activity__workflow_definition__name": "Definiçao do workflow", "@sage/xtrem-workflow/activity__workflow_process__name": "Processo de workflow", "@sage/xtrem-workflow/add-properties": "Selecionar propriedades a atualizar", "@sage/xtrem-workflow/add-variables": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-workflow/data_types__test_decimal_data_type__name": "Teste do tipo de dados decimais", "@sage/xtrem-workflow/data_types__test_enum_enum__name": "Teste enum enum", "@sage/xtrem-workflow/data_types__test_string_data_type__name": "Tipo de dados da string de teste", "@sage/xtrem-workflow/data_types__workflow_definition_status_enum__name": "Enum do status de definição do workflow", "@sage/xtrem-workflow/data_types__workflow_mutation_argument_origin_enum__name": "Origem do argumento de mutação do workflow enum", "@sage/xtrem-workflow/data_types__workflow_mutation_parameter_type_enum__name": "Origem do parâmetro de mutação do workflow enum", "@sage/xtrem-workflow/data_types__workflow_process_status_enum__name": "Enum de status do processo de workflow", "@sage/xtrem-workflow/enums__test_enum__value1": "Valor 1", "@sage/xtrem-workflow/enums__test_enum__value2": "Valor 2", "@sage/xtrem-workflow/enums__test_enum__value3": "Valor 3", "@sage/xtrem-workflow/enums__workflow_definition_status__production": "Produção", "@sage/xtrem-workflow/enums__workflow_definition_status__test": "<PERSON>e", "@sage/xtrem-workflow/enums__workflow_mutation_argument_origin__fromParameter": "Do parâmetro", "@sage/xtrem-workflow/enums__workflow_mutation_argument_origin__fromVariable": "<PERSON> variável", "@sage/xtrem-workflow/enums__workflow_mutation_argument_origin__manual": "Manual", "@sage/xtrem-workflow/enums__workflow_mutation_parameter_type__boolean": "<PERSON><PERSON><PERSON>", "@sage/xtrem-workflow/enums__workflow_mutation_parameter_type__date": "Data", "@sage/xtrem-workflow/enums__workflow_mutation_parameter_type__decimal": "Décimal", "@sage/xtrem-workflow/enums__workflow_mutation_parameter_type__enum": "enum", "@sage/xtrem-workflow/enums__workflow_mutation_parameter_type__integer": "Inteiro", "@sage/xtrem-workflow/enums__workflow_mutation_parameter_type__other": "Outro", "@sage/xtrem-workflow/enums__workflow_mutation_parameter_type__reference": "Referência", "@sage/xtrem-workflow/enums__workflow_mutation_parameter_type__string": "<PERSON><PERSON> (String)", "@sage/xtrem-workflow/enums__workflow_process_status__cancelled": "Cancelado", "@sage/xtrem-workflow/enums__workflow_process_status__error": "Erro", "@sage/xtrem-workflow/enums__workflow_process_status__running": "Em execução", "@sage/xtrem-workflow/enums__workflow_process_status__shutDown": "Shut down", "@sage/xtrem-workflow/enums__workflow_process_status__skipped": "Parado", "@sage/xtrem-workflow/enums__workflow_process_status__success": "Sucesso", "@sage/xtrem-workflow/enums__workflow_process_status__suspended": "Suspenso", "@sage/xtrem-workflow/error-no-test-user-in-test-mode": "Quando o status é Teste, é necessário fornecer um utilizador de teste.", "@sage/xtrem-workflow/invalid-workflow-is-disabled": "", "@sage/xtrem-workflow/menu_item__automation": "Automatização", "@sage/xtrem-workflow/menu_item__designer": "<PERSON><PERSON><PERSON>", "@sage/xtrem-workflow/menu_item__process": "Processo", "@sage/xtrem-workflow/nodes__test_config_locale_error_message": "Mensagem de erro localizada.", "@sage/xtrem-workflow/nodes__workflow_definition__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-workflow/nodes__workflow_definition__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-workflow/nodes__workflow_definition__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-workflow/nodes__workflow_definition__node_name": "Definiçao do workflow", "@sage/xtrem-workflow/nodes__workflow_definition__property__diagram": "Diagrama", "@sage/xtrem-workflow/nodes__workflow_definition__property__flow": "Fluxo", "@sage/xtrem-workflow/nodes__workflow_definition__property__id": "ID", "@sage/xtrem-workflow/nodes__workflow_definition__property__isActive": "Está ativo(a)", "@sage/xtrem-workflow/nodes__workflow_definition__property__name": "Nome", "@sage/xtrem-workflow/nodes__workflow_definition__property__processes": "Processamentos", "@sage/xtrem-workflow/nodes__workflow_definition__property__startTopic": "Iniiar tópio", "@sage/xtrem-workflow/nodes__workflow_definition__property__status": "Status", "@sage/xtrem-workflow/nodes__workflow_definition__property__testUser": "<PERSON><PERSON><PERSON><PERSON> teste", "@sage/xtrem-workflow/nodes__workflow_diagram__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-workflow/nodes__workflow_diagram__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-workflow/nodes__workflow_diagram__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-workflow/nodes__workflow_diagram__mutation__controlDiagram": "Diagrama de controlo", "@sage/xtrem-workflow/nodes__workflow_diagram__mutation__controlDiagram__failed": "O diagrama de controlo falhou.", "@sage/xtrem-workflow/nodes__workflow_diagram__mutation__controlDiagram__parameter__edges": "Contornos", "@sage/xtrem-workflow/nodes__workflow_diagram__mutation__controlDiagram__parameter__nodes": "<PERSON><PERSON> (Nodes)", "@sage/xtrem-workflow/nodes__workflow_diagram__node_name": "Diagrama do workflow", "@sage/xtrem-workflow/nodes__workflow_diagram__property__data": "<PERSON><PERSON>", "@sage/xtrem-workflow/nodes__workflow_diagram__property__version": "Vers<PERSON>", "@sage/xtrem-workflow/nodes__workflow_process__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-workflow/nodes__workflow_process__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-workflow/nodes__workflow_process__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-workflow/nodes__workflow_process__node_name": "Processo de workflow", "@sage/xtrem-workflow/nodes__workflow_process__property__completedAt": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-workflow/nodes__workflow_process__property__containerId": "Contentor", "@sage/xtrem-workflow/nodes__workflow_process__property__definition": "Definição", "@sage/xtrem-workflow/nodes__workflow_process__property__diagram": "Diagrama", "@sage/xtrem-workflow/nodes__workflow_process__property__duration": "Duração", "@sage/xtrem-workflow/nodes__workflow_process__property__errorMessages": "Mensagens de  erro", "@sage/xtrem-workflow/nodes__workflow_process__property__eventLog": "Log do evento", "@sage/xtrem-workflow/nodes__workflow_process__property__id": "ID", "@sage/xtrem-workflow/nodes__workflow_process__property__originId": "ID origem", "@sage/xtrem-workflow/nodes__workflow_process__property__startedAt": "Iniciado em", "@sage/xtrem-workflow/nodes__workflow_process__property__startNotificationId": "Iniciar ID de notificação", "@sage/xtrem-workflow/nodes__workflow_process__property__state": "Região/Distrito", "@sage/xtrem-workflow/nodes__workflow_process__property__status": "Status", "@sage/xtrem-workflow/nodes__workflow_process__property__triggeringUser": "Utilizador de acionamento", "@sage/xtrem-workflow/nodes__workflow_process__property__variables": "Variáveis", "@sage/xtrem-workflow/nodes__workflow_step_template__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-workflow/nodes__workflow_step_template__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-workflow/nodes__workflow_step_template__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-workflow/nodes__workflow_step_template__mutation__registerStepConfiguration": "Configuração da etapa de registo", "@sage/xtrem-workflow/nodes__workflow_step_template__mutation__registerStepConfiguration__failed": "Falha na configuração da etapa de registo.", "@sage/xtrem-workflow/nodes__workflow_step_template__mutation__registerStepConfiguration__parameter__asSetupNode": "Como nó de configuração", "@sage/xtrem-workflow/nodes__workflow_step_template__mutation__registerStepConfiguration__parameter__configData": "Dados de configuração", "@sage/xtrem-workflow/nodes__workflow_step_template__mutation__registerStepConfiguration__parameter__factory": "Fábrica", "@sage/xtrem-workflow/nodes__workflow_step_template__mutation__registerStepConfiguration__parameter__localizedDescription": "Descrição localizada", "@sage/xtrem-workflow/nodes__workflow_step_template__mutation__registerStepConfiguration__parameter__localizedTitle": "Título localizado", "@sage/xtrem-workflow/nodes__workflow_step_template__mutation__registerStepConfiguration__parameter__stepConstructor": "Construtor de etapas", "@sage/xtrem-workflow/nodes__workflow_step_template__node_name": "Modelo de etapas de workflow", "@sage/xtrem-workflow/nodes__workflow_step_template__property__color": "Cor", "@sage/xtrem-workflow/nodes__workflow_step_template__property__configData": "Dados de configuração", "@sage/xtrem-workflow/nodes__workflow_step_template__property__description": "Descrição", "@sage/xtrem-workflow/nodes__workflow_step_template__property__factory": "Fábrica", "@sage/xtrem-workflow/nodes__workflow_step_template__property__icon": "Ícone", "@sage/xtrem-workflow/nodes__workflow_step_template__property__isActive": "Está ativo(a)", "@sage/xtrem-workflow/nodes__workflow_step_template__property__serviceOptions": "Opções de serviços", "@sage/xtrem-workflow/nodes__workflow_step_template__property__stepConstructor": "Construtor de etapas", "@sage/xtrem-workflow/nodes__workflow_step_template__property__stepDescriptor": "Descritor de etapas", "@sage/xtrem-workflow/nodes__workflow_step_template__property__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-workflow/nodes__workflow_step_template__property__variant": "<PERSON><PERSON><PERSON>", "@sage/xtrem-workflow/package__name": "Workflow", "@sage/xtrem-workflow/pages__workflow_action_calculate____subtitle": "Calcular", "@sage/xtrem-workflow/pages__workflow_action_calculate____title": "Configuração da ação", "@sage/xtrem-workflow/pages__workflow_action_calculate__calculationSteps____addButtonText": "Adicionar uma etapa de cálculo", "@sage/xtrem-workflow/pages__workflow_action_calculate__calculationSteps____columns__title__isVariable": "É uma variável?", "@sage/xtrem-workflow/pages__workflow_action_calculate__calculationSteps____columns__title__operation": "Operação", "@sage/xtrem-workflow/pages__workflow_action_calculate__calculationSteps____columns__title__value": "Valor do componente", "@sage/xtrem-workflow/pages__workflow_action_calculate__calculationSteps____columns__title__variable": "Variável do componente", "@sage/xtrem-workflow/pages__workflow_action_calculate__calculationSteps____title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-workflow/pages__workflow_action_calculate__mainSection____title": "Detalhes básicos", "@sage/xtrem-workflow/pages__workflow_action_calculate__outputVariableName____title": "Nome da variável de saída", "@sage/xtrem-workflow/pages__workflow_action_calculate__title____helperText": "T<PERSON><PERSON>lo apresentado no diagrama do workflow.", "@sage/xtrem-workflow/pages__workflow_action_calculate__title____title": "Tí<PERSON>lo da ação", "@sage/xtrem-workflow/pages__workflow_action_condition____title": "Condição do componente", "@sage/xtrem-workflow/pages__workflow_action_condition__branchesBlock____title": "<PERSON>fi<PERSON><PERSON>", "@sage/xtrem-workflow/pages__workflow_action_condition__filters____helperText": "<PERSON>uando todas as condições são verdadeiras, a execução continua para a esquerda. Se alguma das condições for avaliada como falsa, a execução continua para a direita.", "@sage/xtrem-workflow/pages__workflow_action_condition__filters____title": "Condição", "@sage/xtrem-workflow/pages__workflow_action_condition__ifFalseBranch____title": "Adicionar o ramo “se falso", "@sage/xtrem-workflow/pages__workflow_action_condition__ifTrueBranch____title": "Adicionar o ramo “se verdadeiro", "@sage/xtrem-workflow/pages__workflow_action_condition__mainSection____title": "Detalhes básicos", "@sage/xtrem-workflow/pages__workflow_action_condition__title____helperText": "T<PERSON><PERSON>lo apresentado no diagrama do workflow.", "@sage/xtrem-workflow/pages__workflow_action_condition__title____title": "<PERSON><PERSON><PERSON><PERSON> da condição", "@sage/xtrem-workflow/pages__workflow_action_condition__variablesBlock____title": "Definição da condição", "@sage/xtrem-workflow/pages__workflow_action_delete_entity____subtitle": "Eliminar uma entidade", "@sage/xtrem-workflow/pages__workflow_action_delete_entity____title": "Configuração da ação", "@sage/xtrem-workflow/pages__workflow_action_delete_entity__dataEntity____columns__title__name": "Tipo de registo", "@sage/xtrem-workflow/pages__workflow_action_delete_entity__dataEntity____columns__title__package__name": "Package", "@sage/xtrem-workflow/pages__workflow_action_delete_entity__dataEntity____columns__title__title": "Tipo de registo", "@sage/xtrem-workflow/pages__workflow_action_delete_entity__dataEntity____title": "Tipo de registo", "@sage/xtrem-workflow/pages__workflow_action_delete_entity__mainSection____title": "Detalhes básicos", "@sage/xtrem-workflow/pages__workflow_action_delete_entity__recordIdToDelete____title": "Variável do ID de registo", "@sage/xtrem-workflow/pages__workflow_action_delete_entity__title____helperText": "T<PERSON><PERSON>lo apresentado no diagrama do workflow.", "@sage/xtrem-workflow/pages__workflow_action_delete_entity__title____title": "Tí<PERSON>lo da ação", "@sage/xtrem-workflow/pages__workflow_action_read_entity____subtitle": "Leitura de um registo", "@sage/xtrem-workflow/pages__workflow_action_read_entity____title": "Configuração da ação", "@sage/xtrem-workflow/pages__workflow_action_read_entity__failIfNotFound____helperText": "Emite um erro se não for encontrado qualquer registo.", "@sage/xtrem-workflow/pages__workflow_action_read_entity__failIfNotFound____title": "Falha se não for encontrado", "@sage/xtrem-workflow/pages__workflow_action_read_entity__mainSection____title": "Detalhes básicos", "@sage/xtrem-workflow/pages__workflow_action_read_entity__nodeFactory____columns__title__name": "Tipo de registo", "@sage/xtrem-workflow/pages__workflow_action_read_entity__nodeFactory____columns__title__package__name": "Package", "@sage/xtrem-workflow/pages__workflow_action_read_entity__nodeFactory____columns__title__title": "Tipo de registo", "@sage/xtrem-workflow/pages__workflow_action_read_entity__nodeFactory____title": "Tipo de registo", "@sage/xtrem-workflow/pages__workflow_action_read_entity__outputVariableName____title": "Nome da variável de saída", "@sage/xtrem-workflow/pages__workflow_action_read_entity__recordKey____title": "<PERSON>ve de registo", "@sage/xtrem-workflow/pages__workflow_action_read_entity__title____helperText": "T<PERSON><PERSON>lo apresentado no diagrama do workflow.", "@sage/xtrem-workflow/pages__workflow_action_read_entity__title____title": "Tí<PERSON>lo da ação", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification____subtitle": "Notificar um utilizador", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification____title": "Configuração da ação", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__actionsForLocale____columns__title___originalIndex": "Index original", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__actionsForLocale____columns__title__link": "Link", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__actionsForLocale____columns__title__style": "Estilo de b<PERSON>ão", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__actionsForLocale____columns__title__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__actionsSection____title": "Ações", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__icon____title": "Ícone", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__level____title": "Tipo", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__mainSection____title": "G<PERSON>", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__notificationDescription____title": "Descrição da notificação", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__notificationTitle____title": "Título da notificação", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__recipients____addButtonText": "Adicionar um destinatário", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__recipients____columns__columns__user__email__title": "Sobrenome", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__recipients____columns__columns__user__email__title__2": "Primeiro nome", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__recipients____columns__columns__user__email__title__3": "e-mail", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__recipients____columns__lookupDialogTitle__user__email": "Selecionar utilizador", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__recipients____columns__placeholder__user__email": "Selecionar utilizador", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__recipients____columns__title__isManuallySet": "Registar manualmente", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__recipients____columns__title__user": "Utilizador", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__recipients____columns__title__user__email": "Utilizador", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__recipients____title": "Recipientes", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__recipientsSection____title": "Recipientes", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__shouldDisplayToast____helperText": "As notificações urgentes são apresentadas como mensagens pop-up", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__shouldDisplayToast____title": "É urgente?", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__title____helperText": "T<PERSON><PERSON>lo apresentado no diagrama do workflow.", "@sage/xtrem-workflow/pages__workflow_action_send_user_notification__title____title": "Tí<PERSON>lo da ação", "@sage/xtrem-workflow/pages__workflow_action_test_stub____subtitle": "Teste stub", "@sage/xtrem-workflow/pages__workflow_action_test_stub____title": "Configuração da ação", "@sage/xtrem-workflow/pages__workflow_action_test_stub__mainSection____title": "Detalhes básicos", "@sage/xtrem-workflow/pages__workflow_action_test_stub__outputVariableName____title": "Nome da variável de saída", "@sage/xtrem-workflow/pages__workflow_action_test_stub__title____helperText": "T<PERSON><PERSON>lo apresentado no diagrama do workflow.", "@sage/xtrem-workflow/pages__workflow_action_test_stub__title____title": "Tí<PERSON>lo da ação", "@sage/xtrem-workflow/pages__workflow_action_update_entity____subtitle": "Atualizar uma entidade", "@sage/xtrem-workflow/pages__workflow_action_update_entity____title": "Configuração da ação", "@sage/xtrem-workflow/pages__workflow_action_update_entity__mainSection____title": "Seleção de dados", "@sage/xtrem-workflow/pages__workflow_action_update_entity__pathToUpdate____title": "Registo a atualizar", "@sage/xtrem-workflow/pages__workflow_action_update_entity__title____helperText": "T<PERSON><PERSON>lo apresentado no diagrama do workflow.", "@sage/xtrem-workflow/pages__workflow_action_update_entity__title____title": "Tí<PERSON>lo da ação", "@sage/xtrem-workflow/pages__workflow_action_update_entity__updateRulesPod____title": "Novo valor", "@sage/xtrem-workflow/pages__workflow_action_wait____subtitle": "Aguardar", "@sage/xtrem-workflow/pages__workflow_action_wait____title": "Configuração da ação", "@sage/xtrem-workflow/pages__workflow_action_wait__mainSection____title": "Detalhes básicos", "@sage/xtrem-workflow/pages__workflow_action_wait__quantity____title": "Quantidade de tempo", "@sage/xtrem-workflow/pages__workflow_action_wait__title____helperText": "T<PERSON><PERSON>lo apresentado no diagrama do workflow.", "@sage/xtrem-workflow/pages__workflow_action_wait__title____title": "Tí<PERSON>lo da ação", "@sage/xtrem-workflow/pages__workflow_action_wait__unit____title": "Unidade", "@sage/xtrem-workflow/pages__workflow_definition____navigationPanel__inlineActions__title__duplicate": "Dup<PERSON><PERSON>", "@sage/xtrem-workflow/pages__workflow_definition____navigationPanel__listItem__id__title": "ID", "@sage/xtrem-workflow/pages__workflow_definition____navigationPanel__listItem__title__title": "Nome", "@sage/xtrem-workflow/pages__workflow_definition____navigationPanel__listItem__titleRight__title": "Ativo", "@sage/xtrem-workflow/pages__workflow_definition____navigationPanel__listItem__triggerDetails__title": "Detalhes do acionador", "@sage/xtrem-workflow/pages__workflow_definition____navigationPanel__listItem__triggerType__title": "Tipo de acionador", "@sage/xtrem-workflow/pages__workflow_definition____navigationPanel__optionsMenu__title": "Todos os workflows", "@sage/xtrem-workflow/pages__workflow_definition____objectTypePlural": "Workflow", "@sage/xtrem-workflow/pages__workflow_definition____objectTypeSingular": "Workflow", "@sage/xtrem-workflow/pages__workflow_definition____title": "Designer de workflow", "@sage/xtrem-workflow/pages__workflow_definition__deleteWorkflowDefinition____title": "Eliminar", "@sage/xtrem-workflow/pages__workflow_definition__designerSection____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-workflow/pages__workflow_definition__eventLog____columns__title__event": "", "@sage/xtrem-workflow/pages__workflow_definition__eventLog____columns__title__message": "", "@sage/xtrem-workflow/pages__workflow_definition__eventLog____columns__title__stepId": "", "@sage/xtrem-workflow/pages__workflow_definition__eventLog____columns__title__timestamp": "", "@sage/xtrem-workflow/pages__workflow_definition__eventLog____title": "", "@sage/xtrem-workflow/pages__workflow_definition__executionSection____title": "Log", "@sage/xtrem-workflow/pages__workflow_definition__flow____title": "Workflow", "@sage/xtrem-workflow/pages__workflow_definition__headerSection____title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-workflow/pages__workflow_definition__id____title": "ID", "@sage/xtrem-workflow/pages__workflow_definition__isActive____title": "Ativo", "@sage/xtrem-workflow/pages__workflow_definition__name____title": "Nome", "@sage/xtrem-workflow/pages__workflow_definition__processes____columns__postfix__duration": "segundos", "@sage/xtrem-workflow/pages__workflow_definition__processes____columns__title__duration": "Duração", "@sage/xtrem-workflow/pages__workflow_definition__processes____columns__title__errorMessages": "<PERSON><PERSON><PERSON>", "@sage/xtrem-workflow/pages__workflow_definition__processes____columns__title__startedAt": "Hora iní<PERSON>", "@sage/xtrem-workflow/pages__workflow_definition__processes____columns__title__status": "Status", "@sage/xtrem-workflow/pages__workflow_definition__processes____columns__title__triggeringUser__displayName": "Acionado por", "@sage/xtrem-workflow/pages__workflow_definition__processes____dropdownActions__title": "Detalhes vista", "@sage/xtrem-workflow/pages__workflow_definition__processes____title": "Rastros (logs)", "@sage/xtrem-workflow/pages__workflow_definition__refreshProcesses____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-workflow/pages__workflow_definition__status____title": "Status", "@sage/xtrem-workflow/pages__workflow_definition__testUser____columns__title__email": "e-mail", "@sage/xtrem-workflow/pages__workflow_definition__testUser____columns__title__firstName": "Primeiro nome", "@sage/xtrem-workflow/pages__workflow_definition__testUser____columns__title__lastName": "Sobrenome", "@sage/xtrem-workflow/pages__workflow_definition__testUser____lookupDialogTitle": "Seleionar um utilizador", "@sage/xtrem-workflow/pages__workflow_definition__testUser____title": "<PERSON><PERSON><PERSON><PERSON> teste", "@sage/xtrem-workflow/pages__workflow_definition__variables____title": "", "@sage/xtrem-workflow/pages__workflow_event_entity_created____subtitle": "Entidade criada", "@sage/xtrem-workflow/pages__workflow_event_entity_created____title": "Configuração de ativação (Trigger)", "@sage/xtrem-workflow/pages__workflow_event_entity_created__conditionSection____title": "Condição", "@sage/xtrem-workflow/pages__workflow_event_entity_created__filters____helperText": "<PERSON>uando to<PERSON> as condições são verdadeiras, a execução continua no caminho abaixo. Se alguma das condições for avaliada como falsa, a execução continua para a direita.", "@sage/xtrem-workflow/pages__workflow_event_entity_created__filters____title": "Condição", "@sage/xtrem-workflow/pages__workflow_event_entity_created__mainSection____title": "Configuração", "@sage/xtrem-workflow/pages__workflow_event_entity_created__nodeFactory____columns__title__name": "Tipo de registo", "@sage/xtrem-workflow/pages__workflow_event_entity_created__nodeFactory____columns__title__package__name": "Package", "@sage/xtrem-workflow/pages__workflow_event_entity_created__nodeFactory____columns__title__title": "Tipo de registo", "@sage/xtrem-workflow/pages__workflow_event_entity_created__nodeFactory____title": "Tipo de registo", "@sage/xtrem-workflow/pages__workflow_event_entity_created__title____helperText": "T<PERSON><PERSON>lo apresentado no diagrama do workflow.", "@sage/xtrem-workflow/pages__workflow_event_entity_created__title____title": "Tí<PERSON>lo acionador", "@sage/xtrem-workflow/pages__workflow_event_entity_deleted____subtitle": "Entidade eliminada", "@sage/xtrem-workflow/pages__workflow_event_entity_deleted____title": "Configuração de ativação (Trigger)", "@sage/xtrem-workflow/pages__workflow_event_entity_deleted__dataEntity____columns__title__name": "Tipo de registo", "@sage/xtrem-workflow/pages__workflow_event_entity_deleted__dataEntity____columns__title__package__name": "Package", "@sage/xtrem-workflow/pages__workflow_event_entity_deleted__dataEntity____columns__title__title": "Tipo de registo", "@sage/xtrem-workflow/pages__workflow_event_entity_deleted__dataEntity____title": "Tipo de registo", "@sage/xtrem-workflow/pages__workflow_event_entity_deleted__mainSection____title": "Detalhes básicos", "@sage/xtrem-workflow/pages__workflow_event_entity_deleted__outputVariableName____title": "Nome da variável de saída", "@sage/xtrem-workflow/pages__workflow_event_entity_deleted__title____helperText": "T<PERSON><PERSON>lo apresentado no diagrama do workflow.", "@sage/xtrem-workflow/pages__workflow_event_entity_deleted__title____title": "Tí<PERSON>lo acionador", "@sage/xtrem-workflow/pages__workflow_event_entity_updated____subtitle": "Entidade atualizada", "@sage/xtrem-workflow/pages__workflow_event_entity_updated____title": "Configuração de ativação (Trigger)", "@sage/xtrem-workflow/pages__workflow_event_entity_updated__conditionSection____title": "Condição", "@sage/xtrem-workflow/pages__workflow_event_entity_updated__filters____helperText": "<PERSON>uando to<PERSON> as condições são verdadeiras, a execução continua no caminho abaixo. Se alguma das condições for avaliada como falsa, a execução continua para a direita.", "@sage/xtrem-workflow/pages__workflow_event_entity_updated__filters____title": "Condição", "@sage/xtrem-workflow/pages__workflow_event_entity_updated__mainSection____title": "Configuração", "@sage/xtrem-workflow/pages__workflow_event_entity_updated__nodeFactory____columns__title__name": "Tipo de registo", "@sage/xtrem-workflow/pages__workflow_event_entity_updated__nodeFactory____columns__title__package__name": "Package", "@sage/xtrem-workflow/pages__workflow_event_entity_updated__nodeFactory____columns__title__title": "Tipo de registo", "@sage/xtrem-workflow/pages__workflow_event_entity_updated__nodeFactory____title": "Tipo de registo", "@sage/xtrem-workflow/pages__workflow_event_entity_updated__title____helperText": "T<PERSON><PERSON>lo apresentado no diagrama do workflow.", "@sage/xtrem-workflow/pages__workflow_event_entity_updated__title____title": "Tí<PERSON>lo acionador", "@sage/xtrem-workflow/pages__workflow_event_test_started____subtitle": "Teste iniciado", "@sage/xtrem-workflow/pages__workflow_event_test_started____title": "Configuração de ativação (Trigger)", "@sage/xtrem-workflow/pages__workflow_event_test_started__mainSection____title": "Detalhes básicos", "@sage/xtrem-workflow/pages__workflow_event_test_started__parameters____addButtonText": "Adicionar <PERSON>", "@sage/xtrem-workflow/pages__workflow_event_test_started__parameters____columns__columns__node__name__title": "Tipo de registo", "@sage/xtrem-workflow/pages__workflow_event_test_started__parameters____columns__columns__node__name__title__2": "Tipo de registo", "@sage/xtrem-workflow/pages__workflow_event_test_started__parameters____columns__columns__node__name__title__3": "Package", "@sage/xtrem-workflow/pages__workflow_event_test_started__parameters____columns__lookupDialogTitle__node__name": "Selecionar nó referenciado", "@sage/xtrem-workflow/pages__workflow_event_test_started__parameters____columns__placeholder__node__name": "Selecionar nó referenciado", "@sage/xtrem-workflow/pages__workflow_event_test_started__parameters____columns__title__name": "Parâmetro - {{name}}", "@sage/xtrem-workflow/pages__workflow_event_test_started__parameters____columns__title__node__name": "<PERSON>ós (Node)", "@sage/xtrem-workflow/pages__workflow_event_test_started__parameters____columns__title__type": "Tipo", "@sage/xtrem-workflow/pages__workflow_event_test_started__parameters____title": "Parâmet<PERSON> de teste", "@sage/xtrem-workflow/pages__workflow_event_test_started__parametersSection____title": "Parâmetros", "@sage/xtrem-workflow/pages__workflow_event_test_started__title____helperText": "T<PERSON><PERSON>lo apresentado no diagrama do workflow.", "@sage/xtrem-workflow/pages__workflow_event_test_started__title____title": "Tí<PERSON>lo acionador", "@sage/xtrem-workflow/pages__workflow_mutation_action____subtitle": "Executar uma mutação graphQL", "@sage/xtrem-workflow/pages__workflow_mutation_action____title": "Configuração da ação", "@sage/xtrem-workflow/pages__workflow_mutation_action__actionParametersPod____title": "Parâmetros", "@sage/xtrem-workflow/pages__workflow_mutation_action__configDescription____title": "Descrição", "@sage/xtrem-workflow/pages__workflow_mutation_action__configRegisterAsSetupNode____helperText": "Registar a ação como um nó de preparação", "@sage/xtrem-workflow/pages__workflow_mutation_action__configRegisterAsSetupNode____title": "Nó de configuração", "@sage/xtrem-workflow/pages__workflow_mutation_action__mainSection____title": "Principal", "@sage/xtrem-workflow/pages__workflow_mutation_action__mutation____columns__title__factory__name": "<PERSON>ós (Node)", "@sage/xtrem-workflow/pages__workflow_mutation_action__mutation____columns__title__name": "Mu<PERSON><PERSON>", "@sage/xtrem-workflow/pages__workflow_mutation_action__mutation____lookupDialogTitle": "Selecionar uma mutação", "@sage/xtrem-workflow/pages__workflow_mutation_action__mutation____title": "Mu<PERSON><PERSON>", "@sage/xtrem-workflow/pages__workflow_mutation_action__mutationArgumentsPod____title": "Argumentos", "@sage/xtrem-workflow/pages__workflow_mutation_action__mutationSection____title": "Mu<PERSON><PERSON>", "@sage/xtrem-workflow/pages__workflow_mutation_action__outputVariableName____title": "Nome da variável de saída", "@sage/xtrem-workflow/pages__workflow_mutation_action__registerActionButton____title": "Registar a ação", "@sage/xtrem-workflow/pages__workflow_mutation_action__registrationSection____title": "Registo", "@sage/xtrem-workflow/pages__workflow_mutation_action__selector____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-workflow/pages__workflow_mutation_action__showAdvancedOptions____helperText": "Mostrar as opções avançadas da ação\n\nAVISO: esta opção está reservada aos utilizadores avançados", "@sage/xtrem-workflow/pages__workflow_mutation_action__showAdvancedOptions____title": "Apresentar opções avançadas", "@sage/xtrem-workflow/pages__workflow_mutation_action__title____helperText": "T<PERSON><PERSON>lo apresentado no diagrama do workflow.", "@sage/xtrem-workflow/pages__workflow_mutation_action__title____title": "Tí<PERSON>lo da ação", "@sage/xtrem-workflow/pages__workflow_process____navigationPanel__bulkActions__title": "", "@sage/xtrem-workflow/pages__workflow_process____navigationPanel__listItem__duration__postfix": "", "@sage/xtrem-workflow/pages__workflow_process____navigationPanel__listItem__duration__title": "", "@sage/xtrem-workflow/pages__workflow_process____navigationPanel__listItem__errorMessage__title": "", "@sage/xtrem-workflow/pages__workflow_process____navigationPanel__listItem__line2__title": "", "@sage/xtrem-workflow/pages__workflow_process____navigationPanel__listItem__startedAt__title": "", "@sage/xtrem-workflow/pages__workflow_process____navigationPanel__listItem__title__title": "", "@sage/xtrem-workflow/pages__workflow_process____navigationPanel__listItem__titleRight__title": "", "@sage/xtrem-workflow/pages__workflow_process____navigationPanel__listItem__triggeredBy__title": "", "@sage/xtrem-workflow/pages__workflow_process____navigationPanel__optionsMenu__title": "Todos os processos", "@sage/xtrem-workflow/pages__workflow_process____objectTypePlural": "Arquivos (logs) Workflow", "@sage/xtrem-workflow/pages__workflow_process____objectTypeSingular": "Arquivo (log) Workflow", "@sage/xtrem-workflow/pages__workflow_process____title": "Arquivos (logs) Workflow", "@sage/xtrem-workflow/pages__workflow_process___id____title": "", "@sage/xtrem-workflow/pages__workflow_process__completedAt____title": "Hora de fim", "@sage/xtrem-workflow/pages__workflow_process__designerSection____title": "", "@sage/xtrem-workflow/pages__workflow_process__duration____postfix": "", "@sage/xtrem-workflow/pages__workflow_process__duration____title": "", "@sage/xtrem-workflow/pages__workflow_process__eventLogsSection____title": "", "@sage/xtrem-workflow/pages__workflow_process__flow____title": "Workflow", "@sage/xtrem-workflow/pages__workflow_process__goToDesigner____title": "", "@sage/xtrem-workflow/pages__workflow_process__headerSection____title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-workflow/pages__workflow_process__olderVersionMessage____content": "", "@sage/xtrem-workflow/pages__workflow_process__startedAt____title": "Hora iní<PERSON>", "@sage/xtrem-workflow/pages__workflow_process__status____title": "Status", "@sage/xtrem-workflow/pages__workflow_process__tableLogs____columns__title__event": "", "@sage/xtrem-workflow/pages__workflow_process__tableLogs____columns__title__message": "", "@sage/xtrem-workflow/pages__workflow_process__tableLogs____columns__title__step": "", "@sage/xtrem-workflow/pages__workflow_process__tableLogs____columns__title__timestamp": "", "@sage/xtrem-workflow/pages__workflow_process__tableLogs____title": "", "@sage/xtrem-workflow/pages__workflow_process__triggeringUser____title": "Acionado por", "@sage/xtrem-workflow/pages__workflow_process__variablesSection____title": "", "@sage/xtrem-workflow/pages__workflow_process__workflow____title": "Workflow", "@sage/xtrem-workflow/pages__workflow_process__workflowDefinitionId____title": "ID da definição do workflow", "@sage/xtrem-workflow/pages__workflow_select_variables_dialog____title": "", "@sage/xtrem-workflow/pages__workflow_select_variables_dialog__fromPath____title": "Selecionar a partir de...", "@sage/xtrem-workflow/pages__workflow_select_variables_dialog__ok____title": "OK", "@sage/xtrem-workflow/pages__workflow_select_variables_dialog__selectedProperties____helperText": "<PERSON><PERSON><PERSON> as propriedades selecionadas são solicitadas à base de dados.", "@sage/xtrem-workflow/pages__workflow_step_template____navigationPanel__listItem__line2__title": "Descrição", "@sage/xtrem-workflow/pages__workflow_step_template____navigationPanel__listItem__title__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-workflow/pages__workflow_step_template____objectTypePlural": "Modelos de etapas de workflow", "@sage/xtrem-workflow/pages__workflow_step_template____title": "Modelo de etapas de workflow", "@sage/xtrem-workflow/pages__workflow_step_template__description____title": "Descrição", "@sage/xtrem-workflow/pages__workflow_step_template__editActionButton____title": "Editar a ação", "@sage/xtrem-workflow/pages__workflow_step_template__generalSection____title": "G<PERSON>", "@sage/xtrem-workflow/pages__workflow_step_template__icon____title": "Ícone", "@sage/xtrem-workflow/pages__workflow_step_template__isActive____title": "Ativo", "@sage/xtrem-workflow/pages__workflow_step_template__title____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-workflow/permission__manage__name": "Gestão", "@sage/xtrem-workflow/permission__read__name": "<PERSON>r", "@sage/xtrem-workflow/service_options__workflow__name": "Workflow", "@sage/xtrem-workflow/service_options__workflow_advanced__name": "Workflow avançado", "@sage/xtrem-workflow/service_options__workflow_option__name": "Workflow opção", "@sage/xtrem-workflow/time-unit-d": "<PERSON><PERSON>", "@sage/xtrem-workflow/time-unit-h": "<PERSON><PERSON>", "@sage/xtrem-workflow/time-unit-m": "<PERSON><PERSON><PERSON>", "@sage/xtrem-workflow/time-unit-ms": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-workflow/time-unit-s": "<PERSON><PERSON><PERSON>", "@sage/xtrem-workflow/workflow_error_empty_workflow": "É necessário adicionar um acionador.", "@sage/xtrem-workflow/workflow_error_no_property_to_update": "É necessário adicionar uma propriedade para atualizar.", "@sage/xtrem-workflow/workflow_mutation_action_argument_not_found": "Esta ação refere-se ao argumento: {{argumentName}}, que não existe na mutação: {{mutationName}}.", "@sage/xtrem-workflow/workflow_mutation_argument_not_found": "O argumento de mutação não está definido na configuração da ação: {{argument}}.", "@sage/xtrem-workflow/workflow_mutation_argument_type_mismatch": "O argumento da mutação tem uma incompatibilidade de tipo: {{argument}}. Esperado: {{expectedType}}, obtido: {{actualType}}.", "@sage/xtrem-workflow/workflow_unknown_mutation": "A mutação não existe: {{mutationName}}.", "@sage/xtrem-workflow/workflow-delete-definition-confirm-body": "", "@sage/xtrem-workflow/workflow-delete-definition-confirm-title": "", "@sage/xtrem-workflow/workflow-dialog-title-error": "", "@sage/xtrem-workflow/workflow-entity-created-condition-not-an-array": "as condições não são uma matriz", "@sage/xtrem-workflow/workflow-entity-created-default-title": "{{factoryName}} criado", "@sage/xtrem-workflow/workflow-entity-created-no-property-selection": "Configuração de evento inválida: a seleção de propriedades é necessária quando é fornecido um filtro", "@sage/xtrem-workflow/workflow-entity-created-step-variables-not-an-array": "stepVariables não é uma matriz", "@sage/xtrem-workflow/workflow-entity-deleted-default-title": "{{factoryName}} eliminado", "@sage/xtrem-workflow/workflow-entity-updated-condition-not-an-array": "as condições não são uma matriz", "@sage/xtrem-workflow/workflow-entity-updated-default-title": "{{factoryName}} atualizado", "@sage/xtrem-workflow/workflow-entity-updated-no-property-selection": "Configuração de evento inválida: a seleção de propriedades é necessária quando é fornecido um filtro", "@sage/xtrem-workflow/workflow-entity-updated-step-variables-not-an-array": "stepVariables não é uma matriz", "@sage/xtrem-workflow/workflow-error-cannot-unselect-root-property": "", "@sage/xtrem-workflow/workflow-error-event-cant-have-a-parent": "", "@sage/xtrem-workflow/workflow-error-flow-has-cycle": "", "@sage/xtrem-workflow/workflow-error-more-than-one-start-node": "", "@sage/xtrem-workflow/workflow-error-no-description": "", "@sage/xtrem-workflow/workflow-error-no-parent": "", "@sage/xtrem-workflow/workflow-error-no-start-node": "", "@sage/xtrem-workflow/workflow-error-no-title": "", "@sage/xtrem-workflow/workflow-error-start-topics-forbidden": "", "@sage/xtrem-workflow/workflow-error-too-few-outputs": "", "@sage/xtrem-workflow/workflow-error-too-many-outputs": "", "@sage/xtrem-workflow/workflow-send-user-notification-error-no-notification-description": "É necessária uma descrição da notificação", "@sage/xtrem-workflow/workflow-send-user-notification-error-no-notification-title": "É necessário um título de notificação", "@sage/xtrem-workflow/workflow-variable-not-found": "A variável não existe: {{variableName}}.", "@sage/xtrem-workflow/workflow-variable-not-found-with-suggestion": "A variável não existe: {{variableName}}. Referia-se a: {{closestVariable}}?"}