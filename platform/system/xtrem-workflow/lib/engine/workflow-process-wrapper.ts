import { WorkflowProcessStatus } from '../enums/workflow-process-status';
import { FlowWrapper } from './flow-wrapper';
import { WorkflowEngine } from './workflow-engine';
import { WorkflowProcessState } from './workflow-process-state';

export interface WorkflowProcessWrapperData {
    id: string;
    status: WorkflowProcessStatus;
    containerId: string;
    startNotificationId: string;
    originId: string;
    triggeringUser: { email: string };
    loginEmail: string;
    state: WorkflowProcessState;
}

/**
 * Wrapper around workflow process data.
 *
 * This wrapper class allows us to manipulate a workflow process node (its status, state, ...)
 * without the overhead of a Node (properties returning Promise, properties assigned with node.$.set, ...).
 */
export class WorkflowProcessWrapper {
    /** The id of the workflow process */
    readonly processId: string;

    /** The status of the workflow process */
    readonly status: WorkflowProcessStatus;

    /** The id of the container that runs the process */
    containerId: string;

    /** The id of the notification that started the process (unique to this process) */
    readonly startNotificationId: string;

    /** The origin id (common to all the workflow processes triggered by the same source event) */
    readonly originId: string;

    /** The email of the user who executes the workflow */
    readonly triggeringUser: { email: string };

    /** The email of the logged user */
    readonly loginEmail: string;

    /* The state of the workflow process */
    state: WorkflowProcessState;

    constructor(
        /** The wrapper of the flow that the process is running */
        readonly flowWrapper: FlowWrapper,

        data: WorkflowProcessWrapperData,
    ) {
        this.processId = data.id;
        this.status = data.status;
        this.containerId = data.containerId;
        this.startNotificationId = data.startNotificationId;
        this.originId = data.originId;
        this.triggeringUser = data.triggeringUser;
        this.state = data.state;
        this.loginEmail = data.loginEmail;
    }

    /** Get the engine that runs the process */
    get engine(): WorkflowEngine {
        return this.flowWrapper.engine;
    }

    /** Get the tenant id of the process */
    get tenantId(): string {
        return this.flowWrapper.tenantId;
    }

    /** Get the definition id of the process */
    get definitionId(): string {
        return this.flowWrapper.definitionId;
    }
}
