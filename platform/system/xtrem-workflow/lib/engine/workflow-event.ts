import { ValidationContext } from '@sage/xtrem-core';
import { WorkflowEventConfig } from '../shared-functions';
import { WorkflowStep } from './workflow-step';

/**
 * Abstract base class for workflow events.
 * A workflow event is a workflow step that starts a workflow.
 */
export abstract class WorkflowEvent<ConfigT extends WorkflowEventConfig, PrivateStateT = unknown> extends WorkflowStep<
    ConfigT,
    PrivateStateT
> {
    override controlInputEdges(cx: ValidationContext): void {
        const inputEdges = this.flowWrapper.getInputEdges(this.nodeId);
        if (inputEdges.length > 0) {
            this.addControlError(
                cx,
                cx.localize(
                    '@sage/xtrem-workflow/workflow-error-event-cant-have-a-parent',
                    'An event cannot have a parent.',
                ),
            );
        }
    }
}
