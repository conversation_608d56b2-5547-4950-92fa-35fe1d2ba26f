import { ValidationContext } from '@sage/xtrem-core';
import { WorkflowActionConfig } from '../shared-functions';
import { WorkflowStep } from './workflow-step';

/**
 * Abstract base class for workflow actions.
 *
 * A workflow action is a workflow step that is triggered by one or more other workflow steps
 * and that performs an action.
 */
export abstract class WorkflowAction<
    ConfigT extends WorkflowActionConfig,
    PrivateStateT = unknown,
> extends WorkflowStep<ConfigT, PrivateStateT> {
    override controlInputEdges(cx: ValidationContext): void {
        const inputEdges = this.flowWrapper.getInputEdges(this.nodeId);
        if (inputEdges.length === 0) {
            this.addControlError(
                cx,
                cx.localize(
                    '@sage/xtrem-workflow/workflow-error-no-parent',
                    'An action needs to have at least one parent.',
                ),
            );
        }
    }

    private controlStartTopics(cx: ValidationContext): void {
        if (this.stepFactory.descriptor.startTopics?.length) {
            this.addControlError(
                cx,
                cx.localize(
                    '@sage/xtrem-workflow/workflow-error-start-topics-forbidden',
                    'Start topics are not allowed in action nodes.',
                ),
            );
        }
    }

    /**
     * Controls the step variables.
     */
    private _controlStepVariables(cx: ValidationContext): void {
        const config = this.resolveConfig();
        (config.stepVariables || []).forEach(variable => {
            if (this.getVariableDefinition(variable.path) === undefined) {
                this.addControlError(cx, `variable ${variable.path} is not defined.`);
            }
            // The step variables may contain variables created by the node itself
            // if so, their path will start from the node's outputVariableName.
            if (
                config.outputVariableName === variable.path ||
                variable.path.startsWith(`${config.outputVariableName}.`)
            ) {
                return;
            }
            // A step variable must be based on the outputVariableName of another step
            const definingNodes = this.flowWrapper.getDefiningNodes(variable.path);
            if (definingNodes.length === 0) {
                const nodeWithClosestOutputPath = this.flowWrapper.getClosestDefiningNode(variable.path);
                if (nodeWithClosestOutputPath == null) {
                    this.addControlError(
                        cx,
                        cx.localize(
                            '@sage/xtrem-workflow/workflow-variable-not-found',
                            'Variable does not exist: {{variableName}}.',
                            { variableName: variable.path },
                        ),
                    );
                } else {
                    const parts = variable.path.split('.');
                    parts[0] = nodeWithClosestOutputPath.data?.outputVariableName ?? '';
                    this.addControlError(
                        cx,
                        cx.localize(
                            '@sage/xtrem-workflow/workflow-variable-not-found-with-suggestion',
                            'Variable does not exist: {{variableName}}. Did you mean: {{closestVariable}}?',
                            { variableName: variable.path, closestVariable: parts.join('.') },
                        ),
                    );
                }
            }
        });
    }

    override async control(cx: ValidationContext): Promise<void> {
        await super.control(cx);
        this.controlStartTopics(cx);
        this._controlStepVariables(cx);
    }
}
