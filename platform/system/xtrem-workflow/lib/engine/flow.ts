/**
 * The interfaces below are subsets of the interfaces from reactflow that the server side needs.
 *
 * The FlowDiagram.data property is a Flow object and contains the full reactflow data.
 * The server side does not modify this data and only uses the small subset defined by the interfaces below.
 */

import { WorkflowStepConfig } from '../shared-functions';

/**
 * The FlowNode interface is the subset of reactflow's Node interface that the server side needs.
 */
export interface FlowNode<ConfigT extends WorkflowStepConfig = WorkflowStepConfig> {
    id: string;
    data: ConfigT;
    type: string;
}

/**
 * The FlowEdge interface is the subset of reactflow's Edge interface that the server side needs.
 */
export interface FlowEdge {
    id: string;
    source: string;
    target: string;
    sourceHandle: string;
    // Steps have only one input handle, so we don't need the targetHandle property.
    // targetHandle: string;
}

/**
 * The Flow interface is the subset of reactflow's Flow interface that the server side needs.
 */
export interface Flow {
    nodes: FlowNode[];
    edges: FlowEdge[];
}
