import {
    Application,
    asyncArray,
    Context,
    WorkflowResult,
    WorkflowRunOptions,
    WorkflowStartEvent,
    WorkflowStepDescriptor,
    WorkflowStepInterface,
} from '@sage/xtrem-core';
import { uniq } from 'lodash';
import { WorkflowStepTemplate } from '../nodes';
import { WorkflowRunner } from '../test/workflow-runner';
import { WorkflowEngine } from './workflow-engine';

/**
 * @internal
 *
 * The workflow manager that is injected in the application via CoreHooks.createWorkflowManager
 */
export class WorkflowManager {
    readonly #engine: WorkflowEngine;

    constructor(readonly application: Application) {
        this.#engine = new WorkflowEngine(application);
    }

    get engine(): WorkflowEngine {
        return this.#engine;
    }

    registerWorkflowStepConstructor(packageName: string, workflowStep: WorkflowStepInterface): void {
        this.engine.registerWorkflowStepConstructor(packageName, workflowStep);
    }

    /**
     * Returns the active workflow step descriptors.
     */
    // eslint-disable-next-line class-methods-use-this
    async getWorkflowStepDescriptors(context: Context): Promise<WorkflowStepDescriptor[]> {
        const allStepConfigurations = await context
            .query(WorkflowStepTemplate, {
                filter: {
                    isActive: true,
                },
            })
            .toArray();

        const descriptors: WorkflowStepDescriptor[] = [];
        await asyncArray(allStepConfigurations).forEach(async stepConfig => {
            if (!(await stepConfig.areServiceOptionsEnabled(context))) return;
            descriptors.push(await stepConfig.getWorkflowStepDescriptor(context));
        });

        return descriptors;
    }

    getWorkflowStepDescriptor(key: string): WorkflowStepDescriptor {
        return this.engine.findStepFactory(key).descriptor;
    }

    getWorkflowTopics(): string[] {
        return uniq(Object.values(this.#engine.getWorkflowStepFactories()).flatMap(stepFactory => stepFactory.topics));
    }

    runTest(startEvent: WorkflowStartEvent, options: WorkflowRunOptions): Promise<WorkflowResult> {
        return new WorkflowRunner(this.application).run(startEvent, options);
    }

    start(): Promise<void> {
        return this.#engine.start();
    }
}
