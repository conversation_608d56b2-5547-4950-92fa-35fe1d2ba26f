import { datetime, Dict } from '@sage/xtrem-core';

/**
 * @internal
 * Status of workflow step
 */
export type WorkflowStepStatus =
    /** Step is inactive because at least one of its input edges has not been traversed yet */
    | 'inactive'
    /** All input edges have been reached and at least one of them is triggered */
    | 'triggered'
    /** All input edges have been reached and they are all ignored */
    | 'ignored'
    /** Step is running */
    | 'running'
    /** Step is suspended and is waiting for input */
    | 'suspended'
    /** Step is shut down because its container was shut down  */
    | 'shutDown'
    /** Step completed successfully  */
    | 'success'
    /** Step failed */
    | 'error';

/**
 * @internal
 * Status of input edges
 */
export type WorkflowInputStatus =
    /** Still waiting for input */
    | 'unknown'
    /** At least one ancestor chain leading to this anchor was triggered */
    | 'triggered'
    /** All the ancestor chains leading to this anchor were ignored */
    | 'ignored';

/**
 * @internal
 * State of a workflow step.
 *
 * This state is stored in the process state.
 * It is updated every time the step execution progresses.
 */
export interface WorkflowStepState<PrivateStateT = unknown> {
    /** Status of the step */
    status: WorkflowStepStatus;

    /** Error message if status is error */
    errorMessage?: string;

    /** Enter timestamp */
    enteredAt: datetime;

    /** Exit timestamp */
    exitedAt?: datetime;

    /** Private data that complex steps need to maintain across wakeUp calls */
    privateState: PrivateStateT;

    /** Statuses of the input edges */
    inputStatuses: Dict<WorkflowInputStatus>;
}
