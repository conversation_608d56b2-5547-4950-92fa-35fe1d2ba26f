import { Dict } from '@sage/xtrem-core';
import { WorkflowLogEntry } from '../shared-functions/workflow-log';
import { WorkflowStepState } from './workflow-step-state';

/**
 * State of a workflow process
 *
 * This state is stored in the state property of the WorkflowProcess node.
 */
export interface WorkflowProcessState {
    /** Steps states */
    stepStates: Dict<WorkflowStepState>;

    /** Locale */
    locale: string;

    /** Values of the variables set by steps */
    variables: Dict<any>;

    /** Ids of the flow nodes that have some input pending */
    readyNodeIds: Dict<boolean>;

    /** Payloads received and not processed yet, indexed by node id  */
    pendingPayloads: Dict<any>;

    /** Contexts for suspended states that expect a result through an event  */
    suspendedStepContexts: Dict<any>;

    /** Number of uncaught errors in steps  */
    errorCount: number;

    /** Event log */
    log: WorkflowLogEntry[];
}
