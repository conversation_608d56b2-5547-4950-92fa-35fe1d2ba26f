import { Application, asyncArray, BusinessRuleError, Dict, LogicError, ValidationContext } from '@sage/xtrem-core';
import { WorkflowVariable } from '@sage/xtrem-shared';
import * as levenshtein from 'fast-levenshtein';
import * as _ from 'lodash';
import { Flow, FlowEdge, FlowNode } from './flow';
import { WorkflowEngine } from './workflow-engine';
import { WorkflowProcessWrapper } from './workflow-process-wrapper';
import { WorkflowStep } from './workflow-step';
import { WorkflowInputStatus } from './workflow-step-state';

/**
 * @internal
 * Wrapper around a Flow diagram.
 *
 * This wrapper class does not modify the Flow object, it only provides methods to access and analyze it.
 */
export class FlowWrapper {
    /** A dictionary that maps node IDs to FlowNode objects. */
    readonly nodesById: Dict<FlowNode> = {};

    /** A dictionary that maps edge IDs to FlowEdge objects. */
    readonly edgesById: Dict<FlowEdge> = {};

    /** A dictionary that maps edge IDs to FlowEdge objects. */
    readonly #variableDefinitionsByPath: Dict<WorkflowVariable> = {};

    constructor(
        readonly engine: WorkflowEngine,
        readonly tenantId: string,
        readonly definitionId: string,
        readonly flow: Flow,
    ) {
        flow.nodes.forEach(node => {
            if (this.nodesById[node.id]) throw new BusinessRuleError(`duplicate node: ${node.id}`);
            this.nodesById[node.id] = node;

            const variables = [...(node.data?.stepVariables ?? [])];

            const outputVariables = (node.data?.outputVariables || []) as WorkflowVariable[];
            if (outputVariables && outputVariables.length > 0) {
                // Compatibility code: formerly the step could have an array of outputVariables
                variables.push(...outputVariables);
            }

            variables.forEach(variable => {
                this.#variableDefinitionsByPath[variable.path] = variable;
            });
        });
        flow.edges.forEach(edge => {
            if (this.edgesById[edge.id]) throw new BusinessRuleError(`duplicate edge: ${edge.id}`);
            this.edgesById[edge.id] = edge;
        });
        this.freeze();
    }

    /** Retrieves the start node of a given flow. */
    static getStartNode(flow: Flow): FlowNode {
        if (flow.nodes == null) throw new LogicError('Invalid workflow - the flow is empty');
        const startNodes = flow.nodes.filter(node => flow.edges.every(edge => edge.target !== node.id));
        if (startNodes.length === 0) {
            throw new LogicError('Invalid workflow - flow does not have any start node');
        }
        if (startNodes.length > 1) {
            const nodeIds = startNodes.map(node => `'${node.id}'`).join(', ');
            throw new LogicError(`Invalid workflow - flow has more than one start nodes: ${nodeIds}`);
        }
        return startNodes[0];
    }

    /** Retrieves the start node of the flow. */
    getStartNode(): FlowNode {
        return FlowWrapper.getStartNode(this.flow);
    }

    /** Creates a new LogicError with the specified message prefixed by the flow id. */
    logicError(message: string): LogicError {
        return new LogicError(`${this.definitionId}: ${message}`);
    }

    /** Finds a FlowNode by its ID. Throws if the ID is not found. */
    findNode(id: string): FlowNode {
        const node = this.nodesById[id];
        if (!node) throw this.logicError(`node ${id} not found`);
        return node;
    }

    /** Finds a FlowEdge by its ID. Throws if the ID is not found. */
    findEdge(id: string): FlowEdge {
        const edge = this.edgesById[id];
        if (!edge) throw this.logicError(`edge ${id} not found`);
        return edge;
    }

    /** Retrieves the input edges of a node. */
    getInputEdges(nodeId: string): FlowEdge[] {
        return this.flow.edges.filter(edge => edge.target === nodeId);
    }

    /** Retrieves the output edges of a node. */
    getOutputEdges(nodeId: string): FlowEdge[] {
        return this.flow.edges.filter(edge => edge.source === nodeId);
    }

    /**
     * Retrieves the nodes that define a variable at a given path.
     */
    getDefiningNodes(path: string): FlowNode[] {
        return Object.values(this.nodesById).filter(node => {
            if (node.data == null) return false;
            const outputVariableName = node.data.outputVariableName;
            if (outputVariableName == null) return false;
            return path.startsWith(`${outputVariableName}.`) || path.startsWith(`$old.${outputVariableName}.`);
        });
    }

    getClosestDefiningNode(path: string): FlowNode | undefined {
        const firstPathSegment = path.split('.')[0];
        const nodes = Object.values(this.nodesById);
        //
        const distances = nodes.map(node =>
            node.data?.outputVariableName == null
                ? Infinity
                : levenshtein.get(node.data?.outputVariableName, firstPathSegment),
        );
        const nodeWithClosestOutputVariable = nodes[distances.indexOf(Math.min(...distances))];
        return nodeWithClosestOutputVariable;
    }

    /** Creates a new WorkflowStep instance for a given node. */
    createWorflowStep(processWrapper: WorkflowProcessWrapper, nodeId: string): WorkflowStep {
        const node = this.findNode(nodeId);
        const stepFactory = processWrapper.engine.findStepFactory(node.type);
        // eslint-disable-next-line new-cap
        return new stepFactory.stepConstructor(processWrapper, node);
    }

    /** Detects cycles in the flow diagram. Returns the nodes that are part of the cycle. */
    private detectCycle(): FlowNode[] {
        const { nodes } = this.flow;
        const removed = {} as Dict<true>;

        let found = true;
        while (found) {
            found = false;
            nodes
                .filter(node => !removed[node.id])
                // eslint-disable-next-line @typescript-eslint/no-loop-func
                .forEach(node => {
                    if (this.getInputEdges(node.id).every(edge => removed[edge.source])) {
                        removed[node.id] = true;
                        found = true;
                    }
                });
        }
        return nodes.filter(node => !removed[node.id]);
    }

    /** Controls the flow diagram. */
    async control(application: Application, cx: ValidationContext): Promise<void> {
        if (this.flow == null || this.flow.nodes.length === 0) {
            cx.addError(
                cx.localize('@sage/xtrem-workflow/workflow_error_empty_workflow', 'You need to add a trigger.'),
            );
            return;
        }

        const { nodes, edges } = this.flow;

        // Check that all edges reference valid nodes
        edges.forEach(edge => {
            if (!this.nodesById[edge.source]) {
                cx.addError(`Edge ${edge.id}: source not found: ${edge.source}.`);
            }
            if (!this.nodesById[edge.target]) {
                cx.addError(`Edge ${edge.id}: target not found: ${edge.target}.`);
            }
            if (!edge.sourceHandle) {
                cx.addError(`Edge ${edge.id}: sourceHandle is missing.`);
            }
        });

        // Check that flow has exactly 1 start node
        const sourceNodes = nodes.filter(node => !edges.some(edge => edge.target === node.id));
        if (sourceNodes.length === 0) {
            cx.addError(
                cx.localize(
                    '@sage/xtrem-workflow/workflow-error-no-start-node',
                    'The flow does not have any start node.',
                ),
            );
            return;
        }
        if (sourceNodes.length > 1) {
            const nodeIds = sourceNodes.map(node => `'${node.id}'`).join(', ');
            cx.addError(
                cx.localize(
                    '@sage/xtrem-workflow/workflow-error-more-than-one-start-node',
                    'Flow has more than one start nodes: {{nodeIds}}',
                    { nodeIds },
                ),
            );
            return;
        }

        // Check that the flow does not contain any cycles
        const cycle = this.detectCycle();
        if (cycle.length > 0) {
            cx.addError(
                cx.localize(
                    '@sage/xtrem-workflow/workflow-error-flow-has-cycle',
                    'Flow has a cycle on node: {{nodeId}}',
                    { nodeId: cycle[0].id },
                ),
            );
            return;
        }

        const locale = cx.context.currentLocale;

        // Control all the nodes
        await asyncArray(nodes).forEach(async node => {
            const stepFactory = WorkflowEngine.fromApplication(application).findStepFactory(node.type);
            // We need to create a new validationContext for each node to have the correct path (the last part of the path is the node id)
            const validationContextForNode = new ValidationContext(cx.context, [...cx.path, node.id]);
            await stepFactory.createStepForControl(this, node, locale).control(validationContextForNode);
        });
    }

    /** Returns the initial status of the inputs of a node. Initally, all inputs are unknown. */
    getInitialInputStatuses(node: FlowNode): Dict<WorkflowInputStatus> {
        const edges = this.getInputEdges(node.id);
        return _.fromPairs(edges.map(edge => [edge.id, 'unknown']));
    }

    getVariableDefinition(path: string): WorkflowVariable {
        const variable = this.#variableDefinitionsByPath[path];
        if (!variable) throw this.logicError(`variable definition not found: ${path}`);
        return variable;
    }

    getPrefixedVariables(prefix: string): WorkflowVariable[] {
        return Object.values(this.#variableDefinitionsByPath).filter(variable => variable.path.startsWith(prefix));
    }

    /** Freezes the flow diagram and all its nodes and edges. */
    static freezeFlow(flow: Flow): void {
        Object.freeze(flow);
        Object.freeze(flow.nodes);
        Object.freeze(flow.edges);
        flow.nodes.forEach(Object.freeze);
        flow.edges.forEach(Object.freeze);
    }

    /** Freezes the flow helper and all its nodes and edges. */
    private freeze(): void {
        FlowWrapper.freezeFlow(this.flow);
        Object.freeze(this.nodesById);
        Object.freeze(this.edgesById);
        Object.freeze(this);
    }
}
