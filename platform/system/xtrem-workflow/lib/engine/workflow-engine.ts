import {
    Application,
    ConfigManager,
    ContainerManager,
    Context,
    ContextOptions,
    Datetime,
    Dict,
    LogicError,
    NodeQueryFilter,
    NodeSelectResult,
    NodeSelector,
    WorkflowStepInterface,
    asyncArray,
    datetime,
    gracefulShutdown,
    integer,
    sleepMillis,
    toInteger,
} from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';

import { AnyValue, AuthConfig } from '@sage/xtrem-shared';
import { merge } from 'lodash';
import { nanoid } from 'nanoid';
import { WorkflowStepFactory } from '../index';
import { WorkflowDefinition, WorkflowProcess } from '../nodes';
import { WorkflowLogEntry } from '../shared-functions/workflow-log';
import { FlowWrapper } from './flow-wrapper';
import { logger } from './logger';
import { WorkflowManager } from './workflow-manager';
import { WorkflowNotificationBroker } from './workflow-notification-broker';
import { WorkflowProcessWrapper, WorkflowProcessWrapperData } from './workflow-process-wrapper';
import { WorkflowStepConstructor } from './workflow-step-factory';
import { WorkflowStepState } from './workflow-step-state';

export interface StartProcessOptions {
    flowWrapper: FlowWrapper;
    startTopic: string;
    startNotificationId: string;
    originId: string;
    payload: Dict<unknown>;
    userEmail: string;
    loginEmail: string;
    locale: string;
}

/**
 * @internal
 *
 * The workflow engine is the core of the workflow system. It manages the workflow steps and processes.
 */
export class WorkflowEngine {
    /* The workflow step factories, indexed by step key. */
    readonly #stepFactories: Dict<WorkflowStepFactory> = {};

    /** The notification broker for workflow events. */
    readonly notificationBroker = new WorkflowNotificationBroker(this);

    /** The active workflow processes, indexed by process ID. */
    readonly activeProcesses: Dict<WorkflowProcessWrapper> = {};

    #running = false;

    constructor(
        /** The application object. */
        readonly application: Application,
    ) {}

    /** Registers a workflow step into the engine's #stepFactories. */
    registerWorkflowStepConstructor(packageName: string, workflowStep: WorkflowStepInterface): void {
        const stepDescriptor = workflowStep.descriptor;
        // Abstract step classes don't have a static type member. Ignore them.
        if (!stepDescriptor) return;

        const stepConstructor = workflowStep.descriptor.key;
        if (this.#stepFactories[stepConstructor])
            throw new LogicError(
                `Workflow step ${stepConstructor} in ${packageName} already declared in ${this.#stepFactories[stepConstructor].packageName}`,
            );
        this.#stepFactories[stepConstructor] = new WorkflowStepFactory(
            this,
            packageName,
            workflowStep as WorkflowStepConstructor,
        );
    }

    /**
     * Returns true if the workflow step factory is registered in the engine.
     * @param stepConstructor
     */
    hasStepFactory(stepConstructor: string): boolean {
        return !!this.#stepFactories[stepConstructor];
    }

    /** Finds a workflow step factory by key. */
    findStepFactory(stepConstructor: string): WorkflowStepFactory {
        let stepFactory = this.#stepFactories[stepConstructor];
        if (!stepFactory) {
            // The step constructor maybe something like 'xxx/yyy'
            // where xxx is a stepConstructor and yyy a variant
            const parts = stepConstructor.split('/');
            if (parts.length === 2) {
                stepFactory = this.#stepFactories[parts[0]];
            }
        }
        if (!stepFactory) throw new LogicError(`Workflow step ${stepConstructor} not found`);
        return stepFactory;
    }

    /** Returns the workflow step factories */
    getWorkflowStepFactories(): WorkflowStepFactory[] {
        return Object.values(this.#stepFactories);
    }

    static getProcessLocale(context: Context): string {
        const locale = context.currentLocale;
        if (!locale) return 'en-US';
        // Allow 'base' locale for tests but not in prod to avoid crashes in Intl formatting functions that are used by reporting.
        if (locale === 'base' && context.application.applicationType !== 'test') return 'en-US';
        return locale;
    }

    /**
     * Execute a select query for workflow definitions.
     */
    static async selectWorkflowDefinitions<SelectorT extends NodeSelector<WorkflowDefinition>>(
        context: Context,
        selector: SelectorT,
        filter: NodeQueryFilter<WorkflowDefinition>,
    ): Promise<NodeSelectResult<WorkflowDefinition, SelectorT>[]> {
        const defs = (await context.unsafeWithRootUser(() =>
            context.select(
                WorkflowDefinition,
                { ...(selector as any), ...{ status: true, testUser: true } },
                { filter },
            ),
        )) as NodeSelectResult<WorkflowDefinition, SelectorT>[];

        return asyncArray(defs)
            .filter(async def => {
                const defAsAny = def as any;
                if (defAsAny.status === 'production') return true;
                if (defAsAny.testUser === (await context.loginUser)?._id) return true;
                return false;
            })
            .toArray();
    }

    /** Starts a workflow process in a given context, for a given flow, topic, and payload. */
    private async startProcessWithContext(
        context: Context,
        { flowWrapper, startTopic, startNotificationId, originId, payload }: StartProcessOptions,
    ): Promise<WorkflowProcessWrapper | null> {
        const startNode = flowWrapper.getStartNode();
        const stepFactory = this.findStepFactory(startNode.type);
        if (startNode.data.topic !== startTopic) throw new LogicError(`${stepFactory.nodeType}: topic mismatch`);

        if (startNode.data.topic === 'WorkflowProcess/testStarted' && payload.workflow !== flowWrapper.definitionId) {
            // test-started event is not for this workflow
            return null;
        }

        const definitions = await WorkflowEngine.selectWorkflowDefinitions(
            context,
            { isActive: true, _id: true },
            { id: flowWrapper.definitionId },
        );
        if (!definitions[0]?.isActive) {
            logger.warn(`Ignoring inactive workflow ${flowWrapper.definitionId}`);
            return null;
        }
        if (definitions.length === 0) throw new LogicError('Workflow definition not found');
        if (definitions.length !== 1) throw new LogicError('More than one workflow definition found');

        // When the workflow was designed by an administrator, he may have set an execution user
        // if so, we have to use it to run the workflow, else we are using the user that triggered the event
        const triggeringUserMail = (await context.getUserInfo()).email;
        if (triggeringUserMail == null)
            throw new LogicError('Cannot start workflow process: triggering user not found');

        const stepData: WorkflowStepState = {
            status: 'triggered',
            enteredAt: datetime.now(true),
            exitedAt: datetime.now(true),
            privateState: {},
            inputStatuses: {},
        };

        const locale = WorkflowEngine.getProcessLocale(context);

        const processData = {
            id: nanoid(),
            status: 'running',
            containerId: ContainerManager.containerId,
            startNotificationId,
            originId,
            triggeringUser: { email: triggeringUserMail },
            loginEmail: (await context.loginUser)?.email,
            state: {
                locale,
                stepStates: { [startNode.id]: stepData },
                readyNodeIds: { [startNode.id]: true },
                pendingPayloads: { [startNode.id]: payload },
                suspendedStepContexts: {},
                variables: {},
                errorCount: 0,
                log: [] as WorkflowLogEntry[],
            },
        } as WorkflowProcessWrapperData;

        let processWrapper = new WorkflowProcessWrapper(flowWrapper, processData);

        // eslint-disable-next-line new-cap
        const step = new stepFactory.stepConstructor(processWrapper, startNode);

        try {
            // If the step is not accepted, return before creating the WorkflowProcess object
            if (!(await step.accept())) {
                return null;
            }
        } catch (error) {
            logger.error(`Test to accept workflow execution failed: ${error.stack}`);
            stepData.status = 'error';
            stepData.errorMessage = error.message;

            processData.status = 'error';
            processData.state.errorCount += 1;
            processData.state.log.push(step.getLogEntry('error', error.message));
            // recreate the processWrapper with the new data
            processWrapper = new WorkflowProcessWrapper(flowWrapper, processData);
            // Continue and create a process so that the user can find the error in the process log.
        }

        step.logVerbose(() => `assigning process variable values: ${JSON.stringify(payload)}`);
        merge(processWrapper.state.variables, payload);

        const createData = {
            id: processWrapper.processId,
            ...processWrapper,
            triggeringUser: `#${triggeringUserMail}`,
            definition: `#${flowWrapper.definitionId}`,
        };
        await context.unsafeWithRootUser(async () => {
            const process = await context.create(WorkflowProcess, createData);
            await process.$.save();
        });

        if (processData.status === 'error') {
            step.logError(`Error starting process: ${stepData.errorMessage}`);
            return null;
        }

        this.activeProcesses[processWrapper.processId] = processWrapper;
        step.logInfo(`Started process ${processWrapper.processId}`);
        // TODO: we don't need this event for now - enabe or remove later
        // await context.notify('WorkflowProcess/started', _.pick(processData, 'id'));

        return processWrapper;
    }

    /**
     * Builds the AuthConfig part of the options for a context.
     */
    static buildAuthConfigForContext(loginUser: string, userEmail: string): AuthConfig {
        const authConfig: AuthConfig = {
            login: loginUser,
            // Will only be used when the 'isDemoTenant' service option is enabled
            persona: userEmail,
        };
        const apiId = xtremSystem.functions.matchApiEmail(loginUser)?.[1];
        if (apiId) {
            // The user is an API user, we have to set its auth0.
            // The `auth0` part is the UUID of the API user.
            authConfig.auth0 = `api|${apiId}`;
        }
        return authConfig;
    }

    /** Starts a workflow process for a given flow, topic, and payload. */
    async startProcess(options: StartProcessOptions): Promise<void> {
        const { flowWrapper, locale, loginEmail, userEmail } = options;
        try {
            const processWrapper = await this.withCommittedContext(
                flowWrapper.tenantId,
                context => this.startProcessWithContext(context, options),
                {
                    userEmail,
                    locale,
                    auth: WorkflowEngine.buildAuthConfigForContext(loginEmail, userEmail),
                },
            );
            if (processWrapper) this.wakeUpProcess(processWrapper);
        } catch (error) {
            // Do not fail because startProcess is called with Promise.all and we
            // want only this process to fail, not the others
            logger.error(() => `Error starting process: ${error.stack}`);
        }
    }

    /** Returns the node ID of the next step to wake up in the process, or null if there is none. */
    private static findNodeIdToWakeUp(processWrapper: WorkflowProcessWrapper): string | null {
        const stepId = Object.keys(processWrapper.state.readyNodeIds)[0];
        return stepId ?? null;
    }

    /** Wakes up a workflow process. */
    // eslint-disable-next-line class-methods-use-this
    wakeUpProcess(processWrapper: WorkflowProcessWrapper): void {
        const stepId = WorkflowEngine.findNodeIdToWakeUp(processWrapper);
        if (!stepId) {
            logger.verbose(() => `Workflow wake up: skipping process ${processWrapper.processId}: nothing pending`);
            return;
        }

        const step = processWrapper.flowWrapper.createWorflowStep(processWrapper, stepId);
        step.logVerbose(() => 'waking up');
        step.wakeUpAndCatch();
    }

    private static unresponsiveUpdateLimit(): Datetime {
        const delay = toInteger(ConfigManager.current.workflow?.unresponsiveDelayInSeconds ?? 120);
        return Datetime.now().addSeconds(-delay);
    }

    /** Captures the tenant IDs of processes that have lost their container */
    private async captureProcessTenantIds(): Promise<string[]> {
        // This one is written as raw SQL because the framework does not give access to the _tenant_id column
        const selected = await this.withReadonlyContext(
            null,
            context =>
                context.executeSql<{ _tenant_id: string }[]>(
                    `
                SELECT DISTINCT _tenant_id FROM ${context.schemaName}.workflow_process
                WHERE container_id != $1
                    AND (status = 'shutDown' OR (status = 'running' AND _update_stamp < $2))
                `,
                    [ContainerManager.containerId, String(WorkflowEngine.unresponsiveUpdateLimit())],
                    { allowUnsafe: true },
                ),
            { asRoot: true },
        );
        return selected.map(p => p._tenant_id);
    }

    static getCaptureFetchSize(): integer {
        return toInteger(ConfigManager.current.workflow?.captureFetchSize ?? 20);
    }

    /**
     * Captures processes that have lost their container in a given context
     * They are reassigned to the current container and their status is set to 'running'
     * Their _update_stamp is also updated (by SQL triggers) to prevent them from being captured again too soon
     *
     * @param rootContext - The context to use for the database operations (MUST BE A ROOT CONTEXT)
     */
    private async captureProcessesWithContext(rootContext: Context): Promise<WorkflowProcessWrapper[]> {
        const containerId = ContainerManager.containerId;
        const processes = await rootContext.select(
            WorkflowProcess,
            {
                id: true,
                definition: { id: true },
                diagram: { data: true },
                state: true,
                containerId: true,
                startNotificationId: true,
                originId: true,
                triggeringUser: { email: true },
                status: true,
            },
            {
                first: WorkflowEngine.getCaptureFetchSize(),
                filter: {
                    // We capture processes that were running in other containers
                    containerId: { _ne: containerId },
                    _or: [
                        // Processes that were shut down gracefully
                        { status: 'shutDown' },
                        // Processes that were killed abruptly
                        { status: 'running', _updateStamp: { _lt: WorkflowEngine.unresponsiveUpdateLimit() } },
                    ],
                },
                forUpdate: true,
            },
        );

        logger.verbose(() => `Capturing ${processes.length} processes: ${processes.map(p => p.id).join(', ')}`);

        // Update status, containerId and _update_stamp
        await rootContext.bulkUpdate(WorkflowProcess, {
            set: { containerId, status: 'running' },
            where: {
                id: { _in: processes.map(p => p.id) },
            },
        });

        const tenantId = rootContext.tenantId;
        if (!tenantId) throw new LogicError('Tenant ID is required');
        return processes.map(
            process =>
                new WorkflowProcessWrapper(
                    new FlowWrapper(this, tenantId, process.definition.id, process.diagram.data),
                    { ...process, loginEmail: process.triggeringUser.email },
                ),
        );
    }

    /** Captures processes that have lost their container */
    private async captureProcesses(): Promise<boolean> {
        // Captures the tenant IDs of the processes that have lost their container
        const tenantIds = await this.captureProcessTenantIds();
        if (tenantIds.length === 0) return false;

        // Returns true if one of the tenants may have more processes to capture
        return asyncArray(tenantIds).some(async tenantId => {
            const processWrappers = await this.withCommittedContext(
                tenantId,
                contextWithRootUser => this.captureProcessesWithContext(contextWithRootUser),
                { asRoot: true },
            );
            processWrappers.forEach(processWrapper => {
                this.activeProcesses[processWrapper.processId] = processWrapper;
                this.wakeUpProcess(processWrapper);
            });
            logger.info(`Captured ${processWrappers.length} processes for tenant ${tenantId}`);
            return processWrappers.length === WorkflowEngine.getCaptureFetchSize();
        });
    }

    /** Resumes the active processes that have lost their container */
    async resumeActiveProcesses(tenantId: string): Promise<void> {
        const activeProcesses = await this.withReadonlyContext(
            tenantId,
            contextWithRootUser =>
                contextWithRootUser.select(
                    WorkflowProcess,
                    {
                        id: true,
                        definition: { id: true },
                        diagram: { data: true },
                        state: true,
                        containerId: true,
                        startNotificationId: true,
                        originId: true,
                        triggeringUser: { email: true },
                        status: true,
                    },
                    { filter: { status: 'running' } },
                ),
            { asRoot: true },
        );

        // TODO: handle container id
        activeProcesses.forEach(activeProcess => {
            const flowWrapper = new FlowWrapper(
                this,
                tenantId,
                activeProcess.definition.id,
                activeProcess.diagram.data,
            );
            this.activeProcesses[activeProcess.id] = new WorkflowProcessWrapper(flowWrapper, {
                ...activeProcess,
                loginEmail: activeProcess.triggeringUser.email,
            });
        });
    }

    /**
     * Watches for processes to capture.
     * This is implemented as a loop that captures processes, then sleeps for a number of seconds.
     * The sleep is adjusted to avoid querying too often when there are no processes to capture.
     */
    async watchForProcessesToCapture(): Promise<void> {
        const minSeconds = ConfigManager.current.workflow?.capturePollingMinSeconds ?? 1;
        const maxSeconds = ConfigManager.current.workflow?.capturePollingMaxSeconds ?? 120;
        let seconds = minSeconds;

        while (this.#running) {
            try {
                const captured = await this.captureProcesses();

                // If we captured some processes, we poll fast, otherwise we let poll time increase to maxSeconds
                seconds = captured ? minSeconds : Math.min(seconds * 2, maxSeconds);
                await sleepMillis(seconds * 1000);
            } catch (error) {
                // If an error occurs, we sleep for the maximum time to avoid flooding the logs
                logger.error(`Error in watchForProcessesToCapture: ${error.stack}`);
                await sleepMillis(maxSeconds * 1000);
            }
        }
    }

    /** Starts the workflow engine. */
    async start(): Promise<void> {
        if (this.#running) return;
        this.#running = true;

        await this.notificationBroker.startListening();
        gracefulShutdown.on('stop', () => this.stop());

        this.watchForProcessesToCapture().catch(err => {
            logger.error(err);
        });
    }

    /** Stops the workflow engine */
    private stop(): void {
        this.#running = false;
    }

    /** Is the engine running? */
    isRunning(): boolean {
        return this.#running;
    }

    /** Static method to get the engine from the application */
    static fromApplication(application: Application): WorkflowEngine {
        return (application.workflowManager as WorkflowManager).engine;
    }

    /**
     *  Executes a body of code with a committed context
     */
    withCommittedContext<T extends AnyValue | void>(
        tenantId: string | null,
        body: (context: Context) => Promise<T>,
        options: ContextOptions & {
            asRoot?: boolean;
        },
    ): Promise<T> {
        if (options.asRoot) {
            // We need application.asRoot because we don't provide a user and we also need context.unsafeWithRootUser
            // because filters on WorkflowProcess/WorkflowDefinition rely on context.loginUser which is not set
            // by application.asRoot
            return this.application.asRoot.withCommittedContext(
                tenantId,
                context => context.unsafeWithRootUser(() => body(context)),
                options,
            );
        }
        return this.application.withCommittedContext(tenantId, body, options);
    }

    /** Executes a body of code with a readonly context */
    withReadonlyContext<T extends AnyValue | void>(
        tenantId: string | null,
        body: (context: Context) => Promise<T>,
        options: ContextOptions & {
            asRoot?: boolean;
        },
    ): Promise<T> {
        if (options.asRoot) {
            // We need application.asRoot because we don't provide a user and we also need context.unsafeWithRootUser
            // because filters on WorkflowProcess/WorkflowDefinition rely on context.loginUser which is not set
            // by application.asRoot
            return this.application.asRoot.withReadonlyContext(
                tenantId,
                context => context.unsafeWithRootUser(() => body(context)),
                options,
            );
        }
        return this.application.withReadonlyContext(tenantId, body, options);
    }
}

/** Starts the workflow service. */
export async function startService(application: Application): Promise<void> {
    logger.info('Starting workflow service');
    const engine = WorkflowEngine.fromApplication(application);
    await engine.start();
}
