import { Application, asyncArray, Context, LogicError, WorkflowStepDescriptor } from '@sage/xtrem-core';
import * as _ from 'lodash';
import { WorkflowStepConfig } from '../shared-functions';
import { FlowNode } from './flow';
import { FlowWrapper } from './flow-wrapper';
import { WorkflowEngine } from './workflow-engine';
import { WorkflowProcessWrapper } from './workflow-process-wrapper';
import { WorkflowStep } from './workflow-step';

/**
 * @internal
 *
 * Constructor for a workflow step.
 */
export type WorkflowStepConstructor<
    ConfigT extends WorkflowStepConfig = WorkflowStepConfig,
    StepT extends WorkflowStep<ConfigT> = WorkflowStep<ConfigT>,
> = (new (processWrapper: WorkflowProcessWrapper, flowNode: FlowNode<ConfigT>) => StepT) & {
    descriptor: WorkflowStepDescriptor;
};

/**
 * @internal
 *
 * A factory for creating workflow steps.
 *
 * The step factories are registered in the workflow engine when the application is created.
 * They are identified by their flow node type.
 *
 * When a workflow process enters a step, the engine creates a step instance using the step factory.
 */
export class WorkflowStepFactory<
    ConfigT extends WorkflowStepConfig = WorkflowStepConfig,
    StepT extends WorkflowStep<ConfigT> = WorkflowStep<ConfigT>,
> {
    constructor(
        readonly engine: WorkflowEngine,
        readonly packageName: string,
        readonly stepConstructor: WorkflowStepConstructor<ConfigT, StepT>,
    ) {
        if (!this.#startTopics)
            this.#startTopics = this.engine.notificationBroker.expandTopics(this.descriptor.startTopics);
        if (!this.#resumeTopics)
            this.#resumeTopics = this.engine.notificationBroker.expandTopics(this.descriptor.resumeTopics);
        Object.freeze(this.#startTopics);
        Object.freeze(this.#resumeTopics);
        this.#topics = _.uniq([...this.#startTopics, ...this.#resumeTopics]);
    }

    get application(): Application {
        return this.engine.application;
    }

    /** Gets the type of the step (entity-created, entity-updated, condition, calculate, send-mail, ...). */
    get nodeType(): string {
        return this.descriptor.key;
    }

    /** Gets the step descriptor. */
    get descriptor(): WorkflowStepDescriptor {
        return this.stepConstructor.descriptor;
    }

    readonly #startTopics: string[];

    readonly #resumeTopics: string[];

    readonly #topics: string[];

    /** The complete list of topics */
    get topics(): string[] {
        if (!this.#topics)
            throw new LogicError(
                `Workflow step ${this.nodeType} has not been registered into the application. Likely a missing workflowSteps export in its package's index.ts`,
            );
        return this.#topics;
    }

    /** The list of topics that start a workflow process */
    get startTopics(): string[] {
        return this.#startTopics;
    }

    /** The list of topics that may resume a workflow process */
    get resumeTopics(): string[] {
        return this.#resumeTopics;
    }

    /** Creates a workflow step for control-only. This is called when controlling a workflow definition */
    createStepForControl(flowWrapper: FlowWrapper, node: FlowNode, locale: string): StepT {
        const processWrapper = new WorkflowProcessWrapper(flowWrapper, {
            id: '',
            status: 'running',
            state: {
                stepStates: {},
                variables: {},
                readyNodeIds: {},
                pendingPayloads: {},
                suspendedStepContexts: {},
                errorCount: 0,
                log: [],
                locale,
            },
            containerId: '',
            startNotificationId: '',
            originId: '',
            triggeringUser: { email: '' },
            loginEmail: '',
        });

        // eslint-disable-next-line new-cap
        return new this.stepConstructor(processWrapper, node as FlowNode<ConfigT>);
    }

    isEnabled(context: Context): Promise<boolean> {
        const serviceOptions = this.descriptor.serviceOptions?.() ?? [];
        return asyncArray(serviceOptions).every(serviceOption => context.isServiceOptionEnabled(serviceOption));
    }
}
