import { NotificationEnvelope, registerDynamicNotificationListener } from '@sage/xtrem-communication';
import { Application, ContainerManager, Context, LogicError, NotifyOperation } from '@sage/xtrem-core';
import * as _ from 'lodash';
import { WorkflowProcess } from '../nodes';
import { FlowWrapper } from './flow-wrapper';
import { logger } from './logger';
import { WorkflowEngine } from './workflow-engine';
import { WorkflowProcessWrapper } from './workflow-process-wrapper';
import { WorkflowStep } from './workflow-step';

/**
 * @internal
 *
 * Brokern that manages the dispatch of notifications to workflow processes.
 */
export class WorkflowNotificationBroker {
    constructor(readonly engine: WorkflowEngine) {}

    /** Gets the application object */
    get application(): Application {
        return this.engine.application;
    }

    /** Utility to expand wildcards in topics */
    expandTopics(topics: string | undefined): string[] {
        if (!topics) return [];
        return _.flattenDeep(
            topics.split(',').map(topic => {
                if (topic.startsWith('**/')) {
                    const event = topic.substring(3) as NotifyOperation;
                    return this.application
                        .getAllFactories()
                        .filter(factory => factory.notifies.includes(event))
                        .map(factory => `${factory.name}/${event}`);
                }
                return topic;
            }),
        );
    }

    /** Adds a dynamic listener for a topic */
    private addDynamicTopicListener(
        topic: string,
        onBody: (context: Context, envelope: NotificationEnvelope) => Promise<void>,
    ): void {
        const queue = 'workflow';
        logger.verbose(() => `queue ${queue}: Add dynamic notification listener for topic ${topic}`);
        registerDynamicNotificationListener({
            application: this.application,
            topic,
            queue,
            onBody,
        });
    }

    /** Event handler for start topics */
    async onStartTopic(context: Context, envelope: NotificationEnvelope): Promise<void> {
        const startTopic = envelope.attributes.topic;
        const flowWrappers = (
            await WorkflowEngine.selectWorkflowDefinitions(
                context,
                { _id: true, id: true, diagram: { data: true } },
                {
                    startTopic,
                    // Note: we don't need to filter on the workflows that the user is allowed to see
                    // because the filter defined in WorkflowDefinition.getFilters() will apply and do it for us
                },
            )
        ).map(def => new FlowWrapper(this.engine, String(context.tenantId), def.id, def.diagram.data));

        logger.verbose(
            () =>
                `Received notification topic ${startTopic}: matching workflows: ${
                    flowWrappers.map(flowWrapper => flowWrapper.definitionId).join(',') || '<none>'
                }`,
        );
        const userEmail = (await context.user)?.email;
        if (!userEmail) throw new LogicError('missing user email');

        const { notificationId: startNotificationId, originId, login: loginEmail } = envelope.attributes;
        if (!loginEmail) throw new LogicError('missing login email');
        const payload = envelope.payload;
        const locale = WorkflowEngine.getProcessLocale(context);
        await Promise.all(
            flowWrappers.map(flowWrapper =>
                this.engine.startProcess({
                    flowWrapper,
                    startTopic,
                    startNotificationId,
                    originId,
                    payload,
                    userEmail,
                    loginEmail,
                    locale,
                }),
            ),
        );
    }

    /** Event handler for resume topics */
    async onResumeTopic(context: Context, envelope: NotificationEnvelope): Promise<void> {
        const resumeToken = context.getContextValue('resumeToken');
        if (!resumeToken || !resumeToken.startsWith('workflow:')) return;
        const { processId, stepId } = WorkflowStep.parseResumeToken(resumeToken);
        const [dataFromDb] = await context.unsafeWithRootUser(() =>
            context.select(
                WorkflowProcess,
                {
                    _id: true,
                    id: true,
                    state: true,
                    status: true,
                    containerId: true,
                    startNotificationId: true,
                    originId: true,
                    triggeringUser: { email: true },
                    definition: { id: true },
                    diagram: { data: true },
                },
                { filter: { id: processId }, forUpdate: true },
            ),
        );
        // If no process is listening for this notification, ignore it
        if (!dataFromDb) return;

        const process = {
            loginEmail: envelope.attributes.login,
            ...dataFromDb,
        };

        const tenantId = context.tenantId;
        if (!tenantId) throw new LogicError('missing tenantId');
        const flowWrapper = new FlowWrapper(this.engine, String(tenantId), process.definition.id, process.diagram.data);

        const processWrapper = new WorkflowProcessWrapper(flowWrapper, process);

        processWrapper.state.pendingPayloads[stepId] = envelope.payload;

        let needsWakeUp: boolean;
        switch (processWrapper.status) {
            case 'running':
                // Another step of the process is running (in this container or another one)
                // We just have to update the process state with the new pending payload.
                // This payload will be picked up when the other step will complete.
                needsWakeUp = false;
                break;
            case 'suspended':
                // The process needs to be restarted in this container
                processWrapper.containerId = ContainerManager.containerId;
                needsWakeUp = true;
                break;
            default:
                throw new LogicError(`cannot resume process, bad status: ${processWrapper.status}`);
        }

        await context.unsafeWithRootUser(() =>
            context.bulkUpdate(WorkflowProcess, {
                set: _.pick(processWrapper, 'state', 'status'),
                where: { id: processWrapper.processId },
            }),
        );

        if (needsWakeUp) {
            const step = flowWrapper.createWorflowStep(processWrapper, stepId);
            setImmediate(() => step.resume());
        }
    }

    /** Adds a listener for a start topic */
    addStartListener(topic: string): void {
        return this.addDynamicTopicListener(topic, (context, envelope) => this.onStartTopic(context, envelope));
    }

    /** Adds a listener for a resume topic */
    addResumeListener(topic: string): void {
        return this.addDynamicTopicListener(topic, (context, envelope) => this.onResumeTopic(context, envelope));
    }

    /** Gets the list of all start topics */
    private getAllStartTopics(): string[] {
        const stepFactories = this.engine.getWorkflowStepFactories();
        return _.uniq(_.flattenDeep(Object.values(stepFactories).map(stepFactory => stepFactory.startTopics)));
    }

    /** Gets the list of all resume topics */
    private getAllResumeTopics(): string[] {
        const stepFactories = this.engine.getWorkflowStepFactories();
        return _.uniq(_.flattenDeep(Object.values(stepFactories).map(stepFactory => stepFactory.resumeTopics)));
    }

    /** Start listening on all the topics found in workflow steps */
    async startListening(): Promise<void> {
        // Wait a bit to let routing be initialized
        await new Promise<void>(resolve => {
            setTimeout(resolve, 1000);
        });
        this.getAllStartTopics().forEach(topic => this.addStartListener(topic));
        this.getAllResumeTopics().forEach(topic => this.addResumeListener(topic));
    }
}
