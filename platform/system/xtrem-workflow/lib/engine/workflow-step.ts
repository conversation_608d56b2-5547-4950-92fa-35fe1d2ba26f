import {
    AnyValue,
    Application,
    ContainerManager,
    Context,
    ContextOptions,
    datetime,
    LogicError,
    ValidationContext,
    WorkflowStepDescriptor,
} from '@sage/xtrem-core';
import { AsyncResponse, Dict, WorkflowVariable } from '@sage/xtrem-shared';
import * as handlebars from 'handlebars';
import * as _ from 'lodash';
import { WorkflowProcess } from '../nodes/workflow-process';
import { WorkflowStepConfig } from '../shared-functions';
import { WorkflowLogEntry, WorkflowLogEvent } from '../shared-functions/workflow-log';
import { FlowEdge, FlowNode } from './flow';
import { FlowWrapper } from './flow-wrapper';
import { logger } from './logger';
import { WorkflowEngine } from './workflow-engine';
import { WorkflowProcessState } from './workflow-process-state';
import { WorkflowProcessWrapper } from './workflow-process-wrapper';
import { WorkflowStepFactory } from './workflow-step-factory';
import { WorkflowInputStatus, WorkflowStepState, WorkflowStepStatus } from './workflow-step-state';

/**
 * Base class for all workflow steps.
 *
 * The workflow steps are created by the WorkflowEngine when a process is started or resumed, and when entering a new step.
 *
 * The workflow step is responsible for executing the logic of the step, and for updating the process state when the step is completed.
 */
export abstract class WorkflowStep<ConfigT extends WorkflowStepConfig = WorkflowStepConfig, PrivateStateT = unknown> {
    /**
     * The state of the step. It is loaded from the process state when the step is created and saved together with the process state
     * when a step is completed or when it needs to be checkpointed.
     */
    readonly #state: WorkflowStepState<PrivateStateT>;

    #resolvedConfig: ConfigT;

    constructor(
        readonly processWrapper: WorkflowProcessWrapper,
        readonly flowNode: FlowNode<ConfigT>,
        privateState: PrivateStateT = {} as PrivateStateT,
    ) {
        // Load the step state from the process state, or create a new one if the step is entered for the first time.
        this.#state = (processWrapper.state.stepStates[flowNode.id] as WorkflowStepState<PrivateStateT>) || {
            status: 'inactive',
            privateState,
            inputStatuses: processWrapper.flowWrapper.getInitialInputStatuses(flowNode),
        };
    }

    /** Gets the raw configuration (without variable substitutions) of the step. */
    get rawConfig(): ConfigT {
        return this.flowNode.data;
    }

    /**
     * The (optional) output variable name of the step
     */
    get outputVariableName(): string | undefined {
        return this.rawConfig.outputVariableName;
    }

    /**
     * Returns the values for the local variables
     */
    // eslint-disable-next-line @typescript-eslint/no-unused-vars,class-methods-use-this
    protected _getLocalVariablesValues(_config: ConfigT): Dict<any> {
        return {};
    }

    private replaceVariables<T>(value: T): T {
        if (!value) return value;
        if (typeof value === 'string') {
            if (value.includes('{{')) {
                // Disable escaping because we are not generating HTML, just substituting variables in strings
                const localVariables = this._getLocalVariablesValues(this.rawConfig);
                const allVariables =
                    Object.keys(localVariables).length === 0
                        ? this.processWrapper.state.variables
                        : {
                              ...this.processWrapper.state.variables,
                              ...localVariables,
                          };
                const resolveVariablesValues = (stringToResolve: string): T => {
                    const compiled = handlebars.compile(stringToResolve || '', { noEscape: true });
                    return compiled(allVariables) as T;
                };
                const result = resolveVariablesValues(value);
                if (Object.keys(localVariables).length > 0) {
                    // When we have local variables, they could refer to other non-local variables
                    // So we have to resolve them again
                    if (typeof result === 'string' && result.includes('{{')) return resolveVariablesValues(result);
                }
                return result;
            }
            return value;
        }
        if (Array.isArray(value)) {
            return value.map(v => this.replaceVariables(v)) as T;
        }
        if (typeof value === 'object') {
            return _.mapValues(value, v => this.replaceVariables(v)) as T;
        }
        return value;
    }

    resolveConfig(): ConfigT {
        if (!this.#resolvedConfig) this.#resolvedConfig = this.replaceVariables(this.rawConfig);
        return this.#resolvedConfig;
    }

    /** Gets the application. */
    get application(): Application {
        return this.stepFactory.application;
    }

    get engine(): WorkflowEngine {
        return this.processWrapper.engine;
    }

    /** Gets the flow wrapper. */
    get flowWrapper(): FlowWrapper {
        return this.processWrapper.flowWrapper;
    }

    /** Gets the type of the step (entity-created, entity-updated, condition, calculate, send-mail, ...). */
    get nodeType(): string {
        return this.flowNode.type;
    }

    /**
     * @internal
     * Gets the step factory.
     */
    get stepFactory(): WorkflowStepFactory {
        return this.engine.findStepFactory(this.nodeType);
    }

    /** Gets the descriptor of the step factory */
    get descriptor(): WorkflowStepDescriptor {
        return this.stepFactory.descriptor;
    }

    /** Gets the step's flowNode id. */
    get nodeId(): string {
        return this.flowNode.id;
    }

    /** Gets the private state of the step. */
    get privateState(): PrivateStateT {
        return this.#state.privateState;
    }

    /** Gets the tenant id. */
    get tenantId(): string {
        return this.flowWrapper.tenantId;
    }

    /** Gets the workflow definition id. */
    get definitionId(): string {
        return this.flowWrapper.definitionId;
    }

    /** Logs a message with the step id and type */
    logMessage(message: string): string {
        return `Step ${this.definitionId}.${this.nodeId}: ${message}`;
    }

    /* Returns a new LogicError with a message prefixed with the step id and type */
    logicError(message: string): LogicError {
        return new LogicError(this.logMessage(message));
    }

    /** Logs a message at the info level */
    logInfo(message: string): void {
        logger.info(this.logMessage(message));
    }

    /** Logs a message at the warning level */
    logWarn(message: string): void {
        logger.warn(this.logMessage(message));
    }

    /** Logs a message at the error level */
    logError(message: string): void {
        logger.error(this.logMessage(message));
    }

    /** Logs a message at the verbose level */
    logVerbose(messageProvider: () => string): void {
        logger.verbose(() => this.logMessage(messageProvider()));
    }

    /** Logs a message at the debug level */
    logDebug(messageProvider: () => string): void {
        logger.debug(() => this.logMessage(messageProvider()));
    }

    /** Utility method to add an error the validation context during control */
    // eslint-disable-next-line class-methods-use-this
    addControlError(cx: ValidationContext, message: string): void {
        cx.error.add(message);
    }

    /** Controls the input edges. Overridden by WorkflowAction and WorkflowEvent */
    abstract controlInputEdges(cx: ValidationContext): void;

    /** Gets the maximum number of output edges. */
    get maxOutputs(): number {
        return this.stepFactory.descriptor.maxOutputs ?? Infinity;
    }

    /** Controls the output edges. */
    private controlOutputEdges(cx: ValidationContext): void {
        const outputEdges = this.flowWrapper.getOutputEdges(this.nodeId);

        const minOutputs = this.descriptor.minOutputs ?? 0;
        if (outputEdges.length < minOutputs) {
            this.addControlError(
                cx,
                cx.localize(
                    '@sage/xtrem-workflow/workflow-error-too-few-outputs',
                    'Node has too few outputs: at least {{minOutputs}} expected, got {{outputEdges}}.',
                    { minOutputs, outputEdges: outputEdges.length },
                ),
            );
        }

        const maxOutputs = this.descriptor.maxOutputs ?? Infinity;
        if (outputEdges.length > maxOutputs) {
            this.addControlError(
                cx,
                cx.localize(
                    '@sage/xtrem-workflow/workflow-error-too-many-outputs',
                    'Node has too many outputs: at most {{maxOutputs}} expected, got {{outputEdges}}.',
                    { maxOutputs, outputEdges: outputEdges.length },
                ),
            );
        }
    }

    /**
     * Controls the step.
     *
     * Subclasses may override it but they have to call the super.control(cx).
     */
    // eslint-disable-next-line require-await
    async control(cx: ValidationContext): Promise<void> {
        this.controlInputEdges(cx);
        this.controlOutputEdges(cx);
    }

    /** Parse an entity name (@sage/packageName/nodeName) into its packageName and nodeName */
    static parseEntityName(entityName: string): { packageName: string; nodeName: string } {
        const [, packageKebabName, nodeName] = entityName.split('/');
        const packageName = _.camelCase(packageKebabName);
        return { packageName, nodeName };
    }

    /** setImmediate variant to get proper error handling */
    setImmediate(body: () => AsyncResponse<void>): void {
        setImmediate(async () => {
            // Use try/catch to catch synchronous errors thrown by body()
            try {
                await Promise.resolve(body());
            } catch (err) {
                this.logError(err.stack);
                this.exit('error', { errorMessage: err.message }).catch(e => this.logError(e.stack));
            }
        });
    }

    /**
     * Wakes up the step.
     * This method is called by the engine when the step is started or resumed.
     * WorkflowStep subclasses must override this method to implement the logic of the step.
     */
    protected abstract wakeUp(): Promise<void>;

    wakeUpAndCatch(): void {
        return this.setImmediate(async () => {
            if (this.engine.isRunning()) {
                await this.wakeUp();
            } else {
                await this.shutDown();
            }
        });
    }

    /** Gets the value of a variable in the process state */
    getVariableValue(path: string): any {
        return _.get(this.processWrapper.state.variables, path);
    }

    /** Gets the definition of a variable from the process diagram */
    getVariableDefinition(path: string): WorkflowVariable {
        return this.flowWrapper.getVariableDefinition(path);
    }

    /** Gets the status of the step */
    getStatus(): WorkflowStepStatus {
        return this.#state.status;
    }

    /** Gets a debug data that we can serialize in JSON */
    get debugData(): Omit<WorkflowProcessWrapper, 'flowWrapper'> {
        return _.omit(this.processWrapper, 'flowWrapper');
    }

    /** Gets the originId that is common to all processes triggered by the same initial event  */
    get originId(): string {
        return this.processWrapper.originId;
    }

    /** Gets a token that we use to reconciliate resume event with its originating process and step  */
    get resumeToken(): string {
        return `workflow:${this.processWrapper.processId}:${this.nodeId}`;
    }

    /** Parses an resume token into a processId and a stepId */
    static parseResumeToken(resumeToken: string): { processId: string; stepId: string } {
        const [tag, processId, stepId] = resumeToken.split(':');
        if (tag !== 'workflow' || !processId || !stepId) throw new LogicError(`invalid resume token: ${resumeToken}`);
        return { processId, stepId };
    }

    /**
     * Builds the context options to use for the step.
     */
    private _buildContextOptions(): ContextOptions {
        return {
            contextValues: { originId: this.originId, resumeToken: this.resumeToken },
            userEmail: this.processWrapper.triggeringUser.email,
            locale: this.processWrapper.state.locale,
            auth: WorkflowEngine.buildAuthConfigForContext(
                this.processWrapper.loginEmail,
                this.processWrapper.triggeringUser.email,
            ),
            source: 'workflow',
        };
    }

    /** Executes a body of code with a committed context */
    withCommittedContext<T extends AnyValue | void>(
        body: (context: Context) => Promise<T>,
        options: {
            asRoot?: boolean;
        } = {},
    ): Promise<T> {
        return this.engine.withCommittedContext(this.processWrapper.tenantId, body, {
            asRoot: options.asRoot,
            ...this._buildContextOptions(),
        });
    }

    /** Executes a body of code with a readonly context */
    withReadonlyContext<T extends AnyValue | void>(
        body: (context: Context) => Promise<T>,
        options: {
            asRoot?: boolean;
        } = {},
    ): Promise<T> {
        return this.engine.withReadonlyContext(this.processWrapper.tenantId, body, {
            asRoot: options.asRoot,
            ...this._buildContextOptions(),
        });
    }

    getLogEntry(event: WorkflowLogEvent, message?: string): WorkflowLogEntry {
        return {
            timestamp: datetime.now(true).toJSON(),
            stepId: this.nodeId,
            event,
            message,
        };
    }

    get locale(): string {
        return this.processWrapper.state.locale;
    }

    /** Enters the step */
    protected enter(): void {
        const state = this.#state;
        delete this.processWrapper.state.readyNodeIds[this.nodeId];
        state.status = 'running';
        state.enteredAt = datetime.now(true);
    }

    /** Propagates the execution through an edge */
    private propagateThroughEdge(
        newProcessState: WorkflowProcessState,
        edge: FlowEdge,
        inputStatus: WorkflowInputStatus,
    ): void {
        const node = this.flowWrapper.findNode(edge.target);

        let stepState = newProcessState.stepStates[node.id];
        if (!stepState) {
            const step = this.flowWrapper.createWorflowStep(this.processWrapper, node.id);
            stepState = step.#state;
            newProcessState.stepStates[node.id] = stepState;
        }

        const oldInputStatus = stepState.inputStatuses[edge.id];
        if (oldInputStatus !== 'unknown')
            throw this.logicError(
                `cannot propagate on edge ${edge.id}: expected 'unknown' status, got '${oldInputStatus}'`,
            );
        stepState.inputStatuses[edge.id] = inputStatus;

        // If at least one input is unknown, the step remains inactive and we stop propagation here
        const hasUnknownInputs = Object.values(stepState.inputStatuses).includes('unknown');
        if (hasUnknownInputs) return;

        const hasTriggeredInputs = Object.values(stepState.inputStatuses).includes('triggered');
        if (hasTriggeredInputs) {
            // All inputs are known and at least one of them is triggered.
            // We mark the node as triggered so that it will be woken up.
            stepState.status = 'triggered';
            newProcessState.readyNodeIds[node.id] = true;
        } else {
            // All inputs are known and all of them are ignored.
            // We mark the node as ignored and we propagate the ignored status recursively through its output edges.
            stepState.status = 'ignored';
            const outputEdges = this.flowWrapper.getOutputEdges(node.id);
            outputEdges.forEach(e => this.propagateThroughEdge(newProcessState, e, 'ignored'));
        }
    }

    /** Propagates the execution through the edges that match the outputHandle */
    private propagateThroughEdges(newProcessState: WorkflowProcessState, triggeredOutputs: string[]): void {
        const outputEdges = this.flowWrapper.getOutputEdges(this.nodeId);
        outputEdges.forEach(edge => {
            const inputStatus = triggeredOutputs.includes(edge.sourceHandle) ? 'triggered' : 'ignored';
            this.propagateThroughEdge(newProcessState, edge, inputStatus);
        });
    }

    /**
     * Updates the process state and notifies the engine that the step is done
     * @param rootContext - The context to use for the database operations (MUST BE A ROOT CONTEXT)
     */
    private async updateProcess(
        rootContext: Context,
        status: WorkflowStepStatus,
        options: {
            result?: any;
            errorMessage?: string;
            progressMessage?: string;
            outputHandle?: string;
            suspendedStepContext?: any;
        } = {},
    ): Promise<WorkflowProcessWrapper | null> {
        // Re-read the process data with a lock, to handle race conditions when processing events in
        // another container.
        // We have to start with what we found in the database.
        const [dataFromDb] = await rootContext.select(
            WorkflowProcess,
            {
                id: true,
                status: true,
                state: true,
                containerId: true,
                startNotificationId: true,
                originId: true,
                triggeringUser: { email: true },
                completedAt: true,
            },
            { filter: { id: this.processWrapper.processId }, forUpdate: true },
        );
        if (!dataFromDb) throw this.logicError(`process not found: ${this.processWrapper.processId}`);

        const newProcessData = { loginEmail: this.processWrapper.loginEmail, ...dataFromDb };
        if (newProcessData.containerId !== ContainerManager.containerId) {
            // This process is being handled by another container, so we don't update it.
            this.logWarn(`Skipping update of process ${newProcessData.id} because it is handled by another container`);
            return null;
        }

        // currentProcessWrapper will be thrown away so we don't have to clone it.
        const newProcessState = newProcessData.state;
        let newStepState = newProcessState.stepStates[this.nodeId];
        if (!newStepState) {
            // We don't save state after enter so we have to use this.state if it is not in the database
            newStepState = this.#state;
            newProcessState.stepStates[this.nodeId] = newStepState;
        }

        // transfer status and private data to the new state.
        newStepState.status = status;
        newStepState.privateState = _.cloneDeep(this.#state.privateState);

        // Clears the pending payloads and edges (if any) when we exit or suspend the node
        const clearPendingEdgesAndPayloads = (): void => {
            delete newProcessState.pendingPayloads[this.nodeId];
            delete newProcessState.suspendedStepContexts[this.nodeId];
            delete newProcessState.readyNodeIds[this.nodeId];
        };

        switch (status) {
            case 'success': {
                newStepState.exitedAt = datetime.now(true);
                newProcessState.log.push(this.getLogEntry('success', JSON.stringify(options.result)));

                _.merge(newProcessState.variables, options.result ?? {});

                clearPendingEdgesAndPayloads();

                // Push the output edges into newProcessState.pendingEdges
                this.propagateThroughEdges(newProcessState, [options.outputHandle || 'out']);
                break;
            }

            case 'error':
                newStepState.exitedAt = datetime.now(true);
                newProcessState.log.push(this.getLogEntry('error', options.errorMessage));

                // Record error message in step state and increment error count
                newStepState.errorMessage = options.errorMessage;
                newProcessState.errorCount += 1;

                clearPendingEdgesAndPayloads();
                break;

            case 'suspended':
                newProcessState.log.push(this.getLogEntry('suspended'));
                clearPendingEdgesAndPayloads();
                newProcessState.suspendedStepContexts[this.nodeId] = options.suspendedStepContext;
                break;

            case 'shutDown':
                newProcessState.log.push(
                    this.getLogEntry('shutDown', `shut down by container ${newProcessData.containerId}`),
                );
                break;

            case 'running':
                newProcessState.log.push(this.getLogEntry('running', options.progressMessage));
                break;

            default:
                throw this.logicError(`invalid status: ${status}`);
        }

        // Process is done if there are no active edges and no pending payloads
        const processDone =
            Object.keys(newProcessState.readyNodeIds).length === 0 &&
            Object.keys(newProcessState.pendingPayloads).length === 0 &&
            Object.keys(newProcessState.suspendedStepContexts).length === 0;

        const stepStates = Object.values(newProcessState.stepStates);
        if (processDone) {
            newProcessData.status = newProcessState.errorCount === 0 ? 'success' : 'error';
            newProcessData.completedAt = datetime.now();
        } else if (stepStates.some(stepState => stepState.status === 'running' || stepState.status === 'triggered')) {
            // At least one step is still running or triggered so process is still running
            newProcessData.status = 'running';
        } else if (stepStates.some(stepState => stepState.status === 'shutDown')) {
            // At least one step is shut down so container is being shut down
            newProcessData.status = 'shutDown';
        } else if (stepStates.some(stepState => stepState.status === 'suspended')) {
            // All running steps have been suspended so process is suspended
            newProcessData.status = 'suspended';
        } else {
            // Do not change the process status
        }

        await rootContext.bulkUpdate(WorkflowProcess, {
            set: _.pick(newProcessData, 'state', 'status', 'completedAt'),
            where: { id: this.processWrapper.processId },
        });

        if (newProcessData.status === 'success' || newProcessData.status === 'error') {
            await rootContext.notify(
                'WorkflowProcess/completed',
                _.pick(newProcessData, 'id', 'status', 'startNotificationId', 'completedAt'),
            );
        }

        return new WorkflowProcessWrapper(this.processWrapper.flowWrapper, newProcessData);
    }

    /** Continues execution after the process state has been updated */
    private continueAfterUpdate(processWrapper: WorkflowProcessWrapper): void {
        switch (processWrapper.status) {
            case 'running':
                // Wake up process to handle new pending edge or payload
                this.setImmediate(() => this.engine.wakeUpProcess(processWrapper));
                break;
            case 'success':
            case 'error':
                delete this.engine.activeProcesses[processWrapper.processId];
                this.logInfo(`Completed process ${processWrapper.processId} with status ${processWrapper.status}`);
                break;
            case 'suspended':
                break;
            default:
                throw this.logicError(`invalid process status after step exit: ${processWrapper.status}`);
        }
    }

    /** Exits the step with a status and eventual error message */
    protected async exit(
        status: 'success' | 'error',
        options: { result?: any; errorMessage?: string; outputHandle?: string } = {},
    ): Promise<void> {
        this.logVerbose(() => `exiting step with status ${status}`);
        const newProcessWrapper = await this.withCommittedContext(
            rootContext => this.updateProcess(rootContext, status, options),
            { asRoot: true },
        );
        if (newProcessWrapper) this.continueAfterUpdate(newProcessWrapper);
    }

    /** Saves the process state and continues execution */
    protected async checkpoint(progressMessage: string): Promise<void> {
        this.logVerbose(() => `checkpointing step: ${progressMessage}`);
        const newProcessWrapper = await this.withCommittedContext(
            rootContext => this.updateProcess(rootContext, 'running', { progressMessage }),
            { asRoot: true },
        );
        if (!newProcessWrapper) return;

        // Update the state with what we got from the database
        Object.assign(this.processWrapper, newProcessWrapper);

        // Continue with this step
        this.logVerbose(() => 'continuing');
        this.wakeUpAndCatch();
    }

    /** Suspends the step */
    protected async suspend(options: { suspendedStepContext: any }): Promise<void> {
        this.logVerbose(() => `suspending step with context ${JSON.stringify(options.suspendedStepContext)}`);
        if (
            await this.withCommittedContext(rootContext => this.updateProcess(rootContext, 'suspended', options), {
                asRoot: true,
            })
        )
            this.logVerbose(() => 'suspended');
    }

    /** @internal Resumes the step */
    resume(): void {
        this.logVerbose(() => 'resuming step');
        this.wakeUpAndCatch();
    }

    /** Shut the step down because the engine was stopped by a graceful shutdown */
    protected async shutDown(): Promise<void> {
        this.logVerbose(() => 'shutting down step');
        if (
            await this.withCommittedContext(rootContext => this.updateProcess(rootContext, 'shutDown'), {
                asRoot: true,
            })
        )
            this.logVerbose(() => 'shut down');
    }

    /**
     * Accept the step.
     * Overridden by subclasses.
     */
    // eslint-disable-next-line class-methods-use-this
    accept(): Promise<boolean> {
        return Promise.resolve(true);
    }

    /**
     * The static descriptor which is defined by subclasses and shared between server and client.
     * Every concrete WorkflowStep subclass must have a static descriptor.
     */
    // Unfortunately we cannot declaree the descriptor as an abstract static property (see https://github.com/microsoft/TypeScript/issues/34516)
    // So we initialize it to null to get a runtime error if subclassed do not override it
    static descriptor: WorkflowStepDescriptor = null as any;
}
