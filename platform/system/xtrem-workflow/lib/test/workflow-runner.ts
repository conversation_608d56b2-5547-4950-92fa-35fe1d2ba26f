import * as xtremCommunication from '@sage/xtrem-communication';
import {
    Application,
    asyncArray,
    Context,
    ContextOptions,
    Dict,
    Logger,
    LogicError,
    Node,
    NodeCreateData,
    StaticThis,
    WorkflowResult,
    WorkflowRunOptions,
    WorkflowStartEvent,
} from '@sage/xtrem-core';
import { WorkflowError } from '@sage/xtrem-shared';
import { WorkflowEngine } from '../engine/workflow-engine';
import { WorkflowProcessState } from '../engine/workflow-process-state';
import { WorkflowProcessStatus } from '../enums/workflow-process-status';
import { WorkflowProcess } from '../nodes/workflow-process';
import { workflow } from '../service-options';

const logger = Logger.getLogger(__filename, 'workflow');

export interface WorkflowProcessCompletedEvent {
    id: string;
    status: WorkflowProcessStatus;
    errorMessage?: string;
    startNotificationId: string;
    completedAt: string;
}

type WorkflowCompletionCallback = (context: Context, completedEvent: WorkflowProcessCompletedEvent) => Promise<void>;

export class WorkflowRunner {
    constructor(readonly application: Application) {}

    // eslint-disable-next-line class-methods-use-this
    private async readProcessState(context: Context, startNotificationId: string): Promise<WorkflowProcessState> {
        const workflowProcesses = await context.query(WorkflowProcess, { filter: { startNotificationId } }).toArray();
        if (workflowProcesses.length === 0)
            throw new LogicError(`missing workflow process for notificationId: ${startNotificationId}`);
        if (workflowProcesses.length > 1)
            throw new LogicError(
                `too many workflow processes (${workflowProcesses.length}) for notificationId: ${startNotificationId}: ${await asyncArray(
                    workflowProcesses,
                )
                    .map(async p => (await p.definition).id)
                    .toArray()}`,
            );
        const process = workflowProcesses[0];
        return process.state;
    }

    private static _getContextOptions(options: WorkflowRunOptions): ContextOptions {
        const ctxOptions: ContextOptions = {
            userEmail: options.userEmail,
            locale: options.locale,
            source: 'workflow',
        };
        if (options.loginEmail !== options.userEmail) {
            ctxOptions.auth = { persona: options.userEmail, login: options.loginEmail };
        }
        return ctxOptions;
    }

    private sendCreatedEntityNotification<NodeT extends Node>(
        nodeConstructor: StaticThis<NodeT>,
        data: NodeCreateData<NodeT>,
        options: WorkflowRunOptions,
    ): Promise<string> {
        return this.application.withCommittedContext(
            options.tenantId,
            async context => {
                const node = await context.create(nodeConstructor, data);
                await node.$.save();

                const notifications = await context.select(
                    xtremCommunication.nodes.SysNotification,
                    { notificationId: true },
                    {
                        first: 1,
                        filter: { topic: `${nodeConstructor.name}/created` },
                        orderBy: { _id: -1 },
                    },
                );
                if (notifications.length !== 1) throw new Error('Expected 1 notification');
                return notifications[0].notificationId;
            },
            WorkflowRunner._getContextOptions(options),
        );
    }

    private sendUpdatedEntityNotification<NodeT extends Node>(
        nodeConstructor: StaticThis<NodeT>,
        data: NodeCreateData<NodeT>,
        options: WorkflowRunOptions,
    ): Promise<string> {
        return this.application.withCommittedContext(
            options.tenantId,
            async context => {
                const node = await context.read(nodeConstructor, { _id: data._id }, { forUpdate: true });
                await node.$.set(data);
                await node.$.save();

                const notifications = await context.select(
                    xtremCommunication.nodes.SysNotification,
                    { notificationId: true, payload: true },
                    {
                        first: 1,
                        filter: { topic: `${nodeConstructor.name}/updated` },
                        orderBy: { _id: -1 },
                    },
                );
                if (notifications.length !== 1) throw new Error('Expected 1 notification');
                return notifications[0].notificationId;
            },
            WorkflowRunner._getContextOptions(options),
        );
    }

    private sendStartNotification(startEvent: WorkflowStartEvent, options: WorkflowRunOptions): Promise<string> {
        const [nodeName, eventName] = startEvent.topic.split('/');
        if (/(created|updated|deleted)/.test(eventName)) {
            const factory = this.application.getFactoryByName(nodeName);
            switch (eventName) {
                case 'created':
                    return this.sendCreatedEntityNotification(
                        factory.nodeConstructor,
                        startEvent.payload as NodeCreateData<Node>,
                        options,
                    );
                case 'updated':
                    return this.sendUpdatedEntityNotification(
                        factory.nodeConstructor,
                        startEvent.payload as NodeCreateData<Node>,
                        options,
                    );

                case 'deleted':
                default:
                    throw new LogicError(`${eventName}: not implemented`);
            }
        }
        return this.application.withCommittedContext(
            options.tenantId,
            context => context.notify('WorkflowProcess/testStarted', startEvent.payload as object),
            WorkflowRunner._getContextOptions(options),
        );
    }

    private static getErrors(state: WorkflowProcessState): WorkflowError[] {
        const errors: WorkflowError[] = [];
        Object.entries(state.stepStates).forEach(([stepId, stepState]) => {
            if (stepState.errorMessage) errors.push({ stepId, message: stepState.errorMessage });
        });
        return errors;
    }

    private async withWorkflowServiceOption<T>(tenantId: string, body: () => Promise<T>): Promise<T> {
        const wasActive = await this.application.withCommittedContext(tenantId, async context => {
            const active = await context.isServiceOptionEnabled(workflow);
            if (!active) await this.application.serviceOptionManager.activateServiceOptions(context, [workflow]);
            return active;
        });
        try {
            return await body();
        } finally {
            if (!wasActive) {
                await this.application.withCommittedContext(tenantId, context =>
                    this.application.serviceOptionManager.deactivateServiceOptions(context, [workflow]),
                );
            }
        }
    }

    async runWithServiceOption(startEvent: WorkflowStartEvent, options: WorkflowRunOptions): Promise<WorkflowResult> {
        // Ensure that engine is started
        await WorkflowEngine.fromApplication(this.application).start();

        const startNotificationId = await this.sendStartNotification(startEvent, options);
        logger.info(`Workflow started: ${startEvent.topic}`);
        return new Promise((resolve, reject) => {
            WorkflowRunner.completionCallbacks[startNotificationId] = async (context, completedEvent) => {
                const { status } = completedEvent;
                delete WorkflowRunner.completionCallbacks[startNotificationId];

                const state = await this.readProcessState(context, startNotificationId);
                const errors = WorkflowRunner.getErrors(state);
                logger.info(`Workflow completed with status: ${status}`);
                const variables = state.variables;
                clearTimeout(timeoutId);
                // call done after the context is closed to avoid a race condition on the saving of the process state
                try {
                    context.on('closed', () => resolve({ status, variables, errors }));
                } catch (err) {
                    context.on('closed', () => reject(err));
                }
            };

            // Timeout must be greater than the visibility window of the queue + the time to execute the workflow
            const timeoutId = setTimeout(() => {
                reject(new Error('Workflow timed out'));
            }, 60_000);
        });
    }

    run(startEvent: WorkflowStartEvent, options: WorkflowRunOptions): Promise<WorkflowResult> {
        return this.withWorkflowServiceOption(options.tenantId, () => this.runWithServiceOption(startEvent, options));
    }

    static async onWorkflowCompleted(context: Context, completedEvent: WorkflowProcessCompletedEvent): Promise<void> {
        const startNotificationId = completedEvent.startNotificationId;

        const completionCallback = this.completionCallbacks[startNotificationId];
        if (completionCallback) {
            await completionCallback(context, completedEvent);
        }
    }

    private static completionCallbacks: Dict<WorkflowCompletionCallback> = {};
}
