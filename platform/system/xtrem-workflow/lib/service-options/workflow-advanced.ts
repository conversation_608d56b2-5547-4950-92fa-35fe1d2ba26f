import { ServiceOption } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremWorkflow from '../index';

export const workflowAdvanced = new ServiceOption({
    __filename,
    // DO NOT change the status of this option to 'released'.
    // Instead remove this option from steps when we release them.
    status: 'workInProgress',
    description: 'Workflow advanced features (not yet released)',
    isHidden: false,
    activates: () => [xtremWorkflow.serviceOptions.workflow, xtremSystem.serviceOptions.sysTag],
});
