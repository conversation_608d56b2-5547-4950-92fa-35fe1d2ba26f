import { EnumDataType } from '@sage/xtrem-core';

export enum WorkflowMutationParameterTypeEnum {
    integer,
    decimal,
    string,
    boolean,
    date,
    /**
     * A reference to a node
     */
    reference,
    enum,
    other,
}

export type WorkflowMutationParameterType = keyof typeof WorkflowMutationParameterTypeEnum;

export const WorkflowMutationParameterTypeDataType = new EnumDataType<WorkflowMutationParameterType>({
    enum: WorkflowMutationParameterTypeEnum,
    filename: __filename,
});
