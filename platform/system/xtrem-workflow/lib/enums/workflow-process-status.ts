import { EnumDataType } from '@sage/xtrem-core';

export enum WorkflowProcessStatusEnum {
    /** running */
    running,
    /** complete without errors */
    success,
    /** complete with errors */
    error,
    /** cancelled */
    cancelled,
    /** suspended */
    suspended,
    /** shut down because its container was shut down*/
    shutDown,
    /** skipped (this one is never stored) */
    skipped,
}

export type WorkflowProcessStatus = keyof typeof WorkflowProcessStatusEnum;

export const WorkflowProcessStatusDataType = new EnumDataType<WorkflowProcessStatus>({
    enum: WorkflowProcessStatusEnum,
    filename: __filename,
});
