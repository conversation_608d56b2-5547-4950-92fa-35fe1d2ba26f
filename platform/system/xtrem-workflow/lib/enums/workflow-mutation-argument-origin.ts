import { EnumDataType } from '@sage/xtrem-core';

export enum WorkflowMutationArgumentOriginEnum {
    /**
     * The value for the argument will be fetched from a parameter of the action
     */
    fromParameter,
    /**
     * The value for the argument is a variable of the workflow
     */
    fromVariable,
    /**
     * Manual value
     */
    manual,
}

export type WorkflowMutationArgumentOrigin = keyof typeof WorkflowMutationArgumentOriginEnum;

export const WorkflowMutationArgumentOriginDataType = new EnumDataType<WorkflowMutationArgumentOrigin>({
    enum: WorkflowMutationArgumentOriginEnum,
    filename: __filename,
});
