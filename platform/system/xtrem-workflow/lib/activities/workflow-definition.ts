import { Activity } from '@sage/xtrem-core';
import * as xtremMetadata from '@sage/xtrem-metadata';
import * as xtremSystem from '@sage/xtrem-system';
import { WorkflowDefinition, WorkflowProcess } from '../nodes';

export const workflowDefinition = new Activity({
    description: 'Workflow',
    node: () => WorkflowDefinition,
    __filename,
    permissions: ['read', 'manage'],
    operationGrants: {
        read: [
            {
                operations: ['read'],
                on: [() => WorkflowProcess, () => xtremSystem.nodes.User, () => xtremMetadata.nodes.MetaNodeFactory],
            },
        ],
        manage: [{ operations: ['create', 'update', 'delete'], on: [() => WorkflowDefinition, () => WorkflowProcess] }],
    },
});
