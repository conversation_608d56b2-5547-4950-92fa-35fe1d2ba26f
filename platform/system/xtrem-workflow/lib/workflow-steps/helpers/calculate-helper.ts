import { LogicError, ValidationContext } from '@sage/xtrem-core';
import { Decimal, newDecimal } from '@sage/xtrem-decimal';
import { CalculateOperation, WorkflowCalculation } from '../../shared-functions';
import { CalculateAction } from '../actions/calculate-action';
import { VariableHelper } from './variable-helper';

type Operator = (helper: CalculateHelper, v1: any, v2: any) => number;

export class CalculateHelper {
    constructor(private readonly step: CalculateAction) {}

    private parseNumber(str: string): number {
        const value = parseFloat(str);
        if (!Number.isFinite(value)) throw this.step.logicError(`invalid number: ${str}`);
        return value;
    }

    /**
     * Convert the operands to a numeric values. Used by arithmetic operators.
     *
     * v1 is obtained from a variable, but v2 maybe a string or a variable.
     * So we rely on the type of v1 to determine how to convert v2.
     *
     * This file is preprocessed so operators will be converted to typesLib functions calls
     * that know how to deal with decimals.
     */
    private getNumericOperands(v1: unknown, v2: unknown): [number, number] {
        const { operator } = this.step.resolveConfig();

        if (v1 == null || v2 == null) throw this.step.logicError(`Cannot ${operator} null values`);

        if (Decimal.isDecimal(v1)) {
            if (Decimal.isDecimal(v2) || typeof v2 === 'number') return [v1, v2] as unknown as [number, number];
            if (typeof v2 === 'string') return [v1, newDecimal(v2)] as unknown as [number, number];
            throw this.step.logicError(`Cannot ${operator} decimal with: ${v2}`);
        }
        if (typeof v1 === 'number') {
            if (typeof v2 === 'number') return [v1, v2];
            if (typeof v2 === 'string') return [v1, this.parseNumber(v2)];
            throw this.step.logicError(`Cannot ${operator} number with: ${v2}`);
        }
        throw this.step.logicError(`Cannot ${operator} ${typeof v1} with ${typeof v2}`);
    }

    private static numericOperator(fn: (v1: number, v2: number) => number): Operator {
        return (helper, v1, v2) => {
            const [numericV1, numericV2] = helper.getNumericOperands(v1, v2);
            return fn(numericV1, numericV2);
        };
    }

    private static divide(v1: any, v2: any): number {
        if (v2 === 0) throw new LogicError('Division by zero');
        return v1 / v2;
    }

    static ops: { [K in CalculateOperation]: Operator } = {
        add: this.numericOperator((v1, v2) => v1 + v2),
        subtract: this.numericOperator((v1, v2) => v1 - v2),
        multiply: this.numericOperator((v1, v2) => v1 * v2),
        divide: this.numericOperator((v1, v2) => this.divide(v1, v2)),
    };

    static isOperation(string: string): string is CalculateOperation {
        return this.ops[string as CalculateOperation] !== undefined;
    }

    evaluateVariablePath(path: string): any {
        const variable = this.step.getVariableDefinition(path);
        const value = this.step.getVariableValue(path);
        return new VariableHelper(this.step, variable).getTypedValue(value);
    }

    evaluateArg(calculation: WorkflowCalculation): any {
        if (!calculation.isVariable) return calculation.value;
        // calculation.value is the path to the variable
        if (!calculation.variable) throw this.step.logicError('missing calculate step variable');
        return this.evaluateVariablePath(calculation.variable);
    }

    evaluateCalculation(prevValue: number, calculation: WorkflowCalculation): number {
        const thisValue = this.evaluateArg(calculation);
        if (!calculation.operation) throw this.step.logicError('invalid calculate step: no operation');
        const op = CalculateHelper.ops[calculation.operation];
        if (!op) throw this.step.logicError(`invalid filter type ${calculation.operation}`);
        this.step.logVerbose(
            () => `evaluating calculation step ${calculation.operation} with v1=${prevValue}, v2=${thisValue}`,
        );
        return op(this, prevValue, thisValue);
    }

    evaluate(): number {
        const { calculationSteps } = this.step.resolveConfig();
        return calculationSteps.reduce((prevValue, calculation, index) => {
            if (index === 0) return this.evaluateArg(calculation) as number;
            if (!calculation.operation) throw this.step.logicError('invalid calculate step: no operation');
            const operation = CalculateHelper.ops[calculation.operation];
            return operation(this, prevValue, this.evaluateArg(calculation));
        }, 0);
    }

    private controlCalculate(cx: ValidationContext, calculation: WorkflowCalculation, index: number): void {
        if (calculation.isVariable && !calculation.variable) {
            this.step.addControlError(cx, `calculate step ${index}: missing variable path`);
        }
        if (!calculation.isVariable && typeof calculation.value !== 'number')
            this.step.addControlError(cx, `calculate step ${index}: value is not a number`);
        else if (index === 0) {
            if (calculation.operation)
                this.step.addControlError(cx, 'invalid calculate step: operation not allowed on first step');
        } else if (!calculation.operation) {
            this.step.addControlError(cx, 'invalid calculate step: no operation');
        } else if (!CalculateHelper.isOperation(calculation.operation)) {
            this.step.addControlError(cx, `invalid calculate step: invalid operation: ${calculation.operation}`);
        }
    }

    control(cx: ValidationContext): void {
        const { calculationSteps } = this.step.rawConfig;
        calculationSteps.forEach((calculation, index) => this.controlCalculate(cx, calculation, index));
    }
}
