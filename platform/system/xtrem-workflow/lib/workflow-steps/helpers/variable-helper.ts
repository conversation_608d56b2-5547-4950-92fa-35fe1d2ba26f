import { Datetime, DateValue } from '@sage/xtrem-date-time';
import { Decimal, newDecimal } from '@sage/xtrem-decimal';
import { WorkflowVariable } from '@sage/xtrem-shared';
import { WorkflowStep } from '../../engine';

export class VariableHelper {
    constructor(
        readonly step: WorkflowStep,
        readonly variable: WorkflowVariable,
    ) {}

    typeError(value: unknown): Error {
        const got = value == null ? String(value) : `${typeof value} '${value}'`;
        return this.step.logicError(`${this.variable.path}: Expected ${this.variable.type}, got ${got}`);
    }

    getTypedValue(value: unknown): unknown {
        if (value == null) return value;
        switch (this.variable.type) {
            case 'Boolean':
                if (typeof value !== 'boolean') throw this.typeError(value);
                return !!value;

            // date types
            case 'Date':
                if (DateValue.isDate(value)) return value;
                if (typeof value !== 'string') throw this.typeError(value);
                return DateValue.parse(value);
            case 'DateTime':
                if (Datetime.isDatetime(value)) return value;
                if (typeof value !== 'string') throw this.typeError(value);
                return Datetime.parse(value);

            // numeric types
            case 'Decimal':
                if (Decimal.isDecimal(value)) return value;
                if (typeof value !== 'string' && typeof value !== 'number') throw this.typeError(value);
                try {
                    return newDecimal(String(value));
                } catch (error) {
                    throw this.typeError(value);
                }
            case 'Float':
                if (typeof value !== 'number') throw this.typeError(value);
                return value;
            case 'Int':
                if (typeof value !== 'number') throw this.typeError(value);
                return value;

            // string types
            case 'Enum':
                if (typeof value !== 'string') throw this.typeError(value);
                return value;
            case 'String':
                if (typeof value !== 'string') throw this.typeError(value);
                return value;

            // misc types
            case 'IntOrString':
            case 'IntReference':
                if (typeof value !== 'string' && typeof value !== 'number') throw this.typeError(value);
                return value;
            case 'Json':
                return value;

            default:
                throw this.step.logicError(`Unknown variable type: ${this.variable.type}`);
        }
    }
}
