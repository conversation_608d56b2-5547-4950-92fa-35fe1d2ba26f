import { LogicError } from '@sage/xtrem-core';
import { DateRange, Datetime, DateValue } from '@sage/xtrem-date-time';
import { Decimal, newDecimal } from '@sage/xtrem-decimal';
import { TimeFrameValues, timeFrameValues } from '@sage/xtrem-filter-utils';
import { FilterTypeValue, RANGE_DIVIDER, WorkflowVariable } from '@sage/xtrem-shared';
import { WorkflowStep } from '../../engine';
import { WorkflowCondition } from '../../shared-functions';
import { VariableHelper } from './variable-helper';

type Operator = (helper: ConditionHelper, v1: any, v2: any) => boolean;

export class ConditionHelper {
    private readonly getVariableDefinition: (path: string) => WorkflowVariable;

    private readonly getVariableValue: (path: string) => unknown;

    constructor(
        private readonly step: WorkflowStep,
        { getVariableDefinition, getVariableValue } = {
            getVariableDefinition: step.getVariableDefinition.bind(step),
            getVariableValue: step.getVariableValue.bind(step),
        },
    ) {
        this.getVariableDefinition = getVariableDefinition;
        this.getVariableValue = getVariableValue;
    }

    private parseNumber(str: string): number {
        const value = parseFloat(str);
        if (!Number.isFinite(value)) throw this.step.logicError(`invalid number: ${str}`);
        return value;
    }

    /**
     * Convert the operands to comparable values.
     *
     * v1 is obtained from a variable, but v2 maybe a string or a variable.
     * So we rely on the type of v1 to determine how to convert v2.
     *
     * This file is preprocessed so operators will be converted to typesLib functions calls
     * that know how to deal with decimals.
     */
    private getComparableOperands(v1: unknown, v2: unknown): [any, any] {
        if (v1 == null || v2 == null) return [v1 ?? null, v2 ?? null];
        if (typeof v1 === 'string') {
            if (typeof v2 === 'string') return [v1, v2];
            throw this.step.logicError(`Cannot compare string with: ${v2}`);
        }
        if (Decimal.isDecimal(v1)) {
            if (Decimal.isDecimal(v2) || typeof v2 === 'number') return [v1, v2];
            if (typeof v2 === 'string') return [v1, newDecimal(v2)];
            throw this.step.logicError(`Cannot compare decimal with: ${v2}`);
        }
        if (typeof v1 === 'number') {
            if (typeof v2 === 'number') return [v1, v2];
            if (typeof v2 === 'string') return [v1, this.parseNumber(v2)];
            throw this.step.logicError(`Cannot compare number with: ${v2}`);
        }

        if (DateValue.isDate(v1)) {
            if (DateValue.isDate(v2)) return [v1.value, v2.value];
            // null date is before any date so 0 will do (date values are >= 1000_00_00)
            if (v2 === '') return [v1.value, 0];
            if (typeof v2 === 'string') return [v1.value, DateValue.parse(v2).value];
            throw this.step.logicError(`Cannot compare date with: ${v2}`);
        }
        if (Datetime.isDatetime(v1)) {
            if (Datetime.isDatetime(v2)) return [v1.value, v2.value];
            // null date is before any date so 0 will do (date values are >= 1000_00_00)
            if (v2 === '') return [v1.value, 0];
            if (typeof v2 === 'string') return [v1.value, Datetime.parse(v2).value];
            throw this.step.logicError(`Cannot compare datetime with: ${v2}`);
        }
        throw this.step.logicError(`Cannot compare ${typeof v1} with ${typeof v2}`);
    }

    private static comparisonOperator(cmp: (v1: any, v2: any) => boolean): Operator {
        return (helper, v1, v2) => {
            const [comparableV1, comparableV2] = helper.getComparableOperands(v1, v2);
            return cmp(comparableV1, comparableV2);
        };
    }

    private checkTypeof(v: any, type: string): void {
        if (typeof v !== type) throw this.step.logicError(`Expected ${type}, got ${v == null ? v : typeof v}`);
    }

    private static stringOperator(func: (v1: string, v2: string) => boolean): Operator;
    private static stringOperator(func: (v1: string) => boolean): Operator;
    private static stringOperator(func: ((v1: string, v2: string) => boolean) | ((v1: string) => boolean)): Operator {
        return (helper, v1, v2) => {
            helper.checkTypeof(v1, 'string');
            if (func.length === 2) {
                helper.checkTypeof(v2, 'string');
                return (func as (v1: string, v2: string) => boolean)(v1, v2);
            }
            return (func as (v1: string) => boolean)(v1);
        };
    }

    private static matchesOperator(): Operator {
        return (helper, v1, v2) => {
            if (typeof v1 === 'boolean') {
                const convertedV2 = typeof v2 === 'string' || v2 == null ? Boolean(v2) : v2;
                helper.checkTypeof(convertedV2, 'boolean');
                return v1 === convertedV2;
            }
            helper.checkTypeof(v1, 'string');
            helper.checkTypeof(v2, 'string');
            return new RegExp(v2, 'i').test(v1);
        };
    }

    private static inRangeOperator(): Operator {
        return (helper, v1, v2) => {
            if (typeof v2 !== 'string') throw helper.step.logicError(`Cannot compare range with ${typeof v2}`);
            const [start, end] = v2.split(RANGE_DIVIDER);
            const [comparableV1, comparableStart] = helper.getComparableOperands(v1, start);
            const [, comparableEnd] = helper.getComparableOperands(v1, end);
            return comparableV1 >= comparableStart && comparableV1 <= comparableEnd;
        };
    }

    private static setOperator({ negated }: { negated: boolean }): Operator {
        return (helper, v1, v2) => {
            const values = Array.isArray(v2) ? v2 : [v2];
            const found = values.some(v => {
                const [comparableV1, comparableV] = helper.getComparableOperands(v1, v);
                return comparableV1 === comparableV;
            });
            return negated ? !found : found;
        };
    }

    private static timeFrameOperator(): Operator {
        return (helper, v1, v2) => {
            if (typeof v2 !== 'string') throw helper.step.logicError(`Cannot compare time frame with ${typeof v2}`);
            if (!timeFrameValues.includes(v2 as TimeFrameValues))
                throw helper.step.logicError(`Invalid time frame value: ${v2}`);
            const date = Datetime.isDatetime(v1) ? v1.date : v1;
            if (!DateValue.isDate(date)) throw helper.step.logicError(`Invalid time frame value: ${date}`);
            const range = DateRange.getDateRange({ date: DateValue.today().toString(), range: v2 as TimeFrameValues });
            return range.includes(date);
        };
    }

    static ops: { [K in FilterTypeValue | 'timeFrame']: Operator } = {
        equals: this.comparisonOperator((v1, v2) => v1 === v2),
        notEqual: this.comparisonOperator((v1, v2) => v1 !== v2),
        lessThan: this.comparisonOperator((v1, v2) => v1 < v2),
        lessThanOrEqual: this.comparisonOperator((v1, v2) => v1 <= v2),
        greaterThan: this.comparisonOperator((v1, v2) => v1 > v2),
        greaterThanOrEqual: this.comparisonOperator((v1, v2) => v1 >= v2),

        contains: this.stringOperator((v1, v2) => v1.includes(v2)),
        notContains: this.stringOperator((v1, v2) => !v1.includes(v2)),
        startsWith: this.stringOperator((v1, v2) => v1.startsWith(v2)),
        endsWith: this.stringOperator((v1, v2) => v1.endsWith(v2)),
        matches: this.matchesOperator(),
        inRange: this.inRangeOperator(),
        set: this.setOperator({ negated: false }),
        multiNotEqual: this.setOperator({ negated: true }),
        empty: this.stringOperator(v1 => v1 === '' || v1 == null),
        notEmpty: this.stringOperator(v1 => v1 !== '' && v1 != null),
        timeFrame: this.timeFrameOperator(),

        multipleRange: () => {
            // Note: this selector will never be proposed to the user (whatever the type of the property)
            throw new LogicError('NYI: multipleRange condition');
        },
    };

    evaluateVariablePath(path: string): any {
        const variable = this.getVariableDefinition(path);
        const value = this.getVariableValue(path);
        return new VariableHelper(this.step, variable).getTypedValue(value);
    }

    evaluateArg1(condition: WorkflowCondition): any {
        return this.evaluateVariablePath(condition.path);
    }

    evaluateArg2(condition: WorkflowCondition): any {
        if (!condition.useParameter) return condition.value;
        // condition.value is the path to the variable
        const paths = Array.isArray(condition.value)
            ? (condition.value as string[])
            : (condition.value as string).split(RANGE_DIVIDER);
        return paths.map(path => this.evaluateVariablePath(path)).join(RANGE_DIVIDER);
    }

    evaluateCondition(condition: WorkflowCondition): boolean {
        const v1 = this.evaluateArg1(condition);
        const v2 = this.evaluateArg2(condition);
        const op = ConditionHelper.ops[condition.operator];
        if (!op) throw this.step.logicError(`invalid filter type ${condition.operator}`);
        this.step.logVerbose(() => `evaluating condition ${condition.operator} with v1=${v1}, v2=${v2}`);
        return op(this, v1, v2);
    }

    evaluate(conditions: WorkflowCondition[]): boolean {
        return conditions.every(condition => this.evaluateCondition(condition));
    }
}
