import { Dict } from '@sage/xtrem-core';
import { WorkflowVariable } from '@sage/xtrem-shared';
import * as _ from 'lodash';

export type NodeSelector = Dict<NodeSelector> | true;

function getVariableSelectorPath(variable: WorkflowVariable): string {
    // add the value property to the path if the variable is an input stream
    return variable.type === '_InputStream' ? `${variable.path}.value` : variable.path;
}

function getNodeSelector(selectedVariables: WorkflowVariable[]): NodeSelector {
    // Converts [{path: 'a.b.c'}, {path: 'a.d'}] to { a: { b: { c: true }, d: true } }
    const nodeSelector = {} as Dict<NodeSelector>;
    selectedVariables.forEach(variable => {
        const path = getVariableSelectorPath(variable);
        if (variable) _.set(nodeSelector, path, true);
    });
    return nodeSelector;
}

export function getGraphqlSelector(selectedVariables: WorkflowVariable[]): string {
    const nodeSelector = getNodeSelector(selectedVariables);

    // Convert { a: { b: { c: true }, d: true } } to '{ a { b { c } d } }`
    const convert = (key: string, obj: Dict<NodeSelector>): string => {
        if (key === '_customData') {
            return Object.keys(obj)
                .map(k => `${k}__json: _customData(selector: "${k}")`)
                .join(' ');
        }
        return `${key} { ${Object.entries(obj)
            .map(([k, v]) => (v === true ? k : convert(k, v)))
            .join(' ')} }`;
    };
    return convert('', nodeSelector as Dict<NodeSelector>);
}
