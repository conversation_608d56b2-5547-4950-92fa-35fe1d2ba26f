import { AsyncMutationStatus } from '@sage/xtrem-client';
import { Dict } from '@sage/xtrem-core';
import { WorkflowVariable } from '@sage/xtrem-shared';
import axios from 'axios';
import * as _ from 'lodash';
import { WorkflowStep } from '../../engine';
import { getGraphqlSelector } from './selector-helper';

export type GraphqlOperationType = 'query' | 'mutation' | 'asyncMutation';

export interface Request {
    operationType: GraphqlOperationType;

    packageName: string;

    nodeName: string;

    operationName: string;

    args: string;

    selector: string;

    variables: Dict<any>;

    outputPath: string;
}

interface GraphqlResponse<T = any> {
    data: T;
}

export interface AsyncTrackingResponse {
    status: AsyncMutationStatus;
    errorMessage?: string;
    result?: unknown;
}

const useAxios = false;

export class GraphqlHelper {
    constructor(
        private readonly step: WorkflowStep,
        private readonly request: Request,
    ) {}

    static async executeGraphql(
        step: WorkflowStep,
        { query, variables }: { query: string; variables: Dict<any> },
        options: { asRoot?: boolean } = {},
    ): Promise<GraphqlResponse> {
        step.logInfo(`Executing graphql query: ${query} with variables: ${JSON.stringify(variables)}`);
        if (useAxios) {
            const url = 'http://localhost:8240/api';
            try {
                step.logVerbose(() => `[AXIOS] sending graphql request ${query} with ${JSON.stringify(variables)}`);
                const response = await axios.post(url, { query, variables });
                step.logVerbose(() => `[AXIOS] graphql response ${JSON.stringify(response.data)}`);
                return response.data;
            } catch (err) {
                step.logError(`[AXIOS] graphql error ${JSON.stringify(err.response?.data || 'no data')}`);
                throw err;
            }
        } else {
            return step.withReadonlyContext(
                async context => {
                    step.logVerbose(() => `[LOCAL] sending graphql request ${query} with ${JSON.stringify(variables)}`);
                    const data = await context.executeGraphql(query);
                    step.logVerbose(() => `[LOCAL] graphql response ${JSON.stringify(data)}`);
                    await new Promise(setImmediate);
                    return { data };
                },
                {
                    asRoot: options.asRoot,
                },
            );
        }
    }

    executeGraphql({ query, variables }: { query: string; variables: Dict<any> }): Promise<GraphqlResponse> {
        return GraphqlHelper.executeGraphql(this.step, { query, variables });
    }

    private formatAsyncStartMutation(): string {
        const { packageName, nodeName, operationName, args } = this.request;
        return `mutation {
            ${packageName} {
                ${_.camelCase(nodeName)} {
                    ${operationName} {
                        start ${args} { trackingId }
                    }
                }
            }
        }`;
    }

    private formatAsyncTrackingQuery(trackingId: string): string {
        const { packageName, nodeName, operationName, selector } = this.request;
        return `query {
            ${packageName} {
                ${_.camelCase(nodeName)} {
                    ${operationName} {
                        track(trackingId: "${trackingId}") { status, errorMessage, result ${selector} }
                    }
                }
            }
        }`;
    }

    private formatQueryOrMutation(): string {
        const { operationType, packageName, nodeName, operationName, args, selector } = this.request;
        return `${operationType} {
            ${packageName} {
                ${_.camelCase(nodeName)} {
                    ${operationName}${args} ${selector}
                }
            }
        }`;
    }

    private unwrapJsonData(data: any): any {
        if (!data) return data;
        if (Array.isArray(data)) return data.map(item => this.unwrapJsonData(item));
        if (typeof data !== 'object') return data;
        const result = {} as any;

        Object.entries(data).forEach(([key, value]) => {
            if (key.endsWith('__json')) {
                _.merge(result, { _customData: { [key.replace('__json', '')]: JSON.parse(String(value)) } });
            } else {
                result[key] = this.unwrapJsonData(value);
            }
        });
        return result;
    }

    private unwrapResponseData<DataT>(data: any, key?: 'start' | 'track'): DataT {
        const { packageName, nodeName, operationName } = this.request;
        const base = data[packageName][_.camelCase(nodeName)][operationName];
        return this.unwrapJsonData(key ? base?.[key] : base);
    }

    async startAsyncMutation(): Promise<{ trackingId: string }> {
        const query = this.formatAsyncStartMutation();
        const response = await this.executeGraphql({ query, variables: {} });
        return this.unwrapResponseData<{ trackingId: string }>(response.data, 'start');
    }

    async trackAsyncMutation(trackingId: string): Promise<AsyncTrackingResponse> {
        const query = this.formatAsyncTrackingQuery(trackingId);
        this.step.logVerbose(() => `Tracking GraphQL async mutation`);
        const response = await this.executeGraphql({ query, variables: {} });
        return this.unwrapResponseData<AsyncTrackingResponse>(response.data, 'track');
    }

    async executeQueryOrMutation<T>(): Promise<T> {
        const { variables, operationType } = this.request;
        const query = this.formatQueryOrMutation();
        const response = await this.executeGraphql({ query, variables });
        const result = this.unwrapResponseData<T>(response.data);
        this.step.logVerbose(() => `Graphql ${operationType} succeeded, result: ${JSON.stringify(result)}`);
        return result;
    }

    async readNode(nodeName: string, recordId: string): Promise<any> {
        const { variables } = this.request;
        const query = this.formatQueryOrMutation();
        const response = await this.executeGraphql({ query, variables });
        if (!response.data) throw this.step.logicError(`Record not found: ${nodeName}.${recordId}`);
        const result = this.unwrapResponseData<any>(response.data);
        this.step.logVerbose(() => `Graphql read succeeded, result: ${JSON.stringify(result)}`);
        return result;
    }

    static updateTickVariable: WorkflowVariable = {
        path: '_updateTick',
        type: 'Int',
        title: 'Update Tick',
    };

    static readNode({
        step,
        entityName,
        recordId,
        selectedVariables,
    }: {
        step: WorkflowStep;
        entityName: string;
        recordId: string;
        selectedVariables: WorkflowVariable[];
    }): Promise<any> {
        const { packageName, nodeName } = WorkflowStep.parseEntityName(entityName);
        const graphqlSelector = getGraphqlSelector([...selectedVariables, this.updateTickVariable]);
        const helper = new GraphqlHelper(step, {
            operationType: 'query',
            packageName,
            nodeName,
            operationName: 'read',
            args: `(_id: "${recordId}")`,
            selector: graphqlSelector,
            variables: {},
            outputPath: '',
        });
        return helper.readNode(nodeName, recordId);
    }
}
