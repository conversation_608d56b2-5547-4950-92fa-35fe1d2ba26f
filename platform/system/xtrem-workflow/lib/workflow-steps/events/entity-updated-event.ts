import { integer, ValidationContext, WorkflowStepDescriptor } from '@sage/xtrem-core';
import { LogicError, variableFilters, WorkflowVariable } from '@sage/xtrem-shared';
import * as _ from 'lodash';
import { FlowNode, WorkflowStep } from '../../engine';
import { logger } from '../../engine/logger';
import { WorkflowProcessWrapper } from '../../engine/workflow-process-wrapper';
import { WorkflowProcess } from '../../nodes/workflow-process';
import { EntityUpdatedEventConfig } from '../../shared-functions';
import { ConditionHelper } from '../helpers/condition-helper';
import { GraphqlHelper } from '../helpers/graphql-helper';
import { NotificationEvent } from './notification-event';

export class EntityUpdatedEventStep extends NotificationEvent<EntityUpdatedEventConfig, { recordId: integer }> {
    constructor(processWrapper: WorkflowProcessWrapper, flowNode: FlowNode<EntityUpdatedEventConfig>) {
        const { nodeName } = WorkflowStep.parseEntityName(flowNode.data.entityName);
        const topic = `${nodeName}/updated`;

        super(processWrapper, _.merge(flowNode, { data: { topic } }));
        flowNode.data.outputVariableName = _.camelCase(nodeName);
    }

    async getOldPayload({
        entityName,
        recordId,
        updateTick,
        variables,
    }: {
        entityName: string;
        recordId: string;
        updateTick: number;
        variables: WorkflowVariable[];
    }): Promise<any> {
        const { nodeName } = WorkflowStep.parseEntityName(entityName);
        const factory = this.application.getFactoryByName(nodeName);
        const rootFactory = this.application.getFactoryByName(nodeName).rootFactory;
        const rootTableName = rootFactory.tableName;

        const auditLogFilter = {
            rootTableName,
            recordId,
            newUpdateTick: updateTick - 1,
        };
        const query = `
            query {
                xtremAuditing {
                    sysAuditLog {
                        query(filter: ${JSON.stringify(JSON.stringify(auditLogFilter))}
                        ) {
                            edges { node { recordData } }
                        }
                    }
                }
            }`;

        // Caution: we need to use a root user to query the sysAuditLogs because the triggering user may
        // not have access to the audit logs (an activity controls the access to the audit logs).
        const response = await GraphqlHelper.executeGraphql(this, { query, variables: {} }, { asRoot: true });
        const oldRecordData = response.data.xtremAuditing.sysAuditLog.query?.edges[0]?.node.recordData;
        if (!oldRecordData) throw rootFactory.logicError(`Old record not found: _id=${recordId}, `);
        const oldRecord = JSON.parse(oldRecordData);
        // Only keep the top-level variables (not nested)

        const oldPropertiesName: string[] = [];
        const oldValues: any[] = [];

        let customData: any = null;
        variables.forEach(v => {
            const path = v.path.endsWith('._id') ? v.path.slice(0, -4) : v.path;
            if (v.isCustom) {
                if (customData == null) {
                    customData = {};
                    oldPropertiesName.push('_customData');
                    oldValues.push(customData);
                }
                const oldCustomData = oldRecord._custom_data || {};
                // Path is something like _customData.foo
                const nameParts = path.split('.');
                if (nameParts.length !== 2) {
                    throw this.logicError(`Invalid path for custom field: ${path}`);
                }
                const customFieldName = nameParts[1];
                customData[customFieldName] = oldCustomData[customFieldName];
                return;
            }
            const property = factory.findProperty(path);
            if (!property.isStored) return;
            oldPropertiesName.push(property.name);
            oldValues.push(
                property.isReferenceProperty()
                    ? { _id: oldRecord[property.requiredColumnName] }
                    : oldRecord[property.requiredColumnName],
            );
        });
        return _.zipObject(oldPropertiesName, oldValues);
    }

    /**
     * Checks if the event was reentered with the same origin id and the same record id
     * Returns true if the event is not reentrant, throws otherwise
     */
    async checkReentrancy(): Promise<boolean> {
        const { originId, definitionId } = this.processWrapper;
        // Find the processes with the same workflow definition and the same originId
        const similarProcesses = await this.withReadonlyContext(
            rootContext =>
                rootContext.select(
                    WorkflowProcess,
                    { _id: true, id: true, state: true },
                    { filter: { originId, definition: { id: definitionId } } },
                ),
            { asRoot: true },
        );
        const previousProcess = similarProcesses.find(p => {
            const startState = p.state.stepStates[this.nodeId] as unknown as EntityUpdatedEventStep;
            return startState.privateState.recordId === this.privateState.recordId;
        });
        if (previousProcess) {
            // We could log a warning and return false, but then the process record would not be created and we would
            // only get the warning in the logs. It's better to throw an error so the administator can see that
            // the workflow is reentrant in the process log.
            throw this.logicError(
                `Reentrant event detected: process ${previousProcess.id} already processed this event`,
            );
        }
        return true;
    }

    async checkConditions(): Promise<boolean> {
        const { outputVariableName, entityName, conditions } = this.resolveConfig();
        if (outputVariableName == null) {
            throw this.logicError('Output variable name is not defined');
        }
        const variables = this.processWrapper.flowWrapper.getPrefixedVariables(`${outputVariableName}.`);

        const eventData = this.processWrapper.state.pendingPayloads[this.nodeId];
        if (!eventData._id) throw this.logicError('Invalid event payload: _id not found');
        this.privateState.recordId = eventData._id;
        if (!eventData._updateTick) throw this.logicError('Invalid event payload: _updateTick not found');

        if (!variables.length && !conditions.length) return Promise.resolve(true);

        const stripPrefix =
            (prefixLength: number) =>
            (variable: WorkflowVariable): WorkflowVariable => ({
                ...variable,
                path: variable.path.substring(prefixLength),
            });

        const { not, isOld } = variableFilters;
        // Add _updateTick to the selected paths
        const currentPayload = await GraphqlHelper.readNode({
            step: this,
            entityName,
            recordId: eventData._id,
            selectedVariables: variables.filter(not(isOld)).map(stripPrefix(outputVariableName.length + 1)),
        });

        if (!currentPayload) throw this.logicError(`Record not found: ${eventData._id}`);
        const updateTick = currentPayload._updateTick;

        if (updateTick < eventData._updateTick) {
            // This is an error because _updateTick should never decrease
            throw new LogicError(
                `Invalid event payload: current tick (${updateTick}) is older than event tick (${updateTick})`,
            );
        }

        if (updateTick > eventData._updateTick) {
            // This is only a warning. Another transaction may have been committed after the event was sent
            // To be very safe we should re-fetch the current data from the audit log at eventData._updateTick.
            // We'll worry about this later.
            logger.warn(
                `Record was modified after this update event: current tick=${updateTick} is greater than event tick=${eventData._updateTick}`,
            );
        }

        const oldVariables = this.processWrapper.flowWrapper.getPrefixedVariables(`$old.${outputVariableName}.`);
        const oldPayload = await this.getOldPayload({
            entityName,
            recordId: eventData._id,
            variables: oldVariables.map(stripPrefix('$old.'.length + outputVariableName.length + 1)),
            updateTick,
        });
        if (!oldPayload) throw this.logicError(`Record not found: ${eventData._id}`);

        const payload = {
            [outputVariableName]: currentPayload,
            $old: { [outputVariableName]: oldPayload },
        };
        // Override the pending payload with the full entity data
        this.processWrapper.state.pendingPayloads[this.nodeId] = payload;

        if (!conditions.length) return Promise.resolve(true);

        const allVariables = [...variables, ...oldVariables];
        const findVariable = (path: string): WorkflowVariable => {
            const variable = allVariables.find(v => v.path === path);
            if (!variable) throw this.logicError(`Variable not found: ${path}`);
            return variable;
        };

        return new ConditionHelper(this, {
            getVariableDefinition: findVariable,
            getVariableValue: path => _.get(payload, path),
        }).evaluate(conditions);
    }

    override async accept(): Promise<boolean> {
        return (await this.checkConditions()) && this.checkReentrancy();
    }

    override async control(cx: ValidationContext): Promise<void> {
        await super.control(cx);
        const { conditions, stepVariables } = this.rawConfig;
        if (!Array.isArray(stepVariables)) {
            throw this.logicError(
                cx.localize(
                    '@sage/xtrem-workflow/workflow-entity-updated-step-variables-not-an-array',
                    'stepVariables is not an array',
                ),
            );
        }
        if (!Array.isArray(conditions)) {
            throw this.logicError(
                cx.localize(
                    '@sage/xtrem-workflow/workflow-entity-updated-condition-not-an-array',
                    'conditions is not an array',
                ),
            );
        }
        if (conditions.length > 0 && stepVariables.length === 0) {
            this.addControlError(
                cx,
                cx.localize(
                    '@sage/xtrem-workflow/workflow-entity-updated-no-property-selection',
                    'Invalid event configuration: property selection is required when filter is provided',
                ),
            );
        }
    }

    static override readonly descriptor = {
        type: 'event',
        key: 'entity-updated',
        startTopics: '**/updated',
        title: '',
        description: '',
        ui: {
            icon: 'bright',
            color: '#0060a7ff',
            configurationPage: '@sage/xtrem-workflow/WorkflowEventEntityUpdated',
        },
    } as WorkflowStepDescriptor<EntityUpdatedEventConfig>;
}
