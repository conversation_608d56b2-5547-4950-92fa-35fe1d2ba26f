import { ValidationContext, WorkflowStepDescriptor } from '@sage/xtrem-core';
import { WorkflowVariable } from '@sage/xtrem-shared';
import * as _ from 'lodash';
import { FlowNode, WorkflowProcessWrapper, WorkflowStep } from '../../engine';
import { EntityCreatedEventConfig } from '../../shared-functions';
import { ConditionHelper } from '../helpers/condition-helper';
import { GraphqlHelper } from '../helpers/graphql-helper';
import { NotificationEvent } from './notification-event';

export class EntityCreatedEventStep extends NotificationEvent<EntityCreatedEventConfig> {
    constructor(processWrapper: WorkflowProcessWrapper, flowNode: FlowNode<EntityCreatedEventConfig>) {
        const { nodeName } = WorkflowStep.parseEntityName(flowNode.data.entityName);
        const topic = `${nodeName}/created`;

        super(processWrapper, _.merge(flowNode, { data: { topic } }));
        flowNode.data.outputVariableName = _.camelCase(nodeName);
    }

    override async accept(): Promise<boolean> {
        const { entityName, conditions, outputVariableName } = this.resolveConfig();
        if (outputVariableName == null) {
            throw this.logicError('Output variable name is not defined');
        }
        const prefix = `${outputVariableName}.`;
        const variables = this.processWrapper.flowWrapper.getPrefixedVariables(prefix);

        if (!variables.length && !conditions.length) return Promise.resolve(true);

        const eventData = this.processWrapper.state.pendingPayloads[this.nodeId];
        if (!eventData._id) throw this.logicError('Invalid event payload: _id not found');

        const relativeVariables = variables.map(variable => ({
            ...variable,
            path: variable.path.slice(prefix.length),
        }));
        const responseData = await GraphqlHelper.readNode({
            step: this,
            entityName,
            recordId: eventData._id,
            selectedVariables: relativeVariables,
        });

        // Override the pending payload with the full entity data
        this.processWrapper.state.pendingPayloads[this.nodeId] = { [outputVariableName]: responseData };

        if (!conditions.length) return Promise.resolve(true);

        const findVariable = (path: string): WorkflowVariable => {
            const variable = variables.find(v => v.path === path);
            if (!variable) throw this.logicError(`Variable not found: ${path}`);
            return variable;
        };

        const prefixedData = {
            [outputVariableName]: responseData,
        };
        return new ConditionHelper(this, {
            getVariableDefinition: findVariable,
            getVariableValue: path => _.get(prefixedData, path),
        }).evaluate(conditions);
    }

    override async control(cx: ValidationContext): Promise<void> {
        await super.control(cx);
        const { conditions, stepVariables } = this.rawConfig;
        if (!Array.isArray(stepVariables)) {
            throw this.logicError(
                cx.localize(
                    '@sage/xtrem-workflow/workflow-entity-created-step-variables-not-an-array',
                    'stepVariables is not an array',
                ),
            );
        }
        if (!Array.isArray(conditions)) {
            throw this.logicError(
                cx.localize(
                    '@sage/xtrem-workflow/workflow-entity-created-condition-not-an-array',
                    'conditions is not an array',
                ),
            );
        }
        if (conditions.length > 0 && stepVariables.length === 0) {
            this.addControlError(
                cx,
                cx.localize(
                    '@sage/xtrem-workflow/workflow-entity-created-no-property-selection',
                    'Invalid event configuration: property selection is required when filter is provided',
                ),
            );
        }
    }

    static override readonly descriptor = {
        type: 'event',
        key: 'entity-created',
        startTopics: '**/created',
        title: '',
        description: '',
        ui: {
            icon: 'addons',
            color: '#335b70ff',
            configurationPage: '@sage/xtrem-workflow/WorkflowEventEntityCreated',
        },
    } as WorkflowStepDescriptor<EntityCreatedEventConfig>;
}
