import { WorkflowStepDescriptor } from '@sage/xtrem-core';
import * as _ from 'lodash';
import { FlowNode, WorkflowStep } from '../../engine';
import { WorkflowProcessWrapper } from '../../engine/workflow-process-wrapper';
import { workflowAdvanced } from '../../service-options';
import { WorkflowEventConfig } from '../../shared-functions';
import { NotificationEvent } from './notification-event';

export interface EntityDeletedEventConfig extends WorkflowEventConfig {
    node: string;
}

export class EntityDeletedEventStep extends NotificationEvent<EntityDeletedEventConfig> {
    constructor(processWrapper: WorkflowProcessWrapper, flowNode: FlowNode<EntityDeletedEventConfig>) {
        const { nodeName } = WorkflowStep.parseEntityName(flowNode.data.node);
        const topic = `${nodeName}/deleted`;

        super(processWrapper, _.merge(flowNode, { data: { topic, outputVariableName: '' } }));
    }

    static override readonly descriptor = {
        type: 'event',
        key: 'entity-deleted',
        startTopics: '**/deleted',
        title: '',
        description: '',
        serviceOptions: () => [workflowAdvanced],

        ui: {
            icon: 'undo',
            color: '#000000ff',
            configurationPage: '@sage/xtrem-workflow/WorkflowEventEntityDeleted',
        },
    } as WorkflowStepDescriptor<EntityDeletedEventConfig>;
}
