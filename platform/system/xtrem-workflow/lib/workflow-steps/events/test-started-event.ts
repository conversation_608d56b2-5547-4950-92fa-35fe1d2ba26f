import { Datetime, DateValue, Dict, WorkflowStepDescriptor } from '@sage/xtrem-core';
import { WorkflowVariableType } from '@sage/xtrem-shared';
import * as _ from 'lodash';
import { FlowNode, WorkflowProcessWrapper } from '../../engine';
import { workflowAdvanced } from '../../service-options';
import { WorkflowEventConfig } from '../../shared-functions';
import { NotificationEvent } from './notification-event';

export interface TestParameter {
    name: string;
    type: WorkflowVariableType;
}

export interface TestStartedPayload {
    parameters: TestParameter[];
    workflow: string;
}

export interface TestStartedEventConfig extends WorkflowEventConfig, TestStartedPayload {}

const topic = 'WorkflowProcess/testStarted';

export class TestStartedEvent extends NotificationEvent<TestStartedEventConfig> {
    constructor(processWrapper: WorkflowProcessWrapper, flowNode: FlowNode<TestStartedEventConfig>) {
        super(processWrapper, _.merge(flowNode, { data: { topic } }));
    }

    private static defaultParameterValue(type: WorkflowVariableType): any {
        switch (type) {
            case 'String':
                return '';
            case 'Boolean':
                return false;
            case 'Decimal':
                return 0.0;
            case 'Int':
                return 0;
            case 'Date':
                return DateValue.today();
            case 'Datetime':
                return Datetime.now();
            default:
                return '';
        }
    }

    override async wakeUp(): Promise<void> {
        this.enter();
        const payload = this.processWrapper.state.pendingPayloads[this.nodeId] as TestStartedEventConfig;
        if (payload.workflow !== this.processWrapper.definitionId)
            throw this.logicError(
                `invalid process: expected ${this.processWrapper.definitionId}, got ${payload.workflow}`,
            );

        const test = { parameters: {} } as { parameters: Dict<any> };
        const config = this.resolveConfig();
        config.parameters.forEach(parameter => {
            test.parameters[parameter.name] = TestStartedEvent.defaultParameterValue(parameter.type);
        });
        _.merge(test, { parameters: payload.parameters });

        await this.exit('success', { result: { test } });
    }

    static override readonly descriptor = {
        type: 'event',
        key: 'test-started',
        startTopics: topic,
        title: '',
        description: '',
        serviceOptions: () => [workflowAdvanced],
        ui: {
            icon: 'binocular',
            color: '#000000ff',
            configurationPage: '@sage/xtrem-workflow/WorkflowEventTestStarted',
        },
    } as WorkflowStepDescriptor<TestStartedEventConfig>;
}
