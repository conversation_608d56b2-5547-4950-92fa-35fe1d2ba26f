import { WorkflowEvent } from '../../engine';
import { NotificationEventConfig } from '../../shared-functions';

export abstract class NotificationEvent<ConfigT extends {} = {}, PrivateStateT = unknown> extends WorkflowEvent<
    NotificationEventConfig & ConfigT,
    PrivateStateT
> {
    get topic(): string {
        return this.rawConfig.topic;
    }

    // eslint-disable-next-line class-methods-use-this
    prepareEventPayload(payload: any): Promise<any> {
        return payload;
    }

    override async wakeUp(): Promise<void> {
        this.enter();
        const result = this.processWrapper.state.pendingPayloads[this.nodeId];
        await this.exit('success', { result });
    }
}
