import { sleepMillis } from '@sage/xtrem-core';
import { AsyncResponse, Dict } from '@sage/xtrem-shared';
import * as _ from 'lodash';
import { WorkflowAction } from '../../engine';
import { WorkflowGraphqlActionConfig } from '../../shared-functions';
import { AsyncTrackingResponse, GraphqlHelper, Request } from '../helpers/graphql-helper';

export interface GraphqlActionPrivateState {
    start?: { trackingId: string };
    track?: AsyncTrackingResponse;
}

export abstract class GraphqlAction<
    ConfigT extends WorkflowGraphqlActionConfig,
    PrivateStateT extends GraphqlActionPrivateState = GraphqlActionPrivateState,
> extends WorkflowAction<ConfigT, PrivateStateT> {
    #request: Request;

    abstract buildRequest(): AsyncResponse<Request>;

    /**
     * Convert a list of path to a graphQL selector
     * @example
     * _dottedPathsToSelector(['_id', 'billToCustomer._id', 'billToCustomer.name', 'name']) => '_id name billToCustomer {_id name}'
     */
    private static _dottedPathsToSelector(paths: string[]): string {
        const tree: Dict<any> = {};

        // Transform the list of paths to a tree
        // in the example:. ['_id', 'billToCustomer._id', 'billToCustomer.name', 'name'] => { _id: true, name: true, billToCustomer: { _id: true, name: true } }
        paths.forEach(path => {
            _.set(tree, path, true);
        });

        // Convert the tree to a GraphQL selector string
        // in the example: { _id: true, name: true, billToCustomer: { _id: true, name: true } } => '_id name billToCustomer {_id name}'
        const flattenNode = (node: any): string => {
            return Object.entries(node)
                .map(([key, value]) => {
                    if (value === true) return key;
                    return `${key} { ${flattenNode(value)} }`;
                })
                .join(' ');
        };

        return flattenNode(tree);
    }

    private _fixRequestSelector(): void {
        const selector = this.#request.selector;
        if (!/\b_id\b/.test(selector)) return;
        const { outputVariableName } = this.resolveConfig();
        const variables = this.flowWrapper.getPrefixedVariables(outputVariableName);
        const regExp = new RegExp(`^${outputVariableName}.`);
        const variablesToAdd = GraphqlAction._dottedPathsToSelector(variables.map(v => v.path.replace(regExp, '')));

        if (variablesToAdd === '') return;
        this.#request.selector = selector.replace(/\b_id\b/, variablesToAdd);
    }

    private async getRequest(): Promise<Request> {
        if (!this.#request) {
            this.#request = await this.buildRequest();
            this._fixRequestSelector();
        }
        return this.#request;
    }

    async startAsyncMutation(): Promise<void> {
        const request = await this.getRequest();
        const helper = new GraphqlHelper(this, request);
        this.privateState.start = await helper.startAsyncMutation();

        await this.checkpoint(
            `async mutation started: ${request.nodeName}.${request.operationName} args: ${request.args}`,
        );
    }

    private prefixedResult(result: any): any {
        const { outputVariableName } = this.resolveConfig();
        return { [outputVariableName]: result };
    }

    async trackAsyncMutation(): Promise<void> {
        const request = await this.getRequest();
        const trackingId = this.privateState.start?.trackingId;
        if (!trackingId) throw this.logicError('no tracking id');

        const helper = new GraphqlHelper(this, request);
        const track = await helper.trackAsyncMutation(trackingId);
        this.privateState.track = track;

        const status = track.status;
        // TODO: Do we need logMessages here?
        if (status === 'error') {
            this.logError(`Async mutation failed: ${JSON.stringify(track.errorMessage)}`);
            await this.exit('error', { errorMessage: track.errorMessage });
        } else if (status === 'success') {
            this.logInfo(`Async mutation succeeded: ${JSON.stringify(track.result)}`);
            const result = await this.getResultFromGraphqlResult(track.result);
            await this.exit('success', { result: this.prefixedResult(result) });
        } else {
            this.logVerbose(() => `Async mutation before wait ...`);
            await sleepMillis(1000);
            this.logVerbose(() => `Async mutation after wait ...`);
            await this.checkpoint(`async mutation polling returned ${status}`);
        }
    }

    private async wakeUpAsyncMutation(): Promise<void> {
        if (this.privateState.start) {
            await this.trackAsyncMutation();
        } else {
            this.enter();
            await this.startAsyncMutation();
        }
    }

    protected async completeMutation(graphqlResult: any): Promise<void> {
        const result = await this.getResultFromGraphqlResult(graphqlResult);
        await this.exit('success', { result: this.prefixedResult(result) });
    }

    protected async executeQueryOrMutation<T>(): Promise<T> {
        const request = await this.getRequest();
        const helper = new GraphqlHelper(this, request);
        return helper.executeQueryOrMutation();
    }

    private async wakeUpQueryOrMutation(): Promise<void> {
        this.enter();
        const result = await this.executeQueryOrMutation();
        await this.completeMutation(result);
    }

    async getResultFromGraphqlResult<ResultT = unknown>(graphqlResult: any): Promise<ResultT> {
        const request = await this.getRequest();
        return request.outputPath ? _.get(graphqlResult, request.outputPath) : graphqlResult;
    }

    override async wakeUp(): Promise<void> {
        const request = await this.getRequest();
        switch (request.operationType) {
            case 'asyncMutation':
                await this.wakeUpAsyncMutation();
                break;
            case 'mutation':
            case 'query': {
                // split in 2 because we override in complex mutations
                await this.wakeUpQueryOrMutation();
                break;
            }
            default:
                throw this.logicError(`invalid operationType: ${request.operationType}`);
        }
    }
}
