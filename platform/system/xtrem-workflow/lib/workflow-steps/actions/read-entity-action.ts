import { Dict, WorkflowStepDescriptor } from '@sage/xtrem-core';
import { WorkflowVariable } from '@sage/xtrem-shared';
import * as _ from 'lodash';
import { WorkflowAction, WorkflowStep } from '../../engine';
import { workflowAdvanced } from '../../service-options';
import { NodeReadActionConfig } from '../../shared-functions/read-entity-action-config';
import { GraphqlHelper } from '../helpers/graphql-helper';

export class NodeReadAction<T extends {} = {}> extends WorkflowAction<NodeReadActionConfig & T> {
    /** converts the selected properties dict to a graphql selector */
    // eslint-disable-next-line class-methods-use-this
    formatSelector(variables: WorkflowVariable[]): string {
        // Converts ['a', 'b', 'c'] to { a: { b: { c: true } } }
        const makeObject = (path: string[]): Dict<any> => {
            const [head, ...rest] = path;
            return { [head]: rest.length ? makeObject(rest) : true };
        };
        // Convert ['a.b.c', 'a.b.d'] to { a: { b: { c: true, d: true } } }
        const selectorObj = _.merge({}, ...variables.map(variable => makeObject(variable.path.split('.'))));

        // Convert { a: { b: { c: true, d: true } } } to '{ a { b { c d } } }`
        const makeString = (key: string, obj: Dict<any>): string => {
            return `${key} { ${Object.keys(obj)
                .map(k => (obj[k] === true ? k : makeString(k, obj[k])))
                .join(' ')} }`;
        };
        return makeString('', selectorObj);
    }

    override async wakeUp(): Promise<void> {
        const { recordKey, entityName, stepVariables, failIfNotFound, outputVariableName } = this.resolveConfig();
        if (!stepVariables?.length) throw this.logicError('step variables missing');
        const { packageName, nodeName } = WorkflowStep.parseEntityName(entityName);
        const nodeCamelName = _.camelCase(nodeName);

        if (!recordKey) throw this.logicError('record key missing');

        const selectorVariables = stepVariables.map(variable => ({
            ...variable,
            path: variable.path.slice(outputVariableName.length + 1),
        }));
        const selector = this.formatSelector(selectorVariables);
        const query = `
            query {
                ${packageName} {
                    ${nodeCamelName} {
                        read(_id: "#${recordKey}")${selector}
                    }
                }
            }`;

        this.enter();
        const response = await GraphqlHelper.executeGraphql(this, { query, variables: {} });
        const result = response.data[packageName]?.[nodeCamelName]?.read;
        if (result == null) {
            this.logVerbose(() => 'Graphql query failed');
            if (failIfNotFound) {
                await this.exit('error', { errorMessage: `record with key '${recordKey}' not found` });
            } else {
                await this.exit('success', { result: { [outputVariableName]: null } });
            }
            return;
        }
        this.logVerbose(() => `Graphql query succeeded: ${JSON.stringify(result, null, 4)}`);

        await this.exit('success', { result: { [outputVariableName]: result } });
    }

    static override readonly descriptor = {
        type: 'action',
        key: 'read-entity',
        title: '',
        description: '',
        serviceOptions: () => [workflowAdvanced],
        minOutputs: 1,
        ui: {
            icon: 'binocular',
            color: '#335b70ff',
            configurationPage: '@sage/xtrem-workflow/WorkflowActionReadEntity',
        },
    } as WorkflowStepDescriptor<NodeReadActionConfig>;
}
