import { LogicError, ValidationContext, WorkflowStepDescriptor } from '@sage/xtrem-core';
import { nodes as metadataNodes } from '@sage/xtrem-metadata';
import { Dict } from '@sage/xtrem-shared';
import * as _ from 'lodash';
import { WorkflowMutationActionConfig } from '../../shared-functions/workflow-mutation-action-config';
import { GraphqlOperationType, Request } from '../helpers/graphql-helper';
import { GraphqlAction } from './graphql-action';

export class WorkflowMutationAction<ConfigT extends WorkflowMutationActionConfig> extends GraphqlAction<ConfigT> {
    private async _getMetaOperation(): Promise<metadataNodes.MetaNodeOperation | null> {
        const config = this.resolveConfig();
        if (config.mutationNaturalKey == null || config.mutationNaturalKey === '') {
            throw new LogicError(`${this.nodeId}: no mutationNaturalKey defined in the action configuration`);
        }

        const operation = await this.withReadonlyContext(
            context => context.tryRead(metadataNodes.MetaNodeOperation, { _id: config.mutationNaturalKey }),
            { asRoot: true }, // We need 'asRoot' when called from the mutation that controls the diagram
        );
        return operation;
    }

    private _buildMutationArguments(config: WorkflowMutationActionConfig): string {
        const args = config.mutationArguments
            .map(arg => {
                const getValue = (): any => {
                    if (arg.origin === 'manual') {
                        if (arg.type === 'boolean') {
                            // Note: arg.value will be undefined if the user saved the configuration page
                            // without setting/unsetting the checkbox
                            return !!arg.value;
                        }
                        return arg.value;
                    }
                    if (arg.origin === 'fromVariable') {
                        // The value is a variable
                        return this.getVariableValue(arg.value as string);
                    }
                    // The value is in the actionParameters of the action
                    const actionParameter = config.actionParameters.find(p => p.name === arg.name);
                    if (actionParameter == null)
                        throw this.logicError(`Could not find any parameter for mutation argument ${arg.name}`);
                    // When actionParameter.isVariable is true, value is a string that contains the variable name
                    return actionParameter.isVariable
                        ? this.getVariableValue(actionParameter.value as string)
                        : actionParameter.value;
                };
                const value = getValue();
                if (arg.type === 'reference' && value != null && typeof value === 'object' && value._id != null) {
                    // The value is a reference, we need to convert it to the id
                    return `${arg.name}: ${value._id}`;
                }
                return `${arg.name}: ${JSON.stringify(value)}`;
            })
            .filter(a => a);
        return `(${args.join(', ')})`;
    }

    /**
     * Returns the values for the local variables
     */
    // eslint-disable-next-line class-methods-use-this
    protected override _getLocalVariablesValues(config: ConfigT): Dict<any> {
        return {
            _: config.actionParameters.reduce((total, actionParameter) => {
                total[actionParameter.name] = actionParameter.value;
                return total;
            }, {} as Dict<any>),
        };
    }

    /** Gets the value of a variable in the process state */
    override getVariableValue(path: string): any {
        const callSuper = (variablePath: string): any => {
            const v = super.getVariableValue(variablePath);
            if (v == null) {
                const cfg = this.resolveConfig();
                throw this.logicError(
                    `Mutation ${cfg.mutationNaturalKey}: the variable ${variablePath} could not be found`,
                );
            }
            return v;
        };
        if (!path.startsWith('_local.')) return callSuper(path);

        const config = this.resolveConfig();
        // remove the leading prefix '_local.'
        const parameterName = path.substring(7);
        const actionParameter = config.actionParameters.find(p => p.name === parameterName);
        if (actionParameter == null) return undefined;
        if (actionParameter.isVariable) {
            // the actionParameter refers to a variable
            return callSuper(actionParameter.value as string);
        }
        const parameterDefinition = config.mutationArguments.find(p => p.name === parameterName);
        if (parameterDefinition == null) throw this.logicError(`No definition found for parameter ${parameterName}`);
        const value = actionParameter.value;
        if (parameterDefinition.type === 'reference' && value != null && typeof value === 'object') {
            return (value as any)._id;
        }
        return value;
    }

    /**
     * Convert a full package name to a short one (@sage/xtrem-workflow -> xtremWorkflow)
     */
    private static _fixPackageName(packageName: string): string {
        const parts = packageName.split('/');
        if (parts.length < 2) return packageName;
        return _.camelCase(parts[1]);
    }

    override async buildRequest(): Promise<Request> {
        const config = this.resolveConfig();

        const metaOperation = await this._getMetaOperation();
        if (metaOperation == null) {
            throw this.logicError(`Could not find the mutation with natural key ${config.mutationNaturalKey}`);
        }

        const factory = await metaOperation.factory;
        const nodeName = _.camelCase(await factory.name);
        const packageName = WorkflowMutationAction._fixPackageName(await (await factory.package).name);

        const args = this._buildMutationArguments(config);
        this.logInfo(`Running mutation ${packageName}.${config.mutationNaturalKey} with args: ${args}`);

        return {
            operationType: (await metaOperation.kind) as GraphqlOperationType,
            packageName,
            nodeName,
            operationName: await metaOperation.name,
            args,
            selector: config.selector,
            variables: [],
            outputPath: '',
        };
    }

    override async control(cx: ValidationContext): Promise<void> {
        await super.control(cx);
        const mutation = await this._getMetaOperation();
        if (mutation == null) {
            cx.error.add(
                cx.localize(
                    '@sage/xtrem-workflow/workflow_unknown_mutation',
                    'The mutation does not exist: {{mutationName}}.',
                    { mutationName: this.resolveConfig().mutationNaturalKey },
                ),
            );
        }
        // Ensure that all the mandatory parameters are defined
        const config = this.resolveConfig();
        const parameters = (await mutation?.parameters) ?? [];
        parameters.forEach(parameter => {
            const parameterName = parameter.name;
            if (parameter.isMandatory && !config.mutationArguments.some(arg => arg.name === parameterName)) {
                cx.error.add(
                    cx.localize(
                        '@sage/xtrem-workflow/workflow_mutation_argument_not_found',
                        'The mutation argument is not defined in the action configuration: {{argument}}.',
                        { argument: parameterName },
                    ),
                );
            }
        });

        // Ensure that all the mutationArguments match the mutation parameters
        const mutationArguments = config.mutationArguments;
        mutationArguments.forEach(argument => {
            const parameter = parameters?.find(p => p.name === argument.name);
            if (parameter == null) {
                cx.error.add(
                    cx.localize(
                        '@sage/xtrem-workflow/workflow_mutation_action_argument_not_found',
                        'The action refers to the argument: {{argumentName}}, that does not exist in the mutation: {{mutationName}}.',
                        { argumentName: argument.name, mutationName: config.mutationNaturalKey },
                    ),
                );
            } else if (parameter.type !== argument.type) {
                cx.error.add(
                    cx.localize(
                        '@sage/xtrem-workflow/workflow_mutation_argument_type_mismatch',
                        'The mutation argument has a type mismatch: {{argument}}. Expected: {{expectedType}}, got: {{actualType}}.',
                        {
                            argument: argument.name,
                            expectedType: parameter.type,
                            actualType: argument.type,
                        },
                    ),
                );
            }
        });
    }

    static override readonly descriptor = {
        type: 'action',
        key: 'mutation',
        title: '',
        description: '',
        minOutputs: 0,
        serviceOptions: () => [],
        ui: {
            icon: 'connected',
            color: '#ef6700ff',
            configurationPage: '@sage/xtrem-workflow/WorkflowMutationAction',
        },
    } as WorkflowStepDescriptor<WorkflowMutationActionConfig>;
}
