import { Context, ValidationContext, WorkflowStepDescriptor } from '@sage/xtrem-core';
import { IdType, InitialNotification, getTextForLocale } from '@sage/xtrem-shared';
import { WorkflowAction } from '../../engine';
import { SendUserNotificationActionConfig, SendUserNotificationActionRecipient } from '../../shared-functions';

export class SendUserNotificationAction<T extends {} = {}> extends WorkflowAction<
    SendUserNotificationActionConfig & T
> {
    private _getUserId(recipient: SendUserNotificationActionRecipient): IdType {
        const { user, isManuallySet } = recipient;

        const getId = (): IdType => {
            if (isManuallySet) {
                // user should be an object { _id, email }
                if (typeof user === 'string') throw this.logicError(`Invalid format for user: ${user}`);
                return user._id;
            }
            // user should be a path
            if (typeof user !== 'string') throw this.logicError(`Invalid format for user: ${user}`);
            return this.getVariableValue(user);
        };

        const userId = getId();
        if (!userId) {
            throw this.logicError('User not found');
        }
        return userId;
    }

    private buildNotification(context: Context): InitialNotification {
        const {
            localizedNotificationTitle,
            localizedNotificationDescription,
            icon,
            level,
            actions,
            shouldDisplayToast,
            recipients,
        } = this.resolveConfig();

        const notif: InitialNotification = {
            title: getTextForLocale(localizedNotificationTitle, context.currentLocale) ?? '',
            description: getTextForLocale(localizedNotificationDescription, context.currentLocale) ?? '',
            icon: icon || 'none',
            level: level || 'info',
            shouldDisplayToast: !!shouldDisplayToast,
            actions: actions.map(action => {
                return {
                    style: action.style,
                    title: getTextForLocale(action.localizedTitle, context.currentLocale) ?? '',
                    link: action.link,
                };
            }),
        };
        if (recipients.length > 0) {
            notif.recipientsId = recipients.map(recipient => this._getUserId(recipient));
        }
        return notif;
    }

    override async wakeUp(): Promise<void> {
        await this.withCommittedContext(context => {
            const notification = this.buildNotification(context);
            return context.notifyUser(notification);
        });
        await this.exit('success', {});
    }

    override async control(cx: ValidationContext): Promise<void> {
        await super.control(cx);
        if (!this.rawConfig.localizedNotificationTitle)
            cx.error.add(
                cx.localize(
                    '@sage/xtrem-workflow/workflow-send-user-notification-error-no-notification-title',
                    'Notification title is required',
                ),
            );
        if (!this.rawConfig.localizedNotificationDescription)
            cx.error.add(
                cx.localize(
                    '@sage/xtrem-workflow/workflow-send-user-notification-error-no-notification-description',
                    'Notification description is required',
                ),
            );
    }

    static override readonly descriptor = {
        type: 'action',
        key: 'send-user-notification',
        title: '',
        description: '',
        ui: {
            icon: 'megaphone',
            color: '#335b70ff',
            configurationPage: '@sage/xtrem-workflow/WorkflowActionSendUserNotification',
        },
    } as WorkflowStepDescriptor<SendUserNotificationActionConfig>;
}
