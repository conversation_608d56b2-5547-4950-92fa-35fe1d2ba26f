import { WorkflowStepDescriptor } from '@sage/xtrem-core';
import { WorkflowAction } from '../../engine';
import { workflowAdvanced } from '../../service-options';
import { DeleteEntityActionConfig } from '../../shared-functions';

export class DeleteEntityAction<T extends {} = {}> extends WorkflowAction<DeleteEntityActionConfig & T> {
    override wakeUp(): Promise<void> {
        throw this.logicError('NIY');
    }

    static override readonly descriptor = {
        type: 'action',
        key: 'delete-entity',
        title: '',
        description: '',
        serviceOptions: () => [workflowAdvanced],
        ui: {
            icon: 'undo',
            color: '#335b70ff',
            configurationPage: '@sage/xtrem-workflow/WorkflowActionDeleteEntity',
        },
    } as WorkflowStepDescriptor<DeleteEntityActionConfig>;
}
