import { integer, sleepMillis, WorkflowStepDescriptor } from '@sage/xtrem-core';
import { WorkflowAction } from '../../engine';
import { workflowAdvanced } from '../../service-options';
import { WaitActionConfig } from '../../shared-functions';

export class WaitAction<T extends {} = {}> extends WorkflowAction<WaitActionConfig & T, { wakeUpTime: integer }> {
    getMillis(): number {
        const { quantity, unit } = this.resolveConfig();
        if (unit !== 's') throw this.logicError(`invalid unit: ${unit}`);
        return quantity * 1000;
    }

    override async wakeUp(): Promise<void> {
        const now = Date.now();
        if (!this.privateState.wakeUpTime) {
            // Step hasn't been entered yet
            this.enter();
            const millis = this.getMillis();
            this.privateState.wakeUpTime = now + millis;
            // We save the wakeUpTime in the private state so that this step can be resumed
            // by another container instance if the current one crashes
            // Checkpoint will save and call wakeUp again
            await this.checkpoint(`waiting for ${millis} ms`);
        } else {
            const remaining = Math.max(this.privateState.wakeUpTime - now, 0);
            if (remaining > 0) await sleepMillis(remaining);
            await this.exit('success');
        }
    }

    static override readonly descriptor = {
        type: 'action',
        key: 'wait',
        title: '',
        description: '',
        minOutputs: 1,
        serviceOptions: () => [workflowAdvanced],
        ui: {
            icon: 'hourglass',
            color: '#335b70ff',
            configurationPage: '@sage/xtrem-workflow/WorkflowActionWait',
        },
    } as WorkflowStepDescriptor<WaitActionConfig>;
}
