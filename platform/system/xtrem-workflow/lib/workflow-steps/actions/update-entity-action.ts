import { Decimal, Dict, ValidationContext, WorkflowStepDescriptor } from '@sage/xtrem-core';
import * as _ from 'lodash';
import { WorkflowAction, WorkflowStep } from '../../engine';
import { UpdateEntityActionConfig } from '../../shared-functions/update-entity-action-config';
import { GraphqlHelper } from '../helpers/graphql-helper';
import { getGraphqlSelector } from '../helpers/selector-helper';

export class UpdateEntityAction<T extends {} = {}> extends WorkflowAction<UpdateEntityActionConfig & T> {
    getUpdateValues(): Dict<any> {
        const { pathToUpdate, updateRules } = this.resolveConfig();
        const values: Dict<any> = { _id: String(this.getVariableValue(pathToUpdate)) };
        updateRules.forEach(rule => {
            if (rule.sourceValue == null) return;
            const value = rule.isSourceVariable ? this.getVariableValue(rule.sourceValue) : rule.sourceValue;
            _.set(values, rule.destinationPath, value);
        });
        return values;
    }

    convertValue(value: any): any {
        if (value === null) return value;
        if (Array.isArray(value)) return value.map(v => this.convertValue(v));
        if (typeof value !== 'object') return JSON.stringify(value);
        if (Decimal.isDecimal(value)) return value.toString();
        const inner = Object.entries(value).map(
            ([key, v]) => `${key}: ${key === '_customData' ? JSON.stringify(JSON.stringify(v)) : this.convertValue(v)}`,
        );
        return `{ ${inner.join(', ')} }`;
    }

    override async control(cx: ValidationContext): Promise<void> {
        await super.control(cx);
        if (this.rawConfig.updateRules.length === 0) {
            cx.error.add(
                cx.localize(
                    '@sage/xtrem-workflow/workflow_error_no_property_to_update',
                    'You need to add a property to update.',
                ),
            );
        }
    }

    override async wakeUp(): Promise<void> {
        this.enter();

        const { pathToUpdate, entityName } = this.resolveConfig();
        const { packageName, nodeName } = WorkflowStep.parseEntityName(entityName);

        const prefix = pathToUpdate.replace(/_id$/, '');
        const variables = this.processWrapper.flowWrapper.getPrefixedVariables(prefix);
        const relativeVariables = variables.map(variable => ({
            ...variable,
            path: variable.path.slice(prefix.length),
        }));

        const selector = getGraphqlSelector(relativeVariables);

        const updateValues = this.getUpdateValues();

        const helper = new GraphqlHelper(this, {
            operationType: 'mutation',
            packageName,
            nodeName,
            operationName: 'update',
            args: `(data: ${this.convertValue(updateValues)})`,
            selector,
            variables: {},
            outputPath: '',
        });

        const responseData = await helper.executeQueryOrMutation();
        this.logVerbose(() => `Graphql update mutation succeeded: ${JSON.stringify(responseData, null, 4)}`);

        const outputVariableName = prefix.substring(0, prefix.length - 1);
        await this.exit('success', { result: { [outputVariableName]: responseData } });
    }

    static override readonly descriptor = {
        type: 'action',
        key: 'update-entity',
        title: '',
        description: '',
        ui: {
            icon: 'pencil',
            color: '#335b70ff',
            configurationPage: '@sage/xtrem-workflow/WorkflowActionUpdateEntity',
        },
    } as WorkflowStepDescriptor<UpdateEntityActionConfig>;
}
