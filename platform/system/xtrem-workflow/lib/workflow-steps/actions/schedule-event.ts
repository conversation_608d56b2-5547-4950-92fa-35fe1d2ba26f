import { WorkflowStepDescriptor } from '@sage/xtrem-core';
import { WorkflowEvent } from '../../engine';
import { workflowAdvanced } from '../../service-options';
import { WorkflowEventConfig } from '../../shared-functions';

export interface ScheduleEventConfig extends WorkflowEventConfig {}

export class ScheduleEvent<T extends {} = {}> extends WorkflowEvent<ScheduleEventConfig & T> {
    override wakeUp(): Promise<void> {
        throw this.logicError('NIY');
    }

    static override readonly descriptor = {
        type: 'event',
        key: 'schedule',
        title: '',
        description: '',
        serviceOptions: () => [workflowAdvanced],
        ui: {
            icon: 'clock',
            color: '#0060a7ff',
            configurationPage: '@sage/xtrem-scheduler/WorkflowEventSchedule',
        },
    } as WorkflowStepDescriptor<ScheduleEventConfig>;
}
