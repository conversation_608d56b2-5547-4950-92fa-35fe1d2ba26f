import { ValidationContext, WorkflowStepDescriptor } from '@sage/xtrem-core';
import { WorkflowAction } from '../../engine';
import { workflowAdvanced } from '../../service-options';
import { CalculateActionConfig } from '../../shared-functions';
import { CalculateHelper } from '../helpers/calculate-helper';

export class CalculateAction<T extends {} = {}> extends WorkflowAction<CalculateActionConfig & T> {
    evaluate(): number {
        return new CalculateHelper(this).evaluate();
    }

    override async wakeUp(): Promise<void> {
        this.enter();

        try {
            const value = this.evaluate();
            this.logVerbose(() => `calculate result ${value}`);

            const { outputVariableName } = this.resolveConfig();
            await this.exit('success', { result: { [outputVariableName]: value } });
        } catch (error) {
            this.logVerbose(() => `calculate result failed ${error.stack}`);
            await this.exit('error', { errorMessage: error.message });
        }
    }

    override async control(cx: ValidationContext): Promise<void> {
        await super.control(cx);
        new CalculateHelper(this).control(cx);
    }

    static override readonly descriptor = {
        type: 'action',
        key: 'calculate',
        title: '',
        description: '',
        serviceOptions: () => [workflowAdvanced],
        minOutputs: 1,
        ui: {
            icon: 'accounting',
            color: '#335b70ff',
            configurationPage: '@sage/xtrem-workflow/WorkflowActionCalculate',
        },
    } as WorkflowStepDescriptor<CalculateActionConfig>;
}
