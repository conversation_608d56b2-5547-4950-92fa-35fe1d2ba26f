import { ValidationContext, WorkflowStepDescriptor } from '@sage/xtrem-core';
import { WorkflowAction } from '../../engine';
import { WorkflowActionConfig, WorkflowCondition } from '../../shared-functions';
import { ConditionHelper } from '../helpers/condition-helper';

export type ConditionPropertyKind = 'SCALAR';
export type ConditionPropertyType = 'String';
export type ConditionOp = 'startsWith';
export type ConditionValue = string | number | boolean | null;

export interface ConditionProperty {
    id: string;
    data: {
        kind: ConditionPropertyKind;
        type: ConditionPropertyType;
    };
}

export interface Condition {
    property: ConditionProperty;
    filterType: ConditionOp;
    filterValue: ConditionValue;
}

export interface ConditionActionConfig extends WorkflowActionConfig {
    conditions: WorkflowCondition[];
}

export class ConditionAction<T extends {} = {}> extends WorkflowAction<ConditionActionConfig & T> {
    override async wakeUp(): Promise<void> {
        this.enter();

        const { conditions } = this.resolveConfig();
        const value = new ConditionHelper(this).evaluate(conditions);
        this.logVerbose(() => `condition is ${value}`);

        await this.exit('success', { outputHandle: value ? 'out-true' : 'out-false' });
    }

    private controlCondition(cx: ValidationContext, condition: WorkflowCondition): void {
        if (!ConditionHelper.ops[condition.operator])
            this.addControlError(cx, `invalid filter type: ${condition.operator}`);
    }

    override async control(cx: ValidationContext): Promise<void> {
        await super.control(cx);
        const { conditions } = this.rawConfig;
        conditions.forEach(condition => this.controlCondition(cx, condition));
    }

    static override readonly descriptor = {
        type: 'condition',
        key: 'condition',
        title: '',
        description: '',
        minOutputs: 1,
        ui: {
            icon: 'connected',
            color: '#ef6700ff',
            configurationPage: '@sage/xtrem-workflow/WorkflowActionCondition',
        },
    } as WorkflowStepDescriptor<ConditionActionConfig>;
}
