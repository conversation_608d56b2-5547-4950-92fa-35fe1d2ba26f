import { WorkflowStepDescriptor } from '@sage/xtrem-core';
import { WorkflowAction } from '../../engine';
import { workflowAdvanced } from '../../service-options';
import { TestStubActionConfig } from '../../shared-functions';

export class TestStubAction<T extends {} = {}> extends WorkflowAction<TestStubActionConfig & T> {
    override async wakeUp(): Promise<void> {
        this.enter();
        const { outputVariableName } = this.resolveConfig();
        await this.exit('success', { result: { [outputVariableName]: true } });
    }

    static override readonly descriptor = {
        type: 'action',
        key: 'test-stub',
        title: '',
        description: '',
        serviceOptions: () => [workflowAdvanced],
        ui: {
            icon: 'megaphone',
            color: '#335b70ff',
            configurationPage: '@sage/xtrem-workflow/WorkflowActionTestStub',
        },
    } as WorkflowStepDescriptor<TestStubActionConfig>;
}
