import { Dict, NodeDetails } from '@sage/xtrem-shared';
import { fetchNodeDetails } from '@sage/xtrem-ui';

export class NodeDetailsFetcher {
    private _cache: Dict<Dict<NodeDetails>> = {};

    /**
     * Fetches the details for a given node.
     * @param nodeName The name of the node (without @sage/xtrem-...)
     * @param options The options to pass to the fetchNodeDetails function
     * @returns
     */
    async fetch(
        nodeName: string,
        options: {
            locale?: string;
            getCollections?: boolean;
        } = {},
    ): Promise<Dict<NodeDetails>> {
        const optionsToUse = {
            locale: options.locale ?? 'en-US',
            getCollections: options.getCollections ?? false,
        };
        const key = `${nodeName}:${optionsToUse.locale}:${optionsToUse.getCollections}`;
        const cached = this._cache[key];
        if (cached) return cached;

        const nodeDetails = await fetchNodeDetails({ ...optionsToUse, nodeName });

        const propertiesDict = Object.values(nodeDetails).reduce((total, nodeDetail) => {
            total[nodeDetail.name] = nodeDetail;
            return total;
        }, {} as Dict<NodeDetails>);
        this._cache[key] = propertiesDict;
        return propertiesDict;
    }
}
