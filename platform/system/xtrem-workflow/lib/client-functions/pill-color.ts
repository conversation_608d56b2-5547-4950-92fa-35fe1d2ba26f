import { ColoredElement } from '@sage/xtrem-system-api';
import { colorfulPillPattern } from '@sage/xtrem-system/build/lib/client-functions/color-pattern';
import { WorkflowProcessStatus } from '@sage/xtrem-workflow-api';
import { WorkflowLogEvent } from '../shared-functions/workflow-log';

export function processStatusPillColor(status: WorkflowProcessStatus, coloredElement: ColoredElement): string {
    switch (status) {
        case 'running':
            return colorfulPillPattern.filledFocus[coloredElement];
        case 'success':
            return colorfulPillPattern.filledPositive[coloredElement];
        case 'error':
            return colorfulPillPattern.filledNegative[coloredElement];
        case 'cancelled':
            return colorfulPillPattern.filledCaution[coloredElement];
        case 'suspended':
            return colorfulPillPattern.filledNeutral[coloredElement];
        default:
            return colorfulPillPattern.default[coloredElement];
    }
}

export function logEventPillColor(status: WorkflowLogEvent, coloredElement: ColoredElement): string {
    switch (status) {
        case 'running':
            return colorfulPillPattern.filledFocus[coloredElement];
        case 'success':
            return colorfulPillPattern.filledPositive[coloredElement];
        case 'error':
            return colorfulPillPattern.filledNegative[coloredElement];
        case 'suspended':
            return colorfulPillPattern.filledNeutral[coloredElement];
        default:
            throw new Error(`Unknown log event: ${status}`);
    }
}
