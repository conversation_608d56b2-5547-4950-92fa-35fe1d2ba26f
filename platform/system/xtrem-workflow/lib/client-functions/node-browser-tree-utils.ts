import { asyncArray } from '@sage/xtrem-async-helper';
import { NodeDetails, TreeElement, WorkflowVariable } from '@sage/xtrem-shared';
import { NodeBrowserTreeValue } from '@sage/xtrem-ui/build/lib/component/field/node-browser-tree/node-browser-tree-types';
import { fromPairs } from 'lodash';
import { NodeDetailsFetcher } from './node-details-fetcher';
import { getVariableTitle } from './variable-utils';

export function stripPackageName(entityName: string): string {
    return entityName?.split('/').slice(-1)[0];
}

// Resolve node name for _id properties
async function resolveIdNodeName(
    nodeDetailsFetcher: NodeDetailsFetcher,
    entityName: string,
    path: string[],
): Promise<string | undefined> {
    if (path[path.length - 1] !== '_id') return undefined;

    const nodeName = stripPackageName(entityName);
    const properties = await nodeDetailsFetcher.fetch(nodeName);
    const [name, ...rest] = path;
    if (rest.length === 0) return nodeName;
    const property = properties[name];
    if (!property) throw new Error(`Property ${name} not found in node ${entityName}`);
    if (!property.node) throw new Error(`Property ${name} not found in node ${entityName}`);
    return resolveIdNodeName(nodeDetailsFetcher, property.node, rest);
}

export function convertNodeBrowserTreeValueToVariables(
    nodeDetailsFetcher: NodeDetailsFetcher,
    value: NodeBrowserTreeValue | null,
    { entityName }: { entityName: string | null },
): Promise<WorkflowVariable[]> {
    if (entityName === null) return Promise.resolve([]);
    return asyncArray(Object.entries(value ?? {}))
        .map(
            async ([path, selectedProperty]) =>
                ({
                    path,
                    title: await getVariableTitle(nodeDetailsFetcher, entityName, path),
                    type: selectedProperty.data.type,
                    node:
                        selectedProperty.data.node ||
                        (await resolveIdNodeName(nodeDetailsFetcher, entityName, path.split('.'))),
                    enumType: selectedProperty.data.enumType ?? undefined,
                    enumValues: selectedProperty.data.enumValues ?? undefined,
                    isCustom: selectedProperty.data.isCustom,
                }) as WorkflowVariable,
        )
        .toArray();
}

export function convertVariablesToNodeBrowserTreeValue(variables: WorkflowVariable[]): NodeBrowserTreeValue {
    return fromPairs(
        variables.map(variable => [
            variable.path,
            {
                id: variable.path,
                key: variable.path,
                label: variable.title,
                title: variable.title,
                canBeExpanded: false,
                canBeSelected: true,
                labelKey: variable.title,
                labelPath: variable.title,
                data: {
                    type: variable.type,
                    node: variable.node,
                    // force canFilter to true because filtering is done on workflow variables, not in SQL
                    canFilter: true,
                    canSort: false,
                    kind: 'SCALAR',
                    label: variable.title,
                    name: variable.path,
                    enumType: variable.enumType,
                    enumValues: variable.enumValues,
                    isCustom: variable.isCustom,
                },
            } as TreeElement<NodeDetails>,
        ]),
    );
}
