import { Graph<PERSON>pi, MetaNodeFactory } from '@sage/xtrem-metadata-api';
import { LocalizedText, NodeDetails, WorkflowVariable } from '@sage/xtrem-shared';
import * as ui from '@sage/xtrem-ui';
import * as levenshtein from 'fast-levenshtein';
import { WorkflowStepConfig } from '../shared-functions/workflow-step-config';
import { convertPathFromSerialization } from './variable-utils';

export type WorkflowConfigPageData<T extends WorkflowStepConfig> = T & {
    /**
     * The localized titles (indexed by locale).
     */
    localizedTitle: LocalizedText;

    subtitle: string;
};

export async function restoreNodeFieldFromString(
    pageInstance: ui.Page<GraphApi>,
    targetField: ui.fields.Reference<MetaNodeFactory>,
    node?: string | null,
): Promise<void> {
    if (node) {
        const [vendorName, packageName, nodeName] = String(node).split('/');
        const result = await pageInstance.$.graph
            .node('@sage/xtrem-metadata/MetaNodeFactory')
            .query(
                ui.queryUtils.edgesSelector(
                    {
                        _id: true,
                        name: true,
                        title: true,
                        naturalKey: true,
                        package: {
                            name: true,
                        },
                    },
                    {
                        filter: { name: nodeName, package: { name: `${vendorName}/${packageName}` } },
                    },
                ),
            )
            .execute();

        targetField.value = result.edges[0].node;
    }
}

export function convertNodeDetailsTypeToDynamicParameterType(
    parameterType: NodeDetails['type'],
    kind: NodeDetails['kind'],
): string {
    if (kind === 'ENUM') {
        return 'enum';
    }

    switch (parameterType) {
        case 'Boolean':
            return 'boolean';
        case 'String':
            return 'string';
        case 'Float':
            return 'float';
        case 'Date':
            return 'date';
        case 'Integer':
            return 'integer';
        case 'Decimal':
            // TODO: is this a typo?
            return 'Decimal';
        case 'IntReference':
            return 'reference';
        default:
            return 'string';
    }
}

export function getInputData(page: ui.Page): { inputVariables: WorkflowVariable[]; oldRootPaths: string[] } {
    const { inputVariables, oldRootPaths } = page.$.queryParameters;
    return {
        inputVariables: ((inputVariables as unknown as WorkflowVariable[]) ?? []).map((variable: WorkflowVariable) => ({
            ...variable,
            path: convertPathFromSerialization(variable.path),
        })),
        oldRootPaths: oldRootPaths as unknown as string[],
    };
}

export function validateInputVariablesInField(
    mode: 'input' | 'select',
    inputVariables: WorkflowVariable[],
    fieldValue: string,
): string | undefined {
    // When input mode is 'select', the fieldValue is a string that contains the variable name
    // When input mode is 'input', the fieldValue is a string that may contain variable names in the format {{variable1}} {{variable2}}
    const variableNamesToValidate = mode === 'input' ? fieldValue.match(/\{\{([_a-zA-Z0-9.]+)\}\}/g) : [fieldValue];
    if (variableNamesToValidate == null || variableNamesToValidate.length === 0) return undefined;

    // eslint-disable-next-line no-restricted-syntax
    for (const variable of variableNamesToValidate) {
        const variableName = mode === 'input' ? variable.substring(2, variable.length - 2) : variable;
        if (!inputVariables.find(v => v.path === variableName)) {
            if (inputVariables.length === 0) {
                return ui.localize(
                    '@sage/xtrem-workflow/workflow-variable-not-found',
                    'Variable does not exist: {{variableName}}.',
                    { variableName },
                );
            }
            const distances = inputVariables.map(v => levenshtein.get(v.path, variableName));
            // get the closest variable name
            const closestVariable = inputVariables[distances.indexOf(Math.min(...distances))];
            return ui.localize(
                '@sage/xtrem-workflow/workflow-variable-not-found-with-suggestion',
                'Variable does not exist: {{variableName}}. Did you mean: {{closestVariable}}?',
                { variableName, closestVariable: closestVariable.path },
            );
        }
    }

    return undefined;
}
