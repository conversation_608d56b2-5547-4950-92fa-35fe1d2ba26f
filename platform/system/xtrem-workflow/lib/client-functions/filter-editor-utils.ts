import { FilterType, FiltrableType, WorkflowVariable } from '@sage/xtrem-shared';
import * as ui from '@sage/xtrem-ui';
import { WorkflowCondition } from '../shared-functions/workflow-config-types';
import { convertVariablesToNodeBrowserTreeValue } from './node-browser-tree-utils';

function mapVariableToFilterParameter(workflowVariable: WorkflowVariable): ui.FilterParameter {
    return {
        name: workflowVariable.path,
        type: workflowVariable.type,
        label: workflowVariable.title,
    };
}

export function convertFilterEditorValueToWorkflowConditions(
    filterEditorValue: ui.fields.FilterEditor['value'],
): WorkflowCondition[] {
    return (filterEditorValue?.filters ?? []).map(filter => ({
        path: filter.id || '',
        operator: filter.filterType as FilterType<FiltrableType>,
        useParameter: !!filter.parameter,
        value: filter.filterValue,
    }));
}

export type FilterEditorData = Pick<
    ui.fields.FilterEditor,
    'filterParameters' | 'selectedProperties' | 'isDisabled' | 'value'
>;

export function convertParametersAndConditionsToFieldEditorProperties({
    parameters,
    conditions,
}: {
    parameters: WorkflowVariable[];
    conditions: WorkflowCondition[];
}): FilterEditorData {
    const filterParameters = parameters.map(mapVariableToFilterParameter);
    const selectedProperties = convertVariablesToNodeBrowserTreeValue(parameters);

    const filters = conditions
        .filter(condition => !!selectedProperties[condition.path])
        .map((condition, i) => {
            const property = selectedProperties[condition.path];
            return {
                _id: String(i),
                ...property,
                path: condition.path,
                filterType: condition.operator,
                parameter: condition.useParameter,
                filterValue: condition.value,
            };
        });

    return {
        filterParameters,
        selectedProperties,
        isDisabled: Object.keys(selectedProperties).length === 0,
        value: {
            parameters: filterParameters,
            filters,
        },
    };
}
