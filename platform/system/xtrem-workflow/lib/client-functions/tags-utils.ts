import { withoutEdges } from '@sage/xtrem-client';
import * as ui from '@sage/xtrem-ui';

/**
 * Resolve a list of ids into a list of tags
 *
 * @param page the page
 * @param naturalKeys the natural keys of the tags to fetch (something like ['#tag1', '#tag2'])
 */
export async function resolveSysTagsFromNaturalKeys(
    page: ui.Page,
    naturalKeys: string[] | undefined,
): Promise<
    {
        _id: string;
        name: string;
        description: string;
    }[]
> {
    if (naturalKeys == null || naturalKeys.length === 0) return [];

    const result = await page.$.graph
        .node('@sage/xtrem-system/SysTag')
        .query(
            ui.queryUtils.edgesSelector(
                {
                    _id: true,
                    name: true,
                    description: true,
                },
                {
                    filter: { _id: { _in: naturalKeys } },
                },
            ),
        )
        .execute();

    return withoutEdges(result);
}
