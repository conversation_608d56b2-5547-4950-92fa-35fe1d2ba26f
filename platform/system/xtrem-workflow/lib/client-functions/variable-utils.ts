import { asyncArray } from '@sage/xtrem-async-helper';
import { AsyncResponse, LogicError, WorkflowVariable, WorkflowVariableType } from '@sage/xtrem-shared';
import * as ui from '@sage/xtrem-ui';
import { decorators, fetchNodeDetails, fields, Page, QueryParameters } from '@sage/xtrem-ui';
import { SelectItem } from '@sage/xtrem-ui/build/lib/component/ui/select/select-component';
import { NodeDetails } from '@sage/xtrem-ui/build/lib/service/node-information-service';
import { orderBy, set, uniqBy } from 'lodash';
import { stripPackageName } from './node-browser-tree-utils';
import { validateInputVariablesInField } from './node-configuration-utils';
import { NodeDetailsFetcher } from './node-details-fetcher';

export interface NodeFactoryData {
    name: string;
    title: string;
    naturalKey?: string;
}

export const idTitle = '🆔';

const isNodeDetailsReference = (nodeDetails: NodeDetails): boolean =>
    nodeDetails.kind === 'OBJECT' && !!nodeDetails.node;

export const getNodeFactoryVariables = async ({
    nodeFactory,
    prefix,
    locale,
    withOldValues,
}: {
    nodeFactory: NodeFactoryData;
    prefix: string;
    locale?: string;
    withOldValues?: boolean;
}): Promise<WorkflowVariable[]> => {
    const idVariable: WorkflowVariable = {
        path: `${prefix}._id`,
        type: 'IntReference',
        node: nodeFactory.name,
        title: addIdIconToTitle(nodeFactory.title),
        isCustom: false,
    };
    const naturalKeyPropertyNames = [...(nodeFactory?.naturalKey ? JSON.parse(nodeFactory.naturalKey) : [])];
    const naturalKeyPropertyDetails = await fetchNodeDetails({
        nodeName: nodeFactory?.name ?? '',
        locale: locale ?? 'en-US',
    });
    const naturalKeyVariables = naturalKeyPropertyNames.map((propertyName: string) => {
        const propertyDetails = naturalKeyPropertyDetails[propertyName];
        if (!propertyDetails) throw new Error(`Property ${propertyName} not found in node ${nodeFactory.name}`);
        const isReference = isNodeDetailsReference(propertyDetails);
        const titlePostfix = isReference ? ` / ${idTitle}` : '';
        return {
            path: `${prefix}.${propertyName}${isReference ? '._id' : ''}`,
            type: isReference ? 'IntReference' : propertyDetails.type,
            node: propertyDetails.node,
            title: `${nodeFactory.title} / ${propertyDetails.label}${titlePostfix}`,
            isCustom: propertyDetails.isCustom,
        } as WorkflowVariable;
    });
    const oldNaturalKeyVariables = withOldValues ? naturalKeyVariables.map(getOldVariable) : [];
    return [idVariable, ...naturalKeyVariables, ...oldNaturalKeyVariables];
};

export const tidyVariables = (variables: WorkflowVariable[]): WorkflowVariable[] => {
    return uniqBy(
        // Sort by title, but put _id first
        orderBy(variables, variable => variable.title.replace(idTitle, '')),
        'path',
    );
};

/**
 * Remove the '/ 🆔' from a title
 * @param title
 * @returns
 */
export const stripIdIconFromTitle = (title: string): string => title.replace(new RegExp(` / ${idTitle}$`), '');

/**
 * Add '/ 🆔' to title
 * @param title
 * @returns
 */
export const addIdIconToTitle = (title: string): string => `${title} / ${idTitle}`;

/**
 * Returns the title of a variable from its path
 * @param variables the list of variables
 * @param path the path of the variable
 */
export const getVariableTitleFromPath = (variables: WorkflowVariable[], path: string): string => {
    const variable = findVariable(variables, path);
    return stripIdIconFromTitle(variable.title);
};

// STDEN REMOVE ASAP
export const getVariablesSummaryDecorator = (): decorators.StaticContentDecoratorProperties => ({
    isTransient: true,
    width: 'extra-large',
    title: 'Available variables',
    isMarkdown: true,
    // TODO: localize
    content: '_No variables selected_',
});

/**
 * Temporary variable to get around a bug: the value of a StaticContent field may not be correctly displayed
 * if it was set while the field was hidden.
 * TODO: fix the bug and eliminate this variable.
 * */
export const hideStaticContentFields = false;

export const getVariableTitle = async (
    nodeDetailsFetcher: NodeDetailsFetcher,
    entityName: string,
    path: string,
): Promise<string> => {
    let nodeName = stripPackageName(entityName);
    const names = [] as string[];
    let lookForCustomField = false;
    await asyncArray(path.split('.')).forEach(async propertyName => {
        if (propertyName === '_customData') {
            lookForCustomField = true;
            return;
        }
        if (!nodeName) throw new LogicError(`Node name missing for property ${propertyName}, full path was ${path}`);
        const properties = await nodeDetailsFetcher.fetch(nodeName);
        const fullPropertyName = lookForCustomField ? `_customData.${propertyName}` : propertyName;
        const property = properties[fullPropertyName];
        if (property == null) throw new LogicError(`Property ${propertyName} not found in node ${nodeName}`);
        if (property.isCustom !== lookForCustomField) {
            throw new LogicError(
                `Property ${propertyName} was resolved with wrong custom state (expected ${lookForCustomField})`,
            );
        }
        if (fullPropertyName === '_id') names.push(idTitle);
        else if (property.isCustom) names.push(`${property.label} (custom)`);
        else names.push(property.label);
        nodeName = property.node ?? '';
    });
    return names.join(' / ');
};

export function formatSelectedVariables(variables: WorkflowVariable[]): string {
    const map = {};
    variables.forEach(variable => set(map, variable.title.replace(/\//g, '.'), variable.title.split('/').pop()));
    const format = (obj: any, prefix: string): string => {
        let simpleValues = Object.values(obj)
            .filter(value => typeof value === 'string')
            .join(', ')
            .trim();
        let simpleText = '';
        if (simpleValues) {
            simpleValues = `**${simpleValues}**`;
            simpleText = prefix ? `${prefix} / ${simpleValues}` : simpleValues;
        }
        const objText = Object.entries(obj)
            // eslint-disable-next-line @typescript-eslint/no-unused-vars
            .filter(([_key, value]) => typeof value === 'object')
            .map(([key, value]) => format(value, prefix ? `${prefix} / ${key}` : key))
            .join('\n- ');
        return [simpleText, objText].filter(s => !!s).join('\n- ');
    };
    const all = format(map, '');
    return all ? `- ${all}` : '';
}

export function lookupVariable(variables: WorkflowVariable[], path: string): WorkflowVariable | null {
    return variables.find(v => v.path === path) ?? null;
}

export function findVariable(variables: WorkflowVariable[], path: string): WorkflowVariable {
    const variable = lookupVariable(variables, path);
    if (!variable) throw new Error(`Variable not found: ${path}`);
    return variable;
}

export function collectEmbeddedPaths(value: string): string[] {
    const matches = value.match(/{{[^{}]+}}/g);
    return matches?.map(match => match.slice(2, -2)) ?? [];
}

/**
 * Transform a list of variables that were read from the payload of an action into variables
 * that can be used in the page.
 * @param variables
 */
export function transformVariablesFromSerialization(variables: WorkflowVariable[]): WorkflowVariable[] {
    variables.forEach(variable => {
        variable.path = convertPathFromSerialization(variable.path);
    });
    return variables; // for chaining
}

/**
 * Only keep the properties of a WorkflowVariable that need to be serialized
 * @returns
 */
export function convertVariableForSerialization(variable: WorkflowVariable): WorkflowVariable {
    // Note: only returns the properties we want to serialize
    return {
        node: variable.node,
        title: variable.title,
        type: variable.type,
        enumType: variable.enumType,
        enumValues: variable.enumValues,
        path: convertPathForSerialization(variable.path),
        isCustom: variable.isCustom,
    };
}

/**
 * Convert a path for serialization.
 */
export function convertPathForSerialization(pathToSerialize: string): string {
    return pathToSerialize;
}

/**
 * Convert a path from its serialized form.
 */
export function convertPathFromSerialization(serializedPath: string): string {
    return serializedPath;
}

/**
 * Computes the list of variables that are used by the action.
 * This function will return a serializable form of the variables (to be used in the payload of an action)
 */
export function getStepVariablesForSerialization(
    currentVariables: WorkflowVariable[],
    usedPaths: string[],
): WorkflowVariable[] {
    return currentVariables.filter(variable => usedPaths.includes(variable.path)).map(convertVariableForSerialization);
}

export async function getOldRelativePropertyPaths(
    nodeDetailsFetcher: NodeDetailsFetcher,
    nodeName: string,
): Promise<string[]> {
    // Only the stored properties of the main record have old data
    return Object.values(await nodeDetailsFetcher.fetch(nodeName))
        .filter(property => property.isStored && property.name !== '_id')
        .map(property => (isNodeDetailsReference(property) ? `${property.name}._id` : property.name));
}

export function getOldVariable(variable: WorkflowVariable): WorkflowVariable {
    return {
        ...variable,
        path: `$old.${variable.path}`,
        // TODO: localize
        title: `${variable.title} (old)`,
    };
}

export function getOldVariables(
    variables: WorkflowVariable[],
    rootPrefix: string,
    oldRelativePropertyPaths: string[],
): WorkflowVariable[] {
    return variables
        .filter(variable =>
            oldRelativePropertyPaths.some(
                // _id does not change (at top level)
                propertyName => `${rootPrefix}${propertyName}` === variable.path,
            ),
        )
        .map(getOldVariable);
}

async function _openSelectVariablesDialog<PageT extends Page, VariableT extends WorkflowVariable>({
    page,
    fromVariables,
    selectedVariables,
    oldRootPaths,
    selectionMode = 'variables',
    options = {},
}: {
    page: PageT;
    fromVariables?: VariableT[];
    selectedVariables: VariableT[];
    oldRootPaths: string[];
    selectionMode?: 'variables' | 'propertiesToUpdate';
    options?: {
        /** Only show _ids and references */
        onlyShowIdsAndReferences?: true;
    };
}): Promise<{ selectedVariables: VariableT[]; selectedRoot: VariableT } | undefined> {
    const queryParameters: QueryParameters = {
        inputVariables: JSON.stringify(selectedVariables),
        oldRootPaths: JSON.stringify(oldRootPaths),
        selectionMode,
        options: JSON.stringify(options),
    };
    if (selectionMode === 'propertiesToUpdate') {
        queryParameters.fromVariables = JSON.stringify(fromVariables);
    }
    const result = (await page.$.dialog.page('@sage/xtrem-workflow/WorkflowSelectVariablesDialog', queryParameters)) as
        | { selectedVariables: VariableT[]; selectedRootVariable: VariableT }
        | undefined;

    if (result == null || result.selectedRootVariable == null) {
        // The user clicked on 'Ok' before selecting a root variable
        return undefined;
    }
    return {
        selectedVariables: result?.selectedVariables,
        selectedRoot: result.selectedRootVariable,
    };
}

/**
 * Maps a variable to a SelectItem (for dynamicSelects)
 */
export function mapVariableToSelectItem(variable: WorkflowVariable, mode: 'input' | 'select'): SelectItem {
    if (mode === 'select') {
        // In select mode, we only need the path, without any {{...}}
        // in select mode, we don't want to display the 'foo / 🆔' but simply 'foo'
        return {
            id: variable.path,
            value: variable.path,
            displayedAs: stripIdIconFromTitle(variable.title),
        };
    }
    return {
        id: variable.path,
        value: `{{${variable.path}}}`,
        displayedAs: variable.title,
    };
}

/**
 * Set the options for a dynamic select field from a list of variables
 */
export function setOptionsForDynamicSelect(dynamicSelect: fields.DynamicSelect, variables: WorkflowVariable[]): void {
    dynamicSelect.options = variables.map(variable => mapVariableToSelectItem(variable, dynamicSelect.mode || 'input'));
    if (dynamicSelect.mode === 'select' && dynamicSelect.value == null && variables.length === 1) {
        dynamicSelect.value = variables[0].path;
    }
}

/**
 * Returns the properties for a decorators.dynamicSelectField to select variables (in input mode)
 */
export function getDynamicSelectProperties<PageT extends Page, VariableT extends WorkflowVariable = WorkflowVariable>({
    mode = 'input',
    getVariables,
    selectableVariableFilter,
    getOldRootPaths,
    updateCurrentVariables,
    selectionMode = 'variables',
    options = {},
}: {
    /**
     * The mode of the dynamic field:
     * - 'input': the field is an input field
     * - 'select': the field is a select field
     */
    mode?: 'input' | 'select';
    /**
     * The function that returns the current variables.
     */
    getVariables: (page: PageT) => VariableT[];
    /**
     * The function that filters the variables that the user can select (from the selectBox)
     */
    selectableVariableFilter: (page: PageT, variable: VariableT) => boolean;
    /**
     * The function that returns the old root paths
     */
    getOldRootPaths: (page: PageT) => string[];
    /**
     * The function that updates the current variables
     */
    updateCurrentVariables: (page: PageT, variables: VariableT[]) => AsyncResponse<void>;
    selectionMode?: 'variables' | 'propertiesToUpdate';
    options?: {
        /** Only show _ids and references */
        onlyShowIdsAndReferences?: true;
    };
}): any {
    return {
        populateListTitle: selectionMode === 'variables' ? 'Add a variable' : 'Add a property',
        helperText: mode === 'input' ? "Click on the '+' icon to add a variable." : undefined,
        mode,
        async populateList() {
            // Happens when user clicks on the 'Add a variable' button
            const result = await _openSelectVariablesDialog({
                page: this,
                selectedVariables: getVariables(this),
                oldRootPaths: getOldRootPaths(this),
                selectionMode,
                options,
            });
            if (result == null) return [];
            await updateCurrentVariables(this, result.selectedVariables);
            // Only fill the select box with the variables that are selectable
            const selectableVariables = getVariables(this).filter(variable => selectableVariableFilter(this, variable));
            return selectableVariables.map((variable: WorkflowVariable) => mapVariableToSelectItem(variable, mode));
        },
        validation(value: string): string | undefined {
            return validateInputVariablesInField(
                mode,
                getVariables(this).filter(variable => selectableVariableFilter(this, variable)),
                value,
            );
        },
    };
}

/**
 * Returns the properties for a nestedFields.dynamicSelect to select variables (in input mode)
 */
export function getNestedDynamicSelectProperties<
    PageT extends Page,
    VariableT extends WorkflowVariable = WorkflowVariable,
>({
    mode = 'input',
    getVariables,
    selectableVariableFilter,
    getOldRootPaths,
    updateCurrentVariables,
}: {
    /**
     * The mode of the dynamic field:
     * - 'input': the field is an input field
     * - 'select': the field is a select field
     */
    mode?: 'input' | 'select';
    /**
     * The function that returns the current variables.
     */
    getVariables: (page: PageT) => VariableT[];
    /**
     * The function that filters the variables that the user can select (from the selectBox)
     */
    selectableVariableFilter: (page: PageT, variable: VariableT) => boolean;
    /**
     * The function that returns the old root paths
     */
    getOldRootPaths: (page: PageT) => string[];
    /**
     * The function that updates the current variables
     */
    updateCurrentVariables: (page: PageT, variables: VariableT[]) => void;
}): any {
    return {
        ...getDynamicSelectProperties({
            mode,
            getVariables,
            selectableVariableFilter,
            getOldRootPaths,
            updateCurrentVariables,
        }),
        options() {
            // The initial option, when the component is loaded
            return getVariables(this)
                .filter(variable => selectableVariableFilter(this, variable))
                .map((variable: WorkflowVariable) => mapVariableToSelectItem(variable, mode));
        },
    };
}

/**
 * Validate the controls. This function is intended to be used in the onLoad event of a page.
 * It will validate all the controls in the page, including the dynamic selects and the pod collections.
 */
export async function validateDynamicVariablesPickers(
    controls: (fields.PodCollection | fields.DynamicSelect | fields.DynamicPod)[],
): Promise<void> {
    await asyncArray(controls).forEach(async control => {
        if (control instanceof fields.DynamicSelect) {
            setTimeout(async () => {
                // Hack for React
                if (control.value && control.value.length > 0) await control.validate();
            }, 0);
        } else if (control instanceof fields.PodCollection) {
            // Force the validation of the pods to make sure any variable they could use is still valid
            await control.validate(true);
        } else {
            await control.validate();
        }
    });
}

/**
 * Returns true if the target object is assignable from the source object
 * @param target
 * @param source
 * @returns
 */
export function variableIsAssignableFrom(
    target: {
        type: WorkflowVariableType;
        enumType?: string;
        node?: string;
    },
    source: WorkflowVariable,
): boolean {
    const targetType = target.type;
    let sourceType = source.type;
    if (sourceType === 'IntOrString' && source.path.endsWith('._id')) {
        sourceType = 'IntReference';
    }
    if (targetType === sourceType) {
        if (targetType === 'IntReference') {
            return target.node === source.node;
        }
        if (targetType === 'Enum') {
            return target.enumType === source.enumType;
        }
        return true;
    }
    if (targetType === 'IntOrString' && (sourceType === 'Int' || sourceType === 'String')) return true;
    if (targetType === 'Int' && sourceType === 'IntOrString') return true;
    if (targetType === 'String' && sourceType === 'IntOrString') return true;
    if (targetType === 'Decimal' && sourceType === 'Int') return true;
    if (targetType === 'Decimal' && sourceType === 'IntOrString') return true;
    if (targetType === 'Int' && sourceType === 'Decimal') return true;
    if (targetType === 'IntOrString' && sourceType === 'Decimal') return true;
    return false;
}

/**
 * Opens a dialog to select variables.
 * This function is intended to be used in a page to allow the user to select variables.
 */
export async function handleVariableSelection<PageT extends ui.Page>({
    page,
    getFromVariables,
    getSelectedVariables,
    setSelectedVariables,
    getRestrictedVariables,
    getOldRootPaths,
    selectionMode = 'variables',
    options = {},
}: {
    page: PageT;
    getFromVariables?: (page: PageT) => WorkflowVariable[];
    getSelectedVariables: (page: PageT) => WorkflowVariable[];
    getRestrictedVariables?: (page: PageT) => WorkflowVariable[];
    setSelectedVariables: (
        page: PageT,
        variables: WorkflowVariable[],
        rootVariable: WorkflowVariable,
    ) => AsyncResponse<void>;
    getOldRootPaths: (page: PageT) => string[];
    selectionMode?: 'variables' | 'propertiesToUpdate';
    options?: {
        /** Only show _ids and references */
        onlyShowIdsAndReferences?: true;
    };
}): Promise<void> {
    const queryParameters: ui.QueryParameters = {
        inputVariables: JSON.stringify(getSelectedVariables(page)),
        oldRootPaths: JSON.stringify(getOldRootPaths(page)),
        selectionMode,
        options: JSON.stringify(options),
    };

    if (getRestrictedVariables != null) {
        queryParameters.restrictedVariables = JSON.stringify(getRestrictedVariables(page));
    }

    if (selectionMode === 'propertiesToUpdate') {
        if (getFromVariables == null) {
            throw new Error("'getFromVariables' must be provided when selectionType is 'propertiesToUpdate'");
        }
        queryParameters.fromVariables = JSON.stringify(getFromVariables(page));
    }

    const result = (await page.$.dialog.page('@sage/xtrem-workflow/WorkflowSelectVariablesDialog', queryParameters)) as
        | { selectedVariables: WorkflowVariable[]; selectedRootVariable: WorkflowVariable }
        | undefined;

    if (result?.selectedRootVariable == null) {
        // The user clicked on 'Ok' before selecting a root variable
        return;
    }

    if (result?.selectedVariables) {
        // Update the output variables (exclude the variables that are already in the input)
        await setSelectedVariables(page, result.selectedVariables, result.selectedRootVariable);
    }
}
