import { AsyncResponse, WorkflowVariable } from '@sage/xtrem-shared';
import * as ui from '@sage/xtrem-ui';
import { handleVariableSelection } from './variable-utils';

/**
 * Open the dialog to select variables
 * @param getFromVariables Function to get the variables to select from. Only used when selectionType is 'propertiesToUpdate'
 * @param getSelectedVariables Function to get the selected variables
 * @param setSelectedVariables Function to set the selected variables
 * @param getRestrictedVariables Function to get the allowed variables (all the variables are allowed if not provided)
 * @param getOldRootPaths Function to get the old root paths
 * @param selectionMode Selection mode. Can be 'variables' or 'propertiesToUpdate'. Default is 'variables'
 * @param options Options for the dialog. Can be used to only show _ids and references
 * @param options.onlyShowIdsAndReferences Only show _ids and references. Default is false
 * @returns
 */
export function getSelectVariablesButtonDecorator<PageT extends ui.Page>({
    getFromVariables,
    getSelectedVariables,
    setSelectedVariables,
    getRestrictedVariables,
    getOldRootPaths,
    selectionMode = 'variables',
    options = {},
}: {
    getFromVariables?: (page: PageT) => WorkflowVariable[];
    getSelectedVariables: (page: PageT) => WorkflowVariable[];
    getRestrictedVariables?: (page: PageT) => WorkflowVariable[];
    setSelectedVariables: (
        page: PageT,
        variables: WorkflowVariable[],
        rootVariable: WorkflowVariable,
    ) => AsyncResponse<void>;
    getOldRootPaths: (page: PageT) => string[];
    selectionMode?: 'variables' | 'propertiesToUpdate';
    options?: {
        /** Only show _ids and references */
        onlyShowIdsAndReferences?: true;
    };
}): ui.decorators.ButtonDecoratorProperties {
    return {
        width: 'small',
        map() {
            return ui.localize('@sage/xtrem-workflow/add-variables', 'Add variables');
        },
        async onClick(this: PageT) {
            await handleVariableSelection<PageT>({
                page: this,
                getFromVariables,
                getSelectedVariables,
                setSelectedVariables,
                getRestrictedVariables,
                getOldRootPaths,
                selectionMode,
                options,
            });
        },
    } as ui.decorators.ButtonDecoratorProperties;
}
