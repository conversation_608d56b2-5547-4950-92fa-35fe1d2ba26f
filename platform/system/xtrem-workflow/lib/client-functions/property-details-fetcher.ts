import { Dict } from '@sage/xtrem-shared';
import * as ui from '@sage/xtrem-ui';
import { merge } from 'lodash';

export interface PropertyDataTypeDetails {
    node: string;
    value: { bind: string };
    valueField: string;
    columns: any[];
    type: string;
    enumType?: string;
}

export type PropertyDetails = {
    name: string;
    /**
     * Is the property a custom field ?
     */
    isCustom: boolean;
    dataTypeDetails?: PropertyDataTypeDetails;
};

export type PropertyDetailsFetcherReturnType = {
    properties: PropertyDetails[];
    dataType?: PropertyDataTypeDetails;
};

export class PropertyDetailsFetcher {
    private _cache: Dict<PropertyDetailsFetcherReturnType> = {};

    /**
     * Fetches the property details for a given node.
     * @param nodeName The name of the node (without @sage/xtrem-...)
     */
    async fetch(page: ui.Page, nodeName: string): Promise<PropertyDetailsFetcherReturnType> {
        const key = nodeName;
        const cached = this._cache[key];
        if (cached) return cached;

        const result = await _fetchPropertiesDetails(page, nodeName);
        this._cache[key] = result;
        return result;
    }
}

async function _fetchPropertiesDetails(page: ui.Page, nodeName: string): Promise<PropertyDetailsFetcherReturnType> {
    if (!nodeName) return { properties: [] };
    const result = (await page.$.graph.raw(
        `{
            getNodeDetails(nodeName: "${nodeName}") {
                defaultDataTypeDetails {
                    node
                    type
                    title
                    value {
                        bind
                        title
                        type
                        enumType
                    }
                    helperText {
                        bind
                        title
                        type
                        enumType
                    }
                    columns {
                        bind
                        title
                        type
                        enumType
                    }
                }

                properties {
                    name
                    enumType
                    type
                    isCustom
                    dataTypeDetails {
                        node
                        type
                        title
                        value {
                            bind
                            title
                            type
                            enumType
                        },
                        helperText {
                            bind
                            title
                            type
                            enumType
                        },
                        isDefault
                        columns {
                            bind
                            title
                            type
                            enumType
                        },
                        precision
                        scale
                        roundingMode
                        maxLength
                        isLocalized
                        doNotTrim
                        truncate
                        values {
                            value
                            title
                        }
                        allowedContentTypes
                    },
                }
            }
            getNodeCustomDetails(nodeName: "${nodeName}") {
                properties {
                    name
                    enumType
                    type
                    isCustom
                    dataTypeDetails {
                        node
                        type
                        title
                        value {
                            bind
                            title
                            type
                            enumType
                        },
                        helperText {
                            bind
                            title
                            type
                            enumType
                        },
                        isDefault
                        columns {
                            bind
                            title
                            type
                            enumType
                        },
                        precision
                        scale
                        roundingMode
                        maxLength
                        isLocalized
                        doNotTrim
                        truncate
                        values {
                            value
                            title
                        }
                        allowedContentTypes
                    },
                }
            }
        }`,
        false,
        true,
    )) as {
        getNodeDetails: { defaultDataTypeDetails: PropertyDataTypeDetails; properties: PropertyDetails[] };
        getNodeCustomDetails: { properties: PropertyDetails[] };
    };
    return {
        properties: merge(
            result.getNodeDetails.properties,
            result.getNodeCustomDetails.properties.map(property => ({
                ...property,
                name: `_customData.${property.name}`,
            })),
        ),
        dataType: result.getNodeDetails.defaultDataTypeDetails,
    };
}
