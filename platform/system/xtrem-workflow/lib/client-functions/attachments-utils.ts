import { WorkflowVariable } from '@sage/xtrem-shared';
import { NodeDetailsFetcher } from './node-details-fetcher';

/**
 * Returns whether a node has attachments or not.
 *
 * @param nodeDetailsFetcher the node details fetcher
 * @param nodeName the short node name (without @sage/xtrem-...)
 * @returns
 */
export async function nodeHasAttachments(nodeDetailsFetcher: NodeDetailsFetcher, nodeName: string): Promise<boolean> {
    const nodeDetails = await nodeDetailsFetcher.fetch(nodeName, { getCollections: true });
    return nodeDetails._attachments != null;
}

/**
 * Returns true if the variable refers to a node that has attachments.
 */
export function variableIsReferenceToNodeWithAttachments(
    variable: WorkflowVariable,
    nodeDetailsFetcher: NodeDetailsFetcher,
): Promise<boolean> {
    if (variable.type !== 'IntReference' && variable.type !== 'IntOrString' && variable.type !== 'ExternalReference') {
        return Promise.resolve(false);
    }
    if (variable.node == null) return Promise.resolve(false);

    return nodeHasAttachments(nodeDetailsFetcher, variable.node);
}

/**
 * Returns true if the variable refers to the UploadedFile node.
 */
export function variableIsReferenceToUploadedFile(variable: WorkflowVariable): boolean {
    if (variable.type !== 'IntReference' && variable.type !== 'IntOrString' && variable.type !== 'ExternalReference') {
        return false;
    }
    return variable.node === 'UploadedFile';
}
