import * as ui from '@sage/xtrem-ui';
import { WorkflowParameter } from '../shared-functions/workflow-config-types';
import { collectEmbeddedPaths, convertPathForSerialization, convertPathFromSerialization } from './variable-utils';

/**
 * Converts values of a DynamicPod into workflow parameters (that will be stored in the payload of an action)
 * @param podValue the value of the DynamicPod
 */
export function convertDynamicPodValueToWorkflowParameters(
    podValue: ui.fields.DynamicPod['value'],
): WorkflowParameter[] {
    if (!podValue) return [];
    const variableNames = Object.keys(podValue).filter(key => !key.endsWith('/isVariable'));
    return variableNames.map(name => {
        const isVariable = podValue[`${name}/isVariable`];
        const value = podValue[name];
        return {
            name,
            isVariable,
            value: typeof value === 'string' ? convertPathForSerialization(value) : value,
        };
    });
}

/**
 * Converts workflow parameters (stored in the payload of an action) into values for a DynamicPod
 * @param parameters the workflow parameters to convert
 */
export function convertWorkflowParametersToDynamicPodValue(
    parameters: WorkflowParameter[],
): ui.fields.DynamicPod['value'] {
    const value = {} as ui.fields.DynamicPod['value'];
    parameters.forEach(parameter => {
        value[`${parameter.name}/isVariable`] = parameter.isVariable;
        value[parameter.name] = parameter.value;
        if (typeof value[parameter.name] === 'string') {
            value[parameter.name] = convertPathFromSerialization(value[parameter.name]);
        }
    });
    return value;
}

/**
 * Returns the variable paths used in a dynamic pod.
 * @param dynamicPod the dynamic pod
 * @param variableNames the names of the variables to look for
 * @param options optional options
 * @param options.isVariableChecker a function to check if a variable is a variable (default checks for '/isVariable' suffix)
 */
export function getUsedVariablePathsFromDynamicPod(
    dynamicPod: ui.fields.DynamicPod,
    variableNames: string[],
    options: {
        isVariableChecker?: (variableName: string, dynamicPodValues: any) => boolean;
    } = {},
): string[] {
    const isVariableCheckerToUse =
        options.isVariableChecker ??
        ((variableName: string, dynamicPodValues: any): boolean => {
            return !!dynamicPodValues[`${variableName}/isVariable`];
        });
    const paths: string[] = [];
    const values = dynamicPod.value;
    variableNames.forEach(variableName => {
        // Check if the pod contains a value for this variable-to-update
        const value = values[variableName];
        if (value == null) return;
        if (isVariableCheckerToUse(variableName, values)) {
            // Value is the selected variable
            paths.push(value as string);
        } else if (typeof value === 'string') {
            // Value is a string that may contain {{path1}}, {{path2}}, etc.
            paths.push(...collectEmbeddedPaths(value as string));
        }
    });
    return paths;
}
