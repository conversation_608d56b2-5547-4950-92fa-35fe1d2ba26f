import * as ui from '@sage/xtrem-ui';

import { asyncArray } from '@sage/xtrem-async-helper';
import { withoutEdges } from '@sage/xtrem-client';
import { LocalizeLocale, LogicError, WorkflowVariable } from '@sage/xtrem-shared';
import {
    addParametersToDynamicPod,
    DynamicParameter,
} from '@sage/xtrem-system/build/lib/client-functions/dynamic-pod-helper';
import { sortBy } from 'lodash';
import { UpdateRule } from '../shared-functions/update-entity-action-config';
import { convertNodeDetailsTypeToDynamicParameterType } from './node-configuration-utils';
import { PropertyDataTypeDetails, PropertyDetails } from './property-details-fetcher';
import { variableIsAssignableFrom } from './variable-utils';

export interface UpdatableVariable extends WorkflowVariable {
    /**
     * The full target node name. Something like '@sage/xtrem-master-data/Currency'
     * Is only set the variable is an IntReference
     */
    nodeFullName?: string;
}

/**
 * Convert the serialized rules into values for a dynamic pod.
 */
export async function convertUpdateRulesToPodValue(
    page: ui.Page,
    pathPrefix: string,
    updateRules: UpdateRule[],
    variables: UpdatableVariable[],
    propertyDetails: PropertyDetails[],
): Promise<ui.fields.DynamicPod['value']> {
    const value = {} as ui.fields.DynamicPod['value'];
    await asyncArray(updateRules).forEach(async rule => {
        const id = rule.destinationPath.replace('_customData.', '_customData_');

        const variable = variables.find(v => v.path === `${pathPrefix}${rule.destinationPath}`);
        if (variable == null) {
            console.error(`convertUpdateRulesToPodValue: variable not found: ${rule.destinationPath}`);
            return;
        }

        value[`${id}/isVariable`] = rule.isSourceVariable;

        if (rule.sourceValue == null) {
            console.error(`convertUpdateRulesToPodValue: rule source value not set: ${variable.path}`);
            return;
        }

        if (rule.isSourceVariable) {
            // The source value is a variable, so we just store the path
            value[id] = rule.sourceValue;
            return;
        }

        if (variable.type === 'IntReference') {
            // We have to resolve the reference to the actual value (rule.sourceValue is the _id of the instance)
            rule.sourceValue = await resolveVariableValue(page, rule, variable, propertyDetails);
        }
        value[id] = rule.sourceValue;
    });
    return value;
}

export async function resolveVariableValue(
    page: ui.Page,
    updateRule: UpdateRule,
    variable: UpdatableVariable,
    propertyDetails: PropertyDetails[],
): Promise<any> {
    const varDetails = propertyDetails.find(prop => prop.name === updateRule.destinationPath);
    if (varDetails == null) {
        console.error(`convertUpdateRulesToPodValue: variable details not found: ${variable.path}`);
        return updateRule.sourceValue;
    }
    if (varDetails.dataTypeDetails == null) {
        console.error(`convertUpdateRulesToPodValue: variable dataTypeDetails not found: ${variable.path}`);
        return updateRule.sourceValue;
    }

    if (variable.nodeFullName == null) {
        console.error(`convertUpdateRulesToPodValue: variable nodeFullName not set: ${variable.path}`);
        return updateRule.sourceValue;
    }

    const columnsToFetch = varDetails.dataTypeDetails.columns.reduce(
        (total, col) => {
            if (col.type === 'binaryStream' || col.type === 'textStream') return total;
            total[col.bind] = true;
            return total;
        },
        { _id: true },
    );

    const result = await page.$.graph
        .node(variable.nodeFullName)
        .query(
            ui.queryUtils.edgesSelector(columnsToFetch, {
                filter: { _id: updateRule.sourceValue },
            }),
        )
        .execute();

    return withoutEdges(result)[0];
}

export function convertVariableTypeToDynamicParameterType(variable: WorkflowVariable): string {
    return convertNodeDetailsTypeToDynamicParameterType(variable.type, variable.type === 'Enum' ? 'ENUM' : 'SCALAR');
}

export function getPropertyAttributesForDynamicPod(
    variable: WorkflowVariable,
    propertyDetails: PropertyDetails[],
): PropertyDataTypeDetails | { options: string[] } | {} {
    let propertyName = variable.path.split('.').pop();
    if (propertyName == null) throw new LogicError(`Property name is missing: ${variable.path}`);
    if (variable.isCustom) propertyName = `_customData.${propertyName}`;
    const property = propertyDetails.find(prop => prop.name === propertyName);
    if (!property) throw new LogicError(`Property details are missing: ${variable.path}`);
    if (variable.type === 'Enum') return { options: variable.enumValues };
    return property.dataTypeDetails ?? {};
}

export function fillUpdateRulesPodFromVariables({
    dynamicPod,
    variablesToUpdate,
    locale,
    propertyDetails,
    getPropertiesForDynamicSelects,
    getPropertiesForDynamicInputs,
}: {
    dynamicPod: ui.fields.DynamicPodControlObject;
    variablesToUpdate: WorkflowVariable[];
    locale: LocalizeLocale;
    propertyDetails: PropertyDetails[];
    getPropertiesForDynamicSelects: (
        selectableVariableFilter: (_page: ui.Page, variable: WorkflowVariable) => boolean,
    ) => any;
    getPropertiesForDynamicInputs: (
        selectableVariableFilter: (_page: ui.Page, variable: WorkflowVariable) => boolean,
    ) => any;
}): void {
    const parameters = [] as DynamicParameter[];

    if (dynamicPod.value == null) throw new Error('Dynamic pod value is not set');

    // We only want to display the last part of the title (the title of all the variable will start with the same prefix)
    const getVariableTitle = (variable: WorkflowVariable): string => {
        return variable.title.split(' / ').pop() ?? variable.title;
    };

    sortBy(variablesToUpdate, v => v.title).forEach(variableToUpdate => {
        const id = variableToUpdate.path.replace('_customData.', '_customData_');
        const checkboxFieldId = `${id}/isVariable`;

        // This field is only used when the 'Enter manually' checkbox is checked
        const fieldWhenManual = (): DynamicParameter => {
            if (variableToUpdate.type === 'String') {
                // Use a dynamicSelect in input mode for string variables
                return {
                    _id: id,
                    name: id,
                    title: getVariableTitle(variableToUpdate),
                    type: 'dynamicSelect',
                    dataType: {
                        name: id,
                        attributes: getPropertiesForDynamicInputs((_page: ui.Page, variable: WorkflowVariable) =>
                            matchesType(variable),
                        ),
                    },
                };
            }
            // Other types: use a dedicated editor per type
            return {
                _id: id,
                name: id,
                title: getVariableTitle(variableToUpdate),
                type: convertVariableTypeToDynamicParameterType(variableToUpdate),
                dataType: {
                    attributes: getPropertyAttributesForDynamicPod(variableToUpdate, propertyDetails),
                } as any,
            };
        };

        const matchesType = (variableInList: WorkflowVariable): boolean => {
            return variableIsAssignableFrom(
                {
                    type: variableToUpdate.type,
                    node: variableToUpdate.node,
                    enumType: variableToUpdate.enumType,
                },
                variableInList,
            );
        };

        // This field is only used when the 'Enter manually' checkbox is unchecked
        const variableSelection = (): DynamicParameter => ({
            _id: id,
            name: id,
            title: getVariableTitle(variableToUpdate),
            type: 'dynamicSelect',
            dataType: {
                name: id,
                attributes: getPropertiesForDynamicSelects((_page: ui.Page, variable: WorkflowVariable) =>
                    matchesType(variable),
                ),
            },
        });
        const checkboxField = {
            _id: checkboxFieldId,
            type: 'boolean',
            name: checkboxFieldId,
            title: 'Enter manually',
            dataType: {
                name: checkboxFieldId,
                attributes: {
                    width: 'small',
                    isValueReversed: true,
                },
            },
        };
        parameters.push(dynamicPod.value[checkboxFieldId] ? variableSelection() : fieldWhenManual(), checkboxField);
    });

    addParametersToDynamicPod({
        dynamicPod,
        locale,
        parameters,
        isFullWidth: false,
    });
}
