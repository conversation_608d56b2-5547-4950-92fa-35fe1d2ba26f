import { AsyncResponse, WorkflowVariable } from '@sage/xtrem-shared';
import * as ui from '@sage/xtrem-ui';
import { handleVariableSelection } from './variable-utils';

export function getSelectVariablesFilterDecorator<PageT extends ui.Page>({
    getSelectedVariables,
    setSelectedVariables,
    getOldRootPaths,
}: {
    getSelectedVariables: (page: PageT) => WorkflowVariable[];
    setSelectedVariables: (
        page: PageT,
        variables: WorkflowVariable[],
        rootVariable: WorkflowVariable,
    ) => AsyncResponse<void>;
    getOldRootPaths: (page: PageT) => string[];
}): ui.decorators.FilterEditorDecoratorProperties {
    return {
        filterValueActionLabel: ui.localize('@sage/xtrem-workflow/add-variables', 'Add variables'),
        async filterValueAction(this: PageT) {
            await handleVariableSelection<PageT>({
                page: this,
                getSelectedVariables,
                setSelectedVariables,
                getOldRootPaths,
            });
        },
        propertyActionLabel: ui.localize('@sage/xtrem-workflow/add-variables', 'Add variables'),
        async propertyAction(this: PageT) {
            await handleVariableSelection<PageT>({
                page: this,
                getSelectedVariables,
                getOldRootPaths,
                setSelectedVariables,
            });
        },
    } as ui.decorators.FilterEditorDecoratorProperties;
}
