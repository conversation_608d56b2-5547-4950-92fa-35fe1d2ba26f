import { WorkflowVariable } from '@sage/xtrem-shared';

/**
 * Configuration for a workflow step.
 *
 * The configuration is created/updated in the workflow designer and saved in the nodes of the Flow diagram
 * (the flowNode.data property).
 *
 * This configuration interface is extended by workflow step subclasses to define their own configuration.
 */
export interface WorkflowStepConfig {
    /**
     * The (optional) output variable name
     */
    outputVariableName?: string;

    stepVariables?: WorkflowVariable[];

    [key: string]: unknown;
}
