import { <PERSON><PERSON> } from '@sage/xtrem-shared';
import { WorkflowMutationArgumentOrigin } from '../enums/workflow-mutation-argument-origin';
import { WorkflowParameter } from './workflow-config-types';
import { WorkflowGraphqlActionConfig } from './workflow-graphql-action-config';

/**
 * An argument of the mutation
 */
export interface WorkflowMutationArgument {
    name: string;
    /**
     * The origin of the argument
     * - fromParameter: the value for the argument will be fetched from a parameter of the action
     * - fromVariable: the argument is a variable of the workflow
     * - manual: the argument is a manual argument
     */
    origin: WorkflowMutationArgumentOrigin;
    isMandatory: boolean;
    value: Json;
    type: string;
    /**
     * When type is 'reference', the name of the referenced node
     */
    node?: string;
    /**
     * When type is 'enum', the name of the referenced enum
     */
    enumType?: string;
}

export interface WorkflowMutationActionConfig extends WorkflowGraphqlActionConfig {
    /**
     * The natural key of the mutation instance (factory+name+action)
     */
    mutationNaturalKey: string;

    /**
     * The parameters of the action
     * Callers of this action will have to provide values for all the parameters definition (at least for all the mandatory ones)
     */
    actionParameters: WorkflowParameter[];

    /**
     * The parameters of the mutation.
     */
    mutationArguments: WorkflowMutationArgument[];

    /**
     * The selector
     */
    selector: string;
}
