import { IconType, IdType, LocalizedText, NotificationLevel } from '@sage/xtrem-shared';
import { WorkflowActionConfig } from './workflow-action-configs';

export interface SendUserNotificationActionRecipient {
    /**
     * A path to a User instance if isManuallySet is false or a user {_id, email} if isManuallySet is true
     */
    user: string | { _id: IdType; email: string };
    isManuallySet: boolean;
}

export interface SendUserNotificationActionAction {
    style: 'primary' | 'secondary' | 'tertiary' | 'link';

    /**
     * The localized titles (indexed by locale).
     */
    localizedTitle: LocalizedText;

    link: string;
}

/** Configuration for the SendUserNotificationAction. */
export interface SendUserNotificationActionConfig extends WorkflowActionConfig {
    /**
     * The localized titles (indexed by locale) of the notification.
     */
    localizedNotificationTitle: LocalizedText;
    /**
     * The localized descriptions (indexed by locale) of the notification.
     */
    localizedNotificationDescription: LocalizedText;

    level: NotificationLevel;

    icon: IconType;

    shouldDisplayToast?: boolean;

    recipients: SendUserNotificationActionRecipient[];

    actions: SendUserNotificationActionAction[];
}
