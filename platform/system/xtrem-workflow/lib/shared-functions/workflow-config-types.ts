import { FilterType, FiltrableType, Json } from '@sage/xtrem-shared';

/** A path is a string in dot notation that represents a property path in an object. */
export type Path = string;

export interface WorkflowCondition {
    path: Path;
    operator: FilterType<FiltrableType>;
    useParameter: boolean;
    value: Json;
}

export interface WorkflowParameter {
    name: string;
    isVariable: boolean;
    value: Json;
}
