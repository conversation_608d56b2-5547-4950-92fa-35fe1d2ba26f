import { WorkflowActionConfig } from './workflow-action-configs';

export type CalculateOperation = 'add' | 'subtract' | 'multiply' | 'divide';

export interface WorkflowCalculation {
    operation?: CalculateOperation;
    isVariable: boolean;
    variable?: string;
    value?: number;
}

export interface CalculateActionConfig extends WorkflowActionConfig {
    calculationSteps: WorkflowCalculation[];
}
