import { WorkflowVariable } from '@sage/xtrem-shared';
import { WorkflowCondition } from './workflow-config-types';
import { WorkflowStepConfig } from './workflow-step-config';

export interface WorkflowEventConfig extends WorkflowStepConfig {}

export interface NotificationEventConfig extends WorkflowEventConfig {
    topic: string;
}

export interface EntityCreatedEventConfig extends WorkflowEventConfig {
    /** The entity name, formatted as @sage/packageName/nodeName  */
    entityName: string;

    stepVariables: WorkflowVariable[];

    conditions: WorkflowCondition[];
}

export interface EntityUpdatedEventConfig extends WorkflowEventConfig {
    /** The entity name, formatted as @sage/packageName/nodeName  */
    entityName: string;

    stepVariables: WorkflowVariable[];

    conditions: WorkflowCondition[];
}
