import { WorkflowStepConfig } from './workflow-step-config';

/** Abstract base class for workflow action configurations. */
export interface WorkflowActionConfig extends WorkflowStepConfig {
    /**
     * The (optional) output variable name.
     * Override of WorkflowStepConfig to make it non nullable
     */
    outputVariableName: string;
}

export interface GraphqlActionConfig extends WorkflowActionConfig {}

export interface DeleteEntityActionConfig extends WorkflowActionConfig {}

export interface QueryDataActionConfig extends WorkflowActionConfig {}

export interface WaitActionConfig extends WorkflowActionConfig {
    quantity: number;
    unit: 's';
}

export interface TestStubActionConfig extends WorkflowActionConfig {}
