import * as xtremAuthorization from '@sage/xtrem-authorization';
import { Collection, Node, Reference, decorators, useDefaultValue } from '@sage/xtrem-core';
import { BusinessRuleError, ValidationSeverity } from '@sage/xtrem-shared';
import * as xtremSystem from '@sage/xtrem-system';
import { Flow, FlowWrapper, WorkflowEngine } from '../engine';
import { logger } from '../engine/logger';
import * as xtremWorkflow from '../index';

@decorators.node<WorkflowDefinition>({
    storage: 'sql',
    canCreate: true,
    canRead: true,
    canUpdate: true,
    canDelete: true,
    canSearch: true,
    isPublished: true,
    canDuplicate: true,

    serviceOptions: () => [xtremWorkflow.serviceOptions.workflow],
    indexes: [
        {
            orderBy: { id: 1 },
            isUnique: true,
            isNaturalKey: true,
        },
        {
            orderBy: { startTopic: 1, id: 1 },
        },
    ],
    notifies: ['created', 'updated', 'deleted'],
    async deleteBegin() {
        await this.$.context.bulkDeleteSql(xtremWorkflow.nodes.WorkflowProcess, { where: { definition: this._id } });
    },
    async prepare() {
        const startNode = FlowWrapper.getStartNode(await this.flow);
        const currentStartTopic = await this.startTopic;
        const expectedStartTopic = (startNode.data as unknown as { topic: string })?.topic;
        if (currentStartTopic !== expectedStartTopic) {
            // The topic was changed (the node bound to the trigger was probably changed)
            await logger.verboseAsync(
                async () =>
                    `Workflow ${await this.id} start topic changed from ${currentStartTopic} to ${expectedStartTopic}`,
            );
            await this.$.set({ startTopic: expectedStartTopic });
        }
    },

    async controlBegin() {
        const flow = await this.flow;
        if (flow == null) {
            throw new BusinessRuleError(
                this.$.context.localize(
                    '@sage/xtrem-workflow/workflow_error_empty_workflow',
                    'You need to add a trigger.',
                ),
            );
        }
    },

    async saveBegin() {
        if ((await this.status) === 'production' && (await this.testUser) != null) {
            // reset the test user in production mode
            await this.$.set({ testUser: null });
        }
        if (this.$.status === 'added') {
            // New node, we have to compute its id from its name
            await this.$.set({ id: await this._computeUniqueIdFromName() });
        }
    },
    async saveEnd() {
        await logger.verboseAsync(
            async () => `saved workflow ${await this.id}, flow=${JSON.stringify(await this.flow, null, 4)}`,
        );
    },
    async getFilters(context) {
        // Note: we use the loginUser vs the user of the context to deal with demo personas on demo tenants
        const loginUser = await context.loginUser;

        if (loginUser?.isAdministrator) {
            // Administrators can see all workflows
            return [];
        }

        // Show only the workflows created by the user
        return [
            {
                testUser: `#${loginUser?.email}`,
            },
        ];
    },
})
export class WorkflowDefinition extends Node {
    @decorators.stringProperty<WorkflowDefinition, 'id'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.name,
        lookupAccess: true,
    })
    readonly id: Promise<string>;

    @decorators.stringProperty<WorkflowDefinition, 'name'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.localizedName,
        lookupAccess: true,
    })
    readonly name: Promise<string>;

    @decorators.booleanProperty<WorkflowDefinition, 'isActive'>({
        isStored: true,
        isPublished: true,
        defaultValue: true,
        provides: ['isActive'],
    })
    readonly isActive: Promise<boolean>;

    @decorators.enumProperty<WorkflowDefinition, 'status'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremWorkflow.enums.WorkflowDefinitionStatusDataType,
        async isFrozen() {
            return !(await this.$.context.getUserInfo()).isAdministrator;
        },
        defaultValue() {
            return 'test';
        },
    })
    readonly status: Promise<xtremWorkflow.enums.WorkflowDefinitionStatus>;

    /**
     * In test mode the workflow will only be triggered by events generated by the testUser.
     * This allows the test user to test the workflow without affecting other users.
     * This is initialized with the user that created the workflow.
     * Only admnistrators can change it.
     */
    @decorators.referenceProperty<WorkflowDefinition, 'testUser'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        node: () => xtremSystem.nodes.User,
        filters: {
            lookup: xtremAuthorization.filters.user.interactiveUsers,
        },
        async control(cx) {
            if ((await this.status) === 'test') {
                if ((await this.testUser) == null) {
                    cx.addDiagnose(
                        ValidationSeverity.error,
                        cx.localize(
                            '@sage/xtrem-workflow/error-no-test-user-in-test-mode',
                            'When the status is Test, you need to provide a test user.',
                        ),
                    );
                }
            }
        },
        async isFrozen() {
            return !(await this.$.context.getUserInfo()).isAdministrator;
        },
        async defaultValue() {
            return `#${(await this.$.context.getUserInfo()).email}`;
        },
    })
    readonly testUser: Reference<xtremSystem.nodes.User | null>;

    @decorators.referenceProperty<WorkflowDefinition, 'diagram'>({
        isStored: true,
        isPublished: true,
        isMutable: true,
        node: () => xtremWorkflow.nodes.WorkflowDiagram,
        async control(cx) {
            const engine = WorkflowEngine.fromApplication(this.$.context.application);
            const tenantId = this.$.context.tenantId || '';
            const id = await this.id;
            const flow = await (await this.diagram).data;
            await new FlowWrapper(engine, tenantId, id, flow).control(this.$.context.application, cx);
        },
    })
    readonly diagram: Reference<xtremWorkflow.nodes.WorkflowDiagram>;

    @decorators.jsonProperty<WorkflowDefinition, 'flow'>({
        isPublished: true,
        dependsOn: ['diagram'],
        async getValue() {
            return (await this.diagram).data;
        },
    })
    readonly flow: Promise<Flow>;

    @decorators.stringProperty<WorkflowDefinition, 'startTopic'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.name,
        dependsOn: ['flow'],
        async defaultValue() {
            const startNode = FlowWrapper.getStartNode(await this.flow);

            return (startNode.data as unknown as { topic: string })?.topic;
        },
        updatedValue: useDefaultValue,
    })
    readonly startTopic: Promise<string>;

    @decorators.collectionProperty<WorkflowDefinition, 'processes'>({
        isPublished: true,
        node: () => xtremWorkflow.nodes.WorkflowProcess,
        reverseReference: 'definition',
    })
    readonly processes: Collection<xtremWorkflow.nodes.WorkflowProcess>;

    /**
     * Compute the unique id of the workflow from it's name.
     */
    private async _computeUniqueIdFromName(): Promise<string> {
        // Build a unique id starting with the name of the workflow + '_'
        // We only keep the some chars from the name
        // 'My # beautiful * workflow' -> 'Mybeautifulworkflow_'
        const name = (await this.name).replace(/[^0-9a-zA-Z]/g, '');
        return this.$.context.buildUniqueValueForProperty(this, 'id', `${name}_`);
    }
}
