import { Context, Node, ValidationContext, decorators } from '@sage/xtrem-core';
import { ValidationSeverity, WorkflowError } from '@sage/xtrem-shared';
import * as xtremSystem from '@sage/xtrem-system';
import { Flow, FlowEdge, FlowNode, FlowWrapper, WorkflowEngine } from '../engine';
import * as xtremWorkflow from '../index';

@decorators.node<WorkflowDiagram>({
    storage: 'sql',
    isPublished: true,
    isContentAddressable: true,
    isCached: true,
    canCreate: true,
    canRead: true,
    canUpdate: true,
    serviceOptions: () => [xtremWorkflow.serviceOptions.workflow],
})
export class WorkflowDiagram extends Node {
    /** The version of the application that created it */
    @decorators.stringProperty<WorkflowDiagram, 'version'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremSystem.dataTypes.version,
        defaultValue() {
            return this.$.context.application.version;
        },
    })
    readonly version: Promise<string>;

    @decorators.jsonProperty<WorkflowDiagram, 'data'>({
        isStored: true,
        isPublished: true,
    })
    readonly data: Promise<Flow>;

    /**
     * Control of a workflow diagram.
     * @param context
     * @param nodes
     * @param edges
     * @returns The list of nodes with errors, or undefined if there are no errors.
     */
    @decorators.mutation<typeof WorkflowDiagram, 'controlDiagram'>({
        isPublished: true,
        parameters: [
            {
                name: 'nodes',
                type: 'json',
                isMandatory: true,
            },
            {
                name: 'edges',
                type: 'json',
                isMandatory: true,
            },
        ],
        return: {
            type: 'array',
            isNullable: true,
            item: {
                type: 'object',
                properties: {
                    stepId: { type: 'string' },
                    message: { type: 'string' },
                },
            },
        },
    })
    static async controlDiagram(context: Context, nodes: any, edges: any): Promise<WorkflowError[] | undefined> {
        const flowNodes = nodes as FlowNode[];
        const flowEdges = edges as FlowEdge[];
        const flowWrapper = new FlowWrapper(
            WorkflowEngine.fromApplication(context.application),
            context.tenantId || '',
            '', // definitionId is not used in this context
            {
                nodes: flowNodes,
                edges: flowEdges,
            },
        );

        await flowWrapper.control(context.application, new ValidationContext(context));

        const diagnoses = context.diagnoses;
        const errors = diagnoses.filter(d => d.severity === ValidationSeverity.error);
        if (errors.length > 0) {
            return errors.map(d => ({
                stepId: d.path.at(-1) || '',
                message: d.message,
            }));
        }

        return undefined;
    }
}
