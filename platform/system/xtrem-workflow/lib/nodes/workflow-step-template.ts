import {
    asyncArray,
    Context,
    decorators,
    Node,
    Reference,
    WorkflowStepDescriptor,
    WorkflowStepIcon,
    workflowStepIconDataType,
} from '@sage/xtrem-core';
import * as xtremMetadata from '@sage/xtrem-metadata';
import { Dict } from '@sage/xtrem-shared';
import * as xtremSystem from '@sage/xtrem-system';
import { merge, uniqBy } from 'lodash';
import { logger } from '../engine/logger';
import * as xtremWorkflow from '../index';

@decorators.node<WorkflowStepTemplate>({
    storage: 'sql',
    canCreate: true,
    canRead: true,
    canUpdate: true,
    canDelete: true,
    canSearch: true,
    isPublished: true,
    isSetupNode: true,
    serviceOptions: () => [xtremWorkflow.serviceOptions.workflow],
    indexes: [
        {
            orderBy: { stepConstructor: 1, variant: 1 },
            isUnique: true,
            isNaturalKey: true,
        },
    ],
})
export class WorkflowStepTemplate extends Node {
    private _descriptor: WorkflowStepDescriptor | null = null;

    @decorators.stringProperty<WorkflowStepTemplate, 'stepConstructor'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.name,
    })
    readonly stepConstructor: Promise<string>;

    @decorators.stringProperty<WorkflowStepTemplate, 'variant'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.name,
        lookupAccess: true,
        isNotEmpty: true,
    })
    readonly variant: Promise<string>;

    @decorators.referenceProperty<WorkflowStepTemplate, 'factory'>({
        isStored: true,
        isNullable: true,
        isPublished: true,
        node: () => xtremMetadata.nodes.MetaNodeFactory,
    })
    readonly factory: Reference<xtremMetadata.nodes.MetaNodeFactory | null>;

    @decorators.booleanProperty<WorkflowStepTemplate, 'isActive'>({
        isStored: true,
        isPublished: true,
        defaultValue: true,
        provides: ['isActive'],
    })
    readonly isActive: Promise<boolean>;

    @decorators.stringProperty<WorkflowStepTemplate, 'title'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.localizedTitle,
        lookupAccess: true,
    })
    readonly title: Promise<string>;

    @decorators.stringProperty<WorkflowStepTemplate, 'description'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.localizedDescription,
        lookupAccess: true,
    })
    readonly description: Promise<string>;

    @decorators.enumProperty<WorkflowStepTemplate, 'icon'>({
        isStored: true,
        isPublished: true,
        dataType: () => workflowStepIconDataType,
    })
    readonly icon: Promise<WorkflowStepIcon>;

    @decorators.stringProperty<WorkflowStepTemplate, 'color'>({
        isPublished: true,
        isStored: true,
        dataType: () => xtremSystem.dataTypes.rgbColor,
    })
    readonly color: Promise<string>;

    @decorators.referenceArrayProperty<WorkflowStepTemplate, 'serviceOptions'>({
        isStored: true,
        isPublished: true,
        onDelete: 'restrict',
        node: () => xtremMetadata.nodes.MetaServiceOption,
    })
    readonly serviceOptions: Promise<xtremMetadata.nodes.MetaServiceOption[]>;

    @decorators.jsonProperty<WorkflowStepTemplate, 'configData'>({
        isNullable: false,
        isStored: true,
        isPublished: true,
    })
    readonly configData: Promise<any>;

    @decorators.jsonProperty<WorkflowStepTemplate, 'stepDescriptor'>({
        isNullable: false,
        isStored: false,
        isPublished: true,
        computeValue() {
            return this.getWorkflowStepDescriptor(this.$.context);
        },
    })
    readonly stepDescriptor: Promise<WorkflowStepDescriptor>;

    /**
     * Returns whether ALL the service options are enabled for this workflow step
     */
    async areServiceOptionsEnabled(context: Context): Promise<boolean> {
        const serviceOptions = (await this.getWorkflowStepDescriptor(context)).serviceOptions?.();
        if (serviceOptions == null || serviceOptions.length === 0) return true;
        return asyncArray(serviceOptions).every(serviceOption => context.isServiceOptionEnabled(serviceOption));
    }

    /**
     * Returns the WorkflowStepDescriptor for the current node
     */
    async getWorkflowStepDescriptor(context: Context): Promise<WorkflowStepDescriptor> {
        if (this._descriptor != null) return this._descriptor;

        const stepConstructor = await this.stepConstructor;
        // Retrieve the descriptor from the manager
        const descriptor = this.$.factory.application.workflowManager.getWorkflowStepDescriptor(stepConstructor);
        this._descriptor = merge({}, descriptor, {
            key: `${descriptor.key}/${await this.variant}`,
            title: await this.title,
            description: await this.description,
            defaultConfig: await this.configData,
            ui: { color: `#${await this.color}`, icon: await this.icon },
        });
        const allServiceOptions = [...(descriptor.serviceOptions?.() ?? [])];
        const serviceOptionNames = await this.serviceOptions;
        await asyncArray(serviceOptionNames).forEach(async metaServiceOption => {
            const serviceOption = context.serviceOptionManager.serviceOptionsByName[await metaServiceOption.name];
            if (serviceOption) allServiceOptions.push(serviceOption);
        });
        this._descriptor.serviceOptions = () => uniqBy(allServiceOptions, 'name');
        return this._descriptor;
    }

    @decorators.mutation<typeof WorkflowStepTemplate, 'registerStepConfiguration'>({
        isPublished: true,
        parameters: [
            { name: 'stepConstructor', type: 'string', isMandatory: true },
            { name: 'asSetupNode', type: 'boolean', isMandatory: true },
            { name: 'factory', type: 'reference', node: () => xtremMetadata.nodes.MetaNodeFactory, isMandatory: true },
            {
                name: 'localizedTitle',
                type: 'json',
                isMandatory: true,
            },
            {
                name: 'localizedDescription',
                type: 'json',
                isMandatory: true,
            },
            { name: 'configData', type: 'json', isMandatory: true },
        ],
        return: {
            type: 'reference',
            node: () => WorkflowStepTemplate,
        },
    })
    static async registerStepConfiguration(
        context: Context,
        stepConstructor: string,
        asSetupNode: boolean,
        factory: xtremMetadata.nodes.MetaNodeFactory,
        localizedTitle: Dict<string>,
        localizedDescription: Dict<string>,
        configData: any,
    ): Promise<Reference<WorkflowStepTemplate>> {
        logger.info(`Registering step configuration ${stepConstructor}`);
        if (localizedTitle == null || localizedTitle.base == null || localizedTitle.base.length === 0) {
            throw new Error(context.localize('@sage/xtrem-workflow/workflow-error-no-title', 'Title is missing'));
        }
        if (
            localizedDescription == null ||
            localizedDescription.base == null ||
            localizedDescription.base.length === 0
        ) {
            throw new Error(
                context.localize('@sage/xtrem-workflow/workflow-error-no-description', 'Description is missing'),
            );
        }
        const descriptor = context.application.workflowManager.getWorkflowStepDescriptor(stepConstructor);
        const createdNode = await context.withLocalizedTextAsJson(async () => {
            const createdConf = await context.create(WorkflowStepTemplate, {
                stepConstructor,
                variant: '',
                isActive: true,
                title: JSON.stringify(localizedTitle),
                description: JSON.stringify(localizedDescription),
                icon: descriptor.ui.icon,
                // Colors in descriptor have the leading '#'
                color: descriptor.ui.color.split('#').pop(),
                configData,
                factory,
            });
            // Build a unique variant from the title
            // "worklows - My beautiful action" -> "workflows_my_beautiful_action"
            const variant = await context.buildUniqueValueForProperty(
                createdConf,
                'variant',
                `${asSetupNode ? '_' : ''}${localizedTitle.base
                    .replace(/\s/g, '_')
                    .replace(/[^_0-9a-zA-Z]/g, '')
                    .replace(/__/g, '_')
                    .substring(0, 77) // 80 is the max length for a variant but leave 3 digits for the incremental part
                    .toLowerCase()}`,
            );
            if (asSetupNode) {
                await createdConf.$.set({
                    variant,
                    _vendor: '#sage',
                });
            } else {
                await createdConf.$.set({
                    variant,
                });
            }
            logger.info(`Variant set to ${await createdConf.variant}`);

            await createdConf.$.save();
            return createdConf;
        });
        return createdNode;
    }
}
