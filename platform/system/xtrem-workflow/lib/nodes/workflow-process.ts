import * as xtremCommunication from '@sage/xtrem-communication';
import { ContainerManager, Context, Node, Reference, datetime, decorators, nanoIdDataType } from '@sage/xtrem-core';
import { Dict, WorkflowVariable } from '@sage/xtrem-shared';
import * as xtremSystem from '@sage/xtrem-system';
import { WorkflowProcessState } from '../engine/workflow-process-state';
import * as xtremWorkflow from '../index';
import { WorkflowLogEntry } from '../shared-functions/workflow-log';
import { WorkflowProcessCompletedEvent, WorkflowRunner } from '../test/workflow-runner';

@decorators.node<WorkflowProcess>({
    storage: 'sql',
    canCreate: true,
    canRead: true,
    canUpdate: true,
    canDelete: true,
    canSearch: true,
    isPublished: true,
    isSkippedByLayerExtract: true,
    isClearedByReset: true,
    serviceOptions: () => [xtremWorkflow.serviceOptions.workflow],
    indexes: [
        { orderBy: { id: 1 }, isUnique: true, isNaturalKey: true },
        // index originId to improve performance of entity-updated event
        { orderBy: { originId: 1 } },
    ],
    async getFilters(context) {
        // Note: we use the loginUser vs the user of the context to deal with demo personas on demo tenants
        const loginUser = await context.loginUser;

        if (loginUser?.isAdministrator) {
            // Administrators can see all workflows
            return [];
        }

        // Show only the workflows created by the user
        return [
            { definition: { testUser: `#${loginUser?.email}` } }, // Include workflows for the test user
        ];
    },
})
export class WorkflowProcess extends Node {
    @decorators.stringProperty<WorkflowProcess, 'id'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.nanoid,
        lookupAccess: true,
    })
    readonly id: Promise<string>;

    @decorators.referenceProperty<WorkflowProcess, 'definition'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        ignoreIsActive: true,
        node: () => xtremWorkflow.nodes.WorkflowDefinition,
    })
    readonly definition: Reference<xtremWorkflow.nodes.WorkflowDefinition>;

    @decorators.referenceProperty<WorkflowProcess, 'diagram'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        node: () => xtremWorkflow.nodes.WorkflowDiagram,
        dependsOn: ['definition'],
        async defaultValue() {
            return (await this.definition).diagram;
        },
    })
    readonly diagram: Reference<xtremWorkflow.nodes.WorkflowDiagram>;

    @decorators.stringProperty<WorkflowProcess, 'containerId'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.name,
        defaultValue() {
            return ContainerManager.containerId;
        },
    })
    readonly containerId: Promise<string>;

    @decorators.stringProperty<WorkflowProcess, 'startNotificationId'>({
        isStored: true,
        isPublished: true,
        dataType: () => nanoIdDataType,
        isFrozen: true,
    })
    readonly startNotificationId: Promise<string>;

    @decorators.stringProperty<WorkflowProcess, 'originId'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremCommunication.dataTypes.originIdDataType,
    })
    readonly originId: Promise<string>;

    @decorators.referenceProperty<WorkflowProcess, 'triggeringUser'>({
        isStored: true,
        isPublished: true,
        node: () => xtremSystem.nodes.User,
        isFrozen: true,
    })
    readonly triggeringUser: Promise<xtremSystem.nodes.User>;

    @decorators.enumProperty<WorkflowProcess, 'status'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremWorkflow.enums.WorkflowProcessStatusDataType,
    })
    readonly status: Promise<xtremWorkflow.enums.WorkflowProcessStatus>;

    @decorators.datetimeProperty<WorkflowProcess, 'startedAt'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
    })
    readonly startedAt: Promise<datetime>;

    @decorators.datetimeProperty<WorkflowProcess, 'completedAt'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
    })
    readonly completedAt: Promise<datetime | null>;

    @decorators.integerProperty<WorkflowProcess, 'duration'>({
        isPublished: true,
        async computeValue() {
            const startedAt = await this.startedAt;
            const completedAt = await this.completedAt;
            if (!startedAt || !completedAt) {
                return 0;
            }
            // Calculate the duration in seconds
            const durationSeconds = Math.floor((completedAt.value - startedAt.value) / 1000);
            return durationSeconds;
        },
    })
    readonly duration: Promise<number>;

    @decorators.jsonProperty<WorkflowProcess, 'state'>({
        isStored: true,
    })
    readonly state: Promise<WorkflowProcessState>;

    @decorators.stringProperty<WorkflowProcess, 'errorMessages'>({
        isPublished: true,
        async computeValue() {
            const state = await this.state;
            return Object.entries(state.stepStates)
                .filter(([, stepState]) => !!stepState.errorMessage)
                .map(([stepId, stepState]) => `${stepId}: ${stepState.errorMessage}`)
                .join('\n');
        },
    })
    readonly errorMessages: Promise<string>;

    @decorators.jsonProperty<WorkflowProcess, 'eventLog'>({
        isPublished: true,
        async getValue() {
            const state = await this.state;
            return state.log;
        },
    })
    readonly eventLog: Promise<WorkflowLogEntry[]>;

    @decorators.jsonProperty<WorkflowProcess, 'variables'>({
        isPublished: true,
        async getValue() {
            const state = await this.state;
            return state.variables;
        },
    })
    readonly variables: Promise<Dict<WorkflowVariable>>;

    @decorators.notificationListener<typeof WorkflowProcess>({
        topic: 'WorkflowProcess/completed',
    })
    static onCompleted(context: Context, event: WorkflowProcessCompletedEvent): Promise<void> {
        return WorkflowRunner.onWorkflowCompleted(context, event);
    }

    /**
     * Placeholder listener to ensure that workflow queue will be listened to.
     * DO NOT REMOVE
     */
    @decorators.notificationListener({
        topic: 'WorkflowProcess/placeholder',
    })
    static placeholder(): void {
        /* empty */
    }
}
