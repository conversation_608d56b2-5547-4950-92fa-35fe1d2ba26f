Missing start node:
  input:
    diagram: '{
      \"edges\": [
        { \"id\": \"e1\", \"source\": \"n1\", \"target\": \"n2\", \"sourceHandle\": \"out\" },
        { \"id\": \"e2\", \"source\": \"n2\", \"target\": \"n1\", \"sourceHandle\": \"out\" }],
      \"nodes\": [
        { \"id\": \"n1\", \"type\": \"entity-created\" },
        { \"id\": \"n2\", \"type\": \"wait\" }]
      }'
  output:
    diagnoses: '[{
      "message": "Invalid workflow - flow does not have any start node",
      "path": [],
      "severity": 4
    }]'
  envConfigs:
    testActiveServiceOptions:
      - workflow

More than one start node:
  input:
    diagram: '{
      \"edges\": [
        { \"id\": \"e1\", \"source\": \"n1\", \"target\": \"n3\", \"sourceHandle\": \"out\" },
        { \"id\": \"e2\", \"source\": \"n2\", \"target\": \"n3\", \"sourceHandle\": \"out\" }],
      \"nodes\": [
        { \"id\": \"n1\", \"type\": \"entity-created\" },
        { \"id\": \"n2\", \"type\": \"entity-created\" },
        { \"id\": \"n3\", \"type\": \"wait\" }]
      }'
  output:
    diagnoses: '[{
      "message": "Invalid workflow - flow has more than one start nodes: ''n1'', ''n2''",
      "path": [],
      "severity": 4
    }]'
  envConfigs:
    testActiveServiceOptions:
      - workflow

Cycle:
  input:
    diagram: '{
      \"edges\": [
        { \"id\": \"e0\", \"source\": \"n0\", \"target\": \"n1\", \"sourceHandle\": \"out\" },
        { \"id\": \"e1\", \"source\": \"n1\", \"target\": \"n2\", \"sourceHandle\": \"out\" },
        { \"id\": \"e2\", \"source\": \"n2\", \"target\": \"n1\", \"sourceHandle\": \"out\" }],
      \"nodes\": [
        { \"id\": \"n0\", \"type\": \"entity-created\" },
        { \"id\": \"n1\", \"type\": \"wait\" },
        { \"id\": \"n2\", \"type\": \"wait\" }]
      }'
  output:
    diagnoses: '[{
        "message": "Flow has a cycle on node: n1",
        "path": ["diagram"],
        "severity": 3
    }]'
  envConfigs:
    testActiveServiceOptions:
      - workflow
