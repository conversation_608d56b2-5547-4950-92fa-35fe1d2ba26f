envConfigs:
  testActiveServiceOptions:
    - workflow
scenarios:
  Test 1, 1, 2 (equals true, between true):
    workflow: 'conditionIntegerTest'
    startEvent:
      topic: 'TestDataTypes/created'
      payload: { id: 'integerTest1', int1: 1, int2: 1, int3: 2 }
    expectedResult:
      status: 'success'
      variables:
        equals: true
        notEquals: <<undefined>>
        lessThan: <<undefined>>
        lessThanOrEqual: true
        greaterThan: <<undefined>>
        greaterThanOrEqual: true
        between: true

  Test 3, 1, 2 (equals false, between false):
    workflow: 'conditionIntegerTest'
    startEvent:
      topic: 'TestDataTypes/created'
      payload: { id: 'integerTest2', int1: 3, int2: 1, int3: 2 }
    expectedResult:
      status: 'success'
      variables:
        equals: <<undefined>>
        notEquals: true
        lessThan: <<undefined>>
        lessThanOrEqual: <<undefined>>
        greaterThan: true
        greaterThanOrEqual: true
        between: <<undefined>>

  Test 2, 1, 2 (equals false, between true):
    workflow: 'conditionIntegerTest'
    startEvent:
      topic: 'TestDataTypes/created'
      payload: { id: 'integerTest3', int1: 2, int2: 1, int3: 2 }
    expectedResult:
      status: 'success'
      variables:
        equals: <<undefined>>
        notEquals: true
        lessThan: <<undefined>>
        lessThanOrEqual: <<undefined>>
        greaterThan: true
        greaterThanOrEqual: true
        between: true
