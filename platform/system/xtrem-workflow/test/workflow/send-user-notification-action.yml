envConfigs:
  testActiveServiceOptions:
    - workflow
scenarios:
  Can send a notification (en):
    envConfigs:
      locale: 'en-US'
    workflow: 'sendUserNotificationTest'
    mocks:
      - path: '@sage/xtrem-workflow/build/test/fixtures/send-user-notification-mock/UserNotificationMock'
        calls:
          - input:
              title: 'Title - my title'
              description: 'Description(en) - my description'
              icon: 'business'
              level: 'info'
              shouldDisplayToast: true
              actions:
                - title: 'action1(en) - my action 1'
                  link: 'link1 - my link 1'
                  style: 'primary'
    startEvent:
      topic: 'WorkflowProcess/testStarted'
      payload:
        workflow: 'sendUserNotificationTest'
        parameters:
          title: 'my title'
          description: 'my description'
          action1: 'my action 1'
          link1: 'my link 1'
    expectedResult:
      status: 'success'
      variables:
        done: true

  Can send a notification (fr):
    envConfigs:
      locale: 'fr-FR'
    workflow: 'sendUserNotificationTest'
    mocks:
      - path: '@sage/xtrem-workflow/build/test/fixtures/send-user-notification-mock/UserNotificationMock'
        calls:
          - input:
              title: 'Titre - mon titre'
              description: 'Description(fr) - ma description'
              icon: 'business'
              level: 'info'
              shouldDisplayToast: true
              actions:
                - title: 'action1(fr) - mon action 1'
                  link: 'link1 - mon lien 1'
                  style: 'primary'
    startEvent:
      topic: 'WorkflowProcess/testStarted'
      payload:
        workflow: 'sendUserNotificationTest'
        parameters:
          title: 'mon titre'
          description: 'ma description'
          action1: 'mon action 1'
          link1: 'mon lien 1'
    expectedResult:
      status: 'success'
      variables:
        done: true
