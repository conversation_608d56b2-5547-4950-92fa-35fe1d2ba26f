envConfigs:
  testActiveServiceOptions:
    - workflow
scenarios:
  Test 1.5, 1.5, 2.5 (equals true, between true):
    workflow: 'conditionDecimalTest'
    startEvent:
      topic: 'TestDataTypes/created'
      payload: { id: 'decimalTest1', decimal1: 1.5, decimal2: 1.5, decimal3: 2.5 }
    expectedResult:
      status: 'success'
      variables:
        equals: true
        notEquals: <<undefined>>
        lessThan: <<undefined>>
        lessThanOrEqual: true
        greaterThan: <<undefined>>
        greaterThanOrEqual: true
        between: true

  Test 2.5, 1, 2 (equals false, between false):
    workflow: 'conditionDecimalTest'
    startEvent:
      topic: 'TestDataTypes/created'
      payload: { id: 'decimalTest2', decimal1: 2.5, decimal2: 1, decimal3: 2 }
    expectedResult:
      status: 'success'
      variables:
        equals: <<undefined>>
        notEquals: true
        lessThan: <<undefined>>
        lessThanOrEqual: <<undefined>>
        greaterThan: true
        greaterThanOrEqual: true
        between: <<undefined>>

  Test 2.5, 1, 2.5 (equals false, between true):
    workflow: 'conditionDecimalTest'
    startEvent:
      topic: 'TestDataTypes/created'
      payload: { id: 'decimalTest3', decimal1: 2.5, decimal2: 1, decimal3: 2.5 }
    expectedResult:
      status: 'success'
      variables:
        equals: <<undefined>>
        notEquals: true
        lessThan: <<undefined>>
        lessThanOrEqual: <<undefined>>
        greaterThan: true
        greaterThanOrEqual: true
        between: true
