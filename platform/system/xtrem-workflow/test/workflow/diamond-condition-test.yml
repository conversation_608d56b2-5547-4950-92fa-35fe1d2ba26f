envConfigs:
  testActiveServiceOptions:
    - workflow
scenarios:
  Can execute a diamond flow with a true condition:
    workflow: 'diamondConditionTest'
    startEvent:
      topic: 'WorkflowProcess/testStarted'
      payload:
        workflow: 'diamondConditionTest'
        parameters:
          p1: true
    expectedResult:
      status: 'success'
      variables:
        p1True: true
        p1False: <<undefined>>
        join: true
  Can execute a diamond flow with a false condition:
    workflow: 'diamondConditionTest'
    startEvent:
      topic: 'WorkflowProcess/testStarted'
      payload:
        workflow: 'diamondConditionTest'
        parameters:
          p1: false
    expectedResult:
      status: 'success'
      variables:
        p1True: <<undefined>>
        p1False: true
        join: true
