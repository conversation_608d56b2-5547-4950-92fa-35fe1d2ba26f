envConfigs:
  testActiveServiceOptions:
    - workflow
scenarios:
  Can select a custom field in an entity created event:
    workflow: 'customFieldsTest'
    startEvent:
      topic: 'TestDataTypes/created'
      payload:
        id: customField1
        _customData:
          customText: 'hello'
          customBool: false
    expectedResult:
      status: 'success'
      variables:
        testDataTypes:
          id: customField1
          _customData:
            customText: 'hello world!'
            customBool: true
        ok: true
