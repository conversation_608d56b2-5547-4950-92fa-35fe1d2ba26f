envConfigs:
  today: '2024-10-04'
  testActiveServiceOptions:
    - workflow

scenarios:
  Test Jan01, Jan01, Jan02 (equals true, between true):
    workflow: 'conditionDatetimeTest'
    startEvent:
      topic: 'TestDataTypes/created'
      payload:
        id: 'dtTest1'
        datetime1: '2024-01-01T18:30:00Z'
        datetime2: '2024-01-01T18:30:00Z'
        datetime3: '2024-01-01T18:30:00Z'
    expectedResult:
      status: 'success'
      variables:
        equals: true
        notEquals: <<undefined>>
        lessThan: <<undefined>>
        lessThanOrEqual: true
        greaterThan: <<undefined>>
        greaterThanOrEqual: true
        between: true

  Test Jan03, Jan01, Jan02 (equals false, between false):
    workflow: 'conditionDatetimeTest'
    startEvent:
      topic: 'TestDataTypes/created'
      payload:
        id: 'dtTest2'
        datetime1: '2024-01-03T18:30:00Z'
        datetime2: '2024-01-01T18:30:00Z'
        datetime3: '2024-01-02T18:30:00Z'
    expectedResult:
      status: 'success'
      variables:
        equals: <<undefined>>
        notEquals: true
        lessThan: <<undefined>>
        lessThanOrEqual: <<undefined>>
        greaterThan: true
        greaterThanOrEqual: true
        between: <<undefined>>

  Test Jan02, Jan01, Jan02 (equals false, between true):
    workflow: 'conditionDatetimeTest'
    startEvent:
      topic: 'TestDataTypes/created'
      payload:
        id: 'dtTest3'
        datetime1: '2024-01-02T18:30:00Z'
        datetime2: '2024-01-01T18:30:00Z'
        datetime3: '2024-01-02T18:30:00Z'
    expectedResult:
      status: 'success'
      variables:
        equals: <<undefined>>
        notEquals: true
        lessThan: <<undefined>>
        lessThanOrEqual: <<undefined>>
        greaterThan: true
        greaterThanOrEqual: true
        between: true

  Test last 7 days:
    workflow: 'conditionDatetimeTest'
    startEvent:
      topic: 'TestDataTypes/created'
      payload:
        id: 'dtTest4'
        datetime1: '2024-10-03T18:30:00Z'
    expectedResult:
      status: 'success'
      variables:
        last7Days: true
        last30Days: true
        currentMonth: true
        previousMonth: <<undefined>>
        currentYear: true
        previousYear: <<undefined>>

  Test last 30 days:
    workflow: 'conditionDatetimeTest'
    startEvent:
      topic: 'TestDataTypes/created'
      payload:
        id: 'dtTest5'
        datetime1: '2024-09-20T18:30:00Z'
    expectedResult:
      status: 'success'
      variables:
        last7Days: <<undefined>>
        last30Days: true
        currentMonth: <<undefined>>
        previousMonth: true
        currentYear: true
        previousYear: <<undefined>>

  Test current month:
    workflow: 'conditionDatetimeTest'
    startEvent:
      topic: 'TestDataTypes/created'
      payload:
        id: 'dtTest6'
        datetime1: '2024-10-20T18:30:00Z'
    expectedResult:
      status: 'success'
      variables:
        last7Days: <<undefined>>
        last30Days: <<undefined>>
        currentMonth: true
        previousMonth: <<undefined>>
        currentYear: true
        previousYear: <<undefined>>

  Test previous month:
    workflow: 'conditionDatetimeTest'
    startEvent:
      topic: 'TestDataTypes/created'
      payload:
        id: 'dtTest7'
        datetime1: '2024-09-02T18:30:00Z'
    expectedResult:
      status: 'success'
      variables:
        last7Days: <<undefined>>
        last30Days: <<undefined>>
        currentMonth: <<undefined>>
        previousMonth: true
        currentYear: true
        previousYear: <<undefined>>

  Test current year:
    workflow: 'conditionDatetimeTest'
    startEvent:
      topic: 'TestDataTypes/created'
      payload:
        id: 'dtTest8'
        datetime1: '2024-01-10T18:30:00Z'
    expectedResult:
      status: 'success'
      variables:
        last7Days: <<undefined>>
        last30Days: <<undefined>>
        currentMonth: <<undefined>>
        previousMonth: <<undefined>>
        currentYear: true
        previousYear: <<undefined>>

  Test previous year:
    workflow: 'conditionDatetimeTest'
    startEvent:
      topic: 'TestDataTypes/created'
      payload:
        id: 'dtTest9'
        datetime1: '2023-01-10T18:30:00Z'
    expectedResult:
      status: 'success'
      variables:
        last7Days: <<undefined>>
        last30Days: <<undefined>>
        currentMonth: <<undefined>>
        previousMonth: <<undefined>>
        currentYear: <<undefined>>
        previousYear: true

  Test too old:
    workflow: 'conditionDatetimeTest'
    startEvent:
      topic: 'TestDataTypes/created'
      payload:
        id: 'dtTest10'
        datetime1: '2022-01-10T18:30:00Z'
    expectedResult:
      status: 'success'
      variables:
        last7Days: <<undefined>>
        last30Days: <<undefined>>
        currentMonth: <<undefined>>
        previousMonth: <<undefined>>
        currentYear: <<undefined>>
        previousYear: <<undefined>>
