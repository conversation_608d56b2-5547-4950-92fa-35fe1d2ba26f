envConfigs:
  testActiveServiceOptions:
    - workflow
scenarios:
  # <PERSON> is created by entity-created-event.yml test.
  # So don't execute this one in "only" mode.
  Triggers age checking workflow if a person's age increases:
    workflow: 'entityUpdatedEvent'
    startEvent:
      topic: 'TestPerson/updated'
      payload: { _id: '#Alice', age: 40 }
    expectedResult:
      status: 'success'
      variables:
        # workflow does not select pet.type
        testPerson: { name: '<PERSON>', age: 40, pet: { name: '<PERSON><PERSON><PERSON>' } }
        $old: { testPerson: { name: '<PERSON>', age: 30 } }
