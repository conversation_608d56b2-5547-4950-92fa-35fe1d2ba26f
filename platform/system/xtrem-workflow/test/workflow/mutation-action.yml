envConfigs:
  testActiveServiceOptions:
    - workflow
scenarios:
  Can invoke a mutation:
    workflow: 'mutationTestFromVariable'
    startEvent:
      topic: 'TestNodeForMutation/created'
      payload: { id: 'TEST1', valInt: 2, valStr: 'foo', valBool: true, valEnum: 'value1' }
    expectedResult:
      status: 'success'
      variables:
        updatedNode: { id: 'TEST1', valInt: 7, valStr: 'foofoo', valBool: false, valEnum: 'value2' }
