envConfigs:
  testActiveServiceOptions:
    - workflow
scenarios:
  Can update a record:
    workflow: 'updateEntityTest'
    startEvent:
      topic: 'TestDataTypes/created'
      payload:
        id: 'updateEntity1'
        bool1: true
        str1: world
        date1: '2019-01-01'
    expectedResult:
      status: 'success'
      variables:
        testDataTypes:
          bool1: true
          bool2: true
          str1: world
          str2: Hello world!
          date1: '2019-01-01'
          date2: '2019-01-01'
