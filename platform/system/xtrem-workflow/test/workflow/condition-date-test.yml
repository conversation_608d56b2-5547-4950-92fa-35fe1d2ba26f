envConfigs:
  today: '2024-10-04'
  testActiveServiceOptions:
    - workflow

scenarios:
  Test Jan01, Jan01, Jan02 (equals true, between true):
    workflow: 'conditionDateTest'
    startEvent:
      topic: 'TestDataTypes/created'
      payload: { id: 'dateTest1', date1: '2024-01-01', date2: '2024-01-01', date3: '2024-01-01' }
    expectedResult:
      status: 'success'
      variables:
        equals: true
        notEquals: <<undefined>>
        lessThan: <<undefined>>
        lessThanOrEqual: true
        greaterThan: <<undefined>>
        greaterThanOrEqual: true
        between: true

  Test Jan03, Jan01, Jan02 (equals false, between false):
    workflow: 'conditionDateTest'
    startEvent:
      topic: 'TestDataTypes/created'
      payload: { id: 'dateTest2', date1: '2024-01-03', date2: '2024-01-01', date3: '2024-01-02' }
    expectedResult:
      status: 'success'
      variables:
        equals: <<undefined>>
        notEquals: true
        lessThan: <<undefined>>
        lessThanOrEqual: <<undefined>>
        greaterThan: true
        greaterThanOrEqual: true
        between: <<undefined>>

  Test Jan02, Jan01, Jan02 (equals false, between true):
    workflow: 'conditionDateTest'
    startEvent:
      topic: 'TestDataTypes/created'
      payload: { id: 'dateTest3', date1: '2024-01-02', date2: '2024-01-01', date3: '2024-01-02' }
    expectedResult:
      status: 'success'
      variables:
        equals: <<undefined>>
        notEquals: true
        lessThan: <<undefined>>
        lessThanOrEqual: <<undefined>>
        greaterThan: true
        greaterThanOrEqual: true
        between: true

  Test last 7 days:
    workflow: 'conditionDateTest'
    startEvent:
      topic: 'TestDataTypes/created'
      payload: { id: 'dateTest4', date1: '2024-10-03' }
    expectedResult:
      status: 'success'
      variables:
        last7Days: true
        last30Days: true
        currentMonth: true
        previousMonth: <<undefined>>
        currentYear: true
        previousYear: <<undefined>>

  Test last 30 days:
    workflow: 'conditionDateTest'
    startEvent:
      topic: 'TestDataTypes/created'
      payload: { id: 'dateTest5', date1: '2024-09-20' }
    expectedResult:
      status: 'success'
      variables:
        last7Days: <<undefined>>
        last30Days: true
        currentMonth: <<undefined>>
        previousMonth: true
        currentYear: true
        previousYear: <<undefined>>

  Test current month:
    workflow: 'conditionDateTest'
    startEvent:
      topic: 'TestDataTypes/created'
      payload: { id: 'dateTest6', date1: '2024-10-20' }
    expectedResult:
      status: 'success'
      variables:
        last7Days: <<undefined>>
        last30Days: <<undefined>>
        currentMonth: true
        previousMonth: <<undefined>>
        currentYear: true
        previousYear: <<undefined>>

  Test previous month:
    workflow: 'conditionDateTest'
    startEvent:
      topic: 'TestDataTypes/created'
      payload: { id: 'dateTest7', date1: '2024-09-02' }
    expectedResult:
      status: 'success'
      variables:
        last7Days: <<undefined>>
        last30Days: <<undefined>>
        currentMonth: <<undefined>>
        previousMonth: true
        currentYear: true
        previousYear: <<undefined>>

  Test current year:
    workflow: 'conditionDateTest'
    startEvent:
      topic: 'TestDataTypes/created'
      payload: { id: 'dateTest8', date1: '2024-01-10' }
    expectedResult:
      status: 'success'
      variables:
        last7Days: <<undefined>>
        last30Days: <<undefined>>
        currentMonth: <<undefined>>
        previousMonth: <<undefined>>
        currentYear: true
        previousYear: <<undefined>>

  Test previous year:
    workflow: 'conditionDateTest'
    startEvent:
      topic: 'TestDataTypes/created'
      payload: { id: 'dateTest9', date1: '2023-01-10' }
    expectedResult:
      status: 'success'
      variables:
        last7Days: <<undefined>>
        last30Days: <<undefined>>
        currentMonth: <<undefined>>
        previousMonth: <<undefined>>
        currentYear: <<undefined>>
        previousYear: true

  Test too old:
    workflow: 'conditionDateTest'
    startEvent:
      topic: 'TestDataTypes/created'
      payload: { id: 'dateTest10', date1: '2022-01-10' }
    expectedResult:
      status: 'success'
      variables:
        last7Days: <<undefined>>
        last30Days: <<undefined>>
        currentMonth: <<undefined>>
        previousMonth: <<undefined>>
        currentYear: <<undefined>>
        previousYear: <<undefined>>
