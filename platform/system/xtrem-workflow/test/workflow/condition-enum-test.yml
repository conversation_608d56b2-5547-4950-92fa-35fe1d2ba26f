envConfigs:
  testActiveServiceOptions:
    - workflow
scenarios:
  Test value1 value1 (equals true):
    workflow: 'conditionEnumTest'
    startEvent:
      topic: 'TestDataTypes/created'
      payload: { id: 'enumTest1', enum1: value1, enum2: value1 }
    expectedResult:
      status: 'success'
      variables:
        equalsValue1: true
        notEqualsValue1: <<undefined>>
        equals: true

  Test value2 value3 (equals false):
    workflow: 'conditionEnumTest'
    startEvent:
      topic: 'TestDataTypes/created'
      payload: { id: 'enumTest2', enum1: value2, enum2: value3 }
    expectedResult:
      status: 'success'
      variables:
        equalsValue1: <<undefined>>
        notEqualsValue1: true
        equals: <<undefined>>
