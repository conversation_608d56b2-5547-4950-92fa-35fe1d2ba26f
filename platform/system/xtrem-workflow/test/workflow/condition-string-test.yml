envConfigs:
  testActiveServiceOptions:
    - workflow
scenarios:
  Test str1 equals abc:
    workflow: 'conditionStringTest'
    startEvent:
      topic: 'TestDataTypes/created'
      payload: { id: 'stringEqualsAbc', str1: abc }
    expectedResult:
      status: 'success'
      variables:
        equalsAbc: true
        notEqualsAbc: <<undefined>>
        equals: <<undefined>>
        notEquals: true
        contains: true
        startsWith: true
        endsWith: true

  Test str1 not equals abc:
    workflow: 'conditionStringTest'
    startEvent:
      topic: 'TestDataTypes/created'
      payload: { id: 'stringNotEqualsAbc', str1: def }
    expectedResult:
      status: 'success'
      variables:
        equalsAbc: <<undefined>>
        notEqualsAbc: true
        equals: <<undefined>>
        notEquals: true
        contains: true # str2 is empty
        startsWith: true # str2 is empty
        endsWith: true # str2 is empty

  Test str1 equals str2:
    workflow: 'conditionStringTest'
    startEvent:
      topic: 'TestDataTypes/created'
      payload: { id: 'stringEquals', str1: foo, str2: foo }
    expectedResult:
      status: 'success'
      variables:
        equalsAbc: <<undefined>>
        notEqualsAbc: true
        equals: true
        notEquals: <<undefined>>
        contains: true
        startsWith: true
        endsWith: true

  Test str1 not equals str2:
    workflow: 'conditionStringTest'
    startEvent:
      topic: 'TestDataTypes/created'
      payload: { id: 'stringNotEquals', str1: foo, str2: bar }
    expectedResult:
      status: 'success'
      variables:
        equalsAbc: <<undefined>>
        notEqualsAbc: true
        equals: <<undefined>>
        notEquals: true
        contains: <<undefined>>
        startsWith: <<undefined>>
        endsWith: <<undefined>>

  Test str1 contains str2:
    workflow: 'conditionStringTest'
    startEvent:
      topic: 'TestDataTypes/created'
      payload: { id: 'stringContains', str1: foobarzoo, str2: bar }
    expectedResult:
      status: 'success'
      variables:
        contains: true

  Test str1 does not contain str2:
    workflow: 'conditionStringTest'
    startEvent:
      topic: 'TestDataTypes/created'
      payload: { id: 'stringDoesNotContain', str1: foobarzoo, str2: baa }
    expectedResult:
      status: 'success'
      variables:
        contains: <<undefined>>

  Test str1 starts with str2:
    workflow: 'conditionStringTest'
    startEvent:
      topic: 'TestDataTypes/created'
      payload: { id: 'stringStartsWith', str1: foobarzoo, str2: foo }
    expectedResult:
      status: 'success'
      variables:
        startsWith: true

  Test str1 does not start with str2:
    workflow: 'conditionStringTest'
    startEvent:
      topic: 'TestDataTypes/created'
      payload: { id: 'stringDoesNotStartWith', str1: foobarzoo, str2: bar }
    expectedResult:
      status: 'success'
      variables:
        startsWith: <<undefined>>

  Test str1 ends with str2:
    workflow: 'conditionStringTest'
    startEvent:
      topic: 'TestDataTypes/created'
      payload: { id: 'stringEndsWith', str1: foobarzoo, str2: zoo }
    expectedResult:
      status: 'success'
      variables:
        endsWith: true

  Test str1 does not end with str2:
    workflow: 'conditionStringTest'
    startEvent:
      topic: 'TestDataTypes/created'
      payload: { id: 'stringDoesNotEndWith', str1: foobarzoo, str2: bar }
    expectedResult:
      status: 'success'
      variables:
        endsWith: <<undefined>>
