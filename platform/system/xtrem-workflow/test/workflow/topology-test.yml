envConfigs:
  testActiveServiceOptions:
    - workflow
scenarios:
  Can execute a flow with a complex topology (v1, v2, v3, v4 all true):
    workflow: 'complexTopologyTest'
    startEvent:
      topic: 'WorkflowProcess/testStarted'
      payload:
        workflow: 'complexTopologyTest'
        parameters:
          v1: true
          v2: true
          v3: true
          v4: true
    expectedResult:
      status: 'success'
      variables:
        t1_t2: true
        t1_f2: <<undefined>>
        f1_t3: <<undefined>>
        f1_f3: <<undefined>>
        t1_f2_t4_or_f1_t3_t4: <<undefined>>
        t1_f2_f4_or_f1_t3: <<undefined>>

  Can execute a flow with a complex topology (v1, v2, v3, v4 all false):
    workflow: 'complexTopologyTest'
    startEvent:
      topic: 'WorkflowProcess/testStarted'
      payload:
        workflow: 'complexTopologyTest'
        parameters:
          v1: false
          v2: false
          v3: false
          v4: false
    expectedResult:
      status: 'success'
      variables:
        t1_t2: <<undefined>>
        t1_f2: <<undefined>>
        f1_t3: <<undefined>>
        f1_f3: true
        t1_f2_t4_or_f1_t3_t4: <<undefined>>
        t1_f2_f4_or_f1_t3: <<undefined>>

  Can execute a flow with a complex topology (v1 false, v2, v3, v4 all true):
    workflow: 'complexTopologyTest'
    startEvent:
      topic: 'WorkflowProcess/testStarted'
      payload:
        workflow: 'complexTopologyTest'
        parameters:
          v1: false
          v2: true
          v3: true
          v4: true
    expectedResult:
      status: 'success'
      variables:
        t1_t2: <<undefined>>
        t1_f2: <<undefined>>
        f1_t3: true
        f1_f3: <<undefined>>
        t1_f2_t4_or_f1_t3_t4: true
        t1_f2_f4_or_f1_t3: true

  Can execute a flow with a complex topology (v1 and v4 false, v2 and v3 true):
    workflow: 'complexTopologyTest'
    startEvent:
      topic: 'WorkflowProcess/testStarted'
      payload:
        workflow: 'complexTopologyTest'
        parameters:
          v1: false
          v2: true
          v3: true
          v4: false
    expectedResult:
      status: 'success'
      variables:
        t1_t2: <<undefined>>
        t1_f2: <<undefined>>
        f1_t3: true
        f1_f3: <<undefined>>
        t1_f2_t4_or_f1_t3_t4: <<undefined>>
        t1_f2_f4_or_f1_t3: true

  Can execute a flow with a complex topology (v3 true, v1, v2 and v4 false):
    workflow: 'complexTopologyTest'
    startEvent:
      topic: 'WorkflowProcess/testStarted'
      payload:
        workflow: 'complexTopologyTest'
        parameters:
          v1: false
          v2: false
          v3: true
          v4: false
    expectedResult:
      status: 'success'
      variables:
        t1_t2: <<undefined>>
        t1_f2: <<undefined>>
        f1_t3: true
        f1_f3: <<undefined>>
        t1_f2_t4_or_f1_t3_t4: <<undefined>>
        t1_f2_f4_or_f1_t3: true
