envConfigs:
  testActiveServiceOptions:
    - workflow
scenarios:
  Test bool1 equals true:
    workflow: 'conditionBooleanTest'
    startEvent:
      topic: 'TestDataTypes/created'
      payload: { id: 'bool1EqTrue', bool1: true }
    expectedResult:
      status: 'success'
      variables:
        bool1EqualsTrue: true
        bool1EqualsFalse: <<undefined>>
        bool1EqualsBool2: <<undefined>>
        bool1NotEqualsBool2: true
  Test bool1 equals false:
    workflow: 'conditionBooleanTest'
    startEvent:
      topic: 'TestDataTypes/created'
      payload: { id: 'bool1EqFalse', bool1: false }
    expectedResult:
      status: 'success'
      variables:
        bool1EqualsTrue: <<undefined>>
        bool1EqualsFalse: true
        bool1EqualsBool2: true
        bool1NotEqualsBool2: <<undefined>>
  Test bool1 equals bool2:
    workflow: 'conditionBooleanTest'
    startEvent:
      topic: 'TestDataTypes/created'
      payload: { id: 'bool1EqBool2', bool1: true, bool2: true }
    expectedResult:
      status: 'success'
      variables:
        bool1EqualsTrue: true
        bool1EqualsFalse: <<undefined>>
        bool1EqualsBool2: true
        bool1NotEqualsBool2: <<undefined>>
  Test bool1 not equals bool2:
    workflow: 'conditionBooleanTest'
    startEvent:
      topic: 'TestDataTypes/created'
      payload: { id: 'bool1NotEqBool2', bool1: false, bool2: true }
    expectedResult:
      status: 'success'
      variables:
        bool1EqualsTrue: <<undefined>>
        bool1EqualsFalse: true
        bool1EqualsBool2: <<undefined>>
        bool1NotEqualsBool2: true
