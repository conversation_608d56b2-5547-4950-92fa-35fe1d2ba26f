envConfigs:
  testActiveServiceOptions:
    - workflow
scenarios:
  Can read a user with a valid userId:
    workflow: 'readEntityActionTest'
    startEvent:
      topic: 'WorkflowProcess/testStarted'
      payload:
        workflow: 'readEntityActionTest'
        parameters: { userId: '<EMAIL>' }
    expectedResult:
      status: 'success'
      variables:
        user:
          email: '<EMAIL>'
          firstName: 'Jack'
          lastName: 'Sparrow'
          preferences: null

  Fails if reading a user with an invalid userId and failIfNotFound is true:
    workflow: 'readEntityActionTest'
    startEvent:
      topic: 'WorkflowProcess/testStarted'
      payload:
        workflow: 'readEntityActionTest'
        parameters: { userId: 'bad' }
    expectedResult:
      status: 'error'
      errors: [{ message: "record with key 'bad' not found", stepId: 'read-entity-1' }]

  Returns null if reading a user with an invalid userId and failIfNotFound is false:
    workflow: 'readEntityActionTestWithNull'
    startEvent:
      topic: 'WorkflowProcess/testStarted'
      payload:
        workflow: 'readEntityActionTestWithNull'
        parameters: { userId: 'bad' }
    expectedResult:
      status: 'success'
      variables:
        user: null
