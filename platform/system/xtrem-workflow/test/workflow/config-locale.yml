envConfigs:
  testActiveServiceOptions:
    - workflow

scenarios:
  Test localized message (fr-FR):
    workflow: 'configLocaleTest'
    envConfigs:
      locale: 'fr-FR'
    startEvent:
      topic: 'TestConfigLocale/created'
      payload: { val: 2 }
    expectedResult:
      status: 'error'
      errors:
        - stepId: 'update-entity-1'
          message: "~Message d'erreur localisé."

  Test localized message (en-US):
    workflow: 'configLocaleTest'
    envConfigs:
      locale: 'en-US'
    startEvent:
      topic: 'TestConfigLocale/created'
      payload: { val: 2 }
    expectedResult:
      status: 'error'
      errors:
        - stepId: 'update-entity-1'
          message: '~Localized error message.'
