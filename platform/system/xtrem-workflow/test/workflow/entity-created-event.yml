envConfigs:
  testActiveServiceOptions:
    - workflow
scenarios:
  Can trigger a workflow, select the data and filter successfully:
    workflow: 'entityCreatedEvent'
    startEvent:
      topic: 'TestPerson/created'
      payload: { name: '<PERSON>', age: 30, pet: { name: '<PERSON><PERSON><PERSON>', type: 'cat' } }
    expectedResult:
      status: 'success'
      variables:
        # workflow does not select pet.type
        testPerson: { name: '<PERSON>', age: 30, pet: { name: '<PERSON><PERSON><PERSON>' } }
