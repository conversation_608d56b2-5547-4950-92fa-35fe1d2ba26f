envConfigs:
  testActiveServiceOptions:
    - workflow
scenarios:
  Propagates to true branch with true conditions:
    workflow: 'conditionActionTest'
    startEvent:
      topic: 'WorkflowProcess/testStarted'
      payload:
        workflow: 'conditionActionTest'
        parameters: { p1: 4, p2: 18 }
    expectedResult:
      status: 'success'
      variables: { yep: true }

  Propagates to false branch with first condition false:
    workflow: 'conditionActionTest'
    startEvent:
      topic: 'WorkflowProcess/testStarted'
      payload:
        workflow: 'conditionActionTest'
        parameters: { p1: 19, p2: 18 }
    expectedResult:
      status: 'success'
      variables: { nope: true }

  Propagates to false branch with second condition false:
    workflow: 'conditionActionTest'
    startEvent:
      topic: 'WorkflowProcess/testStarted'
      payload:
        workflow: 'conditionActionTest'
        parameters: { p1: 4, p2: 23 }
    expectedResult:
      status: 'success'
      variables: { nope: true }
