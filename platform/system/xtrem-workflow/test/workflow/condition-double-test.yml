envConfigs:
  testActiveServiceOptions:
    - workflow
scenarios:
  Test 1.5, 1.5, 2.5 (equals true, between true):
    workflow: 'conditionDoubleTest'
    startEvent:
      topic: 'TestDataTypes/created'
      payload: { id: 'doubleTest1', double1: 1.5, double2: 1.5, double3: 2.5 }
    expectedResult:
      status: 'success'
      variables:
        equals: true
        notEquals: <<undefined>>
        lessThan: <<undefined>>
        lessThanOrEqual: true
        greaterThan: <<undefined>>
        greaterThanOrEqual: true
        between: true

  Test 2.5, 1, 2 (equals false, between false):
    workflow: 'conditionDoubleTest'
    startEvent:
      topic: 'TestDataTypes/created'
      payload: { id: 'doubleTest2', double1: 2.5, double2: 1, double3: 2 }
    expectedResult:
      status: 'success'
      variables:
        equals: <<undefined>>
        notEquals: true
        lessThan: <<undefined>>
        lessThanOrEqual: <<undefined>>
        greaterThan: true
        greaterThanOrEqual: true
        between: <<undefined>>

  Test 2.5, 1, 2.5 (equals false, between true):
    workflow: 'conditionDoubleTest'
    startEvent:
      topic: 'TestDataTypes/created'
      payload: { id: 'doubleTest3', double1: 2.5, double2: 1, double3: 2.5 }
    expectedResult:
      status: 'success'
      variables:
        equals: <<undefined>>
        notEquals: true
        lessThan: <<undefined>>
        lessThanOrEqual: <<undefined>>
        greaterThan: true
        greaterThanOrEqual: true
        between: true
