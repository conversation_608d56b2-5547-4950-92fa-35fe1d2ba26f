envConfigs:
  testActiveServiceOptions:
    - workflow
scenarios:
  Can add 2 integers:
    workflow: 'calculateTest'
    startEvent:
      topic: 'WorkflowProcess/testStarted'
      payload:
        workflow: 'calculateTest'
        parameters:
          p1: 7
          p2: 4
    expectedResult:
      status: 'success'
      variables:
        p1Plus10: 17
        p1PlusP2: 11
        p1MinusP2: 3
        p1MultP2: 28
        p1DivP2: 1.75
        fourOps: 14

  Fails if dividing by zero:
    workflow: 'calculateTest'
    startEvent:
      topic: 'WorkflowProcess/testStarted'
      payload:
        workflow: 'calculateTest'
        parameters:
          p1: 7
          p2: 0
    expectedResult:
      status: 'error'
      variables:
        p1Plus10: 17
        p1PlusP2: 7
        p1MinusP2: 7
        p1MultP2: 0
        p1DivP2: <<undefined>>
        fourOps: <<undefined>>
      errors:
        - stepId: 'calculate-5'
          message: 'Division by zero'
        - stepId: 'calculate-6'
          message: 'Division by zero'

  Fails if adding an integer and a string:
    workflow: 'calculateTest'
    startEvent:
      topic: 'WorkflowProcess/testStarted'
      payload:
        workflow: 'calculateTest'
        parameters:
          p1: 7
          p2: 'hello'
    expectedResult:
      status: 'error'
      variables:
        p1Plus10: 17
        p1PlusP2: <<undefined>>
        p1MinusP2: <<undefined>>
        p1MultP2: <<undefined>>
        p1DivP2: <<undefined>>
        fourOps: <<undefined>>

      errors:
        - stepId: 'calculate-2'
          message: "Step calculateTest.calculate-2: test.parameters.p2: Expected Decimal, got string 'hello'"
        - stepId: 'calculate-3'
          message: "Step calculateTest.calculate-3: test.parameters.p2: Expected Decimal, got string 'hello'"
        - stepId: 'calculate-4'
          message: "Step calculateTest.calculate-4: test.parameters.p2: Expected Decimal, got string 'hello'"
        - stepId: 'calculate-5'
          message: "Step calculateTest.calculate-5: test.parameters.p2: Expected Decimal, got string 'hello'"
        - stepId: 'calculate-6'
          message: "Step calculateTest.calculate-6: test.parameters.p2: Expected Decimal, got string 'hello'"
