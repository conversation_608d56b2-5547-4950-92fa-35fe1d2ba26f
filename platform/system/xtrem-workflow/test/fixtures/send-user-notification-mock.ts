import { assertDeepPartialMatch, Context, WorkflowMock } from '@sage/xtrem-core';
import { Logger } from '@sage/xtrem-log';
import { assert } from 'chai';
import * as sinon from 'sinon';

export const logger = Logger.getLogger(__filename, 'test-mailer-mock');

const sandbox = sinon.createSandbox();

export class UserNotificationMock extends WorkflowMock {
    async execute<T>(body: () => Promise<T>): Promise<T> {
        const stub = sandbox.stub(Context.prototype, 'notifyUser').callsFake(notification => {
            assert.equal(this.options.calls.length, 1);
            const call = this.options.calls[0];
            try {
                assertDeepPartialMatch(notification, call.input);
                return Promise.resolve();
            } catch (error) {
                // eslint-disable-next-line no-console
                console.log(error.message);
                return Promise.reject(error);
            }
        });
        try {
            return await body();
        } finally {
            stub.restore();
        }
    }
}
