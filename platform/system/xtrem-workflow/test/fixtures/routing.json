{"@sage/xtrem-workflow": [{"topic": "TestPerson/created", "queue": "workflow", "sourceFileName": "test/fixtures/lib/nodes/test-person.ts"}, {"topic": "TestPerson/updated", "queue": "workflow", "sourceFileName": "test/fixtures/lib/nodes/test-person.ts"}, {"topic": "TestDataTypes/created", "queue": "workflow", "sourceFileName": "test/fixtures/lib/nodes/test-data-types.ts"}, {"topic": "TestConfigLocale/created", "queue": "workflow", "sourceFileName": "test/fixtures/lib/nodes/test-config-locale.ts"}, {"topic": "TestDataTypes/updated", "queue": "workflow", "sourceFileName": "test/fixtures/lib/nodes/test-data-types.ts"}, {"topic": "TestNodeForMutation/created", "queue": "workflow", "sourceFileName": "test/fixtures/lib/nodes/test-node-for-mutation.ts"}]}