import { Node, decorators, integer } from '@sage/xtrem-core';
import { ValidationSeverity } from '@sage/xtrem-shared';

@decorators.node<TestConfigLocale>({
    storage: 'sql',
    isPublished: true,
    canUpdate: true,
    isCustomizable: true,
    notifies: ['created', 'updated', 'deleted'],
    async controlBegin(cx) {
        if ((await this.val) === 3) {
            cx.addDiagnose(
                ValidationSeverity.error,
                cx.localize('@sage/xtrem-workflow/nodes__test_config_locale_error_message', 'Localized error message.'),
            );
        }
    },
})
export class TestConfigLocale extends Node {
    @decorators.integerProperty<TestConfigLocale, 'val'>({
        isPublished: true,
        isStored: true,
    })
    readonly val: Promise<integer>;
}
