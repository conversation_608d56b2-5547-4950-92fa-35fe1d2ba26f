import { Node, Reference, decorators, integer } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import { TestPet } from './test-pet';

@decorators.node<TestPerson>({
    storage: 'sql',
    isPublished: true,
    notifies: ['created', 'updated', 'deleted'],
    indexes: [{ orderBy: { name: 1 }, isUnique: true, isNaturalKey: true }],
})
export class TestPerson extends Node {
    @decorators.stringProperty<TestPerson, 'name'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.name,
    })
    readonly name: Promise<string>;

    @decorators.integerProperty<TestPerson, 'age'>({
        isStored: true,
        isPublished: true,
    })
    readonly age: Promise<integer>;

    @decorators.referenceProperty<TestPerson, 'pet'>({
        isVital: true,
        isPublished: true,
        isNullable: true,
        node: () => TestPet,
        reverseReference: 'owner',
    })
    readonly pet: Reference<TestPet>;
}
