import { Context, Node, Reference, decorators, integer } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremTest from '../index';

/**
 * This node is used to test the mutation action
 */
@decorators.node<TestNodeForMutation>({
    storage: 'sql',
    isPublished: true,
    notifies: ['created', 'updated', 'deleted'],
    indexes: [{ orderBy: { id: 1 }, isUnique: true, isNaturalKey: true }],
})
export class TestNodeForMutation extends Node {
    @decorators.stringProperty<TestNodeForMutation, 'id'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.name,
    })
    readonly id: Promise<string>;

    @decorators.stringProperty<TestNodeForMutation, 'valStr'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.name,
    })
    readonly valStr: Promise<string>;

    @decorators.integerProperty<TestNodeForMutation, 'valInt'>({
        isStored: true,
        isPublished: true,
    })
    readonly valInt: Promise<integer>;

    @decorators.booleanProperty<TestNodeForMutation, 'valBool'>({
        isPublished: true,
        isStored: true,
    })
    readonly valBool: Promise<boolean>;

    @decorators.enumProperty<TestNodeForMutation, 'valEnum'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        dataType: () => xtremTest.enums.testEnumDataType,
    })
    readonly valEnum: Promise<xtremTest.enums.TestEnum>;

    /**
     * A test mutation
     */
    @decorators.mutation<typeof TestNodeForMutation, 'testMutation'>({
        isPublished: true,
        parameters: [
            {
                name: 'nodeToUpdate',
                type: 'reference',
                node: () => TestNodeForMutation,
                isWritable: true,
            },
            {
                name: 'intStep',
                type: 'integer',
                isMandatory: true,
            },
            {
                name: 'strPrefix',
                type: 'string',
                isMandatory: true,
            },
            {
                name: 'newEnumVal',
                type: 'enum',
                dataType: () => xtremTest.enums.testEnumDataType,
                isMandatory: true,
            },
        ],
        return: {
            type: 'reference',
            node: () => TestNodeForMutation,
        },
    })
    static async testMutation(
        _context: Context,
        nodeToUpdate: TestNodeForMutation,
        intStep: integer,
        strPrefix: string,
        newEnumVal: xtremTest.enums.TestEnum,
    ): Reference<TestNodeForMutation> {
        await nodeToUpdate.$.set({
            valInt: (await nodeToUpdate.valInt) + intStep,
            valStr: `${strPrefix}${await nodeToUpdate.valStr}`,
            valEnum: newEnumVal,
            valBool: !(await nodeToUpdate.valBool),
        });
        await nodeToUpdate.$.save();
        return nodeToUpdate;
    }
}
