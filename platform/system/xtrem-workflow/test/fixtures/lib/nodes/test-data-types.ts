import { Node, Time, date, datetime, decimal, decorators, integer } from '@sage/xtrem-core';
import * as xtremTest from '../index';

@decorators.node<TestDataTypes>({
    storage: 'sql',
    isPublished: true,
    canUpdate: true,
    isCustomizable: true,
    notifies: ['created', 'updated', 'deleted'],
    indexes: [{ orderBy: { id: 1 }, isUnique: true, isNaturalKey: true }],
})
export class TestDataTypes extends Node {
    @decorators.stringProperty<TestDataTypes, 'id'>({
        isPublished: true,
        isStored: true,
        dataType: () => xtremTest.dataTypes.testStringDataType,
    })
    readonly id: Promise<string>;

    @decorators.booleanProperty<TestDataTypes, 'bool1'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly bool1: Promise<boolean | null>;

    @decorators.booleanProperty<TestDataTypes, 'bool2'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly bool2: Promise<boolean | null>;

    @decorators.stringProperty<TestDataTypes, 'str1'>({
        isPublished: true,
        isStored: true,
        dataType: () => xtremTest.dataTypes.testStringDataType,
    })
    readonly str1: Promise<string>;

    @decorators.stringProperty<TestDataTypes, 'str2'>({
        isPublished: true,
        isStored: true,
        dataType: () => xtremTest.dataTypes.testStringDataType,
    })
    readonly str2: Promise<string>;

    @decorators.enumProperty<TestDataTypes, 'enum1'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        dataType: () => xtremTest.enums.testEnumDataType,
    })
    readonly enum1: Promise<xtremTest.enums.TestEnum | null>;

    @decorators.enumProperty<TestDataTypes, 'enum2'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        dataType: () => xtremTest.enums.testEnumDataType,
    })
    readonly enum2: Promise<xtremTest.enums.TestEnum | null>;

    @decorators.enumProperty<TestDataTypes, 'enum3'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        dataType: () => xtremTest.enums.testEnumDataType,
    })
    readonly enum3: Promise<xtremTest.enums.TestEnum | null>;

    @decorators.integerProperty<TestDataTypes, 'int1'>({
        isPublished: true,
        isStored: true,
    })
    readonly int1: Promise<integer>;

    @decorators.integerProperty<TestDataTypes, 'int2'>({
        isPublished: true,
        isStored: true,
    })
    readonly int2: Promise<integer>;

    @decorators.integerProperty<TestDataTypes, 'int3'>({
        isPublished: true,
        isStored: true,
    })
    readonly int3: Promise<integer>;

    @decorators.decimalProperty<TestDataTypes, 'decimal1'>({
        isPublished: true,
        isStored: true,
        dataType: () => xtremTest.dataTypes.testDecimalDataType,
    })
    readonly decimal1: Promise<decimal>;

    @decorators.decimalProperty<TestDataTypes, 'decimal2'>({
        isPublished: true,
        isStored: true,
        dataType: () => xtremTest.dataTypes.testDecimalDataType,
    })
    readonly decimal2: Promise<decimal>;

    @decorators.decimalProperty<TestDataTypes, 'decimal3'>({
        isPublished: true,
        isStored: true,
        dataType: () => xtremTest.dataTypes.testDecimalDataType,
    })
    readonly decimal3: Promise<decimal>;

    @decorators.doubleProperty<TestDataTypes, 'double1'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly double1: Promise<number>;

    @decorators.doubleProperty<TestDataTypes, 'double2'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly double2: Promise<number>;

    @decorators.doubleProperty<TestDataTypes, 'double3'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly double3: Promise<number>;

    @decorators.dateProperty<TestDataTypes, 'date1'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly date1: Promise<date | null>;

    @decorators.dateProperty<TestDataTypes, 'date2'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly date2: Promise<date | null>;

    @decorators.dateProperty<TestDataTypes, 'date3'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly date3: Promise<date | null>;

    @decorators.datetimeProperty<TestDataTypes, 'datetime1'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly datetime1: Promise<datetime | null>;

    @decorators.datetimeProperty<TestDataTypes, 'datetime2'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly datetime2: Promise<datetime | null>;

    @decorators.datetimeProperty<TestDataTypes, 'datetime3'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly datetime3: Promise<datetime | null>;

    @decorators.timeProperty<TestDataTypes, 'time1'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly time1: Promise<Time | null>;

    @decorators.timeProperty<TestDataTypes, 'time2'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly time2: Promise<Time | null>;

    @decorators.timeProperty<TestDataTypes, 'time3'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly time3: Promise<Time | null>;
}
