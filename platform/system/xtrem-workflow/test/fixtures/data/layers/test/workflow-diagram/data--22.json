{"edges": [{"id": "entity-created-1--out-1", "source": "entity-created-1", "target": "update-entity-1", "sourceHandle": "out"}], "nodes": [{"id": "entity-created-1", "data": {"localizedTitle": {"base": "Test Config Locale created"}, "topic": "TestConfigLocale/created", "subtitle": "@sage/xtrem-workflow/TestConfigLocale", "conditions": [], "entityName": "@sage/xtrem-workflow/TestConfigLocale", "stepVariables": [{"path": "testConfigLocale.val", "type": "Int", "title": "Val"}, {"node": "TestConfigLocale", "path": "testConfigLocale._id", "type": "IntReference", "title": "Test config locale id"}]}, "type": "entity-created/_generic", "width": 280, "height": 98, "origin": [0.5, 0], "position": {"x": 20, "y": 20}, "selected": false}, {"id": "update-entity-1", "data": {"localizedTitle": {"base": "Update value and raise localized error"}, "subtitle": null, "entityName": "@sage/xtrem-workflow/TestConfigLocale", "updateRules": [{"sourceValue": "3", "destinationPath": "val"}], "pathToUpdate": "testConfigLocale._id", "stepVariables": [{"path": "testConfigLocale.val", "type": "Int", "title": "Val"}]}, "type": "update-entity/_generic", "width": 280, "height": 68, "origin": [0.5, 0], "dragging": false, "position": {"x": 20, "y": 220}, "selected": false}]}