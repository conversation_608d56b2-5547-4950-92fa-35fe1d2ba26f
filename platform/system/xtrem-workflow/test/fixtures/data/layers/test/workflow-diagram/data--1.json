{"edges": [{"id": "test-started-1--out-1", "source": "test-started-1", "target": "calculate-1", "sourceHandle": "out"}, {"id": "calculate-1--out-1", "source": "calculate-1", "target": "test-stub-1", "sourceHandle": "out"}, {"id": "test-started-1--out-2", "source": "test-started-1", "target": "calculate-2", "sourceHandle": "out"}, {"id": "calculate-2--out-1", "source": "calculate-2", "target": "test-stub-2", "sourceHandle": "out"}, {"id": "test-started-1--out-3", "source": "test-started-1", "target": "calculate-3", "sourceHandle": "out"}, {"id": "calculate-3--out-1", "source": "calculate-3", "target": "test-stub-3", "sourceHandle": "out"}, {"id": "test-started-1--out-4", "source": "test-started-1", "target": "calculate-4", "sourceHandle": "out"}, {"id": "calculate-4--out-1", "source": "calculate-4", "target": "test-stub-4", "sourceHandle": "out"}, {"id": "test-started-1--out-5", "source": "test-started-1", "target": "calculate-5", "sourceHandle": "out"}, {"id": "calculate-5--out-1", "source": "calculate-5", "target": "test-stub-5", "sourceHandle": "out"}, {"id": "test-started-1--out-6", "source": "test-started-1", "target": "calculate-6", "sourceHandle": "out"}, {"id": "calculate-6--out-1", "source": "calculate-6", "target": "test-stub-6", "sourceHandle": "out"}], "nodes": [{"id": "test-started-1", "data": {"topic": "WorkflowProcess/testStarted", "parameters": [{"name": "p1", "type": "Decimal"}, {"name": "p2", "type": "Decimal"}], "stepVariables": [{"path": "test.parameters.p1", "type": "Decimal", "title": "P 1"}, {"path": "test.parameters.p2", "type": "Decimal", "title": "P 2"}]}, "type": "test-started/_generic", "width": 280, "height": 62, "origin": [0.5, 0], "dragging": false, "position": {"x": 740, "y": -60}, "selected": false, "positionAbsolute": {"x": 740, "y": -60}}, {"id": "calculate-1", "data": {"localizedTitle": {"base": "p1 + 10"}, "subtitle": null, "stepVariables": [{"path": "p1Plus10", "type": "Float", "title": "Calculation result ({p1 + 10})"}], "calculationSteps": [{"value": null, "variable": "test.parameters.p1", "isVariable": true}, {"value": 10, "operation": "add", "isVariable": false}], "outputVariableName": "p1Plus10"}, "type": "calculate/_generic", "width": 280, "height": 62, "origin": [0.5, 0], "dragging": false, "position": {"x": 60, "y": 140}, "selected": false, "positionAbsolute": {"x": 60, "y": 140}}, {"id": "test-stub-1", "data": {"localizedTitle": {"base": "plus10 done"}, "subtitle": "", "stepVariables": [{"path": "plus10Done", "type": "Float", "title": "End test result ({plus10 done})"}], "outputVariableName": "plus10Done"}, "type": "test-stub/_generic", "width": 280, "height": 62, "origin": [0.5, 0], "dragging": false, "position": {"x": 60, "y": 240}, "selected": false, "positionAbsolute": {"x": 60, "y": 240}}, {"id": "calculate-2", "data": {"localizedTitle": {"base": "p1 + p2"}, "subtitle": null, "stepVariables": [{"path": "p1PlusP2", "type": "Float", "title": "Calculation result ({p1 + p2})"}], "calculationSteps": [{"variable": "test.parameters.p1", "isVariable": true}, {"value": null, "variable": "test.parameters.p2", "operation": "add", "isVariable": true}], "outputVariableName": "p1PlusP2"}, "type": "calculate/_generic", "width": 280, "height": 62, "origin": [0.5, 0], "dragging": false, "position": {"x": 400, "y": 140}, "selected": false, "positionAbsolute": {"x": 400, "y": 140}}, {"id": "test-stub-2", "data": {"localizedTitle": {"base": "plus done"}, "subtitle": null, "stepVariables": [{"path": "plusDone", "type": "Float", "title": "End test result ({plus done})"}], "outputVariableName": "plusDone"}, "type": "test-stub/_generic", "width": 280, "height": 62, "origin": [0.5, 0], "dragging": false, "position": {"x": 400, "y": 240}, "selected": false, "positionAbsolute": {"x": 400, "y": 240}}, {"id": "calculate-3", "data": {"localizedTitle": {"base": "p1 - p2"}, "subtitle": null, "stepVariables": [{"path": "p1MinusP2", "type": "Float", "title": "Calculation result ({p1 - p2})"}], "calculationSteps": [{"variable": "test.parameters.p1", "isVariable": true}, {"variable": "test.parameters.p2", "operation": "subtract", "isVariable": true}], "outputVariableName": "p1MinusP2"}, "type": "calculate/_generic", "width": 280, "height": 62, "origin": [0.5, 0], "dragging": false, "position": {"x": 740, "y": 140}, "selected": false, "positionAbsolute": {"x": 740, "y": 140}}, {"id": "test-stub-3", "data": {"localizedTitle": {"base": "minus done"}, "subtitle": null, "stepVariables": [{"path": "minusDone", "type": "Float", "title": "End test result ({minus done})"}], "outputVariableName": "minusDone"}, "type": "test-stub/_generic", "width": 280, "height": 62, "origin": [0.5, 0], "dragging": false, "position": {"x": 740, "y": 240}, "selected": false, "positionAbsolute": {"x": 740, "y": 240}}, {"id": "calculate-4", "data": {"localizedTitle": {"base": "p1 * p2"}, "subtitle": null, "stepVariables": [{"path": "p1MultP2", "type": "Float", "title": "Calculation result ({p1 * p2})"}], "calculationSteps": [{"variable": "test.parameters.p1", "isVariable": true}, {"variable": "test.parameters.p2", "operation": "multiply", "isVariable": true}], "outputVariableName": "p1MultP2"}, "type": "calculate/_generic", "width": 280, "height": 62, "origin": [0.5, 0], "dragging": false, "position": {"x": 1060, "y": 140}, "selected": false, "positionAbsolute": {"x": 1060, "y": 140}}, {"id": "test-stub-4", "data": {"localizedTitle": {"base": "mult done"}, "subtitle": null, "stepVariables": [{"path": "multDone", "type": "Float", "title": "End test result ({mult done})"}], "outputVariableName": "multDone"}, "type": "test-stub/_generic", "width": 280, "height": 62, "origin": [0.5, 0], "dragging": false, "position": {"x": 1060, "y": 240}, "selected": false, "positionAbsolute": {"x": 1060, "y": 240}}, {"id": "calculate-5", "data": {"localizedTitle": {"base": "p1 / p2"}, "subtitle": null, "stepVariables": [{"path": "p1DivP2", "type": "Float", "title": "Calculation result ({p1 / p2})"}], "calculationSteps": [{"value": null, "variable": "test.parameters.p1", "isVariable": true}, {"variable": "test.parameters.p2", "operation": "divide", "isVariable": true}], "outputVariableName": "p1DivP2"}, "type": "calculate/_generic", "width": 280, "height": 62, "origin": [0.5, 0], "dragging": false, "position": {"x": 1380, "y": 140}, "selected": false, "positionAbsolute": {"x": 1380, "y": 140}}, {"id": "test-stub-5", "data": {"localizedTitle": {"base": "div done"}, "subtitle": null, "stepVariables": [{"path": "divDone", "type": "Float", "title": "End test result ({div done})"}], "outputVariableName": "divDone"}, "type": "test-stub/_generic", "width": 280, "height": 62, "origin": [0.5, 0], "dragging": false, "position": {"x": 1380, "y": 240}, "selected": false, "positionAbsolute": {"x": 1380, "y": 240}}, {"id": "calculate-6", "data": {"localizedTitle": {"base": "(((p1 + 10) - 3) * 4) / p2"}, "subtitle": null, "stepVariables": [{"path": "fourOps", "type": "Float", "title": "Calculation result ({(((p1 + 10) - 3) * 4) / p2})"}], "calculationSteps": [{"variable": "test.parameters.p1", "isVariable": true}, {"value": 10, "operation": "add"}, {"value": 3, "operation": "subtract"}, {"value": 4, "operation": "multiply"}, {"variable": "test.parameters.p2", "operation": "divide", "isVariable": true}], "outputVariableName": "fourOps"}, "type": "calculate/_generic", "width": 280, "height": 62, "origin": [0.5, 0], "dragging": false, "position": {"x": 560, "y": 360}, "selected": false, "positionAbsolute": {"x": 560, "y": 360}}, {"id": "test-stub-6", "data": {"localizedTitle": {"base": "four ops done"}, "subtitle": null, "stepVariables": [{"path": "fourOpsDone", "type": "Float", "title": "End test result ({four ops done})"}], "outputVariableName": "fourOpsDone"}, "type": "test-stub/_generic", "width": 280, "height": 62, "origin": [0.5, 0], "dragging": false, "position": {"x": 560, "y": 460}, "selected": false, "positionAbsolute": {"x": 560, "y": 460}}]}