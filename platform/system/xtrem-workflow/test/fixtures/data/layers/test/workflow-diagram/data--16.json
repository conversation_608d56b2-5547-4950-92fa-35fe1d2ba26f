{"edges": [{"id": "test-started-1--out-1", "source": "test-started-1", "target": "test-stub-1", "sourceHandle": "out"}, {"id": "test-started-1--out-2", "source": "test-started-1", "target": "test-stub-2", "sourceHandle": "out"}, {"id": "test-stub-1--out-1", "source": "test-stub-1", "target": "test-stub-3", "sourceHandle": "out"}, {"id": "test-stub-2--out-1", "source": "test-stub-2", "target": "test-stub-3", "sourceHandle": "out"}], "nodes": [{"id": "test-started-1", "data": {"topic": "WorkflowProcess/testStarted", "parameters": [], "stepVariables": [], "outputVariableName": "test"}, "type": "test-started/_generic", "width": 280, "height": 62, "origin": [0.5, 0], "dragging": false, "position": {"x": 420, "y": 20}, "selected": false, "positionAbsolute": {"x": 420, "y": 20}}, {"id": "test-stub-1", "data": {"localizedTitle": {"base": "branch1"}, "subtitle": null, "stepVariables": [{"path": "branch1", "type": "Float", "title": "End test result ({branch1})"}], "outputVariableName": "branch1"}, "type": "test-stub/_generic", "width": 280, "height": 62, "origin": [0.5, 0], "dragging": false, "position": {"x": 220, "y": 160}, "selected": false, "positionAbsolute": {"x": 220, "y": 160}}, {"id": "test-stub-2", "data": {"localizedTitle": {"base": "branch2"}, "subtitle": null, "stepVariables": [{"path": "branch2", "type": "Float", "title": "End test result ({branch2})"}], "outputVariableName": "branch2"}, "type": "test-stub/_generic", "width": 280, "height": 62, "origin": [0.5, 0], "dragging": false, "position": {"x": 620, "y": 160}, "selected": false, "positionAbsolute": {"x": 620, "y": 160}}, {"id": "test-stub-3", "data": {"localizedTitle": {"base": "join"}, "subtitle": null, "stepVariables": [{"path": "join", "type": "Float", "title": "End test result ({join})"}], "outputVariableName": "join"}, "type": "test-stub/_generic", "width": 280, "height": 62, "origin": [0.5, 0], "dragging": false, "position": {"x": 420, "y": 320}, "selected": false, "positionAbsolute": {"x": 420, "y": 320}}]}