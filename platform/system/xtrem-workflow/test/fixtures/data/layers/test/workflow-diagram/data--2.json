{"edges": [], "nodes": [{"id": "entity-created-1", "data": {"localizedTitle": {"base": "Test Person created"}, "topic": "TestPerson/created", "subtitle": "@sage/xtrem-workflow/TestPerson", "conditions": [{"path": "testPerson.age", "value": "40", "operator": "lessThan", "useParameter": false}], "entityName": "@sage/xtrem-workflow/TestPerson", "stepVariables": [{"path": "testPerson.age", "type": "Int", "title": "Test Person / Age"}, {"path": "testPerson.name", "type": "String", "title": "Test Person / Name"}, {"path": "testPerson.pet.name", "type": "String", "title": "Test Person / Pet / Name"}]}, "type": "entity-created/_generic", "width": 257, "height": 69, "origin": [0.5, 0], "dragging": true, "position": {"x": 453, "y": 40}, "selected": true, "positionAbsolute": {"x": 453, "y": 40}}]}