{"edges": [{"id": "test-started-1--out-1", "source": "test-started-1", "target": "condition-1", "sourceHandle": "out"}, {"id": "condition-1--out-true-1", "source": "condition-1", "target": "test-stub-1", "sourceHandle": "out-true"}, {"id": "condition-1--out-false-1", "source": "condition-1", "target": "test-stub-2", "sourceHandle": "out-false"}, {"id": "test-stub-1--out-1", "source": "test-stub-1", "target": "test-stub-3", "sourceHandle": "out"}, {"id": "test-stub-2--out-1", "source": "test-stub-2", "target": "test-stub-3", "sourceHandle": "out"}], "nodes": [{"id": "test-started-1", "data": {"topic": "WorkflowProcess/testStarted", "parameters": [{"name": "p1", "type": "Boolean"}], "stepVariables": [{"path": "test.parameters.p1", "type": "Boolean", "title": "P 1"}]}, "type": "test-started/_generic", "width": 280, "height": 62, "origin": [0.5, 0], "dragging": false, "position": {"x": 500, "y": 20}, "selected": false, "positionAbsolute": {"x": 500, "y": 20}}, {"id": "condition-1", "data": {"localizedTitle": {"base": "p1?"}, "subtitle": null, "conditions": [{"path": "test.parameters.p1", "value": "true", "operator": "matches", "useParameter": false}], "stepVariables": []}, "type": "condition/_generic", "width": 120, "height": 80, "origin": [0.5, 0], "dragging": false, "position": {"x": 580, "y": 140}, "selected": false, "positionAbsolute": {"x": 580, "y": 140}}, {"id": "test-stub-1", "data": {"localizedTitle": {"base": "p1True"}, "subtitle": null, "stepVariables": [{"path": "p1True", "type": "Float", "title": "End test result ({p1True})"}], "outputVariableName": "p1True"}, "type": "test-stub/_generic", "width": 280, "height": 62, "origin": [0.5, 0], "dragging": false, "position": {"x": 240, "y": 240}, "selected": false, "positionAbsolute": {"x": 240, "y": 240}}, {"id": "test-stub-2", "data": {"localizedTitle": {"base": "p1False"}, "subtitle": null, "stepVariables": [{"path": "p1False", "type": "Float", "title": "End test result ({p1False})"}], "outputVariableName": "p1False"}, "type": "test-stub/_generic", "width": 280, "height": 62, "origin": [0.5, 0], "dragging": false, "position": {"x": 780, "y": 240}, "selected": false, "positionAbsolute": {"x": 780, "y": 240}}, {"id": "test-stub-3", "data": {"localizedTitle": {"base": "join"}, "subtitle": null, "stepVariables": [{"path": "join", "type": "Float", "title": "End test result ({join})"}], "outputVariableName": "join"}, "type": "test-stub/_generic", "width": 280, "height": 62, "origin": [0.5, 0], "dragging": false, "position": {"x": 500, "y": 420}, "selected": false, "positionAbsolute": {"x": 500, "y": 420}}]}