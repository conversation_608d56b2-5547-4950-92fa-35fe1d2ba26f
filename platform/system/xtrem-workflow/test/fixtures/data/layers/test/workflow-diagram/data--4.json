{"edges": [{"id": "test-started-1--out-1", "source": "test-started-1", "target": "read-entity-1", "sourceHandle": "out"}, {"id": "read-entity-1--out-1", "source": "read-entity-1", "target": "test-stub-1", "sourceHandle": "out"}], "nodes": [{"id": "test-started-1", "data": {"localizedTitle": {"base": "Start test"}, "topic": "WorkflowProcess/testStarted", "parameters": [{"name": "userId", "type": "String"}], "stepVariables": [{"path": "test.parameters.userId", "type": "String", "title": "User id"}]}, "type": "test-started/_generic", "width": 280, "height": 62, "origin": [0.5, 0], "dragging": false, "position": {"x": 520, "y": 20}, "selected": false, "positionAbsolute": {"x": 520, "y": 20}}, {"id": "read-entity-1", "data": {"title": null, "subtitle": "@sage/xtrem-system/User", "recordKey": "{{test.parameters.userId}}", "entityName": "@sage/xtrem-system/User", "failIfNotFound": true, "stepVariables": [{"path": "user.email", "type": "String", "title": "Email"}, {"path": "user.firstName", "type": "String", "title": "First name"}, {"path": "user.lastName", "type": "String", "title": "Last name"}], "outputVariableName": "user"}, "type": "read-entity/_generic", "width": 280, "height": 69, "origin": [0.5, 0], "dragging": false, "position": {"x": 520, "y": 160}, "selected": false, "positionAbsolute": {"x": 520, "y": 160}}, {"id": "test-stub-1", "data": {"title": null, "subtitle": null, "stepVariables": [{"path": "done", "type": "Float", "title": "End test result"}], "outputVariableName": "done"}, "type": "test-stub/_generic", "width": 280, "height": 62, "origin": [0.5, 0], "dragging": false, "position": {"x": 520, "y": 320}, "selected": false, "positionAbsolute": {"x": 520, "y": 320}}]}