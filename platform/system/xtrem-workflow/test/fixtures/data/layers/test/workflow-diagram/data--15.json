{"edges": [{"id": "test-started-1--out-1", "source": "test-started-1", "target": "send-user-notification-1", "sourceHandle": "out"}, {"id": "send-user-notification-1--out-1", "source": "send-user-notification-1", "target": "test-stub-1", "sourceHandle": "out"}], "nodes": [{"id": "test-started-1", "data": {"localizedTitle": {"base": "Start test"}, "topic": "WorkflowProcess/testStarted", "parameters": [{"name": "title", "type": "String"}, {"name": "description", "type": "String"}, {"name": "action1", "type": "String"}, {"name": "link1", "type": "String"}], "stepVariables": [{"path": "test.parameters.title", "type": "String", "title": "Title"}, {"path": "test.parameters.description", "type": "String", "title": "Description"}, {"path": "test.parameters.action1", "type": "String", "title": "Action 1"}, {"path": "test.parameters.link1", "type": "String", "title": "Link 1"}]}, "type": "test-started/_generic", "width": 280, "height": 62, "origin": [0.5, 0], "dragging": false, "position": {"x": 400, "y": 20}, "selected": false, "positionAbsolute": {"x": 400, "y": 20}}, {"id": "send-user-notification-1", "data": {"icon": "business", "level": "info", "localizedTitle": {"en": "The title", "fr": "Le titre"}, "actions": [{"link": "link1 - {{test.parameters.link1}}", "style": "primary", "localizedTitle": {"en": "action1(en) - {{test.parameters.action1}}", "fr": "action1(fr) - {{test.parameters.action1}}"}, "isLinkVariable": false, "isTitleVariable": false}], "subtitle": null, "recipients": [], "iconVariable": null, "levelVariable": null, "isIconVariable": null, "isLevelVariable": null, "stepVariables": [], "localizedNotificationTitle": {"en": "Title - {{test.parameters.title}}", "fr": "Titre - {{test.parameters.title}}"}, "shouldDisplayToast": true, "localizedNotificationDescription": {"en": "Description(en) - {{test.parameters.description}}", "fr": "Description(fr) - {{test.parameters.description}}"}}, "type": "send-user-notification/_generic", "width": 280, "height": 62, "origin": [0.5, 0], "dragging": false, "position": {"x": 400, "y": 140}, "selected": false, "positionAbsolute": {"x": 400, "y": 140}}, {"id": "test-stub-1", "data": {"localizedTitle": {"base": "done"}, "subtitle": null, "stepVariables": [{"path": "done", "type": "Float", "title": "End test result ({done})"}], "outputVariableName": "done"}, "type": "test-stub/_generic", "width": 280, "height": 62, "origin": [0.5, 0], "dragging": false, "position": {"x": 400, "y": 260}, "selected": false, "positionAbsolute": {"x": 400, "y": 260}}]}