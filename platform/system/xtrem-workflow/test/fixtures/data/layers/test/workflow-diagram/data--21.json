{"edges": [{"id": "test-started-1--out-1", "source": "test-started-1", "target": "condition-1", "sourceHandle": "out"}, {"id": "condition-1--out-true-1", "source": "condition-1", "target": "condition-2", "sourceHandle": "out-true"}, {"id": "condition-1--out-false-1", "source": "condition-1", "target": "condition-3", "sourceHandle": "out-false"}, {"id": "condition-2--out-true-1", "source": "condition-2", "target": "test-stub-1", "sourceHandle": "out-true"}, {"id": "condition-2--out-false-1", "source": "condition-2", "target": "condition-4", "sourceHandle": "out-false"}, {"id": "condition-2--out-false-2", "source": "condition-2", "target": "test-stub-2", "sourceHandle": "out-false"}, {"id": "condition-3--out-true-1", "source": "condition-3", "target": "condition-4", "sourceHandle": "out-true"}, {"id": "condition-3--out-true-2", "source": "condition-3", "target": "test-stub-3", "sourceHandle": "out-true"}, {"id": "condition-3--out-false-1", "source": "condition-3", "target": "test-stub-4", "sourceHandle": "out-false"}, {"id": "condition-4--out-true-1", "source": "condition-4", "target": "test-stub-5", "sourceHandle": "out-true"}, {"id": "condition-4--out-false-1", "source": "condition-4", "target": "test-stub-6", "sourceHandle": "out-false"}, {"id": "test-stub-3--out-1", "source": "test-stub-3", "target": "test-stub-6", "sourceHandle": "out"}], "nodes": [{"id": "test-started-1", "data": {"topic": "WorkflowProcess/testStarted", "parameters": [{"name": "v1", "type": "Boolean"}, {"name": "v2", "type": "Boolean"}, {"name": "v3", "type": "Boolean"}, {"name": "v4", "type": "Boolean"}], "stepVariables": [{"path": "test.parameters.v1", "type": "Boolean", "title": "V 1"}, {"path": "test.parameters.v2", "type": "Boolean", "title": "V 2"}, {"path": "test.parameters.v3", "type": "Boolean", "title": "V 3"}, {"path": "test.parameters.v4", "type": "Boolean", "title": "V 4"}]}, "type": "test-started/_generic", "width": 280, "height": 62, "origin": [0.5, 0], "dragging": false, "position": {"x": 620, "y": 0}, "selected": false, "positionAbsolute": {"x": 620, "y": 0}}, {"id": "condition-1", "data": {"localizedTitle": {"base": "v1?"}, "subtitle": null, "conditions": [{"path": "test.parameters.v1", "value": "true", "operator": "matches", "useParameter": false}], "stepVariables": []}, "type": "condition/_generic", "width": 120, "height": 80, "origin": [0.5, 0], "dragging": false, "position": {"x": 700, "y": 120}, "selected": false, "positionAbsolute": {"x": 700, "y": 120}}, {"id": "condition-2", "data": {"localizedTitle": {"base": "v2?"}, "subtitle": null, "conditions": [{"path": "test.parameters.v2", "value": "true", "operator": "matches", "useParameter": false}], "stepVariables": []}, "type": "condition/_generic", "width": 120, "height": 80, "origin": [0.5, 0], "dragging": false, "position": {"x": 320, "y": 240}, "selected": false, "positionAbsolute": {"x": 320, "y": 240}}, {"id": "condition-3", "data": {"localizedTitle": {"base": "v3?"}, "subtitle": null, "conditions": [{"path": "test.parameters.v3", "value": "true", "operator": "matches", "useParameter": false}], "stepVariables": []}, "type": "condition/_generic", "width": 120, "height": 80, "origin": [0.5, 0], "dragging": false, "position": {"x": 1120, "y": 240}, "selected": false, "positionAbsolute": {"x": 1120, "y": 240}}, {"id": "test-stub-1", "data": {"localizedTitle": {"base": "T1, T2"}, "subtitle": null, "stepVariables": [{"path": "t1_t2", "type": "Float", "title": "End test result ({v2 true})"}], "outputVariableName": "t1_t2"}, "type": "test-stub/_generic", "width": 280, "height": 62, "origin": [0.5, 0], "dragging": false, "position": {"x": 80, "y": 360}, "selected": false, "positionAbsolute": {"x": 80, "y": 360}}, {"id": "condition-4", "data": {"localizedTitle": {"base": "v4?"}, "subtitle": null, "conditions": [{"path": "test.parameters.v4", "value": "true", "operator": "matches", "useParameter": false}], "stepVariables": []}, "type": "condition/_generic", "width": 120, "height": 80, "origin": [0.5, 0], "dragging": false, "position": {"x": 700, "y": 460}, "selected": false, "positionAbsolute": {"x": 700, "y": 460}}, {"id": "test-stub-2", "data": {"localizedTitle": {"base": "T1, F2"}, "subtitle": null, "stepVariables": [{"path": "t1_f2", "type": "Float", "title": "End test result ({v2 false})"}], "outputVariableName": "t1_f2"}, "type": "test-stub/_generic", "width": 280, "height": 62, "origin": [0.5, 0], "dragging": false, "position": {"x": 400, "y": 360}, "selected": false, "positionAbsolute": {"x": 400, "y": 360}}, {"id": "test-stub-3", "data": {"localizedTitle": {"base": "F1, T3"}, "subtitle": null, "stepVariables": [{"path": "f1_t3", "type": "Float", "title": "End test result ({v3 true})"}], "outputVariableName": "f1_t3"}, "type": "test-stub/_generic", "width": 280, "height": 62, "origin": [0.5, 0], "dragging": false, "position": {"x": 880, "y": 360}, "selected": false, "positionAbsolute": {"x": 880, "y": 360}}, {"id": "test-stub-4", "data": {"localizedTitle": {"base": "F1, F3"}, "subtitle": null, "stepVariables": [{"path": "f1_f3", "type": "Float", "title": "End test result ({v3 false})"}], "outputVariableName": "f1_f3"}, "type": "test-stub/_generic", "width": 280, "height": 62, "origin": [0.5, 0], "dragging": false, "position": {"x": 1220, "y": 360}, "selected": false, "positionAbsolute": {"x": 1220, "y": 360}}, {"id": "test-stub-5", "data": {"localizedTitle": {"base": "T1, F2, T4 or F1, T3, T4"}, "subtitle": null, "stepVariables": [{"path": "t1_f2_t4_or_f1_t3_t4", "type": "Float", "title": "End test result ({v4 true})"}], "outputVariableName": "t1_f2_t4_or_f1_t3_t4"}, "type": "test-stub/_generic", "width": 280, "height": 62, "origin": [0.5, 0], "dragging": false, "position": {"x": 440, "y": 580}, "selected": false, "positionAbsolute": {"x": 440, "y": 580}}, {"id": "test-stub-6", "data": {"localizedTitle": {"base": "(T1, F2, F4) or (F1, T3)"}, "subtitle": null, "stepVariables": [{"path": "t1_f2_f4_or_f1_t3", "type": "Float", "title": "End test result ({v4 false})"}], "outputVariableName": "t1_f2_f4_or_f1_t3"}, "type": "test-stub/_generic", "width": 280, "height": 62, "origin": [0.5, 0], "dragging": false, "position": {"x": 820, "y": 580}, "selected": false, "positionAbsolute": {"x": 820, "y": 580}}]}