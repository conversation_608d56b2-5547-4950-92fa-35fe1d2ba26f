{"edges": [{"id": "entity-created-1--out-1", "source": "entity-created-1", "target": "update-entity-1", "sourceHandle": "out"}, {"id": "update-entity-1--out-1", "source": "update-entity-1", "target": "test-stub-1", "sourceHandle": "out"}], "nodes": [{"id": "entity-created-1", "data": {"localizedTitle": {"base": "Test Data Types created"}, "topic": "TestDataTypes/created", "subtitle": "@sage/xtrem-workflow/TestDataTypes", "conditions": [{"path": "testDataTypes.id", "value": "customField", "operator": "startsWith", "useParameter": false}], "entityName": "@sage/xtrem-workflow/TestDataTypes", "stepVariables": [{"path": "testDataTypes._customData.customText", "type": "String", "title": "custom text"}, {"path": "testDataTypes._customData.customBool", "type": "Boolean", "title": "custom boolean"}, {"path": "testDataTypes.id", "type": "String", "title": "Id"}, {"node": "TestDataTypes", "path": "testDataTypes._id", "type": "IntReference", "title": "Test data types id"}]}, "type": "entity-created/_generic", "width": 280, "height": 84, "origin": [0.5, 0], "dragging": false, "position": {"x": 220, "y": 40}, "selected": false, "positionAbsolute": {"x": 220, "y": 40}}, {"id": "update-entity-1", "data": {"subtitle": null, "entityName": "@sage/xtrem-workflow/TestDataTypes", "updateRules": [{"sourceValue": true, "destinationPath": "_customData.customBool"}, {"sourceValue": "{{testDataTypes._customData.customText}} world!", "destinationPath": "_customData.customText"}], "pathToUpdate": "testDataTypes._id", "stepVariables": [{"path": "testDataTypes._customData.customBool", "type": "Boolean", "title": "custom bool"}, {"path": "testDataTypes._customData.customText", "type": "String", "title": "custom text"}]}, "type": "update-entity/_generic", "width": 280, "height": 62, "origin": [0.5, 0], "dragging": false, "position": {"x": 220, "y": 180}, "selected": false}, {"id": "test-stub-1", "data": {"subtitle": null, "stepVariables": [{"path": "ok", "type": "Float", "title": "End test result"}], "outputVariableName": "ok"}, "type": "test-stub/_generic", "width": 280, "height": 62, "origin": [0.5, 0], "dragging": false, "position": {"x": 220, "y": 300}, "selected": false, "positionAbsolute": {"x": 220, "y": 300}}]}