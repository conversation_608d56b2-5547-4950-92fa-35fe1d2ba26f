{"edges": [{"id": "entity-created-1--out-1", "source": "entity-created-1", "target": "condition-1", "sourceHandle": "out"}, {"id": "condition-1--out-true-1", "source": "condition-1", "target": "test-stub-1", "sourceHandle": "out-true"}, {"id": "entity-created-1--out-2", "source": "entity-created-1", "target": "condition-2", "sourceHandle": "out"}, {"id": "condition-2--out-true-1", "source": "condition-2", "target": "test-stub-2", "sourceHandle": "out-true"}, {"id": "entity-created-1--out-3", "source": "entity-created-1", "target": "condition-3", "sourceHandle": "out"}, {"id": "condition-3--out-true-1", "source": "condition-3", "target": "test-stub-3", "sourceHandle": "out-true"}, {"id": "entity-created-1--out-4", "source": "entity-created-1", "target": "condition-4", "sourceHandle": "out"}, {"id": "condition-4--out-true-1", "source": "condition-4", "target": "test-stub-4", "sourceHandle": "out-true"}, {"id": "entity-created-1--out-5", "source": "entity-created-1", "target": "condition-5", "sourceHandle": "out"}, {"id": "condition-5--out-true-1", "source": "condition-5", "target": "test-stub-5", "sourceHandle": "out-true"}, {"id": "entity-created-1--out-6", "source": "entity-created-1", "target": "condition-6", "sourceHandle": "out"}, {"id": "entity-created-1--out-7", "source": "entity-created-1", "target": "condition-7", "sourceHandle": "out"}, {"id": "condition-7--out-true-1", "source": "condition-7", "target": "test-stub-7", "sourceHandle": "out-true"}, {"id": "entity-created-1--out-8", "source": "entity-created-1", "target": "condition-8", "sourceHandle": "out"}, {"id": "condition-8--out-true-1", "source": "condition-8", "target": "test-stub-8", "sourceHandle": "out-true"}, {"id": "entity-created-1--out-9", "source": "entity-created-1", "target": "condition-9", "sourceHandle": "out"}, {"id": "condition-9--out-true-1", "source": "condition-9", "target": "test-stub-9", "sourceHandle": "out-true"}, {"id": "entity-created-1--out-10", "source": "entity-created-1", "target": "condition-10", "sourceHandle": "out"}, {"id": "condition-10--out-true-1", "source": "condition-10", "target": "test-stub-10", "sourceHandle": "out-true"}, {"id": "entity-created-1--out-11", "source": "entity-created-1", "target": "condition-11", "sourceHandle": "out"}, {"id": "condition-11--out-true-1", "source": "condition-11", "target": "test-stub-11", "sourceHandle": "out-true"}, {"id": "entity-created-1--out-12", "source": "entity-created-1", "target": "condition-12", "sourceHandle": "out"}, {"id": "condition-12--out-true-1", "source": "condition-12", "target": "test-stub-12", "sourceHandle": "out-true"}, {"id": "entity-created-1--out-13", "source": "entity-created-1", "target": "condition-13", "sourceHandle": "out"}, {"id": "condition-13--out-true-1", "source": "condition-13", "target": "test-stub-13", "sourceHandle": "out-true"}, {"id": "condition-6--out-true-1", "source": "condition-6", "target": "test-stub-6", "sourceHandle": "out-true"}], "nodes": [{"id": "entity-created-1", "data": {"localizedTitle": {"base": "Test Data Types created"}, "topic": "TestDataTypes/created", "subtitle": "@sage/xtrem-workflow/TestDataTypes", "conditions": [{"path": "testDataTypes.id", "value": "date", "operator": "startsWith", "useParameter": false}], "entityName": "@sage/xtrem-workflow/TestDataTypes", "stepVariables": [{"node": "TestDataTypes", "path": "testDataTypes._id", "type": "IntReference", "title": "Test data types id"}, {"path": "testDataTypes.id", "type": "String", "title": "Id"}, {"path": "testDataTypes.date1", "type": "Date", "title": "Date 1"}, {"path": "testDataTypes.date2", "type": "Date", "title": "Date 2"}, {"path": "testDataTypes.date3", "type": "Date", "title": "Date 3"}]}, "type": "entity-created/_generic", "width": 280, "height": 84, "origin": [0.5, 0], "dragging": false, "position": {"x": 1020, "y": -20}, "selected": false, "positionAbsolute": {"x": 1020, "y": -20}}, {"id": "condition-1", "data": {"localizedTitle": {"base": "equals"}, "subtitle": null, "conditions": [{"path": "testDataTypes.date1", "value": "testDataTypes.date2", "operator": "equals", "useParameter": true}], "stepVariables": []}, "type": "condition/_generic", "width": 120, "height": 80, "origin": [0.5, 0], "dragging": false, "position": {"x": 20, "y": 160}, "selected": false, "positionAbsolute": {"x": 20, "y": 160}}, {"id": "test-stub-1", "data": {"localizedTitle": {"base": "equals"}, "subtitle": null, "stepVariables": [{"path": "equals", "type": "Float", "title": "End test result ({equal})"}], "outputVariableName": "equals"}, "type": "test-stub/_generic", "width": 280, "height": 62, "origin": [0.5, 0], "dragging": false, "position": {"x": -160, "y": 280}, "selected": false, "positionAbsolute": {"x": -160, "y": 280}}, {"id": "condition-2", "data": {"localizedTitle": {"base": "not equals"}, "subtitle": null, "conditions": [{"path": "testDataTypes.date1", "value": "testDataTypes.date2", "operator": "notEqual", "useParameter": true}], "stepVariables": []}, "type": "condition/_generic", "width": 120, "height": 80, "origin": [0.5, 0], "dragging": false, "position": {"x": 200, "y": 160}, "selected": false, "positionAbsolute": {"x": 200, "y": 160}}, {"id": "test-stub-2", "data": {"localizedTitle": {"base": "not equals"}, "subtitle": null, "stepVariables": [{"path": "notEquals", "type": "Float", "title": "End test result ({not equal})"}], "outputVariableName": "notEquals"}, "type": "test-stub/_generic", "width": 280, "height": 62, "origin": [0.5, 0], "dragging": false, "position": {"x": 20, "y": 400}, "selected": false, "positionAbsolute": {"x": 20, "y": 400}}, {"id": "condition-3", "data": {"localizedTitle": {"base": "less than"}, "subtitle": null, "conditions": [{"path": "testDataTypes.date1", "value": "testDataTypes.date2", "operator": "lessThan", "useParameter": true}], "stepVariables": []}, "type": "condition/_generic", "width": 120, "height": 80, "origin": [0.5, 0], "dragging": false, "position": {"x": 380, "y": 160}, "selected": false, "positionAbsolute": {"x": 380, "y": 160}}, {"id": "test-stub-3", "data": {"localizedTitle": {"base": "less than"}, "subtitle": null, "stepVariables": [{"path": "lessThan", "type": "Float", "title": "End test result ({less than})"}], "outputVariableName": "lessThan"}, "type": "test-stub/_generic", "width": 280, "height": 62, "origin": [0.5, 0], "dragging": false, "position": {"x": 200, "y": 280}, "selected": false, "positionAbsolute": {"x": 200, "y": 280}}, {"id": "condition-4", "data": {"localizedTitle": {"base": "greater than"}, "subtitle": null, "conditions": [{"path": "testDataTypes.date1", "value": "testDataTypes.date2", "operator": "greaterThan", "useParameter": true}], "stepVariables": []}, "type": "condition/_generic", "width": 120, "height": 80, "origin": [0.5, 0], "dragging": false, "position": {"x": 740, "y": 160}, "selected": false, "positionAbsolute": {"x": 740, "y": 160}}, {"id": "test-stub-4", "data": {"localizedTitle": {"base": "greater than"}, "subtitle": null, "stepVariables": [{"path": "greaterThan", "type": "Float", "title": "End test result ({greater than})"}], "outputVariableName": "greaterThan"}, "type": "test-stub/_generic", "width": 280, "height": 62, "origin": [0.5, 0], "dragging": false, "position": {"x": 560, "y": 280}, "selected": false, "positionAbsolute": {"x": 560, "y": 280}}, {"id": "condition-5", "data": {"localizedTitle": {"base": "less or equal"}, "subtitle": null, "conditions": [{"path": "testDataTypes.date1", "value": "testDataTypes.date2", "operator": "lessThanOrEqual", "useParameter": true}], "stepVariables": []}, "type": "condition/_generic", "width": 120, "height": 80, "origin": [0.5, 0], "dragging": false, "position": {"x": 560, "y": 160}, "selected": false, "positionAbsolute": {"x": 560, "y": 160}}, {"id": "test-stub-5", "data": {"localizedTitle": {"base": "less than or equal"}, "subtitle": null, "stepVariables": [{"path": "lessThanOrEqual", "type": "Float", "title": "End test result ({less than or equal})"}], "outputVariableName": "lessThanOrEqual"}, "type": "test-stub/_generic", "width": 280, "height": 62, "origin": [0.5, 0], "dragging": false, "position": {"x": 380, "y": 400}, "selected": false, "positionAbsolute": {"x": 380, "y": 400}}, {"id": "condition-6", "data": {"localizedTitle": {"base": "greater or equal"}, "subtitle": null, "conditions": [{"path": "testDataTypes.date1", "value": "testDataTypes.date2", "operator": "greaterThanOrEqual", "useParameter": true}], "stepVariables": []}, "type": "condition/_generic", "width": 120, "height": 80, "origin": [0.5, 0], "dragging": false, "position": {"x": 920, "y": 160}, "selected": false, "positionAbsolute": {"x": 920, "y": 160}}, {"id": "condition-7", "data": {"localizedTitle": {"base": "between"}, "subtitle": null, "conditions": [{"path": "testDataTypes.date1", "value": "testDataTypes.date2~testDataTypes.date3", "operator": "inRange", "useParameter": true}], "stepVariables": []}, "type": "condition/_generic", "width": 120, "height": 80, "origin": [0.5, 0], "dragging": false, "position": {"x": 1100, "y": 160}, "selected": false, "positionAbsolute": {"x": 1100, "y": 160}}, {"id": "test-stub-7", "data": {"localizedTitle": {"base": "between"}, "subtitle": null, "stepVariables": [{"path": "between", "type": "Float", "title": "End test result ({between})"}], "outputVariableName": "between"}, "type": "test-stub/_generic", "width": 280, "height": 62, "origin": [0.5, 0], "dragging": false, "position": {"x": 920, "y": 280}, "selected": false, "positionAbsolute": {"x": 920, "y": 280}}, {"id": "condition-8", "data": {"localizedTitle": {"base": "last 7 days"}, "subtitle": null, "conditions": [{"path": "testDataTypes.date1", "value": "last-7-days", "operator": "timeFrame", "useParameter": false}], "stepVariables": []}, "type": "condition/_generic", "width": 120, "height": 80, "origin": [0.5, 0], "dragging": false, "position": {"x": 1280, "y": 160}, "selected": false, "positionAbsolute": {"x": 1280, "y": 160}}, {"id": "test-stub-8", "data": {"localizedTitle": {"base": "last 7 days"}, "subtitle": null, "stepVariables": [{"path": "last7Days", "type": "Float", "title": "End test result ({last 7 days})"}], "outputVariableName": "last7Days"}, "type": "test-stub/_generic", "width": 280, "height": 62, "origin": [0.5, 0], "dragging": false, "position": {"x": 1100, "y": 400}, "selected": false, "positionAbsolute": {"x": 1100, "y": 400}}, {"id": "condition-9", "data": {"localizedTitle": {"base": "last 30 days"}, "subtitle": null, "conditions": [{"path": "testDataTypes.date1", "value": "last-30-days", "operator": "timeFrame", "useParameter": false}], "stepVariables": []}, "type": "condition/_generic", "width": 120, "height": 80, "origin": [0.5, 0], "dragging": false, "position": {"x": 1480, "y": 160}, "selected": false, "positionAbsolute": {"x": 1480, "y": 160}}, {"id": "test-stub-9", "data": {"localizedTitle": {"base": "last 30 days"}, "subtitle": null, "stepVariables": [{"path": "last30Days", "type": "Float", "title": "End test result ({last 30 days})"}], "outputVariableName": "last30Days"}, "type": "test-stub/_generic", "width": 280, "height": 62, "origin": [0.5, 0], "dragging": false, "position": {"x": 1300, "y": 280}, "selected": false, "positionAbsolute": {"x": 1300, "y": 280}}, {"id": "condition-10", "data": {"localizedTitle": {"base": "prev month"}, "subtitle": null, "conditions": [{"path": "testDataTypes.date1", "value": "previous-month", "operator": "timeFrame", "useParameter": false}], "stepVariables": []}, "type": "condition/_generic", "width": 120, "height": 80, "origin": [0.5, 0], "dragging": false, "position": {"x": 1880, "y": 160}, "selected": false, "positionAbsolute": {"x": 1880, "y": 160}}, {"id": "test-stub-10", "data": {"localizedTitle": {"base": "previous month"}, "subtitle": null, "stepVariables": [{"path": "previousMonth", "type": "Float", "title": "End test result ({previous month})"}], "outputVariableName": "previousMonth"}, "type": "test-stub/_generic", "width": 280, "height": 62, "origin": [0.5, 0], "dragging": false, "position": {"x": 1700, "y": 280}, "selected": false, "positionAbsolute": {"x": 1700, "y": 280}}, {"id": "condition-11", "data": {"localizedTitle": {"base": "this month"}, "subtitle": null, "conditions": [{"path": "testDataTypes.date1", "value": "same-month", "operator": "timeFrame", "useParameter": false}], "stepVariables": []}, "type": "condition/_generic", "width": 120, "height": 80, "origin": [0.5, 0], "dragging": false, "position": {"x": 1680, "y": 160}, "selected": false, "positionAbsolute": {"x": 1680, "y": 160}}, {"id": "test-stub-11", "data": {"localizedTitle": {"base": "current month"}, "subtitle": null, "stepVariables": [{"path": "currentMonth", "type": "Float", "title": "End test result ({current month})"}], "outputVariableName": "currentMonth"}, "type": "test-stub/_generic", "width": 280, "height": 62, "origin": [0.5, 0], "dragging": false, "position": {"x": 1500, "y": 400}, "selected": false, "positionAbsolute": {"x": 1500, "y": 400}}, {"id": "condition-12", "data": {"localizedTitle": {"base": "this year"}, "subtitle": null, "conditions": [{"path": "testDataTypes.date1", "value": "same-year", "operator": "timeFrame", "useParameter": false}], "stepVariables": []}, "type": "condition/_generic", "width": 120, "height": 80, "origin": [0.5, 0], "dragging": false, "position": {"x": 2080, "y": 160}, "selected": false, "positionAbsolute": {"x": 2080, "y": 160}}, {"id": "test-stub-12", "data": {"localizedTitle": {"base": "current year"}, "subtitle": null, "stepVariables": [{"path": "currentYear", "type": "Float", "title": "End test result ({current year})"}], "outputVariableName": "currentYear"}, "type": "test-stub/_generic", "width": 280, "height": 62, "origin": [0.5, 0], "dragging": false, "position": {"x": 1900, "y": 400}, "selected": false, "positionAbsolute": {"x": 1900, "y": 400}}, {"id": "condition-13", "data": {"localizedTitle": {"base": "prev year"}, "subtitle": null, "conditions": [{"path": "testDataTypes.date1", "value": "previous-year", "operator": "timeFrame", "useParameter": false}], "stepVariables": []}, "type": "condition/_generic", "width": 120, "height": 80, "origin": [0.5, 0], "dragging": false, "position": {"x": 2280, "y": 160}, "selected": false, "positionAbsolute": {"x": 2280, "y": 160}}, {"id": "test-stub-13", "data": {"localizedTitle": {"base": "previous year"}, "subtitle": null, "stepVariables": [{"path": "previousYear", "type": "Float", "title": "End test result ({previous year})"}], "outputVariableName": "previousYear"}, "type": "test-stub/_generic", "width": 280, "height": 62, "origin": [0.5, 0], "dragging": false, "position": {"x": 2100, "y": 280}, "selected": false, "positionAbsolute": {"x": 2100, "y": 280}}, {"id": "test-stub-6", "data": {"localizedTitle": {"base": "greater than or equal"}, "subtitle": null, "stepVariables": [{"path": "greaterThanOrEqual", "type": "Float", "title": "End test result ({greater than or equal})"}], "outputVariableName": "greaterThanOrEqual"}, "type": "test-stub/_generic", "width": 280, "height": 62, "origin": [0.5, 0], "dragging": false, "position": {"x": 740, "y": 400}, "selected": false, "positionAbsolute": {"x": 740, "y": 400}}]}