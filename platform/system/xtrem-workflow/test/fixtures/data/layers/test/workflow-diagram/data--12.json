{"edges": [{"id": "entity-created-1--out-1", "source": "entity-created-1", "target": "condition-1", "sourceHandle": "out"}, {"id": "condition-1--out-true-1", "source": "condition-1", "target": "test-stub-1", "sourceHandle": "out-true"}, {"id": "entity-created-1--out-2", "source": "entity-created-1", "target": "condition-2", "sourceHandle": "out"}, {"id": "condition-2--out-true-1", "source": "condition-2", "target": "test-stub-2", "sourceHandle": "out-true"}, {"id": "entity-created-1--out-3", "source": "entity-created-1", "target": "condition-3", "sourceHandle": "out"}, {"id": "condition-3--out-true-1", "source": "condition-3", "target": "test-stub-3", "sourceHandle": "out-true"}], "nodes": [{"id": "entity-created-1", "data": {"localizedTitle": {"base": "Test Data Types created"}, "topic": "TestDataTypes/created", "subtitle": "@sage/xtrem-workflow/TestDataTypes", "conditions": [{"path": "testDataTypes.id", "value": "enum", "operator": "startsWith", "useParameter": false}], "entityName": "@sage/xtrem-workflow/TestDataTypes", "stepVariables": [{"node": "TestDataTypes", "path": "testDataTypes._id", "type": "IntReference", "title": "Test data types id"}, {"path": "testDataTypes.id", "type": "String", "title": "Id"}, {"node": "@sage/xtrem-workflow/TestEnum", "path": "testDataTypes.enum1", "type": "Enum", "title": "Enum 1"}, {"node": "@sage/xtrem-workflow/TestEnum", "path": "testDataTypes.enum2", "type": "Enum", "title": "Enum 2"}]}, "type": "entity-created/_generic", "width": 280, "height": 84, "origin": [0.5, 0], "dragging": false, "position": {"x": 500, "y": 20}, "selected": false, "positionAbsolute": {"x": 500, "y": 20}}, {"id": "condition-1", "data": {"localizedTitle": {"base": "eq \"value1\""}, "subtitle": null, "conditions": [{"path": "testDataTypes.enum1", "value": ["value1"], "operator": "set", "useParameter": false}], "stepVariables": []}, "type": "condition/_generic", "width": 120, "height": 80, "origin": [0.5, 0], "dragging": false, "position": {"x": 260, "y": 160}, "selected": false, "positionAbsolute": {"x": 260, "y": 160}}, {"id": "test-stub-1", "data": {"localizedTitle": {"base": "equals \"value1\""}, "subtitle": null, "stepVariables": [{"path": "equalsValue1", "type": "Float", "title": "End test result ({equals \"value1\"})"}], "outputVariableName": "equalsValue1"}, "type": "test-stub/_generic", "width": 280, "height": 62, "origin": [0.5, 0], "dragging": false, "position": {"x": 80, "y": 280}, "selected": false, "positionAbsolute": {"x": 80, "y": 280}}, {"id": "condition-2", "data": {"localizedTitle": {"base": "neq \"value1\""}, "subtitle": null, "conditions": [{"path": "testDataTypes.enum1", "value": ["value1"], "operator": "multiNotEqual", "useParameter": false}], "stepVariables": []}, "type": "condition/_generic", "width": 120, "height": 80, "origin": [0.5, 0], "dragging": false, "position": {"x": 580, "y": 160}, "selected": false, "positionAbsolute": {"x": 580, "y": 160}}, {"id": "test-stub-2", "data": {"localizedTitle": {"base": "not equal \"value1\""}, "subtitle": null, "stepVariables": [{"path": "notEqualsValue1", "type": "Float", "title": "End test result ({not equal \"value1\"})"}], "outputVariableName": "notEqualsValue1"}, "type": "test-stub/_generic", "width": 280, "height": 62, "origin": [0.5, 0], "dragging": false, "position": {"x": 400, "y": 280}, "selected": false, "positionAbsolute": {"x": 400, "y": 280}}, {"id": "condition-3", "data": {"subtitle": null, "conditions": [{"path": "testDataTypes.enum1", "value": ["testDataTypes.enum2"], "operator": "set", "useParameter": true}], "stepVariables": []}, "type": "condition/_generic", "width": 120, "height": 80, "origin": [0.5, 0], "dragging": false, "position": {"x": 900, "y": 160}, "selected": false, "positionAbsolute": {"x": 900, "y": 160}}, {"id": "test-stub-3", "data": {"localizedTitle": {"base": "equals"}, "subtitle": null, "stepVariables": [{"path": "equals", "type": "Float", "title": "End test result ({equals})"}], "outputVariableName": "equals"}, "type": "test-stub/_generic", "width": 280, "height": 62, "origin": [0.5, 0], "dragging": false, "position": {"x": 720, "y": 280}, "selected": false, "positionAbsolute": {"x": 720, "y": 280}}]}