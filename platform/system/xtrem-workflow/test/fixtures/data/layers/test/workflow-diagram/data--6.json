{"edges": [], "nodes": [{"id": "entity-updated-1", "data": {"localizedTitle": {"base": "Test Person updated"}, "topic": "TestPerson/updated", "subtitle": "@sage/xtrem-workflow/TestPerson", "conditions": [{"path": "testPerson.age", "value": "$old.testPerson.age", "operator": "greaterThan", "useParameter": true}], "entityName": "@sage/xtrem-workflow/TestPerson", "stepVariables": [{"path": "testPerson.age", "type": "Int", "title": "Test Person / Age"}, {"path": "testPerson.name", "type": "String", "title": "Test Person / Name"}, {"path": "testPerson.pet.name", "type": "String", "title": "Test Person / Pet / Name"}, {"path": "$old.testPerson.age", "type": "Int", "title": "Test Person / Age (old)"}, {"path": "$old.testPerson.name", "type": "String", "title": "Test Person / Name (old)"}]}, "type": "entity-updated/_generic", "width": 280, "height": 69, "origin": [0.5, 0], "dragging": false, "position": {"x": 480, "y": 40}, "selected": false, "positionAbsolute": {"x": 480, "y": 40}}]}