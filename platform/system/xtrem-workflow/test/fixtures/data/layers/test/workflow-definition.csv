"id";"name";"is_active";"diagram";"start_topic";"test_user"
"calculateTest";"{""base"":""Calculate test""}";"Y";"1";"WorkflowProcess/testStarted";"<EMAIL>"
"entityCreatedEvent";"{""base"":""Test person created""}";"Y";"2";"TestPerson/created";"<EMAIL>"
"conditionActionTest";"{""base"":""Condition test""}";"Y";"3";"WorkflowProcess/testStarted";"<EMAIL>"
"readEntityActionTest";"{""base"":""Test read-entity action""}";"Y";"4";"WorkflowProcess/testStarted";"<EMAIL>"
"readEntityActionTestWithNull";"{""base"":""Test read-entity action (null if not found)""}";"Y";"5";"WorkflowProcess/testStarted";"<EMAIL>"
"entityUpdatedEventTest";"{""base"":""Test person updated""}";"Y";"6";"TestPerson/updated";"<EMAIL>"
"conditionBooleanTest";"{""base"":""Test boolean condition""}";"Y";"7";"TestDataTypes/created";"<EMAIL>"
"conditionStringTest";"{""base"":""Test string conditions""}";"Y";"8";"TestDataTypes/created";"<EMAIL>"
"conditionDecimalTest";"{""base"":""Test decimal conditions""}";"Y";"9";"TestDataTypes/created";"<EMAIL>"
"conditionIntegerTest";"{""base"":""Test integer conditions""}";"Y";"10";"TestDataTypes/created";"<EMAIL>"
"conditionDoubleTest";"{""base"":""Test double conditions""}";"Y";"11";"TestDataTypes/created";"<EMAIL>"
"conditionEnumTest";"{""base"":""Test enum conditions""}";"Y";"12";"TestDataTypes/created";"<EMAIL>"
"conditionDateTest";"{""base"":""Test date conditions""}";"Y";"13";"TestDataTypes/created";"<EMAIL>"
"conditionDatetimeTest";"{""base"":""Test datetime conditions""}";"Y";"14";"TestDataTypes/created";"<EMAIL>"
"sendUserNotificationTest";"{""base"":""Test send user notification""}";"Y";"15";"WorkflowProcess/testStarted";"<EMAIL>"
"diamondTest";"{""base"":""Test simple diamond topology""}";"Y";"16";"WorkflowProcess/testStarted";"<EMAIL>"
"diamondConditionTest";"{""base"":""Test diamond topology with condition""}";"Y";"17";"WorkflowProcess/testStarted";"<EMAIL>"
"updateEntityTest";"{""base"":""Test update entity""}";"Y";"18";"TestDataTypes/created";"<EMAIL>"
"mutationTest";"{""base"":""Mutation test""}";"Y";"19";"TestNodeForMutation/created";"<EMAIL>"
"customFieldsTest";"{""base"":""Test custom fields""}";"Y";"20";"TestDataTypes/created";"<EMAIL>"
"complexTopologyTest";"{""base"":""Test of complex topology""}";"Y";"21";"WorkflowProcess/testStarted";"<EMAIL>"
"configLocaleTest";"{""base"":""Config locale test""}";"Y";"22";"TestConfigLocale/created";"<EMAIL>"
