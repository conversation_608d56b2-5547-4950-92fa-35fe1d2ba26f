{"edges": [{"id": "entity-created-1--out-1", "source": "entity-created-1", "target": "condition-1", "sourceHandle": "out"}, {"id": "condition-1--out-true-1", "source": "condition-1", "target": "test-stub-1", "sourceHandle": "out-true"}, {"id": "entity-created-1--out-2", "source": "entity-created-1", "target": "condition-2", "sourceHandle": "out"}, {"id": "condition-2--out-true-1", "source": "condition-2", "target": "test-stub-3", "sourceHandle": "out-true"}, {"id": "entity-created-1--out-3", "source": "entity-created-1", "target": "condition-3", "sourceHandle": "out"}, {"id": "condition-3--out-true-1", "source": "condition-3", "target": "test-stub-5", "sourceHandle": "out-true"}, {"id": "entity-created-1--out-4", "source": "entity-created-1", "target": "condition-4", "sourceHandle": "out"}, {"id": "condition-4--out-true-1", "source": "condition-4", "target": "test-stub-7", "sourceHandle": "out-true"}, {"id": "entity-created-1--out-5", "source": "entity-created-1", "target": "condition-5", "sourceHandle": "out"}, {"id": "condition-5--out-true-1", "source": "condition-5", "target": "test-stub-9", "sourceHandle": "out-true"}, {"id": "entity-created-1--out-6", "source": "entity-created-1", "target": "condition-6", "sourceHandle": "out"}, {"id": "condition-6--out-true-1", "source": "condition-6", "target": "test-stub-11", "sourceHandle": "out-true"}, {"id": "entity-created-1--out-7", "source": "entity-created-1", "target": "condition-7", "sourceHandle": "out"}, {"id": "condition-7--out-true-1", "source": "condition-7", "target": "test-stub-13", "sourceHandle": "out-true"}], "nodes": [{"id": "entity-created-1", "data": {"localizedTitle": {"base": "Test Data Types created"}, "topic": "TestDataTypes/created", "subtitle": "@sage/xtrem-workflow/TestDataTypes", "conditions": [{"path": "testDataTypes.id", "value": "string", "operator": "startsWith", "useParameter": false}], "entityName": "@sage/xtrem-workflow/TestDataTypes", "stepVariables": [{"node": "TestDataTypes", "path": "testDataTypes._id", "type": "IntReference", "title": "Test data types id"}, {"path": "testDataTypes.id", "type": "String", "title": "Id"}]}, "type": "entity-created/_generic", "width": 280, "height": 84, "origin": [0.5, 0], "dragging": false, "position": {"x": 140, "y": 0}, "selected": false, "positionAbsolute": {"x": 140, "y": 0}}, {"id": "condition-1", "data": {"localizedTitle": {"base": "equals abc"}, "subtitle": null, "conditions": [{"path": "testDataTypes.str1", "value": "abc", "operator": "equals", "useParameter": false}], "stepVariables": [{"path": "testDataTypes.str1", "type": "String", "title": "Str 1"}]}, "type": "condition/_generic", "width": 120, "height": 80, "origin": [0.5, 0], "dragging": false, "position": {"x": -320, "y": 220}, "selected": false, "positionAbsolute": {"x": -320, "y": 220}}, {"id": "test-stub-1", "data": {"localizedTitle": {"base": "equals abc"}, "subtitle": null, "stepVariables": [{"path": "equalsAbc", "type": "Float", "title": "End test result ({equals abc})"}], "outputVariableName": "equalsAbc"}, "type": "test-stub/_generic", "width": 280, "height": 62, "origin": [0.5, 0], "dragging": false, "position": {"x": -500, "y": 340}, "selected": false, "positionAbsolute": {"x": -500, "y": 340}}, {"id": "condition-2", "data": {"localizedTitle": {"base": "neq abc"}, "subtitle": null, "conditions": [{"path": "testDataTypes.str1", "value": "abc", "operator": "notEqual", "useParameter": false}], "stepVariables": [{"path": "testDataTypes.str1", "type": "String", "title": "Str 1"}]}, "type": "condition/_generic", "width": 120, "height": 80, "origin": [0.5, 0], "dragging": false, "position": {"x": -140, "y": 220}, "selected": false, "positionAbsolute": {"x": -140, "y": 220}}, {"id": "test-stub-3", "data": {"localizedTitle": {"base": "not equals abc"}, "subtitle": null, "stepVariables": [{"path": "notEqualsAbc", "type": "Float", "title": "End test result ({not equals abc})"}], "outputVariableName": "notEqualsAbc"}, "type": "test-stub/_generic", "width": 280, "height": 62, "origin": [0.5, 0], "dragging": false, "position": {"x": -320, "y": 460}, "selected": false, "positionAbsolute": {"x": -320, "y": 460}}, {"id": "condition-3", "data": {"localizedTitle": {"base": "equals"}, "subtitle": null, "conditions": [{"path": "testDataTypes.str1", "value": "testDataTypes.str2", "operator": "equals", "useParameter": true}], "stepVariables": [{"path": "testDataTypes.str1", "type": "String", "title": "Str 1"}, {"path": "testDataTypes.str2", "type": "String", "title": "Str 2"}]}, "type": "condition/_generic", "width": 120, "height": 80, "origin": [0.5, 0], "dragging": false, "position": {"x": 40, "y": 220}, "selected": false, "positionAbsolute": {"x": 40, "y": 220}}, {"id": "test-stub-5", "data": {"localizedTitle": {"base": "equals"}, "subtitle": null, "stepVariables": [{"path": "equals", "type": "Float", "title": "End test result ({equals})"}], "outputVariableName": "equals"}, "type": "test-stub/_generic", "width": 280, "height": 62, "origin": [0.5, 0], "dragging": false, "position": {"x": -140, "y": 340}, "selected": false, "positionAbsolute": {"x": -140, "y": 340}}, {"id": "condition-4", "data": {"localizedTitle": {"base": "neq str2"}, "subtitle": null, "conditions": [{"path": "testDataTypes.str1", "value": "testDataTypes.str2", "operator": "notEqual", "useParameter": true}], "stepVariables": [{"path": "testDataTypes.str1", "type": "String", "title": "Str 1"}, {"path": "testDataTypes.str2", "type": "String", "title": "Str 2"}]}, "type": "condition/_generic", "width": 120, "height": 80, "origin": [0.5, 0], "dragging": false, "position": {"x": 220, "y": 220}, "selected": false, "positionAbsolute": {"x": 220, "y": 220}}, {"id": "test-stub-7", "data": {"localizedTitle": {"base": "not equals"}, "subtitle": null, "stepVariables": [{"path": "notEquals", "type": "Float", "title": "End test result ({not equals})"}], "outputVariableName": "notEquals"}, "type": "test-stub/_generic", "width": 280, "height": 62, "origin": [0.5, 0], "dragging": false, "position": {"x": 40, "y": 460}, "selected": false, "positionAbsolute": {"x": 40, "y": 460}}, {"id": "condition-5", "data": {"localizedTitle": {"base": "contains"}, "subtitle": null, "conditions": [{"path": "testDataTypes.str1", "value": "testDataTypes.str2", "operator": "contains", "useParameter": true}], "stepVariables": [{"path": "testDataTypes.str1", "type": "String", "title": "Str 1"}, {"path": "testDataTypes.str2", "type": "String", "title": "Str 2"}]}, "type": "condition/_generic", "width": 120, "height": 80, "origin": [0.5, 0], "dragging": false, "position": {"x": 400, "y": 220}, "selected": false, "positionAbsolute": {"x": 400, "y": 220}}, {"id": "test-stub-9", "data": {"localizedTitle": {"base": "contains"}, "subtitle": null, "stepVariables": [{"path": "contains", "type": "Float", "title": "End test result ({contains})"}], "outputVariableName": "contains"}, "type": "test-stub/_generic", "width": 280, "height": 62, "origin": [0.5, 0], "dragging": false, "position": {"x": 220, "y": 340}, "selected": false, "positionAbsolute": {"x": 220, "y": 340}}, {"id": "condition-6", "data": {"localizedTitle": {"base": "startsWith"}, "subtitle": null, "conditions": [{"path": "testDataTypes.str1", "value": "testDataTypes.str2", "operator": "startsWith", "useParameter": true}], "stepVariables": [{"path": "testDataTypes.str1", "type": "String", "title": "Str 1"}, {"path": "testDataTypes.str2", "type": "String", "title": "Str 2"}]}, "type": "condition/_generic", "width": 120, "height": 80, "origin": [0.5, 0], "dragging": false, "position": {"x": 580, "y": 220}, "selected": false, "positionAbsolute": {"x": 580, "y": 220}}, {"id": "test-stub-11", "data": {"localizedTitle": {"base": "starts with"}, "subtitle": null, "stepVariables": [{"path": "startsWith", "type": "Float", "title": "End test result ({starts with})"}], "outputVariableName": "startsWith"}, "type": "test-stub/_generic", "width": 280, "height": 62, "origin": [0.5, 0], "dragging": false, "position": {"x": 400, "y": 460}, "selected": false, "positionAbsolute": {"x": 400, "y": 460}}, {"id": "condition-7", "data": {"localizedTitle": {"base": "endsWith"}, "subtitle": null, "conditions": [{"path": "testDataTypes.str1", "value": "testDataTypes.str2", "operator": "endsWith", "useParameter": true}], "stepVariables": [{"path": "testDataTypes.str1", "type": "String", "title": "Str 1"}, {"path": "testDataTypes.str2", "type": "String", "title": "Str 2"}]}, "type": "condition/_generic", "width": 120, "height": 80, "origin": [0.5, 0], "dragging": false, "position": {"x": 780, "y": 220}, "selected": false, "positionAbsolute": {"x": 780, "y": 220}}, {"id": "test-stub-13", "data": {"localizedTitle": {"base": "ends with"}, "subtitle": null, "stepVariables": [{"path": "endsWith", "type": "Float", "title": "End test result ({ends with})"}], "outputVariableName": "endsWith"}, "type": "test-stub/_generic", "width": 280, "height": 62, "origin": [0.5, 0], "dragging": false, "position": {"x": 600, "y": 340}, "selected": false, "positionAbsolute": {"x": 600, "y": 340}}]}