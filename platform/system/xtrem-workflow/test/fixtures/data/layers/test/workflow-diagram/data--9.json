{"edges": [{"id": "entity-created-1--out-1", "source": "entity-created-1", "target": "condition-1", "sourceHandle": "out"}, {"id": "condition-1--out-true-1", "source": "condition-1", "target": "test-stub-1", "sourceHandle": "out-true"}, {"id": "entity-created-1--out-2", "source": "entity-created-1", "target": "condition-2", "sourceHandle": "out"}, {"id": "condition-2--out-true-1", "source": "condition-2", "target": "test-stub-2", "sourceHandle": "out-true"}, {"id": "entity-created-1--out-3", "source": "entity-created-1", "target": "condition-3", "sourceHandle": "out"}, {"id": "condition-3--out-true-1", "source": "condition-3", "target": "test-stub-3", "sourceHandle": "out-true"}, {"id": "entity-created-1--out-4", "source": "entity-created-1", "target": "condition-4", "sourceHandle": "out"}, {"id": "condition-4--out-true-1", "source": "condition-4", "target": "test-stub-4", "sourceHandle": "out-true"}, {"id": "entity-created-1--out-5", "source": "entity-created-1", "target": "condition-5", "sourceHandle": "out"}, {"id": "condition-5--out-true-1", "source": "condition-5", "target": "test-stub-5", "sourceHandle": "out-true"}, {"id": "entity-created-1--out-6", "source": "entity-created-1", "target": "condition-6", "sourceHandle": "out"}, {"id": "condition-6--out-true-1", "source": "condition-6", "target": "test-stub-6", "sourceHandle": "out-true"}, {"id": "entity-created-1--out-7", "source": "entity-created-1", "target": "condition-7", "sourceHandle": "out"}, {"id": "condition-7--out-true-1", "source": "condition-7", "target": "test-stub-7", "sourceHandle": "out-true"}], "nodes": [{"id": "entity-created-1", "data": {"localizedTitle": {"base": "Test Data Types created"}, "topic": "TestDataTypes/created", "subtitle": "@sage/xtrem-workflow/TestDataTypes", "conditions": [{"path": "testDataTypes.id", "value": "decimal", "operator": "startsWith", "useParameter": false}], "entityName": "@sage/xtrem-workflow/TestDataTypes", "stepVariables": [{"node": "TestDataTypes", "path": "testDataTypes._id", "type": "IntReference", "title": "Test data types id"}, {"path": "testDataTypes.id", "type": "String", "title": "Id"}, {"path": "testDataTypes.decimal1", "type": "Decimal", "title": "Decimal 1"}, {"path": "testDataTypes.decimal2", "type": "Decimal", "title": "Decimal 2"}, {"path": "testDataTypes.decimal3", "type": "Decimal", "title": "Decimal 3"}], "outputVariableName": "testDataTypes"}, "type": "entity-created/_generic", "width": 280, "height": 84, "origin": [0.5, 0], "dragging": false, "position": {"x": 520, "y": 20}, "selected": false, "positionAbsolute": {"x": 520, "y": 20}}, {"id": "condition-1", "data": {"localizedTitle": {"base": "equals"}, "subtitle": null, "conditions": [{"path": "testDataTypes.decimal1", "value": "testDataTypes.decimal2", "operator": "equals", "useParameter": true}], "stepVariables": []}, "type": "condition/_generic", "width": 120, "height": 80, "origin": [0.5, 0], "dragging": false, "position": {"x": 40, "y": 200}, "selected": false, "positionAbsolute": {"x": 40, "y": 200}}, {"id": "test-stub-1", "data": {"localizedTitle": {"base": "equals"}, "subtitle": null, "stepVariables": [{"path": "equals", "type": "Float", "title": "End test result ({equals})"}], "outputVariableName": "equals"}, "type": "test-stub/_generic", "width": 280, "height": 62, "origin": [0.5, 0], "dragging": false, "position": {"x": -140, "y": 320}, "selected": false, "positionAbsolute": {"x": -140, "y": 320}}, {"id": "condition-2", "data": {"localizedTitle": {"base": "not equals"}, "subtitle": null, "conditions": [{"path": "testDataTypes.decimal1", "value": "testDataTypes.decimal2", "operator": "notEqual", "useParameter": true}], "stepVariables": []}, "type": "condition/_generic", "width": 120, "height": 80, "origin": [0.5, 0], "dragging": false, "position": {"x": 220, "y": 200}, "selected": false, "positionAbsolute": {"x": 220, "y": 200}}, {"id": "test-stub-2", "data": {"localizedTitle": {"base": "not equals"}, "subtitle": null, "stepVariables": [{"path": "notEquals", "type": "Float", "title": "End test result ({not equals})"}], "outputVariableName": "notEquals"}, "type": "test-stub/_generic", "width": 280, "height": 62, "origin": [0.5, 0], "dragging": false, "position": {"x": 40, "y": 440}, "selected": false, "positionAbsolute": {"x": 40, "y": 440}}, {"id": "condition-3", "data": {"localizedTitle": {"base": "less than"}, "subtitle": null, "conditions": [{"path": "testDataTypes.decimal1", "value": "testDataTypes.decimal2", "operator": "lessThan", "useParameter": true}], "stepVariables": []}, "type": "condition/_generic", "width": 120, "height": 80, "origin": [0.5, 0], "dragging": false, "position": {"x": 400, "y": 200}, "selected": false, "positionAbsolute": {"x": 400, "y": 200}}, {"id": "test-stub-3", "data": {"localizedTitle": {"base": "less than"}, "subtitle": null, "stepVariables": [{"path": "lessThan", "type": "Float", "title": "End test result ({less than})"}], "outputVariableName": "lessThan"}, "type": "test-stub/_generic", "width": 280, "height": 62, "origin": [0.5, 0], "dragging": false, "position": {"x": 220, "y": 320}, "selected": false, "positionAbsolute": {"x": 220, "y": 320}}, {"id": "condition-4", "data": {"localizedTitle": {"base": "less or equal"}, "subtitle": null, "conditions": [{"path": "testDataTypes.decimal1", "value": "testDataTypes.decimal2", "operator": "lessThanOrEqual", "useParameter": true}], "stepVariables": []}, "type": "condition/_generic", "width": 120, "height": 80, "origin": [0.5, 0], "dragging": false, "position": {"x": 600, "y": 200}, "selected": false, "positionAbsolute": {"x": 600, "y": 200}}, {"id": "test-stub-4", "data": {"localizedTitle": {"base": "less than or equal"}, "subtitle": null, "stepVariables": [{"path": "lessThanOrEqual", "type": "Float", "title": "End test result ({less than or equal})"}], "outputVariableName": "lessThanOrEqual"}, "type": "test-stub/_generic", "width": 280, "height": 62, "origin": [0.5, 0], "dragging": false, "position": {"x": 420, "y": 440}, "selected": false, "positionAbsolute": {"x": 420, "y": 440}}, {"id": "condition-5", "data": {"localizedTitle": {"base": "greater than"}, "subtitle": null, "conditions": [{"path": "testDataTypes.decimal1", "value": "testDataTypes.decimal2", "operator": "greaterThan", "useParameter": true}], "stepVariables": []}, "type": "condition/_generic", "width": 120, "height": 80, "origin": [0.5, 0], "dragging": false, "position": {"x": 780, "y": 200}, "selected": false, "positionAbsolute": {"x": 780, "y": 200}}, {"id": "test-stub-5", "data": {"localizedTitle": {"base": "greater than"}, "subtitle": null, "stepVariables": [{"path": "greaterThan", "type": "Float", "title": "End test result ({greater than})"}], "outputVariableName": "greaterThan"}, "type": "test-stub/_generic", "width": 280, "height": 62, "origin": [0.5, 0], "dragging": false, "position": {"x": 600, "y": 320}, "selected": false, "positionAbsolute": {"x": 600, "y": 320}}, {"id": "condition-6", "data": {"localizedTitle": {"base": "greater or equal"}, "subtitle": null, "conditions": [{"path": "testDataTypes.decimal1", "value": "testDataTypes.decimal2", "operator": "greaterThanOrEqual", "useParameter": true}], "stepVariables": []}, "type": "condition/_generic", "width": 120, "height": 80, "origin": [0.5, 0], "dragging": false, "position": {"x": 980, "y": 200}, "selected": false, "positionAbsolute": {"x": 980, "y": 200}}, {"id": "test-stub-6", "data": {"localizedTitle": {"base": "greater or equal"}, "subtitle": null, "stepVariables": [{"path": "greaterThanOrEqual", "type": "Float", "title": "End test result ({greater or equal})"}], "outputVariableName": "greaterThanOrEqual"}, "type": "test-stub/_generic", "width": 280, "height": 62, "origin": [0.5, 0], "dragging": false, "position": {"x": 800, "y": 440}, "selected": false, "positionAbsolute": {"x": 800, "y": 440}}, {"id": "condition-7", "data": {"localizedTitle": {"base": "between"}, "subtitle": null, "conditions": [{"path": "testDataTypes.decimal1", "value": "testDataTypes.decimal2~testDataTypes.decimal3", "operator": "inRange", "useParameter": true}], "stepVariables": []}, "type": "condition/_generic", "width": 120, "height": 80, "origin": [0.5, 0], "dragging": false, "position": {"x": 1180, "y": 200}, "selected": false, "positionAbsolute": {"x": 1180, "y": 200}}, {"id": "test-stub-7", "data": {"localizedTitle": {"base": "between"}, "subtitle": null, "stepVariables": [{"path": "between", "type": "Float", "title": "End test result ({between})"}], "outputVariableName": "between"}, "type": "test-stub/_generic", "width": 280, "height": 62, "origin": [0.5, 0], "dragging": false, "position": {"x": 1000, "y": 320}, "selected": false, "positionAbsolute": {"x": 1000, "y": 320}}]}