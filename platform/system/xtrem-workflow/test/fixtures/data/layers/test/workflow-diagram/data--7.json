{"edges": [{"id": "entity-created-1--out-1", "source": "entity-created-1", "target": "condition-1", "sourceHandle": "out"}, {"id": "condition-1--out-true-1", "source": "condition-1", "target": "test-stub-1", "sourceHandle": "out-true"}, {"id": "condition-1--out-false-1", "source": "condition-1", "target": "test-stub-2", "sourceHandle": "out-false"}, {"id": "entity-created-1--out-2", "source": "entity-created-1", "target": "condition-2", "sourceHandle": "out"}, {"id": "condition-2--out-true-1", "source": "condition-2", "target": "test-stub-3", "sourceHandle": "out-true"}, {"id": "condition-2--out-false-1", "source": "condition-2", "target": "test-stub-4", "sourceHandle": "out-false"}], "nodes": [{"id": "entity-created-1", "data": {"localizedTitle": {"base": "Test Data Types created"}, "topic": "TestDataTypes/created", "subtitle": "@sage/xtrem-workflow/TestDataTypes", "conditions": [{"path": "testDataTypes.id", "value": "bool", "operator": "startsWith", "useParameter": false}], "entityName": "@sage/xtrem-workflow/TestDataTypes", "stepVariables": [{"path": "testDataTypes.id", "type": "String", "title": "Test Data Types / Id"}, {"node": "TestDataTypes", "path": "testDataTypes._id", "type": "IntReference", "title": "Test Data Types / 🆔"}]}, "type": "entity-created/_generic", "width": 280, "height": 84, "origin": [0.5, 0], "dragging": false, "position": {"x": 540, "y": -40}, "selected": false, "positionAbsolute": {"x": 540, "y": -40}}, {"id": "condition-1", "data": {"localizedTitle": {"base": "bool1 equals true"}, "subtitle": null, "conditions": [{"path": "testDataTypes.bool1", "value": "true", "operator": "matches", "useParameter": false}], "stepVariables": [{"path": "testDataTypes.bool1", "type": "Boolean", "title": "Test Data Types / Bool 1"}]}, "type": "condition/_generic", "width": 250, "height": 72, "origin": [0.5, 0], "dragging": false, "position": {"x": 300, "y": 160}, "selected": false, "positionAbsolute": {"x": 300, "y": 160}}, {"id": "test-stub-1", "data": {"localizedTitle": {"base": "bool1 equals true"}, "subtitle": null, "stepVariables": [{"path": "bool1EqualsTrue", "type": "Float", "title": "End test result ({bool1 equals true})"}], "outputVariableName": "bool1EqualsTrue"}, "type": "test-stub/_generic", "width": 250, "height": 80, "origin": [0.5, 0], "dragging": false, "position": {"x": 80, "y": 300}, "selected": false, "positionAbsolute": {"x": 80, "y": 300}}, {"id": "test-stub-2", "data": {"localizedTitle": {"base": "bool1 equals false"}, "subtitle": null, "stepVariables": [{"path": "bool1EqualsFalse", "type": "Float", "title": "End test result ({bool1 equals false})"}], "outputVariableName": "bool1EqualsFalse"}, "type": "test-stub/_generic", "width": 250, "height": 87, "origin": [0.5, 0], "dragging": false, "position": {"x": 460, "y": 300}, "selected": false, "positionAbsolute": {"x": 460, "y": 300}}, {"id": "condition-2", "data": {"localizedTitle": {"base": "bool1 equals bool2"}, "subtitle": null, "conditions": [{"path": "testDataTypes.bool1", "value": "testDataTypes.bool2", "operator": "matches", "useParameter": true}], "stepVariables": [{"path": "testDataTypes.bool1", "type": "Boolean", "title": "Test Data Types / Bool 1"}, {"path": "testDataTypes.bool2", "type": "Boolean", "title": "Test Data Types / Bool 2"}]}, "type": "condition/_generic", "width": 250, "height": 72, "origin": [0.5, 0], "dragging": false, "position": {"x": 940, "y": 160}, "selected": false, "positionAbsolute": {"x": 940, "y": 160}}, {"id": "test-stub-3", "data": {"localizedTitle": {"base": "bool1 equals bool2"}, "subtitle": null, "stepVariables": [{"path": "bool1EqualsBool2", "type": "Float", "title": "End test result ({bool1 equals bool2})"}], "outputVariableName": "bool1EqualsBool2"}, "type": "test-stub/_generic", "width": 250, "height": 87, "origin": [0.5, 0], "dragging": false, "position": {"x": 740, "y": 300}, "selected": false, "positionAbsolute": {"x": 740, "y": 300}}, {"id": "test-stub-4", "data": {"localizedTitle": {"base": "bool1 not equals bool2"}, "subtitle": null, "stepVariables": [{"path": "bool1NotEqualsBool2", "type": "Float", "title": "End test result ({bool1 not equals bool2})"}], "outputVariableName": "bool1NotEqualsBool2"}, "type": "test-stub/_generic", "width": 250, "height": 87, "origin": [0.5, 0], "dragging": false, "position": {"x": 1120, "y": 300}, "selected": false, "positionAbsolute": {"x": 1120, "y": 300}}]}