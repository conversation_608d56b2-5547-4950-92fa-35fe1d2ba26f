{"edges": [{"id": "test-started-1--out-1", "source": "test-started-1", "target": "read-entity-1", "sourceHandle": "out"}, {"id": "read-entity-1--out-1", "source": "read-entity-1", "target": "test-stub-1", "sourceHandle": "out"}], "nodes": [{"id": "test-started-1", "data": {"localizedTitle": {"en-US": "Start test", "fr-FR": "<PERSON><PERSON><PERSON><PERSON> le test"}, "topic": "WorkflowProcess/testStarted", "parameters": [{"name": "userId", "type": "String"}], "stepVariables": [{"path": "test.parameters.userId", "type": "String", "title": "User id"}]}, "type": "test-started/_generic", "width": 180, "height": 65, "origin": [0.5, 0], "dragging": false, "position": {"x": 500, "y": 40}, "selected": false, "positionAbsolute": {"x": 500, "y": 40}}, {"id": "read-entity-1", "data": {"subtitle": "@sage/xtrem-system/User", "recordKey": "{{test.parameters.userId}}", "entityName": "@sage/xtrem-system/User", "failIfNotFound": false, "stepVariables": [{"path": "user.email", "type": "String", "title": "Email"}, {"path": "user.firstName", "type": "String", "title": "First name"}, {"path": "user.lastName", "type": "String", "title": "Last name"}], "outputVariableName": "user"}, "type": "read-entity/_generic", "width": 213, "height": 69, "origin": [0.5, 0], "dragging": false, "position": {"x": 500, "y": 180}, "selected": false, "positionAbsolute": {"x": 500, "y": 180}}, {"id": "test-stub-1", "data": {"subtitle": null, "stepVariables": [{"path": "done", "type": "Float", "title": "End test result"}], "outputVariableName": "done"}, "type": "test-stub/_generic", "width": 180, "height": 65, "origin": [0.5, 0], "dragging": false, "position": {"x": 500, "y": 320}, "selected": false, "positionAbsolute": {"x": 500, "y": 320}}]}