{"edges": [{"id": "test-started-1--out-1", "source": "test-started-1", "target": "condition-1", "sourceHandle": "out"}, {"id": "condition-1--out-false-1", "source": "condition-1", "target": "test-stub-1", "sourceHandle": "out-false"}, {"id": "condition-1--out-true-1", "source": "condition-1", "target": "test-stub-2", "sourceHandle": "out-true"}], "nodes": [{"id": "test-started-1", "data": {"localizedTitle": {"base": "Start condition test"}, "topic": "WorkflowProcess/testStarted", "parameters": [{"name": "p1", "type": "Decimal"}, {"name": "p2", "type": "Decimal"}], "stepVariables": [{"path": "test.parameters.p1", "type": "Decimal", "title": "P 1"}, {"path": "test.parameters.p2", "type": "Decimal", "title": "P 2"}], "outputVariableName": "test"}, "type": "test-started/_generic", "width": 280, "height": 62, "origin": [0.5, 0], "dragging": false, "position": {"x": 440, "y": -80}, "selected": false, "positionAbsolute": {"x": 440, "y": -80}}, {"id": "condition-1", "data": {"localizedTitle": {"base": "p1 < p2 and p2 < 20"}, "subtitle": null, "conditions": [{"path": "test.parameters.p1", "value": "test.parameters.p2", "operator": "lessThan", "useParameter": true}, {"path": "test.parameters.p2", "value": "20", "operator": "lessThan", "useParameter": false}], "stepVariables": []}, "type": "condition/_generic", "width": 250, "height": 72, "origin": [0.5, 0], "dragging": false, "position": {"x": 440, "y": 100}, "selected": false, "positionAbsolute": {"x": 440, "y": 100}}, {"id": "test-stub-1", "data": {"localizedTitle": {"base": "Nope"}, "subtitle": null, "stepVariables": [{"path": "nope", "type": "Float", "title": "End test result ({Nope})"}], "outputVariableName": "nope"}, "type": "test-stub/_generic", "width": 280, "height": 62, "origin": [0.5, 0], "dragging": false, "position": {"x": 720, "y": 280}, "selected": false, "positionAbsolute": {"x": 720, "y": 280}}, {"id": "test-stub-2", "data": {"localizedTitle": {"base": "Yep"}, "subtitle": null, "stepVariables": [{"path": "yep", "type": "Float", "title": "End test result ({Yep})"}], "outputVariableName": "yep"}, "type": "test-stub/_generic", "width": 280, "height": 62, "origin": [0.5, 0], "dragging": false, "position": {"x": 220, "y": 280}, "selected": false, "positionAbsolute": {"x": 220, "y": 280}}]}