{"edges": [{"id": "entity-created-1--out-1", "source": "entity-created-1", "target": "update-entity-1", "sourceHandle": "out"}], "nodes": [{"id": "entity-created-1", "data": {"localizedTitle": {"base": "Test Data Types created"}, "topic": "TestDataTypes/created", "subtitle": "@sage/xtrem-workflow/TestDataTypes", "conditions": [{"path": "testDataTypes.id", "value": "updateEntity", "operator": "startsWith", "useParameter": false}], "entityName": "@sage/xtrem-workflow/TestDataTypes", "stepVariables": [{"path": "testDataTypes.id", "type": "String", "title": "Test Data Types / Id"}, {"node": "TestDataTypes", "path": "testDataTypes._id", "type": "IntReference", "title": "Test Data Types / 🆔"}], "outputVariableName": "testDataTypes"}, "type": "entity-created/_generic", "width": 250, "height": 116, "origin": [0.5, 0], "dragging": false, "position": {"x": 340, "y": 20}, "selected": false, "positionAbsolute": {"x": 340, "y": 20}}, {"id": "update-entity-1", "data": {"subtitle": null, "entityName": "@sage/xtrem-workflow/TestDataTypes", "updateRules": [{"sourceValue": "testDataTypes.bool1", "destinationPath": "bool2", "isSourceVariable": true}, {"sourceValue": "testDataTypes.date1", "destinationPath": "date2", "isSourceVariable": true}, {"sourceValue": "Hello {{testDataTypes.str1}}!", "destinationPath": "str2", "isSourceVariable": false}], "pathToUpdate": "testDataTypes._id", "stepVariables": [{"node": "TestDataTypes", "path": "testDataTypes._id", "type": "IntReference", "title": "Test Data Types / 🆔"}, {"path": "testDataTypes.bool1", "type": "Boolean", "title": "Test Data Types / Bool 1"}, {"path": "testDataTypes.bool2", "type": "Boolean", "title": "Test Data Types / Bool 2"}, {"path": "testDataTypes.date1", "type": "Date", "title": "Test Data Types / Date 1"}, {"path": "testDataTypes.date2", "type": "Date", "title": "Test Data Types / Date 2"}, {"path": "testDataTypes.str1", "type": "String", "title": "Test Data Types / Str 1"}, {"path": "testDataTypes.str2", "type": "String", "title": "Test Data Types / Str 2"}], "variablesToUpdate": [{"path": "testDataTypes.bool2", "type": "Boolean", "title": "Test Data Types / Bool 2"}, {"path": "testDataTypes.date2", "type": "Date", "title": "Test Data Types / Date 2"}, {"path": "testDataTypes.str2", "type": "String", "title": "Test Data Types / Str 2"}]}, "type": "update-entity/_generic", "width": 250, "height": 80, "origin": [0.5, 0], "dragging": false, "position": {"x": 340, "y": 180}, "selected": false, "positionAbsolute": {"x": 340, "y": 180}}]}