{"edges": [{"id": "entity-created/_generic-1--out-1", "source": "entity-created/_generic-1", "target": "mutation/_generic-1", "sourceHandle": "out"}], "nodes": [{"id": "entity-created/_generic-1", "data": {"topic": "TestNodeForMutation/created", "subtitle": "", "conditions": [], "entityName": "@sage/xtrem-workflow/TestNodeForMutation", "stepVariables": [{"node": "TestNodeForMutation", "path": "testNodeForMutation._id", "type": "IntReference", "title": "Test Node For Mutation / 🆔", "isCustom": false}, {"path": "testNodeForMutation.id", "type": "String", "title": "Test Node For Mutation / Id", "isCustom": false}], "localizedTitle": {"base": "Test Person For Mutation created", "en-US": "Test Node For Mutation created"}}, "type": "entity-created/_generic", "width": 250, "height": 86, "dragging": false, "position": {"x": 140, "y": 40}, "selected": false, "positionAbsolute": {"x": 140, "y": 40}}, {"id": "mutation/_generic-1", "data": {"selector": "{ _id id valInt valStr valEnum valBool }", "subtitle": null, "stepVariables": [{"node": "TestNodeForMutation", "path": "testNodeForMutation._id", "type": "IntReference", "title": "Test Node For Mutation / 🆔", "isCustom": false}, {"path": "testNodeForMutation.valStr", "type": "String", "title": "Test Node For Mutation / Val str", "isCustom": false}, {"node": "TestNodeForMutation", "path": "updatedNode._id", "type": "reference", "title": "updatedNode"}], "localizedTitle": {}, "actionParameters": [{"name": "strPrefix", "value": "testNodeForMutation.valStr", "isVariable": true}, {"name": "newEnumVal", "value": "value2", "isVariable": false}], "mutationArguments": [{"name": "nodeToUpdate", "node": "TestNodeForMutation", "type": "reference", "value": "testNodeForMutation._id", "origin": "fromVariable"}, {"name": "intStep", "type": "integer", "value": 5, "origin": "manual", "isMandatory": true}, {"name": "strPrefix", "type": "string", "origin": "fromParameter", "isMandatory": true}, {"name": "newEnumVal", "type": "enum", "origin": "fromParameter", "isMandatory": true}], "mutationNaturalKey": "TestNodeForMutation|testMutation", "outputVariableName": "updatedNode"}, "type": "mutation/_generic", "width": 250, "height": 80, "dragging": false, "position": {"x": 140, "y": 160}, "selected": false, "positionAbsolute": {"x": 140, "y": 160}}]}