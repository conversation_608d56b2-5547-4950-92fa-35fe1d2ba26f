declare module '@sage/xtrem-workflow-api-partial' {
    import type { Package as SageXtremAuditing$Package } from '@sage/xtrem-auditing-api';
    import type { Package as SageXtremAuthorization$Package } from '@sage/xtrem-authorization-api';
    import type { Package as SageXtremCommunication$Package } from '@sage/xtrem-communication-api';
    import type { Package as SageXtremCustomization$Package } from '@sage/xtrem-customization-api';
    import type { Package as SageXtremDashboard$Package } from '@sage/xtrem-dashboard-api';
    import type {
        MetaNodeFactory,
        MetaServiceOption,
        Package as SageXtremMetadata$Package,
    } from '@sage/xtrem-metadata-api';
    import type { Package as SageXtremRouting$Package } from '@sage/xtrem-routing-api';
    import type { Package as SageXtremSystem$Package, SysVendor, User } from '@sage/xtrem-system-api';
    import type {
        AggregateQueryOperation,
        AggregateReadOperation,
        AsyncOperation,
        ClientCollection,
        ClientNode,
        ClientNodeInput,
        CreateOperation,
        DeleteOperation,
        DuplicateOperation,
        GetDefaultsOperation,
        GetDuplicateOperation,
        Operation as Node$Operation,
        QueryOperation,
        ReadOperation,
        UpdateByIdOperation,
        UpdateOperation,
        integer,
    } from '@sage/xtrem-client';
    export interface WorkflowDefinitionStatus$Enum {
        test: 0;
        production: 1;
    }
    export type WorkflowDefinitionStatus = keyof WorkflowDefinitionStatus$Enum;
    export interface WorkflowMutationArgumentOrigin$Enum {
        fromParameter: 0;
        fromVariable: 1;
        manual: 2;
    }
    export type WorkflowMutationArgumentOrigin = keyof WorkflowMutationArgumentOrigin$Enum;
    export interface WorkflowMutationParameterType$Enum {
        integer: 0;
        decimal: 1;
        string: 2;
        boolean: 3;
        date: 4;
        reference: 5;
        enum: 6;
        other: 7;
    }
    export type WorkflowMutationParameterType = keyof WorkflowMutationParameterType$Enum;
    export interface WorkflowProcessStatus$Enum {
        running: 0;
        success: 1;
        error: 2;
        cancelled: 3;
        suspended: 4;
        shutDown: 5;
        skipped: 6;
    }
    export type WorkflowProcessStatus = keyof WorkflowProcessStatus$Enum;
    export interface WorkflowDefinition extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        id: string;
        name: string;
        isActive: boolean;
        status: WorkflowDefinitionStatus;
        testUser: User;
        diagram: WorkflowDiagram;
        flow: string;
        startTopic: string;
        processes: ClientCollection<WorkflowProcess>;
    }
    export interface WorkflowDefinitionInput extends ClientNodeInput {
        id?: string;
        name?: string;
        isActive?: boolean | string;
        status?: WorkflowDefinitionStatus;
        testUser?: integer | string;
        diagram?: integer | string;
        startTopic?: string;
    }
    export interface WorkflowDefinitionBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        id: string;
        name: string;
        isActive: boolean;
        status: WorkflowDefinitionStatus;
        testUser: User;
        diagram: WorkflowDiagram;
        flow: any;
        startTopic: string;
        processes: ClientCollection<WorkflowProcess>;
    }
    export interface WorkflowDefinition$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface WorkflowDefinition$Lookups {
        testUser: QueryOperation<User>;
        diagram: QueryOperation<WorkflowDiagram>;
    }
    export interface WorkflowDefinition$Operations {
        query: QueryOperation<WorkflowDefinition>;
        read: ReadOperation<WorkflowDefinition>;
        aggregate: {
            read: AggregateReadOperation<WorkflowDefinition>;
            query: AggregateQueryOperation<WorkflowDefinition>;
        };
        create: CreateOperation<WorkflowDefinitionInput, WorkflowDefinition>;
        getDuplicate: GetDuplicateOperation<WorkflowDefinition>;
        duplicate: DuplicateOperation<string, WorkflowDefinitionInput, WorkflowDefinition>;
        update: UpdateOperation<WorkflowDefinitionInput, WorkflowDefinition>;
        updateById: UpdateByIdOperation<WorkflowDefinitionInput, WorkflowDefinition>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: WorkflowDefinition$AsyncOperations;
        lookups(dataOrId: string | { data: WorkflowDefinitionInput }): WorkflowDefinition$Lookups;
        getDefaults: GetDefaultsOperation<WorkflowDefinition>;
    }
    export interface WorkflowDiagram extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        version: string;
        data: string;
    }
    export interface WorkflowDiagramInput extends ClientNodeInput {
        version?: string;
        data?: string;
    }
    export interface WorkflowDiagramBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        version: string;
        data: any;
    }
    export interface WorkflowDiagram$Mutations {
        controlDiagram: Node$Operation<
            {
                nodes: string;
                edges: string;
            },
            | {
                  stepId: string;
                  message: string;
              }[]
            | null
        >;
    }
    export interface WorkflowDiagram$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface WorkflowDiagram$Operations {
        query: QueryOperation<WorkflowDiagram>;
        read: ReadOperation<WorkflowDiagram>;
        aggregate: {
            read: AggregateReadOperation<WorkflowDiagram>;
            query: AggregateQueryOperation<WorkflowDiagram>;
        };
        create: CreateOperation<WorkflowDiagramInput, WorkflowDiagram>;
        getDuplicate: GetDuplicateOperation<WorkflowDiagram>;
        update: UpdateOperation<WorkflowDiagramInput, WorkflowDiagram>;
        updateById: UpdateByIdOperation<WorkflowDiagramInput, WorkflowDiagram>;
        mutations: WorkflowDiagram$Mutations;
        asyncOperations: WorkflowDiagram$AsyncOperations;
        getDefaults: GetDefaultsOperation<WorkflowDiagram>;
    }
    export interface WorkflowProcess extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        id: string;
        definition: WorkflowDefinition;
        diagram: WorkflowDiagram;
        containerId: string;
        startNotificationId: string;
        originId: string;
        triggeringUser: User;
        status: WorkflowProcessStatus;
        startedAt: string;
        completedAt: string;
        duration: integer;
        errorMessages: string;
        eventLog: string;
        variables: string;
    }
    export interface WorkflowProcessInput extends ClientNodeInput {
        id?: string;
        definition?: integer | string;
        diagram?: integer | string;
        containerId?: string;
        startNotificationId?: string;
        originId?: string;
        triggeringUser?: integer | string;
        status?: WorkflowProcessStatus;
        startedAt?: string;
        completedAt?: string;
    }
    export interface WorkflowProcessBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        id: string;
        definition: WorkflowDefinition;
        diagram: WorkflowDiagram;
        containerId: string;
        startNotificationId: string;
        originId: string;
        triggeringUser: User;
        status: WorkflowProcessStatus;
        startedAt: string;
        completedAt: string;
        duration: integer;
        errorMessages: string;
        eventLog: any;
        variables: any;
    }
    export interface WorkflowProcess$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface WorkflowProcess$Lookups {
        definition: QueryOperation<WorkflowDefinition>;
        diagram: QueryOperation<WorkflowDiagram>;
        triggeringUser: QueryOperation<User>;
    }
    export interface WorkflowProcess$Operations {
        query: QueryOperation<WorkflowProcess>;
        read: ReadOperation<WorkflowProcess>;
        aggregate: {
            read: AggregateReadOperation<WorkflowProcess>;
            query: AggregateQueryOperation<WorkflowProcess>;
        };
        create: CreateOperation<WorkflowProcessInput, WorkflowProcess>;
        getDuplicate: GetDuplicateOperation<WorkflowProcess>;
        update: UpdateOperation<WorkflowProcessInput, WorkflowProcess>;
        updateById: UpdateByIdOperation<WorkflowProcessInput, WorkflowProcess>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: WorkflowProcess$AsyncOperations;
        lookups(dataOrId: string | { data: WorkflowProcessInput }): WorkflowProcess$Lookups;
        getDefaults: GetDefaultsOperation<WorkflowProcess>;
    }
    export interface WorkflowStepTemplate extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        stepConstructor: string;
        variant: string;
        factory: MetaNodeFactory;
        isActive: boolean;
        title: string;
        description: string;
        icon: WorkflowStepIcon;
        color: string;
        serviceOptions: MetaServiceOption[];
        configData: string;
        stepDescriptor: string;
    }
    export interface WorkflowStepTemplateInput extends ClientNodeInput {
        _vendor?: integer | string;
        stepConstructor?: string;
        variant?: string;
        factory?: integer | string;
        isActive?: boolean | string;
        title?: string;
        description?: string;
        icon?: WorkflowStepIcon;
        color?: string;
        serviceOptions?: (integer | string)[];
        configData?: string;
    }
    export interface WorkflowStepTemplateBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        stepConstructor: string;
        variant: string;
        factory: MetaNodeFactory;
        isActive: boolean;
        title: string;
        description: string;
        icon: WorkflowStepIcon;
        color: string;
        serviceOptions: MetaServiceOption[];
        configData: any;
        stepDescriptor: any;
    }
    export interface WorkflowStepTemplate$Mutations {
        registerStepConfiguration: Node$Operation<
            {
                stepConstructor: string;
                asSetupNode: boolean | string;
                factory: string;
                localizedTitle: string;
                localizedDescription: string;
                configData: string;
            },
            WorkflowStepTemplate
        >;
    }
    export interface WorkflowStepTemplate$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface WorkflowStepTemplate$Lookups {
        _vendor: QueryOperation<SysVendor>;
        factory: QueryOperation<MetaNodeFactory>;
    }
    export interface WorkflowStepTemplate$Operations {
        query: QueryOperation<WorkflowStepTemplate>;
        read: ReadOperation<WorkflowStepTemplate>;
        aggregate: {
            read: AggregateReadOperation<WorkflowStepTemplate>;
            query: AggregateQueryOperation<WorkflowStepTemplate>;
        };
        create: CreateOperation<WorkflowStepTemplateInput, WorkflowStepTemplate>;
        getDuplicate: GetDuplicateOperation<WorkflowStepTemplate>;
        update: UpdateOperation<WorkflowStepTemplateInput, WorkflowStepTemplate>;
        updateById: UpdateByIdOperation<WorkflowStepTemplateInput, WorkflowStepTemplate>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        mutations: WorkflowStepTemplate$Mutations;
        asyncOperations: WorkflowStepTemplate$AsyncOperations;
        lookups(dataOrId: string | { data: WorkflowStepTemplateInput }): WorkflowStepTemplate$Lookups;
        getDefaults: GetDefaultsOperation<WorkflowStepTemplate>;
    }
    export interface Package {
        '@sage/xtrem-workflow/WorkflowDefinition': WorkflowDefinition$Operations;
        '@sage/xtrem-workflow/WorkflowDiagram': WorkflowDiagram$Operations;
        '@sage/xtrem-workflow/WorkflowProcess': WorkflowProcess$Operations;
        '@sage/xtrem-workflow/WorkflowStepTemplate': WorkflowStepTemplate$Operations;
    }
    export interface GraphApi
        extends Package,
            SageXtremAuditing$Package,
            SageXtremAuthorization$Package,
            SageXtremCommunication$Package,
            SageXtremCustomization$Package,
            SageXtremDashboard$Package,
            SageXtremMetadata$Package,
            SageXtremRouting$Package,
            SageXtremSystem$Package {}
}
declare module '@sage/xtrem-workflow-api' {
    export type * from '@sage/xtrem-workflow-api-partial';
}
declare module '@sage/xtrem-auditing-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-workflow-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-authorization-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-workflow-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-communication-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-workflow-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-customization-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-workflow-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-dashboard-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-workflow-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-metadata-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-workflow-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-routing-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-workflow-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-system-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-workflow-api';
    export interface GraphApi extends GraphApiExtension {}
}
