"step_constructor";"variant";"_vendor";"is_active";"title";"description";"icon";"color";"service_options"
"entity-created";"_generic";"sage";"Y";"{""base"":""Entity created""}";"{""base"":""This event is triggered when a new instance of the selected entity type is created in the system.""}";"addons";"335b70ff";"[]"
"entity-deleted";"_generic";"sage";"Y";"{""base"":""Entity deleted""}";"{""base"":""This event is triggered when an instance of the selected entity type is deleted from the system.""}";"undo";"000000ff";"[]"
"entity-updated";"_generic";"sage";"Y";"{""base"":""Entity updated""}";"{""base"":""This event is triggered when an instance of the selected entity type is updated in the system.""}";"bright";"0060a7ff";"[]"
"test-started";"_generic";"sage";"Y";"{""base"":""Test scenario started""}";"{""base"":""This event is triggered when a workflow test scenario starts.""}";"binocular";"000000ff";"[]"
"condition";"_generic";"sage";"Y";"{""base"":""Condition""}";"{""base"":""Allows the workflow logic to be branched based on a condition.""}";"connected";"ef6700ff";"[]"
"calculate";"_generic";"sage";"Y";"{""base"":""Calculate""}";"{""base"":""Calculates a value using simple mathematical operations using variable from the previous steps.""}";"accounting";"335b70ff";"[]"
"delete-entity";"_generic";"sage";"Y";"{""base"":""Delete entity""}";"{""base"":""Deletes an entity from the database.""}";"undo";"335b70ff";"[]"
"read-entity";"_generic";"sage";"Y";"{""base"":""Read record""}";"{""base"":""Reads a record by its ID field and use its value in subsequent steps.""}";"binocular";"335b70ff";"[]"
"schedule";"_generic";"sage";"Y";"{""base"":""Schedule""}";"{""base"":""This event is triggered on based on a predefined schedule.""}";"clock";"0060a7ff";"[]"
"send-user-notification";"_generic";"sage";"Y";"{""base"":""Send notification""}";"{""base"":""This action sends a notification to a set of users.""}";"megaphone";"335b70ff";"[]"
"test-stub";"_generic";"sage";"Y";"{""base"":""Test stub""}";"{""base"":""Ends the test scenario.""}";"megaphone";"335b70ff";"[]"
"update-entity";"_generic";"sage";"Y";"{""base"":""Update entity""}";"{""base"":""Updates a record in the database.""}";"pencil";"335b70ff";"[]"
"wait";"_generic";"sage";"Y";"{""base"":""Wait""}";"{""base"":""Waits for a predefined period before taking any further actions.""}";"hourglass";"335b70ff";"[""workflowAdvanced""]"
"mutation";"_generic";"sage";"Y";"{""base"":""Mutation""}";"{""base"":""Allows the workflow logic to execute a mutation.""}";"connected";"ef6700ff";"[""workflowAdvanced""]"
