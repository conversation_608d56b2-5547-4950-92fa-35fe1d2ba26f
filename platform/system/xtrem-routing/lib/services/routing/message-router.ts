import * as xtremCommunication from '@sage/xtrem-communication';
import {
    Application,
    asyncArray,
    asyncFireAndForget,
    Context,
    datetime,
    Dict,
    PubSub,
    SqsSendQueue,
    TextStream,
    withAdvisoryLock,
} from '@sage/xtrem-core';
import { logger } from '../utils';

// The routers do not get their queues from the application because it may be run from a separate routing service
// which does not have all the queues in its configuration.
// Instead, routers create their SQS send queues dynamically

/** @internal */
export abstract class MessageRouter {
    /** The map of names to queues */
    private static queuesByName: Dict<SqsSendQueue> = {};

    /** Returns a send queue by looking up the map and creating a new queue if necessary */
    protected static makeQueue(name: string): SqsSendQueue {
        if (!this.queuesByName[name]) {
            this.queuesByName[name] = new SqsSendQueue({
                name,
                description: `routing queue ${name}`,
            });
        }
        return this.queuesByName[name];
    }

    // This function is called when we get notified that a message was
    // added to the SysMessage table by a call to context.send.
    // There may be several messages in the SysMessage table
    // We dequeue them and post them to their SQS queue.
    private static async onMessageQueuedAsync(application: Application): Promise<void> {
        logger.info('Starting message forwarding from SysMessage to SQS queues');

        Application.emitter.emit('messageForwardingStart', { application });

        // Note: the lock number was randomly generated. If this key is changed, update in message-test.ts
        const advisoryLockId = ****************;
        // TODO: make this configurable
        const pageSize = 1000;
        let done = false;
        while (!done) {
            await application.asRoot.withCommittedContext(
                null,
                // eslint-disable-next-line @typescript-eslint/no-loop-func
                context =>
                    withAdvisoryLock(context, advisoryLockId, async () => {
                        const messages = await context
                            .query(xtremCommunication.nodes.SysMessage, {
                                skipLocked: true,
                                forUpdate: true,
                                filter: { status: { _eq: 'pending' } },
                                first: pageSize,
                            })
                            .toArray();
                        done = messages.length < pageSize;

                        logger.verbose(() => `Message forwarding: processing ${messages.length} messages`);

                        await asyncArray(messages).forEach(async message => {
                            const envelope: xtremCommunication.MessageEnvelope = {
                                payload: (await message.payload).value,
                                attributes: await message.attributes,
                                deduplicationId: await message.messageId,
                            };
                            await this.trySendForTenant(context, message, envelope);
                        });
                    }),
                { description: () => 'MessageRouter.onMessageQueued' },
            );
        }
        Application.emitter.emit('messageForwardingStop', { application });
    }

    private static onMessageQueued(application: Application): void {
        asyncFireAndForget(
            () => this.onMessageQueuedAsync(application),
            'Failed to route messages to SQS queues',
            logger,
        );
    }

    private static async trySendForTenant(
        context: Context,
        message: xtremCommunication.nodes.SysMessage,
        envelope: xtremCommunication.MessageEnvelope,
    ): Promise<void> {
        const oldTenantId = context.tenantId;
        const { application } = context;
        try {
            await context.setTenantId(await message.tenantId);
            await this.trySend(context, message, envelope);
            Application.emitter.emit('messageSent', { application });
        } catch (error) {
            // Failed if we cannot set the tenant id, we skip it because it means it is an invalid one.
            // This can happen when a tenant has been deleted
            Application.emitter.emit('messageSendFailed', { application });
            logger.error(error.stack);
        } finally {
            await message.$.delete();
            await context.setTenantId(oldTenantId);
        }
    }

    private static async trySend(
        context: Context,
        message: xtremCommunication.nodes.SysMessage,
        envelope: xtremCommunication.MessageEnvelope,
    ): Promise<void> {
        try {
            const queue = this.makeQueue(await message.queue);

            await queue.send(context, envelope);
        } catch (error) {
            logger.error(error.stack);
            await xtremCommunication.nodes.SysMessageHistory.createOrUpdateMessageHistory(context, {
                integrationSolution: await message.queue,
                receivedStamp: datetime.now(),
                attributes: envelope.attributes,
                receivedRequest: TextStream.fromJsonObject(envelope),
                errorStack: error && TextStream.fromString(error.stack || error.message),
                errorMessage: error && error.message,
                status: 'error',
            });
        }
    }

    private static isActive = false;

    static async start(application: Application): Promise<void> {
        if (this.isActive) return;
        this.isActive = true;
        // previous test may have subscribed so we need to unsubscribe first.
        if (await PubSub.isSubscribed('message_queued')) await PubSub.unsubscribe('message_queued');
        await PubSub.subscribe('message_queued', () => this.onMessageQueued(application));
    }
}
