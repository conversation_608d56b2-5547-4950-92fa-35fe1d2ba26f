import * as xtremCommunication from '@sage/xtrem-communication';
import {
    Application,
    asyncArray,
    asyncFireAndForget,
    ConfigManager,
    Context,
    Dict,
    NotificationQueue,
    PubSub,
    PubSubPayload,
    withAdvisoryLock,
} from '@sage/xtrem-core';
import * as fs from 'fs';
import * as path from 'path';
import { logger, sleepMillis } from '../utils';

interface ProcessNotificationCounters {
    all: number;
    sent: number;
    skiped: number;
}

export interface Route {
    queue: string;
    topic: string;
    sourceFileName: string;
}

/** @internal */
export interface NotificationRoutePayload extends PubSubPayload {
    event: 'created';
    extraRoutes: Route[];
}

/** @internal */
export abstract class NotificationRouter {
    /** The map of names to queues */
    private static queuesByName: Dict<NotificationQueue> = {};

    /** Returns a send queue by looking up the map and creating a new queue if necessary */
    protected static makeQueue(application: Application, name: string): NotificationQueue {
        if (!this.queuesByName[name]) {
            this.queuesByName[name] = new NotificationQueue({
                name,
                sqsName: application.sqsQueueName(name),
                description: `routing queue ${name}`,
            });
        }
        return this.queuesByName[name];
    }

    private static queuesByTopic: Dict<NotificationQueue[]> = {};

    private static refreshRouting(application: Application, extraRoutes: Route[] = []): void {
        logger.info('[QRouter] Refreshing routing table');
        const routingFilePath = path.join(application.dir, 'routing.json');
        if (!fs.existsSync(routingFilePath)) return;
        const routing = JSON.parse(fs.readFileSync(routingFilePath, 'utf-8'));
        const testRoutingFilePath = path.join(application.dir, 'test/fixtures/routing.json');
        if (
            application.applicationType === 'test' &&
            !application.options.startOptions?.queues &&
            fs.existsSync(testRoutingFilePath)
        ) {
            const testRouting = JSON.parse(fs.readFileSync(testRoutingFilePath, 'utf-8')) as Dict<Route[]>;
            Object.entries(testRouting).forEach(([packageName, testRoutes]) => {
                if (!routing[packageName]) routing[packageName] = [];
                routing[packageName].push(...testRoutes);
            });
        }

        let routes: { queue: string; topic: string }[] = extraRoutes;
        Object.keys(routing).forEach(packageName => {
            routes = [...routes, ...routing[packageName]];
        });

        // If the application was started with a --queues flag, then only the specified queues
        // have been started
        const specificQueuesToStart = application.startOptions.queues;
        if (specificQueuesToStart != null && specificQueuesToStart.length > 0) {
            logger.warn(
                `The application was started with --queues=xx, only the routes for the following queues will be considered: ${specificQueuesToStart.join(
                    ',',
                )}`,
            );
        }

        routes.forEach(route => {
            const name = route.queue;
            if (specificQueuesToStart != null && !specificQueuesToStart.includes(name)) {
                logger.verbose(
                    () => `Route ${name} was skipped (it's queue was not started, according to the --queues flag)`,
                );
                return;
            }
            const queue = this.makeQueue(application, name);
            const queues = this.queuesByTopic[route.topic] || (this.queuesByTopic[route.topic] = []);
            if (!queues.includes(queue)) {
                queues.push(queue);
                logger.info(
                    `[QRouter] Added topic ${route.topic} for queue ${queue.name} (#${queues.length}) to routing table`,
                );
            }
        });
    }

    /**
     * Sends all notifications to their SQS queues
     *
     * @param context
     * @param notifications
     * @returns true if notifications was successfully processed
     */
    private static async processNotifications(
        context: Context,
        notifications: xtremCommunication.nodes.SysNotification[],
    ): Promise<ProcessNotificationCounters> {
        const counters = { all: notifications.length, sent: 0, skiped: 0 };
        await asyncArray(notifications).forEach(async notification => {
            const queues = this.queuesByTopic[await notification.topic];
            const payload = (await notification.payload).value;

            const attributes = await xtremCommunication.functions.getAttributesFromNotification(notification);
            const envelope: xtremCommunication.NotificationEnvelope = {
                payload,
                attributes,
                deduplicationId: await notification.notificationId,
            };
            let toDelete = true;
            try {
                await context.setTenantId(await notification.tenantId);
                if (await this.trySend(context, queues, notification, envelope)) {
                    counters.sent += 1;
                    Application.emitter.emit('notificationSent', { application: context.application, payload });
                } else {
                    // not sent because of a (temporary) error, so we keep the notification to give it a chance to be sent later
                    toDelete = false;
                }
            } catch (error) {
                logger.verbose(
                    () =>
                        `[QRouter] processNotifications ${notification._id} error: sent=${toDelete} - ${error.message}`,
                );
                Application.emitter.emit('notificationSendFailed', { application: context.application, payload });
                // Failed if we cannot set the tenant id, we skip it because it means it is an invalid one.
                // This can happen when a tenant has been deleted
                counters.skiped += 1;
                logger.error(error.stack);
            } finally {
                logger.verbose(
                    () => `[QRouter] processNotifications ${notification._id} finally: toDelete=${toDelete}`,
                );
                if (toDelete) {
                    await notification.$.delete();
                }
            }
        });
        logger.verbose(() => `[QRouter] processNotifications return ${JSON.stringify(counters)}`);
        return counters;
    }

    private static async trySend(
        context: Context,
        queues: NotificationQueue[],
        notification: xtremCommunication.nodes.SysNotification,
        envelope: xtremCommunication.NotificationEnvelope,
    ): Promise<boolean> {
        const storeError = async (error: Error): Promise<void> => {
            // We need to create the history record with a new context constructed with the correct tenant, login and
            // persona (if this is a demo tenant).
            // The context passed in is not initialized to any tenant or user, as we only need this context to query and delete
            // from SysNotifications and to send the messgae to the relevant queue.
            const persona = (await context.supportsPersona()) ? await notification.userEmail : undefined;
            await context.application.withCommittedContext(
                await notification.tenantId,
                historyContext =>
                    xtremCommunication.nodes.SysNotificationState.upsert(historyContext, {
                        envelope,
                        status: 'error',
                        error,
                    }),
                { auth: { login: await notification.login, persona }, description: () => 'NotificationRouter.trySend' },
            );
        };

        try {
            if (!queues) {
                // Cannot route topic to a queue, ignore with a warning
                // Don't throw as we will continously try to reprocess this invalid message over and over
                const message = `Cannot route notification: ${await notification.topic}: queue not found`;
                logger.warn(message);
                await storeError(new Error(message));
            } else {
                await asyncArray(queues).forEach(async queue => {
                    await queue.send(context, envelope);
                });
            }
            return true;
        } catch (error) {
            logger.error(error.stack);
            await storeError(error);
            return false;
        }
    }

    private static async onNotificationQueuedAsync(application: Application): Promise<void> {
        // This function is called when we get notified that a notification was
        // added to the SysNotification table by a call to context.notify.
        // There may be several messages in the SysNotification table
        // We dequeue them and post them to their SQS queue.
        logger.info('[QRouter] Starting message forwarding from SysNotification to SQS queues');

        Application.emitter.emit('notificationRoutingStart', { application });

        const interop = {
            ...{ routingPollingSeconds: 1, routingReadCount: 3 },
            ...ConfigManager.current.interop,
        };
        let routingReadCount = interop.routingReadCount;
        // Note: the lock number was randomly generated. If this key is changed, update in notification-test.ts
        const advisoryLockId = *****************;
        // TODO: make this configurable
        const pageSize = 1000;
        let done = false;
        while (routingReadCount > 0 && !done) {
            const processed = await application.asRoot.withCommittedContext(
                null,
                // eslint-disable-next-line @typescript-eslint/no-loop-func
                context =>
                    // withAdvisoryLock will wait for any current advisory locks to be released, before processing
                    // notifications. This way we ensure exclusivity when processing the notifications.
                    withAdvisoryLock(context, advisoryLockId, async () => {
                        const notifications = await context
                            .query(xtremCommunication.nodes.SysNotification, {
                                skipLocked: true,
                                forUpdate: true,
                                filter: { status: { _eq: 'pending' } },
                                first: pageSize,
                            })
                            .toArray();
                        done = notifications.length < pageSize;

                        logger.info(`[QRouter] Message forwarding: processing ${notifications.length} messages`);

                        const counters = await this.processNotifications(context, notifications);
                        const delta = counters.all - (counters.sent + counters.skiped);
                        // if all notifications have been processed we can stop otherwise we need to retry
                        if (delta === 0) {
                            routingReadCount -= 1;
                            return true;
                        }
                        return false;
                    }),
            );
            if (processed) {
                logger.info('[QRouter] End message forwarding from SysNotification to SQS queues');
                break;
            }
            // No notification read: let's retry later
            await sleepMillis(interop.routingPollingSeconds * 1000);
        }
        Application.emitter.emit('notificationsRoutingStop', { application });
    }

    private static onNotificationQueued(application: Application): void {
        asyncFireAndForget(
            () => this.onNotificationQueuedAsync(application),
            'Failed to route notifications to SQS queues',
            logger,
        );
    }

    private static onNotificationRoute(application: Application) {
        // This function is called when we get notified that notification routes need to be
        // added to the routing table by a call to QueueManager.registerNotificationRoutes.
        return (payload: NotificationRoutePayload): void => {
            try {
                logger.info(`[QRouter] Received routes notification with event '${payload.event}'`);
                if (payload.event === 'created') this.refreshRouting(application, payload.extraRoutes);
            } catch (error) {
                logger.error(error.stack);
            }
        };
    }

    private static onClearTestQueues(application: Application) {
        return async (payload: PubSubPayload & { applicationName: string }) => {
            if (application.name === payload.applicationName)
                try {
                    // Run queue cleanup in parallel to save time
                    await Promise.all(
                        Object.values(this.queuesByName).map(queue => {
                            logger.info(`Clearing test notification queue ${queue.name}`);
                            return queue.clearQueue();
                        }),
                    );
                } catch (err) {
                    logger.error(err.stack);
                }
            Application.emitter.emit('testQueuesCleared', this);
        };
    }

    private static isActive = false;

    /** @internal */
    static async registerNotificationRoutes(application: Application): Promise<void> {
        if (application.applicationType !== 'test') return;
        logger.info(`[QRouter] Registering application routes of ${application.name}`);
        await application.asRoot.withCommittedContext(null, context =>
            PubSub.publish(context, 'notification_route', {
                event: 'created',
                tenantId: null,
            }),
        );
    }

    static async start(application: Application): Promise<void> {
        if (this.isActive) return;
        this.isActive = true;

        // Read the routing tables
        this.refreshRouting(application);

        // previous test may have subscribed so we need to unsubscribe first.
        if (await PubSub.isSubscribed('notification_queued')) await PubSub.unsubscribe('notification_queued');
        await PubSub.subscribe('notification_queued', () => this.onNotificationQueued(application));
        if (await PubSub.isSubscribed('notification_route')) await PubSub.unsubscribe('notification_route');
        await PubSub.subscribe('notification_route', this.onNotificationRoute(application));

        if (application.applicationType === 'test') {
            if (await PubSub.isSubscribed('clear_test_notification_queues'))
                await PubSub.unsubscribe('clear_test_notification_queues');
            await PubSub.subscribe('clear_test_notification_queues', this.onClearTestQueues(application));
        }

        await this.registerNotificationRoutes(application);
    }
}
