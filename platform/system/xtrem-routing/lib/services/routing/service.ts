// This module merges the communication APIs (listeners, queues, context methods) into @sage/xtrem-core
import { Application, ConfigManager } from '@sage/xtrem-core';
import { logger } from '../utils';
import { MessageRouter } from './message-router';
import { NotificationRouter } from './notification-router';

export async function startService(application: Application): Promise<void> {
    if (application.startOptions.services.includes('@sage/xtrem-routing')) {
        logger.info('Starting routing services');
        await MessageRouter.start(application);
        await NotificationRouter.start(application);
    }
}

Application.emitter.once('testListen', (application: Application) => {
    (async () => {
        await MessageRouter.start(application);
        await NotificationRouter.start(application);
    })().catch(err => logger.error(err));
});

/**
 * Listener for the event fired when a file is uploaded using the /dev/upload route
 */
Application.emitter.on(
    'devFileUploaded',
    (data: {
        payload: {
            contextValue: any;
            contextId: string;
            result: string;
            reason: string;
        };
        application: Application;
    }) => {
        const config = ConfigManager.current;

        data.application.asRoot
            .withCommittedContext(config.tenantId || '7'.repeat(21), async context => {
                logger.info(
                    `Development upload: Notify on ${data.payload.contextValue.replyTopic} for contextId ${data.payload.contextId} and result ${data.payload.result}`,
                );
                // Mock infrastructure adapter message listener
                await context.notify(data.payload.contextValue.replyTopic, {
                    ...data.payload,
                });
            })
            .catch(err => logger.error(err.stack));
    },
);
