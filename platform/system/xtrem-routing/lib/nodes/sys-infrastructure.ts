/** @ignore */ /** */
import { MessageEnvelope } from '@sage/xtrem-communication';
import { Context, CoreHooks, decorators, Node } from '@sage/xtrem-core';
import { InfrastructureHelper } from '@sage/xtrem-infrastructure-adapter';
import { infrastructureEventQueue } from '../queues';
import { logger } from '../services/utils';

export interface InfrastructureEnvelope {
    tenantId: string;
    contextId: string;
    result: string;
    reason?: string;
    /**
     * JSON when the event timed out, we receive the orginal data from the context since it's deleted on dynamoDB
     */
    contextJson?: any;
}

@decorators.node<SysInfrastructure>({
    isPublished: false,
    storage: 'json',
    isPlatformNode: true,
})
export class SysInfrastructure extends Node {
    @decorators.messageListener<typeof SysInfrastructure, InfrastructureEnvelope>({
        queue: () => infrastructureEventQueue,
        integrationSolution: 'erp-adapter',
        format: 'json',
        getTenantId(envelope) {
            return envelope.payload.tenantId;
        },

        async getContextAttributes(context, envelope) {
            const asyncContext = await InfrastructureHelper.getAsyncContext(envelope.payload.contextId);
            logger.info(
                `Infrastructure Event: Trying to extract context attributes for context ${envelope.payload.contextId}`,
            );
            let contextValue = asyncContext?.contextXtrem || {};
            // If result is 'timeout' then the context has already been deleted
            if (envelope.payload.result === 'timeout') {
                contextValue = envelope.payload.contextJson || {};
            }

            logger.info(
                `Infrastructure Event Received: 
                contextId= ${envelope.payload.contextId}, 
                tenantId= ${envelope.payload.tenantId}, 
                result= ${envelope.payload.result}, 
                contextValue= ${JSON.stringify(contextValue)}`,
            );

            if (contextValue.user) {
                // We use any here as we do not have the User node in the dependencies.
                // We use toUserInfo as it awaits all the properties needed in the UserInfo object.
                const user = (
                    (await context.read(CoreHooks.sysManager.getUserNode(), {
                        _id: contextValue.user,
                    })) as any
                ).toUserInfo();

                return {
                    user,
                    locale: 'base',
                };
            }

            throw new Error(`No user supplied in context. payload=${envelope.payload}`);
        },
        getId(_context, envelope) {
            return envelope.payload.contextId;
        },
    })
    static async onResponseFromThirdParty(
        context: Context,
        data: MessageEnvelope<InfrastructureEnvelope>,
    ): Promise<void> {
        logger.info(`onResponseFromThirdParty-${JSON.stringify(data.payload)}`);
        // We create the context JSON when creating the async context
        // - Uploading a file
        // - Print service request ...
        // See https://confluence.sage.com/display/XTREEM/Async+Context+Library
        const asyncContext = await InfrastructureHelper.getAsyncContext(data.payload.contextId);

        // contextValue is a JSON object we set when creating the context
        // In our api we enforce that this data has a replyTopic
        // See https://confluence.sage.com/pages/viewpage.action?spaceKey=XTREEM&title=Async+Context+Module
        let contextValue = asyncContext?.contextXtrem || {};
        // If result is timeOut then the context has already been deleted
        if (data.payload.result === 'timeout') {
            contextValue = data.payload.contextJson || {};
        }

        if (!contextValue.replyTopic) {
            // If result is 'timeout' then the context has already been deleted
            // If there is not replyTopic, we can't action it, so delete the context
            if (data.payload.result !== 'timeout') {
                await InfrastructureHelper.deleteAsyncContext(data.payload.contextId);
            }
            throw new Error(`No replyTopic supplied in context. payload=${data.payload}`);
        }

        // Notify on reply topic
        await context.notify(contextValue.replyTopic, {
            contextValue,
            contextId: data.payload.contextId,
            result: data.payload.result,
            reason: data.payload.reason,
        });
    }
}
