module.exports = {
    extends: '../../.eslintrc-base.js',
    parserOptions: {
        tsconfigRootDir: __dirname,
        project: 'tsconfig.json',
        extraFileExtensions: ['.json'],
    },
    overrides: [
        {
            files: ['lib/**/*.ts'],
            rules: {
                // same rule as xtrem-core
                '@typescript-eslint/explicit-function-return-type': [
                    'error',
                    {
                        allowExpressions: true,
                    },
                ],
            },
        },
        {
            files: ['test/graphql/**/*.json'],
            extends: ['plugin:@sage/xtrem/recommended-json'],
        },
    ],
};
