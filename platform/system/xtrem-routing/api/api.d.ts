declare module '@sage/xtrem-routing-api-partial' {
    import type { Package as SageXtremCommunication$Package } from '@sage/xtrem-communication-api';
    import type { Package as SageXtremSystem$Package } from '@sage/xtrem-system-api';
    export interface Package {}
    export interface GraphApi extends Package, SageXtremCommunication$Package, SageXtremSystem$Package {}
}
declare module '@sage/xtrem-routing-api' {
    export type * from '@sage/xtrem-routing-api-partial';
}
declare module '@sage/xtrem-communication-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-routing-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-system-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-routing-api';
    export interface GraphApi extends GraphApiExtension {}
}
