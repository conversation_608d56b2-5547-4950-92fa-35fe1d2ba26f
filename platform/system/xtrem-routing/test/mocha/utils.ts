import { ConfigManager, initTables, NotificationQueue, Test } from '@sage/xtrem-core';

export function configureTestQueues() {
    if (
        !ConfigManager.current.interop?.queues?.testReceiveQueue?.url ||
        !ConfigManager.current.interop?.queues?.testSendQueue?.url ||
        !ConfigManager.current.interop?.queues?.testVisibilityMultipleListenersSendQueue?.url ||
        !ConfigManager.current.interop?.queues?.testVisibilityMultipleListenersReceiveQueue1?.url ||
        !ConfigManager.current.interop?.queues?.testVisibilityMultipleListenersReceiveQueue2?.url
    ) {
        // eslint-disable-next-line no-console
        console.log(
            '  WARN: skip queues test: testVisibilityMultipleListenersSendQueue, testVisibilityMultipleListenersReceiveQueue1, testVisibilityMultipleListenersReceiveQueue2, testReceiveQueue, and testSendQueue queues must have a url defined',
        );
        return false;
    }

    if (
        ConfigManager.current.interop?.queues?.testReceiveQueue?.url !==
        ConfigManager.current.interop?.queues?.testSendQueue?.url
    )
        throw new Error(
            'testReceiveQueue and testSendQueue are different, this will break the send/receive queue loop used by the unit tests.',
        );

    if (
        ConfigManager.current.interop?.queues?.testVisibilityMultipleListenersSendQueue?.url !==
            ConfigManager.current.interop?.queues?.testVisibilityMultipleListenersReceiveQueue1?.url &&
        ConfigManager.current.interop?.queues?.testVisibilityMultipleListenersSendQueue?.url !==
            ConfigManager.current.interop?.queues?.testVisibilityMultipleListenersReceiveQueue2?.url
    )
        throw new Error(
            'testVisibilityMultipleListenersSendQueue, testVisibilityMultipleListenersReceiveQueue1 and testVisibilityMultipleListenersReceiveQueue2 are different, this will break the message visibility unit tests.',
        );

    ConfigManager.current.interop = ConfigManager.current.interop ?? {};
    ConfigManager.current.interop.messageVisibilitySeconds = 2;

    return true;
}

export async function clearTestNotificationQueue(): Promise<void> {
    // For unit tests we generate the elasticMq config and a notification queue is added per package
    // The queue name will be the package name with the routing queue name appended
    // e.g. routing is xtrem-sales-routing, when running the sale unit tests
    const queue = new NotificationQueue({
        name: 'routing',
        sqsName: Test.application.sqsQueueName('routing'),
        description: 'routing queue',
    });
    await queue.clearQueue();
}

export async function setup(): Promise<void> {
    await initTables([]);
}
