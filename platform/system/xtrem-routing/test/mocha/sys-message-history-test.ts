import * as xtremCommunication from '@sage/xtrem-communication';
import { Context, datetime, Test, TextStream } from '@sage/xtrem-core';
import { assert } from 'chai';
import { setup } from './utils';

function createSysMessageHistory(context: Context, data?: any): Promise<xtremCommunication.nodes.SysMessageHistory> {
    const dataCreate = {
        id: data?.id,
        integrationSolution: data?.integrationSolution || 'intacct',
        nodeName: data?.nodeName || 'myNode',
        nodeId: data?.nodeId || '1',
        context: data?.context || {
            intacctID: '1',
        },
        sentRequest:
            data?.sentRequest || TextStream.fromString("<get><node name='myIntacctNode' >1</node><get>", 'text/xml'),
        errorStack: typeof data?.errorStack === 'string' ? TextStream.fromString(data?.errorStack) : data?.errorStack,
    };
    return xtremCommunication.nodes.SysMessageHistory.createOrUpdateMessageHistory(context, dataCreate);
}

describe('SysMessageHistory node', () => {
    before(() => setup());
    it('id key is set by default if undefined ', () =>
        Test.withContext(async context => {
            const newHistory = await createSysMessageHistory(context, {
                id: undefined,
            });
            assert.isNotNull(await newHistory.id);
            assert.match(await newHistory.id, /[\w]*-[\w]*-[\w]*-[\w]*-[\w]*/gs);
        }));

    it('Can create , update SysMessageHistory node', () =>
        Test.withContext(
            async context => {
                const newHistory = await createSysMessageHistory(context, {
                    id: '1234',
                });
                assert.equal(await newHistory.id, '1234');
                assert.equal(await newHistory.sendStamp, null);
                assert.equal(await newHistory.receivedStamp, null);
                assert.equal(await newHistory.status, 'notSent');

                assert.deepEqual(await newHistory.communicationDiagnoses, {});

                const existingHistory = await context.read(
                    xtremCommunication.nodes.SysMessageHistory,
                    { _id: newHistory._id },
                    { forUpdate: true },
                );

                await existingHistory.$.set({ status: 'sent' });
                await existingHistory.$.save();

                assert.equal(await existingHistory.status, 'sent');
                assert.deepEqual(await existingHistory.sendStamp, datetime.now());

                await existingHistory.$.set({
                    receivedRequest: TextStream.fromString('<received>Data</received>', 'text/xml'),
                });
                await existingHistory.$.save();

                assert.equal(await existingHistory.status, 'received');
                assert.deepEqual(await existingHistory.receivedStamp, datetime.now());
            },
            {
                now: '2019-10-20T17:23:07Z',
            },
        ));

    it('Can create SysMessageHistory with error stack', () =>
        Test.withContext(
            async context => {
                const newHistory = await createSysMessageHistory(context, {
                    id: '4321',
                    errorStack: `at /xtrem/app/node_modules/f-promise/src/index.ts:77:25
at /home/<USER>/work/1/s/platform/system/xtrem-communication/build/lib/core-extension/queues/sqs-receive-queue.js:51:26
at Array.forEach (<anonymous>)
at /home/<USER>/work/1/s/platform/system/xtrem-communication/build/lib/core-extension/queues/sqs-receive-queue.js:53:36
at Array.forEach (<anonymous>)
at /home/<USER>/work/1/s/platform/system/xtrem-communication/build/lib/core-extension/queues/sqs-receive-queue.js:54:34`,
                });
                assert.equal(await newHistory.id, '4321');
                assert.equal(await newHistory.status, 'notSent');

                assert.deepEqual(await newHistory.communicationDiagnoses, {});
            },
            {
                now: '2019-10-20T17:23:07Z',
            },
        ));

    it('Can purge SysMessageHistory ', () =>
        Test.withContext(async context => {
            await createSysMessageHistory(context);

            assert.equal(
                await context.query(xtremCommunication.nodes.SysMessageHistory, {
                    filter: { integrationSolution: 'intacct' },
                }).length,
                1,
            );

            await xtremCommunication.nodes.SysMessageHistory.purge(context, 'intacct');

            assert.equal(
                await context.query(xtremCommunication.nodes.SysMessageHistory, {
                    filter: { integrationSolution: 'intacct' },
                }).length,
                0,
            );
        }));
});
