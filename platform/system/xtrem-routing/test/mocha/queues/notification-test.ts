import { nodes as communicationNodes, convertToTextStream } from '@sage/xtrem-communication';
import {
    Application,
    ConditionVariable,
    Context,
    Dict,
    LocalizeLocale,
    Test,
    TextStream,
    asyncArray,
    serviceOptionsMock,
    withAdvisoryLock,
} from '@sage/xtrem-core';
import { SQSMessageWrapper } from '@sage/xtrem-messaging-wrapper';
import { AuthConfig } from '@sage/xtrem-shared';
import * as xtremSystem from '@sage/xtrem-system';
import { assert } from 'chai';
import * as _ from 'lodash';
import * as sinon from 'sinon';
import { sleepMillis } from '../../../lib/services/utils';
import { TestNotification, TestNotificationPayload } from '../../fixtures/nodes/test-notification';
import * as testApplication from '../../fixtures/test-application';
import { clearTestNotificationQueue, configureTestQueues, setup } from '../utils';

const sandbox = sinon.createSandbox();

const { SysMessage, SysNotification, SysNotificationState } = communicationNodes;
const notificationEmail = '<EMAIL>';

const isDemoTenantServiceOption = xtremSystem.serviceOptions.isDemoTenant;

let conditionOnNotificationRoutingStarted: ConditionVariable;

Application.emitter.on('notificationRoutingStart', () => {
    if (conditionOnNotificationRoutingStarted) conditionOnNotificationRoutingStarted.notifyAll();
});

let conditionNotificationSent: ConditionVariable;

Application.emitter.on('notificationSent', () => {
    if (conditionNotificationSent) conditionNotificationSent.notifyAll();
});

Application.emitter.on('notificationSendFailed', () => {
    if (conditionNotificationSent) conditionNotificationSent.notifyAll();
});

let conditionNotificationProcessed: ConditionVariable;

Application.emitter.on('notificationProcessed', () => {
    if (conditionNotificationProcessed) conditionNotificationProcessed.notifyAll();
});

let conditionNotificationsRoutingStopped: ConditionVariable;

Application.emitter.on('notificationsRoutingStop', () => {
    if (conditionNotificationsRoutingStopped) conditionNotificationsRoutingStopped.notifyAll();
});

type NotificationData = { payload: { text: string }; contextValues: { replyTopic: string | undefined } };

const canReceiveNotification = async (isDemoTenant: boolean, originalAuth: AuthConfig, login?: string) => {
    const auth = { ...originalAuth };
    if (login) {
        auth.login = login;
        const apiId = xtremSystem.functions.matchApiEmail(login)?.[0];
        auth.auth0 = apiId && `api|${apiId}`;
    }
    // We need Test.withContext to create the tables. But we don't use its context becasue
    // because it is uncommitted and the notification is not sent.
    // We create a second context with Context.committted
    const locale = 'eo-AQ';
    // TO INVESTIGATE: we have a strange behavior under stress conditions where the notifications are received twice
    // this can be reproduced by increasing the max value to 200 or maybe more and running the single test:
    //    it.only(`can receive notifications in listeners${message}`, () => canReceiveNotification(isDemoTenant, auth));
    // For now, I'm reducing the max value to 20 to avoid the issue
    const max = 20;

    const buildNotifications = (name: string): NotificationData[] =>
        Array.from({ length: max }).map((v, i) => ({
            payload: {
                text: `can receive ${name}(${isDemoTenant ? 'demo' : 'non demo'}) - hello${i + 1}`,
            },
            contextValues: { replyTopic: 'TestNotification/replyTopic' },
        }));

    const notifications = {
        notification1: buildNotifications('notification1'),
        notification2: buildNotifications('notification2'),
    } as Dict<NotificationData[]>;

    function getResult(callbackName: string): Promise<NotificationData[]> {
        const nodeConstructor = testApplication.nodes.TestNotification as any;

        const resultsName = `${callbackName}-notification-results`;
        nodeConstructor[resultsName] = [] as NotificationData[];
        return new Promise((resolve, reject) => {
            nodeConstructor[callbackName] = async (context: Context, payload: TestNotificationPayload) => {
                const results: NotificationData[] = nodeConstructor[resultsName];
                assert.equal(context.tenantId, tenantId);
                assert.equal((await context.user)!.email, auth.persona || auth.login);
                assert.equal((await context.loginUser)!.email, auth.login);
                assert.equal(context.currentLocale, locale);
                if (results.find(r => r.payload.text === payload.text)) {
                    reject(new Error(`duplicate notification '${payload.text}' in ${callbackName}`));
                    return;
                }
                results.push({
                    payload,
                    contextValues: { replyTopic: context.getContextValue('replyTopic') },
                });
                if (results.length === max) {
                    nodeConstructor[callbackName] = null;
                    resolve(results);
                }
            };
        });
    }
    const promise1 = getResult('testCallback1');
    const promise2 = getResult('testCallback2');

    await Test.withContext(
        async testContext => {
            await testContext.application.withCommittedContext(
                tenantId,
                context =>
                    asyncArray(Object.keys(notifications)).forEach(key =>
                        asyncArray(notifications[key]).forEach(
                            async notification =>
                                // eslint-disable-next-line no-void
                                void (await context.notify<TestNotificationPayload>(
                                    `TestNotification/${key}`,
                                    notification.payload,
                                    {
                                        replyTopic: notification.contextValues.replyTopic,
                                    },
                                )),
                        ),
                    ),
                { auth, locale },
            );
            const compare = (a: NotificationData, b: NotificationData) => a.payload.text.localeCompare(b.payload.text);

            const results = (await Promise.all([promise1, promise2])).map(r => r.sort(compare));
            const expectedResults = Object.values(notifications).map(r => r.sort(compare));
            assert.deepEqual(results[0], expectedResults[0]);
            assert.deepEqual(results[1], expectedResults[1]);
        },
        { auth, tenantId },
    );
};

interface CallbackHandler {
    results: NotificationData[];
    callback?: (context: Context, payload: TestNotificationPayload) => void;
    resolve?: () => void;
    promise?: Promise<NotificationData[]>;
}

function setDefaultTestCallback(callbackName: string) {
    const handler: CallbackHandler = {
        results: [] as NotificationData[],
    };

    handler.promise = new Promise(resolve => {
        const nodeConstructor = testApplication.nodes.TestNotification as any;
        const callback = (context: Context, payload: TestNotificationPayload) => {
            handler.results.push({
                payload,
                contextValues: { replyTopic: context.getContextValue('replyTopic') },
            });
        };
        handler.resolve = () => {
            resolve(handler.results);
        };
        handler.callback = callback;
        nodeConstructor[callbackName] = callback;
    });
    return handler;
}

describe('notification nodes tests', () => {
    before(() => setup());
    it('convertToTextStream', async () => {
        const payload = { message: 'JSON' };
        assert.doesNotThrow(() => convertToTextStream(payload));
        assert.equal(await convertToTextStream(payload).contentType, 'application/json');

        assert.doesNotThrow(() => convertToTextStream(JSON.stringify(payload)));
        assert.equal(await convertToTextStream(JSON.stringify(payload)).contentType, 'application/json');
    });

    it('create SysNotification', async () => {
        await Test.withContext(async context => {
            const payload = { message: 'this a JSON' };
            const userEmail = (await context.user)!.email;
            const notification = await context.create(SysNotification, {
                tenantId: Test.defaultTenantId,
                userEmail,
                login: (await context.loginUser)?.email || userEmail,
                locale: context.currentLocale,
                topic: 'topic',
                payload: convertToTextStream(payload),
                originId: '0',
                replyId: '',
            });
            await notification.$.save();
        });

        await Test.withContext(async context => {
            const payload = { message: 'this a JSON' };
            const userEmail = (await context.user)!.email;

            const notification = await context.create(SysNotification, {
                tenantId: Test.defaultTenantId,
                userEmail,
                login: (await context.loginUser)?.email || userEmail,
                locale: context.currentLocale,
                topic: 'topic',
                payload: new TextStream(JSON.stringify(payload)),
                originId: '0',
                replyId: '',
            });
            await notification.$.save();
        });
    });

    it('create SysMessage', async () => {
        await Test.withContext(async context => {
            const payload = { message: 'this a JSON' };
            const message = await context.create(SysMessage, {
                tenantId: Test.defaultTenantId,
                queue: 'queue',
                payload: convertToTextStream(payload),
                attributes: {},
            });
            await message.$.save();
        });

        await Test.withContext(async context => {
            const payload = { message: 'this a JSON' };
            const message = await context.create(SysMessage, {
                tenantId: Test.defaultTenantId,
                queue: 'queue',
                payload: new TextStream(JSON.stringify(payload)),
                attributes: {},
            });
            await message.$.save();
        });
    });
});

const tenantId = Test.defaultTenantId;

[false, true].forEach(isDemoTenant => {
    const message = isDemoTenant ? ' (with demo tenant)' : '';
    const auth: AuthConfig = isDemoTenant
        ? { login: notificationEmail, persona: '<EMAIL>' }
        : { login: notificationEmail };
    describe(`notification tests${message}`, () => {
        const isDemoTenantOld = serviceOptionsMock.isDemoTenant;

        before(async function before(this: Mocha.Context) {
            if (!configureTestQueues()) this.skip();
            await setup();
            if (isDemoTenant) {
                // we need to override the isDemoTenant service option used by the accessRightManager mock in order to not use the one provided by xtrem-system
                serviceOptionsMock.isDemoTenant = isDemoTenantServiceOption;
                await Test.application.withCommittedContext(tenantId, context =>
                    context.serviceOptionManager.activateServiceOptions(context, [isDemoTenantServiceOption]),
                );
            }
        });

        after(async () => {
            if (configureTestQueues() && isDemoTenant) {
                await Test.application.withCommittedContext(tenantId, context =>
                    context.serviceOptionManager.deactivateServiceOptions(context, [isDemoTenantServiceOption]),
                );
                serviceOptionsMock.isDemoTenant = isDemoTenantOld;
            }
        });

        const callbackHandlers: any = [];

        beforeEach(async () => {
            await clearTestNotificationQueue();
            callbackHandlers.push(setDefaultTestCallback('testCallback1'));
            callbackHandlers.push(setDefaultTestCallback('testCallback2'));
        });

        afterEach(async () => {
            // eslint-disable-next-line no-restricted-syntax
            for (const p of callbackHandlers) {
                p.resolve();
                await p.promise;
            }
            callbackHandlers.length = 0;
        });

        it(`cannot create notification in readonly context${message}`, () =>
            Test.withReadonlyContext(async context => {
                await assert.isRejected(
                    context.notify('TestNotification/notification1', { text: 'hello' }),
                    'notify is allowed only in the context of a mutation',
                );
            }));

        it(`can check the demo tenant service option${message}`, () =>
            Test.withReadonlyContext(async context => {
                assert.equal(await context.supportsPersona(), isDemoTenant);
            }));

        it(`Can always update visibility timeout of notifications in listeners${message}`, async () => {
            // We need Test.withContext to create the tables. But we don't use its context
            // because it is uncommitted and the notification is not sent.
            // We create a second context with Context.committted
            const locale = 'eo-AQ';
            const max = 10;
            const array1: any[] = Array.from({ length: 5000 });
            const array2: any[] = Array.from({ length: 1000 });
            const buildNotifications = (name: string): NotificationData[] =>
                Array.from({ length: max }).map((v, i) => ({
                    payload: { text: `${name} - hello${i + 1}` },
                    contextValues: { replyTopic: 'TestNotification/replyTopic' },
                }));

            const notifications = {
                notification1: buildNotifications('notification1'),
            } as Dict<NotificationData[]>;

            function getResult(callbackName: string): Promise<NotificationData[]> {
                return new Promise(resolve => {
                    const nodeConstructor = testApplication.nodes.TestNotification as any;

                    const results = `${callbackName}-notification-results`;
                    nodeConstructor[results] = [] as any[];

                    nodeConstructor[callbackName] = async (context: Context, payload: TestNotificationPayload) => {
                        await asyncArray(array1).forEach(a1 => {
                            return asyncArray(array2).forEach(a2 => {
                                _.noop(a1, a2);
                            });
                        });
                        assert.equal(context.tenantId, tenantId);
                        assert.equal((await context.user)?.email, auth.persona || auth.login);
                        assert.equal((await context.loginUser)?.email, auth.login);
                        assert.equal(context.currentLocale, locale);
                        nodeConstructor[results].push({
                            payload,
                            contextValues: { replyTopic: context.getContextValue('replyTopic') },
                        });
                        if (nodeConstructor[results].length === max) {
                            nodeConstructor[callbackName] = null;
                            resolve(nodeConstructor[results]);
                        }
                    };
                });
            }

            await Test.withContext(
                async testContext => {
                    const promise1 = getResult('testCallback1');
                    await testContext.application.withCommittedContext(
                        tenantId,
                        context =>
                            asyncArray(Object.keys(notifications)).forEach(key =>
                                asyncArray(notifications[key]).forEach(
                                    async notification =>
                                        // eslint-disable-next-line no-void
                                        void (await context.notify<TestNotificationPayload>(
                                            `TestNotification/${key}`,
                                            notification.payload,
                                            {
                                                replyTopic: notification.contextValues.replyTopic,
                                            },
                                        )),
                                ),
                            ),
                        { auth, locale },
                    );
                    const compare = (a: NotificationData, b: NotificationData) =>
                        a.payload.text.localeCompare(b.payload.text);

                    assert.deepEqual((await promise1).sort(compare), notifications.notification1.sort(compare));
                },
                { auth, tenantId },
            );
        });

        it(`Can cancel notification processing in listeners${message}`, async () => {
            const visibilityTimeoutCondition = Test.createConditionVariable('visibilityTimeoutCondition');
            // create a stub that throws an error when trying to change the visibility timeout to simulate a receive handle error
            sandbox.stub(SQSMessageWrapper.prototype, 'changeMessageVisibilityTimeout').callsFake(() => {
                visibilityTimeoutCondition.notifyAll();
                throw new Error('visibility timeout error');
            });

            // create a notification callback that creates a node and then waits for a while to be cancelled
            TestNotification.testCallback1 = async (context: Context, payload: TestNotificationPayload) => {
                const node = await context.create(TestNotification, {
                    id: 1,
                    stringVal: payload.text,
                });
                await node.$.save();
                for (let i = 0; i < 5; i += 1) {
                    await sleepMillis(200);
                }
                await visibilityTimeoutCondition.wait();
                assert.isTrue(context.isAborted);
            };

            conditionNotificationProcessed = Test.createConditionVariable('conditionNotificationProcessed');
            Application.emitter.once('notificationProcessed', () => {
                conditionNotificationProcessed.notifyAll();
            });

            await Test.withContext(
                async testContext => {
                    await testContext.application.withCommittedContext(
                        tenantId,
                        async context => {
                            // clean up the table to be sure we do not have any node with id 1
                            await context.deleteMany(TestNotification, {});

                            await context.notify<TestNotificationPayload>(
                                `TestNotification/notification1`,
                                { text: 'hello to cancel' },
                                { replyTopic: 'TestNotification/replyTopic' },
                            );
                        },
                        { auth },
                    );
                },
                { auth, tenantId },
            );

            // wait for the notification to be processed
            await conditionNotificationProcessed.wait();

            // check that the node was not created (because the notification processing was cancelled by the abort controller)
            const node = await Test.withReadonlyContext(context => context.tryRead(TestNotification, { id: 1 }), {
                auth,
                tenantId,
            });
            assert.isNull(node);
            sandbox.restore();
        });

        it(`can receive notifications in listeners${message}`, () => canReceiveNotification(isDemoTenant, auth));

        it(`can receive notifications in listeners${message} as api user`, () =>
            canReceiveNotification(isDemoTenant, auth, '<EMAIL>'));

        it(`can receive notifications in listeners in node extensions${message}`, async () => {
            // We need Test.withContext to create the tables. But we don't use its context becasue
            // because it is uncommitted and the notification is not sent.
            // We create a second context with Context.committted
            const locale = 'eo-AQ';
            const max = 1;

            const buildNotifications = (name: string): NotificationData[] =>
                Array.from({ length: max }).map((v, i) => ({
                    payload: { text: `${name} - hello${i + 1}` },
                    contextValues: { replyTopic: 'TestNotification/replyTopic' },
                }));

            const notifications = {
                'notification-extension1': buildNotifications('notification-extension1'),
            } as Dict<NotificationData[]>;

            function getResult(callbackName: string): Promise<NotificationData[]> {
                return new Promise(resolve => {
                    const nodeConstructor = testApplication.nodes.TestNotification as any;

                    const results = `${callbackName}-notification-results`;
                    nodeConstructor[results] = [] as any[];

                    nodeConstructor[callbackName] = async (context: Context, payload: TestNotificationPayload) => {
                        assert.equal(context.tenantId, tenantId);
                        assert.equal((await context.user)!.email, auth.persona || auth.login);
                        assert.equal((await context.loginUser)!.email, auth.login);
                        assert.equal(context.currentLocale, locale);
                        nodeConstructor[results].push({
                            payload,
                            contextValues: { replyTopic: context.getContextValue('replyTopic') },
                        });
                        if (nodeConstructor[results].length === max) {
                            nodeConstructor[callbackName] = null;
                            resolve(nodeConstructor[results]);
                        }
                    };
                });
            }

            await Test.withContext(
                async testContext => {
                    const promise1 = getResult('testExtensionCallback1');
                    await testContext.application.withCommittedContext(
                        tenantId,
                        context =>
                            asyncArray(Object.keys(notifications)).forEach(key =>
                                asyncArray(notifications[key]).forEach(
                                    async notification =>
                                        // eslint-disable-next-line no-void
                                        void (await context.notify<TestNotificationPayload>(
                                            `TestNotification/${key}`,
                                            notification.payload,
                                            {
                                                replyTopic: notification.contextValues.replyTopic,
                                            },
                                        )),
                                ),
                            ),
                        { auth, locale },
                    );
                    const compare = (a: NotificationData, b: NotificationData) =>
                        a.payload.text.localeCompare(b.payload.text);

                    assert.deepEqual(
                        (await promise1).sort(compare),
                        notifications['notification-extension1'].sort(compare),
                    );
                },
                { auth, tenantId },
            );
        });

        it(`can identify the origin of a notification${message}`, async () => {
            const locale = 'eo-AQ';

            await Test.withContext(testContext =>
                testContext.application.withCommittedContext(
                    tenantId,
                    async context => {
                        const notificationId1 = await context.notify<TestNotificationPayload>(
                            'TestNotification/notification1',
                            {
                                text: 'hello1',
                            },
                            { replyTopic: 'TestNotificationPayload/replyTopic' },
                        );

                        const notificationRecord1 = (
                            await context
                                .query(SysNotification, {
                                    filter: { notificationId: notificationId1 },
                                })
                                .toArray()
                        )[0];

                        // notification record exists
                        assert.isNotNull(notificationRecord1);
                        assert.equal(await notificationRecord1!.tenantId, tenantId);
                        assert.equal(await notificationRecord1!.userEmail, (await context.user)!.email);
                        assert.equal((await context.user)!.email, auth.persona || auth.login);
                        assert.equal((await context.loginUser)!.email, auth.login);
                        assert.equal(await notificationRecord1!.userEmail, auth.persona || auth.login);
                        assert.equal(await notificationRecord1!.login, auth.login);
                        assert.equal(await notificationRecord1!.locale, locale);
                        assert.equal(
                            await notificationRecord1!.topic,
                            `${testApplication.nodes.TestNotification.name}/notification1`,
                        );
                        assert.equal(await notificationRecord1!.replyTopic, 'TestNotificationPayload/replyTopic');

                        const notificationId2 = await context.notify<TestNotificationPayload>(
                            'TestNotification/notification2',
                            {
                                text: 'hello2',
                            },
                            { replyTopic: 'TestNotification/replyTopic' },
                        );

                        const notificationRecord2 = (
                            await context
                                .query(SysNotification, {
                                    filter: { notificationId: notificationId2 },
                                })
                                .toArray()
                        )[0];

                        // second notification record exists
                        assert.isNotNull(notificationRecord2);
                        assert.equal(await notificationRecord2!.tenantId, tenantId);
                        assert.equal((await context.user)!.email, auth.persona || auth.login);
                        assert.equal((await context.loginUser)!.email, auth.login);
                        assert.equal(await notificationRecord2!.userEmail, auth.persona || auth.login);
                        assert.equal(await notificationRecord2!.login, auth.login);
                        assert.equal(await notificationRecord2!.locale, locale);
                        assert.equal(
                            await notificationRecord2!.topic,
                            `${testApplication.nodes.TestNotification.name}/notification2`,
                        );
                        assert.equal(await notificationRecord2!.replyTopic, 'TestNotification/replyTopic');

                        assert.equal(
                            await notificationRecord1!.originId,
                            `${context.requestSource}:${context.customerRequestId}`,
                        );
                        // originId should be the same for both notifications
                        assert.equal(await notificationRecord1!.originId, await notificationRecord2!.originId);
                    },
                    { auth, locale },
                ),
            );
        });

        it(`can receive many notifications${message}`, async () => {
            const ids: number[] = [];

            const maxNotifications = 20;
            for (let index = 0; index < maxNotifications; index += 1) {
                ids.push(index);
            }

            // TODO: why do we use an invalid (not in LocalizeLocale) locale code here?
            const locale = 'eo-AQ' as string as LocalizeLocale;
            let nextId = 0;

            testApplication.nodes.TestNotification.testCallback1 = async (
                context: Context,
                payload: TestNotificationPayload,
            ) => {
                nextId += 1;

                const node = await context.create(TestNotification, {
                    id: nextId,
                    stringVal: payload.text,
                });

                await node.$.save();
            };

            conditionNotificationProcessed = Test.createConditionVariable('conditionNotificationProcessed');

            await Test.withContext(
                async testContext => {
                    await testContext.application.withCommittedContext(
                        tenantId,
                        async context => {
                            await context.deleteMany(TestNotification, {});
                            await asyncArray(ids).forEach(
                                async id =>
                                    // eslint-disable-next-line no-void
                                    void (await context.notify<TestNotificationPayload>(
                                        'TestNotification/notification1',
                                        {
                                            text: `payload ${id}`,
                                        },
                                    )),
                            );
                        },
                        { auth, locale },
                    );
                },
                { auth, tenantId, locale },
            );

            for (
                let index = 0;
                index < maxNotifications;
                index += 1,
                    conditionNotificationProcessed = Test.createConditionVariable('conditionNotificationProcessed')
            ) {
                await conditionNotificationProcessed.wait();
            }

            await Test.withContext(
                async testContext => {
                    // The notifications are not always processed in the order they were sent, there we have to sort the result for the comparison
                    const results = await testContext
                        .query(TestNotification)
                        .sort(async (a, b) => {
                            if ((await a.stringVal) > (await b.stringVal)) return 1;
                            if ((await a.stringVal) < (await b.stringVal)) return -1;
                            return 0;
                        })
                        .map(r => r.stringVal)
                        .toArray();

                    const stringValues = ids
                        .map(id => `payload ${id}`)
                        .sort((a, b) => {
                            if (a > b) return 1;
                            if (a < b) return -1;
                            return 0;
                        });

                    // assert.equal(results.length, stringValues.length);
                    assert.deepEqual(results, stringValues);
                },
                { auth, tenantId, locale },
            );
        });

        it(`can reply to a notifications with a replyId passed and check the origin id for correlation${message}`, async () => {
            const locale = 'eo-AQ';

            await Test.withContext(testContext =>
                testContext.application.withCommittedContext(
                    tenantId,
                    async context => {
                        const notificationId1 = await context.notify<TestNotificationPayload>(
                            'TestNotification/notification1',
                            {
                                text: 'hello1',
                            },
                        );

                        const notificationRecord1 = (
                            await context
                                .query(SysNotification, {
                                    filter: { notificationId: notificationId1 },
                                })
                                .toArray()
                        )[0];

                        const notificationId2 = await context.reply<TestNotificationPayload>(
                            'TestNotification/notification2',
                            {
                                text: 'hello2',
                            },
                            { replyId: await notificationRecord1.notificationId },
                        );

                        const notificationRecord2 = (
                            await context
                                .query(SysNotification, {
                                    filter: { notificationId: notificationId2 },
                                })
                                .toArray()
                        )[0];

                        assert.equal(await notificationRecord1.notificationId, await notificationRecord2.replyId);
                        // we consider only the case of a reply within the listener
                        assert.equal(await notificationRecord1.originId, await notificationRecord2.originId);

                        const notificationId3 = await context.notify<TestNotificationPayload>(
                            'TestNotification/notification1',
                            {
                                text: 'hello3',
                            },
                        );
                        const notificationRecord3 = (
                            await context
                                .query(SysNotification, {
                                    filter: { notificationId: notificationId3 },
                                })
                                .toArray()
                        )[0];

                        const notificationId4 = await context.reply<TestNotificationPayload>(
                            'TestNotification/notification2',
                            {
                                text: 'hello4',
                            },
                            { replyId: await notificationRecord3.notificationId },
                        );

                        const notificationRecord4 = (
                            await context
                                .query(SysNotification, {
                                    filter: { notificationId: notificationId4 },
                                })
                                .toArray()
                        )[0];

                        assert.equal(await notificationRecord3.notificationId, await notificationRecord4.replyId);
                        // we consider only the case of a reply within the listener
                        assert.equal(await notificationRecord2.originId, await notificationRecord3.originId);
                        assert.equal(await notificationRecord4.originId, await notificationRecord3.originId);
                    },
                    { auth, locale },
                ),
            );
        });

        it(`can reply to a notifications without a replyId passed, but set from current context notificationId contextValue${message}`, async () => {
            const locale = 'eo-AQ';

            await Test.withContext(
                async testContext => {
                    let notificationId = '';
                    const promise = new Promise<any>(resolve => {
                        testApplication.nodes.TestNotification.testCallback1 = (
                            replyContext: Context,
                            payload: TestNotificationPayload,
                        ) => {
                            testApplication.nodes.TestNotification.testCallback1 = null;
                            resolve({ text: `${payload.text}-${replyContext.getContextValue('replyId')}` });
                        };
                    });
                    await testContext.application.withCommittedContext(
                        tenantId,
                        async context => {
                            notificationId = await context.notify<TestNotificationPayload>(
                                'TestNotification/notificationReply',
                                {
                                    text: 'hello1',
                                },
                            );
                        },
                        { auth, locale, userEmail: '<EMAIL>' },
                    );

                    const reply = await promise;
                    // the reply is resolved to the text of the original payload - the replyId
                    assert.deepEqual(reply, { text: `hello1-${notificationId}` });
                },
                { auth, tenantId },
            );
        });

        it(`will log an error in SysNotificationState if topic is invalid ${message}`, async () => {
            // TODO: why do we use an invalid (not in LocalizeLocale) locale code here?
            const locale = 'en-US' as string as LocalizeLocale;
            let notificationId = '';

            conditionNotificationSent = Test.createConditionVariable('conditionNotificationSent');

            await Test.withCommittedContext(
                async context => {
                    notificationId = await context.notify<TestNotificationPayload>('TestNotification/DoesNotExist', {
                        text: 'hello1',
                    });
                },
                { auth, tenantId, locale },
            );
            await conditionNotificationSent.wait();

            await Test.withReadonlyContext(
                async context => {
                    const sysNotificationState = await context
                        .query(SysNotificationState, { filter: { notificationId } })
                        .toArray();
                    const user = await context.user;
                    assert.equal(
                        sysNotificationState.length,
                        1,
                        `NotificationId : ${notificationId} not found - User:${user?.email} (${user?._id})`,
                    );
                    assert.equal(
                        await sysNotificationState[0].message,
                        'Cannot route notification: TestNotification/DoesNotExist: queue not found',
                    );
                },
                { auth: { login: '<EMAIL>' }, tenantId, locale },
            );

            // Cannot query notification state across tenants
            // TODO: Cannot test this users only loaded for default tenant, therefore this will fail with user
            // not found, review later
            // await (
            //     Test.withContext(
            //         async (context => {
            //             const notificationHistory = await (
            //                 context.query(SysNotificationHistory, { filter: { notificationId } }).toArray(),
            //             );
            //             assert.equal(notificationHistory.length, 0);
            //         }),
            //         { auth, tenantId: '1'.repeat(21), locale },
            //     ),
            // );
        });

        it(`cannot reply without either passing a replyId or having the notificationId set in contextValues${message}`, async () => {
            // TODO: why do we use an invalid (not in LocalizeLocale) locale code here?
            const locale = 'eo-AQ' as string as LocalizeLocale;

            await Test.withContext(
                context =>
                    assert.isRejected(
                        context.reply<TestNotificationPayload>('TestNotification/notificationReply', {
                            text: 'hello1',
                        }),
                        'A reply notification requires a replyId',
                    ),
                { auth, tenantId, locale },
            );
        });
        it(`waits to acquire an advisory lock, before processing notification${message}`, async () => {
            const locale = 'eo-AQ';
            await Test.withContext(
                async testContext => {
                    conditionOnNotificationRoutingStarted = Test.createConditionVariable(
                        'conditionOnNotificationRoutingStarted',
                    );
                    conditionNotificationsRoutingStopped = Test.createConditionVariable(
                        'conditionNotificationsRoutingStopped',
                    );

                    const advisoryLockId = *****************;
                    let notificationId = '';
                    // after withAdvisoryLock finishes the notification-router can acquired its pending lock and process
                    await withAdvisoryLock(testContext, advisoryLockId, async () => {
                        await testContext.application.withCommittedContext(
                            tenantId,
                            async context => {
                                notificationId = await context.notify<TestNotificationPayload>(
                                    'TestNotification/notification1',
                                    {
                                        text: 'hello1',
                                    },
                                );
                            },
                            { auth, locale },
                        );

                        await conditionOnNotificationRoutingStarted.wait();

                        // notification should still be here, since the notification-router has an advisory lock on it
                        await testContext.application.withCommittedContext(
                            tenantId,
                            async context => {
                                assert.equal(
                                    await context.query(SysNotification, {
                                        filter: { notificationId },
                                    }).length,
                                    1,
                                );
                            },
                            { auth, locale },
                        );
                    });

                    await conditionNotificationsRoutingStopped.wait();

                    // notification should now be processed
                    await testContext.application.withCommittedContext(
                        tenantId,
                        async context => {
                            assert.equal(
                                await context.query(SysNotification, {
                                    filter: { notificationId },
                                }).length,
                                0,
                            );
                        },
                        { auth, locale },
                    );
                },
                { auth, tenantId },
            );
        });
    });
});
