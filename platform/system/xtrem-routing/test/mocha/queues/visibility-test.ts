import { getSQSMessageWrapper, sleepMillis } from '@sage/xtrem-communication';
import { ConfigManager, Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import { nanoid } from 'nanoid';
import { testVisibilityMultipleListenersReceiveQueue1 } from '../../fixtures/queues/test-visibility-multiple-listeners-receive-queue1';
import { testVisibilityMultipleListenersReceiveQueue2 } from '../../fixtures/queues/test-visibility-multiple-listeners-receive-queue2';
import * as testApplication from '../../fixtures/test-application';
import { configureTestQueues, setup } from '../utils';

const tenantId = Test.defaultTenantId;
const locale = 'fr-CA';
const email = '<EMAIL>';
const config = ConfigManager.current;
const messageVisibilitySeconds = config.interop?.messageVisibilitySeconds || 30;
const receiveUrl = config.interop?.queues?.testVisibilityMultipleListenersReceiveQueue1?.url || '';
describe('Visibility tests', () => {
    before(async function before() {
        if (!configureTestQueues() || !receiveUrl) this.skip();

        await setup();
    });

    beforeEach(async () => {
        // We need to clear the queue
        await testVisibilityMultipleListenersReceiveQueue1.clearQueue();
        await testVisibilityMultipleListenersReceiveQueue2.clearQueue();

        testApplication.nodes.TestMessage.testCallback = null;
    });

    /**
     * 2 listeners: Expiry
     * First listener messageVisibilityTime expires
     * Second listener can process message
     * Counter should equal 2
     */
    it('should be processed by once without visilibilty timeout expiring', async () => {
        // We need Test.withContext to create the tables. But we don't use its context
        // because it is uncommitted and the notification is not sent.
        // We create a second context with Test.committed

        await Test.withContext(testContext =>
            testContext.application.withCommittedContext(
                tenantId,
                context =>
                    context.send(testApplication.queues.testVisibilityMultipleListenersSendQueue, {
                        payload: {
                            text: 'hello',
                        },
                        attributes: {
                            tenantId,
                            email,
                            locale,
                        },
                    }),
                { userEmail: '<EMAIL>' },
            ),
        );

        let counter = 0;
        const result = await new Promise(resolve => {
            testApplication.nodes.TestMessage.testCallback = async () => {
                counter += 1;
                if (counter === 1) {
                    await sleepMillis((messageVisibilitySeconds / 2) * 3000);
                }
                resolve(counter);
            };
        });

        assert.deepEqual(result, 1);

        const messages = await getSQSMessageWrapper(receiveUrl, { region: 'eu-west-1' }).receiveMessage({
            // must be < 10 if you pick a lot of message make sure you have enough time to process them during visibility window
            MaxNumberOfMessages: 5,
            // you have 30 seconds to process the message otherwise it will become visible again
            VisibilityTimeout: messageVisibilitySeconds,
            ReceiveRequestAttemptId: nanoid(),
            WaitTimeSeconds: 1,
        });

        assert.equal(messages.length, 0);
    });
});
