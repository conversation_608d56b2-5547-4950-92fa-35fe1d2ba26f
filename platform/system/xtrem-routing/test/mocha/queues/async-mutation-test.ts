import { AsyncMutationState } from '@sage/xtrem-communication';
import { Application, AsyncResponse, BatchLogEntry, ConditionVariable, integer, Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import { sleepMillis } from '../../../lib/services/utils';
import { TestAsyncMutation } from '../../fixtures/nodes/test-async-mutation';
import { clearTestNotificationQueue, configureTestQueues, setup } from '../utils';

let conditionNotificationProcessed: ConditionVariable;

Application.emitter.on('notificationProcessed', () => {
    if (conditionNotificationProcessed) conditionNotificationProcessed.notifyAll();
});

describe('Async mutation', () => {
    before(async function before() {
        if (!configureTestQueues()) this.skip();
        await setup();
    });

    beforeEach(async () => {
        // We need to clear the queue
        await clearTestNotificationQueue();
    });

    const testAsyncMutationWithListener = async (value: number, expectedPayload: AsyncMutationState<integer>) => {
        const done = new ConditionVariable();
        let resultPayload: AsyncMutationState<integer> | undefined;
        let resultCount = 0;

        TestAsyncMutation.onResult = (context, payload): AsyncResponse<void> => {
            delete payload.errorStack; // ignore it in deepEqual
            resultPayload = payload;
            resultCount += 1;
            done.notifyAll();
        };

        await Test.withCommittedContext(
            async context => {
                await context.notify(
                    'TestAsyncMutation/squareIt/start',
                    { value },
                    {
                        replyTopic: 'TestAsyncMutation/squareIt/result',
                    },
                );
            },
            { userEmail: '<EMAIL>' },
        );

        await done.wait();
        assert.deepEqual(resultPayload, expectedPayload);
        assert.equal(resultCount, 1);
    };

    it('can execute an async mutation with a notification (done)', () =>
        testAsyncMutationWithListener(7, { status: 'success', result: 49 }));
    it('can execute an async mutation with a notification (error)', () =>
        testAsyncMutationWithListener(13, {
            status: 'error',
            errorMessage: 'Square it failed.',
        }));

    const testAsyncMutationWithGraphQl = async (
        value: number,
        expectedPayload: {
            status: string;
            result: number | null;
            errorMessage: string | null;
            logMessages: BatchLogEntry[];
        },
    ) => {
        conditionNotificationProcessed = Test.createConditionVariable('conditionNotificationProcessed');

        const submitted = await Test.withReadonlyContext(
            context =>
                Test.graphql<{
                    xtremRouting: { testAsyncMutation: { squareIt: { start: { trackingId: string } } } };
                }>(
                    context,
                    `mutation { xtremRouting { testAsyncMutation { squareIt { start(value: ${value}) { trackingId } } } } }`,
                ),
            { userEmail: '<EMAIL>' },
        );
        assert.isObject(submitted);
        const trackingId = submitted.data.xtremRouting.testAsyncMutation.squareIt.start.trackingId;
        assert.isString(trackingId);

        await conditionNotificationProcessed.wait();

        for (let i = 0; i < 20; i += 1) {
            const polled = await Test.withReadonlyContext(
                context =>
                    Test.graphql<{
                        xtremRouting: {
                            testAsyncMutation: {
                                squareIt: {
                                    track: {
                                        status: string;
                                        result: number | null;
                                        errorMessage: string | null;
                                        logMessages: BatchLogEntry[];
                                    };
                                };
                            };
                        };
                    }>(
                        context,
                        `query { xtremRouting { testAsyncMutation { squareIt {
                            track(trackingId: "${trackingId}") {
                                status, result, errorMessage, logMessages { level, message }
                            }
                        } } } }`,
                    ),
                { userEmail: '<EMAIL>' },
            );
            assert.isObject(polled);

            const tracker = polled.data.xtremRouting.testAsyncMutation.squareIt.track;
            if (tracker.status === 'pending' || tracker.status === 'running') {
                await sleepMillis(10);
            } else {
                assert.deepEqual(tracker, expectedPayload);
                return;
            }
        }
        assert.fail('polling failed');
    };

    it('can execute an async mutation with graphql (success)', async () => {
        await testAsyncMutationWithGraphQl(9, { status: 'success', result: 81, errorMessage: null, logMessages: [] });
    });
    it('can execute an async mutation with graphql (error)', async () => {
        await testAsyncMutationWithGraphQl(13, {
            status: 'error',
            result: null,
            errorMessage: 'Square it failed.',
            logMessages: [{ level: 'exception', message: 'Sorry, I am superstitious' }],
        });
    });
});
