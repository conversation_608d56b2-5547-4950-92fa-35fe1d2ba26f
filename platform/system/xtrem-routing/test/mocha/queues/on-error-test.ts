import { nodes as communicationNodes } from '@sage/xtrem-communication';
import { Application, ConditionVariable, LocalizeLocale, Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import { TestMessage } from '../../fixtures/nodes/test-message';
import { testReceiveQueue } from '../../fixtures/queues/_index';
import * as testApplication from '../../fixtures/test-application';
import { clearTestNotificationQueue, configureTestQueues, setup } from '../utils';

const { SysNotificationState } = communicationNodes;

const locale = 'fr-CA' as LocalizeLocale;
const email = '<EMAIL>';

let conditionOnMessageError: ConditionVariable;

Application.emitter.on('messageOnError', () => {
    if (conditionOnMessageError) conditionOnMessageError.notifyAll();
});

let conditionNotificationOnError: ConditionVariable;

Application.emitter.on('notificationOnError', () => {
    if (conditionNotificationOnError) conditionNotificationOnError.notifyAll();
});

describe('onError tests', () => {
    before(async function before() {
        if (!configureTestQueues()) this.skip();
        await setup();
    });

    beforeEach(async () => {
        // We need to clear the queue
        await clearTestNotificationQueue();
        await testReceiveQueue.clearQueue();
    });

    it('can notify a listener that does not have an onError method', async () => {
        conditionNotificationOnError = Test.createConditionVariable('conditionNotificationOnError');

        await Test.withCommittedContext(context => context.notify('TestNotification/withoutOnError', {}), {
            userEmail: email,
            locale,
        });

        await conditionNotificationOnError.wait();

        await Test.withContext(
            // We need Test.withContext to create the tables. But we don't use its context
            // because it is uncommitted and the notification is not sent.
            // We create a second context with Test.committed
            async testContext => {
                const result = await testContext
                    .query(SysNotificationState, {
                        filter: { topic: 'TestNotification/withoutOnError' },
                    })
                    .toArray();
                assert.equal(result.length, 1);
                assert.equal(await result[0].topic, 'TestNotification/withoutOnError');
                assert.equal(await result[0].status, 'error');
                assert.equal(await result[0].message, 'TestNotification/withoutOnError');
            },
            { locale },
        );
    });

    it('can notify a listener that has an onError method', async () => {
        conditionNotificationOnError = Test.createConditionVariable('conditionNotificationOnError');

        await Test.withCommittedContext(context => context.notify('TestNotification/withOnError', {}), {
            userEmail: email,
            locale,
        });

        await conditionNotificationOnError.wait();

        await Test.withContext(
            // We need Test.withContext to create the tables. But we don't use its context
            // because it is uncommitted and the notification is not sent.
            // We create a second context with Test.committed
            async testContext => {
                const result = await testContext
                    .query(SysNotificationState, {
                        filter: { topic: 'TestNotification/withOnError' },
                    })
                    .toArray();
                assert.equal(result.length, 1);
                assert.equal(await result[0].topic, 'TestNotification/withOnError');
                // notification history was populated in onError
                assert.equal(await result[0].status, 'pending');
                assert.equal(await result[0].message, 'TestNotification/withOnError');
            },
            { locale },
        );
    });

    it('can send a message to a listener that has an onError method', () =>
        Test.withContext(
            // We need Test.withContext to create the tables. But we don't use its context
            // because it is uncommitted and the notification is not sent.
            // We create a second context with Test.committed
            async () => {
                conditionOnMessageError = Test.createConditionVariable('conditionOnMessageError');

                await Test.withCommittedContext(
                    context =>
                        context.send(testApplication.queues.testSendQueue, {
                            payload: {
                                text: 'onMessageWithOnError',
                                throwError: true,
                            },
                            attributes: {
                                tenantId: Test.defaultTenantId,
                                email,
                                locale,
                            },
                        }),
                    { userEmail: email, locale },
                );

                await conditionOnMessageError.wait();

                await Test.withCommittedContext(
                    async context => {
                        const result = await context
                            .query(TestMessage, {
                                filter: { stringVal: 'onMessageWithOnError' },
                                forUpdate: true,
                            })
                            .toArray();
                        assert.equal(result.length, 1);
                        await result[0].$.delete();
                    },
                    { locale },
                );
            },
            { locale },
        ));
});
