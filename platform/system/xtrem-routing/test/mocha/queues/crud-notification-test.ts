import { Context, Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as testApplication from '../../fixtures/test-application';
import { clearTestNotificationQueue, configureTestQueues, setup } from '../utils';

interface NotificationResultOptions {
    asserts?: (context: Context) => void;
    timeout?: number;
    max?: number;
}

function getNotificationResult(nodeConstructor: any, options: NotificationResultOptions, callbackName: string) {
    return new Promise((resolve, reject) => {
        const timeout = (options.timeout || 10) * 1000;
        const max = options.max ?? 1;

        const payloads = `${callbackName}-payloads`;
        nodeConstructor[payloads] = [] as any[];

        const timeoutId = setTimeout(() => {
            nodeConstructor[payloads] = null;
            nodeConstructor[callbackName] = null;
            reject(new Error(`notification listening timeout: ${timeout} ms`));
        }, timeout);

        nodeConstructor[callbackName] = (context: Context, payload: any) => {
            clearTimeout(timeoutId);
            options.asserts?.(context);
            // We don't test _updateTick
            delete payload._updateTick;
            nodeConstructor[payloads].push(payload);
            if (nodeConstructor[payloads].length === max) {
                nodeConstructor[callbackName] = null;
                resolve(nodeConstructor[payloads]);
                nodeConstructor[payloads] = null;
            }
        };
    });
}

/**
 * To run these tests locally :
 * 1. Add the following paragraph to the xtrem-config.yml file:
    interop:
        messageVisibilitySeconds: 5
        sendRetrySeconds: 3
        queues:
            testSendQueue:
            url: 'http://127.0.0.1:9324/queue/unit-test-message-queue.fifo'
            testReceiveQueue:
            url: 'http://127.0.0.1:9324/queue/unit-test-message-queue.fifo'
            routing:
            url: 'http://127.0.0.1:9324/queue/communication-notficiations.fifo'
            testNonExistQueue:
            url: 'http://127.0.0.1:9324/queue/non-existent-unit-test-queue.fifo'
            testVisibilityMultipleListenersSendQueue:
            url: 'http://127.0.0.1:9324/queue/unit-test-visibility-multiple-listeners-queue.fifo'
            # 2 receive queues are required to test visibility
            testVisibilityMultipleListenersReceiveQueue1:
            url: 'http://127.0.0.1:9324/queue/unit-test-visibility-multiple-listeners-queue.fifo'
            testVisibilityMultipleListenersReceiveQueue2:
            url: 'http://127.0.0.1:9324/queue/unit-test-visibility-multiple-listeners-queue.fifo'
 *
 * 2. Start a elasticmq container along with the postgres docker container.
 *    To do that, use the following command:
 *    docker run -p 9324:9324 -p 9325:9325 -d --volume /xtrem/xtrem/docker/elasticmq/elasticmq.conf:/opt/elasticmq.conf --name elasticmq-test softwaremill/elasticmq
 *
 * It's done, the tests shouldn't be skipped.
 */

// Skipped because the triggers are in xtrem-auditing, which is not a dependency of xtrem-routing
describe.skip('CRUD notification tests', () => {
    before(async function before() {
        if (!configureTestQueues()) this.skip();
        await setup();
    });

    beforeEach(() => clearTestNotificationQueue());

    const getResult = getNotificationResult.bind(null, testApplication.nodes.TestCrudNotification, {});
    const getResult2 = getNotificationResult.bind(null, testApplication.nodes.TestCrudListener, {});

    it('can receive create notifications in listeners', async () => {
        await Test.withCommittedContext(async context => {
            await (
                await context.create(testApplication.nodes.TestCrudNotification, {
                    _id: 1,
                    id: 1,
                    booleanVal: true,
                    stringVal: 'created_1',
                })
            ).$.save();
        });
        const createdPromises = [getResult('testCreatedCallback'), getResult2('testCreatedCallback')];
        (await Promise.all(createdPromises)).forEach(r => assert.deepEqual(r, [{ _id: 1 }]));
    });

    it('can receive update notifications in listeners', async () => {
        await Test.withCommittedContext(async context =>
            (
                await context.create(testApplication.nodes.TestCrudNotification, {
                    _id: 2,
                    id: 2,
                    booleanVal: false,
                    stringVal: 'created_2',
                })
            ).$.save(),
        );
        const createdPromises = [getResult('testCreatedCallback'), getResult2('testCreatedCallback')];

        for (let i = 0; i < createdPromises.length; i += 1) assert.deepEqual(await createdPromises[i], [{ _id: 2 }]);
        await Test.withCommittedContext(async context => {
            const node = await context.read(testApplication.nodes.TestCrudNotification, { id: 2 }, { forUpdate: true });
            await node.$.set({ stringVal: 'updated_2' });
            await node.$.save();
        });
        const updatedPromises = [getResult('testUpdatedCallback'), getResult2('testUpdatedCallback')];

        (await Promise.all(updatedPromises)).forEach(r => assert.deepEqual(r, [{ _id: 2 }]));
    });

    it('can receive delete notifications in listeners', async () => {
        await Test.withCommittedContext(async context =>
            (
                await context.create(testApplication.nodes.TestCrudNotification, {
                    _id: 3,
                    id: 3,
                    booleanVal: true,
                    stringVal: 'created_3',
                })
            ).$.save(),
        );
        const createdPromises = [getResult('testCreatedCallback'), getResult2('testCreatedCallback')];

        (await Promise.all(createdPromises)).forEach(r => assert.deepEqual(r, [{ _id: 3 }]));
        await Test.withCommittedContext(context =>
            context.delete(testApplication.nodes.TestCrudNotification, { id: 3 }),
        );
        const deletedPromises = [getResult('testDeletedCallback'), getResult2('testDeletedCallback')];

        (await Promise.all(deletedPromises)).forEach(r => assert.deepEqual(r, [{ _id: 3 }]));
    });

    it('can disable all CRUD notifications', async () => {
        // notifications disabled for all, promise will timeout
        await Test.withCommittedContext(
            async context =>
                (
                    await context.create(testApplication.nodes.TestCrudNotification, {
                        _id: 4,
                        id: 4,
                        booleanVal: true,
                        stringVal: 'created_4',
                    })
                ).$.save(),
            { disableAllCrudNotifications: true },
        );
        const createdPromises = [getResult('testCreatedCallback'), getResult2('testCreatedCallback')];

        await assert.isRejected(Promise.all(createdPromises), /notification listening timeout: .* ms/);
    });

    it('can disable CRUD notifications for specific tenant', async () => {
        // notifications disabled for tenant 777777777777777777777
        await Test.withCommittedContext(
            async context =>
                (
                    await context.create(testApplication.nodes.TestCrudNotification, {
                        _id: 5,
                        id: 5,
                        booleanVal: true,
                        stringVal: 'created_5',
                    })
                ).$.save(),
            { disableTenantCrudNotifications: true },
        );
        const createdPromises = [getResult('testCreatedCallback'), getResult2('testCreatedCallback')];

        await assert.isRejected(Promise.all(createdPromises), /notification listening timeout: .* ms/);

        await Test.withCommittedContext(async context =>
            (
                await context.create(testApplication.nodes.TestCrudNotification, {
                    _id: 6,
                    id: 6,
                    booleanVal: true,
                    stringVal: 'created_6',
                })
            ).$.save(),
        );
        const createdPromise2 = getResult('testCreatedCallback');

        assert.deepEqual(await createdPromise2, [{ _id: 6 }]);
    });
});
