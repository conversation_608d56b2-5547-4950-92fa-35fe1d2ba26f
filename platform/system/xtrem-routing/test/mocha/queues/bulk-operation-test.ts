import { AsyncMutationState } from '@sage/xtrem-communication';
import { Application, AsyncResponse, BatchLogEntry, ConditionVariable, Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import { sleepMillis } from '../../../lib/services/utils';
import { TestBulkOperation } from '../../fixtures/nodes/test-bulk-operation';
import { clearTestNotificationQueue, configureTestQueues, setup } from '../utils';

let conditionNotificationProcessed: ConditionVariable;

Application.emitter.on('notificationProcessed', () => {
    if (conditionNotificationProcessed) conditionNotificationProcessed.notifyAll();
});

async function testBulkDeleteWithListener(filter: string, expectedPayload: AsyncMutationState<boolean>) {
    const done = new ConditionVariable();
    let resultPayload: AsyncMutationState<boolean> | undefined;
    let resultCount = 0;

    TestBulkOperation.onResult = (_context, payload): AsyncResponse<void> => {
        delete payload.errorStack; // ignore it in deepEqual
        resultPayload = payload;
        resultCount += 1;
        done.notifyAll();
    };

    await Test.withCommittedContext(
        async context => {
            await context.notify(
                'TestBulkOperation/bulkDelete/start',
                { filter },
                {
                    replyTopic: 'TestBulkOperation/bulkDelete/result',
                },
            );
        },
        { userEmail: '<EMAIL>' },
    );

    await done.wait();
    assert.deepEqual(resultPayload, expectedPayload);
    assert.equal(resultCount, 1);
}

async function testBulkDeleteWithGraphQl(
    filter: string,
    expectedPayload: {
        status: string;
        result: boolean | null;
        errorMessage: string | null;
        logMessages: BatchLogEntry[] | null;
    },
) {
    conditionNotificationProcessed = Test.createConditionVariable('conditionNotificationProcessed');

    // filter is a stringify version of a filter object but then we need to stringify it a second time to get an escaped version
    const mutation = `mutation { xtremRouting { testBulkOperation { bulkDelete { start(filter: ${JSON.stringify(
        filter,
    )}) { trackingId } } } } }`;

    const submitted = await Test.withReadonlyContext(
        context =>
            Test.graphql<{
                xtremRouting: { testBulkOperation: { bulkDelete: { start: { trackingId: string } } } };
            }>(context, mutation),
        { userEmail: '<EMAIL>' },
    );
    assert.isObject(submitted);
    const trackingId = submitted.data.xtremRouting.testBulkOperation.bulkDelete.start.trackingId;
    assert.isString(trackingId);

    await conditionNotificationProcessed.wait();

    for (let i = 0; i < 20; i += 1) {
        const polled = await Test.withReadonlyContext(
            context =>
                Test.graphql<{
                    xtremRouting: {
                        testBulkOperation: {
                            bulkDelete: {
                                track: {
                                    status: string;
                                    result: boolean | null;
                                    errorMessage: string | null;
                                    logMessages: BatchLogEntry[];
                                };
                            };
                        };
                    };
                }>(
                    context,
                    `query { xtremRouting { testBulkOperation { bulkDelete { track(trackingId: "${trackingId}") { status, result, errorMessage, logMessages { level, message } } } } } }`,
                ),
            { userEmail: '<EMAIL>' },
        );
        assert.isObject(polled);

        const tracker = polled.data.xtremRouting.testBulkOperation.bulkDelete.track;
        if (tracker.status === 'pending' || tracker.status === 'running') {
            await sleepMillis(10);
        } else {
            assert.deepEqual(tracker, expectedPayload);
            return;
        }
    }
    assert.fail('polling failed');
}

function createTestNodes(codes: string[]) {
    return Test.withCommittedContext(context =>
        Promise.all(
            codes.map(async code => {
                const node = await context.create(TestBulkOperation, { code, description: `desc ${code}` });
                await node.$.save();
                return node._id;
            }),
        ),
    );
}

describe('Bulk operations', () => {
    before(async function before() {
        if (!configureTestQueues()) this.skip();
        await setup();
    });

    beforeEach(async () => {
        // We need to clear the queue
        await clearTestNotificationQueue();
    });

    it('can execute a bulk delete with a notification', async () => {
        const ids = await createTestNodes(['A1', 'A2']);
        const filter = { _id: { _in: ids } };

        await testBulkDeleteWithListener(JSON.stringify(filter), {
            status: 'success',
            result: true,
        });
        const len = await Test.withReadonlyContext(
            async context => (await context.query(TestBulkOperation, { filter }).toArray()).length,
        );
        assert.strictEqual(len, 0);
    });

    it('failed to execute a bulk delete with an empty filter', () =>
        testBulkDeleteWithListener('', {
            status: 'error',
            errorMessage: 'Bulk delete failed.',
        }));

    it('failed to execute a bulk delete with invalid operation filter', () =>
        testBulkDeleteWithListener("{ '_invalidOp': {} }", {
            status: 'error',
            errorMessage: 'Bulk delete failed.',
        }));

    it('can execute a bulk delete with graphql', async () => {
        const ids = await createTestNodes(['B1', 'B2']);
        const filter = { _id: { _in: ids } };
        await testBulkDeleteWithGraphQl(JSON.stringify(filter), {
            status: 'success',
            result: true,
            errorMessage: null,
            logMessages: [
                {
                    level: 'info',
                    message: 'TestBulkOperation: 2 records done.',
                },
            ],
        });
    });
    it('failed to execute a bulk delete with graphql using empty filter', async () => {
        await testBulkDeleteWithGraphQl('', {
            status: 'error',
            result: null,
            errorMessage: 'Bulk delete failed.',
            logMessages: [
                {
                    level: 'exception',
                    message: 'Filter is empty',
                },
            ],
        });
    });
    it('failed to execute a bulk delete with graphql', async () => {
        await testBulkDeleteWithGraphQl(JSON.stringify({ _invalidOp: {} }), {
            status: 'error',
            result: null,
            errorMessage: 'Bulk delete failed.',
            logMessages: [
                {
                    level: 'exception',
                    message: 'this: invalid operator: _invalidOp',
                },
            ],
        });
    });
});
