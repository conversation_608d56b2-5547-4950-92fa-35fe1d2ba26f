import { nodes as communicationNodes } from '@sage/xtrem-communication';
import {
    Application,
    asyncArray,
    ConditionVariable,
    Context,
    serviceOptionsMock,
    Test,
    withAdvisoryLock,
} from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import { assert } from 'chai';
import { testReceiveQueue } from '../../fixtures/queues/test-receive-queue';
import * as testApplication from '../../fixtures/test-application';
import { configureTestQueues, setup } from '../utils';

const { SysMessage, SysMessageHistory } = communicationNodes;

const tenantId = Test.defaultTenantId;
const locale = 'fr-CA';
const email = '<EMAIL>';

const isDemoTenantServiceOption = xtremSystem.serviceOptions.isDemoTenant;
function testCallbackResult() {
    return new Promise(resolve => {
        testApplication.nodes.TestMessage.testCallback = async (context, envelope) => {
            testApplication.nodes.TestMessage.testCallback = null;
            resolve({
                text: envelope.payload.text,
                tenantId: context.tenantId,
                locale: context.currentLocale,
                email: (await context.user)!.email,
            });
        };
    });
}

let conditionMessageForwardingStart: ConditionVariable;

Application.emitter.on('messageForwardingStart', () => {
    if (conditionMessageForwardingStart) conditionMessageForwardingStart.notifyAll();
});

let conditionOnMessageSent: ConditionVariable;

Application.emitter.on('messageSent', () => {
    if (conditionOnMessageSent) conditionOnMessageSent.notifyAll();
});

[false, true].forEach(isDemoTenant => {
    const message = isDemoTenant ? ' (with demo tenant)' : '';
    const auth = isDemoTenant
        ? { login: '<EMAIL>', persona: '<EMAIL>' }
        : { login: '<EMAIL>' };
    const persona = isDemoTenant ? '<EMAIL>' : '<EMAIL>';

    describe.skip(`Message tests${message}`, () => {
        const isDemoTenantOld = serviceOptionsMock.isDemoTenant;

        before(async function before(this: Mocha.Context) {
            if (!configureTestQueues()) this.skip();

            await setup();
            // we need to override the isDemoTenant service option used by the accessRightManager mock in order to not use the one provided by xtrem-system
            serviceOptionsMock.isDemoTenant = isDemoTenantServiceOption;

            if (isDemoTenant) {
                await Test.application.withCommittedContext(tenantId, context =>
                    Test.application.serviceOptionManager.activateServiceOptions(context, [isDemoTenantServiceOption]),
                );
            }
        });

        beforeEach(async () => {
            // We need to clear the queue
            await testReceiveQueue.clearQueue();
            testApplication.nodes.TestMessage.testCallback = null;
        });

        after(async () => {
            if (configureTestQueues() && isDemoTenant) {
                await Test.application.withCommittedContext(tenantId, context =>
                    Test.application.serviceOptionManager.deactivateServiceOptions(context, [
                        isDemoTenantServiceOption,
                    ]),
                );
                serviceOptionsMock.isDemoTenant = isDemoTenantOld;
            }
        });

        it(`cannot send a message from a readonly context${message}`, () =>
            Test.withReadonlyContext(
                async context => {
                    await assert.isRejected(
                        context.send(testApplication.queues.testSendQueue, {
                            payload: { text: 'hello' },
                            attributes: {
                                tenantId,
                                email,
                                locale,
                            },
                        }),
                        'send is allowed only in the context of a mutation',
                    );
                },
                { auth },
            ));

        it(`cannot send message containing invalid characters${message}`, () =>
            Test.withContext(testContext =>
                testContext.application.withCommittedContext(
                    testContext.tenantId,
                    async context => {
                        await assert.isRejected(
                            context.send(testApplication.queues.testSendQueue, {
                                payload: 'hell\u0000o',
                                attributes: {
                                    tenantId,
                                    email,
                                    locale,
                                },
                            }),
                            'message payload contains invalid characters: hell\u0000o',
                        );
                    },
                    { auth },
                ),
            ));

        it(`can receive a message from a message listener${message}`, async () => {
            // We need Test.withContext to create the tables. But we don't use its context
            // because it is uncommitted and the notification is not sent.
            // We create a second context with Test.committed
            await Test.withContext(testContext =>
                testContext.application.withCommittedContext(
                    testContext.tenantId,
                    context =>
                        context.send(testApplication.queues.testSendQueue, {
                            payload: {
                                text: 'hello',
                            },
                            attributes: {
                                tenantId,
                                email,
                                locale,
                            },
                        }),
                    { auth },
                ),
            );
            const result = await testCallbackResult();
            assert.deepEqual(result, { text: 'hello', tenantId, locale, email: persona });
        });

        it(`can send and receive many messages${message}`, async () => {
            const payloads: string[] = [];

            for (let index = 0; index < 20; index += 1) {
                payloads.push(`hello ${index}`);
            }

            // We need Test.withContext to create the tables. But we don't use its context
            // because it is uncommitted and the notification is not sent.
            // We create a second context with Test.committed
            await Test.withContext(testContext =>
                testContext.application.withCommittedContext(
                    testContext.tenantId,
                    context =>
                        asyncArray(payloads).forEach(payload =>
                            context.send(testApplication.queues.testSendQueue, {
                                payload: {
                                    text: payload,
                                },
                                attributes: {
                                    tenantId,
                                    email,
                                    locale,
                                },
                            }),
                        ),
                    { auth },
                ),
            );

            const results = [];
            for (let index = 0; index < payloads.length; index += 1) {
                const result = await new Promise(resolve => {
                    testApplication.nodes.TestMessage.testCallback = async (context, envelope) => {
                        testApplication.nodes.TestMessage.testCallback = null;
                        assert.equal(context.tenantId, Test.defaultTenantId);
                        if (isDemoTenant) {
                            assert.equal((await context.user)!.email, '<EMAIL>');
                        } else {
                            assert.equal((await context.user)!.email, '<EMAIL>');
                        }
                        assert.equal(context.currentLocale, 'fr-CA');
                        resolve(envelope.payload.text);
                    };
                });

                results.push(result);
            }
            assert.deepEqual(results, payloads);
        });

        it(`will log an error in SysMessageHistory if sending of message fails${message}`, async () => {
            // We need Test.withContext to create the tables. But we don't use its context
            // because it is uncommitted and the notification is not sent.
            // We create a second context with Test.committed
            await Test.application.withCommittedContext(null, testContext =>
                Context.tenantManager.ensureTenantExists(testContext, {
                    customer: {
                        id: tenantId,
                        name: `Customer for tenant ${tenantId}`,
                    },
                    tenant: {
                        id: tenantId,
                        name: 'Tenant for xtrem-communication tests',
                    },
                }),
            );
            return Test.withContext(async testContext => {
                conditionOnMessageSent = Test.createConditionVariable('conditionOnMessageSent');

                await testContext.application.withCommittedContext(tenantId, async context => {
                    await context.deleteMany(SysMessageHistory, {});
                    await context.send(testApplication.queues.testNonExistentQueue, {
                        payload: {
                            text: 'hello',
                        },
                        attributes: {
                            tenantId,
                            email,
                            locale,
                        },
                    });
                });
                await conditionOnMessageSent.wait();
                await Test.withContext(
                    async context => {
                        const messageHistory = await context
                            .query(SysMessageHistory, {
                                filter: { integrationSolution: 'testNonExistentQueue' },
                            })
                            .toArray();
                        assert.equal(messageHistory.length, 1);
                        assert.equal(
                            await messageHistory[0].errorMessage,
                            'No Url defined for queue testNonExistentQueue',
                        );
                    },
                    { tenantId },
                );
                // Also cannot query message history across tenants
                // TODO: Cannot test this users only loaded for default tenant, therefore this will fail with user
                // not found, review later
                // await (
                //     Test.withContext(
                //         async (context => {
                //             const messageHistory = await (
                //                 context
                //                     .query(SysMessageHistory, {
                //                         filter: { integrationSolution: 'testNonExistentQueue' },
                //                     })
                //                     .toArray(),
                //             );
                //             assert.equal(messageHistory.length, 0);
                //         }),

                //         { tenantId: '9'.repeat(21) },
                //     ),
                // );
            });
        });

        it(`waits to acquire an advisory lock, before processing message${message}`, () =>
            Test.withContext(
                // We need Test.withContext to create the tables. But we don't use its context
                // because it is uncommitted and the notification is not sent.
                // We create a second context with Test.committed
                async testContext => {
                    conditionMessageForwardingStart = Test.createConditionVariable('conditionMessageForwardingStart');
                    const advisoryLockId = 6744347829059596;
                    // after withAdvisoryLock finishes the message-router can acquired its pending lock and process
                    await withAdvisoryLock(testContext, advisoryLockId, async () => {
                        await testContext.application.withCommittedContext(
                            testContext.tenantId,
                            context =>
                                context.send(testApplication.queues.testSendQueue, {
                                    payload: {
                                        text: 'hello',
                                    },
                                    attributes: {
                                        tenantId,
                                        email,
                                        locale,
                                    },
                                }),
                            { auth },
                        );

                        // Wait for onMessageQueued to be called:
                        await conditionMessageForwardingStart.wait();
                        // message should still be here, since the message-router has an advisory lock on it
                        await testContext.application.withReadonlyContext(testContext.tenantId, async context => {
                            assert.equal(
                                await context.query(SysMessage, {
                                    filter: {
                                        queue: {
                                            _eq: testApplication.queues.testSendQueue.name,
                                        },
                                    },
                                }).length,
                                1,
                            );
                        });
                    });

                    const result = await testCallbackResult();
                    assert.deepEqual(result, { text: 'hello', tenantId, locale, email: persona });
                    // message is now processed
                    await testContext.application.withReadonlyContext(testContext.tenantId, async context => {
                        assert.equal(
                            await context.query(SysMessage, {
                                filter: { queue: { _eq: testApplication.queues.testSendQueue.name } },
                            }).length,
                            0,
                        );
                    });
                },
            ));

        it(`can start with a readonly context${message}`, async () => {
            // We need Test.withContext to create the tables. But we don't use its context
            // because it is uncommitted and the notification is not sent.
            // We create a second context with Test.committed
            await Test.withContext(testContext =>
                testContext.application.withCommittedContext(
                    testContext.tenantId,
                    context =>
                        context.send(testApplication.queues.testSendQueue, {
                            payload: {
                                startsReadOnly: true,
                            },
                            attributes: {
                                tenantId,
                                email,
                                locale,
                            },
                        }),
                    { auth },
                ),
            );

            const result = await new Promise(resolve => {
                testApplication.nodes.TestMessage.testCallback = (context, envelope) => {
                    if (envelope.payload.startsReadOnly) {
                        testApplication.nodes.TestMessage.testCallback = null;
                        resolve({
                            text: String(context.isWritable),
                            startsReadOnly: !!envelope.payload.startsReadOnly,
                        });
                    }
                };
            });
            assert.deepEqual(result, { text: 'false', startsReadOnly: true });
        });
    });
});
