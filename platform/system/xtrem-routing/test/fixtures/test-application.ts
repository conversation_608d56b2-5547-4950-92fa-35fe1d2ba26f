// The `xtrem test` command checks for a test/fixtures/test-application.ts file (this file)
// and combines its exports with the lib/index.ts exports before running the mocha unit tests.
import { getTestFixtures, TestSysVendor, TestUser } from '@sage/xtrem-core';
import { configureTestQueues } from '../mocha/utils';
import * as dataTypes from './data-types/_index';
import * as nodeExtensions from './node-extensions/_index';
import * as testNodes from './nodes/_index';
import * as queues from './queues/_index';

const fixtures = getTestFixtures();

fixtures.updateContext();

const nodes = { ...testNodes, TestUser, TestSysVendor };

export { dataTypes, nodes, nodeExtensions, queues };

configureTestQueues();
