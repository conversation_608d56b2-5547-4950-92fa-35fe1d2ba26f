{"@sage/xtrem-communication": [{"queue": "routing", "topic": "TestAsyncMutation/squareIt/start", "sourceFileName": "test-async-mutation.ts"}, {"queue": "routing", "topic": "TestAsyncMutation/squareIt/result", "sourceFileName": "test-async-mutation.ts"}, {"queue": "routing", "topic": "TestBulkOperation/bulkDelete/start", "sourceFileName": "test-bulk-operation.ts"}, {"queue": "routing", "topic": "TestBulkOperation/bulkDelete/result", "sourceFileName": "test-bulk-operation.ts"}, {"queue": "routing", "topic": "TestCrudNotification/created", "sourceFileName": "test-crud-notification.ts"}, {"queue": "routing", "topic": "TestCrudNotification/updated", "sourceFileName": "test-crud-notification.ts"}, {"queue": "routing", "topic": "TestCrudNotification/deleted", "sourceFileName": "test-crud-notification.ts"}, {"queue": "routing", "topic": "TestNotification/notification1", "sourceFileName": "test-notification.ts"}, {"queue": "routing", "topic": "TestNotification/notification2", "sourceFileName": "test-notification.ts"}, {"queue": "routing", "topic": "TestNotification/notificationReply", "sourceFileName": "test-notification.ts"}, {"queue": "routing", "topic": "TestNotification/withoutOnError", "sourceFileName": "test-notification.ts"}, {"queue": "routing", "topic": "TestNotification/withOnError", "sourceFileName": "test-notification.ts"}, {"queue": "routing", "topic": "TestNotification/notification-extension1", "sourceFileName": "test-notification-extension.ts"}]}