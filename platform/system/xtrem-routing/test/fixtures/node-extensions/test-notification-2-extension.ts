import { decorators, NodeExtension } from '@sage/xtrem-core';
import { descriptionDataType } from '../data-types/data-types';
import { TestNotification2 } from '../nodes/test-notification';

@decorators.nodeExtension<TestNotification2Extension>({
    extends: () => TestNotification2,
})
export class TestNotification2Extension extends NodeExtension<TestNotification2> {
    @decorators.stringProperty<TestNotification2Extension, 'stringExtensionVal'>({
        isPublished: true,
        isStored: true,
        dataType: () => descriptionDataType,
    })
    readonly stringExtensionVal: Promise<string>;
}

declare module '../nodes/test-notification' {
    export interface TestNotification2 extends TestNotification2Extension {}
}
