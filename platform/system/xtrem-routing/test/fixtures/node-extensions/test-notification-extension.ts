import { AsyncResponse, Context, decorators, NodeExtension } from '@sage/xtrem-core';
import { logger } from '../../../lib/services/utils';
import { TestNotification, TestNotificationPayload } from '../nodes/test-notification';

@decorators.nodeExtension<TestNotificationExtension>({
    extends: () => TestNotification,
})
export class TestNotificationExtension extends NodeExtension<TestNotification> {
    @decorators.notificationListener<typeof TestNotificationExtension>({
        topic: 'TestNotification/notification-extension1',
    })
    static async onExtensionNotification1(context: Context, payload: TestNotificationPayload): Promise<void> {
        if (!this.testExtensionCallback1) throw new Error('no test callback');
        await this.testExtensionCallback1(context, payload);
    }

    // Unit test assigns its own callback to the this static variable
    static testExtensionCallback1:
        | ((context: Context, payload: TestNotificationPayload) => AsyncResponse<void>)
        | null = (context, payload) => {
        logger.warn(`test callback 1: discarding notification: ${JSON.stringify(payload)}`);
    };
}

declare module '../nodes/test-notification' {
    export interface TestNotification extends TestNotificationExtension {}
}
