import { AsyncMutationState } from '@sage/xtrem-communication';
import { AsyncResponse, Context, decorators, Node } from '@sage/xtrem-core';
import { codeDataType, descriptionDataType } from '../data-types/data-types';

@decorators.node<TestBulkOperation>({
    isPublished: true,
    storage: 'sql',
    canDelete: true,
    canBulkDelete: true,
})
export class TestBulkOperation extends Node {
    @decorators.stringProperty<TestBulkOperation, 'code'>({
        isPublished: true,
        isStored: true,
        isFrozen: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.stringProperty<TestBulkOperation, 'description'>({
        isPublished: true,
        isStored: true,
        dataType: () => descriptionDataType,
    })
    readonly description: Promise<string>;

    @decorators.notificationListener<typeof TestBulkOperation>({
        topic: 'TestBulkOperation/bulkDelete/result',
    })
    static onResult(_context: Context, payload: AsyncMutationState<boolean>): AsyncResponse<void> {
        throw new Error(`test result callback: discarding notification: ${payload}`);
    }
}
