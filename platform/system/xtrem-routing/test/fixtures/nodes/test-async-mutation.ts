import { AsyncMutationState, sleepMillis } from '@sage/xtrem-communication';
import { AsyncResponse, BusinessRuleError, Context, decorators, integer, Node } from '@sage/xtrem-core';

@decorators.node<TestAsyncMutation>({
    isPublished: true,
    storage: 'json',
})
export class TestAsyncMutation extends Node {
    @decorators.asyncMutation<typeof TestAsyncMutation, 'squareIt'>({
        isPublished: true,
        isSchedulable: true,
        parameters: [{ name: 'value', type: 'integer' }],
        return: 'integer',
    })
    static async squareIt(context: Context, value: integer): Promise<integer> {
        await sleepMillis(20);
        if (value === 13) throw new BusinessRuleError('Sorry, I am superstitious');
        return value * value;
    }

    @decorators.notificationListener<typeof TestAsyncMutation>({
        topic: 'TestAsyncMutation/squareIt/result',
    })
    static onResult(context: Context, payload: AsyncMutationState<integer>): AsyncResponse<void> {
        throw new Error(`test result callback: discarding notification: ${payload}`);
    }
}
