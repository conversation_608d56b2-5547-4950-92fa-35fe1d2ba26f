import { Context, decorators, integer, Node } from '@sage/xtrem-core';
import { logger } from '../../../lib/services/utils';
import { descriptionDataType } from '../data-types/data-types';

export interface TestCrudNotificationJson {
    id?: number;
    code?: string;
    isValidated?: boolean;
    text?: {
        fr?: string;
        en?: string;
        es?: string;
    };
    authors?: Array<string>;
}

@decorators.node<TestCrudNotification>({
    tableName: 'TestCrudNotification',
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canDeleteMany: true,
    indexes: [{ orderBy: { id: 1 }, isUnique: true }],
    notifies: ['created', 'updated', 'deleted'],
})
export class TestCrudNotification extends Node {
    @decorators.integerProperty<TestCrudNotification, 'id'>({
        isPublished: true,
        isStored: true,
    })
    readonly id: Promise<integer>;

    @decorators.booleanProperty<TestCrudNotification, 'booleanVal'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly booleanVal: Promise<boolean | null>;

    @decorators.stringProperty<TestCrudNotification, 'stringVal'>({
        isPublished: true,
        isStored: true,
        dataType: () => descriptionDataType,
    })
    readonly stringVal: Promise<string>;

    @decorators.notificationListener({
        topic: 'TestCrudNotification/created',
    })
    static onCreated(context: Context, payload: object): void {
        if (!this.testCreatedCallback) throw new Error('no created test callback');
        this.testCreatedCallback(context, payload);
    }

    // Unit test assigns its own callback to the this static variable
    static testCreatedCallback: ((context: Context, payload: any) => void) | null = (context, payload) => {
        logger.warn(`test created callback: discarding notification: ${payload}`);
    };

    @decorators.notificationListener({
        topic: 'TestCrudNotification/updated',
    })
    static onUpdated(context: Context, payload: object): void {
        if (!this.testUpdatedCallback) throw new Error('no updated test callback');
        this.testUpdatedCallback(context, payload);
    }

    // Unit test assigns its own callback to the this static variable
    static testUpdatedCallback: ((context: Context, payload: any) => void) | null = (context, payload) => {
        logger.warn(`test updated callback: discarding notification: ${payload}`);
    };

    @decorators.notificationListener({
        topic: 'TestCrudNotification/deleted',
    })
    static onDeleted(context: Context, payload: object): void {
        if (!this.testDeletedCallback) throw new Error('no deleted test callback');
        this.testDeletedCallback(context, payload);
    }

    // Unit test assigns its own callback to the this static variable
    static testDeletedCallback: ((context: Context, payload: any) => void) | null = (context, payload) => {
        logger.warn(`test deleted callback: discarding notification: ${payload}`);
    };
}

@decorators.node<TestCrudListener>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canDeleteMany: true,
    indexes: [{ orderBy: { id: 1 }, isUnique: true }],
})
export class TestCrudListener extends Node {
    @decorators.integerProperty<TestCrudListener, 'id'>({
        isPublished: true,
        isStored: true,
    })
    readonly id: Promise<integer>;

    @decorators.notificationListener({
        topic: 'TestCrudNotification/created',
    })
    static onCreated(context: Context, payload: object): void {
        if (!this.testCreatedCallback) throw new Error('no created test callback');
        this.testCreatedCallback(context, payload);
    }

    // Unit test assigns its own callback to the this static variable
    static testCreatedCallback: ((context: Context, payload: any) => void) | null = (context, payload) => {
        logger.warn(`test created callback: discarding notification: ${payload}`);
    };

    @decorators.notificationListener({
        topic: 'TestCrudNotification/updated',
    })
    static onUpdated(context: Context, payload: object): void {
        if (!this.testUpdatedCallback) throw new Error('no updated test callback');
        this.testUpdatedCallback(context, payload);
    }

    // Unit test assigns its own callback to the this static variable
    static testUpdatedCallback: ((context: Context, payload: any) => void) | null = (context, payload) => {
        logger.warn(`test updated callback: discarding notification: ${payload}`);
    };

    @decorators.notificationListener({
        topic: 'TestCrudNotification/deleted',
    })
    static onDeleted(context: Context, payload: object): void {
        if (!this.testDeletedCallback) throw new Error('no deleted test callback');
        this.testDeletedCallback(context, payload);
    }

    // Unit test assigns its own callback to the this static variable
    static testDeletedCallback: ((context: Context, payload: any) => void) | null = (context, payload) => {
        logger.warn(`test deleted callback: discarding notification: ${payload}`);
    };
}
