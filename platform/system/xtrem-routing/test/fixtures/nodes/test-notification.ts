import { nodes as communicationNodes } from '@sage/xtrem-communication';
import { AsyncResponse, Context, decorators, integer, Node } from '@sage/xtrem-core';
import { logger } from '../../../lib/services/utils';
import { descriptionDataType } from '../data-types/data-types';

const { SysNotificationState } = communicationNodes;

export interface TestNotificationPayload {
    text: string;
}

@decorators.node<TestNotification>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canDeleteMany: true,
    indexes: [{ orderBy: { id: 1 }, isUnique: true }],
})
export class TestNotification extends Node {
    @decorators.integerProperty<TestNotification, 'id'>({
        isPublished: true,
        isStored: true,
    })
    readonly id: Promise<integer>;

    @decorators.booleanProperty<TestNotification, 'booleanVal'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly booleanVal: Promise<boolean | null>;

    @decorators.stringProperty<TestNotification, 'stringVal'>({
        isPublished: true,
        isStored: true,
        dataType: () => descriptionDataType,
    })
    readonly stringVal: Promise<string>;

    @decorators.notificationListener<typeof TestNotification>({
        topic: 'TestNotification/notification1',
    })
    static async onNotification1(context: Context, payload: TestNotificationPayload): Promise<void> {
        if (!this.testCallback1) throw new Error('no test callback 1');
        await this.testCallback1(context, payload);
    }

    // Unit test assigns its own callback to the this static variable
    static testCallback1: ((context: Context, payload: TestNotificationPayload) => AsyncResponse<void>) | null = (
        context,
        payload,
    ) => {
        logger.warn(`test callback 1: discarding notification: ${JSON.stringify(payload)}`);
    };

    @decorators.notificationListener<typeof TestNotification>({
        topic: 'TestNotification/notification2',
    })
    static onNotification2(context: Context, payload: TestNotificationPayload): void {
        if (!this.testCallback2) throw new Error('no test callback 2');
        this.testCallback2(context, payload);
    }

    // Unit test assigns its own callback to the this static variable
    static testCallback2: ((context: Context, payload: TestNotificationPayload) => void) | null = (
        context,
        payload,
    ) => {
        logger.warn(`test callback 2: discarding notification: ${JSON.stringify(payload)}`);
    };

    @decorators.notificationListener<typeof TestNotification>({
        topic: 'TestNotification/notificationReply',
    })
    static async onNotificationReply(context: Context, payload: TestNotificationPayload): Promise<void> {
        await context.reply<TestNotificationPayload>('TestNotification/notification1', payload);
    }

    @decorators.notificationListener<typeof TestNotification>({
        topic: 'TestNotification/withoutOnError',
    })
    static withoutOnError(): void {
        throw Error('TestNotification/withoutOnError');
    }

    @decorators.notificationListener<typeof TestNotification>({
        topic: 'TestNotification/withOnError',
        onError: async (context, envelope, error) =>
            // eslint-disable-next-line no-void
            void (await SysNotificationState.upsert(context, { envelope, status: 'pending', error })),
    })
    static withOnError(): void {
        throw Error('TestNotification/withOnError');
    }
}

@decorators.node<TestNotification2>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canDeleteMany: true,
    indexes: [{ orderBy: { id: 1 }, isUnique: true }],
})
export class TestNotification2 extends Node {
    @decorators.integerProperty<TestNotification2, 'id'>({
        isPublished: true,
        isStored: true,
    })
    readonly id: Promise<integer>;

    @decorators.booleanProperty<TestNotification2, 'booleanVal'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly booleanVal: Promise<boolean | null>;

    @decorators.stringProperty<TestNotification2, 'stringVal'>({
        isPublished: true,
        isStored: true,
        dataType: () => descriptionDataType,
    })
    readonly stringVal: Promise<string>;
}
