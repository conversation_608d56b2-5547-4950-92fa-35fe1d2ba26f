import { MessageEnvelope } from '@sage/xtrem-communication';
import { Context, decorators, integer, Node } from '@sage/xtrem-core';
import { logger } from '../../../lib/services/utils';
import * as testApplication from '../test-application';

export interface TestMessagePayload {
    text: string;
    throwError?: boolean;
    startsReadOnly?: boolean;
}

@decorators.node<TestMessage>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canDeleteMany: true,
    indexes: [{ orderBy: { id: 1 }, isUnique: true }],
})
export class TestMessage extends Node {
    @decorators.integerProperty<TestMessage, 'id'>({
        isPublished: true,
        isStored: true,
    })
    readonly id: Promise<integer>;

    @decorators.booleanProperty<TestMessage, 'booleanVal'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly booleanVal: Promise<boolean | null>;

    @decorators.stringProperty<TestMessage, 'stringVal'>({
        isPublished: true,
        isStored: true,
        dataType: () => testApplication.dataTypes.descriptionDataType,
    })
    readonly stringVal: Promise<string>;

    @decorators.messageListener<typeof TestMessage, TestMessagePayload>({
        queue: () => testApplication.queues.testReceiveQueue,
        format: 'json',
        getTenantId(envelope) {
            return envelope.attributes.tenantId;
        },
        async getContextAttributes(context, envelope) {
            return {
                user: await Context.accessRightsManager.getUser(context, envelope.attributes.email),
                locale: envelope.attributes.locale,
            };
        },
        async onError(context, envelope, error) {
            if (envelope.payload.throwError) {
                const node = await context.create(TestMessage, {
                    stringVal: error.message,
                    id: Number(Math.floor(Math.random() * 32)),
                });
                await node.$.save();
            }
        },
        startsReadOnly(context, envelope) {
            return !!envelope.payload.startsReadOnly;
        },
    })
    static onMessageFromTestQueue(context: Context, envelope: MessageEnvelope<TestMessagePayload>): void {
        const payload = envelope.payload;
        if (payload.throwError) throw new Error(payload.text);

        if (!this.testCallback) throw new Error('no test callback');
        this.testCallback(context, envelope);
    }

    // Unit test assigns its own callback to the this static variable
    static testCallback: ((context: Context, envelope: MessageEnvelope<TestMessagePayload>) => void) | null = (
        context,
        envelope,
    ) => {
        logger.warn(`test listener: discarding message: ${JSON.stringify(envelope)}`);
    };

    static counter = 0;

    @decorators.messageListener<typeof TestMessage, TestMessagePayload>({
        queue: () => testApplication.queues.testVisibilityMultipleListenersReceiveQueue1,
        format: 'json',
        getTenantId(envelope) {
            return envelope.attributes.tenantId;
        },
        async getContextAttributes(context, envelope) {
            return {
                user: await Context.accessRightsManager.getUser(context, envelope.attributes.email),
                locale: envelope.attributes.locale,
            };
        },
    })
    static onMessageVisibilityMultipleListenersReceiveQueueTest1(
        context: Context,
        envelope: MessageEnvelope<TestMessagePayload>,
    ): void {
        if (!this.testCallback) throw new Error('no test callback');
        this.testCallback(context, envelope);
    }

    @decorators.messageListener<typeof TestMessage, TestMessagePayload>({
        queue: () => testApplication.queues.testVisibilityMultipleListenersReceiveQueue2,
        format: 'json',

        getTenantId(envelope) {
            return envelope.attributes.tenantId;
        },
        async getContextAttributes(context, envelope) {
            return {
                user: await Context.accessRightsManager.getUser(context, envelope.attributes.email),
                locale: envelope.attributes.locale,
            };
        },
    })
    static onMessageVisibilityMultipleListenersReceiveQueueTest2(
        context: Context,
        envelope: MessageEnvelope<TestMessagePayload>,
    ): void {
        if (!this.testCallback) throw new Error('no test callback');
        this.testCallback(context, envelope);
    }
}
