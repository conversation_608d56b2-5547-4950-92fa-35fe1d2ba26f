<table class="query-table" data-context-object-type="ShowCaseProvider" data-context-object-path="xtremShowCase.showCaseProvider.query.edges" data-context-filter="[]" data-context-list-order="{&quot;_id&quot;:&quot;ascending&quot;,&quot;additionalInfo&quot;:&quot;ascending&quot;,&quot;dateField&quot;:&quot;ascending&quot;}" data-alias="XSdPUIEZ"><thead class="query-table-head"><tr class="query-table-row"><td class="query-table-cell" style="background-color:#0000001a;border:1px solid #000000;padding:2px;"><p>{{ translatedContent "911bb8e73724fe9b06a7e1e3e3176c7e" }}</p></td><td class="query-table-cell" style="background-color:#dfdfdf;border:1px solid #000000;"><p>{{ translatedContent "49ee3087348e8d44e1feda1917443987" }}</p></td><td class="query-table-cell" style="background-color:#dfdfdf;border:1px solid #000000;"><p>{{ translatedContent "e2543e2c474d165e36a14f4da1bfeb41" }}</p></td><td class="query-table-cell" style="background-color:#0000001a;border:1px solid #000000;padding:2px;"><p>{{ translatedContent "9554ce40e7959ce198210376c97150b3" }}</p></td></tr></thead><tbody class="query-table-body"><!--{{#each XSdPUIEZ.showCaseProvider.query.edges}}{{#with node}}--><tr class="query-table-row"><td class="query-table-cell" style="border:1px solid #000000;padding:2px;vertical-align:middle;"><p><span class="property" data-property-display-label="_id" data-property-data-type="IntOrString" data-property-name="_id" data-property-data-format="" data-property-parent-context="ShowCaseProvider">{{_id}}</span></p></td><td class="query-table-cell" style="border:1px solid #000000;vertical-align:middle;"><section class="conditional-block" data-context-condition="[{&quot;conjunction&quot;:&quot;and&quot;,&quot;valueType1&quot;:&quot;property&quot;,&quot;valueType2&quot;:&quot;constant&quot;,&quot;_id&quot;:&quot;1&quot;,&quot;value1&quot;:{&quot;label&quot;:&quot;Date field&quot;,&quot;data&quot;:{&quot;name&quot;:&quot;dateField&quot;,&quot;title&quot;:&quot;Date field&quot;,&quot;canSort&quot;:true,&quot;canFilter&quot;:true,&quot;type&quot;:&quot;Date&quot;,&quot;isCustom&quot;:false,&quot;isMutable&quot;:false,&quot;isStored&quot;:true,&quot;isOnInputType&quot;:true,&quot;isOnOutputType&quot;:true,&quot;enumType&quot;:null,&quot;dataType&quot;:&quot;&quot;,&quot;targetNode&quot;:&quot;&quot;,&quot;isCollection&quot;:false,&quot;kind&quot;:&quot;SCALAR&quot;,&quot;label&quot;:&quot;Date field&quot;,&quot;node&quot;:&quot;Date&quot;,&quot;iconType&quot;:&quot;csv&quot;},&quot;id&quot;:&quot;dateField&quot;,&quot;key&quot;:&quot;dateField&quot;,&quot;labelKey&quot;:&quot;Date field&quot;,&quot;labelPath&quot;:&quot;Date field&quot;},&quot;value2&quot;:{&quot;formattedValue&quot;:&quot;30/06/2019&quot;,&quot;rawValue&quot;:&quot;2019-06-30&quot;},&quot;key&quot;:&quot;1&quot;,&quot;operator&quot;:&quot;lessThan&quot;}]"><!--{{#if ( lt dateField "2019-06-30" )}}--><div class="conditional-block-body"><span class="property" data-property-display-label="Text field" data-property-data-type="String" data-property-name="textField" data-property-data-format="" data-property-parent-context="ShowCaseProvider">{{textField}}</span></div><!--{{/if}}--><div class="conditional-block-footer">&nbsp;</div></section></td><td class="query-table-cell" style="border:1px solid #000000;"><p><span class="property" data-property-display-label="Date field" data-property-data-type="Date" data-property-name="dateField" data-property-data-format="FullDate" data-property-parent-context="ShowCaseProvider">{{formatDate dateField 'FullDate'}}</span></p></td><td class="query-table-cell" style="border:1px solid #000000;padding:2px;vertical-align:middle;"><p><span class="property" data-property-display-label="Net price" data-property-data-type="Decimal" data-property-name="flagshipProduct.netPrice" data-property-data-format="2" data-property-parent-context="ShowCaseProvider">{{formatNumber flagshipProduct.netPrice 2}}</span></p></td></tr><tr class="query-table-row" data-hidden="1"><td class="query-table-cell" colspan="4"><p>&nbsp;</p></td></tr><!--{{/with}}{{/each}}--><tr class="query-table-row" data-hidden="1"><td class="query-table-cell" colspan="4"><p>&nbsp;</p></td></tr></tbody><tfoot class="query-table-footer"><tr class="query-table-row"><td class="query-table-cell"><p>&nbsp;</p></td><td class="query-table-cell"><p>{{ translatedContent "19f13ba8e866a7d3af64e13c958be668" }}</p></td><td class="query-table-cell"><p>&nbsp;</p></td><td class="query-table-cell"><p>&nbsp;</p></td></tr></tfoot></table>