<table class="query-table" data-context-object-type="ShowCaseCountry" data-context-object-path="xtremShowCase.showCaseCountry.query.edges" data-context-filter="[]" data-context-list-order="{&quot;_id&quot;:&quot;ascending&quot;,&quot;code&quot;:&quot;ascending&quot;,&quot;name&quot;:&quot;ascending&quot;,&quot;phoneCountryCode&quot;:&quot;ascending&quot;}" data-alias="ljTiigeB"><thead class="query-table-head"><tr class="query-table-row"><td class="query-table-cell" style="background-color:#0000001a;border:1px solid #000000;padding:2px;vertical-align:top;"><p>{{ translatedContent "911bb8e73724fe9b06a7e1e3e3176c7e" }}</p></td><td class="query-table-cell" style="background-color:#0000001a;border:1px solid #000000;padding:2px;vertical-align:top;"><p>{{ translatedContent "ca0dbad92a874b2f69b549293387925e" }}</p></td><td class="query-table-cell" style="background-color:#0000001a;border:1px solid #000000;padding:2px;vertical-align:top;"><p>{{ translatedContent "49ee3087348e8d44e1feda1917443987" }}</p></td><td class="query-table-cell" style="background-color:#0000001a;border:1px solid #000000;padding:2px;vertical-align:top;"><p>{{ translatedContent "68e2481cf8e6c0de8a211e8be4d94bed" }}</p></td></tr></thead><tbody class="query-table-body"><!--{{#each ljTiigeB.showCaseCountry.query.edges}}{{#with node}}--><tr class="query-table-row"><td class="query-table-cell" style="border:1px solid #000000;padding:2px;vertical-align:top;"><p><span class="property" data-property-display-label="_id" data-property-data-type="IntOrString" data-property-name="_id" data-property-data-format="" data-property-parent-context="ShowCaseCountry">{{_id}}</span></p></td><td class="query-table-cell" style="border:1px solid #000000;padding:2px;vertical-align:top;"><p><span class="property" data-property-display-label="Code" data-property-data-type="String" data-property-name="code" data-property-data-format="" data-property-parent-context="ShowCaseCountry">{{code}}</span></p></td><td class="query-table-cell" style="border:1px solid #000000;padding:2px;vertical-align:top;"><p><span class="property" data-property-display-label="Name" data-property-data-type="String" data-property-name="name" data-property-data-format="" data-property-parent-context="ShowCaseCountry">{{name}}</span></p></td><td class="query-table-cell e-right-align" style="border:1px solid #000000;padding:2px;vertical-align:top;"><p><span class="property" data-property-display-label="Phone country code" data-property-data-type="Int" data-property-name="phoneCountryCode" data-property-data-format="" data-property-parent-context="ShowCaseCountry">{{phoneCountryCode}}</span></p></td></tr><tr class="query-table-row" data-hidden="1"><td class="query-table-cell" colspan="4"><p>&nbsp;</p></td></tr><!--{{/with}}{{/each}}--><tr class="query-table-row" data-hidden="1"><td class="query-table-cell" colspan="4"><p>&nbsp;</p></td></tr></tbody><tfoot class="query-table-footer"><tr class="query-table-row"><td class="query-table-cell"><p>&nbsp;</p></td><td class="query-table-cell"><p>&nbsp;</p></td><td class="query-table-cell"><p>&nbsp;</p></td><td class="query-table-cell"><p>&nbsp;</p></td></tr></tfoot></table>