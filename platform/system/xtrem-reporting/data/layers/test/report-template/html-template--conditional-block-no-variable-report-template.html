<table class="query-table" data-context-object-type="ShowCaseProvider" data-context-object-path="xtremShowCase.showCaseProvider.query.edges" data-context-filter="[]" data-context-list-order="{&quot;_id&quot;:&quot;ascending&quot;,&quot;dateField&quot;:&quot;ascending&quot;,&quot;textField&quot;:&quot;ascending&quot;,&quot;flagshipProduct.netPrice&quot;:&quot;ascending&quot;}" data-alias="tmUqqnGV"><thead class="query-table-head"><tr class="query-table-row"><td class="query-table-cell" style="background-color:#0000001a;border:1px solid #000000;padding:2px;vertical-align:top;"><p>{{ translatedContent "911bb8e73724fe9b06a7e1e3e3176c7e" }}</p></td><td class="query-table-cell" style="background-color:#0000001a;border:1px solid #000000;padding:2px;vertical-align:top;"><p>{{ translatedContent "e2543e2c474d165e36a14f4da1bfeb41" }}</p></td><td class="query-table-cell" style="background-color:#0000001a;border:1px solid #000000;padding:2px;vertical-align:top;"><p>{{ translatedContent "40ac89df9ff895152e53bb7ffb13a024" }}</p></td><td class="query-table-cell" style="background-color:#0000001a;border:1px solid #000000;padding:2px;vertical-align:top;"><p>{{ translatedContent "9554ce40e7959ce198210376c97150b3" }}</p></td></tr></thead><tbody class="query-table-body"><!--{{#each tmUqqnGV.showCaseProvider.query.edges}}{{#with node}}--><tr class="query-table-row"><td class="query-table-cell" style="border:1px solid #000000;padding:2px;vertical-align:top;"><p><span class="property" data-property-display-label="_id" data-property-data-type="IntOrString" data-property-name="_id" data-property-data-format="" data-property-parent-context="ShowCaseProvider">{{_id}}</span></p></td><td class="query-table-cell" style="border:1px solid #000000;padding:2px;vertical-align:top;"><p><span class="property" data-property-display-label="Date field" data-property-data-type="Date" data-property-name="dateField" data-property-data-format="FullDate" data-property-parent-context="ShowCaseProvider">{{formatDate dateField 'FullDate'}}</span></p></td><td class="query-table-cell" style="border:1px solid #000000;padding:2px;vertical-align:top;"><p><span class="property" data-property-display-label="Text field" data-property-data-type="String" data-property-name="textField" data-property-data-format="" data-property-parent-context="ShowCaseProvider">{{textField}}</span></p></td><td class="query-table-cell e-right-align" style="border:1px solid #000000;padding:2px;vertical-align:top;"><p><span class="property" data-property-display-label="Net price" data-property-data-type="Decimal" data-property-name="flagshipProduct.netPrice" data-property-data-format="2" data-property-parent-context="ShowCaseProvider">{{formatNumber flagshipProduct.netPrice 2}}</span></p></td></tr><tr class="query-table-row" data-hidden="1"><td class="query-table-cell" colspan="4"><p>&nbsp;</p></td></tr><!--{{/with}}{{/each}}--><tr class="query-table-row" data-hidden="1"><td class="query-table-cell" colspan="4"><p>&nbsp;</p></td></tr></tbody><tfoot class="query-table-footer"><tr class="query-table-row"><td class="query-table-cell"><p>&nbsp;</p></td><td class="query-table-cell"><p>&nbsp;</p></td><td class="query-table-cell"><p>&nbsp;</p></td><td class="query-table-cell"><p>&nbsp;</p></td></tr></tfoot></table>