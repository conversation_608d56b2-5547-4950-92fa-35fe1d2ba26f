{"edges": [{"id": "test-started-1--out-1", "source": "test-started-1", "target": "print-document-1", "sourceHandle": "out"}, {"id": "print-document-1--out-1", "source": "print-document-1", "target": "test-stub-1", "sourceHandle": "out"}], "nodes": [{"id": "test-started-1", "data": {"topic": "WorkflowProcess/testStarted", "parameters": [{"name": "userId", "type": "String"}], "stepVariables": [{"path": "test.parameters.userId", "type": "String", "title": "User id"}], "outputVariableName": "test"}, "type": "test-started", "width": 280, "height": 62, "origin": [0.5, 0], "dragging": false, "position": {"x": 440, "y": 40}, "selected": false, "positionAbsolute": {"x": 440, "y": 40}}, {"id": "print-document-1", "data": {"report": "userDetails", "subtitle": "userDetails", "localizedTitle": {"en-US": "Print user details", "fr-FR": "Imprimer les détails de l'utilisateur"}, "stepVariables": [{"path": "printDocument.downloadUrl", "type": "String", "title": "Generated report URL"}], "reportParameters": [{"name": "_id", "value": "<EMAIL>", "isVariable": false}], "outputVariableName": "printDocument"}, "type": "print-document", "width": 280, "height": 69, "origin": [0.5, 0], "dragging": false, "position": {"x": 440, "y": 160}, "selected": false, "positionAbsolute": {"x": 440, "y": 160}}, {"id": "test-stub-1", "data": {"subtitle": null, "stepVariables": [{"path": "done", "type": "Float", "title": "End test result"}], "outputVariableName": "done"}, "type": "test-stub", "width": 280, "height": 62, "origin": [0.5, 0], "dragging": false, "position": {"x": 440, "y": 300}, "selected": false, "positionAbsolute": {"x": 440, "y": 300}}]}