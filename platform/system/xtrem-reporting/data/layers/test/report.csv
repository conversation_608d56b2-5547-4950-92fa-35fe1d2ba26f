"name";"_vendor";"description";"parent_package";"active_template";"report_type";"is_factory";"printing_type"
"userDetails";;"Basic information about a user";"xtrem-reporting";"userTemplate";"printedDocument";"Y";"multiple"
"companyDetails";;"Basic information about a company";"xtrem-reporting";"companyTemplate";"printedDocument";"N";"multiple"
"testReport";;"Report for use by unit tests";"xtrem-reporting";"testTemplate";"printedDocument";"N";"multiple"
"noActiveTemplate";;"Basic information about a company";"xtrem-reporting";;"printedDocument";"N";"multiple"
"activeTemplateReport";;"Active Template used for automation script";"xtrem-reporting";"activeTemplate";"printedDocument";"N";"multiple"
"errorReport";;"This report is used to test error cases with the code field using global objects and methods";"xtrem-reporting";"errorTemplate";"printedDocument";"N";"multiple"
"variableTestReport";;"test data for Report Template integration tests.";"xtrem-show-case";;"printedDocument";"N";"multiple"
"noVariableReport";;"Test report with no variables for Report Template integration tests";"xtrem-show-case";;"printedDocument";"N";"multiple"
