"report";"_sort_value";"_vendor";"name";"title";"is_mandatory";"type";"data_type"
"companyDetails";"100";;"email";"{""base"":""Email""}";"N";"string";
"userDetails";"100";;"_id";"{""base"":""Id""}";"N";"string";
"activeTemplateReport";"100";;"_id";"{""base"":""Id""}";"N";"integer";
"activeTemplateReport";"200";;"companyId";"{""base"":""Company""}";"N";"reference";"company"
"activeTemplateReport";"300";;"enabled";"{""base"":""Enabled""}";"Y";"boolean";
"testReport";"100";;"id";"{""base"":""Id""}";"N";"string";
"testReport";"200";;"active";"{""base"":""Active""}";"N";"string";
"variableTestReport";"10";;"String";"{""en"":""String"",""base"":""String"",""en-GB"":""String""}";"N";"string";
"variableTestReport";"20";;"Integer";"{""en"":""Integer"",""base"":""Integer"",""en-GB"":""Integer""}";"N";"integer";
"variableTestReport";"30";;"Decimal";"{""en"":""Decimal"",""base"":""Decimal"",""en-GB"":""Decimal""}";"N";"decimal";
"variableTestReport";"40";;"Boolean";"{""en"":""Boolean"",""base"":""Boolean"",""en-GB"":""Boolean""}";"N";"boolean";
"variableTestReport";"50";;"Date";"{""en"":""Date"",""base"":""Date"",""en-GB"":""Date""}";"N";"date";
"variableTestReport";"60";;"textStream";"{""en"":""Text stream"",""base"":""Text stream"",""en-GB"":""Text stream""}";"N";"textStream";
