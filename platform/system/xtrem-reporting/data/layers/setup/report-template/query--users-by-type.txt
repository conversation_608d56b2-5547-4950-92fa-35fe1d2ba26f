{
  qApp: xtremSystem {
    user {
      query(filter: "{userType:'application'}") {
        edges {
          node {
            email
            firstName
            lastName
            userType
          }
        }
      }
    }
  }
  
  {{#if includeSystemUsers}}
    qSys: xtremSystem {
      user {
        query(filter: "{userType:'system'}") {
          edges {
            node {
              email
              firstName
              lastName
              userType
            }
          }
        }
      }
    }
	{{/if}}

}

