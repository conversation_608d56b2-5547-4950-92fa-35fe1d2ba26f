function capitalize(word) {
    return word?.replace(/^(.)/, c => c.toUpperCase());
}

const activeUsers = queryResponse.xtremSystem.user.query.edges.filter(edge => edge.node.isActive);

const capitalizedUsers = activeUsers.map(edge => {
    const pieces = edge.node.email.split('@');
    const segments = pieces[0].split('.');
    const firstName = capitalize(segments[0]);
    const lastName = capitalize(segments[1]);
    edge.node.email = `${firstName}${lastName ? '.' + lastName : ''}@${pieces[1]}`;

    return edge;
});

return  capitalizedUsers;