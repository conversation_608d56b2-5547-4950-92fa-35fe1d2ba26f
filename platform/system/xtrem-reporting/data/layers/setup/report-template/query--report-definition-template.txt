{
  query1: xtremReporting {
    report {
      query {
        edges {
          node {
            _id
            name
            parentPackage
            activeTemplate {
              _id
            }
            isFactory
            description
            variables {
              query {
                edges {
                  node {
                    _id
                    name
                    type
                  }
                }
              }
            }
          }
        }
      }
    }
  }
  query2: xtremReporting {
    reportTemplate {
      query {
        edges {
          node {
            _id
            name
            isFactory
            query {
              value
            }
            code {
              value
            }
            htmlTemplate {
              value
            }
            styleSheet {
              value
            }
          }
        }
      }
    }
  }
}