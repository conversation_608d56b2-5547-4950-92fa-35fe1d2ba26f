<div class="entry">
    <h1>Users By Type</h1>

    <table>
        <tbody>
            {{#each xtremSystem.user.query.edges 'node.authorizationGroup.query.totalCount' 'sum'}}
                {{#printBreakIfPropertyChanged 'node.userType'}}
                    <tr>
                        <td colspan="5" class="user-type"><strong>{{node.userType}}</strong></td>
                    </tr>
                    <tr>
                        <td class="header">First Name</td>
                        <td class="header">Last Name</td>
                        <td class="header">Email</td>
                        <td class="header">User Type</td>
                        <td class="header">Num of Auth Groups</td>

                    </tr>
                {{/printBreakIfPropertyChanged}}
                <tr>
                    <td>{{node.firstName}}</td>
                    <td>{{node.lastName}}</td>
                    <td>{{node.email}}</td>
                    <td>{{node.userType}}</td>
                    <td>{{node.authorizationGroup.query.totalCount}}</td>
                </tr>
                {{#printBreakIfPropertyWillChange 'node.userType' 'node.authorizationGroup.query.totalCount' 'sum'}}
                    <tr>
                        <td colspan="4"><strong>Total number of groups for user type:</strong></td>
                        <td>{{blockAggregatedData.node.authorizationGroup.query.totalCount.sum}}</td>
                    </tr>
                {{/printBreakIfPropertyWillChange}}
                {{#if @last}}
                    <tr>
                        <td colspan="5" class="user-type"></td>
                    </tr>
                    <tr>
                        <td colspan="4"><strong>Total number of auth groups:</strong></td>
                        <td>{{aggregatedData.node.authorizationGroup.query.totalCount.sum}}</td>
                    </tr>
                {{/if}}
            {{/each}}
        </tbody>
    </table>
</div>