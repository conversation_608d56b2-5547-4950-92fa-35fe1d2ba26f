table {
    font-size: 12px;
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 16px;
  }
  
  table tbody tr {
    border-bottom: 1px solid var(--tableSeparator);
  }
  
  table tbody tr:nth-child(even) {
    background: #F2F5F6;
  }
  
  .user-type{
    padding-top:1cm;
    padding-bottom: 1cm;
    text-align: center
  }
  
  table td.header {
    border-bottom: 2px solid var(--themePrimary);
    border-right: 1px solid var(--tableSeparator);
    border-top:0;
    padding: 4px;
    font-size: 12px;
    font-weight: bold;
  }
  
  table td.header:last-child {
      border-right: none;
  }
  
  table td.header:first-child {
      padding-left: 0
  }
  
  table td {
    font-size: 12px;
    vertical-align: top;
    border-bottom: 1px solid var(--tableSeparator);
    border-right: 1px solid var(--tableSeparator);
    padding: 4px;
  }
  
  
  table td:last-child{
      border-right: none;
      padding-right: 0;
  }
  table td:first-child{
      padding-left: 0;
  }
  
  table .column-left {
    text-align: left;
  }
  
  table .column-right{
    text-align: right;
  }
  
  .header-table{
    width: 100%;
  }
  
  .header-table td{
    vertical-align: top;
  }
  
  h1 {
      color: var(--themePrimary);
  }
  
  h2 {
    color: var(--slate90);
  }
  
  hr.sep {
    border-top: 1px dotted var(--themePrimary);
  }
  
 