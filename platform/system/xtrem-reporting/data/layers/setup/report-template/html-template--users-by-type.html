<div class="entry">
    <h1>Users By Type</h1>  
       
    <hr class="sep"> 
    <h2>Application Users</h2>
    {{#if codeBlockResult.applicationUsersCount}}
        <table>
            <thead>
                <th>First Name</th>
                <th>Last Name</th>
                <th>Email</th>
            </thead>
            <tbody>
                {{#each codeBlockResult.applicationUsers}}
                    <tr>
                        <td>{{node.firstName}}</td>
                        <td>{{node.lastName}}</td>
                        <td>{{node.email}}</td>
                    </tr>
                {{/each}}
            </tbody>
        </table>
        Application user count: {{length codeBlockResult.applicationUsers}}<br />
    {{else}}
        No application users found<br />
    {{/if}}
    <br />

    {{#if codeBlockResult.systemUsersIncluded}}
        <hr class="sep">
        <h2>System Users</h2>
        {{#if codeBlockResult.systemUsersCount}}
            <table>
                <thead>
                    <th>First Name</th>
                    <th>Last Name</th>
                    <th>Email</th>
                </thead>
                <tbody>
                    {{#each codeBlockResult.systemUsers}}
                    <tr>
                        <td>{{node.firstName}}</td>
                        <td>{{node.lastName}}</td>
                        <td>{{node.email}}</td>
                    </tr>
                    {{/each}}
                </tbody>
            </table>
            System user count: {{length codeBlockResult.systemUsers}}<br />
        {{else}}
            No system users found<br />
        {{/if}}
        <br />
    {{/if}}
    
</div>