const applicationUsers = queryResponse.qApp.user.query.edges.filter(edge => edge.node.userType === 'application');
const applicationUsersCount = applicationUsers.length;

const systemUsersIncluded = !!queryResponse.qSys;
const systemUsers = systemUsersIncluded ? queryResponse.qSys.user.query.edges.filter(edge => edge.node.userType === 'system') : [];
const systemUsersCount = systemUsers.length;

return {
    applicationUsersCount,
    applicationUsers,
    systemUsersIncluded,
    systemUsersCount,
    systemUsers,
};
