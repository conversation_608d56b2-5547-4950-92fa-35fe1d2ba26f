"name";"_vendor";"type";"description";"value";"is_factory"
"logo";"sage";"number";"Color for Logo";"#00DC00";"Y"
"background";"sage";"number";"Color for Input, Table, & Card Background";"#FFFFFF";"Y"
"themePrimary";"sage";"number";"Color for Primary Controls";"#008A21";"Y"
"themePrimaryHover";"sage";"number";"Color for Hover over Primary Controls";"#005C9A";"Y"
"gold";"sage";"number";"Color for Focus States";"#FFB500";"Y"
"info";"sage";"number";"Color for Information Messages";"#0077C8";"Y"
"success";"sage";"number";"Color for Success Flashes & Messages";"#00B000";"Y"
"warning";"sage";"number";"Color for Warning Messages";"#E96400";"Y"
"error";"sage";"number";"Color for In-Product Error";"#C7384F";"Y"
"slate";"sage";"number";"Color for miscellaneous borders and backgraounds";"#003349";"Y"
"slate20";"sage";"number";"Color for Icons with focus";"#335C6D";"Y"
"slate40";"sage";"number";"Color for Input Borders & Icons without focus";"#668592";"Y"
"slate60";"sage";"number";"Color for Shadows";"#99ADB6";"Y"
"slate80";"sage";"number";"Color for Text Highlight & Table Row Hover";"#CCD6DB";"Y"
"slate90";"sage";"number";"Color for Disabled Button Background & Separator";"#E5EAEC";"Y"
"slate95";"sage";"number";"Color for App, Dialog, Disabled Input Background, & Hover";"#F2F5F6";"Y"
"black55";"sage";"number";"Color for Disabled Text";"rgba(0, 0, 0, 0.55)";"Y"
"black65";"sage";"number";"Color for Text of Icons without focus";"rgba(0, 0, 0, 0.65)";"Y"
"black90";"sage";"number";"Color for standard Text";"rgba(0, 0, 0, 0.90)";"Y"
"tableSeparator";"sage";"number";"Color for Shadow and Table Separator";"#D9E0E4";"Y"
"textAndLabels";"sage";"number";"Color for Body Text & Field Labels";"rgba(0, 0, 0, 0.85)";"Y"
