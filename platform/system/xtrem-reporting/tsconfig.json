{"extends": "../../tsconfig-base.json", "compilerOptions": {"outDir": "build", "rootDir": ".", "baseUrl": "."}, "include": ["index.ts", "application.ts", "lib/**/*", "lib-generated/**/*", "api/api.d.ts", "test/**/*.ts"], "exclude": ["lib/pages/**/*", "lib/widgets/**/*", "lib/page-extensions/**/*", "lib/page-fragments/**/*", "lib/stickers/**/*", "lib/i18n/**/*", "**/*.feature", "**/*.png", "lib/client-functions/**/*"], "references": [{"path": "../xtrem-authorization"}, {"path": "../xtrem-communication"}, {"path": "../../back-end/xtrem-core"}, {"path": "../../shared/xtrem-date-time"}, {"path": "../../shared/xtrem-decimal"}, {"path": "../../front-end/xtrem-document-editor"}, {"path": "../../shared/xtrem-filter-utils"}, {"path": "../../shared/xtrem-i18n"}, {"path": "../xtrem-infrastructure-adapter"}, {"path": "../../back-end/xtrem-js"}, {"path": "../xtrem-metadata"}, {"path": "../../back-end/xtrem-pdf-generator"}, {"path": "../../shared/xtrem-shared"}, {"path": "../xtrem-system"}, {"path": "../../front-end/xtrem-ui"}, {"path": "../../front-end/xtrem-ui-components"}, {"path": "../../front-end/xtrem-ui-plugin-graphiql"}, {"path": "../../front-end/xtrem-ui-plugin-monaco"}, {"path": "../../front-end/xtrem-ui-plugin-pdf"}, {"path": "../xtrem-upload"}, {"path": "../xtrem-workflow"}, {"path": "../../back-end/eslint-plugin-xtrem"}, {"path": "../../cli/xtrem-cli"}, {"path": "../../front-end/xtrem-client"}, {"path": "api"}, {"path": "../xtrem-routing"}]}