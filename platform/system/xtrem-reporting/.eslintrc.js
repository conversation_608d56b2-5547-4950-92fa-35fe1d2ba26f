module.exports = {
    extends: '../../.eslintrc-base.js',
    plugins: ['@sage/xtrem'],
    parserOptions: {
        tsconfigRootDir: __dirname,
        project: 'tsconfig.json',
        extraFileExtensions: ['.json'],
    },
    overrides: [
        {
            files: ['lib/**/*.ts'],
            rules: {
                '@typescript-eslint/no-floating-promises': 'error',
            },
        },
    ],
};
