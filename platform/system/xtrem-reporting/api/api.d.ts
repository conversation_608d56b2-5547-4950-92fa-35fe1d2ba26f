declare module '@sage/xtrem-reporting-api-partial' {
    import type { Package as SageXtremAuditing$Package } from '@sage/xtrem-auditing-api';
    import type { Package as SageXtremAuthorization$Package } from '@sage/xtrem-authorization-api';
    import type { Package as SageXtremCommunication$Package } from '@sage/xtrem-communication-api';
    import type { Package as SageXtremCustomization$Package } from '@sage/xtrem-customization-api';
    import type { Package as SageXtremDashboard$Package } from '@sage/xtrem-dashboard-api';
    import type {
        MetaDataType,
        MetaNodeFactory,
        MetaNodeOperation,
        Package as SageXtremMetadata$Package,
    } from '@sage/xtrem-metadata-api';
    import type { Package as SageXtremRouting$Package } from '@sage/xtrem-routing-api';
    import type { Package as SageXtremScheduler$Package } from '@sage/xtrem-scheduler-api';
    import type { Package as SageXtremSystem$Package, SysVendor, User } from '@sage/xtrem-system-api';
    import type { Package as SageXtremUpload$Package, UploadedFile } from '@sage/xtrem-upload-api';
    import type { Package as SageXtremWorkflow$Package } from '@sage/xtrem-workflow-api';
    import type {
        AggregateQueryOperation,
        AggregateReadOperation,
        AsyncOperation,
        BinaryStream,
        ClientCollection,
        ClientNode,
        ClientNodeInput,
        CreateOperation,
        DeleteOperation,
        DuplicateOperation,
        GetDefaultsOperation,
        GetDuplicateOperation,
        Operation as Node$Operation,
        QueryOperation,
        ReadOperation,
        TextStream,
        UpdateByIdOperation,
        UpdateOperation,
        VitalClientNode,
        VitalClientNodeInput,
        decimal,
        integer,
    } from '@sage/xtrem-client';
    export interface PrintingType$Enum {
        multiple: 1;
        single: 2;
        notApplicable: 3;
    }
    export type PrintingType = keyof PrintingType$Enum;
    export interface ReportAssignmentType$Enum {
        mainList: 1;
        detailPage: 2;
    }
    export type ReportAssignmentType = keyof ReportAssignmentType$Enum;
    export interface ReportLocale$Enum {
        ar_SA: 1;
        de_DE: 2;
        en_GB: 3;
        en_US: 4;
        es_ES: 5;
        fr_FR: 6;
        it_IT: 7;
        pl_PL: 8;
        pt_BR: 9;
        pt_PT: 10;
        zh_CN: 11;
    }
    export type ReportLocale = keyof ReportLocale$Enum;
    export interface ReportPageOrientation$Enum {
        portrait: 1;
        landscape: 2;
    }
    export type ReportPageOrientation = keyof ReportPageOrientation$Enum;
    export interface ReportPaperFormat$Enum {
        letter: 1;
        legal: 2;
        tabloid: 3;
        ledger: 4;
        a0: 5;
        a1: 6;
        a2: 7;
        a3: 8;
        a4: 9;
        a5: 10;
        a6: 11;
    }
    export type ReportPaperFormat = keyof ReportPaperFormat$Enum;
    export interface ReportProcessingPhase$Enum {
        preProcessing: 0;
        postProcessing: 1;
        postProcessingError: 2;
    }
    export type ReportProcessingPhase = keyof ReportProcessingPhase$Enum;
    export interface ReportResourceType$Enum {
        image: 1;
        fontType: 2;
    }
    export type ReportResourceType = keyof ReportResourceType$Enum;
    export interface ReportStyleVariableType$Enum {
        string: 1;
        number: 2;
        boolean: 3;
    }
    export type ReportStyleVariableType = keyof ReportStyleVariableType$Enum;
    export interface ReportType$Enum {
        printedDocument: 1;
        email: 2;
    }
    export type ReportType = keyof ReportType$Enum;
    export interface TemplateType$Enum {
        list: 0;
        form: 1;
        advanced: 2;
    }
    export type TemplateType = keyof TemplateType$Enum;
    export interface Report extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        name: string;
        description: string;
        parentPackage: string;
        activeTemplate: ReportTemplate;
        reportTemplates: ClientCollection<ReportTemplate>;
        reportType: ReportType;
        isFactory: boolean;
        pageAssignments: ClientCollection<ReportAssignmentAssociation>;
        printingType: PrintingType;
        preProcessingOperation: MetaNodeOperation;
        postProcessingOperation: MetaNodeOperation;
        variables: ClientCollection<ReportVariable>;
    }
    export interface ReportInput extends ClientNodeInput {
        _vendor?: integer | string;
        name?: string;
        description?: string;
        parentPackage?: string;
        activeTemplate?: integer | string;
        reportType?: ReportType;
        isFactory?: boolean | string;
        pageAssignments?: Partial<ReportAssignmentAssociationInput>[];
        printingType?: PrintingType;
        preProcessingOperation?: integer | string;
        postProcessingOperation?: integer | string;
        variables?: Partial<ReportVariableInput>[];
    }
    export interface ReportBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        name: string;
        description: string;
        parentPackage: string;
        activeTemplate: ReportTemplate;
        reportTemplates: ClientCollection<ReportTemplate>;
        reportType: ReportType;
        isFactory: boolean;
        pageAssignments: ClientCollection<ReportAssignmentAssociation>;
        printingType: PrintingType;
        preProcessingOperation: MetaNodeOperation;
        postProcessingOperation: MetaNodeOperation;
        variables: ClientCollection<ReportVariableBinding>;
    }
    export interface Report$Mutations {
        generateReports: Node$Operation<
            {
                reportName: string;
                reportTemplateName: string;
                reportSettings: {
                    reportVariables: string;
                    locale?: string;
                    paperFormat?: string;
                    pageOrientation?: string;
                    leftMarginCm?: decimal | string;
                    rightMarginCm?: decimal | string;
                    topMarginCm?: decimal | string;
                    bottomMarginCm?: decimal | string;
                    documentTitle?: string;
                }[];
            },
            BinaryStream[]
        >;
        createOrUpdateReport: Node$Operation<
            {
                data: {
                    _id?: string;
                    name?: string;
                    templateId?: string;
                    templateName?: string;
                    description?: string;
                    parentPackage?: string;
                    externalHtmlTemplate?: TextStream;
                    dataSource?: integer | string;
                    selectedProperties?: string;
                    parameters?: string;
                    filters?: string;
                    content?: string;
                    variables?: string;
                    reportSettings?: {
                        locale?: string;
                        paperFormat?: string;
                        pageOrientation?: string;
                        leftMarginCm?: decimal | string;
                        rightMarginCm?: decimal | string;
                        topMarginCm?: decimal | string;
                        bottomMarginCm?: decimal | string;
                        isDefaultHeaderFooter?: boolean | string;
                    };
                    printType?: PrintingType;
                };
            },
            {
                reportId: string;
                reportTemplateId: string;
            }
        >;
    }
    export interface Report$AsyncOperations {
        generateReportPdf: AsyncOperation<
            {
                reportName: string;
                reportTemplateName?: string;
                reportSettings?: {
                    variables: string;
                    locale?: string;
                    paperFormat?: string;
                    pageOrientation?: string;
                    leftMarginCm?: decimal | string;
                    rightMarginCm?: decimal | string;
                    topMarginCm?: decimal | string;
                    bottomMarginCm?: decimal | string;
                    documentTitle?: string;
                };
            },
            {
                key: string;
                filename: string;
                mimeType: string;
                contentLength: integer;
                status: string;
                rejectReason: string;
                canSkipAntivirusScan: boolean;
                uploadUrl: string;
                downloadUrl: string;
            }
        >;
        generateUploadedFile: AsyncOperation<
            {
                reportName: string;
                reportTemplateName?: string;
                reportSettings?: {
                    variables: string;
                    locale?: string;
                    paperFormat?: string;
                    pageOrientation?: string;
                    leftMarginCm?: decimal | string;
                    rightMarginCm?: decimal | string;
                    topMarginCm?: decimal | string;
                    bottomMarginCm?: decimal | string;
                    documentTitle?: string;
                };
            },
            UploadedFile
        >;
        generateReportAndNotifyUser: AsyncOperation<
            {
                reportName: string;
                reportTemplateName?: string;
                reportSettings?: {
                    variables: string;
                    locale?: string;
                    paperFormat?: string;
                    pageOrientation?: string;
                    documentTitle?: string;
                };
            },
            boolean
        >;
        generateReportZip: AsyncOperation<
            {
                reportName: string;
                reportTemplateName?: string;
                reportSettings: {
                    variables: string;
                    locale?: string;
                    paperFormat?: string;
                    documentTitle?: string;
                }[];
            },
            {
                key: string;
                filename: string;
                mimeType: string;
                contentLength: integer;
                status: string;
                rejectReason: string;
                canSkipAntivirusScan: boolean;
                uploadUrl: string;
                downloadUrl: string;
            }
        >;
        printRecords: AsyncOperation<
            {
                filter: string;
                nodeName: string;
                reportTemplate: string;
                paperFormat: ReportPaperFormat;
                pageOrientation: ReportPageOrientation;
                joinDocuments: boolean | string;
                reportParameters: string;
            },
            string
        >;
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface Report$Lookups {
        _vendor: QueryOperation<SysVendor>;
        activeTemplate: QueryOperation<ReportTemplate>;
        preProcessingOperation: QueryOperation<MetaNodeOperation>;
        postProcessingOperation: QueryOperation<MetaNodeOperation>;
    }
    export interface Report$Operations {
        query: QueryOperation<Report>;
        read: ReadOperation<Report>;
        aggregate: {
            read: AggregateReadOperation<Report>;
            query: AggregateQueryOperation<Report>;
        };
        create: CreateOperation<ReportInput, Report>;
        getDuplicate: GetDuplicateOperation<Report>;
        update: UpdateOperation<ReportInput, Report>;
        updateById: UpdateByIdOperation<ReportInput, Report>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        mutations: Report$Mutations;
        asyncOperations: Report$AsyncOperations;
        lookups(dataOrId: string | { data: ReportInput }): Report$Lookups;
        getDefaults: GetDefaultsOperation<Report>;
    }
    export interface ReportAssignmentAssociation extends VitalClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        page: ReportAssignmentPage;
        report: Report;
        isActive: boolean;
        isDefault: boolean;
    }
    export interface ReportAssignmentAssociationInput extends VitalClientNodeInput {
        _vendor?: integer | string;
        report?: integer | string;
        isActive?: boolean | string;
        isDefault?: boolean | string;
    }
    export interface ReportAssignmentAssociationBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        page: ReportAssignmentPage;
        report: Report;
        isActive: boolean;
        isDefault: boolean;
    }
    export interface ReportAssignmentAssociation$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface ReportAssignmentAssociation$Lookups {
        _vendor: QueryOperation<SysVendor>;
        report: QueryOperation<Report>;
    }
    export interface ReportAssignmentAssociation$Operations {
        query: QueryOperation<ReportAssignmentAssociation>;
        read: ReadOperation<ReportAssignmentAssociation>;
        aggregate: {
            read: AggregateReadOperation<ReportAssignmentAssociation>;
            query: AggregateQueryOperation<ReportAssignmentAssociation>;
        };
        asyncOperations: ReportAssignmentAssociation$AsyncOperations;
        lookups(dataOrId: string | { data: ReportAssignmentAssociationInput }): ReportAssignmentAssociation$Lookups;
        getDefaults: GetDefaultsOperation<ReportAssignmentAssociation>;
    }
    export interface ReportAssignmentPage extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        screenId: string;
        reportAssignmentType: ReportAssignmentType;
        assignments: ClientCollection<ReportAssignmentAssociation>;
    }
    export interface ReportAssignmentPageInput extends ClientNodeInput {
        _vendor?: integer | string;
        screenId?: string;
        reportAssignmentType?: ReportAssignmentType;
        assignments?: Partial<ReportAssignmentAssociationInput>[];
    }
    export interface ReportAssignmentPageBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        screenId: string;
        reportAssignmentType: ReportAssignmentType;
        assignments: ClientCollection<ReportAssignmentAssociationBinding>;
    }
    export interface ReportAssignmentPage$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface ReportAssignmentPage$Lookups {
        _vendor: QueryOperation<SysVendor>;
    }
    export interface ReportAssignmentPage$Operations {
        query: QueryOperation<ReportAssignmentPage>;
        read: ReadOperation<ReportAssignmentPage>;
        aggregate: {
            read: AggregateReadOperation<ReportAssignmentPage>;
            query: AggregateQueryOperation<ReportAssignmentPage>;
        };
        create: CreateOperation<ReportAssignmentPageInput, ReportAssignmentPage>;
        getDuplicate: GetDuplicateOperation<ReportAssignmentPage>;
        update: UpdateOperation<ReportAssignmentPageInput, ReportAssignmentPage>;
        updateById: UpdateByIdOperation<ReportAssignmentPageInput, ReportAssignmentPage>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: ReportAssignmentPage$AsyncOperations;
        lookups(dataOrId: string | { data: ReportAssignmentPageInput }): ReportAssignmentPage$Lookups;
        getDefaults: GetDefaultsOperation<ReportAssignmentPage>;
    }
    export interface ReportResource extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        name: string;
        description: string;
        type: ReportResourceType;
        mimetype: string;
        content: BinaryStream;
        isFactory: boolean;
    }
    export interface ReportResourceInput extends ClientNodeInput {
        name?: string;
        description?: string;
        type?: ReportResourceType;
        mimetype?: string;
        content?: BinaryStream;
        isFactory?: boolean | string;
    }
    export interface ReportResourceBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        name: string;
        description: string;
        type: ReportResourceType;
        mimetype: string;
        content: BinaryStream;
        isFactory: boolean;
    }
    export interface ReportResource$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface ReportResource$Operations {
        query: QueryOperation<ReportResource>;
        read: ReadOperation<ReportResource>;
        aggregate: {
            read: AggregateReadOperation<ReportResource>;
            query: AggregateQueryOperation<ReportResource>;
        };
        create: CreateOperation<ReportResourceInput, ReportResource>;
        getDuplicate: GetDuplicateOperation<ReportResource>;
        duplicate: DuplicateOperation<string, ReportResourceInput, ReportResource>;
        update: UpdateOperation<ReportResourceInput, ReportResource>;
        updateById: UpdateByIdOperation<ReportResourceInput, ReportResource>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: ReportResource$AsyncOperations;
        getDefaults: GetDefaultsOperation<ReportResource>;
    }
    export interface ReportStyleVariable extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        name: string;
        type: ReportStyleVariableType;
        description: string;
        value: string;
        isFactory: boolean;
    }
    export interface ReportStyleVariableInput extends ClientNodeInput {
        _vendor?: integer | string;
        name?: string;
        type?: ReportStyleVariableType;
        description?: string;
        value?: string;
        isFactory?: boolean | string;
    }
    export interface ReportStyleVariableBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        name: string;
        type: ReportStyleVariableType;
        description: string;
        value: string;
        isFactory: boolean;
    }
    export interface ReportStyleVariable$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface ReportStyleVariable$Lookups {
        _vendor: QueryOperation<SysVendor>;
    }
    export interface ReportStyleVariable$Operations {
        query: QueryOperation<ReportStyleVariable>;
        read: ReadOperation<ReportStyleVariable>;
        aggregate: {
            read: AggregateReadOperation<ReportStyleVariable>;
            query: AggregateQueryOperation<ReportStyleVariable>;
        };
        create: CreateOperation<ReportStyleVariableInput, ReportStyleVariable>;
        getDuplicate: GetDuplicateOperation<ReportStyleVariable>;
        duplicate: DuplicateOperation<string, ReportStyleVariableInput, ReportStyleVariable>;
        update: UpdateOperation<ReportStyleVariableInput, ReportStyleVariable>;
        updateById: UpdateByIdOperation<ReportStyleVariableInput, ReportStyleVariable>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: ReportStyleVariable$AsyncOperations;
        lookups(dataOrId: string | { data: ReportStyleVariableInput }): ReportStyleVariable$Lookups;
        getDefaults: GetDefaultsOperation<ReportStyleVariable>;
    }
    export interface ReportTemplate extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        name: string;
        attachmentTemplate: TextStream;
        attachmentName: string;
        attachmentMimeType: string;
        styleSheet: TextStream;
        query: TextStream;
        code: TextStream;
        report: Report;
        reportWizard: ReportWizard;
        isFactory: boolean;
        isExpertDocument: boolean;
        reportType: ReportType;
        templateType: TemplateType;
        translatableTexts: ClientCollection<ReportTranslatableText>;
        baseLocale: ReportLocale;
        defaultPaperFormat: ReportPaperFormat;
        defaultPageOrientation: ReportPageOrientation;
        defaultLeftMargin: string;
        defaultRightMargin: string;
        defaultTopMargin: string;
        defaultBottomMargin: string;
        isDefaultHeaderFooter: boolean;
        externalHtmlTemplate: TextStream;
        externalHeaderHtmlTemplate: TextStream;
        externalFooterHtmlTemplate: TextStream;
    }
    export interface ReportTemplateInput extends ClientNodeInput {
        _vendor?: integer | string;
        name?: string;
        attachmentTemplate?: TextStream;
        attachmentName?: string;
        attachmentMimeType?: string;
        styleSheet?: TextStream;
        query?: TextStream;
        code?: TextStream;
        report?: integer | string;
        reportWizard?: ReportWizardInput;
        isFactory?: boolean | string;
        isExpertDocument?: boolean | string;
        translatableTexts?: Partial<ReportTranslatableTextInput>[];
        baseLocale?: ReportLocale;
        defaultPaperFormat?: ReportPaperFormat;
        defaultPageOrientation?: ReportPageOrientation;
        defaultLeftMargin?: decimal | string;
        defaultRightMargin?: decimal | string;
        defaultTopMargin?: decimal | string;
        defaultBottomMargin?: decimal | string;
        isDefaultHeaderFooter?: boolean | string;
        externalHtmlTemplate?: TextStream;
        externalHeaderHtmlTemplate?: TextStream;
        externalFooterHtmlTemplate?: TextStream;
    }
    export interface ReportTemplateBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        name: string;
        attachmentTemplate: TextStream;
        attachmentName: string;
        attachmentMimeType: string;
        styleSheet: TextStream;
        query: TextStream;
        code: TextStream;
        report: Report;
        reportWizard: ReportWizardBinding;
        isFactory: boolean;
        isExpertDocument: boolean;
        reportType: ReportType;
        templateType: TemplateType;
        translatableTexts: ClientCollection<ReportTranslatableTextBinding>;
        baseLocale: ReportLocale;
        defaultPaperFormat: ReportPaperFormat;
        defaultPageOrientation: ReportPageOrientation;
        defaultLeftMargin: string;
        defaultRightMargin: string;
        defaultTopMargin: string;
        defaultBottomMargin: string;
        isDefaultHeaderFooter: boolean;
        externalHtmlTemplate: TextStream;
        externalHeaderHtmlTemplate: TextStream;
        externalFooterHtmlTemplate: TextStream;
    }
    export interface ReportTemplate$Queries {
        getSchema: Node$Operation<{}, string>;
        exportTranslations: Node$Operation<
            {
                sourceLocale?: string;
                targetLocale?: string;
                reportTemplates?: string[];
            },
            BinaryStream
        >;
    }
    export interface ReportTemplate$Mutations {
        generateReportSample: Node$Operation<
            {
                reportTemplate: {
                    _id?: string;
                    isExpertDocument?: boolean | string;
                    baseLocale?: string;
                    styleSheet: TextStream;
                    externalHtmlTemplate: TextStream;
                    externalHeaderHtmlTemplate?: TextStream;
                    externalFooterHtmlTemplate?: TextStream;
                    attachmentTemplate?: TextStream;
                    attachmentName?: string;
                    attachmentMimeType?: string;
                    query: TextStream;
                    code: TextStream;
                    translatableTexts?: {
                        hash?: string;
                        locale?: ReportLocale;
                        text?: string;
                    }[];
                };
                reportSettings?: {
                    variables: string;
                    locale?: string;
                    paperFormat?: string;
                    pageOrientation?: string;
                    leftMarginCm?: decimal | string;
                    rightMarginCm?: decimal | string;
                    topMarginCm?: decimal | string;
                    bottomMarginCm?: decimal | string;
                    watermark?: string;
                };
            },
            BinaryStream
        >;
        importTranslations: Node$Operation<
            {
                input: {
                    translations?: BinaryStream;
                };
            },
            boolean[]
        >;
        generateWizardPreview: Node$Operation<
            {
                template: string;
                reportSettings?: {
                    variables: string;
                    locale?: string;
                    paperFormat?: string;
                    pageOrientation?: string;
                    leftMarginCm?: decimal | string;
                    rightMarginCm?: decimal | string;
                    topMarginCm?: decimal | string;
                    bottomMarginCm?: decimal | string;
                    isDefaultHeaderFooter?: boolean | string;
                    reportName?: string;
                };
            },
            BinaryStream
        >;
    }
    export interface ReportTemplate$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface ReportTemplate$Lookups {
        _vendor: QueryOperation<SysVendor>;
        report: QueryOperation<Report>;
    }
    export interface ReportTemplate$Operations {
        query: QueryOperation<ReportTemplate>;
        read: ReadOperation<ReportTemplate>;
        aggregate: {
            read: AggregateReadOperation<ReportTemplate>;
            query: AggregateQueryOperation<ReportTemplate>;
        };
        queries: ReportTemplate$Queries;
        create: CreateOperation<ReportTemplateInput, ReportTemplate>;
        getDuplicate: GetDuplicateOperation<ReportTemplate>;
        duplicate: DuplicateOperation<string, ReportTemplateInput, ReportTemplate>;
        update: UpdateOperation<ReportTemplateInput, ReportTemplate>;
        updateById: UpdateByIdOperation<ReportTemplateInput, ReportTemplate>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        mutations: ReportTemplate$Mutations;
        asyncOperations: ReportTemplate$AsyncOperations;
        lookups(dataOrId: string | { data: ReportTemplateInput }): ReportTemplate$Lookups;
        getDefaults: GetDefaultsOperation<ReportTemplate>;
    }
    export interface ReportTranslatableText extends VitalClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        reportTemplate: ReportTemplate;
        originalText: ReportTranslatableText;
        translations: ClientCollection<ReportTranslatableText>;
        hash: string;
        locale: ReportLocale;
        isBaseLocale: boolean;
        text: string;
    }
    export interface ReportTranslatableTextInput extends VitalClientNodeInput {
        _vendor?: integer | string;
        originalText?: integer | string;
        hash?: string;
        locale?: ReportLocale;
        isBaseLocale?: boolean | string;
        text?: string;
    }
    export interface ReportTranslatableTextBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        reportTemplate: ReportTemplate;
        originalText: ReportTranslatableText;
        translations: ClientCollection<ReportTranslatableText>;
        hash: string;
        locale: ReportLocale;
        isBaseLocale: boolean;
        text: string;
    }
    export interface ReportTranslatableText$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface ReportTranslatableText$Lookups {
        _vendor: QueryOperation<SysVendor>;
        originalText: QueryOperation<ReportTranslatableText>;
    }
    export interface ReportTranslatableText$Operations {
        query: QueryOperation<ReportTranslatableText>;
        read: ReadOperation<ReportTranslatableText>;
        aggregate: {
            read: AggregateReadOperation<ReportTranslatableText>;
            query: AggregateQueryOperation<ReportTranslatableText>;
        };
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: ReportTranslatableText$AsyncOperations;
        lookups(dataOrId: string | { data: ReportTranslatableTextInput }): ReportTranslatableText$Lookups;
        getDefaults: GetDefaultsOperation<ReportTranslatableText>;
    }
    export interface ReportVariable extends VitalClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        name: string;
        title: string;
        isMandatory: boolean;
        type: MetaPropertyType;
        dataType: MetaDataType;
        report: Report;
    }
    export interface ReportVariableInput extends VitalClientNodeInput {
        _vendor?: integer | string;
        name?: string;
        title?: string;
        isMandatory?: boolean | string;
        type?: MetaPropertyType;
        dataType?: integer | string;
    }
    export interface ReportVariableBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        name: string;
        title: string;
        isMandatory: boolean;
        type: MetaPropertyType;
        dataType: MetaDataType;
        report: Report;
    }
    export interface ReportVariable$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface ReportVariable$Lookups {
        _vendor: QueryOperation<SysVendor>;
        dataType: QueryOperation<MetaDataType>;
    }
    export interface ReportVariable$Operations {
        query: QueryOperation<ReportVariable>;
        read: ReadOperation<ReportVariable>;
        aggregate: {
            read: AggregateReadOperation<ReportVariable>;
            query: AggregateQueryOperation<ReportVariable>;
        };
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: ReportVariable$AsyncOperations;
        lookups(dataOrId: string | { data: ReportVariableInput }): ReportVariable$Lookups;
        getDefaults: GetDefaultsOperation<ReportVariable>;
    }
    export interface ReportWizard extends VitalClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        reportTemplate: ReportTemplate;
        id: string;
        name: string;
        description: string;
        dataSource: MetaNodeFactory;
        selectedProperties: string;
        filters: string;
        parameters: string;
        content: string;
        templateType: TemplateType;
    }
    export interface ReportWizardInput extends VitalClientNodeInput {
        _vendor?: integer | string;
        id?: string;
        name?: string;
        description?: string;
        dataSource?: integer | string;
        selectedProperties?: string;
        filters?: string;
        parameters?: string;
        content?: string;
        templateType?: TemplateType;
    }
    export interface ReportWizardBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        reportTemplate: ReportTemplate;
        id: string;
        name: string;
        description: string;
        dataSource: MetaNodeFactory;
        selectedProperties: any;
        filters: any;
        parameters: any;
        content: any;
        templateType: TemplateType;
    }
    export interface ReportWizard$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface ReportWizard$Lookups {
        _vendor: QueryOperation<SysVendor>;
        dataSource: QueryOperation<MetaNodeFactory>;
    }
    export interface ReportWizard$Operations {
        query: QueryOperation<ReportWizard>;
        read: ReadOperation<ReportWizard>;
        aggregate: {
            read: AggregateReadOperation<ReportWizard>;
            query: AggregateQueryOperation<ReportWizard>;
        };
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: ReportWizard$AsyncOperations;
        lookups(dataOrId: string | { data: ReportWizardInput }): ReportWizard$Lookups;
        getDefaults: GetDefaultsOperation<ReportWizard>;
    }
    export interface Package {
        '@sage/xtrem-reporting/Report': Report$Operations;
        '@sage/xtrem-reporting/ReportAssignmentAssociation': ReportAssignmentAssociation$Operations;
        '@sage/xtrem-reporting/ReportAssignmentPage': ReportAssignmentPage$Operations;
        '@sage/xtrem-reporting/ReportResource': ReportResource$Operations;
        '@sage/xtrem-reporting/ReportStyleVariable': ReportStyleVariable$Operations;
        '@sage/xtrem-reporting/ReportTemplate': ReportTemplate$Operations;
        '@sage/xtrem-reporting/ReportTranslatableText': ReportTranslatableText$Operations;
        '@sage/xtrem-reporting/ReportVariable': ReportVariable$Operations;
        '@sage/xtrem-reporting/ReportWizard': ReportWizard$Operations;
    }
    export interface GraphApi
        extends Package,
            SageXtremAuditing$Package,
            SageXtremAuthorization$Package,
            SageXtremCommunication$Package,
            SageXtremCustomization$Package,
            SageXtremDashboard$Package,
            SageXtremMetadata$Package,
            SageXtremRouting$Package,
            SageXtremScheduler$Package,
            SageXtremSystem$Package,
            SageXtremUpload$Package,
            SageXtremWorkflow$Package {}
}
declare module '@sage/xtrem-reporting-api' {
    export type * from '@sage/xtrem-reporting-api-partial';
}
declare module '@sage/xtrem-auditing-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-reporting-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-authorization-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-reporting-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-communication-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-reporting-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-customization-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-reporting-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-dashboard-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-reporting-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-metadata-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-reporting-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-routing-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-reporting-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-scheduler-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-reporting-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-system-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-reporting-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-upload-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-reporting-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-workflow-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-reporting-api';
    export interface GraphApi extends GraphApiExtension {}
}
