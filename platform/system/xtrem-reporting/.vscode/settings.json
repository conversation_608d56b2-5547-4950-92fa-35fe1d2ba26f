{"editor.formatOnSave": true, "files.exclude": {"**/node_modules": true, "**/build": true, "**/out": true, "**/certificatetest": true}, "files.insertFinalNewline": true, "typescript.tsdk": "./node_modules/typescript/lib", "cSpell.words": ["ITEMGLGROUP", "Stockable", "Sublot", "clas", "datetime", "dropdown", "intacct", "options", "reorderable", "sdata", "selectable", "smtp", "xtrem"], "git.ignoreLimitWarning": true, "[typescript]": {"editor.insertSpaces": true}, "[azure-pipelines]": {"editor.tabSize": 2}, "editor.codeActionsOnSave": {"source.organizeImports": "never"}, "cucumberautocomplete.steps": ["./platform/cli/xtrem-cli-atp/lib/extensions/handlers/test/step-definitions/**/*.step.ts", "./platform/cli/xtrem-cli/build/cucumber-steps.js"], "cucumberautocomplete.syncfeatures": "./platform/**/test/cucumber/**/*.feature", "editor.rulers": [120], "editor.tabSize": 4, "editor.detectIndentation": false, "files.associations": {"*.graphql.hbs": "graphql", "*.json.hbs": "plainText", "*/pipelines/.yml": "azure-pipelines"}, "graphql.format.indentHandlebars": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.insertSpaces": true, "files.eol": "\n"}