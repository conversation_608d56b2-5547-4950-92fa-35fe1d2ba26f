{"version": "0.2.0", "configurations": [{"type": "node", "request": "launch", "protocol": "inspector", "name": "Run xtrem-reporting", "program": "${workspaceRoot}/../../cli/xtrem-cli/bin/xtrem", "args": ["start"], "cwd": "${workspaceRoot}", "sourceMaps": true, "stopOnEntry": false}, {"type": "node", "request": "launch", "protocol": "inspector", "name": "Run tests", "program": "${workspaceRoot}/../../cli/xtrem-cli/bin/xtrem", "args": ["test", "--noTimeout", "--unit", "--layers=setup,test"], "sourceMaps": true, "stopOnEntry": false}]}