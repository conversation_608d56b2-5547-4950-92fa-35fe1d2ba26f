{"@sage/xtrem-system": [{"topic": "Company/asyncExport/start", "queue": "import-export", "sourceFileName": "company.ts"}, {"topic": "Locale/asyncExport/start", "queue": "import-export", "sourceFileName": "locale.ts"}, {"topic": "Site/asyncExport/start", "queue": "import-export", "sourceFileName": "site.ts"}, {"topic": "SysChangelog/asyncExport/start", "queue": "import-export", "sourceFileName": "sys-changelog.ts"}, {"topic": "SysClientNotification/asyncExport/start", "queue": "import-export", "sourceFileName": "sys-client-notification.ts"}, {"topic": "SysClientNotificationAction/asyncExport/start", "queue": "import-export", "sourceFileName": "sys-client-notification-action.ts"}, {"topic": "SysClientUserSettings/asyncExport/start", "queue": "import-export", "sourceFileName": "sys-client-user-settings.ts"}, {"topic": "SysCsvChecksum/asyncExport/start", "queue": "import-export", "sourceFileName": "sys-csv-checksum.ts"}, {"topic": "SysCustomer/asyncExport/start", "queue": "import-export", "sourceFileName": "sys-customer.ts"}, {"topic": "SysCustomRecord/asyncExport/start", "queue": "import-export", "sourceFileName": "sys-custom-record.ts"}, {"topic": "SysCustomSqlHistory/asyncExport/start", "queue": "import-export", "sourceFileName": "sys-custom-sql-history.ts"}, {"topic": "SysDataValidationReport/asyncExport/start", "queue": "import-export", "sourceFileName": "sys-data-validation-report.ts"}, {"topic": "SysDataValidationReportLine/asyncExport/start", "queue": "import-export", "sourceFileName": "sys-data-validation-report-line.ts"}, {"topic": "SysDeviceToken/asyncExport/start", "queue": "import-export", "sourceFileName": "sys-device-token.ts"}, {"topic": "SysNote/asyncExport/start", "queue": "import-export", "sourceFileName": "sys-note.ts"}, {"topic": "SysPackAllocation/asyncExport/start", "queue": "import-export", "sourceFileName": "sys-pack-allocation.ts"}, {"topic": "SysPackVersion/asyncExport/start", "queue": "import-export", "sourceFileName": "sys-pack-version.ts"}, {"topic": "SysPatchHistory/asyncExport/start", "queue": "import-export", "sourceFileName": "sys-patch-history.ts"}, {"topic": "SysServiceOption/asyncExport/start", "queue": "import-export", "sourceFileName": "sys-service-option.ts"}, {"topic": "SysServiceOptionState/asyncExport/start", "queue": "import-export", "sourceFileName": "sys-service-option-state.ts"}, {"topic": "SysServiceOptionToServiceOption/asyncExport/start", "queue": "import-export", "sourceFileName": "sys-service-option-to-service-option.ts"}, {"topic": "SysTag/asyncExport/start", "queue": "import-export", "sourceFileName": "sys-tag.ts"}, {"topic": "SysTenant/asyncExport/start", "queue": "import-export", "sourceFileName": "sys-tenant.ts"}, {"topic": "SysUpgrade/asyncExport/start", "queue": "import-export", "sourceFileName": "sys-upgrade.ts"}, {"topic": "SysVendor/asyncExport/start", "queue": "import-export", "sourceFileName": "sys-vendor.ts"}, {"topic": "User/asyncExport/start", "queue": "import-export", "sourceFileName": "user.ts"}, {"topic": "UserNavigation/asyncExport/start", "queue": "import-export", "sourceFileName": "user-navigation.ts"}, {"topic": "UserPreferences/asyncExport/start", "queue": "import-export", "sourceFileName": "user-preferences.ts"}], "@sage/xtrem-communication": [{"topic": "SysDynamicListener/asyncExport/start", "queue": "import-export", "sourceFileName": "sys-dynamic-listener.ts"}, {"topic": "SysMessage/asyncExport/start", "queue": "import-export", "sourceFileName": "sys-message.ts"}, {"topic": "SysMessageHistory/asyncExport/start", "queue": "import-export", "sourceFileName": "sys-message-history.ts"}, {"topic": "SysNotification/asyncExport/start", "queue": "import-export", "sourceFileName": "sys-notification.ts"}, {"topic": "SysNotificationHistory/asyncExport/start", "queue": "import-export", "sourceFileName": "sys-notification-history.ts"}, {"topic": "SysNotificationLogEntry/asyncExport/start", "queue": "import-export", "sourceFileName": "sys-notification-log-entry.ts"}, {"topic": "SysNotificationState/asyncExport/start", "queue": "import-export", "sourceFileName": "sys-notification-state.ts"}, {"topic": "SysNotificationState/bulkDelete/start", "queue": "routing", "sourceFileName": "sys-notification-state.ts"}], "@sage/xtrem-authorization": [{"topic": "Activity/asyncExport/start", "queue": "import-export", "sourceFileName": "activity.ts"}, {"topic": "GroupRole/asyncExport/start", "queue": "import-export", "sourceFileName": "group-role.ts"}, {"topic": "GroupRoleSite/asyncExport/start", "queue": "import-export", "sourceFileName": "group-role-site.ts"}, {"topic": "GroupSite/asyncExport/start", "queue": "import-export", "sourceFileName": "group-site.ts"}, {"topic": "RestrictedNodeUserGrant/asyncExport/start", "queue": "import-export", "sourceFileName": "restricted-node-user-grant.ts"}, {"topic": "Role/asyncExport/start", "queue": "import-export", "sourceFileName": "role.ts"}, {"topic": "RoleActivity/asyncExport/start", "queue": "import-export", "sourceFileName": "role-activity.ts"}, {"topic": "RoleToRole/asyncExport/start", "queue": "import-export", "sourceFileName": "role-to-role.ts"}, {"topic": "SiteGroup/asyncExport/start", "queue": "import-export", "sourceFileName": "site-group.ts"}, {"topic": "SiteGroupToSite/asyncExport/start", "queue": "import-export", "sourceFileName": "site-group-to-site.ts"}, {"topic": "SiteGroupToSiteGroup/asyncExport/start", "queue": "import-export", "sourceFileName": "site-group-to-site-group.ts"}, {"topic": "SupportAccessHistory/asyncExport/start", "queue": "import-export", "sourceFileName": "support-access-history.ts"}, {"topic": "User/sendBulkWelcomeMail/start", "queue": "authorization", "sourceFileName": "user-extension.ts"}, {"topic": "UserBillingRole/asyncExport/start", "queue": "import-export", "sourceFileName": "user-billing-role.ts"}, {"topic": "UserGroup/asyncExport/start", "queue": "import-export", "sourceFileName": "user-group.ts"}], "@sage/xtrem-metadata": [{"topic": "MetaActivity/asyncExport/start", "queue": "import-export", "sourceFileName": "meta-activity.ts"}, {"topic": "MetaActivityPermission/asyncExport/start", "queue": "import-export", "sourceFileName": "meta-activity-permission.ts"}, {"topic": "MetaDataType/asyncExport/start", "queue": "import-export", "sourceFileName": "meta-data-type.ts"}, {"topic": "MetaNodeFactory/asyncExport/start", "queue": "import-export", "sourceFileName": "meta-node-factory.ts"}, {"topic": "MetaNodeOperation/asyncExport/start", "queue": "import-export", "sourceFileName": "meta-node-operation.ts"}, {"topic": "MetaNodeProperty/asyncExport/start", "queue": "import-export", "sourceFileName": "meta-node-property.ts"}, {"topic": "MetaPackage/asyncExport/start", "queue": "import-export", "sourceFileName": "meta-package.ts"}, {"topic": "MetaServiceOption/asyncExport/start", "queue": "import-export", "sourceFileName": "meta-service-option.ts"}], "@sage/xtrem-auditing": [{"topic": "SysAuditLog/asyncExport/start", "queue": "import-export", "sourceFileName": "sys-audit-log.ts"}], "@sage/xtrem-customization": [{"topic": "CustomField/asyncExport/start", "queue": "import-export", "sourceFileName": "custom-field.ts"}], "@sage/xtrem-dashboard": [{"topic": "Dashboard/asyncExport/start", "queue": "import-export", "sourceFileName": "dashboard.ts"}, {"topic": "DashboardItem/asyncExport/start", "queue": "import-export", "sourceFileName": "dashboard-item.ts"}, {"topic": "DashboardItemPosition/asyncExport/start", "queue": "import-export", "sourceFileName": "dashboard-item-position.ts"}, {"topic": "WidgetCategory/asyncExport/start", "queue": "import-export", "sourceFileName": "widget-category.ts"}], "@sage/xtrem-routing": [{"topic": "SysInfrastructure/asyncExport/start", "queue": "import-export", "sourceFileName": "sys-infrastructure.ts"}], "@sage/xtrem-workflow": [{"topic": "Company/created", "queue": "workflow", "sourceFileName": "<workflow step>"}, {"topic": "Company/deleted", "queue": "workflow", "sourceFileName": "<workflow step>"}, {"topic": "Company/updated", "queue": "workflow", "sourceFileName": "<workflow step>"}, {"topic": "Role/created", "queue": "workflow", "sourceFileName": "<workflow step>"}, {"topic": "Role/deleted", "queue": "workflow", "sourceFileName": "<workflow step>"}, {"topic": "Role/updated", "queue": "workflow", "sourceFileName": "<workflow step>"}, {"topic": "Site/created", "queue": "workflow", "sourceFileName": "<workflow step>"}, {"topic": "Site/deleted", "queue": "workflow", "sourceFileName": "<workflow step>"}, {"topic": "Site/updated", "queue": "workflow", "sourceFileName": "<workflow step>"}, {"topic": "SiteGroup/created", "queue": "workflow", "sourceFileName": "<workflow step>"}, {"topic": "SiteGroup/deleted", "queue": "workflow", "sourceFileName": "<workflow step>"}, {"topic": "SiteGroup/updated", "queue": "workflow", "sourceFileName": "<workflow step>"}, {"topic": "WorkflowDefinition/asyncExport/start", "queue": "import-export", "sourceFileName": "workflow-definition.ts"}, {"topic": "WorkflowDefinition/created", "queue": "workflow", "sourceFileName": "<workflow step>"}, {"topic": "WorkflowDefinition/deleted", "queue": "workflow", "sourceFileName": "<workflow step>"}, {"topic": "WorkflowDefinition/updated", "queue": "workflow", "sourceFileName": "<workflow step>"}, {"topic": "WorkflowDiagram/asyncExport/start", "queue": "import-export", "sourceFileName": "workflow-diagram.ts"}, {"topic": "WorkflowProcess/asyncExport/start", "queue": "import-export", "sourceFileName": "workflow-process.ts"}, {"topic": "WorkflowProcess/completed", "queue": "workflow", "sourceFileName": "workflow-process.ts"}, {"topic": "WorkflowProcess/placeholder", "queue": "workflow", "sourceFileName": "workflow-process.ts"}, {"topic": "WorkflowProcess/testStarted", "queue": "workflow", "sourceFileName": "<workflow step>"}, {"topic": "WorkflowStepTemplate/asyncExport/start", "queue": "import-export", "sourceFileName": "workflow-step-template.ts"}], "@sage/xtrem-scheduler": [{"topic": "SysChore/asyncExport/start", "queue": "import-export", "sourceFileName": "sys-chore.ts"}, {"topic": "SysChore/purgeContentAddressableTables/start", "queue": "routing", "sourceFileName": "sys-chore.ts"}, {"topic": "SysClientNotification/purgeSysClientNotification/start", "queue": "routing", "sourceFileName": "sys-client-notification-extension.ts"}, {"topic": "SysJobSchedule/asyncExport/start", "queue": "import-export", "sourceFileName": "sys-job-schedule.ts"}, {"topic": "SysJobSchedule/bulkDelete/start", "queue": "routing", "sourceFileName": "sys-job-schedule.ts"}, {"topic": "SysJobSchedule/jobScheduleDelete", "queue": "routing", "sourceFileName": "sys-job-schedule.ts"}, {"topic": "SysJobSchedule/jobScheduleReset", "queue": "routing", "sourceFileName": "sys-job-schedule.ts"}, {"topic": "SysNotificationState/bulkStop/start", "queue": "routing", "sourceFileName": "sys-notification-state-extension.ts"}, {"topic": "SysNotificationState/markAsRead/start", "queue": "routing", "sourceFileName": "sys-notification-state-extension.ts"}, {"topic": "SysNotificationState/purgeHistory/start", "queue": "routing", "sourceFileName": "sys-notification-state-extension.ts"}, {"topic": "SysNotificationState/simulateAsyncMutation/start", "queue": "routing", "sourceFileName": "sys-notification-state-extension.ts"}, {"topic": "SysNotificationState/updateStatus", "queue": "routing", "sourceFileName": "sys-notification-state-extension.ts"}], "@sage/xtrem-upload": [{"topic": "UploadedFile/InfrastructureComplete", "queue": "import-export", "sourceFileName": "uploaded-file.ts"}, {"topic": "UploadedFile/purge/start", "queue": "import-export", "sourceFileName": "uploaded-file.ts"}], "@sage/xtrem-reporting": [{"topic": "Report/asyncExport/start", "queue": "import-export", "sourceFileName": "report.ts"}, {"topic": "Report/generateReportAndNotifyUser/start", "queue": "reporting", "sourceFileName": "report.ts"}, {"topic": "Report/generateReportPdf/start", "queue": "reporting", "sourceFileName": "report.ts"}, {"topic": "Report/generateReportZip/start", "queue": "reporting", "sourceFileName": "report.ts"}, {"topic": "Report/generateUploadedFile/start", "queue": "reporting", "sourceFileName": "report.ts"}, {"topic": "Report/printRecords/start", "queue": "reporting", "sourceFileName": "report.ts"}, {"topic": "ReportAssignmentAssociation/asyncExport/start", "queue": "import-export", "sourceFileName": "report-assignment-association.ts"}, {"topic": "ReportAssignmentPage/asyncExport/start", "queue": "import-export", "sourceFileName": "report-assignment-page.ts"}, {"topic": "ReportResource/asyncExport/start", "queue": "import-export", "sourceFileName": "report-resource.ts"}, {"topic": "ReportStyleVariable/asyncExport/start", "queue": "import-export", "sourceFileName": "report-style-variable.ts"}, {"topic": "ReportTemplate/asyncExport/start", "queue": "import-export", "sourceFileName": "report-template.ts"}, {"topic": "ReportTranslatableText/asyncExport/start", "queue": "import-export", "sourceFileName": "report-translatable-text.ts"}, {"topic": "ReportVariable/asyncExport/start", "queue": "import-export", "sourceFileName": "report-variable.ts"}, {"topic": "ReportWizard/asyncExport/start", "queue": "import-export", "sourceFileName": "report-wizard.ts"}]}