PATH: XTREEM/xtrem-reporting/Implementation+in+applicative+packages

Page contents:

-   [Purchase Order Example](#purchase-order)
-   [Purchase Orders By Status Example](#purchase-order-by-status)
-   [Users By Type Example](#users-by-type)
-   [Active Users Example](#active-users)
-   [Report Definition Example](#report-definition)

To add report generation on a page, create a page action and open the PrintDocument dialog provided by the XTreeM Reporting package. The dialog uses the generateReport mutation to generate a report and then displays it in a PDF viewer.

## Reporting General Workflow

![Flowchart](assets/images/xtreem-reporting-flowchart_rev3.png)

## Purchase Order Example<a name="purchase-order"></a>

In the XTreeM Reporting package, a report named "purchaseOrder" and a template named "purchaseOrderTemplate" were created. The report template contains the HTML layout of a purchase order and a GraphQL query was written to select the data of a purchase order based on the order's number. The template is assigned as the Active Template of the report and a variable named "orderNumber" is defined.

![Purchase Order Report](assets/images/purchase-order-report.png)

In the XTreeM Purchasing package, a page action was added to the Purchase Order page. The action provides a "Print" action for the user to click on after selecting a purchase order. It opens the PrintDocument dialog and passes in the name of the report and the value for the report variable (the number of the current purchase order).

```typescript
@ui.decorators.pageAction<PurchaseOrder>({
        title: 'Print',
        async onClick() {
            this.$.dialog.page(
                '@sage/xtrem-reporting/PrintDocument',
                {
                    reportName: 'purchaseOrder',
                    orderNumber: this.number.value,
                },
                {
                    size: 'extra-large',
                },
            );
        },
    })
    printAction: ui.PageAction;
```

When the user selects a purchase order and clicks "Print" the dialog sends the GraphQL mutation and displays the PDF data from the response in a PDF viewer.

![Purchase Order Screen](assets/images/purchase-order-screen.png)

![Purchase Order PO11 report](assets/images/purchaseorder-po11-report.png)

## Purchase Orders By Status Example<a name="purchase-order-by-status"></a>

In the XTreeM Reporting package, a report named "purchaseOrdersByStatus" and a template named "purchaseOrdersByStatus" were created. The report template contains the HTML layout of a purchase order that was written to separate the list of orders by status. In this example the 'printBreakIfPropertyChanged' custom handlebars helper is used to introduce a page break after each time the status value changes. It should also be noted that the above custom helper can only be used within the context of #each helper.

### Query

```typescript
{
  xtremPurchasing{
    purchaseOrder{
      query(orderBy:"{status:1}",first:500){
        edges{
          node{
            status
            number
            orderDate
            totalAmountExcludingTax
            lines{
              query{
                totalCount
              }
            }
            currency{
              symbol
            }
          }
        }
      }
    }
  }
}

```

### HTML Template with Custom Handlebars helpers

```html
<div id="main">
    {{#each xtremPurchasing.purchaseOrder.query.edges}} {{#printBreakIfPropertyChanged 'node.status'}}
    <h2>{{enumValue '@sage/xtrem-purchasing/PurchaseDocumentStatus' node.status}} orders</h2>
    <table class="header-table">
        <thead>
            <tr>
                <th class="column-left">Number</th>
                <th class="column-left">Order Date</th>
                <th class="column-right">Order Total</th>
                <th class="column-right">Number of Lines</th>
            </tr>
        </thead>
        <tbody>
            {{/printBreakIfPropertyChanged}}
            <tr>
                <td><strong>{{node.number}}</strong></td>
                <td>{{node.orderDate}}</td>
                <td class="column-right">{{node.currency.symbol}} {{formatNumber node.totalAmountExcludingTax 2}}</td>
                <td class="column-right">{{node.lines.query.totalCount}}</td>
            </tr>
            {{#printBreakIfPropertyWillChange 'node.status'}}
        </tbody>
    </table>
    <div class="page-break"></div>
    {{/printBreakIfPropertyWillChange}} {{/each}}
</div>
```

## Users By Type Example<a name="users-by-type"></a>

In XTreeM Reporting, a report named "usersByType" and a template named "usersByType" were created to illustrate an example using both the code field as well as some Handlebars helpers within the HTML template field. The report template contains the HTML layout of lists of users and a GraphQL query and post processing code was written to classify the data based on the user's type. The template is assigned as the Active Template of the report and a variable named "includeSystemUsers" is defined to indicate whether system users should also be returned by the query.

| Variable Name      | Type    | Value |
| ------------------ | ------- | ----- |
| includeSystemUsers | boolean | true  |

### Query with Handlebars expressions

```typescript
{
  xtremSystem {
    user {
      query (filter : "{userType: {_or: ['application' {{#if includeSystemUsers}},'system'{{/if}}]}}") {
        edges {
          node {
            email
            firstName
            lastName
            userType
          }
        }
      }
    }
  }
}

```

### Code used to post-process the query result (after query execution)

```ts
{
const applicationUsers = queryResponse.xtremSystem.user.query.edges.filter(edge => edge.node.userType === 'application');
const applicationUsersCount = applicationUsers.length;

const systemUsers = queryResponse.xtremSystem.user.query.edges.filter(edge => edge.node.userType === 'system');
const systemUsersCount = systemUsers?.length;

return {
    applicationUsersCount,
    applicationUsers,
    systemUsersCount,
    systemUsers,
};
```

### HTML Template with Handlebars helpers

'codeBlockResult' is the name of the variable that holds the results returned by the code block along with the use of some Handlebars helpers to process and organize the data for display on the report

```html
<div class="entry">
    <h1>Users By Type</h1>
    <hr class="sep" />

    {{#if codeBlockResult.applicationUsersCount}}
    <h2>Application Users</h2>
    <table>
        <thead>
            <th>First Name</th>
            <th>Last Name</th>
            <th>Email</th>
        </thead>
        <tbody>
            {{#each codeBlockResult.applicationUsers}}
            <tr>
                <td>{{node.firstName}}</td>
                <td>{{node.lastName}}</td>
                <td>{{node.email}}</td>
            </tr>
            {{/each}}
        </tbody>
    </table>
    Application user count: {{length codeBlockResult.applicationUsers}}<br />
    <br />
    {{/if}} {{#if codeBlockResult.systemUsersCount}}
    <hr class="sep" />
    <h2>System Users</h2>
    <table>
        <thead>
            <th>First Name</th>
            <th>Last Name</th>
            <th>Email</th>
        </thead>
        <tbody>
            {{#each codeBlockResult.systemUsers}}
            <tr>
                <td>{{node.firstName}}</td>
                <td>{{node.lastName}}</td>
                <td>{{node.email}}</td>
            </tr>
            {{/each}}
        </tbody>
    </table>
    System user count: {{length codeBlockResult.systemUsers}}<br />
    <br />
    {{/if}}
</div>
```

## Active Users Example<a name="active-users"></a>

In XTreeM Reporting, a report named "activeUsersListing" and a template named "activeUsers" were created to illustrate an example using the code field that contains a method used to capitalize a user's first and last name. The template is assigned as the Active Template of the report and the query does not define a variable.

### Query with Handlebars expressions

```typescript
{
  xtremSystem {
    user {
      query {
        edges {
          node {
            _id
            email
            firstName
            lastName
            isActive
            displayName
            userType
          }
        }
      }
    }
  }
}
```

### Code used to post-process the query result (after query execution)

```ts
function capitalize(word) {
    return word?.replace(/^(.)/, c => c.toUpperCase());
}

const activeUsers = queryResponse.xtremSystem.user.query.edges.filter(edge => edge.node.isActive);

const capitalizedUsers = activeUsers.map(edge => {
    const pieces = edge.node.email.split('@');
    const segments = pieces[0].split('.');
    const firstName = capitalize(segments[0]);
    const lastName = capitalize(segments[1]);
    edge.node.email = `${firstName}${lastName ? '.' + lastName : ''}@${pieces[1]}`;

    return edge;
});

return capitalizedUsers;
```

### HTML Template with #each Handlebars helper

'codeBlockResult' is the name of the variable that holds the results returned by the code block along with the use of #each Handlebars helper to process and organize the data for display on the report

```html
<div class="entry">
    <h1>Active Users</h1>
    <table>
        <thead>
            <th>First Name</th>
            <th>Last Name</th>
            <th>Email</th>
        </thead>
        <tbody>
            {{#each codeBlockResult}}
            <tr>
                <td>{{node.firstName}}</td>
                <td>{{node.lastName}}</td>
                <td>{{node.email}}</td>
            </tr>
            {{/each}}
        </tbody>
    </table>
</div>
```

#### Setting aggregators in the #each helper

Aside from the element to iterate from, rear arguments can be set in the #each helper to compute derived aggregation data. This can be useful for quick reporting without leaving the HTML Template context.

Aggregators are requested by applying any built-in available operation against some **numeric** property of the owner's collection nodes, and accessed within the each loop scope through the `aggregatedData` variable.

| Operations |
| ---------- |
| average    |
| count      |
| max        |
| min        |
| sum        |

```html
<div class="entry">
    <h1>All Employees</h1>
    <table>
        <thead>
            <th>First Name</th>
            <th>Last Name</th>
            <th>Email</th>
            <th>Age</th>
        </thead>
        <tbody>
            {{#each xtremPackage.employeesNode.query.edges 'node.age' 'average'}}
            <tr>
                <td>{{node.firstName}}</td>
                <td>{{node.lastName}}</td>
                <td>{{node.email}}</td>
                <td>{{node.age}}</td>
            </tr>
            {{#if @last}}
            <tr>
                <td colspan="3">Average age:</td>
                <td>{{aggregatedData.node.age.average}}</td>
            </tr>
            {{/if}} {{/each}}
        </tbody>
    </table>
</div>
```

Notice how we pass arguments:

-   wrapped in single quotes,
-   after the owner element,
-   in 2-size chunks (for instance, 'node.age' 'average')

The results are taken back at `aggregatedData.{property_path}.{calculation}`

#### Using aggregators in printBreakIfPropertyWillChange helper within an each block

Unlike with `each` in case of this helper functions the aggregated groups are reset whenever the block is printed. The aggregated values are available via the `blockAggregatedData` variable using the same structure as `aggregatedData`.

## Report Definition Example<a name="report-definition"></a>

In XTreeM Reporting, a report named "reportDefinitions" and a template named "reportDefinitionTemplate" were created to illustrate an example using 2 named queries and the code field that combines the results to be used with the html template. The template is assigned as the Active Template of the report and the queries do not define any variable.

### Queries

```typescript
{
  query1: xtremReporting {
    report {
      query {
        edges {
          node {
            _id
            name
            parentPackage
            activeTemplate {
              _id
            }
            isFactory
            description
            variables {
              query {
                edges {
                  node {
                    _id
                    name
                    type
                  }
                }
              }
            }
          }
        }
      }
    }
  }
  query2: xtremReporting {
    reportTemplate {
      query {
        edges {
          node {
            _id
            name
            isFactory
            query {
              value
            }
            code {
              value
            }
            htmlTemplate {
              value
            }
            styleSheet {
              value
            }
          }
        }
      }
    }
  }
}
```

### Code used to combine the query results (after query execution)

```ts
queryResponse.query1.report.query.edges.forEach(report => {
    const templateId = report.node.activeTemplate?._id;
    if (templateId) {
        const template = queryResponse.query2.reportTemplate.query.edges.find(edge => edge.node._id === templateId);
        if (template) {
            report.node.activeTemplate = template.node;
        }
    }
});

return queryResponse.query1;
```

### HTML Template with #each Handlebars helper

'codeBlockResult' is the name of the variable that holds the results returned by the code block along with the use of #each Handlebars helper to process and organize the data for display on the report

```html
<div id="main">
    <h1>Report Definitions</h1>
    <hr class="sep">

    {{#each codeBlockResult.report.query.edges}}
        <p>
            <h2>Report: {{node.name}}</h2>
            {{node.description}}
        </p>
        Active Template: {{node.activeTemplate.name}}<br />
        <hr>
        Query:<br />{{node.activeTemplate.query.value}}<br /><br />
        Code:<br />{{node.activeTemplate.code.value}}<br /><br />
        HTML:<br />{{node.activeTemplate.htmlTemplate.value}}<br /><br />
        Style Sheet:<br />{{node.activeTemplate.styleSheet.value}}<br /><br />
        <hr class="sep">
    {{/each}}
</div>
```

### HTML Template with formatting functions

Functions can be used inside the Handlebars template to process your data.

#### formatNumber

In the example below 'formatNumber' is the name of the function that given a number and a scale (max and min number of decimal places) returns the number formatted according to current locale.

| parameter | type   | description                                                                      |
| --------- | ------ | -------------------------------------------------------------------------------- |
| `value`   | number | A Javascript native Date or a String in iso format ('YYYY-MM-DD')                |
| `scale`   | Object | Variable passed to template which indicates max and min number of decimal places |

```typescript
const scale = {
   minimumFractionDigits: 0,
   maximumFractionDigits: 0,
}.
```

```html
<div id="main">
    <h1>Report Definitions</h1>
    <hr class="sep">

    {{#each codeBlockResult.report.query.edges}}
        <p>
            <h2>Report: {{node.name}}</h2>
            {{node.description}}
        </p>
        Active Template: {{node.activeTemplate.name}}<br />
        <hr>
        Formatted number:<br />{{formatNumber node.value scale}}<br /><br />
        <hr class="sep">
    {{/each}}
</div>
```

#### formatDate

In the example below 'formatDate' is the name of the function that given a date in ISO format and a format
returns the date formatted according to given format and current locale.

#### Parameters

| parameter | type           | description                                                                                                            |
| --------- | -------------- | ---------------------------------------------------------------------------------------------------------------------- |
| `date`    | Date or String | A Javascript native Date or a String in iso format ('YYYY-MM-DD')                                                      |
| `format`  | string         | Optional. Defaults to FullDate. Available values: 'FullDate', 'LongMonth', 'LongMonthYear', 'MonthDay' and MonthYear'. |

```html
<div id="main">
    <h1>Report Definitions</h1>
    <hr class="sep">

    {{#each codeBlockResult.report.query.edges}}
        <p>
            <h2>Report: {{node.name}}</h2>
            {{node.description}}
        </p>
        Active Template: {{node.activeTemplate.name}}<br />
        <hr>
        Formatted Date:<br />{{formatDate node.value format}}<br /><br />
        <hr class="sep">
    {{/each}}
</div>
```
