PATH: XTREEM/Development+documentation/xtrem-reporting

The reporting engine for XTreeM API is implemented by the xtrem-reporting package, which is part of the platform project.

## Reporting Components

### Report Object

-   **Name:**
    -   A String representing a Report that can be generated by any Package within 'xtrem-services'.
-   **Description:**
    -   A String representing the purpose and aim of the report.
-   **Parent Package:**
    -   A list of available Packages that the report can inherit from.
-   **Active Template:**
    -   A String representing the name of a Report Template that is associated with this Report.
-   **isFactory:**
    -   A boolean value indicating whether or not a Report can be modified.
-   **ReportVariables:**
    -   A list of Variable Names and Types that will be used to customize the Query that the Report Template uses to retrieve the data to be used for the Report.

### Report Template Object

-   **Name:**
    -   A String representing a Report Template that can be used by a report.
        The name of the template must adhere to the camelCase Capitalization Style:
        Camel Casing requires the first letter of an identifier to be in lowercase and the first letter of each subsequent concatenated word to be capitalized. For example: mySampleTemplate or salesTemplate
-   **Report:**
    -   A String representing a Report Name that this Template is to be associated with.
-   **isFactory:**
    -   A boolean value indicating whether or not a Report Template can be modified.
-   **Data Query:**
    -   A String representing a GraphQL query that is used to retrieve the data that is to be combined with the Report's HTML Template.
    -   The query can contain Handlebars style variables which are injected into the Query String at runtime before the query is executed. For example the invoice number in case of an invoice.
-   **Code:**
    -   A String representing javascript code that is executed after the query to post-process the resulting data structure returned by the API. The query result is passed as a parameter to an anonymous method that can be accessed through the 'queryResponse' variable. The code field needs to return an object or a literal that is then injected into the query result structure which is accessible by handlebars through the 'codeBlockResult' variable on the HTML Template tab.
-   **Report template:**
    -   A static HTML string with Handlebars support for templating. Handlebars variables are populated by the data returned from the report template's data query execution. The template supports handlebars functions such as loops.
    -   > An online reference editor and processor to handlebars can be used to learn how the templating support is supposed to work, here is a link to an online handlebars test runner: [`Try Handlebars`](http://tryhandlebarsjs.com/)
        > A generic list of helper methods provided by the following project (with basic intellisense support) has also been integrated into the report template processor, the list of what is included is referenced here: [`Handlebars Helpers`](https://github.com/helpers/handlebars-helpers)
-   **Style sheets:**
    -   A String representing a Standard CSS stylesheet that can be defined by the user to customize the look of their reports. The style is applied to the the final report's generated HTML before it is turned into a PDF and made available for viewing or Download by the user.

## Report Generation Workflow

![Process Flow](assets/images/xtreem-reporting-process-flow_rev3.png)

### Steps

1. **Report Creation**: added through the 'Report' Page of xtrem services (similar to the xtrem-mailer). This page consists of the following key components:

    - Name, Parent Package, and Description fields: Enables the user to use the report from other Pages.
    - isFactory field: read-only check to denote base reports provided by the API that will not be editable.
    - Variables fields: Enables the user to pass filtering parameter values to the report template for the purposes of query execution on XTreeM API to provide data for the report.

    - ![Step 1 Report Page Screen Capture](assets/images/step1-reportpage.png)

2. **Report Template Creation**: added through the 'Report Template' Page of xtrem services (similar to the xtrem-mailer's template editor). This page consists of the following key components:

    - Name and Report fields: Enables the user to define and associate the template with a report.

    - ![Step 2.1 Report Template Basic Page Screen Capture](assets/images/step21-reporttemplate.png)

    - A GraphQL 'Data Query' editor field: Based on GraphiQL editor, enables the user to provide a query which after variable replacement and execution provides the data for the report's template.

    - ![Step 2.2 Data Query Screen Capture](assets/images/step22-templatequery.png)

    - An HTML editor field with Handlebars support: Based on the Monaco editor, enables the user to create an HTML template with basic intellisense support.

        > An online reference editor and processor to handlebars can be used to learn how the templating support is supposed to work, here is a link to an online handlebars test runner: [`Try Handlebars`](http://tryhandlebarsjs.com/)

    - ![Step 2.3 HTML Template Screen Capture](assets/images/step23-templatehtml.png)

    - A CSS editor field: Based on the Monaco editor, enables the user to edit CSS file for the template

    - ![Step 2.4 Style Sheet Screen Capture](assets/images/step24-templatestyle.png)

    - A test report section (preview): This should contain a table with variables and the file field which can be used to obtain the sample report.

    - ![Step 2.5 Preview Screen Capture](assets/images/step25-templatepreview.png)

3. **Setting the report template as the Active Template on the Report**

    - After the above objects are created, the next step involves establishment of an association between the Report and a report template by setting the Report object's (step 1) 'Active Template' field to the template that was made for it.

    - ![Step 3 Report page Screen Capture](assets/images/step3-reportpage.png)

## Report generation on the server to HTML

The report generation mechanism can be triggered at multiple levels:

-   by a Page Action from another XtreeM Page
-   by importing the appropriate exported functions from the 'xtrem-reporting' package
    -   The report's GraphQL query is executed with the user supplied variable values. Having the query result ready, the template will be populated using the handlebars library. The HTML document is also populated with additional style sheets and returned as a PDF document. See [Implementation in applicative packages](application-implementation.md) for more information.
-   The generated HTML document is converted to PDF using Pupeteer (and a chromimum instance) and made available for the user to view or download.
