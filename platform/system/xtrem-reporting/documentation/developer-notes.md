PATH: XTREEM/xtrem-reporting/Developer+notes

Page contents:

- [Application of Report Variables](#report-variables)
- [Custom mutations that generate reports](#custom-mutations)
- [Autocomplete suggestions (IntelliSense) for Handlebars expressions in the HTML Template field](#html-handlebars)
- [Pre/Post processing operations](#pre-post-operations)

---

## Application of Report Variables<a name="report-variables"></a>

Here is an example query containing Handlebars expressions based on the report variables. The sequence of code snippets demonstrates how the values are applied to the query.

### Report variables table

| Variable Name | Type    | Value |
| ------------- | ------- | ----- |
| companyId     | string  | US001 |
| \_id          | number  | 1     |
| enabled       | boolean | true  |

### Query with Handlebars expressions, as entered by the developer in the report template

> The embedded GraphiQL tool can be used in order to execute and see the results of a query that is going to be passed into the template
> Also, the query can be tested against the API using any existing API client application such as [`Postman`](https://www.postman.com/product/api-client/)

```typescript
{
  xtremSystem {
    company {
      query(filter: "{_id:'{{_id}}', id:'{{companyId}}', isActive: {{enabled}} }") {
        edges {
          node {
            id
            isActive
            sites {
              query {
                edges {
                  node {
                    id
                    legalCompany {
                      id
                    }}}}}}}}}}}
```

### Query after Handlebars compilation (during processing)

```typescript
{
  xtremSystem {
    company {
      query(filter: "{_id:'1', id: 'US001' , isActive: true }") {
        edges {
          node {
            id
            isActive
            sites {
              query {
                edges {
                  node {
                    id
                    legalCompany {
                      id
                    }}}}}}}}}}}
```

### Result of query execution

```typescript
{
    "data": {
        "xtremSystem": {
            "company": {
                "query": {
                    "edges": [
                        {
                            "node": {
                                "id": "US001",
                                "isActive": true,
                                "sites": {
                                    "query": {
                                        "edges": [
                                            {
                                                "node": {
                                                    "id": "US001",
                                                    "legalCompany": {
                                                        "id": "US001"
                                                    }
                                                }
                                            },
                                            {
                                                "node": {
                                                    "id": "US002",
                                                    "legalCompany": {
                                                        "id": "US001"
                                                    }
                                                }
                                            }
                                        ]
                                    }
                                }
                            }
                        }
                    ]
                }
            }
        }
    },
    "extensions": {
        "restrictedFilters": [],
        "diagnoses": []
    }
}
```

---

## Custom mutations that generate reports<a name="custom-mutations"></a>

The Report node provides three mutations to generate reports. Each requires information about the report or report template along with report variables, and responds with a string containing PDF data. The PDF data is base64-encoded and can be sent to a previewer for display in the UI.

Report variables are provided as JSON strings containing the names and values of variables to be populated into the GraphQL query and HTML template of a report template.

### generateReport

To generate a single report, provide the name of a report object and the report variables string.

The Active Template of the report determines the GraphQL query, HTML template, and CSS style sheet used in the process.

**Mutation:**

```typescript
mutation {
  xtremReporting {
    report {
      generateReport(
        reportName: "companyDetails",
        reportVariables: "{\"id\":\"FR20\"}"
      )
    }
  }
}
```

**Response:**

The value of generateReport is a the PDF data as a binary string.

```typescript
{
  "data": {
    "xtremReporting": {
      "report": {
        "generateReport": "JVBERi0xLjQKJdPr6eEKMSAwIG9iago8PC9DcmVhdG9 ... 2JPkr9HIohvw52nsfQTYVS/4EAe5hzG=="
      }
    }
  },
  "extensions": {
    "diagnoses": []
  }
}
```

### generateReportZip

To generate multiple reports, provide this mutation with the report name and an array of JSON strings. Each element of the array needs to include the report variables for a single document.

**Mutation:**

```typescript
mutation {
  xtremReporting {
    report {
      generateReports(
        reportName: "companyDetails",
        reportVariables: ["{\"id\":\"US001\"}","{\"id\":\"FR20\"}","{\"id\":\"GB20\"}"]
      )
    }
  }
}
```

**Response:**

The value of generateReport is a the PDF data as a binary string.

```typescript
{
  "data": {
    "xtremReporting": {
      "report": {
        "generateReportZip": [
          "JVBERi0xLjQKJdPr6eEKMSAwIG9iago8PC9DcmVhdG9yIChDaHJvbW ... +CnN0YXJ0eHJlZgoxNTYwOQolJUVPRg==",
          "JVBERi0xLjQKJdPr6eEKMSAwIG9iago8PC9DcmVhdG9yIChDaHJvbW ... 2JPkr9HIohvw52nsfQTYVS/4EAe5hzG==",
          "JVBERi0xLjQKJdPr6eEKMSAwIG9iago8PC9DcmVhdG9yIChDaHJvbW ... oZ7+cSnLhziNGk9Ll8PdUZ9q7KthRaH==",
        ]
      }
    }
  },
  "extensions": {
    "diagnoses": []
  }
}
```

---

## Autocomplete suggestions (IntelliSense) for Handlebars expressions in the HTML Template field<a name="html-handlebars"></a>

The HTML Template value in a Report Template will most likely contain Handlebars expressions based on the node properties used in the query; to filter the records returned by the query, for example. To assist with this we populate IntelliSense with a list of suggested completions whenever it is triggered by the user.

Suggestions are triggered by typing either the opening double braces of a Handlebars expression ( **{{** ) , a dot ( **.** ), or Ctrl-Space.

### Basic properties from the query

The **extractSuggestionsFromGraphQLQuery()** function takes a GraphQL query as a string argument and returns a nested array of EditorSuggestion-type objects. The function parses the query string into a JSON object and then converts each property of the object into a suggestion object.

The array can then set as the value for the handlebarsSuggestions decorator of a Monaco plugin field in a page.

**Query, as entered by the developer:**

```typescript
{
  xtremSystem {
    company {
      query(filter: "{id:'{{id}}',id:'{{companyId}}',isActive:{{enabled}} }") {
        edges {
          node {
            id
            sites {
              query {
                edges {
                  node {
                    id
                    legalCompany {
                      id
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}

```

**Suggestion array:**

```typescript
[
    {
        label: 'company',
        insertText: 'company',
        children: [
            {
                label: 'query',
                insertText: 'query',
                children: [
                    {
                        label: 'edges',
                        insertText: 'edges',
                        children: [
                            {
                                label: 'node',
                                insertText: 'node',
                                children: [
                                    {
                                        label: 'id',
                                        insertText: 'id',
                                        children: [],
                                    },
                                    {
                                        label: 'sites',
                                        insertText: 'sites',
                                        children: [
                                            {
                                                label: 'query',
                                                insertText: 'query',
                                                children: [
                                                    {
                                                        label: 'edges',
                                                        insertText: 'edges',
                                                        children: [
                                                            {
                                                                label: 'node',
                                                                insertText: 'node',
                                                                children: [
                                                                    {
                                                                        label: 'id',
                                                                        insertText: 'id',
                                                                        children: [],
                                                                    },
                                                                    {
                                                                        label: 'legalCompany',
                                                                        insertText: 'legalCompany',
                                                                        children: [
                                                                            {
                                                                                label: 'id',
                                                                                insertText: 'id',
                                                                                children: [],
                                                                            },
                                                                        ],
                                                                    },
                                                                ],
                                                            },
                                                        ],
                                                    },
                                                ],
                                            },
                                        ],
                                    },
                                ],
                            },
                        ],
                    },
                ],
            },
        ],
    },
];
```

**IntelliSense suggestions, as seen by the user:**
![IntelliSense Handlebars suggestions](assets/images/handlebars-suggestions.jpg)

---

## Implement an application Pre/Post-processing operations on a report<a name="pre-post-operations"></a>

**This needs the service option reportAssignment to be enabled**

All the tasks linked to the printing of a report must be triggered no matter how it was requested (ui action, graphql query, workflow automation, ...).
To enable applications to prepare printing or save status once printed the print engine supports the assignment of a pre-processing-operation and/or a post-processing-operation reference on the report to check if a print is allowed or not, to set standard parameters (only watermark for now) and update the printed record.
To be compatible with the print engine these operations must be published async mutations both available to the user and activated if associated to a service option. Their parameters must be defined as variables in the report with th same name AND type.

### Define an operation

Assuming you want to print a report's info only if it is a single print report, the report would need a variable of type reference and Report for data-type.
This report needs a pre-processing operation to allow or not the printing of the report and inform the user why a print is refused by the application's rules.

```typescript
@decorators.asyncMutation<typeof MyNode, 'onlySinglePrintCanBePrinted'>({
    isPublished: true,
    parameters: [
        {
            name: 'report',
            type: 'reference',
            node: () => xtremReporting.nodes.Report,
        },
    ],
    return: 'boolean',
})
static async onlySinglePrintCanBePrinted(context: Context, report: xtremReporting.nodes.Report) {
    if(await report.printingType === 'multiple') {
        throw new BusinessRuleError('You cannot print details about a multiple prints report using this report');
    }

    return true;
}
```

Additionally you can set the standard printing settings from the pre-processing operation.
Currently the engine allow these settings:

- watermark

You'll have to ensure setting the proper return type.

```typescript
@decorators.asyncMutation<typeof MyNode, 'applyWatermark'>({
    isPublished: true,
    parameters: [],
    return: {
        type: 'object',
        properties: {
            watermark: {
                type: 'string',
            },
        },
    },
})
static async applyWatermark(context: Context) {
    return {
        watermark: 'Pre-processed watermark',
    };
}
```

### Assign operations to your report

With the service-option 'DevTools' enabled you can edit the report using the application's UI,
two reference fields are added to the page to let you define which operations you want to be executed by the report engine.

![Report's pre and post operation fields](assets/images/reports-pre-and-post-operation-fields.png)

Once opened the lookup will be filtered to only show compatible operations.

![Select a pre or post operation on the report page](assets/images/report-select-pre-or-post-operation.png)

Alternatively you can edit the report's CSV to define these columns:

| name      | pre_processing_operation                   | post_processing_operation        |
| --------- | ------------------------------------------ | -------------------------------- |
| myReport  | MyNode\|onlySinglePrintCanBePrinted\|start |                                  |
| myReport2 | MyNode\|applyWatermark\|start              | MyNode\|notifyExternalApp\|start |
| myReport3 |                                            | MyNode\|setIsPrintedFlag\|start  |

the pattern for the natural key on the MetaNodeOperation in `<NodeName>|<operationName>|start`, the last part is the action of the operation but we only allow start.

### Test your report

To test your report while developing, don't use the preview as this particular printing method won't trigger the associated pre and post operations.
Instead use the Standard print action on the application's UI or this standard print mutation from the GraphQl explorer:

```graphql
mutation {
    xtremReporting {
        report {
            generateReportPdf {
                start(
                    reportTemplateName: "myReportsTemplate"
                    reportName: "myReport"
                    reportSettings: { paperFormat: "a4", variables: "<variables as JSON>" }
                ) {
                    trackingId
                }
            }
        }
    }
}
```

Once printed opening the batch task history page should reveal up to 3 new task, 1 for each operation: preProcessing, print, postProcessing according to your settings and the success or not of each task.
Here is an example of the screen on which we display the Logs of the print task showing how to retrieve the generated document.

![Find reports download url once generated](assets/images/report-print-task-history.png)

### Handle errors that occurred during print

To finalize the process we need to be able to manage errors, for that purpose the print engine is able to call the post-processing operation with additional parameters to identify the error and let the application reset the updated values.

```typescript
@decorators.asyncMutation<typeof MyNode, 'afterPrint'>({
    isPublished: true,
    parameters: [
        {
            name: 'report',
            type: 'reference',
            isWritable: true,
            node: () => xtremReporting.nodes.Report,
        },
        {
            name: 'error',
            type: 'object',
            isNullable: true,
            properties: {
                message: 'string',
            },
        }
    ],
    return: 'boolean',
})
static async afterPrint(
    context: Context,
    report: xtremReporting.nodes.Report,
    error: { message: string } | null
) {
    if(error) {
        await report.$.set({
          <property_to_clean>: '<clean_value>'
        });
    } else {
        await report.$.set({
          isPrinted: true,
        });
    }

    await report.$.save();

    return true;
}
```
