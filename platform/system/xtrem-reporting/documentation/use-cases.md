PATH: XTREEM/xtrem-reporting/Sample+use+cases

## Date ranges in the query filter

The Report can be used to pass dates to the query as strings through its variables collection. The base query from the Report Template would need to have its query filter parameters set and the values are replaced by handlebars expressions as follows:

### Report Variables

| Variable Name | Type   | Value      |
| ------------- | ------ | ---------- |
| dateFrom      | string | 2020-08-08 |
| dateTo        | string | 2020-08-12 |

### Report Template Query

```typescript
{
  xtremPurchasing {
    purchaseOrderLine {
      query(filter: "{purchaseOrder:{orderDate: { _gte: '{{dateFrom}}', _lte: '{{dateTo}}' } }}") {
        edges {
          node {
            purchaseOrder {
              orderDate
              number
              supplier {
                name
              }
            }
            item {
              name
            }
            quantity
            price
          }
        }
      }
    }
  }
}
```
