PATH: XTREEM/xtrem-reporting/Static+resources

Static resources can be used in report templates, they can be inserted as base64 data URLs.

## Helper function

The static resources can be inserted into reports based on their names. They are injected to the reports by the `reportResource` handlebars helper function. The returned value is a formatted data URL based which contains the mime type of the resource file.

## Example:

In the following example, the `companyLogo` resource image file is loaded into an image tag.

```
<div>
    <img alt="Company Logo" src="{{reportResource 'companyLogo'}}" />
    <h1>Financial report</h1>
    ...
</div>

```
