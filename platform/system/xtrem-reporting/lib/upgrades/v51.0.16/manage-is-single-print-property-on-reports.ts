import { CustomSqlAction } from '@sage/xtrem-system';

export const manageIsSinglePrintPropertyOnReports = new CustomSqlAction({
    description: 'Manage new property isSinglePrint on reports',
    fixes: {
        notNullableColumns: [{ table: 'report', column: 'is_single_print' }],
    },
    body: async helper => {
        await helper.executeSql(`
            UPDATE ${helper.schemaName}.report r
            SET is_single_print = CASE
                WHEN r.report_type = 'printedDocument' AND rv._id IS NOT NULL THEN true
                WHEN r.report_type = 'email' THEN null
                ELSE false
            END
            FROM ${helper.schemaName}.report r2
            LEFT JOIN ${helper.schemaName}.report_variable rv
            ON r2._id = rv.report
            AND r2._tenant_id = rv._tenant_id
            AND rv.is_mandatory = true
            AND rv.type = 'reference'
            WHERE r._id = r2._id
            AND r._tenant_id = r2._tenant_id;
        `);
    },
});
