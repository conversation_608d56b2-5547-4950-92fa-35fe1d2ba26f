import '@sage/xtrem-communication';

import { CoreHooks } from '@sage/xtrem-core';
import { PrintingManager } from './functions/printing-manager';

export * as activities from './activities/_index';
export * as dataTypes from './data-types/_index';
export * as enums from './enums';
export * as functions from './functions/_index';
export * as menuItems from './menu-items/_index';
export * as nodes from './nodes';
export * as serviceOptions from './service-options';
export * as sharedFunctions from './shared-functions/_index';
export * as workflowSteps from './workflow-steps/_index';

export function updateContext() {
    CoreHooks.createPrintingManager = () => new PrintingManager();
}

updateContext();
