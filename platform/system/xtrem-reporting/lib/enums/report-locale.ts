import { EnumDataType } from '@sage/xtrem-core';

export enum ReportLocaleEnum {
    ar_SA = 1,
    de_DE = 2,
    en_GB = 3,
    en_US = 4,
    es_ES = 5,
    fr_FR = 6,
    it_IT = 7,
    pl_PL = 8,
    pt_BR = 9,
    pt_PT = 10,
    zh_CN = 11,
}

export type ReportLocale = keyof typeof ReportLocaleEnum;

export const ReportLocaleDataType = new EnumDataType<ReportLocale>({
    enum: ReportLocaleEnum,
    filename: __filename,
});
