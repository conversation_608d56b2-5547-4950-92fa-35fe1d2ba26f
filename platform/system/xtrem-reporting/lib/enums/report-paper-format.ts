import { EnumDataType } from '@sage/xtrem-core';

// These are also declared in xtrem-pdf-generator but need to redeclare them here because of the enum-transformer
export enum ReportPaperFormatEnum {
    letter = 1,
    legal = 2,
    tabloid = 3,
    ledger = 4,
    a0 = 5,
    a1 = 6,
    a2 = 7,
    a3 = 8,
    a4 = 9,
    a5 = 10,
    a6 = 11,
}

export type ReportPaperFormat = keyof typeof ReportPaperFormatEnum;

export const ReportPaperFormatDataType = new EnumDataType<ReportPaperFormat>({
    enum: ReportPaperFormatEnum,
    filename: __filename,
});
