{"@sage/xtrem-reporting/activity__report__name": "Report", "@sage/xtrem-reporting/activity__report_resource__name": "Report resource", "@sage/xtrem-reporting/activity__report_style_variable__name": "Report style variable", "@sage/xtrem-reporting/activity__report_template__name": "Report template", "@sage/xtrem-reporting/cannot-modify-assignment-protected-by-a-vendor": "Cannot edit vendor protected assignments.", "@sage/xtrem-reporting/data_types__md_5_hash_data_type__name": "MD 5 hash data type", "@sage/xtrem-reporting/data_types__printing_type_enum__name": "Printing type enum", "@sage/xtrem-reporting/data_types__report_assignment_type_enum__name": "Report assignment type enum", "@sage/xtrem-reporting/data_types__report_locale_enum__name": "Report locale enum", "@sage/xtrem-reporting/data_types__report_page_orientation_enum__name": "Report page orientation enum", "@sage/xtrem-reporting/data_types__report_paper_format_enum__name": "Report paper format enum", "@sage/xtrem-reporting/data_types__report_processing_phase_enum__name": "Report processing phase enum", "@sage/xtrem-reporting/data_types__report_resource_type_enum__name": "Report resource type enum", "@sage/xtrem-reporting/data_types__report_style_variable_type_enum__name": "Report style variable type enum", "@sage/xtrem-reporting/data_types__report_type_enum__name": "Report type enum", "@sage/xtrem-reporting/data_types__style_variable_value_data_type__name": "Style variable value data type", "@sage/xtrem-reporting/data_types__template_type_enum__name": "Template type enum", "@sage/xtrem-reporting/enums__printing_type__multiple": "Multiple", "@sage/xtrem-reporting/enums__printing_type__notApplicable": "Not applicable", "@sage/xtrem-reporting/enums__printing_type__single": "Single", "@sage/xtrem-reporting/enums__report_assignment_type__detailPage": "Detail page", "@sage/xtrem-reporting/enums__report_assignment_type__mainList": "Main list", "@sage/xtrem-reporting/enums__report_locale__ar_SA": "Arabic (Saudi Arabia)", "@sage/xtrem-reporting/enums__report_locale__de_DE": "German (Germany)", "@sage/xtrem-reporting/enums__report_locale__en_GB": "English (United Kingdom)", "@sage/xtrem-reporting/enums__report_locale__en_US": "English (US)", "@sage/xtrem-reporting/enums__report_locale__es_ES": "Spanish (Spain)", "@sage/xtrem-reporting/enums__report_locale__fr_FR": "French (France)", "@sage/xtrem-reporting/enums__report_locale__it_IT": "Italian", "@sage/xtrem-reporting/enums__report_locale__pl_PL": "Polish", "@sage/xtrem-reporting/enums__report_locale__pt_BR": "Portuguese (Brazil)", "@sage/xtrem-reporting/enums__report_locale__pt_PT": "Portuguese (Portugal)", "@sage/xtrem-reporting/enums__report_locale__zh_CN": "Chinese (simplified)", "@sage/xtrem-reporting/enums__report_page_orientation__landscape": "Landscape", "@sage/xtrem-reporting/enums__report_page_orientation__portrait": "Portrait", "@sage/xtrem-reporting/enums__report_paper_format__a0": "A0", "@sage/xtrem-reporting/enums__report_paper_format__a1": "A1", "@sage/xtrem-reporting/enums__report_paper_format__a2": "A2", "@sage/xtrem-reporting/enums__report_paper_format__a3": "A3", "@sage/xtrem-reporting/enums__report_paper_format__a4": "A4", "@sage/xtrem-reporting/enums__report_paper_format__a5": "A5", "@sage/xtrem-reporting/enums__report_paper_format__a6": "A6", "@sage/xtrem-reporting/enums__report_paper_format__ledger": "Ledger", "@sage/xtrem-reporting/enums__report_paper_format__legal": "Legal", "@sage/xtrem-reporting/enums__report_paper_format__letter": "Letter", "@sage/xtrem-reporting/enums__report_paper_format__tabloid": "Tabloid", "@sage/xtrem-reporting/enums__report_processing_phase__postProcessing": "Post-processing", "@sage/xtrem-reporting/enums__report_processing_phase__postProcessingError": "Post processing error", "@sage/xtrem-reporting/enums__report_processing_phase__preProcessing": "Pre-processing", "@sage/xtrem-reporting/enums__report_resource_type__fontType": "Font type", "@sage/xtrem-reporting/enums__report_resource_type__image": "Image", "@sage/xtrem-reporting/enums__report_style_variable_type__boolean": "Boolean", "@sage/xtrem-reporting/enums__report_style_variable_type__number": "Number", "@sage/xtrem-reporting/enums__report_style_variable_type__string": "String", "@sage/xtrem-reporting/enums__report_type__email": "Email", "@sage/xtrem-reporting/enums__report_type__printedDocument": "Printed document", "@sage/xtrem-reporting/enums__report_variable_type__boolean": "Boolean", "@sage/xtrem-reporting/enums__report_variable_type__number": "Number", "@sage/xtrem-reporting/enums__report_variable_type__string": "String", "@sage/xtrem-reporting/enums__template_type__advanced": "Advanced", "@sage/xtrem-reporting/enums__template_type__form": "Form", "@sage/xtrem-reporting/enums__template_type__list": "List", "@sage/xtrem-reporting/function__generic_utils__send_user_notification__title_fail": "{{reportName}} printing unsuccessful", "@sage/xtrem-reporting/function__generic_utils__send_user_notification__title_success": "{{reportName}} printing successful", "@sage/xtrem-reporting/function__generic_utils__send_user_notification_description": "Document printed.", "@sage/xtrem-reporting/function__generic_utils__send_user_notification_description_error": "The document did not print. See the Batch task history page for details.", "@sage/xtrem-reporting/functions__document-builder__code-syntax-error": "The 'Template code' field uses an incorrect syntax: {{ErrorMessage}}", "@sage/xtrem-reporting/functions__document-builder__code-unexpected-error": "The 'Template code' field contains unexpected errors: {{ErrorMessage}}", "@sage/xtrem-reporting/functions__document-builder__current-no-code": "The current report template does not have a code block.", "@sage/xtrem-reporting/functions__document-builder__current-no-html-template": "This report template does not have an HTML template.", "@sage/xtrem-reporting/functions__document-builder__invalid-query": "Invalid query", "@sage/xtrem-reporting/functions__document-builder__invalid-report-variables": "Invalid report variables", "@sage/xtrem-reporting/functions__document-builder__no-active-template": "The {{reportName}} report does not have an active template.", "@sage/xtrem-reporting/functions__document-builder__no-code": "The '{{templateName}}' report template does not have a code block.", "@sage/xtrem-reporting/functions__document-builder__no-html-template": "The {{templateName}} report template does not have an HTML template.", "@sage/xtrem-reporting/functions__document-builder__query-error": "An error occurred whilst querying the data: {{ErrorMessage}}", "@sage/xtrem-reporting/functions__document-builder__report-not-found": "Report '{{reportName}}' not found", "@sage/xtrem-reporting/functions__document-builder__report-template-not-found": "Report template '{{templateName}}' not found", "@sage/xtrem-reporting/functions__pdf-utils__max-total-pages-reached": "This report exceeds the maximum page limit: {{maxTotalPages}}. Try to reduce the current number of estimated pages: ({{estimatedTotalPages}}).", "@sage/xtrem-reporting/functions__pre_post_processing__run_pre_post_processing_operation__finished": "Result for {{operationName}}, {{operationPhase}}: {{operationResult}}", "@sage/xtrem-reporting/functions__pre_post_processing__start_async_operation__starting": "Action started: {{operationName}}, {{operationPhase}}", "@sage/xtrem-reporting/functions__template_utils__invalid-template": "Invalid report template", "@sage/xtrem-reporting/invalid-attachment-mimetype": "The attachment MIME type is incorrect.", "@sage/xtrem-reporting/invalid-attachment-name": "The attachment file name is incorrect.", "@sage/xtrem-reporting/margin-unit-cm": "cm", "@sage/xtrem-reporting/margin-unit-inch": "in", "@sage/xtrem-reporting/menu_item__reporting": "Reporting", "@sage/xtrem-reporting/nodes__report__a-mandatory-reference-variable-required-for-single-print-reports": "A mandatory reference variable is required for single print reports.", "@sage/xtrem-reporting/nodes__report__asyncMutation__asyncExport": "Export", "@sage/xtrem-reporting/nodes__report__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-reporting/nodes__report__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-reporting/nodes__report__asyncMutation__generateReportAndNotifyUser": "Generate report and notify user", "@sage/xtrem-reporting/nodes__report__asyncMutation__generateReportAndNotifyUser__failed": "Generate report and notify user failed.", "@sage/xtrem-reporting/nodes__report__asyncMutation__generateReportAndNotifyUser__parameter__reportName": "Report name", "@sage/xtrem-reporting/nodes__report__asyncMutation__generateReportAndNotifyUser__parameter__reportSettings": "Report settings", "@sage/xtrem-reporting/nodes__report__asyncMutation__generateReportAndNotifyUser__parameter__reportTemplateName": "Report template name", "@sage/xtrem-reporting/nodes__report__asyncMutation__generateReportPdf": "Generate report PDF", "@sage/xtrem-reporting/nodes__report__asyncMutation__generateReportPdf__failed": "Generate report PDF failed.", "@sage/xtrem-reporting/nodes__report__asyncMutation__generateReportPdf__parameter__reportName": "Report name", "@sage/xtrem-reporting/nodes__report__asyncMutation__generateReportPdf__parameter__reportSettings": "Report settings", "@sage/xtrem-reporting/nodes__report__asyncMutation__generateReportPdf__parameter__reportTemplateName": "Report template name", "@sage/xtrem-reporting/nodes__report__asyncMutation__generateReportZip": "Generate report ZIP", "@sage/xtrem-reporting/nodes__report__asyncMutation__generateReportZip__failed": "Generate report ZIP failed.", "@sage/xtrem-reporting/nodes__report__asyncMutation__generateReportZip__parameter__reportName": "Report name", "@sage/xtrem-reporting/nodes__report__asyncMutation__generateReportZip__parameter__reportSettings": "Report settings", "@sage/xtrem-reporting/nodes__report__asyncMutation__generateReportZip__parameter__reportTemplateName": "Report template name", "@sage/xtrem-reporting/nodes__report__asyncMutation__generateUploadedFile": "Generate uploaded file", "@sage/xtrem-reporting/nodes__report__asyncMutation__generateUploadedFile__failed": "Generate uploaded file failed.", "@sage/xtrem-reporting/nodes__report__asyncMutation__generateUploadedFile__parameter__reportName": "Report name", "@sage/xtrem-reporting/nodes__report__asyncMutation__generateUploadedFile__parameter__reportSettings": "Report settings", "@sage/xtrem-reporting/nodes__report__asyncMutation__generateUploadedFile__parameter__reportTemplateName": "Report template name", "@sage/xtrem-reporting/nodes__report__asyncMutation__printRecords": "", "@sage/xtrem-reporting/nodes__report__asyncMutation__printRecords__failed": "", "@sage/xtrem-reporting/nodes__report__asyncMutation__printRecords__parameter__filter": "", "@sage/xtrem-reporting/nodes__report__asyncMutation__printRecords__parameter__joinDocuments": "", "@sage/xtrem-reporting/nodes__report__asyncMutation__printRecords__parameter__nodeName": "", "@sage/xtrem-reporting/nodes__report__asyncMutation__printRecords__parameter__pageOrientation": "", "@sage/xtrem-reporting/nodes__report__asyncMutation__printRecords__parameter__paperFormat": "", "@sage/xtrem-reporting/nodes__report__asyncMutation__printRecords__parameter__reportParameters": "", "@sage/xtrem-reporting/nodes__report__asyncMutation__printRecords__parameter__reportTemplate": "", "@sage/xtrem-reporting/nodes__report__error_generating_report": "Error during report generation: {{errorMessage}}", "@sage/xtrem-reporting/nodes__report__mutation__batchGenerateReports": "Batch generate reports", "@sage/xtrem-reporting/nodes__report__mutation__batchGenerateReports__parameter__reportName": "Report name", "@sage/xtrem-reporting/nodes__report__mutation__batchGenerateReports__parameter__reportSettings": "Report settings", "@sage/xtrem-reporting/nodes__report__mutation__createOrUpdateReport": "Create or update report", "@sage/xtrem-reporting/nodes__report__mutation__createOrUpdateReport__failed": "Create or update report failed.", "@sage/xtrem-reporting/nodes__report__mutation__createOrUpdateReport__parameter__data": "Data", "@sage/xtrem-reporting/nodes__report__mutation__generateReport": "Generate report", "@sage/xtrem-reporting/nodes__report__mutation__generateReport__parameter__reportName": "Report name", "@sage/xtrem-reporting/nodes__report__mutation__generateReport__parameter__reportSettings": "Report settings", "@sage/xtrem-reporting/nodes__report__mutation__generateReports": "Generate reports", "@sage/xtrem-reporting/nodes__report__mutation__generateReports__failed": "Generate reports failed.", "@sage/xtrem-reporting/nodes__report__mutation__generateReports__parameter__reportName": "Report name", "@sage/xtrem-reporting/nodes__report__mutation__generateReports__parameter__reportSettings": "Report settings", "@sage/xtrem-reporting/nodes__report__mutation__generateReports__parameter__reportTemplateName": "Report template name", "@sage/xtrem-reporting/nodes__report__name_exists": "This report name already exists.", "@sage/xtrem-reporting/nodes__report__node_name": "Report", "@sage/xtrem-reporting/nodes__report__only-printed-document-reports-can-be-single-or-multiple-prints": "Only '{{printedDocument}}' reports can be single or multiple prints.", "@sage/xtrem-reporting/nodes__report__property__activeTemplate": "Active template", "@sage/xtrem-reporting/nodes__report__property__description": "Description", "@sage/xtrem-reporting/nodes__report__property__isFactory": "Factory", "@sage/xtrem-reporting/nodes__report__property__isSinglePrint": "Is single print", "@sage/xtrem-reporting/nodes__report__property__name": "Name", "@sage/xtrem-reporting/nodes__report__property__pageAssignments": "Page assignments", "@sage/xtrem-reporting/nodes__report__property__parentPackage": "Parent package", "@sage/xtrem-reporting/nodes__report__property__postProcessingOperation": "Post-processing operation", "@sage/xtrem-reporting/nodes__report__property__preProcessingOperation": "Pre-processing operation", "@sage/xtrem-reporting/nodes__report__property__printingType": "Printing type", "@sage/xtrem-reporting/nodes__report__property__reportTemplates": "Report templates", "@sage/xtrem-reporting/nodes__report__property__reportType": "Report type", "@sage/xtrem-reporting/nodes__report__property__setupId": "Setup ID", "@sage/xtrem-reporting/nodes__report__property__variables": "Variables", "@sage/xtrem-reporting/nodes__report_assignment_association__asyncMutation__asyncExport": "Export", "@sage/xtrem-reporting/nodes__report_assignment_association__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-reporting/nodes__report_assignment_association__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-reporting/nodes__report_assignment_association__node_name": "Report assignment association", "@sage/xtrem-reporting/nodes__report_assignment_association__property__isActive": "Is active", "@sage/xtrem-reporting/nodes__report_assignment_association__property__isDefault": "Is default", "@sage/xtrem-reporting/nodes__report_assignment_association__property__page": "Page", "@sage/xtrem-reporting/nodes__report_assignment_association__property__report": "Report", "@sage/xtrem-reporting/nodes__report_assignment_page__asyncMutation__asyncExport": "Export", "@sage/xtrem-reporting/nodes__report_assignment_page__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-reporting/nodes__report_assignment_page__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-reporting/nodes__report_assignment_page__node_name": "Report assignment page", "@sage/xtrem-reporting/nodes__report_assignment_page__property__assignments": "Assignments", "@sage/xtrem-reporting/nodes__report_assignment_page__property__reportAssignmentType": "Report assignment type", "@sage/xtrem-reporting/nodes__report_assignment_page__property__screenId": "Screen ID", "@sage/xtrem-reporting/nodes__report_resource__asyncMutation__asyncExport": "Export", "@sage/xtrem-reporting/nodes__report_resource__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-reporting/nodes__report_resource__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-reporting/nodes__report_resource__node_name": "Report resource", "@sage/xtrem-reporting/nodes__report_resource__property__content": "Content", "@sage/xtrem-reporting/nodes__report_resource__property__description": "Description", "@sage/xtrem-reporting/nodes__report_resource__property__isFactory": "Factory", "@sage/xtrem-reporting/nodes__report_resource__property__mimetype": "Mimetype", "@sage/xtrem-reporting/nodes__report_resource__property__name": "Name", "@sage/xtrem-reporting/nodes__report_resource__property__type": "Type", "@sage/xtrem-reporting/nodes__report_style_variable__asyncMutation__asyncExport": "Export", "@sage/xtrem-reporting/nodes__report_style_variable__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-reporting/nodes__report_style_variable__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-reporting/nodes__report_style_variable__node_name": "Report style variable", "@sage/xtrem-reporting/nodes__report_style_variable__property__description": "Description", "@sage/xtrem-reporting/nodes__report_style_variable__property__isFactory": "Factory", "@sage/xtrem-reporting/nodes__report_style_variable__property__name": "Name", "@sage/xtrem-reporting/nodes__report_style_variable__property__setupId": "Setup ID", "@sage/xtrem-reporting/nodes__report_style_variable__property__type": "Type", "@sage/xtrem-reporting/nodes__report_style_variable__property__value": "Value", "@sage/xtrem-reporting/nodes__report_template__asyncMutation__asyncExport": "Export", "@sage/xtrem-reporting/nodes__report_template__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-reporting/nodes__report_template__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-reporting/nodes__report_template__mutation__generateReportSample": "Generate report sample", "@sage/xtrem-reporting/nodes__report_template__mutation__generateReportSample__failed": "Generate report sample failed.", "@sage/xtrem-reporting/nodes__report_template__mutation__generateReportSample__parameter__reportSettings": "Report settings", "@sage/xtrem-reporting/nodes__report_template__mutation__generateReportSample__parameter__reportTemplate": "Report template", "@sage/xtrem-reporting/nodes__report_template__mutation__generateWizardPreview": "Generate wizard preview", "@sage/xtrem-reporting/nodes__report_template__mutation__generateWizardPreview__failed": "Generate wizard preview failed.", "@sage/xtrem-reporting/nodes__report_template__mutation__generateWizardPreview__parameter__reportSettings": "Report settings", "@sage/xtrem-reporting/nodes__report_template__mutation__generateWizardPreview__parameter__template": "Template", "@sage/xtrem-reporting/nodes__report_template__mutation__importTranslations": "Import translations", "@sage/xtrem-reporting/nodes__report_template__mutation__importTranslations__failed": "Import translations failed.", "@sage/xtrem-reporting/nodes__report_template__mutation__importTranslations__parameter__input": "Input", "@sage/xtrem-reporting/nodes__report_template__node_name": "Report template", "@sage/xtrem-reporting/nodes__report_template__property__attachmentMimeType": "Attachment MIME type", "@sage/xtrem-reporting/nodes__report_template__property__attachmentName": "Attachment name", "@sage/xtrem-reporting/nodes__report_template__property__attachmentTemplate": "Attachment template", "@sage/xtrem-reporting/nodes__report_template__property__baseLocale": "Base locale", "@sage/xtrem-reporting/nodes__report_template__property__code": "Code", "@sage/xtrem-reporting/nodes__report_template__property__defaultBottomMargin": "Default bottom margin", "@sage/xtrem-reporting/nodes__report_template__property__defaultLeftMargin": "<PERSON><PERSON><PERSON> left margin", "@sage/xtrem-reporting/nodes__report_template__property__defaultPageOrientation": "Default page orientation", "@sage/xtrem-reporting/nodes__report_template__property__defaultPaperFormat": "Default paper format", "@sage/xtrem-reporting/nodes__report_template__property__defaultRightMargin": "De<PERSON>ult right margin", "@sage/xtrem-reporting/nodes__report_template__property__defaultTopMargin": "De<PERSON><PERSON> top margin", "@sage/xtrem-reporting/nodes__report_template__property__externalFooterHtmlTemplate": "External footer HTML template", "@sage/xtrem-reporting/nodes__report_template__property__externalHeaderHtmlTemplate": "External header HTML template", "@sage/xtrem-reporting/nodes__report_template__property__externalHtmlTemplate": "External HTML template", "@sage/xtrem-reporting/nodes__report_template__property__footerHtmlTemplate": "Footer HTML template", "@sage/xtrem-reporting/nodes__report_template__property__headerHtmlTemplate": "Header HTML template", "@sage/xtrem-reporting/nodes__report_template__property__htmlTemplate": "HTML template", "@sage/xtrem-reporting/nodes__report_template__property__isDefaultHeaderFooter": "Is default header footer", "@sage/xtrem-reporting/nodes__report_template__property__isExpertDocument": "Is expert or advanced document", "@sage/xtrem-reporting/nodes__report_template__property__isFactory": "Factory", "@sage/xtrem-reporting/nodes__report_template__property__name": "Name", "@sage/xtrem-reporting/nodes__report_template__property__query": "Query", "@sage/xtrem-reporting/nodes__report_template__property__report": "Report", "@sage/xtrem-reporting/nodes__report_template__property__reportType": "Report type", "@sage/xtrem-reporting/nodes__report_template__property__reportWizard": "Report wizard", "@sage/xtrem-reporting/nodes__report_template__property__setupId": "Setup ID", "@sage/xtrem-reporting/nodes__report_template__property__styleSheet": "Style sheet", "@sage/xtrem-reporting/nodes__report_template__property__templateType": "Template type", "@sage/xtrem-reporting/nodes__report_template__property__translatableTexts": "Translatable texts", "@sage/xtrem-reporting/nodes__report_template__query__exportTranslations": "Export translations", "@sage/xtrem-reporting/nodes__report_template__query__exportTranslations__failed": "Export translations failed.", "@sage/xtrem-reporting/nodes__report_template__query__exportTranslations__parameter__reportTemplates": "Report templates", "@sage/xtrem-reporting/nodes__report_template__query__exportTranslations__parameter__sourceLocale": "Source locale", "@sage/xtrem-reporting/nodes__report_template__query__exportTranslations__parameter__targetLocale": "Target locale", "@sage/xtrem-reporting/nodes__report_template__query__getSchema": "Get schema", "@sage/xtrem-reporting/nodes__report_template__query__getSchema__failed": "Get schema failed.", "@sage/xtrem-reporting/nodes__report_translatable_text__asyncMutation__asyncExport": "Export", "@sage/xtrem-reporting/nodes__report_translatable_text__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-reporting/nodes__report_translatable_text__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-reporting/nodes__report_translatable_text__invalid_hash_value": "The hash value is not a valid MD5 value.", "@sage/xtrem-reporting/nodes__report_translatable_text__node_name": "Report translatable text", "@sage/xtrem-reporting/nodes__report_translatable_text__property__hash": "Hash", "@sage/xtrem-reporting/nodes__report_translatable_text__property__isBaseLocale": "Base locale", "@sage/xtrem-reporting/nodes__report_translatable_text__property__locale": "Locale", "@sage/xtrem-reporting/nodes__report_translatable_text__property__originalText": "Original text", "@sage/xtrem-reporting/nodes__report_translatable_text__property__reportTemplate": "Report template", "@sage/xtrem-reporting/nodes__report_translatable_text__property__text": "Text", "@sage/xtrem-reporting/nodes__report_translatable_text__property__translations": "Translations", "@sage/xtrem-reporting/nodes__report_variable__asyncMutation__asyncExport": "Export", "@sage/xtrem-reporting/nodes__report_variable__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-reporting/nodes__report_variable__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-reporting/nodes__report_variable__node_name": "Report variable", "@sage/xtrem-reporting/nodes__report_variable__property__dataType": "Data type", "@sage/xtrem-reporting/nodes__report_variable__property__isMandatory": "Is mandatory", "@sage/xtrem-reporting/nodes__report_variable__property__name": "Name", "@sage/xtrem-reporting/nodes__report_variable__property__report": "Report", "@sage/xtrem-reporting/nodes__report_variable__property__title": "Title", "@sage/xtrem-reporting/nodes__report_variable__property__type": "Type", "@sage/xtrem-reporting/nodes__report_wizard__asyncMutation__asyncExport": "Export", "@sage/xtrem-reporting/nodes__report_wizard__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-reporting/nodes__report_wizard__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-reporting/nodes__report_wizard__node_name": "Report wizard", "@sage/xtrem-reporting/nodes__report_wizard__property__content": "Content", "@sage/xtrem-reporting/nodes__report_wizard__property__dataSource": "Data source", "@sage/xtrem-reporting/nodes__report_wizard__property__description": "Description", "@sage/xtrem-reporting/nodes__report_wizard__property__filters": "Filters", "@sage/xtrem-reporting/nodes__report_wizard__property__id": "ID", "@sage/xtrem-reporting/nodes__report_wizard__property__name": "Name", "@sage/xtrem-reporting/nodes__report_wizard__property__parameters": "Parameters", "@sage/xtrem-reporting/nodes__report_wizard__property__reportTemplate": "Report template", "@sage/xtrem-reporting/nodes__report_wizard__property__selectedProperties": "Selected properties", "@sage/xtrem-reporting/nodes__report_wizard__property__templateType": "Template type", "@sage/xtrem-reporting/nodes__report-assignment__a-default-report-already-exists-for-this-main-list": "A default report already exists on this page's main list: {{defaultReportName}}", "@sage/xtrem-reporting/nodes__report-assignment__a-default-report-already-exists-for-this-page": "A default report already exists on this page: {{defaultReportName}}.", "@sage/xtrem-reporting/nodes__report-template__cannot-modify-factory-template": "Factory templates cannot be updated.", "@sage/xtrem-reporting/nodes__report-template__name-camelcased": "Write the template name in camelCase.", "@sage/xtrem-reporting/nodes__report-template__wrong-attachment-extension": "Include the file extension in the attachment file name.", "@sage/xtrem-reporting/nodes__report-template__wrong-attachment-mimetype": "The attachment MIME type is incorrect.", "@sage/xtrem-reporting/nodes__report-variable__no-spaces-allowed-in-variable-name": "The variable name must not contain spaces.", "@sage/xtrem-reporting/only-one-default-assignment-allowed": "Only one default assignment is allowed.", "@sage/xtrem-reporting/package__name": "Reporting", "@sage/xtrem-reporting/page__report_template_generation_in_progress": "Report {{reportName}}: Generation in progress.", "@sage/xtrem-reporting/page-x-of-y": "Page [[current]] of [[total]]", "@sage/xtrem-reporting/pages__bulk_printing_dialog____title": "", "@sage/xtrem-reporting/pages__bulk_printing_dialog__joinDocuments____title": "", "@sage/xtrem-reporting/pages__bulk_printing_dialog__mainSection____title": "", "@sage/xtrem-reporting/pages__bulk_printing_dialog__pageFormatBlock____title": "", "@sage/xtrem-reporting/pages__bulk_printing_dialog__pageOrientation____title": "", "@sage/xtrem-reporting/pages__bulk_printing_dialog__paperFormat____title": "", "@sage/xtrem-reporting/pages__bulk_printing_dialog__reportParameters____title": "", "@sage/xtrem-reporting/pages__bulk_printing_dialog__reportTemplate____columns__title__name": "", "@sage/xtrem-reporting/pages__bulk_printing_dialog__reportTemplate____title": "", "@sage/xtrem-reporting/pages__bulk_printing_dialog__reportTemplateBlock____title": "", "@sage/xtrem-reporting/pages__delete_template_dialog_content": "You are about to delete template {{templateName}}.", "@sage/xtrem-reporting/pages__delete_template_title": "Confirm delete", "@sage/xtrem-reporting/pages__list_assignment_wizard____title": "Assign reports", "@sage/xtrem-reporting/pages__list_printing_wizard____title": "Assign reports", "@sage/xtrem-reporting/pages__list_report_creation____subtitle": "Choose a template style to match your needs.", "@sage/xtrem-reporting/pages__list_report_creation____title": "Create a template", "@sage/xtrem-reporting/pages__list_report_creation___id____title": "ID", "@sage/xtrem-reporting/pages__list_report_creation__activeTemplate____columns__title__isFactory": "Is factory template?", "@sage/xtrem-reporting/pages__list_report_creation__activeTemplate____columns__title__name": "Template name", "@sage/xtrem-reporting/pages__list_report_creation__activeTemplate____lookupDialogTitle": "Select active template", "@sage/xtrem-reporting/pages__list_report_creation__activeTemplate____title": "Active template", "@sage/xtrem-reporting/pages__list_report_creation__contentSection____title": "Content", "@sage/xtrem-reporting/pages__list_report_creation__dataEntity____columns__title__title": "Record type", "@sage/xtrem-reporting/pages__list_report_creation__dataEntity____title": "Data source", "@sage/xtrem-reporting/pages__list_report_creation__description____title": "Description", "@sage/xtrem-reporting/pages__list_report_creation__filterSection____title": "Filters", "@sage/xtrem-reporting/pages__list_report_creation__isDefaultHeaderFooter____title": "Default header and footer", "@sage/xtrem-reporting/pages__list_report_creation__isFactory____helperText": "Indicates a default report template", "@sage/xtrem-reporting/pages__list_report_creation__isFactory____title": "Is factory", "@sage/xtrem-reporting/pages__list_report_creation__name____title": "Name", "@sage/xtrem-reporting/pages__list_report_creation__newVariableDataType____title": "Data type", "@sage/xtrem-reporting/pages__list_report_creation__parameters____title": "Parameters", "@sage/xtrem-reporting/pages__list_report_creation__parentPackage____title": "Parent package", "@sage/xtrem-reporting/pages__list_report_creation__previewBottomMargin____title": "Bottom margin", "@sage/xtrem-reporting/pages__list_report_creation__previewLeftMargin____title": "Left margin", "@sage/xtrem-reporting/pages__list_report_creation__previewLocale____title": "Locale", "@sage/xtrem-reporting/pages__list_report_creation__previewPageOrientation____title": "Page orientation", "@sage/xtrem-reporting/pages__list_report_creation__previewPaperFormat____title": "Page format", "@sage/xtrem-reporting/pages__list_report_creation__previewRightMargin____title": "Right margin", "@sage/xtrem-reporting/pages__list_report_creation__previewSection____title": "Preview", "@sage/xtrem-reporting/pages__list_report_creation__previewTopMargin____title": "Top margin", "@sage/xtrem-reporting/pages__list_report_creation__propertiesBlock____title": "Build your data template with nested references.", "@sage/xtrem-reporting/pages__list_report_creation__propertiesSection____title": "Data", "@sage/xtrem-reporting/pages__list_report_creation__report____title": "Report", "@sage/xtrem-reporting/pages__list_report_creation__reportTemplates____title": "Report templates", "@sage/xtrem-reporting/pages__list_report_creation__reportType____title": "Report type", "@sage/xtrem-reporting/pages__list_report_creation__selectedProperties____title": "Properties", "@sage/xtrem-reporting/pages__list_report_creation__templateName____title": "Template name", "@sage/xtrem-reporting/pages__list_report_creation__variables____title": "Variables", "@sage/xtrem-reporting/pages__list-report-creation__duplicate_report_name": "The report name already exists.", "@sage/xtrem-reporting/pages__list-report-creation__duplicate_report_template_name": "The report template name already exists.", "@sage/xtrem-reporting/pages__print_document____title": "Print document", "@sage/xtrem-reporting/pages__printing_assignment_dialog____title": "Assign reports to the page", "@sage/xtrem-reporting/pages__printing_assignment_dialog__addReportAssignment____title": "Add", "@sage/xtrem-reporting/pages__printing_assignment_dialog__assignments____columns__title__isDefault": "<PERSON><PERSON><PERSON>", "@sage/xtrem-reporting/pages__printing_assignment_dialog__assignments____inlineActions__title": "Delete", "@sage/xtrem-reporting/pages__printing_assignment_dialog__createReport____title": "Create a report", "@sage/xtrem-reporting/pages__record_assignment_wizard____title": "Assign reports", "@sage/xtrem-reporting/pages__record_printing_assignment_dialog__assign____title": "Assign", "@sage/xtrem-reporting/pages__record_printing_wizard____title": "Assign reports", "@sage/xtrem-reporting/pages__report____navigationPanel__listItem__created__title": "Created", "@sage/xtrem-reporting/pages__report____navigationPanel__listItem__line_4__title": "Parent package", "@sage/xtrem-reporting/pages__report____navigationPanel__listItem__line_5__title": "Active template", "@sage/xtrem-reporting/pages__report____navigationPanel__listItem__line2__title": "Description", "@sage/xtrem-reporting/pages__report____navigationPanel__listItem__line3__title": "Factory", "@sage/xtrem-reporting/pages__report____navigationPanel__listItem__line4__title": "Parent package", "@sage/xtrem-reporting/pages__report____navigationPanel__listItem__line5__title": "Active template", "@sage/xtrem-reporting/pages__report____navigationPanel__listItem__line6__title": "Document type", "@sage/xtrem-reporting/pages__report____navigationPanel__listItem__title__title": "Name", "@sage/xtrem-reporting/pages__report____navigationPanel__listItem__titleRight__title": "ID", "@sage/xtrem-reporting/pages__report____navigationPanel__listItem__updated__title": "Updated", "@sage/xtrem-reporting/pages__report____navigationPanel__optionsMenu__title": "All reports", "@sage/xtrem-reporting/pages__report____navigationPanel__optionsMenu__title__2": "Factory reports", "@sage/xtrem-reporting/pages__report____navigationPanel__optionsMenu__title__3": "Custom reports", "@sage/xtrem-reporting/pages__report____objectTypePlural": "Reports", "@sage/xtrem-reporting/pages__report____objectTypeSingular": "Report", "@sage/xtrem-reporting/pages__report____title": "Report", "@sage/xtrem-reporting/pages__report__activeTemplate____columns__title__isFactory": "Factory template", "@sage/xtrem-reporting/pages__report__activeTemplate____columns__title__name": "Template name", "@sage/xtrem-reporting/pages__report__activeTemplate____lookupDialogTitle": "Select active template", "@sage/xtrem-reporting/pages__report__activeTemplate____title": "Active template", "@sage/xtrem-reporting/pages__report__addNewTemplate____title": "Add new template", "@sage/xtrem-reporting/pages__report__addVariableAction____title": "Add variable", "@sage/xtrem-reporting/pages__report__createViaWizard____title": "Create via wizard", "@sage/xtrem-reporting/pages__report__description____title": "Description", "@sage/xtrem-reporting/pages__report__isFactory____helperText": "Indicates if the report is provided by default.", "@sage/xtrem-reporting/pages__report__isFactory____title": "Factory", "@sage/xtrem-reporting/pages__report__isSinglePrint____helperText": "Indicates if report is generating a single print or a list of prints", "@sage/xtrem-reporting/pages__report__isSinglePrint____title": "Is single print", "@sage/xtrem-reporting/pages__report__name____title": "Name", "@sage/xtrem-reporting/pages__report__no_spaces_in_variable_name": "The variable name must not contain spaces.", "@sage/xtrem-reporting/pages__report__parentPackage____title": "Parent package", "@sage/xtrem-reporting/pages__report__postProcessingOperation____columns__title__factory__title": "Node", "@sage/xtrem-reporting/pages__report__postProcessingOperation____columns__title__name": "Name", "@sage/xtrem-reporting/pages__report__postProcessingOperation____columns__title__signature": "Signature", "@sage/xtrem-reporting/pages__report__postProcessingOperation____columns__title__title": "Translatable name", "@sage/xtrem-reporting/pages__report__postProcessingOperation____title": "Post-processing operation", "@sage/xtrem-reporting/pages__report__preProcessingOperation____columns__title__factory__title": "Node", "@sage/xtrem-reporting/pages__report__preProcessingOperation____columns__title__name": "Name", "@sage/xtrem-reporting/pages__report__preProcessingOperation____columns__title__signature": "Signature", "@sage/xtrem-reporting/pages__report__preProcessingOperation____columns__title__title": "Translatable name", "@sage/xtrem-reporting/pages__report__preProcessingOperation____title": "Pre-processing operation", "@sage/xtrem-reporting/pages__report__printingType____helperText": "Indicates if report is generating a single print or a list of prints", "@sage/xtrem-reporting/pages__report__printingType____title": "Printing type", "@sage/xtrem-reporting/pages__report__reportTemplates____columns__title__baseLocale": "Definition locale", "@sage/xtrem-reporting/pages__report__reportTemplates____columns__title__defaultPageOrientation": "Page orientation", "@sage/xtrem-reporting/pages__report__reportTemplates____columns__title__defaultPaperFormat": "Paper format", "@sage/xtrem-reporting/pages__report__reportTemplates____columns__title__name": "Name", "@sage/xtrem-reporting/pages__report__reportTemplates____columns__title__reportType": "Report type", "@sage/xtrem-reporting/pages__report__reportTemplates____columns__title__templateType": "Type", "@sage/xtrem-reporting/pages__report__reportTemplates____inlineActions__title": "Edit", "@sage/xtrem-reporting/pages__report__reportTemplates____inlineActions__title__2": "Generate", "@sage/xtrem-reporting/pages__report__reportTemplates____inlineActions__title__3": "Delete", "@sage/xtrem-reporting/pages__report__reportTemplates____title": "Templates", "@sage/xtrem-reporting/pages__report__reportType____title": "Report type", "@sage/xtrem-reporting/pages__report__saveAction____title": "Save", "@sage/xtrem-reporting/pages__report__templatesSection____title": "Templates", "@sage/xtrem-reporting/pages__report__variables____columns__title__dataType__title": "Data type", "@sage/xtrem-reporting/pages__report__variables____columns__title__isMandatory": "Is mandatory", "@sage/xtrem-reporting/pages__report__variables____columns__title__name": "Variable name", "@sage/xtrem-reporting/pages__report__variables____columns__title__title": "Variable title", "@sage/xtrem-reporting/pages__report__variables____columns__title__type": "Variable type", "@sage/xtrem-reporting/pages__report__variables____data_type_cannot_be_empty": "You need to select the data type.", "@sage/xtrem-reporting/pages__report__variables____dropdownActions__title": "Remove", "@sage/xtrem-reporting/pages__report__variables____duplicate_name": "This variable name is already used on this report. ", "@sage/xtrem-reporting/pages__report__variables____name_cannot_be_empty": "Variable name mandatory ", "@sage/xtrem-reporting/pages__report__variables____title": "Variables", "@sage/xtrem-reporting/pages__report__variables____type_cannot_be_empty": "Variable type mandatory ", "@sage/xtrem-reporting/pages__report__variableSection____title": "Variables", "@sage/xtrem-reporting/pages__report_generate_panel____subtitle": "Enter parameters to generate a report.", "@sage/xtrem-reporting/pages__report_generate_panel____title": "Generate report", "@sage/xtrem-reporting/pages__report_generate_panel__cancel____title": "Cancel", "@sage/xtrem-reporting/pages__report_generate_panel__description____title": "Description", "@sage/xtrem-reporting/pages__report_generate_panel__generate____title": "Generate", "@sage/xtrem-reporting/pages__report_generate_panel__mainSection____title": "Generate report", "@sage/xtrem-reporting/pages__report_generate_panel__name____title": "Name", "@sage/xtrem-reporting/pages__report_generate_panel__parameters____title": "Parameters", "@sage/xtrem-reporting/pages__report_generate_panel__templateName____title": "Template name", "@sage/xtrem-reporting/pages__report_resource____navigationPanel__listItem__line3__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-reporting/pages__report_resource____navigationPanel__listItem__titleRight__title": "ID", "@sage/xtrem-reporting/pages__report_resource____objectTypePlural": "Report resources", "@sage/xtrem-reporting/pages__report_resource____objectTypeSingular": "Report resource", "@sage/xtrem-reporting/pages__report_resource____title": "Resources", "@sage/xtrem-reporting/pages__report_resource__content____title": "Value", "@sage/xtrem-reporting/pages__report_resource__description____title": "Description", "@sage/xtrem-reporting/pages__report_resource__isFactory____helperText": "Indicates if report is factory provided", "@sage/xtrem-reporting/pages__report_resource__isFactory____title": "Factory", "@sage/xtrem-reporting/pages__report_resource__name____title": "Name", "@sage/xtrem-reporting/pages__report_resource__type____title": "Resource type", "@sage/xtrem-reporting/pages__report_selection____subtitle": "Choose a template style to match your needs.", "@sage/xtrem-reporting/pages__report_selection____title": "Create a report", "@sage/xtrem-reporting/pages__report_style_variable____navigationPanel__listItem__line_4__title": "Value", "@sage/xtrem-reporting/pages__report_style_variable____navigationPanel__listItem__line3__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-reporting/pages__report_style_variable____navigationPanel__listItem__line4__title": "Value", "@sage/xtrem-reporting/pages__report_style_variable____navigationPanel__listItem__titleRight__title": "ID", "@sage/xtrem-reporting/pages__report_style_variable____objectTypePlural": "Report style variables", "@sage/xtrem-reporting/pages__report_style_variable____objectTypeSingular": "Report style variable", "@sage/xtrem-reporting/pages__report_style_variable____title": "Style variables", "@sage/xtrem-reporting/pages__report_style_variable__description____title": "Description", "@sage/xtrem-reporting/pages__report_style_variable__isFactory____helperText": "Indicates if the report is provided by default.", "@sage/xtrem-reporting/pages__report_style_variable__isFactory____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-reporting/pages__report_style_variable__name____title": "Name", "@sage/xtrem-reporting/pages__report_style_variable__value____title": "Value", "@sage/xtrem-reporting/pages__report_template____navigationPanel__listItem__created__title": "Created", "@sage/xtrem-reporting/pages__report_template____navigationPanel__listItem__line_4__title": "Definition locale", "@sage/xtrem-reporting/pages__report_template____navigationPanel__listItem__line_5__title": "Document type", "@sage/xtrem-reporting/pages__report_template____navigationPanel__listItem__line2__title": "Report", "@sage/xtrem-reporting/pages__report_template____navigationPanel__listItem__line3__title": "Factory", "@sage/xtrem-reporting/pages__report_template____navigationPanel__listItem__line4__title": "Definition locale", "@sage/xtrem-reporting/pages__report_template____navigationPanel__listItem__line5__title": "Document type", "@sage/xtrem-reporting/pages__report_template____navigationPanel__listItem__templateType__title": "Type", "@sage/xtrem-reporting/pages__report_template____navigationPanel__listItem__title__title": "Name", "@sage/xtrem-reporting/pages__report_template____navigationPanel__listItem__titleRight__title": "ID", "@sage/xtrem-reporting/pages__report_template____navigationPanel__listItem__updated__title": "Updated", "@sage/xtrem-reporting/pages__report_template____navigationPanel__optionsMenu__title": "All templates", "@sage/xtrem-reporting/pages__report_template____navigationPanel__optionsMenu__title__2": "Factory templates", "@sage/xtrem-reporting/pages__report_template____navigationPanel__optionsMenu__title__3": "Custom templates", "@sage/xtrem-reporting/pages__report_template____objectTypePlural": "Report templates", "@sage/xtrem-reporting/pages__report_template____objectTypeSingular": "Report template", "@sage/xtrem-reporting/pages__report_template____title": "Report template", "@sage/xtrem-reporting/pages__report_template__appendicesFooterBlock____title": "Footer", "@sage/xtrem-reporting/pages__report_template__appendicesHeaderBlock____title": "Header", "@sage/xtrem-reporting/pages__report_template__appendicesSection____title": "Header and footer", "@sage/xtrem-reporting/pages__report_template__attachmentMimeType____helperText": "Example: text/XML", "@sage/xtrem-reporting/pages__report_template__attachmentMimeType____title": "Attachment MIME type", "@sage/xtrem-reporting/pages__report_template__attachmentName____helperText": "Example: attachment.xml", "@sage/xtrem-reporting/pages__report_template__attachmentName____title": "Attachment name", "@sage/xtrem-reporting/pages__report_template__attachmentSection____title": "Attachment", "@sage/xtrem-reporting/pages__report_template__attachmentTemplate____helperText": "", "@sage/xtrem-reporting/pages__report_template__attachmentTemplate____title": "Attachment template", "@sage/xtrem-reporting/pages__report_template__baseLocale____title": "Definition locale", "@sage/xtrem-reporting/pages__report_template__code____helperText": "Javascript code block for post-processing: The query result is available through the \"queryResponse\" reference.", "@sage/xtrem-reporting/pages__report_template__code____title": "Code", "@sage/xtrem-reporting/pages__report_template__codeSection____title": "Code", "@sage/xtrem-reporting/pages__report_template__convertToForm____title": "Convert to form", "@sage/xtrem-reporting/pages__report_template__convertToHtml____title": "Convert to HTML", "@sage/xtrem-reporting/pages__report_template__defaultBottomMargin____postfix": "cm", "@sage/xtrem-reporting/pages__report_template__defaultBottomMargin____title": "Bottom margin", "@sage/xtrem-reporting/pages__report_template__defaultLeftMargin____postfix": "cm", "@sage/xtrem-reporting/pages__report_template__defaultLeftMargin____title": "Left margin", "@sage/xtrem-reporting/pages__report_template__defaultPageOrientation____title": "Page orientation", "@sage/xtrem-reporting/pages__report_template__defaultPaperFormat____title": "Page format", "@sage/xtrem-reporting/pages__report_template__defaultRightMargin____postfix": "cm", "@sage/xtrem-reporting/pages__report_template__defaultRightMargin____title": "Right margin", "@sage/xtrem-reporting/pages__report_template__defaultTopMargin____postfix": "cm", "@sage/xtrem-reporting/pages__report_template__defaultTopMargin____title": "Top margin", "@sage/xtrem-reporting/pages__report_template__detailPanelHeaderBlock____title": "Preview", "@sage/xtrem-reporting/pages__report_template__detailPanelHeaderSection____title": "Preview", "@sage/xtrem-reporting/pages__report_template__detailPanelSettingsSection____title": "Settings", "@sage/xtrem-reporting/pages__report_template__detailPanelVariablesSection____title": "Variables", "@sage/xtrem-reporting/pages__report_template__duplicateTemplateAction____title": "Duplicate", "@sage/xtrem-reporting/pages__report_template__editViaWizard____title": "Edit", "@sage/xtrem-reporting/pages__report_template__extendPreview____title": "Extend preview", "@sage/xtrem-reporting/pages__report_template__externalFooterHtmlTemplate____helperText": "Footer HTML", "@sage/xtrem-reporting/pages__report_template__externalFooterHtmlTemplate____title": "Footer", "@sage/xtrem-reporting/pages__report_template__externalHeaderHtmlTemplate____helperText": "Header <PERSON>", "@sage/xtrem-reporting/pages__report_template__externalHeaderHtmlTemplate____title": "Header", "@sage/xtrem-reporting/pages__report_template__externalHtmlTemplate____helperText": "HTML source of the report", "@sage/xtrem-reporting/pages__report_template__externalHtmlTemplate____title": "Template", "@sage/xtrem-reporting/pages__report_template__formatHTML____title": "Format", "@sage/xtrem-reporting/pages__report_template__generate____title": "Generate", "@sage/xtrem-reporting/pages__report_template__htmlTemplateSection____title": "HTML template", "@sage/xtrem-reporting/pages__report_template__isFactory____helperText": "Indicates if the report template is provided by default.", "@sage/xtrem-reporting/pages__report_template__isFactory____title": "Factory", "@sage/xtrem-reporting/pages__report_template__mainSection____title": "Basic details", "@sage/xtrem-reporting/pages__report_template__name____title": "Name", "@sage/xtrem-reporting/pages__report_template__pageFormatBlock____title": "Page format", "@sage/xtrem-reporting/pages__report_template__previewAction____title": "Preview", "@sage/xtrem-reporting/pages__report_template__previewBottomMargin____postfix": "cm", "@sage/xtrem-reporting/pages__report_template__previewBottomMargin____title": "Preview bottom margin", "@sage/xtrem-reporting/pages__report_template__previewDialogSection____title": "Document preview", "@sage/xtrem-reporting/pages__report_template__previewLeftMargin____postfix": "cm", "@sage/xtrem-reporting/pages__report_template__previewLeftMargin____title": "Preview left margin", "@sage/xtrem-reporting/pages__report_template__previewLocale____title": "Preview locale", "@sage/xtrem-reporting/pages__report_template__previewPageOrientation____title": "Page orientation", "@sage/xtrem-reporting/pages__report_template__previewPaperFormat____title": "Page format", "@sage/xtrem-reporting/pages__report_template__previewRightMargin____postfix": "cm", "@sage/xtrem-reporting/pages__report_template__previewRightMargin____title": "Preview right margin", "@sage/xtrem-reporting/pages__report_template__previewTopMargin____postfix": "cm", "@sage/xtrem-reporting/pages__report_template__previewTopMargin____title": "Preview top margin", "@sage/xtrem-reporting/pages__report_template__previewWatermark____title": "Watermark", "@sage/xtrem-reporting/pages__report_template__query____helperText": "This query will find data and load it into the report.", "@sage/xtrem-reporting/pages__report_template__query____title": "Data query", "@sage/xtrem-reporting/pages__report_template__queryTemplateSection____title": "Data query", "@sage/xtrem-reporting/pages__report_template__report____title": "Report", "@sage/xtrem-reporting/pages__report_template__reportParameters____title": "Parameters", "@sage/xtrem-reporting/pages__report_template__reportType____title": "Report type", "@sage/xtrem-reporting/pages__report_template__reportWizard____title": "Report wizard", "@sage/xtrem-reporting/pages__report_template__save____title": "Save", "@sage/xtrem-reporting/pages__report_template__saveAction____title": "Save", "@sage/xtrem-reporting/pages__report_template__sendTestEmail____title": "Extend preview", "@sage/xtrem-reporting/pages__report_template__showPreview____title": "Preview", "@sage/xtrem-reporting/pages__report_template__styleSheet____helperText": "CSS style sheet", "@sage/xtrem-reporting/pages__report_template__styleSheet____title": "Style sheet", "@sage/xtrem-reporting/pages__report_template__styleSheetSection____title": "Style sheet", "@sage/xtrem-reporting/pages__report_template__templateSection____title": "Template", "@sage/xtrem-reporting/pages__report_template__translatableTexts____columns__title__locale": "Target language", "@sage/xtrem-reporting/pages__report_template__translatableTexts____columns__title__originalText__text": "Source", "@sage/xtrem-reporting/pages__report_template__translatableTexts____columns__title__text": "Target", "@sage/xtrem-reporting/pages__report_template__translatableTexts____dropdownActions__title": "Lookup", "@sage/xtrem-reporting/pages__report_template__translatableTexts____title": "Translations", "@sage/xtrem-reporting/pages__report_template__translationSection____title": "Translations", "@sage/xtrem-reporting/pages__report_template__translationSuggestion____columns__title___updateStamp": "Last modified", "@sage/xtrem-reporting/pages__report_template__translationSuggestion____columns__title__reportTemplate__name": "Report", "@sage/xtrem-reporting/pages__report_template__translationSuggestion____columns__title__reportTemplate__report__parentPackage": "Package", "@sage/xtrem-reporting/pages__report_template__translationSuggestion____columns__title__text": "Text", "@sage/xtrem-reporting/pages__report_template__translationSuggestion____title": "Suggestion", "@sage/xtrem-reporting/pages__report_template__variables____columns__title__name": "Variable", "@sage/xtrem-reporting/pages__report_template__variables____columns__title__type": "Type", "@sage/xtrem-reporting/pages__report_template__variables____columns__title__value": "Value", "@sage/xtrem-reporting/pages__report_template__variables____title": "Variables", "@sage/xtrem-reporting/pages__report_template_convert_dialog____title": "Convert to HTML mode", "@sage/xtrem-reporting/pages__report_template_convert_dialog__content____content": "If you convert to HTML mode, this template will be lost and the links you can access it from\n\nIf you keep this template, you can continue to use it, but any changes you make to it will not be reflected in the HTML mode.", "@sage/xtrem-reporting/pages__report_template_convert_dialog__convert____title": "Convert", "@sage/xtrem-reporting/pages__report_template_convert_dialog__copy____title": "Create HTML copy", "@sage/xtrem-reporting/pages__report_template_convert_dialog__form_content": "If you convert to a form, this template and the links where you access it from are lost.\n\n If you create a copy as a form, you can continue to use this list, but any changes you make are not made to the form version.", "@sage/xtrem-reporting/pages__report_template_convert_dialog__form_copy": "Create form copy", "@sage/xtrem-reporting/pages__report_template_convert_dialog__form_copy_title": "Create form copy", "@sage/xtrem-reporting/pages__report_template_convert_dialog__html_content": "If you convert to HTML, this template and the links where you access it from are lost.\n\n If you create an HTML copy, you can continue to use this template, but any changes you make are not made to the HTML version.", "@sage/xtrem-reporting/pages__report_template_convert_dialog__html_copy": "Create HTML copy", "@sage/xtrem-reporting/pages__template__created": "Record created", "@sage/xtrem-reporting/pages__template__updated": "Record updated", "@sage/xtrem-reporting/pages__template_editor____title": "Report templates", "@sage/xtrem-reporting/pages__template_editor___id____title": "ID", "@sage/xtrem-reporting/pages__template_editor__createTemplate____title": "New", "@sage/xtrem-reporting/pages__template_editor__deleteTemplate____title": "Delete", "@sage/xtrem-reporting/pages__template_editor__emailAddress____helperText": "A test report will be sent to this address.", "@sage/xtrem-reporting/pages__template_editor__emailAddress____title": "Address", "@sage/xtrem-reporting/pages__template_editor__isFactory____helperText": "Factory templates cannot be deleted.", "@sage/xtrem-reporting/pages__template_editor__isFactory____title": "Factory", "@sage/xtrem-reporting/pages__template_editor__key____helperText": "Template identifier", "@sage/xtrem-reporting/pages__template_editor__key____title": "Key", "@sage/xtrem-reporting/pages__template_editor__mainSection____title": "Editor", "@sage/xtrem-reporting/pages__template_editor__saveTemplate____title": "Save", "@sage/xtrem-reporting/pages__template_editor__template____title": "Template", "@sage/xtrem-reporting/pages__template_editor__test____title": "Test", "@sage/xtrem-reporting/pages__template_editor__testData____helperText": "Data in JSON format that will be displayed on the test template above.", "@sage/xtrem-reporting/pages__template_editor__testData____title": "Test data", "@sage/xtrem-reporting/pages__template_editor__testSection____title": "Send a test email", "@sage/xtrem-reporting/pages__translation_import_export____title": "Translation import and export", "@sage/xtrem-reporting/pages__translation_import_export__exportField____title": "Download", "@sage/xtrem-reporting/pages__translation_import_export__exportSection____title": "Export", "@sage/xtrem-reporting/pages__translation_import_export__importField____title": "Import file", "@sage/xtrem-reporting/pages__translation_import_export__importSection____title": "Import", "@sage/xtrem-reporting/pages__translation_import_export__reportTemplates____helperText": "Select the reports for export (optional).", "@sage/xtrem-reporting/pages__translation_import_export__reportTemplates____title": "Report template", "@sage/xtrem-reporting/pages__translation_import_export__sourceLocale____title": "Source language", "@sage/xtrem-reporting/pages__translation_import_export__targetLocale____title": "Target language", "@sage/xtrem-reporting/pages__workflow_action_print_document____subtitle": "Print a document", "@sage/xtrem-reporting/pages__workflow_action_print_document____title": "Action configuration", "@sage/xtrem-reporting/pages__workflow_action_print_document__mainSection____title": "Basic details", "@sage/xtrem-reporting/pages__workflow_action_print_document__outputVariableName____title": "Output variable name", "@sage/xtrem-reporting/pages__workflow_action_print_document__report____title": "Report", "@sage/xtrem-reporting/pages__workflow_action_print_document__reportParameters____title": "Parameters", "@sage/xtrem-reporting/pages__workflow_action_print_document__reportParametersPod____title": "Report parameters", "@sage/xtrem-reporting/pages__workflow_action_print_document__title____helperText": "Title displayed in the workflow diagram.", "@sage/xtrem-reporting/pages__workflow_action_print_document__title____title": "Action title", "@sage/xtrem-reporting/pages-confirm-delete": "Delete", "@sage/xtrem-reporting/permission__manage__name": "Manage", "@sage/xtrem-reporting/permission__read__name": "Read", "@sage/xtrem-reporting/removing-expert-flag": "You cannot convert an advanced document to one that is not.", "@sage/xtrem-reporting/report-creation-wizard-mode-advanced-description": "Create a template using HTML tools and a custom CSS.", "@sage/xtrem-reporting/report-creation-wizard-mode-advanced-title": "Advanced", "@sage/xtrem-reporting/report-creation-wizard-mode-form-description": "Design a custom form such as a purchase or sales order that you can use for specific customers or another unique business need.", "@sage/xtrem-reporting/report-creation-wizard-mode-form-title": "Form", "@sage/xtrem-reporting/report-creation-wizard-mode-list-description": "Create a table with several columns and a grand total at the end.", "@sage/xtrem-reporting/report-creation-wizard-mode-list-title": "List", "@sage/xtrem-reporting/service_options__async_per_post_processing__name": "Async per post processing", "@sage/xtrem-reporting/service_options__report_assignment__name": "Report assignment"}