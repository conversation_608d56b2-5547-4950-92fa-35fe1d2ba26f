import { GraphApi, ReportPageOrientation, ReportPaperFormat, ReportTemplate } from '@sage/xtrem-reporting-api';
import { fillDynamicPod } from '@sage/xtrem-system/build/lib/client-functions/dynamic-pod-helper';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.page<BulkPrintingDialog>({
    title: 'Bulk printing settings',
    isTransient: true,
    onLoad() {
        //TODO: FILTER THE REPORT TEMPLATES BASED ON THE PAGE
        ui.console.log(`BulkPrintingDialog from ${this.$.queryParameters[ui.QUERY_PARAM_PRINTING_SOURCE_PAGE]}`);
        this.nodeName.value = String(this.$.queryParameters[ui.QUERY_PARAM_PRINTING_NODE_TYPE] || '').split('/').pop();

    },
    businessActions() {
        return [this.$standardCancelAction, this.$standardDialogConfirmationAction];
    },
})
export class BulkPrintingDialog extends ui.Page<GraphApi> {

    @ui.decorators.textField<BulkPrintingDialog>({
    })
    nodeName: ui.fields.Text;

    @ui.decorators.section<BulkPrintingDialog>({
        isTitleHidden: true,
        title: 'Basic Details',
    })
    mainSection: ui.containers.Section;

    @ui.decorators.block<BulkPrintingDialog>({
        parent() {
            return this.mainSection;
        },
        title: 'Report template',
        isTitleHidden: true,
    })
    reportTemplateBlock: ui.containers.Block;

    @ui.decorators.referenceField<BulkPrintingDialog, ReportTemplate>({
        parent() {
            return this.reportTemplateBlock;
        },
        columns: [
            ui.nestedFields.text({ bind: 'name', title: 'Name' }),
            ui.nestedFields.text({ bind: { defaultPaperFormat: true, }, isHidden: true }),
            ui.nestedFields.text({ bind: { defaultPageOrientation: true, }, isHidden: true }),
            ui.nestedFields.text({ bind: { report: { _id: true }, }, isHidden: true }),
        ],
        node: '@sage/xtrem-reporting/ReportTemplate',
        valueField: 'name',
        title: 'Template',
        isMandatory: true,

        // TODO: Filter the report templates based on the page assignments
        isFullWidth: true,
        async onChange() {
            if (!this.reportTemplate.value) {
                return;
            }
            this.pageOrientation.value = this.reportTemplate.value.defaultPageOrientation;
            this.paperFormat.value = this.reportTemplate.value.defaultPaperFormat;
            const parameters = await fillDynamicPod({
                graph: this.$.graph,
                parameterNode: '@sage/xtrem-reporting/ReportVariable',
                filter: { report: this.reportTemplate.value.report._id },
                dynamicPod: this.reportParameters,
                locale: this.$.locale,
            });

            if (parameters.length > 0) {
                this.reportParameters.isHidden = false;
                this.reportParameters.value = {};
            }
        }
    })
    reportTemplate: ui.fields.Reference<ReportTemplate>;

    @ui.decorators.dynamicPodField<BulkPrintingDialog>({
        title: 'Parameters',
        isTitleHidden: true,
        isTransient: true,
        isFullWidth: true,
        isHidden: true,
        columns: [],
        parent() {
            return this.reportTemplateBlock;
        },
    })
    reportParameters: ui.fields.DynamicPod;

    @ui.decorators.block<BulkPrintingDialog>({
        parent() {
            return this.mainSection;
        },
        title: 'Page format',
        isTitleHidden: true,
    })
    pageFormatBlock: ui.containers.Block;

    @ui.decorators.dropdownListField<BulkPrintingDialog>({
        parent() {
            return this.pageFormatBlock;
        },
        title: 'Page orientation',
        optionType: '@sage/xtrem-reporting/ReportPageOrientation',
        isMandatory: true,
    })
    pageOrientation: ui.fields.DropdownList<ReportPageOrientation>;

    @ui.decorators.dropdownListField<BulkPrintingDialog>({
        parent() {
            return this.pageFormatBlock;
        },
        title: 'Page format',
        optionType: '@sage/xtrem-reporting/ReportPaperFormat',
        isMandatory: true,
    })
    paperFormat: ui.fields.DropdownList<ReportPaperFormat>;

    @ui.decorators.checkboxField<BulkPrintingDialog>({
        parent() {
            return this.pageFormatBlock;
        },
        title: 'Combine documents onto a single PDF',
    })
    joinDocuments: ui.fields.Checkbox;

}
