import { GraphApi, ReportStyleVariable as ReportStyleVariableApi } from '@sage/xtrem-reporting-api';
import {
    setApplicativePageCrudActions,
    setOrderOfPageHeaderDropDownActions,
} from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import * as ui from '@sage/xtrem-ui';
import { reporting } from '../menu-items/reporting';

@ui.decorators.page<ReportStyleVariable, ReportStyleVariableApi>({
    module: '',
    title: 'Style Variables',
    objectTypeSingular: 'Report Style Variable',
    objectTypePlural: 'Report Style Variables',
    idField() {
        return this.name;
    },
    node: '@sage/xtrem-reporting/ReportStyleVariable',
    menuItem: reporting,
    createAction() {
        return this.$standardNewAction;
    },
    headerDropDownActions() {
        return setOrderOfPageHeaderDropDownActions({
            remove: [this.$standardDeleteAction],
            dropDownBusinessActions: [this.$standardOpenRecordHistoryAction],
        });
    },
    headerQuickActions() {
        return [this.$standardDuplicateAction];
    },
    businessActions() {
        return [this.$standardCancelAction, this.$standardSaveAction];
    },
    onLoad() {
        this.$standardDeleteAction.isDisabled = !!this.isFactory.value;
        this.name.isReadOnly = !!this.isFactory.value;
        setApplicativePageCrudActions({
            page: this,
            isDirty: false,
            save: this.$standardSaveAction,
            cancel: this.$standardCancelAction,
            duplicate: this.$standardDuplicateAction,
            remove: this.$standardDeleteAction,
        });
    },
    navigationPanel: {
        listItem: {
            titleRight: ui.nestedFields.icon({
                bind: '_id',
                title: 'ID',
                map: () => {
                    return 'factory';
                },
                isHidden: (value, rowData) => {
                    if (rowData) {
                        return !rowData.isFactory;
                    }
                },
            }),
            title: ui.nestedFields.text({ bind: 'name' }),
            line2: ui.nestedFields.text({ bind: 'description' }),

            line_4: ui.nestedFields.text<ReportStyleVariable, ReportStyleVariableApi>({
                bind: 'value',
                title: 'Value',
            }),
            line3: ui.nestedFields.checkbox<ReportStyleVariable, ReportStyleVariableApi>({
                bind: 'isFactory',
                title: 'Default',
            }),
        },
    },
    onDirtyStateUpdated(isDirty: boolean) {
        setApplicativePageCrudActions({
            page: this,
            isDirty,
            save: this.$standardSaveAction,
            cancel: this.$standardCancelAction,
            duplicate: this.$standardDuplicateAction,
            remove: this.$standardDeleteAction,
        });
    },
})
export class ReportStyleVariable extends ui.Page<GraphApi, ReportStyleVariableApi> {
    @ui.decorators.section<ReportStyleVariable>({
        isTitleHidden: true,
    })
    mainSection: ui.containers.Section;

    @ui.decorators.block<ReportStyleVariable>({
        parent() {
            return this.mainSection;
        },
    })
    mainBlock: ui.containers.Block;

    @ui.decorators.textField<ReportStyleVariable>({
        parent() {
            return this.mainBlock;
        },
        title: 'Name',
        isMandatory: true,
    })
    name: ui.fields.Text;

    @ui.decorators.checkboxField<ReportStyleVariable>({
        parent() {
            return this.mainBlock;
        },
        title: 'Is factory',
        helperText: 'Indicates if report is factory provided',
        isReadOnly: true,
    })
    isFactory: ui.fields.Checkbox;

    @ui.decorators.textAreaField<ReportStyleVariable>({
        parent() {
            return this.mainBlock;
        },
        isFullWidth: true,
        rows: 4,
        title: 'Description',
        isMandatory: true,
    })
    description: ui.fields.TextArea;

    @ui.decorators.textField<ReportStyleVariable>({
        parent() {
            return this.mainBlock;
        },
        title: 'Value',
        isMandatory: true,
        isFullWidth: true,
    })
    value: ui.fields.Text;
}
