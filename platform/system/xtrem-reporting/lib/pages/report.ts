import { datetime, formatDateToCurrentLocale } from '@sage/xtrem-date-time';
import { MetaNodeOperation } from '@sage/xtrem-metadata-api';
import {
    GraphApi,
    PrintingType,
    Report as ReportApi,
    ReportTemplate,
    ReportType,
    ReportVariable,
    TemplateType,
} from '@sage/xtrem-reporting-api';
import {
    setApplicativePageCrudActions,
    setOrderOfPageHeaderDropDownActions,
} from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import { confirmDialogWithAcceptButtonText } from '@sage/xtrem-system/build/lib/client-functions/main-list-actions';
import * as ui from '@sage/xtrem-ui';
import { PartialCollectionValueWithIds } from '@sage/xtrem-ui/build/lib/component/types';
import { kebabCase } from 'lodash';
import { reporting } from '../menu-items/reporting';

@ui.decorators.page<Report, ReportApi>({
    module: '',
    title: 'Report',
    objectTypeSingular: 'Report',
    objectTypePlural: 'Reports',
    node: '@sage/xtrem-reporting/Report',
    headerSection() {
        return this.mainSection;
    },
    menuItem: reporting,
    mode: 'tabs',
    idField() {
        return this.name;
    },
    createAction() {
        return [this.$standardNewAction, this.createViaWizard];
    },
    headerDropDownActions() {
        return setOrderOfPageHeaderDropDownActions({
            remove: [this.$standardDeleteAction],
            dropDownBusinessActions: [this.$standardOpenRecordHistoryAction],
        });
    },
    businessActions() {
        return [this.$standardCancelAction, this.$standardSaveAction];
    },
    async onLoad() {
        setApplicativePageCrudActions({
            page: this,
            isDirty: false,
            save: this.$standardSaveAction,
            cancel: this.$standardCancelAction,
            remove: this.$standardDeleteAction,
        });

        const isFactory = this.isFactory.value === true;

        this.name.isDisabled = isFactory || !!this.name.value;
        this.parentPackage.isDisabled = isFactory;
        this.description.isDisabled = isFactory;
        this.$standardDeleteAction.isDisabled = isFactory;
        this.variables.isDisabled = isFactory;
        // Disable reportType field if editing item:
        this.reportType.isDisabled = !!this.name.value;

        // Non standard use of service options here because we only want to manage if the field
        // is editable and visible using devTools.
        const isDevToolsEnabled = this.$.isServiceOptionEnabled('devTools');
        this.preProcessingOperation.isHidden = !isDevToolsEnabled;
        this.preProcessingOperation.isDisabled = !isDevToolsEnabled;
        this.postProcessingOperation.isHidden = !isDevToolsEnabled;
        this.postProcessingOperation.isDisabled = !isDevToolsEnabled;

        const result = await this.$.graph.raw('{__type(name: "RootQueryType") {name,fields{name}}}');
        this.parentPackage.options = result.__type.fields.map((t: any) => kebabCase(t.name));
        this.$.setPageClean();
    },
    onDirtyStateUpdated(isDirty: boolean) {
        setApplicativePageCrudActions({
            page: this,
            isDirty,
            save: this.$standardSaveAction,
            cancel: this.$standardCancelAction,
            remove: this.$standardDeleteAction,
        });
    },
    navigationPanel: {
        optionsMenu: [
            { title: 'All Reports', graphQLFilter: {} },
            { title: 'Factory Reports', graphQLFilter: { isFactory: true } },
            { title: 'Custom Reports', graphQLFilter: { isFactory: false } },
        ],
        listItem: {
            titleRight: ui.nestedFields.icon<Report, ReportApi>({
                bind: '_id',
                title: 'ID',
                map: () => {
                    return 'factory';
                },
                isHidden: (value, rowData) => {
                    if (rowData) {
                        return !rowData.isFactory;
                    }
                },
            }),
            title: ui.nestedFields.text<Report, ReportApi>({ bind: 'name', title: 'Name' }),
            line2: ui.nestedFields.text<Report, ReportApi>({ bind: 'description', title: 'Description' }),
            line_4: ui.nestedFields.select<Report, ReportApi>({
                title: 'Parent package',
                bind: 'parentPackage',
            }),
            line3: ui.nestedFields.checkbox<Report, ReportApi>({ bind: 'isFactory', title: 'Factory' }),
            line_5: ui.nestedFields.reference<Report, ReportApi>({
                bind: 'activeTemplate',
                title: 'Active template',
                node: '@sage/xtrem-reporting/ReportTemplate',
                valueField: 'name',
                isHiddenDesktop: true,
                tunnelPage: undefined,
            }),
            line6: ui.nestedFields.icon<Report, ReportApi>({
                bind: 'reportType',
                title: 'Document type',
                map(value: any) {
                    if (value === 'printedDocument') {
                        return 'pdf';
                    } else {
                        return value;
                    }
                },
            }),
            created: ui.nestedFields.label<Report, ReportApi>({
                bind: '_createStamp',
                title: 'Created',
                isHiddenOnMainField: true,
                map(value: any) {
                    const locale = this.$.locale || 'en-US';
                    return value
                        ? `${formatDateToCurrentLocale(datetime.parse(value, locale), locale)} ${datetime
                              .parse(value)
                              .time.toString()}`
                        : '';
                },
            }),
            updated: ui.nestedFields.label<Report, ReportApi>({
                bind: '_updateStamp',
                title: 'Updated',
                isHiddenOnMainField: true,
                map(value: any) {
                    const locale = this.$.locale || 'en-US';
                    return value
                        ? `${formatDateToCurrentLocale(datetime.parse(value, locale), locale)} ${datetime
                              .parse(value)
                              .time.toString()}`
                        : '';
                },
            }),
        },
    },
})
export class Report extends ui.Page<GraphApi, ReportApi> {
    getSerializedValues() {
        const { values } = this.$;

        if (values.reportType !== 'printedDocument' && values.printingType) {
            // enforce no update on printingType if reportType isn't printedDocument
            delete values.printingType;
        }
        return values;
    }

    private validateReportVariable(data: ReportVariable): boolean {
        let isValid = true;
        let validationMessage = '';

        if (data.name === '' || data.name === null) {
            isValid = false;
            validationMessage += ui.localize(
                '@sage/xtrem-reporting/pages__report__variables____name_cannot_be_empty',
                'Variable name cannot be empty. ',
            );
        }

        const variableNames = this.variables.value.filter(row => row._id !== data._id).map(row => row.name);
        if (variableNames.includes(data.name)) {
            isValid = false;
            validationMessage += ui.localize(
                '@sage/xtrem-reporting/pages__report__variables____duplicate_name',
                'Variable name is already in use on this report. ',
            );
        }

        if (data.type === null) {
            isValid = false;
            validationMessage += ui.localize(
                '@sage/xtrem-reporting/pages__report__variables____type_cannot_be_empty',
                'Variable type must be selected. ',
            );
        }

        if (isValid) {
            return isValid;
        } else {
            this.$.showToast(validationMessage, { type: 'error' });
        }
        return false;
    }

    @ui.decorators.pageAction<Report>({
        title: 'Create via wizard',
        buttonType: 'secondary',
        async onClick() {
            await this.$.dialog.page(
                '@sage/xtrem-reporting/ReportSelection',
                {},
                { resolveOnCancel: true, height: 500, size: 'extra-large' },
            );
        },
    })
    createViaWizard: ui.PageAction;

    @ui.decorators.section<Report>({
        isTitleHidden: true,
    })
    mainSection: ui.containers.Section;

    @ui.decorators.section<Report>({
        title: 'Templates',
    })
    templatesSection: ui.containers.Section;

    @ui.decorators.section<Report>({
        title: 'Variables',
    })
    variableSection: ui.containers.Section;

    @ui.decorators.block<Report>({
        parent() {
            return this.mainSection;
        },
    })
    mainBlock: ui.containers.Block;

    @ui.decorators.textField<Report>({
        parent() {
            return this.mainBlock;
        },
        isHidden: true,
    })
    _id: ui.fields.Text;

    @ui.decorators.textField<Report>({
        parent() {
            return this.mainBlock;
        },
        title: 'Name',
        isMandatory: true,
    })
    name: ui.fields.Text;

    @ui.decorators.selectField<Report>({
        parent() {
            return this.mainBlock;
        },
        title: 'Parent package',
        isMandatory: true,
    })
    parentPackage: ui.fields.Select;

    @ui.decorators.referenceField<Report, ReportTemplate>({
        parent() {
            return this.mainBlock;
        },
        title: 'Active template',
        lookupDialogTitle: 'Select active template',
        node: '@sage/xtrem-reporting/ReportTemplate',
        valueField: 'name',
        columns: [
            ui.nestedFields.text({ bind: 'name', title: 'Template Name' }),
            ui.nestedFields.checkbox({ bind: 'isFactory', title: 'Is Factory template?' }),
            ui.nestedFields.reference({
                bind: 'report',
                valueField: '_id',
                node: '@sage/xtrem-reporting/Report',
                isHidden: true,
            }),
            ui.nestedFields.reference({
                bind: 'reportWizard',
                valueField: 'name',
                node: '@sage/xtrem-reporting/Report',
                isHidden: true,
                columns: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical({ bind: 'description' }),
                ],
            }),
        ],
        filter() {
            return {
                report: { _id: this._id.value },
            };
        },
    })
    activeTemplate: ui.fields.Reference;

    @ui.decorators.dropdownListField<Report>({
        parent() {
            return this.mainBlock;
        },
        title: 'Report type',
        optionType: '@sage/xtrem-reporting/ReportType',
        fetchesDefaults: true,
    })
    reportType: ui.fields.DropdownList<ReportType>;

    @ui.decorators.checkboxField<Report>({
        parent() {
            return this.mainBlock;
        },
        title: 'Is factory',
        helperText: 'Indicates if report is factory provided',
        isReadOnly: true,
    })
    isFactory: ui.fields.Checkbox;

    @ui.decorators.selectField<Report>({
        parent() {
            return this.mainBlock;
        },
        title: 'Printing type',
        helperText: 'Indicates if report is generating a single print or a list of prints',
        optionType: '@sage/xtrem-reporting/PrintingType',
    })
    printingType: ui.fields.Select<PrintingType>;

    @ui.decorators.referenceField<Report, MetaNodeOperation>({
        parent() {
            return this.mainBlock;
        },
        title: 'Pre-processing operation',
        node: '@sage/xtrem-metadata/MetaNodeOperation',
        isHidden: true,
        isDisabled: true,
        columns: [
            ui.nestedFields.text({ bind: { factory: { title: true } }, title: 'Node' }),
            ui.nestedFields.text({ bind: 'name', title: 'Name' }),
            ui.nestedFields.text({ bind: 'title', title: 'Translatable name' }),
            ui.nestedFields.text({ bind: 'signature', title: 'Signature' }),
        ],
    })
    preProcessingOperation: ui.fields.Reference<MetaNodeOperation, Report>;

    @ui.decorators.referenceField<Report>({
        parent() {
            return this.mainBlock;
        },
        title: 'Post-processing operation',
        node: '@sage/xtrem-metadata/MetaNodeOperation',
        isHidden: true,
        isDisabled: true,
        columns: [
            ui.nestedFields.text({ bind: { factory: { title: true } }, title: 'Node' }),
            ui.nestedFields.text({ bind: 'name', title: 'Name' }),
            ui.nestedFields.text({ bind: 'title', title: 'Translatable name' }),
            ui.nestedFields.text({ bind: 'signature', title: 'Signature' }),
        ],
    })
    postProcessingOperation: ui.fields.Reference;

    @ui.decorators.textAreaField<Report>({
        parent() {
            return this.mainBlock;
        },
        isFullWidth: true,
        rows: 4,
        title: 'Description',
        isMandatory: true,
    })
    description: ui.fields.TextArea;

    @ui.decorators.tableField<Report, ReportVariable>({
        title: 'Variables',
        canSelect: false,
        canAddNewLine: true,
        node: '@sage/xtrem-reporting/ReportVariable',
        parent() {
            return this.variableSection;
        },
        columns: [
            ui.nestedFields.text({
                bind: 'name',
                title: 'Variable Name',
                isMandatory: true,
                onChange(_id, rowData) {
                    this.validateReportVariable(rowData);
                },
                validation(val) {
                    if (val && val.indexOf(' ') >= 0) {
                        return ui.localize(
                            '@sage/xtrem-reporting/pages__report__no_spaces_in_variable_name',
                            'Variable name must not contain spaces.',
                        );
                    }
                    return undefined;
                },
            }),
            ui.nestedFields.checkbox({
                bind: 'isMandatory',
                title: 'Is Mandatory',
                onChange(_id, value) {
                    this.validateReportVariable(value);
                },
            }),
            ui.nestedFields.text({
                bind: 'title',
                title: 'Variable title',
                isMandatory: true,
                onChange(_id, rowData) {
                    this.validateReportVariable(rowData);
                },
            }),
            ui.nestedFields.dropdownList({
                bind: 'type',
                title: 'Variable type',
                optionType: '@sage/xtrem-metadata/MetaPropertyType',
                canFilter: false,
                isMandatory: true,
                onChange(_id, value) {
                    this.validateReportVariable(value);
                },
            }),
            ui.nestedFields.reference({
                bind: 'dataType',
                title: 'Data type',
                node: '@sage/xtrem-metadata/MetaDataType',
                valueField: 'title',
                isDisabled(_recordId: string, record: ReportVariable) {
                    return record.type !== 'reference' && record.type !== 'enum';
                },
                validation(value, record) {
                    if (!value && (record.type === 'reference' || record.type === 'enum')) {
                        return ui.localize(
                            '@sage/xtrem-reporting/pages__report__variables____data_type_cannot_be_empty',
                            'You need to select the data type.',
                        );
                    }
                    return undefined;
                },
                onChange(_id, value) {
                    this.validateReportVariable(value);
                },
            }),
        ],
        dropdownActions: [
            {
                icon: 'minus',
                title: 'Remove',
                isDisabled() {
                    return this.isFactory.value === true;
                },
                onClick(rowId: any) {
                    this.variables.removeRecord(rowId);
                },
            },
        ],
    })
    variables: ui.fields.Table;

    // @ui.decorators.section<Report>({
    //     title: 'New report variable',
    //     isHidden: true,
    // })
    // newReportVariableSection: ui.containers.Section;

    // @ui.decorators.block<Report>({
    //     isTitleHidden: true,
    //     parent() {
    //         return this.newReportVariableSection;
    //     },
    // })
    // newReportVariableBlock: ui.containers.Block;

    // @ui.decorators.dropdownListField<Report>({
    //     title: 'Variable Type',
    //     isTransient: true,
    //     isFullWidth: true,
    //     optionType: '@sage/xtrem-metadata/MetaPropertyType',
    //     onChange() {
    //         this.newVariableDataType.isDisabled = this.newVariableType.value !== 'reference';
    //     },
    //     parent() {
    //         return this.newReportVariableBlock;
    //     },
    // })
    // newVariableType: ui.fields.DropdownList;

    // @ui.decorators.textField<Report>({
    //     title: 'Variable name',
    //     isFullWidth: true,
    //     isTransient: true,
    //     parent() {
    //         return this.newReportVariableBlock;
    //     },
    // })
    // newVariableName: ui.fields.Text;

    // @ui.decorators.textField<Report>({
    //     title: 'Variable title',
    //     isFullWidth: true,
    //     isTransient: true,
    //     parent() {
    //         return this.newReportVariableBlock;
    //     },
    // })
    // newVariableTitle: ui.fields.Text;

    // @ui.decorators.referenceField<Report>({
    //     title: 'Data type',
    //     isFullWidth: true,
    //     isTransient: true,
    //     isDisabled: true,
    //     parent() {
    //         return this.newReportVariableBlock;
    //     },
    //     node: '@sage/xtrem-metadata/MetaDataType',
    //     filter: { type: 'reference' },
    //     valueField: 'title',
    // })
    // newVariableDataType: ui.fields.Reference<MetaDataType>;

    @ui.decorators.tableField<Report, ReportTemplate>({
        title: 'Templates',
        bind: 'reportTemplates',
        isTitleHidden: true,
        canFilter: true,
        canUserHideColumns: true,
        canSelect: false,
        canExport: true,
        isReadOnly: true,
        pageSize: 10,
        displayMode: ui.fields.TableDisplayMode.compact,
        node: '@sage/xtrem-reporting/ReportTemplate',
        orderBy: {
            name: 1,
        },
        parent() {
            return this.templatesSection;
        },
        columns: [
            ui.nestedFields.text({
                title: 'Name',
                bind: 'name',
                isReadOnly: true,
            }),
            ui.nestedFields.select({
                title: 'Type',
                bind: 'templateType',
                optionType: '@sage/xtrem-reporting/TemplateType',
            }),
            ui.nestedFields.icon({
                bind: 'reportType',
                title: 'Report type',
                map(value: any) {
                    if (value === 'printedDocument') {
                        return 'pdf';
                    } else {
                        return value;
                    }
                },
            }),
            ui.nestedFields.dropdownList({
                bind: 'baseLocale',
                title: 'Definition locale',
                optionType: '@sage/xtrem-reporting/ReportLocale',
            }),
            ui.nestedFields.dropdownList({
                bind: 'defaultPaperFormat',
                title: 'Paper format',
                optionType: '@sage/xtrem-reporting/ReportPaperFormat',
            }),
            ui.nestedFields.dropdownList({
                bind: 'defaultPageOrientation',
                title: 'Page orientation',
                optionType: '@sage/xtrem-reporting/ReportPageOrientation',
            }),
            ui.nestedFields.technical({
                bind: 'isExpertDocument',
            }),
            ui.nestedFields.technical({
                bind: 'isFactory',
            }),
            ui.nestedFields.technical({
                bind: 'report',
                nestedFields: [
                    ui.nestedFields.technical({
                        bind: '_id',
                    }),
                    ui.nestedFields.technical({
                        bind: 'name',
                    }),
                ],
            }),
        ],
        inlineActions: [
            {
                icon: 'edit',
                title: 'Edit',
                async onClick(rowId: string, rowItem: PartialCollectionValueWithIds<ReportTemplate>) {
                    this.editTemplate(rowId, rowItem.templateType);
                },
                isDisabled(_rowId: string, rowItem: PartialCollectionValueWithIds<ReportTemplate>) {
                    return rowItem.isFactory;
                },
            },
            {
                icon: 'print',
                title: 'Generate',
                onClick(_rowId: string, rowItem: PartialCollectionValueWithIds<ReportTemplate>) {
                    this.$.dialog.page(
                        '@sage/xtrem-reporting/ReportGeneratePanel',
                        {
                            _id: this.$.recordId || '',
                            reportName: this.name.value || '',
                            description: this.description.value || '',
                            reportTemplateName: rowItem.name,
                            reportSettings: JSON.stringify({
                                variables: this.variables.value,
                                locale: rowItem.baseLocale,
                                paperFormat: rowItem.defaultPaperFormat,
                                pageOrientation: rowItem.defaultPageOrientation,
                            }),
                        },
                        { size: 'extra-large' },
                    );
                },
            },
            {
                icon: 'bin',
                title: 'Delete',
                isDestructive: true,
                async onClick(rowId: string, rowItem: PartialCollectionValueWithIds<ReportTemplate>) {
                    if (
                        await confirmDialogWithAcceptButtonText(
                            this,
                            ui.localize('@sage/xtrem-reporting/pages__delete_template_title', 'Confirm delete'),
                            ui.localize(
                                '@sage/xtrem-reporting/pages__delete_template_dialog_content',
                                'You are about to delete template {{templateName}}.',
                                { templateName: rowItem.name },
                            ),
                            ui.localize('@sage/xtrem-reporting/pages-confirm-delete', 'Delete'),
                        )
                    ) {
                        await this.$.graph.delete({
                            _id: rowId,
                            nodeName: '@sage/xtrem-reporting/ReportTemplate',
                        });
                        await this.reportTemplates.refresh();
                    }
                },
                isDisabled(_rowId: string, rowItem: PartialCollectionValueWithIds<ReportTemplate>) {
                    return rowItem.isFactory || this.activeTemplate.value?._id === rowItem._id;
                },
            },
        ],
        headerBusinessActions() {
            return [this.addNewTemplate];
        },
    })
    reportTemplates: ui.fields.Table<ReportTemplate>;

    @ui.decorators.pageAction<Report>({
        icon: 'add',
        title: 'Add new template',
        onError() {
            // Intentionally left empty
        },
        async onClick() {
            await this.$.dialog.page(
                '@sage/xtrem-reporting/ReportSelection',
                {
                    report: JSON.stringify({
                        _id: this.$.recordId,
                        name: this.name.value,
                        description: this.description.value,
                    }),
                    fromReport: true,
                },
                { resolveOnCancel: true, height: 500, size: 'extra-large' },
            );
            this.reportTemplates.refresh();
        },
    })
    addNewTemplate: ui.PageAction;

    async editTemplate(rowId: string, templateType: TemplateType) {
        switch (templateType) {
            case 'form':
            case 'advanced':
                await this.$.router.goTo('@sage/xtrem-reporting/ReportTemplate', {
                    isExpertDocument: templateType === 'advanced',
                    _id: rowId,
                });
                break;
            case 'list':
            default:
                await this.$.dialog.page(
                    '@sage/xtrem-reporting/ListReportCreation',
                    { _id: this.$.recordId, templateId: rowId, fromReport: true },
                    { resolveOnCancel: true, height: 500, size: 'extra-large' },
                );
                break;
        }
    }
}
