import { extractEdges } from '@sage/xtrem-client';
import { GraphApi, Report, ReportVariable } from '@sage/xtrem-reporting-api';
import { LocalizedText, WorkflowVariable, getTextForLocale, mergeLocalizedText, titleCase } from '@sage/xtrem-shared';
import * as ui from '@sage/xtrem-ui';
import { WorkflowDefinition as WorkflowDefinitionApi } from '@sage/xtrem-workflow-api';
import {
    WorkflowConfigPageData,
    convertDynamicPodValueToWorkflowParameters,
    convertWorkflowParametersToDynamicPodValue,
    getInputData,
    getUsedVariablePathsFromDynamicPod,
} from '@sage/xtrem-workflow/lib/client-functions';
import {
    addIdIconToTitle,
    getNestedDynamicSelectProperties,
    getStepVariablesForSerialization,
    tidyVariables,
    transformVariablesFromSerialization,
    validateDynamicVariablesPickers,
} from '@sage/xtrem-workflow/lib/client-functions/variable-utils';
import { WorkflowParameter } from '@sage/xtrem-workflow/lib/shared-functions/workflow-config-types';
import { pick, startCase } from 'lodash';
import { updateReportParameterColumns } from '../client-functions/workflow-utils';

@ui.decorators.page<WorkflowActionPrintDocument, WorkflowDefinitionApi>({
    title: 'Action configuration',
    subtitle: 'Print a document',
    businessActions() {
        if (this.$.queryParameters.isReadOnly) {
            return [this.$standardCancelAction];
        }
        return [this.$standardDialogConfirmationAction];
    },
    async onLoad() {
        await this._onLoad();
    },
})
export class WorkflowActionPrintDocument extends ui.Page<GraphApi> {
    // Components

    @ui.decorators.section<WorkflowActionPrintDocument>({
        isTitleHidden: true,
        title: 'Basic Details',
        isDisabled() {
            return !!this.$.queryParameters.isReadOnly;
        },
    })
    mainSection: ui.containers.Section;

    @ui.decorators.block<WorkflowActionPrintDocument>({
        parent() {
            return this.mainSection;
        },
    })
    mainBlock: ui.containers.Block;

    @ui.decorators.textField<WorkflowActionPrintDocument>({
        title: 'Action title',
        helperText: 'Title displayed in the workflow diagram.',
        isFullWidth: true,
        isTransient: true,
        parent() {
            return this.mainBlock;
        },
    })
    title: ui.fields.Text;

    // Note: localizedTitle is a JSON object that contains the translations (something like { 'en-US': 'Hello', 'fr-FR': 'Bonjour' })
    // title is only editing the translation for the current locale
    @ui.decorators.technicalJsonField<WorkflowActionPrintDocument>({})
    localizedTitle: ui.fields.TechnicalJson<LocalizedText>;

    @ui.decorators.textField<WorkflowActionPrintDocument>({})
    subtitle: ui.fields.Text;

    @ui.decorators.referenceField<WorkflowActionPrintDocument, Report>({
        parent() {
            return this.mainBlock;
        },
        minLookupCharacters: 0,
        title: 'Report',
        node: '@sage/xtrem-reporting/Report',
        valueField: 'name',
        isMandatory: true,
        isFullWidth: true,
        filter: { reportType: { _eq: 'printedDocument' } },
        async onChange() {
            const reportName = this.report.value?.name ?? '';
            this.reportParametersPod.value = {};
            this.subtitle.value = reportName;
            await this.loadReportVariables();
            this.updateReportParameterColumns();
            // TODO: localize
            if (!this.title.value) this.title.value = `Print ${startCase(reportName).toLowerCase()}`;
        },
    })
    report: ui.fields.Reference;

    @ui.decorators.technicalJsonField<WorkflowActionPrintDocument>({})
    reportParameters: ui.fields.TechnicalJson<WorkflowParameter[]>;

    @ui.decorators.technicalJsonField<WorkflowActionPrintDocument>({})
    stepVariables: ui.fields.TechnicalJson<WorkflowVariable[]>;

    @ui.decorators.dynamicPodField<WorkflowActionPrintDocument>({
        title: 'Report parameters',
        isTransient: true,
        isFullWidth: true,
        isHidden: true,
        columns: [],
        onChange() {
            this.updateReportParameterColumns();
        },
        parent() {
            return this.mainBlock;
        },
    })
    reportParametersPod: ui.fields.DynamicPod;

    @ui.decorators.textField<WorkflowActionPrintDocument>({
        parent() {
            return this.mainBlock;
        },
        isFullWidth: true,
        isMandatory: true,
        title: 'Output variable name',
    })
    outputVariableName: ui.fields.Text;

    // Private variables

    private reportVariables: ReportVariable[] = [];

    private currentVariables: WorkflowVariable[] = [];

    private oldRootPaths: string[];

    private async _onLoad(): Promise<void> {
        const { oldRootPaths } = getInputData(this);
        this.stepVariables.value = transformVariablesFromSerialization(this.stepVariables.value ?? []);
        this.oldRootPaths = oldRootPaths;
        this._updateCurrentVariables([]);

        await this.restoreReportFieldFromString();
        this.reportParametersPod.value = convertWorkflowParametersToDynamicPodValue(this.reportParameters.value ?? []);

        await this.loadReportVariables();
        this.updateReportParameterColumns();

        if (!this.outputVariableName.value) {
            this.outputVariableName.value = 'printedDocument';
        }

        const locale = this.$.locale;
        this.title.value = getTextForLocale(this.localizedTitle.value ?? {}, locale);

        await this._validateFields();
    }

    /**
     * Validate the fields of the page.
     */
    private async _validateFields(): Promise<void> {
        await validateDynamicVariablesPickers([this.reportParametersPod]);
    }

    private async restoreReportFieldFromString(): Promise<void> {
        const name = this.report.value;
        if (name) {
            const result = await this.$.graph
                .node('@sage/xtrem-reporting/Report')
                .query(
                    ui.queryUtils.edgesSelector(
                        {
                            _id: true,
                            name: true,
                        },
                        {
                            filter: { name, reportType: { _eq: 'printedDocument' } },
                        },
                    ),
                )
                .execute();

            if (result.edges.length === 0) {
                throw new Error(`The report '${name}' does not exist.`);
            }
            this.report.value = result.edges[0].node;
        }
    }

    private _updateCurrentVariables(variables: WorkflowVariable[]): void {
        const { inputVariables } = getInputData(this);
        this.currentVariables = tidyVariables([...inputVariables, ...this.stepVariables.value, ...variables]);
        this.updateReportParameterColumns();
    }

    private async loadReportVariables() {
        if (this.report.value?._id) {
            this.reportVariables = extractEdges(
                await this.$.graph
                    .node('@sage/xtrem-reporting/ReportVariable')
                    .query(
                        ui.queryUtils.edgesSelector(
                            {
                                _id: true,
                                _sortValue: true,
                                name: true,
                                type: true,
                                title: true,
                                dataType: {
                                    name: true,
                                    attributes: true,
                                },
                                report: {
                                    _id: true,
                                },
                            },
                            { filter: { report: this.report.value._id } },
                        ),
                    )
                    .execute(),
            ) as any;
        }

        this.reportParametersPod.isHidden = !this.report.value?._id || this.reportVariables.length === 0;
        this.updateReportParameterColumns();

        this.reportVariables.forEach(variable => {
            const isVariableFieldName = `${variable.name}/isVariable`;
            if (this.reportParametersPod.value[isVariableFieldName] === undefined) {
                this.reportParametersPod.value[isVariableFieldName] = true;
            }
        });
    }

    private updateReportParameterColumns(): void {
        updateReportParameterColumns({
            reportParametersPod: this.reportParametersPod,
            reportVariables: this.reportVariables,
            locale: this.$.locale ?? 'en-US',
            getPropertiesForDynamicSelects: (filter: (_page: ui.Page, variable: WorkflowVariable) => boolean) => {
                return getNestedDynamicSelectProperties<WorkflowActionPrintDocument>({
                    mode: 'select',
                    getVariables: page => page.currentVariables,
                    selectableVariableFilter: filter,
                    getOldRootPaths: page => page.oldRootPaths,
                    updateCurrentVariables: (page, variables) => page._updateCurrentVariables(variables),
                });
            },
        });
    }

    /**
     * Returns the paths of the variables used by the action that should be serialized in the action's payload.
     */
    private _getUsedVariablePathsForSerialization(): string[] {
        const variableNamesToCheck = this.reportVariables.map(variable => variable.name);
        return getUsedVariablePathsFromDynamicPod(this.reportParametersPod, variableNamesToCheck);
    }

    getSerializedValues(): WorkflowConfigPageData<any> {
        const outputVariableName = this.outputVariableName.value;
        // Do not localize error here - value will always be set becausefield is mandatory
        if (!outputVariableName) throw new Error('Output variable name is required');

        const reportParameters = convertDynamicPodValueToWorkflowParameters(this.reportParametersPod.value);

        const usedPaths = this._getUsedVariablePathsForSerialization();
        const stepVariables = [
            ...getStepVariablesForSerialization(this.currentVariables, usedPaths),
            {
                path: `${outputVariableName}._id`,
                title: addIdIconToTitle(titleCase(outputVariableName)),
                type: 'IntReference',
                node: 'UploadedFile',
            },
        ];

        const locale = this.$.locale;
        const localizedTitle = mergeLocalizedText(this.localizedTitle.value ?? {}, this.title.value ?? '', locale);

        return {
            ...pick(this.$.values, 'subtitle', 'outputVariableName'),
            report: this.report.value?.name,
            stepVariables,
            reportParameters,
            localizedTitle,
        };
    }
}
