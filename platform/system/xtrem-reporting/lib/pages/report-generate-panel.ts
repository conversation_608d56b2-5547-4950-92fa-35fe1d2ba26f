import { GraphApi } from '@sage/xtrem-reporting-api';
import { fillDynamicPod } from '@sage/xtrem-system/build/lib/client-functions/dynamic-pod-helper';
import * as ui from '@sage/xtrem-ui';
import { getVariableValue } from '../client-functions/list-report-utils';

@ui.decorators.page<ReportGeneratePanel>({
    title: 'Generate report',
    subtitle: 'Enter parameters to generate a report.',
    isTransient: true,
    businessActions() {
        return [this.cancel, this.generate];
    },
    async onLoad() {
        this.reportSettings = JSON.parse(this.$.queryParameters.reportSettings as string);
        this.parameters.value = this.reportSettings.variables;
        this.name.value = this.$.queryParameters.reportName as string;
        this.templateName.value = this.$.queryParameters.reportTemplateName as string;
        this.description.value = this.$.queryParameters.description as string;
        this._id = this.$.queryParameters._id as string;
        this.loadDynamicPod();
    },
})
export class ReportGeneratePanel extends ui.Page<GraphApi> {
    private _id: string;
    private reportSettings: {
        variables: string;
        locale: string;
        paperFormat: string;
        pageOrientation: string;
    };
    @ui.decorators.section<ReportGeneratePanel>({
        isTitleHidden: true,
        title: 'Generate report',
    })
    mainSection: ui.containers.Section;

    @ui.decorators.block<ReportGeneratePanel>({
        parent() {
            return this.mainSection;
        },
    })
    mainBlock: ui.containers.Block;

    @ui.decorators.textField<ReportGeneratePanel>({
        parent() {
            return this.mainBlock;
        },
        title: 'Name',
        isReadOnly: true,
    })
    name: ui.fields.Text;

    @ui.decorators.textField<ReportGeneratePanel>({
        parent() {
            return this.mainBlock;
        },
        title: 'Template name',
        isReadOnly: true,
    })
    templateName: ui.fields.Text;

    @ui.decorators.textAreaField<ReportGeneratePanel>({
        parent() {
            return this.mainBlock;
        },
        title: 'Description',
        width: 'large',
        isReadOnly: true,
        rows: 5,
    })
    description: ui.fields.TextArea;

    @ui.decorators.dynamicPodField<ReportGeneratePanel>({
        title: 'Parameters',
        width: 'medium',
        isTitleHidden: true,
        columns: [],
        parent() {
            return this.mainBlock;
        },
    })
    parameters: ui.fields.DynamicPod;

    @ui.decorators.pageAction<ReportGeneratePanel>({
        icon: 'print',
        title: 'Generate',
        buttonType: 'primary',
        onError() {},
        async onClick() {
            const variables = getVariableValue(this.parameters.value || {});
            await this.$.graph
                .node('@sage/xtrem-reporting/Report')
                .asyncOperations.generateReportAndNotifyUser.start(
                    { trackingId: true },
                    {
                        reportName: this.name.value || '',
                        reportTemplateName: this.templateName.value,
                        reportSettings: {
                            ...this.reportSettings,
                            variables,
                        },
                    },
                )
                .execute();
            this.$.showToast(
                ui.localize(
                    '@sage/xtrem-reporting/page__report_template_generation_in_progress',
                    'Report {{reportName}}: Generation in progress.',
                    { reportName: this.name.value },
                ),
                { timeout: 3000, type: 'info' },
            );
            this.$.finish(true);
        },
    })
    generate: ui.PageAction;

    @ui.decorators.pageAction<ReportGeneratePanel>({
        title: 'Cancel',
        buttonType: 'tertiary',
        onClick() {
            this.$.finish(true);
        },
    })
    cancel: ui.PageAction;

    private async loadDynamicPod() {
        if (!this._id) return;
        this.parameters.value = {};
        await fillDynamicPod({
            graph: this.$.graph,
            parameterNode: '@sage/xtrem-reporting/ReportVariable',
            filter: { report: this._id },
            dynamicPod: this.parameters,
            locale: this.$.locale || 'en-US',
        });
    }
}
