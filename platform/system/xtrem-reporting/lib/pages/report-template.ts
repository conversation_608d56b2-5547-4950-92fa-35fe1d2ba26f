import { datetime, formatDateToCurrentLocale } from '@sage/xtrem-date-time';
import { Decimal } from '@sage/xtrem-decimal';
import { PaperOrientation, PaperSize } from '@sage/xtrem-document-editor';
import {
    Report,
    ReportTemplate as ReportTemplateApi,
    ReportTranslatableText,
    ReportWizard,
} from '@sage/xtrem-reporting-api';
import { prettify } from '@sage/xtrem-shared';
import {
    setDisplayOfCommonPageActions,
    setOrderOfPageHeaderDropDownActions,
} from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import { DataTypeAttribute, fillDynamicPod } from '@sage/xtrem-system/build/lib/client-functions/dynamic-pod-helper';
import * as ui from '@sage/xtrem-ui';
import type { GraphiqlPluginProperties } from '@sage/xtrem-ui-plugin-graphiql';
import type { MonacoPluginProperties } from '@sage/xtrem-ui-plugin-monaco';
import { TEMPLATE_CONVERT, TEMPLATE_COPY } from '../client-functions/client-constants';
import { getDisplayUnit, getVariableValue } from '../client-functions/list-report-utils';
import { extractSuggestionsFromGraphQLQuery } from '../client-functions/monaco-utils';
import { reporting } from '../menu-items/reporting';

const CM_INCH = 2.54;

@ui.decorators.page<ReportTemplate, ReportTemplateApi>({
    title: 'Report Template',
    objectTypeSingular: 'Report Template',
    objectTypePlural: 'Report Templates',
    idField() {
        return this.name;
    },
    node: '@sage/xtrem-reporting/ReportTemplate',
    mode: 'tabs',
    menuItem: reporting,
    createAction() {
        return this.$standardNewAction;
    },
    headerDropDownActions() {
        return setOrderOfPageHeaderDropDownActions({
            remove: [this.$standardDeleteAction],
            dropDownBusinessActions: [this.$standardOpenRecordHistoryAction],
        });
    },
    headerQuickActions() {
        return [this.$standardDuplicateAction];
    },
    async onLoad() {
        if (this.$.queryParameters.isExpertDocument)
            this.isExpertDocument.value = !!this.$.queryParameters.isExpertDocument;

        if (this.$.queryParameters.report) {
            this.report.value = JSON.parse(this.$.queryParameters.report as string);
        }
        await this.initPage();
        this.getSuggestionsFromQuery();

        this.$.setPageClean();
        setDisplayOfCommonPageActions({
            page: this,
            isDirty: this.$.isDirty,
            save: this.save,
            cancel: this.$standardCancelAction,
            duplicate: this.$standardDuplicateAction,
            remove: this.$standardDeleteAction,
            businessActions: [
                this.showPreview,
                this.convertToHtml,
                this.convertToForm,
                this.editViaWizard,
                this.generate,
            ],
        });
        this.manageBusinessActions(this.$.isDirty);
        if (this.$.page.id) {
            await this.loadVariables();
            // Don't wait for the preview to load before displaying the page.
            this.updatePreview();
        }
        this.$.setPageClean();
    },
    businessActions() {
        return [
            this.formatHTML,
            this.showPreview,
            this.convertToHtml,
            this.convertToForm,
            this.$standardCancelAction,
            this.save,
            this.editViaWizard,
            this.generate,
        ];
    },
    onDirtyStateUpdated(isDirty: boolean) {
        setDisplayOfCommonPageActions({
            page: this,
            isDirty,
            save: this.save,
            cancel: this.$standardCancelAction,
            duplicate: this.$standardDuplicateAction,
            remove: this.$standardDeleteAction,
            businessActions: [this.showPreview, this.convertToHtml, this.convertToForm, this.generate],
        });
        this.manageBusinessActions(isDirty);
    },
    detailPanel() {
        return {
            title: 'Preview',
            footerActions: [this.extendPreview],
            header: this.detailPanelHeaderSection,
            sections: [this.detailPanelSettingsSection, this.detailPanelVariablesSection],
        };
    },
    navigationPanel: {
        optionsMenu: [
            { title: 'All Templates', graphQLFilter: {} },
            { title: 'Factory Templates', graphQLFilter: { isFactory: true } },
            { title: 'Custom Templates', graphQLFilter: { isFactory: false } },
        ],
        orderBy: { name: 1 },
        listItem: {
            titleRight: ui.nestedFields.icon({
                bind: '_id',
                title: 'ID',
                map: () => {
                    return 'factory';
                },
                isHidden: (value, rowData) => {
                    if (rowData) {
                        return !rowData.isFactory;
                    }
                },
            }),
            title: ui.nestedFields.text({ bind: 'name', title: 'Name' }),
            line2: ui.nestedFields.text({
                bind: { report: { name: true } },
                title: 'Report',
            }),
            line_4: ui.nestedFields.dropdownList<ReportTemplate, ReportTemplateApi>({
                bind: 'baseLocale',
                title: 'Definition locale',
                optionType: '@sage/xtrem-reporting/ReportLocale',
            }),
            templateType: ui.nestedFields.select({
                title: 'Type',
                bind: 'templateType',
                optionType: '@sage/xtrem-reporting/TemplateType',
            }),
            line_5: ui.nestedFields.icon<ReportTemplate, ReportTemplateApi>({
                bind: 'reportType',
                title: 'Document type',
                map(value: any) {
                    if (value === 'printedDocument') {
                        return 'pdf';
                    } else {
                        return value;
                    }
                },
            }),
            line3: ui.nestedFields.checkbox({ bind: 'isFactory', title: 'Factory' }),
            created: ui.nestedFields.label<ReportTemplate, ReportTemplateApi>({
                bind: '_createStamp',
                title: 'Created',
                isHiddenOnMainField: true,
                map(value: any) {
                    const locale = this.$.locale || 'en-US';
                    return value
                        ? `${formatDateToCurrentLocale(datetime.parse(value, locale), locale)} ${datetime
                              .parse(value)
                              .time.toString()}`
                        : '';
                },
            }),
            updated: ui.nestedFields.label<ReportTemplate, ReportTemplateApi>({
                bind: '_updateStamp',
                title: 'Updated',
                isHiddenOnMainField: true,
                map(value: any) {
                    const locale = this.$.locale || 'en-US';
                    return value
                        ? `${formatDateToCurrentLocale(datetime.parse(value, locale), locale)} ${datetime
                              .parse(value)
                              .time.toString()}`
                        : '';
                },
            }),
        },
    },
})
export class ReportTemplate extends ui.Page {
    private previewTimeoutRef: number | null = null;
    private translationIdForLookupDialog: string | null = null;

    private convertToDisplayUnit(value: number): number {
        if (!value) {
            return 0;
        }

        if (this.$.locale === 'en-US') {
            return Decimal.div(value, CM_INCH).toDP(3).toNumber();
        }

        return value;
    }

    private convertToInternalUnit(value: number) {
        if (!value) {
            return 0;
        }

        if (this.$.locale === 'en-US') {
            return Decimal.mul(value, CM_INCH).toDP(3).toNumber();
        }
        return value;
    }

    private manageBusinessActions(isDirty: boolean) {
        const isFactory = this.isFactory.value;
        this.$standardDeleteAction.isDisabled = isFactory || this.$.isNewPage;
        this.save.isDisabled = isFactory;
        this.generate.isHidden = this.reportType.value === 'email' || isDirty || this.$.isNewPage;
        this.convertToHtml.isHidden =
            isDirty || isFactory || this.isExpertDocument.value || this.$.isNewPage || !!this.reportWizard.value;
        this.convertToForm.isHidden =
            isDirty || isFactory || this.isExpertDocument.value || this.$.isNewPage || !this.reportWizard.value;
        this.editViaWizard.isHidden =
            isDirty || isFactory || this.isExpertDocument.value || this.$.isNewPage || !this.reportWizard.value;
    }

    getSerializedValues() {
        const { values } = this.$;
        if (this.$.locale === 'en-US') {
            values.defaultBottomMargin = this.convertToInternalUnit(values.defaultBottomMargin);
            values.defaultTopMargin = this.convertToInternalUnit(values.defaultTopMargin);
            values.defaultLeftMargin = this.convertToInternalUnit(values.defaultLeftMargin);
            values.defaultRightMargin = this.convertToInternalUnit(values.defaultRightMargin);
        }
        values.externalHtmlTemplate = this.isExpertDocument.value
            ? {
                  value: this.externalHtmlTemplate.value,
              }
            : this.template.value;

        return values;
    }

    private async loadVariables() {
        if (!this.report.value?._id) return;
        this.reportParameters.value = {};
        const parameters = await fillDynamicPod({
            graph: this.$.graph,
            parameterNode: '@sage/xtrem-reporting/ReportVariable',
            filter: { report: this.report.value._id },
            dynamicPod: this.reportParameters,
            locale: this.$.locale,
        });

        this.template.parameters = parameters.map(r => {
            let type: string;
            switch (r.type) {
                case 'string':
                    type = 'String';
                    break;
                case 'boolean':
                    type = 'Boolean';
                    break;
                case 'date':
                    type = 'Date';
                    break;
                case 'float':
                case 'integer':
                case 'double':
                case 'decimal':
                case 'int':
                    type = 'Float';
                    break;
                default:
                    type = r.type;
            }

            return {
                name: r.name,
                label: r.title,
                type,
            };
        });
    }

    private async updatePreview() {
        if (this.$.detailPanel.isHidden || this.reportType.value === 'email') {
            // No need to generate a preview if the it will not be displayed
            return;
        }

        const variables = getVariableValue(this.reportParameters.value || {});

        const isMissingVariable = (this.reportParameters?.properties?.columns || [])
            .filter(val => (val.properties as unknown as DataTypeAttribute).isMandatory)
            .some(val => !Object.keys(JSON.parse(variables)).includes(val.properties.bind));

        if (isMissingVariable) return;

        const reportTemplate = this.getSerializedValues();

        if (!reportTemplate.externalHtmlTemplate?.value) {
            this.pdfPreview.value = null;
            this.pdfDialogPreview.value = null;
            return;
        }

        this.pdfPreview.isLoading = true;
        this.pdfDialogPreview.isLoading = true;

        try {
            const result = await this.$.graph
                .node('@sage/xtrem-reporting/ReportTemplate')
                .mutations.generateReportSample(
                    { value: true },
                    {
                        reportTemplate: {
                            _id: reportTemplate._id || null,
                            query: reportTemplate.query || null,
                            code: reportTemplate.code || null,
                            styleSheet: reportTemplate.styleSheet || null,
                            externalHtmlTemplate: reportTemplate.externalHtmlTemplate || null,
                            externalHeaderHtmlTemplate: reportTemplate.externalHeaderHtmlTemplate || null,
                            externalFooterHtmlTemplate: reportTemplate.externalFooterHtmlTemplate || null,
                            baseLocale: reportTemplate.baseLocale || null,
                            isExpertDocument: reportTemplate.isExpertDocument || false,
                            translatableTexts:
                                reportTemplate.translatableTexts?.map((t: any) => ({
                                    hash: t.hash,
                                    locale: t.locale,
                                    text: t.text,
                                })) || [],
                            attachmentTemplate: reportTemplate.attachmentTemplate || null,
                            attachmentName: reportTemplate.attachmentName || null,
                            attachmentMimeType: reportTemplate.attachmentMimeType || null,
                        },
                        reportSettings: {
                            locale: this.previewLocale.value || null,
                            paperFormat: this.previewPaperFormat.value || null,
                            pageOrientation: this.previewPageOrientation.value || null,
                            leftMarginCm: this.previewLeftMargin.value
                                ? this.convertToInternalUnit(this.previewLeftMargin.value)
                                : null,
                            rightMarginCm: this.previewRightMargin.value
                                ? this.convertToInternalUnit(this.previewRightMargin.value)
                                : null,
                            topMarginCm: this.previewTopMargin.value
                                ? this.convertToInternalUnit(this.previewTopMargin.value)
                                : null,
                            bottomMarginCm: this.previewBottomMargin.value
                                ? this.convertToInternalUnit(this.previewBottomMargin.value)
                                : null,
                            variables,
                            watermark: this.previewWatermark.value || null,
                        },
                    },
                )
                .execute();
            this.pdfPreview.value = result;
            this.pdfDialogPreview.value = result;
            this.pdfPreview.isLoading = false;
            this.pdfDialogPreview.isLoading = false;
        } catch (e) {
            ui.console.error(e);
            this.$.showToast('Failed to update preview.', { type: 'warning' });

            this.pdfPreview.isLoading = false;
            this.pdfDialogPreview.isLoading = false;
        }
    }

    private updateTypeDependentElementVisibility() {
        const isEditingEmailTemplate = this.reportType.value === 'email';

        this.extendPreview.isHidden = isEditingEmailTemplate;
        this.showPreview.isHidden = isEditingEmailTemplate;
        this.previewPaperFormat.isHidden = isEditingEmailTemplate;
        this.previewBottomMargin.isHidden = isEditingEmailTemplate;
        this.previewTopMargin.isHidden = isEditingEmailTemplate;
        this.previewLeftMargin.isHidden = isEditingEmailTemplate;
        this.previewRightMargin.isHidden = isEditingEmailTemplate;
        this.previewPageOrientation.isHidden = isEditingEmailTemplate;
        this.appendicesSection.isHidden = isEditingEmailTemplate || !this.isExpertDocument.value;
        this.attachmentSection.isHidden = isEditingEmailTemplate;
        this.$.detailPanel.isHidden = isEditingEmailTemplate;
        this.template.paperOrientation = isEditingEmailTemplate
            ? 'fullScreen'
            : (this.defaultPageOrientation.value as PaperOrientation);
    }

    private schedulePreviewUpdate() {
        if (this.previewTimeoutRef) {
            clearTimeout(this.previewTimeoutRef);
            this.previewTimeoutRef = null;
        }
        this.previewTimeoutRef = setTimeout(async () => {
            this.previewTimeoutRef = null;
            await this.updatePreview();
        }, 2000) as unknown as number;
    }

    @ui.decorators.pageAction<ReportTemplate>({
        title: 'Format',
        isHidden: true,
        async onClick() {
            this.externalHtmlTemplate.value = await prettify(this.externalHtmlTemplate.value || '', 'html');
            this.externalHeaderHtmlTemplate.value = await prettify(this.externalHeaderHtmlTemplate.value || '', 'html');
            this.externalFooterHtmlTemplate.value = await prettify(this.externalFooterHtmlTemplate.value || '', 'html');
        },
    })
    formatHTML: ui.PageAction;

    @ui.decorators.pageAction<ReportTemplate>({
        title: 'Extend preview',
        async onClick() {
            this.previewDialogSection.isHidden = false;
            await this.$.dialog.custom('info', this.previewDialogSection, {
                fullScreen: true,
                resolveOnCancel: true,
                cancelButton: { isHidden: true },
                acceptButton: { isHidden: true },
            });
            this.previewDialogSection.isHidden = true;
        },
        onError() {
            this.previewDialogSection.isHidden = true;
        },
        isHidden() {
            return this.$.isDirty || this.$.isNewPage;
        },
    })
    extendPreview: ui.PageAction;

    @ui.decorators.pageAction<ReportTemplate>({
        title: 'Save',
        async onClick() {
            const isUpdateMode = !this.$.isNewPage;
            await this.$standardSaveAction.execute(true);
            if (isUpdateMode) {
                this.initPage();
            }
        },
    })
    save: ui.PageAction;

    @ui.decorators.pageAction<ReportTemplate>({
        title: 'Preview',
        shortcut: ['alt', 'p'],
        async onClick() {
            if (this.$.detailPanel.isHidden) {
                this.$.detailPanel.isHidden = false;
                await this.updatePreview();
            } else {
                this.extendPreview.execute();
            }
        },
    })
    showPreview: ui.PageAction;

    @ui.decorators.pageAction<ReportTemplate>({
        title: 'Generate',
        async onClick() {
            const reportName = this.report.value.name;
            const variables = getVariableValue(this.reportParameters.value || {});
            await this.$.graph
                .node('@sage/xtrem-reporting/Report')
                .asyncOperations.generateReportAndNotifyUser.start(
                    { trackingId: true },
                    {
                        reportName,
                        reportTemplateName: this.name.value,
                        reportSettings: {
                            variables,
                            locale: this.previewLocale.value,
                            paperFormat: this.defaultPaperFormat.value,
                        },
                    },
                )
                .execute();
            this.$.showToast(
                ui.localize(
                    '@sage/xtrem-reporting/page__report_template_generation_in_progress',
                    'Report {{reportName}}: Generation in progress.',
                    { reportName },
                ),
                { timeout: 3000, type: 'info' },
            );
        },
        isHidden() {
            return this.reportType.value === 'email';
        },
    })
    generate: ui.PageAction;

    @ui.decorators.pageAction<ReportTemplate>({
        title: 'Convert to HTML',
        async onClick() {
            const result = await this.$.dialog.page(
                '@sage/xtrem-reporting/ReportTemplateConvertDialog',
                { _id: this.$.recordId },
                { resolveOnCancel: true },
            );

            if (result === TEMPLATE_CONVERT) {
                this.$.loader.isHidden = false;
                await this.$.graph.update({ _id: this.$.recordId, values: { isExpertDocument: true } });
                await this.$.router.refresh();
                this.$.loader.isHidden = true;
            } else if (result === TEMPLATE_COPY) {
                const duplicatedId = await this.$.graph.duplicate({
                    _id: this.$.recordId,
                    values: { isExpertDocument: true },
                });
                await this.$.router.selectRecord(duplicatedId, true);
                return;
            }
        },
    })
    convertToHtml: ui.PageAction;

    @ui.decorators.pageAction<ReportTemplate>({
        title: 'Convert to form',
        isHidden() {
            return (
                this.$.isDirty ||
                this.$.isNewPage ||
                this.isExpertDocument.value ||
                !this.reportWizard.value ||
                this.isFactory.value
            );
        },
        async onClick() {
            const result = await this.$.dialog.page(
                '@sage/xtrem-reporting/ReportTemplateConvertDialog',
                { _id: this.$.recordId, formCopy: true },
                { resolveOnCancel: true },
            );

            if (result === TEMPLATE_CONVERT) {
                this.$.loader.isHidden = false;
                await this.$.graph.update({ _id: this.$.recordId, values: { reportWizard: null } });
                await this.$.router.refresh();
                this.$.loader.isHidden = true;
            } else if (result === TEMPLATE_COPY) {
                const duplicatedId = await this.$.graph.duplicate({
                    _id: this.$.recordId,
                    values: { reportWizard: null },
                });
                await this.$.router.selectRecord(duplicatedId, true);
                return;
            }
        },
    })
    convertToForm: ui.PageAction;

    @ui.decorators.section<ReportTemplate>({
        title: 'Preview',
    })
    detailPanelHeaderSection: ui.containers.Section;

    @ui.decorators.block<ReportTemplate>({
        title: 'Preview',
        parent() {
            return this.detailPanelHeaderSection;
        },
    })
    detailPanelHeaderBlock: ui.containers.Block;

    @ui.decorators.previewField<ReportTemplate>({
        parent() {
            return this.detailPanelHeaderBlock;
        },
        height: 270,
        isFullWidth: true,
        isTransient: true,
        isTitleHidden: true,
        mimeType: 'application/pdf',
        filename: 'preview.pdf',
        canZoom: true,
        defaultZoomLevel: 0.5,
        isFilenameHidden: true,
    })
    pdfPreview: ui.fields.Preview;

    @ui.decorators.section<ReportTemplate>({
        title: 'Variables',
    })
    detailPanelVariablesSection: ui.containers.Section;

    @ui.decorators.block<ReportTemplate>({
        isTitleHidden: true,
        parent() {
            return this.detailPanelVariablesSection;
        },
    })
    detailPanelVariablesBlock: ui.containers.Block;

    @ui.decorators.dynamicPodField<ReportTemplate>({
        title: 'Parameters',
        isTitleHidden: true,
        isTransient: true,
        isFullWidth: true,
        columns: [],
        async onChange() {
            this.schedulePreviewUpdate();
            this.reportParameters.isDirty = false;
        },
        parent() {
            return this.detailPanelVariablesBlock;
        },
    })
    reportParameters: ui.fields.DynamicPod;

    @ui.decorators.section<ReportTemplate>({
        title: 'Settings',
    })
    detailPanelSettingsSection: ui.containers.Section;

    @ui.decorators.block<ReportTemplate>({
        isTitleHidden: true,
        parent() {
            return this.detailPanelSettingsSection;
        },
    })
    detailPanelSettingsBlock: ui.containers.Block;

    @ui.decorators.dropdownListField<ReportTemplate>({
        parent() {
            return this.detailPanelSettingsBlock;
        },
        isTransient: true,
        title: 'Page format',
        isFullWidth: true,
        optionType: '@sage/xtrem-reporting/ReportPaperFormat',
        async onChange() {
            this.schedulePreviewUpdate();
        },
        isHidden() {
            return this.reportType.value === 'email';
        },
    })
    previewPaperFormat: ui.fields.DropdownList;

    @ui.decorators.dropdownListField<ReportTemplate>({
        parent() {
            return this.detailPanelSettingsBlock;
        },
        isTransient: true,
        title: 'Page orientation',
        isFullWidth: true,
        optionType: '@sage/xtrem-reporting/ReportPageOrientation',
        async onChange() {
            this.schedulePreviewUpdate();
        },
        isHidden() {
            return this.reportType.value === 'email';
        },
    })
    previewPageOrientation: ui.fields.DropdownList;

    @ui.decorators.dropdownListField<ReportTemplate>({
        parent() {
            return this.detailPanelSettingsBlock;
        },
        isTransient: true,
        title: 'Preview locale',
        isFullWidth: true,
        optionType: '@sage/xtrem-reporting/ReportLocale',
        async onChange() {
            this.schedulePreviewUpdate();
        },
    })
    previewLocale: ui.fields.DropdownList;

    @ui.decorators.numericField<ReportTemplate>({
        parent() {
            return this.detailPanelSettingsBlock;
        },
        title: 'Preview top margin',
        width: 'small',
        postfix() {
            return getDisplayUnit(this.$.locale);
        },
        isTransient: true,
        scale: 2,
        async onChange() {
            this.schedulePreviewUpdate();
        },
    })
    previewTopMargin: ui.fields.Numeric;

    @ui.decorators.numericField<ReportTemplate>({
        parent() {
            return this.detailPanelSettingsBlock;
        },
        title: 'Preview bottom margin',
        width: 'small',
        postfix() {
            return getDisplayUnit(this.$.locale);
        },
        isTransient: true,
        scale: 2,
        async onChange() {
            this.schedulePreviewUpdate();
        },
    })
    previewBottomMargin: ui.fields.Numeric;

    @ui.decorators.numericField<ReportTemplate>({
        parent() {
            return this.detailPanelSettingsBlock;
        },
        title: 'Preview left margin',
        width: 'small',
        isTransient: true,
        postfix() {
            return getDisplayUnit(this.$.locale);
        },
        scale: 2,
        async onChange() {
            this.schedulePreviewUpdate();
        },
    })
    previewLeftMargin: ui.fields.Numeric;

    @ui.decorators.numericField<ReportTemplate>({
        parent() {
            return this.detailPanelSettingsBlock;
        },
        title: 'Preview right margin',
        width: 'small',
        isTransient: true,
        postfix() {
            return getDisplayUnit(this.$.locale);
        },
        scale: 2,
        async onChange() {
            this.schedulePreviewUpdate();
        },
    })
    previewRightMargin: ui.fields.Numeric;

    @ui.decorators.textField<ReportTemplate>({
        parent() {
            return this.detailPanelSettingsBlock;
        },
        isTransient: true,
        title: 'Watermark',
        isFullWidth: true,
        async onChange() {
            this.schedulePreviewUpdate();
        },
    })
    previewWatermark: ui.fields.Text;

    @ui.decorators.section<ReportTemplate>({
        isTitleHidden: true,
        title: 'Basic Details',
    })
    mainSection: ui.containers.Section;

    @ui.decorators.block<ReportTemplate>({
        parent() {
            return this.mainSection;
        },
    })
    mainBlock: ui.containers.Block;

    @ui.decorators.textField<ReportTemplate>({
        parent() {
            return this.mainBlock;
        },
        isHidden: true,
    })
    _id: ui.fields.Text;

    @ui.decorators.textField<ReportTemplate>({
        parent() {
            return this.mainBlock;
        },
        title: 'Name',
        isMandatory: true,
        maxLength: 180,
    })
    name: ui.fields.Text;

    @ui.decorators.referenceField<ReportTemplate, Report>({
        parent() {
            return this.mainBlock;
        },
        minLookupCharacters: 0,
        title: 'Report',
        node: '@sage/xtrem-reporting/Report',
        valueField: 'name',
        isMandatory: true,
        tunnelPage: '@sage/xtrem-reporting/Report',
        async onChange() {
            this.updateTypeDependentElementVisibility();
            this.reportParameters.setNestedFields([]);
            await this.loadVariables();
        },
    })
    report: ui.fields.Reference;

    @ui.decorators.referenceField<ReportTemplate, ReportWizard>({
        parent() {
            return this.mainBlock;
        },
        title: 'Report wizard',
        node: '@sage/xtrem-reporting/ReportWizard',
        valueField: 'name',
        isHidden: true,
        columns: [ui.nestedFields.technical({ bind: 'name' }), ui.nestedFields.technical({ bind: 'id' })],
    })
    reportWizard: ui.fields.Reference<ReportWizard>;

    @ui.decorators.block<ReportTemplate>({
        parent() {
            return this.mainSection;
        },
        isHidden() {
            return this.reportType.value === 'email';
        },
        title: 'Page format',
    })
    pageFormatBlock: ui.containers.Block;

    @ui.decorators.dropdownListField<ReportTemplate>({
        parent() {
            return this.mainBlock;
        },
        title: 'Definition locale',
        optionType: '@sage/xtrem-reporting/ReportLocale',
    })
    baseLocale: ui.fields.DropdownList;

    @ui.decorators.dropdownListField<ReportTemplate>({
        parent() {
            return this.pageFormatBlock;
        },
        title: 'Page format',
        optionType: '@sage/xtrem-reporting/ReportPaperFormat',
        onChange() {
            this.template.paperSize = this.defaultPaperFormat.value as PaperSize;
            this.previewPaperFormat.value = this.defaultPaperFormat.value;
            this.schedulePreviewUpdate();
        },
    })
    defaultPaperFormat: ui.fields.DropdownList;

    @ui.decorators.dropdownListField<ReportTemplate>({
        parent() {
            return this.pageFormatBlock;
        },
        title: 'Page orientation',
        optionType: '@sage/xtrem-reporting/ReportPageOrientation',
        onChange() {
            this.template.paperOrientation = this.defaultPageOrientation.value as PaperOrientation;
            this.previewPageOrientation.value = this.defaultPageOrientation.value;
            this.schedulePreviewUpdate();
        },
    })
    defaultPageOrientation: ui.fields.DropdownList;

    @ui.decorators.separatorField<ReportTemplate>({
        parent() {
            return this.pageFormatBlock;
        },
        isInvisible: true,
        isFullWidth: true,
    })
    paperFormatSeparator: ui.fields.Separator;

    @ui.decorators.numericField<ReportTemplate>({
        parent() {
            return this.pageFormatBlock;
        },
        title: 'Top margin',
        width: 'small',
        postfix() {
            return getDisplayUnit(this.$.locale);
        },
        scale: 2,
        onChange() {
            this.template.marginTop = this.defaultTopMargin.value;
            this.previewTopMargin.value = this.defaultTopMargin.value;
            this.schedulePreviewUpdate();
        },
    })
    defaultTopMargin: ui.fields.Numeric;

    @ui.decorators.numericField<ReportTemplate>({
        parent() {
            return this.pageFormatBlock;
        },
        title: 'Bottom margin',
        width: 'small',
        postfix() {
            return getDisplayUnit(this.$.locale);
        },
        scale: 2,
        onChange() {
            this.template.marginBottom = this.defaultBottomMargin.value;
            this.previewBottomMargin.value = this.defaultBottomMargin.value;
            this.schedulePreviewUpdate();
        },
    })
    defaultBottomMargin: ui.fields.Numeric;

    @ui.decorators.numericField<ReportTemplate>({
        parent() {
            return this.pageFormatBlock;
        },
        title: 'Left margin',
        width: 'small',
        postfix() {
            return getDisplayUnit(this.$.locale);
        },
        scale: 2,
        onChange() {
            this.template.marginLeft = this.defaultLeftMargin.value;
            this.previewLeftMargin.value = this.defaultLeftMargin.value;
            this.schedulePreviewUpdate();
        },
    })
    defaultLeftMargin: ui.fields.Numeric;

    @ui.decorators.numericField<ReportTemplate>({
        parent() {
            return this.pageFormatBlock;
        },
        title: 'Right margin',
        width: 'small',
        postfix() {
            return getDisplayUnit(this.$.locale);
        },
        scale: 2,
        onChange() {
            this.template.marginRight = this.defaultRightMargin.value;
            this.previewRightMargin.value = this.defaultRightMargin.value;
            this.schedulePreviewUpdate();
        },
    })
    defaultRightMargin: ui.fields.Numeric;

    @ui.decorators.iconField<ReportTemplate>({
        parent() {
            return this.mainBlock;
        },
        title: 'Report type',
        map(value: any) {
            if (value === 'printedDocument') {
                return 'pdf';
            } else {
                return value;
            }
        },
    })
    reportType: ui.fields.Icon;

    @ui.decorators.checkboxField<ReportTemplate>({
        parent() {
            return this.mainBlock;
        },
        title: 'Is factory',
        helperText: 'Indicates if report template is factory provided',
        isReadOnly: true,
    })
    isFactory: ui.fields.Checkbox;

    @ui.decorators.checkboxField<ReportTemplate>({
        access: {
            bind: 'create',
        },
    })
    isExpertDocument: ui.fields.Checkbox;

    @ui.decorators.section<ReportTemplate>({
        isTitleHidden: true,
        title: 'Data Query',
        isHidden() {
            return !this.isExpertDocument.value;
        },
    })
    queryTemplateSection: ui.containers.Section;

    @ui.decorators.pluginField<ReportTemplate, GraphiqlPluginProperties>({
        parent() {
            return this.queryTemplateSection;
        },
        title: 'Data Query',
        isTitleHidden: true,
        helperText: 'Write a query against the database',
        isFullWidth: true,
        isToolbarHidden: true,
        pluginPackage: '@sage/xtrem-ui-plugin-graphiql',
        onChange() {
            this.getSuggestionsFromQuery();
            this.schedulePreviewUpdate();
        },
    })
    query: ui.fields.Plugin<GraphiqlPluginProperties>;

    @ui.decorators.section<ReportTemplate>({
        isTitleHidden: true,
        title: 'Code',
        isHidden() {
            return !this.isExpertDocument.value;
        },
    })
    codeSection: ui.containers.Section;

    @ui.decorators.pluginField<ReportTemplate, MonacoPluginProperties>({
        parent() {
            return this.codeSection;
        },
        title: 'Code',
        isTitleHidden: true,
        helperText:
            'Javascript code block for post processing: Query result is available through "queryResponse" reference',
        isFullWidth: true,
        pluginPackage: '@sage/xtrem-ui-plugin-monaco',
        language: 'javascript',
        isReadOnly: true,
        handlebarsSuggestions: null,
        onChange() {
            this.schedulePreviewUpdate();
        },
    })
    code: ui.fields.Plugin<MonacoPluginProperties>;

    @ui.decorators.section<ReportTemplate>({
        isTitleHidden: true,
        title: 'HTML Template',
        isHidden() {
            return !this.isExpertDocument.value;
        },
        onActive() {
            this.formatHTML.isHidden = false;
        },
        onInactive() {
            this.formatHTML.isHidden = true;
        },
    })
    htmlTemplateSection: ui.containers.Section;

    @ui.decorators.block<ReportTemplate>({
        parent() {
            return this.htmlTemplateSection;
        },
    })
    htmlTemplateBlock: ui.containers.Block;

    @ui.decorators.pluginField<ReportTemplate, MonacoPluginProperties>({
        parent() {
            return this.htmlTemplateBlock;
        },
        title: 'Template',
        isTitleHidden: true,
        helperText: 'HTML source of the report',
        isFullWidth: true,
        pluginPackage: '@sage/xtrem-ui-plugin-monaco',
        language: 'handlebars',
        height: 600,
        isReadOnly: true,
        isMandatory() {
            return this.isExpertDocument.value;
        },
        onChange() {
            this.schedulePreviewUpdate();
        },
        handlebarsSuggestions: null,
    })
    externalHtmlTemplate: ui.fields.Plugin<MonacoPluginProperties>;

    @ui.decorators.section<ReportTemplate>({
        isTitleHidden: true,
        title: 'Template',
        isHidden() {
            return !!this.isExpertDocument.value;
        },
    })
    templateSection: ui.containers.Section;

    @ui.decorators.formDesignerField<ReportTemplate>({
        bind: 'externalHtmlTemplate',
        parent() {
            return this.templateSection;
        },
        onChange() {
            this.schedulePreviewUpdate();
        },
        onFooterValueChanged(newValue) {
            this.externalFooterHtmlTemplate.value = newValue;
            this.externalFooterHtmlTemplate.isDirty = true;
            this.schedulePreviewUpdate();
        },
        onHeaderValueChanged(newValue) {
            this.externalHeaderHtmlTemplate.value = newValue;
            this.externalHeaderHtmlTemplate.isDirty = true;
            this.schedulePreviewUpdate();
        },
    })
    template: ui.fields.FormDesigner;

    @ui.decorators.section<ReportTemplate>({
        isTitleHidden: true,
        title: 'Header & Footer',
        onActive() {
            this.formatHTML.isHidden = false;
        },
        onInactive() {
            this.formatHTML.isHidden = true;
        },
    })
    appendicesSection: ui.containers.Section;

    @ui.decorators.block<ReportTemplate>({
        parent() {
            return this.appendicesSection;
        },
        title: 'Header',
    })
    appendicesHeaderBlock: ui.containers.Block;

    @ui.decorators.block<ReportTemplate>({
        parent() {
            return this.appendicesSection;
        },
        title: 'Footer',
    })
    appendicesFooterBlock: ui.containers.Block;

    @ui.decorators.pluginField<ReportTemplate, MonacoPluginProperties>({
        parent() {
            return this.appendicesHeaderBlock;
        },
        title: 'Header',
        helperText: 'Header HTML',
        isFullWidth: true,
        isTitleHidden: true,
        pluginPackage: '@sage/xtrem-ui-plugin-monaco',
        language: 'handlebars',
        height: 250,
        isReadOnly: true,
        onChange() {
            this.schedulePreviewUpdate();
        },
        handlebarsSuggestions: null,
    })
    externalHeaderHtmlTemplate: ui.fields.Plugin<MonacoPluginProperties>;

    @ui.decorators.pluginField<ReportTemplate, MonacoPluginProperties>({
        parent() {
            return this.appendicesFooterBlock;
        },
        title: 'Footer',
        helperText: 'Footer HTML',
        isFullWidth: true,
        isTitleHidden: true,
        pluginPackage: '@sage/xtrem-ui-plugin-monaco',
        language: 'handlebars',
        height: 250,
        isReadOnly: true,
        onChange() {
            this.schedulePreviewUpdate();
        },
        handlebarsSuggestions: null,
    })
    externalFooterHtmlTemplate: ui.fields.Plugin<MonacoPluginProperties>;

    @ui.decorators.section<ReportTemplate>({
        isTitleHidden: true,
        title: 'Attachment',
        isHidden() {
            return !this.isExpertDocument.value;
        },
    })
    attachmentSection: ui.containers.Section;

    @ui.decorators.block<ReportTemplate>({
        parent() {
            return this.attachmentSection;
        },
    })
    attachmentBlock: ui.containers.Block;

    @ui.decorators.pluginField<ReportTemplate, MonacoPluginProperties>({
        parent() {
            return this.attachmentBlock;
        },
        title: 'Attachment Template',
        isTitleHidden: true,
        helperText: '',
        isFullWidth: true,
        pluginPackage: '@sage/xtrem-ui-plugin-monaco',
        language: 'handlebars',
        height: 600,
        onChange() {
            this.schedulePreviewUpdate();
        },
        handlebarsSuggestions: null,
    })
    attachmentTemplate: ui.fields.Plugin<MonacoPluginProperties>;

    @ui.decorators.textField<ReportTemplate>({
        parent() {
            return this.attachmentBlock;
        },
        title: 'Attachment name',
        helperText: 'Example: attachment.xml',
        validation() {
            if (this.attachmentTemplate.value && !this.attachmentName.value?.match(/(?:\.([^.]+))?$/)) {
                return ui.localize('@sage/xtrem-reporting/invalid-attachment-name', 'Invalid attachment file name');
            }
        },
    })
    attachmentName: ui.fields.Text;

    @ui.decorators.textField<ReportTemplate>({
        parent() {
            return this.attachmentBlock;
        },
        title: 'Attachment mimetype',
        helperText: 'Example: text/xml',
        validation() {
            if (this.attachmentTemplate.value && !this.attachmentMimeType.value?.match(/^[-\w.]+\/[-\w.]+$/)) {
                return ui.localize('@sage/xtrem-reporting/invalid-attachment-mimetype', 'Invalid attachment MIME type');
            }
        },
    })
    attachmentMimeType: ui.fields.Text;

    @ui.decorators.section<ReportTemplate>({
        isTitleHidden: true,
        title: 'Style Sheet',
        isHidden() {
            return !this.isExpertDocument.value;
        },
    })
    styleSheetSection: ui.containers.Section;

    @ui.decorators.pluginField<ReportTemplate, MonacoPluginProperties>({
        parent() {
            return this.styleSheetSection;
        },
        title: 'Stylesheet',
        isTitleHidden: true,
        helperText: 'CSS style sheet',
        isFullWidth: true,
        pluginPackage: '@sage/xtrem-ui-plugin-monaco',
        language: 'css',
        onChange() {
            if (this.styleSheet.value === null) {
                this.styleSheet.value = ' ';
            }
            this.schedulePreviewUpdate();
        },
    })
    styleSheet: ui.fields.Plugin<MonacoPluginProperties>;

    @ui.decorators.section<ReportTemplate>({
        isTitleHidden: true,
        title: 'Translations',
    })
    translationSection: ui.containers.Section;

    @ui.decorators.tableField<ReportTemplate, ReportTranslatableText>({
        title: 'Translations',
        node: '@sage/xtrem-reporting/ReportTranslatableText',
        filter() {
            return { isBaseLocale: false };
        },
        parent() {
            return this.translationSection;
        },
        orderBy: {
            locale: 1,
            _id: 1,
        },
        onChange() {
            this.schedulePreviewUpdate();
        },
        canSelect: false,
        canFilter: true,
        dropdownActions: [
            {
                title: 'Lookup',
                icon: 'search',
                isDisabled() {
                    return !this.baseLocale.value || !this._id.value;
                },
                async onClick(recordId, row) {
                    this.translationSuggestion.filter = {
                        hash: row.hash,
                        locale: row.locale,
                        text: { _ne: '' } as any,
                        originalText: { locale: this.baseLocale.value as any },
                        reportTemplate: { _id: { _ne: this._id.value } },
                    };
                    const suggestions = await this.translationSuggestion.fetchSuggestions('');
                    if (suggestions.length === 0) {
                        this.$.showToast('No suggestions were found.', {
                            type: 'info',
                        });
                    } else if (suggestions.length === 1) {
                        this.translatableTexts.setRecordValue({ ...row, text: suggestions[0].text } as any);
                        this.$.showToast('Suggestion is applied!', {
                            type: 'success',
                        });
                    } else {
                        this.translationIdForLookupDialog = recordId;
                        this.translationSuggestion.openDialog();
                    }
                },
            },
        ],
        columns: [
            ui.nestedFields.select({
                bind: 'locale',
                title: 'Target Language',
                isReadOnly: true,
                optionType: '@sage/xtrem-reporting/ReportLocale',
            }),
            ui.nestedFields.reference({
                node: '@sage/xtrem-reporting/ReportTranslatableText',
                bind: 'originalText',
                valueField: 'text',
                title: 'Source',
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                bind: 'text',
                title: 'Target',
            }),
            ui.nestedFields.text({
                bind: 'hash',
                isHidden: true,
            }),
        ],
    })
    translatableTexts: ui.fields.Table<ReportTranslatableText>;

    @ui.decorators.referenceField<ReportTemplate, ReportTranslatableText>({
        parent() {
            return this.detailPanelHeaderBlock;
        },
        isHidden: true,
        isTransient: true,
        title: 'Suggestion',
        node: '@sage/xtrem-reporting/ReportTranslatableText',
        valueField: 'text',
        helperTextField: 'hash',
        onChange() {
            if (this.translationIdForLookupDialog) {
                this.translatableTexts.setRecordValue({
                    _id: this.translationIdForLookupDialog,
                    text: this.translationSuggestion.value.text,
                });
                this.translationIdForLookupDialog = null;
                this.translationSuggestion.value = null;
            }
        },
        columns: [
            ui.nestedFields.text({
                bind: 'text',
                title: 'Text',
            }),
            ui.nestedFields.date({
                title: 'Last modified',
                bind: '_updateStamp',
            }),
            ui.nestedFields.reference<ReportTemplate, ReportTranslatableText, ReportTemplateApi>({
                bind: 'reportTemplate',
                title: 'Report',
                node: '@sage/xtrem-reporting/ReportTemplate',
                valueField: 'name',
            }),
            ui.nestedFields.reference<ReportTemplate, ReportTranslatableText, ReportTemplateApi>({
                bind: 'reportTemplate',
                title: 'Package',
                node: '@sage/xtrem-reporting/ReportTemplate',
                valueField: { report: { parentPackage: true } },
            }),
        ],
    })
    translationSuggestion: ui.fields.Reference<ReportTranslatableText>;

    async initPage() {
        const isFactory = this.isFactory.value === true;
        const isListReport = !!this.reportWizard.value;
        const isNewPage = this.$.isNewPage;
        this.translatableTexts.isReadOnly = isListReport;
        this.name.isReadOnly = isFactory || isListReport;
        this.report.isDisabled = isFactory || isListReport;
        this.baseLocale.isDisabled = isFactory || this.translatableTexts.value.length > 0 || isListReport;
        this.$standardDeleteAction.isDisabled = isFactory || isNewPage;
        this.$standardSaveAction.isDisabled = isFactory;
        this.template.isDisabled = isFactory || isListReport;
        this.externalHtmlTemplate.isReadOnly = isFactory || isListReport;
        this.externalHeaderHtmlTemplate.isReadOnly = isFactory || isListReport;
        this.externalFooterHtmlTemplate.isReadOnly = isFactory || isListReport;
        this.convertToHtml.isHidden = isFactory || this.isExpertDocument.value || isListReport;
        this.editViaWizard.isHidden = isFactory || this.isExpertDocument.value || !isListReport;
        this.convertToForm.isHidden = isFactory || this.isExpertDocument.value || !isListReport;
        this.attachmentTemplate.isReadOnly = isFactory || isListReport;
        this.attachmentName.isReadOnly = isFactory || isListReport;
        this.attachmentMimeType.isReadOnly = isFactory || isListReport;
        this.query.isReadOnly = isFactory || isListReport;
        this.code.isReadOnly = isFactory || isListReport;
        this.styleSheet.isReadOnly = isFactory || isListReport;
        this.externalHtmlTemplate.value = await prettify(this.externalHtmlTemplate.value || '', 'html');
        this.externalHeaderHtmlTemplate.value = await prettify(this.externalHeaderHtmlTemplate.value || '', 'html');
        this.externalFooterHtmlTemplate.value = await prettify(this.externalFooterHtmlTemplate.value || '', 'html');
        this.pdfPreview.value = null;
        this.pdfDialogPreview.value = null;
        this.template.headerValue = this.externalHeaderHtmlTemplate.value || '';
        this.template.footerValue = this.externalFooterHtmlTemplate.value || '';

        this.previewPaperFormat.value = this.defaultPaperFormat.value;
        this.previewPageOrientation.value = this.defaultPageOrientation.value;
        this.previewBottomMargin.value = this.convertToDisplayUnit(this.defaultBottomMargin.value);
        this.previewTopMargin.value = this.convertToDisplayUnit(this.defaultTopMargin.value);
        this.previewLeftMargin.value = this.convertToDisplayUnit(this.defaultLeftMargin.value);
        this.previewRightMargin.value = this.convertToDisplayUnit(this.defaultRightMargin.value);
        this.template.marginLeft = Number(this.defaultLeftMargin.value);
        this.template.marginRight = Number(this.defaultRightMargin.value);
        this.template.marginTop = Number(this.defaultTopMargin.value);
        this.template.marginBottom = Number(this.defaultBottomMargin.value);

        if (this.$.locale === 'en-US') {
            this.defaultLeftMargin.value = this.convertToDisplayUnit(this.defaultLeftMargin.value);
            this.defaultRightMargin.value = this.convertToDisplayUnit(this.defaultRightMargin.value);
            this.defaultTopMargin.value = this.convertToDisplayUnit(this.defaultTopMargin.value);
            this.defaultBottomMargin.value = this.convertToDisplayUnit(this.defaultBottomMargin.value);
        }

        if (!this.baseLocale.value) {
            this.baseLocale.value = this.$.locale.replace('-', '_');
        }

        this.template.paperSize = this.defaultPaperFormat.value as PaperSize;

        this.previewLocale.value = this.baseLocale.value;
        if (this.name.value) {
            this.$.page.title = this.name.value;
        }

        this.reportParameters.value = {};
        this.updateTypeDependentElementVisibility();
    }

    async getSuggestionsFromQuery() {
        if (this.query.value) {
            const schema = await this.$.graph.graph
                .node('@sage/xtrem-reporting/ReportTemplate')
                .queries.getSchema()
                .execute();

            this.externalHtmlTemplate.setProperty(
                'handlebarsSuggestions',
                extractSuggestionsFromGraphQLQuery(this.query.value, JSON.parse(schema)),
            );
        }
    }

    @ui.decorators.pageAction<ReportTemplate>({
        title: 'Edit',
        buttonType: 'secondary',
        async onClick() {
            try {
                await this.$.dialog.page(
                    '@sage/xtrem-reporting/ListReportCreation',
                    { _id: this.report.value._id, templateId: this._id.value },
                    { resolveOnCancel: true, height: 500, size: 'extra-large' },
                );
            } catch (error) {
                console.log(error);
            }
        },
        isHidden() {
            return !this.reportWizard.value || this.$.isDirty;
        },
    })
    editViaWizard: ui.PageAction;

    @ui.decorators.section<ReportTemplate>({
        title: 'Document Preview',
        isTitleHidden: true,
        isHidden: true,
    })
    previewDialogSection: ui.containers.Section;

    @ui.decorators.previewField<ReportTemplate>({
        parent() {
            return this.previewDialogSection;
        },
        isFullWidth: true,
        isTransient: true,
        mimeType: 'application/pdf',
        filename: 'preview.pdf',
        canZoom: true,
        isFilenameHidden: true,
    })
    pdfDialogPreview: ui.fields.Preview;
}
