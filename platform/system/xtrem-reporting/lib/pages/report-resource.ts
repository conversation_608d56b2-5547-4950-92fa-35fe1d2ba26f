import { GraphApi, ReportResource as ReportResourceApi } from '@sage/xtrem-reporting-api';
import { Dict } from '@sage/xtrem-shared';
import {
    setApplicativePageCrudActions,
    setOrderOfPageHeaderDropDownActions,
} from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import * as ui from '@sage/xtrem-ui';
import { reporting } from '../menu-items/reporting';

const mimetypeMap: Dict<string> = {
    fontType:
        'image/svg+xml,application/x-font-ttf,application/x-font-truetype,application/x-font-opentype,application/font-woff,application/font-woff2,application/vnd.ms-fontobject,application/font-sfnt',
    image: 'image/*',
};

@ui.decorators.page<ReportResource, ReportResourceApi>({
    module: '',
    title: 'Resources',
    objectTypeSingular: 'Report Resource',
    objectTypePlural: 'Report Resources',
    idField() {
        return this.name;
    },
    node: '@sage/xtrem-reporting/ReportResource',
    menuItem: reporting,
    createAction() {
        return this.$standardNewAction;
    },
    headerDropDownActions() {
        return setOrderOfPageHeaderDropDownActions({
            remove: [this.$standardDeleteAction],
            dropDownBusinessActions: [this.$standardOpenRecordHistoryAction],
        });
    },
    headerQuickActions() {
        return [this.$standardDuplicateAction];
    },
    businessActions() {
        return [this.$standardCancelAction, this.$standardSaveAction];
    },
    onLoad() {
        this.$standardDeleteAction.isDisabled = !!this.isFactory.value;
        this.name.isReadOnly = !!this.isFactory.value;
        if (this.type.value) {
            this.content.fileTypes = mimetypeMap[this.type.value];
            this.content.isDisabled = false;
        }

        setApplicativePageCrudActions({
            page: this,
            isDirty: false,
            save: this.$standardSaveAction,
            cancel: this.$standardCancelAction,
            duplicate: this.$standardDuplicateAction,
            remove: this.$standardDeleteAction,
        });

        const isFactory = this.isFactory.value;
        this.$standardDeleteAction.isDisabled = this.isFactory.value;
        this.name.isDisabled = isFactory;
        this.description.isDisabled = isFactory;
        this.type.isDisabled = isFactory;
    },
    navigationPanel: {
        listItem: {
            titleRight: ui.nestedFields.icon({
                bind: '_id',
                title: 'ID',
                map: () => {
                    return 'factory';
                },
                isHidden: (value, rowData) => {
                    if (rowData) {
                        return !rowData.isFactory;
                    }
                },
            }),
            title: ui.nestedFields.text({ bind: 'name' }),
            line2: ui.nestedFields.text({ bind: 'description' }),
            line3: ui.nestedFields.checkbox<ReportResource, ReportResourceApi>({
                bind: 'isFactory',
                title: 'Default',
            }),
        },
    },
    onDirtyStateUpdated(isDirty: boolean) {
        setApplicativePageCrudActions({
            page: this,
            isDirty,
            save: this.$standardSaveAction,
            cancel: this.$standardCancelAction,
            duplicate: this.$standardDuplicateAction,
            remove: this.$standardDeleteAction,
        });
        const isFactory = this.isFactory.value;
        this.$standardDeleteAction.isDisabled = isFactory;
    },
})
export class ReportResource extends ui.Page<GraphApi, ReportResourceApi> {
    @ui.decorators.section<ReportResource>({
        isTitleHidden: true,
    })
    mainSection: ui.containers.Section;

    @ui.decorators.block<ReportResource>({
        parent() {
            return this.mainSection;
        },
    })
    mainBlock: ui.containers.Block;

    @ui.decorators.textField<ReportResource>({
        parent() {
            return this.mainBlock;
        },
        title: 'Name',
        isMandatory: true,
    })
    name: ui.fields.Text;

    @ui.decorators.textField<ReportResource>({
        parent() {
            return this.mainBlock;
        },
        isHidden: true,
    })
    mimetype: ui.fields.Text;

    @ui.decorators.selectField<ReportResource>({
        parent() {
            return this.mainBlock;
        },
        optionType: '@sage/xtrem-reporting/ReportResourceType',
        title: 'Resource type',
        isMandatory: true,
        onChange() {
            this.content.fileTypes = mimetypeMap[this.type.value];
            this.content.isDisabled = false;
        },
    })
    type: ui.fields.Select;

    @ui.decorators.checkboxField<ReportResource>({
        parent() {
            return this.mainBlock;
        },
        title: 'Is factory',
        helperText: 'Indicates if report is factory provided',
        isReadOnly: true,
    })
    isFactory: ui.fields.Checkbox;

    @ui.decorators.textAreaField<ReportResource>({
        parent() {
            return this.mainBlock;
        },
        isFullWidth: true,
        rows: 4,
        title: 'Description',
        isMandatory: true,
    })
    description: ui.fields.TextArea;

    @ui.decorators.fileField<ReportResource>({
        parent() {
            return this.mainBlock;
        },
        onFileInfo(mimetype) {
            this.mimetype.value = mimetype;
        },
        onChange() {
            if (!this.content.value) {
                this.mimetype.value = null;
            }
        },
        title: 'Value',
        text: 'File',
        isMandatory: true,
        isDisabled: true,
    })
    content: ui.fields.File;
}
