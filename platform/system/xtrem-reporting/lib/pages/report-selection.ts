import { GraphApi, Report, TemplateType } from '@sage/xtrem-reporting-api';
import * as ui from '@sage/xtrem-ui';
import { reportOptionsDescription, reportOptionsValues } from '../client-functions/messages';

@ui.decorators.page<ReportSelection, Report>({
    title: 'Create a report',
    subtitle: 'Choose a template style to match your needs.',
    isTransient: true,
    onLoad() {
        if (this.$.queryParameters.report) {
            this.report = this.$.queryParameters.report as string;
        }
    },
})
export class ReportSelection extends ui.Page<GraphApi> {
    private report: string;

    @ui.decorators.section<ReportSelection>({
        isTitleHidden: true,
    })
    mainSection: ui.containers.Section;

    @ui.decorators.block<ReportSelection>({
        parent() {
            return this.mainSection;
        },
    })
    mainBlock: ui.containers.Block;

    @ui.decorators.selectionCard<ReportSelection>({
        icon(option: TemplateType) {
            switch (option) {
                case 'form':
                    return 'page';
                case 'advanced':
                    return 'globe';
                case 'list':
                default:
                    return 'table';
            }
        },
        description(option: TemplateType) {
            return reportOptionsDescription(option);
        },
        map(option: TemplateType) {
            return reportOptionsValues(option);
        },
        parent() {
            return this.mainBlock;
        },
        optionType: '@sage/xtrem-reporting/TemplateType',
        async onChange() {
            this.$.setPageClean();
            let closeDialog = false;
            switch (this.mode.value) {
                case 'form':
                case 'advanced':
                    await this.$.router.goTo('@sage/xtrem-reporting/ReportTemplate', {
                        isExpertDocument: this.mode.value === 'advanced',
                        _id: '$new',
                        report: this.report,
                    });
                    break;
                case 'list':
                default:
                    const fromReport = !!this.$.queryParameters.fromReport;
                    const reportId = this.report ? JSON.parse(this.report)._id : '$new';
                    await this.$.dialog.page(
                        '@sage/xtrem-reporting/ListReportCreation',
                        { _id: reportId, report: this.report, fromReport },
                        { resolveOnCancel: true, height: 500, size: 'extra-large' },
                    );
                    closeDialog = fromReport;
                    break;
            }
            if (closeDialog) {
                this.$.finish();
            }
        },
    })
    mode: ui.fields.SelectionCard;
}
