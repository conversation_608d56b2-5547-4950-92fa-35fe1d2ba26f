import type { ExtractEdges } from '@sage/xtrem-client';
import { extractEdges } from '@sage/xtrem-client';
import type { DocumentEditorInsertDialogResult, InsertQueryTableCommandArgument } from '@sage/xtrem-document-editor';
import { MetaDataType, MetaNodeFactory } from '@sage/xtrem-metadata-api';
import { GraphApi, Report, ReportTemplate, ReportVariable, ReportWizard } from '@sage/xtrem-reporting-api';
import { Dict } from '@sage/xtrem-shared';
import {
    addParametersToDynamicPod,
    type DynamicParameter,
} from '@sage/xtrem-system/build/lib/client-functions/dynamic-pod-helper';
import * as ui from '@sage/xtrem-ui';
import { camelCase, kebabCase } from 'lodash';
import {
    ReportContent,
    SelectedProperty,
    convertContentTableValueToAggregationProperty,
    convertContentTableValueToOrderByProperty,
    convertParameterTypeToFieldType,
    convertSelectedPropertiesToContent,
    convertSelectedPropertiesToDataModelProperty,
    getDisplayUnit,
    removeExtractEdgesPartial,
} from '../client-functions/list-report-utils';

@ui.decorators.page<ListReportCreation, Report>({
    title: 'Create a template',
    subtitle: 'Choose a template style to match your needs.',
    mode: 'wizard',
    node: '@sage/xtrem-reporting/Report',
    async onLoad() {
        this.fromReport = !!this.$.queryParameters.fromReport;
        const packageNames = await this.$.graph.raw('{__type(name: "RootQueryType") {fields{name}}}');
        this.parentPackage.options = packageNames.__type.fields.map((t: { name: string }) => kebabCase(t.name));
        if (this._id.value && this.$.queryParameters && this.$.queryParameters.templateId) {
            this.templateId = this.$.queryParameters.templateId.toString();
            const reportTemplate: ReportTemplate = this.reportTemplates.value.find(
                template => template._id === this.templateId,
            ) as unknown as ReportTemplate;
            this.loadFields(reportTemplate);
            this.selectedProperties.focus();
        } else {
            this.parameters.value = {};
            this.loadFields();
            if (this.$.queryParameters.report) {
                const report = JSON.parse(this.$.queryParameters.report as string);
                if (report) {
                    this.name.value = report.name;
                    this.description.value = report.description;
                    this.report.value = report;
                }
            }
        }
    },
    async onClose(isWizardCompleted) {
        if (isWizardCompleted) {
            const errors = await this.$.page.validate();
            if (errors.length > 0) {
                return;
            }
            const reportSettings = {
                locale: this.previewLocale.value || 'en-US',
                paperFormat: this.previewPaperFormat.value || 'a4',
                pageOrientation: this.previewPageOrientation.value || 'portrait',
                leftMarginCm: this.previewLeftMargin.value || 2,
                rightMarginCm: this.previewRightMargin.value || 2,
                topMarginCm: this.previewTopMargin.value || 2,
                bottomMarginCm: this.previewBottomMargin.value || 2,
                isDefaultHeaderFooter: this.isDefaultHeaderFooter.value,
            };
            const reportIds = await this.$.graph
                .node('@sage/xtrem-reporting/Report')
                .mutations.createOrUpdateReport(
                    { reportId: true, reportTemplateId: true },
                    {
                        data: {
                            _id: this._id.value ?? this.report.value?._id ?? null,
                            name: this.name.value,
                            templateId: this.templateId,
                            templateName: this.templateName.value ?? null,
                            description: this.description.value,
                            parentPackage: this.parentPackage.value,
                            externalHtmlTemplate: this.template.value,
                            dataSource: this.dataEntity.value._id,
                            selectedProperties: JSON.stringify(this.selectedProperties.value),
                            parameters: JSON.stringify(this.parameters.value),
                            filters: JSON.stringify(this.filters.value),
                            content: JSON.stringify(this.content.value),
                            variables: JSON.stringify(this.variables.value),
                            reportSettings,
                        },
                    },
                )
                .execute();
            this.$.setPageClean();
            if (!this.fromReport) {
                this.$.router.goTo(`@sage/xtrem-reporting/Report`, { _id: reportIds.reportId });
            }
        }
    },
})
export class ListReportCreation extends ui.Page<GraphApi, Report> {
    private fromReport = false;
    private templateId: string | null = null;

    private async loadFields(reportTemplate?: ReportTemplate) {
        if (reportTemplate) {
            const reportWizard: ReportWizard = reportTemplate.reportWizard;
            this.dataEntity.value = removeExtractEdgesPartial(reportWizard.dataSource);
            const formattedNodeName = `${this.dataEntity.value.package.name}/${this.dataEntity.value.name}`;
            this.templateName.value = reportTemplate.name;
            this.filters.node = formattedNodeName;
            this.selectedProperties.node = formattedNodeName;
            this.selectedProperties.value = JSON.parse(reportWizard.selectedProperties);
            this.content.node = formattedNodeName;
            this.filters.selectedProperties = this.selectedProperties.value;
            this.content.selectedProperties = this.selectedProperties.value;
            this.content.value = JSON.parse(reportWizard.content);
            this.parentPackage.value = this.dataEntity.value.package.name.replace('@sage/', '');
            this.filters.value = JSON.parse(reportWizard.filters);
            this.parameters.value = JSON.parse(reportWizard.parameters);
            this.previewPaperFormat.value = reportTemplate.defaultPaperFormat;
            this.previewPageOrientation.value = reportTemplate.defaultPageOrientation;
            this.previewLocale.value = reportTemplate.baseLocale;
            this.previewTopMargin.value = Number(reportTemplate.defaultTopMargin);
            this.previewBottomMargin.value = Number(reportTemplate.defaultBottomMargin);
            this.previewLeftMargin.value = Number(reportTemplate.defaultLeftMargin);
            this.previewRightMargin.value = Number(reportTemplate.defaultRightMargin);
            this.isDefaultHeaderFooter.value = reportTemplate.isDefaultHeaderFooter;
        } else {
            this.parameters.value = {};
            this.previewPaperFormat.value = 'a4';
            this.previewPageOrientation.value = 'portrait';
            this.previewLocale.value = 'en_US';
            this.previewTopMargin.value = 2;
            this.previewBottomMargin.value = 2;
            this.previewLeftMargin.value = 2;
            this.previewRightMargin.value = 2;
            this.isDefaultHeaderFooter.value = false;
        }
    }

    private async updatePreview() {
        if (!this.template.value?.value) {
            return;
        }

        this.pdfPreview.isLoading = true;
        const result = await this.$.graph
            .node('@sage/xtrem-reporting/ReportTemplate')
            .mutations.generateWizardPreview(
                { value: true },
                {
                    template: this.template.value?.value,
                    reportSettings: {
                        locale: this.previewLocale.value || 'en_US',
                        paperFormat: this.previewPaperFormat.value || this.parameters.value?.paperFormat || 'a4',
                        variables: JSON.stringify(this.parameters.value || {}),
                        pageOrientation: this.previewPageOrientation.value || 'portrait',
                        leftMarginCm: this.previewLeftMargin.value || 2,
                        rightMarginCm: this.previewRightMargin.value || 2,
                        topMarginCm: this.previewTopMargin.value || 2,
                        bottomMarginCm: this.previewBottomMargin.value || 2,
                        isDefaultHeaderFooter: this.isDefaultHeaderFooter.value,
                        reportName: this.name.value,
                    },
                },
            )
            .execute();
        this.pdfPreview.value = result;
        this.pdfPreview.isLoading = false;
    }

    @ui.decorators.textField<ListReportCreation>({
        parent() {
            return this.previewBlock;
        },
        title: '_id',
        isHidden: true,
    })
    _id: ui.fields.Text;

    @ui.decorators.selectField<ListReportCreation>({
        parent() {
            return this.previewBlock;
        },
        title: 'Parent package',
        isHidden: true,
    })
    parentPackage: ui.fields.Select;

    @ui.decorators.tableField<ListReportCreation, ReportVariable>({
        title: 'Variables',
        isHidden: true,
        node: '@sage/xtrem-reporting/ReportVariable',
        parent() {
            return this.previewBlock;
        },
        columns: [
            ui.nestedFields.technical({ bind: 'name' }),
            ui.nestedFields.technical({ bind: 'title' }),
            ui.nestedFields.technical({ bind: 'type' }),
            ui.nestedFields.reference({
                bind: 'dataType',
                node: '@sage/xtrem-metadata/MetaDataType',
                valueField: 'title',
                isHidden: true,
            }),
        ],
    })
    variables: ui.fields.Table<ReportVariable>;

    @ui.decorators.referenceField<ListReportCreation, Report>({
        title: 'Report',
        isTransient: true,
        isHidden: true,
        parent() {
            return this.previewBlock;
        },
        node: '@sage/xtrem-reporting/Report',
        valueField: 'name',
        columns: [ui.nestedFields.technical({ bind: 'name' }), ui.nestedFields.technical({ bind: 'description' })],
    })
    report: ui.fields.Reference<MetaDataType>;

    @ui.decorators.referenceField<ListReportCreation, MetaDataType>({
        title: 'Data type',
        isFullWidth: true,
        isTransient: true,
        isHidden: true,
        parent() {
            return this.previewBlock;
        },
        node: '@sage/xtrem-metadata/MetaDataType',
        filter: { type: 'reference' },
        valueField: 'title',
    })
    newVariableDataType: ui.fields.Reference<MetaDataType>;

    @ui.decorators.tableField<ListReportCreation, ReportTemplate>({
        title: 'Report templates',
        isHidden: true,
        node: '@sage/xtrem-reporting/ReportTemplate',
        parent() {
            return this.previewBlock;
        },
        columns: [
            ui.nestedFields.technical({ bind: '_id' }),
            ui.nestedFields.technical({ bind: 'name' }),
            ui.nestedFields.technical({ bind: 'isExpertDocument' }),
            ui.nestedFields.technical({ bind: 'reportType' }),
            ui.nestedFields.technical({ bind: 'isFactory' }),
            ui.nestedFields.technical({ bind: 'defaultPaperFormat' }),
            ui.nestedFields.technical({ bind: 'defaultPageOrientation' }),
            ui.nestedFields.technical({ bind: 'defaultLeftMargin' }),
            ui.nestedFields.technical({ bind: 'defaultRightMargin' }),
            ui.nestedFields.technical({ bind: 'defaultTopMargin' }),
            ui.nestedFields.technical({ bind: 'defaultBottomMargin' }),
            ui.nestedFields.technical({ bind: 'baseLocale' }),
            ui.nestedFields.technical({ bind: 'isDefaultHeaderFooter' }),
            ui.nestedFields.technical({
                bind: 'reportWizard',
                nestedFields: [
                    ui.nestedFields.technical({ bind: '_id' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'description' }),
                    ui.nestedFields.technical({
                        bind: 'dataSource',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: '_id' }),
                            ui.nestedFields.technical({ bind: 'title' }),
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: { package: { name: true } } }),
                        ],
                    }),
                    ui.nestedFields.technical({ bind: 'content' }),
                    ui.nestedFields.technical({ bind: 'filters' }),
                    ui.nestedFields.technical({ bind: 'parameters' }),
                    ui.nestedFields.technical({ bind: 'selectedProperties' }),
                    ui.nestedFields.technical({ bind: 'templateType' }),
                ],
            }),
        ],
    })
    reportTemplates: ui.fields.Table<ReportTemplate>;

    @ui.decorators.section<ListReportCreation>({
        isTitleHidden: true,
        title: 'Data',
    })
    propertiesSection: ui.containers.Section;

    @ui.decorators.block<ListReportCreation>({
        parent() {
            return this.propertiesSection;
        },
        title: 'Build your data template with nested references.',
    })
    propertiesBlock: ui.containers.Block;

    @ui.decorators.referenceField<ListReportCreation, MetaNodeFactory>({
        parent() {
            return this.propertiesBlock;
        },
        orderBy: {
            name: 1,
        },
        columns: [
            ui.nestedFields.text({ bind: 'title', title: 'Record type' }),
            ui.nestedFields.technical({ bind: 'name' }),
            ui.nestedFields.technical({ bind: { package: { name: true } } }),
        ],
        isMandatory: true,
        node: '@sage/xtrem-metadata/MetaNodeFactory',
        title: 'Data source',
        isTransient: true,
        isFullWidth: true,
        valueField: 'title',
        minLookupCharacters: 0,
        onChange() {
            if (this.dataEntity.value) {
                const formattedNodeName = `${this.dataEntity.value.package.name}/${this.dataEntity.value.name}`;
                this.filters.node = formattedNodeName;
                this.selectedProperties.node = formattedNodeName;
                this.content.node = formattedNodeName;
                this.parentPackage.value = this.dataEntity.value.package.name.replace('@sage/', '');
            } else {
                this.selectedProperties.node = null;
                this.selectedProperties.value = null;
                this.parentPackage.value = null;
                this.filters.value = null;
            }
            this.parameters.value = {};
            // If the selected node changes, we reset the values
            this.template.value = { value: '' };
            this.filters.value = { filters: [], parameters: [] };
        },
    })
    dataEntity: ui.fields.Reference<MetaNodeFactory>;

    @ui.decorators.nodeBrowserTreeField<ListReportCreation>({
        title: 'Properties',
        isTitleHidden: true,
        isTransient: true,
        parent() {
            return this.propertiesBlock;
        },
        onChange() {
            this.template.value = { value: '' };
            this.filters.selectedProperties = this.selectedProperties.value;
            this.content.selectedProperties = this.selectedProperties.value;
            this.content.value = Object.values(this.selectedProperties.value).map(property => {
                return convertSelectedPropertiesToContent(property);
            });
        },
    })
    selectedProperties: ui.fields.NodeBrowserTree;

    @ui.decorators.section<ListReportCreation>({
        isTitleHidden: true,
        title: 'Content',
    })
    contentSection: ui.containers.Section;

    @ui.decorators.block<ListReportCreation>({
        parent() {
            return this.contentSection;
        },
    })
    contentBlock: ui.containers.Block;

    @ui.decorators.contentTable<ListReportCreation>({
        parent() {
            return this.contentBlock;
        },
        isFullWidth: true,
        isTransient: true,
        node: '@sage/xtrem-reporting/Report',
        onChange() {
            const selectedProperties: Dict<SelectedProperty> = this.selectedProperties.value;
            this.selectedProperties.value = {};

            this.content.value.forEach((element: ReportContent) => {
                selectedProperties[element.property.id].label = element.title;
                this.selectedProperties.value[element.property.id] = selectedProperties[element.property.id];
            });
        },
    })
    content: ui.fields.ContentTable;

    @ui.decorators.section<ListReportCreation>({
        isTitleHidden: true,
        title: 'Filters',
    })
    filterSection: ui.containers.Section;

    @ui.decorators.block<ListReportCreation>({
        parent() {
            return this.filterSection;
        },
    })
    filterBlock: ui.containers.Block;

    @ui.decorators.filterEditorField<ListReportCreation>({
        parent() {
            return this.filterBlock;
        },
        isFullWidth: true,
        isTransient: true,
        async onChange() {
            this.parameters.value = {};
            this.template.value = { value: '' };
            this.variables.value = [];
            const parameters = this.filters.value?.parameters.map<ExtractEdges<DynamicParameter>>(p => {
                return {
                    _id: p.name,
                    bind: p.name,
                    title: p.label || p.name,
                    name: p.name,
                    type: convertParameterTypeToFieldType(p.type),
                };
            });
            addParametersToDynamicPod({
                isFullWidth: true,
                parameters,
                dynamicPod: this.parameters,
                locale: this.$.locale,
            });

            parameters.map(parameter => {
                this.variables.addRecord({
                    name: parameter.name,
                    type: parameter.type,
                    title: parameter.title,
                    dataType: this.newVariableDataType.value,
                });
            });
        },
    })
    filters: ui.fields.FilterEditor;

    @ui.decorators.section<ListReportCreation>({
        isTitleHidden: true,
        title: 'Preview',
        onActive() {
            this.pdfPreview.isLoading = true;
            this.pdfPreview.defaultZoomLevel = 0.5;
        },
    })
    previewSection: ui.containers.Section;

    @ui.decorators.block<ListReportCreation>({
        parent() {
            return this.previewSection;
        },
        width: 'small',
    })
    previewBlock: ui.containers.Block;

    @ui.decorators.block<ListReportCreation>({
        parent() {
            return this.previewSection;
        },
        width: 'small',
    })
    parametersBlock: ui.containers.Block;

    @ui.decorators.formDesignerField<ListReportCreation>({
        isHidden: true,
        isTransient: true,
        parent() {
            return this.previewBlock;
        },
        async onChange() {
            await this.updatePreview();
        },
        onReady() {
            const trimmedPackageName = camelCase(this.dataEntity.value.package.name?.split('/')[1]);
            const camelCaseNodeName = camelCase(this.dataEntity.value.name);
            const selectedFields = convertSelectedPropertiesToDataModelProperty(
                this.selectedProperties.value,
                trimmedPackageName,
            );
            const tableConfiguration: DocumentEditorInsertDialogResult = {
                selectedMode: 'list',
                aggregations: convertContentTableValueToAggregationProperty(this.content.value),
                selectedFields,
                filters: this.filters.value?.filters,
                orderBy: convertContentTableValueToOrderByProperty(this.content.value),
                collectionType: {},
            };
            const commandArgument: InsertQueryTableCommandArgument = {
                lookupResult: tableConfiguration,
                selectedItem: {
                    key: camelCaseNodeName,
                    id: this.dataEntity.value.name,
                    label: this.dataEntity.value.title,
                    labelKey: this.dataEntity.value.title,
                    labelPath: this.dataEntity.value.title,
                    data: {
                        namespace: trimmedPackageName,
                        type: this.dataEntity.value.name,
                        canFilter: true,
                        kind: 'OBJECT',
                        name: this.dataEntity.value.title,
                        canSort: true,
                        label: this.dataEntity.value.title,
                        isStored: true,
                        isOnInputType: true,
                        isOnOutputType: true,
                        dataType: 'OBJECT',
                        targetNode: '',
                        enumType: '',
                        isCustom: false,
                        isMutable: false,
                    },
                },
                remapInfo: { path: `${trimmedPackageName}.${camelCaseNodeName}.query.edges`, subPath: 'node' },
            };
            this.template.executeEditorCommand('queryTableInsert', commandArgument);
        },
    })
    template: ui.fields.FormDesigner;

    @ui.decorators.previewField<ListReportCreation>({
        parent() {
            return this.previewBlock;
        },
        width: 'half',
        height: 420,
        defaultZoomLevel: 0.5,
        isTransient: true,
        isTitleHidden: true,
        canZoom: true,
        mimeType: 'application/pdf',
        filename: 'preview.pdf',
        isFilenameHidden: true,
    })
    pdfPreview: ui.fields.Preview;

    @ui.decorators.textField<ListReportCreation>({
        parent() {
            return this.parametersBlock;
        },
        title: 'Name',
        isMandatory: true,
        isReadOnly() {
            return !!this._id.value;
        },
        async onChange() {
            if (this.isDefaultHeaderFooter.value) this.updatePreview();
        },
        async validation(value) {
            if (!this.name.isReadOnly && value) {
                const result = extractEdges(
                    await this.$.graph
                        .node('@sage/xtrem-reporting/Report')
                        .query(
                            ui.queryUtils.edgesSelector(
                                {
                                    _id: true,
                                },
                                {
                                    filter: {
                                        name: { _eq: value },
                                    },
                                },
                            ),
                        )
                        .execute(),
                );
                return result.length === 0
                    ? undefined
                    : ui.localize(
                          '@sage/xtrem-reporting/pages__list-report-creation__duplicate_report_name',
                          'Report name already exists',
                      );
            }
            return undefined;
        },
    })
    name: ui.fields.Text;

    @ui.decorators.textField<ListReportCreation>({
        parent() {
            return this.parametersBlock;
        },
        title: 'Template name',
        isTransient: true,
        isMandatory() {
            return !!this._id.value;
        },
        isReadOnly() {
            return !!this.templateId;
        },
        isHidden() {
            return !this._id.value;
        },
        async validation(value) {
            if (!this.templateName.isReadOnly && value) {
                const result = extractEdges(
                    await this.$.graph
                        .node('@sage/xtrem-reporting/ReportTemplate')
                        .query(
                            ui.queryUtils.edgesSelector(
                                {
                                    _id: true,
                                },
                                {
                                    filter: {
                                        name: { _eq: value },
                                    },
                                },
                            ),
                        )
                        .execute(),
                );
                return result.length === 0
                    ? undefined
                    : ui.localize(
                          '@sage/xtrem-reporting/pages__list-report-creation__duplicate_report_template_name',
                          'Report template name already exists',
                      );
            }
            return undefined;
        },
    })
    templateName: ui.fields.Text;

    @ui.decorators.textAreaField<ListReportCreation>({
        parent() {
            return this.parametersBlock;
        },
        title: 'Description',
        width: 'large',
        isMandatory: true,
        rows: 5,
    })
    description: ui.fields.TextArea;

    @ui.decorators.dynamicPodField<ListReportCreation>({
        title: 'Parameters',
        width: 'small',
        isTitleHidden: true,
        isTransient: true,
        columns: [],
        async onChange() {
            await this.updatePreview();
        },
        parent() {
            return this.parametersBlock;
        },
    })
    parameters: ui.fields.DynamicPod;

    @ui.decorators.separatorField<ListReportCreation>({
        parent() {
            return this.parametersBlock;
        },
        isFullWidth: true,
        isDisabled: true,
    })
    separatorField: ui.fields.Separator;

    @ui.decorators.dropdownListField<ListReportCreation>({
        parent() {
            return this.parametersBlock;
        },
        isTransient: true,
        title: 'Page format',
        width: 'small',
        optionType: '@sage/xtrem-reporting/ReportPaperFormat',
        async onChange() {
            this.updatePreview();
        },
    })
    previewPaperFormat: ui.fields.DropdownList;

    @ui.decorators.dropdownListField<ListReportCreation>({
        parent() {
            return this.parametersBlock;
        },
        isTransient: true,
        title: 'Page orientation',
        width: 'small',
        optionType: '@sage/xtrem-reporting/ReportPageOrientation',
        async onChange() {
            this.updatePreview();
        },
    })
    previewPageOrientation: ui.fields.DropdownList;

    @ui.decorators.dropdownListField<ListReportCreation>({
        parent() {
            return this.parametersBlock;
        },
        isTransient: true,
        title: 'Locale',
        width: 'small',
        optionType: '@sage/xtrem-reporting/ReportLocale',
        async onChange() {
            this.updatePreview();
        },
    })
    previewLocale: ui.fields.DropdownList;

    @ui.decorators.switchField<ListReportCreation>({
        parent() {
            return this.parametersBlock;
        },
        title: 'Default header and footer',
        width: 'small',
        isTransient: true,
        async onChange() {
            this.updatePreview();
        },
    })
    isDefaultHeaderFooter: ui.fields.Switch;

    @ui.decorators.numericField<ListReportCreation>({
        parent() {
            return this.parametersBlock;
        },
        title: 'Top margin',
        width: 'small',
        postfix() {
            return getDisplayUnit(this.$.locale);
        },
        isTransient: true,
        scale: 2,
        async onChange() {
            this.updatePreview();
        },
    })
    previewTopMargin: ui.fields.Numeric;

    @ui.decorators.numericField<ListReportCreation>({
        parent() {
            return this.parametersBlock;
        },
        title: 'Bottom margin',
        width: 'small',
        postfix() {
            return getDisplayUnit(this.$.locale);
        },
        isTransient: true,
        scale: 2,
        async onChange() {
            this.updatePreview();
        },
    })
    previewBottomMargin: ui.fields.Numeric;

    @ui.decorators.numericField<ListReportCreation>({
        parent() {
            return this.parametersBlock;
        },
        title: 'Left margin',
        width: 'small',
        isTransient: true,
        postfix() {
            return getDisplayUnit(this.$.locale);
        },
        scale: 2,
        async onChange() {
            this.updatePreview();
        },
    })
    previewLeftMargin: ui.fields.Numeric;

    @ui.decorators.numericField<ListReportCreation>({
        parent() {
            return this.parametersBlock;
        },
        title: 'Right margin',
        width: 'small',
        isTransient: true,
        postfix() {
            return getDisplayUnit(this.$.locale);
        },
        scale: 2,
        async onChange() {
            this.updatePreview();
        },
    })
    previewRightMargin: ui.fields.Numeric;

    @ui.decorators.referenceField<ListReportCreation, ReportTemplate>({
        parent() {
            return this.previewBlock;
        },
        title: 'Active template',
        lookupDialogTitle: 'Select active template',
        node: '@sage/xtrem-reporting/ReportTemplate',
        valueField: 'name',
        columns: [
            ui.nestedFields.text({ bind: 'name', title: 'Template Name' }),
            ui.nestedFields.checkbox({ bind: 'isFactory', title: 'Is Factory template?' }),
            ui.nestedFields.reference({
                bind: 'report',
                valueField: '_id',
                node: '@sage/xtrem-reporting/Report',
                isHidden: true,
            }),
            ui.nestedFields.reference({
                bind: 'reportWizard',
                valueField: '_id',
                node: '@sage/xtrem-reporting/ReportWizard',
                isHidden: true,
                columns: [
                    ui.nestedFields.reference({
                        bind: 'dataSource',
                        valueField: '_id',
                        node: '@sage/xtrem-metadata/MetaNodeFactory',
                        isHidden: true,
                        columns: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'title' }),
                            ui.nestedFields.reference({
                                bind: 'package',
                                node: '@sage/xtrem-metadata/MetaPackage',
                                valueField: 'name',
                                isHidden: true,
                            }),
                        ],
                    }),
                    ui.nestedFields.technical({ bind: 'content' }),
                    ui.nestedFields.technical({ bind: 'filters' }),
                    ui.nestedFields.technical({ bind: 'parameters' }),
                    ui.nestedFields.technical({ bind: 'selectedProperties' }),
                ],
            }),
        ],
        isHidden: true,
    })
    activeTemplate: ui.fields.Reference;

    @ui.decorators.dropdownListField<ListReportCreation>({
        title: 'Report type',
        optionType: '@sage/xtrem-reporting/ReportType',
        parent() {
            return this.previewBlock;
        },
        isHidden: true,
    })
    reportType: ui.fields.DropdownList;

    @ui.decorators.checkboxField<ListReportCreation>({
        parent() {
            return this.previewBlock;
        },
        title: 'Is factory',
        helperText: 'Indicates a default report template',
        isReadOnly: true,
        isHidden: true,
    })
    isFactory: ui.fields.Checkbox;
}
