import { GraphApi, Report, ReportAssignmentAssociation, ReportAssignmentType } from '@sage/xtrem-reporting-api';
import { setApplicativePageCrudActions } from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.page<PrintingAssignmentDialog>({
    title: 'Assign reports to the page',
    node: '@sage/xtrem-reporting/ReportAssignmentPage',
    access: {
        node: '@sage/xtrem-reporting/ReportAssignmentPage',
    },
    businessActions() {
        return [this.$standardCancelAction, this.$standardSaveAction];
    },
    async defaultEntry() {
        return '$new';
    },
    async onLoad() {
        await this.init();
    },
    onDirtyStateUpdated(isDirty: boolean) {
        this.resetButtonState({ isDirty });
    },
})
export class PrintingAssignmentDialog extends ui.Page<GraphApi> {
    @ui.decorators.section<PrintingAssignmentDialog>({
        // title: 'Select the report to assign',
        isTitleHidden: true,
    })
    mainSection: ui.containers.Section<PrintingAssignmentDialog>;

    @ui.decorators.textField<PrintingAssignmentDialog>({})
    screenId: ui.fields.Text;

    @ui.decorators.selectField<PrintingAssignmentDialog>({
        optionType: '@sage/xtrem-reporting/enums/ReportAssignmentType',
    })
    reportAssignmentType: ui.fields.Select<ReportAssignmentType>;

    @ui.decorators.tableField<PrintingAssignmentDialog, ReportAssignmentAssociation>({
        parent() {
            return this.mainSection;
        },
        isTitleHidden: true,
        node: '@sage/xtrem-reporting/ReportAssignmentAssociation',
        columns: [
            ui.nestedFields.reference<PrintingAssignmentDialog, ReportAssignmentAssociation, Report>({
                bind: 'report',
                node: '@sage/xtrem-reporting/Report',
                helperTextField: 'description',
                isDisabled(_value, rowValue) {
                    // If the row _id is undefined we use 0 instead
                    return Number(rowValue?._id ?? 0) > 0 || rowValue?.isActive === false;
                },
                validation(_value, rowValue) {
                    return this.validateVendorProtectedFields({
                        rowId: rowValue._id,
                        vendor: rowValue._vendor,
                    });
                },
            }),
            ui.nestedFields.switch({
                bind: 'isDefault',
                title: 'Default',
                isDisabled(_value, rowValue) {
                    return rowValue?.isActive === false;
                },
                validation(value, rowValue) {
                    return this.validateDefaultAssignment({
                        rowId: rowValue._id,
                        isDefault: value,
                    });
                },
                async onChange(_rowName, value: ui.PartialCollectionValue<ReportAssignmentAssociation>) {
                    await this.handleChangesOnDefaultAssignment({
                        rowId: value._id ?? '',
                        isDefault: value.isDefault ?? false,
                    });
                },
            }),
            ui.nestedFields.text({ bind: { report: { description: true } }, isDisabled: true }),
            ui.nestedFields.technical({ bind: 'isActive' }),
            ui.nestedFields.technical({ bind: { _vendor: { name: true } } }),
        ],
        headerBusinessActions() {
            return [this.createReport];
        },
        fieldActions() {
            return [this.addReportAssignment];
        },
        inlineActions: [
            {
                icon: 'delete',
                title: 'Delete',
                isDisabled(_rowId, rowValue) {
                    return !!rowValue._vendor;
                },
                async onClick(rowId) {
                    return this.assignments.removeRecord(rowId);
                },
            },
        ],
        onRowSelected(rowId) {
            this.handleRowSelectionAndDeselection({ rowId, isActive: true });
        },
        onRowUnselected(rowId) {
            this.handleRowSelectionAndDeselection({ rowId, isActive: false });
        },
    })
    assignments: ui.fields.Table<ReportAssignmentAssociation>;

    @ui.decorators.pageAction<PrintingAssignmentDialog>({
        icon: 'add',
        title: 'Add',
        async onClick() {
            const newLine = this.assignments.addOrUpdateRecordValue({
                isActive: true,
            });

            this.assignments.selectRecord(newLine._id);
        },
    })
    addReportAssignment: ui.PageAction;

    @ui.decorators.pageAction<PrintingAssignmentDialog>({
        icon: 'add',
        title: 'Create report',
        async onClick() {
            const screenId = this.screenId.value;
            const result = await this.$.dialog.page(
                '@sage/xtrem-reporting/Report',
                {
                    _id: '$new',
                    ...(screenId && { _PRINTING_SOURCE_PAGE: screenId }),
                },
                {
                    fullScreen: true,
                    resolveOnCancel: true,
                },
            );

            if (result && result._id) {
                const assignment = this.assignments.addOrUpdateRecordValue({
                    report: await this.$.graph
                        .node('@sage/xtrem-reporting/Report')
                        .read(
                            {
                                _id: true,
                                name: true,
                                description: true,
                            },
                            result._id,
                        )
                        .execute(),
                    isActive: true,
                });

                this.assignments.selectRecord(assignment._id);
            }
        },
    })
    createReport: ui.PageAction;

    selectedDefaultAssignmentsRowId: string[];

    resetButtonState(args: { isDirty: boolean }) {
        setApplicativePageCrudActions({
            page: this,
            isDirty: args.isDirty,
            save: this.$standardSaveAction,
            cancel: this.$standardCancelAction,
        });
    }

    async init() {
        const screenId = (this.$.queryParameters._PRINTING_SOURCE_PAGE ?? '') as string;
        let reportAssignmentType: ReportAssignmentType;

        switch (this.$.queryParameters._PRINTING_SOURCE_TYPE) {
            case 'list':
                reportAssignmentType = 'mainList';
                break;
            case 'record':
            default:
                reportAssignmentType = 'detailPage';
                break;
        }

        if (this.$.isNewPage) {
            const reportAssignmentPageNaturalKey = `#${screenId}|${reportAssignmentType}`;

            const reportAssignmentPageId: string | undefined = (
                await this.$.graph
                    .node('@sage/xtrem-reporting/ReportAssignmentPage')
                    .read({ _id: true }, reportAssignmentPageNaturalKey)
                    .execute()
            )?._id;

            if (reportAssignmentPageId) {
                await this.$.router.selectRecord(reportAssignmentPageId);
            }
        }

        this.screenId.value = screenId;
        this.reportAssignmentType.value = reportAssignmentType;

        this.selectedDefaultAssignmentsRowId = [];

        this.assignments.value.forEach(assignment => {
            if (assignment.isActive) {
                this.assignments.selectRecord(assignment._id);
            }
            if (assignment.isDefault) {
                this.selectedDefaultAssignmentsRowId.push(assignment._id);
            }
        });

        this.$standardSaveAction.title = ui.localize(
            '@sage/xtrem-reporting/pages__record_printing_assignment_dialog__assign____title',
            'Assign',
        );

        this.resetButtonState({ isDirty: false });
    }

    validateVendorProtectedFields(args: {
        rowId: string;
        vendor: ui.PartialCollectionValue<ReportAssignmentAssociation>['_vendor'];
    }): string | undefined {
        if (args.vendor?.name) {
            return ui.localize(
                '@sage/xtrem-reporting/cannot-modify-assignment-protected-by-a-vendor',
                'Cannot edit vendor protected assignments.',
            );
        }
        return undefined;
    }

    allowActionOnVendorProtectedFields(args: {
        rowId: string;
        vendor: ui.PartialCollectionValue<ReportAssignmentAssociation>['_vendor'];
    }): boolean {
        return !this.validateVendorProtectedFields(args);
    }

    validateDefaultAssignment(args: { rowId: string; isDefault: boolean }): string | undefined {
        if (
            args.isDefault &&
            this.selectedDefaultAssignmentsRowId &&
            ((this.selectedDefaultAssignmentsRowId.length === 1 &&
                !this.selectedDefaultAssignmentsRowId.includes(args.rowId)) ||
                this.selectedDefaultAssignmentsRowId.length > 1)
        ) {
            return ui.localize(
                '@sage/xtrem-reporting/only-one-default-assignment-allowed',
                'Only one default assignment is allowed.',
            );
        }
    }

    async handleChangesOnDefaultAssignment(args: { rowId: string; isDefault: boolean }) {
        if (args.isDefault) {
            if (!this.selectedDefaultAssignmentsRowId.includes(args.rowId)) {
                this.selectedDefaultAssignmentsRowId.push(args.rowId);
            }
        } else {
            this.selectedDefaultAssignmentsRowId = this.selectedDefaultAssignmentsRowId.filter(
                rowId => rowId !== args.rowId,
            );
        }

        await this.assignments.validate();
    }

    handleRowSelectionAndDeselection(args: { rowId: string; isActive: boolean }) {
        const vendor = this.assignments.getRecordValue(args.rowId)?._vendor;

        if (this.allowActionOnVendorProtectedFields({ rowId: args.rowId, vendor })) {
            this.assignments.addOrUpdateRecordValue({ _id: args.rowId, isActive: args.isActive });
        } else {
            if (args.isActive) {
                this.assignments.unselectRecord(args.rowId);
            } else {
                this.assignments.selectRecord(args.rowId);
            }
        }
    }
}
