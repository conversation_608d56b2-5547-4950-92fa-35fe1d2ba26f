import * as ui from '@sage/xtrem-ui';
import { ReportTemplate, GraphApi } from '@sage/xtrem-reporting-api';
import { reporting } from '../menu-items/reporting';

@ui.decorators.page<TranslationImportExport, ReportTemplate>({
    module: '',
    title: 'Translation import/export',
    node: '@sage/xtrem-reporting/Report',
    menuItem: reporting,
    mode: 'tabs',
    isTransient: true,
})
export class TranslationImportExport extends ui.Page<GraphApi> {
    @ui.decorators.section<TranslationImportExport>({
        isTitleHidden: true,
        title: 'Export',
    })
    exportSection: ui.containers.Section;

    @ui.decorators.block<TranslationImportExport>({
        parent() {
            return this.exportSection;
        },
    })
    exportBlock: ui.containers.Block;

    @ui.decorators.dropdownListField<TranslationImportExport>({
        parent() {
            return this.exportBlock;
        },
        title: 'Source Language',
        isMandatory: true,
        optionType: '@sage/xtrem-reporting/ReportLocale',
    })
    sourceLocale: ui.fields.DropdownList;

    @ui.decorators.dropdownListField<TranslationImportExport>({
        parent() {
            return this.exportBlock;
        },
        isMandatory: true,
        title: 'Target Language',
        optionType: '@sage/xtrem-reporting/ReportLocale',
    })
    targetLocale: ui.fields.DropdownList;

    @ui.decorators.multiReferenceField<TranslationImportExport, ReportTemplate>({
        parent() {
            return this.exportBlock;
        },
        title: 'Report Template',
        node: '@sage/xtrem-reporting/ReportTemplate',
        helperText: 'Select the reports for export, optional',
        valueField: 'name',
    })
    reportTemplates: ui.fields.MultiReference;

    @ui.decorators.buttonField<TranslationImportExport>({
        parent() {
            return this.exportBlock;
        },
        map() {
            return 'Export';
        },
        async onClick() {
            const validation = await this.exportBlock.validate();
            if (validation.length !== 0) {
                return;
            }
            this.exportField.isHidden = true;
            this.$.loader.isHidden = false;

            const value = await this.$.graph
                .node('@sage/xtrem-reporting/ReportTemplate')
                .queries.exportTranslations(
                    {
                        value: true,
                    },
                    {
                        reportTemplates: this.reportTemplates.value.map(r => r._id),
                        sourceLocale: this.sourceLocale.value,
                        targetLocale: this.targetLocale.value,
                    },
                )
                .execute();
            this.exportField.value = value;
            this.exportField.text = `reports-${this.sourceLocale.value}-${this.targetLocale.value}-${new Date()
                .toISOString()
                .substring(0, 10)}.json`;
            this.$.showToast('The exported translation file is available for downloading.', { type: 'success' });
            this.exportField.isHidden = false;

            this.$.loader.isHidden = true;
        },
        onError() {
            this.$.loader.isHidden = true;
        },
    })
    exportButton: ui.fields.Button;

    @ui.decorators.fileField<TranslationImportExport>({
        parent() {
            return this.exportBlock;
        },
        fileTypes: 'application/json',
        text: '',
        title: 'Download',
        isHidden: true,
        isReadOnly: true,
    })
    exportField: ui.fields.File;

    @ui.decorators.section<TranslationImportExport>({
        isTitleHidden: true,
        title: 'Import',
    })
    importSection: ui.containers.Section;

    @ui.decorators.block<TranslationImportExport>({
        parent() {
            return this.importSection;
        },
    })
    importBlock: ui.containers.Block;

    @ui.decorators.fileField<TranslationImportExport>({
        parent() {
            return this.importBlock;
        },
        fileTypes: 'application/json',
        text: 'Ready to import!',
        title: 'Import file',
        isMandatory: true,
    })
    importField: ui.fields.File;

    @ui.decorators.buttonField<TranslationImportExport>({
        parent() {
            return this.importBlock;
        },
        map() {
            return 'Upload';
        },
        async onClick() {
            const validation = await this.importBlock.validate();
            if (validation.length !== 0) {
                return;
            }
            this.$.loader.isHidden = false;
            await this.$.graph
                .node('@sage/xtrem-reporting/ReportTemplate')
                .mutations.importTranslations(true, { input: { translations: this.importField.value } })
                .execute();
            this.$.loader.isHidden = true;
            this.$.showToast('Translations successfully imported.', { type: 'success' });
        },
        onError() {
            this.$.loader.isHidden = true;
            return 'Import failed.';
        },
    })
    importButton: ui.fields.Button;
}
