import * as ui from '@sage/xtrem-ui';
import { TEMPLATE_COPY, TEMPLATE_CONVERT } from '../client-functions/client-constants';

@ui.decorators.page<ReportTemplateConvertDialog>({
    title: 'Convert to HTML mode',
    businessActions() {
        return [this.$standardCancelAction, this.copy, this.convert];
    },
    isTransient: true,
    onLoad() {
        if (this.$.queryParameters?.formCopy) {
            this.copy.title = ui.localize(
                '@sage/xtrem-reporting/pages__report_template_convert_dialog__form_copy',
                'Create form copy',
            );
            this.$.page.title = ui.localize(
                '@sage/xtrem-reporting/pages__report_template_convert_dialog__form_copy_title',
                'Create form copy',
            );
            this.content.value = ui.localize(
                '@sage/xtrem-reporting/pages__report_template_convert_dialog__form_content',
                'If you convert to a form, this template and the links where you access it from are lost.\n\n If you create a copy as a form, you can continue to use this list, but any changes you make are not made to the form version.',
            );
        } else {
            this.copy.title = ui.localize(
                '@sage/xtrem-reporting/pages__report_template_convert_dialog__html_copy',
                'Create HTML copy',
            );
            this.content.value = ui.localize(
                '@sage/xtrem-reporting/pages__report_template_convert_dialog__html_content',
                'If you convert to HTML, this template and the links where you access it from are lost.\n\n If you create an HTML copy, you can continue to use this template, but any changes you make are not made to the HTML version.',
            );
        }
    },
})
export class ReportTemplateConvertDialog extends ui.Page {
    @ui.decorators.pageAction<ReportTemplateConvertDialog>({
        title: 'Create HTML copy',
        buttonType: 'secondary',
        onClick() {
            this.$.finish(TEMPLATE_COPY);
        },
    })
    copy: ui.PageAction;

    @ui.decorators.pageAction<ReportTemplateConvertDialog>({
        title: 'Convert',
        isDestructive: true,
        async onClick() {
            this.$.finish(TEMPLATE_CONVERT);
        },
    })
    convert: ui.PageAction;

    @ui.decorators.section<ReportTemplateConvertDialog>({
        isTitleHidden: true,
    })
    section: ui.containers.Section;

    @ui.decorators.block<ReportTemplateConvertDialog>({
        isTitleHidden: true,
        parent() {
            return this.section;
        },
    })
    block: ui.containers.Block;

    @ui.decorators.staticContentField<ReportTemplateConvertDialog>({
        isTransient: true,
        isMarkdown: true,
        isTitleHidden: true,
        isFullWidth: true,
        parent() {
            return this.block;
        },
        content:
            'If you convert to HTML mode, this template will be lost and the links you can access it from\n\nIf you keep this template, you can continue to use it, but any changes you make to it will not be reflected in the HTML mode.',
    })
    content: ui.fields.StaticContent;
}
