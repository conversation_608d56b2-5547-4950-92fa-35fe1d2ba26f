import { Report } from '@sage/xtrem-reporting-api';
import * as ui from '@sage/xtrem-ui';
import axios from 'axios';
import { <PERSON>uff<PERSON> } from 'buffer';

@ui.decorators.page<PrintDocument, Report>({
    title: 'Print Document',
    isTransient: true,
    areNavigationTabsHidden: true,
    async onLoad() {
        const reportVariables = { ...this.$.queryParameters };
        const { locale = this.$.locale, paperFormat = null, reportName, documentTitle } = reportVariables;
        delete reportVariables.reportName;
        delete reportVariables.locale;
        delete reportVariables.paperFormat;
        delete reportVariables.documentTitle;
        try {
            this.preview.isLoading = true;
            const result = await this.fetchReport(
                String(reportName),
                String(locale),
                paperFormat as string,
                reportVariables,
                documentTitle as string,
            );
            this.preview.value = result.value.downloadUrl;
            this.preview.filename = result.value.filename;
            this.preview.isLoading = false;
        } catch (e: any) {
            ui.console.error(e);
            this.$.showToast(e.message, { timeout: 0, type: 'error' });
            this.preview.isLoading = false;
        }
    },
    onError() {
        this.preview.isLoading = false;
    },
})
export class PrintDocument extends ui.Page {
    fetchReport = async (
        reportName: string,
        locale: string,
        paperFormat: string,
        reportVariables: any,
        documentTitle?: string,
    ): Promise<{
        value: {
            key: string;
            filename: string;
            mimeType: string;
            contentLength: number;
            status: string;
            rejectReason: string;
            uploadUrl: string;
            downloadUrl: string;
        };
    }> => {
        const reportNode = await this.$.graph.graph.node('@sage/xtrem-reporting/Report');
        // TODO: We only use the active template at the moment. When the user can choose which template
        // to use this will need to be changed. XT-55919.
        const { trackingId } = await reportNode.asyncOperations.generateReportPdf
            .start(
                { trackingId: true },
                {
                    reportName,
                    reportTemplateName: '',
                    reportSettings: {
                        variables: JSON.stringify(reportVariables),
                        locale,
                        paperFormat,
                        ...(documentTitle ? { documentTitle } : {}),
                    },
                },
            )
            .execute();
        const retryAfterMillis = 1000;
        const timeoutMillis = 60000;
        const startedAt = Date.now();
        let success = false;
        while (!success) {
            const now = Date.now();
            if (now - startedAt > timeoutMillis) {
                throw new Error(`polling has timed out: the maximum time allowed was ${timeoutMillis} ms`);
            }
            const { status, result, errorMessage, logMessages } = await reportNode.asyncOperations.generateReportPdf
                .track(
                    {
                        status: true,
                        result: {
                            key: true,
                            filename: true,
                            mimeType: true,
                            contentLength: true,
                            status: true,
                            rejectReason: true,
                            uploadUrl: true,
                            downloadUrl: true,
                        },
                        errorMessage: true,
                        logMessages: {
                            message: true,
                            level: true,
                        },
                    },
                    { trackingId },
                )
                .execute();

            if (status === 'error') {
                const exception = logMessages.find(
                    (log: { level: string; message: string }) => log.level === 'exception',
                );
                if (exception) {
                    throw new Error(exception.message);
                }
                throw new Error(errorMessage);
            }
            if (status === 'success') {
                success = true;
                return { value: result };
            }
            await new Promise(resolve => {
                setTimeout(resolve, retryAfterMillis);
            });
        }
    };

    downloadPdf = async (url: string) => {
        try {
            const response = await axios.get(url, {
                responseType: 'arraybuffer',
            });
            const base64Data = Buffer.from(response.data, 'binary').toString('base64');
            return base64Data;
        } catch (error) {
            console.error(error);
            return null;
        }
    };

    @ui.decorators.section<PrintDocument>({
        isTitleHidden: true,
    })
    mainSection: ui.containers.Section;

    @ui.decorators.block<PrintDocument>({
        parent() {
            return this.mainSection;
        },
    })
    mainBlock: ui.containers.Block;

    @ui.decorators.previewField<PrintDocument>({
        parent() {
            return this.mainBlock;
        },
        height: 500,
        isFullWidth: true,
        isTransient: true,
        canDownload: true,
        canZoom: true,
        canPrint: true,
        hasThumbnailBar: true,
        mimeType: 'application/pdf',
    })
    preview: ui.fields.Preview;
}
