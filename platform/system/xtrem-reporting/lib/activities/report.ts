import { Activity } from '@sage/xtrem-core';
import { Report } from '../nodes/report';
import { ReportTemplate } from '../nodes/report-template';

export const report = new Activity({
    description: 'Report',
    node: () => Report,
    __filename,
    permissions: ['read', 'manage'],
    operationGrants: {
        manage: [
            {
                operations: [
                    'create',
                    'update',
                    'delete',
                    'generateReports',
                    'createOrUpdateReport',
                    'generateReportAndNotifyUser',
                    'generateReportPdf',
                    'generateReportZip',
                    'generateUploadedFile',
                ],
                on: [() => Report],
            },
            {
                operations: ['lookup'],
                on: [() => ReportTemplate],
            },
        ],
    },
});
