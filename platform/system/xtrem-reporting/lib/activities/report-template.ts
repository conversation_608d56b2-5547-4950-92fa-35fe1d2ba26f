import { Activity } from '@sage/xtrem-core';
import { ReportTemplate } from '../nodes/report-template';
import { Report } from '../nodes/report';

export const reportTemplate = new Activity({
    description: 'Report template',
    node: () => ReportTemplate,
    __filename,
    permissions: ['read', 'manage'],
    operationGrants: {
        manage: [
            {
                operations: [
                    'create',
                    'update',
                    'delete',
                    'generateReportSample',
                    'exportTranslations',
                    'importTranslations',
                    'getSchema',
                    'generateWizardPreview',
                ],
                on: [() => ReportTemplate],
            },
            {
                operations: ['generateReportAndNotifyUser'],
                on: [() => Report],
            },
        ],
    },
});
