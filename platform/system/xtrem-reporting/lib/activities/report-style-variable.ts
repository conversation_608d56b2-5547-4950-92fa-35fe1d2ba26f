import { Activity } from '@sage/xtrem-core';
import { ReportStyleVariable } from '../nodes/report-style-variable';

export const reportStyleVariable = new Activity({
    description: 'Style variable',
    node: () => ReportStyleVariable,
    __filename,
    permissions: ['read', 'manage'],
    operationGrants: {
        manage: [
            {
                operations: ['create', 'update', 'delete'],
                on: [() => ReportStyleVariable],
            },
        ],
    },
});
