import { Activity } from '@sage/xtrem-core';
import { ReportResource } from '../nodes/report-resource';

export const reportResource = new Activity({
    description: 'Report resource',
    node: () => ReportResource,
    __filename,
    permissions: ['read', 'manage'],
    operationGrants: {
        manage: [
            {
                operations: ['create', 'update', 'delete'],
                on: [() => ReportResource],
            },
        ],
    },
});
