import { Logger, WorkflowStepDescriptor } from '@sage/xtrem-core';
import { GraphqlAction, Request, WorkflowGraphqlActionConfig, WorkflowParameter } from '@sage/xtrem-workflow';
import { zipObject } from 'lodash';

interface PrintDocumentActionConfig extends WorkflowGraphqlActionConfig {
    report: string;
    reportParameters: WorkflowParameter[];
}

const logger = Logger.getLogger(__filename, 'print-action');

export class PrintDocumentAction extends GraphqlAction<PrintDocumentActionConfig> {
    override buildRequest(): Request {
        const { report, reportParameters } = this.resolveConfig();

        const parameters = reportParameters;
        const variables = zipObject(
            parameters.map(param => param.name),
            parameters.map(param => (param.isVariable ? this.getVariableValue(param.value as string) : param.value)),
        );

        logger.verbose(() => `Generating document ${report} with parameters: ${JSON.stringify(variables)}`);

        return {
            operationType: 'asyncMutation',
            packageName: 'xtremReporting',
            nodeName: 'report',
            operationName: 'generateUploadedFile',
            args: `(reportName: "${report}", reportSettings: { variables: ${JSON.stringify(
                JSON.stringify(variables),
            )}, locale: "${this.locale}" })`,
            selector: '{ _id }',
            variables: [],
            outputPath: '',
        };
    }

    static override readonly descriptor = {
        type: 'action',
        key: 'print-document',
        title: '',
        description: '',
        ui: {
            icon: 'print',
            color: '#335b70ff',
            configurationPage: '@sage/xtrem-reporting/WorkflowActionPrintDocument',
        },
    } as WorkflowStepDescriptor<PrintDocumentActionConfig>;
}
