import type { EditorSuggestion } from '@sage/xtrem-ui-plugin-monaco';

interface SchemaOfType {
    kind: string;
    name: string;
}

interface SchemaFieldType {
    kind: string;
    name: string;
    ofType: SchemaOfType | null;
}

interface SchemaField {
    name: string;
    type: SchemaFieldType;
}

interface SchemaType {
    kind: string;
    name: string;
    description: string;
    fields: SchemaField[];
}

const jsonSuggestions = (
    obj: any,
    schemaTypes: SchemaType[],
    schemaObjName: string,
    schemaObjKind: string,
): EditorSuggestion[] => {
    return Object.keys(obj).map(key => {
        const type = schemaTypes.find(t => t.name === schemaObjName && t.kind === schemaObjKind);
        if (!type) {
            throw new Error('schema type not found');
        }
        const fieldObj = type.fields?.find((field: any) => field.name === key);
        if (!fieldObj) {
            throw new Error('schema field not found');
        }

        const isList = fieldObj.type.kind === 'LIST';
        const fieldObjKind = isList ? fieldObj.type.ofType!.kind : fieldObj.type.kind;
        const fieldObjName = isList ? fieldObj.type.ofType!.name : fieldObj.type.name;
        if (!fieldObjName) {
            throw new Error('field name not found');
        }

        return {
            label: key,
            insertText: key,
            children:
                typeof obj[key] === 'object' ? jsonSuggestions(obj[key], schemaTypes, fieldObjName, fieldObjKind) : [],
            sortText: '!query',
            kind: 'Property',
            isArray: isList,
        } as EditorSuggestion;
    });
};

/**
 *
 * @param query GraphQL query string. The objects in the query will be converted into a nested array of suggestions which can be provided to a Monaco editor plugin to be used by IntelliSense for the Handlebars language.
 *
 * @return An array of EditorSuggestion objects, or null.
 */
export const extractSuggestionsFromGraphQLQuery = (query: string, schema: any): EditorSuggestion[] | null => {
    if (query) {
        try {
            const jsonObject = JSON.parse(
                query
                    .replace(/\(.*\)/g, '')
                    .replace(/#(.*)/g, '')
                    .replace(/:[a-zA-Z0-9_\s]*/g, '')
                    .replace(/([a-zA-Z0-9_]+)/g, '"$1"')
                    .replace(/\s/g, '')
                    .replace(/""/g, '": true, "')
                    .replace(/",/g, '": true, ')
                    .replace(/"}/g, '": true }"')
                    .replace(/"{/g, '":{')
                    .replace(/}"/g, '}')
                    .replace(/}"([a-zA-Z0-9_])/g, '}, "$1')
                    .replace(/}([a-zA-Z0-9_])/g, '}, $1')
                    .replace(/,\s([a-zA-Z0-9_])/g, ', "$1'),
            );

            const schemaTypes = schema.__schema.types as any as SchemaType[];

            return jsonSuggestions(jsonObject, schemaTypes, 'RootQueryType', 'OBJECT');
        } catch (e) {
            // since this is error related to suggestions, we don't need to notify the user
            return [];
        }
    }

    return null;
};
