import * as ui from '@sage/xtrem-ui';
import { TemplateType } from '@sage/xtrem-reporting-api';

export function reportOptionsDescription(option: TemplateType): string {
    switch (option) {
        case 'form':
            return ui.localize(
                '@sage/xtrem-reporting/report-creation-wizard-mode-form-description',
                'Design a custom form such as a purchase or sales order that you can use for specific customers or another unique business need.',
            );
        case 'advanced':
            return ui.localize(
                '@sage/xtrem-reporting/report-creation-wizard-mode-advanced-description',
                'Create a template using HTML tools and a custom CSS.',
            );
        case 'list':
        default:
            return ui.localize(
                '@sage/xtrem-reporting/report-creation-wizard-mode-list-description',
                'Create a table with several columns and a grand total at the end.',
            );
    }
}

export function reportOptionsValues(option: TemplateType): string {
    switch (option) {
        case 'form':
            return ui.localize('@sage/xtrem-reporting/report-creation-wizard-mode-form-title', 'Form');
        case 'advanced':
            return ui.localize('@sage/xtrem-reporting/report-creation-wizard-mode-advanced-title', 'Advanced');
        case 'list':
        default:
            return ui.localize('@sage/xtrem-reporting/report-creation-wizard-mode-list-title', 'List');
    }
}
