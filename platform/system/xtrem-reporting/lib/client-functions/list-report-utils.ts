import type { ExtractEdgesPartial } from '@sage/xtrem-client';
import type { DataModelProperty } from '@sage/xtrem-document-editor';
import * as _ from 'lodash';
import { Dict, FieldKey, LocalizeLocale, NodeDetails, Sortings } from '@sage/xtrem-shared';
import * as ui from '@sage/xtrem-ui';

export type ReportContent = NonNullable<InstanceType<typeof ui.fields.ContentTable>['value']>[number];
export type TreeElement = NonNullable<InstanceType<typeof ui.fields.NodeBrowserTree>['value']>[number];
export type SelectedProperty = NonNullable<ReportContent['property']>;
export type Order = ReportContent['sorting'];
export interface GroupAggregation {
    group: ReportContent['group'];
    operation: ReportContent['operation'];
}

export function convertParameterTypeToFieldType(parameterType: NodeDetails['type']): string {
    switch (parameterType) {
        case 'Boolean':
            return 'boolean';
        case 'Enum':
            return 'enum';
        case 'Float':
            return 'float';
        case 'Integer':
            return 'integer';
        case 'Decimal':
            return 'Decimal';
        case 'String':
        default:
            return 'string';
    }
}

export function convertSelectedPropertiesToContent(property: TreeElement): ReportContent {
    const selectedProperty: SelectedProperty = {
        ...property,
        data: {
            ...property.data,
            targetNode: property.data.targetNode || '',
        },
        key: property.id,
        labelKey: property.label,
    };

    return {
        title: property.label,
        sorting: property.data.canSort ? Sortings.ascending : undefined,
        presentation: FieldKey.Text,
        formatting: '',
        divisor: '',
        operation: 'NONE',
        labelPath: property.labelPath,
        property: selectedProperty,
        path: property.id,
        group: 0,
    };
}

export function convertSelectedPropertiesToDataModelProperty(
    selectedProperties: Dict<SelectedProperty>,
    namespace: string,
): Dict<DataModelProperty> {
    const fields: Dict<DataModelProperty> = {};

    if (selectedProperties) {
        Object.entries(selectedProperties).forEach(([key, value]) => {
            fields[key] = { ...value.data, ...value, name: value.id, namespace };
        });
    }
    return fields;
}

export function removeExtractEdgesPartial<T>(partialObject: T) {
    return partialObject as unknown as ExtractEdgesPartial<T>;
}

export function convertContentTableValueToAggregationProperty(elements: Array<ReportContent>): Dict<GroupAggregation> {
    const aggregations: Dict<GroupAggregation> = {};

    if (elements) {
        elements.forEach(element => {
            if (element.property?.id) {
                const key = element.property.id;
                const aggregation: GroupAggregation = {
                    group: element.group,
                    operation: element.operation,
                };
                aggregations[key] = aggregation;
            }
        });
    }

    return aggregations;
}

export function convertContentTableValueToOrderByProperty(elements: Array<ReportContent>): Dict<Order> {
    const orders: Dict<Order> = {};

    if (elements) {
        elements.forEach(element => {
            if (element.property?.id) {
                orders[element.property.id] = element.sorting;
            }
        });
    }

    return orders;
}

export function getDisplayUnit(locale: LocalizeLocale): string {
    return locale === 'en-US'
        ? ui.localize('@sage/xtrem-reporting/margin-unit-inch', 'in')
        : ui.localize('@sage/xtrem-reporting/margin-unit-cm', 'cm');
}

/**
 * I want to have a clean typing for dynamicPod.values
 * Or I want an accessor to all values into my dynamicPod
 * to be able able to know witch type of value I have
 */
export function getVariableValue(parameters: any): string {
    // For reference variables the value is an object with _id and lookup properties and we only pass the _id
    // For non-reference variables we pass the value as is.

    const variables = _.mapValues((parameters || {}) as Dict<any>, value => (value?._id ? value?._id : value));
    return JSON.stringify(variables);
}
