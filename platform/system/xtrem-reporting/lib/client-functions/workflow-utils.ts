import { enums as metaEnums } from '@sage/xtrem-metadata';
import type { ReportVariable } from '@sage/xtrem-reporting-api';
import { GraphQLTypes, LocalizeLocale, WorkflowVariable } from '@sage/xtrem-shared';
import {
    addParametersToDynamicPod,
    DynamicParameter,
} from '@sage/xtrem-system/build/lib/client-functions/dynamic-pod-helper';
import * as ui from '@sage/xtrem-ui';
import { stripPackageName } from '@sage/xtrem-workflow/build/lib/client-functions/node-browser-tree-utils';
import { variableIsAssignableFrom } from '@sage/xtrem-workflow/build/lib/client-functions/variable-utils';

/**
 * map the type of a reportVariable to a GraphQL type
 */
function reportVariableTypeToVariableType(type: metaEnums.MetaPropertyType): keyof typeof GraphQLTypes {
    switch (type) {
        case 'boolean':
            return 'Boolean';
        case 'uuid':
        case 'string':
            return 'String';
        case 'byte':
        case 'short':
        case 'integer':
            return 'Int';
        case 'decimal':
        case 'float':
        case 'double':
            return 'Decimal';
        case 'enum':
            return 'Enum';
        case 'date':
            return 'Date';
        case 'time':
        case 'datetime':
            return 'DateTime';
        case 'binaryStream':
        case 'textStream':
            return '_OutputTextStream';
        case 'json':
            return 'Json';
        case 'reference':
            return 'IntReference';
        case 'collection':
        case 'jsonReference':
        case 'integerArray':
        case 'enumArray':
        case 'referenceArray':
        case 'stringArray':
        case 'integerRange':
        case 'decimalRange':
        case 'dateRange':
        case 'datetimeRange':
            // No exact match for these types
            return 'String';
        default:
            return 'String';
    }
}

export function updateReportParameterColumns({
    reportParametersPod,
    reportVariables,
    locale,
    getPropertiesForDynamicSelects,
}: {
    reportParametersPod: ui.fields.DynamicPod;
    reportVariables: ReportVariable[];
    locale: LocalizeLocale;
    getPropertiesForDynamicSelects: (
        selectableVariableFilter: (_page: ui.Page, variable: WorkflowVariable) => boolean,
    ) => any;
}) {
    const parameters = (reportVariables || []).reduce(
        (prevValue: DynamicParameter[], reportVariable: ReportVariable) => {
            const checkboxFieldId = `${reportVariable.name}/isVariable`;
            const attributes = {
                ...(typeof reportVariable.dataType?.attributes === 'object'
                    ? (reportVariable.dataType.attributes as any)
                    : JSON.parse(reportVariable.dataType?.attributes ?? '{}')),
            };

            const matchesType = (r: WorkflowVariable) => {
                if (r.node) {
                    return r.node === stripPackageName(attributes.node ?? '');
                }
                const graphQlType = reportVariableTypeToVariableType(reportVariable.type);
                return variableIsAssignableFrom(
                    {
                        ...attributes,
                        type: graphQlType,
                    },
                    r,
                );
            };

            const field = { ...reportVariable };
            field.dataType = {
                attributes: {
                    ...(attributes as any),
                    width: 'small',
                },
            } as any;
            const variableSelection = {
                ...reportVariable,
                type: 'dynamicSelect',
                dataType: {
                    name: reportVariable.name,
                    attributes: getPropertiesForDynamicSelects((_page: ui.Page, variable: WorkflowVariable) =>
                        matchesType(variable),
                    ),
                },
            };

            const isVariableField = {
                _id: checkboxFieldId,
                type: 'boolean',
                name: checkboxFieldId,
                title: 'Enter manually',
                dataType: {
                    name: checkboxFieldId,
                    attributes: {
                        width: 'small',
                        isValueReversed: true,
                    },
                },
            };

            return [
                ...prevValue,
                reportParametersPod.value?.[checkboxFieldId] ? variableSelection : field,
                isVariableField,
            ];
        },
        [] as DynamicParameter[],
    );

    addParametersToDynamicPod({
        dynamicPod: reportParametersPod,
        locale,
        parameters,
        isFullWidth: false,
    });
}
