import { Dict, localeFallbacks, LocalizeLocale } from '@sage/xtrem-shared';
import { MD5 } from 'crypto-js';
import * as htmlParser from 'node-html-parser';
import * as xtremReporting from '../index';

const translationContainerId = '__translationExtractionContainer';

export interface ExtractionResult {
    content: string;
    extractedContent: Dict<string>;
}

export interface TranslatableText {
    hash: string;
    locale: xtremReporting.enums.ReportLocale;
    text?: string;
}

const getPlaceholderElement = (hash: string) => `{{ translatedContent "${hash}" }}`;

export const extractTranslatableContent = (content: string) => {
    const extractedContent: Dict<string> = {};
    const parsedValue = htmlParser.parse(`<div id="${translationContainerId}">${content}</div>`, { comment: true });
    parsedValue.querySelectorAll('*').forEach(v => {
        v.childNodes.forEach(c => {
            if (c.nodeType === htmlParser.NodeType.TEXT_NODE) {
                let textContent = c.textContent;
                if (textContent.trim()) {
                    if (textContent.startsWith('<!')) {
                        // Filtering out commented content
                        return;
                    }
                    textContent.split(/{{{?[^}{]*}}}?/).forEach(fragment => {
                        const trimmedContent = fragment.trim();
                        if (trimmedContent) {
                            const hash = MD5(trimmedContent).toString();
                            extractedContent[hash] = trimmedContent;
                            textContent = textContent.split(trimmedContent).join(getPlaceholderElement(hash));
                        }
                    });
                }
                if (c.textContent !== textContent) {
                    c.textContent = textContent;
                }
            }
        });
    });

    return {
        content: parsedValue.querySelector(`#${translationContainerId}`)!.innerHTML.replace(/&#x22;/g, '"'),
        extractedContent,
    };
};

export const restoreTranslationContent = (content: string, literals: Dict<string>) => {
    return content.replace(/\{\{\s+translatedContent\s+"(\w+)"\s+\}\}/g, (_all, hash) => literals[hash]);
};

export const createDictionaryForRendering = (
    targetLocale: xtremReporting.enums.ReportLocale,
    baseLocale: xtremReporting.enums.ReportLocale,
    translations: TranslatableText[],
    baseSet: Dict<string> = {},
): Dict<string> => {
    const translatedLiterals: Dict<string> = { ...baseSet };
    const findText = (locale: xtremReporting.enums.ReportLocale, hash: string): string | null =>
        translations.find(t => t.hash === hash && t.locale === locale)?.text || null;
    const normalizedTargetLocale: LocalizeLocale = targetLocale.split('_').join('-') as unknown as LocalizeLocale;
    const fallbackPath = localeFallbacks[normalizedTargetLocale as LocalizeLocale];
    const baseLiterals = translations.filter(t => t.locale === baseLocale);
    const stringsToCollect: string[] = [...Object.keys(baseSet), ...baseLiterals.map(t => t.hash)];
    // Assemble dictionary using fallback paths
    stringsToCollect.forEach(hash => {
        for (let i = 0; i < fallbackPath.length; i += 1) {
            const locale = fallbackPath[i];
            if (locale === 'base') {
                // Fallback to base
                const text = findText(baseLocale, hash);
                if (!text && !translatedLiterals[hash]) {
                    throw new Error(`No translation could be found for ${hash}`);
                }
                translatedLiterals[hash] = text || translatedLiterals[hash];
                break;
            }

            // Checking if a translation is available
            const text = findText(locale.split('-').join('_') as xtremReporting.enums.ReportLocale, hash);
            if (text) {
                translatedLiterals[hash] = text;
                break;
            }
        }
    });

    return translatedLiterals;
};
