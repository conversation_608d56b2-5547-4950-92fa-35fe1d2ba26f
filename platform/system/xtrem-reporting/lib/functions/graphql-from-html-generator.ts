import { getFilterObject } from '@sage/xtrem-filter-utils';
import type { Dict, FilterProperty, LocalizeLocale } from '@sage/xtrem-shared';
import {
    ATTR_ALIAS,
    ATTR_CONTEXT_CONDITION,
    ATTR_CONTEXT_FILTER,
    ATTR_CONTEXT_LIST_ORDER,
    ATTR_CONTEXT_OBJECT_PATH,
    ATTR_PROPERTY_NAME,
    BLOCK_AGGREGATION_PROPERTY_NAME,
    CUSTOM_DATA_PROPERTY,
    PROPERTY_CLASS,
    QUERY_TABLE_CLASS,
    RECORD_CONTEXT_CLASS,
    deepMerge,
} from '@sage/xtrem-shared';
import type { ConditionEditorProperty, ValueType } from '@sage/xtrem-ui-components';
import { DOMParser, MIME_TYPE } from '@xmldom/xmldom';
import { jsonToGraphQLQuery } from 'json-to-graphql-query';
import { isArray, set } from 'lodash';
import * as xtremReporting from '../index';

interface JsonObject {
    [key: string]: any;
}

function convertLocaleToHyphenFormat(locale: string): LocalizeLocale {
    return locale.replace('_', '-') as LocalizeLocale;
}

function parseContextFilter(contextFilter: string): FilterProperty[] {
    const parsedObject = JSON.parse(contextFilter.replace(/&quot;/g, '"'));
    if (Array.isArray(parsedObject)) {
        return parsedObject as FilterProperty[];
    }
    throw new Error('Context filter wrong type should be FilterProperty[]');
}
function hasClass(node: Node, className: string): boolean {
    if (node.nodeType === node.ELEMENT_NODE) {
        const element = node as Element;
        const classes = element.getAttribute('class');
        if (classes) {
            return classes.split(' ').includes(className);
        }
    }
    return false;
}

function processChildren(node: Element, locale?: xtremReporting.enums.ReportLocale): JsonObject {
    const children = node.childNodes;
    if (!children) return {};
    let result: JsonObject = {};
    for (let i = 0; i < children.length; i += 1) {
        const childNode = children[i] as Element;
        result = deepMerge(result, processNode(childNode, true, locale));
    }

    return result;
}

function setProperty(target: any, propertyName: string) {
    const propertyNameSegments: string[] = propertyName.split('.');
    const customDataIndex = propertyNameSegments.findIndex(segment => segment === CUSTOM_DATA_PROPERTY);
    if (customDataIndex !== -1) {
        const customDataPath = propertyNameSegments.slice(0, customDataIndex + 1).join('.');
        set(target, customDataPath, true);
    } else {
        set(target, propertyName, true);
    }
}

function processConditionProperty(valueType?: ValueType, value?: any): JsonObject {
    const result: JsonObject = {};
    if (valueType === 'property') {
        const values = isArray(value) ? value : [value];
        values.forEach(v => {
            setProperty(result, v.id);
        });
    }

    return result;
}

function processConditionBlockNode(node: Element, locale?: xtremReporting.enums.ReportLocale): JsonObject {
    let result: JsonObject = {};

    const contextCondition = node.getAttribute?.(ATTR_CONTEXT_CONDITION) || '';
    const parsedContextConditions = JSON.parse(contextCondition.replace(/&quot;/g, '"')) as ConditionEditorProperty[];
    parsedContextConditions.forEach(c => {
        const value1Result = processConditionProperty(c.valueType1, c.value1);
        result = deepMerge(result, value1Result);
        const value2Result = processConditionProperty(c.valueType2, c.value2);
        result = deepMerge(result, value2Result);
    });
    return deepMerge(result, processChildren(node, locale));
}

function processPropertyNode(node: Element): JsonObject {
    const result: JsonObject = {};
    const propertyName = node.getAttribute(ATTR_PROPERTY_NAME) || '';
    // Global variables (starting with @root) are injected on run time, so they are excluded from the query
    // The blocked aggregated data is generated on runtime and not fetched from the server
    if (
        !propertyName.startsWith('@root.') &&
        propertyName.indexOf(`${BLOCK_AGGREGATION_PROPERTY_NAME}.`) !== 0 &&
        propertyName !== 'pageNumberTotal' &&
        propertyName !== 'pageNumberCurrent' &&
        propertyName !== 'pageNumberFullMessage'
    ) {
        setProperty(result, propertyName);
    }

    return result;
}

function processNode(
    node: Element,
    isMainElementLevel = false,
    locale?: xtremReporting.enums.ReportLocale,
): JsonObject {
    if (!node) return {};
    const contextCondition = node.getAttribute?.(ATTR_CONTEXT_CONDITION) || '';

    let result: JsonObject = {};
    if (hasClass(node, RECORD_CONTEXT_CLASS) || hasClass(node, QUERY_TABLE_CLASS)) {
        result = deepMerge(result, processContextNode(node, isMainElementLevel, locale));
    } else if (hasClass(node, PROPERTY_CLASS)) {
        result = deepMerge(result, processPropertyNode(node));
    } else if (contextCondition) {
        result = deepMerge(result, processConditionBlockNode(node, locale));
    } else {
        result = deepMerge(result, processChildren(node, locale));
    }

    return result;
}

function extractKey(input: string): string {
    const regex = /^(query|edges|node)/;
    const match = input.match(regex);

    if (match) {
        return match[0];
    }
    return input;
}

function createObject(
    parts: string[],
    node: Element,
    isMainElementLevel = false,
    locale?: xtremReporting.enums.ReportLocale,
): JsonObject {
    const contextFilter = node.getAttribute(ATTR_CONTEXT_FILTER) || '';
    const contextSort = node.getAttribute(ATTR_CONTEXT_LIST_ORDER) || '';
    const alias = node.getAttribute(ATTR_ALIAS);

    if (parts.length === 0) {
        return processChildren(node);
    }

    const [first, ...rest] = parts;
    let key = extractKey(first);
    const args: JsonObject = {};

    if (key === 'query' && contextFilter) {
        const parsedContextFilter = parseContextFilter(contextFilter);
        if (parsedContextFilter) {
            const formattedLocale = locale ? (convertLocaleToHyphenFormat(locale) as LocalizeLocale) : undefined;
            const filterObject = getFilterObject({ filters: parsedContextFilter, locale: formattedLocale });
            set(args, '__args.filter', JSON.stringify(filterObject));
        }
    }

    if (key === 'query' && contextSort) {
        const sortObject = JSON.parse(contextSort.replace(/&quot;/g, '"'));

        const orderBy = JSON.stringify(
            Object.keys(sortObject).reduce(
                (prevValue: Dict<1 | -1>, orderByKey: string) => {
                    set(prevValue, orderByKey, sortObject[orderByKey] === 'ascending' ? 1 : -1);
                    return { ...prevValue };
                },
                {} as Dict<1 | -1>,
            ),
        );

        set(args, '__args.orderBy', orderBy);
    }

    // In case of a single record context element, only one record should be fetched
    if (key === 'query' && node.nodeName === 'section') {
        set(args, '__args.first', 1);
    }

    const childObject = createObject(rest, node, undefined, locale);
    if (key === 'edges' && !childObject.node) {
        // Hack to ensure that we always add the `node` sub-selector when we query a collection with `edges`
        return { [key]: { ...args, node: { _id: true, ...childObject } } };
    }

    if (key === 'node' && !childObject._id) {
        childObject._id = true;
    }

    // We set the alias if the child object structure is *.query.edges
    if (alias && isMainElementLevel && key !== 'node') {
        set(args, '__aliasFor', key);
        key = alias;
    }

    return { [key]: { ...args, ...childObject } };
}

function processContextNode(
    node: Element,
    isMainElementLevel = false,
    locale?: xtremReporting.enums.ReportLocale,
): JsonObject {
    const pathString = node.getAttribute(ATTR_CONTEXT_OBJECT_PATH) || '';

    const pathArray: string[] = pathString
        .split('.')
        // Filter out array keys
        .filter(v => parseInt(v, 10).toString() !== v);

    return createObject(pathArray, node, isMainElementLevel, locale);
}

function graphqlFromHtmlGenerator(
    bodyValue: string,
    headerValue?: string,
    footerValue?: string,
    locale?: xtremReporting.enums.ReportLocale,
): string {
    const parser = new DOMParser();
    // We need to wrap the DOM structure into a container to ensure we have a single root element.
    const doc = parser.parseFromString(
        `<div>
    ${headerValue || ''}
    ${bodyValue}
    ${footerValue || ''}

    </div>`,
        MIME_TYPE.HTML,
    );

    if (!doc?.documentElement) {
        // It's a light valid query, we need it so the preview generation won't fail
        return '{__typename}';
    }

    const queryJSON = processNode(doc.documentElement as unknown as Element, undefined, locale);
    return jsonToGraphQLQuery({ query: queryJSON }, { pretty: true });
}

export default graphqlFromHtmlGenerator;
