import { ConfigManager, Context, monitoredFunnel } from '@sage/xtrem-core';
import { generatePdfBuffer } from '@sage/xtrem-pdf-generator';
import * as xtremReporting from '../index';

const pdfGenerationFunnel = monitoredFunnel('pdfGeneration', 1);

export interface GeneratePdfDataArgs {
    attachmentMimeType?: string;
    attachmentName?: string;
    bottomMarginCm?: number;
    context: Context;
    leftMarginCm?: number;
    outputPath?: string;
    pageOrientation: xtremReporting.enums.ReportPageOrientation;
    paperFormat: xtremReporting.enums.ReportPaperFormat;
    populatedAttachment?: string;
    populatedBodyContent: string;
    populatedFooterContent?: string;
    populatedHeaderContent?: string;
    reportObject?: xtremReporting.functions.ReportObject;
    reportName: string;
    rightMarginCm?: number;
    topMarginCm?: number;
    isDefaultHeaderFooter?: boolean;
    documentTitle?: string;
    isSample?: boolean;
    additionalParameters?: xtremReporting.functions.PrePostProcessingOperationAdditionalParameters;
}

interface MaxTotalPagesReachedError extends Error {
    cause?: {
        code?: string;
        estimatedTotalPages?: number;
        maxTotalPages?: number;
    };
}
function isMaxTotalPagesReachedError(error: unknown): error is MaxTotalPagesReachedError {
    if (!(error instanceof Error)) {
        return false;
    }
    if (!error.cause || typeof error.cause !== 'object') {
        return false;
    }
    return (
        'code' in error.cause &&
        error.cause.code === 'max-total-pages-reached' &&
        'estimatedTotalPages' in error.cause &&
        'maxTotalPages' in error.cause
    );
}

const _generatePdfData = async (args: GeneratePdfDataArgs): Promise<Buffer> => {
    try {
        return await pdfGenerationFunnel(() =>
            generatePdfBuffer({
                productName: args.context.configuration.getProductName(),
                reportName: args.reportName,
                populatedBodyContent: args.populatedBodyContent,
                populatedHeaderContent: args.populatedHeaderContent,
                populatedFooterContent: args.populatedFooterContent,
                paperFormat: args.paperFormat,
                pageOrientation: args.pageOrientation,
                leftMarginCm: args.leftMarginCm,
                rightMarginCm: args.rightMarginCm,
                topMarginCm: args.topMarginCm,
                bottomMarginCm: args.bottomMarginCm,
                isDefaultHeaderFooter: !!args.isDefaultHeaderFooter,
                documentTitle: args.documentTitle || '',
                reportOptions: ConfigManager.current.reportOptions,
            }),
        );
    } catch (error) {
        if (isMaxTotalPagesReachedError(error)) {
            // If the error is due to the maximum number of pages reached, we throw a specific error
            throw args.context.businessRuleError({
                key: '@sage/xtrem-reporting/functions__pdf-utils__max-total-pages-reached',
                message:
                    'This report exceeds the maximum page limit: {{maxTotalPages}}. Try to reduce the current number of estimated pages: ({{estimatedTotalPages}}).',
                data: {
                    estimatedTotalPages: error.cause?.estimatedTotalPages,
                    maxTotalPages: error.cause?.maxTotalPages,
                },
                innerError: error,
            });
        } else {
            // If the error is not related to the maximum number of pages, we rethrow it
            throw error;
        }
    }
};

export const generatePdfData = async (args: GeneratePdfDataArgs): Promise<Buffer> => {
    try {
        const buffer = await _generatePdfData(args);

        if (args.reportObject && !args.isSample) {
            await xtremReporting.functions.runPrePostProcessingOperation(
                args.context,
                args.reportObject,
                'postProcessing',
                args.additionalParameters,
            );
        }

        return buffer;
    } catch (error) {
        if (args.reportObject && !args.isSample) {
            await xtremReporting.functions.runPrePostProcessingOperation(
                args.context,
                args.reportObject,
                'postProcessingError',
                {
                    error,
                },
            );
        }

        // Let the framework handle the error after the applicative post-processing has finished
        throw error;
    }
};
