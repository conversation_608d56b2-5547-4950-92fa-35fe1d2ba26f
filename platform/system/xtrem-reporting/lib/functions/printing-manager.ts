/* eslint-disable class-methods-use-this */
import type { Context } from '@sage/xtrem-core';
import { PrintingManager as IPrintingManager } from '@sage/xtrem-core';
import * as xtremReporting from '..';

export class PrintingManager implements IPrintingManager {
    private isEnabled(context: Context): Promise<boolean> {
        return context.isServiceOptionEnabled(xtremReporting.serviceOptions.reportAssignment);
    }

    async getRecordPrintingGlobalBulkMutationName(context: Context): Promise<string | null> {
        const isEnabled = await this.isEnabled(context);
        if (!isEnabled) {
            return null;
        }
        return 'printRecords';
    }

    async getRecordListPrintingGlobalBulkActionConfigurationURL(context: Context): Promise<string | null> {
        const isEnabled = await this.isEnabled(context);
        if (!isEnabled) {
            return null;
        }

        return '@sage/xtrem-reporting/BulkPrintingDialog';
    }

    async hasRecordPrintingTemplates(context: Context, page: string): Promise<boolean> {
        const isEnabled = await this.isEnabled(context);
        if (!isEnabled) {
            return false;
        }

        // TODO: Implement a check to see if the page has record printing templates.
        console.log(page);
        return true;
    }

    async getPrintingAssignmentDialogUrl(context: Context): Promise<string | null> {
        const isEnabled = await this.isEnabled(context);
        if (!isEnabled) {
            return null;
        }
        return '@sage/xtrem-reporting/PrintingAssignmentDialog';
    }

    async getListPrintingWizardUrl(context: Context): Promise<string | null> {
        const isEnabled = await this.isEnabled(context);
        if (!isEnabled) {
            return null;
        }

        return '@sage/xtrem-reporting/ListPrintingWizard';
    }

    async getRecordPrintingWizardUrl(context: Context): Promise<string | null> {
        const isEnabled = await this.isEnabled(context);
        if (!isEnabled) {
            return null;
        }

        return '@sage/xtrem-reporting/RecordPrintingWizard';
    }
}
