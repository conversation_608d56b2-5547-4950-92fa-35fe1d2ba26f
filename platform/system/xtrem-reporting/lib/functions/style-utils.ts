import { asyncArray, Context } from '@sage/xtrem-core';
import * as lodash from 'lodash';
import * as xtremReporting from '../index';
import * as fs from 'fs/promises';
import * as path from 'path';

/**
 * This function builds an HTML <style> section to initialize CSS style variables for the reporting template.
 * It includes all of the variables defined in the ReportStyleVariables table.
 *
 * @param context A context object from the application calling this function.
 *
 * @return A snippet of HTML declaring the CSS style variables.
 */
export async function resolveStyleVariables(context: Context): Promise<string | null> {
    const ctx = context;
    const styleVariables = await ctx.query(xtremReporting.nodes.ReportStyleVariable).toArray();
    if (styleVariables) {
        const variableLines = await asyncArray(styleVariables)
            .sort(async (v1, v2) => (await v1.name).localeCompare(await v2.name))
            .reduce(async (previousValue, currentValue) => {
                const formattedValue = lodash.trimStart(lodash.trimEnd((await currentValue.value).trim(), '"'), '"');
                return `${previousValue}\n                --${await currentValue.name}: ${formattedValue};`;
            }, '');

        return `:root {${variableLines}
            }`;
    }

    return null;
}

export function getDocumentEditorStyleSheet(): Promise<string> {
    const packageJsonPath = require.resolve('@sage/xtrem-document-editor/package.json');
    const styleSheetFilePath = path.resolve(path.dirname(packageJsonPath), 'build', 'xtrem-document-editor.css');
    return fs.readFile(styleSheetFilePath, 'utf-8');
}
