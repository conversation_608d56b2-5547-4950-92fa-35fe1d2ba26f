import * as xtremCommunication from '@sage/xtrem-communication';
import {
    AnyValue,
    asyncArray,
    BusinessRuleError,
    Context,
    decorators,
    Diagnose,
    Logger,
    Node,
    OperationError,
    OperationReturn,
    StaticThis,
    SystemError,
    ValidationSeverity,
} from '@sage/xtrem-core';
import * as xtremMetadata from '@sage/xtrem-metadata';
import { pascalCase } from '@sage/xtrem-shared';
import * as xtremUpload from '@sage/xtrem-upload';
import * as xtremReporting from '..';
import { ReportGenerationSettings } from './html-document-builder';

const logger = new Logger(__filename, 'pre-post-processing');

export enum OperationType {
    mutation,
    query,
}

export enum AsyncOperationRequestType {
    start,
    track,
}

export enum PreProcessingOperationManageableSettings {
    watermark,
}

export interface PrePostProcessingOperationAdditionalParameters {
    uploadedFile?: xtremUpload.nodes.UploadedFile;
    error?: Error;
}

// Defined in xtrem-communication
export type AsyncMutationStatus = 'pending' | 'running' | 'success' | 'error';

export type OperationDetails = {
    instance: xtremMetadata.nodes.MetaNodeOperation;
    package: string;
    node: string;
    name: string;
    title: string;
    isNotAsync: boolean;
    type: keyof typeof OperationType;
    phase: xtremReporting.enums.ReportProcessingPhase;
};

type AsyncOperationReturnType = Parameters<typeof decorators.asyncMutation>[0]['return'];

export type AsyncOperationTrackResultType<ResultType extends AnyValue> = {
    status: AsyncMutationStatus;
    result: ResultType;
    errorMessage: string;
    logMessages: {
        level: string;
        message: string;
    }[];
};

export type AsyncOperationResultType<
    RequestType extends AsyncOperationRequestType,
    ResultType extends AnyValue = never,
> = {
    [PackageName: string]: {
        [NodeName: string]: {
            [OperationName: string]: (RequestType extends AsyncOperationRequestType.start
                ? {
                      start: {
                          trackingId: string;
                      };
                  }
                : {}) &
                (RequestType extends AsyncOperationRequestType.track
                    ? {
                          track: AsyncOperationTrackResultType<ResultType>;
                      }
                    : {});
        };
    };
};

export type ClassicOperationResultType<ResultType extends AnyValue = never> = {
    [PackageName: string]: {
        [NodeName: string]: {
            [OperationName: string]: ResultType;
        };
    };
};

export type PreProcessingOperationManageableSettingsType = keyof typeof PreProcessingOperationManageableSettings;
export type ReportManageableSettings = Partial<Record<PreProcessingOperationManageableSettingsType, any>>;

export function extractManageableSettingsFromPreProcessingOperationResult(
    operationResult: any,
): ReportManageableSettings {
    const manageableSettings: ReportManageableSettings = {};
    const allowedKeys = Object.values(PreProcessingOperationManageableSettings);

    Object.keys(operationResult).forEach(key => {
        if (allowedKeys.includes(key as PreProcessingOperationManageableSettingsType)) {
            manageableSettings[key as PreProcessingOperationManageableSettingsType] = operationResult[key];
        }
    });

    return manageableSettings;
}

export function buildGraphqlBody({
    operationDetails,
    operationType,
    operationAction,
    operationParameters,
    operationSelector,
}: {
    operationDetails: Omit<OperationDetails, 'instance' | 'phase' | 'title'>;
    operationType?: keyof typeof OperationType;
    operationAction?: string | null;
    operationParameters?: string | null;
    operationSelector?: string | null;
}): string {
    const parameters = operationParameters ? ` (${operationParameters})` : '';
    const selector = operationSelector ? ` ${operationSelector}` : '';

    let formattedBody = '';

    if (operationAction) {
        formattedBody = ` { ${operationAction}${parameters}${selector} }`;
    } else {
        formattedBody = `${parameters}${selector}`;
    }

    return `${operationType ?? operationDetails.type} { ${operationDetails.package} { ${operationDetails.node} { ${operationDetails.name}${formattedBody} } } }`;
}

function appendParameter(parameterName: string, parameterValue: any, populatedParameters?: string) {
    const graphqlReadyValue = Number(parameterValue) ? parameterValue : JSON.stringify(parameterValue);

    if (populatedParameters && populatedParameters.length > 0) {
        return `${populatedParameters}, ${parameterName}: ${graphqlReadyValue}`;
    }
    return `${parameterName}: ${graphqlReadyValue}`;
}

function isKeyofObj<T extends object>(key: any, obj: T): key is keyof T {
    return key in obj;
}

export async function populateOperationParametersAccordingToSignature(
    operationDetails: Pick<OperationDetails, 'instance'>,
    settings: ReportGenerationSettings,
    additionalParameters?: PrePostProcessingOperationAdditionalParameters,
): Promise<string | undefined> {
    const operationSignature = await operationDetails.instance?.signature;
    let populatedParameters: string | undefined;

    if (operationSignature) {
        const operationParameters = operationSignature.parameters;

        operationParameters.forEach(parameter => {
            const parameterName = parameter.name;

            let parameterValue = settings.variables?.[parameterName];

            if (additionalParameters && isKeyofObj(parameterName, additionalParameters)) {
                const additionalParameterValue = additionalParameters[parameterName];

                if (parameterName === 'error') {
                    parameterValue = {
                        message: (additionalParameterValue as Error).message,
                    };
                } else if (parameter.type === 'reference' && additionalParameterValue instanceof Node) {
                    parameterValue = additionalParameterValue._id;
                } else {
                    parameterValue = additionalParameterValue;
                }
            }

            if (parameterValue) {
                populatedParameters = appendParameter(parameterName, parameterValue, populatedParameters);
            }
        });
    }

    return populatedParameters;
}

function formatReturnSelectorObject(selectorToFormat: OperationReturn<StaticThis<Node>, any>): object | true {
    if (typeof selectorToFormat === 'object') {
        switch (selectorToFormat.type) {
            case 'object':
                return Object.keys(selectorToFormat.properties).reduce(
                    (formatted, key) => ({
                        ...formatted,
                        [key]: formatReturnSelectorObject(
                            selectorToFormat.properties[
                                key as keyof typeof selectorToFormat.properties
                            ] as OperationReturn<StaticThis<Node>, any>,
                        ),
                    }),
                    {},
                );
            case 'reference':
                return {
                    _id: true,
                };
            default:
                return true;
        }
    }
    return true;
}

export async function populateGraphQLResultSelectorAccordingToSignature(
    operationDetails: {
        instance?: OperationDetails['instance'] | null;
    } & Pick<OperationDetails, 'isNotAsync'>,
): Promise<string> {
    const operationSignature = await operationDetails.instance?.signature;

    if (operationSignature) {
        const operationReturnTypeDefinition = operationSignature.return as
            | AsyncOperationReturnType
            | OperationReturn<StaticThis<Node>, any>;

        if (typeof operationReturnTypeDefinition === 'object' && operationReturnTypeDefinition.type === 'object') {
            let operationResultTypeDefinition: OperationReturn<StaticThis<Node>, any>;

            if (operationDetails.isNotAsync) {
                operationResultTypeDefinition = operationReturnTypeDefinition as OperationReturn<StaticThis<Node>, any>;
            } else {
                operationResultTypeDefinition = operationReturnTypeDefinition.properties?.result as OperationReturn<
                    StaticThis<Node>,
                    any
                >;
            }

            const selector = formatReturnSelectorObject(operationResultTypeDefinition);

            // JSON stringify the selector and remove the boolean values from the selector
            const jsonSelectorObject = JSON.stringify(selector, (_key: string, value: any) => {
                if (typeof value === 'boolean') {
                    return '';
                }
                return value;
            });

            return jsonSelectorObject.replaceAll(/[":,]*/g, '');
        }
    }

    return '';
}

/**
 * A function that starts an async operation.
 *
 * @param context A Context object that describes the current context.
 * @param operationDetails An object that contains the package, node, and name of the operation.
 * @param operationType A string value that contains the type of the operation.
 *
 * @returns A string value that contains the tracking ID of the async operation.
 */
export async function startAsyncOperation(
    context: Context,
    args: {
        operationDetails: Omit<OperationDetails, 'instance'>;
        parameters?: string;
    },
): Promise<string> {
    const operationBody = buildGraphqlBody({
        operationDetails: args.operationDetails,
        operationAction: 'start',
        operationParameters: args.parameters,
        operationSelector: '{ trackingId }',
    });

    return (await context.executeGraphql<AsyncOperationResultType<AsyncOperationRequestType.start>>(operationBody))[
        args.operationDetails.package
    ][args.operationDetails.node][args.operationDetails.name].start.trackingId;
}

const sleepMillis = (millis: number) =>
    new Promise(resolve => {
        setTimeout(resolve, millis);
    });

/**
 * A function that handle tracking for async graphql operation completion.
 *
 * @param context A Context object that describes the current context.
 * @param trackingId A string value that contains the tracking ID of the async operation.
 * @param operationDetails An object that contains the package, node, and name of the operation.
 * @param operationType A string value that contains the type of the operation.
 *
 * @returns The result of the async operation.
 */
export async function waitForAsyncOperationCompletion<ResultType extends AnyValue>(
    context: Context,
    args: {
        trackingId: string;
        operationDetails: Omit<OperationDetails, 'instance'>;
        resultSelector: string;
    },
): Promise<ResultType> {
    let running = true;
    let pollCount = 1;
    let responseData: AsyncOperationTrackResultType<ResultType> | undefined;

    const startMillis = Date.now();

    while (running) {
        const currentResponseData = (
            await context.select(
                xtremCommunication.nodes.SysNotificationState,
                {
                    status: true,
                    result: true,
                    message: true,
                    logs: {
                        level: true,
                        message: true,
                    },
                },
                {
                    filter: {
                        notificationId: args.trackingId,
                    },
                    first: 1,
                },
            )
        ).at(0);

        logger.debug(
            () =>
                `Tracking ${args.operationDetails.phase} operation: ${args.operationDetails.name}, result: ${JSON.stringify(currentResponseData)}`,
        );

        if (currentResponseData) {
            const exception = currentResponseData.logs.find(log => log.level === 'exception');
            const errors = currentResponseData.logs.filter(log => log.level === 'error');
            switch (currentResponseData.status) {
                case 'pending':
                case 'running':
                    break;
                case 'success':
                    responseData = {
                        status: currentResponseData.status,
                        result: currentResponseData.result as ResultType,
                        errorMessage: currentResponseData.message || '',
                        logMessages: currentResponseData.logs,
                    };
                    running = false;
                    break;
                case 'error':
                    // exception: severity = 4
                    if (exception) {
                        throw new BusinessRuleError(exception.message);
                    }
                    // error: severity = 3
                    if (errors.length > 0) {
                        const diagnoses = errors.map(log => new Diagnose(ValidationSeverity.error, [], log.message));
                        const factory = context.application.getFactoryByName(pascalCase(args.operationDetails.node));
                        throw new OperationError(context, factory, args.operationDetails.name, diagnoses);
                    }
                    throw new Error(
                        `${currentResponseData.message || '<unknown error>'} tracking ${args.operationDetails.name} operation.`,
                    );
                default:
                    throw new Error(
                        `invalid status: ${currentResponseData.status} received while tracking ${args.operationDetails.name} operation.`,
                    );
            }

            // Ramp up sleep time from 100 to 1000 and then remain at 1000.
            await sleepMillis(Math.min(pollCount * 100, 1000));
            pollCount += 1;

            if (startMillis + 300000 < Date.now()) {
                throw new Error(`polling cancelled after 5 minutes tracking ${args.operationDetails.name} operation.`);
            }
        } else {
            throw new Error(
                `polling cancelled, invalid response data received from ${args.operationDetails.name} operation: ${JSON.stringify(responseData)}.`,
            );
        }
    }

    if (!responseData) {
        throw new Error(`polling cancelled, unable to track ${args.operationDetails.name} operation.`);
    }

    return responseData?.result;
}

/**
 * A function that runs an async operation.
 *
 * @param context A Context object that describes the current context.
 * @param operationDetails An object that contains the package, node, and name of the operation.
 * @param operationType A string value that contains the type of the operation.
 *
 * @return the result of the async operation.
 */
export async function runAsyncOperation<ResultType extends AnyValue>(
    context: Context,
    args: {
        operationDetails: OperationDetails;
        settings: ReportGenerationSettings;
        additionalParameters?: PrePostProcessingOperationAdditionalParameters;
    },
): Promise<ResultType> {
    if (!(await context.isServiceOptionEnabled(xtremReporting.serviceOptions.asyncPerPostProcessing))) {
        const warningMessage = 'Skipping async pre/post operation as this feature is not activated';

        logger.warn(() => warningMessage);

        if (context.batch) {
            await context.batch.logMessage('warning', warningMessage);
        }

        return true as ResultType;
    }

    const trackingId = await startAsyncOperation(context, {
        operationDetails: args.operationDetails,
        parameters: await populateOperationParametersAccordingToSignature(
            args.operationDetails,
            args.settings,
            args.additionalParameters,
        ),
    });

    const resultSelector = await populateGraphQLResultSelectorAccordingToSignature(args.operationDetails);

    return waitForAsyncOperationCompletion<ResultType>(context, {
        trackingId,
        operationDetails: args.operationDetails,
        resultSelector,
    });
}

/**
 * Runs a classic operation (mutation or query) and returns the result.
 *
 * @param context - A Context object that describes the current context.
 * @param args - An object that contains the operation details, operation type, settings, and additional parameters.
 * @returns A promise that resolves to the result of the operation.
 *
 **/
export async function runClassicOperation<ResultType extends AnyValue>(
    context: Context,
    args: {
        operationDetails: OperationDetails;
        settings: ReportGenerationSettings;
        additionalParameters?: PrePostProcessingOperationAdditionalParameters;
    },
): Promise<ResultType> {
    const operationParameters = await populateOperationParametersAccordingToSignature(
        args.operationDetails,
        args.settings,
        args.additionalParameters,
    );

    const operationSelector = await populateGraphQLResultSelectorAccordingToSignature(args.operationDetails);

    const operationBody = buildGraphqlBody({
        operationDetails: args.operationDetails,
        operationParameters,
        operationSelector,
    });

    logger.debug(
        () =>
            `Report ${args.operationDetails.phase} classic operation (${args.operationDetails.name}) requested with body: ${JSON.stringify(operationBody)}`,
    );

    return (await context.executeGraphql<ClassicOperationResultType<ResultType>>(operationBody))[
        args.operationDetails.package
    ][args.operationDetails.node][args.operationDetails.name];
}

export async function getOperationDetails(
    operation: xtremMetadata.nodes.MetaNodeOperation,
    phase: xtremReporting.enums.ReportProcessingPhase,
): Promise<OperationDetails> {
    const graphqlMutationName = await operation.name;
    const graphqlNodeName = (await (await operation.factory).name).replace(/^[A-Z]/, char => char.toLowerCase());
    const graphqlPackageName = (await (await operation.package).name)
        .replace('@sage/', '')
        .replace(/-([a-z])/g, (_, letter) => letter.toUpperCase());

    return {
        instance: operation,
        name: graphqlMutationName,
        node: graphqlNodeName,
        package: graphqlPackageName,
        title: await operation.title,
        isNotAsync: ['mutation', 'query'].includes(await operation.kind),
        type: (await operation.kind) === 'query' ? 'query' : 'mutation',
        phase,
    };
}

export async function checkPrePostOperationAreOnlyUsedInReadOnlyContext(
    context: Context,
    reportObject: xtremReporting.functions.ReportObject,
) {
    if (reportObject.instance) {
        const prePostProcessingOperationReference = await reportObject.instance.preProcessingOperation;
        const postProcessingOperationReference = await reportObject.instance.postProcessingOperation;

        if (context.isWritable && (prePostProcessingOperationReference || postProcessingOperationReference)) {
            throw new SystemError(
                'Pre/Post processing operations are not allowed in a writable context. Please use a read-only context.',
            );
        }
    }
}

function callOperationUsingAppropriateMechanism(
    context: Context,
    {
        operationDetails,
        settings,
        additionalParameters,
    }: {
        operationDetails: OperationDetails;
        settings: ReportGenerationSettings;
        additionalParameters?: PrePostProcessingOperationAdditionalParameters;
    },
) {
    if (operationDetails.isNotAsync) {
        return runClassicOperation(context, {
            operationDetails,
            settings,
            additionalParameters,
        });
    }

    return runAsyncOperation(context, {
        operationDetails,
        settings,
        additionalParameters,
    });
}

/**
 * Runs the given pre or post processing operation for a report printing.
 *
 * @param context - A Context object that describes the current context.
 * @param reportObject - An object that contains the report settings and instance.
 * @param prePostProcessingOperation - An optional MetaNodeOperation object that defines the pre or post processing operation.
 * @returns A promise that resolves to an array of results from the pre/post processing operations, or undefined if no operation was provided.
 *
 * @throws Will throw an error if the operation fails.
 */
export async function runPrePostProcessingOperation<
    Phase extends xtremReporting.enums.ReportProcessingPhase,
    ReturnType = Phase extends 'preProcessing' ? ReportManageableSettings : boolean,
>(
    context: Context,
    reportObject: xtremReporting.functions.ReportObject,
    reportProcessingPhase: Phase,
    additionalParameters?: PrePostProcessingOperationAdditionalParameters,
): Promise<ReturnType[]> {
    if (reportObject.instance) {
        const prePostProcessingOperation =
            reportProcessingPhase === 'preProcessing'
                ? await reportObject.instance.preProcessingOperation
                : await reportObject.instance.postProcessingOperation;

        if (prePostProcessingOperation) {
            const operationDetails = await getOperationDetails(prePostProcessingOperation, reportProcessingPhase);

            const reportSettingPerPrint = asyncArray(reportObject.settings);
            const prePostProcessMutationResultPerPrint = await reportSettingPerPrint
                .map(async settings => {
                    const result = await callOperationUsingAppropriateMechanism(context, {
                        operationDetails,
                        settings,
                        additionalParameters,
                    });
                    const jsonResult = JSON.stringify(result);

                    logger.debug(
                        () =>
                            `Report ${reportProcessingPhase} processing operation (${operationDetails.name}) finished with result: ${jsonResult}`,
                    );

                    if (reportProcessingPhase === 'preProcessing') {
                        return extractManageableSettingsFromPreProcessingOperationResult(result);
                    }

                    return true;
                })
                .toArray();

            return prePostProcessMutationResultPerPrint as ReturnType[];
        }
    }

    return [];
}
