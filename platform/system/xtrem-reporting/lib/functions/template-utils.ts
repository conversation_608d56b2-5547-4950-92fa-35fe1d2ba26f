import * as handlebarsHelpers from '@sage/handlebars-helpers';
import { Context, Dict } from '@sage/xtrem-core';
import { DatePresetFormat, formatDateToCurrentLocale } from '@sage/xtrem-date-time';
import { Decimal, div, max, min, sum } from '@sage/xtrem-decimal';
import { getLiteral } from '@sage/xtrem-i18n';
import { reportHistogram } from '@sage/xtrem-pdf-generator';
import {
    BLOCK_AGGREGATION_PROPERTY_NAME,
    BLOCK_AGGREGATION_ROLLING_PROPERTY_NAME,
    DataInputError,
    LocalizeLocale,
    withRethrow,
} from '@sage/xtrem-shared';
import * as xmldom from '@xmldom/xmldom';
import * as handlebars from 'handlebars';
import * as handlebarsUtils from 'handlebars-utils';
import * as jsbarcode from 'jsbarcode';
import {
    chunk,
    get,
    isArray,
    isEmpty,
    isFunction,
    isNumber,
    isString,
    isUndefined,
    set,
    snakeCase,
    uniq,
} from 'lodash';
import { PNG } from 'pngjs';
import * as qrcodeLib from 'qrcode';
import * as xtremReporting from '../index';

interface ResolvedResource {
    mimetype: string;
    content: any;
    name: string;
}

interface HandlebarsAstPosition {
    line: number;
    column: number;
}
interface HandlebarsOptions extends handlebars.HelperOptions {
    loc: { start: HandlebarsAstPosition; end: HandlebarsAstPosition };
}

const substringByPositions = (
    input: string,
    startLine: number,
    startChar: number,
    endLine: number,
    endChar: number,
): string => {
    // Split the input string into lines
    const lines = input.split('\n');

    // Validate input positions
    if (
        startLine < 1 ||
        startLine > lines.length ||
        endLine < 1 ||
        endLine > lines.length ||
        startChar < 0 ||
        endChar < 0 ||
        startChar > lines[startLine - 1].length ||
        endChar > lines[endLine - 1].length
    ) {
        throw new Error('Invalid line or character positions');
    }

    // Extract the substring based on provided positions
    const startIdx = lines.slice(0, startLine - 1).reduce((acc, line) => acc + line.length + 1, 0) + startChar;
    const endIdx =
        lines.slice(0, endLine).reduce((acc, line) => acc + line.length + 1, 0) - (lines[endLine - 1].length - endChar);

    return input.substring(startIdx, endIdx);
};

const calculateAggregatedData = (context: any, requestedAggregatedData: any): any => {
    return requestedAggregatedData.reduce((prevValue: any, pair: [string, string]) => {
        const [propertyPath, operation] = pair;
        let aggregatedTotal;
        if (isArray(context)) {
            let decimalValues: Decimal[];
            let stringOrDateValues: any[];
            let valuesToUse;
            switch (operation) {
                case 'count':
                case 'distinctCount':
                    aggregatedTotal = uniq(context.map(node => get(node, propertyPath))).length;
                    break;
                case 'average':
                case 'avg':
                case 'min':
                case 'max':
                case 'sum':
                    decimalValues = context.reduce((acc: Decimal[], node) => {
                        const value = get(node, propertyPath);
                        if (isNumeric(value)) {
                            acc.push(Decimal.make(value));
                        }
                        return acc;
                    }, []);
                    stringOrDateValues = context
                        .map(node => get(node, propertyPath))
                        .filter(value => typeof value === 'string' || value instanceof Date);

                    if (decimalValues.length > 0) {
                        valuesToUse = decimalValues;
                    } else {
                        valuesToUse = stringOrDateValues;
                    }

                    if (valuesToUse.length === 0) {
                        aggregatedTotal = undefined;
                    } else {
                        aggregatedTotal = calculateAggregatedValue(operation, valuesToUse);
                    }
                    break;
                default:
                    aggregatedTotal = undefined;
            }
        }
        set(prevValue, `${propertyPath}.${operation}`, aggregatedTotal);
        return prevValue;
    }, {} as any);
};

const calculateAggregatedValue = (operation: string, valuesToUse: any[]) => {
    const areAllValuesDecimal = !valuesToUse.find(v => !Decimal.isDecimal(v));

    switch (operation) {
        case 'average':
        case 'avg':
            return div(sum(...valuesToUse), valuesToUse.length);
        case 'min':
            if (areAllValuesDecimal) {
                return min(...valuesToUse);
            }
            return [...valuesToUse].sort()[0];
        case 'max':
            if (areAllValuesDecimal) {
                return max(...valuesToUse);
            }
            return [...valuesToUse].sort().reverse()[0];
        case 'sum':
            return sum(...valuesToUse);
        default:
            return undefined;
    }
};

const isNumeric = (value: any): boolean => {
    // eslint-disable-next-line no-restricted-globals
    return (typeof value === 'number' || (typeof value === 'string' && value.trim() !== '')) && !isNaN(value as any);
};

const resolveReportResources = async (context: Context, template?: string | null): Promise<Dict<ResolvedResource>> => {
    if (!template) {
        return {};
    }

    const templateRegex = /\{\{reportResource\s+'([a-zA-Z0-9\s]+)'/g;

    const result = Array.from(template.matchAll(templateRegex)).map(a => a[1]);
    if (result.length === 0) {
        return {};
    }

    const resources = await context
        .query(xtremReporting.nodes.ReportResource, { filter: { name: { _in: result } } })
        .toArray();
    const pendingResources = resources.map(
        async (r: xtremReporting.nodes.ReportResource): Promise<ResolvedResource> => {
            return {
                name: await r.name,
                content: await r.content,
                mimetype: await r.mimetype,
            };
        },
    );

    const resolvedResources = await Promise.all(pendingResources);

    return resolvedResources.reduce(
        (prevValue: Dict<ResolvedResource>, current: ResolvedResource) => ({ ...prevValue, [current.name]: current }),
        {} as Dict<ResolvedResource>,
    );
};

// THIS FUNCTION WAS COPIED FROM HANDLEBARS SOURCE CODE, ONLY THE ITERATION CONTEXT WAS UPDATED TO PROVIDE ACCESS TO THE COMPLETE DATASET
// https://github.com/handlebars-lang/handlebars.js/blob/master/lib/handlebars/helpers/each.js
const each = function XtremEach(this: any, ...args: any) {
    const options = args.pop();
    let [context, ...requestedAggregatedData] = args;
    requestedAggregatedData = chunk(requestedAggregatedData, 2);

    if (!options) {
        // eslint-disable-next-line @typescript-eslint/no-throw-literal
        throw new handlebars.Exception('Must pass iterator to #each');
    }

    const fn = options.fn;
    const inverse = options.inverse;
    let i = 0;
    let ret = '';
    let data: any;

    if (isFunction(context)) {
        context = context.call(this);
    }

    if (options.data) {
        data = handlebars.createFrame(options.data);
    }

    const aggregatedData = calculateAggregatedData(context, requestedAggregatedData);

    function execIteration(field: any, index: number, last: boolean = false) {
        if (data) {
            data.key = field;
            data.index = index;
            data.first = index === 0;
            data.last = !!last;
            data.collection = context;
        }
        context[field].aggregatedData = aggregatedData;
        ret = `${ret}${fn(context[field], {
            data,
            fn,
            blockParams: [context[field], field],
        })}`;
    }

    if (context && typeof context === 'object') {
        if (isArray(context)) {
            for (let j = context.length; i < j; i += 1) {
                if (i in context) {
                    execIteration(i, i, i === context.length - 1);
                }
            }
        } else if (global.Symbol && context[(global.Symbol as any).iterator]) {
            const newContext = [];
            const iterator = context[(global.Symbol as any).iterator]();
            for (let it = iterator.next(); !it.done; it = iterator.next()) {
                newContext.push(it.value);
            }

            context = newContext;
            for (let j = context.length; i < j; i += 1) {
                execIteration(i, i, i === context.length - 1);
            }
        } else {
            let priorKey: any;

            Object.keys(context).forEach(key => {
                // We're running the iterations one step out of sync so we can detect
                // the last iteration without have to scan the object twice and create
                // an itermediate keys array.
                if (priorKey !== undefined) {
                    execIteration(priorKey, i - 1);
                }
                priorKey = key;
                i += 1;
            });
            if (priorKey !== undefined) {
                execIteration(priorKey, i - 1, true);
            }
        }
    }

    if (i === 0) {
        ret = inverse(this);
    }
    return ret;
};

// THIS FUNCTION WAS COPIED FROM HANDLEBARS SOURCE CODE, ONLY THE ITERATION CONTEXT WAS UPDATED TO PROVIDE ACCESS TO WITH-PATH IN WRAPPED CONTEXTS
const withFn = (originalTemplate: string) =>
    function XtremWidth(this: any, context: any, options: any) {
        const templateLiteral = substringByPositions(
            originalTemplate,
            options.loc.start.line,
            options.loc.start.column,
            options.loc.end.line,
            options.loc.end.column,
        );
        const _withPath = templateLiteral.match(/^\{\{#with\s([a-zA-Z0-9._$]+)\}\}/)?.[1];
        if (arguments.length !== 2) {
            // eslint-disable-next-line @typescript-eslint/no-throw-literal
            throw new handlebars.Exception('#with requires exactly one argument');
        }

        const fn = options.fn;

        if (!isEmpty(context)) {
            const data = { ...options.data, _withPath };

            return fn(context, {
                data,
                blockParams: [context],
            });
        }

        return options.inverse(this);
    };

const barcode = (data: string, height: number) => {
    if (!data || !height) {
        // eslint-disable-next-line @typescript-eslint/no-throw-literal
        throw new handlebars.Exception('#barcode must have two arguments.');
    }
    const xmlSerializer = new xmldom.XMLSerializer();
    const xmlDocument = new xmldom.DOMParser().parseFromString('<html></html>', xmldom.MIME_TYPE.XML_TEXT);
    const svgNode = xmlDocument.createElementNS('http://www.w3.org/2000/svg', 'svg');
    jsbarcode(svgNode, data, { xmlDocument: xmlDocument as unknown as Document, height, displayValue: false });

    const svgText = xmlSerializer.serializeToString(svgNode);
    const buff = Buffer.from(svgText, 'utf8');
    return `data:image/svg+xml;base64,${buff.toString('base64')}`;
};

/**
 * @xtrem-decimal-ignore
 */
const qrcode = (data: string) => {
    const margin = 4;
    const size = 256;
    if (!data) {
        // eslint-disable-next-line @typescript-eslint/no-throw-literal
        throw new handlebars.Exception('#qrcode must have at least one argument.');
    }

    const qr = qrcodeLib.create(data, { errorCorrectionLevel: 'M' });
    const moduleCount = qr.modules.size;
    const cellSize = Math.floor((size - 2 * margin) / moduleCount);
    const pngSize = cellSize * moduleCount + 2 * margin;

    const png = new PNG({ width: pngSize, height: pngSize, colorType: 2 });

    // Fill background white
    for (let y = 0; y < pngSize; y += 1) {
        for (let x = 0; x < pngSize; x += 1) {
            const idx = (png.width * y + x) * 4;
            png.data[idx] = 255;
            png.data[idx + 1] = 255;
            png.data[idx + 2] = 255;
            png.data[idx + 3] = 255;
        }
    }

    // Draw QR modules (black pixels)
    for (let r = 0; r < qr.modules.size; r += 1) {
        for (let c = 0; c < qr.modules.size; c += 1) {
            if (qr.modules.get(c, r)) {
                const startX = margin + c * cellSize;
                const startY = margin + r * cellSize;

                for (let y = 0; y < cellSize; y += 1) {
                    for (let x = 0; x < cellSize; x += 1) {
                        const px = startX + x;
                        const py = startY + y;
                        const idx = (py * png.width + px) * 4;
                        png.data[idx] = 0;
                        png.data[idx + 1] = 0;
                        png.data[idx + 2] = 0;
                        png.data[idx + 3] = 255;
                    }
                }
            }
        }
    }

    // Return PNG buffer synchronously
    const buffer = PNG.sync.write(png);
    const base64encodedImage = buffer.toString('base64');
    return `data:image/png;base64,${base64encodedImage}`;
};

const enumValueHelper =
    (locale: xtremReporting.enums.ReportLocale = 'en_US') =>
    (enumName: string, memberName: string) => {
        if (!memberName) {
            return '';
        }
        const components = enumName.split('/');
        const key = `${components[0]}/${components[1]}/enums__${snakeCase(components[2])}__${memberName}`;
        const normalizedLocale = locale.split('_').join('-');

        try {
            // If enum or member name does not exist, the get literal function throws an exception.
            return getLiteral(key, normalizedLocale as LocalizeLocale).content;
        } catch {
            return memberName;
        }
    };

const formatNumber =
    (locale: xtremReporting.enums.ReportLocale) =>
    (value: number, scale: number = 0): string => {
        const fixedScale = isNumber(scale) ? scale : 0;
        const normalizedLocale = locale.split('_').join('-') as LocalizeLocale;
        return new Intl.NumberFormat(normalizedLocale, {
            minimumFractionDigits: fixedScale,
            maximumFractionDigits: fixedScale,
        }).format(value || 0);
    };
const formatDate =
    (locale: xtremReporting.enums.ReportLocale) =>
    (date: Date | string, format: DatePresetFormat): string => {
        if (!date) {
            return '';
        }
        const normalizedLocale = locale.split('_').join('-') as LocalizeLocale;
        return formatDateToCurrentLocale(date, normalizedLocale, format);
    };

const displayImage = (image: string) => {
    const imageData =
        image ??
        '/9j/4AAQSkZJRgABAQAASABIAAD/4QBMRXhpZgAATU0AKgAAAAgAAgESAAMAAAABAAEAAIdpAAQAAAABAAAAJgAAAAAAAqACAAQAAAABAAAABaADAAQAAAABAAAABAAAAAD/7QA4UGhvdG9zaG9wIDMuMAA4QklNBAQAAAAAAAA4QklNBCUAAAAAABDUHYzZjwCyBOmACZjs+EJ+/+ICIElDQ19QUk9GSUxFAAEBAAACEGFwcGwEAAAAbW50clJHQiBYWVogB+cACgALAAgANgAkYWNzcEFQUEwAAAAAQVBQTAAAAAAAAAAAAAAAAAAAAAAAAPbWAAEAAAAA0y1hcHBsQ9EzhxVMIyw1rCQclqpS3gAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAKZGVzYwAAAPwAAAA2Y3BydAAAATQAAABQd3RwdAAAAYQAAAAUclhZWgAAAZgAAAAUZ1hZWgAAAawAAAAUYlhZWgAAAcAAAAAUclRSQwAAAdQAAAAQY2hhZAAAAeQAAAAsYlRSQwAAAdQAAAAQZ1RSQwAAAdQAAAAQbWx1YwAAAAAAAAABAAAADGVuVVMAAAAaAAAAHABMAEcAIABJAFAAUwAgAEYAVQBMAEwASABEAABtbHVjAAAAAAAAAAEAAAAMZW5VUwAAADQAAAAcAEMAbwBwAHkAcgBpAGcAaAB0ACAAQQBwAHAAbABlACAASQBuAGMALgAsACAAMgAwADIAM1hZWiAAAAAAAAD21gABAAAAANMtWFlaIAAAAAAAAHFaAAA6SQAAAm1YWVogAAAAAAAAYFAAALjwAAAQY1hZWiAAAAAAAAAlLAAADMcAAMBdcGFyYQAAAAAAAAAAAAH2BHNmMzIAAAAAAAELtwAABZb///NXAAAHKQAA/df///u3///9pgAAA9oAAMD2/8AAEQgABAAFAwEiAAIRAQMRAf/EAB8AAAEFAQEBAQEBAAAAAAAAAAABAgMEBQYHCAkKC//EALUQAAIBAwMCBAMFBQQEAAABfQECAwAEEQUSITFBBhNRYQcicRQygZGhCCNCscEVUtHwJDNicoIJChYXGBkaJSYnKCkqNDU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6g4SFhoeIiYqSk5SVlpeYmZqio6Slpqeoqaqys7S1tre4ubrCw8TFxsfIycrS09TV1tfY2drh4uPk5ebn6Onq8fLz9PX29/j5+v/EAB8BAAMBAQEBAQEBAQEAAAAAAAABAgMEBQYHCAkKC//EALURAAIBAgQEAwQHBQQEAAECdwABAgMRBAUhMQYSQVEHYXETIjKBCBRCkaGxwQkjM1LwFWJy0QoWJDThJfEXGBkaJicoKSo1Njc4OTpDREVGR0hJSlNUVVZXWFlaY2RlZmdoaWpzdHV2d3h5eoKDhIWGh4iJipKTlJWWl5iZmqKjpKWmp6ipqrKztLW2t7i5usLDxMXGx8jJytLT1NXW19jZ2uLj5OXm5+jp6vLz9PX29/j5+v/bAEMAAQEBAQEBAgEBAgMCAgIDBAMDAwMEBgQEBAQEBgcGBgYGBgYHBwcHBwcHBwgICAgICAkJCQkJCwsLCwsLCwsLC//bAEMBAgICAwMDBQMDBQsIBggLCwsLCwsLCwsLCwsLCwsLCwsLCwsLCwsLCwsLCwsLCwsLCwsLCwsLCwsLCwsLCwsLC//dAAQAAf/aAAwDAQACEQMRAD8A/v4ooooA/9k=';
    return `data:image/jpeg;base64,${imageData}`;
};

const TOTAL_PAGES_PLACEHOLDER = '<span class="totalPages"></span>';
const CURRENT_PAGE_PLACEHOLDER = '<span class="pageNumber"></span>';
/**
 * Returns a compiled Handlebars template delegate, which can later be populated with data.
 *
 * @param template A Handlebars template to be compiled.
 *
 * @return A Handlebars template delegate.
 */
export function getCompiledTemplate(template: string): HandlebarsTemplateDelegate<any> {
    return handlebars.compile(template);
}

function printBreakSectionBody(
    scope: any,
    aggregationGroups: any,
    breakHelperName: string,
    options: HandlebarsOptions,
    shouldPrint: boolean,
): string | null {
    /**
     * In case we have multiple printBreak blocks within the same context we need to track different data
     * sets. It is done by the position of the block within the template.
     *  */
    const blockKey = `${options.loc.start.line}-${options.loc.start.column}`;
    const rollingDataKey = `data.root.__breakSectionData.${breakHelperName}.rolling.${blockKey}`;
    const dataKey = `data.root.__breakSectionData.${breakHelperName}.block.${blockKey}`;
    const aggregationData = [...get(options, dataKey, []), scope];
    const aggregationDataRolling = [...get(options, rollingDataKey, []), scope];
    set(options, dataKey, aggregationData);
    set(options, rollingDataKey, aggregationDataRolling);

    if (shouldPrint) {
        const aggregationGroupChunks = chunk(aggregationGroups, 2);
        const calculatedAggregatedData = calculateAggregatedData(aggregationData, aggregationGroupChunks);

        const calculatedRollingAggregatedData = calculateAggregatedData(aggregationDataRolling, aggregationGroupChunks);

        const result = options.fn({
            ...scope,
            [BLOCK_AGGREGATION_PROPERTY_NAME]: calculatedAggregatedData,
            [BLOCK_AGGREGATION_ROLLING_PROPERTY_NAME]: calculatedRollingAggregatedData,
            // This is here for legacy purposes, we should really use the one with _ prefix to differentiate from other values in the scope
            blockAggregatedData: calculatedAggregatedData,
        });

        // Reset the aggregated data when the block is printed.
        set(options, dataKey, []);
        return result;
    }

    return null;
}

/**
 * Inserts data into a compiled Handlebars template.
 *
 * @param compiledTemplate A compiled Handlebars template (also known as a delegate).
 * @param variables A JSON string value or array of JSON string values that specify the report variables and their values.
 * @param locale A string value that contains the current locale that is used to localize enum values.
 *
 * @return A string value that contains the template populated with data.
 */
function _populateCompiledTemplate(
    xtremContext: Context,
    compiledTemplate: handlebars.TemplateDelegate,
    originalTemplate: string,
    variables: any[] | Dict<any>,
    locale: xtremReporting.enums.ReportLocale = 'en_US',
    translations: Dict<string> = {},
    isHeaderOrFooter = false,
    reportResources: Dict<ResolvedResource> = {},
): string {
    return compiledTemplate(variables, {
        helpers: {
            ...handlebarsHelpers([
                'array',
                'collection',
                'comparison',
                'date',
                'inflection',
                'markdown',
                'match',
                'math',
                'misc',
                'number',
                'object',
                'regex',
                'string',
                'url',
            ]),
            with: withFn(originalTemplate),
            each,
            barcode,
            pageNumberCurrent: () => {
                if (!isHeaderOrFooter) {
                    // eslint-disable-next-line @typescript-eslint/no-throw-literal
                    throw new handlebars.Exception(
                        '#pageNumberCurrent can only be used in the header or the footer of the document.',
                    );
                }
                return new handlebars.SafeString(CURRENT_PAGE_PLACEHOLDER);
            },
            pageNumberTotal: () => {
                if (!isHeaderOrFooter) {
                    // eslint-disable-next-line @typescript-eslint/no-throw-literal
                    throw new handlebars.Exception(
                        '#pageNumberTotal can only be used in the header or the footer of the document.',
                    );
                }
                return new handlebars.SafeString(TOTAL_PAGES_PLACEHOLDER);
            },
            reportResource: (key: string): string | handlebars.SafeString => {
                const resource = reportResources[key];
                if (resource) {
                    return new handlebars.SafeString(`data:${resource.mimetype};base64,${resource.content}`);
                }
                return '';
            },
            pageNumberFullMessage: () => {
                if (!isHeaderOrFooter) {
                    // eslint-disable-next-line @typescript-eslint/no-throw-literal
                    throw new handlebars.Exception(
                        '#pageNumberFullMessage can only be used in the header or the footer of the document.',
                    );
                }
                const normalizedLocale = locale.split('_').join('-');
                const labelContent = xtremContext
                    .localize(
                        '@sage/xtrem-reporting/page-x-of-y',
                        'Page [[current]] of [[total]]',
                        {},
                        normalizedLocale as LocalizeLocale,
                    )
                    .replace('[[current]]', CURRENT_PAGE_PLACEHOLDER)
                    .replace('[[total]]', TOTAL_PAGES_PLACEHOLDER);

                return new handlebars.SafeString(labelContent);
            },
            printBreakIfPropertyChanged: function printBreakIfPropertyChanged(this: any, ...args: any) {
                const propertyPath = args[0];
                const options: HandlebarsOptions = args[args.length - 1];

                if (!propertyPath) {
                    // eslint-disable-next-line @typescript-eslint/no-throw-literal
                    throw new handlebars.Exception('Must pass a path to to #printBreakIfPropertyChanged');
                }

                if (isUndefined(options.data.collection) || isUndefined(options.data.index)) {
                    // eslint-disable-next-line @typescript-eslint/no-throw-literal
                    throw new handlebars.Exception(
                        '#printBreakIfPropertyChanged can only be used in the context of an #each loop',
                    );
                }
                const propertyPathWithParentContext = options.data._withPath
                    ? `${options.data._withPath}.${propertyPath}`
                    : propertyPath;

                const shouldPrint =
                    options.data.first ||
                    get(this, propertyPath) !==
                        get(options.data.collection[options.data.index - 1], propertyPathWithParentContext);

                const aggregationGroups = [...args].slice(1, -1);

                return printBreakSectionBody(this, aggregationGroups, 'hasChanged', options, shouldPrint);
            },
            printBreakIfPropertyWillChange: function printBreakIfPropertyWillChange(this: any, ...args: any) {
                const propertyPath = args[0];
                const options: HandlebarsOptions = args[args.length - 1];

                if (!propertyPath) {
                    // eslint-disable-next-line @typescript-eslint/no-throw-literal
                    throw new handlebars.Exception('Must pass a path to to #printBreakIfPropertyWillChange');
                }

                if (isUndefined(options.data.collection) || isUndefined(options.data.index)) {
                    // eslint-disable-next-line @typescript-eslint/no-throw-literal
                    throw new handlebars.Exception(
                        '#printBreakIfPropertyWillChange can only be used in the context of an #each loop',
                    );
                }

                const propertyPathWithParentContext = options.data._withPath
                    ? `${options.data._withPath}.${propertyPath}`
                    : propertyPath;

                const shouldPrint =
                    options.data.last ||
                    get(this, propertyPath) !==
                        get(options.data.collection[options.data.index + 1], propertyPathWithParentContext);

                // The first item is the group property, the last one is the handlebars options
                const aggregationGroups = [...args].slice(1, -1);

                return printBreakSectionBody(this, aggregationGroups, 'willChange', options, shouldPrint);
            },
            printBreakIfLast: function printBreakIfLast(this: any, ...args: any) {
                const propertyPath = args[0];
                const options: HandlebarsOptions = args[args.length - 1];

                if (!propertyPath) {
                    // eslint-disable-next-line @typescript-eslint/no-throw-literal
                    throw new handlebars.Exception('Must pass a path to to #printBreakIfLast');
                }

                if (isUndefined(options.data.collection) || isUndefined(options.data.index)) {
                    // eslint-disable-next-line @typescript-eslint/no-throw-literal
                    throw new handlebars.Exception(
                        '#printBreakIfLast can only be used in the context of an #each loop',
                    );
                }
                const aggregationGroups = [...args].slice(0, -1);
                return printBreakSectionBody(this, aggregationGroups, 'last', options, options.data.last);
            },
            enumValue: enumValueHelper(locale),
            formatNumber: formatNumber(locale),
            formatDate: formatDate(locale),
            displayImage,

            startsWithString: function startsWith(this: any, expectedValue: string, targetValue: string, options: any) {
                if (arguments.length !== 3) {
                    // eslint-disable-next-line @typescript-eslint/no-throw-literal
                    throw new handlebars.Exception('#startsWith requires exactly two argument');
                }
                return handlebarsUtils.value(
                    isString(expectedValue) && isString(targetValue) && targetValue.startsWith(expectedValue),
                    this,
                    options,
                );
            },
            endsWithString: function startsWith(this: any, expectedValue: string, targetValue: string, options: any) {
                if (arguments.length !== 3) {
                    // eslint-disable-next-line @typescript-eslint/no-throw-literal
                    throw new handlebars.Exception('#startsWith requires exactly two argument');
                }
                return handlebarsUtils.value(
                    isString(expectedValue) && isString(targetValue) && targetValue.endsWith(expectedValue),
                    this,
                    options,
                );
            },
            containsString: function containsString(
                this: any,
                expectedValue: string,
                targetValue: string,
                options: any,
            ) {
                if (arguments.length !== 3) {
                    // eslint-disable-next-line @typescript-eslint/no-throw-literal
                    throw new handlebars.Exception('#startsWith requires exactly two argument');
                }
                return handlebarsUtils.value(
                    isString(expectedValue) && isString(targetValue) && targetValue.indexOf(expectedValue) !== -1,
                    this,
                    options,
                );
            },
            translatedContent: function translatedContent(this: any, key: string) {
                if (!key) {
                    // eslint-disable-next-line @typescript-eslint/no-throw-literal
                    throw new handlebars.Exception('Missing translation key');
                }
                if (translations[key] === undefined) {
                    // eslint-disable-next-line @typescript-eslint/no-throw-literal
                    throw new handlebars.Exception(`Translation for ${key} could not be found.`);
                }
                return translations[key];
            },
            qrcode,
        },
    });
}

export function populateCompiledTemplate(
    xtremContext: Context,
    reportName: string,
    compiledTemplate: handlebars.TemplateDelegate,
    originalTemplate: string,
    variables: any[] | Dict<any>,
    locale: xtremReporting.enums.ReportLocale = 'en_US',
    translations: Dict<string> = {},
    isHeaderOrFooter = false,
    reportResources: Dict<ResolvedResource> = {},
): Promise<string> {
    return reportHistogram.withMetrics({ reportName, method: 'populate_template' }, () =>
        withRethrow(
            () =>
                _populateCompiledTemplate(
                    xtremContext,
                    compiledTemplate,
                    originalTemplate,
                    variables,
                    locale,
                    translations,
                    isHeaderOrFooter,
                    reportResources,
                ),
            error => new DataInputError(error.message, error),
        ),
    );
}

/**
 * Inserts data into a string template containing embedded variable values (a Handlebars template).
 *
 * @param template A string value that is to have its variable values replaced.
 * @param variables A JSON string value or array of JSON string values that specify the variables and their values to be inserted into the template.
 *
 * @return A string value that contains the template populated with data.
 */
export async function populateTemplateWithVariables(
    context: Context,
    reportName: string | null,
    template: string | null,
    variables: any[] | Dict<any>,
    locale: xtremReporting.enums.ReportLocale = 'en_US',
    isOptional = false,
    translations: Dict<string> = {},
    isHeaderOrFooter = false,
): Promise<string> {
    if (!template && !isOptional) {
        throw new Error(
            context.localize(
                '@sage/xtrem-reporting/functions__template_utils__invalid-template',
                "Couldn't evaluate report template.",
            ),
        );
    }

    const resolvedResources = await resolveReportResources(context, template);

    return populateCompiledTemplate(
        context,
        reportName || '',
        getCompiledTemplate(template || ''),
        template || '',
        variables,
        locale,
        translations,
        isHeaderOrFooter,
        resolvedResources,
    );
}
