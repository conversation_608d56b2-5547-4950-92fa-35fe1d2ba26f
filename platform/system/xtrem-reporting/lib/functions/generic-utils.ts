import { Context, TextStream } from '@sage/xtrem-core';
import * as xtremMetadata from '@sage/xtrem-metadata';
import { InitialNotificationAction } from '@sage/xtrem-shared';
import { constants } from 'fs';
import * as fs from 'fs/promises';
import * as xtremReporting from '../index';

export const WIZARD_PREVIEW_TEMPLATE_NAME = 'wizardPreviewTemplate';

export const getLocaleToUse = (
    context: Context,
    locale?: xtremReporting.enums.ReportLocale,
): xtremReporting.enums.ReportLocale => locale || (context.currentLocale as xtremReporting.enums.ReportLocale);

export const getPaperFormatToUse = (
    locale: xtremReporting.enums.ReportLocale,
    paperFormat?: xtremReporting.enums.ReportPaperFormat,
): xtremReporting.enums.ReportPaperFormat => paperFormat || (locale === 'en_US' ? 'letter' : 'a4');

export const checkFileExists = async (filePath: string): Promise<boolean> => {
    try {
        await fs.access(filePath, constants.F_OK);
        return true;
    } catch (err) {
        return false;
    }
};

export async function sendUserNotification(
    context: Context,
    data: {
        status: string;
        reportName: string;
        rejectReason?: string;
        downloadUrl?: string;
    },
) {
    const { downloadUrl, reportName, status, rejectReason } = data;
    const historyLink = context.batch.notificationStateLink;
    const isNotSuccessful = status === 'rejected' || !!rejectReason || !downloadUrl;

    const actions: InitialNotificationAction[] = isNotSuccessful
        ? [
              {
                  link: historyLink,
                  title: 'History',
                  icon: 'link',
                  style: 'tertiary',
              },
          ]
        : [
              {
                  link: `${downloadUrl}`,
                  title: 'Download file',
                  icon: 'link',
                  style: 'secondary',
              },
              {
                  link: historyLink,
                  title: 'History',
                  icon: 'link',
                  style: 'tertiary',
              },
          ];

    const title =
        status === 'rejected'
            ? context.localize(
                  '@sage/xtrem-reporting/function__generic_utils__send_user_notification__title_fail',
                  '{{reportName}} printing unsuccessful',
                  { reportName },
              )
            : context.localize(
                  '@sage/xtrem-reporting/function__generic_utils__send_user_notification__title_success',
                  '{{reportName}} printing successful',
                  { reportName },
              );

    let description =
        status === 'rejected' || rejectReason
            ? (rejectReason as string)
            : context.localize(
                  '@sage/xtrem-reporting/function__generic_utils__send_user_notification_description',
                  'Document printed.',
              );

    if (!downloadUrl && !rejectReason) {
        description = context.localize(
            '@sage/xtrem-reporting/function__generic_utils__send_user_notification_description_error',
            'The document did not print. See the Batch task history page for details.',
        );
    }

    await context.notifyUser({
        title,
        icon: isNotSuccessful ? 'link' : 'download',
        description,
        level: status === 'rejected' ? 'error' : 'success',
        shouldDisplayToast: true,
        actions,
    });
}

export interface ReportCreationInput {
    description: string;
    parentPackage: string;
    externalHtmlTemplate: TextStream;
    dataSource: xtremMetadata.nodes.MetaNodeFactory;
    selectedProperties: string;
    parameters: string;
    filters: string;
    content: string;
    reportSettings: xtremReporting.functions.IncomingReportGenerationSettings;
    _id?: string;
    name?: string;
    templateId?: string;
    templateName?: string;
    variables?: string;
    isDefaultHeaderFooter?: boolean;
    printingType?: xtremReporting.enums.PrintingType;
}
