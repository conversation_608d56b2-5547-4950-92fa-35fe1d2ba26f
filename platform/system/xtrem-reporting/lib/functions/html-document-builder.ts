import {
    AnyValue,
    ConfigManager,
    Context,
    Dict,
    Logger,
    TextStream,
    asyncArray,
    date,
    htmlSanitizer,
} from '@sage/xtrem-core';
import { Interpreter } from '@sage/xtrem-js';
import { reportHistogram } from '@sage/xtrem-pdf-generator';
import { CUSTOM_DATA_PROPERTY, remapObjectByKey } from '@sage/xtrem-shared';
import { DirectiveNode, FieldNode, Kind, parse, print, visit } from 'graphql';
import * as handlebars from 'handlebars';
import { escapeRegExp, isEmpty, isNull, set } from 'lodash';
import { nanoid } from 'nanoid';
import * as xtremReporting from '..';
import { ReportVariable } from '../nodes/report-variable';
import { resolveStyleVariables } from './style-utils';
import { getCompiledTemplate, populateCompiledTemplate, populateTemplateWithVariables } from './template-utils';
import { TranslatableText } from './translation-utils';

const logger = Logger.getLogger(__filename, 'document-builder');

const previewMaxPerPage = 100;

const baseStyles = `@media print { body { -webkit-print-color-adjust: exact; } }
            body {
                font-size: 12pt;
                margin: 0;
            }

            h1,h2,h3,h4,th{
                color: var(--themePrimary);
            }

            .xtrem-page-break {
                page-break-after: always;
            }
`;

export interface TemplateContents {
    code: string;
    htmlTemplate: string;
    locale?: xtremReporting.enums.ReportLocale;
    paperFormat?: xtremReporting.enums.ReportPaperFormat;
    query: string;
    styleSheet: string;
}

export interface ReportGenerationSettings {
    bottomMarginCm?: number;
    leftMarginCm?: number;
    locale: xtremReporting.enums.ReportLocale;
    pageOrientation?: xtremReporting.enums.ReportPageOrientation;
    paperFormat?: xtremReporting.enums.ReportPaperFormat;
    rightMarginCm?: number;
    topMarginCm?: number;
    variables: Dict<any>;
    watermark?: string;
    isDefaultHeaderFooter?: boolean;
    documentTitle?: string;
}

export interface ReportGenerationResolvedQuerySettings {
    locale: xtremReporting.enums.ReportLocale;
    paperFormat?: xtremReporting.enums.ReportPaperFormat;
    resolvedQueryResultSet: Dict<any>;
}

export interface PopulatedHtmlTemplate {
    populatedAttachmentContent?: string;
    populatedBodyContent: string;
    populatedFooterContent?: string;
    populatedHeaderContent?: string;
}

export interface IncomingReportGenerationSettings {
    bottomMarginCm?: number;
    leftMarginCm?: number;
    locale?: xtremReporting.enums.ReportLocale;
    pageOrientation?: xtremReporting.enums.ReportPageOrientation;
    paperFormat?: xtremReporting.enums.ReportPaperFormat;
    rightMarginCm?: number;
    topMarginCm?: number;
    variables?: string;
    watermark?: string;
    isDefaultHeaderFooter?: boolean;
    reportName?: string;
    documentTitle?: string;
    isBulk?: boolean;
}

/**
 * Validates the necessary properties of a Report Template.
 *
 * @param context A Context object that describes the current context.
 * @param name A string value that contains the report template's name.
 * @param htmlTemplate A string value that contains the report template's htmlTemplate.
 * @param query A string value that contains the report template's query.
 * @param code A string value that contains the report template's code block.
 */
function validateTemplateContents(context: Context, name: string, htmlTemplate: string) {
    if (!htmlTemplate) {
        throw name
            ? context.businessRuleError({
                  key: '@sage/xtrem-reporting/functions__document-builder__no-html-template',
                  message: "The report template named '{{templateName}}' does not have an HTML template.",
                  data: { templateName: name },
              })
            : context.businessRuleError({
                  key: '@sage/xtrem-reporting/functions__document-builder__current-no-html-template',
                  message: 'The current report template does not have an HTML template.',
              });
    }
}

/**
 * Validates the necessary properties of a report template object.
 *
 * @param context A Context object that describes the current context.
 * @param template A ReportTemplate node object to be validated.
 */
async function validateTemplate(context: Context, template: xtremReporting.nodes.ReportTemplate): Promise<void> {
    validateTemplateContents(context, await template.name, (await template.htmlTemplate).value);
}

/**
 * Removes all occurrences of the erroneous "_customField_" prefix.
 * This is needed for backwards compatibility with legacy reports.
 */
export function cleanCustomFieldPrefix(val?: string | { toString(): string }): string {
    return typeof val === 'string'
        ? val.replace(/_customField_/g, '')
        : val && typeof val.toString === 'function'
          ? val.toString().replace(/_customField_/g, '')
          : '';
}

export type ReportObject = {
    instance: xtremReporting.nodes.Report | null;
    name: string;
    settings: ReportGenerationSettings[];
};

/**
 * Try to read a report instance from the given name.
 * then return a ReportObject with the instance (or null) and the name.
 *
 * @param context A Context object that describes the current context.
 * @param reportName the name of the report to read
 *
 * @returns A ReportObject object.
 */
export async function getReportObjectFromName(
    context: Context,
    reportName: string,
    settings: ReportGenerationSettings[],
): Promise<ReportObject> {
    return {
        instance: await context.tryRead(xtremReporting.nodes.Report, { name: reportName }),
        name: reportName,
        settings,
    };
}

/**
 * Gets the active template from a report.
 *
 * @param context A Context object that describes the current context.
 * @param reportName A string value that contains the name of the report.
 *
 * @return A ReportTemplate node object.
 */
export async function getActiveTemplateFromReport(
    context: Context,
    report: ReportObject,
): Promise<xtremReporting.nodes.ReportTemplate> {
    if (!report.instance) {
        throw context.businessRuleError({
            key: '@sage/xtrem-reporting/functions__document-builder__report-not-found',
            message: "Couldn't find the report named '{{reportName}}'.",
            data: { reportName: report.name },
        });
    }

    const template = await report.instance.activeTemplate;
    if (!template) {
        throw context.businessRuleError({
            key: '@sage/xtrem-reporting/functions__document-builder__no-active-template',
            message: "The report named '{{reportName}}' does not have an active template.",
            data: { reportName: report.name },
        });
    }

    const templateObject = await context.tryRead(xtremReporting.nodes.ReportTemplate, {
        name: await template.name,
    });
    if (!templateObject) {
        // This should never happen because of validation rules but this check is here in case the data is corrupted.
        throw context.businessRuleError({
            key: '@sage/xtrem-reporting/functions__document-builder__report-template-not-found',
            message: "Couldn't find the report template named '{{templateName}}'.",
            data: { templateName: await template.name },
        });
    }

    return templateObject;
}

/**
 * Gets the provided report template, if that does not exist it gets the active template from the report.
 *
 * @param context A Context object that describes the current context.
 * @param reportName A string value that contains the name of the report.
 * @param reportTemplateName A string value that contains the name of the report template.
 *
 * @return A ReportTemplate node object.
 */
export async function getReportTemplateFromName(
    context: Context,
    report: ReportObject,
    reportTemplateName?: string,
): Promise<xtremReporting.nodes.ReportTemplate> {
    if (reportTemplateName) {
        const templateObject = await context.tryRead(xtremReporting.nodes.ReportTemplate, {
            name: reportTemplateName,
        });
        if (templateObject) {
            return templateObject;
        }
    }

    return getActiveTemplateFromReport(context, report);
}

/**
 * Builds the beginning of an HTML document, including the style sheet and style variables. All style variables from the ReportStyleVariables table are included.
 *
 * @param context A Context object that describes the current context.
 * @param reportStyleSheet A string value that contains CSS style definitions.
 *
 * @return A string value that contains a partial HTML document. It begins with the opening <head> tag and ends with the closing </head> tag.
 */
async function assembleHtmlHead(
    context: Context,
    reportName: string,
    reportStyleSheet: string,
    data: object,
    locale: xtremReporting.enums.ReportLocale = 'en_US',
    translations: Dict<string> = {},
    watermark?: string,
): Promise<string> {
    const resolvedReportStyleSheet = await populateTemplateWithVariables(
        context,
        reportName,
        reportStyleSheet,
        data,
        locale,
        true,
        translations,
    );
    let resolveStyleVariable = '';
    if (!isNull(context.tenantId)) {
        resolveStyleVariable = (await resolveStyleVariables(context)) || '';
    }

    if (watermark) {
        resolveStyleVariable += `\nbody::after {
            content: "${watermark}";
            position: fixed;
            top: 50%;
            left: 50%;
            transform: rotate(315deg) translate(-50%, -50%);
            font-size: 100px;
            color: rgba(0, 0, 0, 0.1);
            z-index: 1;
            pointer-events: none;
            font-family: monospace;
        }`;
    }

    return `<head><meta charset="utf-8">
        <style>
            ${resolveStyleVariable}

            ${baseStyles}
            ${resolvedReportStyleSheet}
        </style>
    </head>`;
}

/**
 * Builds the ending of an HTML document, including the body containing a populated HTML template.
 *
 * @param context A Context object that describes the current context.
 * @param htmlTemplate A string value that contains an HTML template.
 * @param data An object that contains the names and values of data to populate into the HTML Template.
 * @param locale A string value that contains the current locale that, if needed, is used to localize messages to the user.
 *
 * @return A string value that contains a partial HTML document. It begins with the opening <body> tag and ends with the closing </html> tag.
 */
export async function assembleHtmlBody(
    context: Context,
    reportName: string,
    htmlTemplate: string,
    data: object,
    locale: xtremReporting.enums.ReportLocale = 'en_US',
    translations: Dict<string> = {},
): Promise<string> {
    const resolvedBody = await populateTemplateWithVariables(
        context,
        reportName,
        htmlTemplate,
        data,
        locale,
        false,
        translations,
    );

    return `<body class="ck ck-content">${resolvedBody}</body>`;
}

async function addStreamDirectivesToEdges(
    context: Context,
    reportName: string,
    query: string,
    variables: Dict<any>,
    { isSample = false }: { isSample: boolean },
): Promise<string> {
    if (!query || query === '{}') return '{}';

    // Escape string parameters
    const reportVariables = await context
        .query(ReportVariable, { filter: { report: { name: reportName } } })
        .reduce<Dict<string>>(async (acc, v) => {
            const name = await v.name;
            const type = await v.type;
            acc[name] = type;
            return acc;
        }, {} as Dict<string>);

    const escapedVariables = Object.keys(variables).reduce<Dict<any>>((acc: Dict<any>, key: string) => {
        if (reportVariables[key] === 'string') {
            acc[`${key}__ESCAPED`] = escapeRegExp(variables[key]);
        }
        acc[key] = variables[key];
        return acc;
    }, {} as Dict<any>);

    // process handlebars in query string
    const handlebarsTemplate = handlebars.compile(query);
    const processedQuery = handlebarsTemplate(escapedVariables);

    const ast = parse(processedQuery);
    const adjustedAst = visit(ast, {
        enter(node) {
            if (isSample && node.kind === Kind.FIELD && node.name.value === 'query') {
                const firstArgument = (node.arguments || []).find(arg => arg.name && arg.name.value === 'first');
                if (
                    firstArgument &&
                    firstArgument.value.kind === Kind.INT &&
                    Number(firstArgument.value.value) < previewMaxPerPage
                ) {
                    return node;
                }
                const filteredFirstArguments =
                    (node.arguments || []).filter(arg => !(arg.name && arg.name.value === 'first')) || [];
                return {
                    ...node,
                    arguments: [
                        ...filteredFirstArguments,
                        {
                            kind: 'Argument',
                            name: { kind: 'Name', value: 'first' },
                            value: { kind: 'IntValue', value: previewMaxPerPage },
                        },
                    ],
                };
            }
            if (node.kind === Kind.FIELD && node.name.value === 'edges' && 'directives' in node) {
                const existingDirectives = node.directives || [];
                // Add the "@stream" directive to the "edges" field
                const newDirectives: DirectiveNode[] = [
                    ...existingDirectives,
                    {
                        kind: Kind.DIRECTIVE,
                        name: {
                            kind: Kind.NAME,
                            value: 'stream',
                        },
                        arguments: [
                            {
                                kind: Kind.ARGUMENT,
                                name: {
                                    kind: Kind.NAME,
                                    value: 'label',
                                },
                                value: {
                                    kind: Kind.STRING,
                                    value: nanoid(),
                                },
                            },
                            {
                                kind: Kind.ARGUMENT,
                                name: {
                                    kind: Kind.NAME,
                                    value: 'initialCount',
                                },
                                value: {
                                    kind: Kind.INT,
                                    value: '20',
                                },
                            },
                        ],
                    },
                ];
                const newEdgesNode: FieldNode = {
                    ...node,
                    directives: newDirectives,
                };
                return newEdgesNode;
            }
            return node;
        },
    });

    return print(adjustedAst);
}

type QueryStreamResponseAsync = AsyncGenerator<{ data: object; path: string[]; errors?: Error[] }>;

type QueryStreamResponseSync = { data: AnyValue; errors?: Error[] };

type StreamResponse = { data: AnyValue; errors: Error[] };

async function getStreamResponse(
    queryStreamResponse: QueryStreamResponseAsync | QueryStreamResponseSync,
): Promise<StreamResponse> {
    const queryResponse: StreamResponse = { data: null, errors: [] };

    const isAsync = (obj: QueryStreamResponseAsync | QueryStreamResponseSync): obj is QueryStreamResponseAsync => {
        return obj instanceof Object && Symbol.asyncIterator in obj;
    };

    if (queryStreamResponse != null) {
        if (isAsync(queryStreamResponse)) {
            // eslint-disable-next-line no-restricted-syntax
            for await (const patch of queryStreamResponse) {
                if (patch.errors && patch.errors.length > 0) {
                    queryResponse.errors = [...queryResponse.errors, ...patch.errors];
                } else if (patch.path) {
                    if (!queryResponse.data) {
                        queryResponse.data = {};
                    }
                    set(queryResponse.data as object, patch.path, patch.data);
                    await new Promise(setImmediate);
                } else {
                    queryResponse.data = patch.data;
                }
            }
        } else if (queryStreamResponse.errors && queryStreamResponse.errors.length > 0) {
            queryResponse.errors = queryStreamResponse.errors;
        } else {
            queryResponse.data = queryStreamResponse.data;
        }
    }

    return queryResponse;
}

const _queryReportData = async (
    context: Context,
    reportName: string,
    settings: xtremReporting.functions.ReportGenerationSettings,
    query: string,
    { isSample = false }: { isSample: boolean },
): Promise<StreamResponse> => {
    const adjustedQuery = await addStreamDirectivesToEdges(context, reportName, query, settings.variables, {
        isSample,
    });
    const compiledQueryTemplate = getCompiledTemplate(adjustedQuery);

    logger.debug(() => 'Running query');

    const resolvedQueryString = await populateCompiledTemplate(
        context,
        reportName,
        compiledQueryTemplate,
        adjustedQuery,
        settings.variables,
        settings.locale,
        {},
    );

    if (!resolvedQueryString || resolvedQueryString.trim() === '{}') {
        return {
            data: null,
            errors: [],
        };
    }

    const queryStreamResponse = (await context.executeGraphqlStream(resolvedQueryString)) as
        | QueryStreamResponseAsync
        | QueryStreamResponseSync;

    const queryResponse = await getStreamResponse(queryStreamResponse);

    logger.verbose(() => `Query response: ${JSON.stringify(queryResponse)}`);

    if (queryResponse.errors && queryResponse.errors.length > 0) {
        throw context.businessRuleError({
            key: '@sage/xtrem-reporting/functions__document-builder__query-error',
            message: 'An error occurred while querying the data: {{ErrorMessage}}',
            data: { ErrorMessage: queryResponse.errors[0].message },
        });
    }

    // Parse all JSON string _customData properties
    const remappedResponse = remapObjectByKey(queryResponse.data, CUSTOM_DATA_PROPERTY, (v: any) =>
        typeof v === 'string' ? JSON.parse(v) : v,
    );

    logger.verbose(() => `Remapped query response: ${JSON.stringify(remappedResponse)}`);

    return remappedResponse;
};

const queryReportData = (
    context: Context,
    reportName: string,
    settings: xtremReporting.functions.ReportGenerationSettings,
    query: string,
    { isSample = false }: { isSample: boolean },
): Promise<StreamResponse> => {
    return reportHistogram.withMetrics({ reportName, method: 'query_data' }, () =>
        _queryReportData(context, reportName, settings, query, { isSample }),
    );
};

type StreamResponseWithCodeBlockResult = StreamResponse & { codeBlockResult?: AnyValue };

function executeCodeBlock(
    code: string,
    queryResponse: StreamResponseWithCodeBlockResult,
): StreamResponseWithCodeBlockResult {
    logger.debug(() => 'Executing code block');

    // we shouldn't mutate the original object so we create a new one here
    const queryResponseWithCodeBlockResult = { ...queryResponse };

    const codeExecutionResult = Interpreter.safeEval(code, {
        timeout: ConfigManager.current?.security?.jsEval?.timeoutInMillis ?? 2000,
        sandbox: { queryResponse },
    });
    logger.debug(
        () =>
            `Code block execution result (${codeExecutionResult.duration}ms): ${JSON.stringify(
                codeExecutionResult.value,
            )}`,
    );

    // extend query response with the result of the execution of the code block
    if (queryResponse) queryResponseWithCodeBlockResult.codeBlockResult = codeExecutionResult.value;

    return queryResponseWithCodeBlockResult;
}

export const getResolvedTemplateContextSets = async (
    context: Context,
    reportName: string,
    reportSettings: xtremReporting.functions.ReportGenerationSettings[],
    query: string,
    code: string,
    { isSample = false }: { isSample: boolean },
): Promise<xtremReporting.functions.ReportGenerationResolvedQuerySettings[]> => {
    const user = await context.user;
    const userName = user?.displayName || `${user?.firstName} ${user?.lastName}`.trim() || user?.email;

    return asyncArray(reportSettings)
        .map(async settings => {
            let queryResponseWithCodeBlockResult = (await queryReportData(context, reportName, settings, query, {
                isSample,
            })) as StreamResponseWithCodeBlockResult;
            try {
                if (code) {
                    queryResponseWithCodeBlockResult = executeCodeBlock(code, queryResponseWithCodeBlockResult);
                }
                return {
                    locale: settings.locale,
                    paperFormat: settings.paperFormat,
                    resolvedQueryResultSet: {
                        currentDate: date.today().toJsDate(),
                        user: userName,
                        ...settings.variables,
                        ...queryResponseWithCodeBlockResult,
                    },
                };
            } catch (e) {
                if (e instanceof SyntaxError || e instanceof ReferenceError) {
                    throw context.businessRuleError({
                        key: '@sage/xtrem-reporting/functions__document-builder__code-syntax-error',
                        message: "Template's code field contains invalid syntax: {{ErrorMessage}}",
                        data: { ErrorMessage: e.message },
                    });
                } else {
                    throw context.businessRuleError({
                        key: '@sage/xtrem-reporting/functions__document-builder__code-unexpected-error',
                        message: "Template's code field contains unexpected error(s): {{ErrorMessage}}",
                        data: { ErrorMessage: e.message },
                    });
                }
            }
        })
        .toArray();
};
/**
 * Builds a whole HTML document from the given template and style sheet, and populates it based on the given query and report variables data.
 *
 * @param context A Context object that describes the current context.
 * @param reportVariableSets A JSON string array. Each element contains data that specifies report variables and their values for a single document.
 * @param htmlTemplate A string value that contains an HTML template.
 * @param query A string value that contains a GraphQL query.
 * @param styleSheet A string value that contains CSS style definitions.
 * @param locale A string value that contains the current locale that, if needed, is used to localize messages to the user.
 *
 * @return A string value containing a complete HTML document.
 */

export interface PerformReportGenerationArgs {
    attachmentTemplate?: string;
    baseLocale: xtremReporting.enums.ReportLocale;
    bodyTemplate: string;
    footerHtmlTemplate?: string;
    headerHtmlTemplate?: string;
    isExpertDocument?: boolean;
    reportName: string;
    resolvedTemplateContext: xtremReporting.functions.ReportGenerationResolvedQuerySettings[];
    styleSheet?: string;
    translatedStrings?: TranslatableText[];
    translationsToUse?: Dict<string>;
    watermarks?: (string | undefined)[];
}

export function performReportGeneration(
    context: Context,
    generationArgs: PerformReportGenerationArgs,
): Promise<PopulatedHtmlTemplate[]> {
    return Promise.all(
        generationArgs.resolvedTemplateContext.map(
            async (settings: xtremReporting.functions.ReportGenerationResolvedQuerySettings, i: number) => {
                let populatedAttachmentContent: string | undefined;
                let populatedHeaderContent: string | undefined;
                let populatedFooterContent: string | undefined;

                // If translations are provided, for example from the preview section, then we will not try to resolve them.
                const translations = !isEmpty(generationArgs.translationsToUse)
                    ? generationArgs.translationsToUse
                    : xtremReporting.functions.createDictionaryForRendering(
                          settings.locale,
                          generationArgs.baseLocale,
                          generationArgs.translatedStrings || [],
                      );

                const htmlBody = await assembleHtmlBody(
                    context,
                    generationArgs.reportName,
                    generationArgs.bodyTemplate,
                    settings.resolvedQueryResultSet,
                    settings.locale,
                    translations,
                );

                const htmlHead = await assembleHtmlHead(
                    context,
                    generationArgs.reportName,
                    generationArgs.styleSheet || '',
                    settings.resolvedQueryResultSet,
                    settings.locale,
                    translations,
                    generationArgs.watermarks?.[i],
                );

                const fullHtml = `<!DOCTYPE html>
    <html>
    ${htmlHead}
    ${htmlBody}
    </html>`;
                const populatedBodyContent = await htmlSanitizer(fullHtml, {
                    ignoreTemplating: true,
                    throwIfModified: false,
                });
                logger.debug(() => `Body content: ${populatedBodyContent}`);

                if (generationArgs.headerHtmlTemplate) {
                    const fullHeader = await populateTemplateWithVariables(
                        context,
                        generationArgs.reportName,
                        generationArgs.headerHtmlTemplate,
                        settings.resolvedQueryResultSet,
                        settings.locale,
                        true,
                        translations,
                        true,
                    );

                    populatedHeaderContent = await htmlSanitizer(fullHeader, {
                        ignoreTemplating: true,
                        throwIfModified: false,
                    });

                    logger.debug(() => `Header content: ${populatedHeaderContent}`);
                }

                if (generationArgs.footerHtmlTemplate) {
                    const fullFooter = await populateTemplateWithVariables(
                        context,
                        generationArgs.reportName,
                        generationArgs.footerHtmlTemplate,
                        settings.resolvedQueryResultSet,
                        settings.locale,
                        true,
                        translations,
                        true,
                    );
                    populatedFooterContent = await htmlSanitizer(fullFooter, {
                        ignoreTemplating: true,
                        throwIfModified: false,
                    });
                    logger.debug(() => `Footer content: ${populatedFooterContent}`);
                }

                if (generationArgs.attachmentTemplate) {
                    populatedAttachmentContent = await populateTemplateWithVariables(
                        context,
                        generationArgs.reportName,
                        generationArgs.attachmentTemplate,
                        settings.resolvedQueryResultSet,
                        settings.locale,
                        true,
                        translations,
                    );
                    logger.debug(() => `Attachment content: ${populatedAttachmentContent}`);
                }

                return {
                    populatedBodyContent,
                    populatedHeaderContent,
                    populatedFooterContent,
                    populatedAttachmentContent,
                };
            },
        ),
    );
}

/**
 * Uses sets of report variables data and a report name to generate multiple HTML documents.
 *
 * @param context A Context object that describes the current context.
 * @param reportName A string value that contains the name of the report.
 * @param reportTemplateName A string value that contains the name of the report template.
 * @param reportVariableSets A JSON string array. Each element specifies the report variables and their values for a single document.
 * @param locale A string value that contains the current locale that, if needed, is used to localize messages to the user.
 *
 * @return A string array of HTML documents.
 */
export async function generateReports(
    context: Context,
    reportName: string,
    reportTemplateName: string,
    reportSettings: xtremReporting.functions.ReportGenerationSettings[],
): Promise<PopulatedHtmlTemplate[]> {
    const reportObject = await getReportObjectFromName(context, reportName, reportSettings);

    const settingOverrides = (await xtremReporting.functions.runPrePostProcessingOperation(
        context,
        reportObject,
        'preProcessing',
    )) as xtremReporting.functions.ReportGenerationSettings[];

    if (settingOverrides && settingOverrides.length === reportSettings.length) {
        settingOverrides.forEach((settingOverride, i) => {
            reportSettings[i] = { ...reportSettings[i], ...settingOverride };
        });
    }

    const reportTemplate = await getReportTemplateFromName(context, reportObject, reportTemplateName);

    await validateTemplate(context, reportTemplate);
    const translatedStrings = (await reportTemplate.translatableTexts
        .map(text => text.$.payload())
        .toArray()) as TranslatableText[];

    const resolvedTemplateContext = await getResolvedTemplateContextSets(
        context,
        reportName,
        reportSettings,
        (await reportTemplate.query).value,
        (await reportTemplate.code).value,
        { isSample: false },
    );

    return performReportGeneration(context, {
        reportName,
        resolvedTemplateContext,
        baseLocale: await reportTemplate.baseLocale,
        bodyTemplate: (await reportTemplate.htmlTemplate).value,
        headerHtmlTemplate: (await reportTemplate.headerHtmlTemplate).value,
        footerHtmlTemplate: (await reportTemplate.footerHtmlTemplate).value,
        styleSheet: (await reportTemplate.styleSheet).value,
        translatedStrings,
        attachmentTemplate: (await reportTemplate.attachmentTemplate).value,
        isExpertDocument: await reportTemplate.isExpertDocument,
        watermarks: reportSettings.map(r => r.watermark),
    });
}

/**
 * Uses a set of report variables data and a report name to generate an HTML document.
 *
 * @param context A Context object that describes the current context.
 * @param reportName A string value that contains the name of the report.
 * @param reportTemplateName A string value that contains the name of the report template.
 * @param reportVariables A JSON string that specifies the report variables and their values.
 * @param locale A string value that contains the current locale that, if needed, is used to localize messages to the user.
 *
 * @return A string value containing an HTML document.
 */
export async function generateReport(
    context: Context,
    reportName: string,
    reportTemplateName: string,
    reportSettings: xtremReporting.functions.ReportGenerationSettings,
): Promise<PopulatedHtmlTemplate> {
    return (await generateReports(context, reportName, reportTemplateName, [reportSettings]))[0];
}

export interface GenerateReportFromTemplateContents {
    context: Context;
    reportName: string;
    bodyHtmlTemplate: string;
    headerHtmlTemplate?: string;
    footerHtmlTemplate?: string;
    query: TextStream;
    code?: TextStream;
    styleSheet: TextStream;
    variables?: Dict<any>;
    locale?: xtremReporting.enums.ReportLocale;
    paperFormat?: xtremReporting.enums.ReportPaperFormat;
    baseLocale: xtremReporting.enums.ReportLocale;
    translations?: Dict<string>;
    attachmentTemplate?: TextStream;
    isExpertDocument?: boolean;
    isSample?: boolean;
    watermark?: string;
}

/**
 * Uses a set of report variables data and data from a report template to generate an HTML document.
 */
export async function generateReportFromTemplateContents({
    context,
    reportName,
    baseLocale,
    bodyHtmlTemplate,
    headerHtmlTemplate,
    footerHtmlTemplate,
    query,
    code,
    styleSheet,
    attachmentTemplate,
    variables = {},
    locale = 'en_US',
    paperFormat = 'a4',
    translations = {},
    isExpertDocument = false,
    isSample = false,
    watermark,
}: GenerateReportFromTemplateContents): Promise<PopulatedHtmlTemplate> {
    const cleanBodyHtmlTemplate = cleanCustomFieldPrefix(bodyHtmlTemplate);
    const cleanHeaderHtmlTemplate = cleanCustomFieldPrefix(headerHtmlTemplate);
    const cleanFooterHtmlTemplate = cleanCustomFieldPrefix(footerHtmlTemplate);
    const cleanQuery = cleanCustomFieldPrefix(query);
    validateTemplateContents(context, '', bodyHtmlTemplate);
    const resolvedTemplateContext = await getResolvedTemplateContextSets(
        context,
        reportName,
        [
            {
                variables,
                locale,
                paperFormat,
            },
        ],
        cleanQuery?.toString(),
        code?.toString() || '',
        { isSample },
    );

    return (
        await performReportGeneration(context, {
            reportName,
            baseLocale,
            resolvedTemplateContext,
            bodyTemplate: cleanBodyHtmlTemplate,
            headerHtmlTemplate: cleanHeaderHtmlTemplate,
            footerHtmlTemplate: cleanFooterHtmlTemplate,
            styleSheet: styleSheet?.toString(),
            translationsToUse: translations,
            attachmentTemplate: attachmentTemplate?.toString(),
            isExpertDocument,
            watermarks: watermark ? [watermark] : undefined,
        })
    )[0];
}

export function defaultHtmlFooter(): string {
    return `<table width="100%">
            <tr>
                <td align="left">
                Printed by&nbsp;<span
                    class="property"
                    data-property-display-label="Printed by"
                    data-property-data-type="String"
                    data-property-name="@root.user"
                    data-property-data-format=""
                    data-property-parent-context="$globalProperties"
                    >{{@root.user}}</span
                >&nbsp;on the&nbsp;<span
                    class="property"
                    data-property-display-label="Current date"
                    data-property-data-type="Date"
                    data-property-name="@root.currentDate"
                    data-property-data-format="FullDate"
                    data-property-parent-context="$globalProperties"
                    >{{formatDate @root.currentDate "FullDate"}}</span
                >
                </td>
                <td align="right">Page&nbsp;
                <span
                    class="property"
                    data-property-display-label="Page number"
                    data-property-data-type="String"
                    data-property-name="pageNumberCurrent"
                    data-property-data-format=""
                    data-property-parent-context="$globalProperties"
                    >{{pageNumberCurrent}}</span
                >
                </td>
            </tr>
            </table>`;
}

export function defaultHtmlHeader(reportName: string): string {
    return `<h1 style="text-align:center;">${reportName}</h1>`;
}
