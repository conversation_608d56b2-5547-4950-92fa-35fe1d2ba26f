import { asyncArray, Context, Diagnose, NodeQueryFilter } from '@sage/xtrem-core';
import { Dict, ValidationSeverity } from '@sage/xtrem-shared';
import * as xtremReporting from '../index';

const glue = '__';
const getKeyByComponents = (
    sourceLocale: xtremReporting.enums.ReportLocale,
    targetLocale: xtremReporting.enums.ReportLocale,
    reportTemplateName: string,
    hash: string,
) => [reportTemplateName, sourceLocale, targetLocale, hash].join(glue);

const getComponentsByKey = (
    key: string,
): {
    sourceLocale: xtremReporting.enums.ReportLocale;
    targetLocale: xtremReporting.enums.ReportLocale;
    reportTemplateName: string;
    hash: string;
} => {
    const components = key.split(glue);
    if (components.length !== 4) {
        throw new Error(`Invalid import key: ${key}`);
    }

    if (
        !xtremReporting.enums.ReportLocaleDataType.values.includes(components[1] as xtremReporting.enums.ReportLocale)
    ) {
        throw new Error(`Invalid locale code: ${components[1]}`);
    }
    if (
        !xtremReporting.enums.ReportLocaleDataType.values.includes(components[2] as xtremReporting.enums.ReportLocale)
    ) {
        throw new Error(`Invalid locale code: ${components[2]}`);
    }
    if (!/^[a-f0-9]{32}$/gi.test(components[3])) {
        throw new Error(`Invalid hash: ${components[3]}`);
    }

    return {
        sourceLocale: components[1] as xtremReporting.enums.ReportLocale,
        targetLocale: components[2] as xtremReporting.enums.ReportLocale,
        reportTemplateName: components[0],
        hash: components[3],
    };
};

export const exportTranslations = async (
    context: Context,
    sourceLocale: xtremReporting.enums.ReportLocale,
    targetLocale: xtremReporting.enums.ReportLocale,
    reportTemplates?: xtremReporting.nodes.ReportTemplate[],
): Promise<Dict<string>> => {
    const filter: NodeQueryFilter<xtremReporting.nodes.ReportTranslatableText> = {
        reportTemplate: { baseLocale: sourceLocale },
        locale: targetLocale,
        isBaseLocale: false,
        text: '',
    };

    if (reportTemplates && reportTemplates.length > 0) {
        filter.reportTemplate = { _in: reportTemplates.map(r => r._id) };
    }
    const result = await context
        .query(xtremReporting.nodes.ReportTranslatableText, { filter })
        .reduce(async (prevValue: Dict<string>, translatableEntry: xtremReporting.nodes.ReportTranslatableText) => {
            const key = getKeyByComponents(
                sourceLocale,
                targetLocale,
                await (
                    await translatableEntry.reportTemplate
                ).name,
                await translatableEntry.hash,
            );

            return { ...prevValue, [key]: await (await translatableEntry.originalText)!.text };
        }, {} as Dict<string>);

    return result;
};

export const importTranslations = (context: Context, translations: Dict<string>): Promise<void> =>
    asyncArray(Object.keys(translations)).forEach(async key => {
        const { sourceLocale, targetLocale, hash, reportTemplateName } = getComponentsByKey(key);
        const filter: NodeQueryFilter<xtremReporting.nodes.ReportTranslatableText> = {
            reportTemplate: { name: reportTemplateName, baseLocale: sourceLocale },
            locale: targetLocale,
            isBaseLocale: false,
            hash,
        };

        const translation = await context
            .query(xtremReporting.nodes.ReportTranslatableText, { filter, first: 1, forUpdate: true })
            .at(0);

        if (!translation) {
            context.diagnoses.push(
                new Diagnose(
                    ValidationSeverity.error,
                    [],
                    `${key} could not be imported, the entry doesn't exist in the database.`,
                ),
            );
            return;
        }

        if (await translation.text) {
            context.diagnoses.push(
                new Diagnose(
                    ValidationSeverity.error,
                    [],
                    `${key} could not be imported, it has been already translated.`,
                ),
            );
        }

        await translation.$.set({ text: translations[key] });
        await translation.$.save();
    });
