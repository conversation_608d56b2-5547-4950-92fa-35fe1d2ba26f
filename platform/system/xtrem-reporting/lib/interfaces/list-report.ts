export interface Content {
    _id: string;
    group: number;
    sorting: string;
    presentation: string;
    title: string;
    labelPath: string;
    property: {
        id: string;
        label: string;
        labelPath: string;
        data: {
            type: string;
            kind: string;
            isCollection: boolean;
            name: string;
            canFilter: boolean;
            canSort: boolean;
            label: string;
        };
    };
    path: string;
}

export interface PropertyData {
    type: string;
    kind: string;
    enumValues?: string[];
    isCollection: boolean;
    name: string;
    canFilter: boolean;
    canSort: boolean;
    label: string;
}

export interface SelectedProperty {
    label: string;
    data: PropertyData;
    id: string;
    key: string;
    labelKey: string;
    labelPath: string;
}

export interface ReportFilter {
    filters: {
        _id: string;
        parameter: boolean;
        label: string;
        filterType: string;
        filterValue: { formattedValue: string; rawValue: string } | string;
        data: PropertyData;
        id: string;
        labelPath: string;
        property: SelectedProperty;
    }[];
    parameters: { name: string; label: string; type: string }[];
}

export interface ReportParameters {
    _id: string;
    name: string;
    title: string;
    type: string;
    dataType: {
        name: string;
        attributes: {
            optionType: string;
        };
    };
}
