import { Collection, decorators, Node } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremReporting from '..';

@decorators.node<ReportAssignmentPage>({
    storage: 'sql',
    indexes: [{ orderBy: { screenId: 1, reportAssignmentType: 1 }, isUnique: true, isNaturalKey: true }],
    isSetupNode: true,
    isPublished: true,
    canCreate: true,
    canRead: true,
    canSearch: true,
    canDelete: true,
    canUpdate: true,
    serviceOptions: () => [xtremReporting.serviceOptions.reportAssignment],
})
export class ReportAssignmentPage extends Node {
    @decorators.stringProperty<ReportAssignmentPage, 'screenId'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isNotEmpty: true,
        dataType: () => xtremSystem.dataTypes.name,
    })
    readonly screenId: Promise<string>;

    @decorators.enumProperty<ReportAssignmentPage, 'reportAssignmentType'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremReporting.enums.reportAssignmentTypeDataType,
    })
    readonly reportAssignmentType: Promise<xtremReporting.enums.ReportAssignmentType>;

    @decorators.collectionProperty<ReportAssignmentPage, 'assignments'>({
        isPublished: true,
        isVital: true,
        reverseReference: 'page',
        node: () => xtremReporting.nodes.ReportAssignmentAssociation,
    })
    assignments: Collection<xtremReporting.nodes.ReportAssignmentAssociation>;
}
