import {
    asyncArray,
    BinaryStream,
    Collection,
    Context,
    datetime,
    decorators,
    FileStorageManager,
    Logger,
    Node,
    NodePayloadData,
    Reference,
    sleepMillis,
    TextStream,
} from '@sage/xtrem-core';
import * as xtremDateTime from '@sage/xtrem-date-time';
import { FileTimeToLive } from '@sage/xtrem-file-storage';
import * as xtremInfrastructureAdapter from '@sage/xtrem-infrastructure-adapter';
import * as xtremMetadata from '@sage/xtrem-metadata';
import { Dict, LocalizeLocale } from '@sage/xtrem-shared';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremUpload from '@sage/xtrem-upload';
import * as archiver from 'archiver';
import axios from 'axios';
import { createWriteStream } from 'fs';
import * as fs from 'fs/promises';
import * as JSZip from 'jszip';
import { camelCase, kebabCase } from 'lodash';
import { nanoid } from 'nanoid';
import { URL } from 'node:url';
import * as os from 'os';
import * as fsp from 'path';
import * as xtremReporting from '..';
import { defaultHtmlFooter, defaultHtmlHeader } from '../functions/html-document-builder';
import { generatePdfData } from '../functions/pdf-utils';

const logger = Logger.getLogger(__filename, 'reporting-report-node');

@decorators.node<Report>({
    package: 'xtrem-reporting',
    storage: 'sql',
    indexes: [{ orderBy: { name: 1 }, isUnique: true, isNaturalKey: true }],
    isSetupNode: true,
    isPublished: true,
    canCreate: true,
    canRead: true,
    canSearch: true,
    canDelete: true,
    canUpdate: true,
    async controlEnd(cx) {
        if (await this.$.context.isServiceOptionEnabled(xtremReporting.serviceOptions.reportAssignment)) {
            const printingType = await this.printingType;
            const variableCount = await this.variables.length;
            const reportType = await this.reportType;
            if (reportType === 'printedDocument') {
                const atLeastOneMandatoryReferenceVariable =
                    (await this.variables.find(async variable => {
                        const variableIsReference = (await variable.type) === 'reference';
                        const isMandatory = !!(await variable.isMandatory);
                        return variableIsReference && isMandatory;
                    })) !== undefined;

                if (printingType === 'single' && (variableCount === 0 || !atLeastOneMandatoryReferenceVariable)) {
                    cx.error.add(
                        this.$.context.localize(
                            '@sage/xtrem-reporting/nodes__report__a-mandatory-reference-variable-required-for-single-print-reports',
                            'A mandatory reference variable is required for single print reports.',
                        ),
                    );
                }
            }
        }
    },
})
export class Report extends Node {
    @decorators.stringProperty<Report, 'name'>({
        isStored: true,
        isPublished: true,
        isNotEmpty: true,
        isFrozen() {
            return this.isFactory;
        },
        dataType: () => xtremSystem.dataTypes.name,
        lookupAccess: true,
    })
    readonly name: Promise<string>;

    @decorators.stringProperty<Report, 'description'>({
        isStored: true,
        isPublished: true,
        isNotEmpty: true,
        dataType: () => xtremSystem.dataTypes.description,
        lookupAccess: true,
    })
    readonly description: Promise<string>;

    @decorators.stringProperty<Report, 'parentPackage'>({
        isStored: true,
        isPublished: true,
        isNotEmpty: true,
        dataType: () => xtremSystem.dataTypes.shortDescription,
    })
    readonly parentPackage: Promise<string>;

    @decorators.referenceProperty<Report, 'activeTemplate'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        isOwnedByCustomer: true,
        node: () => xtremReporting.nodes.ReportTemplate,
    })
    readonly activeTemplate: Reference<xtremReporting.nodes.ReportTemplate | null>;

    @decorators.collectionProperty<Report, 'reportTemplates'>({
        node: () => xtremReporting.nodes.ReportTemplate,
        isPublished: true,
        reverseReference: 'report',
    })
    readonly reportTemplates: Collection<xtremReporting.nodes.ReportTemplate>;

    @decorators.enumProperty<Report, 'reportType'>({
        isPublished: true,
        isStored: true,
        dataType: () => xtremReporting.enums.ReportTypeDataType,
        defaultValue() {
            return 'printedDocument';
        },
    })
    readonly reportType: Promise<xtremReporting.enums.ReportType>;

    @decorators.collectionProperty<Report, 'variables'>({
        isPublished: true,
        isVital: true,
        dependsOn: ['printingType'],
        reverseReference: 'report',
        node: () => xtremReporting.nodes.ReportVariable,
    })
    readonly variables: Collection<xtremReporting.nodes.ReportVariable>;

    @decorators.booleanProperty<Report, 'isFactory'>({
        isStored: true,
        isPublished: true,
        defaultValue() {
            return false;
        },
    })
    readonly isFactory: Promise<boolean>;

    @decorators.collectionProperty<Report, 'pageAssignments'>({
        isPublished: true,
        isAssociation: true,
        reverseReference: 'report',
        node: () => xtremReporting.nodes.ReportAssignmentAssociation,
    })
    pageAssignments: Collection<xtremReporting.nodes.ReportAssignmentAssociation>;

    @decorators.enumProperty<Report, 'printingType'>({
        isStored: true,
        isPublished: true,
        dependsOn: ['reportType'],
        dataType: () => xtremReporting.enums.printingTypeDataType,
        serviceOptions: () => [xtremReporting.serviceOptions.reportAssignment],
        defaultValue() {
            return 'notApplicable';
        },
        async control(cx, val) {
            const reportType = await this.reportType;
            if (reportType !== 'printedDocument' && val !== 'notApplicable') {
                cx.error.add(
                    this.$.context.localize(
                        '@sage/xtrem-reporting/nodes__report__only-printed-document-reports-can-be-single-or-multiple-prints',
                        "Only '{{printedDocument}}' reports can be single or multiple prints.",
                        {
                            printedDocument: this.$.context.localizeEnumMember(
                                '@sage/xtrem-reporting/ReportType',
                                'printedDocument',
                            ),
                        },
                    ),
                );
            }
        },
    })
    readonly printingType: Promise<xtremReporting.enums.PrintingType>;

    @decorators.referenceProperty<Report, 'preProcessingOperation'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        filters: {
            lookup: {
                isPublished: true,
                name: {
                    _nin: ['asyncExport'],
                },
                _or: [
                    {
                        kind: {
                            _in: ['mutation', 'query'],
                        },
                    },
                    {
                        kind: 'asyncMutation',
                        action: 'start',
                    },
                ],
            },
        },
        node: () => xtremMetadata.nodes.MetaNodeOperation,
        async isFrozen() {
            return !(await this.$.context.isServiceOptionEnabled(xtremSystem.serviceOptions.DevTools));
        },
    })
    preProcessingOperation: Reference<xtremMetadata.nodes.MetaNodeOperation | null>;

    @decorators.referenceProperty<Report, 'postProcessingOperation'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        filters: {
            lookup: {
                isPublished: true,
                name: {
                    _nin: ['asyncExport'],
                },
                _or: [
                    {
                        kind: {
                            _in: ['mutation', 'query'],
                        },
                    },
                    {
                        kind: 'asyncMutation',
                        action: 'start',
                    },
                ],
            },
        },
        node: () => xtremMetadata.nodes.MetaNodeOperation,
        async isFrozen() {
            return !(await this.$.context.isServiceOptionEnabled(xtremSystem.serviceOptions.DevTools));
        },
    })
    postProcessingOperation: Reference<xtremMetadata.nodes.MetaNodeOperation | null>;

    // TODO: this method is deprecated. To be removed.
    @decorators.mutation<typeof Report, 'generateReports'>({
        isPublished: true,
        startsReadOnly: true,
        parameters: [
            {
                name: 'reportName',
                type: 'string',
                isMandatory: true,
            },
            {
                name: 'reportTemplateName',
                type: 'string',
                isMandatory: true,
            },
            {
                name: 'reportSettings',
                type: 'array',
                isMandatory: true,
                item: {
                    type: 'object',
                    name: 'ReportSettings',
                    properties: {
                        reportVariables: {
                            name: 'reportVariables',
                            type: 'string',
                            isMandatory: true,
                        },
                        locale: {
                            name: 'locale',
                            type: 'string',
                            isMandatory: false,
                        },
                        paperFormat: {
                            name: 'paperFormat',
                            type: 'string',
                            isMandatory: false,
                        },
                        pageOrientation: {
                            name: 'pageOrientation',
                            type: 'string',
                            isMandatory: false,
                        },
                        leftMarginCm: {
                            name: 'leftMarginCm',
                            type: 'decimal',
                            isMandatory: false,
                        },
                        rightMarginCm: {
                            name: 'rightMarginCm',
                            type: 'decimal',
                            isMandatory: false,
                        },
                        topMarginCm: {
                            name: 'topMarginCm',
                            type: 'decimal',
                            isMandatory: false,
                        },
                        bottomMarginCm: {
                            name: 'bottomMarginCm',
                            type: 'decimal',
                            isMandatory: false,
                        },
                        documentTitle: {
                            name: 'documentTitle',
                            type: 'string',
                            isMandatory: false,
                        },
                    },
                },
            },
        ],
        return: {
            type: 'array',
            item: {
                type: 'binaryStream',
            },
        },
    })
    static async generateReports(
        context: Context,
        reportName: string,
        reportTemplateName: string,
        reportSettings: xtremReporting.functions.IncomingReportGenerationSettings[],
    ): Promise<BinaryStream[]> {
        const parsedInput: xtremReporting.functions.ReportGenerationSettings[] =
            reportSettings.map<xtremReporting.functions.ReportGenerationSettings>(r => ({
                ...r,
                locale: xtremReporting.functions.getLocaleToUse(context, r.locale),
                variables: r.variables ? JSON.parse(r.variables) : {},
            }));

        const reportObject = await xtremReporting.functions.getReportObjectFromName(context, reportName, parsedInput);
        await xtremReporting.functions.checkPrePostOperationAreOnlyUsedInReadOnlyContext(context, reportObject);

        const htmlDocuments = await xtremReporting.functions.generateReports(
            context,
            reportName,
            reportTemplateName,
            parsedInput,
        );

        const reportTemplate = await xtremReporting.functions.getReportTemplateFromName(
            context,
            reportObject,
            reportTemplateName,
        );
        const defaultTemplatePaperFormat = await reportTemplate.defaultPaperFormat;
        const defaultPageOrientation = await reportTemplate.defaultPageOrientation;
        const defaultTopMargin = await reportTemplate.defaultTopMargin;
        const defaultBottomMargin = await reportTemplate.defaultBottomMargin;
        const defaultLeftMargin = await reportTemplate.defaultLeftMargin;
        const defaultRightMargin = await reportTemplate.defaultRightMargin;

        return asyncArray(htmlDocuments)
            .map(
                async (
                    {
                        populatedBodyContent,
                        populatedHeaderContent,
                        populatedFooterContent,
                        populatedAttachmentContent,
                    },
                    index: number,
                ) => {
                    const paperFormatToUse = xtremReporting.functions.getPaperFormatToUse(
                        parsedInput[index].locale,
                        parsedInput[index].paperFormat || defaultTemplatePaperFormat,
                    );

                    const topMarginCm = parsedInput[index].topMarginCm || defaultTopMargin || 2;
                    const bottomMarginCm = parsedInput[index].bottomMarginCm || defaultBottomMargin || 2;
                    const leftMarginCm = parsedInput[index].leftMarginCm || defaultLeftMargin || 2;
                    const rightMarginCm = parsedInput[index].rightMarginCm || defaultRightMargin || 2;

                    const pageOrientationToUse = parsedInput[index].pageOrientation || defaultPageOrientation;

                    return BinaryStream.fromBuffer(
                        await generatePdfData({
                            context,
                            reportObject,
                            reportName,
                            populatedBodyContent,
                            populatedHeaderContent,
                            populatedFooterContent,
                            paperFormat: paperFormatToUse,
                            pageOrientation: pageOrientationToUse,
                            outputPath: '',
                            populatedAttachment: populatedAttachmentContent,
                            attachmentName: await reportTemplate.attachmentName,
                            attachmentMimeType: await reportTemplate.attachmentMimeType,
                            bottomMarginCm,
                            leftMarginCm,
                            rightMarginCm,
                            topMarginCm,
                            documentTitle: parsedInput[index].documentTitle,
                        }),
                    );
                },
            )
            .toArray();
    }

    @decorators.asyncMutation<typeof Report, 'generateReportPdf'>({
        isPublished: true,
        startsReadOnly: true,
        parameters: [
            {
                name: 'reportName',
                type: 'string',
                isMandatory: true,
            },
            {
                name: 'reportTemplateName',
                type: 'string',
                isMandatory: false,
            },
            {
                type: 'object',
                name: 'reportSettings',
                properties: {
                    variables: {
                        name: 'variables',
                        type: 'string',
                        isMandatory: true,
                    },
                    locale: {
                        name: 'locale',
                        type: 'string',
                        isMandatory: false,
                    },
                    paperFormat: {
                        name: 'paperFormat',
                        type: 'string',
                        isMandatory: false,
                    },
                    pageOrientation: {
                        name: 'pageOrientation',
                        type: 'string',
                        isMandatory: false,
                    },
                    leftMarginCm: {
                        name: 'leftMarginCm',
                        type: 'decimal',
                        isMandatory: false,
                    },
                    rightMarginCm: {
                        name: 'rightMarginCm',
                        type: 'decimal',
                        isMandatory: false,
                    },
                    topMarginCm: {
                        name: 'topMarginCm',
                        type: 'decimal',
                        isMandatory: false,
                    },
                    bottomMarginCm: {
                        name: 'bottomMarginCm',
                        type: 'decimal',
                        isMandatory: false,
                    },
                    documentTitle: {
                        name: 'documentTitle',
                        type: 'string',
                        isMandatory: false,
                    },
                },
            },
        ],
        return: {
            type: 'object',
            properties: {
                key: 'string',
                filename: 'string',
                mimeType: 'string',
                contentLength: 'integer',
                status: 'string',
                rejectReason: 'string',
                canSkipAntivirusScan: 'boolean',
                uploadUrl: 'string',
                downloadUrl: 'string',
            },
        },
    })
    static async generateReportPdf(
        context: Context,
        reportName: string,
        reportTemplateName: string,
        reportSettings: xtremReporting.functions.IncomingReportGenerationSettings,
    ): Promise<NodePayloadData<xtremUpload.nodes.UploadedFile>> {
        return (await Report.generateUploadedFile(context, reportName, reportTemplateName, reportSettings)).$.payload();
    }

    @decorators.asyncMutation<typeof Report, 'generateUploadedFile'>({
        isPublished: true,
        startsReadOnly: true,
        parameters: [
            {
                name: 'reportName',
                type: 'string',
                isMandatory: true,
            },
            {
                name: 'reportTemplateName',
                type: 'string',
                isMandatory: false,
            },
            {
                type: 'object',
                name: 'reportSettings',
                properties: {
                    variables: {
                        name: 'variables',
                        type: 'string',
                        isMandatory: true,
                    },
                    locale: {
                        name: 'locale',
                        type: 'string',
                        isMandatory: false,
                    },
                    paperFormat: {
                        name: 'paperFormat',
                        type: 'string',
                        isMandatory: false,
                    },
                    pageOrientation: {
                        name: 'pageOrientation',
                        type: 'string',
                        isMandatory: false,
                    },
                    leftMarginCm: {
                        name: 'leftMarginCm',
                        type: 'decimal',
                        isMandatory: false,
                    },
                    rightMarginCm: {
                        name: 'rightMarginCm',
                        type: 'decimal',
                        isMandatory: false,
                    },
                    topMarginCm: {
                        name: 'topMarginCm',
                        type: 'decimal',
                        isMandatory: false,
                    },
                    bottomMarginCm: {
                        name: 'bottomMarginCm',
                        type: 'decimal',
                        isMandatory: false,
                    },
                    documentTitle: {
                        name: 'documentTitle',
                        type: 'string',
                        isMandatory: false,
                    },
                },
            },
        ],
        return: {
            type: 'reference',
            node: () => xtremUpload.nodes.UploadedFile,
        },
    })
    static async generateUploadedFile(
        context: Context,
        reportName: string,
        reportTemplateName: string,
        reportSettings: xtremReporting.functions.IncomingReportGenerationSettings,
    ): Promise<xtremUpload.nodes.UploadedFile> {
        logger.verbose(() => `Generating report ${reportName} with settings: ${JSON.stringify(reportSettings)}`);

        const parsedInput = [
            {
                ...reportSettings,
                locale: xtremReporting.functions.getLocaleToUse(context, reportSettings.locale),
                variables: reportSettings.variables ? JSON.parse(reportSettings.variables) : {},
            },
        ] as xtremReporting.functions.ReportGenerationSettings[];

        const reportObject = await xtremReporting.functions.getReportObjectFromName(context, reportName, parsedInput);
        await xtremReporting.functions.checkPrePostOperationAreOnlyUsedInReadOnlyContext(context, reportObject);

        const reportTemplate = await xtremReporting.functions.getReportTemplateFromName(
            context,
            reportObject,
            reportTemplateName,
        );
        const variables = reportSettings.variables ? JSON.parse(reportSettings.variables) : {};
        const localeToUse = xtremReporting.functions.getLocaleToUse(context, reportSettings.locale);
        const defaultTemplatePaperFormat = await reportTemplate.defaultPaperFormat;
        const defaultTemplatePageOrientation = await reportTemplate.defaultPageOrientation;
        const paperFormatToUse = xtremReporting.functions.getPaperFormatToUse(
            localeToUse,
            reportSettings.paperFormat || defaultTemplatePaperFormat,
        );

        const topMarginCm = reportSettings.topMarginCm || (await reportTemplate?.defaultTopMargin) || 2;
        const bottomMarginCm = reportSettings.bottomMarginCm || (await reportTemplate?.defaultBottomMargin) || 2;
        const leftMarginCm = reportSettings.leftMarginCm || (await reportTemplate?.defaultLeftMargin) || 2;
        const rightMarginCm = reportSettings.rightMarginCm || (await reportTemplate?.defaultRightMargin) || 2;

        const { populatedBodyContent, populatedFooterContent, populatedHeaderContent, populatedAttachmentContent } =
            await xtremReporting.functions.generateReport(context, reportName, reportTemplateName, {
                variables,
                locale: localeToUse,
                paperFormat: paperFormatToUse,
            });

        const pageOrientationToUse = reportSettings.pageOrientation || defaultTemplatePageOrientation;

        const timestamp = xtremDateTime.datetime
            .now()
            .format(
                reportSettings.locale ? (reportSettings.locale.replace('_', '-') as LocalizeLocale) : 'base',
                'YYYY-MM-DD-HH-mm-ss',
            );
        const filename = Report._generateReportFilename(reportName, timestamp);
        const _objectKey = `print-output/${filename}.pdf`;

        const uploadedFile = await this.createUploadedFile(context, {
            kind: 'upload',
            objectKey: _objectKey,
            filename: `${filename}.pdf`,
            reportName,
            timestamp,
        });

        try {
            await xtremInfrastructureAdapter.InfrastructureHelper.createFile(
                context,
                await uploadedFile.key,
                `Generated report - ${reportName}`,
                await generatePdfData({
                    context,
                    reportName,
                    reportObject,
                    populatedBodyContent,
                    populatedHeaderContent,
                    populatedFooterContent,
                    paperFormat: paperFormatToUse,
                    pageOrientation: pageOrientationToUse,
                    populatedAttachment: populatedAttachmentContent,
                    attachmentName: await reportTemplate.attachmentName,
                    attachmentMimeType: await reportTemplate.attachmentMimeType,
                    leftMarginCm,
                    rightMarginCm,
                    topMarginCm,
                    bottomMarginCm,
                    documentTitle: reportSettings.documentTitle ?? filename,
                    additionalParameters: {
                        uploadedFile,
                    },
                }),
                FileTimeToLive.Expire10Days,
            );

            if (reportSettings.isBulk) {
                await context.batch.logMessage('info', await uploadedFile.filename);
            } else {
                await context.batch.logMessage('result', await uploadedFile.filename, {
                    data: {
                        downloadUrl: await uploadedFile.downloadUrl,
                        filename: await uploadedFile.filename,
                    },
                });
                await context.batch.updateProgress({
                    totalCount: 1,
                    successCount: 1,
                });
            }
        } catch (error) {
            await this.deleteUploadedFile(context, uploadedFile);
        }

        return uploadedFile;
    }

    @decorators.asyncMutation<typeof Report, 'generateReportAndNotifyUser'>({
        isPublished: true,
        startsReadOnly: true,
        parameters: [
            {
                name: 'reportName',
                type: 'string',
                isMandatory: true,
            },
            {
                name: 'reportTemplateName',
                type: 'string',
                isMandatory: false,
            },
            {
                type: 'object',
                name: 'reportSettings',
                properties: {
                    variables: {
                        name: 'variables',
                        type: 'string',
                        isMandatory: true,
                    },
                    locale: {
                        name: 'locale',
                        type: 'string',
                        isMandatory: false,
                    },
                    paperFormat: {
                        name: 'paperFormat',
                        type: 'string',
                        isMandatory: false,
                    },
                    pageOrientation: {
                        name: 'pageOrientation',
                        type: 'string',
                        isMandatory: false,
                    },
                    documentTitle: {
                        name: 'documentTitle',
                        type: 'string',
                        isMandatory: false,
                    },
                },
            },
        ],
        return: 'boolean',
    })
    static async generateReportAndNotifyUser(
        context: Context,
        reportName: string,
        reportTemplateName: string,
        reportSettings: xtremReporting.functions.IncomingReportGenerationSettings,
    ): Promise<boolean> {
        let status = 'rejected';
        let rejectReason = '';
        let downloadUrl = '';
        let success = true;
        try {
            const result = await xtremReporting.nodes.Report.generateReportPdf(
                context,
                reportName,
                reportTemplateName,
                reportSettings,
            );
            status = result.status ?? 'rejected';
            rejectReason = result.rejectReason ?? '';
            downloadUrl = result.downloadUrl ?? '';
        } catch (error) {
            rejectReason = context.localize(
                '@sage/xtrem-reporting/nodes__report__error_generating_report',
                'Error during report generation: {{errorMessage}}',
                { errorMessage: error.message },
            );
            logger.error(error.message, error.stack);
            success = false;
        }

        await xtremReporting.functions.sendUserNotification(context, {
            status,
            reportName,
            rejectReason,
            downloadUrl,
        });

        return success;
    }

    @decorators.asyncMutation<typeof Report, 'generateReportZip'>({
        isPublished: true,
        startsReadOnly: true,
        parameters: [
            {
                name: 'reportName',
                type: 'string',
                isMandatory: true,
            },
            {
                name: 'reportTemplateName',
                type: 'string',
                isMandatory: false,
            },
            {
                name: 'reportSettings',
                type: 'array',
                isMandatory: true,
                item: {
                    type: 'object',
                    name: 'ReportSettings',
                    properties: {
                        variables: {
                            name: 'variables',
                            type: 'string',
                            isMandatory: true,
                        },
                        locale: {
                            name: 'locale',
                            type: 'string',
                            isMandatory: false,
                        },
                        paperFormat: {
                            name: 'paperFormat',
                            type: 'string',
                            isMandatory: false,
                        },
                        documentTitle: {
                            name: 'documentTitle',
                            type: 'string',
                            isMandatory: false,
                        },
                    },
                },
            },
        ],
        return: {
            type: 'object',
            properties: {
                key: 'string',
                filename: 'string',
                mimeType: 'string',
                contentLength: 'integer',
                status: 'string',
                rejectReason: 'string',
                canSkipAntivirusScan: 'boolean',
                uploadUrl: 'string',
                downloadUrl: 'string',
            },
        },
    })
    static async generateReportZip(
        context: Context,
        reportName: string,
        reportTemplateName: string,
        reportSettings: xtremReporting.functions.IncomingReportGenerationSettings[],
    ): Promise<NodePayloadData<xtremUpload.nodes.UploadedFile>> {
        const parsedInput: xtremReporting.functions.ReportGenerationSettings[] =
            reportSettings.map<xtremReporting.functions.ReportGenerationSettings>(r => ({
                ...r,
                locale: xtremReporting.functions.getLocaleToUse(context, r.locale),
                variables: r.variables ? JSON.parse(r.variables) : {},
            }));

        const reportObject = await xtremReporting.functions.getReportObjectFromName(context, reportName, parsedInput);
        await xtremReporting.functions.checkPrePostOperationAreOnlyUsedInReadOnlyContext(context, reportObject);

        const htmlDocuments = await xtremReporting.functions.generateReports(
            context,
            reportName,
            reportTemplateName,
            parsedInput,
        );

        const reportTemplate = await xtremReporting.functions.getReportTemplateFromName(
            context,
            reportObject,
            reportTemplateName,
        );
        const defaultTemplatePaperFormat = await reportTemplate.defaultPaperFormat;
        const defaultPageOrientation = await reportTemplate.defaultPageOrientation;

        // upload is hardcoded
        const timestamp = xtremDateTime.datetime.now().format(undefined, 'YYYY-MM-DD-HH-mm-ss');
        const filename = Report._generateReportFilename(reportName, timestamp);
        const _objectKey = `print-output/${filename}.zip`;

        const uploadedFile = await this.createUploadedFile(context, {
            kind: 'upload',
            objectKey: _objectKey,
            filename: `${filename}.zip`,
            reportName,
            timestamp,
            isArchive: true,
        });

        try {
            const reports = await asyncArray(htmlDocuments)
                .map(
                    async (
                        {
                            populatedBodyContent,
                            populatedHeaderContent,
                            populatedFooterContent,
                            populatedAttachmentContent,
                        },
                        index: number,
                    ) => {
                        const paperFormatToUse = xtremReporting.functions.getPaperFormatToUse(
                            parsedInput[index].locale,
                            parsedInput[index].paperFormat || defaultTemplatePaperFormat,
                        );
                        const pageOrientationToUse = parsedInput[index].pageOrientation || defaultPageOrientation;

                        return BinaryStream.fromBuffer(
                            await generatePdfData({
                                context,
                                reportObject,
                                reportName,
                                populatedBodyContent,
                                populatedHeaderContent,
                                populatedFooterContent,
                                paperFormat: paperFormatToUse,
                                pageOrientation: pageOrientationToUse,
                                populatedAttachment: populatedAttachmentContent,
                                attachmentName: await reportTemplate.attachmentName,
                                attachmentMimeType: await reportTemplate.attachmentMimeType,
                                leftMarginCm: parsedInput[index].leftMarginCm || 2,
                                rightMarginCm: parsedInput[index].rightMarginCm || 2,
                                topMarginCm: parsedInput[index].topMarginCm || 2,
                                bottomMarginCm: parsedInput[index].bottomMarginCm || 2,
                                documentTitle: parsedInput[index].documentTitle,
                                additionalParameters: {
                                    uploadedFile,
                                },
                            }),
                        );
                    },
                )
                .toArray();

            const zip = new JSZip();

            reports.forEach((report: BinaryStream, index: number) => {
                zip.file(`${filename}-${index}.pdf`, report.value, { binary: true });
            });

            const zipContent = await zip.generateAsync({ type: 'nodebuffer' });
            await xtremInfrastructureAdapter.InfrastructureHelper.createFile(
                context,
                await uploadedFile.key,
                `Generated archive - ${reportName}`,
                zipContent,
                FileTimeToLive.Expire10Days,
            );

            await context.batch.logMessage('result', await uploadedFile.filename, {
                data: {
                    downloadUrl: await uploadedFile.downloadUrl,
                    filename: await uploadedFile.filename,
                },
            });
        } catch (error) {
            await this.deleteUploadedFile(context, uploadedFile);

            // let the framework handle the error
            throw error;
        }

        return uploadedFile.$.payload();
    }

    /**
     *
     * @param context
     * @param parameters
     * @private
     */
    static async createUploadedFile(
        context: Context,
        {
            objectKey,
            filename,
            reportName,
            timestamp,
            isArchive,
        }: {
            kind: xtremUpload.enums.UploadedFileKind;
            objectKey: string;
            filename: string;
            reportName: string;
            timestamp: string;
            isArchive?: boolean;
        },
    ): Promise<xtremUpload.nodes.UploadedFile> {
        const uploadedFileId = await context.runInIsolatedContext(async isolatedContext => {
            const createUploadedFileNode = async (key: string, keyFilename: string) => {
                const uploadFileNode = await isolatedContext.create(xtremUpload.nodes.UploadedFile, {
                    key,
                    status: 'verified',
                    canSkipAntivirusScan: true,
                    filename: keyFilename,
                    mimeType: isArchive ? 'application/zip' : 'application/pdf',
                    kind: 'upload',
                    expirationDate: datetime.now().addDays(10),
                });

                await uploadFileNode.$.save();

                return uploadFileNode;
            };
            let count = 0;
            let result: xtremUpload.nodes.UploadedFile | undefined;
            let _filename = filename;
            let _objectKey = objectKey;
            while (result == null) {
                const fileExist = await isolatedContext.tryRead(xtremUpload.nodes.UploadedFile, { key: _objectKey });
                if (!fileExist) {
                    result = await createUploadedFileNode(_objectKey, _filename);
                } else {
                    await sleepMillis(1000);
                    count += 1;
                    _filename = `${kebabCase(reportName)}-${timestamp}-(${count}).${isArchive ? 'zip' : 'pdf'}`;
                    _objectKey = `print-output/${_filename}`;
                }
            }

            return result._id;
        });

        return context.read(xtremUpload.nodes.UploadedFile, { _id: uploadedFileId });
    }

    /**
     *
     * @param context
     * @param uploadedFile
     * @returns
     * @private
     */
    static deleteUploadedFile(context: Context, uploadedFile: xtremUpload.nodes.UploadedFile) {
        return context.runInIsolatedContext(async isolatedContext => {
            const deleteSafeUploaded = await isolatedContext.read(
                xtremUpload.nodes.UploadedFile,
                { _id: uploadedFile._id },
                {
                    forUpdate: true,
                },
            );

            return deleteSafeUploaded.$.delete();
        });
    }

    static async generateZipToS3(
        context: Context,
        reports: NodePayloadData<xtremUpload.nodes.UploadedFile>[],
        reportName: string,
    ): Promise<NodePayloadData<xtremUpload.nodes.UploadedFile>> {
        const timestamp = xtremDateTime.datetime.now().format(undefined, 'YYYY-MM-DD-HH-mm-ss');
        const filename = Report._generateReportFilename(reportName, timestamp);

        const tempPath = fsp.join(os.tmpdir(), kebabCase(reportName), timestamp);
        const tempZipDir = fsp.join(tempPath, 'zip');
        const tempFilesDir = fsp.join(tempPath, 'reports');

        const zipFilename = fsp.join(tempZipDir, `${filename}.zip`);

        try {
            await fs.mkdir(tempPath, { recursive: true });
            await fs.mkdir(tempFilesDir, { recursive: true });
            await fs.mkdir(tempZipDir, { recursive: true });

            await Promise.all(reports.map(report => this.writeReport(context, tempFilesDir, report)));

            const output = createWriteStream(zipFilename);
            const archive = archiver('zip');
            archive.pipe(output);
            archive.directory(tempFilesDir, false);
            await archive.finalize();

            const zipContent = await fs.readFile(zipFilename);

            const result = await xtremReporting.nodes.Report.createUploadedFile(context, {
                kind: 'upload',
                objectKey: `print-output/${filename}.zip`,
                filename: `${filename}.zip`,
                reportName,
                timestamp,
                isArchive: true,
            });

            await xtremInfrastructureAdapter.InfrastructureHelper.createFile(
                context,
                await result.key,
                `Generated archive - ${reportName}`,
                zipContent,
                FileTimeToLive.Expire10Days,
            );
            return await result.$.payload();
        } finally {
            if (await fs.stat(tempPath)) await fs.rm(tempPath, { recursive: true });
        }
    }

    @decorators.mutation<typeof Report, 'createOrUpdateReport'>({
        isPublished: true,
        parameters: [
            {
                name: 'data',
                type: 'object',
                isMandatory: true,
                properties: {
                    _id: {
                        name: '_id',
                        type: 'string',
                    },
                    name: {
                        name: 'name',
                        type: 'string',
                    },
                    templateId: {
                        name: 'templateId',
                        type: 'string',
                    },
                    templateName: {
                        name: 'templateName',
                        type: 'string',
                    },
                    description: {
                        name: 'description',
                        type: 'string',
                    },
                    parentPackage: {
                        name: 'parentPackage',
                        type: 'string',
                    },
                    externalHtmlTemplate: {
                        name: 'externalHtmlTemplate',
                        type: 'textStream',
                    },
                    dataSource: {
                        name: 'dataSource',
                        type: 'reference',
                        node: () => xtremMetadata.nodes.MetaNodeFactory,
                    },
                    selectedProperties: {
                        name: 'selectedProperties',
                        type: 'string',
                    },
                    parameters: {
                        name: 'parameters',
                        type: 'string',
                    },
                    filters: {
                        name: 'filters',
                        type: 'string',
                    },
                    content: {
                        name: 'content',
                        type: 'string',
                    },
                    variables: {
                        name: 'variables',
                        type: 'string',
                        isMandatory: false,
                    },
                    reportSettings: {
                        type: 'object',
                        name: 'reportSettings',
                        properties: {
                            locale: {
                                name: 'locale',
                                type: 'string',
                                isMandatory: false,
                            },
                            paperFormat: {
                                name: 'paperFormat',
                                type: 'string',
                                isMandatory: false,
                            },
                            pageOrientation: {
                                name: 'pageOrientation',
                                type: 'string',
                                isMandatory: false,
                            },
                            leftMarginCm: {
                                name: 'leftMarginCm',
                                type: 'decimal',
                                isMandatory: false,
                            },
                            rightMarginCm: {
                                name: 'rightMarginCm',
                                type: 'decimal',
                                isMandatory: false,
                            },
                            topMarginCm: {
                                name: 'topMarginCm',
                                type: 'decimal',
                                isMandatory: false,
                            },
                            bottomMarginCm: {
                                name: 'bottomMarginCm',
                                type: 'decimal',
                                isMandatory: false,
                            },
                            isDefaultHeaderFooter: {
                                name: 'isDefaultHeaderFooter',
                                type: 'boolean',
                                isMandatory: false,
                            },
                        },
                    },
                    printType: {
                        name: 'printType',
                        type: 'enum',
                        dataType: () => xtremReporting.enums.printingTypeDataType,
                    },
                },
            },
        ],
        return: {
            type: 'object',
            properties: {
                reportId: {
                    name: 'reportId',
                    type: 'string',
                },
                reportTemplateId: {
                    name: 'reportTemplateId',
                    type: 'string',
                },
            },
        },
    })
    static async createOrUpdateReport(
        context: Context,
        data: xtremReporting.functions.ReportCreationInput,
    ): Promise<{ reportId: string; reportTemplateId: string }> {
        const {
            _id,
            name,
            templateId,
            templateName,
            description,
            parentPackage,
            externalHtmlTemplate,
            dataSource,
            selectedProperties,
            content,
            filters,
            parameters,
            variables,
            printingType,
        } = data;
        const {
            locale,
            paperFormat,
            pageOrientation,
            leftMarginCm,
            rightMarginCm,
            topMarginCm,
            bottomMarginCm,
            isDefaultHeaderFooter,
        } = data.reportSettings;

        logger.debug(
            () => `Start createOrUpdate _id:${_id} name:${name} templateId:${templateId} templateName:${templateName}`,
        );
        let report: Report | null = null;
        const reportData = {
            name,
            description,
            parentPackage,
            ...(variables ? { variables: JSON.parse(variables) } : {}),
            printingType,
        };
        if (_id) {
            report = await context.read(xtremReporting.nodes.Report, { _id }, { forUpdate: true });

            await report.$.set(reportData);
        } else {
            report = await context.create(xtremReporting.nodes.Report, reportData);
        }
        await report.$.save();
        let reportTemplate: xtremReporting.nodes.ReportTemplate | null = null;
        if (templateId) {
            reportTemplate = await context.tryRead(
                xtremReporting.nodes.ReportTemplate,
                { _id: templateId },
                { forUpdate: true },
            );
        }

        const reportTemplateName = Report.getTemplateName(templateName, await reportTemplate?.name, await report.name);
        logger.debug(() => `Creating report template ${reportTemplateName}`);

        const templateData = {
            report,
            isFactory: false,
            name: camelCase(reportTemplateName),
            isExpertDocument: false,
            externalHtmlTemplate,
            reportWizard: {
                id: camelCase(reportTemplateName),
                name: reportTemplateName,
                description,
                dataSource,
                selectedProperties: JSON.parse(selectedProperties),
                content: JSON.parse(content),
                filters: JSON.parse(filters),
                parameters: JSON.parse(parameters),
            },
            baseLocale: locale,
            defaultPaperFormat: paperFormat,
            defaultPageOrientation: pageOrientation,
            defaultTopMargin: topMarginCm,
            defaultBottomMargin: bottomMarginCm,
            defaultLeftMargin: leftMarginCm,
            defaultRightMargin: rightMarginCm,
            isDefaultHeaderFooter,
            footerHtmlTemplate: isDefaultHeaderFooter ? new TextStream(defaultHtmlFooter()) : new TextStream(''),
            headerHtmlTemplate: isDefaultHeaderFooter
                ? new TextStream(defaultHtmlHeader(await report.name))
                : new TextStream(''),
        };

        if (reportTemplate) {
            await reportTemplate.$.set(templateData);
        } else {
            reportTemplate = await context.create(xtremReporting.nodes.ReportTemplate, templateData);
        }

        await reportTemplate.$.save();

        await logger.debugAsync(async () => `Report active template ${await report?.activeTemplate}`);
        if (!(await report?.activeTemplate)) {
            logger.debug(() => `Update active template ${reportTemplate?._id}`);
            await report.$.set({ activeTemplate: reportTemplate });
            await report.$.save();
            await logger.debugAsync(async () => `Updated active template ${await report?.activeTemplate}`);
        }

        return { reportId: `${report._id}`, reportTemplateId: `${reportTemplate._id}` };
    }

    static async writeReport(
        context: Context,
        tempFilesDir: string,
        report: NodePayloadData<xtremUpload.nodes.UploadedFile>,
    ): Promise<void> {
        // we should log the error, but for now there is a weird issue with loading that makes the process crash
        if (!report.key || !report.filename || !report.downloadUrl) {
            return;
        }
        const downloadUrl = new URL(report.downloadUrl);
        const encodedTarget = downloadUrl.searchParams.get('t');
        if (!encodedTarget) {
            return;
        }
        const targetLocation = await FileStorageManager.getTargetDownloadUrl(context, encodedTarget);
        const response = await axios.get(targetLocation, { responseType: 'stream' });
        await fs.writeFile(fsp.join(tempFilesDir, `${report.filename}`), response.data);
    }

    static getTemplateName(
        templateName: string | undefined,
        reportTemplateName: string | undefined,
        reportName: string,
    ): string {
        if (templateName && templateName !== '') {
            return templateName;
        }
        if (reportTemplateName && reportTemplateName !== '') {
            return reportTemplateName;
        }
        if (reportName && reportName !== '') {
            return reportName;
        }
        return '';
    }

    /**
     * Generates a unique report filename
     * @param reportName
     * @param timestamp
     * @returns
     */
    private static _generateReportFilename(reportName: string, timestamp: string): string {
        return `${kebabCase(reportName)}-${timestamp}-${nanoid(8)}`;
    }

    @decorators.asyncMutation<typeof Report, 'printRecords'>({
        isPublished: true,
        startsReadOnly: false,
        isGlobal: true,
        queue: 'reporting',
        parameters: [
            {
                name: 'filter',
                type: 'string',
                isMandatory: true,
            },
            {
                name: 'nodeName',
                type: 'string',
                isMandatory: true,
            },
            {
                name: 'reportTemplate',
                type: 'reference',
                node: () => xtremReporting.nodes.ReportTemplate,
                isMandatory: true,
            },
            {
                name: 'paperFormat',
                type: 'enum',
                dataType: () => xtremReporting.enums.ReportPaperFormatDataType,
                isMandatory: true,
            },
            {
                name: 'pageOrientation',
                type: 'enum',
                dataType: () => xtremReporting.enums.ReportPageOrientationDataType,
                isMandatory: true,
            },
            {
                name: 'joinDocuments',
                type: 'boolean',
                isMandatory: true,
            },
            {
                name: 'reportParameters',
                type: 'json',
                isMandatory: true,
            },
        ],
        return: {
            type: 'string',
        },
    })
    static async printRecords(
        context: Context,
        filter: string,
        nodeName: string,
        reportTemplate: xtremReporting.nodes.ReportTemplate,
        paperFormat: xtremReporting.enums.ReportPaperFormat,
        pageOrientation: xtremReporting.enums.ReportPageOrientation,
        joinDocuments: boolean,
        reportParameters: Dict<any>,
    ): Promise<string> {
        const parsedFilter = JSON.parse(filter);
        logger.debug(() => `Parsed Filter: ${JSON.stringify(parsedFilter)}`);
        const nodeFactory = context.application.getFactoryByName(nodeName);
        const items = await nodeFactory.query(context, { filter: parsedFilter });
        await items.forEach(item => {
            logger.debug(() => `item to be printed: ${item._id} - ${item.$.factory.name}`);
        });
        logger.debug(() => `Filter: ${filter}`);
        logger.debug(() => `Node Name: ${nodeName}`);
        logger.debug(() => `Report Template: ${reportTemplate._id}`);
        logger.debug(() => `Paper Format: ${paperFormat}`);
        logger.debug(() => `Page Orientation: ${pageOrientation}`);
        logger.debug(() => `Join Documents: ${joinDocuments}`);
        logger.debug(() => `Report parameters: ${JSON.stringify(reportParameters)}`);
        return 'test';
    }
}
