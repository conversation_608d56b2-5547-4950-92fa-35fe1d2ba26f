import { decorators, Node } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremReporting from '../index';

@decorators.node<ReportStyleVariable>({
    package: 'xtrem-reporting',
    storage: 'sql',
    indexes: [{ orderBy: { name: 1 }, isUnique: true, isNaturalKey: true }],
    isSetupNode: true,
    isPublished: true,
    canCreate: true,
    canRead: true,
    canSearch: true,
    canDelete: true,
    canUpdate: true,
    canDuplicate: true,
})
export class ReportStyleVariable extends Node {
    @decorators.stringProperty<ReportStyleVariable, 'name'>({
        isStored: true,
        isPublished: true,
        isNotEmpty: true,
        isFrozen: true,
        dataType: () => xtremSystem.dataTypes.name,
        duplicateRequiresPrompt: true,
        duplicatedValue: '',
    })
    readonly name: Promise<string>;

    @decorators.enumProperty<ReportStyleVariable, 'type'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        dataType: () => xtremReporting.enums.reportStyleVariableTypeDataType,
    })
    readonly type: Promise<xtremReporting.enums.ReportStyleVariableType | null>;

    @decorators.stringProperty<ReportStyleVariable, 'description'>({
        isStored: true,
        isPublished: true,
        isNotEmpty: true,
        dataType: () => xtremSystem.dataTypes.description,
        duplicateRequiresPrompt: true,
    })
    readonly description: Promise<string>;

    @decorators.stringProperty<ReportStyleVariable, 'value'>({
        isStored: true,
        isPublished: true,
        isNotEmpty: true,
        dataType: () => xtremReporting.dataTypes.styleVariableValueDataType,
        duplicateRequiresPrompt: true,
    })
    readonly value: Promise<string>;

    @decorators.booleanProperty<ReportStyleVariable, 'isFactory'>({
        isStored: true,
        isPublished: true,
        duplicatedValue: false,
    })
    readonly isFactory: Promise<boolean>;
}
