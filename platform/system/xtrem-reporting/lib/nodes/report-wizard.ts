import { decorators, Node, Reference } from '@sage/xtrem-core';
import * as xtremMetadata from '@sage/xtrem-metadata';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremReporting from '../index';
import { Content, ReportFilter, ReportParameters, SelectedProperty } from '../interfaces/list-report';

@decorators.node<ReportWizard>({
    package: 'xtrem-reporting',
    storage: 'sql',
    isPublished: true,
    canRead: true,
    canSearch: true,
    canDelete: true,
    isVitalReferenceChild: true,
})
export class ReportWizard extends Node {
    @decorators.referenceProperty<ReportWizard, 'reportTemplate'>({
        isStored: true,
        isPublished: true,
        node: () => xtremReporting.nodes.ReportTemplate,
        lookupAccess: true,
        isVitalParent: true,
    })
    readonly reportTemplate: Reference<xtremReporting.nodes.ReportTemplate>;

    @decorators.stringProperty<ReportWizard, 'id'>({
        isStored: true,
        isPublished: true,
        isNotEmpty: true,
        dataType: () => xtremSystem.dataTypes.id,
        lookupAccess: true,
    })
    readonly id: Promise<string>;

    @decorators.stringProperty<ReportWizard, 'name'>({
        isStored: true,
        isPublished: true,
        isNotEmpty: true,
        dataType: () => xtremSystem.dataTypes.name,
        lookupAccess: true,
    })
    readonly name: Promise<string>;

    @decorators.stringProperty<ReportWizard, 'description'>({
        isStored: true,
        isPublished: true,
        isNotEmpty: true,
        dataType: () => xtremSystem.dataTypes.description,
        lookupAccess: true,
    })
    readonly description: Promise<string>;

    @decorators.referenceProperty<ReportWizard, 'dataSource'>({
        isStored: true,
        isPublished: true,
        node: () => xtremMetadata.nodes.MetaNodeFactory,
        lookupAccess: true,
    })
    readonly dataSource: Reference<xtremMetadata.nodes.MetaNodeFactory>;

    @decorators.jsonProperty<ReportWizard, 'selectedProperties'>({
        isStored: true,
        isPublished: true,
    })
    readonly selectedProperties: Promise<SelectedProperty[]>;

    @decorators.jsonProperty<ReportWizard, 'filters'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
    })
    readonly filters: Promise<ReportFilter>;

    @decorators.jsonProperty<ReportWizard, 'parameters'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
    })
    readonly parameters: Promise<ReportParameters>;

    @decorators.jsonProperty<ReportWizard, 'content'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
    })
    readonly content: Promise<Content[]>;

    @decorators.enumProperty<ReportWizard, 'templateType'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremReporting.enums.TemplateTypeDataType,
        defaultValue: () => 'list',
    })
    readonly templateType: Promise<xtremReporting.enums.TemplateType>;
}
