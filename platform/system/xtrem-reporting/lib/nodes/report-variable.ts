import { Node, Reference, decorators, titleCase } from '@sage/xtrem-core';
import * as xtremMetadata from '@sage/xtrem-metadata';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremReporting from '../index';

@decorators.node<ReportVariable>({
    package: 'xtrem-reporting',
    storage: 'sql',
    indexes: [
        {
            orderBy: { report: 1, name: 1 },
            isUnique: true,
        },
    ],
    isPublished: true,
    canRead: true,
    canSearch: true,
    canDelete: true,
    isVitalCollectionChild: true,
})
export class ReportVariable extends Node implements xtremMetadata.interfaces.ParameterNode {
    @decorators.stringProperty<ReportVariable, 'name'>({
        isStored: true,
        isPublished: true,
        isNotEmpty: true,
        isFrozen: true,
        dataType: () => xtremSystem.dataTypes.name,
        lookupAccess: true,
        control(cx, val) {
            if (val && val.indexOf(' ') >= 0) {
                cx.error.add(
                    this.$.context.localize(
                        '@sage/xtrem-reporting/nodes__report-variable__no-spaces-allowed-in-variable-name',
                        'Variable name must not contain spaces.',
                    ),
                );
            }
        },
    })
    readonly name: Promise<string>;

    @decorators.stringProperty<ReportVariable, 'title'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.localizedTitle,
        dependsOn: ['name'],
        async defaultValue() {
            return titleCase(await this.name);
        },
        lookupAccess: true,
    })
    readonly title: Promise<string>;

    @decorators.booleanProperty<ReportVariable, 'isMandatory'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        lookupAccess: true,
        defaultValue: false,
    })
    readonly isMandatory: Promise<boolean | null>;

    @decorators.enumProperty<ReportVariable, 'type'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMetadata.enums.MetaPropertyTypeDataType,
        lookupAccess: true,
    })
    readonly type: Promise<xtremMetadata.enums.MetaPropertyType>;

    @decorators.referenceProperty<ReportVariable, 'dataType'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        lookupAccess: true,
        node: () => xtremMetadata.nodes.MetaDataType,
    })
    readonly dataType: Reference<xtremMetadata.nodes.MetaDataType | null>;

    @decorators.referenceProperty<ReportVariable, 'report'>({
        isStored: true,
        isPublished: true,
        isFrozen: true,
        node: () => xtremReporting.nodes.Report,
        isVitalParent: true,
        lookupAccess: true,
    })
    readonly report: Reference<xtremReporting.nodes.Report>;
}
