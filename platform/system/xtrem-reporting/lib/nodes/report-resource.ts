import { BinaryStream, decorators, Node } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremReporting from '../index';

@decorators.node<ReportResource>({
    package: 'xtrem-reporting',
    storage: 'sql',
    indexes: [
        {
            orderBy: { name: 1 },
            isUnique: true,
            isNaturalKey: true,
        },
    ],
    isPublished: true,
    canCreate: true,
    canRead: true,
    canSearch: true,
    canDelete: true,
    canUpdate: true,
    canDuplicate: true,
})
export class ReportResource extends Node {
    @decorators.stringProperty<ReportResource, 'name'>({
        isStored: true,
        isPublished: true,
        isNotEmpty: true,
        dataType: () => xtremSystem.dataTypes.name,
        duplicateRequiresPrompt: true,
        duplicatedValue: '',
    })
    readonly name: Promise<string>;

    @decorators.stringProperty<ReportResource, 'description'>({
        isStored: true,
        isPublished: true,
        isNotEmpty: false,
        dataType: () => xtremSystem.dataTypes.description,
        duplicateRequiresPrompt: true,
    })
    readonly description: Promise<string>;

    @decorators.enumProperty<ReportResource, 'type'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        dataType: () => xtremReporting.enums.reportResourceTypeDataType,
        duplicateRequiresPrompt: true,
    })
    readonly type: Promise<xtremReporting.enums.ReportResourceType | null>;

    @decorators.stringProperty<ReportResource, 'mimetype'>({
        isPublished: true,
        isStored: true,
        dataType: () => xtremSystem.dataTypes.mimeType,
    })
    readonly mimetype: Promise<string>;

    @decorators.binaryStreamProperty<ReportResource, 'content'>({
        isStored: true,
        isPublished: true,
    })
    readonly content: Promise<BinaryStream>;

    @decorators.booleanProperty<ReportResource, 'isFactory'>({
        isStored: true,
        isPublished: true,
    })
    readonly isFactory: Promise<boolean>;
}
