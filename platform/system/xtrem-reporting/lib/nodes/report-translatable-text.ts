import { BusinessRuleError, Collection, decorators, Node, Reference } from '@sage/xtrem-core';
import { dataTypes } from '@sage/xtrem-system';
import * as xtremReporting from '../index';

@decorators.node<ReportTranslatableText>({
    package: 'xtrem-reporting',
    storage: 'sql',
    indexes: [
        {
            orderBy: { reportTemplate: 1, _sortValue: 1, hash: 1, locale: 1 },
            isUnique: true,
        },
    ],
    isPublished: true,
    canRead: true,
    canSearch: true,
    canDelete: true,
    isVitalCollectionChild: true,
})
export class ReportTranslatableText extends Node {
    @decorators.referenceProperty<ReportTranslatableText, 'reportTemplate'>({
        isStored: true,
        isFrozen: true,
        isPublished: true,
        node: () => xtremReporting.nodes.ReportTemplate,
        isVitalParent: true,
        lookupAccess: true,
    })
    readonly reportTemplate: Reference<xtremReporting.nodes.ReportTemplate>;

    @decorators.referenceProperty<ReportTranslatableText, 'originalText'>({
        isPublished: true,
        isNullable: true,
        isStored: true,
        isFrozen: true,
        node: () => xtremReporting.nodes.ReportTranslatableText,
    })
    readonly originalText: Reference<xtremReporting.nodes.ReportTranslatableText | null>;

    @decorators.collectionProperty<ReportTranslatableText, 'translations'>({
        isPublished: true,
        reverseReference: 'originalText',
        node: () => xtremReporting.nodes.ReportTranslatableText,
    })
    readonly translations: Collection<xtremReporting.nodes.ReportTranslatableText>;

    @decorators.stringProperty<ReportTranslatableText, 'hash'>({
        isStored: true,
        isFrozen: true,
        isPublished: true,
        dataType: () => xtremReporting.dataTypes.md5HashDataType,
        control(cx, value) {
            const regexExp = /^[a-f0-9]{32}$/gi;
            if (!regexExp.test(value)) {
                throw new BusinessRuleError(
                    this.$.context.localize(
                        '@sage/xtrem-reporting/nodes__report_translatable_text__invalid_hash_value',
                        'The hash value is not a valid MD5 value.',
                    ),
                );
            }
        },
    })
    readonly hash: Promise<string>;

    @decorators.enumProperty<ReportTranslatableText, 'locale'>({
        isPublished: true,
        isStored: true,
        dataType: () => xtremReporting.enums.ReportLocaleDataType,
    })
    readonly locale: Promise<xtremReporting.enums.ReportLocale>;

    @decorators.booleanProperty<ReportTranslatableText, 'isBaseLocale'>({
        isStored: true,
        isPublished: true,
        isFrozen: true,
        dependsOn: ['reportTemplate', 'hash'],
        defaultValue() {
            return false;
        },
    })
    readonly isBaseLocale: Promise<boolean>;

    @decorators.stringProperty<ReportTranslatableText, 'text'>({
        isStored: true,
        isPublished: true,
        isNotEmpty: false,
        isRequired: false,
        dataType: () => dataTypes.description,
        dependsOn: ['reportTemplate', 'hash', 'isBaseLocale'],
        isFrozen() {
            return this.isBaseLocale;
        },
        lookupAccess: true,
    })
    readonly text: Promise<string>;
}
