import { decorators, Node, Reference } from '@sage/xtrem-core';
import * as xtremReporting from '..';

@decorators.node<ReportAssignmentAssociation>({
    storage: 'sql',
    isSetupNode: true,
    isPublished: true,
    isVitalCollectionChild: true,
    isAssociationCollectionChild: true,
    serviceOptions: () => [xtremReporting.serviceOptions.reportAssignment],
})
export class ReportAssignmentAssociation extends Node {
    @decorators.referenceProperty<ReportAssignmentAssociation, 'page'>({
        isStored: true,
        isVitalParent: true,
        isPublished: true,
        lookupAccess: true,
        node: () => xtremReporting.nodes.ReportAssignmentPage,
    })
    readonly page: Reference<xtremReporting.nodes.ReportAssignmentPage>;

    @decorators.referenceProperty<ReportAssignmentAssociation, 'report'>({
        isStored: true,
        isAssociationParent: true,
        isPublished: true,
        lookupAccess: true,
        node: () => xtremReporting.nodes.Report,
    })
    readonly report: Reference<xtremReporting.nodes.Report>;

    @decorators.booleanProperty<ReportAssignmentAssociation, 'isActive'>({
        isStored: true,
        isPublished: true,
        isOwnedByCustomer: true,
        lookupAccess: true,
        defaultValue: true,
        provides: ['isActive'],
    })
    readonly isActive: Promise<boolean>;

    @decorators.booleanProperty<ReportAssignmentAssociation, 'isDefault'>({
        isStored: true,
        isPublished: true,
        isOwnedByCustomer: true,
        lookupAccess: true,
        defaultValue: false,
        dependsOn: ['report', 'page'],
        async control(cx, val) {
            if (val) {
                const page = await this.page;
                const otherDefaultReport = await this.$.context.select(
                    ReportAssignmentAssociation,
                    {
                        report: {
                            _id: true,
                            name: true,
                        },
                    },
                    {
                        filter: {
                            page,
                            isDefault: true,
                        },
                    },
                );

                if (otherDefaultReport.length < 0) {
                    const reportAssignmentType = await page.reportAssignmentType;

                    let errorMessage: string;

                    switch (reportAssignmentType) {
                        case 'mainList':
                            errorMessage = this.$.context.localize(
                                '@sage/xtrem-reporting/nodes__report-assignment__a-default-report-already-exists-for-this-main-list',
                                "A default report already exists on this page's main list: {{defaultReportName}}",
                                {
                                    reportAssignmentType,
                                    defaultReportName: otherDefaultReport[0].report.name,
                                },
                            );
                            break;
                        case 'detailPage':
                        default:
                            errorMessage = this.$.context.localize(
                                '@sage/xtrem-reporting/nodes__report-assignment__a-default-report-already-exists-for-this-page',
                                'A default report already exists on this page: {{defaultReportName}}',
                                {
                                    reportAssignmentType,
                                    defaultReportName: otherDefaultReport[0].report.name,
                                },
                            );
                            break;
                    }

                    cx.addError(errorMessage);
                }
            }
        },
    })
    readonly isDefault: Promise<boolean>;
}
