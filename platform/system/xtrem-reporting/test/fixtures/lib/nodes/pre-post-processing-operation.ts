import { Context, decorators, Node } from '@sage/xtrem-core';
import * as xtremReporting from '../../../../index';

@decorators.node({
    isPublished: true,
    storage: 'sql',
    serviceOptions: () => [xtremReporting.serviceOptions.reportAssignment],
})
export class PrePostProcessingOperation extends Node {
    @decorators.mutation<typeof PrePostProcessingOperation, 'preProcess'>({
        isPublished: true,
        startsReadOnly: true,
        parameters: [
            {
                name: 'report',
                type: 'reference',
                node: () => xtremReporting.nodes.Report,
                isNullable: true,
            },
        ],
        return: {
            type: 'object',
            properties: {
                watermark: {
                    type: 'string',
                },
            },
        },
    })
    static async preProcess(context: Context, report?: xtremReporting.nodes.Report | null) {
        await Promise.resolve(report);

        await context.batch.logMessage('result', `PreProcessingOperation triggered for report "${report?._id}"`, {
            data: { reportId: report?._id },
        });

        return {
            watermark: 'PreProcessingOperation',
        };
    }

    @decorators.asyncMutation<typeof PrePostProcessingOperation, 'preProcessAsync'>({
        isPublished: true,
        startsReadOnly: true,
        parameters: [
            {
                name: 'report',
                type: 'reference',
                node: () => xtremReporting.nodes.Report,
                isNullable: true,
            },
        ],
        return: {
            type: 'object',
            properties: {
                watermark: {
                    type: 'string',
                },
            },
        },
    })
    static async preProcessAsync(context: Context, report?: xtremReporting.nodes.Report | null) {
        await Promise.resolve(report);

        await context.batch.logMessage('result', `PreProcessingOperation triggered for report "${report?._id}"`, {
            data: { reportId: report?._id },
        });

        return {
            watermark: 'PreProcessingOperation',
        };
    }

    @decorators.mutation<typeof PrePostProcessingOperation, 'postProcess'>({
        isPublished: true,
        startsReadOnly: true,
        parameters: [
            {
                name: 'report',
                type: 'reference',
                node: () => xtremReporting.nodes.Report,
                isNullable: true,
            },
            {
                name: 'error',
                type: 'object',
                properties: {
                    message: 'string',
                },
            },
        ],
        return: 'boolean',
    })
    static async postProcess(
        context: Context,
        report?: xtremReporting.nodes.Report | null,
        error?: { message: string },
    ) {
        await Promise.resolve({ report, error });

        await context.batch.logMessage('result', `PostProcessingOperation triggered for report "${report?._id}"`, {
            data: { reportId: report?._id, error },
        });

        return true;
    }

    @decorators.asyncMutation<typeof PrePostProcessingOperation, 'postProcessAsync'>({
        isPublished: true,
        startsReadOnly: true,
        parameters: [
            {
                name: 'report',
                type: 'reference',
                node: () => xtremReporting.nodes.Report,
                isNullable: true,
            },
            {
                name: 'error',
                type: 'object',
                properties: {
                    message: 'string',
                },
            },
        ],
        return: 'boolean',
    })
    static async postProcessAsync(
        context: Context,
        report?: xtremReporting.nodes.Report | null,
        error?: { message: string },
    ) {
        await Promise.resolve({ report, error });

        await context.batch.logMessage('result', `PostProcessingOperation triggered for report "${report?._id}"`, {
            data: { reportId: report?._id, error },
        });

        return true;
    }
}
