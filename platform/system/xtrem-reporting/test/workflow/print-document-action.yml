envConfigs:
  testActiveServiceOptions:
    - workflow
scenarios:
  Can print a document (without parameters):
    workflow: 'printDocumentActionTestNoParams'
    startEvent:
      topic: 'WorkflowProcess/testStarted'
      payload:
        workflow: 'printDocumentActionTestNoParams'
        parameters: {}
    expectedResult:
      status: 'success'
      variables:
        done: true
        printDocument:
          downloadUrl: '~http://.*/download\?t='
