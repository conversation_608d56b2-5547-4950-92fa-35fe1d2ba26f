mutation {
    xtremReporting {
        reportTemplate {
            create(
                data: {
                    name: "graphQlReportTemplateCreate"
                    report: "#testReport"
                    isFactory: false
                    isExpertDocument: true
                    query: {
                        value: "{xtremSystem{company{query(filter:\"{id:'{{id}}',isActive:{{isActive}} }\"){edges {node {id,isActive,sites{query{edges{node{id,legalCompany{id}}}}}}}}}}}"
                    }
                    externalHtmlTemplate: { value: "<div><h1>This is the title</h1><p>And some text</p></div>" }
                    externalFooterHtmlTemplate: {
                        value: "<div><h1>This is the footer</h1><p>And some footer text</p></div>"
                    }
                    externalHeaderHtmlTemplate: {
                        value: "<div><h1>This is the header</h1><p>And some header text</p></div>"
                    }
                    styleSheet: { value: ".header {color: #0073C2;} .detail-row {color: #E96400;}" }
                }
            ) {
                name
                report {
                    name
                }
                isFactory
                query {
                    value
                }
                externalHtmlTemplate {
                    value
                }
                externalFooterHtmlTemplate {
                    value
                }
                externalHeaderHtmlTemplate {
                    value
                }
                styleSheet {
                    value
                }
                translatableTexts {
                    query(first: 500) {
                        edges {
                            node {
                                locale
                                text
                                hash
                            }
                        }
                    }
                }
            }
        }
    }
}
