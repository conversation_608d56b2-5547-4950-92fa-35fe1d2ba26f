mutation {
    xtremReporting {
        reportTemplate {
            create(
                data: {
                    name: "graphQlReportTemplateCreate"
                    report: "#testReport"
                    isFactory: false
                    isExpertDocument: true
                    query: {
                        value: "{xtremSystem{company{query(filter:\"{id:'{{id}}',isActive:{{isActive}} }\"){edges {node {id,isActive,sites{query{edges{node{id,legalCompany{id}}}}}}}}}}}"
                    }
                    externalHtmlTemplate: {
                        value: "<div><h1 class=\"header\">Company: {{xtremSystem.company.query.edges.[0].node.id}}</h1><table><tr><th class=\"detail-row\">Site ID</th></tr>{{#each xtremSystem.company.query.edges.[0].node.sites.query.edges}}<tr><td>{{node.id}}</td></tr>{{/each}}</table></div>"
                    }
                    styleSheet: { value: ".header {color: #0073C2;} .detail-row {color: #E96400;}" }
                }
            ) {
                name
                report {
                    name
                }
                isFactory
                query {
                    value
                }
                externalHtmlTemplate {
                    value
                }
                styleSheet {
                    value
                }
                translatableTexts {
                    query {
                        edges {
                            node {
                                locale
                                text
                                hash
                            }
                        }
                    }
                }
            }
        }
    }
}
