mutation {
    xtremReporting {
        report {
            create(
                data: {
                    name: "testReportWithVariables"
                    parentPackage: "xtrem-reporting"
                    description: "Test GraphQL Report Create with invalid variable name"
                    variables: { name: "nonReference", type: "string" }
                    printingType: "single"
                }
            ) {
                name
                description
                parentPackage
                activeTemplate {
                    name
                }
                isFactory
            }
        }
    }
}
