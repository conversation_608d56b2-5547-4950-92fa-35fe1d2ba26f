mutation {
    xtremReporting {
        reportTemplate {
            create(
                data: {
                    name: "templateAndWizard"
                    report: "#testReport"
                    reportWizard: {
                        name: "Template and Wizard"
                        id: "templateAndWizard"
                        description: "Test GraphQL Report Wizard"
                        dataSource: "1"
                        selectedProperties: "[{     \"label\": \"_id\",     \"data\": {         \"type\": \"IntOrString\",         \"kind\": \"SCALAR\",         \"isCollection\": false,         \"name\": \"_id\",         \"canFilter\": true,         \"canSort\": true,         \"label\": \"_id\"     },     \"id\": \"_id\",     \"key\": \"_id\",     \"labelKey\": \"_id\",     \"labelPath\": \"_id\" },  {     \"label\": \"Status\",     \"data\": {         \"type\": \"@sage/xtrem-sales/SalesOrderStatus\",         \"kind\": \"ENUM\",         \"enumValues\": [             \"quote\",             \"pending\",             \"inProgress\",             \"closed\"         ],         \"isCollection\": false,         \"name\": \"status\",         \"canFilter\": true,         \"canSort\": true,         \"label\": \"Status\"     },     \"id\": \"status\",     \"key\": \"status\",     \"labelKey\": \"Status\",     \"labelPath\": \"Status\" }]"
                        templateType: "list"
                        parameters: "{\"_id\": \"paperFormat\", \"name\":\"paperFormat\",  \"title\": \"Paper format\",\"type\": \"enum\",\"dataType\":{ \"name\": \"paperFormat\",\"attributes\":{\"optionType\": \"@sage/xtrem-reporting/ReportPaperFormat\"}}}"
                        filters: "{\"filters\": [       {          \"_id\": \"4\",          \"parameter\": true,          \"label\": \"Number\",          \"filterType\": \"equals\",          \"filterValue\": \"number\",          \"data\": {             \"type\": \"String\",             \"kind\": \"SCALAR\",             \"isCollection\": false,             \"name\": \"number\",             \"canFilter\": true,             \"canSort\": true,             \"label\": \"Number\"          },          \"id\": \"number\",          \"labelPath\": \"Number\",          \"property\": {             \"label\": \"Number\",             \"data\": {                \"type\": \"String\",                \"kind\": \"SCALAR\",                \"isCollection\": false,                \"name\": \"number\",                \"canFilter\": true,                \"canSort\": true,                \"label\": \"Number\"             },             \"id\": \"number\",             \"key\": \"number\",             \"labelKey\": \"Number\",             \"labelPath\": \"Number\"          }       }    ],    \"parameters\": [       {          \"name\": \"number\",          \"label\": \"Number\",          \"type\": \"String\"       }    ] }"
                        content: "[     {         \"_id\": \"1\",         \"group\": 0,         \"sorting\": \"ascending\",         \"presentation\": \"Text\",         \"title\": \"First name\",         \"labelPath\": \"First name\",         \"property\": {             \"id\": \"firstName\",             \"label\": \"First name\",             \"labelPath\": \"First name\",             \"data\": {                 \"type\": \"String\",                 \"kind\": \"SCALAR\",                 \"isCollection\": false,                 \"name\": \"firstName\",                 \"canFilter\": true,                 \"canSort\": true,                 \"label\": \"First name\"             }         },         \"path\": \"firstName\"     },     {         \"_id\": \"2\",         \"group\": 0,         \"sorting\": \"ascending\",         \"presentation\": \"Text\",         \"title\": \"Last name\",         \"labelPath\": \"Last name\",         \"property\": {             \"id\": \"lastName\",             \"label\": \"Last name\",             \"labelPath\": \"Last name\",             \"data\": {                 \"type\": \"String\",                 \"kind\": \"SCALAR\",                 \"isCollection\": false,                 \"name\": \"lastName\",                 \"canFilter\": true,                 \"canSort\": true,                 \"label\": \"Last name\"             }         },         \"path\": \"lastName\"     },     {         \"_id\": \"3\",         \"group\": 0,         \"presentation\": \"Text\",         \"title\": \"Email\",         \"labelPath\": \"Email\",         \"property\": {             \"id\": \"email\",             \"label\": \"Email\",             \"labelPath\": \"Email\",             \"data\": {                 \"type\": \"String\",                 \"kind\": \"SCALAR\",                 \"isCollection\": false,                 \"name\": \"email\",                 \"canFilter\": true,                 \"canSort\": true,                 \"label\": \"Email\"             }         },         \"path\": \"email\"     },     {         \"_id\": \"4\",         \"group\": 0,         \"presentation\": \"Text\",         \"title\": \"City\",         \"labelPath\": \"City\",         \"property\": {             \"id\": \"city\",             \"label\": \"City\",             \"labelPath\": \"City\",             \"data\": {                 \"type\": \"String\",                 \"kind\": \"SCALAR\",                 \"isCollection\": false,                 \"name\": \"city\",                 \"canFilter\": true,                 \"canSort\": true,                 \"label\": \"City\"             }         },         \"path\": \"city\"     } ]"
                    }
                    isFactory: false
                    isExpertDocument: true
                    query: {
                        value: "{xtremSystem{company{query(filter:\"{id:'{{id}}',isActive:{{isActive}} }\"){edges {node {id,isActive,sites{query{edges{node{id,legalCompany{id}}}}}}}}}}}"
                    }
                    externalHtmlTemplate: {
                        value: "<div><h1 class=\"header\">Company: {{xtremSystem.company.query.edges.[0].node.id}}</h1><table><tr><th class=\"detail-row\">Site ID</th></tr>{{#each xtremSystem.company.query.edges.[0].node.sites.query.edges}}<tr><td>{{node.id}}</td></tr>{{/each}}</table></div>"
                    }
                    styleSheet: { value: ".header {color: #0073C2;} .detail-row {color: #E96400;}" }
                }
            ) {
                name
                report {
                    name
                }
                isFactory
                query {
                    value
                }
                externalHtmlTemplate {
                    value
                }
                styleSheet {
                    value
                }
                translatableTexts {
                    query {
                        edges {
                            node {
                                locale
                                text
                                hash
                            }
                        }
                    }
                }
            }
        }
    }
}
