mutation {
    xtremReporting {
        report {
            create(
                data: {
                    name: "testReportWithVariables"
                    parentPackage: "xtrem-reporting"
                    description: "Test GraphQL Report Create with invalid variable name"
                    variables: { name: "invalid Variable Name", type: "string" }
                    printingType: "notApplicable"
                }
            ) {
                name
                description
                parentPackage
                activeTemplate {
                    name
                }
                isFactory
            }
        }
    }
}
