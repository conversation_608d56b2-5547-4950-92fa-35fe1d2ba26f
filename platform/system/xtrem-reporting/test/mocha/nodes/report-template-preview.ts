import { Test, TextStream } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as parsePdf from 'pdf-parse';
import * as xtremReporting from '../../../index';

describe('Preview generation', () => {
    it('it should render basic preview', () =>
        Test.withContext(async context => {
            const result = await xtremReporting.nodes.ReportTemplate.generateReportSample(
                context,
                {
                    reportName: 'testReport',
                    code: TextStream.fromString(''),
                    query: TextStream.fromString(''),
                    styleSheet: TextStream.fromString(''),
                    externalHtmlTemplate: TextStream.fromString(
                        '<div><span>Hi!</span><span>How is it going?</span></div>',
                    ),
                    baseLocale: 'en_US',
                    translatableTexts: [
                        {
                            hash: '5360706c803a759e3a9f2ca54a651950',
                            text: 'Hi!',
                            locale: 'en_US',
                        },
                        {
                            hash: '5360706c803a759e3a9f2ca54a651950',
                            text: '¡Hola!',
                            locale: 'es_ES',
                        },
                        {
                            hash: 'bedcc50fb0d3e7c1d2f3d95d093b0798',
                            text: 'How is it going?',
                            locale: 'en_US',
                        },
                    ],
                    attachmentTemplate: TextStream.fromString('<div><span>Attachment template content</span></div>'),
                    attachmentName: 'attachment.xml',
                    attachmentMimeType: 'application/xml',
                },
                {
                    locale: 'es_ES',
                },
            );

            const pdfDocument = await parsePdf(result.value);
            assert.equal(pdfDocument.text.trim(), '¡Hola!How is it going?');
        }));

    it('it should throw with xss in preview', () =>
        Test.withContext(async context => {
            await assert.isRejected(
                xtremReporting.nodes.ReportTemplate.generateReportSample(
                    context,
                    {
                        reportName: 'testReport',
                        code: TextStream.fromString(''),
                        query: TextStream.fromString(''),
                        styleSheet: TextStream.fromString(''),
                        externalHtmlTemplate: TextStream.fromString(
                            `<iframe src= "file:///etc/hostname" width="500px" height="800px" />
                        <iframe src= "file:///etc/os-release" width="500px" height="800px" />
                        <iframe src= "file:///xtrem/app/package.json" width="500px" height="800px" />
                        <iframe src= "file:///xtrem/app/pnpm-lock.yaml" width="500px" height="3800px" />
                        <iframe src= "file:///xtrem/app/logs/" width="500px" height="800px" />
                        <div><span>Hacked</span></div>`,
                        ),
                        baseLocale: 'en_US',
                        translatableTexts: [],
                        attachmentTemplate: TextStream.fromString(
                            '<div><span>Attachment template content</span></div>',
                        ),
                        attachmentName: 'attachment.xml',
                        attachmentMimeType: 'application/xml',
                    },
                    {
                        locale: 'es_ES',
                    },
                ),
                'html content violate xss validation rules',
            );
        }));

    it('it should use the base locale if no local is provided', () =>
        Test.withContext(async context => {
            const result = await xtremReporting.nodes.ReportTemplate.generateReportSample(
                context,
                {
                    reportName: 'testReport',
                    code: TextStream.fromString(''),
                    query: TextStream.fromString(''),
                    styleSheet: TextStream.fromString(''),
                    externalHtmlTemplate: TextStream.fromString(
                        '<div><span>Hi!</span><span>How is it going?</span></div>',
                    ),
                    baseLocale: 'en_US',
                    translatableTexts: [
                        {
                            hash: '5360706c803a759e3a9f2ca54a651950',
                            text: 'Hi!',
                            locale: 'en_US',
                        },
                        {
                            hash: '5360706c803a759e3a9f2ca54a651950',
                            text: '¡Hola!',
                            locale: 'es_ES',
                        },
                        {
                            hash: 'bedcc50fb0d3e7c1d2f3d95d093b0798',
                            text: 'How is it going?',
                            locale: 'en_US',
                        },
                    ],
                    attachmentTemplate: TextStream.fromString('<div><span>Attachment template content</span></div>'),
                    attachmentName: 'attachment.xml',
                    attachmentMimeType: 'application/xml',
                },
                {},
            );
            const pdfDocument = await parsePdf(result.value);
            assert.equal(pdfDocument.text.trim(), 'Hi!How is it going?');
        }));

    it('it should generate PDF preview with full page number message in the footer', () =>
        Test.withContext(async context => {
            const result = await xtremReporting.nodes.ReportTemplate.generateReportSample(
                context,
                {
                    reportName: 'testReport',
                    code: TextStream.fromString(''),
                    query: TextStream.fromString(''),
                    styleSheet: TextStream.fromString(''),
                    externalHtmlTemplate: TextStream.fromString(
                        '<div><span>Hi!</span><span>How is it going?</span></div>',
                    ),
                    externalFooterHtmlTemplate: TextStream.fromString('{{pageNumberFullMessage}}'),
                    baseLocale: 'en_US',
                    translatableTexts: [],
                    attachmentTemplate: TextStream.fromString('<div><span>Attachment template content</span></div>'),
                    attachmentName: 'attachment.xml',
                    attachmentMimeType: 'application/xml',
                },
                {},
            );
            const pdfDocument = await parsePdf(result.value);
            assert.equal(pdfDocument.text.trim(), 'Hi!How is it going?\nPage 1 of 1');
        }));

    it('it should generate PDF preview with full page number message in the footer in French', () =>
        Test.withContext(async context => {
            const result = await xtremReporting.nodes.ReportTemplate.generateReportSample(
                context,
                {
                    reportName: 'testReport',
                    code: TextStream.fromString(''),
                    query: TextStream.fromString(''),
                    styleSheet: TextStream.fromString(''),
                    externalHtmlTemplate: TextStream.fromString(
                        '<div><span>Hi!</span><span>How is it going?</span></div>',
                    ),
                    externalFooterHtmlTemplate: TextStream.fromString('{{pageNumberFullMessage}}'),
                    baseLocale: 'en_US',
                    translatableTexts: [],
                    attachmentTemplate: TextStream.fromString('<div><span>Attachment template content</span></div>'),
                    attachmentName: 'attachment.xml',
                    attachmentMimeType: 'application/xml',
                },
                {
                    locale: 'fr_FR',
                },
            );
            const pdfDocument = await parsePdf(result.value);
            assert.equal(pdfDocument.text.trim(), 'Hi!How is it going?\nPage 1 de 1');
        }));

    it('it should generate PDF preview with total page number and current page number', () =>
        Test.withContext(async context => {
            const result = await xtremReporting.nodes.ReportTemplate.generateReportSample(
                context,
                {
                    reportName: 'testReport',
                    code: TextStream.fromString(''),
                    query: TextStream.fromString(''),
                    styleSheet: TextStream.fromString(''),
                    externalHtmlTemplate: TextStream.fromString(
                        '<div><div style="page-break-after: always;">Hi!</div><div style="page-break-after: always;">How is it going?</div><div>Good</div></div>',
                    ),
                    externalFooterHtmlTemplate: TextStream.fromString('{{pageNumberCurrent}}-{{pageNumberTotal}}'),
                    baseLocale: 'en_US',
                    translatableTexts: [],
                    attachmentTemplate: TextStream.fromString('<div><span>Attachment template content</span></div>'),
                    attachmentName: 'attachment.xml',
                    attachmentMimeType: 'application/xml',
                },
                {
                    locale: 'fr_FR',
                },
            );
            const pdfDocument = await parsePdf(result.value);
            assert.equal(pdfDocument.text.trim(), 'Hi!\n1-3\n\nHow is it going?\n2-3\n\nGood\n3-3');
        }));

    it('should throw an error when the pageNumberFullMessage is accessed from the page body', () =>
        Test.withContext(async context => {
            await assert.isRejected(
                xtremReporting.nodes.ReportTemplate.generateReportSample(
                    context,
                    {
                        reportName: 'testReport',
                        code: TextStream.fromString(''),
                        query: TextStream.fromString(''),
                        styleSheet: TextStream.fromString(''),
                        externalHtmlTemplate: TextStream.fromString(
                            '<div><span>Hi!</span><span>How is it going?{{pageNumberFullMessage}}</span></div>',
                        ),
                        baseLocale: 'en_US',
                        translatableTexts: [],
                        attachmentTemplate: TextStream.fromString(
                            '<div><span>Attachment template content</span></div>',
                        ),
                        attachmentName: 'attachment.xml',
                        attachmentMimeType: 'application/xml',
                    },
                    {
                        locale: 'fr_FR',
                    },
                ),
                '#pageNumberFullMessage can only be used in the header or the footer of the document.',
            );
        }));

    it('should throw an error when the pageNumberTotal is accessed from the page body', () =>
        Test.withContext(async context => {
            await assert.isRejected(
                xtremReporting.nodes.ReportTemplate.generateReportSample(
                    context,
                    {
                        reportName: 'testReport',
                        code: TextStream.fromString(''),
                        query: TextStream.fromString(''),
                        styleSheet: TextStream.fromString(''),
                        externalHtmlTemplate: TextStream.fromString(
                            '<div><span>Hi!</span><span>How is it going?{{pageNumberTotal}}</span></div>',
                        ),
                        baseLocale: 'en_US',
                        translatableTexts: [],
                        attachmentTemplate: TextStream.fromString(
                            '<div><span>Attachment template content</span></div>',
                        ),
                        attachmentName: 'attachment.xml',
                        attachmentMimeType: 'application/xml',
                    },
                    {
                        locale: 'fr_FR',
                    },
                ),
                '#pageNumberTotal can only be used in the header or the footer of the document.',
            );
        }));

    it('should throw an error when the pageNumberCurrent is accessed from the page body', () =>
        Test.withContext(async context => {
            await assert.isRejected(
                xtremReporting.nodes.ReportTemplate.generateReportSample(
                    context,
                    {
                        reportName: 'testReport',
                        code: TextStream.fromString(''),
                        query: TextStream.fromString(''),
                        styleSheet: TextStream.fromString(''),
                        externalHtmlTemplate: TextStream.fromString(
                            '<div><span>Hi!</span><span>How is it going?{{pageNumberCurrent}}</span></div>',
                        ),
                        baseLocale: 'en_US',
                        translatableTexts: [],
                        attachmentTemplate: TextStream.fromString(
                            '<div><span>Attachment template content</span></div>',
                        ),
                        attachmentName: 'attachment.xml',
                        attachmentMimeType: 'application/xml',
                    },
                    {
                        locale: 'fr_FR',
                    },
                ),
                '#pageNumberCurrent can only be used in the header or the footer of the document.',
            );
        }));
});
