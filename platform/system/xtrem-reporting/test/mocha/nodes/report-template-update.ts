import { Test, TextStream } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremReporting from '../../../lib/index';

describe('Modify a Report Template node', () => {
    it('Modification of name fails for factory templates', () =>
        Test.withContext(async context => {
            const userTemplate = await context.tryRead(
                xtremReporting.nodes.ReportTemplate,
                { name: 'userTemplate' },
                { forUpdate: true },
            );

            await assert.isRejected(
                userTemplate!.$.set({ name: 'userTemplateRenamed' }),
                'ReportTemplate.name: cannot set value on frozen property',
            );
        }));

    it('Modification of name does not fail for report template that is not a factory record', () =>
        Test.withContext(async context => {
            const userTemplate = await context.read(
                xtremReporting.nodes.ReportTemplate,
                { name: 'testTemplate' },
                { forUpdate: true },
            );

            await userTemplate.$.set({ name: 'testTemplateRenamed' });

            assert.equal(userTemplate?.$.getRawPropertyValue('name'), 'testTemplateRenamed');

            await userTemplate!.$.save();
        }));

    xit('should remove strings that are not used anymore', () =>
        Test.withContext(async context => {
            const newTemplate = await context.create(xtremReporting.nodes.ReportTemplate, {
                name: 'goodTemplate',
                baseLocale: 'en_US',
                report: '#TEST_NO_ACTIVE_TEMPLATE',
                externalHtmlTemplate: TextStream.fromString('<div><h1>Test Report</h1><span>Hi there!</span></div>'),
            });
            await newTemplate.$.save();
            let strings = await newTemplate.translatableTexts.toArray();

            assert.equal(strings.length, 22);
            assert.equal(await strings[0].hash, '09365f79b744cd3d5116da0bfce802a5');
            assert.equal(await strings[0].locale, 'en_US');
            assert.equal(await strings[0].text, 'Test Report');

            assert.equal(await strings[1].hash, '09365f79b744cd3d5116da0bfce802a5');
            assert.equal(await strings[1].locale, 'ar_SA');
            assert.equal(await strings[1].text, '');

            assert.equal(await strings[2].hash, '09365f79b744cd3d5116da0bfce802a5');
            assert.equal(await strings[2].locale, 'de_DE');
            assert.equal(await strings[2].text, '');

            assert.equal(await strings[11].hash, '396199333edbf40ad43e62a1c1397793');
            assert.equal(await strings[11].locale, 'en_US');
            assert.equal(await strings[11].text, 'Hi there!');

            assert.equal(await strings[12].hash, '396199333edbf40ad43e62a1c1397793');
            assert.equal(await strings[12].locale, 'ar_SA');
            assert.equal(await strings[12].text, '');

            assert.equal(await strings[13].hash, '396199333edbf40ad43e62a1c1397793');
            assert.equal(await strings[13].locale, 'de_DE');
            assert.equal(await strings[13].text, '');

            await newTemplate.$.set({
                externalHtmlTemplate: TextStream.fromString('<div><h1>Test Report</h1><span>Hey there!</span></div>'),
            });
            await newTemplate.$.save();
            strings = await newTemplate.translatableTexts.toArray();
            assert.equal(strings.length, 22);

            assert.equal(await strings[11].locale, 'en_US');
            assert.equal(await strings[11].text, 'Hey there!');
        }));
});
