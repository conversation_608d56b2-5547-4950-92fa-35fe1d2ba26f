import { Test, TextStream } from '@sage/xtrem-core';
import * as xtremMetaData from '@sage/xtrem-metadata';
import { assert } from 'chai';
import * as xtremReporting from '../../../lib/index';

describe('Should update a report and a report template', () => {
    it('Update succeeds', () =>
        Test.withContext(async context => {
            const externalHtmlTemplate = new TextStream(
                '<table class="query-table" data-context-object-type="account" data-context-object-path="xtremFinanceData.account.query.edges" data-context-filter="[]" data-context-list-order="{}" data-alias="WHlTdRvQ"></table>',
            );
            const nodeMetaDataArray = await context.read(xtremMetaData.nodes.MetaNodeFactory, { _id: '20' });
            const reportSettings: xtremReporting.functions.IncomingReportGenerationSettings = {
                locale: 'en_US',
                paperFormat: 'a4',
                pageOrientation: 'portrait',
                leftMarginCm: 2,
                rightMarginCm: 2,
                topMarginCm: 2,
                bottomMarginCm: 2,
            };
            const reportIds = await xtremReporting.nodes.Report.createOrUpdateReport(context, {
                name: 'Test update report',
                description: 'salt table',
                parentPackage: 'xtrem-finance-data',
                externalHtmlTemplate,
                dataSource: nodeMetaDataArray,
                selectedProperties:
                    '{"name":{"label":"Name","data":{"type":"String","kind":"SCALAR","isCollection":false,"name":"name","canFilter":true,"canSort":true,"label":"Name","isOnInputType":true,"isOnOutputType":true,"dataType":"name","referenceNode":"","enumType":null,"isCustom":false},"id":"name","key":"name","labelKey":"Name","labelPath":"Name"},"id":{"label":"ID","data":{"type":"String","kind":"SCALAR","isCollection":false,"name":"id","canFilter":true,"canSort":true,"label":"ID","isOnInputType":true,"isOnOutputType":true,"dataType":"id","referenceNode":"","enumType":null,"isCustom":false},"id":"id","key":"id","labelKey":"ID","labelPath":"ID"},"recordNo":{"label":"Record number","data":{"type":"Int","kind":"SCALAR","isCollection":false,"name":"recordNo","canFilter":true,"canSort":true,"label":"Record number","isOnInputType":true,"isOnOutputType":true,"dataType":"","referenceNode":"","enumType":null,"isCustom":false},"id":"recordNo","key":"recordNo","labelKey":"Record number","labelPath":"Record number"}}',
                parameters: '{}',
                filters: '{"filters":[],"parameters":[]}',
                content:
                    '[{"sorting":"ascending","operation":"NONE","group":0,"_id":"1","presentation":"Text","title":"Name","labelPath":"Name","formatting":"","divisor":"","property":{"label":"Name","data":{"type":"String","kind":"SCALAR","isCollection":false,"name":"name","canFilter":true,"canSort":true,"label":"Name","isOnInputType":true,"isOnOutputType":true,"dataType":"name","referenceNode":"","enumType":null,"isCustom":false},"id":"name","key":"name","labelKey":"Name","labelPath":"Name"},"path":"name"},{"sorting":"ascending","operation":"NONE","group":0,"_id":"2","presentation":"Text","title":"ID","labelPath":"ID","formatting":"","divisor":"","property":{"label":"ID","data":{"type":"String","kind":"SCALAR","isCollection":false,"name":"id","canFilter":true,"canSort":true,"label":"ID","isOnInputType":true,"isOnOutputType":true,"dataType":"id","referenceNode":"","enumType":null,"isCustom":false},"id":"id","key":"id","labelKey":"ID","labelPath":"ID"},"path":"id"},{"sorting":"ascending","operation":"NONE","group":0,"_id":"3","presentation":"Text","title":"Record number","labelPath":"Record number","formatting":"","divisor":"","property":{"label":"Record number","data":{"type":"Int","kind":"SCALAR","isCollection":false,"name":"recordNo","canFilter":true,"canSort":true,"label":"Record number","isOnInputType":true,"isOnOutputType":true,"dataType":"","referenceNode":"","enumType":null,"isCustom":false},"id":"recordNo","key":"recordNo","labelKey":"Record number","labelPath":"Record number"},"path":"recordNo"}]',
                variables: '[]',
                reportSettings,
                printingType: 'notApplicable',
            });

            const nodeBeforeUpdate = await context.tryRead(xtremReporting.nodes.Report, { _id: reportIds.reportId });

            const externalHtmlTemplateUpdate = new TextStream(
                '<table class="query-table" data-context-object-type="account" data-context-object-path="xtremFinanceData.account.query.edges" data-context-filter="[]" data-context-list-order="{}" data-alias="WHlTdRvQ"><thead class="query-table-head"><tr class="query-table-row"><td class="query-table-cell"><p>Name</p></td><td class="query-table-cell"><p>ID</p></td><td class="query-table-cell"><p>Record number</p></td></tr></thead></table>',
            );
            await xtremReporting.nodes.Report.createOrUpdateReport(context, {
                _id: reportIds.reportId.toString(),
                templateId: (await nodeBeforeUpdate?.activeTemplate)?._id.toString(),
                name: await nodeBeforeUpdate?.name,
                templateName: await (await nodeBeforeUpdate?.activeTemplate)?.name,
                description: 'table salt',
                parentPackage: 'xtrem-finance-data',
                externalHtmlTemplate: externalHtmlTemplateUpdate,
                dataSource: nodeMetaDataArray,
                selectedProperties:
                    '{"name":{"label":"Name","data":{"type":"String","kind":"SCALAR","isCollection":false,"name":"name","canFilter":true,"canSort":true,"label":"Name","isOnInputType":true,"isOnOutputType":true,"dataType":"name","referenceNode":"","enumType":null,"isCustom":false},"id":"name","key":"name","labelKey":"Name","labelPath":"Name"},"id":{"label":"ID","data":{"type":"String","kind":"SCALAR","isCollection":false,"name":"id","canFilter":true,"canSort":true,"label":"ID","isOnInputType":true,"isOnOutputType":true,"dataType":"id","referenceNode":"","enumType":null,"isCustom":false},"id":"id","key":"id","labelKey":"ID","labelPath":"ID"},"recordNo":{"label":"Record number","data":{"type":"Int","kind":"SCALAR","isCollection":false,"name":"recordNo","canFilter":true,"canSort":true,"label":"Record number","isOnInputType":true,"isOnOutputType":true,"dataType":"","referenceNode":"","enumType":null,"isCustom":false},"id":"recordNo","key":"recordNo","labelKey":"Record number","labelPath":"Record number"}}',
                parameters: '{}',
                filters: '{"filters":[],"parameters":[]}',
                content:
                    '[{"sorting":"ascending","operation":"NONE","group":0,"_id":"1","presentation":"Text","title":"Name","labelPath":"Name","formatting":"","divisor":"","property":{"label":"Name","data":{"type":"String","kind":"SCALAR","isCollection":false,"name":"name","canFilter":true,"canSort":true,"label":"Name","isOnInputType":true,"isOnOutputType":true,"dataType":"name","referenceNode":"","enumType":null,"isCustom":false},"id":"name","key":"name","labelKey":"Name","labelPath":"Name"},"path":"name"},{"sorting":"ascending","operation":"NONE","group":0,"_id":"2","presentation":"Text","title":"ID","labelPath":"ID","formatting":"","divisor":"","property":{"label":"ID","data":{"type":"String","kind":"SCALAR","isCollection":false,"name":"id","canFilter":true,"canSort":true,"label":"ID","isOnInputType":true,"isOnOutputType":true,"dataType":"id","referenceNode":"","enumType":null,"isCustom":false},"id":"id","key":"id","labelKey":"ID","labelPath":"ID"},"path":"id"},{"sorting":"ascending","operation":"NONE","group":0,"_id":"3","presentation":"Text","title":"Record number","labelPath":"Record number","formatting":"","divisor":"","property":{"label":"Record number","data":{"type":"Int","kind":"SCALAR","isCollection":false,"name":"recordNo","canFilter":true,"canSort":true,"label":"Record number","isOnInputType":true,"isOnOutputType":true,"dataType":"","referenceNode":"","enumType":null,"isCustom":false},"id":"recordNo","key":"recordNo","labelKey":"Record number","labelPath":"Record number"},"path":"recordNo"}]',
                variables: '[]',
                reportSettings,
            });

            const nodeAfterUpdate = await context.tryRead(xtremReporting.nodes.Report, { _id: reportIds.reportId });

            assert.notDeepEqual(nodeBeforeUpdate?.description, nodeAfterUpdate?.description);
            assert.notDeepEqual(
                (await nodeBeforeUpdate?.activeTemplate)?.externalHtmlTemplate,
                (await nodeAfterUpdate?.activeTemplate)?.externalHtmlTemplate,
            );
            assert.notDeepEqual(
                (await (await nodeBeforeUpdate?.activeTemplate)?.reportWizard)?.description,
                (await (await nodeAfterUpdate?.activeTemplate)?.reportWizard)?.description,
            );
        }));
});
