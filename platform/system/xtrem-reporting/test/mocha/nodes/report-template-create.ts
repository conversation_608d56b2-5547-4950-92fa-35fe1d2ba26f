import { Test, TextStream } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremReporting from '../../../lib/index';

describe('Create a Report Template node', () => {
    it('Create succeeds', () =>
        Test.withContext(async context => {
            const newTemplate = await context.create(xtremReporting.nodes.ReportTemplate, {
                name: 'goodTemplate',
                report: '#noActiveTemplate',
                isExpertDocument: true,
                externalHtmlTemplate: TextStream.fromString(''),
            });
            assert.deepEqual(newTemplate.$.context.diagnoses, []);
            await newTemplate.$.save();
            assert.deepEqual(newTemplate.$.context.diagnoses, []);
        }));

    it('Create fails on duplicate name', () =>
        Test.withContext(async context => {
            const templateData = {
                name: 'myTemplate',
                report: '#noActiveTemplate',
                isExpertDocument: true,
            };

            // Create report
            const newTemplate1 = await context.create(xtremReporting.nodes.ReportTemplate, templateData);
            assert.deepEqual(newTemplate1.$.context.diagnoses, []);
            await newTemplate1.$.save();
            assert.deepEqual(newTemplate1.$.context.diagnoses, []);

            // Create second report with same name - it should throw an error
            const newTemplate2 = await context.create(xtremReporting.nodes.ReportTemplate, templateData);
            assert.deepEqual(newTemplate2.$.context.diagnoses, []);
            await assert.isRejected(newTemplate2.$.save());
        }));

    it('should extract translation keys from html input', () =>
        Test.withContext(async context => {
            const newTemplate = await context.create(xtremReporting.nodes.ReportTemplate, {
                name: 'goodTemplate',
                baseLocale: 'en_US',
                report: '#noActiveTemplate',
                isExpertDocument: true,
                externalHtmlTemplate: TextStream.fromString('<div><h1>Test Report</h1><span>Hi there!</span></div>'),
            });
            await newTemplate.$.save();
            const strings = await newTemplate.translatableTexts.toArray();

            assert.equal(strings.length, 22);
            assert.equal(await strings[0].hash, '09365f79b744cd3d5116da0bfce802a5');
            assert.equal(await strings[0].locale, 'en_US');
            assert.equal(await strings[0].text, 'Test Report');

            assert.equal(await strings[1].hash, '09365f79b744cd3d5116da0bfce802a5');
            assert.equal(await strings[1].locale, 'ar_SA');
            assert.equal(await strings[1].text, '');

            assert.equal(await strings[2].hash, '09365f79b744cd3d5116da0bfce802a5');
            assert.equal(await strings[2].locale, 'de_DE');
            assert.equal(await strings[2].text, '');

            assert.equal(await strings[3].hash, '09365f79b744cd3d5116da0bfce802a5');
            assert.equal(await strings[3].locale, 'en_GB');
            assert.equal(await strings[3].text, '');

            assert.equal(await strings[4].hash, '09365f79b744cd3d5116da0bfce802a5');
            assert.equal(await strings[4].locale, 'es_ES');
            assert.equal(await strings[4].text, '');

            assert.equal(await strings[5].hash, '09365f79b744cd3d5116da0bfce802a5');
            assert.equal(await strings[5].locale, 'fr_FR');
            assert.equal(await strings[5].text, '');

            assert.equal(await strings[6].hash, '09365f79b744cd3d5116da0bfce802a5');
            assert.equal(await strings[6].locale, 'it_IT');
            assert.equal(await strings[6].text, '');

            assert.equal(await strings[7].hash, '09365f79b744cd3d5116da0bfce802a5');
            assert.equal(await strings[7].locale, 'pl_PL');
            assert.equal(await strings[7].text, '');

            assert.equal(await strings[8].hash, '09365f79b744cd3d5116da0bfce802a5');
            assert.equal(await strings[8].locale, 'pt_BR');
            assert.equal(await strings[8].text, '');

            assert.equal(await strings[9].hash, '09365f79b744cd3d5116da0bfce802a5');
            assert.equal(await strings[9].locale, 'pt_PT');
            assert.equal(await strings[9].text, '');

            assert.equal(await strings[10].hash, '09365f79b744cd3d5116da0bfce802a5');
            assert.equal(await strings[10].locale, 'zh_CN');
            assert.equal(await strings[10].text, '');

            assert.equal(await strings[11].hash, '396199333edbf40ad43e62a1c1397793');
            assert.equal(await strings[11].locale, 'en_US');
            assert.equal(await strings[11].text, 'Hi there!');

            assert.equal(await strings[12].hash, '396199333edbf40ad43e62a1c1397793');
            assert.equal(await strings[12].locale, 'ar_SA');
            assert.equal(await strings[12].text, '');

            assert.equal(await strings[13].hash, '396199333edbf40ad43e62a1c1397793');
            assert.equal(await strings[13].locale, 'de_DE');
            assert.equal(await strings[13].text, '');

            assert.equal(await strings[14].hash, '396199333edbf40ad43e62a1c1397793');
            assert.equal(await strings[14].locale, 'en_GB');
            assert.equal(await strings[14].text, '');

            assert.equal(await strings[15].hash, '396199333edbf40ad43e62a1c1397793');
            assert.equal(await strings[15].locale, 'es_ES');
            assert.equal(await strings[15].text, '');

            assert.equal(await strings[16].hash, '396199333edbf40ad43e62a1c1397793');
            assert.equal(await strings[16].locale, 'fr_FR');
            assert.equal(await strings[16].text, '');

            assert.equal(await strings[17].hash, '396199333edbf40ad43e62a1c1397793');
            assert.equal(await strings[17].locale, 'it_IT');
            assert.equal(await strings[17].text, '');

            assert.equal(await strings[18].hash, '396199333edbf40ad43e62a1c1397793');
            assert.equal(await strings[18].locale, 'pl_PL');
            assert.equal(await strings[18].text, '');

            assert.equal(await strings[19].hash, '396199333edbf40ad43e62a1c1397793');
            assert.equal(await strings[19].locale, 'pt_BR');
            assert.equal(await strings[19].text, '');

            assert.equal(await strings[20].hash, '396199333edbf40ad43e62a1c1397793');
            assert.equal(await strings[20].locale, 'pt_PT');
            assert.equal(await strings[20].text, '');

            assert.equal(await strings[21].hash, '396199333edbf40ad43e62a1c1397793');
            assert.equal(await strings[21].locale, 'zh_CN');
            assert.equal(await strings[21].text, '');
        }));
});
