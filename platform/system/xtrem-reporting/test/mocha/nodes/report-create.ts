import { NodeCreateData, Test, ValidationSeverity } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremReporting from '../../../lib/index';

describe('Create a Report node', () => {
    it('<PERSON>reate succeeds', () =>
        Test.withContext(async context => {
            // Create report
            const newReport = await context.create(xtremReporting.nodes.Report, {
                name: 'goodReport',
                parentPackage: 'xtrem-reporting',
                description: 'Good Report',
                printingType: 'single',
                variables: [
                    {
                        name: 'myVariable',
                        type: 'reference',
                        isMandatory: true,
                        dataType: '#user',
                    },
                ],
            });
            assert.deepEqual(newReport.$.context.diagnoses, []);
            await newReport.$.save();
            assert.deepEqual(newReport.$.context.diagnoses, []);
        }));

    it('Create fails on duplicate name', () =>
        Test.withContext(async context => {
            const reportData: NodeCreateData<xtremReporting.nodes.Report> = {
                name: 'myReport',
                parentPackage: 'xtrem-reporting',
                description: 'My Test Report',
                printingType: 'multiple',
            };

            // Create first report
            const newReport1 = await context.create(xtremReporting.nodes.Report, reportData);
            assert.deepEqual(newReport1.$.context.diagnoses, []);
            await newReport1.$.save();
            assert.deepEqual(newReport1.$.context.diagnoses, []);

            // Create second report with same name - it should throw an error
            const newReport2 = await context.create(xtremReporting.nodes.Report, reportData);
            assert.deepEqual(newReport2.$.context.diagnoses, []);
            await assert.isRejected(newReport2.$.save());
        }));

    it('Create fails on single print with email report type', () =>
        Test.withContext(
            async context => {
                const reportData: NodeCreateData<xtremReporting.nodes.Report> = {
                    name: 'myReport',
                    parentPackage: 'xtrem-reporting',
                    description: 'My Test Report',
                    reportType: 'email',
                    printingType: 'single',
                };

                // Create first report
                const newReport = await context.create(xtremReporting.nodes.Report, reportData);
                assert.deepEqual(newReport.$.context.diagnoses, []);
                await assert.isRejected(newReport.$.save(), 'The record was not created.');
                assert.deepEqual(newReport.$.context.diagnoses, [
                    {
                        message: "Only 'Printed document' reports can be single or multiple prints.",
                        path: ['printingType'],
                        severity: ValidationSeverity.error,
                    },
                ]);
            },
            { testActiveServiceOptions: [xtremReporting.serviceOptions.reportAssignment] },
        ));

    it('Create fails on single print without variables (missing mandatory variable)', () =>
        Test.withContext(
            async context => {
                const reportData: NodeCreateData<xtremReporting.nodes.Report> = {
                    name: 'myReport',
                    parentPackage: 'xtrem-reporting',
                    description: 'My Test Report',
                    printingType: 'single',
                };

                // Create first report
                const newReport = await context.create(xtremReporting.nodes.Report, reportData);
                assert.deepEqual(newReport.$.context.diagnoses, []);
                await assert.isRejected(newReport.$.save(), 'The record was not created.');
                assert.deepEqual(newReport.$.context.diagnoses, [
                    {
                        message: 'A mandatory reference variable is required for single print reports.',
                        path: [],
                        severity: ValidationSeverity.error,
                    },
                ]);
            },
            { testActiveServiceOptions: [xtremReporting.serviceOptions.reportAssignment] },
        ));
});
