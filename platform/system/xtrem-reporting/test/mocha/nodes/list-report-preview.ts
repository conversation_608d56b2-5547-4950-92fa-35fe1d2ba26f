import { Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as parsePdf from 'pdf-parse';
import * as xtremReporting from '../../../index';

describe('Wizard preview generation', () => {
    it('it should render basic preview', () =>
        Test.withContext(async context => {
            const result = await xtremReporting.nodes.ReportTemplate.generateWizardPreview(
                context,
                '<table class="query-table" data-context-object-type="reportTemplate" data-context-object-path="xtremReporting.reportTemplate.query.edges" data-context-filter="[]" data-context-list-order="{}" data-alias="WHlTdRvQ"><thead class="query-table-head"><tr class="query-table-row"><td class="query-table-cell" style="background-color:#0000001a;border:1px solid #000000;padding:2px;"><p>Name</p></td><td class="query-table-cell" style="background-color:#0000001a;border:1px solid #000000;padding:2px;"><p>Attachment Name</p></td><td class="query-table-cell" style="background-color:#0000001a;border:1px solid #000000;padding:2px;"><p>Default left margin</p></td></tr></thead><tbody class="query-table-body"><!--{{#each WHlTdRvQ.reportTemplate.query.edges}}{{#with node}}--><tr class="query-table-row"><td class="query-table-cell" style="border:1px solid #000000;padding:2px;"><p><span class="property" data-property-display-label="Name" data-property-data-type="String" data-property-name="name" data-property-data-format="" data-property-parent-context="reportTemplate">{{name}}</span></p></td><td class="query-table-cell" style="border:1px solid #000000;padding:2px;"><p><span class="property" data-property-display-label="Attachment Name" data-property-data-type="String" data-property-name="attachmentName" data-property-data-format="" data-property-parent-context="reportTemplate">{{attachmentName}}</span></p></td><td class="query-table-cell" style="border:1px solid #000000;padding:2px;"><p><span class="property" data-property-display-label="Default left margin" data-property-data-type="Int" data-property-name="defaultLeftMargin" data-property-data-format="" data-property-parent-context="reportTemplate">{{defaultLeftMargin}}</span></p></td></tr><!--{{#printBreakIfPropertyWillChange \'undefined\'}}--><tr class="query-table-row" data-footer-group="undefined"><td class="query-table-cell" style="border:1px solid #000000;padding:2px;"><p>&nbsp;</p></td><td class="query-table-cell" style="border:1px solid #000000;padding:2px;"><p>&nbsp;</p></td><td class="query-table-cell" style="border:1px solid #000000;padding:2px;"><p>&nbsp;</p></td></tr><!--{{/printBreakIfPropertyWillChange}}--><tr class="query-table-row" data-hidden="1"><td class="query-table-cell" colspan="3">&nbsp;</td></tr><tr class="query-table-row" data-hidden="1"><td class="query-table-cell" colspan="3"><p>&nbsp;</p></td></tr><!--{{/with}}{{/each}}--><tr class="query-table-row" data-hidden="1"><td class="query-table-cell" colspan="3">&nbsp;</td></tr></tbody><tfoot class="query-table-footer"><tr class="query-table-row"><td class="query-table-cell"><p>&nbsp;</p></td><td class="query-table-cell"><p>&nbsp;</p></td><td class="query-table-cell"><p>&nbsp;</p></td></tr></tfoot></table>',
                {
                    locale: 'en_US',
                    paperFormat: 'a4',
                    variables: JSON.stringify({}),
                },
            );

            const pdfDocument = await parsePdf(result.value);
            assert.deepEqual(pdfDocument.text.trim().split('\n').slice(1), [
                'activeTemplate2',
                'activeUsers2',
                'companyTemplate2',
                'conditionalBlockNoVariableReportTemplate2.54',
                'conditionalBlockTemplate2.54',
                'emptyTemplate2.54',
                'errorTemplate2',
                'filledPageTemplate2',
                'monacoPlugInTemplate2',
                'onboarding_tenant2',
                'onboarding_user2',
                'pre-processing-test-report-template2',
                'pre-processing-test-report-template-async2',
                'reportDefinitionTemplate2',
                'testTemplate2',
                'usersByType2',
                'usersByTypeAggregate2',
                'userTemplateuser.xml2',
            ]);
        }));

    it('Create preview with default header', () =>
        Test.withContext(
            async context => {
                const result = await xtremReporting.nodes.ReportTemplate.generateWizardPreview(
                    context,
                    '<table class="query-table" data-context-object-type="reportTemplate" data-context-object-path="xtremReporting.reportTemplate.query.edges" data-context-filter="[]" data-context-list-order="{}" data-alias="WHlTdRvQ"><thead class="query-table-head"><tr class="query-table-row"><td class="query-table-cell" style="background-color:#0000001a;border:1px solid #000000;padding:2px;"><p>Name</p></td><td class="query-table-cell" style="background-color:#0000001a;border:1px solid #000000;padding:2px;"><p>AttachmentName</p></td><td class="query-table-cell" style="background-color:#0000001a;border:1px solid #000000;padding:2px;"><p>Default left margin</p></td></tr></thead><tbody class="query-table-body"><!--{{#each WHlTdRvQ.reportTemplate.query.edges}}{{#with node}}--><tr class="query-table-row"><td class="query-table-cell" style="border:1px solid #000000;padding:2px;"><p><span class="property" data-property-display-label="Name" data-property-data-type="String" data-property-name="name" data-property-data-format="" data-property-parent-context="reportTemplate">{{name}}</span></p></td><td class="query-table-cell" style="border:1px solid #000000;padding:2px;"><p><span class="property" data-property-display-label="AttachmentName" data-property-data-type="String" data-property-name="attachmentName" data-property-data-format="" data-property-parent-context="reportTemplate">{{attachmentName}}</span></p></td><td class="query-table-cell" style="border:1px solid #000000;padding:2px;"><p><span class="property" data-property-display-label="Default left margin" data-property-data-type="Int" data-property-name="defaultLeftMargin" data-property-data-format="" data-property-parent-context="reportTemplate">{{defaultLeftMargin}}</span></p></td></tr><!--{{#printBreakIfPropertyWillChange \'undefined\'}}--><tr class="query-table-row" data-footer-group="undefined"><td class="query-table-cell" style="border:1px solid #000000;padding:2px;"><p>&nbsp;</p></td><td class="query-table-cell" style="border:1px solid #000000;padding:2px;"><p>&nbsp;</p></td><td class="query-table-cell" style="border:1px solid #000000;padding:2px;"><p>&nbsp;</p></td></tr><!--{{/printBreakIfPropertyWillChange}}--><tr class="query-table-row" data-hidden="1"><td class="query-table-cell" colspan="3">&nbsp;</td></tr><tr class="query-table-row" data-hidden="1"><td class="query-table-cell" colspan="3"><p>&nbsp;</p></td></tr><!--{{/with}}{{/each}}--><tr class="query-table-row" data-hidden="1"><td class="query-table-cell" colspan="3">&nbsp;</td></tr></tbody><tfoot class="query-table-footer"><tr class="query-table-row"><td class="query-table-cell"><p>&nbsp;</p></td><td class="query-table-cell"><p>&nbsp;</p></td><td class="query-table-cell"><p>&nbsp;</p></td></tr></tfoot></table>',
                    {
                        locale: 'en_US',
                        paperFormat: 'a4',
                        variables: JSON.stringify({}),
                        isDefaultHeaderFooter: true,
                        reportName: 'Test',
                    },
                );

                const pdfDocument = await parsePdf(result.value);
                const output = pdfDocument.text
                    .trim()
                    .replaceAll(/(&nbsp;|\u00A0)/g, ' ')
                    .split('\n');

                assert.equal(output[output.length - 2], 'Test'); // default header shows report name
                assert.equal('Printed by Unit Test on the 03/14/2025Page  1', output[output.length - 1]); // default footer shows user who printed and the date and page No
            },
            {
                user: {
                    email: '<EMAIL>',
                },
                today: '2025-03-14',
            },
        ));
});
