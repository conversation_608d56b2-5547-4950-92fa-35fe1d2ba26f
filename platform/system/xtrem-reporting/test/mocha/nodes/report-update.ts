import { Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremReporting from '../../../lib/index';

describe('Modify a Report node', () => {
    it('Modification of description succeeds', () =>
        Test.withContext(async context => {
            const userReport = await context.tryRead(
                xtremReporting.nodes.Report,
                { name: 'testReport' },
                { forUpdate: true },
            );

            await userReport!.$.set({ description: 'Test Report modification' });
            await userReport!.$.save();
            assert.deepEqual(userReport!.$.context.diagnoses, []);
        }));

    it('Modification of name fails for factory reports ', () =>
        Test.withContext(async context => {
            const userReport = await context.tryRead(
                xtremReporting.nodes.Report,
                { name: 'userDetails' },
                { forUpdate: true },
            );

            await assert.isRejected(
                userReport!.$.set({ name: 'testReportRenamed' }),
                'Report.name: cannot set value on frozen property',
            );
        }));

    it('Modification of name does not fail for fails for non-factory reports ', () =>
        Test.withContext(async context => {
            const testReport = await context.read(
                xtremReporting.nodes.Report,
                { name: 'testReport' },
                { forUpdate: true },
            );

            await testReport.$.set({ name: 'testReportRenamed' });
            assert.equal(testReport.$.getRawPropertyValue('name'), 'testReportRenamed');

            await testReport.$.save();
        }));
});
