import { BinaryStream, ConfigManager, Context, Decompress, Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as fs from 'fs';
import * as parsePdf from 'pdf-parse';
import * as Sinon from 'sinon';
import * as xtremReporting from '../../../lib/index';

describe('generateReport', () => {
    const localBasePath = `${process.cwd()}/test/tmp`;

    let requestCount = 0;
    let executeGraphqlStub: Sinon.SinonStub;

    before(() => {
        executeGraphqlStub = Sinon.stub(Context.prototype, 'executeGraphql');
    });

    after(() => {
        executeGraphqlStub.restore();

        // cleanup generated files
        fs.rmSync(localBasePath, { recursive: true, force: true });
    });

    afterEach(() => {
        requestCount = 0;
    });

    it('should generate a pdf using async mutation', () =>
        Test.withContext(
            async context => {
                const config = ConfigManager.current;
                if (config.s3Storage) {
                    config.s3Storage = {
                        ...config.s3Storage,
                        localBasePath,
                    };
                } else {
                    config.s3Storage = {
                        localBasePath,
                    };
                }
                const result = await xtremReporting.nodes.Report.generateReportPdf(context, 'userDetails', '', {
                    locale: 'en_US',
                    paperFormat: 'a4',
                    variables: JSON.stringify({
                        _id: '<EMAIL>',
                    }),
                });
                const tenantId = context.tenantId;
                const key = result.key;
                const filePath = [localBasePath, tenantId, key].join('/');

                const data = (await fs.promises.readFile(filePath as string)) as unknown as Buffer;
                const pdfDocument = await parsePdf(BinaryStream.fromBuffer(data).value);

                assert.equal(
                    pdfDocument.text.trim(),
                    `Unit
PropertyValue
Last NameTest
ID6
<EMAIL>`,
                );
            },
            { source: 'listener' },
        ));

    it('should call the pre and post processing operations if declared on the report', () =>
        Test.withReadonlyContext(
            async context => {
                executeGraphqlStub.callsFake((query: string) => {
                    requestCount += 1;

                    const startOperationResult = (operationName: string) =>
                        Promise.resolve({
                            xtremReporting: {
                                prePostProcessingOperation: {
                                    [operationName]: {
                                        start: {
                                            trackingId: `${operationName}_tracking`,
                                        },
                                    },
                                },
                            },
                        });

                    switch (requestCount) {
                        case 1:
                            assert.equal(
                                query,
                                'mutation { xtremReporting { prePostProcessingOperation { preProcess (report: "#pre-processing-test-report") {watermark} } } }',
                            );

                            return startOperationResult('preProcess');
                        case 2:
                            assert.equal(
                                query,
                                'mutation { xtremReporting { prePostProcessingOperation { postProcess (report: "#pre-processing-test-report") } } }',
                            );

                            return startOperationResult('postProcess');
                        default:
                            throw new Error('Unexpected call');
                    }
                });

                await assert.isFulfilled(
                    xtremReporting.nodes.Report.generateReportPdf(
                        context,
                        'pre-processing-test-report',
                        'pre-processing-test-report-template',
                        {
                            locale: 'en_US',
                            paperFormat: 'a4',
                            variables: JSON.stringify({
                                report: '#pre-processing-test-report',
                            }),
                        },
                    ),
                );
                assert.equal(requestCount, 2);
            },
            { source: 'listener', testActiveServiceOptions: [xtremReporting.serviceOptions.reportAssignment] },
        ));

    it('should generate a zip using async mutation', () =>
        Test.withContext(
            async context => {
                const config = ConfigManager.current;
                if (config.s3Storage) {
                    config.s3Storage = {
                        ...config.s3Storage,
                        localBasePath,
                    };
                } else {
                    config.s3Storage = {
                        localBasePath,
                    };
                }

                const result = await xtremReporting.nodes.Report.generateReportZip(context, 'userDetails', '', [
                    {
                        locale: 'en_US',
                        paperFormat: 'a4',
                        variables: JSON.stringify({
                            _id: '<EMAIL>',
                        }),
                    },
                ]);
                const tenantId = context.tenantId;
                const key = result.key;
                const filePath = [localBasePath, tenantId, key].join('/');

                const pdfs = await Decompress.decompressZipToFolder(filePath, localBasePath);

                const data = (await fs.promises.readFile(`${localBasePath}/${pdfs[0]}`)) as unknown as Buffer;
                const pdfDocument = await parsePdf(BinaryStream.fromBuffer(data).value);

                assert.equal(
                    pdfDocument.text.trim(),
                    `Unit
PropertyValue
Last NameTest
ID6
<EMAIL>`,
                );
            },
            { source: 'listener' },
        ));

    it('should generate multiple reports', () =>
        Test.withContext(async context => {
            const result = await xtremReporting.nodes.Report.generateReports(context, 'userDetails', '', [
                {
                    locale: 'en_US',
                    paperFormat: 'a4',
                    variables: JSON.stringify({
                        _id: '<EMAIL>',
                    }),
                },
                {
                    locale: 'en_US',
                    paperFormat: 'letter',
                    variables: JSON.stringify({
                        _id: '<EMAIL>',
                    }),
                },
            ]);

            const pdfDocument1 = await parsePdf(result[0].value);
            assert.equal(
                pdfDocument1.text.trim(),
                `Unit
PropertyValue
Last NameTest
ID6
<EMAIL>`,
            );

            const pdfDocument2 = await parsePdf(result[1].value);
            assert.equal(
                pdfDocument2.text.trim(),
                `Marcus Vinicius
PropertyValue
Last Nameda Cruz e Mello Moraes
ID8
<EMAIL>`,
            );
        }));
});
