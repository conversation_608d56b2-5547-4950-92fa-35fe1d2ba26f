import { Test } from '@sage/xtrem-core';
import { expect } from 'chai';
import { resolveStyleVariables } from '../../../lib/functions/style-utils';

describe('style utils', () => {
    it('should build stylesheet variable section', () =>
        Test.withContext(async context => {
            const generatedCss = await resolveStyleVariables(context);
            expect(generatedCss).to.eq(`:root {
                --background: #FFFFFF;
                --black55: rgba(0, 0, 0, 0.55);
                --black65: rgba(0, 0, 0, 0.65);
                --black90: rgba(0, 0, 0, 0.90);
                --error: #C7384F;
                --gold: #FFB500;
                --info: #0077C8;
                --logo: #00DC00;
                --slate: #003349;
                --slate20: #335C6D;
                --slate40: #668592;
                --slate60: #99ADB6;
                --slate80: #CCD6DB;
                --slate90: #E5EAEC;
                --slate95: #F2F5F6;
                --success: #00B000;
                --tableSeparator: #D9E0E4;
                --textAndLabels: rgba(0, 0, 0, 0.85);
                --themePrimary: #008A21;
                --themePrimaryHover: #005C9A;
                --warning: #E96400;
            }`);
        }));
});
