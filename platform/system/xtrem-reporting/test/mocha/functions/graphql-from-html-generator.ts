import { expect } from 'chai';
import { jsonToGraphQLQuery } from 'json-to-graphql-query';
import generateGraphQLQueryFromHTML from '../../../lib/functions/graphql-from-html-generator';

describe('graphql from html generator', () => {
    it('should generate graphql query from html', () => {
        const xmlFragment = `
    <div>
<section class="record-context" data-context-object-type="SalesInvoice" data-context-object-path="xtremSales.salesInvoice.query.edges.0.node" data-context-filter="[]">
<!--{{#with xtremSales.salesInvoice.query.edges.0.node}}-->
<div class="report-context-body">
  <p>
    Invoice Date:&nbsp;
    <span class="property" data-property-display-label="Invoice Date" data-property-data-type="String" data-property-name="invoiceDate" data-property-data-format="">
      {{invoiceNumber}}
    </span>
  </p>
  <table class="query-table" data-context-object-type="SalesInvoiceLine" data-context-object-path="lines.query.edges" data-context-list-order="{&quot;description&quot;:&quot;ascending&quot;}" data-context-filter="[{&quot;id&quot;:&quot;description&quot;,&quot;label&quot;:&quot;Description&quot;,&quot;filterType&quot;:&quot;contains&quot;,&quot;filterValue&quot;:&quot;s&quot;,&quot;data&quot;:{&quot;name&quot;:&quot;description&quot;,&quot;label&quot;:&quot;Description&quot;,&quot;kind&quot;:&quot;SCALAR&quot;,&quot;type&quot;:&quot;String&quot;,&quot;canFilter&quot;:true,&quot;canSort&quot;:true},&quot;labelPath&quot;:&quot;Description&quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Description&quot;,&quot;key&quot;:&quot;&quot;,&quot;labelKey&quot;:&quot;&quot;,&quot;id&quot;:&quot;description&quot;,&quot;type&quot;:&quot;String&quot;,&quot;data&quot;:{&quot;name&quot;:&quot;description&quot;,&quot;label&quot;:&quot;Description&quot;,&quot;kind&quot;:&quot;SCALAR&quot;,&quot;type&quot;:&quot;String&quot;,&quot;canFilter&quot;:true,&quot;canSort&quot;:true},&quot;labelPath&quot;:&quot;Description&quot;,&quot;presentation&quot;:&quot;Text&quot;,&quot;title&quot;:&quot;Description&quot;},&quot;key&quot;:&quot;&quot;,&quot;labelKey&quot;:&quot;&quot;}]">
    <thead class="query-table-head">
      <tr class="query-table-row">
        <td class="query-table-cell" style="background-color:#0000001a;border:1px solid #000000;padding:2px;">
          <p>
            Net Price
          </p>
        </td>
      </tr>
    </thead>
    <tbody class="query-table-body">
      <!--{{#each lines.query.edges}}{{#with node}}-->
      <tr class="query-table-row">
        <td class="query-table-cell" style="border:1px solid #000000;padding:2px;">
          <p>
            <span class="property" data-property-display-label="Net Price" data-property-data-type="Float" data-property-name="netPrice" data-property-data-format="">
              {{netPrice}}
            </span>
          </p>
        </td>
      </tr>
      <!--{{/with}}{{/each}}-->
      <tr class="query-table-row" data-hidden="1">
        <td class="query-table-cell" colspan="1">
          &nbsp;
        </td>
      </tr>
    </tbody>
    <tfoot class="query-table-footer">
      <tr class="query-table-row">
        <td class="query-table-cell">
          &nbsp;
        </td>
      </tr>
    </tfoot>
  </table>
</div>
<!--{{/with}}-->
<span class="report-context-footer">
  &nbsp;
</span>
</section>
</div>
`;

        const expectedGraphQLJsonObject = {
            query: {
                xtremSales: {
                    salesInvoice: {
                        query: {
                            __args: {
                                filter: '{}',
                                first: 1,
                            },
                            edges: {
                                node: {
                                    invoiceDate: true,
                                    lines: {
                                        query: {
                                            __args: {
                                                filter: '{"description":{"_and":[{"_regex":"s","_options":"i"}]}}',
                                                orderBy: '{"description":1}',
                                            },
                                            edges: {
                                                node: {
                                                    _id: true,
                                                    netPrice: true,
                                                },
                                            },
                                        },
                                    },
                                    _id: true,
                                },
                            },
                        },
                    },
                },
            },
        };

        const graphqlQuery = generateGraphQLQueryFromHTML(xmlFragment);
        expect(graphqlQuery).to.equal(jsonToGraphQLQuery(expectedGraphQLJsonObject, { pretty: true }));
    });

    it('should generate template for a single table report with alias', () => {
        const xmlFragment =
            '<table class="query-table" data-context-object-type="User" data-alias="uirLgWkV" data-context-object-path="xtremSystem.user.query.edges" data-context-filter="[]" data-context-list-order="{}"><thead class="query-table-head"><tr class="query-table-row"><td class="query-table-cell" style="background-color:#0000001a;border:1px solid #000000;padding:2px;"><p>Last Name</p></td></tr></thead><tbody class="query-table-body"><!--{{#each xtremSystem.user.query.edges}}{{#with node}}--><tr class="query-table-row"><td class="query-table-cell" style="border:1px solid #000000;padding:2px;"><p><span class="property" data-property-display-label="Last Name" data-property-data-type="String" data-property-name="lastName" data-property-data-format="" data-property-parent-context="User">{{lastName}}</span></p></td></tr><!--{{/with}}{{/each}}--><tr class="query-table-row" data-hidden="1"><td class="query-table-cell" colspan="1">&nbsp;</td></tr></tbody><tfoot class="query-table-footer"><tr class="query-table-row"><td class="query-table-cell">&nbsp;</td></tr></tfoot></table>';
        const expectedGraphQLJsonObject = {
            query: {
                uirLgWkV: {
                    __aliasFor: 'xtremSystem',
                    user: {
                        query: {
                            __args: {
                                filter: '{}',
                                orderBy: '{}',
                            },
                            edges: {
                                node: {
                                    _id: true,
                                    lastName: true,
                                },
                            },
                        },
                    },
                },
            },
        };
        const graphqlQuery = generateGraphQLQueryFromHTML(xmlFragment);
        expect(graphqlQuery).to.equal(jsonToGraphQLQuery(expectedGraphQLJsonObject, { pretty: true }));
    });
    it('should generate template for query with deep aliases', () => {
        const xmlFragment = `<section class="record-context" data-context-object-type="SalesOrder" data-context-object-path="xtremSales.salesOrder.query.edges.0.node" data-context-filter="[]" data-alias="TzyVbBJl" data-context-list-order="{&quot;number&quot;:&quot;ascending&quot;}">
            <!--{{#with TzyVbBJl.salesOrder.query.edges.0.node}}-->
            <div class="report-context-body">
               <figure class="table" style="width:100%;">
                  <table class="ck-table-resized">
                     <colgroup>
                        <col style="width:36.09%;">
                        <col style="width:63.91%;">
                     </colgroup>
                     <tbody>
                        <tr>
                           <td style="border:1px solid #668494;">&nbsp;</td>
                           <td style="border:1px solid #668494;text-align:right;">
                              <h4><span style="background-color:transparent;color:#0060A7;"><strong>Sales order confirmation</strong></span></h4>
                              <p><span class="property" data-property-display-label="Number" data-property-data-type="String" data-property-name="number" data-property-data-format="" data-property-parent-context="SalesOrder">{{number}}</span></p>
                              <p><span class="property" data-property-display-label="Order date" data-property-data-type="Date" data-property-name="orderDate" data-property-data-format="FullDate" data-property-parent-context="SalesOrder">{{formatDate orderDate 'FullDate'}}</span></p>
                           </td>
                        </tr>
                     </tbody>
                  </table>
               </figure>
               <figure class="table" style="width:100%;">
                  <table class="ck-table-resized">
                     <colgroup>
                        <col style="width:58.27%;">
                        <col style="width:41.73%;">
                     </colgroup>
                     <tbody>
                        <tr>
                           <td style="border-color:#668494;border-style:solid;">
                              <figure class="table">
                                 <table>
                                    <tbody>
                                       <tr>
                                          <td style="border-color:#004D86;border-style:dotted;"><span style="color:#0060A7;"><strong>Sold-to address</strong></span></td>
                                       </tr>
                                    </tbody>
                                 </table>
                              </figure>
                              <p><strong><span class="property" data-property-display-label="Name" data-property-data-type="String" data-property-name="soldToCustomer.name" data-property-data-format="" data-property-parent-context="SalesOrder">{{soldToCustomer.name}}</span></strong></p>
                              <p><span class="property" data-property-display-label="Address line 1" data-property-data-type="String" data-property-name="soldToAddress.addressLine1" data-property-data-format="" data-property-parent-context="SalesOrder">{{soldToAddress.addressLine1}}</span></p>
                              <p><span class="property" data-property-display-label="City" data-property-data-type="String" data-property-name="soldToAddress.city" data-property-data-format="" data-property-parent-context="SalesOrder">{{soldToAddress.city}}</span></p>
                              <p><span class="property" data-property-display-label="Postal code" data-property-data-type="String" data-property-name="soldToAddress.postcode" data-property-data-format="" data-property-parent-context="SalesOrder">{{soldToAddress.postcode}}</span></p>
                           </td>
                           <td style="border-color:#668494;border-style:solid;">
                              <figure class="table">
                                 <table>
                                    <tbody>
                                       <tr>
                                          <td style="border-color:#0060A7;border-style:dashed;"><span style="color:#0060A7;"><strong>Ship-to address</strong></span></td>
                                       </tr>
                                    </tbody>
                                 </table>
                              </figure>
                              <p><span class="property" data-property-display-label="Name" data-property-data-type="String" data-property-name="shipToCustomer.name" data-property-data-format="" data-property-parent-context="SalesOrder">{{shipToCustomer.name}}</span></p>
                              <p><span class="property" data-property-display-label="Address line 1" data-property-data-type="String" data-property-name="shipToAddress.addressLine1" data-property-data-format="" data-property-parent-context="SalesOrder">{{shipToAddress.addressLine1}}</span></p>
                              <p><span class="property" data-property-display-label="City" data-property-data-type="String" data-property-name="shipToAddress.city" data-property-data-format="" data-property-parent-context="SalesOrder">{{shipToAddress.city}}</span></p>
                              <p><span class="property" data-property-display-label="Postal code" data-property-data-type="String" data-property-name="shipToAddress.postcode" data-property-data-format="" data-property-parent-context="SalesOrder">{{shipToAddress.postcode}}</span></p>
                           </td>
                        </tr>
                     </tbody>
                  </table>
               </figure>
               <figure class="table">
                  <table>
                     <tbody>
                        <tr>
                           <td style="border:1px solid #668494;"><span style="color:#0060A7;"><strong>Shipping date</strong></span></td>
                           <td style="border:1px solid #668494;"><span style="color:#0060A7;"><strong>Delivery mode</strong></span></td>
                           <td style="border:1px solid #668494;"><span style="color:#0060A7;"><strong>Incoterms</strong></span></td>
                           <td style="border:1px solid #668494;"><span style="color:#0060A7;"><strong>Payment term</strong></span></td>
                        </tr>
                        <tr>
                           <td style="border:1px solid #668494;"><span class="property" data-property-display-label="Shipping date" data-property-data-type="Date" data-property-name="shippingDate" data-property-data-format="FullDate" data-property-parent-context="SalesOrder">{{formatDate shippingDate 'FullDate'}}</span></td>
                           <td style="border:1px solid #668494;"><span class="property" data-property-display-label="Name" data-property-data-type="String" data-property-name="deliveryMode.name" data-property-data-format="" data-property-parent-context="SalesOrder">{{deliveryMode.name}}</span></td>
                           <td style="border:1px solid #668494;"><span class="property" data-property-display-label="Name" data-property-data-type="String" data-property-name="incoterm.name" data-property-data-format="" data-property-parent-context="SalesOrder">{{incoterm.name}}</span></td>
                           <td style="border:1px solid #668494;"><span class="property" data-property-display-label="Name" data-property-data-type="String" data-property-name="paymentTerm.name" data-property-data-format="" data-property-parent-context="SalesOrder">{{paymentTerm.name}}</span></td>
                        </tr>
                     </tbody>
                  </table>
               </figure>
               <p>&nbsp;</p>
               <table class="query-table" data-context-object-type="SalesOrderLine" data-context-object-path="lines.query.edges" data-context-list-order="{}" data-context-filter="[]" data-alias="ZrXYukNY">
                  <thead class="query-table-head">
                     <tr class="query-table-row">
                        <td class="query-table-cell" style="background-color:#0000001a;border-color:#000000;padding:2px;">
                           <p>Item ID</p>
                        </td>
                     </tr>
                  </thead>
                  <tbody class="query-table-body">
                     <!--{{#each ZrXYukNY.query.edges}}{{#with node}}-->
                     <tr class="query-table-row">
                        <td class="query-table-cell" style="border-color:#000000;padding:2px;">
                           <p><span class="property" data-property-display-label="Item ID" data-property-data-type="String" data-property-name="itemId" data-property-data-format="" data-property-parent-context="SalesOrderLine">{{itemId}}</span></p>
                        </td>
                     </tr>
                     <!--{{/with}}{{/each}}-->
                     <tr class="query-table-row" data-hidden="1">
                        <td class="query-table-cell" colspan="1">
                           <p>&nbsp;</p>
                        </td>
                     </tr>
                  </tbody>
                  <tfoot class="query-table-footer">
                     <tr class="query-table-row">
                        <td class="query-table-cell">
                           <p>&nbsp;</p>
                        </td>
                     </tr>
                  </tfoot>
               </table>
            </div>
            <!--{{/with}}--><span class="report-context-footer">&nbsp;</span>
         </section>
         <p>&nbsp;</p>`;
        const expectedGraphQLJsonObject = {
            query: {
                TzyVbBJl: {
                    __aliasFor: 'xtremSales',
                    salesOrder: {
                        query: {
                            __args: {
                                filter: '{}',
                                orderBy: '{"number":1}',
                                first: 1,
                            },
                            edges: {
                                node: {
                                    number: true,
                                    orderDate: true,
                                    soldToCustomer: {
                                        name: true,
                                    },
                                    soldToAddress: {
                                        addressLine1: true,
                                        city: true,
                                        postcode: true,
                                    },
                                    shipToCustomer: {
                                        name: true,
                                    },
                                    shipToAddress: {
                                        addressLine1: true,
                                        city: true,
                                        postcode: true,
                                    },
                                    shippingDate: true,
                                    deliveryMode: {
                                        name: true,
                                    },
                                    incoterm: {
                                        name: true,
                                    },
                                    paymentTerm: {
                                        name: true,
                                    },
                                    ZrXYukNY: {
                                        __aliasFor: 'lines',
                                        query: {
                                            __args: {
                                                filter: '{}',
                                                orderBy: '{}',
                                            },
                                            edges: {
                                                node: {
                                                    _id: true,
                                                    itemId: true,
                                                },
                                            },
                                        },
                                    },
                                    _id: true,
                                },
                            },
                        },
                    },
                },
            },
        };
        const graphqlQuery = generateGraphQLQueryFromHTML(xmlFragment);
        expect(graphqlQuery).to.equal(jsonToGraphQLQuery(expectedGraphQLJsonObject, { pretty: true }));
    });

    it('should mark template parameters in the query as handlebars variables', () => {
        const xmlFragment =
            '<section class="record-context" data-context-object-type="PurchaseOrder" data-alias="uirLgWkV" data-context-object-path="xtremPurchasing.purchaseOrder.query.edges.0.node" data-context-filter="[{&quot;id&quot;:&quot;number&quot;,&quot;parameter&quot;:true,&quot;label&quot;:&quot;Number&quot;,&quot;filterType&quot;:&quot;equals&quot;,&quot;filterValue&quot;:&quot;orderNumber&quot;,&quot;data&quot;:{&quot;type&quot;:&quot;String&quot;,&quot;kind&quot;:&quot;SCALAR&quot;,&quot;isCollection&quot;:false,&quot;name&quot;:&quot;number&quot;,&quot;canFilter&quot;:true,&quot;canSort&quot;:true,&quot;label&quot;:&quot;Number&quot;,&quot;iconType&quot;:&quot;csv&quot;},&quot;labelPath&quot;:&quot;number&quot;,&quot;property&quot;:{&quot;type&quot;:&quot;String&quot;,&quot;kind&quot;:&quot;SCALAR&quot;,&quot;isCollection&quot;:false,&quot;name&quot;:&quot;number&quot;,&quot;canFilter&quot;:true,&quot;canSort&quot;:true,&quot;label&quot;:&quot;Number&quot;,&quot;iconType&quot;:&quot;csv&quot;,&quot;id&quot;:&quot;number&quot;,&quot;data&quot;:{&quot;type&quot;:&quot;String&quot;,&quot;kind&quot;:&quot;SCALAR&quot;,&quot;isCollection&quot;:false,&quot;name&quot;:&quot;number&quot;,&quot;canFilter&quot;:true,&quot;canSort&quot;:true,&quot;label&quot;:&quot;Number&quot;,&quot;iconType&quot;:&quot;csv&quot;},&quot;key&quot;:&quot;number&quot;,&quot;labelKey&quot;:&quot;number&quot;,&quot;labelPath&quot;:&quot;number&quot;},&quot;key&quot;:&quot;&quot;,&quot;labelKey&quot;:&quot;&quot;}]"><!--{{#with xtremPurchasing.purchaseOrder.query.edges.0.node}}--><div class="report-context-body"><p>Purchase Order:&nbsp;<span class="property" data-property-display-label="Number" data-property-data-type="String" data-property-name="number" data-property-data-format="" data-property-parent-context="PurchaseOrder">{{number}}</span></p><p><span class="property" data-property-display-label="Order date" data-property-data-type="Date" data-property-name="orderDate" data-property-data-format="FullDate" data-property-parent-context="PurchaseOrder">{{formatDate orderDate \'FullDate\'}}</span></p><p>&nbsp;</p><p>&nbsp;</p><p>&nbsp;</p><p>&nbsp;</p></div><!--{{/with}}--><span class="report-context-footer">&nbsp;</span></section>';
        const expectedGraphQLJsonObject = {
            query: {
                uirLgWkV: {
                    __aliasFor: 'xtremPurchasing',
                    purchaseOrder: {
                        query: {
                            __args: {
                                filter: '{"number":{"_and":[{"_eq":"{{orderNumber}}"}]}}',
                                first: 1,
                            },
                            edges: {
                                node: {
                                    number: true,
                                    orderDate: true,
                                    _id: true,
                                },
                            },
                        },
                    },
                },
            },
        };
        const graphqlQuery = generateGraphQLQueryFromHTML(xmlFragment);
        expect(graphqlQuery).to.equal(jsonToGraphQLQuery(expectedGraphQLJsonObject, { pretty: true }));
    });

    it('should generate query with multiple deep properties that are bound to the same parent property', () => {
        const xmlFragment =
            '<section class="record-context" data-context-object-type="PurchaseOrder" data-context-object-path="xtremPurchasing.purchaseOrder.query.edges.0.node" data-context-filter="[{&quot;id&quot;:&quot;number&quot;,&quot;parameter&quot;:true,&quot;label&quot;:&quot;Number&quot;,&quot;filterType&quot;:&quot;equals&quot;,&quot;filterValue&quot;:&quot;orderNumber&quot;,&quot;data&quot;:{&quot;type&quot;:&quot;String&quot;,&quot;kind&quot;:&quot;SCALAR&quot;,&quot;isCollection&quot;:false,&quot;name&quot;:&quot;number&quot;,&quot;canFilter&quot;:true,&quot;canSort&quot;:true,&quot;label&quot;:&quot;Number&quot;,&quot;iconType&quot;:&quot;csv&quot;},&quot;labelPath&quot;:&quot;number&quot;,&quot;property&quot;:{&quot;type&quot;:&quot;String&quot;,&quot;kind&quot;:&quot;SCALAR&quot;,&quot;isCollection&quot;:false,&quot;name&quot;:&quot;number&quot;,&quot;canFilter&quot;:true,&quot;canSort&quot;:true,&quot;label&quot;:&quot;Number&quot;,&quot;iconType&quot;:&quot;csv&quot;,&quot;id&quot;:&quot;number&quot;,&quot;data&quot;:{&quot;type&quot;:&quot;String&quot;,&quot;kind&quot;:&quot;SCALAR&quot;,&quot;isCollection&quot;:false,&quot;name&quot;:&quot;number&quot;,&quot;canFilter&quot;:true,&quot;canSort&quot;:true,&quot;label&quot;:&quot;Number&quot;,&quot;iconType&quot;:&quot;csv&quot;},&quot;key&quot;:&quot;number&quot;,&quot;labelKey&quot;:&quot;number&quot;,&quot;labelPath&quot;:&quot;number&quot;},&quot;key&quot;:&quot;&quot;,&quot;labelKey&quot;:&quot;&quot;}]"><!--{{#with xtremPurchasing.purchaseOrder.query.edges.0.node}}--><div class="report-context-body"><p>Purchase Order:&nbsp;<span class="property" data-property-display-label="Number" data-property-data-type="String" data-property-name="number" data-property-data-format="" data-property-parent-context="PurchaseOrder">{{number}}</span></p><p><span class="property" data-property-display-label="Order date" data-property-data-type="Date" data-property-name="orderDate" data-property-data-format="FullDate" data-property-parent-context="PurchaseOrder">{{formatDate orderDate \'FullDate\'}}</span></p><p>&nbsp;</p><p><span class="property" data-property-display-label="Name" data-property-data-type="String" data-property-name="site.country.name" data-property-data-format="" data-property-parent-context="PurchaseOrder">{{site.country.name}}</span></p><p><span class="property" data-property-display-label="Name" data-property-data-type="String" data-property-name="site.currency.name" data-property-data-format="" data-property-parent-context="PurchaseOrder">{{site.currency.name}}</span></p><p><span class="property" data-property-display-label="Symbol" data-property-data-type="String" data-property-name="site.currency.symbol" data-property-data-format="" data-property-parent-context="PurchaseOrder">{{site.currency.symbol}}</span></p><p><span class="property" data-property-display-label="Address line 1" data-property-data-type="String" data-property-name="supplier.billByAddress.addressLine1" data-property-data-format="" data-property-parent-context="PurchaseOrder">{{supplier.billByAddress.addressLine1}}</span><span class="property" data-property-display-label="City" data-property-data-type="String" data-property-name="supplier.billByAddress.city" data-property-data-format="" data-property-parent-context="PurchaseOrder">{{supplier.billByAddress.city}}</span><span class="property" data-property-display-label="Postal code" data-property-data-type="String" data-property-name="supplier.billByAddress.postcode" data-property-data-format="" data-property-parent-context="PurchaseOrder">{{supplier.billByAddress.postcode}}</span></p><table class="query-table" data-context-object-type="PurchaseOrderLine" data-context-object-path="lines.query.edges" data-context-filter="[]" data-context-list-order="{}"><thead class="query-table-head"><tr class="query-table-row"><td class="query-table-cell" style="background-color:#0000001a;border:1px solid #000000;padding:2px;"><p>EAN</p></td><td class="query-table-cell" style="background-color:#0000001a;border:1px solid #000000;padding:2px;"><p>Name</p></td><td class="query-table-cell" style="background-color:#0000001a;border:1px solid #000000;padding:2px;"><p>Net price</p></td><td class="query-table-cell" style="background-color:#0000001a;border:1px solid #000000;padding:2px;"><p>Quantity</p></td></tr></thead><tbody class="query-table-body"><!--{{#each lines.query.edges}}{{#with node}}--><tr class="query-table-row"><td class="query-table-cell" style="border:1px solid #000000;padding:2px;"><p><span class="property" data-property-display-label="EAN" data-property-data-type="String" data-property-name="item.eanNumber" data-property-data-format="" data-property-parent-context="PurchaseOrderLine">{{item.eanNumber}}</span></p></td><td class="query-table-cell" style="border:1px solid #000000;padding:2px;"><p><span class="property" data-property-display-label="Name" data-property-data-type="String" data-property-name="item.name" data-property-data-format="" data-property-parent-context="PurchaseOrderLine">{{item.name}}</span></p></td><td class="query-table-cell" style="border:1px solid #000000;padding:2px;"><p><span class="property" data-property-display-label="Net price" data-property-data-type="Decimal" data-property-name="netPrice" data-property-data-format="" data-property-parent-context="PurchaseOrderLine">{{netPrice}}</span></p></td><td class="query-table-cell" style="border:1px solid #000000;padding:2px;"><p><span class="property" data-property-display-label="Quantity" data-property-data-type="Decimal" data-property-name="quantity" data-property-data-format="" data-property-parent-context="PurchaseOrderLine">{{quantity}}</span></p></td></tr><!--{{/with}}{{/each}}--><tr class="query-table-row" data-hidden="1"><td class="query-table-cell" colspan="4">&nbsp;</td></tr></tbody><tfoot class="query-table-footer"><tr class="query-table-row"><td class="query-table-cell">&nbsp;</td><td class="query-table-cell">&nbsp;</td><td class="query-table-cell">&nbsp;</td><td class="query-table-cell">&nbsp;</td></tr></tfoot></table><p>&nbsp;</p><p>&nbsp;</p><p>&nbsp;</p><p>&nbsp;</p><p>&nbsp;</p></div><!--{{/with}}--><span class="report-context-footer">&nbsp;</span></section>';

        const expectedGraphQLJsonObject = {
            query: {
                xtremPurchasing: {
                    purchaseOrder: {
                        query: {
                            __args: {
                                filter: '{"number":{"_and":[{"_eq":"{{orderNumber}}"}]}}',
                                first: 1,
                            },
                            edges: {
                                node: {
                                    number: true,
                                    orderDate: true,
                                    site: {
                                        country: { name: true },
                                        currency: { name: true, symbol: true },
                                    },
                                    supplier: {
                                        billByAddress: {
                                            addressLine1: true,
                                            city: true,
                                            postcode: true,
                                        },
                                    },
                                    lines: {
                                        query: {
                                            __args: {
                                                filter: '{}',
                                                orderBy: '{}',
                                            },
                                            edges: {
                                                node: {
                                                    _id: true,
                                                    item: {
                                                        eanNumber: true,
                                                        name: true,
                                                    },
                                                    netPrice: true,
                                                    quantity: true,
                                                },
                                            },
                                        },
                                    },
                                    _id: true,
                                },
                            },
                        },
                    },
                },
            },
        };
        const graphqlQuery = generateGraphQLQueryFromHTML(xmlFragment);
        expect(graphqlQuery).to.equal(jsonToGraphQLQuery(expectedGraphQLJsonObject, { pretty: true }));
    });

    it('should generate a simple list query when there are more than one root elements', () => {
        const xmlFragment =
            '<p>Hi</p><table class="query-table" data-context-object-type="User" data-context-object-path="xtremSystem.user.query.edges" data-context-filter="[]" data-context-list-order="{}"><thead class="query-table-head"><tr class="query-table-row"><td class="query-table-cell" style="background-color:#0000001a;border:1px solid #000000;padding:2px;"><p>Email</p></td><td class="query-table-cell" style="background-color:#0000001a;border:1px solid #000000;padding:2px;"><p>First name</p></td><td class="query-table-cell" style="background-color:#0000001a;border:1px solid #000000;padding:2px;"><p>Last name</p></td></tr></thead><tbody class="query-table-body"><!--{{#each xtremSystem.user.query.edges}}{{#with node}}--><tr class="query-table-row"><td class="query-table-cell" style="border:1px solid #000000;padding:2px;"><p><span class="property" data-property-display-label="Email" data-property-data-type="String" data-property-name="email" data-property-data-format="" data-property-parent-context="User">{{email}}</span></p></td><td class="query-table-cell" style="border:1px solid #000000;padding:2px;"><p><span class="property" data-property-display-label="First name" data-property-data-type="String" data-property-name="firstName" data-property-data-format="" data-property-parent-context="User">{{firstName}}</span></p></td><td class="query-table-cell" style="border:1px solid #000000;padding:2px;"><p><span class="property" data-property-display-label="Last name" data-property-data-type="String" data-property-name="lastName" data-property-data-format="" data-property-parent-context="User">{{lastName}}</span></p></td></tr><!--{{/with}}{{/each}}--><tr class="query-table-row" data-hidden="1"><td class="query-table-cell" colspan="3">&nbsp;</td></tr></tbody><tfoot class="query-table-footer"><tr class="query-table-row"><td class="query-table-cell">&nbsp;</td><td class="query-table-cell">&nbsp;</td><td class="query-table-cell">&nbsp;</td></tr></tfoot></table>';

        const expectedGraphQLJsonObject = {
            query: {
                xtremSystem: {
                    user: {
                        query: {
                            __args: {
                                filter: '{}',
                                orderBy: '{}',
                            },
                            edges: {
                                node: {
                                    _id: true,
                                    email: true,
                                    firstName: true,
                                    lastName: true,
                                },
                            },
                        },
                    },
                },
            },
        };
        const graphqlQuery = generateGraphQLQueryFromHTML(xmlFragment);
        expect(graphqlQuery).to.equal(jsonToGraphQLQuery(expectedGraphQLJsonObject, { pretty: true }));
    });

    it('should generate a list report document with custom fields', () => {
        const xmlFragment =
            '<p>List report with custom fields</p><table class="query-table" data-context-object-type="ShowCaseProduct" data-context-object-path="xtremShowCase.showCaseProduct.query.edges" data-context-filter="[]" data-context-list-order="{&quot;_customData.testCustomField&quot;:&quot;ascending&quot;,&quot;product&quot;:&quot;ascending&quot;,&quot;netPrice&quot;:&quot;ascending&quot;}" data-alias="WJhkjsVn"><thead class="query-table-head"><tr class="query-table-row"><td class="query-table-cell" style="background-color:#0000001a;border:1px solid #000000;padding:2px;"><p>testCustomField</p></td><td class="query-table-cell" style="background-color:#0000001a;border:1px solid #000000;padding:2px;"><p>Product</p></td><td class="query-table-cell" style="background-color:#0000001a;border:1px solid #000000;padding:2px;"><p>Net price</p></td></tr></thead><tbody class="query-table-body"><!--{{#each WJhkjsVn.showCaseProduct.query.edges}}{{#with node}}--><tr class="query-table-row"><td class="query-table-cell" style="border:1px solid #000000;padding:2px;"><p><span class="property" data-property-display-label="testCustomField" data-property-data-type="String" data-property-name="_customData.testCustomField" data-property-data-format="" data-property-parent-context="ShowCaseProduct">{{_customData.testCustomField}}</span></p></td><td class="query-table-cell" style="border:1px solid #000000;padding:2px;"><p><span class="property" data-property-display-label="Product" data-property-data-type="String" data-property-name="product" data-property-data-format="" data-property-parent-context="ShowCaseProduct">{{product}}</span><span class="property" data-property-display-label="test" data-property-data-type="Boolean" data-property-name="_customData.test" data-property-data-format="" data-property-parent-context="ShowCaseProduct">{{_customData.test}}</span></p></td><td class="query-table-cell" style="border:1px solid #000000;padding:2px;"><p><span class="property" data-property-display-label="Net price" data-property-data-type="Decimal" data-property-name="netPrice" data-property-data-format="" data-property-parent-context="ShowCaseProduct">{{netPrice}}</span></p></td></tr><tr class="query-table-row" data-hidden="1"><td class="query-table-cell" colspan="3"><p>&nbsp;</p></td></tr><!--{{/with}}{{/each}}--><tr class="query-table-row" data-hidden="1"><td class="query-table-cell" colspan="3">&nbsp;</td></tr></tbody><tfoot class="query-table-footer"><tr class="query-table-row"><td class="query-table-cell"><p>&nbsp;</p></td><td class="query-table-cell"><p>&nbsp;</p></td><td class="query-table-cell"><p>&nbsp;</p></td></tr></tfoot></table>';

        const expectedGraphQLJsonObject = {
            query: {
                WJhkjsVn: {
                    __aliasFor: 'xtremShowCase',
                    showCaseProduct: {
                        query: {
                            __args: {
                                filter: '{}',
                                orderBy: '{"_customData":{"testCustomField":1},"product":1,"netPrice":1}',
                            },
                            edges: {
                                node: {
                                    _id: true,
                                    _customData: true,
                                    product: true,
                                    netPrice: true,
                                },
                            },
                        },
                    },
                },
            },
        };
        const graphqlQuery = generateGraphQLQueryFromHTML(xmlFragment);
        expect(graphqlQuery).to.equal(jsonToGraphQLQuery(expectedGraphQLJsonObject, { pretty: true }));
    });

    it('should generate a block report document with custom fields', () => {
        const xmlFragment =
            '<section class="record-context" data-context-object-type="ShowCaseProduct" data-context-object-path="xtremShowCase.showCaseProduct.query.edges.0.node" data-context-filter="[]" data-context-list-order="{}" data-alias="gXaRvEgp"><!--{{#with gXaRvEgp.showCaseProduct.query.edges.0.node}}--><div class="report-context-body"><p><span class="property" data-property-display-label="Product" data-property-data-type="String" data-property-name="product" data-property-data-format="" data-property-parent-context="ShowCaseProduct">{{product}}</span><span class="property" data-property-display-label="testCustomField" data-property-data-type="String" data-property-name="_customData.testCustomField" data-property-data-format="" data-property-parent-context="ShowCaseProduct">{{_customData.testCustomField}}</span></p></div><!--{{/with}}--><span class="report-context-footer">&nbsp;</span></section>';

        const expectedGraphQLJsonObject = {
            query: {
                gXaRvEgp: {
                    __aliasFor: 'xtremShowCase',
                    showCaseProduct: {
                        query: {
                            __args: {
                                filter: '{}',
                                orderBy: '{}',
                                first: 1,
                            },
                            edges: {
                                node: {
                                    product: true,
                                    _customData: true,
                                    _id: true,
                                },
                            },
                        },
                    },
                },
            },
        };

        const graphqlQuery = generateGraphQLQueryFromHTML(xmlFragment);
        expect(graphqlQuery).to.equal(jsonToGraphQLQuery(expectedGraphQLJsonObject, { pretty: true }));
    });

    it('should generate a query with deep custom fields', () => {
        const xmlFragment =
            '<section class="record-context" data-context-object-type="SalesInvoice" data-context-object-path="xtremSales.salesInvoice.query.edges.0.node" data-context-filter="[]" data-context-list-order="{}" data-alias="KfTkcBKD"><!--{{#with KfTkcBKD.salesInvoice.query.edges.0.node}}--><div class="report-context-body"><p>Number:&nbsp;<span class="property" data-property-display-label="Number" data-property-data-type="String" data-property-name="number" data-property-data-format="" data-property-parent-context="SalesInvoice">{{number}}</span></p><p>Nested custom field:&nbsp;<span class="property" data-property-display-label="displayInReport" data-property-data-type="String" data-property-name="billToCustomer._customData.displayInReport" data-property-data-format="" data-property-parent-context="SalesInvoice">{{billToCustomer._customData.displayInReport}}</span></p><p>Other custom field:&nbsp;<span class="property" data-property-display-label="asdasdsa" data-property-data-type="String" data-property-name="_customData.asdasdsa" data-property-data-format="" data-property-parent-context="SalesInvoice">{{_customData.asdasdsa}}</span></p><p>&nbsp;</p><p>&nbsp;</p><p>&nbsp;</p></div><!--{{/with}}--><span class="report-context-footer">&nbsp;</span></section>';
        const expectedGraphQLJsonObject = {
            query: {
                KfTkcBKD: {
                    __aliasFor: 'xtremSales',
                    salesInvoice: {
                        query: {
                            __args: {
                                filter: '{}',
                                orderBy: '{}',
                                first: 1,
                            },
                            edges: {
                                node: {
                                    number: true,
                                    billToCustomer: {
                                        _customData: true,
                                    },
                                    _customData: true,
                                    _id: true,
                                },
                            },
                        },
                    },
                },
            },
        };

        const graphqlQuery = generateGraphQLQueryFromHTML(xmlFragment);
        expect(graphqlQuery).to.equal(jsonToGraphQLQuery(expectedGraphQLJsonObject, { pretty: true }));
    });

    it('should exclude global properties from generated query', () => {
        const xmlFragment =
            '<table class="query-table" data-context-object-type="Company" data-context-object-path="xtremSystem.company.query.edges" data-context-filter="[]" data-context-list-order="{&quot;isActive&quot;:&quot;ascending&quot;,&quot;id&quot;:&quot;ascending&quot;}" data-alias="KCDJsKQx"><thead class="query-table-head"><tr class="query-table-row"><td class="query-table-cell" style="background-color:#0000001a;border:1px solid #000000;padding:2px;"><p>Active</p></td><td class="query-table-cell" style="background-color:#0000001a;border:1px solid #000000;padding:2px;"><p>ID</p></td></tr></thead><tbody class="query-table-body"><!--{{#each KCDJsKQx.company.query.edges}}{{#with node}}--><tr class="query-table-row"><td class="query-table-cell" style="border:1px solid #000000;padding:2px;"><p><span class="property" data-property-display-label="Active" data-property-data-type="Boolean" data-property-name="isActive" data-property-data-format="" data-property-parent-context="Company">{{isActive}}</span><span class="property" data-property-display-label="Parameter - _id" data-property-data-type="String" data-property-name="@root._id" data-property-data-format="" data-property-parent-context="$globalProperties">{{@root._id}}</span></p></td><td class="query-table-cell" style="border:1px solid #000000;padding:2px;"><p><span class="property" data-property-display-label="ID" data-property-data-type="String" data-property-name="id" data-property-data-format="" data-property-parent-context="Company">{{id}}</span>test</p></td></tr><tr class="query-table-row" data-hidden="1"><td class="query-table-cell" colspan="2"><p>&nbsp;</p></td></tr><!--{{/with}}{{/each}}--><tr class="query-table-row" data-hidden="1"><td class="query-table-cell" colspan="2">&nbsp;</td></tr></tbody><tfoot class="query-table-footer"><tr class="query-table-row"><td class="query-table-cell"><p>&nbsp;</p></td><td class="query-table-cell"><p>&nbsp;</p></td></tr></tfoot></table><p><span class="property" data-property-display-label="Created by" data-property-data-type="String" data-property-name="@root.createdByUser" data-property-data-format="" data-property-parent-context="$globalProperties">{{@root.createdByUser}}</span></p>';

        const expectedGraphQLJsonObject = {
            query: {
                KCDJsKQx: {
                    __aliasFor: 'xtremSystem',
                    company: {
                        query: {
                            __args: {
                                filter: '{}',
                                orderBy: '{"isActive":1,"id":1}',
                            },
                            edges: {
                                node: {
                                    _id: true,
                                    isActive: true,
                                    id: true,
                                },
                            },
                        },
                    },
                },
            },
        };
        const graphqlQuery = generateGraphQLQueryFromHTML(xmlFragment);
        expect(graphqlQuery).to.equal(jsonToGraphQLQuery(expectedGraphQLJsonObject, { pretty: true }));
    });

    it('should generate a list query without the block aggregate object properties when the table is grouped', () => {
        const xmlFragment =
            '<p>Test</p><table class="query-table" data-context-object-type="ShowCaseProduct" data-context-object-path="xtremShowCase.showCaseProduct.query.edges" data-context-filter="[]" data-context-list-order="{&quot;product&quot;:&quot;ascending&quot;,&quot;qty&quot;:&quot;ascending&quot;,&quot;netPrice&quot;:&quot;ascending&quot;,&quot;provider.textField&quot;:&quot;ascending&quot;,&quot;category&quot;:&quot;ascending&quot;}" data-alias="SShDzhRC"><thead class="query-table-head"><tr class="query-table-row"><td class="query-table-cell" style="background-color:#0000001a;border:1px solid #000000;padding:2px;"><p>Product</p></td><td class="query-table-cell" style="background-color:#0000001a;border:1px solid #000000;padding:2px;"><p>Qty</p></td><td class="query-table-cell" style="background-color:#0000001a;border:1px solid #000000;padding:2px;"><p>Net price</p></td><td class="query-table-cell" style="background-color:#0000001a;border:1px solid #000000;padding:2px;"><p>Text field</p></td><td class="query-table-cell" style="background-color:#0000001a;border:1px solid #000000;padding:2px;"><p>Category</p></td></tr></thead><tbody class="query-table-body"><!--{{#each SShDzhRC.showCaseProduct.query.edges}}{{#with node}}--><tr class="query-table-row"><td class="query-table-cell" style="border:1px solid #000000;padding:2px;"><p><span class="property" data-property-display-label="Product" data-property-data-type="String" data-property-name="product" data-property-data-format="" data-property-parent-context="ShowCaseProduct">{{product}}</span></p></td><td class="query-table-cell" style="border:1px solid #000000;padding:2px;"><p><span class="property" data-property-display-label="Qty" data-property-data-type="Int" data-property-name="qty" data-property-data-format="" data-property-parent-context="ShowCaseProduct">{{qty}}</span></p></td><td class="query-table-cell" style="border:1px solid #000000;padding:2px;"><p><span class="property" data-property-display-label="Net price" data-property-data-type="Decimal" data-property-name="netPrice" data-property-data-format="" data-property-parent-context="ShowCaseProduct">{{netPrice}}</span></p></td><td class="query-table-cell" style="border:1px solid #000000;padding:2px;"><p><span class="property" data-property-display-label="Text field" data-property-data-type="String" data-property-name="provider.textField" data-property-data-format="" data-property-parent-context="ShowCaseProduct">{{provider.textField}}</span></p></td><td class="query-table-cell" style="border:1px solid #000000;padding:2px;"><p><span class="property" data-property-display-label="Category" data-property-data-type="@sage/xtrem-show-case/ShowCaseProductCategory" data-property-name="category" data-property-data-format="ENUM" data-property-parent-context="ShowCaseProduct">{{enumValue \'@sage/xtrem-show-case/ShowCaseProductCategory\' category}}</span></p></td></tr><!--{{#printBreakIfPropertyWillChange \'product\' \'product\' \'distinctCount\' \'qty\' \'sum\' \'netPrice\' \'sum\'}}--><tr class="query-table-row" data-footer-group="2"><td class="query-table-cell" style="border:1px solid #000000;padding:2px;"><p><span class="property" data-property-display-label="Product" data-property-data-type="String" data-property-name="_blockAggregatedData.product.distinctCount" data-property-data-format="" data-property-parent-context="ShowCaseProduct">{{_blockAggregatedData.product.distinctCount}}</span></p></td><td class="query-table-cell" style="border:1px solid #000000;padding:2px;"><p><span class="property" data-property-display-label="Qty" data-property-data-type="Int" data-property-name="_blockAggregatedData.qty.sum" data-property-data-format="" data-property-parent-context="ShowCaseProduct">{{_blockAggregatedData.qty.sum}}</span></p></td><td class="query-table-cell" style="border:1px solid #000000;padding:2px;"><p><span class="property" data-property-display-label="Net price" data-property-data-type="Decimal" data-property-name="_blockAggregatedData.netPrice.sum" data-property-data-format="" data-property-parent-context="ShowCaseProduct">{{_blockAggregatedData.netPrice.sum}}</span></p></td><td class="query-table-cell" style="border:1px solid #000000;padding:2px;"><p>e</p></td><td class="query-table-cell" style="border:1px solid #000000;padding:2px;"><p>&nbsp;</p></td></tr><!--{{/printBreakIfPropertyWillChange}}--><tr class="query-table-row" data-hidden="1"><td class="query-table-cell" colspan="5">&nbsp;</td></tr><!--{{#printBreakIfPropertyWillChange \'netPrice\' \'product\' \'distinctCount\' \'qty\' \'sum\' \'netPrice\' \'sum\'}}--><tr class="query-table-row" data-footer-group="0"><td class="query-table-cell" style="border:1px solid #000000;padding:2px;"><p><span class="property" data-property-display-label="Product" data-property-data-type="String" data-property-name="_blockAggregatedData.product.distinctCount" data-property-data-format="" data-property-parent-context="ShowCaseProduct">{{_blockAggregatedData.product.distinctCount}}</span></p></td><td class="query-table-cell" style="border:1px solid #000000;padding:2px;"><p><span class="property" data-property-display-label="Qty" data-property-data-type="Int" data-property-name="_blockAggregatedData.qty.sum" data-property-data-format="" data-property-parent-context="ShowCaseProduct">{{_blockAggregatedData.qty.sum}}</span></p></td><td class="query-table-cell" style="border:1px solid #000000;padding:2px;"><p><span class="property" data-property-display-label="Net price" data-property-data-type="Decimal" data-property-name="_blockAggregatedData.netPrice.sum" data-property-data-format="" data-property-parent-context="ShowCaseProduct">{{_blockAggregatedData.netPrice.sum}}</span></p></td><td class="query-table-cell" style="border:1px solid #000000;padding:2px;"><p>&nbsp;</p></td><td class="query-table-cell" style="border:1px solid #000000;padding:2px;"><p>&nbsp;</p></td></tr><!--{{/printBreakIfPropertyWillChange}}--><tr class="query-table-row" data-hidden="1"><td class="query-table-cell" colspan="5">&nbsp;</td></tr><!--{{#printBreakIfPropertyWillChange \'provider.textField\' \'product\' \'distinctCount\' \'qty\' \'sum\' \'netPrice\' \'sum\'}}--><tr class="query-table-row" data-footer-group="1"><td class="query-table-cell" style="border:1px solid #000000;padding:2px;"><p><span class="property" data-property-display-label="Product" data-property-data-type="String" data-property-name="_blockAggregatedData.product.distinctCount" data-property-data-format="" data-property-parent-context="ShowCaseProduct">{{_blockAggregatedData.product.distinctCount}}</span></p></td><td class="query-table-cell" style="border:1px solid #000000;padding:2px;"><p><span class="property" data-property-display-label="Qty" data-property-data-type="Int" data-property-name="_blockAggregatedData.qty.sum" data-property-data-format="" data-property-parent-context="ShowCaseProduct">{{_blockAggregatedData.qty.sum}}</span></p></td><td class="query-table-cell" style="border:1px solid #000000;padding:2px;"><p><span class="property" data-property-display-label="Net price" data-property-data-type="Decimal" data-property-name="_blockAggregatedData.netPrice.sum" data-property-data-format="" data-property-parent-context="ShowCaseProduct">{{_blockAggregatedData.netPrice.sum}}</span></p></td><td class="query-table-cell" style="border:1px solid #000000;padding:2px;"><p>&nbsp;</p></td><td class="query-table-cell" style="border:1px solid #000000;padding:2px;"><p>&nbsp;</p></td></tr><!--{{/printBreakIfPropertyWillChange}}--><tr class="query-table-row" data-hidden="1"><td class="query-table-cell" colspan="5">&nbsp;</td></tr><!--{{/with}}{{/each}}--><tr class="query-table-row" data-hidden="1"><td class="query-table-cell" colspan="5">&nbsp;</td></tr></tbody></table>';

        const expectedGraphQLJsonObject = {
            query: {
                SShDzhRC: {
                    __aliasFor: 'xtremShowCase',
                    showCaseProduct: {
                        query: {
                            __args: {
                                filter: '{}',
                                orderBy: '{"product":1,"qty":1,"netPrice":1,"provider":{"textField":1},"category":1}',
                            },
                            edges: {
                                node: {
                                    _id: true,
                                    product: true,
                                    qty: true,
                                    netPrice: true,
                                    provider: {
                                        textField: true,
                                    },
                                    category: true,
                                },
                            },
                        },
                    },
                },
            },
        };
        const graphqlQuery = generateGraphQLQueryFromHTML(xmlFragment);
        expect(graphqlQuery).to.equal(jsonToGraphQLQuery(expectedGraphQLJsonObject, { pretty: true }));
    });

    it('should create a graphql query merging the queries from multiple fragments', () => {
        const xmlFragment1 =
            '<section class="record-context" data-context-object-type="PurchaseOrder" data-context-object-path="xtremSales.salesOrder.query.edges.0.node" data-context-filter="[]" data-context-list-order="{}" data-alias="LgWkVuir"><!--{{#with LgWkVuir.salesOrder.query.edges.0.node}}--><div class="report-context-body"><p>Sales Order:&nbsp;<span class="property" data-property-display-label="Number" data-property-data-type="String" data-property-name="number" data-property-data-format="" data-property-parent-context="PurchaseOrder">{{number}}</span></p></div><!--{{/with}}--><span class="report-context-footer">&nbsp;</span></section>';
        const xmlFragment2 =
            '<section class="record-context" data-context-object-type="PurchaseOrder" data-context-object-path="xtremPurchasing.purchaseOrder.query.edges.0.node" data-context-filter="[]" data-context-list-order="{}" data-alias="uirLgWkV"><!--{{#with uirLgWkV.purchaseOrder.query.edges.0.node}}--><div class="report-context-body"><p>Purchase Order:&nbsp;<span class="property" data-property-display-label="Number" data-property-data-type="String" data-property-name="number" data-property-data-format="" data-property-parent-context="PurchaseOrder">{{number}}</span></p></div><!--{{/with}}--><span class="report-context-footer">&nbsp;</span></section>';
        const expectedGraphQLJsonObject = {
            query: {
                uirLgWkV: {
                    __aliasFor: 'xtremPurchasing',
                    purchaseOrder: {
                        query: {
                            __args: {
                                filter: '{}',
                                orderBy: '{}',
                                first: 1,
                            },
                            edges: {
                                node: {
                                    number: true,
                                    _id: true,
                                },
                            },
                        },
                    },
                },
                LgWkVuir: {
                    __aliasFor: 'xtremSales',
                    salesOrder: {
                        query: {
                            __args: {
                                filter: '{}',
                                orderBy: '{}',
                                first: 1,
                            },
                            edges: {
                                node: {
                                    number: true,
                                    _id: true,
                                },
                            },
                        },
                    },
                },
            },
        };
        const graphqlQuery = generateGraphQLQueryFromHTML(xmlFragment1, xmlFragment2);
        expect(graphqlQuery).to.equal(jsonToGraphQLQuery(expectedGraphQLJsonObject, { pretty: true }));
    });

    it('should create a graphql query using the properties from a condition', () => {
        const xmlFragment =
            '<section class="record-context" data-context-object-type="PurchaseOrder" data-context-object-path="xtremPurchasing.purchaseOrder.query.edges.0.node" data-context-filter="[]" data-context-list-order="{}" data-alias="PAKcHBgF"><!--{{#with PAKcHBgF.purchaseOrder.query.edges.0.node}}--><div class="report-context-body"><p>Purchase Order&nbsp;<span class="property" data-property-display-label="Number" data-property-data-type="String" data-property-name="number" data-property-data-format="" data-property-parent-context="PurchaseOrder">{{number}}</span></p><section class="conditional-block" data-context-condition="[{&quot;conjunction&quot;:&quot;and&quot;,&quot;valueType1&quot;:&quot;property&quot;,&quot;valueType2&quot;:&quot;property&quot;,&quot;_id&quot;:&quot;1&quot;,&quot;value1&quot;:{&quot;label&quot;:&quot;Number&quot;,&quot;data&quot;:{&quot;name&quot;:&quot;number&quot;,&quot;title&quot;:&quot;Number&quot;,&quot;canSort&quot;:true,&quot;canFilter&quot;:true,&quot;type&quot;:&quot;String&quot;,&quot;isCustom&quot;:false,&quot;isMutable&quot;:false,&quot;isOnInputType&quot;:true,&quot;isOnOutputType&quot;:true,&quot;enumType&quot;:null,&quot;dataType&quot;:&quot;documentNumber&quot;,&quot;targetNode&quot;:&quot;&quot;,&quot;isCollection&quot;:false,&quot;kind&quot;:&quot;SCALAR&quot;,&quot;label&quot;:&quot;Number&quot;,&quot;node&quot;:&quot;String&quot;,&quot;iconType&quot;:&quot;csv&quot;},&quot;id&quot;:&quot;number&quot;,&quot;key&quot;:&quot;number&quot;,&quot;labelKey&quot;:&quot;Number&quot;,&quot;labelPath&quot;:&quot;Number&quot;},&quot;value2&quot;:{&quot;label&quot;:&quot;Last name&quot;,&quot;data&quot;:{&quot;name&quot;:&quot;lastName&quot;,&quot;title&quot;:&quot;Last name&quot;,&quot;canSort&quot;:true,&quot;canFilter&quot;:true,&quot;type&quot;:&quot;String&quot;,&quot;isCustom&quot;:false,&quot;isMutable&quot;:false,&quot;isOnInputType&quot;:true,&quot;isOnOutputType&quot;:true,&quot;enumType&quot;:null,&quot;dataType&quot;:&quot;&quot;,&quot;targetNode&quot;:&quot;&quot;,&quot;isCollection&quot;:false,&quot;kind&quot;:&quot;SCALAR&quot;,&quot;label&quot;:&quot;Last name&quot;,&quot;node&quot;:&quot;String&quot;,&quot;iconType&quot;:&quot;csv&quot;},&quot;id&quot;:&quot;currency._updateUser.lastName&quot;,&quot;key&quot;:&quot;currency._updateUser.lastName&quot;,&quot;labelKey&quot;:&quot;Last name&quot;,&quot;labelPath&quot;:&quot;Currency > Update user > Last name&quot;},&quot;key&quot;:&quot;1&quot;,&quot;operator&quot;:&quot;equals&quot;}]"><!--{{#if ( eq number currency._updateUser.lastName )}}--><div class="conditional-block-body">CONDITION</div><!--{{/if}}--><div class="conditional-block-footer">&nbsp;</div></section><p>&nbsp;</p><p>&nbsp;</p></div><!--{{/with}}--><span class="report-context-footer">&nbsp;</span></section>';

        const expectedGraphQLJsonObject = {
            query: {
                PAKcHBgF: {
                    __aliasFor: 'xtremPurchasing',
                    purchaseOrder: {
                        query: {
                            __args: {
                                filter: '{}',
                                orderBy: '{}',
                                first: 1,
                            },
                            edges: {
                                node: {
                                    number: true,
                                    currency: {
                                        _updateUser: {
                                            lastName: true,
                                        },
                                    },
                                    _id: true,
                                },
                            },
                        },
                    },
                },
            },
        };
        const graphqlQuery = generateGraphQLQueryFromHTML(xmlFragment);
        expect(graphqlQuery).to.equal(jsonToGraphQLQuery(expectedGraphQLJsonObject, { pretty: true }));
    });

    it('should create a graphql query using the properties from a condition within a table', () => {
        const xmlFragment =
            '<table class="query-table" data-context-object-type="User" data-context-object-path="xtremSystem.user.query.edges" data-context-filter="[]" data-context-list-order="{&quot;lastName&quot;:&quot;ascending&quot;,&quot;firstName&quot;:&quot;ascending&quot;,&quot;email&quot;:&quot;ascending&quot;}" data-alias="OtwqqBvk"><thead class="query-table-head"><tr class="query-table-row"><td class="query-table-cell" style="background-color:#0000001a;border:1px solid #000000;padding:2px;vertical-align:top;"><p>Last name</p></td><td class="query-table-cell" style="background-color:#0000001a;border:1px solid #000000;padding:2px;vertical-align:top;"><p>First name</p></td><td class="query-table-cell"><p>Admin</p></td><td class="query-table-cell" style="background-color:#0000001a;border:1px solid #000000;padding:2px;vertical-align:top;"><p>Email</p></td></tr></thead><tbody class="query-table-body"><!--{{#each OtwqqBvk.user.query.edges}}{{#with node}}--><tr class="query-table-row"><td class="query-table-cell" style="border:1px solid #000000;padding:2px;vertical-align:top;"><p><span class="property" data-property-display-label="Last name" data-property-data-type="String" data-property-name="lastName" data-property-data-format="" data-property-parent-context="User">{{lastName}}</span></p></td><td class="query-table-cell" style="border:1px solid #000000;padding:2px;vertical-align:top;"><p><span class="property" data-property-display-label="First name" data-property-data-type="String" data-property-name="firstName" data-property-data-format="" data-property-parent-context="User">{{firstName}}</span></p></td><td class="query-table-cell"><section class="conditional-block" data-context-condition="[{&quot;conjunction&quot;:&quot;and&quot;,&quot;valueType1&quot;:&quot;property&quot;,&quot;valueType2&quot;:&quot;constant&quot;,&quot;_id&quot;:&quot;1&quot;,&quot;value1&quot;:{&quot;label&quot;:&quot;Administrator&quot;,&quot;data&quot;:{&quot;name&quot;:&quot;isAdministrator&quot;,&quot;title&quot;:&quot;Administrator&quot;,&quot;canSort&quot;:true,&quot;canFilter&quot;:true,&quot;type&quot;:&quot;Boolean&quot;,&quot;isCustom&quot;:false,&quot;isMutable&quot;:false,&quot;isOnInputType&quot;:true,&quot;isOnOutputType&quot;:true,&quot;enumType&quot;:null,&quot;dataType&quot;:&quot;&quot;,&quot;targetNode&quot;:&quot;&quot;,&quot;isCollection&quot;:false,&quot;kind&quot;:&quot;SCALAR&quot;,&quot;label&quot;:&quot;Administrator&quot;,&quot;node&quot;:&quot;Boolean&quot;,&quot;iconType&quot;:&quot;csv&quot;},&quot;id&quot;:&quot;isAdministrator&quot;,&quot;key&quot;:&quot;isAdministrator&quot;,&quot;labelKey&quot;:&quot;Administrator&quot;,&quot;labelPath&quot;:&quot;Administrator&quot;},&quot;value2&quot;:&quot;true&quot;,&quot;key&quot;:&quot;1&quot;,&quot;operator&quot;:&quot;equals&quot;}]"><!--{{#if ( eq isAdministrator true )}}--><div class="conditional-block-body">ADMIN!!</div><!--{{/if}}--><div class="conditional-block-footer">&nbsp;</div></section></td><td class="query-table-cell" style="border:1px solid #000000;padding:2px;vertical-align:top;"><p><span class="property" data-property-display-label="Email" data-property-data-type="String" data-property-name="email" data-property-data-format="" data-property-parent-context="User">{{email}}</span></p></td></tr><tr class="query-table-row" data-hidden="1"><td class="query-table-cell" colspan="4"><p>&nbsp;</p></td></tr><!--{{/with}}{{/each}}--><tr class="query-table-row" data-hidden="1"><td class="query-table-cell" colspan="4"><p>&nbsp;</p></td></tr></tbody><tfoot class="query-table-footer"><tr class="query-table-row"><td class="query-table-cell"><p>&nbsp;</p></td><td class="query-table-cell"><p>&nbsp;</p></td><td class="query-table-cell"><p>&nbsp;</p></td><td class="query-table-cell"><p>&nbsp;</p></td></tr></tfoot></table>';

        const expectedGraphQLJsonObject = {
            query: {
                OtwqqBvk: {
                    __aliasFor: 'xtremSystem',
                    user: {
                        query: {
                            __args: {
                                filter: '{}',
                                orderBy: '{"lastName":1,"firstName":1,"email":1}',
                            },
                            edges: {
                                node: {
                                    _id: true,
                                    lastName: true,
                                    firstName: true,
                                    isAdministrator: true,
                                    email: true,
                                },
                            },
                        },
                    },
                },
            },
        };
        const graphqlQuery = generateGraphQLQueryFromHTML(xmlFragment);
        expect(graphqlQuery).to.equal(jsonToGraphQLQuery(expectedGraphQLJsonObject, { pretty: true }));
    });

    it('should create a graphql query using the filter with range date parameters', () => {
        const xmlFragment =
            '<table class="query-table" data-context-object-type="ShowCaseProduct" data-context-object-path="xtremShowCase.showCaseProduct.query.edges" data-context-filter="[{&quot;_id&quot;:&quot;1&quot;,&quot;label&quot;:&quot;Ending date&quot;,&quot;filterType&quot;:&quot;inRange&quot;,&quot;filterValue&quot;:&quot;startDateParam~endDateParam&quot;,&quot;data&quot;:{&quot;name&quot;:&quot;endingDate&quot;,&quot;title&quot;:&quot;Ending date&quot;,&quot;canSort&quot;:true,&quot;canFilter&quot;:true,&quot;type&quot;:&quot;Date&quot;,&quot;isCustom&quot;:false,&quot;isMutable&quot;:false,&quot;isOnInputType&quot;:true,&quot;isOnOutputType&quot;:true,&quot;enumType&quot;:null,&quot;dataType&quot;:&quot;&quot;,&quot;targetNode&quot;:&quot;&quot;,&quot;isCollection&quot;:false,&quot;kind&quot;:&quot;SCALAR&quot;,&quot;label&quot;:&quot;Ending date&quot;,&quot;iconType&quot;:&quot;csv&quot;},&quot;id&quot;:&quot;endingDate&quot;,&quot;labelPath&quot;:&quot;endingDate&quot;,&quot;property&quot;:{&quot;name&quot;:&quot;endingDate&quot;,&quot;title&quot;:&quot;Ending date&quot;,&quot;canSort&quot;:true,&quot;canFilter&quot;:true,&quot;type&quot;:&quot;Date&quot;,&quot;isCustom&quot;:false,&quot;isMutable&quot;:false,&quot;isOnInputType&quot;:true,&quot;isOnOutputType&quot;:true,&quot;enumType&quot;:null,&quot;dataType&quot;:&quot;&quot;,&quot;targetNode&quot;:&quot;&quot;,&quot;isCollection&quot;:false,&quot;kind&quot;:&quot;SCALAR&quot;,&quot;label&quot;:&quot;Ending date&quot;,&quot;iconType&quot;:&quot;csv&quot;,&quot;id&quot;:&quot;endingDate&quot;,&quot;data&quot;:{&quot;name&quot;:&quot;endingDate&quot;,&quot;title&quot;:&quot;Ending date&quot;,&quot;canSort&quot;:true,&quot;canFilter&quot;:true,&quot;type&quot;:&quot;Date&quot;,&quot;isCustom&quot;:false,&quot;isMutable&quot;:false,&quot;isOnInputType&quot;:true,&quot;isOnOutputType&quot;:true,&quot;enumType&quot;:null,&quot;dataType&quot;:&quot;&quot;,&quot;targetNode&quot;:&quot;&quot;,&quot;isCollection&quot;:false,&quot;kind&quot;:&quot;SCALAR&quot;,&quot;label&quot;:&quot;Ending date&quot;,&quot;iconType&quot;:&quot;csv&quot;},&quot;key&quot;:&quot;endingDate&quot;,&quot;labelKey&quot;:&quot;endingDate&quot;,&quot;labelPath&quot;:&quot;endingDate&quot;},&quot;parameter&quot;:true}]" data-context-list-order="{&quot;endingDate&quot;:&quot;ascending&quot;,&quot;netPrice&quot;:&quot;ascending&quot;,&quot;product&quot;:&quot;ascending&quot;}" data-alias="IvQkSbSR"><thead class="query-table-head"><tr class="query-table-row"><td class="query-table-cell" style="background-color:#0000001a;border:1px solid #000000;padding:2px;vertical-align:top;"><p>Ending date</p></td><td class="query-table-cell" style="background-color:#0000001a;border:1px solid #000000;padding:2px;vertical-align:top;"><p>Net price</p></td><td class="query-table-cell" style="background-color:#0000001a;border:1px solid #000000;padding:2px;vertical-align:top;"><p>Product</p></td></tr></thead><tbody class="query-table-body"><!--{{#each IvQkSbSR.showCaseProduct.query.edges}}{{#with node}}--><tr class="query-table-row"><td class="query-table-cell" style="border:1px solid #000000;padding:2px;vertical-align:top;"><p><span class="property" data-property-display-label="Ending date" data-property-data-type="Date" data-property-name="endingDate" data-property-data-format="FullDate" data-property-parent-context="ShowCaseProduct">{{formatDate endingDate \'FullDate\'}}</span></p></td><td class="query-table-cell e-right-align" style="border:1px solid #000000;padding:2px;vertical-align:top;"><p><span class="property" data-property-display-label="Net price" data-property-data-type="Decimal" data-property-name="netPrice" data-property-data-format="2" data-property-parent-context="ShowCaseProduct">{{formatNumber netPrice 2}}</span></p></td><td class="query-table-cell" style="border:1px solid #000000;padding:2px;vertical-align:top;"><p><span class="property" data-property-display-label="Product" data-property-data-type="String" data-property-name="product" data-property-data-format="" data-property-parent-context="ShowCaseProduct">{{product}}</span></p></td></tr><tr class="query-table-row" data-hidden="1"><td class="query-table-cell" colspan="3"><p>&nbsp;</p></td></tr><!--{{/with}}{{/each}}--><tr class="query-table-row" data-hidden="1"><td class="query-table-cell" colspan="3"><p>&nbsp;</p></td></tr></tbody><tfoot class="query-table-footer"><tr class="query-table-row"><td class="query-table-cell"><p>&nbsp;</p></td><td class="query-table-cell"><p>&nbsp;</p></td><td class="query-table-cell"><p>&nbsp;</p></td></tr></tfoot></table>';

        const expectedGraphQLJsonObject = {
            query: {
                IvQkSbSR: {
                    __aliasFor: 'xtremShowCase',
                    showCaseProduct: {
                        query: {
                            __args: {
                                filter: '{"endingDate":{"_and":[{"_gte":"{{startDateParam}}","_lte":"{{endDateParam}}"}]}}',
                                orderBy: '{"endingDate":1,"netPrice":1,"product":1}',
                            },
                            edges: {
                                node: {
                                    _id: true,
                                    endingDate: true,
                                    netPrice: true,
                                    product: true,
                                },
                            },
                        },
                    },
                },
            },
        };
        const graphqlQuery = generateGraphQLQueryFromHTML(xmlFragment);
        expect(graphqlQuery).to.equal(jsonToGraphQLQuery(expectedGraphQLJsonObject, { pretty: true }));
    });

    it('should create a graphql query using a contains parameter filter on a string property', () => {
        const xmlFragment =
            '<table class="query-table" data-context-object-type="ShowCaseProduct" data-context-object-path="xtremShowCase.showCaseProduct.query.edges" data-context-filter="[{&quot;_id&quot;:&quot;1&quot;,&quot;label&quot;:&quot;Product&quot;,&quot;filterType&quot;:&quot;contains&quot;,&quot;filterValue&quot;:&quot;test&quot;,&quot;data&quot;:{&quot;name&quot;:&quot;product&quot;,&quot;title&quot;:&quot;Product&quot;,&quot;canSort&quot;:true,&quot;canFilter&quot;:true,&quot;type&quot;:&quot;String&quot;,&quot;isCustom&quot;:false,&quot;isMutable&quot;:false,&quot;isOnInputType&quot;:true,&quot;isOnOutputType&quot;:true,&quot;enumType&quot;:null,&quot;dataType&quot;:&quot;descriptionDataType&quot;,&quot;targetNode&quot;:&quot;&quot;,&quot;isCollection&quot;:false,&quot;kind&quot;:&quot;SCALAR&quot;,&quot;label&quot;:&quot;Product&quot;,&quot;iconType&quot;:&quot;csv&quot;},&quot;id&quot;:&quot;product&quot;,&quot;labelPath&quot;:&quot;product&quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Product&quot;,&quot;data&quot;:{&quot;name&quot;:&quot;product&quot;,&quot;title&quot;:&quot;Product&quot;,&quot;canSort&quot;:true,&quot;canFilter&quot;:true,&quot;type&quot;:&quot;String&quot;,&quot;isCustom&quot;:false,&quot;isMutable&quot;:false,&quot;isOnInputType&quot;:true,&quot;isOnOutputType&quot;:true,&quot;enumType&quot;:null,&quot;dataType&quot;:&quot;descriptionDataType&quot;,&quot;targetNode&quot;:&quot;&quot;,&quot;isCollection&quot;:false,&quot;kind&quot;:&quot;SCALAR&quot;,&quot;label&quot;:&quot;Product&quot;,&quot;iconType&quot;:&quot;csv&quot;},&quot;id&quot;:&quot;product&quot;,&quot;key&quot;:&quot;product&quot;,&quot;labelKey&quot;:&quot;Product&quot;,&quot;labelPath&quot;:&quot;product&quot;},&quot;parameter&quot;:true}]" data-context-list-order="{&quot;product&quot;:&quot;ascending&quot;,&quot;email&quot;:&quot;ascending&quot;}" data-alias="MjSRXShs"><thead class="query-table-head"><tr class="query-table-row"><td class="query-table-cell" style="background-color:#0000001a;border:1px solid #000000;padding:2px;vertical-align:top;"><p>Product</p></td><td class="query-table-cell" style="background-color:#0000001a;border:1px solid #000000;padding:2px;vertical-align:top;"><p>Email</p></td></tr></thead><tbody class="query-table-body"><!--{{#each MjSRXShs.showCaseProduct.query.edges}}{{#with node}}--><tr class="query-table-row"><td class="query-table-cell" style="border:1px solid #000000;padding:2px;vertical-align:top;"><p><span class="property" data-property-display-label="Product" data-property-data-type="String" data-property-name="product" data-property-data-format="" data-property-parent-context="ShowCaseProduct">{{product}}</span></p></td><td class="query-table-cell" style="border:1px solid #000000;padding:2px;vertical-align:top;"><p><span class="property" data-property-display-label="Email" data-property-data-type="String" data-property-name="email" data-property-data-format="" data-property-parent-context="ShowCaseProduct">{{email}}</span></p></td></tr><tr class="query-table-row" data-hidden="1"><td class="query-table-cell" colspan="2"><p>&nbsp;</p></td></tr><!--{{/with}}{{/each}}--><tr class="query-table-row" data-hidden="1"><td class="query-table-cell" colspan="2"><p>&nbsp;</p></td></tr></tbody><tfoot class="query-table-footer"><tr class="query-table-row"><td class="query-table-cell"><p>&nbsp;</p></td><td class="query-table-cell"><p>&nbsp;</p></td></tr></tfoot></table>';

        const expectedGraphQLJsonObject = {
            query: {
                MjSRXShs: {
                    __aliasFor: 'xtremShowCase',
                    showCaseProduct: {
                        query: {
                            __args: {
                                filter: '{"product":{"_and":[{"_regex":"{{test__ESCAPED}}","_options":"i"}]}}',
                                orderBy: '{"product":1,"email":1}',
                            },
                            edges: {
                                node: {
                                    _id: true,
                                    product: true,
                                    email: true,
                                },
                            },
                        },
                    },
                },
            },
        };
        const graphqlQuery = generateGraphQLQueryFromHTML(xmlFragment);
        expect(graphqlQuery).to.equal(jsonToGraphQLQuery(expectedGraphQLJsonObject, { pretty: true }));
    });
});
