import { Test } from '@sage/xtrem-core';
import { expect } from 'chai';
import {
    createDictionaryForRendering,
    extractTranslatableContent,
    restoreTranslationContent,
} from '../../../lib/functions/translation-utils';

const originalFixture1 = `<div class="entry">
    <h1>Users By Type</h1>
    <hr class="sep">
    <h2>Application Users</h2>
    {{#if codeBlockResult.applicationUsersCount}}
        <table>
            <thead>
                <tr><th>First Name</th>
                <th>Last Name</th>
                <th>Email</th>
            </tr></thead>
            <tbody>
                {{#each codeBlockResult.applicationUsers}}
                    <tr>
                        <td>{{node.firstName}}</td>
                        <td>{{node.lastName}}</td>
                        <td>{{node.email}}</td>
                    </tr>
                {{/each}}
            </tbody>
        </table>
        Application user count: {{length codeBlockResult.applicationUsers}}<br>
    {{else}}
        No application users found<br>
    {{/if}}
    <br>

    {{#if codeBlockResult.systemUsersIncluded}}
        <hr class="sep">
        <h2>System Users</h2>
        {{#if codeBlockResult.systemUsersCount}}
            <table>
                <thead>
                    <tr><th>First Name</th>
                    <th>Last Name</th>
                    <th>Email</th>
                </tr></thead>
                <tbody>
                    {{#each codeBlockResult.systemUsers}}
                    <tr>
                        <td>{{node.firstName}}</td>
                        <td>{{node.lastName}}</td>
                        <td>{{node.email}}</td>
                    </tr>
                    {{/each}}
                </tbody>
            </table>
            System user count: {{length codeBlockResult.systemUsers}}<br>
        {{else}}
            No system users found<br>
        {{/if}}
        <br>
    {{/if}}

</div>`;

const replacedFixture2 = `<div class="entry">
    <h1>{{ translatedContent "d69164791ae1e56b0e6bfa4d22ac6d8f" }}</h1>
    <hr class="sep">
    <h2>{{ translatedContent "2e94a7f4d1eeea33306d080897172fe0" }}</h2>
    {{#if codeBlockResult.applicationUsersCount}}
        <table>
            <thead>
                <tr><th>{{ translatedContent "bc910f8bdf70f29374f496f05be0330c" }}</th>
                <th>{{ translatedContent "77587239bf4c54ea493c7033e1dbf636" }}</th>
                <th>{{ translatedContent "ce8ae9da5b7cd6c3df2929543a9af92d" }}</th>
            </tr></thead>
            <tbody>
                {{#each codeBlockResult.applicationUsers}}
                    <tr>
                        <td>{{node.firstName}}</td>
                        <td>{{node.lastName}}</td>
                        <td>{{node.email}}</td>
                    </tr>
                {{/each}}
            </tbody>
        </table>
        {{ translatedContent "0b6241818df6179d365252a67f593f3e" }} {{length codeBlockResult.applicationUsers}}<br>
    {{else}}
        {{ translatedContent "cf8e64031b758760236af8f7a47d4ae0" }}<br>
    {{/if}}
    <br>

    {{#if codeBlockResult.systemUsersIncluded}}
        <hr class="sep">
        <h2>{{ translatedContent "2f87402c222b566606a0e1bd36a20ae9" }}</h2>
        {{#if codeBlockResult.systemUsersCount}}
            <table>
                <thead>
                    <tr><th>{{ translatedContent "bc910f8bdf70f29374f496f05be0330c" }}</th>
                    <th>{{ translatedContent "77587239bf4c54ea493c7033e1dbf636" }}</th>
                    <th>{{ translatedContent "ce8ae9da5b7cd6c3df2929543a9af92d" }}</th>
                </tr></thead>
                <tbody>
                    {{#each codeBlockResult.systemUsers}}
                    <tr>
                        <td>{{node.firstName}}</td>
                        <td>{{node.lastName}}</td>
                        <td>{{node.email}}</td>
                    </tr>
                    {{/each}}
                </tbody>
            </table>
            {{ translatedContent "e19d1aadd73b572c7cac6250e9e482ac" }} {{length codeBlockResult.systemUsers}}<br>
        {{else}}
            {{ translatedContent "f1385fbdcbbef78debddf6da40bf6c94" }}<br>
        {{/if}}
        <br>
    {{/if}}

</div>`;

const originalFixture3 = `<!DOCTYPE html PUBLIC "" -//W3C//DTD XHTML 1.0 Transitional//EN"" ""
http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd"">
<html xmlns="" http://www.w3.org/1999/xhtml"" xmlns:v="" urn:schemas-microsoft-com:vml"" xmlns:o=""
urn:schemas-microsoft-com:office:office"">

<head>
<meta http-equiv="" Content-Type"" content="" text/html; charset=utf-8"">
<title>Xtrem</title>
<!-- Brand_EmailTemplate_V5.7.2 -->

<!--<link rel=""icon"" type=""image/x-icon"" href=""https://www.sage.com/favicon.ico"">
<link rel=""shortcut icon"" type=""image/x-icon"" href=""https://www.sage.com/favicon.ico"">-->

<!--[if gte mso 9]>
<xml>
    <o:OfficeDocumentSettings>
        <o:AllowPNG/>
        <o:PixelsPerInch>96</o:PixelsPerInch>
    </o:OfficeDocumentSettings>
</xml>
<style>
    li {
        text-indent: -1em; /* Normalise space between bullets and text */
    }
</style>
<![endif]-->

<!--[if (gte mso 9)|(IE)]>
<style type=""text/css"">
    table {
        border-collapse: collapse;
        border-spacing: 0;
        mso-line-height-rule: exactly;
        mso-margin-bottom-alt: 0;
        mso-margin-top-alt: 0;
        mso-table-lspace: 0pt;
        mso-table-rspace: 0pt;
    }
</style>
<![endif]-->

<!--[if gte mso 9]>
<xml>
    <o:OfficeDocumentSettings>
        <o:AllowPNG/>
        <o:PixelsPerInch>96</o:PixelsPerInch>
    </o:OfficeDocumentSettings>
</xml>
<![endif]-->
<!--[if gt mso 15]>
<style type=""text/css"" media=""all"">
    /* Outlook 2016 Height Fix */
    table, tr, td {
        border-collapse: collapse;
    }

    tr {
        font-size: 0px;
        line-height: 0px;
        border-collapse: collapse;
    }
</style>
<![endif]-->

</head>

<body>
    <p>This is a template!</p>
    <!-- <div>This is a commented item</div> -->
</body>

</html>`;

const fixtureDictionary1 = {
    '0b6241818df6179d365252a67f593f3e': 'Application user count:',
    cf8e64031b758760236af8f7a47d4ae0: 'No application users found',
    e19d1aadd73b572c7cac6250e9e482ac: 'System user count:',
    f1385fbdcbbef78debddf6da40bf6c94: 'No system users found',
    d69164791ae1e56b0e6bfa4d22ac6d8f: 'Users By Type',
    '2e94a7f4d1eeea33306d080897172fe0': 'Application Users',
    bc910f8bdf70f29374f496f05be0330c: 'First Name',
    '77587239bf4c54ea493c7033e1dbf636': 'Last Name',
    ce8ae9da5b7cd6c3df2929543a9af92d: 'Email',
    '2f87402c222b566606a0e1bd36a20ae9': 'System Users',
};

export const originalFixture2 = `<div>
{{#if codeBlockResult.isInvoiceDraft}}
<div class="draft">
{{else if xtremSales.salesInvoice.query.edges.0.node.isPrinted}}
<div class="duplicata">
{{else}}
<div>
{{/if}}
{{#with xtremSales.salesInvoice.query.edges.0.node}}
<table class="report-container">
    <thead class="report-header">
        <tr>
            <th class="normal-black">
                <div class="header-info" style="position:relative;top:-8px;">
                    <table>
                        <tbody>
                            <tr>
                                <td class="column-left">
                                    <strong>{{salesSite.name}}</strong><br>
                                    {{#with salesSiteAddress}}
                                    {{addressLine1}}<br>
                                    {{#if addressLine2}}{{addressLine2}}<br>{{/if}}
                                    {{postcode}} {{city}} {{region}}<br>
                                    {{country.name}}
                                    {{/with}}
                                    {{#if salesSite.siret}}<br>SIRET: {{salesSite.siret}}{{/if}}
                                    {{#if salesSite.taxIdNumber}}<br>N° de TVA: {{salesSite.taxIdNumber}}{{/if}}
                                </td>
                                <td class="column-right">
                                    <div style="position:relative;top:-20px;"><h1>Facture</h1><br></div>
                                    <div style="text-align:left;padding-left:300px;position:relative;top:-15px;">
                                        Numéro: <strong>{{number}}</strong><br>
                                        Date de facture: <strong>{{invoiceDate}}</strong>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </th>
        </tr>
    </thead>
    <tfoot class="report-footer">
        <tr>
            <td class="report-footer-cell">
                <div class="footer-info">
                    <table class="header-table">
                        <tbody>
                            <tr>
                                <td class="column-center">
                                    <strong>{{#if salesSite.legalCompany.legalForm}}<span style="text-transform:uppercase;">{{salesSite.legalCompany.legalForm}}</span> {{/if}}{{salesSite.legalCompany.name}}</strong>{{#if salesSite.legalCompany.rcs}} {{salesSite.legalCompany.rcs}}{{/if}}{{#if salesSite.legalCompany.naf}} APE: {{salesSite.legalCompany.naf}}{{/if}}<br>
                                    {{#with salesSite.legalCompany.addresses.query.edges.0.node}}
                                    {{addressLine1}} {{addressLine2}} {{postcode}} {{city}} {{region}} {{country.name}}
                                    {{/with}}
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </td>
        </tr>
    </tfoot>
    <tbody class="report-content">
        <tr>
            <td class="report-content-cell">
                <div class="main">
                    <div class="address-frame">
                    </div>
                    <div>
                        <div style="position:relative;top:-10px;">
                            <table>
                                <thead>
                                    <tr>
                                        <th class="column-left">Adresse de livraison</th>
                                        <th class="column-left">Adresse de facturation</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td style="width:60%">
                                            <strong>{{billToCustomer.name}}</strong>
                                            <br>
                                            {{#with lines.query.edges.0.node.consumptionDirectAddress}}
                                            {{addressLine1}}<br>
                                            {{#if addressLine2}}{{addressLine2}}<br>{{/if}}
                                            {{postcode}} {{city}} {{region}}<br>
                                            {{country.name}}
                                            {{/with}}
                                        </td>
                                        <td>
                                            <strong>{{billToCustomer.name}}</strong>
                                            <br>
                                            {{#with billToAddressDetail}}
                                            {{addressLine1}}<br>
                                            {{#if addressLine2}}{{addressLine2}}<br>{{/if}}
                                            {{postcode}} {{city}} {{region}}<br>
                                            {{country.name}}
                                            {{/with}}
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div style="position:relative;top:10px;">
                            <p style="text-align:left">
                                Client: <strong>{{billToCustomer.name}}</strong> ID: {{billToCustomer.id}}
                                {{#if billToCustomer.businessEntity.siret}}<br>SIRET: {{billToCustomer.businessEntity.siret}}{{/if}}
                                {{#if billToCustomer.businessEntity.taxIdNumber}}<br>N° de TVA: {{billToCustomer.businessEntity.taxIdNumber}}{{/if}}
                            </p>
                        </div>
                        <div style="position:relative;top:5px;"><br></div>
                        <div class="avoidBreakInside bottomSpace">
                            <table class="lines-table">
                                <thead>
                                    <tr>
                                        <th class="column-left">Commande</th>
                                        <th class="column-left">Livraison</th>
                                        <th class="column-left">Article</th>
                                        <th class="column-left">Description</th>
                                        <th class="column-right">Quantité</th>
                                        <th class="column-left">Unité</th>
                                        <th class="column-right">Prix net</th>
                                        <th class="column-right">HT</th>
                                        <th class="column-right">Taux TVA</th>
                                        <th class="column-right">TTC</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {{#each lines.query.edges}}
                                    <tr>
                                        <td class="column-left">
                                            {{node.salesOrderLines.query.edges.0.node.linkedDocument.document.number}}
                                        </td>
                                        <td class="column-left">
                                            {{node.salesShipmentLines.query.edges.0.node.linkedDocument.document.number}}</td>
                                        <td class="column-left">{{node.itemId}}</td>
                                        <td class="column-left">{{node.itemDescription}}</td>
                                        <td class="column-right">{{toFixed node.quantity 2}}</td>
                                        <td class="column-left">{{node.unit.name}}</td>
                                        <td class="column-right">{{toFixed node.netPrice 2}} {{../currency.symbol}}</td>
                                        <td class="column-right">{{toFixed node.lineAmountExcludingTax 2}} {{../currency.symbol}}</td>
                                        <td class="column-right">{{toFixed node.taxes.query.edges.0.node.taxRate 2}}%</td>
                                        <td class="column-right">{{toFixed node.lineAmountIncludingTax 2}} {{../currency.symbol}}</td>
                                    </tr>
                                    {{/each}}
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="article">
                        <table class="header-table">
                            <tbody>
                                <tr>
                                    <td class="column-left">
                                        <p>
                                            Pas d'escompte pour paiment anticipé<br>
                                            Taux de pénalité de retard en vigueur à la date de facture<br>
                                            Indemnité forfaitaire de recouvrement de 40 euros
                                        </p>
                                        <table class="lines-table">
                                            <thead>
                                                <tr>
                                                    <th class="column-left">Conditions de règlement</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td class="column-left">
                                                        Conditions de paiement: {{paymentTerm.name}}<br>
                                                        <strong>Date d'échéance: {{dueDate}}</strong>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </td>
                                    <td class="column-right">
                                        <table class="header-table">
                                            <thead>
                                                <tr>
                                                    <th class="column-left">Taxe</th>
                                                    <th class="column-left">Base</th>
                                                    <th class="column-left">Taux TVA</th>
                                                    <th class="column-left">Montant</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {{#each taxes.query.edges}}
                                                <tr>
                                                    <td class="column-left">{{node.tax}}</td>
                                                    <td class="column-right">{{toFixed node.taxableAmount 2}} {{../currency.symbol}}</td>
                                                    <td class="column-right">{{toFixed node.taxRate 2}}%</td>
                                                    <td class="column-right">{{toFixed node.taxAmount 2}} {{../currency.symbol}}</td>
                                                </tr>
                                                <tr>
                                                    <td colspan="4" class="column-left">{{node.taxReference.legalMention}}</td>
                                                </tr>
                                                {{/each}}
                                            </tbody>
                                        </table>
                                        <table class="header-table">
                                            <thead>
                                                <tr>
                                                    <th class="column-left">Total HT</th>
                                                    <th class="column-left">Montant TVA</th>
                                                    <th class="column-left">Total TTC</th>
                                                    <th class="column-left">Net à payer</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td class="column-right">{{toFixed totalAmountExcludingTax 2}} {{currency.symbol}}</td>
                                                    <td class="column-right">{{toFixed totalTaxAmount 2}} {{currency.symbol}}</td>
                                                    <td class="column-right">{{toFixed totalAmountIncludingTax 2}} {{currency.symbol}}</td>
                                                    <td class="column-right"><strong>{{toFixed totalAmountIncludingTax 2}} {{currency.symbol}}</strong></td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </td>
        </tr>
    </tbody>
</table>
{{/with}}
</div></div></div></div>`;

const fixtureDictionary2 = {
    '0268802e96f7499bc2e54cb7e337bc48': 'Montant TVA',
    '02d4bdb3b1fd9ea1528143b7911853bb': 'Facture',
    '095a1b43effec73955e31e790438de49': 'Base',
    '0bcef9c45bd8a48eda1b26eb0c61c869': '%',
    '1a2342d59a674037f626c4e6dd0370ac': 'Date de facture:',
    '1b27b371c9e7d0298eab533bffae53b7': 'Quantité',
    '1dfced8f8484229c96e6ec4c5fe64b8d': 'Adresse de facturation',
    '20c86479a2e1b7770cdbdc90bd4699dd': 'Taxe',
    '2c74ca5f79748296d53dbcba569971e6': 'Conditions de paiement:',
    '3005cddb89d57c53c227c3321054cc9b': 'Adresse de livraison',
    '34fe79bd6b57e1756a71f79b2e14c91d': 'Prix net',
    '4dd4ce8750a4d8d7172be85adc3e6068': 'Montant',
    '5e2f8ee473fdebc99fef4dc9e7ee3146': 'Article',
    '5edf57714612f84deedd39a0e719d2d5': "Date d'échéance:",
    '6710008b3e0608a0b6da4f00c4ab010f': 'N° de TVA:',
    '7137a85d9b8d9e6d09f47767d2ca31ee': 'APE:',
    '776a4f1f391ad4e2173fd4647c442783': 'Total TTC',
    '77efa457f4180b1b64c51a38736515e8': 'Livraison',
    '7958ff7ff538d18386061bc7f68ef364': 'Total HT',
    '7a97243fd867ffecf2ae3400392dc884': 'Client:',
    '87efcd44b7972a33b064dbcba8860461': 'Commande',
    '90d64eeba8247d656ef6b4800ec0f52f': 'HT',
    '9605d8eb1f6e6c6d6496f295e41eb7e4': 'Net à payer',
    '9fde04c6bde5b5174e39e7e58893b80b': 'Indemnité forfaitaire de recouvrement de 40 euros',
    ad066e822a9ea6b8e785095af5d8154e: 'Conditions de règlement',
    b067574dc6a0c09ede2e3373fcd65544: 'Numéro:',
    b5a7adde1af5c87d7fd797b6245c2a39: 'Description',
    cb23d8a8eaadf4ad4be679d9261e507f: 'Taux TVA',
    d04e2c1b67f3ef0d475409516b812e8b: 'SIRET:',
    e0f0b0564d3d29a93fad7a4178b7b1ca: 'ID:',
    e5d142a96712cbc2fe29e3e90eba6129: "Pas d'escompte pour paiment anticipé",
    ec36488d2babebbeb796e601dca6bb4c: 'Taux de pénalité de retard en vigueur à la date de facture',
    ece0971cbecbb2fa54d0cb09a08fcf52: 'Unité',
    f01390a0b796aab97beed915df1cdd63: 'TTC',
};

describe('translation utils', () => {
    describe('fixture 1', () => {
        it('should create a dictionary of hashes and string entries from the document', () =>
            Test.withContext(() => {
                const result = extractTranslatableContent(originalFixture1);
                expect(result.extractedContent).to.deep.eq(fixtureDictionary1);
            }));

        it('should replace string literals in the content', () =>
            Test.withContext(() => {
                const result = extractTranslatableContent(originalFixture1);
                expect(result.content).to.eq(replacedFixture2);
            }));

        it('should restore translations from dictionary', () => {
            const result = restoreTranslationContent(replacedFixture2, fixtureDictionary1);
            expect(result).to.eq(originalFixture1);
        });
    });

    describe('fixture 2', () => {
        it('should create a dictionary of hashes and string entries from the document', () =>
            Test.withContext(() => {
                const result = extractTranslatableContent(originalFixture2);
                expect(result.extractedContent).to.deep.eq(fixtureDictionary2);
            }));

        it('should replace string literals in the content', () =>
            Test.withContext(() => {
                const result = extractTranslatableContent(originalFixture1);
                expect(result.content).to.eq(replacedFixture2);
            }));
    });

    describe('fixture 3', () => {
        it('should create a dictionary of hashes and string entries from the document excluding comments and metadata', () =>
            Test.withContext(() => {
                const result = extractTranslatableContent(originalFixture3);
                expect(result.extractedContent).to.deep.eq({
                    '63bce4d5b289f60e1e4a31005c8941b2': 'Xtrem',
                    '96bca8205d132bce364899df4f770730': 'This is a template!',
                });
            }));
    });

    describe('assemble translations for report generation', () => {
        const dictionary = createDictionaryForRendering(
            'es_ES',
            'en_US',
            [
                {
                    hash: '1',
                    locale: 'es_ES',
                    text: 'Uno',
                },
                {
                    hash: '1',
                    locale: 'en_US',
                    text: 'One',
                },
                {
                    hash: '1',
                    locale: 'en_GB',
                    text: 'One',
                },
                {
                    hash: '2',
                    locale: 'en_US',
                    text: 'Two',
                },
                {
                    hash: '3',
                    locale: 'en_GB',
                    text: 'British Three',
                },
            ],
            {
                '1': 'one',
                '2': 'two',
                '3': 'three',
                '4': 'four',
            },
        );

        it('should find translation in the target language if available', () => {
            expect(dictionary['1']).to.eq('Uno');
        });

        it('should use fallback locales if a translation in the target language is not available', () => {
            expect(dictionary['2']).to.eq('Two');
            expect(dictionary['3']).to.eq('British Three');
        });

        it('should fall back to the base set if no translation found', () => {
            expect(dictionary['4']).to.eq('four');
        });
    });
});
