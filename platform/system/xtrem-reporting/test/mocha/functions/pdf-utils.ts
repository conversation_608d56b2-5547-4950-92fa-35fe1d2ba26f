import { Context, Test } from '@sage/xtrem-core';
import { assert, expect } from 'chai';
import * as parsePdf from 'pdf-parse';
import * as Sinon from 'sinon';
import * as xtremReporting from '../../../index';

describe('pdf utils', () => {
    let requestCount = 0;
    let executeGraphqlStub: Sinon.SinonStub;

    before(() => {
        executeGraphqlStub = Sinon.stub(Context.prototype, 'executeGraphql');
    });

    after(() => {
        executeGraphqlStub.restore();
    });

    it('should turn HTML document into PDF binary stream', () =>
        Test.withContext(async context => {
            const result = await xtremReporting.functions.generatePdfData({
                context,
                reportName: 'testReport',
                reportObject: {
                    instance: null,
                    name: 'testReport',
                    settings: [],
                },
                populatedBodyContent: '<html><head></head><body><h1>Hi there!</h1></body></html>',
                paperFormat: 'a4',
                pageOrientation: 'portrait',
            });
            expect(result).to.be.instanceOf(<PERSON>uff<PERSON>);
        }));
    it('should turn HTML document into PDF binary stream and call post processing operations if declared on the report', () =>
        Test.withContext(
            async context => {
                executeGraphqlStub.callsFake((query: string) => {
                    requestCount += 1;

                    assert.equal(
                        query,
                        'mutation { xtremReporting { prePostProcessingOperation { postProcess (report: "#pre-processing-test-report") } } }',
                    );
                    return Promise.resolve({
                        xtremReporting: {
                            prePostProcessingOperation: {
                                postProcess: true,
                            },
                        },
                    });
                });

                const result = await xtremReporting.functions.generatePdfData({
                    context,
                    reportName: 'pre-processing-test-report',
                    reportObject: {
                        instance: await context.read(xtremReporting.nodes.Report, {
                            _id: '#pre-processing-test-report',
                        }),
                        name: 'pre-processing-test-report',
                        settings: [
                            {
                                variables: {
                                    report: '#pre-processing-test-report',
                                },
                                locale: 'en_US',
                            },
                        ],
                    },
                    populatedBodyContent: '<html><head></head><body><h1>Hi there!</h1></body></html>',
                    paperFormat: 'a4',
                    pageOrientation: 'portrait',
                });
                expect(result).to.be.instanceOf(Buffer);
                assert.equal(requestCount, 1);
            },
            { testActiveServiceOptions: [xtremReporting.serviceOptions.reportAssignment] },
        ));

    it('should generate a valid PDF binary stream', () =>
        Test.withContext(async context => {
            const generatedPdfBinaryContent = await xtremReporting.functions.generatePdfData({
                context,
                reportName: 'testReport',
                reportObject: {
                    instance: null,
                    name: 'testReport',
                    settings: [],
                },
                populatedBodyContent: '<html><head></head><body><h1>Hi there!</h1></body></html>',
                paperFormat: 'a4',
                pageOrientation: 'portrait',
            });
            const parsedPdf = await parsePdf(generatedPdfBinaryContent);
            // If the document is invalid parsePdf would throw an error.
            expect(parsedPdf.numpages).to.eq(1);
        }));

    it('should generate a PDF document with the text content from the HTML document', () =>
        Test.withContext(async context => {
            const generatedPdfBinaryContent = await xtremReporting.functions.generatePdfData({
                context,
                reportName: 'testReport',
                reportObject: {
                    instance: null,
                    name: 'testReport',
                    settings: [],
                },
                populatedBodyContent: '<html><head></head><body><h1>Hi there!</h1></body></html>',
                paperFormat: 'a4',
                pageOrientation: 'portrait',
            });
            const parsedPdf = await parsePdf(generatedPdfBinaryContent);
            expect(parsedPdf.text.trim()).to.eq('Hi there!');
        }));
});
