import { BinaryStream, Test } from '@sage/xtrem-core';
import { DatePresetFormat } from '@sage/xtrem-date-time';
import { expect } from 'chai';
import { populateTemplateWithVariables } from '../../../lib/functions/template-utils';
import { ReportResource } from '../../../lib/nodes/report-resource';
import { setup } from './setup';

const imageSource =
    '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';

setup();
describe('template utils (handlebars integration)', () => {
    it('should build basic template', () =>
        Test.withContext(async context => {
            expect(
                await populateTemplateWithVariables(context, 'testReport', 'Hi {{user.name}}!', {
                    user: { name: 'John Doe' },
                }),
            ).to.eq('Hi John Doe!');
        }));

    it('should integrate helper functions', () =>
        Test.withContext(async context => {
            expect(
                await populateTemplateWithVariables(context, 'testReport', 'The result is {{multiply a b}}.', {
                    a: 2,
                    b: 3,
                }),
            ).to.eq('The result is 6.');
            expect(
                await populateTemplateWithVariables(context, 'testReport', 'The result is {{toFixed a b}}.', {
                    a: '2.1233324',
                    b: 3,
                }),
            ).to.eq('The result is 2.123.');
            expect(
                await populateTemplateWithVariables(context, 'testReport', 'The result is {{toFixed a b}}.', {
                    a: '2.1',
                    b: 3,
                }),
            ).to.eq('The result is 2.100.');
        }));

    describe('enumValues helper function', () => {
        it('should resolve enum values using the custom `enumValues` helper function', () =>
            Test.withContext(async context => {
                expect(
                    await populateTemplateWithVariables(
                        context,
                        'testReport',
                        "The resolved enum value is {{enumValue '@sage/xtrem-reporting/ReportResourceType' myValue}}.",
                        { myValue: 'fontType' },
                    ),
                ).to.eq('The resolved enum value is Font type.');
            }));
        it('should return an empty string if the enum value is null', () =>
            Test.withContext(async context => {
                expect(
                    await populateTemplateWithVariables(
                        context,
                        'testReport',
                        "The resolved enum value is {{enumValue '@sage/xtrem-reporting/ReportResourceType' myValue}}.",
                        { myValue: null },
                    ),
                ).to.eq('The resolved enum value is .');
            }));

        it('should return the literal if enum cannot be resolved', () =>
            Test.withContext(async context => {
                expect(
                    await populateTemplateWithVariables(
                        context,
                        'testReport',
                        "The resolved enum value is {{enumValue '@sage/xtrem-reporting/ReportResourceType' myValue}}.",
                        { myValue: 'invalidValue' },
                    ),
                ).to.eq('The resolved enum value is invalidValue.');
            }));
    });

    describe('each aggregators', () => {
        it('should be calculating right', () =>
            Test.withContext(async context => {
                expect(
                    await populateTemplateWithVariables(
                        context,
                        'testReport',
                        "{{#each xtremPackage.node.query.edges 'node.prop1' 'sum' 'node.prop2' 'average'}}{{#if @last}}{{aggregatedData.node.prop1.sum}} {{aggregatedData.node.prop2.average}}{{/if}}{{/each}}",
                        {
                            xtremPackage: {
                                node: {
                                    query: {
                                        edges: [
                                            {
                                                node: {
                                                    prop1: '1',
                                                    prop2: '2',
                                                },
                                            },
                                            {
                                                node: {
                                                    prop1: '3',
                                                    prop2: '5',
                                                },
                                            },
                                        ],
                                    },
                                },
                            },
                        },
                    ),
                ).to.eq('4 3.5');
            }));

        it('should calculate aggregations for printBreakIfPropertyWillChange', () =>
            Test.withContext(async context => {
                const result = await populateTemplateWithVariables(
                    context,
                    'testReport',
                    `{{#each xtremPackage.node.query.edges 'node.prop1' 'sum' 'node.prop2' 'average'}}
                            {{node.prop1}}
                            {{#printBreakIfPropertyWillChange 'node.category' 'node.prop1' 'sum' 'node.prop2' 'average'}}
                                CAT {{node.category}}: SUM: {{blockAggregatedData.node.prop1.sum}} AVG: {{blockAggregatedData.node.prop2.average}} 
                            {{/printBreakIfPropertyWillChange}}
                        {{/each}}`,
                    {
                        xtremPackage: {
                            node: {
                                query: {
                                    edges: [
                                        {
                                            node: {
                                                prop1: '1',
                                                prop2: '2',
                                                category: '1',
                                            },
                                        },
                                        {
                                            node: {
                                                prop1: '3',
                                                prop2: '5',
                                                category: '1',
                                            },
                                        },
                                        {
                                            node: {
                                                prop1: '5',
                                                prop2: '5',
                                                category: '2',
                                            },
                                        },
                                        {
                                            node: {
                                                prop1: '5',
                                                prop2: '5',
                                                category: '2',
                                            },
                                        },
                                        {
                                            node: {
                                                prop1: '5',
                                                prop2: '5',
                                                category: '3',
                                            },
                                        },
                                    ],
                                },
                            },
                        },
                    },
                );
                expect(result).to.contain('CAT 1: SUM: 4 AVG: 3.5');
                expect(result).to.contain('CAT 2: SUM: 10 AVG: 5');
                expect(result).to.contain('CAT 3: SUM: 5 AVG: 5');
            }));

        it('should calculate aggregations for printBreakIfPropertyWillChange with rolling totals', () =>
            Test.withContext(async context => {
                const result = await populateTemplateWithVariables(
                    context,
                    'testReport',
                    `{{#each xtremPackage.node.query.edges 'node.prop1' 'sum' 'node.prop2' 'average'}}
                            {{node.prop1}}
                            {{#printBreakIfPropertyWillChange 'node.category' 'node.prop1' 'sum' 'node.prop2' 'average'}}
                                CAT {{node.category}}: SUM: {{_blockAggregatedData.node.prop1.sum}} AVG: {{_blockAggregatedData.node.prop2.average}} ROLLING TOTAL: {{_blockAggregatedRollingData.node.prop1.sum}} 
                            {{/printBreakIfPropertyWillChange}}
                        {{/each}}`,
                    {
                        xtremPackage: {
                            node: {
                                query: {
                                    edges: [
                                        {
                                            node: {
                                                prop1: '1',
                                                prop2: '2',
                                                category: '1',
                                            },
                                        },
                                        {
                                            node: {
                                                prop1: '3',
                                                prop2: '5',
                                                category: '1',
                                            },
                                        },
                                        {
                                            node: {
                                                prop1: '5',
                                                prop2: '5',
                                                category: '2',
                                            },
                                        },
                                        {
                                            node: {
                                                prop1: '5',
                                                prop2: '5',
                                                category: '2',
                                            },
                                        },
                                        {
                                            node: {
                                                prop1: '5',
                                                prop2: '5',
                                                category: '3',
                                            },
                                        },
                                    ],
                                },
                            },
                        },
                    },
                );
                expect(result).to.contain('CAT 1: SUM: 4 AVG: 3.5 ROLLING TOTAL: 4');
                expect(result).to.contain('CAT 2: SUM: 10 AVG: 5 ROLLING TOTAL: 14');
                expect(result).to.contain('CAT 3: SUM: 5 AVG: 5 ROLLING TOTAL: 19');
            }));
    });

    describe('formatNumber helper function', () => {
        it('should return the number formatted according "es_ES" locale', () => {
            const scale = 2;
            const locale = 'es_ES';
            return Test.withContext(async context => {
                expect(
                    await populateTemplateWithVariables(
                        context,
                        'testReport',
                        'The formatted value is {{formatNumber myValue scale}}.',
                        {
                            myValue: 1000,
                            scale,
                        },
                        locale,
                    ),
                ).to.eq('The formatted value is 1000,00.');
            });
        });

        it('should return the number formatted according "es_ES" locale when the value is undefined', () => {
            const scale = 2;
            const locale = 'es_ES';
            return Test.withContext(async context => {
                expect(
                    await populateTemplateWithVariables(
                        context,
                        'testReport',
                        'The formatted value is {{formatNumber myValue scale}}.',
                        {
                            scale,
                        },
                        locale,
                    ),
                ).to.eq('The formatted value is 0,00.');
            });
        });

        it('should return the number formatted according "de_DE" locale when the value is undefined', () => {
            const scale = 2;
            const locale = 'de_DE';
            return Test.withContext(async context => {
                expect(
                    await populateTemplateWithVariables(
                        context,
                        'testReport',
                        'The formatted value is {{formatNumber myValue scale}}.',
                        {
                            scale,
                        },
                        locale,
                    ),
                ).to.eq('The formatted value is 0,00.');
            });
        });
        it('should return the number formatted according "en_US" locale', () =>
            Test.withContext(async context => {
                const scale = 2;
                const locale = 'en_US';
                expect(
                    await populateTemplateWithVariables(
                        context,
                        'testReport',
                        'The formatted value is {{formatNumber myValue scale}}.',
                        {
                            myValue: 1000,
                            scale,
                        },
                        locale,
                    ),
                ).to.eq('The formatted value is 1,000.00.');
            }));
        it('should return maximum number of decimal digits', () =>
            Test.withContext(async context => {
                const scale = 2;
                const locale = 'en_US';
                expect(
                    await populateTemplateWithVariables(
                        context,
                        'testReport',
                        'The formatted value is {{formatNumber myValue scale}}.',
                        {
                            myValue: 1000.567,
                            scale,
                        },
                        locale,
                    ),
                ).to.eq('The formatted value is 1,000.57.');
            }));
    });

    describe('formatDate helper function', () => {
        it('should return the date formatted as FullDate', () => {
            const format: DatePresetFormat = 'FullDate';
            const locale = 'es_ES';
            return Test.withContext(async context => {
                expect(
                    await populateTemplateWithVariables(
                        context,
                        'testReport',
                        'The formatted value is {{formatDate myValue format}}.',
                        {
                            myValue: new Date('2022-01-01'),
                            format,
                        },
                        locale,
                    ),
                ).to.eq('The formatted value is 01/01/2022.');
            });
        });
        it('should return the date formatted as LongMonth', () => {
            const format: DatePresetFormat = 'LongMonth';
            const locale = 'en_US';
            return Test.withContext(async context => {
                expect(
                    await populateTemplateWithVariables(
                        context,
                        'testReport',
                        'The formatted value is {{formatDate myValue format}}.',
                        {
                            myValue: new Date('2019-07-22'),
                            format,
                        },
                        locale,
                    ),
                ).to.eq('The formatted value is July.');
            });
        });
        it('should return the date formatted as MonthDay', () => {
            const format: DatePresetFormat = 'MonthDay';
            const locale = 'es_ES';
            return Test.withContext(async context => {
                expect(
                    await populateTemplateWithVariables(
                        context,
                        'testReport',
                        'The formatted value is {{formatDate myValue format}}.',
                        {
                            myValue: '2022-07-02',
                            format,
                        },
                        locale,
                    ),
                ).to.eq('The formatted value is 02/07.');
            });
        });
        it('should return the date formatted as LongMonthYear', () => {
            const format: DatePresetFormat = 'LongMonthYear';
            const locale = 'es_ES';
            return Test.withContext(async context => {
                expect(
                    await populateTemplateWithVariables(
                        context,
                        'testReport',
                        'The formatted value is {{formatDate myValue format}}.',
                        {
                            myValue: '2022-07-02',
                            format,
                        },
                        locale,
                    ),
                ).to.eq('The formatted value is Julio 2022.');
            });
        });
        it('should return the date formatted as MonthYear', () => {
            const format: DatePresetFormat = 'MonthYear';
            const locale = 'es_ES';
            return Test.withContext(async context => {
                expect(
                    await populateTemplateWithVariables(
                        context,
                        'testReport',
                        'The formatted value is {{formatDate myValue format}}.',
                        {
                            myValue: '2022-07-02',
                            format,
                        },
                        locale,
                    ),
                ).to.eq('The formatted value is 07/2022.');
            });
        });

        it('should should not crash if the date is null', () => {
            const format: DatePresetFormat = 'LongMonth';
            const locale = 'en_US';
            return Test.withContext(async context => {
                expect(
                    await populateTemplateWithVariables(
                        context,
                        'testReport',
                        'The formatted value is {{formatDate myValue format}}.',
                        {
                            myValue: null,
                            format,
                        },
                        locale,
                    ),
                ).to.eq('The formatted value is .');
            });
        });
    });

    describe('containsString helper function', () => {
        it('should evaluate true if variable contains literal', () =>
            Test.withContext(async context => {
                expect(
                    await populateTemplateWithVariables(
                        context,
                        'testReport',
                        "Evaluation result {{#if (containsString 'es' myValue) }}CONTENT{{/if}}.",
                        { myValue: 'test' },
                    ),
                ).to.eq('Evaluation result CONTENT.');
            }));

        it('should evaluate false if variable does not contain literal', () =>
            Test.withContext(async context => {
                expect(
                    await populateTemplateWithVariables(
                        context,
                        'testReport',
                        "Evaluation result {{#if (containsString 'es' myValue) }}CONTENT{{/if}}.",
                        { myValue: 'something' },
                    ),
                ).to.eq('Evaluation result .');
            }));
    });

    describe('startsWithString helper function', () => {
        it('should evaluate true if variable starts with literal', () =>
            Test.withContext(async context => {
                expect(
                    await populateTemplateWithVariables(
                        context,
                        'testReport',
                        "Evaluation result {{#if (startsWithString 'te' myValue) }}CONTENT{{/if}}.",
                        { myValue: 'test' },
                    ),
                ).to.eq('Evaluation result CONTENT.');
            }));

        it('should evaluate false if variable does not start with literal', () =>
            Test.withContext(async context => {
                expect(
                    await populateTemplateWithVariables(
                        context,
                        'testReport',
                        "Evaluation result {{#if (startsWithString 'te' myValue) }}CONTENT{{/if}}.",
                        { myValue: 'something' },
                    ),
                ).to.eq('Evaluation result .');
            }));
    });

    describe('endsWithString helper function', () => {
        it('should evaluate true if variable ends with literal', () =>
            Test.withContext(async context => {
                expect(
                    await populateTemplateWithVariables(
                        context,
                        'testReport',
                        "Evaluation result {{#if (endsWithString 'st' myValue) }}CONTENT{{/if}}.",
                        { myValue: 'test' },
                    ),
                ).to.eq('Evaluation result CONTENT.');
            }));

        it('should evaluate false if variable does not ends with literal', () =>
            Test.withContext(async context => {
                expect(
                    await populateTemplateWithVariables(
                        context,
                        'testReport',
                        "Evaluation result {{#if (endsWithString 'st' myValue) }}CONTENT{{/if}}.",
                        { myValue: 'something' },
                    ),
                ).to.eq('Evaluation result .');
            }));
    });

    it('should resolve static resources', () =>
        Test.withContext(async context => {
            const resouceNode = await context.create(ReportResource, {
                content: BinaryStream.fromBuffer(Buffer.from(imageSource, 'base64')),
                mimetype: 'image/svg+xml',
                name: 'testResource',
                type: 'image',
            });

            await resouceNode.$.save();

            const locale = 'en_US';

            const result = await populateTemplateWithVariables(
                context,
                'testReport',
                'This is an image: <img src="{{reportResource \'testResource\'}}" alt="a very good image" />',
                {},
                locale,
                false,
                {},
            );
            expect(
                result.startsWith(
                    'This is an image: <img src="data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiIHN0YW5',
                ),
            ).to.eq(true);
        }));
});
