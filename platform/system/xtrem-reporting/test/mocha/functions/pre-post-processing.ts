import * as xtremCommunication from '@sage/xtrem-communication';
import { Context, Test } from '@sage/xtrem-core';
import * as xtremMetadata from '@sage/xtrem-metadata';
import { assert } from 'chai';
import 'chai-as-promised';
import * as Sinon from 'sinon';
import * as xtremReporting from '../../../index';

describe('pre-post-processing', () => {
    let requestCount = 0;
    let executeGraphqlStub: Sinon.SinonStub;

    beforeEach(() => {
        executeGraphqlStub = Sinon.stub(Context.prototype, 'executeGraphql');
    });

    afterEach(() => {
        executeGraphqlStub.restore();
        requestCount = 0;
    });

    describe('extractManageableSettingsFromPreProcessingOperationResult', () => {
        it('should extract manageable settings correctly', () => {
            const operationResult = { watermark: 'test' };
            const result =
                xtremReporting.functions.extractManageableSettingsFromPreProcessingOperationResult(operationResult);
            assert.deepEqual(result, { watermark: 'test' });
        });

        it('should not return non-manageable settings', () => {
            const operationResult = { watermark: 'test', nonManageable: 'value' };
            const result =
                xtremReporting.functions.extractManageableSettingsFromPreProcessingOperationResult(operationResult);
            assert.deepEqual(result, { watermark: 'test' });
        });
    });

    describe('operationDetailsToGraphqlBaseBody', () => {
        it('should generate correct GraphQL body', () => {
            const operationDetails: Omit<xtremReporting.functions.OperationDetails, 'instance' | 'phase' | 'title'> = {
                package: 'testPackage',
                node: 'testNode',
                name: 'testName',
                isNotAsync: true,
                type: 'mutation',
            };
            const result = xtremReporting.functions.buildGraphqlBody({
                operationDetails,
            });
            assert.equal(result, `mutation { testPackage { testNode { testName } } }`);
        });

        it('should include operationType in the generated body', () => {
            const operationDetails: Omit<xtremReporting.functions.OperationDetails, 'instance' | 'phase' | 'title'> = {
                package: 'testPackage',
                node: 'testNode',
                name: 'testName',
                isNotAsync: true,
                type: 'mutation',
            };
            const result = xtremReporting.functions.buildGraphqlBody({ operationDetails, operationType: 'query' });
            assert.equal(result, 'query { testPackage { testNode { testName } } }');
        });

        it('should include operationAction in the generated body', () => {
            const operationDetails: Omit<xtremReporting.functions.OperationDetails, 'instance' | 'phase' | 'title'> = {
                package: 'testPackage',
                node: 'testNode',
                name: 'testName',
                isNotAsync: false,
                type: 'mutation',
            };
            const result = xtremReporting.functions.buildGraphqlBody({
                operationDetails,
                operationAction: 'testAction',
            });
            assert.equal(result, 'mutation { testPackage { testNode { testName { testAction } } } }');
        });
    });

    describe('populateOperationParametersAccordingToSignature', () => {
        it('should populate parameters according to signature', async () => {
            await Test.withContext(async context => {
                const operationDetails = {
                    instance: await context.read(xtremMetadata.nodes.MetaNodeOperation, {
                        _id: '#PrePostProcessingOperation|preProcess|',
                    }),
                };
                const settings = {
                    variables: {
                        report: 'testReport',
                    },
                    locale: context.currentLocale,
                } as xtremReporting.functions.ReportGenerationSettings;
                const result = await xtremReporting.functions.populateOperationParametersAccordingToSignature(
                    operationDetails,
                    settings,
                );
                assert.equal(result, 'report: "testReport"');
            });
        });

        it('should populate parameters according to signature including error in post processing', async () => {
            await Test.withContext(async context => {
                const operationDetails = {
                    instance: await context.read(xtremMetadata.nodes.MetaNodeOperation, {
                        _id: '#PrePostProcessingOperation|postProcess|',
                    }),
                };
                const settings = {
                    variables: {
                        report: 'testReport',
                    },
                    locale: context.currentLocale,
                } as xtremReporting.functions.ReportGenerationSettings;
                const result = await xtremReporting.functions.populateOperationParametersAccordingToSignature(
                    operationDetails,
                    settings,
                    {
                        error: new Error('test error'),
                    },
                );
                assert.equal(result, 'report: "testReport", error: {"message":"test error"}');
            });
        });

        it("should not return parameters not present in the operation's signature", async () => {
            await Test.withContext(async context => {
                const operationDetails = {
                    instance: await context.read(xtremMetadata.nodes.MetaNodeOperation, {
                        _id: '#PrePostProcessingOperation|preProcess|',
                    }),
                };
                const settings = {
                    variables: {
                        nonExistentParam: 'value',
                    },
                    locale: context.currentLocale,
                } as xtremReporting.functions.ReportGenerationSettings;
                const result = await xtremReporting.functions.populateOperationParametersAccordingToSignature(
                    operationDetails,
                    settings,
                );
                assert.equal(result, undefined);
            });
        });
    });

    describe('populateGraphQLReturnSelectorAccordingToSignature', () => {
        it('should build a GraphQL selector for the result field of an async mutation according to its signature', async () => {
            await Test.withContext(async context => {
                const operationDetails = {
                    instance: await context.read(xtremMetadata.nodes.MetaNodeOperation, {
                        _id: '#PrePostProcessingOperation|preProcess|',
                    }),
                    isNotAsync: true,
                };

                const selector =
                    await xtremReporting.functions.populateGraphQLResultSelectorAccordingToSignature(operationDetails);

                assert.equal(selector, '{watermark}');
            });
        });
    });

    describe('runClassicOperation', () => {
        it('should run async operation and return result', async () => {
            await Test.withContext(
                async context => {
                    const args: Parameters<typeof xtremReporting.functions.runClassicOperation>[1] = {
                        operationDetails: {
                            package: 'xtremReporting',
                            node: 'prePostProcessingOperation',
                            name: 'preProcess',
                            instance: await context.read(xtremMetadata.nodes.MetaNodeOperation, {
                                _id: '#PrePostProcessingOperation|preProcess|',
                            }),
                            isNotAsync: true,
                            type: 'mutation',
                            phase: 'preProcessing' as xtremReporting.functions.OperationDetails['phase'],
                            title: 'preProcess',
                        },
                        settings: {} as xtremReporting.functions.ReportGenerationSettings,
                    };

                    executeGraphqlStub.callsFake((query: string) => {
                        requestCount += 1;

                        assert.equal(
                            query,
                            'mutation { xtremReporting { prePostProcessingOperation { preProcess {watermark} } } }',
                        );
                        return Promise.resolve({
                            xtremReporting: {
                                prePostProcessingOperation: {
                                    preProcess: {
                                        test: true,
                                    },
                                },
                            },
                        });
                    });

                    let result: any;
                    const promise = async () => {
                        result = await xtremReporting.functions.runClassicOperation(context, args);
                    };

                    await assert.isFulfilled(promise());
                    assert.isObject(result);
                    assert.deepEqual(result, { test: true });
                    assert.equal(requestCount, 1);
                },
                { testActiveServiceOptions: [xtremReporting.serviceOptions.reportAssignment] },
            );
        });

        it('should rethrow the error if executeGraphql fails', async () => {
            await Test.withContext(
                async context => {
                    const args: Parameters<typeof xtremReporting.functions.runClassicOperation>[1] = {
                        operationDetails: {
                            package: 'xtremReporting',
                            node: 'prePostProcessingOperation',
                            name: 'preProcess',
                            instance: await context.read(xtremMetadata.nodes.MetaNodeOperation, {
                                _id: '#PrePostProcessingOperation|preProcess|',
                            }),
                            isNotAsync: true,
                            type: 'mutation',
                            phase: 'preProcessing' as xtremReporting.functions.OperationDetails['phase'],
                            title: 'preProcess',
                        },
                        settings: {} as xtremReporting.functions.ReportGenerationSettings,
                    };

                    executeGraphqlStub.callsFake(() => {
                        throw new Error('Fake GraphQL execution failed');
                    });

                    await assert.isRejected(
                        xtremReporting.functions.runClassicOperation(context, args),
                        Error,
                        'Fake GraphQL execution failed',
                    );
                },
                { testActiveServiceOptions: [xtremReporting.serviceOptions.reportAssignment] },
            );
        });
    });

    describe('startAsyncOperation', () => {
        it('should start async operation and return tracking ID', async () => {
            await Test.withContext(
                async context => {
                    const args: Parameters<typeof xtremReporting.functions.startAsyncOperation>[1] = {
                        operationDetails: {
                            package: 'xtremReporting',
                            node: 'prePostProcessingOperation',
                            name: 'preProcessAsync',
                            isNotAsync: false,
                            type: 'mutation',
                            phase: 'preProcessing' as xtremReporting.functions.OperationDetails['phase'],
                            title: 'preProcess',
                        },
                    };

                    executeGraphqlStub.callsFake((query: string) => {
                        assert.equal(
                            query,
                            'mutation { xtremReporting { prePostProcessingOperation { preProcessAsync { start { trackingId } } } } }',
                        );
                        return Promise.resolve({
                            xtremReporting: {
                                prePostProcessingOperation: {
                                    preProcessAsync: {
                                        start: {
                                            trackingId: 'testTrackingID',
                                        },
                                    },
                                },
                            },
                        });
                    });

                    const result = await xtremReporting.functions.startAsyncOperation(context, args);
                    assert.isString(result);
                },
                {
                    testActiveServiceOptions: [
                        xtremReporting.serviceOptions.reportAssignment,
                        xtremReporting.serviceOptions.asyncPerPostProcessing,
                    ],
                },
            );
        });

        it('should throw an error if GraphQL execution fails', async () => {
            await Test.withContext(
                async context => {
                    const args: Parameters<typeof xtremReporting.functions.startAsyncOperation>[1] = {
                        operationDetails: {
                            package: 'testPackage',
                            node: 'testNode',
                            name: 'testName',
                            isNotAsync: false,
                            type: 'mutation',
                            phase: 'preProcessing' as xtremReporting.functions.OperationDetails['phase'],
                            title: 'testName',
                        },
                    };

                    executeGraphqlStub.callsFake(() => {
                        throw new Error('Fake GraphQL execution failed');
                    });

                    await assert.isRejected(
                        xtremReporting.functions.startAsyncOperation(context, args),
                        Error,
                        'Fake GraphQL execution failed',
                    );
                },
                {
                    testActiveServiceOptions: [
                        xtremReporting.serviceOptions.reportAssignment,
                        xtremReporting.serviceOptions.asyncPerPostProcessing,
                    ],
                },
            );
        });
    });

    describe('waitForAsyncOperationCompletion', () => {
        it('should wait for async operation completion and return result', async () => {
            await Test.withContext(
                async context => {
                    const args: Parameters<typeof xtremReporting.functions.waitForAsyncOperationCompletion>[1] = {
                        trackingId: 'testTrackingID',
                        operationDetails: {
                            package: 'xtremReporting',
                            node: 'prePostProcessingOperation',
                            name: 'preProcessAsync',
                            isNotAsync: false,
                            type: 'mutation',
                            phase: 'preProcessing' as xtremReporting.functions.OperationDetails['phase'],
                            title: 'preProcess',
                        },
                        resultSelector: '{ test }',
                    };

                    const fakeNotificationState = await context.create(xtremCommunication.nodes.SysNotificationState, {
                        notificationId: 'testTrackingID',
                        status: 'running',
                        logs: [
                            {
                                level: 'info',
                                message: 'test message',
                            },
                        ],
                    });
                    await fakeNotificationState.$.save();

                    let result: any;
                    const promise = async () => {
                        const resultPromise = xtremReporting.functions.waitForAsyncOperationCompletion(context, args);

                        await new Promise(resolve => {
                            setTimeout(resolve, 200);
                        }).then(async () => {
                            await fakeNotificationState.$.update({
                                status: 'success',
                                result: { test: true },
                            });
                            await fakeNotificationState.$.save();
                        });

                        result = await resultPromise;
                    };

                    await assert.isFulfilled(promise());
                    assert.isObject(result);
                    assert.deepEqual(result, { test: true });
                },
                {
                    testActiveServiceOptions: [
                        xtremReporting.serviceOptions.reportAssignment,
                        xtremReporting.serviceOptions.asyncPerPostProcessing,
                    ],
                },
            );
        });

        it('should throw an error if operation status is error', async () => {
            await Test.withContext(
                async context => {
                    const args: Parameters<typeof xtremReporting.functions.waitForAsyncOperationCompletion>[1] = {
                        trackingId: 'testTrackingID',
                        operationDetails: {
                            package: 'testPackage',
                            node: 'testNode',
                            name: 'testName',
                            isNotAsync: false,
                            type: 'mutation',
                            phase: 'preProcessing' as xtremReporting.functions.OperationDetails['phase'],
                            title: 'testName',
                        },
                        resultSelector: '{ test }',
                    };

                    const fakeNotificationState = await context.create(xtremCommunication.nodes.SysNotificationState, {
                        notificationId: 'testTrackingID',
                        status: 'error',
                        message: 'Operation failed tracking testName operation.',
                        logs: [
                            {
                                level: 'info',
                                message: 'test message',
                            },
                        ],
                    });
                    await fakeNotificationState.$.save();

                    await assert.isRejected(
                        xtremReporting.functions.waitForAsyncOperationCompletion(context, args),
                        Error,
                        'Operation failed tracking testName operation.',
                    );
                },
                {
                    testActiveServiceOptions: [
                        xtremReporting.serviceOptions.reportAssignment,
                        xtremReporting.serviceOptions.asyncPerPostProcessing,
                    ],
                },
            );
        });
    });

    describe('runAsyncOperation', () => {
        it('should run async operation and return result', async () => {
            await Test.withContext(
                async context => {
                    const args: Parameters<typeof xtremReporting.functions.runAsyncOperation>[1] = {
                        operationDetails: {
                            package: 'xtremReporting',
                            node: 'prePostProcessingOperation',
                            name: 'preProcessAsync',
                            instance: await context.read(xtremMetadata.nodes.MetaNodeOperation, {
                                _id: '#PrePostProcessingOperation|preProcessAsync|start',
                            }),
                            isNotAsync: false,
                            type: 'mutation',
                            phase: 'preProcessing' as xtremReporting.functions.OperationDetails['phase'],
                            title: 'preProcess',
                        },
                        settings: {} as xtremReporting.functions.ReportGenerationSettings,
                    };

                    executeGraphqlStub.callsFake((query: string) => {
                        requestCount += 1;

                        assert.equal(
                            query,
                            'mutation { xtremReporting { prePostProcessingOperation { preProcessAsync { start { trackingId } } } } }',
                        );
                        return Promise.resolve({
                            xtremReporting: {
                                prePostProcessingOperation: {
                                    preProcessAsync: {
                                        start: {
                                            trackingId: 'testTrackingID',
                                        },
                                    },
                                },
                            },
                        });
                    });

                    const fakeNotificationState = await context.create(xtremCommunication.nodes.SysNotificationState, {
                        notificationId: 'testTrackingID',
                        status: 'success',
                        result: { test: true },
                        logs: [
                            {
                                level: 'info',
                                message: 'test message',
                            },
                        ],
                    });
                    await fakeNotificationState.$.save();

                    let result: any;
                    const promise = async () => {
                        result = await xtremReporting.functions.runAsyncOperation(context, args);
                    };

                    await assert.isFulfilled(promise());
                    assert.isObject(result);
                    assert.deepEqual(result, { test: true });
                    assert.equal(requestCount, 1);
                },
                {
                    testActiveServiceOptions: [
                        xtremReporting.serviceOptions.reportAssignment,
                        xtremReporting.serviceOptions.asyncPerPostProcessing,
                    ],
                },
            );
        });

        it('should rethrow the error if startAsyncOperation fails', async () => {
            await Test.withContext(
                async context => {
                    const args: Parameters<typeof xtremReporting.functions.runAsyncOperation>[1] = {
                        operationDetails: {
                            package: 'xtremReporting',
                            node: 'prePostProcessingOperation',
                            name: 'preProcess',
                            instance: await context.read(xtremMetadata.nodes.MetaNodeOperation, {
                                _id: '#PrePostProcessingOperation|preProcessAsync|start',
                            }),
                            isNotAsync: false,
                            type: 'mutation',
                            phase: 'preProcessing' as xtremReporting.functions.OperationDetails['phase'],
                            title: 'preProcess',
                        },
                        settings: {} as xtremReporting.functions.ReportGenerationSettings,
                    };

                    executeGraphqlStub.callsFake(() => {
                        throw new Error('Fake GraphQL execution failed');
                    });

                    await assert.isRejected(
                        xtremReporting.functions.runAsyncOperation(context, args),
                        Error,
                        'Fake GraphQL execution failed',
                    );
                },
                {
                    testActiveServiceOptions: [
                        xtremReporting.serviceOptions.reportAssignment,
                        xtremReporting.serviceOptions.asyncPerPostProcessing,
                    ],
                },
            );
        });

        it('should skip operation processing if asyncPerPostProcessing service option is not active', async () => {
            await Test.withContext(async context => {
                const args: Parameters<typeof xtremReporting.functions.runAsyncOperation>[1] = {
                    operationDetails: {
                        package: 'xtremReporting',
                        node: 'prePostProcessingOperation',
                        name: 'preProcess',
                        instance: await context.read(xtremMetadata.nodes.MetaNodeOperation, {
                            _id: '#PrePostProcessingOperation|preProcessAsync|start',
                        }),
                        isNotAsync: false,
                        type: 'mutation',
                        phase: 'preProcessing' as xtremReporting.functions.OperationDetails['phase'],
                        title: 'preProcess',
                    },
                    settings: {} as xtremReporting.functions.ReportGenerationSettings,
                };

                executeGraphqlStub.callsFake(() => {
                    requestCount += 1;
                });

                let result: any;
                const promise = async () => {
                    result = await xtremReporting.functions.runAsyncOperation(context, args);
                };

                await assert.isFulfilled(promise());
                assert.equal(requestCount, 0);
                assert.isTrue(result); // if called mutation should return { watermark: 'PreProcessingOperation' }
            });
        });
    });

    describe('getOperationDetails', () => {
        it('should get operation details', async () => {
            await Test.withContext(async context => {
                const operation = await context.read(xtremMetadata.nodes.MetaNodeOperation, {
                    _id: '#PrePostProcessingOperation|preProcess|',
                });
                const phase = 'preProcessing';
                const result = await xtremReporting.functions.getOperationDetails(operation, phase);
                assert.deepEqual(result, {
                    package: 'xtremReporting',
                    node: 'prePostProcessingOperation',
                    name: 'preProcess',
                    instance: operation,
                    isNotAsync: true,
                    type: 'mutation',
                    phase,
                    title: 'Pre Process',
                });
            });
        });
    });

    describe.skip('runPrePostProcessingOperation', () => {
        it('should run pre processing operation and return results', async () => {
            await Test.withContext(
                async context => {
                    const reportObject = {
                        instance: await context.read(xtremReporting.nodes.Report, {
                            _id: '#pre-processing-test-report',
                        }),
                        name: 'pre-processing-test-report',
                        settings: [
                            {
                                variables: {
                                    report: 'testReport',
                                },
                                locale: context.currentLocale,
                            },
                        ],
                    } as xtremReporting.functions.ReportObject;
                    const reportProcessingPhase = 'preProcessing';

                    executeGraphqlStub.callsFake(() => {
                        requestCount += 1;
                        return Promise.resolve({
                            xtremReporting: {
                                prePostProcessingOperation: {
                                    preProcess: {
                                        watermark: true,
                                    },
                                },
                            },
                        });
                    });

                    let result: any;
                    const promise = async () => {
                        result = await xtremReporting.functions.runPrePostProcessingOperation(
                            context,
                            reportObject,
                            reportProcessingPhase,
                        );
                    };

                    await assert.isFulfilled(promise());
                    assert.isArray(result);
                    assert.deepEqual(result, [{ watermark: true }]);
                    assert.equal(requestCount, 1);
                },
                { testActiveServiceOptions: [xtremReporting.serviceOptions.reportAssignment] },
            );
        });

        it('should run post processing operation and return results', async () => {
            await Test.withContext(
                async context => {
                    const reportObject = {
                        instance: await context.read(xtremReporting.nodes.Report, {
                            _id: '#pre-processing-test-report',
                        }),
                        name: 'pre-processing-test-report',
                        settings: [
                            {
                                variables: {
                                    report: 'testReport',
                                },
                                locale: context.currentLocale,
                            },
                        ],
                    } as xtremReporting.functions.ReportObject;
                    const reportProcessingPhase = 'postProcessing';

                    executeGraphqlStub.callsFake(() => {
                        requestCount += 1;

                        return Promise.resolve({
                            xtremReporting: {
                                prePostProcessingOperation: {
                                    postProcess: true,
                                },
                            },
                        });
                    });

                    let result: any;
                    const promise = async () => {
                        result = await xtremReporting.functions.runPrePostProcessingOperation(
                            context,
                            reportObject,
                            reportProcessingPhase,
                        );
                    };

                    await assert.isFulfilled(promise());
                    assert.isArray(result);
                    assert.deepEqual(result, [true]);
                    assert.equal(requestCount, 1);
                },
                { testActiveServiceOptions: [xtremReporting.serviceOptions.reportAssignment] },
            );
        });

        it('should return an empty array if no operation is provided', async () => {
            await Test.withContext(
                async context => {
                    const reportObject = {
                        instance: await context.read(xtremReporting.nodes.Report, {
                            _id: '#testReport',
                        }),
                        name: 'testReport',
                        settings: [
                            {
                                variables: {
                                    report: 'testReport',
                                },
                                locale: context.currentLocale,
                            },
                        ],
                    } as xtremReporting.functions.ReportObject;
                    const reportProcessingPhase = 'postProcessing';
                    const result = await xtremReporting.functions.runPrePostProcessingOperation(
                        context,
                        reportObject,
                        reportProcessingPhase,
                    );
                    assert.isEmpty(result);
                },
                { testActiveServiceOptions: [xtremReporting.serviceOptions.reportAssignment] },
            );
        });

        it('should return an empty array if reportAssignment service option is not active', async () => {
            await Test.withContext(async context => {
                const reportObject = {
                    instance: await context.read(xtremReporting.nodes.Report, {
                        _id: '#pre-processing-test-report',
                    }),
                    name: 'pre-processing-test-report',
                    settings: [
                        {
                            variables: {
                                report: 'testReport',
                            },
                            locale: context.currentLocale,
                        },
                    ],
                } as xtremReporting.functions.ReportObject;
                const reportProcessingPhase = 'preProcessing';

                const result = await xtremReporting.functions.runPrePostProcessingOperation(
                    context,
                    reportObject,
                    reportProcessingPhase,
                );

                assert.isEmpty(result);
            });
        });

        it('should throw an error if operation fails', async () => {
            await Test.withContext(
                async context => {
                    const reportObject = {
                        instance: await context.read(xtremReporting.nodes.Report, {
                            _id: '#pre-processing-test-report',
                        }),
                        name: 'pre-processing-test-report',
                        settings: [
                            {
                                variables: {
                                    report: 'testReport',
                                },
                                locale: context.currentLocale,
                            },
                        ],
                    } as xtremReporting.functions.ReportObject;
                    const reportProcessingPhase = 'preProcessing';

                    executeGraphqlStub.callsFake(() => {
                        throw new Error('Fake GraphQL execution failed');
                    });

                    await assert.isRejected(
                        xtremReporting.functions.runPrePostProcessingOperation(
                            context,
                            reportObject,
                            reportProcessingPhase,
                        ),
                        Error,
                        'Fake GraphQL execution failed',
                    );
                },
                { testActiveServiceOptions: [xtremReporting.serviceOptions.reportAssignment] },
            );
        });
    });
});
