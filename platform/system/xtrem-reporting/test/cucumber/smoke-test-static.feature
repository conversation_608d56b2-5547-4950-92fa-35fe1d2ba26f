@xtrem_reporting
Feature: smoke-test-static

    Scenario Outline: As a user I want to make sure that each page loads without any problems on desktop
        Given the user opens the application on a desktop using the following link: "<Page>"
        Then the "<Title>" titled page is displayed

        Examples:
            | Page                                                  | Title                         |
            | @sage/xtrem-reporting/Report                          | Reports                       |
            | @sage/xtrem-reporting/Report/eyJfaWQiOiIxIn0=         | Report activeUsersListing     |
            | @sage/xtrem-reporting/ReportTemplate                  | Report templates              |
            | @sage/xtrem-reporting/ReportTemplate/eyJfaWQiOiIxIn0= | Report template activeUsers   |
            | @sage/xtrem-reporting/TranslationImportExport         | Translation import and export |
            | @sage/xtrem-reporting/ReportStyleVariable             | Report style variables        |
            | @sage/xtrem-reporting/ReportResource                  | Report resources              |

    # Positive test to ensure that the error dialog is definietly exist and open
    Scenario: As a user I want to make sure that an error dialog appears if the page does not load on desktop
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Wazup"
        Then an error dialog appears

    # # CRUD: smoke tests share some common characteristics
    Scena<PERSON>: As a user I want to create modify and delete a new report
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-reporting/Report"
        When the user selects the "Name" labelled text field on the main page
        And the user writes "smokeReport" in the text field
        And the user selects the "Parent Package" labelled select field on the main page
        And the user writes "xtrem-reporting" in the select field
        And the user selects the "Description" labelled text area field on the main page
        And the user writes "Smoke Test" in the text area field
        And the user selects the "Variables" labelled table field on the main page
        And the user clicks the "addVariable" bound action of the table field
        And the user selects the "Variable Name" labelled text field on a modal
        And the user writes "smokeVariable" in the text field
        And the user selects the "Variable Type" labelled select field on a modal
        And the user writes "boolean" in the select field
        And the user selects "boolean" in the select field
        And the user clicks the "OK" button of the Custom dialog
        And the user waits 2 seconds
        And the user clicks the "Save" labelled business action button on the main page
        # Search
        And the user searches for "smokeReport" in the navigation panel
        And the user clicks the record with the text "noActiveTemplate" in the navigation panel
        And the user selects the "Description" labelled text area field on the main page
        Then the value of the text area field is "Smoke Test"
        And the user selects the "Name" labelled text field on the main page
        Then the value of the text field is "smokeReport"
        And the user selects the "variables" bound table field on the main page
        And the user selects the row 1 of the table field
        Then the value of the "Variable Name" labelled nested text field of the selected row in the table field is "smokeVariable"
        And the value of the "Variable Type" labelled nested select field of the selected row in the table field is "boolean"
        # modify and Update record
        When the user selects the "Name" labelled text field on the main page
        And the user writes "smokeTest" in the text field
        And the user selects the "variables" bound table field on the main page
        And the user selects the row 1 of the table field
        And the user writes "smokeTest" in the "Variable Name" labelled nested text field of the selected row in the table field
        And the user clicks the "Save" labelled business action button on the main page
        And the user selects the "Name" labelled text field on the main page
        Then the value of the text field is "smokeTest"
        And the user selects the "variables" bound table field on the main page
        And the user selects the row 1 of the table field
        Then the value of the "Variable Name" labelled nested text field of the selected row in the table field is "smokeTest"
        # Delete it
        When the user clicks the "Delete" labelled more actions button in the header
        Then the text in the header of the dialog is "Confirm deletion"
        When the user clicks the "Yes" button of the Message dialog
        And the user selects the "Name" labelled text field on the main page
        Then the value of the text field is ""

    Scenario: As a user I want to create modify and delete a new report template
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-reporting/ReportTemplate"
        When the user selects the "Name" labelled text field on the main page
        And the user writes "smokeReportTemplate" in the text field
        And the user selects the "Report" labelled reference field on the main page
        And the user writes "userDetails" in the reference field
        And the user selects the "Report" labelled reference field on the main page
        And the user selects "userDetails" in the reference field
        And selects the "Data query" labelled navigation anchor on the main page
        And the "Data query" labelled navigation anchor is selected
        And the user selects the "query" bound graphiql editor field on the main page
        And the user writes "---smoke---Data Query---" in the graphiql editor field
        And selects the "code" labelled navigation anchor on the main page
        And the "Code" labelled navigation anchor is selected
        And the "code" bound code editor field on the main page is displayed
        And the user selects the "code" bound code editor field on the main page
        And the user writes "return 'someResult';" in the code editor field
        And selects the "htmlTemplate" labelled navigation anchor on the main page
        And the user selects the "htmlTemplate" bound code editor field on the main page
        And the user writes "---smoke---HTML Template---" in the code editor field
        And selects the "Stylesheet" labelled navigation anchor on the main page
        And the user selects the "Stylesheet" labelled code editor field on the main page
        And the user writes "----smoke---styleSheet---" in the code editor field
        And the user clicks the "Save" labelled business action button on the main page
        And the user waits 2 seconds
        # preview tab
        When the user clicks the "preview" bound business action button on the main page
        And selects the "Preview" labelled navigation anchor on the main page
        Then the "Preview" labelled navigation anchor is selected
        And the "pdf" bound pdf viewer field on the main page is displayed
        When the user waits 2 seconds
        And the user clicks the Close button of the dialog on a full width modal
        And selects the "basicDetails" labelled navigation anchor on the main page
        Then the "basicDetails" labelled navigation anchor is selected
        And the user selects the "Name" labelled text field on the main page
        Then the value of the text field is "smokeReportTemplate"
        # Search and Update record
        When the user searches for "smokeReportTemplate" in the navigation panel
        And the user clicks the record with the text "smokeReportTemplate" in the navigation panel
        # Delete it
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Yes" button of the Message dialog
        And the user selects the "Name" labelled text field on the main page
        Then the value of the text field is ""
