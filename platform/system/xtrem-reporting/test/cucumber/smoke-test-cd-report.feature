@xtrem_reporting
Feature: smoke-test-cd-report

    Scenario: Create Report
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-reporting/Report"
        Then the "Report" titled page is displayed

        # Click Add <PERSON>rud <PERSON>ton
        And the user clicks the "Create" labelled business action button on the navigation panel

        # Fill in a some fields
        And the user selects the "Name" labelled text field on the main page
        And the user writes "SMOKETEST" in the text field
        And the user selects the "Parent Package" labelled select field on the main page
        And the user writes "xtrem-reporting" in the select field
        And the user selects "xtrem-reporting" in the select field
        And the user selects the "Description" labelled text area field on the main page
        And the user writes "Test description" in the text area field

        # Click Save CRUD Button
        When the user clicks the "Save" labelled business action button on the main page

        # Verify Creation
        Then the value of the toast is "Record created"

    Scenario: Delete Report
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-reporting/Report"
        Then the "Report" titled page is displayed

        # Search for the record on navigation panel
        When the user searches for "SMOKETEST" in the navigation panel
        And the user clicks the "first" navigation panel's row

        # Click Delete CRUD Button
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Yes" button of the Confirm dialog

        # Verify Deletion
        Then the value of the toast is "Report deleted"
