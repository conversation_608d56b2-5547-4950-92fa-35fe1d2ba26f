Feature: Report page

    Scenario: As a user I want to load an existing factory report and ensure its fields are disabled
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-reporting/Report"
        Then the "Reports" titled page is displayed
        When the user searches for "userDetails" in the navigation panel
        And the user clicks the record with the text "userDetails" in the navigation panel
        Then the "Report userDetails" titled page is displayed
        When the user selects the "Name" labelled text field on the main page
        Then the text field is disabled
        When the user selects the "Parent Package" labelled select field on the main page
        Then the select field is disabled
        And the "Add Variable" labelled header action button on the main page is disabled
        And the user selects the "variables" bound table field on the main page
        And the user selects the row 1 of the table field
        Then the "remove" dropdown action of the selected row in the table field is disabled
        And the value of the "Variable Name" labelled nested text field of the selected row in the table field is "_id"
        And the value of the "Variable Type" labelled nested select field of the selected row in the table field is "string"

    Scenario: As a user I want to create a new report and edit it
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-reporting/Report"
        Then the "Reports" titled page is displayed
        When the user selects the "Name" labelled text field on the main page
        And the user writes "automationReport" in the text field
        And the user selects the "Parent Package" labelled select field on the main page
        And the user writes "xtrem-reporting" in the select field
        And the user selects the "Description" labelled text area field on the main page
        And the user writes "Test description" in the text area field
        And the user selects the "Variables" labelled table field on the main page
        And the user clicks the "addVariable" bound action of the table field
        Then the text in the header of the dialog is "New report variable"
        When the user selects the "Variable Name" labelled text field on a modal
        And the user writes "myTestReportVariable" in the text field
        And the user selects the "Variable Type" labelled select field on a modal
        And the user writes "boolean" in the select field
        And the user selects "boolean" in the select field
        And the user clicks the "OK" button of the Custom dialog
        And the user selects the "variables" bound table field on the main page
        And the user selects the row 1 of the table field
        Then the value of the "Variable Name" labelled nested text field of the selected row in the table field is "myTestReportVariable"
        And the value of the "Variable Type" labelled nested select field of the selected row in the table field is "boolean"
        When the user clicks the "Save" labelled business action button on the main page
        Then the value of the toast is "Record created"
        And the user selects the "Description" labelled text area field on the main page
        Then the value of the text area field is "Test description"
        And the user selects the "Name" labelled text field on the main page
        Then the value of the text field is "automationReport"
        # clean up
        When the user clicks the "Delete" labelled more actions button in the header
        Then the text in the header of the dialog is "Confirm deletion"
        When the user clicks the "Yes" button of the Message dialog
        Then the user selects the "Name" labelled text field on the main page
        Then the value of the text field is ""

    Scenario: As a user I want to use the lookup to find an active template
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-reporting/Report"
        And the user searches for "activeTemplateReport" in the navigation panel
        And the user clicks the record with the text "activeTemplateReport" in the navigation panel
        When the user selects the "activeTemplate" bound reference field on the main page
        And the user clicks the lookup button of the reference field
        And the user selects the "activeTemplate" bound table field on a modal
        And the user selects the row 1 of the table field
        When the user clicks the "name" bound nested field of the selected row in the table field
        And the user selects the "Active template" labelled reference field on the main page
        Then the value of the reference field is "activeTemplate"

    Scenario: As a user I want to use an invalid active template
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-reporting/Report"
        And the user searches for "activeTemplateReport" in the navigation panel
        And the user clicks the record with the text "activeTemplateReport" in the navigation panel
        When the user selects the "activeTemplate" bound reference field on the main page
        And the user writes "xxxxx" in the reference field
        And the user waits 2 seconds
        And the user clicks the "Save" labelled business action button on the main page
        And the user waits 2 seconds
        And the user searches for "noActiveTemplate" in the navigation panel
        And the user clicks the record with the text "noActiveTemplate" in the navigation panel
        And the user selects the "Name" labelled text field on the main page
        Then the value of the text field is "noActiveTemplate"
        And the user selects the "activeTemplate" bound reference field on the main page
        Then the value of the reference field is ""

    Scenario: As a user I want to remove a variable table name and type
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-reporting/Report"
        When the user selects the "Name" labelled text field on the main page
        And the user writes "salesReport" in the text field
        And the user selects the "Parent Package" labelled select field on the main page
        And the user writes "xtrem-reporting" in the select field
        And the user selects the "activeTemplate" bound reference field on the main page
        And the user writes "userTemplate" in the reference field
        And the user selects the "Description" labelled text area field on the main page
        And the user writes "Test description" in the text area field
        And the user selects the "Variables" labelled table field on the main page
        And the user clicks the "addVariable" bound action of the table field
        And the user selects the "Variable Name" labelled text field on a modal
        And the user writes "true" in the text field
        And the user selects the "Variable Type" labelled select field on a modal
        And the user writes "boolean" in the select field
        And the user selects "boolean" in the select field
        And the user clicks the "OK" button of the Custom dialog
        # add row 2
        And the user selects the "Variables" labelled table field on the main page
        And the user clicks the "addVariable" bound action of the table field
        And the user selects the "Variable Name" labelled text field on a modal
        And the user writes "First Name" in the text field
        And the user selects the "Variable Type" labelled select field on a modal
        And the user writes "string" in the select field
        And the user selects "string" in the select field
        And the user clicks the "OK" button of the Custom dialog
        # add row 3
        And the user selects the "Variables" labelled table field on the main page
        And the user clicks the "addVariable" bound action of the table field
        And the user selects the "Variable Name" labelled text field on a modal
        And the user writes "_id" in the text field
        And the user selects the "Variable Type" labelled select field on a modal
        And the user writes "number" in the select field
        And the user selects "number" in the select field
        And the user clicks the "OK" button of the Custom dialog
        And the user clicks the "Save" labelled business action button on the main page
        And the user waits 2 seconds
        And the user selects the "variables" bound table field on the main page
        And the user selects the row 1 of the table field
        Then the value of the "Variable Name" labelled nested text field of the selected row in the table field is "true"
        And the value of the "Variable Type" labelled nested select field of the selected row in the table field is "boolean"
        And the user selects the row 2 of the table field
        And the value of the "Variable Name" labelled nested text field of the selected row in the table field is "First Name"
        And the value of the "Variable Type" labelled nested select field of the selected row in the table field is "string"
        And the user selects the row 3 of the table field
        And the value of the "Variable Name" labelled nested text field of the selected row in the table field is "_id"
        And the value of the "Variable Type" labelled nested select field of the selected row in the table field is "number"
        #   remove
        And the user selects the row 1 of the table field
        When the user clicks the "Remove" dropdown action of the selected row of the table field
        And the user clicks the "Save" labelled business action button on the main page
        And the user waits 2 seconds
        #  Refresh the page and verify
        And the user searches for "salesReport" in the navigation panel
        And the user clicks the record with the text "salesReport" in the navigation panel
        And the user selects the "variables" bound table field on the main page
        And the user selects the row 1 of the table field
        Then the value of the "Variable Name" labelled nested text field of the selected row in the table field is "First Name"
        And the user selects the row 2 of the table field
        And the value of the "Variable Name" labelled nested text field of the selected row in the table field is "_id"
        # clean up
        When the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Yes" button of the Message dialog
        Then the value of the toast is "Report deleted"
        And the user selects the "Name" labelled text field on the main page
        Then the value of the text field is ""

    Scenario: As a user I want to edit a variable table name and type
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-reporting/Report"
        And the user searches for "noActiveTemplate" in the navigation panel
        And the user clicks the record with the text "noActiveTemplate" in the navigation panel
        When the user selects the "Variables" labelled table field on the main page
        And the user clicks the "addVariable" bound action of the table field
        And the user selects the "Variable Name" labelled text field on a modal
        And the user writes "PO001" in the text field
        And the user selects the "Variable Type" labelled select field on a modal
        And the user writes "number" in the select field
        And the user selects "number" in the select field
        And the user clicks the "OK" button of the Custom dialog
        And the user selects the "variables" bound table field on the main page
        And the user selects the row 1 of the table field
        And the user writes "PO002" in the "Variable Name" labelled nested text field of the selected row in the table field
        And the user clicks the "Save" labelled business action button on the main page
        Then the value of the toast is "Record updated"
        When the user selects the "Variables" labelled table field on the main page
        And the user clicks the "addVariable" bound action of the table field
        And the user selects the "Variable Name" labelled text field on a modal
        And the user writes "PO002" in the text field
        And the user selects the "Variable Type" labelled select field on a modal
        And the user writes "number" in the select field
        And the user selects "number" in the select field
        And the user clicks the "OK" button of the Custom dialog
        Then the value of the toast is "This variable name is already used on this report."
        #  clean up
        And the user selects the "variables" bound table field on the main page
        And the user selects the row 1 of the table field
        And the user clicks the "Remove" dropdown action of the selected row of the table field
        And the user clicks the "Save" labelled business action button on the main page

    Scenario: As a user I want to ensure a duplicate report name is not allowed
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-reporting/Report"
        When the user selects the "Name" labelled text field on the main page
        And the user writes "activeTemplateReport" in the text field
        And the user selects the "Parent Package" labelled select field on the main page
        And the user writes "xtrem-reporting" in the select field
        And the user selects the "Description" labelled text area field on the main page
        And the user writes "Test description" in the text area field
        And the user clicks the "Save" labelled business action button on the main page
        Then the value of the toast is "This report name already exists. Rename your report and try again."
        When the user searches for "activeTemplateReport" in the navigation panel
        And the user clicks the record with the text "activeTemplateReport" in the navigation panel
        Then the text in the header of the dialog is "Unsaved changes"
        When the user clicks the "Yes" button of the Message dialog
        And the user selects the "Name" labelled text field on the main page
        And the user writes "testReport" in the text field
        And the user waits 2 seconds
        And the user clicks the "Save" labelled business action button on the main page
        Then the value of the toast is "This report name already exists. Rename your report and try again."

    Scenario: As a user I do not want to leave and discard the changes (go back) and unsaved report cannot be deleted
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-reporting/Report"
        When the user selects the "Name" labelled text field on the main page
        And the user writes "invalidReport" in the text field
        And the user selects the "Parent Package" labelled select field on the main page
        And the user writes "xtrem-reporting" in the select field
        And the user clicks the "Delete" labelled more actions button in the header
        Then the value of the toast is "Unsaved reports cannot be deleted."
        When the user clicks the record with the text "noActiveTemplate" in the navigation panel
        Then the text in the header of the dialog is "Unsaved changes"
        When the user clicks the "Go back" button of the Message dialog
        And the user selects the "Name" labelled text field on the main page
        Then the value of the text field is "invalidReport"

    Scenario: As a user I want ensure the mandatory field message is displayed
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-reporting/Report/$new"
        And the user clicks the "Save" labelled business action button on the main page
        Then the value of the toast is "Mandatory field, Mandatory field, Mandatory field"
