Feature: Report Template page

    Scenario: As a user I want to create a new report template using only required fields
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-reporting/ReportTemplate"
        Then the "Report template" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel
        And the user selects the "Name" labelled text field on the main page
        And the user writes "autoReportTemplate" in the text field
        And the user selects the "Report" labelled reference field on the main page
        And the user writes "userDetails" in the reference field
        And the user selects "userDetails" in the reference field
        When the user clicks the "Save" labelled business action button on the main page
        Then the value of the toast is "Record created"
        And the user selects the "Name" labelled text field on the main page
        Then the value of the text field is "autoReportTemplate"
        And the user selects the "Report" labelled reference field on the main page
        Then the value of the reference field is "userDetails"
        When the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Yes" button of the Message dialog
        Then no dialogs are displayed
        And the user selects the "Name" labelled text field on the main page
        Then the value of the text field is ""

    Scenario: As a user I want to create a new report template
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-reporting/ReportTemplate"
        Then the "Report template" titled page is displayed
        When the user selects the "Name" labelled text field on the main page
        And the user writes "newReportTemplate" in the text field
        And the user selects the "Report" labelled reference field on the main page
        And the user writes "userDetails" in the reference field
        And the user selects "userDetails" in the reference field
        And the user waits 3 seconds
        And selects the "data Query" labelled navigation anchor on the main page
        Then the "data Query" labelled navigation anchor is selected
        And the "query" bound graphiql editor field on the main page is displayed
        When the user selects the "query" bound graphiql editor field on the main page
        And the user writes "-------Data Query----------" in the graphiql editor field
        And selects the "code" labelled navigation anchor on the main page
        Then the "Code" labelled navigation anchor is selected
        And the "code" bound code editor field on the main page is displayed
        When the user selects the "code" bound code editor field on the main page
        And the user writes "return 'someResult';" in the code editor field
        And selects the "htmlTemplate" labelled navigation anchor on the main page
        Then the "htmlTemplate" labelled navigation anchor is selected
        And the "htmlTemplate" bound code editor field on the main page is displayed
        When the user selects the "htmlTemplate" bound code editor field on the main page
        And the user writes "-------HTMLTest----------" in the code editor field
        And selects the "Stylesheet" labelled navigation anchor on the main page
        Then the "Stylesheet" labelled navigation anchor is selected
        And the "styleSheet" bound code editor field on the main page is displayed
        When the user selects the "Stylesheet" labelled code editor field on the main page
        And the user writes "------styleSheet--------" in the code editor field
        And the user clicks the "Save" labelled business action button on the main page
        And the user waits 2 seconds
        Then the value of the toast is "Record created"
        And selects the "basicDetails" labelled navigation anchor on the main page
        Then the "basicDetails" labelled navigation anchor is selected
        And the user selects the "Name" labelled text field on the main page
        Then the value of the text field is "newReportTemplate"
        When the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Yes" button of the Message dialog
        And the user selects the "Name" labelled text field on the main page
        Then the value of the text field is ""

    Scenario: As a user I want to cancel an edited Report Template page
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-reporting/ReportTemplate"
        And the user searches for "testTemplate" in the navigation panel
        And the user clicks the record with the text "testTemplate" in the navigation panel
        When the user selects the "Name" labelled text field on the main page
        And the user writes "invoiceReportTemplate" in the text field
        And the user clicks the cancel CRUD button on the main page
        And the user waits 2 seconds
        And the text in the header of the dialog is "Unsaved changes"
        And the user clicks the "Yes" button of the Message dialog
        And the user selects the "Name" labelled text field on the main page
        Then the value of the text field is "testTemplate"
        When the user clicks the "duplicateTemplate" bound business action button on the main page
        And the user selects the "Name" labelled text field on the main page
        Then the value of the text field is "testTemplate1"
        And the user writes "editDupReportTemplate" in the text field
        And the user clicks the cancel CRUD button on the main page
        Then the text in the header of the dialog is "Unsaved changes"
        When the user clicks the "Yes" button of the Message dialog
        And the user selects the "Name" labelled text field on the main page
        Then the value of the text field is "testTemplate1"
        When the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Yes" button of the Message dialog
        And the user selects the "Name" labelled text field on the main page
        Then the value of the text field is ""

    Scenario: As a user I want to duplicate a report template
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-reporting/ReportTemplate"
        And the "duplicateTemplate" bound business action button on the main page is disabled
        When the user selects the "Name" labelled text field on the main page
        And the user writes "stockReportTemplate" in the text field
        And the user selects the "Report" labelled reference field on the main page
        And the user writes "companyDetails" in the reference field
        And the user selects "companyDetails" in the reference field
        And the user clicks the "Save" labelled business action button on the main page
        Then the "duplicateTemplate" bound business action button on the main page is enabled
        When the user clicks the "duplicateTemplate" bound business action button on the main page
        Then the "stockReportTemplate1" titled page is displayed
        When the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Yes" button of the Message dialog
        Then the value of the toast is "Report template deleted"
        And the user selects the "Name" labelled text field on the main page
        Then the value of the text field is ""
        # Clean up Delete stockReportTemplate
        When the user opens the application on a desktop using the following link: "@sage/xtrem-reporting/ReportTemplate"
        And the user searches for "stockReportTemplate" in the navigation panel
        And the user clicks the record with the text "stockReportTemplate" in the navigation panel
        And the user waits 2 seconds
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Yes" button of the Message dialog
        Then the value of the toast is "Report template deleted"
        And the user selects the "Name" labelled text field on the main page
        Then the value of the text field is ""

    Scenario: As a user I want to load an existing factory report template and ensure its fields are disabled
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-reporting/ReportTemplate"
        And the user searches for "userTemplate" in the navigation panel
        And the user clicks the record with the text "userTemplate" in the navigation panel
        Then the "userTemplate" titled page is displayed
        And the user selects the "Name" labelled text field on the main page
        And the text field is read-only
        And the user selects the "Report" labelled reference field on the main page
        Then the reference field is disabled

    Scenario: As a user I want to load a Report Template and ensure the Preview button is enabled
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-reporting/ReportTemplate"
        And the user searches for "testTemplate" in the navigation panel
        And the user clicks the record with the text "testTemplate" in the navigation panel
        Then the "preview" bound business action button on the main page is enabled
        When the user clicks the "preview" bound business action button on the main page
        And selects the "Variables" labelled navigation anchor on the main page
        And the "Variables" labelled navigation anchor is selected
        # variables tab
        And the user selects the "variables" bound table field on the main page
        And the user selects the row 1 of the table field
        When the user writes "US001" in the "Value" labelled nested text field of the selected row in the table field
        Then the value of the "Value" labelled nested text field of the selected row in the table field is "US001"
        And the user selects the row 2 of the table field
        When the user writes "true" in the "Value" labelled nested text field of the selected row in the table field
        Then the value of the "Value" labelled nested text field of the selected row in the table field is "true"
        # preview tab
        When selects the "Preview" labelled navigation anchor on the main page
        Then the "Preview" labelled navigation anchor is selected
        And the "pdf" bound pdf viewer field on the main page is displayed
        And the user waits 2 seconds
        And the user clicks the Close button of the dialog on a full width modal

    Scenario: An error dialog appears when previwing a template with a valid code and no variable values
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-reporting/ReportTemplate"
        When the user searches for "testTemplate" in the navigation panel
        And the user clicks the record with the text "testTemplate" in the navigation panel
        #  verify preview
        And the user clicks the "preview" bound business action button on the main page
        And selects the "Preview" labelled navigation anchor on the main page
        And the user waits 2 seconds
        # Error
        Then an info dialog appears
        And the user clicks the "OK" button of the Message dialog

    Scenario: An error dialog appears when previwing a template with an invalid code and no variable values
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-reporting/ReportTemplate"
        When the user searches for "errorTemplate" in the navigation panel
        And the user clicks the record with the text "errorTemplate" in the navigation panel
        And selects the "code" labelled navigation anchor on the main page
        And the user selects the "code" bound code editor field on the main page
        And the user inserts line 1 in the code editor field
        And the user writes "--invalid code-- " to line 1 of the code editor field
        #    verify preview
        And the user clicks the "preview" bound business action button on the main page
        And selects the "Preview" labelled navigation anchor on the main page
        And the user waits 2 seconds
        # Error
        Then an info dialog appears
        And the user clicks the "OK" button of the Message dialog

    Scenario: As a user I want to create a Report Template and ensure the Preview button is disabled.
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-reporting/ReportTemplate"
        When the user selects the "Name" labelled text field on the main page
        And the user writes "salesReportTemplate" in the text field
        And the user selects the "Report" labelled reference field on the main page
        And the user writes "userDetails" in the reference field
        And the user selects "userDetails" in the reference field
        And the user waits 3 seconds
        Then the "preview" bound business action button on the main page is disabled
        When selects the "data Query" labelled navigation anchor on the main page
        And the user selects the "query" bound graphiql editor field on the main page
        And the user writes "-------Data Query----------" in the graphiql editor field
        And selects the "code" labelled navigation anchor on the main page
        And the user selects the "code" bound code editor field on the main page
        And the user writes "return 'someResult';" in the code editor field
        Then the "preview" bound business action button on the main page is disabled
        When selects the "htmlTemplate" labelled navigation anchor on the main page
        And the user selects the "htmlTemplate" bound code editor field on the main page
        And the user writes "----HTMLTest-----" in the code editor field
        Then the "preview" bound business action button on the main page is disabled
        When the user clicks the "Save" labelled business action button on the main page
        And the user waits 2 seconds
        Then the "preview" bound business action button on the main page is enabled
        And the user waits 2 seconds
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Yes" button of the Message dialog
        And the user selects the "Name" labelled text field on the main page
        Then the value of the text field is ""

    Scenario: As a user I want to ensure a duplicate report template name is not allowed
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-reporting/ReportTemplate"
        When the user selects the "Name" labelled text field on the main page
        And the user writes "activeTemplate" in the text field
        And the user selects the "Report" labelled reference field on the main page
        And the user writes "testReport" in the reference field
        And the user selects "testReport" in the reference field
        And the user waits 2 seconds
        And the user clicks the "Save" labelled business action button on the main page
        Then the value of the toast is "This template name already exists. Rename your template and try again."
        When the user searches for "activeTemplate" in the navigation panel
        And the user clicks the record with the text "activeTemplate" in the navigation panel
        Then the text in the header of the dialog is "Unsaved changes"
        When the user clicks the "Yes" button of the Message dialog
        And the user selects the "Name" labelled text field on the main page
        And the user writes "testTemplate" in the text field
        And the user waits 2 seconds
        And the user clicks the "Save" labelled business action button on the main page
        Then the value of the toast is "This template name already exists. Rename your template and try again."

    Scenario: As a user I do not want to leave and discard the changes (go back) and unsaved template cannot be deleted
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-reporting/ReportTemplate"
        When the user selects the "Name" labelled text field on the main page
        And the user writes "invalidTemplate" in the text field
        And the user selects the "Report" labelled reference field on the main page
        And the user writes "testReport" in the reference field
        And the user clicks the "Delete" labelled more actions button in the header
        Then the value of the toast is "Unsaved templates cannot be deleted."
        When the user clicks the record with the text "activeTemplate" in the navigation panel
        Then the text in the header of the dialog is "Unsaved changes"
        When the user clicks the "Go back" button of the Message dialog
        And the user selects the "Name" labelled text field on the main page
        Then the value of the text field is "invalidTemplate"

    Scenario: As a user I want ensure the mandatory field message is displayed
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-reporting/ReportTemplate"
        And the user clicks the "Create" labelled business action button on the navigation panel
        And the user clicks the "Save" labelled business action button on the main page
        Then the value of the toast is "Mandatory field, Mandatory field"

    Scenario Outline: As a user I want to ensure a non camelCase report template name is not allowed
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-reporting/ReportTemplate"
        When the user selects the "Name" labelled text field on the main page
        And the user writes "<templateName>" in the text field
        And the user selects the "Report" labelled reference field on the main page
        And the user writes "testReport" in the reference field
        And the user selects "testReport" in the reference field
        And the user waits 2 seconds
        And the user clicks the "Save" labelled business action button on the main page
        Then the value of the toast is "The template name must be written in camelCase. Rename your template and try again."

        Examples:
            | templateName       |
            | 1234567%*          |
            | came space case    |
            | camel-case         |
            | camelCase1234567%* |

    Scenario: As a user I want to make sure the popup suggestions contain the #each query properties that are defined by the Data Query XT-4809
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-reporting/ReportTemplate"
        And the user searches for "monacoPlugInTemplate" in the navigation panel
        And the user clicks the record with the text "monacoPlugInTemplate" in the navigation panel
        When selects the "htmlTemplate" labelled navigation anchor on the main page
        And the user waits 1 seconds
        And the user selects the "htmlTemplate" bound code editor field on the main page
        And the user inserts line 1 in the code editor field
        And the user writes "<div><h1 class='header'>Company: TestArray " to line 1 of the code editor field
        And the user inserts line 2 in the code editor field
        And the user writes "{{xtremSystem.company.query.edges.[2].node.sites.query.edges.[0].node.id}}" to line 2 of the code editor field
        And the user inserts line 3 in the code editor field
        And the user writes "</h1> <!comment> {{" to line 3 of the code editor field
        And the suggestion on the code editor field is "xtremSystem.company.query.edges.[0].node.sites.query.edges.[0].node.legalCompany"
        And the user inserts line 4 in the code editor field
        And the user writes "{{#with xtremSystem.company.query.edges}}" to line 4 of the code editor field
        And the user inserts line 5 in the code editor field
        And the user writes "<!comment> {{" to line 5 of the code editor field
        And the suggestion on the code editor field is "node.sites.query.edges.[0].node.legalCompany.id"
        And the user inserts line 6 in the code editor field
        And the user writes "{{#each node.sites.query.edges}}" to line 6 of the code editor field
        And the user inserts line 7 in the code editor field
        And the user writes "<!comment inside each>  {{" to line 7 of the code editor field
        And the suggestion on the code editor field is "node.id"
        And the user inserts line 8 in the code editor field
        And the user writes "{{/each}} <!comment> {{" to line 8 of the code editor field
        And the suggestion on the code editor field is "node.sites.query.edges.[0].node.id"
        And the user inserts line 9 in the code editor field
        And the user writes "{{/with}}  <!comment> {{" to line 9 of the code editor field
        And the suggestion on the code editor field is "xtremSystem.company.query.edges.[0].node.sites.query.edges.[0].node.legalCompany.id"
        And the user inserts line 10 in the code editor field
        And the user writes "{{#with xtremSystem.company.query.edges.[2].node.sites.query.edges}}" to line 10 of the code editor field
        And the user inserts line 11 in the code editor field
        And the user writes "<!comment inside with> {{" to line 11 of the code editor field
        And the suggestion on the code editor field is "node.id"
        And the user inserts line 12 in the code editor field
        And the user writes "{{#with}} <!comment inside second with>" to line 12 of the code editor field
        And the user inserts line 13 in the code editor field
        And the user writes "<!comment inside #with> {{" to line 13 of the code editor field
        And the suggestion on the code editor field is "node.id"
        And the user inserts line 14 in the code editor field
        And the user writes "{{/with}} <!comment> {{" to line 14 of the code editor field
        And the suggestion on the code editor field is "xtremSystem.company.query.edges.[0]"
        And the user inserts line 15 in the code editor field
        And the user writes "{{/with}} <!comment> {{" to line 15 of the code editor field
        And the suggestion on the code editor field is "xtremSystem.company.query.edges.[0].node.id"
        And selects the "htmlTemplate" labelled navigation anchor on the main page

    Scenario: As a user I want to ensure the suggestion details are displayed
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-reporting/ReportTemplate"
        And the user searches for "monacoPlugInTemplate" in the navigation panel
        And the user clicks the record with the text "monacoPlugInTemplate" in the navigation panel
        When selects the "htmlTemplate" labelled navigation anchor on the main page
        And the user waits 1 seconds
        And the user selects the "htmlTemplate" bound code editor field on the main page
        And the user inserts line 1 in the code editor field
        And the user writes "{{" to line 1 of the code editor field
        And the details of the "dirname" suggestion on the code editor field contain "Handlebars-Helpers Reference"
