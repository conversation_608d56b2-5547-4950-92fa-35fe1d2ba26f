@xtrem_reporting
Feature: smoke-test-cd-report-template

    Scenario: Create Report Template
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-reporting/ReportTemplate"
        Then the "Report template" titled page is displayed

        # Click Add <PERSON>rud <PERSON>ton
        And the user clicks the "Create" labelled business action button on the navigation panel

        # Fill in a some fields
        And the user selects the "Name" labelled text field on the main page
        And the user writes "REPORTTEMPLATETEST" in the text field
        And the user selects the "Report" labelled reference field on the main page
        And the user writes "testReport" in the reference field
        And the user selects "testReport" in the reference field

        # Click Save CRUD Button
        When the user clicks the "Save" labelled business action button on the main page

        # Verify Creation
        Then the value of the toast is "Record created"

    Scenario: Delete Report Template
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-reporting/ReportTemplate"
        Then the "Report template" titled page is displayed
        # ... (IMPLEMENT)

        # Search for the record on navigation panel
        When the user searches for "REPORTTEMPLATETEST" in the navigation panel
        And the user clicks the "first" navigation panel's row

        # Click Delete CRUD Button
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog

        # Verify Deletion
        Then the value of the toast is "Record deleted"

    Scenario: Duplicate Report Template
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-reporting/ReportTemplate/eyJfaWQiOiIxNCJ9"
        Then the "Report template" titled page is displayed

        #Click Duplicate Button
        When the user clicks the "Duplicate" labelled button in the header

        #CVerify duplication
        And the user selects the "name" bound text field on the main page
        Then the value of the text field is "jobTravelerTemplate1"
        And the user selects the "report" bound reference field on the main page
        Then the value of the reference field is "jobTraveler"
        And the user selects the "Definition locale" labelled dropdown-list field on the main page
        Then the value of the dropdown-list field is "Arabic (Saudi Arabia)"
        And the user selects the "Default paper format" labelled dropdown-list field on the main page
        Then the value of the dropdown-list field is "Letter"
        And the user selects the "Factory" labelled checkbox field on the main page
        And the checkbox field appears
