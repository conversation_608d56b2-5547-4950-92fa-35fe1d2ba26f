loginUrl: https://login.dev-sagextrem.com/
redirectUrl: https://cluster-a.dev-sagextrem.com/home
jwksUrl: https://login.dev-sagextrem.com/.well-known/jwks.json
issuer: login.dev-sagextrem.com
audience: cluster-a.dev-sagextrem.com
services:
  helmet:
    disabledOptions:
      # You can disable CSP and/or HSTS by setting the following properties to true
      # csp: true
      # hsts: true
    csp:
      # By default the Xtrem service configures the CSP rules according to the Url above,
      # if for some reason you've got some CSP violation in the browser, you can amend the directives.
      # Each directive is an array of elements that while be merged with the default one.
      # For more details, see Helmet doc https://github.com/helmetjs/helmet/blob/main/README.md
      # and MDN https://developer.mozilla.org/en-US/docs/Web/HTTP/CSP
      directives:
        scriptSrc:
        styleSrc:
        frameSrc:
          # - 'https://third-party-product.example.com/'
        connectSrc:
    hsts:
      # Number of seconds browsers should remember to prefer HTTPS.
      # If passed a non-integer, the value is rounded down. It defaults to 15552000, which is 180 days.
      maxAge: 15552000
      # boolean which dictates whether to include the includeSubDomains directive,
      # which makes this policy extend to subdomains. It defaults to true.
      includeSubDomains: true
      # boolean. If true, it adds the preload directive, expressing intent to add your HSTS policy to browsers.
      # See https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Strict-Transport-Security#Preloading_Strict_Transport_Security
      # section on MDN for more. It defaults to false.
      preload: false
