{"data": [{"_id": "0", "path": "!textField", "locale": "", "dataType": "string", "description": "textField"}, {"_id": "10", "path": "integerField", "locale": "", "dataType": "integer", "description": "integerField"}, {"_id": "20", "path": "decimalField", "locale": "", "dataType": "decimal", "description": "decimalField"}, {"_id": "30", "path": "booleanField", "locale": "", "dataType": "boolean", "description": "booleanField"}, {"_id": "40", "path": "dateField", "locale": "", "dataType": "date", "description": "dateField"}, {"_id": "50", "path": "document", "locale": "", "dataType": "textStream", "description": ""}, {"_id": "60", "path": "logo", "locale": "", "dataType": "binaryStream", "description": ""}, {"_id": "70", "path": "siteAddress(name)", "locale": "", "dataType": "reference", "description": ""}, {"_id": "80", "path": "addresses", "locale": "", "dataType": "referenceArray", "description": ""}, {"_id": "90", "path": "flagshipProduct(_id)", "locale": "", "dataType": "reference", "description": ""}, {"_id": "100", "path": "item(_id)", "locale": "", "dataType": "reference", "description": ""}, {"_id": "110", "path": "minQuantity", "locale": "", "dataType": "integer", "description": ""}, {"_id": "120", "path": "ratings", "locale": "", "dataType": "enumA<PERSON>y", "description": ""}, {"_id": "130", "path": "#products", "locale": "", "dataType": "collection", "description": ""}, {"_id": "140", "path": "!product", "locale": "", "dataType": "string", "description": ""}, {"_id": "150", "path": "barcode", "locale": "", "dataType": "string", "description": ""}, {"_id": "160", "path": "description", "locale": "", "dataType": "string", "description": ""}, {"_id": "170", "path": "hotProduct", "locale": "", "dataType": "boolean", "description": ""}, {"_id": "180", "path": "fixedQuantity", "locale": "", "dataType": "integer", "description": ""}, {"_id": "190", "path": "st", "locale": "", "dataType": "integer", "description": ""}, {"_id": "200", "path": "listPrice", "locale": "", "dataType": "decimal", "description": ""}, {"_id": "210", "path": "progress", "locale": "", "dataType": "integer", "description": ""}, {"_id": "220", "path": "tax", "locale": "", "dataType": "decimal", "description": ""}, {"_id": "230", "path": "amount", "locale": "", "dataType": "decimal", "description": ""}, {"_id": "240", "path": "email", "locale": "", "dataType": "string", "description": ""}, {"_id": "250", "path": "releaseDate", "locale": "", "dataType": "date", "description": ""}, {"_id": "260", "path": "endingDate", "locale": "", "dataType": "date", "description": ""}, {"_id": "270", "path": "category", "locale": "", "dataType": "enum", "description": ""}, {"_id": "280", "path": "subcategories", "locale": "", "dataType": "enumA<PERSON>y", "description": ""}, {"_id": "290", "path": "entries", "locale": "", "dataType": "stringArray", "description": ""}, {"_id": "300", "path": "qty", "locale": "", "dataType": "integer", "description": ""}, {"_id": "310", "path": "netPrice", "locale": "", "dataType": "decimal", "description": ""}, {"_id": "320", "path": "//originAddress", "locale": "", "dataType": "reference", "description": ""}, {"_id": "330", "path": "name", "locale": "", "dataType": "string", "description": ""}, {"_id": "340", "path": "addressLine1", "locale": "", "dataType": "string", "description": ""}, {"_id": "350", "path": "addressLine2", "locale": "", "dataType": "string", "description": ""}, {"_id": "360", "path": "city", "locale": "", "dataType": "string", "description": ""}, {"_id": "370", "path": "country", "locale": "", "dataType": "reference", "description": ""}]}