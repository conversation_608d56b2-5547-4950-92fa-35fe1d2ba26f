{"data": [{"_id": "0", "path": "customer(name)", "locale": "", "dataType": "string", "description": "customer(name)"}, {"_id": "10", "path": "purchaseDate", "locale": "", "dataType": "date", "description": "purchaseDate"}, {"_id": "20", "path": "notes", "locale": "", "dataType": "textStream", "description": "notes"}, {"_id": "30", "path": "pdf", "locale": "", "dataType": "binaryStream", "description": "pdf"}, {"_id": "40", "path": "queryText", "locale": "", "dataType": "textStream", "description": "queryText"}, {"_id": "50", "path": "order(_id)", "locale": "", "dataType": "reference", "description": "order(_id)"}, {"_id": "60", "path": "#lines", "locale": "", "dataType": "collection", "description": "#lines"}, {"_id": "70", "path": "orderQuantity", "locale": "", "dataType": "integer", "description": "orderQuantity"}, {"_id": "80", "path": "comments", "locale": "", "dataType": "string", "description": "comments"}, {"_id": "90", "path": "product(_id)", "locale": "", "dataType": "reference", "description": "product"}, {"_id": "100", "path": "pdf", "locale": "", "dataType": "binaryStream", "description": "pdf"}, {"_id": "110", "path": "netPrice", "locale": "", "dataType": "decimal", "description": "netPrice"}, {"_id": "120", "path": "discountType", "locale": "", "dataType": "enum", "description": "discountType"}]}