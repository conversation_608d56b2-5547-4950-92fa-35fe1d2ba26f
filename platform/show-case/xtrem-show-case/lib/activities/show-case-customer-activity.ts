import { Activity } from '@sage/xtrem-core';
import { ShowCaseCustomer } from '../nodes/show-case-customer';
import { ShowCaseInvoice } from '../nodes/show-case-invoice';

export const showCaseCustomerActivity = new Activity({
    description: 'Showcase customer activity',
    node: () => ShowCaseCustomer,
    __filename,
    permissions: ['lookup', 'read', 'create', 'update', 'delete'],
    operationGrants: {
        testMutation: [
            {
                on: [() => ShowCaseInvoice],
                operations: ['read'],
            },
        ],
    },
});
