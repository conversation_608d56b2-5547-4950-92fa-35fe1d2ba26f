import { GraphApi } from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.sticker<ProfileSticker>({
    title: 'User Profile',
    icon: 'person',
    isActive() {
        return true;
    },
})
export class ProfileSticker extends ui.Sticker<GraphApi> {
    @ui.decorators.section<ProfileSticker>({
        title: 'Profile',
    })
    section: ui.containers.Section;

    @ui.decorators.block<ProfileSticker>({
        parent() {
            return this.section;
        },
    })
    block1: ui.containers.Block;

    @ui.decorators.imageField<ProfileSticker>({
        title: 'You look awesome today!',
        height: '200px',
        isReadOnly: true,
        parent() {
            return this.block1;
        },
    })
    field1: ui.fields.Image;
}
