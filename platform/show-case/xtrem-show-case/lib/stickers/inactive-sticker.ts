import * as ui from '@sage/xtrem-ui';

@ui.decorators.sticker<InactiveSticker>({
    title: 'Inactive sticker',
    icon: 'tick',
    isActive() {
        return Promise.resolve(false);
    },
})
export class InactiveSticker extends ui.Sticker {
    @ui.decorators.section<InactiveSticker>({
        title: 'Nothing good',
    })
    section1: ui.containers.Section;

    @ui.decorators.block<InactiveSticker>({
        title: 'Block 1',
        parent() {
            return this.section1;
        },
    })
    block1: ui.containers.Block;

    @ui.decorators.numericField<InactiveSticker>({
        title: 'Just this',
        isMandatory: true,
        parent() {
            return this.block1;
        },
    })
    field1: ui.fields.Numeric;
}
