import { GraphApi } from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.sticker<ActiveSticker>({
    title: 'Printer destination',
    icon: 'home',
    // category: 'ADC',
    businessActions() {
        return [this.action1, this.action2, this.action3];
    },
    isActive() {
        return true;
    },
    onOpen() {
        this.$.showToast('This sticker is open!');
    },
})
export class ActiveSticker extends ui.Sticker<GraphApi> {
    @ui.decorators.section<ActiveSticker>({
        title: 'Section 1',
    })
    section1: ui.containers.Section;

    @ui.decorators.block<ActiveSticker>({
        title: 'Block 1',
        parent() {
            return this.section1;
        },
    })
    block1: ui.containers.Block;

    @ui.decorators.numericField<ActiveSticker>({
        title: 'Numeric badgeContent test',
        onChange() {
            this.updateLabel(this.field1.value);
        },
        parent() {
            return this.block1;
        },
    })
    field1: ui.fields.Numeric;

    @ui.decorators.block<ActiveSticker>({
        title: 'Block 2',
        parent() {
            return this.section1;
        },
    })
    block2: ui.containers.Block;

    @ui.decorators.textField<ActiveSticker>({
        title: 'Text badgeContent test',
        onChange() {
            this.updateLabel(this.field2.value);
        },
        parent() {
            return this.block2;
        },
    })
    field2: ui.fields.Text;

    @ui.decorators.block<ActiveSticker>({
        title: 'Block 3',
        parent() {
            return this.section1;
        },
    })
    block3: ui.containers.Block;

    @ui.decorators.buttonField<ActiveSticker>({
        map() {
            return 'Finish sticker';
        },
        onClick() {
            this.$.finish();
        },
        parent() {
            return this.block3;
        },
    })
    finishButton: ui.fields.Button;

    @ui.decorators.pageAction<ActiveSticker>({
        title: 'First',
        onClick() {
            this.$.showToast('First action was clicked.');
        },
    })
    action1: ui.PageAction;

    @ui.decorators.pageAction<ActiveSticker>({
        title: 'Second',
        onClick() {
            this.$.showToast('Second action was clicked.');
        },
    })
    action2: ui.PageAction;

    @ui.decorators.pageAction<ActiveSticker>({
        title: 'Third',
        onClick() {
            this.$.showToast('Third action was clicked.');
        },
    })
    action3: ui.PageAction;

    updateLabel(value: string | number) {
        this.$.sticker.updateMenuItem(value);
    }
}
