import { date } from '@sage/xtrem-date-time';
import * as ui from '@sage/xtrem-ui';

@ui.widgets.table<ListOfProductsTable>({
    title: 'Products',
    description: 'Detailed list with products',
    cacheLifespan: ui.widgets.WidgetCacheLifespan.day,
    category: 'DEMO_CATEGORY',
    content() {
        return (
            this.$.data?.xtremShowCase.showCaseProduct.query.edges.map(({ node, cursor }: any) => ({
                _id: node._id,
                title: node.product,
                titleRight: node.releaseDate,
                line2: node.category,
                image: node.imageField?.value,
                cursor,
            })) || []
        );
    },
    canSwitchViewMode: true,
    displayMode: 'card',
    rowDefinition: {
        title: {
            title: 'Name',
            renderedAs: 'link',
            onClick(_id: string) {
                this.$.router.goTo('@sage/xtrem-show-case/ShowCaseProduct', { _id });
            },
        },
        titleRight: { title: 'Release date' },
        line2: {
            title: 'Category',
        },
    },
    dateFilter: {
        maxDate: date.today().toJsDate(),
        periodTypes: [ui.DateFilterPeriodType.DAY, ui.DateFilterPeriodType.WEEK, ui.DateFilterPeriodType.MONTH],
    },
    getQuery(args) {
        const filter: any = {};
        const selectedPeriod = this.$.options.selectedPeriod;
        if (selectedPeriod) {
            filter.releaseDate = {
                _gte: selectedPeriod.startDate,
                _lte: selectedPeriod.endDate,
            };
        }
        return {
            xtremShowCase: {
                showCaseProduct: {
                    query: {
                        __args: {
                            filter: JSON.stringify(filter),
                            orderBy: JSON.stringify({ releaseDate: 1 }),
                            first: args?.first ?? 20,
                            ...(args?.after?.cursor && { after: args.after.cursor }),
                        },
                        edges: {
                            node: {
                                _id: true,
                                product: true,
                                releaseDate: true,
                                category: true,
                                imageField: {
                                    value: true,
                                },
                            },
                            cursor: true,
                        },
                    },
                },
            },
        };
    },
})
export class ListOfProductsTable extends ui.widgets.TableWidget {}
