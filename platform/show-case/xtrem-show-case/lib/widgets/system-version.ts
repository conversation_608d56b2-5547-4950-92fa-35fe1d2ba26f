import * as ui from '@sage/xtrem-ui';

@ui.widgets.indicatorTile<SystemVersion>({
    title: 'System version',
    description: 'Version information about the currently installed system version',
    cacheLifespan: ui.widgets.WidgetCacheLifespan.day,
    icon: 'info',
    value() {
        return this.$.data.xtremSystem.sysPackVersion.query.edges?.[0]?.node?.version || 'N/A';
    },
    getQuery() {
        return {
            xtremSystem: {
                sysPackVersion: {
                    query: {
                        __args: { filter: "{name:'@sage/xtrem-system'}" },
                        edges: {
                            node: {
                                version: true,
                            },
                        },
                    },
                },
            },
        };
    },
})
export class SystemVersion extends ui.widgets.AbstractWidget {}
