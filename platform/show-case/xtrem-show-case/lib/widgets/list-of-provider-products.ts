import * as ui from '@sage/xtrem-ui';

@ui.widgets.table<ListOfProviderProducts>({
    title: 'Products',
    description: 'Detailed list with products',
    cacheLifespan: ui.widgets.WidgetCacheLifespan.day,
    group: 'StandardShowCaseProvider-360',
    category: 'DEMO_CATEGORY',
    content() {
        return (
            this.$.data?.xtremShowCase.showCaseProduct.query.edges.map(({ node, cursor }: any) => ({
                _id: node._id,
                title: node.product,
                titleRight: node.releaseDate,
                line2: node.category,
                image: node.imageField?.value,
                cursor,
            })) || []
        );
    },
    canSwitchViewMode: true,
    displayMode: 'card',
    rowDefinition: {
        title: { title: 'Name' },
        titleRight: { title: 'Release date' },
        line2: {
            title: 'Category',
        },
    },
    getQuery(args) {
        const filter: any = {
            provider: { _id: { _eq: this.$.contextVariables.recordId } },
        };
        return {
            xtremShowCase: {
                showCaseProduct: {
                    query: {
                        __args: {
                            filter: JSON.stringify(filter),
                            orderBy: JSON.stringify({ releaseDate: 1 }),
                            first: args?.first ?? 20,
                            ...(args?.after?.cursor && { after: args.after.cursor }),
                        },
                        edges: {
                            node: {
                                _id: true,
                                product: true,
                                releaseDate: true,
                                category: true,
                                imageField: {
                                    value: true,
                                },
                            },
                            cursor: true,
                        },
                    },
                },
            },
        };
    },
})
export class ListOfProviderProducts extends ui.widgets.TableWidget {}
