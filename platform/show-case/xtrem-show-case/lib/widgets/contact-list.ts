import type { Dict } from '@sage/xtrem-shared';
import * as ui from '@sage/xtrem-ui';

type NodeProps = Omit<
    ui.widgets.ContactCardWidgetContent,
    | 'contacts'
    | 'onContactTypeSwitchChanged'
    | 'onContactAdd'
    | 'onNoteAdded'
    | 'onNoteDeleted'
    | 'onNoteEdited'
    | 'onSelectedContactChanged'
    | 'stringLiterals'
    | 'numberOfAddresses'
    | 'numberOfContacts'
    | 'notes'
>;

type Node = {
    _id: string;
    textField: string;
    concatenatedAddress: string;
    siteAddress?: {
        name: string;
        city: string;
    };
};

let isInitialLoad = true;

const contacts = {
    'contact-1': '<PERSON>',
    'contact-2': '<PERSON>',
    'contact-3': '<PERSON>',
} as const;

const contactCache: Record<string, NodeProps> = {
    '': {
        contactName: undefined,
        contactEmailAddress: undefined,
        contactImage: undefined,
        contactPhoneNumber: undefined,
        contactPosition: undefined,
        contactRole: undefined,
        iconSrc: undefined,
    },
    'contact-1': {
        contactName: '<PERSON>',
        contactEmailAddress: '<EMAIL>',
        contactImage:
            'data:image/svg+xml;base64,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',
        contactPhoneNumber: '************',
        contactPosition: 'Manager',
        contactRole: 'Admin',
        iconSrc: 'arrow-up',
    },
    'contact-2': {
        contactName: 'Jane Doe',
        contactEmailAddress: '<EMAIL>',
        contactImage:
            'data:image/svg+xml;base64,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',
        contactPhoneNumber: '************',
        contactPosition: 'CEO',
        contactRole: 'CEO',
        iconSrc: 'arrow-down',
    },
    'contact-3': {
        contactName: 'John Smith',
        contactEmailAddress: '<EMAIL>',
        contactImage:
            'data:image/svg+xml;base64,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',
        contactPhoneNumber: '************',
        contactPosition: 'Accountant',
        contactRole: 'Accountant',
        iconSrc: 'arrow-left',
    },
};

@ui.widgets.contactCard<ContactList>({
    title: 'Contact cards',
    cacheLifespan: ui.widgets.WidgetCacheLifespan.day,
    description: 'Contact cards',
    category: 'OTHER_CATEGORY',
    content() {
        const edges = this.$.data?.xtremShowCase?.showCaseProvider?.query?.edges;
        const addressCache = (edges ?? []).reduce(
            (acc, curr) => {
                if (curr.node.concatenatedAddress) {
                    acc[curr.node._id] = {
                        addressFunction: curr.node.siteAddress?.name ?? '',
                        address: curr.node.concatenatedAddress ?? 'Unknown',
                    };
                }
                return acc;
            },
            {
                '': {
                    address: undefined,
                    addressFunction: undefined,
                },
            } as Record<string, Pick<NodeProps, 'address' | 'addressFunction'>>,
        );
        const addresses = (edges ?? []).reduce((acc, curr) => {
            if (curr.node.concatenatedAddress) {
                acc[curr.node._id] = curr.node.concatenatedAddress;
            }
            return acc;
        }, {} as Dict<string>);
        const firstAddress = edges?.[0]?.node;
        const firstContact = contactCache[this.$.options.selectedContactId ?? 'contact-1'];
        return {
            notes: this.$.options.notes ?? [],
            address: isInitialLoad ? (firstAddress?.concatenatedAddress ?? 'Unknown') : this.$.options.address,
            addressRole: isInitialLoad ? (firstAddress?.siteAddress?.city ?? 'Primary') : this.$.options.addressRole,
            addressFunction: isInitialLoad ? firstAddress?.siteAddress?.name : this.$.options.addressFunction,
            contactName: isInitialLoad ? firstContact?.contactName : this.$.options.contactName,
            contactEmailAddress: isInitialLoad ? firstContact?.contactEmailAddress : this.$.options.contactEmailAddress,
            contactImage: isInitialLoad ? firstContact?.contactImage : this.$.options.contactImage,
            contactPhoneNumber: isInitialLoad ? firstContact?.contactPhoneNumber : this.$.options.contactPhoneNumber,
            contactPosition: isInitialLoad ? firstContact?.contactPosition : this.$.options.contactPosition,
            contactRole: isInitialLoad ? firstContact?.contactRole : this.$.options.contactRole,
            contacts: isInitialLoad ? contacts : (this.$.options.contacts ?? {}),
            addresses: isInitialLoad ? addresses : (this.$.options.addresses ?? {}),
            iconSrc: isInitialLoad ? firstContact.iconSrc : this.$.options.iconSrc,
            numberOfAddresses: this.$.options.numberOfAddresses ?? Object.keys(addresses).length,
            numberOfContacts: this.$.options.numberOfContacts ?? Object.keys(contacts).length,
            // eslint-disable-next-line require-await
            onContactTypeSwitchChanged: async selectedContactType => {
                isInitialLoad = false;
                this.$.options = {
                    ...(this.$.options as ui.widgets.ContactCardWidgetOptions),
                    selectedContactType,
                };
            },
            // eslint-disable-next-line require-await
            onAddressAdd: async () => {
                this.$.showToast('Address added', { type: 'success' });
            },
            // eslint-disable-next-line require-await
            onContactAdd: async () => {
                this.$.showToast('Contact added', { type: 'success' });
            },
            // eslint-disable-next-line require-await
            onNoteAdded: async newNoteText => {
                // call the API to add a note
                const currentNotes = this.$.options.notes ?? [];
                this.$.options = {
                    ...(this.$.options as ui.widgets.ContactCardWidgetOptions),
                    notes: [
                        ...currentNotes,
                        {
                            _id: String(Date.now()),
                            text: newNoteText,
                            timestamp: new Date(),
                        },
                    ],
                };
            },
            // eslint-disable-next-line require-await
            onNoteDeleted: async deletedId => {
                // call the API to delete a note
                this.$.options = {
                    ...(this.$.options as ui.widgets.ContactCardWidgetOptions),
                    notes: (this.$.options.notes ?? []).filter(note => note._id !== deletedId) ?? [],
                };
            },
            // eslint-disable-next-line require-await
            onNoteEdited: async (editedId, text) => {
                // call the API to edit a note
                this.$.options = {
                    ...(this.$.options as ui.widgets.ContactCardWidgetOptions),
                    notes:
                        (this.$.options.notes ?? []).map(note => (note._id === editedId ? { ...note, text } : note)) ??
                        [],
                };
            },
            // eslint-disable-next-line require-await
            onSelectedContactChanged: async selectedContactId => {
                isInitialLoad = false;

                this.$.options = {
                    ...(this.$.options as ui.widgets.ContactCardWidgetOptions),
                    ...contactCache[selectedContactId ?? ''],
                    selectedContactId,
                    contacts,
                    addresses,
                };
                this.$.showToast(`Selected contact: ${selectedContactId}`, {
                    type: 'success',
                });
            },
            // eslint-disable-next-line require-await
            onSelectedAddressChanged: async selectedAddressId => {
                isInitialLoad = false;

                this.$.options = {
                    ...(this.$.options as ui.widgets.ContactCardWidgetOptions),
                    ...addressCache[selectedAddressId ?? ''],
                    selectedAddressId,
                    contacts,
                    addresses,
                };
                this.$.showToast(`Selected address: ${selectedAddressId}`, {
                    type: 'success',
                });
            },
            selectedAddressId: this.$.options.selectedAddressId,
            selectedContactId: this.$.options.selectedContactId,
        };
    },
    getQuery() {
        return {
            xtremShowCase: {
                showCaseProvider: {
                    query: {
                        edges: {
                            node: {
                                _id: true,
                                textField: true,
                                concatenatedAddress: true,
                                siteAddress: {
                                    name: true,
                                    city: true,
                                },
                            },
                        },
                    },
                },
            },
        };
    },
})
export class ContactList extends ui.widgets.ContactCardWidget<{
    xtremShowCase: {
        showCaseProvider: {
            query: {
                edges: {
                    node: Node;
                }[];
            };
        };
    };
}> {}
