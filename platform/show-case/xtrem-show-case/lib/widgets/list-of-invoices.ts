import { GraphApi } from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import { padStart } from 'lodash';

@ui.widgets.table<ListOfInvoices>({
    title: 'Invoices',
    description: 'Invoice list with notes',
    cacheLifespan: ui.widgets.WidgetCacheLifespan.day,
    category: 'DEMO_CATEGORY',
    content() {
        return (
            this.$.data?.xtremShowCase.showCaseInvoice.query.edges.map(({ node, cursor }: any) => ({
                _id: node._id,
                title: `SCINV${padStart(node._id, 6, '0')}`,
                titleRight: node.customer.name,
                line2: ui.formatDateToCurrentLocale(node.purchaseDate),
                line2Right: node.lines.query.totalCount,
                longContent: node.notes.value,
                line3: 'tick_circle',
                cursor,
            })) || []
        );
    },
    canSwitchViewMode: true,
    displayMode: 'card',
    rowDefinition: {
        title: { title: 'Invoice Number' },
        titleRight: { title: 'Customer' },
        line2: {
            title: 'Category',
        },
        line2Right: {
            title: 'Number of lines',
            decimalDigits: 0,
            valueFormat: 'number',
        },
        longContent: {
            title: 'Notes',
        },
        line3: {
            title: 'Status',
            renderedAs: 'icon',
            displayOptions: {
                colorVariant: 'neutral',
            },
        },
    },
    rowActions: [
        {
            title: 'Delete',
            onClick(_id) {
                this.$.graph
                    .node('@sage/xtrem-show-case/ShowCaseInvoice')
                    .deleteById(String(_id))
                    .execute()
                    .then(() => {
                        this.$.showToast('Invoice deleted successfully');
                        this.$.refreshWidget();
                    })
                    .catch(() => {
                        this.$.showToast('Error deleting invoice', { type: 'error' });
                    });
            },
        },
        {
            title: 'Tag',
            onClick() {
                this.$.graph
                    .node('@sage/xtrem-show-case/ShowCaseCustomer')
                    .mutations.testMutation(true, { something: 'Test customer mutation' })
                    .execute()
                    .then(() => {
                        this.$.showToast('Custom mutation executed successfully');
                    })
                    .catch(() => {
                        this.$.showToast('Error executing custom mutation', { type: 'error' });
                    });
            },
        },
    ],
    getQuery(args) {
        return {
            xtremShowCase: {
                showCaseInvoice: {
                    query: {
                        __args: {
                            first: args?.first ?? 20,
                            ...(args?.after?.cursor && { after: args.after.cursor }),
                        },
                        edges: {
                            node: {
                                _id: true,
                                customer: {
                                    name: true,
                                },
                                lines: {
                                    query: {
                                        totalCount: true,
                                    },
                                },
                                notes: {
                                    value: true,
                                },
                                purchaseDate: true,
                                totalProductQty: true,
                            },
                            cursor: true,
                        },
                    },
                },
            },
        };
    },
})
export class ListOfInvoices extends ui.widgets.TableWidget<any, GraphApi> {}
