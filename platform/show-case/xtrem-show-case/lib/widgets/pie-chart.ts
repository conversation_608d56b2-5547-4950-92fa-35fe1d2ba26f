import { Decimal } from '@sage/xtrem-decimal';
import * as ui from '@sage/xtrem-ui';

@ui.widgets.pieChart<PieChart>({
    title: 'Pie Chart',
    cacheLifespan: ui.widgets.WidgetCacheLifespan.day,
    category: 'OTHER_CATEGORY',
    content() {
        const total =
            this.$.data?.xtremShowCase.showCaseProduct.queryAggregate.edges.reduce(
                (
                    previousValue,
                    {
                        node: {
                            values: {
                                _id: { distinctCount },
                            },
                        },
                    },
                ) => {
                    return new Decimal(previousValue + distinctCount).toNumber();
                },
                0,
            ) ?? 1;
        const aggregatedData = this.$.data?.xtremShowCase.showCaseProduct.queryAggregate.edges.reduce(
            (
                previousValue,
                {
                    node: {
                        group: { category },
                        values: {
                            _id: { distinctCount },
                        },
                    },
                },
            ) => {
                previousValue.push({
                    title: category || 'Other',
                    _id: category || 'Other',
                    value: new Decimal((distinctCount / total) * 100).toNumber(),
                });
                return previousValue;
            },
            [] as {
                title: string;
                _id: string;
                value: number;
            }[],
        );
        return aggregatedData || [];
    },
    isDonut: true,
    hasCardView: true,
    filter: {
        ariaLabel: 'Filter data',
        options: {
            'hot-products': 'Only hot products',
            'not-hot-products': 'Not hot products',
        },
        onChange(value) {
            this.$.options = {
                ...this.$.options,
                selectedFilter: value ?? undefined,
            };
            this.$.refreshWidget();
        },
    },
    rowDefinition: {
        title: { title: 'Some title', renderedAs: 'pill' },
    },
    callToActions: {
        doSomething: {
            title: 'See more',
            onClick() {
                this.$.showToast('Perform action on pie chart');
            },
        },
    },
    getQuery() {
        let filterArgs;
        if (this.$.options.selectedFilter === 'hot-products') {
            filterArgs = {
                __args: {
                    filter: JSON.stringify({
                        hotProduct: true,
                    }),
                },
            };
        } else if (this.$.options.selectedFilter === 'not-hot-products') {
            filterArgs = {
                __args: {
                    filter: JSON.stringify({
                        hotProduct: false,
                    }),
                },
            };
        }
        return {
            xtremShowCase: {
                showCaseProduct: {
                    queryAggregate: {
                        edges: {
                            node: {
                                group: {
                                    category: true,
                                },
                                values: {
                                    _id: {
                                        distinctCount: true,
                                    },
                                },
                            },
                            cursor: true,
                        },
                        ...filterArgs,
                    },
                },
            },
        };
    },
})
export class PieChart extends ui.widgets.PieChartWidget<{
    xtremShowCase: {
        showCaseProduct: {
            queryAggregate: {
                edges: {
                    node: {
                        group: {
                            category: string;
                        };
                        values: {
                            _id: {
                                distinctCount: number;
                            };
                        };
                    };
                    cursor: string;
                }[];
            };
        };
    };
}> {}
