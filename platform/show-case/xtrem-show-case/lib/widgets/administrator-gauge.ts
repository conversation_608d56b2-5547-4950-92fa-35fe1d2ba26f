import * as ui from '@sage/xtrem-ui';

@ui.widgets.gauge<AdministratorGauge>({
    title: 'Ratio of administrators',
    cacheLifespan: ui.widgets.WidgetCacheLifespan.day,
    content() {
        const body = this.$.data.xtremSystem.user;
        return {
            value: body.adminCount.totalCount,
            totalValue: body.totalUserCount.totalCount,
            evolutionValue: 1,
        };
    },
    scale: 0,
    color: ui.tokens.colorsSemanticPositive500,
    evolutionUnit: 'pcs',
    callToActions: {
        cantDoThis: {
            title: 'Some action',
            onClick() {
                this.$.showToast('Well you cannot do much with these users');
            },
        },
    },
    getQuery() {
        return {
            xtremSystem: {
                user: {
                    adminCount: {
                        __aliasFor: 'query',
                        __args: { filter: '{isAdministrator: true}' },
                        totalCount: true,
                    },
                    totalUserCount: {
                        __aliasFor: 'query',
                        totalCount: true,
                    },
                },
            },
        };
    },
})
export class AdministratorGauge extends ui.widgets.AbstractWidget {}
