import * as ui from '@sage/xtrem-ui';
import { ShowCaseProduct } from '@sage/xtrem-show-case-api';

interface Product {
    id: string;
    listPrice: number;
    amount: number;
}

@ui.widgets.lineChart<ProductLineChart, Product, string, number>({
    title: 'Products',
    cacheLifespan: ui.widgets.WidgetCacheLifespan.day,
    category: 'OTHER_CATEGORY',
    content() {
        return this.$.data.xtremShowCase.showCaseProduct.query.edges.map(({ node: { listPrice, amount, _id } }) => ({
            id: _id,
            listPrice: Number.parseFloat(listPrice),
            amount: Number.parseFloat(amount),
        }));
    },
    callToActions: {
        doSomething: {
            title: 'Action',
            onClick() {
                this.$.showToast('Perform action on products');
            },
        },
    },
    primaryAxis: {
        bind: 'id',
        title: 'Product ID',
    },
    secondaryAxes: [
        {
            bind: 'listPrice',
            title: 'Price',
        },
        {
            bind: 'amount',
            title: 'Amount',
            onClick(record) {
                this.$.showToast(`Clicked on: ${JSON.stringify(record)}`);
            },
            tooltipContent(record, primaryValue, secondaryValue) {
                return `ID: ${primaryValue} - Amount: ${secondaryValue}`;
            },
        },
    ],
    getQuery() {
        return {
            xtremShowCase: {
                showCaseProduct: {
                    query: {
                        edges: {
                            node: {
                                _id: true,
                                product: true,
                                description: true,
                                listPrice: true,
                                tax: true,
                                amount: true,
                                total: true,
                                releaseDate: true,
                                qty: true,
                                netPrice: true,
                            },
                        },
                    },
                },
            },
        };
    },
})
export class ProductLineChart extends ui.widgets.AbstractWidget<{
    xtremShowCase: { showCaseProduct: { query: { edges: { node: ShowCaseProduct }[] } } };
}> {}
