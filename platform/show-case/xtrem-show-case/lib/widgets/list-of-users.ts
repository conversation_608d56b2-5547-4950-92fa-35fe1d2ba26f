import * as ui from '@sage/xtrem-ui';

@ui.widgets.staticContent<ListOfUsers>({
    title: 'List of users',
    cacheLifespan: ui.widgets.WidgetCacheLifespan.day,
    description: 'Unordered list of the current users',
    category: 'OTHER_CATEGORY',
    content() {
        const body = this.$.data.xtremSystem.user.query.edges
            .map((e: any) => `- ${e.node.firstName} ${e.node.lastName} (${e.node.email})`)
            .join('\n');

        return `**List of users**\n${body}`;
    },
    getQuery() {
        return {
            xtremSystem: {
                user: {
                    query: {
                        edges: {
                            node: {
                                email: true,
                                firstName: true,
                                lastName: true,
                            },
                        },
                    },
                },
            },
        };
    },
})
export class ListOfUsers extends ui.widgets.AbstractWidget {}
