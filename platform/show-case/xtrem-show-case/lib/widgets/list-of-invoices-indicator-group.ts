import * as ui from '@sage/xtrem-ui';

@ui.widgets.indicatorTileGroup<ListOfInvoicesIndicatorGroup>({
    title: 'Invoices Overview Tile Group',
    description: 'Overview of key invoice data in tiles',
    cacheLifespan: ui.widgets.WidgetCacheLifespan.day,
    settingsPage: '@sage/xtrem-show-case/InvoicesOverviewSettings',
    category: 'DEMO_CATEGORY',
    content() {
        const edges = this.$.data?.xtremShowCase.showCaseInvoice.query.edges || [];
        return edges.flatMap((edge: any) => {
            const invoice = edge.node;
            return [
                {
                    title: 'Invoice Date',
                    value: ui.formatDateToCurrentLocale(invoice.purchaseDate),
                    valueColor: 'black',
                    icon: 'calendar',
                    iconColor: 'black',
                    hasSeparatorAfter: true,
                    contentAlignment: 'left',
                    onClick: () => {
                        this.$.router.goTo('@sage/xtrem-show-case/ShowCaseProduct');
                    },
                },
                {
                    title: 'Total Quantity',
                    value: String(
                        invoice.lines.query.edges.reduce(
                            (sum: number, lineEdge: any) => sum + lineEdge.node.orderQuantity,
                            0,
                        ),
                    ),
                    valueColor: 'green',
                    icon: 'cart',
                    iconColor: 'green',
                    hasSeparatorAfter: true,
                    contentAlignment: 'left',
                },
                {
                    title: 'Customer',
                    value: invoice.customer.name,
                    valueColor: 'black',
                    icon: 'user',
                    iconColor: 'black',
                    hasSeparatorAfter: true,
                    contentAlignment: 'left',
                },
            ];
        });
    },
    headerActions: [
        {
            title: 'View Details',
            icon: 'bullet_list_dotted',
            onClick() {
                this.$.router.goTo('@sage/xtrem-show-case/ShowCaseProduct');
            },
        },
    ],
    getQuery() {
        return {
            xtremShowCase: {
                showCaseInvoice: {
                    query: {
                        edges: {
                            node: {
                                _id: true,
                                purchaseDate: true,
                                customer: {
                                    name: true,
                                },
                                lines: {
                                    query: {
                                        edges: {
                                            node: {
                                                orderQuantity: true,
                                            },
                                        },
                                    },
                                },
                            },
                        },
                    },
                },
            },
        };
    },
})
export class ListOfInvoicesIndicatorGroup extends ui.widgets.AbstractWidget {}
