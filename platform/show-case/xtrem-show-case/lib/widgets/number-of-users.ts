import * as ui from '@sage/xtrem-ui';

@ui.widgets.indicatorTile<NumberOfUsers>({
    title: 'Number of users',
    cacheLifespan: ui.widgets.WidgetCacheLifespan.day,
    settingsPage: '@sage/xtrem-show-case/NumberOfUsersSettings',
    icon() {
        return 'person';
    },
    value() {
        return String(this.$.data.xtremSystem.user.query.totalCount);
    },
    headerActions: [
        {
            title: 'View list',
            icon: 'bullet_list_dotted',
            onClick() {
                this.$.router.goTo('@sage/xtrem-authorization/UserList');
            },
        },
    ],
    getQuery() {
        const filter: any = {};
        if (this.$.settings.shouldExcludeAdministrativeUsers) {
            filter.isAdministrator = false;
        }
        if (this.$.settings.shouldExcludeInactiveUsers) {
            filter.isActive = true;
        }

        return {
            xtremSystem: {
                user: {
                    query: {
                        __args: { filter: JSON.stringify(filter) },
                        totalCount: true,
                    },
                },
            },
        };
    },
})
export class NumberOfUsers extends ui.widgets.AbstractWidget {}
