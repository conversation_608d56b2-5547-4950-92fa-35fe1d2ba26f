import * as ui from '@sage/xtrem-ui';

@ui.widgets.table<ListOfUsersTableWithTotalCount>({
    title: 'Users with total count',
    description: 'Detailed list about the current users with total count.',
    cacheLifespan: ui.widgets.WidgetCacheLifespan.day,
    category: 'DEMO_CATEGORY',
    content() {
        return (
            this.$.data.xtremSystem.user.query.edges?.map(({ node, cursor }: any) => ({
                _id: node._id,
                title: node.firstName,
                titleRight: node.lastName,
                line2: node.email,
                line2Right: node.userType,
                image: node.photo?.value,
                cursor,
            })) || []
        );
    },
    totalCount() {
        return this.$.data.xtremSystem.user.query.totalCount;
    },
    callToActions: {
        doSomething: {
            title: 'Do something',
            isHidden() {
                const selectedItems = this.$.options.selectedItems;
                return (selectedItems ?? []).length === 0;
            },
            onClick() {
                this.$.showToast('Well you cannot do much with these users');
            },
        },
        cantDoThis: {
            title: 'Disabled action',
            isDisabled: true,
            onClick() {
                this.$.showToast('Well you cannot do much with these users');
            },
        },
    },
    canSwitchViewMode: true,
    displayMode: 'card',
    canSelect: true,
    dataDropdownMenu: {
        userType: {
            all: { title: 'All users' },
            normal: { title: 'Normal accounts' },
            administrative: { title: 'Administrative accounts' },
            inactive: { title: 'Inactive accounts' },
        },
        orderBy: {
            firstName: { title: 'Sort by first name' },
            lastName: { title: 'Sort by last name' },
            userType: { title: 'Sort by user type' },
        },
    },
    rowDefinition: {
        title: { title: 'First name', displayOptions: { columnWidth: 500 } },
        titleRight: { title: 'Last name', displayOptions: { isLabelDisplayedOnCard: true } },
        line2: {
            title: 'Email',
            renderedAs: 'link',
            onClick(_id: string) {
                this.$.router.goTo('@sage/xtrem-authorization/User', { _id });
            },
        },
        line2Right: {
            title: 'User Type',
            renderedAs: 'pill',
            displayOptions: {
                colorVariant(_id: string) {
                    return Number(_id) % 2 ? 'warning' : 'positive';
                },
                tooltipText(_id: string) {
                    const userNode = this.$.data.xtremSystem.user.query.edges.find(({ node }: any) => node._id === _id);

                    return `This belongs to ${userNode.node.firstName}`;
                },
            },
        },
    },
    rowActions: [
        {
            title: 'Open',
            onClick(_id, row) {
                ui.console.log(_id, row);
                this.$.router.goTo('@sage/xtrem-authorization/User', { _id });
            },
        },
        ui.menuSeparator(),
        {
            title: 'isDisabled',
            isDestructive: true,
            onClick(_id) {
                this.$.router.goTo('@sage/xtrem-authorization/User', { _id });
            },
            isDisabled(_id, row) {
                return row.titleRight === 'Support';
            },
        },
        ui.menuSeparator(),
        {
            title: 'Delete',
            isDestructive: true,
            icon: 'delete',
            onClick(_id) {
                this.$.router.goTo('@sage/xtrem-authorization/User', { _id });
            },
        },
        ui.menuSeparator(),
        {
            title: 'isHidden',
            onClick(_id) {
                this.$.router.goTo('@sage/xtrem-authorization/User', { _id });
            },
            isHidden(_id) {
                return Number(_id) % 2 === 0;
            },
        },
    ],
    getQuery(args) {
        const filter: any = {};
        const dataOptions = this.$.options.dataOptions;
        if (dataOptions?.userType === 'normal') {
            filter.isAdministrator = false;
            filter.isActive = true;
        }

        if (dataOptions?.userType === 'administrative') {
            filter.isAdministrator = true;
        }

        if (dataOptions?.userType === 'inactive') {
            filter.isActive = false;
        }

        const orderBy: any = {};
        if (!dataOptions?.orderBy || dataOptions?.orderBy === 'firstName') {
            orderBy.firstName = 1;
        }

        if (dataOptions?.orderBy === 'lastName') {
            orderBy.lastName = 1;
        }

        if (dataOptions?.userType === 'userType') {
            orderBy.userType = 1;
        }

        return {
            xtremSystem: {
                user: {
                    query: {
                        __args: {
                            filter: JSON.stringify(filter),
                            orderBy: JSON.stringify(orderBy),
                            first: args?.first ?? 20,
                            ...(args?.after?.cursor && { after: args.after.cursor }),
                        },
                        edges: {
                            node: {
                                _id: true,
                                email: true,
                                firstName: true,
                                lastName: true,
                                userType: true,
                                photo: {
                                    value: true,
                                },
                            },
                            cursor: true,
                        },
                        totalCount: true,
                    },
                },
            },
        };
    },
})
export class ListOfUsersTableWithTotalCount extends ui.widgets.TableWidget {}
