import * as ui from '@sage/xtrem-ui';

@ui.widgets.table<ListOfInvoiceLinesTable>({
    title: 'Invoice lines',
    description: 'Invoice lines with dialog page link',
    cacheLifespan: ui.widgets.WidgetCacheLifespan.day,
    category: 'DEMO_CATEGORY',
    content() {
        return (
            this.$.data?.xtremShowCase.showCaseInvoiceLine.query.edges.map(({ node, cursor }: any) => ({
                _id: node._id,
                title: node._id,
                titleRight: node.invoice._id,
                line2: node.product.description,
                line2Right: node.product.product,
                cursor,
            })) || []
        );
    },
    canSwitchViewMode: true,
    displayMode: 'table',
    rowDefinition: {
        title: { title: 'Invoice line ID' },
        titleRight: { title: 'Invoice ID' },
        line2: {
            title: 'Product description',
        },
        line2Right: {
            title: 'Product',
            renderedAs: 'link',
            async onClick(_id: string) {
                const node = this.getNodeFromLineId(_id);
                await this.$.dialog.page(
                    '@sage/xtrem-show-case/StandardShowCaseProduct',
                    { _id: node.product._id },
                    { fullScreen: true },
                );
            },
        },
    },
    rowActions: [],
    getQuery(args) {
        return {
            xtremShowCase: {
                showCaseInvoiceLine: {
                    query: {
                        __args: {
                            first: args?.first ?? 20,
                            ...(args?.after?.cursor && { after: args.after.cursor }),
                        },
                        edges: {
                            node: {
                                _id: true,
                                product: {
                                    _id: true,
                                    product: true,
                                    imageField: {
                                        value: true,
                                    },
                                    description: true,
                                },
                                invoice: {
                                    _id: true,
                                },
                            },
                            cursor: true,
                        },
                    },
                },
            },
        };
    },
})
export class ListOfInvoiceLinesTable extends ui.widgets.TableWidget {
    private getNodeFromLineId(invoiceLineId: string): any {
        return this.$.data.xtremShowCase.showCaseInvoiceLine.query.edges.find(
            (node: any) => node.node._id === invoiceLineId,
        ).node;
    }
}
