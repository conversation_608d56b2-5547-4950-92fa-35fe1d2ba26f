import * as ui from '@sage/xtrem-ui';

const providerBoxWidth = 300;
const providerBoxHeight = 100;

const padding = 20;
const labelHeight = 16;
const imageSize = 60;

@ui.widgets.visualProcess<ProvidersProcess>({
    title: 'Suppliers',
    cacheLifespan: ui.widgets.WidgetCacheLifespan.day,
    category: 'DEMO_CATEGORY',
    content() {
        const edges: { node: any }[] = this.$.data.xtremShowCase.showCaseProvider.query.edges;
        const totalHeight = edges.length * (providerBoxHeight + padding) + padding;
        const totalWidth = providerBoxWidth + padding * 2;
        const body = edges
            .map((e, index) =>
                this.createProviderObject(padding, padding + index * (providerBoxHeight + padding), e.node),
            )
            .reduce((prevValue: ui.VP.XShape[], currentValue: ui.VP.XShape[]) => {
                return [...prevValue, ...currentValue];
            }, [] as ui.VP.XShape[]);

        return {
            acts: { a: [] },
            layersArr: [
                {
                    id: 1,
                    alpha: 1,
                    lock: true,
                    visible: true,
                    xpropsArr: body,
                },
            ],
            contentSize: {
                xheight: totalHeight,
                xwidth: totalWidth,
            },
            currentLayerId: 1,
            docDims: {
                xtop: 0,
                xleft: 0,
                xheight: totalHeight,
                xwidth: totalWidth,
            },
            reachedGroupNum: 1,
        };
    },
    getQuery() {
        return {
            xtremShowCase: {
                showCaseProvider: {
                    query: {
                        edges: {
                            node: {
                                textField: true,
                                _id: true,
                                products: {
                                    query: {
                                        totalCount: true,
                                    },
                                },
                            },
                        },
                    },
                },
            },
        };
    },
})
export class ProvidersProcess extends ui.widgets.AbstractWidget {
    // eslint-disable-next-line class-methods-use-this
    createProviderObject = (startX: number, startY: number, provider: any): ui.VP.XShape[] => {
        const labelY = startY + (providerBoxHeight - labelHeight) / 2;
        const labelX = startX + padding;

        const imageY = startY + (providerBoxHeight - imageSize) / 2;
        const imageX = startX + providerBoxWidth - imageSize - padding;

        const xlinkProps = {
            xcode: '@sage/xtrem-show-case/ShowCaseProvider',
            xparam1: provider._id,
            xtype: 'erpfunc',
        };
        return [
            {
                xstrokeProps: {
                    xend: {
                        xtype: 'none',
                    },
                    xstart: {
                        xtype: 'none',
                    },
                    xalpha: 100,
                    xcolor: ui.tokens.colorsUtilityMajor100,
                    xthickness: 1,
                    xtype: 'solidstroke',
                },
                xfillProps: {
                    xgtype: 'linear',
                    xalpha: 100,
                    xcolor: '#E6EBED',
                    xtype: 'solidfill',
                },
                xlinkProps,
                xanchors: [
                    {
                        type: null,
                        x: startX,
                        y: startY,
                    },
                    {
                        type: null,
                        x: startX + providerBoxWidth,
                        y: startY,
                    },
                    {
                        type: null,
                        x: startX + providerBoxWidth,
                        y: startY + providerBoxHeight,
                    },
                    {
                        type: null,
                        x: startX,
                        y: startY + providerBoxHeight,
                    },
                ],
                xshadowProps: {
                    xtype: 'global',
                },
                xdrawBehaviorCode: 'K_API_RECT',
                xshapeType: 'apishape',
                uniqueID: `provider_main_${provider._id}`,
            } as any,
            {
                xlinkProps,
                xanchors: [
                    {
                        type: null,
                        x: labelX,
                        y: labelY,
                    },
                    {
                        type: null,
                        x: labelX + 200,
                        y: labelY,
                    },
                    {
                        type: null,
                        x: labelX + 200,
                        y: labelY + labelHeight,
                    },
                    {
                        type: null,
                        x: labelX,
                        y: labelY + labelHeight,
                    },
                ],
                xtextFormat: {
                    letterSpacing: 0,
                    display: 'block',
                    blockIndent: 0,
                    leading: 20,
                    indent: 0,
                    rightMargin: 5,
                    leftMargin: 5,
                    align: 'left',
                    url: '',
                    color: '0',
                    size: labelHeight,
                    font: 'Arial',
                },
                xtext: ui.localize(
                    '@sage/xtrem-show-case/provider-product-count',
                    '{{name}}: {{countOfProducts}} products',
                    {
                        name: provider.textField,
                        countOfProducts: provider.products.query.totalCount,
                    },
                ),
                xcaptionProps: {
                    xsizingMode: 'fit',
                    xvertAlignMode: 'left',
                },
                xdrawBehaviorCode: 'K_API_RECT',
                xshapeType: 'apishape',
                uniqueID: `provider_main_${provider._id}_label`,
            },
            {
                xsize: {
                    xwidth: imageSize,
                    xheight: imageSize,
                },
                xcenter: {
                    x: 0,
                    y: 0,
                },
                eltid: 'factory',
                bibid: 'business-icons',
                xlinkProps,
                xanchors: [
                    {
                        x: imageX,
                        y: imageY,
                    },
                    {
                        x: imageX + imageSize,
                        y: imageY,
                    },
                    {
                        x: imageX + imageSize,
                        y: imageY + imageSize,
                    },
                    {
                        x: imageX,
                        y: imageY + imageSize,
                    },
                ],
                xtext: '',
                xcaptionProps: {
                    xsizingMode: 'fit',
                    xvertAlignMode: 'middle',
                },
                xcaptionPos: {
                    xleft: 0,
                    xtop: 0,
                },
                xcaptionSize: {
                    xwidth: 0,
                    xheight: 0,
                },
                xcaptionDeltaPos: {
                    x: 0,
                    y: 0,
                },
                xshadowProps: {
                    xtype: 'none',
                },
                xdrawBehaviorCode: null,
                xshapeType: 'fileshape',
                uniqueID: `provider_main_${provider._id}_image`,
            },
        ];
    };
}
