import { ShowCaseEmployee } from '@sage/xtrem-show-case-api';

import * as ui from '@sage/xtrem-ui';

@ui.decorators.pageFragment<ShowCaseProductPrices>({
    onLoad() {
        console.log('Fragment loaded');
    },
})
export class ShowCaseProductPrices extends ui.PageFragment {
    @ui.decorators.numericField<ShowCaseProductPrices>({
        title: 'Tax',
        scale: 2,
    })
    tax: ui.fields.Numeric;

    @ui.decorators.numericField<ShowCaseProductPrices>({
        title: 'Net price',
        scale: 2,
    })
    netPrice: ui.fields.Numeric;

    @ui.decorators.numericField<ShowCaseProductPrices>({
        title: 'Discount',
    })
    discount: ui.fields.Numeric;

    @ui.decorators.numericField<ShowCaseProductPrices>({
        title: 'Amount',
        scale: 2,
        validation() {
            if (this.amount.value && this.amount.value > 100) {
                return 'Amount must be less than 100.';
            }
            return undefined;
        },
    })
    amount: ui.fields.Numeric;

    @ui.decorators.numericField<ShowCaseProductPrices>({
        title: 'List price',
        scale: 2,
        fetchesDefaults: true,
    })
    listPrice: ui.fields.Numeric;

    @ui.decorators.referenceField<ShowCaseProductPrices, ShowCaseEmployee>({
        bind: 'designerEmployee',
        node: '@sage/xtrem-show-case/ShowCaseEmployee',
        tunnelPage: '@sage/xtrem-show-case/ShowCaseEmployee',
        title: 'Product designed by',
        valueField: 'firstName',
        helperTextField: 'lastName',
        validation() {
            if (this.designerEmployee.value?.firstName === 'John') {
                return 'No Johns allowed.';
            }
            return undefined;
        },
        onChange() {
            this.$.showToast('The designer employee field was changed.');
        },
        columns: [
            ui.nestedFields.text({ bind: 'firstName', title: 'First name' }),
            ui.nestedFields.text({ bind: 'lastName', title: 'Last name' }),
        ],
        createTunnelLinkText: 'Create a new employee',
    })
    designerEmployee: ui.fields.Reference<ShowCaseEmployee>;
}
