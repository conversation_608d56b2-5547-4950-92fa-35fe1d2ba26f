import { ReferenceDataType } from '@sage/xtrem-core';
import { ShowCaseEmployee } from '../nodes/show-case-employee';

export const showCaseEmployee = new ReferenceDataType({
    reference: () => ShowCaseEmployee,
    isDefault: true,
    lookup: {
        valuePath: 'firstName',
        helperTextPath: 'lastName',
        columnPaths: ['firstName', 'lastName', 'email'],
        tunnelPage: '@sage/xtrem-show-case/ShowCaseEmployee',
    },
});
