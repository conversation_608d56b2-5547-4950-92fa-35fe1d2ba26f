import { ReferenceDataType } from '@sage/xtrem-core';
import { ShowCaseProvider } from '../nodes/show-case-provider';

export const showCaseProvider = new ReferenceDataType({
    reference: () => ShowCaseProvider,
    isDefault: true,
    lookup: {
        valuePath: 'textField',
        columnPaths: ['textField', 'integerField', 'dateField'],
        tunnelPage: '@sage/xtrem-show-case/StandardShowCaseProvider',
        imageFieldPath: 'logo',
    },
});
