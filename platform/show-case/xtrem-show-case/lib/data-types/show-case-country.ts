import { ReferenceDataType } from '@sage/xtrem-core';
import { ShowCaseCountry } from '../nodes/show-case-country';

export const showCaseCountry = new ReferenceDataType({
    reference: () => ShowCaseCountry,
    isDefault: true,
    lookup: {
        valuePath: 'name',
        helperTextPath: 'code',
        columnPaths: ['name', 'code', 'phoneCountryCode'],
        tunnelPage: '@sage/xtrem-show-case/ShowCaseCountry',
    },
});
