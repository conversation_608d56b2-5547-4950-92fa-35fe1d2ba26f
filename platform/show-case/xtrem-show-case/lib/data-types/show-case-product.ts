import { ReferenceDataType } from '@sage/xtrem-core';
import { ShowCaseProduct } from '../nodes/show-case-product';

export const showCaseProduct = new ReferenceDataType({
    reference: () => ShowCaseProduct,
    isDefault: true,
    lookup: {
        valuePath: 'product',
        helperTextPath: 'description',
        columnPaths: [
            'product',
            'description',
            'hotProduct',
            'qty',
            'category',
            'releaseDate',
            'email',
            'amount',
            'tax',
            'total',
        ],
        tunnelPage: '@sage/xtrem-show-case/StandardShowCaseProduct',
        imageFieldPath: 'imageField',
    },
});
