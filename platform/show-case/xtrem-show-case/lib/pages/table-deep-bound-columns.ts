import {
    <PERSON>raph<PERSON><PERSON>,
    <PERSON>Case<PERSON>ou<PERSON>ry,
    ShowCaseProduct,
    ShowCaseProductOriginAddress,
    ShowCaseProvider,
} from '@sage/xtrem-show-case-api';
import { setOrderOfPageHeaderDropDownActions } from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import * as ui from '@sage/xtrem-ui';
import { tableField } from '../menu-items/table-field';

@ui.decorators.page<TableDeepBoundColumns, ShowCaseProvider>({
    authorizationCode: 'SHCPRVD',
    category: 'SHOWCASE',
    createAction() {
        return this.$standardNewAction;
    },
    headerDropDownActions() {
        return setOrderOfPageHeaderDropDownActions({
            remove: [this.$standardDeleteAction],
            dropDownBusinessActions: [this.$standardOpenRecordHistoryAction],
        });
    },
    businessActions() {
        return [this.$standardCancelAction, this.$standardSaveAction];
    },
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.text({ bind: 'textField' }),
            titleRight: ui.nestedFields.text({ bind: '_id' }),
        },
    },
    node: '@sage/xtrem-show-case/ShowCaseProvider',
    title: 'Table - Deep cell binding',
    menuItem: tableField,
})
export class TableDeepBoundColumns extends ui.Page<GraphApi> {
    @ui.decorators.section<TableDeepBoundColumns>({})
    section: ui.containers.Section;

    @ui.decorators.block<TableDeepBoundColumns>({
        parent() {
            return this.section;
        },
    })
    block: ui.containers.Block;

    @ui.decorators.textField<TableDeepBoundColumns>({
        parent() {
            return this.block;
        },
        title: 'Id',
        isReadOnly: true,
    })
    _id: ui.fields.Text;

    @ui.decorators.dateField<TableDeepBoundColumns>({
        parent() {
            return this.block;
        },
        title: 'Date',
    })
    dateField: ui.fields.Date;

    @ui.decorators.checkboxField<TableDeepBoundColumns>({
        parent() {
            return this.block;
        },
        title: 'Checkbox',
    })
    booleanField: ui.fields.Checkbox;

    @ui.decorators.referenceField<TableDeepBoundColumns, ShowCaseProduct>({
        parent() {
            return this.block;
        },
        title: 'Flagship product',
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        orderBy: { description: 1 },
        columns: [
            ui.nestedFields.text({ bind: '_id', isReadOnly: true, title: 'Id' }),
            ui.nestedFields.text({ bind: 'description', title: 'Description' }),
            ui.nestedFields.checkbox({ bind: 'hotProduct', isReadOnly: true, title: 'Hot' }),
            ui.nestedFields.select({ bind: 'category', isReadOnly: true, title: 'Category' }),
            ui.nestedFields.reference<TableDeepBoundColumns, ShowCaseProduct, ShowCaseProductOriginAddress>({
                bind: 'originAddress',
                title: 'Origin Address',
                node: '@sage/xtrem-show-case/ShowCaseProductOriginAddress',
                valueField: 'name',
            }),
            ui.nestedFields.numeric({ bind: 'total', title: 'Net Price', scale: 2 }),
        ],
        valueField: 'description',
        helperTextField: '_id',
        additionalLookupRecords() {
            return this.products.getNewRecords();
        },
    })
    flagshipProduct: ui.fields.Reference;

    @ui.decorators.tableField<TableDeepBoundColumns, ShowCaseProduct>({
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        parent() {
            return this.block;
        },
        fieldActions() {
            return [this.addNewRecord];
        },
        title: 'Products',
        orderBy: { _id: 1 },
        columns: [
            ui.nestedFields.text({ bind: '_id', isReadOnly: true, title: 'Id' }),
            ui.nestedFields.link({
                bind: 'product',
                title: 'Product',
                onClick(_id) {
                    this.$.router.goTo('@sage/xtrem-show-case/ShowCaseProduct', { _id });
                },
            }),
            ui.nestedFields.text({ bind: 'description', title: 'Description', isMandatory: true }),
            ui.nestedFields.checkbox({ bind: 'hotProduct', isReadOnly: true, title: 'Hot' }),
            ui.nestedFields.select({ bind: 'category', isReadOnly: true, title: 'Category' }),
            ui.nestedFields.numeric({ bind: 'total', title: 'Net Price', scale: 2 }),
            ui.nestedFields.text({
                bind: { originAddress: { name: true } },
                title: 'Address name',
                validation(v) {
                    if (v === 'Test name') {
                        return 'Test name is not a valid name';
                    }
                    return undefined;
                },
            }),
            ui.nestedFields.text({ bind: { originAddress: { addressLine1: true } }, title: 'Address line 1' }),
            ui.nestedFields.text({ bind: { originAddress: { addressLine2: true } }, title: 'Address line 2' }),
            ui.nestedFields.text({ bind: { originAddress: { city: true } }, title: 'City' }),
            ui.nestedFields.reference<TableDeepBoundColumns, ShowCaseProduct, ShowCaseCountry>({
                bind: { originAddress: { country: true } },
                node: '@sage/xtrem-show-case/ShowCaseCountry',
                valueField: 'name',
                isAutoSelectEnabled: true,
                helperTextField: 'code',
                title: 'Country',
                columns: [
                    ui.nestedFields.text({ bind: 'name', title: 'Name' }),
                    ui.nestedFields.text({ bind: 'code', title: 'Code' }),
                    ui.nestedFields.text({ bind: 'phoneCountryCode', title: 'Tel Country code' }),
                ],
            }),
            ui.nestedFields.filterSelect<TableDeepBoundColumns, ShowCaseProduct, ShowCaseCountry>({
                bind: { originAddress: { country: { code: true } } },
                minLookupCharacters: 0,
                isHiddenOnMainField: true,
                valueField: 'code',
                node: '@sage/xtrem-show-case/ShowCaseCountry',
                title: 'Country code',
            }),
        ],
        inlineActions: [
            {
                icon: 'box_arrow_left',
                title: 'Edit on sidebar',
                async onClick(rowId: any) {
                    this.products.openSidebar(rowId);
                },
            },
        ],
        dropdownActions: [
            {
                icon: 'bin',
                title: 'Remove',
                isDestructive: true,
                async onClick(rowId: any, data: any) {
                    await this.$.graph.delete({
                        _id: data._id,
                        nodeName: '@sage/xtrem-show-case/ShowCaseProduct',
                    });
                    await this.products.refresh();
                },
            },
        ],
        sidebar: {
            title(_id, recordValue) {
                return recordValue.product;
            },
            layout() {
                return {
                    mainSection: {
                        title: 'Main section',
                        blocks: {
                            mainBlock: {
                                title: 'Product',
                                fields: ['product', 'category', 'total', 'description'],
                            },
                            anotherBlock: {
                                title: 'Origin address',
                                fields: [
                                    { originAddress: { addressLine1: true } },
                                    { originAddress: { addressLine2: true } },
                                    { originAddress: { city: true } },
                                    { originAddress: { country: true } },
                                    { originAddress: { country: { code: true } } },
                                ] as any, // TODO: CHECK TYPING HERE
                            },
                        },
                    },
                };
            },
        },
    })
    products: ui.fields.Table<ShowCaseProduct, TableDeepBoundColumns>;

    @ui.decorators.pageAction<TableDeepBoundColumns>({
        title: 'Add new record',
        icon: 'add',
        onError: () => '',
        async onClick() {
            const result = await this.$.dialog.page(
                '@sage/xtrem-show-case/ShowCaseProductDialog',
                {},
                { rightAligned: true },
            );
            this.products.addRecord(result);
        },
    })
    addNewRecord: ui.PageAction;
}
