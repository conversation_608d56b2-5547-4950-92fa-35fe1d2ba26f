import { <PERSON><PERSON>h<PERSON><PERSON>, ShowCaseProduct as ShowCaseProductNode, ShowCaseProvider } from '@sage/xtrem-show-case-api';
import { setApplicativePageCrudActions } from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import * as ui from '@sage/xtrem-ui';
import { misc } from '../menu-items/misc';

@ui.decorators.page<PageDefaultCrudActions, ShowCaseProductNode>({
    authorizationCode: 'SHCPRDT',
    module: 'show-case',
    title: 'Page - Default CRUD Actions',
    node: '@sage/xtrem-show-case/ShowCaseProduct',
    category: 'SHOWCASE',
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.text({ bind: 'product' }),
            titleRight: ui.nestedFields.text({ bind: '_id' }),
            line2: ui.nestedFields.text({ bind: 'description', canFilter: false }),
        },
    },
    menuItem: misc,
    createAction() {
        return this.$standardNewAction;
    },
    headerDropDownActions() {
        return [this.$standardDeleteAction];
    },
    headerQuickActions() {
        return [this.$standardDuplicateAction];
    },
    businessActions() {
        return [this.$standardCancelAction, this.$standardSaveAction];
    },
    onLoad() {
        setApplicativePageCrudActions({
            page: this,
            isDirty: false,
            save: this.$standardSaveAction,
            cancel: this.$standardCancelAction,
            duplicate: this.$standardDuplicateAction,
            remove: this.$standardDeleteAction,
        });
    },
    onDirtyStateUpdated(isDirty: boolean) {
        setApplicativePageCrudActions({
            page: this,
            isDirty,
            save: this.$standardSaveAction,
            cancel: this.$standardCancelAction,
            duplicate: this.$standardDuplicateAction,
            remove: this.$standardDeleteAction,
        });
    },
})
export class PageDefaultCrudActions extends ui.Page<GraphApi> {
    override $standardDeletePromptTitle = 'Overridden dialog title';

    @ui.decorators.section<PageDefaultCrudActions>({
        isTitleHidden: true,
    })
    section: ui.containers.Section;

    @ui.decorators.block<PageDefaultCrudActions>({
        parent() {
            return this.section;
        },
    })
    block: ui.containers.Block;

    @ui.decorators.textField<PageDefaultCrudActions>({
        parent() {
            return this.block;
        },
        title: 'Product',
        bind: 'product',
        isMandatory: true,
    })
    product1: ui.fields.Text;

    @ui.decorators.textField<PageDefaultCrudActions>({
        parent() {
            return this.block;
        },
        title: 'Description',
        bind: 'description',
    })
    description1: ui.fields.Text;

    @ui.decorators.checkboxField<PageDefaultCrudActions>({
        parent() {
            return this.block;
        },
        title: 'Hot product',
    })
    hotProduct: ui.fields.Checkbox;

    @ui.decorators.numericField<PageDefaultCrudActions>({
        parent() {
            return this.block;
        },
        title: 'Quantity',
        fetchesDefaults: true,
    })
    qty: ui.fields.Numeric;

    @ui.decorators.numericField<PageDefaultCrudActions>({
        parent() {
            return this.block;
        },
        title: 'Stock',
    })
    st: ui.fields.Numeric;

    @ui.decorators.numericField<PageDefaultCrudActions>({
        parent() {
            return this.block;
        },
        title: 'List price',
        fetchesDefaults: true,
        scale: 2,
    })
    listPrice: ui.fields.Numeric;

    @ui.decorators.numericField<PageDefaultCrudActions>({
        parent() {
            return this.block;
        },
        title: 'Tax',
        scale: 2,
    })
    tax: ui.fields.Numeric;

    @ui.decorators.numericField<PageDefaultCrudActions>({
        parent() {
            return this.block;
        },
        title: 'Amount',
        scale: 2,
    })
    amount: ui.fields.Numeric;

    @ui.decorators.numericField<PageDefaultCrudActions>({
        parent() {
            return this.block;
        },
        title: 'Net price',
        scale: 2,
    })
    netPrice: ui.fields.Numeric;

    @ui.decorators.referenceField<PageDefaultCrudActions, ShowCaseProvider>({
        parent() {
            return this.block;
        },
        columns: [ui.nestedFields.text({ bind: 'textField' })],
        node: '@sage/xtrem-show-case/ShowCaseProvider',
        title: 'Provider',
        valueField: 'textField',
        helperTextField: '_id',
        minLookupCharacters: 0,
    })
    provider: ui.fields.Reference;

    @ui.decorators.dateField<PageDefaultCrudActions>({
        parent() {
            return this.block;
        },
        title: 'Release date',
    })
    releaseDate: ui.fields.Date;

    @ui.decorators.dateField<PageDefaultCrudActions>({
        parent() {
            return this.block;
        },
        title: 'Ending date',
    })
    endingDate: ui.fields.Date;

    @ui.decorators.radioField<PageDefaultCrudActions>({
        parent() {
            return this.block;
        },
        title: 'Category Select',
        optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
    })
    category: ui.fields.Radio;

    @ui.decorators.numericField<PageDefaultCrudActions>({
        parent() {
            return this.block;
        },
        title: 'Discount',
    })
    discount: ui.fields.Numeric;

    @ui.decorators.numericField<PageDefaultCrudActions>({
        parent() {
            return this.block;
        },
        title: 'Fixed Quantity',
        isTransientInput: true,
    })
    fixedQuantity: ui.fields.Numeric;

    @ui.decorators.block<PageDefaultCrudActions>({
        width: 'small',
        parent() {
            return this.section;
        },
    })
    singleFieldDefaultValues: ui.containers.Block;

    @ui.decorators.checkboxField<PageDefaultCrudActions>({
        parent() {
            return this.singleFieldDefaultValues;
        },
        title: 'Should skip value update?',
        helperText: 'If ticked the value will not be updated',
        isTransient: true,
    })
    singleFieldDefaultCheckbox: ui.fields.Checkbox;

    @ui.decorators.buttonField<PageDefaultCrudActions>({
        parent() {
            return this.singleFieldDefaultValues;
        },
        isTransient: true,
        map() {
            return 'Fetch one field';
        },
        async onClick() {
            const result = await this.netPrice.fetchDefault(this.singleFieldDefaultCheckbox.value);
            this.$.showToast(`The value is ${result}`, { type: 'info' });
        },
        title: 'Force-fetch defaults',
        helperText: 'Fetches defaults for the net price',
    })
    singleFieldDefaultButton: ui.fields.Button;

    @ui.decorators.block<PageDefaultCrudActions>({
        width: 'small',
        parent() {
            return this.section;
        },
    })
    multipleFieldDefaultValues: ui.containers.Block;

    @ui.decorators.checkboxField<PageDefaultCrudActions>({
        parent() {
            return this.multipleFieldDefaultValues;
        },
        title: 'Should skip value update?',
        helperText: 'If ticked the value will not be updated',
        isTransient: true,
    })
    multipleFieldDefaultCheckbox: ui.fields.Checkbox;

    @ui.decorators.buttonField<PageDefaultCrudActions>({
        parent() {
            return this.multipleFieldDefaultValues;
        },
        isTransient: true,
        map() {
            return 'Fetch two fields';
        },
        async onClick() {
            const result = await this.$.fetchDefaults(
                [this.st.id, this.netPrice],
                this.multipleFieldDefaultCheckbox.value,
            );
            this.$.showToast(`Stock: ${result.st}, net price: ${result.netPrice}`, { type: 'info' });
        },
        title: 'Force-fetch defaults',
        helperText: 'Fetches defaults for the net price and stock',
    })
    multipleFieldDefaultButton: ui.fields.Button;
}
