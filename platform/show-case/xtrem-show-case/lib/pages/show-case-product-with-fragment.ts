import { Graph<PERSON><PERSON>, ShowCaseProduct as ShowCaseProductNode, ShowCaseProvider } from '@sage/xtrem-show-case-api';
import {
    setApplicativePageCrudActions,
    setOrderOfPageHeaderDropDownActions,
} from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import * as ui from '@sage/xtrem-ui';
import { applicationPages } from '../menu-items/application-pages';

@ui.decorators.page<ShowCaseProductWithFragment, ShowCaseProductNode>({
    authorizationCode: 'SHCPRVD',
    category: 'SHOWCASE',
    title: 'ShowCase Product with Fragment',
    menuItem: applicationPages,
    objectTypeSingular: 'Product',
    objectTypePlural: 'Products',
    idField() {
        return this._id;
    },
    businessActions() {
        return [this.$standardCancelAction, this.$standardSaveAction];
    },
    headerQuickActions() {
        return [this.$standardDuplicateAction];
    },
    headerDropDownActions() {
        return setOrderOfPageHeaderDropDownActions({
            remove: [this.$standardDeleteAction],
            dropDownBusinessActions: [this.$standardOpenRecordHistoryAction],
        });
    },
    headerLabel() {
        return this.category;
    },
    module: 'show-case',
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.text({ bind: 'product', title: 'Product' }),
            titleRight: ui.nestedFields.text({ bind: '_id', title: 'ID', canFilter: false }),
            line2: ui.nestedFields.text({ bind: 'description', title: 'Description', canFilter: true }),
            listPrice: ui.nestedFields.numeric({ bind: 'listPrice', title: 'List Price', isHiddenOnMainField: true }),
        },
    },
    node: '@sage/xtrem-show-case/ShowCaseProduct',
    priority: 500,
    onLoad() {
        setApplicativePageCrudActions({
            page: this,
            isDirty: false,
            save: this.$standardSaveAction,
            cancel: this.$standardCancelAction,
            duplicate: this.$standardDuplicateAction,
            remove: this.$standardDeleteAction,
            actions: [],
        });
    },
    onDirtyStateUpdated(isDirty: boolean) {
        setApplicativePageCrudActions({
            page: this,
            isDirty,
            save: this.$standardSaveAction,
            cancel: this.$standardCancelAction,
            duplicate: this.$standardDuplicateAction,
            remove: this.$standardDeleteAction,
            actions: [],
        });
    },
})
export class ShowCaseProductWithFragment extends ui.Page<GraphApi> {
    @ui.decorators.labelField<ShowCaseProductWithFragment>({
        parent() {
            return this.block;
        },
        title: 'Category',
        optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
    })
    category: ui.fields.Label;

    @ui.decorators.section<ShowCaseProductWithFragment>({})
    section: ui.containers.Section;

    @ui.decorators.block<ShowCaseProductWithFragment>({
        parent() {
            return this.section;
        },
    })
    block: ui.containers.Block;

    @ui.decorators.textField<ShowCaseProductWithFragment>({
        parent() {
            return this.block;
        },
        title: 'ID',
        isReadOnly: true,
    })
    _id: ui.fields.Text;

    @ui.decorators.textField<ShowCaseProductWithFragment>({
        parent() {
            return this.block;
        },
        title: 'Product',
        isMandatory: true,
    })
    product: ui.fields.Text;

    @ui.decorators.textField<ShowCaseProductWithFragment>({
        parent() {
            return this.block;
        },
        title: 'Description',
    })
    description: ui.fields.Text;

    @ui.decorators.checkboxField<ShowCaseProductWithFragment>({
        parent() {
            return this.block;
        },
        title: 'Hot product',
    })
    hotProduct: ui.fields.Checkbox;

    @ui.decorators.numericField<ShowCaseProductWithFragment>({
        parent() {
            return this.block;
        },
        title: 'Quantity',
        fetchesDefaults: true,
    })
    qty: ui.fields.Numeric;

    @ui.decorators.numericField<ShowCaseProductWithFragment>({
        parent() {
            return this.block;
        },
        title: 'Stock',
    })
    st: ui.fields.Numeric;

    @ui.decorators.progressField<ShowCaseProductWithFragment>({
        parent() {
            return this.block;
        },
        title: 'Progress',
    })
    progress: ui.fields.Progress;

    @ui.decorators.fragmentFields<ShowCaseProductWithFragment>({
        parent() {
            return this.block;
        },
        fragment: '@sage/xtrem-show-case/ShowCaseProductPrices',
    })
    fragmentFields: ui.containers.FragmentFields;

    @ui.decorators.buttonField<ShowCaseProductWithFragment>({
        parent() {
            return this.block;
        },
        isTransient: true,
        map() {
            return 'Show/Hide Price fragment';
        },
        title: 'Show prices',
        onClick() {
            this.fragmentFields.isHidden = !this.fragmentFields.isHidden;
        },
    })
    showPrices: ui.fields.Button;

    @ui.decorators.buttonField<ShowCaseProductWithFragment>({
        parent() {
            return this.block;
        },
        isTransient: true,
        map() {
            return 'Validate Prices Fragment';
        },
        title: 'Validate prices',
        onClick() {
            this.fragmentFields.validate();
        },
    })
    validatePrices: ui.fields.Button;

    @ui.decorators.referenceField<ShowCaseProductWithFragment, ShowCaseProvider>({
        parent() {
            return this.block;
        },
        columns: [ui.nestedFields.text({ bind: 'textField' })],
        node: '@sage/xtrem-show-case/ShowCaseProvider',
        title: 'Provider',
        valueField: 'textField',
        helperTextField: '_id',
        minLookupCharacters: 0,
        tunnelPage: '@sage/xtrem-show-case/StandardShowCaseProvider',
    })
    provider: ui.fields.Reference;

    @ui.decorators.dateField<ShowCaseProductWithFragment>({
        parent() {
            return this.block;
        },
        title: 'Release date',
    })
    releaseDate: ui.fields.Date;

    @ui.decorators.dateField<ShowCaseProductWithFragment>({
        parent() {
            return this.block;
        },
        title: 'Ending date',
    })
    endingDate: ui.fields.Date;
}
