import { ShowCase<PERSON>ust<PERSON>, ShowCaseProduct, ShowCaseProvider } from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import { tableField } from '../menu-items/_index';

@ui.decorators.page<TableWithOptionsMenu, ShowCaseProvider>({
    authorizationCode: 'BSCFLDS',
    defaultEntry: () => '2',
    module: 'show-case',
    category: 'SHOWCASE',
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.text({ bind: 'textField' }),
            titleRight: ui.nestedFields.text({ bind: '_id' }),
        },
        optionsMenu: [
            {
                title: 'All items',
                graphQLFilter: {},
            },
            {
                title: 'Amazon',
                graphQLFilter: { textField: { _eq: 'Amazon' } },
            },
        ],
    },
    node: '@sage/xtrem-show-case/ShowCaseProvider',
    onLoad() {
        this.calculateTableTotals();
        this.canSelect.value = true;
        this.canUserHideColumns.value = true;
        this.mediumValueLimit.value = 5;
        this.highValueLimit.value = 20;
        this.quantityValuePrefix.value = '';
        this.quantityValueScale.value = 0;
        this.canEditDescription.value = true;
        this.columnTitleProperty = 'Dynamically allocated title';
        this.field.hideColumn('description');
        this.columnsList.value = 'description';
    },
    subtitle: 'Complex table examples',
    title: 'Field - Table',
    businessActions() {
        return [this.saveTable];
    },
    headerLabel() {
        return this.headerLabel;
    },
    menuItem: tableField,
})
export class TableWithOptionsMenu extends ui.Page {
    // @ts-ignore
    private columnTitleProperty: string;

    @ui.decorators.labelField<TableWithOptionsMenu>({
        isTransient: true,
        map() {
            return 'Title label info';
        },
        onClick() {
            console.log('Doing something when the label field is clicked');
        },
    })
    headerLabel: ui.fields.Label;

    @ui.decorators.section<TableWithOptionsMenu>({
        title: 'Table field',
    })
    section: ui.containers.Section;

    @ui.decorators.block<TableWithOptionsMenu>({
        parent() {
            return this.section;
        },
        title: 'Field example',
    })
    fieldBlock: ui.containers.Block;

    @ui.decorators.numericField<TableWithOptionsMenu>({
        parent() {
            return this.fieldBlock;
        },
        title: 'Selected items total',
        isReadOnly: true,
        scale: 2,
        prefix: '$',
        isTransient: true,
    })
    tableSelectedTotal: ui.fields.Numeric;

    @ui.decorators.numericField<TableWithOptionsMenu>({
        parent() {
            return this.fieldBlock;
        },
        isTransient: true,
        title: 'Items total',
        isReadOnly: true,
        scale: 2,
        prefix: '$',
    })
    tableSampleTotal: ui.fields.Numeric;

    @ui.decorators.buttonField<TableWithOptionsMenu>({
        parent() {
            return this.fieldBlock;
        },
        isTransient: true,
        async onClick() {
            await this.field.refresh();
        },
        map() {
            return 'Refresh Table';
        },
    })
    refreshTable: ui.fields.Button;

    @ui.decorators.buttonField<TableWithOptionsMenu>({
        parent() {
            return this.fieldBlock;
        },
        onClick() {
            const rowData = {
                product: 'Aaaaaaaa',
                description: 'aaaaaaa',
                st: 1,
                qty: 1,
                listPrice: '1.1',
                tax: '1',
                amount: '1',
                releaseDate: '2020-08-04',
            };
            this.field.addRecord(rowData);
        },
        isTransient: true,
        map() {
            return 'Add New Item';
        },
    })
    addNewItem: ui.fields.Button;

    @ui.decorators.buttonField<TableWithOptionsMenu>({
        parent() {
            return this.fieldBlock;
        },
        onClick() {
            this.field.addRecordWithDefaults();
        },
        isTransient: true,
        map() {
            return 'Add New Item With Defaults';
        },
    })
    addNewItemWithDefaults: ui.fields.Button;

    @ui.decorators.buttonField<TableWithOptionsMenu>({
        parent() {
            return this.fieldBlock;
        },
        onClick() {
            this.field.unselectAllRecords();
        },
        isTransient: true,
        map() {
            return 'Unselect all items';
        },
    })
    unselectAllRecords: ui.fields.Button;

    @ui.decorators.buttonField<TableWithOptionsMenu>({
        parent() {
            return this.fieldBlock;
        },
        onClick() {
            this.field.unselectRecord('320');
        },
        isTransient: true,
        map() {
            return 'Unselect Item 320';
        },
    })
    unselectRecord: ui.fields.Button;

    @ui.decorators.buttonField<TableWithOptionsMenu>({
        parent() {
            return this.fieldBlock;
        },
        onClick() {
            this.field.selectRecord('320');
        },
        isTransient: true,
        map() {
            return 'Select Item 320';
        },
    })
    selectRecord: ui.fields.Button;

    @ui.decorators.buttonField<TableWithOptionsMenu>({
        parent() {
            return this.fieldBlock;
        },
        onClick() {
            this.calculateSelectedTotal();
        },
        isTransient: true,
        map() {
            return 'Recalculate selected totals';
        },
    })
    recalculateTotal: ui.fields.Button;

    @ui.decorators.buttonField<TableWithOptionsMenu>({
        parent() {
            return this.fieldBlock;
        },
        onClick() {
            this.field.filter = { qty: { _gt: 16 } };
        },
        isTransient: true,
        map() {
            return 'Filter Quantity > 16';
        },
    })
    filterTableButton: ui.fields.Button;

    @ui.decorators.tableField<TableWithOptionsMenu, ShowCaseProduct>({
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        bind: 'products',
        mobileCard: {
            title: ui.nestedFields.date<TableWithOptionsMenu, ShowCaseProduct>({
                bind: 'releaseDate',
                title: 'Date',
            }),
            titleRight: ui.nestedFields.label<TableWithOptionsMenu, ShowCaseProduct>({
                bind: 'amount',
                title: 'Amount',
                prefix: '$',
                isHiddenDesktop: false,
            }),
            line2: ui.nestedFields.select<TableWithOptionsMenu, ShowCaseProduct>({
                bind: 'category',
                title: 'Category',
                optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
            }),
            line2Right: ui.nestedFields.reference<TableWithOptionsMenu, ShowCaseProduct, ShowCaseProvider>({
                bind: 'provider',
                title: 'Provider',
                valueField: 'textField',
                node: '@sage/xtrem-show-case/ShowCaseProvider',
                minLookupCharacters: 0,
                columns: [
                    ui.nestedFields.text({ bind: '_id', title: 'ID', canFilter: false }),
                    ui.nestedFields.text({ bind: 'textField', title: 'Provider', canFilter: true }),
                    ui.nestedFields.image({ bind: 'logo', title: 'Logo', canFilter: false }),
                ],
                imageField: 'logo',
                onOpenLookupDialog() {
                    console.log('reference lookup dialog open');
                },
                onCloseLookupDialog() {
                    console.log('reference lookup dialog close');
                },
                onClick(id: string, raw: any) {
                    ui.console.log('CLICKED LOOKUP', { id, raw });
                },
                onChange(id: string, raw: any) {
                    ui.console.log('CHANGED LOOKUP', { id, raw });
                },
            }),
        },
        hasSearchBoxMobile: true,
        canExport: true,
        canResizeColumns: true,
        columns: [
            ui.nestedFields.text({
                bind: '_id',
                title: 'Id',
                isHiddenDesktop: false,
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                bind: 'product',
                title: 'Product',
            }),
            ui.nestedFields.text({
                bind: 'description',
                title: 'Description',
                isDisabled() {
                    return !this.canEditDescription.value;
                },
            }),
            ui.nestedFields.numeric({
                bind: 'qty',
                title: 'Quantity',
                onChange(_id: number, rowData: ui.PartialNodeWithId<ShowCaseProduct>) {
                    this.updateRow(_id, rowData);
                },
                prefix() {
                    return this.quantityValuePrefix.value || '';
                },
                scale() {
                    return this.quantityValueScale.value || 0;
                },
                fetchesDefaults: true,
            }),
            ui.nestedFields.numeric({
                bind: 'fixedQuantity',
                title: 'Fixed Quantity',
                isTransientInput: true,
            }),
            ui.nestedFields.numeric({
                bind: 'listPrice',
                title: 'List Price',
                scale: 2,
                onChange(_id: number, rowData: ui.PartialNodeWithId<ShowCaseProduct>) {
                    this.updateRow(_id, rowData);
                },
                isHiddenDesktop: false,
                fetchesDefaults: true,
            }),
            ui.nestedFields.numeric({
                bind: 'netPrice',
                title: 'Net Price',
                isReadOnly: (value: number, rowValue: any) => {
                    return !!rowValue.hotProduct;
                },
                scale: 2,
                isHiddenDesktop: false,
            }),
            ui.nestedFields.label({
                backgroundColor(_fieldValue: number, rowData: ShowCaseProduct) {
                    return rowData.qty > this.highValueLimit.value
                        ? '00b000'
                        : rowData.qty > this.mediumValueLimit.value
                          ? 'ffb700'
                          : 'e96400';
                },
                bind: 'qty',
                isTransient: true,
                map(_fieldValue, rowData) {
                    return rowData.qty > this.highValueLimit.value
                        ? 'High'
                        : rowData.qty > this.mediumValueLimit.value
                          ? 'Medium'
                          : 'Low';
                },
                title: 'Indicator',
            }),
            ui.nestedFields.label({
                bind: 'st',
                onClick(_id: string | number, rowData) {
                    this.$.dialog.message('info', 'Label was clicked', `Label ${rowData.st} was clicked (row ${_id})`);
                },
                title: 'St',
            }),
            ui.nestedFields.checkbox({
                bind: 'hotProduct',
                title: 'Hot',
            }),
            ui.nestedFields.switch({
                bind: 'hotProduct',
                title: 'Hot (Switch)',
            }),
            ui.nestedFields.link({
                bind: 'product',
                isTransient: true,
                map(_fieldValue, rowData) {
                    return `http://${(rowData?.product ?? ('' as string))
                        .replace(/-|_|\s/g, '')
                        .substring(0, 4)
                        .toLowerCase()}.sage.com`;
                },
                onClick(id: string, raw: any) {
                    ui.console.log('CLICKED LINK', { id, raw });
                },
                title: 'Link',
                page: 'https://www.google.com',
            }),
            ui.nestedFields.progress({
                bind: 'progress',
                title: 'Progress',
            }),
            ui.nestedFields.numeric({
                bind: 'tax',
                title: 'Tax',
                prefix: 'T',
                isMandatory: true,
                scale: 2,
                isHiddenDesktop: false,
            }),
            ui.nestedFields.label<TableWithOptionsMenu, ShowCaseProduct>({
                bind: 'amount',
                title: 'Amount',
                prefix: '$',
                isHiddenDesktop: false,
            }),
            ui.nestedFields.reference<TableWithOptionsMenu, ShowCaseProduct, ShowCaseProvider>({
                bind: 'provider',
                title: 'Provider',
                valueField: 'textField',
                node: '@sage/xtrem-show-case/ShowCaseProvider',
                minLookupCharacters: 0,
                imageField: 'logo',
                onClick(id: string, raw: any) {
                    ui.console.log('CLICKED NO LOOKUP', { id, raw });
                },
                onChange(id: string, raw: any) {
                    ui.console.log('CHANGED NO LOOKUP', { id, raw });
                    this.$.showToast(`New provider set: ${raw?.provider?.textField ?? ''}`);
                },
            }),
            ui.nestedFields.reference<TableWithOptionsMenu, ShowCaseProduct, ShowCaseProvider>({
                bind: 'provider',
                title: 'Provider',
                valueField: 'textField',
                node: '@sage/xtrem-show-case/ShowCaseProvider',
                minLookupCharacters: 0,
                columns: [
                    ui.nestedFields.text({ bind: '_id', title: 'ID', canFilter: false }),
                    ui.nestedFields.text({ bind: 'textField', title: 'Provider', canFilter: true }),
                    ui.nestedFields.image({ bind: 'logo', title: 'Logo', canFilter: false }),
                ],
                imageField: 'logo',
                onOpenLookupDialog() {
                    console.log('reference lookup dialog open');
                },
                onCloseLookupDialog() {
                    console.log('reference lookup dialog close');
                },
                onClick(id: string, raw: any) {
                    ui.console.log('CLICKED LOOKUP', { id, raw });
                },
                onChange(id: string, raw: any) {
                    ui.console.log('CHANGED LOOKUP', { id, raw });
                },
            }),
            ui.nestedFields.filterSelect<TableWithOptionsMenu, ShowCaseProduct, ShowCaseCustomer>({
                bind: 'email',
                title: 'Email',
                valueField: 'email',
                node: '@sage/xtrem-show-case/ShowCaseCustomer',
                minLookupCharacters: 0,
                width: 'large',
                onChange(id: string, raw: string) {
                    ui.console.log('CHANGED FILTER SELECT', { id, raw });
                },
                columns: [
                    ui.nestedFields.text({ bind: '_id', title: 'Id' }),
                    ui.nestedFields.text({ bind: 'email', title: 'Email' }),
                ],
            }),
            ui.nestedFields.date<TableWithOptionsMenu, ShowCaseProduct>({
                bind: 'releaseDate',
                title: 'Date',
                maxDate(row) {
                    return row.endingDate;
                },
            }),
            ui.nestedFields.date({
                bind: 'endingDate',
                title: 'Ending date',
                minDate(row) {
                    return row.releaseDate;
                },
            }),
            ui.nestedFields.numeric({
                bind: 'discount',
                title: 'Discount',
            }),
            ui.nestedFields.select<TableWithOptionsMenu, ShowCaseProduct>({
                bind: 'category',
                title: 'Category',
                optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
            }),
            ui.nestedFields.image({
                bind: 'imageField',
                title: 'Image',
            }),
            ui.nestedFields.icon({
                bind: 'qty',
                title: 'Icon',
                map: value => {
                    return value === 0 ? 'error' : value < 10 ? 'warning' : '';
                },
            }),
        ],
        orderBy: {
            product: 1,
        },
        fieldActions() {
            return [this.addProduct];
        },
        parent() {
            return this.fieldBlock;
        },
        onChange() {
            this.calculateTableTotals();
            this.calculateSelectedTotal();
        },
        onRowSelected(_id: string | number, item: any) {
            this.calculateSelectedTotal();
            this.$.showToast(`${item.product} selected.`);
        },
        onRowUnselected(_id: string | number, item: any) {
            this.calculateSelectedTotal();
            this.$.showToast(`${item.product} unselected.`, { type: 'warning' });
        },
        dropdownActions: [
            {
                icon: 'add',
                title: 'Add',
                isDisabled() {
                    return false;
                },
                onClick(rowId: any, data: any) {
                    this.$.dialog.message(
                        'info',
                        'Add Row Action was clicked',
                        `Product: ${data.product} Row: ${rowId}`,
                    );
                },
            },
            {
                icon: 'locked',
                title: 'Maybe disabled',
                isDisabled(id: any, row: any) {
                    return (row?.qty ?? 0) < 10;
                },
                onClick(rowId: any, data: any) {
                    this.$.showToast(data.product, { type: 'info' });
                },
            },
            ui.menuSeparator(),
            {
                title: 'Action no icon',
                isDisabled(id: any, row: any) {
                    return (row?.qty ?? 0) < 10;
                },
                onClick(rowId: any, data: any) {
                    this.$.showToast(data.product, { type: 'info' });
                },
            },
            {
                title: 'Refresh record',
                icon: 'refresh',
                async onClick(rowId) {
                    await this.field.refreshRecord(rowId);
                },
            },
            ui.menuSeparator(),
            {
                icon: 'minus',
                title: 'Remove',
                isDestructive: true,
                onClick(rowId: any) {
                    this.field.removeRecord(rowId);
                },
            },
        ],
    })
    field: ui.fields.Table<ShowCaseProduct>;

    @ui.decorators.pageAction<TableWithOptionsMenu>({
        title: 'Save',
        onClick() {
            this.$.loader.display();
            return this.$.graph
                .update()
                .then(() => {
                    this.$.finish();
                })
                .catch(() => {
                    this.$.showToast('Something went wrong', { type: 'error' });
                })
                .finally(() => {
                    this.$.loader.hide();
                });
        },
    })
    saveTable: ui.PageAction;

    @ui.decorators.numericField<TableWithOptionsMenu>({
        title: 'Medium value limit',
        isTransient: true,
        parent() {
            return this.fieldBlock;
        },
    })
    mediumValueLimit: ui.fields.Numeric;

    @ui.decorators.numericField<TableWithOptionsMenu>({
        title: 'High value limit (auto redraw)',
        isTransient: true,
        parent() {
            return this.fieldBlock;
        },
        async onChange() {
            await this.field.redraw('qty');
        },
    })
    highValueLimit: ui.fields.Numeric;

    @ui.decorators.textField<TableWithOptionsMenu>({
        title: 'Quantity value prefix',
        isTransient: true,
        parent() {
            return this.fieldBlock;
        },
    })
    quantityValuePrefix: ui.fields.Text;

    @ui.decorators.numericField<TableWithOptionsMenu>({
        title: 'Quantity value scale (auto redraw)',
        isTransient: true,
        scale: 0,
        parent() {
            return this.fieldBlock;
        },
        async onChange() {
            await this.field.redraw('qty');
        },
    })
    quantityValueScale: ui.fields.Numeric;

    @ui.decorators.checkboxField<TableWithOptionsMenu>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Can edit Description',

        isTransient: true,
    })
    canEditDescription: ui.fields.Checkbox;

    @ui.decorators.buttonField<TableWithOptionsMenu>({
        isTransient: true,
        map() {
            return 'Redraw';
        },
        parent() {
            return this.fieldBlock;
        },
        onClick() {
            this.field.redraw();
        },
    })
    redrawButton: ui.fields.Button;

    @ui.decorators.separatorField<TableWithOptionsMenu>({
        parent() {
            return this.fieldBlock;
        },
        isFullWidth: true,
        isInvisible: true,
    })
    sep1: ui.fields.Separator;

    @ui.decorators.textField<TableWithOptionsMenu>({
        title: 'Columns',
        isTransient: true,
        parent() {
            return this.fieldBlock;
        },
    })
    columnsList: ui.fields.Text;

    @ui.decorators.buttonField<TableWithOptionsMenu>({
        isTransient: true,
        map() {
            return 'Hide Columns';
        },
        parent() {
            return this.fieldBlock;
        },
        onClick() {
            this.columnsList.value.split(',').map(column => this.field.hideColumn(column.trim()));
        },
    })
    hideColumnButton: ui.fields.Button;

    @ui.decorators.buttonField<TableWithOptionsMenu>({
        isTransient: true,
        map() {
            return 'Show Columns';
        },
        parent() {
            return this.fieldBlock;
        },
        onClick() {
            this.columnsList.value.split(',').map(column => this.field.showColumn(column.trim()));
        },
    })
    showColumnButton: ui.fields.Button;

    @ui.decorators.block<TableWithOptionsMenu>({
        parent() {
            return this.section;
        },
        title: 'Configuration',
    })
    configurationBlock: ui.containers.Block;

    @ui.decorators.checkboxField<TableWithOptionsMenu>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Can select',
        onChange() {
            this.field.canSelect = this.canSelect.value;
        },
        isTransient: true,
    })
    canSelect: ui.fields.Checkbox;

    @ui.decorators.checkboxField<TableWithOptionsMenu>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Can user hide columns',
        onChange() {
            this.field.canUserHideColumns = this.canUserHideColumns.value;
        },
        isTransient: true,
    })
    canUserHideColumns: ui.fields.Checkbox;

    @ui.decorators.textField<TableWithOptionsMenu>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Helper text',
        onChange() {
            this.field.helperText = this.helperText.value;
        },
        isTransient: true,
    })
    helperText: ui.fields.Text;

    @ui.decorators.checkboxField<TableWithOptionsMenu>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is disabled',
        onChange() {
            this.field.isDisabled = this.isDisabled.value;
        },
        isTransient: true,
    })
    isDisabled: ui.fields.Checkbox;

    @ui.decorators.checkboxField<TableWithOptionsMenu>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is container disabled',
        onChange() {
            this.fieldBlock.isDisabled = this.isContainerDisabled.value;
        },
        isTransient: true,
    })
    isContainerDisabled: ui.fields.Checkbox;

    @ui.decorators.checkboxField<TableWithOptionsMenu>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is helper text hidden',
        onChange() {
            this.field.isHelperTextHidden = this.isHelperTextHidden.value;
        },
        isTransient: true,
    })
    isHelperTextHidden: ui.fields.Checkbox;

    @ui.decorators.checkboxField<TableWithOptionsMenu>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is hidden',
        onChange() {
            this.field.isHidden = this.isHidden.value;
        },
        isTransient: true,
    })
    isHidden: ui.fields.Checkbox;

    @ui.decorators.checkboxField<TableWithOptionsMenu>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is title hidden',
        onChange() {
            this.field.isTitleHidden = this.isTitleHidden.value;
        },
        isTransient: true,
    })
    isTitleHidden: ui.fields.Checkbox;

    @ui.decorators.textField<TableWithOptionsMenu>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Title',
        onChange() {
            this.field.title = this.title.value;
        },
        isTransient: true,
    })
    title: ui.fields.Text;

    @ui.decorators.pageAction<TableWithOptionsMenu>({
        title: 'Add row',
        icon: 'plus',
        onClick() {
            this.$.dialog
                .page('@sage/xtrem-show-case/ShowCaseProduct', undefined, {
                    rightAligned: true,
                    size: 'medium-large',
                })
                .then(() => {
                    return this.field.refresh();
                })
                .catch(error => {
                    ui.console.error(error);
                    this.$.showToast('An error ocurred during creation');
                });
        },
    })
    addProduct: ui.PageAction;

    updateRow(_id: any, rowData: ui.PartialNodeWithId<ShowCaseProduct>) {
        const updatedAmount = rowData.qty * parseFloat(rowData.listPrice);
        rowData.amount = String(updatedAmount);
        this.field.setRecordValue(rowData);
    }

    calculateTableTotals() {
        this.tableSampleTotal.value = (this.field.value || [])
            .map(v => parseFloat(v.amount))
            .reduce((prev, v) => prev + v, 0);
        this.calculateSelectedTotal();
    }

    calculateSelectedTotal() {
        const selectedRecords = this.field.selectedRecords || [];
        const selectedTotal = (this.field.value || [])
            .filter(v => selectedRecords.indexOf(v._id) !== -1)
            .map(v => parseFloat(v.amount))
            .reduce((prev, v) => prev + v, 0);

        this.tableSelectedTotal.value = selectedTotal;
    }
}
