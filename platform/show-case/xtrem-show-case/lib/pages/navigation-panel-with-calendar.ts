import { GraphApi, ShowCaseProduct } from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import { navigationPanel } from '../menu-items/navigation-panel';

@ui.decorators.page<NavigationPanelWithCalendar, ShowCaseProduct>({
    authorizationCode: 'NAVPANEL',
    module: 'show-case',
    title: 'Navigation Panel - With calendar view',
    node: '@sage/xtrem-show-case/ShowCaseProduct',
    category: 'SHOWCASE',
    createAction() {
        return this.$standardNewAction;
    },
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.text({
                bind: 'product',
                title: 'Product',
            }),
            line2: ui.nestedFields.text({ bind: 'description', title: 'Description' }),
            line2Right: ui.nestedFields.label({
                bind: 'category',
                title: 'Category',
                optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
            }),
            image: ui.nestedFields.image({ bind: 'imageField' }),
        },
        startDateField: 'releaseDate',
        endDateField: 'endingDate',
        cardColor(row: ShowCaseProduct) {
            switch (row.category) {
                case 'awful':
                    return ui.tokens.colorsSemanticNegative500;
                case 'notBad':
                    return ui.tokens.colorsSemanticCaution500;
                case 'ok':
                    return ui.tokens.colorsSemanticInfo500;
                case 'good':
                    return ui.tokens.colorsSemanticPositive500;
                case 'great':
                    return ui.tokens.colorsSemanticPositive600;
                default:
                    return '';
            }
        },
        isEventMovable(row: ShowCaseProduct) {
            return row.category === 'good' || row.category === 'ok';
        },
        optionsMenu: [
            {
                title: 'All',
                graphQLFilter: {},
            },
            {
                title: 'Good products',
                graphQLFilter: {
                    category: { _eq: 'good' },
                },
            },
            {
                title: 'Great products',
                graphQLFilter: {
                    category: { _eq: 'great' },
                },
            },
            {
                title: 'Awful products',
                graphQLFilter: {
                    category: { _eq: 'awful' },
                },
            },
        ],
    },
    menuItem: navigationPanel,
})
export class NavigationPanelWithCalendar extends ui.Page<GraphApi> {
    @ui.decorators.section<NavigationPanelWithCalendar>({})
    section: ui.containers.Section;

    @ui.decorators.block<NavigationPanelWithCalendar>({
        parent() {
            return this.section;
        },
        title: 'Product data',
    })
    block: ui.containers.Block;

    @ui.decorators.textField<NavigationPanelWithCalendar>({
        parent() {
            return this.block;
        },
        title: 'Id',
        isReadOnly: true,
    })
    _id: ui.fields.Text;

    @ui.decorators.textField<NavigationPanelWithCalendar>({
        parent() {
            return this.block;
        },
        title: 'Product',
        isReadOnly: true,
    })
    product: ui.fields.Text;
}
