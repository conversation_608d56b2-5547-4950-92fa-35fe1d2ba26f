import { GraphApi, ShowCaseProduct } from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import { referenceField } from '../menu-items/reference-field';

interface MySpecialTransientRow {
    _id: string;
    qty: number;
    product: ShowCaseProduct;
}

@ui.decorators.page<ReferenceFilterWithRowData>({
    authorizationCode: 'BSCFLDS',
    module: 'show-case',
    title: 'Reference (nested filter decorator)',
    category: 'SHOWCASE',
    menuItem: referenceField,
    isTransient: true,
    onLoad() {
        this.field.addRecord({ qty: 20 });
    },
})
export class ReferenceFilterWithRowData extends ui.Page<GraphApi> {
    @ui.decorators.section<ReferenceFilterWithRowData>({
        title: 'Reference with filter applied on row data',
    })
    section: ui.containers.Section;

    @ui.decorators.block<ReferenceFilterWithRowData>({
        parent() {
            return this.section;
        },
        title: 'Field example',
    })
    fieldBlock: ui.containers.Block;

    @ui.decorators.tableField<ReferenceFilterWithRowData, MySpecialTransientRow>({
        columns: [
            ui.nestedFields.numeric({ bind: 'qty', title: 'Order Quantity' }),
            ui.nestedFields.reference<ReferenceFilterWithRowData, MySpecialTransientRow, ShowCaseProduct>({
                bind: 'product',
                canFilter: true,
                node: '@sage/xtrem-show-case/ShowCaseProduct',
                title: 'Product',
                valueField: 'product',
                filter(rowData) {
                    return {
                        qty: { _gt: rowData.qty },
                    };
                },
                columns: [
                    ui.nestedFields.text({ bind: 'product', title: 'Product', canFilter: true }),
                    ui.nestedFields.text({ bind: 'description', canFilter: true, title: 'Description' }),
                    ui.nestedFields.numeric({ bind: 'qty', canFilter: true, title: 'Stock Quantity' }),
                ],
            }),
        ],
        parent() {
            return this.fieldBlock;
        },
        isFullWidth: true,
    })
    field: ui.fields.Table<any>;
}
