import * as ui from '@sage/xtrem-ui';
import { containers } from '../menu-items/containers';

@ui.decorators.page<PageTabs>({
    category: 'SHOWCASE',
    mode: 'tabs',
    isTransient: true,
    module: 'show-case',
    title: 'Page - Tabs Layout',
    menuItem: containers,
})
export class PageTabs extends ui.Page {
    @ui.decorators.section<PageTabs>({
        title: 'Unspecified Section',
    })
    section1: ui.containers.Section;

    @ui.decorators.section<PageTabs>({
        isTitleHidden: false,
        title: 'Not Hidden Section',
        indicatorContent: 'Hi!',
    })
    section2: ui.containers.Section;

    @ui.decorators.section<PageTabs>({
        isTitleHidden: true,
        title: 'Hidden Section',
    })
    section3: ui.containers.Section;

    @ui.decorators.block<PageTabs>({
        parent() {
            return this.section1;
        },
        title: 'First block',
    })
    block1: ui.containers.Block;

    @ui.decorators.block<PageTabs>({
        parent() {
            return this.section1;
        },
        title: 'Second block',
    })
    block2: ui.containers.Block;

    @ui.decorators.block<PageTabs>({
        parent() {
            return this.section2;
        },
        title: 'Third block',
    })
    block3: ui.containers.Block;

    @ui.decorators.block<PageTabs>({
        parent() {
            return this.section3;
        },
        title: 'Fourth block',
    })
    block4: ui.containers.Block;
}
