import * as ui from '@sage/xtrem-ui';
import { misc } from '../menu-items/misc';

@ui.decorators.page<Workflow>({
    module: 'show-case',
    title: 'Field - Workflow',
    category: 'SHOWCASE',
    isTransient: true,
    menuItem: misc,
    onLoad() {
        this.workflow.value = {
            edges: [
                {
                    id: 'entity-created-1--out-1',
                    source: 'entity-created-1',
                    target: 'condition-1',
                    sourceHandle: 'out',
                },
                {
                    id: 'condition-1--out-true-1',
                    source: 'condition-1',
                    target: 'send-email-1',
                    sourceHandle: 'out-true',
                },
            ],
            nodes: [
                {
                    id: 'entity-created-1',
                    data: {
                        topic: 'Company/created',
                        subtitle: '',
                        conditions: [],
                        entityName: '@sage/xtrem-system/Company',
                        stepVariables: [
                            {
                                node: 'Company',
                                path: 'company._id',
                                type: 'IntReference',
                                title: 'Company / 🆔',
                                isCustom: false,
                            },
                            {
                                path: 'company.id',
                                type: 'String',
                                title: 'Company / ID',
                                isCustom: false,
                            },
                        ],
                        localizedTitle: {
                            base: 'xd',
                            'en-US': 'xd',
                        },
                    } as any,
                    type: 'entity-created',
                    width: 250,
                    height: 80,
                    dragging: false,
                    position: {
                        x: 1060,
                        y: 40,
                    },
                    selected: false,
                },
                {
                    id: 'condition-1',
                    data: {
                        subtitle: null,
                        conditions: [
                            {
                                path: 'company._createUser.email',
                                value: 'patata',
                                operator: 'contains',
                                useParameter: false,
                            },
                        ],
                        ifTrueBranch: true,
                        ifFalseBranch: false,
                        stepVariables: [
                            {
                                path: 'company._createUser.email',
                                type: 'String',
                                title: 'Company / Create user / Email',
                                isCustom: false,
                            },
                        ],
                        localizedTitle: {
                            base: 'xd',
                            'en-US': 'xd',
                        },
                    } as any,
                    type: 'condition',
                    width: 250,
                    height: 72,
                    dragging: false,
                    position: {
                        x: 1060,
                        y: 240,
                    },
                    selected: false,
                },
                {
                    id: 'send-email-1',
                    data: {
                        from: null,
                        report: 'onboarding_user',
                        replyTo: null,
                        subtitle: 'onboarding_user',
                        addressList: [
                            {
                                value: 'xd',
                                addressType: 'to',
                            },
                        ],
                        stepVariables: [
                            {
                                path: 'company._createUser.email',
                                type: 'String',
                                title: 'Company / Create user / Email',
                                isCustom: false,
                            },
                            {
                                path: 'sentEmail.success',
                                type: 'Boolean',
                                title: 'Mail sent OK',
                            },
                        ],
                        attachmentList: [],
                        localizedTitle: {
                            base: 'xd',
                            'en-US': 'xd',
                        },
                        localizedSubject: {
                            base: '{{company._createUser.email}}',
                            'en-US': '{{company._createUser.email}}',
                        },
                        reportParameters: [
                            {
                                name: 'userName',
                                value: 'company._createUser.email',
                                isVariable: true,
                            },
                        ],
                        outputVariableName: 'sentEmail',
                    } as any,
                    type: 'send-email',
                    width: 250,
                    height: 87,
                    dragging: false,
                    position: {
                        x: 840,
                        y: 440,
                    },
                    selected: false,
                },
            ],
        };
    },
})
export class Workflow extends ui.Page {
    @ui.decorators.section<Workflow>({
        isTitleHidden: true,
    })
    section: ui.containers.Section;

    @ui.decorators.block<Workflow>({
        parent() {
            return this.section;
        },
        isTitleHidden: true,
    })
    fieldBlock: ui.containers.Block;

    @ui.decorators.workflowField<Workflow>({
        parent() {
            return this.fieldBlock;
        },
        isReadOnly: true,
        title: 'Month name',
        isFullWidth: true,
        validation(value) {
            const nodes = value?.nodes || [];
            const errors = nodes.map(node => {
                if (node.type === 'send-email') {
                    const email = node.data as any;
                    if (email.addressList.length === 0) {
                        return {
                            stepId: node.id,
                            message: 'Email node must have at least one address',
                        };
                    }
                    // Check if the email is valid
                    const isValidEmail = email.addressList.every((address: any) => {
                        return address.value && address.value.includes('@');
                    });
                    if (!isValidEmail) {
                        return {
                            stepId: node.id,
                            message: 'Email address is not valid',
                        };
                    }
                }
                return null;
            });
            const filteredErrors = errors.filter(error => error !== null);
            return filteredErrors.length > 0 ? filteredErrors : undefined;
        },
    })
    workflow: ui.fields.Workflow;

    @ui.decorators.buttonField<Workflow>({
        parent() {
            return this.fieldBlock;
        },
        onClick() {
            this.workflow.validate();
        },
        map() {
            return 'Validate';
        },
    })
    validate: ui.fields.Button;
}
