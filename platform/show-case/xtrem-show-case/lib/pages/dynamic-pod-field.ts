import { WorkflowVariable } from '@sage/xtrem-shared';
import { G<PERSON>h<PERSON><PERSON>, ShowCaseProvider } from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import { fields } from '../menu-items/fields';

@ui.decorators.page<DynamicPodField, ShowCaseProvider>({
    module: 'show-case',
    title: 'Dynamic Pod Field',
    category: 'SHOWCASE',
    menuItem: fields,
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.text({ bind: 'textField', title: 'Name' }),
            line2: ui.nestedFields.text({ bind: '_id', title: 'ID' }),
            line3: ui.nestedFields.date({ bind: 'dateField', title: 'Added on' }),
            image: ui.nestedFields.image({ bind: 'logo', title: 'Logo', placeholderMode: 'Initials' }),
        },
    },
    businessActions() {
        return [this.$standardSaveAction];
    },
    createAction() {
        return this.$standardNewAction;
    },
    headerDropDownActions() {
        return [this.$standardDeleteAction];
    },
    node: '@sage/xtrem-show-case/ShowCaseProvider',
})
export class DynamicPodField extends ui.Page<GraphApi> {
    @ui.decorators.section<DynamicPodField>({})
    section: ui.containers.Section;

    @ui.decorators.block<DynamicPodField>({
        title: 'Field example',
        parent() {
            return this.section;
        },
    })
    fieldBlock: ui.containers.Block;

    @ui.decorators.dynamicPodField<DynamicPodField>({
        validation(value) {
            if (value.contactPerson.name === 'patata') {
                return 'Patata is not allowed';
            }
            return undefined;
        },
        columns: [
            ui.nestedFields.text({ bind: { contactPerson: { name: true } }, title: 'Contact name' }),
            ui.nestedFields.text({
                bind: { contactPerson: { phoneNumber: true } },
                title: 'Contact phone number',
                validation(value) {
                    if (value && value.length > 10) {
                        return 'Phone number is too long';
                    }
                    return undefined;
                },
            }),
            ui.nestedFields.textArea({ bind: 'internalNote', title: 'Internal notes' }),
            ui.nestedFields.reference({
                valueField: 'name',
                bind: 'country',
                node: '@sage/xtrem-show-case/ShowCaseCountry',
            }),
            ui.nestedFields.switch({
                bind: 'hotProduct',
                title: 'Hot (Switch)',
            }),
            ui.nestedFields.dynamicSelect({
                bind: 'dynamicSelect',
                title: 'Dynamic select',
                mode: 'input',
                options() {
                    return [
                        { id: '1', value: '1', displayedAs: 'One' },
                        { id: '2', value: '2', displayedAs: 'Two' },
                        { id: '3', value: '3', displayedAs: 'Three' },
                    ];
                },
                async populateList(currentList) {
                    const inputVariables = [
                        { node: 'Company', path: 'company._id', title: 'Company / 🆔', type: 'IntReference' },
                        { path: 'company.id', title: 'Company / ID', type: 'String' },
                    ];
                    const oldRootPaths: any = [];
                    const onlyWritable = false;
                    const result = (await this.$.dialog.page('@sage/xtrem-workflow/WorkflowSelectVariablesDialog', {
                        inputVariables: JSON.stringify(inputVariables),
                        oldRootPaths: JSON.stringify(oldRootPaths),
                        onlyWritable: !!onlyWritable,
                    })) as
                        | { selectedVariables: WorkflowVariable[]; selectedRootVariable: WorkflowVariable }
                        | undefined;

                    const listItems = result?.selectedVariables.map(v => ({
                        id: v.path,
                        value: `{{${v.path}}}`,
                        displayedAs: v.title,
                    }));

                    return [...currentList, ...(listItems || [])];
                },
            }),
        ],
        parent() {
            return this.fieldBlock;
        },
        title: 'Dynamic Pod field',
        width: 'extra-large',
    })
    additionalInfo: ui.fields.DynamicPod;

    @ui.decorators.labelField<DynamicPodField>({
        parent() {
            return this.fieldBlock;
        },
        isHidden: true,
        isTransient: true,
        map() {
            return 'Change was triggered';
        },
    })
    changeTriggered: ui.fields.Label;

    @ui.decorators.labelField<DynamicPodField>({
        parent() {
            return this.fieldBlock;
        },
        isHidden: true,
        isTransient: true,
        map() {
            return 'Click was triggered';
        },
    })
    clickTriggered: ui.fields.Label;

    @ui.decorators.block<DynamicPodField>({
        title: 'Add Dynamic pod nested field',
        parent() {
            return this.section;
        },
    })
    nestedBlock: ui.containers.Block;

    @ui.decorators.buttonField<DynamicPodField>({
        parent() {
            return this.nestedBlock;
        },
        map() {
            return 'Add nested field';
        },
        isTransient: true,
        onClick() {
            this.additionalInfo.addNestedField(
                ui.nestedFields.checkbox({ bind: { amount: { data: { canSort: true } } }, title: 'Can sort amount' }),
            );
        },
    })
    addNestedField: ui.fields.Button;

    @ui.decorators.buttonField<DynamicPodField>({
        parent() {
            return this.nestedBlock;
        },
        map() {
            return 'Set a new nested field array';
        },
        isTransient: true,
        onClick() {
            this.additionalInfo.setNestedFields([
                ui.nestedFields.text({ bind: { patata1: true }, title: 'Patata 1' }),
                ui.nestedFields.text({ bind: { patata2: true }, title: 'Patata 2' }),
            ]);
        },
    })
    setNestedField: ui.fields.Button;

    @ui.decorators.buttonField<DynamicPodField>({
        isTransient: true,
        parent() {
            return this.nestedBlock;
        },
        map() {
            return 'Validate';
        },
        onClick() {
            this.additionalInfo.validate();
        },
    })
    validateDynamicPod: ui.fields.Button;

    @ui.decorators.buttonField<DynamicPodField>({
        parent() {
            return this.nestedBlock;
        },
        map() {
            return 'Remove nested field';
        },
        isTransient: true,
        onClick() {
            this.additionalInfo.removeNestedField({ amount: { data: { canSort: true } } });
        },
    })
    removeNestedField: ui.fields.Button;

    @ui.decorators.buttonField<DynamicPodField>({
        parent() {
            return this.nestedBlock;
        },
        map() {
            return 'Remove all nested fields';
        },
        isTransient: true,
        onClick() {
            this.additionalInfo.removeAllNestedFields();
        },
    })
    removeAllNestedField: ui.fields.Button;

    @ui.decorators.block<DynamicPodField>({
        parent() {
            return this.section;
        },
        title: 'Configuration',
    })
    configurationBlock: ui.containers.Block;

    @ui.decorators.checkboxField<DynamicPodField>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is disabled',
        isTransient: true,
        helperText:
            'Determines whether the field is disabled or not. It can also be defined as callback function that returns a boolean.',
        isReversed: true,
        onChange() {
            this.additionalInfo.isDisabled = !!this.isDisabled.value;
        },
    })
    isDisabled: ui.fields.Checkbox;

    @ui.decorators.checkboxField<DynamicPodField>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is read only',
        isTransient: true,
        helperText:
            'Whether the field is editable (isReadOnly = false) or not (isReadOnly = true). The difference with disabled is that isReadOnly suggests that the field is never editable. It can be defined as a boolean, or conditionally by a callback that returns a boolean.',
        isReversed: true,
        onChange() {
            this.additionalInfo.isReadOnly = !!this.isReadOnly.value;
        },
    })
    isReadOnly: ui.fields.Checkbox;

    @ui.decorators.checkboxField<DynamicPodField>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is hidden',
        isTransient: true,
        helperText: 'Determines whether the field is displayed or not.',
        isReversed: true,
        onChange() {
            this.additionalInfo.isHidden = !!this.isHidden.value;
        },
    })
    isHidden: ui.fields.Checkbox;

    @ui.decorators.textField<DynamicPodField>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Title',
        isTransient: true,
        helperText:
            'The title that is displayed above the field. The title can be provided as a string, or a callback function returning a string. When declared as a callback within the column of a nested grid, the column id is provided as a parameter. It is automatically picked up by the i18n engine and externalized for translation.',
        onChange() {
            this.additionalInfo.title = this.title.value || '';
        },
    })
    title: ui.fields.Text;

    @ui.decorators.checkboxField<DynamicPodField>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is title hidden',
        isTransient: true,
        helperText: 'Whether the field title above the field should be displayed and its vertical space preserved.',
        isReversed: true,
        onChange() {
            this.additionalInfo.isTitleHidden = !!this.isTitleHidden.value;
        },
    })
    isTitleHidden: ui.fields.Checkbox;

    @ui.decorators.textField<DynamicPodField>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Helper text',
        isTransient: true,
        helperText:
            'The helper text that is displayed above the field. It is automatically picked up by the i18n engine and externalized.',
        onChange() {
            this.additionalInfo.helperText = this.helperText.value || '';
        },
    })
    helperText: ui.fields.Text;

    @ui.decorators.checkboxField<DynamicPodField>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is helper text hidden',
        isTransient: true,
        helperText:
            'Whether the helper text underneath the field should be displayed and its vertical space preserved.',
        isReversed: true,
        onChange() {
            this.additionalInfo.isHelperTextHidden = !!this.isHelperTextHidden.value;
        },
    })
    isHelperTextHidden: ui.fields.Checkbox;

    @ui.decorators.textField<DynamicPodField>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Info message',
        isTransient: true,
        helperText:
            'Indicate additional warning message, rendered as tooltip and blue border. It can also be defined as callback function.',
        onChange() {
            this.additionalInfo.infoMessage = this.infoMessage.value || '';
        },
    })
    infoMessage: ui.fields.Text;

    @ui.decorators.textField<DynamicPodField>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Warning message',
        isTransient: true,
        helperText:
            'Indicate additional information, rendered as tooltip and orange border. It can also be defined as callback.',
        onChange() {
            this.additionalInfo.warningMessage = this.warningMessage.value || '';
        },
    })
    warningMessage: ui.fields.Text;
}
