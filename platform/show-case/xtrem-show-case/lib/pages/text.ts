import * as ui from '@sage/xtrem-ui';
import { textField } from '../menu-items/text-field';

@ui.decorators.page<Text>({
    menuItem: textField,
    title: 'Text',
    subtitle: 'Fields',
    isTransient: true,
    headerSection() {
        return this.headerSection;
    },
})
export class Text extends ui.Page {
    @ui.decorators.section<Text>({
        title: 'Text',
        isTitleHidden: true,
    })
    headerSection: ui.containers.Section;

    @ui.decorators.section<Text>({
        title: 'Text field',
        isTitleHidden: true,
    })
    section: ui.containers.Section;

    @ui.decorators.block<Text>({
        parent() {
            return this.headerSection;
        },
        title: 'Introduction',
        width: 'medium',
    })
    introductionBlock: ui.containers.Block;

    @ui.decorators.staticContentField<Text>({
        parent() {
            return this.introductionBlock;
        },
        isTitleHidden: true,
        isFullWidth: true,
        content: 'Text fields are the most simple kind of fields. They can be used to represent simple text values.',
    })
    description: ui.fields.StaticContent;

    @ui.decorators.linkField<Text>({
        parent() {
            return this.introductionBlock;
        },
        title: 'Source code',
        isFullWidth: true,
        map() {
            return ui.localize('@sage/xtrem-show-case/check-source-code', 'Check it on GitHub');
        },
        page: 'https://github.com/Sage-ERP-X3/xtrem/blob/master/platform/show-case/xtrem-show-case/lib/pages/text.ts',
    })
    sourceCodeLink: ui.fields.Link;

    @ui.decorators.messageField<Text>({
        parent() {
            return this.introductionBlock;
        },
        isTitleHidden: true,
        isFullWidth: true,
        content: 'It is also available as a nested field in the navigation panel, tables, charts and calendars.',
    })
    nestedOptions: ui.fields.Message;

    @ui.decorators.block<Text>({
        parent() {
            return this.headerSection;
        },
        title: 'Field example',
        width: 'medium',
    })
    fieldBlock: ui.containers.Block;

    @ui.decorators.textField<Text>({
        parent() {
            return this.fieldBlock;
        },
        onClick() {
            this.clickTriggered.isHidden = false;
            setTimeout(() => {
                this.clickTriggered.isHidden = true;
            }, 5000);
        },
        onChange() {
            this.fieldCallback.value = this.field.value;
            this.changeTriggered.isHidden = false;
            setTimeout(() => {
                this.changeTriggered.isHidden = true;
            }, 5000);
        },
    })
    field: ui.fields.Text;

    @ui.decorators.labelField<Text>({
        parent() {
            return this.fieldBlock;
        },
        isHidden: true,
        map() {
            return 'Change was triggered';
        },
    })
    changeTriggered: ui.fields.Label;

    @ui.decorators.labelField<Text>({
        parent() {
            return this.fieldBlock;
        },
        isHidden: true,
        map() {
            return 'Click was triggered';
        },
    })
    clickTriggered: ui.fields.Label;

    @ui.decorators.textField<Text>({
        parent() {
            return this.fieldBlock;
        },
        title() {
            return this.title.value || '';
        },
        prefix() {
            return this.prefix.value || '';
        },
        postfix() {
            return this.postfix.value || '';
        },
        isDisabled() {
            return this.isDisabled.value || false;
        },
        isReadOnly() {
            return this.isReadOnly.value || false;
        },
        isHidden() {
            return this.isHidden.value || false;
        },
        onChange() {
            this.field.value = this.fieldCallback.value;
        },
        helperText: 'This field is configured by callbacks',
    })
    fieldCallback: ui.fields.Text;

    @ui.decorators.block<Text>({
        parent() {
            return this.section;
        },
        title: 'Configuration',
    })
    configurationBlock: ui.containers.Block;

    @ui.decorators.checkboxField<Text>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is disabled',
        helperText:
            'Determines whether the field is disabled or not. It can also be defined as callback function that returns a boolean.',
        isFullWidth: true,
        isReversed: true,
        onChange() {
            this.field.isDisabled = !!this.isDisabled.value;
        },
    })
    isDisabled: ui.fields.Checkbox;

    @ui.decorators.checkboxField<Text>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is read only',
        helperText:
            'Whether the field is editable (isReadOnly = false) or not (isReadOnly = true). The difference with disabled is that isReadOnly suggests that the field is never editable. It can be defined as a boolean, or conditionally by a callback that returns a boolean.',
        isFullWidth: true,
        isReversed: true,
        onChange() {
            this.field.isReadOnly = !!this.isReadOnly.value;
        },
    })
    isReadOnly: ui.fields.Checkbox;

    @ui.decorators.checkboxField<Text>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is hidden',
        helperText: 'Determines whether the field is displayed or not.',
        isFullWidth: true,
        isReversed: true,
        onChange() {
            this.field.isHidden = !!this.isHidden.value;
        },
    })
    isHidden: ui.fields.Checkbox;

    @ui.decorators.textField<Text>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Title',
        isFullWidth: true,
        helperText:
            'The title that is displayed above the field. The title can be provided as a string, or a callback function returning a string. When declared as a callback within the column of a nested grid, the column id is provided as a parameter. It is automatically picked up by the i18n engine and externalized for translation.',
        onChange() {
            this.field.title = this.title.value || '';
        },
    })
    title: ui.fields.Text;

    @ui.decorators.checkboxField<Text>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is title hidden',
        isFullWidth: true,
        helperText: 'Whether the field title above the field should be displayed and its vertical space preserved.',
        isReversed: true,
        onChange() {
            this.field.isTitleHidden = !!this.isTitleHidden.value;
        },
    })
    isTitleHidden: ui.fields.Checkbox;

    @ui.decorators.textField<Text>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Helper text',
        helperText:
            'The helper text that is displayed above the field. It is automatically picked up by the i18n engine and externalized.',
        isFullWidth: true,
        onChange() {
            this.field.helperText = this.helperText.value || '';
        },
    })
    helperText: ui.fields.Text;

    @ui.decorators.checkboxField<Text>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is helper text hidden',
        helperText:
            'Whether the helper text underneath the field should be displayed and its vertical space preserved.',
        isFullWidth: true,
        isReversed: true,
        onChange() {
            this.field.isHelperTextHidden = !!this.isHelperTextHidden.value;
        },
    })
    isHelperTextHidden: ui.fields.Checkbox;

    @ui.decorators.selectField<Text>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Icon',
        isFullWidth: true,
        helperText:
            'Icon to be displayed on the right side of the input. The list of icons are defined by Sage Design Language System',
        onChange() {
            this.field.icon = this.icon.value as any;
        },
        options: ['add', 'calendar', 'edit', 'gift', 'image', 'ledger', 'pause_circle', 'refresh', 'tag', 'video'],
    })
    icon: ui.fields.Select;

    @ui.decorators.textField<Text>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Prefix',
        isFullWidth: true,
        helperText:
            'A string that is displayed inside the field before the value, aligned to the left. It can be defined as a string, or conditionally by a callback that returns a string.',
        onChange() {
            this.field.prefix = this.prefix.value || '';
        },
    })
    prefix: ui.fields.Text;

    @ui.decorators.textField<Text>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Postfix',
        helperText:
            'A string that is displayed inside the field after the value, aligned to the right. It can be defined as a string, or conditionally by a callback that returns a string',
        isFullWidth: true,
        onChange() {
            this.field.postfix = this.postfix.value || '';
        },
    })
    postfix: ui.fields.Text;

    @ui.decorators.textField<Text>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Placeholder',
        helperText:
            'Placeholder text which is displayed inside the field body when the field is empty. It is automatically picked up by the i18n engine and externalized.',
        isFullWidth: true,
        onChange() {
            this.field.placeholder = this.placeholder.value || '';
        },
    })
    placeholder: ui.fields.Text;

    @ui.decorators.checkboxField<Text>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is mandatory',
        helperText:
            ' Makes the field mandatory, empty values will raise an error message. It can also be defined as callback function that returns a boolean.',
        isFullWidth: true,
        isReversed: true,
        onChange() {
            this.field.isMandatory = !!this.isMandatory.value;
        },
    })
    isMandatory: ui.fields.Checkbox;

    @ui.decorators.numericField<Text>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Max length',
        helperText:
            'Sets the maximum number of allowed characters. It can also be defined as callback function that returns a number.',
        isFullWidth: true,
        onChange() {
            this.field.maxLength = this.maxLength.value || undefined;
        },
    })
    maxLength: ui.fields.Numeric;

    @ui.decorators.numericField<Text>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Min length',
        helperText:
            'Sets the minimum number of required characters. It can also be defined as callback function that returns a number.',
        isFullWidth: true,
        onChange() {
            this.field.minLength = this.minLength.value || undefined;
        },
    })
    minLength: ui.fields.Numeric;

    @ui.decorators.textField<Text>({
        parent() {
            return this.additionalBlock;
        },
        title: 'Info message',
        helperText:
            'Indicate additional warning message, rendered as tooltip and blue border. It can also be defined as callback function.',
        isFullWidth: true,
        onChange() {
            this.field.infoMessage = this.infoMessage.value || '';
        },
    })
    infoMessage: ui.fields.Text;

    @ui.decorators.textField<Text>({
        parent() {
            return this.additionalBlock;
        },
        title: 'Warning message',
        helperText:
            'Indicate additional information, rendered as tooltip and orange border. It can also be defined as callback.',
        isFullWidth: true,
        onChange() {
            this.field.warningMessage = this.warningMessage.value || '';
        },
    })
    warningMessage: ui.fields.Text;

    @ui.decorators.textField<Text>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Value',
        isFullWidth: true,
        onChange() {
            if (this.field.value !== this.value.value) {
                this.field.value = this.value.value;
                this.fieldCallback.value = this.value.value;
            }
        },
    })
    value: ui.fields.Text;

    @ui.decorators.buttonField<Text>({
        parent() {
            return this.configurationBlock;
        },
        map() {
            return 'Focus field';
        },
        onClick() {
            this.field.focus();
        },
    })
    focus: ui.fields.Button;

    @ui.decorators.block<Text>({
        parent() {
            return this.section;
        },
        title: 'Events',
    })
    eventsBlock: ui.containers.Block;

    @ui.decorators.staticContentField<Text>({
        parent() {
            return this.eventsBlock;
        },
        isTitleHidden: true,
        isFullWidth: true,
        isMarkdown: true,
        content:
            '-   **onClick**: Triggered when any parts of the field is clicked, no arguments provided.\n-   **onChange**: Triggered when the field value changed and the focus is about to move away from the field, no arguments provided.\n-   **onError**: Handles errors thrown from the callback functions\n-   **onInputValueChange**: Triggered when the user stops typing into the input of the field. The execution is debounced by 150ms to cater for continuos typing.',
    })
    eventsDescription: ui.fields.StaticContent;

    @ui.decorators.block<Text>({
        parent() {
            return this.section;
        },
        title: 'Runtime functions',
    })
    runtimeFunctionsBlock: ui.containers.Block;

    @ui.decorators.staticContentField<Text>({
        parent() {
            return this.runtimeFunctionsBlock;
        },
        isTitleHidden: true,
        isFullWidth: true,
        isMarkdown: true,
        content:
            "-   **focus()**: Moves the focus to the field.\n-   **getNextField(isFocusable)**: Returns the next field instance. The order is calculated by the page prototype. If the isFocusable argument is set to true, it returns the next visible, enabled and non read-only field. It only considers the committed page state, so `commitValueAndPropertyChanges` call might be required beforehand to get the expected result.\n-   **refresh()**: Refetches the field's value from the server and updates it on the screen, only for non-transient pages.\n-   **validate()**: Triggers the field validation rules. Since the validation rules might be asynchronous, this method returns a promise that must be awaited to get the validation result\n-   **validateWithDetails()**: In addition to the functionality of `validate` it returns more details, including the rule that failed and where applicable, the row ID and colum ID.\n-   **fetchDefault(skipSet)**: Force re-fetches default value for the field. If the `skipSet` flag is set to true, it returns the default values but not apply them to the screen.\n-   **isDirty()**: Sets or gets the dirty state of the field.",
    })
    runtimeFunctionDescription: ui.fields.StaticContent;

    /* Additional examples */

    @ui.decorators.block<Text>({
        parent() {
            return this.section;
        },
        title: 'Additional examples',
    })
    additionalBlock: ui.containers.Block;

    @ui.decorators.textField<Text>({
        parent() {
            return this.additionalBlock;
        },
        title: 'Mandatory',
        isMandatory: true,
    })
    mandatory: ui.fields.Text;

    @ui.decorators.textField<Text>({
        parent() {
            return this.additionalBlock;
        },
        title: 'Full width',
        isFullWidth: true,
    })
    fullWidth: ui.fields.Text;

    @ui.decorators.block<Text>({
        parent() {
            return this.section;
        },
        title: 'Dynamic validations',
    })
    dynamicValidations: ui.containers.Block;

    @ui.decorators.textField<Text>({
        parent() {
            return this.dynamicValidations;
        },
        title: 'Mandatory By Setter',
    })
    mandatoryBySetter: ui.fields.Text;

    @ui.decorators.switchField<Text>({
        parent() {
            return this.dynamicValidations;
        },
        title: 'Is Mandatory?',
        onChange() {
            this.mandatoryBySetter.isMandatory = this.mandatoryBySetterSwitch.value || undefined;
        },
    })
    mandatoryBySetterSwitch: ui.fields.Switch;

    @ui.decorators.separatorField<Text>({
        parent() {
            return this.dynamicValidations;
        },
        isFullWidth: true,
    })
    mandatorySeparator1: ui.fields.Separator;

    @ui.decorators.textField<Text>({
        parent() {
            return this.dynamicValidations;
        },
        title: 'Mandatory By Callback',
        isMandatory() {
            return this.mandatoryByCallbackSwitch.value || false;
        },
    })
    mandatoryByCallback: ui.fields.Text;

    @ui.decorators.switchField<Text>({
        parent() {
            return this.dynamicValidations;
        },
        title: 'Is Mandatory?',
    })
    mandatoryByCallbackSwitch: ui.fields.Switch;

    @ui.decorators.separatorField<Text>({
        parent() {
            return this.dynamicValidations;
        },
        isFullWidth: true,
    })
    mandatorySeparator2: ui.fields.Separator;

    @ui.decorators.textField<Text>({
        parent() {
            return this.dynamicValidations;
        },
        title: 'Min length by setter',
    })
    minLengthBySetter: ui.fields.Text;

    @ui.decorators.numericField<Text>({
        parent() {
            return this.dynamicValidations;
        },
        title: 'Min length?',
        helperText: 'When 0 is set, the rule is disabled',
        onChange() {
            this.minLengthBySetter.minLength = this.minLengthBySetterLimit.value || undefined;
        },
    })
    minLengthBySetterLimit: ui.fields.Numeric;

    @ui.decorators.separatorField<Text>({
        parent() {
            return this.dynamicValidations;
        },
        isFullWidth: true,
    })
    mandatorySeparator3: ui.fields.Separator;

    @ui.decorators.textField<Text>({
        parent() {
            return this.dynamicValidations;
        },
        title: 'Min length By Callback',
        minLength() {
            return this.minLengthCallbackLimit.value || undefined;
        },
    })
    minLengthByCallback: ui.fields.Text;

    @ui.decorators.numericField<Text>({
        parent() {
            return this.dynamicValidations;
        },
        title: 'Min length?',
        helperText: 'When 0 is set, the rule is disabled',
    })
    minLengthCallbackLimit: ui.fields.Numeric;

    @ui.decorators.separatorField<Text>({
        parent() {
            return this.dynamicValidations;
        },
        isFullWidth: true,
    })
    mandatorySeparator4: ui.fields.Separator;

    @ui.decorators.textField<Text>({
        parent() {
            return this.dynamicValidations;
        },
        title: 'Max length by setter',
    })
    maxLengthBySetter: ui.fields.Text;

    @ui.decorators.numericField<Text>({
        parent() {
            return this.dynamicValidations;
        },
        title: 'Max length?',
        helperText: 'When 0 is set, the rule is disabled',
        onChange() {
            this.maxLengthBySetter.maxLength = this.maxLengthBySetterLimit.value || undefined;
        },
    })
    maxLengthBySetterLimit: ui.fields.Numeric;

    @ui.decorators.separatorField<Text>({
        parent() {
            return this.dynamicValidations;
        },
        isFullWidth: true,
    })
    mandatorySeparator5: ui.fields.Separator;

    @ui.decorators.textField<Text>({
        parent() {
            return this.dynamicValidations;
        },
        title: 'Max length By Callback',
        maxLength() {
            return this.maxLengthCallbackLimit.value || undefined;
        },
    })
    maxLengthByCallback: ui.fields.Text;

    @ui.decorators.numericField<Text>({
        parent() {
            return this.dynamicValidations;
        },
        title: 'Max length?',
        helperText: 'When 0 is set, the rule is disabled',
    })
    maxLengthCallbackLimit: ui.fields.Numeric;

    @ui.decorators.textField<Text>({
        parent() {
            return this.additionalBlock;
        },
        title: 'Password Field',
        isFullWidth: true,
        isPassword: true,
    })
    passwordField: ui.fields.Text;

    @ui.decorators.textField<Text>({
        parent() {
            return this.additionalBlock;
        },
        title: 'Text Field with autocomplete',
        getAutocompleteText(inputValue: string) {
            const colors = ['red', 'green', 'blue', 'yellow', 'black', 'white', 'purple', 'orange', 'pink', 'brown'];
            return new Promise(resolve =>
                setTimeout(() => {
                    if (!inputValue || inputValue.endsWith(' ')) {
                        resolve(null);
                    }
                    const parts = inputValue.toLowerCase().split(' ');
                    const lastPart = parts[parts.length - 1];
                    const suggestions = colors.filter(color => color.startsWith(lastPart));
                    if (suggestions.length !== 0) {
                        resolve(suggestions[0].substring(lastPart.length));
                    } else {
                        resolve(null);
                    }
                }, 100),
            );
        },
        isFullWidth: true,
        helperText: 'Start typing names of colors',
    })
    autocompleteField: ui.fields.Text;

    @ui.decorators.separatorField<Text>({
        parent() {
            return this.additionalBlock;
        },
        isFullWidth: true,
    })
    fieldSeparator1: ui.fields.Separator;

    @ui.decorators.textField<Text>({
        parent() {
            return this.additionalBlock;
        },
        title: 'With warning message',
        warningMessage: 'Wow, warning!',
    })
    warningMessageField: ui.fields.Text;

    @ui.decorators.textField<Text>({
        parent() {
            return this.additionalBlock;
        },
        title: 'With warning message with callback',
        helperText: 'Type "wow"',
        warningMessage() {
            if (this.warningMessageWithCallbackField.value === 'wow') {
                return 'Warning message';
            }
            return null;
        },
    })
    warningMessageWithCallbackField: ui.fields.Text;

    @ui.decorators.textField<Text>({
        parent() {
            return this.additionalBlock;
        },
        title: 'With info message',
        infoMessage: 'Wow, warning!',
    })
    infoMessageField: ui.fields.Text;

    @ui.decorators.textField<Text>({
        parent() {
            return this.additionalBlock;
        },
        title: 'With info message with callback',
        helperText: 'Type "wow"',
        infoMessage() {
            if (this.infoMessageWithCallbackField.value === 'wow') {
                return 'Info message';
            }
            return null;
        },
    })
    infoMessageWithCallbackField: ui.fields.Text;

    @ui.decorators.textField<Text>({
        parent() {
            return this.additionalBlock;
        },
        title: 'With info message',
        warningMessage: 'Wow, warning!',
        infoMessage: 'You should not see this',
    })
    infoAndWarningMessageField: ui.fields.Text;

    @ui.decorators.textField<Text>({
        parent() {
            return this.additionalBlock;
        },
        isMandatory: true,
        title: 'Info, warning and validation',
        warningMessage: 'Wow, warning!',
        infoMessage: 'You should not see this',
        helperText: 'This field is mandatory too.',
    })
    infoAndWarningMessageMandatoryField: ui.fields.Text;
}
