import { ShowCaseProvider } from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import { fields } from '../menu-items/fields';

@ui.decorators.page<VisualProcess, ShowCaseProvider>({
    authorizationCode: 'ESHOWCABP',
    module: 'show-case',
    title: 'Field - Visual Process',
    node: '@sage/xtrem-show-case/ShowCaseProvider',
    category: 'SHOWCASE',
    menuItem: fields,
    businessActions() {
        return [this.save];
    },
    defaultEntry: () => '1',
    onLoad() {
        if (this.field.value && this.field.value.value) {
            this.value.value = this.field.value.value;
        }
    },
})
export class VisualProcess extends ui.Page {
    @ui.decorators.pageAction({
        title: 'Save',
        async onClick() {
            await this.$.graph.update();
        },
    })
    save: ui.PageAction;

    @ui.decorators.section<VisualProcess>({
        title: 'Visual Process',
    })
    section: ui.containers.Section;

    @ui.decorators.block<VisualProcess>({
        parent() {
            return this.section;
        },
        isTitleHidden: true,
        width: 'medium',
    })
    block: ui.containers.Block;

    @ui.decorators.visualProcessField<VisualProcess>({
        bind: 'document',
        parent() {
            return this.block;
        },
        title: 'Visual Process',
    })
    field: ui.fields.VisualProcess;

    @ui.decorators.block<VisualProcess>({
        parent() {
            return this.section;
        },
        isTitleHidden: true,
        width: 'medium',
    })
    configurationBlock: ui.containers.Block;

    @ui.decorators.textField<VisualProcess>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Title',
        isFullWidth: true,
        isTransient: true,
        onChange() {
            this.field.title = this.title.value;
        },
    })
    title: ui.fields.Text;

    @ui.decorators.textField<VisualProcess>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Helper text',
        isFullWidth: true,
        isTransient: true,
        onChange() {
            this.field.helperText = this.helperText.value;
        },
    })
    helperText: ui.fields.Text;

    @ui.decorators.checkboxField<VisualProcess>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is disabled',
        isTransient: true,
        onChange() {
            this.field.isDisabled = this.isDisabled.value;
        },
    })
    isDisabled: ui.fields.Checkbox;

    @ui.decorators.checkboxField<VisualProcess>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is helper text hidden',
        isTransient: true,
        onChange() {
            this.field.isHelperTextHidden = this.isHelperTextHidden.value;
        },
    })
    isHelperTextHidden: ui.fields.Checkbox;

    @ui.decorators.checkboxField<VisualProcess>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is hidden',
        isTransient: true,
        onChange() {
            this.field.isHidden = this.isHidden.value;
        },
    })
    isHidden: ui.fields.Checkbox;

    @ui.decorators.checkboxField<VisualProcess>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is readOnly',
        isTransient: true,
        onChange() {
            this.field.isReadOnly = this.isReadOnly.value;
        },
    })
    isReadOnly: ui.fields.Checkbox;

    @ui.decorators.checkboxField<VisualProcess>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is title hidden',
        isTransient: true,
        onChange() {
            this.field.isTitleHidden = this.isTitleHidden.value;
        },
    })
    isTitleHidden: ui.fields.Checkbox;

    @ui.decorators.textAreaField<VisualProcess>({
        parent() {
            return this.configurationBlock;
        },
        isTransient: true,
        title: 'Value',
        isReadOnly: true,
        rows: 15,
        isFullWidth: true,
    })
    value: ui.fields.TextArea;
}
