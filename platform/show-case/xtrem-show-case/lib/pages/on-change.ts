import * as ui from '@sage/xtrem-ui';
import { misc } from '../menu-items/misc';

@ui.decorators.page<OnChange>({
    title: 'Page - On change',
    authorizationCode: 'WIZARD',
    module: 'show-case',
    category: 'SHOWCASE',
    onError(error: any, _screenId: string, elementId: string) {
        return `Error caught by page. Error on ${elementId}: ${error.message || error}`;
    },
    isTransient: true,
    menuItem: misc,
})
export class OnChange extends ui.Page {
    @ui.decorators.section<OnChange>({
        isTitleHidden: true,
    })
    section: ui.containers.Section;

    @ui.decorators.block<OnChange>({
        parent() {
            return this.section;
        },
    })
    block: ui.containers.Block;

    @ui.decorators.textField<OnChange>({
        width: 'half',
        isDisabled: true,
        title: 'Field #1',
        parent() {
            return this.block;
        },
        onChange() {
            this.$.showToast(`Field #1 onChange triggered`);
        },
        helperText: 'Show a notification when "onChange" is triggered.',
    })
    field1: ui.fields.Text;

    @ui.decorators.buttonField<OnChange>({
        width: 'half',
        title: 'Trigger "onChange" for field #1',
        parent() {
            return this.block;
        },
        async onClick() {
            await this.field1.executeOnChange();
        },
        map() {
            return ui.localize('@sage/xtrem-show-case/click-me', 'Click me');
        },
    })
    button1: ui.fields.Button;

    @ui.decorators.textField<OnChange>({
        width: 'half',
        isDisabled: true,
        title: 'Field #2',
        parent() {
            return this.block;
        },
        onChange() {
            throw new Error('An error occurred');
        },
        helperText:
            'Throws an error when "onChange" is triggered. Error is caught by the button\'s "onError" handler which displays a notification.',
    })
    field2: ui.fields.Text;

    @ui.decorators.buttonField<OnChange>({
        width: 'half',
        title: 'Trigger "onChange" for field #2',
        parent() {
            return this.block;
        },
        async onClick() {
            await this.field2.executeOnChange();
        },
        map() {
            return ui.localize('@sage/xtrem-show-case/click-me', 'Click me');
        },
        onError(error: any) {
            return this.$.showToast(`Error on field #2 caught by button: ${error.message || error}`);
        },
    })
    button2: ui.fields.Button;

    @ui.decorators.textField<OnChange>({
        width: 'half',
        isDisabled: true,
        title: 'Field #3',
        parent() {
            return this.block;
        },
        onChange() {
            try {
                throw new Error('An error occurred');
            } catch (err) {
                this.$.showToast(`Error on field #3 caught by app logic: ${err.message || err}`);
            }
        },
        helperText:
            'Throws an error when "onChange" is triggered. Error is caught by app logic which displays a notification.',
    })
    field3: ui.fields.Text;

    @ui.decorators.buttonField<OnChange>({
        width: 'half',
        title: 'Trigger "onChange" for field #3',
        parent() {
            return this.block;
        },
        async onClick() {
            await this.field3.executeOnChange(true);
        },
        map() {
            return ui.localize('@sage/xtrem-show-case/click-me', 'Click me');
        },
    })
    button3: ui.fields.Button;

    @ui.decorators.textField<OnChange>({
        width: 'half',
        isDisabled: true,
        title: 'Field #4',
        parent() {
            return this.block;
        },
        onChange() {
            throw new Error('An error occurred');
        },
        helperText:
            'Throws an error when "onChange" is triggered. Error is caught by the page\'s "onError" handler which displays a notification.',
    })
    field4: ui.fields.Text;

    @ui.decorators.buttonField<OnChange>({
        width: 'half',
        title: 'Trigger "onChange" for field #4',
        parent() {
            return this.block;
        },
        async onClick() {
            await this.field4.executeOnChange();
        },
        map() {
            return ui.localize('@sage/xtrem-show-case/click-me', 'Click me');
        },
    })
    button4: ui.fields.Button;
}
