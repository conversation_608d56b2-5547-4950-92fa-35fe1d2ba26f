import { GraphApi, ShowCaseProduct } from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import { containers } from '../menu-items/containers';

@ui.decorators.page<Block, ShowCaseProduct>({
    authorizationCode: 'SHCSBLK',
    category: 'SHOWCASE',
    menuItem: containers,
    module: 'show-case',
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.text({ bind: 'product', title: 'Product' }),
            line2: ui.nestedFields.text({ bind: '_id', title: 'Id' }),
        },
    },
    node: '@sage/xtrem-show-case/ShowCaseProduct',
    title: 'Block',
})
export class Block extends ui.Page<GraphApi> {
    @ui.decorators.section<Block>({
        title: 'Product',
        mode: 'accordion',
    })
    section: ui.containers.Section;

    @ui.decorators.block<Block>({
        parent() {
            return this.section;
        },
        title: 'Product Information',
    })
    block: ui.containers.Block;

    @ui.decorators.textField<Block>({
        bind: '_id',
        parent() {
            return this.block;
        },
        title: 'Id',
    })
    id: ui.fields.Text;

    @ui.decorators.textField<Block>({
        bind: 'product',
        parent() {
            return this.block;
        },
        title: 'Product',
    })
    product: ui.fields.Text;
}
