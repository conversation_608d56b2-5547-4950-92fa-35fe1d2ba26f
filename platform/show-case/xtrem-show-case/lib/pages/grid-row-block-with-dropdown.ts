import { Graph<PERSON><PERSON>, ShowCaseProduct, ShowCaseProvider } from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import { tableField } from '../menu-items/table-field';

@ui.decorators.page<GridRowBlockWithDropdown, ShowCaseProvider>({
    module: 'show-case',
    category: 'SHOWCASE',
    detailPanel() {
        return {
            header: this.detailPanelHead,
            sections: [this.detailPanelBody],
        };
    },
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.text({ bind: 'textField' }),
            titleRight: ui.nestedFields.text({ bind: '_id' }),
        },
    },
    node: '@sage/xtrem-show-case/ShowCaseProvider',
    title: 'GridRowBlock with Dropdown',
    menuItem: tableField,
})
export class GridRowBlockWithDropdown extends ui.Page<GraphApi> {
    @ui.decorators.section<GridRowBlockWithDropdown>({})
    section: ui.containers.Section;

    @ui.decorators.block<GridRowBlockWithDropdown>({
        parent() {
            return this.section;
        },
    })
    block: ui.containers.Block;

    @ui.decorators.tableField<GridRowBlockWithDropdown, ShowCaseProduct>({
        bind: 'products',
        canFilter: false,
        canSelect: false,
        columns: [
            ui.nestedFields.text({
                bind: '_id',
                isReadOnly: true,
                title: 'Id',
                width: 'medium',
            }),
            ui.nestedFields.dropdownList({
                bind: 'category',
                optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
                title: 'Category',
                width: 'medium',
            }),
            ui.nestedFields.text({
                bind: 'product',
                isReadOnly: true,
                title: 'Product',
                width: 'medium',
            }),
        ],
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        onRowClick(id: string) {
            this.gridRowBlock.selectedRecordId = id;
            this.$.detailPanel!.isHidden = false;
        },
        parent() {
            return this.block;
        },
        title: 'Products',
    })
    field: ui.fields.Table<ShowCaseProduct>;

    @ui.decorators.section<GridRowBlockWithDropdown>({})
    detailPanelHead: ui.containers.Section;

    @ui.decorators.block<GridRowBlockWithDropdown>({
        parent() {
            return this.detailPanelHead;
        },
    })
    detailPanelHeadBlock: ui.containers.Block;

    @ui.decorators.section<GridRowBlockWithDropdown>({
        title: 'Product',
    })
    detailPanelBody: ui.containers.Section;

    @ui.decorators.gridRowBlock<GridRowBlockWithDropdown>({
        boundTo() {
            return this.field;
        },
        parent() {
            return this.detailPanelBody;
        },
        title: 'GridRowBlock',
    })
    gridRowBlock: ui.containers.GridRowBlock;
}
