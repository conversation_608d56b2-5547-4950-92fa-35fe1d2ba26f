import * as ui from '@sage/xtrem-ui';
import { validation } from '../menu-items/validation';

@ui.decorators.page<TabSectionValidation>({
    module: 'show-case',
    title: 'Page - Tabs Layout with validations',
    isTransient: true,
    mode: 'tabs',
    businessActions() {
        return [this.validate];
    },
    menuItem: validation,
})
export class TabSectionValidation extends ui.Page {
    @ui.decorators.pageAction<TabSectionValidation>({
        title: 'Validate',
        async onClick() {
            await this.$.page.validateWithDetails();
        },
    })
    validate: ui.PageAction;

    @ui.decorators.section<TabSectionValidation>({
        title: 'Responsive blocks',
    })
    mainSection: ui.containers.Section;

    @ui.decorators.block<TabSectionValidation>({
        parent() {
            return this.mainSection;
        },
        title: 'Extra-large Block',
        width: 'extra-large',
    })
    extraLargeBlock: ui.containers.Block;

    @ui.decorators.textField<TabSectionValidation>({
        parent() {
            return this.extraLargeBlock;
        },
        title: 'Small field',
        width: 'small',
    })
    textField4: ui.fields.Text;

    @ui.decorators.textField<TabSectionValidation>({
        parent() {
            return this.extraLargeBlock;
        },
        title: 'Medium field',
        width: 'medium',
        isMandatory: true,
    })
    textField41: ui.fields.Text;

    @ui.decorators.textField<TabSectionValidation>({
        parent() {
            return this.extraLargeBlock;
        },
        title: 'Large field',
        width: 'large',
    })
    textField42: ui.fields.Text;

    @ui.decorators.textField<TabSectionValidation>({
        parent() {
            return this.extraLargeBlock;
        },
        title: 'Standard field',
        isMandatory: true,
    })
    textField43: ui.fields.Text;

    @ui.decorators.textField<TabSectionValidation>({
        parent() {
            return this.extraLargeBlock;
        },
        title: 'Standard field',
        isMandatory: true,
    })
    textField44: ui.fields.Text;

    @ui.decorators.textField<TabSectionValidation>({
        parent() {
            return this.extraLargeBlock;
        },
        title: 'Standard field',
    })
    textField45: ui.fields.Text;

    @ui.decorators.textField<TabSectionValidation>({
        parent() {
            return this.extraLargeBlock;
        },
        title: 'Standard field',
    })
    textField46: ui.fields.Text;

    @ui.decorators.textField<TabSectionValidation>({
        parent() {
            return this.extraLargeBlock;
        },
        title: 'Standard field',
        isMandatory: true,
    })
    textField47: ui.fields.Text;

    @ui.decorators.textField<TabSectionValidation>({
        parent() {
            return this.extraLargeBlock;
        },
        title: 'Standard field',
    })
    textField48: ui.fields.Text;

    @ui.decorators.textField<TabSectionValidation>({
        parent() {
            return this.extraLargeBlock;
        },
        title: 'Standard field',
    })
    textField49: ui.fields.Text;

    @ui.decorators.block<TabSectionValidation>({
        parent() {
            return this.mainSection;
        },
        title: 'Small Block',
        width: 'small',
    })
    smallBlock1: ui.containers.Block;

    @ui.decorators.textField<TabSectionValidation>({
        parent() {
            return this.smallBlock1;
        },
        title: 'Standard field',
    })
    textField3: ui.fields.Text;

    @ui.decorators.textField<TabSectionValidation>({
        parent() {
            return this.smallBlock1;
        },
        title: 'Standard field',
        isMandatory: true,
    })
    textField32: ui.fields.Text;

    @ui.decorators.textField<TabSectionValidation>({
        parent() {
            return this.smallBlock1;
        },
        title: 'Standard field',
    })
    textField33: ui.fields.Text;

    @ui.decorators.textField<TabSectionValidation>({
        parent() {
            return this.smallBlock1;
        },
        title: 'Standard field',
    })
    textField34: ui.fields.Text;

    @ui.decorators.block<TabSectionValidation>({
        parent() {
            return this.mainSection;
        },
        title: 'Large Block',
        width: 'large',
    })
    largeBlock: ui.containers.Block;

    @ui.decorators.textField<TabSectionValidation>({
        parent() {
            return this.largeBlock;
        },
        title: 'Medium field',
        width: 'medium',
        isMandatory: true,
    })
    textField11: ui.fields.Text;

    @ui.decorators.textField<TabSectionValidation>({
        parent() {
            return this.largeBlock;
        },
        title: 'Small field',
        width: 'small',
        isMandatory: true,
    })
    textField12: ui.fields.Text;

    @ui.decorators.textField<TabSectionValidation>({
        parent() {
            return this.largeBlock;
        },
        title: 'Medium field',
        width: 'medium',
        isMandatory: true,
    })
    textField13: ui.fields.Text;

    @ui.decorators.textField<TabSectionValidation>({
        parent() {
            return this.largeBlock;
        },
        title: 'Large field',
        width: 'large',
        isMandatory: true,
    })
    textField14: ui.fields.Text;

    @ui.decorators.textField<TabSectionValidation>({
        parent() {
            return this.largeBlock;
        },
        title: 'Large field',
        width: 'large',
        isMandatory: true,
    })
    textField15: ui.fields.Text;

    @ui.decorators.section<TabSectionValidation>({
        title: 'Another Section',
    })
    anotherSection: ui.containers.Section;

    @ui.decorators.block<TabSectionValidation>({
        parent() {
            return this.anotherSection;
        },
        title: 'Medium Block 1',
        width: 'medium',
    })
    mediumBlock1: ui.containers.Block;

    @ui.decorators.textField<TabSectionValidation>({
        parent() {
            return this.mediumBlock1;
        },
        title: 'Large field',
        width: 'large',
    })
    textField2: ui.fields.Text;

    @ui.decorators.textField<TabSectionValidation>({
        parent() {
            return this.mediumBlock1;
        },
        title: 'Small field',
        width: 'small',
    })
    textField22: ui.fields.Text;

    @ui.decorators.textField<TabSectionValidation>({
        parent() {
            return this.mediumBlock1;
        },
        title: 'Medium field',
        width: 'medium',
    })
    textField23: ui.fields.Text;

    @ui.decorators.textField<TabSectionValidation>({
        parent() {
            return this.mediumBlock1;
        },
        title: 'Medium field',
        width: 'medium',
    })
    textField24: ui.fields.Text;

    @ui.decorators.block<TabSectionValidation>({
        parent() {
            return this.anotherSection;
        },
        title: 'Medium Block 2',
        width: 'medium',
    })
    mediumBlock2: ui.containers.Block;

    @ui.decorators.textField<TabSectionValidation>({
        parent() {
            return this.mediumBlock2;
        },
        title: 'Large field',
        width: 'large',
    })
    textField7: ui.fields.Text;

    @ui.decorators.textField<TabSectionValidation>({
        parent() {
            return this.mediumBlock2;
        },
        title: 'Small field',
        width: 'small',
        isMandatory: true,
    })
    textField72: ui.fields.Text;

    @ui.decorators.textField<TabSectionValidation>({
        parent() {
            return this.mediumBlock2;
        },
        title: 'Standard field',
        isMandatory: true,
    })
    textField73: ui.fields.Text;

    @ui.decorators.textField<TabSectionValidation>({
        parent() {
            return this.mediumBlock2;
        },
        title: 'Standard field',
        isMandatory: true,
    })
    textField74: ui.fields.Text;

    @ui.decorators.textField<TabSectionValidation>({
        parent() {
            return this.mediumBlock2;
        },
        title: 'Standard field',
        isMandatory: true,
    })
    textField75: ui.fields.Text;

    @ui.decorators.section<TabSectionValidation>({
        title: 'Another Section',
    })
    thirdSection: ui.containers.Section;

    @ui.decorators.block<TabSectionValidation>({
        parent() {
            return this.thirdSection;
        },
        title: 'Medium Block 3',
        width: 'medium',
    })
    mediumBlock3: ui.containers.Block;

    @ui.decorators.textField<TabSectionValidation>({
        parent() {
            return this.mediumBlock3;
        },
        title: 'Standard field',
        isMandatory: true,
    })
    textField81: ui.fields.Text;

    @ui.decorators.block<TabSectionValidation>({
        parent() {
            return this.thirdSection;
        },
        title: 'Medium Block 4',
        width: 'medium',
    })
    mediumBlock4: ui.containers.Block;

    @ui.decorators.textField<TabSectionValidation>({
        parent() {
            return this.mediumBlock4;
        },
        title: 'Standard field',
        isMandatory: true,
    })
    textField91: ui.fields.Text;
}
