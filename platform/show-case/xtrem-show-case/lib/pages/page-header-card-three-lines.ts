import * as ui from '@sage/xtrem-ui';
import { misc } from '../menu-items/misc';

@ui.decorators.page<PageHeaderCardThreeLines>({
    areNavigationTabsHidden: true,
    authorizationCode: 'HDRCRD',
    title: 'Three lines',
    module: 'show-case',
    category: 'SHOWCASE',
    menuItem: misc,
    isTransient: true,
    headerCard() {
        return {
            title: this.headerText1,
            titleRight: this.headerText2,
            line2: this.headerText3,
            line2Right: this.headerText4,
            line3: this.headerText5,
            line3Right: this.headerText6,
        };
    },
    onLoad() {
        this.headerText1.value = 'Wasabi';
        this.headerText2.value = 'tastes';
        this.headerText3.value = 'VERY';
        this.headerText4.value = 'spicy';
        this.headerText5.value = 'Another';
        this.headerText6.value = 'Line';
    },
})
export class PageHeaderCardThreeLines extends ui.Page {
    @ui.decorators.textField<PageHeaderCardThreeLines>({
        isReadOnly: true,
    })
    headerText1: ui.fields.Text;

    @ui.decorators.textField<PageHeaderCardThreeLines>({})
    headerText2: ui.fields.Text;

    @ui.decorators.textField<PageHeaderCardThreeLines>({})
    headerText3: ui.fields.Text;

    @ui.decorators.textField<PageHeaderCardThreeLines>({
        isReadOnly: true,
    })
    headerText4: ui.fields.Text;

    @ui.decorators.textField<PageHeaderCardThreeLines>({})
    headerText5: ui.fields.Text;

    @ui.decorators.textField<PageHeaderCardThreeLines>({
        isReadOnly: true,
    })
    headerText6: ui.fields.Text;

    @ui.decorators.section<PageHeaderCardThreeLines>({
        title: 'Page body',
    })
    section: ui.containers.Section;

    @ui.decorators.block<PageHeaderCardThreeLines>({
        parent() {
            return this.section;
        },
    })
    fieldBlock: ui.containers.Block;

    @ui.decorators.labelField<PageHeaderCardThreeLines>({
        parent() {
            return this.fieldBlock;
        },
        map() {
            return 'Nothing to see on this page except on XS breakpoint';
        },
    })
    infoLabel: ui.fields.Label;

    @ui.decorators.textAreaField<PageHeaderCardThreeLines>({
        parent() {
            return this.fieldBlock;
        },
        rows: 15,
        isFullWidth: true,
        title: 'Large text area',
        helperText: 'This field is to trigger scrolling',
    })
    textArea: ui.fields.TextArea;
}
