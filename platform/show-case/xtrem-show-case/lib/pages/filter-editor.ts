import { MetaNodeFactory } from '@sage/xtrem-metadata-api';
import { Dict, Property } from '@sage/xtrem-shared';
import * as ui from '@sage/xtrem-ui';
import { MonacoPluginProperties } from '@sage/xtrem-ui-plugin-monaco';
import { fields } from '../menu-items/fields';

@ui.decorators.page<FilterEditor>({
    menuItem: fields,
    title: 'Filter Editor',
    subtitle: 'Fields',
    isTransient: true,
    headerSection() {
        return this.headerSection;
    },
})
export class FilterEditor extends ui.Page {
    @ui.decorators.section<FilterEditor>({
        title: 'FilterEditor',
        isTitleHidden: true,
    })
    headerSection: ui.containers.Section;

    @ui.decorators.section<FilterEditor>({
        title: 'FilterEditor field',
        isTitleHidden: true,
    })
    section: ui.containers.Section;

    @ui.decorators.block<FilterEditor>({
        parent() {
            return this.headerSection;
        },
        title: 'Introduction',
    })
    introductionBlock: ui.containers.Block;

    @ui.decorators.staticContentField<FilterEditor>({
        parent() {
            return this.introductionBlock;
        },
        isTitleHidden: true,
        width: 'large',
        content:
            'Filter editor field is meant for usage in wizards for example for data export and import. It can be bound to a JSON property on the server.',
    })
    description: ui.fields.StaticContent;

    @ui.decorators.linkField<FilterEditor>({
        parent() {
            return this.introductionBlock;
        },
        title: 'Source code',
        isFullWidth: true,
        map() {
            return ui.localize('@sage/xtrem-show-case/check-source-code', 'Check it on GitHub');
        },
        page: 'https://github.com/Sage-ERP-X3/xtrem/blob/master/platform/show-case/xtrem-show-case/lib/pages/filter-editor.ts',
    })
    sourceCodeLink: ui.fields.Link;

    @ui.decorators.block<FilterEditor>({
        parent() {
            return this.headerSection;
        },
        title: 'Field example',
        width: 'extra-large',
    })
    fieldBlock: ui.containers.Block;

    @ui.decorators.filterEditorField<FilterEditor>({
        parent() {
            return this.fieldBlock;
        },
        onClick() {
            this.clickTriggered.isHidden = false;
            setTimeout(() => {
                this.clickTriggered.isHidden = true;
            }, 5000);
        },
        isFullWidth: true,
        node: 'ShowCaseEmployee',
        selectedProperties: {
            lastName: {
                label: 'Last name',
                data: {
                    type: 'String',
                    kind: 'SCALAR',
                    isCollection: false,
                    name: 'lastName',
                    canFilter: true,
                    canSort: true,
                    label: 'Last name',
                    enumType: null,
                    isCustom: false,
                    dataType: '',
                    targetNode: '',
                    isStored: false,
                    isOnInputType: false,
                    isOnOutputType: false,
                    isMutable: false,
                },
                id: 'lastName',
                key: 'lastName',
                labelKey: 'Last name',
                labelPath: 'Last name',
            },
            firstName: {
                label: 'First name',
                data: {
                    type: 'String',
                    kind: 'SCALAR',
                    isCollection: false,
                    name: 'firstName',
                    canFilter: true,
                    canSort: true,
                    label: 'First name',
                    enumType: null,
                    isCustom: false,
                    dataType: '',
                    targetNode: '',
                    isStored: false,
                    isOnInputType: false,
                    isOnOutputType: false,
                    isMutable: false,
                },
                id: 'firstName',
                key: 'firstName',
                labelKey: 'First name',
                labelPath: 'First name',
            },
            email: {
                label: 'Email',
                data: {
                    type: 'String',
                    kind: 'SCALAR',
                    isCollection: false,
                    name: 'email',
                    canFilter: true,
                    canSort: true,
                    label: 'Email',
                    enumType: null,
                    isCustom: false,
                    dataType: '',
                    targetNode: '',
                    isStored: false,
                    isOnInputType: false,
                    isOnOutputType: false,
                    isMutable: false,
                },
                id: 'email',
                key: 'email',
                labelKey: 'Email',
                labelPath: 'Email',
            },
            city: {
                label: 'City',
                data: {
                    type: 'String',
                    kind: 'SCALAR',
                    isCollection: false,
                    name: 'city',
                    canFilter: true,
                    canSort: true,
                    label: 'City',
                    enumType: null,
                    isCustom: false,
                    dataType: '',
                    targetNode: '',
                    isStored: false,
                    isOnInputType: false,
                    isOnOutputType: false,
                    isMutable: false,
                },
                id: 'city',
                key: 'city',
                labelKey: 'City',
                labelPath: 'City',
            },
        },
        onChange() {
            this.valueField.value = JSON.stringify(this.field.value || {}, null, 4);
            this.changeTriggered.isHidden = false;
            setTimeout(() => {
                this.changeTriggered.isHidden = true;
            }, 5000);
        },
    })
    field: ui.fields.FilterEditor;

    @ui.decorators.labelField<FilterEditor>({
        parent() {
            return this.fieldBlock;
        },
        isHidden: true,
        map() {
            return 'Change was triggered';
        },
    })
    changeTriggered: ui.fields.Label;

    @ui.decorators.labelField<FilterEditor>({
        parent() {
            return this.fieldBlock;
        },
        isHidden: true,
        map() {
            return 'Click was triggered';
        },
    })
    clickTriggered: ui.fields.Label;

    @ui.decorators.block<FilterEditor>({
        parent() {
            return this.section;
        },
        title: 'Configuration',
    })
    configurationBlock: ui.containers.Block;

    @ui.decorators.checkboxField<FilterEditor>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Pod Mode',
        onChange() {
            this.field.mode = this.podMode.value ? 'pod' : 'table';
        },
    })
    podMode: ui.fields.Checkbox;

    @ui.decorators.checkboxField<FilterEditor>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is hidden',
        helperText: 'Determines whether the field is displayed or not.',
        isFullWidth: true,
        isReversed: true,
        onChange() {
            this.field.isHidden = !!this.isHidden.value;
        },
    })
    isHidden: ui.fields.Checkbox;

    @ui.decorators.textField<FilterEditor>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Title',
        isFullWidth: true,
        helperText:
            'The title that is displayed above the field. The title can be provided as a string, or a callback function returning a string. When declared as a callback within the column of a nested grid, the column id is provided as a parameter. It is automatically picked up by the i18n engine and externalized for translation.',
        onChange() {
            this.field.title = this.title.value || '';
        },
    })
    title: ui.fields.Text;

    @ui.decorators.checkboxField<FilterEditor>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is title hidden',
        isFullWidth: true,
        helperText: 'Whether the field title above the field should be displayed and its vertical space preserved.',
        isReversed: true,
        onChange() {
            this.field.isTitleHidden = !!this.isTitleHidden.value;
        },
    })
    isTitleHidden: ui.fields.Checkbox;

    @ui.decorators.textField<FilterEditor>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Helper text',
        helperText:
            'The helper text that is displayed above the field. It is automatically picked up by the i18n engine and externalized.',
        isFullWidth: true,
        onChange() {
            this.field.helperText = this.helperText.value || '';
        },
    })
    helperText: ui.fields.Text;

    @ui.decorators.checkboxField<FilterEditor>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is helper text hidden',
        isFullWidth: true,
        isReversed: true,
        onChange() {
            this.field.isHelperTextHidden = this.isHelperTextHidden.value;
        },
    })
    isHelperTextHidden: ui.fields.Checkbox;

    @ui.decorators.block<FilterEditor>({
        parent() {
            return this.section;
        },
        title: 'Events',
    })
    eventsBlock: ui.containers.Block;

    @ui.decorators.staticContentField<FilterEditor>({
        parent() {
            return this.eventsBlock;
        },
        isTitleHidden: true,
        isFullWidth: true,
        isMarkdown: true,
        content:
            '-   **onClick**: Triggered when any parts of the field is clicked, no arguments provided.\n-   **onChange**: Triggered when the field value changed and the focus is about to move away from the field, no arguments provided.\n-   **onError**: Handles errors thrown from the callback functions\n-   **onInputValueChange**: Triggered when the user stops typing into the input of the field. The execution is debounced by 150ms to cater for continuos typing.',
    })
    eventsDescription: ui.fields.StaticContent;

    @ui.decorators.block<FilterEditor>({
        parent() {
            return this.section;
        },
        title: 'Runtime functions',
    })
    runtimeFunctionsBlock: ui.containers.Block;

    @ui.decorators.staticContentField<FilterEditor>({
        parent() {
            return this.runtimeFunctionsBlock;
        },
        isTitleHidden: true,
        isFullWidth: true,
        isMarkdown: true,
        content:
            "-   **focus()**: Moves the focus to the field.\n-   **getNextField(isFocusable)**: Returns the next field instance. The order is calculated by the page prototype. If the isFocusable argument is set to true, it returns the next visible, enabled and non read-only field. It only considers the committed page state, so `commitValueAndPropertyChanges` call might be required beforehand to get the expected result.\n-   **refresh()**: Refetches the field's value from the server and updates it on the screen, only for non-transient pages.\n-   **validate()**: Triggers the field validation rules. Since the validation rules might be asynchronous, this method returns a promise that must be awaited to get the validation result\n-   **validateWithDetails()**: In addition to the functionality of `validate` it returns more details, including the rule that failed and where applicable, the row ID and colum ID.\n-   **fetchDefault(skipSet)**: Force re-fetches default value for the field. If the `skipSet` flag is set to true, it returns the default values but not apply them to the screen.\n-   **isDirty()**: Sets or gets the dirty state of the field.",
    })
    runtimeFunctionDescription: ui.fields.StaticContent;

    /* Additional examples */
    @ui.decorators.block<FilterEditor>({
        parent() {
            return this.section;
        },
        title: 'Value',
    })
    valueBlock: ui.containers.Block;

    @ui.decorators.pluginField<FilterEditor, MonacoPluginProperties>({
        parent() {
            return this.valueBlock;
        },
        title: 'Value',
        helperText: 'Value of the filter component in JSON representation',
        isFullWidth: true,
        pluginPackage: '@sage/xtrem-ui-plugin-monaco',
        language: 'json',
        height: 400,
        isReadOnly: true,
    })
    valueField: ui.fields.Plugin<MonacoPluginProperties>;

    @ui.decorators.buttonField<FilterEditor>({
        parent() {
            return this.valueBlock;
        },
        title: 'Reset value',
        isTitleHidden: true,
        map() {
            return 'Reset value';
        },
        onClick() {
            this.field.value = null;
            this.valueField.value = null;
        },
    })
    resetFieldValue: ui.fields.Button;

    @ui.decorators.referenceField<FilterEditor, MetaNodeFactory>({
        parent() {
            return this.configurationBlock;
        },
        orderBy: {
            name: 1,
        },
        columns: [
            ui.nestedFields.text({ bind: 'name', title: 'Record type', isHidden: true }),
            ui.nestedFields.text({ bind: 'title', title: 'Node name' }),
            ui.nestedFields.text({
                bind: { package: { name: true } }, // 'package',
                title: 'Package',
                isHidden: true,
            }),
        ],
        node: '@sage/xtrem-metadata/MetaNodeFactory',
        title: 'Node',
        valueField: 'title',
        helperTextField: { package: { name: true } },
        minLookupCharacters: 0,
        isAutoSelectEnabled: true,
        isTransient: true,
        async onChange() {
            if (this.filterNode.value) {
                this.fieldBlock.isHidden = true;
                this.field.node = this.filterNode.value.name;
                const properties = await ui.fetchNodeDetails({
                    nodeName: this.filterNode.value.name,
                    locale: this.$.locale,
                });

                this.field.selectedProperties = Object.keys(properties).reduce((prevValue: Dict<Property>, r) => {
                    const data = properties[r];
                    prevValue[data.name] = {
                        data,
                        id: data.name,
                        key: data.name,
                        labelKey: data.label,
                        labelPath: data.label,
                        label: data.label,
                    };
                    return prevValue;
                }, {});
                this.field.value = { filters: [], parameters: [] };
                this.fieldBlock.isHidden = false;
            }
        },
    })
    filterNode: ui.fields.Reference;
}
