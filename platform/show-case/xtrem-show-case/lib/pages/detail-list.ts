import { Graph<PERSON><PERSON>, ShowCaseProduct, ShowCaseProvider } from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import { fields } from '../menu-items/fields';

@ui.decorators.page<DetailList, ShowCaseProvider>({
    authorizationCode: 'BSCFLDDL',
    module: 'show-case',
    title: 'Field - Detail list',
    node: '@sage/xtrem-show-case/ShowCaseProvider',
    category: 'SHOWCASE',
    menuItem: fields,
    defaultEntry: () => '1',
})
export class DetailList extends ui.Page<GraphApi> {
    @ui.decorators.section<DetailList>({ isTitleHidden: true })
    section: ui.containers.Section;

    @ui.decorators.block<DetailList>({
        isTitleHidden: true,
        parent() {
            return this.section;
        },
    })
    fieldBlock: ui.containers.Block;

    @ui.decorators.detailListField<DetailList, ShowCaseProduct>({
        parent() {
            return this.fieldBlock;
        },
        orderBy: { _id: 1 },
        bind: 'products',
        onRecordClick(item: any) {
            this.$.dialog.message(
                'info',
                'Item clicked',
                `Clicked field ${item.product} with Description ${item.description}`,
            );
        },
        fields: [
            ui.nestedFields.text({
                bind: '_id',
                title: 'ID:',
            }),
            ui.nestedFields.text({
                bind: 'description',
                title: 'Description',
            }),
            ui.nestedFields.text<DetailList, ShowCaseProduct>({
                bind: 'st',
                title: 'St',
                postfix(id, rowData) {
                    return `dynamic postfix - ${rowData?._id}`;
                },
            }),
            ui.nestedFields.numeric({
                bind: 'qty',
                title: 'Quantity',
                scale: 0,
            }),
            ui.nestedFields.link({
                bind: 'product',
                title: 'Product',
                onClick(_id) {
                    this.$.router.goTo('@sage/xtrem-show-case/ShowCaseProduct', { _id });
                },
            }),
            ui.nestedFields.label<DetailList, ShowCaseProduct>({
                bind: 'testHidden' as any,
                isTransient: true,
                title: 'Conditional display',
                map() {
                    return 'Only displayed with even IDs';
                },
                isHidden(value, rowValue) {
                    return Number(rowValue?._id) % 2 === 0;
                },
            }),
            ui.nestedFields.label<DetailList, ShowCaseProduct>({
                bind: 'testHiddenDesktop' as any,
                isTransient: true,
                title: 'Hidden on desktop',
                map() {
                    return 'You should not see me on desktop';
                },
                isHiddenDesktop: true,
            }),
            ui.nestedFields.label<DetailList, ShowCaseProduct>({
                bind: 'testHiddenMobile' as any,
                isTransient: true,
                title: 'Hidden on mobile',
                map() {
                    return 'You should not see me on mobile';
                },
                isHiddenMobile: true,
            }),
            ui.nestedFields.label<DetailList, ShowCaseProduct>({
                bind: 'testTitleHidden' as any,
                isTransient: true,
                title: 'Hidden on title',
                map() {
                    return 'My title is hidden :-(';
                },

                isTitleHidden: true,
            }),
            ui.nestedFields.reference<DetailList, ShowCaseProduct, ShowCaseProvider>({
                bind: 'provider',
                node: '@sage/xtrem-show-case/ShowCaseProvider',
                title: 'Provider',
                valueField: 'textField',
            }),
        ],
    })
    field: ui.fields.DetailList<ShowCaseProduct, DetailList>;

    @ui.decorators.block<DetailList>({
        parent() {
            return this.section;
        },
        title: 'Configuration',
    })
    configurationBlock: ui.containers.Block;

    @ui.decorators.textField<DetailList>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Helper text',
        onChange() {
            this.field.helperText = this.helperText.value;
        },
        isTransient: true,
    })
    helperText: ui.fields.Text;

    @ui.decorators.checkboxField<DetailList>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is disabled',
        onChange() {
            this.field.isDisabled = this.isDisabled.value;
        },
        isTransient: true,
    })
    isDisabled: ui.fields.Checkbox;

    @ui.decorators.checkboxField<DetailList>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is helper text hidden',
        onChange() {
            this.field.isHelperTextHidden = this.isHelperTextHidden.value;
        },
        isTransient: true,
    })
    isHelperTextHidden: ui.fields.Checkbox;

    @ui.decorators.checkboxField<DetailList>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is hidden',
        onChange() {
            this.field.isHidden = this.isHidden.value;
        },
        isTransient: true,
    })
    isHidden: ui.fields.Checkbox;

    @ui.decorators.checkboxField<DetailList>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is title hidden',
        onChange() {
            this.field.isTitleHidden = this.isTitleHidden.value;
        },
        isTransient: true,
    })
    isTitleHidden: ui.fields.Checkbox;

    @ui.decorators.textField<DetailList>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Title',
        onChange() {
            this.field.title = this.title.value;
        },
        isTransient: true,
    })
    title: ui.fields.Text;

    /* Additional examples */

    @ui.decorators.block<DetailList>({
        parent() {
            return this.section;
        },
        title: 'Additional examples',
    })
    additionalBlock: ui.containers.Block;

    @ui.decorators.detailListField<DetailList, ShowCaseProduct>({
        bind: 'products',
        title: 'Restricted result set',
        fields: [
            ui.nestedFields.text({
                bind: 'product',
                title: 'Product',
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                bind: 'description',
                title: 'Description',
                isReadOnly: true,
            }),
            ui.nestedFields.numeric({
                bind: 'qty',
                title: 'Quantity',
                scale: 0,
                isReadOnly: true,
            }),
        ],
        parent() {
            return this.additionalBlock;
        },
    })
    restrictedResultSet: ui.fields.DetailList<ShowCaseProduct>;

    @ui.decorators.textField<DetailList>({
        isTransient: true,
        parent() {
            return this.additionalBlock;
        },
        title: 'Result set restriction (on product name)',
        onChange() {
            const filter = this.resultSetRestriction.value
                ? {
                      product: { _regex: this.resultSetRestriction.value, _options: 'i' },
                  }
                : undefined;

            this.restrictedResultSet.filter = filter;
        },
    })
    resultSetRestriction: ui.fields.Text;
}
