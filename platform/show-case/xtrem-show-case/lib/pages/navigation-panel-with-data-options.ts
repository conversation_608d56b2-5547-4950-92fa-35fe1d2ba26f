import { GraphApi, ShowCaseProduct } from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import { navigationPanel } from '../menu-items/navigation-panel';

@ui.decorators.page<NavigationPanelWithDataOptions, ShowCaseProduct>({
    authorizationCode: 'NAVPNLDATOPT',
    category: 'SHOWCASE',
    idField() {
        return this.id;
    },
    menuItem: navigationPanel,
    module: 'show-case',
    navigationPanel: {
        bulkActions: [
            {
                mutation: 'export',
                title: 'Export',
                buttonType: 'secondary',
                icon: 'file_excel',
                isDestructive: false,
                id: 'export',
            },
            {
                mutation: 'addPrefixToProductName',
                title: 'Add prefix to name',
                buttonType: 'secondary',
                icon: 'edit',
                configurationPage: '@sage/xtrem-show-case/ShowCaseProductRenameActionDialog',
                isDestructive: false,
                id: 'addPrefixToProductName',
            },
        ],
        listItem: {
            title: ui.nestedFields.text({ bind: 'product', title: 'Product' }),
            titleRight: ui.nestedFields.label({ bind: '_id', title: 'Id' }),
            line2: ui.nestedFields.dropdownList({
                bind: 'category',
                canFilter: true,
                optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
                title: 'Category',
            }),
            line3: ui.nestedFields.reference({
                bind: 'provider',
                canFilter: true,
                node: '@sage/xtrem-show-case/ShowCaseProvider',
                title: 'Provider',
                valueField: 'textField',
            }),
        },
        optionsMenu: [
            {
                graphQLFilter: { _id: { _lt: '11' }, category: { _in: ['great', 'good', 'ok'] } },
                title: 'Positive Categories',
            },
            {
                graphQLFilter: { _id: { _lt: '11' }, category: { _in: ['notBad', 'awful'] } },
                title: 'Negative Categories',
            },
            {
                graphQLFilter: { _id: { _lt: '11' } },
                title: 'All Categories',
            },
        ],
        orderBy: { _id: 1 },
    },
    node: '@sage/xtrem-show-case/ShowCaseProduct',
    objectTypePlural: 'Products',
    objectTypeSingular: 'Product',
    title: 'Navigation Panel (with Data Options)',
})
export class NavigationPanelWithDataOptions extends ui.Page<GraphApi> {
    @ui.decorators.section<NavigationPanelWithDataOptions>({})
    section: ui.containers.Section;

    @ui.decorators.block<NavigationPanelWithDataOptions>({
        isDisabled: true,
        parent() {
            return this.section;
        },
    })
    block: ui.containers.Block;

    @ui.decorators.textField<NavigationPanelWithDataOptions>({
        bind: '_id',
        parent() {
            return this.block;
        },
        title: 'Id',
    })
    id: ui.fields.Text;

    @ui.decorators.textField<NavigationPanelWithDataOptions>({
        bind: 'product',
        parent() {
            return this.block;
        },
        title: 'Product',
    })
    product: ui.fields.Text;

    @ui.decorators.textField<NavigationPanelWithDataOptions>({
        bind: 'category',
        parent() {
            return this.block;
        },
        title: 'Category',
    })
    category: ui.fields.Text;
}
