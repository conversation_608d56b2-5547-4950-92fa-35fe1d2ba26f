import * as ui from '@sage/xtrem-ui';
import { fields } from '../menu-items/fields';

@ui.decorators.page<SelectionCard>({
    authorizationCode: 'BSCFLDS',
    module: 'show-case',
    title: 'Field - SelectionCard',
    category: 'SHOWCASE',
    isTransient: true,
    menuItem: fields,
})
export class SelectionCard extends ui.Page {
    @ui.decorators.section<SelectionCard>({
        title: 'SelectionCard field',
    })
    section: ui.containers.Section;

    @ui.decorators.block<SelectionCard>({
        parent() {
            return this.section;
        },
        title: 'Field example',
    })
    fieldBlock: ui.containers.Block;

    @ui.decorators.selectionCard<SelectionCard>({
        title: 'SelectionCard group',
        icon() {
            return 'apple';
        },
        description() {
            return 'Add description';
        },
        parent() {
            return this.fieldBlock;
        },
        helperText: 'helper text',
        onChange() {
            if (this.field.value !== this.value.value) {
                this.value.value = this.field.value;
                this.changeTriggered.isHidden = false;
                setTimeout(() => {
                    this.changeTriggered.isHidden = true;
                }, 5000);
            }
        },
        optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
    })
    field: ui.fields.SelectionCard;

    @ui.decorators.labelField<SelectionCard>({
        parent() {
            return this.fieldBlock;
        },
        isHidden: true,
        map() {
            return 'Changed was triggered';
        },
    })
    changeTriggered: ui.fields.Label;

    @ui.decorators.block<SelectionCard>({
        parent() {
            return this.section;
        },
        title: 'Configuration',
    })
    configurationBlock: ui.containers.Block;

    @ui.decorators.textField<SelectionCard>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Helper text',
        onChange() {
            this.field.helperText = this.helperText.value;
        },
    })
    helperText: ui.fields.Text;

    @ui.decorators.checkboxField<SelectionCard>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is disabled',
        onChange() {
            this.field.isDisabled = this.isDisabled.value;
        },
    })
    isDisabled: ui.fields.Checkbox;

    @ui.decorators.checkboxField<SelectionCard>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is helper text hidden',
        onChange() {
            this.field.isHelperTextHidden = this.isHelperTextHidden.value;
        },
    })
    isHelperTextHidden: ui.fields.Checkbox;

    @ui.decorators.checkboxField<SelectionCard>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is hidden',
        onChange() {
            this.field.isHidden = this.isHidden.value;
        },
    })
    isHidden: ui.fields.Checkbox;

    @ui.decorators.checkboxField<SelectionCard>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is readOnly',
        onChange() {
            this.field.isReadOnly = this.isReadOnly.value;
        },
    })
    isReadOnly: ui.fields.Checkbox;

    @ui.decorators.checkboxField<SelectionCard>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is title hidden',
        onChange() {
            this.field.isTitleHidden = this.isTitleHidden.value;
        },
    })
    isTitleHidden: ui.fields.Checkbox;

    @ui.decorators.textField<SelectionCard>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Title',
        onChange() {
            this.field.title = this.title.value;
        },
    })
    title: ui.fields.Text;

    @ui.decorators.selectionCard<SelectionCard>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Value',
        icon() {
            return 'apple';
        },
        description() {
            return 'Value description';
        },
        onChange() {
            if (this.value.value !== this.field.value) {
                this.field.value = this.value.value;
            }
        },
        optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
    })
    value: ui.fields.SelectionCard;

    @ui.decorators.buttonField<SelectionCard>({
        parent() {
            return this.configurationBlock;
        },
        map() {
            return 'Focus field';
        },
        onClick() {
            this.field.focus();
        },
    })
    focus: ui.fields.Button;

    /* Additional examples */

    @ui.decorators.block<SelectionCard>({
        parent() {
            return this.section;
        },
        title: 'Additional examples',
    })
    additionalBlock: ui.containers.Block;

    @ui.decorators.selectionCard<SelectionCard>({
        parent() {
            return this.additionalBlock;
        },
        title: 'Options set in file',
        icon() {
            return 'apple';
        },
        description() {
            return 'Add description';
        },
        isMandatory: true,
        helperText: 'The options are not from an enum',
        options: ['dog', 'cat', 'cow', 'a very long option, just to test line breaks', 'giraffe'],
    })
    inlineOption: ui.fields.SelectionCard;

    @ui.decorators.selectionCard<SelectionCard>({
        parent() {
            return this.additionalBlock;
        },
        title: 'Mandatory',
        icon() {
            return 'apple';
        },
        description() {
            return 'Add description';
        },
        isMandatory: true,
        optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
    })
    mandatory: ui.fields.SelectionCard;

    @ui.decorators.buttonField<SelectionCard>({
        parent() {
            return this.additionalBlock;
        },
        map() {
            return 'Validate mandatory field';
        },
        async onClick() {
            ui.console.log(await this.mandatory.validate());
        },
    })
    validateMandatory: ui.fields.Button;

    @ui.decorators.selectionCard<SelectionCard>({
        parent() {
            return this.additionalBlock;
        },
        title: 'Sorted with option type',
        icon() {
            return 'apple';
        },
        description() {
            return 'Add description';
        },
        isSortedAlphabetically: true,
        optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
    })
    sortedOptionType: ui.fields.SelectionCard;

    @ui.decorators.selectionCard<SelectionCard>({
        parent() {
            return this.additionalBlock;
        },
        title: 'Sorted with options',
        icon(option) {
            switch (option) {
                case 'Dreadful':
                    return 'arrow-down';
                case 'Awful':
                    return 'arrow-left';
                case 'Bad':
                    return 'arrow-right';
                case 'Mediocre':
                    return 'apple';
                case 'Great':
                    return 'arrow-up';
                default:
                    return 'apple';
            }
        },
        description() {
            return 'Add description';
        },
        isSortedAlphabetically: true,
        options: ['Dreadful', 'Awful', 'Great', 'Bad', 'Mediocre'],
    })
    sortedWithOptions: ui.fields.SelectionCard;

    @ui.decorators.selectionCard<SelectionCard>({
        parent() {
            return this.additionalBlock;
        },
        title: 'With filter',
        hasFilter: true,
        icon(option) {
            switch (option) {
                case 'Dreadful':
                    return 'arrow-down';
                case 'Awful':
                    return 'arrow-left';
                case 'Bad':
                    return 'arrow-right';
                case 'Mediocre':
                    return 'apple';
                case 'Great':
                    return 'arrow-up';
                default:
                    return 'apple';
            }
        },
        description(option) {
            return `${option} description`;
        },
        isSortedAlphabetically: true,
        options: ['Dreadful', 'Awful', 'Great', 'Bad', 'Mediocre'],
    })
    withFilter: ui.fields.SelectionCard;

    @ui.decorators.section<SelectionCard>({
        title: 'Examples in various container sizes',
    })
    variousSizeContainer: ui.containers.Section;

    @ui.decorators.block<SelectionCard>({
        parent() {
            return this.variousSizeContainer;
        },
        title: 'extra large block example',
        width: 'extra-large',
    })
    extraLargeBlock: ui.containers.Block;

    @ui.decorators.selectionCard<SelectionCard>({
        title: 'SelectionCard group',
        icon() {
            return 'apple';
        },
        description() {
            return 'Add description';
        },
        isFullWidth: true,
        parent() {
            return this.extraLargeBlock;
        },
        optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
    })
    fieldExtraLarge: ui.fields.SelectionCard;

    @ui.decorators.block<SelectionCard>({
        parent() {
            return this.variousSizeContainer;
        },
        title: 'large block example',
        width: 'large',
    })
    largeBlock: ui.containers.Block;

    @ui.decorators.selectionCard<SelectionCard>({
        title: 'SelectionCard group',
        icon() {
            return 'apple';
        },
        description() {
            return 'Add description';
        },
        isFullWidth: true,
        parent() {
            return this.largeBlock;
        },
        optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
    })
    fieldLarge: ui.fields.SelectionCard;

    @ui.decorators.block<SelectionCard>({
        parent() {
            return this.variousSizeContainer;
        },
        title: 'medium block example',
        width: 'medium',
    })
    mediumBlock: ui.containers.Block;

    @ui.decorators.selectionCard<SelectionCard>({
        title: 'SelectionCard group',
        icon() {
            return 'apple';
        },
        description() {
            return 'Add description';
        },
        isFullWidth: true,
        parent() {
            return this.mediumBlock;
        },
        optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
    })
    fieldMedium: ui.fields.SelectionCard;

    @ui.decorators.block<SelectionCard>({
        parent() {
            return this.variousSizeContainer;
        },
        title: 'small block example',
        width: 'small',
    })
    smallBlock: ui.containers.Block;

    @ui.decorators.selectionCard<SelectionCard>({
        title: 'SelectionCard group',
        icon() {
            return 'apple';
        },
        description() {
            return 'Add description';
        },
        isFullWidth: true,
        parent() {
            return this.smallBlock;
        },
        optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
    })
    fieldSmall: ui.fields.SelectionCard;

    @ui.decorators.block<SelectionCard>({
        parent() {
            return this.variousSizeContainer;
        },
        title: 'extra small block example',
        width: 'small',
    })
    extraSmallBlock: ui.containers.Block;

    @ui.decorators.selectionCard<SelectionCard>({
        title: 'SelectionCard group',
        icon() {
            return 'apple';
        },
        description() {
            return 'Add description';
        },
        isFullWidth: true,
        parent() {
            return this.extraSmallBlock;
        },
        optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
    })
    fieldExtraSmall: ui.fields.SelectionCard;

    @ui.decorators.separatorField<SelectionCard>({
        parent() {
            return this.additionalBlock;
        },
        isFullWidth: true,
    })
    fieldSeparator1: ui.fields.Separator;

    @ui.decorators.selectionCard<SelectionCard>({
        parent() {
            return this.additionalBlock;
        },
        title: 'With warning message',
        icon() {
            return 'apple';
        },
        description() {
            return 'Add description';
        },
        warningMessage: 'Wow, warning!',
        optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
    })
    warningMessageField: ui.fields.SelectionCard;

    @ui.decorators.selectionCard<SelectionCard>({
        parent() {
            return this.additionalBlock;
        },
        title: 'With warning message with callback',
        icon() {
            return 'apple';
        },
        description() {
            return 'Add description';
        },
        helperText: 'Select "awful"',
        warningMessage() {
            if (this.warningMessageWithCallbackField.value === 'awful') {
                return 'Warning message';
            }
            return null;
        },
        optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
    })
    warningMessageWithCallbackField: ui.fields.SelectionCard;

    @ui.decorators.selectionCard<SelectionCard>({
        parent() {
            return this.additionalBlock;
        },
        title: 'With info message',
        icon() {
            return 'apple';
        },
        description() {
            return 'Add description';
        },
        infoMessage: 'Wow, warning!',
        optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
    })
    infoMessageField: ui.fields.SelectionCard;

    @ui.decorators.selectionCard<SelectionCard>({
        parent() {
            return this.additionalBlock;
        },
        title: 'With info message with callback',
        icon() {
            return 'apple';
        },
        description() {
            return 'Add description';
        },
        helperText: 'Type "awful"',
        infoMessage() {
            if (this.infoMessageWithCallbackField.value === 'awful') {
                return 'Info message';
            }
            return null;
        },
        optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
    })
    infoMessageWithCallbackField: ui.fields.SelectionCard;

    @ui.decorators.selectionCard<SelectionCard>({
        parent() {
            return this.additionalBlock;
        },
        title: 'With info message',
        icon() {
            return 'apple';
        },
        description() {
            return 'Add description';
        },
        warningMessage: 'Wow, warning!',
        infoMessage: 'You should not see this',
        optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
    })
    infoAndWarningMessageField: ui.fields.SelectionCard;

    @ui.decorators.selectionCard<SelectionCard>({
        parent() {
            return this.additionalBlock;
        },
        validation() {
            if (this.infoAndWarningMessageMandatoryField.value === 'good') {
                return 'Error message';
            }
            return '';
        },
        title: 'Info, warning and validation',
        icon() {
            return 'apple';
        },
        description() {
            return 'Add description';
        },
        warningMessage: 'Wow, warning!',
        infoMessage: 'You should not see this',
        helperText: 'Not allowed to select "good"',
        optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
    })
    infoAndWarningMessageMandatoryField: ui.fields.SelectionCard;
}
