import { Graph<PERSON><PERSON>, ShowCaseInvoiceLine, ShowCaseProduct } from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import { misc } from '../menu-items/misc';

@ui.decorators.page<PodComponentStates, ShowCaseInvoiceLine>({
    authorizationCode: 'PDCMPSTATES',
    category: 'SHOWCASE',
    defaultEntry() {
        return '467';
    },
    module: 'show-case',
    node: '@sage/xtrem-show-case/ShowCaseInvoiceLine',
    title: 'Pod Component States',
    menuItem: misc,
    navigationPanel: null,
})
export class PodComponentStates extends ui.Page<GraphApi, ShowCaseInvoiceLine> {
    static getProductColumns() {
        return [
            ui.nestedFields.text<PodComponentStates, ShowCaseProduct>({
                bind: 'product',
                isFullWidth: true,
                title: 'Product',
            }),
            ui.nestedFields.textArea<PodComponentStates, ShowCaseProduct>({
                bind: 'description',
                isFullWidth: true,
                title: 'Description',
            }),
        ];
    }

    @ui.decorators.section<PodComponentStates>({
        title: 'Field Components',
    })
    podFieldSection: ui.containers.Section;

    @ui.decorators.section<PodComponentStates>({
        title: 'Block Components',
    })
    podBlockSection: ui.containers.Section;

    @ui.decorators.block<PodComponentStates>({
        parent() {
            return this.podFieldSection;
        },
        title: 'Pod Field',
    })
    podFieldBlock: ui.containers.Block;

    @ui.decorators.block<PodComponentStates>({
        parent() {
            return this.podFieldSection;
        },
        title: 'Vital Pod Field',
    })
    vitalPodFieldBlock: ui.containers.Block;

    /* --------- */
    /* POD FIELD */
    /* --------- */

    @ui.decorators.podField<PodComponentStates, ShowCaseProduct>({
        bind: 'product',
        canRemove: true,
        columns: PodComponentStates.getProductColumns(),
        isDisabled: false,
        isReadOnly: false,
        parent() {
            return this.podFieldBlock;
        },
        size: 'large',
        title: 'Enabled & Value',
    })
    enabledValuePodField: ui.fields.Pod;

    @ui.decorators.podField<PodComponentStates, ShowCaseProduct>({
        bind: 'product',
        canRemove: true,
        columns: PodComponentStates.getProductColumns(),
        isDisabled: true,
        isReadOnly: false,
        parent() {
            return this.podFieldBlock;
        },
        size: 'large',
        title: 'Disabled & Value',
    })
    disabledValuePodField: ui.fields.Pod;

    @ui.decorators.podField<PodComponentStates, ShowCaseProduct>({
        bind: 'product',
        canRemove: true,
        columns: PodComponentStates.getProductColumns(),
        isDisabled: false,
        isReadOnly: true,
        parent() {
            return this.podFieldBlock;
        },
        size: 'large',
        title: 'Readonly & Value',
    })
    readonlyValuePodField: ui.fields.Pod;

    @ui.decorators.separatorField<PodComponentStates>({
        isFullWidth: true,
        parent() {
            return this.podFieldBlock;
        },
    })
    valuePodFieldSeparator: ui.fields.Separator;

    @ui.decorators.podField<PodComponentStates, ShowCaseProduct>({
        canRemove: true,
        columns: PodComponentStates.getProductColumns(),
        isDisabled: false,
        isReadOnly: false,
        isTransient: true,
        parent() {
            return this.podFieldBlock;
        },
        size: 'large',
        title: 'Enabled & Empty',
    })
    enabledEmptyPodField: ui.fields.Pod;

    @ui.decorators.podField<PodComponentStates, ShowCaseProduct>({
        canRemove: true,
        columns: PodComponentStates.getProductColumns(),
        isDisabled: true,
        isReadOnly: false,
        isTransient: true,
        parent() {
            return this.podFieldBlock;
        },
        size: 'large',
        title: 'Disabled & Empty',
    })
    disabledEmptyPodField: ui.fields.Pod;

    @ui.decorators.podField<PodComponentStates, ShowCaseProduct>({
        canRemove: true,
        columns: PodComponentStates.getProductColumns(),
        isDisabled: false,
        isReadOnly: true,
        isTransient: true,
        parent() {
            return this.podFieldBlock;
        },
        size: 'large',
        title: 'Readonly & Empty',
    })
    readonlyEmptyPodField: ui.fields.Pod;

    @ui.decorators.podField<PodComponentStates, ShowCaseProduct>({
        addButtonText: 'Lorem Ipsum',
        canRemove: true,
        columns: PodComponentStates.getProductColumns(),
        isDisabled: false,
        isReadOnly: false,
        isTransient: true,
        parent() {
            return this.podFieldBlock;
        },
        placeholder: 'Lorem ipsum dolor sed amit',
        size: 'large',
        title: 'Custom Text',
    })
    customEmptyPodField: ui.fields.Pod;

    @ui.decorators.podField<PodComponentStates, ShowCaseProduct>({
        bind: 'product',
        canRemove: true,
        columns: PodComponentStates.getProductColumns(),
        isDisabled: false,
        isReadOnly: false,
        parent() {
            return this.podFieldBlock;
        },
        size: 'large',
        title: 'With info',
        infoMessage: 'Hi!',
    })
    enabledValuePodFieldInfo: ui.fields.Pod;

    @ui.decorators.podField<PodComponentStates, ShowCaseProduct>({
        bind: 'product',
        canRemove: true,
        columns: PodComponentStates.getProductColumns(),
        isDisabled: false,
        isReadOnly: false,
        parent() {
            return this.podFieldBlock;
        },
        size: 'large',
        title: 'With warning',
        warningMessage: 'Hi!',
    })
    enabledValuePodFieldWarning: ui.fields.Pod;

    /* --------------- */
    /* VITAL POD FIELD */
    /* --------------- */

    @ui.decorators.vitalPodField<PodComponentStates, ShowCaseProduct>({
        bind: 'product',
        canRemove: true,
        columns: PodComponentStates.getProductColumns(),
        isDisabled: false,
        isReadOnly: false,
        parent() {
            return this.vitalPodFieldBlock;
        },
        size: 'large',
        title: 'Enabled & Value',
    })
    enabledValueVitalPodField: ui.fields.VitalPod;

    @ui.decorators.vitalPodField<PodComponentStates, ShowCaseProduct>({
        bind: 'product',
        canRemove: true,
        columns: PodComponentStates.getProductColumns(),
        isDisabled: true,
        isReadOnly: false,
        parent() {
            return this.vitalPodFieldBlock;
        },
        size: 'large',
        title: 'Disabled & Value',
    })
    disabledValueVitalPodField: ui.fields.VitalPod;

    @ui.decorators.vitalPodField<PodComponentStates, ShowCaseProduct>({
        bind: 'product',
        canRemove: true,
        columns: PodComponentStates.getProductColumns(),
        isDisabled: false,
        isReadOnly: true,
        parent() {
            return this.vitalPodFieldBlock;
        },
        size: 'large',
        title: 'Readonly & Value',
    })
    readonlyValueVitalPodField: ui.fields.VitalPod;

    @ui.decorators.separatorField<PodComponentStates>({
        isFullWidth: true,
        parent() {
            return this.vitalPodFieldBlock;
        },
    })
    valueVitalPodFieldSeparator: ui.fields.Separator;

    @ui.decorators.vitalPodField<PodComponentStates, ShowCaseProduct>({
        canRemove: true,
        columns: PodComponentStates.getProductColumns(),
        isDisabled: false,
        isReadOnly: false,
        isTransient: true,
        parent() {
            return this.vitalPodFieldBlock;
        },
        size: 'large',
        title: 'Enabled & Empty',
    })
    enabledEmptyVitalPodField: ui.fields.VitalPod;

    @ui.decorators.vitalPodField<PodComponentStates, ShowCaseProduct>({
        canRemove: true,
        columns: PodComponentStates.getProductColumns(),
        isDisabled: true,
        isReadOnly: false,
        isTransient: true,
        parent() {
            return this.vitalPodFieldBlock;
        },
        size: 'large',
        title: 'Disabled & Empty',
    })
    disabledEmptyVitalPodField: ui.fields.VitalPod;

    @ui.decorators.vitalPodField<PodComponentStates, ShowCaseProduct>({
        canRemove: true,
        columns: PodComponentStates.getProductColumns(),
        isDisabled: false,
        isReadOnly: true,
        isTransient: true,
        parent() {
            return this.vitalPodFieldBlock;
        },
        size: 'large',
        title: 'Readonly & Empty',
    })
    readonlyEmptyVitalPodField: ui.fields.VitalPod;

    @ui.decorators.vitalPodField<PodComponentStates, ShowCaseProduct>({
        addButtonText: 'Lorem Ipsum',
        canRemove: true,
        columns: PodComponentStates.getProductColumns(),
        isDisabled: false,
        isReadOnly: false,
        isTransient: true,
        parent() {
            return this.vitalPodFieldBlock;
        },
        placeholder: 'Lorem ipsum dolor sed amit',
        size: 'large',
        title: 'Custom Text',
    })
    customEmptyVitalPodField: ui.fields.VitalPod;

    @ui.decorators.vitalPodField<PodComponentStates, ShowCaseProduct>({
        bind: 'product',
        canRemove: true,
        columns: PodComponentStates.getProductColumns(),
        isDisabled: false,
        isReadOnly: false,
        parent() {
            return this.vitalPodFieldBlock;
        },
        size: 'large',
        title: 'With info',
        infoMessage: 'Hi!',
    })
    enabledValueVitalPodFieldInfo: ui.fields.VitalPod;

    @ui.decorators.vitalPodField<PodComponentStates, ShowCaseProduct>({
        bind: 'product',
        canRemove: true,
        columns: PodComponentStates.getProductColumns(),
        isDisabled: false,
        isReadOnly: false,
        parent() {
            return this.vitalPodFieldBlock;
        },
        size: 'large',
        title: 'With warning',
        warningMessage: 'Hi!',
    })
    enabledValueVitalPodFieldWarning: ui.fields.VitalPod;

    /* --------- */
    /* POD BLOCK */
    /* --------- */

    @ui.decorators.podField<PodComponentStates, ShowCaseProduct>({
        bind: 'product',
        canRemove: true,
        columns: PodComponentStates.getProductColumns(),
        isDisabled: false,
        isReadOnly: false,
        parent() {
            return this.podBlockSection;
        },
        size: 'large',
        title: 'Enabled & Value',
    })
    enabledValuePodBlock: ui.fields.Pod;

    @ui.decorators.podField<PodComponentStates, ShowCaseProduct>({
        bind: 'product',
        canRemove: true,
        columns: PodComponentStates.getProductColumns(),
        isDisabled: true,
        isReadOnly: false,
        parent() {
            return this.podBlockSection;
        },
        size: 'large',
        title: 'Disabled & Value',
    })
    disabledValuePodBlock: ui.fields.Pod;

    @ui.decorators.podField<PodComponentStates, ShowCaseProduct>({
        bind: 'product',
        canRemove: true,
        columns: PodComponentStates.getProductColumns(),
        isDisabled: false,
        isReadOnly: true,
        parent() {
            return this.podBlockSection;
        },
        size: 'large',
        title: 'Readonly & Value',
    })
    readonlyValuePodBlock: ui.fields.Pod;

    @ui.decorators.podField<PodComponentStates, ShowCaseProduct>({
        canRemove: true,
        columns: PodComponentStates.getProductColumns(),
        isDisabled: false,
        isReadOnly: false,
        isTransient: true,
        parent() {
            return this.podBlockSection;
        },
        size: 'large',
        title: 'Enabled & Empty',
    })
    enabledEmptyPodBlock: ui.fields.Pod;

    @ui.decorators.podField<PodComponentStates, ShowCaseProduct>({
        canRemove: true,
        columns: PodComponentStates.getProductColumns(),
        isDisabled: true,
        isReadOnly: false,
        isTransient: true,
        parent() {
            return this.podBlockSection;
        },
        size: 'large',
        title: 'Disabled & Empty',
    })
    disabledEmptyPodBlock: ui.fields.Pod;

    @ui.decorators.podField<PodComponentStates, ShowCaseProduct>({
        columns: PodComponentStates.getProductColumns(),
        canRemove: true,
        isDisabled: false,
        isReadOnly: true,
        isTransient: true,
        parent() {
            return this.podBlockSection;
        },
        size: 'large',
        title: 'Readonly & Empty',
    })
    readonlyEmptyPodBlock: ui.fields.Pod;

    @ui.decorators.podField<PodComponentStates, ShowCaseProduct>({
        addButtonText: 'Lorem Ipsum',
        canRemove: true,
        columns: PodComponentStates.getProductColumns(),
        isDisabled: false,
        isReadOnly: false,
        isTransient: true,
        parent() {
            return this.podBlockSection;
        },
        placeholder: 'Lorem ipsum dolor sed amit',
        size: 'large',
        title: 'Custom Text',
    })
    customEmptyPodBlock: ui.fields.Pod;

    @ui.decorators.podField<PodComponentStates, ShowCaseProduct>({
        bind: 'product',
        canRemove: true,
        columns: PodComponentStates.getProductColumns(),
        isDisabled: false,
        isReadOnly: false,
        parent() {
            return this.vitalPodFieldBlock;
        },
        size: 'large',
        title: 'With info',
        infoMessage: 'Hi there!',
    })
    enabledValuePodBlockInfo: ui.fields.Pod;

    @ui.decorators.podField<PodComponentStates, ShowCaseProduct>({
        bind: 'product',
        canRemove: true,
        columns: PodComponentStates.getProductColumns(),
        isDisabled: false,
        isReadOnly: false,
        parent() {
            return this.vitalPodFieldBlock;
        },
        size: 'large',
        title: 'With warning',
        warningMessage: 'Hi there!',
    })
    enabledValuePodBlockWarning: ui.fields.Pod;

    /* --------------- */
    /* VITAL POD BLOCK */
    /* --------------- */

    @ui.decorators.vitalPodField<PodComponentStates, ShowCaseProduct>({
        bind: 'product',
        canRemove: true,
        columns: PodComponentStates.getProductColumns(),
        isDisabled: false,
        isReadOnly: false,
        parent() {
            return this.podBlockSection;
        },
        size: 'large',
        title: 'Enabled & Value',
    })
    enabledValueVitalPodBlock: ui.fields.VitalPod;

    @ui.decorators.vitalPodField<PodComponentStates, ShowCaseProduct>({
        bind: 'product',
        canRemove: true,
        columns: PodComponentStates.getProductColumns(),
        isDisabled: true,
        isReadOnly: false,
        parent() {
            return this.podBlockSection;
        },
        size: 'large',
        title: 'Disabled & Value',
    })
    disabledValueVitalPodBlock: ui.fields.VitalPod;

    @ui.decorators.vitalPodField<PodComponentStates, ShowCaseProduct>({
        bind: 'product',
        canRemove: true,
        columns: PodComponentStates.getProductColumns(),
        isDisabled: false,
        isReadOnly: true,
        parent() {
            return this.podBlockSection;
        },
        size: 'large',
        title: 'Readonly & Value',
    })
    readonlyValueVitalPodBlock: ui.fields.VitalPod;

    @ui.decorators.vitalPodField<PodComponentStates, ShowCaseProduct>({
        canRemove: true,
        columns: PodComponentStates.getProductColumns(),
        isDisabled: false,
        isReadOnly: false,
        isTransient: true,
        parent() {
            return this.podBlockSection;
        },
        size: 'large',
        title: 'Enabled & Empty',
    })
    enabledEmptyVitalPodBlock: ui.fields.VitalPod;

    @ui.decorators.vitalPodField<PodComponentStates, ShowCaseProduct>({
        canRemove: true,
        columns: PodComponentStates.getProductColumns(),
        isDisabled: true,
        isReadOnly: false,
        isTransient: true,
        parent() {
            return this.podBlockSection;
        },
        size: 'large',
        title: 'Disabled & Empty',
    })
    disabledEmptyVitalPodBlock: ui.fields.VitalPod;

    @ui.decorators.vitalPodField<PodComponentStates, ShowCaseProduct>({
        columns: PodComponentStates.getProductColumns(),
        canRemove: true,
        isDisabled: false,
        isReadOnly: true,
        isTransient: true,
        parent() {
            return this.podBlockSection;
        },
        size: 'large',
        title: 'Readonly & Empty',
    })
    readonlyEmptyVitalPodBlock: ui.fields.VitalPod;

    @ui.decorators.vitalPodField<PodComponentStates, ShowCaseProduct>({
        addButtonText: 'Lorem Ipsum',
        canRemove: true,
        columns: PodComponentStates.getProductColumns(),
        isDisabled: false,
        isReadOnly: false,
        isTransient: true,
        parent() {
            return this.podBlockSection;
        },
        placeholder: 'Lorem ipsum dolor sed amit',
        size: 'large',
        title: 'Custom Text',
    })
    customEmptyVitalPodBlock: ui.fields.VitalPod;

    @ui.decorators.vitalPodField<PodComponentStates, ShowCaseProduct>({
        bind: 'product',
        canRemove: true,
        columns: PodComponentStates.getProductColumns(),
        isDisabled: false,
        isReadOnly: false,
        infoMessage: 'Hi!',
        parent() {
            return this.podBlockSection;
        },
        size: 'large',
        title: 'With info',
    })
    enabledValueVitalPodBlockInfo: ui.fields.VitalPod;

    @ui.decorators.vitalPodField<PodComponentStates, ShowCaseProduct>({
        bind: 'product',
        canRemove: true,
        columns: PodComponentStates.getProductColumns(),
        isDisabled: false,
        isReadOnly: false,
        warningMessage: 'Hi!',
        parent() {
            return this.podBlockSection;
        },
        size: 'large',
        title: 'With warning',
    })
    enabledValueVitalPodBlockWarning: ui.fields.VitalPod;
}
