import {
    Graph<PERSON><PERSON>,
    ShowCaseInvoice as ShowCaseInvoiceNode,
    ShowCaseInvoiceLine,
    ShowCaseProduct,
    ShowCaseCustomer,
    ShowCaseOrder,
} from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import { applicationPages } from '../menu-items/application-pages';
import { setOrderOfPageHeaderDropDownActions } from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';

@ui.decorators.page<StandardShowCaseInvoice, ShowCaseInvoiceNode>({
    authorizationCode: 'SHCPRVD',
    category: 'SHOWCASE',
    menuItem: applicationPages,
    objectTypeSingular: 'Invoice',
    objectTypePlural: 'Invoices',
    mode: 'tabs',
    hasAttachmentsSection: true,
    idField() {
        return this._id;
    },
    businessActions() {
        return [this.$standardCancelAction, this.$standardSaveAction];
    },
    headerQuickActions() {
        return [this.$standardDuplicateAction];
    },
    headerDropDownActions() {
        return setOrderOfPageHeaderDropDownActions<StandardShowCaseInvoice>({
            remove: [this.$standardDeleteAction],
            dropDownBusinessActions: [this.$standardOpenRecordHistoryAction],
        });
    },
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.text({ bind: '_id', prefix: 'INV' }),
            titleLine2: ui.nestedFields.reference<StandardShowCaseInvoice, ShowCaseInvoiceNode, ShowCaseCustomer>({
                bind: 'customer',
                node: '@sage/xtrem-show-case/ShowCaseCustomer',
                valueField: 'name',
                canFilter: true,
            }),
            line2: ui.nestedFields.date({ bind: 'purchaseDate' }),
            titleRight: ui.nestedFields.count({ bind: 'lines', postfix: 'it' }),
        },
    },
    node: '@sage/xtrem-show-case/ShowCaseInvoice',
    title: 'Standard invoice page',
    priority: 300,
    headerSection() {
        return this.headerSection;
    },
})
export class StandardShowCaseInvoice extends ui.Page<GraphApi> {
    @ui.decorators.section<StandardShowCaseInvoice>({
        title: 'Header',
        isTitleHidden: true,
    })
    headerSection: ui.containers.Section;

    @ui.decorators.block<StandardShowCaseInvoice>({
        parent() {
            return this.headerSection;
        },
    })
    headerBlock: ui.containers.Block;

    @ui.decorators.textField<StandardShowCaseInvoice>({
        parent() {
            return this.headerBlock;
        },
        title: 'Invoice Number',
        isReadOnly: true,
        prefix: 'INV',
    })
    _id: ui.fields.Text;

    @ui.decorators.dateField<StandardShowCaseInvoice>({
        parent() {
            return this.headerBlock;
        },
        title: 'Purchase Date',
    })
    purchaseDate: ui.fields.Date;

    @ui.decorators.numericField<StandardShowCaseInvoice>({
        parent() {
            return this.headerBlock;
        },
        title: 'Total quantity',
        isReadOnly: true,
    })
    totalProductQty: ui.fields.Numeric;

    @ui.decorators.referenceField<StandardShowCaseInvoice, ShowCaseOrder>({
        parent() {
            return this.headerBlock;
        },
        fetchesDefaults: true,
        title: 'Order',
        node: '@sage/xtrem-show-case/ShowCaseOrder',
        valueField: { customer: { name: true } },
        columns: [
            ui.nestedFields.text({ bind: '_id', title: 'ID', canFilter: false }),
            ui.nestedFields.reference<StandardShowCaseInvoice, ShowCaseOrder, ShowCaseCustomer>({
                bind: 'customer',
                node: '@sage/xtrem-show-case/ShowCaseCustomer',
                title: 'Customer',
                valueField: 'name',
            }),
        ],
    })
    order: ui.fields.Reference;

    @ui.decorators.section<StandardShowCaseInvoice>({
        title: 'Lines',
        isTitleHidden: true,
    })
    linesSection: ui.containers.Section;

    @ui.decorators.tableField<StandardShowCaseInvoice, ShowCaseInvoiceLine>({
        node: '@sage/xtrem-show-case/ShowCaseInvoiceLine',
        parent() {
            return this.linesSection;
        },
        title: 'Lines',
        fetchesDefaults: true,
        canAddNewLine: true,
        isPhantomRowDisabled: false,
        columns: [
            ui.nestedFields.reference<StandardShowCaseInvoice, ShowCaseInvoiceLine, ShowCaseProduct>({
                bind: 'product',
                title: 'Product',
                isAutoSelectEnabled: true,
                node: '@sage/xtrem-show-case/ShowCaseProduct',
                valueField: { product: true },
                helperTextField: { _id: true },
                fetchesDefaults: true,
            }),
            ui.nestedFields.numeric({
                bind: 'orderQuantity',
                title: 'Ordered Quantity',
                scale: 0,
                fetchesDefaults: true,
            }),
            ui.nestedFields.numeric({ bind: 'netPrice', title: 'Net Price', scale: 2, fetchesDefaults: true }),
            ui.nestedFields.select({
                bind: 'discountType',
                title: 'Discount Type',
                optionType: '@sage/xtrem-show-case/ShowCaseDiscountType',
            }),
            ui.nestedFields.numeric({ bind: '_sortValue', isHidden: true }),
            ui.nestedFields.text({ bind: 'comments', title: 'Comment' }),
        ],
        dropdownActions: [
            {
                icon: 'bin',
                title: 'Remove',
                isDestructive: true,
                async onClick(rowId: any) {
                    this.lines.removeRecord(rowId);
                },
            },
        ],
    })
    lines: ui.fields.Table;

    @ui.decorators.section<StandardShowCaseInvoice>({
        title: 'Notes',
        isTitleHidden: true,
    })
    notesSection: ui.containers.Section;

    @ui.decorators.richTextField<StandardShowCaseInvoice>({
        parent() {
            return this.notesSection;
        },
        title: 'Notes',
        isTitleHidden: true,
    })
    notes: ui.fields.RichText;
}
