import * as ui from '@sage/xtrem-ui';

@ui.decorators.page<NumberOfUsersSettings>({
    title: 'Number of users settings',
    isTransient: true,
    onLoad() {
        this.shouldExcludeAdministrativeUsers.value = !!this.$.queryParameters.shouldExcludeAdministrativeUsers;
        this.shouldExcludeInactiveUsers.value = !!this.$.queryParameters.shouldExcludeInactiveUsers;
    },
    businessActions() {
        return [this.save];
    },
})
export class NumberOfUsersSettings extends ui.Page {
    @ui.decorators.section<NumberOfUsersSettings>({
        isTitleHidden: true,
    })
    section: ui.containers.Section;

    @ui.decorators.block<NumberOfUsersSettings>({
        parent() {
            return this.section;
        },
    })
    fieldBlock: ui.containers.Block;

    @ui.decorators.switchField<NumberOfUsersSettings>({
        parent() {
            return this.fieldBlock;
        },
        isFullWidth: true,
        title: 'Administrative users excluded',
    })
    shouldExcludeAdministrativeUsers: ui.fields.Switch;

    @ui.decorators.switchField<NumberOfUsersSettings>({
        parent() {
            return this.fieldBlock;
        },
        isFullWidth: true,
        title: 'Inactive users excluded',
    })
    shouldExcludeInactiveUsers: ui.fields.Switch;

    @ui.decorators.pageAction<NumberOfUsersSettings>({
        title: 'Save Settings',
        onClick() {
            this.$.finish({
                shouldExcludeAdministrativeUsers: !!this.shouldExcludeAdministrativeUsers.value,
                shouldExcludeInactiveUsers: !!this.shouldExcludeInactiveUsers.value,
            });
        },
    })
    save: ui.PageAction;
}
