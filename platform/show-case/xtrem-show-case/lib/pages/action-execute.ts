import * as ui from '@sage/xtrem-ui';
import { fields } from '../menu-items/fields';

@ui.decorators.page<ActionExecute>({
    authorizationCode: 'ACTINBTNS',
    module: 'show-case',
    category: 'SHOWCASE',
    title: 'Page - Action Buttons Execution',
    isTransient: true,
    onError(error) {
        return `Error catched by screen element: ${error.message}`;
    },
    businessActions() {
        return [this.businessActionWithOnError, this.businessActionWithoutOnError];
    },
    onLoad() {},
    menuItem: fields,
})
export class ActionExecute extends ui.Page {
    @ui.decorators.section<ActionExecute>({})
    mainSection: ui.containers.Section;

    @ui.decorators.block<ActionExecute>({
        parent() {
            return this.mainSection;
        },
    })
    mainBlock: ui.containers.Block;

    @ui.decorators.buttonField<ActionExecute>({
        title: "Execute 'Business action with onError': ",
        map() {
            return ui.localize('@sage/xtrem-show-case/with-default-error-handlers', ' With default error handlers');
        },
        parent() {
            return this.mainBlock;
        },
        onClick() {
            this.businessActionWithOnError.execute(true);
        },
    })
    button1: ui.fields.Button;

    @ui.decorators.buttonField<ActionExecute>({
        title: '',
        map() {
            return ui.localize('@sage/xtrem-show-case/with-custom-error-handler', 'With custom error handler');
        },
        parent() {
            return this.mainBlock;
        },
        async onClick() {
            try {
                await this.businessActionWithOnError.execute();
            } catch (e) {
                this.$.showToast(`Error catched by caller: ${e.message}`, { type: 'error' });
            }
        },
    })
    button2: ui.fields.Button;

    @ui.decorators.pageAction<ActionExecute>({
        title: 'Business action with onError',
        onError(error) {
            return `Error catched by action element: ${error.message}`;
        },
        onClick() {
            throw Error('ACTION ERROR');
        },
    })
    businessActionWithOnError: ui.PageAction;

    @ui.decorators.pageAction<ActionExecute>({
        title: 'Business action without onError',
        onClick() {
            throw Error('ACTION ERROR');
        },
    })
    businessActionWithoutOnError: ui.PageAction;
}
