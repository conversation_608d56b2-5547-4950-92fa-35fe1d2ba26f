import { <PERSON><PERSON>h<PERSON><PERSON>, Show<PERSON>aseProvider, ShowCaseProduct } from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import { pod } from '../menu-items/pod';

@ui.decorators.page<PodField, ShowCaseProduct>({
    authorizationCode: 'SHCPRVD',
    category: 'SHOWCASE',
    menuItem: pod,
    businessActions() {
        return [this.toggleNavigationPanel, this.update];
    },
    createAction() {
        return this.create;
    },
    headerDropDownActions() {
        return [this.delete];
    },
    module: 'show-case',
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.text({ bind: 'product' }),
            titleRight: ui.nestedFields.text({ bind: '_id' }),
            line2: ui.nestedFields.text({ bind: 'description', canFilter: false }),
        },
    },
    node: '@sage/xtrem-show-case/ShowCaseProduct',
    title: 'Pod as a field',
})
export class <PERSON>d<PERSON>ield extends ui.Page<GraphApi> {
    @ui.decorators.section<PodField>({})
    mainSection: ui.containers.Section;

    @ui.decorators.block<PodField>({
        parent() {
            return this.mainSection;
        },
    })
    block: ui.containers.Block;

    @ui.decorators.textField<PodField>({
        parent() {
            return this.block;
        },
        title: 'Id',
        width: 'medium',
    })
    _id: ui.fields.Text;

    @ui.decorators.podField<PodField, ShowCaseProvider>({
        title: 'Provider',
        width: 'large',
        lookupDialogTitle: 'Add item',
        parent() {
            return this.block;
        },
        onClick() {
            this.clickTriggered.isHidden = false;
            setTimeout(() => {
                this.clickTriggered.isHidden = true;
            }, 5000);
        },
        onChange() {
            this.changeTriggered.isHidden = false;
            setTimeout(() => {
                this.changeTriggered.isHidden = true;
            }, 5000);
        },
        onError() {
            ui.console.error('just testing onError works.');
        },
        columns: [
            ui.nestedFields.text({ bind: 'textField', title: 'Text field' }),
            ui.nestedFields.numeric({ bind: 'integerField', title: 'Numeric field' }),
            ui.nestedFields.text({ bind: '_id', title: 'id' }),
            ui.nestedFields.date({ bind: 'dateField', title: 'date field' }),
        ],
        node: '@sage/xtrem-show-case/ShowCaseProvider',
    })
    provider: ui.fields.Pod;

    @ui.decorators.pageAction<PodField>({
        title: 'Custom create title',
        icon: 'add',
        async onClick() {
            await this.$.graph.create();
            this.$.dialog.message('info', 'Mutation Create', `Created entry: ${this._id.value}`, {
                fullScreen: false,
                rightAligned: false,
                acceptButton: {
                    isDisabled: false,
                    isHidden: false,
                    text: 'OK',
                },
            });
        },
    })
    create: ui.PageAction;

    @ui.decorators.pageAction<PodField>({
        title: 'Save',
        async onClick() {
            await this.$.graph.update();
            this.$.dialog.message('info', 'Mutation Update', `Updated entry: ${this._id.value}`, {
                fullScreen: false,
                rightAligned: false,
                acceptButton: {
                    isDisabled: false,
                    isHidden: false,
                    text: 'OK',
                },
            });
        },
    })
    update: ui.PageAction;

    @ui.decorators.pageAction<PodField>({
        title: 'Delete',
        icon: 'bin',
        isDestructive: true,
        async onClick() {
            await this.$.graph.delete();
            this.$.router.goTo('@sage/xtrem-show-case/BoundPage');
        },
    })
    delete: ui.PageAction;

    @ui.decorators.pageAction<PodField>({
        title: 'Toggle Navigation Panel',
        async onClick() {
            this.$.isNavigationPanelHidden = !this.$.isNavigationPanelHidden;
        },
    })
    toggleNavigationPanel: ui.PageAction;

    /** Testing field buttons */

    @ui.decorators.labelField<PodField>({
        parent() {
            return this.block;
        },
        isTransient: true,
        isHidden: true,
        map() {
            return 'Change was triggered';
        },
    })
    changeTriggered: ui.fields.Label;

    @ui.decorators.labelField<PodField>({
        parent() {
            return this.block;
        },
        isTransient: true,
        isHidden: true,
        map() {
            return 'Click was triggered';
        },
    })
    clickTriggered: ui.fields.Label;

    @ui.decorators.labelField<PodField>({
        parent() {
            return this.block;
        },
        isTransient: true,
        isHidden: true,
        map() {
            return 'Add Click was triggered';
        },
    })
    addClickTriggered: ui.fields.Label;

    @ui.decorators.section<PodField>({
        isTitleHidden: true,
    })
    configurationSection: ui.containers.Section;

    @ui.decorators.block<PodField>({
        parent() {
            return this.configurationSection;
        },
        title: 'Configuration',
        isTransient: true,
    })
    configurationBlock: ui.containers.Block;

    @ui.decorators.textField<PodField>({
        parent() {
            return this.configurationBlock;
        },
        isTransient: true,
        title: 'Helper text',
        onChange() {
            this.provider.helperText = this.helperText.value;
        },
    })
    helperText: ui.fields.Text;

    @ui.decorators.checkboxField<PodField>({
        parent() {
            return this.configurationBlock;
        },
        isTransient: true,
        title: 'Is disabled',
        onChange() {
            this.provider.isDisabled = this.isDisabled.value;
        },
    })
    isDisabled: ui.fields.Checkbox;

    @ui.decorators.checkboxField<PodField>({
        parent() {
            return this.configurationBlock;
        },
        isTransient: true,
        title: 'Is removable',
        onChange() {
            this.provider.canRemove = this.isRemovable.value;
        },
    })
    isRemovable: ui.fields.Checkbox;

    @ui.decorators.checkboxField<PodField>({
        parent() {
            return this.configurationBlock;
        },
        isTransient: true,
        title: 'Is helper text hidden',
        onChange() {
            this.provider.isHelperTextHidden = this.isHelperTextHidden.value;
        },
    })
    isHelperTextHidden: ui.fields.Checkbox;

    @ui.decorators.checkboxField<PodField>({
        parent() {
            return this.configurationBlock;
        },
        isTransient: true,
        title: 'Is hidden',
        onChange() {
            this.provider.isHidden = this.isHidden.value;
        },
    })
    isHidden: ui.fields.Checkbox;

    @ui.decorators.checkboxField<PodField>({
        parent() {
            return this.configurationBlock;
        },
        isTransient: true,
        title: 'Is readOnly',
        onChange() {
            this.provider.isReadOnly = this.isReadOnly.value;
        },
    })
    isReadOnly: ui.fields.Checkbox;

    @ui.decorators.checkboxField<PodField>({
        parent() {
            return this.configurationBlock;
        },
        isTransient: true,
        title: 'Is title hidden',
        onChange() {
            this.provider.isTitleHidden = this.isTitleHidden.value;
        },
    })
    isTitleHidden: ui.fields.Checkbox;

    @ui.decorators.textField<PodField>({
        parent() {
            return this.configurationBlock;
        },
        isTransient: true,
        title: 'Title',
        onChange() {
            this.provider.title = this.title.value;
        },
    })
    title: ui.fields.Text;

    @ui.decorators.buttonField<PodField>({
        parent() {
            return this.configurationBlock;
        },
        isTransient: true,
        map() {
            return 'Focus field';
        },
        onClick() {
            this.provider.focus();
        },
    })
    focus: ui.fields.Button;

    @ui.decorators.buttonField<PodField>({
        parent() {
            return this.configurationBlock;
        },
        isTransient: true,
        map() {
            return 'Open Reference Lookup dialog for provider field';
        },
        onClick() {
            this.provider.openLookupDialog();
        },
    })
    openDialog: ui.fields.Button;
}
