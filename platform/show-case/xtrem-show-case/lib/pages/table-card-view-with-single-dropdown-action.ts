import { ShowCaseProvider, ShowCaseProduct } from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import { tableField } from '../menu-items/_index';

@ui.decorators.page<TableCardViewWithSingleDropdownAction, ShowCaseProvider>({
    authorizationCode: 'BSCFLDS',
    defaultEntry: () => '2',
    module: 'show-case',
    category: 'SHOWCASE',
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.text({ bind: 'textField' }),
            titleRight: ui.nestedFields.text({ bind: '_id' }),
        },
    },
    node: '@sage/xtrem-show-case/ShowCaseProvider',
    title: 'Field - Table - Card view with single dropdown action',
    menuItem: tableField,
})
export class TableCardViewWithSingleDropdownAction extends ui.Page {
    @ui.decorators.section<TableCardViewWithSingleDropdownAction>({
        isTitleHidden: true,
    })
    section: ui.containers.Section;

    @ui.decorators.section<TableCardViewWithSingleDropdownAction>({
        title: 'Edit row',
        isHidden: true,
    })
    customDialogSection: ui.containers.Section;

    @ui.decorators.gridRowBlock<TableCardViewWithSingleDropdownAction>({
        parent() {
            return this.customDialogSection;
        },
        isTitleHidden: true,
        boundTo() {
            return this.field;
        },
        fieldFilter(columnId: string) {
            return columnId !== 'tax';
        },
        readOnlyOverride(columnId: string) {
            if (columnId === 'description' || columnId === 'provider') {
                return true;
            }
            return undefined;
        },
    })
    dialogGridRowBlock: ui.containers.GridRowBlock;

    @ui.decorators.block<TableCardViewWithSingleDropdownAction>({
        parent() {
            return this.section;
        },
    })
    fieldBlock: ui.containers.Block;

    @ui.decorators.tableField<TableCardViewWithSingleDropdownAction, ShowCaseProduct>({
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        bind: 'products',
        title: 'Products',
        canExport: false,
        cardView: true,
        columns: [
            ui.nestedFields.text({
                bind: '_id',
                title: 'Id',
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                bind: 'product',
                title: 'Product',
            }),
            ui.nestedFields.text({
                bind: 'description',
                isFullWidth: true,
                title: 'Description',
            }),
            ui.nestedFields.numeric({
                bind: 'qty',
                title: 'Quantity',
                fetchesDefaults: true,
            }),
            ui.nestedFields.numeric({
                bind: 'listPrice',
                title: 'List Price',
                scale: 2,
                isHiddenDesktop: false,
                fetchesDefaults: true,
            }),
        ],
        orderBy: {
            product: 1,
        },
        parent() {
            return this.fieldBlock;
        },
        dropdownActions: [
            {
                icon: 'edit',
                title: 'Edit on sidebar',
                async onClick(rowId: any) {
                    this.dialogGridRowBlock.selectedRecordId = rowId;
                    this.customDialogSection.isHidden = false;
                    try {
                        await this.$.dialog.custom('info', this.customDialogSection, { rightAligned: true });
                    } catch {
                        // Intentionally empty.
                    }
                    this.customDialogSection.isHidden = true;
                },
            },
        ],
    })
    field: ui.fields.Table<ShowCaseProduct>;
}
