import * as ui from '@sage/xtrem-ui';
import { containers } from '../menu-items/containers';

@ui.decorators.page<Alignment>({
    module: 'show-case',
    title: 'Sections - Alignment',
    isTransient: true,
    category: 'SHOWCASE',
    businessActions() {
        return [this.toggleBlocks];
    },
    menuItem: containers,
})
export class Alignment extends ui.Page {
    @ui.decorators.pageAction<Alignment>({
        title: 'Hide some items',
        onClick() {
            this.textField42.isHidden = !this.textField42.isHidden;
            this.smallBlock1.isHidden = !this.smallBlock1.isHidden;
            this.textField13.isHidden = !this.textField13.isHidden;
            this.textField72.isHidden = !this.textField72.isHidden;
        },
    })
    toggleBlocks: ui.PageAction;

    @ui.decorators.section<Alignment>({
        title: 'Responsive blocks',
    })
    mainSection: ui.containers.Section;

    @ui.decorators.block<Alignment>({
        parent() {
            return this.mainSection;
        },
        title: 'Extra-large Block',
        width: 'extra-large',
    })
    extraLargeBlock: ui.containers.Block;

    @ui.decorators.textField<Alignment>({
        parent() {
            return this.extraLargeBlock;
        },
        title: 'Small field',
        width: 'small',
    })
    textField4: ui.fields.Text;

    @ui.decorators.textField<Alignment>({
        parent() {
            return this.extraLargeBlock;
        },
        title: 'Medium field',
        width: 'medium',
    })
    textField41: ui.fields.Text;

    @ui.decorators.textField<Alignment>({
        parent() {
            return this.extraLargeBlock;
        },
        title: 'Large field',
        width: 'large',
    })
    textField42: ui.fields.Text;

    @ui.decorators.textField<Alignment>({
        parent() {
            return this.extraLargeBlock;
        },
        title: 'Standard field',
    })
    textField43: ui.fields.Text;

    @ui.decorators.textField<Alignment>({
        parent() {
            return this.extraLargeBlock;
        },
        title: 'Standard field',
    })
    textField44: ui.fields.Text;

    @ui.decorators.textField<Alignment>({
        parent() {
            return this.extraLargeBlock;
        },
        title: 'Standard field',
    })
    textField45: ui.fields.Text;

    @ui.decorators.textField<Alignment>({
        parent() {
            return this.extraLargeBlock;
        },
        title: 'Standard field',
    })
    textField46: ui.fields.Text;

    @ui.decorators.textField<Alignment>({
        parent() {
            return this.extraLargeBlock;
        },
        title: 'Standard field',
    })
    textField47: ui.fields.Text;

    @ui.decorators.textField<Alignment>({
        parent() {
            return this.extraLargeBlock;
        },
        title: 'Standard field',
    })
    textField48: ui.fields.Text;

    @ui.decorators.textField<Alignment>({
        parent() {
            return this.extraLargeBlock;
        },
        title: 'Standard field',
    })
    textField49: ui.fields.Text;

    @ui.decorators.block<Alignment>({
        parent() {
            return this.mainSection;
        },
        title: 'Small Block',
        width: 'small',
    })
    smallBlock1: ui.containers.Block;

    @ui.decorators.textField<Alignment>({
        parent() {
            return this.smallBlock1;
        },
        title: 'Standard field',
    })
    textField3: ui.fields.Text;

    @ui.decorators.textField<Alignment>({
        parent() {
            return this.smallBlock1;
        },
        title: 'Standard field',
    })
    textField32: ui.fields.Text;

    @ui.decorators.textField<Alignment>({
        parent() {
            return this.smallBlock1;
        },
        title: 'Standard field',
    })
    textField33: ui.fields.Text;

    @ui.decorators.textField<Alignment>({
        parent() {
            return this.smallBlock1;
        },
        title: 'Standard field',
    })
    textField34: ui.fields.Text;

    @ui.decorators.block<Alignment>({
        parent() {
            return this.mainSection;
        },
        title: 'Large Block',
        width: 'large',
    })
    largeBlock: ui.containers.Block;

    @ui.decorators.textField<Alignment>({
        parent() {
            return this.largeBlock;
        },
        title: 'Medium field',
        width: 'medium',
    })
    textField11: ui.fields.Text;

    @ui.decorators.textField<Alignment>({
        parent() {
            return this.largeBlock;
        },
        title: 'Small field',
        width: 'small',
    })
    textField12: ui.fields.Text;

    @ui.decorators.textField<Alignment>({
        parent() {
            return this.largeBlock;
        },
        title: 'Medium field',
        width: 'medium',
    })
    textField13: ui.fields.Text;

    @ui.decorators.textField<Alignment>({
        parent() {
            return this.largeBlock;
        },
        title: 'Large field',
        width: 'large',
    })
    textField14: ui.fields.Text;

    @ui.decorators.textField<Alignment>({
        parent() {
            return this.largeBlock;
        },
        title: 'Large field',
        width: 'large',
    })
    textField15: ui.fields.Text;

    @ui.decorators.block<Alignment>({
        parent() {
            return this.mainSection;
        },
        title: 'Medium Block 1',
        width: 'medium',
    })
    mediumBlock1: ui.containers.Block;

    @ui.decorators.textField<Alignment>({
        parent() {
            return this.mediumBlock1;
        },
        title: 'Large field',
        width: 'large',
    })
    textField2: ui.fields.Text;

    @ui.decorators.textField<Alignment>({
        parent() {
            return this.mediumBlock1;
        },
        title: 'Small field',
        width: 'small',
    })
    textField22: ui.fields.Text;

    @ui.decorators.textField<Alignment>({
        parent() {
            return this.mediumBlock1;
        },
        title: 'Medium field',
        width: 'medium',
    })
    textField23: ui.fields.Text;

    @ui.decorators.textField<Alignment>({
        parent() {
            return this.mediumBlock1;
        },
        title: 'Medium field',
        width: 'medium',
    })
    textField24: ui.fields.Text;

    @ui.decorators.block<Alignment>({
        parent() {
            return this.mainSection;
        },
        title: 'Medium Block 2',
        width: 'medium',
    })
    mediumBlock2: ui.containers.Block;

    @ui.decorators.textField<Alignment>({
        parent() {
            return this.mediumBlock2;
        },
        title: 'Large field',
        width: 'large',
    })
    textField7: ui.fields.Text;

    @ui.decorators.textField<Alignment>({
        parent() {
            return this.mediumBlock2;
        },
        title: 'Small field',
        width: 'small',
    })
    textField72: ui.fields.Text;

    @ui.decorators.textField<Alignment>({
        parent() {
            return this.mediumBlock2;
        },
        title: 'Standard field',
    })
    textField73: ui.fields.Text;

    @ui.decorators.textField<Alignment>({
        parent() {
            return this.mediumBlock2;
        },
        title: 'Standard field',
    })
    textField74: ui.fields.Text;

    @ui.decorators.textField<Alignment>({
        parent() {
            return this.mediumBlock2;
        },
        title: 'Standard field',
    })
    textField75: ui.fields.Text;

    @ui.decorators.section<Alignment>({
        title: 'Another Section',
    })
    anotherSection: ui.containers.Section;

    @ui.decorators.block<Alignment>({
        parent() {
            return this.anotherSection;
        },
        title: 'Medium Block 3',
        width: 'medium',
    })
    mediumBlock3: ui.containers.Block;

    @ui.decorators.block<Alignment>({
        parent() {
            return this.anotherSection;
        },
        title: 'Medium Block 4',
        width: 'medium',
    })
    mediumBlock4: ui.containers.Block;
}
