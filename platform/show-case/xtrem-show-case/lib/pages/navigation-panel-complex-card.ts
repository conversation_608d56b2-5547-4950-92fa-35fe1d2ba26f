import { Graph<PERSON><PERSON>, ShowCaseProduct, ShowCaseProvider } from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import { navigationPanel } from '../menu-items/navigation-panel';

@ui.decorators.page<NavigationPanelComplexCard, ShowCaseProduct>({
    authorizationCode: 'NAVPANEL',
    module: 'show-case',
    title: 'Navigation Panel - Complex Card',
    subtitle: 'Navigation panel examples',
    node: '@sage/xtrem-show-case/ShowCaseProduct',
    category: 'SHOWCASE',
    businessActions() {
        return [this.previousItem, this.nextItem];
    },
    navigationPanel: {
        isFirstLetterSeparatorHidden: true,
        listItem: {
            title: ui.nestedFields.text({ bind: 'product' }),
            titleRight: ui.nestedFields.label({
                bind: 'category',
                optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
            }),
            line2: ui.nestedFields.reference({
                bind: 'provider',
                valueField: 'textField',
                node: '@sage/xtrem-show-case/ShowCaseProvider',
            }),
            line3: ui.nestedFields.text({ bind: 'releaseDate' }),
            listPrice: ui.nestedFields.technical({ bind: 'listPrice' }),
        },
        isAutoSelectEnabled: true,
        menuType: 'toggle',
        bulkActions: [
            {
                mutation: 'export',
                title: 'Export',
                buttonType: 'secondary',
                icon: 'file_excel',
                isDestructive: false,
                id: 'export',
            },
        ],
        inlineActions: [
            {
                title: 'Change name to banana',
                refreshesMainList: 'record',
                async onClick(recordId) {
                    await this.$.graph.update({ values: { _id: recordId, product: 'Banana' } });
                },
                icon: 'favourite',
            },
            {
                title: 'Add an X in front of product name',
                refreshesMainList: 'list',
                async onClick(recordId, record) {
                    await this.$.graph.update({ values: { _id: recordId, product: `X ${record.product}` } });
                },
                icon: 'delete',
                isDestructive: true,
            },
            {
                icon: 'locked',
                title: 'Access controlled',
                access: {
                    node: '@sage/xtrem-show-case/ShowCaseProduct',
                    bind: 'discount',
                },
                onClick() {
                    this.$.dialog.message('info', 'Access controlled action', `This is action is now available!`);
                },
            },
        ],
        dropdownActions: [
            {
                title: 'Just display a notification',
                async onClick(_recordId, record) {
                    this.$.showToast(`This is ${record.product}`);
                },
                icon: 'info',
            },
            {
                title: 'Change name to banana',
                refreshesMainList: 'record',
                async onClick(recordId) {
                    await this.$.graph.update({ values: { _id: recordId, product: 'Banana' } });
                },
                icon: 'favourite',
            },
            ui.menuSeparator(),
            {
                title: 'Add an X in front of product name',
                refreshesMainList: 'list',
                async onClick(recordId, record) {
                    await this.$.graph.update({ values: { _id: recordId, product: `X ${record.product}` } });
                },
                icon: 'delete',
                isDestructive: true,
            },
            {
                title: 'An action referring to a technical field',
                onClick(_recordId, record) {
                    this.$.showToast(`This product costs ${record.listPrice}`);
                },
                icon: 'money_bag',
            },
            {
                icon: 'locked',
                title: 'Access controlled',
                access: {
                    node: '@sage/xtrem-show-case/ShowCaseProduct',
                    bind: 'discount',
                },
                onClick() {
                    this.$.dialog.message('info', 'Access controlled action', `This is action is now available!`);
                },
            },
        ],
        optionsMenu: [
            {
                title: 'All',
                icon: 'shop',
                graphQLFilter: {},
            },
            {
                title: 'Only from Ali',
                graphQLFilter: { provider: { textField: { _eq: 'Ali Express' } } },
            },
        ],
    },
    async onLoad() {
        this.hasPreviousRecord.value = await this.$.router.hasPreviousRecord();
        this.hasNextRecord.value = await this.$.router.hasNextRecord();
    },
    menuItem: navigationPanel,
})
export class NavigationPanelComplexCard extends ui.Page<GraphApi> {
    @ui.decorators.section<NavigationPanelComplexCard>({})
    section: ui.containers.Section;

    @ui.decorators.block<NavigationPanelComplexCard>({
        parent() {
            return this.section;
        },
        title: 'Product data',
    })
    block: ui.containers.Block;

    @ui.decorators.textField<NavigationPanelComplexCard>({
        parent() {
            return this.block;
        },
        title: 'Id',
        isReadOnly: true,
    })
    _id: ui.fields.Text;

    @ui.decorators.textField<NavigationPanelComplexCard>({
        parent() {
            return this.block;
        },
        title: 'Product',
        isReadOnly: true,
    })
    product: ui.fields.Text;

    @ui.decorators.checkboxField<NavigationPanelComplexCard>({
        parent() {
            return this.block;
        },
        title: 'Has Previous Record?',
        isTransient: true,
        isDisabled: true,
    })
    hasPreviousRecord: ui.fields.Checkbox;

    @ui.decorators.checkboxField<NavigationPanelComplexCard>({
        parent() {
            return this.block;
        },
        title: 'Has Next Record?',
        isTransient: true,
        isDisabled: true,
    })
    hasNextRecord: ui.fields.Checkbox;

    @ui.decorators.pageAction({
        title: 'Next Item',
        onClick() {
            this.$.router.nextRecord();
        },
    })
    nextItem: ui.PageAction;

    @ui.decorators.pageAction({
        title: 'Previous Item',
        onClick() {
            this.$.router.previousRecord();
        },
    })
    previousItem: ui.PageAction;

    @ui.decorators.referenceField<NavigationPanelComplexCard, ShowCaseProvider>({
        parent() {
            return this.block;
        },
        columns: [ui.nestedFields.text({ bind: 'textField' })],
        node: '@sage/xtrem-show-case/ShowCaseProvider',
        title: 'Provider',
        isReadOnly: true,
        valueField: 'textField',
        helperTextField: '_id',
        minLookupCharacters: 0,
    })
    provider: ui.fields.Reference;
}
