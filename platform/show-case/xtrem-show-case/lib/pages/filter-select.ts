import { ShowCaseProduct } from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import { fields } from '../menu-items/fields';

@ui.decorators.page<FilterSelect>({
    authorizationCode: 'FLTRSLCT',
    module: 'show-case',
    title: 'Field - Filter Select',
    isTransient: true,
    category: 'SHOWCASE',
    onLoad() {
        this.isNewEnabled.value = this.field.isNewEnabled;
        this.min3CharsLookupExample.value = 'NO VALUE';
    },
    menuItem: fields,
})
export class FilterSelect extends ui.Page {
    @ui.decorators.section<FilterSelect>({
        title: 'Filter Select Field',
    })
    section: ui.containers.Section;

    @ui.decorators.block<FilterSelect>({
        title: 'Dynamic Field Example',
        parent() {
            return this.section;
        },
    })
    block: ui.containers.Block;

    @ui.decorators.filterSelectField<FilterSelect, ShowCaseProduct>({
        isFullWidth: true,
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        title: 'Filter Select Field',
        valueField: 'product',
        minLookupCharacters: 0,
        lookupDialogTitle: 'Select product',
        onChange() {
            this.value.value = this.field.value;
            this.changeLabel.isHidden = false;
            setTimeout(() => {
                this.changeLabel.isHidden = true;
            }, 2000);
        },
        parent() {
            return this.block;
        },
    })
    field: ui.fields.FilterSelect;

    @ui.decorators.labelField<FilterSelect>({
        isHidden: true,
        map() {
            return 'Change was triggered';
        },
        parent() {
            return this.block;
        },
    })
    changeLabel: ui.fields.Label;

    @ui.decorators.block<FilterSelect>({
        title: 'Field Configuration',
        parent() {
            return this.section;
        },
    })
    configuration: ui.containers.Block;

    @ui.decorators.textField<FilterSelect>({
        title: 'Helper Text',
        onChange() {
            this.field.helperText = this.helperText.value;
        },
        parent() {
            return this.configuration;
        },
    })
    helperText: ui.fields.Text;

    @ui.decorators.selectField<FilterSelect>({
        options: ['alert', 'bin', 'camera', 'edit', 'info', 'in_progress', 'question', 'scan', 'search'],
        title: 'Icon',
        onChange() {
            this.field.icon = this.icon.value as any;
        },
        parent() {
            return this.configuration;
        },
    })
    icon: ui.fields.Select;

    @ui.decorators.checkboxField<FilterSelect>({
        title: 'Is Disabled',
        onChange() {
            this.field.isDisabled = this.isDisabled.value;
        },
        parent() {
            return this.configuration;
        },
    })
    isDisabled: ui.fields.Checkbox;

    @ui.decorators.checkboxField<FilterSelect>({
        title: 'Is Helper Text Hidden',
        onChange() {
            this.field.isHelperTextHidden = this.isHelperTextHidden.value;
        },
        parent() {
            return this.configuration;
        },
    })
    isHelperTextHidden: ui.fields.Checkbox;

    @ui.decorators.checkboxField<FilterSelect>({
        title: 'Is Hidden',
        onChange() {
            this.field.isHidden = this.isHidden.value;
        },
        parent() {
            return this.configuration;
        },
    })
    isHidden: ui.fields.Checkbox;

    @ui.decorators.checkboxField<FilterSelect>({
        title: 'Is Mandatory',
        onChange() {
            this.field.isMandatory = this.isMandatory.value;
        },
        parent() {
            return this.configuration;
        },
    })
    isMandatory: ui.fields.Checkbox;

    @ui.decorators.checkboxField<FilterSelect>({
        title: 'Is Read-Only',
        onChange() {
            this.field.isReadOnly = this.isReadOnly.value;
        },
        parent() {
            return this.configuration;
        },
    })
    isReadOnly: ui.fields.Checkbox;

    @ui.decorators.checkboxField<FilterSelect>({
        title: 'Is Title Hidden',
        onChange() {
            this.field.isTitleHidden = this.isTitleHidden.value;
        },
        parent() {
            return this.configuration;
        },
    })
    isTitleHidden: ui.fields.Checkbox;

    @ui.decorators.checkboxField<FilterSelect>({
        title: 'Is New Enabled',
        onChange() {
            this.field.isNewEnabled = this.isNewEnabled.value;
        },
        parent() {
            return this.configuration;
        },
    })
    isNewEnabled: ui.fields.Checkbox;

    @ui.decorators.numericField<FilterSelect>({
        title: 'Maximum Length',
        onChange() {
            this.field.maxLength = this.maxLength.value;
        },
        parent() {
            return this.configuration;
        },
    })
    maxLength: ui.fields.Numeric;

    @ui.decorators.numericField<FilterSelect>({
        title: 'Minimum Length',
        onChange() {
            this.field.minLength = this.minLength.value;
        },
        parent() {
            return this.configuration;
        },
    })
    minLength: ui.fields.Numeric;

    @ui.decorators.textField<FilterSelect>({
        title: 'Placeholder',
        onChange() {
            this.field.placeholder = this.placeholder.value;
        },
        parent() {
            return this.configuration;
        },
    })
    placeholder: ui.fields.Text;

    @ui.decorators.textField<FilterSelect>({
        title: 'Title',
        onChange() {
            this.field.title = this.title.value;
        },
        parent() {
            return this.configuration;
        },
    })
    title: ui.fields.Text;

    @ui.decorators.textField<FilterSelect>({
        title: 'Value',
        onChange() {
            if (this.field.value !== this.value.value) {
                this.field.value = this.value.value;
            }
        },
        parent() {
            return this.configuration;
        },
    })
    value: ui.fields.Text;

    @ui.decorators.buttonField<FilterSelect>({
        map() {
            return 'Focus Field';
        },
        onClick() {
            this.field.focus();
        },
        parent() {
            return this.configuration;
        },
    })
    focus: ui.fields.Button;

    @ui.decorators.buttonField<FilterSelect>({
        map() {
            return 'Validate Field';
        },
        async onClick() {
            const result = await this.field.validateWithDetails();
            if (result && result.length > 0) {
                this.$.showToast(result[0].message, { type: 'warning' });
            } else {
                this.$.showToast('Field is valid', { type: 'success' });
            }
        },
        parent() {
            return this.configuration;
        },
    })
    validateField: ui.fields.Button;

    @ui.decorators.block<FilterSelect>({
        title: 'Field Sizes',
        parent() {
            return this.section;
        },
    })
    sizes: ui.containers.Block;

    @ui.decorators.filterSelectField<FilterSelect, ShowCaseProduct>({
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        title: 'Small',
        size: 'small',
        valueField: 'product',
        parent() {
            return this.sizes;
        },
    })
    small: ui.fields.FilterSelect;

    @ui.decorators.filterSelectField<FilterSelect, ShowCaseProduct>({
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        title: 'Medium',
        size: 'medium',
        valueField: 'product',
        parent() {
            return this.sizes;
        },
    })
    medium: ui.fields.FilterSelect;

    @ui.decorators.filterSelectField<FilterSelect, ShowCaseProduct>({
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        title: 'Large',
        size: 'large',
        valueField: 'product',
        parent() {
            return this.sizes;
        },
    })
    large: ui.fields.FilterSelect;

    @ui.decorators.block<FilterSelect>({
        title: 'Additional examples',
        parent() {
            return this.section;
        },
    })
    additionalExamplesBlock: ui.containers.Block;

    @ui.decorators.buttonField<FilterSelect>({
        isTransient: true,
        map() {
            return 'Set Existing Value';
        },
        parent() {
            return this.additionalExamplesBlock;
        },
        onClick() {
            this.field.focus();
            this.field.value = 'Spinach - Baby';
        },
    })
    setExistingValue: ui.fields.Button;

    @ui.decorators.buttonField<FilterSelect>({
        isTransient: true,
        map() {
            return 'Set New Value';
        },
        parent() {
            return this.additionalExamplesBlock;
        },
        onClick() {
            this.field.value = 'Hola';
        },
    })
    setNewValue: ui.fields.Button;

    @ui.decorators.filterSelectField<FilterSelect, ShowCaseProduct>({
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        title: 'With 3 min lookup characters',
        valueField: 'product',
        minLookupCharacters: 3,
        parent() {
            return this.additionalExamplesBlock;
        },
        onChange() {
            if (this.with3minLookupChars.value) {
                this.min3CharsLookupExample.value = this.with3minLookupChars.value;
            } else {
                this.min3CharsLookupExample.value = 'NO VALUE';
            }
        },
    })
    with3minLookupChars: ui.fields.FilterSelect;

    @ui.decorators.textField<FilterSelect>({
        parent() {
            return this.additionalExamplesBlock;
        },
        title: 'With 3 min lookup characters value',
        isDisabled: true,
    })
    min3CharsLookupExample: ui.fields.Text;

    @ui.decorators.filterSelectField<FilterSelect, ShowCaseProduct>({
        parent() {
            return this.additionalExamplesBlock;
        },
        title: 'Restricted Results by filter callback',
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        valueField: 'product',
        isFullWidth: true,
        columns: [
            ui.nestedFields.text({
                bind: 'product',
                title: 'Product',
                canFilter: true,
            }),
            ui.nestedFields.reference<FilterSelect, ShowCaseProduct>({
                bind: 'provider',
                node: '@sage/xtrem-show-case/ShowCaseProvider',
                title: 'Provider',
                canFilter: true,
                valueField: 'textField',
                isHidden: true,
            }),
        ],
        filter() {
            return {
                provider: {
                    textField: {
                        _regex: 'zon',
                    },
                },
            };
        },
    })
    restrictedResultByCallbackFilter: ui.fields.FilterSelect;

    @ui.decorators.filterSelectField<FilterSelect, ShowCaseProduct>({
        parent() {
            return this.additionalExamplesBlock;
        },
        title: 'Restricted Results by filter',
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        valueField: 'product',
        isFullWidth: true,
        columns: [
            ui.nestedFields.text({
                bind: 'product',
                title: 'Product',
                canFilter: true,
            }),
            ui.nestedFields.reference<FilterSelect, ShowCaseProduct>({
                bind: 'provider',
                node: '@sage/xtrem-show-case/ShowCaseProvider',
                title: 'Provider',
                canFilter: true,
                valueField: 'textField',
                isHidden: true,
            }),
        ],
        filter: {
            provider: {
                textField: 'Ali Express',
            },
        },
    })
    restrictedResultByFilter: ui.fields.FilterSelect;

    @ui.decorators.filterSelectField<FilterSelect, ShowCaseProduct>({
        parent() {
            return this.additionalExamplesBlock;
        },
        title: 'Restricted Results by Combined Callback Filter',
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        valueField: 'product',
        isFullWidth: true,
        filter() {
            return {
                provider: {
                    _or: [
                        {
                            textField: {
                                _regex: 'zon',
                            },
                        },
                        {
                            decimalField: '2.34',
                        },
                    ],
                },
            };
        },
    })
    restrictedResultByCombinedCallbackFilter: ui.fields.FilterSelect;

    @ui.decorators.filterSelectField<FilterSelect, ShowCaseProduct>({
        isFullWidth: true,
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        title: 'Additional records in the lookup dialog',
        valueField: 'product',
        minLookupCharacters: 0,
        additionalLookupRecords() {
            return [{ _id: '-1', netPrice: '3', description: 'Some Product', product: 'Some product', progress: 3 }];
        },
        parent() {
            return this.additionalExamplesBlock;
        },
        helperText: 'It is called "Some Product"',
    })
    additionalRecords: ui.fields.FilterSelect;

    @ui.decorators.block<FilterSelect>({
        title: 'Field Validation',
        parent() {
            return this.section;
        },
    })
    validation: ui.containers.Block;

    @ui.decorators.filterSelectField<FilterSelect, ShowCaseProduct>({
        helperText: 'Why not select something with "Spinach" in it?',
        isFullWidth: true,
        isMandatory: true,
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        title: 'Filter Select with Custom Validation',
        valueField: 'product',
        parent() {
            return this.validation;
        },
        validation(value) {
            if (value) {
                return value.includes('Spinach') || value.includes('spinach') ? "Eww, I don't like spinach." : '';
            }
            return undefined;
        },
    })
    validated: ui.fields.FilterSelect;

    @ui.decorators.separatorField<FilterSelect>({
        parent() {
            return this.additionalExamplesBlock;
        },
        isFullWidth: true,
    })
    fieldSeparator1: ui.fields.Separator;

    @ui.decorators.filterSelectField<FilterSelect, ShowCaseProduct>({
        parent() {
            return this.additionalExamplesBlock;
        },
        title: 'With warning message',
        warningMessage: 'Wow, warning!',
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        valueField: 'product',
    })
    warningMessageField: ui.fields.FilterSelect<ShowCaseProduct>;

    @ui.decorators.filterSelectField<FilterSelect, ShowCaseProduct>({
        parent() {
            return this.additionalExamplesBlock;
        },
        title: 'With warning message with callback',
        helperText: 'Select some wine',
        warningMessage() {
            if (
                this.warningMessageWithCallbackField.value &&
                this.warningMessageWithCallbackField.value?.toLowerCase().indexOf('wine') !== -1
            ) {
                return 'Warning message';
            }
            return null;
        },
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        valueField: 'product',
    })
    warningMessageWithCallbackField: ui.fields.FilterSelect<ShowCaseProduct>;

    @ui.decorators.filterSelectField<FilterSelect, ShowCaseProduct>({
        parent() {
            return this.additionalExamplesBlock;
        },
        title: 'With info message',
        infoMessage: 'Wow, warning!',
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        valueField: 'product',
    })
    infoMessageField: ui.fields.FilterSelect<ShowCaseProduct>;

    @ui.decorators.filterSelectField<FilterSelect, ShowCaseProduct>({
        parent() {
            return this.additionalExamplesBlock;
        },
        title: 'With info message with callback',
        helperText: 'Select some wine',
        infoMessage() {
            if (
                this.infoMessageWithCallbackField.value &&
                this.infoMessageWithCallbackField.value?.toLowerCase().indexOf('wine') !== -1
            ) {
                return 'Info message';
            }
            return null;
        },
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        valueField: 'product',
    })
    infoMessageWithCallbackField: ui.fields.FilterSelect<ShowCaseProduct>;

    @ui.decorators.filterSelectField<FilterSelect, ShowCaseProduct>({
        parent() {
            return this.additionalExamplesBlock;
        },
        title: 'With info message',
        warningMessage: 'Wow, warning!',
        infoMessage: 'You should not see this',
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        valueField: 'product',
    })
    infoAndWarningMessageField: ui.fields.FilterSelect<ShowCaseProduct>;

    @ui.decorators.filterSelectField<FilterSelect, ShowCaseProduct>({
        parent() {
            return this.additionalExamplesBlock;
        },
        isMandatory: true,
        title: 'Info, warning and validation',
        warningMessage: 'Wow, warning!',
        infoMessage: 'You should not see this',
        helperText: 'This field is mandatory too.',
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        valueField: 'product',
    })
    infoAndWarningMessageMandatoryField: ui.fields.FilterSelect<ShowCaseProduct>;
}
