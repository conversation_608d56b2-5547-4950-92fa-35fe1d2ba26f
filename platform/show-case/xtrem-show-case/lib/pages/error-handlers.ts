import * as ui from '@sage/xtrem-ui';
import { misc } from '../menu-items/misc';

@ui.decorators.page<ErrorHandlers>({
    title: 'Page - Error handlers',
    authorizationCode: 'WIZARD',
    module: 'show-case',
    category: 'SHOWCASE',
    businessActions() {
        return [this.businessAction1, this.businessAction2, this.crudAction1];
    },
    createAction() {
        return this.crudAction2;
    },
    onError(error: any, screenId: string, elementId: string) {
        return `Page caught. Error on ${elementId}: ${error.message || error}`;
    },
    async onLoad() {
        setTimeout(() => {
            this.field4.value = [
                {
                    _id: '3',
                    column1: 'some value',
                    column2: 'some other value',
                },
            ];
        });
        await new Promise((res, rej) => {
            rej(new Error('ERROR'));
        });
    },
    isTransient: true,
    menuItem: misc,
})
export class ErrorHandlers extends ui.Page {
    @ui.decorators.section<ErrorHandlers>({
        isTitleHidden: true,
    })
    section: ui.containers.Section;

    @ui.decorators.block<ErrorHandlers>({
        parent() {
            return this.section;
        },
    })
    block: ui.containers.Block;

    @ui.decorators.textField<ErrorHandlers>({
        title: 'Text with error handler',
        parent() {
            return this.block;
        },
        onChange() {
            throw new Error('An error occurred');
        },
        onError() {
            return 'An error!';
        },
        helperText: 'Error is thrown on change and it is handled.',
    })
    field1: ui.fields.Text;

    @ui.decorators.textField<ErrorHandlers>({
        title: 'Text with error handler',
        parent() {
            return this.block;
        },
        onChange() {
            throw new Error('An error occurred');
        },
        onError() {
            // Swallows the error
        },
        helperText: 'This error is silent',
    })
    field2: ui.fields.Text;

    @ui.decorators.textField<ErrorHandlers>({
        title: 'Text with error handler',
        parent() {
            return this.block;
        },
        onChange() {
            throw new Error('An error occurred');
        },
        helperText: 'This field does not have error handler',
    })
    field3: ui.fields.Text;

    @ui.decorators.tableField<ErrorHandlers>({
        isTransient: true,
        title: 'Table with handler',
        parent() {
            return this.block;
        },
        onError(error: any, screenId: string, elementId: string) {
            return `Table caught. Error on ${elementId}: ${error.message || error}`;
        },
        columns: [
            ui.nestedFields.text({
                bind: 'column1',
                title: 'Has handler',
                onChange() {
                    throw new Error('uups');
                },
                onError() {
                    return 'Column caught the error!';
                },
            }),
            ui.nestedFields.text({
                bind: 'column2',
                title: 'No handler',
                onChange() {
                    throw new Error('uups');
                },
            }),
        ],
        dropdownActions: [
            {
                title: 'No handler',
                onClick() {
                    throw new Error('uups');
                },
            },
            {
                title: 'Has handler',
                onClick() {
                    throw new Error('uups');
                },
                onError() {
                    return 'The row error handler caught the error!';
                },
            },
        ],
    })
    field4: ui.fields.Table;

    @ui.decorators.pageAction<ErrorHandlers>({
        title: 'A business action',
        onClick() {
            throw new Error('no way!');
        },
        onError() {
            return 'Error on business action';
        },
    })
    businessAction1: ui.PageAction;

    @ui.decorators.pageAction<ErrorHandlers>({
        title: 'A business action no handler',
        onClick() {
            throw new Error('no way!');
        },
    })
    businessAction2: ui.PageAction;

    @ui.decorators.pageAction<ErrorHandlers>({
        title: 'A crud action',
        onClick() {
            throw new Error('no way!');
        },
        onError() {
            return 'Error on save';
        },
    })
    crudAction1: ui.PageAction;

    @ui.decorators.pageAction<ErrorHandlers>({
        title: 'No handler',
        onClick() {
            throw new Error('error on create');
        },
    })
    crudAction2: ui.PageAction;
}
