import {
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>ust<PERSON>,
    ShowCaseInvoiceLine,
    ShowCaseInvoice as ShowCaseInvoiceNode,
    ShowCaseOrder,
    ShowCaseProduct,
} from '@sage/xtrem-show-case-api';
import { setOrderOfPageHeaderDropDownActions } from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import * as ui from '@sage/xtrem-ui';
import { applicationPages } from '../menu-items/application-pages';

@ui.decorators.page<ShowCaseInvoice, ShowCaseInvoiceNode>({
    authorizationCode: 'SHCPRVD',
    category: 'SHOWCASE',
    menuItem: applicationPages,
    objectTypeSingular: 'Invoice',
    objectTypePlural: 'Invoices',
    idField() {
        return [this._id, this.purchaseDate];
    },
    headerQuickActions() {
        return [this.$standardDuplicateAction, this.$standardNewAction];
    },
    businessActions() {
        return [this.refreshPage, this.emptyPage, this.$standardSaveAction];
    },
    createAction() {
        return this.$standardNewAction;
    },
    headerDropDownActions() {
        return setOrderOfPageHeaderDropDownActions({
            remove: [this.$standardDeleteAction],
            dropDownBusinessActions: [this.$standardOpenRecordHistoryAction],
        });
    },
    module: 'show-case',
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.text({ bind: '_id', prefix: 'INV' }),
            titleLine2: ui.nestedFields.reference<ShowCaseInvoice, ShowCaseInvoiceNode, ShowCaseCustomer>({
                bind: 'customer',
                node: '@sage/xtrem-show-case/ShowCaseCustomer',
                valueField: 'name',
                canFilter: true,
            }),
            line2: ui.nestedFields.date({ bind: 'purchaseDate' }),
            titleRight: ui.nestedFields.count({ bind: 'lines', postfix: 'it' }),
        },
    },
    node: '@sage/xtrem-show-case/ShowCaseInvoice',
    title: 'ShowCase - Invoice',
    priority: 300,
})
export class ShowCaseInvoice extends ui.Page<GraphApi> {
    @ui.decorators.section<ShowCaseInvoice>({})
    section: ui.containers.Section;

    @ui.decorators.block<ShowCaseInvoice>({
        parent() {
            return this.section;
        },
    })
    block: ui.containers.Block;

    @ui.decorators.textField<ShowCaseInvoice>({
        parent() {
            return this.block;
        },
        title: 'Invoice Number',
        isReadOnly: true,
        prefix: 'INV',
    })
    _id: ui.fields.Text;

    @ui.decorators.dateField<ShowCaseInvoice>({
        parent() {
            return this.block;
        },
        title: 'Purchase Date',
    })
    purchaseDate: ui.fields.Date;

    @ui.decorators.numericField<ShowCaseInvoice>({
        parent() {
            return this.block;
        },
        title: 'Total quantity',
        isReadOnly: true,
    })
    totalProductQty: ui.fields.Numeric;

    @ui.decorators.referenceField<ShowCaseInvoice, ShowCaseOrder>({
        parent() {
            return this.block;
        },
        fetchesDefaults: true,
        title: 'Order',
        node: '@sage/xtrem-show-case/ShowCaseOrder',
        valueField: { customer: { name: true } },
        columns: [
            ui.nestedFields.text({ bind: '_id', title: 'ID', canFilter: false }),
            ui.nestedFields.reference<ShowCaseInvoice, ShowCaseOrder, ShowCaseCustomer>({
                bind: 'customer',
                node: '@sage/xtrem-show-case/ShowCaseCustomer',
                title: 'Customer',
                valueField: 'name',
            }),
        ],
    })
    order: ui.fields.Reference;

    @ui.decorators.buttonField<ShowCaseInvoice>({
        parent() {
            return this.block;
        },
        map() {
            return 'Add new line';
        },
        isTransient: true,
        async onClick() {
            await this.lines.addRecordWithDefaults();
        },
    })
    addNewLineButton: ui.fields.Button;

    @ui.decorators.checkboxField<ShowCaseInvoice>({
        parent() {
            return this.block;
        },
        title: 'Disable phantom row',
        onChange() {
            this.lines.isPhantomRowDisabled = this.isPhantomRowDisabled.value;
        },
        onClick() {
            this.$.showToast('Disable/Enable phantom row was triggered');
        },
        isTransient: true,
    })
    isPhantomRowDisabled: ui.fields.Checkbox;

    @ui.decorators.tableField<ShowCaseInvoice, ShowCaseInvoiceLine>({
        node: '@sage/xtrem-show-case/ShowCaseInvoiceLine',
        parent() {
            return this.block;
        },
        title: 'Lines',
        fetchesDefaults: true,
        canAddNewLine: true,
        isPhantomRowDisabled: false,
        columns: [
            ui.nestedFields.reference<ShowCaseInvoice, ShowCaseInvoiceLine, ShowCaseProduct>({
                bind: 'product',
                title: 'Product',
                isAutoSelectEnabled: true,
                node: '@sage/xtrem-show-case/ShowCaseProduct',
                valueField: { product: true },
                helperTextField: { _id: true },
                fetchesDefaults: true,
            }),
            ui.nestedFields.numeric({
                bind: 'orderQuantity',
                title: 'Ordered Quantity',
                scale: 0,
                fetchesDefaults: true,
            }),
            ui.nestedFields.numeric({ bind: 'netPrice', title: 'Net Price', scale: 2, fetchesDefaults: true }),
            ui.nestedFields.select({
                bind: 'discountType',
                title: 'Discount Type',
                optionType: '@sage/xtrem-show-case/ShowCaseDiscountType',
            }),
            ui.nestedFields.numeric({ bind: '_sortValue', isHidden: true }),
            ui.nestedFields.text({ bind: 'comments', title: 'Comment' }),
        ],
        fieldActions() {
            return [this.addNewLine];
        },
        dropdownActions: [
            {
                icon: 'bin',
                title: 'Remove',
                isDestructive: true,
                async onClick(rowId: any) {
                    this.lines.removeRecord(rowId);
                },
            },
        ],
    })
    lines: ui.fields.Table;

    @ui.decorators.richTextField<ShowCaseInvoice>({
        parent() {
            return this.block;
        },
        title: 'Notes',
    })
    notes: ui.fields.RichText;

    @ui.decorators.pageAction<ShowCaseInvoice>({
        async onClick() {
            await this.$.graph.create();
            this.$.dialog.message('info', 'Mutation Create', `Created entry: ${this._id.value}`, {
                fullScreen: false,
                rightAligned: false,
                acceptButton: {
                    isDisabled: false,
                    isHidden: false,
                    text: 'ok',
                },
            });
        },
    })
    create: ui.PageAction;

    @ui.decorators.pageAction<ShowCaseInvoice>({
        icon: 'add',
        title: 'Add Line',
        async onClick() {
            await this.lines.addRecordWithDefaults();
        },
    })
    addNewLine: ui.PageAction;

    @ui.decorators.pageAction<ShowCaseInvoice>({
        title: 'Refresh',
        onClick() {
            return this.$.router.refresh();
        },
    })
    refreshPage: ui.PageAction;

    @ui.decorators.pageAction<ShowCaseInvoice>({
        title: 'Empty page',
        onClick() {
            return this.$.router.emptyPage();
        },
    })
    emptyPage: ui.PageAction;
}
