import { Graph<PERSON><PERSON> } from '@sage/xtrem-show-case-api';
import { ShowCaseProduct, ShowCaseProductOriginAddress } from '@sage/xtrem-show-case-api-partial';
import * as ui from '@sage/xtrem-ui';
import { vitalPod } from '../menu-items/vital-pod';

@ui.decorators.page<VitalPodBlock, ShowCaseProduct>({
    authorizationCode: 'SHCPRVD',
    category: 'SHOWCASE',
    menuItem: vitalPod,
    businessActions() {
        return [this.toggleNavigationPanel, this.update];
    },
    createAction() {
        return this.create;
    },
    headerDropDownActions() {
        return [this.delete];
    },
    module: 'show-case',
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.text({ bind: 'product' }),
            line2: ui.nestedFields.text({ bind: '_id' }),
            line3: ui.nestedFields.date({ bind: 'description' }),
        },
    },
    node: '@sage/xtrem-show-case/ShowCaseProduct',
    title: 'Field - Vital Pod (as Block)',
})
export class VitalPodBlock extends ui.Page<GraphApi> {
    @ui.decorators.section<VitalPodBlock>({})
    mainSection: ui.containers.Section;

    @ui.decorators.block<VitalPodBlock>({
        parent() {
            return this.mainSection;
        },
        width: 'medium',
    })
    block: ui.containers.Block;

    @ui.decorators.textField<VitalPodBlock>({
        parent() {
            return this.block;
        },
        title: 'Id',
        width: 'medium',
    })
    _id: ui.fields.Text;

    @ui.decorators.vitalPodField<VitalPodBlock, ShowCaseProductOriginAddress>({
        title: 'Address',
        width: 'medium',
        parent() {
            return this.mainSection;
        },
        onClick() {
            this.clickTriggered.isHidden = false;
            setTimeout(() => {
                this.clickTriggered.isHidden = true;
            }, 5000);
        },
        onChange() {
            this.changeTriggered.isHidden = false;
            setTimeout(() => {
                this.changeTriggered.isHidden = true;
            }, 5000);
        },
        onAddButtonClick() {
            this.addClickTriggered.isHidden = false;
            setTimeout(() => {
                this.addClickTriggered.isHidden = true;
            }, 5000);
            return {
                addressLine1: 'Patata',
            };
        },
        dropdownActions: [
            {
                title: 'Action 1',
                icon: 'sync',
                isDisabled() {
                    return this.isAction1Disabled.value;
                },
                onClick() {
                    this.$.showToast('Action 1 triggered');
                },
            },
            {
                title: 'Action 2',
                icon: 'document_tick',
                isHidden() {
                    return this.isAction2Hidden.value;
                },
                onClick() {
                    this.$.showToast('Action 2 triggered');
                },
            },
            {
                title: 'Action 3',
                icon: 'settings',
                onClick() {
                    this.$.showToast('Action 3 triggered');
                },
            },
        ],
        onError() {
            ui.console.error('just testing onError works.');
        },
        columns: [
            ui.nestedFields.text({ bind: 'name', title: 'Name' }),
            ui.nestedFields.text({ bind: 'addressLine1', title: 'Line1' }),
            ui.nestedFields.text({ bind: 'addressLine2', title: 'Line2' }),
            ui.nestedFields.reference({
                bind: 'country',
                title: 'Country',
                node: '@sage/xtrem-show-case/ShowCaseCountry',
                valueField: 'name',
                helperTextField: 'code',
            }),
        ],
        node: '@sage/xtrem-show-case/ShowCaseProductOriginAddress',
    })
    originAddress: ui.fields.VitalPod;

    @ui.decorators.pageAction<VitalPodBlock>({
        title: 'Custom create title',
        async onClick() {
            await this.$.graph.create();
            this.$.dialog.message('info', 'Mutation Create', `Created entry: ${this._id.value}`, {
                fullScreen: false,
                rightAligned: false,
                acceptButton: {
                    isDisabled: false,
                    isHidden: false,
                    text: 'OK',
                },
            });
        },
    })
    create: ui.PageAction;

    @ui.decorators.pageAction<VitalPodBlock>({
        title: 'Save',
        async onClick() {
            await this.$.graph.update();
            this.$.dialog.message('info', 'Mutation Update', `Updated entry: ${this._id.value}`, {
                fullScreen: false,
                rightAligned: false,
                acceptButton: {
                    isDisabled: false,
                    isHidden: false,
                    text: 'OK',
                },
            });
        },
    })
    update: ui.PageAction;

    @ui.decorators.pageAction<VitalPodBlock>({
        title: 'Delete',
        icon: 'bin',
        isDestructive: true,
        async onClick() {
            await this.$.graph.delete();
            this.$.router.goTo('@sage/xtrem-show-case/BoundPage');
        },
    })
    delete: ui.PageAction;

    @ui.decorators.pageAction<VitalPodBlock>({
        title: 'Toggle Navigation Panel',
        async onClick() {
            this.$.isNavigationPanelHidden = !this.$.isNavigationPanelHidden;
        },
    })
    toggleNavigationPanel: ui.PageAction;

    /** Testing field buttons */

    @ui.decorators.labelField<VitalPodBlock>({
        parent() {
            return this.block;
        },
        isTransient: true,
        isHidden: true,
        map() {
            return 'Change was triggered';
        },
    })
    changeTriggered: ui.fields.Label;

    @ui.decorators.labelField<VitalPodBlock>({
        parent() {
            return this.block;
        },
        isTransient: true,
        isHidden: true,
        map() {
            return 'Click was triggered';
        },
    })
    clickTriggered: ui.fields.Label;

    @ui.decorators.labelField<VitalPodBlock>({
        parent() {
            return this.block;
        },
        isTransient: true,
        isHidden: true,
        map() {
            return 'Add Click was triggered';
        },
    })
    addClickTriggered: ui.fields.Label;

    @ui.decorators.section<VitalPodBlock>({
        isTitleHidden: true,
    })
    configurationSection: ui.containers.Section;

    @ui.decorators.block<VitalPodBlock>({
        parent() {
            return this.configurationSection;
        },
        title: 'Configuration',
        isTransient: true,
    })
    configurationBlock: ui.containers.Block;

    @ui.decorators.textField<VitalPodBlock>({
        parent() {
            return this.configurationBlock;
        },
        isTransient: true,
        title: 'Helper text',
        onChange() {
            this.originAddress.helperText = this.helperText.value;
        },
    })
    helperText: ui.fields.Text;

    @ui.decorators.checkboxField<VitalPodBlock>({
        parent() {
            return this.configurationBlock;
        },
        isTransient: true,
        title: 'Is disabled',
        onChange() {
            this.originAddress.isDisabled = this.isDisabled.value;
        },
    })
    isDisabled: ui.fields.Checkbox;

    @ui.decorators.checkboxField<VitalPodBlock>({
        parent() {
            return this.configurationBlock;
        },
        isTransient: true,
        title: 'Is removable',
        onChange() {
            this.originAddress.canRemove = this.isRemovable.value;
        },
    })
    isRemovable: ui.fields.Checkbox;

    @ui.decorators.checkboxField<VitalPodBlock>({
        parent() {
            return this.configurationBlock;
        },
        isTransient: true,
        title: 'Disable action 1',
    })
    isAction1Disabled: ui.fields.Checkbox;

    @ui.decorators.checkboxField<VitalPodBlock>({
        parent() {
            return this.configurationBlock;
        },
        isTransient: true,
        title: 'Hide action 2',
    })
    isAction2Hidden: ui.fields.Checkbox;

    @ui.decorators.checkboxField<VitalPodBlock>({
        parent() {
            return this.configurationBlock;
        },
        isTransient: true,
        title: 'Is helper text hidden',
        onChange() {
            this.originAddress.isHelperTextHidden = this.isHelperTextHidden.value;
        },
    })
    isHelperTextHidden: ui.fields.Checkbox;

    @ui.decorators.checkboxField<VitalPodBlock>({
        parent() {
            return this.configurationBlock;
        },
        isTransient: true,
        title: 'Is hidden',
        onChange() {
            this.originAddress.isHidden = this.isHidden.value;
        },
    })
    isHidden: ui.fields.Checkbox;

    @ui.decorators.checkboxField<VitalPodBlock>({
        parent() {
            return this.configurationBlock;
        },
        isTransient: true,
        title: 'Is readOnly',
        onChange() {
            this.originAddress.isReadOnly = this.isReadOnly.value;
        },
    })
    isReadOnly: ui.fields.Checkbox;

    @ui.decorators.checkboxField<VitalPodBlock>({
        parent() {
            return this.configurationBlock;
        },
        isTransient: true,
        title: 'Is title hidden',
        onChange() {
            this.originAddress.isTitleHidden = this.isTitleHidden.value;
        },
    })
    isTitleHidden: ui.fields.Checkbox;

    @ui.decorators.textField<VitalPodBlock>({
        parent() {
            return this.configurationBlock;
        },
        isTransient: true,
        title: 'Title',
        onChange() {
            this.originAddress.title = this.title.value;
        },
    })
    title: ui.fields.Text;

    @ui.decorators.buttonField<VitalPodBlock>({
        parent() {
            return this.configurationBlock;
        },
        isTransient: true,
        map() {
            return 'Focus field';
        },
        onClick() {
            this.originAddress.focus();
        },
    })
    focus: ui.fields.Button;
}
