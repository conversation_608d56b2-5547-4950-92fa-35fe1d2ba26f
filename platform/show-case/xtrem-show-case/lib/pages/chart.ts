import { <PERSON><PERSON>h<PERSON><PERSON>, Show<PERSON>aseProvider, ShowCaseProduct } from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import { chartField } from '../menu-items/chart-field';

@ui.decorators.page<Chart, ShowCaseProvider>({
    authorizationCode: 'BSCFLDS',
    module: 'show-case',
    node: '@sage/xtrem-show-case/ShowCaseProvider',
    title: 'Field - Chart',
    category: 'SHOWCASE',
    defaultEntry: () => '2',
    menuItem: chartField,
})
export class Chart extends ui.Page<GraphApi> {
    @ui.decorators.section<Chart>({
        title: 'Chart field',
    })
    section: ui.containers.Section;

    @ui.decorators.block<Chart>({
        parent() {
            return this.section;
        },
        title: 'Field example',
    })
    fieldBlock: ui.containers.Block;

    @ui.decorators.chartField<Chart>({
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        bind: 'products',
        chart: ui.charts.line<Chart, ShowCaseProduct>({
            series: [
                ui.nestedFields.numeric({
                    bind: 'qty',
                    title: 'Quantity',
                    canFilter: true,
                }),
                ui.nestedFields.numeric({
                    bind: 'amount',
                    prefix: '€',
                    title: 'Amount',
                    canFilter: true,
                }),
            ],
            xAxis: ui.nestedFields.date({ bind: 'releaseDate', title: 'Release Date' }),
        }),
        fieldActions() {
            return [this.fieldActionTest];
        },
        isFullWidth: true,
        parent() {
            return this.fieldBlock;
        },
    })
    field: ui.fields.Chart;

    @ui.decorators.block<Chart>({
        parent() {
            return this.section;
        },
        title: 'Configuration',
    })
    configurationBlock: ui.containers.Block;

    @ui.decorators.textField<Chart>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Helper text',
        onChange() {
            this.field.helperText = this.helperText.value;
        },
        isTransient: true,
    })
    helperText: ui.fields.Text;

    @ui.decorators.checkboxField<Chart>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is disabled',
        onChange() {
            this.field.isDisabled = this.isDisabled.value;
        },
        isTransient: true,
    })
    isDisabled: ui.fields.Checkbox;

    @ui.decorators.checkboxField<Chart>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is helper text hidden',
        onChange() {
            this.field.isHelperTextHidden = this.isHelperTextHidden.value;
        },
        isTransient: true,
    })
    isHelperTextHidden: ui.fields.Checkbox;

    @ui.decorators.checkboxField<Chart>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is hidden',
        onChange() {
            this.field.isHidden = this.isHidden.value;
        },
        isTransient: true,
    })
    isHidden: ui.fields.Checkbox;

    @ui.decorators.checkboxField<Chart>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is title hidden',
        onChange() {
            this.field.isTitleHidden = this.isTitleHidden.value;
        },
        isTransient: true,
    })
    isTitleHidden: ui.fields.Checkbox;

    @ui.decorators.textField<Chart>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Title',
        onChange() {
            this.field.title = this.title.value;
        },
        isTransient: true,
    })
    title: ui.fields.Text;

    @ui.decorators.pageAction<Chart>({
        title: 'Field action',
        icon: 'fax',
        onClick() {
            this.$.showToast('Field action triggered');
        },
    })
    fieldActionTest: ui.PageAction;

    /* Additional examples */

    @ui.decorators.block<Chart>({
        parent() {
            return this.section;
        },
        title: 'Additional examples',
    })
    additionalBlock: ui.containers.Block;

    // TODO Is full width

    @ui.decorators.chartField<Chart>({
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        bind: 'products',
        title: 'Restricted result set',
        chart: ui.charts.line<Chart, ShowCaseProduct>({
            series: [
                ui.nestedFields.numeric({
                    bind: 'qty',
                    title: 'Quantity',
                    canFilter: true,
                }),
                ui.nestedFields.numeric({
                    bind: 'amount',
                    prefix: '€',
                    title: 'Amount',
                    canFilter: true,
                }),
            ],
            xAxis: ui.nestedFields.date({ bind: 'releaseDate', title: 'Release Date' }),
        }),
        isFullWidth: true,
        parent() {
            return this.additionalBlock;
        },
    })
    restrictedResultSet: ui.fields.Chart;

    @ui.decorators.numericField<Chart>({
        parent() {
            return this.additionalBlock;
        },
        isTransient: true,
        title: 'Result set restriction (on Amount maximum value)',
        onChange() {
            const filter = this.resultSetRestriction.value
                ? {
                      amount: { _lte: String(this.resultSetRestriction.value) },
                  }
                : undefined;

            this.restrictedResultSet.filter = filter;
        },
    })
    resultSetRestriction: ui.fields.Numeric;
}
