import { GraphApi, ShowCaseProduct } from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import { tableField } from '../menu-items/_index';

@ui.decorators.page<TableSidebarFocus>({
    authorizationCode: 'TBLSDFCS',
    category: 'SHOWCASE',
    menuItem: tableField,
    module: 'show-case',
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.text({ bind: 'textField', title: 'Provider' }),
            titleRight: ui.nestedFields.label({ bind: '_id', title: 'Id' }),
            line2Right: ui.nestedFields.count({
                bind: 'products',
                title: 'Product Count',
            }),
        },
    },
    node: '@sage/xtrem-show-case/ShowCaseProvider',
    title: 'Table with Sidebar - Focus Scenario',
})
export class TableSidebarFocus extends ui.Page<GraphApi> {
    @ui.decorators.section<TableSidebarFocus>({})
    section: ui.containers.Section;

    @ui.decorators.block<TableSidebarFocus>({
        parent() {
            return this.section;
        },
    })
    block: ui.containers.Block;

    @ui.decorators.textField<TableSidebarFocus>({
        bind: '_id',
        parent() {
            return this.block;
        },
        title: 'Id',
    })
    id: ui.fields.Text;

    @ui.decorators.textField<TableSidebarFocus>({
        bind: 'textField',
        parent() {
            return this.block;
        },
        title: 'Provider',
    })
    provider: ui.fields.Text;

    @ui.decorators.tableField<TableSidebarFocus, ShowCaseProduct>({
        bind: 'products',
        canExport: true,
        canFilter: true,
        columns: [
            ui.nestedFields.text({ bind: 'product', title: 'Product' }),
            ui.nestedFields.text({ bind: 'description', title: 'Description' }),
            ui.nestedFields.numeric({
                bind: 'netPrice',
                async onChange(_: string, record?: ShowCaseProduct) {
                    if (
                        record &&
                        (await this.$.dialog
                            .confirmation(
                                'warn',
                                'Are you sure you want to change the price?',
                                'You are about to change the price. This action may have the unintended consequence of changing the price. Are you sure you want to proceed?',
                                {
                                    acceptButton: { text: 'Confirm' },
                                    cancelButton: { text: 'Cancel' },
                                },
                            )
                            .then(() => true)
                            .catch(() => false))
                    ) {
                        console.log('Price change confirmed!');
                        record.netPrice = Number(record.netPrice).toFixed(2);
                    }
                },
                scale: 2,
                title: 'Price',
            }),
            ui.nestedFields.text({ bind: 'barcode', title: 'Barcode' }),
        ],
        hasLineNumbers: true,
        inlineActions: [
            {
                icon: 'box_arrow_left',
                async onClick(id: string) {
                    this.products.openSidebar(id);
                },
                title: 'Edit Product',
            },
        ],
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        parent() {
            return this.block;
        },
        sidebar: {
            layout() {
                return {
                    section: {
                        title: 'Product Details',
                        blocks: {
                            block: {
                                fields: ['_id', 'product', 'netPrice'],
                            },
                        },
                    },
                };
            },
            title(id: string, record?: ShowCaseProduct) {
                return record ? `${record.product} (${id})` : ui.localize('@sage/xtrem-show-case/product', 'Product');
            },
        },
        title: 'Products',
    })
    products: ui.fields.Table<ShowCaseProduct>;
}
