import * as ui from '@sage/xtrem-ui';
import { fields } from '../menu-items/fields';
import type { ShowCaseProductCategory } from '@sage/xtrem-show-case-api';

@ui.decorators.page<RadioButtons>({
    authorizationCode: 'BSCFLDS',
    module: 'show-case',
    title: 'Field - Radio',
    category: 'SHOWCASE',
    isTransient: true,
    menuItem: fields,
})
export class RadioButtons extends ui.Page {
    @ui.decorators.section<RadioButtons>({
        title: 'Radio field',
    })
    section: ui.containers.Section;

    @ui.decorators.block<RadioButtons>({
        parent() {
            return this.section;
        },
        title: 'Field example',
    })
    fieldBlock: ui.containers.Block;

    @ui.decorators.radioField<RadioButtons>({
        title: 'Radio group',
        parent() {
            return this.fieldBlock;
        },
        helperText: 'helper text',
        onChange() {
            if (this.field.value !== this.value.value) {
                this.value.value = this.field.value;
                this.changeTriggered.isHidden = false;
                setTimeout(() => {
                    this.changeTriggered.isHidden = true;
                }, 5000);
            }
        },
        optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
    })
    field: ui.fields.Radio<ShowCaseProductCategory>;

    @ui.decorators.labelField<RadioButtons>({
        parent() {
            return this.fieldBlock;
        },
        isHidden: true,
        map() {
            return 'Changed was triggered';
        },
    })
    changeTriggered: ui.fields.Label;

    @ui.decorators.block<RadioButtons>({
        parent() {
            return this.section;
        },
        title: 'Configuration',
    })
    configurationBlock: ui.containers.Block;

    @ui.decorators.textField<RadioButtons>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Helper text',
        onChange() {
            this.field.helperText = this.helperText.value;
        },
    })
    helperText: ui.fields.Text;

    @ui.decorators.checkboxField<RadioButtons>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is disabled',
        onChange() {
            this.field.isDisabled = this.isDisabled.value;
        },
    })
    isDisabled: ui.fields.Checkbox;

    @ui.decorators.checkboxField<RadioButtons>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is helper text hidden',
        onChange() {
            this.field.isHelperTextHidden = this.isHelperTextHidden.value;
        },
    })
    isHelperTextHidden: ui.fields.Checkbox;

    @ui.decorators.checkboxField<RadioButtons>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is hidden',
        onChange() {
            this.field.isHidden = this.isHidden.value;
        },
    })
    isHidden: ui.fields.Checkbox;

    @ui.decorators.checkboxField<RadioButtons>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is readOnly',
        onChange() {
            this.field.isReadOnly = this.isReadOnly.value;
        },
    })
    isReadOnly: ui.fields.Checkbox;

    @ui.decorators.checkboxField<RadioButtons>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is title hidden',
        onChange() {
            this.field.isTitleHidden = this.isTitleHidden.value;
        },
    })
    isTitleHidden: ui.fields.Checkbox;

    @ui.decorators.textField<RadioButtons>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Title',
        onChange() {
            this.field.title = this.title.value;
        },
    })
    title: ui.fields.Text;

    @ui.decorators.radioField<RadioButtons>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Value',
        onChange() {
            if (this.value.value !== this.field.value) {
                this.field.value = this.value.value;
            }
        },
        optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
    })
    value: ui.fields.Radio<ShowCaseProductCategory>;

    @ui.decorators.buttonField<RadioButtons>({
        parent() {
            return this.configurationBlock;
        },
        map() {
            return 'Focus field';
        },
        onClick() {
            this.field.focus();
        },
    })
    focus: ui.fields.Button;

    /* Additional examples */

    @ui.decorators.block<RadioButtons>({
        parent() {
            return this.section;
        },
        title: 'Additional examples',
    })
    additionalBlock: ui.containers.Block;

    @ui.decorators.radioField<RadioButtons>({
        parent() {
            return this.additionalBlock;
        },
        title: 'Options set in file',
        isMandatory: true,
        helperText: 'The options are not from an enum',
        options: ['dog', 'cat', 'cow', 'a very long option, just to test line breaks', 'giraffe'],
    })
    inlineOption: ui.fields.Radio;

    @ui.decorators.radioField<RadioButtons>({
        parent() {
            return this.additionalBlock;
        },
        title: 'Mandatory',
        isMandatory: true,
        optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
    })
    mandatory: ui.fields.Radio<ShowCaseProductCategory>;

    @ui.decorators.buttonField<RadioButtons>({
        parent() {
            return this.additionalBlock;
        },
        map() {
            return 'Validate mandatory field';
        },
        async onClick() {
            ui.console.log(await this.mandatory.validate());
        },
    })
    validateMandatory: ui.fields.Button;

    @ui.decorators.radioField<RadioButtons>({
        parent() {
            return this.additionalBlock;
        },
        title: 'Full width',
        isFullWidth: true,
        optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
    })
    fullWidth: ui.fields.Radio<ShowCaseProductCategory>;

    @ui.decorators.radioField<RadioButtons>({
        parent() {
            return this.additionalBlock;
        },
        title: 'Sorted with option type',
        isSortedAlphabetically: true,
        optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
    })
    sortedOptionType: ui.fields.Radio<ShowCaseProductCategory>;

    @ui.decorators.radioField<RadioButtons>({
        parent() {
            return this.additionalBlock;
        },
        title: 'Sorted with options',
        isSortedAlphabetically: true,
        options: ['Dreadful', 'Awful', 'Great', 'Bad', 'Mediocre'],
    })
    sortedWithOptions: ui.fields.Radio;

    @ui.decorators.section<RadioButtons>({
        title: 'Examples in various container sizes',
    })
    variousSizeContainer: ui.containers.Section;

    @ui.decorators.block<RadioButtons>({
        parent() {
            return this.variousSizeContainer;
        },
        title: 'extra large block example',
        width: 'extra-large',
    })
    extraLargeBlock: ui.containers.Block;

    @ui.decorators.radioField<RadioButtons>({
        title: 'Radio group',
        isFullWidth: true,
        parent() {
            return this.extraLargeBlock;
        },
        optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
    })
    fieldExtraLarge: ui.fields.Radio<ShowCaseProductCategory>;

    @ui.decorators.block<RadioButtons>({
        parent() {
            return this.variousSizeContainer;
        },
        title: 'large block example',
        width: 'large',
    })
    largeBlock: ui.containers.Block;

    @ui.decorators.radioField<RadioButtons>({
        title: 'Radio group',
        isFullWidth: true,
        parent() {
            return this.largeBlock;
        },
        optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
    })
    fieldLarge: ui.fields.Radio<ShowCaseProductCategory>;

    @ui.decorators.block<RadioButtons>({
        parent() {
            return this.variousSizeContainer;
        },
        title: 'medium block example',
        width: 'medium',
    })
    mediumBlock: ui.containers.Block;

    @ui.decorators.radioField<RadioButtons>({
        title: 'Radio group',
        isFullWidth: true,
        parent() {
            return this.mediumBlock;
        },
        optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
    })
    fieldMedium: ui.fields.Radio<ShowCaseProductCategory>;

    @ui.decorators.block<RadioButtons>({
        parent() {
            return this.variousSizeContainer;
        },
        title: 'small block example',
        width: 'small',
    })
    smallBlock: ui.containers.Block;

    @ui.decorators.radioField<RadioButtons>({
        title: 'Radio group',
        isFullWidth: true,
        parent() {
            return this.smallBlock;
        },
        optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
    })
    fieldSmall: ui.fields.Radio<ShowCaseProductCategory>;

    @ui.decorators.block<RadioButtons>({
        parent() {
            return this.variousSizeContainer;
        },
        title: 'extra small block example',
        width: 'small',
    })
    extraSmallBlock: ui.containers.Block;

    @ui.decorators.radioField<RadioButtons>({
        title: 'Radio group',
        isFullWidth: true,
        parent() {
            return this.extraSmallBlock;
        },
        optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
    })
    fieldExtraSmall: ui.fields.Radio<ShowCaseProductCategory>;

    @ui.decorators.separatorField<RadioButtons>({
        parent() {
            return this.additionalBlock;
        },
        isFullWidth: true,
    })
    fieldSeparator1: ui.fields.Separator;

    @ui.decorators.radioField<RadioButtons>({
        parent() {
            return this.additionalBlock;
        },
        title: 'With warning message',
        warningMessage: 'Wow, warning!',
        optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
    })
    warningMessageField: ui.fields.Radio<ShowCaseProductCategory>;

    @ui.decorators.radioField<RadioButtons>({
        parent() {
            return this.additionalBlock;
        },
        title: 'With warning message with callback',
        helperText: 'Select "awful"',
        warningMessage() {
            if (this.warningMessageWithCallbackField.value === 'awful') {
                return 'Warning message';
            }
            return null;
        },
        optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
    })
    warningMessageWithCallbackField: ui.fields.Radio<ShowCaseProductCategory>;

    @ui.decorators.radioField<RadioButtons>({
        parent() {
            return this.additionalBlock;
        },
        title: 'With info message',
        infoMessage: 'Wow, warning!',
        optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
    })
    infoMessageField: ui.fields.Radio<ShowCaseProductCategory>;

    @ui.decorators.radioField<RadioButtons>({
        parent() {
            return this.additionalBlock;
        },
        title: 'With info message with callback',
        helperText: 'Type "awful"',
        infoMessage() {
            if (this.infoMessageWithCallbackField.value === 'awful') {
                return 'Info message';
            }
            return null;
        },
        optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
    })
    infoMessageWithCallbackField: ui.fields.Radio<ShowCaseProductCategory>;

    @ui.decorators.radioField<RadioButtons>({
        parent() {
            return this.additionalBlock;
        },
        title: 'With info message',
        warningMessage: 'Wow, warning!',
        infoMessage: 'You should not see this',
        optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
    })
    infoAndWarningMessageField: ui.fields.Radio;

    @ui.decorators.radioField<RadioButtons>({
        parent() {
            return this.additionalBlock;
        },
        validation() {
            if (this.infoAndWarningMessageMandatoryField.value === 'good') {
                return 'Error message';
            }
            return '';
        },
        title: 'Info, warning and validation',
        warningMessage: 'Wow, warning!',
        infoMessage: 'You should not see this',
        helperText: 'Not allowed to select "good"',
        optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
    })
    infoAndWarningMessageMandatoryField: ui.fields.Radio<ShowCaseProductCategory>;
}
