import { GraphApi, ShowCaseProduct } from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import { navigationPanel } from '../menu-items/navigation-panel';

@ui.decorators.page<NavigationPanelHidden, ShowCaseProduct>({
    authorizationCode: 'NAVPNLHID',
    module: 'show-case',
    title: 'Navigation Panel - With Hidden Nested Items',
    node: '@sage/xtrem-show-case/ShowCaseProduct',
    category: 'SHOWCASE',
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.text({ bind: 'product' }),
            titleRight: ui.nestedFields.label({ bind: 'category' }),
            line2: ui.nestedFields.text({
                bind: 'releaseDate',
                isHidden(value, data) {
                    return data?.category === 'good';
                },
            }),
        },
        optionsMenu: [
            {
                title: 'All',
                graphQLFilter: {},
            },
            {
                title: 'Amazon',
                graphQLFilter: {
                    provider: {
                        textField: {
                            _eq: 'Amazon',
                        },
                    },
                },
            },
        ],
    },
    menuItem: navigationPanel,
})
export class NavigationPanelHidden extends ui.Page<GraphApi> {
    @ui.decorators.section<NavigationPanelHidden>({
        title: 'Nothing to see here...',
    })
    section: ui.containers.Section;
}
