import { Graph<PERSON><PERSON>, ShowCaseCountry, ShowCaseEmployee as ShowCaseEmployeeNode } from '@sage/xtrem-show-case-api';
import {
    setApplicativePageCrudActions,
    setOrderOfPageHeaderDropDownActions,
} from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import * as ui from '@sage/xtrem-ui';
import { applicationPages } from '../menu-items/application-pages';

@ui.decorators.page<ShowCaseEmployee, ShowCaseEmployeeNode>({
    menuItem: applicationPages,
    createAction() {
        return this.$standardNewAction;
    },
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.text({ bind: 'firstName' }),
            titleRight: ui.nestedFields.text({ bind: 'lastName' }),
        },
    },
    businessActions() {
        return [this.$standardCancelAction, this.$standardSaveAction];
    },
    headerQuickActions() {
        return [this.$standardDuplicateAction];
    },
    headerDropDownActions() {
        return setOrderOfPageHeaderDropDownActions({
            remove: [this.$standardDeleteAction],
            dropDownBusinessActions: [this.$standardOpenRecordHistoryAction],
        });
    },
    node: '@sage/xtrem-show-case/ShowCaseEmployee',
    title: 'ShowCase - Employee',
    objectTypeSingular: 'Employee',
    objectTypePlural: 'Employees',
    idField() {
        return [this.firstName, this.lastName];
    },
    onLoad() {
        setApplicativePageCrudActions({
            page: this,
            isDirty: false,
            save: this.$standardSaveAction,
            cancel: this.$standardCancelAction,
            remove: this.$standardDeleteAction,
            actions: [],
        });
    },
    onDirtyStateUpdated(isDirty: boolean) {
        setApplicativePageCrudActions({
            page: this,
            isDirty,
            save: this.$standardSaveAction,
            cancel: this.$standardCancelAction,
            remove: this.$standardDeleteAction,
            actions: [],
        });
    },
    priority: 300,
})
export class ShowCaseEmployee extends ui.Page<GraphApi> {
    @ui.decorators.section<ShowCaseEmployee>({})
    section: ui.containers.Section;

    @ui.decorators.block<ShowCaseEmployee>({
        parent() {
            return this.section;
        },
    })
    block: ui.containers.Block;

    @ui.decorators.textField<ShowCaseEmployee>({
        parent() {
            return this.block;
        },
        title: 'ID',
        isReadOnly: true,
    })
    _id: ui.fields.Text;

    @ui.decorators.textField<ShowCaseEmployee>({
        parent() {
            return this.block;
        },
        title: 'First Name',
    })
    firstName: ui.fields.Text;

    @ui.decorators.textField<ShowCaseEmployee>({
        parent() {
            return this.block;
        },
        title: 'Last Name',
    })
    lastName: ui.fields.Text;

    @ui.decorators.referenceField<ShowCaseEmployee, ShowCaseCountry>({
        parent() {
            return this.block;
        },
        bind: 'country',
        title: 'Country',
        node: '@sage/xtrem-show-case/ShowCaseCountry',
        tunnelPage: '@sage/xtrem-show-case/ShowCaseCountry',
        valueField: 'name',
        helperTextField: 'code',
        columns: null,
    })
    country: ui.fields.Reference;

    @ui.decorators.treeField<ShowCaseEmployee, ShowCaseEmployeeNode>({
        node: '@sage/xtrem-show-case/ShowCaseEmployee',
        sublevelProperty: 'teamMembers',
        bind: 'teamMembers',
        title: 'Team members',
        masterColumn: ui.nestedFields.text({
            bind: 'email',
            title: 'Email address',
        }),
        columns: [
            ui.nestedFields.text({
                bind: 'firstName',
                title: 'First name',
            }),
            ui.nestedFields.text({
                bind: 'lastName',
                title: 'Last Name',
            }),
            ui.nestedFields.text({
                bind: 'city',
                title: 'City',
            }),
            ui.nestedFields.reference({
                bind: 'country',
                node: '@sage/xtrem-show-case/ShowCaseCountry',
                title: 'Country',
                valueField: 'name',
            }),
        ],
        parent() {
            return this.block;
        },
    })
    field: ui.fields.Tree<ShowCaseEmployeeNode>;
}
