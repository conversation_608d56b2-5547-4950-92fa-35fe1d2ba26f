import {
    ShowCase<PERSON>ustomer,
    ShowCaseInvoice,
    ShowCaseInvoiceLine,
    ShowCaseOrder,
    ShowCaseProduct,
} from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import { nestedGrid } from '../menu-items/nested-grid';

function randomBetween(min: number, max: number): number {
    return Math.floor(Math.random() * (max - min + 1) + min);
}

@ui.decorators.page<NestedGrid, ShowCaseCustomer>({
    authorizationCode: 'BSCFLDS',
    module: 'show-case',
    category: 'SHOWCASE',
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.text({ bind: '_id' }),
            titleLineLeft: ui.nestedFields.text({ bind: 'name' }),
            titleRight: ui.nestedFields.text({ bind: 'email' }),
        },
    },
    node: '@sage/xtrem-show-case/ShowCaseCustomer',
    title: 'Field - NestedGrid',
    menuItem: nestedGrid,
    businessActions() {
        return [this.saveNestedGrid];
    },
})
export class NestedGrid extends ui.Page {
    @ui.decorators.section<NestedGrid>({
        title: 'Nested Grid',
    })
    section: ui.containers.Section;

    @ui.decorators.block<NestedGrid>({
        parent() {
            return this.section;
        },
        title: 'Nested Grid block',
    })
    fieldBlock: ui.containers.Block;

    @ui.decorators.block<NestedGrid>({
        parent() {
            return this.section;
        },
        title: 'Fetch level block',
    })
    fetchLevelBlock: ui.containers.Block;

    @ui.decorators.block<NestedGrid>({
        parent() {
            return this.section;
        },
        title: 'Select items by app code',
    })
    selectItemBlock: ui.containers.Block;

    @ui.decorators.block<NestedGrid>({
        parent() {
            return this.section;
        },
        title: 'Nested Grid in activation mode',
    })
    activationBlock: ui.containers.Block;

    @ui.decorators.block<NestedGrid>({
        parent() {
            return this.section;
        },
        title: 'Configuration',
    })
    configurationBlock: ui.containers.Block;

    @ui.decorators.textField<NestedGrid>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Title',
        onChange() {
            this.field.title = this.title.value;
        },
        isTransient: true,
    })
    title: ui.fields.Text;

    @ui.decorators.textField<NestedGrid>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Helper text',
        onChange() {
            this.field.helperText = this.helperText.value;
        },
        isTransient: true,
    })
    helperText: ui.fields.Text;

    @ui.decorators.checkboxField<NestedGrid>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is hidden',
        onChange() {
            this.field.isHidden = this.isHidden.value;
        },
        isTransient: true,
    })
    isHidden: ui.fields.Checkbox;

    @ui.decorators.checkboxField<NestedGrid>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is title hidden',
        onChange() {
            this.field.isTitleHidden = this.isTitleHidden.value;
        },
        isTransient: true,
    })
    isTitleHidden: ui.fields.Checkbox;

    @ui.decorators.checkboxField<NestedGrid>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is helper text hidden',
        onChange() {
            this.field.isHelperTextHidden = this.isHelperTextHidden.value;
        },
        isTransient: true,
    })
    isHelperTextHidden: ui.fields.Checkbox;

    @ui.decorators.checkboxField<NestedGrid>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is disabled',
        onChange() {
            this.field.isDisabled = this.isDisabled.value;
        },
        isTransient: true,
    })
    isDisabled: ui.fields.Checkbox;

    @ui.decorators.checkboxField<NestedGrid>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is container disabled',
        onChange() {
            this.fieldBlock.isDisabled = this.isContainerDisabled.value;
        },
        isTransient: true,
    })
    isContainerDisabled: ui.fields.Checkbox;

    @ui.decorators.nestedGridField<NestedGrid, [ShowCaseOrder, ShowCaseInvoice, ShowCaseInvoiceLine]>({
        bind: 'orders',
        canFilter: true,
        canActivate: true,
        onRowClick(rowId: string, rowData: any) {
            ui.console.log(rowData);
        },
        helperText: 'The change indicator color is also disabled!',
        isChangeIndicatorDisabled: true,
        onRowActivated(_id: string | number, row: any) {
            this.$.showToast(
                `${Object.keys(row)
                    .filter(k => !k.startsWith('__'))
                    .map(key => (typeof row[key] === 'object' ? row[key]?._id : row[key]))
                    .join(' | ')} ACTIVATED.`,
            );
        },
        onRowDeactivated(_id: string | number, row: any) {
            this.$.showToast(
                `${Object.keys(row)
                    .filter(k => !k.startsWith('__'))
                    .map(key => (typeof row[key] === 'object' ? row[key]?._id : row[key]))
                    .join(' | ')} DEACTIVATED.`,
            );
        },
        levels: [
            {
                node: '@sage/xtrem-show-case/ShowCaseOrder',
                childProperty: 'invoices',
                canAddNewLine: true,
                columns: [
                    ui.nestedFields.text({
                        bind: '_id',
                        title: 'Id',
                        isHiddenDesktop: false,
                        isReadOnly: true,
                        onClick(rowId: string, rowData: any) {
                            ui.console.log(rowData);
                        },
                    }),
                    ui.nestedFields.date({
                        bind: 'orderDate',
                        title: 'Order Date',
                        canFilter: true,
                    }),
                ],
                sidebar: {
                    title: 'Add new invoice',
                    layout() {
                        return {
                            mainSection: {
                                title: 'Main section',
                                blocks: {
                                    mainBlock: {
                                        title: 'Some block title',
                                        fields: ['_id', 'orderDate'],
                                    },
                                },
                            },
                        };
                    },
                },
            },
            {
                node: '@sage/xtrem-show-case/ShowCaseInvoice',
                childProperty: 'lines',
                columns: [
                    ui.nestedFields.text({
                        bind: '_id',
                        title: 'Id',
                        isHiddenDesktop: false,
                        isReadOnly: true,
                        onClick(rowId: string, rowData: any) {
                            ui.console.log(rowData);
                        },
                    }),
                    ui.nestedFields.date({
                        bind: 'purchaseDate',
                        title: 'Purchase Date',
                        onClick(rowId: string, rowData: any) {
                            ui.console.log(rowData);
                        },
                    }),
                ],
            },
            {
                node: '@sage/xtrem-show-case/ShowCaseInvoiceLine',
                columns: [
                    ui.nestedFields.text({
                        bind: '_id',
                        title: 'Id',
                        isHiddenDesktop: false,
                        isReadOnly: true,
                        onClick(rowId: string, rowData: any) {
                            ui.console.log(rowData);
                        },
                    }),
                    ui.nestedFields.numeric({
                        bind: 'orderQuantity',
                        title: 'Quantity',
                        scale: 0,
                    }),
                    ui.nestedFields.numeric({
                        bind: 'netPrice',
                        title: 'Net Price',
                        scale: 2,
                    }),
                    ui.nestedFields.reference<NestedGrid, ShowCaseInvoiceLine, ShowCaseProduct>({
                        isFilterLimitedToDataset: true,
                        bind: 'product',
                        node: '@sage/xtrem-show-case/ShowCaseProduct',
                        valueField: { product: true },
                        helperTextField: { _id: true },
                        title: 'Product',
                        canFilter: true,
                    }),
                ],
                dropdownActions: [
                    {
                        icon: 'bin',
                        title: 'Remove',
                        isDestructive: true,
                        onClick(rowId, _data, level) {
                            this.activatableField.removeRecord(rowId, level ?? 0);
                        },
                    },
                ],
                canAddNewLine: true,
                sidebar: {
                    title: 'Add new invoice line',
                    layout() {
                        return {
                            mainSection: {
                                title: 'Main section',
                                blocks: {
                                    mainBlock: {
                                        title: 'Some block title',
                                        fields: ['_id', 'orderQuantity', 'netPrice', 'product'],
                                    },
                                },
                            },
                        };
                    },
                },
            },
        ],
        parent() {
            return this.activationBlock;
        },
    })
    activatableField: ui.fields.NestedGrid<[ShowCaseOrder, ShowCaseInvoice, ShowCaseInvoiceLine], NestedGrid>;

    @ui.decorators.nestedGridField<NestedGrid, [ShowCaseOrder, ShowCaseInvoice, ShowCaseInvoiceLine]>({
        bind: 'orders',
        onRowSelected(id: string | number, row: any, level: number) {
            const type = level === 0 ? 'Order' : level === 1 ? 'Invoice' : 'Invoice Line';
            this.$.showToast(
                `${type} with ID ${id} (${Object.keys(row)
                    .filter(k => k !== '_id')
                    .map(key => (typeof row[key] === 'object' ? row[key]?._id : row[key]))
                    .join(' | ')}) SELECTED.`,
            );
            if (level === 0) {
                this.selectedOperations.value = (this.selectedOperations.value || '')
                    .split(',')
                    .filter(s => s !== '')
                    .concat(String(id))
                    .sort()
                    .join(',');
            } else if (level === 1) {
                this.selectedInvoices.value = (this.selectedInvoices.value || '')
                    .split(',')
                    .filter(s => s !== '')
                    .concat(String(id))
                    .sort()
                    .join(',');
            } else if (level === 2) {
                this.selectedInvoiceLines.value = (this.selectedInvoiceLines.value || '')
                    .split(',')
                    .filter(s => s !== '')
                    .concat(String(id))
                    .sort()
                    .join(',');
            }
        },
        onRowUnselected(id: string | number, row: any, level: number) {
            const type = level === 0 ? 'Order' : level === 1 ? 'Invoice' : 'Invoice Line';
            this.$.showToast(
                `${type} with ID ${id} (${Object.keys(row)
                    .filter(k => k !== '_id')
                    .map(key => (typeof row[key] === 'object' ? row[key]?._id : row[key]))
                    .join(' | ')}) UNSELECTED.`,
            );
            if (level === 0) {
                this.selectedOperations.value = (this.selectedOperations.value || '')
                    .split(',')
                    .filter(s => s !== '' && s !== String(id))
                    .sort()
                    .join(',');
            } else if (level === 1) {
                this.selectedInvoices.value = (this.selectedInvoices.value || '')
                    .split(',')
                    .filter(s => s !== '' && s !== String(id))
                    .sort()
                    .join(',');
            } else if (level === 2) {
                this.selectedInvoiceLines.value = (this.selectedInvoiceLines.value || '')
                    .split(',')
                    .filter(s => s !== '' && s !== String(id))
                    .sort()
                    .join(',');
            }
        },
        levels: [
            {
                node: '@sage/xtrem-show-case/ShowCaseOrder',
                childProperty: 'invoices',
                canAddNewLine: true,
                columns: [
                    ui.nestedFields.text({
                        bind: '_id',
                        title: 'Id',
                        isHiddenDesktop: false,
                        isReadOnly: true,
                    }),
                    ui.nestedFields.date({
                        bind: 'orderDate',
                        title: 'Order Date',
                        canFilter: true,
                    }),
                    ui.nestedFields.dropdownList({
                        bind: 'orderType',
                        canFilter: true,
                        optionType: '@sage/xtrem-show-case/ShowCaseOrderType',
                        title: 'Order Type',
                    }),
                ],
                onLevelExpanded(parentId, children) {
                    this.$.showToast(`Expanded order with ID ${parentId} and ${children.length} children`);
                },
                emptyStateText: 'We are in level 1',
                emptyStateClickableText: 'Click me',
                onEmptyStateLinkClick(parentId, level) {
                    this.$.dialog.message(
                        'info',
                        'Hey',
                        `You just clicked me\n ParentId: ${parentId}\n Level: ${level}`,
                    );
                },
                dropdownActions: [
                    {
                        icon: 'add',
                        title: 'Random Action',
                        isDisabled() {
                            return false;
                        },
                        onClick(rowId, data: any) {
                            this.$.showToast(`You triggered the action on ${data._id} - ${data.orderDate}`, {
                                type: 'info',
                            });
                        },
                    },
                    {
                        title: 'Refresh record',
                        icon: 'refresh',
                        onClick(rowId, rowItem, level) {
                            return this.field.refreshRecord(rowId, level);
                        },
                    },
                ],
            },
            {
                node: '@sage/xtrem-show-case/ShowCaseInvoice',
                childProperty: 'lines',
                columns: [
                    ui.nestedFields.reference<NestedGrid, ShowCaseInvoice, ShowCaseCustomer>({
                        bind: { customer: true },
                        title: 'Customer',
                        valueField: 'name',
                        node: '@sage/xtrem-show-case/ShowCaseCustomer',
                    }),
                    ui.nestedFields.text({
                        bind: '_id',
                        title: 'Id',
                        isHiddenDesktop: false,
                        isReadOnly: true,
                    }),
                    ui.nestedFields.numeric({
                        bind: 'totalProductQty',
                        title: 'Total Product Quantity',
                        canFilter: true,
                    }),
                    ui.nestedFields.date({
                        bind: 'purchaseDate',
                        title: 'Purchase Date',
                        canFilter: true,
                    }),
                ],
                onLevelExpanded(parentId, children) {
                    this.$.showToast(`Expanded invoice with ID ${parentId} and ${children.length} children`);
                },
                emptyStateText: 'We are in level 2',
                emptyStateClickableText: 'Click me',
                onEmptyStateLinkClick(parentId, level) {
                    this.$.dialog.message(
                        'info',
                        'Hey',
                        `You just clicked me\n ParentId: ${parentId}\n Level: ${level}`,
                    );
                },
                dropdownActions: [
                    {
                        icon: 'add',
                        title: 'Row Action',
                        isDisabled(_id, rowValue) {
                            return Number(rowValue._id) % 2 === 0;
                        },
                        async onClick(rowId, data: ShowCaseInvoice, level) {
                            this.$.showToast(`You triggered the action on ${data._id} - ${data.purchaseDate}`, {
                                type: 'info',
                            });
                            // await here because the nested-grid.feature cucumber tests clicks the OK button
                            await this.$.dialog.message('info', 'add1', `Level is ${level}`);
                        },
                    },
                    {
                        icon: 'none',
                        title: 'Row Action 2',
                        async onClick(rowId) {
                            this.$.showToast(`You triggered the action on ${rowId}`, {
                                type: 'info',
                            });
                        },
                    },
                ],
            },
            {
                node: '@sage/xtrem-show-case/ShowCaseInvoiceLine',
                columns: [
                    ui.nestedFields.text({
                        bind: '_id',
                        title: 'Id',
                        isHiddenDesktop: false,
                        isReadOnly: true,
                    }),
                    ui.nestedFields.numeric({
                        bind: 'orderQuantity',
                        title: 'Quantity',
                        scale: 0,
                        canFilter: true,
                    }),
                    ui.nestedFields.numeric({
                        bind: 'netPrice',
                        title: 'Net Price',
                        scale: 2,
                        canFilter: true,
                    }),
                    ui.nestedFields.reference<NestedGrid, ShowCaseInvoiceLine, ShowCaseProduct>({
                        isFilterLimitedToDataset: true,
                        bind: 'product',
                        node: '@sage/xtrem-show-case/ShowCaseProduct',
                        valueField: { product: true },
                        helperTextField: { _id: true },
                        title: 'Product',
                        canFilter: true,
                    }),
                    ui.nestedFields.numeric({
                        bind: 'priceDiscount',
                        title: 'Price Discount',
                        scale: 2,
                    }),
                ],
                emptyStateText: 'We are in level 3',
                emptyStateClickableText: 'Click me',
                onEmptyStateLinkClick(parentId, level) {
                    this.$.dialog.message(
                        'info',
                        'Hey',
                        `You just clicked me\n ParentId: ${parentId}\n Level: ${level}`,
                    );
                },
                dropdownActions: [
                    {
                        icon: 'add',
                        title: 'Random Action',
                        isDisabled() {
                            return false;
                        },
                        onClick(rowId, data: any) {
                            this.$.showToast(`You triggered the action on ${data._id} - ${data.product.product}`, {
                                type: 'info',
                            });
                        },
                    },
                    {
                        title: 'Refresh record',
                        icon: 'refresh',
                        onClick(rowId, rowItem, level) {
                            return this.field.refreshRecord(rowId, level);
                        },
                    },
                ],
            },
        ],
        parent() {
            return this.fieldBlock;
        },
    })
    field: ui.fields.NestedGrid<[ShowCaseOrder, ShowCaseInvoice, ShowCaseInvoiceLine], NestedGrid>;

    @ui.decorators.nestedGridField<NestedGrid, [ShowCaseOrder, ShowCaseInvoice, ShowCaseInvoiceLine]>({
        title: 'NestedGrid with section as parent',
        bind: 'orders',
        onRowSelected(id: string | number, row: any, level: number) {
            const type = level === 0 ? 'Order' : level === 1 ? 'Invoice' : 'Invoice Line';
            this.$.showToast(
                `${type} with ID ${id} (${Object.keys(row)
                    .filter(k => k !== '_id')
                    .map(key => (typeof row[key] === 'object' ? row[key]?._id : row[key]))
                    .join(' | ')}) SELECTED.`,
            );
            if (level === 0) {
                this.selectedOperations.value = (this.selectedOperations.value || '')
                    .split(',')
                    .filter(s => s !== '')
                    .concat(String(id))
                    .sort()
                    .join(',');
            } else if (level === 1) {
                this.selectedInvoices.value = (this.selectedInvoices.value || '')
                    .split(',')
                    .filter(s => s !== '')
                    .concat(String(id))
                    .sort()
                    .join(',');
            } else if (level === 2) {
                this.selectedInvoiceLines.value = (this.selectedInvoiceLines.value || '')
                    .split(',')
                    .filter(s => s !== '')
                    .concat(String(id))
                    .sort()
                    .join(',');
            }
        },
        onRowUnselected(id: string | number, row: any, level: number) {
            const type = level === 0 ? 'Order' : level === 1 ? 'Invoice' : 'Invoice Line';
            this.$.showToast(
                `${type} with ID ${id} (${Object.keys(row)
                    .filter(k => k !== '_id')
                    .map(key => (typeof row[key] === 'object' ? row[key]?._id : row[key]))
                    .join(' | ')}) UNSELECTED.`,
            );
            if (level === 0) {
                this.selectedOperations.value = (this.selectedOperations.value || '')
                    .split(',')
                    .filter(s => s !== '' && s !== String(id))
                    .sort()
                    .join(',');
            } else if (level === 1) {
                this.selectedInvoices.value = (this.selectedInvoices.value || '')
                    .split(',')
                    .filter(s => s !== '' && s !== String(id))
                    .sort()
                    .join(',');
            } else if (level === 2) {
                this.selectedInvoiceLines.value = (this.selectedInvoiceLines.value || '')
                    .split(',')
                    .filter(s => s !== '' && s !== String(id))
                    .sort()
                    .join(',');
            }
        },
        levels: [
            {
                node: '@sage/xtrem-show-case/ShowCaseOrder',
                childProperty: 'invoices',
                columns: [
                    ui.nestedFields.text({
                        bind: '_id',
                        title: 'Id',
                        isHiddenDesktop: false,
                        isReadOnly: true,
                    }),
                    ui.nestedFields.date({
                        bind: 'orderDate',
                        title: 'Order Date',
                        canFilter: true,
                    }),
                    ui.nestedFields.dropdownList({
                        bind: 'orderType',
                        canFilter: true,
                        optionType: '@sage/xtrem-show-case/ShowCaseOrderType',
                        title: 'Order Type',
                    }),
                ],
                emptyStateText: 'We are in level 1',
                emptyStateClickableText: 'Click me',
                onEmptyStateLinkClick() {
                    this.$.dialog.message('info', 'Hey', 'You just clicked me');
                },
                dropdownActions: [
                    {
                        icon: 'add',
                        title: 'Random Action',
                        isDisabled() {
                            return false;
                        },
                        onClick(rowId, data: any) {
                            this.$.showToast(`You triggered the action on ${data._id} - ${data.orderDate}`, {
                                type: 'info',
                            });
                        },
                    },
                    {
                        title: 'Refresh record',
                        icon: 'refresh',
                        onClick(rowId, rowItem, level) {
                            return this.field.refreshRecord(rowId, level);
                        },
                    },
                ],
            },
            {
                node: '@sage/xtrem-show-case/ShowCaseInvoice',
                childProperty: 'lines',
                columns: [
                    ui.nestedFields.text({
                        bind: '_id',
                        title: 'Id',
                        isHiddenDesktop: false,
                        isReadOnly: true,
                    }),
                    ui.nestedFields.numeric({
                        bind: 'totalProductQty',
                        title: 'Total Product Quantity',
                        canFilter: true,
                    }),
                    ui.nestedFields.date({
                        bind: 'purchaseDate',
                        title: 'Purchase Date',
                        canFilter: true,
                    }),
                ],
                emptyStateText: 'We are in level 2',
                emptyStateClickableText: 'Click me',
                onEmptyStateLinkClick() {
                    this.$.dialog.message('info', 'Hey', 'You just clicked me');
                },
                dropdownActions: [
                    {
                        icon: 'add',
                        title: 'Row Action',
                        isDisabled(_id, rowValue) {
                            return Number(rowValue._id) % 2 === 0;
                        },
                        async onClick(rowId, data: ShowCaseInvoice, level) {
                            this.$.showToast(`You triggered the action on ${data._id} - ${data.purchaseDate}`, {
                                type: 'info',
                            });
                            // await here because the nested-grid.feature cucumber tests clicks the OK button
                            await this.$.dialog.message('info', 'add1', `Level is ${level}`);
                        },
                    },
                ],
            },
            {
                node: '@sage/xtrem-show-case/ShowCaseInvoiceLine',
                columns: [
                    ui.nestedFields.text({
                        bind: '_id',
                        title: 'Id',
                        isHiddenDesktop: false,
                        isReadOnly: true,
                    }),
                    ui.nestedFields.numeric({
                        bind: 'orderQuantity',
                        title: 'Quantity',
                        scale: 0,
                        canFilter: true,
                    }),
                    ui.nestedFields.numeric({
                        bind: 'netPrice',
                        title: 'Net Price',
                        scale: 2,
                        canFilter: true,
                    }),
                    ui.nestedFields.reference<NestedGrid, ShowCaseInvoiceLine, ShowCaseProduct>({
                        isFilterLimitedToDataset: true,
                        bind: 'product',
                        node: '@sage/xtrem-show-case/ShowCaseProduct',
                        valueField: { product: true },
                        helperTextField: { _id: true },
                        title: 'Product',
                        canFilter: true,
                    }),
                    ui.nestedFields.numeric({
                        bind: 'priceDiscount',
                        title: 'Price Discount',
                        scale: 2,
                    }),
                ],
                emptyStateText: 'We are in level 3',
                emptyStateClickableText: 'Click me',
                onEmptyStateLinkClick() {
                    this.$.dialog.message('info', 'Hey', 'You just clicked me');
                },
                dropdownActions: [
                    {
                        icon: 'add',
                        title: 'Random Action',
                        isDisabled() {
                            return false;
                        },
                        onClick(rowId, data: any) {
                            this.$.showToast(`You triggered the action on ${data._id} - ${data.product.product}`, {
                                type: 'info',
                            });
                        },
                    },
                    {
                        title: 'Refresh record',
                        icon: 'refresh',
                        onClick(rowId, rowItem, level) {
                            return this.field.refreshRecord(rowId, level);
                        },
                    },
                ],
            },
        ],
        parent() {
            return this.section;
        },
    })
    fieldChildOfSection: ui.fields.NestedGrid<[ShowCaseOrder, ShowCaseInvoice, ShowCaseInvoiceLine], NestedGrid>;

    @ui.decorators.checkboxField<NestedGrid>({
        parent() {
            return this.fetchLevelBlock;
        },
        title: 'Should open level on UI?',
        isTransient: true,
    })
    shouldOpenLevelOnUi: ui.fields.Checkbox;

    @ui.decorators.textField<NestedGrid>({
        parent() {
            return this.fetchLevelBlock;
        },
        title: 'Parent ID',
        isTransient: true,
    })
    parentId: ui.fields.Text;

    @ui.decorators.numericField<NestedGrid>({
        parent() {
            return this.fetchLevelBlock;
        },
        title: 'Level',
        isTransient: true,
        min: 1,
        max: 3,
    })
    level: ui.fields.Numeric;

    @ui.decorators.buttonField<NestedGrid>({
        parent() {
            return this.fetchLevelBlock;
        },
        map() {
            return 'Fetch Level';
        },
        async onClick() {
            const children = await this.field.loadChildRecords({
                childLevel: this.level.value || 1,
                parentRecordId: this.parentId.value,
                openLevel: this.shouldOpenLevelOnUi.value,
            });
            ui.console.log(children);
        },
        isTransient: true,
    })
    fetchLevelButton: ui.fields.Button;

    @ui.decorators.textField<NestedGrid>({
        parent() {
            return this.selectItemBlock;
        },
        title: 'Select Record ID',
        isTransient: true,
    })
    selectRecordId: ui.fields.Text;

    @ui.decorators.numericField<NestedGrid>({
        parent() {
            return this.selectItemBlock;
        },
        title: 'Level',
        isTransient: true,
    })
    selectRecordLevel: ui.fields.Numeric;

    @ui.decorators.buttonField<NestedGrid>({
        parent() {
            return this.selectItemBlock;
        },
        map() {
            return 'Select item';
        },
        onClick() {
            this.field.selectRecord(this.selectRecordId.value, Number(this.selectRecordLevel.value));
        },
        isTransient: true,
    })
    selectItem: ui.fields.Button;

    @ui.decorators.buttonField<NestedGrid>({
        parent() {
            return this.selectItemBlock;
        },
        map() {
            return 'Unselect item';
        },
        onClick() {
            this.field.unselectRecord(this.selectRecordId.value, Number(this.selectRecordLevel.value));
        },
        isTransient: true,
    })
    unselectItem: ui.fields.Button;

    @ui.decorators.textAreaField<NestedGrid>({
        isReadOnly: true,
        isTransient: true,
        title: 'Selected Operations',
        parent() {
            return this.fieldBlock;
        },
    })
    selectedOperations: ui.fields.TextArea;

    @ui.decorators.textAreaField<NestedGrid>({
        isReadOnly: true,
        isTransient: true,
        title: 'Selected Invoices',
        parent() {
            return this.fieldBlock;
        },
    })
    selectedInvoices: ui.fields.TextArea;

    @ui.decorators.textAreaField<NestedGrid>({
        isReadOnly: true,
        isTransient: true,
        title: 'Selected Invoice Lines',
        parent() {
            return this.fieldBlock;
        },
    })
    selectedInvoiceLines: ui.fields.TextArea;

    @ui.decorators.buttonField<NestedGrid>({
        parent() {
            return this.fieldBlock;
        },
        onClick() {
            this.field.addRecordWithDefaults(0, undefined);
        },
        isTransient: true,
        map() {
            return 'Add New Default Order';
        },
    })
    addNewOrder: ui.fields.Button;

    @ui.decorators.buttonField<NestedGrid>({
        parent() {
            return this.fieldBlock;
        },
        onClick() {
            const orders = this.field.value;
            if (orders.length === 0) {
                this.$.dialog.message(
                    'warn',
                    'Warning',
                    'Could not add a new invoice since the grid has no orders. Retry after adding an order.',
                );
                return;
            }
            const orderId = orders[randomBetween(0, orders.length - 1)]._id;
            this.field.addRecordWithDefaults(1, orderId);
            this.$.showToast(`A new invoice has been added to the order with ID ${orderId}`, { type: 'info' });
        },
        isTransient: true,
        map() {
            return 'Add New Default Invoice';
        },
    })
    addNewInvoice: ui.fields.Button;

    @ui.decorators.buttonField<NestedGrid>({
        parent() {
            return this.fieldBlock;
        },
        onClick() {
            const orders = this.field.value;
            if (orders.length === 0) {
                this.$.dialog.message(
                    'warn',
                    'Warning',
                    'Could not add a new invoice line since the grid has no orders. Retry after adding an order.',
                );
                return;
            }
            const invoiceMap = orders.reduce<any>((orderAcc, o) => {
                return {
                    ...orderAcc,
                    ...o.invoices
                        .map(i => i._id)
                        .reduce<any>((invoiceAcc, i) => {
                            invoiceAcc[i] = o._id;
                            return invoiceAcc;
                        }, {}),
                };
            }, {});
            const allInvoiceIds = Object.keys(invoiceMap);
            if (allInvoiceIds.length === 0) {
                this.$.dialog.message(
                    'warn',
                    'Warning',
                    'Could not add a new invoice line since the grid has no invoices. Retry after adding an invoice to an order.',
                );
                return;
            }
            const invoiceId = allInvoiceIds[randomBetween(0, allInvoiceIds.length - 1)];
            this.field.addRecordWithDefaults(2, invoiceId);
            this.$.showToast(
                `A new invoice line has been added to the invoice with ID ${invoiceId} under order with ID ${invoiceMap[invoiceId]}`,
                { type: 'info' },
            );
        },
        isTransient: true,
        map() {
            return 'Add New Default Invoice Line';
        },
    })
    addNewInvoiceLine: ui.fields.Button;

    @ui.decorators.pageAction<NestedGrid>({
        title: 'Save',
        onClick() {
            this.$.loader.display();
            return this.$.graph
                .update()
                .then(() => {
                    this.$.finish();
                })
                .catch(() => {
                    this.$.showToast('Something went wrong', { type: 'error' });
                })
                .finally(() => {
                    this.$.loader.hide();
                });
        },
    })
    saveNestedGrid: ui.PageAction;

    @ui.decorators.buttonField<NestedGrid>({
        isTransient: true,
        parent() {
            return this.fieldBlock;
        },
        onClick() {
            this.hiddenNestedGrid.isHidden = !this.hiddenNestedGrid.isHidden;
        },
        map() {
            return 'Show hidden nested grid';
        },
    })
    showHiddenGrid: ui.fields.Button;

    @ui.decorators.nestedGridField<NestedGrid, [ShowCaseOrder, ShowCaseInvoice, ShowCaseInvoiceLine]>({
        bind: 'orders',
        levels: [
            {
                node: '@sage/xtrem-show-case/ShowCaseOrder',
                childProperty: 'invoices',
                columns: [
                    ui.nestedFields.text({
                        bind: '_id',
                        title: 'Id',
                        isHiddenDesktop: false,
                        isReadOnly: true,
                    }),
                    ui.nestedFields.date({
                        bind: 'orderDate',
                        title: 'Order Date',
                        canFilter: true,
                    }),
                    ui.nestedFields.dropdownList({
                        bind: 'orderType',
                        canFilter: true,
                        optionType: '@sage/xtrem-show-case/ShowCaseOrderType',
                        title: 'Order Type',
                    }),
                ],
            },
            {
                node: '@sage/xtrem-show-case/ShowCaseInvoice',
                childProperty: 'lines',
                columns: [
                    ui.nestedFields.text({
                        bind: '_id',
                        title: 'Id',
                        isHiddenDesktop: false,
                        isReadOnly: true,
                    }),
                    ui.nestedFields.numeric({
                        bind: 'totalProductQty',
                        title: 'Total Product Quantity',
                        canFilter: true,
                    }),
                    ui.nestedFields.date({
                        bind: 'purchaseDate',
                        title: 'Purchase Date',
                        canFilter: true,
                    }),
                ],
            },
            {
                node: '@sage/xtrem-show-case/ShowCaseInvoiceLine',
                columns: [
                    ui.nestedFields.text({
                        bind: '_id',
                        title: 'Id',
                        isHiddenDesktop: false,
                        isReadOnly: true,
                    }),
                    ui.nestedFields.numeric({
                        bind: 'orderQuantity',
                        title: 'Quantity',
                        scale: 0,
                        canFilter: true,
                    }),
                    ui.nestedFields.numeric({
                        bind: 'netPrice',
                        title: 'Net Price',
                        scale: 2,
                        canFilter: true,
                    }),
                    ui.nestedFields.reference<NestedGrid, ShowCaseInvoiceLine, ShowCaseProduct>({
                        isFilterLimitedToDataset: true,
                        bind: 'product',
                        node: '@sage/xtrem-show-case/ShowCaseProduct',
                        valueField: { product: true },
                        helperTextField: { _id: true },
                        title: 'Product',
                        canFilter: true,
                    }),
                    ui.nestedFields.numeric({
                        bind: 'priceDiscount',
                        title: 'Price Discount',
                        scale: 2,
                    }),
                ],
            },
        ],
        parent() {
            return this.fieldBlock;
        },
        isHidden: true,
        title: 'Hidden by default',
    })
    hiddenNestedGrid: ui.fields.NestedGrid<[ShowCaseOrder, ShowCaseInvoice, ShowCaseInvoiceLine], NestedGrid>;
}
