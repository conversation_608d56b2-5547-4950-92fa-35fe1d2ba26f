import { ShowCaseProvider, ShowCaseProduct } from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import { tableField } from '../menu-items/_index';

@ui.decorators.page<TableOptionsMenu, ShowCaseProvider>({
    authorizationCode: 'TBLOPTMNU',
    module: 'show-case',
    category: 'SHOWCASE',
    mode: 'tabs',
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.text({ bind: 'textField' }),
            titleRight: ui.nestedFields.text({ bind: '_id' }),
        },
    },
    node: '@sage/xtrem-show-case/ShowCaseProvider',
    title: 'Field - Table (Options Menu)',
    menuItem: tableField,
})
export class TableOptionsMenu extends ui.Page {
    @ui.decorators.section<TableOptionsMenu>({
        title: 'Dropdown',
    })
    dropdownSection: ui.containers.Section;

    @ui.decorators.section<TableOptionsMenu>({
        title: 'Tabs',
    })
    tabsSection: ui.containers.Section;

    @ui.decorators.section<TableOptionsMenu>({
        title: 'Toggle',
    })
    toggleSection: ui.containers.Section;

    @ui.decorators.section<TableOptionsMenu>({
        title: 'Dropdown (w/o Search)',
    })
    searchlessDropdownSection: ui.containers.Section;

    @ui.decorators.section<TableOptionsMenu>({
        title: 'Tabs (w/o Search)',
    })
    searchlessTabsSection: ui.containers.Section;

    @ui.decorators.block<TableOptionsMenu>({
        parent() {
            return this.dropdownSection;
        },
    })
    dropdownBlock: ui.containers.Block;

    @ui.decorators.block<TableOptionsMenu>({
        parent() {
            return this.tabsSection;
        },
    })
    tabsBlock: ui.containers.Block;

    @ui.decorators.block<TableOptionsMenu>({
        parent() {
            return this.toggleSection;
        },
    })
    toggleBlock: ui.containers.Block;

    @ui.decorators.block<TableOptionsMenu>({
        parent() {
            return this.searchlessDropdownSection;
        },
    })
    searchlessDropdownBlock: ui.containers.Block;

    @ui.decorators.block<TableOptionsMenu>({
        parent() {
            return this.searchlessTabsSection;
        },
    })
    searchlessTabsBlock: ui.containers.Block;

    @ui.decorators.tableField<TableOptionsMenu, ShowCaseProduct>({
        bind: 'products',
        columns: [
            ui.nestedFields.text({
                bind: '_id',
                title: 'Id',
            }),
            ui.nestedFields.text({
                bind: 'product',
                title: 'Product',
            }),
            ui.nestedFields.numeric({
                bind: 'netPrice',
                title: 'Net Price',
                scale: 2,
            }),
        ],
        fieldActions() {
            return [this.performTask, this.cancelTask];
        },
        hasSearchBoxMobile: true,
        mobileCard: {
            title: ui.nestedFields.label<TableOptionsMenu, ShowCaseProduct>({
                bind: '_id',
                title: 'Id',
            }),
            titleRight: ui.nestedFields.numeric<TableOptionsMenu, ShowCaseProduct>({
                bind: 'netPrice',
                title: 'Net Price',
                scale: 2,
                prefix: '$',
            }),
            line2: ui.nestedFields.text<TableOptionsMenu, ShowCaseProduct>({
                bind: 'product',
                title: 'Product',
            }),
        },
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        optionsMenu: [
            {
                title: 'All',
                graphQLFilter: {},
            },
            {
                title: 'QA Passed',
                graphQLFilter: {
                    category: { _in: ['notBad', 'good', 'great'] },
                },
            },
            {
                title: 'Quality issues',
                graphQLFilter: {
                    category: { _in: ['ok', 'awful'] },
                },
            },
        ],
        optionsMenuType: 'dropdown',
        parent() {
            return this.dropdownBlock;
        },
        title: 'Table with Dropdown Option Menu',
    })
    dropdownField: ui.fields.Table<ShowCaseProduct>;

    @ui.decorators.tableField<TableOptionsMenu, ShowCaseProduct>({
        bind: 'products',
        columns: [
            ui.nestedFields.text({
                bind: '_id',
                title: 'Id',
            }),
            ui.nestedFields.text({
                bind: 'product',
                title: 'Product',
            }),
            ui.nestedFields.numeric({
                bind: 'netPrice',
                title: 'Net Price',
                scale: 2,
            }),
        ],
        fieldActions() {
            return [this.performTask, this.cancelTask];
        },
        hasSearchBoxMobile: true,
        mobileCard: {
            title: ui.nestedFields.label<TableOptionsMenu, ShowCaseProduct>({
                bind: '_id',
                title: 'Id',
            }),
            titleRight: ui.nestedFields.numeric<TableOptionsMenu, ShowCaseProduct>({
                bind: 'netPrice',
                title: 'Net Price',
                scale: 2,
                prefix: '$',
            }),
            line2: ui.nestedFields.text<TableOptionsMenu, ShowCaseProduct>({
                bind: 'product',
                title: 'Product',
            }),
        },
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        optionsMenu: [
            {
                title: 'All',
                graphQLFilter: {},
            },
            {
                title: 'QA Passed',
                graphQLFilter: {
                    category: { _in: ['notBad', 'good', 'great'] },
                },
            },
            {
                title: 'Quality issues',
                graphQLFilter: {
                    category: { _in: ['ok', 'awful'] },
                },
            },
        ],
        optionsMenuType: 'tabs',
        parent() {
            return this.tabsBlock;
        },
        title: 'Table with Tabs Option Menu',
    })
    tabsField: ui.fields.Table<ShowCaseProduct>;

    @ui.decorators.tableField<TableOptionsMenu, ShowCaseProduct>({
        bind: 'products',
        columns: [
            ui.nestedFields.text({
                bind: '_id',
                title: 'Id',
            }),
            ui.nestedFields.text({
                bind: 'product',
                title: 'Product',
            }),
            ui.nestedFields.numeric({
                bind: 'netPrice',
                title: 'Net Price',
                scale: 2,
            }),
        ],
        fieldActions() {
            return [this.performTask, this.cancelTask];
        },
        hasSearchBoxMobile: true,
        mobileCard: {
            title: ui.nestedFields.label<TableOptionsMenu, ShowCaseProduct>({
                bind: '_id',
                title: 'Id',
            }),
            titleRight: ui.nestedFields.numeric<TableOptionsMenu, ShowCaseProduct>({
                bind: 'netPrice',
                title: 'Net Price',
                scale: 2,
                prefix: '$',
            }),
            line2: ui.nestedFields.text<TableOptionsMenu, ShowCaseProduct>({
                bind: 'product',
                title: 'Product',
            }),
        },
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        optionsMenu: [
            {
                title: 'All',
                graphQLFilter: {},
            },
            {
                title: 'QA Passed',
                graphQLFilter: {
                    category: { _in: ['notBad', 'good', 'great'] },
                },
            },
            {
                title: 'Quality issues',
                graphQLFilter: {
                    category: { _in: ['ok', 'awful'] },
                },
            },
        ],
        optionsMenuType: 'toggle',
        parent() {
            return this.toggleBlock;
        },
        title: 'Table with Toggle Option Menu',
    })
    toggleField: ui.fields.Table<ShowCaseProduct>;

    @ui.decorators.tableField<TableOptionsMenu, ShowCaseProduct>({
        bind: 'products',
        columns: [
            ui.nestedFields.text({
                bind: '_id',
                title: 'Id',
            }),
            ui.nestedFields.text({
                bind: 'product',
                title: 'Product',
            }),
            ui.nestedFields.numeric({
                bind: 'netPrice',
                title: 'Net Price',
                scale: 2,
            }),
        ],
        fieldActions() {
            return [this.performTask, this.cancelTask];
        },
        hasSearchBoxMobile: false,
        mobileCard: {
            title: ui.nestedFields.label<TableOptionsMenu, ShowCaseProduct>({
                bind: '_id',
                title: 'Id',
            }),
            titleRight: ui.nestedFields.numeric<TableOptionsMenu, ShowCaseProduct>({
                bind: 'netPrice',
                title: 'Net Price',
                scale: 2,
                prefix: '$',
            }),
            line2: ui.nestedFields.text<TableOptionsMenu, ShowCaseProduct>({
                bind: 'product',
                title: 'Product',
            }),
        },
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        optionsMenu: [
            {
                title: 'All',
                graphQLFilter: {},
            },
            {
                title: 'QA Passed',
                graphQLFilter: {
                    category: { _in: ['notBad', 'good', 'great'] },
                },
            },
            {
                title: 'Quality issues',
                graphQLFilter: {
                    category: { _in: ['ok', 'awful'] },
                },
            },
        ],
        optionsMenuType: 'dropdown',
        parent() {
            return this.searchlessDropdownBlock;
        },
        title: 'Table with Toggle Option Menu',
    })
    searchlessDropdownField: ui.fields.Table<ShowCaseProduct>;

    @ui.decorators.tableField<TableOptionsMenu, ShowCaseProduct>({
        bind: 'products',
        columns: [
            ui.nestedFields.text({
                bind: '_id',
                title: 'Id',
            }),
            ui.nestedFields.text({
                bind: 'product',
                title: 'Product',
            }),
            ui.nestedFields.numeric({
                bind: 'netPrice',
                title: 'Net Price',
                scale: 2,
            }),
        ],
        fieldActions() {
            return [this.performTask, this.cancelTask];
        },
        hasSearchBoxMobile: false,
        mobileCard: {
            title: ui.nestedFields.label<TableOptionsMenu, ShowCaseProduct>({
                bind: '_id',
                title: 'Id',
            }),
            titleRight: ui.nestedFields.numeric<TableOptionsMenu, ShowCaseProduct>({
                bind: 'netPrice',
                title: 'Net Price',
                scale: 2,
                prefix: '$',
            }),
            line2: ui.nestedFields.text<TableOptionsMenu, ShowCaseProduct>({
                bind: 'product',
                title: 'Product',
            }),
        },
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        optionsMenu: [
            {
                title: 'All',
                graphQLFilter: {},
            },
            {
                title: 'QA Passed',
                graphQLFilter: {
                    category: { _in: ['notBad', 'good', 'great'] },
                },
            },
            {
                title: 'Quality issues',
                graphQLFilter: {
                    category: { _in: ['ok', 'awful'] },
                },
            },
        ],
        optionsMenuType: 'tabs',
        parent() {
            return this.searchlessTabsBlock;
        },
        title: 'Table with Toggle Option Menu',
    })
    searchlessTabsField: ui.fields.Table<ShowCaseProduct>;

    @ui.decorators.pageAction<TableOptionsMenu>({
        buttonType: 'secondary',
        icon: 'plus',
        onClick() {
            this.$.showToast('You have performed the task. Good job!');
        },
        title: 'Perform Task',
    })
    performTask: ui.PageAction;

    @ui.decorators.pageAction<TableOptionsMenu>({
        buttonType: 'primary',
        icon: 'bin',
        onClick() {
            this.$.showToast('You have cancelled the current task.');
        },
        title: 'Cancel Task',
    })
    cancelTask: ui.PageAction;
}
