import * as ui from '@sage/xtrem-ui';
import { containers } from '../menu-items/containers';

@ui.decorators.page<Wizard>({
    title: 'Page - Wizard Layout',
    authorizationCode: 'WIZARD',
    module: 'show-case',
    mode: 'wizard',
    category: 'SHOWCASE',
    isTransient: true,
    menuItem: containers,
    businessActions() {
        return [this.$standardCancelAction];
    },
})
export class Wizard extends ui.Page {
    @ui.decorators.section<Wizard>({
        title: 'First section',
        onActive() {
            this.$.showToast('First section active');
        },
        onInactive() {
            this.$.showToast('First section inactive');
        },
        wizardNextButtonLabel: 'This is a custom next label',
        wizardNextButtonType: 'tertiary',
    })
    firstSection: ui.containers.Section;

    @ui.decorators.block<Wizard>({
        parent() {
            return this.firstSection;
        },
    })
    numberBlock: ui.containers.Block;

    @ui.decorators.numericField<Wizard>({
        title: 'Invalid under 10',
        parent() {
            return this.numberBlock;
        },
        min: 10,
        isMandatory: true,
    })
    minValueNumber: ui.fields.Numeric;

    @ui.decorators.section<Wizard>({
        wizardPreviousButtonLabel: 'Custom prev label',
        wizardPreviousButtonType: 'gradient-grey',
        wizardNextButtonType: 'gradient-white',
        title: 'Second section',
        onActive() {
            this.$.showToast('Second section active');
        },
        onInactive() {
            this.$.showToast('Second section inactive');
        },
    })
    secondSection: ui.containers.Section;

    @ui.decorators.block<Wizard>({
        parent() {
            return this.secondSection;
        },
    })
    textBlock: ui.containers.Block;

    @ui.decorators.textField<Wizard>({
        title: 'Mandatory',
        parent() {
            return this.textBlock;
        },
        isMandatory: true,
    })
    mandatoryText: ui.fields.Text;

    @ui.decorators.section<Wizard>({
        title: 'Third section',
        onActive() {
            this.$.showToast('Third section active');
        },
        onInactive() {
            this.$.showToast('Third section inactive');
        },
    })
    thirdSection: ui.containers.Section;

    @ui.decorators.block<Wizard>({
        parent() {
            return this.thirdSection;
        },
    })
    easyBlock: ui.containers.Block;

    @ui.decorators.textField<Wizard>({
        parent() {
            return this.easyBlock;
        },
        title: 'Another text field',
        helperText: 'Write "abc123"',
        validation(v: string) {
            if (v !== 'abc123') {
                return 'Invalid';
            }
            return undefined;
        },
    })
    easyLabel: ui.fields.Text;
}
