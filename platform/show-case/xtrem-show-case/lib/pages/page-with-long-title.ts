import * as ui from '@sage/xtrem-ui';
import { containers } from '../menu-items/containers';

@ui.decorators.page<PageWithLongTitle>({
    category: 'SHOWCASE',
    mode: 'tabs',
    isTransient: true,
    module: 'show-case',
    title: 'Page - With a long title example lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.',
    menuItem: containers,
})
export class PageWithLongTitle extends ui.Page {
    @ui.decorators.section<PageWithLongTitle>({
        title: 'Unspecified Section',
    })
    section1: ui.containers.Section;

    @ui.decorators.block<PageWithLongTitle>({
        parent() {
            return this.section1;
        },
        title: 'First block',
    })
    block1: ui.containers.Block;

    @ui.decorators.block<PageWithLongTitle>({
        parent() {
            return this.section1;
        },
        title: 'Second block',
    })
    block2: ui.containers.Block;
}
