import * as ui from '@sage/xtrem-ui';
import {
    ShowCaseProduct,
    ShowCaseCustomer,
    ShowCaseOrder,
    ShowCaseInvoice,
    ShowCaseInvoiceLine,
} from '@sage/xtrem-show-case-api';
import { validation } from '../menu-items/validation';

@ui.decorators.page<NestedGridValidation, ShowCaseCustomer>({
    authorizationCode: 'BSCFLDS',
    module: 'show-case',
    category: 'SHOWCASE',
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.text({ bind: '_id' }),
            titleLineLeft: ui.nestedFields.text({ bind: 'name' }),
            titleRight: ui.nestedFields.text({ bind: 'email' }),
        },
    },
    node: '@sage/xtrem-show-case/ShowCaseCustomer',
    title: 'Field - NestedGrid Validation',
    businessActions() {
        return [this.$standardCancelAction, this.$standardSaveAction];
    },
    detailPanel() {
        return {
            header: this.detailPanelHeaderSection,
            sections: [this.detailPanelBodySection1],
        };
    },
    menuItem: validation,
})
export class NestedGridValidation extends ui.Page {
    @ui.decorators.section<NestedGridValidation>({})
    detailPanelHeaderSection: ui.containers.Section;

    @ui.decorators.block<NestedGridValidation>({
        parent() {
            return this.detailPanelHeaderSection;
        },
    })
    detailPanelHeaderBlock: ui.containers.Block;

    @ui.decorators.section<NestedGridValidation>({
        title: 'info',
    })
    detailPanelBodySection1: ui.containers.Section;

    @ui.decorators.block<NestedGridValidation>({
        parent() {
            return this.detailPanelBodySection1;
        },
        title: 'First section block',
    })
    detailPanelBlock1: ui.containers.Block;

    @ui.decorators.gridRowBlock<NestedGridValidation>({
        parent() {
            return this.detailPanelBodySection1;
        },
        title: 'Second section block',
        boundTo() {
            return this.field as any;
        },
    })
    demoGridRowBlock: ui.containers.GridRowBlock;

    @ui.decorators.section<NestedGridValidation>({
        title: 'Nested Grid With Validation',
        isTitleHidden: true,
    })
    section: ui.containers.Section;

    @ui.decorators.block<NestedGridValidation>({
        parent() {
            return this.section;
        },
        title: 'Nested Grid With Validation',
    })
    fieldBlock: ui.containers.Block;

    @ui.decorators.nestedGridField<NestedGridValidation, [ShowCaseOrder, ShowCaseInvoice, ShowCaseInvoiceLine]>({
        bind: 'orders',
        title: 'Test field with validation',
        onRowClick(recordId, rowItem, level) {
            this.demoGridRowBlock.selectRowAndLevel(recordId, level);
        },
        levels: [
            {
                node: '@sage/xtrem-show-case/ShowCaseOrder',
                childProperty: 'invoices',
                columns: [
                    ui.nestedFields.text({
                        bind: '_id',
                        title: 'Id',
                        isHiddenDesktop: false,
                        isReadOnly: true,
                    }),
                    ui.nestedFields.date({
                        bind: 'orderDate',
                        title: 'Order Date',
                        isMandatory: true,
                        canFilter: true,
                    }),
                ],
            },
            {
                node: '@sage/xtrem-show-case/ShowCaseInvoice',
                childProperty: 'lines',
                orderBy: { _id: 1 },
                columns: [
                    ui.nestedFields.text({
                        bind: '_id',
                        title: 'Id',
                        isHiddenDesktop: false,
                        isReadOnly: true,
                    }),
                    ui.nestedFields.numeric({
                        bind: 'totalProductQty',
                        title: 'Total Product Quantity',
                        min: 0,
                        canFilter: true,
                    }),
                    ui.nestedFields.date({
                        bind: 'purchaseDate',
                        title: 'Purchase Date',
                        canFilter: true,
                    }),
                ],
            },
            {
                node: '@sage/xtrem-show-case/ShowCaseInvoiceLine',
                columns: [
                    ui.nestedFields.text({
                        bind: '_id',
                        title: 'Id',
                        isHiddenDesktop: false,
                        isReadOnly: true,
                    }),
                    ui.nestedFields.numeric({
                        bind: 'orderQuantity',
                        title: 'Quantity',
                        scale: 0,
                        min: 0,
                        canFilter: true,
                    }),
                    ui.nestedFields.numeric({
                        bind: 'netPrice',
                        title: 'Net Price',
                        scale: 2,
                        canFilter: true,
                    }),
                    ui.nestedFields.reference<NestedGridValidation, ShowCaseInvoiceLine, ShowCaseProduct>({
                        bind: 'product',
                        node: '@sage/xtrem-show-case/ShowCaseProduct',
                        valueField: { product: true },
                        helperTextField: { _id: true },
                        isMandatory: true,
                        title: 'Product',
                        canFilter: true,
                    }),
                ],
            },
        ],
        parent() {
            return this.fieldBlock;
        },
    })
    field: ui.fields.NestedGrid<[ShowCaseOrder, ShowCaseInvoice, ShowCaseInvoiceLine], NestedGridValidation>;
}
