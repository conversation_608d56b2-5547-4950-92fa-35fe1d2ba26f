import { MetaNodeFactory } from '@sage/xtrem-metadata-api';
import { Dict, Property } from '@sage/xtrem-shared';
import * as ui from '@sage/xtrem-ui';
import { MonacoPluginProperties } from '@sage/xtrem-ui-plugin-monaco';
import { fields } from '../menu-items/fields';

@ui.decorators.page<ContentTable>({
    menuItem: fields,
    title: 'Content Table',
    subtitle: 'Fields',
    isTransient: true,
    headerSection() {
        return this.headerSection;
    },
})
export class ContentTable extends ui.Page {
    @ui.decorators.section<ContentTable>({
        title: 'ContentTable',
        isTitleHidden: true,
    })
    headerSection: ui.containers.Section;

    @ui.decorators.section<ContentTable>({
        title: 'ContentTable field',
        isTitleHidden: true,
    })
    section: ui.containers.Section;

    @ui.decorators.block<ContentTable>({
        parent() {
            return this.headerSection;
        },
        title: 'Introduction',
    })
    introductionBlock: ui.containers.Block;

    @ui.decorators.staticContentField<ContentTable>({
        parent() {
            return this.introductionBlock;
        },
        isTitleHidden: true,
        width: 'large',
        content:
            'The content table field is meant for usage in wizards for example for data export and import. It can be bound to a JSON property on the server.',
    })
    description: ui.fields.StaticContent;

    @ui.decorators.linkField<ContentTable>({
        parent() {
            return this.introductionBlock;
        },
        title: 'Source code',
        width: 'medium',
        map() {
            return ui.localize('@sage/xtrem-show-case/check-source-code', 'Check it on GitHub');
        },
        page: 'https://github.com/Sage-ERP-X3/xtrem/blob/master/platform/show-case/xtrem-show-case/lib/pages/content-table.ts',
    })
    sourceCodeLink: ui.fields.Link;

    @ui.decorators.block<ContentTable>({
        parent() {
            return this.headerSection;
        },
        title: 'Field example',
        width: 'extra-large',
    })
    fieldBlock: ui.containers.Block;

    @ui.decorators.contentTable<ContentTable>({
        parent() {
            return this.fieldBlock;
        },
        onClick() {
            this.clickTriggered.isHidden = false;
            setTimeout(() => {
                this.clickTriggered.isHidden = true;
            }, 5000);
        },
        isFullWidth: true,
        node: 'ShowCaseEmployee',
        selectedProperties: {
            lastName: {
                label: 'Last name',
                data: {
                    type: 'String',
                    kind: 'SCALAR',
                    isCollection: false,
                    name: 'lastName',
                    canFilter: true,
                    canSort: true,
                    label: 'Last name',
                    enumType: null,
                    isCustom: false,
                    dataType: '',
                    targetNode: '',
                    isStored: false,
                    isOnInputType: false,
                    isOnOutputType: false,
                    isMutable: false,
                },
                id: 'lastName',
                key: 'lastName',
                labelKey: 'Last name',
                labelPath: 'Last name',
            },
            firstName: {
                label: 'First name',
                data: {
                    type: 'String',
                    kind: 'SCALAR',
                    isCollection: false,
                    name: 'firstName',
                    canFilter: true,
                    canSort: true,
                    label: 'First name',
                    enumType: null,
                    isCustom: false,
                    dataType: '',
                    targetNode: '',
                    isStored: false,
                    isOnInputType: false,
                    isOnOutputType: false,
                    isMutable: false,
                },
                id: 'firstName',
                key: 'firstName',
                labelKey: 'First name',
                labelPath: 'First name',
            },
            email: {
                label: 'Email',
                data: {
                    type: 'String',
                    kind: 'SCALAR',
                    isCollection: false,
                    name: 'email',
                    canFilter: true,
                    canSort: true,
                    label: 'Email',
                    enumType: null,
                    isCustom: false,
                    dataType: '',
                    targetNode: '',
                    isStored: false,
                    isOnInputType: false,
                    isOnOutputType: false,
                    isMutable: false,
                },
                id: 'email',
                key: 'email',
                labelKey: 'Email',
                labelPath: 'Email',
            },
            city: {
                label: 'City',
                data: {
                    type: 'String',
                    kind: 'SCALAR',
                    isCollection: false,
                    name: 'city',
                    canFilter: true,
                    canSort: true,
                    label: 'City',
                    enumType: null,
                    isCustom: false,
                    dataType: '',
                    targetNode: '',
                    isStored: false,
                    isOnInputType: false,
                    isOnOutputType: false,
                    isMutable: false,
                },
                id: 'city',
                key: 'city',
                labelKey: 'City',
                labelPath: 'City',
            },
        },
        onChange() {
            this.valueField.value = JSON.stringify(this.field.value || {}, null, 4);
            this.changeTriggered.isHidden = false;
            setTimeout(() => {
                this.changeTriggered.isHidden = true;
            }, 5000);
        },
    })
    field: ui.fields.ContentTable;

    @ui.decorators.labelField<ContentTable>({
        parent() {
            return this.fieldBlock;
        },
        isHidden: true,
        map() {
            return 'Change was triggered';
        },
    })
    changeTriggered: ui.fields.Label;

    @ui.decorators.labelField<ContentTable>({
        parent() {
            return this.fieldBlock;
        },
        isHidden: true,
        map() {
            return 'Click was triggered';
        },
    })
    clickTriggered: ui.fields.Label;

    @ui.decorators.block<ContentTable>({
        parent() {
            return this.section;
        },
        title: 'Configuration',
    })
    configurationBlock: ui.containers.Block;

    @ui.decorators.checkboxField<ContentTable>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is read only',
        helperText:
            'Whether the field is editable (isReadOnly = false) or not (isReadOnly = true). The difference with disabled is that isReadOnly suggests that the field is never editable. It can be defined as a boolean, or conditionally by a callback that returns a boolean.',
        isFullWidth: true,
        isReversed: true,
        onChange() {
            this.field.isReadOnly = !!this.isReadOnly.value;
        },
    })
    isReadOnly: ui.fields.Checkbox;

    @ui.decorators.checkboxField<ContentTable>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is hidden',
        helperText: 'Determines whether the field is displayed or not.',
        isFullWidth: true,
        isReversed: true,
        onChange() {
            this.field.isHidden = !!this.isHidden.value;
        },
    })
    isHidden: ui.fields.Checkbox;

    @ui.decorators.textField<ContentTable>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Title',
        isFullWidth: true,
        helperText:
            'The title that is displayed above the field. The title can be provided as a string, or a callback function returning a string. When declared as a callback within the column of a nested grid, the column id is provided as a parameter. It is automatically picked up by the i18n engine and externalized for translation.',
        onChange() {
            this.field.title = this.title.value || '';
        },
    })
    title: ui.fields.Text;

    @ui.decorators.checkboxField<ContentTable>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is title hidden',
        isFullWidth: true,
        helperText: 'Whether the field title above the field should be displayed and its vertical space preserved.',
        isReversed: true,
        onChange() {
            this.field.isTitleHidden = !!this.isTitleHidden.value;
        },
    })
    isTitleHidden: ui.fields.Checkbox;

    @ui.decorators.textField<ContentTable>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Helper text',
        helperText:
            'The helper text that is displayed above the field. It is automatically picked up by the i18n engine and externalized.',
        isFullWidth: true,
        onChange() {
            this.field.helperText = this.helperText.value || '';
        },
    })
    helperText: ui.fields.Text;

    @ui.decorators.checkboxField<ContentTable>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is mandatory',
        helperText:
            ' Makes the field mandatory, empty values will raise an error message. It can also be defined as callback function that returns a boolean.',
        isFullWidth: true,
        isReversed: true,
        onChange() {
            this.field.isMandatory = !!this.isMandatory.value;
        },
    })
    isMandatory: ui.fields.Checkbox;

    @ui.decorators.block<ContentTable>({
        parent() {
            return this.section;
        },
        title: 'Events',
    })
    eventsBlock: ui.containers.Block;

    @ui.decorators.staticContentField<ContentTable>({
        parent() {
            return this.eventsBlock;
        },
        isTitleHidden: true,
        isFullWidth: true,
        isMarkdown: true,
        content:
            '-   **onClick**: Triggered when any parts of the field is clicked, no arguments provided.\n-   **onChange**: Triggered when the field value changed and the focus is about to move away from the field, no arguments provided.\n-   **onError**: Handles errors thrown from the callback functions\n-   **onInputValueChange**: Triggered when the user stops typing into the input of the field. The execution is debounced by 150ms to cater for continuos typing.',
    })
    eventsDescription: ui.fields.StaticContent;

    @ui.decorators.block<ContentTable>({
        parent() {
            return this.section;
        },
        title: 'Runtime functions',
    })
    runtimeFunctionsBlock: ui.containers.Block;

    @ui.decorators.staticContentField<ContentTable>({
        parent() {
            return this.runtimeFunctionsBlock;
        },
        isTitleHidden: true,
        isFullWidth: true,
        isMarkdown: true,
        content:
            "-   **getNextField(isFocusable)**: Returns the next field instance. The order is calculated by the page prototype. If the isFocusable argument is set to true, it returns the next visible, enabled and non read-only field. It only considers the committed page state, so `commitValueAndPropertyChanges` call might be required beforehand to get the expected result.\n-   **refresh()**: Refetches the field's value from the server and updates it on the screen, only for non-transient pages.\n-   **validate()**: Triggers the field validation rules. Since the validation rules might be asynchronous, this method returns a promise that must be awaited to get the validation result\n-   **validateWithDetails()**: In addition to the functionality of `validate` it returns more details, including the rule that failed and where applicable, the row ID and colum ID.\n-   **fetchDefault(skipSet)**: Force re-fetches default value for the field. If the `skipSet` flag is set to true, it returns the default values but not apply them to the screen.\n-   **isDirty()**: Sets or gets the dirty state of the field.",
    })
    runtimeFunctionDescription: ui.fields.StaticContent;

    /* Additional examples */
    @ui.decorators.block<ContentTable>({
        parent() {
            return this.section;
        },
        title: 'Value',
    })
    valueBlock: ui.containers.Block;

    @ui.decorators.pluginField<ContentTable, MonacoPluginProperties>({
        parent() {
            return this.valueBlock;
        },
        title: 'Value',
        helperText: 'Value of the filter component in JSON representation',
        isFullWidth: true,
        pluginPackage: '@sage/xtrem-ui-plugin-monaco',
        language: 'json',
        height: 400,
        isReadOnly: true,
    })
    valueField: ui.fields.Plugin<MonacoPluginProperties>;

    @ui.decorators.buttonField<ContentTable>({
        parent() {
            return this.valueBlock;
        },
        title: 'Reset value',
        isTitleHidden: true,
        map() {
            return 'Reset value';
        },
        onClick() {
            this.field.value = null;
            this.valueField.value = null;
        },
    })
    resetFieldValue: ui.fields.Button;

    @ui.decorators.referenceField<ContentTable, MetaNodeFactory>({
        parent() {
            return this.configurationBlock;
        },
        orderBy: {
            name: 1,
        },
        columns: [
            ui.nestedFields.text({ bind: 'name', title: 'Record type', isHidden: true }),
            ui.nestedFields.text({ bind: 'title', title: 'Node name' }),
            ui.nestedFields.text({
                bind: { package: { name: true } }, // 'package',
                title: 'Package',
                isHidden: true,
            }),
        ],
        node: '@sage/xtrem-metadata/MetaNodeFactory',
        title: 'Node',
        valueField: 'title',
        helperTextField: { package: { name: true } },
        minLookupCharacters: 0,
        isAutoSelectEnabled: true,
        isTransient: true,
        async onChange() {
            if (this.contentNode.value) {
                this.field.node = this.contentNode.value.name;
                this.fieldBlock.isHidden = true;
                const properties = await ui.fetchNodeDetails({
                    nodeName: this.contentNode.value.name,
                    locale: this.$.locale,
                });

                this.field.selectedProperties = Object.keys(properties).reduce((prevValue: Dict<Property>, r) => {
                    const data = properties[r];
                    prevValue[data.name] = {
                        data,
                        id: data.name,
                        key: data.name,
                        labelKey: data.label,
                        labelPath: data.label,
                        label: data.label,
                    };
                    return prevValue;
                }, {});
                this.field.value = [];
                this.fieldBlock.isHidden = false;
            }
        },
    })
    contentNode: ui.fields.Reference;
}
