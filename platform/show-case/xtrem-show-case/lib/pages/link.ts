import * as ui from '@sage/xtrem-ui';
import { fields } from '../menu-items/fields';

interface Parameter {
    _id: string;
    name: string;
    value: string | number;
}

@ui.decorators.page<Link>({
    authorizationCode: 'BSCFLDS',
    module: 'show-case',
    title: 'Field - Link',
    isTransient: true,
    category: 'SHOWCASE',
    onLoad() {
        this.field.value = 'Link value';
        this.value.value = 'Link value';
    },
    menuItem: fields,
})
export class Link extends ui.Page {
    @ui.decorators.section<Link>({
        title: 'Link field',
    })
    section: ui.containers.Section;

    @ui.decorators.block<Link>({
        parent() {
            return this.section;
        },
        title: 'Field example',
    })
    fieldBlock: ui.containers.Block;

    @ui.decorators.linkField<Link>({
        page: '@sage/xtrem-show-case/Link',
        parent() {
            return this.fieldBlock;
        },
        map(value) {
            return this.map.value || value;
        },
    })
    field: ui.fields.Link;

    @ui.decorators.block<Link>({
        parent() {
            return this.section;
        },
        title: 'Configuration',
    })
    configurationBlock: ui.containers.Block;

    @ui.decorators.textField<Link>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Helper text',
        onChange() {
            this.field.helperText = this.helperText.value;
        },
    })
    helperText: ui.fields.Text;

    @ui.decorators.checkboxField<Link>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is disabled',
        onChange() {
            this.field.isDisabled = this.isDisabled.value;
        },
    })
    isDisabled: ui.fields.Checkbox;

    @ui.decorators.checkboxField<Link>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is helper text hidden',
        onChange() {
            this.field.isHelperTextHidden = this.isHelperTextHidden.value;
        },
    })
    isHelperTextHidden: ui.fields.Checkbox;

    @ui.decorators.checkboxField<Link>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is hidden',
        onChange() {
            this.field.isHidden = this.isHidden.value;
        },
    })
    isHidden: ui.fields.Checkbox;

    @ui.decorators.checkboxField<Link>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is title hidden',
        onChange() {
            this.field.isTitleHidden = this.isTitleHidden.value;
        },
    })
    isTitleHidden: ui.fields.Checkbox;

    @ui.decorators.textField<Link>({
        parent() {
            return this.configurationBlock;
        },
        onChange() {
            // To update the link field, we need to trigger a change on the value
            const fieldValue = this.field.value;
            this.field.value = String(new Date().getTime());
            setTimeout(() => {
                this.field.value = fieldValue;
            }, 0);
        },
        title: 'Map value',
    })
    map: ui.fields.Text;

    @ui.decorators.textField<Link>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Page',
        onChange() {
            this.field.page = this.page.value;
        },
    })
    page: ui.fields.Text;

    @ui.decorators.tableField<Link>({
        columns: [
            ui.nestedFields.text({ bind: 'name', title: 'Name' }),
            ui.nestedFields.text({ bind: 'value', title: 'Value' }),
        ],
        parent() {
            return this.configurationBlock;
        },
        onChange() {},
        title: 'Parameters',
    })
    parameters: ui.fields.Table<Parameter>;

    @ui.decorators.textField<Link>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Title',
        onChange() {
            this.field.title = this.title.value;
        },
    })
    title: ui.fields.Text;

    @ui.decorators.textField<Link>({
        onChange() {
            this.field.value = this.value.value;
        },
        parent() {
            return this.configurationBlock;
        },
        title: 'Value',
    })
    value: ui.fields.Text;

    @ui.decorators.buttonField<Link>({
        parent() {
            return this.configurationBlock;
        },
        map() {
            return 'Focus field';
        },
        onClick() {
            this.field.focus();
        },
    })
    focus: ui.fields.Button;

    /* Additional examples */

    @ui.decorators.block<Link>({
        parent() {
            return this.section;
        },
        title: 'Additional examples',
    })
    additionalBlock: ui.containers.Block;

    @ui.decorators.linkField<Link>({
        title: 'Custom onClick',
        parent() {
            return this.additionalBlock;
        },
        map() {
            return 'Open sesame!';
        },
        onClick() {
            this.$.dialog.message('info', 'Custom on click', 'You can do almost everything from this onClick handler');
        },
    })
    customOnClick: ui.fields.Link;

    @ui.decorators.linkField<Link>({
        title: 'Open external link',
        parent() {
            return this.additionalBlock;
        },
        map() {
            return 'Open in new tab';
        },
        onClick() {
            this.$.router.goToExternal('https://www.google.com');
        },
    })
    openExternalLink: ui.fields.Link;

    @ui.decorators.linkField<Link>({
        title: 'Open internal link',
        parent() {
            return this.additionalBlock;
        },
        map() {
            return 'Open in new tab';
        },
        onClick() {
            this.$.router.goToExternal('@sage/xtrem-show-case/ShowCaseProduct');
        },
    })
    openInternalLink: ui.fields.Link;
}
