import { GraphApi, ShowCaseProduct } from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import { misc } from '../menu-items/misc';

@ui.decorators.page<CustomLookupDialog>({
    menuItem: misc,
    isTransient: true,
    title: 'Dialog - Custom Lookup',
})
export class CustomLookupDialog extends ui.Page<GraphApi> {
    @ui.decorators.section<CustomLookupDialog>({
        isTitleHidden: true,
    })
    section: ui.containers.Section;

    @ui.decorators.block<CustomLookupDialog>({
        parent() {
            return this.section;
        },
        isTitleHidden: true,
    })
    block: ui.containers.Block;

    @ui.decorators.buttonField<CustomLookupDialog>({
        parent() {
            return this.block;
        },
        isFullWidth: true,
        async onClick() {
            const result = await this.$.dialog.lookup<ShowCaseProduct>({
                node: '@sage/xtrem-show-case/ShowCaseProduct',
            });
            ui.console.log(result);
            this.$.showToast(`Selected ${result.length} products\n${result.map(p => `\n- ${p.product}`).join('\n')}`, {
                language: 'markdown',
            });
        },
        map() {
            return 'Open lookup';
        },
        title: 'Single selection, auto-configured',
    })
    openSingleSelectionLookupAutoConfigured: ui.fields.Button;

    @ui.decorators.buttonField<CustomLookupDialog>({
        parent() {
            return this.block;
        },
        isFullWidth: true,
        async onClick() {
            const result = await this.$.dialog.lookup<ShowCaseProduct>({
                node: '@sage/xtrem-show-case/ShowCaseProduct',
                isMultiSelect: true,
            });
            ui.console.log(result);
            this.$.showToast(`Selected ${result.length} products\n${result.map(p => `\n- ${p.product}`).join('\n')}`, {
                language: 'markdown',
            });
        },
        map() {
            return 'Open lookup';
        },
        title: 'Multi-selection, auto-configured',
    })
    openMultiSelectionLookupAutoConfigured: ui.fields.Button;

    @ui.decorators.buttonField<CustomLookupDialog>({
        parent() {
            return this.block;
        },
        isFullWidth: true,
        async onClick() {
            const result = await this.$.dialog.lookup<ShowCaseProduct>({
                node: '@sage/xtrem-show-case/ShowCaseProduct',
                columns: [
                    ui.nestedFields.text({ bind: '_id' }),
                    ui.nestedFields.text({ bind: 'product' }),
                    ui.nestedFields.text({ bind: 'description' }),
                    ui.nestedFields.checkbox({ bind: 'hotProduct' }),
                    ui.nestedFields.select({ bind: 'category' }),
                ],
            });
            ui.console.log(result);
            this.$.showToast(`Selected ${result.length} products\n${result.map(p => `\n- ${p.product}`).join('\n')}`, {
                language: 'markdown',
            });
        },
        map() {
            return 'Open lookup';
        },
        title: 'Single selection, custom columns configured',
    })
    openSingleSelectionLookupWithColumns: ui.fields.Button;

    @ui.decorators.buttonField<CustomLookupDialog>({
        parent() {
            return this.block;
        },
        isFullWidth: true,
        async onClick() {
            const result = await this.$.dialog.lookup<ShowCaseProduct>({
                acceptButton: { text: 'Select and add to order' },
                dialogTitle: 'Add products to your order',
                node: '@sage/xtrem-show-case/ShowCaseProduct',
                isEditable: true,
                isMultiSelect: true,
                filter: {
                    provider: { textField: 'Amazon' },
                    netPrice: { _gt: '15' },
                },
                orderBy: {
                    product: 1,
                },
                columns: [
                    ui.nestedFields.text({ bind: '_id', isReadOnly: true, title: 'ID' }),
                    ui.nestedFields.text({ bind: 'product', isReadOnly: true, title: 'Product' }),
                    ui.nestedFields.text({
                        bind: { provider: { textField: true } },
                        isReadOnly: true,
                        title: 'Description',
                    }),
                    ui.nestedFields.numeric({ bind: 'netPrice', title: 'Price', min: 1, max: 500 }),
                ],
            });
            ui.console.log(result);
            this.$.showToast(
                `Selected ${result.length} products\n${result.map(p => `\n- ${p.product} - ${p.netPrice}`).join('\n')}`,
                { language: 'markdown' },
            );
        },
        map() {
            return 'Open lookup';
        },
        title: 'Multi-selection, custom columns configured, editable',
    })
    openMultiSelectionLookupWithEditableColumns: ui.fields.Button;

    @ui.decorators.buttonField<CustomLookupDialog>({
        parent() {
            return this.block;
        },
        isFullWidth: true,
        async onClick() {
            const result = await this.$.dialog.lookup<ShowCaseProduct>({
                acceptButton: { text: 'Select and add to order' },
                dialogTitle: 'Add products to your order',
                node: '@sage/xtrem-show-case/ShowCaseProduct',
                mapServerRecord(record) {
                    record.netPrice += 1000;
                    return record;
                },
                columns: [
                    ui.nestedFields.text({ bind: '_id', isReadOnly: true, title: 'ID' }),
                    ui.nestedFields.text({ bind: 'product', isReadOnly: true, title: 'Product' }),
                    ui.nestedFields.text({
                        bind: { provider: { textField: true } },
                        isReadOnly: true,
                        title: 'Description',
                    }),
                    ui.nestedFields.numeric({ bind: 'netPrice', title: 'Price' }),
                ],
            });
            ui.console.log(result);
            this.$.showToast(
                `Selected ${result.length} products\n${result.map(p => `\n- ${p.product} - ${p.netPrice}`).join('\n')}`,
                { language: 'markdown' },
            );
        },
        map() {
            return 'Open lookup';
        },
        title: 'Single selection with remapped data',
    })
    openRemappedLookupData: ui.fields.Button;

    @ui.decorators.buttonField<CustomLookupDialog>({
        parent() {
            return this.block;
        },
        isFullWidth: true,
        async onClick() {
            const result = await this.$.dialog.lookup<ShowCaseProduct>({
                node: '@sage/xtrem-show-case/ShowCaseProduct',
                selectedRecordId: '5',
                orderBy: {
                    _id: 1,
                },
            });
            ui.console.log(result);
            this.$.showToast(`Selected ${result.length} products\n${result.map(p => `\n- ${p.product}`).join('\n')}`, {
                language: 'markdown',
            });
        },
        map() {
            return 'Open lookup';
        },
        title: 'Single selection, set selected item',
    })
    openSingleSelectionLookupAutoConfiguredPreSelected: ui.fields.Button;
}
