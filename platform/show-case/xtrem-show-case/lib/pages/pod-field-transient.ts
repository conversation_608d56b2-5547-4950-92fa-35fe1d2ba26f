import { <PERSON><PERSON>h<PERSON><PERSON>, ShowCaseProvider, ShowCaseProduct } from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import { pod } from '../menu-items/pod';

@ui.decorators.page<PodFieldTransient, ShowCaseProduct>({
    authorizationCode: 'SHCPRVD',
    category: 'SHOWCASE',
    menuItem: pod,
    module: 'show-case',
    isTransient: true,
    title: 'Pod as a field (transient page)',
})
export class PodFieldTransient extends ui.Page<GraphApi> {
    @ui.decorators.section<PodFieldTransient>({})
    mainSection: ui.containers.Section;

    @ui.decorators.block<PodFieldTransient>({
        parent() {
            return this.mainSection;
        },
    })
    block: ui.containers.Block;

    @ui.decorators.textField<PodFieldTransient>({
        parent() {
            return this.block;
        },
        title: 'Id',
        width: 'medium',
    })
    _id: ui.fields.Text;

    @ui.decorators.podField<PodFieldTransient, ShowCaseProvider>({
        title: 'Provider',
        width: 'large',
        parent() {
            return this.block;
        },
        onClick() {
            this.clickTriggered.isHidden = false;
            setTimeout(() => {
                this.clickTriggered.isHidden = true;
            }, 5000);
        },
        onChange() {
            this.changeTriggered.isHidden = false;
            setTimeout(() => {
                this.changeTriggered.isHidden = true;
            }, 5000);
        },
        onError() {
            ui.console.error('just testing onError works.');
        },
        columns: [
            ui.nestedFields.text({ bind: 'textField', title: 'Text field' }),
            ui.nestedFields.text({ bind: 'integerField', title: 'Numeric field' }),
            ui.nestedFields.text({ bind: '_id', title: 'id' }),
            ui.nestedFields.text({ bind: 'dateField', title: 'date field' }),
        ],
        node: '@sage/xtrem-show-case/ShowCaseProvider',
    })
    provider: ui.fields.Pod;

    @ui.decorators.pageAction<PodFieldTransient>({
        async onClick() {
            await this.$.graph.create();
            this.$.dialog.message('info', 'Mutation Create', `Created entry: ${this._id.value}`, {
                fullScreen: false,
                rightAligned: false,
                acceptButton: {
                    isDisabled: false,
                    isHidden: false,
                    text: 'OK',
                },
            });
        },
    })
    create: ui.PageAction;

    @ui.decorators.pageAction<PodFieldTransient>({
        async onClick() {
            await this.$.graph.update();
            this.$.dialog.message('info', 'Mutation Update', `Updated entry: ${this._id.value}`, {
                fullScreen: false,
                rightAligned: false,
                acceptButton: {
                    isDisabled: false,
                    isHidden: false,
                    text: 'OK',
                },
            });
        },
    })
    update: ui.PageAction;

    @ui.decorators.pageAction<PodFieldTransient>({
        async onClick() {
            await this.$.graph.delete();
            this.$.router.goTo('@sage/xtrem-show-case/BoundPage');
        },
    })
    delete: ui.PageAction;

    @ui.decorators.pageAction<PodFieldTransient>({
        title: 'Toggle Navigation Panel',
        async onClick() {
            this.$.isNavigationPanelHidden = !this.$.isNavigationPanelHidden;
        },
    })
    toggleNavigationPanel: ui.PageAction;

    /** Testing field buttons */

    @ui.decorators.labelField<PodFieldTransient>({
        parent() {
            return this.block;
        },
        isTransient: true,
        isHidden: true,
        map() {
            return 'Change was triggered';
        },
    })
    changeTriggered: ui.fields.Label;

    @ui.decorators.labelField<PodFieldTransient>({
        parent() {
            return this.block;
        },
        isTransient: true,
        isHidden: true,
        map() {
            return 'Click was triggered';
        },
    })
    clickTriggered: ui.fields.Label;

    @ui.decorators.labelField<PodFieldTransient>({
        parent() {
            return this.block;
        },
        isTransient: true,
        isHidden: true,
        map() {
            return 'Add Click was triggered';
        },
    })
    addClickTriggered: ui.fields.Label;

    @ui.decorators.section<PodFieldTransient>({
        isTitleHidden: true,
    })
    configurationSection: ui.containers.Section;

    @ui.decorators.block<PodFieldTransient>({
        parent() {
            return this.configurationSection;
        },
        title: 'Configuration',
        isTransient: true,
    })
    configurationBlock: ui.containers.Block;

    @ui.decorators.textField<PodFieldTransient>({
        parent() {
            return this.configurationBlock;
        },
        isTransient: true,
        title: 'Helper text',
        onChange() {
            this.provider.helperText = this.helperText.value;
        },
    })
    helperText: ui.fields.Text;

    @ui.decorators.checkboxField<PodFieldTransient>({
        parent() {
            return this.configurationBlock;
        },
        isTransient: true,
        title: 'Is disabled',
        onChange() {
            this.provider.isDisabled = this.isDisabled.value;
        },
    })
    isDisabled: ui.fields.Checkbox;

    @ui.decorators.checkboxField<PodFieldTransient>({
        parent() {
            return this.configurationBlock;
        },
        isTransient: true,
        title: 'Is removable',
        onChange() {
            this.provider.canRemove = this.isRemovable.value;
        },
    })
    isRemovable: ui.fields.Checkbox;

    @ui.decorators.checkboxField<PodFieldTransient>({
        parent() {
            return this.configurationBlock;
        },
        isTransient: true,
        title: 'Is helper text hidden',
        onChange() {
            this.provider.isHelperTextHidden = this.isHelperTextHidden.value;
        },
    })
    isHelperTextHidden: ui.fields.Checkbox;

    @ui.decorators.checkboxField<PodFieldTransient>({
        parent() {
            return this.configurationBlock;
        },
        isTransient: true,
        title: 'Is hidden',
        onChange() {
            this.provider.isHidden = this.isHidden.value;
        },
    })
    isHidden: ui.fields.Checkbox;

    @ui.decorators.checkboxField<PodFieldTransient>({
        parent() {
            return this.configurationBlock;
        },
        isTransient: true,
        title: 'Is readOnly',
        onChange() {
            this.provider.isReadOnly = this.isReadOnly.value;
        },
    })
    isReadOnly: ui.fields.Checkbox;

    @ui.decorators.checkboxField<PodFieldTransient>({
        parent() {
            return this.configurationBlock;
        },
        isTransient: true,
        title: 'Is title hidden',
        onChange() {
            this.provider.isTitleHidden = this.isTitleHidden.value;
        },
    })
    isTitleHidden: ui.fields.Checkbox;

    @ui.decorators.textField<PodFieldTransient>({
        parent() {
            return this.configurationBlock;
        },
        isTransient: true,
        title: 'Title',
        onChange() {
            this.provider.title = this.title.value;
        },
    })
    title: ui.fields.Text;

    @ui.decorators.buttonField<PodFieldTransient>({
        parent() {
            return this.configurationBlock;
        },
        isTransient: true,
        map() {
            return 'Focus field';
        },
        onClick() {
            this.provider.focus();
        },
    })
    focus: ui.fields.Button;

    @ui.decorators.buttonField<PodFieldTransient>({
        parent() {
            return this.configurationBlock;
        },
        isTransient: true,
        map() {
            return 'Open Reference Lookup dialog for provider field';
        },
        onClick() {
            this.provider.openLookupDialog();
        },
    })
    openDialog: ui.fields.Button;

    @ui.decorators.podField<PodFieldTransient, ShowCaseProduct>({
        parent() {
            return this.block;
        },

        node: '@sage/xtrem-show-case/ShowCaseProduct',
        title: 'Flagship Product!',
        columns: [
            ui.nestedFields.text({ bind: 'product', title: 'Product Name' }),
            ui.nestedFields.multiDropdown({ bind: 'entries', title: 'Entries' }),
        ],
    })
    flagshipProduct: ui.fields.Pod;
}
