import * as ui from '@sage/xtrem-ui';
import { misc } from '../menu-items/misc';

@ui.decorators.page<ShortcutsDialog>({
    authorizationCode: 'HLPRPNL',
    module: 'show-case',
    isTransient: true,
    title: '',
    menuItem: misc,
    onLoad() {
        this.infoText.value =
            'If a dialog is opened over the screen, the dialog shortcuts should take precedence. Try Shift+Alt+P';
        this.$.page.title = 'Dialog with shortcuts';
    },
})
export class ShortcutsDialog extends ui.Page {
    @ui.decorators.section<ShortcutsDialog>({
        isTitleHidden: true,
    })
    section1: ui.containers.Section;

    @ui.decorators.block<ShortcutsDialog>({
        title: 'This is supposed to be opened in a page dialog',
        parent() {
            return this.section1;
        },
    })
    block1: ui.containers.Block;

    @ui.decorators.buttonField<ShortcutsDialog>({
        isTitleHidden: true,
        parent() {
            return this.block1;
        },
        map() {
            return 'Click me!';
        },
        title: 'Button 1',
        shortcut: ['shift', 'control', 'm'],
        helperText: 'You can also trigger me by Shift + Ctrl + M',
        onClick() {
            this.$.showToast('Wow you clicked Button 1 in the dialog!');
        },
    })
    button1: ui.fields.Button;

    @ui.decorators.pageAction<ShortcutsDialog>({
        isTitleHidden: true,
        shortcut: ['shift', 'alt', 'p'],
        onClick() {
            this.$.showToast('An action is triggered by Shift + Alt + P in the dialog');
        },
    })
    action1: ui.PageAction;

    @ui.decorators.textAreaField<ShortcutsDialog>({
        isTitleHidden: true,
        parent() {
            return this.block1;
        },
        isReadOnly: true,
        isFullWidth: true,
    })
    infoText: ui.fields.TextArea;
}
