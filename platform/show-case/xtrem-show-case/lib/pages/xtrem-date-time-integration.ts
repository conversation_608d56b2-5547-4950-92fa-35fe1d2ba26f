import * as ui from '@sage/xtrem-ui';
import * as xtremDateTime from '@sage/xtrem-date-time';
import { misc } from '../menu-items/misc';

@ui.decorators.page<XtremDateTimeIntegration>({
    module: 'show-case',
    title: 'Xtrem date time lib integration',
    category: 'SHOWCASE',
    isTransient: true,
    menuItem: misc,
    onLoad() {
        const date = xtremDateTime.date.make(2021, 12, 1);
        this.monthName.value = date.format('MMM');
        this.dayName.value = date.format('ddd');
        this.monthNameLong.value = date.format('MMMM');
        this.dayNameLong.value = date.format('dddd');
    },
})
export class XtremDateTimeIntegration extends ui.Page {
    @ui.decorators.section<XtremDateTimeIntegration>({
        isTitleHidden: true,
    })
    section: ui.containers.Section;

    @ui.decorators.block<XtremDateTimeIntegration>({
        parent() {
            return this.section;
        },
        isTitleHidden: true,
    })
    fieldBlock: ui.containers.Block;

    @ui.decorators.textField<XtremDateTimeIntegration>({
        parent() {
            return this.fieldBlock;
        },
        isReadOnly: true,
        title: 'Month name',
    })
    monthName: ui.fields.Text;

    @ui.decorators.textField<XtremDateTimeIntegration>({
        parent() {
            return this.fieldBlock;
        },
        isReadOnly: true,
        title: 'Day name',
    })
    dayName: ui.fields.Text;

    @ui.decorators.textField<XtremDateTimeIntegration>({
        parent() {
            return this.fieldBlock;
        },
        isReadOnly: true,
        title: 'Month name long',
    })
    monthNameLong: ui.fields.Text;

    @ui.decorators.textField<XtremDateTimeIntegration>({
        parent() {
            return this.fieldBlock;
        },
        isReadOnly: true,
        title: 'Day name long',
    })
    dayNameLong: ui.fields.Text;
}
