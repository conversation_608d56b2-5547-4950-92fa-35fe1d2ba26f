import { Graph<PERSON><PERSON>, ShowCaseProduct, ShowCaseProvider } from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import { navigationPanel } from '../menu-items/navigation-panel';

@ui.decorators.page<NavigationPanelWithLinks, ShowCaseProduct>({
    authorizationCode: 'NAVPANEL',
    module: 'show-case',
    title: 'Navigation Panel',
    node: '@sage/xtrem-show-case/ShowCaseProduct',
    category: 'SHOWCASE',
    businessActions() {
        return [this.$standardCancelAction, this.$standardSaveAction];
    },
    createAction() {
        return this.$standardNewAction;
    },
    navigationPanel: {
        emptyStateText: 'We are in level 1',
        emptyStateClickableText: 'Click me',
        onEmptyStateLinkClick() {
            this.$.dialog.message('info', 'Hey', 'You just clicked me');
        },
        isAutoSelectEnabled: true,
        listItem: {
            title: ui.nestedFields.link({
                bind: 'product',
                page: '@sage/xtrem-show-case/NavigationPanelWithLinks',
                queryParameters: (_, rowValue) => ({ _id: rowValue._id }),
            }),
            titleRight: ui.nestedFields.label({ bind: '_id' }),
            line2Right: ui.nestedFields.date({ bind: 'releaseDate' }),
            line2: ui.nestedFields.reference({
                bind: 'provider',
                node: '@sage/xtrem-show-case/ShowCaseProvider',
                valueField: 'textField',
            }),
            image: ui.nestedFields.image({ bind: 'imageField' }),
        },
    },
    menuItem: navigationPanel,
})
export class NavigationPanelWithLinks extends ui.Page<GraphApi> {
    @ui.decorators.section<NavigationPanelWithLinks>({})
    section: ui.containers.Section;

    @ui.decorators.block<NavigationPanelWithLinks>({
        parent() {
            return this.section;
        },
        title: 'Current product data',
    })
    block: ui.containers.Block;

    @ui.decorators.textField<NavigationPanelWithLinks>({
        parent() {
            return this.block;
        },
        title: 'Id',
        isReadOnly: true,
    })
    _id: ui.fields.Text;

    @ui.decorators.textField<NavigationPanelWithLinks>({
        parent() {
            return this.block;
        },
        title: 'Product',
        isReadOnly: true,
    })
    product: ui.fields.Text;

    @ui.decorators.referenceField<NavigationPanelWithLinks, ShowCaseProvider>({
        parent() {
            return this.block;
        },
        helperTextField: '_id',
        isReadOnly: true,
        node: '@sage/xtrem-show-case/ShowCaseProvider',
        title: 'Provider',
        valueField: 'textField',
    })
    provider: ui.fields.Reference;
}
