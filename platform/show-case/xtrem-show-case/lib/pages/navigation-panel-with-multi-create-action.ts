import { Graph<PERSON><PERSON>, ShowCaseProduct, ShowCaseProvider } from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import { navigationPanel } from '../menu-items/navigation-panel';

@ui.decorators.page<NavigationPanelWithMultiCreateAction, ShowCaseProduct>({
    authorizationCode: 'NAVPNLMCA',
    businessActions() {
        return [this.$standardCancelAction, this.$standardSaveAction];
    },
    category: 'SHOWCASE',
    createAction() {
        return [this.listCreate, this.formCreate, this.advnCreate];
    },
    headerDropDownActions() {
        return [this.$standardDeleteAction];
    },
    menuItem: navigationPanel,
    module: 'show-case',
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.text({
                bind: 'product',
                title: 'Product',
            }),
            titleRight: ui.nestedFields.text({
                bind: '_id',
                title: 'Id',
            }),
            line2: ui.nestedFields.reference({
                bind: 'provider',
                node: '@sage/xtrem-show-case/ShowCaseProvider',
                title: 'Provider',
                valueField: 'textField',
            }),
            category: ui.nestedFields.label({
                canFilter: true,
                bind: 'category',
                title: 'Category',
                optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
            }),
        },
    },
    node: '@sage/xtrem-show-case/ShowCaseProduct',
    objectTypePlural: 'Products',
    objectTypeSingular: 'Product',
    title: 'Navigation Panel (with Multi-Create)',
})
export class NavigationPanelWithMultiCreateAction extends ui.Page<GraphApi> {
    @ui.decorators.pageAction<NavigationPanelWithMultiCreateAction>({
        onClick() {
            this.$.showToast('This should handle the custom "List" create action.');
        },
        title: 'List',
    })
    listCreate: ui.PageAction;

    @ui.decorators.pageAction<NavigationPanelWithMultiCreateAction>({
        onClick() {
            this.$.showToast('This should handle the custom "Form" create action.');
        },
        title: 'Form',
    })
    formCreate: ui.PageAction;

    @ui.decorators.pageAction<NavigationPanelWithMultiCreateAction>({
        onClick() {
            this.$.showToast('This should handle the custom "Advanced" create action.');
        },
        title: 'Advanced',
    })
    advnCreate: ui.PageAction;

    @ui.decorators.section<NavigationPanelWithMultiCreateAction>({})
    section: ui.containers.Section;

    @ui.decorators.block<NavigationPanelWithMultiCreateAction>({
        parent() {
            return this.section;
        },
        title: 'Product',
    })
    block: ui.containers.Block;

    @ui.decorators.textField<NavigationPanelWithMultiCreateAction>({
        bind: '_id',
        isReadOnly: true,
        parent() {
            return this.block;
        },
        title: 'Id',
        width: 'small',
    })
    id: ui.fields.Text;

    @ui.decorators.textField<NavigationPanelWithMultiCreateAction>({
        bind: 'product',
        parent() {
            return this.block;
        },
        title: 'Product',
        width: 'medium',
    })
    product: ui.fields.Text;

    @ui.decorators.referenceField<NavigationPanelWithMultiCreateAction, ShowCaseProvider>({
        bind: 'provider',
        node: '@sage/xtrem-show-case/ShowCaseProvider',
        parent() {
            return this.block;
        },
        title: 'Provider',
        valueField: 'textField',
        width: 'medium',
    })
    provider: ui.fields.Reference;
}
