import { Graph<PERSON><PERSON>, ShowCaseProduct, ShowCaseProvider } from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import { pod } from '../menu-items/pod';

@ui.decorators.page<PodBlock, ShowCaseProduct>({
    authorizationCode: 'SHCPRVD',
    category: 'SHOWCASE',
    menuItem: pod,
    businessActions() {
        return [this.toggleNavigationPanel];
    },
    createAction() {
        return this.create;
    },
    headerDropDownActions() {
        return [this.delete];
    },
    module: 'show-case',
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.text({ bind: 'product' }),
            titleRight: ui.nestedFields.text({ bind: '_id' }),
            line2: ui.nestedFields.text({ bind: 'description', canFilter: false }),
        },
    },
    node: '@sage/xtrem-show-case/ShowCaseProduct',
    title: 'Pod as a block',
})
export class Pod<PERSON>lock extends ui.Page<GraphApi> {
    @ui.decorators.section<PodBlock>({})
    mainSection: ui.containers.Section;

    @ui.decorators.block<PodBlock>({
        parent() {
            return this.mainSection;
        },
        width: 'medium',
    })
    block: ui.containers.Block;

    @ui.decorators.textField<PodBlock>({
        parent() {
            return this.block;
        },
        title: 'Id',
        width: 'medium',
    })
    _id: ui.fields.Text;

    @ui.decorators.podField<PodBlock, ShowCaseProvider>({
        title: 'Provider',
        width: 'large',
        parent() {
            return this.mainSection;
        },
        dropdownActions: [
            {
                title: 'Action 1',
                icon: 'sync',
                isDisabled() {
                    return this.isAction1Disabled.value;
                },
                onClick() {
                    this.$.showToast('Action 1 triggered');
                },
            },
            {
                title: 'Action 2',
                icon: 'document_tick',
                isHidden() {
                    return this.isAction2Hidden.value;
                },
                onClick() {
                    this.$.showToast('Action 1 triggered');
                },
            },
        ],
        onClick() {
            this.clickTriggered.isHidden = false;
            setTimeout(() => {
                this.clickTriggered.isHidden = true;
            }, 5000);
        },
        onChange() {
            this.changeTriggered.isHidden = false;
            setTimeout(() => {
                this.changeTriggered.isHidden = true;
            }, 5000);
        },
        onError() {
            ui.console.error('just testing onError works.');
        },
        additionalLookupRecords() {
            return [
                {
                    _id: '-1',
                    textField: 'Some Provider',
                    integerField: 3,
                    concatenatedAddress: 'Stella-Klein-Löw-Weg 15\nViertel Zwei\nVienna\n1020\nAustria',
                },
            ];
        },
        columns: [
            ui.nestedFields.text({ bind: 'textField', title: 'Text field' }),
            ui.nestedFields.numeric({ bind: 'integerField', title: 'Numeric field' }),
            ui.nestedFields.text({ bind: '_id', title: 'id' }),
            ui.nestedFields.date({ bind: 'dateField', title: 'date field' }),
            ui.nestedFields.textArea({ bind: 'concatenatedAddress', title: 'Address' }),
        ],
        node: '@sage/xtrem-show-case/ShowCaseProvider',
    })
    provider: ui.fields.Pod;

    @ui.decorators.pageAction<PodBlock>({
        title: 'Create',
        icon: 'add',
        async onClick() {
            await this.$.graph.create();
            this.$.dialog.message('info', 'Mutation Create', `Created entry: ${this._id.value}`, {
                fullScreen: false,
                rightAligned: false,
                acceptButton: {
                    isDisabled: false,
                    isHidden: false,
                    text: 'OK',
                },
            });
        },
    })
    create: ui.PageAction;

    @ui.decorators.pageAction<PodBlock>({
        title: 'Save',
        async onClick() {
            await this.$.graph.update();
            this.$.dialog.message('info', 'Mutation Update', `Updated entry: ${this._id.value}`, {
                fullScreen: false,
                rightAligned: false,
                acceptButton: {
                    isDisabled: false,
                    isHidden: false,
                    text: 'OK',
                },
            });
        },
    })
    update: ui.PageAction;

    @ui.decorators.pageAction<PodBlock>({
        title: 'Delete',
        icon: 'bin',
        isDestructive: true,
        async onClick() {
            await this.$.graph.delete();
            this.$.router.goTo('@sage/xtrem-show-case/BoundPage');
        },
    })
    delete: ui.PageAction;

    @ui.decorators.pageAction<PodBlock>({
        title: 'Toggle Navigation Panel',
        async onClick() {
            this.$.isNavigationPanelHidden = !this.$.isNavigationPanelHidden;
        },
    })
    toggleNavigationPanel: ui.PageAction;

    /** Testing field buttons */

    @ui.decorators.labelField<PodBlock>({
        parent() {
            return this.block;
        },
        isTransient: true,
        isHidden: true,
        map() {
            return 'Change was triggered';
        },
    })
    changeTriggered: ui.fields.Label;

    @ui.decorators.labelField<PodBlock>({
        parent() {
            return this.block;
        },
        isTransient: true,
        isHidden: true,
        map() {
            return 'Click was triggered';
        },
    })
    clickTriggered: ui.fields.Label;

    @ui.decorators.labelField<PodBlock>({
        parent() {
            return this.block;
        },
        isTransient: true,
        isHidden: true,
        map() {
            return 'Add Click was triggered';
        },
    })
    addClickTriggered: ui.fields.Label;

    @ui.decorators.section<PodBlock>({
        isTitleHidden: true,
    })
    configurationSection: ui.containers.Section;

    @ui.decorators.block<PodBlock>({
        parent() {
            return this.configurationSection;
        },
        title: 'Configuration',
        isTransient: true,
    })
    configurationBlock: ui.containers.Block;

    @ui.decorators.textField<PodBlock>({
        parent() {
            return this.configurationBlock;
        },
        isTransient: true,
        title: 'Helper text',
        onChange() {
            this.provider.helperText = this.helperText.value;
        },
    })
    helperText: ui.fields.Text;

    @ui.decorators.checkboxField<PodBlock>({
        parent() {
            return this.configurationBlock;
        },
        isTransient: true,
        title: 'Is disabled',
        onChange() {
            this.provider.isDisabled = this.isDisabled.value;
        },
    })
    isDisabled: ui.fields.Checkbox;

    @ui.decorators.checkboxField<PodBlock>({
        parent() {
            return this.configurationBlock;
        },
        isTransient: true,
        title: 'Is removable',
        onChange() {
            this.provider.canRemove = this.isRemovable.value;
        },
    })
    isRemovable: ui.fields.Checkbox;

    @ui.decorators.checkboxField<PodBlock>({
        parent() {
            return this.configurationBlock;
        },
        isTransient: true,
        title: 'Is helper text hidden',
        onChange() {
            this.provider.isHelperTextHidden = this.isHelperTextHidden.value;
        },
    })
    isHelperTextHidden: ui.fields.Checkbox;

    @ui.decorators.checkboxField<PodBlock>({
        parent() {
            return this.configurationBlock;
        },
        isTransient: true,
        title: 'Is hidden',
        onChange() {
            this.provider.isHidden = this.isHidden.value;
        },
    })
    isHidden: ui.fields.Checkbox;

    @ui.decorators.checkboxField<PodBlock>({
        parent() {
            return this.configurationBlock;
        },
        isTransient: true,
        title: 'Is readOnly',
        onChange() {
            this.provider.isReadOnly = this.isReadOnly.value;
        },
    })
    isReadOnly: ui.fields.Checkbox;

    @ui.decorators.checkboxField<PodBlock>({
        parent() {
            return this.configurationBlock;
        },
        isTransient: true,
        title: 'Is title hidden',
        onChange() {
            this.provider.isTitleHidden = this.isTitleHidden.value;
        },
    })
    isTitleHidden: ui.fields.Checkbox;

    @ui.decorators.textField<PodBlock>({
        parent() {
            return this.configurationBlock;
        },
        isTransient: true,
        title: 'Title',
        onChange() {
            this.provider.title = this.title.value;
        },
    })
    title: ui.fields.Text;

    @ui.decorators.buttonField<PodBlock>({
        parent() {
            return this.configurationBlock;
        },
        isTransient: true,
        map() {
            return 'Focus field';
        },
        onClick() {
            this.provider.focus();
        },
    })
    focus: ui.fields.Button;

    @ui.decorators.checkboxField<PodBlock>({
        parent() {
            return this.configurationBlock;
        },
        isTransient: true,
        title: 'Disable action 1',
    })
    isAction1Disabled: ui.fields.Checkbox;

    @ui.decorators.checkboxField<PodBlock>({
        parent() {
            return this.configurationBlock;
        },
        isTransient: true,
        title: 'Hide action 2',
    })
    isAction2Hidden: ui.fields.Checkbox;
}
