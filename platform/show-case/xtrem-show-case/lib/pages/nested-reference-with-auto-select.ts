import { <PERSON><PERSON>h<PERSON><PERSON>, ShowCaseProduct, ShowCaseProvider } from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import { tableField } from '../menu-items/table-field';

@ui.decorators.page<NestedReferenceWithAutoSelect, ShowCaseProvider>({
    authorizationCode: 'NSTREFATSLCT',
    category: 'SHOWCASE',
    createAction() {
        return this.$standardNewAction;
    },
    idField() {
        return this.id;
    },
    menuItem: tableField,
    module: 'show-case',
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.text({
                bind: 'textField',
                title: 'Provider',
            }),
            titleRight: ui.nestedFields.label({
                bind: '_id',
                title: 'Id',
            }),
        },
    },
    node: '@sage/xtrem-show-case/ShowCaseProvider',
    objectTypeSingular: 'Provider',
    objectTypePlural: 'Providers',
    title: 'Field - Nested Reference (with AutoSelect)',
})
export class NestedReferenceWithAutoSelect extends ui.Page<GraphApi> {
    @ui.decorators.section<NestedReferenceWithAutoSelect>({})
    section: ui.containers.Section;

    @ui.decorators.block<NestedReferenceWithAutoSelect>({
        parent() {
            return this.section;
        },
    })
    block: ui.containers.Block;

    @ui.decorators.textField<NestedReferenceWithAutoSelect>({
        bind: '_id',
        isReadOnly: true,
        parent() {
            return this.block;
        },
        title: 'Id',
    })
    id: ui.fields.Text;

    @ui.decorators.tableField<NestedReferenceWithAutoSelect, ShowCaseProduct>({
        bind: 'products',
        canExport: true,
        canFilter: true,
        columns: [
            ui.nestedFields.text({
                bind: '_id',
                isReadOnly: true,
                title: 'Id',
            }),
            ui.nestedFields.text({
                bind: 'product',
                title: 'Product',
            }),
            ui.nestedFields.reference<NestedReferenceWithAutoSelect, ShowCaseProduct, ShowCaseProvider>({
                bind: 'provider',
                isAutoSelectEnabled: true,
                node: '@sage/xtrem-show-case/ShowCaseProvider',
                title: 'Provider',
                valueField: 'textField',
            }),
        ],
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        orderBy: { product: 1 },
        parent() {
            return this.block;
        },
        title: 'Products',
    })
    products: ui.fields.Table<ShowCaseProduct>;
}
