import { ShowCase<PERSON>ust<PERSON>, ShowCaseProduct, ShowCaseProvider } from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import { tableField } from '../menu-items/_index';

@ui.decorators.page<Table, ShowCaseProvider>({
    authorizationCode: 'BSCFLDS',
    defaultEntry: () => '2',
    module: 'show-case',
    category: 'SHOWCASE',
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.text({ bind: 'textField' }),
            titleRight: ui.nestedFields.text({ bind: '_id' }),
        },
    },
    node: '@sage/xtrem-show-case/ShowCaseProvider',
    onLoad() {
        this.calculateTableTotals();
        this.canSelect.value = true;
        this.canUserHideColumns.value = true;
        this.mediumValueLimit.value = 5;
        this.highValueLimit.value = 20;
        this.quantityValuePrefix.value = '';
        this.quantityValueScale.value = 0;
        this.canEditDescription.value = true;
        this.columnTitleProperty = 'Dynamically allocated title';
        this.field.hideColumn('description');
        this.columnsList.value = 'description';
        this.isReadOnly.value = true;
    },
    subtitle: 'Complex table examples',
    title: 'Field - Table',
    businessActions() {
        return [this.saveTable];
    },
    headerLabel() {
        return this.headerLabel;
    },
    menuItem: tableField,
})
export class Table extends ui.Page {
    private columnTitleProperty: string;

    @ui.decorators.labelField<Table>({
        isTransient: true,
        map() {
            return 'Title label info';
        },
        onClick() {
            console.log('Doing something when the label field is clicked');
        },
    })
    headerLabel: ui.fields.Label;

    @ui.decorators.section<Table>({
        title: 'Table field',
    })
    section: ui.containers.Section;

    @ui.decorators.block<Table>({
        parent() {
            return this.section;
        },
        title: 'Field example',
    })
    fieldBlock: ui.containers.Block;

    @ui.decorators.numericField<Table>({
        parent() {
            return this.fieldBlock;
        },
        title: 'Selected items total',
        isReadOnly: true,
        scale: 2,
        prefix: '$',
        isTransient: true,
    })
    tableSelectedTotal: ui.fields.Numeric;

    @ui.decorators.numericField<Table>({
        parent() {
            return this.fieldBlock;
        },
        isTransient: true,
        title: 'Items total',
        isReadOnly: true,
        scale: 2,
        prefix: '$',
    })
    tableSampleTotal: ui.fields.Numeric;

    @ui.decorators.buttonField<Table>({
        parent() {
            return this.fieldBlock;
        },
        isTransient: true,
        async onClick() {
            await this.field.refresh();
        },
        map() {
            return 'Refresh Table';
        },
    })
    refreshTable: ui.fields.Button;

    @ui.decorators.buttonField<Table>({
        parent() {
            return this.fieldBlock;
        },
        onClick() {
            const rowData = {
                product: 'Aaaaaaaa',
                description: 'aaaaaaa',
                st: 1,
                qty: 1,
                listPrice: '1.1',
                tax: '1',
                amount: '1',
                releaseDate: '2020-08-04',
            };
            this.field.addRecord(rowData);
        },
        isTransient: true,
        map() {
            return 'Add New Item';
        },
    })
    addNewItem: ui.fields.Button;

    @ui.decorators.buttonField<Table>({
        parent() {
            return this.fieldBlock;
        },
        onClick() {
            this.field.addRecordWithDefaults();
        },
        isTransient: true,
        map() {
            return 'Add New Item With Defaults';
        },
    })
    addNewItemWithDefaults: ui.fields.Button;

    @ui.decorators.buttonField<Table>({
        parent() {
            return this.fieldBlock;
        },
        onClick() {
            this.field.unselectAllRecords();
        },
        isTransient: true,
        map() {
            return 'Unselect all items';
        },
    })
    unselectAllRecords: ui.fields.Button;

    @ui.decorators.buttonField<Table>({
        parent() {
            return this.fieldBlock;
        },
        onClick() {
            this.field.unselectRecord('320');
        },
        isTransient: true,
        map() {
            return 'Unselect Item 320';
        },
    })
    unselectRecord: ui.fields.Button;

    @ui.decorators.buttonField<Table>({
        parent() {
            return this.fieldBlock;
        },
        onClick() {
            this.field.selectRecord('320');
        },
        isTransient: true,
        map() {
            return 'Select Item 320';
        },
    })
    selectRecord: ui.fields.Button;

    @ui.decorators.buttonField<Table>({
        parent() {
            return this.fieldBlock;
        },
        onClick() {
            this.calculateSelectedTotal();
        },
        isTransient: true,
        map() {
            return 'Recalculate selected totals';
        },
    })
    recalculateTotal: ui.fields.Button;

    @ui.decorators.buttonField<Table>({
        parent() {
            return this.fieldBlock;
        },
        onClick() {
            this.field.filter = { qty: { _gt: 16 } };
        },
        isTransient: true,
        map() {
            return 'Filter Quantity > 16';
        },
    })
    filterTableButton: ui.fields.Button;

    @ui.decorators.tableField<Table, ShowCaseProduct>({
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        bind: 'products',
        mobileCard: {
            title: ui.nestedFields.date<Table, ShowCaseProduct>({
                bind: 'releaseDate',
                title: 'Date',
            }),
            titleRight: ui.nestedFields.label<Table, ShowCaseProduct>({
                bind: 'amount',
                title: 'Amount',
                prefix: '$',
                isHiddenDesktop: false,
            }),
            line2: ui.nestedFields.select<Table, ShowCaseProduct>({
                bind: 'category',
                title: 'Category',
                optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
            }),
            line2Right: ui.nestedFields.reference<Table, ShowCaseProduct, ShowCaseProvider>({
                bind: 'provider',
                title: 'Provider',
                valueField: 'textField',
                node: '@sage/xtrem-show-case/ShowCaseProvider',
                minLookupCharacters: 0,
                columns: [
                    ui.nestedFields.text({ bind: '_id', title: 'ID', canFilter: false }),
                    ui.nestedFields.text({ bind: 'textField', title: 'Provider', canFilter: true }),
                    ui.nestedFields.image({ bind: 'logo', title: 'Logo', canFilter: false }),
                ],
                imageField: 'logo',
                onOpenLookupDialog() {
                    console.log('reference lookup dialog open');
                },
                onCloseLookupDialog() {
                    console.log('reference lookup dialog close');
                },
                onClick(id: string, raw: any) {
                    ui.console.log('CLICKED LOOKUP', { id, raw });
                },
                onChange(id: string, raw: any) {
                    ui.console.log('CHANGED LOOKUP', { id, raw });
                },
                tunnelPage: null,
            }),
        },
        hasSearchBoxMobile: true,
        canExport: true,
        canResizeColumns: true,
        columns: [
            ui.nestedFields.text({
                bind: '_id',
                title: 'Id',
                isHiddenDesktop: false,
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                bind: 'product',
                title: 'Product',
            }),
            ui.nestedFields.text({
                bind: 'description',
                title: 'Description',
                isDisabled() {
                    return !this.canEditDescription.value;
                },
            }),
            ui.nestedFields.numeric({
                bind: 'qty',
                title: 'Quantity',
                onChange(_id: number, rowData: ui.PartialNodeWithId<ShowCaseProduct>) {
                    this.updateRow(_id, rowData);
                },
                prefix() {
                    return this.quantityValuePrefix.value || '';
                },
                scale() {
                    return this.quantityValueScale.value || 0;
                },
                fetchesDefaults: true,
            }),
            ui.nestedFields.numeric({
                bind: 'fixedQuantity',
                title: 'Fixed Quantity',
                isTransientInput: true,
            }),
            ui.nestedFields.numeric({
                bind: 'listPrice',
                title: 'List Price',
                scale: 2,
                onChange(_id: number, rowData: ui.PartialNodeWithId<ShowCaseProduct>) {
                    this.updateRow(_id, rowData);
                },
                isHiddenDesktop: false,
                fetchesDefaults: true,
            }),
            ui.nestedFields.numeric({
                bind: 'netPrice',
                title: 'Net Price',
                isReadOnly: (value: number, rowValue: any) => {
                    return !!rowValue.hotProduct;
                },
                scale: 2,
                isHiddenDesktop: false,
            }),
            ui.nestedFields.label({
                backgroundColor(_fieldValue: number, rowData: ShowCaseProduct) {
                    return rowData.qty > this.highValueLimit.value
                        ? '00b000'
                        : rowData.qty > this.mediumValueLimit.value
                          ? 'ffb700'
                          : 'e96400';
                },
                bind: 'qty',
                isTransient: true,
                map(_fieldValue, rowData) {
                    return rowData.qty > this.highValueLimit.value
                        ? 'High'
                        : rowData.qty > this.mediumValueLimit.value
                          ? 'Medium'
                          : 'Low';
                },
                title: 'Indicator',
            }),
            ui.nestedFields.label({
                bind: 'st',
                onClick(_id: string | number, rowData) {
                    this.$.dialog.message('info', 'Label was clicked', `Label ${rowData.st} was clicked (row ${_id})`);
                },
                title: 'St',
            }),
            ui.nestedFields.checkbox({
                bind: 'hotProduct',
                title: 'Hot',
            }),
            ui.nestedFields.switch({
                bind: 'hotProduct',
                title: 'Hot (Switch)',
            }),
            ui.nestedFields.link({
                bind: 'product',
                isTransient: true,
                map(_fieldValue, rowData) {
                    return `http://${(rowData?.product ?? ('' as string))
                        .replace(/-|_|\s/g, '')
                        .substring(0, 4)
                        .toLowerCase()}.sage.com`;
                },
                onClick(id: string, raw: any) {
                    ui.console.log('CLICKED LINK', { id, raw });
                },
                title: 'Link',
                page: 'https://www.google.com',
            }),
            ui.nestedFields.progress({
                bind: 'progress',
                title: 'Progress',
            }),
            ui.nestedFields.numeric({
                bind: 'tax',
                title: 'Tax',
                prefix: 'T',
                isMandatory: true,
                scale: 2,
                isHiddenDesktop: false,
            }),
            ui.nestedFields.label<Table, ShowCaseProduct>({
                bind: 'amount',
                title: 'Amount',
                prefix: '$',
                isHiddenDesktop: false,
            }),
            ui.nestedFields.reference<Table, ShowCaseProduct, ShowCaseProvider>({
                bind: 'provider',
                title: 'Provider',
                valueField: 'textField',
                node: '@sage/xtrem-show-case/ShowCaseProvider',
                minLookupCharacters: 0,
                imageField: 'logo',
                helperTextField: '_id',
                onClick(id: string, raw: any) {
                    ui.console.log('CLICKED NO LOOKUP', { id, raw });
                },
                onChange(id: string, raw: any) {
                    ui.console.log('CHANGED NO LOOKUP', { id, raw });
                    this.$.showToast(`New provider set: ${raw?.provider?.textField ?? ''}`);
                },
                columns: null,
                tunnelPage: null,
            }),
            ui.nestedFields.reference<Table, ShowCaseProduct, ShowCaseProvider>({
                bind: 'provider',
                title: 'Provider',
                valueField: 'textField',
                node: '@sage/xtrem-show-case/ShowCaseProvider',
                minLookupCharacters: 0,
                columns: [
                    ui.nestedFields.text({ bind: '_id', title: 'ID', canFilter: false }),
                    ui.nestedFields.text({ bind: 'textField', title: 'Provider', canFilter: true }),
                    ui.nestedFields.image({ bind: 'logo', title: 'Logo', canFilter: false }),
                ],
                helperTextField: '_id',
                imageField: 'logo',
                onOpenLookupDialog() {
                    console.log('reference lookup dialog open');
                },
                onCloseLookupDialog() {
                    console.log('reference lookup dialog close');
                },
                onClick(id: string, raw: any) {
                    ui.console.log('CLICKED LOOKUP', { id, raw });
                },
                onChange(id: string, raw: any) {
                    ui.console.log('CHANGED LOOKUP', { id, raw });
                },
                tunnelPage: null,
            }),
            ui.nestedFields.filterSelect<Table, ShowCaseProduct, ShowCaseCustomer>({
                bind: 'email',
                title: 'Email',
                valueField: 'email',
                node: '@sage/xtrem-show-case/ShowCaseCustomer',
                minLookupCharacters: 0,
                width: 'large',
                onChange(id: string, raw: string) {
                    ui.console.log('CHANGED FILTER SELECT', { id, raw });
                },
                columns: [
                    ui.nestedFields.text({ bind: '_id', title: 'Id' }),
                    ui.nestedFields.text({ bind: 'email', title: 'Email' }),
                ],
            }),
            ui.nestedFields.date<Table, ShowCaseProduct>({
                bind: 'releaseDate',
                title: 'Date',
                maxDate(row) {
                    return row.endingDate;
                },
            }),
            ui.nestedFields.date({
                bind: 'endingDate',
                title: 'Ending date',
                minDate(row) {
                    return row.releaseDate;
                },
            }),
            ui.nestedFields.numeric({
                bind: 'discount',
                title: 'Discount',
            }),
            ui.nestedFields.select<Table, ShowCaseProduct>({
                bind: 'category',
                title: 'Category',
                optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
            }),
            ui.nestedFields.image({
                bind: 'imageField',
                title: 'Image',
            }),
            ui.nestedFields.icon({
                bind: 'qty',
                title: 'Icon',
                map: value => {
                    return value === 0 ? 'error' : value < 10 ? 'warning' : '';
                },
            }),
        ],
        orderBy: {
            product: 1,
        },
        parent() {
            return this.fieldBlock;
        },
        onChange() {
            this.calculateTableTotals();
            this.calculateSelectedTotal();
        },
        onRowSelected(_id: string | number, item: any) {
            this.calculateSelectedTotal();
            this.$.showToast(`${item.product} selected.`);
        },
        onRowUnselected(_id: string | number, item: any) {
            this.calculateSelectedTotal();
            this.$.showToast(`${item.product} unselected.`, { type: 'warning' });
        },
        dropdownActions: [
            {
                icon: 'locked',
                title: 'Maybe disabled',
                isDisabled(id: any, row: any) {
                    return (row?.qty ?? 0) < 10;
                },
                onClick(rowId: any, data: any) {
                    this.$.showToast(data.product, { type: 'info' });
                },
            },
            ui.menuSeparator(),
            {
                title: 'Action no icon',
                isDisabled(id: any, row: any) {
                    return (row?.qty ?? 0) < 10;
                },
                onClick(rowId: any, data: any) {
                    this.$.showToast(data.product, { type: 'info' });
                },
            },
            {
                title: 'Refresh record',
                icon: 'refresh',
                async onClick(rowId) {
                    await this.field.refreshRecord(rowId);
                },
            },
            ui.menuSeparator(),
            {
                icon: 'minus',
                title: 'Remove',
                isDestructive: true,
                onClick(rowId: any) {
                    this.field.removeRecord(rowId);
                },
            },
        ],
        canAddNewLine: true,
        sidebar: {
            title: 'Create a new record',
            layout() {
                return {
                    mainSection: {
                        title: 'Main section',
                        blocks: {
                            mainBlock: {
                                title: 'Some block title',
                                fields: [
                                    '_id',
                                    'tax',
                                    'provider',
                                    'category',
                                    'qty',
                                    'amount',
                                    'description',
                                    'product',
                                    'listPrice',
                                    'netPrice',
                                    'releaseDate',
                                    'endingDate',
                                    'discount',
                                    'hotProduct',
                                    'fixedQuantity',
                                    'imageField',
                                ],
                            },
                        },
                    },
                };
            },
        },
    })
    field: ui.fields.Table<ShowCaseProduct>;

    @ui.decorators.pageAction<Table>({
        title: 'Save',
        onClick() {
            this.$.loader.display();
            return this.$.graph
                .update()
                .then(() => {
                    this.$.finish();
                })
                .catch(() => {
                    this.$.showToast('Something went wrong', { type: 'error' });
                })
                .finally(() => {
                    this.$.loader.hide();
                });
        },
    })
    saveTable: ui.PageAction;

    @ui.decorators.numericField<Table>({
        title: 'Medium value limit',
        isTransient: true,
        parent() {
            return this.fieldBlock;
        },
    })
    mediumValueLimit: ui.fields.Numeric;

    @ui.decorators.numericField<Table>({
        title: 'High value limit (auto redraw)',
        isTransient: true,
        parent() {
            return this.fieldBlock;
        },
        async onChange() {
            await this.field.redraw('qty');
        },
    })
    highValueLimit: ui.fields.Numeric;

    @ui.decorators.textField<Table>({
        title: 'Quantity value prefix',
        isTransient: true,
        parent() {
            return this.fieldBlock;
        },
    })
    quantityValuePrefix: ui.fields.Text;

    @ui.decorators.numericField<Table>({
        title: 'Quantity value scale (auto redraw)',
        isTransient: true,
        scale: 0,
        parent() {
            return this.fieldBlock;
        },
        async onChange() {
            await this.field.redraw('qty');
        },
    })
    quantityValueScale: ui.fields.Numeric;

    @ui.decorators.checkboxField<Table>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Can edit Description',

        isTransient: true,
    })
    canEditDescription: ui.fields.Checkbox;

    @ui.decorators.buttonField<Table>({
        isTransient: true,
        map() {
            return 'Redraw';
        },
        parent() {
            return this.fieldBlock;
        },
        onClick() {
            this.field.redraw();
        },
    })
    redrawButton: ui.fields.Button;

    @ui.decorators.separatorField<Table>({
        parent() {
            return this.fieldBlock;
        },
        isFullWidth: true,
        isInvisible: true,
    })
    sep1: ui.fields.Separator;

    @ui.decorators.textField<Table>({
        title: 'Columns',
        isTransient: true,
        parent() {
            return this.fieldBlock;
        },
    })
    columnsList: ui.fields.Text;

    @ui.decorators.buttonField<Table>({
        isTransient: true,
        map() {
            return 'Hide Columns';
        },
        parent() {
            return this.fieldBlock;
        },
        onClick() {
            this.columnsList.value.split(',').map(column => this.field.hideColumn(column.trim()));
        },
    })
    hideColumnButton: ui.fields.Button;

    @ui.decorators.buttonField<Table>({
        isTransient: true,
        map() {
            return 'Show Columns';
        },
        parent() {
            return this.fieldBlock;
        },
        onClick() {
            this.columnsList.value.split(',').map(column => this.field.showColumn(column.trim()));
        },
    })
    showColumnButton: ui.fields.Button;

    @ui.decorators.block<Table>({
        parent() {
            return this.section;
        },
        title: 'Configuration',
    })
    configurationBlock: ui.containers.Block;

    @ui.decorators.checkboxField<Table>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Can select',
        onChange() {
            this.field.canSelect = this.canSelect.value;
        },
        isTransient: true,
    })
    canSelect: ui.fields.Checkbox;

    @ui.decorators.checkboxField<Table>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Can user hide columns',
        onChange() {
            this.field.canUserHideColumns = this.canUserHideColumns.value;
        },
        isTransient: true,
    })
    canUserHideColumns: ui.fields.Checkbox;

    @ui.decorators.textField<Table>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Helper text',
        onChange() {
            this.field.helperText = this.helperText.value;
        },
        isTransient: true,
    })
    helperText: ui.fields.Text;

    @ui.decorators.checkboxField<Table>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is disabled',
        onChange() {
            this.field.isDisabled = this.isDisabled.value;
        },
        isTransient: true,
    })
    isDisabled: ui.fields.Checkbox;

    @ui.decorators.checkboxField<Table>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is container disabled',
        onChange() {
            this.fieldBlock.isDisabled = this.isContainerDisabled.value;
        },
        isTransient: true,
    })
    isContainerDisabled: ui.fields.Checkbox;

    @ui.decorators.checkboxField<Table>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is helper text hidden',
        onChange() {
            this.field.isHelperTextHidden = this.isHelperTextHidden.value;
        },
        isTransient: true,
    })
    isHelperTextHidden: ui.fields.Checkbox;

    @ui.decorators.checkboxField<Table>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is hidden',
        onChange() {
            this.field.isHidden = this.isHidden.value;
        },
        isTransient: true,
    })
    isHidden: ui.fields.Checkbox;

    @ui.decorators.checkboxField<Table>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is title hidden',
        onChange() {
            this.field.isTitleHidden = this.isTitleHidden.value;
        },
        isTransient: true,
    })
    isTitleHidden: ui.fields.Checkbox;

    @ui.decorators.textField<Table>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Title',
        onChange() {
            this.field.title = this.title.value;
        },
        isTransient: true,
    })
    title: ui.fields.Text;

    updateRow(_id: any, rowData: ui.PartialNodeWithId<ShowCaseProduct>) {
        const updatedAmount = rowData.qty * parseFloat(rowData.listPrice);
        rowData.amount = String(updatedAmount);
        this.field.setRecordValue(rowData);
    }

    calculateTableTotals() {
        this.tableSampleTotal.value = (this.field.value || [])
            .map(v => parseFloat(v.amount))
            .reduce((prev, v) => prev + v, 0);
        this.calculateSelectedTotal();
    }

    calculateSelectedTotal() {
        const selectedRecords = this.field.selectedRecords || [];
        const selectedTotal = (this.field.value || [])
            .filter(v => selectedRecords.indexOf(v._id) !== -1)
            .map(v => parseFloat(v.amount))
            .reduce((prev, v) => prev + v, 0);

        this.tableSelectedTotal.value = selectedTotal;
    }

    /* Additional examples */

    @ui.decorators.block<Table>({
        parent() {
            return this.section;
        },
        title: 'Additional examples',
    })
    additionalBlock: ui.containers.Block;

    @ui.decorators.tableField<Table, ShowCaseProduct>({
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        bind: 'products',
        orderBy: { _id: 1 },
        title: 'Restricted result set',
        helperText: 'The change indicator color is also disabled!',
        isChangeIndicatorDisabled: true,
        canSelect: false,
        columns: [
            ui.nestedFields.text({
                bind: 'product',
                title: 'Product',
            }),
            ui.nestedFields.text({
                bind: 'description',
                title: 'Description',
            }),
            ui.nestedFields.label({
                bind: 'st',
                title: 'St',
            }),
            ui.nestedFields.numeric({
                bind: 'qty',
                title: 'Quantity',
                scale: 0,
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                bind: 'category',
                isReadOnly: true,
                title() {
                    return this.columnTitleProperty;
                },
            }),
        ],
        parent() {
            return this.additionalBlock;
        },
    })
    restrictedResultSet: ui.fields.Table<ShowCaseProduct>;

    @ui.decorators.textField<Table>({
        isTransient: true,
        parent() {
            return this.additionalBlock;
        },
        title: 'Result set restriction (on product name)',
        onChange() {
            const filter = this.resultSetRestriction.value
                ? {
                      product: { _regex: this.resultSetRestriction.value, _options: 'i' },
                  }
                : undefined;

            this.restrictedResultSet.filter = filter;
        },
    })
    resultSetRestriction: ui.fields.Text;

    @ui.decorators.textField<Table>({
        isTransient: true,
        parent() {
            return this.additionalBlock;
        },
        title: 'Result set restriction (on description name)',
        onChange() {
            const filter = this.resultSetRestrictionDescription.value
                ? {
                      product: undefined as any,
                      description: { _regex: this.resultSetRestrictionDescription.value, _options: 'i' },
                  }
                : undefined;

            this.restrictedResultSet.filter = filter;
        },
    })
    resultSetRestrictionDescription: ui.fields.Text;

    @ui.decorators.separatorField<Table>({
        parent() {
            return this.additionalBlock;
        },
        isFullWidth: true,
    })
    sep2: ui.fields.Separator;

    @ui.decorators.tableField<Table, ShowCaseProduct>({
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        bind: 'products',
        title: 'Info and warning state',
        isChangeIndicatorDisabled: true,
        canSelect: false,
        pageSize: 10,
        columns: [
            ui.nestedFields.text({
                bind: 'product',
                title: 'Product',
                isMandatory: true,
            }),
            ui.nestedFields.text({
                bind: 'description',
                title: 'Description',
            }),
            ui.nestedFields.label({
                bind: 'st',
                title: 'St',
            }),
        ],
        parent() {
            return this.additionalBlock;
        },
    })
    infoAndWarningExample: ui.fields.Table<ShowCaseProduct>;

    @ui.decorators.textField<Table>({
        isTransient: true,
        parent() {
            return this.additionalBlock;
        },
        title: 'Warning message',
        onChange() {
            this.infoAndWarningExample.warningMessage = this.warningMessage.value || null;
        },
    })
    warningMessage: ui.fields.Text;

    @ui.decorators.textField<Table>({
        isTransient: true,
        parent() {
            return this.additionalBlock;
        },
        title: 'Info message',
        onChange() {
            this.infoAndWarningExample.infoMessage = this.infoMessage.value || null;
        },
    })
    infoMessage: ui.fields.Text;

    @ui.decorators.tableField<Table, ShowCaseProduct>({
        title: 'Table with section as parent',
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        bind: 'products',
        mobileCard: {
            title: ui.nestedFields.date<Table, ShowCaseProduct>({
                bind: 'releaseDate',
                title: 'Date',
            }),
            titleRight: ui.nestedFields.label<Table, ShowCaseProduct>({
                bind: 'amount',
                title: 'Amount',
                prefix: '$',
                isHiddenDesktop: false,
            }),
            line2: ui.nestedFields.select<Table, ShowCaseProduct>({
                bind: 'category',
                title: 'Category',
                optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
            }),
            line2Right: ui.nestedFields.reference<Table, ShowCaseProduct, ShowCaseProvider>({
                bind: 'provider',
                title: 'Provider',
                valueField: 'textField',
                node: '@sage/xtrem-show-case/ShowCaseProvider',
                minLookupCharacters: 0,
                columns: [
                    ui.nestedFields.text({ bind: '_id', title: 'ID', canFilter: false }),
                    ui.nestedFields.text({ bind: 'textField', title: 'Provider', canFilter: true }),
                    ui.nestedFields.image({ bind: 'logo', title: 'Logo', canFilter: false }),
                ],
                imageField: 'logo',
                onOpenLookupDialog() {
                    console.log('reference lookup dialog open');
                },
                onCloseLookupDialog() {
                    console.log('reference lookup dialog close');
                },
                onClick(id: string, raw: any) {
                    ui.console.log('CLICKED LOOKUP', { id, raw });
                },
                onChange(id: string, raw: any) {
                    ui.console.log('CHANGED LOOKUP', { id, raw });
                },
                tunnelPage: null,
            }),
        },
        hasSearchBoxMobile: true,
        canExport: true,
        canResizeColumns: true,
        columns: [
            ui.nestedFields.text({
                bind: '_id',
                title: 'Id',
                isHiddenDesktop: false,
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                bind: 'product',
                title: 'Product',
            }),
            ui.nestedFields.text({
                bind: 'description',
                title: 'Description',
                isDisabled() {
                    return !this.canEditDescription.value;
                },
            }),
            ui.nestedFields.numeric({
                bind: 'qty',
                title: 'Quantity',
                onChange(_id: number, rowData: ui.PartialNodeWithId<ShowCaseProduct>) {
                    this.updateRow(_id, rowData);
                },
                prefix() {
                    return this.quantityValuePrefix.value || '';
                },
                scale() {
                    return this.quantityValueScale.value || 0;
                },
                fetchesDefaults: true,
            }),
            ui.nestedFields.numeric({
                bind: 'fixedQuantity',
                title: 'Fixed Quantity',
                isTransientInput: true,
            }),
            ui.nestedFields.numeric({
                bind: 'listPrice',
                title: 'List Price',
                scale: 2,
                onChange(_id: number, rowData: ui.PartialNodeWithId<ShowCaseProduct>) {
                    this.updateRow(_id, rowData);
                },
                isHiddenDesktop: false,
                fetchesDefaults: true,
            }),
            ui.nestedFields.numeric({
                bind: 'netPrice',
                title: 'Net Price',
                isReadOnly: (value: number, rowValue: any) => {
                    return !!rowValue.hotProduct;
                },
                scale: 2,
                isHiddenDesktop: false,
            }),
            ui.nestedFields.label({
                backgroundColor(_fieldValue: number, rowData: ShowCaseProduct) {
                    return rowData.qty > this.highValueLimit.value
                        ? '00b000'
                        : rowData.qty > this.mediumValueLimit.value
                          ? 'ffb700'
                          : 'e96400';
                },
                bind: 'qty',
                isTransient: true,
                map(_fieldValue, rowData) {
                    return rowData.qty > this.highValueLimit.value
                        ? 'High'
                        : rowData.qty > this.mediumValueLimit.value
                          ? 'Medium'
                          : 'Low';
                },
                title: 'Indicator',
            }),
            ui.nestedFields.label({
                bind: 'st',
                onClick(_id: string | number, rowData) {
                    this.$.dialog.message('info', 'Label was clicked', `Label ${rowData.st} was clicked (row ${_id})`);
                },
                title: 'St',
            }),
            ui.nestedFields.checkbox({
                bind: 'hotProduct',
                title: 'Hot',
            }),
            ui.nestedFields.switch({
                bind: 'hotProduct',
                title: 'Hot (Switch)',
            }),
            ui.nestedFields.link({
                bind: 'product',
                isTransient: true,
                map(_fieldValue, rowData) {
                    return `http://${(rowData?.product ?? ('' as string))
                        .replace(/-|_|\s/g, '')
                        .substring(0, 4)
                        .toLowerCase()}.sage.com`;
                },
                onClick(id: string, raw: any) {
                    ui.console.log('CLICKED LINK', { id, raw });
                },
                title: 'Link',
                page: 'https://www.google.com',
            }),
            ui.nestedFields.progress({
                bind: 'progress',
                title: 'Progress',
            }),
            ui.nestedFields.numeric({
                bind: 'tax',
                title: 'Tax',
                prefix: 'T',
                scale: 2,
                isHiddenDesktop: false,
            }),
            ui.nestedFields.label<Table, ShowCaseProduct>({
                bind: 'amount',
                title: 'Amount',
                prefix: '$',
                isHiddenDesktop: false,
            }),
            ui.nestedFields.reference<Table, ShowCaseProduct, ShowCaseProvider>({
                bind: 'provider',
                title: 'Provider',
                valueField: 'textField',
                node: '@sage/xtrem-show-case/ShowCaseProvider',
                minLookupCharacters: 0,
                imageField: 'logo',
                onClick(id: string, raw: any) {
                    ui.console.log('CLICKED NO LOOKUP', { id, raw });
                },
                onChange(id: string, raw: any) {
                    ui.console.log('CHANGED NO LOOKUP', { id, raw });
                },
                columns: null,
                tunnelPage: null,
            }),
            ui.nestedFields.reference<Table, ShowCaseProduct, ShowCaseProvider>({
                bind: 'provider',
                title: 'Provider',
                valueField: 'textField',
                node: '@sage/xtrem-show-case/ShowCaseProvider',
                minLookupCharacters: 0,
                columns: [
                    ui.nestedFields.text({ bind: '_id', title: 'ID', canFilter: false }),
                    ui.nestedFields.text({ bind: 'textField', title: 'Provider', canFilter: true }),
                    ui.nestedFields.image({ bind: 'logo', title: 'Logo', canFilter: false }),
                ],
                imageField: 'logo',
                onOpenLookupDialog() {
                    console.log('reference lookup dialog open');
                },
                onCloseLookupDialog() {
                    console.log('reference lookup dialog close');
                },
                onClick(id: string, raw: any) {
                    ui.console.log('CLICKED LOOKUP', { id, raw });
                },
                onChange(id: string, raw: any) {
                    ui.console.log('CHANGED LOOKUP', { id, raw });
                },
                tunnelPage: null,
            }),
            ui.nestedFields.filterSelect<Table, ShowCaseProduct, ShowCaseCustomer>({
                bind: 'email',
                title: 'Email',
                valueField: 'email',
                node: '@sage/xtrem-show-case/ShowCaseCustomer',
                minLookupCharacters: 0,
                width: 'large',
                onChange(id: string, raw: string) {
                    ui.console.log('CHANGED FILTER SELECT', { id, raw });
                },
            }),
            ui.nestedFields.date<Table, ShowCaseProduct>({
                bind: 'releaseDate',
                title: 'Date',
                maxDate(row) {
                    return row.endingDate;
                },
            }),
            ui.nestedFields.date({
                bind: 'endingDate',
                title: 'Ending date',
                minDate(row) {
                    return row.releaseDate;
                },
            }),
            ui.nestedFields.numeric({
                bind: 'discount',
                title: 'Discount',
            }),
            ui.nestedFields.select<Table, ShowCaseProduct>({
                bind: 'category',
                title: 'Category',
                optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
            }),
            ui.nestedFields.image({
                bind: 'imageField',
                title: 'Image',
            }),
            ui.nestedFields.icon({
                bind: 'qty',
                title: 'Icon',
                map: value => {
                    return value === 0 ? 'error' : value < 10 ? 'warning' : '';
                },
            }),
        ],
        orderBy: {
            product: 1,
        },
        parent() {
            return this.section;
        },
        onChange() {
            this.calculateTableTotals();
            this.calculateSelectedTotal();
        },
        onRowSelected(_id: string | number, item: any) {
            this.calculateSelectedTotal();
            this.$.showToast(`${item.product} selected.`);
        },
        onRowUnselected(_id: string | number, item: any) {
            this.calculateSelectedTotal();
            this.$.showToast(`${item.product} unselected.`, { type: 'warning' });
        },
        dropdownActions: [
            {
                icon: 'locked',
                title: 'Maybe disabled',
                isDisabled(id: any, row: any) {
                    return (row?.qty ?? 0) < 10;
                },
                onClick(rowId: any, data: any) {
                    this.$.showToast(data.product, { type: 'info' });
                },
            },
            {
                icon: 'minus',
                title: 'Remove',
                onClick(rowId: any) {
                    this.field.removeRecord(rowId);
                },
            },
            {
                title: 'Action no icon',
                isDisabled(id: any, row: any) {
                    return (row?.qty ?? 0) < 10;
                },
                onClick(rowId: any, data: any) {
                    this.$.showToast(data.product, { type: 'info' });
                },
            },
            {
                title: 'Refresh record',
                icon: 'refresh',
                async onClick(rowId) {
                    await this.field.refreshRecord(rowId);
                },
            },
        ],
    })
    fieldChildOfSection: ui.fields.Table<ShowCaseProduct>;

    @ui.decorators.checkboxField<Table>({
        parent() {
            return this.additionalBlock;
        },
        title: 'Is read-only',
        onChange() {
            this.fieldReadOnlyTable.isReadOnly = this.isReadOnly.value;
        },
        isTransient: true,
    })
    isReadOnly: ui.fields.Checkbox;

    @ui.decorators.tableField<Table, ShowCaseProduct>({
        isReadOnly: true,
        title: 'Table in read-only mode',
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        bind: 'products',
        mobileCard: {
            title: ui.nestedFields.date<Table, ShowCaseProduct>({
                bind: 'releaseDate',
                title: 'Date',
            }),
            titleRight: ui.nestedFields.label<Table, ShowCaseProduct>({
                bind: 'amount',
                title: 'Amount',
                prefix: '$',
                isHiddenDesktop: false,
            }),
            line2: ui.nestedFields.select<Table, ShowCaseProduct>({
                bind: 'category',
                title: 'Category',
                optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
            }),
            line2Right: ui.nestedFields.reference<Table, ShowCaseProduct, ShowCaseProvider>({
                bind: 'provider',
                title: 'Provider',
                valueField: 'textField',
                node: '@sage/xtrem-show-case/ShowCaseProvider',
                minLookupCharacters: 0,
                columns: [
                    ui.nestedFields.text({ bind: '_id', title: 'ID', canFilter: false }),
                    ui.nestedFields.text({ bind: 'textField', title: 'Provider', canFilter: true }),
                    ui.nestedFields.image({ bind: 'logo', title: 'Logo', canFilter: false }),
                ],
                helperTextField: '_id',
                imageField: 'logo',
                onOpenLookupDialog() {
                    console.log('reference lookup dialog open');
                },
                onCloseLookupDialog() {
                    console.log('reference lookup dialog close');
                },
                onClick(id: string, raw: any) {
                    ui.console.log('CLICKED LOOKUP', { id, raw });
                },
                onChange(id: string, raw: any) {
                    ui.console.log('CHANGED LOOKUP', { id, raw });
                },
            }),
        },
        hasSearchBoxMobile: true,
        canExport: true,
        canResizeColumns: true,
        columns: [
            ui.nestedFields.text({
                bind: '_id',
                title: 'Id',
                isHiddenDesktop: false,
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                bind: 'product',
                title: 'Product',
            }),
            ui.nestedFields.text({
                bind: 'description',
                title: 'Description',
                isDisabled() {
                    return !this.canEditDescription.value;
                },
            }),
            ui.nestedFields.numeric({
                bind: 'qty',
                title: 'Quantity',
                onChange(_id: number, rowData: ui.PartialNodeWithId<ShowCaseProduct>) {
                    this.updateRow(_id, rowData);
                },
                prefix() {
                    return this.quantityValuePrefix.value || '';
                },
                scale() {
                    return this.quantityValueScale.value || 0;
                },
                fetchesDefaults: true,
            }),
            ui.nestedFields.numeric({
                bind: 'fixedQuantity',
                title: 'Fixed Quantity',
                isTransientInput: true,
            }),
            ui.nestedFields.numeric({
                bind: 'listPrice',
                title: 'List Price',
                scale: 2,
                onChange(_id: number, rowData: ui.PartialNodeWithId<ShowCaseProduct>) {
                    this.updateRow(_id, rowData);
                },
                isHiddenDesktop: false,
                fetchesDefaults: true,
            }),
            ui.nestedFields.numeric({
                bind: 'netPrice',
                title: 'Net Price',
                isReadOnly: (value: number, rowValue: any) => {
                    return !!rowValue.hotProduct;
                },
                scale: 2,
                isHiddenDesktop: false,
            }),
            ui.nestedFields.label({
                backgroundColor(_fieldValue: number, rowData: ShowCaseProduct) {
                    return rowData.qty > this.highValueLimit.value
                        ? '00b000'
                        : rowData.qty > this.mediumValueLimit.value
                          ? 'ffb700'
                          : 'e96400';
                },
                bind: 'qty',
                isTransient: true,
                map(_fieldValue, rowData) {
                    return rowData.qty > this.highValueLimit.value
                        ? 'High'
                        : rowData.qty > this.mediumValueLimit.value
                          ? 'Medium'
                          : 'Low';
                },
                title: 'Indicator',
            }),
            ui.nestedFields.label({
                bind: 'st',
                onClick(_id: string | number, rowData) {
                    this.$.dialog.message('info', 'Label was clicked', `Label ${rowData.st} was clicked (row ${_id})`);
                },
                title: 'St',
            }),
            ui.nestedFields.checkbox({
                bind: 'hotProduct',
                title: 'Hot',
            }),
            ui.nestedFields.switch({
                bind: 'hotProduct',
                title: 'Hot (Switch)',
            }),
            ui.nestedFields.link({
                bind: 'product',
                isTransient: true,
                map(_fieldValue, rowData) {
                    return `http://${(rowData?.product ?? ('' as string))
                        .replace(/-|_|\s/g, '')
                        .substring(0, 4)
                        .toLowerCase()}.sage.com`;
                },
                onClick(id: string, raw: any) {
                    ui.console.log('CLICKED LINK', { id, raw });
                },
                title: 'Link',
                page: 'https://www.google.com',
            }),
            ui.nestedFields.progress({
                bind: 'progress',
                title: 'Progress',
            }),
            ui.nestedFields.numeric({
                bind: 'tax',
                title: 'Tax',
                prefix: 'T',
                scale: 2,
                isHiddenDesktop: false,
            }),
            ui.nestedFields.label<Table, ShowCaseProduct>({
                bind: 'amount',
                title: 'Amount',
                prefix: '$',
                isHiddenDesktop: false,
            }),
            ui.nestedFields.reference<Table, ShowCaseProduct, ShowCaseProvider>({
                bind: 'provider',
                title: 'Provider',
                valueField: 'textField',
                node: '@sage/xtrem-show-case/ShowCaseProvider',
                minLookupCharacters: 0,
                imageField: 'logo',
                onClick(id: string, raw: any) {
                    ui.console.log('CLICKED NO LOOKUP', { id, raw });
                },
                onChange(id: string, raw: any) {
                    ui.console.log('CHANGED NO LOOKUP', { id, raw });
                },
                columns: null,
                tunnelPage: null,
            }),
            ui.nestedFields.reference<Table, ShowCaseProduct, ShowCaseProvider>({
                bind: 'provider',
                title: 'Provider',
                valueField: 'textField',
                node: '@sage/xtrem-show-case/ShowCaseProvider',
                minLookupCharacters: 0,
                columns: [
                    ui.nestedFields.text({ bind: '_id', title: 'ID', canFilter: false }),
                    ui.nestedFields.text({ bind: 'textField', title: 'Provider', canFilter: true }),
                    ui.nestedFields.image({ bind: 'logo', title: 'Logo', canFilter: false }),
                ],
                tunnelPage: '@sage/xtrem-show-case/StandardShowCaseProvider',
                imageField: 'logo',
                onOpenLookupDialog() {
                    console.log('reference lookup dialog open');
                },
                onCloseLookupDialog() {
                    console.log('reference lookup dialog close');
                },
                onClick(id: string, raw: any) {
                    ui.console.log('CLICKED LOOKUP', { id, raw });
                },
                onChange(id: string, raw: any) {
                    ui.console.log('CHANGED LOOKUP', { id, raw });
                },
            }),
            ui.nestedFields.filterSelect<Table, ShowCaseProduct, ShowCaseCustomer>({
                bind: 'email',
                title: 'Email',
                valueField: 'email',
                node: '@sage/xtrem-show-case/ShowCaseCustomer',
                minLookupCharacters: 0,
                width: 'large',
                onChange(id: string, raw: string) {
                    ui.console.log('CHANGED FILTER SELECT', { id, raw });
                },
            }),
            ui.nestedFields.date<Table, ShowCaseProduct>({
                bind: 'releaseDate',
                title: 'Date',
                maxDate(row) {
                    return row.endingDate;
                },
            }),
            ui.nestedFields.date({
                bind: 'endingDate',
                title: 'Ending date',
                minDate(row) {
                    return row.releaseDate;
                },
            }),
            ui.nestedFields.numeric({
                bind: 'discount',
                title: 'Discount',
            }),
            ui.nestedFields.select<Table, ShowCaseProduct>({
                bind: 'category',
                title: 'Category',
                optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
            }),
            ui.nestedFields.image({
                bind: 'imageField',
                title: 'Image',
            }),
            ui.nestedFields.icon({
                bind: 'qty',
                title: 'Icon',
                map: value => {
                    return value === 0 ? 'error' : value < 10 ? 'warning' : '';
                },
            }),
        ],
        orderBy: {
            product: 1,
        },
        parent() {
            return this.additionalBlock;
        },
        onChange() {
            this.calculateTableTotals();
            this.calculateSelectedTotal();
        },
        onRowSelected(_id: string | number, item: any) {
            this.calculateSelectedTotal();
            this.$.showToast(`${item.product} selected.`);
        },
        onRowUnselected(_id: string | number, item: any) {
            this.calculateSelectedTotal();
            this.$.showToast(`${item.product} unselected.`, { type: 'warning' });
        },
        dropdownActions: [
            {
                icon: 'locked',
                title: 'Maybe disabled',
                isDisabled(id: any, row: any) {
                    return (row?.qty ?? 0) < 10;
                },
                onClick(rowId: any, data: any) {
                    this.$.showToast(data.product, { type: 'info' });
                },
            },
            {
                icon: 'minus',
                title: 'Remove',
                onClick(rowId: any) {
                    this.field.removeRecord(rowId);
                },
            },
            {
                title: 'Action no icon',
                isDisabled(id: any, row: any) {
                    return (row?.qty ?? 0) < 10;
                },
                onClick(rowId: any, data: any) {
                    this.$.showToast(data.product, { type: 'info' });
                },
            },
            {
                title: 'Refresh record',
                icon: 'refresh',
                async onClick(rowId) {
                    await this.field.refreshRecord(rowId);
                },
            },
        ],
    })
    fieldReadOnlyTable: ui.fields.Table<ShowCaseProduct>;

    @ui.decorators.tableField<Table, ShowCaseProduct>({
        isReadOnly: true,
        title: 'Table with line numbers',
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        bind: 'products',
        hasLineNumbers: true,
        columns: [
            ui.nestedFields.text({
                bind: '_id',
                title: 'Id',
                isHiddenDesktop: false,
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                bind: 'product',
                title: 'Product',
            }),
            ui.nestedFields.text({
                bind: 'description',
                title: 'Description',
                isDisabled() {
                    return !this.canEditDescription.value;
                },
            }),
            ui.nestedFields.numeric({
                bind: 'qty',
                title: 'Quantity',
                onChange(_id: number, rowData: ui.PartialNodeWithId<ShowCaseProduct>) {
                    this.updateRow(_id, rowData);
                },
                prefix() {
                    return this.quantityValuePrefix.value || '';
                },
                scale() {
                    return this.quantityValueScale.value || 0;
                },
                fetchesDefaults: true,
            }),
            ui.nestedFields.numeric({
                bind: 'fixedQuantity',
                title: 'Fixed Quantity',
                isTransientInput: true,
            }),
            ui.nestedFields.numeric({
                bind: 'listPrice',
                title: 'List Price',
                scale: 2,
                onChange(_id: number, rowData: ui.PartialNodeWithId<ShowCaseProduct>) {
                    this.updateRow(_id, rowData);
                },
                isHiddenDesktop: false,
                fetchesDefaults: true,
            }),
            ui.nestedFields.numeric({
                bind: 'netPrice',
                title: 'Net Price',
                isReadOnly: (value: number, rowValue: any) => {
                    return !!rowValue.hotProduct;
                },
                scale: 2,
                isHiddenDesktop: false,
            }),
            ui.nestedFields.label({
                bind: 'st',
                onClick(_id: string | number, rowData) {
                    this.$.dialog.message('info', 'Label was clicked', `Label ${rowData.st} was clicked (row ${_id})`);
                },
                title: 'St',
            }),
            ui.nestedFields.checkbox({
                bind: 'hotProduct',
                title: 'Hot',
            }),
            ui.nestedFields.switch({
                bind: 'hotProduct',
                title: 'Hot (Switch)',
            }),
            ui.nestedFields.progress({
                bind: 'progress',
                title: 'Progress',
            }),
            ui.nestedFields.numeric({
                bind: 'tax',
                title: 'Tax',
                prefix: 'T',
                scale: 2,
                isHiddenDesktop: false,
            }),
            ui.nestedFields.label<Table, ShowCaseProduct>({
                bind: 'amount',
                title: 'Amount',
                prefix: '$',
                isHiddenDesktop: false,
            }),
            ui.nestedFields.date<Table, ShowCaseProduct>({
                bind: 'releaseDate',
                title: 'Date',
                maxDate(row) {
                    return row.endingDate;
                },
            }),
            ui.nestedFields.date({
                bind: 'endingDate',
                title: 'Ending date',
                minDate(row) {
                    return row.releaseDate;
                },
            }),
            ui.nestedFields.numeric({
                bind: 'discount',
                title: 'Discount',
            }),
            ui.nestedFields.select<Table, ShowCaseProduct>({
                bind: 'category',
                title: 'Category',
                optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
            }),
            ui.nestedFields.image({
                bind: 'imageField',
                title: 'Image',
            }),
        ],
        orderBy: {
            product: 1,
        },
        parent() {
            return this.additionalBlock;
        },
        onChange() {
            this.calculateTableTotals();
            this.calculateSelectedTotal();
        },
        onRowSelected(_id: string | number, item: any) {
            this.calculateSelectedTotal();
            this.$.showToast(`${item.product} selected.`);
        },
        onRowUnselected(_id: string | number, item: any) {
            this.calculateSelectedTotal();
            this.$.showToast(`${item.product} unselected.`, { type: 'warning' });
        },
    })
    fieldLineNumbers: ui.fields.Table<ShowCaseProduct>;
}
