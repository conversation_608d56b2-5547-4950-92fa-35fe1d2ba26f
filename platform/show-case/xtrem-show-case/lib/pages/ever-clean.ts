import { <PERSON>raph<PERSON><PERSON>, ShowCaseProduct as ShowCaseProductNode, ShowCaseProvider } from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import { misc } from '../menu-items/misc';

@ui.decorators.page<EverClean, ShowCaseProductNode>({
    authorizationCode: 'SHCPRDT',
    module: 'show-case',
    title: 'Ever Clean Page',
    node: '@sage/xtrem-show-case/ShowCaseProduct',
    menuItem: misc,
    category: 'SHOWCASE',
    onLoad() {
        this.$.storage.set('filterNavPanelItems', true);
    },
    onClose() {
        ui.console.log('on close main called');
    },
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.text({ bind: 'product' }),
            titleRight: ui.nestedFields.text({ bind: '_id' }),
            line2: ui.nestedFields.text({ bind: 'description', canFilter: false }),
        },
    },
    skipDirtyCheck: true,
    businessActions() {
        return [this.refreshPage, this.emptyPage, this.saveShowCaseProduct];
    },
    headerDropDownActions() {
        return [this.$standardDeleteAction];
    },
})
export class EverClean extends ui.Page<GraphApi> {
    @ui.decorators.section<EverClean>({
        title: 'This page never gets dirty!',
    })
    section: ui.containers.Section;

    @ui.decorators.block<EverClean>({
        parent() {
            return this.section;
        },
    })
    block: ui.containers.Block;

    @ui.decorators.textField<EverClean>({
        parent() {
            return this.block;
        },
        title: 'ID',
        isReadOnly: true,
    })
    _id: ui.fields.Text;

    @ui.decorators.textField<EverClean>({
        parent() {
            return this.block;
        },
        title: 'Product',
        isMandatory: true,
    })
    product: ui.fields.Text;

    @ui.decorators.textField<EverClean>({
        parent() {
            return this.block;
        },
        title: 'Description',
    })
    description: ui.fields.Text;

    @ui.decorators.checkboxField<EverClean>({
        parent() {
            return this.block;
        },
        title: 'Hot product',
    })
    hotProduct: ui.fields.Checkbox;

    @ui.decorators.numericField<EverClean>({
        parent() {
            return this.block;
        },
        title: 'Quantity',
        fetchesDefaults: true,
    })
    qty: ui.fields.Numeric;

    @ui.decorators.numericField<EverClean>({
        parent() {
            return this.block;
        },
        title: 'Stock',
    })
    st: ui.fields.Numeric;

    @ui.decorators.numericField<EverClean>({
        parent() {
            return this.block;
        },
        title: 'List price',
        scale: 2,
        fetchesDefaults: true,
    })
    listPrice: ui.fields.Numeric;

    @ui.decorators.progressField<EverClean>({
        parent() {
            return this.block;
        },
        title: 'Progress',
    })
    progress: ui.fields.Progress;

    @ui.decorators.numericField<EverClean>({
        parent() {
            return this.block;
        },
        title: 'Tax',
        scale: 2,
    })
    tax: ui.fields.Numeric;

    @ui.decorators.numericField<EverClean>({
        parent() {
            return this.block;
        },
        title: 'Net price',
        scale: 2,
    })
    netPrice: ui.fields.Numeric;

    @ui.decorators.numericField<EverClean>({
        parent() {
            return this.block;
        },
        title: 'Discount',
    })
    discount: ui.fields.Numeric;

    @ui.decorators.numericField<EverClean>({
        parent() {
            return this.block;
        },
        title: 'Amount',
        scale: 2,
    })
    amount: ui.fields.Numeric;

    @ui.decorators.referenceField<EverClean, ShowCaseProvider>({
        parent() {
            return this.block;
        },
        columns: [ui.nestedFields.text({ bind: 'textField' })],
        node: '@sage/xtrem-show-case/ShowCaseProvider',
        title: 'Provider',
        valueField: 'textField',
        helperTextField: '_id',
        minLookupCharacters: 0,
    })
    provider: ui.fields.Reference;

    @ui.decorators.dateField<EverClean>({
        parent() {
            return this.block;
        },
        title: 'Release date',
    })
    releaseDate: ui.fields.Date;

    @ui.decorators.dateField<EverClean>({
        parent() {
            return this.block;
        },
        title: 'Ending date',
    })
    endingDate: ui.fields.Date;

    @ui.decorators.radioField<EverClean>({
        parent() {
            return this.block;
        },
        title: 'Category Select',
        optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
    })
    category: ui.fields.Radio;

    @ui.decorators.pageAction<EverClean>({
        title: 'Create',
        onClick() {
            this.$.loader.display();
            const values = this.$.values;
            delete values._id;
            return this.$.graph
                .create({ values })
                .then(() => {
                    this.$.finish();
                })
                .catch(() => {
                    this.$.showToast('Something went wrong', { type: 'error' });
                })
                .finally(() => {
                    this.$.loader.hide();
                });
        },
    })
    createShowCaseProduct: ui.PageAction;

    @ui.decorators.pageAction<EverClean>({
        title: 'Save',
        async onClick() {
            this.$.loader.display();
            const result = await this.$.page.validateWithDetails();

            if (result.length) {
                // INFO: Check used to demonstrate internal component error in the integration tests, as
                //       I was unable to test a toast notification directly.
                const dateResult = result.find(validationResult => validationResult.validationRule === 'dateValue');

                if (dateResult) {
                    this.$.dialog.message('error', 'Validation Error', dateResult.message, {
                        fullScreen: false,
                        rightAligned: false,
                        acceptButton: {
                            isDisabled: false,
                            isHidden: false,
                            text: 'OK',
                        },
                    });
                } else {
                    this.$.showToast(result.map(r => r.message).join(', '), { type: 'warning' });
                }

                this.$.loader.hide();
                return;
            }

            await this.$.graph
                .update()
                .then(() => {
                    this.$.finish();
                })
                .catch(() => {
                    this.$.showToast('Something went wrong', { type: 'error' });
                })
                .finally(() => {
                    this.$.loader.hide();
                });
        },
    })
    saveShowCaseProduct: ui.PageAction;

    @ui.decorators.pageAction<EverClean>({
        title: 'Refresh',
        onClick() {
            return this.$.router.refresh();
        },
    })
    refreshPage: ui.PageAction;

    @ui.decorators.pageAction<EverClean>({
        title: 'Empty page',
        onClick() {
            return this.$.router.emptyPage();
        },
    })
    emptyPage: ui.PageAction;

    @ui.decorators.pageAction<EverClean>({
        title: 'Delete',
        async onClick() {
            const options: ui.dialogs.DialogOptions = {
                acceptButton: {
                    text: 'Yes',
                },
                cancelButton: {
                    text: 'No',
                },
            };
            if (
                await this.$.dialog
                    .confirmation(
                        'warn',
                        ui.localize('@sage/xtrem-show-case/delete-dialog-title', 'Confirm deletion'),
                        ui.localize(
                            '@sage/xtrem-show-case/delete-dialog-content',
                            'You are about to delete this record.',
                        ),
                        options,
                    )
                    .then(() => true)
                    .catch(() => false)
            ) {
                this.$.loader.display();
                try {
                    await this.$.graph.delete();
                    this.$.showToast(ui.localize('@sage/xtrem-show-case/delete-confirmation', 'Record deleted'), {
                        type: 'success',
                    });
                } catch (e) {
                    this.$.showToast(e.message, { timeout: 0, type: 'error' });
                }
                this.$.router.goTo(`@sage/xtrem-show-case/${this.$.page.id}`);
                this.$.loader.hide();
            }
        },
    })
    deleteShowCaseProduct: ui.PageAction;
}
