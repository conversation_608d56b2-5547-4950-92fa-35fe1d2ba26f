import {
    <PERSON>raph<PERSON><PERSON>,
    ShowCaseCustomer as ShowCaseCustomerNode,
    ShowCaseInvoice,
    ShowCaseInvoiceLine,
} from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import { applicationPages } from '../menu-items/application-pages';

@ui.decorators.page<ShowCaseCustomer, ShowCaseCustomerNode>({
    authorizationCode: 'SHCCSTMR',
    category: 'SHOWCASE',
    idField() {
        return [this.name];
    },
    menuItem: applicationPages,
    module: 'show-case',
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.text({ bind: 'name', title: 'Name' }),
            titleRight: ui.nestedFields.text({ bind: '_id', title: 'Id' }),
            line2: ui.nestedFields.text({ bind: 'email', title: 'Email' }),
            line2Right: ui.nestedFields.aggregate({
                bind: 'invoices',
                title: 'Invoices',
                aggregateOn: '_id',
                aggregationMethod: 'distinctCount',
            }),
        },
    },
    node: '@sage/xtrem-show-case/ShowCaseCustomer',
    objectTypeSingular: 'Customer',
    objectTypePlural: 'Customers',
    title: 'ShowCase - Customer',
})
export class ShowCaseCustomer extends ui.Page<GraphApi> {
    @ui.decorators.section<ShowCaseCustomer>({})
    section: ui.containers.Section;

    @ui.decorators.block<ShowCaseCustomer>({
        parent() {
            return this.section;
        },
    })
    block: ui.containers.Block;

    @ui.decorators.textField<ShowCaseCustomer>({
        bind: '_id',
        isReadOnly: true,
        parent() {
            return this.block;
        },
        title: 'Id',
    })
    id: ui.fields.Text;

    @ui.decorators.textField<ShowCaseCustomer>({
        bind: 'name',
        isReadOnly: true,
        parent() {
            return this.block;
        },
        title: 'Name',
    })
    name: ui.fields.Text;

    @ui.decorators.nestedGridField<ShowCaseCustomer, [ShowCaseInvoice, ShowCaseInvoiceLine]>({
        bind: 'invoices',
        canActivate: false,
        canFilter: true,
        canSelect: false,
        levels: [
            {
                childProperty: 'lines',
                columns: [
                    ui.nestedFields.text({
                        bind: 'purchaseDate',
                        title: 'Purchase Date',
                    }),
                    ui.nestedFields.numeric({
                        bind: 'totalProductQty',
                        scale: 0,
                        title: 'Total Quantity',
                    }),
                    ui.nestedFields.text({
                        bind: '_sortValue',
                        title: '(Sort Value)',
                    }),
                ],
                node: '@sage/xtrem-show-case/ShowCaseInvoice',
                orderBy: { _sortValue: 1 },
            },
            {
                columns: [
                    ui.nestedFields.reference({
                        bind: 'product',
                        node: '@sage/xtrem-show-case/ShowCaseProduct',
                        title: 'Product',
                        valueField: 'product',
                    }),
                    ui.nestedFields.numeric({
                        bind: 'netPrice',
                        scale: 2,
                        prefix: '$',
                        title: 'Unit Price',
                    }),
                    ui.nestedFields.numeric({
                        bind: 'orderQuantity',
                        scale: 0,
                        title: 'Quantity',
                    }),
                ],
                node: '@sage/xtrem-show-case/ShowCaseInvoiceLine',
                orderBy: { netPrice: -1 },
            },
        ],
        node: '@sage/xtrem-show-case/ShowCaseInvoice',
        parent() {
            return this.block;
        },
        title: 'Invoices',
    })
    invoices: ui.fields.NestedGrid<[ShowCaseInvoice, ShowCaseInvoiceLine]>;
}
