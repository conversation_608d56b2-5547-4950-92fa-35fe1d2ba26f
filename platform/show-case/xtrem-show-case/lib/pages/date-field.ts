import * as ui from '@sage/xtrem-ui';
import { fields } from '../menu-items/fields';

function printDateInFormats(isoDate: string): string {
    return `${ui.formatDateToCurrentLocale(isoDate)} ${ui.formatDateToCurrentLocale(
        isoDate,
        'LongMonth',
    )} ${ui.formatDateToCurrentLocale(isoDate, 'LongMonthYear')} ${ui.formatDateToCurrentLocale(
        isoDate,
        'FullDate',
    )} ${ui.formatDateToCurrentLocale(isoDate, 'MonthYear')} ${ui.formatDateToCurrentLocale(isoDate, 'MonthDay')}`;
}

@ui.decorators.page<DateField>({
    authorizationCode: 'BSCFLDS',
    module: 'show-case',
    title: 'Field - Date',
    isTransient: true,
    category: 'SHOWCASE',
    onLoad() {
        this.field.value = '2019-07-22';
        this.value.value = '2019-07-22';
        this.dateFormatsField.value = printDateInFormats(this.field.value);
    },
    menuItem: fields,
})
export class DateField extends ui.Page {
    @ui.decorators.section<DateField>({
        title: 'Date field',
    })
    section: ui.containers.Section;

    @ui.decorators.block<DateField>({
        parent() {
            return this.section;
        },
        title: 'Field example',
    })
    fieldBlock: ui.containers.Block;

    @ui.decorators.dateField<DateField>({
        parent() {
            return this.fieldBlock;
        },
        onClick() {
            this.clickTriggered.isHidden = false;
            setTimeout(() => {
                this.clickTriggered.isHidden = true;
            }, 5000);
        },
        onChange() {
            if (this.field.value !== this.value.value) {
                this.value.value = this.field.value;
                this.changeTriggered.isHidden = false;
                setTimeout(() => {
                    this.changeTriggered.isHidden = true;
                }, 5000);
            }
            this.dateFormatsField.value = printDateInFormats(this.field.value);
        },
    })
    field: ui.fields.Date;

    @ui.decorators.labelField<DateField>({
        parent() {
            return this.fieldBlock;
        },
        isHidden: true,
        map() {
            return 'Change was triggered';
        },
    })
    changeTriggered: ui.fields.Label;

    @ui.decorators.labelField<DateField>({
        parent() {
            return this.fieldBlock;
        },
        isHidden: true,
        map() {
            return 'Click was triggered';
        },
    })
    clickTriggered: ui.fields.Label;

    @ui.decorators.block<DateField>({
        parent() {
            return this.section;
        },
        title: 'Configuration',
    })
    configurationBlock: ui.containers.Block;

    @ui.decorators.textField<DateField>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Helper text',
        onChange() {
            this.field.helperText = this.helperText.value;
        },
    })
    helperText: ui.fields.Text;

    @ui.decorators.checkboxField<DateField>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is disabled',
        onChange() {
            this.field.isDisabled = this.isDisabled.value;
        },
    })
    isDisabled: ui.fields.Checkbox;

    @ui.decorators.checkboxField<DateField>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is helper text hidden',
        onChange() {
            this.field.isHelperTextHidden = this.isHelperTextHidden.value;
        },
    })
    isHelperTextHidden: ui.fields.Checkbox;

    @ui.decorators.checkboxField<DateField>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is hidden',
        onChange() {
            this.field.isHidden = this.isHidden.value;
        },
    })
    isHidden: ui.fields.Checkbox;

    @ui.decorators.checkboxField<DateField>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is readOnly',
        onChange() {
            this.field.isReadOnly = this.isReadOnly.value;
        },
    })
    isReadOnly: ui.fields.Checkbox;

    @ui.decorators.checkboxField<DateField>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is title hidden',
        onChange() {
            this.field.isTitleHidden = this.isTitleHidden.value;
        },
    })
    isTitleHidden: ui.fields.Checkbox;

    @ui.decorators.textField<DateField>({
        title: 'Placeholder',
        onChange() {
            this.field.placeholder = this.placeholder.value;
        },
        parent() {
            return this.configurationBlock;
        },
    })
    placeholder: ui.fields.Text;

    @ui.decorators.textField<DateField>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Title',
        onChange() {
            this.field.title = this.title.value;
        },
    })
    title: ui.fields.Text;

    @ui.decorators.dateField<DateField>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Value',
        onChange() {
            if (this.value.value !== this.field.value) {
                this.field.value = this.value.value;
            }
        },
    })
    value: ui.fields.Date;

    @ui.decorators.buttonField<DateField>({
        parent() {
            return this.configurationBlock;
        },
        map() {
            return 'Focus field and then open a dialog field';
        },
        onClick() {
            this.field.focus();
            setTimeout(() => {
                this.$.dialog.message('info', 'Test dialog', 'It is really just to test a bug');
            }, 1000);
        },
    })
    openDialogButton: ui.fields.Button;

    @ui.decorators.buttonField<DateField>({
        parent() {
            return this.configurationBlock;
        },
        map() {
            return 'Focus field';
        },
        onClick() {
            this.field.focus();
        },
    })
    focus: ui.fields.Button;

    @ui.decorators.buttonField<DateField>({
        parent() {
            return this.configurationBlock;
        },
        map() {
            return 'Disable in 2s';
        },
        onClick() {
            setTimeout(() => {
                this.field.isDisabled = true;
                this.isDisabled.value = true;
            }, 2000);
        },
    })
    disableInASecond: ui.fields.Button;

    /* Additional examples */

    @ui.decorators.block<DateField>({
        parent() {
            return this.section;
        },
        title: 'Additional examples',
    })
    additionalBlock: ui.containers.Block;

    @ui.decorators.dateField<DateField>({
        parent() {
            return this.additionalBlock;
        },
        title: 'Mandatory',
        isMandatory: true,
    })
    mandatory: ui.fields.Date;

    @ui.decorators.buttonField<DateField>({
        parent() {
            return this.additionalBlock;
        },
        map() {
            return 'Validate this field';
        },
        async onClick() {
            const result = await this.mandatory.validate();
            this.$.dialog.message('info', 'Validation result', result || 'All good');
        },
    })
    validateMandatory: ui.fields.Button;

    @ui.decorators.dateField<DateField>({
        parent() {
            return this.additionalBlock;
        },
        title: 'Before today',
        maxDate: new Date(),
    })
    maxDate: ui.fields.Date;

    @ui.decorators.dateField<DateField>({
        parent() {
            return this.additionalBlock;
        },
        title: 'After today',
        minDate: new Date(),
    })
    minDate: ui.fields.Date;

    @ui.decorators.dateField<DateField>({
        parent() {
            return this.additionalBlock;
        },
        title: 'On or before 2023-02-22',
        maxDate: '2023-02-22',
    })
    maxDateByDateString: ui.fields.Date;

    @ui.decorators.dateField<DateField>({
        parent() {
            return this.additionalBlock;
        },
        title: 'On or after 2023-02-22',
        minDate: new Date(),
    })
    minDateByDateString: ui.fields.Date;

    @ui.decorators.dateField<DateField>({
        parent() {
            return this.additionalBlock;
        },
        title: 'Full width',
        isFullWidth: true,
    })
    fullWidth: ui.fields.Date;

    @ui.decorators.dateField<DateField>({
        parent() {
            return this.additionalBlock;
        },
        title: 'Future date',
        validation(value) {
            const date = new Date(value);
            const now = new Date();
            return date < now ? 'You have to choose a date in the future' : undefined;
        },
    })
    customValidation: ui.fields.Date;

    @ui.decorators.dateField<DateField>({
        parent() {
            return this.additionalBlock;
        },
        title: 'Date properties as String or Date',
        minDate: new Date(`${new Date().getFullYear()}-01-01`),
        maxDate: `${new Date().getFullYear()}-12-31`,
        onChange() {
            this.minMaxValidation.minDate = `${(this.minMaxValidation.minDate as Date).getFullYear() - 1}-01-01`;
            this.minMaxValidation.maxDate = new Date(
                `${(this.minMaxValidation.maxDate as Date).getFullYear() + 1}-12-31`,
            );
            ui.console.log(
                `Min Date changed to ${this.minMaxValidation.minDate} and Max Date to ${this.minMaxValidation.maxDate}`,
            );
        },
    })
    minMaxValidation: ui.fields.Date;

    @ui.decorators.buttonField<DateField>({
        parent() {
            return this.additionalBlock;
        },
        map() {
            return 'Validate this block';
        },
        async onClick() {
            const result = await this.additionalBlock.validate();
            this.$.dialog.message('info', 'Validation result', result.length === 0 ? 'All good.' : result.join('\n'));
        },
    })
    validateBlock: ui.fields.Button;

    @ui.decorators.block<DateField>({
        parent() {
            return this.section;
        },
        title: 'Supported date formats',
    })
    dateFormats: ui.containers.Block;

    @ui.decorators.textField<DateField>({
        isReadOnly: true,
        parent() {
            return this.dateFormats;
        },
    })
    dateFormatsField: ui.fields.Text;

    @ui.decorators.separatorField<DateField>({
        parent() {
            return this.additionalBlock;
        },
        isFullWidth: true,
    })
    fieldSeparator1: ui.fields.Separator;

    @ui.decorators.dateField<DateField>({
        parent() {
            return this.additionalBlock;
        },
        title: 'With warning message',
        warningMessage: 'Wow, warning!',
    })
    warningMessageField: ui.fields.Date;

    @ui.decorators.dateField<DateField>({
        parent() {
            return this.additionalBlock;
        },
        title: 'With warning message with callback',
        helperText: 'Select "2022-04-27"',
        warningMessage() {
            if (this.warningMessageWithCallbackField.value === '2022-04-27') {
                return 'Warning message';
            }
            return null;
        },
    })
    warningMessageWithCallbackField: ui.fields.Date;

    @ui.decorators.dateField<DateField>({
        parent() {
            return this.additionalBlock;
        },
        title: 'With info message',
        infoMessage: 'Wow, warning!',
    })
    infoMessageField: ui.fields.Date;

    @ui.decorators.dateField<DateField>({
        parent() {
            return this.additionalBlock;
        },
        title: 'With info message with callback',
        helperText: 'Select "2022-04-27"',
        infoMessage() {
            if (this.infoMessageWithCallbackField.value === '2022-04-27') {
                return 'Info message';
            }
            return null;
        },
    })
    infoMessageWithCallbackField: ui.fields.Date;

    @ui.decorators.dateField<DateField>({
        parent() {
            return this.additionalBlock;
        },
        title: 'With info message',
        warningMessage: 'Wow, warning!',
        infoMessage: 'You should not see this',
    })
    infoAndWarningMessageField: ui.fields.Date;

    @ui.decorators.dateField<DateField>({
        parent() {
            return this.additionalBlock;
        },
        validation() {
            if (this.infoAndWarningMessageMandatoryField.value === '2022-04-27') {
                return 'Error message';
            }
            return '';
        },
        title: 'Info, warning and validation',
        warningMessage: 'Wow, warning!',
        infoMessage: 'You should not see this',
        helperText: 'Not allowed to select 2022-04-27',
    })
    infoAndWarningMessageMandatoryField: ui.fields.Date;
}
