import { Graph<PERSON><PERSON>, ShowCaseProduct, ShowCaseProvider } from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import { fields } from '../menu-items/fields';

@ui.decorators.page<Calendar, ShowCaseProvider>({
    authorizationCode: 'BSCFLDS',
    module: 'show-case',
    title: 'Field - Calendar',
    node: '@sage/xtrem-show-case/ShowCaseProvider',
    category: 'SHOWCASE',
    navigationPanel: {
        listItem: {
            titleRight: ui.nestedFields.text({ bind: 'dateField' }),
            title: ui.nestedFields.text({ bind: '_id' }),
        },
    },
    defaultEntry: () => '1',
    menuItem: fields,
})
export class Calendar extends ui.Page<GraphApi> {
    @ui.decorators.section<Calendar>({
        title: 'Calendar field',
    })
    section: ui.containers.Section;

    @ui.decorators.block<Calendar>({
        parent() {
            return this.section;
        },
        title: 'Field example',
    })
    fieldBlock: ui.containers.Block;

    @ui.decorators.calendarField<Calendar, ShowCaseProduct>({
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        bind: 'products',
        endDateField: 'endingDate',
        startDateField: 'releaseDate', // Event Start date if no end-date then will be full day.
        fieldActions() {
            return [this.fieldActionTest];
        },
        parent() {
            return this.fieldBlock;
        },
        onDayClick(date) {
            this.$.dialog.message('info', 'Day clicked', date && date.toString());
        },
        onEventClick(_id, event) {
            this.$.dialog.message('info', 'Event clicked', event.product);
        },
        cardColor(v) {
            return Number(v.listPrice) > 20 ? undefined : ui.tokens.colorsSemanticPositive500;
        },
        eventCard: {
            title: ui.nestedFields.text({
                bind: 'product',
                onClick(id, data: any) {
                    this.$.dialog.message(
                        'info',
                        'Nested field clicked',
                        `Clicked field ${id} with title ${data.product}`,
                    );
                },
            }),
            titleRight: ui.nestedFields.numeric({
                bind: 'tax',
                canFilter: true,
            }),
            line2: ui.nestedFields.text({ bind: 'listPrice', canFilter: true }),
            line2Right: ui.nestedFields.numeric({
                bind: 'qty',
                canFilter: true,
            }),
            image: ui.nestedFields.image({ bind: 'imageField' }),
        },
    })
    field: ui.fields.Calendar;

    @ui.decorators.block<Calendar>({
        parent() {
            return this.section;
        },
        title: 'Configuration',
    })
    configurationBlock: ui.containers.Block;

    @ui.decorators.textField<Calendar>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Helper text',
        onChange() {
            this.field.helperText = this.helperText.value;
        },
        isTransient: true,
    })
    helperText: ui.fields.Text;

    @ui.decorators.checkboxField<Calendar>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is disabled',
        onChange() {
            this.field.isDisabled = this.isDisabled.value;
        },
        isTransient: true,
    })
    isDisabled: ui.fields.Checkbox;

    @ui.decorators.checkboxField<Calendar>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is helper text hidden',
        onChange() {
            this.field.isHelperTextHidden = this.isHelperTextHidden.value;
        },
        isTransient: true,
    })
    isHelperTextHidden: ui.fields.Checkbox;

    @ui.decorators.checkboxField<Calendar>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is hidden',
        onChange() {
            this.field.isHidden = this.isHidden.value;
        },
        isTransient: true,
    })
    isHidden: ui.fields.Checkbox;

    @ui.decorators.checkboxField<Calendar>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is title hidden',
        onChange() {
            this.field.isTitleHidden = this.isTitleHidden.value;
        },
        isTransient: true,
    })
    isTitleHidden: ui.fields.Checkbox;

    @ui.decorators.textField<Calendar>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Title',
        onChange() {
            this.field.title = this.title.value;
        },
        isTransient: true,
    })
    title: ui.fields.Text;

    @ui.decorators.pageAction<Calendar>({
        title: 'Field action',
        icon: 'fax',
        onClick() {
            this.$.showToast('Field action triggered');
        },
    })
    fieldActionTest: ui.PageAction;

    /* Additional examples */

    @ui.decorators.block<Calendar>({
        parent() {
            return this.section;
        },
        title: 'Additional examples',
    })
    additionalBlock: ui.containers.Block;

    @ui.decorators.calendarField<Calendar>({
        bind: 'products',
        startDateField: 'releaseDate',
        endDateField: 'endingDate',
        parent() {
            return this.additionalBlock;
        },
        maxDate: new Date(), // Max date user allowed to navigate
        minDate: new Date(), // Min date user allowed to navigate
        title: 'Max & min date',
        eventCard: {
            title: ui.nestedFields.text({ bind: 'product' }),
            titleRight: ui.nestedFields.text({ bind: 'description' }),
            line2: ui.nestedFields.numeric({ bind: 'qty' }),
            line2Right: ui.nestedFields.numeric({ bind: 'tax' }),
        },
        isFullWidth: true,
    })
    maxMinDate: ui.fields.Calendar;

    @ui.decorators.calendarField<Calendar, ShowCaseProduct>({
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        bind: 'products',
        title: 'Restricted result set',
        endDateField: 'endingDate',
        startDateField: 'releaseDate',
        parent() {
            return this.additionalBlock;
        },
        eventCard: {
            title: ui.nestedFields.text({
                bind: 'product',
                canFilter: true,
            }),
            titleRight: ui.nestedFields.text({
                bind: 'qty',
                canFilter: true,
            }),
        },
    })
    restrictedResultSet: ui.fields.Calendar;

    @ui.decorators.textField<Calendar>({
        parent() {
            return this.additionalBlock;
        },
        isTransient: true,
        title: 'Result set restriction (on product name)',
        onChange() {
            const filter = this.resultSetRestriction.value
                ? {
                      product: { _regex: this.resultSetRestriction.value, _options: 'i' },
                  }
                : undefined;

            this.restrictedResultSet.filter = filter;
        },
    })
    resultSetRestriction: ui.fields.Text;
}
