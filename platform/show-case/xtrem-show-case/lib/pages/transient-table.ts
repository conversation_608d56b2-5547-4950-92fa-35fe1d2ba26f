import { GraphApi } from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import { tableField } from '../menu-items/table-field';

@ui.decorators.page<TransientTable>({
    authorizationCode: 'BSCFLDS',
    module: 'show-case',
    isTransient: true,
    category: 'SHOWCASE',
    onLoad() {
        this.canSelect.value = true;
        this.canUserHideColumns.value = true;
    },
    title: 'Field - Table (Transient)',
    menuItem: tableField,
})
export class TransientTable extends ui.Page<GraphApi> {
    @ui.decorators.section<TransientTable>({
        title: 'Table field',
    })
    section: ui.containers.Section;

    @ui.decorators.block<TransientTable>({
        parent() {
            return this.section;
        },
        title: 'Field example',
    })
    fieldBlock: ui.containers.Block;

    @ui.decorators.buttonField<TransientTable>({
        map() {
            return 'Load data';
        },
        parent() {
            return this.fieldBlock;
        },
        async onClick() {
            const result = await this.$.graph
                .node('@sage/xtrem-show-case/ShowCaseProduct')
                .query(
                    ui.queryUtils.edgesSelector({
                        _id: true,
                        amount: true,
                        product: true,
                        description: true,
                        hotProduct: true,
                        qty: true,
                        listPrice: true,
                        progress: true,
                        netPrice: true,
                        category: true,
                    }),
                )
                .execute();
            this.field.value = result.edges.map(e => e.node);
            this.calculateTableTotals();
        },
    })
    loadButton: ui.fields.Button;

    @ui.decorators.buttonField<TransientTable>({
        map() {
            return 'Load with selection';
        },
        parent() {
            return this.fieldBlock;
        },
        async onClick() {
            this.field.isHidden = true;
            const result = await this.$.graph
                .node('@sage/xtrem-show-case/ShowCaseProduct')
                .query(
                    ui.queryUtils.edgesSelector({
                        _id: true,
                        amount: true,
                        product: true,
                        description: true,
                        hotProduct: true,
                        qty: true,
                        listPrice: true,
                        progress: true,
                        netPrice: true,
                        category: true,
                    }),
                )
                .execute();
            this.field.value = result.edges.map(e => e.node);
            this.field.selectRecord('1');
            this.field.selectRecord('3');
            this.field.isHidden = false;
        },
    })
    preselectButton: ui.fields.Button;

    @ui.decorators.numericField<TransientTable>({
        parent() {
            return this.fieldBlock;
        },
        title: 'Selected items total',
        isReadOnly: true,
        scale: 2,
        prefix: '$',
        isTransient: true,
    })
    tableSelectedTotal: ui.fields.Numeric;

    @ui.decorators.numericField<TransientTable>({
        parent() {
            return this.fieldBlock;
        },
        isTransient: true,
        title: 'Items total',
        isReadOnly: true,
        scale: 2,
        prefix: '$',
    })
    tableSampleTotal: ui.fields.Numeric;

    @ui.decorators.tableField<TransientTable>({
        pageSize: 5,
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        canExport: true,
        hasSearchBoxMobile: true,
        emptyStateText: 'to load data',
        emptyStateClickableText: 'Click here',
        async onEmptyStateLinkClick() {
            const result = await this.$.graph
                .node('@sage/xtrem-show-case/ShowCaseProduct')
                .query(
                    ui.queryUtils.edgesSelector({
                        _id: true,
                        amount: true,
                        product: true,
                        description: true,
                        hotProduct: true,
                        qty: true,
                        listPrice: true,
                        progress: true,
                        netPrice: true,
                        category: true,
                    }),
                )
                .execute();
            this.field.value = result.edges.map(e => e.node);
            this.calculateTableTotals();
        },
        columns: [
            ui.nestedFields.text({
                bind: '_id',
                title: 'Id',
                isHiddenOnMainField: true,
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                bind: 'product',
                title: 'Product',
            }),
            ui.nestedFields.text({
                bind: 'description',
                title: 'Description',
            }),
            ui.nestedFields.checkbox({
                bind: 'hotProduct',
                title: 'Hot',
            }),
            ui.nestedFields.numeric({
                bind: 'qty',
                isTransient: true,
                title: 'Quantity',
                scale: 0,
                onChange(_id: number, rowData: any) {
                    this.updateRow(_id, rowData);
                },
                validation(value) {
                    if (value === 5) {
                        return new Promise<string>(resolve => setTimeout(() => resolve('Error'), 2000));
                    }

                    return undefined;
                },
            }),
            ui.nestedFields.numeric({
                bind: 'listPrice',
                title: 'List Price',
                scale: 2,
                onChange(_id: number, rowData: any) {
                    this.updateRow(_id, rowData);
                },
            }),
            ui.nestedFields.numeric({
                bind: 'amount',
                title: 'Total Amount',
                scale: 2,
                isReadOnly: true,
            }),
            ui.nestedFields.progress({
                bind: 'progress',
                title: 'Progress',
            }),
            ui.nestedFields.numeric({
                bind: 'netPrice',
                title: 'Net Price',
                scale: (value: any, rowData: any) => {
                    return rowData.hotProduct ? 2 : 1;
                },
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.numeric({
                bind: 'tax',
                title: 'Tax',
                scale: 2,
                isHiddenOnMainField: true,
            }),

            ui.nestedFields.date({
                bind: 'endingDate',
                title: 'Ending date',
            }),
            ui.nestedFields.label({
                bind: 'category',
                title: 'Category',
                optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
            }),
            ui.nestedFields.select<TransientTable>({
                bind: 'stuff',
                title: 'Select with Option callback',
                options() {
                    return this.conditionalOptionType.value ? ['Set 1 A', 'Set 1 B'] : ['Set 2 A', 'Set 2 B'];
                },
            }),
        ],
        orderBy: {
            _id: 1,
        },
        fieldActions() {
            return [this.addProduct];
        },
        parent() {
            return this.fieldBlock;
        },
        onChange() {
            this.calculateTableTotals();
            this.calculateSelectedTotal();
        },
        onRowSelected(_id: string | number, item: any) {
            this.calculateSelectedTotal();
            this.$.showToast(`${item.product} selected.`);
        },
        onRowUnselected(_id: string | number, item: any) {
            this.calculateSelectedTotal();
            this.$.showToast(`${item.product} unselected.`, { type: 'warning' });
        },
        dropdownActions: [
            {
                icon: 'add',
                title: 'Add',
                isDisabled() {
                    return false;
                },
                onClick(rowId: any, data: any) {
                    this.$.dialog.message(
                        'info',
                        'Add Row Action was clicked',
                        `Product: ${data.product} Row: ${rowId}`,
                    );
                },
            },
            {
                icon: 'locked',
                title: 'Maybe disabled',
                isDisabled(id: any, row: any) {
                    return row.qty < 10;
                },
                onClick(rowId: any, data: any) {
                    this.$.showToast(data.product, { type: 'info' });
                },
            },
            ui.menuSeparator(),
            {
                title: 'Action no icon',
                isDisabled(id: any, row: any) {
                    return row.qty < 10;
                },
                onClick(rowId: any, data: any) {
                    this.$.showToast(data.product, { type: 'info' });
                },
            },
        ],
    })
    field: ui.fields.Table;

    @ui.decorators.textAreaField<TransientTable>({
        isFullWidth: true,
        isReadOnly: true,
        isTransient: true,
        parent() {
            return this.fieldBlock;
        },
        rows: 6,
    })
    description: ui.fields.TextArea;

    @ui.decorators.block<TransientTable>({
        parent() {
            return this.section;
        },
        title: 'Configuration',
    })
    configurationBlock: ui.containers.Block;

    @ui.decorators.checkboxField<TransientTable>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Can select',
        onChange() {
            this.field.canSelect = this.canSelect.value;
        },
        isTransient: true,
    })
    canSelect: ui.fields.Checkbox;

    @ui.decorators.checkboxField<TransientTable>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Can user hide columns',
        onChange() {
            this.field.canUserHideColumns = this.canUserHideColumns.value;
        },
        isTransient: true,
    })
    canUserHideColumns: ui.fields.Checkbox;

    @ui.decorators.textField<TransientTable>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Helper text',
        onChange() {
            this.field.helperText = this.helperText.value;
        },
        isTransient: true,
    })
    helperText: ui.fields.Text;

    @ui.decorators.checkboxField<TransientTable>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is disabled',
        onChange() {
            this.field.isDisabled = this.isDisabled.value;
        },
        isTransient: true,
    })
    isDisabled: ui.fields.Checkbox;

    @ui.decorators.checkboxField<TransientTable>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is helper text hidden',
        onChange() {
            this.field.isHelperTextHidden = this.isHelperTextHidden.value;
        },
        isTransient: true,
    })
    isHelperTextHidden: ui.fields.Checkbox;

    @ui.decorators.checkboxField<TransientTable>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is hidden',
        onChange() {
            this.field.isHidden = this.isHidden.value;
        },
        isTransient: true,
    })
    isHidden: ui.fields.Checkbox;

    @ui.decorators.checkboxField<TransientTable>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Option type column value',
        onChange() {
            this.field.redraw('stuff');
        },
        isTransient: true,
    })
    conditionalOptionType: ui.fields.Checkbox;

    @ui.decorators.checkboxField<TransientTable>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is title hidden',
        onChange() {
            this.field.isTitleHidden = this.isTitleHidden.value;
        },
        isTransient: true,
    })
    isTitleHidden: ui.fields.Checkbox;

    @ui.decorators.textField<TransientTable>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Title',
        onChange() {
            this.field.title = this.title.value;
        },
        isTransient: true,
    })
    title: ui.fields.Text;

    @ui.decorators.pageAction<TransientTable>({
        title: 'Add row',
        icon: 'plus',
        onClick() {
            this.$.dialog
                .page('@sage/xtrem-show-case/ShowCaseProduct', undefined, {
                    rightAligned: true,
                    size: 'medium-large',
                })
                .then(() => {
                    return this.field.refresh();
                })
                .catch(error => {
                    ui.console.error(error);
                    this.$.showToast('An error ocurred during creation');
                });
        },
    })
    addProduct: ui.PageAction;

    updateRow(_id: any, rowData: any) {
        const updatedAmount = rowData.qty * parseFloat(rowData.listPrice);
        rowData.amount = String(updatedAmount);
        this.field.setRecordValue(rowData);
    }

    calculateTableTotals() {
        this.tableSampleTotal.value = (this.field.value || [])
            .map(v => parseFloat(v.amount))
            .reduce((prev, v) => prev + v, 0);
        this.calculateSelectedTotal();
    }

    calculateSelectedTotal() {
        const selectedRecords = this.field.selectedRecords || [];
        const selectedTotal = (this.field.value || [])
            .filter(v => selectedRecords.indexOf(v._id) !== -1)
            .map(v => parseFloat(v.amount))
            .reduce((prev, v) => prev + v, 0);

        this.tableSelectedTotal.value = selectedTotal;
    }
}
