import {
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>ountry,
    ShowCase<PERSON>roviderAddress as ShowCaseProviderAddressNode,
} from '@sage/xtrem-show-case-api';
import {
    setApplicativePageCrudActions,
    setOrderOfPageHeaderDropDownActions,
} from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import * as ui from '@sage/xtrem-ui';
import { applicationPages } from '../menu-items/application-pages';

@ui.decorators.page<ShowCaseProviderAddress, ShowCaseProviderAddressNode>({
    menuItem: applicationPages,
    createAction() {
        return this.$standardNewAction;
    },
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.text({ bind: 'name' }),
            titleRight: ui.nestedFields.text({ bind: 'addressLine1' }),
            line2: ui.nestedFields.text({ bind: 'addressLine2' }),
            line2Right: ui.nestedFields.reference<
                ShowCaseProviderAddress,
                ShowCaseProviderAddressNode,
                ShowCaseCountry
            >({ bind: { country: true }, valueField: 'name' }),
        },
    },
    businessActions() {
        return [this.$standardCancelAction, this.$standardSaveAction];
    },
    headerQuickActions() {
        return [this.$standardDuplicateAction];
    },
    headerDropDownActions() {
        return setOrderOfPageHeaderDropDownActions({
            remove: [this.$standardDeleteAction],
            dropDownBusinessActions: [this.$standardOpenRecordHistoryAction],
        });
    },
    node: '@sage/xtrem-show-case/ShowCaseProviderAddress',
    title: 'Show case - Provider address',
    objectTypeSingular: 'Provider address',
    objectTypePlural: 'Provider addresses',
    idField() {
        return this._id;
    },
    priority: 300,
    onLoad() {
        setApplicativePageCrudActions({
            page: this,
            isDirty: false,
            save: this.$standardSaveAction,
            cancel: this.$standardCancelAction,
            duplicate: this.$standardDuplicateAction,
            remove: this.$standardDeleteAction,
            actions: [],
        });
    },
    onDirtyStateUpdated(isDirty: boolean) {
        setApplicativePageCrudActions({
            page: this,
            isDirty,
            save: this.$standardSaveAction,
            cancel: this.$standardCancelAction,
            duplicate: this.$standardDuplicateAction,
            remove: this.$standardDeleteAction,
            actions: [],
        });
    },
})
export class ShowCaseProviderAddress extends ui.Page<GraphApi> {
    @ui.decorators.section<ShowCaseProviderAddress>({})
    section: ui.containers.Section;

    @ui.decorators.block<ShowCaseProviderAddress>({
        parent() {
            return this.section;
        },
    })
    block: ui.containers.Block;

    @ui.decorators.textField<ShowCaseProviderAddress>({
        parent() {
            return this.block;
        },
        title: 'Record ID',
        isReadOnly: true,
    })
    _id: ui.fields.Text;

    @ui.decorators.textField<ShowCaseProviderAddress>({
        parent() {
            return this.block;
        },
        title: 'Name',
    })
    name: ui.fields.Text;

    @ui.decorators.textField<ShowCaseProviderAddress>({
        parent() {
            return this.block;
        },
        title: 'Line 1',
    })
    addressLine1: ui.fields.Text;

    @ui.decorators.textField<ShowCaseProviderAddress>({
        parent() {
            return this.block;
        },
        title: 'Line 2',
    })
    addressLine2: ui.fields.Text;

    @ui.decorators.textField<ShowCaseProviderAddress>({
        parent() {
            return this.block;
        },
        title: 'City',
    })
    city: ui.fields.Text;

    @ui.decorators.referenceField<ShowCaseProviderAddress, ShowCaseCountry>({
        parent() {
            return this.block;
        },
        bind: 'country',
        title: 'Country',
        node: '@sage/xtrem-show-case/ShowCaseCountry',
        tunnelPage: '@sage/xtrem-show-case/ShowCaseCountry',
        valueField: 'name',
        helperTextField: 'code',
    })
    country: ui.fields.Reference;
}
