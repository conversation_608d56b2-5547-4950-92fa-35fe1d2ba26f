import { <PERSON><PERSON><PERSON><PERSON><PERSON>, ShowCaseProvider, ShowCaseProduct } from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import { chartField } from '../menu-items/chart-field';

@ui.decorators.page<ChartPie, ShowCaseProvider>({
    authorizationCode: 'BSCFLDS',
    module: 'show-case',
    node: '@sage/xtrem-show-case/ShowCaseProvider',
    title: 'Field - Chart - Pie',
    category: 'SHOWCASE',
    defaultEntry: () => '2',
    async onLoad() {
        this.isLegendHidden.value = true;
        const productCost = [
            { _id: '1', name: 'Manufacturing', cost: 23.7 },
            { _id: '2', name: 'R&D', cost: 78.32 },
            { _id: '3', name: 'Raw materials', cost: 89.12 },
            { _id: '4', name: 'Distribution', cost: 4.34 },
        ];
        this.transientChartExample.value = productCost;
        this.priceComponentsTable.value = productCost;
        await this.$.commitValueAndPropertyChanges();
    },
    menuItem: chartField,
})
export class Chart<PERSON>ie extends ui.Page<GraphApi> {
    @ui.decorators.section<ChartPie>({
        title: 'Basic Example with configuration',
    })
    section: ui.containers.Section;

    @ui.decorators.block<ChartPie>({
        parent() {
            return this.section;
        },
        title: 'Field example',
        width: 'medium',
    })
    fieldBlock: ui.containers.Block;

    @ui.decorators.chartField<ChartPie>({
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        bind: 'products',
        isLegendHidden: true,
        chart: ui.charts.pie<ChartPie, ShowCaseProduct>({
            series: [
                ui.nestedFields.numeric({
                    bind: 'qty',
                    title: 'Quantity',
                    canFilter: true,
                }),
            ],
            xAxis: ui.nestedFields.text({ bind: 'product', title: 'Product' }),
        }),
        isFullWidth: true,
        parent() {
            return this.fieldBlock;
        },
    })
    field: ui.fields.Chart;

    @ui.decorators.block<ChartPie>({
        parent() {
            return this.section;
        },
        title: 'Configuration',
        width: 'medium',
    })
    configurationBlock: ui.containers.Block;

    @ui.decorators.textField<ChartPie>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Title',
        onChange() {
            this.field.title = this.title.value;
        },
        isTransient: true,
    })
    title: ui.fields.Text;

    @ui.decorators.textField<ChartPie>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Helper text',
        onChange() {
            this.field.helperText = this.helperText.value;
        },
        isTransient: true,
    })
    helperText: ui.fields.Text;

    @ui.decorators.checkboxField<ChartPie>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is disabled',
        onChange() {
            this.field.isDisabled = this.isDisabled.value;
        },
        isTransient: true,
    })
    isDisabled: ui.fields.Checkbox;

    @ui.decorators.checkboxField<ChartPie>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is Legend Hidden',
        onChange() {
            this.field.isLegendHidden = this.isLegendHidden.value;
        },
        isTransient: true,
    })
    isLegendHidden: ui.fields.Checkbox;

    @ui.decorators.checkboxField<ChartPie>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is helper text hidden',
        onChange() {
            this.field.isHelperTextHidden = this.isHelperTextHidden.value;
        },
        isTransient: true,
    })
    isHelperTextHidden: ui.fields.Checkbox;

    @ui.decorators.checkboxField<ChartPie>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is hidden',
        onChange() {
            this.field.isHidden = this.isHidden.value;
        },
        isTransient: true,
    })
    isHidden: ui.fields.Checkbox;

    @ui.decorators.checkboxField<ChartPie>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is title hidden',
        onChange() {
            this.field.isTitleHidden = this.isTitleHidden.value;
        },
        isTransient: true,
    })
    isTitleHidden: ui.fields.Checkbox;

    @ui.decorators.section<ChartPie>({
        title: 'Product price components (Transient example)',
    })
    transientSection: ui.containers.Section;

    @ui.decorators.block<ChartPie>({
        parent() {
            return this.transientSection;
        },
        width: 'medium',
    })
    transientTableBlock: ui.containers.Block;

    @ui.decorators.tableField<ChartPie>({
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        isTransient: true,
        columns: [
            ui.nestedFields.text({
                bind: 'name',
                title: 'Price component name',
                isHiddenDesktop: true,
                isReadOnly: true,
            }),
            ui.nestedFields.numeric({
                bind: 'cost',
                title: 'Cost',
                scale: 2,
            }),
        ],
        parent() {
            return this.transientTableBlock;
        },
        onChange() {
            this.transientChartExample.value = this.priceComponentsTable.value;
        },
    })
    priceComponentsTable: ui.fields.Table;

    @ui.decorators.block<ChartPie>({
        parent() {
            return this.transientSection;
        },
        width: 'medium',
    })
    transientTableChart: ui.containers.Block;

    @ui.decorators.chartField<ChartPie, ShowCaseProvider>({
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        isLegendHidden: true,
        isTransient: true,
        isFullWidth: true,
        chart: ui.charts.pie<ChartPie>({
            series: [
                ui.nestedFields.numeric({
                    bind: 'cost',
                    title: 'Cost',
                    scale: 3,
                    canFilter: true,
                }),
            ],
            xAxis: ui.nestedFields.text({ bind: 'name', title: 'Name' }),
        }),
        parent() {
            return this.transientTableChart;
        },
    })
    transientChartExample: ui.fields.Chart;
}
