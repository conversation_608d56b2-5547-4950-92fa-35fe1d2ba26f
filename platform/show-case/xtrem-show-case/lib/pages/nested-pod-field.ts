import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Show<PERSON><PERSON><PERSON><PERSON>ider, ShowCaseProduct, ShowCaseProviderAddress } from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import { pod } from '../menu-items/pod';

@ui.decorators.page<NestedPodField, ShowCaseProduct>({
    authorizationCode: 'SHCPRVD',
    category: 'SHOWCASE',
    menuItem: pod,
    module: 'show-case',
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.text({ bind: 'product' }),
            titleRight: ui.nestedFields.text({ bind: '_id' }),
            line2: ui.nestedFields.text({ bind: 'description', canFilter: false }),
        },
    },
    node: '@sage/xtrem-show-case/ShowCaseProduct',
    title: 'Pod Field with Nested Reference Arrays',
})
export class NestedPodField extends ui.Page<GraphApi> {
    @ui.decorators.section<NestedPodField>({})
    mainSection: ui.containers.Section;

    @ui.decorators.block<NestedPodField>({
        parent() {
            return this.mainSection;
        },
    })
    block: ui.containers.Block;

    @ui.decorators.podField<NestedPodField, ShowCaseProvider>({
        title: 'Provider',
        width: 'large',
        parent() {
            return this.block;
        },
        canRemove: true,
        columns: [
            ui.nestedFields.text({ bind: 'textField', title: 'Text field' }),
            ui.nestedFields.text({ bind: 'integerField', title: 'Numeric field' }),
            ui.nestedFields.text({ bind: '_id', title: 'id' }),
            ui.nestedFields.text({ bind: 'dateField', title: 'date field' }),
            ui.nestedFields.multiReference<NestedPodField, ShowCaseProvider, ShowCaseProviderAddress>({
                bind: 'addresses',
                node: '@sage/xtrem-show-case/ShowCaseProviderAddress',
                minLookupCharacters: 0,
                valueField: 'name',
                title: 'Addresses',
                isFullWidth: true,
            }),
        ],
        node: '@sage/xtrem-show-case/ShowCaseProvider',
    })
    provider: ui.fields.Pod;
}
