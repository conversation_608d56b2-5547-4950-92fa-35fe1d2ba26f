import { GraphApi, ShowCaseProduct } from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import { applicationPages } from '../menu-items/application-pages';

@ui.decorators.page<ShowCaseProductDialog, ShowCaseProduct>({
    authorizationCode: 'SHCPRDT',
    module: 'show-case',
    title: 'ShowCase - Product dialog',
    node: '@sage/xtrem-show-case/ShowCaseProduct',
    menuItem: applicationPages,
    category: 'SHOWCASE',
    businessActions() {
        return [this.addRecord];
    },
})
export class ShowCaseProductDialog extends ui.Page<GraphApi> {
    @ui.decorators.section<ShowCaseProductDialog>({})
    section: ui.containers.Section;

    @ui.decorators.block<ShowCaseProductDialog>({
        parent() {
            return this.section;
        },
    })
    block: ui.containers.Block;

    @ui.decorators.textField<ShowCaseProductDialog>({
        parent() {
            return this.block;
        },
        title: 'ID',
        isReadOnly: true,
    })
    _id: ui.fields.Text;

    @ui.decorators.textField<ShowCaseProductDialog>({
        parent() {
            return this.block;
        },
        title: 'Product',
        isMandatory: true,
    })
    product: ui.fields.Text;

    @ui.decorators.textField<ShowCaseProductDialog>({
        parent() {
            return this.block;
        },
        title: 'Description',
    })
    description: ui.fields.Text;

    @ui.decorators.checkboxField<ShowCaseProductDialog>({
        parent() {
            return this.block;
        },
        title: 'Hot product',
    })
    hotProduct: ui.fields.Checkbox;

    @ui.decorators.numericField<ShowCaseProductDialog>({
        parent() {
            return this.block;
        },
        title: 'Quantity',
        fetchesDefaults: true,
    })
    qty: ui.fields.Numeric;

    @ui.decorators.numericField<ShowCaseProductDialog>({
        parent() {
            return this.block;
        },
        title: 'Stock',
    })
    st: ui.fields.Numeric;

    @ui.decorators.numericField<ShowCaseProductDialog>({
        parent() {
            return this.block;
        },
        title: 'List price',
        scale: 2,
        fetchesDefaults: true,
    })
    listPrice: ui.fields.Numeric;

    @ui.decorators.progressField<ShowCaseProductDialog>({
        parent() {
            return this.block;
        },
        title: 'Progress',
    })
    progress: ui.fields.Progress;

    @ui.decorators.numericField<ShowCaseProductDialog>({
        parent() {
            return this.block;
        },
        title: 'Tax',
        scale: 2,
    })
    tax: ui.fields.Numeric;

    @ui.decorators.numericField<ShowCaseProductDialog>({
        parent() {
            return this.block;
        },
        title: 'Net price',
        scale: 2,
    })
    netPrice: ui.fields.Numeric;

    @ui.decorators.numericField<ShowCaseProductDialog>({
        parent() {
            return this.block;
        },
        title: 'Amount',
        scale: 2,
    })
    amount: ui.fields.Numeric;

    @ui.decorators.dateField<ShowCaseProductDialog>({
        parent() {
            return this.block;
        },
        title: 'Release date',
    })
    releaseDate: ui.fields.Date;

    @ui.decorators.dateField<ShowCaseProductDialog>({
        parent() {
            return this.block;
        },
        title: 'Ending date',
    })
    endingDate: ui.fields.Date;

    @ui.decorators.radioField<ShowCaseProductDialog>({
        parent() {
            return this.block;
        },
        title: 'Category Select',
        optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
    })
    category: ui.fields.Radio;

    @ui.decorators.pageAction<ShowCaseProductDialog>({
        title: 'Add product',
        onClick() {
            return this.$.finish(this.$.values);
        },
    })
    addRecord: ui.PageAction;
}
