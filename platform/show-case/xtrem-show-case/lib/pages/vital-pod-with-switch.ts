import { <PERSON><PERSON>h<PERSON><PERSON>, ShowCaseProduct, ShowCaseProvider } from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import { vitalPod } from '../menu-items/vital-pod';

@ui.decorators.page<VitalPodWithSwitch, ShowCaseProduct>({
    authorizationCode: 'VTPODSWTC',
    category: 'SHOWCASE',
    menuItem: vitalPod,
    module: 'show-case',
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.text({ bind: 'product' }),
            titleRight: ui.nestedFields.text({ bind: '_id' }),
            line2: ui.nestedFields.text({ bind: 'description', canFilter: false }),
        },
    },
    node: '@sage/xtrem-show-case/ShowCaseProduct',
    title: 'Field - Vital Pod (with Nested Switch)',
})
export class VitalPodWithSwitch extends ui.Page<GraphApi> {
    @ui.decorators.section<VitalPodWithSwitch>({})
    section: ui.containers.Section;

    @ui.decorators.block<VitalPodWithSwitch>({
        parent() {
            return this.section;
        },
    })
    block: ui.containers.Block;

    @ui.decorators.switchField<VitalPodWithSwitch>({
        isTransient: true,
        title: 'IsReadOnly',
        onChange() {
            this.field.isReadOnly = this.configuration.value;
        },
        parent() {
            return this.block;
        },
    })
    configuration: ui.fields.Switch;

    @ui.decorators.vitalPodField<VitalPodWithSwitch, ShowCaseProvider>({
        bind: 'provider',
        columns: [
            ui.nestedFields.text({ bind: '_id', title: 'Id', isReadOnly: true }),
            ui.nestedFields.text({ bind: 'textField', title: 'Text Field' }),
            ui.nestedFields.switch({ bind: 'booleanField', title: 'Boolean Field' }),
        ],
        node: '@sage/xtrem-show-case/ShowCaseProvider',
        title: 'Provider',
        width: 'large',
        parent() {
            return this.block;
        },
    })
    field: ui.fields.VitalPod;
}
