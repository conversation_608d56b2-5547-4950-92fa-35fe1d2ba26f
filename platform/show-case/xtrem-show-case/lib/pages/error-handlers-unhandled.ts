import * as ui from '@sage/xtrem-ui';
import { misc } from '../menu-items/misc';

@ui.decorators.page<ErrorHandlersUnhandled>({
    title: 'Page - No Error handlers',
    authorizationCode: 'WIZARD',
    module: 'show-case',
    category: 'SHOWCASE',
    async onLoad() {
        setTimeout(() => {
            this.field4.value = [
                {
                    _id: '3',
                    column1: 'some value',
                    column2: 'some other value',
                },
            ];
        });
        await new Promise((res, rej) => {
            rej(new Error('ERROR'));
        });
    },
    isTransient: true,
    menuItem: misc,
})
export class ErrorHandlersUnhandled extends ui.Page {
    @ui.decorators.section<ErrorHandlersUnhandled>({
        isTitleHidden: true,
    })
    section: ui.containers.Section;

    @ui.decorators.block<ErrorHandlersUnhandled>({
        parent() {
            return this.section;
        },
        title: 'This page has no error handlers and can be compared with "Page - Error Handlers"',
    })
    block: ui.containers.Block;

    @ui.decorators.textField<ErrorHandlersUnhandled>({
        title: 'Text with error handler',
        parent() {
            return this.block;
        },
        onChange() {
            throw new Error('An error occurred');
        },
        helperText: 'Error is thrown on change and it is handled.',
    })
    field1: ui.fields.Text;

    @ui.decorators.textField<ErrorHandlersUnhandled>({
        title: 'Text with error handler',
        parent() {
            return this.block;
        },
        onChange() {
            throw new Error('An error occurred');
        },
        helperText: 'This field does not have error handler',
    })
    field3: ui.fields.Text;

    @ui.decorators.tableField<ErrorHandlersUnhandled>({
        isTransient: true,
        title: 'Table with handler',
        parent() {
            return this.block;
        },
        columns: [
            ui.nestedFields.text({
                bind: 'column1',
                title: 'Has handler',
                onChange() {
                    throw new Error('uups');
                },
            }),
            ui.nestedFields.text({
                bind: 'column2',
                title: 'No handler',
                onChange() {
                    throw new Error('uups');
                },
            }),
        ],
    })
    field4: ui.fields.Table;
}
