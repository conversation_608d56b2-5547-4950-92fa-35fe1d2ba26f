import { ShowCaseProvider, ShowCaseProduct } from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import { tableField } from '../menu-items/_index';

@ui.decorators.page<TableRemapped, ShowCaseProvider>({
    authorizationCode: 'BSCFLDS',
    defaultEntry: () => '2',
    module: 'show-case',
    category: 'SHOWCASE',
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.text({ bind: 'textField' }),
            titleRight: ui.nestedFields.text({ bind: '_id' }),
        },
    },
    node: '@sage/xtrem-show-case/ShowCaseProvider',
    title: 'Field - Table - Remapped value',
    businessActions() {
        return [this.$standardCancelAction, this.$standardSaveAction];
    },
    onLoad() {
        this.demoValue.value = String(this.$.storage.get('TEST') || '');
    },
    menuItem: tableField,
})
export class TableRemapped extends ui.Page {
    @ui.decorators.section<TableRemapped>({
        title: 'Table field',
    })
    section: ui.containers.Section;

    @ui.decorators.block<TableRemapped>({
        parent() {
            return this.section;
        },
        title: 'Field example',
    })
    fieldBlock: ui.containers.Block;

    @ui.decorators.tableField<TableRemapped, ShowCaseProduct>({
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        bind: 'products',
        columns: [
            ui.nestedFields.text({
                bind: '_id',
                title: 'Id',
                isHiddenOnMainField: true,
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                bind: 'product',
                title: 'Product',
            }),
            ui.nestedFields.text({
                bind: 'description',
                title: 'Description',
            }),
            ui.nestedFields.checkbox({
                bind: 'hotProduct',
                title: 'Hot',
            }),
            ui.nestedFields.text({
                bind: 'transientColumn' as any,
                title: 'Transient calculated',
                isTransient: true,
            }),
        ],
        mapServerRecord(r) {
            const testValue = this.$.storage.get('TEST') || 'NOT SET';
            return { ...r, transientColumn: `${testValue} ${r.product}` };
        },
        orderBy: {
            product: 1,
        },
        parent() {
            return this.fieldBlock;
        },
        headerBusinessActions() {
            return [this.nothing];
        },
    })
    field: ui.fields.Table<ShowCaseProduct>;

    @ui.decorators.pageAction<TableRemapped>({
        title: 'It does nothing',
        onClick() {
            this.$.showToast('I told you.');
        },
    })
    nothing: ui.PageAction;

    @ui.decorators.textField<TableRemapped>({
        title: 'Set prefix to storage',
        isTransient: true,
        parent() {
            return this.fieldBlock;
        },
        onChange() {
            this.$.storage.set('TEST', this.demoValue.value);
        },
    })
    demoValue: ui.fields.Text;

    @ui.decorators.buttonField<TableRemapped>({
        parent() {
            return this.fieldBlock;
        },
        isTransient: true,
        async onClick() {
            await this.$.router.refresh(true);
        },
        map() {
            return 'Refresh Page';
        },
    })
    refreshTable: ui.fields.Button;
}
