import { Graph<PERSON><PERSON>, ShowCaseProduct, ShowCaseProvider, ShowCaseCountry } from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import { referenceField } from '../menu-items/reference-field';

@ui.decorators.page<Reference>({
    authorizationCode: 'BSCFLDS',
    menuItem: referenceField,
    module: 'show-case',
    title: 'Field - Reference',
    category: 'SHOWCASE',
    isTransient: true,
    onLoad() {
        this.autoSelectValue.value = 'NO VALUE';
    },
})
export class Reference extends ui.Page<GraphApi> {
    @ui.decorators.section<Reference>({
        title: 'Reference field',
    })
    section: ui.containers.Section;

    @ui.decorators.block<Reference>({
        parent() {
            return this.section;
        },
        title: 'Field example',
    })
    fieldBlock: ui.containers.Block;

    @ui.decorators.referenceField<Reference, ShowCaseProduct>({
        columns: [
            ui.nestedFields.text({ bind: '_id', title: 'ID', canFilter: false }),
            ui.nestedFields.text({ bind: 'product', title: 'Product', canFilter: true }),
            ui.nestedFields.text({ bind: 'description', canFilter: true, title: 'Description' }),
            ui.nestedFields.reference<Reference, ShowCaseProduct, ShowCaseProvider>({
                bind: 'provider',
                canFilter: true,
                node: '@sage/xtrem-show-case/ShowCaseProvider',
                title: 'Provider',
                valueField: 'textField',
            }),
        ],
        helperTextField: 'description',
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        onClick() {
            this.clickTriggered.isHidden = false;
            setTimeout(() => {
                this.clickTriggered.isHidden = true;
            }, 5000);
        },
        onChange() {
            if (this.field.value !== this.value.value) {
                this.value.value = this.field.value;
                this.changeTriggered.isHidden = false;
                setTimeout(() => {
                    this.changeTriggered.isHidden = true;
                }, 5000);
            }
            if (this.field.helperText !== this.helperText.value) {
                this.helperText.value = this.field.helperText;
            }
        },
        parent() {
            return this.fieldBlock;
        },
        isFullWidth: true,
        valueField: 'product',
        imageField: 'imageField',
        tunnelPage: null,
    })
    field: ui.fields.Reference;

    @ui.decorators.labelField<Reference>({
        parent() {
            return this.fieldBlock;
        },
        isHidden: true,
        map() {
            return 'Change was triggered';
        },
    })
    changeTriggered: ui.fields.Label;

    @ui.decorators.labelField<Reference>({
        parent() {
            return this.fieldBlock;
        },
        isHidden: true,
        map() {
            return 'Click was triggered';
        },
    })
    clickTriggered: ui.fields.Label;

    @ui.decorators.block<Reference>({
        parent() {
            return this.section;
        },
        title: 'Configuration',
    })
    configurationBlock: ui.containers.Block;

    @ui.decorators.textField<Reference>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Helper text',
        onChange() {
            this.field.helperText = this.helperText.value;
        },
    })
    helperText: ui.fields.Text;

    @ui.decorators.selectField<Reference>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Icon',
        onChange() {
            this.field.icon = this.icon.value as any;
        },
        options: [
            'scan',
            'add',
            'calendar',
            'edit',
            'gift',
            'image',
            'ledger',
            'pause_circle',
            'refresh',
            'tag',
            'video',
        ],
    })
    icon: ui.fields.Select;

    @ui.decorators.checkboxField<Reference>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is disabled',
        onChange() {
            this.field.isDisabled = this.isDisabled.value;
        },
    })
    isDisabled: ui.fields.Checkbox;

    @ui.decorators.checkboxField<Reference>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is helper text hidden',
        onChange() {
            this.field.isHelperTextHidden = this.isHelperTextHidden.value;
        },
    })
    isHelperTextHidden: ui.fields.Checkbox;

    @ui.decorators.checkboxField<Reference>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is hidden',
        onChange() {
            this.field.isHidden = this.isHidden.value;
        },
    })
    isHidden: ui.fields.Checkbox;

    @ui.decorators.checkboxField<Reference>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is readOnly',
        onChange() {
            this.field.isReadOnly = this.isReadOnly.value;
        },
    })
    isReadOnly: ui.fields.Checkbox;

    @ui.decorators.checkboxField<Reference>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is title hidden',
        onChange() {
            this.field.isTitleHidden = this.isTitleHidden.value;
        },
    })
    isTitleHidden: ui.fields.Checkbox;

    @ui.decorators.textField<Reference>({
        title: 'Placeholder',
        onChange() {
            this.field.placeholder = this.placeholder.value;
        },
        parent() {
            return this.configurationBlock;
        },
    })
    placeholder: ui.fields.Text;

    @ui.decorators.textField<Reference>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Title',
        onChange() {
            this.field.title = this.title.value;
        },
    })
    title: ui.fields.Text;

    @ui.decorators.referenceField<Reference, ShowCaseProduct>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Value',
        onChange() {
            if (this.field.value !== this.value.value) {
                this.field.value = this.value.value;
            }
        },
        helperTextField: 'description',
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        valueField: 'product',
        columns: null,
        tunnelPage: null,
    })
    value: ui.fields.Reference;

    @ui.decorators.buttonField<Reference>({
        parent() {
            return this.configurationBlock;
        },
        map() {
            return 'Focus field';
        },
        onClick() {
            this.field.focus();
        },
    })
    focus: ui.fields.Button;

    @ui.decorators.buttonField<Reference>({
        parent() {
            return this.configurationBlock;
        },
        map() {
            return 'Open dialog';
        },
        onClick() {
            this.field.openDialog();
        },
    })
    openButton: ui.fields.Button;

    @ui.decorators.buttonField<Reference>({
        parent() {
            return this.configurationBlock;
        },
        map() {
            return 'Clear Value';
        },
        onClick() {
            this.field.value = null;
        },
    })
    clearValue: ui.fields.Button;

    /* Additional examples */

    @ui.decorators.block<Reference>({
        parent() {
            return this.section;
        },
        title: 'Additional examples',
    })
    additionalBlock: ui.containers.Block;

    @ui.decorators.referenceField<Reference, ShowCaseProduct>({
        parent() {
            return this.additionalBlock;
        },
        title: 'Mandatory',
        isMandatory: true,
        helperTextField: 'description',
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        valueField: 'product',
        columns: null,
        tunnelPage: null,
    })
    mandatory: ui.fields.Reference;

    @ui.decorators.referenceField<Reference, ShowCaseProduct>({
        parent() {
            return this.additionalBlock;
        },
        title: 'Autoselect',
        isAutoSelectEnabled: true,
        helperTextField: 'description',
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        valueField: 'product',
        columns: null,
        tunnelPage: null,
        onChange() {
            if (this.autoSelect.value) {
                this.autoSelectValue.value = this.autoSelect.value.product;
            } else {
                this.autoSelectValue.value = 'NO VALUE';
            }
        },
    })
    autoSelect: ui.fields.Reference;

    @ui.decorators.textField<Reference>({
        parent() {
            return this.additionalBlock;
        },
        title: 'Autoselect field value',
        isDisabled: true,
    })
    autoSelectValue: ui.fields.Text;

    @ui.decorators.referenceField<Reference, ShowCaseProduct>({
        parent() {
            return this.additionalBlock;
        },
        title: 'Full width',
        isFullWidth: true,
        helperTextField: 'description',
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        valueField: 'product',
        columns: null,
        tunnelPage: null,
    })
    fullWidth: ui.fields.Reference;

    @ui.decorators.referenceField<Reference, ShowCaseProduct>({
        parent() {
            return this.additionalBlock;
        },
        title: 'No minimum lookup characters',
        helperTextField: 'description',
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        valueField: 'product',
        minLookupCharacters: 0,
        columns: null,
        tunnelPage: null,
    })
    noMinimumLookupCharacters: ui.fields.Reference;

    @ui.decorators.referenceField<Reference, ShowCaseProduct>({
        parent() {
            return this.additionalBlock;
        },
        title: 'Restricted result set',
        helperTextField: 'description',
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        valueField: 'product',
        lookupDialogTitle: 'Test',
        columns: [
            ui.nestedFields.text({ bind: '_id', title: 'ID', canFilter: false }),
            ui.nestedFields.text({ bind: 'product', title: 'Product', canFilter: true }),
            ui.nestedFields.text({ bind: 'description', canFilter: true, title: 'Description' }),
            ui.nestedFields.reference<Reference, ShowCaseProduct, ShowCaseProvider>({
                bind: 'provider',
                title: 'Provider',
                valueField: 'textField',
                canFilter: true,
                node: '@sage/xtrem-show-case/ShowCaseProvider',
            }),
        ],
        tunnelPage: null,
    })
    restrictedResultSet: ui.fields.Reference;

    @ui.decorators.textField<Reference>({
        parent() {
            return this.additionalBlock;
        },
        title: 'Result set restriction',
        onChange() {
            const filter = this.resultSetRestriction.value
                ? {
                      product: { _regex: this.resultSetRestriction.value, _options: 'i' },
                  }
                : undefined;

            this.restrictedResultSet.filter = filter;
        },
    })
    resultSetRestriction: ui.fields.Text;

    @ui.decorators.block<Reference>({
        parent() {
            return this.section;
        },
        title: 'Suggestions on columns',
    })
    suggestionOnColumnsBlock: ui.containers.Block;

    @ui.decorators.referenceField<Reference, ShowCaseProduct>({
        columns: [
            ui.nestedFields.text({ bind: '_id', title: 'ID', canFilter: false }),
            ui.nestedFields.text({ bind: 'product', title: 'Product', canFilter: true }),
            ui.nestedFields.text({ bind: 'description', canFilter: true, title: 'Description' }),
            ui.nestedFields.reference<Reference, ShowCaseProduct, ShowCaseProvider>({
                bind: 'provider',
                canFilter: true,
                node: '@sage/xtrem-show-case/ShowCaseProvider',
                title: 'Provider',
                valueField: 'textField',
            }),
        ],
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        shouldSuggestionsIncludeColumns: true,
        parent() {
            return this.suggestionOnColumnsBlock;
        },
        valueField: 'product',
        title: 'Suggestions from column data',
        isAutoSelectEnabled: true,
        tunnelPage: null,
    })
    suggestionOnColumns: ui.fields.Reference;

    @ui.decorators.referenceField<Reference, ShowCaseProduct>({
        parent() {
            return this.additionalBlock;
        },
        title: 'Restricted Results by Callback Filter',
        helperTextField: 'description',
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        valueField: 'product',
        tunnelPage: null,
        columns: [
            ui.nestedFields.text({ bind: '_id', title: 'ID', canFilter: false }),
            ui.nestedFields.text({ bind: 'product', title: 'Product', canFilter: true }),
            ui.nestedFields.reference<Reference, ShowCaseProduct>({
                bind: 'provider',
                node: '@sage/xtrem-show-case/ShowCaseProvider',
                title: 'Provider',
                canFilter: true,
                valueField: 'textField',
                isHidden: true,
            }),
        ],
        filter() {
            return {
                provider: {
                    textField: {
                        _regex: 'zon',
                    },
                },
            };
        },
    })
    restrictedResultByCallbackFilter: ui.fields.Reference;

    @ui.decorators.referenceField<Reference, ShowCaseProduct>({
        parent() {
            return this.additionalBlock;
        },
        title: 'Restricted Results by Combined Callback Filter',
        helperTextField: 'description',
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        valueField: 'product',
        tunnelPage: null,
        columns: [
            ui.nestedFields.text({ bind: '_id', title: 'ID', canFilter: false }),
            ui.nestedFields.text({ bind: 'product', title: 'Product', canFilter: true }),
            ui.nestedFields.reference<Reference, ShowCaseProduct, ShowCaseProduct['provider']>({
                bind: 'provider',
                node: '@sage/xtrem-show-case/ShowCaseProvider',
                title: 'Provider',
                valueField: 'textField',
            }),
        ],
        filter() {
            return {
                provider: {
                    _or: [
                        {
                            textField: {
                                _regex: 'zon',
                            },
                        },
                        {
                            decimalField: '2.34',
                        },
                    ],
                },
            };
        },
    })
    restrictedResultByCombinedCallbackFilter: ui.fields.Reference<ShowCaseProduct, Reference>;

    @ui.decorators.referenceField<Reference, ShowCaseProduct>({
        parent() {
            return this.additionalBlock;
        },
        title: 'Restricted Results by filter',
        helperTextField: 'description',
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        valueField: 'product',
        tunnelPage: null,
        columns: [
            ui.nestedFields.text({ bind: '_id', title: 'ID', canFilter: false }),
            ui.nestedFields.text({ bind: 'product', title: 'Product', canFilter: true }),
            ui.nestedFields.reference<Reference, ShowCaseProduct>({
                bind: 'provider',
                node: '@sage/xtrem-show-case/ShowCaseProvider',
                title: 'Provider',
                canFilter: true,
                valueField: 'textField',
                isHidden: true,
            }),
        ],
        filter: {
            provider: {
                textField: 'Ali Express',
            },
        },
    })
    restrictedResultByFilter: ui.fields.Reference;

    @ui.decorators.separatorField<Reference>({
        parent() {
            return this.additionalBlock;
        },
        isFullWidth: true,
    })
    fieldSeparator1: ui.fields.Separator;

    @ui.decorators.referenceField<Reference, ShowCaseProduct>({
        parent() {
            return this.additionalBlock;
        },
        title: 'With warning message',
        warningMessage: 'Wow, warning!',
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        valueField: 'product',
        helperTextField: 'description',
        columns: null,
        tunnelPage: null,
    })
    warningMessageField: ui.fields.Reference<ShowCaseProduct>;

    @ui.decorators.referenceField<Reference, ShowCaseProduct>({
        parent() {
            return this.additionalBlock;
        },
        title: 'With warning message with callback',
        helperText: 'Select some wine',
        warningMessage() {
            if (
                this.warningMessageWithCallbackField.value &&
                this.warningMessageWithCallbackField.value.product?.toLowerCase().indexOf('wine') !== -1
            ) {
                return 'Warning message';
            }
            return null;
        },
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        valueField: 'product',
        helperTextField: 'description',
        columns: null,
        tunnelPage: null,
    })
    warningMessageWithCallbackField: ui.fields.Reference<ShowCaseProduct>;

    @ui.decorators.referenceField<Reference, ShowCaseProduct>({
        parent() {
            return this.additionalBlock;
        },
        title: 'With info message',
        infoMessage: 'Wow, warning!',
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        valueField: 'product',
        helperTextField: 'description',
        columns: null,
        tunnelPage: null,
    })
    infoMessageField: ui.fields.Reference<ShowCaseProduct>;

    @ui.decorators.referenceField<Reference, ShowCaseProduct>({
        parent() {
            return this.additionalBlock;
        },
        title: 'With info message with callback',
        helperText: 'Select some wine',
        infoMessage() {
            if (
                this.infoMessageWithCallbackField.value &&
                this.infoMessageWithCallbackField.value.product?.toLowerCase().indexOf('wine') !== -1
            ) {
                return 'Info message';
            }
            return null;
        },
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        valueField: 'product',
        helperTextField: 'description',
        columns: null,
        tunnelPage: null,
    })
    infoMessageWithCallbackField: ui.fields.Reference<ShowCaseProduct>;

    @ui.decorators.referenceField<Reference, ShowCaseProduct>({
        parent() {
            return this.additionalBlock;
        },
        title: 'With info message',
        warningMessage: 'Wow, warning!',
        infoMessage: 'You should not see this',
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        valueField: 'product',
        helperTextField: 'description',
        columns: null,
        tunnelPage: null,
    })
    infoAndWarningMessageField: ui.fields.Reference<ShowCaseProduct>;

    @ui.decorators.referenceField<Reference, ShowCaseProduct>({
        parent() {
            return this.additionalBlock;
        },
        isMandatory: true,
        title: 'Info, warning and validation',
        warningMessage: 'Wow, warning!',
        infoMessage: 'You should not see this',
        helperText: 'This field is mandatory too.',
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        valueField: 'product',
        helperTextField: 'description',
        columns: null,
        tunnelPage: null,
    })
    infoAndWarningMessageMandatoryField: ui.fields.Reference<ShowCaseProduct>;

    @ui.decorators.referenceField<Reference, ShowCaseProduct>({
        isHiddenMobile: true,
        parent() {
            return this.additionalBlock;
        },
        title: 'This field will be hidden in mobile',
        helperText: 'isHiddenMobile is true.',
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        valueField: 'product',
        columns: null,
        tunnelPage: null,
    })
    isHiddenOnMobileField: ui.fields.Reference<ShowCaseProduct>;

    @ui.decorators.referenceField<Reference, ShowCaseProduct>({
        parent() {
            return this.additionalBlock;
        },
        title: 'Mobile card',
        helperText: 'On mobile lookup dialog',
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        valueField: 'product',
        columns: [
            ui.nestedFields.text({ bind: '_id', title: 'ID', canFilter: false }),
            ui.nestedFields.text({ bind: 'product', title: 'Product', canFilter: true }),
            ui.nestedFields.text({ bind: 'description', canFilter: true, title: 'Description' }),
            ui.nestedFields.reference<Reference, ShowCaseProduct, ShowCaseProvider>({
                bind: 'provider',
                canFilter: true,
                node: '@sage/xtrem-show-case/ShowCaseProvider',
                title: 'Provider',
                valueField: 'textField',
            }),
        ],
        mobileCard: {
            title: ui.nestedFields.text({ bind: '_id', title: 'ID', canFilter: false }),
            line2: ui.nestedFields.text({ bind: 'product', title: 'Product', canFilter: true }),
            line3: ui.nestedFields.text({ bind: 'description', canFilter: true, title: 'Description' }),
        },
        tunnelPage: null,
    })
    hasMobileCard: ui.fields.Reference<ShowCaseProduct>;

    @ui.decorators.block<Reference>({
        parent() {
            return this.section;
        },
        title: 'Reference field with image',
    })
    withImageBlock: ui.containers.Block;

    @ui.decorators.referenceField<Reference, ShowCaseProduct>({
        columns: [
            ui.nestedFields.text({ bind: '_id', title: 'ID', canFilter: false }),
            ui.nestedFields.text({ bind: 'product', title: 'Product', canFilter: true }),
            ui.nestedFields.text({ bind: 'description', canFilter: true, title: 'Description' }),
        ],
        imageField: 'imageField',
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        shouldSuggestionsIncludeColumns: true,
        parent() {
            return this.withImageBlock;
        },
        valueField: 'product',
        title: 'Product with image',
        tunnelPage: null,
    })
    withImage: ui.fields.Reference;

    @ui.decorators.checkboxField<Reference>({
        parent() {
            return this.withImageBlock;
        },
        title: 'Is readOnly',
        onChange() {
            this.withImage.isReadOnly = this.withImageIsReadOnly.value;
        },
    })
    withImageIsReadOnly: ui.fields.Checkbox;

    @ui.decorators.checkboxField<Reference>({
        parent() {
            return this.withImageBlock;
        },
        title: 'Is Disabled',
        onChange() {
            this.withImage.isDisabled = this.withImageIsDisabled.value;
        },
    })
    withImageIsDisabled: ui.fields.Checkbox;

    @ui.decorators.block<Reference>({
        parent() {
            return this.section;
        },
        title: 'Reference Sizes',
    })
    sizesBlock: ui.containers.Block;

    @ui.decorators.referenceField<Reference, ShowCaseProduct>({
        columns: [
            ui.nestedFields.text({ bind: '_id', title: 'ID', canFilter: false }),
            ui.nestedFields.text({ bind: 'product', title: 'Product', canFilter: true }),
            ui.nestedFields.text({ bind: 'description', canFilter: true, title: 'Description' }),
            ui.nestedFields.reference<Reference, ShowCaseProduct, ShowCaseProvider>({
                bind: 'provider',
                canFilter: true,
                node: '@sage/xtrem-show-case/ShowCaseProvider',
                title: 'Provider',
                valueField: 'textField',
            }),
        ],
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        onClick() {
            this.clickTriggered.isHidden = false;
            setTimeout(() => {
                this.clickTriggered.isHidden = true;
            }, 5000);
        },
        onChange() {
            if (this.small.value !== this.value.value) {
                this.value.value = this.small.value;
                this.changeTriggered.isHidden = false;
                setTimeout(() => {
                    this.changeTriggered.isHidden = true;
                }, 5000);
            }
        },
        parent() {
            return this.sizesBlock;
        },
        valueField: 'product',
        imageField: 'imageField',
        size: 'small',
        title: 'Small',
        tunnelPage: null,
    })
    small: ui.fields.Reference;

    @ui.decorators.referenceField<Reference, ShowCaseProduct>({
        columns: [
            ui.nestedFields.text({ bind: '_id', title: 'ID', canFilter: false }),
            ui.nestedFields.text({ bind: 'product', title: 'Product', canFilter: true }),
            ui.nestedFields.text({ bind: 'description', canFilter: true, title: 'Description' }),
            ui.nestedFields.reference<Reference, ShowCaseProduct, ShowCaseProvider>({
                bind: 'provider',
                canFilter: true,
                node: '@sage/xtrem-show-case/ShowCaseProvider',
                title: 'Provider',
                valueField: 'textField',
            }),
        ],
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        onClick() {
            this.clickTriggered.isHidden = false;
            setTimeout(() => {
                this.clickTriggered.isHidden = true;
            }, 5000);
        },
        onChange() {
            if (this.medium.value !== this.value.value) {
                this.value.value = this.medium.value;
                this.changeTriggered.isHidden = false;
                setTimeout(() => {
                    this.changeTriggered.isHidden = true;
                }, 5000);
            }
        },
        parent() {
            return this.sizesBlock;
        },
        valueField: 'product',
        imageField: 'imageField',
        title: 'Medium',
        tunnelPage: null,
    })
    medium: ui.fields.Reference;

    @ui.decorators.referenceField<Reference, ShowCaseProduct>({
        columns: [
            ui.nestedFields.text({ bind: '_id', title: 'ID', canFilter: false }),
            ui.nestedFields.text({ bind: 'product', title: 'Product', canFilter: true }),
            ui.nestedFields.text({ bind: 'description', canFilter: true, title: 'Description' }),
            ui.nestedFields.reference<Reference, ShowCaseProduct, ShowCaseProvider>({
                bind: 'provider',
                canFilter: true,
                node: '@sage/xtrem-show-case/ShowCaseProvider',
                title: 'Provider',
                valueField: 'textField',
            }),
        ],
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        onClick() {
            this.clickTriggered.isHidden = false;
            setTimeout(() => {
                this.clickTriggered.isHidden = true;
            }, 5000);
        },
        onChange() {
            if (this.large.value !== this.value.value) {
                this.value.value = this.large.value;
                this.changeTriggered.isHidden = false;
                setTimeout(() => {
                    this.changeTriggered.isHidden = true;
                }, 5000);
            }
        },
        parent() {
            return this.sizesBlock;
        },
        valueField: 'product',
        imageField: 'imageField',
        size: 'large',
        title: 'Large',
        tunnelPage: null,
    })
    large: ui.fields.Reference;

    @ui.decorators.block<Reference>({
        parent() {
            return this.section;
        },
        title: 'Order block',
    })
    orderBlock: ui.containers.Block;

    @ui.decorators.referenceField<Reference, ShowCaseProduct>({
        columns: [
            ui.nestedFields.reference<Reference, ShowCaseProduct, ShowCaseProvider>({
                bind: 'provider',
                canFilter: true,
                node: '@sage/xtrem-show-case/ShowCaseProvider',
                title: 'Provider',
                valueField: 'textField',
            }),
            ui.nestedFields.text({ bind: '_id', title: 'ID', canFilter: false }),
            ui.nestedFields.text({ bind: 'product', title: 'Product', canFilter: true }),
            ui.nestedFields.text({ bind: 'description', canFilter: true, title: 'Description' }),
        ],
        helperText: 'Ordered by provider->textField->ASC (first column declared)',
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        parent() {
            return this.orderBlock;
        },
        valueField: 'product',
        imageField: 'imageField',
        isFullWidth: true,
        tunnelPage: null,
    })
    orderedField: ui.fields.Reference;

    @ui.decorators.referenceField<Reference, ShowCaseProduct>({
        columns: [
            ui.nestedFields.reference<Reference, ShowCaseProduct, ShowCaseProvider>({
                bind: 'provider',
                canFilter: true,
                node: '@sage/xtrem-show-case/ShowCaseProvider',
                title: 'Provider',
                valueField: 'textField',
            }),
            ui.nestedFields.text({ bind: '_id', title: 'ID', canFilter: false }),
            ui.nestedFields.text({ bind: 'product', title: 'Product', canFilter: true }),
            ui.nestedFields.text({ bind: 'description', canFilter: true, title: 'Description' }),
        ],
        helperText: 'Ordered by description DESC and then _id ASC',
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        parent() {
            return this.orderBlock;
        },
        valueField: 'product',
        imageField: 'imageField',
        isFullWidth: true,
        orderBy: { description: -1, _id: 1 },
        tunnelPage: null,
    })
    orderedField2: ui.fields.Reference;

    @ui.decorators.block<Reference>({
        parent() {
            return this.section;
        },
        title: 'Auto configured examples',
    })
    autoConfigBlock: ui.containers.Block;

    @ui.decorators.referenceField<Reference, ShowCaseCountry>({
        parent() {
            return this.autoConfigBlock;
        },
        node: '@sage/xtrem-show-case/ShowCaseCountry',
    })
    autoConfigCountry: ui.fields.Reference;

    @ui.decorators.referenceField<Reference, ShowCaseProduct>({
        parent() {
            return this.autoConfigBlock;
        },
        node: '@sage/xtrem-show-case/ShowCaseProduct',
    })
    autoConfigProduct: ui.fields.Reference;

    @ui.decorators.referenceField<Reference, ShowCaseProvider>({
        parent() {
            return this.autoConfigBlock;
        },
        node: '@sage/xtrem-show-case/ShowCaseProvider',
    })
    autoConfigProvider: ui.fields.Reference;
}
