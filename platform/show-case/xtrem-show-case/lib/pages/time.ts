import * as ui from '@sage/xtrem-ui';
import { fields } from '../menu-items/fields';

@ui.decorators.page<Time>({
    menuItem: fields,
    authorizationCode: 'BSCFLDS',
    module: 'show-case',
    title: 'Time',
    category: 'SHOWCASE',
    isTransient: true,
    headerSection() {
        return this.headerSection;
    },
})
export class Time extends ui.Page {
    @ui.decorators.section<Time>({
        title: 'Time',
        isTitleHidden: true,
    })
    headerSection: ui.containers.Section;

    @ui.decorators.section<Time>({
        title: 'Time field',
        isTitleHidden: true,
    })
    section: ui.containers.Section;

    @ui.decorators.block<Time>({
        parent() {
            return this.headerSection;
        },
        title: 'Introduction',
        width: 'medium',
    })
    introductionBlock: ui.containers.Block;

    @ui.decorators.staticContentField<Time>({
        parent() {
            return this.introductionBlock;
        },
        isTitleHidden: true,
        isFullWidth: true,
        content: 'Time fields can be used to represent time values.',
    })
    description: ui.fields.StaticContent;

    @ui.decorators.linkField<Time>({
        parent() {
            return this.introductionBlock;
        },
        title: 'Source code',
        isFullWidth: true,
        map() {
            return ui.localize('@sage/xtrem-show-case/check-source-code', 'Check it on GitHub');
        },
        page: 'https://github.com/Sage-ERP-X3/xtrem/blob/master/platform/show-case/xtrem-show-case/lib/pages/time.ts',
    })
    sourceCodeLink: ui.fields.Link;

    @ui.decorators.block<Time>({
        parent() {
            return this.headerSection;
        },
        title: 'Field example',
        width: 'medium',
    })
    fieldBlock: ui.containers.Block;

    @ui.decorators.timeField<Time>({
        parent() {
            return this.fieldBlock;
        },
        onClick() {
            this.clickTriggered.isHidden = false;
            setTimeout(() => {
                this.clickTriggered.isHidden = true;
            }, 1000);
        },
        onChange() {
            this.fieldCallback.value = this.field.value;
            this.value.value = this.field.value;
            this.setMinutes.value = this.field.minutes;
            this.setHours.value = this.field.hours;
            this.setSeconds.value = this.field.seconds;
            this.changeTriggered.isHidden = false;
            setTimeout(() => {
                this.changeTriggered.isHidden = true;
            }, 5000);
        },
        helperText: 'This field accept all values except for 13:00',
        validation() {
            if (this.field.value === '13:00:00') {
                return 'Value cannot be 13:00:00';
            }
            return undefined;
        },
    })
    field: ui.fields.Time;

    @ui.decorators.labelField<Time>({
        parent() {
            return this.fieldBlock;
        },
        isHidden: true,
        map() {
            return 'Change was triggered';
        },
    })
    changeTriggered: ui.fields.Label;

    @ui.decorators.labelField<Time>({
        parent() {
            return this.fieldBlock;
        },
        isHidden: true,
        map() {
            return 'Click was triggered';
        },
    })
    clickTriggered: ui.fields.Label;

    @ui.decorators.timeField<Time>({
        parent() {
            return this.fieldBlock;
        },
        title() {
            return this.title.value || '';
        },
        isDisabled() {
            return this.isDisabled.value || false;
        },
        isReadOnly() {
            return this.isReadOnly.value || false;
        },
        isHidden() {
            return this.isHidden.value || false;
        },
        onChange() {
            this.field.value = this.fieldCallback.value;
        },
        helperText: 'This field reacts to changes to the field next to it, except for validation errors',
    })
    fieldCallback: ui.fields.Time;

    @ui.decorators.block<Time>({
        parent() {
            return this.section;
        },
        title: 'Configuration',
    })
    configurationBlock: ui.containers.Block;

    @ui.decorators.checkboxField<Time>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is disabled',
        helperText:
            'Determines whether the field is disabled or not. It can also be defined as callback function that returns a boolean.',
        isFullWidth: true,
        isReversed: true,
        onChange() {
            this.field.isDisabled = !!this.isDisabled.value;
        },
    })
    isDisabled: ui.fields.Checkbox;

    @ui.decorators.checkboxField<Time>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is read only',
        helperText:
            'Whether the field is editable (isReadOnly = false) or not (isReadOnly = true). The difference with disabled is that isReadOnly suggests that the field is never editable. It can be defined as a boolean, or conditionally by a callback that returns a boolean.',
        isFullWidth: true,
        isReversed: true,
        onChange() {
            this.field.isReadOnly = !!this.isReadOnly.value;
        },
    })
    isReadOnly: ui.fields.Checkbox;

    @ui.decorators.checkboxField<Time>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is hidden',
        helperText: 'Determines whether the field is displayed or not.',
        isFullWidth: true,
        isReversed: true,
        onChange() {
            this.field.isHidden = !!this.isHidden.value;
        },
    })
    isHidden: ui.fields.Checkbox;

    @ui.decorators.textField<Time>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Title',
        isFullWidth: true,
        helperText:
            'The title that is displayed above the field. The title can be provided as a string, or a callback function returning a string. When declared as a callback within the column of a nested grid, the column id is provided as a parameter. It is automatically picked up by the i18n engine and externalized for translation.',
        onChange() {
            this.field.title = this.title.value || '';
        },
    })
    title: ui.fields.Text;

    @ui.decorators.checkboxField<Time>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is title hidden',
        isFullWidth: true,
        helperText: 'Whether the field title above the field should be displayed and its vertical space preserved.',
        isReversed: true,
        onChange() {
            this.field.isTitleHidden = !!this.isTitleHidden.value;
        },
    })
    isTitleHidden: ui.fields.Checkbox;

    @ui.decorators.textField<Time>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Helper text',
        helperText:
            'The helper text that is displayed above the field. It is automatically picked up by the i18n engine and externalized.',
        isFullWidth: true,
        onChange() {
            this.field.helperText = this.helperText.value || '';
        },
    })
    helperText: ui.fields.Text;

    @ui.decorators.checkboxField<Time>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is helper text hidden',
        helperText:
            'Whether the helper text underneath the field should be displayed and its vertical space preserved.',
        isFullWidth: true,
        isReversed: true,
        onChange() {
            this.field.isHelperTextHidden = !!this.isHelperTextHidden.value;
        },
    })
    isHelperTextHidden: ui.fields.Checkbox;

    @ui.decorators.checkboxField<Time>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is mandatory',
        helperText:
            ' Makes the field mandatory, empty values will raise an error message. It can also be defined as callback function that returns a boolean.',
        isFullWidth: true,
        isReversed: true,
        onChange() {
            this.field.isMandatory = !!this.isMandatory.value;
        },
    })
    isMandatory: ui.fields.Checkbox;

    @ui.decorators.textField<Time>({
        parent() {
            return this.additionalBlock;
        },
        title: 'Info message',
        helperText:
            'Indicate additional warning message, rendered as tooltip and blue border. It can also be defined as callback function.',
        isFullWidth: true,
        onChange() {
            this.field.infoMessage = this.infoMessage.value || '';
        },
    })
    infoMessage: ui.fields.Text;

    @ui.decorators.textField<Time>({
        parent() {
            return this.additionalBlock;
        },
        title: 'Warning message',
        helperText:
            'Indicate additional information, rendered as tooltip and orange border. It can also be defined as callback.',
        isFullWidth: true,
        onChange() {
            this.field.warningMessage = this.warningMessage.value || '';
        },
    })
    warningMessage: ui.fields.Text;

    @ui.decorators.textField<Time>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Value',
        isFullWidth: true,
        helperText: 'Accepted format is HH:mm:ss\nSetting an invalid time will raise an error message.',
        onChange() {
            if (this.field.value !== this.value.value) {
                this.field.value = this.value.value;
                this.fieldCallback.value = this.value.value;
            }
        },
    })
    value: ui.fields.Text;

    @ui.decorators.buttonField<Time>({
        parent() {
            return this.configurationBlock;
        },
        map() {
            return 'Set to null';
        },
        title: 'Set value to null',
        onClick() {
            this.field.value = null;
        },
    })
    setToNullButton: ui.fields.Button;

    @ui.decorators.numericField<Time>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Set hours',
        helperText: 'Set the hours of the field',
        onChange() {
            const numericValue = this.setHours.value;
            if (!Number.isNaN(numericValue) && Number.isInteger(numericValue)) {
                this.field.hours = numericValue;
            } else {
                this.field.hours = 0;
            }
        },
    })
    setHours: ui.fields.Numeric;

    @ui.decorators.numericField<Time>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Set minutes',
        helperText: 'Set the minutes of the field',
        onChange() {
            const numericValue = this.setMinutes.value;
            if (!Number.isNaN(numericValue) && Number.isInteger(numericValue)) {
                this.field.minutes = numericValue;
            } else {
                this.field.minutes = 0;
            }
        },
    })
    setMinutes: ui.fields.Numeric;

    @ui.decorators.numericField<Time>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Set seconds',
        helperText: 'Set the seconds of the field',
        onChange() {
            const numericValue = this.setSeconds.value;
            if (!Number.isNaN(numericValue) && Number.isInteger(numericValue)) {
                this.field.seconds = numericValue;
            } else {
                this.field.seconds = 0;
            }
        },
    })
    setSeconds: ui.fields.Numeric;

    @ui.decorators.buttonField<Time>({
        parent() {
            return this.configurationBlock;
        },
        map() {
            return 'Focus field';
        },
        onClick() {
            this.field.focus();
        },
    })
    focus: ui.fields.Button;

    @ui.decorators.block<Time>({
        parent() {
            return this.section;
        },
        title: 'Events',
    })
    eventsBlock: ui.containers.Block;

    @ui.decorators.staticContentField<Time>({
        parent() {
            return this.eventsBlock;
        },
        isTitleHidden: true,
        isFullWidth: true,
        isMarkdown: true,
        content:
            '-   **onClick**: Triggered when any parts of the field is clicked, no arguments provided.\n-   **onChange**: Triggered when the field value changed and the focus is about to move away from the field, no arguments provided.\n-   **onError**: Handles errors thrown from the callback functions.',
    })
    eventsDescription: ui.fields.StaticContent;

    @ui.decorators.block<Time>({
        parent() {
            return this.section;
        },
        title: 'Runtime functions',
    })
    runtimeFunctionsBlock: ui.containers.Block;

    @ui.decorators.staticContentField<Time>({
        parent() {
            return this.runtimeFunctionsBlock;
        },
        isTitleHidden: true,
        isFullWidth: true,
        isMarkdown: true,
        content:
            "-   **focus()**: Moves the focus to the field.\n-   **getNextField(isFocusable)**: Returns the next field instance. The order is calculated by the page prototype. If the isFocusable argument is set to true, it returns the next visible, enabled and non read-only field. It only considers the committed page state, so `commitValueAndPropertyChanges` call might be required beforehand to get the expected result.\n-   **refresh()**: Refetches the field's value from the server and updates it on the screen, only for non-transient pages.\n-   **validate()**: Triggers the field validation rules. Since the validation rules might be asynchronous, this method returns a promise that must be awaited to get the validation result\n-   **validateWithDetails()**: In addition to the functionality of `validate` it returns more details, including the rule that failed and where applicable, the row ID and colum ID.\n-   **fetchDefault(skipSet)**: Force re-fetches default value for the field. If the `skipSet` flag is set to true, it returns the default values but not apply them to the screen.\n-   **isDirty()**: Sets or gets the dirty state of the field.",
    })
    runtimeFunctionDescription: ui.fields.StaticContent;

    /* Additional examples */

    @ui.decorators.block<Time>({
        parent() {
            return this.section;
        },
        title: 'Additional examples',
    })
    additionalBlock: ui.containers.Block;

    @ui.decorators.timeField<Time>({
        parent() {
            return this.additionalBlock;
        },
        title: 'Mandatory',
        isMandatory: true,
    })
    mandatory: ui.fields.Time;

    @ui.decorators.timeField<Time>({
        parent() {
            return this.additionalBlock;
        },
        title: 'Full width',
        isFullWidth: true,
    })
    fullWidth: ui.fields.Time;

    @ui.decorators.block<Time>({
        parent() {
            return this.section;
        },
        title: 'Dynamic validations',
    })
    dynamicValidations: ui.containers.Block;

    @ui.decorators.timeField<Time>({
        parent() {
            return this.dynamicValidations;
        },
        title: 'Mandatory By Setter',
    })
    mandatoryBySetter: ui.fields.Time;

    @ui.decorators.switchField<Time>({
        parent() {
            return this.dynamicValidations;
        },
        title: 'Is Mandatory?',
        onChange() {
            this.mandatoryBySetter.isMandatory = this.mandatoryBySetterSwitch.value || undefined;
        },
    })
    mandatoryBySetterSwitch: ui.fields.Switch;

    @ui.decorators.separatorField<Time>({
        parent() {
            return this.dynamicValidations;
        },
        isFullWidth: true,
    })
    mandatorySeparator1: ui.fields.Separator;

    @ui.decorators.timeField<Time>({
        parent() {
            return this.dynamicValidations;
        },
        title: 'Mandatory By Callback',
        isMandatory() {
            return this.mandatoryByCallbackSwitch.value || false;
        },
    })
    mandatoryByCallback: ui.fields.Time;

    @ui.decorators.switchField<Time>({
        parent() {
            return this.dynamicValidations;
        },
        title: 'Is Mandatory?',
    })
    mandatoryByCallbackSwitch: ui.fields.Switch;

    @ui.decorators.separatorField<Time>({
        parent() {
            return this.additionalBlock;
        },
        isFullWidth: true,
    })
    fieldSeparator1: ui.fields.Separator;

    @ui.decorators.timeField<Time>({
        parent() {
            return this.additionalBlock;
        },
        title: 'With warning message',
        warningMessage: 'Wow, warning!',
    })
    warningMessageField: ui.fields.Time;

    @ui.decorators.timeField<Time>({
        parent() {
            return this.additionalBlock;
        },
        title: 'With warning message with callback',
        helperText: 'Enter 09:00',
        warningMessage() {
            if (this.warningMessageWithCallbackField.value === '09:00:00') {
                return 'Warning message';
            }
            return null;
        },
    })
    warningMessageWithCallbackField: ui.fields.Time;

    @ui.decorators.timeField<Time>({
        parent() {
            return this.additionalBlock;
        },
        title: 'With info message',
        infoMessage: 'Wow, warning!',
    })
    infoMessageField: ui.fields.Time;

    @ui.decorators.timeField<Time>({
        parent() {
            return this.additionalBlock;
        },
        title: 'With info message with callback',
        helperText: 'Enter "09:00"',
        infoMessage() {
            if (this.infoMessageWithCallbackField.value === '09:00:00') {
                return 'Info message';
            }
            return null;
        },
    })
    infoMessageWithCallbackField: ui.fields.Time;

    @ui.decorators.timeField<Time>({
        parent() {
            return this.additionalBlock;
        },
        title: 'With info message',
        warningMessage: 'Wow, warning!',
        infoMessage: 'You should not see this',
    })
    infoAndWarningMessageField: ui.fields.Time;

    @ui.decorators.timeField<Time>({
        parent() {
            return this.additionalBlock;
        },
        isMandatory: true,
        title: 'Info, warning and validation',
        warningMessage: 'Wow, warning!',
        infoMessage: 'You should not see this',
        helperText: 'This field is mandatory too.',
    })
    infoAndWarningMessageMandatoryField: ui.fields.Time;
}
