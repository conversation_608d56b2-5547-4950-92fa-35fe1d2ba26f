import { Graph<PERSON><PERSON>, ShowCaseProvider } from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import { misc } from '../menu-items/misc';

@ui.decorators.page<CrudUpdate, ShowCaseProvider>({
    authorizationCode: 'SHCPRDT',
    module: 'show-case',
    title: 'CRUD - Update',
    node: '@sage/xtrem-show-case/ShowCaseProvider',
    onLoad() {
        this.saveCrudUpdate.isDisabled = !this.$.queryParameters._id;
    },
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.text({ bind: 'textField' }),
            titleRight: ui.nestedFields.text({ bind: '_id' }),
            image: ui.nestedFields.image({ bind: 'logo' }),
        },
    },
    businessActions() {
        return [this.saveCrudUpdate];
    },
    menuItem: misc,
})
export class CrudUpdate extends ui.Page<GraphApi> {
    @ui.decorators.section<CrudUpdate>({})
    section: ui.containers.Section;

    @ui.decorators.block<CrudUpdate>({
        parent() {
            return this.section;
        },
    })
    block: ui.containers.Block;

    @ui.decorators.textField<CrudUpdate>({
        parent() {
            return this.block;
        },
        title: 'Provider',
    })
    textField: ui.fields.Text;

    @ui.decorators.textField<CrudUpdate>({
        parent() {
            return this.block;
        },
        title: 'Some decimal info',
    })
    decimalField: ui.fields.Text;

    @ui.decorators.imageField<CrudUpdate>({
        parent() {
            return this.block;
        },
        title: 'Logo',
    })
    logo: ui.fields.Image;

    @ui.decorators.pageAction<CrudUpdate>({
        title: 'Save',
        onClick() {
            this.$.graph.update();
        },
    })
    saveCrudUpdate: ui.PageAction;
}
