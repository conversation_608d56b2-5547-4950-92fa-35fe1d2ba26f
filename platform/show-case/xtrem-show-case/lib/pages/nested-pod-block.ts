import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Show<PERSON><PERSON><PERSON>rovider, ShowCaseProduct, ShowCaseProviderAddress } from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import { pod } from '../menu-items/pod';

@ui.decorators.page<NestedPodBlock, ShowCaseProduct>({
    authorizationCode: 'SHCPRVD',
    category: 'SHOWCASE',
    menuItem: pod,
    module: 'show-case',
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.text({ bind: 'product' }),
            titleRight: ui.nestedFields.text({ bind: '_id' }),
            line2: ui.nestedFields.text({ bind: 'description', canFilter: false }),
        },
    },
    node: '@sage/xtrem-show-case/ShowCaseProduct',
    title: 'Pod Block with Nested Reference Arrays',
})
export class NestedPodBlock extends ui.Page<GraphApi> {
    @ui.decorators.section<NestedPodBlock>({})
    mainSection: ui.containers.Section;

    @ui.decorators.podField<NestedPodBlock, ShowCaseProvider>({
        title: 'Provider',
        width: 'large',
        parent() {
            return this.mainSection;
        },
        canRemove: true,
        columns: [
            ui.nestedFields.text({ bind: 'textField', title: 'Text field' }),
            ui.nestedFields.text({ bind: 'integerField', title: 'Numeric field' }),
            ui.nestedFields.text({ bind: '_id', title: 'id' }),
            ui.nestedFields.text({ bind: 'dateField', title: 'date field' }),
            ui.nestedFields.multiReference<NestedPodBlock, ShowCaseProvider, ShowCaseProviderAddress>({
                bind: 'addresses',
                node: '@sage/xtrem-show-case/ShowCaseProviderAddress',
                minLookupCharacters: 0,
                valueField: 'name',
                title: 'Addresses',
                isFullWidth: true,
            }),
        ],
        node: '@sage/xtrem-show-case/ShowCaseProvider',
    })
    provider: ui.fields.Pod;
}
