import { Graph<PERSON><PERSON>, ShowCaseProduct } from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import { misc } from '../menu-items/misc';

@ui.decorators.page<UniquePodActions>({
    authorizationCode: 'UNIQPODACTN',
    category: 'SHOWCASE',
    menuItem: misc,
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.text({
                bind: 'textField',
                title: 'Provider',
            }),
            line2: ui.nestedFields.text({
                bind: '_id',
                title: 'Id',
            }),
        },
    },
    node: '@sage/xtrem-show-case/ShowCaseProvider',
    title: 'Unique Pod Actions',
})
export class UniquePodActions extends ui.Page<GraphApi> {
    @ui.decorators.section<UniquePodActions>({})
    section: ui.containers.Section;

    @ui.decorators.block<UniquePodActions>({
        parent() {
            return this.section;
        },
    })
    block: ui.containers.Block;

    @ui.decorators.block<UniquePodActions>({
        parent() {
            return this.section;
        },
        title: 'Actions',
    })
    actions: ui.containers.Block;

    @ui.decorators.podField<UniquePodActions, ShowCaseProduct>({
        bind: 'flagshipProduct',
        columns: [
            ui.nestedFields.text({
                bind: '_id',
                title: 'Id',
            }),
            ui.nestedFields.text({
                bind: 'product',
                title: 'Product',
            }),
            ui.nestedFields.reference({
                bind: 'provider',
                isReadOnly: true,
                node: '@sage/xtrem-show-case/ShowCaseProvider',
                title: 'Provider',
                valueField: 'textField',
            }),
        ],
        dropdownActions: [
            {
                icon: 'analysis',
                onClick() {
                    this.$.showToast('This is an dropdown action with no action id and title "Analysis".');
                },
                title: 'Analysis',
            },
            {
                id: 'myCollaborateAction',
                icon: 'collaborate',
                onClick() {
                    this.$.showToast(
                        'This is an dropdown action with action id "myCollaborateAction" and title "Collaborate".',
                    );
                },
                title: 'Collaborate',
            },
        ],
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        parent() {
            return this.block;
        },
        width: 'medium',
    })
    pod: ui.fields.Pod;

    @ui.decorators.podCollectionField<UniquePodActions, ShowCaseProduct>({
        bind: 'products',
        columns: [
            ui.nestedFields.text({
                bind: '_id',
                title: 'Id',
            }),
            ui.nestedFields.text({
                bind: 'product',
                title: 'Product',
            }),
            ui.nestedFields.reference({
                bind: 'provider',
                isReadOnly: true,
                node: '@sage/xtrem-show-case/ShowCaseProvider',
                title: 'Provider',
                valueField: 'textField',
            }),
        ],
        dropdownActions: [
            {
                icon: 'analysis',
                onClick() {
                    this.$.showToast('This is an dropdown action with no action id and title "Analysis".');
                },
                title: 'Analysis',
            },
            {
                id: 'myCollaborateAction',
                icon: 'collaborate',
                onClick() {
                    this.$.showToast(
                        'This is an dropdown action with action id "myCollaborateAction" and title "Collaborate".',
                    );
                },
                title: 'Collaborate',
            },
        ],
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        orderBy: { _id: -1 },
        parent() {
            return this.actions;
        },
    })
    products: ui.fields.PodCollection<ShowCaseProduct>;
}
