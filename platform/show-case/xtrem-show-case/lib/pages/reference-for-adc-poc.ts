import { Graph<PERSON><PERSON>, ShowCaseProduct, ShowCaseProvider } from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import { referenceField } from '../menu-items/reference-field';

@ui.decorators.page<ReferenceForAdcPoc>({
    authorizationCode: 'BSCFLDS',
    module: 'show-case',
    title: 'Reference ADC usecase',
    category: 'SHOWCASE',
    menuItem: referenceField,
    isTransient: true,
    onLoad() {
        this.sampleCodes.value =
            'One match:\n3550952118857630\nTwo matches:\n3576421148211549\nNo matches:\n111111111111';
    },
})
export class ReferenceForAdcPoc extends ui.Page<GraphApi> {
    @ui.decorators.section<ReferenceForAdcPoc>({})
    section: ui.containers.Section;

    @ui.decorators.block<ReferenceForAdcPoc>({
        parent() {
            return this.section;
        },
    })
    fieldBlock: ui.containers.Block;

    @ui.decorators.referenceField<ReferenceForAdcPoc, ShowCaseProduct>({
        columns: [
            ui.nestedFields.text({ bind: '_id', title: 'ID', canFilter: false }),
            ui.nestedFields.text({ bind: 'product', title: 'Product', canFilter: true }),
            ui.nestedFields.text({ bind: 'description', canFilter: true, title: 'Description' }),
            ui.nestedFields.text({ bind: 'barcode', canFilter: true, title: 'Barcode' }),
            ui.nestedFields.reference<ReferenceForAdcPoc, ShowCaseProduct, ShowCaseProvider>({
                bind: 'provider',
                canFilter: true,
                node: '@sage/xtrem-show-case/ShowCaseProvider',
                title: 'Provider',
                valueField: 'textField',
            }),
        ],
        helperTextField: 'description',
        title: 'Product 1',
        shouldSuggestionsIncludeColumns: true,
        width: 'large',
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        isAutoSelectEnabled: true,
        onChange() {
            this.field2.focus();
        },
        parent() {
            return this.fieldBlock;
        },
        valueField: 'product',
        tunnelPage: null,
    })
    field1: ui.fields.Reference;

    @ui.decorators.referenceField<ReferenceForAdcPoc, ShowCaseProduct>({
        columns: [
            ui.nestedFields.text({ bind: '_id', title: 'ID', canFilter: false }),
            ui.nestedFields.text({ bind: 'product', title: 'Product', canFilter: true }),
            ui.nestedFields.text({ bind: 'description', canFilter: true, title: 'Description' }),
            ui.nestedFields.text({ bind: 'barcode', canFilter: true, title: 'Barcode' }),
            ui.nestedFields.reference<ReferenceForAdcPoc, ShowCaseProduct, ShowCaseProvider>({
                bind: 'provider',
                canFilter: true,
                node: '@sage/xtrem-show-case/ShowCaseProvider',
                title: 'Provider',
                valueField: 'textField',
            }),
        ],
        helperTextField: 'description',
        title: 'Product 2',
        shouldSuggestionsIncludeColumns: true,
        width: 'large',
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        isAutoSelectEnabled: true,
        onChange() {
            this.field3.focus();
        },
        parent() {
            return this.fieldBlock;
        },
        valueField: 'product',
        tunnelPage: null,
    })
    field2: ui.fields.Reference;

    @ui.decorators.referenceField<ReferenceForAdcPoc, ShowCaseProduct>({
        columns: [
            ui.nestedFields.text({ bind: '_id', title: 'ID', canFilter: false }),
            ui.nestedFields.text({ bind: 'product', title: 'Product', canFilter: true }),
            ui.nestedFields.text({ bind: 'description', canFilter: true, title: 'Description' }),
            ui.nestedFields.text({ bind: 'barcode', canFilter: true, title: 'Barcode' }),
            ui.nestedFields.reference<ReferenceForAdcPoc, ShowCaseProduct, ShowCaseProvider>({
                bind: 'provider',
                canFilter: true,
                node: '@sage/xtrem-show-case/ShowCaseProvider',
                title: 'Provider',
                valueField: 'textField',
            }),
        ],
        helperTextField: 'description',
        title: 'Product 3',
        shouldSuggestionsIncludeColumns: true,
        width: 'large',
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        isAutoSelectEnabled: true,
        onChange() {
            this.field1.focus();
        },
        parent() {
            return this.fieldBlock;
        },
        valueField: 'product',
        tunnelPage: null,
    })
    field3: ui.fields.Reference;

    @ui.decorators.textAreaField<ReferenceForAdcPoc>({
        title: 'Sample codes',
        width: 'large',
        isDisabled: true,
        rows: 6,
        parent() {
            return this.fieldBlock;
        },
    })
    sampleCodes: ui.fields.TextArea;
}
