import * as ui from '@sage/xtrem-ui';
import { fields } from '../menu-items/fields';

type Todo = { _id: string; mandatory: string; readonly: string; optional: string };
const initialValues = [
    {
        _id: '2',
        mandatory: 'a',
        readonly: 'a',
        optional: 'a',
    },
    {
        _id: '1',
        mandatory: 'b',
        readonly: 'b',
        optional: 'b',
    },
];

@ui.decorators.page<RowActions>({
    title: 'Row actions',
    subtitle: 'Combinations of dropdown, inline and phantom row actions',
    // defaultEntry: () => '2',
    // node: '@sage/xtrem-show-case/ShowCaseProvider',
    mode: 'tabs',
    menuItem: fields,
    onLoad() {
        // this.table1.isReadOnly = false;
        this.table1.value = [...initialValues];
        this.table2.value = [...initialValues];
        this.table3.value = [...initialValues];
        this.table4.value = [...initialValues];
        this.table5.value = [...initialValues];
        this.table6.value = [...initialValues];
        this.table7.value = [...initialValues];
        this.table8.value = [...initialValues];
        this.table9.value = [...initialValues];
        this.table91.value = [...initialValues];
        this.table910.value = [...initialValues];
        this.table911.value = [...initialValues];
        this.table10.value = [...initialValues];
        this.table11.value = [...initialValues];
        this.table12.value = [...initialValues];
        this.table13.value = [...initialValues];
        this.table14.value = [...initialValues];
        this.table15.value = [...initialValues];
        this.table16.value = [...initialValues];
        this.table17.value = [...initialValues];
        this.table18.value = [...initialValues];
        this.table19.value = [...initialValues];
        this.table112.value = [...initialValues];
    },
})
export class RowActions extends ui.Page {
    @ui.decorators.section<RowActions>({
        title: 'Multiple action',
    })
    section1: ui.containers.Section;

    @ui.decorators.block<RowActions>({
        title: 'No action',
        parent() {
            return this.section2;
        },
    })
    section1block1: ui.containers.Block;

    @ui.decorators.tableField<RowActions, Todo>({
        isTransient: true,
        title: '',
        canSelect: false,
        // canAddNewLine: true,
        columns: [
            ui.nestedFields.text({
                bind: '_id',
                title: 'Id',
                isHiddenOnMainField: true,
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                bind: 'mandatory',
                title: 'Mandatory',
                isMandatory: true,
            }),
            ui.nestedFields.text({
                bind: 'readonly',
                title: 'Readonly',
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                bind: 'optional',
                title: 'Optional',
            }),
        ],
        orderBy: { _id: -1 },
        parent() {
            return this.section1block1;
        },
    })
    table1: ui.fields.Table<Todo>;

    @ui.decorators.checkboxField<RowActions>({
        onChange() {
            this.table1.isReadOnly = this.table1isReadOnly.value;
        },
        parent() {
            return this.section1block1;
        },
        title: 'isReadonly',
        isTransient: true,
    })
    table1isReadOnly: ui.fields.Checkbox;

    @ui.decorators.checkboxField<RowActions>({
        onChange() {
            this.table1.canSelect = this.table1canSelect.value;
            this.table1.redraw();
        },
        parent() {
            return this.section1block1;
        },
        title: 'canSelect',
        isTransient: true,
    })
    table1canSelect: ui.fields.Checkbox;

    @ui.decorators.checkboxField<RowActions>({
        onChange() {
            this.table1.canResizeColumns = this.table1canResizeColumns.value;
            this.table1.redraw();
        },
        parent() {
            return this.section1block1;
        },
        title: 'canResizeColumns',
        isTransient: true,
    })
    table1canResizeColumns: ui.fields.Checkbox;

    @ui.decorators.block<RowActions>({
        title: 'No action + canAddNewLine',
        parent() {
            return this.section2;
        },
    })
    section1block2: ui.containers.Block;

    @ui.decorators.tableField<RowActions, Todo>({
        isTransient: true,
        title: '',
        canSelect: false,
        canAddNewLine: true,
        columns: [
            ui.nestedFields.text({
                bind: '_id',
                title: 'Id',
                isHiddenOnMainField: true,
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                bind: 'mandatory',
                title: 'Mandatory',
                isMandatory: true,
            }),
            ui.nestedFields.text({
                bind: 'readonly',
                title: 'Readonly',
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                bind: 'optional',
                title: 'Optional',
            }),
        ],
        orderBy: { _id: -1 },
        parent() {
            return this.section1block2;
        },
    })
    table2: ui.fields.Table<Todo>;

    @ui.decorators.checkboxField<RowActions>({
        onChange() {
            this.table2.isReadOnly = this.table2isReadOnly.value;
        },
        parent() {
            return this.section1block2;
        },
        title: 'isReadonly',
        isTransient: true,
    })
    table2isReadOnly: ui.fields.Checkbox;

    @ui.decorators.checkboxField<RowActions>({
        onChange() {
            this.table2.canSelect = this.table2canSelect.value;
            this.table2.redraw();
        },
        parent() {
            return this.section1block2;
        },
        title: 'canSelect',
        isTransient: true,
    })
    table2canSelect: ui.fields.Checkbox;

    @ui.decorators.checkboxField<RowActions>({
        onChange() {
            this.table2.canResizeColumns = this.table2canResizeColumns.value;
            this.table2.redraw();
        },
        parent() {
            return this.section1block2;
        },
        title: 'canResizeColumns',
        isTransient: true,
    })
    table2canResizeColumns: ui.fields.Checkbox;

    @ui.decorators.block<RowActions>({
        title: 'Single dropdown',
        parent() {
            return this.section2;
        },
    })
    section1block3: ui.containers.Block;

    @ui.decorators.tableField<RowActions, Todo>({
        isTransient: true,
        title: '',
        canSelect: false,
        // canAddNewLine: true,
        columns: [
            ui.nestedFields.text({
                bind: '_id',
                title: 'Id',
                isHiddenOnMainField: true,
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                bind: 'mandatory',
                title: 'Mandatory',
                isMandatory: true,
            }),
            ui.nestedFields.text({
                bind: 'readonly',
                title: 'Readonly',
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                bind: 'optional',
                title: 'Optional',
            }),
        ],
        orderBy: { _id: -1 },
        dropdownActions: [
            {
                icon: 'tag',
                title: 'Tag',
                isDisabled() {
                    return false;
                },
                children: [
                    {
                        title: 'Tag',
                        isDisabled() {
                            return false;
                        },
                        onClick(rowId: any, data: any) {
                            this.$.dialog.message(
                                'info',
                                'Add Row Action was clicked',
                                `Optional: ${data.optional} Row: ${rowId}`,
                            );
                        },
                    },
                    {
                        title: 'Tag',
                        isDisabled(_rowId, data) {
                            return data.mandatory === 'a';
                        },
                        onClick(rowId: any, data: any) {
                            this.$.dialog.message(
                                'info',
                                'Add Row Action was clicked',
                                `Optional: ${data.optional} Row: ${rowId}`,
                            );
                        },
                    },
                    ui.menuSeparator(),
                    {
                        title: 'Tag',
                        isDisabled() {
                            return false;
                        },
                        onClick(rowId: any, data: any) {
                            this.$.dialog.message(
                                'info',
                                'Add Row Action was clicked',
                                `Optional: ${data.optional} Row: ${rowId}`,
                            );
                        },
                    },
                ],
            },
        ],
        parent() {
            return this.section1block3;
        },
    })
    table3: ui.fields.Table<Todo>;

    @ui.decorators.checkboxField<RowActions>({
        onChange() {
            this.table3.isReadOnly = this.table3isReadOnly.value;
        },
        parent() {
            return this.section1block3;
        },
        title: 'isReadonly',
        isTransient: true,
    })
    table3isReadOnly: ui.fields.Checkbox;

    @ui.decorators.checkboxField<RowActions>({
        onChange() {
            this.table3.canSelect = this.table3canSelect.value;
            this.table3.redraw();
        },
        parent() {
            return this.section1block3;
        },
        title: 'canSelect',
        isTransient: true,
    })
    table3canSelect: ui.fields.Checkbox;

    @ui.decorators.checkboxField<RowActions>({
        onChange() {
            this.table3.canResizeColumns = this.table3canResizeColumns.value;
            this.table3.redraw();
        },
        parent() {
            return this.section1block3;
        },
        title: 'canResizeColumns',
        isTransient: true,
    })
    table3canResizeColumns: ui.fields.Checkbox;

    @ui.decorators.block<RowActions>({
        title: 'Single dropdown + canAddNewLine',
        parent() {
            return this.section2;
        },
    })
    section1block4: ui.containers.Block;

    @ui.decorators.tableField<RowActions, Todo>({
        isTransient: true,
        title: '',
        canSelect: false,
        canAddNewLine: true,
        columns: [
            ui.nestedFields.text({
                bind: '_id',
                title: 'Id',
                isHiddenOnMainField: true,
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                bind: 'mandatory',
                title: 'Mandatory',
                isMandatory: true,
            }),
            ui.nestedFields.text({
                bind: 'readonly',
                title: 'Readonly',
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                bind: 'optional',
                title: 'Optional',
            }),
        ],
        orderBy: { _id: -1 },
        dropdownActions: [
            {
                icon: 'draft',
                title: 'Draft',
                isDisabled() {
                    return false;
                },
                onClick(rowId: any, data: any) {
                    this.$.dialog.message(
                        'info',
                        'Add Row Action was clicked',
                        `Optional: ${data.optional} Row: ${rowId}`,
                    );
                },
            },
        ],
        parent() {
            return this.section1block4;
        },
    })
    table4: ui.fields.Table<Todo>;

    @ui.decorators.checkboxField<RowActions>({
        onChange() {
            this.table4.isReadOnly = this.table4isReadOnly.value;
        },
        parent() {
            return this.section1block4;
        },
        title: 'isReadonly',
        isTransient: true,
    })
    table4isReadOnly: ui.fields.Checkbox;

    @ui.decorators.checkboxField<RowActions>({
        onChange() {
            this.table4.canSelect = this.table4canSelect.value;
            this.table4.redraw();
        },
        parent() {
            return this.section1block4;
        },
        title: 'canSelect',
        isTransient: true,
    })
    table4canSelect: ui.fields.Checkbox;

    @ui.decorators.checkboxField<RowActions>({
        onChange() {
            this.table4.canResizeColumns = this.table4canResizeColumns.value;
            this.table4.redraw();
        },
        parent() {
            return this.section1block4;
        },
        title: 'canResizeColumns',
        isTransient: true,
    })
    table4canResizeColumns: ui.fields.Checkbox;

    @ui.decorators.block<RowActions>({
        title: 'Multiple dropdown',
        parent() {
            return this.section2;
        },
    })
    section1block5: ui.containers.Block;

    @ui.decorators.tableField<RowActions, Todo>({
        isTransient: true,
        title: '',
        canSelect: false,
        // canAddNewLine: true,
        columns: [
            ui.nestedFields.text({
                bind: '_id',
                title: 'Id',
                isHiddenOnMainField: true,
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                bind: 'mandatory',
                title: 'Mandatory',
                isMandatory: true,
            }),
            ui.nestedFields.text({
                bind: 'readonly',
                title: 'Readonly',
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                bind: 'optional',
                title: 'Optional',
            }),
        ],
        orderBy: { _id: -1 },
        dropdownActions: [
            {
                icon: 'add',
                title: 'Add',
                isDisabled() {
                    return false;
                },
                onClick(rowId: any, data: any) {
                    this.$.dialog.message(
                        'info',
                        'Add Row Action was clicked',
                        `Optional: ${data.optional} Row: ${rowId}`,
                    );
                },
            },
            {
                icon: 'disconnect',
                title: 'Remove',
                isDestructive: true,
                isDisabled() {
                    return false;
                },
                onClick(rowId: any, data: any) {
                    this.$.dialog.message(
                        'info',
                        'Add Row Action was clicked',
                        `Optional: ${data.optional} Row: ${rowId}`,
                    );
                },
            },
        ],
        parent() {
            return this.section1block5;
        },
    })
    table5: ui.fields.Table<Todo>;

    @ui.decorators.checkboxField<RowActions>({
        onChange() {
            this.table5.isReadOnly = this.table5isReadOnly.value;
        },
        parent() {
            return this.section1block5;
        },
        title: 'isReadonly',
        isTransient: true,
    })
    table5isReadOnly: ui.fields.Checkbox;

    @ui.decorators.checkboxField<RowActions>({
        onChange() {
            this.table5.canSelect = this.table5canSelect.value;
            this.table5.redraw();
        },
        parent() {
            return this.section1block5;
        },
        title: 'canSelect',
        isTransient: true,
    })
    table5canSelect: ui.fields.Checkbox;

    @ui.decorators.checkboxField<RowActions>({
        onChange() {
            this.table5.canResizeColumns = this.table5canResizeColumns.value;
            this.table5.redraw();
        },
        parent() {
            return this.section1block5;
        },
        title: 'canResizeColumns',
        isTransient: true,
    })
    table5canResizeColumns: ui.fields.Checkbox;

    @ui.decorators.block<RowActions>({
        title: 'Multiple dropdown + canAddNewLine',
        parent() {
            return this.section2;
        },
    })
    section1block6: ui.containers.Block;

    @ui.decorators.tableField<RowActions, Todo>({
        isTransient: true,
        title: '',
        canSelect: false,
        canAddNewLine: true,
        columns: [
            ui.nestedFields.text({
                bind: '_id',
                title: 'Id',
                isHiddenOnMainField: true,
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                bind: 'mandatory',
                title: 'Mandatory',
                isMandatory: true,
            }),
            ui.nestedFields.text({
                bind: 'readonly',
                title: 'Readonly',
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                bind: 'optional',
                title: 'Optional',
            }),
        ],
        orderBy: { _id: -1 },
        dropdownActions: [
            {
                icon: 'add',
                title: 'Add',
                isDisabled() {
                    return false;
                },
                children: [
                    {
                        title: 'Add one',
                        isDisabled() {
                            return false;
                        },
                        onClick(rowId: any, data: any) {
                            this.$.dialog.message(
                                'info',
                                'Add Row Action was clicked',
                                `Optional: ${data.optional} Row: ${rowId}`,
                            );
                        },
                    },
                    ui.menuSeparator(),
                    {
                        title: 'Add multiple',
                        isDisabled() {
                            return false;
                        },
                        children: [
                            {
                                title: 'Add two',
                                isDisabled() {
                                    return false;
                                },
                                onClick(rowId: any, data: any) {
                                    this.$.dialog.message(
                                        'info',
                                        'Add Row Action was clicked',
                                        `Optional: ${data.optional} Row: ${rowId}`,
                                    );
                                },
                            },
                            ui.menuSeparator(),
                            {
                                title: 'Add three',
                                isDisabled() {
                                    return false;
                                },
                                onClick(rowId: any, data: any) {
                                    this.$.dialog.message(
                                        'info',
                                        'Add Row Action was clicked',
                                        `Optional: ${data.optional} Row: ${rowId}`,
                                    );
                                },
                            },
                        ],
                    },
                ],
            },
            {
                icon: 'settings',
                title: 'Settings',
                isDisabled() {
                    return false;
                },
                onClick(rowId: any, data: any) {
                    this.$.dialog.message(
                        'info',
                        'Add Row Action was clicked',
                        `Optional: ${data.optional} Row: ${rowId}`,
                    );
                },
            },
            {
                icon: 'none',
                title: 'Profile',
                isDisabled() {
                    return false;
                },
                onClick(rowId: any, data: any) {
                    this.$.dialog.message(
                        'info',
                        'Add Row Action was clicked',
                        `Optional: ${data.optional} Row: ${rowId}`,
                    );
                },
            },
        ],
        parent() {
            return this.section1block6;
        },
    })
    table6: ui.fields.Table<Todo>;

    @ui.decorators.checkboxField<RowActions>({
        onChange() {
            this.table6.isReadOnly = this.table6isReadOnly.value;
        },
        parent() {
            return this.section1block6;
        },
        title: 'isReadonly',
        isTransient: true,
    })
    table6isReadOnly: ui.fields.Checkbox;

    @ui.decorators.checkboxField<RowActions>({
        onChange() {
            this.table6.canSelect = this.table6canSelect.value;
            this.table6.redraw();
        },
        parent() {
            return this.section1block6;
        },
        title: 'canSelect',
        isTransient: true,
    })
    table6canSelect: ui.fields.Checkbox;

    @ui.decorators.checkboxField<RowActions>({
        onChange() {
            this.table6.canResizeColumns = this.table6canResizeColumns.value;
            this.table6.redraw();
        },
        parent() {
            return this.section1block6;
        },
        title: 'canResizeColumns',
        isTransient: true,
    })
    table6canResizeColumns: ui.fields.Checkbox;

    @ui.decorators.block<RowActions>({
        title: 'Single inline',
        parent() {
            return this.section2;
        },
    })
    section1block7: ui.containers.Block;

    @ui.decorators.tableField<RowActions, Todo>({
        isTransient: true,
        title: '',
        canSelect: false,
        // canAddNewLine: true,
        columns: [
            ui.nestedFields.text({
                bind: '_id',
                title: 'Id',
                isHiddenOnMainField: true,
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                bind: 'mandatory',
                title: 'Mandatory',
                isMandatory: true,
            }),
            ui.nestedFields.text({
                bind: 'readonly',
                title: 'Readonly',
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                bind: 'optional',
                title: 'Optional',
            }),
        ],
        orderBy: { _id: -1 },
        inlineActions: [
            {
                icon: 'chart_line',
                title: 'Chart line',
                onClick(rowId: any, data: any) {
                    this.$.dialog.message(
                        'info',
                        'Add Inline Row Action was clicked',
                        `Optional: ${data.optional} Row: ${rowId}`,
                    );
                },
                buttonType: 'tertiary',
            },
        ],
        parent() {
            return this.section1block7;
        },
    })
    table7: ui.fields.Table<Todo>;

    @ui.decorators.checkboxField<RowActions>({
        onChange() {
            this.table7.isReadOnly = this.table7isReadOnly.value;
        },
        parent() {
            return this.section1block7;
        },
        title: 'isReadonly',
        isTransient: true,
    })
    table7isReadOnly: ui.fields.Checkbox;

    @ui.decorators.checkboxField<RowActions>({
        onChange() {
            this.table7.canSelect = this.table7canSelect.value;
            this.table7.redraw();
        },
        parent() {
            return this.section1block7;
        },
        title: 'canSelect',
        isTransient: true,
    })
    table7canSelect: ui.fields.Checkbox;

    @ui.decorators.checkboxField<RowActions>({
        onChange() {
            this.table7.canResizeColumns = this.table7canResizeColumns.value;
            this.table7.redraw();
        },
        parent() {
            return this.section1block7;
        },
        title: 'canResizeColumns',
        isTransient: true,
    })
    table7canResizeColumns: ui.fields.Checkbox;

    @ui.decorators.block<RowActions>({
        title: 'Single inline + canAddNewLine',
        parent() {
            return this.section2;
        },
    })
    section1block8: ui.containers.Block;

    @ui.decorators.tableField<RowActions, Todo>({
        isTransient: true,
        title: '',
        canSelect: false,
        canAddNewLine: true,
        columns: [
            ui.nestedFields.text({
                bind: '_id',
                title: 'Id',
                isHiddenOnMainField: true,
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                bind: 'mandatory',
                title: 'Mandatory',
                isMandatory: true,
            }),
            ui.nestedFields.text({
                bind: 'readonly',
                title: 'Readonly',
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                bind: 'optional',
                title: 'Optional',
            }),
        ],
        orderBy: { _id: -1 },
        inlineActions: [
            {
                icon: 'share',
                title: 'Share',
                onClick(rowId: any, data: any) {
                    this.$.dialog.message(
                        'info',
                        'Add Inline Row Action was clicked',
                        `Optional: ${data.optional} Row: ${rowId}`,
                    );
                },
                buttonType: 'tertiary',
            },
        ],
        parent() {
            return this.section1block8;
        },
    })
    table8: ui.fields.Table<Todo>;

    @ui.decorators.checkboxField<RowActions>({
        onChange() {
            this.table8.isReadOnly = this.table8isReadOnly.value;
        },
        parent() {
            return this.section1block8;
        },
        title: 'isReadonly',
        isTransient: true,
    })
    table8isReadOnly: ui.fields.Checkbox;

    @ui.decorators.checkboxField<RowActions>({
        onChange() {
            this.table8.canSelect = this.table8canSelect.value;
            this.table8.redraw();
        },
        parent() {
            return this.section1block8;
        },
        title: 'canSelect',
        isTransient: true,
    })
    table8canSelect: ui.fields.Checkbox;

    @ui.decorators.checkboxField<RowActions>({
        onChange() {
            this.table8.canResizeColumns = this.table8canResizeColumns.value;
            this.table8.redraw();
        },
        parent() {
            return this.section1block8;
        },
        title: 'canResizeColumns',
        isTransient: true,
    })
    table8canResizeColumns: ui.fields.Checkbox;

    @ui.decorators.block<RowActions>({
        title: 'Single action none icon',
        parent() {
            return this.section1;
        },
    })
    section1block9: ui.containers.Block;

    @ui.decorators.tableField<RowActions, Todo>({
        isTransient: true,
        title: '',
        canSelect: false,
        // canAddNewLine: true,
        columns: [
            ui.nestedFields.text({
                bind: '_id',
                title: 'Id',
                isHiddenOnMainField: true,
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                bind: 'mandatory',
                title: 'Mandatory',
                isMandatory: true,
            }),
            ui.nestedFields.text({
                bind: 'readonly',
                title: 'Readonly',
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                bind: 'optional',
                title: 'Optional',
            }),
        ],
        orderBy: { _id: -1 },
        dropdownActions: [
            {
                icon: 'none',
                title: 'Tag',
                isDisabled() {
                    return false;
                },
                onClick(rowId: any, data: any) {
                    this.$.dialog.message(
                        'info',
                        'Add Row Action was clicked',
                        `Optional: ${data.optional} Row: ${rowId}`,
                    );
                },
            },
        ],
        parent() {
            return this.section1block9;
        },
    })
    table91: ui.fields.Table<Todo>;

    @ui.decorators.block<RowActions>({
        title: 'Single action no icon',
        parent() {
            return this.section1;
        },
    })
    section1block10: ui.containers.Block;

    @ui.decorators.tableField<RowActions, Todo>({
        isTransient: true,
        title: '',
        canSelect: false,
        // canAddNewLine: true,
        columns: [
            ui.nestedFields.text({
                bind: '_id',
                title: 'Id',
                isHiddenOnMainField: true,
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                bind: 'mandatory',
                title: 'Mandatory',
                isMandatory: true,
            }),
            ui.nestedFields.text({
                bind: 'readonly',
                title: 'Readonly',
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                bind: 'optional',
                title: 'Optional',
            }),
        ],
        orderBy: { _id: -1 },
        dropdownActions: [
            {
                title: 'Tag',
                isDisabled() {
                    return false;
                },
                onClick(rowId: any, data: any) {
                    this.$.dialog.message(
                        'info',
                        'Add Row Action was clicked',
                        `Optional: ${data.optional} Row: ${rowId}`,
                    );
                },
            },
        ],
        parent() {
            return this.section1block10;
        },
    })
    table910: ui.fields.Table<Todo>;

    @ui.decorators.block<RowActions>({
        title: 'Single action an icon',
        parent() {
            return this.section1;
        },
    })
    section1block11: ui.containers.Block;

    @ui.decorators.tableField<RowActions, Todo>({
        isTransient: true,
        title: '',
        canSelect: false,
        // canAddNewLine: true,
        columns: [
            ui.nestedFields.text({
                bind: '_id',
                title: 'Id',
                isHiddenOnMainField: true,
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                bind: 'mandatory',
                title: 'Mandatory',
                isMandatory: true,
            }),
            ui.nestedFields.text({
                bind: 'readonly',
                title: 'Readonly',
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                bind: 'optional',
                title: 'Optional',
            }),
        ],
        orderBy: { _id: -1 },
        dropdownActions: [
            {
                title: 'Tag',
                icon: 'coins',
                isDisabled() {
                    return false;
                },
                onClick(rowId: any, data: any) {
                    this.$.dialog.message(
                        'info',
                        'Add Row Action was clicked',
                        `Optional: ${data.optional} Row: ${rowId}`,
                    );
                },
            },
        ],
        parent() {
            return this.section1block11;
        },
    })
    table911: ui.fields.Table<Todo>;

    @ui.decorators.block<RowActions>({
        title: 'Multiple major inline + Single dropdown',
        parent() {
            return this.section1;
        },
    })
    section1block12: ui.containers.Block;

    @ui.decorators.tableField<RowActions, Todo>({
        isTransient: true,
        title: '',
        canSelect: false,
        columns: [
            ui.nestedFields.text({
                bind: '_id',
                title: 'Id',
                isHiddenOnMainField: true,
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                bind: 'mandatory',
                title: 'Mandatory',
                isMandatory: true,
            }),
            ui.nestedFields.text({
                bind: 'readonly',
                title: 'Readonly',
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                bind: 'optional',
                title: 'Optional',
            }),
        ],
        orderBy: { _id: -1 },
        inlineActions: [
            {
                icon: 'disconnect',
                title: 'Disconnect',

                onClick(rowId: any, data: any) {
                    this.$.dialog.message(
                        'info',
                        'Add Inline Row Action was clicked',
                        `Optional: ${data.optional} Row: ${rowId}`,
                    );
                },
                buttonType: 'tertiary',
                isMajor: true,
                isDestructive: true,
            },
            {
                icon: 'locked',
                title: 'Locked',
                onClick(rowId: any, data: any) {
                    this.$.dialog.message(
                        'info',
                        'Add Inline Row Action was clicked',
                        `Optional: ${data.optional} Row: ${rowId}`,
                    );
                },
                isDisabled(rowId, data) {
                    return !!data.optional;
                },
                isMajor: true,
                buttonType: 'tertiary',
            },
            {
                icon: 'edit',
                title: 'Edit',
                onClick(rowId: any, data: any) {
                    this.$.dialog.message(
                        'info',
                        'Add Inline Row Action was clicked',
                        `Optional: ${data.optional} Row: ${rowId}`,
                    );
                },
                isMajor: true,
                buttonType: 'tertiary',
            },
        ],
        dropdownActions: [
            {
                icon: 'copy',
                title: 'Copy',
                isDisabled() {
                    return false;
                },
                onClick(rowId: any, data: any) {
                    this.$.dialog.message(
                        'info',
                        'Add Row Action was clicked',
                        `Optional: ${data.optional} Row: ${rowId}`,
                    );
                },
            },
        ],
        parent() {
            return this.section1block12;
        },
    })
    table112: ui.fields.Table<Todo>;

    @ui.decorators.section<RowActions>({
        title: 'Unique actions',
    })
    section2: ui.containers.Section;

    @ui.decorators.block<RowActions>({
        title: 'Multiple inline',
        parent() {
            return this.section1;
        },
    })
    section2block1: ui.containers.Block;

    @ui.decorators.tableField<RowActions, Todo>({
        isTransient: true,
        title: '',
        canSelect: false,
        columns: [
            ui.nestedFields.text({
                bind: '_id',
                title: 'Id',
                isHiddenOnMainField: true,
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                bind: 'mandatory',
                title: 'Mandatory',
                isMandatory: true,
            }),
            ui.nestedFields.text({
                bind: 'readonly',
                title: 'Readonly',
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                bind: 'optional',
                title: 'Optional',
            }),
        ],
        orderBy: { _id: -1 },
        inlineActions: [
            {
                icon: 'delivery',
                title: 'Delivery',
                onClick(rowId: any, data: any) {
                    this.$.dialog.message(
                        'info',
                        'Add Inline Row Action was clicked',
                        `Optional: ${data.optional} Row: ${rowId}`,
                    );
                },
                buttonType: 'primary',
            },
            {
                icon: 'clock',
                title: 'Clock',
                onClick(rowId: any, data: any) {
                    this.$.dialog.message(
                        'info',
                        'Add Inline Row Action was clicked',
                        `Optional: ${data.optional} Row: ${rowId}`,
                    );
                },
                buttonType: 'secondary',
            },
            {
                icon: 'key',
                title: 'Key',
                onClick(rowId: any, data: any) {
                    this.$.dialog.message(
                        'info',
                        'Add Inline Row Action was clicked',
                        `Optional: ${data.optional} Row: ${rowId}`,
                    );
                },
                buttonType: 'tertiary',
            },
        ],
        parent() {
            return this.section2block1;
        },
    })
    table9: ui.fields.Table<Todo>;

    @ui.decorators.checkboxField<RowActions>({
        onChange() {
            this.table9.isReadOnly = this.table9isReadOnly.value;
        },
        parent() {
            return this.section2block1;
        },
        title: 'isReadonly',
        isTransient: true,
    })
    table9isReadOnly: ui.fields.Checkbox;

    @ui.decorators.checkboxField<RowActions>({
        onChange() {
            this.table9.canSelect = this.table9canSelect.value;
            this.table9.redraw();
        },
        parent() {
            return this.section2block1;
        },
        title: 'canSelect',
        isTransient: true,
    })
    table9canSelect: ui.fields.Checkbox;

    @ui.decorators.checkboxField<RowActions>({
        onChange() {
            this.table9.canResizeColumns = this.table9canResizeColumns.value;
            this.table9.redraw();
        },
        parent() {
            return this.section2block1;
        },
        title: 'canResizeColumns',
        isTransient: true,
    })
    table9canResizeColumns: ui.fields.Checkbox;

    @ui.decorators.block<RowActions>({
        title: 'Multiple inline + canAddNewLine',
        parent() {
            return this.section1;
        },
    })
    section2block2: ui.containers.Block;

    @ui.decorators.tableField<RowActions, Todo>({
        isTransient: true,
        title: '',
        canSelect: false,
        canAddNewLine: true,
        columns: [
            ui.nestedFields.text({
                bind: '_id',
                title: 'Id',
                isHiddenOnMainField: true,
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                bind: 'mandatory',
                title: 'Mandatory',
                isMandatory: true,
            }),
            ui.nestedFields.text({
                bind: 'readonly',
                title: 'Readonly',
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                bind: 'optional',
                title: 'Optional',
            }),
        ],
        orderBy: { _id: -1 },
        inlineActions: [
            {
                icon: 'link',
                title: 'Link',
                onClick(rowId: any, data: any) {
                    this.$.dialog.message(
                        'info',
                        'Add Inline Row Action was clicked',
                        `Optional: ${data.optional} Row: ${rowId}`,
                    );
                },
                buttonType: 'tertiary',
            },
            {
                icon: 'email',
                title: 'Email',
                onClick(rowId: any, data: any) {
                    this.$.dialog.message(
                        'info',
                        'Add Inline Row Action was clicked',
                        `Optional: ${data.optional} Row: ${rowId}`,
                    );
                },
                buttonType: 'tertiary',
            },
            {
                icon: 'chat_notes',
                title: 'Chat notes',
                onClick(rowId: any, data: any) {
                    this.$.dialog.message(
                        'info',
                        'Add Inline Row Action was clicked',
                        `Optional: ${data.optional} Row: ${rowId}`,
                    );
                },
                buttonType: 'tertiary',
            },
        ],
        parent() {
            return this.section2block2;
        },
    })
    table10: ui.fields.Table<Todo>;

    @ui.decorators.checkboxField<RowActions>({
        onChange() {
            this.table10.isReadOnly = this.table10isReadOnly.value;
        },
        parent() {
            return this.section2block2;
        },
        title: 'isReadonly',
        isTransient: true,
    })
    table10isReadOnly: ui.fields.Checkbox;

    @ui.decorators.checkboxField<RowActions>({
        onChange() {
            this.table10.canSelect = this.table10canSelect.value;
            this.table10.redraw();
        },
        parent() {
            return this.section2block2;
        },
        title: 'canSelect',
        isTransient: true,
    })
    table10canSelect: ui.fields.Checkbox;

    @ui.decorators.checkboxField<RowActions>({
        onChange() {
            this.table10.canResizeColumns = this.table10canResizeColumns.value;
            this.table10.redraw();
        },
        parent() {
            return this.section2block2;
        },
        title: 'canResizeColumns',
        isTransient: true,
    })
    table10canResizeColumns: ui.fields.Checkbox;

    @ui.decorators.block<RowActions>({
        title: 'Multiple inline + Single dropdown',
        parent() {
            return this.section1;
        },
    })
    section2block3: ui.containers.Block;

    @ui.decorators.tableField<RowActions, Todo>({
        isTransient: true,
        title: '',
        canSelect: false,
        columns: [
            ui.nestedFields.text({
                bind: '_id',
                title: 'Id',
                isHiddenOnMainField: true,
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                bind: 'mandatory',
                title: 'Mandatory',
                isMandatory: true,
            }),
            ui.nestedFields.text({
                bind: 'readonly',
                title: 'Readonly',
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                bind: 'optional',
                title: 'Optional',
            }),
        ],
        orderBy: { _id: -1 },
        inlineActions: [
            {
                icon: 'disconnect',
                title: 'Disconnect',
                onClick(rowId: any, data: any) {
                    this.$.dialog.message(
                        'info',
                        'Add Inline Row Action was clicked',
                        `Optional: ${data.optional} Row: ${rowId}`,
                    );
                },
                buttonType: 'tertiary',
                isDestructive: true,
            },
            {
                icon: 'locked',
                title: 'Locked',
                onClick(rowId: any, data: any) {
                    this.$.dialog.message(
                        'info',
                        'Add Inline Row Action was clicked',
                        `Optional: ${data.optional} Row: ${rowId}`,
                    );
                },
                isDisabled(rowId, data) {
                    return !!data.optional;
                },
                buttonType: 'tertiary',
            },
            {
                icon: 'edit',
                title: 'Edit',
                onClick(rowId: any, data: any) {
                    this.$.dialog.message(
                        'info',
                        'Add Inline Row Action was clicked',
                        `Optional: ${data.optional} Row: ${rowId}`,
                    );
                },

                buttonType: 'tertiary',
            },
        ],
        dropdownActions: [
            {
                icon: 'copy',
                title: 'Copy',
                isDisabled() {
                    return false;
                },
                onClick(rowId: any, data: any) {
                    this.$.dialog.message(
                        'info',
                        'Add Row Action was clicked',
                        `Optional: ${data.optional} Row: ${rowId}`,
                    );
                },
            },
        ],
        parent() {
            return this.section2block3;
        },
    })
    table11: ui.fields.Table<Todo>;

    @ui.decorators.checkboxField<RowActions>({
        onChange() {
            this.table11.isReadOnly = this.table11isReadOnly.value;
        },
        parent() {
            return this.section2block3;
        },
        title: 'isReadonly',
        isTransient: true,
    })
    table11isReadOnly: ui.fields.Checkbox;

    @ui.decorators.checkboxField<RowActions>({
        onChange() {
            this.table11.canSelect = this.table11canSelect.value;
            this.table11.redraw();
        },
        parent() {
            return this.section2block3;
        },
        title: 'canSelect',
        isTransient: true,
    })
    table11canSelect: ui.fields.Checkbox;

    @ui.decorators.checkboxField<RowActions>({
        onChange() {
            this.table11.canResizeColumns = this.table11canResizeColumns.value;
            this.table11.redraw();
        },
        parent() {
            return this.section2block3;
        },
        title: 'canResizeColumns',
        isTransient: true,
    })
    table11canResizeColumns: ui.fields.Checkbox;

    @ui.decorators.block<RowActions>({
        title: 'Multiple inline + Single dropdown + canAddNewLine',
        parent() {
            return this.section1;
        },
    })
    section2block4: ui.containers.Block;

    @ui.decorators.tableField<RowActions, Todo>({
        isTransient: true,
        title: '',
        canSelect: false,
        canAddNewLine: true,
        columns: [
            ui.nestedFields.text({
                bind: '_id',
                title: 'Id',
                isHiddenOnMainField: true,
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                bind: 'mandatory',
                title: 'Mandatory',
                isMandatory: true,
            }),
            ui.nestedFields.text({
                bind: 'readonly',
                title: 'Readonly',
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                bind: 'optional',
                title: 'Optional',
            }),
        ],
        orderBy: { _id: -1 },
        inlineActions: [
            {
                icon: 'add',
                title: 'Add',
                onClick(rowId: any, data: any) {
                    this.$.dialog.message(
                        'info',
                        'Add Inline Row Action was clicked',
                        `Optional: ${data.optional} Row: ${rowId}`,
                    );
                },
                buttonType: 'tertiary',
            },
            {
                icon: 'add',
                title: 'Add',
                onClick(rowId: any, data: any) {
                    this.$.dialog.message(
                        'info',
                        'Add Inline Row Action was clicked',
                        `Optional: ${data.optional} Row: ${rowId}`,
                    );
                },
                buttonType: 'tertiary',
            },
            {
                icon: 'add',
                title: 'Add',
                onClick(rowId: any, data: any) {
                    this.$.dialog.message(
                        'info',
                        'Add Inline Row Action was clicked',
                        `Optional: ${data.optional} Row: ${rowId}`,
                    );
                },
                isHidden(rowId, data) {
                    return !!data.optional;
                },
                buttonType: 'tertiary',
            },
        ],
        dropdownActions: [
            {
                icon: 'add',
                title: 'Add',
                isDisabled() {
                    return false;
                },
                onClick(rowId: any, data: any) {
                    this.$.dialog.message(
                        'info',
                        'Add Row Action was clicked',
                        `Optional: ${data.optional} Row: ${rowId}`,
                    );
                },
            },
        ],
        parent() {
            return this.section2block4;
        },
    })
    table12: ui.fields.Table<Todo>;

    @ui.decorators.checkboxField<RowActions>({
        onChange() {
            this.table12.isReadOnly = this.table12isReadOnly.value;
        },
        parent() {
            return this.section2block4;
        },
        title: 'isReadonly',
        isTransient: true,
    })
    table12isReadOnly: ui.fields.Checkbox;

    @ui.decorators.checkboxField<RowActions>({
        onChange() {
            this.table12.canSelect = this.table12canSelect.value;
            this.table12.redraw();
        },
        parent() {
            return this.section2block4;
        },
        title: 'canSelect',
        isTransient: true,
    })
    table12canSelect: ui.fields.Checkbox;

    @ui.decorators.checkboxField<RowActions>({
        onChange() {
            this.table12.canResizeColumns = this.table12canResizeColumns.value;
            this.table12.redraw();
        },
        parent() {
            return this.section2block4;
        },
        title: 'canResizeColumns',
        isTransient: true,
    })
    table12canResizeColumns: ui.fields.Checkbox;

    @ui.decorators.block<RowActions>({
        title: 'Single inline + Single dropdown',
        parent() {
            return this.section1;
        },
    })
    section2block5: ui.containers.Block;

    @ui.decorators.tableField<RowActions, Todo>({
        isTransient: true,
        title: '',
        canSelect: false,
        columns: [
            ui.nestedFields.text({
                bind: '_id',
                title: 'Id',
                isHiddenOnMainField: true,
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                bind: 'mandatory',
                title: 'Mandatory',
                isMandatory: true,
            }),
            ui.nestedFields.text({
                bind: 'readonly',
                title: 'Readonly',
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                bind: 'optional',
                title: 'Optional',
            }),
        ],
        orderBy: { _id: -1 },
        inlineActions: [
            {
                icon: 'add',
                title: 'Add',
                onClick(rowId: any, data: any) {
                    this.$.dialog.message(
                        'info',
                        'Add Inline Row Action was clicked',
                        `Optional: ${data.optional} Row: ${rowId}`,
                    );
                },
                buttonType: 'tertiary',
            },
        ],
        dropdownActions: [
            {
                icon: 'add',
                title: 'Add',
                isDisabled() {
                    return false;
                },
                onClick(rowId: any, data: any) {
                    this.$.dialog.message(
                        'info',
                        'Add Row Action was clicked',
                        `Optional: ${data.optional} Row: ${rowId}`,
                    );
                },
            },
        ],
        parent() {
            return this.section2block5;
        },
    })
    table13: ui.fields.Table<Todo>;

    @ui.decorators.checkboxField<RowActions>({
        onChange() {
            this.table13.isReadOnly = this.table13isReadOnly.value;
        },
        parent() {
            return this.section2block5;
        },
        title: 'isReadonly',
        isTransient: true,
    })
    table13isReadOnly: ui.fields.Checkbox;

    @ui.decorators.checkboxField<RowActions>({
        onChange() {
            this.table13.canSelect = this.table13canSelect.value;
            this.table13.redraw();
        },
        parent() {
            return this.section2block5;
        },
        title: 'canSelect',
        isTransient: true,
    })
    table13canSelect: ui.fields.Checkbox;

    @ui.decorators.checkboxField<RowActions>({
        onChange() {
            this.table13.canResizeColumns = this.table13canResizeColumns.value;
            this.table13.redraw();
        },
        parent() {
            return this.section2block5;
        },
        title: 'canResizeColumns',
        isTransient: true,
    })
    table13canResizeColumns: ui.fields.Checkbox;

    @ui.decorators.block<RowActions>({
        title: 'Single inline + Single dropdown + canAddNewLine',
        parent() {
            return this.section1;
        },
    })
    section2block6: ui.containers.Block;

    @ui.decorators.tableField<RowActions, Todo>({
        isTransient: true,
        title: '',
        canSelect: false,
        canAddNewLine: true,
        columns: [
            ui.nestedFields.text({
                bind: '_id',
                title: 'Id',
                isHiddenOnMainField: true,
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                bind: 'mandatory',
                title: 'Mandatory',
                isMandatory: true,
            }),
            ui.nestedFields.text({
                bind: 'readonly',
                title: 'Readonly',
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                bind: 'optional',
                title: 'Optional',
            }),
        ],
        orderBy: { _id: -1 },
        inlineActions: [
            {
                icon: 'add',
                title: 'Add',
                onClick(rowId: any, data: any) {
                    this.$.dialog.message(
                        'info',
                        'Add Inline Row Action was clicked',
                        `Optional: ${data.optional} Row: ${rowId}`,
                    );
                },
                buttonType: 'tertiary',
            },
        ],
        dropdownActions: [
            {
                icon: 'add',
                title: 'Add',
                isDisabled() {
                    return false;
                },
                onClick(rowId: any, data: any) {
                    this.$.dialog.message(
                        'info',
                        'Add Row Action was clicked',
                        `Optional: ${data.optional} Row: ${rowId}`,
                    );
                },
            },
        ],
        parent() {
            return this.section2block6;
        },
    })
    table14: ui.fields.Table<Todo>;

    @ui.decorators.checkboxField<RowActions>({
        onChange() {
            this.table14.isReadOnly = this.table14isReadOnly.value;
        },
        parent() {
            return this.section2block6;
        },
        title: 'isReadonly',
        isTransient: true,
    })
    table14isReadOnly: ui.fields.Checkbox;

    @ui.decorators.checkboxField<RowActions>({
        onChange() {
            this.table14.canSelect = this.table14canSelect.value;
            this.table14.redraw();
        },
        parent() {
            return this.section2block6;
        },
        title: 'canSelect',
        isTransient: true,
    })
    table14canSelect: ui.fields.Checkbox;

    @ui.decorators.checkboxField<RowActions>({
        onChange() {
            this.table14.canResizeColumns = this.table14canResizeColumns.value;
            this.table14.redraw();
        },
        parent() {
            return this.section2block6;
        },
        title: 'canResizeColumns',
        isTransient: true,
    })
    table14canResizeColumns: ui.fields.Checkbox;

    @ui.decorators.block<RowActions>({
        title: 'Multiple inline + Multiple dropdown',
        parent() {
            return this.section1;
        },
    })
    section2block7: ui.containers.Block;

    @ui.decorators.tableField<RowActions, Todo>({
        isTransient: true,
        title: '',
        canSelect: false,
        columns: [
            ui.nestedFields.text({
                bind: '_id',
                title: 'Id',
                isHiddenOnMainField: true,
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                bind: 'mandatory',
                title: 'Mandatory',
                isMandatory: true,
            }),
            ui.nestedFields.text({
                bind: 'readonly',
                title: 'Readonly',
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                bind: 'optional',
                title: 'Optional',
            }),
        ],
        orderBy: { _id: -1 },
        inlineActions: [
            {
                icon: 'add',
                title: 'Add',
                onClick(rowId: any, data: any) {
                    this.$.dialog.message(
                        'info',
                        'Add Inline Row Action was clicked',
                        `Optional: ${data.optional} Row: ${rowId}`,
                    );
                },
                buttonType: 'tertiary',
            },
            {
                icon: 'add',
                title: 'Add',
                onClick(rowId: any, data: any) {
                    this.$.dialog.message(
                        'info',
                        'Add Inline Row Action was clicked',
                        `Optional: ${data.optional} Row: ${rowId}`,
                    );
                },
                buttonType: 'tertiary',
            },
            {
                icon: 'add',
                title: 'Add',
                onClick(rowId: any, data: any) {
                    this.$.dialog.message(
                        'info',
                        'Add Inline Row Action was clicked',
                        `Optional: ${data.optional} Row: ${rowId}`,
                    );
                },
                buttonType: 'tertiary',
            },
        ],
        dropdownActions: [
            {
                icon: 'add',
                title: 'Add',
                isDisabled() {
                    return false;
                },
                onClick(rowId: any, data: any) {
                    this.$.dialog.message(
                        'info',
                        'Add Row Action was clicked',
                        `Optional: ${data.optional} Row: ${rowId}`,
                    );
                },
            },
            {
                icon: 'add',
                title: 'Add',
                isDisabled() {
                    return false;
                },
                onClick(rowId: any, data: any) {
                    this.$.dialog.message(
                        'info',
                        'Add Row Action was clicked',
                        `Optional: ${data.optional} Row: ${rowId}`,
                    );
                },
            },
        ],
        parent() {
            return this.section2block7;
        },
    })
    table15: ui.fields.Table<Todo>;

    @ui.decorators.checkboxField<RowActions>({
        onChange() {
            this.table15.isReadOnly = this.table15isReadOnly.value;
        },
        parent() {
            return this.section2block7;
        },
        title: 'isReadonly',
        isTransient: true,
    })
    table15isReadOnly: ui.fields.Checkbox;

    @ui.decorators.checkboxField<RowActions>({
        onChange() {
            this.table15.canSelect = this.table15canSelect.value;
            this.table15.redraw();
        },
        parent() {
            return this.section2block7;
        },
        title: 'canSelect',
        isTransient: true,
    })
    table15canSelect: ui.fields.Checkbox;

    @ui.decorators.checkboxField<RowActions>({
        onChange() {
            this.table15.canResizeColumns = this.table15canResizeColumns.value;
            this.table15.redraw();
        },
        parent() {
            return this.section2block7;
        },
        title: 'canResizeColumns',
        isTransient: true,
    })
    table15canResizeColumns: ui.fields.Checkbox;

    @ui.decorators.block<RowActions>({
        title: 'Multiple inline + Multiple dropdown + canAddNewLine',
        parent() {
            return this.section1;
        },
    })
    section2block8: ui.containers.Block;

    @ui.decorators.tableField<RowActions, Todo>({
        isTransient: true,
        title: '',
        canSelect: false,
        canAddNewLine: true,
        columns: [
            ui.nestedFields.text({
                bind: '_id',
                title: 'Id',
                isHiddenOnMainField: true,
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                bind: 'mandatory',
                title: 'Mandatory',
                isMandatory: true,
            }),
            ui.nestedFields.text({
                bind: 'readonly',
                title: 'Readonly',
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                bind: 'optional',
                title: 'Optional',
            }),
        ],
        orderBy: { _id: -1 },
        inlineActions: [
            {
                icon: 'add',
                title: 'Add',
                onClick(rowId: any, data: any) {
                    this.$.dialog.message(
                        'info',
                        'Add Inline Row Action was clicked',
                        `Optional: ${data.optional} Row: ${rowId}`,
                    );
                },
                buttonType: 'tertiary',
            },
            {
                icon: 'add',
                title: 'Add',
                onClick(rowId: any, data: any) {
                    this.$.dialog.message(
                        'info',
                        'Add Inline Row Action was clicked',
                        `Optional: ${data.optional} Row: ${rowId}`,
                    );
                },
                buttonType: 'tertiary',
            },
            {
                icon: 'add',
                title: 'Add',
                onClick(rowId: any, data: any) {
                    this.$.dialog.message(
                        'info',
                        'Add Inline Row Action was clicked',
                        `Optional: ${data.optional} Row: ${rowId}`,
                    );
                },
                buttonType: 'tertiary',
            },
        ],
        dropdownActions: [
            {
                icon: 'add',
                title: 'Add',
                isDisabled() {
                    return false;
                },
                onClick(rowId: any, data: any) {
                    this.$.dialog.message(
                        'info',
                        'Add Row Action was clicked',
                        `Optional: ${data.optional} Row: ${rowId}`,
                    );
                },
            },
            {
                icon: 'add',
                title: 'Add',
                isDisabled() {
                    return false;
                },
                onClick(rowId: any, data: any) {
                    this.$.dialog.message(
                        'info',
                        'Add Row Action was clicked',
                        `Optional: ${data.optional} Row: ${rowId}`,
                    );
                },
            },
        ],
        parent() {
            return this.section2block8;
        },
    })
    table16: ui.fields.Table<Todo>;

    @ui.decorators.checkboxField<RowActions>({
        onChange() {
            this.table16.isReadOnly = this.table16isReadOnly.value;
        },
        parent() {
            return this.section2block8;
        },
        title: 'isReadonly',
        isTransient: true,
    })
    table16isReadOnly: ui.fields.Checkbox;

    @ui.decorators.checkboxField<RowActions>({
        onChange() {
            this.table16.canSelect = this.table16canSelect.value;
            this.table16.redraw();
        },
        parent() {
            return this.section2block8;
        },
        title: 'canSelect',
        isTransient: true,
    })
    table16canSelect: ui.fields.Checkbox;

    @ui.decorators.checkboxField<RowActions>({
        onChange() {
            this.table16.canResizeColumns = this.table16canResizeColumns.value;
            this.table16.redraw();
        },
        parent() {
            return this.section2block8;
        },
        title: 'canResizeColumns',
        isTransient: true,
    })
    table16canResizeColumns: ui.fields.Checkbox;

    @ui.decorators.block<RowActions>({
        title: 'Single inline + Multiple dropdown',
        parent() {
            return this.section1;
        },
    })
    section2block9: ui.containers.Block;

    @ui.decorators.tableField<RowActions, Todo>({
        isTransient: true,
        title: '',
        canSelect: false,
        columns: [
            ui.nestedFields.text({
                bind: '_id',
                title: 'Id',
                isHiddenOnMainField: true,
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                bind: 'mandatory',
                title: 'Mandatory',
                isMandatory: true,
            }),
            ui.nestedFields.text({
                bind: 'readonly',
                title: 'Readonly',
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                bind: 'optional',
                title: 'Optional',
            }),
        ],
        orderBy: { _id: -1 },
        inlineActions: [
            {
                icon: 'add',
                title: 'Add',
                onClick(rowId: any, data: any) {
                    this.$.dialog.message(
                        'info',
                        'Add Inline Row Action was clicked',
                        `Optional: ${data.optional} Row: ${rowId}`,
                    );
                },
                buttonType: 'tertiary',
            },
        ],
        dropdownActions: [
            {
                icon: 'add',
                title: 'Add',
                isDisabled() {
                    return false;
                },
                onClick(rowId: any, data: any) {
                    this.$.dialog.message(
                        'info',
                        'Add Row Action was clicked',
                        `Optional: ${data.optional} Row: ${rowId}`,
                    );
                },
            },
            {
                icon: 'add',
                title: 'Add',
                isDisabled() {
                    return false;
                },
                onClick(rowId: any, data: any) {
                    this.$.dialog.message(
                        'info',
                        'Add Row Action was clicked',
                        `Optional: ${data.optional} Row: ${rowId}`,
                    );
                },
            },
        ],
        parent() {
            return this.section2block9;
        },
    })
    table17: ui.fields.Table<Todo>;

    @ui.decorators.checkboxField<RowActions>({
        onChange() {
            this.table17.isReadOnly = this.table17isReadOnly.value;
        },
        parent() {
            return this.section2block9;
        },
        title: 'isReadonly',
        isTransient: true,
    })
    table17isReadOnly: ui.fields.Checkbox;

    @ui.decorators.checkboxField<RowActions>({
        onChange() {
            this.table17.canSelect = this.table17canSelect.value;
            this.table17.redraw();
        },
        parent() {
            return this.section2block9;
        },
        title: 'canSelect',
        isTransient: true,
    })
    table17canSelect: ui.fields.Checkbox;

    @ui.decorators.checkboxField<RowActions>({
        onChange() {
            this.table17.canResizeColumns = this.table17canResizeColumns.value;
            this.table17.redraw();
        },
        parent() {
            return this.section2block9;
        },
        title: 'canResizeColumns',
        isTransient: true,
    })
    table17canResizeColumns: ui.fields.Checkbox;

    @ui.decorators.block<RowActions>({
        title: 'Single inline + Multiple dropdown + canAddNewLine',
        parent() {
            return this.section1;
        },
    })
    section2block10: ui.containers.Block;

    @ui.decorators.tableField<RowActions, Todo>({
        isTransient: true,
        title: '',
        canSelect: false,
        canAddNewLine: true,
        columns: [
            ui.nestedFields.text({
                bind: '_id',
                title: 'Id',
                isHiddenOnMainField: true,
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                bind: 'mandatory',
                title: 'Mandatory',
                isMandatory: true,
            }),
            ui.nestedFields.text({
                bind: 'readonly',
                title: 'Readonly',
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                bind: 'optional',
                title: 'Optional',
            }),
        ],
        orderBy: { _id: -1 },
        inlineActions: [
            {
                icon: 'add',
                title: 'Add',
                onClick(rowId: any, data: any) {
                    this.$.dialog.message(
                        'info',
                        'Add Inline Row Action was clicked',
                        `Optional: ${data.optional} Row: ${rowId}`,
                    );
                },
                buttonType: 'tertiary',
            },
        ],
        dropdownActions: [
            {
                icon: 'add',
                title: 'Add',
                isDisabled() {
                    return false;
                },
                onClick(rowId: any, data: any) {
                    this.$.dialog.message(
                        'info',
                        'Add Row Action was clicked',
                        `Optional: ${data.optional} Row: ${rowId}`,
                    );
                },
            },
            {
                icon: 'add',
                title: 'Add',
                isDisabled() {
                    return false;
                },
                onClick(rowId: any, data: any) {
                    this.$.dialog.message(
                        'info',
                        'Add Row Action was clicked',
                        `Optional: ${data.optional} Row: ${rowId}`,
                    );
                },
            },
        ],
        parent() {
            return this.section2block10;
        },
    })
    table18: ui.fields.Table<Todo>;

    @ui.decorators.checkboxField<RowActions>({
        onChange() {
            this.table18.isReadOnly = this.table18isReadOnly.value;
        },
        parent() {
            return this.section2block10;
        },
        title: 'isReadonly',
        isTransient: true,
    })
    table18isReadOnly: ui.fields.Checkbox;

    @ui.decorators.checkboxField<RowActions>({
        onChange() {
            this.table18.canSelect = this.table18canSelect.value;
            this.table18.redraw();
        },
        parent() {
            return this.section2block10;
        },
        title: 'canSelect',
        isTransient: true,
    })
    table18canSelect: ui.fields.Checkbox;

    @ui.decorators.checkboxField<RowActions>({
        onChange() {
            this.table18.canResizeColumns = this.table18canResizeColumns.value;
            this.table18.redraw();
        },
        parent() {
            return this.section2block10;
        },
        title: 'canResizeColumns',
        isTransient: true,
    })
    table18canResizeColumns: ui.fields.Checkbox;

    @ui.decorators.block<RowActions>({
        title: 'Access controlled actions',
        parent() {
            return this.section1;
        },
    })
    section2block11: ui.containers.Block;

    @ui.decorators.tableField<RowActions, Todo>({
        isTransient: true,
        title: 'Access controlled actions',
        canSelect: false,
        canAddNewLine: true,
        columns: [
            ui.nestedFields.text({
                bind: '_id',
                title: 'Id',
                isHiddenOnMainField: true,
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                bind: 'mandatory',
                title: 'Mandatory',
                isMandatory: true,
            }),
            ui.nestedFields.text({
                bind: 'readonly',
                title: 'Readonly',
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                bind: 'optional',
                title: 'Optional',
            }),
        ],
        orderBy: { _id: -1 },
        inlineActions: [
            {
                icon: 'add',
                title: 'Add',
                onClick(rowId: any, data: any) {
                    this.$.dialog.message(
                        'info',
                        'Add Inline Row Action was clicked',
                        `Optional: ${data.optional} Row: ${rowId}`,
                    );
                },
                buttonType: 'tertiary',
            },
            {
                icon: 'locked',
                title: 'Access controlled',
                access: {
                    node: '@sage/xtrem-show-case/ShowCaseProduct',
                    bind: 'discount',
                },
                onClick() {
                    this.$.dialog.message('info', 'Access controlled action', `This is action is now available!`);
                },
            },
        ],
        dropdownActions: [
            {
                icon: 'add',
                title: 'Add',
                isDisabled() {
                    return false;
                },
                onClick(rowId: any, data: any) {
                    this.$.dialog.message(
                        'info',
                        'Add Row Action was clicked',
                        `Optional: ${data.optional} Row: ${rowId}`,
                    );
                },
            },
            {
                icon: 'add',
                title: 'Add',
                isDisabled() {
                    return false;
                },
                onClick(rowId: any, data: any) {
                    this.$.dialog.message(
                        'info',
                        'Add Row Action was clicked',
                        `Optional: ${data.optional} Row: ${rowId}`,
                    );
                },
            },
            {
                icon: 'locked',
                title: 'Access controlled',
                access: {
                    node: '@sage/xtrem-show-case/ShowCaseProduct',
                    bind: 'discount',
                },
                onClick() {
                    this.$.dialog.message('info', 'Access controlled action', `This is action is now available!`);
                },
            },
        ],
        parent() {
            return this.section2block11;
        },
    })
    table19: ui.fields.Table<Todo>;
}
