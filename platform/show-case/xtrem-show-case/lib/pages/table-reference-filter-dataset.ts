import { ShowCaseProvider, ShowCaseProduct } from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import { tableField } from '../menu-items/table-field';

@ui.decorators.page<TableReferenceFilterDataset, ShowCaseProduct>({
    authorizationCode: 'BSCFLDS',
    mode: 'tabs',
    node: '@sage/xtrem-show-case/ShowCaseProduct',
    module: 'show-case',
    category: 'SHOWCASE',
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.text({ bind: 'product' }),
            titleLeft: ui.nestedFields.reference({
                bind: 'provider',
                title: 'Provider',
                valueField: 'textField',
                node: '@sage/xtrem-show-case/ShowCaseProvider',
                isFilterLimitedToDataset: true,
            }),
        },
    },
    createAction() {
        return this.$standardNewAction;
    },
    title: 'Table reference reference filter dataset',
    businessActions() {
        return [this.$standardCancelAction, this.$standardSaveAction, this.$standardDeleteAction];
    },

    menuItem: tableField,
    async onLoad() {
        const result = await this.$.graph
            .node('@sage/xtrem-show-case/ShowCaseProduct')
            .query(
                ui.queryUtils.edgesSelector(
                    {
                        _id: true,
                        product: true,
                        provider: {
                            _id: true,
                            textField: true,
                        },
                    },
                    {
                        filter: {
                            provider: this.provider?.value?._id === '2' ? 1 : this.provider?.value?._id || 1,
                        },
                    },
                ),
            )
            .execute();
        const result2 = await this.$.graph
            .node('@sage/xtrem-show-case/ShowCaseProduct')
            .query(
                ui.queryUtils.edgesSelector(
                    {
                        _id: true,
                        product: true,
                        provider: {
                            _id: true,
                            textField: true,
                        },
                    },
                    { filter: { provider: 2 } },
                ),
            )
            .execute();
        this.field.value = [...result.edges.map((e: any) => e.node), ...result2.edges.map((e: any) => e.node)];
    },
})
export class TableReferenceFilterDataset extends ui.Page {
    @ui.decorators.section<TableReferenceFilterDataset>({
        title: 'Table field',
    })
    section: ui.containers.Section;

    @ui.decorators.block<TableReferenceFilterDataset>({
        parent() {
            return this.section;
        },
        title: 'Field example',
    })
    fieldBlock: ui.containers.Block;

    @ui.decorators.textField<TableReferenceFilterDataset>({
        parent() {
            return this.fieldBlock;
        },
        title: 'Product',
    })
    product: ui.fields.Text;

    @ui.decorators.referenceField<TableReferenceFilterDataset, ShowCaseProvider>({
        parent() {
            return this.fieldBlock;
        },
        columns: [ui.nestedFields.text({ bind: 'textField' })],
        node: '@sage/xtrem-show-case/ShowCaseProvider',
        title: 'Provider',
        valueField: 'textField',
        helperTextField: '_id',
        minLookupCharacters: 0,
    })
    provider: ui.fields.Reference;

    @ui.decorators.tableField<TableReferenceFilterDataset, ShowCaseProduct>({
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        bind: 'products',
        isTransient: true,
        title: 'Transient Table with no relation with the product selected',
        columns: [
            ui.nestedFields.text({
                bind: '_id',
                title: 'Id',
                isHiddenOnMainField: true,
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                bind: 'product',
                title: 'Product',
                isMandatory: true,
            }),
            ui.nestedFields.reference<TableReferenceFilterDataset, ShowCaseProduct, ShowCaseProvider>({
                isFilterLimitedToDataset: true,
                bind: 'provider',
                title: 'Provider',
                valueField: 'textField',
                node: '@sage/xtrem-show-case/ShowCaseProvider',
                isMandatory: true,
                minLookupCharacters: 0,
                columns: [
                    ui.nestedFields.text({ bind: '_id', title: 'ID', canFilter: false }),
                    ui.nestedFields.text({ bind: 'textField', title: 'Provider', canFilter: true }),
                    ui.nestedFields.image({ bind: 'logo', title: 'Logo', canFilter: false }),
                ],
                imageField: 'logo',
            }),
        ],
        orderBy: {
            product: 1,
        },
        parent() {
            return this.fieldBlock;
        },
    })
    field: ui.fields.Table<ShowCaseProduct>;
}
