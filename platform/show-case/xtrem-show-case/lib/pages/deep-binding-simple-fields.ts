import { GraphApi, ShowCaseProduct } from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import { misc } from '../menu-items/misc';

@ui.decorators.page<DeepBindingSimpleFields, ShowCaseProduct>({
    authorizationCode: 'SHCPRVD',
    category: 'SHOWCASE',
    menuItem: misc,
    createAction() {
        return this.$standardNewAction;
    },
    headerDropDownActions() {
        return [this.$standardDeleteAction];
    },
    businessActions() {
        return [this.$standardCancelAction, this.$standardSaveAction];
    },
    module: 'show-case',
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.text<DeepBindingSimpleFields, ShowCaseProduct>({
                title: 'Product',
                bind: 'product',
            }),
            line2: ui.nestedFields.text<DeepBindingSimpleFields, ShowCaseProduct>({
                title: 'Origin country',
                bind: { originAddress: { country: { code: true } } },
            }),
            line3: ui.nestedFields.text<DeepBindingSimpleFields, ShowCaseProduct>({
                title: 'Origin address',
                bind: { originAddress: { addressLine1: true } },
            }),
        },
    },
    node: '@sage/xtrem-show-case/ShowCaseProduct',
    title: 'Simple fields',
})
export class DeepBindingSimpleFields extends ui.Page<GraphApi, ShowCaseProduct> {
    @ui.decorators.section<DeepBindingSimpleFields>({})
    mainSection: ui.containers.Section;

    @ui.decorators.block<DeepBindingSimpleFields>({
        parent() {
            return this.mainSection;
        },
    })
    block: ui.containers.Block;

    @ui.decorators.textField<DeepBindingSimpleFields>({
        parent() {
            return this.block;
        },
        title: 'Id',
        width: 'medium',
    })
    _id: ui.fields.Text;

    @ui.decorators.textField<DeepBindingSimpleFields>({
        parent() {
            return this.block;
        },
        title: 'Name',
        isMandatory: true,
        bind: 'product',
    })
    name: ui.fields.Text;

    @ui.decorators.textField<DeepBindingSimpleFields>({
        parent() {
            return this.block;
        },
        title: 'Description',
    })
    description: ui.fields.Text;

    @ui.decorators.checkboxField<DeepBindingSimpleFields>({
        parent() {
            return this.block;
        },
        title: 'Hot product',
    })
    hotProduct: ui.fields.Checkbox;

    @ui.decorators.textField<DeepBindingSimpleFields>({
        parent() {
            return this.block;
        },
        title: 'Address name',
        bind: {
            originAddress: {
                name: true,
            },
        },
    })
    addressName: ui.fields.Text;

    @ui.decorators.textField<DeepBindingSimpleFields>({
        parent() {
            return this.block;
        },
        title: 'Address line 1',
        bind: {
            originAddress: {
                addressLine1: true,
            },
        },
    })
    addressLine1: ui.fields.Text;

    @ui.decorators.textField<DeepBindingSimpleFields>({
        parent() {
            return this.block;
        },
        title: 'Address line 2',
        bind: {
            originAddress: {
                addressLine2: true,
            },
        },
    })
    addressLine2: ui.fields.Text;
}
