import * as ui from '@sage/xtrem-ui';
import { fields } from '../menu-items/fields';
import { MonacoPluginProperties } from '@sage/xtrem-ui-plugin-monaco';

@ui.decorators.page<FormDesigner>({
    menuItem: fields,
    title: 'Form Designer',
    subtitle: 'Fields',
    isTransient: true,
})
export class FormDesigner extends ui.Page {
    @ui.decorators.section<FormDesigner>({
        title: 'Form Designer field',
        isTitleHidden: true,
    })
    section: ui.containers.Section;

    @ui.decorators.block<FormDesigner>({
        parent() {
            return this.section;
        },
        title: 'Field example',
    })
    fieldBlock: ui.containers.Block;

    @ui.decorators.formDesignerField<FormDesigner>({
        parent() {
            return this.fieldBlock;
        },
    })
    field: ui.fields.FormDesigner;

    @ui.decorators.block<FormDesigner>({
        parent() {
            return this.section;
        },
        title: 'Configuration',
    })
    configurationBlock: ui.containers.Block;

    @ui.decorators.checkboxField<FormDesigner>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is disabled',
        helperText:
            'Determines whether the field is disabled or not. It can also be defined as callback function that returns a boolean.',
        isFullWidth: true,
        isReversed: true,
        onChange() {
            this.field.isDisabled = !!this.isDisabled.value;
        },
    })
    isDisabled: ui.fields.Checkbox;

    @ui.decorators.checkboxField<FormDesigner>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is hidden',
        helperText: 'Determines whether the field is displayed or not.',
        isFullWidth: true,
        isReversed: true,
        onChange() {
            this.field.isHidden = !!this.isHidden.value;
        },
    })
    isHidden: ui.fields.Checkbox;

    @ui.decorators.textField<FormDesigner>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Title',
        isFullWidth: true,
        helperText:
            'The title that is displayed above the field. The title can be provided as a string, or a callback function returning a string. When declared as a callback within the column of a nested grid, the column id is provided as a parameter. It is automatically picked up by the i18n engine and externalized for translation.',
        onChange() {
            this.field.title = this.title.value || '';
        },
    })
    title: ui.fields.Text;

    @ui.decorators.checkboxField<FormDesigner>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is title hidden',
        isFullWidth: true,
        helperText: 'Whether the field title above the field should be displayed and its vertical space preserved.',
        isReversed: true,
        onChange() {
            this.field.isTitleHidden = !!this.isTitleHidden.value;
        },
    })
    isTitleHidden: ui.fields.Checkbox;

    @ui.decorators.textField<FormDesigner>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Helper text',
        helperText:
            'The helper text that is displayed above the field. It is automatically picked up by the i18n engine and externalized.',
        isFullWidth: true,
        onChange() {
            this.field.helperText = this.helperText.value || '';
        },
    })
    helperText: ui.fields.Text;

    @ui.decorators.checkboxField<FormDesigner>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is helper text hidden',
        helperText:
            'Whether the helper text underneath the field should be displayed and its vertical space preserved.',
        isFullWidth: true,
        isReversed: true,
        onChange() {
            this.field.isHelperTextHidden = !!this.isHelperTextHidden.value;
        },
    })
    isHelperTextHidden: ui.fields.Checkbox;

    @ui.decorators.block<FormDesigner>({
        parent() {
            return this.section;
        },
        title: 'Command execution',
    })
    editorCommandBlock: ui.containers.Block;

    @ui.decorators.staticContentField<FormDesigner>({
        parent() {
            return this.editorCommandBlock;
        },
        isTitleHidden: true,
        isFullWidth: true,
        isMarkdown: true,
        content:
            'The application code can execute CK Editor commands using via the form designer field. It can be practical for inserting new objects, or for modifying formatting of the selected text from the application code layer.\nIn this example you can execute a command (e.g "bold"). The arguments can be provided as a JSON array in the code editor field below',
    })
    description: ui.fields.StaticContent;

    @ui.decorators.textField<FormDesigner>({
        parent() {
            return this.editorCommandBlock;
        },
        title: 'Editor command',
        helperText: 'Name of the command to be executed',
        isFullWidth: true,
    })
    commandName: ui.fields.Text;

    @ui.decorators.pluginField<FormDesigner, MonacoPluginProperties>({
        parent() {
            return this.editorCommandBlock;
        },
        title: 'Command arguments',
        helperText: 'They must be defined as a JSON array',
        isFullWidth: true,
        pluginPackage: '@sage/xtrem-ui-plugin-monaco',
        language: 'json',
        height: 200,
    })
    args: ui.fields.Plugin<MonacoPluginProperties>;

    @ui.decorators.buttonField<FormDesigner>({
        title: 'Execute editor command',
        isTitleHidden: true,
        map() {
            return ui.localize('@sage/xtrem-show-case/execute-editor-command', 'Execute editor command');
        },
        parent() {
            return this.editorCommandBlock;
        },
        onClick() {
            const args = this.args.value ? JSON.parse(this.args.value) || [] : [];
            if (!(args instanceof Array)) {
                throw new Error('The arguments must be defined as an array');
            }
            if (!this.commandName.value) {
                throw new Error('You must set a command name.');
            }

            this.field.executeEditorCommand(this.commandName.value, ...args);
        },
    })
    executeEditorCommand: ui.fields.Button;
}
