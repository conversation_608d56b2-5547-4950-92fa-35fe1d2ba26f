import { ShowCaseProvider } from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import { misc } from '../menu-items/misc';

@ui.decorators.page<Sounds, ShowCaseProvider>({
    authorizationCode: 'BSCFLDS',
    defaultEntry: () => '2',
    module: 'show-case',
    category: 'SHOWCASE',
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.text({ bind: 'textField' }),
            titleRight: ui.nestedFields.text({ bind: '_id' }),
        },
        isAutoSelectEnabled: true,
        isSoundDisabled: true,
    },
    node: '@sage/xtrem-show-case/ShowCaseProvider',
    title: 'Play sounds',
    menuItem: misc,
})
export class Sounds extends ui.Page {
    @ui.decorators.section<Sounds>({
        title: 'Sounds',
    })
    section: ui.containers.Section;

    @ui.decorators.block<Sounds>({
        parent() {
            return this.section;
        },
        title: 'Examples',
    })
    fieldBlock: ui.containers.Block;

    @ui.decorators.buttonField<Sounds>({
        parent() {
            return this.fieldBlock;
        },
        isTransient: true,
        onClick() {
            return this.$.sound.success();
        },
        map() {
            return 'Play success sound';
        },
    })
    successSound: ui.fields.Button;

    @ui.decorators.buttonField<Sounds>({
        parent() {
            return this.fieldBlock;
        },
        isTransient: true,
        onClick() {
            return this.$.sound.error();
        },
        map() {
            return 'Play error sound';
        },
    })
    errorSound: ui.fields.Button;

    @ui.decorators.block<Sounds>({
        parent() {
            return this.section;
        },
        isTitleHidden: true,
        title: 'isSoundDisabled',
    })
    fieldBlock2: ui.containers.Block;

    @ui.decorators.selectField<Sounds>({
        parent() {
            return this.fieldBlock2;
        },
        title: 'Select',
        isTransient: true,
        isSoundDisabled() {
            return this.isDisabled.value;
        },
        optionType: '@sage/xtrem-show-case/ShowCaseProviderRating',
    })
    selectField: ui.fields.Select;

    @ui.decorators.checkboxField<Sounds>({
        parent() {
            return this.fieldBlock2;
        },
        isTransient: true,
        title: 'Sound disabled',
    })
    isDisabled: ui.fields.Checkbox;

    @ui.decorators.referenceField<Sounds>({
        parent() {
            return this.fieldBlock2;
        },
        title: 'Reference',
        isTransient: true,
        isAutoSelectEnabled: true,
        helperTextField: 'description',
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        isSoundDisabled() {
            return this.isDisabled2.value;
        },
        valueField: 'product',
    })
    referenceField: ui.fields.Reference;

    @ui.decorators.checkboxField<Sounds>({
        parent() {
            return this.fieldBlock2;
        },
        isTransient: true,
        title: 'Sound disabled',
    })
    isDisabled2: ui.fields.Checkbox;

    /**
     * Examples for Dropdown, Select, Filter-Select, Reference, Multi-Dropdown
     * and Multi-Reference fields, with isSoundDisabled property set at runtime.
     **/

    @ui.decorators.block<Sounds>({
        parent() {
            return this.section;
        },
        title: 'Runtime Examples',
    })
    runtimeBlock: ui.containers.Block;

    // Select

    @ui.decorators.selectField<Sounds>({
        isSoundDisabled: false,
        isTransient: true,
        optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
        parent() {
            return this.runtimeBlock;
        },
        title: 'Select',
        width: 'medium',
    })
    runtimeSelect: ui.fields.Select;

    @ui.decorators.checkboxField<Sounds>({
        isReadOnly: true,
        isTransient: true,
        parent() {
            return this.runtimeBlock;
        },
        title: 'Sound Disabled?',
    })
    runtimeSelectCheckbox: ui.fields.Checkbox;

    @ui.decorators.buttonField<Sounds>({
        isTransient: true,
        map() {
            return 'Toggle Sound';
        },
        onClick() {
            const isDisabled = !this.runtimeSelect.isSoundDisabled;
            this.runtimeSelect.isSoundDisabled = isDisabled;
            this.runtimeSelectCheckbox.value = isDisabled;
        },
        parent() {
            return this.runtimeBlock;
        },
    })
    runtimeSelectToggle: ui.fields.Button;

    @ui.decorators.separatorField<Sounds>({
        isFullWidth: true,
        isInvisible: true,
        parent() {
            return this.runtimeBlock;
        },
    })
    runtimeSelectSeparator: ui.fields.Separator;

    // Filter-Select

    @ui.decorators.filterSelectField<Sounds>({
        isSoundDisabled: false,
        isTransient: true,
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        parent() {
            return this.runtimeBlock;
        },
        title: 'Filter Select',
        valueField: 'product',
        width: 'medium',
    })
    runtimeFilterSelect: ui.fields.FilterSelect;

    @ui.decorators.checkboxField<Sounds>({
        isReadOnly: true,
        isTransient: true,
        parent() {
            return this.runtimeBlock;
        },
        title: 'Sound Disabled?',
    })
    runtimeFilterSelectCheckbox: ui.fields.Checkbox;

    @ui.decorators.buttonField<Sounds>({
        isTransient: true,
        map() {
            return 'Toggle Sound';
        },
        onClick() {
            const isDisabled = !this.runtimeFilterSelect.isSoundDisabled;
            this.runtimeFilterSelect.isSoundDisabled = isDisabled;
            this.runtimeFilterSelectCheckbox.value = isDisabled;
        },
        parent() {
            return this.runtimeBlock;
        },
    })
    runtimeFilterSelectToggle: ui.fields.Button;

    @ui.decorators.separatorField<Sounds>({
        isFullWidth: true,
        isInvisible: true,
        parent() {
            return this.runtimeBlock;
        },
    })
    runtimeFilterSelectSeparator: ui.fields.Separator;

    // Reference

    @ui.decorators.referenceField<Sounds>({
        isAutoSelectEnabled: true,
        isSoundDisabled: false,
        isTransient: true,
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        parent() {
            return this.runtimeBlock;
        },
        title: 'Reference',
        valueField: 'product',
        width: 'medium',
    })
    runtimeReference: ui.fields.Reference;

    @ui.decorators.checkboxField<Sounds>({
        isReadOnly: true,
        isTransient: true,
        parent() {
            return this.runtimeBlock;
        },
        title: 'Sound Disabled?',
    })
    runtimeReferenceCheckbox: ui.fields.Checkbox;

    @ui.decorators.buttonField<Sounds>({
        isTransient: true,
        map() {
            return 'Toggle Sound';
        },
        onClick() {
            const isDisabled = !this.runtimeReference.isSoundDisabled;
            this.runtimeReference.isSoundDisabled = isDisabled;
            this.runtimeReferenceCheckbox.value = isDisabled;
        },
        parent() {
            return this.runtimeBlock;
        },
    })
    runtimeReferenceToggle: ui.fields.Button;

    @ui.decorators.separatorField<Sounds>({
        isFullWidth: true,
        isInvisible: true,
        parent() {
            return this.runtimeBlock;
        },
    })
    runtimeReferenceSeparator: ui.fields.Separator;
}
