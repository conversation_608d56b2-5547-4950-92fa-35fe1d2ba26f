import { GraphApi } from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import { tableField } from '../menu-items/_index';

@ui.decorators.page<TableDefaultValues>({
    authorizationCode: 'BSCFLDS',
    module: 'show-case',
    category: 'SHOWCASE',
    node: '@sage/xtrem-show-case/ShowCaseOrder',
    title: 'Field - Table (Default Values)',
    createAction() {
        return this.$standardNewAction;
    },
    businessActions() {
        return [this.$standardCancelAction, this.$standardSaveAction];
    },
    headerDropDownActions() {
        return [this.$standardDeleteAction];
    },
    menuItem: tableField,
})
export class TableDefaultValues extends ui.Page<GraphApi> {
    @ui.decorators.section<TableDefaultValues>({
        title: 'Table field',
    })
    section: ui.containers.Section;

    @ui.decorators.block<TableDefaultValues>({
        parent() {
            return this.section;
        },
        title: 'Field example',
    })
    fieldBlock: ui.containers.Block;

    @ui.decorators.buttonField<TableDefaultValues>({
        parent() {
            return this.fieldBlock;
        },
        onClick() {
            this.field.addRecordWithDefaults();
        },
        isTransient: true,
        map() {
            return 'Add New Item With Defaults';
        },
    })
    addNewItemWithDefaults: ui.fields.Button;

    @ui.decorators.referenceField<TableDefaultValues>({
        bind: 'customer',
        node: '@sage/xtrem-show-case/ShowCaseCustomer',
        valueField: 'name',
        parent() {
            return this.fieldBlock;
        },
        title: 'Customer Name',
    })
    customer: ui.fields.Reference;

    @ui.decorators.tableField<TableDefaultValues>({
        pageSize: 5,
        node: '@sage/xtrem-show-case/ShowCaseInvoice',
        bind: 'invoices',
        fetchesDefaults: true,
        columns: [
            ui.nestedFields.reference({
                bind: 'customer',
                node: '@sage/xtrem-show-case/ShowCaseCustomer',
                title: 'Customer Name',
                valueField: 'name',
            }),
            ui.nestedFields.date({
                bind: 'purchaseDate',
                title: 'Purchase Date',
                fetchesDefaults: true,
            }),
        ],
        orderBy: {
            _id: 1,
        },
        parent() {
            return this.fieldBlock;
        },
    })
    field: ui.fields.Table;
}
