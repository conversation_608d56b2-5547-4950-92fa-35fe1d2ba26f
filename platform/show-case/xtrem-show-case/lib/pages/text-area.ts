import * as ui from '@sage/xtrem-ui';
import { fields } from '../menu-items/fields';

@ui.decorators.page<TextArea>({
    menuItem: fields,
    title: 'Text area',
    subtitle: 'Fields',
    isTransient: true,
    onLoad() {
        this.field.value = 'Sample';
        this.value.value = 'Sample';
    },
})
export class TextArea extends ui.Page {
    @ui.decorators.section<TextArea>({
        title: 'Text area field',
        isTitleHidden: true,
    })
    section: ui.containers.Section<TextArea>;

    @ui.decorators.block<TextArea>({
        parent() {
            return this.section;
        },
        title: 'Introduction',
        width: 'medium',
    })
    introductionBlock: ui.containers.Block;

    @ui.decorators.staticContentField<TextArea>({
        parent() {
            return this.introductionBlock;
        },
        isTitleHidden: true,
        isFullWidth: true,
        content: 'The textarea field represents a block of unformatted text in the user interface.',
    })
    description: ui.fields.StaticContent;

    @ui.decorators.linkField<TextArea>({
        parent() {
            return this.introductionBlock;
        },
        title: 'Source code',
        isFullWidth: true,
        map() {
            return ui.localize('@sage/xtrem-show-case/check-source-code', 'Check it on GitHub');
        },
        page: 'https://github.com/Sage-ERP-X3/xtrem/blob/master/platform/show-case/xtrem-show-case/lib/pages/text-area.ts',
    })
    sourceCodeLink: ui.fields.Link;

    @ui.decorators.block<TextArea>({
        parent() {
            return this.section;
        },
        title: 'Field example',
        width: 'medium',
    })
    fieldBlock: ui.containers.Block;

    @ui.decorators.textAreaField<TextArea>({
        parent() {
            return this.fieldBlock;
        },
        rows: 5,
        onClick() {
            this.clickTriggered.isHidden = false;
            setTimeout(() => {
                this.clickTriggered.isHidden = true;
            }, 5000);
        },
        onChange() {
            this.fieldCallback.value = this.field.value;
            this.changeTriggered.isHidden = false;
            setTimeout(() => {
                this.changeTriggered.isHidden = true;
            }, 5000);
        },
    })
    field: ui.fields.TextArea;

    @ui.decorators.labelField<TextArea>({
        parent() {
            return this.fieldBlock;
        },
        isHidden: true,
        map() {
            return 'Change was triggered';
        },
    })
    changeTriggered: ui.fields.Label;

    @ui.decorators.labelField<TextArea>({
        parent() {
            return this.fieldBlock;
        },
        isHidden: true,
        map() {
            return 'Click was triggered';
        },
    })
    clickTriggered: ui.fields.Label;

    @ui.decorators.textAreaField<TextArea>({
        parent() {
            return this.fieldBlock;
        },
        title() {
            return this.title.value || '';
        },
        isDisabled() {
            return this.isDisabled.value || false;
        },
        isReadOnly() {
            return this.isReadOnly.value || false;
        },
        isHidden() {
            return this.isHidden.value || false;
        },
        onChange() {
            this.field.value = this.fieldCallback.value;
        },
        helperText: 'This field is configured by callbacks',
        rows: 5,
    })
    fieldCallback: ui.fields.TextArea;

    @ui.decorators.block<TextArea>({
        parent() {
            return this.section;
        },
        title: 'Configuration',
    })
    configurationBlock: ui.containers.Block;

    @ui.decorators.checkboxField<TextArea>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is disabled',
        helperText:
            'Determines whether the field is disabled or not. It can also be defined as callback function that returns a boolean.',
        isFullWidth: true,
        isReversed: true,
        onChange() {
            this.field.isDisabled = !!this.isDisabled.value;
        },
    })
    isDisabled: ui.fields.Checkbox;

    @ui.decorators.checkboxField<TextArea>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is read only',
        helperText:
            'Whether the field is editable (isReadOnly = false) or not (isReadOnly = true). The difference with disabled is that isReadOnly suggests that the field is never editable. It can be defined as a boolean, or conditionally by a callback that returns a boolean.',
        isFullWidth: true,
        isReversed: true,
        onChange() {
            this.field.isReadOnly = !!this.isReadOnly.value;
        },
    })
    isReadOnly: ui.fields.Checkbox;

    @ui.decorators.checkboxField<TextArea>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is hidden',
        helperText: 'Determines whether the field is displayed or not.',
        isFullWidth: true,
        isReversed: true,
        onChange() {
            this.field.isHidden = !!this.isHidden.value;
        },
    })
    isHidden: ui.fields.Checkbox;

    @ui.decorators.textField<TextArea>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Title',
        isFullWidth: true,
        helperText:
            'The title that is displayed above the field. The title can be provided as a string, or a callback function returning a string. When declared as a callback within the column of a nested grid, the column id is provided as a parameter. It is automatically picked up by the i18n engine and externalized for translation.',
        onChange() {
            this.field.title = this.title.value || '';
        },
    })
    title: ui.fields.Text;

    @ui.decorators.checkboxField<TextArea>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is title hidden',
        isFullWidth: true,
        helperText: 'Whether the field title above the field should be displayed and its vertical space preserved.',
        isReversed: true,
        onChange() {
            this.field.isTitleHidden = !!this.isTitleHidden.value;
        },
    })
    isTitleHidden: ui.fields.Checkbox;

    @ui.decorators.textField<TextArea>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Helper text',
        helperText:
            'The helper text that is displayed above the field. It is automatically picked up by the i18n engine and externalized.',
        isFullWidth: true,
        onChange() {
            this.field.helperText = this.helperText.value || '';
        },
    })
    helperText: ui.fields.Text;

    @ui.decorators.checkboxField<TextArea>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is helper text hidden',
        helperText:
            'Whether the helper text underneath the field should be displayed and its vertical space preserved.',
        isFullWidth: true,
        isReversed: true,
        onChange() {
            this.field.isHelperTextHidden = !!this.isHelperTextHidden.value;
        },
    })
    isHelperTextHidden: ui.fields.Checkbox;

    @ui.decorators.textField<TextArea>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Placeholder',
        helperText:
            'Placeholder text which is displayed inside the field body when the field is empty. It is automatically picked up by the i18n engine and externalized.',
        isFullWidth: true,
        onChange() {
            this.field.placeholder = this.placeholder.value || '';
        },
    })
    placeholder: ui.fields.Text;

    @ui.decorators.checkboxField<TextArea>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is mandatory',
        helperText:
            ' Makes the field mandatory, empty values will raise an error message. It can also be defined as callback function that returns a boolean.',
        isFullWidth: true,
        isReversed: true,
        onChange() {
            this.field.isMandatory = !!this.isMandatory.value;
        },
    })
    isMandatory: ui.fields.Checkbox;

    @ui.decorators.numericField<TextArea>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Max length',
        helperText:
            'Sets the maximum number of allowed characters. It can also be defined as callback function that returns a number.',
        isFullWidth: true,
        onChange() {
            this.field.maxLength = this.maxLength.value || undefined;
        },
    })
    maxLength: ui.fields.Numeric;

    @ui.decorators.numericField<TextArea>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Min length',
        helperText:
            'Sets the minimum number of required characters. It can also be defined as callback function that returns a number.',
        isFullWidth: true,
        onChange() {
            this.field.minLength = this.minLength.value || undefined;
        },
    })
    minLength: ui.fields.Numeric;

    @ui.decorators.textField<TextArea>({
        parent() {
            return this.additionalBlock;
        },
        title: 'Info message',
        helperText:
            'Indicate additional warning message, rendered as tooltip and blue border. It can also be defined as callback function.',
        isFullWidth: true,
        onChange() {
            this.field.infoMessage = this.infoMessage.value || '';
        },
    })
    infoMessage: ui.fields.Text;

    @ui.decorators.textField<TextArea>({
        parent() {
            return this.additionalBlock;
        },
        title: 'Warning message',
        helperText:
            'Indicate additional information, rendered as tooltip and orange border. It can also be defined as callback.',
        isFullWidth: true,
        onChange() {
            this.field.warningMessage = this.warningMessage.value || '';
        },
    })
    warningMessage: ui.fields.Text;

    @ui.decorators.textField<TextArea>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Value',
        isFullWidth: true,
        onChange() {
            if (this.field.value !== this.value.value) {
                this.field.value = this.value.value;
                this.fieldCallback.value = this.value.value;
            }
        },
    })
    value: ui.fields.Text;

    @ui.decorators.buttonField<TextArea>({
        parent() {
            return this.configurationBlock;
        },
        map() {
            return 'Focus field';
        },
        onClick() {
            this.field.focus();
        },
    })
    focus: ui.fields.Button;

    @ui.decorators.block<TextArea>({
        parent() {
            return this.section;
        },
        title: 'Additional examples',
    })
    additionalBlock: ui.containers.Block;

    @ui.decorators.textAreaField<TextArea>({
        parent() {
            return this.additionalBlock;
        },
        title: 'With warning message',
        warningMessage: 'Wow, warning!',
    })
    warningMessageField: ui.fields.TextArea;

    @ui.decorators.textAreaField<TextArea>({
        parent() {
            return this.additionalBlock;
        },
        title: 'With warning message with callback',
        helperText: 'Type "wow"',
        warningMessage() {
            if (this.warningMessageWithCallbackField.value === 'wow') {
                return 'Warning message';
            }
            return null;
        },
    })
    warningMessageWithCallbackField: ui.fields.TextArea;

    @ui.decorators.textAreaField<TextArea>({
        parent() {
            return this.additionalBlock;
        },
        title: 'With info message',
        infoMessage: 'Wow, warning!',
    })
    infoMessageField: ui.fields.TextArea;

    @ui.decorators.textAreaField<TextArea>({
        parent() {
            return this.additionalBlock;
        },
        title: 'With info message with callback',
        helperText: 'Type "wow"',
        infoMessage() {
            if (this.infoMessageWithCallbackField.value === 'wow') {
                return 'Info message';
            }
            return null;
        },
    })
    infoMessageWithCallbackField: ui.fields.TextArea;

    @ui.decorators.textAreaField<TextArea>({
        parent() {
            return this.additionalBlock;
        },
        title: 'With info message',
        warningMessage: 'Wow, warning!',
        infoMessage: 'You should not see this',
    })
    infoAndWarningMessageField: ui.fields.TextArea;

    @ui.decorators.textAreaField<TextArea>({
        parent() {
            return this.additionalBlock;
        },
        isMandatory: true,
        title: 'Info, warning and validation',
        warningMessage: 'Wow, warning!',
        infoMessage: 'You should not see this',
        helperText: 'This field is mandatory too.',
    })
    infoAndWarningMessageMandatoryField: ui.fields.TextArea;
}
