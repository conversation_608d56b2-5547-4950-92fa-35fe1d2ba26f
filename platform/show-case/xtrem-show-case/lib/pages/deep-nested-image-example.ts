import { Graph<PERSON><PERSON>, ShowCaseProduct, ShowCaseProvider as ShowCaseProviderNode } from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import { fields } from '../menu-items/fields';

@ui.decorators.page<DeepNestedImageExample, ShowCaseProviderNode>({
    authorizationCode: 'SHCPRVD',
    category: 'SHOWCASE',
    menuItem: fields,
    objectTypeSingular: 'Provider',
    objectTypePlural: 'Providers',
    idField() {
        return this._id;
    },
    module: 'show-case',
    navigationPanel: {
        bulkActions: [
            {
                mutation: 'modify',
                title: 'Modify',
                buttonType: 'primary',
                isDestructive: false,
                id: 'modify',
            },
            {
                mutation: 'export',
                title: 'Export',
                buttonType: 'secondary',
                icon: 'file_excel',
                isDestructive: false,
                id: 'export',
            },
            {
                mutation: 'print',
                title: 'Print',
                buttonType: 'tertiary',
                icon: 'print',
                isDestructive: false,
                id: 'print',
            },
            {
                mutation: 'remove',
                title: 'Delete',
                buttonType: 'tertiary',
                icon: 'delete',
                isDestructive: true,
                id: 'remove',
            },
        ],
        listItem: {
            title: ui.nestedFields.text({ bind: 'textField', title: 'Name' }),
            line2: ui.nestedFields.text({ bind: '_id', title: 'ID' }),
            line3: ui.nestedFields.date({ bind: 'dateField', title: 'Added on' }),
            image: ui.nestedFields.image({ bind: 'logo', title: 'Logo', placeholderMode: 'Initials' }),
        },
    },
    node: '@sage/xtrem-show-case/ShowCaseProvider',
    title: 'Deep nested image example',
    priority: 200,
})
export class DeepNestedImageExample extends ui.Page<GraphApi> {
    @ui.decorators.section<DeepNestedImageExample>({})
    section: ui.containers.Section;

    @ui.decorators.block<DeepNestedImageExample>({
        parent() {
            return this.section;
        },
    })
    block: ui.containers.Block;

    @ui.decorators.textField<DeepNestedImageExample>({
        parent() {
            return this.block;
        },
        title: 'Id',
    })
    _id: ui.fields.Text;

    @ui.decorators.dateField<DeepNestedImageExample>({
        parent() {
            return this.block;
        },
        title: 'Date',
    })
    dateField: ui.fields.Date;

    @ui.decorators.checkboxField<DeepNestedImageExample>({
        parent() {
            return this.block;
        },
        title: 'Checkbox',
    })
    booleanField: ui.fields.Checkbox;

    @ui.decorators.numericField<DeepNestedImageExample>({
        parent() {
            return this.block;
        },
        title: 'Integer',
        scale: 0,
    })
    integerField: ui.fields.Numeric;

    @ui.decorators.numericField<DeepNestedImageExample>({
        parent() {
            return this.block;
        },
        async onChange() {
            await this.products.revalidate(record => {
                return record.qty >= this.minQuantity.value;
            });
        },
        title: 'Minimum Quantity',
        scale: 0,
    })
    minQuantity: ui.fields.Numeric;

    @ui.decorators.numericField<DeepNestedImageExample>({
        bind: 'decimalField',
        parent() {
            return this.block;
        },
        title: 'Decimal',
        scale: 2,
    })
    testingTheBindPropertyInMutations: ui.fields.Numeric;

    @ui.decorators.tableField<DeepNestedImageExample, ShowCaseProduct>({
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        mainField: 'description',
        parent() {
            return this.block;
        },
        title: 'Products',
        canFilter: false,
        onRowClick() {
            ui.console.log('clicked');
        },
        mobileCard: {
            title: ui.nestedFields.text({ bind: 'description', title: 'Description' }),
            image: ui.nestedFields.image({ bind: { provider: { logo: true } } }),
        },
        sidebar: {
            title(_id, recordValue) {
                return recordValue.description;
            },
            layout() {
                return {
                    mainSection: {
                        title: 'Main section',
                        blocks: {
                            mainBlock: {
                                title: 'Some block title',
                                fields: ['category', 'total', 'qty', 'amount'],
                            },
                        },
                    },
                };
            },
        },
        orderBy: { _id: 1 },
        columns: [
            ui.nestedFields.text({ bind: '_id', isReadOnly: true, title: 'Id' }),
            ui.nestedFields.link({
                bind: 'product',
                title: 'Product',
                onClick(_id) {
                    this.$.router.goTo('@sage/xtrem-show-case/ShowCaseProduct', { _id });
                },
            }),
            ui.nestedFields.text({ bind: 'description', title: 'Description' }),
            ui.nestedFields.checkbox({ bind: 'hotProduct', isReadOnly: true, title: 'Hot' }),
            ui.nestedFields.select({ bind: 'category', isReadOnly: true, title: 'Category' }),
            ui.nestedFields.numeric({ bind: 'total', title: 'Net Price', scale: 2, min: 0 }),
            ui.nestedFields.numeric({ bind: 'qty', title: 'Quantity', scale: 2 }),
            ui.nestedFields.numeric({ bind: 'amount', title: 'Amount', scale: 2 }),
        ],
        dropdownActions: [
            {
                title: 'Open in sidebar',
                icon: 'box_arrow_left',
                onClick(rowId: any) {
                    this.products.openSidebar(rowId);
                },
            },
            ui.menuSeparator(),
            {
                icon: 'bin',
                title: 'Remove',
                isDestructive: true,
                async onClick(rowId: any, data: any) {
                    await this.$.graph.delete({
                        _id: data._id,
                        nodeName: '@sage/xtrem-show-case/ShowCaseProduct',
                    });
                    await this.products.refresh();
                },
            },
        ],
    })
    products: ui.fields.Table<ShowCaseProduct>;
}
