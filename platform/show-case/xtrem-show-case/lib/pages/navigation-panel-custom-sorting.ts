import { Graph<PERSON><PERSON>, ShowCaseProduct, ShowCaseProvider } from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import { navigationPanel } from '../menu-items/navigation-panel';

@ui.decorators.page<NavigationPanelCustomSorting, ShowCaseProduct>({
    authorizationCode: 'NAVPANEL',
    module: 'show-case',
    title: 'Navigation Panel - Custom sort',
    subtitle: 'Navigation panel examples',
    node: '@sage/xtrem-show-case/ShowCaseProduct',
    category: 'SHOWCASE',
    navigationPanel: {
        isFirstLetterSeparatorHidden: true,
        listItem: {
            title: ui.nestedFields.text({ bind: 'product' }),
            titleRight: ui.nestedFields.label({ bind: 'category' }),
            line2: ui.nestedFields.reference({
                bind: 'provider',
                valueField: 'textField',
                node: '@sage/xtrem-show-case/ShowCaseProvider',
            }),
            line3: ui.nestedFields.text({ bind: 'releaseDate' }),
        },
        orderBy: {
            releaseDate: -1,
        },
    },
    menuItem: navigationPanel,
})
export class NavigationPanelCustomSorting extends ui.Page<GraphApi> {
    @ui.decorators.section<NavigationPanelCustomSorting>({})
    section: ui.containers.Section;

    @ui.decorators.block<NavigationPanelCustomSorting>({
        parent() {
            return this.section;
        },
        title: 'Product data',
    })
    block: ui.containers.Block;

    @ui.decorators.textField<NavigationPanelCustomSorting>({
        parent() {
            return this.block;
        },
        title: 'Id',
        isReadOnly: true,
    })
    _id: ui.fields.Text;

    @ui.decorators.textField<NavigationPanelCustomSorting>({
        parent() {
            return this.block;
        },
        title: 'Product',
        isReadOnly: true,
    })
    product: ui.fields.Text;

    @ui.decorators.referenceField<NavigationPanelCustomSorting, ShowCaseProvider>({
        parent() {
            return this.block;
        },
        columns: [ui.nestedFields.text({ bind: 'textField' })],
        node: '@sage/xtrem-show-case/ShowCaseProvider',
        title: 'Provider',
        isReadOnly: true,
        valueField: 'textField',
        helperTextField: '_id',
        minLookupCharacters: 0,
    })
    provider: ui.fields.Reference;
}
