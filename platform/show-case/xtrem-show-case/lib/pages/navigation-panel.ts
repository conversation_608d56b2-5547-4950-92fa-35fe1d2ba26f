import { <PERSON><PERSON>h<PERSON><PERSON>, ShowCaseProduct, ShowCaseProvider } from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import { navigationPanel } from '../menu-items/navigation-panel';

const idQueryParameterFilterTitle = 'Id query parameter';
const storageFilterTitle = 'Local storage';

@ui.decorators.page<NavigationPanel, ShowCaseProduct>({
    authorizationCode: 'NAVPANEL',
    module: 'show-case',
    title: 'Navigation Panel',
    node: '@sage/xtrem-show-case/ShowCaseProduct',
    category: 'SHOWCASE',
    onLoad() {
        this.storageFilter.value = this.$.storage.get('storageFilter') as string;
        if (!this.$.queryParameters._id) {
            this.$.router.firstRecord();
        }
    },
    businessActions() {
        return [this.$standardCancelAction, this.$standardSaveAction];
    },
    createAction() {
        return this.$standardNewAction;
    },
    navigationPanel: {
        emptyStateText: 'We are in level 1',
        emptyStateClickableText: 'Click me',
        onEmptyStateLinkClick() {
            this.$.dialog.message('info', 'Hey', 'You just clicked me');
        },
        isAutoSelectEnabled: true,
        listItem: {
            title: ui.nestedFields.text({ bind: 'product' }),
            titleRight: ui.nestedFields.label({ bind: '_id' }),
            line2Right: ui.nestedFields.date({ bind: 'releaseDate' }),
            line2: ui.nestedFields.reference({
                bind: 'provider',
                node: '@sage/xtrem-show-case/ShowCaseProvider',
                valueField: 'textField',
            }),
            image: ui.nestedFields.image({ bind: 'imageField' }),
        },
        optionsMenu: [
            {
                title: 'All Products (link)',
                page: '@sage/xtrem-show-case/NavigationPanel',
            },
            {
                title: storageFilterTitle,
                graphQLFilter: storage => {
                    const storageFilter = storage.get('storageFilter') as string;
                    return storageFilter ? { product: { _regex: storageFilter, _options: 'i' } } : undefined;
                },
            },
            {
                title: idQueryParameterFilterTitle,
                graphQLFilter: (_, queryParameter) => {
                    return { _id: { _eq: String(queryParameter._id) } };
                },
            },
        ],
        onOptionsMenuValueChange(mainFilterValue: string, selectedFilter: string) {
            this.inputFilterValue.value = mainFilterValue;
            this.currentDropdownItem.value = selectedFilter;

            // CC Design flaw: The functional developer should perform the following checks on the optionsMenu graphQLFilter functions, but there is no access to the page in that context to display an error message

            if (
                selectedFilter === idQueryParameterFilterTitle &&
                !this.onDropdownChangePreventFiltering.value &&
                !this.$.queryParameters._id
            ) {
                this.$.dialog.message(
                    'warn',
                    'No id query parameter',
                    'No results are displayed for this dropdown filter if no id query parameter is provided (e.g. no product is selected)',
                );
            }

            if (
                selectedFilter === storageFilterTitle &&
                !this.onDropdownChangePreventFiltering.value &&
                !this.storageFilter.value
            ) {
                this.$.dialog.message(
                    'warn',
                    'No local storage filter',
                    'No filter will be applied to the list because the local storage filter is empty',
                );
            }

            return this.onDropdownChangePreventFiltering.value;
        },
        onSelect(listItemValue: any) {
            this.onSelectEntryProduct.value = listItemValue.product;
            this.onSelectEntryProvider.value = listItemValue.provider.textField;
            return this.onSelectEntryPreventNavigation.value;
        },
    },
    menuItem: navigationPanel,
})
export class NavigationPanel extends ui.Page<GraphApi> {
    @ui.decorators.section<NavigationPanel>({})
    section: ui.containers.Section;

    @ui.decorators.block<NavigationPanel>({
        parent() {
            return this.section;
        },
        title: 'Current product data',
    })
    block: ui.containers.Block;

    @ui.decorators.textField<NavigationPanel>({
        parent() {
            return this.block;
        },
        title: 'Id',
        isReadOnly: true,
    })
    _id: ui.fields.Text;

    @ui.decorators.textField<NavigationPanel>({
        parent() {
            return this.block;
        },
        title: 'Product',
        isReadOnly: true,
    })
    product: ui.fields.Text;

    @ui.decorators.referenceField<NavigationPanel, ShowCaseProvider>({
        parent() {
            return this.block;
        },
        helperTextField: '_id',
        isReadOnly: true,
        node: '@sage/xtrem-show-case/ShowCaseProvider',
        title: 'Provider',
        valueField: 'textField',
    })
    provider: ui.fields.Reference;

    @ui.decorators.buttonField<NavigationPanel>({
        parent() {
            return this.block;
        },
        isTransient: true,
        async onClick() {
            await this.$.refreshNavigationPanel();
        },
        map() {
            return 'Refresh Navigation Panel';
        },
    })
    refreshNavigationPanel: ui.fields.Button;

    /** ******* filters *********/

    @ui.decorators.block<NavigationPanel>({
        parent() {
            return this.section;
        },
        title: 'Navigation panel filters',
    })
    filtersBlock: ui.containers.Block;

    @ui.decorators.textField<NavigationPanel>({
        parent() {
            return this.filtersBlock;
        },
        isTransient: true,
        title: 'Local storage product filter',
        onChange() {
            this.$.storage.set('storageFilter', this.storageFilter.value);
        },
    })
    storageFilter: ui.fields.Text;

    @ui.decorators.textField<NavigationPanel>({
        parent() {
            return this.filtersBlock;
        },
        isTransient: true,
        title: 'List item right item bind',
        helperText: 'Search something in the nav panel in order to apply it',
        onChange() {
            this.$.page.navigationPanel.listItem.titleLineRight = ui.nestedFields.text({
                bind: `${this.listItemRight.value}`,
            });
        },
    })
    listItemRight: ui.fields.Text;

    @ui.decorators.textField<NavigationPanel>({
        parent() {
            return this.filtersBlock;
        },
        isTransient: true,
        isReadOnly: true,
        title: 'Navigation panel input filter',
    })
    inputFilterValue: ui.fields.Text;

    @ui.decorators.textField<NavigationPanel>({
        parent() {
            return this.filtersBlock;
        },
        isTransient: true,
        isReadOnly: true,
        title: 'Navigation panel current dropdown item',
    })
    currentDropdownItem: ui.fields.Text;

    @ui.decorators.checkboxField<NavigationPanel>({
        parent() {
            return this.filtersBlock;
        },
        isTransient: true,
        title: "Prevent filtering on dropdown selection (doesn't apply for links)",
    })
    onDropdownChangePreventFiltering: ui.fields.Checkbox;

    /** ******* onSelect *********/

    @ui.decorators.block<NavigationPanel>({
        parent() {
            return this.section;
        },
        title: 'Navigation panel onSelect',
    })
    onChangeBlock: ui.containers.Block;

    @ui.decorators.checkboxField<NavigationPanel>({
        parent() {
            return this.onChangeBlock;
        },
        isTransient: true,
        title: 'Prevent navigation on entry selection',
    })
    onSelectEntryPreventNavigation: ui.fields.Checkbox;

    @ui.decorators.textField<NavigationPanel>({
        parent() {
            return this.onChangeBlock;
        },
        isTransient: true,
        isReadOnly: true,
        title: 'Selected entry: product',
    })
    onSelectEntryProduct: ui.fields.Text;

    @ui.decorators.textField<NavigationPanel>({
        parent() {
            return this.onChangeBlock;
        },
        isTransient: true,
        isReadOnly: true,
        title: 'Selected entry: provider',
    })
    onSelectEntryProvider: ui.fields.Text;
}
