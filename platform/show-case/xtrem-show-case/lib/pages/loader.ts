import * as ui from '@sage/xtrem-ui';
import { misc } from '../menu-items/misc';

@ui.decorators.page<Loader>({
    authorizationCode: 'PGLDR',
    module: 'show-case',
    title: 'Page - Loader',
    isTransient: true,
    category: 'SHOWCASE',
    onLoad() {
        this.loaderTimeout.value = 4;
    },
    menuItem: misc,
})
export class Loader extends ui.Page {
    @ui.decorators.section<Loader>({
        title: 'Loader',
    })
    section: ui.containers.Section;

    @ui.decorators.block<Loader>({
        parent() {
            return this.section;
        },
    })
    block: ui.containers.Block;

    @ui.decorators.numericField<Loader>({
        title: 'Loader timeout',
        parent() {
            return this.block;
        },
        postfix: 'seconds',
    })
    loaderTimeout: ui.fields.Numeric;

    @ui.decorators.buttonField<Loader>({
        map() {
            return 'Display loader';
        },
        parent() {
            return this.block;
        },
        onClick() {
            this.$.loader.isHidden = false;
            setTimeout(() => {
                this.$.loader.isHidden = true;
            }, this.loaderTimeout.value * 1000);
        },
    })
    showLoader: ui.fields.Button;
}
