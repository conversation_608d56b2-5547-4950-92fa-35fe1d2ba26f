import * as ui from '@sage/xtrem-ui';
import { fields } from '../menu-items/fields';

@ui.decorators.page<Numeric>({
    menuItem: fields,
    title: 'Numeric',
    subtitle: 'Fields',
    isTransient: true,
    onLoad() {
        this.field.value = 50.72;
        this.value.value = 50.72;
        this.scale.value = 2;
    },
    headerSection() {
        return this.headerSection;
    },
})
export class Numeric extends ui.Page {
    @ui.decorators.section<Numeric>({
        title: 'Numeric',
        isTitleHidden: true,
    })
    headerSection: ui.containers.Section;

    @ui.decorators.section<Numeric>({
        title: 'Numeric field',
    })
    section: ui.containers.Section;

    @ui.decorators.block<Numeric>({
        parent() {
            return this.headerSection;
        },
        title: 'Introduction',
        width: 'medium',
    })
    introductionBlock: ui.containers.Block;

    @ui.decorators.staticContentField<Numeric>({
        parent() {
            return this.introductionBlock;
        },
        isTitleHidden: true,
        isFullWidth: true,
        content: 'Numeric fields can be used to represent integers and decimals.',
    })
    description: ui.fields.StaticContent;

    @ui.decorators.linkField<Numeric>({
        parent() {
            return this.introductionBlock;
        },
        title: 'Source code',
        isFullWidth: true,
        map() {
            return ui.localize('@sage/xtrem-show-case/check-source-code', 'Check it on GitHub');
        },
        page: 'https://github.com/Sage-ERP-X3/xtrem/blob/master/platform/show-case/xtrem-show-case/lib/pages/numeric.ts',
    })
    sourceCodeLink: ui.fields.Link;

    @ui.decorators.block<Numeric>({
        parent() {
            return this.headerSection;
        },
        width: 'medium',
        title: 'Field example',
    })
    fieldBlock: ui.containers.Block;

    @ui.decorators.numericField<Numeric>({
        parent() {
            return this.fieldBlock;
        },
        onClick() {
            this.clickTriggered.isHidden = false;
            setTimeout(() => {
                this.clickTriggered.isHidden = true;
            }, 5000);
        },
        onChange() {
            if (this.field.value !== this.value.value) {
                this.value.value = this.field.value;
                this.changeTriggered.isHidden = false;
                setTimeout(() => {
                    this.changeTriggered.isHidden = true;
                }, 5000);
            }
        },
        scale: 2,
    })
    field: ui.fields.Numeric;

    @ui.decorators.labelField<Numeric>({
        parent() {
            return this.fieldBlock;
        },
        isHidden: true,
        map() {
            return 'Change was triggered';
        },
    })
    changeTriggered: ui.fields.Label;

    @ui.decorators.labelField<Numeric>({
        parent() {
            return this.fieldBlock;
        },
        isHidden: true,
        map() {
            return 'Click was triggered';
        },
    })
    clickTriggered: ui.fields.Label;

    @ui.decorators.block<Numeric>({
        parent() {
            return this.section;
        },
        title: 'Configuration',
    })
    configurationBlock: ui.containers.Block;

    @ui.decorators.checkboxField<Numeric>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is disabled',
        helperText:
            'Determines whether the field is disabled or not. It can also be defined as callback function that returns a boolean.',
        isFullWidth: true,
        isReversed: true,
        onChange() {
            this.field.isDisabled = !!this.isDisabled.value;
        },
    })
    isDisabled: ui.fields.Checkbox;

    @ui.decorators.checkboxField<Numeric>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is read only',
        helperText:
            'Whether the field is editable (isReadOnly = false) or not (isReadOnly = true). The difference with disabled is that isReadOnly suggests that the field is never editable. It can be defined as a boolean, or conditionally by a callback that returns a boolean.',
        isFullWidth: true,
        isReversed: true,
        onChange() {
            this.field.isReadOnly = !!this.isReadOnly.value;
        },
    })
    isReadOnly: ui.fields.Checkbox;

    @ui.decorators.checkboxField<Numeric>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is hidden',
        helperText: 'Determines whether the field is displayed or not.',
        isFullWidth: true,
        isReversed: true,
        onChange() {
            this.field.isHidden = !!this.isHidden.value;
        },
    })
    isHidden: ui.fields.Checkbox;

    @ui.decorators.textField<Numeric>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Title',
        isFullWidth: true,
        helperText:
            'The title that is displayed above the field. The title can be provided as a string, or a callback function returning a string. When declared as a callback within the column of a nested grid, the column id is provided as a parameter. It is automatically picked up by the i18n engine and externalized for translation.',
        onChange() {
            this.field.title = this.title.value || '';
        },
    })
    title: ui.fields.Text;

    @ui.decorators.checkboxField<Numeric>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is title hidden',
        isFullWidth: true,
        helperText: 'Whether the field title above the field should be displayed and its vertical space preserved.',
        isReversed: true,
        onChange() {
            this.field.isTitleHidden = !!this.isTitleHidden.value;
        },
    })
    isTitleHidden: ui.fields.Checkbox;

    @ui.decorators.textField<Numeric>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Helper text',
        helperText:
            'The helper text that is displayed above the field. It is automatically picked up by the i18n engine and externalized.',
        isFullWidth: true,
        onChange() {
            this.field.helperText = this.helperText.value || '';
        },
    })
    helperText: ui.fields.Text;

    @ui.decorators.checkboxField<Numeric>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is helper text hidden',
        helperText:
            'Whether the helper text underneath the field should be displayed and its vertical space preserved.',
        isFullWidth: true,
        isReversed: true,
        onChange() {
            this.field.isHelperTextHidden = !!this.isHelperTextHidden.value;
        },
    })
    isHelperTextHidden: ui.fields.Checkbox;

    @ui.decorators.selectField<Numeric>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Icon',
        isFullWidth: true,
        helperText:
            'Icon to be displayed on the right side of the input. The list of icons are defined by Sage Design Language System',
        onChange() {
            this.field.icon = this.icon.value as any;
        },
        options: ['add', 'calendar', 'edit', 'gift', 'image', 'ledger', 'pause_circle', 'refresh', 'tag', 'video'],
    })
    icon: ui.fields.Select;

    @ui.decorators.textField<Numeric>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Prefix',
        isFullWidth: true,
        helperText:
            'A string that is displayed inside the field before the value, aligned to the left. It can be defined as a string, or conditionally by a callback that returns a string.',
        onChange() {
            this.field.prefix = this.prefix.value || '';
        },
    })
    prefix: ui.fields.Text;

    @ui.decorators.textField<Numeric>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Postfix',
        helperText:
            'A string that is displayed inside the field after the value, aligned to the right. It can be defined as a string, or conditionally by a callback that returns a string',
        isFullWidth: true,
        onChange() {
            this.field.postfix = this.postfix.value || '';
        },
    })
    postfix: ui.fields.Text;

    @ui.decorators.dropdownListField<Numeric>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Unit',
        helperText: 'Automatically sets the prefix or postfix and the scale.',
        isFullWidth: true,
        options: ['EUR', 'GBP', 'USD', 'CHF', 'HUF'],
        onChange() {
            switch (this.unit.value) {
                case 'EUR':
                    this.field.unit = { decimalDigits: 2, id: 'EUR', symbol: '€' };
                    break;
                case 'GBP':
                    this.field.unit = { decimalDigits: 2, id: 'GBP', symbol: '£' };
                    break;
                case 'USD':
                    this.field.unit = { decimalDigits: 2, id: 'USD', symbol: '$' };
                    break;
                case 'CHF':
                    this.field.unit = { decimalDigits: 2, id: 'CHF', symbol: 'Fr' };
                    break;
                case 'HUF':
                    this.field.unit = { decimalDigits: 0, id: 'HUF', symbol: 'Ft' };
                    break;
                default:
                    this.field.unit = null;
            }
        },
    })
    unit: ui.fields.DropdownList;

    @ui.decorators.textField<Numeric>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Placeholder',
        helperText:
            'Placeholder text which is displayed inside the field body when the field is empty. It is automatically picked up by the i18n engine and externalized.',
        isFullWidth: true,
        onChange() {
            this.field.placeholder = this.placeholder.value || '';
        },
    })
    placeholder: ui.fields.Text;

    @ui.decorators.checkboxField<Numeric>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is mandatory',
        helperText:
            ' Makes the field mandatory, empty values will raise an error message. It can also be defined as callback function that returns a boolean.',
        isFullWidth: true,
        isReversed: true,
        onChange() {
            this.field.isMandatory = !!this.isMandatory.value;
        },
    })
    isMandatory: ui.fields.Checkbox;

    @ui.decorators.numericField<Numeric>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Max value',
        helperText:
            'Sets the upper end of the allowed value range. It can also be defined as callback function that returns a number.',
        isFullWidth: true,
        onChange() {
            this.field.max = this.maxValue.value || undefined;
        },
    })
    maxValue: ui.fields.Numeric;

    @ui.decorators.numericField<Numeric>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Min value',
        helperText:
            'Sets the lower end of the allowed value range. It can also be defined as callback function that returns a number.',
        isFullWidth: true,
        onChange() {
            this.field.min = this.minValue.value || undefined;
        },
    })
    minValue: ui.fields.Numeric;

    @ui.decorators.numericField<Numeric>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Scale',
        helperText:
            'Number of decimal digits to be displayed in the field. It can be defined as a number, or conditionally by a callback that returns a number.',
        isFullWidth: true,
        onChange() {
            this.field.scale = this.scale.value || undefined;
        },
    })
    scale: ui.fields.Numeric;

    @ui.decorators.textField<Numeric>({
        parent() {
            return this.additionalBlock;
        },
        title: 'Info message',
        helperText:
            'Indicate additional warning message, rendered as tooltip and blue border. It can also be defined as callback function.',
        isFullWidth: true,
        onChange() {
            this.field.infoMessage = this.infoMessage.value || '';
        },
    })
    infoMessage: ui.fields.Text;

    @ui.decorators.textField<Numeric>({
        parent() {
            return this.additionalBlock;
        },
        title: 'Warning message',
        helperText:
            'Indicate additional information, rendered as tooltip and orange border. It can also be defined as callback.',
        isFullWidth: true,
        onChange() {
            this.field.warningMessage = this.warningMessage.value || '';
        },
    })
    warningMessage: ui.fields.Text;

    @ui.decorators.numericField<Numeric>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Value',
        onChange() {
            if (this.value.value !== this.field.value) {
                this.field.value = this.value.value;
            }
        },
        scale: 2,
    })
    value: ui.fields.Numeric;

    @ui.decorators.buttonField<Numeric>({
        parent() {
            return this.configurationBlock;
        },
        map() {
            return 'Focus field';
        },
        onClick() {
            this.field.focus();
        },
    })
    focus: ui.fields.Button;

    @ui.decorators.buttonField<Numeric>({
        parent() {
            return this.configurationBlock;
        },
        map() {
            return 'Clear field';
        },
        onClick() {
            this.field.value = null;
        },
    })
    clearField: ui.fields.Button;

    /* Additional examples */

    @ui.decorators.block<Numeric>({
        parent() {
            return this.section;
        },
        title: 'Additional examples',
    })
    additionalBlock: ui.containers.Block;

    @ui.decorators.numericField<Numeric>({
        parent() {
            return this.additionalBlock;
        },
        title: 'Mandatory',
        isMandatory: true,
    })
    mandatory: ui.fields.Numeric;

    @ui.decorators.numericField<Numeric>({
        parent() {
            return this.additionalBlock;
        },
        title: 'Mandatory Decimal',
        isMandatory: true,
        scale: 2,
        postfix: '€',
    })
    mandatoryWithScale: ui.fields.Numeric;

    @ui.decorators.numericField<Numeric>({
        parent() {
            return this.additionalBlock;
        },
        title: 'Non-Zero integer',
        isNotZero: true,
    })
    nonZeroInteger: ui.fields.Numeric;

    @ui.decorators.numericField<Numeric>({
        parent() {
            return this.additionalBlock;
        },
        title: 'Non-Zero Decimal',
        isNotZero: true,
        scale: 2,
        postfix: '€',
    })
    nonZeroWithScale: ui.fields.Numeric;

    @ui.decorators.numericField<Numeric>({
        parent() {
            return this.additionalBlock;
        },
        title: 'Full width',
        isFullWidth: true,
    })
    fullWidth: ui.fields.Numeric;

    @ui.decorators.separatorField<Numeric>({
        parent() {
            return this.additionalBlock;
        },
        isFullWidth: true,
    })
    fieldSeparator1: ui.fields.Separator;

    @ui.decorators.numericField<Numeric>({
        parent() {
            return this.additionalBlock;
        },
        title: 'With warning message',
        warningMessage: 'Wow, warning!',
    })
    warningMessageField: ui.fields.Numeric;

    @ui.decorators.numericField<Numeric>({
        parent() {
            return this.additionalBlock;
        },
        title: 'With warning message with callback',
        helperText: 'Type "123"',
        warningMessage() {
            if (this.warningMessageWithCallbackField.value === 123) {
                return 'Warning message';
            }
            return null;
        },
    })
    warningMessageWithCallbackField: ui.fields.Numeric;

    @ui.decorators.numericField<Numeric>({
        parent() {
            return this.additionalBlock;
        },
        title: 'With info message',
        infoMessage: 'Wow, warning!',
    })
    infoMessageField: ui.fields.Numeric;

    @ui.decorators.numericField<Numeric>({
        parent() {
            return this.additionalBlock;
        },
        title: 'With info message with callback',
        helperText: 'Type "123"',
        infoMessage() {
            if (this.infoMessageWithCallbackField.value === 123) {
                return 'Info message';
            }
            return null;
        },
    })
    infoMessageWithCallbackField: ui.fields.Numeric;

    @ui.decorators.numericField<Numeric>({
        parent() {
            return this.additionalBlock;
        },
        title: 'With info message',
        warningMessage: 'Wow, warning!',
        infoMessage: 'You should not see this',
    })
    infoAndWarningMessageField: ui.fields.Numeric;

    @ui.decorators.numericField<Numeric>({
        parent() {
            return this.additionalBlock;
        },
        isMandatory: true,
        title: 'Info, warning and validation',
        warningMessage: 'Wow, warning!',
        infoMessage: 'You should not see this',
        helperText: 'This field is mandatory too.',
    })
    infoAndWarningMessageMandatoryField: ui.fields.Numeric;

    @ui.decorators.block<Numeric>({
        parent() {
            return this.section;
        },
        title: 'Dynamic validations',
    })
    dynamicValidations: ui.containers.Block;

    @ui.decorators.numericField<Numeric>({
        parent() {
            return this.dynamicValidations;
        },
        title: 'Mandatory By Setter',
    })
    mandatoryBySetter: ui.fields.Numeric;

    @ui.decorators.switchField<Numeric>({
        parent() {
            return this.dynamicValidations;
        },
        title: 'Is Mandatory?',
        onChange() {
            this.mandatoryBySetter.isMandatory = this.mandatoryBySetterSwitch.value;
        },
    })
    mandatoryBySetterSwitch: ui.fields.Switch;

    @ui.decorators.separatorField<Numeric>({
        parent() {
            return this.dynamicValidations;
        },
        isFullWidth: true,
    })
    mandatorySeparator1: ui.fields.Separator;

    @ui.decorators.numericField<Numeric>({
        parent() {
            return this.dynamicValidations;
        },
        title: 'Mandatory By Callback',
        isMandatory() {
            return this.mandatoryByCallbackSwitch.value;
        },
    })
    mandatoryByCallback: ui.fields.Numeric;

    @ui.decorators.switchField<Numeric>({
        parent() {
            return this.dynamicValidations;
        },
        title: 'Is Mandatory?',
    })
    mandatoryByCallbackSwitch: ui.fields.Switch;

    @ui.decorators.separatorField<Numeric>({
        parent() {
            return this.dynamicValidations;
        },
        isFullWidth: true,
    })
    isNotZeroSeparator: ui.fields.Separator;

    @ui.decorators.numericField<Numeric>({
        parent() {
            return this.dynamicValidations;
        },
        title: 'is not zero by Setter',
    })
    isNotZeroBySetter: ui.fields.Numeric;

    @ui.decorators.switchField<Numeric>({
        parent() {
            return this.dynamicValidations;
        },
        title: 'Is not zero?',
        onChange() {
            this.isNotZeroBySetter.isNotZero = this.isNotZeroBySetterSwitch.value;
        },
    })
    isNotZeroBySetterSwitch: ui.fields.Switch;

    @ui.decorators.separatorField<Numeric>({
        parent() {
            return this.dynamicValidations;
        },
        isFullWidth: true,
    })
    isNotZeroSeparator1: ui.fields.Separator;

    @ui.decorators.numericField<Numeric>({
        parent() {
            return this.dynamicValidations;
        },
        title: 'Is not zero By Callback',
        isNotZero() {
            return this.isNotZeroByCallbackSwitch.value;
        },
    })
    isNotZeroByCallback: ui.fields.Numeric;

    @ui.decorators.switchField<Numeric>({
        parent() {
            return this.dynamicValidations;
        },
        title: 'Is not zero?',
    })
    isNotZeroByCallbackSwitch: ui.fields.Switch;

    @ui.decorators.separatorField<Numeric>({
        parent() {
            return this.dynamicValidations;
        },
        isFullWidth: true,
    })
    mandatorySeparator2: ui.fields.Separator;

    @ui.decorators.numericField<Numeric>({
        parent() {
            return this.dynamicValidations;
        },
        title: 'Min length by setter',
    })
    minValueBySetter: ui.fields.Numeric;

    @ui.decorators.numericField<Numeric>({
        parent() {
            return this.dynamicValidations;
        },
        title: 'Min value?',
        helperText: 'When 0 is set, the rule is disabled',
        onChange() {
            this.minValueBySetter.min = this.minValueBySetterLimit.value || undefined;
        },
    })
    minValueBySetterLimit: ui.fields.Numeric;

    @ui.decorators.separatorField<Numeric>({
        parent() {
            return this.dynamicValidations;
        },
        isFullWidth: true,
    })
    mandatorySeparator3: ui.fields.Separator;

    @ui.decorators.numericField<Numeric>({
        parent() {
            return this.dynamicValidations;
        },
        title: 'Min value By Callback',
        min() {
            return this.minLengthCallbackLimit.value || undefined;
        },
    })
    minValueByCallback: ui.fields.Numeric;

    @ui.decorators.numericField<Numeric>({
        parent() {
            return this.dynamicValidations;
        },
        title: 'Min value?',
        helperText: 'When 0 is set, the rule is disabled',
    })
    minLengthCallbackLimit: ui.fields.Numeric;

    @ui.decorators.separatorField<Numeric>({
        parent() {
            return this.dynamicValidations;
        },
        isFullWidth: true,
    })
    mandatorySeparator4: ui.fields.Separator;

    @ui.decorators.numericField<Numeric>({
        parent() {
            return this.dynamicValidations;
        },
        title: 'Max value by setter',
    })
    maxValueBySetter: ui.fields.Numeric;

    @ui.decorators.numericField<Numeric>({
        parent() {
            return this.dynamicValidations;
        },
        title: 'Max value?',
        helperText: 'When 0 is set, the rule is disabled',
        onChange() {
            this.maxValueBySetter.max = this.maxValueBySetterLimit.value || undefined;
        },
    })
    maxValueBySetterLimit: ui.fields.Numeric;

    @ui.decorators.separatorField<Numeric>({
        parent() {
            return this.dynamicValidations;
        },
        isFullWidth: true,
    })
    mandatorySeparator5: ui.fields.Separator;

    @ui.decorators.numericField<Numeric>({
        parent() {
            return this.dynamicValidations;
        },
        title: 'Max value By Callback',
        max() {
            return this.maxValueCallbackLimit.value || undefined;
        },
    })
    maxValueByCallback: ui.fields.Numeric;

    @ui.decorators.numericField<Numeric>({
        parent() {
            return this.dynamicValidations;
        },
        title: 'Max value?',
        helperText: 'When 0 is set, the rule is disabled',
    })
    maxValueCallbackLimit: ui.fields.Numeric;
}
