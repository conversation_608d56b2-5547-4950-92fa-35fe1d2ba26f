import * as ui from '@sage/xtrem-ui';
import { DateValue, Datetime } from '@sage/xtrem-date-time';
import { ShowCaseProduct } from '@sage/xtrem-show-case-api';
import { fields } from '../menu-items/fields';

@ui.decorators.page<RelativeDate>({
    title: 'Field - Relative date',
    defaultEntry: () => '2',
    node: '@sage/xtrem-show-case/ShowCaseProvider',
    menuItem: fields,
    onLoad() {
        this.field.value = '2023-02-07';
        this.configFieldTitle.value = this.field.title;
        const today = DateValue.today();
        const now = Datetime.now();
        this.today.value = today.toString();
        this.tomorrow.value = today.addDays(1).toString();
        this.yesterday.value = today.addDays(-1).toString();
        this.fourdaysago.value = today.addDays(-4).toString();
        this.twoweeksago.value = today.addDays(-14).toString();
        this.threemonthsago.value = today.addMonths(-3).toString();
        this.fiveyearsago.value = today.addYears(-5).toString();
        this.dateInput.value = today.toString();
        this.dateOutput.value = today.toString();
        this.datetimeInput.value = now.toString();
        this.datetimeOutput.value = now.toString();
        this.card.value = {
            _id: '123',
            icon: 'calendar_today',
            title: 'Date',
            date: today.toString(),
        };
        this.card2.value = {
            _id: '123',
            icon: 'clock',
            title: 'Datetime',
            time: new Date(Date.now()),
        };
    },
})
export class RelativeDate extends ui.Page {
    @ui.decorators.section<RelativeDate>({
        title: 'Relative Date Field',
    })
    section: ui.containers.Section;

    @ui.decorators.block<RelativeDate>({
        parent() {
            return this.section;
        },
        title: 'Field Example',
    })
    fieldBlock: ui.containers.Block;

    @ui.decorators.relativeDateField<RelativeDate>({
        parent() {
            return this.fieldBlock;
        },
        title: 'Field example',
        isTransient: true,
    })
    field: ui.fields.RelativeDate;

    @ui.decorators.cardField<RelativeDate, any>({
        isTransient: true,
        cardDefinition: {
            image: ui.nestedFields.icon({
                bind: 'icon',
                isTransient: true,
            }),
            title: ui.nestedFields.text({
                bind: 'title',
                title: 'Title',
            }),
            line2: ui.nestedFields.relativeDate({
                bind: 'date',
                title: 'Relative date',
            }),
        },
        parent() {
            return this.fieldBlock;
        },
    })
    card: ui.fields.Card;

    @ui.decorators.cardField<RelativeDate, any>({
        isTransient: true,
        cardDefinition: {
            image: ui.nestedFields.icon({
                bind: 'icon',
                isTransient: true,
            }),
            title: ui.nestedFields.text({
                bind: 'title',
                title: 'Title',
            }),
            line2: ui.nestedFields.relativeDate({
                bind: 'time',
                title: 'Relative date',
            }),
        },
        parent() {
            return this.fieldBlock;
        },
    })
    card2: ui.fields.Card;

    @ui.decorators.block<RelativeDate>({
        parent() {
            return this.section;
        },
        title: 'Field Configuration',
    })
    configBlock: ui.containers.Block;

    @ui.decorators.checkboxField<RelativeDate>({
        onChange() {
            this.field.isTitleHidden = this.configFieldTitleHidden.value;
        },
        parent() {
            return this.configBlock;
        },
        title: 'Is Title Hidden?',
        isTransient: true,
    })
    configFieldTitleHidden: ui.fields.Checkbox;

    @ui.decorators.textField<RelativeDate>({
        onChange() {
            this.field.title = this.configFieldTitle.value;
        },
        parent() {
            return this.configBlock;
        },
        title: 'Title',
        width: 'large',
        isTransient: true,
    })
    configFieldTitle: ui.fields.Text;

    @ui.decorators.separatorField<RelativeDate>({
        isFullWidth: true,
        isInvisible: true,
        parent() {
            return this.configBlock;
        },
    })
    configSeperator1: ui.fields.Separator;

    @ui.decorators.checkboxField<RelativeDate>({
        onChange() {
            this.field.isHelperTextHidden = this.configFieldHelperTextHidden.value;
        },
        parent() {
            return this.configBlock;
        },
        title: 'Is Helper Text Hidden?',
        isTransient: true,
    })
    configFieldHelperTextHidden: ui.fields.Checkbox;

    @ui.decorators.textField<RelativeDate>({
        onChange() {
            this.field.helperText = this.configFieldHelperText.value;
        },
        parent() {
            return this.configBlock;
        },
        title: 'Helper Text',
        width: 'large',
        isTransient: true,
    })
    configFieldHelperText: ui.fields.Text;

    @ui.decorators.separatorField<RelativeDate>({
        isFullWidth: true,
        isInvisible: true,
        parent() {
            return this.configBlock;
        },
    })
    configSeperator2: ui.fields.Separator;

    @ui.decorators.checkboxField<RelativeDate>({
        onChange() {
            this.field.isHidden = this.configFieldHidden.value;
        },
        parent() {
            return this.configBlock;
        },
        title: 'Is Hidden?',
        isTransient: true,
    })
    configFieldHidden: ui.fields.Checkbox;

    @ui.decorators.block<RelativeDate>({
        parent() {
            return this.section;
        },
        title: 'Test',
    })
    fieldBlock2: ui.containers.Block;

    @ui.decorators.block<RelativeDate>({
        parent() {
            return this.section;
        },
        title: 'Significant distances',
    })
    fieldBlock3: ui.containers.Block;

    @ui.decorators.dateField<RelativeDate>({
        parent() {
            return this.fieldBlock2;
        },
        onChange() {
            this.dateOutput.value = this.dateInput.value;
        },
        title: 'Date',
        isTransient: true,
    })
    dateInput: ui.fields.Date;

    @ui.decorators.relativeDateField<RelativeDate>({
        parent() {
            return this.fieldBlock2;
        },
        isTitleHidden: true,
        isTransient: true,
    })
    dateOutput: ui.fields.RelativeDate;

    @ui.decorators.textField<RelativeDate>({
        parent() {
            return this.fieldBlock2;
        },
        onChange() {
            this.datetimeOutput.value = this.datetimeInput.value;
        },
        validation() {
            // check is a good iso datetime string
            return undefined;
        },
        title: 'Datetime',
        width: 'medium',
        isTransient: true,
    })
    datetimeInput: ui.fields.Text;

    @ui.decorators.relativeDateField<RelativeDate>({
        parent() {
            return this.fieldBlock2;
        },
        isTitleHidden: true,
        isTransient: true,
    })
    datetimeOutput: ui.fields.RelativeDate;

    @ui.decorators.relativeDateField<RelativeDate>({
        parent() {
            return this.fieldBlock3;
        },
        title() {
            return this.tomorrow.value;
        },
        isTransient: true,
    })
    tomorrow: ui.fields.RelativeDate;

    @ui.decorators.relativeDateField<RelativeDate>({
        parent() {
            return this.fieldBlock3;
        },
        title() {
            return this.today.value;
        },
        isTransient: true,
    })
    today: ui.fields.RelativeDate;

    @ui.decorators.relativeDateField<RelativeDate>({
        parent() {
            return this.fieldBlock3;
        },
        title() {
            return this.yesterday.value;
        },
        isTransient: true,
    })
    yesterday: ui.fields.RelativeDate;

    @ui.decorators.relativeDateField<RelativeDate>({
        parent() {
            return this.fieldBlock3;
        },
        title() {
            return this.fourdaysago.value;
        },
        isTransient: true,
    })
    fourdaysago: ui.fields.RelativeDate;

    @ui.decorators.relativeDateField<RelativeDate>({
        parent() {
            return this.fieldBlock3;
        },
        title() {
            return this.twoweeksago.value;
        },
        isTransient: true,
    })
    twoweeksago: ui.fields.RelativeDate;

    @ui.decorators.relativeDateField<RelativeDate>({
        parent() {
            return this.fieldBlock3;
        },
        title() {
            return this.threemonthsago.value;
        },
        isTransient: true,
    })
    threemonthsago: ui.fields.RelativeDate;

    @ui.decorators.relativeDateField<RelativeDate>({
        parent() {
            return this.fieldBlock3;
        },
        title() {
            return this.fiveyearsago.value;
        },
        isTransient: true,
    })
    fiveyearsago: ui.fields.RelativeDate;

    @ui.decorators.block<RelativeDate>({
        parent() {
            return this.section;
        },
        title: 'Table',
    })
    fieldBlock4: ui.containers.Block;

    @ui.decorators.tableField<RelativeDate, ShowCaseProduct>({
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        bind: 'products',
        canResizeColumns: true,
        canSelect: false,
        headerBusinessActions() {
            return [this.$standardSaveAction];
        },
        filter: () => {
            return { _id: '250' };
        },
        columns: [
            ui.nestedFields.text({
                bind: 'product',
                title: 'Product',
            }),
            ui.nestedFields.text({
                bind: 'description',
                title: 'Description',
            }),
            ui.nestedFields.relativeDate<RelativeDate, ShowCaseProduct>({
                bind: 'releaseDate',
                title: 'Release date',
            }),
            ui.nestedFields.relativeDate<RelativeDate, ShowCaseProduct>({
                bind: '_updateStamp',
                title: 'Last update',
            }),
        ],
        parent() {
            return this.fieldBlock4;
        },
    })
    table: ui.fields.Table<ShowCaseProduct>;

    @ui.decorators.block<RelativeDate>({
        parent() {
            return this.section;
        },
        title: 'Pod',
    })
    fieldBlock5: ui.containers.Block;

    @ui.decorators.podCollectionField<RelativeDate, ShowCaseProduct>({
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        bind: 'products',
        filter: () => {
            return { _id: '250' };
        },
        columns: [
            ui.nestedFields.text({
                bind: 'product',
                title: 'Product',
            }),
            ui.nestedFields.text({
                bind: '_updateStamp',
                title: 'updateStamp',
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                bind: 'description',
                title: 'Description',
            }),
            ui.nestedFields.relativeDate({
                bind: '_updateStamp',
                title: 'Last update',
            }),
        ],
        parent() {
            return this.fieldBlock5;
        },
    })
    pod: ui.fields.PodCollection<ShowCaseProduct>;
}
