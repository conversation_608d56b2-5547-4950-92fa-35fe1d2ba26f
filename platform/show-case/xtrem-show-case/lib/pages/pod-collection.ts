import { GraphA<PERSON>, ShowCaseInvoiceLine, ShowCaseProduct } from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import { podCollection } from '../menu-items/_index';

@ui.decorators.page<PodCollection>({
    authorizationCode: 'BSCFLDS',
    menuItem: podCollection,
    title: 'Pod Collection',
    defaultEntry: () => '2',
    module: 'show-case',
    category: 'SHOWCASE',
    businessActions() {
        return [this.$standardCancelAction, this.$standardSaveAction];
    },
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.text({ bind: '_id', prefix: 'INV' }),
            // titleLine2: ui.nestedFields.numeric({ bind: 'h', prefix: 'INV' }),
            line2: ui.nestedFields.date({ bind: 'purchaseDate' }),
            titleRight: ui.nestedFields.count({ bind: 'lines', postfix: 'it' }),
        },
    },
    node: '@sage/xtrem-show-case/ShowCaseInvoice',
})
export class PodCollection extends ui.Page<GraphApi> {
    @ui.decorators.section<PodCollection>({})
    section: ui.containers.Section;

    @ui.decorators.block<PodCollection>({
        parent() {
            return this.section;
        },
        title: 'Field example',
    })
    fieldBlock: ui.containers.Block;

    @ui.decorators.podCollectionField<PodCollection, ShowCaseInvoiceLine>({
        node: '@sage/xtrem-show-case/ShowCaseInvoiceLine',
        orderBy: {
            orderQuantity: 1,
        },
        parent() {
            return this.fieldBlock;
        },
        onClick() {},
        onChange() {},
        onRecordAdded() {
            this.$.showToast('A new pod was added', { type: 'success' });
        },
        fetchesDefaults: true,
        recordTitle(value: any, rowData: ShowCaseInvoiceLine | null): any {
            return rowData?.product?.product && rowData?.product?._id
                ? `${rowData.product.product} ${rowData?.product?._id}`
                : 'Unknown';
        },
        headerLabel: ui.nestedFields.label<PodCollection, ShowCaseInvoiceLine>({
            bind: 'discountType',
            backgroundColor(v: string) {
                return v === 'newProductPromotion'
                    ? ui.tokens.colorsSemanticPositive500
                    : ui.tokens.colorsActionMajor500;
            },
            borderColor(v: string) {
                return v === 'newProductPromotion'
                    ? ui.tokens.colorsSemanticPositive500
                    : ui.tokens.colorsActionMajor500;
            },
            color() {
                return ui.tokens.colorsYang100;
            },
            map(v) {
                return ui.localizeEnumMember('@sage/xtrem-show-case/ShowCaseDiscountType', v);
            },
        }),
        dropdownActions: [
            {
                icon: 'add',
                title: 'Add',
                isDisabled() {
                    return false;
                },
                onClick(rowId: any, data: any) {
                    this.$.dialog.message(
                        'info',
                        'Add Row Action was clicked',
                        `Product: ${data.product?.product} Row: ${rowId}`,
                    );
                },
            },
            {
                icon: 'locked',
                title: 'Maybe disabled',
                isDisabled(id: any, row: any) {
                    return row.orderQuantity < 10;
                },
                onClick(rowId: any, data: any) {
                    if (data.product) {
                        //  this.$.showToast(data.product.product, { type: 'info' });
                    }
                },
            },
            {
                icon: 'minus',
                title: 'Remove',
                onClick(rowId: any) {
                    this.lines.removeRecord(rowId);
                },
            },
            {
                title: 'Action no icon',
                isHidden(id: any, row: any) {
                    return row.orderQuantity < 10;
                },
                onClick() {
                    //     this.$.showToast(data.product, { type: 'info' });
                },
            },
        ],
        columns: [
            ui.nestedFields.reference<PodCollection, ShowCaseInvoiceLine, ShowCaseProduct>({
                bind: 'product',
                title: 'Product',
                isAutoSelectEnabled: true,
                minLookupCharacters: 0,
                node: '@sage/xtrem-show-case/ShowCaseProduct',
                valueField: 'product',
                fetchesDefaults: true,
                isFullWidth: true,
            }),
            ui.nestedFields.numeric({
                bind: 'orderQuantity',
                title: 'Ordered Quantity',
                scale: 0,
                fetchesDefaults: true,
                isFullWidth: true,
            }),
            ui.nestedFields.numeric({ bind: 'netPrice', title: 'Net Price', scale: 2, prefix: '€', width: 'small' }),
            ui.nestedFields.select({
                access: {
                    node: '@sage/xtrem-show-case/ShowCaseProduct',
                    bind: 'discount',
                },
                bind: 'discountType',
                title: 'Discount Type',
                optionType: '@sage/xtrem-show-case/ShowCaseDiscountType',
            }),
            ui.nestedFields.textArea({
                bind: 'comments',
                title: 'Comments',
                isFullWidth: true,
                rows: 5,
            }),
        ],
        validation(value: ShowCaseInvoiceLine[]) {
            if (value.length === 0) {
                return 'At least one line is required';
            }
            return undefined;
        },
    })
    lines: ui.fields.PodCollection;

    @ui.decorators.block<PodCollection>({
        parent() {
            return this.section;
        },
        title: 'Configuration',
    })
    configurationBlock: ui.containers.Block;

    @ui.decorators.checkboxField<PodCollection>({
        isTransient: true,
        parent() {
            return this.configurationBlock;
        },
        title: 'Can remove item',
        onChange() {
            this.lines.canRemoveRecord = this.canRemoveRecord.value;
        },
    })
    canRemoveRecord: ui.fields.Checkbox;

    @ui.decorators.checkboxField<PodCollection>({
        isTransient: true,
        parent() {
            return this.configurationBlock;
        },
        title: 'Can add item',
        onChange() {
            this.lines.canAddRecord = this.canAddRecord.value;
        },
    })
    canAddRecord: ui.fields.Checkbox;

    @ui.decorators.checkboxField<PodCollection>({
        isTransient: true,
        parent() {
            return this.configurationBlock;
        },
        title: 'Can select',
        onChange() {
            this.lines.canSelect = this.canSelect.value;
        },
    })
    canSelect: ui.fields.Checkbox;

    @ui.decorators.checkboxField<PodCollection>({
        isTransient: true,
        parent() {
            return this.configurationBlock;
        },
        title: 'Is Disabled',
        onChange() {
            this.lines.isDisabled = this.isDisabled.value;
        },
    })
    isDisabled: ui.fields.Checkbox;

    @ui.decorators.checkboxField<PodCollection>({
        isTransient: true,
        parent() {
            return this.configurationBlock;
        },
        title: 'Is Read-only',
        onChange() {
            this.lines.isReadOnly = this.isReadOnly.value;
        },
    })
    isReadOnly: ui.fields.Checkbox;

    @ui.decorators.separatorField<PodCollection>({
        parent() {
            return this.configurationBlock;
        },
        isFullWidth: true,
    })
    separator1: ui.fields.Separator;

    @ui.decorators.textField<PodCollection>({
        isTransient: true,
        parent() {
            return this.configurationBlock;
        },
        title: 'Title',
        onChange() {
            this.lines.title = this.title.value;
        },
    })
    title: ui.fields.Text;

    @ui.decorators.textField<PodCollection>({
        isTransient: true,
        parent() {
            return this.configurationBlock;
        },
        title: 'Helper Text',
        onChange() {
            this.lines.helperText = this.helperText.value;
        },
    })
    helperText: ui.fields.Text;

    @ui.decorators.textField<PodCollection>({
        isTransient: true,
        parent() {
            return this.configurationBlock;
        },
        title: 'Add button placeholder text',
        onChange() {
            this.lines.addButtonText = this.newItemText.value;
        },
    })
    newItemText: ui.fields.Text;

    @ui.decorators.textField<PodCollection>({
        isTransient: true,
        parent() {
            return this.configurationBlock;
        },
        title: 'Remove dialog title',
        onChange() {
            this.lines.removeDialogTitle = this.removeDialogTitle.value;
        },
    })
    removeDialogTitle: ui.fields.Text;

    @ui.decorators.textField<PodCollection>({
        isTransient: true,
        parent() {
            return this.configurationBlock;
        },
        title: 'Remove dialog text',
        onChange() {
            this.lines.removeDialogText = this.removeDialogText.value;
        },
    })
    removeDialogText: ui.fields.Text;

    @ui.decorators.block<PodCollection>({
        parent() {
            return this.section;
        },
        title: 'Additional examples',
    })
    additionalBlock: ui.containers.Block;

    @ui.decorators.podCollectionField<PodCollection, ShowCaseInvoiceLine>({
        node: '@sage/xtrem-show-case/ShowCaseInvoiceLine',
        bind: 'lines',
        title: 'Info and warning state',
        canSelect: false,
        pageSize: 10,
        columns: [
            ui.nestedFields.reference<PodCollection, ShowCaseInvoiceLine, ShowCaseProduct>({
                bind: 'product',
                title: 'Product',
                isAutoSelectEnabled: true,
                minLookupCharacters: 0,
                node: '@sage/xtrem-show-case/ShowCaseProduct',
                valueField: 'product',
                fetchesDefaults: true,
                isFullWidth: true,
            }),
            ui.nestedFields.numeric({
                bind: 'orderQuantity',
                title: 'Ordered Quantity',
                scale: 0,
                fetchesDefaults: true,
                isFullWidth: true,
            }),
        ],
        parent() {
            return this.additionalBlock;
        },
    })
    infoAndWarningExample: ui.fields.PodCollection<ShowCaseProduct>;

    @ui.decorators.textField<PodCollection>({
        isTransient: true,
        parent() {
            return this.additionalBlock;
        },
        title: 'Warning message',
        onChange() {
            this.infoAndWarningExample.warningMessage = this.warningMessage.value || null;
        },
    })
    warningMessage: ui.fields.Text;

    @ui.decorators.textField<PodCollection>({
        isTransient: true,
        parent() {
            return this.additionalBlock;
        },
        title: 'Info message',
        onChange() {
            this.infoAndWarningExample.infoMessage = this.infoMessage.value || null;
        },
    })
    infoMessage: ui.fields.Text;
}
