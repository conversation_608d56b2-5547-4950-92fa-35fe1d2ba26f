import { ShowCaseProvider, ShowCaseProduct } from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import { fields } from '../menu-items/fields';

@ui.decorators.page<TableSummary, ShowCaseProvider>({
    authorizationCode: 'BSCFLDS',
    defaultEntry: () => '2',
    module: 'show-case',
    category: 'SHOWCASE',
    node: '@sage/xtrem-show-case/ShowCaseProvider',
    title: 'Field - Table Summary',
    menuItem: fields,
    mode: 'tabs',
    onLoad() {
        this.smallTransientExample.addOrUpdateRecordValue({
            costCategory: 'Machine cost',
            value1: 12.34,
            value2: 42.04,
        });
        this.smallTransientExample.addOrUpdateRecordValue({
            costCategory: 'Labour cost',
            value1: 43.34,
            value2: 90.04,
        });
        this.smallTransientExample.addOrUpdateRecordValue({
            costCategory: 'Tool cost',
            value1: 54.34,
            value2: 15.04,
        });
        this.smallTransientExample.addOrUpdateRecordValue({
            costCategory: 'Total cost',
            value1: 34.34,
            value2: 324.94,
        });
    },
})
export class TableSummary extends ui.Page {
    @ui.decorators.section<TableSummary>({
        title: 'Bound examples',
    })
    section: ui.containers.Section;

    @ui.decorators.block<TableSummary>({
        parent() {
            return this.section;
        },
        title: 'Field example',
    })
    fieldBlock: ui.containers.Block;

    updateRow(_id: any, rowData: ui.PartialNodeWithId<ShowCaseProduct>) {
        const updatedAmount = rowData.qty * parseFloat(rowData.listPrice);
        rowData.amount = String(updatedAmount);
        this.field.setRecordValue(rowData);
    }

    @ui.decorators.tableSummaryField<TableSummary, ShowCaseProduct>({
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        bind: 'products',
        title: 'Summary Table Title',
        helperText: 'Helper text here.',
        columns: [
            ui.nestedFields.text({
                bind: '_id',
                title: 'Id',
            }),
            ui.nestedFields.text({
                bind: 'product',
                title: 'Product',
            }),
            ui.nestedFields.numeric({
                bind: 'qty',
                title: 'Quantity',
            }),
            ui.nestedFields.label({
                bind: 'category',
                isTransient: true,
                map(_fieldValue, rowData) {
                    return rowData.qty > 10 ? 'High' : 'Low';
                },
                title: 'Indicator',
            }),
            ui.nestedFields.reference({
                bind: 'provider',
                title: 'Provider',
                valueField: 'textField',
                node: '@sage/xtrem-show-case/ShowCaseProvider',
            }),
        ],
        orderBy: {
            product: 1,
        },
        parent() {
            return this.fieldBlock;
        },
        emptyStateText: 'Custom no data to display message',
    })
    field: ui.fields.TableSummary<ShowCaseProduct>;

    @ui.decorators.block<TableSummary>({
        parent() {
            return this.section;
        },
        title: 'Configuration',
    })
    configurationBlock: ui.containers.Block;

    @ui.decorators.textField<TableSummary>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Title',
        isTransient: true,
        onChange() {
            this.field.title = this.title.value;
        },
    })
    title: ui.fields.Text;

    @ui.decorators.textField<TableSummary>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Helper text',
        isTransient: true,
        onChange() {
            this.field.helperText = this.helperText.value;
        },
    })
    helperText: ui.fields.Text;

    @ui.decorators.checkboxField<TableSummary>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is disabled',
        isTransient: true,
        onChange() {
            this.field.isDisabled = this.isDisabled.value;
        },
    })
    isDisabled: ui.fields.Checkbox;

    @ui.decorators.checkboxField<TableSummary>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is helper text hidden',
        isTransient: true,
        onChange() {
            this.field.isHelperTextHidden = this.isHelperTextHidden.value;
        },
    })
    isHelperTextHidden: ui.fields.Checkbox;

    @ui.decorators.checkboxField<TableSummary>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is hidden',
        isTransient: true,
        onChange() {
            this.field.isHidden = this.isHidden.value;
        },
    })
    isHidden: ui.fields.Checkbox;

    @ui.decorators.checkboxField<TableSummary>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is title hidden',
        isTransient: true,
        onChange() {
            this.field.isTitleHidden = this.isTitleHidden.value;
        },
    })
    isTitleHidden: ui.fields.Checkbox;

    @ui.decorators.buttonField<TableSummary>({
        parent() {
            return this.configurationBlock;
        },
        isTransient: true,
        async onClick() {
            await this.field.refresh();
        },
        map() {
            return 'Refresh Table';
        },
    })
    refreshTable: ui.fields.Button;

    @ui.decorators.buttonField<TableSummary>({
        isTransient: true,
        map() {
            return 'Redraw';
        },
        parent() {
            return this.configurationBlock;
        },
        onClick() {
            this.field.redraw();
        },
    })
    redrawButton: ui.fields.Button;

    @ui.decorators.block<TableSummary>({
        parent() {
            return this.section;
        },
        title: 'Remove a line',
    })
    removeALineBlock: ui.containers.Block;

    @ui.decorators.numericField<TableSummary>({
        isTransient: true,
        parent() {
            return this.removeALineBlock;
        },
        title: 'Record ID',
    })
    removeALineId: ui.fields.Numeric;

    @ui.decorators.buttonField<TableSummary>({
        isTransient: true,
        parent() {
            return this.removeALineBlock;
        },
        map() {
            return 'Remove';
        },
        onClick() {
            this.field.removeRecord(String(this.removeALineId.value));
        },
    })
    removeALineButton: ui.fields.Button;

    @ui.decorators.block<TableSummary>({
        parent() {
            return this.section;
        },
        title: 'Modify a line',
    })
    modifyALineBlock: ui.containers.Block;

    @ui.decorators.numericField<TableSummary>({
        isTransient: true,
        parent() {
            return this.modifyALineBlock;
        },
        title: 'Record ID',
    })
    modifyALineId: ui.fields.Numeric;

    @ui.decorators.textField<TableSummary>({
        isTransient: true,
        parent() {
            return this.modifyALineBlock;
        },
        title: 'Column id',
    })
    modifyALineColumnId: ui.fields.Text;

    @ui.decorators.textField<TableSummary>({
        isTransient: true,
        parent() {
            return this.modifyALineBlock;
        },
        title: 'Value',
    })
    modifyALineValue: ui.fields.Text;

    @ui.decorators.buttonField<TableSummary>({
        isTransient: true,
        parent() {
            return this.modifyALineBlock;
        },
        map() {
            return 'Modify';
        },
        onClick() {
            const id = String(this.modifyALineId.value);
            const record = this.field.getRecordValue(id);
            this.field.addOrUpdateRecordValue({
                ...record,
                [this.modifyALineColumnId.value]: this.modifyALineValue.value,
            });
        },
    })
    modifyALineButton: ui.fields.Button;

    @ui.decorators.block<TableSummary>({
        parent() {
            return this.section;
        },
        title: 'Additional examples',
    })
    additionalBlock: ui.containers.Block;

    @ui.decorators.tableSummaryField<TableSummary, ShowCaseProduct>({
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        bind: 'products',
        title: 'Restricted result set',
        columns: [
            ui.nestedFields.text({
                bind: '_id',
                title: 'Id',
            }),
            ui.nestedFields.text({
                bind: 'product',
                title: 'Product',
            }),
            ui.nestedFields.numeric({
                bind: 'qty',
                title: 'Quantity',
            }),
        ],
        parent() {
            return this.additionalBlock;
        },
    })
    restrictedResultSet: ui.fields.TableSummary<ShowCaseProduct>;

    @ui.decorators.textField<TableSummary>({
        isTransient: true,
        parent() {
            return this.additionalBlock;
        },
        title: 'Result set restriction (on product name)',
        onChange() {
            const filter = this.resultSetRestriction.value
                ? {
                      product: { _regex: this.resultSetRestriction.value, _options: 'i' },
                  }
                : undefined;

            this.restrictedResultSet.filter = filter;
        },
    })
    resultSetRestriction: ui.fields.Text;

    @ui.decorators.section<TableSummary>({
        title: 'Transient examples',
    })
    transientSection: ui.containers.Section;

    @ui.decorators.block<TableSummary>({
        parent() {
            return this.transientSection;
        },
        title: 'Rendered into a small block',
        width: 'small',
    })
    smallBlock: ui.containers.Block;

    @ui.decorators.tableSummaryField<TableSummary>({
        isTransient: true,
        isTitleHidden: true,
        columns: [
            ui.nestedFields.text({
                bind: 'costCategory',
                title: 'Cost',
            }),
            ui.nestedFields.numeric({
                bind: 'value1',
                title: 'Value 1',
                scale: 2,
            }),
            ui.nestedFields.numeric({
                bind: 'value2',
                title: 'Value 2',
                scale: 2,
            }),
        ],
        parent() {
            return this.smallBlock;
        },
    })
    smallTransientExample: ui.fields.TableSummary;

    @ui.decorators.section<TableSummary>({
        title: 'Empty Example',
    })
    emptySection: ui.containers.Section;

    @ui.decorators.block<TableSummary>({
        parent() {
            return this.emptySection;
        },
    })
    emptyBlock: ui.containers.Block;

    @ui.decorators.tableSummaryField<TableSummary>({
        columns: [],
        isTransient: true,
        parent() {
            return this.emptyBlock;
        },
        title: 'Empty Table Summary',
    })
    emptyTable: ui.fields.TableSummary;

    @ui.decorators.block<TableSummary>({
        parent() {
            return this.emptySection;
        },
    })
    customEmptyBlock: ui.containers.Block;

    @ui.decorators.tableSummaryField<TableSummary>({
        columns: [],
        emptyStateText: 'Y U have no items?',
        isTransient: true,
        parent() {
            return this.customEmptyBlock;
        },
        title: 'Custom Empty Table Summary Message',
    })
    customEmptyTable: ui.fields.TableSummary;

    @ui.decorators.tableSummaryField<TableSummary, ShowCaseProduct>({
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        bind: 'products',
        title: 'Summary Table with section as parent',
        helperText: 'Helper text here.',
        columns: [
            ui.nestedFields.text({
                bind: '_id',
                title: 'Id',
            }),
            ui.nestedFields.text({
                bind: 'product',
                title: 'Product',
            }),
            ui.nestedFields.numeric({
                bind: 'qty',
                title: 'Quantity',
            }),
            ui.nestedFields.label({
                bind: 'category',
                isTransient: true,
                map(_fieldValue, rowData) {
                    return rowData.qty > 10 ? 'High' : 'Low';
                },
                title: 'Indicator',
            }),
            ui.nestedFields.reference({
                bind: 'provider',
                title: 'Provider',
                valueField: 'textField',
                node: '@sage/xtrem-show-case/ShowCaseProvider',
            }),
        ],
        orderBy: {
            product: 1,
        },
        parent() {
            return this.section;
        },
        emptyStateText: 'Custom no data to display message',
    })
    fieldChildOfSection: ui.fields.TableSummary<ShowCaseProduct>;
}
