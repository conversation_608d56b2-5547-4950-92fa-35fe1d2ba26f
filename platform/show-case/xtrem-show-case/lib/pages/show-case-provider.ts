import {
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    ShowCaseProduct,
    ShowCaseProviderAddress,
    ShowCaseProviderAddressBinding,
    ShowCaseProvider as ShowCaseProviderNode,
} from '@sage/xtrem-show-case-api';
import { setOrderOfPageHeaderDropDownActions } from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import * as ui from '@sage/xtrem-ui';
import { applicationPages } from '../menu-items/application-pages';

@ui.decorators.page<ShowCaseProvider, ShowCaseProviderNode>({
    authorizationCode: 'SHCPRVD',
    category: 'SHOWCASE',
    menuItem: applicationPages,
    objectTypeSingular: 'Provider',
    objectTypePlural: 'Providers',
    idField() {
        return this._id;
    },
    businessActions() {
        return [this.toggleNavigationPanel, this.update];
    },
    headerQuickActions() {
        return [this.$standardDuplicateAction];
    },
    headerDropDownActions() {
        return setOrderOfPageHeaderDropDownActions({
            remove: [this.delete],
            dropDownBusinessActions: [
                this.$standardOpenRecordHistoryAction,
                this.$standardOpenCustomizationPageWizardAction,
                this.update,
                this.create,
            ],
        });
    },
    detailPanel() {
        return {
            header: this.detailPanelHeaderDetailSection,
            sections: [this.detailPanelDetailSection],
        };
    },
    module: 'show-case',
    navigationPanel: {
        bulkActions: [
            {
                mutation: 'modify',
                title: 'Modify',
                buttonType: 'primary',
                isDestructive: false,
                id: 'modify',
            },
            {
                mutation: 'export',
                title: 'Export',
                buttonType: 'secondary',
                icon: 'file_excel',
                isDestructive: false,
                id: 'export',
            },
            {
                mutation: 'print',
                title: 'Print',
                buttonType: 'tertiary',
                icon: 'print',
                isDestructive: false,
                id: 'print',
            },
            {
                mutation: 'remove',
                title: 'Delete',
                buttonType: 'tertiary',
                icon: 'delete',
                isDestructive: true,
                id: 'delete',
            },
        ],
        listItem: {
            title: ui.nestedFields.text({ bind: 'textField', title: 'Name' }),
            line2: ui.nestedFields.text({ bind: '_id', title: 'ID' }),
            line3: ui.nestedFields.date({ bind: 'dateField', title: 'Added on' }),
            image: ui.nestedFields.image({ bind: 'logo', title: 'Logo', placeholderMode: 'Initials' }),
        },
    },
    node: '@sage/xtrem-show-case/ShowCaseProvider',
    title: 'ShowCase - Provider',
    priority: 200,
    onLoad() {
        this.$.detailPanel.isHidden = true;
    },
})
export class ShowCaseProvider extends ui.Page<GraphApi> {
    @ui.decorators.section<ShowCaseProvider>({})
    section: ui.containers.Section;

    @ui.decorators.block<ShowCaseProvider>({
        parent() {
            return this.section;
        },
    })
    block: ui.containers.Block;

    @ui.decorators.textField<ShowCaseProvider>({
        parent() {
            return this.block;
        },
        title: 'Id',
    })
    _id: ui.fields.Text;

    @ui.decorators.textField<ShowCaseProvider>({
        parent() {
            return this.block;
        },
        title: 'Text',
    })
    textField: ui.fields.Text;

    @ui.decorators.dateField<ShowCaseProvider>({
        parent() {
            return this.block;
        },
        title: 'Date',
    })
    dateField: ui.fields.Date;

    @ui.decorators.checkboxField<ShowCaseProvider>({
        parent() {
            return this.block;
        },
        title: 'Checkbox',
    })
    booleanField: ui.fields.Checkbox;

    @ui.decorators.numericField<ShowCaseProvider>({
        parent() {
            return this.block;
        },
        title: 'Integer',
        scale: 0,
    })
    integerField: ui.fields.Numeric;

    @ui.decorators.numericField<ShowCaseProvider>({
        parent() {
            return this.block;
        },
        async onChange() {
            await this.products.revalidate(record => {
                return record.qty >= this.minQuantity.value;
            });
        },
        title: 'Minimum Quantity',
        scale: 0,
    })
    minQuantity: ui.fields.Numeric;

    @ui.decorators.numericField<ShowCaseProvider>({
        bind: 'decimalField',
        parent() {
            return this.block;
        },
        title: 'Decimal',
        scale: 2,
    })
    testingTheBindPropertyInMutations: ui.fields.Numeric;

    @ui.decorators.tableField<ShowCaseProvider, ShowCaseProduct>({
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        mainField: 'description',
        parent() {
            return this.block;
        },
        title: 'Products',
        onRowClick() {
            ui.console.log('clicked');
        },
        orderBy: { _id: 1 },
        columns: [
            ui.nestedFields.text({ bind: '_id', isReadOnly: true, title: 'Id' }),
            ui.nestedFields.link({
                bind: 'product',
                title: 'Product',
                onClick(_id) {
                    this.$.router.goTo('@sage/xtrem-show-case/ShowCaseProduct', { _id });
                },
            }),
            ui.nestedFields.reference<ShowCaseProvider, ShowCaseProduct, ShowCaseProviderAddress>({
                bind: 'originAddress',
                valueField: 'city',
                title: 'City',
                node: '@sage/xtrem-show-case/ShowCaseProviderAddress',
                tunnelPage: '@sage/xtrem-show-case/ShowCaseProviderAddress',
            }),
            ui.nestedFields.text({ bind: 'description', title: 'Description' }),
            ui.nestedFields.checkbox({ bind: 'hotProduct', isReadOnly: true, title: 'Hot' }),
            ui.nestedFields.select({ bind: 'category', isReadOnly: true, title: 'Category' }),
            ui.nestedFields.numeric({ bind: 'total', title: 'Net Price', scale: 2, min: 0, isDisabled: false }),
            ui.nestedFields.numeric({ bind: 'qty', title: 'Quantity', scale: 2 }),
            ui.nestedFields.numeric({ bind: 'amount', title: 'Amount', scale: 2 }),
        ],
        dropdownActions: [
            {
                title: 'Open in Detail panel',
                icon: 'play',
                onClick(rowId: any, data: any) {
                    this.$.detailPanel.isHidden = false;
                    this.productCard.value = data;
                    this.detailPanelHeaderDetailSection.title = data.product;
                    this.dialogGridRowBlock.selectedRecordId = data._id;
                },
            },
            ui.menuSeparator(),
            {
                icon: 'bin',
                title: 'Remove',
                isDestructive: true,
                async onClick(rowId: any, data: any) {
                    await this.$.graph.delete({
                        _id: data._id,
                        nodeName: '@sage/xtrem-show-case/ShowCaseProduct',
                    });
                    await this.products.refresh();
                },
            },
        ],
    })
    products: ui.fields.Table<ShowCaseProduct>;

    @ui.decorators.podField<ShowCaseProvider, ShowCaseProduct>({
        parent() {
            return this.block;
        },

        node: '@sage/xtrem-show-case/ShowCaseProduct',
        title: 'Flagship Product!',
        columns: [
            ui.nestedFields.text({ bind: 'product', title: 'Product Name' }),
            ui.nestedFields.multiDropdown({ bind: 'entries', title: 'Entries' }),
        ],
    })
    flagshipProduct: ui.fields.Pod;

    @ui.decorators.vitalPodField<ShowCaseProvider, ShowCaseProviderAddressBinding>({
        title: 'Address',
        parent() {
            return this.block;
        },
        columns: [
            ui.nestedFields.text({ bind: 'name', title: 'Name' }),
            ui.nestedFields.text({ bind: 'addressLine1', title: 'Line1' }),
            ui.nestedFields.text({ bind: 'addressLine2', title: 'Line2' }),
            ui.nestedFields.reference({
                bind: 'country',
                title: 'Country',
                node: '@sage/xtrem-show-case/ShowCaseCountry',
                valueField: 'name',
                helperTextField: 'code',
            }),
            ui.nestedFields.text({ bind: 'zip', title: 'zip', isTransientInput: true }),
        ],
        node: '@sage/xtrem-show-case/ShowCaseProviderAddress',
    })
    siteAddress: ui.fields.VitalPod;

    @ui.decorators.section<ShowCaseProvider>({})
    detailPanelHeaderDetailSection: ui.containers.Section;

    @ui.decorators.section<ShowCaseProvider>({})
    detailPanelDetailSection: ui.containers.Section;

    @ui.decorators.cardField<ShowCaseProvider, ShowCaseProduct>({
        isFullWidth: true,
        parent() {
            return this.detailPanelHeaderDetailSection;
        },
        isTransient: true,
        cardDefinition: {
            title: ui.nestedFields.text({ bind: 'description' }),
            titleRight: ui.nestedFields.label({ bind: 'category' }),
            line2: ui.nestedFields.numeric({ bind: 'amount', scale: 2 }),
            image: ui.nestedFields.image({ bind: 'imageField' }),
        },
    })
    productCard: ui.fields.Card;

    @ui.decorators.gridRowBlock<ShowCaseProvider>({
        parent() {
            return this.detailPanelDetailSection;
        },
        isTitleHidden: true,
        boundTo() {
            return this.products;
        },
    })
    dialogGridRowBlock: ui.containers.GridRowBlock;

    @ui.decorators.pageAction<ShowCaseProvider>({
        title: 'Custom Create',
        async onClick() {
            await this.$.graph.create();
            this.$.dialog.message('info', 'Mutation Create', `Created entry: ${this._id.value}`, {
                fullScreen: false,
                rightAligned: false,
                acceptButton: {
                    isDisabled: false,
                    isHidden: false,
                    text: 'OK',
                },
            });
        },
    })
    create: ui.PageAction;

    @ui.decorators.pageAction<ShowCaseProvider>({
        title: 'Save',
        async onClick() {
            await this.$standardSaveAction.execute(true);
            if (await this.$.page.isValid) {
                this.$.dialog.message('info', 'Mutation Update', `Updated entry: ${this._id.value}`, {
                    fullScreen: false,
                    rightAligned: false,
                    acceptButton: {
                        isDisabled: false,
                        isHidden: false,
                        text: 'OK',
                    },
                });
            }
        },
    })
    update: ui.PageAction;

    @ui.decorators.pageAction<ShowCaseProvider>({
        title: 'Delete',
        icon: 'bin',
        isDestructive: true,
        async onClick() {
            await this.$.graph.delete();
            this.$.router.goTo('@sage/xtrem-show-case/BoundPage');
        },
    })
    delete: ui.PageAction;

    @ui.decorators.pageAction<ShowCaseProvider>({
        title: 'Toggle Navigation Panel',
        async onClick() {
            this.$.isNavigationPanelHidden = !this.$.isNavigationPanelHidden;
        },
    })
    toggleNavigationPanel: ui.PageAction;

    @ui.decorators.buttonField<ShowCaseProvider>({
        isTransient: true,
        map() {
            return 'Hide Description Column';
        },
        onClick() {
            this.products.hideColumn('description');
        },
        parent() {
            return this.block;
        },
    })
    hideDescriptionButton: ui.fields.Button;
}
