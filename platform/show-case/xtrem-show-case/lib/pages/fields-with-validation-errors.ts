import { G<PERSON>h<PERSON><PERSON>, ShowCaseProduct, ShowCaseProvider } from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import { validation } from '../menu-items/validation';

@ui.decorators.page<FieldsWithValidationErrors, ShowCaseProduct>({
    authorizationCode: 'FLDVLDTERR',
    businessActions() {
        return [this.validate];
    },
    category: 'SHOWCASE',
    isTransient: true,
    module: 'show-case',
    node: '@sage/xtrem-show-case/ShowCaseProduct',
    title: 'Fields with Validation Errors',
    subtitle: 'Test Scenario',
    menuItem: validation,
})
export class FieldsWithValidationErrors extends ui.Page<GraphApi> {
    @ui.decorators.section<FieldsWithValidationErrors>({})
    section: ui.containers.Section;

    @ui.decorators.block<FieldsWithValidationErrors>({
        parent() {
            return this.section;
        },
        width: 'small',
    })
    blockLeft: ui.containers.Block;

    @ui.decorators.block<FieldsWithValidationErrors>({
        parent() {
            return this.section;
        },
        width: 'small',
    })
    blockRight: ui.containers.Block;

    @ui.decorators.block<FieldsWithValidationErrors>({
        parent() {
            return this.section;
        },
        title: 'This is a spacer block so the footer does not overlap the fields on visual regression tests',
    })
    blockSpacing: ui.containers.Block;

    @ui.decorators.textField<FieldsWithValidationErrors>({
        bind: 'product',
        isFullWidth: true,
        isMandatory: true,
        isTransient: true,
        parent() {
            return this.blockLeft;
        },
        title: 'Text Field',
    })
    text: ui.fields.Text;

    @ui.decorators.textAreaField<FieldsWithValidationErrors>({
        bind: 'description',
        isFullWidth: true,
        isMandatory: true,
        isTransient: true,
        parent() {
            return this.blockLeft;
        },
        title: 'Text Area Field',
    })
    textArea: ui.fields.TextArea;

    @ui.decorators.numericField<FieldsWithValidationErrors>({
        bind: 'qty',
        isFullWidth: true,
        isMandatory: true,
        isTransient: true,
        parent() {
            return this.blockLeft;
        },
        title: 'Numeric Field',
    })
    numeric: ui.fields.Numeric;

    @ui.decorators.dateField<FieldsWithValidationErrors>({
        bind: 'releaseDate',
        isFullWidth: true,
        isMandatory: true,
        isTransient: true,
        parent() {
            return this.blockLeft;
        },
        title: 'Date Field',
    })
    date: ui.fields.Date;

    @ui.decorators.dropdownListField<FieldsWithValidationErrors>({
        bind: 'category',
        isFullWidth: true,
        isMandatory: true,
        isTransient: true,
        optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
        parent() {
            return this.blockRight;
        },
        title: 'Dropdown List Field',
    })
    dropdown: ui.fields.DropdownList;

    @ui.decorators.selectField<FieldsWithValidationErrors>({
        bind: 'category',
        isFullWidth: true,
        isMandatory: true,
        isTransient: true,
        optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
        parent() {
            return this.blockRight;
        },
        title: 'Select Field',
    })
    select: ui.fields.Select;

    @ui.decorators.referenceField<FieldsWithValidationErrors, ShowCaseProvider>({
        bind: 'provider',
        columns: [
            ui.nestedFields.text({ bind: '_id', title: 'Id' }),
            ui.nestedFields.text({ bind: 'textField', title: 'Provider' }),
        ],
        isFullWidth: true,
        isMandatory: true,
        isTransient: true,
        node: '@sage/xtrem-show-case/ShowCaseProvider',
        parent() {
            return this.blockRight;
        },
        title: 'Reference Field',
        valueField: 'textField',
    })
    reference: ui.fields.Reference<ShowCaseProvider>;

    @ui.decorators.filterSelectField<FieldsWithValidationErrors, ShowCaseProvider>({
        bind: 'provider',
        columns: [
            ui.nestedFields.text({ bind: '_id', title: 'Id' }),
            ui.nestedFields.text({ bind: 'textField', title: 'Provider' }),
        ],
        isFullWidth: true,
        isMandatory: true,
        isTransient: true,
        node: '@sage/xtrem-show-case/ShowCaseProvider',
        parent() {
            return this.blockRight;
        },
        title: 'Filter Select Field',
        valueField: 'textField',
    })
    filterSelect: ui.fields.FilterSelect<ShowCaseProvider>;

    @ui.decorators.multiDropdownField<FieldsWithValidationErrors>({
        bind: 'subcategories',
        isFullWidth: true,
        isMandatory: true,
        isTransient: true,
        optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
        parent() {
            return this.blockRight;
        },
        title: 'Multi-Dropdown Field',
    })
    multiDropdown: ui.fields.MultiDropdown;

    @ui.decorators.multiReferenceField<FieldsWithValidationErrors>({
        bind: 'addresses',
        columns: [
            ui.nestedFields.text({ bind: '_id', title: 'Id' }),
            ui.nestedFields.text({ bind: 'name', title: 'Name' }),
        ],
        isFullWidth: true,
        isMandatory: true,
        isTransient: true,
        node: '@sage/xtrem-show-case/ShowCaseProviderAddress',
        parent() {
            return this.blockRight;
        },
        title: 'Multi-Reference Field',
        valueField: 'name',
    })
    multiReference: ui.fields.MultiReference;

    @ui.decorators.pageAction<FieldsWithValidationErrors>({
        onClick() {
            this.$.page.validate();
        },
        title: 'Validate',
    })
    validate: ui.PageAction;
}
