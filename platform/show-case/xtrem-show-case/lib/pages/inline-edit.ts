import { ShowCaseProvider, ShowCaseProduct } from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import * as xtremDateTime from '@sage/xtrem-date-time';
import { tableField } from '../menu-items/table-field';

type Todo = { _id: string; text: string };

@ui.decorators.page<InlineEdit, ShowCaseProvider>({
    authorizationCode: 'BSCFLDS',
    defaultEntry: () => '2',
    mode: 'tabs',
    module: 'show-case',
    category: 'SHOWCASE',
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.text({ bind: 'textField' }),
            titleRight: ui.nestedFields.text({ bind: '_id' }),
        },
    },
    node: '@sage/xtrem-show-case/ShowCaseProvider',
    title: 'Inline Edit',
    businessActions() {
        return [this.$standardCancelAction, this.$standardSaveAction];
    },
    detailPanel() {
        return {
            header: this.detailPanelHeaderSection,
            sections: [this.detailPanelBodySection1],
        };
    },
    menuItem: tableField,
})
export class InlineEdit extends ui.Page {
    @ui.decorators.section<InlineEdit>({})
    detailPanelHeaderSection: ui.containers.Section;

    @ui.decorators.block<InlineEdit>({
        parent() {
            return this.detailPanelHeaderSection;
        },
    })
    detailPanelHeaderBlock: ui.containers.Block;

    @ui.decorators.section<InlineEdit>({
        title: 'info',
    })
    detailPanelBodySection1: ui.containers.Section;

    @ui.decorators.block<InlineEdit>({
        parent() {
            return this.detailPanelBodySection1;
        },
        title: 'First section block',
    })
    detailPanelBlock1: ui.containers.Block;

    @ui.decorators.gridRowBlock<InlineEdit>({
        parent() {
            return this.detailPanelBodySection1;
        },
        title: 'Second section block',
        boundTo() {
            return this.field;
        },
    })
    demoGridRowBlock: ui.containers.GridRowBlock;

    @ui.decorators.section<InlineEdit>({
        title: 'Table field',
    })
    section: ui.containers.Section;

    @ui.decorators.section<InlineEdit>({
        title: 'Another section field',
    })
    section2: ui.containers.Section;

    @ui.decorators.block<InlineEdit>({
        parent() {
            return this.section;
        },
        title: 'Field example',
    })
    fieldBlock: ui.containers.Block;

    @ui.decorators.tableField<InlineEdit, ShowCaseProduct>({
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        bind: 'products',
        title: 'Table title',
        canAddNewLine: true,
        onRowAdded(id, row) {
            this.$.showToast(
                `Row added successfully - id: ${id}, ${Object.keys(row)
                    .sort()
                    .filter(key => key !== 'releaseDate')
                    .map(key => {
                        const value = row[key as keyof typeof row];
                        return `${key}: ${key === 'provider' ? row.provider.textField : value}`;
                    })
                    .join(', ')}`,
            );
        },
        mobileCard: null,
        columns: [
            ui.nestedFields.text({
                bind: '_id',
                title: 'Id',
                isHiddenOnMainField: true,
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                bind: 'product',
                title: 'Product',
                isMandatory: true,
            }),
            ui.nestedFields.text({
                bind: 'description',
                title: 'Description',
                validation(value) {
                    if (value.toLowerCase().includes('invalid')) {
                        return 'Error';
                    }
                    return undefined;
                },
            }),
            ui.nestedFields.numeric({
                bind: 'listPrice',
                title: 'List Price',
                scale: 2,
                isHiddenOnMainField: true,
                fetchesDefaults: true,
            }),
            ui.nestedFields.numeric<InlineEdit, ShowCaseProduct>({
                bind: 'netPrice',
                title: 'Net Price',
                scale: 2,
                min: 4,
                validation(value) {
                    if (value === 5) {
                        return new Promise<string>(resolve => setTimeout(() => resolve('Error'), 5000));
                    }
                    return undefined;
                },
            }),
            ui.nestedFields.checkbox({
                bind: 'hotProduct',
                title: 'Hot',
            }),
            ui.nestedFields.numeric({
                bind: 'qty',
                title: 'Ordered Quantity',
                scale: 0,
            }),
            ui.nestedFields.switch({
                bind: 'hotProduct',
                title: 'Hot (Switch)',
            }),
            ui.nestedFields.numeric({
                bind: 'tax',
                title: 'Tax',
                max: 20,
                prefix: 'T',
                scale: 2,
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.label<InlineEdit, ShowCaseProduct>({
                bind: 'amount',
                title: 'Amount',
                prefix: '$',
            }),
            ui.nestedFields.date<InlineEdit, ShowCaseProduct>({
                bind: 'releaseDate',
                title: 'Date',
                validation(value) {
                    const d = xtremDateTime.date.parse(value);
                    if (d.compare(xtremDateTime.date.make(2020, 1, 1)) < 0) {
                        return 'Too old (must be at least 2020)';
                    }
                    return undefined;
                },
            }),
            ui.nestedFields.select<InlineEdit, ShowCaseProduct>({
                bind: 'category',
                title: 'Category',
                optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
                validation(value) {
                    if (value.toLowerCase().includes('awful')) {
                        return 'Really? :(';
                    }
                    return undefined;
                },
            }),
            ui.nestedFields.reference<InlineEdit, ShowCaseProduct, ShowCaseProvider>({
                bind: 'provider',
                title: 'Provider',
                valueField: 'textField',
                node: '@sage/xtrem-show-case/ShowCaseProvider',
                isMandatory: true,
                minLookupCharacters: 0,
                columns: [
                    ui.nestedFields.text({ bind: '_id', title: 'ID', canFilter: false }),
                    ui.nestedFields.text({ bind: 'textField', title: 'Provider', canFilter: true }),
                    ui.nestedFields.image({ bind: 'logo', title: 'Logo', canFilter: false }),
                ],
                imageField: 'logo',
                validation(value, rowData) {
                    if (value.textField.toLowerCase().includes('amazon') && Number(rowData.tax) > 100) {
                        return "Amazon's tax is too high (max is 100)";
                    }
                    return undefined;
                },
            }),
            ui.nestedFields.numeric({
                bind: 'st',
                title: 'Stock level',
                scale: 0,
            }),
        ],
        dropdownActions: [
            {
                icon: 'add',
                title: 'Add',
                isDisabled() {
                    return false;
                },
                onClick(rowId: any, data: any) {
                    this.$.dialog.message(
                        'info',
                        'Add Row Action was clicked',
                        `Product: ${data.product} Row: ${rowId}`,
                    );
                },
            },
            {
                icon: 'bin',
                title: 'Delete',
                onClick(rowId: any) {
                    this.field.removeRecord(rowId);
                },
            },
        ],
        orderBy: {
            product: 1,
        },
        parent() {
            return this.fieldBlock;
        },
        onRowClick(_id: string) {
            this.$.detailPanel!.isHidden = false;
            this.demoGridRowBlock.selectedRecordId = _id;
        },
    })
    field: ui.fields.Table<ShowCaseProduct>;

    @ui.decorators.tableField<InlineEdit, Todo>({
        isTransient: true,
        title: 'Empty table',
        canAddNewLine: true,
        columns: [
            ui.nestedFields.text({
                bind: '_id',
                title: 'Id',
                isHiddenOnMainField: true,
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                bind: 'text',
                title: 'Text',
                isMandatory: true,
            }),
        ],
        parent() {
            return this.fieldBlock;
        },
    })
    emptyTable: ui.fields.Table<Todo>;

    @ui.decorators.buttonField<InlineEdit>({
        parent() {
            return this.fieldBlock;
        },
        isTransient: true,
        async onClick() {
            await this.field.refresh();
        },
        map() {
            return 'Refresh Table';
        },
    })
    refreshTable: ui.fields.Button;

    @ui.decorators.buttonField<InlineEdit>({
        parent() {
            return this.fieldBlock;
        },
        async onClick() {
            const result = await this.field.validateWithDetails();
            ui.console.log(result);
        },
        isTransient: true,
        map() {
            return 'Validate table';
        },
    })
    validateTable: ui.fields.Button;

    @ui.decorators.numericField<InlineEdit>({
        parent() {
            return this.fieldBlock;
        },
        title: 'Min Quantity',
        scale: 0,
    })
    minQuantity: ui.fields.Numeric;

    @ui.decorators.numericField<InlineEdit>({
        parent() {
            return this.fieldBlock;
        },
        isMandatory: true,
        title: 'Integer',
        scale: 0,
    })
    integerField: ui.fields.Numeric;
}
