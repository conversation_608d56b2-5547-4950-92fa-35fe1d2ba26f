import * as ui from '@sage/xtrem-ui';
import { fields } from '../menu-items/fields';

@ui.decorators.page<Checkbox>({
    authorizationCode: 'BSCFLDS',
    module: 'show-case',
    title: 'Field - Checkbox',
    category: 'SHOWCASE',
    isTransient: true,
    onLoad() {
        this.field.value = true;
        this.value.value = true;
    },
    menuItem: fields,
})
export class Checkbox extends ui.Page {
    @ui.decorators.section<Checkbox>({
        title: 'Checkbox field',
    })
    section: ui.containers.Section;

    @ui.decorators.block<Checkbox>({
        parent() {
            return this.section;
        },
        title: 'Field example',
    })
    fieldBlock: ui.containers.Block;

    @ui.decorators.checkboxField<Checkbox>({
        parent() {
            return this.fieldBlock;
        },
        onClick() {
            this.clickTriggered.isHidden = false;
            setTimeout(() => {
                this.clickTriggered.isHidden = true;
            }, 5000);
        },
        onChange() {
            if (this.field.value !== this.value.value) {
                this.value.value = this.field.value;
                this.changeTriggered.isHidden = false;
                setTimeout(() => {
                    this.changeTriggered.isHidden = true;
                }, 5000);
            }
        },
    })
    field: ui.fields.Checkbox;

    @ui.decorators.labelField<Checkbox>({
        parent() {
            return this.fieldBlock;
        },
        isHidden: true,
        map() {
            return 'Change was triggered';
        },
    })
    changeTriggered: ui.fields.Label;

    @ui.decorators.labelField<Checkbox>({
        parent() {
            return this.fieldBlock;
        },
        isHidden: true,
        map() {
            return 'Click was triggered';
        },
    })
    clickTriggered: ui.fields.Label;

    @ui.decorators.block<Checkbox>({
        parent() {
            return this.section;
        },
        title: 'Configuration',
    })
    configurationBlock: ui.containers.Block;

    @ui.decorators.textField<Checkbox>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Helper text',
        onChange() {
            this.field.helperText = this.helperText.value;
        },
    })
    helperText: ui.fields.Text;

    @ui.decorators.checkboxField<Checkbox>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is disabled',
        onChange() {
            this.field.isDisabled = this.isDisabled.value;
        },
    })
    isDisabled: ui.fields.Checkbox;

    @ui.decorators.checkboxField<Checkbox>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is helper text hidden',
        onChange() {
            this.field.isHelperTextHidden = this.isHelperTextHidden.value;
        },
    })
    isHelperTextHidden: ui.fields.Checkbox;

    @ui.decorators.checkboxField<Checkbox>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is hidden',
        onChange() {
            this.field.isHidden = this.isHidden.value;
        },
    })
    isHidden: ui.fields.Checkbox;

    @ui.decorators.checkboxField<Checkbox>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is readOnly',
        onChange() {
            this.field.isReadOnly = this.isReadOnly.value;
        },
    })
    isReadOnly: ui.fields.Checkbox;

    @ui.decorators.checkboxField<Checkbox>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is title hidden',
        onChange() {
            this.field.isTitleHidden = this.isTitleHidden.value;
        },
    })
    isTitleHidden: ui.fields.Checkbox;

    @ui.decorators.textField<Checkbox>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Title',
        onChange() {
            this.field.title = this.title.value;
        },
    })
    title: ui.fields.Text;

    @ui.decorators.checkboxField<Checkbox>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Value',
        onChange() {
            if (this.value.value !== this.field.value) {
                this.field.value = this.value.value;
            }
        },
    })
    value: ui.fields.Checkbox;

    @ui.decorators.buttonField<Checkbox>({
        parent() {
            return this.configurationBlock;
        },
        map() {
            return 'Focus field';
        },
        onClick() {
            this.field.focus();
        },
    })
    focus: ui.fields.Button;

    /* Additional examples */

    @ui.decorators.block<Checkbox>({
        parent() {
            return this.section;
        },
        title: 'Additional examples',
    })
    additionalBlock: ui.containers.Block;

    @ui.decorators.checkboxField<Checkbox>({
        parent() {
            return this.additionalBlock;
        },
        title: 'Mandatory',
        isMandatory: true,
    })
    mandatory: ui.fields.Checkbox;

    @ui.decorators.separatorField<Checkbox>({
        parent() {
            return this.additionalBlock;
        },
        isFullWidth: true,
    })
    fieldSeparator1: ui.fields.Separator;

    @ui.decorators.checkboxField<Checkbox>({
        parent() {
            return this.additionalBlock;
        },
        title: 'With warning message',
        warningMessage: 'Wow, warning!',
    })
    warningMessageField: ui.fields.Checkbox;

    @ui.decorators.checkboxField<Checkbox>({
        parent() {
            return this.additionalBlock;
        },
        title: 'With warning message with callback',
        helperText: 'Warning if checked',
        warningMessage() {
            if (this.warningMessageWithCallbackField.value) {
                return 'Warning message';
            }
            return null;
        },
    })
    warningMessageWithCallbackField: ui.fields.Checkbox;

    @ui.decorators.checkboxField<Checkbox>({
        parent() {
            return this.additionalBlock;
        },
        title: 'With info message',
        infoMessage: 'Wow, warning!',
    })
    infoMessageField: ui.fields.Checkbox;

    @ui.decorators.checkboxField<Checkbox>({
        parent() {
            return this.additionalBlock;
        },
        title: 'With info message with callback',
        helperText: 'Info message if checked',
        infoMessage() {
            if (this.infoMessageWithCallbackField.value) {
                return 'Info message';
            }
            return null;
        },
    })
    infoMessageWithCallbackField: ui.fields.Checkbox;

    @ui.decorators.checkboxField<Checkbox>({
        parent() {
            return this.additionalBlock;
        },
        title: 'With info message',
        warningMessage: 'Wow, warning!',
        infoMessage: 'You should not see this',
    })
    infoAndWarningMessageField: ui.fields.Checkbox;

    @ui.decorators.checkboxField<Checkbox>({
        parent() {
            return this.additionalBlock;
        },
        validation() {
            if (this.infoAndWarningMessageMandatoryField.value) {
                return 'Error message';
            }
            return '';
        },
        title: 'Info, warning and validation',
        warningMessage: 'Wow, warning!',
        infoMessage: 'You should not see this',
        helperText: 'Error if checked',
    })
    infoAndWarningMessageMandatoryField: ui.fields.Checkbox;
}
