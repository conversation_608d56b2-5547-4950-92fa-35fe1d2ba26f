import { ShowCaseInvoice, ShowCaseInvoiceLine, ShowCaseProduct, ShowCaseProvider } from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import { tableField } from '../menu-items/_index';

@ui.decorators.page<TableNestedReference, ShowCaseInvoice>({
    authorizationCode: 'BSCFLDS',
    module: 'show-case',
    category: 'SHOWCASE',
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.text({ bind: '_id' }),
            titleRight: ui.nestedFields.date({ bind: 'purchaseDate' }),
        },
    },
    node: '@sage/xtrem-show-case/ShowCaseInvoice',
    title: 'Field - Table (nested reference)',
    menuItem: tableField,
})
export class TableNestedReference extends ui.Page {
    @ui.decorators.section<TableNestedReference>({
        title: 'Table',
    })
    section: ui.containers.Section;

    @ui.decorators.block<TableNestedReference>({
        parent() {
            return this.section;
        },
        title: 'Table with nested reference',
    })
    fieldBlock: ui.containers.Block;

    @ui.decorators.tableField<TableNestedReference, ShowCaseInvoiceLine>({
        node: '@sage/xtrem-show-case/ShowCaseInvoiceLine',
        bind: 'lines',
        canFilter: true,
        canAddNewLine: true,
        columns: [
            ui.nestedFields.text({
                bind: '_id',
                title: 'Id',
                isHiddenOnMainField: true,
                isReadOnly: true,
            }),
            ui.nestedFields.numeric({
                bind: 'orderQuantity',
                title: 'Quantity',
                scale: 0,
            }),
            ui.nestedFields.reference<TableNestedReference, ShowCaseInvoiceLine, ShowCaseProduct>({
                bind: 'product',
                valueField: { provider: { textField: true } },
                title: 'Provider',
                node: '@sage/xtrem-show-case/ShowCaseProduct',
                canFilter: true,
                columns: [
                    ui.nestedFields.text({
                        bind: '_id',
                        title: 'Id',
                        isHiddenOnMainField: true,
                        isReadOnly: true,
                    }),
                    ui.nestedFields.text({
                        bind: 'product',
                        title: 'Product',
                        canFilter: true,
                    }),
                    ui.nestedFields.reference<TableNestedReference, ShowCaseProduct, ShowCaseProvider>({
                        bind: 'provider',
                        node: '@sage/xtrem-show-case/ShowCaseProvider',
                        title: 'Provider',
                        canFilter: true,
                        valueField: 'textField',
                    }),
                    ui.nestedFields.text({
                        bind: 'description',
                        title: 'Description',
                        canFilter: true,
                    }),
                ],
            }),
            ui.nestedFields.reference<TableNestedReference, ShowCaseInvoiceLine, ShowCaseInvoiceLine['product']>({
                bind: 'product',
                valueField: { provider: { integerField: true } },
                title: 'Provider Integer',
                node: '@sage/xtrem-show-case/ShowCaseProduct',
                canFilter: true,
                minLookupCharacters: 0,
                helperTextField: { provider: { textField: true } },
                columns: [
                    ui.nestedFields.reference<TableNestedReference, ShowCaseProduct, ShowCaseProvider>({
                        bind: 'provider',
                        node: '@sage/xtrem-show-case/ShowCaseProvider',
                        title: 'Provider text',
                        canFilter: true,
                        valueField: 'textField',
                    }),
                    ui.nestedFields.reference<TableNestedReference, ShowCaseProduct, ShowCaseProvider>({
                        bind: 'provider',
                        node: '@sage/xtrem-show-case/ShowCaseProvider',
                        title: 'Provider integer',
                        canFilter: true,
                        valueField: 'integerField',
                    }),
                ],
            }),
            ui.nestedFields.numeric({
                bind: 'netPrice',
                title: 'Price',
                scale: 2,
            }),
        ],
        orderBy: {
            orderQuantity: -1,
        },
        parent() {
            return this.fieldBlock;
        },
    })
    field: ui.fields.Table<any>;
}
