import { GraphApi, ShowCaseInvoice } from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import { camelCase } from 'lodash';
import { applicationPages } from '../menu-items/application-pages';

@ui.decorators.page<ShowCaseInvoiceReference>({
    authorizationCode: 'BSCFLDS',
    module: 'show-case',
    title: 'ShowCase - Invoices in Reference',
    category: 'SHOWCASE',
    onLoad() {
        this.operatorType.value = 'at least';
    },
    menuItem: applicationPages,
})
export class ShowCaseInvoiceReference extends ui.Page<GraphApi> {
    @ui.decorators.section<ShowCaseInvoiceReference>({})
    section: ui.containers.Section;

    @ui.decorators.block<ShowCaseInvoiceReference>({
        parent() {
            return this.section;
        },
    })
    fieldBlock: ui.containers.Block;

    @ui.decorators.referenceField<ShowCaseInvoiceReference, ShowCaseInvoice>({
        columns: [
            ui.nestedFields.text({ bind: '_id', title: 'Invoice Number', canFilter: false }),
            ui.nestedFields.date({ bind: 'purchaseDate', title: 'Purchase Date' }),
            ui.nestedFields.reference({
                bind: 'customer',
                canFilter: true,
                node: '@sage/xtrem-show-case/ShowCaseCustomer',
                title: 'Customer Name',
                valueField: 'name',
            }),
            ui.nestedFields.count({ bind: 'lines', title: 'Number of lines' }),
        ],
        helperTextField: '_id',
        title: 'Invoices',
        shouldSuggestionsIncludeColumns: true,
        width: 'large',
        node: '@sage/xtrem-show-case/ShowCaseInvoice',
        parent() {
            return this.fieldBlock;
        },
        valueField: { customer: { name: true } },
        canFilter: true,
        isAutoSelectEnabled: true,
    })
    field: ui.fields.Reference;

    @ui.decorators.section<ShowCaseInvoiceReference>({})
    filterSection: ui.containers.Section;

    @ui.decorators.block<ShowCaseInvoiceReference>({
        parent() {
            return this.filterSection;
        },
    })
    shouldFilterBlock: ui.containers.Block;

    @ui.decorators.switchField<ShowCaseInvoiceReference>({
        isTransient: true,
        parent() {
            return this.shouldFilterBlock;
        },
        title: 'Apply Filter',
        onChange() {
            this.filterBlock.isHidden = !this.filterBlock.isHidden;

            if (this.filterBlock.isHidden) {
                this.field.filter = null;
            }
        },
    })
    shouldFilter: ui.fields.Switch;

    @ui.decorators.block<ShowCaseInvoiceReference>({
        parent() {
            return this.filterSection;
        },
        isHidden: true,
    })
    filterBlock: ui.containers.Block;

    @ui.decorators.selectField<ShowCaseInvoiceReference>({
        isTransient: true,
        parent() {
            return this.filterBlock;
        },
        title: 'Filter Operator Type',
        options: ['at least', 'at most', 'every', 'none'],
        onChange() {
            if (this.operatorType.value === 'at least' || this.operatorType.value === 'at most') {
                this.numberOfCases.isDisabled = false;
            } else {
                this.numberOfCases.value = null;
                this.numberOfCases.isDisabled = true;
            }
        },
        helperText: 'Operator Type to filter against "Lines" collection in the Invoice',
    })
    operatorType: ui.fields.Select;

    @ui.decorators.numericField<ShowCaseInvoiceReference>({
        isTransient: true,
        parent() {
            return this.filterBlock;
        },
        title: 'Occurrence',
        helperText: 'Number of cases that should occur for the selected operator type.',
    })
    numberOfCases: ui.fields.Numeric;

    @ui.decorators.switchField<ShowCaseInvoiceReference>({
        isTransient: true,
        parent() {
            return this.filterBlock;
        },
        title: 'PRODUCT -> Greater than?',
        helperText: 'Disable if you want to perform a "less than" comparison to Lines\' PRODUCT quantity. ',
    })
    greaterThanProductQuantity: ui.fields.Switch;

    @ui.decorators.numericField<ShowCaseInvoiceReference>({
        isTransient: true,
        parent() {
            return this.filterBlock;
        },
        title: 'Product Quantity',
        helperText: 'Quantity of products to be compared with.',
    })
    quantityOfProducts: ui.fields.Numeric;

    @ui.decorators.switchField<ShowCaseInvoiceReference>({
        isTransient: true,
        parent() {
            return this.filterBlock;
        },
        title: 'ORDER -> Greater than?',
        helperText: 'Disable if you want to perform a "less than" comparison to Lines\' ORDER quantity. ',
    })
    greaterThanOrderQuantity: ui.fields.Switch;

    @ui.decorators.numericField<ShowCaseInvoiceReference>({
        isTransient: true,
        parent() {
            return this.filterBlock;
        },
        title: 'Order Quantity',
        helperText: 'Quantity of orders to be compared with.',
    })
    quantityOfOrders: ui.fields.Numeric;

    @ui.decorators.buttonField<ShowCaseInvoiceReference>({
        isTransient: true,
        parent() {
            return this.filterBlock;
        },
        map() {
            return 'OK';
        },
        onClick() {
            const isValueValid = (value: number) => {
                return typeof value !== 'undefined' && value !== null && !Number.isNaN(Number(value));
            };

            this.field.filter = {
                lines: {
                    [`_${camelCase(this.operatorType.value)}`]: this.numberOfCases.isDisabled
                        ? true
                        : this.numberOfCases.value,
                    ...(isValueValid(this.quantityOfProducts.value) && {
                        product: {
                            qty: {
                                [this.greaterThanProductQuantity.value ? '_gt' : '_lt']: this.quantityOfProducts.value,
                            },
                        },
                    }),
                    ...(isValueValid(this.quantityOfOrders.value) && {
                        orderQuantity: {
                            [this.greaterThanOrderQuantity.value ? '_gt' : '_lt']: this.quantityOfOrders.value,
                        },
                    }),
                },
            };
        },
    })
    applyFilter: ui.fields.Button;
}
