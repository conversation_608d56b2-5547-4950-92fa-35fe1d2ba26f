import * as ui from '@sage/xtrem-ui';
import { validation } from '../menu-items/validation';

@ui.decorators.page<ConditionalValidation>({
    authorizationCode: 'HLPRPNL',
    module: 'show-case',
    category: 'SHOWCASE',
    title: 'Page - Conditional validation',
    isTransient: true,
    validation() {
        if (this.testField1.value !== this.testField2.value) {
            return 'Test field 1 and test field 2 must have the same value.';
        }
        return undefined;
    },
    menuItem: validation,
})
export class ConditionalValidation extends ui.Page {
    @ui.decorators.section<ConditionalValidation>({
        isTitleHidden: true,
    })
    controlSection: ui.containers.Section;

    @ui.decorators.block<ConditionalValidation>({
        title: 'Configuration',
        parent() {
            return this.controlSection;
        },
    })
    controlBlock: ui.containers.Block;

    @ui.decorators.buttonField<ConditionalValidation>({
        map() {
            return 'Hide Field 1';
        },
        parent() {
            return this.controlBlock;
        },
        onClick() {
            this.testField1.isHidden = !this.testField1.isHidden;
        },
        helperText: 'This button toggles visibility.',
    })
    hideField1Button: ui.fields.Button;

    @ui.decorators.buttonField<ConditionalValidation>({
        map() {
            return 'Hide Field 2';
        },
        parent() {
            return this.controlBlock;
        },
        onClick() {
            this.testField2.isHidden = !this.testField2.isHidden;
        },
        helperText: 'This button toggles visibility.',
    })
    hideField2Button: ui.fields.Button;

    @ui.decorators.buttonField<ConditionalValidation>({
        map() {
            return 'Hide Field 3';
        },
        parent() {
            return this.controlBlock;
        },
        onClick() {
            this.testField3.isHidden = !this.testField3.isHidden;
        },
        helperText: 'This button toggles visibility.',
    })
    hideField3Button: ui.fields.Button;

    @ui.decorators.separatorField<ConditionalValidation>({
        parent() {
            return this.controlBlock;
        },
        isFullWidth: true,
    })
    sep1: ui.fields.Separator;

    @ui.decorators.buttonField<ConditionalValidation>({
        map() {
            return 'Disable Field 1';
        },
        parent() {
            return this.controlBlock;
        },
        onClick() {
            this.testField1.isDisabled = !this.testField1.isDisabled;
        },
        helperText: 'This button toggles the disabled state.',
    })
    disableField1Button: ui.fields.Button;

    @ui.decorators.buttonField<ConditionalValidation>({
        map() {
            return 'Disable Field 2';
        },
        parent() {
            return this.controlBlock;
        },
        onClick() {
            this.testField2.isDisabled = !this.testField2.isDisabled;
        },
        helperText: 'This button toggles the disabled state.',
    })
    disableField2Button: ui.fields.Button;

    @ui.decorators.buttonField<ConditionalValidation>({
        map() {
            return 'Disable Field 3';
        },
        parent() {
            return this.controlBlock;
        },
        onClick() {
            this.testField3.isDisabled = !this.testField3.isDisabled;
        },
        helperText: 'This button toggles the disabled state.',
    })
    disableField3Button: ui.fields.Button;

    @ui.decorators.separatorField<ConditionalValidation>({
        parent() {
            return this.controlBlock;
        },
        isFullWidth: true,
    })
    sep2: ui.fields.Separator;

    @ui.decorators.buttonField<ConditionalValidation>({
        map() {
            return 'Hide Test Section';
        },
        parent() {
            return this.controlBlock;
        },
        onClick() {
            this.testSection.isHidden = !this.testSection.isHidden;
        },
        helperText: 'This button toggles visibility.',
    })
    hideTestSection: ui.fields.Button;

    @ui.decorators.buttonField<ConditionalValidation>({
        map() {
            return 'Hide Test Block 1';
        },
        onClick() {
            this.testBlock1.isHidden = !this.testBlock1.isHidden;
        },
        parent() {
            return this.controlBlock;
        },
        helperText: 'This button toggles visibility.',
    })
    hideTestBlock1: ui.fields.Button;

    @ui.decorators.buttonField<ConditionalValidation>({
        map() {
            return 'Hide Test Block 2';
        },
        onClick() {
            this.testBlock2.isHidden = !this.testBlock2.isHidden;
        },
        parent() {
            return this.controlBlock;
        },
        helperText: 'This button toggles visibility.',
    })
    hideTestBlock2: ui.fields.Button;

    @ui.decorators.separatorField<ConditionalValidation>({
        parent() {
            return this.controlBlock;
        },
        isFullWidth: true,
    })
    sep3: ui.fields.Separator;

    @ui.decorators.buttonField<ConditionalValidation>({
        map() {
            return 'Validate Test Block 1';
        },
        async onClick() {
            const result = await this.testBlock1.validate();
            this.$.dialog.message('info', 'Validation result', result.length === 0 ? 'All good.' : result.join('\n'));
        },
        parent() {
            return this.controlBlock;
        },
    })
    validateBlock1Button: ui.fields.Button;

    @ui.decorators.buttonField<ConditionalValidation>({
        map() {
            return 'Validate Test Block 2';
        },
        async onClick() {
            const result = await this.testBlock2.validate();
            this.$.dialog.message('info', 'Validation result', result.length === 0 ? 'All good.' : result.join('\n'));
        },
        parent() {
            return this.controlBlock;
        },
    })
    validateBlock2Button: ui.fields.Button;

    @ui.decorators.buttonField<ConditionalValidation>({
        map() {
            return 'Validate Test Section';
        },
        async onClick() {
            const result = await this.testSection.validate();
            this.$.dialog.message('info', 'Validation result', result.length === 0 ? 'All good.' : result.join('\n'));
        },
        parent() {
            return this.controlBlock;
        },
    })
    validateSectionButton: ui.fields.Button;

    @ui.decorators.buttonField<ConditionalValidation>({
        map() {
            return 'Validate Page';
        },
        async onClick() {
            const result = await this.$.page.validate();
            this.$.dialog.message('info', 'Validation result', result.length === 0 ? 'All good.' : result.join('\n'));
        },
        parent() {
            return this.controlBlock;
        },
    })
    validatePageButton: ui.fields.Button;

    @ui.decorators.section<ConditionalValidation>({
        title: 'Test Section',
    })
    testSection: ui.containers.Section;

    @ui.decorators.block<ConditionalValidation>({
        title: 'Test Block 1',
        parent() {
            return this.testSection;
        },
    })
    testBlock1: ui.containers.Block;

    @ui.decorators.block<ConditionalValidation>({
        title: 'Test Block 2',
        parent() {
            return this.testSection;
        },
    })
    testBlock2: ui.containers.Block;

    @ui.decorators.textField<ConditionalValidation>({
        title: 'Test Field 1',
        isMandatory: true,
        helperText: 'This field is mandatory',
        parent() {
            return this.testBlock1;
        },
    })
    testField1: ui.fields.Text;

    @ui.decorators.textField<ConditionalValidation>({
        title: 'Test Field 2',
        isMandatory: true,
        helperText: 'This field is mandatory',
        parent() {
            return this.testBlock1;
        },
    })
    testField2: ui.fields.Text;

    @ui.decorators.textField<ConditionalValidation>({
        title: 'Test Field 3',
        isMandatory: true,
        helperText: 'This field is mandatory',
        parent() {
            return this.testBlock2;
        },
    })
    testField3: ui.fields.Text;
}
