import * as ui from '@sage/xtrem-ui';
import {
    ShowCaseProduct,
    ShowCaseCustomer,
    ShowCaseOrder,
    ShowCaseInvoice,
    ShowCaseInvoiceLine,
} from '@sage/xtrem-show-case-api';
import { nestedGrid } from '../menu-items/nested-grid';

@ui.decorators.page<NestedGridWithLineNumbers, ShowCaseCustomer>({
    authorizationCode: 'BSCFLDS',
    module: 'show-case',
    category: 'SHOWCASE',
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.text({ bind: 'name' }),
            titleRight: ui.nestedFields.text({ bind: '_id' }),
            line2: ui.nestedFields.text({ bind: 'email' }),
        },
    },
    node: '@sage/xtrem-show-case/ShowCaseCustomer',
    title: 'Field - NestedGrid with line numbers',
    menuItem: nestedGrid,
})
export class NestedGridWithLineNumbers extends ui.Page {
    @ui.decorators.section<NestedGridWithLineNumbers>({
        title: 'Nested Grid',
    })
    section: ui.containers.Section;

    @ui.decorators.block<NestedGridWithLineNumbers>({
        parent() {
            return this.section;
        },
        title: 'Nested Grid block',
    })
    fieldBlock: ui.containers.Block;

    @ui.decorators.block<NestedGridWithLineNumbers>({
        parent() {
            return this.section;
        },
        title: 'Fetch level block',
    })
    fetchLevelBlock: ui.containers.Block;

    @ui.decorators.block<NestedGridWithLineNumbers>({
        parent() {
            return this.section;
        },
        title: 'Select items by app code',
    })
    selectItemBlock: ui.containers.Block;

    @ui.decorators.block<NestedGridWithLineNumbers>({
        parent() {
            return this.section;
        },
        title: 'Configuration',
    })
    configurationBlock: ui.containers.Block;

    @ui.decorators.textField<NestedGridWithLineNumbers>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Title',
        onChange() {
            this.field.title = this.title.value;
        },
        isTransient: true,
    })
    title: ui.fields.Text;

    @ui.decorators.textField<NestedGridWithLineNumbers>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Helper text',
        onChange() {
            this.field.helperText = this.helperText.value;
        },
        isTransient: true,
    })
    helperText: ui.fields.Text;

    @ui.decorators.checkboxField<NestedGridWithLineNumbers>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is hidden',
        onChange() {
            this.field.isHidden = this.isHidden.value;
        },
        isTransient: true,
    })
    isHidden: ui.fields.Checkbox;

    @ui.decorators.checkboxField<NestedGridWithLineNumbers>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is title hidden',
        onChange() {
            this.field.isTitleHidden = this.isTitleHidden.value;
        },
        isTransient: true,
    })
    isTitleHidden: ui.fields.Checkbox;

    @ui.decorators.checkboxField<NestedGridWithLineNumbers>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is helper text hidden',
        onChange() {
            this.field.isHelperTextHidden = this.isHelperTextHidden.value;
        },
        isTransient: true,
    })
    isHelperTextHidden: ui.fields.Checkbox;

    @ui.decorators.checkboxField<NestedGridWithLineNumbers>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is disabled',
        onChange() {
            this.field.isDisabled = this.isDisabled.value;
        },
        isTransient: true,
    })
    isDisabled: ui.fields.Checkbox;

    @ui.decorators.checkboxField<NestedGridWithLineNumbers>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is container disabled',
        onChange() {
            this.fieldBlock.isDisabled = this.isContainerDisabled.value;
        },
        isTransient: true,
    })
    isContainerDisabled: ui.fields.Checkbox;

    @ui.decorators.nestedGridField<NestedGridWithLineNumbers, [ShowCaseOrder, ShowCaseInvoice, ShowCaseInvoiceLine]>({
        bind: 'orders',
        canFilter: true,
        hasLineNumbers: true,
        levels: [
            {
                node: '@sage/xtrem-show-case/ShowCaseOrder',
                childProperty: 'invoices',
                columns: [
                    ui.nestedFields.text({
                        bind: '_id',
                        title: 'Id',
                        isHiddenDesktop: false,
                        isReadOnly: true,
                        onClick(rowId: string, rowData: any) {
                            ui.console.log(rowData);
                        },
                    }),
                    ui.nestedFields.date({
                        bind: 'orderDate',
                        title: 'Order Date',
                        canFilter: true,
                    }),
                ],
            },
            {
                node: '@sage/xtrem-show-case/ShowCaseInvoice',
                childProperty: 'lines',
                columns: [
                    ui.nestedFields.text({
                        bind: '_id',
                        title: 'Id',
                        isHiddenDesktop: false,
                        isReadOnly: true,
                        onClick(rowId: string, rowData: any) {
                            ui.console.log(rowData);
                        },
                    }),
                    ui.nestedFields.date({
                        bind: 'purchaseDate',
                        title: 'Purchase Date',
                        onClick(rowId: string, rowData: any) {
                            ui.console.log(rowData);
                        },
                    }),
                ],
            },
            {
                node: '@sage/xtrem-show-case/ShowCaseInvoiceLine',
                columns: [
                    ui.nestedFields.text({
                        bind: '_id',
                        title: 'Id',
                        isHiddenDesktop: false,
                        isReadOnly: true,
                        onClick(rowId: string, rowData: any) {
                            ui.console.log(rowData);
                        },
                    }),
                    ui.nestedFields.numeric({
                        bind: 'orderQuantity',
                        title: 'Quantity',
                        scale: 0,
                    }),
                    ui.nestedFields.numeric({
                        bind: 'netPrice',
                        title: 'Net Price',
                        scale: 2,
                    }),
                    ui.nestedFields.reference<NestedGridWithLineNumbers, ShowCaseInvoiceLine, ShowCaseProduct>({
                        bind: 'product',
                        node: '@sage/xtrem-show-case/ShowCaseProduct',
                        valueField: { product: true },
                        helperTextField: { _id: true },
                        title: 'Product',
                        canFilter: true,
                    }),
                ],
            },
        ],
        parent() {
            return this.fieldBlock;
        },
    })
    field: ui.fields.NestedGrid<[ShowCaseOrder, ShowCaseInvoice, ShowCaseInvoiceLine], NestedGridWithLineNumbers>;
}
