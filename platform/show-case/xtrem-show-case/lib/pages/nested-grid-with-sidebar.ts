import {
    ShowCase<PERSON>ust<PERSON>,
    ShowCaseInvoice,
    ShowCaseInvoiceLine,
    ShowCaseOrder,
    ShowCaseProduct,
} from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import { nestedGrid } from '../menu-items/nested-grid';

@ui.decorators.page<NestedGridWithSidebar, ShowCaseCustomer>({
    authorizationCode: 'BSCFLDS',
    module: 'show-case',
    category: 'SHOWCASE',
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.text({ bind: '_id' }),
            titleLineLeft: ui.nestedFields.text({ bind: 'name' }),
            titleRight: ui.nestedFields.text({ bind: 'email' }),
        },
    },
    node: '@sage/xtrem-show-case/ShowCaseCustomer',
    title: 'Field - NestedGrid with sidebar',
    menuItem: nestedGrid,
    businessActions() {
        return [this.$standardSaveAction];
    },
})
export class NestedGridWithSidebar extends ui.Page {
    @ui.decorators.section<NestedGridWithSidebar>({
        title: 'Nested Grid with sidebar',
    })
    section: ui.containers.Section;

    @ui.decorators.block<NestedGridWithSidebar>({
        parent() {
            return this.section;
        },
        title: 'Nested Grid block',
    })
    fieldBlock: ui.containers.Block;

    @ui.decorators.textAreaField<NestedGridWithSidebar>({
        isTransient: true,
        isHidden: true,
        title: 'Details',
    })
    orderDetails: ui.fields.TextArea<NestedGridWithSidebar>;

    @ui.decorators.textAreaField<NestedGridWithSidebar>({
        isTransient: true,
        isHidden: true,
        title: 'Notes',
    })
    invoiceNotes: ui.fields.TextArea<NestedGridWithSidebar>;

    @ui.decorators.textAreaField<NestedGridWithSidebar>({
        isTransient: true,
        isHidden: true,
        title: 'Comments',
    })
    invoiceLineComments: ui.fields.TextArea<NestedGridWithSidebar>;

    @ui.decorators.nestedGridField<NestedGridWithSidebar, [ShowCaseOrder, ShowCaseInvoice, ShowCaseInvoiceLine]>({
        bind: 'orders',
        levels: [
            {
                node: '@sage/xtrem-show-case/ShowCaseOrder',
                childProperty: 'invoices',
                columns: [
                    ui.nestedFields.text({
                        bind: '_id',
                        title: 'Id',
                        isHiddenDesktop: false,
                        isReadOnly: true,
                        onClick(_rowId: string, rowData: any) {
                            ui.console.log(rowData);
                        },
                    }),
                    ui.nestedFields.reference<NestedGridWithSidebar, ShowCaseOrder, ShowCaseCustomer>({
                        bind: 'customer',
                        node: '@sage/xtrem-show-case/ShowCaseCustomer',
                        title: 'Customer',
                        valueField: 'name',
                        isMandatory: true,
                    }),
                    ui.nestedFields.date({
                        bind: 'orderDate',
                        title: 'Order Date',
                    }),
                    ui.nestedFields.technical({
                        bind: { details: { value: true } },
                    }),
                ],
                dropdownActions: [
                    {
                        icon: 'box_arrow_left',
                        title: 'Edit order on sidebar',
                        async onClick(rowId: string, _, level) {
                            this.field.openSidebar(rowId, level);
                        },
                    },
                ],
                canAddNewLine: true,
                sidebar: {
                    headerDropdownActions: [
                        {
                            title: 'Primary action',
                            onClick(_id, rowData) {
                                this.$.showToast(
                                    `Primary action called for order with id ${_id} and date ${rowData.orderDate}`,
                                );
                            },
                        },
                        ui.menuSeparator(),
                        {
                            title: 'Secondary action',
                            icon: 'bank_with_card',
                            onClick(_id, rowData) {
                                this.$.showToast(
                                    `Secondary action called for order with id ${_id} and date ${rowData.orderDate}`,
                                    {
                                        type: 'warning',
                                    },
                                );
                            },
                        },
                        ui.menuSeparator(),
                        {
                            icon: 'delete',
                            title: 'Remove',
                            async onClick(_id) {
                                this.field.removeRecord(_id, 0);
                            },
                        },
                    ],
                    headerQuickActions: [
                        {
                            title: 'Lorem',
                            icon: 'three_boxes',
                            onClick(_id, rowData) {
                                this.$.showToast(
                                    `You clicked Lorem for order with id ${_id} and date ${rowData.orderDate}`,
                                );
                            },
                        },
                        {
                            title: 'Ipsum',
                            icon: 'video',
                            onClick(_id, rowData) {
                                this.$.showToast(
                                    `You clicked Ipsum for order with id ${_id} and date ${rowData.orderDate}`,
                                );
                            },
                        },
                    ],
                    async onRecordOpened(_id, recordValue) {
                        if (recordValue && recordValue.details) {
                            this.orderDetails.value = recordValue.details.value;
                            this.orderDetails.isHidden = false;
                        }
                    },
                    async onRecordConfirmed() {
                        this.orderDetails.isHidden = true;
                    },
                    async onRecordDiscarded() {
                        this.orderDetails.isHidden = true;
                    },
                    title(_id, recordValue) {
                        return `Order ${recordValue?._id}`;
                    },
                    layout() {
                        return {
                            mainSection: {
                                title: 'Main section',
                                blocks: {
                                    mainBlock: {
                                        title: 'Main block',
                                        fields: ['_id', 'orderDate'],
                                    },
                                    anotherBlock: {
                                        title: 'Details',
                                        fields: [this.orderDetails],
                                    },
                                },
                            },
                            detailsSection: {
                                title: 'Details section',
                                blocks: {
                                    someRandomBlock: {
                                        fields: ['customer'],
                                    },
                                },
                            },
                        };
                    },
                },
            },
            {
                node: '@sage/xtrem-show-case/ShowCaseInvoice',
                childProperty: 'lines',
                columns: [
                    ui.nestedFields.text({
                        bind: '_id',
                        title: 'Id',
                        isHiddenDesktop: false,
                        isReadOnly: true,
                        onClick(_rowId: string, rowData: any) {
                            ui.console.log(rowData);
                        },
                    }),
                    ui.nestedFields.date({
                        bind: 'purchaseDate',
                        title: 'Purchase Date',
                        onClick(_rowId: string, rowData: any) {
                            ui.console.log(rowData);
                        },
                    }),
                    ui.nestedFields.reference<NestedGridWithSidebar, ShowCaseInvoice, ShowCaseCustomer>({
                        bind: 'customer',
                        node: '@sage/xtrem-show-case/ShowCaseCustomer',
                        title: 'Customer',
                        valueField: 'name',
                        isMandatory: true,
                    }),
                    ui.nestedFields.technical({
                        bind: { notes: { value: true } },
                    }),
                ],
                dropdownActions: [
                    {
                        icon: 'box_arrow_left',
                        title: 'Edit invoice on sidebar',
                        async onClick(rowId: string, _, level) {
                            this.field.openSidebar(rowId, level);
                        },
                    },
                ],
                sidebar: {
                    headerDropdownActions: [
                        {
                            title: 'Primary action',
                            onClick(_id, rowData) {
                                this.$.showToast(
                                    `Primary action called for order with id ${_id} and purchase date ${rowData.purchaseDate}`,
                                );
                            },
                        },
                        ui.menuSeparator(),
                        {
                            title: 'Secondary action',
                            icon: 'bank_with_card',
                            onClick(_id, rowData) {
                                this.$.showToast(
                                    `Secondary action called for order with id ${_id} and purchase date ${rowData.purchaseDate}`,
                                    {
                                        type: 'warning',
                                    },
                                );
                            },
                        },
                        ui.menuSeparator(),
                        {
                            icon: 'delete',
                            title: 'Remove',
                            async onClick(_id) {
                                this.field.removeRecord(_id, 1);
                            },
                        },
                    ],
                    headerQuickActions: [
                        {
                            title: 'Lorem',
                            icon: 'three_boxes',
                            onClick(_id, rowData) {
                                this.$.showToast(
                                    `You clicked Lorem for order with id ${_id} and purchase date ${rowData.purchaseDate}`,
                                );
                            },
                        },
                        {
                            title: 'Ipsum',
                            icon: 'video',
                            onClick(_id, rowData) {
                                this.$.showToast(
                                    `You clicked Ipsum for order with id ${_id} and purchase date ${rowData.purchaseDate}`,
                                );
                            },
                        },
                    ],
                    async onRecordOpened(_id, recordValue) {
                        if (recordValue && recordValue.notes) {
                            this.invoiceNotes.value = recordValue.notes.value;
                            this.invoiceNotes.isHidden = false;
                        }
                    },
                    async onRecordConfirmed() {
                        this.invoiceNotes.isHidden = true;
                    },
                    async onRecordDiscarded() {
                        this.invoiceNotes.isHidden = true;
                    },
                    title(_id, recordValue) {
                        return `Invoice ${recordValue?._id}`;
                    },
                    layout() {
                        return {
                            mainSection: {
                                title: 'Main section',
                                blocks: {
                                    mainBlock: {
                                        title: 'Main block',
                                        fields: ['_id', 'purchaseDate'],
                                    },
                                    anotherBlock: {
                                        title: 'Notes',
                                        fields: [this.invoiceNotes],
                                    },
                                },
                            },
                            detailsSection: {
                                title: 'Details section',
                                blocks: {
                                    someRandomBlock: {
                                        fields: ['customer'],
                                    },
                                },
                            },
                        };
                    },
                },
            },
            {
                node: '@sage/xtrem-show-case/ShowCaseInvoiceLine',
                columns: [
                    ui.nestedFields.text({
                        bind: '_id',
                        title: 'Id',
                        isHiddenDesktop: false,
                        isReadOnly: true,
                        onClick(_rowId: string, rowData: any) {
                            ui.console.log(rowData);
                        },
                    }),
                    ui.nestedFields.numeric({
                        bind: 'orderQuantity',
                        title: 'Quantity',
                        scale: 0,
                        validation: v => {
                            if (v < 0) {
                                return 'Quantity must be positive';
                            }
                            return '';
                        },
                    }),
                    ui.nestedFields.numeric({
                        bind: 'netPrice',
                        title: 'Net Price',
                        scale: 2,
                    }),
                    ui.nestedFields.reference<NestedGridWithSidebar, ShowCaseInvoiceLine, ShowCaseProduct>({
                        isFilterLimitedToDataset: true,
                        bind: 'product',
                        node: '@sage/xtrem-show-case/ShowCaseProduct',
                        valueField: { product: true },
                        helperTextField: { _id: true },
                        title: 'Product',
                        isMandatory: true,
                    }),
                ],
                dropdownActions: [
                    {
                        icon: 'box_arrow_left',
                        title: 'Edit invoice line on sidebar',
                        async onClick(rowId: string, _, level) {
                            this.field.openSidebar(rowId, level);
                        },
                    },
                ],
                canAddNewLine: true,
                sidebar: {
                    headerDropdownActions: [
                        {
                            title: 'Primary action',
                            onClick(_id, rowData) {
                                this.$.showToast(
                                    `Primary action called for order with id ${_id} and net price ${rowData.netPrice}`,
                                );
                            },
                        },
                        ui.menuSeparator(),
                        {
                            title: 'Secondary action',
                            icon: 'bank_with_card',
                            onClick(_id, rowData) {
                                this.$.showToast(
                                    `Secondary action called for order with id ${_id} and net price ${rowData.netPrice}`,
                                    {
                                        type: 'warning',
                                    },
                                );
                            },
                        },
                        ui.menuSeparator(),
                        {
                            icon: 'delete',
                            title: 'Remove',
                            async onClick(_id) {
                                this.field.removeRecord(_id, 2);
                            },
                        },
                    ],
                    headerQuickActions: [
                        {
                            title: 'Lorem',
                            icon: 'three_boxes',
                            onClick(_id, rowData) {
                                this.$.showToast(
                                    `You clicked Lorem for order with id ${_id} and net price ${rowData.netPrice}`,
                                );
                            },
                        },
                        {
                            title: 'Ipsum',
                            icon: 'video',
                            onClick(_id, rowData) {
                                this.$.showToast(
                                    `You clicked Ipsum for order with id ${_id} and net price ${rowData.netPrice}`,
                                );
                            },
                        },
                    ],
                    async onRecordOpened(_id, recordValue) {
                        if (recordValue) {
                            this.invoiceLineComments.value = recordValue.comments || '';
                            this.invoiceLineComments.isHidden = false;
                        }
                    },
                    async onRecordConfirmed() {
                        this.invoiceLineComments.isHidden = true;
                    },
                    async onRecordDiscarded() {
                        this.invoiceLineComments.isHidden = true;
                    },
                    title(_id, recordValue) {
                        return `Invoice line ${recordValue?._id}`;
                    },
                    layout() {
                        return {
                            mainSection: {
                                title: 'Main section',
                                blocks: {
                                    mainBlock: {
                                        title: 'Main block',
                                        fields: ['_id', 'orderQuantity', 'netPrice'],
                                    },
                                    anotherBlock: {
                                        title: 'Comments',
                                        fields: [this.invoiceLineComments],
                                    },
                                },
                            },
                            detailsSection: {
                                title: 'Details section',
                                blocks: {
                                    someRandomBlock: {
                                        fields: ['product'],
                                    },
                                },
                            },
                        };
                    },
                },
            },
        ],
        parent() {
            return this.fieldBlock;
        },
    })
    field: ui.fields.NestedGrid<[ShowCaseOrder, ShowCaseInvoice, ShowCaseInvoiceLine], NestedGridWithSidebar>;
}
