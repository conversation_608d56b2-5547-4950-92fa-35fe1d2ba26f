import { ShowCaseProvider } from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import { misc } from '../menu-items/misc';

@ui.decorators.page<BindProperty, ShowCaseProvider>({
    authorizationCode: 'ESHOWCABP',
    module: 'show-case',
    title: 'Bind property',
    node: '@sage/xtrem-show-case/ShowCaseProvider',
    category: 'SHOWCASE',
    defaultEntry: () => '1',
    menuItem: misc,
})
export class BindProperty extends ui.Page {
    @ui.decorators.section<BindProperty>({
        title: 'Button section',
    })
    section: ui.containers.Section;

    @ui.decorators.block<BindProperty>({
        parent() {
            return this.section;
        },
        title: 'Buttons',
    })
    block: ui.containers.Block;

    @ui.decorators.labelField<BindProperty>({
        parent() {
            return this.block;
        },
        title: 'Bound label field',
        bind: 'integerField',
    })
    sinatra: ui.fields.Label;

    @ui.decorators.textField<BindProperty>({
        parent() {
            return this.block;
        },
        title: 'Bound text field',
        bind: 'integerField',
    })
    elvis: ui.fields.Text;

    @ui.decorators.numericField<BindProperty>({
        parent() {
            return this.block;
        },
        title: 'Regular numeric field',
        scale: 4,
    })
    integerField: ui.fields.Numeric;
}
