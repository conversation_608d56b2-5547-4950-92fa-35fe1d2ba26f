import { Graph<PERSON><PERSON>, ShowCaseProduct, ShowCaseProvider } from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import { fields } from '../menu-items/fields';

@ui.decorators.page<Count, ShowCaseProvider>({
    authorizationCode: 'BSCFLDS',
    defaultEntry: () => '2',
    module: 'show-case',
    node: '@sage/xtrem-show-case/ShowCaseProvider',
    title: 'Field - Count',
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.text({ bind: '_id' }),
            titleRight: ui.nestedFields.count({ bind: 'products', prefix: 'qty:' }),
        },
    },
    onLoad() {
        this.$.showToast(`The field value is: ${this.totalCountOfProducts.value}`);
    },
    menuItem: fields,
})
export class Count extends ui.Page<GraphApi> {
    @ui.decorators.section<Count>({
        title: 'Count field',
    })
    section: ui.containers.Section;

    @ui.decorators.block<Count>({
        parent() {
            return this.section;
        },
        title: 'Field example',
    })
    fieldBlock: ui.containers.Block;

    @ui.decorators.countField<Count>({
        title: 'Total number of products',
        parent() {
            return this.fieldBlock;
        },
        bind: 'products',
    })
    totalCountOfProducts: ui.fields.Count;

    @ui.decorators.countField<Count, ShowCaseProduct>({
        title: 'Total number of products (with qty field greater than 15)',
        parent() {
            return this.fieldBlock;
        },
        filter: {
            qty: { _gt: '15' },
        },
        bind: 'products',
    })
    filteredCountOfProducts: ui.fields.Count;

    @ui.decorators.numericField<Count>({
        isTransient: true,
        scale: 0,
        parent() {
            return this.fieldBlock;
        },
        title: 'Set count field value',
        onChange() {
            this.totalCountOfProducts.value = this.updateValue.value || 0;
        },
    })
    updateValue: ui.fields.Numeric;

    @ui.decorators.buttonField<Count>({
        isTransient: true,
        title: 'Set value to null',
        map() {
            return 'Set value to null';
        },
        parent() {
            return this.fieldBlock;
        },
        onClick() {
            this.totalCountOfProducts.value = null;
        },
    })
    disableBusinessAction1: ui.fields.Button;
}
