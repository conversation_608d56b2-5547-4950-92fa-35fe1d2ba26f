import {
    ShowCase<PERSON>ustomer,
    ShowCaseInvoice,
    ShowCaseInvoiceLine,
    ShowCaseOrder,
    ShowCaseProduct,
} from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import { validation } from '../menu-items/validation';

@ui.decorators.page<NestedGridInfiniteScroll, ShowCaseCustomer>({
    authorizationCode: 'BSCFLDS',
    module: 'show-case',
    category: 'SHOWCASE',
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.text({ bind: '_id' }),
            titleLineLeft: ui.nestedFields.text({ bind: 'name' }),
            titleRight: ui.nestedFields.text({ bind: 'email' }),
        },
    },
    node: '@sage/xtrem-show-case/ShowCaseCustomer',
    title: 'Field - NestedGrid Infinite Scroll',
    businessActions() {
        return [this.$standardCancelAction, this.$standardSaveAction];
    },
    menuItem: validation,
})
export class NestedGridInfiniteScroll extends ui.Page {
    @ui.decorators.section<NestedGridInfiniteScroll>({
        title: 'Nested grid with infinite scroll',
        isTitleHidden: true,
    })
    section: ui.containers.Section;

    @ui.decorators.nestedGridField<NestedGridInfiniteScroll, [ShowCaseOrder, ShowCaseInvoice, ShowCaseInvoiceLine]>({
        bind: 'orders',
        title: 'Test field with infinite scroll',
        levels: [
            {
                node: '@sage/xtrem-show-case/ShowCaseOrder',
                childProperty: 'invoices',
                columns: [
                    ui.nestedFields.text({
                        bind: '_id',
                        title: 'ID',
                        isHiddenDesktop: false,
                        isReadOnly: true,
                    }),
                    ui.nestedFields.date({
                        bind: 'orderDate',
                        title: 'Order date',
                        isMandatory: true,
                        canFilter: true,
                    }),
                ],
            },
            {
                node: '@sage/xtrem-show-case/ShowCaseInvoice',
                childProperty: 'lines',
                orderBy: { _id: 1 },
                columns: [
                    ui.nestedFields.text({
                        bind: '_id',
                        title: 'ID',
                        isHiddenDesktop: false,
                        isReadOnly: true,
                    }),
                    ui.nestedFields.numeric({
                        bind: 'totalProductQty',
                        title: 'Total quantity',
                        min: 0,
                        canFilter: true,
                    }),
                    ui.nestedFields.date({
                        bind: 'purchaseDate',
                        title: 'Purchase date',
                        canFilter: true,
                    }),
                ],
            },
            {
                node: '@sage/xtrem-show-case/ShowCaseInvoiceLine',
                columns: [
                    ui.nestedFields.text({
                        bind: '_id',
                        title: 'Id',
                        isHiddenDesktop: false,
                        isReadOnly: true,
                    }),
                    ui.nestedFields.numeric({
                        bind: 'orderQuantity',
                        title: 'Quantity',
                        scale: 0,
                        min: 0,
                        canFilter: true,
                    }),
                    ui.nestedFields.numeric({
                        bind: 'netPrice',
                        title: 'Net price',
                        scale: 2,
                        canFilter: true,
                    }),
                    ui.nestedFields.reference<NestedGridInfiniteScroll, ShowCaseInvoiceLine, ShowCaseProduct>({
                        bind: 'product',
                        node: '@sage/xtrem-show-case/ShowCaseProduct',
                        valueField: { product: true },
                        helperTextField: { _id: true },
                        isMandatory: true,
                        title: 'Product',
                        canFilter: true,
                    }),
                ],
            },
        ],
        parent() {
            return this.section;
        },
    })
    field: ui.fields.NestedGrid<[ShowCaseOrder, ShowCaseInvoice, ShowCaseInvoiceLine], NestedGridInfiniteScroll>;
}
