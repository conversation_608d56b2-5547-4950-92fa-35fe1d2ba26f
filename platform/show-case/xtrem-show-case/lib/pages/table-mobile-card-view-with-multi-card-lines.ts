import { Graph<PERSON><PERSON> } from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import { tableField } from '../menu-items/_index';

@ui.decorators.page<TableMobileCardViewWithMultiCardLines>({
    authorizationCode: 'TBLMBCRD',
    category: 'SHOWCASE',
    isTransient: true,
    module: 'show-case',
    title: 'Mobile card view with multi card lines',
    menuItem: tableField,
})
export class TableMobileCardViewWithMultiCardLines extends ui.Page<GraphApi> {
    @ui.decorators.section<TableMobileCardViewWithMultiCardLines>({
        isTitleHidden: true,
    })
    section: ui.containers.Section;

    @ui.decorators.block<TableMobileCardViewWithMultiCardLines>({
        parent() {
            return this.section;
        },
    })
    block: ui.containers.Block;

    @ui.decorators.pageAction<TableMobileCardViewWithMultiCardLines>({
        title: 'Load',
        icon: 'circles_connection',
        buttonType: 'primary',
        async onClick() {
            const result = await this.$.graph
                .node('@sage/xtrem-show-case/ShowCaseProduct')
                .query(
                    ui.queryUtils.edgesSelector({
                        _id: true,
                        amount: true,
                        barcode: true,
                        imageField: { value: true },
                        product: true,
                        releaseDate: true,
                        tax: true,
                    }),
                )
                .execute();

            const products = result.edges
                .map((edge: any) => {
                    return edge.node;
                })
                .map((node: any, index: number) => {
                    const productResult = { ...node };

                    if (index % 2 === 0) {
                        productResult.imageField = undefined;
                    }

                    if (index % 3 === 0) {
                        productResult.product = undefined;
                    }

                    return productResult;
                });

            this.field.value = products;
        },
    })
    button: ui.PageAction;

    @ui.decorators.tableField<TableMobileCardViewWithMultiCardLines>({
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        cardView: true,
        headerBusinessActions() {
            return [this.button];
        },
        canSelect: false,
        dropdownActions: [
            {
                id: 'edit',
                icon: 'edit',
                title: 'Edit',
                onClick() {
                    // Intentionally left empty
                },
            },
            {
                id: 'delete',
                icon: 'delete',
                title: 'Delete',
                onClick() {
                    // Intentionally left empty
                },
            },
        ],
        optionsMenu() {
            const thisYear = new Date().getFullYear();
            const lastYear = thisYear - 1;
            return [
                {
                    title: 'All',
                    graphQLFilter: {},
                },
                {
                    title: 'This year',
                    graphQLFilter: {
                        _and: [
                            { releaseDate: { _gte: `${thisYear}-01-01` } },
                            { releaseDate: { _lte: `${thisYear}-12-31` } },
                        ],
                    },
                },
                {
                    title: 'Last year',
                    graphQLFilter: {
                        _and: [
                            { releaseDate: { _gte: `${lastYear}-01-01` } },
                            { releaseDate: { _lte: `${lastYear}-12-31` } },
                        ],
                    },
                },
                {
                    title: 'Old orders',
                    graphQLFilter: {
                        _and: [{ releaseDate: { _lte: `${lastYear - 1}-12-31` } }],
                    },
                },
            ];
        },
        recordWidth: 'small',
        areCardFieldTitlesDisplayed: true,
        columns: [
            ui.nestedFields.text({
                bind: '_id',
                title: 'Id',
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                bind: 'product',
                title: 'Product',
                isReadOnly: true,
            }),
            ui.nestedFields.date({
                bind: 'releaseDate',
                title: 'Release date',
                isReadOnly: true,
            }),
            ui.nestedFields.numeric({
                bind: 'amount',
                title: 'Price',
                prefix: '$',
                scale: 2,
            }),
            ui.nestedFields.image({
                bind: 'imageField',
                title: 'Image',
                isReadOnly: true,
            }),
        ],
        mobileCard: {
            title: ui.nestedFields.text({
                bind: 'product',
                title: 'Product',
            }),
            titleRight: ui.nestedFields.text({
                bind: 'barcode',
                title: 'Barcode',
            }),
            line2: ui.nestedFields.date({
                bind: 'releaseDate',
                title: 'Release date',
            }),
            line2Right: ui.nestedFields.text({
                bind: '_id',
                title: 'ID',
            }),
            image: ui.nestedFields.image({
                bind: 'imageField',
                title: 'Image',
            }),
        },
        orderBy: { _id: 1 },
        parent() {
            return this.block;
        },
    })
    field: ui.fields.Table;
}
