import { GraphApi, ShowCaseProduct } from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import { textField } from '../menu-items/text-field';

@ui.decorators.page<TextWithPassword, ShowCaseProduct>({
    authorizationCode: 'TXTPSWRD',
    businessActions() {
        return [this.action];
    },
    category: 'SHOWCASE',
    defaultEntry: () => '313',
    menuItem: textField,
    module: 'show-case',
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.text({ bind: 'product' }),
            titleRight: ui.nestedFields.label({ bind: '_id' }),
            line2: ui.nestedFields.text({ bind: 'description', canFilter: false }),
        },
    },
    node: '@sage/xtrem-show-case/ShowCaseProduct',
    title: 'Text (with Password)',
})
export class TextWithPassword extends ui.Page<GraphApi> {
    @ui.decorators.section<TextWithPassword>({
        title: 'Text Field with Password',
    })
    section: ui.containers.Section;

    @ui.decorators.block<TextWithPassword>({
        parent() {
            return this.section;
        },
    })
    block: ui.containers.Block;

    @ui.decorators.textField<TextWithPassword>({
        bind: 'description',
        isReadOnly: true,
        parent() {
            return this.block;
        },
        title: 'Description',
    })
    description: ui.fields.Text;

    @ui.decorators.textField<TextWithPassword>({
        bind: 'description',
        isPassword: true,
        parent() {
            return this.block;
        },
        title: 'Password',
    })
    password: ui.fields.Text;

    @ui.decorators.pageAction<TextWithPassword>({
        title: 'Action',
        onClick() {
            ui.console.log('Trigger page action.');
        },
    })
    action: ui.PageAction;
}
