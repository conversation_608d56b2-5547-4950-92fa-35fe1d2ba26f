import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ase<PERSON><PERSON>ider, ShowCaseProduct, ShowCaseProductOriginAddress } from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import { referenceField } from '../menu-items/reference-field';

@ui.decorators.page<VitalPayloadReference, ShowCaseProvider>({
    authorizationCode: 'SHCPRVD',
    category: 'SHOWCASE',
    createAction() {
        return this.$standardNewAction;
    },
    headerDropDownActions() {
        return [this.$standardDeleteAction];
    },
    businessActions() {
        return [this.$standardCancelAction, this.$standardSaveAction];
    },

    navigationPanel: {
        listItem: {
            title: ui.nestedFields.text({ bind: 'textField' }),
            titleRight: ui.nestedFields.text({ bind: '_id' }),
        },
    },
    node: '@sage/xtrem-show-case/ShowCaseProvider',
    title: 'Vital Payload reference',
    menuItem: referenceField,
})
export class VitalPayloadReference extends ui.Page<GraphApi> {
    @ui.decorators.section<VitalPayloadReference>({})
    section: ui.containers.Section;

    @ui.decorators.block<VitalPayloadReference>({
        parent() {
            return this.section;
        },
    })
    block: ui.containers.Block;

    @ui.decorators.textField<VitalPayloadReference>({
        parent() {
            return this.block;
        },
        title: 'Id',
        isReadOnly: true,
    })
    _id: ui.fields.Text;

    @ui.decorators.dateField<VitalPayloadReference>({
        parent() {
            return this.block;
        },
        title: 'Date',
    })
    dateField: ui.fields.Date;

    @ui.decorators.checkboxField<VitalPayloadReference>({
        parent() {
            return this.block;
        },
        title: 'Checkbox',
    })
    booleanField: ui.fields.Checkbox;

    @ui.decorators.referenceField<VitalPayloadReference, ShowCaseProduct>({
        parent() {
            return this.block;
        },
        title: 'Flagship product',
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        orderBy: { description: 1 },
        columns: [
            ui.nestedFields.text({ bind: '_id', isReadOnly: true, title: 'Id' }),
            ui.nestedFields.text({ bind: 'description', title: 'Description' }),
            ui.nestedFields.checkbox({ bind: 'hotProduct', isReadOnly: true, title: 'Hot' }),
            ui.nestedFields.select({ bind: 'category', isReadOnly: true, title: 'Category' }),
            ui.nestedFields.reference<VitalPayloadReference, ShowCaseProduct, ShowCaseProductOriginAddress>({
                bind: 'originAddress',
                title: 'Origin Address',
                node: '@sage/xtrem-show-case/ShowCaseProductOriginAddress',
                valueField: 'name',
            }),
            ui.nestedFields.numeric({ bind: 'total', title: 'Net Price', scale: 2 }),
        ],
        valueField: 'description',
        helperTextField: '_id',
        additionalLookupRecords() {
            return this.products.getNewRecords();
        },
    })
    flagshipProduct: ui.fields.Reference;

    @ui.decorators.tableField<VitalPayloadReference, ShowCaseProduct>({
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        parent() {
            return this.block;
        },
        fieldActions() {
            return [this.addNewRecord];
        },
        title: 'Products',
        canFilter: false,
        orderBy: { _id: 1 },
        columns: [
            ui.nestedFields.text({ bind: '_id', isReadOnly: true, title: 'Id' }),
            ui.nestedFields.link({
                bind: 'product',
                title: 'Product',
                onClick(_id) {
                    this.$.router.goTo('@sage/xtrem-show-case/ShowCaseProduct', { _id });
                },
            }),
            ui.nestedFields.text({ bind: 'description', title: 'Description' }),
            ui.nestedFields.checkbox({ bind: 'hotProduct', isReadOnly: true, title: 'Hot' }),
            ui.nestedFields.select({ bind: 'category', isReadOnly: true, title: 'Category' }),
            ui.nestedFields.numeric({ bind: 'total', title: 'Net Price', scale: 2 }),
        ],
        dropdownActions: [
            {
                icon: 'bin',
                title: 'Remove',
                isDestructive: true,
                async onClick(rowId: any, data: any) {
                    await this.$.graph.delete({
                        _id: data._id,
                        nodeName: '@sage/xtrem-show-case/ShowCaseProduct',
                    });
                    await this.products.refresh();
                },
            },
        ],
    })
    products: ui.fields.Table<ShowCaseProduct, VitalPayloadReference>;

    @ui.decorators.pageAction<VitalPayloadReference>({
        title: 'Add new record',
        icon: 'add',
        onError: () => '',
        async onClick() {
            const result = await this.$.dialog.page(
                '@sage/xtrem-show-case/ShowCaseProductDialog',
                {},
                { rightAligned: true },
            );
            this.products.addRecord(result);
        },
    })
    addNewRecord: ui.PageAction;
}
