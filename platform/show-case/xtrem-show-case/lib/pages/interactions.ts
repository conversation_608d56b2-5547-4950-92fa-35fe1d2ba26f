import * as ui from '@sage/xtrem-ui';
import { misc } from '../menu-items/misc';

@ui.decorators.page<Interactions>({
    authorizationCode: 'BSCINCTS',
    module: 'show-case',
    title: 'Event listeners',
    isTransient: true,
    category: 'SHOWCASE',
    onLoad() {
        this.interactionCalcDescription.value =
            'Fill the following inputs with numbers and add them using the "Sum" button.';
        this.interactionCalcInput1.value = 1.0;
        this.interactionCalcInput2.value = 1.0;
        this.interactionCalcButton.value = 'Sum';
        this.interactionCalcResult.value = 2.0;
        this.interactionDependentFieldsDescription.value = "Based on the checkbox's value, other fields change";
        this.interactionDependentFieldsCheckbox.value = false;
        this.interactionDependentFieldsTextField1.value = '-(';
        this.interactionDependentFieldsTextField2.value = '';
        this.interactionValidationLabel.value = 'Is Valid';
    },
    menuItem: misc,
})
export class Interactions extends ui.Page {
    @ui.decorators.section<Interactions>({
        title: 'Interactions',
    })
    interactionsSection: ui.containers.Section;

    @ui.decorators.block<Interactions>({
        parent() {
            return this.interactionsSection;
        },
        title: 'Simple Calculation',
    })
    interactionCalcBlock: ui.containers.Block;

    @ui.decorators.textAreaField<Interactions>({
        parent() {
            return this.interactionCalcBlock;
        },
        isReadOnly: true,
    })
    interactionCalcDescription: ui.fields.TextArea;

    @ui.decorators.numericField<Interactions>({
        parent() {
            return this.interactionCalcBlock;
        },
        title: 'First Number',
        scale: 1,
    })
    interactionCalcInput1: ui.fields.Numeric;

    @ui.decorators.numericField<Interactions>({
        parent() {
            return this.interactionCalcBlock;
        },
        title: 'Second Number',
        scale: 1,
    })
    interactionCalcInput2: ui.fields.Numeric;

    @ui.decorators.buttonField<Interactions>({
        parent() {
            return this.interactionCalcBlock;
        },
        map: () => 'Calculate',
        onClick() {
            this.onInteractionCalcButtonClick();
        },
    })
    interactionCalcButton: ui.fields.Button;

    @ui.decorators.numericField<Interactions>({
        parent() {
            return this.interactionCalcBlock;
        },
        title: 'Result',
        isReadOnly: true,
        scale: 1,
    })
    interactionCalcResult: ui.fields.Numeric;

    @ui.decorators.block<Interactions>({
        parent() {
            return this.interactionsSection;
        },
        title: 'Dependent fields',
    })
    interactionDependentFieldsBlock: ui.containers.Block;

    @ui.decorators.textAreaField<Interactions>({
        parent() {
            return this.interactionDependentFieldsBlock;
        },
        isReadOnly: true,
    })
    interactionDependentFieldsDescription: ui.fields.TextArea;

    @ui.decorators.checkboxField<Interactions>({
        parent() {
            return this.interactionDependentFieldsBlock;
        },
        title: 'Check me!',
        onChange() {
            this.onInteractionDependentFieldChange();
        },
    })
    interactionDependentFieldsCheckbox: ui.fields.Checkbox;

    @ui.decorators.textField<Interactions>({
        parent() {
            return this.interactionDependentFieldsBlock;
        },
        title: 'I am disabled',
        isDisabled: true,
    })
    interactionDependentFieldsTextField1: ui.fields.Text;

    @ui.decorators.textField<Interactions>({
        parent() {
            return this.interactionDependentFieldsBlock;
        },
        title: 'Wow I just appeared here',
        isHidden: true,
    })
    interactionDependentFieldsTextField2: ui.fields.Text;

    @ui.decorators.block<Interactions>({
        parent() {
            return this.interactionsSection;
        },
        title: 'Validation',
    })
    interactionValidationBlock: ui.containers.Block;

    @ui.decorators.textField<Interactions>({
        parent() {
            return this.interactionValidationBlock;
        },
        title: 'Another mandatory',
        isMandatory: true,
    })
    interactionValidationField: ui.fields.Text;

    @ui.decorators.labelField<Interactions>({
        parent() {
            return this.interactionValidationBlock;
        },
        title: 'Simple Label',
    })
    interactionValidationLabel: ui.fields.Label;

    @ui.decorators.buttonField<Interactions>({
        parent() {
            return this.interactionValidationBlock;
        },
        map: () => 'Validate page',
        onClick() {
            this.$.loader.display();
            setTimeout(() => {
                this.$.page
                    .validate()
                    .then(errorMessages => {
                        this.$.loader.hide();
                        if (errorMessages.length) {
                            this.$.dialog.message(
                                'error',
                                'Validation errors',
                                'Some fields have validation errors. Please fix them before proceeding',
                            );
                        } else {
                            this.$.dialog.message('info', 'Successful validation', 'All of the fields are correct');
                        }
                    })
                    .catch(e => {
                        throw e;
                    });
            }, 2000);
        },
    })
    interactionPageValidation: ui.fields.Button;

    @ui.decorators.block<Interactions>({
        title: 'Miscellaneous interactions',
        parent() {
            return this.interactionsSection;
        },
    })
    miscellaneousBlock: ui.containers.Block;

    @ui.decorators.textField<Interactions>({
        parent() {
            return this.miscellaneousBlock;
        },
        title: 'New Page name',
        isTransient: true,
    })
    textFieldPageTitle: ui.fields.Text;

    @ui.decorators.buttonField<Interactions>({
        parent() {
            return this.miscellaneousBlock;
        },
        map() {
            return 'Change the page title!';
        },
        onClick() {
            this.$.page.title = this.textFieldPageTitle.value;
        },
    })
    buttonChangeTitle: ui.fields.Button;

    onInteractionCalcButtonClick() {
        const sum = this.interactionCalcInput1.value + this.interactionCalcInput2.value;
        this.interactionCalcResult.value = sum;
    }

    /* Dependent fields */

    onInteractionDependentFieldChange() {
        const value = !this.interactionDependentFieldsCheckbox.value;
        this.interactionDependentFieldsTextField2.isHidden = value;
        this.interactionDependentFieldsTextField1.isDisabled = value;
        if (!value) {
            this.interactionDependentFieldsTextField1.value = 'Yay!';
        } else {
            this.interactionDependentFieldsTextField1.value = '...not again';
        }
    }

    @ui.decorators.block<Interactions>({
        title: 'Default error handling',
        parent() {
            return this.interactionsSection;
        },
    })
    errorHandling: ui.containers.Block;

    @ui.decorators.buttonField<Interactions>({
        parent() {
            return this.errorHandling;
        },
        map() {
            return ui.localize('@sage/xtrem-show-case/pages__interactions_throw-exception', 'Throw exception');
        },
        onClick() {
            this.$.loader.display();
            return new Promise((resolve, reject) => setTimeout(() => reject(new Error('Ooops!')), 2000));
        },
    })
    throwException: ui.fields.Button;

    @ui.decorators.buttonField<Interactions>({
        parent() {
            return this.miscellaneousBlock;
        },
        map: () => 'Go home',
        onClick() {
            return this.$.router.goHome();
        },
    })
    goHomeButton: ui.fields.Button;
}
