import { GraphApi, ShowCaseProduct as ShowCaseProductNode } from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import { applicationPages } from '../menu-items/application-pages';

@ui.decorators.page<ShowCaseProductReturnArrow, ShowCaseProductNode>({
    authorizationCode: 'SHCPRDT',
    module: 'show-case',
    title: 'ShowCase Product Return Arrow',
    objectTypeSingular: 'Product',
    objectTypePlural: 'Products',
    idField() {
        return this.product;
    },
    node: '@sage/xtrem-show-case/ShowCaseProduct',
    menuItem: applicationPages,
    category: 'SHOWCASE',
    headerQuickActions() {
        return [this.$standardDuplicateAction];
    },
    onLoad() {
        this.$.storage.set('filterNavPanelItems', true);
    },
    onClose() {
        ui.console.log('on close main called');
    },
    navigationPanel: {
        bulkActions: [
            {
                mutation: 'export',
                title: 'Export',
                buttonType: 'secondary',
                icon: 'file_excel',
                isDestructive: false,
                id: 'export',
            },
            {
                mutation: 'addPrefixToProductName',
                title: 'Add prefix to name',
                buttonType: 'secondary',
                icon: 'edit',
                configurationPage: '@sage/xtrem-show-case/ShowCaseProductRenameActionDialog',
                isDestructive: false,
                id: 'addPrefixToProductName',
            },
        ],
        dropdownActions: [
            {
                id: 'someAction',
                title: 'Some action',
                icon: 'chart_pie',
                onClick(_recordId, recordValue) {
                    this.$.dialog.message('info', recordValue._id, `It is ${recordValue.product}`);
                },
            },
        ],
        inlineActions: [
            {
                id: 'delete',
                title: 'Add an X in front of product name',
                async onClick() {
                    this.$.dialog.message('info', '', 'Added X in front of product name');
                },
                icon: 'delete',
                isDestructive: true,
            },
        ],
        listItem: {
            title: ui.nestedFields.text({ bind: 'product', title: 'Product' }),
            titleRight: ui.nestedFields.text({ bind: '_id', title: 'ID', canFilter: false }),
            line2: ui.nestedFields.text({ bind: 'description', title: 'Description', canFilter: true }),
            listPrice: ui.nestedFields.numeric({ bind: 'listPrice', title: 'List Price', isHiddenOnMainField: true }),
            category: ui.nestedFields.label({
                canFilter: true,
                bind: 'category',
                title: 'Category',
                optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
            }),
            netPrice: ui.nestedFields.numeric({ bind: 'netPrice', title: 'Net Price', isHiddenOnMainField: true }),
            tax: ui.nestedFields.numeric({ bind: 'tax', title: 'Tax Price', isHiddenOnMainField: true }),
            hotProduct: ui.nestedFields.checkbox({
                bind: 'hotProduct',
                title: 'Is hot product',
                isHiddenOnMainField: true,
            }),

            endDate: ui.nestedFields.date({
                bind: 'endingDate',
                title: 'Ending date',
                isHiddenOnMainField: true,
            }),
        },
    },
    businessActions() {
        return [this.refreshPage, this.emptyPage, this.$standardSaveAction];
    },
    createAction() {
        return this.$standardNewAction;
    },
    headerDropDownActions() {
        return [this.$standardDeleteAction];
    },
    priority: 100,
})
export class ShowCaseProductReturnArrow extends ui.Page<GraphApi> {
    @ui.decorators.section<ShowCaseProductReturnArrow>({})
    section: ui.containers.Section;

    @ui.decorators.block<ShowCaseProductReturnArrow>({
        parent() {
            return this.section;
        },
    })
    block: ui.containers.Block;

    @ui.decorators.textField<ShowCaseProductReturnArrow>({
        parent() {
            return this.block;
        },
        title: 'ID',
        isReadOnly: true,
    })
    _id: ui.fields.Text;

    @ui.decorators.textField<ShowCaseProductReturnArrow>({
        parent() {
            return this.block;
        },
        title: 'Product',
        isMandatory: true,
    })
    product: ui.fields.Text;

    @ui.decorators.textField<ShowCaseProductReturnArrow>({
        parent() {
            return this.block;
        },
        title: 'Description',
    })
    description: ui.fields.Text;

    @ui.decorators.checkboxField<ShowCaseProductReturnArrow>({
        parent() {
            return this.block;
        },
        title: 'Hot product',
    })
    hotProduct: ui.fields.Checkbox;

    @ui.decorators.numericField<ShowCaseProductReturnArrow>({
        parent() {
            return this.block;
        },
        title: 'Quantity',
        fetchesDefaults: true,
    })
    qty: ui.fields.Numeric;

    @ui.decorators.numericField<ShowCaseProductReturnArrow>({
        parent() {
            return this.block;
        },
        title: 'Stock',
    })
    st: ui.fields.Numeric;

    @ui.decorators.numericField<ShowCaseProductReturnArrow>({
        parent() {
            return this.block;
        },
        title: 'List price',
        scale: 2,
        fetchesDefaults: true,
    })
    listPrice: ui.fields.Numeric;

    @ui.decorators.progressField<ShowCaseProductReturnArrow>({
        parent() {
            return this.block;
        },
        title: 'Progress',
    })
    progress: ui.fields.Progress;

    @ui.decorators.numericField<ShowCaseProductReturnArrow>({
        parent() {
            return this.block;
        },
        title: 'Tax',
        scale: 2,
    })
    tax: ui.fields.Numeric;

    @ui.decorators.numericField<ShowCaseProductReturnArrow>({
        parent() {
            return this.block;
        },
        title: 'Net price',
        scale: 2,
    })
    netPrice: ui.fields.Numeric;

    @ui.decorators.numericField<ShowCaseProductReturnArrow>({
        parent() {
            return this.block;
        },
        title: 'Discount',
    })
    discount: ui.fields.Numeric;

    @ui.decorators.numericField<ShowCaseProductReturnArrow>({
        parent() {
            return this.block;
        },
        title: 'Amount',
        scale: 2,
    })
    amount: ui.fields.Numeric;

    @ui.decorators.linkField<ShowCaseProductReturnArrow>({
        title: 'Go to ShowCaseInvoice',
        isTransient: true,
        parent() {
            return this.block;
        },
        map() {
            return 'Go to Invoice';
        },
        onClick() {
            this.$.router.goTo('@sage/xtrem-show-case/ShowCaseInvoice');
        },
    })
    goToShowCaseInvoice: ui.fields.Link;

    @ui.decorators.dateField<ShowCaseProductReturnArrow>({
        parent() {
            return this.block;
        },
        title: 'Release date',
    })
    releaseDate: ui.fields.Date;

    @ui.decorators.dateField<ShowCaseProductReturnArrow>({
        parent() {
            return this.block;
        },
        title: 'Ending date',
    })
    endingDate: ui.fields.Date;

    @ui.decorators.radioField<ShowCaseProductReturnArrow>({
        parent() {
            return this.block;
        },
        title: 'Category Select',
        optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
    })
    category: ui.fields.Radio;

    @ui.decorators.dropdownListField<ShowCaseProductReturnArrow>({
        bind: 'category',
        optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
        parent() {
            return this.block;
        },
        title: 'Category (Dropdown)',
    })
    category2: ui.fields.DropdownList;

    @ui.decorators.multiDropdownField<ShowCaseProductReturnArrow>({
        parent() {
            return this.block;
        },
        title: 'Subcategories',
        optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
    })
    subcategories: ui.fields.MultiDropdown;

    @ui.decorators.multiDropdownField<ShowCaseProductReturnArrow>({
        parent() {
            return this.block;
        },
        isSortedAlphabetically: true,
        title: 'Entries',
        options: ['one', 'two', 'three', 'four', 'five'],
    })
    entries: ui.fields.MultiDropdown;

    @ui.decorators.fileDepositField<ShowCaseProductReturnArrow>({
        parent() {
            return this.block;
        },
        fileTypes: 'text/js, image/*',
        title: 'Certificate',
        node: '@sage/xtrem-upload/UploadedFile',
        kind: 'upload',
    })
    certificate: ui.fields.FileDeposit;

    @ui.decorators.pageAction<ShowCaseProductReturnArrow>({
        title: 'Create',
        onClick() {
            this.$.loader.display();
            const values = this.$.values;
            delete values._id;
            return this.$.graph
                .create({ values })
                .then(() => {
                    this.$.finish();
                })
                .catch(() => {
                    this.$.showToast('Something went wrong', { type: 'error' });
                })
                .finally(() => {
                    this.$.loader.hide();
                });
        },
    })
    createShowCaseProduct: ui.PageAction;

    @ui.decorators.pageAction<ShowCaseProductReturnArrow>({
        title: 'Save',
        async onClick() {
            this.$.loader.display();
            const result = await this.$.page.validateWithDetails();

            if (result.length) {
                // INFO: Check used to demonstrate internal component error in the integration tests, as
                //       I was unable to test a toast notification directly.
                const dateResult = result.find(validationResult => validationResult.validationRule === 'dateValue');

                if (dateResult) {
                    this.$.dialog.message('error', 'Validation Error', dateResult.message, {
                        fullScreen: false,
                        rightAligned: false,
                        acceptButton: {
                            isDisabled: false,
                            isHidden: false,
                            text: 'OK',
                        },
                    });
                } else {
                    this.$.showToast(result.map(r => r.message).join(', '), { type: 'warning' });
                }

                this.$.loader.hide();
                return;
            }

            this.$.graph
                .update()
                .then(() => {
                    this.$.finish();
                })
                .catch(() => {
                    this.$.showToast('Something went wrong', { type: 'error' });
                })
                .finally(() => {
                    this.$.loader.hide();
                });
        },
    })
    saveShowCaseProduct: ui.PageAction;

    @ui.decorators.block<ShowCaseProductReturnArrow>({
        access: {
            node: '@sage/xtrem-show-case/ShowCaseProduct',
            bind: 'discount',
        },
        parent() {
            return this.section;
        },
        title: 'Access controlled block',
    })
    accessControlledBlock: ui.containers.Block;

    @ui.decorators.numericField<ShowCaseProductReturnArrow>({
        helperText: 'This field should be disabled if the parent block is blocked by access rights',
        parent() {
            return this.accessControlledBlock;
        },
        isTransient: true,
        title: 'Block access one',
        scale: 2,
    })
    blockAccessControlledField1: ui.fields.Numeric;

    @ui.decorators.numericField<ShowCaseProductReturnArrow>({
        helperText: 'This field should be disabled if the parent block is blocked by access rights',
        parent() {
            return this.accessControlledBlock;
        },
        isTransient: true,
        title: 'Block access two',
        scale: 2,
    })
    blockAccessControlledField2: ui.fields.Numeric;

    @ui.decorators.pageAction<ShowCaseProductReturnArrow>({
        title: 'Refresh',
        onClick() {
            return this.$.router.refresh();
        },
    })
    refreshPage: ui.PageAction;

    @ui.decorators.pageAction<ShowCaseProductReturnArrow>({
        title: 'Empty page',
        onClick() {
            return this.$.router.emptyPage();
        },
    })
    emptyPage: ui.PageAction;

    @ui.decorators.pageAction<ShowCaseProductReturnArrow>({
        title: 'Save',
        async onClick() {
            try {
                await this.$.graph
                    .node('@sage/xtrem-show-case/ShowCaseProduct')
                    .update({ _id: true }, { data: this.$.values })
                    .execute();
            } catch (error) {
                this.$.processServerErrors(error);
            }
        },
    })
    customSave: ui.PageAction;

    @ui.decorators.pageAction<ShowCaseProductReturnArrow>({
        title: 'Delete',
        async onClick() {
            const options: ui.dialogs.DialogOptions = {
                acceptButton: {
                    text: 'Yes',
                },
                cancelButton: {
                    text: 'No',
                },
            };
            if (
                await this.$.dialog
                    .confirmation(
                        'warn',
                        ui.localize('@sage/xtrem-show-case/delete-dialog-title', 'Confirm deletion'),
                        ui.localize(
                            '@sage/xtrem-show-case/delete-dialog-content',
                            'You are about to delete this record.',
                        ),
                        options,
                    )
                    .then(() => true)
                    .catch(() => false)
            ) {
                this.$.loader.display();
                try {
                    await this.$.graph.delete();
                    this.$.showToast(ui.localize('@sage/xtrem-show-case/delete-confirmation', 'Record deleted'), {
                        type: 'success',
                    });
                } catch (e) {
                    this.$.showToast(e.message, { timeout: 0, type: 'error' });
                }
                this.$.router.goTo(`@sage/xtrem-show-case/${this.$.page.id}`);
                this.$.loader.hide();
            }
        },
    })
    deleteShowCaseProduct: ui.PageAction;
}
