import { Graph<PERSON><PERSON>, ShowCaseInvoice } from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import type { PdfPluginProperties } from '@sage/xtrem-ui-plugin-pdf';
import { plugins } from '../menu-items/plugins';

@ui.decorators.page<PluginPdf, ShowCaseInvoice>({
    authorizationCode: 'SHCPRVD',
    category: 'SHOWCASE',
    businessActions() {
        return [this.update];
    },
    module: 'show-case',
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.reference({
                bind: 'customer',
                valueField: 'name',
                node: '@sage/xtrem-show-case/ShowCaseCustomer',
            }),
            line2: ui.nestedFields.text({ bind: '_id' }),
        },
    },
    node: '@sage/xtrem-show-case/ShowCaseInvoice',
    title: 'Field - PDF Plugin',
    menuItem: plugins,
    onLoad() {
        this.height.value = 500;
    },
})
export class PluginPdf extends ui.Page<GraphApi> {
    @ui.decorators.section<PluginPdf>({
        isTitleHidden: true,
    })
    section: ui.containers.Section;

    @ui.decorators.block<PluginPdf>({
        parent() {
            return this.section;
        },
    })
    block: ui.containers.Block;

    @ui.decorators.textField<PluginPdf>({
        parent() {
            return this.block;
        },
        title: 'Id',
        isReadOnly: true,
    })
    _id: ui.fields.Text;

    @ui.decorators.numericField<PluginPdf>({
        parent() {
            return this.block;
        },
        isTransient: true,
        title: 'Height',
        helperText: 'PDF field height in pixels',
        min: 50,

        max: 1000,
        onChange() {
            this.pdf.setProperty('height', this.height.value);
        },
    })
    height: ui.fields.Numeric;

    @ui.decorators.pluginField<PluginPdf, PdfPluginProperties>({
        parent() {
            return this.block;
        },
        title: 'PDF',
        pluginPackage: '@sage/xtrem-ui-plugin-pdf',
        height: 500,
        isFullWidth: true,
    })
    pdf: ui.fields.Plugin<PdfPluginProperties>;

    @ui.decorators.pageAction<PluginPdf>({
        title: 'Save',
        async onClick() {
            await this.$.graph.update();
            this.$.showToast(`Updated entry: ${this._id.value}`, {
                type: 'success',
            });
        },
    })
    update: ui.PageAction;
}
