import { Filter } from '@sage/xtrem-client';
import { ShowCaseProduct, ShowCaseProvider } from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import { tableField } from '../menu-items/_index';

@ui.decorators.page<TableFilterRefresh, ShowCaseProvider>({
    authorizationCode: 'TBLFLTRFSH',
    category: 'SHOWCASE',
    defaultEntry: () => '2',
    module: 'show-case',
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.text({ bind: 'textField' }),
            titleRight: ui.nestedFields.text({ bind: '_id' }),
        },
    },
    node: '@sage/xtrem-show-case/ShowCaseProvider',
    title: 'Field - Table (Refresh w/ Filter)',
    menuItem: tableField,
})
export class TableFilterRefresh extends ui.Page {
    @ui.decorators.section<TableFilterRefresh>({})
    section: ui.containers.Section;

    @ui.decorators.block<TableFilterRefresh>({
        title: 'Filter Configuration',
        parent() {
            return this.section;
        },
    })
    filterBlock: ui.containers.Block;

    @ui.decorators.numericField<TableFilterRefresh>({
        isTransient: true,
        title: 'Minimum Price',
        onChange() {
            this.table.filter = this.getUpdatedTableFilter();
        },
        parent() {
            return this.filterBlock;
        },
    })
    priceFilter: ui.fields.Numeric;

    @ui.decorators.textField<TableFilterRefresh>({
        isTransient: true,
        title: 'Description Includes',
        onChange() {
            this.table.filter = this.getUpdatedTableFilter();
        },
        parent() {
            return this.filterBlock;
        },
    })
    descriptionFilter: ui.fields.Text;

    @ui.decorators.block<TableFilterRefresh>({
        title: 'Table',
        parent() {
            return this.section;
        },
    })
    tableBlock: ui.containers.Block;

    @ui.decorators.tableField<TableFilterRefresh, ShowCaseProduct>({
        bind: 'products',
        canExport: false,
        canFilter: false,
        canResizeColumns: false,
        columns: [
            ui.nestedFields.text({
                bind: '_id',
                title: 'Id',
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                bind: 'product',
                title: 'Product',
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                bind: 'description',
                title: 'Description',
                isReadOnly: true,
            }),
            ui.nestedFields.numeric({
                bind: 'netPrice',
                title: 'Price',
                scale: 2,
                isReadOnly: true,
            }),
        ],
        filter() {
            return this.getUpdatedTableFilter();
        },
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        orderBy: {
            product: 1,
        },
        parent() {
            return this.tableBlock;
        },
    })
    table: ui.fields.Table<ShowCaseProduct>;

    getUpdatedTableFilter() {
        const filter: Filter<ShowCaseProduct> = {};

        if (this.priceFilter?.value) {
            filter.netPrice = { _gte: `${this.priceFilter.value}` };
        }

        if (this.descriptionFilter?.value) {
            filter.description = { _regex: this.descriptionFilter.value, _options: 'i' };
        }

        return filter;
    }
}
