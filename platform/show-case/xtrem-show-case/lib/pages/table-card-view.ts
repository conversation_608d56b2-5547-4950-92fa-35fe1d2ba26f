import { ShowCaseProvider, ShowCaseProduct } from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import { tableField } from '../menu-items/_index';

@ui.decorators.page<TableCardView, ShowCaseProvider>({
    authorizationCode: 'BSCFLDS',
    defaultEntry: () => '2',
    module: 'show-case',
    category: 'SHOWCASE',
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.text({ bind: 'textField' }),
            titleRight: ui.nestedFields.text({ bind: '_id' }),
        },
    },
    node: '@sage/xtrem-show-case/ShowCaseProvider',
    onLoad() {
        this.canSelect.value = true;
        this.canUserHideColumns.value = true;
        this.description.value = `The indicator column is meant to demonstrate the ability of manipulating field properties based on other values in the same row. Specifically you can modify the following behaviors:
        
- Hide the Indicator field by setting the Hot checkbox to true
- Change the background color of the Indicator field by setting the Quantity value (>20, 20-6, <6)
- Change the text of the Indicator field by setting the Quantity value (>20, 20-6, <6)`;
    },
    title: 'Field - Table card view',
    menuItem: tableField,
})
export class TableCardView extends ui.Page {
    @ui.decorators.section<TableCardView>({
        title: 'Table field',
    })
    section: ui.containers.Section;

    @ui.decorators.block<TableCardView>({
        parent() {
            return this.section;
        },
        title: 'Field example',
    })
    fieldBlock: ui.containers.Block;

    @ui.decorators.tableField<TableCardView, ShowCaseProduct>({
        node: '@sage/xtrem-show-case/ShowCaseProvider',
        bind: 'products',
        cardView: true,
        mobileCard: {
            title: ui.nestedFields.label({
                bind: 'amount',
                title: 'Amount',
                prefix: '$',
                isHiddenOnMainField: true,
            }),
            titleRight: ui.nestedFields.date({
                bind: 'releaseDate',
                title: 'Date',
            }),
            line2: ui.nestedFields.select({
                bind: 'category',
                title: 'Category',
                optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
            }),
            line2Right: ui.nestedFields.reference<TableCardView, ShowCaseProduct, ShowCaseProvider>({
                bind: 'provider',
                title: 'Provider',
                valueField: 'textField',
                node: '@sage/xtrem-show-case/ShowCaseProvider',
            }),
            image: ui.nestedFields.image({
                bind: 'imageField',
                title: 'Image',
                shape: 'circle',
            }),
        },
        columns: [
            ui.nestedFields.label({
                bind: 'amount',
                title: 'Amount',
                prefix: '$',
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.date({
                bind: 'releaseDate',
                title: 'Date',
            }),
            ui.nestedFields.select({
                bind: 'category',
                title: 'Category',
                optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
            }),
            ui.nestedFields.reference<TableCardView, ShowCaseProduct, ShowCaseProvider>({
                bind: 'provider',
                title: 'Provider',
                valueField: 'textField',
                node: '@sage/xtrem-show-case/ShowCaseProvider',
            }),
            ui.nestedFields.image({
                bind: 'imageField',
                title: 'Image',
            }),
        ],
        orderBy: {
            product: 1,
        },
        fieldActions() {
            return [this.addProduct];
        },
        parent() {
            return this.fieldBlock;
        },
        dropdownActions: [
            {
                icon: 'add',
                title: 'Add',
                isDisabled() {
                    return false;
                },
                onClick(rowId: any, data: any) {
                    this.$.dialog.message(
                        'info',
                        'Add Row Action was clicked',
                        `Product: ${data.product} Row: ${rowId}`,
                    );
                },
            },
            {
                icon: 'locked',
                title: 'Maybe disabled',
                isDisabled(id: any, row: any) {
                    return row.qty < 10;
                },
                onClick(rowId: any, data: any) {
                    this.$.showToast(data.product, { type: 'info' });
                },
            },
            {
                title: 'Action no icon',
                isDisabled(id: any, row: any) {
                    return row.qty < 10;
                },
                onClick(rowId: any, data: any) {
                    this.$.showToast(data.product, { type: 'info' });
                },
            },
        ],
    })
    field: ui.fields.Table<ShowCaseProduct>;

    @ui.decorators.textAreaField<TableCardView>({
        isFullWidth: true,
        isReadOnly: true,
        isTransient: true,
        parent() {
            return this.fieldBlock;
        },
        rows: 6,
    })
    description: ui.fields.TextArea;

    @ui.decorators.numericField<TableCardView>({
        parent() {
            return this.fieldBlock;
        },
        title: 'Selected items total',
        isReadOnly: true,
        scale: 2,
        prefix: '$',
        isTransient: true,
    })
    tableSelectedTotal: ui.fields.Numeric;

    @ui.decorators.numericField<TableCardView>({
        parent() {
            return this.fieldBlock;
        },
        isTransient: true,
        title: 'Items total',
        isReadOnly: true,
        scale: 2,
        prefix: '$',
    })
    tableSampleTotal: ui.fields.Numeric;

    @ui.decorators.block<TableCardView>({
        parent() {
            return this.section;
        },
        title: 'Configuration',
    })
    configurationBlock: ui.containers.Block;

    @ui.decorators.checkboxField<TableCardView>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Can select',
        onChange() {
            this.field.canSelect = this.canSelect.value;
        },
        isTransient: true,
    })
    canSelect: ui.fields.Checkbox;

    @ui.decorators.checkboxField<TableCardView>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Can user hide columns',
        onChange() {
            this.field.canUserHideColumns = this.canUserHideColumns.value;
        },
        isTransient: true,
    })
    canUserHideColumns: ui.fields.Checkbox;

    @ui.decorators.textField<TableCardView>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Helper text',
        onChange() {
            this.field.helperText = this.helperText.value;
        },
        isTransient: true,
    })
    helperText: ui.fields.Text;

    @ui.decorators.checkboxField<TableCardView>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is disabled',
        onChange() {
            this.field.isDisabled = this.isDisabled.value;
        },
        isTransient: true,
    })
    isDisabled: ui.fields.Checkbox;

    @ui.decorators.checkboxField<TableCardView>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is helper text hidden',
        onChange() {
            this.field.isHelperTextHidden = this.isHelperTextHidden.value;
        },
        isTransient: true,
    })
    isHelperTextHidden: ui.fields.Checkbox;

    @ui.decorators.checkboxField<TableCardView>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is hidden',
        onChange() {
            this.field.isHidden = this.isHidden.value;
        },
        isTransient: true,
    })
    isHidden: ui.fields.Checkbox;

    @ui.decorators.checkboxField<TableCardView>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is title hidden',
        onChange() {
            this.field.isTitleHidden = this.isTitleHidden.value;
        },
        isTransient: true,
    })
    isTitleHidden: ui.fields.Checkbox;

    @ui.decorators.textField<TableCardView>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Title',
        onChange() {
            this.field.title = this.title.value;
        },
        isTransient: true,
    })
    title: ui.fields.Text;

    @ui.decorators.pageAction<TableCardView>({
        title: 'Add row',
        icon: 'plus',
        onClick() {
            this.$.dialog
                .page('@sage/xtrem-show-case/ShowCaseProduct', undefined, {
                    rightAligned: true,
                    size: 'medium-large',
                })
                .then(() => {
                    return this.field.refresh();
                })
                .catch(error => {
                    ui.console.error(error);
                    this.$.showToast('An error ocurred during creation');
                });
        },
    })
    addProduct: ui.PageAction;

    updateRow(_id: any, rowData: ui.PartialNodeWithId<ShowCaseProduct>) {
        const updatedAmount = rowData.qty * parseFloat(rowData.listPrice);
        rowData.amount = String(updatedAmount);
        this.field.value = this.field.value.map(item => (item._id === _id ? rowData : item));
    }
}
