import {
    Graph<PERSON><PERSON>,
    ShowCaseProduct,
    ShowCaseProviderAddressBinding,
    ShowCaseProvider as ShowCaseProviderNode,
} from '@sage/xtrem-show-case-api';
import {
    setApplicativePageCrudActions,
    setOrderOfPageHeaderDropDownActions,
} from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import * as ui from '@sage/xtrem-ui';
import { applicationPages } from '../menu-items/application-pages';

@ui.decorators.page<AutoConfigShowCaseProvider, ShowCaseProviderNode>({
    title: 'Auto-configured Provider page',
    menuItem: applicationPages,
    objectTypeSingular: 'Provider',
    objectTypePlural: 'Providers',
    idField() {
        return this._id;
    },
    businessActions() {
        return [this.$standardCancelAction, this.$standardSaveAction];
    },
    headerQuickActions() {
        return [this.$standardDuplicateAction];
    },
    headerDropDownActions() {
        return setOrderOfPageHeaderDropDownActions({
            remove: [this.$standardDeleteAction],
            dropDownBusinessActions: [this.$standardOpenRecordHistoryAction],
        });
    },
    headerSection() {
        return this.headerSection;
    },
    module: 'show-case',
    node: '@sage/xtrem-show-case/ShowCaseProvider',
    priority: 500,
    onLoad() {
        setApplicativePageCrudActions({
            page: this,
            isDirty: false,
            save: this.$standardSaveAction,
            cancel: this.$standardCancelAction,
            duplicate: this.$standardDuplicateAction,
            remove: this.$standardDeleteAction,
            actions: [],
        });
    },
    onDirtyStateUpdated(isDirty: boolean) {
        setApplicativePageCrudActions({
            page: this,
            isDirty,
            save: this.$standardSaveAction,
            cancel: this.$standardCancelAction,
            duplicate: this.$standardDuplicateAction,
            remove: this.$standardDeleteAction,
            actions: [],
        });
    },
})
export class AutoConfigShowCaseProvider extends ui.Page<GraphApi> {
    @ui.decorators.section<AutoConfigShowCaseProvider>({
        title: 'Header',
    })
    headerSection: ui.containers.Section;

    @ui.decorators.block<AutoConfigShowCaseProvider>({
        parent() {
            return this.headerSection;
        },
    })
    headerBlock: ui.containers.Block;

    @ui.decorators.textField<AutoConfigShowCaseProvider>({
        parent() {
            return this.headerBlock;
        },
        title: 'ID',
        isReadOnly: true,
    })
    _id: ui.fields.Text;

    @ui.decorators.textField<AutoConfigShowCaseProvider>({
        parent() {
            return this.headerBlock;
        },
        title: 'Name',
    })
    textField: ui.fields.Text;

    @ui.decorators.referenceField<AutoConfigShowCaseProvider, ShowCaseProviderAddressBinding>({
        title: 'Address',
        parent() {
            return this.headerBlock;
        },
        createTunnelLinkText: 'Create a new site address',
    })
    siteAddress: ui.fields.Reference;

    @ui.decorators.referenceField<AutoConfigShowCaseProvider, ShowCaseProduct>({
        parent() {
            return this.headerBlock;
        },
        createTunnelLinkText: 'Create a new product',
    })
    flagshipProduct: ui.fields.Reference;

    @ui.decorators.section<AutoConfigShowCaseProvider>({
        title: 'Products',
    })
    productListSection: ui.containers.Section;

    @ui.decorators.tableField<AutoConfigShowCaseProvider, ShowCaseProduct>({
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        parent() {
            return this.productListSection;
        },
        title: 'Products',
        isTitleHidden: true,
        orderBy: { _id: 1 },
        dropdownActions: [
            {
                icon: 'box_arrow_left',
                title: 'Edit on sidebar',
                async onClick(rowId: any) {
                    this.products.openSidebar(rowId);
                },
            },
        ],
        sidebar: {
            title(_id, recordValue) {
                return recordValue.product;
            },
            layout() {
                return {
                    mainSection: {
                        title: 'Product details',
                        blocks: {
                            mainBlock: {
                                fields: ['product', 'designerEmployee', 'category', 'qty', 'amount', 'description'],
                            },
                        },
                    },
                };
            },
        },
    })
    products: ui.fields.Table<ShowCaseProduct>;

    @ui.decorators.section<AutoConfigShowCaseProvider>({
        title: 'Misc',
    })
    miscSection: ui.containers.Section;

    @ui.decorators.block<AutoConfigShowCaseProvider>({
        parent() {
            return this.miscSection;
        },
    })
    miscBlock: ui.containers.Block;

    @ui.decorators.dateField<AutoConfigShowCaseProvider>({
        parent() {
            return this.miscBlock;
        },
        title: 'Date',
    })
    dateField: ui.fields.Date;

    @ui.decorators.checkboxField<AutoConfigShowCaseProvider>({
        parent() {
            return this.miscBlock;
        },
        title: 'Checkbox',
    })
    booleanField: ui.fields.Checkbox;

    @ui.decorators.numericField<AutoConfigShowCaseProvider>({
        parent() {
            return this.miscBlock;
        },
        title: 'Integer',
    })
    integerField: ui.fields.Numeric;
}
