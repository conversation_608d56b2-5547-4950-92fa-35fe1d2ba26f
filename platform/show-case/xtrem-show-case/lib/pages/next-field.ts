import * as ui from '@sage/xtrem-ui';
import { misc } from '../menu-items/misc';

@ui.decorators.page<NextField>({
    module: 'show-case',
    title: 'Focus utils',
    isTransient: true,
    category: 'SHOWCASE',
    menuItem: misc,
})
export class NextField extends ui.Page {
    @ui.decorators.section<NextField>({
        title: 'Configuration',
    })
    configSection: ui.containers.Section;

    @ui.decorators.block<NextField>({
        parent() {
            return this.configSection;
        },
        isTitleHidden: true,
    })
    configBlock: ui.containers.Block;

    @ui.decorators.checkboxField<NextField>({
        parent() {
            return this.configBlock;
        },
        title: 'Should find focusable field?',
    })
    shouldFindFocusable: ui.fields.Checkbox;

    @ui.decorators.buttonField<NextField>({
        parent() {
            return this.configBlock;
        },
        title: 'Find next',
        onClick() {
            setTimeout(() => {
                if (this.$.page.focussedField) {
                    const nextField = this.$.page.focussedField.getNextField(this.shouldFindFocusable.value);
                    if (nextField && this.shouldFindFocusable.value) {
                        this.$.showToast(`Next field is ${nextField.title}!`);
                        nextField.focus();
                    } else if (nextField) {
                        this.$.showToast(`Next field is ${nextField.title}!`);
                    } else {
                        this.$.showToast('Next field wasnt found :(', { type: 'warning' });
                    }
                } else {
                    this.$.showToast('No field was in focus', { type: 'warning' });
                }
            }, 3000);
        },
        map() {
            return 'Next!';
        },
        helperText: 'Focus any button and then in 3 seconds the next one will be focussed.',
    })
    findNextButton: ui.fields.Button;

    @ui.decorators.section<NextField>({
        title: 'Sandbox',
    })
    testSection1: ui.containers.Section;

    @ui.decorators.block<NextField>({
        parent() {
            return this.testSection1;
        },
        isTitleHidden: true,
    })
    testSection1Block1: ui.containers.Block;

    @ui.decorators.textField<NextField>({
        parent() {
            return this.testSection1Block1;
        },
        title: 'A - Small field',
        width: 'small',
    })
    testSection1Block1Field1: ui.fields.Text;

    @ui.decorators.textField<NextField>({
        parent() {
            return this.testSection1Block1;
        },
        isHidden: true,
        title: 'B - Medium field',
        width: 'medium',
    })
    testSection1Block1Field2: ui.fields.Text;

    @ui.decorators.textField<NextField>({
        parent() {
            return this.testSection1Block1;
        },
        isDisabled: true,
        title: 'C - Large field',
        width: 'large',
    })
    testSection1Block1Field3: ui.fields.Text;

    @ui.decorators.textField<NextField>({
        parent() {
            return this.testSection1Block1;
        },
        title: 'D - Large field',
        width: 'large',
    })
    testSection1Block1Field4: ui.fields.Text;

    @ui.decorators.textField<NextField>({
        parent() {
            return this.testSection1Block1;
        },
        title: 'E - Hidden field',
        isHidden() {
            return true;
        },
        width: 'large',
    })
    testSection1Block1Field5: ui.fields.Text;

    @ui.decorators.block<NextField>({
        parent() {
            return this.testSection1;
        },
        isTitleHidden: true,
    })
    testSection1Block2: ui.containers.Block;

    @ui.decorators.textField<NextField>({
        parent() {
            return this.testSection1Block2;
        },
        title: 'F - Small field',
        isDisabled: true,
        width: 'small',
    })
    testSection1Block2Field1: ui.fields.Text;

    @ui.decorators.textField<NextField>({
        parent() {
            return this.testSection1Block2;
        },
        title: 'G - Small field',
        width: 'small',
    })
    testSection1Block2Field2: ui.fields.Text;

    @ui.decorators.textField<NextField>({
        parent() {
            return this.testSection1Block2;
        },
        title: 'H - is disabled',
        width: 'small',
        isDisabled() {
            return true;
        },
    })
    testSection1Block2Field3: ui.fields.Text;

    @ui.decorators.textField<NextField>({
        parent() {
            return this.testSection1Block2;
        },
        title: 'I - not disabled',
        width: 'small',
        isDisabled() {
            return false;
        },
    })
    testSection1Block2Field4: ui.fields.Text;

    @ui.decorators.block<NextField>({
        parent() {
            return this.testSection1;
        },
        isTitleHidden: true,
        isHidden: true,
    })
    testSection1Block3: ui.containers.Block;

    @ui.decorators.textField<NextField>({
        parent() {
            return this.testSection1Block3;
        },
        title: 'J - Small field',
        width: 'small',
    })
    testSection1Block3Field1: ui.fields.Text;
}
