import { <PERSON><PERSON>h<PERSON><PERSON>, ShowCaseProduct, ShowCaseProvider } from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import { navigationPanel } from '../menu-items/navigation-panel';

@ui.decorators.page<NavigationPanelProgressBar, ShowCaseProduct>({
    authorizationCode: 'NAVPANEL',
    module: 'show-case',
    title: 'Navigation Panel - Progress bar',
    subtitle: 'Navigation panel examples',
    node: '@sage/xtrem-show-case/ShowCaseProduct',
    category: 'SHOWCASE',
    businessActions() {
        return [this.previousItem, this.nextItem];
    },
    navigationPanel: {
        isFirstLetterSeparatorHidden: true,
        listItem: {
            title: ui.nestedFields.text({ bind: 'product' }),
            titleRight: ui.nestedFields.label({ bind: 'category' }),
            line2: ui.nestedFields.reference({
                bind: 'provider',
                valueField: 'textField',
                node: '@sage/xtrem-show-case/ShowCaseProvider',
            }),
            line3: ui.nestedFields.text({ bind: 'releaseDate' }),
            progressBar: ui.nestedFields.progress({
                bind: 'progress',
                areProgressLabelsHidden: true,
            }),
        },
        isAutoSelectEnabled: true,
        menuType: 'toggle',
    },
    async onLoad() {
        this.hasPreviousRecord.value = await this.$.router.hasPreviousRecord();
        this.hasNextRecord.value = await this.$.router.hasNextRecord();
    },
    menuItem: navigationPanel,
})
export class NavigationPanelProgressBar extends ui.Page<GraphApi> {
    @ui.decorators.section<NavigationPanelProgressBar>({})
    section: ui.containers.Section;

    @ui.decorators.block<NavigationPanelProgressBar>({
        parent() {
            return this.section;
        },
        title: 'Product data',
    })
    block: ui.containers.Block;

    @ui.decorators.textField<NavigationPanelProgressBar>({
        parent() {
            return this.block;
        },
        title: 'Id',
        isReadOnly: true,
    })
    _id: ui.fields.Text;

    @ui.decorators.textField<NavigationPanelProgressBar>({
        parent() {
            return this.block;
        },
        title: 'Product',
        isReadOnly: true,
    })
    product: ui.fields.Text;

    @ui.decorators.checkboxField<NavigationPanelProgressBar>({
        parent() {
            return this.block;
        },
        title: 'Has Previous Record?',
        isTransient: true,
        isDisabled: true,
    })
    hasPreviousRecord: ui.fields.Checkbox;

    @ui.decorators.checkboxField<NavigationPanelProgressBar>({
        parent() {
            return this.block;
        },
        title: 'Has Next Record?',
        isTransient: true,
        isDisabled: true,
    })
    hasNextRecord: ui.fields.Checkbox;

    @ui.decorators.pageAction({
        title: 'Next Item',
        onClick() {
            this.$.router.nextRecord();
        },
    })
    nextItem: ui.PageAction;

    @ui.decorators.pageAction({
        title: 'Previous Item',
        onClick() {
            this.$.router.previousRecord();
        },
    })
    previousItem: ui.PageAction;

    @ui.decorators.referenceField<NavigationPanelProgressBar, ShowCaseProvider>({
        parent() {
            return this.block;
        },
        columns: [ui.nestedFields.text({ bind: 'textField' })],
        node: '@sage/xtrem-show-case/ShowCaseProvider',
        title: 'Provider',
        isReadOnly: true,
        valueField: 'textField',
        helperTextField: '_id',
        minLookupCharacters: 0,
    })
    provider: ui.fields.Reference;
}
