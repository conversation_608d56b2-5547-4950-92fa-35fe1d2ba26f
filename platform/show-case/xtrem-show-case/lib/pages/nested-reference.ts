import * as ui from '@sage/xtrem-ui';
// import {ShowCaseInvoice} from '../../api/api';
import { ShowCaseInvoice } from '@sage/xtrem-show-case-api';
import { tableField } from '../menu-items/table-field';

@ui.decorators.page<NestedReference>({
    authorizationCode: 'BSCFLDS',
    module: 'show-case',
    category: 'SHOWCASE',
    title: 'Field - NestedReference',
    menuItem: tableField,
})
export class NestedReference extends ui.Page {
    @ui.decorators.section<NestedReference>({
        title: 'NestedReference field',
    })
    section: ui.containers.Section;

    @ui.decorators.block<NestedReference>({
        parent() {
            return this.section;
        },
        title: 'Nested Reference Example',
    })
    fieldBlock: ui.containers.Block;

    @ui.decorators.referenceField<NestedReference, ShowCaseInvoice>({
        columns: [
            ui.nestedFields.text({ bind: '_id', title: 'ID', canFilter: false }),
            ui.nestedFields.reference<NestedReference, ShowCaseInvoice, ShowCaseInvoice['customer']>({
                bind: 'customer',
                valueField: { name: true },
                title: 'Customer',
                node: '@sage/xtrem-show-case/ShowCaseCustomer',
                canFilter: true,
            }),
            ui.nestedFields.reference<NestedReference, ShowCaseInvoice, ShowCaseInvoice['customer']>({
                bind: 'customer',
                valueField: { email: true },
                title: 'E-mail',
                node: '@sage/xtrem-show-case/ShowCaseCustomer',
                canFilter: true,
            }),
            ui.nestedFields.date({ bind: 'purchaseDate', title: 'Date', canFilter: true }),
        ],
        helperTextField: { customer: { email: true } },
        node: '@sage/xtrem-show-case/ShowCaseInvoice',
        isFullWidth: true,
        parent() {
            return this.fieldBlock;
        },
        valueField: { customer: { name: true } },
        canFilter: true,
    })
    field: ui.fields.Reference;
}
