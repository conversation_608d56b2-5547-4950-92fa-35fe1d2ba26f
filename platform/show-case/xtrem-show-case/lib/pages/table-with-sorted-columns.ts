import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Show<PERSON>ase<PERSON>ustomer, ShowCaseProduct, ShowCaseProvider } from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import { tableField } from '../menu-items/_index';
import { Table } from './table';

@ui.decorators.page<Table, ShowCaseProvider>({
    authorizationCode: 'BSCFLDS',
    defaultEntry: () => '2',
    module: 'show-case',
    category: 'SHOWCASE',
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.text({ bind: 'textField' }),
            titleRight: ui.nestedFields.text({ bind: '_id' }),
        },
    },
    node: '@sage/xtrem-show-case/ShowCaseProvider',
    title: 'Field - Table with sorted columns',
    menuItem: tableField,
})
export class TableWithSortedColumns extends ui.Page<GraphApi, ShowCaseProvider> {
    @ui.decorators.section<TableWithSortedColumns>({
        title: 'Table with sorted columns field',
    })
    section: ui.containers.Section;

    @ui.decorators.block<TableWithSortedColumns>({
        parent() {
            return this.section;
        },
        title: 'Field example',
    })
    fieldBlock: ui.containers.Block;

    @ui.decorators.numericField<TableWithSortedColumns>({
        parent() {
            return this.fieldBlock;
        },
        title: 'Selected items total',
        isReadOnly: true,
        scale: 2,
        prefix: '$',
        isTransient: true,
    })
    tableSelectedTotal: ui.fields.Numeric;

    @ui.decorators.switchField<TableWithSortedColumns>({
        parent() {
            return this.fieldBlock;
        },
        title: 'Sort columns by bind DESC?',
        onChange() {
            this.field.redraw();
        },
        isTransient: true,
    })
    decreasingOrderSwitch: ui.fields.Switch;

    @ui.decorators.numericField<TableWithSortedColumns>({
        parent() {
            return this.fieldBlock;
        },
        isTransient: true,
        title: 'Items total',
        isReadOnly: true,
        scale: 2,
        prefix: '$',
    })
    tableSampleTotal: ui.fields.Numeric;

    @ui.decorators.tableField<TableWithSortedColumns, ShowCaseProduct>({
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        bind: 'products',
        hasSearchBoxMobile: true,
        canExport: true,
        canResizeColumns: true,
        columns: [
            ui.nestedFields.text({
                bind: '_id',
                title: 'Id',
                isHiddenOnMainField: true,
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                bind: 'product',
                title: 'Product',
            }),
            ui.nestedFields.text({
                bind: 'description',
                title: 'Description',
            }),
            ui.nestedFields.numeric({
                bind: 'qty',
                title: 'Quantity',
                fetchesDefaults: true,
            }),
            ui.nestedFields.numeric({
                bind: 'fixedQuantity',
                title: 'Fixed Quantity',
                isTransientInput: true,
            }),
            ui.nestedFields.numeric({
                bind: 'listPrice',
                title: 'List Price',
                scale: 2,
                isHiddenOnMainField: true,
                fetchesDefaults: true,
            }),
            ui.nestedFields.numeric({
                bind: 'netPrice',
                title: 'Net Price',
                isReadOnly: (value: number, rowValue: any) => {
                    return !!rowValue.hotProduct;
                },
                scale: 2,
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.label({
                bind: 'qty',
                isTransient: true,
                title: 'Indicator',
            }),
            ui.nestedFields.label({
                bind: 'st',
                onClick(_id: string | number, rowData) {
                    this.$.dialog.message('info', 'Label was clicked', `Label ${rowData.st} was clicked (row ${_id})`);
                },
                title: 'St',
            }),
            ui.nestedFields.checkbox({
                bind: 'hotProduct',
                title: 'Hot',
            }),
            ui.nestedFields.switch({
                bind: 'hotProduct',
                title: 'Hot (Switch)',
            }),
            ui.nestedFields.link({
                bind: 'product',
                isTransient: true,
                icon(_id, rowData) {
                    if (rowData.qty < 10) {
                        return 'arrow_down';
                    }
                    return 'arrow_up';
                },
                iconColor(_id, rowData) {
                    if (rowData.qty < 10) {
                        return ui.tokens.colorsSemanticCaution500;
                    }
                    return ui.tokens.colorsSemanticPositive500;
                },
                map(_fieldValue, rowData) {
                    return `http://${(rowData.product as string)
                        .replace(/-|_|\s/g, '')
                        .substring(0, 4)
                        .toLowerCase()}.sage.com`;
                },
                onClick(id: string, raw: any) {
                    ui.console.log('CLICKED LINK', { id, raw });
                },
                title: 'Link',
                page: 'https://www.google.com',
            }),
            ui.nestedFields.progress({
                bind: 'progress',
                title: 'Progress',
            }),
            ui.nestedFields.numeric({
                bind: 'tax',
                title: 'Tax',
                prefix: 'T',
                scale: 2,
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.label<TableWithSortedColumns, ShowCaseProduct>({
                bind: 'amount',
                title: 'Amount',
                prefix: '$',
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.filterSelect<TableWithSortedColumns, ShowCaseProduct, ShowCaseCustomer>({
                bind: 'email',
                title: 'Email',
                valueField: 'email',
                node: '@sage/xtrem-show-case/ShowCaseCustomer',
                minLookupCharacters: 0,
                width: 'large',
                onChange(id: string, raw: string) {
                    ui.console.log('CHANGED FILTER SELECT', { id, raw });
                },
            }),
            ui.nestedFields.date<TableWithSortedColumns, ShowCaseProduct>({
                bind: 'releaseDate',
                title: 'Date',
            }),
            ui.nestedFields.date({
                bind: 'endingDate',
                title: 'Ending date',
            }),
            ui.nestedFields.select<TableWithSortedColumns, ShowCaseProduct>({
                bind: 'category',
                title: 'Category',
                optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
            }),
            ui.nestedFields.image({
                bind: 'imageField',
                title: 'Image',
            }),
            ui.nestedFields.icon({
                bind: 'qty',
                title: 'Icon',
                map: value => {
                    return value === 0 ? 'error' : value < 10 ? 'warning' : '';
                },
            }),
        ],
        orderBy: {
            product: 1,
        },
        parent() {
            return this.fieldBlock;
        },
        dropdownActions: [
            {
                icon: 'add',
                title: 'Add',
                isDisabled() {
                    return false;
                },
                onClick(rowId: any, data: any) {
                    this.$.dialog.message(
                        'info',
                        'Add Row Action was clicked',
                        `Product: ${data.product} Row: ${rowId}`,
                    );
                },
            },
            {
                icon: 'locked',
                title: 'Maybe disabled',
                isDisabled(id: any, row: any) {
                    return row.qty < 10;
                },
                onClick(rowId: any, data: any) {
                    this.$.showToast(data.product, { type: 'info' });
                },
            },
            {
                icon: 'minus',
                title: 'Remove',
                onClick(rowId: any) {
                    this.field.removeRecord(rowId);
                },
            },
            {
                title: 'Action no icon',
                isDisabled(id: any, row: any) {
                    return row.qty < 10;
                },
                onClick(rowId: any, data: any) {
                    this.$.showToast(data.product, { type: 'info' });
                },
            },
        ],
        sortColumns(column1: any, column2: any) {
            return column1.bind.localeCompare(column2.bind) * (this.decreasingOrderSwitch.value ? -1 : 1);
        },
    })
    field: ui.fields.Table<ShowCaseProduct>;
}
