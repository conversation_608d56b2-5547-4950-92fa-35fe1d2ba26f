import {
    <PERSON><PERSON>h<PERSON><PERSON>,
    ShowCaseProduct,
    ShowCaseProviderAddressBinding,
    ShowCaseProvider as ShowCaseProviderNode,
} from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import { containers } from '../menu-items/containers';

@ui.decorators.page<HeaderSection, ShowCaseProviderNode>({
    authorizationCode: 'SHCPRVD',
    category: 'SHOWCASE',
    objectTypeSingular: 'Provider',
    objectTypePlural: 'Providers',
    idField() {
        return this.textField;
    },
    createAction() {
        return this.$standardNewAction;
    },
    headerDropDownActions() {
        return [this.$standardDeleteAction, this.dropDownAction1, this.dropDownAction2];
    },
    headerQuickActions() {
        return [this.quickAction1, this.quickAction2];
    },
    businessActions() {
        return [this.$standardCancelAction, this.$standardSaveAction];
    },
    headerSection() {
        return this.headerSection;
    },
    onLoad() {
        this.selectedTotal.value = 0;
        this.stepIndicator.value = 'Order';
        this.minPrice.value = 'Unknown';
        this.maxPrice.value = 'Unknown';
        this.averagePrice.value = 'Unknown';
        this.priceSum.value = 'Unknown';
    },
    module: 'show-case',
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.text({ bind: 'textField', title: 'Name' }),
            line2: ui.nestedFields.text({ bind: '_id', title: 'ID' }),
            line3: ui.nestedFields.date({ bind: 'dateField', title: 'Added on' }),
            image: ui.nestedFields.image({ bind: 'logo', title: 'Logo', placeholderMode: 'Initials' }),
        },
    },
    node: '@sage/xtrem-show-case/ShowCaseProvider',
    title: 'Header Section',
    priority: 200,
    mode: 'tabs',
    menuItem: containers,
})
export class HeaderSection extends ui.Page<GraphApi> {
    updateAggregations = () => {
        const average = this.products.calculateAggregatedValue({
            aggregationKey: 'total',
            aggregationMethod: 'avg',
        });

        const max = this.products.calculateAggregatedValue({
            aggregationKey: 'total',
            aggregationMethod: 'max',
        });

        const min = this.products.calculateAggregatedValue({
            aggregationKey: 'total',
            aggregationMethod: 'min',
        });

        const sum = this.products.calculateAggregatedValue({
            aggregationKey: 'total',
            aggregationMethod: 'sum',
        });
        this.averagePrice.value = `$ ${average.toFixed(2)}`;
        this.minPrice.value = `$ ${min.toFixed(2)}`;
        this.maxPrice.value = `$ ${max.toFixed(2)}`;
        this.priceSum.value = `$ ${sum.toFixed(2)}`;
    };

    @ui.decorators.section<HeaderSection>({
        title: 'Header Section',
        isTitleHidden: true,
    })
    headerSection: ui.containers.Section;

    @ui.decorators.block<HeaderSection>({
        isTitleHidden: true,
        parent() {
            return this.headerSection;
        },
    })
    headerBlock: ui.containers.Block;

    @ui.decorators.tile<HeaderSection>({
        parent() {
            return this.headerSection;
        },
    })
    tileContainer: ui.containers.Tile;

    @ui.decorators.section<HeaderSection>({
        title: 'Basic details',
    })
    section: ui.containers.Section;

    @ui.decorators.block<HeaderSection>({
        parent() {
            return this.section;
        },
    })
    block: ui.containers.Block;

    @ui.decorators.stepSequenceField<HeaderSection>({
        parent() {
            return this.headerBlock;
        },
        isTransient: true,
        options: ['Create', 'Approve', 'Order', 'Receive', 'Invoice'],
    })
    stepIndicator: ui.fields.StepSequence;

    @ui.decorators.textField<HeaderSection>({
        parent() {
            return this.headerBlock;
        },
        title: 'ID',
    })
    _id: ui.fields.Text;

    @ui.decorators.textField<HeaderSection>({
        parent() {
            return this.tileContainer;
        },
        title: 'Name',
        icon: 'basket',
        iconColor: ui.tokens.colorsSemanticPositive600,
        onClick() {
            this.$.showToast('name is clicked');
        },
    })
    textField: ui.fields.Text;

    @ui.decorators.countField<HeaderSection, ShowCaseProduct>({
        parent() {
            return this.tileContainer;
        },
        title: 'Total product count',
        bind: 'products',
    })
    productCount: ui.fields.Count;

    @ui.decorators.textField<HeaderSection>({
        parent() {
            return this.tileContainer;
        },
        title: 'Average price',
        isTransient: true,
    })
    averagePrice: ui.fields.Text;

    @ui.decorators.textField<HeaderSection>({
        parent() {
            return this.tileContainer;
        },
        title: 'Min price',
        isTransient: true,
    })
    minPrice: ui.fields.Text;

    @ui.decorators.textField<HeaderSection>({
        parent() {
            return this.tileContainer;
        },
        title: 'Max price',
        isTransient: true,
    })
    maxPrice: ui.fields.Text;

    @ui.decorators.textField<HeaderSection>({
        parent() {
            return this.tileContainer;
        },
        title: 'Price sum',
        isTransient: true,
    })
    priceSum: ui.fields.Text;

    @ui.decorators.aggregateField<HeaderSection, ShowCaseProduct>({
        parent() {
            return this.tileContainer;
        },
        aggregateOn: 'listPrice',
        aggregationMethod: 'sum',
        prefix: '$',
        title: 'Total list price',
        bind: 'products',
        onClick() {
            this.$.showToast('total list price is clicked');
        },
    })
    stockValue: ui.fields.Aggregate;

    @ui.decorators.dateField<HeaderSection>({
        parent() {
            return this.headerBlock;
        },
        title: 'Date',
    })
    dateField: ui.fields.Date;

    @ui.decorators.checkboxField<HeaderSection>({
        parent() {
            return this.block;
        },
        title: 'Checkbox',
    })
    booleanField: ui.fields.Checkbox;

    @ui.decorators.numericField<HeaderSection>({
        parent() {
            return this.block;
        },
        title: 'Integer',
        scale: 0,
    })
    integerField: ui.fields.Numeric;

    @ui.decorators.numericField<HeaderSection>({
        parent() {
            return this.tileContainer;
        },
        title: 'Selected total',
        scale: 2,
        isTransient: true,
    })
    selectedTotal: ui.fields.Numeric;

    @ui.decorators.numericField<HeaderSection>({
        parent() {
            return this.block;
        },
        title: 'Minimum Quantity',
        scale: 0,
    })
    minQuantity: ui.fields.Numeric;

    @ui.decorators.numericField<HeaderSection>({
        bind: 'decimalField',
        parent() {
            return this.block;
        },
        title: 'Decimal',
        scale: 2,
    })
    testingTheBindPropertyInMutations: ui.fields.Numeric;

    @ui.decorators.section<HeaderSection>({
        title: 'Products',
    })
    productSection: ui.containers.Section;

    @ui.decorators.tableField<HeaderSection, ShowCaseProduct>({
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        mainField: 'description',
        parent() {
            return this.productSection;
        },
        isTitleHidden: true,
        canFilter: false,
        onRowClick: ui.console.log,
        orderBy: { _id: 1 },
        canAddNewLine: true,
        canSelect: true,
        canExport: true,
        onRowSelected() {
            this.selectedTotal.value = this.products.selectedRecords.reduce<number>((total: number, s: string) => {
                return total + Number(this.products.getRecordValue(s)?.qty || 0);
            }, 0);
        },
        onDataLoaded() {
            this.minPrice.value = 'Unknown';
            this.maxPrice.value = 'Unknown';
            this.averagePrice.value = 'Unknown';
            this.priceSum.value = 'Unknown';
        },
        onAllDataLoaded() {
            this.updateAggregations();
        },
        onRowUnselected() {
            this.selectedTotal.value = this.products.selectedRecords.reduce<number>((total: number, s: string) => {
                return total + Number(this.products.getRecordValue(s)?.qty || 0);
            }, 0);
        },
        optionsMenu: [
            {
                title: 'All',
                graphQLFilter: {},
            },
            {
                title: 'QA Passed',
                graphQLFilter: {
                    category: { _in: ['notBad', 'good', 'great'] },
                },
            },
            {
                title: 'Quality issues',
                graphQLFilter: {
                    category: { _in: ['ok', 'awful'] },
                },
            },
        ],
        columns: [
            ui.nestedFields.text({ bind: '_id', isReadOnly: true, title: 'Id' }),
            ui.nestedFields.link({
                bind: 'product',
                title: 'Product',
                onClick(_id) {
                    this.$.router.goTo('@sage/xtrem-show-case/ShowCaseProduct', { _id });
                },
            }),
            ui.nestedFields.text({ bind: 'description', title: 'Description' }),
            ui.nestedFields.checkbox({ bind: 'hotProduct', isReadOnly: true, title: 'Hot' }),
            ui.nestedFields.select({
                bind: 'category',
                optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
                title: 'Category',
            }),
            ui.nestedFields.numeric({ bind: 'total', title: 'Net Price', scale: 2, min: 0, isDisabled: false }),
            ui.nestedFields.numeric({ bind: 'qty', title: 'Quantity', scale: 2 }),
            ui.nestedFields.numeric({ bind: 'amount', title: 'Amount', scale: 2 }),
        ],
    })
    products: ui.fields.Table<ShowCaseProduct>;

    @ui.decorators.podField<HeaderSection, ShowCaseProduct>({
        parent() {
            return this.block;
        },

        node: '@sage/xtrem-show-case/ShowCaseProduct',
        title: 'Flagship Product!',
        columns: [
            ui.nestedFields.text({ bind: 'product', title: 'Product Name' }),
            ui.nestedFields.multiDropdown({ bind: 'entries', title: 'Entries' }),
        ],
    })
    flagshipProduct: ui.fields.Pod;

    @ui.decorators.vitalPodField<HeaderSection, ShowCaseProviderAddressBinding>({
        title: 'Address',
        parent() {
            return this.block;
        },
        columns: [
            ui.nestedFields.text({ bind: 'name', title: 'Name' }),
            ui.nestedFields.text({ bind: 'addressLine1', title: 'Line1' }),
            ui.nestedFields.text({ bind: 'addressLine2', title: 'Line2' }),
            ui.nestedFields.reference({
                bind: 'country',
                title: 'Country',
                node: '@sage/xtrem-show-case/ShowCaseCountry',
                valueField: 'name',
                helperTextField: 'code',
            }),
            ui.nestedFields.text({ bind: 'zip', title: 'zip', isTransientInput: true }),
        ],
        node: '@sage/xtrem-show-case/ShowCaseProviderAddress',
    })
    siteAddress: ui.fields.VitalPod;

    @ui.decorators.section<HeaderSection>({
        title: 'Config section',
    })
    configSection: ui.containers.Section;

    @ui.decorators.block<HeaderSection>({
        parent() {
            return this.configSection;
        },
    })
    configBlock: ui.containers.Block;

    @ui.decorators.checkboxField<HeaderSection>({
        parent() {
            return this.configBlock;
        },
        title: 'Is name hidden?',
        isFullWidth: true,
        isTransient: true,
        onChange() {
            this.textField.isHidden = !!this.isNameHidden.value;
        },
    })
    isNameHidden: ui.fields.Checkbox;

    @ui.decorators.checkboxField<HeaderSection>({
        parent() {
            return this.configBlock;
        },
        title: 'Is product count hidden?',
        isFullWidth: true,
        isTransient: true,
        onChange() {
            this.productCount.isHidden = !!this.isProductCountHidden.value;
        },
    })
    isProductCountHidden: ui.fields.Checkbox;

    @ui.decorators.checkboxField<HeaderSection>({
        parent() {
            return this.configBlock;
        },
        title: 'Is stock value hidden?',
        isFullWidth: true,
        isTransient: true,
        onChange() {
            this.stockValue.isHidden = !!this.isStockValueHidden.value;
        },
    })
    isStockValueHidden: ui.fields.Checkbox;

    @ui.decorators.checkboxField<HeaderSection>({
        parent() {
            return this.configBlock;
        },
        title: 'Is stock value hidden?',
        isFullWidth: true,
        isTransient: true,
        onChange() {
            this.selectedTotal.isHidden = !!this.isSelectedTotalHidden.value;
        },
    })
    isSelectedTotalHidden: ui.fields.Checkbox;

    @ui.decorators.checkboxField<HeaderSection>({
        parent() {
            return this.configBlock;
        },
        title: 'Is tile container hidden?',
        isFullWidth: true,
        isTransient: true,
        onChange() {
            this.tileContainer.isHidden = !!this.hideTileField.value;
        },
    })
    hideTileField: ui.fields.Checkbox;

    @ui.decorators.pageAction<HeaderSection>({
        title: 'Drop-down action 1',
        icon: 'alert',
        onClick() {
            // Do nothing
        },
    })
    dropDownAction1: ui.PageAction;

    @ui.decorators.pageAction<HeaderSection>({
        title: 'Drop-down action 2',
        icon: 'calendar_today',
        onClick() {
            // Do nothing
        },
    })
    dropDownAction2: ui.PageAction;

    @ui.decorators.pageAction<HeaderSection>({
        title: 'Quick action 1',
        icon: 'stacked_boxes',
        onClick() {
            // Do nothing
        },
    })
    quickAction1: ui.PageAction;

    @ui.decorators.pageAction<HeaderSection>({
        title: 'Quick action 2',
        icon: 'bank',
        onClick() {
            // Do nothing
        },
    })
    quickAction2: ui.PageAction;
}
