import { GraphApi } from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.page<ShowCaseProductRenameActionDialog>({
    isTransient: true,
    title: 'Mass product renaming',
    businessActions() {
        return [this.$standardCancelAction, this.$standardDialogConfirmationAction];
    },
})
export class ShowCaseProductRenameActionDialog extends ui.Page<GraphApi> {
    @ui.decorators.section<ShowCaseProductRenameActionDialog>({
        isTitleHidden: true,
    })
    section: ui.containers.Section;

    @ui.decorators.block<ShowCaseProductRenameActionDialog>({
        isTitleHidden: true,
        parent() {
            return this.section;
        },
    })
    fieldBlock: ui.containers.Block;

    @ui.decorators.textField<ShowCaseProductRenameActionDialog>({
        parent() {
            return this.fieldBlock;
        },
        isMandatory: true,
        title: 'Prefix',
        helperText: "This text will be prepended to the selected products' names",
    })
    prefix: ui.fields.Text;
}
