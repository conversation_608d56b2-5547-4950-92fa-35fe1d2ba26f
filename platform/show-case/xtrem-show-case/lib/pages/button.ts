import * as ui from '@sage/xtrem-ui';
import { fields } from '../menu-items/fields';

@ui.decorators.page<Button>({
    authorizationCode: 'BSCFLDS',
    module: 'show-case',
    title: 'Field - Button',
    isTransient: true,
    category: 'SHOWCASE',
    onLoad() {
        this.field.value = 'Sample';
        this.value.value = 'Sample';
    },
    menuItem: fields,
})
export class Button extends ui.Page {
    @ui.decorators.section<Button>({
        title: 'Button field',
    })
    section: ui.containers.Section;

    @ui.decorators.block<Button>({
        parent() {
            return this.section;
        },
        title: 'Field example',
    })
    fieldBlock: ui.containers.Block;

    @ui.decorators.buttonField<Button>({
        parent() {
            return this.fieldBlock;
        },
        onClick() {
            this.clickTriggered.isHidden = false;
            setTimeout(() => {
                this.clickTriggered.isHidden = true;
            }, 5000);
        },
    })
    field: ui.fields.Button;

    @ui.decorators.labelField<Button>({
        parent() {
            return this.fieldBlock;
        },
        isHidden: true,
        map() {
            return 'Click was triggered';
        },
    })
    clickTriggered: ui.fields.Label;

    @ui.decorators.block<Button>({
        parent() {
            return this.section;
        },
        title: 'Configuration',
    })
    configurationBlock: ui.containers.Block;

    @ui.decorators.textField<Button>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Helper text',
        onChange() {
            this.field.helperText = this.helperText.value;
        },
    })
    helperText: ui.fields.Text;

    @ui.decorators.checkboxField<Button>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is disabled',
        onChange() {
            this.field.isDisabled = this.isDisabled.value;
        },
    })
    isDisabled: ui.fields.Checkbox;

    @ui.decorators.checkboxField<Button>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is helper text hidden',
        onChange() {
            this.field.isHelperTextHidden = this.isHelperTextHidden.value;
        },
    })
    isHelperTextHidden: ui.fields.Checkbox;

    @ui.decorators.checkboxField<Button>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is hidden',
        onChange() {
            this.field.isHidden = this.isHidden.value;
        },
    })
    isHidden: ui.fields.Checkbox;

    @ui.decorators.checkboxField<Button>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is title hidden',
        onChange() {
            this.field.isTitleHidden = this.isTitleHidden.value;
        },
    })
    isTitleHidden: ui.fields.Checkbox;

    @ui.decorators.checkboxField<Button>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Set title dirty',
        onChange() {
            this.title.isDirty = !this.title.isDirty;
            if (this.title.isDirty) {
                this.transient.value = 'Is dirty';
            } else {
                this.transient.value = 'Not dirty';
            }
            this.isTitleDirty.isDirty = false;
        },
    })
    isTitleDirty: ui.fields.Checkbox;

    @ui.decorators.textField<Button>({
        parent() {
            return this.configurationBlock;
        },
        isTransient: true,
        isReadOnly: true,
        title: 'Is dirty',
    })
    transient: ui.fields.Text;

    @ui.decorators.textField<Button>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Title',
        onChange() {
            this.field.title = this.title.value;
        },
    })
    title: ui.fields.Text;

    @ui.decorators.textField<Button>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Value',
        onChange() {
            this.field.value = this.value.value;
        },
    })
    value: ui.fields.Text;

    @ui.decorators.buttonField<Button>({
        parent() {
            return this.configurationBlock;
        },
        map() {
            return 'Focus field';
        },
        onClick() {
            this.field.focus();
        },
    })
    focus: ui.fields.Button;
}
