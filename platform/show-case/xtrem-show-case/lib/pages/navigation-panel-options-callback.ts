import { Graph<PERSON><PERSON>, ShowCaseProduct } from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import { navigationPanel } from '../menu-items/navigation-panel';

@ui.decorators.page<NavigationPanelOptionsCallback, ShowCaseProduct>({
    authorizationCode: 'NAVPANEL',
    module: 'show-case',
    title: 'Navigation Panel with options callback',
    node: '@sage/xtrem-show-case/ShowCaseProduct',
    category: 'SHOWCASE',
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.text({ bind: 'product' }),
            titleRight: ui.nestedFields.label({ bind: 'category' }),
            line2: ui.nestedFields.reference({
                bind: 'provider',
                valueField: 'textField',
                node: '@sage/xtrem-show-case/ShowCaseProvider',
            }),
            line3: ui.nestedFields.text({ bind: 'releaseDate' }),
        },
        async optionsMenu(graph, storage, queryParam, username, userCode) {
            const result = await graph
                .node('@sage/xtrem-show-case/ShowCaseProvider')
                .query({ edges: { node: { textField: true } } })
                .execute();
            ui.console.log(storage);
            ui.console.log(queryParam);
            ui.console.log('username', username);
            ui.console.log('userCode', userCode);
            const providers = result.edges.map(edge => {
                return {
                    title: `Only from ${edge.node.textField}`,
                    graphQLFilter: { provider: { textField: { _eq: edge.node.textField } } },
                };
            });
            return [
                {
                    title: 'All',
                    graphQLFilter: {},
                },
                ...providers,
                {
                    title: 'My Own products',
                    graphQLFilter: { provider: { textField: { _eq: username } } },
                },
            ];
        },
    },
    menuItem: navigationPanel,
})
export class NavigationPanelOptionsCallback extends ui.Page<GraphApi> {
    @ui.decorators.section<NavigationPanelOptionsCallback>({})
    section: ui.containers.Section;
}
