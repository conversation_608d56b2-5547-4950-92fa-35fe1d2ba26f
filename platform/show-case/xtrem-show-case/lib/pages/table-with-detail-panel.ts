import {
    Show<PERSON><PERSON><PERSON><PERSON><PERSON>,
    ShowCaseProduct,
    ShowCaseProduct<PERSON><PERSON>in<PERSON>ddress,
    ShowCaseProvider,
} from '@sage/xtrem-show-case-api';
import { setOrderOfPageHeaderDropDownActions } from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import * as ui from '@sage/xtrem-ui';
import { tableField } from '../menu-items/_index';

@ui.decorators.page<TableWithDetailPanel, ShowCaseProvider>({
    authorizationCode: 'BSCFLDS',
    defaultEntry: () => '2',
    module: 'show-case',
    category: 'SHOWCASE',
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.text({ bind: 'textField' }),
            titleRight: ui.nestedFields.text({ bind: '_id' }),
        },
    },
    node: '@sage/xtrem-show-case/ShowCaseProvider',
    title: 'Field - Table - With detail panel',
    detailPanel() {
        return {
            header: this.detailPanelHeaderSection,
            sections: [this.detailPanelBodySection1, this.detailPanelBodySection2],
        };
    },
    headerQuickActions() {
        return [this.$standardDuplicateAction];
    },
    headerDropDownActions() {
        return setOrderOfPageHeaderDropDownActions({
            remove: [this.$standardDeleteAction],
            dropDownBusinessActions: [this.$standardOpenRecordHistoryAction],
        });
    },
    businessActions() {
        return [this.$standardCancelAction, this.$standardSaveAction];
    },
    menuItem: tableField,
})
export class TableWithDetailPanel extends ui.Page {
    @ui.decorators.section<TableWithDetailPanel>({
        title: 'Time tracking',
    })
    detailPanelHeaderSection: ui.containers.Section;

    @ui.decorators.block<TableWithDetailPanel>({
        parent() {
            return this.detailPanelHeaderSection;
        },
    })
    detailPanelHeaderBlock: ui.containers.Block;

    @ui.decorators.labelField<TableWithDetailPanel>({
        parent() {
            return this.detailPanelHeaderBlock;
        },
        isTransient: true,
        title: 'Detail panel',
        map() {
            return 'header';
        },
    })
    detailPanelProductImageField: ui.fields.Label;

    @ui.decorators.section<TableWithDetailPanel>({
        title: 'info',
    })
    detailPanelBodySection1: ui.containers.Section;

    @ui.decorators.block<TableWithDetailPanel>({
        parent() {
            return this.detailPanelBodySection1;
        },
        title: 'First section block',
    })
    detailPanelBlock1: ui.containers.Block;

    @ui.decorators.gridRowBlock<TableWithDetailPanel>({
        parent() {
            return this.detailPanelBodySection1;
        },
        title: 'Second section block',
        boundTo() {
            return this.field;
        },
        fieldFilter(columnId: string) {
            return columnId !== 'tax';
        },
    })
    demoGridRowBlock: ui.containers.GridRowBlock;

    @ui.decorators.textField<TableWithDetailPanel>({
        isReadOnly: true,
        isTransient: true,
        parent() {
            return this.detailPanelBlock1;
        },
        title: 'My text',
    })
    detailPanelProductCategory: ui.fields.Text;

    @ui.decorators.numericField<TableWithDetailPanel>({
        isReadOnly: false,
        isTransient: true,
        scale: 1,
        parent() {
            return this.detailPanelBlock1;
        },
        title: 'Scaled number',
    })
    detailPanelScaledNumber: ui.fields.Numeric;

    @ui.decorators.section<TableWithDetailPanel>({
        title: 'draft',
    })
    detailPanelBodySection2: ui.containers.Section;

    @ui.decorators.block<TableWithDetailPanel>({
        parent() {
            return this.detailPanelBodySection2;
        },
        title: 'Second section block',
    })
    detailPanelBlock2: ui.containers.Block;

    @ui.decorators.textAreaField<TableWithDetailPanel>({
        isTransient: true,
        parent() {
            return this.detailPanelBlock2;
        },
        title: 'My area',
        rows: 5,
    })
    detailPanelNote: ui.fields.TextArea;

    @ui.decorators.section<TableWithDetailPanel>({
        isTitleHidden: true,
    })
    section: ui.containers.Section;

    @ui.decorators.section<TableWithDetailPanel>({
        title: 'Edit row',
        isHidden: true,
    })
    customDialogSection: ui.containers.Section;

    @ui.decorators.gridRowBlock<TableWithDetailPanel>({
        parent() {
            return this.customDialogSection;
        },
        isTitleHidden: true,
        boundTo() {
            return this.field;
        },
        fieldFilter(columnId: string) {
            return columnId !== 'tax';
        },
        readOnlyOverride(columnId: string) {
            if (columnId === 'description' || columnId === 'provider') {
                return true;
            }
            return undefined;
        },
    })
    dialogGridRowBlock: ui.containers.GridRowBlock;

    @ui.decorators.vitalPodField<TableWithDetailPanel, ShowCaseProductOriginAddress>({
        isTransient: true,
        parent() {
            return this.detailPanelBlock2;
        },
        title: 'Product Origin',
        isTitleHidden: true,
        isFullWidth: true,
        node: '@sage/xtrem-show-case/ShowCaseProductOriginAddress',
        onChange() {
            const recordValue = this.field.getRecordValue(this.demoGridRowBlock.selectedRecordId);
            this.field.setRecordValue({ ...recordValue, originAddress: this.originAddress.value });
        },
        columns: [
            ui.nestedFields.text({
                title: 'Address name',
                bind: 'name',
            }),
            ui.nestedFields.text({
                title: 'Address line 1',
                bind: 'addressLine1',
            }),
            ui.nestedFields.text({
                title: 'Address line 2',
                bind: 'addressLine2',
            }),
            ui.nestedFields.reference({
                bind: 'country',
                title: 'Country',
                node: '@sage/xtrem-show-case/ShowCaseCountry',
                valueField: 'name',
                helperTextField: 'code',
                columns: [
                    ui.nestedFields.text({ bind: 'name', title: 'Name' }),
                    ui.nestedFields.text({ bind: 'code', title: 'Code' }),
                    ui.nestedFields.text({ bind: 'phoneCountryCode', title: 'Tel Country code' }),
                ],
                tunnelPage: null,
            }),
        ],
    })
    originAddress: ui.fields.VitalPod<ShowCaseProductOriginAddress>;

    @ui.decorators.block<TableWithDetailPanel>({
        parent() {
            return this.section;
        },
    })
    fieldBlock: ui.containers.Block;

    @ui.decorators.tableField<TableWithDetailPanel, ShowCaseProduct>({
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        bind: 'products',
        title: 'Products',
        canExport: false,
        columns: [
            ui.nestedFields.text({
                bind: '_id',
                title: 'Id',
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                bind: 'product',
                title: 'Product',
            }),
            ui.nestedFields.text({
                bind: 'description',
                isFullWidth: true,
                title: 'Description',
            }),
            ui.nestedFields.numeric({
                bind: 'qty',
                title: 'Quantity',
                fetchesDefaults: true,
            }),
            ui.nestedFields.numeric({
                bind: 'listPrice',
                title: 'List Price',
                scale: 2,
                isHiddenDesktop: false,
                fetchesDefaults: true,
            }),

            ui.nestedFields.label({
                bind: 'st',
                onClick(_id: string | number, rowData) {
                    this.$.dialog.message('info', 'Label was clicked', `Label ${rowData.st} was clicked (row ${_id})`);
                },
                title: 'St',
                isExcludedFromMainField: true,
            }),
            ui.nestedFields.numeric({
                bind: 'tax',
                title: 'Tax',
                prefix: 'T',
                scale: 2,
                isExcludedFromMainField: true,
                isHiddenDesktop: false,
            }),
            ui.nestedFields.label<TableWithDetailPanel, ShowCaseProduct>({
                bind: 'amount',
                title: 'Amount',
                prefix: '$',
                isExcludedFromMainField: true,
                isHiddenDesktop: false,
            }),
            ui.nestedFields.reference<TableWithDetailPanel, ShowCaseProduct, ShowCaseProvider>({
                bind: 'provider',
                title: 'Provider',
                valueField: 'textField',
                node: '@sage/xtrem-show-case/ShowCaseProvider',
                minLookupCharacters: 0,
                onClick(id: string, raw: any) {
                    ui.console.log('CLICKED NO LOOKUP', { id, raw });
                },
                onChange(id: string, raw: any) {
                    ui.console.log('CHANGED NO LOOKUP', { id, raw });
                },
                columns: null,
                tunnelPage: null,
            }),
            ui.nestedFields.reference<TableWithDetailPanel, ShowCaseProduct, ShowCaseProvider>({
                bind: 'provider',
                title: 'Provider',
                valueField: 'textField',
                node: '@sage/xtrem-show-case/ShowCaseProvider',
                minLookupCharacters: 0,
                isExcludedFromMainField: true,
                columns: [
                    ui.nestedFields.text({ bind: '_id', title: 'ID', canFilter: false }),
                    ui.nestedFields.text({ bind: 'textField', title: 'Provider', canFilter: true }),
                    ui.nestedFields.image({ bind: 'logo', title: 'Logo', canFilter: false }),
                ],
                imageField: 'logo',
                onClick(id: string, raw: any) {
                    ui.console.log('CLICKED LOOKUP', { id, raw });
                },
                onChange(id: string, raw: any) {
                    ui.console.log('CHANGED LOOKUP', { id, raw });
                },
                tunnelPage: null,
            }),
            ui.nestedFields.link({
                bind: 'product',
                isTransient: true,
                map(_fieldValue, rowData) {
                    return `http://${(rowData.product as string)
                        .replace(/-|_|\s/g, '')
                        .substring(0, 4)
                        .toLowerCase()}.sage.com`;
                },
                onClick(id: string, raw: any) {
                    ui.console.log('CLICKED LINK', { id, raw });
                },
                title: 'Link',
                page: 'https://www.google.com',
                isExcludedFromMainField: true,
            }),
            ui.nestedFields.progress({
                bind: 'progress',
                title: 'Progress',
            }),
            ui.nestedFields.numeric({
                bind: 'fixedQuantity',
                title: 'Fixed Quantity',
                isTransientInput: true,
            }),
            ui.nestedFields.checkbox({
                bind: 'hotProduct',
                title: 'Hot',
            }),
            ui.nestedFields.numeric({
                bind: 'netPrice',
                title: 'Net Price',
                isReadOnly: (value: number, rowValue: any) => {
                    return !!rowValue.hotProduct;
                },
                scale: 2,
                isHiddenDesktop: false,
            }),
            ui.nestedFields.filterSelect<TableWithDetailPanel, ShowCaseProduct, ShowCaseCustomer>({
                bind: 'email',
                title: 'Email',
                valueField: 'email',
                node: '@sage/xtrem-show-case/ShowCaseCustomer',
                isExcludedFromMainField: false,
                minLookupCharacters: 0,
                width: 'large',
                onChange(id: string, raw: string) {
                    ui.console.log('CHANGED FILTER SELECT', { id, raw });
                },
            }),
            ui.nestedFields.date<TableWithDetailPanel, ShowCaseProduct>({
                bind: 'releaseDate',
                title: 'Date',
            }),
            ui.nestedFields.date({
                bind: 'endingDate',
                title: 'Ending date',
                isExcludedFromMainField: true,
            }),
            ui.nestedFields.select<TableWithDetailPanel, ShowCaseProduct>({
                bind: 'category',
                title: 'Category',
                optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
            }),
            ui.nestedFields.image({
                bind: 'imageField',
                title: 'Image',
                isExcludedFromMainField: true,
            }),
            ui.nestedFields.icon({
                bind: 'qty',
                title: 'Icon',
                isExcludedFromMainField: true,

                map: (value: any) => {
                    return value === 0 ? 'error' : value < 10 ? 'warning' : '';
                },
            }),
            ui.nestedFields.technical<TableWithDetailPanel, ShowCaseProduct, ShowCaseProductOriginAddress>({
                bind: 'originAddress',
                node: '@sage/xtrem-show-case/ShowCaseProductOriginAddress',
                nestedFields: [
                    ui.nestedFields.text({
                        bind: 'name',
                    }),
                    ui.nestedFields.text({
                        bind: 'addressLine1',
                    }),
                    ui.nestedFields.text({
                        bind: 'addressLine2',
                    }),
                    ui.nestedFields.reference({
                        bind: 'country',
                        node: '@sage/xtrem-show-case/ShowCaseCountry',
                        valueField: 'name',
                        helperTextField: 'code',
                        columns: null,
                        tunnelPage: null,
                    }),
                ],
            }),
            ui.nestedFields.numeric<TableWithDetailPanel, ShowCaseProduct>({
                bind: 'discount',
                title: 'Discount',
                isExcludedFromMainField: true,
            }),
        ],
        orderBy: {
            product: 1,
        },
        parent() {
            return this.fieldBlock;
        },
        onRowClick(_id: string) {
            this.$.detailPanel!.isHidden = false;
            this.demoGridRowBlock.selectedRecordId = _id;
            const selectedRecord = this.field.getRecordValue(_id);
            this.technicalFieldValue.value = String(selectedRecord.provider!.integerField);
            this.originAddress.value = selectedRecord.originAddress;
        },
        dropdownActions: [
            {
                id: 'edit',
                icon: 'edit',
                title: 'Edit on sidebar',
                async onClick(rowId: any) {
                    this.dialogGridRowBlock.selectedRecordId = rowId;
                    this.customDialogSection.isHidden = false;
                    try {
                        await this.$.dialog.custom('info', this.customDialogSection, { rightAligned: true });
                    } catch {
                        // Intentionally empty.
                    }
                    this.customDialogSection.isHidden = true;
                },
            },
            {
                id: 'locked',
                icon: 'locked',
                title: 'Maybe disabled',
                isDisabled(id: any, row: any) {
                    return row.qty < 10;
                },
                onClick(rowId: any, data: any) {
                    this.$.showToast(data.product, { type: 'info' });
                },
            },
            {
                id: 'actionNoIcon',
                title: 'Action no icon',
                isDisabled(id: any, row: any) {
                    return row.qty < 10;
                },
                onClick(rowId: any, data: any) {
                    this.$.showToast(data.product, { type: 'info' });
                },
            },
        ],
        inlineActions: [
            {
                id: 'edit',
                icon: 'edit',
                async onClick() {
                    this.$.dialog.message('info', 'Edit', 'Edit was clicked');
                },
                title: 'Edit Product',
            },
        ],
    })
    field: ui.fields.Table<ShowCaseProduct>;

    @ui.decorators.textField<TableWithDetailPanel>({
        parent() {
            return this.fieldBlock;
        },
        isTransient: true,
        isReadOnly: true,
    })
    technicalFieldValue: ui.fields.Text;

    @ui.decorators.tableField<TableWithDetailPanel, ShowCaseProduct>({
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        bind: 'products',
        title: 'TableField as direct child of section',
        canExport: false,
        columns: [
            ui.nestedFields.text({
                bind: '_id',
                title: 'Id',
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                bind: 'product',
                title: 'Product',
            }),
            ui.nestedFields.text({
                bind: 'description',
                isFullWidth: true,
                title: 'Description',
            }),
            ui.nestedFields.numeric({
                bind: 'qty',
                title: 'Quantity',
                fetchesDefaults: true,
            }),
            ui.nestedFields.numeric({
                bind: 'listPrice',
                title: 'List Price',
                scale: 2,
                isHiddenDesktop: false,
                fetchesDefaults: true,
            }),
            ui.nestedFields.label({
                bind: 'st',
                onClick(_id: string | number, rowData) {
                    this.$.dialog.message('info', 'Label was clicked', `Label ${rowData.st} was clicked (row ${_id})`);
                },
                title: 'St',
                isExcludedFromMainField: true,
            }),
            ui.nestedFields.numeric({
                bind: 'tax',
                title: 'Tax',
                prefix: 'T',
                scale: 2,
                isExcludedFromMainField: true,
                isHiddenDesktop: false,
            }),
            ui.nestedFields.label<TableWithDetailPanel, ShowCaseProduct>({
                bind: 'amount',
                title: 'Amount',
                prefix: '$',
                isExcludedFromMainField: true,
                isHiddenDesktop: false,
            }),
            ui.nestedFields.reference<TableWithDetailPanel, ShowCaseProduct, ShowCaseProvider>({
                bind: 'provider',
                title: 'Provider',
                valueField: 'textField',
                node: '@sage/xtrem-show-case/ShowCaseProvider',
                minLookupCharacters: 0,
                onClick(id: string, raw: any) {
                    ui.console.log('CLICKED NO LOOKUP', { id, raw });
                },
                onChange(id: string, raw: any) {
                    ui.console.log('CHANGED NO LOOKUP', { id, raw });
                },
                columns: null,
                tunnelPage: null,
            }),
            ui.nestedFields.reference<TableWithDetailPanel, ShowCaseProduct, ShowCaseProvider>({
                bind: 'provider',
                title: 'Provider',
                valueField: 'textField',
                node: '@sage/xtrem-show-case/ShowCaseProvider',
                minLookupCharacters: 0,
                isExcludedFromMainField: true,
                columns: [
                    ui.nestedFields.text({ bind: '_id', title: 'ID', canFilter: false }),
                    ui.nestedFields.text({ bind: 'textField', title: 'Provider', canFilter: true }),
                    ui.nestedFields.image({ bind: 'logo', title: 'Logo', canFilter: false }),
                ],
                imageField: 'logo',
                onClick(id: string, raw: any) {
                    ui.console.log('CLICKED LOOKUP', { id, raw });
                },
                onChange(id: string, raw: any) {
                    ui.console.log('CHANGED LOOKUP', { id, raw });
                },
                tunnelPage: null,
            }),
            ui.nestedFields.link({
                bind: 'product',
                isTransient: true,
                map(_fieldValue, rowData) {
                    return `http://${(rowData.product as string)
                        .replace(/-|_|\s/g, '')
                        .substring(0, 4)
                        .toLowerCase()}.sage.com`;
                },
                onClick(id: string, raw: any) {
                    ui.console.log('CLICKED LINK', { id, raw });
                },
                title: 'Link',
                page: 'https://www.google.com',
                isExcludedFromMainField: true,
            }),
            ui.nestedFields.progress({
                bind: 'progress',
                title: 'Progress',
            }),
            ui.nestedFields.numeric({
                bind: 'fixedQuantity',
                title: 'Fixed Quantity',
                isTransientInput: true,
            }),
            ui.nestedFields.checkbox({
                bind: 'hotProduct',
                title: 'Hot',
            }),
            ui.nestedFields.numeric({
                bind: 'netPrice',
                title: 'Net Price',
                isReadOnly: (value: number, rowValue: any) => {
                    return !!rowValue.hotProduct;
                },
                scale: 2,
                isHiddenDesktop: false,
            }),
            ui.nestedFields.filterSelect<TableWithDetailPanel, ShowCaseProduct, ShowCaseCustomer>({
                bind: 'email',
                title: 'Email',
                valueField: 'email',
                node: '@sage/xtrem-show-case/ShowCaseCustomer',
                isExcludedFromMainField: false,
                minLookupCharacters: 0,
                width: 'large',
                onChange(id: string, raw: string) {
                    ui.console.log('CHANGED FILTER SELECT', { id, raw });
                },
            }),
            ui.nestedFields.date<TableWithDetailPanel, ShowCaseProduct>({
                bind: 'releaseDate',
                title: 'Date',
            }),
            ui.nestedFields.date({
                bind: 'endingDate',
                title: 'Ending date',
                isExcludedFromMainField: true,
            }),
            ui.nestedFields.select<TableWithDetailPanel, ShowCaseProduct>({
                bind: 'category',
                title: 'Category',
                optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
            }),
            ui.nestedFields.image({
                bind: 'imageField',
                title: 'Image',
                isExcludedFromMainField: true,
            }),
            ui.nestedFields.icon({
                bind: 'qty',
                title: 'Icon',
                isExcludedFromMainField: true,

                map: (value: any) => {
                    return value === 0 ? 'error' : value < 10 ? 'warning' : '';
                },
            }),
            ui.nestedFields.technical<TableWithDetailPanel, ShowCaseProduct, ShowCaseProductOriginAddress>({
                bind: 'originAddress',
                node: '@sage/xtrem-show-case/ShowCaseProductOriginAddress',
                nestedFields: [
                    ui.nestedFields.text({
                        bind: 'name',
                    }),
                    ui.nestedFields.text({
                        bind: 'addressLine1',
                    }),
                    ui.nestedFields.text({
                        bind: 'addressLine2',
                    }),
                    ui.nestedFields.reference({
                        bind: 'country',
                        node: '@sage/xtrem-show-case/ShowCaseCountry',
                        valueField: 'name',
                        helperTextField: 'code',
                        columns: null,
                        tunnelPage: null,
                    }),
                ],
            }),
            ui.nestedFields.numeric<TableWithDetailPanel, ShowCaseProduct>({
                bind: 'discount',
                title: 'Discount',
                isExcludedFromMainField: true,
            }),
        ],
        orderBy: {
            product: 1,
        },
        parent() {
            return this.section;
        },
        onRowClick(_id: string) {
            this.$.detailPanel!.isHidden = false;
            this.demoGridRowBlock.selectedRecordId = _id;
            const selectedRecord = this.field.getRecordValue(_id);
            this.technicalFieldValue.value = String(selectedRecord.provider.integerField);
            this.originAddress.value = selectedRecord.originAddress;
        },
        dropdownActions: [
            {
                icon: 'edit',
                title: 'Edit on sidebar',
                async onClick(rowId: any) {
                    this.dialogGridRowBlock.selectedRecordId = rowId;
                    this.customDialogSection.isHidden = false;
                    try {
                        await this.$.dialog.custom('info', this.customDialogSection, { rightAligned: true });
                    } catch {
                        // Intentionally empty.
                    }
                    this.customDialogSection.isHidden = true;
                },
            },
            {
                icon: 'locked',
                title: 'Maybe disabled',
                isDisabled(id: any, row: any) {
                    return row.qty < 10;
                },
                onClick(rowId: any, data: any) {
                    this.$.showToast(data.product, { type: 'info' });
                },
            },
            {
                title: 'Action no icon',
                isDisabled(id: any, row: any) {
                    return row.qty < 10;
                },
                onClick(rowId: any, data: any) {
                    this.$.showToast(data.product, { type: 'info' });
                },
            },
        ],
    })
    tableFieldWithParentSection: ui.fields.Table<ShowCaseProduct>;
}
