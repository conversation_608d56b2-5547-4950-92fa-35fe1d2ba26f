import {
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    Show<PERSON>aseEmployee,
    ShowCaseProduct as ShowCaseProductNode,
    ShowCaseProvider,
} from '@sage/xtrem-show-case-api';
import {
    setApplicativePageCrudActions,
    setOrderOfPageHeaderDropDownActions,
} from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import * as ui from '@sage/xtrem-ui';
import { applicationPages } from '../menu-items/application-pages';

@ui.decorators.page<MainListWithDeepBoundReference, ShowCaseProductNode>({
    title: 'Main list with deep bound reference',
    menuItem: applicationPages,
    objectTypeSingular: 'Product',
    objectTypePlural: 'Products',
    idField() {
        return this._id;
    },
    businessActions() {
        return [this.$standardCancelAction, this.$standardSaveAction];
    },
    headerQuickActions() {
        return [this.$standardDuplicateAction];
    },
    headerDropDownActions() {
        return setOrderOfPageHeaderDropDownActions({
            remove: [this.$standardDeleteAction],
            dropDownBusinessActions: [this.$standardOpenRecordHistoryAction],
        });
    },
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.text({ bind: 'product', title: 'Product' }),
            titleRight: ui.nestedFields.text({ bind: '_id', title: 'ID', canFilter: false }),
            line2: ui.nestedFields.text({ bind: 'description', title: 'Description', canFilter: true }),
            designerEmployeeCountry: ui.nestedFields.reference({
                bind: { designerEmployee: { country: true } },
                node: '@sage/xtrem-show-case/ShowCaseCountry',
                title: 'Designer employee country',
                valueField: 'name',
            }),
            provider: ui.nestedFields.reference({
                bind: 'provider',
                node: '@sage/xtrem-show-case/ShowCaseProvider',
                title: 'Supplier',
                valueField: 'textField',
                isHiddenOnMainField: true,
            }),
        },
    },
    module: 'show-case',
    node: '@sage/xtrem-show-case/ShowCaseProduct',
    priority: 500,
    onLoad() {
        setApplicativePageCrudActions({
            page: this,
            isDirty: false,
            save: this.$standardSaveAction,
            cancel: this.$standardCancelAction,
            duplicate: this.$standardDuplicateAction,
            remove: this.$standardDeleteAction,
            actions: [],
        });
    },
    onDirtyStateUpdated(isDirty: boolean) {
        setApplicativePageCrudActions({
            page: this,
            isDirty,
            save: this.$standardSaveAction,
            cancel: this.$standardCancelAction,
            duplicate: this.$standardDuplicateAction,
            remove: this.$standardDeleteAction,
            actions: [],
        });
    },
})
export class MainListWithDeepBoundReference extends ui.Page<GraphApi> {
    @ui.decorators.section<MainListWithDeepBoundReference>({})
    section: ui.containers.Section;

    @ui.decorators.block<MainListWithDeepBoundReference>({
        parent() {
            return this.section;
        },
    })
    block: ui.containers.Block;

    @ui.decorators.textField<MainListWithDeepBoundReference>({
        parent() {
            return this.block;
        },
        title: 'ID',
        isReadOnly: true,
    })
    _id: ui.fields.Text;

    @ui.decorators.textField<MainListWithDeepBoundReference>({
        parent() {
            return this.block;
        },
        isMandatory: true,
    })
    product: ui.fields.Text;

    @ui.decorators.textField<MainListWithDeepBoundReference>({
        parent() {
            return this.block;
        },
    })
    description: ui.fields.Text;

    @ui.decorators.selectField<MainListWithDeepBoundReference>({
        parent() {
            return this.block;
        },
        title: 'Category',
    })
    category: ui.fields.Select;

    @ui.decorators.checkboxField<MainListWithDeepBoundReference>({
        parent() {
            return this.block;
        },
        title: 'Hot product',
    })
    hotProduct: ui.fields.Checkbox;

    @ui.decorators.numericField<MainListWithDeepBoundReference>({
        parent() {
            return this.block;
        },
        title: 'Quantity',
        fetchesDefaults: true,
    })
    qty: ui.fields.Numeric;

    @ui.decorators.numericField<MainListWithDeepBoundReference>({
        parent() {
            return this.block;
        },
        title: 'Stock',
    })
    st: ui.fields.Numeric;

    @ui.decorators.numericField<MainListWithDeepBoundReference>({
        parent() {
            return this.block;
        },
        title: 'List price',
        scale: 2,
        fetchesDefaults: true,
    })
    listPrice: ui.fields.Numeric;

    @ui.decorators.progressField<MainListWithDeepBoundReference>({
        parent() {
            return this.block;
        },
        title: 'Progress',
    })
    progress: ui.fields.Progress;

    @ui.decorators.numericField<MainListWithDeepBoundReference>({
        parent() {
            return this.block;
        },
    })
    tax: ui.fields.Numeric;

    @ui.decorators.numericField<MainListWithDeepBoundReference>({
        parent() {
            return this.block;
        },
    })
    netPrice: ui.fields.Numeric;

    @ui.decorators.numericField<MainListWithDeepBoundReference>({
        parent() {
            return this.block;
        },
    })
    discount: ui.fields.Numeric;

    @ui.decorators.numericField<MainListWithDeepBoundReference>({
        parent() {
            return this.block;
        },
    })
    amount: ui.fields.Numeric;

    @ui.decorators.numericField<MainListWithDeepBoundReference>({
        parent() {
            return this.block;
        },
    })
    total: ui.fields.Numeric;

    @ui.decorators.referenceField<MainListWithDeepBoundReference, ShowCaseProvider>({
        parent() {
            return this.block;
        },
        minLookupCharacters: 0,
    })
    provider: ui.fields.Reference;

    @ui.decorators.referenceField<MainListWithDeepBoundReference, ShowCaseEmployee>({
        parent() {
            return this.block;
        },
        bind: 'designerEmployee',
        node: '@sage/xtrem-show-case/ShowCaseEmployee',
        tunnelPage: '@sage/xtrem-show-case/ShowCaseEmployee',
        title: 'Product designed by',
        valueField: 'firstName',
        helperTextField: 'lastName',
        validation() {
            if (this.designerEmployee.value?.firstName === 'John') {
                return 'No Johns allowed.';
            }
            return undefined;
        },
        onChange() {
            this.$.showToast('The designer employee field was changed.');
        },
        columns: [
            ui.nestedFields.text({ bind: 'firstName', title: 'First name' }),
            ui.nestedFields.text({ bind: 'lastName', title: 'Last name' }),
        ],
        createTunnelLinkText: 'Create a new employee',
    })
    designerEmployee: ui.fields.Reference<ShowCaseEmployee>;

    @ui.decorators.dateField<MainListWithDeepBoundReference>({
        parent() {
            return this.block;
        },
        title: 'Release date',
    })
    releaseDate: ui.fields.Date;

    @ui.decorators.dateField<MainListWithDeepBoundReference>({
        parent() {
            return this.block;
        },
        title: 'Ending date',
    })
    endingDate: ui.fields.Date;
}
