import { <PERSON><PERSON>h<PERSON><PERSON>, ShowCaseProvider as ShowCaseProviderNode } from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import { fields } from '../menu-items/fields';

@ui.decorators.page<MessageField, ShowCaseProviderNode>({
    authorizationCode: 'SHCPRVD',
    category: 'SHOWCASE',
    menuItem: fields,
    module: 'show-case',
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.text({ bind: 'textField', title: 'Provider' }),
            line2: ui.nestedFields.text({ bind: '_id', title: 'ID' }),
            line3: ui.nestedFields.date({ bind: 'dateField', title: 'Date' }),
        },
    },
    node: '@sage/xtrem-show-case/ShowCaseProvider',
    title: 'Field - Message',
})
export class MessageField extends ui.Page<GraphApi> {
    @ui.decorators.section<MessageField>({})
    section: ui.containers.Section;

    @ui.decorators.block<MessageField>({
        parent() {
            return this.section;
        },
    })
    block: ui.containers.Block;

    @ui.decorators.messageField<MessageField>({
        parent() {
            return this.block;
        },
        title: 'Bound example',
    })
    textField: ui.fields.Message;

    @ui.decorators.block<MessageField>({
        parent() {
            return this.section;
        },
    })
    contentExampleBlock: ui.containers.Block;

    @ui.decorators.messageField<MessageField>({
        isTransient: true,
        parent() {
            return this.block;
        },
        content: 'This is some long content\nwith line breaks and\nit can be translated too.',
    })
    exampleField: ui.fields.Message;

    @ui.decorators.textField<MessageField>({
        isTransient: true,
        parent() {
            return this.contentExampleBlock;
        },
        title: 'Helper text',
        onChange() {
            this.exampleField.helperText = this.helperText.value || '';
        },
    })
    helperText: ui.fields.Text;

    @ui.decorators.textField<MessageField>({
        isTransient: true,
        parent() {
            return this.contentExampleBlock;
        },
        title: 'Title',
        onChange() {
            this.exampleField.title = this.title.value || '';
        },
    })
    title: ui.fields.Text;

    @ui.decorators.checkboxField<MessageField>({
        isTransient: true,
        parent() {
            return this.contentExampleBlock;
        },
        title: 'Is hidden',
        onChange() {
            this.exampleField.isHidden = !!this.isHidden.value;
        },
    })
    isHidden: ui.fields.Checkbox;

    @ui.decorators.checkboxField<MessageField>({
        isTransient: true,
        parent() {
            return this.contentExampleBlock;
        },
        title: 'Is title hidden',
        onChange() {
            this.exampleField.isTitleHidden = !!this.isTitleHidden.value;
        },
    })
    isTitleHidden: ui.fields.Checkbox;

    @ui.decorators.selectField<MessageField>({
        isTransient: true,
        parent() {
            return this.contentExampleBlock;
        },
        title: 'Variant',
        options: ['info', 'success', 'warning', 'error'],
        onChange() {
            this.exampleField.variant = (this.variant.value as any) || 'info';
        },
    })
    variant: ui.fields.Select;

    @ui.decorators.textAreaField<MessageField>({
        isTransient: true,
        parent() {
            return this.contentExampleBlock;
        },
        title: 'Content',
        onChange() {
            this.exampleField.content = this.content.value || '';
        },
        isFullWidth: true,
    })
    content: ui.fields.TextArea;

    @ui.decorators.block<MessageField>({
        parent() {
            return this.section;
        },
        title: 'Additional examples',
    })
    additionalExamples: ui.containers.Block;

    @ui.decorators.messageField<MessageField>({
        isTransient: true,
        isMarkdown: true,
        title: 'Markdown content',
        parent() {
            return this.additionalExamples;
        },
        content:
            '## Hi there!\n\nThis **is** some _markdown_ content.\n\n- See \n\n- It can do\n\n- bullet points\n\nAnd the dangerous tags are escaped: <iframe src="http://wwww.sage.com"></iframe>\n\n<script>alert("this could be dangerous");</script>',
    })
    markdownExample: ui.fields.Message;

    @ui.decorators.messageField<MessageField>({
        isTransient: true,
        isMarkdown: true,
        title: 'Info variant',
        variant: 'info',
        parent() {
            return this.additionalExamples;
        },
        content: 'Hi there',
    })
    infoVariant: ui.fields.Message;

    @ui.decorators.messageField<MessageField>({
        isTransient: true,
        isMarkdown: true,
        title: 'Error variant',
        variant: 'error',
        parent() {
            return this.additionalExamples;
        },
        content: 'Hi there',
    })
    errorVariant: ui.fields.Message;

    @ui.decorators.messageField<MessageField>({
        isTransient: true,
        isMarkdown: true,
        title: 'Warning variant',
        variant: 'warning',
        parent() {
            return this.additionalExamples;
        },
        content: 'Hi there',
    })
    warningVariant: ui.fields.Message;

    @ui.decorators.messageField<MessageField>({
        isTransient: true,
        isMarkdown: true,
        title: 'Success variant',
        variant: 'success',
        parent() {
            return this.additionalExamples;
        },
        content: 'Hi there',
    })
    successVariant: ui.fields.Message;

    @ui.decorators.messageField<MessageField>({
        isTransient: true,
        title: 'Small content',
        width: 'small',
        parent() {
            return this.additionalExamples;
        },
        content: 'Small',
    })
    smallExample: ui.fields.Message;

    @ui.decorators.messageField<MessageField>({
        isTransient: true,
        isMarkdown: true,
        title: 'Parent is a section',
        parent() {
            return this.section;
        },
        content: 'Hi there',
    })
    withSectionParent: ui.fields.Message;
}
