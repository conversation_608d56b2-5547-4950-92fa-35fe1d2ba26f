import * as ui from '@sage/xtrem-ui';
import { ShowCase<PERSON>ustomer, ShowCaseOrder, ShowCaseInvoice, ShowCaseInvoiceLine } from '@sage/xtrem-show-case-api';
import { nestedGrid } from '../menu-items/nested-grid';

@ui.decorators.page<NestedGridRemapped, ShowCaseCustomer>({
    authorizationCode: 'BSCFLDS',
    module: 'show-case',
    category: 'SHOWCASE',
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.text({ bind: '_id' }),
            titleLineLeft: ui.nestedFields.text({ bind: 'name' }),
        },
    },
    node: '@sage/xtrem-show-case/ShowCaseCustomer',
    title: 'Field - NestedGrid - Remapped value',
    onLoad() {
        this.demoValueLevel1.value = String(this.$.storage.get('L1') || '');
        this.demoValueLevel2.value = String(this.$.storage.get('L2') || '');
    },
    businessActions() {
        return [this.$standardCancelAction, this.$standardSaveAction];
    },
    menuItem: nestedGrid,
})
export class NestedGridRemapped extends ui.Page {
    @ui.decorators.section<NestedGridRemapped>({
        title: 'Nested Grid',
    })
    section: ui.containers.Section;

    @ui.decorators.block<NestedGridRemapped>({
        parent() {
            return this.section;
        },
        title: 'Nested Grid block',
    })
    fieldBlock: ui.containers.Block;

    @ui.decorators.nestedGridField<NestedGridRemapped, [ShowCaseOrder, ShowCaseInvoice]>({
        bind: 'orders',
        optionsMenu() {
            const thisYear = new Date().getFullYear();
            const lastYear = thisYear - 1;
            return [
                {
                    title: 'This year',
                    graphQLFilter: {
                        _and: [
                            { orderDate: { _gte: `${thisYear}-01-01` } },
                            { orderDate: { _lte: `${thisYear}-12-31` } },
                        ],
                    },
                },
                {
                    title: 'Last year',
                    graphQLFilter: {
                        _and: [
                            { orderDate: { _gte: `${lastYear}-01-01` } },
                            { orderDate: { _lte: `${lastYear}-12-31` } },
                        ],
                    },
                },
                {
                    title: 'Old orders',
                    graphQLFilter: {
                        _and: [{ orderDate: { _lte: `${lastYear - 1}-12-31` } }],
                    },
                },
            ];
        },
        levels: [
            {
                node: '@sage/xtrem-show-case/ShowCaseOrder',
                childProperty: 'invoices',
                columns: [
                    ui.nestedFields.text({
                        bind: '_id',
                        title: 'Id',
                        isHiddenDesktop: false,
                        isReadOnly: true,
                    }),
                    ui.nestedFields.date({
                        bind: 'orderDate',
                        title: 'Order Date',
                        canFilter: true,
                    }),
                    ui.nestedFields.text({
                        bind: 'transientColumn' as any,
                        title: 'Transient calculated',
                        isTransient: true,
                    }),
                ],
                mapServerRecord(r) {
                    const testValue = this.$.storage.get('L1') || 'NOT SET';
                    return { ...r, transientColumn: `${testValue} ${r.orderDate}` };
                },
            },
            {
                node: '@sage/xtrem-show-case/ShowCaseInvoice',
                columns: [
                    ui.nestedFields.text({
                        bind: '_id',
                        title: 'Id',
                        isHiddenDesktop: false,
                        isReadOnly: true,
                    }),
                    ui.nestedFields.numeric({
                        bind: 'totalProductQty',
                        title: 'Total Product Quantity',
                        canFilter: true,
                    }),
                    ui.nestedFields.date({
                        bind: 'purchaseDate',
                        title: 'Purchase Date',
                        canFilter: true,
                    }),
                    ui.nestedFields.text({
                        bind: 'transientColumn' as any,
                        title: 'Transient calculated',
                        isTransient: true,
                    }),
                ],
                mapServerRecord(r) {
                    const testValue = this.$.storage.get('L2') || 'NOT SET';
                    return { ...r, transientColumn: `${testValue} ${r.totalProductQty}` };
                },
            },
        ],
        parent() {
            return this.fieldBlock;
        },
    })
    field: ui.fields.NestedGrid<[ShowCaseOrder, ShowCaseInvoice, ShowCaseInvoiceLine], NestedGridRemapped>;

    @ui.decorators.textField<NestedGridRemapped>({
        title: 'Set prefix to storage for level 1',
        isTransient: true,
        parent() {
            return this.fieldBlock;
        },
        onChange() {
            this.$.storage.set('L1', this.demoValueLevel1.value);
        },
    })
    demoValueLevel1: ui.fields.Text;

    @ui.decorators.textField<NestedGridRemapped>({
        title: 'Set prefix to storage for level 2',
        isTransient: true,
        parent() {
            return this.fieldBlock;
        },
        onChange() {
            this.$.storage.set('L2', this.demoValueLevel2.value);
        },
    })
    demoValueLevel2: ui.fields.Text;

    @ui.decorators.buttonField<NestedGridRemapped>({
        parent() {
            return this.fieldBlock;
        },
        isTransient: true,
        async onClick() {
            await this.$.router.refresh(true);
        },
        map() {
            return 'Refresh Page';
        },
    })
    refreshTable: ui.fields.Button;
}
