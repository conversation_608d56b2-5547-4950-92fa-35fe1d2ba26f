import { GraphApi, ShowCaseProduct } from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import { fields } from '../menu-items/fields';
import type { ShowCaseProductCategory } from '@sage/xtrem-show-case-api';

const map = (value: any) => {
    switch (value) {
        case 'change':
            return 'Change Event';
        case 'click':
            return 'Click Event';
        case 'error':
            return 'Error Event';
        default:
            return '';
    }
};

@ui.decorators.page<DropdownList>({
    authorizationCode: 'DRPDLST',
    category: 'SHOWCASE',
    mode: 'tabs',
    module: 'show-case',
    isTransient: true,
    title: 'Field - Dropdown List',
    async onLoad() {
        const result = await this.$.graph
            .node('@sage/xtrem-show-case/ShowCaseProduct')
            .query(
                ui.queryUtils.edgesSelector({
                    _id: true,
                    product: true,
                    category: true,
                }),
            )
            .execute();

        this.table.value = result.edges.map(edge => edge.node);
    },
    menuItem: fields,
})
export class DropdownList extends ui.Page<GraphApi> {
    @ui.decorators.section<DropdownList>({
        title: 'Dropdown List Field',
    })
    section: ui.containers.Section;

    @ui.decorators.block<DropdownList>({
        title: 'Dynamic Field Example',
        parent() {
            return this.section;
        },
    })
    block: ui.containers.Block;

    @ui.decorators.dropdownListField<DropdownList>({
        isTransient: true,
        options: ['one', 'two', 'three', 'four', 'five'],
        title: 'Dropdown List Field (Options)',
        map(value: any) {
            switch (value) {
                case 'one':
                    return 'One';
                case 'two':
                    return 'Two';
                case 'three':
                    return 'Three';
                case 'four':
                    return 'Four';
                case 'five':
                    return 'Five';
                default:
                    return value;
            }
        },
        onChange() {
            ui.console.log("Do something when field's value has changed.");
            this.value.value = this.field1.value;
            this.label1.value = 'change';
            setTimeout(() => {
                this.label1.value = '';
            }, 2000);
        },
        onClick() {
            ui.console.log('Do something when field has been clicked.');
            this.label1.value = 'click';
            setTimeout(() => {
                this.label1.value = '';
            }, 2000);
        },
        onError(error, screenId, elementId) {
            ui.console.log("Do something when field's callback has thrown an error", { error, screenId, elementId });
            this.label1.value = 'error';
            setTimeout(() => {
                this.label1.value = '';
            }, 2000);
        },
        parent() {
            return this.block;
        },
    })
    field1: ui.fields.DropdownList<'one' | 'two' | 'three' | 'four' | 'five'>;

    @ui.decorators.labelField<DropdownList>({
        map,
        parent() {
            return this.block;
        },
    })
    label1: ui.fields.Label;

    @ui.decorators.dropdownListField<DropdownList>({
        isTransient: true,
        optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
        title: 'Dropdown List Field (OptionType)',
        map(value) {
            switch (value) {
                case 'great':
                    return 'Great';
                case 'good':
                    return 'Good';
                case 'ok':
                    return 'OK';
                case 'notBad':
                    return 'Not Bad';
                case 'awful':
                    return 'Awful';
                default:
                    return value;
            }
        },
        onChange() {
            ui.console.log("Do something when field's value has changed.");
            this.label2.value = 'change';
            setTimeout(() => {
                this.label2.value = '';
            }, 2000);
        },
        onClick() {
            ui.console.log('Do something when field has been clicked.');
            this.label2.value = 'click';
            setTimeout(() => {
                this.label2.value = '';
            }, 2000);
        },
        onError(error, screenId, elementId) {
            ui.console.log("Do something when field's callback has thrown an error", { error, screenId, elementId });
            this.label2.value = 'error';
            setTimeout(() => {
                this.label2.value = '';
            }, 2000);
        },
        parent() {
            return this.block;
        },
    })
    field2: ui.fields.DropdownList<ShowCaseProductCategory>;

    @ui.decorators.labelField<DropdownList>({
        map,
        parent() {
            return this.block;
        },
    })
    label2: ui.fields.Label;

    @ui.decorators.block<DropdownList>({
        title: 'Field Configuration',
        parent() {
            return this.section;
        },
    })
    configuration: ui.containers.Block;

    @ui.decorators.textField<DropdownList>({
        title: 'Helper Text',
        onChange() {
            this.field1.helperText = this.helperText.value;
        },
        parent() {
            return this.configuration;
        },
    })
    helperText: ui.fields.Text;

    @ui.decorators.checkboxField<DropdownList>({
        title: 'Is Disabled',
        onChange() {
            this.field1.isDisabled = this.isDisabled.value;
        },
        parent() {
            return this.configuration;
        },
    })
    isDisabled: ui.fields.Checkbox;

    @ui.decorators.checkboxField<DropdownList>({
        title: 'Is Helper Text Hidden',
        onChange() {
            this.field1.isHelperTextHidden = this.isHelperTextHidden.value;
        },
        parent() {
            return this.configuration;
        },
    })
    isHelperTextHidden: ui.fields.Checkbox;

    @ui.decorators.checkboxField<DropdownList>({
        title: 'Is Hidden',
        onChange() {
            this.field1.isHidden = this.isHidden.value;
        },
        parent() {
            return this.configuration;
        },
    })
    isHidden: ui.fields.Checkbox;

    @ui.decorators.checkboxField<DropdownList>({
        title: 'Is Mandatory',
        onChange() {
            this.field1.isMandatory = this.isMandatory.value;
        },
        parent() {
            return this.configuration;
        },
    })
    isMandatory: ui.fields.Checkbox;

    @ui.decorators.checkboxField<DropdownList>({
        title: 'Is Read-Only',
        onChange() {
            this.field1.isReadOnly = this.isReadOnly.value;
        },
        parent() {
            return this.configuration;
        },
    })
    isReadOnly: ui.fields.Checkbox;

    @ui.decorators.checkboxField<DropdownList>({
        title: 'Is Title Hidden',
        onChange() {
            this.field1.isTitleHidden = this.isTitleHidden.value;
        },
        parent() {
            return this.configuration;
        },
    })
    isTitleHidden: ui.fields.Checkbox;

    @ui.decorators.textField<DropdownList>({
        title: 'Placeholder',
        onChange() {
            this.field1.placeholder = this.placeholder.value;
        },
        parent() {
            return this.configuration;
        },
    })
    placeholder: ui.fields.Text;

    @ui.decorators.textField<DropdownList>({
        title: 'Title',
        onChange() {
            this.field1.title = this.title.value;
        },
        parent() {
            return this.configuration;
        },
    })
    title: ui.fields.Text;

    @ui.decorators.textField<DropdownList>({
        title: 'Value',
        onChange() {
            if (this.field1.value !== this.value.value) {
                this.field1.value = this.value.value as any;
            }
        },
        parent() {
            return this.configuration;
        },
    })
    value: ui.fields.Text;

    @ui.decorators.buttonField<DropdownList>({
        map() {
            return 'Focus Field';
        },
        onClick() {
            this.field1.focus();
        },
        parent() {
            return this.configuration;
        },
    })
    focus: ui.fields.Button;

    @ui.decorators.buttonField<DropdownList>({
        map() {
            return 'Validate Field';
        },
        onClick() {
            this.field1.validate();
        },
        parent() {
            return this.configuration;
        },
    })
    validate: ui.fields.Button;

    @ui.decorators.block<DropdownList>({
        title: 'Field Sizes',
        parent() {
            return this.section;
        },
    })
    sizes: ui.containers.Block;

    @ui.decorators.dropdownListField<DropdownList>({
        isTransient: true,
        options: ['one', 'two', 'three'],
        size: 'small',
        title: 'Small',
        parent() {
            return this.sizes;
        },
    })
    small: ui.fields.DropdownList;

    @ui.decorators.dropdownListField<DropdownList>({
        isTransient: true,
        options: ['one', 'two', 'three'],
        size: 'medium',
        title: 'Medium',
        parent() {
            return this.sizes;
        },
    })
    medium: ui.fields.DropdownList;

    @ui.decorators.dropdownListField<DropdownList>({
        isTransient: true,
        options: ['one', 'two', 'three'],
        size: 'large',
        title: 'Large',
        parent() {
            return this.sizes;
        },
    })
    large: ui.fields.DropdownList;

    @ui.decorators.block<DropdownList>({
        title: 'Validation',
        parent() {
            return this.section;
        },
    })
    validation: ui.containers.Block;

    @ui.decorators.dropdownListField<DropdownList>({
        isFullWidth: true,
        isMandatory: true,
        isTransient: true,
        options: ['Success', 'Error'],
        title: 'Dropdown List with Custom Validation',
        parent() {
            return this.validation;
        },
        validation(value) {
            return value === 'Error' ? 'Oh, you selected an error.' : '';
        },
    })
    validated: ui.fields.DropdownList;

    @ui.decorators.block<DropdownList>({
        title: 'Error Handlers',
        parent() {
            return this.section;
        },
    })
    errors: ui.containers.Block;

    @ui.decorators.dropdownListField<DropdownList>({
        isTransient: true,
        options: ['One', 'Two', 'Three'],
        title: 'Dropdown List with Error Handler',
        parent() {
            return this.errors;
        },
        onChange() {
            throw new Error('Oh, an error was thrown.');
        },
        onError(error) {
            return error.message;
        },
    })
    errorHandled: ui.fields.DropdownList;

    @ui.decorators.dropdownListField<DropdownList>({
        isTransient: true,
        options: ['One', 'Two', 'Three'],
        title: 'Dropdown List without Error Handler',
        parent() {
            return this.errors;
        },
        onChange() {
            throw new Error('Oh, an error was thrown.');
        },
    })
    errorUnhandled: ui.fields.DropdownList;

    @ui.decorators.block<DropdownList>({
        title: 'Localization',
        parent() {
            return this.section;
        },
    })
    localization: ui.containers.Block;

    @ui.decorators.dropdownListField<DropdownList>({
        isTransient: true,
        optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
        title: 'Dropdown List with Translations',
        parent() {
            return this.localization;
        },
    })
    fieldLocalized: ui.fields.DropdownList<ShowCaseProductCategory>;

    /* Additional examples */

    @ui.decorators.block<DropdownList>({
        parent() {
            return this.section;
        },
        title: 'Additional examples',
    })
    additionalBlock: ui.containers.Block;

    @ui.decorators.dropdownListField<DropdownList>({
        parent() {
            return this.additionalBlock;
        },
        title: 'Sorted with option type',
        isSortedAlphabetically: true,
        optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
    })
    sortedOptionType: ui.fields.DropdownList<ShowCaseProductCategory>;

    @ui.decorators.dropdownListField<DropdownList>({
        parent() {
            return this.additionalBlock;
        },
        title: 'Sorted with options',
        isSortedAlphabetically: true,
        options: ['Dreadful', 'Awful', 'Great', 'Bad', 'Mediocre'],
    })
    sortedWithOptions: ui.fields.DropdownList;

    @ui.decorators.dropdownListField<DropdownList>({
        parent() {
            return this.additionalBlock;
        },
        title: 'With empty value',
        hasEmptyValue: true,
        options: ['Dreadful', 'Awful', 'Great', 'Bad', 'Mediocre'],
    })
    withEmptyValue: ui.fields.DropdownList;

    @ui.decorators.section<DropdownList>({
        title: 'Nested DropdownList',
    })
    nestedSection: ui.containers.Section;

    @ui.decorators.block<DropdownList>({
        parent() {
            return this.nestedSection;
        },
    })
    nestedBlock: ui.containers.Block;

    @ui.decorators.tableField<DropdownList, ShowCaseProduct>({
        columns: [
            ui.nestedFields.text({
                bind: '_id',
                title: 'Id',
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                bind: 'product',
                title: 'Product',
                isReadOnly: true,
            }),
            ui.nestedFields.dropdownList({
                bind: 'category',
                title: 'Category (Dropdown)',
                optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
            }),
            ui.nestedFields.dropdownList({
                bind: 'category',
                title: 'Category (Dropdown with empty value)',
                hasEmptyValue: true,
                optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
            }),
            ui.nestedFields.select({
                bind: 'category',
                title: 'Category (Select)',
                optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
            }),
        ],
        isTransient: true,
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        parent() {
            return this.nestedBlock;
        },
        title: 'Table with nested DropdownList field',
    })
    table: ui.fields.Table<ShowCaseProduct>;

    @ui.decorators.separatorField<DropdownList>({
        parent() {
            return this.additionalBlock;
        },
        isFullWidth: true,
    })
    fieldSeparator1: ui.fields.Separator;

    @ui.decorators.dropdownListField<DropdownList>({
        parent() {
            return this.additionalBlock;
        },
        title: 'With warning message',
        warningMessage: 'Wow, warning!',
        optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
    })
    warningMessageField: ui.fields.DropdownList<ShowCaseProductCategory>;

    @ui.decorators.dropdownListField<DropdownList>({
        parent() {
            return this.additionalBlock;
        },
        title: 'With warning message with callback',
        helperText: 'Select "awful"',
        warningMessage() {
            if (this.warningMessageWithCallbackField.value === 'awful') {
                return 'Warning message';
            }
            return null;
        },
        optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
    })
    warningMessageWithCallbackField: ui.fields.DropdownList<ShowCaseProductCategory>;

    @ui.decorators.dropdownListField<DropdownList>({
        parent() {
            return this.additionalBlock;
        },
        title: 'With info message',
        infoMessage: 'Wow, warning!',
        optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
    })
    infoMessageField: ui.fields.DropdownList<ShowCaseProductCategory>;

    @ui.decorators.dropdownListField<DropdownList>({
        parent() {
            return this.additionalBlock;
        },
        title: 'With info message with callback',
        helperText: 'Type "awful"',
        infoMessage() {
            if (this.infoMessageWithCallbackField.value === 'awful') {
                return 'Info message';
            }
            return null;
        },
        optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
    })
    infoMessageWithCallbackField: ui.fields.DropdownList<ShowCaseProductCategory>;

    @ui.decorators.dropdownListField<DropdownList>({
        parent() {
            return this.additionalBlock;
        },
        title: 'With info message',
        warningMessage: 'Wow, warning!',
        infoMessage: 'You should not see this',
        optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
    })
    infoAndWarningMessageField: ui.fields.DropdownList<ShowCaseProductCategory>;

    @ui.decorators.dropdownListField<DropdownList>({
        parent() {
            return this.additionalBlock;
        },
        validation() {
            if (this.infoAndWarningMessageMandatoryField.value === 'good') {
                return 'Error message';
            }
            return '';
        },
        title: 'Info, warning and validation',
        warningMessage: 'Wow, warning!',
        infoMessage: 'You should not see this',
        helperText: 'Not allowed to select "good"',
        optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
    })
    infoAndWarningMessageMandatoryField: ui.fields.DropdownList<ShowCaseProductCategory>;

    @ui.decorators.block<DropdownList>({
        parent() {
            return this.section;
        },
        title: 'Value Setters',
    })
    valueSetterBlock: ui.containers.Block;

    @ui.decorators.dropdownListField<DropdownList>({
        hasEmptyValue: true,
        isTransient: true,
        options: ['Apple', 'Banana', 'Coconut', 'Date'],
        parent() {
            return this.valueSetterBlock;
        },
        title: 'Field #1 with Empty Option',
    })
    valueSetterField1: ui.fields.DropdownList;

    @ui.decorators.buttonField<DropdownList>({
        map() {
            return 'Set Field #1 to null';
        },
        onClick() {
            this.valueSetterField1.value = null;
        },
        parent() {
            return this.valueSetterBlock;
        },
    })
    valueSetterButton1: ui.fields.Button;

    @ui.decorators.dropdownListField<DropdownList>({
        hasEmptyValue: false,
        isTransient: true,
        options: ['Apple', 'Banana', 'Coconut', 'Date'],
        parent() {
            return this.valueSetterBlock;
        },
        title: 'Field #2 without Empty Option',
    })
    valueSetterField2: ui.fields.DropdownList;

    @ui.decorators.buttonField<DropdownList>({
        map() {
            return 'Set Field #2 to null';
        },
        onClick() {
            this.valueSetterField2.value = null;
        },
        parent() {
            return this.valueSetterBlock;
        },
    })
    valueSetterButton2: ui.fields.Button;
}
