import * as ui from '@sage/xtrem-ui';
import { fields } from '../menu-items/fields';

@ui.decorators.page<Separator>({
    authorizationCode: 'BSCFLDS',
    module: 'show-case',
    menuItem: fields,
    title: 'Separator',
    category: 'SHOWCASE',
    isTransient: true,
    areNavigationTabsHidden: true,
})
export class Separator extends ui.Page {
    @ui.decorators.section<Separator>({
        isTitleHidden: true,
    })
    section: ui.containers.Section;

    @ui.decorators.block<Separator>({
        parent() {
            return this.section;
        },
        title: 'Vertical example',
    })
    fieldBlock1: ui.containers.Block;

    @ui.decorators.textField<Separator>({
        parent() {
            return this.fieldBlock1;
        },
        title: 'Previous field',
    })
    previousField1: ui.fields.Text;

    @ui.decorators.separatorField<Separator>({
        parent() {
            return this.fieldBlock1;
        },
        onClick() {
            this.clickTriggered1.isHidden = false;
            setTimeout(() => {
                this.clickTriggered1.isHidden = true;
            }, 5000);
        },
    })
    field1: ui.fields.Separator;

    @ui.decorators.textField<Separator>({
        parent() {
            return this.fieldBlock1;
        },
        title: 'Next field',
    })
    nextField1: ui.fields.Text;

    @ui.decorators.labelField<Separator>({
        parent() {
            return this.fieldBlock1;
        },
        isHidden: true,
        map() {
            return 'Click was triggered';
        },
    })
    clickTriggered1: ui.fields.Label;

    @ui.decorators.separatorField<Separator>({
        parent() {
            return this.fieldBlock1;
        },
        isFullWidth: true,
        isInvisible: true,
    })
    sep1: ui.fields.Separator;

    @ui.decorators.checkboxField<Separator>({
        parent() {
            return this.fieldBlock1;
        },
        title: 'Is disabled',
        onChange() {
            this.field1.isDisabled = this.isDisabled1.value;
        },
    })
    isDisabled1: ui.fields.Checkbox;

    @ui.decorators.checkboxField<Separator>({
        parent() {
            return this.fieldBlock1;
        },
        title: 'Is Hidden',
        onChange() {
            this.field1.isHidden = this.isHidden1.value;
        },
    })
    isHidden1: ui.fields.Checkbox;

    @ui.decorators.checkboxField<Separator>({
        parent() {
            return this.fieldBlock1;
        },
        title: 'Is Transparent',
        onChange() {
            this.field1.isInvisible = this.isInvisible1.value;
        },
    })
    isInvisible1: ui.fields.Checkbox;

    @ui.decorators.block<Separator>({
        parent() {
            return this.section;
        },
        title: 'Horizontal example example',
    })
    fieldBlock2: ui.containers.Block;

    @ui.decorators.textField<Separator>({
        parent() {
            return this.fieldBlock2;
        },
        title: 'Previous field',
    })
    previousField2: ui.fields.Text;

    @ui.decorators.separatorField<Separator>({
        isFullWidth: true,
        parent() {
            return this.fieldBlock2;
        },
        onClick() {
            this.clickTriggered2.isHidden = false;
            setTimeout(() => {
                this.clickTriggered2.isHidden = true;
            }, 5000);
        },
    })
    field2: ui.fields.Separator;

    @ui.decorators.textField<Separator>({
        parent() {
            return this.fieldBlock2;
        },
        title: 'Next field',
    })
    nextField2: ui.fields.Text;

    @ui.decorators.labelField<Separator>({
        parent() {
            return this.fieldBlock2;
        },
        isHidden: true,
        map() {
            return 'Click was triggered';
        },
    })
    clickTriggered2: ui.fields.Label;

    @ui.decorators.separatorField<Separator>({
        parent() {
            return this.fieldBlock2;
        },
        isFullWidth: true,
        isInvisible: true,
    })
    sep2: ui.fields.Separator;

    @ui.decorators.checkboxField<Separator>({
        parent() {
            return this.fieldBlock2;
        },
        title: 'Is disabled',
        onChange() {
            this.field2.isDisabled = this.isDisabled2.value;
        },
    })
    isDisabled2: ui.fields.Checkbox;

    @ui.decorators.checkboxField<Separator>({
        parent() {
            return this.fieldBlock2;
        },
        title: 'Is Hidden',
        onChange() {
            this.field2.isHidden = this.isHidden2.value;
        },
    })
    isHidden2: ui.fields.Checkbox;

    @ui.decorators.checkboxField<Separator>({
        parent() {
            return this.fieldBlock2;
        },
        title: 'Is Transparent',
        onChange() {
            this.field2.isInvisible = this.isInvisible2.value;
        },
    })
    isInvisible2: ui.fields.Checkbox;
}
