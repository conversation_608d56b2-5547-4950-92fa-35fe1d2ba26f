import * as ui from '@sage/xtrem-ui';
import { fields } from '../menu-items/fields';

@ui.decorators.page<Progress>({
    authorizationCode: 'BSCFLDS',
    module: 'show-case',
    title: 'Field - Progress',
    category: 'SHOWCASE',
    isTransient: true,
    menuItem: fields,
    onLoad() {
        this.field.value = 50;
        this.value.value = 50;
        this.fullWidth.value = 75;
    },
})
export class Progress extends ui.Page {
    @ui.decorators.section<Progress>({
        title: 'Progress field',
    })
    section: ui.containers.Section;

    @ui.decorators.block<Progress>({
        parent() {
            return this.section;
        },
        title: 'Field example',
    })
    fieldBlock: ui.containers.Block;

    @ui.decorators.progressField<Progress>({
        parent() {
            return this.fieldBlock;
        },
        onClick() {
            this.clickTriggered.isHidden = false;
            setTimeout(() => {
                this.clickTriggered.isHidden = true;
            }, 5000);
        },
    })
    field: ui.fields.Progress;

    @ui.decorators.labelField<Progress>({
        parent() {
            return this.fieldBlock;
        },
        isHidden: true,
        map() {
            return 'Click was triggered';
        },
    })
    clickTriggered: ui.fields.Label;

    @ui.decorators.block<Progress>({
        parent() {
            return this.section;
        },
        title: 'Configuration',
    })
    configurationBlock: ui.containers.Block;

    @ui.decorators.textField<Progress>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Helper text',
        onChange() {
            this.field.helperText = this.helperText.value;
        },
    })
    helperText: ui.fields.Text;

    @ui.decorators.textField<Progress>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Max progress label',
        onChange() {
            this.field.maxProgressLabel = this.maxProgressLabel.value;
        },
    })
    maxProgressLabel: ui.fields.Text;

    @ui.decorators.textField<Progress>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Current progress label',
        onChange() {
            this.field.currentProgressLabel = this.currentProgressLabel.value;
        },
    })
    currentProgressLabel: ui.fields.Text;

    @ui.decorators.checkboxField<Progress>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is disabled',
        onChange() {
            this.field.isDisabled = this.isDisabled.value;
        },
    })
    isDisabled: ui.fields.Checkbox;

    @ui.decorators.checkboxField<Progress>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Are progress labels hidden?',
        onChange() {
            this.field.areProgressLabelsHidden = this.areProgressLabelsHidden.value;
        },
    })
    areProgressLabelsHidden: ui.fields.Checkbox;

    @ui.decorators.checkboxField<Progress>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is helper text hidden',
        onChange() {
            this.field.isHelperTextHidden = this.isHelperTextHidden.value;
        },
    })
    isHelperTextHidden: ui.fields.Checkbox;

    @ui.decorators.checkboxField<Progress>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is hidden',
        onChange() {
            this.field.isHidden = this.isHidden.value;
        },
    })
    isHidden: ui.fields.Checkbox;

    @ui.decorators.checkboxField<Progress>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is title hidden',
        onChange() {
            this.field.isTitleHidden = this.isTitleHidden.value;
        },
    })
    isTitleHidden: ui.fields.Checkbox;

    @ui.decorators.textField<Progress>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Title',
        onChange() {
            this.field.title = this.title.value;
        },
    })
    title: ui.fields.Text;

    @ui.decorators.numericField<Progress>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Value',
        onChange() {
            this.field.value = this.value.value;
        },
    })
    value: ui.fields.Numeric;

    @ui.decorators.block<Progress>({
        parent() {
            return this.section;
        },
        title: 'Additional examples',
    })
    additionalBlock: ui.containers.Block;

    @ui.decorators.progressField<Progress>({
        parent() {
            return this.additionalBlock;
        },
        title: 'Full width example',
        isFullWidth: true,
    })
    fullWidth: ui.fields.Progress;
}
