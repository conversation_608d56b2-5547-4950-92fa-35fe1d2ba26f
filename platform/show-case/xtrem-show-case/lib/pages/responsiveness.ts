import * as ui from '@sage/xtrem-ui';
import { misc } from '../menu-items/misc';

@ui.decorators.page<Responsiveness>({
    areNavigationTabsHidden: true,
    authorizationCode: 'BSCRSPNSV',
    module: 'show-case',
    title: 'Responsiveness',
    category: 'SHOWCASE',
    isTransient: true,
    menuItem: misc,
})
export class Responsiveness extends ui.Page {
    @ui.decorators.section<Responsiveness>({
        title: 'Responsiveness',
    })
    responsiveSection: ui.containers.Section;

    @ui.decorators.block<Responsiveness>({
        parent() {
            return this.responsiveSection;
        },
        title: 'This block is visible until M breakpoint (width < 960px)',
        isHiddenDesktop: true,
    })
    desktopHiddenBlock: ui.containers.Block;

    @ui.decorators.block<Responsiveness>({
        parent() {
            return this.responsiveSection;
        },
        title: 'This block is visible from M breakpoint on (width >= 960px)',
        isHiddenMobile: true,
    })
    mobileHiddenBlock: ui.containers.Block;
}
