import {
    Graph<PERSON><PERSON>,
    ShowCaseInvoice,
    ShowCaseInvoiceLine as ShowCaseInvoiceLineNode,
    ShowCaseProduct,
} from '@sage/xtrem-show-case-api';
import { setOrderOfPageHeaderDropDownActions } from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import * as ui from '@sage/xtrem-ui';
import { applicationPages } from '../menu-items/application-pages';

@ui.decorators.page<ShowCaseInvoiceLine, ShowCaseInvoiceLineNode>({
    authorizationCode: 'SHCINVLN',
    businessActions() {
        return [this.refreshPage, this.emptyPage, this.$standardSaveAction];
    },
    category: 'SHOWCASE',
    createAction() {
        return this.$standardNewAction;
    },
    headerDropDownActions() {
        return setOrderOfPageHeaderDropDownActions({
            remove: [this.$standardDeleteAction],
            dropDownBusinessActions: [this.$standardOpenRecordHistoryAction],
        });
    },
    headerQuickActions() {
        return [this.$standardDuplicateAction, this.$standardNewAction];
    },
    idField() {
        return [this._id];
    },
    menuItem: applicationPages,
    module: 'show-case',
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.text({ bind: '_id', title: 'Id' }),
            line2: ui.nestedFields.reference<ShowCaseInvoiceLine, ShowCaseInvoiceLineNode, ShowCaseInvoice>({
                bind: 'invoice',
                node: '@sage/xtrem-show-case/ShowCaseInvoice',
                title: 'Invoice',
                valueField: '_id',
            }),
            line3: ui.nestedFields.reference<ShowCaseInvoiceLine, ShowCaseInvoiceLineNode, ShowCaseProduct>({
                bind: 'product',
                node: '@sage/xtrem-show-case/ShowCaseProduct',
                title: 'Product',
                valueField: 'product',
            }),
        },
    },
    node: '@sage/xtrem-show-case/ShowCaseInvoiceLine',
    objectTypePlural: 'Invoice Lines',
    objectTypeSingular: 'Invoice Line',
    title: 'ShowCase - Invoice Line',
    priority: 400,
})
export class ShowCaseInvoiceLine extends ui.Page<GraphApi> {
    @ui.decorators.section<ShowCaseInvoiceLine>({})
    section: ui.containers.Section;

    @ui.decorators.block<ShowCaseInvoiceLine>({
        parent() {
            return this.section;
        },
    })
    block: ui.containers.Block;

    @ui.decorators.textField<ShowCaseInvoiceLine>({
        bind: '_id',
        isReadOnly: true,
        parent() {
            return this.block;
        },
        title: 'Id',
        width: 'medium',
    })
    _id: ui.fields.Text;

    @ui.decorators.referenceField<ShowCaseInvoiceLine, ShowCaseInvoice>({
        bind: 'invoice',
        columns: [ui.nestedFields.text({ bind: '_id', title: 'Invoice' })],
        helperTextField: '_id',
        node: '@sage/xtrem-show-case/ShowCaseInvoice',
        parent() {
            return this.block;
        },
        title: 'Invoice',
        valueField: '_id',
        width: 'medium',
    })
    invoice: ui.fields.Reference<ShowCaseInvoice>;

    @ui.decorators.referenceField<ShowCaseInvoiceLine, ShowCaseProduct>({
        bind: 'product',
        columns: [
            ui.nestedFields.text({ bind: 'product', title: 'Product' }),
            ui.nestedFields.text({ bind: 'barcode', title: 'Barcode' }),
        ],
        helperTextField: '_id',
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        parent() {
            return this.block;
        },
        title: 'Product',
        valueField: 'product',
        width: 'medium',
    })
    product: ui.fields.Reference<ShowCaseProduct>;

    @ui.decorators.numericField<ShowCaseInvoiceLine>({
        bind: 'orderQuantity',
        parent() {
            return this.block;
        },
        scale: 0,
        title: 'Order Quantity',
        width: 'medium',
    })
    orderQuantity: ui.fields.Numeric;

    @ui.decorators.numericField<ShowCaseInvoiceLine>({
        bind: 'orderQuantity',
        parent() {
            return this.block;
        },
        prefix: '$',
        scale: 2,
        title: 'Price',
        width: 'medium',
    })
    netPrice: ui.fields.Numeric;

    @ui.decorators.textField<ShowCaseInvoiceLine>({
        bind: 'priceDiscount',
        parent() {
            return this.block;
        },
        title: 'Price Discount',
        width: 'medium',
    })
    priceDiscount: ui.fields.Text;

    @ui.decorators.dropdownListField<ShowCaseInvoiceLine>({
        bind: 'discountType',
        optionType: '@sage/xtrem-show-case/ShowCaseDiscountType',
        parent() {
            return this.block;
        },
        title: 'Discount Type',
        width: 'medium',
    })
    discountType: ui.fields.DropdownList;

    @ui.decorators.textAreaField<ShowCaseInvoiceLine>({
        bind: 'comments',
        parent() {
            return this.block;
        },
        title: 'Comments',
        width: 'large',
    })
    comments: ui.fields.TextArea;

    @ui.decorators.pageAction<ShowCaseInvoiceLine>({
        title: 'Refresh',
        onClick() {
            return this.$.router.refresh();
        },
    })
    refreshPage: ui.PageAction;

    @ui.decorators.pageAction<ShowCaseInvoiceLine>({
        title: 'Empty page',
        onClick() {
            return this.$.router.emptyPage();
        },
    })
    emptyPage: ui.PageAction;
}
