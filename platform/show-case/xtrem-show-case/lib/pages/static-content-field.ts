import { <PERSON><PERSON>h<PERSON><PERSON>, ShowCaseProvider as ShowCaseProviderNode } from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import { fields } from '../menu-items/fields';

@ui.decorators.page<StaticContentField, ShowCaseProviderNode>({
    authorizationCode: 'SHCPRVD',
    category: 'SHOWCASE',
    menuItem: fields,
    module: 'show-case',
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.text({ bind: 'textField' }),
            line2: ui.nestedFields.text({ bind: '_id' }),
            line3: ui.nestedFields.date({ bind: 'dateField' }),
        },
    },
    node: '@sage/xtrem-show-case/ShowCaseProvider',
    title: 'Static Content Field',
    onLoad() {
        this.maxVisibleLines.value = 'All';
    },
})
export class StaticContentField extends ui.Page<GraphApi> {
    @ui.decorators.section<StaticContentField>({})
    section: ui.containers.Section;

    @ui.decorators.block<StaticContentField>({
        parent() {
            return this.section;
        },
    })
    block: ui.containers.Block;

    @ui.decorators.staticContentField<StaticContentField>({
        bind: 'textField',
        parent() {
            return this.block;
        },
        title: 'Bound example',
    })
    boundField: ui.fields.StaticContent;

    @ui.decorators.block<StaticContentField>({
        parent() {
            return this.section;
        },
    })
    contentExampleBlock: ui.containers.Block;

    @ui.decorators.staticContentField<StaticContentField>({
        isTransient: true,
        parent() {
            return this.block;
        },
        content: 'This is some long content\nwith line breaks and\nit can be translated too.',
    })
    longContentField: ui.fields.StaticContent;

    @ui.decorators.textField<StaticContentField>({
        isTransient: true,
        parent() {
            return this.contentExampleBlock;
        },
        title: 'Helper text',
        onChange() {
            this.longContentField.helperText = this.helperText.value || '';
        },
    })
    helperText: ui.fields.Text;

    @ui.decorators.textField<StaticContentField>({
        isTransient: true,
        parent() {
            return this.contentExampleBlock;
        },
        title: 'Title',
        onChange() {
            this.longContentField.title = this.title.value || '';
        },
    })
    title: ui.fields.Text;

    @ui.decorators.checkboxField<StaticContentField>({
        isTransient: true,
        parent() {
            return this.contentExampleBlock;
        },
        title: 'Is hidden',
        onChange() {
            this.longContentField.isHidden = !!this.isHidden.value;
        },
    })
    isHidden: ui.fields.Checkbox;

    @ui.decorators.dropdownListField<StaticContentField>({
        isTransient: true,
        parent() {
            return this.contentExampleBlock;
        },
        options: ['All', '1', '2', '3', '4', '5'],
        title: 'Number of visible lines',
        onChange() {
            const newValue = this.maxVisibleLines.value === 'All' ? undefined : Number(this.maxVisibleLines.value);
            this.boundField.maxVisibleLines = newValue;
            this.longContentField.maxVisibleLines = newValue;
            this.markdownField.maxVisibleLines = newValue;
        },
    })
    maxVisibleLines: ui.fields.DropdownList;

    @ui.decorators.staticContentField<StaticContentField>({
        isTransient: true,
        isMarkdown: true,
        title: 'Markdown content',
        parent() {
            return this.block;
        },
        content:
            '## Hi there!\n\nThis **is** some _markdown_ content.\n\n- See \n\n- It can do\n\n- bullet points\n\nAnd the dangerous tags are escaped: <iframe src="http://wwww.sage.com"></iframe>\n\n<script>alert("this could be dangerous");</script>',
    })
    markdownField: ui.fields.StaticContent;
}
