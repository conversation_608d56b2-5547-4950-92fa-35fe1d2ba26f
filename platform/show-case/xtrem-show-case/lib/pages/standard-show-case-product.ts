import type { ShowCaseProductCategory } from '@sage/xtrem-show-case-api';
import {
    Graph<PERSON><PERSON>,
    ShowCaseEmployee,
    ShowCaseProduct as ShowCaseProductNode,
    ShowCaseProvider,
} from '@sage/xtrem-show-case-api';
import {
    setApplicativePageCrudActions,
    setOrderOfPageHeaderDropDownActions,
} from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import * as ui from '@sage/xtrem-ui';
import { applicationPages } from '../menu-items/application-pages';

@ui.decorators.page<StandardShowCaseProduct, ShowCaseProductNode>({
    authorizationCode: 'SHCPRVD',
    category: 'SHOWCASE',
    title: 'Standard Product page',
    menuItem: applicationPages,
    objectTypeSingular: 'Product',
    objectTypePlural: 'Products',
    idField() {
        return this._id;
    },
    businessActions() {
        return [this.$standardCancelAction, this.$standardSaveAction];
    },
    headerQuickActions() {
        return [this.$standardDuplicateAction];
    },
    headerDropDownActions() {
        return setOrderOfPageHeaderDropDownActions({
            remove: [this.$standardDeleteAction],
            dropDownBusinessActions: [this.$standardOpenRecordHistoryAction],
        });
    },
    headerLabel() {
        return this.category;
    },
    headerField() {
        return this._id;
    },
    module: 'show-case',
    navigationPanel: {
        bulkActions: [
            {
                mutation: 'export',
                title: 'Export',
                buttonType: 'secondary',
                icon: 'file_excel',
                isDestructive: false,
                id: 'export',
            },
        ],
        listItem: {
            title: ui.nestedFields.text({ bind: 'product', title: 'Product', groupAggregationMethod: 'distinctCount' }),
            titleRight: ui.nestedFields.text({ bind: '_id', title: 'ID', canFilter: false }),
            line2: ui.nestedFields.text({ bind: 'description', title: 'Description', groupAggregationMethod: 'min' }),
            listPrice: ui.nestedFields.numeric({
                bind: 'listPrice',
                title: 'List Price',
                isHiddenOnMainField: true,
                scale: 2,
                groupAggregationMethod: 'sum',
            }),
            category: ui.nestedFields.label({ bind: 'category', isHiddenOnMainField: true }),
            provider: ui.nestedFields.reference({
                bind: 'provider',
                isHiddenOnMainField: true,
                groupAggregationMethod: 'distinctCount',
            }),
            releaseDate: ui.nestedFields.date({
                bind: 'releaseDate',
                isHiddenOnMainField: true,
            }),
        },
    },
    node: '@sage/xtrem-show-case/ShowCaseProduct',
    priority: 500,
    onLoad() {
        setApplicativePageCrudActions<StandardShowCaseProduct>({
            page: this,
            isDirty: false,
            save: this.$standardSaveAction,
            cancel: this.$standardCancelAction,
            duplicate: this.$standardDuplicateAction,
            remove: this.$standardDeleteAction,
            actions: [],
        });
    },
    onDirtyStateUpdated(isDirty: boolean) {
        setApplicativePageCrudActions<StandardShowCaseProduct>({
            page: this,
            isDirty,
            save: this.$standardSaveAction,
            cancel: this.$standardCancelAction,
            duplicate: this.$standardDuplicateAction,
            remove: this.$standardDeleteAction,
            actions: [],
        });
    },
})
export class StandardShowCaseProduct extends ui.Page<GraphApi> {
    @ui.decorators.labelField<StandardShowCaseProduct>({
        parent() {
            return this.block;
        },
        title: 'Category',
        optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
    })
    category: ui.fields.Label<ShowCaseProductCategory>;

    @ui.decorators.section<StandardShowCaseProduct>({})
    section: ui.containers.Section;

    @ui.decorators.block<StandardShowCaseProduct>({
        parent() {
            return this.section;
        },
    })
    block: ui.containers.Block;

    @ui.decorators.textField<StandardShowCaseProduct>({
        parent() {
            return this.block;
        },
        title: 'ID',
        isReadOnly: true,
    })
    _id: ui.fields.Text;

    @ui.decorators.textField<StandardShowCaseProduct>({
        parent() {
            return this.block;
        },
        title: 'Product',
        isMandatory: true,
    })
    product: ui.fields.Text;

    @ui.decorators.textField<StandardShowCaseProduct>({
        parent() {
            return this.block;
        },
        title: 'Description',
    })
    description: ui.fields.Text;

    @ui.decorators.checkboxField<StandardShowCaseProduct>({
        parent() {
            return this.block;
        },
        title: 'Hot product',
    })
    hotProduct: ui.fields.Checkbox;

    @ui.decorators.numericField<StandardShowCaseProduct>({
        parent() {
            return this.block;
        },
        title: 'Quantity',
        fetchesDefaults: true,
    })
    qty: ui.fields.Numeric;

    @ui.decorators.numericField<StandardShowCaseProduct>({
        parent() {
            return this.block;
        },
        title: 'Stock',
    })
    st: ui.fields.Numeric;

    @ui.decorators.numericField<StandardShowCaseProduct>({
        parent() {
            return this.block;
        },
        title: 'List price',
        scale: 2,
        fetchesDefaults: true,
    })
    listPrice: ui.fields.Numeric;

    @ui.decorators.progressField<StandardShowCaseProduct>({
        parent() {
            return this.block;
        },
        title: 'Progress',
    })
    progress: ui.fields.Progress;

    @ui.decorators.numericField<StandardShowCaseProduct>({
        parent() {
            return this.block;
        },
        title: 'Tax',
        scale: 2,
    })
    tax: ui.fields.Numeric;

    @ui.decorators.numericField<StandardShowCaseProduct>({
        parent() {
            return this.block;
        },
        title: 'Net price',
        scale: 2,
    })
    netPrice: ui.fields.Numeric;

    @ui.decorators.numericField<StandardShowCaseProduct>({
        parent() {
            return this.block;
        },
        title: 'Discount',
    })
    discount: ui.fields.Numeric;

    @ui.decorators.numericField<StandardShowCaseProduct>({
        parent() {
            return this.block;
        },
        title: 'Amount',
        scale: 2,
    })
    amount: ui.fields.Numeric;

    @ui.decorators.referenceField<StandardShowCaseProduct, ShowCaseProvider>({
        parent() {
            return this.block;
        },
        columns: [ui.nestedFields.text({ bind: 'textField' })],
        node: '@sage/xtrem-show-case/ShowCaseProvider',
        title: 'Provider',
        valueField: 'textField',
        helperTextField: '_id',
        minLookupCharacters: 0,
        tunnelPage: '@sage/xtrem-show-case/StandardShowCaseProvider',
    })
    provider: ui.fields.Reference;

    @ui.decorators.referenceField<StandardShowCaseProduct, ShowCaseEmployee>({
        parent() {
            return this.block;
        },
        bind: 'designerEmployee',
        node: '@sage/xtrem-show-case/ShowCaseEmployee',
        tunnelPage: '@sage/xtrem-show-case/ShowCaseEmployee',
        title: 'Product designed by',
        valueField: 'firstName',
        helperTextField: 'lastName',
        validation() {
            if (this.designerEmployee.value?.firstName === 'John') {
                return 'No Johns allowed.';
            }
            return undefined;
        },
        onChange() {
            this.$.showToast('The designer employee field was changed.');
        },
        columns: [
            ui.nestedFields.text({ bind: 'firstName', title: 'First name' }),
            ui.nestedFields.text({ bind: 'lastName', title: 'Last name' }),
        ],
        createTunnelLinkText: 'Create a new employee',
    })
    designerEmployee: ui.fields.Reference<ShowCaseEmployee>;

    @ui.decorators.dateField<StandardShowCaseProduct>({
        parent() {
            return this.block;
        },
        title: 'Release date',
    })
    releaseDate: ui.fields.Date;

    @ui.decorators.dateField<StandardShowCaseProduct>({
        parent() {
            return this.block;
        },
        title: 'Ending date',
    })
    endingDate: ui.fields.Date;

    @ui.decorators.dateTimeField<StandardShowCaseProduct>({
        parent() {
            return this.block;
        },
        title: 'Created at',
    })
    createdAt: ui.fields.Datetime;

    @ui.decorators.dateTimeRangeField<StandardShowCaseProduct>({
        parent() {
            return this.block;
        },
        width: 'large',
        title: 'Manufactured within',
        timeZone: 'Australia/Sydney',
    })
    manufacturedWithin: ui.fields.DatetimeRange;
}
