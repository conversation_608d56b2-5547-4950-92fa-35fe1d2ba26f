import * as ui from '@sage/xtrem-ui';
import { fields } from '../menu-items/fields';
import { WorkflowVariable } from '@sage/xtrem-shared';

@ui.decorators.page<DynamicSelectField>({
    menuItem: fields,
    title: 'Dynamic select Field',
    subtitle: 'Fields',
    isTransient: true,
    headerSection() {
        return this.headerSection;
    },
    onLoad() {},
})
export class DynamicSelectField extends ui.Page {
    @ui.decorators.section<DynamicSelectField>({
        title: 'Dynamic select Field',
        isTitleHidden: true,
    })
    headerSection: ui.containers.Section;

    @ui.decorators.section<DynamicSelectField>({
        title: 'Dynamic select Field Example',
        isTitleHidden: true,
    })
    section: ui.containers.Section;

    @ui.decorators.block<DynamicSelectField>({
        parent() {
            return this.headerSection;
        },
        title: 'Introduction',
        width: 'medium',
    })
    introductionBlock: ui.containers.Block;

    @ui.decorators.staticContentField<DynamicSelectField>({
        parent() {
            return this.introductionBlock;
        },
        isTitleHidden: true,
        isFullWidth: true,
        content: 'Dynamic select Fields allow users to select values from a dynamically generated list.',
    })
    description: ui.fields.StaticContent;

    @ui.decorators.linkField<DynamicSelectField>({
        parent() {
            return this.introductionBlock;
        },
        title: 'Source code',
        isFullWidth: true,
        map() {
            return ui.localize('@sage/xtrem-show-case/check-source-code', 'Check it on GitHub');
        },
        page: 'https://github.com/Sage-ERP-X3/xtrem/blob/master/platform/show-case/xtrem-show-case/lib/pages/dynamic-select-field.ts',
    })
    sourceCodeLink: ui.fields.Link;

    @ui.decorators.block<DynamicSelectField>({
        parent() {
            return this.headerSection;
        },
        title: 'Field example',
        width: 'medium',
    })
    fieldBlock: ui.containers.Block;

    @ui.decorators.dynamicSelectField<DynamicSelectField>({
        parent() {
            return this.fieldBlock;
        },
        isFullWidth: true,
        title: 'Dynamic select',
        options() {
            return [
                { id: '1', value: '1', displayedAs: 'One' },
                { id: '2', value: '2', displayedAs: 'Two' },
                { id: '3', value: '3', displayedAs: 'Three' },
            ];
        },
        onChange() {
            this.eventTriggered.isHidden = false;
            this.eventTriggered.value = 'onChange event triggered';
            setTimeout(() => {
                this.eventTriggered.value = '';
                this.eventTriggered.isHidden = true;
            }, 2000);
            this.selectedValue.value = this.dynamicSelect.value;
        },
        onClick() {
            this.eventTriggered.isHidden = false;
            this.eventTriggered.value = 'onClick event triggered';
            setTimeout(() => {
                this.eventTriggered.value = '';
                this.eventTriggered.isHidden = true;
            }, 2000);
        },
        async populateList(currentList) {
            const inputVariables = [
                { node: 'Company', path: 'company._id', title: 'Company / 🆔', type: 'IntReference' },
                { path: 'company.id', title: 'Company / ID', type: 'String' },
            ];
            const oldRootPaths: any = [];
            const onlyWritable = false;
            const result = (await this.$.dialog.page('@sage/xtrem-workflow/WorkflowSelectVariablesDialog', {
                inputVariables: JSON.stringify(inputVariables),
                oldRootPaths: JSON.stringify(oldRootPaths),
                onlyWritable: !!onlyWritable,
            })) as { selectedVariables: WorkflowVariable[]; selectedRootVariable: WorkflowVariable } | undefined;

            const listItems = result?.selectedVariables.map(v => ({
                id: v.path,
                value: `{{${v.path}}}`,
                displayedAs: v.title,
            }));

            return [...currentList, ...(listItems || [])];
        },
    })
    dynamicSelect: ui.fields.DynamicSelect;

    @ui.decorators.labelField<DynamicSelectField>({
        title: 'Event triggered',
        parent() {
            return this.fieldBlock;
        },
        isHidden: true,
    })
    eventTriggered: ui.fields.Label;

    @ui.decorators.labelField<DynamicSelectField>({
        parent() {
            return this.fieldBlock;
        },
        title: 'Selected value',
        isFullWidth: true,
    })
    selectedValue: ui.fields.Label;

    @ui.decorators.block<DynamicSelectField>({
        parent() {
            return this.section;
        },
        title: 'Configuration',
    })
    configurationBlock: ui.containers.Block;

    @ui.decorators.selectField<DynamicSelectField>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Mode',
        options: ['select', 'input'],
        onChange() {
            this.dynamicSelect.mode = (this.mode.value as 'select' | 'input') || 'select';
        },
    })
    mode: ui.fields.Select;

    @ui.decorators.checkboxField<DynamicSelectField>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is disabled',
        isFullWidth: true,
        isReversed: true,
        onChange() {
            this.dynamicSelect.isDisabled = !!this.isDisabled.value;
        },
    })
    isDisabled: ui.fields.Checkbox;

    @ui.decorators.checkboxField<DynamicSelectField>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is read only',
        isFullWidth: true,
        isReversed: true,
        onChange() {
            this.dynamicSelect.isReadOnly = !!this.isReadOnly.value;
        },
    })
    isReadOnly: ui.fields.Checkbox;

    @ui.decorators.checkboxField<DynamicSelectField>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is hidden',
        isFullWidth: true,
        isReversed: true,
        onChange() {
            this.dynamicSelect.isHidden = !!this.isHidden.value;
        },
    })
    isHidden: ui.fields.Checkbox;

    @ui.decorators.textField<DynamicSelectField>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Title',
        isFullWidth: true,
        onChange() {
            this.dynamicSelect.title = this.title.value || '';
        },
    })
    title: ui.fields.Text;

    @ui.decorators.checkboxField<DynamicSelectField>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is title hidden',
        isFullWidth: true,
        isReversed: true,
        onChange() {
            this.dynamicSelect.isTitleHidden = !!this.isTitleHidden.value;
        },
    })
    isTitleHidden: ui.fields.Checkbox;

    @ui.decorators.textField<DynamicSelectField>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Helper text',
        isFullWidth: true,
        onChange() {
            this.dynamicSelect.helperText = this.helperText.value || '';
        },
    })
    helperText: ui.fields.Text;

    @ui.decorators.checkboxField<DynamicSelectField>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is helper text hidden',
        isFullWidth: true,
        isReversed: true,
        onChange() {
            this.dynamicSelect.isHelperTextHidden = !!this.isHelperTextHidden.value;
        },
    })
    isHelperTextHidden: ui.fields.Checkbox;

    @ui.decorators.textField<DynamicSelectField>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Value',
        isFullWidth: true,
        onChange() {
            if (this.dynamicSelect.value !== this.value.value) {
                this.dynamicSelect.value = this.value.value;
            }
        },
    })
    value: ui.fields.Text;

    @ui.decorators.buttonField<DynamicSelectField>({
        parent() {
            return this.configurationBlock;
        },
        map() {
            return 'Focus field';
        },
        onClick() {
            this.dynamicSelect.focus();
        },
    })
    focus: ui.fields.Button;

    @ui.decorators.buttonField<DynamicSelectField>({
        parent() {
            return this.configurationBlock;
        },
        map() {
            return 'Set new list';
        },
        onClick() {
            this.dynamicSelect.options = [
                { id: '7', value: '7', displayedAs: 'Seven' },
                { id: '8', value: '8', displayedAs: 'Eight' },
                { id: '9', value: '9', displayedAs: 'Nine' },
            ];
            this.$.showToast('List has been updated');
        },
    })
    setList: ui.fields.Button;

    @ui.decorators.dynamicSelectField<DynamicSelectField>({
        title: 'Dynamic select input',
        mode: 'input',
        async populateList(currentList) {
            return [
                ...currentList,
                { id: '4', value: '4', displayedAs: 'Four' },
                { id: '5', value: '5', displayedAs: 'Five' },
                { id: '6', value: '6', displayedAs: 'Six' },
            ];
        },
        parent() {
            return this.configurationBlock;
        },
        options() {
            return [
                { id: '1', value: '1', displayedAs: 'One' },
                { id: '2', value: '2', displayedAs: 'Two' },
                { id: '3', value: '3', displayedAs: 'Three' },
            ];
        },
    })
    dynamicSelect2: ui.fields.DynamicSelect;

    @ui.decorators.block<DynamicSelectField>({
        parent() {
            return this.section;
        },
        title: 'Nested Field',
    })
    block: ui.containers.Block;

    @ui.decorators.dynamicPodField<DynamicSelectField>({
        parent() {
            return this.block;
        },
        title: 'Dynamic pod',
        columns: [
            ui.nestedFields.dynamicSelect({
                bind: 'dynamicSelect',
                title: 'Dynamic select',
                isFullWidth: true,
                mode(_value, rowValue) {
                    if (rowValue?.isManual) {
                        return 'input';
                    }
                    return 'select';
                },

                options() {
                    return [
                        { id: '1', value: '1', displayedAs: 'One' },
                        { id: '2', value: '2', displayedAs: 'Two' },
                        { id: '3', value: '3', displayedAs: 'Three' },
                    ];
                },
                async populateList(currentList) {
                    const inputVariables = [
                        { node: 'Company', path: 'company._id', title: 'Company / 🆔', type: 'IntReference' },
                        { path: 'company.id', title: 'Company / ID', type: 'String' },
                    ];
                    const oldRootPaths: any = [];
                    const onlyWritable = false;
                    const result = (await this.$.dialog.page('@sage/xtrem-workflow/WorkflowSelectVariablesDialog', {
                        inputVariables: JSON.stringify(inputVariables),
                        oldRootPaths: JSON.stringify(oldRootPaths),
                        onlyWritable: !!onlyWritable,
                    })) as
                        | { selectedVariables: WorkflowVariable[]; selectedRootVariable: WorkflowVariable }
                        | undefined;

                    const listItems = result?.selectedVariables.map(v => ({
                        id: v.path,
                        value: `{{${v.path}}}`,
                        displayedAs: v.title,
                    }));

                    return [...currentList, ...(listItems || [])];
                },
            }),
            ui.nestedFields.checkbox({
                bind: 'isManual',
                title: 'Is manual',
                isFullWidth: true,
            }),
        ],
    })
    dynamicPod: ui.fields.DynamicPod;
}
