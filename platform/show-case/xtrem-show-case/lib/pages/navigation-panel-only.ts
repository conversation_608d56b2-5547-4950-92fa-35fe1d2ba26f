import { GraphApi, ShowCaseProduct } from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import { navigationPanel } from '../menu-items/navigation-panel';

@ui.decorators.page<NavigationPanelOnly, ShowCaseProduct>({
    authorizationCode: 'NAVPANEL',
    module: 'show-case',
    title: 'Navigation Panel - No page body, only nav panel',
    node: '@sage/xtrem-show-case/ShowCaseProduct',
    category: 'SHOWCASE',
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.text({ bind: 'product' }),
            titleRight: ui.nestedFields.label({ bind: 'category' }),
            line2: ui.nestedFields.reference({
                bind: 'provider',
                valueField: 'textField',
                node: '@sage/xtrem-show-case/ShowCaseProvider',
            }),
            line3: ui.nestedFields.text({ bind: 'releaseDate' }),
        },
        optionsMenu: [
            {
                title: 'All',
                graphQLFilter: {},
            },
            {
                title: 'Only from <PERSON>',
                graphQLFilter: { provider: { textField: { _eq: 'Ali Express' } } },
            },
        ],
    },
    menuItem: navigationPanel,
})
export class NavigationPanelOnly extends ui.Page<GraphApi> {
    @ui.decorators.section<NavigationPanelOnly>({
        title: 'No fields on this page.',
    })
    section: ui.containers.Section;
}
