import * as ui from '@sage/xtrem-ui';
import { misc } from '../menu-items/misc';

@ui.decorators.page<Toasts>({
    authorizationCode: 'NOTIFY',
    module: 'show-case',
    title: 'Toasts',
    category: 'SHOWCASE',
    isTransient: true,
    menuItem: misc,
    onLoad() {
        this.toastsText.value = 'Notification content';
        this.toastsTimeout.value = 4000;
        this.toastsType.value = 'default';
    },
})
export class Toasts extends ui.Page {
    @ui.decorators.section<Toasts>({
        title: 'Toasts',
    })
    toastsSection: ui.containers.Section;

    @ui.decorators.block<Toasts>({
        parent() {
            return this.toastsSection;
        },
    })
    toastsBlock: ui.containers.Block;

    @ui.decorators.textField<Toasts>({
        parent() {
            return this.toastsBlock;
        },
        title: 'Message',
    })
    toastsText: ui.fields.Text;

    @ui.decorators.numericField<Toasts>({
        parent() {
            return this.toastsBlock;
        },
        title: 'Timeout',
    })
    toastsTimeout: ui.fields.Numeric;

    @ui.decorators.selectField<Toasts>({
        parent() {
            return this.toastsBlock;
        },
        title: 'Type',
        options: ['default', 'error', 'info', 'success', 'warning'],
    })
    toastsType: ui.fields.Select;

    @ui.decorators.buttonField<Toasts>({
        parent() {
            return this.toastsBlock;
        },
        onClick() {
            const content = this.toastsText.value || 'Notification content';
            this.$.showToast(content, {
                timeout: this.toastsTimeout.value,
                type: this.toastsType.value === 'default' ? undefined : (this.toastsType.value as any),
            });
        },
        map() {
            return 'Notify!';
        },
    })
    toastsButton: ui.fields.Button;

    @ui.decorators.buttonField<Toasts>({
        parent() {
            return this.toastsBlock;
        },
        onClick() {
            this.$.removeToasts();
        },
        map() {
            return 'Remove toasts';
        },
    })
    removeButton: ui.fields.Button;
}
