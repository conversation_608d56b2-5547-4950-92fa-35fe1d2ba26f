import * as ui from '@sage/xtrem-ui';
import { fields } from '../menu-items/fields';

@ui.decorators.page<StepSequence>({
    title: 'Field - Step Sequence',
    isTransient: true,
    menuItem: fields,
    onLoad() {
        this.field.value = 'Receive';
        this.field2.value = 'Approve';
        this.field4.value = 'ok';
    },
})
export class StepSequence extends ui.Page {
    @ui.decorators.section<StepSequence>({
        title: 'Step Sequence Field',
    })
    section: ui.containers.Section;

    @ui.decorators.block<StepSequence>({
        parent() {
            return this.section;
        },
        title: 'Field Example',
    })
    fieldBlock: ui.containers.Block;

    @ui.decorators.stepSequenceField<StepSequence>({
        parent() {
            return this.fieldBlock;
        },
        options: ['Create', 'Approve', 'Order', 'Receive', 'Invoice'],
    })
    field: ui.fields.StepSequence;

    @ui.decorators.buttonField<StepSequence>({
        parent() {
            return this.fieldBlock;
        },
        map() {
            return 'Next';
        },
        onClick() {
            this.field.value = 'Invoice';
            this.next.isDisabled = true;
        },
    })
    next: ui.fields.Button;

    @ui.decorators.block<StepSequence>({
        parent() {
            return this.section;
        },
        title: 'Field Configuration',
    })
    configBlock: ui.containers.Block;

    @ui.decorators.checkboxField<StepSequence>({
        onChange() {
            this.field.isTitleHidden = this.configFieldTitleHidden.value;
        },
        parent() {
            return this.configBlock;
        },
        title: 'Is Title Hidden?',
    })
    configFieldTitleHidden: ui.fields.Checkbox;

    @ui.decorators.textField<StepSequence>({
        onChange() {
            this.field.title = this.configFieldTitle.value;
        },
        parent() {
            return this.configBlock;
        },
        title: 'Title',
        width: 'large',
    })
    configFieldTitle: ui.fields.Text;

    @ui.decorators.separatorField<StepSequence>({
        isFullWidth: true,
        isInvisible: true,
        parent() {
            return this.configBlock;
        },
    })
    configSeperator1: ui.fields.Separator;

    @ui.decorators.checkboxField<StepSequence>({
        onChange() {
            this.field.isHelperTextHidden = this.configFieldHelperTextHidden.value;
        },
        parent() {
            return this.configBlock;
        },
        title: 'Is Helper Text Hidden?',
    })
    configFieldHelperTextHidden: ui.fields.Checkbox;

    @ui.decorators.textField<StepSequence>({
        onChange() {
            this.field.helperText = this.configFieldHelperText.value;
        },
        parent() {
            return this.configBlock;
        },
        title: 'Helper Text',
        width: 'large',
    })
    configFieldHelperText: ui.fields.Text;

    @ui.decorators.separatorField<StepSequence>({
        isFullWidth: true,
        isInvisible: true,
        parent() {
            return this.configBlock;
        },
    })
    configSeperator2: ui.fields.Separator;

    @ui.decorators.checkboxField<StepSequence>({
        onChange() {
            this.field.isHidden = this.configFieldHidden.value;
        },
        parent() {
            return this.configBlock;
        },
        title: 'Is Hidden?',
    })
    configFieldHidden: ui.fields.Checkbox;

    @ui.decorators.block<StepSequence>({
        parent() {
            return this.section;
        },
        title: 'Example using isVertical',
    })
    fieldBlock4: ui.containers.Block;

    @ui.decorators.block<StepSequence>({
        parent() {
            return this.section;
        },
        title: 'Example using enum',
    })
    fieldBlock2: ui.containers.Block;

    @ui.decorators.block<StepSequence>({
        parent() {
            return this.section;
        },
        title: 'Example with runtime statuses',
    })
    fieldBlock5: ui.containers.Block;

    @ui.decorators.stepSequenceField<StepSequence>({
        parent() {
            return this.fieldBlock5;
        },
        options: ['First', 'Second', 'Third'],
    })
    field6: ui.fields.StepSequence;

    @ui.decorators.dropdownListField<StepSequence>({
        parent() {
            return this.fieldBlock5;
        },
        options: ['First', 'Second', 'Third'],
    })
    step: ui.fields.DropdownList;

    @ui.decorators.dropdownListField<StepSequence>({
        parent() {
            return this.fieldBlock5;
        },
        options: ['complete', 'incomplete', 'current'],
    })
    status: ui.fields.DropdownList;

    @ui.decorators.buttonField<StepSequence>({
        parent() {
            return this.fieldBlock5;
        },
        onClick() {
            if (this.step.value && this.status.value) {
                this.field6.statuses = {
                    ...this.field6.statuses,
                    [this.step.value]: this.status.value as 'complete' | 'incomplete' | 'current',
                };
            }
        },
        map() {
            return 'Set status';
        },
    })
    setStatusButton: ui.fields.Button;

    @ui.decorators.block<StepSequence>({
        parent() {
            return this.section;
        },
        title: 'Example using map',
    })
    fieldBlock3: ui.containers.Block;

    @ui.decorators.stepSequenceField<StepSequence>({
        parent() {
            return this.fieldBlock4;
        },
        options: ['Create', 'Approve', 'Order', 'Receive', 'Invoice'],
        isVertical: true,
        width: 'small',
    })
    field2: ui.fields.StepSequence;

    @ui.decorators.stepSequenceField<StepSequence>({
        parent() {
            return this.fieldBlock2;
        },
        optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
    })
    field4: ui.fields.StepSequence;

    @ui.decorators.stepSequenceField<StepSequence>({
        parent() {
            return this.fieldBlock3;
        },
        options: ['Create', 'Approve', 'Order', 'Receive', 'Invoice'],
        map(value: string) {
            return value.toLowerCase();
        },
    })
    field5: ui.fields.StepSequence;
}
