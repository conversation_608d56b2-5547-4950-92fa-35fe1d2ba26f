import { Graph<PERSON><PERSON>, ShowCaseProduct as ShowCaseProductNode, ShowCaseProvider } from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import { containers } from '../menu-items/containers';

@ui.decorators.page<TileContainerPage, ShowCaseProductNode>({
    module: 'show-case',
    title: 'Tile Container',
    objectTypePlural: 'Products',
    node: '@sage/xtrem-show-case/ShowCaseProduct',
    category: 'SHOWCASE',

    navigationPanel: {
        listItem: {
            title: ui.nestedFields.text({ bind: 'product', title: 'Product' }),
            titleRight: ui.nestedFields.text({ bind: '_id', title: 'ID' }),
        },
    },
    priority: 100,
    menuItem: containers,
})
export class TileContainerPage extends ui.Page<GraphApi> {
    @ui.decorators.section<TileContainerPage>({})
    section: ui.containers.Section;

    @ui.decorators.tile<TileContainerPage>({
        parent() {
            return this.section;
        },
    })
    tileContainer: ui.containers.Tile;

    @ui.decorators.textField<TileContainerPage>({
        parent() {
            return this.tileContainer;
        },
        title: 'ID',
        prefix: '*',
    })
    _id: ui.fields.Text;

    @ui.decorators.textField<TileContainerPage>({
        parent() {
            return this.tileContainer;
        },
        title: 'Product',
        isMandatory: true,
        icon: 'alert',
    })
    product: ui.fields.Text;

    @ui.decorators.textField<TileContainerPage>({
        parent() {
            return this.tileContainer;
        },
        title: 'Description',
    })
    description: ui.fields.Text;

    @ui.decorators.numericField<TileContainerPage>({
        parent() {
            return this.tileContainer;
        },
        title: 'List price',
        scale: 2,
    })
    listPrice: ui.fields.Numeric;

    @ui.decorators.numericField<TileContainerPage>({
        parent() {
            return this.tileContainer;
        },
        title: 'Tax',
        scale: 2,
    })
    tax: ui.fields.Numeric;

    @ui.decorators.numericField<TileContainerPage>({
        parent() {
            return this.tileContainer;
        },
        title: 'Net price',
        scale: 2,
    })
    netPrice: ui.fields.Numeric;

    @ui.decorators.numericField<TileContainerPage>({
        parent() {
            return this.tileContainer;
        },
        title: 'Discount',
    })
    discount: ui.fields.Numeric;

    @ui.decorators.numericField<TileContainerPage>({
        parent() {
            return this.tileContainer;
        },
        title: 'Amount',
        scale: 2,
    })
    amount: ui.fields.Numeric;

    @ui.decorators.referenceField<TileContainerPage, ShowCaseProvider>({
        parent() {
            return this.tileContainer;
        },
        columns: [ui.nestedFields.text({ bind: 'textField' })],
        node: '@sage/xtrem-show-case/ShowCaseProvider',
        title: 'Provider',
        valueField: 'textField',
        helperTextField: '_id',
        minLookupCharacters: 0,
    })
    provider: ui.fields.Reference;

    @ui.decorators.dateField<TileContainerPage>({
        parent() {
            return this.tileContainer;
        },
        title: 'Release date',
    })
    releaseDate: ui.fields.Date;

    @ui.decorators.dateField<TileContainerPage>({
        parent() {
            return this.tileContainer;
        },
        title: 'Ending date',
    })
    endingDate: ui.fields.Date;

    @ui.decorators.dropdownListField<TileContainerPage>({
        bind: 'category',
        optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
        parent() {
            return this.tileContainer;
        },
        title: 'Category (Dropdown)',
    })
    category2: ui.fields.DropdownList;

    @ui.decorators.block<TileContainerPage>({
        parent() {
            return this.section;
        },
        title: 'Settings',
    })
    settingsBlock: ui.containers.Block;

    @ui.decorators.checkboxField<TileContainerPage>({
        parent() {
            return this.settingsBlock;
        },
        isTransient: true,
        title: 'Is tile container disabled',
        onChange() {
            this.tileContainer.isDisabled = this.isTileContainerDisabled.value;
        },
    })
    isTileContainerDisabled: ui.fields.Checkbox;

    @ui.decorators.checkboxField<TileContainerPage>({
        parent() {
            return this.settingsBlock;
        },
        isTransient: true,
        title: 'Is net price disabled',
        onChange() {
            this.netPrice.isDisabled = this.isNetPriceDisabled.value;
        },
    })
    isNetPriceDisabled: ui.fields.Checkbox;
}
