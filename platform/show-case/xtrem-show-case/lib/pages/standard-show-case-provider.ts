import {
    Graph<PERSON><PERSON>,
    ShowCaseProduct,
    ShowCaseProviderAddressBinding,
    ShowCaseProvider as ShowCaseProviderNode,
} from '@sage/xtrem-show-case-api';
import {
    setApplicativePageCrudActions,
    setOrderOfPageHeaderDropDownActions,
} from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import * as ui from '@sage/xtrem-ui';
import { applicationPages } from '../menu-items/application-pages';

@ui.decorators.page<StandardShowCaseProvider, ShowCaseProviderNode>({
    authorizationCode: 'SHCPRVD',
    category: 'SHOWCASE',
    mode: 'tabs',
    title: 'Standard Provider page',
    menuItem: applicationPages,
    objectTypeSingular: 'Provider',
    objectTypePlural: 'Providers',
    has360View: true,
    on360ViewSwitched(isOn) {
        this.$.showToast(`360 view is ${isOn ? 'on' : 'off'}`);
        this.$standardOpenRecordHistoryAction.isHidden = isOn;
    },
    idField() {
        return this._id;
    },
    businessActions() {
        return [this.$standardCancelAction, this.$standardSaveAction];
    },
    headerQuickActions() {
        return [this.$standardDuplicateAction, this.navigateToShowCaseProducts];
    },
    headerDropDownActions() {
        return setOrderOfPageHeaderDropDownActions<StandardShowCaseProvider>({
            remove: [this.$standardDeleteAction],
            dropDownBusinessActions: [this.$standardOpenRecordHistoryAction],
        });
    },
    headerSection() {
        return this.headerSection;
    },
    module: 'show-case',
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.text({ bind: 'textField', title: 'Name' }),
            line2: ui.nestedFields.text({ bind: '_id', title: 'ID' }),
            line3: ui.nestedFields.date({ bind: 'dateField', title: 'Added on' }),
            image: ui.nestedFields.image({ bind: 'logo', title: 'Logo', placeholderMode: 'Initials' }),
        },
    },
    node: '@sage/xtrem-show-case/ShowCaseProvider',
    priority: 500,
    onLoad() {
        setApplicativePageCrudActions({
            page: this,
            isDirty: false,
            save: this.$standardSaveAction,
            cancel: this.$standardCancelAction,
            duplicate: this.$standardDuplicateAction,
            remove: this.$standardDeleteAction,
            actions: [],
        });

        const productCount = this.products.value.length;
        this.productListSection.indicatorContent = productCount === 20 ? '19+' : String(productCount);
    },
    onDirtyStateUpdated(isDirty: boolean) {
        setApplicativePageCrudActions({
            page: this,
            isDirty,
            save: this.$standardSaveAction,
            cancel: this.$standardCancelAction,
            duplicate: this.$standardDuplicateAction,
            remove: this.$standardDeleteAction,
            actions: [],
        });
    },
})
export class StandardShowCaseProvider extends ui.Page<GraphApi> {
    @ui.decorators.section<StandardShowCaseProvider>({
        title: 'Header',
    })
    headerSection: ui.containers.Section;

    @ui.decorators.block<StandardShowCaseProvider>({
        parent() {
            return this.headerSection;
        },
    })
    headerBlock: ui.containers.Block;

    @ui.decorators.textField<StandardShowCaseProvider>({
        parent() {
            return this.headerBlock;
        },
        title: 'ID',
        isReadOnly: true,
    })
    _id: ui.fields.Text;

    @ui.decorators.textField<StandardShowCaseProvider>({
        parent() {
            return this.headerBlock;
        },
        title: 'Name',
    })
    textField: ui.fields.Text;

    @ui.decorators.referenceField<StandardShowCaseProvider, ShowCaseProviderAddressBinding>({
        title: 'Address',
        parent() {
            return this.headerBlock;
        },
        columns: [
            ui.nestedFields.text({ bind: 'name', title: 'Name' }),
            ui.nestedFields.text({ bind: 'addressLine1', title: 'Line1' }),
            ui.nestedFields.text({ bind: 'addressLine2', title: 'Line2' }),
            ui.nestedFields.reference({
                bind: 'country',
                title: 'Country',
                node: '@sage/xtrem-show-case/ShowCaseCountry',
                valueField: 'name',
                helperTextField: 'code',
            }),
            ui.nestedFields.text({ bind: 'zip', title: 'zip', isTransientInput: true }),
        ],
        node: '@sage/xtrem-show-case/ShowCaseProviderAddress',
        valueField: 'name',
        tunnelPage: '@sage/xtrem-show-case/ShowCaseProviderAddress',
        createTunnelLinkText: 'Create a new site address',
    })
    siteAddress: ui.fields.Reference;

    @ui.decorators.referenceField<StandardShowCaseProvider, ShowCaseProduct>({
        parent() {
            return this.headerBlock;
        },
        valueField: 'product',
        helperTextField: '_id',
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        title: 'Flagship Product',
        tunnelPage: '@sage/xtrem-show-case/StandardShowCaseProduct',
        columns: [ui.nestedFields.text({ bind: 'product', title: 'Product Name' })],
        createTunnelLinkText: 'Create a new product',
    })
    flagshipProduct: ui.fields.Reference;

    @ui.decorators.section<StandardShowCaseProvider>({
        title: 'Products',
    })
    productListSection: ui.containers.Section;

    @ui.decorators.tableField<StandardShowCaseProvider, ShowCaseProduct>({
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        mainField: 'description',
        parent() {
            return this.productListSection;
        },
        title: 'Products',
        isTitleHidden: true,
        orderBy: { _id: 1 },
        columns: [
            ui.nestedFields.text({ bind: '_id', isReadOnly: true, title: 'Id' }),
            ui.nestedFields.text({
                bind: 'product',
                title: 'Product',
            }),
            ui.nestedFields.text({ bind: 'description', title: 'Description' }),
            ui.nestedFields.checkbox({ bind: 'hotProduct', isReadOnly: true, title: 'Hot' }),
            ui.nestedFields.select({ bind: 'category', isReadOnly: true, title: 'Category' }),
            ui.nestedFields.numeric({
                bind: 'total',
                title: 'Net Price',
                scale: 2,
                min: 0,
                onChange(_id, rowValue) {
                    setTimeout(
                        () => {
                            this.products.addOrUpdateRecordValue({
                                ...rowValue,
                                amount: rowValue.qty * rowValue.total,
                            });
                        },
                        Math.floor(Math.random() * 1000),
                    );
                },
            }),
            ui.nestedFields.numeric({
                bind: 'qty',
                title: 'Quantity',
                scale: 2,
                onChange(_id, rowValue) {
                    setTimeout(
                        () => {
                            this.products.addOrUpdateRecordValue({
                                ...rowValue,
                                amount: rowValue.qty * rowValue.total,
                            });
                        },
                        Math.floor(Math.random() * 1000),
                    );
                },
            }),
            ui.nestedFields.numeric({ bind: 'amount', title: 'Amount', scale: 2 }),
            ui.nestedFields.reference({
                bind: 'designerEmployee',
                node: '@sage/xtrem-show-case/ShowCaseEmployee',
                title: 'Product designed by',
                valueField: 'firstName',
                helperTextField: 'lastName',
                tunnelPage: '@sage/xtrem-show-case/ShowCaseEmployee',
                validation(value) {
                    if (value?.firstName === 'John') {
                        return 'No Johns allowed.';
                    }
                    return undefined;
                },
                onChange() {
                    this.$.showToast('The designer employee field was changed.');
                },
                columns: [
                    ui.nestedFields.text({ bind: 'firstName', title: 'First name' }),
                    ui.nestedFields.text({ bind: 'lastName', title: 'Last name' }),
                ],
            }),
            ui.nestedFields.datetime({
                bind: 'createdAt',
                title: 'Created at',
            }),
        ],
        dropdownActions: [
            {
                icon: 'box_arrow_left',
                title: 'Edit on sidebar',
                async onClick(rowId: any) {
                    this.products.openSidebar(rowId);
                },
            },
        ],
        sidebar: {
            title(_id, recordValue) {
                return recordValue.product;
            },
            layout() {
                return {
                    mainSection: {
                        title: 'Product details',
                        blocks: {
                            mainBlock: {
                                fields: ['product', 'designerEmployee', 'category', 'qty', 'amount', 'description'],
                            },
                        },
                    },
                };
            },
        },
    })
    products: ui.fields.Table<ShowCaseProduct>;

    @ui.decorators.section<StandardShowCaseProvider>({
        title: 'Misc',
    })
    miscSection: ui.containers.Section;

    @ui.decorators.block<StandardShowCaseProvider>({
        parent() {
            return this.miscSection;
        },
    })
    miscBlock: ui.containers.Block;

    @ui.decorators.dateField<StandardShowCaseProvider>({
        parent() {
            return this.miscBlock;
        },
        title: 'Date',
    })
    dateField: ui.fields.Date;

    @ui.decorators.checkboxField<StandardShowCaseProvider>({
        parent() {
            return this.miscBlock;
        },
        title: 'Checkbox',
    })
    booleanField: ui.fields.Checkbox;

    @ui.decorators.numericField<StandardShowCaseProvider>({
        parent() {
            return this.miscBlock;
        },
        title: 'Integer',
        scale: 0,
    })
    integerField: ui.fields.Numeric;

    @ui.decorators.pageAction<StandardShowCaseProvider>({
        title: 'Go to ShowCase Products',
        icon: 'box_arrow_left',
        async onClick() {
            this.$.router.goTo('@sage/xtrem-show-case/ShowCaseProduct');
        },
    })
    navigateToShowCaseProducts: ui.PageAction;
}
