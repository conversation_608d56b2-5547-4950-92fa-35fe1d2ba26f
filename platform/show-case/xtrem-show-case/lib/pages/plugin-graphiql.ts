import { Graph<PERSON><PERSON>, ShowCaseInvoice } from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import type { GraphiqlPluginProperties } from '@sage/xtrem-ui-plugin-graphiql';
import { plugins } from '../menu-items/plugins';

@ui.decorators.page<PluginGraphiql, ShowCaseInvoice>({
    authorizationCode: 'SHCPRVD',
    category: 'SHOWCASE',
    businessActions() {
        return [this.update];
    },
    module: 'show-case',
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.reference({
                bind: 'customer',
                valueField: 'name',
                node: '@sage/xtrem-show-case/ShowCaseCustomer',
            }),
            line2: ui.nestedFields.text({ bind: '_id' }),
        },
    },
    node: '@sage/xtrem-show-case/ShowCaseInvoice',
    title: 'Field - GraphiQL Plugin',
    menuItem: plugins,
    onLoad() {
        this.height.value = 400;
    },
})
export class PluginGraphiql extends ui.Page<GraphApi> {
    @ui.decorators.section<PluginGraphiql>({
        isTitleHidden: true,
    })
    section: ui.containers.Section;

    @ui.decorators.block<PluginGraphiql>({
        parent() {
            return this.section;
        },
    })
    block: ui.containers.Block;

    @ui.decorators.textField<PluginGraphiql>({
        parent() {
            return this.block;
        },
        title: 'Id',
        isReadOnly: true,
    })
    _id: ui.fields.Text;

    @ui.decorators.checkboxField<PluginGraphiql>({
        parent() {
            return this.block;
        },
        isTransient: true,
        title: 'Is readOnly',
        onChange() {
            this.queryText.isReadOnly = this.isReadOnly.value;
        },
    })
    isReadOnly: ui.fields.Checkbox;

    @ui.decorators.checkboxField<PluginGraphiql>({
        parent() {
            return this.block;
        },
        isTransient: true,
        title: 'Is Toolbar hidden',
        onChange() {
            this.queryText.setProperty('isToolbarHidden', this.isToolbarHidden.value);
        },
    })
    isToolbarHidden: ui.fields.Checkbox;

    @ui.decorators.numericField<PluginGraphiql>({
        parent() {
            return this.block;
        },
        isTransient: true,
        title: 'Height',
        helperText: 'Editor height in pixels',
        min: 50,
        max: 1000,
        onChange() {
            this.queryText.setProperty('height', this.height.value);
        },
    })
    height: ui.fields.Numeric;

    @ui.decorators.pluginField<PluginGraphiql, GraphiqlPluginProperties>({
        parent() {
            return this.block;
        },
        title: 'Data Query',
        helperText: 'Write a query against the database',
        isFullWidth: true,
        pluginPackage: '@sage/xtrem-ui-plugin-graphiql',
        height: 400,
    })
    queryText: ui.fields.Plugin<GraphiqlPluginProperties>;

    @ui.decorators.pageAction<PluginGraphiql>({
        title: 'Save',
        async onClick() {
            await this.$.graph.update();
            this.$.showToast(`Updated entry: ${this._id.value}`, {
                type: 'success',
            });
        },
    })
    update: ui.PageAction;
}
