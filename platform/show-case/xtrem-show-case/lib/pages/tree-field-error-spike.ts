import { ShowCaseComponent } from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import { fields } from '../menu-items/fields';

@ui.decorators.page<TreeFieldErrorSpike>({
    authorizationCode: 'BSCFLDS',
    module: 'show-case',
    title: 'Tree Field Error',
    category: 'SHOWCASE',
    menuItem: fields,
    node: '@sage/xtrem-show-case/ShowCaseBillOfMaterial',
})
export class TreeFieldErrorSpike extends ui.Page {
    @ui.decorators.section<TreeFieldErrorSpike>({
        title: 'Multilevel view',
    })
    multilevelSection: ui.containers.Section;

    @ui.decorators.treeField<TreeFieldErrorSpike, ShowCaseComponent>({
        title: 'Components',
        bind: 'components',
        isTitleHidden: true,
        node: '@sage/xtrem-show-case/ShowCaseComponent',
        masterColumn: ui.nestedFields.text({
            title: 'Item name',
            bind: { item: { name: true } },
        }),
        sublevelProperty: { bom: { components: true } },
        orderBy: {
            componentNumber: 1,
        },
        parent() {
            return this.multilevelSection;
        },
        columns: [
            ui.nestedFields.numeric({
                title: 'Component number',
                bind: 'componentNumber',
            }),
            ui.nestedFields.reference({
                title: 'Item name',
                bind: 'item',
                isHiddenOnMainField: true,
                node: '@sage/xtrem-show-case/ShowCaseItem',
                valueField: 'name',
                columns: [ui.nestedFields.text({ title: 'Name', bind: 'name', width: 'large' })],
            }),
        ],
    })
    treeComponents: ui.fields.Tree<ShowCaseComponent>;
}
