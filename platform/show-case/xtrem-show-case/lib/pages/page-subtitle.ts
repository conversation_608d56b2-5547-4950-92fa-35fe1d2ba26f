import * as ui from '@sage/xtrem-ui';
import { containers } from '../menu-items/containers';

@ui.decorators.page<PageSubtitle>({
    category: 'SHOWCASE',
    isTransient: true,
    mode: 'default',
    module: 'show-case',
    subtitle: 'The quick brown fox jumps over the lazy dog',
    title: 'Page - Subtitle',
    menuItem: containers,
    onLoad() {
        this.field.value = 'The quick brown fox jumps over the lazy dog';
    },
})
export class PageSubtitle extends ui.Page {
    @ui.decorators.section<PageSubtitle>({
        isExtendable: false,
        isOpen: true,
        title: 'Section',
    })
    section: ui.containers.Section;

    @ui.decorators.block<PageSubtitle>({
        parent() {
            return this.section;
        },
        title: 'Block',
    })
    block: ui.containers.Block;

    @ui.decorators.textField<PageSubtitle>({
        parent() {
            return this.block;
        },
        onChange() {
            this.$.page.subtitle = this.field.value;
        },
        title: 'Field',
        helperText: 'Change this value',
    })
    field: ui.fields.Text;
}
