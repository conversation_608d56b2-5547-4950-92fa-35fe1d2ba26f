import { GraphApi, ShowCaseInvoiceLine, ShowCaseProduct } from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import { validation } from '../menu-items/validation';

@ui.decorators.page<PodCollectionWithValidation>({
    authorizationCode: 'BSCFLDS',
    title: 'Pod Collection - Validation',
    defaultEntry: () => '2',
    module: 'show-case',
    category: 'SHOWCASE',
    createAction() {
        return this.$standardNewAction;
    },
    businessActions() {
        return [this.$standardCancelAction, this.$standardSaveAction];
    },
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.text({ bind: '_id', prefix: 'INV' }),
            // titleLine2: ui.nestedFields.numeric({ bind: 'h', prefix: 'INV' }),
            line2: ui.nestedFields.date({ bind: 'purchaseDate' }),
            titleRight: ui.nestedFields.count({ bind: 'lines', postfix: 'it' }),
        },
    },
    node: '@sage/xtrem-show-case/ShowCaseInvoice',
    menuItem: validation,
    onLoad() {
        this.lines2.value = [
            {
                _id: '1',
                comments: 'Please add this comment',
            },
            {
                _id: '2',
                comments: 'add a comment',
            },
        ];
    },
})
export class PodCollectionWithValidation extends ui.Page<GraphApi> {
    @ui.decorators.section<PodCollectionWithValidation>({})
    section: ui.containers.Section;

    @ui.decorators.block<PodCollectionWithValidation>({
        parent() {
            return this.section;
        },
        title: 'Field example',
    })
    fieldBlock: ui.containers.Block;

    @ui.decorators.podCollectionField<PodCollectionWithValidation, ShowCaseInvoiceLine>({
        node: '@sage/xtrem-show-case/ShowCaseInvoiceLine',
        orderBy: {
            product: { _id: -1 },
        },
        parent() {
            return this.fieldBlock;
        },
        fetchesDefaults: true,
        recordTitle(value: any, rowData: ShowCaseInvoiceLine | null) {
            return rowData?.product?.product && rowData?.product?._id
                ? `${rowData.product.product} ${rowData?.product?._id}`
                : 'Unknown';
        },
        dropdownActions: [
            {
                icon: 'add',
                title: 'Add',
                isDisabled() {
                    return false;
                },
                onClick(rowId: any, data: any) {
                    this.$.dialog.message(
                        'info',
                        'Add Row Action was clicked',
                        `Product: ${data.product} Row: ${rowId}`,
                    );
                },
            },
            {
                icon: 'locked',
                title: 'Maybe disabled',
                isDisabled(id: any, row: any) {
                    return row.orderQuantity < 10;
                },
                onClick(rowId: any, data: any) {
                    if (data.product) {
                        this.$.showToast(data.product.product, { type: 'info' });
                    }
                },
            },
            {
                icon: 'minus',
                title: 'Remove',
                onClick(rowId: any) {
                    this.lines.removeRecord(rowId);
                },
            },
            {
                title: 'Action no icon',
                isHidden(id: any, row: any) {
                    return row.orderQuantity < 10;
                },
                onClick(rowId: any, data: any) {
                    this.$.showToast(data.product, { type: 'info' });
                },
            },
        ],
        headerLabel: ui.nestedFields.label({ bind: 'netPrice' }),
        columns: [
            ui.nestedFields.reference<PodCollectionWithValidation, ShowCaseInvoiceLine, ShowCaseProduct>({
                bind: 'product',
                title: 'Product',
                isAutoSelectEnabled: true,
                isMandatory: true,
                minLookupCharacters: 0,
                node: '@sage/xtrem-show-case/ShowCaseProduct',
                valueField: 'product',
                fetchesDefaults: true,
                isFullWidth: true,
            }),
            ui.nestedFields.numeric({
                bind: 'orderQuantity',
                title: 'Ordered Quantity',
                scale: 0,
                min: 1,
                fetchesDefaults: true,
                isFullWidth: true,
            }),
            ui.nestedFields.select({
                bind: 'discountType',
                title: 'Discount Type',
                optionType: '@sage/xtrem-show-case/ShowCaseDiscountType',
                isFullWidth: true,
            }),
            ui.nestedFields.textArea({
                bind: 'comments',
                title: 'Comments',
                isFullWidth: true,
                rows: 5,
                validation(value: string) {
                    if (!value.includes('Please')) {
                        return 'The comment must contain the word "Please".';
                    }
                    return undefined;
                },
            }),
        ],
    })
    lines: ui.fields.PodCollection;

    @ui.decorators.podCollectionField<PodCollectionWithValidation, ShowCaseInvoiceLine>({
        isTransient: true,
        parent() {
            return this.fieldBlock;
        },
        title: 'Validate transient pod collection',

        columns: [
            ui.nestedFields.text({
                bind: 'comments',
                title: 'Comments',
                isTransient: true,
                validation(value: string) {
                    if (!value.includes('Please')) {
                        return 'The comment must contain the word "Please".';
                    }
                    return undefined;
                },
            }),
        ],
    })
    lines2: ui.fields.PodCollection;

    @ui.decorators.block<PodCollectionWithValidation>({
        parent() {
            return this.section;
        },
        title: 'Configuration',
    })
    configurationBlock: ui.containers.Block;

    @ui.decorators.checkboxField<PodCollectionWithValidation>({
        isTransient: true,
        parent() {
            return this.configurationBlock;
        },
        title: 'Can remove item',
        onChange() {
            this.lines.canRemoveRecord = this.canRemoveRecord.value;
        },
    })
    canRemoveRecord: ui.fields.Checkbox;

    @ui.decorators.checkboxField<PodCollectionWithValidation>({
        isTransient: true,
        parent() {
            return this.configurationBlock;
        },
        title: 'Can add item',
        onChange() {
            this.lines.canAddRecord = this.canAddRecord.value;
        },
    })
    canAddRecord: ui.fields.Checkbox;

    @ui.decorators.buttonField<PodCollectionWithValidation>({
        isTransient: true,
        parent() {
            return this.configurationBlock;
        },
        title: 'Force validation',
        map() {
            return 'Force validation';
        },
        async onClick() {
            await this.lines.validate(true);
            await this.lines2.validate(true);
        },
    })
    forceValidation: ui.fields.Button;

    @ui.decorators.checkboxField<PodCollectionWithValidation>({
        isTransient: true,
        parent() {
            return this.configurationBlock;
        },
        title: 'Can select',
        onChange() {
            this.lines.canSelect = this.canSelect.value;
        },
    })
    canSelect: ui.fields.Checkbox;

    @ui.decorators.checkboxField<PodCollectionWithValidation>({
        isTransient: true,
        parent() {
            return this.configurationBlock;
        },
        title: 'Is Disabled',
        onChange() {
            this.lines.isDisabled = this.isDisabled.value;
        },
    })
    isDisabled: ui.fields.Checkbox;

    @ui.decorators.checkboxField<PodCollectionWithValidation>({
        isTransient: true,
        parent() {
            return this.configurationBlock;
        },
        title: 'Is Read-only',
        onChange() {
            this.lines.isReadOnly = this.isReadOnly.value;
        },
    })
    isReadOnly: ui.fields.Checkbox;

    @ui.decorators.separatorField<PodCollectionWithValidation>({
        parent() {
            return this.configurationBlock;
        },
        isFullWidth: true,
    })
    separator1: ui.fields.Separator;

    @ui.decorators.textField<PodCollectionWithValidation>({
        isTransient: true,
        parent() {
            return this.configurationBlock;
        },
        title: 'Title',
        onChange() {
            this.lines.title = this.title.value;
        },
    })
    title: ui.fields.Text;

    @ui.decorators.textField<PodCollectionWithValidation>({
        isTransient: true,
        parent() {
            return this.configurationBlock;
        },
        title: 'Helper Text',
        onChange() {
            this.lines.helperText = this.helperText.value;
        },
    })
    helperText: ui.fields.Text;

    @ui.decorators.textField<PodCollectionWithValidation>({
        isTransient: true,
        parent() {
            return this.configurationBlock;
        },
        title: 'Add button placeholder text',
        onChange() {
            this.lines.addButtonText = this.newItemText.value;
        },
    })
    newItemText: ui.fields.Text;

    @ui.decorators.textField<PodCollectionWithValidation>({
        isTransient: true,
        parent() {
            return this.configurationBlock;
        },
        title: 'Remove dialog title',
        onChange() {
            this.lines.removeDialogTitle = this.removeDialogTitle.value;
        },
    })
    removeDialogTitle: ui.fields.Text;

    @ui.decorators.textField<PodCollectionWithValidation>({
        isTransient: true,
        parent() {
            return this.configurationBlock;
        },
        title: 'Remove dialog text',
        onChange() {
            this.lines.removeDialogText = this.removeDialogText.value;
        },
    })
    removeDialogText: ui.fields.Text;
}
