import { validEmail } from '@sage/xtrem-system/build/lib/shared-functions/email-validation';
import { isValidPassword } from '@sage/xtrem-system/build/lib/shared-functions/password';
import * as ui from '@sage/xtrem-ui';
import { misc } from '../menu-items/misc';

function hasValidEmailAndPassword(email: string, password: string) {
    return validEmail(email) && isValidPassword(password);
}
@ui.decorators.page<FunctionsPage>({
    title: 'Imported functions',
    authorizationCode: 'IMPFNS',
    module: 'show-case',
    isTransient: true,
    category: 'SHOWCASE',
    onLoad() {
        this.button.value = 'Register';
    },
    menuItem: misc,
})
export class FunctionsPage extends ui.Page {
    @ui.decorators.section<FunctionsPage>({
        isTitleHidden: true,
    })
    section: ui.containers.Section;

    @ui.decorators.block<FunctionsPage>({
        parent() {
            return this.section;
        },
        title: 'Register Demo',
    })
    fieldBlock: ui.containers.Block;

    @ui.decorators.textField<FunctionsPage>({
        parent() {
            return this.fieldBlock;
        },
        title: 'E-mail',
        helperText: 'A valid e-mail address',
        validation(email) {
            return validEmail(email) ? undefined : 'Invalid Email';
        },
    })
    email: ui.fields.Text;

    @ui.decorators.textField<FunctionsPage>({
        parent() {
            return this.fieldBlock;
        },
        title: 'Password',
        helperText:
            'Minimum 8 characters, at least one upper case English letter, one lower case English letter, one number and one special character.',
        validation(password) {
            return isValidPassword(password) ? undefined : 'Invalid Password';
        },
    })
    password: ui.fields.Text;

    @ui.decorators.buttonField<FunctionsPage>({
        parent() {
            return this.fieldBlock;
        },
        onClick() {
            if (!hasValidEmailAndPassword(this.email.value, this.password.value)) {
                this.$.showToast('Invalid e-mail or password', { type: 'warning' });
            } else {
                this.$.showToast('Congratulations, you are now registered!', { type: 'success' });
            }
        },
    })
    button: ui.fields.Button;
}
