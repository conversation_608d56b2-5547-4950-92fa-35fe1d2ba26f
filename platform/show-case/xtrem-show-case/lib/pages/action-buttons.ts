import * as ui from '@sage/xtrem-ui';
import { fields } from '../menu-items/fields';

@ui.decorators.page<ActionButtons>({
    authorizationCode: 'ACTINBTNS',
    module: 'show-case',
    category: 'SHOWCASE',
    title: 'Page - Action Buttons',
    isTransient: true,
    createAction() {
        return this.createCrud;
    },
    headerDropDownActions() {
        return [
            this.deleteCrud,
            this.dropDownAction1,
            this.dropDownAction2,
            this.$standardOpenCustomizationPageWizardAction,
        ];
    },
    headerQuickActions() {
        return [this.quickAction1, this.quickAction2];
    },
    businessActions() {
        return [this.businessAction1, this.businessAction2, this.businessAction3, this.businessAction4, this.saveCrud];
    },
    onLoad() {
        ui.console.log(ui.localize('@sage/xtrem-show-case/demo', 'Hi Joe'));
        this.descriptionField.value = ui.localize(
            '@sage/xtrem-show-case/action-button-about',
            'This page show cases the CRUD and business actions\nThe actions are located outside the page body, on the title line and on the right-side panel.',
        );
    },
    menuItem: fields,
})
export class ActionButtons extends ui.Page {
    @ui.decorators.pageAction<ActionButtons>({
        title: 'Save',
        onClick() {
            this.resultField.value = ui.localize('@sage/xtrem-show-case/crud-button-save', 'Save CRUD button');
        },
    })
    saveCrud: ui.PageAction;

    @ui.decorators.pageAction<ActionButtons>({
        onClick() {
            this.resultField.value = ui.localize('@sage/xtrem-show-case/crud-button-create', 'Create CRUD button');
        },
    })
    createCrud: ui.PageAction;

    @ui.decorators.pageAction<ActionButtons>({
        title: 'Delete',
        icon: 'bin',
        isDestructive: true,
        onClick() {
            this.resultField.value = ui.localize('@sage/xtrem-show-case/crud-button-delete', 'Delete CRUD button');
        },
    })
    deleteCrud: ui.PageAction;

    @ui.decorators.pageAction<ActionButtons>({
        onClick() {
            this.resultField.value = ui.localize('@sage/xtrem-show-case/crud-button-close', 'Close CRUD button');
        },
    })
    closeCrud: ui.PageAction;

    @ui.decorators.pageAction<ActionButtons>({
        title: 'Business action 1',
        onClick() {
            this.resultField.value = ui.localize('@sage/xtrem-show-case/business-action', 'Business action {{0}}', [1]);
        },
    })
    businessAction1: ui.PageAction;

    @ui.decorators.pageAction<ActionButtons>({
        title: 'Business action 2',
        onClick() {
            this.resultField.value = ui.localize('@sage/xtrem-show-case/business-action', 'Business action {{0}}', [2]);
        },
    })
    businessAction2: ui.PageAction;

    @ui.decorators.pageAction<ActionButtons>({
        title: 'Business action 3',
        helperText: 'This action is loading for 5 seconds',
        onClick() {
            this.businessAction3.isLoading = true;
            this.resultField.value = ui.localize('@sage/xtrem-show-case/business-action', 'Business action {{0}}', [3]);

            setTimeout(() => {
                this.businessAction3.isLoading = false;
            }, 5000);
        },
    })
    businessAction3: ui.PageAction;

    @ui.decorators.pageAction<ActionButtons>({
        title: 'Business action 4',
        onClick() {
            this.resultField.value = ui.localize('@sage/xtrem-show-case/business-action', 'Business action {{0}}', [4]);
        },
    })
    businessAction4: ui.PageAction;

    @ui.decorators.pageAction<ActionButtons>({
        title: 'Drop-down action 1',
        icon: 'alert',
        onClick() {
            this.resultField.value = ui.localize('@sage/xtrem-show-case/dropdown-action', 'Drop-down action {{0}}', [
                1,
            ]);
        },
    })
    dropDownAction1: ui.PageAction;

    @ui.decorators.pageAction<ActionButtons>({
        title: 'Drop-down action 2',
        icon: 'calendar_today',
        onClick() {
            this.resultField.value = ui.localize('@sage/xtrem-show-case/dropdown-action', 'Drop-down action {{0}}', [
                2,
            ]);
        },
    })
    dropDownAction2: ui.PageAction;

    @ui.decorators.pageAction<ActionButtons>({
        title: 'Quick action 1',
        icon: 'stacked_boxes',
        onClick() {
            this.resultField.value = ui.localize('@sage/xtrem-show-case/quick-action', 'Quick action {{0}}', [1]);
        },
    })
    quickAction1: ui.PageAction;

    @ui.decorators.pageAction<ActionButtons>({
        title: 'Quick action 2',
        icon: 'bank',
        onClick() {
            this.resultField.value = ui.localize('@sage/xtrem-show-case/quick-action', 'Quick action {{0}}', [2]);
        },
    })
    quickAction2: ui.PageAction;

    @ui.decorators.section<ActionButtons>({
        title: 'About this page',
    })
    mainSection: ui.containers.Section;

    @ui.decorators.block<ActionButtons>({
        parent() {
            return this.mainSection;
        },
        title: '',
    })
    mainBlock: ui.containers.Block;

    @ui.decorators.textAreaField<ActionButtons>({
        parent() {
            return this.mainBlock;
        },
        isReadOnly: true,
        isFullWidth: true,
        rows: 4,
    })
    descriptionField: ui.fields.TextArea;

    @ui.decorators.textField<ActionButtons>({
        title: 'Selected action',
        parent() {
            return this.mainBlock;
        },
        isReadOnly: true,
        isFullWidth: true,
    })
    resultField: ui.fields.Text;

    @ui.decorators.section<ActionButtons>({
        title: 'Controlling Buttons',
        isTitleHidden: true,
    })
    controlSection: ui.containers.Section;

    @ui.decorators.block<ActionButtons>({
        parent() {
            return this.controlSection;
        },
        title: '',
    })
    controlBlock: ui.containers.Block;

    @ui.decorators.buttonField<ActionButtons>({
        title: 'Toggle Business Action 1 disabled',
        map() {
            return ui.localize('@sage/xtrem-show-case/click-me', 'Click me');
        },
        parent() {
            return this.controlBlock;
        },
        onClick() {
            this.businessAction1.isDisabled = !this.businessAction1.isDisabled;
        },
    })
    disableBusinessAction1: ui.fields.Button;

    @ui.decorators.buttonField<ActionButtons>({
        title: 'Toggle Business Action 2 hidden',
        map() {
            return ui.localize('@sage/xtrem-show-case/click-me', 'Click me');
        },
        parent() {
            return this.controlBlock;
        },
        onClick() {
            this.businessAction2.isHidden = !this.businessAction2.isHidden;
        },
    })
    hideBusinessAction2: ui.fields.Button;

    @ui.decorators.buttonField<ActionButtons>({
        title: 'Toggle Save CRUD action disabled',
        map() {
            return ui.localize('@sage/xtrem-show-case/click-me', 'Click me');
        },
        parent() {
            return this.controlBlock;
        },
        onClick() {
            this.saveCrud.isDisabled = !this.saveCrud.isDisabled;
        },
    })
    disableSaveCrudAction: ui.fields.Button;

    @ui.decorators.buttonField<ActionButtons>({
        title: 'Toggle Business Action 3 disabled',
        map() {
            return ui.localize('@sage/xtrem-show-case/click-me', 'Click me');
        },
        parent() {
            return this.controlBlock;
        },
        onClick() {
            this.businessAction3.isDisabled = !this.businessAction3.isDisabled;
        },
    })
    disableBusinessAction3: ui.fields.Button;

    @ui.decorators.buttonField<ActionButtons>({
        title: 'Toggle Business Action 4 disabled',
        map() {
            return ui.localize('@sage/xtrem-show-case/click-me', 'Click me');
        },
        parent() {
            return this.controlBlock;
        },
        onClick() {
            this.businessAction4.isDisabled = !this.businessAction4.isDisabled;
        },
    })
    disableBusinessAction4: ui.fields.Button;

    @ui.decorators.buttonField<ActionButtons>({
        title: 'Drop-down Action 1 disabled',
        map() {
            return ui.localize('@sage/xtrem-show-case/click-me', 'Click me');
        },
        parent() {
            return this.controlBlock;
        },
        onClick() {
            this.dropDownAction1.isDisabled = !this.dropDownAction1.isDisabled;
        },
    })
    disableDropDownAction1: ui.fields.Button;

    @ui.decorators.buttonField<ActionButtons>({
        title: 'Drop-down Action 2 disabled',
        map() {
            return ui.localize('@sage/xtrem-show-case/click-me', 'Click me');
        },
        parent() {
            return this.controlBlock;
        },
        onClick() {
            this.dropDownAction2.isDisabled = !this.dropDownAction2.isDisabled;
        },
    })
    disableDropDownAction2: ui.fields.Button;

    @ui.decorators.buttonField<ActionButtons>({
        title: 'Quick Action 1 disabled',
        map() {
            return ui.localize('@sage/xtrem-show-case/click-me', 'Click me');
        },
        parent() {
            return this.controlBlock;
        },
        onClick() {
            this.quickAction1.isDisabled = !this.quickAction1.isDisabled;
        },
    })
    disableQuickAction1: ui.fields.Button;

    @ui.decorators.buttonField<ActionButtons>({
        title: 'Quick Action 2 disabled',
        map() {
            return ui.localize('@sage/xtrem-show-case/click-me', 'Click me');
        },
        parent() {
            return this.controlBlock;
        },
        onClick() {
            this.quickAction2.isDisabled = !this.quickAction2.isDisabled;
        },
    })
    disableQuickAction2: ui.fields.Button;

    @ui.decorators.buttonField<ActionButtons>({
        title: 'Drop-down Action 1 hidden',
        map() {
            return ui.localize('@sage/xtrem-show-case/click-me', 'Click me');
        },
        parent() {
            return this.controlBlock;
        },
        onClick() {
            this.dropDownAction1.isHidden = !this.dropDownAction1.isHidden;
        },
    })
    hideDropDownAction1: ui.fields.Button;

    @ui.decorators.buttonField<ActionButtons>({
        title: 'Drop-down Action 2 hidden',
        map() {
            return ui.localize('@sage/xtrem-show-case/click-me', 'Click me');
        },
        parent() {
            return this.controlBlock;
        },
        onClick() {
            this.dropDownAction2.isHidden = !this.dropDownAction2.isHidden;
        },
    })
    hideDropDownAction2: ui.fields.Button;

    @ui.decorators.buttonField<ActionButtons>({
        title: 'Quick Action 1 hidden',
        map() {
            return ui.localize('@sage/xtrem-show-case/click-me', 'Click me');
        },
        parent() {
            return this.controlBlock;
        },
        onClick() {
            this.quickAction1.isHidden = !this.quickAction1.isHidden;
        },
    })
    hideQuickAction1: ui.fields.Button;

    @ui.decorators.buttonField<ActionButtons>({
        title: 'Quick Action 2 hidden',
        map() {
            return ui.localize('@sage/xtrem-show-case/click-me', 'Click me');
        },
        parent() {
            return this.controlBlock;
        },
        onClick() {
            this.quickAction2.isHidden = !this.quickAction2.isHidden;
        },
    })
    hideQuickAction2: ui.fields.Button;
}
