import * as ui from '@sage/xtrem-ui';
import { containers } from '../menu-items/containers';

@ui.decorators.page<PageHeaderCard>({
    areNavigationTabsHidden: true,
    authorizationCode: 'HDRCRD',
    title: 'Two lines',
    module: 'show-case',
    category: 'SHOWCASE',
    isTransient: true,
    menuItem: containers,
    headerCard() {
        return {
            title: this.headerText1,
            titleRight: this.headerLabel2,
            line2: this.headerText3,
            line2Right: this.headerText4,
        };
    },
    onLoad() {
        this.headerText1.value = 'Wasabi';
        this.headerLabel2.value = 'tastes';
        this.headerText3.value = 'VERY';
        this.headerText4.value = 'spicy';
    },
})
export class PageHeaderCard extends ui.Page {
    @ui.decorators.textField<PageHeaderCard>({
        isReadOnly: true,
    })
    headerText1: ui.fields.Text;

    @ui.decorators.labelField<PageHeaderCard>({})
    headerLabel2: ui.fields.Label;

    @ui.decorators.textField<PageHeaderCard>({})
    headerText3: ui.fields.Text;

    @ui.decorators.textField<PageHeaderCard>({
        isReadOnly: true,
    })
    headerText4: ui.fields.Text;

    @ui.decorators.section<PageHeaderCard>({
        title: 'Page body',
    })
    section: ui.containers.Section;

    @ui.decorators.block<PageHeaderCard>({
        parent() {
            return this.section;
        },
    })
    fieldBlock: ui.containers.Block;

    @ui.decorators.labelField<PageHeaderCard>({
        parent() {
            return this.fieldBlock;
        },
        map() {
            return 'Nothing to see on this page except on XS breakpoint';
        },
    })
    infoLabel: ui.fields.Label;
}
