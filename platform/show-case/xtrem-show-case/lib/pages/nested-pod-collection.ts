import { Graph<PERSON><PERSON>, ShowCaseItem, ShowCase<PERSON>rovider, ShowCaseProviderAddress } from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import { podCollection } from '../menu-items/_index';

@ui.decorators.page<NestedPodCollection, ShowCaseItem>({
    authorizationCode: 'BSCFLDS',
    menuItem: podCollection,
    title: 'Pod Collection With nested Reference Arrays',
    defaultEntry: () => '2',
    module: 'show-case',
    category: 'SHOWCASE',
    createAction() {
        return this.$standardNewAction;
    },
    businessActions() {
        return [this.$standardCancelAction, this.$standardSaveAction];
    },
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.text({ bind: '_id', prefix: 'INV' }),
            line2: ui.nestedFields.numeric({ bind: 'quantity' }),
            titleRight: ui.nestedFields.count({ bind: 'providers', postfix: 'it' }),
        },
    },
    node: '@sage/xtrem-show-case/ShowCaseItem',
})
export class NestedPodCollection extends ui.Page<GraphApi> {
    @ui.decorators.section<NestedPodCollection>({})
    section: ui.containers.Section;

    @ui.decorators.block<NestedPodCollection>({
        parent() {
            return this.section;
        },
        title: 'Field example',
    })
    fieldBlock: ui.containers.Block;

    @ui.decorators.podCollectionField<NestedPodCollection, ShowCaseProvider>({
        node: '@sage/xtrem-show-case/ShowCaseProvider',
        parent() {
            return this.fieldBlock;
        },
        fetchesDefaults: true,
        recordTitle(value: any, rowData: ShowCaseProvider | null) {
            return rowData.textField;
        },
        isDisabled: false,
        dropdownActions: [
            {
                icon: 'add',
                title: 'Add',
                isDisabled() {
                    return false;
                },
                onClick(rowId: any, data: any) {
                    this.$.dialog.message(
                        'info',
                        'Add Row Action was clicked',
                        `Product: ${data.product} Row: ${rowId}`,
                    );
                },
            },
            {
                icon: 'minus',
                title: 'Remove',
                onClick(rowId: any) {
                    this.providers.removeRecord(rowId);
                },
            },
        ],
        columns: [
            ui.nestedFields.multiReference<NestedPodCollection, ShowCaseProvider, ShowCaseProviderAddress>({
                bind: 'addresses',
                node: '@sage/xtrem-show-case/ShowCaseProviderAddress',
                minLookupCharacters: 0,
                valueField: 'name',
                title: 'Addresses',
                isFullWidth: true,
                isDisabled: false,
            }),
        ],
    })
    providers: ui.fields.PodCollection;
}
