import * as ui from '@sage/xtrem-ui';
import { fields } from '../menu-items/fields';

@ui.decorators.page<Switch>({
    authorizationCode: 'BSCFLDS',
    module: 'show-case',
    menuItem: fields,
    title: 'Switch',
    category: 'SHOWCASE',
    isTransient: true,
    onLoad() {
        this.field.value = true;
        this.value.value = true;
    },
})
export class Switch extends ui.Page {
    @ui.decorators.section<Switch>({
        title: 'Switch field',
    })
    section: ui.containers.Section;

    @ui.decorators.block<Switch>({
        parent() {
            return this.section;
        },
        title: 'Field example',
    })
    fieldBlock: ui.containers.Block;

    @ui.decorators.switchField<Switch>({
        parent() {
            return this.fieldBlock;
        },
        title: 'Title example',
        helperText: 'Helper text example',
        onClick() {
            this.clickTriggered.isHidden = false;
            setTimeout(() => {
                this.clickTriggered.isHidden = true;
            }, 2500);
        },
        onChange() {
            this.value.value = !this.field.value;
            this.changeTriggered.isHidden = false;
            setTimeout(() => {
                this.changeTriggered.isHidden = true;
            }, 2500);
        },
    })
    field: ui.fields.Switch;

    @ui.decorators.labelField<Switch>({
        parent() {
            return this.fieldBlock;
        },
        isHidden: true,
        map() {
            return 'Change was triggered';
        },
    })
    changeTriggered: ui.fields.Label;

    @ui.decorators.labelField<Switch>({
        parent() {
            return this.fieldBlock;
        },
        isHidden: true,
        map() {
            return 'Click was triggered';
        },
    })
    clickTriggered: ui.fields.Label;

    @ui.decorators.block<Switch>({
        parent() {
            return this.section;
        },
        title: 'Configuration',
    })
    configurationBlock: ui.containers.Block;

    @ui.decorators.switchField<Switch>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is disabled',
        onChange() {
            this.field.isDisabled = this.isDisabled.value;
        },
    })
    isDisabled: ui.fields.Switch;

    @ui.decorators.switchField<Switch>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is large',
        onChange() {
            this.field.size = this.isLarge.value ? 'large' : 'small';
        },
    })
    isLarge: ui.fields.Switch;

    @ui.decorators.switchField<Switch>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is helper text hidden',
        onChange() {
            this.field.isHelperTextHidden = this.isHelperTextHidden.value;
        },
    })
    isHelperTextHidden: ui.fields.Switch;

    @ui.decorators.switchField<Switch>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is hidden',
        onChange() {
            this.field.isHidden = this.isHidden.value;
        },
    })
    isHidden: ui.fields.Switch;

    @ui.decorators.switchField<Switch>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is readOnly',
        onChange() {
            this.field.isReadOnly = this.isReadOnly.value;
        },
    })
    isReadOnly: ui.fields.Switch;

    @ui.decorators.switchField<Switch>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is title hidden',
        onChange() {
            this.field.isTitleHidden = this.isTitleHidden.value;
        },
    })
    isTitleHidden: ui.fields.Switch;

    @ui.decorators.textField<Switch>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Title',
        onChange() {
            this.field.title = this.title.value;
        },
    })
    title: ui.fields.Text;

    @ui.decorators.textField<Switch>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Helper text',
        onChange() {
            this.field.helperText = this.helperText.value;
        },
    })
    helperText: ui.fields.Text;

    @ui.decorators.textField<Switch>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Title Help',
        onChange() {
            this.field.titleHelp = this.titleHelp.value;
        },
    })
    titleHelp: ui.fields.Text;

    @ui.decorators.switchField<Switch>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Value',
        onChange() {
            this.field.value = this.value.value;
        },
    })
    value: ui.fields.Switch;

    @ui.decorators.buttonField<Switch>({
        parent() {
            return this.configurationBlock;
        },
        map() {
            return 'Focus field';
        },
        onClick() {
            this.field.focus();
        },
    })
    focus: ui.fields.Button;

    /* Additional examples */

    @ui.decorators.block<Switch>({
        parent() {
            return this.section;
        },
        title: 'Additional examples',
    })
    additionalBlock: ui.containers.Block;

    @ui.decorators.separatorField<Switch>({
        parent() {
            return this.additionalBlock;
        },
        isFullWidth: true,
    })
    fieldSeparator1: ui.fields.Separator;

    @ui.decorators.switchField<Switch>({
        parent() {
            return this.additionalBlock;
        },
        title: 'With warning message',
        warningMessage: 'Wow, warning!',
    })
    warningMessageField: ui.fields.Switch;

    @ui.decorators.switchField<Switch>({
        parent() {
            return this.additionalBlock;
        },
        title: 'With warning message with callback',
        helperText: 'Warning if checked',
        warningMessage() {
            if (this.warningMessageWithCallbackField.value) {
                return 'Warning message';
            }
            return null;
        },
    })
    warningMessageWithCallbackField: ui.fields.Switch;

    @ui.decorators.switchField<Switch>({
        parent() {
            return this.additionalBlock;
        },
        title: 'With info message',
        infoMessage: 'Wow, warning!',
    })
    infoMessageField: ui.fields.Switch;

    @ui.decorators.switchField<Switch>({
        parent() {
            return this.additionalBlock;
        },
        title: 'With info message with callback',
        helperText: 'Info message if checked',
        infoMessage() {
            if (this.infoMessageWithCallbackField.value) {
                return 'Info message';
            }
            return null;
        },
    })
    infoMessageWithCallbackField: ui.fields.Switch;

    @ui.decorators.switchField<Switch>({
        parent() {
            return this.additionalBlock;
        },
        title: 'With info message',
        warningMessage: 'Wow, warning!',
        infoMessage: 'You should not see this',
    })
    infoAndWarningMessageField: ui.fields.Switch;

    @ui.decorators.switchField<Switch>({
        parent() {
            return this.additionalBlock;
        },
        validation() {
            if (this.infoAndWarningMessageMandatoryField.value) {
                return 'Error message';
            }
            return '';
        },
        title: 'Info, warning and validation',
        warningMessage: 'Wow, warning!',
        infoMessage: 'You should not see this',
        helperText: 'Error if checked',
    })
    infoAndWarningMessageMandatoryField: ui.fields.Switch;
}
