import * as ui from '@sage/xtrem-ui';
import { ShowCaseProvider } from '@sage/xtrem-show-case-api';
import { containers } from '../menu-items/containers';

@ui.decorators.page<DetailPanel, ShowCaseProvider>({
    authorizationCode: 'HLPRPNL',
    category: 'SHOWCASE',
    businessActions() {
        return [this.action1, this.action2, this.action3, this.action4];
    },
    detailPanel() {
        return {
            footerActions: [this.action1, this.action2, this.action3, this.action4],
            header: this.detailPanelHeaderSection,
            sections: [
                this.detailPanelBodySection1,
                this.detailPanelBodySection2,
                this.detailPanelBodySection3,
                this.detailPanelBodySection4,
                this.detailPanelBodySection5,
            ],
        };
    },
    isTransient: true,
    module: 'show-case',

    node: '@sage/xtrem-show-case/ShowCaseProvider',
    title: 'Page - Detail panel',
    menuItem: containers,
})
export class DetailPanel extends ui.Page {
    @ui.decorators.section<DetailPanel>({
        title: 'Hide/display',
    })
    section1: ui.containers.Section;

    @ui.decorators.block<DetailPanel>({
        parent() {
            return this.section1;
        },
    })
    block1: ui.containers.Block;

    @ui.decorators.buttonField<DetailPanel>({
        parent() {
            return this.block1;
        },
        map() {
            return 'Toggle detail panel';
        },
        onClick() {
            this.$.detailPanel.isHidden = !this.$.detailPanel.isHidden;
        },
    })
    toggleDetailPanelButton: ui.fields.Button;

    @ui.decorators.buttonField<DetailPanel>({
        parent() {
            return this.block1;
        },
        map() {
            return 'Toggle all actions';
        },
        onClick() {
            this.action1.isHidden = !this.action1IsHidden.value;
            this.action1IsHidden.value = !this.action1IsHidden.value;
            this.action2.isHidden = !this.action2IsHidden.value;
            this.action2IsHidden.value = !this.action2IsHidden.value;
            this.action3.isHidden = !this.action3IsHidden.value;
            this.action3IsHidden.value = !this.action3IsHidden.value;
            this.action4.isHidden = !this.action4IsHidden.value;
            this.action4IsHidden.value = !this.action4IsHidden.value;
        },
    })
    toggleAllActionsHidden: ui.fields.Button;

    @ui.decorators.separatorField<DetailPanel>({
        parent() {
            return this.block1;
        },
        isFullWidth: true,
    })
    controlsSeparator1: ui.fields.Separator;

    @ui.decorators.checkboxField<DetailPanel>({
        parent() {
            return this.block1;
        },
        onClick() {
            this.action1.isHidden = !this.action1IsHidden.value;
        },
        title: 'Action 1 isHidden',
    })
    action1IsHidden: ui.fields.Checkbox;

    @ui.decorators.checkboxField<DetailPanel>({
        parent() {
            return this.block1;
        },
        onClick() {
            this.action2.isHidden = !this.action2IsHidden.value;
        },
        title: 'Action 2 isHidden',
    })
    action2IsHidden: ui.fields.Checkbox;

    @ui.decorators.checkboxField<DetailPanel>({
        parent() {
            return this.block1;
        },
        onClick() {
            this.action3.isHidden = !this.action3IsHidden.value;
        },
        title: 'Action 3 isHidden',
    })
    action3IsHidden: ui.fields.Checkbox;

    @ui.decorators.checkboxField<DetailPanel>({
        parent() {
            return this.block1;
        },
        onClick() {
            this.action4.isHidden = !this.action4IsHidden.value;
        },
        title: 'Action 4 isHidden',
    })
    action4IsHidden: ui.fields.Checkbox;

    @ui.decorators.checkboxField<DetailPanel>({
        parent() {
            return this.block1;
        },
        onClick() {
            this.$.detailPanel.isCloseButtonHidden = !this.closeButtonHidden.value;
        },
        title: 'Close button hidden',
    })
    closeButtonHidden: ui.fields.Checkbox;

    @ui.decorators.checkboxField<DetailPanel>({
        parent() {
            return this.block1;
        },
        onClick() {
            this.detailPanelHeaderSection.isHidden = !this.headerSectionHidden.value;
        },
        title: 'Header section hidden',
    })
    headerSectionHidden: ui.fields.Checkbox;

    @ui.decorators.checkboxField<DetailPanel>({
        parent() {
            return this.block1;
        },
        onClick() {
            this.detailPanelBodySection1.isHidden = !this.firstTabSectionHidden.value;
        },
        title: 'First',
    })
    firstTabSectionHidden: ui.fields.Checkbox;

    @ui.decorators.checkboxField<DetailPanel>({
        parent() {
            return this.block1;
        },
        onClick() {
            this.detailPanelBodySection2.isHidden = !this.secondTabSectionHidden.value;
        },
        title: 'Second detail panel section hidden',
    })
    secondTabSectionHidden: ui.fields.Checkbox;

    @ui.decorators.checkboxField<DetailPanel>({
        parent() {
            return this.block1;
        },
        onClick() {
            this.detailPanelBodySection3.isHidden = !this.thirdTabSectionHidden.value;
        },
        title: 'Third detail panel section hidden',
    })
    thirdTabSectionHidden: ui.fields.Checkbox;

    @ui.decorators.checkboxField<DetailPanel>({
        parent() {
            return this.block1;
        },
        onClick() {
            this.detailPanelBodySection4.isHidden = !this.fourthTabSectionHidden.value;
        },
        title: 'Fourth detail panel section hidden',
    })
    fourthTabSectionHidden: ui.fields.Checkbox;

    @ui.decorators.checkboxField<DetailPanel>({
        parent() {
            return this.block1;
        },
        onClick() {
            this.detailPanelBodySection5.isHidden = !this.fifthTabSectionHidden.value;
        },
        title: 'Fifth detail panel section hidden',
    })
    fifthTabSectionHidden: ui.fields.Checkbox;

    @ui.decorators.separatorField<DetailPanel>({
        parent() {
            return this.block1;
        },
        isFullWidth: true,
    })
    controlsSeparator2: ui.fields.Separator;

    @ui.decorators.section<DetailPanel>({
        title: 'Enable/disable',
    })
    section2: ui.containers.Section;

    @ui.decorators.block<DetailPanel>({
        parent() {
            return this.section2;
        },
    })
    block2: ui.containers.Block;

    @ui.decorators.section<DetailPanel>({
        title: 'Resolve scale',
    })
    section3: ui.containers.Section;

    @ui.decorators.block<DetailPanel>({
        parent() {
            return this.section3;
        },
    })
    block3: ui.containers.Block;

    @ui.decorators.buttonField<DetailPanel>({
        parent() {
            return this.block3;
        },
        map() {
            return 'toggleScale';
        },
        onClick() {
            this.detailPanelScaledNumber.scale = 2;
        },
    })
    toggleScale: ui.fields.Button;

    @ui.decorators.checkboxField<DetailPanel>({
        parent() {
            return this.block2;
        },
        onClick() {
            this.action1.isDisabled = !this.action1IsDisabled.value;
        },
        title: 'Action 1 isDisabled',
    })
    action1IsDisabled: ui.fields.Checkbox;

    @ui.decorators.checkboxField<DetailPanel>({
        parent() {
            return this.block2;
        },
        onClick() {
            this.action2.isDisabled = !this.action2IsDisabled.value;
        },
        title: 'Action 2 isDisabled',
    })
    action2IsDisabled: ui.fields.Checkbox;

    @ui.decorators.checkboxField<DetailPanel>({
        parent() {
            return this.block2;
        },
        onClick() {
            this.action3.isDisabled = !this.action3IsDisabled.value;
        },
        title: 'Action 3 isDisabled',
    })
    action3IsDisabled: ui.fields.Checkbox;

    @ui.decorators.checkboxField<DetailPanel>({
        parent() {
            return this.block2;
        },
        onClick() {
            this.action4.isDisabled = !this.action4IsDisabled.value;
        },
        title: 'Action 4 isDisabled',
    })
    action4IsDisabled: ui.fields.Checkbox;

    @ui.decorators.section<DetailPanel>({
        title: 'Time tracking',
    })
    detailPanelHeaderSection: ui.containers.Section;

    @ui.decorators.block<DetailPanel>({
        parent() {
            return this.detailPanelHeaderSection;
        },
    })
    detailPanelHeaderBlock: ui.containers.Block;

    @ui.decorators.labelField<DetailPanel>({
        parent() {
            return this.detailPanelHeaderBlock;
        },
        isTransient: true,
        title: 'Detail panel',
        map() {
            return 'header';
        },
    })
    detailPanelProductImageField: ui.fields.Label;

    @ui.decorators.section<DetailPanel>({
        title: 'One Section',
    })
    detailPanelBodySection1: ui.containers.Section;

    @ui.decorators.block<DetailPanel>({
        parent() {
            return this.detailPanelBodySection1;
        },
        title: 'First section block',
    })
    detailPanelBlock1: ui.containers.Block;

    @ui.decorators.numericField<DetailPanel>({
        isReadOnly: true,
        isTransient: true,
        width: 'large',
        parent() {
            return this.detailPanelBlock1;
        },
        title: 'Scaled number',
    })
    detailPanelScaledNumber4: ui.fields.Numeric;

    @ui.decorators.textField<DetailPanel>({
        isReadOnly: true,
        isTransient: true,
        parent() {
            return this.detailPanelBlock1;
        },
        title: 'My text',
    })
    detailPanelProductCategory: ui.fields.Text;

    @ui.decorators.dateField<DetailPanel>({
        title: 'Date',
        parent() {
            return this.detailPanelBlock1;
        },
    })
    dateField1: ui.fields.Date;

    @ui.decorators.numericField<DetailPanel>({
        isReadOnly: false,
        isTransient: true,
        scale: 1,
        parent() {
            return this.detailPanelBlock1;
        },
        title: 'Scaled number',
    })
    detailPanelScaledNumber: ui.fields.Numeric;

    @ui.decorators.block<DetailPanel>({
        parent() {
            return this.detailPanelBodySection1;
        },
        title: 'Second section block',
    })
    detailPanelBlock2: ui.containers.Block;

    @ui.decorators.textField<DetailPanel>({
        isReadOnly: true,
        isTransient: true,
        parent() {
            return this.detailPanelBlock2;
        },
        title: 'My text',
    })
    detailPanelProductCategory2: ui.fields.Text;

    @ui.decorators.numericField<DetailPanel>({
        isReadOnly: false,
        isTransient: true,
        scale: 1,
        parent() {
            return this.detailPanelBlock2;
        },
        title: 'Scaled number',
    })
    detailPanelScaledNumber2: ui.fields.Numeric;

    @ui.decorators.numericField<DetailPanel>({
        isReadOnly: true,
        isTransient: true,
        width: 'large',
        parent() {
            return this.detailPanelBlock2;
        },
        title: 'Scaled number',
    })
    detailPanelScaledNumber3: ui.fields.Numeric;

    @ui.decorators.block<DetailPanel>({
        parent() {
            return this.detailPanelBodySection1;
        },
        title: 'Third section block',
    })
    detailPanelBlock3: ui.containers.Block;

    @ui.decorators.textField<DetailPanel>({
        isReadOnly: true,
        isTransient: true,
        parent() {
            return this.detailPanelBlock3;
        },
        title: 'My text',
    })
    detailPanelProductCategory3: ui.fields.Text;

    @ui.decorators.numericField<DetailPanel>({
        isReadOnly: false,
        isTransient: true,
        scale: 1,
        parent() {
            return this.detailPanelBlock3;
        },
        title: 'Scaled number',
    })
    detailPanelScaledNumber5: ui.fields.Numeric;

    @ui.decorators.textAreaField<DetailPanel>({
        isTransient: true,
        parent() {
            return this.detailPanelBlock3;
        },
        title: 'My area',
        rows: 5,
    })
    detailPanelNote2: ui.fields.TextArea;

    @ui.decorators.numericField<DetailPanel>({
        isReadOnly: true,
        isTransient: true,
        width: 'large',
        parent() {
            return this.detailPanelBlock3;
        },
        title: 'Scaled number',
    })
    detailPanelScaledNumber6: ui.fields.Numeric;

    @ui.decorators.section<DetailPanel>({
        title: 'Another section',
    })
    detailPanelBodySection2: ui.containers.Section;

    @ui.decorators.block<DetailPanel>({
        parent() {
            return this.detailPanelBodySection2;
        },
        title: 'Draft section block',
    })
    detailPanelBlock4: ui.containers.Block;

    @ui.decorators.textAreaField<DetailPanel>({
        isTransient: true,
        parent() {
            return this.detailPanelBlock4;
        },
        title: 'My area',
        rows: 5,
    })
    detailPanelNote: ui.fields.TextArea;

    @ui.decorators.pageAction<DetailPanel>({
        title: 'Action 1',
        onClick() {
            this.$.showToast('You clicked on action 1');
        },
    })
    action1: ui.PageAction;

    @ui.decorators.pageAction<DetailPanel>({
        title: 'Action 2',
        onClick() {
            this.$.showToast('You clicked on action 2');
        },
    })
    action2: ui.PageAction;

    @ui.decorators.pageAction<DetailPanel>({
        title: 'Action 3',
        onClick() {
            this.$.showToast('You clicked on action 3');
        },
    })
    action3: ui.PageAction;

    @ui.decorators.pageAction<DetailPanel>({
        title: 'Action 4',
        onClick() {
            this.$.showToast('You clicked on action 4');
        },
    })
    action4: ui.PageAction;

    @ui.decorators.section<DetailPanel>({
        title: 'Third',
    })
    detailPanelBodySection3: ui.containers.Section;

    @ui.decorators.block<DetailPanel>({
        parent() {
            return this.detailPanelBodySection3;
        },
        title: 'Third section block',
    })
    detailPanelBlockThird: ui.containers.Block;

    @ui.decorators.section<DetailPanel>({
        title: 'Forth',
    })
    detailPanelBodySection4: ui.containers.Section;

    @ui.decorators.block<DetailPanel>({
        parent() {
            return this.detailPanelBodySection4;
        },
        title: 'Forth section block',
    })
    detailPanelBlockFourth: ui.containers.Block;

    @ui.decorators.section<DetailPanel>({
        title: 'Fifth',
    })
    detailPanelBodySection5: ui.containers.Section;

    @ui.decorators.block<DetailPanel>({
        parent() {
            return this.detailPanelBodySection5;
        },
        title: 'Fifth section block',
    })
    detailPanelBlockFifth: ui.containers.Block;
}
