import * as ui from '@sage/xtrem-ui';
import { fields } from '../menu-items/fields';

@ui.decorators.page<FileUpload>({
    authorizationCode: 'BSCFLDS',
    module: 'show-case',
    title: 'Field - File Upload',
    category: 'SHOWCASE',
    isTransient: true,
    menuItem: fields,
})
export class FileUpload extends ui.Page {
    @ui.decorators.section<FileUpload>({
        title: 'File Upload field',
    })
    section: ui.containers.Section;

    @ui.decorators.block<FileUpload>({
        parent() {
            return this.section;
        },
        title: 'Field example',
    })
    fieldBlock: ui.containers.Block;

    @ui.decorators.fileField<FileUpload>({
        parent() {
            return this.fieldBlock;
        },
        title: 'Title example',
        helperText: 'Helper text example',
        text: 'Text example',
        onClick() {
            this.clickTriggered.isHidden = false;
            setTimeout(() => {
                this.clickTriggered.isHidden = true;
            }, 2500);
        },
        onChange() {
            this.value.value = this.field.value;
            this.changeTriggered.isHidden = false;
            setTimeout(() => {
                this.changeTriggered.isHidden = true;
            }, 2500);
        },
    })
    field: ui.fields.File;

    @ui.decorators.labelField<FileUpload>({
        parent() {
            return this.fieldBlock;
        },
        isHidden: true,
        map() {
            return 'Change was triggered';
        },
    })
    changeTriggered: ui.fields.Label;

    @ui.decorators.labelField<FileUpload>({
        parent() {
            return this.fieldBlock;
        },
        isHidden: true,
        map() {
            return 'Click was triggered';
        },
    })
    clickTriggered: ui.fields.Label;

    @ui.decorators.block<FileUpload>({
        parent() {
            return this.section;
        },
        title: 'Configuration',
    })
    configurationBlock: ui.containers.Block;

    @ui.decorators.checkboxField<FileUpload>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is disabled',
        onChange() {
            this.field.isDisabled = this.isDisabled.value;
        },
    })
    isDisabled: ui.fields.Checkbox;

    @ui.decorators.checkboxField<FileUpload>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is helper text hidden',
        onChange() {
            this.field.isHelperTextHidden = this.isHelperTextHidden.value;
        },
    })
    isHelperTextHidden: ui.fields.Checkbox;

    @ui.decorators.checkboxField<FileUpload>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is hidden',
        onChange() {
            this.field.isHidden = this.isHidden.value;
        },
    })
    isHidden: ui.fields.Checkbox;

    @ui.decorators.checkboxField<FileUpload>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is readOnly',
        onChange() {
            this.field.isReadOnly = this.isReadOnly.value;
        },
    })
    isReadOnly: ui.fields.Checkbox;

    @ui.decorators.checkboxField<FileUpload>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is title hidden',
        onChange() {
            this.field.isTitleHidden = this.isTitleHidden.value;
        },
    })
    isTitleHidden: ui.fields.Checkbox;

    @ui.decorators.textField<FileUpload>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Title',
        onChange() {
            this.field.title = this.title.value;
        },
    })
    title: ui.fields.Text;

    @ui.decorators.textField<FileUpload>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Helper text',
        onChange() {
            this.field.helperText = this.helperText.value;
        },
    })
    helperText: ui.fields.Text;

    @ui.decorators.textField<FileUpload>({
        parent() {
            return this.configurationBlock;
        },
        title: 'text',
        onChange() {
            this.field.text = this.text.value;
        },
    })
    text: ui.fields.Text;

    @ui.decorators.textField<FileUpload>({
        parent() {
            return this.configurationBlock;
        },
        title: 'File types',
        onChange() {
            this.field.fileTypes = this.fileTypes.value;
        },
    })
    fileTypes: ui.fields.Text;

    @ui.decorators.buttonField<FileUpload>({
        parent() {
            return this.configurationBlock;
        },
        map() {
            return 'Focus field';
        },
        onClick() {
            this.field.focus();
        },
    })
    focus: ui.fields.Button;

    @ui.decorators.fileField<FileUpload>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Value',
        text: 'Text Value example',
        onChange() {
            this.field.value = this.value.value;
        },
    })
    value: ui.fields.File;
}
