import { GraphApi, ShowCaseProduct } from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import { navigationPanel } from '../menu-items/navigation-panel';

@ui.decorators.page<NavigationPanelEmpty, ShowCaseProduct>({
    authorizationCode: 'NAVPANEL',
    module: 'show-case',
    title: 'Navigation Panel - Empty',
    node: '@sage/xtrem-show-case/ShowCaseProduct',
    category: 'SHOWCASE',
    navigationPanel: {
        emptyStateText: 'We are in level 1',
        emptyStateClickableText: 'Click me',
        onEmptyStateLinkClick() {
            this.$.dialog.message('info', 'Hey', 'You just clicked me');
        },
        optionsMenu: [
            {
                title: 'Default',
                graphQLFilter: { _id: '-4' },
            },
        ],
        listItem: {
            title: ui.nestedFields.text({ bind: 'product', title: 'Product' }),
            titleRight: ui.nestedFields.label({ bind: 'category' }),
        },
    },
    menuItem: navigationPanel,
})
export class NavigationPanelEmpty extends ui.Page<GraphApi> {
    @ui.decorators.section<NavigationPanelEmpty>({
        title: 'No fields on this page.',
    })
    section: ui.containers.Section;
}
