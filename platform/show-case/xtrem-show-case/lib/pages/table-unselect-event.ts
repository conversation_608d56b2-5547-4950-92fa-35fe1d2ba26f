import { <PERSON>raph<PERSON><PERSON>, ShowCaseProduct, ShowCaseProvider } from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import { tableField } from '../menu-items/_index';

@ui.decorators.page<TableUnselectEvent, ShowCaseProvider>({
    authorizationCode: 'TBLUNSLCTEVNT',
    businessActions() {
        return [this.$standardCancelAction, this.$standardSaveAction];
    },
    category: 'SHOWCASE',
    createAction() {
        return this.$standardNewAction;
    },
    headerSection() {
        return this.headerSection;
    },
    idField() {
        return this.id;
    },
    menuItem: tableField,
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.text({ bind: 'textField' }),
            titleRight: ui.nestedFields.text({ bind: '_id' }),
        },
    },
    node: '@sage/xtrem-show-case/ShowCaseProvider',
    objectTypePlural: 'Providers',
    objectTypeSingular: 'Provider',
    onLoad() {
        const rows = (this.products.value || [])
            .filter((_, index: number) => {
                return index % 2 === 0;
            })
            .map(data => data?._id);

        rows.forEach(id => {
            this.products.selectRecord(id);
        });
    },
    title: 'Provider (with Select/Unselect)',
})
export class TableUnselectEvent extends ui.Page<GraphApi> {
    @ui.decorators.section<TableUnselectEvent>({})
    headerSection: ui.containers.Section;

    @ui.decorators.block<TableUnselectEvent>({
        parent() {
            return this.headerSection;
        },
    })
    headerBlock: ui.containers.Block;

    @ui.decorators.textField<TableUnselectEvent>({
        bind: '_id',
        isReadOnly: true,
        parent() {
            return this.headerBlock;
        },
        title: 'Id',
    })
    id: ui.fields.Text;

    @ui.decorators.textField<TableUnselectEvent>({
        bind: 'textField',
        isReadOnly: true,
        parent() {
            return this.headerBlock;
        },
        title: 'Provider',
    })
    provider: ui.fields.Text;

    @ui.decorators.section<TableUnselectEvent>({})
    section: ui.containers.Section;

    @ui.decorators.block<TableUnselectEvent>({
        parent() {
            return this.section;
        },
    })
    block: ui.containers.Block;

    @ui.decorators.tableField<TableUnselectEvent, ShowCaseProduct>({
        bind: 'products',
        canSelect: true,
        columns: [
            ui.nestedFields.text({ bind: '_id', title: 'Id' }),
            ui.nestedFields.text({ bind: 'product', title: 'Product' }),
        ],
        isReadOnly: true,
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        onRowSelected(id: string) {
            this.$.showToast(`Product "${id}" was selected.`);
        },
        onRowUnselected(id: string) {
            this.$.showToast(`Product "${id}" was unselected.`);
        },
        parent() {
            return this.block;
        },
        title: 'Products',
    })
    products: ui.fields.Table<ShowCaseProduct>;
}
