import { ShowCaseProvider, ShowCaseProduct } from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import { tableField } from '../menu-items/table-field';

@ui.decorators.page<MobileTableHiddenColumns, ShowCaseProvider>({
    authorizationCode: 'BSCFLDS',
    defaultEntry: () => '2',
    module: 'show-case',
    category: 'SHOWCASE',
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.text({ bind: 'textField' }),
        },
    },
    node: '@sage/xtrem-show-case/ShowCaseProvider',
    onLoad() {
        this.field.hideColumn('description');
    },
    title: 'Field - Table Show and hide',
    menuItem: tableField,
})
export class MobileTableHiddenColumns extends ui.Page {
    @ui.decorators.section<MobileTableHiddenColumns>({
        title: 'Table field',
    })
    section: ui.containers.Section;

    @ui.decorators.block<MobileTableHiddenColumns>({
        parent() {
            return this.section;
        },
        title: 'Field example',
    })
    fieldBlock: ui.containers.Block;

    @ui.decorators.tableField<MobileTableHiddenColumns, ShowCaseProduct>({
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        bind: 'products',
        canFilter: false,
        columns: [
            ui.nestedFields.text({
                bind: '_id',
                title: 'Id',
                isHiddenOnMainField: true,
                isReadOnly: true,
                isHidden() {
                    return true;
                },
            }),
            ui.nestedFields.text({
                bind: 'product',
                title: 'Product',
            }),
            ui.nestedFields.text({
                bind: 'description',
                title: 'Description',
            }),
            ui.nestedFields.numeric({
                bind: 'qty',
                title: 'Quantity',
                fetchesDefaults: true,
            }),
            ui.nestedFields.numeric({
                bind: 'listPrice',
                title: 'List Price',
                scale: 2,
                isHiddenOnMainField: true,
                fetchesDefaults: true,
            }),
            ui.nestedFields.numeric({
                bind: 'netPrice',
                title: 'Net Price',
                isReadOnly: (value: number, rowValue: any) => {
                    return !!rowValue.hotProduct;
                },
                scale: 2,
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.checkbox({
                bind: 'hotProduct',
                title: 'Hot',
            }),
            ui.nestedFields.switch({
                bind: 'hotProduct',
                title: 'Hot (Switch)',
            }),
            ui.nestedFields.link({
                bind: 'product',
                isTransient: true,
                map(_fieldValue, rowData) {
                    return `http://${(rowData.product as string)
                        .replace(/-|_|\s/g, '')
                        .substring(0, 4)
                        .toLowerCase()}.sage.com`;
                },
                onClick(id: string, raw: any) {
                    ui.console.log('CLICKED LINK', { id, raw });
                },
                title: 'Link',
                page: 'https://www.google.com',
            }),
            ui.nestedFields.progress({
                bind: 'progress',
                color: (v: number) => {
                    if (v < 30) {
                        return ui.tokens.colorsSemanticNegative500;
                    }
                    if (v < 50) {
                        return ui.tokens.colorsSemanticCaution500;
                    }
                    if (v > 90) {
                        return ui.tokens.colorsSemanticPositive500;
                    }
                    return ui.tokens.colorsUtilityMajor200;
                },
                title: 'Progress',
            }),
            ui.nestedFields.numeric({
                bind: 'tax',
                title: 'Tax',
                prefix: 'T',
                scale: 2,
                isHiddenOnMainField: true,
            }),
        ],
        orderBy: {
            product: 1,
        },
        parent() {
            return this.fieldBlock;
        },
    })
    field: ui.fields.Table<ShowCaseProduct>;

    @ui.decorators.textField<MobileTableHiddenColumns>({
        title: 'Columns',
        isTransient: true,
        isFullWidth: true,
        helperText: "Such as 'description' or 'product'",
        parent() {
            return this.fieldBlock;
        },
    })
    columnsList: ui.fields.Text;

    @ui.decorators.buttonField<MobileTableHiddenColumns>({
        isTransient: true,
        map() {
            return 'Hide Columns';
        },
        parent() {
            return this.fieldBlock;
        },
        onClick() {
            this.columnsList.value.split(',').map(column => this.field.hideColumn(column.trim()));
        },
    })
    hideColumnButton: ui.fields.Button;

    @ui.decorators.buttonField<MobileTableHiddenColumns>({
        isTransient: true,
        map() {
            return 'Show Columns';
        },
        parent() {
            return this.fieldBlock;
        },
        onClick() {
            this.columnsList.value.split(',').map(column => this.field.showColumn(column.trim()));
        },
    })
    showColumnButton: ui.fields.Button;
}
