import { GraphApi, ShowCaseProduct } from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import { referenceField } from '../menu-items/reference-field';

@ui.decorators.page<ReferenceWithDefault>({
    authorizationCode: 'REFWTDEFLT',
    category: 'SHOWCASE',
    menuItem: referenceField,
    module: 'show-case',
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.text({ bind: 'textField', title: 'Name' }),
            line2: ui.nestedFields.text({ bind: '_id', title: 'ID' }),
            line3: ui.nestedFields.date({ bind: 'dateField', title: 'Added on' }),
            image: ui.nestedFields.image({ bind: 'logo', title: 'Logo', placeholderMode: 'Initials' }),
        },
    },
    node: '@sage/xtrem-show-case/ShowCaseProvider',
    title: 'Reference Field (Default Bindings)',
})
export class ReferenceWithDefault extends ui.Page<GraphApi> {
    @ui.decorators.section<ReferenceWithDefault>({
        title: 'Reference Field with Default Bindings',
    })
    section: ui.containers.Section;

    @ui.decorators.block<ReferenceWithDefault>({
        parent() {
            return this.section;
        },
    })
    block: ui.containers.Block;

    @ui.decorators.textField<ReferenceWithDefault>({
        isReadOnly: true,
        parent() {
            return this.block;
        },
        title: 'Id',
        width: 'medium',
    })
    _id: ui.fields.Text;

    @ui.decorators.referenceField<ReferenceWithDefault, ShowCaseProduct>({
        bind: 'flagshipProduct',
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        orderBy: { _id: 1 },
        parent() {
            return this.block;
        },
        width: 'medium',
    })
    field: ui.fields.Reference<ShowCaseProduct>;

    @ui.decorators.referenceField<ReferenceWithDefault, ShowCaseProduct>({
        bind: 'flagshipProduct',
        helperTextField: 'barcode',
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        orderBy: { _id: 1 },
        parent() {
            return this.block;
        },
        title: 'Partially Custom Reference',
        valueField: 'product',
        width: 'medium',
    })
    fieldWithPartial: ui.fields.Reference<ShowCaseProduct>;

    @ui.decorators.referenceField<ReferenceWithDefault, ShowCaseProduct>({
        bind: 'flagshipProduct',
        columns: [
            ui.nestedFields.text({ bind: '_id', title: 'Id' }),
            ui.nestedFields.text({ bind: 'product', title: 'Product' }),
            ui.nestedFields.text({ bind: 'barcode', title: 'Barcode' }),
        ],
        helperTextField: 'barcode',
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        orderBy: { _id: 1 },
        parent() {
            return this.block;
        },
        title: 'Fully Custom Reference',
        valueField: 'product',
        width: 'medium',
    })
    fieldWithFull: ui.fields.Reference<ShowCaseProduct>;
}
