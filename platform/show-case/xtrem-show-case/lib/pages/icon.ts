import * as ui from '@sage/xtrem-ui';
import { fields } from '../menu-items/fields';

@ui.decorators.page<Icon>({
    authorizationCode: 'BSCFLDS',
    module: 'show-case',
    title: 'Field - Icon',
    isTransient: true,
    category: 'SHOWCASE',
    onLoad() {
        // Check https://carbon.sage.com/style/icons for valid icons
        this.field.value = 'phone';
        this.value.value = 'phone';
    },
    menuItem: fields,
})
export class Icon extends ui.Page {
    @ui.decorators.section<Icon>({
        title: 'Icon field',
    })
    section: ui.containers.Section;

    @ui.decorators.block<Icon>({
        parent() {
            return this.section;
        },
        title: 'Field example',
    })
    fieldBlock: ui.containers.Block;

    @ui.decorators.iconField<Icon>({
        parent() {
            return this.fieldBlock;
        },
    })
    field: ui.fields.Icon;

    @ui.decorators.block<Icon>({
        parent() {
            return this.section;
        },
        title: 'Configuration',
    })
    configurationBlock: ui.containers.Block;

    @ui.decorators.textField<Icon>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Color',
        onChange() {
            this.field.color = this.color.value;
        },
    })
    color: ui.fields.Text;

    @ui.decorators.textField<Icon>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Helper text',
        onChange() {
            this.field.helperText = this.helperText.value;
        },
    })
    helperText: ui.fields.Text;

    @ui.decorators.checkboxField<Icon>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is disabled',
        onChange() {
            this.field.isDisabled = this.isDisabled.value;
        },
    })
    isDisabled: ui.fields.Checkbox;

    @ui.decorators.checkboxField<Icon>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is helper text hidden',
        onChange() {
            this.field.isHelperTextHidden = this.isHelperTextHidden.value;
        },
    })
    isHelperTextHidden: ui.fields.Checkbox;

    @ui.decorators.checkboxField<Icon>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is hidden',
        onChange() {
            this.field.isHidden = this.isHidden.value;
        },
    })
    isHidden: ui.fields.Checkbox;

    @ui.decorators.checkboxField<Icon>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is title hidden',
        onChange() {
            this.field.isTitleHidden = this.isTitleHidden.value;
        },
    })
    isTitleHidden: ui.fields.Checkbox;

    @ui.decorators.textField<Icon>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Title',
        onChange() {
            this.field.title = this.title.value;
        },
    })
    title: ui.fields.Text;

    @ui.decorators.textField<Icon>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Value',
        onChange() {
            this.field.value = this.value.value;
        },
    })
    value: ui.fields.Text;
}
