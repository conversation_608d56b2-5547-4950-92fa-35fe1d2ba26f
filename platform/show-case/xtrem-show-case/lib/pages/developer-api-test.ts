import * as ui from '@sage/xtrem-ui';
import { misc } from '../menu-items/misc';

@ui.decorators.page<DeveloperApiTest>({
    title: 'Developer API Test',
    isTransient: true,
    module: 'show-case',
    category: 'SHOWCASE',
    menuItem: misc,
    onLoad() {
        const isShowCaseOption1Enabled = this.$.isServiceOptionEnabled('showCaseOption1');
        const isShowCaseOption2Enabled = this.$.isServiceOptionEnabled('showCaseOption2');
        const isShowCaseOption3Enabled = this.$.isServiceOptionEnabled('showCaseOption3');
        this.showCaseOption1.value = isShowCaseOption1Enabled ? 'Enabled' : 'Disabled';
        this.showCaseOption2.value = isShowCaseOption2Enabled ? 'Enabled' : 'Disabled';
        this.showCaseOption3.value = isShowCaseOption3Enabled ? 'Enabled' : 'Disabled';
    },
})
export class DeveloperApiTest extends ui.Page {
    @ui.decorators.section({})
    section: ui.containers.Section;

    @ui.decorators.block({
        title: 'Test isServiceOptionEnabled',
        parent() {
            return this.section;
        },
    })
    block: ui.containers.Block;

    @ui.decorators.textField({
        title: 'showCaseOption1',
        parent() {
            return this.block;
        },
        isReadOnly: true,
    })
    showCaseOption1: ui.fields.Text;

    @ui.decorators.textField({
        title: 'showCaseOption2',
        parent() {
            return this.block;
        },
        isReadOnly: true,
    })
    showCaseOption2: ui.fields.Text;

    @ui.decorators.textField({
        title: 'showCaseOption3',
        parent() {
            return this.block;
        },
        isReadOnly: true,
    })
    showCaseOption3: ui.fields.Text;
}
