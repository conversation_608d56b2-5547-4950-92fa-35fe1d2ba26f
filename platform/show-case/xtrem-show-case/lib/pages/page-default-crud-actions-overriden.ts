import { <PERSON><PERSON>h<PERSON><PERSON>, ShowCaseProduct as ShowCaseProductNode, ShowCaseProvider } from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import { misc } from '../menu-items/misc';

@ui.decorators.page<PageDefaultCrudActionsOverriden, ShowCaseProductNode>({
    authorizationCode: 'SHCPRDT',
    module: 'show-case',
    title: 'Page - Default CRUD Actions Overriden',
    node: '@sage/xtrem-show-case/ShowCaseProduct',
    category: 'SHOWCASE',
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.text({ bind: 'product' }),
            titleRight: ui.nestedFields.text({ bind: '_id' }),
            line2: ui.nestedFields.text({ bind: 'description', canFilter: false }),
        },
    },
    menuItem: misc,
    createAction() {
        return this.$standardNewAction;
    },
    businessActions() {
        return [this.saveCrudUpdate];
    },
    headerDropDownActions() {
        return [this.$standardDeleteAction];
    },
})
export class PageDefaultCrudActionsOverriden extends ui.Page<GraphApi> {
    override $standardDeletePromptTitle = 'Overridden dialog title';

    @ui.decorators.section<PageDefaultCrudActionsOverriden>({
        isTitleHidden: true,
    })
    section: ui.containers.Section;

    @ui.decorators.block<PageDefaultCrudActionsOverriden>({
        parent() {
            return this.section;
        },
    })
    block: ui.containers.Block;

    @ui.decorators.textField<PageDefaultCrudActionsOverriden>({
        parent() {
            return this.block;
        },
        title: 'Product',
        bind: 'product',
        isMandatory: true,
    })
    product1: ui.fields.Text;

    @ui.decorators.textField<PageDefaultCrudActionsOverriden>({
        parent() {
            return this.block;
        },
        title: 'Description',
        bind: 'description',
    })
    description1: ui.fields.Text;

    @ui.decorators.checkboxField<PageDefaultCrudActionsOverriden>({
        parent() {
            return this.block;
        },
        title: 'Hot product',
    })
    hotProduct: ui.fields.Checkbox;

    @ui.decorators.numericField<PageDefaultCrudActionsOverriden>({
        parent() {
            return this.block;
        },
        title: 'Quantity',
        fetchesDefaults: true,
    })
    qty: ui.fields.Numeric;

    @ui.decorators.numericField<PageDefaultCrudActionsOverriden>({
        parent() {
            return this.block;
        },
        title: 'Stock',
    })
    st: ui.fields.Numeric;

    @ui.decorators.numericField<PageDefaultCrudActionsOverriden>({
        parent() {
            return this.block;
        },
        title: 'List price',
        fetchesDefaults: true,
        scale: 2,
    })
    listPrice: ui.fields.Numeric;

    @ui.decorators.numericField<PageDefaultCrudActionsOverriden>({
        parent() {
            return this.block;
        },
        title: 'Tax',
        scale: 2,
    })
    tax: ui.fields.Numeric;

    @ui.decorators.numericField<PageDefaultCrudActionsOverriden>({
        parent() {
            return this.block;
        },
        title: 'Amount',
        scale: 2,
    })
    amount: ui.fields.Numeric;

    @ui.decorators.numericField<PageDefaultCrudActionsOverriden>({
        parent() {
            return this.block;
        },
        title: 'Net price',
        scale: 2,
    })
    netPrice: ui.fields.Numeric;

    @ui.decorators.referenceField<PageDefaultCrudActionsOverriden, ShowCaseProvider>({
        parent() {
            return this.block;
        },
        columns: [ui.nestedFields.text({ bind: 'textField' })],
        node: '@sage/xtrem-show-case/ShowCaseProvider',
        title: 'Provider',
        valueField: 'textField',
        helperTextField: '_id',
        minLookupCharacters: 0,
    })
    provider: ui.fields.Reference;

    @ui.decorators.dateField<PageDefaultCrudActionsOverriden>({
        parent() {
            return this.block;
        },
        title: 'Release date',
    })
    releaseDate: ui.fields.Date;

    @ui.decorators.dateField<PageDefaultCrudActionsOverriden>({
        parent() {
            return this.block;
        },
        title: 'Ending date',
    })
    endingDate: ui.fields.Date;

    @ui.decorators.radioField<PageDefaultCrudActionsOverriden>({
        parent() {
            return this.block;
        },
        title: 'Category Select',
        optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
    })
    category: ui.fields.Radio;

    @ui.decorators.numericField<PageDefaultCrudActionsOverriden>({
        parent() {
            return this.block;
        },
        title: 'Discount',
    })
    discount: ui.fields.Numeric;

    @ui.decorators.numericField<PageDefaultCrudActionsOverriden>({
        parent() {
            return this.block;
        },
        title: 'Fixed Quantity',
        isTransientInput: true,
    })
    fixedQuantity: ui.fields.Numeric;

    @ui.decorators.pageAction<PageDefaultCrudActionsOverriden>({
        title: 'Save',
        async onClick() {
            this.$.showToast('Starting to save');
            await this.$standardSaveAction.execute();
            this.$.showToast('Saved');
        },
    })
    saveCrudUpdate: ui.PageAction;
}
