import * as ui from '@sage/xtrem-ui';
import { misc } from '../menu-items/misc';
import { ShowCaseCustomer, GraphApi } from '@sage/xtrem-show-case-api';

interface QueryParameterTableItem {
    _id: string;
    name: string;
    value: string;
}

@ui.decorators.page<Dialogs>({
    authorizationCode: 'DIALOGIT',
    module: 'show-case',
    title: 'Dialogs',
    isTransient: true,
    category: 'SHOWCASE',
    onLoad() {
        this.messageDialogAcceptButtonText.value = 'Accept';
        this.messageDialogLevel.value = 'info';
        this.messageDialogMessage.value = 'The actual message';
        this.messageDialogTitle.value = 'Message dialog';

        this.confirmationDialogAcceptButtonText.value = 'Accept';
        this.confirmationDialogCancelButtonText.value = 'Cancel';
        this.confirmationDialogLevel.value = 'info';
        this.confirmationDialogMessage.value = 'The actual confirmation message';
        this.confirmationDialogTitle.value = 'Confirmation dialog';

        this.customDialogAcceptButtonText.value = 'Accept';
        this.customDialogCancelButtonText.value = 'Cancel';
        this.customDialogLevel.value = 'info';
        this.customDialogTitle.value = 'Custom dialog';

        this.pageDialogPath.value = '@sage/xtrem-show-case/Link';
        this.mockedProcessingTime.value = 2000;
    },
    menuItem: misc,
})
export class Dialogs extends ui.Page<GraphApi> {
    @ui.decorators.section<Dialogs>({
        title: 'Message dialog',
    })
    messageDialogSection: ui.containers.Section;

    @ui.decorators.block<Dialogs>({
        parent() {
            return this.messageDialogSection;
        },
    })
    messageDialogBlock: ui.containers.Block;

    @ui.decorators.checkboxField<Dialogs>({
        title: 'Accept button is disabled',
        parent() {
            return this.messageDialogBlock;
        },
    })
    messageDialogAcceptButtonIsDisabled: ui.fields.Checkbox;

    @ui.decorators.checkboxField<Dialogs>({
        title: 'Accept button is hidden',
        parent() {
            return this.messageDialogBlock;
        },
    })
    messageDialogAcceptButtonIsHidden: ui.fields.Checkbox;

    @ui.decorators.textField<Dialogs>({
        title: 'Accept button text',
        parent() {
            return this.messageDialogBlock;
        },
    })
    messageDialogAcceptButtonText: ui.fields.Text;

    @ui.decorators.checkboxField<Dialogs>({
        title: 'Is fullscreen',
        parent() {
            return this.messageDialogBlock;
        },
    })
    messageDialogIsFullscreen: ui.fields.Checkbox;

    @ui.decorators.checkboxField<Dialogs>({
        title: 'Is right aligned',
        parent() {
            return this.messageDialogBlock;
        },
    })
    messageDialogIsRightAligned: ui.fields.Checkbox;

    @ui.decorators.selectField<Dialogs>({
        title: 'Level',
        options: ['info', 'success', 'error', 'warn'],
        parent() {
            return this.messageDialogBlock;
        },
    })
    messageDialogLevel: ui.fields.Select;

    @ui.decorators.textField<Dialogs>({
        title: 'Message',
        parent() {
            return this.messageDialogBlock;
        },
    })
    messageDialogMessage: ui.fields.Text;

    @ui.decorators.selectField<Dialogs>({
        title: 'Size',
        options: ['extra-small', 'small', 'medium-small', 'medium', 'medium-large', 'large', 'extra-large'],
        parent() {
            return this.messageDialogBlock;
        },
    })
    messageDialogSize: ui.fields.Select;

    @ui.decorators.textField<Dialogs>({
        title: 'Title',
        parent() {
            return this.messageDialogBlock;
        },
    })
    messageDialogTitle: ui.fields.Text;

    @ui.decorators.buttonField<Dialogs>({
        map: () => 'Open dialog',
        onClick() {
            const options: ui.dialogs.DialogOptions = {
                fullScreen: this.messageDialogIsFullscreen.value,
                rightAligned: this.messageDialogIsRightAligned.value,
                size: this.messageDialogSize.value as ui.dialogs.DialogSize,
                acceptButton: {
                    isDisabled: this.messageDialogAcceptButtonIsDisabled.value,
                    isHidden: this.messageDialogAcceptButtonIsHidden.value,
                    text: this.messageDialogAcceptButtonText.value,
                },
            };
            this.$.dialog.message(
                this.messageDialogLevel.value as ui.dialogs.DialogLevel,
                this.messageDialogTitle.value,
                this.messageDialogMessage.value,
                options,
            );
        },
        parent() {
            return this.messageDialogBlock;
        },
    })
    messageDialogButton: ui.fields.Button;

    /** *************************************************************************/

    @ui.decorators.section<Dialogs>({
        title: 'Confirm dialog',
    })
    confirmationDialogSection: ui.containers.Section;

    @ui.decorators.block<Dialogs>({
        parent() {
            return this.confirmationDialogSection;
        },
    })
    confirmationDialogBlock: ui.containers.Block;

    @ui.decorators.checkboxField<Dialogs>({
        title: 'Accept button is disabled',
        parent() {
            return this.confirmationDialogBlock;
        },
    })
    confirmationDialogAcceptButtonIsDisabled: ui.fields.Checkbox;

    @ui.decorators.checkboxField<Dialogs>({
        title: 'Accept button is hidden',
        parent() {
            return this.confirmationDialogBlock;
        },
    })
    confirmationDialogAcceptButtonIsHidden: ui.fields.Checkbox;

    @ui.decorators.textField<Dialogs>({
        title: 'Accept button text',
        parent() {
            return this.confirmationDialogBlock;
        },
    })
    confirmationDialogAcceptButtonText: ui.fields.Text;

    @ui.decorators.checkboxField<Dialogs>({
        title: 'Cancel button is disabled',
        parent() {
            return this.confirmationDialogBlock;
        },
    })
    confirmationDialogCancelButtonIsDisabled: ui.fields.Checkbox;

    @ui.decorators.checkboxField<Dialogs>({
        title: 'Cancel button is hidden',
        parent() {
            return this.confirmationDialogBlock;
        },
    })
    confirmationDialogCancelButtonIsHidden: ui.fields.Checkbox;

    @ui.decorators.textField<Dialogs>({
        title: 'Cancel button text',
        parent() {
            return this.confirmationDialogBlock;
        },
    })
    confirmationDialogCancelButtonText: ui.fields.Text;

    @ui.decorators.checkboxField<Dialogs>({
        title: 'Is fullscreen',
        parent() {
            return this.confirmationDialogBlock;
        },
    })
    confirmationDialogIsFullscreen: ui.fields.Checkbox;

    @ui.decorators.checkboxField<Dialogs>({
        title: 'Is right aligned',
        parent() {
            return this.confirmationDialogBlock;
        },
    })
    confirmationDialogIsRightAligned: ui.fields.Checkbox;

    @ui.decorators.selectField<Dialogs>({
        title: 'Level',
        options: ['info', 'success', 'error', 'warn'],
        parent() {
            return this.confirmationDialogBlock;
        },
    })
    confirmationDialogLevel: ui.fields.Select;

    @ui.decorators.textField<Dialogs>({
        title: 'Message',
        parent() {
            return this.confirmationDialogBlock;
        },
    })
    confirmationDialogMessage: ui.fields.Text;

    @ui.decorators.selectField<Dialogs>({
        title: 'Size',
        options: ['extra-small', 'small', 'medium-small', 'medium', 'medium-large', 'large', 'extra-large'],
        parent() {
            return this.confirmationDialogBlock;
        },
    })
    confirmationDialogSize: ui.fields.Select;

    @ui.decorators.textField<Dialogs>({
        title: 'Title',
        parent() {
            return this.confirmationDialogBlock;
        },
    })
    confirmationDialogTitle: ui.fields.Text;

    @ui.decorators.buttonField<Dialogs>({
        map: () => 'Open dialog',
        onClick() {
            const options: ui.dialogs.DialogOptions = {
                fullScreen: this.confirmationDialogIsFullscreen.value,
                rightAligned: this.confirmationDialogIsRightAligned.value,
                size: this.confirmationDialogSize.value as ui.dialogs.DialogSize,
                acceptButton: {
                    isDisabled: this.confirmationDialogAcceptButtonIsDisabled.value,
                    isHidden: this.confirmationDialogAcceptButtonIsHidden.value,
                    text: this.confirmationDialogAcceptButtonText.value,
                },
                cancelButton: {
                    isDisabled: this.confirmationDialogCancelButtonIsDisabled.value,
                    isHidden: this.confirmationDialogCancelButtonIsHidden.value,
                    text: this.confirmationDialogCancelButtonText.value,
                },
            };
            this.$.dialog.confirmation(
                this.confirmationDialogLevel.value as ui.dialogs.DialogLevel,
                this.confirmationDialogTitle.value,
                this.confirmationDialogMessage.value,
                options,
            );
        },
        parent() {
            return this.confirmationDialogBlock;
        },
    })
    confirmationDialogButton: ui.fields.Button;

    /** *************************************************************************/

    @ui.decorators.section<Dialogs>({
        title: 'Custom dialog',
    })
    customDialogSection: ui.containers.Section;

    @ui.decorators.block<Dialogs>({
        parent() {
            return this.customDialogSection;
        },
    })
    customDialogBlock: ui.containers.Block;

    @ui.decorators.checkboxField<Dialogs>({
        title: 'Accept button is disabled',
        parent() {
            return this.customDialogBlock;
        },
    })
    customDialogAcceptButtonIsDisabled: ui.fields.Checkbox;

    @ui.decorators.checkboxField<Dialogs>({
        title: 'Accept button is hidden',
        parent() {
            return this.customDialogBlock;
        },
    })
    customDialogAcceptButtonIsHidden: ui.fields.Checkbox;

    @ui.decorators.textField<Dialogs>({
        title: 'Accept button text',
        parent() {
            return this.customDialogBlock;
        },
    })
    customDialogAcceptButtonText: ui.fields.Text;

    @ui.decorators.checkboxField<Dialogs>({
        title: 'Cancel button is disabled',
        parent() {
            return this.customDialogBlock;
        },
    })
    customDialogCancelButtonIsDisabled: ui.fields.Checkbox;

    @ui.decorators.checkboxField<Dialogs>({
        title: 'Cancel button is hidden',
        parent() {
            return this.customDialogBlock;
        },
    })
    customDialogCancelButtonIsHidden: ui.fields.Checkbox;

    @ui.decorators.textField<Dialogs>({
        title: 'Cancel button text',
        parent() {
            return this.customDialogBlock;
        },
    })
    customDialogCancelButtonText: ui.fields.Text;

    @ui.decorators.checkboxField<Dialogs>({
        title: 'Is fullscreen',
        parent() {
            return this.customDialogBlock;
        },
    })
    customDialogIsFullscreen: ui.fields.Checkbox;

    @ui.decorators.checkboxField<Dialogs>({
        title: 'Is right aligned',
        parent() {
            return this.customDialogBlock;
        },
    })
    customDialogIsRightAligned: ui.fields.Checkbox;

    @ui.decorators.checkboxField<Dialogs>({
        title: 'Multiple sections?',
        parent() {
            return this.customDialogBlock;
        },
    })
    customDialogMultipleSections: ui.fields.Checkbox;

    @ui.decorators.selectField<Dialogs>({
        title: 'Level',
        options: ['info', 'success', 'error', 'warn'],
        parent() {
            return this.customDialogBlock;
        },
    })
    customDialogLevel: ui.fields.Select;

    @ui.decorators.selectField<Dialogs>({
        title: 'Size',
        options: ['extra-small', 'small', 'medium-small', 'medium', 'medium-large', 'large', 'extra-large'],
        parent() {
            return this.customDialogBlock;
        },
    })
    customDialogSize: ui.fields.Select;

    @ui.decorators.textField<Dialogs>({
        title: 'Title',
        parent() {
            return this.customDialogBlock;
        },
    })
    customDialogTitle: ui.fields.Text;

    @ui.decorators.buttonField<Dialogs>({
        map: () => 'Open dialog',
        onError() {
            this.customDialogContentSection.isHidden = true;
            this.customDialogMultiSectionsContentSection.isHidden = true;
            return '';
        },
        async onClick() {
            const options: ui.dialogs.CustomDialogOptions = {
                fullScreen: this.customDialogIsFullscreen.value,
                rightAligned: this.customDialogIsRightAligned.value,
                size: this.customDialogSize.value as ui.dialogs.DialogSize,
                acceptButton: {
                    isDisabled: this.customDialogAcceptButtonIsDisabled.value,
                    isHidden: this.customDialogAcceptButtonIsHidden.value,
                    text: this.customDialogAcceptButtonText.value,
                },
                cancelButton: {
                    isDisabled: this.customDialogCancelButtonIsDisabled.value,
                    isHidden: this.customDialogCancelButtonIsHidden.value,
                    text: this.customDialogCancelButtonText.value,
                },
                dialogTitle: this.customDialogTitle.value,
            };
            this.customDialogContentSection.isHidden = false;
            this.customDialogMultiSectionsContentSection.isHidden = false;
            await this.$.commitValueAndPropertyChanges();
            await this.$.dialog.custom(
                this.customDialogLevel.value as ui.dialogs.DialogLevel,
                this.customDialogMultipleSections.value
                    ? [this.customDialogContentSection, this.customDialogMultiSectionsContentSection]
                    : this.customDialogContentSection,
                options,
            );
            this.customDialogContentSection.isHidden = true;
            this.customDialogMultiSectionsContentSection.isHidden = true;
        },
        parent() {
            return this.customDialogBlock;
        },
    })
    customDialogButton: ui.fields.Button;

    @ui.decorators.section<Dialogs>({
        isHidden: true,
        title: 'A basic section',
    })
    customDialogContentSection: ui.containers.Section;

    @ui.decorators.block<Dialogs>({
        title: 'Block content',
        parent() {
            return this.customDialogContentSection;
        },
    })
    customDialogContentBlock: ui.containers.Block;

    @ui.decorators.iconField<Dialogs>({
        title: 'Example field',
        map() {
            return 'home';
        },
        parent() {
            return this.customDialogContentBlock;
        },
    })
    customDialogContentField: ui.fields.Icon;

    @ui.decorators.block<Dialogs>({
        title: 'Block 2',
        parent() {
            return this.customDialogContentSection;
        },
    })
    customDialogContentBlock2: ui.containers.Block;

    @ui.decorators.textField<Dialogs>({
        title: 'A Field ',
        parent() {
            return this.customDialogContentBlock2;
        },
    })
    customDialogContentBlock2Field: ui.fields.Text;

    @ui.decorators.block<Dialogs>({
        title: 'Block 3',
        parent() {
            return this.customDialogContentSection;
        },
    })
    customDialogContentBlock3: ui.containers.Block;

    @ui.decorators.textAreaField<Dialogs>({
        title: 'Another Field ',
        parent() {
            return this.customDialogContentBlock3;
        },
    })
    customDialogContentBlock3Field: ui.fields.TextArea;

    /** *************************************************************************/

    @ui.decorators.section<Dialogs>({
        isHidden: true,
        title: 'Second Section',
    })
    customDialogMultiSectionsContentSection: ui.containers.Section;

    @ui.decorators.block<Dialogs>({
        title: 'Second Section block',
        parent() {
            return this.customDialogMultiSectionsContentSection;
        },
    })
    customDialogMultiSectionsContentBlock: ui.containers.Block;

    @ui.decorators.iconField<Dialogs>({
        title: 'Example field',
        map() {
            return 'home';
        },
        parent() {
            return this.customDialogMultiSectionsContentBlock;
        },
    })
    customDialogMultiSectionsContentField: ui.fields.Icon;

    @ui.decorators.block<Dialogs>({
        title: 'Block 2',
        parent() {
            return this.customDialogMultiSectionsContentSection;
        },
    })
    customDialogMultiSectionsContentBlock2: ui.containers.Block;

    @ui.decorators.textAreaField<Dialogs>({
        title: 'A Field ',
        rows: 8,
        parent() {
            return this.customDialogMultiSectionsContentBlock2;
        },
    })
    customDialogMultiSectionsContentBlock2Field: ui.fields.TextArea;

    /** *************************************************************************/

    @ui.decorators.section<Dialogs>({
        title: 'Error Dialog',
    })
    errorDialogSection: ui.containers.Section;

    @ui.decorators.block<Dialogs>({
        parent() {
            return this.errorDialogSection;
        },
    })
    errorDialogBlock: ui.containers.Block;

    @ui.decorators.buttonField<Dialogs>({
        map() {
            return 'Show error an dialog';
        },
        onClick() {
            throw new Error('THIS IS AN ERROR!');
        },
        parent() {
            return this.errorDialogBlock;
        },
    })
    errorDialogField: ui.fields.Button;

    /** *************************************************************************/

    @ui.decorators.section<Dialogs>({
        title: 'Page dialog',
    })
    pageDialogSection: ui.containers.Section;

    @ui.decorators.block<Dialogs>({
        parent() {
            return this.pageDialogSection;
        },
    })
    pageDialogBlock: ui.containers.Block;

    @ui.decorators.checkboxField<Dialogs>({
        title: 'Is fullscreen',
        parent() {
            return this.pageDialogBlock;
        },
    })
    pageDialogIsFullscreen: ui.fields.Checkbox;

    @ui.decorators.checkboxField<Dialogs>({
        title: 'Is right aligned',
        parent() {
            return this.pageDialogBlock;
        },
    })
    pageDialogIsRightAligned: ui.fields.Checkbox;

    @ui.decorators.checkboxField<Dialogs>({
        title: 'Skip dirty check',
        parent() {
            return this.pageDialogBlock;
        },
    })
    pageDialogSkipsDirtyCheck: ui.fields.Checkbox;

    @ui.decorators.checkboxField<Dialogs>({
        title: 'Resolve even if its closed',
        parent() {
            return this.pageDialogBlock;
        },
    })
    pageDialogResolveWhenClosed: ui.fields.Checkbox;

    @ui.decorators.checkboxField<Dialogs>({
        title: 'Render main list',
        helperText: 'By default, main lists and navigation panels are not rendered in dialogs.',
        parent() {
            return this.pageDialogBlock;
        },
    })
    pageDialogDisplayMainList: ui.fields.Checkbox;

    @ui.decorators.textField<Dialogs>({
        title: 'Path',
        parent() {
            return this.pageDialogBlock;
        },
    })
    pageDialogPath: ui.fields.Text;

    @ui.decorators.selectField<Dialogs>({
        title: 'Size',
        options: ['extra-small', 'small', 'medium-small', 'medium', 'medium-large', 'large', 'extra-large'],
        parent() {
            return this.pageDialogBlock;
        },
    })
    pageDialogSize: ui.fields.Select;

    @ui.decorators.tableField<Dialogs>({
        title: 'Query parameters',
        columns: [
            ui.nestedFields.text({ bind: 'name', title: 'Name' }),
            ui.nestedFields.text({ bind: 'value', title: 'Value' }),
        ],
        dropdownActions: [
            {
                icon: 'cross_circle',
                title: 'Remove',
                onClick(_id: any) {
                    this.pageDialogQueryParameters.value = this.pageDialogQueryParameters.value.filter(
                        row => row._id !== _id,
                    );
                },
            },
        ],
        parent() {
            return this.pageDialogBlock;
        },
    })
    pageDialogQueryParameters: ui.fields.Table<QueryParameterTableItem>;

    @ui.decorators.textField<Dialogs>({
        parent() {
            return this.pageDialogBlock;
        },
        title: 'Query parameter name',
    })
    pageDialogQueryParameterName: ui.fields.Text;

    @ui.decorators.textField<Dialogs>({
        parent() {
            return this.pageDialogBlock;
        },
        title: 'Query parameter value',
    })
    pageDialogQueryParameterValue: ui.fields.Text;

    @ui.decorators.buttonField<Dialogs>({
        map() {
            return 'Add query parameter';
        },
        onClick() {
            const parameters = this.pageDialogQueryParameters.value || [];
            const name = this.pageDialogQueryParameterName.value || `Parameter ${parameters.length + 1}`;
            const value = this.pageDialogQueryParameterValue.value || `Value ${parameters.length + 1}`;
            this.pageDialogQueryParameters.addRecord({ name, value });
            this.pageDialogQueryParameterName.value = '';
            this.pageDialogQueryParameterValue.value = '';
        },
        parent() {
            return this.pageDialogBlock;
        },
    })
    pageDialogAddParameter: ui.fields.Button;

    @ui.decorators.separatorField<Dialogs>({
        isFullWidth: true,
        parent() {
            return this.pageDialogBlock;
        },
    })
    pageDialogParametersSeparator: ui.fields.Separator;

    @ui.decorators.tableField<Dialogs>({
        title: 'Page dialog values',
        columns: [
            ui.nestedFields.text({ bind: 'name', title: 'Name' }),
            ui.nestedFields.text({ bind: 'value', title: 'Value' }),
        ],
        dropdownActions: [
            {
                icon: 'cross_circle',
                title: 'Remove',
                onClick(_id: any) {
                    this.pageDialogValues.value = this.pageDialogValues.value.filter(row => row._id !== _id);
                },
            },
        ],
        parent() {
            return this.pageDialogBlock;
        },
    })
    pageDialogValues: ui.fields.Table<QueryParameterTableItem>;

    @ui.decorators.textField<Dialogs>({
        parent() {
            return this.pageDialogBlock;
        },
        title: 'Value property name',
    })
    pageDialogValueName: ui.fields.Text;

    @ui.decorators.textField<Dialogs>({
        parent() {
            return this.pageDialogBlock;
        },
        title: 'Value property value',
    })
    pageDialogValueValue: ui.fields.Text;

    @ui.decorators.buttonField<Dialogs>({
        map() {
            return 'Add value property';
        },
        onClick() {
            const name = this.pageDialogValueName.value || undefined;
            const value = this.pageDialogValueValue.value || undefined;
            this.pageDialogValues.addRecord({ name, value });
            this.pageDialogValueName.value = '';
            this.pageDialogValueValue.value = '';
        },
        parent() {
            return this.pageDialogBlock;
        },
    })
    pageDialogAddValue: ui.fields.Button;

    @ui.decorators.separatorField<Dialogs>({
        isFullWidth: true,
        parent() {
            return this.pageDialogBlock;
        },
    })
    pageDialogValuesSeparator: ui.fields.Separator;

    @ui.decorators.buttonField<Dialogs>({
        map: () => 'Open dialog',
        onError() {},
        async onClick() {
            const values =
                this.pageDialogValues.value?.length === 0
                    ? undefined
                    : (this.pageDialogValues.value || []).reduce<any>(
                          (dictionary, next) => ({ ...dictionary, [next.name]: next.value }),
                          {},
                      );
            const options = {
                fullScreen: this.pageDialogIsFullscreen.value,
                rightAligned: this.pageDialogIsRightAligned.value,
                size: this.pageDialogSize.value as ui.dialogs.DialogSize,
                skipDirtyCheck: this.pageDialogSkipsDirtyCheck.value,
                resolveOnCancel: this.pageDialogResolveWhenClosed.value,
                isMainListDisplayedInDialog: this.pageDialogDisplayMainList.value,
                values,
            };
            const queryParameters = (this.pageDialogQueryParameters.value || []).reduce<any>(
                (dictionary, next) => ({ ...dictionary, [next.name]: next.value }),
                {},
            );
            const result = await this.$.dialog.page(this.pageDialogPath.value, queryParameters, options);
            this.$.showToast(`Dialog result\n:${JSON.stringify(result, null, 4).split('"').join("'")}`, {
                language: 'markdown',
                timeout: 10000,
            });
            ui.console.log(result);
        },
        parent() {
            return this.pageDialogBlock;
        },
    })
    pageDialogButton: ui.fields.Button;

    @ui.decorators.buttonField<Dialogs>({
        map: () => 'Open ShowCaseProduct dialog',
        onError() {},
        async onClick() {
            const values =
                this.pageDialogValues.value?.length === 0
                    ? undefined
                    : (this.pageDialogValues.value || []).reduce<any>(
                          (dictionary, next) => ({ ...dictionary, [next.name]: next.value }),
                          {},
                      );
            const options = {
                fullScreen: this.pageDialogIsFullscreen.value ?? undefined,
                rightAligned: this.pageDialogIsRightAligned.value ?? undefined,
                size: this.pageDialogSize.value as ui.dialogs.DialogSize,
                skipDirtyCheck: this.pageDialogSkipsDirtyCheck.value ?? undefined,
                resolveOnCancel: this.pageDialogResolveWhenClosed.value ?? undefined,
                isMainListDisplayedInDialog: this.pageDialogDisplayMainList.value ?? undefined,
                values,
            };

            const queryParameters = (this.pageDialogQueryParameters.value || []).reduce<any>(
                (dictionary, next) => ({ ...dictionary, [next.name]: next.value }),
                {},
            );

            const result = await this.$.dialog.page('@sage/xtrem-show-case/ShowCaseProduct', queryParameters, options);

            this.$.showToast(`Dialog result\n:${JSON.stringify(result, null, 4).split('"').join("'")}`, {
                language: 'markdown',
                timeout: 10000,
            });
        },
        parent() {
            return this.pageDialogBlock;
        },
    })
    pageDialogShowCaseProductButton: ui.fields.Button;

    @ui.decorators.buttonField<Dialogs>({
        map: () => 'Open ShowCaseEmployee dialog',
        onError() {},
        async onClick() {
            const values =
                this.pageDialogValues.value?.length === 0
                    ? undefined
                    : (this.pageDialogValues.value || []).reduce<any>(
                          (dictionary, next) => ({ ...dictionary, [next.name]: next.value }),
                          {},
                      );
            const options = {
                fullScreen: this.pageDialogIsFullscreen.value ?? undefined,
                rightAligned: this.pageDialogIsRightAligned.value ?? undefined,
                size: this.pageDialogSize.value as ui.dialogs.DialogSize,
                skipDirtyCheck: this.pageDialogSkipsDirtyCheck.value ?? undefined,
                resolveOnCancel: this.pageDialogResolveWhenClosed.value ?? undefined,
                isMainListDisplayedInDialog: this.pageDialogDisplayMainList.value ?? undefined,
                values,
            };

            const queryParameters = (this.pageDialogQueryParameters.value || []).reduce<any>(
                (dictionary, next) => ({ ...dictionary, [next.name]: next.value }),
                {},
            );

            const result = await this.$.dialog.page('@sage/xtrem-show-case/ShowCaseEmployee', queryParameters, options);

            this.$.showToast(`Dialog result\n:${JSON.stringify(result, null, 4).split('"').join("'")}`, {
                language: 'markdown',
                timeout: 10000,
            });
        },
        parent() {
            return this.pageDialogBlock;
        },
    })
    pageDialogShowCaseEmployeeButton: ui.fields.Button;

    @ui.decorators.section<Dialogs>({
        title: 'Async mutation wrapper dialog',
    })
    asyncMutationWrapperDialog: ui.containers.Section;

    @ui.decorators.block<Dialogs>({
        parent() {
            return this.asyncMutationWrapperDialog;
        },
    })
    asyncMutationWrapperBlock: ui.containers.Block;

    @ui.decorators.referenceField<Dialogs>({
        parent() {
            return this.asyncMutationWrapperBlock;
        },
        node: '@sage/xtrem-show-case/ShowCaseCustomer',
        title: 'Customer',
        valueField: 'name',
        isAutoSelectEnabled: true,
    })
    customer: ui.fields.Reference<ShowCaseCustomer>;

    @ui.decorators.textField<Dialogs>({
        parent() {
            return this.asyncMutationWrapperBlock;
        },
        title: 'New name',
    })
    newCustomerName: ui.fields.Text;

    @ui.decorators.buttonField<Dialogs>({
        map: () => 'Run async mutation',
        onError(err) {
            this.$.showToast(`Error: ${err.message}`, { type: 'error' });
        },
        async onClick() {
            const result = await this.$.dialog.asyncLoader(
                '@sage/xtrem-show-case/ShowCaseCustomer',
                'asyncTestMutation',
                {
                    customerId: this.customer.value?._id,
                    newName: this.newCustomerName.value,
                    mockedProcessingTime: this.mockedProcessingTime.value
                        ? Number(this.mockedProcessingTime.value)
                        : 2000,
                    shouldFail: this.shouldOperationFail.value ?? false,
                },
                { name: true, _id: true },
                {
                    isStopAvailable: this.canUserStopAsyncMutation.value ?? false,
                    dialogContent: this.asyncDialogContent.value || undefined,
                    dialogTitle: this.asyncDialogTitle.value || undefined,
                },
            );

            if (result) {
                this.$.showToast(`New customer name is successfully applied: ${result.name}`);
            }
        },
        parent() {
            return this.asyncMutationWrapperBlock;
        },
    })
    asyncMutationButton: ui.fields.Button;

    @ui.decorators.separatorField<Dialogs>({
        parent() {
            return this.asyncMutationWrapperBlock;
        },
        isFullWidth: true,
    })
    asyncSeparator: ui.fields.Separator;

    @ui.decorators.numericField<Dialogs>({
        isReadOnly: false,
        isTransient: true,
        scale: 0,
        parent() {
            return this.asyncMutationWrapperBlock;
        },
        title: 'Mocked processing time (ms)',
    })
    mockedProcessingTime: ui.fields.Numeric;

    @ui.decorators.checkboxField<Dialogs>({
        title: 'Is stop button available?',
        parent() {
            return this.asyncMutationWrapperBlock;
        },
    })
    canUserStopAsyncMutation: ui.fields.Checkbox;

    @ui.decorators.checkboxField<Dialogs>({
        title: 'Should the operation fail?',
        parent() {
            return this.asyncMutationWrapperBlock;
        },
    })
    shouldOperationFail: ui.fields.Checkbox;

    @ui.decorators.textField<Dialogs>({
        isReadOnly: false,
        isTransient: true,
        isFullWidth: true,
        parent() {
            return this.asyncMutationWrapperBlock;
        },
        title: 'Dialog title',
    })
    asyncDialogTitle: ui.fields.Text;

    @ui.decorators.textAreaField<Dialogs>({
        isReadOnly: false,
        isTransient: true,
        isFullWidth: true,
        rows: 5,
        parent() {
            return this.asyncMutationWrapperBlock;
        },
        title: 'Dialog content',
    })
    asyncDialogContent: ui.fields.TextArea;
}
