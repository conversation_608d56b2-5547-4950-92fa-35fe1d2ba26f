import * as ui from '@sage/xtrem-ui';
import { fields } from '../menu-items/fields';

@ui.decorators.page<ToggleButtons>({
    authorizationCode: 'BSCFLDS',
    module: 'show-case',
    title: 'Field - Toggle Buttons',
    category: 'SHOWCASE',
    isTransient: true,
    menuItem: fields,
})
export class ToggleButtons extends ui.Page {
    @ui.decorators.section<ToggleButtons>({
        title: 'Toggle buttons field',
    })
    section: ui.containers.Section;

    @ui.decorators.block<ToggleButtons>({
        parent() {
            return this.section;
        },
        title: 'Field example',
    })
    fieldBlock: ui.containers.Block;

    @ui.decorators.toggleField<ToggleButtons>({
        title: 'Toggle group',
        parent() {
            return this.fieldBlock;
        },
        helperText: 'helper text',
        onChange() {
            if (this.field.value !== this.value.value) {
                this.value.value = this.field.value;
                this.changeTriggered.isHidden = false;
                setTimeout(() => {
                    this.changeTriggered.isHidden = true;
                }, 5000);
            }
        },
        isFullWidth: true,
        size: 'large',
        optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
    })
    field: ui.fields.Toggle;

    @ui.decorators.labelField<ToggleButtons>({
        parent() {
            return this.fieldBlock;
        },
        isHidden: true,
        map() {
            return 'Changed was triggered';
        },
    })
    changeTriggered: ui.fields.Label;

    @ui.decorators.block<ToggleButtons>({
        parent() {
            return this.section;
        },
        title: 'Configuration',
    })
    configurationBlock: ui.containers.Block;

    @ui.decorators.textField<ToggleButtons>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Helper text',
        onChange() {
            this.field.helperText = this.helperText.value;
        },
    })
    helperText: ui.fields.Text;

    @ui.decorators.checkboxField<ToggleButtons>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is disabled',
        onChange() {
            this.field.isDisabled = this.isDisabled.value;
        },
    })
    isDisabled: ui.fields.Checkbox;

    @ui.decorators.checkboxField<ToggleButtons>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is helper text hidden',
        onChange() {
            this.field.isHelperTextHidden = this.isHelperTextHidden.value;
        },
    })
    isHelperTextHidden: ui.fields.Checkbox;

    @ui.decorators.checkboxField<ToggleButtons>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is hidden',
        onChange() {
            this.field.isHidden = this.isHidden.value;
        },
    })
    isHidden: ui.fields.Checkbox;

    @ui.decorators.checkboxField<ToggleButtons>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is readOnly',
        onChange() {
            this.field.isReadOnly = this.isReadOnly.value;
        },
    })
    isReadOnly: ui.fields.Checkbox;

    @ui.decorators.checkboxField<ToggleButtons>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is title hidden',
        onChange() {
            this.field.isTitleHidden = this.isTitleHidden.value;
        },
    })
    isTitleHidden: ui.fields.Checkbox;

    @ui.decorators.textField<ToggleButtons>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Title',
        onChange() {
            this.field.title = this.title.value;
        },
    })
    title: ui.fields.Text;

    @ui.decorators.toggleField<ToggleButtons>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Value',
        onChange() {
            if (this.value.value !== this.field.value) {
                this.field.value = this.value.value;
            }
        },
        optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
        isFullWidth: true,
    })
    value: ui.fields.Toggle;

    @ui.decorators.buttonField<ToggleButtons>({
        parent() {
            return this.configurationBlock;
        },
        map() {
            return 'Focus field';
        },
        onClick() {
            this.field.focus();
        },
    })
    focus: ui.fields.Button;

    /* Additional examples */

    @ui.decorators.block<ToggleButtons>({
        parent() {
            return this.section;
        },
        title: 'Additional examples',
    })
    additionalBlock: ui.containers.Block;

    @ui.decorators.toggleField<ToggleButtons>({
        parent() {
            return this.additionalBlock;
        },
        title: 'Options set in file',
        isMandatory: true,
        helperText: 'The options are not from an enum',
        options: ['dog', 'cat', 'cow', 'a very long option, just to test line breaks', 'giraffe'],
        isFullWidth: true,
    })
    inlineOption: ui.fields.Toggle;

    @ui.decorators.toggleField<ToggleButtons>({
        parent() {
            return this.additionalBlock;
        },
        title: 'Mandatory',
        isMandatory: true,
        optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
        isFullWidth: true,
        mapIcon(value) {
            if (value === 'great') {
                return 'message';
            }
            return 'warning';
        },
    })
    mandatory: ui.fields.Toggle;
}
