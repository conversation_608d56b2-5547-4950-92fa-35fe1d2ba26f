import { GraphA<PERSON>, ShowCaseCountry as ShowCaseCountryNode } from '@sage/xtrem-show-case-api';
import {
    setApplicativePageCrudActions,
    setOrderOfPageHeaderDropDownActions,
} from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import * as ui from '@sage/xtrem-ui';
import { applicationPages } from '../menu-items/application-pages';

@ui.decorators.page<ShowCaseCountry, ShowCaseCountryNode>({
    menuItem: applicationPages,
    createAction() {
        return this.$standardNewAction;
    },
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.text({ bind: 'name' }),
            titleRight: ui.nestedFields.text({ bind: 'code' }),
        },
    },
    businessActions() {
        return [this.$standardCancelAction, this.$standardSaveAction];
    },
    headerQuickActions() {
        return [this.$standardDuplicateAction];
    },
    headerDropDownActions() {
        return setOrderOfPageHeaderDropDownActions({
            remove: [this.$standardDeleteAction],
            dropDownBusinessActions: [this.$standardOpenRecordHistoryAction],
        });
    },
    node: '@sage/xtrem-show-case/ShowCaseCountry',
    title: 'ShowCase - Country',
    objectTypeSingular: 'Country',
    objectTypePlural: 'Countries',
    idField() {
        return this.code;
    },
    priority: 300,
    onLoad() {
        setApplicativePageCrudActions({
            page: this,
            isDirty: false,
            save: this.$standardSaveAction,
            cancel: this.$standardCancelAction,
            remove: this.$standardDeleteAction,
            actions: [],
        });
    },
    onDirtyStateUpdated(isDirty: boolean) {
        setApplicativePageCrudActions({
            page: this,
            isDirty,
            save: this.$standardSaveAction,
            cancel: this.$standardCancelAction,
            remove: this.$standardDeleteAction,
            actions: [],
        });
    },
})
export class ShowCaseCountry extends ui.Page<GraphApi> {
    @ui.decorators.section<ShowCaseCountry>({})
    section: ui.containers.Section;

    @ui.decorators.block<ShowCaseCountry>({
        parent() {
            return this.section;
        },
    })
    block: ui.containers.Block;

    @ui.decorators.textField<ShowCaseCountry>({
        parent() {
            return this.block;
        },
        title: 'Record ID',
        isReadOnly: true,
    })
    _id: ui.fields.Text;

    @ui.decorators.textField<ShowCaseCountry>({
        parent() {
            return this.block;
        },
        title: 'Name',
    })
    name: ui.fields.Text;

    @ui.decorators.textField<ShowCaseCountry>({
        parent() {
            return this.block;
        },
        title: 'Country Code',
    })
    code: ui.fields.Text;

    @ui.decorators.numericField<ShowCaseCountry>({
        parent() {
            return this.block;
        },
        title: 'Phone Country Code',
        prefix: '+',
    })
    phoneCountryCode: ui.fields.Numeric;

    @ui.decorators.tableField<ShowCaseCountry>({
        parent() {
            return this.block;
        },
        title: 'Employees In Country',
        node: '@sage/xtrem-show-case/ShowCaseEmployee',
        columns: [
            ui.nestedFields.text({
                title: 'Name',
                bind: 'firstName',
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                title: 'Last Name',
                bind: 'lastName',
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                title: 'Email',
                bind: 'email',
                isReadOnly: true,
            }),
        ],
    })
    employeesInCountry: ui.fields.Table;

    @ui.decorators.podCollectionField<ShowCaseCountry>({
        parent() {
            return this.block;
        },
        bind: 'employeesInCountry',
        title: 'Employees In Country (Pod collection)',
        node: '@sage/xtrem-show-case/ShowCaseEmployee',
        columns: [
            ui.nestedFields.text({
                title: 'Name',
                bind: 'firstName',
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                title: 'Last Name',
                bind: 'lastName',
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                title: 'Email',
                bind: 'email',
                isReadOnly: true,
            }),
        ],
    })
    podEmployees: ui.fields.PodCollection;
}
