import { <PERSON><PERSON>h<PERSON><PERSON>, Show<PERSON><PERSON><PERSON>rovider, ShowCaseProduct, ShowCaseProviderAddress } from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import { nestedPod } from '../menu-items/nested-pod';

@ui.decorators.page<NestedVitalPodBlock, ShowCaseProduct>({
    authorizationCode: 'SHCPRVD',
    category: 'SHOWCASE',
    menuItem: nestedPod,
    module: 'show-case',
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.text({ bind: 'product' }),
            titleRight: ui.nestedFields.text({ bind: '_id' }),
            line2: ui.nestedFields.text({ bind: 'description', canFilter: false }),
        },
    },
    node: '@sage/xtrem-show-case/ShowCaseProduct',
    title: 'Vital Pod Block with Nested Reference Arrays',
})
export class NestedVitalPodBlock extends ui.Page<GraphApi> {
    @ui.decorators.section<NestedVitalPodBlock>({})
    mainSection: ui.containers.Section;

    @ui.decorators.vitalPodField<NestedVitalPodBlock, ShowCaseProvider>({
        title: 'Provider',
        width: 'large',
        placeholder: 'Lorem ipsum dolor sit amet',
        addButtonText: 'Lorem Ipsum',
        parent() {
            return this.mainSection;
        },
        canRemove: true,
        columns: [
            ui.nestedFields.text({ bind: 'textField', title: 'Text field' }),
            ui.nestedFields.text({ bind: 'integerField', title: 'Numeric field' }),
            ui.nestedFields.text({ bind: '_id', title: 'id' }),
            ui.nestedFields.text({ bind: 'dateField', title: 'date field' }),
            ui.nestedFields.multiReference<NestedVitalPodBlock, ShowCaseProvider, ShowCaseProviderAddress>({
                bind: 'addresses',
                node: '@sage/xtrem-show-case/ShowCaseProviderAddress',
                minLookupCharacters: 0,
                valueField: 'name',
                title: 'Addresses',
                isFullWidth: true,
            }),
            ui.nestedFields.multiDropdown<NestedVitalPodBlock, ShowCaseProvider>({
                bind: 'ratings',
                isFullWidth: true,
                optionType: '@sage/xtrem-show-case/ShowCaseProviderRating',
                title: 'Ratings',
            }),
        ],
        headerLabel: ui.nestedFields.label({ bind: 'textField', isTitleHidden: true }),
        node: '@sage/xtrem-show-case/ShowCaseProvider',
    })
    provider: ui.fields.VitalPod;
}
