import { Graph<PERSON><PERSON>, ShowCaseProvider } from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import { misc } from '../menu-items/misc';

@ui.decorators.page<CrudUpdateFile, ShowCaseProvider>({
    authorizationCode: 'SHCPRDT',
    module: 'show-case',
    title: 'CRUD - Update',
    node: '@sage/xtrem-show-case/ShowCaseProvider',
    onLoad() {
        this.saveCrudUpdate.isDisabled = !this.$.queryParameters._id;
    },
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.text({ bind: 'textField' }),
            titleRight: ui.nestedFields.text({ bind: '_id' }),
            image: ui.nestedFields.image({ bind: 'logo' }),
        },
    },
    businessActions() {
        return [this.saveCrudUpdate];
    },
    menuItem: misc,
})
export class CrudUpdateFile extends ui.Page<GraphApi> {
    @ui.decorators.section<CrudUpdateFile>({})
    section: ui.containers.Section;

    @ui.decorators.block<CrudUpdateFile>({
        parent() {
            return this.section;
        },
    })
    block: ui.containers.Block;

    @ui.decorators.textField<CrudUpdateFile>({
        parent() {
            return this.block;
        },
        title: 'Provider',
    })
    textField: ui.fields.Text;

    @ui.decorators.textField<CrudUpdateFile>({
        parent() {
            return this.block;
        },
        title: 'Some decimal info',
    })
    decimalField: ui.fields.Text;

    @ui.decorators.fileField<CrudUpdateFile>({
        parent() {
            return this.block;
        },
        title: 'Logo File',
        text: 'logo-file',
        fileTypes: 'image/*',
    })
    logo: ui.fields.File;

    @ui.decorators.pageAction<CrudUpdateFile>({
        title: 'Save',
        onClick() {
            this.$.graph.update();
        },
    })
    saveCrudUpdate: ui.PageAction;
}
