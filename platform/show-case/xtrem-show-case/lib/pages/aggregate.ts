import { <PERSON>raph<PERSON><PERSON>, ShowCaseProduct, ShowCaseProvider } from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import { fields } from '../menu-items/fields';

@ui.decorators.page<Aggregate, ShowCaseProvider>({
    authorizationCode: 'SHCPRVD',
    category: 'SHOWCASE',
    menuItem: fields,
    businessActions() {
        return [this.update];
    },
    module: 'show-case',
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.text({ bind: 'textField', title: 'Provider' }),
            line2: ui.nestedFields.text({ bind: '_id', title: 'ID' }),
            titleRight: ui.nestedFields.aggregate<Aggregate>({
                title: 'Max product list price',
                bind: 'products',
                aggregateOn: 'listPrice',
                aggregationMethod: 'max',
                scale: 0,
            }),
        },
    },
    node: '@sage/xtrem-show-case/ShowCaseProvider',
    title: 'Field - Aggregate',
})
export class Aggregate extends ui.Page<GraphApi> {
    @ui.decorators.section<Aggregate>({})
    section: ui.containers.Section;

    @ui.decorators.block<Aggregate>({
        parent() {
            return this.section;
        },
    })
    block: ui.containers.Block;

    @ui.decorators.textField<Aggregate>({
        parent() {
            return this.block;
        },
        title: 'Id',
        isReadOnly: true,
    })
    _id: ui.fields.Text;

    @ui.decorators.textField<Aggregate>({
        parent() {
            return this.block;
        },
        title: 'Provider Name',
    })
    textField: ui.fields.Text;

    @ui.decorators.block<Aggregate>({
        title: 'Average list price of hot products',
        parent() {
            return this.section;
        },
    })
    aggregateBlock1: ui.containers.Block;

    @ui.decorators.aggregateField<Aggregate, ShowCaseProduct>({
        bind: 'products',
        aggregateOn: 'listPrice',
        aggregationMethod: 'avg',
        scale: 2,
        filter: {
            hotProduct: { _eq: true },
        },
        parent() {
            return this.aggregateBlock1;
        },
        title: 'The aggregated value',
        helperText: 'This field aggregated on a filtered result set.',
        onClick() {
            this.fieldClicked1.isHidden = false;
            setTimeout(() => {
                this.fieldClicked1.isHidden = true;
            }, 3000);
        },
    })
    field1: ui.fields.Aggregate;

    @ui.decorators.labelField<Aggregate>({
        parent() {
            return this.aggregateBlock1;
        },
        isTransient: true,
        isHidden: true,
        map() {
            return 'CLICKED!!!';
        },
    })
    fieldClicked1: ui.fields.Label;

    @ui.decorators.separatorField<Aggregate>({
        parent() {
            return this.aggregateBlock1;
        },
        isFullWidth: true,
    })
    fieldSeparator1: ui.fields.Separator;

    @ui.decorators.switchField<Aggregate>({
        isTransient: true,
        parent() {
            return this.aggregateBlock1;
        },
        title: 'Is Disabled?',
        onChange() {
            this.field1.isDisabled = !!this.isFieldDisabled1.value;
        },
    })
    isFieldDisabled1: ui.fields.Switch;

    @ui.decorators.textField<Aggregate>({
        isTransient: true,
        parent() {
            return this.aggregateBlock1;
        },
        title: 'Prefix',
        onChange() {
            this.field1.prefix = this.fieldPrefix1.value || '';
        },
    })
    fieldPrefix1: ui.fields.Text;

    @ui.decorators.textField<Aggregate>({
        isTransient: true,
        parent() {
            return this.aggregateBlock1;
        },
        title: 'Postfix',
        onChange() {
            this.field1.postfix = this.fieldPostfix1.value || '';
        },
    })
    fieldPostfix1: ui.fields.Text;

    @ui.decorators.dropdownListField<Aggregate>({
        parent() {
            return this.aggregateBlock1;
        },
        title: 'Unit',
        helperText: 'Automatically sets the prefix or postfix and the scale.',
        isFullWidth: true,
        options: ['EUR', 'GBP', 'USD', 'CHF', 'HUF'],
        isTransient: true,
        onChange() {
            switch (this.unit.value) {
                case 'EUR':
                    this.field1.unit = { decimalDigits: 2, id: 'EUR', symbol: '€' };
                    break;
                case 'GBP':
                    this.field1.unit = { decimalDigits: 2, id: 'GBP', symbol: '£' };
                    break;
                case 'USD':
                    this.field1.unit = { decimalDigits: 2, id: 'USD', symbol: '$' };
                    break;
                case 'CHF':
                    this.field1.unit = { decimalDigits: 2, id: 'CHF', symbol: 'Fr' };
                    break;
                case 'HUF':
                    this.field1.unit = { decimalDigits: 0, id: 'HUF', symbol: 'Ft' };
                    break;
                default:
                    this.field1.unit = null;
            }
        },
    })
    unit: ui.fields.DropdownList;

    @ui.decorators.numericField<Aggregate>({
        isTransient: true,
        scale: 0,
        parent() {
            return this.aggregateBlock1;
        },
        title: 'Scale',
        onChange() {
            this.field1.scale = this.fieldScale1.value || 0;
        },
    })
    fieldScale1: ui.fields.Numeric;

    @ui.decorators.block<Aggregate>({
        title: 'Distinct product count',
        parent() {
            return this.section;
        },
    })
    aggregateBlock2: ui.containers.Block;

    @ui.decorators.aggregateField<Aggregate, ShowCaseProduct>({
        bind: 'products',
        aggregateOn: '_id',
        aggregationMethod: 'distinctCount',
        parent() {
            return this.aggregateBlock2;
        },
        title: 'The aggregated value',
        helperText: 'This field aggregated on a filtered result set.',
        onClick() {
            this.fieldClicked2.isHidden = false;
            setTimeout(() => {
                this.fieldClicked2.isHidden = true;
            }, 3000);
        },
    })
    field2: ui.fields.Aggregate;

    @ui.decorators.labelField<Aggregate>({
        parent() {
            return this.aggregateBlock2;
        },
        isTransient: true,
        isHidden: true,
        map() {
            return 'CLICKED!!!';
        },
    })
    fieldClicked2: ui.fields.Label;

    @ui.decorators.separatorField<Aggregate>({
        parent() {
            return this.aggregateBlock2;
        },
        isFullWidth: true,
    })
    fieldSeparator2: ui.fields.Separator;

    @ui.decorators.switchField<Aggregate>({
        isTransient: true,
        parent() {
            return this.aggregateBlock2;
        },
        title: 'Is Disabled?',
        onChange() {
            this.field2.isDisabled = !!this.isFieldDisabled2.value;
        },
    })
    isFieldDisabled2: ui.fields.Switch;

    @ui.decorators.textField<Aggregate>({
        isTransient: true,
        parent() {
            return this.aggregateBlock2;
        },
        title: 'Prefix',
        onChange() {
            this.field2.prefix = this.fieldPrefix2.value || '';
        },
    })
    fieldPrefix2: ui.fields.Text;

    @ui.decorators.textField<Aggregate>({
        isTransient: true,
        parent() {
            return this.aggregateBlock2;
        },
        title: 'Postfix',
        onChange() {
            this.field2.postfix = this.fieldPostfix2.value || '';
        },
    })
    fieldPostfix2: ui.fields.Text;

    @ui.decorators.numericField<Aggregate>({
        isTransient: true,
        scale: 0,
        parent() {
            return this.aggregateBlock2;
        },
        title: 'Scale',
        onChange() {
            this.field2.scale = this.fieldScale2.value || 0;
        },
    })
    fieldScale2: ui.fields.Numeric;

    @ui.decorators.block<Aggregate>({
        title: 'Total stock count of product provider',
        parent() {
            return this.section;
        },
    })
    aggregateBlock3: ui.containers.Block;

    @ui.decorators.aggregateField<Aggregate, ShowCaseProduct>({
        bind: 'products',
        aggregateOn: 'qty',
        aggregationMethod: 'sum',
        scale: 0,
        parent() {
            return this.aggregateBlock3;
        },
        title: 'The aggregated value',
        helperText: 'This field aggregated on a filtered result set.',
        onClick() {
            this.fieldClicked3.isHidden = false;
            setTimeout(() => {
                this.fieldClicked3.isHidden = true;
            }, 3000);
        },
    })
    field3: ui.fields.Aggregate;

    @ui.decorators.labelField<Aggregate>({
        parent() {
            return this.aggregateBlock3;
        },
        isTransient: true,
        isHidden: true,
        map() {
            return 'CLICKED!!!';
        },
    })
    fieldClicked3: ui.fields.Label;

    @ui.decorators.separatorField<Aggregate>({
        parent() {
            return this.aggregateBlock3;
        },
        isFullWidth: true,
    })
    fieldSeparator3: ui.fields.Separator;

    @ui.decorators.switchField<Aggregate>({
        isTransient: true,
        parent() {
            return this.aggregateBlock3;
        },
        title: 'Is Disabled?',
        onChange() {
            this.field3.isDisabled = !!this.isFieldDisabled3.value;
        },
    })
    isFieldDisabled3: ui.fields.Switch;

    @ui.decorators.textField<Aggregate>({
        isTransient: true,
        parent() {
            return this.aggregateBlock3;
        },
        title: 'Prefix',
        onChange() {
            this.field3.prefix = this.fieldPrefix3.value || '';
        },
    })
    fieldPrefix3: ui.fields.Text;

    @ui.decorators.textField<Aggregate>({
        isTransient: true,
        parent() {
            return this.aggregateBlock3;
        },
        title: 'Postfix',
        onChange() {
            this.field3.postfix = this.fieldPostfix3.value || '';
        },
    })
    fieldPostfix3: ui.fields.Text;

    @ui.decorators.numericField<Aggregate>({
        isTransient: true,
        scale: 0,
        parent() {
            return this.aggregateBlock3;
        },
        title: 'Scale',
        onChange() {
            this.field3.scale = this.fieldScale3.value || 0;
        },
    })
    fieldScale3: ui.fields.Numeric;

    @ui.decorators.block<Aggregate>({
        title: 'Maximum product list price',
        parent() {
            return this.section;
        },
    })
    aggregateBlock4: ui.containers.Block;

    @ui.decorators.aggregateField<Aggregate, ShowCaseProduct>({
        bind: 'products',
        aggregateOn: 'listPrice',
        aggregationMethod: 'max',
        parent() {
            return this.aggregateBlock4;
        },
        title: 'The aggregated value',
        helperText: 'This field aggregated on a filtered result set.',
        onClick() {
            this.fieldClicked4.isHidden = false;
            setTimeout(() => {
                this.fieldClicked4.isHidden = true;
            }, 4000);
        },
    })
    field4: ui.fields.Aggregate;

    @ui.decorators.labelField<Aggregate>({
        parent() {
            return this.aggregateBlock4;
        },
        isTransient: true,
        isHidden: true,
        map() {
            return 'CLICKED!!!';
        },
    })
    fieldClicked4: ui.fields.Label;

    @ui.decorators.separatorField<Aggregate>({
        parent() {
            return this.aggregateBlock4;
        },
        isFullWidth: true,
    })
    fieldSeparator4: ui.fields.Separator;

    @ui.decorators.switchField<Aggregate>({
        isTransient: true,
        parent() {
            return this.aggregateBlock4;
        },
        title: 'Is Disabled?',
        onChange() {
            this.field4.isDisabled = !!this.isFieldDisabled4.value;
        },
    })
    isFieldDisabled4: ui.fields.Switch;

    @ui.decorators.textField<Aggregate>({
        isTransient: true,
        parent() {
            return this.aggregateBlock4;
        },
        title: 'Prefix',
        onChange() {
            this.field4.prefix = this.fieldPrefix4.value || '';
        },
    })
    fieldPrefix4: ui.fields.Text;

    @ui.decorators.textField<Aggregate>({
        isTransient: true,
        parent() {
            return this.aggregateBlock4;
        },
        title: 'Postfix',
        onChange() {
            this.field4.postfix = this.fieldPostfix4.value || '';
        },
    })
    fieldPostfix4: ui.fields.Text;

    @ui.decorators.numericField<Aggregate>({
        isTransient: true,
        scale: 0,
        parent() {
            return this.aggregateBlock4;
        },
        title: 'Scale',
        onChange() {
            this.field4.scale = this.fieldScale4.value || 0;
        },
    })
    fieldScale4: ui.fields.Numeric;

    @ui.decorators.block<Aggregate>({
        title: 'Minimum product list price',
        parent() {
            return this.section;
        },
    })
    aggregateBlock5: ui.containers.Block;

    @ui.decorators.aggregateField<Aggregate, ShowCaseProduct>({
        bind: 'products',
        aggregateOn: 'listPrice',
        aggregationMethod: 'min',
        scale: 5,
        parent() {
            return this.aggregateBlock5;
        },
        title: 'The aggregated value',
        helperText: 'This field aggregated on a filtered result set.',
        onClick() {
            this.fieldClicked5.isHidden = false;
            setTimeout(() => {
                this.fieldClicked5.isHidden = true;
            }, 5000);
        },
    })
    field5: ui.fields.Aggregate;

    @ui.decorators.labelField<Aggregate>({
        parent() {
            return this.aggregateBlock5;
        },
        isTransient: true,
        isHidden: true,
        map() {
            return 'CLICKED!!!';
        },
    })
    fieldClicked5: ui.fields.Label;

    @ui.decorators.separatorField<Aggregate>({
        parent() {
            return this.aggregateBlock5;
        },
        isFullWidth: true,
    })
    fieldSeparator5: ui.fields.Separator;

    @ui.decorators.switchField<Aggregate>({
        isTransient: true,
        parent() {
            return this.aggregateBlock5;
        },
        title: 'Is Disabled?',
        onChange() {
            this.field5.isDisabled = !!this.isFieldDisabled5.value;
        },
    })
    isFieldDisabled5: ui.fields.Switch;

    @ui.decorators.textField<Aggregate>({
        isTransient: true,
        parent() {
            return this.aggregateBlock5;
        },
        title: 'Prefix',
        onChange() {
            this.field5.prefix = this.fieldPrefix5.value || '';
        },
    })
    fieldPrefix5: ui.fields.Text;

    @ui.decorators.textField<Aggregate>({
        isTransient: true,
        parent() {
            return this.aggregateBlock5;
        },
        title: 'Postfix',
        onChange() {
            this.field5.postfix = this.fieldPostfix5.value || '';
        },
    })
    fieldPostfix5: ui.fields.Text;

    @ui.decorators.numericField<Aggregate>({
        isTransient: true,
        scale: 0,
        parent() {
            return this.aggregateBlock5;
        },
        title: 'Scale',
        onChange() {
            this.field5.scale = this.fieldScale5.value || 0;
        },
    })
    fieldScale5: ui.fields.Numeric;

    @ui.decorators.block<Aggregate>({
        title: 'Aggregation on a child property (product provider)',
        parent() {
            return this.section;
        },
    })
    aggregateBlock6: ui.containers.Block;

    @ui.decorators.aggregateField<Aggregate, ShowCaseProduct>({
        bind: 'products',
        aggregateOn: {
            provider: {
                textField: true,
            },
        },
        aggregationMethod: 'min',
        parent() {
            return this.aggregateBlock6;
        },
        title: "Firs product of this product's (alphabetic order) provider",
        helperText: 'This field aggregated on a filtered result set.',
        onClick() {
            this.fieldClicked5.isHidden = false;
            setTimeout(() => {
                this.fieldClicked5.isHidden = true;
            }, 5000);
        },
    })
    field6: ui.fields.Aggregate;

    @ui.decorators.pageAction<Aggregate>({
        title: 'Save',
        async onClick() {
            await this.$.graph.update();
            this.$.dialog.message('info', 'Mutation Update', `Updated entry: ${this._id.value}`, {
                fullScreen: false,
                rightAligned: false,
                acceptButton: {
                    isDisabled: false,
                    isHidden: false,
                    text: 'ok',
                },
            });
        },
    })
    update: ui.PageAction;
}
