import { GraphApi, ShowCaseProduct } from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import { fields } from '../menu-items/fields';

@ui.decorators.page<MultiFileDepositField, ShowCaseProduct>({
    title: 'Multi file deposit',
    node: '@sage/xtrem-show-case/ShowCaseProduct',
    defaultEntry: () => '1',
    mode: 'tabs',
    onLoad() {},
    menuItem: fields,
})
export class MultiFileDepositField extends ui.Page<GraphApi> {
    @ui.decorators.section<MultiFileDepositField>({
        isTitleHidden: true,
    })
    boundSection: ui.containers.Section;

    @ui.decorators.block<MultiFileDepositField>({
        isTitleHidden: true,
        parent() {
            return this.boundSection;
        },
    })
    fieldBlock: ui.containers.Block;

    @ui.decorators.multiFileDepositField<MultiFileDepositField>({
        parent() {
            return this.fieldBlock;
        },
        node: '@sage/xtrem-upload/AttachmentAssociation',
        attachmentNode: '@sage/xtrem-upload/UploadedFile',
        bind: '_attachments',
        kind: 'attachment',
    })
    field: ui.fields.MultiFileDeposit;

    @ui.decorators.labelField<MultiFileDepositField>({
        isTransient: true,
        parent() {
            return this.fieldBlock;
        },
        isHidden: true,
        map() {
            return 'Click was triggered';
        },
    })
    clickTriggered: ui.fields.Label;

    @ui.decorators.block<MultiFileDepositField>({
        parent() {
            return this.boundSection;
        },
        title: 'Configuration',
    })
    configurationNonTransientBlock: ui.containers.Block;

    @ui.decorators.checkboxField<MultiFileDepositField>({
        isTransient: true,
        parent() {
            return this.configurationNonTransientBlock;
        },
        title: 'Is disabled',
        onChange() {
            this.field.isDisabled = this.isDisabledNonTransient.value;
        },
    })
    isDisabledNonTransient: ui.fields.Checkbox;

    @ui.decorators.checkboxField<MultiFileDepositField>({
        isTransient: true,
        parent() {
            return this.configurationNonTransientBlock;
        },
        title: 'Is helper text hidden',
        onChange() {
            this.field.isHelperTextHidden = this.isHelperTextHiddenNonTransient.value;
        },
    })
    isHelperTextHiddenNonTransient: ui.fields.Checkbox;

    @ui.decorators.checkboxField<MultiFileDepositField>({
        isTransient: true,
        parent() {
            return this.configurationNonTransientBlock;
        },
        title: 'Is hidden',
        onChange() {
            this.field.isHidden = this.isHiddenNonTransient.value;
        },
    })
    isHiddenNonTransient: ui.fields.Checkbox;

    @ui.decorators.checkboxField<MultiFileDepositField>({
        isTransient: true,
        parent() {
            return this.configurationNonTransientBlock;
        },
        title: 'Is title hidden',
        onChange() {
            this.field.isTitleHidden = this.isTitleHiddenNonTransient.value;
        },
    })
    isTitleHiddenNonTransient: ui.fields.Checkbox;

    @ui.decorators.textField<MultiFileDepositField>({
        isTransient: true,
        parent() {
            return this.configurationNonTransientBlock;
        },
        title: 'Title',
        onChange() {
            this.field.title = this.titleNonTransient.value;
        },
    })
    titleNonTransient: ui.fields.Text;

    @ui.decorators.textField<MultiFileDepositField>({
        isTransient: true,
        parent() {
            return this.configurationNonTransientBlock;
        },
        title: 'Helper text',
        onChange() {
            this.field.helperText = this.helperTextNonTransient.value;
        },
    })
    helperTextNonTransient: ui.fields.Text;
}
