import { Graph<PERSON><PERSON>, ShowCaseProduct, ShowCaseProvider } from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import { referenceField } from '../menu-items/reference-field';

@ui.decorators.page<ReferenceNestedLabel>({
    authorizationCode: 'REFNSTLBL',
    menuItem: referenceField,
    category: 'SHOWCASE',
    isTransient: true,
    module: 'show-case',
    title: 'Reference with Nested Label',
})
export class ReferenceNestedLabel extends ui.Page<GraphApi> {
    @ui.decorators.section<ReferenceNestedLabel>({})
    section: ui.containers.Section;

    @ui.decorators.block<ReferenceNestedLabel>({
        parent() {
            return this.section;
        },
    })
    block: ui.containers.Block;

    @ui.decorators.referenceField<ReferenceNestedLabel, ShowCaseProduct>({
        columns: [
            ui.nestedFields.text({ bind: '_id', canFilter: false, title: 'ID' }),
            ui.nestedFields.text({ bind: 'product', canFilter: true, title: 'Product' }),
            ui.nestedFields.reference<ReferenceNestedLabel, ShowCaseProduct, ShowCaseProvider>({
                bind: 'provider',
                canFilter: true,
                node: '@sage/xtrem-show-case/ShowCaseProvider',
                title: 'Provider',
                valueField: 'textField',
            }),
            ui.nestedFields.label({ bind: 'netPrice', canFilter: true }),
        ],
        isFullWidth: true,
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        shouldSuggestionsIncludeColumns: true,
        title: 'Product',
        valueField: 'product',
        parent() {
            return this.block;
        },
        tunnelPage: null,
    })
    field: ui.fields.Reference;
}
