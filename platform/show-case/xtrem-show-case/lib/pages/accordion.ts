import * as ui from '@sage/xtrem-ui';
import { ShowCaseProvider } from '@sage/xtrem-show-case-api';
import { containers } from '../menu-items/containers';

@ui.decorators.page<Accordion, ShowCaseProvider>({
    authorizationCode: 'HLPRPNL',
    category: 'SHOWCASE',
    businessActions() {
        return [this.action1, this.action2, this.action3, this.action4];
    },
    detailPanel() {
        return {
            footerActions: [this.action1, this.action2, this.action3, this.action4],
            header: this.detailPanelHeaderSection,
            sections: [this.detailPanelBodySection1, this.detailPanelBodySection2],
        };
    },
    module: 'show-case',
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.text({ bind: 'textField' }),
            line2: ui.nestedFields.text({ bind: '_id' }),
        },
    },
    node: '@sage/xtrem-show-case/ShowCaseProvider',
    title: 'Section - Accordion mode',
    mode: 'tabs',
    menuItem: containers,
})
export class Accordion extends ui.Page {
    @ui.decorators.section<Accordion>({
        title: 'Section with accordions',
        mode: 'accordion',
    })
    section1: ui.containers.Section;

    @ui.decorators.block<Accordion>({
        parent() {
            return this.section1;
        },
        title: 'Block 1',
    })
    block1: ui.containers.Block;

    @ui.decorators.textField<Accordion>({
        parent() {
            return this.block1;
        },
        title: 'Identifier',
    })
    _id: ui.fields.Text;

    @ui.decorators.dateField<Accordion>({
        parent() {
            return this.block1;
        },
        title: 'Date',
    })
    dateField: ui.fields.Date;

    @ui.decorators.textField<Accordion>({
        parent() {
            return this.block1;
        },
        isTransient: true,
        title: 'Action 3 isHidden',
    })
    action3IsHidden: ui.fields.Text;

    @ui.decorators.textField<Accordion>({
        parent() {
            return this.block1;
        },
        title: 'Action 4 isHidden',
        isTransient: true,
    })
    action4IsHidden: ui.fields.Text;

    @ui.decorators.textField<Accordion>({
        parent() {
            return this.block1;
        },
        title: 'Close button hidden',
        isTransient: true,
    })
    closeButtonHidden: ui.fields.Text;

    @ui.decorators.textField<Accordion>({
        parent() {
            return this.block1;
        },
        isTransient: true,
        title: 'Header section hidden',
    })
    headerSectionHidden: ui.fields.Text;

    @ui.decorators.textField<Accordion>({
        parent() {
            return this.block1;
        },
        title: 'First detail panel section hidden',
        isTransient: true,
    })
    firstTabSectionHidden: ui.fields.Text;

    @ui.decorators.textField<Accordion>({
        parent() {
            return this.block1;
        },
        title: 'Second detail panel section hidden',
        isTransient: true,
    })
    secondTabSectionHidden: ui.fields.Text;

    @ui.decorators.switchField<Accordion>({
        parent() {
            return this.block1;
        },
        title: 'Is block 2 hidden?',
        onChange() {
            this.block2.isHidden = this.block2HiddenSwitch.value;
        },
        isTransient: true,
    })
    block2HiddenSwitch: ui.fields.Switch;

    @ui.decorators.section<Accordion>({
        title: 'Another section',
    })
    section2: ui.containers.Section;

    @ui.decorators.block<Accordion>({
        parent() {
            return this.section1;
        },
        title: 'Block 2',
    })
    block2: ui.containers.Block;

    @ui.decorators.textField<Accordion>({
        parent() {
            return this.block2;
        },
        width: 'medium',
        isTransient: true,
        title: 'A medium sized field',
    })
    text11: ui.fields.Text;

    @ui.decorators.textField<Accordion>({
        parent() {
            return this.block2;
        },
        width: 'medium',
        isTransient: true,
        title: 'Another medium sized field',
    })
    text12: ui.fields.Text;

    @ui.decorators.textField<Accordion>({
        parent() {
            return this.block2;
        },
        width: 'small',
        isTransient: true,
        title: 'Small field',
    })
    text13: ui.fields.Text;

    @ui.decorators.block<Accordion>({
        parent() {
            return this.section1;
        },
        title: 'Block with pod collection',
    })
    block3: ui.containers.Block;

    @ui.decorators.podCollectionField<Accordion>({
        parent() {
            return this.block3;
        },
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        recordWidth: 'large',
        columns: [
            ui.nestedFields.label({
                bind: 'description',
                backgroundColor: ui.tokens.colorsYang100,
                borderColor: ui.tokens.colorsYang100,
                isTitleHidden: true,
            }),
            ui.nestedFields.text({ bind: 'description' }),
            ui.nestedFields.select({ bind: 'category' }),
        ],
    })
    products: ui.fields.PodCollection;

    @ui.decorators.block<Accordion>({
        parent() {
            return this.section2;
        },
        title: 'Just another random block',
    })
    block4: ui.containers.Block;

    @ui.decorators.textField<Accordion>({
        parent() {
            return this.block4;
        },
        title: 'Just a random field',
        isTransient: true,
    })
    toggleScale: ui.fields.Text;

    @ui.decorators.section<Accordion>({
        title: 'Time tracking',
    })
    detailPanelHeaderSection: ui.containers.Section;

    @ui.decorators.block<Accordion>({
        parent() {
            return this.detailPanelHeaderSection;
        },
    })
    detailPanelHeaderBlock: ui.containers.Block;

    @ui.decorators.labelField<Accordion>({
        parent() {
            return this.detailPanelHeaderBlock;
        },
        isTransient: true,
        title: 'Detail panel',
        map() {
            return 'header';
        },
    })
    detailPanelProductImageField: ui.fields.Label;

    @ui.decorators.section<Accordion>({
        title: 'An accordion section',
        mode: 'accordion',
    })
    detailPanelBodySection1: ui.containers.Section;

    @ui.decorators.block<Accordion>({
        parent() {
            return this.detailPanelBodySection1;
        },
        title: 'First section block',
    })
    detailPanelBlock1: ui.containers.Block;

    @ui.decorators.textField<Accordion>({
        isReadOnly: true,
        isTransient: true,
        parent() {
            return this.detailPanelBlock1;
        },
        title: 'My text',
    })
    detailPanelProductCategory: ui.fields.Text;

    @ui.decorators.numericField<Accordion>({
        isReadOnly: false,
        isTransient: true,
        scale: 1,
        parent() {
            return this.detailPanelBlock1;
        },
        title: 'Scaled number',
    })
    detailPanelScaledNumber: ui.fields.Numeric;

    @ui.decorators.block<Accordion>({
        parent() {
            return this.detailPanelBodySection1;
        },
        title: 'Second section block',
    })
    detailPanelBlock2: ui.containers.Block;

    @ui.decorators.switchField<Accordion>({
        isTransient: true,
        parent() {
            return this.detailPanelBlock2;
        },
        title: 'A Switch',
    })
    aSwitch: ui.fields.Switch;

    @ui.decorators.numericField<Accordion>({
        isReadOnly: false,
        isTransient: true,
        scale: 1,
        parent() {
            return this.detailPanelBlock2;
        },
        title: 'A numeric field',
    })
    aNumber: ui.fields.Numeric;

    @ui.decorators.textField<Accordion>({
        isTransient: true,
        width: 'large',
        parent() {
            return this.detailPanelBlock2;
        },
        title: 'A text field',
    })
    aTextField: ui.fields.Text;

    @ui.decorators.section<Accordion>({
        title: 'draft',
    })
    detailPanelBodySection2: ui.containers.Section;

    @ui.decorators.block<Accordion>({
        parent() {
            return this.detailPanelBodySection2;
        },
        title: 'Second section block',
    })
    detailPanelBlock3: ui.containers.Block;

    @ui.decorators.textAreaField<Accordion>({
        isTransient: true,
        parent() {
            return this.detailPanelBlock3;
        },
        title: 'My area',
        rows: 5,
    })
    detailPanelNote: ui.fields.TextArea;

    @ui.decorators.pageAction<Accordion>({
        title: 'Action 1',
        onClick() {
            this.$.showToast('You clicked on action 1');
        },
    })
    action1: ui.PageAction;

    @ui.decorators.pageAction<Accordion>({
        title: 'Action 2',
        onClick() {
            this.$.showToast('You clicked on action 2');
        },
    })
    action2: ui.PageAction;

    @ui.decorators.pageAction<Accordion>({
        title: 'Action 3',
        onClick() {
            this.$.showToast('You clicked on action 3');
        },
    })
    action3: ui.PageAction;

    @ui.decorators.pageAction<Accordion>({
        title: 'Action 4',
        onClick() {
            this.$.showToast('You clicked on action 4');
        },
    })
    action4: ui.PageAction;
}
