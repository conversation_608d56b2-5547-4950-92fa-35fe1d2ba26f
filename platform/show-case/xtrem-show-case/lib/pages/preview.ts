import * as ui from '@sage/xtrem-ui';
import { fields } from '../menu-items/fields';
import { GraphApi } from '@sage/xtrem-show-case-api';

@ui.decorators.page<Preview>({
    menuItem: fields,
    title: 'Preview',
    subtitle: 'Fields',
    isTransient: true,
    mode: 'tabs',
    headerSection() {
        return this.headerSection;
    },
    async onLoad() {
        const result = await this.$.graph
            .node('@sage/xtrem-show-case/ShowCaseInvoice')
            .query(
                ui.queryUtils.edgesSelector(
                    { pdf: { value: true } },
                    {
                        filter: {
                            _id: {
                                _in: [
                                    '300',
                                    '301',
                                    '302',
                                    '303',
                                    '304',
                                    '305',
                                    '306',
                                    '307',
                                    '308',
                                    '309',
                                    '310',
                                    '311',
                                    '312',
                                ],
                            },
                        },
                    },
                ),
            )
            .execute();
        const [
            multiPageDocx,
            singlePagePdf,
            multiPagePdf,
            singlePageDocx,
            jpg,
            svg,
            png,
            txt,
            csv,
            xml,
            json,
            singlePageTiff,
            multiPageTiff,
        ] = result.edges;

        this.field.value = singlePagePdf.node.pdf;
        this.multiPageDocx.value = multiPageDocx.node.pdf;
        this.singlePageDocx.value = singlePageDocx.node.pdf;
        this.autoFittingHightField.value = singlePageDocx.node.pdf;
        this.multiPagePdf.value = multiPagePdf.node.pdf;
        this.singlePagePdf.value = singlePagePdf.node.pdf;
        this.jpg.value = jpg.node.pdf;
        this.svg.value = svg.node.pdf;
        this.png.value = png.node.pdf;
        this.txt.value = txt.node.pdf;
        this.csv.value = csv.node.pdf;
        this.xml.value = xml.node.pdf;
        this.json.value = json.node.pdf;
        this.singlePageTiff.value = singlePageTiff.node.pdf;
        this.multiPageTiff.value = multiPageTiff.node.pdf;
    },
})
export class Preview extends ui.Page<GraphApi> {
    @ui.decorators.section<Preview>({
        title: 'Preview',
        isTitleHidden: true,
    })
    headerSection: ui.containers.Section;

    @ui.decorators.block<Preview>({
        parent() {
            return this.headerSection;
        },
        title: 'Introduction',
        width: 'medium',
    })
    introductionBlock: ui.containers.Block;

    @ui.decorators.staticContentField<Preview>({
        parent() {
            return this.introductionBlock;
        },
        isTitleHidden: true,
        isFullWidth: true,
        isMarkdown: true,
        content:
            'The preview field can render rich documents such as PDF, DOCX or image files. It is a read-only field. Depending on the rendered file type, the user may be able to print, download or zoom the document.',
    })
    description: ui.fields.StaticContent;

    @ui.decorators.section<Preview>({
        title: 'Preview',
        isTitleHidden: true,
    })
    section: ui.containers.Section;

    @ui.decorators.block<Preview>({
        parent() {
            return this.headerSection;
        },
        title: 'Field example',
        width: 'medium',
    })
    fieldBlock: ui.containers.Block;

    @ui.decorators.previewField<Preview>({
        parent() {
            return this.fieldBlock;
        },
        height: 200,
    })
    field: ui.fields.Preview;

    @ui.decorators.labelField<Preview>({
        parent() {
            return this.fieldBlock;
        },
        isHidden: true,
        map() {
            return 'Click was triggered';
        },
    })
    clickTriggered: ui.fields.Label;

    @ui.decorators.linkField<Preview>({
        parent() {
            return this.fieldBlock;
        },
        title: 'Source code',
        isFullWidth: true,
        map() {
            return ui.localize('@sage/xtrem-show-case/check-source-code', 'Check it on GitHub');
        },
        page: 'https://github.com/Sage-ERP-X3/xtrem/blob/master/platform/show-case/xtrem-show-case/lib/pages/preview.ts',
    })
    sourceCodeLink: ui.fields.Link;

    @ui.decorators.block<Preview>({
        parent() {
            return this.section;
        },
        title: 'Configuration',
    })
    configurationBlock: ui.containers.Block;

    @ui.decorators.checkboxField<Preview>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is disabled',
        helperText:
            'Determines whether the field is disabled or not. It can also be defined as callback function that returns a boolean.',
        isFullWidth: true,
        onChange() {
            this.field.isDisabled = !!this.isDisabled.value;
        },
    })
    isDisabled: ui.fields.Checkbox;

    @ui.decorators.checkboxField<Preview>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is hidden',
        helperText: 'Determines whether the field is displayed or not.',
        isFullWidth: true,
        onChange() {
            this.field.isHidden = !!this.isHidden.value;
        },
    })
    isHidden: ui.fields.Checkbox;

    @ui.decorators.checkboxField<Preview>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is loading',
        helperText:
            'Externally set loading state. If set to true, a loading bar is displayed even if the field has value',
        isFullWidth: true,
        onChange() {
            this.field.isLoading = !!this.isLoading.value;
        },
    })
    isLoading: ui.fields.Checkbox;

    @ui.decorators.checkboxField<Preview>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is filename hidden',
        helperText: 'Whether the filename in the header should be hidden',
        isFullWidth: true,
        onChange() {
            this.field.isFilenameHidden = !!this.isFilenameHidden.value;
        },
    })
    isFilenameHidden: ui.fields.Checkbox;

    @ui.decorators.checkboxField<Preview>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Has pagination controls',
        helperText: 'Whether the pagination controls are displayed in the header.',
        isFullWidth: true,
        onChange() {
            this.field.hasPaginationControls = !!this.hasPaginationControls.value;
        },
    })
    hasPaginationControls: ui.fields.Checkbox;

    @ui.decorators.textField<Preview>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Title',
        isFullWidth: true,
        helperText:
            'The title that is displayed above the field. The title can be provided as a string, or a callback function returning a string. When declared as a callback within the column of a nested grid, the column id is provided as a parameter. It is automatically picked up by the i18n engine and externalized for translation.',
        onChange() {
            this.field.title = this.title.value || '';
        },
    })
    title: ui.fields.Text;

    @ui.decorators.checkboxField<Preview>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is title hidden',
        isFullWidth: true,
        helperText: 'Whether the field title above the field should be displayed and its vertical space preserved.',
        isReversed: true,
        onChange() {
            this.field.isTitleHidden = !!this.isTitleHidden.value;
        },
    })
    isTitleHidden: ui.fields.Checkbox;

    @ui.decorators.textField<Preview>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Helper text',
        helperText:
            'The helper text that is displayed above the field. It is automatically picked up by the i18n engine and externalized.',
        isFullWidth: true,
        onChange() {
            this.field.helperText = this.helperText.value || '';
        },
    })
    helperText: ui.fields.Text;

    @ui.decorators.checkboxField<Preview>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is helper text hidden',
        helperText:
            'Whether the helper text underneath the field should be displayed and its vertical space preserved.',
        isFullWidth: true,
        isReversed: true,
        onChange() {
            this.field.isHelperTextHidden = !!this.isHelperTextHidden.value;
        },
    })
    isHelperTextHidden: ui.fields.Checkbox;

    @ui.decorators.buttonField<Preview>({
        parent() {
            return this.configurationBlock;
        },
        map() {
            return 'Focus field';
        },
        onClick() {
            this.field.focus();
        },
    })
    focus: ui.fields.Button;

    @ui.decorators.block<Preview>({
        parent() {
            return this.section;
        },
        title: 'Events',
    })
    eventsBlock: ui.containers.Block;

    @ui.decorators.staticContentField<Preview>({
        parent() {
            return this.eventsBlock;
        },
        isTitleHidden: true,
        isFullWidth: true,
        isMarkdown: true,
        content: '-      **onError**: Handles errors thrown from the callback functions.',
    })
    eventsDescription: ui.fields.StaticContent;

    @ui.decorators.block<Preview>({
        parent() {
            return this.section;
        },
        title: 'Runtime functions',
    })
    runtimeFunctionsBlock: ui.containers.Block;

    @ui.decorators.staticContentField<Preview>({
        parent() {
            return this.runtimeFunctionsBlock;
        },
        isTitleHidden: true,
        isFullWidth: true,
        isMarkdown: true,
        content:
            "-   **focus()**: Moves the focus to the field.\n-   **getNextField(isFocusable)**: Returns the next field instance. The order is calculated by the page prototype. If the isFocusable argument is set to true, it returns the next visible, enabled and non read-only field. It only considers the committed page state, so `commitValueAndPropertyChanges` call might be required beforehand to get the expected result.\n-   **refresh()**: Refetches the field's value from the server and updates it on the screen, only for non-transient pages.\n-   **validate()**: Triggers the field validation rules. Since the validation rules might be asynchronous, this method returns a promise that must be awaited to get the validation result\n-   **validateWithDetails()**: In addition to the functionality of `validate` it returns more details, including the rule that failed and where applicable, the row ID and colum ID.\n-   **fetchDefault(skipSet)**: Force re-fetches default value for the field. If the `skipSet` flag is set to true, it returns the default values but not apply them to the screen.\n-   **isDirty()**: Sets or gets the dirty state of the field.",
    })
    runtimeFunctionDescription: ui.fields.StaticContent;

    /* Additional examples */

    @ui.decorators.block<Preview>({
        parent() {
            return this.section;
        },
        title: 'Additional examples',
    })
    additionalBlock: ui.containers.Block;

    @ui.decorators.previewField<Preview>({
        title: 'Multi-page DOCX file',
        parent() {
            return this.additionalBlock;
        },
        canDownload: true,
        canPrint: true,
        canZoom: true,
        hasThumbnailBar: true,
        hasPaginationControls: true,
    })
    multiPageDocx: ui.fields.Preview;

    @ui.decorators.previewField<Preview>({
        title: 'Single-page DOCX file',
        parent() {
            return this.additionalBlock;
        },
        canDownload: true,
        canPrint: true,
        canZoom: true,
        hasThumbnailBar: true,
        hasPaginationControls: true,
    })
    singlePageDocx: ui.fields.Preview;

    @ui.decorators.previewField<Preview>({
        title: 'Multi-page PDF file',
        parent() {
            return this.additionalBlock;
        },
        canDownload: true,
        canPrint: true,
        canZoom: true,
        hasThumbnailBar: true,
        hasPaginationControls: true,
    })
    multiPagePdf: ui.fields.Preview;

    @ui.decorators.previewField<Preview>({
        title: 'Single-page PDF file',
        parent() {
            return this.additionalBlock;
        },
        canDownload: true,
        canPrint: true,
        canZoom: true,
        hasThumbnailBar: true,
        hasPaginationControls: true,
    })
    singlePagePdf: ui.fields.Preview;

    @ui.decorators.previewField<Preview>({
        title: 'Single-page TIFF file',
        parent() {
            return this.additionalBlock;
        },
        canDownload: true,
        canPrint: true,
        canZoom: true,
        hasThumbnailBar: true,
        hasPaginationControls: true,
    })
    singlePageTiff: ui.fields.Preview;

    @ui.decorators.previewField<Preview>({
        title: 'Multi-page TIFF file',
        parent() {
            return this.additionalBlock;
        },
        canDownload: true,
        canPrint: true,
        canZoom: true,
        hasThumbnailBar: true,
        hasPaginationControls: true,
    })
    multiPageTiff: ui.fields.Preview;

    @ui.decorators.previewField<Preview>({
        title: 'PNG file',
        parent() {
            return this.additionalBlock;
        },
        canDownload: true,
        canPrint: true,
        canZoom: true,
        hasThumbnailBar: true,
        hasPaginationControls: true,
    })
    png: ui.fields.Preview;

    @ui.decorators.previewField<Preview>({
        title: 'SVG file',
        parent() {
            return this.additionalBlock;
        },
        canDownload: true,
        canPrint: true,
        canZoom: true,
        hasThumbnailBar: true,
        hasPaginationControls: true,
        mimeType: 'image/svg+xml',
    })
    svg: ui.fields.Preview;

    @ui.decorators.previewField<Preview>({
        title: 'JPG file',
        parent() {
            return this.additionalBlock;
        },
        canDownload: true,
        canPrint: true,
        canZoom: true,
        hasThumbnailBar: true,
        hasPaginationControls: true,
    })
    jpg: ui.fields.Preview;

    @ui.decorators.previewField<Preview>({
        title: 'Text file',
        parent() {
            return this.additionalBlock;
        },
        canDownload: true,
        canPrint: true,
        canZoom: true,
        hasThumbnailBar: true,
        hasPaginationControls: true,
        mimeType: 'text/plain',
        filename: 'sample.txt',
    })
    txt: ui.fields.Preview;

    @ui.decorators.previewField<Preview>({
        title: 'CSV file',
        parent() {
            return this.additionalBlock;
        },
        canDownload: true,
        canPrint: true,
        canZoom: true,
        hasThumbnailBar: true,
        mimeType: 'text/csv',
    })
    csv: ui.fields.Preview;

    @ui.decorators.previewField<Preview>({
        title: 'XML file',
        parent() {
            return this.additionalBlock;
        },
        canDownload: true,
        canPrint: true,
        canZoom: true,
        hasThumbnailBar: true,
    })
    xml: ui.fields.Preview;

    @ui.decorators.previewField<Preview>({
        title: 'JSON file',
        parent() {
            return this.additionalBlock;
        },
        mimeType: 'application/json',
        canDownload: true,
        canPrint: true,
        canZoom: true,
        hasThumbnailBar: true,
    })
    json: ui.fields.Preview;

    @ui.decorators.previewField<Preview>({
        title: 'Empty document',
        parent() {
            return this.additionalBlock;
        },
        canDownload: true,
        canPrint: true,
        canZoom: true,
        hasThumbnailBar: true,
    })
    empty: ui.fields.Preview;

    @ui.decorators.section<Preview>({
        title: 'Auto-fitting height',
        isTitleHidden: true,
    })
    autoSizingSection: ui.containers.Section;

    @ui.decorators.previewField<Preview>({
        title: 'Auto-fitting height',
        isTitleHidden: true,
        parent() {
            return this.autoSizingSection;
        },
        canDownload: true,
        canPrint: true,
        canZoom: true,
        hasThumbnailBar: true,
    })
    autoFittingHightField: ui.fields.Preview;
}
