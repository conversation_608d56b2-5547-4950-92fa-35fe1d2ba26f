import {
    <PERSON><PERSON>h<PERSON><PERSON>,
    ShowCaseProduct,
    ShowCaseProviderAddressBinding,
    ShowCaseProvider as ShowCaseProviderNode,
} from '@sage/xtrem-show-case-api';
import {
    setApplicativePageCrudActions,
    setOrderOfPageHeaderDropDownActions,
} from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import * as ui from '@sage/xtrem-ui';
import { misc } from '../menu-items/misc';

@ui.decorators.page<LazyLoadedShowCaseProvider, ShowCaseProviderNode>({
    authorizationCode: 'SHCPRVD',
    category: 'SHOWCASE',
    mode: 'tabs',
    title: 'Lazy loaded provider page',
    menuItem: misc,
    objectTypeSingular: 'Provider',
    objectTypePlural: 'Providers',
    idField() {
        return this._id;
    },
    businessActions() {
        return [this.$standardCancelAction, this.$standardSaveAction];
    },
    headerQuickActions() {
        return [this.$standardDuplicateAction];
    },
    headerDropDownActions() {
        return setOrderOfPageHeaderDropDownActions<LazyLoadedShowCaseProvider>({
            remove: [this.$standardDeleteAction],
            dropDownBusinessActions: [this.$standardOpenRecordHistoryAction],
        });
    },
    headerSection() {
        return this.headerSection;
    },
    onLoad() {
        this.$.showToast(`Value of orphan decimal field: ${this.decimalField.value}`);
        setApplicativePageCrudActions({
            page: this,
            isDirty: false,
            save: this.$standardSaveAction,
            cancel: this.$standardCancelAction,
            duplicate: this.$standardDuplicateAction,
            remove: this.$standardDeleteAction,
            actions: [],
        });
    },
    module: 'show-case',
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.text({ bind: 'textField', title: 'Name' }),
            line2: ui.nestedFields.text({ bind: '_id', title: 'ID' }),
            line3: ui.nestedFields.date({ bind: 'dateField', title: 'Added on' }),
            image: ui.nestedFields.image({ bind: 'logo', title: 'Logo', placeholderMode: 'Initials' }),
        },
    },
    node: '@sage/xtrem-show-case/ShowCaseProvider',
    priority: 500,

    onDirtyStateUpdated(isDirty: boolean) {
        setApplicativePageCrudActions({
            page: this,
            isDirty,
            save: this.$standardSaveAction,
            cancel: this.$standardCancelAction,
            duplicate: this.$standardDuplicateAction,
            remove: this.$standardDeleteAction,
            actions: [],
        });
    },
})
export class LazyLoadedShowCaseProvider extends ui.Page<GraphApi> {
    @ui.decorators.numericField<LazyLoadedShowCaseProvider>({
        title: 'Decimal fields',
        scale: 0,
    })
    decimalField: ui.fields.Numeric;

    @ui.decorators.section<LazyLoadedShowCaseProvider>({
        title: 'Header',
    })
    headerSection: ui.containers.Section;

    @ui.decorators.block<LazyLoadedShowCaseProvider>({
        parent() {
            return this.headerSection;
        },
    })
    headerBlock: ui.containers.Block;

    @ui.decorators.textField<LazyLoadedShowCaseProvider>({
        parent() {
            return this.headerBlock;
        },
        title: 'ID',
        isReadOnly: true,
    })
    _id: ui.fields.Text;

    @ui.decorators.textField<LazyLoadedShowCaseProvider>({
        parent() {
            return this.headerBlock;
        },
        title: 'Name',
    })
    textField: ui.fields.Text;

    @ui.decorators.section<LazyLoadedShowCaseProvider>({
        title: 'Misc',
    })
    miscSection: ui.containers.Section;

    @ui.decorators.block<LazyLoadedShowCaseProvider>({
        parent() {
            return this.miscSection;
        },
    })
    miscBlock: ui.containers.Block;

    @ui.decorators.dateField<LazyLoadedShowCaseProvider>({
        parent() {
            return this.miscBlock;
        },
        title: 'Date',
    })
    dateField: ui.fields.Date;

    @ui.decorators.checkboxField<LazyLoadedShowCaseProvider>({
        parent() {
            return this.miscBlock;
        },
        title: 'Checkbox',
    })
    booleanField: ui.fields.Checkbox;

    @ui.decorators.numericField<LazyLoadedShowCaseProvider>({
        parent() {
            return this.miscBlock;
        },
        title: 'Integer',
        scale: 0,
    })
    integerField: ui.fields.Numeric;

    @ui.decorators.section<LazyLoadedShowCaseProvider>({
        title: 'Products',
        isLazyLoaded: true,
        onActive() {
            const productCount = this.products.value.length;
            this.productListSection.indicatorContent = productCount === 20 ? '19+' : String(productCount);
        },
    })
    productListSection: ui.containers.Section;

    @ui.decorators.tableField<LazyLoadedShowCaseProvider, ShowCaseProduct>({
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        mainField: 'description',
        parent() {
            return this.productListSection;
        },
        title: 'Products',
        isTitleHidden: true,
        orderBy: { _id: 1 },
        columns: [
            ui.nestedFields.text({ bind: '_id', isReadOnly: true, title: 'Id' }),
            ui.nestedFields.text({
                bind: 'product',
                title: 'Product',
            }),
            ui.nestedFields.text({ bind: 'description', title: 'Description' }),
            ui.nestedFields.checkbox({ bind: 'hotProduct', isReadOnly: true, title: 'Hot' }),
            ui.nestedFields.select({ bind: 'category', isReadOnly: true, title: 'Category' }),
            ui.nestedFields.numeric({
                bind: 'total',
                title: 'Net Price',
                scale: 2,
                min: 0,
                onChange(_id, rowValue) {
                    setTimeout(
                        () => {
                            this.products.addOrUpdateRecordValue({
                                ...rowValue,
                                amount: rowValue.qty * rowValue.total,
                            });
                        },
                        Math.floor(Math.random() * 1000),
                    );
                },
            }),
            ui.nestedFields.numeric({
                bind: 'qty',
                title: 'Quantity',
                scale: 2,
                onChange(_id, rowValue) {
                    setTimeout(
                        () => {
                            this.products.addOrUpdateRecordValue({
                                ...rowValue,
                                amount: rowValue.qty * rowValue.total,
                            });
                        },
                        Math.floor(Math.random() * 1000),
                    );
                },
            }),
            ui.nestedFields.numeric({ bind: 'amount', title: 'Amount', scale: 2 }),
            ui.nestedFields.reference({
                bind: 'designerEmployee',
                node: '@sage/xtrem-show-case/ShowCaseEmployee',
                title: 'Product designed by',
                valueField: 'firstName',
                helperTextField: 'lastName',
                tunnelPage: '@sage/xtrem-show-case/ShowCaseEmployee',
                validation(value) {
                    if (value?.firstName === 'John') {
                        return 'No Johns allowed.';
                    }
                    return undefined;
                },
                onChange() {
                    this.$.showToast('The designer employee field was changed.');
                },
                columns: [
                    ui.nestedFields.text({ bind: 'firstName', title: 'First name' }),
                    ui.nestedFields.text({ bind: 'lastName', title: 'Last name' }),
                ],
            }),
        ],
        dropdownActions: [
            {
                icon: 'box_arrow_left',
                title: 'Edit on sidebar',
                async onClick(rowId: any) {
                    this.products.openSidebar(rowId);
                },
            },
        ],
        sidebar: {
            title(_id, recordValue) {
                return recordValue.product;
            },
            layout() {
                return {
                    mainSection: {
                        title: 'Product details',
                        blocks: {
                            mainBlock: {
                                fields: ['product', 'designerEmployee', 'category', 'qty', 'amount', 'description'],
                            },
                        },
                    },
                };
            },
        },
    })
    products: ui.fields.Table<ShowCaseProduct>;

    @ui.decorators.section<LazyLoadedShowCaseProvider>({
        title: 'Another section',
        isLazyLoaded: true,
        onActive() {
            this.$.showToast(`Another section is loaded, address: ${this.concatenatedAddress.value} `);
        },
    })
    anotherSection: ui.containers.Section;

    @ui.decorators.block<LazyLoadedShowCaseProvider>({
        title: 'Another section',
        isTitleHidden: true,
        parent() {
            return this.anotherSection;
        },
    })
    anotherBlock: ui.containers.Block;

    @ui.decorators.textField<LazyLoadedShowCaseProvider>({
        parent() {
            return this.anotherBlock;
        },
        title: 'Address',
        isFullWidth: true,
    })
    concatenatedAddress: ui.fields.Text;

    @ui.decorators.vitalPodField<LazyLoadedShowCaseProvider, ShowCaseProviderAddressBinding>({
        title: 'Address',
        parent() {
            return this.anotherBlock;
        },
        columns: [
            ui.nestedFields.text({ bind: 'name', title: 'Name' }),
            ui.nestedFields.text({ bind: 'addressLine1', title: 'Line1' }),
            ui.nestedFields.text({ bind: 'addressLine2', title: 'Line2' }),
            ui.nestedFields.reference({
                bind: 'country',
                title: 'Country',
                node: '@sage/xtrem-show-case/ShowCaseCountry',
                valueField: 'name',
                helperTextField: 'code',
            }),
            ui.nestedFields.text({ bind: 'zip', title: 'zip', isTransientInput: true }),
        ],
        node: '@sage/xtrem-show-case/ShowCaseProviderAddress',
    })
    siteAddress: ui.fields.VitalPod;

    @ui.decorators.podField<LazyLoadedShowCaseProvider, ShowCaseProduct>({
        parent() {
            return this.anotherBlock;
        },
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        title: 'Flagship Product!',
        columns: [
            ui.nestedFields.text({ bind: 'product', title: 'Product Name' }),
            ui.nestedFields.multiDropdown({ bind: 'entries', title: 'Entries' }),
        ],
    })
    flagshipProduct: ui.fields.Pod;
}
