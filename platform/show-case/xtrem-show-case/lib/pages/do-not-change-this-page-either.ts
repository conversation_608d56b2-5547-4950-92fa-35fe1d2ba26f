import {
    Graph<PERSON><PERSON>,
    ShowCaseProduct,
    ShowCaseProvider as ShowC<PERSON><PERSON>roviderNode,
    ShowCaseProviderAddressBinding,
} from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import { applicationPages } from '../menu-items/application-pages';

@ui.decorators.page<DoNotChangeThisPageEither, ShowCaseProviderNode>({
    authorizationCode: 'SHCPRVD',
    category: 'SHOWCASE',
    menuItem: applicationPages,
    objectTypeSingular: 'Provider',
    objectTypePlural: 'Providers',
    idField() {
        return this._id;
    },
    businessActions() {
        return [this.toggleNavigationPanel, this.update];
    },
    headerQuickActions() {
        return [this.$standardDuplicateAction];
    },
    headerDropDownActions() {
        return [this.create, this.update, this.menuSeparator, this.delete];
    },
    detailPanel() {
        return {
            header: this.detailPanelHeaderDetailSection,
            sections: [this.detailPanelDetailSection],
        };
    },
    module: 'show-case',
    navigationPanel: {
        bulkActions: [
            {
                mutation: 'modify',
                title: 'Modify',
                buttonType: 'primary',
                isDestructive: false,
            },
            {
                mutation: 'export',
                title: 'Export',
                buttonType: 'secondary',
                icon: 'file_excel',
                isDestructive: false,
            },
            {
                mutation: 'print',
                title: 'Print',
                buttonType: 'tertiary',
                icon: 'print',
                isDestructive: false,
            },
            {
                mutation: 'remove',
                title: 'Delete',
                buttonType: 'tertiary',
                icon: 'delete',
                isDestructive: true,
            },
        ],
        listItem: {
            title: ui.nestedFields.text({ bind: 'textField', title: 'Name' }),
            line2: ui.nestedFields.text({ bind: '_id', title: 'ID' }),
            line3: ui.nestedFields.date({ bind: 'dateField', title: 'Added on' }),
            image: ui.nestedFields.image({ bind: 'logo', title: 'Logo', placeholderMode: 'Initials' }),
        },
    },
    node: '@sage/xtrem-show-case/ShowCaseProvider',
    title: "Unit test fixture page, don't change it please",
    priority: 200,
    onLoad() {
        this.$.detailPanel.isHidden = true;
    },
})
export class DoNotChangeThisPageEither extends ui.Page<GraphApi> {
    @ui.decorators.section<DoNotChangeThisPageEither>({})
    section: ui.containers.Section;

    @ui.decorators.block<DoNotChangeThisPageEither>({
        parent() {
            return this.section;
        },
    })
    block: ui.containers.Block;

    @ui.decorators.textField<DoNotChangeThisPageEither>({
        parent() {
            return this.block;
        },
        title: 'Id',
    })
    _id: ui.fields.Text;

    @ui.decorators.dateField<DoNotChangeThisPageEither>({
        parent() {
            return this.block;
        },
        title: 'Date',
    })
    dateField: ui.fields.Date;

    @ui.decorators.checkboxField<DoNotChangeThisPageEither>({
        parent() {
            return this.block;
        },
        title: 'Checkbox',
    })
    booleanField: ui.fields.Checkbox;

    @ui.decorators.numericField<DoNotChangeThisPageEither>({
        parent() {
            return this.block;
        },
        title: 'Integer',
        scale: 0,
    })
    integerField: ui.fields.Numeric;

    @ui.decorators.numericField<DoNotChangeThisPageEither>({
        parent() {
            return this.block;
        },
        async onChange() {
            await this.products.revalidate(record => {
                return record.qty >= this.minQuantity.value;
            });
        },
        title: 'Minimum Quantity',
        scale: 0,
    })
    minQuantity: ui.fields.Numeric;

    @ui.decorators.numericField<DoNotChangeThisPageEither>({
        bind: 'decimalField',
        parent() {
            return this.block;
        },
        title: 'Decimal',
        scale: 2,
    })
    testingTheBindPropertyInMutations: ui.fields.Numeric;

    @ui.decorators.tableField<DoNotChangeThisPageEither, ShowCaseProduct>({
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        mainField: 'description',
        parent() {
            return this.block;
        },
        title: 'Products',
        canFilter: false,
        onRowClick() {
            ui.console.log('clicked');
        },
        orderBy: { _id: 1 },
        columns: [
            ui.nestedFields.text({ bind: '_id', isReadOnly: true, title: 'Id' }),
            ui.nestedFields.link({
                bind: 'product',
                title: 'Product',
                onClick(_id) {
                    this.$.router.goTo('@sage/xtrem-show-case/ShowCaseProduct', { _id });
                },
            }),
            ui.nestedFields.text({ bind: 'description', title: 'Description' }),
            ui.nestedFields.checkbox({ bind: 'hotProduct', isReadOnly: true, title: 'Hot' }),
            ui.nestedFields.select({ bind: 'category', isReadOnly: true, title: 'Category' }),
            ui.nestedFields.numeric({ bind: 'total', title: 'Net Price', scale: 2, min: 0 }),
            ui.nestedFields.numeric({ bind: 'qty', title: 'Quantity', scale: 2 }),
            ui.nestedFields.numeric({ bind: 'amount', title: 'Amount', scale: 2 }),
        ],
        dropdownActions: [
            {
                title: 'Open in Detail panel',
                icon: 'play',
                onClick(rowId: any, data: any) {
                    this.$.detailPanel.isHidden = false;
                    this.productCard.value = data;
                    this.detailPanelHeaderDetailSection.title = data.product;
                    this.dialogGridRowBlock.selectedRecordId = data._id;
                },
            },
            ui.menuSeparator(),
            {
                icon: 'bin',
                title: 'Remove',
                isDestructive: true,
                async onClick(rowId: any, data: any) {
                    await this.$.graph.delete({
                        _id: data._id,
                        nodeName: '@sage/xtrem-show-case/ShowCaseProduct',
                    });
                    await this.products.refresh();
                },
            },
        ],
    })
    products: ui.fields.Table<ShowCaseProduct>;

    @ui.decorators.podField<DoNotChangeThisPageEither, ShowCaseProduct>({
        parent() {
            return this.block;
        },

        node: '@sage/xtrem-show-case/ShowCaseProduct',
        title: 'Flagship Product!',
        columns: [
            ui.nestedFields.text({ bind: 'product', title: 'Product Name' }),
            ui.nestedFields.multiDropdown({ bind: 'entries', title: 'Entries' }),
        ],
    })
    flagshipProduct: ui.fields.Pod;

    @ui.decorators.vitalPodField<DoNotChangeThisPageEither, ShowCaseProviderAddressBinding>({
        title: 'Address',
        parent() {
            return this.block;
        },
        columns: [
            ui.nestedFields.text({ bind: 'name', title: 'Name' }),
            ui.nestedFields.text({ bind: 'addressLine1', title: 'Line1' }),
            ui.nestedFields.text({ bind: 'addressLine2', title: 'Line2' }),
            ui.nestedFields.reference({
                bind: 'country',
                title: 'Country',
                node: '@sage/xtrem-show-case/ShowCaseCountry',
                valueField: 'name',
                helperTextField: 'code',
            }),
            ui.nestedFields.text({ bind: 'zip', title: 'zip', isTransientInput: true }),
        ],
        node: '@sage/xtrem-show-case/ShowCaseProviderAddress',
    })
    siteAddress: ui.fields.VitalPod;

    @ui.decorators.section<DoNotChangeThisPageEither>({})
    detailPanelHeaderDetailSection: ui.containers.Section;

    @ui.decorators.section<DoNotChangeThisPageEither>({})
    detailPanelDetailSection: ui.containers.Section;

    @ui.decorators.cardField<DoNotChangeThisPageEither, ShowCaseProduct>({
        isFullWidth: true,
        parent() {
            return this.detailPanelHeaderDetailSection;
        },
        isTransient: true,
        cardDefinition: {
            title: ui.nestedFields.text({ bind: 'description' }),
            titleRight: ui.nestedFields.label({ bind: 'category' }),
            line2: ui.nestedFields.numeric({ bind: 'amount' }),
            image: ui.nestedFields.image({ bind: 'imageField' }),
        },
    })
    productCard: ui.fields.Card;

    @ui.decorators.gridRowBlock<DoNotChangeThisPageEither>({
        parent() {
            return this.detailPanelDetailSection;
        },
        isTitleHidden: true,
        boundTo() {
            return this.products;
        },
    })
    dialogGridRowBlock: ui.containers.GridRowBlock;

    @ui.decorators.pageAction<DoNotChangeThisPageEither>({
        title: 'Custom Create',
        async onClick() {
            await this.$.graph.create();
            this.$.dialog.message('info', 'Mutation Create', `Created entry: ${this._id.value}`, {
                fullScreen: false,
                rightAligned: false,
                acceptButton: {
                    isDisabled: false,
                    isHidden: false,
                    text: 'OK',
                },
            });
        },
    })
    create: ui.PageAction;

    @ui.decorators.pageAction<DoNotChangeThisPageEither>({
        title: 'Save',
        async onClick() {
            await this.$standardSaveAction.execute(true);
            if (await this.$.page.isValid) {
                this.$.dialog.message('info', 'Mutation Update', `Updated entry: ${this._id.value}`, {
                    fullScreen: false,
                    rightAligned: false,
                    acceptButton: {
                        isDisabled: false,
                        isHidden: false,
                        text: 'OK',
                    },
                });
            }
        },
    })
    update: ui.PageAction;

    @ui.decorators.pageAction<DoNotChangeThisPageEither>({
        title: 'Delete',
        icon: 'bin',
        isDestructive: true,
        async onClick() {
            await this.$.graph.delete();
            this.$.router.goTo('@sage/xtrem-show-case/BoundPage');
        },
    })
    delete: ui.PageAction;

    @ui.decorators.pageAction<DoNotChangeThisPageEither>({
        isMenuSeparator: true,
    })
    menuSeparator: ui.PageAction;

    @ui.decorators.pageAction<DoNotChangeThisPageEither>({
        title: 'Toggle Navigation Panel',
        async onClick() {
            this.$.isNavigationPanelHidden = !this.$.isNavigationPanelHidden;
        },
    })
    toggleNavigationPanel: ui.PageAction;

    @ui.decorators.buttonField<DoNotChangeThisPageEither>({
        isTransient: true,
        map() {
            return 'Hide Description Column';
        },
        onClick() {
            this.products.hideColumn('description');
        },
        parent() {
            return this.block;
        },
    })
    hideDescriptionButton: ui.fields.Button;
}
