import * as ui from '@sage/xtrem-ui';
import { fields } from '../menu-items/fields';

@ui.decorators.page<DatetimeRange>({
    menuItem: fields,
    title: 'Date-time Range',
    subtitle: 'Fields',
    isTransient: true,
    headerSection() {
        return this.headerSection;
    },
})
export class DatetimeRange extends ui.Page {
    @ui.decorators.section<DatetimeRange>({
        title: 'DateTime Range',
        isTitleHidden: true,
    })
    headerSection: ui.containers.Section;

    @ui.decorators.section<DatetimeRange>({
        title: 'DateTime Range field',
        isTitleHidden: true,
    })
    section: ui.containers.Section;

    @ui.decorators.block<DatetimeRange>({
        parent() {
            return this.headerSection;
        },
        title: 'Introduction',
        width: 'medium',
    })
    introductionBlock: ui.containers.Block;

    @ui.decorators.staticContentField<DatetimeRange>({
        parent() {
            return this.introductionBlock;
        },
        isTitleHidden: true,
        isFullWidth: true,
        content: 'DatetimeRange fields can be used to represent date and time range values.',
    })
    description: ui.fields.StaticContent;

    @ui.decorators.linkField<DatetimeRange>({
        parent() {
            return this.introductionBlock;
        },
        title: 'Source code',
        isFullWidth: true,
        map() {
            return ui.localize('@sage/xtrem-show-case/check-source-code', 'Check it on GitHub');
        },
        page: 'https://github.com/Sage-ERP-X3/xtrem/blob/master/platform/show-case/xtrem-show-case/lib/pages/datetime-range.ts',
    })
    sourceCodeLink: ui.fields.Link;

    @ui.decorators.block<DatetimeRange>({
        parent() {
            return this.section;
        },
        title: 'Field example',
    })
    fieldBlock: ui.containers.Block;

    @ui.decorators.dateTimeRangeField<DatetimeRange>({
        parent() {
            return this.fieldBlock;
        },
        onClick() {
            this.clickTriggered.isHidden = false;
            setTimeout(() => {
                this.clickTriggered.isHidden = true;
            }, 1000);
        },
        onChange() {
            this.changeTriggered.isHidden = false;
            setTimeout(() => {
                this.changeTriggered.isHidden = true;
            }, 5000);
        },
        width: 'large',
        helperText: 'This field accepts date and time ranges.',
        validation(value: string) {
            const [start, end] = value.split(' - ');
            return new Date(start) >= new Date(end) ? 'End date must be greater than start date' : '';
        },
    })
    field: ui.fields.DatetimeRange;

    @ui.decorators.labelField<DatetimeRange>({
        parent() {
            return this.fieldBlock;
        },
        isHidden: true,
        map() {
            return 'Change was triggered';
        },
    })
    changeTriggered: ui.fields.Label;

    @ui.decorators.labelField<DatetimeRange>({
        parent() {
            return this.fieldBlock;
        },
        isHidden: true,
        map() {
            return 'Click was triggered';
        },
    })
    clickTriggered: ui.fields.Label;

    @ui.decorators.block<DatetimeRange>({
        parent() {
            return this.section;
        },
        title: 'Configuration',
    })
    configurationBlock: ui.containers.Block;

    @ui.decorators.dropdownListField<DatetimeRange>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Time zone',
        helperText: 'Select a time zone for the date-time range field.',
        options: [
            'America/New_York',
            'Europe/London',
            'Asia/Tokyo',
            'Australia/Sydney',
            'America/Los_Angeles',
            'UTC',
            'Europe/Berlin',
            'America/Chicago',
            'Asia/Hong_Kong',
            'Africa/Cairo',
            'Europe/Madrid',
            'America/Sao_Paulo',
            'Asia/Singapore',
            'Africa/Johannesburg',
            'Europe/Paris',
        ],

        onChange() {
            this.field.timeZone = this.timeZoneSelect.value || '';
        },
    })
    timeZoneSelect: ui.fields.DropdownList;

    @ui.decorators.checkboxField<DatetimeRange>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Timezone hidden',
        helperText: 'Determines Timezone is hidden or not',
        isFullWidth: true,
        isReversed: true,
        onChange() {
            this.field.isTimeZoneHidden = !!this.isTimeZoneHidden.value;
        },
    })
    isTimeZoneHidden: ui.fields.Checkbox;

    @ui.decorators.dateField<DatetimeRange>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Default date',

        onChange() {
            this.field.defaultDate = new Date(this.defaultDate.value);
        },
    })
    defaultDate: ui.fields.Date;

    @ui.decorators.dateField<DatetimeRange>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Min Date',
        onChange() {
            this.field.minDate = new Date(this.minDate.value);
        },
    })
    minDate: ui.fields.Date;

    @ui.decorators.dateField<DatetimeRange>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Max Date',
        onChange() {
            this.field.maxDate = new Date(this.maxDate.value);
        },
    })
    maxDate: ui.fields.Date;

    @ui.decorators.checkboxField<DatetimeRange>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is disabled',
        helperText:
            'Determines whether the field is disabled or not. It can also be defined as callback function that returns a boolean.',
        isFullWidth: true,
        isReversed: true,
        onChange() {
            this.field.isDisabled = !!this.isDisabled.value;
        },
    })
    isDisabled: ui.fields.Checkbox;

    @ui.decorators.checkboxField<DatetimeRange>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is read only',
        helperText:
            'Whether the field is editable (isReadOnly = false) or not (isReadOnly = true). The difference with disabled is that isReadOnly suggests that the field is never editable. It can be defined as a boolean, or conditionally by a callback that returns a boolean.',
        isFullWidth: true,
        isReversed: true,
        onChange() {
            this.field.isReadOnly = !!this.isReadOnly.value;
        },
    })
    isReadOnly: ui.fields.Checkbox;

    @ui.decorators.checkboxField<DatetimeRange>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is hidden',
        helperText: 'Determines whether the field is displayed or not.',
        isFullWidth: true,
        isReversed: true,
        onChange() {
            this.field.isHidden = !!this.isHidden.value;
        },
    })
    isHidden: ui.fields.Checkbox;

    @ui.decorators.textField<DatetimeRange>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Title',
        isFullWidth: true,
        helperText:
            'The title that is displayed above the field. The title can be provided as a string, or a callback function returning a string. It is automatically picked up by the i18n engine and externalized for translation.',
        onChange() {
            this.field.title = this.title.value || '';
        },
    })
    title: ui.fields.Text;

    @ui.decorators.checkboxField<DatetimeRange>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is title hidden',
        isFullWidth: true,
        helperText: 'Whether the field title above the field should be displayed and its vertical space preserved.',
        isReversed: true,
        onChange() {
            this.field.isTitleHidden = !!this.isTitleHidden.value;
        },
    })
    isTitleHidden: ui.fields.Checkbox;

    @ui.decorators.textField<DatetimeRange>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Helper text',
        helperText:
            'The helper text that is displayed above the field. It is automatically picked up by the i18n engine and externalized.',
        isFullWidth: true,
        onChange() {
            this.field.helperText = this.helperText.value || '';
        },
    })
    helperText: ui.fields.Text;

    @ui.decorators.checkboxField<DatetimeRange>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is helper text hidden',
        helperText:
            'Whether the helper text underneath the field should be displayed and its vertical space preserved.',
        isFullWidth: true,
        isReversed: true,
        onChange() {
            this.field.isHelperTextHidden = !!this.isHelperTextHidden.value;
        },
    })
    isHelperTextHidden: ui.fields.Checkbox;

    @ui.decorators.checkboxField<DatetimeRange>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is mandatory',
        helperText:
            ' Makes the field mandatory, empty values will raise an error message. It can also be defined as callback function that returns a boolean.',
        isFullWidth: true,
        isReversed: true,
        onChange() {
            this.field.isMandatory = !!this.isMandatory.value;
        },
    })
    isMandatory: ui.fields.Checkbox;

    @ui.decorators.buttonField<DatetimeRange>({
        parent() {
            return this.configurationBlock;
        },
        map() {
            return 'Set to null';
        },
        title: 'Set value to null',
        onClick() {
            this.field.value = null;
        },
    })
    setToNullButton: ui.fields.Button;

    @ui.decorators.buttonField<DatetimeRange>({
        parent() {
            return this.configurationBlock;
        },
        map() {
            return 'Focus field';
        },
        onClick() {
            this.field.focus();
        },
    })
    focus: ui.fields.Button;

    @ui.decorators.block<DatetimeRange>({
        parent() {
            return this.section;
        },
        title: 'Events',
    })
    eventsBlock: ui.containers.Block;

    @ui.decorators.staticContentField<DatetimeRange>({
        parent() {
            return this.eventsBlock;
        },
        isTitleHidden: true,
        isFullWidth: true,
        isMarkdown: true,
        content:
            '-   **onClick**: Triggered when any parts of the field is clicked, no arguments provided.\n-   **onChange**: Triggered when the field value changed and the focus is about to move away from the field, no arguments provided.\n-   **onError**: Handles errors thrown from the callback functions.',
    })
    eventsDescription: ui.fields.StaticContent;

    @ui.decorators.block<DatetimeRange>({
        parent() {
            return this.section;
        },
        title: 'Runtime functions',
    })
    runtimeFunctionsBlock: ui.containers.Block;

    @ui.decorators.staticContentField<DatetimeRange>({
        parent() {
            return this.runtimeFunctionsBlock;
        },
        isTitleHidden: true,
        isFullWidth: true,
        isMarkdown: true,
        content:
            "-   **focus()**: Moves the focus to the field.\n-   **getNextField(isFocusable)**: Returns the next field instance. The order is calculated by the page prototype. If the isFocusable argument is set to true, it returns the next visible, enabled and non read-only field. It only considers the committed page state, so `commitValueAndPropertyChanges` call might be required beforehand to get the expected result.\n-   **refresh()**: Refetches the field's value from the server and updates it on the screen, only for non-transient pages.\n-   **validate()**: Triggers the field validation rules. Since the validation rules might be asynchronous, this method returns a promise that must be awaited to get the validation result\n-   **validateWithDetails()**: In addition to the functionality of `validate` it returns more details, including the rule that failed and where applicable, the row ID and column ID.\n-   **fetchDefault(skipSet)**: Force re-fetches default value for the field. If the `skipSet` flag is set to true, it returns the default values but does not apply them to the screen.\n-   **isDirty()**: Sets or gets the dirty state of the field.",
    })
    runtimeFunctionDescription: ui.fields.StaticContent;
}
