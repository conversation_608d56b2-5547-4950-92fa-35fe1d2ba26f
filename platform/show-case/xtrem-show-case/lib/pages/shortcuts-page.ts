import * as ui from '@sage/xtrem-ui';
import { misc } from '../menu-items/misc';

@ui.decorators.page<ShortcutsPage>({
    authorizationCode: 'HLPRPNL',
    module: 'show-case',
    category: 'SHOWCASE',
    title: 'Page - Shortcuts',
    isTransient: true,
    menuItem: misc,
    onLoad() {
        this.infoText.value =
            'This page has other actions that can be triggered by key combinations. Try Alt+O, Shift+Ctrl+T or just simply the letter K.';
    },
})
export class ShortcutsPage extends ui.Page {
    @ui.decorators.section<ShortcutsPage>({
        isTitleHidden: true,
    })
    section1: ui.containers.Section;

    @ui.decorators.block<ShortcutsPage>({
        isTitleHidden: true,
        parent() {
            return this.section1;
        },
    })
    block1: ui.containers.Block;

    @ui.decorators.buttonField<ShortcutsPage>({
        parent() {
            return this.block1;
        },
        map() {
            return 'Click me!';
        },
        title: 'Button 1',
        shortcut: ['shift', 'control', 'm'],
        helperText: 'You can also trigger me by Shift + Ctrl + M',
        onClick() {
            this.$.showToast('Wow you clicked Button 1!');
        },
    })
    button1: ui.fields.Button;

    @ui.decorators.buttonField<ShortcutsPage>({
        parent() {
            return this.block1;
        },
        map() {
            return 'Click me!';
        },
        title: 'Button 2',
        shortcut: ['shift', 'alt', 'p'],
        helperText: 'You can also trigger me by Shift + Alt + P',
        onClick() {
            this.$.showToast('Wow you clicked Button 2!');
        },
    })
    button2: ui.fields.Button;

    @ui.decorators.buttonField<ShortcutsPage>({
        parent() {
            return this.block1;
        },
        map() {
            return 'Click me!';
        },
        title: 'Open Dialog',
        onClick() {
            this.$.dialog.page('@sage/xtrem-show-case/ShortcutsDialog');
        },
    })
    openDialogButton: ui.fields.Button;

    @ui.decorators.textAreaField<ShortcutsPage>({
        isTitleHidden: true,
        parent() {
            return this.block1;
        },
        isReadOnly: true,
        isFullWidth: true,
    })
    infoText: ui.fields.TextArea;

    @ui.decorators.pageAction<ShortcutsPage>({
        isTitleHidden: true,
        shortcut: ['shift', 'alt', 'p'],
        onClick() {
            this.$.showToast('Wow you clicked triggered an action by shift alt p!');
        },
    })
    action1: ui.PageAction;

    @ui.decorators.pageAction<ShortcutsPage>({
        isTitleHidden: true,
        shortcut: 'k',
        onClick() {
            this.$.showToast('An action is triggered by key K');
        },
    })
    action2: ui.PageAction;
}
