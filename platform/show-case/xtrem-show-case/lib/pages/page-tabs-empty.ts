import * as ui from '@sage/xtrem-ui';
import { containers } from '../menu-items/containers';

@ui.decorators.page<PageTabsEmpty>({
    category: 'SHOWCASE',
    mode: 'default',
    isTransient: true,
    module: 'show-case',
    title: 'Page - Empty Tabs Layout',
    menuItem: containers,
})
export class PageTabsEmpty extends ui.Page {
    @ui.decorators.section<PageTabsEmpty>({
        isTitleHidden: true,
    })
    section1: ui.containers.Section;

    @ui.decorators.section<PageTabsEmpty>({
        isTitleHidden: true,
    })
    section2: ui.containers.Section;

    @ui.decorators.section<PageTabsEmpty>({
        isTitleHidden: true,
    })
    section3: ui.containers.Section;

    @ui.decorators.block<PageTabsEmpty>({
        parent() {
            return this.section1;
        },
        title: 'First Block',
    })
    block1: ui.containers.Block;

    @ui.decorators.block<PageTabsEmpty>({
        parent() {
            return this.section2;
        },
        title: 'Second Block',
    })
    block2: ui.containers.Block;

    @ui.decorators.block<PageTabsEmpty>({
        parent() {
            return this.section3;
        },
        title: 'Third Block',
    })
    block3: ui.containers.Block;
}
