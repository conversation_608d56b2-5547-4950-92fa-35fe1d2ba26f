import { Graph<PERSON><PERSON>, ShowCaseInvoice } from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import type { EditorSuggestion, MonacoPluginProperties } from '@sage/xtrem-ui-plugin-monaco';
import { plugins } from '../menu-items/plugins';

@ui.decorators.page<PluginMonaco, ShowCaseInvoice>({
    authorizationCode: 'SHCPRVD',
    category: 'SHOWCASE',
    businessActions() {
        return [this.update];
    },
    module: 'show-case',
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.reference({
                bind: 'customer',
                valueField: 'name',
                node: '@sage/xtrem-show-case/ShowCaseCustomer',
            }),
            line2: ui.nestedFields.text({ bind: '_id' }),
        },
    },
    node: '@sage/xtrem-show-case/ShowCaseInvoice',
    title: 'Field - Monaco Plugin',
    menuItem: plugins,
    onLoad() {
        this.language.value = 'typescript';
        this.height.value = 400;
    },
})
export class PluginMonaco extends ui.Page<GraphApi> {
    @ui.decorators.section<PluginMonaco>({
        isTitleHidden: true,
    })
    section: ui.containers.Section;

    @ui.decorators.block<PluginMonaco>({
        parent() {
            return this.section;
        },
    })
    block: ui.containers.Block;

    @ui.decorators.textField<PluginMonaco>({
        parent() {
            return this.block;
        },
        title: 'Id',
        isReadOnly: true,
    })
    _id: ui.fields.Text;

    @ui.decorators.checkboxField<PluginMonaco>({
        parent() {
            return this.block;
        },
        isTransient: true,
        title: 'Is readOnly',
        onChange() {
            this.notes.isReadOnly = this.isReadOnly.value;
        },
    })
    isReadOnly: ui.fields.Checkbox;

    @ui.decorators.selectField<PluginMonaco>({
        parent() {
            return this.block;
        },
        isTransient: true,
        title: 'Language',
        options: [
            'css',
            'graphql',
            'handlebars',
            'html',
            'javascript',
            'json',
            'markdown',
            'scss',
            'typescript',
            'xml',
            'yaml',
        ],
        onChange() {
            this.notes.setProperty('language', this.language.value as any);
        },
        helperText: 'For code syntax validation',
    })
    language: ui.fields.Select;

    @ui.decorators.numericField<PluginMonaco>({
        parent() {
            return this.block;
        },
        isTransient: true,
        title: 'Height',
        helperText: 'Editor height in pixels',
        min: 50,
        max: 1000,
        onChange() {
            this.notes.setProperty('height', this.height.value);
        },
    })
    height: ui.fields.Numeric;

    @ui.decorators.pluginField<PluginMonaco, MonacoPluginProperties>({
        parent() {
            return this.block;
        },
        title: 'Code',
        helperText: 'Source code of something',
        isFullWidth: true,
        pluginPackage: '@sage/xtrem-ui-plugin-monaco',
        language: 'typescript',
        height: 400,
        handlebarsSuggestions: [
            {
                label: 'xtremSystem',
                insertText: 'xtremSystem',
                children: [
                    {
                        label: 'company',
                        insertText: 'company',
                        children: [
                            {
                                label: 'query',
                                insertText: 'query',
                                children: [
                                    {
                                        label: 'edges',
                                        insertText: 'edges',
                                        isArray: true,
                                        children: [
                                            {
                                                label: 'node',
                                                insertText: 'node',
                                                children: [
                                                    {
                                                        label: 'id',
                                                        insertText: 'id',
                                                        children: [],
                                                    },
                                                    {
                                                        label: 'legalCompany',
                                                        insertText: 'legalCompany',
                                                        children: [
                                                            {
                                                                label: 'id',
                                                                insertText: 'id',
                                                                children: [],
                                                            },
                                                            {
                                                                label: 'isActive',
                                                                insertText: 'isActive',
                                                                children: [],
                                                            },
                                                        ],
                                                    },
                                                    {
                                                        label: 'linkedSites',
                                                        insertText: 'linkedSites',
                                                        children: [
                                                            {
                                                                label: 'query',
                                                                insertText: 'query',
                                                                children: [
                                                                    {
                                                                        label: 'edges',
                                                                        insertText: 'edges',
                                                                        isArray: true,
                                                                        children: [
                                                                            {
                                                                                label: 'node',
                                                                                insertText: 'node',
                                                                                children: [
                                                                                    {
                                                                                        label: 'id',
                                                                                        insertText: 'id',
                                                                                        children: [],
                                                                                    },
                                                                                    {
                                                                                        label: 'legalCompany',
                                                                                        insertText: 'legalCompany',
                                                                                        children: [
                                                                                            {
                                                                                                label: 'id',
                                                                                                insertText: 'id',
                                                                                                children: [],
                                                                                            },
                                                                                        ],
                                                                                    },
                                                                                ],
                                                                            },
                                                                        ],
                                                                    },
                                                                ],
                                                            },
                                                        ],
                                                    },
                                                ],
                                            },
                                        ],
                                    },
                                ],
                            },
                        ],
                    },
                ],
            },
        ] as EditorSuggestion[],
    })
    notes: ui.fields.Plugin<MonacoPluginProperties>;

    @ui.decorators.pageAction<PluginMonaco>({
        title: 'Save',
        async onClick() {
            await this.$.graph.update();
            this.$.showToast(`Updated entry: ${this._id.value}`, {
                type: 'success',
            });
        },
    })
    update: ui.PageAction;
}
