import * as ui from '@sage/xtrem-ui';
import type { ShowCaseProductCategory } from '@sage/xtrem-show-case-api';
import { fields } from '../menu-items/fields';

@ui.decorators.page<Select>({
    authorizationCode: 'BSCFLDS',
    module: 'show-case',
    title: 'Field - Select',
    category: 'SHOWCASE',
    isTransient: true,
    menuItem: fields,
})
export class Select extends ui.Page {
    @ui.decorators.section<Select>({
        title: 'Select field',
    })
    section: ui.containers.Section;

    @ui.decorators.block<Select>({
        parent() {
            return this.section;
        },
        title: 'Field example',
    })
    fieldBlock: ui.containers.Block;

    @ui.decorators.selectField<Select>({
        parent() {
            return this.fieldBlock;
        },
        onChange() {
            if (this.field.value !== this.value.value) {
                this.value.value = this.field.value;
                this.changeTriggered.isHidden = false;
                setTimeout(() => {
                    this.changeTriggered.isHidden = true;
                }, 5000);
            }
        },
        isSoundDisabled: true,
        optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
    })
    field: ui.fields.Select<ShowCaseProductCategory>;

    @ui.decorators.labelField<Select>({
        parent() {
            return this.fieldBlock;
        },
        isHidden: true,
        map() {
            return 'Changed was triggered';
        },
    })
    changeTriggered: ui.fields.Label;

    @ui.decorators.block<Select>({
        parent() {
            return this.section;
        },
        title: 'Configuration',
    })
    configurationBlock: ui.containers.Block;

    @ui.decorators.block<Select>({
        parent() {
            return this.section;
        },
        title: 'Dynamic Options',
    })
    changeOptionsBlock: ui.containers.Block;

    @ui.decorators.selectField<Select>({
        parent() {
            return this.changeOptionsBlock;
        },
        title: 'Dynamic',
        options: ['Dreadful', 'Awful', 'Awesome'],
    })
    dynamicField: ui.fields.Select;

    @ui.decorators.textField<Select>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Helper text',
        onChange() {
            this.field.helperText = this.helperText.value;
        },
    })
    helperText: ui.fields.Text;

    @ui.decorators.selectField<Select>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Icon',
        onChange() {
            this.field.icon = this.icon.value as any;
        },
        options: ['add', 'calendar', 'edit', 'gift', 'image', 'ledger', 'pause_circle', 'refresh', 'tag', 'video'],
    })
    icon: ui.fields.Select;

    @ui.decorators.checkboxField<Select>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is disabled',
        onChange() {
            this.field.isDisabled = this.isDisabled.value;
        },
    })
    isDisabled: ui.fields.Checkbox;

    @ui.decorators.checkboxField<Select>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is helper text hidden',
        onChange() {
            this.field.isHelperTextHidden = this.isHelperTextHidden.value;
        },
    })
    isHelperTextHidden: ui.fields.Checkbox;

    @ui.decorators.checkboxField<Select>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is hidden',
        onChange() {
            this.field.isHidden = this.isHidden.value;
        },
    })
    isHidden: ui.fields.Checkbox;

    @ui.decorators.checkboxField<Select>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is readOnly',
        onChange() {
            this.field.isReadOnly = this.isReadOnly.value;
        },
    })
    isReadOnly: ui.fields.Checkbox;

    @ui.decorators.checkboxField<Select>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is title hidden',
        onChange() {
            this.field.isTitleHidden = this.isTitleHidden.value;
        },
    })
    isTitleHidden: ui.fields.Checkbox;

    @ui.decorators.textField<Select>({
        title: 'Placeholder',
        onChange() {
            this.field.placeholder = this.placeholder.value;
        },
        parent() {
            return this.configurationBlock;
        },
    })
    placeholder: ui.fields.Text;

    @ui.decorators.textField<Select>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Title',
        onChange() {
            this.field.title = this.title.value;
        },
    })
    title: ui.fields.Text;

    @ui.decorators.selectField<Select>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Value',
        onChange() {
            if (this.value.value !== this.field.value) {
                this.field.value = this.value.value;
            }
        },
        optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
    })
    value: ui.fields.Select<ShowCaseProductCategory>;

    @ui.decorators.buttonField<Select>({
        parent() {
            return this.configurationBlock;
        },
        map() {
            return 'Focus field';
        },
        onClick() {
            this.field.focus();
        },
    })
    focus: ui.fields.Button;

    @ui.decorators.buttonField<Select>({
        parent() {
            return this.changeOptionsBlock;
        },
        map() {
            return 'Set Options';
        },
        onClick() {
            this.dynamicField.options = ['Awesome', 'Excellent'];
        },
    })
    setOptions: ui.fields.Button;

    /* Additional examples */

    @ui.decorators.block<Select>({
        parent() {
            return this.section;
        },
        title: 'Additional examples',
    })
    additionalBlock: ui.containers.Block;

    @ui.decorators.selectField<Select>({
        parent() {
            return this.additionalBlock;
        },
        title: 'Mandatory',
        isMandatory: true,
        optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
    })
    mandatory: ui.fields.Select<ShowCaseProductCategory>;

    @ui.decorators.selectField<Select>({
        parent() {
            return this.additionalBlock;
        },
        title: 'Full width',
        isFullWidth: true,
        optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
    })
    fullWidth: ui.fields.Select<ShowCaseProductCategory>;

    @ui.decorators.selectField<Select>({
        parent() {
            return this.additionalBlock;
        },
        title: 'Sorted with option type',
        isSortedAlphabetically: true,
        optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
    })
    sortedOptionType: ui.fields.Select<ShowCaseProductCategory>;

    @ui.decorators.selectField<Select>({
        parent() {
            return this.additionalBlock;
        },
        title: 'Sorted with options',
        isSortedAlphabetically: true,
        options: ['Dreadful', 'Awful', 'Great', 'Bad', 'Mediocre'],
    })
    sortedWithOptions: ui.fields.Select<'Dreadful' | 'Awful' | 'Great' | 'Bad' | 'Mediocre'>;

    @ui.decorators.separatorField<Select>({
        parent() {
            return this.additionalBlock;
        },
        isFullWidth: true,
    })
    fieldSeparator1: ui.fields.Separator;

    @ui.decorators.selectField<Select>({
        parent() {
            return this.additionalBlock;
        },
        title: 'With warning message',
        warningMessage: 'Wow, warning!',
        optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
    })
    warningMessageField: ui.fields.Select<ShowCaseProductCategory>;

    @ui.decorators.selectField<Select>({
        parent() {
            return this.additionalBlock;
        },
        title: 'With warning message with callback',
        helperText: 'Select "awful"',
        warningMessage() {
            if (this.warningMessageWithCallbackField.value === 'awful') {
                return 'Warning message';
            }
            return null;
        },
        optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
    })
    warningMessageWithCallbackField: ui.fields.Select<ShowCaseProductCategory>;

    @ui.decorators.selectField<Select>({
        parent() {
            return this.additionalBlock;
        },
        title: 'With info message',
        infoMessage: 'Wow, warning!',
        optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
    })
    infoMessageField: ui.fields.Select<ShowCaseProductCategory>;

    @ui.decorators.selectField<Select>({
        parent() {
            return this.additionalBlock;
        },
        title: 'With info message with callback',
        helperText: 'Type "awful"',
        infoMessage() {
            if (this.infoMessageWithCallbackField.value === 'awful') {
                return 'Info message';
            }
            return null;
        },
        optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
    })
    infoMessageWithCallbackField: ui.fields.Select<ShowCaseProductCategory>;

    @ui.decorators.selectField<Select>({
        parent() {
            return this.additionalBlock;
        },
        title: 'With info message',
        warningMessage: 'Wow, warning!',
        infoMessage: 'You should not see this',
        optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
    })
    infoAndWarningMessageField: ui.fields.Select<ShowCaseProductCategory>;

    @ui.decorators.selectField<Select>({
        parent() {
            return this.additionalBlock;
        },
        validation() {
            if (this.infoAndWarningMessageMandatoryField.value === 'good') {
                return 'Error message';
            }
            return '';
        },
        title: 'Info, warning and validation',
        warningMessage: 'Wow, warning!',
        infoMessage: 'You should not see this',
        helperText: 'Not allowed to select "good"',
        optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
    })
    infoAndWarningMessageMandatoryField: ui.fields.Select<ShowCaseProductCategory>;
}
