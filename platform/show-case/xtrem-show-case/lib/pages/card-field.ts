import { <PERSON><PERSON>h<PERSON><PERSON>, ShowCaseProduct, ShowCaseProvider } from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import { fields } from '../menu-items/fields';

@ui.decorators.page<CardField, ShowCaseProduct>({
    authorizationCode: 'BSCFLDDL',
    module: 'show-case',
    title: 'Field - Card',
    node: '@sage/xtrem-show-case/ShowCaseProduct',
    category: 'SHOWCASE',
    defaultEntry: () => '1',
    mode: 'tabs',
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.text({ bind: 'product' }),
            titleRight: ui.nestedFields.text({ bind: '_id' }),
            line2: ui.nestedFields.text({ bind: 'description', canFilter: false }),
            line2Right: ui.nestedFields.numeric({ bind: 'listPrice', canFilter: false }),
            line3: ui.nestedFields.text({ bind: 'barcode', canFilter: false }),
            line3Right: ui.nestedFields.label({
                bind: 'category',
                title: 'Category',
                optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
            }),
            line4: ui.nestedFields.date({ bind: 'releaseDate', canFilter: false }),
            line4Right: ui.nestedFields.date({ bind: 'endingDate', canFilter: false }),
            line5: ui.nestedFields.numeric({ bind: 'discount', canFilter: false }),
            line5Right: ui.nestedFields.text({ bind: 'tax', canFilter: false }),
        },
    },
    onLoad() {
        this.progressBar.value = {
            _id: '123',
            product: 'Show case product',
            progress: 100,
            description: 'Lorem ipsum',
        };
        this.transientFieldFullWidth.value = {
            _id: '123',
            product: 'Show case product',
            progress: 100,
            description: 'Lorem ipsum',
        };
        this.transientFieldNoImage.value = {
            _id: '123',
            product: 'Show case product',
            progress: 100,
            description: 'Lorem ipsum',
        };
        this.longLines.value = {
            _id: '123',
            product:
                'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Praesent vitae purus nec massa bibendum ullamcorper sed mollis ex.',
            description:
                'Quisque pellentesque lectus quis dapibus eleifend. Curabitur nec eleifend lacus. Ut massa lacus, eleifend ac pharetra at, finibus id sem. Donec eget aliquam eros, id gravida velit.',
        };
    },
    menuItem: fields,
})
export class CardField extends ui.Page<GraphApi> {
    @ui.decorators.section<CardField>({
        isTitleHidden: true,
        title: 'Bound examples',
    })
    boundSection: ui.containers.Section;

    @ui.decorators.block<CardField>({
        isTitleHidden: true,
        parent() {
            return this.boundSection;
        },
    })
    fieldBlock: ui.containers.Block;

    @ui.decorators.cardField<CardField, ShowCaseProvider>({
        parent() {
            return this.fieldBlock;
        },
        cardDefinition: {
            title: ui.nestedFields.text({
                bind: '_id',
                title: 'ID:',
            }),
            titleRight: ui.nestedFields.text({
                bind: 'textField',
                title: 'Text field',
            }),
            line2: ui.nestedFields.text({
                bind: 'dateField',
                title: 'Text field',
            }),
            image: ui.nestedFields.image({
                bind: 'logo',
            }),
        },
        onClick() {
            this.clickTriggered.isHidden = false;
            setTimeout(() => {
                this.clickTriggered.isHidden = true;
            }, 2500);
        },
    })
    provider: ui.fields.Card;

    @ui.decorators.labelField<CardField>({
        isTransient: true,
        parent() {
            return this.fieldBlock;
        },
        isHidden: true,
        map() {
            return 'Click was triggered';
        },
    })
    clickTriggered: ui.fields.Label;

    @ui.decorators.block<CardField>({
        parent() {
            return this.boundSection;
        },
        title: 'Configuration',
    })
    configurationNonTransientBlock: ui.containers.Block;

    @ui.decorators.checkboxField<CardField>({
        isTransient: true,
        parent() {
            return this.configurationNonTransientBlock;
        },
        title: 'Is disabled',
        onChange() {
            this.provider.isDisabled = this.isDisabledNonTransient.value;
        },
    })
    isDisabledNonTransient: ui.fields.Checkbox;

    @ui.decorators.checkboxField<CardField>({
        isTransient: true,
        parent() {
            return this.configurationNonTransientBlock;
        },
        title: 'Is helper text hidden',
        onChange() {
            this.provider.isHelperTextHidden = this.isHelperTextHiddenNonTransient.value;
        },
    })
    isHelperTextHiddenNonTransient: ui.fields.Checkbox;

    @ui.decorators.checkboxField<CardField>({
        isTransient: true,
        parent() {
            return this.configurationNonTransientBlock;
        },
        title: 'Is hidden',
        onChange() {
            this.provider.isHidden = this.isHiddenNonTransient.value;
        },
    })
    isHiddenNonTransient: ui.fields.Checkbox;

    @ui.decorators.checkboxField<CardField>({
        isTransient: true,
        parent() {
            return this.configurationNonTransientBlock;
        },
        title: 'Is title hidden',
        onChange() {
            this.provider.isTitleHidden = this.isTitleHiddenNonTransient.value;
        },
    })
    isTitleHiddenNonTransient: ui.fields.Checkbox;

    @ui.decorators.textField<CardField>({
        isTransient: true,
        parent() {
            return this.configurationNonTransientBlock;
        },
        title: 'Title',
        onChange() {
            this.provider.title = this.titleNonTransient.value;
        },
    })
    titleNonTransient: ui.fields.Text;

    @ui.decorators.textField<CardField>({
        isTransient: true,
        parent() {
            return this.configurationNonTransientBlock;
        },
        title: 'Helper text',
        onChange() {
            this.provider.helperText = this.helperTextNonTransient.value;
        },
    })
    helperTextNonTransient: ui.fields.Text;

    @ui.decorators.section<CardField>({ isTitleHidden: true, title: 'Transient example' })
    transientSection: ui.containers.Section;

    @ui.decorators.block<CardField>({
        isTitleHidden: true,
        parent() {
            return this.transientSection;
        },
    })
    transientFieldBlock: ui.containers.Block;

    @ui.decorators.cardField<CardField, ShowCaseProduct>({
        isTransient: true,
        parent() {
            return this.transientFieldBlock;
        },
        cardDefinition: {
            title: ui.nestedFields.text({
                bind: 'product',
            }),
            line2Right: ui.nestedFields.text({
                bind: '_id',
            }),
            line2: ui.nestedFields.text({
                bind: 'description',
            }),
            image: ui.nestedFields.image({
                bind: 'imageField',
            }),
        },
        onClick() {
            this.transientClickTriggered.isHidden = false;
            setTimeout(() => {
                this.transientClickTriggered.isHidden = true;
            }, 2500);
        },
    })
    transientField: ui.fields.Card;

    @ui.decorators.labelField<CardField>({
        isTransient: true,
        parent() {
            return this.transientFieldBlock;
        },
        isHidden: true,
        map() {
            return 'Click was triggered';
        },
    })
    transientClickTriggered: ui.fields.Label;

    @ui.decorators.block<CardField>({
        parent() {
            return this.transientSection;
        },
        title: 'Configuration',
    })
    configurationTransientBlock: ui.containers.Block;

    @ui.decorators.checkboxField<CardField>({
        isTransient: true,
        parent() {
            return this.configurationTransientBlock;
        },
        title: 'Is disabled',
        onChange() {
            this.transientField.isDisabled = this.isDisabledTransient.value;
        },
    })
    isDisabledTransient: ui.fields.Checkbox;

    @ui.decorators.checkboxField<CardField>({
        isTransient: true,
        parent() {
            return this.configurationTransientBlock;
        },
        title: 'Is helper text hidden',
        onChange() {
            this.transientField.isHelperTextHidden = this.isHelperTextHiddenTransient.value;
        },
    })
    isHelperTextHiddenTransient: ui.fields.Checkbox;

    @ui.decorators.checkboxField<CardField>({
        isTransient: true,
        parent() {
            return this.configurationTransientBlock;
        },
        title: 'Is hidden',
        onChange() {
            this.transientField.isHidden = this.isHiddenTransient.value;
        },
    })
    isHiddenTransient: ui.fields.Checkbox;

    @ui.decorators.checkboxField<CardField>({
        isTransient: true,
        parent() {
            return this.configurationTransientBlock;
        },
        title: 'Is title hidden',
        onChange() {
            this.transientField.isTitleHidden = this.isTitleHiddenTransient.value;
        },
    })
    isTitleHiddenTransient: ui.fields.Checkbox;

    @ui.decorators.textField<CardField>({
        isTransient: true,
        parent() {
            return this.configurationTransientBlock;
        },
        title: 'Title',
        onChange() {
            this.transientField.title = this.titleTransient.value;
        },
    })
    titleTransient: ui.fields.Text;

    @ui.decorators.textField<CardField>({
        isTransient: true,
        parent() {
            return this.configurationTransientBlock;
        },
        title: 'Helper text',
        onChange() {
            this.transientField.helperText = this.helperTextTransient.value;
        },
    })
    helperTextTransient: ui.fields.Text;

    @ui.decorators.referenceField<CardField, ShowCaseProduct>({
        isTransient: true,
        title: 'Value of transient examples',
        columns: [
            ui.nestedFields.text({ bind: '_id', title: 'ID', canFilter: false }),
            ui.nestedFields.text({ bind: 'product', title: 'Product', canFilter: true }),
            ui.nestedFields.text({ bind: 'description', canFilter: true, title: 'Description' }),
            ui.nestedFields.image({ bind: 'imageField' }),
        ],
        valueField: 'product',
        width: 'medium',
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        onChange() {
            this.transientField.value = this.transientValue.value;
            this.transientFieldNoImage.value = this.transientValue.value;
            this.transientFieldFullWidth.value = this.transientValue.value;
        },
        parent() {
            return this.configurationTransientBlock;
        },
    })
    transientValue: ui.fields.Reference;

    @ui.decorators.block<CardField>({
        parent() {
            return this.transientSection;
        },
        title: 'Additional Examples',
    })
    additionalExamples: ui.containers.Block;

    @ui.decorators.cardField<CardField, ShowCaseProduct>({
        isTransient: true,
        title: 'No image',
        parent() {
            return this.additionalExamples;
        },
        cardDefinition: {
            title: ui.nestedFields.text({
                bind: 'product',
            }),
            line2Right: ui.nestedFields.text({
                bind: '_id',
            }),
            line2: ui.nestedFields.text({
                bind: 'description',
            }),
        },
    })
    transientFieldNoImage: ui.fields.Card;

    @ui.decorators.cardField<CardField, ShowCaseProduct>({
        isTransient: true,
        isFullWidth: true,
        title: 'Full width',
        parent() {
            return this.additionalExamples;
        },
        cardDefinition: {
            title: ui.nestedFields.text({
                bind: 'product',
            }),
            line2Right: ui.nestedFields.text({
                bind: '_id',
            }),
            line2: ui.nestedFields.text({
                bind: 'description',
            }),
            image: ui.nestedFields.image({
                bind: 'imageField',
            }),
        },
    })
    transientFieldFullWidth: ui.fields.Card;

    @ui.decorators.cardField<CardField, ShowCaseProduct>({
        isTransient: true,
        title: 'With a progress bar',
        parent() {
            return this.additionalExamples;
        },
        cardDefinition: {
            title: ui.nestedFields.text({
                bind: 'product',
            }),
            line2Right: ui.nestedFields.text({
                bind: '_id',
            }),
            line2: ui.nestedFields.text({
                bind: 'description',
            }),
            progressBar: ui.nestedFields.progress({
                bind: 'progress',
                areProgressLabelsHidden: true,
            }),
        },
    })
    progressBar: ui.fields.Card;

    @ui.decorators.section<CardField>({
        title: 'Size Examples',
    })
    sizeSection: ui.containers.Section;

    @ui.decorators.block<CardField>({
        parent() {
            return this.sizeSection;
        },
    })
    sizeBlock: ui.containers.Block;

    @ui.decorators.cardField<CardField, ShowCaseProvider>({
        bind: 'provider',
        cardDefinition: {
            image: ui.nestedFields.image({
                bind: 'logo',
            }),
            title: ui.nestedFields.text({
                bind: 'textField',
                title: 'Provider',
            }),
            titleRight: ui.nestedFields.label({
                bind: '_id',
                title: 'Id',
                backgroundColor: '#335b70',
                borderColor: '#335b70',
                color: '#ffffff',
            }),
        },
        isFullWidth: true,
        parent() {
            return this.sizeBlock;
        },
        title: 'Single Line Card',
        width: 'medium',
    })
    singleLine: ui.fields.Card;

    @ui.decorators.cardField<CardField, ShowCaseProvider>({
        bind: 'provider',
        cardDefinition: {
            image: ui.nestedFields.image({
                bind: 'logo',
            }),
            title: ui.nestedFields.text({
                bind: 'textField',
                title: 'Provider',
            }),
            titleRight: ui.nestedFields.label({
                bind: '_id',
                title: 'Id',
                backgroundColor: '#335b70',
                borderColor: '#335b70',
                color: '#ffffff',
            }),
            line2: ui.nestedFields.text({
                bind: 'concatenatedAddress',
                title: 'Address',
            }),
            line2Right: ui.nestedFields.text({
                bind: 'dateField',
                title: 'Date',
            }),
        },
        isFullWidth: true,
        parent() {
            return this.sizeBlock;
        },
        title: 'Double Line Card',
        width: 'medium',
    })
    doubleLine: ui.fields.Card;

    @ui.decorators.cardField<CardField, ShowCaseProvider>({
        bind: 'provider',
        cardDefinition: {
            image: ui.nestedFields.image({
                bind: 'logo',
            }),
            title: ui.nestedFields.text({
                bind: 'textField',
                title: 'Provider',
            }),
            titleRight: ui.nestedFields.label({
                bind: '_id',
                title: 'Id',
                backgroundColor: '#335b70',
                borderColor: '#335b70',
                color: '#ffffff',
            }),
            line2: ui.nestedFields.text({
                bind: 'concatenatedAddress',
                title: 'Address',
            }),
            line3: ui.nestedFields.link({
                bind: 'textField',
                title: 'Provider',
            }),
            line3Right: ui.nestedFields.text({
                bind: 'dateField',
                title: 'Date',
            }),
        },
        isFullWidth: true,
        parent() {
            return this.sizeBlock;
        },
        title: 'Triple Line Card',
        width: 'medium',
    })
    tripleLine: ui.fields.Card;

    @ui.decorators.cardField<CardField, ShowCaseProvider>({
        bind: 'provider',
        cardDefinition: {
            image: ui.nestedFields.image({
                bind: 'logo',
            }),
            title: ui.nestedFields.text({
                bind: 'textField',
                title: 'Provider',
            }),
            titleRight: ui.nestedFields.label({
                bind: '_id',
                title: 'ID',
                backgroundColor: '#335b70',
                borderColor: '#335b70',
                color: '#ffffff',
            }),
            line2: ui.nestedFields.text({
                bind: 'concatenatedAddress',
                title: 'Address',
            }),
            line3: ui.nestedFields.link({
                bind: 'textField',
                title: 'Provider',
            }),
            line3Right: ui.nestedFields.text({
                bind: 'dateField',
                title: 'Date',
            }),
            line4: ui.nestedFields.reference({
                bind: 'item',
                valueField: 'name',
                node: '@sage/xtrem-show-case/ShowCaseProduct',
                title: 'Flagship product',
            }),
            line4Right: ui.nestedFields.reference({
                bind: { siteAddress: true },
                valueField: 'name',
                node: '@sage/xtrem-show-case/ShowCaseProviderAddress',
                title: 'Site address',
            }),
        },
        isFullWidth: true,
        parent() {
            return this.sizeBlock;
        },
        title: 'Quadruple line card',
        width: 'medium',
    })
    quadrupleLine: ui.fields.Card;

    @ui.decorators.cardField<CardField, ShowCaseProvider>({
        bind: 'provider',
        cardDefinition: {
            image: ui.nestedFields.image({
                bind: 'logo',
            }),
            title: ui.nestedFields.text({
                bind: 'textField',
                title: 'Provider',
            }),
            titleRight: ui.nestedFields.label({
                bind: '_id',
                title: 'ID',
                backgroundColor: '#335b70',
                borderColor: '#335b70',
                color: '#ffffff',
            }),
            line2: ui.nestedFields.text({
                bind: 'concatenatedAddress',
                title: 'Address',
            }),
            line3: ui.nestedFields.link({
                bind: 'textField',
                title: 'Provider',
            }),
            line3Right: ui.nestedFields.text({
                bind: 'dateField',
                title: 'Date',
            }),
            line4: ui.nestedFields.reference({
                bind: 'item',
                valueField: 'name',
                node: '@sage/xtrem-show-case/ShowCaseProduct',
                title: 'Flagship product',
            }),
            line4Right: ui.nestedFields.reference({
                bind: { siteAddress: true },
                valueField: 'name',
                node: '@sage/xtrem-show-case/ShowCaseProviderAddress',
                title: 'Site address',
            }),
            line5: ui.nestedFields.numeric({
                bind: 'minQuantity',
                title: 'Min. quantity',
            }),
            line5Right: ui.nestedFields.numeric({
                bind: 'decimalField',
                title: 'Decimal',
            }),
        },
        isFullWidth: true,
        parent() {
            return this.sizeBlock;
        },
        title: 'Quintuple line card',
        width: 'medium',
    })
    quintupleLine: ui.fields.Card;

    @ui.decorators.cardField<CardField, any>({
        isTransient: true,
        cardDefinition: {
            title: ui.nestedFields.text({
                bind: 'product',
                title: 'Provider',
            }),
            line2: ui.nestedFields.text({
                bind: 'description',
                title: 'Address',
            }),
        },
        parent() {
            return this.additionalExamples;
        },
        title: 'Long lines on card field',
    })
    longLines: ui.fields.Card;
}
