import { Graph<PERSON><PERSON>, ShowCaseProduct } from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import { misc } from '../menu-items/misc';

@ui.decorators.page<UniqueTableActions>({
    authorizationCode: 'UNQTBLACTN',
    category: 'SHOWCASE',
    menuItem: misc,
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.text({
                bind: 'textField',
                title: 'Provider',
            }),
            line2: ui.nestedFields.text({
                bind: '_id',
                title: 'Id',
            }),
        },
    },
    node: '@sage/xtrem-show-case/ShowCaseProvider',
    title: 'Unique Table Actions',
})
export class UniqueTableActions extends ui.Page<GraphApi> {
    @ui.decorators.section<UniqueTableActions>({})
    section: ui.containers.Section;

    @ui.decorators.block<UniqueTableActions>({
        parent() {
            return this.section;
        },
    })
    block: ui.containers.Block;

    @ui.decorators.block<UniqueTableActions>({
        parent() {
            return this.section;
        },
        title: 'Actions',
    })
    actions: ui.containers.Block;

    @ui.decorators.textField<UniqueTableActions>({
        bind: '_id',
        isReadOnly: true,
        parent() {
            return this.block;
        },
        title: 'Id',
        width: 'medium',
    })
    id: ui.fields.Text;

    @ui.decorators.textField<UniqueTableActions>({
        bind: 'textField',
        isReadOnly: true,
        parent() {
            return this.block;
        },
        title: 'Provider',
        width: 'medium',
    })
    provider: ui.fields.Text;

    @ui.decorators.tableField<UniqueTableActions, ShowCaseProduct>({
        bind: 'products',
        canSelect: false,
        columns: [
            ui.nestedFields.text({
                bind: '_id',
                title: 'Id',
            }),
            ui.nestedFields.text({
                bind: 'product',
                title: 'Product',
            }),
            ui.nestedFields.reference({
                bind: 'provider',
                isReadOnly: true,
                node: '@sage/xtrem-show-case/ShowCaseProvider',
                title: 'Provider',
                valueField: 'textField',
            }),
        ],
        dropdownActions: [
            {
                icon: 'analysis',
                onClick() {
                    this.$.showToast('This is an dropdown action with no action id and title "Analysis".');
                },
                title: 'Analysis',
            },
            {
                id: 'myCollaborateAction',
                icon: 'collaborate',
                onClick() {
                    this.$.showToast(
                        'This is an dropdown action with action id "myCollaborateAction" and title "Collaborate".',
                    );
                },
                title: 'Collaborate',
            },
        ],
        inlineActions: [
            {
                icon: 'analysis',
                onClick() {
                    this.$.showToast('This is an inline action with no action id and title "Analysis".');
                },
                title: 'Analysis',
            },
            {
                id: 'myCollaborateAction',
                icon: 'collaborate',
                onClick() {
                    this.$.showToast(
                        'This is an inline action with action id "myCollaborateAction" and title "Collaborate".',
                    );
                },
                title: 'Collaborate',
            },
        ],
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        orderBy: { _id: -1 },
        parent() {
            return this.actions;
        },
    })
    products: ui.fields.Table<ShowCaseProduct>;
}
