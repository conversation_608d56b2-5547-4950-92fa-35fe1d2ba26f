import * as ui from '@sage/xtrem-ui';
import { misc } from '../menu-items/misc';

@ui.decorators.page<PageHeaderCardFourLines>({
    areNavigationTabsHidden: true,
    authorizationCode: 'HDRCRD',
    title: 'Four lines',
    module: 'show-case',
    category: 'SHOWCASE',
    menuItem: misc,
    isTransient: true,
    headerCard() {
        return {
            title: this.headerText1,
            titleRight: this.headerText2,
            line2: this.headerText3,
            line2Right: this.headerText4,
            line3: this.headerText5,
            line3Right: this.headerText6,
            line4: this.headerText7,
            line4Right: this.headerText8,
        };
    },
    onLoad() {
        this.headerText1.value = 'Wasabi';
        this.headerText2.value = 'tastes';
        this.headerText3.value = 'VERY';
        this.headerText4.value = 'spicy';
        this.headerText5.value = 'Another';
        this.headerText6.value = 'Line';
        this.headerText7.value = 'More';
        this.headerText8.value = 'Line';
    },
})
export class PageHeaderCardFourLines extends ui.Page {
    @ui.decorators.textField<PageHeaderCardFourLines>({
        isReadOnly: true,
    })
    headerText1: ui.fields.Text;

    @ui.decorators.textField<PageHeaderCardFourLines>({})
    headerText2: ui.fields.Text;

    @ui.decorators.textField<PageHeaderCardFourLines>({})
    headerText3: ui.fields.Text;

    @ui.decorators.textField<PageHeaderCardFourLines>({
        isReadOnly: true,
    })
    headerText4: ui.fields.Text;

    @ui.decorators.textField<PageHeaderCardFourLines>({})
    headerText5: ui.fields.Text;

    @ui.decorators.textField<PageHeaderCardFourLines>({
        isReadOnly: true,
    })
    headerText6: ui.fields.Text;

    @ui.decorators.textField<PageHeaderCardFourLines>({
        isReadOnly: true,
    })
    headerText7: ui.fields.Text;

    @ui.decorators.textField<PageHeaderCardFourLines>({
        isReadOnly: true,
    })
    headerText8: ui.fields.Text;

    @ui.decorators.section<PageHeaderCardFourLines>({
        title: 'Page body',
    })
    section: ui.containers.Section;

    @ui.decorators.block<PageHeaderCardFourLines>({
        parent() {
            return this.section;
        },
    })
    fieldBlock: ui.containers.Block;

    @ui.decorators.labelField<PageHeaderCardFourLines>({
        parent() {
            return this.fieldBlock;
        },
        map() {
            return 'Nothing to see on this page except on XS breakpoint';
        },
    })
    infoLabel: ui.fields.Label;

    @ui.decorators.textAreaField<PageHeaderCardFourLines>({
        parent() {
            return this.fieldBlock;
        },
        rows: 15,
        isFullWidth: true,
        title: 'Large text area',
        helperText: 'This field is to trigger scrolling',
    })
    textArea: ui.fields.TextArea;
}
