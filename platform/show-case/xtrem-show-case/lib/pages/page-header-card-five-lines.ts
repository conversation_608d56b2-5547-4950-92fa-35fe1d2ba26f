import * as ui from '@sage/xtrem-ui';
import { misc } from '../menu-items/misc';

@ui.decorators.page<PageHeaderCardFiveLines>({
    areNavigationTabsHidden: true,
    authorizationCode: 'HDRCRD',
    title: 'Five lines',
    module: 'show-case',
    category: 'SHOWCASE',
    menuItem: misc,
    isTransient: true,
    headerCard() {
        return {
            title: this.headerText1,
            titleRight: this.headerText2,
            line2: this.headerText3,
            line2Right: this.headerText4,
            line3: this.headerText5,
            line3Right: this.headerText6,
            line4: this.headerText7,
            line4Right: this.headerText8,
            line5: this.headerText9,
            line5Right: this.headerText10,
        };
    },
    onLoad() {
        this.headerText1.value = 'Wasabi';
        this.headerText2.value = 'tastes';
        this.headerText3.value = 'VERY';
        this.headerText4.value = 'spicy';
        this.headerText5.value = 'Another';
        this.headerText6.value = 'Line';
        this.headerText7.value = 'More';
        this.headerText8.value = 'Line';
        this.headerText9.value = 'Even';
        this.headerText10.value = 'More';
    },
})
export class PageHeaderCardFiveLines extends ui.Page {
    @ui.decorators.textField<PageHeaderCardFiveLines>({
        isReadOnly: true,
    })
    headerText1: ui.fields.Text;

    @ui.decorators.textField<PageHeaderCardFiveLines>({})
    headerText2: ui.fields.Text;

    @ui.decorators.textField<PageHeaderCardFiveLines>({})
    headerText3: ui.fields.Text;

    @ui.decorators.textField<PageHeaderCardFiveLines>({
        isReadOnly: true,
    })
    headerText4: ui.fields.Text;

    @ui.decorators.textField<PageHeaderCardFiveLines>({})
    headerText5: ui.fields.Text;

    @ui.decorators.textField<PageHeaderCardFiveLines>({
        isReadOnly: true,
    })
    headerText6: ui.fields.Text;

    @ui.decorators.textField<PageHeaderCardFiveLines>({
        isReadOnly: true,
    })
    headerText7: ui.fields.Text;

    @ui.decorators.textField<PageHeaderCardFiveLines>({
        isReadOnly: true,
    })
    headerText8: ui.fields.Text;

    @ui.decorators.textField<PageHeaderCardFiveLines>({
        isReadOnly: true,
    })
    headerText9: ui.fields.Text;

    @ui.decorators.textField<PageHeaderCardFiveLines>({
        isReadOnly: true,
    })
    headerText10: ui.fields.Text;

    @ui.decorators.section<PageHeaderCardFiveLines>({
        title: 'Page body',
    })
    section: ui.containers.Section;

    @ui.decorators.block<PageHeaderCardFiveLines>({
        parent() {
            return this.section;
        },
    })
    fieldBlock: ui.containers.Block;

    @ui.decorators.labelField<PageHeaderCardFiveLines>({
        parent() {
            return this.fieldBlock;
        },
        map() {
            return 'Nothing to see on this page except on XS breakpoint';
        },
    })
    infoLabel: ui.fields.Label;

    @ui.decorators.textAreaField<PageHeaderCardFiveLines>({
        parent() {
            return this.fieldBlock;
        },
        rows: 15,
        isFullWidth: true,
        title: 'Large text area',
        helperText: 'This field is to trigger scrolling',
    })
    textArea: ui.fields.TextArea;
}
