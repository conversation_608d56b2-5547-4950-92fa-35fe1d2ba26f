import { Graph<PERSON><PERSON> } from '@sage/xtrem-show-case-api';
import { ShowCaseProduct, ShowCaseProductOriginAddress } from '@sage/xtrem-show-case-api-partial';
import * as ui from '@sage/xtrem-ui';
import { vitalPod } from '../menu-items/vital-pod';

@ui.decorators.page<VitalPodField, ShowCaseProduct>({
    authorizationCode: 'SHCPRVD',
    category: 'SHOWCASE',
    menuItem: vitalPod,
    createAction() {
        return this.create;
    },
    headerDropDownActions() {
        return [this.delete];
    },
    businessActions() {
        return [this.toggleNavigationPanel, this.update];
    },
    module: 'show-case',
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.text({ bind: 'product' }),
            line2: ui.nestedFields.text({ bind: '_id' }),
            line3: ui.nestedFields.date({ bind: 'releaseDate' }),
        },
    },
    node: '@sage/xtrem-show-case/ShowCaseProduct',
    title: 'Field - Vital Pod (as Field)',
})
export class VitalPodField extends ui.Page<GraphApi> {
    @ui.decorators.section<VitalPodField>({})
    mainSection: ui.containers.Section;

    @ui.decorators.block<VitalPodField>({
        parent() {
            return this.mainSection;
        },
    })
    block: ui.containers.Block;

    @ui.decorators.textField<VitalPodField>({
        parent() {
            return this.block;
        },
        title: 'Id',
        width: 'medium',
    })
    _id: ui.fields.Text;

    @ui.decorators.vitalPodField<VitalPodField, ShowCaseProductOriginAddress>({
        title: 'Address',
        width: 'large',
        parent() {
            return this.block;
        },
        onClick() {
            this.clickTriggered.isHidden = false;
            setTimeout(() => {
                this.clickTriggered.isHidden = true;
            }, 5000);
        },
        onChange() {
            this.changeTriggered.isHidden = false;
            setTimeout(() => {
                this.changeTriggered.isHidden = true;
            }, 5000);
        },
        onAddButtonClick() {
            this.addClickTriggered.isHidden = false;
            setTimeout(() => {
                this.addClickTriggered.isHidden = true;
            }, 5000);
            return {
                addressLine1: 'Patata',
            };
        },
        onError() {
            ui.console.error('just testing onError works.');
        },
        columns: [
            ui.nestedFields.text({ bind: 'name', title: 'Name' }),
            ui.nestedFields.text({ bind: 'addressLine1', title: 'Line1' }),
            ui.nestedFields.text({ bind: 'addressLine2', title: 'Line2' }),
            ui.nestedFields.reference({
                bind: 'country',
                title: 'Country',
                node: '@sage/xtrem-show-case/ShowCaseCountry',
                valueField: 'name',
                helperTextField: 'code',
            }),
        ],
        node: '@sage/xtrem-show-case/ShowCaseProductOriginAddress',
    })
    originAddress: ui.fields.VitalPod;

    @ui.decorators.pageAction<VitalPodField>({
        title: 'Custom create title',
        async onClick() {
            await this.$.graph.create();
            this.$.dialog.message('info', 'Mutation Create', `Created entry: ${this._id.value}`, {
                fullScreen: false,
                rightAligned: false,
                acceptButton: {
                    isDisabled: false,
                    isHidden: false,
                    text: 'OK',
                },
            });
        },
    })
    create: ui.PageAction;

    @ui.decorators.pageAction<VitalPodField>({
        title: 'Save',
        async onClick() {
            await this.$.graph.update();
            this.$.dialog.message('info', 'Mutation Update', `Updated entry: ${this._id.value}`, {
                fullScreen: false,
                rightAligned: false,
                acceptButton: {
                    isDisabled: false,
                    isHidden: false,
                    text: 'OK',
                },
            });
        },
    })
    update: ui.PageAction;

    @ui.decorators.pageAction<VitalPodField>({
        title: 'Delete',
        icon: 'bin',
        isDestructive: true,
        async onClick() {
            await this.$.graph.delete();
            this.$.router.goTo('@sage/xtrem-show-case/BoundPage');
        },
    })
    delete: ui.PageAction;

    @ui.decorators.pageAction<VitalPodField>({
        title: 'Toggle Navigation Panel',
        async onClick() {
            this.$.isNavigationPanelHidden = !this.$.isNavigationPanelHidden;
        },
    })
    toggleNavigationPanel: ui.PageAction;

    /** Testing field buttons */

    @ui.decorators.labelField<VitalPodField>({
        parent() {
            return this.block;
        },
        isTransient: true,
        isHidden: true,
        map() {
            return 'Change was triggered';
        },
    })
    changeTriggered: ui.fields.Label;

    @ui.decorators.labelField<VitalPodField>({
        parent() {
            return this.block;
        },
        isTransient: true,
        isHidden: true,
        map() {
            return 'Click was triggered';
        },
    })
    clickTriggered: ui.fields.Label;

    @ui.decorators.labelField<VitalPodField>({
        parent() {
            return this.block;
        },
        isTransient: true,
        isHidden: true,
        map() {
            return 'Add Click was triggered';
        },
    })
    addClickTriggered: ui.fields.Label;

    @ui.decorators.section<VitalPodField>({
        isTitleHidden: true,
    })
    configurationSection: ui.containers.Section;

    @ui.decorators.block<VitalPodField>({
        parent() {
            return this.configurationSection;
        },
        title: 'Configuration',
        isTransient: true,
    })
    configurationBlock: ui.containers.Block;

    @ui.decorators.textField<VitalPodField>({
        parent() {
            return this.configurationBlock;
        },
        isTransient: true,
        title: 'Helper text',
        onChange() {
            this.originAddress.helperText = this.helperText.value;
        },
    })
    helperText: ui.fields.Text;

    @ui.decorators.checkboxField<VitalPodField>({
        parent() {
            return this.configurationBlock;
        },
        isTransient: true,
        title: 'Is disabled',
        onChange() {
            this.originAddress.isDisabled = this.isDisabled.value;
        },
    })
    isDisabled: ui.fields.Checkbox;

    @ui.decorators.checkboxField<VitalPodField>({
        parent() {
            return this.configurationBlock;
        },
        isTransient: true,
        title: 'Is removable',
        onChange() {
            this.originAddress.canRemove = this.isRemovable.value;
        },
    })
    isRemovable: ui.fields.Checkbox;

    @ui.decorators.checkboxField<VitalPodField>({
        parent() {
            return this.configurationBlock;
        },
        isTransient: true,
        title: 'Is helper text hidden',
        onChange() {
            this.originAddress.isHelperTextHidden = this.isHelperTextHidden.value;
        },
    })
    isHelperTextHidden: ui.fields.Checkbox;

    @ui.decorators.checkboxField<VitalPodField>({
        parent() {
            return this.configurationBlock;
        },
        isTransient: true,
        title: 'Is hidden',
        onChange() {
            this.originAddress.isHidden = this.isHidden.value;
        },
    })
    isHidden: ui.fields.Checkbox;

    @ui.decorators.checkboxField<VitalPodField>({
        parent() {
            return this.configurationBlock;
        },
        isTransient: true,
        title: 'Is readOnly',
        onChange() {
            this.originAddress.isReadOnly = this.isReadOnly.value;
        },
    })
    isReadOnly: ui.fields.Checkbox;

    @ui.decorators.checkboxField<VitalPodField>({
        parent() {
            return this.configurationBlock;
        },
        isTransient: true,
        title: 'Is title hidden',
        onChange() {
            this.originAddress.isTitleHidden = this.isTitleHidden.value;
        },
    })
    isTitleHidden: ui.fields.Checkbox;

    @ui.decorators.textField<VitalPodField>({
        parent() {
            return this.configurationBlock;
        },
        isTransient: true,
        title: 'Title',
        onChange() {
            this.originAddress.title = this.title.value;
        },
    })
    title: ui.fields.Text;

    @ui.decorators.buttonField<VitalPodField>({
        parent() {
            return this.configurationBlock;
        },
        isTransient: true,
        map() {
            return 'Focus field';
        },
        onClick() {
            this.originAddress.focus();
        },
    })
    focus: ui.fields.Button;
}
