import { Graph<PERSON><PERSON>, ShowCaseProduct, ShowCaseProvider } from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import { fields } from '../menu-items/fields';

@ui.decorators.page<MultiReference>({
    authorizationCode: 'BSCFLDS',
    menuItem: fields,
    module: 'show-case',
    title: 'Field - Multi-reference',
    category: 'SHOWCASE',
    isTransient: true,
})
export class MultiReference extends ui.Page<GraphApi> {
    @ui.decorators.section<MultiReference>({
        title: 'Multi-reference field',
    })
    section: ui.containers.Section;

    @ui.decorators.block<MultiReference>({
        parent() {
            return this.section;
        },
        title: 'Field example',
    })
    fieldBlock: ui.containers.Block;

    @ui.decorators.multiReferenceField<MultiReference, ShowCaseProduct>({
        columns: [
            ui.nestedFields.text({ bind: '_id', title: 'ID', canFilter: false }),
            ui.nestedFields.text({ bind: 'product', title: 'Product', canFilter: true }),
            ui.nestedFields.text({ bind: 'description', canFilter: true, title: 'Description' }),
            ui.nestedFields.reference<MultiReference, ShowCaseProduct, ShowCaseProvider>({
                bind: 'provider',
                canFilter: true,
                node: '@sage/xtrem-show-case/ShowCaseProvider',
                title: 'Provider',
                valueField: 'textField',
            }),
        ],
        helperTextField: 'description',
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        onClick() {
            this.clickTriggered.isHidden = false;
            setTimeout(() => {
                this.clickTriggered.isHidden = true;
            }, 5000);
        },
        onChange() {
            if (this.field.value !== this.value.value) {
                this.value.value = this.field.value;
                this.changeTriggered.isHidden = false;
                setTimeout(() => {
                    this.changeTriggered.isHidden = true;
                }, 5000);
            }
            if (this.field.helperText !== this.helperText.value) {
                this.helperText.value = this.field.helperText;
            }
        },
        parent() {
            return this.fieldBlock;
        },
        isFullWidth: true,
        valueField: 'product',
        imageField: 'imageField',
        lookupDialogTitle: 'Select Products',
    })
    field: ui.fields.MultiReference;

    @ui.decorators.labelField<MultiReference>({
        parent() {
            return this.fieldBlock;
        },
        isHidden: true,
        map() {
            return 'Change was triggered';
        },
    })
    changeTriggered: ui.fields.Label;

    @ui.decorators.labelField<MultiReference>({
        parent() {
            return this.fieldBlock;
        },
        isHidden: true,
        map() {
            return 'Click was triggered';
        },
    })
    clickTriggered: ui.fields.Label;

    @ui.decorators.block<MultiReference>({
        parent() {
            return this.section;
        },
        title: 'Configuration',
    })
    configurationBlock: ui.containers.Block;

    @ui.decorators.textField<MultiReference>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Helper text',
        onChange() {
            this.field.helperText = this.helperText.value;
        },
    })
    helperText: ui.fields.Text;

    @ui.decorators.selectField<MultiReference>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Icon',
        onChange() {
            this.field.icon = this.icon.value as any;
        },
        options: [
            'scan',
            'add',
            'calendar',
            'edit',
            'gift',
            'image',
            'ledger',
            'pause_circle',
            'refresh',
            'tag',
            'video',
        ],
    })
    icon: ui.fields.Select;

    @ui.decorators.checkboxField<MultiReference>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is disabled',
        onChange() {
            this.field.isDisabled = this.isDisabled.value;
        },
    })
    isDisabled: ui.fields.Checkbox;

    @ui.decorators.checkboxField<MultiReference>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is helper text hidden',
        onChange() {
            this.field.isHelperTextHidden = this.isHelperTextHidden.value;
        },
    })
    isHelperTextHidden: ui.fields.Checkbox;

    @ui.decorators.checkboxField<MultiReference>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is hidden',
        onChange() {
            this.field.isHidden = this.isHidden.value;
        },
    })
    isHidden: ui.fields.Checkbox;

    @ui.decorators.checkboxField<MultiReference>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is readOnly',
        onChange() {
            this.field.isReadOnly = this.isReadOnly.value;
        },
    })
    isReadOnly: ui.fields.Checkbox;

    @ui.decorators.checkboxField<MultiReference>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is title hidden',
        onChange() {
            this.field.isTitleHidden = this.isTitleHidden.value;
        },
    })
    isTitleHidden: ui.fields.Checkbox;

    @ui.decorators.textField<MultiReference>({
        title: 'Placeholder',
        onChange() {
            this.field.placeholder = this.placeholder.value;
        },
        parent() {
            return this.configurationBlock;
        },
    })
    placeholder: ui.fields.Text;

    @ui.decorators.numericField<MultiReference>({
        title: 'Set Minimum of Selectable Items',
        onChange() {
            this.field.minItems = this.minItems.value;
        },
        parent() {
            return this.configurationBlock;
        },
    })
    minItems: ui.fields.Numeric;

    @ui.decorators.numericField<MultiReference>({
        title: 'Set Maximum of Selectable Items',
        onChange() {
            this.field.maxItems = this.maxItems.value;
        },
        parent() {
            return this.configurationBlock;
        },
    })
    maxItems: ui.fields.Numeric;

    @ui.decorators.textField<MultiReference>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Title',
        onChange() {
            this.field.title = this.title.value;
        },
    })
    title: ui.fields.Text;

    @ui.decorators.multiReferenceField<MultiReference, ShowCaseProduct>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Value',
        onChange() {
            if (this.field.value !== this.value.value) {
                this.field.value = this.value.value;
            }
        },
        helperTextField: 'description',
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        valueField: 'product',
    })
    value: ui.fields.MultiReference;

    @ui.decorators.buttonField<MultiReference>({
        parent() {
            return this.configurationBlock;
        },
        map() {
            return 'Focus field';
        },
        onClick() {
            this.field.focus();
        },
    })
    focus: ui.fields.Button;

    @ui.decorators.buttonField<MultiReference>({
        parent() {
            return this.configurationBlock;
        },
        map() {
            return 'Open dialog';
        },
        onClick() {
            this.field.openDialog();
        },
    })
    openButton: ui.fields.Button;

    @ui.decorators.buttonField<MultiReference>({
        parent() {
            return this.configurationBlock;
        },
        map() {
            return 'Clear Value';
        },
        onClick() {
            this.field.value = null;
        },
    })
    clearValue: ui.fields.Button;

    /* Additional examples */

    @ui.decorators.block<MultiReference>({
        parent() {
            return this.section;
        },
        title: 'Additional examples',
    })
    additionalBlock: ui.containers.Block;

    @ui.decorators.multiReferenceField<MultiReference, ShowCaseProduct>({
        parent() {
            return this.additionalBlock;
        },
        title: 'Mandatory',
        isMandatory: true,
        helperTextField: 'description',
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        valueField: 'product',
    })
    mandatory: ui.fields.MultiReference;

    @ui.decorators.multiReferenceField<MultiReference, ShowCaseProduct>({
        parent() {
            return this.additionalBlock;
        },
        title: 'Full width',
        isFullWidth: true,
        helperTextField: 'description',
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        valueField: 'product',
    })
    fullWidth: ui.fields.MultiReference;

    @ui.decorators.multiReferenceField<MultiReference, ShowCaseProduct>({
        parent() {
            return this.additionalBlock;
        },
        title: 'No minimum lookup characters',
        helperTextField: 'description',
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        valueField: 'product',
        minLookupCharacters: 0,
    })
    noMinimumLookupCharacters: ui.fields.MultiReference;

    @ui.decorators.multiReferenceField<MultiReference, ShowCaseProduct>({
        parent() {
            return this.additionalBlock;
        },
        title: 'Tunnel link',
        helperTextField: 'description',
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        valueField: 'product',
        tunnelPage: '@sage/xtrem-show-case/StandardShowCaseProduct',
    })
    tunnelLink: ui.fields.MultiReference;

    @ui.decorators.multiReferenceField<MultiReference, ShowCaseProduct>({
        parent() {
            return this.additionalBlock;
        },
        title: 'Restricted result set',
        helperTextField: 'description',
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        valueField: 'product',
        columns: [
            ui.nestedFields.text({ bind: '_id', title: 'ID', canFilter: false }),
            ui.nestedFields.text({ bind: 'product', title: 'Product', canFilter: true }),
            ui.nestedFields.text({ bind: 'description', canFilter: true, title: 'Description' }),
            ui.nestedFields.reference<MultiReference, ShowCaseProduct, ShowCaseProvider>({
                bind: 'provider',
                title: 'Provider',
                valueField: 'textField',
                canFilter: true,
                node: '@sage/xtrem-show-case/ShowCaseProvider',
            }),
        ],
    })
    restrictedResultSet: ui.fields.MultiReference;

    @ui.decorators.textField<MultiReference>({
        parent() {
            return this.additionalBlock;
        },
        title: 'Result set restriction',
        onChange() {
            const filter = this.resultSetRestriction.value
                ? {
                      product: { _regex: this.resultSetRestriction.value, _options: 'i' },
                  }
                : undefined;

            this.restrictedResultSet.filter = filter;
        },
    })
    resultSetRestriction: ui.fields.Text;

    @ui.decorators.block<MultiReference>({
        parent() {
            return this.section;
        },
        title: 'Suggestions on columns',
    })
    suggestionOnColumnsBlock: ui.containers.Block;

    @ui.decorators.multiReferenceField<MultiReference, ShowCaseProduct>({
        columns: [
            ui.nestedFields.text({ bind: '_id', title: 'ID', canFilter: false }),
            ui.nestedFields.text({ bind: 'product', title: 'Product', canFilter: true }),
            ui.nestedFields.text({ bind: 'description', canFilter: true, title: 'Description' }),
            ui.nestedFields.reference<MultiReference, ShowCaseProduct, ShowCaseProvider>({
                bind: 'provider',
                canFilter: true,
                node: '@sage/xtrem-show-case/ShowCaseProvider',
                title: 'Provider',
                valueField: 'textField',
            }),
        ],
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        shouldSuggestionsIncludeColumns: true,
        parent() {
            return this.suggestionOnColumnsBlock;
        },
        valueField: 'product',
        title: 'Suggestions from column data',
    })
    suggestionOnColumns: ui.fields.MultiReference;

    @ui.decorators.multiReferenceField<MultiReference, ShowCaseProduct>({
        parent() {
            return this.additionalBlock;
        },
        title: 'Restricted Results by Callback Filter',
        helperTextField: 'description',
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        valueField: 'product',
        columns: [
            ui.nestedFields.text({ bind: '_id', title: 'ID', canFilter: false }),
            ui.nestedFields.text({ bind: 'product', title: 'Product', canFilter: true }),
        ],
        filter() {
            return {
                provider: {
                    textField: {
                        _regex: 'zon',
                    },
                },
            };
        },
    })
    restrictedResultByCallbackFilter: ui.fields.MultiReference;

    @ui.decorators.multiReferenceField<MultiReference, ShowCaseProduct>({
        parent() {
            return this.additionalBlock;
        },
        title: 'Restricted Results by Combined Callback Filter',
        helperTextField: 'description',
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        valueField: 'product',
        columns: [
            ui.nestedFields.text({ bind: '_id', title: 'ID', canFilter: false }),
            ui.nestedFields.text({ bind: 'product', title: 'Product', canFilter: true }),
        ],
        filter() {
            return {
                provider: {
                    _or: [
                        {
                            textField: {
                                _regex: 'zon',
                            },
                        },
                        {
                            decimalField: '2.34',
                        },
                    ],
                },
            };
        },
    })
    restrictedResultByCombinedCallbackFilter: ui.fields.MultiReference;

    @ui.decorators.multiReferenceField<MultiReference, ShowCaseProduct>({
        parent() {
            return this.additionalBlock;
        },
        title: 'Restricted Results by filter',
        helperTextField: 'description',
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        valueField: 'product',
        columns: [
            ui.nestedFields.text({ bind: '_id', title: 'ID', canFilter: false }),
            ui.nestedFields.text({ bind: 'product', title: 'Product', canFilter: true }),
        ],
        filter: {
            provider: {
                textField: 'Ali Express',
            },
        },
    })
    restrictedResultByFilter: ui.fields.MultiReference;

    @ui.decorators.multiReferenceField<MultiReference, ShowCaseProduct>({
        parent() {
            return this.additionalBlock;
        },
        title: 'Additional records in the lookup dialog',
        helperText: 'It is called "Some Product"',
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        valueField: 'product',
        columns: [
            ui.nestedFields.text({ bind: '_id', title: 'ID', canFilter: false }),
            ui.nestedFields.text({ bind: 'product', title: 'Product', canFilter: true }),
        ],
        additionalLookupRecords() {
            return [{ _id: '-1', netPrice: '3', description: 'Some Product', product: 'Some product', progress: 3 }];
        },
    })
    additionalRecords: ui.fields.MultiReference;

    @ui.decorators.block<MultiReference>({
        parent() {
            return this.section;
        },
        title: 'Multi-reference field with image',
    })
    withImageBlock: ui.containers.Block;

    @ui.decorators.multiReferenceField<MultiReference, ShowCaseProduct>({
        columns: [
            ui.nestedFields.text({ bind: '_id', title: 'ID', canFilter: false }),
            ui.nestedFields.text({ bind: 'product', title: 'Product', canFilter: true }),
            ui.nestedFields.text({ bind: 'description', canFilter: true, title: 'Description' }),
        ],
        imageField: 'imageField',
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        shouldSuggestionsIncludeColumns: true,
        parent() {
            return this.withImageBlock;
        },
        valueField: 'product',
        title: 'Product with image',
    })
    withImage: ui.fields.MultiReference;

    @ui.decorators.checkboxField<MultiReference>({
        parent() {
            return this.withImageBlock;
        },
        title: 'Is readOnly',
        onChange() {
            this.withImage.isReadOnly = this.withImageIsReadOnly.value;
        },
    })
    withImageIsReadOnly: ui.fields.Checkbox;

    @ui.decorators.checkboxField<MultiReference>({
        parent() {
            return this.withImageBlock;
        },
        title: 'Is Disabled',
        onChange() {
            this.withImage.isDisabled = this.withImageIsDisabled.value;
        },
    })
    withImageIsDisabled: ui.fields.Checkbox;

    @ui.decorators.block<MultiReference>({
        parent() {
            return this.section;
        },
        title: 'Multi-reference Sizes',
    })
    sizesBlock: ui.containers.Block;

    @ui.decorators.multiReferenceField<MultiReference, ShowCaseProduct>({
        columns: [
            ui.nestedFields.text({ bind: '_id', title: 'ID', canFilter: false }),
            ui.nestedFields.text({ bind: 'product', title: 'Product', canFilter: true }),
            ui.nestedFields.text({ bind: 'description', canFilter: true, title: 'Description' }),
            ui.nestedFields.reference<MultiReference, ShowCaseProduct, ShowCaseProvider>({
                bind: 'provider',
                canFilter: true,
                node: '@sage/xtrem-show-case/ShowCaseProvider',
                title: 'Provider',
                valueField: 'textField',
            }),
        ],
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        onClick() {
            this.clickTriggered.isHidden = false;
            setTimeout(() => {
                this.clickTriggered.isHidden = true;
            }, 5000);
        },
        onChange() {
            if (this.small.value !== this.value.value) {
                this.value.value = this.small.value;
                this.changeTriggered.isHidden = false;
                setTimeout(() => {
                    this.changeTriggered.isHidden = true;
                }, 5000);
            }
        },
        parent() {
            return this.sizesBlock;
        },
        valueField: 'product',
        imageField: 'imageField',
        size: 'small',
        title: 'Small',
    })
    small: ui.fields.MultiReference;

    @ui.decorators.multiReferenceField<MultiReference, ShowCaseProduct>({
        columns: [
            ui.nestedFields.text({ bind: '_id', title: 'ID', canFilter: false }),
            ui.nestedFields.text({ bind: 'product', title: 'Product', canFilter: true }),
            ui.nestedFields.text({ bind: 'description', canFilter: true, title: 'Description' }),
            ui.nestedFields.reference<MultiReference, ShowCaseProduct, ShowCaseProvider>({
                bind: 'provider',
                canFilter: true,
                node: '@sage/xtrem-show-case/ShowCaseProvider',
                title: 'Provider',
                valueField: 'textField',
            }),
        ],
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        onClick() {
            this.clickTriggered.isHidden = false;
            setTimeout(() => {
                this.clickTriggered.isHidden = true;
            }, 5000);
        },
        onChange() {
            if (this.medium.value !== this.value.value) {
                this.value.value = this.medium.value;
                this.changeTriggered.isHidden = false;
                setTimeout(() => {
                    this.changeTriggered.isHidden = true;
                }, 5000);
            }
        },
        parent() {
            return this.sizesBlock;
        },
        valueField: 'product',
        imageField: 'imageField',
        title: 'Medium',
    })
    medium: ui.fields.MultiReference;

    @ui.decorators.multiReferenceField<MultiReference, ShowCaseProduct>({
        columns: [
            ui.nestedFields.text({ bind: '_id', title: 'ID', canFilter: false }),
            ui.nestedFields.text({ bind: 'product', title: 'Product', canFilter: true }),
            ui.nestedFields.text({ bind: 'description', canFilter: true, title: 'Description' }),
            ui.nestedFields.reference<MultiReference, ShowCaseProduct, ShowCaseProvider>({
                bind: 'provider',
                canFilter: true,
                node: '@sage/xtrem-show-case/ShowCaseProvider',
                title: 'Provider',
                valueField: 'textField',
            }),
        ],
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        onClick() {
            this.clickTriggered.isHidden = false;
            setTimeout(() => {
                this.clickTriggered.isHidden = true;
            }, 5000);
        },
        onChange() {
            if (this.large.value !== this.value.value) {
                this.value.value = this.large.value;
                this.changeTriggered.isHidden = false;
                setTimeout(() => {
                    this.changeTriggered.isHidden = true;
                }, 5000);
            }
        },
        parent() {
            return this.sizesBlock;
        },
        valueField: 'product',
        imageField: 'imageField',
        size: 'large',
        title: 'Large',
    })
    large: ui.fields.MultiReference;

    @ui.decorators.block<MultiReference>({
        parent() {
            return this.section;
        },
        title: 'Order block',
    })
    orderBlock: ui.containers.Block;

    @ui.decorators.multiReferenceField<MultiReference, ShowCaseProduct>({
        columns: [
            ui.nestedFields.reference<MultiReference, ShowCaseProduct, ShowCaseProvider>({
                bind: 'provider',
                canFilter: true,
                node: '@sage/xtrem-show-case/ShowCaseProvider',
                title: 'Provider',
                valueField: 'textField',
            }),
            ui.nestedFields.text({ bind: '_id', title: 'ID', canFilter: false }),
            ui.nestedFields.text({ bind: 'product', title: 'Product', canFilter: true }),
            ui.nestedFields.text({ bind: 'description', canFilter: true, title: 'Description' }),
        ],
        helperText: 'Ordered by provider->textField->ASC (first column declared)',
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        parent() {
            return this.orderBlock;
        },
        valueField: 'product',
        imageField: 'imageField',
        isFullWidth: true,
    })
    orderedField: ui.fields.MultiReference;

    @ui.decorators.multiReferenceField<MultiReference, ShowCaseProduct>({
        columns: [
            ui.nestedFields.reference<MultiReference, ShowCaseProduct, ShowCaseProvider>({
                bind: 'provider',
                canFilter: true,
                node: '@sage/xtrem-show-case/ShowCaseProvider',
                title: 'Provider',
                valueField: 'textField',
            }),
            ui.nestedFields.text({ bind: '_id', title: 'ID', canFilter: false }),
            ui.nestedFields.text({ bind: 'product', title: 'Product', canFilter: true }),
            ui.nestedFields.text({ bind: 'description', canFilter: true, title: 'Description' }),
        ],
        helperText: 'Ordered by description DESC and then _id ASC',
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        parent() {
            return this.orderBlock;
        },
        valueField: 'product',
        imageField: 'imageField',
        isFullWidth: true,
        orderBy: { description: -1, _id: 1 },
    })
    orderedField2: ui.fields.MultiReference;

    @ui.decorators.separatorField<MultiReference>({
        parent() {
            return this.additionalBlock;
        },
        isFullWidth: true,
    })
    fieldSeparator1: ui.fields.Separator;

    @ui.decorators.multiReferenceField<MultiReference, ShowCaseProduct>({
        parent() {
            return this.additionalBlock;
        },
        title: 'With warning message',
        warningMessage: 'Wow, warning!',
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        valueField: 'product',
        helperTextField: 'description',
    })
    warningMessageField: ui.fields.MultiReference<ShowCaseProduct>;

    @ui.decorators.multiReferenceField<MultiReference, ShowCaseProduct>({
        parent() {
            return this.additionalBlock;
        },
        title: 'With warning message with callback',
        helperText: 'Select at least 3 items',
        warningMessage() {
            if (this.warningMessageWithCallbackField.value && this.warningMessageWithCallbackField.value.length > 2) {
                return 'Warning message';
            }
            return null;
        },
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        valueField: 'product',
        helperTextField: 'description',
    })
    warningMessageWithCallbackField: ui.fields.MultiReference<ShowCaseProduct>;

    @ui.decorators.multiReferenceField<MultiReference, ShowCaseProduct>({
        parent() {
            return this.additionalBlock;
        },
        title: 'With info message',
        infoMessage: 'Wow, warning!',
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        valueField: 'product',
        helperTextField: 'description',
    })
    infoMessageField: ui.fields.MultiReference<ShowCaseProduct>;

    @ui.decorators.multiReferenceField<MultiReference, ShowCaseProduct>({
        parent() {
            return this.additionalBlock;
        },
        title: 'With info message with callback',
        helperText: 'Select at least 3 items',
        infoMessage() {
            if (this.infoMessageWithCallbackField.value && this.infoMessageWithCallbackField.value.length > 2) {
                return 'Info message';
            }
            return null;
        },
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        valueField: 'product',
        helperTextField: 'description',
    })
    infoMessageWithCallbackField: ui.fields.MultiReference<ShowCaseProduct>;

    @ui.decorators.multiReferenceField<MultiReference, ShowCaseProduct>({
        parent() {
            return this.additionalBlock;
        },
        title: 'With info message',
        warningMessage: 'Wow, warning!',
        infoMessage: 'You should not see this',
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        valueField: 'product',
        helperTextField: 'description',
    })
    infoAndWarningMessageField: ui.fields.MultiReference<ShowCaseProduct>;

    @ui.decorators.multiReferenceField<MultiReference, ShowCaseProduct>({
        parent() {
            return this.additionalBlock;
        },
        isMandatory: true,
        title: 'Info, warning and validation',
        warningMessage: 'Wow, warning!',
        infoMessage: 'You should not see this',
        helperText: 'This field is mandatory too.',
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        valueField: 'product',
        helperTextField: 'description',
    })
    infoAndWarningMessageMandatoryField: ui.fields.MultiReference<ShowCaseProduct>;
}
