import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Show<PERSON><PERSON>Provider, ShowCaseProduct } from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import { tableField } from '../menu-items/table-field';

@ui.decorators.page<TableWithCalendarView, ShowCaseProvider>({
    authorizationCode: 'SHCPRVD',
    category: 'SHOWCASE',
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.text({ bind: 'textField' }),
            titleRight: ui.nestedFields.text({ bind: '_id' }),
        },
    },
    node: '@sage/xtrem-show-case/ShowCaseProvider',
    title: 'Table - with calendar view option',
    menuItem: tableField,
})
export class TableWithCalendarView extends ui.Page<GraphApi> {
    @ui.decorators.section<TableWithCalendarView>({})
    section: ui.containers.Section;

    @ui.decorators.block<TableWithCalendarView>({
        parent() {
            return this.section;
        },
    })
    block: ui.containers.Block;

    @ui.decorators.textField<TableWithCalendarView>({
        parent() {
            return this.block;
        },
        title: 'Id',
        isReadOnly: true,
    })
    _id: ui.fields.Text;

    @ui.decorators.checkboxField<TableWithCalendarView>({
        parent() {
            return this.block;
        },
        title: 'Checkbox',
    })
    booleanField: ui.fields.Checkbox;

    @ui.decorators.tableField<TableWithCalendarView, ShowCaseProduct>({
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        parent() {
            return this.block;
        },
        title: 'Products',
        orderBy: { _id: 1 },
        optionsMenu() {
            return [
                {
                    title: 'All',
                    graphQLFilter: {},
                },
                {
                    title: 'Good products',
                    graphQLFilter: {
                        category: { _eq: 'good' },
                    },
                },
                {
                    title: 'Great products',
                    graphQLFilter: {
                        category: { _eq: 'great' },
                    },
                },
                {
                    title: 'Awful products',
                    graphQLFilter: {
                        category: { _eq: 'awful' },
                    },
                },
            ];
        },
        columns: [
            ui.nestedFields.text({ bind: '_id', isReadOnly: true, title: 'Id' }),
            ui.nestedFields.link({
                bind: 'product',
                title: 'Product',
                onClick(_id) {
                    this.$.router.goTo('@sage/xtrem-show-case/ShowCaseProduct', { _id });
                },
            }),
            ui.nestedFields.text({ bind: 'description', title: 'Description' }),
            ui.nestedFields.checkbox({ bind: 'hotProduct', isReadOnly: true, title: 'Hot' }),
            ui.nestedFields.numeric({ bind: 'total', title: 'Net Price', scale: 2 }),
            ui.nestedFields.label({
                bind: 'category',
                title: 'Category',
                optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
            }),
        ],
        mobileCard: {
            title: ui.nestedFields.text({
                bind: 'product',
                title: 'Product',
            }),
            line2: ui.nestedFields.text({ bind: 'description', title: 'Description' }),
            image: ui.nestedFields.image({ bind: 'imageField' }),
        },
        startDateField: 'releaseDate',
        endDateField: 'endingDate',
        cardColor(row: ShowCaseProduct) {
            switch (row.category) {
                case 'awful':
                    return ui.tokens.colorsSemanticNegative500;
                case 'notBad':
                    return ui.tokens.colorsSemanticCaution500;
                case 'ok':
                    return ui.tokens.colorsSemanticInfo500;
                case 'good':
                    return ui.tokens.colorsSemanticPositive500;
                case 'great':
                    return ui.tokens.colorsSemanticPositive600;
                default:
                    return '';
            }
        },
        isEventMovable(row: ShowCaseProduct) {
            return row.category === 'good' || row.category === 'ok';
        },
        dropdownActions: [
            {
                icon: 'bin',
                title: 'Remove',
                isDestructive: true,
                async onClick(_id: any, data: any) {
                    await this.$.graph.delete({
                        _id: data._id,
                        nodeName: '@sage/xtrem-show-case/ShowCaseProduct',
                    });
                    await this.products.refresh();
                },
            },
        ],
    })
    products: ui.fields.Table<ShowCaseProduct, TableWithCalendarView>;
}
