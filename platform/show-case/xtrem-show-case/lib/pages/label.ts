import * as ui from '@sage/xtrem-ui';
import { fields } from '../menu-items/fields';

@ui.decorators.page<Label>({
    authorizationCode: 'BSCFLDS',
    module: 'show-case',
    title: 'Field - Label',
    isTransient: true,
    category: 'SHOWCASE',
    onLoad() {
        this.localizedValueSelect.value = 'good';
        this.localizedLabelField.value = 'good';
        this.localizedRawValue.value = 'good';
    },
    menuItem: fields,
})
export class Label extends ui.Page {
    @ui.decorators.section<Label>({
        title: 'Label field',
    })
    section: ui.containers.Section;

    @ui.decorators.block<Label>({
        parent() {
            return this.section;
        },
        title: 'Field example',
    })
    fieldBlock: ui.containers.Block;

    @ui.decorators.labelField<Label>({
        parent() {
            return this.fieldBlock;
        },
        onClick() {
            this.clickTriggered.isHidden = false;
            setTimeout(() => {
                this.clickTriggered.isHidden = true;
            }, 5000);
        },
    })
    field: ui.fields.Label;

    @ui.decorators.labelField<Label>({
        parent() {
            return this.fieldBlock;
        },
        isHidden: true,
        map() {
            return 'Click was triggered';
        },
    })
    clickTriggered: ui.fields.Label;

    @ui.decorators.block<Label>({
        parent() {
            return this.section;
        },
        title: 'Configuration',
    })
    configurationBlock: ui.containers.Block;

    @ui.decorators.textField<Label>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Background color',
        onChange() {
            this.field.backgroundColor = this.backgroundColor.value;
        },
    })
    backgroundColor: ui.fields.Text;

    @ui.decorators.textField<Label>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Border color',
        onChange() {
            this.field.borderColor = this.borderColor.value;
        },
    })
    borderColor: ui.fields.Text;

    @ui.decorators.textField<Label>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Color',
        onChange() {
            this.field.color = this.color.value;
        },
    })
    color: ui.fields.Text;

    @ui.decorators.textField<Label>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Helper text',
        onChange() {
            this.field.helperText = this.helperText.value;
        },
    })
    helperText: ui.fields.Text;

    @ui.decorators.checkboxField<Label>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is disabled',
        onChange() {
            this.field.isDisabled = this.isDisabled.value;
        },
    })
    isDisabled: ui.fields.Checkbox;

    @ui.decorators.checkboxField<Label>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is helper text hidden',
        onChange() {
            this.field.isHelperTextHidden = this.isHelperTextHidden.value;
        },
    })
    isHelperTextHidden: ui.fields.Checkbox;

    @ui.decorators.checkboxField<Label>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is hidden',
        onChange() {
            this.field.isHidden = this.isHidden.value;
        },
    })
    isHidden: ui.fields.Checkbox;

    @ui.decorators.checkboxField<Label>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is title hidden',
        onChange() {
            this.field.isTitleHidden = this.isTitleHidden.value;
        },
    })
    isTitleHidden: ui.fields.Checkbox;

    @ui.decorators.textField<Label>({
        title: 'Postfix',
        onChange() {
            this.field.postfix = this.postfix.value;
        },
        parent() {
            return this.configurationBlock;
        },
    })
    postfix: ui.fields.Text;

    @ui.decorators.textField<Label>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Prefix',
        onChange() {
            this.field.prefix = this.prefix.value;
        },
    })
    prefix: ui.fields.Text;

    @ui.decorators.textField<Label>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Title',
        onChange() {
            this.field.title = this.title.value;
        },
    })
    title: ui.fields.Text;

    @ui.decorators.textField<Label>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Value',
        onChange() {
            this.field.value = this.value.value;
        },
    })
    value: ui.fields.Text;

    @ui.decorators.block<Label>({
        parent() {
            return this.section;
        },
        title: 'Localized example',
    })
    localizedLabelBlock: ui.containers.Block;

    @ui.decorators.labelField<Label>({
        parent() {
            return this.localizedLabelBlock;
        },
        title: 'Localized label',
        optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
    })
    localizedLabelField: ui.fields.Label;

    @ui.decorators.selectField<Label>({
        parent() {
            return this.localizedLabelBlock;
        },
        title: 'Value',
        onChange() {
            this.localizedLabelField.value = this.localizedValueSelect.value;
            this.localizedRawValue.value = this.localizedValueSelect.value;
        },
        helperText: 'Set the value of the localized label here',
        optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
    })
    localizedValueSelect: ui.fields.Select;

    @ui.decorators.textField<Label>({
        parent() {
            return this.localizedLabelBlock;
        },
        isReadOnly: true,
        title: 'Raw value',
        helperText: 'This is the value stored on the server',
    })
    localizedRawValue: ui.fields.Text;
}
