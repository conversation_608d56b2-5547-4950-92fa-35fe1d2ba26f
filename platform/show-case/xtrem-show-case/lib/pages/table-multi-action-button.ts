import {
    Show<PERSON><PERSON><PERSON><PERSON><PERSON>,
    ShowCaseProduct,
    ShowCaseProductOrigin<PERSON>ddress,
    ShowCaseProvider,
} from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import { tableField } from '../menu-items/table-field';

@ui.decorators.page<TableMultiActionButton, ShowCaseProvider>({
    authorizationCode: 'BSCFLDS',
    defaultEntry: () => '2',
    module: 'show-case',
    category: 'SHOWCASE',
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.text({ bind: 'textField' }),
            titleRight: ui.nestedFields.text({ bind: '_id' }),
        },
    },
    node: '@sage/xtrem-show-case/ShowCaseProvider',
    title: 'Table - with multi action button',
    headerQuickActions() {
        return [this.$standardDuplicateAction];
    },
    headerDropDownActions() {
        return [this.$standardDeleteAction];
    },
    businessActions() {
        return [this.$standardCancelAction, this.$standardSaveAction];
    },
    menuItem: tableField,
})
export class TableMultiActionButton extends ui.Page {
    @ui.decorators.section<TableMultiActionButton>({
        isTitleHidden: true,
    })
    section: ui.containers.Section;

    @ui.decorators.block<TableMultiActionButton>({
        parent() {
            return this.section;
        },
    })
    fieldBlock: ui.containers.Block;

    @ui.decorators.tableField<TableMultiActionButton, ShowCaseProduct>({
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        bind: 'products',
        title: 'Products',
        canExport: false,
        canAddNewLine: false,
        columns: [
            ui.nestedFields.text({
                bind: '_id',
                title: 'Id',
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                bind: 'product',
                title: 'Product',
                isMandatory: true,
            }),
            ui.nestedFields.text({
                bind: 'description',
                isFullWidth: true,
                title: 'Description',
            }),
            ui.nestedFields.numeric({
                bind: 'qty',
                title: 'Quantity',
                fetchesDefaults: true,
                validation: v => {
                    if (v < 0) {
                        return 'Quantity must be positive';
                    }
                    return '';
                },
            }),
            ui.nestedFields.numeric({
                bind: 'listPrice',
                title: 'List Price',
                scale: 2,
                isHiddenDesktop: false,
                fetchesDefaults: true,
            }),

            ui.nestedFields.label({
                bind: 'st',
                onClick(_id: string | number, rowData) {
                    this.$.dialog.message('info', 'Label was clicked', `Label ${rowData.st} was clicked (row ${_id})`);
                },
                title: 'St',
                isExcludedFromMainField: true,
            }),
            ui.nestedFields.numeric({
                bind: 'tax',
                title: 'Tax',
                prefix: 'T',
                scale: 2,
                isExcludedFromMainField: true,
                isHiddenDesktop: false,
            }),
            ui.nestedFields.numeric<TableMultiActionButton, ShowCaseProduct>({
                bind: 'amount',
                title: 'Amount',
                prefix(_id: string | number, rowData: ShowCaseProduct) {
                    return rowData?.provider?.textField === 'Amazon' ? '€' : '$';
                },
                scale: 2,
                isExcludedFromMainField: true,
                isHiddenDesktop: false,
            }),
            ui.nestedFields.reference<TableMultiActionButton, ShowCaseProduct, ShowCaseProvider>({
                bind: 'provider',
                title: 'Provider',
                valueField: 'textField',
                node: '@sage/xtrem-show-case/ShowCaseProvider',
                minLookupCharacters: 0,
                columns: [
                    ui.nestedFields.text({ bind: '_id', title: 'ID', canFilter: false }),
                    ui.nestedFields.text({ bind: 'textField', title: 'Provider', canFilter: true }),
                    ui.nestedFields.image({ bind: 'logo', title: 'Logo', canFilter: false }),
                ],
                imageField: 'logo',
                onClick(id: string, raw: any) {
                    ui.console.log('CLICKED LOOKUP', { id, raw });
                },
                onChange(id: string, raw: any) {
                    ui.console.log('CHANGED LOOKUP', { id, raw });
                },
            }),
            ui.nestedFields.link({
                bind: 'product',
                isTransient: true,
                map(_fieldValue, rowData) {
                    return `http://${(rowData.product as string)
                        .replace(/-|_|\s/g, '')
                        .substring(0, 4)
                        .toLowerCase()}.sage.com`;
                },
                onClick(id: string, raw: any) {
                    ui.console.log('CLICKED LINK', { id, raw });
                },
                title: 'Link',
                page: 'https://www.google.com',
                isExcludedFromMainField: true,
            }),
            ui.nestedFields.progress({
                bind: 'progress',
                title: 'Progress',
            }),
            ui.nestedFields.numeric({
                bind: 'fixedQuantity',
                title: 'Fixed Quantity',
                isTransientInput: true,
            }),
            ui.nestedFields.checkbox({
                bind: 'hotProduct',
                title: 'Hot',
            }),
            ui.nestedFields.numeric({
                bind: 'netPrice',
                title: 'Net Price',
                isReadOnly: (value: number, rowValue: any) => {
                    return !!rowValue.hotProduct;
                },
                scale: 2,
                isHiddenDesktop: false,
            }),
            ui.nestedFields.filterSelect<TableMultiActionButton, ShowCaseProduct, ShowCaseCustomer>({
                bind: 'email',
                title: 'Email',
                valueField: 'email',
                node: '@sage/xtrem-show-case/ShowCaseCustomer',
                isExcludedFromMainField: false,
                minLookupCharacters: 0,
                width: 'large',
                onChange(id: string, raw: string) {
                    ui.console.log('CHANGED FILTER SELECT', { id, raw });
                },
            }),
            ui.nestedFields.date<TableMultiActionButton, ShowCaseProduct>({
                bind: 'releaseDate',
                title: 'Date',
            }),
            ui.nestedFields.date({
                bind: 'endingDate',
                title: 'Ending date',
                isExcludedFromMainField: true,
            }),
            ui.nestedFields.select<TableMultiActionButton, ShowCaseProduct>({
                bind: 'category',
                title: 'Category',
                optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
            }),
            ui.nestedFields.image({
                bind: 'imageField',
                title: 'Image',
                isExcludedFromMainField: true,
            }),
            ui.nestedFields.icon({
                bind: 'qty',
                title: 'Icon',
                isExcludedFromMainField: true,

                map: (value: any) => {
                    return value === 0 ? 'error' : value < 10 ? 'warning' : '';
                },
            }),
            ui.nestedFields.technical<TableMultiActionButton, ShowCaseProduct, ShowCaseProductOriginAddress>({
                bind: 'originAddress',
                node: '@sage/xtrem-show-case/ShowCaseProductOriginAddress',
                nestedFields: [
                    ui.nestedFields.text({
                        bind: 'name',
                    }),
                    ui.nestedFields.text({
                        bind: 'addressLine1',
                    }),
                    ui.nestedFields.text({
                        bind: 'addressLine2',
                    }),
                    ui.nestedFields.reference({
                        bind: 'country',
                        node: '@sage/xtrem-show-case/ShowCaseCountry',
                        valueField: 'name',
                        helperTextField: 'code',
                    }),
                ],
            }),
            ui.nestedFields.numeric<TableMultiActionButton, ShowCaseProduct>({
                bind: 'discount',
                title: 'Discount',
                isExcludedFromMainField: true,
            }),
            ui.nestedFields.datetimeRange({
                bind: 'manufacturedWithin',
                isFullWidth: true,
                title: 'Manufacterd within',
            }),
        ],
        mobileCard: {
            title: ui.nestedFields.text({ bind: 'product', title: 'Product' }),
            titleRight: ui.nestedFields.text({ bind: '_id', title: 'ID', canFilter: false }),
            line2: ui.nestedFields.text({ bind: 'description', title: 'Description', canFilter: false }),
            listPrice: ui.nestedFields.numeric({ bind: 'listPrice', title: 'List Price', isHiddenOnMainField: true }),
            image: ui.nestedFields.image({ bind: 'imageField' }),
        },
        orderBy: {
            product: 1,
        },
        parent() {
            return this.fieldBlock;
        },
        addItemActions() {
            return [this.customAddLine, this.customAddLineDisabled];
        },
    })
    table: ui.fields.Table<ShowCaseProduct>;

    @ui.decorators.tableField<TableMultiActionButton, ShowCaseProduct>({
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        bind: 'products',
        title: 'Products with sidebar',
        canExport: false,
        canAddNewLine: false,
        columns: [
            ui.nestedFields.text({
                bind: '_id',
                title: 'Id',
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                bind: 'product',
                title: 'Product',
                isMandatory: true,
            }),
            ui.nestedFields.text({
                bind: 'description',
                isFullWidth: true,
                title: 'Description',
            }),
            ui.nestedFields.numeric({
                bind: 'qty',
                title: 'Quantity',
                fetchesDefaults: true,
                validation: v => {
                    if (v < 0) {
                        return 'Quantity must be positive';
                    }
                    return '';
                },
            }),
            ui.nestedFields.numeric({
                bind: 'listPrice',
                title: 'List Price',
                scale: 2,
                isHiddenDesktop: false,
                fetchesDefaults: true,
            }),

            ui.nestedFields.label({
                bind: 'st',
                onClick(_id: string | number, rowData) {
                    this.$.dialog.message('info', 'Label was clicked', `Label ${rowData.st} was clicked (row ${_id})`);
                },
                title: 'St',
                isExcludedFromMainField: true,
            }),
            ui.nestedFields.numeric({
                bind: 'tax',
                title: 'Tax',
                prefix: 'T',
                scale: 2,
                isExcludedFromMainField: true,
                isHiddenDesktop: false,
            }),
            ui.nestedFields.numeric<TableMultiActionButton, ShowCaseProduct>({
                bind: 'amount',
                title: 'Amount',
                prefix(_id: string | number, rowData: ShowCaseProduct) {
                    return rowData?.provider?.textField === 'Amazon' ? '€' : '$';
                },
                scale: 2,
                isExcludedFromMainField: true,
                isHiddenDesktop: false,
            }),
            ui.nestedFields.reference<TableMultiActionButton, ShowCaseProduct, ShowCaseProvider>({
                bind: 'provider',
                title: 'Provider',
                valueField: 'textField',
                node: '@sage/xtrem-show-case/ShowCaseProvider',
                minLookupCharacters: 0,
                columns: [
                    ui.nestedFields.text({ bind: '_id', title: 'ID', canFilter: false }),
                    ui.nestedFields.text({ bind: 'textField', title: 'Provider', canFilter: true }),
                    ui.nestedFields.image({ bind: 'logo', title: 'Logo', canFilter: false }),
                ],
                imageField: 'logo',
                onClick(id: string, raw: any) {
                    ui.console.log('CLICKED LOOKUP', { id, raw });
                },
                onChange(id: string, raw: any) {
                    ui.console.log('CHANGED LOOKUP', { id, raw });
                },
            }),
            ui.nestedFields.link({
                bind: 'product',
                isTransient: true,
                map(_fieldValue, rowData) {
                    return `http://${(rowData.product as string)
                        .replace(/-|_|\s/g, '')
                        .substring(0, 4)
                        .toLowerCase()}.sage.com`;
                },
                onClick(id: string, raw: any) {
                    ui.console.log('CLICKED LINK', { id, raw });
                },
                title: 'Link',
                page: 'https://www.google.com',
                isExcludedFromMainField: true,
            }),
            ui.nestedFields.progress({
                bind: 'progress',
                title: 'Progress',
            }),
            ui.nestedFields.numeric({
                bind: 'fixedQuantity',
                title: 'Fixed Quantity',
                isTransientInput: true,
            }),
            ui.nestedFields.checkbox({
                bind: 'hotProduct',
                title: 'Hot',
            }),
            ui.nestedFields.numeric({
                bind: 'netPrice',
                title: 'Net Price',
                isReadOnly: (value: number, rowValue: any) => {
                    return !!rowValue.hotProduct;
                },
                scale: 2,
                isHiddenDesktop: false,
            }),
            ui.nestedFields.filterSelect<TableMultiActionButton, ShowCaseProduct, ShowCaseCustomer>({
                bind: 'email',
                title: 'Email',
                valueField: 'email',
                node: '@sage/xtrem-show-case/ShowCaseCustomer',
                isExcludedFromMainField: false,
                minLookupCharacters: 0,
                width: 'large',
                onChange(id: string, raw: string) {
                    ui.console.log('CHANGED FILTER SELECT', { id, raw });
                },
            }),
            ui.nestedFields.date<TableMultiActionButton, ShowCaseProduct>({
                bind: 'releaseDate',
                title: 'Date',
            }),
            ui.nestedFields.date({
                bind: 'endingDate',
                title: 'Ending date',
                isExcludedFromMainField: true,
            }),
            ui.nestedFields.select<TableMultiActionButton, ShowCaseProduct>({
                bind: 'category',
                title: 'Category',
                optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
            }),
            ui.nestedFields.image({
                bind: 'imageField',
                title: 'Image',
                isExcludedFromMainField: true,
            }),
            ui.nestedFields.icon({
                bind: 'qty',
                title: 'Icon',
                isExcludedFromMainField: true,

                map: (value: any) => {
                    return value === 0 ? 'error' : value < 10 ? 'warning' : '';
                },
            }),
            ui.nestedFields.technical<TableMultiActionButton, ShowCaseProduct, ShowCaseProductOriginAddress>({
                bind: 'originAddress',
                node: '@sage/xtrem-show-case/ShowCaseProductOriginAddress',
                nestedFields: [
                    ui.nestedFields.text({
                        bind: 'name',
                    }),
                    ui.nestedFields.text({
                        bind: 'addressLine1',
                    }),
                    ui.nestedFields.text({
                        bind: 'addressLine2',
                    }),
                    ui.nestedFields.reference({
                        bind: 'country',
                        node: '@sage/xtrem-show-case/ShowCaseCountry',
                        valueField: 'name',
                        helperTextField: 'code',
                    }),
                ],
            }),
            ui.nestedFields.numeric<TableMultiActionButton, ShowCaseProduct>({
                bind: 'discount',
                title: 'Discount',
                isExcludedFromMainField: true,
            }),
            ui.nestedFields.datetimeRange({
                bind: 'manufacturedWithin',
                isFullWidth: true,
                title: 'Manufacterd within',
            }),
        ],
        mobileCard: {
            title: ui.nestedFields.text({ bind: 'product', title: 'Product' }),
            titleRight: ui.nestedFields.text({ bind: '_id', title: 'ID', canFilter: false }),
            line2: ui.nestedFields.text({ bind: 'description', title: 'Description', canFilter: false }),
            listPrice: ui.nestedFields.numeric({ bind: 'listPrice', title: 'List Price', isHiddenOnMainField: true }),
            image: ui.nestedFields.image({ bind: 'imageField' }),
        },
        orderBy: {
            product: 1,
        },
        parent() {
            return this.fieldBlock;
        },
        dropdownActions: [
            {
                icon: 'box_arrow_left',
                title: 'Edit on sidebar',
                async onClick(rowId: any) {
                    this.tableWithSidebar.openSidebar(rowId);
                },
            },
        ],
        addItemActions() {
            return [this.customAddLine, this.customAddLineDisabled];
        },
        sidebar: {
            headerDropdownActions: [
                {
                    title: 'Do stuff',
                    onClick(_id, rowData) {
                        this.$.showToast(`It did stuff! ${_id} ${rowData.product}`);
                    },
                },
                ui.menuSeparator(),
                {
                    title: 'Some other stuff',
                    icon: 'bank_with_card',
                    onClick(_id, rowData) {
                        this.$.showToast(`It did some other stuff! ${_id} ${rowData.product}`, {
                            type: 'warning',
                        });
                    },
                },
                ui.menuSeparator(),
                {
                    icon: 'delete',
                    title: 'Remove',
                    async onClick(_id) {
                        this.tableWithSidebar.removeRecord(_id);
                    },
                },
            ],
            headerQuickActions: [
                {
                    title: 'Lorem',
                    icon: 'three_boxes',
                    onClick(_id, rowData) {
                        this.$.showToast(`You clicked Lorem ${_id} ${rowData.product}`);
                    },
                },
                {
                    title: 'Ipsum',
                    icon: 'video',
                    onClick(_id, rowData) {
                        this.$.showToast(`You clicked Ipsum ${_id} ${rowData.product}`);
                    },
                },
            ],
            title(_id, recordValue) {
                return recordValue.product;
            },
            layout() {
                return {
                    mainSection: {
                        title: 'Main section',
                        blocks: {
                            mainBlock: {
                                title: 'Some block title',
                                fields: [
                                    'product',
                                    'tax',
                                    'provider',
                                    'category',
                                    'qty',
                                    'amount',
                                    'description',
                                    'manufacturedWithin',
                                ],
                            },
                            anotherBlock: {
                                title: 'Address',
                                fields: ['listPrice', 'netPrice'],
                            },
                        },
                    },
                    anotherSection: {
                        isHidden(_id, rowData) {
                            return rowData.qty < 10;
                        },
                        title: 'Some other section',
                        blocks: {
                            someRandomBlock: {
                                fields: ['discount', 'releaseDate'],
                            },
                        },
                    },
                };
            },
        },
    })
    tableWithSidebar: ui.fields.Table<ShowCaseProduct>;

    @ui.decorators.pageAction<TableMultiActionButton>({
        title: 'Custom add line action',
        icon: 'factory',
        onClick() {
            this.$.showToast('Custom action is triggered!!');
        },
    })
    customAddLine: ui.PageAction;

    @ui.decorators.pageAction<TableMultiActionButton>({
        title: 'Disabled add line action',
        icon: 'feedback',
        isDisabled: true,
        onClick() {
            this.$.showToast('You should not see this because this action is disabled');
        },
    })
    customAddLineDisabled: ui.PageAction;
}
