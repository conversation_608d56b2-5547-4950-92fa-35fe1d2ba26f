import { GraphA<PERSON> } from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import { tableField } from '../menu-items/_index';

@ui.decorators.page<TableMobileCardView>({
    authorizationCode: 'TBLMBCRD',
    category: 'SHOWCASE',
    isTransient: true,
    module: 'show-case',
    title: 'Table with Mobile Card View',
    menuItem: tableField,
})
export class TableMobileCardView extends ui.Page<GraphApi> {
    @ui.decorators.section<TableMobileCardView>({
        isTitleHidden: true,
    })
    section: ui.containers.Section;

    @ui.decorators.block<TableMobileCardView>({
        parent() {
            return this.section;
        },
    })
    block: ui.containers.Block;

    @ui.decorators.staticContentField<TableMobileCardView>({
        content:
            'Starting with the first row, every second row should not have an image and every third row should not have a title.',
        isFullWidth: true,
        isTransient: true,
        parent() {
            return this.block;
        },
    })
    info: ui.fields.StaticContent;

    @ui.decorators.buttonField<TableMobileCardView>({
        map() {
            return 'Load Data';
        },
        async onClick() {
            const result = await this.$.graph
                .node('@sage/xtrem-show-case/ShowCaseProduct')
                .query(
                    ui.queryUtils.edgesSelector({
                        _id: true,
                        amount: true,
                        barcode: true,
                        imageField: { value: true },
                        product: true,
                        releaseDate: true,
                        tax: true,
                        qty: true,
                        endingDate: true,
                        email: true,
                        provider: { textField: true },
                    }),
                )
                .execute();

            const products = result.edges
                .map((edge: any) => {
                    return edge.node;
                })
                .map((node: any, index: number) => {
                    const productResult = { ...node };

                    if (index % 2 === 0) {
                        productResult.imageField = undefined;
                    }

                    if (index % 3 === 0) {
                        productResult.product = undefined;
                    }

                    return productResult;
                });

            this.field.value = products;
        },
        parent() {
            return this.block;
        },
    })
    button: ui.fields.Button;

    @ui.decorators.tableField<TableMobileCardView>({
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        columns: [
            ui.nestedFields.text({
                bind: '_id',
                title: 'Id',
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                bind: 'product',
                title: 'Product',
                isReadOnly: true,
            }),
            ui.nestedFields.date({
                bind: 'releaseDate',
                title: 'Release Date',
                isReadOnly: true,
            }),
            ui.nestedFields.numeric({
                bind: 'amount',
                title: 'Price',
                prefix: '$',
                scale: 2,
            }),
            ui.nestedFields.image({
                bind: 'imageField',
                title: 'Image',
                isReadOnly: true,
            }),
        ],
        mobileCard: {
            title: ui.nestedFields.text({
                bind: 'product',
                title: 'Product',
            }),
            titleRight: ui.nestedFields.text({
                bind: 'barcode',
                title: 'Barcode',
            }),
            line2: ui.nestedFields.label({
                bind: 'amount',
                title: 'Price',
            }),
            line2Right: ui.nestedFields.date({
                bind: 'releaseDate',
                title: 'Release Date',
            }),
            line3: ui.nestedFields.label({
                bind: '_id',
                title: 'ID',
            }),
            line3Right: ui.nestedFields.numeric({
                bind: 'tax',
                title: 'Tax',
            }),
            line4: ui.nestedFields.date({
                bind: 'endingDate',
                title: 'End Date',
            }),
            line4Right: ui.nestedFields.numeric({
                bind: 'qty',
                title: 'Quantity',
            }),
            line5: ui.nestedFields.label({
                bind: 'email',
                title: 'Email',
            }),
            line5Right: ui.nestedFields.reference({
                bind: 'provider',
                node: '@sage/xtrem-show-case/ShowCaseProvider',
                valueField: 'textField',
                title: 'Provider',
            }),
            image: ui.nestedFields.image({
                bind: 'imageField',
                title: 'Image',
            }),
        },
        orderBy: { _id: 1 },
        parent() {
            return this.block;
        },
    })
    field: ui.fields.Table;
}
