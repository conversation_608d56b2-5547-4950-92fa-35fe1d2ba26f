import * as ui from '@sage/xtrem-ui';
import { fields } from '../menu-items/fields';

@ui.decorators.page<Image>({
    authorizationCode: 'IMGFLDS',
    category: 'SHOWCASE',
    isTransient: true,
    menuItem: fields,
    module: 'show-case',
    onLoad() {
        this.field.title = Image.DEFAULT_CONSTANTS.FIELD_TITLE;
        this.configFieldTitle.value = Image.DEFAULT_CONSTANTS.FIELD_TITLE;
        this.fieldEvent.value = Image.DEFAULT_CONSTANTS.FIELD_EVENTS;
    },
    subtitle: 'Xtrem Image Field Showcase',
    title: 'Image',
})
export class Image extends ui.Page {
    static DEFAULT_CONSTANTS = {
        FIELD_EVENTS: 'No current event...',
        FIELD_TITLE: 'Field Title',
    };

    @ui.decorators.section<Image>({
        title: 'Image Field',
    })
    section: ui.containers.Section;

    @ui.decorators.block<Image>({
        parent() {
            return this.section;
        },
        title: 'Field Example',
    })
    fieldBlock: ui.containers.Block;

    @ui.decorators.imageField<Image>({
        onChange() {
            this.fieldEvent.value = "Field's onChange event was triggered";
            setTimeout(() => {
                this.fieldEvent.value = Image.DEFAULT_CONSTANTS.FIELD_EVENTS;
            }, 2500);
        },
        onClick() {
            this.fieldEvent.value = "Field's onClick event was triggered";
            setTimeout(() => {
                this.fieldEvent.value = Image.DEFAULT_CONSTANTS.FIELD_EVENTS;
            }, 2500);
        },
        parent() {
            return this.fieldBlock;
        },
        title: 'Image Title',
        width: 'large',
    })
    field: ui.fields.Image;

    @ui.decorators.labelField<Image>({
        parent() {
            return this.fieldBlock;
        },
        title: 'Event',
        width: 'medium',
    })
    fieldEvent: ui.fields.Label;

    @ui.decorators.block<Image>({
        parent() {
            return this.section;
        },
        title: 'Field Configuration',
    })
    configBlock: ui.containers.Block;

    @ui.decorators.checkboxField<Image>({
        onChange() {
            this.field.isTitleHidden = this.configFieldTitleHidden.value;
        },
        parent() {
            return this.configBlock;
        },
        title: 'Is Title Hidden?',
    })
    configFieldTitleHidden: ui.fields.Checkbox;

    @ui.decorators.textField<Image>({
        onChange() {
            this.field.title = this.configFieldTitle.value;
        },
        parent() {
            return this.configBlock;
        },
        title: 'Title',
        width: 'large',
    })
    configFieldTitle: ui.fields.Text;

    @ui.decorators.separatorField<Image>({
        isFullWidth: true,
        isInvisible: true,
        parent() {
            return this.configBlock;
        },
    })
    configSeperator1: ui.fields.Separator;

    @ui.decorators.checkboxField<Image>({
        onChange() {
            this.field.isHelperTextHidden = this.configFieldHelperTextHidden.value;
        },
        parent() {
            return this.configBlock;
        },
        title: 'Is Helper Text Hidden?',
    })
    configFieldHelperTextHidden: ui.fields.Checkbox;

    @ui.decorators.textField<Image>({
        onChange() {
            this.field.helperText = this.configFieldHelperText.value;
        },
        parent() {
            return this.configBlock;
        },
        title: 'Helper Text',
        width: 'large',
    })
    configFieldHelperText: ui.fields.Text;

    @ui.decorators.separatorField<Image>({
        isFullWidth: true,
        isInvisible: true,
        parent() {
            return this.configBlock;
        },
    })
    configSeperator2: ui.fields.Separator;

    @ui.decorators.checkboxField<Image>({
        onChange() {
            this.field.isHidden = this.configFieldHidden.value;
        },
        parent() {
            return this.configBlock;
        },
        title: 'Is Hidden?',
    })
    configFieldHidden: ui.fields.Checkbox;

    @ui.decorators.checkboxField<Image>({
        onChange() {
            this.field.isDisabled = this.configFieldDisabled.value;
        },
        parent() {
            return this.configBlock;
        },
        title: 'Is Disabled?',
    })
    configFieldDisabled: ui.fields.Checkbox;

    @ui.decorators.checkboxField<Image>({
        onChange() {
            this.field.isReadOnly = this.configFieldReadonly.value;
        },
        parent() {
            return this.configBlock;
        },
        title: 'Is Readonly?',
    })
    configFieldReadonly: ui.fields.Checkbox;

    @ui.decorators.checkboxField<Image>({
        onChange() {
            this.field.isMandatory = this.configFieldMandatory.value;
        },
        parent() {
            return this.configBlock;
        },
        title: 'Is Mandatory?',
    })
    configFieldMandatory: ui.fields.Checkbox;

    @ui.decorators.separatorField<Image>({
        isFullWidth: true,
        isInvisible: true,
        parent() {
            return this.configBlock;
        },
    })
    configSeperator3: ui.fields.Separator;

    @ui.decorators.buttonField<Image>({
        map() {
            return 'Focus';
        },
        onClick() {
            this.field.focus();
        },
        parent() {
            return this.configBlock;
        },
        width: 'large',
    })
    configFieldFocus: ui.fields.Button;

    @ui.decorators.block<Image>({
        parent() {
            return this.section;
        },
        title: 'Field Sizes',
    })
    sizesBlock: ui.containers.Block;

    @ui.decorators.imageField<Image>({
        parent() {
            return this.sizesBlock;
        },
        title: 'Default',
    })
    sizesDefault: ui.fields.Image;

    @ui.decorators.imageField<Image>({
        parent() {
            return this.sizesBlock;
        },
        width: 'small',
        title: 'Small',
    })
    sizesSmall: ui.fields.Image;

    @ui.decorators.imageField<Image>({
        parent() {
            return this.sizesBlock;
        },
        width: 'medium',
        title: 'Medium',
    })
    sizesMedium: ui.fields.Image;

    @ui.decorators.imageField<Image>({
        parent() {
            return this.sizesBlock;
        },
        width: 'large',
        title: 'Large',
    })
    sizesLarge: ui.fields.Image;
}
