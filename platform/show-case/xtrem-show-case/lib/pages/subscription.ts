import * as ui from '@sage/xtrem-ui';
import { fields } from '../menu-items/fields';

@ui.decorators.page<Subscription>({
    menuItem: fields,
    title: 'Subscription',
    isTransient: true,
    onLoad() {
        this.$.subscribeToEvent('nodeModified', (payload: any) => {
            const timestamp = new Date().toISOString();
            this.textArea.value = `[${timestamp}] ${JSON.stringify(payload, null, 2)}\n${this.textArea.value ?? ''}`;
        });
    },
})
export class Subscription extends ui.Page {
    @ui.decorators.section<Subscription>({
        title: 'Subscription',
        isTitleHidden: true,
    })
    section: ui.containers.Section<Subscription>;

    @ui.decorators.block<Subscription>({
        parent() {
            return this.section;
        },
        title: 'Subscription',
        isTitleHidden: true,
    })
    fieldBlock: ui.containers.Block<Subscription>;

    @ui.decorators.textAreaField<Subscription>({
        parent() {
            return this.fieldBlock;
        },
        rows: 20,
        isReadOnly: true,
        isFullWidth: true,
        title: 'Event log',
        helperText: 'This is a log of node events triggered by the application.',
    })
    textArea: ui.fields.TextArea<Subscription>;
}
