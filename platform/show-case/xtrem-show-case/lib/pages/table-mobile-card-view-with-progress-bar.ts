import { Graph<PERSON><PERSON> } from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import { tableField } from '../menu-items/_index';

@ui.decorators.page<TableMobileCardViewWithProgressBar>({
    authorizationCode: 'TBLMBCRD',
    category: 'SHOWCASE',
    isTransient: true,
    module: 'show-case',
    title: 'Table with Mobile Card View with progress bar',
    menuItem: tableField,
})
export class TableMobileCardViewWithProgressBar extends ui.Page<GraphApi> {
    @ui.decorators.section<TableMobileCardViewWithProgressBar>({
        isTitleHidden: true,
    })
    section: ui.containers.Section;

    @ui.decorators.block<TableMobileCardViewWithProgressBar>({
        parent() {
            return this.section;
        },
    })
    block: ui.containers.Block;

    @ui.decorators.buttonField<TableMobileCardViewWithProgressBar>({
        map() {
            return 'Load Data';
        },
        async onClick() {
            const result = await this.$.graph
                .node('@sage/xtrem-show-case/ShowCaseProduct')
                .query(
                    ui.queryUtils.edgesSelector({
                        _id: true,
                        amount: true,
                        barcode: true,
                        product: true,
                        releaseDate: true,
                        progress: true,
                        tax: true,
                    }),
                )
                .execute();

            const products = result.edges
                .map((edge: any) => {
                    return edge.node;
                })
                .map((node: any, index: number) => {
                    const productResult = { ...node };

                    if (index % 2 === 0) {
                        productResult.icon = 'business';
                    } else {
                        productResult.icon = 'boxed_shapes';
                    }
                    return productResult;
                });

            this.field.value = products;
            this.hiddenLabelsField.value = products;
        },
        parent() {
            return this.block;
        },
    })
    button: ui.fields.Button;

    @ui.decorators.tableField<TableMobileCardViewWithProgressBar>({
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        title: 'With progress bar and icon',
        cardView: true,
        columns: [
            ui.nestedFields.text({
                bind: '_id',
                title: 'Id',
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                bind: 'product',
                title: 'Product',
                isReadOnly: true,
            }),
            ui.nestedFields.date({
                bind: 'releaseDate',
                title: 'Release Date',
                isReadOnly: true,
            }),
            ui.nestedFields.numeric({
                bind: 'amount',
                title: 'Price',
                prefix: '$',
                scale: 2,
            }),
        ],
        mobileCard: {
            title: ui.nestedFields.text({
                bind: 'product',
                title: 'Product',
            }),
            line2: ui.nestedFields.label({
                bind: 'amount',
                title: 'Price',
            }),
            line2Right: ui.nestedFields.date({
                bind: 'releaseDate',
                title: 'Release Date',
            }),
            image: ui.nestedFields.icon({
                bind: 'icon',
                isTransient: true,
            }),
            progressBar: ui.nestedFields.progress({
                bind: 'progress',
                title: 'Progress',
            }),
        },
        orderBy: { _id: 1 },
        parent() {
            return this.block;
        },
    })
    field: ui.fields.Table;

    @ui.decorators.tableField<TableMobileCardViewWithProgressBar>({
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        title: 'With progress bar with hidden labels',
        cardView: true,
        columns: [
            ui.nestedFields.text({
                bind: '_id',
                title: 'Id',
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                bind: 'product',
                title: 'Product',
                isReadOnly: true,
            }),
            ui.nestedFields.date({
                bind: 'releaseDate',
                title: 'Release Date',
                isReadOnly: true,
            }),
            ui.nestedFields.numeric({
                bind: 'amount',
                title: 'Price',
                prefix: '$',
                scale: 2,
            }),
        ],
        mobileCard: {
            title: ui.nestedFields.text({
                bind: 'product',
                title: 'Product',
            }),
            line2: ui.nestedFields.label({
                bind: 'amount',
                title: 'Price',
            }),
            line2Right: ui.nestedFields.date({
                bind: 'releaseDate',
                title: 'Release Date',
            }),
            image: ui.nestedFields.icon({
                bind: 'icon',
                isTransient: true,
            }),
            progressBar: ui.nestedFields.progress({
                bind: 'progress',
                title: 'Progress',
                areProgressLabelsHidden: true,
            }),
        },
        orderBy: { _id: 1 },
        parent() {
            return this.block;
        },
    })
    hiddenLabelsField: ui.fields.Table;
}
