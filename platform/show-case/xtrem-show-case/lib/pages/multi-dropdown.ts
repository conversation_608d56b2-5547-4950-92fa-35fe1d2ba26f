import * as ui from '@sage/xtrem-ui';
import { fields } from '../menu-items/fields';

const map = (value: any) => {
    switch (value) {
        case 'change':
            return 'Change Event';
        case 'click':
            return 'Click Event';
        case 'error':
            return 'Error Event';
        default:
            return '';
    }
};

@ui.decorators.page<MultiDropdown>({
    authorizationCode: 'DRPDLST',
    category: 'SHOWCASE',
    module: 'show-case',
    isTransient: true,
    title: 'Field - Multi-dropdown List',
    menuItem: fields,
})
export class MultiDropdown extends ui.Page {
    @ui.decorators.section<MultiDropdown>({
        title: 'Multi-dropdown List Field',
    })
    section: ui.containers.Section;

    @ui.decorators.block<MultiDropdown>({
        title: 'Field Example',
        parent() {
            return this.section;
        },
    })
    block: ui.containers.Block;

    @ui.decorators.multiDropdownField<MultiDropdown>({
        isTransient: true,
        options() {
            return ['one', 'two', 'three', 'four', 'five'];
        },
        title: 'With Options',
        map(value: any) {
            switch (value) {
                case 'one':
                    return 'One';
                case 'two':
                    return 'Two';
                case 'three':
                    return 'Three';
                case 'four':
                    return 'Four';
                case 'five':
                    return 'Five';
                default:
                    return value;
            }
        },
        onChange() {
            ui.console.log("Do something when field's value has changed.");
            this.value.value = this.field1.value.join(', ');
            this.label1.value = 'change';
            setTimeout(() => {
                this.label1.value = '';
            }, 2000);
        },
        onClick() {
            ui.console.log('Do something when field has been clicked.');
            this.label1.value = 'click';
            setTimeout(() => {
                this.label1.value = '';
            }, 2000);
        },
        onError(error, screenId, elementId) {
            ui.console.log("Do something when field's callback has thrown an error", { error, screenId, elementId });
            this.label1.value = 'error';
            setTimeout(() => {
                this.label1.value = '';
            }, 2000);
        },
        parent() {
            return this.block;
        },
    })
    field1: ui.fields.MultiDropdown;

    @ui.decorators.labelField<MultiDropdown>({
        map,
        parent() {
            return this.block;
        },
    })
    label1: ui.fields.Label;

    @ui.decorators.multiDropdownField<MultiDropdown>({
        isTransient: true,
        optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
        title: 'With OptionType',
        map(value) {
            switch (value) {
                case 'great':
                    return 'Great';
                case 'good':
                    return 'Good';
                case 'ok':
                    return 'OK';
                case 'notBad':
                    return 'Not Bad';
                case 'awful':
                    return 'Awful';
                default:
                    return value;
            }
        },
        onChange() {
            ui.console.log("Do something when field's value has changed.");
            this.label2.value = 'change';
            setTimeout(() => {
                this.label2.value = '';
            }, 2000);
        },
        onClick() {
            ui.console.log('Do something when field has been clicked.');
            this.label2.value = 'click';
            setTimeout(() => {
                this.label2.value = '';
            }, 2000);
        },
        onError(error, screenId, elementId) {
            ui.console.log("Do something when field's callback has thrown an error", { error, screenId, elementId });
            this.label2.value = 'error';
            setTimeout(() => {
                this.label2.value = '';
            }, 2000);
        },
        parent() {
            return this.block;
        },
    })
    field2: ui.fields.MultiDropdown;

    @ui.decorators.labelField<MultiDropdown>({
        map,
        parent() {
            return this.block;
        },
    })
    label2: ui.fields.Label;

    @ui.decorators.block<MultiDropdown>({
        title: 'Field Configuration',
        parent() {
            return this.section;
        },
    })
    configuration: ui.containers.Block;

    @ui.decorators.textField<MultiDropdown>({
        title: 'Helper Text',
        onChange() {
            this.field1.helperText = this.helperText.value;
        },
        parent() {
            return this.configuration;
        },
    })
    helperText: ui.fields.Text;

    @ui.decorators.checkboxField<MultiDropdown>({
        title: 'Is Disabled',
        onChange() {
            this.field1.isDisabled = this.isDisabled.value;
        },
        parent() {
            return this.configuration;
        },
    })
    isDisabled: ui.fields.Checkbox;

    @ui.decorators.checkboxField<MultiDropdown>({
        title: 'Is Helper Text Hidden',
        onChange() {
            this.field1.isHelperTextHidden = this.isHelperTextHidden.value;
        },
        parent() {
            return this.configuration;
        },
    })
    isHelperTextHidden: ui.fields.Checkbox;

    @ui.decorators.checkboxField<MultiDropdown>({
        title: 'Is Hidden',
        onChange() {
            this.field1.isHidden = this.isHidden.value;
        },
        parent() {
            return this.configuration;
        },
    })
    isHidden: ui.fields.Checkbox;

    @ui.decorators.checkboxField<MultiDropdown>({
        title: 'Is Mandatory',
        onChange() {
            this.field1.isMandatory = this.isMandatory.value;
        },
        parent() {
            return this.configuration;
        },
    })
    isMandatory: ui.fields.Checkbox;

    @ui.decorators.checkboxField<MultiDropdown>({
        title: 'Is Read-Only',
        onChange() {
            this.field1.isReadOnly = this.isReadOnly.value;
        },
        parent() {
            return this.configuration;
        },
    })
    isReadOnly: ui.fields.Checkbox;

    @ui.decorators.checkboxField<MultiDropdown>({
        title: 'Is Title Hidden',
        onChange() {
            this.field1.isTitleHidden = this.isTitleHidden.value;
        },
        parent() {
            return this.configuration;
        },
    })
    isTitleHidden: ui.fields.Checkbox;

    @ui.decorators.textField<MultiDropdown>({
        title: 'Placeholder',
        onChange() {
            this.field1.placeholder = this.placeholder.value;
        },
        parent() {
            return this.configuration;
        },
    })
    placeholder: ui.fields.Text;

    @ui.decorators.numericField<MultiDropdown>({
        title: 'Set Minimum of Selectable Items',
        onChange() {
            this.field1.minItems = this.minItems.value;
        },
        parent() {
            return this.configuration;
        },
    })
    minItems: ui.fields.Numeric;

    @ui.decorators.numericField<MultiDropdown>({
        title: 'Set Maximum of Selectable Items',
        onChange() {
            this.field1.maxItems = this.maxItems.value;
        },
        parent() {
            return this.configuration;
        },
    })
    maxItems: ui.fields.Numeric;

    @ui.decorators.textField<MultiDropdown>({
        title: 'Title',
        onChange() {
            this.field1.title = this.title.value;
        },
        parent() {
            return this.configuration;
        },
    })
    title: ui.fields.Text;

    @ui.decorators.textField<MultiDropdown>({
        title: 'Value',
        isReadOnly: true,
        onChange() {
            if (this.field1.value.join(', ') !== this.value.value) {
                this.field1.value = this.value.value.split(', ');
            }
        },
        parent() {
            return this.configuration;
        },
    })
    value: ui.fields.Text;

    @ui.decorators.buttonField<MultiDropdown>({
        map() {
            return 'Focus Field';
        },
        onClick() {
            this.field1.focus();
        },
        parent() {
            return this.configuration;
        },
    })
    focus: ui.fields.Button;

    @ui.decorators.buttonField<MultiDropdown>({
        map() {
            return 'Validate Field';
        },
        onClick() {
            this.field1.validate();
        },
        parent() {
            return this.configuration;
        },
    })
    validate: ui.fields.Button;

    @ui.decorators.block<MultiDropdown>({
        title: 'Field Sizes',
        parent() {
            return this.section;
        },
    })
    sizes: ui.containers.Block;

    @ui.decorators.multiDropdownField<MultiDropdown>({
        isTransient: true,
        options: ['one', 'two', 'three'],
        size: 'small',
        title: 'Small',
        parent() {
            return this.sizes;
        },
    })
    small: ui.fields.MultiDropdown;

    @ui.decorators.multiDropdownField<MultiDropdown>({
        isTransient: true,
        options: ['one', 'two', 'three'],
        size: 'medium',
        title: 'Medium',
        parent() {
            return this.sizes;
        },
    })
    medium: ui.fields.MultiDropdown;

    @ui.decorators.multiDropdownField<MultiDropdown>({
        isTransient: true,
        options: ['one', 'two', 'three'],
        size: 'large',
        title: 'Large',
        parent() {
            return this.sizes;
        },
    })
    large: ui.fields.MultiDropdown;

    @ui.decorators.block<MultiDropdown>({
        title: 'Validation',
        parent() {
            return this.section;
        },
    })
    validation: ui.containers.Block;

    @ui.decorators.multiDropdownField<MultiDropdown>({
        isFullWidth: true,
        isMandatory: true,
        isTransient: true,
        options: ['Success', 'Error'],
        title: 'With Custom Validation',
        parent() {
            return this.validation;
        },
        validation(value) {
            return value.includes('Error') ? 'Oh, you selected an error.' : '';
        },
    })
    validated: ui.fields.MultiDropdown;

    @ui.decorators.block<MultiDropdown>({
        title: 'Error Handlers',
        parent() {
            return this.section;
        },
    })
    errors: ui.containers.Block;

    @ui.decorators.multiDropdownField<MultiDropdown>({
        isTransient: true,
        options: ['One', 'Two', 'Three'],
        title: 'With Error Handler',
        parent() {
            return this.errors;
        },
        onChange() {
            throw new Error('Oh, an error was thrown.');
        },
        onError(error) {
            return error.message;
        },
    })
    errorHandled: ui.fields.MultiDropdown;

    @ui.decorators.multiDropdownField<MultiDropdown>({
        isTransient: true,
        options: ['One', 'Two', 'Three'],
        title: 'Without Error Handler',
        parent() {
            return this.errors;
        },
        onChange() {
            throw new Error('Oh, an error was thrown.');
        },
    })
    errorUnhandled: ui.fields.MultiDropdown;

    @ui.decorators.block<MultiDropdown>({
        title: 'Localization',
        parent() {
            return this.section;
        },
    })
    localization: ui.containers.Block;

    @ui.decorators.multiDropdownField<MultiDropdown>({
        isTransient: true,
        optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
        title: 'With Translations',
        parent() {
            return this.localization;
        },
    })
    fieldLocalized: ui.fields.MultiDropdown;

    @ui.decorators.block<MultiDropdown>({
        parent() {
            return this.section;
        },
        title: 'Additional examples',
    })
    additionalBlock: ui.containers.Block;

    @ui.decorators.separatorField<MultiDropdown>({
        parent() {
            return this.additionalBlock;
        },
        isFullWidth: true,
    })
    fieldSeparator1: ui.fields.Separator;

    @ui.decorators.multiDropdownField<MultiDropdown>({
        parent() {
            return this.additionalBlock;
        },
        title: 'With warning message',
        warningMessage: 'Wow, warning!',
        optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
    })
    warningMessageField: ui.fields.MultiDropdown;

    @ui.decorators.multiDropdownField<MultiDropdown>({
        parent() {
            return this.additionalBlock;
        },
        title: 'With warning message with callback',
        helperText: 'Select at least 3 items',
        warningMessage() {
            if (this.warningMessageWithCallbackField.value && this.warningMessageWithCallbackField.value.length > 2) {
                return 'Warning message';
            }
            return null;
        },
        optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
    })
    warningMessageWithCallbackField: ui.fields.MultiDropdown;

    @ui.decorators.multiDropdownField<MultiDropdown>({
        parent() {
            return this.additionalBlock;
        },
        title: 'With info message',
        infoMessage: 'Wow, warning!',
        optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
    })
    infoMessageField: ui.fields.MultiDropdown;

    @ui.decorators.multiDropdownField<MultiDropdown>({
        parent() {
            return this.additionalBlock;
        },
        title: 'With info message with callback',
        helperText: 'Select at least 3 items',
        warningMessage() {
            if (this.infoMessageWithCallbackField.value && this.infoMessageWithCallbackField.value.length > 2) {
                return 'Warning message';
            }
            return null;
        },
        optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
    })
    infoMessageWithCallbackField: ui.fields.MultiDropdown;

    @ui.decorators.multiDropdownField<MultiDropdown>({
        parent() {
            return this.additionalBlock;
        },
        title: 'With info message',
        warningMessage: 'Wow, warning!',
        infoMessage: 'You should not see this',
        optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
    })
    infoAndWarningMessageField: ui.fields.MultiDropdown;

    @ui.decorators.multiDropdownField<MultiDropdown>({
        parent() {
            return this.additionalBlock;
        },
        validation() {
            if (
                this.infoAndWarningMessageMandatoryField.value &&
                this.infoAndWarningMessageMandatoryField.value.length > 2
            ) {
                return 'Error message';
            }
            return '';
        },
        title: 'Info, warning and validation',
        warningMessage: 'Wow, warning!',
        infoMessage: 'You should not see this',
        helperText: 'Not more than 2 products',
        optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
    })
    infoAndWarningMessageMandatoryField: ui.fields.MultiDropdown;
}
