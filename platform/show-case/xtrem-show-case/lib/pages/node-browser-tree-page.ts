import { GraphA<PERSON>, ShowCaseNodeBrowserTree as ShowCaseNodeBrowserTreeNode } from '@sage/xtrem-show-case-api';
import { MetaNodeFactory } from '@sage/xtrem-metadata-api';
import * as ui from '@sage/xtrem-ui';
import { fields } from '../menu-items/fields';

@ui.decorators.page<NodeBrowserTreePage, ShowCaseNodeBrowserTreeNode>({
    module: 'show-case',
    title: 'Node Browser Tree',
    category: 'SHOWCASE',
    menuItem: fields,
    isTransient: true,
    businessActions() {
        return [this.$standardSaveAction];
    },
    createAction() {
        return this.$standardNewAction;
    },
    headerDropDownActions() {
        return [this.$standardDeleteAction];
    },
    node: '@sage/xtrem-show-case/ShowCaseNodeBrowserTree',
})
export class NodeBrowserTreePage extends ui.Page<GraphApi> {
    @ui.decorators.section<NodeBrowserTreePage>({
        title: 'Node Browser Tree Field',
    })
    section: ui.containers.Section;

    @ui.decorators.block<NodeBrowserTreePage>({
        title: 'Field example',
        parent() {
            return this.section;
        },
    })
    fieldBlock: ui.containers.Block;

    @ui.decorators.nodeBrowserTreeField<NodeBrowserTreePage>({
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        title: 'Node Browser Tree',
        parent() {
            return this.fieldBlock;
        },
        onClick() {
            this.clickTriggered.isHidden = false;
            setTimeout(() => {
                this.clickTriggered.isHidden = true;
            }, 5000);
        },
        onChange() {
            this.changeTriggered.isHidden = false;
            setTimeout(() => {
                this.changeTriggered.isHidden = true;
            }, 5000);
        },
    })
    field: ui.fields.NodeBrowserTree;

    @ui.decorators.labelField<NodeBrowserTreePage>({
        parent() {
            return this.fieldBlock;
        },
        isHidden: true,
        isTransient: true,
        map() {
            return 'Change was triggered';
        },
    })
    changeTriggered: ui.fields.Label;

    @ui.decorators.labelField<NodeBrowserTreePage>({
        parent() {
            return this.fieldBlock;
        },
        isHidden: true,
        isTransient: true,
        map() {
            return 'Click was triggered';
        },
    })
    clickTriggered: ui.fields.Label;

    @ui.decorators.block<NodeBrowserTreePage>({
        parent() {
            return this.section;
        },
        title: 'Configuration',
    })
    configurationBlock: ui.containers.Block;

    @ui.decorators.checkboxField<NodeBrowserTreePage>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is disabled',
        isTransient: true,
        helperText:
            'Determines whether the field is disabled or not. It can also be defined as callback function that returns a boolean.',
        isReversed: true,
        onChange() {
            this.field.isDisabled = !!this.isDisabled.value;
        },
    })
    isDisabled: ui.fields.Checkbox;

    @ui.decorators.checkboxField<NodeBrowserTreePage>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is read only',
        isTransient: true,
        helperText:
            'Whether the field is editable (isReadOnly = false) or not (isReadOnly = true). The difference with disabled is that isReadOnly suggests that the field is never editable. It can be defined as a boolean, or conditionally by a callback that returns a boolean.',
        isReversed: true,
        onChange() {
            this.field.isReadOnly = !!this.isReadOnly.value;
        },
    })
    isReadOnly: ui.fields.Checkbox;

    @ui.decorators.checkboxField<NodeBrowserTreePage>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is hidden',
        isTransient: true,
        helperText: 'Determines whether the field is displayed or not.',
        isReversed: true,
        onChange() {
            this.field.isHidden = !!this.isHidden.value;
        },
    })
    isHidden: ui.fields.Checkbox;

    @ui.decorators.textField<NodeBrowserTreePage>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Title',
        isTransient: true,
        helperText:
            'The title that is displayed above the field. The title can be provided as a string, or a callback function returning a string. When declared as a callback within the column of a nested grid, the column id is provided as a parameter. It is automatically picked up by the i18n engine and externalized for translation.',
        onChange() {
            this.field.title = this.title.value || '';
        },
    })
    title: ui.fields.Text;

    @ui.decorators.checkboxField<NodeBrowserTreePage>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is title hidden',
        isTransient: true,
        helperText: 'Whether the field title above the field should be displayed and its vertical space preserved.',
        isReversed: true,
        onChange() {
            this.field.isTitleHidden = !!this.isTitleHidden.value;
        },
    })
    isTitleHidden: ui.fields.Checkbox;

    @ui.decorators.textField<NodeBrowserTreePage>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Helper text',
        isTransient: true,
        helperText:
            'The helper text that is displayed above the field. It is automatically picked up by the i18n engine and externalized.',
        onChange() {
            this.field.helperText = this.helperText.value || '';
        },
    })
    helperText: ui.fields.Text;

    @ui.decorators.checkboxField<NodeBrowserTreePage>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is helper text hidden',
        isTransient: true,
        helperText:
            'Whether the helper text underneath the field should be displayed and its vertical space preserved.',
        isReversed: true,
        onChange() {
            this.field.isHelperTextHidden = !!this.isHelperTextHidden.value;
        },
    })
    isHelperTextHidden: ui.fields.Checkbox;

    @ui.decorators.textField<NodeBrowserTreePage>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Info message',
        isTransient: true,
        helperText:
            'Indicate additional warning message, rendered as tooltip and blue border. It can also be defined as callback function.',
        onChange() {
            this.field.infoMessage = this.infoMessage.value || '';
        },
    })
    infoMessage: ui.fields.Text;

    @ui.decorators.textField<NodeBrowserTreePage>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Warning message',
        isTransient: true,
        helperText:
            'Indicate additional information, rendered as tooltip and orange border. It can also be defined as callback.',
        onChange() {
            this.field.warningMessage = this.warningMessage.value || '';
        },
    })
    warningMessage: ui.fields.Text;

    @ui.decorators.referenceField<NodeBrowserTreePage, MetaNodeFactory>({
        parent() {
            return this.configurationBlock;
        },
        orderBy: {
            name: 1,
        },
        columns: [
            ui.nestedFields.text({ bind: 'name', title: 'Record type', isHidden: true }),
            ui.nestedFields.text({ bind: 'title', title: 'Node name' }),
            ui.nestedFields.text({
                bind: { package: { name: true } }, // 'package',
                title: 'Package',
                isHidden: true,
            }),
        ],
        node: '@sage/xtrem-metadata/MetaNodeFactory',
        title: 'Node',
        valueField: 'title',
        helperTextField: { package: { name: true } },
        minLookupCharacters: 0,
        isAutoSelectEnabled: true,
        isTransient: true,
        onChange() {
            if (this.node.value) {
                this.field.node = `${this.node.value.package.name}/${this.node.value.name}`;
            } else {
                this.field.node = null;
            }
        },
    })
    node: ui.fields.Reference;

    @ui.decorators.buttonField<NodeBrowserTreePage>({
        parent() {
            return this.configurationBlock;
        },
        map() {
            return 'Focus field';
        },
        onClick() {
            this.field.focus();
        },
    })
    focus: ui.fields.Button;

    @ui.decorators.block<NodeBrowserTreePage>({
        parent() {
            return this.section;
        },
        title: 'Additional examples',
    })
    additionalExamplesBlock: ui.containers.Block;

    @ui.decorators.nodeBrowserTreeField<NodeBrowserTreePage>({
        title: 'Custom fetch items',
        node: '@sage/xtrem-show-case/ShowCaseProvider',
        parent() {
            return this.additionalExamplesBlock;
        },
        async fetchItems(parent) {
            const node = parent.data.type;
            const result = await this.$.graph.raw(
                `
                    {
                    getNodeDetailsList(missingNodeNames: ["${node}"], knownNodeNames: []) {
                        name
                        name
                        title
                        hasAttachments
                        defaultDataType
                        properties {
                        name
                        enumType
                        title
                        canSort
                        canFilter
                        type
                        targetNode
                        dataTypeDetails {
                            name
                            type
                            values {
                            value
                            title
                            }
                        }
                        enumType
                        isCustom
                        dataType
                        targetNode
                        isOnInputType
                        isOnOutputType
                        isMutable
                        isSystemProperty
                        }
                    }
                    }
                `,
                false,
                true,
            );
            return ui.rawNodeDetailsToTreeProperty(parent, result.getNodeDetailsList);
        },
    })
    customFetchItems: ui.fields.NodeBrowserTree;
}
