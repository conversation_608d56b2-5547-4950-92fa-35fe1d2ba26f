import { <PERSON><PERSON>h<PERSON><PERSON>, ShowCaseProduct, ShowCaseProvider } from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import { misc } from '../menu-items/misc';

@ui.decorators.page<PageActions, ShowCaseProvider>({
    headerDropDownActions() {
        return [this.action1, this.menuSeparator1, this.action2, ui.menuSeparator(), this.action3];
    },
    menuItem: misc,
    module: 'show-case',
    navigationPanel: {
        dropdownActions: [
            {
                icon: 'gift',
                id: 'action1',
                onClick() {
                    ui.console.log('Triggered "Action 1"');
                },
                title: 'Action 1',
            },
            ui.menuSeparator({ id: 'separator1' }),
            {
                icon: 'graph',
                id: 'action2',
                onClick() {
                    ui.console.log('Triggered "Action 2"');
                },
                title: 'Action 2',
            },
            ui.menuSeparator({ id: 'separator2' }),
            {
                icon: 'admin',
                id: 'action3',
                onClick() {
                    ui.console.log('Triggered "Action 3"');
                },
                title: 'Action 3',
            },
        ],
        listItem: {
            title: ui.nestedFields.text({ bind: 'textField', title: 'Name' }),
            line2: ui.nestedFields.text({ bind: '_id', title: 'ID' }),
            line3: ui.nestedFields.date({ bind: 'dateField', title: 'Added on' }),
            image: ui.nestedFields.image({ bind: 'logo', title: 'Logo', placeholderMode: 'Initials' }),
        },
    },
    node: '@sage/xtrem-show-case/ShowCaseProvider',
    title: 'Page Actions',
})
export class PageActions extends ui.Page<GraphApi> {
    @ui.decorators.section<PageActions>({})
    section: ui.containers.Section;

    @ui.decorators.block<PageActions>({
        parent() {
            return this.section;
        },
    })
    block: ui.containers.Block;

    @ui.decorators.textField<PageActions>({
        bind: '_id',
        isReadOnly: true,
        parent() {
            return this.block;
        },
        title: 'Id',
        width: 'medium',
    })
    _id: ui.fields.Text;

    @ui.decorators.textField<PageActions>({
        bind: 'textField',
        parent() {
            return this.block;
        },
        title: 'Name',
        width: 'medium',
    })
    textField: ui.fields.Text;

    @ui.decorators.tableField<PageActions, ShowCaseProduct>({
        bind: 'products',
        columns: [
            ui.nestedFields.text({ bind: '_id', title: 'Id' }),
            ui.nestedFields.text({ bind: 'product', title: 'Product' }),
            ui.nestedFields.select({ bind: 'category', title: 'Category' }),
            ui.nestedFields.numeric({ bind: 'qty', title: 'Quantity', scale: 0 }),
        ],
        dropdownActions: [
            {
                icon: 'euro',
                id: 'action1',
                onClick(id: string, data: any) {
                    ui.console.log('Triggered "Action 1"', { id, data });
                },
                title: 'Action 1',
            },
            ui.menuSeparator({ id: 'separator1' }),
            {
                icon: 'analysis',
                id: 'action2',
                onClick(id: string, data: any) {
                    ui.console.log('Triggered "Action 2"', { id, data });
                },
                title: 'Action 2',
            },
            ui.menuSeparator({ id: 'separator2' }),
            {
                icon: 'cash',
                id: 'action3',
                onClick(id: string, data: any) {
                    ui.console.log('Triggered "Action 3"', { id, data });
                },
                title: 'Action 3',
            },
        ],
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        parent() {
            return this.block;
        },
        title: 'Products',
    })
    products: ui.fields.Table<ShowCaseProduct>;

    /* ************ */
    /* Page Actions */
    /* ************ */

    @ui.decorators.pageAction<PageActions>({
        title: 'Action 1',
        icon: 'gift',
    })
    action1: ui.PageAction;

    @ui.decorators.pageAction<PageActions>({
        title: 'Action 2',
        icon: 'graph',
    })
    action2: ui.PageAction;

    @ui.decorators.pageAction<PageActions>({
        title: 'Action 3',
        icon: 'admin',
    })
    action3: ui.PageAction;

    @ui.decorators.pageAction<PageActions>({
        isMenuSeparator: true,
    })
    menuSeparator1: ui.PageAction;
}
