import * as ui from '@sage/xtrem-ui';
import { validation } from '../menu-items/validation';

@ui.decorators.page<ConditionalValidationDisabled>({
    authorizationCode: 'HLPRPNL',
    module: 'show-case',
    category: 'SHOWCASE',
    title: 'Page - Conditional validation disabled',
    isTransient: true,
    validation() {
        if (this.testField1.value !== this.testField2.value) {
            return 'Test field 1 and test field 2 must have the same value.';
        }
        return undefined;
    },
    menuItem: validation,
})
export class ConditionalValidationDisabled extends ui.Page {
    @ui.decorators.section<ConditionalValidationDisabled>({
        isTitleHidden: true,
    })
    controlSection: ui.containers.Section;

    @ui.decorators.block<ConditionalValidationDisabled>({
        title: 'Configuration',
        parent() {
            return this.controlSection;
        },
    })
    controlBlock: ui.containers.Block;

    @ui.decorators.buttonField<ConditionalValidationDisabled>({
        map() {
            return 'Disable Field 1';
        },
        parent() {
            return this.controlBlock;
        },
        onClick() {
            this.testField1.isDisabled = !this.testField1.isDisabled;
        },
        helperText: 'This button toggles disabled status.',
    })
    disableField1Button: ui.fields.Button;

    @ui.decorators.buttonField<ConditionalValidationDisabled>({
        map() {
            return 'Disable Field 2';
        },
        parent() {
            return this.controlBlock;
        },
        onClick() {
            this.testField2.isDisabled = !this.testField2.isDisabled;
        },
        helperText: 'This button toggles disabled status.',
    })
    disableField2Button: ui.fields.Button;

    @ui.decorators.buttonField<ConditionalValidationDisabled>({
        map() {
            return 'Disable Field 3';
        },
        parent() {
            return this.controlBlock;
        },
        onClick() {
            this.testField3.isDisabled = !this.testField3.isDisabled;
        },
        helperText: 'This button toggles disabled status.',
    })
    disableField3Button: ui.fields.Button;

    @ui.decorators.separatorField<ConditionalValidationDisabled>({
        parent() {
            return this.controlBlock;
        },
        isFullWidth: true,
    })
    sep2: ui.fields.Separator;

    @ui.decorators.buttonField<ConditionalValidationDisabled>({
        map() {
            return 'Disable Test Section';
        },
        parent() {
            return this.controlBlock;
        },
        onClick() {
            this.testSection.isDisabled = !this.testSection.isDisabled;
        },
        helperText: 'This button toggles disabled status.',
    })
    disableTestSection: ui.fields.Button;

    @ui.decorators.buttonField<ConditionalValidationDisabled>({
        map() {
            return 'Disable Test Block 1';
        },
        onClick() {
            this.testBlock1.isDisabled = !this.testBlock1.isDisabled;
        },
        parent() {
            return this.controlBlock;
        },
        helperText: 'This button toggles disabled status.',
    })
    disableTestBlock1: ui.fields.Button;

    @ui.decorators.buttonField<ConditionalValidationDisabled>({
        map() {
            return 'Disable Test Block 2';
        },
        onClick() {
            this.testBlock2.isDisabled = !this.testBlock2.isDisabled;
        },
        parent() {
            return this.controlBlock;
        },
        helperText: 'This button toggles disabled status.',
    })
    disableTestBlock2: ui.fields.Button;

    @ui.decorators.separatorField<ConditionalValidationDisabled>({
        parent() {
            return this.controlBlock;
        },
        isFullWidth: true,
    })
    sep3: ui.fields.Separator;

    @ui.decorators.buttonField<ConditionalValidationDisabled>({
        map() {
            return 'Validate Test Block 1';
        },
        async onClick() {
            const result = await this.testBlock1.validate();
            this.$.dialog.message('info', 'Validation result', result.length === 0 ? 'All good.' : result.join('\n'));
        },
        parent() {
            return this.controlBlock;
        },
    })
    validateBlock1Button: ui.fields.Button;

    @ui.decorators.buttonField<ConditionalValidationDisabled>({
        map() {
            return 'Validate Test Block 2';
        },
        async onClick() {
            const result = await this.testBlock2.validate();
            this.$.dialog.message('info', 'Validation result', result.length === 0 ? 'All good.' : result.join('\n'));
        },
        parent() {
            return this.controlBlock;
        },
    })
    validateBlock2Button: ui.fields.Button;

    @ui.decorators.buttonField<ConditionalValidationDisabled>({
        map() {
            return 'Validate Test Section';
        },
        async onClick() {
            const result = await this.testSection.validate();
            this.$.dialog.message('info', 'Validation result', result.length === 0 ? 'All good.' : result.join('\n'));
        },
        parent() {
            return this.controlBlock;
        },
    })
    validateSectionButton: ui.fields.Button;

    @ui.decorators.buttonField<ConditionalValidationDisabled>({
        map() {
            return 'Validate Page';
        },
        async onClick() {
            const result = await this.$.page.validate();
            this.$.dialog.message('info', 'Validation result', result.length === 0 ? 'All good.' : result.join('\n'));
        },
        parent() {
            return this.controlBlock;
        },
    })
    validatePageButton: ui.fields.Button;

    @ui.decorators.section<ConditionalValidationDisabled>({
        title: 'Test Section',
    })
    testSection: ui.containers.Section;

    @ui.decorators.block<ConditionalValidationDisabled>({
        title: 'Test Block 1',
        parent() {
            return this.testSection;
        },
    })
    testBlock1: ui.containers.Block;

    @ui.decorators.block<ConditionalValidationDisabled>({
        title: 'Test Block 2',
        parent() {
            return this.testSection;
        },
    })
    testBlock2: ui.containers.Block;

    @ui.decorators.textField<ConditionalValidationDisabled>({
        title: 'Test Field 1',
        isMandatory: true,
        helperText: 'This field is mandatory',
        parent() {
            return this.testBlock1;
        },
    })
    testField1: ui.fields.Text;

    @ui.decorators.textField<ConditionalValidationDisabled>({
        title: 'Test Field 2',
        isMandatory: true,
        helperText: 'This field is mandatory',
        parent() {
            return this.testBlock1;
        },
    })
    testField2: ui.fields.Text;

    @ui.decorators.textField<ConditionalValidationDisabled>({
        title: 'Test Field 3',
        isMandatory: true,
        helperText: 'This field is mandatory',
        parent() {
            return this.testBlock2;
        },
    })
    testField3: ui.fields.Text;
}
