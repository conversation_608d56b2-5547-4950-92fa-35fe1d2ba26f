import * as ui from '@sage/xtrem-ui';
import { fields } from '../menu-items/fields';

@ui.decorators.page<FileDeposit>({
    menuItem: fields,
    title: 'File Deposit',
    subtitle: 'Fields',
    isTransient: true,
    headerSection() {
        return this.headerSection;
    },
})
export class FileDeposit extends ui.Page {
    @ui.decorators.section<FileDeposit>({
        title: 'File Deposit',
        isTitleHidden: true,
    })
    headerSection: ui.containers.Section;

    @ui.decorators.block<FileDeposit>({
        parent() {
            return this.headerSection;
        },
        title: 'Introduction',
        width: 'medium',
    })
    introductionBlock: ui.containers.Block;

    @ui.decorators.staticContentField<FileDeposit>({
        parent() {
            return this.introductionBlock;
        },
        isTitleHidden: true,
        isFullWidth: true,
        isMarkdown: true,
        content:
            'The file deposit file is designed to handle larger files. The files uploaded through it will be deposited to an external storage system such as AWS S3. The file deposit filed can only be bound to vital reference properties, that refer to a node that implements a specific interface containing the following properties:\n- `downloadUrl`\n- `uploadUrl`\n- `filename`\n - `mimeType`\n- `lastModified`\n - `contentLength`\n- `status`',
    })
    description: ui.fields.StaticContent;

    @ui.decorators.section<FileDeposit>({
        title: 'File Deposit',
        isTitleHidden: true,
    })
    section: ui.containers.Section;

    @ui.decorators.block<FileDeposit>({
        parent() {
            return this.headerSection;
        },
        title: 'Field example',
        width: 'medium',
    })
    fieldBlock: ui.containers.Block;

    @ui.decorators.fileDepositField<FileDeposit>({
        parent() {
            return this.fieldBlock;
        },
        node: '@sage/xtrem-upload/UploadedFile',
        onClick() {
            this.clickTriggered.isHidden = false;
            setTimeout(() => {
                this.clickTriggered.isHidden = true;
            }, 5000);
        },
        onChange() {
            this.changeTriggered.isHidden = false;
            setTimeout(() => {
                this.changeTriggered.isHidden = true;
            }, 5000);
        },
        kind: 'upload',
    })
    field: ui.fields.FileDeposit;

    @ui.decorators.labelField<FileDeposit>({
        parent() {
            return this.fieldBlock;
        },
        isHidden: true,
        map() {
            return 'Change was triggered';
        },
    })
    changeTriggered: ui.fields.Label;

    @ui.decorators.labelField<FileDeposit>({
        parent() {
            return this.fieldBlock;
        },
        isHidden: true,
        map() {
            return 'Click was triggered';
        },
    })
    clickTriggered: ui.fields.Label;

    @ui.decorators.linkField<FileDeposit>({
        parent() {
            return this.fieldBlock;
        },
        title: 'Source code',
        isFullWidth: true,
        map() {
            return ui.localize('@sage/xtrem-show-case/check-source-code', 'Check it on GitHub');
        },
        page: 'https://github.com/Sage-ERP-X3/xtrem/blob/master/platform/show-case/xtrem-show-case/lib/pages/file-deposit.ts',
    })
    sourceCodeLink: ui.fields.Link;

    @ui.decorators.block<FileDeposit>({
        parent() {
            return this.section;
        },
        title: 'Configuration',
    })
    configurationBlock: ui.containers.Block;

    @ui.decorators.checkboxField<FileDeposit>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is disabled',
        helperText:
            'Determines whether the field is disabled or not. It can also be defined as callback function that returns a boolean.',
        isFullWidth: true,
        isReversed: true,
        onChange() {
            this.field.isDisabled = !!this.isDisabled.value;
        },
    })
    isDisabled: ui.fields.Checkbox;

    @ui.decorators.checkboxField<FileDeposit>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is read only',
        helperText:
            'Whether the field is editable (isReadOnly = false) or not (isReadOnly = true). The difference with disabled is that isReadOnly suggests that the field is never editable. It can be defined as a boolean, or conditionally by a callback that returns a boolean.',
        isFullWidth: true,
        isReversed: true,
        onChange() {
            this.field.isReadOnly = !!this.isReadOnly.value;
        },
    })
    isReadOnly: ui.fields.Checkbox;

    @ui.decorators.checkboxField<FileDeposit>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is hidden',
        helperText: 'Determines whether the field is displayed or not.',
        isFullWidth: true,
        isReversed: true,
        onChange() {
            this.field.isHidden = !!this.isHidden.value;
        },
    })
    isHidden: ui.fields.Checkbox;

    @ui.decorators.textField<FileDeposit>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Title',
        isFullWidth: true,
        helperText:
            'The title that is displayed above the field. The title can be provided as a string, or a callback function returning a string. When declared as a callback within the column of a nested grid, the column id is provided as a parameter. It is automatically picked up by the i18n engine and externalized for translation.',
        onChange() {
            this.field.title = this.title.value || '';
        },
    })
    title: ui.fields.Text;

    @ui.decorators.checkboxField<FileDeposit>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is title hidden',
        isFullWidth: true,
        helperText: 'Whether the field title above the field should be displayed and its vertical space preserved.',
        isReversed: true,
        onChange() {
            this.field.isTitleHidden = !!this.isTitleHidden.value;
        },
    })
    isTitleHidden: ui.fields.Checkbox;

    @ui.decorators.textField<FileDeposit>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Helper text',
        helperText:
            'The helper text that is displayed above the field. It is automatically picked up by the i18n engine and externalized.',
        isFullWidth: true,
        onChange() {
            this.field.helperText = this.helperText.value || '';
        },
    })
    helperText: ui.fields.Text;

    @ui.decorators.checkboxField<FileDeposit>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is helper text hidden',
        helperText:
            'Whether the helper text underneath the field should be displayed and its vertical space preserved.',
        isFullWidth: true,
        isReversed: true,
        onChange() {
            this.field.isHelperTextHidden = !!this.isHelperTextHidden.value;
        },
    })
    isHelperTextHidden: ui.fields.Checkbox;

    @ui.decorators.checkboxField<FileDeposit>({
        parent() {
            return this.configurationBlock;
        },
        title: 'Is mandatory',
        helperText:
            ' Makes the field mandatory, empty values will raise an error message. It can also be defined as callback function that returns a boolean.',
        isFullWidth: true,
        isReversed: true,
        onChange() {
            this.field.isMandatory = !!this.isMandatory.value;
        },
    })
    isMandatory: ui.fields.Checkbox;

    @ui.decorators.textField<FileDeposit>({
        parent() {
            return this.additionalBlock;
        },
        title: 'Info message',
        helperText:
            'Indicate additional warning message, rendered as tooltip and blue border. It can also be defined as callback function.',
        isFullWidth: true,
        onChange() {
            this.field.infoMessage = this.infoMessage.value || '';
        },
    })
    infoMessage: ui.fields.Text;

    @ui.decorators.textField<FileDeposit>({
        parent() {
            return this.additionalBlock;
        },
        title: 'Warning message',
        helperText:
            'Indicate additional information, rendered as tooltip and orange border. It can also be defined as callback.',
        isFullWidth: true,
        onChange() {
            this.field.warningMessage = this.warningMessage.value || '';
        },
    })
    warningMessage: ui.fields.Text;

    @ui.decorators.buttonField<FileDeposit>({
        parent() {
            return this.configurationBlock;
        },
        map() {
            return 'Focus field';
        },
        onClick() {
            this.field.focus();
        },
    })
    focus: ui.fields.Button;

    @ui.decorators.buttonField<FileDeposit>({
        parent() {
            return this.configurationBlock;
        },
        map() {
            return 'Validate field';
        },
        async onClick() {
            await this.field.validate();
        },
    })
    validate: ui.fields.Button;

    @ui.decorators.block<FileDeposit>({
        parent() {
            return this.section;
        },
        title: 'Events',
    })
    eventsBlock: ui.containers.Block;

    @ui.decorators.staticContentField<FileDeposit>({
        parent() {
            return this.eventsBlock;
        },
        isTitleHidden: true,
        isFullWidth: true,
        isMarkdown: true,
        content:
            '-   **onClick**: Triggered when any parts of the field is clicked, no arguments provided.\n-   **onChange**: Triggered when the field value changed and the focus is about to move away from the field, no arguments provided.\n-   **onError**: Handles errors thrown from the callback functions.',
    })
    eventsDescription: ui.fields.StaticContent;

    @ui.decorators.block<FileDeposit>({
        parent() {
            return this.section;
        },
        title: 'Runtime functions',
    })
    runtimeFunctionsBlock: ui.containers.Block;

    @ui.decorators.staticContentField<FileDeposit>({
        parent() {
            return this.runtimeFunctionsBlock;
        },
        isTitleHidden: true,
        isFullWidth: true,
        isMarkdown: true,
        content:
            "-   **focus()**: Moves the focus to the field.\n-   **getNextField(isFocusable)**: Returns the next field instance. The order is calculated by the page prototype. If the isFocusable argument is set to true, it returns the next visible, enabled and non read-only field. It only considers the committed page state, so `commitValueAndPropertyChanges` call might be required beforehand to get the expected result.\n-   **refresh()**: Refetches the field's value from the server and updates it on the screen, only for non-transient pages.\n-   **validate()**: Triggers the field validation rules. Since the validation rules might be asynchronous, this method returns a promise that must be awaited to get the validation result\n-   **validateWithDetails()**: In addition to the functionality of `validate` it returns more details, including the rule that failed and where applicable, the row ID and colum ID.\n-   **fetchDefault(skipSet)**: Force re-fetches default value for the field. If the `skipSet` flag is set to true, it returns the default values but not apply them to the screen.\n-   **isDirty()**: Sets or gets the dirty state of the field.",
    })
    runtimeFunctionDescription: ui.fields.StaticContent;

    /* Additional examples */

    @ui.decorators.block<FileDeposit>({
        parent() {
            return this.section;
        },
        title: 'Additional examples',
    })
    additionalBlock: ui.containers.Block;

    @ui.decorators.fileDepositField<FileDeposit>({
        parent() {
            return this.additionalBlock;
        },
        title: 'Mandatory',
        isMandatory: true,
        node: '@sage/xtrem-upload/UploadedFile',
        kind: 'upload',
    })
    mandatory: ui.fields.FileDeposit;

    @ui.decorators.fileDepositField<FileDeposit>({
        parent() {
            return this.additionalBlock;
        },
        title: 'Full width',
        isFullWidth: true,
        node: '@sage/xtrem-upload/UploadedFile',
        kind: 'upload',
    })
    fullWidth: ui.fields.FileDeposit;

    @ui.decorators.block<FileDeposit>({
        parent() {
            return this.section;
        },
        title: 'Dynamic validations',
    })
    dynamicValidations: ui.containers.Block;

    @ui.decorators.fileDepositField<FileDeposit>({
        parent() {
            return this.dynamicValidations;
        },
        node: '@sage/xtrem-upload/UploadedFile',
        title: 'Mandatory By Setter',
        kind: 'upload',
    })
    mandatoryBySetter: ui.fields.FileDeposit;

    @ui.decorators.switchField<FileDeposit>({
        parent() {
            return this.dynamicValidations;
        },
        title: 'Is Mandatory?',
        onChange() {
            this.mandatoryBySetter.isMandatory = this.mandatoryBySetterSwitch.value || undefined;
        },
    })
    mandatoryBySetterSwitch: ui.fields.Switch;

    @ui.decorators.separatorField<FileDeposit>({
        parent() {
            return this.dynamicValidations;
        },
        isFullWidth: true,
    })
    mandatorySeparator1: ui.fields.Separator;

    @ui.decorators.fileDepositField<FileDeposit>({
        parent() {
            return this.dynamicValidations;
        },
        title: 'Mandatory By Callback',
        node: '@sage/xtrem-upload/UploadedFile',
        isMandatory() {
            return this.mandatoryByCallbackSwitch.value || false;
        },
        kind: 'upload',
    })
    mandatoryByCallback: ui.fields.FileDeposit;

    @ui.decorators.switchField<FileDeposit>({
        parent() {
            return this.dynamicValidations;
        },
        title: 'Is Mandatory?',
    })
    mandatoryByCallbackSwitch: ui.fields.Switch;

    @ui.decorators.separatorField<FileDeposit>({
        parent() {
            return this.dynamicValidations;
        },
        isFullWidth: true,
    })
    mandatorySeparator2: ui.fields.Separator;

    @ui.decorators.fileDepositField<FileDeposit>({
        parent() {
            return this.additionalBlock;
        },
        title: 'With warning message',
        warningMessage: 'Wow, warning!',
        node: '@sage/xtrem-upload/UploadedFile',
        kind: 'upload',
    })
    warningMessageField: ui.fields.FileDeposit;

    @ui.decorators.fileDepositField<FileDeposit>({
        parent() {
            return this.additionalBlock;
        },
        title: 'With info message',
        infoMessage: 'Wow, warning!',
        node: '@sage/xtrem-upload/UploadedFile',
        kind: 'upload',
    })
    infoMessageField: ui.fields.FileDeposit;

    @ui.decorators.fileDepositField<FileDeposit>({
        parent() {
            return this.additionalBlock;
        },
        title: 'With info and warning message',
        warningMessage: 'Wow, warning!',
        infoMessage: 'You should not see this',
        node: '@sage/xtrem-upload/UploadedFile',
        kind: 'upload',
    })
    infoAndWarningMessageField: ui.fields.FileDeposit;

    @ui.decorators.fileDepositField<FileDeposit>({
        parent() {
            return this.additionalBlock;
        },
        isMandatory: true,
        title: 'Info, warning and validation',
        warningMessage: 'Wow, warning!',
        infoMessage: 'You should not see this',
        helperText: 'This field is mandatory too.',
        node: '@sage/xtrem-upload/UploadedFile',
        kind: 'upload',
    })
    infoAndWarningMessageMandatoryField: ui.fields.FileDeposit;
}
