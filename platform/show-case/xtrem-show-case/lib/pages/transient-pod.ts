import { Graph<PERSON><PERSON> } from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import { podCollection } from '../menu-items/_index';

@ui.decorators.page<TransientPod>({
    authorizationCode: 'BSCFLDS',
    module: 'show-case',
    isTransient: true,
    category: 'SHOWCASE',
    mode: 'tabs',
    menuItem: podCollection,
    title: 'Pod Collection (Transient)',
})
export class TransientPod extends ui.Page<GraphApi> {
    @ui.decorators.section<TransientPod>({
        isTitleHidden: true,
        title: 'Basic',
    })
    section: ui.containers.Section;

    @ui.decorators.block<TransientPod>({
        parent() {
            return this.section;
        },
        title: 'Field example',
    })
    fieldBlock: ui.containers.Block;

    @ui.decorators.buttonField<TransientPod>({
        map() {
            return 'Load data';
        },
        parent() {
            return this.fieldBlock;
        },
        async onClick() {
            const result = await this.$.graph
                .node('@sage/xtrem-show-case/ShowCaseProduct')
                .query(
                    ui.queryUtils.edgesSelector(
                        {
                            _id: true,
                            amount: true,
                            product: true,
                            description: true,
                            hotProduct: true,
                            qty: true,
                            listPrice: true,
                            progress: true,
                            netPrice: true,
                            category: true,
                            imageField: { value: true },
                        },
                        { first: 6 },
                    ),
                )
                .execute();
            this.largeSelectablePodField.value = result.edges.map(e => e.node);
            this.smallReadOnlyPodField.value = result.edges.map(e => e.node);
        },
    })
    loadButton: ui.fields.Button;

    calculateSelectedTotal() {
        const selectedRecords = this.largeSelectablePodField.selectedRecords || [];
        const selectedTotal = (this.largeSelectablePodField.value || [])
            .filter(v => selectedRecords.indexOf(v._id) !== -1)
            .map(v => parseFloat(v.amount))
            .reduce((prev, v) => prev + v, 0);

        this.tableSelectedTotal.value = selectedTotal;
    }

    @ui.decorators.numericField<TransientPod>({
        parent() {
            return this.fieldBlock;
        },
        title: 'Selected items total',
        isReadOnly: true,
        scale: 2,
        prefix: '$',
        isTransient: true,
    })
    tableSelectedTotal: ui.fields.Numeric;

    @ui.decorators.numericField<TransientPod>({
        parent() {
            return this.fieldBlock;
        },
        isTransient: true,
        title: 'Items total',
        isReadOnly: true,
        scale: 2,
        prefix: '$',
    })
    tableSampleTotal: ui.fields.Numeric;

    @ui.decorators.podCollectionField<TransientPod>({
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        onRowSelected() {
            this.calculateSelectedTotal();
        },
        onRowUnselected() {
            this.calculateSelectedTotal();
        },
        recordTitle(value: any, rowData) {
            return `Item ${rowData._id}`;
        },
        title: 'Large selectable, selectable pods',
        columns: [
            ui.nestedFields.text({
                bind: 'product',
                title: 'Product',
                isReadOnly(product: any, rowData: any) {
                    return !!rowData.hotProduct;
                },
            }),
            ui.nestedFields.checkbox({
                bind: 'hotProduct',
                title: 'Hot',
            }),
            ui.nestedFields.progress({
                bind: 'progress',
                isFullWidth: true,
                title: 'Progress',
            }),
        ],
        orderBy: {
            _id: 1,
        },
        parent() {
            return this.fieldBlock;
        },
    })
    largeSelectablePodField: ui.fields.PodCollection;

    @ui.decorators.section<TransientPod>({
        title: 'This pod is rendered directly onto the section',
    })
    section2: ui.containers.Section;

    @ui.decorators.podCollectionField<TransientPod>({
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        recordTitle: ui.nestedFields.numeric({ bind: 'netPrice', prefix: '$', scale: 2 }),
        isReadOnly: true,
        recordWidth: 'extra-small',
        canRemoveRecord: true,
        dropdownActions: [
            {
                icon: 'add',
                title: 'Add',
                isDisabled() {
                    return false;
                },
                onClick(rowId: any, data: any) {
                    this.$.dialog.message(
                        'info',
                        'Add Row Action was clicked',
                        `Product: ${data.product} Row: ${rowId}`,
                    );
                },
            },
        ],
        columns: [
            ui.nestedFields.text({
                bind: 'product',
                title: 'Product',
            }),
            ui.nestedFields.text({
                bind: 'description',
                title: 'Description',
            }),
            ui.nestedFields.image({
                bind: 'imageField',
                title: 'This is how it looks like',
                isReadOnly: true,
            }),
        ],
        orderBy: {
            _id: 1,
        },
        parent() {
            return this.section2;
        },
    })
    smallReadOnlyPodField: ui.fields.PodCollection;
}
