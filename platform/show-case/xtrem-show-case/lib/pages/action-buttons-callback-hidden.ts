import * as ui from '@sage/xtrem-ui';
import { fields } from '../menu-items/fields';

@ui.decorators.page<ActionButtonsCallbackHidden>({
    authorizationCode: 'ACTINBTNS',
    module: 'show-case',
    category: 'SHOWCASE',
    title: 'Page - Action Buttons hidden callback',
    isTransient: true,
    createAction() {
        return this.createCrud;
    },
    headerDropDownActions() {
        return [this.deleteCrud];
    },
    businessActions() {
        return [this.businessAction1, this.businessAction2, this.saveCrud];
    },
    menuItem: fields,
})
export class ActionButtonsCallbackHidden extends ui.Page {
    @ui.decorators.pageAction<ActionButtonsCallbackHidden>({
        title: 'Save',
        onClick() {
            this.resultField.value = ui.localize('@sage/xtrem-show-case/crud-button-save', 'Save CRUD button');
        },
        isHidden() {
            return this.actionsVisible.value;
        },
    })
    saveCrud: ui.PageAction;

    @ui.decorators.pageAction<ActionButtonsCallbackHidden>({
        onClick() {
            this.resultField.value = ui.localize('@sage/xtrem-show-case/crud-button-create', 'Create CRUD button');
        },
    })
    createCrud: ui.PageAction;

    @ui.decorators.pageAction<ActionButtonsCallbackHidden>({
        title: 'Delete',
        icon: 'bin',
        isDestructive: true,
        onClick() {
            this.resultField.value = ui.localize('@sage/xtrem-show-case/crud-button-delete', 'Delete CRUD button');
        },
    })
    deleteCrud: ui.PageAction;

    @ui.decorators.pageAction<ActionButtonsCallbackHidden>({
        onClick() {
            this.resultField.value = ui.localize('@sage/xtrem-show-case/crud-button-close', 'Close CRUD button');
        },
        isHidden() {
            return this.actionsVisible.value;
        },
    })
    closeCrud: ui.PageAction;

    @ui.decorators.pageAction<ActionButtonsCallbackHidden>({
        title: 'Business action 1',
        onClick() {
            this.resultField.value = ui.localize('@sage/xtrem-show-case/business-action', 'Business action {{0}}', [1]);
        },
    })
    businessAction1: ui.PageAction;

    @ui.decorators.pageAction<ActionButtonsCallbackHidden>({
        title: 'Business action 2',
        onClick() {
            this.resultField.value = ui.localize('@sage/xtrem-show-case/business-action', 'Business action {{0}}', [2]);
        },
        isHidden() {
            return this.actionsVisible.value;
        },
    })
    businessAction2: ui.PageAction;

    @ui.decorators.section<ActionButtonsCallbackHidden>({
        title: 'About this page',
    })
    mainSection: ui.containers.Section;

    @ui.decorators.block<ActionButtonsCallbackHidden>({
        parent() {
            return this.mainSection;
        },
        title: '',
    })
    mainBlock: ui.containers.Block;

    @ui.decorators.textField<ActionButtonsCallbackHidden>({
        title: 'Selected action',
        parent() {
            return this.mainBlock;
        },
        isReadOnly: true,
        isFullWidth: true,
    })
    resultField: ui.fields.Text;

    @ui.decorators.section<ActionButtonsCallbackHidden>({
        title: 'Controlling Buttons',
    })
    controlSection: ui.containers.Section;

    @ui.decorators.block<ActionButtonsCallbackHidden>({
        parent() {
            return this.controlSection;
        },
        title: '',
    })
    controlBlock: ui.containers.Block;

    @ui.decorators.switchField<ActionButtonsCallbackHidden>({
        title: 'Some actions hidden?',
        parent() {
            return this.controlBlock;
        },
    })
    actionsVisible: ui.fields.Switch;
}
