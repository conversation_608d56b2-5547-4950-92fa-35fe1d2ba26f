import { ShowCaseProduct } from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import { misc } from '../menu-items/misc';

@ui.decorators.page<Focus>({
    authorizationCode: 'FOCUS',
    module: 'show-case',
    title: 'Fields focus',
    category: 'SHOWCASE',
    isTransient: true,
    onLoad() {
        this.textField.focus();
    },
    menuItem: misc,
})
export class Focus extends ui.Page {
    @ui.decorators.section<Focus>({
        isTitleHidden: true,
    })
    section: ui.containers.Section;

    @ui.decorators.block<Focus>({
        parent() {
            return this.section;
        },
    })
    fieldBlock: ui.containers.Block;

    /// ///////////////////////////////////////////////////////////

    @ui.decorators.buttonField<Focus>({
        parent() {
            return this.fieldBlock;
        },
        title: 'Button Field',
        map() {
            return 'Just a button';
        },
        onClick() {
            this.$.showToast("This button doesn't do much :-(");
        },
    })
    buttonField: ui.fields.Button;

    @ui.decorators.buttonField<Focus>({
        parent() {
            return this.fieldBlock;
        },
        map() {
            return 'Focus';
        },
        onClick() {
            this.buttonField.focus();
        },
    })
    buttonFieldButton: ui.fields.Button;

    @ui.decorators.separatorField<Focus>({
        parent() {
            return this.fieldBlock;
        },
        isFullWidth: true,
    })
    buttonFieldSeparator: ui.fields.Separator;

    /// ///////////////////////////////////////////////////////////

    @ui.decorators.checkboxField<Focus>({
        parent() {
            return this.fieldBlock;
        },
        title: 'Checkbox Field',
    })
    checkboxField: ui.fields.Checkbox;

    @ui.decorators.buttonField<Focus>({
        parent() {
            return this.fieldBlock;
        },
        map() {
            return 'Focus';
        },
        onClick() {
            this.checkboxField.focus();
        },
    })
    checkboxFieldButton: ui.fields.Button;

    @ui.decorators.separatorField<Focus>({
        parent() {
            return this.fieldBlock;
        },
        isFullWidth: true,
    })
    checkboxFieldSeparator: ui.fields.Separator;

    /// ///////////////////////////////////////////////////////////

    @ui.decorators.dateField<Focus>({
        parent() {
            return this.fieldBlock;
        },
        title: 'Date Field',
    })
    dateField: ui.fields.Date;

    @ui.decorators.buttonField<Focus>({
        parent() {
            return this.fieldBlock;
        },
        map() {
            return 'Focus';
        },
        onClick() {
            this.dateField.focus();
        },
    })
    dateFieldButton: ui.fields.Button;

    @ui.decorators.separatorField<Focus>({
        parent() {
            return this.fieldBlock;
        },
        isFullWidth: true,
    })
    dateFieldSeparator: ui.fields.Separator;

    /// ///////////////////////////////////////////////////////////

    @ui.decorators.linkField<Focus>({
        parent() {
            return this.fieldBlock;
        },
        title: 'Link Field',
        map() {
            return 'Just a link';
        },
        onClick() {
            this.$.showToast("This link doesn't do much :-(");
        },
    })
    linkField: ui.fields.Link;

    @ui.decorators.buttonField<Focus>({
        parent() {
            return this.fieldBlock;
        },
        map() {
            return 'Focus';
        },
        onClick() {
            this.linkField.focus();
        },
    })
    linkFieldButton: ui.fields.Button;

    @ui.decorators.separatorField<Focus>({
        parent() {
            return this.fieldBlock;
        },
        isFullWidth: true,
    })
    linkFieldSeparator: ui.fields.Separator;

    /// ///////////////////////////////////////////////////////////

    @ui.decorators.numericField<Focus>({
        parent() {
            return this.fieldBlock;
        },
        title: 'Numeric Field',
    })
    numericField: ui.fields.Numeric;

    @ui.decorators.buttonField<Focus>({
        parent() {
            return this.fieldBlock;
        },
        map() {
            return 'Focus';
        },
        onClick() {
            this.numericField.focus();
        },
    })
    numericFieldButton: ui.fields.Button;

    @ui.decorators.separatorField<Focus>({
        parent() {
            return this.fieldBlock;
        },
        isFullWidth: true,
    })
    numericFieldSeparator: ui.fields.Separator;

    /// ///////////////////////////////////////////////////////////

    @ui.decorators.referenceField<Focus, ShowCaseProduct>({
        parent() {
            return this.fieldBlock;
        },
        title: 'Reference Field',
        helperTextField: 'description',
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        minLookupCharacters: 0,
        valueField: 'product',
    })
    referenceField: ui.fields.Reference;

    @ui.decorators.buttonField<Focus>({
        parent() {
            return this.fieldBlock;
        },
        map() {
            return 'Focus';
        },
        onClick() {
            this.referenceField.focus();
        },
    })
    referenceFieldButton: ui.fields.Button;

    @ui.decorators.separatorField<Focus>({
        parent() {
            return this.fieldBlock;
        },
        isFullWidth: true,
    })
    referenceFieldSeparator: ui.fields.Separator;

    /// ///////////////////////////////////////////////////////////

    @ui.decorators.selectField<Focus>({
        parent() {
            return this.fieldBlock;
        },
        title: 'Select Field',
        optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
    })
    selectField: ui.fields.Select;

    @ui.decorators.buttonField<Focus>({
        parent() {
            return this.fieldBlock;
        },
        map() {
            return 'Focus';
        },
        onClick() {
            this.selectField.focus();
        },
    })
    selectFieldButton: ui.fields.Button;

    @ui.decorators.separatorField<Focus>({
        parent() {
            return this.fieldBlock;
        },
        isFullWidth: true,
    })
    selectFieldSeparator: ui.fields.Separator;

    /// ///////////////////////////////////////////////////////////

    @ui.decorators.textField<Focus>({
        parent() {
            return this.fieldBlock;
        },
        title: 'Text Field',
    })
    textField: ui.fields.Text;

    @ui.decorators.buttonField<Focus>({
        parent() {
            return this.fieldBlock;
        },
        map() {
            return 'Focus';
        },
        onClick() {
            this.textField.focus();
        },
    })
    textFieldButton: ui.fields.Button;

    @ui.decorators.separatorField<Focus>({
        parent() {
            return this.fieldBlock;
        },
        isFullWidth: true,
    })
    textFieldSeparator: ui.fields.Separator;

    /// ///////////////////////////////////////////////////////////

    @ui.decorators.textAreaField<Focus>({
        parent() {
            return this.fieldBlock;
        },
        title: 'Text area Field',
    })
    textAreaField: ui.fields.TextArea;

    @ui.decorators.buttonField<Focus>({
        parent() {
            return this.fieldBlock;
        },
        map() {
            return 'Focus';
        },
        onClick() {
            this.textAreaField.focus();
        },
    })
    textAreaFieldButton: ui.fields.Button;

    @ui.decorators.separatorField<Focus>({
        parent() {
            return this.fieldBlock;
        },
        isFullWidth: true,
    })
    textAreaFieldSeparator: ui.fields.Separator;
}
