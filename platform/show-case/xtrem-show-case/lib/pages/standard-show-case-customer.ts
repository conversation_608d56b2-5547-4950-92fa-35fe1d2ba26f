import { Graph<PERSON><PERSON>, ShowCaseCustomer as ShowCaseCustomerNode } from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import { applicationPages } from '../menu-items/application-pages';
import { setOrderOfPageHeaderDropDownActions } from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';

@ui.decorators.page<StandardShowCaseCustomer, ShowCaseCustomerNode>({
    authorizationCode: 'SHCPRVD',
    category: 'SHOWCASE',
    menuItem: applicationPages,
    objectTypeSingular: 'Customer',
    objectTypePlural: 'Customers',
    mode: 'tabs',
    idField() {
        return this.name;
    },
    businessActions() {
        return [this.$standardCancelAction, this.$standardSaveAction];
    },
    headerQuickActions() {
        return [this.$standardDuplicateAction];
    },
    headerDropDownActions() {
        return setOrderOfPageHeaderDropDownActions<StandardShowCaseCustomer>({
            remove: [this.$standardDeleteAction],
            dropDownBusinessActions: [this.$standardOpenRecordHistoryAction],
        });
    },
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.text({ bind: 'name', title: 'Name' }),
            line2: ui.nestedFields.text({ bind: 'email', title: 'Email' }),
        },
    },
    node: '@sage/xtrem-show-case/ShowCaseCustomer',
    title: 'Standard customer page',
    priority: 300,
    headerSection() {
        return this.headerSection;
    },
})
export class StandardShowCaseCustomer extends ui.Page<GraphApi> {
    @ui.decorators.section<StandardShowCaseCustomer>({
        title: 'Header',
        isTitleHidden: true,
    })
    headerSection: ui.containers.Section;

    @ui.decorators.block<StandardShowCaseCustomer>({
        parent() {
            return this.headerSection;
        },
    })
    headerBlock: ui.containers.Block;

    @ui.decorators.textField<StandardShowCaseCustomer>({
        bind: '_id',
        isReadOnly: true,
        parent() {
            return this.headerBlock;
        },
        title: 'Id',
    })
    id: ui.fields.Text;

    @ui.decorators.textField<StandardShowCaseCustomer>({
        bind: 'name',
        parent() {
            return this.headerBlock;
        },
        title: 'Name',
    })
    name: ui.fields.Text;
}
