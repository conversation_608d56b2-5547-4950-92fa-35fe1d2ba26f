import { GraphApi, ShowCaseProduct } from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import { misc } from '../menu-items/misc';

@ui.decorators.page<PageDirtyState, ShowCaseProduct>({
    authorizationCode: 'SHCPRDT',
    module: 'show-case',
    title: 'Page - Dirty state on fields',
    node: '@sage/xtrem-show-case/ShowCaseProduct',
    category: 'SHOWCASE',
    menuItem: misc,
    onDirtyStateUpdated(isPageDirty: boolean) {
        if (isPageDirty) {
            this.status.value = 'The page is dirty';
        } else {
            this.status.value = 'The page is clean';
        }
    },
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.text({ bind: 'product' }),
            titleRight: ui.nestedFields.text({ bind: '_id' }),
        },
    },
    createAction() {
        return this.$standardNewAction;
    },
    businessActions() {
        return [this.refreshPage, this.$standardSaveAction];
    },
    headerDropDownActions() {
        return [this.$standardDeleteAction];
    },
})
export class PageDirtyState extends ui.Page<GraphApi> {
    @ui.decorators.section<PageDirtyState>({
        isTitleHidden: true,
    })
    section: ui.containers.Section;

    @ui.decorators.block<PageDirtyState>({
        title: 'Status',
        parent() {
            return this.section;
        },
    })
    statusBlock: ui.containers.Block;

    @ui.decorators.textField<PageDirtyState>({
        parent() {
            return this.statusBlock;
        },
        isTransient: true,
        isReadOnly: true,
        isFullWidth: true,
        isTitleHidden: true,
    })
    status: ui.fields.Text;

    @ui.decorators.block<PageDirtyState>({
        parent() {
            return this.section;
        },
    })
    block: ui.containers.Block;

    @ui.decorators.textField<PageDirtyState>({
        parent() {
            return this.block;
        },
        title: 'ID',
        isReadOnly: true,
    })
    _id: ui.fields.Text;

    @ui.decorators.textField<PageDirtyState>({
        parent() {
            return this.block;
        },
        title: 'Product',
        isMandatory: true,
    })
    product: ui.fields.Text;

    @ui.decorators.textField<PageDirtyState>({
        parent() {
            return this.block;
        },
        title: 'Description',
    })
    description: ui.fields.Text;

    @ui.decorators.checkboxField<PageDirtyState>({
        parent() {
            return this.block;
        },
        title: 'Hot product',
    })
    hotProduct: ui.fields.Checkbox;

    @ui.decorators.pageAction<PageDirtyState>({
        title: 'Refresh',
        onClick() {
            return this.$.router.refresh();
        },
    })
    refreshPage: ui.PageAction;
}
