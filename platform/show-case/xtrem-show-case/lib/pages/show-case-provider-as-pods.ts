import { GraphApi, ShowCaseProduct } from '@sage/xtrem-show-case-api';
import * as ui from '@sage/xtrem-ui';
import { applicationPages } from '../menu-items/application-pages';

@ui.decorators.page<ShowCaseProviderAsPods>({
    authorizationCode: 'PRVDASPDCLCT',
    businessActions() {
        return [this.$standardCancelAction, this.$standardSaveAction];
    },
    category: 'SHOWCASE',
    headerDropDownActions() {
        return [this.$standardDeleteAction];
    },
    headerSection() {
        return this.headerSection;
    },
    idField() {
        return this.id;
    },
    menuItem: applicationPages,
    module: 'show-case',
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.text({ bind: 'textField', title: 'Name' }),
            line2: ui.nestedFields.text({ bind: '_id', title: 'Id' }),
            line3: ui.nestedFields.date({ bind: 'dateField', title: 'Added on' }),
        },
    },
    node: '@sage/xtrem-show-case/ShowCaseProvider',
    objectTypePlural: 'Providers',
    objectTypeSingular: 'Provider',
    title: 'Provider (as Pod Collection)',
})
export class ShowCaseProviderAsPods extends ui.Page<GraphApi> {
    @ui.decorators.section<ShowCaseProviderAsPods>({
        isTitleHidden: true,
        title: 'Header Section',
    })
    headerSection: ui.containers.Section;

    @ui.decorators.tile<ShowCaseProviderAsPods>({
        parent() {
            return this.headerSection;
        },
    })
    headerTiles: ui.containers.Tile;

    @ui.decorators.textField<ShowCaseProviderAsPods>({
        bind: '_id',
        isReadOnly: true,
        parent() {
            return this.headerTiles;
        },
        title: 'Id',
    })
    id: ui.fields.Text;

    @ui.decorators.textField<ShowCaseProviderAsPods>({
        bind: 'textField',
        isReadOnly: true,
        parent() {
            return this.headerTiles;
        },
        title: 'Provider',
    })
    name: ui.fields.Text;

    @ui.decorators.section<ShowCaseProviderAsPods>({
        isTitleHidden: true,
        title: 'Section',
    })
    section: ui.containers.Section;

    @ui.decorators.block<ShowCaseProviderAsPods>({
        parent() {
            return this.section;
        },
    })
    block: ui.containers.Block;

    @ui.decorators.podCollectionField<ShowCaseProviderAsPods, ShowCaseProduct>({
        bind: 'products',
        fetchesDefaults: true,
        node: '@sage/xtrem-show-case/ShowCaseProduct',
        orderBy: {
            _id: 1,
        },
        parent() {
            return this.block;
        },
        recordTitle(value: any, rowData: ShowCaseProduct | null): string {
            return rowData?._id && rowData?.product ? `${rowData.product} | ${rowData._id}` : '';
        },
        columns: [
            ui.nestedFields.text({
                bind: '_id',
                isFullWidth: true,
                title: 'Id',
            }),
            ui.nestedFields.text({
                bind: 'product',
                isFullWidth: true,
                title: 'Product',
            }),
            ui.nestedFields.switch({
                bind: 'hotProduct',
                isFullWidth: true,
                title: 'Feature product?',
            }),
            ui.nestedFields.multiDropdown({
                bind: 'subcategories',
                isFullWidth: true,
                optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
                title: 'Subcategories',
                minItems: 2,
                maxItems: 4,
            }),
            ui.nestedFields.datetimeRange({
                bind: 'manufacturedWithin',
                isFullWidth: true,
                title: 'Manufacterd within',
            }),
        ],
        title: 'Products',
    })
    products: ui.fields.PodCollection<ShowCaseProduct>;
}
