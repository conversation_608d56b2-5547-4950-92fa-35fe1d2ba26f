import { Collection, Node, Reference, decorators } from '@sage/xtrem-core';
import * as xtremShowCase from '../../index';

@decorators.node<ShowCaseEmployee>({
    storage: 'sql',
    canCreate: true,
    canDelete: true,
    canDeleteMany: true,
    canRead: true,
    canSearch: true,
    canUpdate: true,
    isPublished: true,
    isCustomizable: true,
})
export class ShowCaseEmployee extends Node {
    @decorators.stringProperty<ShowCaseEmployee, 'firstName'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremShowCase.dataTypes.descriptionDataType,
    })
    readonly firstName: Promise<string>;

    @decorators.stringProperty<ShowCaseEmployee, 'lastName'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremShowCase.dataTypes.descriptionDataType,
    })
    readonly lastName: Promise<string>;

    @decorators.stringProperty<ShowCaseEmployee, 'city'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremShowCase.dataTypes.descriptionDataType,
    })
    readonly city: Promise<string>;

    @decorators.stringProperty<ShowCaseEmployee, 'email'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremShowCase.dataTypes.emailDataType,
    })
    readonly email: Promise<string>;

    @decorators.referenceProperty<ShowCaseEmployee, 'country'>({
        isStored: true,
        isPublished: true,
        isNullable: false,
        node: () => xtremShowCase.nodes.ShowCaseCountry,
    })
    readonly country: Reference<xtremShowCase.nodes.ShowCaseCountry>;

    @decorators.referenceProperty<ShowCaseEmployee, 'manager'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => xtremShowCase.nodes.ShowCaseEmployee,
        dataType: () => xtremShowCase.dataTypes.showCaseEmployee,
    })
    readonly manager: Reference<xtremShowCase.nodes.ShowCaseEmployee>;

    @decorators.collectionProperty<ShowCaseEmployee, 'teamMembers'>({
        isPublished: true,
        reverseReference: 'manager',
        node: () => xtremShowCase.nodes.ShowCaseEmployee,
    })
    readonly teamMembers: Collection<xtremShowCase.nodes.ShowCaseEmployee>;
}
