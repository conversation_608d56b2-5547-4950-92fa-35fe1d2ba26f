import { decorators, integer, Node, Collection } from '@sage/xtrem-core';
import * as xtremShowCase from '../../index';

@decorators.node<ShowCaseItem>({
    storage: 'sql',
    canCreate: true,
    canDelete: true,
    canRead: true,
    canSearch: true,
    canUpdate: true,
    isPublished: true,
    indexes: [{ orderBy: { _id: 1 }, isUnique: true, isNaturalKey: true }],
})
export class ShowCaseItem extends Node {
    @decorators.integerProperty<ShowCaseItem, 'quantity'>({
        isStored: true,
        isPublished: true,
        defaultValue() {
            return 1;
        },
    })
    readonly quantity: Promise<integer>;

    @decorators.integerProperty<ShowCaseItem, 'unitPrice'>({
        isStored: true,
        isNullable: true,
        isPublished: true,
    })
    readonly unitPrice: Promise<integer | null>;

    @decorators.stringProperty<ShowCaseItem, 'name'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremShowCase.dataTypes.descriptionDataType,
    })
    readonly name: Promise<string>;

    @decorators.collectionProperty<ShowCaseItem, 'providers'>({
        isPublished: true,
        reverseReference: 'item',
        node: () => xtremShowCase.nodes.ShowCaseProvider,
    })
    readonly providers: Collection<xtremShowCase.nodes.ShowCaseProvider>;
}
