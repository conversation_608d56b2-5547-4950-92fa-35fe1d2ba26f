import { Reference, decorators, Node } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremShowCase from '../../index';

@decorators.node<ShowCaseProductOriginAddress>({
    storage: 'sql',
    canCreate: true,
    canDelete: true,
    canRead: true,
    canSearch: true,
    canUpdate: true,
    isPublished: true,
    isVitalReferenceChild: true,
    isCustomizable: true,
})
export class ShowCaseProductOriginAddress extends Node {
    @decorators.referenceProperty<ShowCaseProductOriginAddress, 'product'>({
        isStored: true,
        isPublished: true,
        isVitalParent: true,
        node: () => xtremShowCase.nodes.ShowCaseProduct,
    })
    readonly product: Reference<xtremShowCase.nodes.ShowCaseProduct>;

    @decorators.stringProperty<ShowCaseProductOriginAddress, 'name'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isNotEmpty: true,
        dataType: () => xtremSystem.dataTypes.name,
        async control(ctx, val) {
            await ctx.error.if(val.length).is.not.greater.than(5);
        },
    })
    readonly name: Promise<string>;

    @decorators.stringProperty<ShowCaseProductOriginAddress, 'addressLine1'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremShowCase.dataTypes.descriptionDataType,
        async control(ctx, val) {
            await ctx.error.if(val.length).is.not.greater.than(5);
        },
    })
    readonly addressLine1: Promise<string>;

    @decorators.stringProperty<ShowCaseProductOriginAddress, 'addressLine2'>({
        isStored: true,
        isPublished: true,
        isNotEmpty: false,
        dataType: () => xtremShowCase.dataTypes.descriptionDataType,
    })
    readonly addressLine2: Promise<string>;

    @decorators.stringProperty<ShowCaseProductOriginAddress, 'city'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremShowCase.dataTypes.descriptionDataType,
    })
    readonly city: Promise<string>;

    @decorators.referenceProperty<ShowCaseProductOriginAddress, 'country'>({
        isStored: true,
        isPublished: true,
        isNullable: false,
        node: () => xtremShowCase.nodes.ShowCaseCountry,
    })
    readonly country: Reference<xtremShowCase.nodes.ShowCaseCountry>;

    @decorators.stringProperty<ShowCaseProductOriginAddress, 'zip'>({
        isPublished: true,
        isTransientInput: true,
        dataType: () => xtremShowCase.dataTypes.descriptionDataType,
    })
    readonly zip: Promise<string>;
}
