import { Context, Node, StringDataType, decorators } from '@sage/xtrem-core';

/**
 * This node is only used for smoke test
 * see pipelines/show-case-smoke-tests/show-case-test-specific-queue.sh for more details
 */
@decorators.node<TestAsyncMutationOnSpecificQueue>({
    isPublished: true,
    storage: 'sql',
    canCreate: false,
    canRead: true,
    canUpdate: true,
    canDeleteMany: true,
})
export class TestAsyncMutationOnSpecificQueue extends Node {
    @decorators.stringProperty<TestAsyncMutationOnSpecificQueue, 'name'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 100 }),
    })
    readonly name: Promise<string>;

    // eslint-disable-next-line require-await
    @decorators.asyncMutation<typeof TestAsyncMutationOnSpecificQueue, 'asyncMutationOnSpecificQueue'>({
        isPublished: true,
        queue: 'showcase-test-specific',
        parameters: [
            {
                name: 'val',
                type: 'integer',
                isMandatory: true,
            },
        ],
        return: {
            type: 'integer',
        },
    })
    static async asyncMutationOnSpecificQueue(_context: Context, val: number): Promise<number> {
        return new Promise<number>(resolve => {
            setTimeout(() => {
                resolve(val * 2);
            }, 1000);
        });
    }

    // eslint-disable-next-line require-await
    @decorators.asyncMutation<typeof TestAsyncMutationOnSpecificQueue, 'asyncMutationToProcessExit'>({
        isPublished: true,
        queue: 'import-export',
        parameters: [],
        return: {
            type: 'string',
        },
    })
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    static async asyncMutationToProcessExit(_context: Context): Promise<string> {
        return new Promise<string>(resolve => {
            // We need a timeout to let the message be dequeued. If we close the application right now, the
            // message will still be in the queue and then, the next time the application will be started, it will be
            // dequeued and will stop the app
            setTimeout(() => {
                console.error(`*************************
**     Exiting (from TestAsyncMutationOnSpecificQueue.asyncMutationToProcessExit) (process = ${process.pid})
*************************`);
                process.exit(0);
            }, 3000);
            resolve('done');
        });
    }
}
