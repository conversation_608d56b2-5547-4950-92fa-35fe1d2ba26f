import { Collection, Context, decorators, Node } from '@sage/xtrem-core';
import * as x3ShowCase from '../../index';

@decorators.node<ShowCaseCustomer>({
    storage: 'sql',
    canCreate: true,
    canDelete: true,
    canDeleteMany: true,
    canRead: true,
    canSearch: true,
    canUpdate: true,
    isPublished: true,
    indexes: [{ orderBy: { name: 1 }, isUnique: true }],
})
export class ShowCaseCustomer extends Node {
    @decorators.stringProperty<ShowCaseCustomer, 'name'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => x3ShowCase.dataTypes.descriptionDataType,
    })
    readonly name: Promise<string>;

    @decorators.stringProperty<ShowCaseCustomer, 'contactPerson'>({
        isStored: true,
        isPublished: true,
        dataType: () => x3ShowCase.dataTypes.descriptionDataType,
    })
    readonly contactPerson: Promise<string>;

    @decorators.stringProperty<ShowCaseCustomer, 'email'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => x3ShowCase.dataTypes.emailDataType,
    })
    readonly email: Promise<string>;

    @decorators.collectionProperty<ShowCaseCustomer, 'invoices'>({
        isPublished: true,
        reverseReference: 'customer',
        node: () => x3ShowCase.nodes.ShowCaseInvoice,
    })
    readonly invoices: Collection<x3ShowCase.nodes.ShowCaseInvoice>;

    @decorators.collectionProperty<ShowCaseCustomer, 'orders'>({
        isPublished: true,
        reverseReference: 'customer',
        node: () => x3ShowCase.nodes.ShowCaseOrder,
        isVital: true,
    })
    readonly orders: Collection<x3ShowCase.nodes.ShowCaseOrder>;

    @decorators.mutation<typeof ShowCaseCustomer, 'testMutation'>({
        isPublished: true,
        parameters: [{ name: 'something', type: 'string' }],
        return: 'string',
    })
    static testMutation(context: Context, something: string): string {
        return something.toLowerCase();
    }

    @decorators.mutation<typeof ShowCaseCustomer, 'anotherTestMutation'>({
        isPublished: true,
        parameters: [{ name: 'something', type: 'string' }],
        return: 'string',
    })
    static anotherTestMutation(context: Context, something: string): string {
        return something.toUpperCase();
    }

    @decorators.asyncMutation<typeof ShowCaseCustomer, 'testAsyncMutation'>({
        isPublished: true,
        parameters: [{ name: 'customer', type: 'instance', node: () => ShowCaseCustomer }],
        return: 'string',
        startsReadOnly: true,
    })
    static async testAsyncMutation(context: Context, customer: ShowCaseCustomer): Promise<string> {
        const result = (await customer.$.get('name')) as string;
        return context.runInWritableContext(writeableContext => {
            writeableContext.transaction.rollbackCache();
            return result;
        });
    }

    @decorators.asyncMutation<typeof ShowCaseCustomer, 'asyncTestMutation'>({
        isPublished: true,
        parameters: [
            { name: 'customerId', type: 'string', isMandatory: true },
            { name: 'newName', type: 'string', isMandatory: true },
            { name: 'mockedProcessingTime', type: 'integer', isMandatory: true },
            { name: 'shouldFail', type: 'boolean' },
        ],
        return: { type: 'object', properties: { name: { type: 'string' }, _id: { type: 'string' } } },
    })
    static async asyncTestMutation(
        context: Context,
        customerId: string,
        newName: string,
        mockedProcessingTime: number,
        shouldFail: boolean,
    ): Promise<{ name: string; _id: string }> {
        // Simulate async operation
        await new Promise(resolve => {
            setTimeout(resolve, mockedProcessingTime);
        });

        if (shouldFail) {
            throw new Error('Simulated failure');
        }

        const customer = await context.read(ShowCaseCustomer, { _id: customerId }, { forUpdate: true });
        const originalName = await customer.name;
        if (await context.batch.isStopRequested()) {
            return {
                name: originalName,
                _id: String(customer._id),
            };
        }

        await customer.$.set({ name: newName });
        await customer.$.save();

        if (await context.batch.isUserNotificationRequested()) {
            await context.notifyUser({
                shouldDisplayToast: true,
                level: 'success',
                title: context.localize(
                    '@sage/xtrem-show-case/async-mutation-customer-renamed-title',
                    'Customer renamed',
                ),
                description: context.localize(
                    '@sage/xtrem-show-case/async-mutation-customer-renamed-description',
                    'Customer {{originalName}} has been renamed to {{newName}}',
                    { originalName, newName },
                ),
                icon: 'person_tick',
                actions: [
                    {
                        title: context.localize(
                            '@sage/xtrem-show-case/async-mutation-customer-renamed-action',
                            'View Customer',
                        ),
                        link: `@sage/xtrem-show-case/StandardShowCaseCustomer/${customerId}`,
                        style: 'primary',
                        icon: 'view',
                    },
                ],
            });
        }

        return {
            name: await customer.name,
            _id: String(customer._id),
        };
    }
}
