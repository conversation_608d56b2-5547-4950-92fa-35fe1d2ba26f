import { Collection, Context, Node, decorators, integer } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremShowCase from '../../index';

@decorators.node<ShowCaseCountry>({
    storage: 'sql',
    canCreate: true,
    canDelete: true,
    canDeleteMany: true,
    canRead: true,
    canSearch: true,
    canUpdate: true,
    canDuplicate: true,
    isPublished: true,
    isCustomizable: true,
    indexes: [{ orderBy: { code: 1 }, isUnique: true, isNaturalKey: true }],
})
export class ShowCaseCountry extends Node {
    @decorators.stringProperty<ShowCaseCountry, 'code'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremShowCase.dataTypes.descriptionDataType,
        async duplicatedValue() {
            return `NEW_CODE_${await this.code}`;
        },
    })
    readonly code: Promise<string>;

    @decorators.integerProperty<ShowCaseCountry, 'phoneCountryCode'>({
        isStored: true,
        isPublished: true,
    })
    readonly phoneCountryCode: Promise<integer>;

    @decorators.stringProperty<ShowCaseCountry, 'name'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremShowCase.dataTypes.descriptionDataType,
    })
    readonly name: Promise<string>;

    @decorators.collectionProperty<ShowCaseCountry, 'employeesInCountry'>({
        isPublished: true,
        node: () => xtremShowCase.nodes.ShowCaseEmployee,
        reverseReference: 'country',
    })
    readonly employeesInCountry: Collection<xtremShowCase.nodes.ShowCaseEmployee>;

    /**
     * This method has nothing to do with show case country, we just need to put custom mutations in some node.
     */
    @decorators.mutation<typeof ShowCaseCountry, 'generateFakeNotification'>({
        isPublished: true,
        parameters: [
            {
                name: 'title',
                type: 'string',
                isMandatory: true,
            },
            {
                name: 'description',
                type: 'string',
                isMandatory: true,
            },
            {
                name: 'icon',
                type: 'string',
                isMandatory: true,
            },
            {
                name: 'level',
                type: 'enum',
                dataType: () => xtremSystem.enums.sysClientNotificationLevelDataType,
                isMandatory: true,
            },
            {
                name: 'shouldDisplayToast',
                type: 'boolean',
                isMandatory: true,
            },
            {
                name: 'actions',
                type: 'array',
                item: {
                    type: 'object',
                    properties: {
                        _id: 'string',
                        link: 'string',
                        title: 'string',
                        style: {
                            type: 'enum',
                            dataType: () => xtremSystem.enums.sysClientNotificationActionStyleDataType,
                        },
                    },
                },
            },
        ],
        return: {
            type: 'boolean',
        },
    })
    static async generateFakeNotification(
        context: Context,
        title: string,
        description: string,
        icon: any,
        level: any,
        shouldDisplayToast: boolean,
        actions: { _id: string; link: string; title: string; style: any }[],
    ): Promise<boolean> {
        await context.notifyUser({
            title,
            icon,
            description,
            level,
            shouldDisplayToast,
            actions,
        });
        return true;
    }
}
