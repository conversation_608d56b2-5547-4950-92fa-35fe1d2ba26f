import { Node, Reference, decorators, integer } from '@sage/xtrem-core';
import { ShowCaseBillOfMaterial } from './show-case-bill-of-material';
import { ShowCaseItem } from './show-case-item';

@decorators.node<ShowCaseComponent>({
    storage: 'sql',
    canCreate: true,
    canDelete: true,
    canRead: true,
    canSearch: true,
    canUpdate: true,
    isPublished: true,
    isVitalCollectionChild: true,
    indexes: [
        {
            orderBy: {
                billOfMaterial: +1,
                componentNumber: +1,
            },
            isUnique: true,
        },
    ],
})
export class ShowCaseComponent extends Node {
    @decorators.referenceProperty<ShowCaseComponent, 'billOfMaterial'>({
        isStored: true,
        isPublished: true,
        isVitalParent: true,
        node: () => ShowCaseBillOfMaterial,
        lookupAccess: true,
    })
    readonly billOfMaterial: Reference<ShowCaseBillOfMaterial>;

    @decorators.integerProperty<ShowCaseComponent, 'componentNumber'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
    })
    readonly componentNumber: Promise<integer>;

    @decorators.referenceProperty<ShowCaseComponent, 'item'>({
        isStored: true,
        isPublished: true,
        node: () => ShowCaseItem,
        ignoreIsActive: true,
        isNullable: true,
        lookupAccess: true,
    })
    readonly item: Reference<ShowCaseItem | null>;

    @decorators.referenceProperty<ShowCaseComponent, 'bom'>({
        isPublished: true,
        node: () => ShowCaseBillOfMaterial,
        ignoreIsActive: true,
        isNullable: true,
        dependsOn: ['item'],
        join: {
            item() {
                return this.item;
            },
            async site() {
                return (await this.billOfMaterial).site;
            },
        },
        lookupAccess: true,
    })
    readonly bom: Reference<ShowCaseBillOfMaterial | null>;
}
