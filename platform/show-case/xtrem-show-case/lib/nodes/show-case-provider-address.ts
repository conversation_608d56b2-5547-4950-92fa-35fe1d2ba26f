import { decorators, Node, Reference } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremShowCase from '../../index';

@decorators.node<ShowCaseProviderAddress>({
    storage: 'sql',
    isPublished: true,
    canCreate: true,
    canRead: true,
    canUpdate: true,
    canDelete: true,
    canDeleteMany: true,
    indexes: [{ orderBy: { name: 1 }, isUnique: true }],
    isCustomizable: true,
})
export class ShowCaseProviderAddress extends Node {
    @decorators.stringProperty<ShowCaseProviderAddress, 'name'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isNotEmpty: true,
        dataType: () => xtremSystem.dataTypes.name,
    })
    readonly name: Promise<string>;

    @decorators.stringProperty<ShowCaseProviderAddress, 'addressLine1'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremShowCase.dataTypes.descriptionDataType,
    })
    readonly addressLine1: Promise<string>;

    @decorators.stringProperty<ShowCaseProviderAddress, 'addressLine2'>({
        isStored: true,
        isPublished: true,
        isNotEmpty: false,
        dataType: () => xtremShowCase.dataTypes.descriptionDataType,
    })
    readonly addressLine2: Promise<string>;

    @decorators.stringProperty<ShowCaseProviderAddress, 'city'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremShowCase.dataTypes.descriptionDataType,
    })
    readonly city: Promise<string>;

    @decorators.referenceProperty<ShowCaseProviderAddress, 'country'>({
        isStored: true,
        isPublished: true,
        isNullable: false,
        node: () => xtremShowCase.nodes.ShowCaseCountry,
    })
    readonly country: Reference<xtremShowCase.nodes.ShowCaseCountry>;

    @decorators.stringProperty<ShowCaseProviderAddress, 'zip'>({
        isPublished: true,
        isTransientInput: true,
        dataType: () => xtremShowCase.dataTypes.descriptionDataType,
    })
    readonly zip: Promise<string>;
}
