import {
    BinaryStream,
    Context,
    date,
    datetime,
    decimal,
    decorators,
    integer,
    Node,
    Reference,
    StringArrayDataType,
    ValidationContext,
} from '@sage/xtrem-core';
import { DatetimeRange } from '@sage/xtrem-date-time';
import * as xtremUpload from '@sage/xtrem-upload';
import { promisify } from 'util';
import * as xtremShowCase from '../../index';
import { showCaseDiscountOption } from '../service-options/show-case-discount-option';
import EventEmitter = require('events');

@decorators.node<ShowCaseProduct>({
    storage: 'sql',
    canCreate: true,
    canDelete: true,
    canRead: true,
    canSearch: true,
    canUpdate: true,
    canDuplicate: true,
    isPublished: true,
    isVitalCollectionChild: true,
    isCustomizable: true,
    hasAttachments: true,
    async controlEnd(cx: ValidationContext) {
        if ((await this.st) && (await this.st) < 0) {
            cx.error.add('Some global error');
            if ((await this.st) && (await this.st) < -2) cx.error.add('patata');
        }
    },
})
export class ShowCaseProduct extends Node {
    @decorators.stringProperty<ShowCaseProduct, 'product'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        duplicateRequiresPrompt: true,
        dataType: () => xtremShowCase.dataTypes.descriptionDataType,
        async duplicatedValue() {
            return `New ${await this.product}`;
        },
    })
    readonly product: Promise<string>;

    @decorators.stringProperty<ShowCaseProduct, 'barcode'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremShowCase.dataTypes.descriptionDataType,
    })
    readonly barcode: Promise<string>;

    @decorators.stringProperty<ShowCaseProduct, 'description'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        duplicateRequiresPrompt: true,
        dataType: () => xtremShowCase.dataTypes.descriptionDataType,
    })
    readonly description: Promise<string>;

    @decorators.booleanProperty<ShowCaseProduct, 'hotProduct'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
    })
    readonly hotProduct: Promise<boolean>;

    @decorators.integerProperty<ShowCaseProduct, 'qty'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        defaultValue() {
            return 1;
        },
        dependsOn: ['fixedQuantity', 'provider'],
        async updatedValue() {
            if ((await this.fixedQuantity) > 0) {
                return this.fixedQuantity;
            }
            return this.qty;
        },
        async control(ctx, val) {
            await ctx.error.if(val).is.not.greater.than(await (await this.provider)!.minQuantity);
        },
    })
    readonly qty: Promise<integer>;

    @decorators.integerProperty<ShowCaseProduct, 'fixedQuantity'>({
        isStored: true,
        isPublished: true,
        updatedValue() {
            return this.fixedQuantity;
        },
    })
    readonly fixedQuantity: Promise<integer>;

    @decorators.integerProperty<ShowCaseProduct, 'st'>({
        isStored: true,
        isPublished: true,
        defaultValue() {
            return 1;
        },
    })
    readonly st: Promise<integer>;

    @decorators.decimalProperty<ShowCaseProduct, 'listPrice'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremShowCase.dataTypes.defaultDecimalDataType,
    })
    readonly listPrice: Promise<decimal>;

    @decorators.integerProperty<ShowCaseProduct, 'progress'>({
        isStored: true,
        isNullable: true,
        isPublished: true,
    })
    readonly progress: Promise<integer | null>;

    @decorators.decimalProperty<ShowCaseProduct, 'netPrice'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremShowCase.dataTypes.defaultDecimalDataType,
        dependsOn: ['listPrice', 'qty'],
        async defaultValue() {
            if ((await this.qty) > 10) {
                return (await this.listPrice) * 0.9;
            }

            return this.listPrice;
        },
        async control(ctx, val) {
            await ctx.error.if(val).is.negative();
        },
    })
    readonly netPrice: Promise<decimal>;

    @decorators.integerProperty<ShowCaseProduct, 'discount'>({
        serviceOptions: () => [showCaseDiscountOption],
        isPublished: true,
        isStored: true,
        isNullable: true,
        defaultValue: 20,
        control(ctx, val) {
            if (val && val > 100) {
                throw new Error('Something bad happened');
            }
        },
    })
    readonly discount: Promise<integer | null>;

    @decorators.decimalProperty<ShowCaseProduct, 'tax'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremShowCase.dataTypes.defaultDecimalDataType,
    })
    readonly tax: Promise<decimal>;

    @decorators.decimalProperty<ShowCaseProduct, 'amount'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremShowCase.dataTypes.defaultDecimalDataType,
    })
    readonly amount: Promise<decimal>;

    @decorators.decimalProperty<ShowCaseProduct, 'total'>({
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremShowCase.dataTypes.defaultDecimalDataType,
        dependsOn: ['amount', 'tax'],
        async getValue() {
            return (await this.amount) + (await this.tax);
        },
    })
    readonly total: Promise<decimal>;

    @decorators.referenceProperty<ShowCaseProduct, 'provider'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        node: () => xtremShowCase.nodes.ShowCaseProvider,
        isVitalParent: true,
        duplicateRequiresPrompt: true,
    })
    readonly provider: Reference<xtremShowCase.nodes.ShowCaseProvider | null>;

    @decorators.stringProperty<ShowCaseProduct, 'email'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremShowCase.dataTypes.emailDataType,
    })
    readonly email: Promise<string>;

    @decorators.stringProperty<ShowCaseProduct, 'computedEmail'>({
        isPublished: true,
        dataType: () => xtremShowCase.dataTypes.emailDataType,
        async getValue() {
            return `Sage.${await this.email}`;
        },
    })
    readonly computedEmail: Promise<string>;

    @decorators.datetimeProperty<ShowCaseProduct, 'importedAt'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly importedAt: Promise<datetime | null>;

    @decorators.dateProperty<ShowCaseProduct, 'releaseDate'>({
        isStored: true,
        isPublished: true,
        defaultValue() {
            return date.today();
        },
    })
    readonly releaseDate: Promise<date | null>;

    @decorators.dateProperty<ShowCaseProduct, 'endingDate'>({
        isStored: true,
        isNullable: true,
        isPublished: true,
        async control(ctx, val) {
            if ((await this.releaseDate) && val) {
                await ctx.error.if(val).is.less.than((await this.releaseDate)!);
            }
        },
    })
    readonly endingDate: Promise<date | null>;

    @decorators.enumProperty<ShowCaseProduct, 'category'>({
        dataType: () => xtremShowCase.enums.showCaseProductCategoryDataType,
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
    })
    readonly category: Promise<xtremShowCase.enums.ShowCaseProductCategory | null>;

    @decorators.enumArrayProperty<ShowCaseProduct, 'subcategories'>({
        dataType: () => xtremShowCase.enums.showCaseProductCategoryDataType,
        isStored: true,
        isPublished: true,
        isNullable: true,
    })
    readonly subcategories: Promise<xtremShowCase.enums.ShowCaseProductCategory[] | null>;

    @decorators.stringArrayProperty<ShowCaseProduct, 'entries'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringArrayDataType({ maxLength: 30 }),
    })
    readonly entries: Promise<string[] | null>;

    @decorators.referenceProperty<ShowCaseProduct, 'designerEmployee'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => xtremShowCase.nodes.ShowCaseEmployee,
        dataType: () => xtremShowCase.dataTypes.showCaseEmployee,
    })
    readonly designerEmployee: Reference<xtremShowCase.nodes.ShowCaseEmployee | null>;

    @decorators.referenceProperty<ShowCaseProduct, 'originAddress'>({
        isVital: true,
        isPublished: true,
        lookupAccess: true,
        reverseReference: 'product',
        isNullable: true,
        node: () => xtremShowCase.nodes.ShowCaseProductOriginAddress,
    })
    readonly originAddress: Reference<xtremShowCase.nodes.ShowCaseProductOriginAddress | null>;

    @decorators.referenceProperty<ShowCaseProduct, 'certificate'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        node: () => xtremUpload.nodes.UploadedFile,
    })
    readonly certificate: Reference<xtremUpload.nodes.UploadedFile | null>;

    @decorators.datetimeProperty<ShowCaseProduct, 'createdAt'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly createdAt: Promise<datetime | null>;

    @decorators.datetimeRangeProperty<ShowCaseProduct, 'manufacturedWithin'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        duplicatedValue: null,
    })
    readonly manufacturedWithin: Promise<DatetimeRange | null>;

    @decorators.binaryStreamProperty<ShowCaseProduct, 'imageField'>({
        isNullable: true,
        isPublished: true,
        lookupAccess: true,
        computeValue() {
            return BinaryStream.fromBuffer(
                Buffer.from(
                    '/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAA0JCgsKCA0LCgsODg0PEyAVExISEyccHhcgLikxMC4pLSwzOko+MzZGNywtQFdBRkxOUlNSMj5aYVpQYEpRUk//2wBDAQ4ODhMREyYVFSZPNS01T09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT0//wAARCALQAtADASIAAhEBAxEB/8QAGwABAQACAwEAAAAAAAAAAAAAAAECBQMEBgf/xAA7EAACAQMDAgQEBAUEAgMBAQEAAQIDESEEMUFRcQUSYYETIpGhBjKxwUJS0eHwFCMzYnLxFSRDNFOS/8QAGAEBAQEBAQAAAAAAAAAAAAAAAAEDAgT/xAAgEQEBAAICAgMBAQAAAAAAAAAAAQIRAzESIRNBUTIi/9oADAMBAAIRAxEAPwD6cAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAv1AA4qmpoUv+StTj3kkcE/FdFDfURfa7/Qm4uq7gNZPxzRr8vnn2j/U4J/iGmr+TTyl3lY588f1fGt0Dzs/xBXl+ShTXdtmWg8X1FbW0qdaUfJJ2dlzwPkm9HhXoAAduQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGE61Kn+epCNuskjrz8S0cN9TT3th3/AEJuGnbBrZ+N6KO05S7ROCf4gor8lCb7tInnj+uvGtyDz0/xFUf5KEI922cE/HNbLaVOOP4Y/wBTn5MV+PJ6gXPHz8T1s7qWomuMY/Q4Z6irUv56s59bybJeWL8deyqV6NO/xKsIW/mkkdefimip/m1EMYxn9DyNzG6tx3Oby118cepn45oo7Oc+0f6nBL8Q0l+ShJ93Y88n7lx1OflyX443M/xBWa+ShBd22dafjetltKMOtor9zXE+hPPL9XwxduXiOsqb6ie1sO36HBKtVn/yVJyfrJswTXoFvt9zndXUOnUN8C/Gwe+wVRfgju9jFkGRYycZqS3i00Yp3uiXzhFg9zQqKtQhUjtOKaOQ1f4frfE8PVNvNKTj7bo2h65dx5rNUABUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAXAAxlUhDM5qPd2OCev0cN9TS9pXJuGnZBrp+NaCO1Vy7RZwS/EOn/AIKVV97Innj+uvHL8bgHn5/iOX8Gniv/AClc4J+P6yV/Kqce0bnPyYr8eT09xc8hPxbXTv5tQ0v+qSOCer1NS/nr1HfhyZLyx18VeznVpw/PUjHu7HBPxHR0/wA2pp+0rnjZNu7vf1JdPldjn5avxPWT8b0Mdqkpdos4JfiGh/BRqPvZHm7rAu+hPlyX443k/wARTa+TTxi/WVzrz8e1ktvhwVuI/wBTVYy9i39Uc+eX668MXcn4prpp31Ml2sv0OCeqr1P+StUlfrJs4l6E4zyTdXUZXe7ZMPqE7EclFNtpJZ7EGV7Y3Jv1I3i6CYFX3Hr6kb9Av2AyWfcl825JcXAqwVkVvoS6XUC9xfmxOjG4FuRbl/UhBb2Cs8oiSSsXLAJWfNi7IxyJJNNSV1YoyuREtdW27FWN8dwCtgcjZ7EeANz+HK3l1c6TeJxuu6PSHiNFVdHWUqt2rSV36Xye2i7o9HFdxjyT2oANGYAAAAAAAAAAAAAAHBV1dChUjTq1FGUtkNjnBIyUleLumUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAaDWeMaulUqU4wgnGVtvU355rx6l5NZ5rYqK/vs/2OOS2T07wkt9utU8X187r47XpFJHXnrNVUv5q9V34cnY4XuS/Y89yrfxiyfLd+RezXXpbgjd3h39CYIqq63F+hHLhBLN7exBW29wS7wFZ7r+4FT7lv9exhzgW5AreM3Yvs747k2DygL/2ewvcl+g+oF9SLoLq1sF5KGb2/ct7dDHPP2Cy8gW9+SpoxeHvhC/XARbr+wvx+hFht4L77gPUX6fYXJe6VwLuGEx9wHTkXwwk9+Bd7WAvCJddQNv8A0BWMoX9kHnIEW+xb5yRbbDHv1AruL5HPJdluBL532GL3Le2LjcCX2xxyTD63K3wMdQH3PZeF1/j+H0pt/Naz7o8Zsz0P4ar/AC1dO3lPzL9zTivtxyT03oAPQwAAAAAAAAAAABJNRTbdkjSeJeLOTdHSvG0prnt/U5yykWY2307HiXiioXpUGpVeXxE0M5ynNynJyk3dtmHAuYZZWt8cZi7Wl12o0r/2p3j/ACyyjdaPxehXtGq/hT9dn7nnOOAn6DHOwuEr2iaewPK6XxDUaVryy80P5JbG70nimn1Foyl8Ob4l/U2xzlZXCx3wE7g7cAAAAAAAAAAAAAAAAAAAAAAAAAAAA0viHj1LSa/4EUqigk6qi8xv/n3NzFqUU07pk2tligAqAAAGp/EFLzaWnUS/JKz7P+9jbHX11H4+jq0lvKLt34+5MpuaWXVeLkrMxs/NsZyte5g98M8j1CfKuHjkLBHHHOCBb3GPoS/AxfYC3Q3/AGIl6bh27gM5f6FWexMNLCABr7FVvMvNe3NiYQTAXL337EwOlwMubck97BYeCbvb7FGV7pcsmNkRPAT7ewFyHhEbsW4Qz0FvUm/oXn9gC6B26B2TV1f2F7ANi74syX5wr+oSAqVxZ+hFhsvHYAmtluNuQ+yIsgGs4uNldsuLW3J9QKmuAER5wBU+m5eTFea69OS83dwLxsO5Fm5d+QJ9S/oF0uS4A7/g9f4HiNJt/LJ+R+/97HQKpOLUouzTumWXV2lm496gcWlqqvpqdVfxxTOU9bzAAAAAAAABhVqQpQc6klGK3bMNVqaWlpeerK3RLdnmtbrqusneeILaF8HGWcjrHG1z+I+Jz1N6dO8aXP8A2Ndcb9iOxhbb23kkml5GzF8EZyo27lvfgjW6Kn1CmQxt7i/QDu6XxPU6ZKPm+JBfwy47M3mk8SoaqyUvJP8All+x5YK7O8eSxxlxyvag8zpPFdRp7Rm/iQ6N5XubvS+IafVWVOdpfyywzbHOVjlhY7YAO3IAAAAAAAAAAAAAAAAAAAewAHivxHoavh2vlrNLThKnqGlJW263b22xb1xg23gHiMXThppyVrf7bvuundG18S0NLX6SVCsk1dSi+jWUzwWjl/oNTPTV5ThUdW0fM8+ZNq/vb9DO/wCbtpP9TT6ODo+F61aqgr2863R3jSXbOzQAAAAA8b4lSVDXVoJY8112ef3OnK+epvPxHR8tenW4nHyvuv8A2aJtcHlzmq9OF3Arb/xEd79PYmb4eEcOjkNYFlZXywwLfi1iO/sTnYrWFkBf1wT0u+xbZ3JswDS9xHdq/wBg73yFdMCpWY6ZInjj+gx/7Ayv1D/yxC+5RFfdMLt9S9mRBF6GK55KlkReMXAPG/Abyv1DvnDFsZAqWS44uY89g7WyBVjkZadiXVi2fp7AH3yM9CP9CpsC26kffHcP0uMgVuyvwL4Ja42AuGS64sF6BrAFv7C+SbdWOMAW4vjYl2sPYqfS4FXJGvUNvhcchX5w/qAQ2FyNpvIHqPw5W+JopUm805deH/jNueV/D1f4XiHkbxVi4+6yv3PVHp47vF585qgAO3IAAB09f4hS0cM/NUa+WP8AU4PEvFI6dOlRtKr14ieeqTnUqOdRuUnu3yZ5569RpjhvtyanUVdTVdSrJt8LhHDfOS9yN5MLWy7kt9QPQgZbGHvwORe7AewJf3K3gC2wQJfcZCrcmeRyM9wKL2s1dPgEvsBs9J4xXotRq/7sPXDXubvS62hqo3pTzzF7o8jyVSlGScG01yjvHks7cZccvT2oPPaTxmpS+XUr4keq3X9TdabV0dVG9Gal1XKN8c5kxyxsc4AOnIAAAAAAAAAAAAAAAAea/Ffh1WSXiOl/PSjacYrL6O+/c9KYVacK1KdKpFShNOMovZp8Es2surt4vwrW1aShVlbzrFSKd8nstNXjXpRnFp3R4HXaWPhHi9SnKdV0owjGN7JKL263ePTk3XhOvemq/DqP5G7djPG6uq0ym5uPUgxhJTipRd0zI1ZAAA1vj1FVfDnLmm1L9n+p5R3TPc16arUZ0pbTi4/U8POLhUcZXTTs+5hyz3ttxX1pxsNq1rlbSRi+2DJsy33Md+iJi4e39iIrusEb4W3I5uL3fABLIs3ZFVr9OyLbN2UG+SfoT2L3a9gDtawWyJ9S7MCt57hPLInjoHnn7AVsXtYi32H6AL8l5Iu7HYIyzgxWV0CKscAO7fYNPuTCW3JXcA9ip4yyPcW3f1AJlV75JngXArsl+4wnfnYjvdKz23Jf0Ay3wN3vuRv6C9kBQ2+WQfTIFv2XuLvbryN3m77j0uAtdO+xfYjuL9dgKSz4G5G872Ar4Q3Jh34LygOShUdGvCqt4SUj3UJKUFJbNXR4BPOD1/gVf4/htO7+aneD9tvtY24r9MuSfbYgGNSpCnBznJRit2zZkrdldmk8S8X3o6R95r9jr+JeKT1LdOjeNL7y/sa25jlyfUa44fdHu22xlAnJk1Uckdi+xA4wgM8D9QGdkE83YHIBgcjbkBm24W/7DO2AvQBssh3WxeCBTcjyXI9CCGXcjt0FwC6szhOUJqUG01ynkw3LZlG30njU4NR1MfOv5o7m5oailqI+alNSX6HjrHJTqzpSU6U3FrZpmmPJZ2zy45ensgaPR+NtfJq43/7x/dG4o1qdeHnpTUo9UzaZS9MrjZ25AAdOQAAAAAAAAAAAABqPxJ4a/EPDZfChGdel89NSW75R5TR1/iRnDyyvRfk81rRkli6tjg+hPJ4r8UeH1tFr4azSNeWtN+bzbLb5cZzZvBnnPtphfpt/BfEdtPVl/wCLZvjwsZWalFtLdNHp/CPEFqafw6jSqxX1GGX1TPH7bMAGjMPH+OUfg+JVLL8/zr3/AL3PYGg/E9C8aFZesH+q/RmfJN4u+O6yeduuovd9A97vcnoeZ6VI0tgg7XwioWWR0bGLk3XcCrb0Lne/qRpWVyZ2wBcO2SrpjJL7jbnID0vcPHP3DyL7XAvoHYjtbAvyBfbA9OWOcEd8X3AuEGyb5YYQusZ7FvyyJWyPXoBW/oE7dLkLZIBd+/qLrjcb7IlrgZbDoS9lt/YX6ATbZlb6i7td9gmr9QKsIvR7GLa6MXur7FFTKmYY/wDYurXaAy5YvZ3MPNuiOdsdSDk46LoS9mcfxDCdaMf4kvW4HO3m1iSku7Om9XBbPzP0MHqpN/LSk12LpXe8z6YI6itc1/xa76IxtNr5qjGhsJV6ccylY3/4S1kKtbUUIO68qn2e3+djx7jBv5nKXdmx8A8Y03g2sqTrxl8GpFRbirtO51h6rnObj6BqtTS0tF1K0rJfVnmNd4jU1k83jTTxBfuT8Va6jXo6Krpa8ZJuf5XfhYfQ1On1anZVPll+p3yZXpxhjNbdy999jK7ONSukZJ8LYyaLxsLMWyE7ogZLl5JwALz6jbOCbhppLO4BlfpsPcn6AN88FuTmzFkA52wVr2HIuAzwwxtuhwFOBa2yHsM3IGbXIsvIyOQHGCom47oCqwe+9h6MtkFTk5KNarQkp0pOL6rk48fQl8F2ab3SeNxuoaqNn/NH90binUhVgp05KUXymeLXH+XOWhqaunl56NRxfThmmPLZ2yy459PYg1Gj8ahNKOpShL+ZbG2i01dO6ZvMpemNlnagAqAAAAAAAAB19bpKes07pVVfKlF/ytZTOwAPnSdTTeI19Nqat5pqPopd31uju0a86NWNSnK0ou6Zsvxd4U9RTp6ujRjKcZqNS7eE7LzO29ufTsaHS6unq4ylDEou04vj1PPlNVvjdx7nw/Ww1unVSOJLEo9Gds8VoNZU0eoVSGVtKPVHsNPXp6ijGrSd4yRrhluMssdOU6XjFH43h1VJXcV5l7f2O6SSUotPZqx1fcSXT5/PEvUx7nY1lF6fU1Kck7xk1k68peiPJY9QrehH9PYl7K1vsXncir/Fa4x5vXsSPRNXF8b39Ci8X/YPCwTMcWF7XTsEZNNK5FewTe/IUroC3tuFlNk9AAfToVZ3ZGE87hFxcdMkbXQqbAWV1cL13JnuG7K9wLzlhsja6bkuvS4GV98WD3sYN2xfPJfNjYuhlwyGHnXTIc1uwM8N2GGcHxorb3OOergt2B2r5Dkup0Hrou/lTl2yYOvVknaCivVgbBVEnuYyqxSyzXuVWWXUt/4/4iNK3zSk753A7sq8f5kvVs4pauOybZ126az5V+o+L02A5pamo38tNrvgwcq0vzVIx9E3c4nVb2MHNt74A5fKm7yq1Je9v6i1JcX9W7nFexPN6Ac6mo7RS7JIOode66iV1L5segHM6r5MHNvn6nG39CXYGbn6nR1k25QjfFzsN2udGu268UUbeKdSjGS/MlZtb+hy0avngk2r74Ovp5uNmnZ8HLWh5bVYbN5twB36WolCyldw6r9zv06ilFNNZNLCoc1Ocqb81O2f4SDbrO5eyOtp9Sqis8PozsL6AZD04Ju8F53At+CS/wAQDf2AJKxepGHsQX7E5xsL5sUBcXtuBuAWFclsjdFdwF7LqN1a9iL1K7BUawmsjku5N2A7C7tgWtyPUgqQsTnBkmuQMUrsrS6kbtwLNhV7e5JOMYuU2oxSu5N2SMa1anQpOpVklFffscfh/hmr8erqdaLo6KLuo/zd+p1jja5tkcWmjqfGdSqOhUo6dP56mzkv2X6nvdNSdHT06UpeZwVr9TDR6OjoqCo0IKMV9zsHoxx8WGeXkAA7cAAAAAAAAAAAxnFTg4ySaas0+TwPi+l1Xg+t8tNTraeTbSvaKj+ifr6H0A1X4g8Kh4polHarSl54Pr1i/RnOeO46xy1XlmumenqbDwjxJ6Kt5ajfwZv5vT1NH4fOp5p0K06bazCMXlLa2Mev/s7b9DCWyt7Nx76MlJJxaaezRTzv4f8AEttHWlj/APNv9D0RvjluPPZq6eU/ElD4WuVRLFSP32NKspnrfxLQ+JoYVF/+csvon/iPJPCMOSayejju8UbLe69CPgmfU4dqrLYv27mN37C4RndXI1F7K5jfO+4v6lGT6fUNu/6Ecl7oxb5uBngqZx3suCebfOAOW69RfKtwcHxEk8mD1EYrMgjst9QpYuzoy1lOKb8yZx/63z3UISl2A2PxEn0Zi6nRvvc1sq9ee0LJ9cGPmrS3qJL0/uBs/iRzdmD1EFlyX1Nc4Xfz1JPPYnkpLFr55yB3Za6kn8ruYPWSa+WlJ35OspqOIryr0wR1G9yjndetLZRj3Zg5VX+ar3sjh89w5X5A5Wo/xOUn6yJ54J4SRxeb0Mb3sBzuq32MfiPJxSb9hfGQM3KTY8yRjfJE/sBm5E831MW84ZeAKm9lkYvsY+5L5/YDK5JPpcl0hnkDK7I36ExsSTSAt8PJLtK5jcN2Ajl1OlN31KOzN4uzqxd9Sio2lF4R2YTcW1/C1Zq/B1Kexzp4Iqzg6U0lmnPMWckZW7HBXqzhSSTw5q/3Moz2A7cZXs02mtmjtUNU42hV+vU16l0OWM01Z5vumQbmMk8p7mS2RqqVadGzi24fdHfpVo1EmncDseliXItipkDIfUZ5DfIF4InkelgBdxtuRPBbXYAYb+wIwqpOw4IvVoyx1Aj+xGsmWckW+EAIu2C27EeOwBLNy8i6DyQG7HDqtXT00Fe86kvywW7OPV6vy1FptNH4uol/CsqPc3fgP4d+DL/V6+9TUSzaXB3jha5yymLpeEfh+tr6kdb4pdR3hTWLI9hTpwpQUKcVGKwkuDJKywD0Y4yMMsrQAHTkAAAAAAAAAAAAAA8gAeG/F3hMdJVjq9LP4Cr1vNNpPE7La3Zvu2delUVWmpLD/iTVmn24Pd6vTU9XpqlCqrwmrM+cfDWg8V1NOuqkXGTjUbsorlNLm/Hcx5Mftthlv07d3GV08rlHrvBPE1rdP5Kj/wB+mvm/7LqeTkl36NDTV6mk1Ea1J2lHY4xy8a6yx3HudbQWo0lWk1+aLS78Hgp3UmrnutBrKet0sa1PnddH0PFeOOnpPFa1LL+bzW23z+53yTftxx3Vsdd2eTF9UdaWuppO7S9LnDPxCmsLPYybbd+6ezMXJddjXvX+b/jg32VzCWo1E3eyj33GhsvPZXbMZVYpP5l+pq3KtPeol7tj4d/zTbA2D1MI7yRxT10VtudXyU44aT63yFNR/Kkv/HBUcv8Aq6zXywdiOpqJYbUfdP8AQwdT/wBmLqvoBm4z/jrSt6Inw4rdt92cfnbTzgef6gcy8ieIxT7B1G3luyOBy4uRv1A53U6Mxc2ji82AnncDNzdtyKeL3ZjyRPqwM3LIvncweLFugLeyGW9iJ4tcXAzs7depispcEcrEjsrgZdMB7f5gieb3D2eeAMr3JnGMEWQsAZO+CXyS/puF1sBb+wJvnBOdwMnnYXte5jfqRvIGTd0zFtPsP4SO/UoO5Lkbss7mLeAI3g61H/8AqOaT+U4NP/zMrmtnTbOZPqdeG2DmjwRXHXl/xLrURzrodetb41Ferf2OeLt1IrNdbmSZhFhfl9gOeM2mjlg2peam7PpfB1k+pnGVmQbWhq4ztGStLodqLujSXi8tu/XodrT6mUGozd1w+oGxv6F2MIzUkmmZcfsBb8B4MW8YdjJdSBxwXgd7kX1AqQIW+1gpZ9PYl8WsSc401epOMV1k0jglr9Ioxa1FNqcvLFxd/M+i67l0bdlv+Wz9LjzZuaLVfifTUqzo6ejUrVIvytt+RJ/dir4xXerqaTFKpGOJRj535mrr5bYWc8l8ank3uThr6rTadf79enS5+aaTNFpaHjM9Qqmv1PmpR+ZU7+VTfC8tl919Sx8JqVKHwtbXhVg5/EtaUmn/AOV11L4z7TbZvxbw9UnWWoUqd/L5oxbTfRdfY62u8aqU2o6fw/VVYyStWirRyr4aTySHhuihQjRWnTgne0pPL6u2/wChz0qNGhmhRpUvWEFF/VDUPbpUtd4hotXCOh0LTt5p1IQcmm+G3dJ+/wDQ93+HPEv9bQlCdOvCpDL+LOMm/dP+hqvDPBq+ucamo80KHq8y7eh6nS6WjpKKpUIKEV0NcJWWdjmABozAAAAAAAAAAAAAAAAAAAPNfjLw2Nfw96uFKUpUrOp5HaTgudne39T0pJJNWeUyWbiy6u3zfRVoToqmkoypr8qlf5b4/wA7HLNbsy8Y0X/xnjrmqMY0ZRc1UbeYuyawt03zfdEpuOo06r0X5obNcxfKZ57NPRLt2PCvE5+G6nzWcqUsTiufVepovFq712urair5lKUruL46L6HflE6OtpXXxIrKWfVE360up21qjBPMUzO8P5V9DiniTEXi4HP8TFncebGxwt+tjLjkaGfmxlk8/DMLu9mLgZebA82TG98C66AZeYXd+5hdX2GLjQzTxkl20jEJ4WAMk3dMt79DC+V3LgoyX5dyXtzYmwAsr4syr0J6kXXkC59MF4MWy8q5A4sVb45Je6wR42Ay59QtjFO5Sg8PqW5jyHncDLzfUdckT7+pLu/9AMm7WK2YFuAXUP0Mb3WS8gGxcj3DAXxa5Mp7kbdg98AJMxb6iTyY7oIwm8M4tNmo2Z1Hgw0v5pP1OkbCGxyp9ThgcvByrCX/APTBdI3OdNq5wK0tU3vaCX3ZzLYKz4KtjG9lkywsEBsy6K5iE8q4HKpWOSMllM690jlcZRinOLintKXyr6vAHZo1pUXdPzR5V8o2FGvGqrxePXc1EakIKm3Vp/PiCUrufbj329TpVPG6NPUulRpVHUUvLLzNRSfra4mNqbj1OHuU89Lx2o9fU0caflcH5XOK80vN6Rz/AJ9Dr0Kfjeo1EJ6qvKWnTu1fyRkv/HF/fBfE29NVr0aMb1qsKa6ydjorx3QTqSp0JyquCcpOMWkkv82RrqPhupqfH/1taMoVmm6d3LCeFeytb06HPp/CtFpoyjTpv515ZXk8rldVfuNQ3UrePVpaaNXw/Sqtdu8ZJua9bJ7epjT1fjWo0c5+SNOtJ2hDyqKti9vNztu+y5XcpafT0LfBoUoW5jBX+u5y9y+jTTanwivrqkJaqcqTslJ/EUk/ZL97HMvBqbqU3U1FS1KCjFU0oP8Af9PqbMDdNOKjQpUbNQU5radT5pI5ovy38qUU9/Li/wBAkLdSBusBbl9jY+G+EV9c1N/7dHmbWX2Em+i2Tt0aFGrXqxp0oOU28JHpvDfAadBqrq7Vaiyo/wAMf6my0WioaKl8OhCy5fL7nYNscNdscs7RIAHbgAAAAAAAAAAAAAAAAAAAAAAAB0fGNBHxDQzpeWLmk3DzbX6HzrUVnodZ5lD4MPN8LUQWEuj9tu1up9TPLfivwVVIy8QoQ80lHy14Jfmj/N3XPVdkcZ479u8MtemhkuVycUldM4NDVdOp/oasrtR81Gb/AIo9O6O1NGFbxotdRdGrZX8ryjgizc6uh8ei4K3mWYvozR/lbUsNbiDkvYqd2YXMlsUHujLBj2F7YAyTad17E3C5uyXxsBUxZXuTA5VgMr+oTxjJOwQFW/A53Mb/AGLcC3uCXHAGSwS93ew5ywmBeNvYi5ZL4GeAMr7kvsyB8AZYC2IRAXYbkvaSv0HAGXdE5uS++UVJPcAn9SsxuH6AV4JyR75GQF874XAbGSBBkbKTgKj4MXZIyMHhCI46jsmY6XnuKn5WXSK8fU6R3YvByp88Clpa85eVU2nbzPzYsuueC1FSpzhTlVU5zdoxorzt/ovuc6Xbjg09RVa9P0ObzXicSraehSrah0Z1YwmotKorOTva9lhYeU39yVtVW1FOk/DKdGNWV/PSaUmulnJF0m3ap05T+WnGU30irioo0r/Fq0qbW6nNJ/Tc44afxeeiUfjzp15z+fzVLJRt0X5c8LO9+Dnl4R/qKdOGunSn8NWi4edytfq2vumNQ2nm0i0S1T1LlT8/kThTeX6Xt+g1uqrKMV4Z4XCpdK8m3NxfRrzM5/8A4nRqjChas6UX5vK6n5nxey4OxptPR0sPLQpqK2b5fd8j0e3Qqy8XjptOqFNwqzTdbyU1FrpnCivTc4K/gktbqJaivL/TuTu6d1NP6f1N23cDyNOhPwjT1a8a1StW80IxgvLaLaXLlm7+nC4OzS0tCk01TUpR2nU+eS92c3Yv3G6uoQ+SHljaMd7RwvoO4G5FVbAhUwKs4LZEuXkByVq4tctrhE9DkpUaleoqdKDnN7JI73h3hFfWtTa+HR/na37Lk9RotDQ0VPy0IWfMnu/c7xwtc5ZyNZ4b4DCnaprLTnxD+Fd+pu0rJJbIoNZJGNtvYACoAAAAAAAAAAAAAAAAAAAAAAAAAAAHlZAA+e/ivwX/AEdaNSj/ALdCUvNRml/wz6dun04R0tHq1qqcozShqKb8tWn/ACv+h9I1mlo63S1NPqI+anUVmtn3XqfLfFdJW8N8Sm4Xeo07aa2Van1/z+hlni2wy36d6SNR4lQ8lT40dpfm9H1NtRq09TQjWotOElcwq041KcoTXytWZk0efvkt0WrTdGrKEstP6+pisIqL6lMG8Gd8AB3F7ECqm7C+URbFyAv0FxgLcCyWLEWwe+4XYC8jhkRJbMDLkJ52JzgJu7sBWLk47DgIqtkPdEWW7LZh3sFVbZKrW4IscjsEOdxixG7EAyG3qycEuBkODEt89WA9i7bkV3hI5I0atRfJSnJc+WLYHH7i5z0dJOpTnP4lKMaavO80/L3texx6qFPTaeNdOpXjK9nTg0l3b4GkYbkuuTOnO3hq1X+mUpyq+SMZSePZfm9drepnW0mq13w3pnU01opTUouCb5af9i6NsIUalRNwg7Rtd2wr7ZMJz0uml5NSpymv4E3Fr7Hdn4RWnp9PRdZQVO8pvL8zduObZy856JHZXhOnnL4mq/3prHmt5X9i+oe3UWo0VKpCjW0VJTnHzJLzPyp7Ntys+y/XBwTreIVa6oaWjFUVK3n0+IyV98Pyr3N1/pNKp+d0KblZJNxTslslfY5tsIbNNXDTap6mtCnSpw0rj5Ve0W8fmvlt32b9HZF0fgtHT6j/AFHxak5q7SlZ2ffnsbS+AtybNOnQ8L0tGSk4OpK/m/3Mq/bY7qb8qinZLZLCJgck2pYMAB2FhcWCquoeQEAGBfgZtkC8i9yFuAyyrAv0FyDJF5wYXZzaKn/qddR06a+eaTaey5KjKhQq16ip0YOc3wj0fh3gNKjapq7VKn8v8K/qbPSaKhpKfkowt1fL7nYNscNdscs7USSVlsUA7cAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHkfxJof9Tqpum1CtF+anJ9sp+jPXGi8ajbWRfEoI4z6d4dvncKr8O1Eqnla01SVqtN70p9e3+dDbNJq6d08prk5/GdC6kZaijDzS8tqsP8A/SP9UaTQ1/8ASVI6WrO+nq5oVHlK/wDCzKxtHJ4lp/iU/iQV5Q+6NStz0ko2x0NJrqHwK+F8ksp9PQ5hXXHGSb2HBRldFvyYX2LfgCpl3MVuV2Cra3RBPJGxcCu6vjIvfcjYzYCr1F7ET6/Ujd+AjK45MVkvNgLLbBbu+TH9S3wAxd2D6LqRW3MqVKrWxRpznbfyJuwNoty49jmho60puF6cZWu1KpFNLq1e6RJ09NQoKvW1XmpuXlTox893vbNi6NuG12Dm0dfQ6j4rhp6rjSg5uU6qj5rLZJLH1JQ1f+p0z/0Sp6fUea0U4fEurdbNp9i6TZDT154jSm2/QtXTToLzajy0o9ZyWfZZOTR6bxXy16moqVFWcWqUviflb6Lj1dr9DLS+F6mNB0dTOhKnKXmlFpzu85vjOX9RqDq0paWp55PUTcKeZzhTbil0y07+zM5VaD0fxtHp5VZ+byunWleVs5Si1df1NjR8J0dGjUpxjK1RWlaT25XXJ2Kek01Oyp6alG23yq/1Ho9tTpK3iL09atT00aVRWVOmqOL875bW+9lzwcs9Dq9fQUNfB/Evf4vxYp9rJPHpg3L6XIibNNZR8GjQ0dXTQ1NTy1WvP8q2TT+t0s/Y7Wk0dPRy81OdWUrWvKf+I7N7MjuN00ycnLMpNv1ZFsLdwRQW6F8xH6A0cAjvwVIKIX6B3HAD9QTfBfoEO4ACm/JSe2QgLgE9iSlGP5pJd2Bl3C7HBLUQ480n6IxdarK3kgl3yQdm5jKpCP5pJe51vLVmm51cdEWNKC3Vxscj1MErxTl9jD41WVvLHyp+5kklskjJX6WG1YfDnJ3qVG10ubn8M0l/8vSslaKk3fsarN/Q9B+Ead9ZWqcRp226v+xcfdjnP1jXrAAel5gAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAANR47D5qMu6Nua3xuN9PCWMS/Y5y6dY9tEzz/AIx4bTh55eT/AOvWfzW//OfXs39+56J7nHOEKkZQnFShJWknlNGLZ5fw/VTlJ6LVP/7FJfLJ/wD6R69zm1VBVqMoS3ez6M4PFtBUo1YfCb+LS+ahN7zj/K/Vff3Zz6TVQ1unVWGJLE4/yvoSz7WNFOLhNxkmmnZkNl4np7r48OPzduprMu+SwV2wgR49S3bGkE7Nleepinko0pfnAvkJ4J7AW5VsQL0ILcPY5qelqSj55+WEGvN5pO2Otld29iL4Eqc5UHU1U4fww+VPtu5fRFRwrbODkjSnKHnfljC9vPOSivqxpJa+trIU4aOdGF/nag07f+TyvY5Knhvieurt6uSVJO0VUm4u3DsrtPvkujbGMdP8KVapqoqnB+VuMW7vour7CtKlToKvpqVTUwk3G6bVrJbpLbPU2z8H0XwadGcHOFK7ivM8N7/Ng7EdNp42tQg2tnJeZr3eSej20VCpqHoXqIaSn551FTpw8jdv+zvdvsnjdnbr6PWavSU6Gp83ni/N8aLjZKy+W2Hg27vfLfuO9xs01Gn8EVLRV6Eq93XteXw7tWfff3OfTeFUqEPh/HrTg94Py+V+1mbBvYE8qunBDR6WnTlCGmoqMreZeRWl3RzLEfIsR/lWxWS3VkD2BexCgELjbgB3Yz1HbYAPYLITFwaHvyH6ABTclsl2Vw3YAB3D2wAuARvGWBQcfxqa2fm7ZMXVlZeSn7yf7EHMG0ll29Trt1JL5ptekVYnw4t33fV5GxyuvTTaTcn6Iwdab/JTt3ZYpC1xscbVWWJ1GvSOCqlCOWrsz7FeUQRJdEvYq/UbIrT8vAVi8clHG5UsegCyuZES6ls1yBV3PU/hGnbT6irb80lH6L+55ePqey/DMPJ4TGWPnnKX7fsd8f8ATjkv+W3AB6HnAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA6fi0fNoZ+ln9zuHBrY+fR1l/0ZL0s7eZa6mLM3kxe5g3dfV6aGqoOlO6d7xkt4vho8pqYVtDrp14wtVh//AEUltOP8y/X69GeyOl4lolq6SlTsq9PMG+esX6P7MK1inS1FCNWk1KnNY/oaLV0Hp6zjnyvMX6Hao1P/AI+u201pasvLOLx8Kfr0/wA9+5rdPHU0H5ctK8WToaK/UJ3DTi7NWadmS9+DpGRG+hL5LbIDdBu2BfBPUDI7Gi0r1NSW6pU05VJL+Ff5g62WbvwzRuVF0ql1SVp1rYu/4Yfu+5KrPSUpzpyrt/CdZWhi/lhxZf5wTT+E6HTRsqPnfWcm/tsd+TvJu3ZdBwQ0wjGMI+WMYxXRKyMsdhyEvQGk3YKln0C9AqWwVdhfoLWeCAsq4sh6uzBQtknJeLhW3IIPcAACdiq4AAdwG7HO4BUBuYuSW8kjFzWUl77BWebl2Ov5m7ty72HlV83k/V3JscrqwX8V+2SOs2vlg7dXg40naxbK1nlgPNNrM7f+KMVFXu02/UyysWLuiCWt+VF9B7Fa9QJYqVti2eSZ6ALZLbAxYWAiQz7FtnkqXXewEsxbBYrcoEsVLoVJBZ2TCiSFuHuXPuW2MgIrNuT3fhFP4fhWnj/0T265PCxTckllvofQ6EPh0YQ/lika8XbLlrkABsxAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAkl5otdVYoYHlJJq6MLHPqY+XU1Y9Jv9ThZhXonSWMTLghyrUeL6FTUtTTp+dNWrU7fnj1t1X3XZGl0eoloq/wDo6rjPT1s0Kry16X/zf1PY8+p5vxrwyEE7K2mqS4//ACnw16N/5ko1/iml8kvjQWG7S79f8/c11zdaWp8alPS6pJ1oK01t5o/zL/MM1OooyoVpU5Zts+q6hHHfAbJcuShfr9gTl8mUYuU1GKbbdkurA7OhoOU3UlG8YW8sX/HJuyX1/Q9HCHwaSoqTlZtzl/NN7v8AzodbQw8lS0WnT0t4U78zdvM/b9WjtcnKpb0GEs7lIRU5u8F9QNwHJHsLZABYyX2J7B9AAZL+gvYBfoOCXzkXArHBg5JbtL3sYOtHZO76AcvAwuTrurLhK31MHKct2wOzKcVuzD4y4ycKysXKkByfFb4EpN7swSV8mVsARLBbcq5Xv2ADkO3UrYAJdCpYYttx1LbJBjbkLcy7MJAOMguyIlfn2AW6jbcuw7gByVr/ABC1lgBd7jFy29AvRATObMqwC7hUSyX9QuLlAJ+iDHqEgOfQQ+LrqELX81SKt7n0E8T4DTU/GKG2G5P2R7Y24p6YcvYADVmAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYAHnvEo+XX1PWz+yOozZeMxtq4y6w/dmtZjl23x6RmPYyIcKx24MakI1KcqdSKlCStJPZozZGFeY8T0FWjVgqU2q1NOWnqP+OPMJev/AL6nVqeXxHSOcYOGopYnB7p8o9ZqaFPU0nTq3s8pp5i+GvVHm9Rpqul8QdVwTqeW04xWKsP5o+q6f2ve0aPAZ3fEtPGnKNai70qmzW1zpFEXNjueH05/EdaGHB2j/wCT2+m51acJ1KkYQj5pyaUUt2zd6ahFU6dDDU07NPeKdpy93aK9LgdrQRfwFL+B/wDGnv5d/N3k239Ds8ZGFi232J3OKq43IGL9AosAl87kvkCv0BxzrQj+aUe25xPUxb+WDfq8IDsN59SX3udd1asnZeWP3Zg1N/mk5P1YHYdWEMeZdkzB14vZNnEor0Mkr7oDKVZ2ukv1MHKo/wCJ+2C+XceXOCDG3e/IUbu5msBLIGP5bepkk7lSztctvQCWROxnFK+SNZsigkGv8uZKzZLf3AYRbYIsbF3tcAlZ3+wx9TL2f9SWzlkEs+C7ZbHYIAl7lt0WLFtfhEzbYBYWMiLAEyVLqMtFtzyBLFuHhsZCiWLixeOosAxyHl9BYq97gLdRxgZ4RfpgBv7DYvqNtgN1+F6d/EJzzaNN/dnrDzv4Tp/LqajvvGKf1Z6I9HH/AC8+d/0AA7cAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAANV45H/hn3X6f0NTI3fjMPNpFJfwyT/b9zSy2Ms+2uHTAWAOHaELgliCM4NXp6eppOnUvjMZRw4vqn1OceoHm9TpnScqGpS8lV4mlaMvVdJcte/U0FalKjVlTqfmi7M93rKH+o0s6Std2auaXX+GVJyhUU1KcE0vOlJ+93m3TJZVanwyhGdWVSrLyQhFty/lSXzS7rC7yRu9LGTi69SDhOra0Gv+OC/LH2X3bOvRoVFUXxfhRhGSdRQp+RSSzGNtkvNdvrg7M9RBO93J+iFo5rkuup1nqJP8sfrn7GHnqSy5P2wcq7M5qDzJR7s4nqYr8qlL2t+pxKCs/uZeW36EB1qkl8to/cwtKS+eUpejZyeX0v7i3TYDBQVnYyUfQySxd/QcKy7AY4tgJdTJp9iqL4AxUbcr3Q/RFtuzJJ2utyjC31LayyVW5GX3IJYJMqWMi1ltn1QEtngyCT6i23oAsrY+wytty3LbOPYow8qTZbX2ZbfUegCwsy4Sx+geVjgBxn6Evdl4HFgKlZWFklcclW9yCLLHOC9mLZAZTs07kSt7mSV8EwAeO43yVegsAt6DCsC2bCpu9ipZuTuZcdAI+wS9CvoG772AnYqF/QubXSIF1fG4v0IlYytfYo9b+F6fl8MlO2Z1GzcHQ8Eh5PCaCtunL6u53z04zUeXLsAB0gAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADreIx82hq+kb/AEPOu56fUx82mqx6wa+x5m/ymebTjYtEKQzaI9iFJbginAGwCIazxGMXUtKK67G02Oh4grTTtuiVY1s43tfZbLhGPk9Dme2Sc7EVx+XGTJRxsi+wst+QqWfCFvQyS5ZGgiJFSxfoVbIlr+uQI93f6BXSusPr0KrtXL2AnPcNJrcqsvUc4QES+wRVdsL1AWdg7pleME7gTgLsXLwL5aasAT9Sy8tlZPbN3z6C32CxsBM7ocFwi7vOAIrpfoLNZFs3uUonctsYFlyPVkALPUtuQBLdBd3Ly77DdbAEXe6CGzAq90LY/YfUY5QEL6FMXuFi2XQewAFukONyXwX6gLPsFYt7rqS+wFe7CEUr5LYgnYqG+xzaeCq6mnT/AJ5qP1YhXudJD4eko03e8YRWexzAHseQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAEkrpo8u1a66Ox6l7HmdRHy6qtH/u/1M83eHbiZGVkM2qWIy9SP0IFiMvBN2A2Onr/yx/U7nB1NffyR5JVjXtJPYxtdmWwaI6Rr6kW1jK3H7EaAjusLnkNPiw9QrttcdbgRdy2vvf6iyfsXYCW4JZW3K8bW9y7IDHuXncq6boJdQiJu6Ytn9C/4iPABeotyVLNxgBi/qOSdH9ipNoBnoPsNnwVrIEtfoVKzx9BbO45xuBM5sHngyXa4tdAYxi7c2RRboOACSLsPS42YBbltuyZKgDT7DcrtlBfbewEwN+xb5sQC39QlgexbhUtb1D+gS6sZAqHcdisCPcW6C9+xV2IJtZ2LuULG3IEtg7/g1NVPFdOrbSv9M/sdFZ7m3/DcPN4nf+Sm3t7fudY9xzn09aAD1PMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAee8Sj5dfVXWz+x6E0njMbayMv5oHGfTrDtr2R4LyQybJuLAXwRWIKTsBVg6utX+2n69Ts77HBq03SJSNbdLZq+xi7f4ive1iLOWR0Y9CWtsZW+hPb2AnFrla6DLwgk7Xe3cCXz6BdS/r2C9gJ6/cu6Vi2z6k2Axs7lsr9C2sulhkIizx9hkW2HCsA9i2+osP0APHTsFj1AzwAs+Q9rFVg/VgBbKsXklv8AOoBoSTW+5VdegwBE7LCFsplttclrgXPAshZ9Q83AWTd7jHVFS6F33AxW939S3GOMBb5uBLC2Ha5fT6BdAGbLJXl4HoLLcimVuL/QvO42fUBsN1a2xMIvGQGENsIJ5Lf6gROzZeSdy2sttwDfqeg/ClO9TUVeiUTz+56n8L0/Loak7ZlP9Eacf9OOT+W7AB6HnAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAANT45HNGWeV+htjXeNRvpYyziaOcunWPbSvcj7lIYtzggBBOQO42AI4dUv9r3OZHHqM0pAjUy3f6kdrZRk922T0OXSO9v8yNhbNwr5AnsXkr6hZWwEW1mxja5bZzgPCt+gEtwiWsZcdyXS9bALejFs3yLlyBi1d+gtje3GB0eO5kBH64JyV22W3VCyAc5Hsw7phZSAP2LbGCeW3UJcAEV2IkAF7q+9y2ds/oN/wCxbYCJzjNhfOBbf1L9LgRlSzkewV/YBn0LFK+SIuObBTF+WBf6C3qEOc3JbGS7D0Ci7l9+xOhXewDiw4HcK1svBAewe1/sLlAiLaxC5wBbEsMjIFt1PZeAw8vhVL/td/c8ass93oIfD0FCGcU479jXi7Z8t9OwADdgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAdTxSPm0NTGVZ/c7Zw6uPn0tVb/IyXpZ280QvJDzvQjTKCMBYj3KAJbJjU/wCOXYyuY1PyPsCNS8SI02ZVL3eX0MLdjl0ivcNcF37BMBbuE83YvhclSAxbsLsy8uNsk/UmwWchr1LvbJH2yUBZoueF+45xcgls73Gz6iy5CAbWFkuRxsy7YAjSsLFfqTa24BY3C3BUNidirsA/TgoX3sRplQtn+4Dn0G2wsr7lVyCWWdxa77jNy8AMWHOw5HogK+pChlEvm1gi8BXQAj7lS67DFyCMrfLGzHYAlbkWd+BZ7orAb75G2w/UqQCxNlgBAZ0lKpVhBLMmkj6BFKMVFbJWPD+Fw8/iWmjh/Onn0Pcm/FGPL2AA1ZAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAEkk4tPZlDA8pJNNp7ojOzr6Lo6qcbWTfmXZnWR5729Eu4cYJyUepFTgBgCEeVb0MmR7AjU1PzNdDBqPmdsnLqF/uys+Tixfc5dG1uSWWcF/M8Cz2/YgZu7DbohssjfYA7J4z3CXRMJc2Lny8gHhWsY7PZsyXVk9+xQvsMr/ADcWxncW34ICSQZUuqwF9PYCemBs9y2GL8vqBLNsC3V4Li1kBLYK/QBW5Aiu29g1ktvUNYsA34D3F8i+coBbOORtjgt+CbZuAbyVJWRMDcBa6DeLIuc2CVt9wDxuH1LwTfIDjoPYD3AC3cZWL4HsBUM8bhehMpXAq3JkbsbAZbcE9GHkAXsNiK4uBtfw9HzeKQf8sW/sevPM/hWn5tRXqdIKP1f9j0x6OP8Al5+T+gAGjgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAa/wAXoeegqqWae/Y0Z6qcVKDi1dNWaPM6mk6FedOX8LsZZz7aYX6cZGUGbVAXgjAc4I1kpGBrdRdVH1OF7nPqv+Z72OB/c5dJnGS5ttcWa7C3HBAW2wWMIdxZNYAWaze9x3Kk9h2QEsrdQVbu43dtwHbcbv8AsAr5Al0OS/QLKe/0Aj63DWBnYvtgCW6/qM9Ui3fcewDPoRWLiwtbjcArbjjORa4byBHgJdUX/MBbZAl7lx/6CwM/3AnYy449ie4APKG/JFv0MtluBLtcAWvkXyAfoNx9BwAza/Is+uC5GQIt97FfYl8XFwqoPJAgKlvaxVnknoCChWsQdij0/wCFaaWmr1Lfmko37L+5vTV/h2moeEwf80m/vb9jaHqwmsY8ud/1QAHTkAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADVeM6e8Y147r5ZdjamFanGrSlTltJWJZuLLqvLMGdam6VWVOW8XZmB57NPRKg7FJYKB7dBshklGv1a/wBxnX2eWdrWL5+51bJtf1JVTkt3sPQbMgPPD9w7vnuxjGclf1AnHT3CT6lW1x6YAe9ybZCxjYdgHFh3dhcALrgZ4HUegDnbAd3t9ir2Ik7WwAs8scWsVu5NrgW2EuTFFun69w116AFkYvYcF9wI1gv6EF/oAvj0DXNxxkY6gTF7oqGS26oCLI7FXYgFxfJOblt9ByAG+B9CPNgHNh9RZ7F23AlrFZe5Odgog+4IQXPBSLHIe4C7asLdQvQyim2kst4KPb+FU/h+G6eOfyJu/rk7ZhSgoUoQX8MUjM9c6eOgAKAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAANP4zQtKNeKw8S/Y1Z6fU0Y16E6cv4lj0Z5mUXGTjJWknZoxznvbbjvrSEFrFOGiDgbDglHT1l/MjqWVzu6vZM6fdnNVi9ljJLddzK+2CK+4UtbIfSz2Ks9LhpbL7sIl8KSaaLl8epVltu7J7AHsTbgrxa6J/CAvdYf0HVIvoTn+oDO2LIPYvZktj16AMve1+4fQvHoOwEt12CbLb/ERgL25/sVoZtnIxyAwsBWXDYfp/6JxuBcPf6E4RVbYi9AI8rOxeNxvguwVGnexeCWzctyCdQG3jBb/RlBh9R2IubgMhixccABchVuQRfcrfuRLOSt4wAVhcemBxcCFQWdwygjseHwVTX0Iu1nUjv3OulybHwGDn4tR6RvL7Fx7c5X09mAD1vKAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAaTxfTeSt8aO09+5uzg1lFV9PKHO67nOU3Fxuq80SxlJWk08NEMHoTgpASq6+sX+2dF4O/q1/t3Oi8bMlVM32I+3uVrkmCKO1i27E2uvsWz4ANEwsci3cWV73CKuiJZF3sFuFR5bYsW/pe5LYAcC90LckWHb9Ai5dtroXx6B+qshzuBU+uDG5e6Ct2Ag5wi5tgm2WwHsS+S8DkKrtjIxYhff7gQfoV9dw9gHUdLEsN+xBbi6va+OAr8C1mAwEnkXIAHGLjbCCtyBWsfuS2B2HuUWwvbPUiYVsAONinJQ01fUz8tCnOo/8AqsI2+l/DmoqNPU1I0l0WX/Q6mNvTm5yNGtzsafRanVP/AGKM5rrx9T1mm8E0Ons/hfEkuZ5+2xsIpRSSSSXQ0nF+s7y/jzWl/DdSVnqqygv5YZf1Nzo/DNLon5qMPntbzyd2d0GkwkZ3K0AB05AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGi8VofC1DqRXyzz7nQV7novEaPxtJJJfNHKPOsxzmq2wu4EQKZ1o4dSr0zoWtfexsNQr02a9vJKsY5HX1HHcS+pyosXz9RzsvoEumCgRc7jkt8k55QDbYZa3YWwx1CCDd2iJWRbXxYKisOeoa9Sp2uURvHqOcBWHYBxyW3JOwWSBe2/JHsGXCW4EW/YvoFexHdPOwD7gXyXgCdg9uotYMArjd8E33KBbj3IrC91YoZWAF3C3AbYHsWEZ1JKMIOUnskr3NnpfAdbqGpVIqjHrPf6FmNvTm5SNXcypUqtaXlpU5zk+Iq56vSfh7R0bOs5VpeuF9DaUqNOjHy0oRhHpFWNJxfrO8v48rpfw7q6tnWlGjF9cv6G50vgOioJOcHWkuZ7fQ2oNZhIzudrGEI04KMIqMVskrJGQB05AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA854hQ+BqZRS+WXzR7HozoeLUPi6bzpfNTz7cnOc3HWF1Wi4IVkMHoYVlelI18ou9jZVM05djXSaTdzmrGDwA+r+4+5ypa+97Md7i9+5OVwBeRuVbXI3nHAB+hODLe5MsCO6XccYe4DtuBNsXH3KtmWy/qBjt0Kn7MJWQeNr2APbG4xbe5N/3Dy7tgG772DxyFuLfUBfoS+e4KvuAtnIzgmXsN9gHuwwkiclF5CF84O9pfCNdqcxoOMX/ABT+VFmNqWydui/UJNysk3fZLk9LpfwzTjaWrqym+Ywwvr/6NxptDpdKv/r0YQbVm0sv33NJxX7ZXln08lpvBNfqLP4LpR/mqY+25udL+GtNCz1M51X0Xyo3gNJhI4udrhoaajp4eWhTjBeiOYA7cAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAARpNWaumUAeZ1dB6fUTp8LbscBu/GKHmpRrRWYYfY0rRhlNVvhdxhV/wCOXY18t/c2UleD7GvkrNpGdaRx8DOC4vhBHKsUv8uNx2Li+wEHPoXKwgvUCZX9R3L3JvwAt9wN0EwIgV/YLGbgTj0LyOCPYAyZ3Llj0KJzkPKwOSegFGOhLo59LotTqmlp6M5pv81sfXYsm0tkcN+DHg9BpfwzVdpaquo/9aeX9X/Q3Gl8J0Wms4UIuS/in8z+5pOO1neSTp5LS+HazWZoUZOP8zwvqzcaX8M2fm1de/8A1p/1f9D0Vinc45Gd5LXU03huj0tvg0Ipr+J5f1Z20rAGmnAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADGcFODhJXTVmeYr0pUa0qct4ux6k1PjNDEa8V/1l+xxnNx3hdVqOGjoVMNr1NgtzoVVao1fk89eiOLf+oxYtnfjYxOVCW7l5uACsH0L3wRbACWuhsHtvYAOAuwtf1AXxay7k78dWV85JjkobsN3ZMX6nJSoVq8/LRpzm+kU3YaS1xrrgcm50v4e1dazryjQh//ANS+ix9zb6XwHQ0LOcHWl1qO6+mxpOO1xeSR5Ojpq+olahRnUf8A1W3ubfS/hrUVLS1NSNJfyx+Z/wBP1PTxhGMVGMUktklYyNJxz7Z3kta7SeC6HS5VL4kv5qnzfbY2CilwUHetM7dgAKAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAcdelGtRlTltJWOQAeWnCUKkoSWYuzNfXVps9B4xp/JVjXisTxLuaDU/8j7nlzmq9OF3HBtdEexXyY2zyjh2exfW4FsZAlvQrwsh8E2KG4HsEm5JRi23skssaTZssETNlpvBNbXs5QVKPWb/Y2+m/Dump2lXnKtK+35V/X7nc47XF5JHmIQnVqeSnCU5PZRyzY6bwDW17Ooo0YvmTz9F/Y9VR09GhDy0aUILpFWOWxpOOTtneS/TUab8PaOlZ1fNWl/2dl9EbSlSp0oKFOEYRWyirIzBpJJ04tt7AAVAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHDq6C1GnnTe7WO55HWRam08Pk9oeb8f0/w63xUvlmr+/Jlyz1tpx33ppGui5GLFTV8jdmDdjlrYbbnd0/hes1DThRcY/zSwjbab8O01Z6itKXWMcI6mFrm5yPOJXeLtne0vhGt1Nmqfw4/zTxf9z1Wm0Wm0qXwaMYvrbP1Owazi/Wd5b9NHp/w5RjaWoqyqP8AlirL+v6G20+loaZWo0oQ7LP1OYGkxk6Z229gAKgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAdbXaSOs07pyduU7bHZBLNjQaf8Orz+bU1tuIc+7Nrp/D9Lps0qUVL+Z5Z2gSYyLcreywAOkAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAH/9k=',
                    'base64',
                ),
            );
        },
    })
    readonly imageField: Promise<BinaryStream | null>;

    @decorators.bulkMutation({
        isPublished: true,
    })
    static async export(_context: Context, product: ShowCaseProduct): Promise<void> {
        // eslint-disable-next-line no-console
        console.log('ShowCaseProduct => bulkMutation', await product.product);
    }

    @decorators.bulkMutation<typeof ShowCaseProduct, 'testBulkMutation'>({
        isPublished: true,
    })
    static async testBulkMutation(_context: Context, product: ShowCaseProduct): Promise<void> {
        await sleepMillis(100);
        this.emitter.emit('productBulkMutation', await product.$.get('product'));
        if (await _context.batch.isStopRequested()) {
            await _context.batch.confirmStop();
        }
    }

    @decorators.bulkMutation<typeof ShowCaseProduct, 'addPrefixToProductName'>({
        isPublished: true,
        parameters: [
            {
                name: 'prefix',
                type: 'string',
            },
        ],
    })
    static async addPrefixToProductName(_context: Context, product: ShowCaseProduct, prefix: string): Promise<void> {
        const currentName = await product.product;
        await product.$.set({ product: `${prefix} ${currentName}` });
        await product.$.save();
    }

    static emitter = new EventEmitter();
}

export async function sleepMillis(ms: number): Promise<void> {
    await promisify(setTimeout)(ms);
}
