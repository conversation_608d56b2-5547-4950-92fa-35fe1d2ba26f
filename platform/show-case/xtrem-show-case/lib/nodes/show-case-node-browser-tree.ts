import { decorators, Node } from '@sage/xtrem-core';

@decorators.node<ShowCaseNodeBrowserTree>({
    storage: 'sql',
    canCreate: true,
    canDelete: true,
    canRead: true,
    canSearch: true,
    canUpdate: true,
    isPublished: true,
})
export class ShowCaseNodeBrowserTree extends Node {
    @decorators.jsonProperty<ShowCaseNodeBrowserTree, 'checkedItems'>({
        isStored: true,
        isPublished: true,
    })
    readonly checkedItems: Promise<any | null>;
}
