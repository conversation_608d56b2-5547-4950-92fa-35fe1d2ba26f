import { decorators, Node, Time } from '@sage/xtrem-core';

@decorators.node<ShowCaseTime>({
    storage: 'sql',
    canCreate: true,
    canDelete: true,
    canRead: true,
    canSearch: true,
    canUpdate: true,
    isPublished: true,
    isSetupNode: true,
    indexes: [{ orderBy: { startTime: 1, endTime: 1 }, isUnique: true, isNaturalKey: true }],
})
export class ShowCaseTime extends Node {
    @decorators.timeProperty<ShowCaseTime, 'startTime'>({
        isStored: true,
        isPublished: true,
    })
    readonly startTime: Promise<Time | null>;

    @decorators.timeProperty<ShowCaseTime, 'endTime'>({
        isStored: true,
        isPublished: true,
    })
    readonly endTime: Promise<Time | null>;
}
