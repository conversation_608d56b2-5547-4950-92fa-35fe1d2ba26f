import { Collection, Node, Reference, StringDataType, decorators } from '@sage/xtrem-core';
import { ShowCaseComponent } from './show-case-component';
import { ShowCaseItem } from './show-case-item';

@decorators.node<ShowCaseBillOfMaterial>({
    storage: 'sql',
    canCreate: true,
    canDelete: true,
    canRead: true,
    canSearch: true,
    canUpdate: true,
    isPublished: true,
    indexes: [{ orderBy: { item: +1, site: +1 }, isUnique: true, isNaturalKey: true }],
})
export class ShowCaseBillOfMaterial extends Node {
    @decorators.referenceProperty<ShowCaseBillOfMaterial, 'item'>({
        isStored: true,
        isPublished: true,
        node: () => ShowCaseItem,
        ignoreIsActive: true,
        lookupAccess: true,
        duplicateRequiresPrompt: true,
    })
    readonly item: Reference<ShowCaseItem>;

    @decorators.stringProperty<ShowCaseBillOfMaterial, 'site'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 100 }),
    })
    readonly site: Promise<string>;

    @decorators.collectionProperty<ShowCaseBillOfMaterial, 'components'>({
        isPublished: true,
        lookupAccess: true,
        isVital: true,
        node: () => ShowCaseComponent,
        reverseReference: 'billOfMaterial',
    })
    readonly components: Collection<ShowCaseComponent>;
}
