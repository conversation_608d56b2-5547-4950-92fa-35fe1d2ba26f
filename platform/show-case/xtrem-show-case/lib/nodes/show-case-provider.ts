import {
    BinaryStream,
    Collection,
    Context,
    date,
    decimal,
    decorators,
    integer,
    Node,
    Reference,
    TextStream,
    TextStreamDataType,
} from '@sage/xtrem-core';
import * as xtremShowCase from '../../index';

/**
 * Created with X3 Etna Studio at 2019-07-01T09:12:48.916Z
 */
@decorators.node<ShowCaseProvider>({
    storage: 'sql',
    canCreate: true,
    canDelete: true,
    canDeleteMany: true,
    canDuplicate: true,
    canRead: true,
    canSearch: true,
    canUpdate: true,
    indexes: [{ orderBy: { textField: 1 }, isUnique: true }],
    isPublished: true,
    isCustomizable: true,
})
export class ShowCaseProvider extends Node {
    @decorators.stringProperty<ShowCaseProvider, 'textField'>({
        dataType: () => xtremShowCase.dataTypes.descriptionDataType,
        isStored: true,
        isPublished: true,
        lookupAccess: true,
    })
    readonly textField: Promise<string>;

    @decorators.integerProperty<ShowCaseProvider, 'integerField'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
    })
    readonly integerField: Promise<integer>;

    @decorators.decimalProperty<ShowCaseProvider, 'decimalField'>({
        dataType: () => xtremShowCase.dataTypes.defaultDecimalDataType,
        isStored: true,
        isPublished: true,
        lookupAccess: true,
    })
    readonly decimalField: Promise<decimal>;

    @decorators.booleanProperty<ShowCaseProvider, 'booleanField'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
    })
    readonly booleanField: Promise<boolean>;

    @decorators.dateProperty<ShowCaseProvider, 'dateField'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
    })
    readonly dateField: Promise<date>;

    @decorators.collectionProperty<ShowCaseProvider, 'products'>({
        isPublished: true,
        isVital: true,
        lookupAccess: true,
        forceFullSave: true,
        node: () => xtremShowCase.nodes.ShowCaseProduct,
        reverseReference: 'provider',
    })
    readonly products: Collection<xtremShowCase.nodes.ShowCaseProduct>;

    @decorators.textStreamProperty<ShowCaseProvider, 'document'>({
        dataType: () =>
            new TextStreamDataType({
                maxLength: 1000000,
                allowedContentTypes: ['text/html', 'application/json', 'text/plain'],
            }),
        isPublished: true,
        isStored: true,
    })
    readonly document: Promise<TextStream>;

    @decorators.binaryStreamProperty<ShowCaseProvider, 'logo'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly logo: Promise<BinaryStream | null>;

    @decorators.referenceProperty<ShowCaseProvider, 'siteAddress'>({
        isPublished: true,
        isNullable: true,
        isStored: true,
        node: () => xtremShowCase.nodes.ShowCaseProviderAddress,
    })
    readonly siteAddress: Reference<xtremShowCase.nodes.ShowCaseProviderAddress | null>;

    @decorators.stringProperty<ShowCaseProvider, 'concatenatedAddress'>({
        async computeValue() {
            if (!(await this.siteAddress)) {
                return '';
            }
            const address: string[] = [];
            if (await (await this.siteAddress)!.addressLine1) {
                address.push(await (await this.siteAddress)!.addressLine1);
            }
            if (await (await this.siteAddress)!.addressLine2) {
                address.push(await (await this.siteAddress)!.addressLine2);
            }
            if (await (await this.siteAddress)!.city) {
                address.push(await (await this.siteAddress)!.city);
            }
            if (await (await this.siteAddress)!.zip) {
                address.push(await (await this.siteAddress)!.zip);
            }
            if (await (await (await this.siteAddress)!.country)?.name) {
                address.push(await (await (await this.siteAddress)!.country).name);
            }

            return address.join('\n');
        },
        dependsOn: ['siteAddress'],
        isPublished: true,
    })
    readonly concatenatedAddress: Promise<string>;

    @decorators.referenceArrayProperty<ShowCaseProvider, 'addresses'>({
        isNullable: true,
        isPublished: true,
        isStored: true,
        onDelete: 'restrict',
        node: () => xtremShowCase.nodes.ShowCaseProviderAddress,
    })
    readonly addresses: Promise<xtremShowCase.nodes.ShowCaseProviderAddress[] | null>;

    @decorators.referenceProperty<ShowCaseProvider, 'flagshipProduct'>({
        filters: {
            lookup: {
                provider() {
                    return this;
                },
            },
        },
        isNullable: true,
        isPublished: true,
        isStored: true,
        node: () => xtremShowCase.nodes.ShowCaseProduct,
    })
    readonly flagshipProduct: Reference<xtremShowCase.nodes.ShowCaseProduct | null>;

    @decorators.referenceProperty<ShowCaseProvider, 'item'>({
        isNullable: true,
        isPublished: true,
        isStored: true,
        node: () => xtremShowCase.nodes.ShowCaseItem,
    })
    readonly item: Reference<xtremShowCase.nodes.ShowCaseItem | null>;

    @decorators.integerProperty<ShowCaseProvider, 'minQuantity'>({
        defaultValue() {
            return 0;
        },
        isPublished: true,
        isStored: true,
    })
    readonly minQuantity: Promise<integer>;

    @decorators.enumArrayProperty<ShowCaseProvider, 'ratings'>({
        dataType: () => xtremShowCase.enums.showCaseProviderRatingDataType,
        isNullable: true,
        isPublished: true,
        isStored: true,
    })
    readonly ratings: Promise<xtremShowCase.enums.ShowCaseProviderRating[] | null>;

    @decorators.jsonProperty<ShowCaseProvider, 'additionalInfo'>({
        isNullable: true,
        isPublished: true,
        isStored: true,
    })
    readonly additionalInfo: Promise<any | null>;

    @decorators.bulkMutation<typeof ShowCaseProvider, 'export'>({
        isPublished: true,
    })
    static async export(_context: Context, provider: ShowCaseProvider): Promise<void> {
        // eslint-disable-next-line no-console
        console.log('ShowCaseProvider => bulkMutation', await provider.textField);
    }
}
