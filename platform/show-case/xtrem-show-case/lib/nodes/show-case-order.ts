import { Collection, Node, Reference, TextStream, date, decorators } from '@sage/xtrem-core';
import * as x3ShowCase from '../../index';

@decorators.node<ShowCaseOrder>({
    storage: 'sql',
    canCreate: true,
    canDelete: true,
    canRead: true,
    canSearch: true,
    canUpdate: true,
    isPublished: true,
    isVitalCollectionChild: true,
    hasAttachments: true,
})
export class ShowCaseOrder extends Node {
    @decorators.referenceProperty<ShowCaseOrder, 'customer'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        node: () => x3ShowCase.nodes.ShowCaseCustomer,
        isVitalParent: true,
    })
    readonly customer: Reference<x3ShowCase.nodes.ShowCaseCustomer | null>;

    @decorators.dateProperty<ShowCaseOrder, 'orderDate'>({
        isStored: true,
        isPublished: true,
        defaultValue() {
            return date.parse('2025-01-01');
        },
    })
    readonly orderDate: Promise<date | null>;

    @decorators.textStreamProperty<ShowCaseOrder, 'details'>({
        isStored: true,
        isPublished: true,
    })
    readonly details: Promise<TextStream>;

    @decorators.collectionProperty<ShowCaseOrder, 'invoices'>({
        isPublished: true,
        reverseReference: 'order',
        isVital: true,
        node: () => x3ShowCase.nodes.ShowCaseInvoice,
    })
    readonly invoices: Collection<x3ShowCase.nodes.ShowCaseInvoice>;

    @decorators.enumProperty<ShowCaseOrder, 'orderType'>({
        dataType: () => x3ShowCase.enums.showCaseOrderTypeDataType,
        isStored: true,
        isPublished: true,
        isNullable: true,
    })
    readonly orderType: Promise<x3ShowCase.enums.ShowCaseOrderType | null>;
}
