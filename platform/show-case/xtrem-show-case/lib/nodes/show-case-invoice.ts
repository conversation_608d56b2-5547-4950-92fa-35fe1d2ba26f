import {
    BinaryStream,
    Collection,
    date,
    decorators,
    integer,
    Node,
    Reference,
    TextStream,
    TextStreamDataType,
} from '@sage/xtrem-core';
import * as x3ShowCase from '../../index';

@decorators.node<ShowCaseInvoice>({
    storage: 'sql',
    canCreate: true,
    canDelete: true,
    canRead: true,
    canSearch: true,
    hasAttachments: true,
    canUpdate: true,
    isPublished: true,
    isVitalCollectionChild: true,
    isCustomizable: true,
})
export class ShowCaseInvoice extends Node {
    @decorators.referenceProperty<ShowCaseInvoice, 'customer'>({
        isStored: true,
        isNullable: true,
        isPublished: true,
        lookupAccess: true,
        dependsOn: ['order'],
        node: () => x3ShowCase.nodes.ShowCaseCustomer,
        async defaultValue() {
            return (await this.order)?.customer;
        },
    })
    readonly customer: Reference<x3ShowCase.nodes.ShowCaseCustomer | null>;

    @decorators.dateProperty<ShowCaseInvoice, 'purchaseDate'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        async control(ctx, val) {
            await ctx.error.if(val).is.greater.than(date.fromJsDate(new Date('1/1/2026')));
        },
        defaultValue() {
            return date.parse('2025-01-01');
        },
    })
    readonly purchaseDate: Promise<date | null>;

    @decorators.textStreamProperty<ShowCaseInvoice, 'notes'>({
        isStored: true,
        isPublished: true,
        dataType: () =>
            new TextStreamDataType({
                maxLength: 1000000,
                allowedContentTypes: ['text/html', 'application/json', 'text/plain'],
            }),
    })
    readonly notes: Promise<TextStream>;

    @decorators.collectionProperty<ShowCaseInvoice, 'lines'>({
        isPublished: true,
        reverseReference: 'invoice',
        isVital: true,
        dependsOn: ['order', 'customer'],
        node: () => x3ShowCase.nodes.ShowCaseInvoiceLine,
    })
    readonly lines: Collection<x3ShowCase.nodes.ShowCaseInvoiceLine>;

    @decorators.integerProperty<ShowCaseInvoice, 'totalProductQty'>({
        isPublished: true,
        getValue() {
            return this.lines.sum(currentLine => currentLine.orderQuantity);
        },
    })
    readonly totalProductQty: Promise<integer>;

    @decorators.binaryStreamProperty<ShowCaseInvoice, 'pdf'>({
        isStored: true,
        isNullable: true,
        isPublished: true,
    })
    readonly pdf: Promise<BinaryStream | null>;

    @decorators.textStreamProperty<ShowCaseInvoice, 'queryText'>({
        isStored: true,
        isPublished: true,
    })
    readonly queryText: Promise<TextStream>;

    @decorators.referenceProperty<ShowCaseInvoice, 'order'>({
        isStored: true,
        isPublished: true,
        node: () => x3ShowCase.nodes.ShowCaseOrder,
        isVitalParent: true,
    })
    readonly order: Reference<x3ShowCase.nodes.ShowCaseOrder>;
}
