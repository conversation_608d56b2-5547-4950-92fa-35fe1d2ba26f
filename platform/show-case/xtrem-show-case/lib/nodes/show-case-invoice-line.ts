import { decimal, decorators, integer, Node, Reference } from '@sage/xtrem-core';
import * as x3ShowCase from '../../index';
import { defaultDecimalDataType } from '../data-types/_index';
import { showCaseDiscountOption } from '../service-options/show-case-discount-option';

@decorators.node<ShowCaseInvoiceLine>({
    storage: 'sql',
    canCreate: true,
    canDelete: true,
    canRead: true,
    canSearch: true,
    canUpdate: true,
    isPublished: true,
    isVitalCollectionChild: true,
    isCustomizable: true,
})
export class ShowCaseInvoiceLine extends Node {
    @decorators.referenceProperty<ShowCaseInvoiceLine, 'invoice'>({
        isStored: true,
        isPublished: true,
        isVitalParent: true,
        node: () => x3ShowCase.nodes.ShowCaseInvoice,
    })
    readonly invoice: Reference<x3ShowCase.nodes.ShowCaseInvoice>;

    @decorators.integerProperty<ShowCaseInvoiceLine, 'orderQuantity'>({
        isStored: true,
        isPublished: true,
        defaultValue() {
            return 1;
        },
    })
    readonly orderQuantity: Promise<integer>;

    @decorators.stringProperty<ShowCaseInvoiceLine, 'comments'>({
        isStored: true,
        isPublished: true,
        dataType: () => x3ShowCase.dataTypes.descriptionDataType,
        dependsOn: ['invoice', 'orderQuantity'],
        async defaultValue() {
            const customer = await (await this.invoice).customer;
            return `${await this.orderQuantity} items supplied to ${(await customer?.name) || ''} `;
        },
    })
    readonly comments: Promise<string>;

    @decorators.referenceProperty<ShowCaseInvoiceLine, 'product'>({
        isStored: true,
        isPublished: true,
        node: () => x3ShowCase.nodes.ShowCaseProduct,
    })
    readonly product: Reference<x3ShowCase.nodes.ShowCaseProduct>;

    @decorators.decimalProperty<ShowCaseInvoiceLine, 'netPrice'>({
        isStored: true,
        isPublished: true,
        dependsOn: ['product'],
        dataType: () => defaultDecimalDataType,
        async defaultValue() {
            return (await (await this.product)?.netPrice) || 0;
        },
        async control(ctx, val) {
            await ctx.error.if(val).is.less.than(0);
        },
    })
    readonly netPrice: Promise<decimal>;

    @decorators.decimalProperty<ShowCaseInvoiceLine, 'priceDiscount'>({
        serviceOptions: () => [showCaseDiscountOption],
        isStored: true,
        isPublished: true,
        dataType: () => defaultDecimalDataType,
        defaultValue() {
            return 0;
        },
    })
    readonly priceDiscount: Promise<decimal>;

    @decorators.enumProperty<ShowCaseInvoiceLine, 'discountType'>({
        dataType: () => x3ShowCase.enums.showCaseDiscountTypeDataType,
        isStored: true,
        isPublished: true,
        isNullable: true,
        dependsOn: ['orderQuantity'],
        async defaultValue() {
            return (await this.orderQuantity) > 10 ? 'compensation' : null;
        },
    })
    readonly discountType: Promise<x3ShowCase.enums.ShowCaseDiscountType | null>;
}
