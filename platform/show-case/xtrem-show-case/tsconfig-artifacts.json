{"extends": "../../tsconfig-artifacts-base.json", "compilerOptions": {"outDir": "build", "rootDir": ".", "baseUrl": "."}, "include": ["lib/pages", "lib/widgets", "lib/page-extensions", "lib/page-fragments", "lib/stickers", "lib/widgets", "api/api.d.ts", "lib/menu-items", "lib/client-functions", "lib/shared-functions"], "references": [{"path": "../xtrem-cli"}, {"path": "../etna-ui"}, {"path": "../xtrem-core"}, {"path": "../xtrem-decimal"}, {"path": "./api"}, {"path": "./xtrem-client"}]}