Feature: User Profile


       Scenario: As a user I want to open and close my user profile by clicking on the profile picture button
              Given the user opens the application on a desktop
              Then the settings profile action in the application top bar is displayed
              When the user clicks the settings profile action in the application top bar
              Then the settings profile action in the application top bar is opened
              And the user waits 1 second
              When the user clicks the settings profile action in the application top bar
              Then the settings profile action in the application top bar is closed
