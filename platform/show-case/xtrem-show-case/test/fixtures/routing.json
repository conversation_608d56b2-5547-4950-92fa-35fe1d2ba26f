{"@sage/xtrem-import-export": [{"topic": "ImportExportTemplate/batchImport/start", "queue": "import-export", "sourceFileName": "import-export-template.ts"}, {"topic": "UploadedFile/processUpload", "queue": "import-export", "sourceFileName": "import-result.ts"}], "@sage/xtrem-routing": [{"topic": "UploadedFile/InfrastructureComplete", "queue": "routing", "sourceFileName": "uploaded-file-listener.ts"}], "@sage/xtrem-scheduler": [{"topic": "SysJobExecution/updateStatus", "queue": "routing", "sourceFileName": "sys-job-execution.ts"}, {"topic": "SysJobSchedule/jobScheduleReset", "queue": "routing", "sourceFileName": "sys-job-schedule.ts"}, {"topic": "SysJobSchedule/jobScheduleDelete", "queue": "routing", "sourceFileName": "sys-job-schedule.ts"}], "@sage/xtrem-show-case": [{"topic": "ShowCaseCustomer/testAsyncMutation/start", "queue": "showcase", "sourceFileName": "show-case-customer.ts"}, {"topic": "ShowCaseProvider/export/start", "queue": "showcase", "sourceFileName": "show-case-provider.ts"}, {"topic": "ShowCaseProduct/export/start", "queue": "showcase", "sourceFileName": "show-case-product.ts"}, {"topic": "ShowCaseProduct/testBulkMutation/start", "queue": "showcase", "sourceFileName": "show-case-product.ts"}]}