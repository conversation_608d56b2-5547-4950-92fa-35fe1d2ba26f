Feature: 2-2 Nested grid sidebar
    # Tests the functionality of nested grids with sidebars, including adding new rows, navigating through tabs, and manipulating multi-level grid data
    ## LEVEL 0 - Orders

    Scenario: As a user I want to be able to focus on the phantom row by clicking on the "Add line" action
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/NestedGridWithSidebar/eyJfaWQiOiIxODYifQ=="
        Then the "Field - NestedGrid with sidebar" titled page is displayed
        When the user selects the "field" bound nested grid field on the main page
        When the user adds a new row to the nested grid field
        Then the element with the following selector is focused: "[data-testid='field-0-3'] input"

    Scenario: As a user I want to be able to add an order to nested grids by tabbing
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/NestedGridWithSidebar/eyJfaWQiOiIxODYifQ=="
        Then the "Field - NestedGrid with sidebar" titled page is displayed
        When the user selects the "field" bound nested grid field on the main page
        And the user selects the floating row of the selected nested grid field at level 1
        When the user writes "Cummerata; Weimann" in the "Customer" labelled nested date field of the selected floating row in the nested grid field
        When the user writes "10/10/2024" in the "Order Date" labelled nested date field of the selected floating row in the nested grid field
        And the user presses Tab
        And the user presses Tab
        Then the user selects the row with the following content in the nested grid field
            | columnHeader | cellText           |
            | Id           | -1                 |
            | Customer     | Cummerata; Weimann |
            | Order Date   | 10/10/2024         |

    Scenario: As a user I want to be able to add an order to nested grids by hitting Control+Enter
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/NestedGridWithSidebar/eyJfaWQiOiIxODYifQ=="
        Then the "Field - NestedGrid with sidebar" titled page is displayed
        When the user selects the "field" bound nested grid field on the main page
        And the user selects the floating row of the selected nested grid field at level 1
        When the user writes "Cummerata; Weimann" in the "Customer" labelled nested date field of the selected floating row in the nested grid field
        When the user writes "10/10/2024" in the "Order Date" labelled nested date field of the selected floating row in the nested grid field
        And the user presses Control+Enter
        Then the user selects the row with the following content in the nested grid field
            | columnHeader | cellText           |
            | Id           | -1                 |
            | Customer     | Cummerata; Weimann |
            | Order Date   | 10/10/2024         |

    Scenario: As a user I want the nested grid bound values to render on the sidebar
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/NestedGridWithSidebar/eyJfaWQiOiIxODYifQ=="
        Then the "Field - NestedGrid with sidebar" titled page is displayed
        When the user selects the "field" bound nested grid field on the main page
        And the user selects the row with the following content in the nested grid field
            | columnHeader | cellText           |
            | Id           | 96                 |
            | Customer     | Cummerata; Weimann |
            | Order Date   | 01/01/2021         |
        And the user clicks the "Edit order on sidebar" action of the selected row in the nested grid field
        When the user selects the "Order Date" labelled date field on the sidebar
        Then the value of the date field is "01/01/2021"
        And the user selects the "Id" labelled text field on the sidebar
        Then the value of the text field is "96"
        And the user selects the "Details" labelled text area field on the sidebar
        Then the value of the text area field is "Realigned multi-state software "

    Scenario: As a user I want the nested grid bound values to render on the sidebar - mobile
        Given the user opens the application on a mobile using the following link: "@sage/xtrem-show-case/NestedGridWithSidebar/eyJfaWQiOiIxODYifQ=="
        Then the "Field - NestedGrid with sidebar" titled page is displayed
        When the user selects the "field" bound nested grid field on the main page
        And the value of the "Id" labelled nested text field of the card 1 in the nested grid field is "96"
        And the value of the "Customer" labelled nested reference field of the card 1 in the nested grid field is "Cummerata; Weimann"
        And the value of the "Order Date" labelled nested date field of the card 1 in the nested grid field is "01/01/2021"
        When the user clicks the "Edit order on sidebar" inline action button of the card 1 in the nested grid field
        When the user selects the "Order Date" labelled date field on a full width modal
        Then the value of the date field is "01/01/2021"
        And the user selects the "Id" labelled text field on a full width modal
        Then the value of the text field is "96"
        And the user selects the "Details" labelled text area field on a full width modal
        Then the value of the text area field is "Realigned multi-state software "

    Scenario: As a user I want to make changes on the sidebar but only apply them to the nested grid when I click the Apply button
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/NestedGridWithSidebar/eyJfaWQiOiIxODYifQ=="
        Then the "Field - NestedGrid with sidebar" titled page is displayed
        When the user selects the "field" bound nested grid field on the main page
        And the user selects the row with the following content in the nested grid field
            | columnHeader | cellText           |
            | Id           | 96                 |
            | Customer     | Cummerata; Weimann |
            | Order Date   | 01/01/2021         |
        And the user clicks the "Edit order on sidebar" action of the selected row in the nested grid field
        When the user selects the "Order Date" labelled date field on the sidebar
        And the value of the date field is "01/01/2021"
        When the user writes "02/02/2024" in the date field
        When the user selects the "field" bound nested grid field on the main page
        And the user selects the row with the following content in the nested grid field
            | columnHeader | cellText           |
            | Id           | 96                 |
            | Customer     | Cummerata; Weimann |
            | Order Date   | 01/01/2021         |
        When the user clicks the "Apply" button of the dialog on the sidebar
        And the user selects the "field" bound nested grid field on the main page
        And the user selects the row with the following content in the nested grid field
            | columnHeader | cellText           |
            | Id           | 96                 |
            | Customer     | Cummerata; Weimann |
            | Order Date   | 02/02/2024         |

    Scenario: As a user I want to make changes on the sidebar but only apply them to the nested grid when I click the Apply button - mobile
        Given the user opens the application on a mobile using the following link: "@sage/xtrem-show-case/NestedGridWithSidebar/eyJfaWQiOiIxODYifQ=="
        Then the "Field - NestedGrid with sidebar" titled page is displayed
        When the user selects the "field" bound nested grid field on the main page
        And the value of the "Id" labelled nested text field of the card 1 in the nested grid field is "96"
        When the user clicks the "Edit order on sidebar" inline action button of the card 1 in the nested grid field
        When the user selects the "Order Date" labelled date field on a full width modal
        Then the value of the date field is "01/01/2021"
        When the user writes "02/02/2024" in the date field
        When the user selects the "field" bound nested grid field on the main page
        And the value of the "Order Date" labelled nested date field of the card 1 in the nested grid field is "01/01/2021"
        When the user clicks the "Apply" button of the dialog on the sidebar
        And the user selects the "field" bound nested grid field on the main page
        And the value of the "Order Date" labelled nested date field of the card 1 in the nested grid field is "02/02/2024"

    Scenario: As a user I want to save the changes I make on the sidebar
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/NestedGridWithSidebar/eyJfaWQiOiIxODYifQ=="
        Then the "Field - NestedGrid with sidebar" titled page is displayed
        When the user selects the "field" bound nested grid field on the main page
        And the user selects the row with the following content in the nested grid field
            | columnHeader | cellText           |
            | Id           | 96                 |
            | Customer     | Cummerata; Weimann |
            | Order Date   | 01/01/2021         |
        And the user clicks the "Edit order on sidebar" action of the selected row in the nested grid field
        When the user selects the "Order Date" labelled date field on the sidebar
        And the value of the date field is "01/01/2021"
        When the user writes "02/02/2024" in the date field
        When the user selects the "field" bound nested grid field on the main page
        And the user selects the row with the following content in the nested grid field
            | columnHeader | cellText           |
            | Id           | 96                 |
            | Customer     | Cummerata; Weimann |
            | Order Date   | 01/01/2021         |
        When the user clicks the "Apply" button of the dialog on the sidebar
        When the user selects the "field" bound nested grid field on the main page
        And the user selects the row with the following content in the nested grid field
            | columnHeader | cellText           |
            | Id           | 96                 |
            | Customer     | Cummerata; Weimann |
            | Order Date   | 02/02/2024         |
        And the user clicks the "Save" labelled business action button on the main page
        And the user waits 1 second
        When the user navigates to the following link: "@sage/xtrem-show-case/NestedGridWithSidebar/eyJfaWQiOiIxODYifQ=="
        And the user selects the "field" bound nested grid field on the main page
        And the user selects the row with the following content in the nested grid field
            | columnHeader | cellText           |
            | Id           | 96                 |
            | Customer     | Cummerata; Weimann |
            | Order Date   | 02/02/2024         |
        And the user clicks the "Edit order on sidebar" action of the selected row in the nested grid field
        When the user selects the "Order Date" labelled date field on the sidebar
        And the value of the date field is "02/02/2024"
        When the user writes "01/01/2021" in the date field
        When the user clicks the "Apply" button of the dialog on the sidebar
        And the user clicks the "Save" labelled business action button on the main page
        And the user waits 1 second

    Scenario: As a user I want to save the changes I make on the sidebar - mobile
        Given the user opens the application on a mobile using the following link: "@sage/xtrem-show-case/NestedGridWithSidebar/eyJfaWQiOiIxODYifQ=="
        Then the "Field - NestedGrid with sidebar" titled page is displayed
        When the user selects the "field" bound nested grid field on the main page
        And the value of the "Id" labelled nested text field of the card 1 in the nested grid field is "96"
        When the user clicks the "Edit order on sidebar" inline action button of the card 1 in the nested grid field
        When the user selects the "Order Date" labelled date field on a full width modal
        Then the value of the date field is "01/01/2021"
        When the user writes "02/02/2024" in the date field
        When the user selects the "field" bound nested grid field on the main page
        And the value of the "Order Date" labelled nested date field of the card 1 in the nested grid field is "01/01/2021"
        When the user clicks the "Apply" button of the dialog on a full width modal
        And the user selects the "field" bound nested grid field on the main page
        Then the value of the "Order Date" labelled nested date field of the card 1 in the nested grid field is "02/02/2024"
        And the user clicks the "Save" labelled business action button on the main page
        And the user waits 1 second
        And the user navigates to the following link: "@sage/xtrem-show-case/NestedGridWithSidebar/eyJfaWQiOiIxODYifQ=="
        And the user selects the "field" bound nested grid field on the main page
        And the value of the "Order Date" labelled nested date field of the card 1 in the nested grid field is "02/02/2024"
        When the user clicks the "Edit order on sidebar" inline action button of the card 1 in the nested grid field
        When the user selects the "Order Date" labelled date field on a full width modal
        Then the value of the date field is "02/02/2024"
        When the user writes "01/01/2021" in the date field
        And the user selects the "field" bound nested grid field on the main page
        And the value of the "Order Date" labelled nested date field of the card 1 in the nested grid field is "02/02/2024"
        When the user clicks the "Apply" button of the dialog on a full width modal
        And the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record updated" is displayed

    Scenario: As a user I want to cancel the changes I make on the sidebar
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/NestedGridWithSidebar/eyJfaWQiOiIxODYifQ=="
        Then the "Field - NestedGrid with sidebar" titled page is displayed
        And the user selects the "field" bound nested grid field on the main page
        And the user selects the row with the following content in the nested grid field
            | columnHeader | cellText           |
            | Id           | 96                 |
            | Customer     | Cummerata; Weimann |
            | Order Date   | 01/01/2021         |
        And the user clicks the "Edit order on sidebar" action of the selected row in the nested grid field
        When the user selects the "Order Date" labelled date field on the sidebar
        Then the value of the date field is "01/01/2021"
        When the user writes "02/02/2024" in the date field
        Then the value of the date field is "02/02/2024"
        When the user clicks the "Cancel" button of the dialog on the sidebar
        And the user waits 1 second
        And the user selects the "field" bound nested grid field on the main page
        And the user selects the row with the following content in the nested grid field
            | columnHeader | cellText           |
            | Id           | 96                 |
            | Customer     | Cummerata; Weimann |
            | Order Date   | 01/01/2021         |

    Scenario: As a user I want to cancel the changes I make on the sidebar - mobile
        Given the user opens the application on a mobile using the following link: "@sage/xtrem-show-case/NestedGridWithSidebar/eyJfaWQiOiIxODYifQ=="
        Then the "Field - NestedGrid with sidebar" titled page is displayed
        When the user selects the "field" bound nested grid field on the main page
        And the value of the "Id" labelled nested text field of the card 1 in the nested grid field is "96"
        When the user clicks the "Edit order on sidebar" inline action button of the card 1 in the nested grid field
        When the user selects the "Order Date" labelled date field on a full width modal
        Then the value of the date field is "01/01/2021"
        When the user writes "02/02/2024" in the date field
        Then the value of the date field is "02/02/2024"
        When the user clicks the "Cancel" button of the dialog on a full width modal
        And the user waits 1 second
        And the user selects the "field" bound nested grid field on the main page
        And the value of the "Order Date" labelled nested date field of the card 1 in the nested grid field is "01/01/2021"

    Scenario: As a user I want the clean sidebar to close if I cancel it
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/NestedGridWithSidebar/eyJfaWQiOiIxODYifQ=="
        And the "Field - NestedGrid with sidebar" titled page is displayed
        And the user selects the "field" bound nested grid field on the main page
        And the user selects the row with the following content in the nested grid field
            | columnHeader | cellText           |
            | Id           | 96                 |
            | Customer     | Cummerata; Weimann |
            | Order Date   | 01/01/2021         |
        And the user clicks the "Edit order on sidebar" action of the selected row in the nested grid field
        When the user clicks the "Cancel" button of the dialog on the sidebar
        Then no dialogs are displayed

    Scenario: As a user I want the clean sidebar to close if I cancel it - mobile
        Given the user opens the application on a mobile using the following link: "@sage/xtrem-show-case/NestedGridWithSidebar/eyJfaWQiOiIxODYifQ=="
        Then the "Field - NestedGrid with sidebar" titled page is displayed
        When the user selects the "field" bound nested grid field on the main page
        And the value of the "Id" labelled nested text field of the card 1 in the nested grid field is "96"
        When the user clicks the "Edit order on sidebar" inline action button of the card 1 in the nested grid field
        When the user clicks the "Cancel" button of the dialog on a full width modal
        Then no dialogs are displayed

    Scenario: As a user I want the dirty sidebar to display a warning if I close it
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/NestedGridWithSidebar/eyJfaWQiOiIxODYifQ=="
        And the "Field - NestedGrid with sidebar" titled page is displayed
        And the user selects the "field" bound nested grid field on the main page
        And the user selects the row with the following content in the nested grid field
            | columnHeader | cellText           |
            | Id           | 96                 |
            | Customer     | Cummerata; Weimann |
            | Order Date   | 01/01/2021         |
        And the user clicks the "Edit order on sidebar" action of the selected row in the nested grid field
        When the user selects the "Order Date" labelled date field on the sidebar
        When the user writes "02/02/2024" in the date field
        When the user clicks the "Cancel" button of the dialog on the sidebar
        Then a warn dialog appears
        And the text in the body of the dialog is "Leave and discard your changes?"
        When the user clicks the "Discard" button of the Confirm dialog
        Then no dialogs are displayed

    Scenario: As a user I want the dirty sidebar to display a warning if I close it - mobile
        Given the user opens the application on a mobile using the following link: "@sage/xtrem-show-case/NestedGridWithSidebar/eyJfaWQiOiIxODYifQ=="
        Then the "Field - NestedGrid with sidebar" titled page is displayed
        When the user selects the "field" bound nested grid field on the main page
        And the value of the "Id" labelled nested text field of the card 1 in the nested grid field is "96"
        When the user clicks the "Edit order on sidebar" inline action button of the card 1 in the nested grid field
        When the user selects the "Order Date" labelled date field on a full width modal
        When the user writes "02/02/2024" in the date field
        When the user clicks the "Cancel" button of the dialog on a full width modal
        Then a warn dialog appears
        And the text in the body of the dialog is "Leave and discard your changes?"
        When the user clicks the "Discard" button of the Confirm dialog
        Then no dialogs are displayed

    Scenario: As a user I want to know if a field in my sidebar has errors
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/NestedGridWithSidebar/eyJfaWQiOiIxODYifQ=="
        And the "Field - NestedGrid with sidebar" titled page is displayed
        And the user selects the "field" bound nested grid field on the main page
        And the user selects the row with the following content in the nested grid field
            | columnHeader | cellText           |
            | Id           | 96                 |
            | Customer     | Cummerata; Weimann |
            | Order Date   | 01/01/2021         |
        And the user clicks the "Edit order on sidebar" action of the selected row in the nested grid field
        And selects the "Details section" labelled navigation anchor on the sidebar
        When the user selects the "Customer *" labelled reference field on the sidebar
        And the user clears the reference field
        Then the "Customer *" labelled reference field on the sidebar is invalid
        And the "Apply" button of the dialog is disabled on the sidebar

    Scenario: As a user I want to know if a field in my sidebar has errors - mobile
        Given the user opens the application on a mobile using the following link: "@sage/xtrem-show-case/NestedGridWithSidebar/eyJfaWQiOiIxODYifQ=="
        Then the "Field - NestedGrid with sidebar" titled page is displayed
        When the user selects the "field" bound nested grid field on the main page
        And the value of the "Id" labelled nested text field of the card 1 in the nested grid field is "96"
        When the user clicks the "Edit order on sidebar" inline action button of the card 1 in the nested grid field
        And selects the "Details section" labelled navigation anchor on a full width modal
        When the user selects the "Customer *" labelled reference field on a full width modal
        And the user clears the reference field
        Then the "Customer *" labelled reference field on a full width modal is invalid
        And the "Apply" button of the dialog is disabled on a full width modal

    Scenario: As a user I want to remove a nested grid record from the sidebar
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/NestedGridWithSidebar/eyJfaWQiOiIxODYifQ=="
        Then the "Field - NestedGrid with sidebar" titled page is displayed
        When the user selects the "field" bound nested grid field on the main page
        And the user selects the row with the following content in the nested grid field
            | columnHeader | cellText           |
            | Id           | 96                 |
            | Customer     | Cummerata; Weimann |
            | Order Date   | 01/01/2021         |
        And the user clicks the "Edit order on sidebar" action of the selected row in the nested grid field
        Then the "Order 96" titled sidebar is displayed
        Then the "Field - NestedGrid with sidebar" titled page is displayed
        When the user clicks the "remove" labelled more actions button in the sidebar header
        Then no dialogs are displayed
        And the "Field - NestedGrid with sidebar" titled page is displayed

    Scenario: As a user I want to remove a nested grid record from the sidebar - mobile
        Given the user opens the application on a mobile using the following link: "@sage/xtrem-show-case/NestedGridWithSidebar/eyJfaWQiOiIxODYifQ=="
        Then the "Field - NestedGrid with sidebar" titled page is displayed
        When the user selects the "field" bound nested grid field on the main page
        And the value of the "Id" labelled nested text field of the card 1 in the nested grid field is "96"
        When the user clicks the "Edit order on sidebar" inline action button of the card 1 in the nested grid field
        When the user clicks the "remove" labelled more actions button on a full width modal
        Then no dialogs are displayed
        And the value of the "Id" labelled nested text field of the card 1 in the nested grid field is "42"


    # LEVEL 1 - Invoices

    Scenario: As a user I want the nested grid bound values to render on the sidebar
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/NestedGridWithSidebar/eyJfaWQiOiIxODYifQ=="
        Then the "Field - NestedGrid with sidebar" titled page is displayed
        When the user selects the "field" bound nested grid field on the main page
        And the user selects row with text "96" in column with header "Id" in the nested grid field
        When the user expands the selected row of the nested grid field
        And the user selects the row with the following content in the nested grid field
            | columnHeader  | cellText     |
            | Id            | 323          |
            | Purchase Date | 03/18/2020   |
            | Customer      | Jenkins-Dare |
        And the user clicks the "Edit invoice on sidebar" action of the selected row in the nested grid field
        When the user selects the "Purchase Date" labelled date field on the sidebar
        Then the value of the date field is "03/18/2020"
        And the user selects the "Id" labelled text field on the sidebar
        Then the value of the text field is "323"
        And the user selects the "Notes" labelled text area field on the sidebar
        Then the value of the text area field is "matrix seamless infomediaries "

    Scenario: As a user I want the nested grid bound values to render on the sidebar - mobile
        Given the user opens the application on a mobile using the following link: "@sage/xtrem-show-case/NestedGridWithSidebar/eyJfaWQiOiIxODYifQ=="
        Then the "Field - NestedGrid with sidebar" titled page is displayed
        When the user selects the "field" bound nested grid field on the main page
        And the value of the "Id" labelled nested text field of the card 1 in the nested grid field is "96"
        When the user clicks the card 1 in the nested grid field
        Then the value of the "Id" labelled nested text field of the card 1 in the nested grid field is "323"
        And the value of the "Purchase Date" labelled nested date field of the card 1 in the nested grid field is "03/18/2020"
        And the value of the "Customer" labelled nested reference field of the card 1 in the nested grid field is "Jenkins-Dare"
        When the user clicks the "Edit invoice on sidebar" inline action button of the card 1 in the nested grid field
        When the user selects the "Purchase Date" labelled date field on a full width modal
        Then the value of the date field is "03/18/2020"
        And the user selects the "Id" labelled text field on a full width modal
        Then the value of the text field is "323"
        And the user selects the "Notes" labelled text area field on a full width modal
        Then the value of the text area field is "matrix seamless infomediaries "

    Scenario: As a user I want to make changes on the sidebar but only apply them to the nested grid when I click the Apply button
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/NestedGridWithSidebar/eyJfaWQiOiIxODYifQ=="
        Then the "Field - NestedGrid with sidebar" titled page is displayed
        When the user selects the "field" bound nested grid field on the main page
        And the user selects row with text "96" in column with header "Id" in the nested grid field
        When the user expands the selected row of the nested grid field
        And the user selects the row with the following content in the nested grid field
            | columnHeader  | cellText     |
            | Id            | 323          |
            | Purchase Date | 03/18/2020   |
            | Customer      | Jenkins-Dare |
        And the user clicks the "Edit invoice on sidebar" action of the selected row in the nested grid field
        When the user selects the "Purchase Date" labelled date field on the sidebar
        And the value of the date field is "03/18/2020"
        When the user writes "02/02/2024" in the date field
        When the user selects the "field" bound nested grid field on the main page
        And the user selects the row with the following content in the nested grid field
            | columnHeader  | cellText     |
            | Id            | 323          |
            | Purchase Date | 03/18/2020   |
            | Customer      | Jenkins-Dare |
        When the user clicks the "Apply" button of the dialog on the sidebar
        And the user selects the "field" bound nested grid field on the main page
        And the user selects the row with the following content in the nested grid field
            | columnHeader  | cellText     |
            | Id            | 323          |
            | Purchase Date | 02/02/2024   |
            | Customer      | Jenkins-Dare |
    Scenario: As a user I want to make changes on the sidebar but only apply them to the nested grid when I click the Apply button - mobile
        Given the user opens the application on a mobile using the following link: "@sage/xtrem-show-case/NestedGridWithSidebar/eyJfaWQiOiIxODYifQ=="
        Then the "Field - NestedGrid with sidebar" titled page is displayed
        When the user selects the "field" bound nested grid field on the main page
        And the value of the "Id" labelled nested text field of the card 1 in the nested grid field is "96"
        When the user clicks the card 1 in the nested grid field
        Then the value of the "Id" labelled nested text field of the card 1 in the nested grid field is "323"
        When the user clicks the "Edit invoice on sidebar" inline action button of the card 1 in the nested grid field
        When the user selects the "Purchase Date" labelled date field on a full width modal
        And the value of the date field is "03/18/2020"
        When the user writes "02/02/2024" in the date field
        When the user selects the "field" bound nested grid field on the main page
        Then the value of the "Purchase Date" labelled nested date field of the card 1 in the nested grid field is "03/18/2020"
        When the user clicks the "Apply" button of the dialog on the sidebar
        And the user selects the "field" bound nested grid field on the main page
        Then the value of the "Purchase Date" labelled nested date field of the card 1 in the nested grid field is "02/02/2024"

    Scenario: As a user I want to save the changes I make on the sidebar
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/NestedGridWithSidebar/eyJfaWQiOiIxODYifQ=="
        Then the "Field - NestedGrid with sidebar" titled page is displayed
        When the user selects the "field" bound nested grid field on the main page
        And the user selects row with text "96" in column with header "Id" in the nested grid field
        When the user expands the selected row of the nested grid field
        And the user selects the row with the following content in the nested grid field
            | columnHeader  | cellText     |
            | Id            | 323          |
            | Purchase Date | 03/18/2020   |
            | Customer      | Jenkins-Dare |
        And the user clicks the "Edit invoice on sidebar" action of the selected row in the nested grid field
        When the user selects the "Purchase Date" labelled date field on the sidebar
        And the value of the date field is "03/18/2020"
        When the user writes "02/02/2024" in the date field
        When the user selects the "field" bound nested grid field on the main page
        And the user selects the row with the following content in the nested grid field
            | columnHeader  | cellText     |
            | Id            | 323          |
            | Purchase Date | 03/18/2020   |
            | Customer      | Jenkins-Dare |
        When the user clicks the "Apply" button of the dialog on the sidebar
        When the user selects the "field" bound nested grid field on the main page
        And the user selects the row with the following content in the nested grid field
            | columnHeader  | cellText     |
            | Id            | 323          |
            | Purchase Date | 02/02/2024   |
            | Customer      | Jenkins-Dare |
        And the user clicks the "Save" labelled business action button on the main page
        And the user waits 1 second
        When the user navigates to the following link: "@sage/xtrem-show-case/NestedGridWithSidebar/eyJfaWQiOiIxODYifQ=="
        And the user selects the "field" bound nested grid field on the main page
        And the user selects row with text "96" in column with header "Id" in the nested grid field
        When the user expands the selected row of the nested grid field
        And the user selects the row with the following content in the nested grid field
            | columnHeader  | cellText     |
            | Id            | 323          |
            | Purchase Date | 02/02/2024   |
            | Customer      | Jenkins-Dare |
        And the user clicks the "Edit invoice on sidebar" action of the selected row in the nested grid field
        When the user selects the "Purchase Date" labelled date field on the sidebar
        And the value of the date field is "02/02/2024"
        When the user writes "03/18/2020" in the date field
        When the user clicks the "Apply" button of the dialog on the sidebar
        And the user clicks the "Save" labelled business action button on the main page
        And the user waits 1 second

    Scenario: As a user I want to save the changes I make on the sidebar - mobile
        Given the user opens the application on a mobile using the following link: "@sage/xtrem-show-case/NestedGridWithSidebar/eyJfaWQiOiIxODYifQ=="
        Then the "Field - NestedGrid with sidebar" titled page is displayed
        When the user selects the "field" bound nested grid field on the main page
        And the value of the "Id" labelled nested text field of the card 1 in the nested grid field is "96"
        When the user clicks the card 1 in the nested grid field
        Then the value of the "Id" labelled nested text field of the card 1 in the nested grid field is "323"
        When the user clicks the "Edit invoice on sidebar" inline action button of the card 1 in the nested grid field
        When the user selects the "Purchase Date" labelled date field on a full width modal
        And the value of the date field is "03/18/2020"
        When the user writes "02/02/2024" in the date field
        When the user selects the "field" bound nested grid field on the main page
        And the value of the "Purchase Date" labelled nested date field of the card 1 in the nested grid field is "03/18/2020"
        When the user clicks the "Apply" button of the dialog on the sidebar
        And the user selects the "field" bound nested grid field on the main page
        And the value of the "Purchase Date" labelled nested date field of the card 1 in the nested grid field is "02/02/2024"
        And the user clicks the "Save" labelled business action button on a full width modal
        And a toast containing text "Record updated" is displayed
        And the user navigates to the following link: "@sage/xtrem-show-case/NestedGridWithSidebar/eyJfaWQiOiIxODYifQ=="
        And the user selects the "field" bound nested grid field on the main page
        And the value of the "Id" labelled nested text field of the card 1 in the nested grid field is "96"
        When the user clicks the card 1 in the nested grid field
        Then the value of the "Id" labelled nested text field of the card 1 in the nested grid field is "323"
        When the user clicks the "Edit invoice on sidebar" inline action button of the card 1 in the nested grid field
        When the user selects the "Purchase Date" labelled date field on a full width modal
        And the value of the date field is "02/02/2024"
        When the user writes "03/18/2020" in the date field
        When the user clicks the "Apply" button of the dialog on the sidebar
        And the user clicks the "Save" labelled business action button on a full width modal
        And a toast containing text "Record updated" is displayed

    Scenario: As a user I want to cancel the changes I make on the sidebar
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/NestedGridWithSidebar/eyJfaWQiOiIxODYifQ=="
        Then the "Field - NestedGrid with sidebar" titled page is displayed
        And the user selects the "field" bound nested grid field on the main page
        And the user selects row with text "96" in column with header "Id" in the nested grid field
        When the user expands the selected row of the nested grid field
        And the user selects the row with the following content in the nested grid field
            | columnHeader  | cellText     |
            | Id            | 323          |
            | Purchase Date | 03/18/2020   |
            | Customer      | Jenkins-Dare |
        And the user clicks the "Edit invoice on sidebar" action of the selected row in the nested grid field
        When the user selects the "Purchase Date" labelled date field on the sidebar
        Then the value of the date field is "03/18/2020"
        When the user writes "02/02/2024" in the date field
        Then the value of the date field is "02/02/2024"
        When the user clicks the "Cancel" button of the dialog on the sidebar
        And the user waits 1 second
        And the user selects the "field" bound nested grid field on the main page
        And the user selects the row with the following content in the nested grid field
            | columnHeader  | cellText     |
            | Id            | 323          |
            | Purchase Date | 03/18/2020   |
            | Customer      | Jenkins-Dare |

    Scenario: As a user I want to cancel the changes I make on the sidebar - mobile
        Given the user opens the application on a mobile using the following link: "@sage/xtrem-show-case/NestedGridWithSidebar/eyJfaWQiOiIxODYifQ=="
        Then the "Field - NestedGrid with sidebar" titled page is displayed
        When the user selects the "field" bound nested grid field on the main page
        And the value of the "Id" labelled nested text field of the card 1 in the nested grid field is "96"
        When the user clicks the card 1 in the nested grid field
        Then the value of the "Id" labelled nested text field of the card 1 in the nested grid field is "323"
        When the user clicks the "Edit invoice on sidebar" inline action button of the card 1 in the nested grid field
        When the user selects the "Purchase Date" labelled date field on a full width modal
        Then the value of the date field is "03/18/2020"
        When the user writes "02/02/2024" in the date field
        Then the value of the date field is "02/02/2024"
        When the user clicks the "Cancel" button of the dialog on a full width modal
        And the user waits 1 second
        And the user selects the "field" bound nested grid field on the main page
        And the value of the "Purchase Date" labelled nested date field of the card 1 in the nested grid field is "03/18/2020"

    Scenario: As a user I want the clean sidebar to close if I cancel it
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/NestedGridWithSidebar/eyJfaWQiOiIxODYifQ=="
        And the "Field - NestedGrid with sidebar" titled page is displayed
        And the user selects the "field" bound nested grid field on the main page
        And the user selects row with text "96" in column with header "Id" in the nested grid field
        When the user expands the selected row of the nested grid field
        And the user selects the row with the following content in the nested grid field
            | columnHeader  | cellText     |
            | Id            | 323          |
            | Purchase Date | 03/18/2020   |
            | Customer      | Jenkins-Dare |
        And the user clicks the "Edit invoice on sidebar" action of the selected row in the nested grid field
        When the user clicks the "Cancel" button of the dialog on the sidebar
        Then no dialogs are displayed

    Scenario: As a user I want the clean sidebar to close if I cancel it - mobile
        Given the user opens the application on a mobile using the following link: "@sage/xtrem-show-case/NestedGridWithSidebar/eyJfaWQiOiIxODYifQ=="
        Then the "Field - NestedGrid with sidebar" titled page is displayed
        When the user selects the "field" bound nested grid field on the main page
        And the value of the "Id" labelled nested text field of the card 1 in the nested grid field is "96"
        When the user clicks the card 1 in the nested grid field
        And the value of the "Id" labelled nested text field of the card 1 in the nested grid field is "323"
        When the user clicks the "Edit invoice on sidebar" inline action button of the card 1 in the nested grid field
        When the user clicks the "Cancel" button of the dialog on a full width modal
        Then no dialogs are displayed

    Scenario: As a user I want the dirty sidebar to display a warning if I close it
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/NestedGridWithSidebar/eyJfaWQiOiIxODYifQ=="
        And the "Field - NestedGrid with sidebar" titled page is displayed
        And the user selects the "field" bound nested grid field on the main page
        And the user selects row with text "96" in column with header "Id" in the nested grid field
        When the user expands the selected row of the nested grid field
        And the user selects the row with the following content in the nested grid field
            | columnHeader  | cellText     |
            | Id            | 323          |
            | Purchase Date | 03/18/2020   |
            | Customer      | Jenkins-Dare |
        And the user clicks the "Edit invoice on sidebar" action of the selected row in the nested grid field
        When the user selects the "Purchase Date" labelled date field on the sidebar
        When the user writes "02/02/2024" in the date field
        When the user clicks the "Cancel" button of the dialog on the sidebar
        Then a warn dialog appears
        And the text in the body of the dialog is "Leave and discard your changes?"
        When the user clicks the "Discard" button of the Confirm dialog
        Then no dialogs are displayed

    Scenario: As a user I want the dirty sidebar to display a warning if I close it - mobile
        Given the user opens the application on a mobile using the following link: "@sage/xtrem-show-case/NestedGridWithSidebar/eyJfaWQiOiIxODYifQ=="
        Then the "Field - NestedGrid with sidebar" titled page is displayed
        When the user selects the "field" bound nested grid field on the main page
        And the value of the "Id" labelled nested text field of the card 1 in the nested grid field is "96"
        When the user clicks the card 1 in the nested grid field
        And the value of the "Id" labelled nested text field of the card 1 in the nested grid field is "323"
        When the user clicks the "Edit invoice on sidebar" inline action button of the card 1 in the nested grid field
        When the user selects the "Purchase Date" labelled date field on a full width modal
        When the user writes "02/02/2024" in the date field
        When the user clicks the "Cancel" button of the dialog on a full width modal
        Then a warn dialog appears
        And the text in the body of the dialog is "Leave and discard your changes?"
        When the user clicks the "Discard" button of the Confirm dialog
        Then no dialogs are displayed

    Scenario: As a user I want to know if a field in my sidebar has errors
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/NestedGridWithSidebar/eyJfaWQiOiIxODYifQ=="
        And the "Field - NestedGrid with sidebar" titled page is displayed
        And the user selects the "field" bound nested grid field on the main page
        And the user selects row with text "96" in column with header "Id" in the nested grid field
        When the user expands the selected row of the nested grid field
        And the user selects the row with the following content in the nested grid field
            | columnHeader  | cellText     |
            | Id            | 323          |
            | Purchase Date | 03/18/2020   |
            | Customer      | Jenkins-Dare |
        And the user clicks the "Edit invoice on sidebar" action of the selected row in the nested grid field
        And selects the "Details section" labelled navigation anchor on the sidebar
        When the user selects the "Customer *" labelled reference field on the sidebar
        And the user clears the reference field
        Then the "Customer *" labelled reference field on the sidebar is invalid
        And the "Apply" button of the dialog is disabled on the sidebar

    Scenario: As a user I want to know if a field in my sidebar has errors - mobile
        Given the user opens the application on a mobile using the following link: "@sage/xtrem-show-case/NestedGridWithSidebar/eyJfaWQiOiIxODYifQ=="
        Then the "Field - NestedGrid with sidebar" titled page is displayed
        When the user selects the "field" bound nested grid field on the main page
        And the value of the "Id" labelled nested text field of the card 1 in the nested grid field is "96"
        When the user clicks the card 1 in the nested grid field
        And the value of the "Id" labelled nested text field of the card 1 in the nested grid field is "323"
        When the user clicks the "Edit invoice on sidebar" inline action button of the card 1 in the nested grid field
        And selects the "Details section" labelled navigation anchor on a full width modal
        When the user selects the "Customer *" labelled reference field on a full width modal
        And the user clears the reference field
        Then the "Customer *" labelled reference field on a full width modal is invalid
        And the "Apply" button of the dialog is disabled on a full width modal

    Scenario: As a user I want to remove a nested grid record from the sidebar
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/NestedGridWithSidebar/eyJfaWQiOiIxODYifQ=="
        Then the "Field - NestedGrid with sidebar" titled page is displayed
        When the user selects the "field" bound nested grid field on the main page
        And the user selects row with text "96" in column with header "Id" in the nested grid field
        When the user expands the selected row of the nested grid field
        And the user selects the row with the following content in the nested grid field
            | columnHeader  | cellText     |
            | Id            | 323          |
            | Purchase Date | 03/18/2020   |
            | Customer      | Jenkins-Dare |
        And the user clicks the "Edit invoice on sidebar" action of the selected row in the nested grid field
        Then the "Invoice 323" titled sidebar is displayed
        Then the "Field - NestedGrid with sidebar" titled page is displayed
        When the user clicks the "remove" labelled more actions button in the sidebar header
        Then no dialogs are displayed
        And the "Field - NestedGrid with sidebar" titled page is displayed

    Scenario: As a user I want to remove a nested grid record from the sidebar - mobile
        Given the user opens the application on a mobile using the following link: "@sage/xtrem-show-case/NestedGridWithSidebar/eyJfaWQiOiIxODYifQ=="
        Then the "Field - NestedGrid with sidebar" titled page is displayed
        When the user selects the "field" bound nested grid field on the main page
        And the value of the "Id" labelled nested text field of the card 1 in the nested grid field is "96"
        When the user clicks the card 1 in the nested grid field
        And the value of the "Id" labelled nested text field of the card 1 in the nested grid field is "323"
        When the user clicks the "Edit invoice on sidebar" inline action button of the card 1 in the nested grid field
        When the user clicks the "remove" labelled more actions button on a full width modal
        Then no dialogs are displayed
        And the value of the "Id" labelled nested text field of the card 1 in the nested grid field is "322"

    Scenario: As a user I want to know if a reference field in my sidebar is working correctly
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/NestedGridWithSidebar/eyJfaWQiOiIxODYifQ=="
        And the "Field - NestedGrid with sidebar" titled page is displayed
        And the user selects the "field" bound nested grid field on the main page
        And the user selects row with text "96" in column with header "Id" in the nested grid field
        When the user expands the selected row of the nested grid field
        And the user selects the row with the following content in the nested grid field
            | columnHeader  | cellText     |
            | Id            | 323          |
            | Purchase Date | 03/18/2020   |
            | Customer      | Jenkins-Dare |
        And the user clicks the "Edit invoice on sidebar" action of the selected row in the nested grid field
        And selects the "Details section" labelled navigation anchor on the sidebar
        When the user selects the "Customer *" labelled reference field on the sidebar
        And the user writes "Dare" in the reference field
        And the user selects "Jenkins-Dare" in the reference field
        When the user clicks the "Apply" button of the dialog on the sidebar
        And the user selects the row with the following content in the nested grid field
            | columnHeader  | cellText     |
            | Id            | 323          |
            | Purchase Date | 03/18/2020   |
            | Customer      | Jenkins-Dare |

    # LEVEL 2 - Invoice lines

    Scenario: As a user I want to be able to add an invoice line to nested grids by tabbing
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/NestedGridWithSidebar/eyJfaWQiOiIxODYifQ=="
        Then the "Field - NestedGrid with sidebar" titled page is displayed
        When the user selects the "field" bound nested grid field on the main page
        And the user selects row with text "96" in column with header "Id" in the nested grid field
        When the user expands the selected row of the nested grid field
        And the user selects row with text "323" in column with header "Id" in the nested grid field
        When the user expands the selected row of the nested grid field
        And the user selects the floating row of the selected nested grid field at level 3 with parent id 323
        And the user writes "10" in the "Quantity" labelled nested date field of the selected floating row in the nested grid field
        And the user writes "11" in the "Net Price" labelled nested date field of the selected floating row in the nested grid field
        And the user writes "Spice - Paprika" in the "Product" labelled nested reference field of the selected floating row in the nested grid field
        And the user presses Tab
        And the user presses Tab
        And the user presses Tab
        And the user selects the row with the following content in the nested grid field
            | columnHeader | cellText        |
            | Id           | -2              |
            | Quantity     | 10              |
            | Net Price    | 11.00           |
            | Product      | Spice - Paprika |

    Scenario: As a user I want to be able to add an invoice line to nested grids by hitting Control+Enter
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/NestedGridWithSidebar/eyJfaWQiOiIxODYifQ=="
        Then the "Field - NestedGrid with sidebar" titled page is displayed
        When the user selects the "field" bound nested grid field on the main page
        And the user selects row with text "96" in column with header "Id" in the nested grid field
        When the user expands the selected row of the nested grid field
        And the user selects row with text "323" in column with header "Id" in the nested grid field
        When the user expands the selected row of the nested grid field
        And the user selects the floating row of the selected nested grid field at level 3 with parent id 323
        And the user writes "10" in the "Quantity" labelled nested date field of the selected floating row in the nested grid field
        And the user writes "11" in the "Net Price" labelled nested date field of the selected floating row in the nested grid field
        And the user writes "Spice - Paprika" in the "Product" labelled nested reference field of the selected floating row in the nested grid field
        And the user presses Control+Enter
        And the user selects the row with the following content in the nested grid field
            | columnHeader | cellText        |
            | Id           | -2              |
            | Quantity     | 10              |
            | Net Price    | 11.00           |
            | Product      | Spice - Paprika |

    Scenario: As a user I want the nested grid bound values to render on the sidebar
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/NestedGridWithSidebar/eyJfaWQiOiIxODYifQ=="
        Then the "Field - NestedGrid with sidebar" titled page is displayed
        When the user selects the "field" bound nested grid field on the main page
        And the user selects row with text "96" in column with header "Id" in the nested grid field
        When the user expands the selected row of the nested grid field
        And the user selects row with text "10" in column with header "Id" in the nested grid field
        When the user expands the selected row of the nested grid field
        And the user selects the row with the following content in the nested grid field
            | columnHeader | cellText         |
            | Id           | 142              |
            | Quantity     | 25               |
            | Net Price    | 4,438.81         |
            | Product      | Hinge W Undercut |
        And the user clicks the "Edit invoice line on sidebar" action of the selected row in the nested grid field
        When the user selects the "Quantity" labelled numeric field on the sidebar
        Then the value of the numeric field is "25"
        And the user selects the "Net Price" labelled numeric field on the sidebar
        Then the value of the numeric field is "4,438.81"
        And the user selects the "Comments" labelled text area field on the sidebar
        Then the value of the text area field is ""

    Scenario: As a user I want the nested grid bound values to render on the sidebar - mobile
        Given the user opens the application on a mobile using the following link: "@sage/xtrem-show-case/NestedGridWithSidebar/eyJfaWQiOiIxODYifQ=="
        Then the "Field - NestedGrid with sidebar" titled page is displayed
        When the user selects the "field" bound nested grid field on the main page
        And the value of the "Id" labelled nested text field of the card 1 in the nested grid field is "96"
        When the user clicks the card 1 in the nested grid field
        Then the value of the "Id" labelled nested text field of the card 3 in the nested grid field is "10"
        When the user clicks the card 3 in the nested grid field
        Then the value of the "Id" labelled nested text field of the card 1 in the nested grid field is "142"
        And the value of the "Quantity" labelled nested numeric field of the card 1 in the nested grid field is "25"
        And the value of the "Net Price" labelled nested numeric field of the card 1 in the nested grid field is "4,438.81"
        And the value of the "Product" labelled nested reference field of the card 1 in the nested grid field is "Hinge W Undercut"
        When the user clicks the "Edit invoice line on sidebar" inline action button of the card 1 in the nested grid field
        When the user selects the "Quantity" labelled numeric field on a full width modal
        Then the value of the numeric field is "25"
        And the user selects the "Net Price" labelled numeric field on a full width modal
        Then the value of the numeric field is "4,438.81"
        And the user selects the "Comments" labelled text area field on a full width modal
        Then the value of the text area field is ""

    Scenario: As a user I want to make changes on the sidebar but only apply them to the nested grid when I click the Apply button
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/NestedGridWithSidebar/eyJfaWQiOiIxODYifQ=="
        Then the "Field - NestedGrid with sidebar" titled page is displayed
        When the user selects the "field" bound nested grid field on the main page
        And the user selects row with text "96" in column with header "Id" in the nested grid field
        When the user expands the selected row of the nested grid field
        And the user selects row with text "10" in column with header "Id" in the nested grid field
        When the user expands the selected row of the nested grid field
        And the user selects the row with the following content in the nested grid field
            | columnHeader | cellText         |
            | Id           | 142              |
            | Quantity     | 25               |
            | Net Price    | 4,438.81         |
            | Product      | Hinge W Undercut |
        And the user clicks the "Edit invoice line on sidebar" action of the selected row in the nested grid field
        When the user selects the "Quantity" labelled numeric field on the sidebar
        And the value of the numeric field is "25"
        When the user writes "10" in the numeric field
        When the user selects the "field" bound nested grid field on the main page
        And the user selects the row with the following content in the nested grid field
            | columnHeader | cellText         |
            | Id           | 142              |
            | Quantity     | 25               |
            | Net Price    | 4,438.81         |
            | Product      | Hinge W Undercut |
        When the user clicks the "Apply" button of the dialog on the sidebar
        And the user selects the "field" bound nested grid field on the main page
        And the user selects the row with the following content in the nested grid field
            | columnHeader | cellText         |
            | Id           | 142              |
            | Quantity     | 10               |
            | Net Price    | 4,438.81         |
            | Product      | Hinge W Undercut |

    Scenario: As a user I want to make changes on the sidebar but only apply them to the nested grid when I click the Apply button - mobile
        Given the user opens the application on a mobile using the following link: "@sage/xtrem-show-case/NestedGridWithSidebar/eyJfaWQiOiIxODYifQ=="
        Then the "Field - NestedGrid with sidebar" titled page is displayed
        When the user selects the "field" bound nested grid field on the main page
        And the value of the "Id" labelled nested text field of the card 1 in the nested grid field is "96"
        When the user clicks the card 1 in the nested grid field
        Then the value of the "Id" labelled nested text field of the card 3 in the nested grid field is "10"
        When the user clicks the card 3 in the nested grid field
        Then the value of the "Id" labelled nested text field of the card 1 in the nested grid field is "142"
        When the user clicks the "Edit invoice line on sidebar" inline action button of the card 1 in the nested grid field
        When the user selects the "Quantity" labelled numeric field on a full width modal
        And the value of the numeric field is "25"
        When the user writes "10" in the numeric field
        When the user selects the "field" bound nested grid field on the main page
        Then the value of the "Quantity" labelled nested numeric field of the card 1 in the nested grid field is "25"
        When the user clicks the "Apply" button of the dialog on the sidebar
        And the user selects the "field" bound nested grid field on the main page
        Then the value of the "Quantity" labelled nested numeric field of the card 1 in the nested grid field is "10"

    Scenario: As a user I want to save the changes I make on the sidebar
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/NestedGridWithSidebar/eyJfaWQiOiIxODYifQ=="
        Then the "Field - NestedGrid with sidebar" titled page is displayed
        When the user selects the "field" bound nested grid field on the main page
        And the user selects row with text "96" in column with header "Id" in the nested grid field
        When the user expands the selected row of the nested grid field
        And the user selects row with text "10" in column with header "Id" in the nested grid field
        When the user expands the selected row of the nested grid field
        And the user selects the row with the following content in the nested grid field
            | columnHeader | cellText         |
            | Id           | 142              |
            | Quantity     | 25               |
            | Net Price    | 4,438.81         |
            | Product      | Hinge W Undercut |
        And the user clicks the "Edit invoice line on sidebar" action of the selected row in the nested grid field
        When the user selects the "Quantity" labelled numeric field on the sidebar
        And the value of the numeric field is "25"
        When the user writes "10" in the numeric field
        When the user selects the "field" bound nested grid field on the main page
        And the user selects the row with the following content in the nested grid field
            | columnHeader | cellText         |
            | Id           | 142              |
            | Quantity     | 25               |
            | Net Price    | 4,438.81         |
            | Product      | Hinge W Undercut |
        When the user clicks the "Apply" button of the dialog on the sidebar
        When the user selects the "field" bound nested grid field on the main page
        And the user selects the row with the following content in the nested grid field
            | columnHeader | cellText         |
            | Id           | 142              |
            | Quantity     | 10               |
            | Net Price    | 4,438.81         |
            | Product      | Hinge W Undercut |
        And the user clicks the "Save" labelled business action button on the main page
        And the user waits 1 second
        When the user navigates to the following link: "@sage/xtrem-show-case/NestedGridWithSidebar/eyJfaWQiOiIxODYifQ=="
        And the user selects the "field" bound nested grid field on the main page
        And the user selects row with text "96" in column with header "Id" in the nested grid field
        When the user expands the selected row of the nested grid field
        And the user selects row with text "10" in column with header "Id" in the nested grid field
        When the user expands the selected row of the nested grid field
        And the user selects the row with the following content in the nested grid field
            | columnHeader | cellText         |
            | Id           | 142              |
            | Quantity     | 10               |
            | Net Price    | 4,438.81         |
            | Product      | Hinge W Undercut |
        And the user clicks the "Edit invoice line on sidebar" action of the selected row in the nested grid field
        When the user selects the "Quantity" labelled numeric field on the sidebar
        And the value of the numeric field is "10"
        When the user writes "25" in the numeric field
        When the user clicks the "Apply" button of the dialog on the sidebar
        And the user clicks the "Save" labelled business action button on the main page
        And the user waits 1 second

    Scenario: As a user I want to save the changes I make on the sidebar - mobile
        Given the user opens the application on a mobile using the following link: "@sage/xtrem-show-case/NestedGridWithSidebar/eyJfaWQiOiIxODYifQ=="
        Then the "Field - NestedGrid with sidebar" titled page is displayed
        When the user selects the "field" bound nested grid field on the main page
        And the value of the "Id" labelled nested text field of the card 1 in the nested grid field is "96"
        When the user clicks the card 1 in the nested grid field
        Then the value of the "Id" labelled nested text field of the card 3 in the nested grid field is "10"
        When the user clicks the card 3 in the nested grid field
        Then the value of the "Id" labelled nested text field of the card 1 in the nested grid field is "142"
        When the user clicks the "Edit invoice line on sidebar" inline action button of the card 1 in the nested grid field
        When the user selects the "Quantity" labelled numeric field on a full width modal
        And the value of the numeric field is "25"
        When the user writes "10" in the numeric field
        When the user selects the "field" bound nested grid field on the main page
        And the value of the "Quantity" labelled nested numeric field of the card 1 in the nested grid field is "25"
        When the user clicks the "Apply" button of the dialog on a full width modal
        And the value of the "Quantity" labelled nested numeric field of the card 1 in the nested grid field is "10"
        When the user clicks the "Save" labelled business action button on a full width modal
        And a toast containing text "Record updated" is displayed
        And the user navigates to the following link: "@sage/xtrem-show-case/NestedGridWithSidebar/eyJfaWQiOiIxODYifQ=="
        And the user selects the "field" bound nested grid field on the main page
        And the value of the "Id" labelled nested text field of the card 1 in the nested grid field is "96"
        When the user clicks the card 1 in the nested grid field
        Then the value of the "Id" labelled nested text field of the card 3 in the nested grid field is "10"
        When the user clicks the card 3 in the nested grid field
        Then the value of the "Id" labelled nested text field of the card 1 in the nested grid field is "142"
        When the user clicks the "Edit invoice line on sidebar" inline action button of the card 1 in the nested grid field
        When the user selects the "Quantity" labelled numeric field on a full width modal
        And the value of the numeric field is "10"
        When the user writes "25" in the numeric field
        When the user clicks the "Apply" button of the dialog on a full width modal
        When the user clicks the "Save" labelled business action button on a full width modal
        And a toast containing text "Record updated" is displayed

    Scenario: As a user I want to cancel the changes I make on the sidebar
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/NestedGridWithSidebar/eyJfaWQiOiIxODYifQ=="
        Then the "Field - NestedGrid with sidebar" titled page is displayed
        And the user selects row with text "96" in column with header "Id" in the nested grid field
        When the user expands the selected row of the nested grid field
        And the user selects row with text "10" in column with header "Id" in the nested grid field
        When the user expands the selected row of the nested grid field
        And the user selects the row with the following content in the nested grid field
            | columnHeader | cellText         |
            | Id           | 142              |
            | Quantity     | 25               |
            | Net Price    | 4,438.81         |
            | Product      | Hinge W Undercut |
        And the user clicks the "Edit invoice line on sidebar" action of the selected row in the nested grid field
        When the user selects the "Quantity" labelled numeric field on the sidebar
        Then the value of the numeric field is "25"
        When the user writes "10" in the numeric field
        Then the value of the numeric field is "10"
        When the user clicks the "Cancel" button of the dialog on the sidebar
        And the user waits 1 second
        And the user selects the "field" bound nested grid field on the main page
        And the user selects the row with the following content in the nested grid field
            | columnHeader | cellText         |
            | Id           | 142              |
            | Quantity     | 25               |
            | Net Price    | 4,438.81         |
            | Product      | Hinge W Undercut |

    Scenario: As a user I want to cancel the changes I make on the sidebar - mobile
        Given the user opens the application on a mobile using the following link: "@sage/xtrem-show-case/NestedGridWithSidebar/eyJfaWQiOiIxODYifQ=="
        Then the "Field - NestedGrid with sidebar" titled page is displayed
        When the user selects the "field" bound nested grid field on the main page
        And the value of the "Id" labelled nested text field of the card 1 in the nested grid field is "96"
        When the user clicks the card 1 in the nested grid field
        Then the value of the "Id" labelled nested text field of the card 3 in the nested grid field is "10"
        When the user clicks the card 3 in the nested grid field
        Then the value of the "Id" labelled nested text field of the card 1 in the nested grid field is "142"
        When the user clicks the "Edit invoice line on sidebar" inline action button of the card 1 in the nested grid field
        When the user selects the "Quantity" labelled numeric field on a full width modal
        Then the value of the numeric field is "25"
        When the user writes "10" in the numeric field
        Then the value of the numeric field is "10"
        When the user clicks the "Cancel" button of the dialog on a full width modal
        And the user waits 1 second
        And the user selects the "field" bound nested grid field on the main page
        And the value of the "Quantity" labelled nested numeric field of the card 1 in the nested grid field is "25"

    Scenario: As a user I want the clean sidebar to close if I cancel it
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/NestedGridWithSidebar/eyJfaWQiOiIxODYifQ=="
        And the "Field - NestedGrid with sidebar" titled page is displayed
        And the user selects the "field" bound nested grid field on the main page
        And the user selects row with text "96" in column with header "Id" in the nested grid field
        When the user expands the selected row of the nested grid field
        And the user selects row with text "10" in column with header "Id" in the nested grid field
        When the user expands the selected row of the nested grid field
        And the user selects the row with the following content in the nested grid field
            | columnHeader | cellText         |
            | Id           | 142              |
            | Quantity     | 25               |
            | Net Price    | 4,438.81         |
            | Product      | Hinge W Undercut |
        And the user clicks the "Edit invoice line on sidebar" action of the selected row in the nested grid field
        When the user clicks the "Cancel" button of the dialog on the sidebar
        Then no dialogs are displayed

    Scenario: As a user I want the clean sidebar to close if I cancel it - mobile
        Given the user opens the application on a mobile using the following link: "@sage/xtrem-show-case/NestedGridWithSidebar/eyJfaWQiOiIxODYifQ=="
        Then the "Field - NestedGrid with sidebar" titled page is displayed
        When the user selects the "field" bound nested grid field on the main page
        And the value of the "Id" labelled nested text field of the card 1 in the nested grid field is "96"
        When the user clicks the card 1 in the nested grid field
        And the value of the "Id" labelled nested text field of the card 3 in the nested grid field is "10"
        When the user clicks the card 3 in the nested grid field
        And the value of the "Id" labelled nested text field of the card 1 in the nested grid field is "142"
        When the user clicks the "Edit invoice line on sidebar" inline action button of the card 1 in the nested grid field
        When the user clicks the "Cancel" button of the dialog on a full width modal
        Then no dialogs are displayed

    Scenario: As a user I want the dirty sidebar to display a warning if I close it
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/NestedGridWithSidebar/eyJfaWQiOiIxODYifQ=="
        And the "Field - NestedGrid with sidebar" titled page is displayed
        And the user selects the "field" bound nested grid field on the main page
        And the user selects row with text "96" in column with header "Id" in the nested grid field
        When the user expands the selected row of the nested grid field
        And the user selects row with text "10" in column with header "Id" in the nested grid field
        When the user expands the selected row of the nested grid field
        And the user selects the row with the following content in the nested grid field
            | columnHeader | cellText         |
            | Id           | 142              |
            | Quantity     | 25               |
            | Net Price    | 4,438.81         |
            | Product      | Hinge W Undercut |
        And the user clicks the "Edit invoice line on sidebar" action of the selected row in the nested grid field
        When the user selects the "Quantity" labelled numeric field on the sidebar
        When the user writes "10" in the numeric field
        When the user clicks the "Cancel" button of the dialog on the sidebar
        Then a warn dialog appears
        And the text in the body of the dialog is "Leave and discard your changes?"
        When the user clicks the "Discard" button of the Confirm dialog
        Then no dialogs are displayed

    Scenario: As a user I want the dirty sidebar to display a warning if I close it - mobile
        Given the user opens the application on a mobile using the following link: "@sage/xtrem-show-case/NestedGridWithSidebar/eyJfaWQiOiIxODYifQ=="
        Then the "Field - NestedGrid with sidebar" titled page is displayed
        When the user selects the "field" bound nested grid field on the main page
        And the value of the "Id" labelled nested text field of the card 1 in the nested grid field is "96"
        When the user clicks the card 1 in the nested grid field
        And the value of the "Id" labelled nested text field of the card 3 in the nested grid field is "10"
        When the user clicks the card 3 in the nested grid field
        And the value of the "Id" labelled nested text field of the card 1 in the nested grid field is "142"
        When the user clicks the "Edit invoice line on sidebar" inline action button of the card 1 in the nested grid field
        When the user selects the "Quantity" labelled numeric field on a full width modal
        When the user writes "10" in the numeric field
        When the user clicks the "Cancel" button of the dialog on a full width modal
        Then a warn dialog appears
        And the text in the body of the dialog is "Leave and discard your changes?"
        When the user clicks the "Discard" button of the Confirm dialog
        Then no dialogs are displayed

    Scenario: As a user I want to know if a field in my sidebar has errors
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/NestedGridWithSidebar/eyJfaWQiOiIxODYifQ=="
        And the "Field - NestedGrid with sidebar" titled page is displayed
        And the user selects the "field" bound nested grid field on the main page
        And the user selects row with text "96" in column with header "Id" in the nested grid field
        When the user expands the selected row of the nested grid field
        And the user selects row with text "10" in column with header "Id" in the nested grid field
        When the user expands the selected row of the nested grid field
        And the user selects the row with the following content in the nested grid field
            | columnHeader | cellText         |
            | Id           | 142              |
            | Quantity     | 25               |
            | Net Price    | 4,438.81         |
            | Product      | Hinge W Undercut |
        And the user clicks the "Edit invoice line on sidebar" action of the selected row in the nested grid field
        And selects the "Details section" labelled navigation anchor on the sidebar
        When the user selects the "Product *" labelled reference field on the sidebar
        And the user clears the reference field
        Then the "Product *" labelled reference field on the sidebar is invalid
        And the "Apply" button of the dialog is disabled on the sidebar

    Scenario: As a user I want to know if a field in my sidebar has errors - mobile
        Given the user opens the application on a mobile using the following link: "@sage/xtrem-show-case/NestedGridWithSidebar/eyJfaWQiOiIxODYifQ=="
        Then the "Field - NestedGrid with sidebar" titled page is displayed
        When the user selects the "field" bound nested grid field on the main page
        And the value of the "Id" labelled nested text field of the card 1 in the nested grid field is "96"
        When the user clicks the card 1 in the nested grid field
        And the value of the "Id" labelled nested text field of the card 3 in the nested grid field is "10"
        When the user clicks the card 3 in the nested grid field
        And the value of the "Id" labelled nested text field of the card 1 in the nested grid field is "142"
        When the user clicks the "Edit invoice line on sidebar" inline action button of the card 1 in the nested grid field
        And selects the "Details section" labelled navigation anchor on a full width modal
        When the user selects the "Product *" labelled reference field on a full width modal
        And the user clears the reference field
        Then the "Product *" labelled reference field on a full width modal is invalid
        And the "Apply" button of the dialog is disabled on a full width modal

    Scenario: As a user I want to remove a nested grid record from the sidebar
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/NestedGridWithSidebar/eyJfaWQiOiIxODYifQ=="
        Then the "Field - NestedGrid with sidebar" titled page is displayed
        When the user selects the "field" bound nested grid field on the main page
        And the user selects row with text "96" in column with header "Id" in the nested grid field
        When the user expands the selected row of the nested grid field
        And the user selects row with text "10" in column with header "Id" in the nested grid field
        When the user expands the selected row of the nested grid field
        And the user selects the row with the following content in the nested grid field
            | columnHeader | cellText         |
            | Id           | 142              |
            | Quantity     | 25               |
            | Net Price    | 4,438.81         |
            | Product      | Hinge W Undercut |
        And the user clicks the "Edit invoice line on sidebar" action of the selected row in the nested grid field
        Then the "Invoice line 142" titled sidebar is displayed
        Then the "Field - NestedGrid with sidebar" titled page is displayed
        When the user clicks the "remove" labelled more actions button in the sidebar header
        Then no dialogs are displayed
        And the "Field - NestedGrid with sidebar" titled page is displayed

    Scenario: As a user I want to remove a nested grid record from the sidebar - mobile
        Given the user opens the application on a mobile using the following link: "@sage/xtrem-show-case/NestedGridWithSidebar/eyJfaWQiOiIxODYifQ=="
        Then the "Field - NestedGrid with sidebar" titled page is displayed
        When the user selects the "field" bound nested grid field on the main page
        And the value of the "Id" labelled nested text field of the card 1 in the nested grid field is "96"
        When the user clicks the card 1 in the nested grid field
        And the value of the "Id" labelled nested text field of the card 3 in the nested grid field is "10"
        When the user clicks the card 3 in the nested grid field
        And the value of the "Id" labelled nested text field of the card 1 in the nested grid field is "142"
        When the user clicks the "Edit invoice line on sidebar" inline action button of the card 1 in the nested grid field
        When the user clicks the "remove" labelled more actions button on a full width modal
        Then no dialogs are displayed
        And the value of the "Id" labelled nested text field of the card 1 in the nested grid field is "141"

    Scenario: As a user I want to know if a reference field in my sidebar is working correctly on second level
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/NestedGridWithSidebar/eyJfaWQiOiIxODYifQ=="
        Then the "Field - NestedGrid with sidebar" titled page is displayed
        When the user selects the "field" bound nested grid field on the main page
        And the user selects row with text "96" in column with header "Id" in the nested grid field
        When the user expands the selected row of the nested grid field
        And the user selects row with text "10" in column with header "Id" in the nested grid field
        When the user expands the selected row of the nested grid field
        And the user selects the row with the following content in the nested grid field
            | columnHeader | cellText         |
            | Id           | 142              |
            | Quantity     | 25               |
            | Net Price    | 4,438.81         |
            | Product      | Hinge W Undercut |
        And the user clicks the "Edit invoice line on sidebar" action of the selected row in the nested grid field
        And selects the "Details section" labelled navigation anchor on the sidebar
        When the user selects the "Product *" labelled reference field on the sidebar
        And the user writes "Hinge" in the reference field
        And the user selects "Hinge W Undercut" in the reference field
        When the user clicks the "Apply" button of the dialog on the sidebar
        And the user selects the row with the following content in the nested grid field
            | columnHeader | cellText         |
            | Id           | 142              |
            | Quantity     | 25               |
            | Net Price    | 4,438.81         |
            | Product      | Hinge W Undercut |
