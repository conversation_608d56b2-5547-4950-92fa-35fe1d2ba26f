Feature: 8 Z prod notification center
    # Tests the notification center functionality in production environments, verifying notification creation, display, interactions, and dismissal behaviors


    Scenario: 1 - triggers and verification notification without more action
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/NotificationCenter"
        Then the "Notification center" titled page is displayed

        And the user stores the generated value with length 8 with the key "[ENV_NOTIFICATION_01]"

        And the user selects the "Title" labelled text field on the main page
        And the user writes "My Notification [ENV_NOTIFICATION_01]" in the text field

        And the user selects the "Description" labelled text area field on the main page
        And the user writes "My Notification [ENV_NOTIFICATION_01] description" in the text area field
        # And takes a screenshot

        And the user selects the "Level" labelled dropdown-list field on the main page
        When the user clicks in the dropdown-list field
        And the user selects "Info" in the dropdown-list field

        And the user selects the "Icon" labelled dropdown-list field on the main page
        When the user clicks in the dropdown-list field
        And the user selects "add" in the dropdown-list field

        And the user selects the "actions" bound table field on the main page
        When the user adds a new table row to the table field
        And the user selects the floating row of the table field
        And the user writes "Google" in the "Action title" labelled nested text field of the selected row in the table field
        And the user writes "http://google.com" in the "Link" labelled nested text field of the selected row in the table field
        And the user clicks the "Button style" labelled nested field of the selected row in the table field
        And the user selects "Primary" in the "Button style" labelled nested field of the selected row in the table field
        And the user presses Tab
        And the user presses Tab
        # And takes a screenshot

        And the user clicks the "Trigger notification" labelled business action button on the main page

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/NotificationCenter"
        Then the "Notification center" titled page is displayed

        Given the "unread" notifications icon in the actions header is displayed
        When the user clicks the notifications icon in the actions header

        Then the notification with title "My Notification [ENV_NOTIFICATION_01]" in the notification center is displayed
        And the user selects the notification card with title "My Notification [ENV_NOTIFICATION_01]"
        Then the title of the notification card is "My Notification [ENV_NOTIFICATION_01]"
        Then the relative date of the notification card is "less than a minute ago"
        Then the description of the notification card is "My Notification [ENV_NOTIFICATION_01] description"
        # And takes a screenshot

        When the user clicks the "Mark as read" notification card body action

        When the user clicks the "Google" notification card body action
        Then the text in the header of the dialog is "External link"
        And the user clicks the "Cancel" button of the external link confirmation dialog

        When the user clicks the "Delete" notification card header action

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/NotificationCenter"
        Then the "Notification center" titled page is displayed

        When the user clicks the notifications icon in the actions header

        Then the notification with title "My Notification [ENV_NOTIFICATION_01]" in the notification center is hidden

        And the user clicks the Close button of the sidebar


    Scenario: 2 - triggers and verification notification with more action
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/NotificationCenter"
        Then the "Notification center" titled page is displayed

        Given the user stores the generated value with length 8 with the key "[ENV_NOTIFICATION_02]"

        And the user selects the "Title" labelled text field on the main page
        And the user writes "My Notification [ENV_NOTIFICATION_02]" in the text field

        And the user selects the "Description" labelled text area field on the main page
        And the user writes "My Notification [ENV_NOTIFICATION_02] description" in the text area field
        # And takes a screenshot

        And the user selects the "Level" labelled dropdown-list field on the main page
        When the user clicks in the dropdown-list field
        And the user selects "Info" in the dropdown-list field

        And the user selects the "Icon" labelled dropdown-list field on the main page
        When the user clicks in the dropdown-list field
        And the user selects "add" in the dropdown-list field

        And the user selects the "actions" bound table field on the main page
        When the user adds a new table row to the table field
        And the user selects the floating row of the table field
        And the user writes "Google" in the "Action title" labelled nested text field of the selected row in the table field
        And the user writes "http://google.com" in the "Link" labelled nested text field of the selected row in the table field
        And the user clicks the "Button style" labelled nested field of the selected row in the table field
        And the user selects "Primary" in the "Button style" labelled nested field of the selected row in the table field
        And the user presses Tab
        And the user presses Tab
        And the user selects the floating row of the table field
        And the user writes "Yahoo" in the "Action title" labelled nested text field of the selected row in the table field
        And the user writes "http://yahoo.com" in the "Link" labelled nested text field of the selected row in the table field
        And the user clicks the "Button style" labelled nested field of the selected row in the table field
        And the user selects "Secondary" in the "Button style" labelled nested field of the selected row in the table field
        And the user presses Tab
        And the user presses Tab
        # And takes a screenshot

        And the user clicks the "Trigger notification" labelled business action button on the main page

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/NotificationCenter"
        Then the "Notification center" titled page is displayed

        Given the "unread" notifications icon in the actions header is displayed
        When the user clicks the notifications icon in the actions header

        Then the notification with title "My Notification [ENV_NOTIFICATION_02]" in the notification center is displayed
        And the user selects the notification card with title "My Notification [ENV_NOTIFICATION_02]"
        Then the title of the notification card is "My Notification [ENV_NOTIFICATION_02]"
        Then the relative date of the notification card is "less than a minute ago"
        Then the description of the notification card is "My Notification [ENV_NOTIFICATION_02] description"
        # And takes a screenshot

        When the user clicks the "Mark as read" notification card header action

        When the user clicks the "Google" notification card body action
        Then the text in the header of the dialog is "External link"
        And the user clicks the "Cancel" button of the external link confirmation dialog

        When the user clicks the "Yahoo" notification card body action
        Then the text in the header of the dialog is "External link"
        And the user clicks the "Cancel" button of the external link confirmation dialog

        When the user clicks the "Delete" notification card header action

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/NotificationCenter"
        Then the "Notification center" titled page is displayed

        When the user clicks the notifications icon in the actions header

        Then the notification with title "My Notification [ENV_NOTIFICATION_02]" in the notification center is hidden

        And the user clicks the Close button of the sidebar
