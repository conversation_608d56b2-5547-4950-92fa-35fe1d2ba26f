Feature: 5 Y dashboard widget table edition
    # Tests the editing functionality of existing table widgets, including modifying columns, filters, and display options

    @ClearDashboards
    Scenario: As a user I want to edit an table widget
        Given the user opens the application on a desktop
        And the dashboard page is displayed
        Then the "Create a dashboard to get started." subtitled empty dashboard is displayed
        When the user clicks the create button on the dashboard
        Then the dashboard creation dialog is displayed
        When the user selects the template with title "Showcase dashboard" in the dashboard creation dialog
        And the user clicks the "next" button in the dashboard creation dialog
        Then the "Showcase dashboard" titled dashboard in the dashboard editor is displayed
        When the user clicks the "createAWidget" labelled button in the dashboard editor navigation panel
        Then the "New widget" titled widget editor dialog is displayed
        And the value of the step title of the widget editor dialog is "1. Select a widget to get started"
        When the user writes "Test Table Widget" in the "basic-title" text field in the widget editor dialog
        And the user presses Enter
        And the user writes "My demo category" in the "basic-category" dropdown field in the widget editor dialog
        And the user presses Enter
        And the user writes "Show Case Provider" in the "basic-node" dropdown field in the widget editor dialog
        And the user presses Enter
        And the user selects the "TABLE" widget card in the widget editor dialog
        Then the "TABLE" widget card in the widget editor dialog is selected
        When the user clicks the "next" button in the widget editor dialog
        Then the value of the step title of the widget editor dialog is "2. Select the data to add to your widget"
        When the user selects the "Text field" tree-view element in the widget editor dialog
        Then the "next" button in the widget editor dialog is enabled
        When the user selects the "Integer field" tree-view element in the widget editor dialog
        And the user selects the "Date field" tree-view element in the widget editor dialog
        And the user selects the "Boolean field" tree-view element in the widget editor dialog
        And the user selects the "Min quantity" tree-view element in the widget editor dialog
        And the user clicks the "next" button in the widget editor dialog
        Then the value of the step title of the widget editor dialog is "3. Add your content"
        When the user clicks the "Add column" table button in the widget editor dialog
        And the user writes "Text field" in the "content-property" dropdown field of row "1" in the widget editor dialog
        And the user presses Enter
        Then the value of the "content-title" text field of row "1" in the widget editor dialog is "Text field"
        And the value of the "content-presentation" paragraph field of row "1" in the widget editor dialog is "Text"
        When the user clicks the "add" action button of row "1" in the widget editor dialog
        And the user writes "Date field" in the "content-property" dropdown field of row "2" in the widget editor dialog
        And the user presses Enter
        Then the value of the "content-title" text field of row "2" in the widget editor dialog is "Date field"
        And the value of the "content-presentation" paragraph field of row "2" in the widget editor dialog is "Date"
        When the user clicks the "add" action button of row "2" in the widget editor dialog
        And the user writes "Integer field" in the "content-property" dropdown field of row "3" in the widget editor dialog
        And the user presses Enter
        Then the value of the "content-title" text field of row "3" in the widget editor dialog is "Integer field"
        When the user writes "Numeric" in the "content-presentation" text field of row "3" in the widget editor dialog
        And the user presses Enter
        And the user clicks the "add" action button of row "3" in the widget editor dialog
        And the user writes "Min quantity" in the "content-property" dropdown field of row "4" in the widget editor dialog
        And the user writes "Progress" in the "content-presentation" dropdown field of row "4" in the widget editor dialog
        And the user presses Enter
        And the user clicks the "next" button in the widget editor dialog
        Then the value of the step title of the widget editor dialog is "4. Add your filters"
        When the user clicks the "Add filter" table button in the widget editor dialog
        And the user writes "Text field" in the "filter-property" dropdown field of row "1" in the widget editor dialog
        And the user presses Enter
        And the user writes "Contains" in the "filter-type" dropdown field of row "1" in the widget editor dialog
        And the user presses Enter
        And the user writes "a" in the "filter-value" text field of row "1" in the widget editor dialog
        And the user presses Enter
        And the user clicks the "add" action button of row "1" in the widget editor dialog
        When the user writes "Integer field" in the "filter-property" dropdown field of row "2" in the widget editor dialog
        And the user presses Enter
        And the user writes "Between" in the "filter-type" dropdown field of row "2" in the widget editor dialog
        And the user presses Enter
        And the user writes "2" in the "filter-value-min" text field of row "2" in the widget editor dialog
        And the user presses Enter
        Then the "Invalid integer" validation error of row "2" in the widget editor dialog is displayed
        When the user writes "1" in the "filter-value-min" text field of row "2" in the widget editor dialog
        And the user presses Enter
        And the user writes "4" in the "filter-value-max" text field of row "2" in the widget editor dialog
        And the user presses Enter
        And the user clicks the "add" action button of row "2" in the widget editor dialog
        And the user writes "Min quantity" in the "filter-property" dropdown field of row "3" in the widget editor dialog
        And the user presses Enter
        And the user writes "Less than" in the "filter-type" dropdown field of row "3" in the widget editor dialog
        And the user presses Enter
        And the user writes "10" in the "filter-value" dropdown field of row "3" in the widget editor dialog
        And the user presses Enter
        And the user clicks the "next" button in the widget editor dialog
        Then the value of the step title of the widget editor dialog is "5. Define sorting"
        When the user clicks the "Add a sort condition" table button in the widget editor dialog
        And the user writes "Text field" in the "sorting-property" dropdown field of row "1" in the widget editor dialog
        And the user presses Enter
        And the user writes "Ascending" in the "sorting-order" dropdown field of row "1" in the widget editor dialog
        And the user presses Enter
        And the user clicks the "add" action button of row "1" in the widget editor dialog
        And the user writes "Min quantity" in the "sorting-property" dropdown field of row "2" in the widget editor dialog
        And the user presses Enter
        And the user writes "Descending" in the "sorting-order" dropdown field of row "2" in the widget editor dialog
        And the user presses Enter
        When the user clicks the "next" button in the widget editor dialog
        Then the value of the step title of the widget editor dialog is "6. Create your layout"
        When the user ticks the "See all" checkbox field in the widget editor dialog
        Then the "layout-title-seeAllAction" text field in the widget editor dialog is enabled
        And the "layout-page-seeAllAction" dropdown field in the widget editor dialog is enabled
        When the user writes "Showcase - Provider" in the "layout-page-seeAllAction" dropdown field in the widget editor dialog
        And the user presses Enter
        Then the "See all" preview button in the widget editor dialog is displayed
        When the user ticks the "Create" checkbox field in the widget editor dialog
        Then the "layout-title-createAction" text field in the widget editor dialog is enabled
        And the "layout-page-createAction" dropdown field in the widget editor dialog is enabled
        Then the "Create" preview button in the widget editor dialog is displayed
        When the user writes "Showcase - Provider" in the "layout-page-createAction" dropdown field in the widget editor dialog
        And the user presses Enter
        Then the "Create" preview button in the widget editor dialog is displayed
        Then the "add" button in the widget editor dialog is enabled
        When the user clicks the "add" button in the widget editor dialog
        Then the "Showcase dashboard" titled dashboard in the dashboard editor is displayed
        And the "Test Table Widget" titled widget in the dashboard editor is displayed
        When the user clicks the "save" button in the dashboard editor footer
        Then a toast containing text "Dashboard saved." is displayed
        When the user dismisses all the toasts
        Then the "Showcase dashboard" titled dashboard is displayed
        And the "Test Table Widget" titled widget in the dashboard is displayed

        # edit the widget

        # checking the previously set values are displayed correctly
        When the user clicks the "Edit" labelled CRUD button in the dashboard action menu
        Then the "Test Table Widget" titled widget in the dashboard editor is displayed
        When the user selects the "Test Table Widget" titled table widget field in the dashboard editor
        And the user clicks the "edit" more actions button in the header of the tile-indicator widget field
        Then the "Edit widget" titled widget editor dialog is displayed
        And the value of the step title of the widget editor dialog is "1. Select a widget to get started"
        And the value of the "basic-title" text field in the widget editor dialog is "Test Table Widget"
        And the value of the "basic-category" dropdown field in the widget editor dialog is "My demo category"
        And the "basic-node" dropdown field in the widget editor dialog is hidden
        And the "INDICATOR_TILE" widget card in the widget editor dialog is hidden
        And the "TABLE" widget card in the widget editor dialog is hidden
        And the "next" button in the widget editor dialog is enabled
        When the user clicks the "next" button in the widget editor dialog
        Then the value of the step title of the widget editor dialog is "2. Select the data to add to your widget"
        And the "Boolean field" tree-view element in the widget editor dialog is checked
        And the "Date field" tree-view element in the widget editor dialog is checked
        And the "Integer field" tree-view element in the widget editor dialog is checked
        And the "Text field" tree-view element in the widget editor dialog is checked
        And the "next" button in the widget editor dialog is enabled
        When the user clicks the "next" button in the widget editor dialog
        Then the value of the step title of the widget editor dialog is "3. Add your content"
        And the value of the "content-property" dropdown field of row "1" in the widget editor dialog is "Text field"
        And the value of the "content-title" text field of row "1" in the widget editor dialog is "Text field"
        And the value of the "content-presentation" paragraph field of row "1" in the widget editor dialog is "Text"
        And the value of the "content-property" dropdown field of row "2" in the widget editor dialog is "Date field"
        And the value of the "content-title" text field of row "2" in the widget editor dialog is "Date field"
        And the value of the "content-presentation" paragraph field of row "2" in the widget editor dialog is "Date"
        And the value of the "content-property" dropdown field of row "3" in the widget editor dialog is "Integer field"
        And the value of the "content-title" text field of row "3" in the widget editor dialog is "Integer field"
        And the value of the "content-presentation" dropdown field of row "3" in the widget editor dialog is "Numeric"
        And the value of the "content-property" dropdown field of row "4" in the widget editor dialog is "Min quantity"
        And the value of the "content-title" text field of row "4" in the widget editor dialog is "Min quantity"
        And the value of the "content-presentation" dropdown field of row "4" in the widget editor dialog is "Progress"
        And the "next" button in the widget editor dialog is enabled
        When the user clicks the "next" button in the widget editor dialog
        Then the value of the step title of the widget editor dialog is "4. Add your filters"
        And the value of the "filter-property" dropdown field of row "1" in the widget editor dialog is "Text field"
        And the value of the "filter-type" dropdown field of row "1" in the widget editor dialog is "Contains"
        And the value of the "filter-value" text field of row "1" in the widget editor dialog is "a"
        And the value of the "filter-property" dropdown field of row "2" in the widget editor dialog is "Integer field"
        And the value of the "filter-type" dropdown field of row "2" in the widget editor dialog is "Between"
        And the value of the "filter-value-min" text field of row "2" in the widget editor dialog is "1"
        And the value of the "filter-value-max" text field of row "2" in the widget editor dialog is "4"
        And the value of the "filter-property" dropdown field of row "3" in the widget editor dialog is "Min quantity"
        And the value of the "filter-type" dropdown field of row "3" in the widget editor dialog is "Less than"
        And the value of the "filter-value" text field of row "3" in the widget editor dialog is "10"
        And the "next" button in the widget editor dialog is enabled
        When the user clicks the "next" button in the widget editor dialog
        Then the value of the step title of the widget editor dialog is "5. Define sorting"
        And the value of the "sorting-property" dropdown field of row "1" in the widget editor dialog is "Text field"
        And the value of the "sorting-order" dropdown field of row "1" in the widget editor dialog is "Ascending"
        And the value of the "sorting-property" dropdown field of row "2" in the widget editor dialog is "Min quantity"
        And the value of the "sorting-order" dropdown field of row "2" in the widget editor dialog is "Descending"
        And the "next" button in the widget editor dialog is enabled
        When the user clicks the "next" button in the widget editor dialog
        Then the value of the step title of the widget editor dialog is "6. Create your layout"
        And the "See all" checkbox field in the widget editor dialog is checked
        And the value of the "layout-title-seeAllAction" text field in the widget editor dialog is "See all"
        And the value of the "layout-page-seeAllAction" dropdown field in the widget editor dialog is "ShowCase - Provider"
        And the "See all" preview button in the widget editor dialog is displayed
        And the "Create" checkbox field in the widget editor dialog is checked
        And the value of the "layout-title-createAction" text field in the widget editor dialog is "Create"
        And the value of the "layout-page-createAction" dropdown field in the widget editor dialog is "ShowCase - Provider"
        And the "Create" preview button in the widget editor dialog is displayed
        And the "update" button in the widget editor dialog is enabled

        # modifying the values
        When the user clicks the "previous" button in the widget editor dialog
        Then the value of the step title of the widget editor dialog is "5. Define sorting"
        When the user clicks the "previous" button in the widget editor dialog
        Then the value of the step title of the widget editor dialog is "4. Add your filters"
        When the user clicks the "previous" button in the widget editor dialog
        Then the value of the step title of the widget editor dialog is "3. Add your content"
        When the user clicks the "previous" button in the widget editor dialog
        Then the value of the step title of the widget editor dialog is "2. Select the data to add to your widget"
        When the user clicks the "previous" button in the widget editor dialog
        Then the value of the step title of the widget editor dialog is "1. Select a widget to get started"
        When the user writes "Edited Test Table Widget" in the "basic-title" text field in the widget editor dialog
        And the user presses Enter
        And the user writes "My Other Demo Category" in the "basic-category" dropdown field in the widget editor dialog
        And the user presses Enter
        When the user clicks the "next" button in the widget editor dialog
        Then the value of the step title of the widget editor dialog is "2. Select the data to add to your widget"
        When the user unselects the "Min quantity" tree-view element in the widget editor dialog
        And the user selects the "Decimal field" tree-view element in the widget editor dialog
        And the user clicks the "next" button in the widget editor dialog
        Then the value of the step title of the widget editor dialog is "3. Add your content"
        When the user clicks the "add" action button of row "3" in the widget editor dialog
        And the user writes "Decimal field" in the "content-property" dropdown field of row "4" in the widget editor dialog
        And the user presses Enter
        Then the value of the "content-title" dropdown field of row "4" in the widget editor dialog is "Decimal field"
        Then the "content-presentation" dropdown field of row "4" in the widget editor dialog is enabled
        And the "content-formatting" text field of row "4" in the widget editor dialog is enabled
        When the user writes "Numeric" in the "content-presentation" dropdown field of row "4" in the widget editor dialog
        And the user presses Enter
        And the user writes "2" in the "content-formatting" text field of row "4" in the widget editor dialog
        And the user presses Enter
        And the user clicks the "next" button in the widget editor dialog
        Then the value of the step title of the widget editor dialog is "4. Add your filters"
        When the user clicks the "add" action button of row "2" in the widget editor dialog
        And the user writes "Decimal field" in the "filter-property" dropdown field of row "3" in the widget editor dialog
        And the user writes "Does not equal" in the "filter-type" dropdown field of row "3" in the widget editor dialog
        And the user writes "8" in the "filter-value" text field of row "3" in the widget editor dialog
        And the user clicks the "next" button in the widget editor dialog
        Then the value of the step title of the widget editor dialog is "5. Define sorting"
        When the user clicks the "add" action button of row "1" in the widget editor dialog
        And the user writes "Decimal field" in the "sorting-property" dropdown field of row "2" in the widget editor dialog
        And the user presses Enter
        And the user writes "Ascending" in the "sorting-order" dropdown field of row "2" in the widget editor dialog
        And the user presses Enter
        And the user clicks the "next" button in the widget editor dialog
        Then the value of the step title of the widget editor dialog is "6. Create your layout"
        When the user unticks the "See all" checkbox field in the widget editor dialog
        Then the "layout-title-seeAllAction" dropdown field in the widget editor dialog is disabled
        And the "layout-page-seeAllAction" dropdown field in the widget editor dialog is disabled
        And the "See all" preview button in the widget editor dialog is hidden
        When the user ticks the "See all" checkbox field in the widget editor dialog
        And the user writes "Header Section" in the "layout-page-seeAllAction" dropdown field in the widget editor dialog
        And the user presses Enter
        Then the "See all" preview button in the widget editor dialog is displayed
        When the user unticks the "Create" checkbox field in the widget editor dialog
        Then the "layout-title-createAction" text field in the widget editor dialog is disabled
        And the "layout-page-createAction" dropdown field in the widget editor dialog is disabled
        And the "Create" preview button in the widget editor dialog is hidden
        When the user ticks the "Create" checkbox field in the widget editor dialog
        And the user writes "Header Section" in the "layout-page-createAction" dropdown field in the widget editor dialog
        Then the "Create" preview button in the widget editor dialog is displayed
        When the user clicks the "update" button in the widget editor dialog
        Then the "Showcase dashboard" titled dashboard in the dashboard editor is displayed
        Then the "Edited Test Table Widget" titled widget in the dashboard editor is displayed
        When the user clicks the "save" button in the dashboard editor footer
        And the user dismisses all the toasts
        Then the "Showcase dashboard" titled dashboard is displayed
        And the "Edited Test Table Widget" titled widget in the dashboard is displayed
