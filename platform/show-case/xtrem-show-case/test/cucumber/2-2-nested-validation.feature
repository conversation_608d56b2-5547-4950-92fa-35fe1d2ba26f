Feature: 2-2 Nested validation
    # Tests validation error display when the table contains errors.

    Scenario: As a user I want the validation errors of the grid row block to show up on the table
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TableValidation/eyJfaWQiOiIyIn0="
        # We just write into the field to trigger the on row click event
        And the user selects the "Table title" labelled table field on the main page
        And the user selects the row 3 of the table field
        When the user writes "Apple - Macintosh" in the "Product" labelled nested text field of the selected row in the table field
        And the user selects the "Product" labelled text field on the detail panel
        Then the value of the text field is "Apple - Macintosh"
        Then the "Table title" labelled table field on the main page is valid
        And the user selects the "Product" labelled text field on the detail panel
        Then the text field is valid
        And the user clears the text field
        And the user blurs the text field
        Then the "Table title" labelled table field on the main page is invalid
        And the user selects the "Product" labelled text field on the detail panel
        Then the text field is invalid
        When the user writes "abc" in the text field
        And the user blurs the text field
        Then the "Table title" labelled table field on the main page is valid
        And the user selects the "Product" labelled text field on the detail panel
        Then the text field is valid

    Sc<PERSON>rio: As a user I want the validation errors of the table to show up on the grid row block
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TableValidation/eyJfaWQiOiIyIn0="
        And the user selects the "Table title" labelled table field on the main page
        And the user selects the row 2 of the table field
        Then the value of the "Product" labelled nested text field of the selected row in the table field is "Appetizer - Cheese Bites"
        Then the "Table title" labelled table field on the main page is valid
        And the user selects the "Table title" labelled table field on the main page
        And the user selects the row 2 of the table field
        When the user writes "abc" in the "Product" labelled nested text field of the selected row in the table field
        Then the "Table title" labelled table field on the main page is valid
        And the user selects the "Product" labelled text field on the detail panel
        Then the value of the text field is "abc"
        Then the text field is valid
        And the user selects the "Table title" labelled table field on the main page
        And the user selects the row 2 of the table field
        When the user writes "" in the "Product" labelled nested text field of the selected row in the table field
        Then the "Table title" labelled table field on the main page is invalid
        And the user selects the "Product" labelled text field on the detail panel
        Then the text field is invalid
        And the user selects the "Table title" labelled table field on the main page
        And the user selects the row 2 of the table field
        When the user writes "abc" in the "Product" labelled nested text field of the selected row in the table field
        Then the "Table title" labelled table field on the main page is valid
        And the user selects the "Product" labelled text field on the detail panel
        Then the text field is valid

    Scenario: As a user I want to validate mandatory reference fields
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-show-case/TableValidation/eyJfaWQiOiIyIn0="
        And the user selects the "Table title" labelled table field on the main page
        And the user selects the row 1 of the table field
        Then the value of the "Provider" labelled nested reference field of the selected row in the table field is "Amazon"
        When the user writes "" in the "Provider" labelled nested reference field of the selected row in the table field
        And the user presses Enter
        When the user clicks the "Save" labelled business action button on the main page
        Then a validation error message is displayed containing text
            """"
            Validation errors
            You have 1 error in the following grid: Table title.
            Id: 313 - Provider: You need to select or enter a value.
            """
        Then the "Table title" labelled table field on the main page is invalid

    Scenario: As a user I want the validation errors from the server displayed
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TableValidation/eyJfaWQiOiIyIn0="
        And the user selects the "Table title" labelled table field on the main page
        And the user selects the row 12 of the table field
        Then the value of the "Product" labelled nested text field of the selected row in the table field is "Beef - Tongue, Fresh"
        # We just write into the field to trigger the on row click event and make the row dirty
        When the user writes "Beef - Tongue, Fresh" in the "Product" labelled nested text field of the selected row in the table field
        And the user selects the "Ordered Quantity" labelled numeric field on the detail panel
        Then the numeric field is valid
        And the user selects the "Min Quantity" labelled numeric field on the main page
        And the user writes "1" in the numeric field
        And the user selects the "Ordered Quantity" labelled numeric field on the detail panel
        Then the numeric field is valid
        When the user clicks the "Save" labelled business action button on the main page
        Then a validation error message is displayed containing text
            """
            Validation errors
            You have 11 errors in the following grid: Table title.
            Id: 276 - Ordered Quantity: Enter a value greater than 1 in the  field.
            Id: 282 - Ordered Quantity: Enter a value greater than 1 in the  field.
            Id: 292 - Ordered Quantity: Enter a value greater than 1 in the  field.
            Id: 308 - Ordered Quantity: Enter a value greater than 1 in the  field.
            Id: 337 - Ordered Quantity: Enter a value greater than 1 in the  field.
            Id: 365 - Ordered Quantity: Enter a value greater than 1 in the  field.
            Id: 372 - Ordered Quantity: Enter a value greater than 1 in the  field.
            Id: 466 - Ordered Quantity: Enter a value greater than 1 in the  field.
            Id: 468 - Ordered Quantity: Enter a value greater than 1 in the  field.
            Id: 267 - Ordered Quantity: Enter a value greater than 1 in the  field.
            and 1 more error
            """
        And the user selects the "Ordered Quantity" labelled numeric field on the detail panel
        Then the numeric field is invalid
        Then the "Table title" labelled table field on the main page is invalid
    # Scenario: As a user I want to filter a table by validation errors
    #     Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TableValidation/eyJfaWQiOiIyIn0="
    #     When the user writes "5" to the "Min Quantity" labelled numeric field on the main page
    #     And the user selects the "Save" labelled business action button on the main page
    #     Then a toast containing text "Validation failed" is displayed
    #     And a total of 52 errors is found in the "Table title" labelled table field in the main page
    #     When the user dismisses all the toasts
    #     And the user clicks on the "Product" labelled nested text field of row 1 of the "Table title" labelled table field in the main page
    #     And the user filters by errors the "Table title" labelled table field in the main page
    #     And the value of the "Id" labelled nested text field of row 19 in the "Table title" labelled table field is "419"
    #     And the value of the "Id" labelled nested text field of row 20 in the "Table title" labelled table field is "300"
    #     And the value of the "Id" labelled nested text field of row 1 in the "Table title" labelled table field is "250"
    #     And the value of the "Id" labelled nested text field of row 2 in the "Table title" labelled table field is "333"
    #     When the user filters the "product" bound column in the "Table title" labelled table field with value "wine" in the main page
    #     And the user clicks on the "Product" labelled nested text field of row 3 of the "Table title" labelled table field in the main page
    #     Then the value of the "Id" labelled nested text field of row 1 in the "Table title" labelled table field is "435"
    #     And the value of the "Id" labelled nested text field of row 2 in the "Table title" labelled table field is "316"
    #     And the value of the "Id" labelled nested text field of row 3 in the "Table title" labelled table field is "276"
    #     When the user clicks on the "description" bound column of the "Table title" labelled table field in the main page
    #     Then the value of the "Id" labelled nested text field of row 1 in the "Table title" labelled table field is "316"
    #     And the value of the "Id" labelled nested text field of row 2 in the "Table title" labelled table field is "276"
    #     And the value of the "Id" labelled nested text field of row 3 in the "Table title" labelled table field is "435"
    #     When the user unfilters by errors the "Table title" labelled table field in the main page
    #     Then the value of the "Id" labelled nested text field of row 1 in the "Table title" labelled table field is "408"
    #     And the value of the "Id" labelled nested text field of row 2 in the "Table title" labelled table field is "284"
    #     And the value of the "Id" labelled nested text field of row 19 in the "Table title" labelled table field is "480"
    #     And the value of the "Id" labelled nested text field of row 20 in the "Table title" labelled table field is "333"

    Scenario: As a user I want the validation errros from the disapear when I correct them
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TableValidation/eyJfaWQiOiIyIn0="
        And the user selects the "Table title" labelled table field on the main page
        And the user selects the row 12 of the table field
        Then the value of the "Product" labelled nested text field of the selected row in the table field is "Beef - Tongue, Fresh"
        # We just write into the field to trigger the on row click event and make the row dirty
        When the user writes "Beef - Tongue, Fresh" in the "Product" labelled nested text field of the selected row in the table field
        And the user selects the "Min Quantity" labelled numeric field on the main page
        And the user writes "1" in the numeric field
        When the user clicks the "Save" labelled business action button on the main page
        And the user selects the "Ordered Quantity" labelled numeric field on the detail panel
        Then the numeric field is invalid
        And the user writes "2" in the numeric field
        And the user selects the "Ordered Quantity" labelled numeric field on the detail panel
        Then the numeric field is valid
        When the user clicks the "Save" labelled business action button on the main page
        And the user selects the "Ordered Quantity" labelled numeric field on the detail panel
        Then the numeric field is valid

    Scenario: As a user I want the validation errors from the client and the server to display on the same record
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TableValidation/eyJfaWQiOiIyIn0="
        And the user selects the "Table title" labelled table field on the main page
        And the user selects the row 12 of the table field
        Then the value of the "Product" labelled nested text field of the selected row in the table field is "Beef - Tongue, Fresh"
        # We just write into the field to trigger the on row click event and make the row dirty
        And the user selects the row 12 of the table field
        When the user writes "Beef - Tongue, Fresh" in the "Product" labelled nested text field of the selected row in the table field
        And the user selects the "Min Quantity" labelled numeric field on the main page
        And the user writes "1" in the numeric field
        When the user clicks the "Save" labelled business action button on the main page
        And the user selects the "Ordered Quantity" labelled numeric field on the detail panel
        Then the numeric field is invalid
        And the user selects the "Product" labelled text field on the detail panel
        Then the text field is valid
        When the user clears the text field
        Then the text field is invalid
        And the user selects the "Ordered Quantity" labelled numeric field on the detail panel
        Then the numeric field is invalid
        And the user selects the "Product" labelled text field on the detail panel
        And the user writes "abc" in the text field
        Then the text field is valid
        And the user selects the "Ordered Quantity" labelled numeric field on the detail panel
        Then the numeric field is invalid

    Scenario: As a user I want the validation errors from the server displayed on pages that were not loaded at the time of saving
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TableValidation/eyJfaWQiOiIyIn0="
        And the user waits 1 seconds
        And the user selects the "Min Quantity" labelled numeric field on the main page
        And the user writes "1" in the numeric field
        When the user clicks the "Save" labelled business action button on the main page
        Then a validation error message is displayed containing text
            """
            Validation errors
            """
        And the user selects the "Table title" labelled table field on the main page
        When the user clicks the next page button of the table field
        And the user clicks the next page button of the table field
        And the user selects the row 58 of the table field
        Then the value of the "Product" labelled nested text field of the selected row in the table field is "Cornstarch"
        # We just write into the field to trigger the on row click event
        When the user writes "Cornstarch" in the "Product" labelled nested text field of the selected row in the table field
        And the user selects the "Ordered Quantity" labelled numeric field on the detail panel
        Then the numeric field is invalid
        # We check an other record to ensure that the error is not applied to those which are valid
        And the user selects the "Table title" labelled table field on the main page
        And the user selects the row 57 of the table field
        Then the value of the "Product" labelled nested text field of the selected row in the table field is "Cookie Chocolate Chip With"
        # We just write into the field to trigger the on row click event
        When the user writes "Cookie Chocolate Chip With" in the "Product" labelled nested text field of the selected row in the table field
        And the user selects the "Ordered Quantity" labelled numeric field on the detail panel
        Then the numeric field is valid

    Scenario: As a user I want the standard save action not to display a loader if the validation fails
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TableValidation/eyJfaWQiOiIyIn0="
        And the user selects the "Table title" labelled table field on the main page
        And the user selects the row 12 of the table field
        Then the value of the "Product" labelled nested text field of the selected row in the table field is "Beef - Tongue, Fresh"
        # We just write into the field to trigger the on row click event and make the row dirty
        When the user writes "Beef - Tongue, Fresh" in the "Product" labelled nested text field of the selected row in the table field
        And the user selects the "Ordered Quantity" labelled numeric field on the detail panel
        Then the numeric field is valid
        And the user selects the "Min Quantity" labelled numeric field on the main page
        And the user writes "1" in the numeric field
        And the user selects the "Ordered Quantity" labelled numeric field on the detail panel
        Then the numeric field is valid
        When the user clicks the "Save" labelled business action button on the main page
        Then a validation error message is displayed containing text
            """
            Validation errors
            You have 11 errors in the following grid: Table title.
            Id: 276 - Ordered Quantity: Enter a value greater than 1 in the  field.
            Id: 282 - Ordered Quantity: Enter a value greater than 1 in the  field.
            Id: 292 - Ordered Quantity: Enter a value greater than 1 in the  field.
            Id: 308 - Ordered Quantity: Enter a value greater than 1 in the  field.
            Id: 337 - Ordered Quantity: Enter a value greater than 1 in the  field.
            Id: 365 - Ordered Quantity: Enter a value greater than 1 in the  field.
            Id: 372 - Ordered Quantity: Enter a value greater than 1 in the  field.
            Id: 466 - Ordered Quantity: Enter a value greater than 1 in the  field.
            Id: 468 - Ordered Quantity: Enter a value greater than 1 in the  field.
            Id: 267 - Ordered Quantity: Enter a value greater than 1 in the  field.
            and 1 more error
            """
        And the user selects the "Ordered Quantity" labelled numeric field on the detail panel
        Then the numeric field is invalid
        Then the "Table title" labelled table field on the main page is invalid
        When the user dismisses all the toasts
        And the user clicks the "Save" labelled business action button on the main page
        And the user selects the "Min Quantity" labelled numeric field on the main page
        And the user writes "2" in the numeric field
        And the user writes "1" in the numeric field
        When the user clicks the "Save" labelled business action button on the main page
        Then a validation error message is displayed containing text
            """
            Validation errors
            You have 11 errors in the following grid: Table title.
            Id: 276 - Ordered Quantity: Enter a value greater than 1 in the  field.
            Id: 282 - Ordered Quantity: Enter a value greater than 1 in the  field.
            Id: 292 - Ordered Quantity: Enter a value greater than 1 in the  field.
            Id: 308 - Ordered Quantity: Enter a value greater than 1 in the  field.
            Id: 337 - Ordered Quantity: Enter a value greater than 1 in the  field.
            Id: 365 - Ordered Quantity: Enter a value greater than 1 in the  field.
            Id: 372 - Ordered Quantity: Enter a value greater than 1 in the  field.
            Id: 466 - Ordered Quantity: Enter a value greater than 1 in the  field.
            Id: 468 - Ordered Quantity: Enter a value greater than 1 in the  field.
            Id: 267 - Ordered Quantity: Enter a value greater than 1 in the  field.
            and 1 more error
            """

    Scenario: As a user I want to be able to reattempt saving if the table contains server side errors (simple)
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TableValidation/eyJfaWQiOiIyIn0="
        And the user selects the "Table title" labelled table field on the main page
        And the user selects the row 12 of the table field
        Then the value of the "Product" labelled nested text field of the selected row in the table field is "Beef - Tongue, Fresh"
        # We just write into the field to trigger the on row click event and make the row dirty
        When the user writes "Beef - Tongue, Fresh" in the "Product" labelled nested text field of the selected row in the table field
        And the user selects the "Ordered Quantity" labelled numeric field on the detail panel
        Then the numeric field is valid
        And the user selects the "Min Quantity" labelled numeric field on the main page
        And the user writes "1" in the numeric field
        And the user selects the "Ordered Quantity" labelled numeric field on the detail panel
        Then the numeric field is valid
        When the user clicks the "Save" labelled business action button on the main page
        Then a validation error message is displayed containing text
            """
            Validation errors
            You have 11 errors in the following grid: Table title.
            Id: 276 - Ordered Quantity: Enter a value greater than 1 in the  field.
            Id: 282 - Ordered Quantity: Enter a value greater than 1 in the  field.
            Id: 292 - Ordered Quantity: Enter a value greater than 1 in the  field.
            Id: 308 - Ordered Quantity: Enter a value greater than 1 in the  field.
            Id: 337 - Ordered Quantity: Enter a value greater than 1 in the  field.
            Id: 365 - Ordered Quantity: Enter a value greater than 1 in the  field.
            Id: 372 - Ordered Quantity: Enter a value greater than 1 in the  field.
            Id: 466 - Ordered Quantity: Enter a value greater than 1 in the  field.
            Id: 468 - Ordered Quantity: Enter a value greater than 1 in the  field.
            Id: 267 - Ordered Quantity: Enter a value greater than 1 in the  field.
            and 1 more error
            """
        And the user selects the "Ordered Quantity" labelled numeric field on the detail panel
        Then the numeric field is invalid
        Then the "Table title" labelled table field on the main page is invalid
        When the user dismisses all the toasts
        And the user selects the "Min Quantity" labelled numeric field on the main page
        And the user writes "0" in the numeric field
        When the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed

    Scenario: As a user I want to be able to reattempt saving if the table contains server side errors (complex)
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TableValidation/eyJfaWQiOiIyIn0="
        And the user selects the "Min Quantity" labelled numeric field on the main page
        And the user writes "1" in the numeric field
        When the user clicks the "Save" labelled business action button on the main page
        Then a validation error message is displayed containing text
            """
            Validation errors
            You have 11 errors in the following grid: Table title.
            Id: 276 - Ordered Quantity: Enter a value greater than 1 in the  field.
            Id: 282 - Ordered Quantity: Enter a value greater than 1 in the  field.
            Id: 292 - Ordered Quantity: Enter a value greater than 1 in the  field.
            Id: 308 - Ordered Quantity: Enter a value greater than 1 in the  field.
            Id: 337 - Ordered Quantity: Enter a value greater than 1 in the  field.
            Id: 365 - Ordered Quantity: Enter a value greater than 1 in the  field.
            Id: 372 - Ordered Quantity: Enter a value greater than 1 in the  field.
            Id: 466 - Ordered Quantity: Enter a value greater than 1 in the  field.
            Id: 468 - Ordered Quantity: Enter a value greater than 1 in the  field.
            Id: 267 - Ordered Quantity: Enter a value greater than 1 in the  field.
            and 1 more error
            """
        Then the "Table title" labelled table field on the main page is invalid
        When the user dismisses all the toasts
        And the user selects the "Integer" labelled numeric field on the main page
        And the user clears the numeric field
        Then the numeric field is invalid
        When the user clicks the "Save" labelled business action button on the main page
        Then a validation error message is displayed containing text
            """
            Validation errors

            Integer: You need to enter a value.

            You have 11 errors in the following grid: Table title.
            Id: 276 - Ordered Quantity: Enter a value greater than 1 in the  field.
            Id: 282 - Ordered Quantity: Enter a value greater than 1 in the  field.
            Id: 292 - Ordered Quantity: Enter a value greater than 1 in the  field.
            Id: 308 - Ordered Quantity: Enter a value greater than 1 in the  field.
            Id: 337 - Ordered Quantity: Enter a value greater than 1 in the  field.
            Id: 365 - Ordered Quantity: Enter a value greater than 1 in the  field.
            Id: 372 - Ordered Quantity: Enter a value greater than 1 in the  field.
            Id: 466 - Ordered Quantity: Enter a value greater than 1 in the  field.
            Id: 468 - Ordered Quantity: Enter a value greater than 1 in the  field.
            Id: 267 - Ordered Quantity: Enter a value greater than 1 in the  field.
            and 1 more error
            """
        When the user dismisses all the toasts
        And the user selects the "Integer" labelled numeric field on the main page
        And the user writes "2" in the numeric field
        Then the numeric field is valid
        And the user selects the "Min Quantity" labelled numeric field on the main page
        And the user writes "0" in the numeric field
        When the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed

    Scenario: As a user I want to get updated validation errors totals after row removals
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TableValidation/eyJfaWQiOiIyIn0="
        And the user selects the "Min Quantity" labelled numeric field on the main page
        And the user writes "1" in the numeric field
        When the user clicks the "Save" labelled business action button on the main page
        Then a validation error message is displayed containing text
            """
            Validation errors
            """
        And the user selects the "Table title" labelled table field on the main page
        Then a total of "11" errors is found in the table field
        And the user dismisses all the toasts
        When the user selects the "Table title" labelled table field on the main page
        And the user selects the row 20 of the table field
        And the user clicks the "Delete" dropdown action of the selected row of the table field
        Then a total of "10" errors is found in the table field
