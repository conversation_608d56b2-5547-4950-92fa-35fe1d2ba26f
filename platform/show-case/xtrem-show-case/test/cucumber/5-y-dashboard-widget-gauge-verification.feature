Feature: 5 Y dashboard widget gauge verification
    # Tests the verification of gauge widget values and display in dashboards, ensuring correct representation of percentage-based metrics

    @ClearDashboards
    Scenario: As an ATP XTreeM user I can verify the value of the gauge widget
        XT-41483
        Given the user opens the application on a desktop
        #Create the dashboard
        And the dashboard page is displayed
        Then the "Create a dashboard to get started." subtitled empty dashboard is displayed
        When the user clicks the create button on the dashboard

        #Select showcase dashboard
        When the user selects the template with title "Showcase dashboard" in the dashboard creation dialog
        Then the template with title "Showcase dashboard" in the dashboard creation dialog is selected
        When the user clicks the "Next" button in the dashboard creation dialog


        #Verify the gauge widgets in the Dashboard editor
        Then the "Showcase dashboard" titled dashboard in the dashboard editor is displayed

        And the user selects the "Ratio of administrators" titled gauge widget field in the dashboard editor
        Then the value of the gauge widget field is "4%"
        # And takes a screenshot

        And the user clicks the "cancel" button in the dashboard editor footer
        Then the "Showcase dashboard" titled dashboard is displayed

        #Verify the gauge widgets in the Dashboard
        And the user selects the "Ratio of administrators" titled gauge widget field in the dashboard
        Then the value of the gauge widget field is "4%"
        # And takes a screenshot
        And the user waits 3 seconds
