As a user I want to see that the contact cards widget is displayed correctly
Given  the user opens the application on a HD desktop
When  the user clicks the create button on the dashboard
And  the user selects the template with title "Blank template" in the dashboard creation dialog
And  the user clicks the "next" button in the dashboard creation dialog
Then  the "New dashboard" titled dashboard in the dashboard editor is displayed
When  the user clicks the Add button of the "Contact cards" titled widget card in the dashboard editor navigation panel
And  the user selects the "Contact cards" titled contact-card widget field in the dashboard editor
And  the user increases the widget field by 50,280 pixels
And  the user clicks the "Save" button in the dashboard editor footer
Then  the "New dashboard" titled dashboard is displayed
When  the user selects the "Contact cards" titled contact-card widget field in the dashboard
Then  the value of the "Select contact" filter dropdown of the contact-card widget field is "John Doe"
And  element with test id "db-widget-container-contactCards" looks as before
When  the user clicks the "Switch to site view" toggle button in the header of the contact-card widget field
Then  the value of the "Select address" filter dropdown of the contact-card widget field is "Av Diagonal 200BarcelonaSpain"
And  element with test id "db-widget-container-contactCards" looks as before
When  the user selects "Carrer Jesús Serra Santamans 2 Sant Cugat del Vallès Spain" in the "Select address" filter dropdown of the contact-card widget field
Then  the value of the "Select address" filter dropdown of the contact-card widget field is "Carrer Jesús Serra Santamans 2Sant Cugat del VallèsSpain"
When  the user selects "Jane Doe" in the "Select contact" filter dropdown of the contact-card widget field
Then  the value of the "Select contact" filter dropdown of the contact-card widget field is "Jane Doe"
And  element with test id "db-widget-container-contactCards" looks as before
When  the user clicks the "Switch to contact view" toggle button in the header of the contact-card widget field
And  the user selects "John Smith" in the "Select contact" filter dropdown of the contact-card widget field
Then  the value of the "Select contact" filter dropdown of the contact-card widget field is "John Smith"
And  element with test id "db-widget-container-contactCards" looks as before