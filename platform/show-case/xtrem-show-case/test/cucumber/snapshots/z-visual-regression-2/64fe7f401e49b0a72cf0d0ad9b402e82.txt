As a developer I want the no results found navigation panel to look like before on desktop in split mode
Given  the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseProduct/eyJfaWQiOiIxMDYifQ=="
Then  the user opens the navigation panel
And  the user searches for "Non existing product" in the navigation panel
Then  element with test id "e-field-bind-$navigationPanel" looks as before