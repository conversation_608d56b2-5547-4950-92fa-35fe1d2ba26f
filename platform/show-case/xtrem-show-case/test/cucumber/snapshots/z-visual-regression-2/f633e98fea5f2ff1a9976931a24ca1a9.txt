As a user, I want to see table widget row actions and icons are available
Given  the user opens the application on a HD desktop
And  the dashboard page is displayed
Then  the "Create a dashboard to get started." subtitled empty dashboard is displayed
When  the user clicks the create button on the dashboard
Then  the dashboard creation dialog is displayed
When  the user selects the template 10 in the dashboard creation dialog
And  the user clicks the "next" button in the dashboard creation dialog
Then  the "Showcase dashboard" titled dashboard in the dashboard editor is displayed
When  the user clicks the "cancel" button in the dashboard editor footer
Then  the "Showcase dashboard" titled dashboard is displayed
When  the user selects the "Users with total count" titled table widget field in the dashboard
And  the user selects the card 1 of the table widget field
And  the user clicks row actions button of the selected card of the table widget field
Then  element with test id "db-widget-container-usersWithTotalCount" looks as before
When  the user clicks the "Switch to table view" toggle button in the header of the table widget field
And  the user selects the row with text "Support Readonly" and column header "Last name" of the table widget field
And  the user clicks row actions button of the selected row of the table widget field
Then  element with test id "db-widget-container-usersWithTotalCount" looks as before
When  the user selects the "Invoices" titled table widget field in the dashboard
And  the user selects the card 1 of the table widget field
Then  element with test id "db-widget-container-invoices" looks as before
When  the user clicks the "Switch to table view" toggle button in the header of the table widget field
Then  element with test id "db-widget-container-invoices" looks as before