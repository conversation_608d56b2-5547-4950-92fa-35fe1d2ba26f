As a user I want to check report template Edit conditions button visibility
Given  the user opens the application on a HD desktop using the following link: "@sage/xtrem-reporting/ReportTemplate/eyJfaWQiOiIxNCJ9"
Then  the "Report template conditionalBlockTemplate" titled page is displayed
When  selects the "Template" labelled navigation anchor on the main page
And  the user selects the "template" bound form designer field on the main page
And  the user selects the "1st" occurrence of "ShowCaseProvider" table in the form designer body
And  the user clicks the "Show/Hide formatting panel" button in the form designer toolbar
And  the user selects the cell with the "Date field" column header of the query table body in the table of the form designer
Then  the "Edit conditions" button in the formatting panel is hidden
And  element with class "document-editor-right-panel-body" looks as before
When  the user selects the "1st" occurrence of "ShowCaseProvider" table in the form designer body
And  the user selects the cell with the "Name" column header of the query table body in the table of the form designer
And  the user clicks in the selected cell of the table in the form designer
Then  the "Edit conditions" button in the formatting panel is displayed
And  element with class "document-editor-right-panel-body" looks as before