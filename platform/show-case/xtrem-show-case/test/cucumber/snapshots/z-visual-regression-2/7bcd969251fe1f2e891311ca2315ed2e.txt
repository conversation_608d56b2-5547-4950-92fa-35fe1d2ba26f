As a user I want to see that the contact card widget is displayed correctly
Given  the user opens the application on a HD desktop
And  the dashboard page is displayed
Then  the "Create a dashboard to get started." subtitled empty dashboard is displayed
When  the user clicks the create button on the dashboard
Then  the dashboard creation dialog is displayed
When  the user selects the template 10 in the dashboard creation dialog
And  the user clicks the "next" button in the dashboard creation dialog
Then  the "Showcase dashboard" titled dashboard in the dashboard editor is displayed
When  the user clicks the "cancel" button in the dashboard editor footer
Then  the "Showcase dashboard" titled dashboard is displayed
When  the user selects the "Contact cards" titled contact-card widget field in the dashboard
Then  the value of the filter dropdown of the contact-card widget field is "John Doe"
And  element with test id "db-widget-container-contactCards" looks as before