As a user I want see report template with different page orientation
Given  the user opens the application on a HD desktop using the following link: "@sage/xtrem-reporting/ReportTemplate/eyJfaWQiOiIxNiJ9"
Then  the "Report template filledPageTemplate" titled page is displayed
When  the user selects the "defaultPageOrientation" bound dropdown-list field on the main page
And  the user clicks in the dropdown-list field
Then  element with css selector "[data-testid*='defaultPageOrientation'] [data-testid='e-ui-select-dropdown']" looks as before
When  the user selects "Landscape" in the dropdown-list field
And  the user selects the "Settings" labelled tab in the detail panel
Then  element with css selector "[data-testid*='detailPanelSettingsBlock'] [data-testid*='pageOrientation']" looks as before
When  the user clicks the "extendPreview" bound business action button on the main page
Then  element with css selector "[data-testid*='pdfDialogPreview'] [data-page-number='1']" looks as before
When  the user refreshes the screen
And  the user selects the "defaultPageOrientation" bound dropdown-list field on the main page
And  the user clicks in the dropdown-list field
When  the user selects "Portrait" in the dropdown-list field
And  the user selects the "Settings" labelled tab in the detail panel
Then  element with css selector "[data-testid*='detailPanelSettingsBlock'] [data-testid*='pageOrientation']" looks as before
And  the user waits 3 seconds
When  the user clicks the "extendPreview" bound business action button on the main page
Then  element with css selector "[data-testid*='pdfDialogPreview'] [data-page-number='1']" looks as before