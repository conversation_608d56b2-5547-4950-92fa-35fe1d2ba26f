As a user I want the table sidebar on desktop to look like as before
Given  the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TableWithSidebar/eyJfaWQiOiIyIn0="
And  the "Field - Table - With sidebar" titled page is displayed
And  the user selects the "field" bound table field on the main page
And  the user selects the row 7 of the table field
And  the user clicks the "Edit on sidebar" dropdown action of the selected row of the table field
Then  element with css selector "[data-element='sidebar']" looks as before