As a user I want the selet dropdown to appear on the top when I edit a select field on the phantom row
Given  the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/HeaderSection/eyJfaWQiOiIyIn0="
Then  the "Provider Amazon" titled page is displayed
When  selects the "Products" labelled navigation anchor on the main page
When  the user selects the header section toggle button in the header
And  the user selects the "products" bound table field on the main page
And  the user selects the floating row of the table field
When  the user clicks the "category" bound nested field of the selected row in the table field
And  the user waits 3 seconds
Then  element with css selector "[data-testid='e-table-field-products'] [data-testid='e-ui-select-dropdown']" looks as before