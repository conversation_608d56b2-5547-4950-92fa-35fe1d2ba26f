As I user I want the bar chart and its preview to work correctly
Given  the user opens the application on a desktop
Then  the dashboard page is displayed
And  the "Create a dashboard to get started." subtitled empty dashboard is displayed
When  the user clicks the create button on the dashboard
Then  the dashboard creation dialog is displayed
When  the user selects the template 2 in the dashboard creation dialog
And  the user clicks the "next" button in the dashboard creation dialog
Then  the "Showcase dashboard" titled dashboard in the dashboard editor is displayed
When  the user clicks the "createAWidget" labelled button in the dashboard editor navigation panel
Then  the "New widget" titled widget editor dialog is displayed
And  the value of the step title of the widget editor dialog is "1. Select a widget to get started"
And  the "cancel" button in the widget editor dialog is enabled
And  the "next" button in the widget editor dialog is disabled
When  the user writes "My demo category" in the "basic-category" dropdown field in the widget editor dialog
And  the user presses Enter
And  the user writes "Show Case Product" in the "basic-node" dropdown field in the widget editor dialog
And  the user presses Enter
And  the user writes "Bar Chart Widget" in the "basic-title" text field in the widget editor dialog
And  the user selects the "LINE_CHART" widget card in the widget editor dialog
Then  the "LINE_CHART" widget card in the widget editor dialog is selected
When  the user selects the "BAR_CHART" widget card in the widget editor dialog
Then  the "LINE_CHART" widget card in the widget editor dialog is unselected
And  the "BAR_CHART" widget card in the widget editor dialog is selected
And  the "next" button in the widget editor dialog is enabled
When  the user clicks the "next" button in the widget editor dialog
Then  the value of the step title of the widget editor dialog is "2. Select the data to add to your widget"
And  the "previous" button in the widget editor dialog is enabled
And  the "cancel" button in the widget editor dialog is enabled
And  the "next" button in the widget editor dialog is disabled
When  the user searches for "Price" in the widget editor dialog
And  the user presses Enter
Then  the "Net price" tree-view element in the widget editor dialog is displayed
And  the "List price" tree-view element in the widget editor dialog is displayed
And  the "Amount" tree-view element in the widget editor dialog is hidden
When  the user clears the search field in the widget editor dialog
Then  the "Amount" tree-view element in the widget editor dialog is displayed
When  the user selects the "Net price" tree-view element in the widget editor dialog
Then  the "next" button in the widget editor dialog is disabled
When  the user selects the "List price" tree-view element in the widget editor dialog
Then  the "next" button in the widget editor dialog is enabled
When  the user selects the "Ending date" tree-view element in the widget editor dialog
Then  the "next" button in the widget editor dialog is enabled
When  the user unselects the "List price" tree-view element in the widget editor dialog
And  the user unselects the "Net price" tree-view element in the widget editor dialog
Then  the "next" button in the widget editor dialog is disabled
When  the user selects the "List price" tree-view element in the widget editor dialog
Then  the "next" button in the widget editor dialog is enabled
When  the user selects the "Net price" tree-view element in the widget editor dialog
And  the user selects the "Amount" tree-view element in the widget editor dialog
And  the user selects the "Fixed quantity" tree-view element in the widget editor dialog
And  the user selects the "Qty" tree-view element in the widget editor dialog
And  the user selects the "Discount" tree-view element in the widget editor dialog
And  the user selects the "Category" tree-view element in the widget editor dialog
And  the user clicks the "next" button in the widget editor dialog
Then  the value of the step title of the widget editor dialog is "3. Add your content"
And  the "horizontal-axis" dropdown field in the widget editor dialog is enabled
And  the "group-by" dropdown field in the widget editor dialog is hidden
When  the user writes "Ending Date" in the "horizontal-axis" dropdown field in the widget editor dialog
And  the user presses Enter
Then  the "group-by" dropdown field in the widget editor dialog is enabled
When  the user writes "Month" in the "group-by" dropdown field in the widget editor dialog
And  the user presses Enter
And  the user clicks the "Add value" table button in the widget editor dialog
Then  the "content-property" dropdown field of row "1" in the widget editor dialog is enabled
When  the user writes "Net Price" in the "content-property" dropdown field of row "1" in the widget editor dialog
And  the user presses Enter
Then  the "content-label" text field of row "1" in the widget editor dialog is enabled
And  the "content-formatting" text field of row "1" in the widget editor dialog is enabled
And  the value of the "content-label" text field of row "1" in the widget editor dialog is "Net price"
When  the user writes "2" in the "content-formatting" text field of row "1" in the widget editor dialog
And  the user presses Enter
Then  the "grouping-method" dropdown field of row "1" in the widget editor dialog is enabled
When  the user writes "Distinct count" in the "grouping-method" dropdown field of row "1" in the widget editor dialog
And  the user presses Enter
Then  the "next" button in the widget editor dialog is enabled
When  the user clicks the "add" action button of row "1" in the widget editor dialog
Then  the "content-property" dropdown field of row "2" in the widget editor dialog is enabled
When  the user writes "List Price" in the "content-property" dropdown field of row "2" in the widget editor dialog
Then  the value of the "content-label" text field of row "2" in the widget editor dialog is "List price"
When  the user writes "1" in the "content-formatting" text field of row "2" in the widget editor dialog
And  the user presses Enter
And  the user writes "Distinct count" in the "grouping-method" dropdown field of row "2" in the widget editor dialog
And  the user presses Enter
And  the user clicks the "add" action button of row "2" in the widget editor dialog
Then  the "content-property" dropdown field of row "3" in the widget editor dialog is enabled
When  the user writes "Amount" in the "content-property" dropdown field of row "3" in the widget editor dialog
Then  the value of the "content-label" text field of row "3" in the widget editor dialog is "Amount"
When  the user writes "1" in the "content-formatting" text field of row "3" in the widget editor dialog
And  the user presses Enter
And  the user writes "Distinct Count" in the "grouping-method" dropdown field of row "3" in the widget editor dialog
And  the user presses Enter
And  the user clicks the "add" action button of row "3" in the widget editor dialog
Then  the "content-property" dropdown field of row "4" in the widget editor dialog is enabled
When  the user writes "Net Price" in the "content-property" dropdown field of row "4" in the widget editor dialog
And  the user presses Enter
Then  the value of the "content-label" text field of row "4" in the widget editor dialog is "Net price"
And  the user writes "Maximum" in the "grouping-method" dropdown field of row "4" in the widget editor dialog
And  the user presses Enter
And  the user clicks the "add" action button of row "4" in the widget editor dialog
Then  the "content-property" dropdown field of row "5" in the widget editor dialog is enabled
When  the user writes "Discount" in the "content-property" dropdown field of row "5" in the widget editor dialog
And  the user presses Enter
Then  the value of the "content-label" text field of row "5" in the widget editor dialog is "Discount"
When  the user writes "Distinct count" in the "grouping-method" dropdown field of row "5" in the widget editor dialog
And  the user presses Enter
Then  the "add" action button of row "5" in the widget editor dialog is hidden
When  the user clicks the "next" button in the widget editor dialog
Then  the value of the step title of the widget editor dialog is "4. Add your filters"
When  the user clicks the "Add filter" table button in the widget editor dialog
Then  the "filter-property" dropdown field of row "1" in the widget editor dialog is enabled
And  the "filter-type" text field of row "1" in the widget editor dialog is disabled
And  the "filter-value" text field of row "1" in the widget editor dialog is disabled
When  the user writes "Ending Date" in the "filter-property" dropdown field of row "1" in the widget editor dialog
And  the user presses Enter
Then  the "filter-type" text field of row "1" in the widget editor dialog is enabled
When  the user writes "Less than or equal to" in the "filter-type" text field of row "1" in the widget editor dialog
And  the user presses Enter
Then  the "filter-value" text field of row "1" in the widget editor dialog is enabled
When  the user writes "04/27/2023" in the "filter-value" dropdown field of row "1" in the widget editor dialog
And  the user presses Enter
And  the user clicks the "add" action button of row "1" in the widget editor dialog
And  the user writes "Amount" in the "filter-property" dropdown field of row "2" in the widget editor dialog
And  the user presses Enter
And  the user writes "Less than" in the "filter-type" text field of row "2" in the widget editor dialog
And  the user presses Enter
And  the user writes "100" in the "filter-value" text field of row "2" in the widget editor dialog
And  the user presses Enter
And  the user clicks the "add" action button of row "2" in the widget editor dialog
And  the user writes "Category" in the "filter-property" dropdown field of row "3" in the widget editor dialog
And  the user presses Enter
And  the user writes "Does not equal" in the "filter-type" text field of row "3" in the widget editor dialog
And  the user presses Enter
And  the user writes "Awful" in the "filter-value" text field of row "3" in the widget editor dialog
And  the user presses Enter
And  the user writes "Ok" in the "filter-value" text field of row "3" in the widget editor dialog
And  the user presses Enter
And  the user presses Tab
And  the user clicks the "next" button in the widget editor dialog
Then  the value of the step title of the widget editor dialog is "5. Define sorting"
When  the user clicks the "Add a sort condition" table button in the widget editor dialog
Then  the "sorting-property" dropdown field of row "1" in the widget editor dialog is enabled
And  the "sorting-order" dropdown field of row "1" in the widget editor dialog is enabled
When  the user writes "Ending Date" in the "sorting-property" dropdown field of row "1" in the widget editor dialog
And  the user presses Enter
And  the user writes "Ascending" in the "sorting-order" dropdown field of row "1" in the widget editor dialog
And  the user presses Enter
And  the user clicks the "next" button in the widget editor dialog
Then  the value of the step title of the widget editor dialog is "6. Create your layout"
And  the "layout-vertical-axis-label" text field in the widget editor dialog is enabled
And  the "layout-horizontal-axis-label" text field in the widget editor dialog is enabled
When  the user writes "time" in the "layout-horizontal-axis-label" text field in the widget editor dialog
And  the user presses Enter
And  the user writes "quantities and price" in the "layout-vertical-axis-label" text field in the widget editor dialog
And  the user waits 2 seconds
And  element with test id "db-widget-container-barChartWidget" looks as before