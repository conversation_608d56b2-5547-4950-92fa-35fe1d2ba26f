As a user I want to see a filter menu and business icons in a table widget
Given  the user opens the application on a desktop
And  the dashboard page is displayed
Then  the "Create a dashboard to get started." subtitled empty dashboard is displayed
When  the user clicks the create button on the dashboard
Then  the dashboard creation dialog is displayed
And  the dashboard creation dialog description is "Select a template to get started or build your own dashboard. You can customize any dashboard by adding or removing widgets."
And  the "Blank template" template in the dashboard creation dialog is displayed
When  the user selects the template 1 in the dashboard creation dialog
Then  the template 1 in the dashboard creation dialog is selected
When  the user clicks the "next" button in the dashboard creation dialog
Then  the "New dashboard" titled dashboard in the dashboard editor is displayed
When  the user clicks the Add button of the "Users" titled widget card in the dashboard editor navigation panel
Then  the "Users" titled widget in the dashboard editor is displayed
When  the user selects the "Users" titled table widget field in the dashboard editor
Then  the "Filter menu" filter dropdown of the table widget field is displayed
When  the user clicks the "Save" button in the dashboard editor footer
Then  the "New dashboard" titled dashboard is displayed
When  the user selects the "Users" titled table widget field in the dashboard
And  the user waits 2 seconds
When  the user selects "Inactive accounts" in the "Filter menu" filter dropdown of the table widget field
Then  element with test id "db-widget-container-users" looks as before