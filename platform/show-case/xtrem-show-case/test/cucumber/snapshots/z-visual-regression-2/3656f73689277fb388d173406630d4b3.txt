As a user I want to check that contact card widget is displayed correctly
Given  the user opens the application on a desktop
And  the dashboard page is displayed
Then  the "Create a dashboard to get started." subtitled empty dashboard is displayed
When  the user clicks the create button on the dashboard
Then  the dashboard creation dialog is displayed
And  the dashboard creation dialog description is "Select a template to get started or build your own dashboard. You can customize any dashboard by adding or removing widgets."
And  the "Blank template" template in the dashboard creation dialog is displayed
When  the user selects the template 1 in the dashboard creation dialog
Then  the template 1 in the dashboard creation dialog is selected
When  the user clicks the "next" button in the dashboard creation dialog
Then  the "New dashboard" titled dashboard in the dashboard editor is displayed
When  the user clicks the Add button of the "Contact cards" titled widget card in the dashboard editor navigation panel
Then  the "Contact cards" titled widget in the dashboard editor is displayed
When  the user selects the "Contact cards" titled contact-card widget field in the dashboard editor
And  the user clicks the "Save" button in the dashboard editor footer
And  the user clicks the "Cancel" button in the dashboard editor footer
Then  the "New dashboard" titled dashboard is displayed
When  the user selects the "Contact cards" titled contact-card widget field in the dashboard
Then  the value of the contact filter menu of the contact-card widget is "John Doe"
And  element with test id "db-widget-container-contactCards" looks as before
When  the user clears the value of the contact filter menu of the contact-card widget
Then  the value of the contact filter menu of the contact-card widget is ""
When  the user clicks the "Site" contact filter toggle button of the contact-card widget
And  the user clicks the "Av Diagonal 200 Barcelona Spain" value in the contact filter menu of the contact-card widget
Then  the value of the contact filter menu of the contact-card widget is "Av Diagonal 200BarcelonaSpain"
And  element with test id "db-widget-container-contactCards" looks as before