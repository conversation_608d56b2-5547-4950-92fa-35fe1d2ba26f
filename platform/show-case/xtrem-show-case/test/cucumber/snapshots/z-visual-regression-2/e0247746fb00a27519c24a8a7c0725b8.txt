As a user I want to add a watermark over my Report Template so that I have a way to make sure my documents are safe
Given  the user opens the application on a desktop using the following link: "@sage/xtrem-reporting/ReportTemplate"
Then  the "Report templates" titled page is displayed
When  the user clicks the "Create" labelled business action button on the main page
Then  the "Report template" titled page is displayed
When  the user selects the "Name *" labelled text field on the main page
And  the user writes "Report Template with Watermark" in the text field
And  the user selects the "Report *" labelled reference field on the main page
And  the user clicks the lookup button of the reference field
And  the user selects the "report" bound table field on a modal
And  the user selects the row with text "activeTemplateReport" in the "Name" labelled column header of the table field
And  the user clicks the "Name" labelled nested field of the selected row in the table field
And  selects the "Template" labelled navigation anchor on the main page
And  the user selects the "template" bound form designer field on the main page
And  the user clicks in the body of the form designer
And  the user clicks the "Show/Hide fields panel" button in the form designer toolbar
Then  the "Fields" panel in the form designer is displayed
When  the user selects the node-step-tree in the form designer on the main page
And  the user selects the tree-view element of level "1" with text "Company" in the node-step-tree of the form designer
And  the user clicks the tree-view element in the node-step-tree of the form designer
And  the title of the form designer editor dialog is "Insert an object type"
And  the title step of the form designer editor dialog is "1. Insert mode"
And  the card with "Table" value in the selection card of the form designer editor dialog is unselected
When  the user selects the card with "Table" value in the selection card of the form designer editor dialog
Then  the card with "Table" value in the selection card of the form designer editor dialog is selected
When  the user clicks the "Next" button of the form designer editor dialog
Then  the title of the form designer editor dialog is "Insert an object type"
And  the title step of the form designer editor dialog is "2. Column selection"
And  the user selects the node-step-tree in the form designer on a modal
When  the user selects the tree-view element of level "1" with text "Active" in the node-step-tree of the form designer
And  the user ticks the tree-view element in the node-step-tree of the form designer
And  the user clicks the "Next" button of the form designer editor dialog
Then  the title step of the form designer editor dialog is "3. Define content"
When  the user clicks the "Next" button of the form designer editor dialog
Then  the title step of the form designer editor dialog is "4. Define filters"
When  the user clicks the "Confirm" button of the form designer editor dialog
And  the user clicks the "Show/Hide fields panel" button in the form designer toolbar
Then  the "Fields" panel in the form designer is hidden
When  the user clicks the "Preview" labelled business action button on the main page
And  the user waits 10 seconds
Then  element with css selector ".e-section.e-single-section.e-section-context-dialog" looks as before