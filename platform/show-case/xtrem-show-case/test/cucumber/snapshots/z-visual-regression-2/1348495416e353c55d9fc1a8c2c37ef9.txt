As a user I want report template header and footer to have page number and total number of pages
Given  the user opens the application on a HD desktop using the following link: "@sage/xtrem-reporting/ReportTemplate/eyJfaWQiOiIxNiJ9"
Then  the "Report template filledPageTemplate" titled page is displayed
When  selects the "Template" labelled navigation anchor on the main page
And  the user selects the "template" bound form designer field on the main page
And  the user clicks in the body of the form designer
And  the user clicks the "Show/hide document properties panel" button in the form designer toolbar
Then  the "Fields" panel in the form designer is displayed
And  element with test id "e-data-step-tree-container" looks as before
When  the user clicks in the header of the form designer
Then  element with test id "e-data-step-tree-container" looks as before
When  the user selects the node-step-tree in the form designer on the main page
And  the user selects the tree-view element of level "1" with text "Total number of pages" in the node-step-tree of the form designer
And  the user clicks the tree-view element in the node-step-tree of the form designer
When  the user clicks in the footer of the form designer
Then  element with test id "e-data-step-tree-container" looks as before
When  the user selects the node-step-tree in the form designer on the main page
And  the user selects the tree-view element of level "1" with text "Page number" in the node-step-tree of the form designer
And  the user clicks the tree-view element in the node-step-tree of the form designer
And  the user waits 3 seconds
And  the user clicks the "extendPreview" bound business action button on the main page
Then  element with css selector "[data-testid*='pdfDialogPreview'] [data-page-number='1']" looks as before
And  element with css selector "[data-testid*='pdfDialogPreview'] [data-page-number='2']" looks as before
And  element with css selector "[data-testid*='pdfDialogPreview'] [data-page-number='6']" looks as before