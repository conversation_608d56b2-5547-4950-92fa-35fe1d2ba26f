As a user I want to know if a field in my sidebar has errors
Given  the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TableWithSidebar/eyJfaWQiOiIyIn0="
And  the "Field - Table - With sidebar" titled page is displayed
And  the user selects the "field" bound table field on the main page
And  the user clicks the "Edit on sidebar" dropdown action of row 1 of the table field
Then  the user selects the "Quantity" labelled numeric field on the sidebar
And  the user writes "-2" in the numeric field
And  the user presses Enter
Then  the numeric field is invalid
And  the "Apply" button of the dialog is disabled on the sidebar
Then  element with css selector "[data-element='sidebar']" looks as before