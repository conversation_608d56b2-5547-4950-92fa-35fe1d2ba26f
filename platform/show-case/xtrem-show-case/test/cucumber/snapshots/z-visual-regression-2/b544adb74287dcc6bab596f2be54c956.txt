As a user I want the see responsiveness in dashboard editor columns when using mobile device
Given  the user opens the application on a mobile
Then  the dashboard page is displayed
When  the user clicks the create button on the dashboard
Then  the dashboard creation dialog is displayed
When  the user selects the template with title "Blank template" in the dashboard creation dialog
And  the user clicks the "next" button in the dashboard creation dialog
Then  the "New dashboard" titled dashboard in the dashboard editor is displayed
When  the user clicks the Add button of the "Invoices" titled widget card in the dashboard editor navigation panel
Then  the "Invoices" titled widget in the dashboard editor is displayed
When  the user clicks the "toggleWidgetList" labelled button in the dashboard editor navigation panel
Then  element with test id "e-dashboard-editor-body" looks as before