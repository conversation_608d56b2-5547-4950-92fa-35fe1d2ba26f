As a user I want to configure report template margins for locale "English US"
Given  the user opens the application on a HD desktop using the following link: "@sage/xtrem-reporting/ReportTemplate/eyJfaWQiOiIxNiJ9"
Then  the "Report template filledPageTemplate" titled page is displayed
When  the user switches language to "English US"
And  the user refreshes the screen
And  the user selects the "defaultTopMargin" bound numeric field on the main page
And  the user writes "2.1" in the numeric field
And  the user presses Tab
And  the user selects the "defaultBottomMargin" bound numeric field on the main page
And  the user writes "2.1" in the numeric field
And  the user presses Tab
And  the user selects the "defaultLeftMargin" bound numeric field on the main page
And  the user writes "2.1" in the numeric field
And  the user presses Tab
And  the user selects the "defaultRightMargin" bound numeric field on the main page
And  the user writes "2.1" in the numeric field
And  the user presses Tab
And  the user clicks the "save" bound business action button on the main page
And  the user waits 3 seconds
Then  element with test id "e-field-bind-pageFormatBlock" looks as before
And  the user clicks the "extendPreview" bound business action button on the main page
Then  element with css selector "[data-testid*='pdfDialogPreview'] [data-page-number='1']" looks as before