As a user I want to check conditional block value types for document without parameters and conditions limit
Given  the user opens the application on a HD desktop using the following link: "@sage/xtrem-reporting/ReportTemplate/eyJfaWQiOiIxNSJ9"
Then  the "Report template conditionalBlockNoVariableReportTemplate" titled page is displayed
When  selects the "Template" labelled navigation anchor on the main page
And  the user selects the "template" bound form designer field on the main page
And  the user selects the "1st" occurrence of "ShowCaseProvider" table in the form designer body
And  the user clicks the "Conditional block" button in the form designer toolbar
Then  the title of the form designer editor dialog is "Conditional block"
When  the user clicks the "Add condition" table button in the form designer widget editor
And  the user clicks the "add" action button of row "1" in the form designer widget editor
And  the user clicks the "add" action button of row "2" in the form designer widget editor
And  the user clicks the "add" action button of row "3" in the form designer widget editor
And  the user clicks the "add" action button of row "4" in the form designer widget editor
And  the user clicks the "add" action button of row "5" in the form designer widget editor
And  the user clicks the "add" action button of row "6" in the form designer widget editor
And  the user clicks the "add" action button of row "7" in the form designer widget editor
And  the user clicks the "add" action button of row "8" in the form designer widget editor
And  the user clicks the "add" action button of row "9" in the form designer widget editor
Then  the "add" action button of row "10" the form designer widget editor is hidden
When  the user selects the "Date field" option in the "value1" dropdown field of row "10" in the form designer editor
And  the user opens the "valueType2" dropdown field of row "10" in the form designer editor
Then  element with css selector "[role='dialog']" looks as before