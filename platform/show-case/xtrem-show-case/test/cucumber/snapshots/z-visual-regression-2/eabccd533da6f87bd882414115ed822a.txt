As a user I want dependent fields in the sidebar to update when source field is changed
Given  the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TableWithSidebar/eyJfaWQiOiIyIn0="
Then  the "Field - Table - With sidebar" titled page is displayed
When  the user selects the "field" bound table field on the main page
And  the user clicks the "Edit on sidebar" dropdown action of row 1 of the table field
When  the user selects the "Provider" labelled reference field on the sidebar
Then  the value of the reference field is "Amazon"
And  element with test id "e-field-bind-amount" looks as before