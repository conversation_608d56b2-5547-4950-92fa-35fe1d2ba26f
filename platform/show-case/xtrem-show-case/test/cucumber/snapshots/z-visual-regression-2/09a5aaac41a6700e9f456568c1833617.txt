As a user I want to see data represented in a donut chart widget
Given  the user opens the application on a HD desktop
And  the dashboard page is displayed
Then  the "Create a dashboard to get started." subtitled empty dashboard is displayed
When  the user clicks the create button on the dashboard
Then  the dashboard creation dialog is displayed
When  the user selects the template 10 in the dashboard creation dialog
And  the user clicks the "next" button in the dashboard creation dialog
Then  the "Showcase dashboard" titled dashboard in the dashboard editor is displayed
When  the user clicks the "cancel" button in the dashboard editor footer
Then  the "Showcase dashboard" titled dashboard is displayed
When  the user selects the "Pie Chart" titled pie-chart widget field in the dashboard
Then  element with test id "db-widget-container-pieChart" looks as before
When  the user clicks the "Switch to card view" toggle button in the header of the pie-chart widget field
And  the user waits 2 seconds
Then  element with test id "db-widget-container-pieChart" looks as before