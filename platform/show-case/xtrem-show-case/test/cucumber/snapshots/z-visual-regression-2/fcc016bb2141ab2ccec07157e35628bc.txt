As a user, I want to see long content in table widget
Given  the user opens the application on a HD desktop
And  the dashboard page is displayed
Then  the "Create a dashboard to get started." subtitled empty dashboard is displayed
When  the user clicks the create button on the dashboard
Then  the dashboard creation dialog is displayed
When  the user selects the template 10 in the dashboard creation dialog
And  the user clicks the "next" button in the dashboard creation dialog
Then  the "Showcase dashboard" titled dashboard in the dashboard editor is displayed
When  the user clicks the "cancel" button in the dashboard editor footer
Then  the "Showcase dashboard" titled dashboard is displayed
When  the user selects the "Invoices" titled table widget field in the dashboard
And  the user scrolls down to the card with text "SCINV000070" and card section "Title" of the table widget field
Then  element with test id "db-widget-container-invoices" looks as before
When  the user clicks the "Switch to table view" toggle button in the header of the table widget field
And  the user scrolls down to the row with text "SCINV000068" and column header "Invoice Number" of the table widget field
And  the user selects the row with text "SCINV000068" and column header "Invoice Number" of the table widget field
And  the user expands the row of the table widget field
And  the user selects the row with text "SCINV000070" and column header "Invoice Number" of the table widget field
And  the user expands the row of the table widget field
Then  element with test id "db-widget-container-invoices" looks as before