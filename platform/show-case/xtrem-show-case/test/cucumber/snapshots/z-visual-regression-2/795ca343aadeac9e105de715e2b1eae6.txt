As a user I want to use text stream variables in report template
Given  the user opens the application on a HD desktop using the following link: "@sage/xtrem-reporting/ReportTemplate"
When  the user clicks the "Create" labelled business action button on the navigation panel
Then  the "Report template" titled page is displayed
When  the user selects the "Name" labelled text field on the main page
And  the user writes "textStreamTemplate" in the text field
And  the user presses Enter
And  the user selects the "Report" labelled reference field on the main page
And  the user writes "variableTestReport" in the reference field
And  the user selects "variableTestReport" in the reference field
And  the user presses Enter
And  selects the "Template" labelled navigation anchor on the main page
And  the user selects the "template" bound form designer field on the main page
And  the user clicks in the body of the form designer
And  the user clicks the "Show/hide document properties panel" button in the form designer toolbar
Then  the "Fields" panel in the form designer is displayed
And  element with test id "e-data-step-tree-container" looks as before