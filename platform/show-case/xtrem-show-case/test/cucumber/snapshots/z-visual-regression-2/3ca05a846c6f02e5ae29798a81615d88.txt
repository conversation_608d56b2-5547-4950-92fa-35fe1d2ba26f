As a user I want to use text stream variables in report template
Given  the user opens the application on a HD desktop using the following link: "@sage/xtrem-reporting/ReportTemplate"
When  the user clicks the "Create" labelled business action button on the navigation panel
Then  the "Report template" titled page is displayed
When  the user selects the "Name" labelled text field on the main page
And  the user writes "textStreamTemplate" in the text field
And  the user presses Enter
And  the user selects the "Report" labelled reference field on the main page
And  the user writes "variableTestReport" in the reference field
And  the user selects "variableTestReport" in the reference field
And  the user presses Enter
And  selects the "Template" labelled navigation anchor on the main page
And  the user selects the "template" bound form designer field on the main page
And  the user clicks in the body of the form designer
And  the user clicks the "Show/hide document properties panel" button in the form designer toolbar
Then  the "Fields" panel in the form designer is displayed
And  element with test id "e-data-step-tree-container" looks as before
When  the user clicks the "Show/hide fields panel" button in the form designer toolbar
Then  the "Fields" panel in the form designer is displayed
When  the user searches "Show case provider" in the node-step-tree of the form designer on the main page
And  the user selects the node-step-tree in the form designer on the main page
And  the user selects the tree-view element of level "1" with text "Show case provider" in the node-step-tree of the form designer
And  the user clicks the tree-view element in the node-step-tree of the form designer
Then  the title of the form designer editor dialog is "Insert an object type"
And  the title step of the form designer editor dialog is "1. Insert mode"
When  the user selects the card with "Table" value in the selection card of the form designer editor dialog
And  the user clicks the "Next" button of the form designer editor dialog
Then  the title step of the form designer editor dialog is "2. Column selection"
When  the user selects the node-step-tree in the form designer on a modal
And  the user selects the tree-view element of level "1" with text "Document" in the node-step-tree of the form designer
And  the user ticks the tree-view element in the node-step-tree of the form designer
And  the user clicks the "Next" button of the form designer editor dialog
Then  the title step of the form designer editor dialog is "3. Define content"
And  element with css selector "[role='dialog']" looks as before
And  the user clicks the "Previous" button of the form designer editor dialog
And  the user selects the tree-view element of level "1" with text "Integer field" in the node-step-tree of the form designer
And  the user ticks the tree-view element in the node-step-tree of the form designer
And  the user selects the tree-view element of level "1" with text "Text field" in the node-step-tree of the form designer
And  the user ticks the tree-view element in the node-step-tree of the form designer
And  the user clicks the "Next" button of the form designer editor dialog
Then  the title step of the form designer editor dialog is "3. Define content"
And  element with css selector "[role='dialog']" looks as before
When  the user drags row "1" down "2" rows in the form designer editor
When  the user writes "Group 2" in the "Group" dropdown-list field of row "3" in the form designer widget editor
And  the user presses Tab
Then  a error toast with text "Non-sortable properties cannot be the first items in the group. Select other properties." is displayed
When  the user dismisses all the toasts
And  the user presses Tab
Then  element with css selector "[role='dialog']" looks as before
When  the user clicks the "Next" button of the form designer editor dialog
Then  the title step of the form designer editor dialog is "4. Define filters"
When  the user clicks the "Add filter" table button in the form designer widget editor
And  the user opens the "property" dropdown field of row "1" in the form designer editor
Then  element with css selector "[role='dialog']" looks as before