As a user I want the header of an invalid nested referece column to look like before
Given  the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/InlineEdit/eyJfaWQiOiIxIn0="
When  the user selects the "field" bound table field on the main page
And  the user writes "" in the "Provider" labelled nested reference field of row 1 in the table field
Then  the "Table title" labelled table field on the main page is invalid
And  element with css selector ".e-nested-header-label-provider" looks as before