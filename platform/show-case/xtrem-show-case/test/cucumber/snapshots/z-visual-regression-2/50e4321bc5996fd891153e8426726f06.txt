As a user I want "EUR" to be displayed in "German"
Given  the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Numeric"
When  the user switches language to "German"
Then  the "Numeric" titled page is displayed
Given  the user selects the "Scale" labelled numeric field on the main page
And  the user clicks in the numeric field
And  the user writes "1" in the numeric field
And  the user blurs the numeric field
And  the user clears the numeric field
And  the user waits 1 second
Then  the user selects the "unit" bound dropdown-list field on the main page
When  the user clicks in the dropdown-list field
And  the user selects "EUR" in the dropdown-list field
And  the user waits 1 second
Then  element with test id "e-field-bind-field" looks as before