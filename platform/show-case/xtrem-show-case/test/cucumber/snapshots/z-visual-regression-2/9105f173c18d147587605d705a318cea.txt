As a user I want to check 360 view toggle ON, OFF, disabled state
Given  the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/StandardShowCaseProvider/eyJfaWQiOiIxIn0="
Then  the 360 view switch in the header is OFF
And  element with test id "e-page-360-switch" looks as before
When  the user clicks the 360 view switch in the header
Then  the 360 view switch in the header is ON
And  element with test id "e-page-360-switch" looks as before
When  the user clicks the 360 view switch in the header
Then  the 360 view switch in the header is OFF
When  the user selects the "Address" labelled reference field on the main page
And  the user clears the reference field
Then  the 360 view switch in the header is disabled
And  element with test id "e-page-360-switch" looks as before