mobile - As a user I want to see five lines in my navigation panel card
Given  the user opens the application on a mobile using the following link: "@sage/xtrem-show-case/CardField/eyJfaWQiOiIxMDYifQ=="
Then  the "Field - Card" titled page is displayed
When  the user opens the navigation panel
And  the user selects the "$navigationPanel" bound table field on the navigation panel
And  the value of the "product" bound nested text field of the card 1 in the table field is "Anisette - Mcguiness"
And  the value of the "description" bound nested text field of the card 1 in the table field is "complexity"
And  the value of the "barcode" bound nested text field of the card 1 in the table field is "3546705000074574"
And  the value of the "releaseDate" bound nested date field of the card 1 in the table field is "01/02/2020"
And  the value of the "_id" bound nested text field of the card 1 in the table field is "106"
And  the value of the "listPrice" bound nested numeric field of the card 1 in the table field is "19"
And  the value of the "category" bound nested label field of the card 1 in the table field is "Good"
And  the value of the "tax" bound nested text field of the card 1 in the table field is "7.53"
And  element with test id "e-card" looks as before