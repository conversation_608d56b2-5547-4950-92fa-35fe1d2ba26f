As a user I want to make sure that the empty state of a table with a disabled phantom row looks like before
Given  the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseInvoice/eyJfaWQiOiIxOSJ9"
And  the user selects the "isPhantomRowDisabled" bound checkbox field on the main page
And  the user clicks in the checkbox field
Then  a toast containing text "Disable/Enable phantom row was triggered" is displayed
Then  element with test id "e-table-field-lines" looks as before