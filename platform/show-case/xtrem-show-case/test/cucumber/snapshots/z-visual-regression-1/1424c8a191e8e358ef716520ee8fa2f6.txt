As a user I want date time range field and picker to look the same as before
Given  the user opens the application on a HD desktop using the following link: "@sage/xtrem-show-case/DatetimeRange"
Then  the "Date-time Range" titled page is displayed
When  the user selects the "field" bound date-time-range field on the main page
And  the user selects the "April" month of the start date-time-range field
And  the user selects the "2025" year of the start date-time-range field
And  the user selects the "14" day in the start date-time-range field
And  the user writes "12:15" in time field of the start date-time-range field
Then  element with class "e-datetime-range-container" looks as before
And  element with css selector "#--DatetimeRange-bind-field-start" looks as before
When  the user selects the "April" month of the end date-time-range field
And  the user selects the "2025" year of the end date-time-range field
And  the user selects the "18" day in the end date-time-range field
And  the user writes "07:30" in time field of the end date-time-range field
Then  element with class "e-datetime-range-container" looks as before