As a user I want to see nested grid with infinite scroll
Given  the user opens the application on a HD desktop using the following link: "@sage/xtrem-show-case/NestedGridInfiniteScroll/eyJfaWQiOiIxNTkifQ=="
Then  the "Field - NestedGrid Infinite Scroll" titled page is displayed
When  the user selects the "field" bound nested grid field on the main page
And  the user clicks the "ID" labelled column of the nested grid field
And  the user selects row with text "31" in column with header "Id" in the nested grid field
And  the user expands the selected row of the nested grid field
Then  element with test id "e-page-body" looks as before
When  the user scrolls down to the row with text "420" and column header "Id" in the nested grid field
Then  element with test id "e-page-body" looks as before