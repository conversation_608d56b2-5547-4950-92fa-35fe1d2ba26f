As a user I want the mobile table card field with a progress bar with hidden labels to look like as before on tablet
Given  the user opens the application on a tablet using the following link: "@sage/xtrem-show-case/TableMobileCardViewWithProgressBar"
When  the user clicks in the "button" bound button field on the main page
Then  element with css selector "[data-testid~='e-field-bind-hiddenLabelsField'] .e-table-field-mobile-rows [data-testid~='e-card']" looks as before