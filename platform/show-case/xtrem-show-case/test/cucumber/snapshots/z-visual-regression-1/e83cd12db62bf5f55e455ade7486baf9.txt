As a user I want to see multiple cards in a single row on the mobile card view
Given  the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TableMobileCardViewWithMultiCardLines"
Then  the "Mobile card view with multi card lines" titled page is displayed
And  the user selects the "field" bound table field on the main page
Then  the table field is empty
When  the user clicks the "Load" labelled business action button of the table field
Then  element with test id "e-table-field-mobile-rows" looks as before