As a user I want see tunnel dialogs enforcing a fixed height
Given  the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/StandardShowCaseProduct/413"
Then  the "Product 413" titled page is displayed
When  the user selects the "Provider" labelled reference field on the main page
Then  the reference field tunnel link is displayed
When  the user clicks the reference field tunnel link
Then  an info dialog appears on a full width modal
When  the user waits 2 seconds
Then  the dialog title is "Provider 2" on a full width modal
And  selects the "Products" labelled navigation anchor on a full width modal
And  the user selects the "products" bound table field on a full width modal
Then  element with test id "e-table-field-products" looks as before