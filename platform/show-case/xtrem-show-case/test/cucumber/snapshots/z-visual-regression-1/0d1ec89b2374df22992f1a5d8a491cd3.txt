As a user I want the dashboard tabs to be positioned correctly
Given  the user opens the application on a desktop
And  the dashboard page is displayed
Then  the "Create a dashboard to get started." subtitled empty dashboard is displayed
When  the user clicks the create button on the dashboard
Then  the dashboard creation dialog is displayed
And  the dashboard creation dialog description is "Select a template to get started or build your own dashboard. You can customize any dashboard by adding or removing widgets."
And  the "Blank template" template in the dashboard creation dialog is displayed
And  the "next" button in the dashboard creation dialog is disabled
And  the "cancel" button in the dashboard creation dialog is enabled
When  the user selects the template 1 in the dashboard creation dialog
Then  the template 1 in the dashboard creation dialog is selected
And  the "next" button in the dashboard creation dialog is enabled
When  the user clicks the "next" button in the dashboard creation dialog
Then  the "New dashboard" titled dashboard in the dashboard editor is displayed
When  the user clicks the "cancel" button in the dashboard editor footer
Then  the "New dashboard" titled dashboard is displayed
Then  element with class "e-dashboard" looks as before