As a developer I want the table header's option menu types to look like as before on desktop
Given  the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TableOptionsMenu/eyJfaWQiOiIyIn0="
Then  the "Field - Table (Options Menu)" titled page is displayed
When  selects the "Dropdown" labelled navigation anchor on the main page
Then  element with css selector ".e-table-field[data-testid~='e-field-bind-dropdownField'] .e-field-header" looks as before
When  selects the "Tabs" labelled navigation anchor on the main page
Then  element with css selector ".e-table-field[data-testid~='e-field-bind-tabsField'] .e-field-header" looks as before
When  selects the "Toggle" labelled navigation anchor on the main page
Then  element with css selector ".e-table-field[data-testid~='e-field-bind-toggleField'] .e-field-header" looks as before
When  selects the "Dropdown (w/o Search)" labelled navigation anchor on the main page
Then  element with css selector ".e-table-field[data-testid~='e-field-bind-searchlessDropdownField'] .e-field-header" looks as before