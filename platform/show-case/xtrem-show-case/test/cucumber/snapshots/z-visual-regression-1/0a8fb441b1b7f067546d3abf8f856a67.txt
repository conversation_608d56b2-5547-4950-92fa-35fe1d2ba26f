As a user I want the detail panel tab container to look like as before when overloaded
Given  the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/DetailPanel/"
When  the user selects the tab 1 in the detail panel
Then  the "firstSectionBlock" labelled block container on the detail panel is displayed
Then  element with css selector ".e-detail-panel-tab-container" looks as before