As a developer I want my page dialog fullscreen to look as before
Given  the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Dialogs"
When  the user selects the "Path" labelled text field on the main page
And  the user writes "@sage/xtrem-show-case/CardField" in the text field
And  the user selects the "pageDialogIsFullscreen" bound checkbox field on the main page
And  the user clicks in the checkbox field
And  the user clicks in the "pageDialogButton" bound button field on the main page
Then  element with css selector "[data-element='dialog-full-screen']" looks as before