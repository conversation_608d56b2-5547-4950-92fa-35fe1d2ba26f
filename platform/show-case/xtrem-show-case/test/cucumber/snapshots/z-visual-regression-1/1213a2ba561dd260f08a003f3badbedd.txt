As a user I want row actions to look the same as before - view mode
Given  the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/RowActions"
Then  the "Row actions" titled page is displayed
And  the user selects the "table91" bound table field on the main page
And  the user selects the row 1 of the table field
Then  the value of the "Readonly" labelled nested text field of the selected row in the table field is "a"
Then  element with css selector "[data-testid~='e-field-bind-table91'] [row-index='0'] [col-id='__actions']" looks as before
And  the user selects the "table112" bound table field on the main page
Then  the value of the "Readonly" labelled nested text field of the selected row in the table field is "a"
And  element with css selector "[data-testid~='e-field-bind-table112'] [row-index='0'] [col-id='__actions']" looks as before