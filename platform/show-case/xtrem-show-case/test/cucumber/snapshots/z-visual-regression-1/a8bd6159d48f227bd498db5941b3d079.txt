As a developer I want to make sure that the rich text editor looks like as it used to on desktop when is readOnly
Given  the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/RichText"
Then  the "Field - Rich text" titled page is displayed
When  the user selects the "isReadOnly" bound checkbox field on the main page
And  the user clicks in the checkbox field
And  the user waits 1 seconds
Then  element with test id "e-field-bind-field" looks as before