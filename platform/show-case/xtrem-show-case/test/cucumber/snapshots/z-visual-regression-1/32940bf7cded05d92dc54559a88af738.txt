As a user I want the detail panel tab container to look like as before
Given  the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/DetailPanel/"
When  the user selects the "Fifth detail panel section hidden" labelled checkbox field on the main page
And  the user clicks in the checkbox field
When  the user selects the "Fourth detail panel section hidden" labelled checkbox field on the main page
And  the user clicks in the checkbox field
When  the user selects the "Third detail panel section hidden" labelled checkbox field on the main page
And  the user clicks in the checkbox field
When  the user selects the tab 1 in the detail panel
Then  the "firstSectionBlock" labelled block container on the detail panel is displayed
Then  element with css selector ".e-detail-panel-tab-container" looks as before