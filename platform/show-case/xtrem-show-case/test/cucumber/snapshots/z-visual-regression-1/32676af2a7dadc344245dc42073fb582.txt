As a user I want row actions to look the same as before - edit mode
Given  the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/RowActions"
Then  the "Row actions" titled page is displayed
And  the user selects the "table91" bound table field on the main page
And  the user selects the row 1 of the table field
Then  the value of the "Readonly" labelled nested text field of the selected row in the table field is "a"
When  the user clicks the "Optional" labelled nested field of the selected row in the table field
And  the user presses Tab
Then  the element with the following selector is focused: ".ag-popup .e-table-field-actions-container"
Then  element with css selector ".ag-popup .e-table-field-actions-container" looks as before