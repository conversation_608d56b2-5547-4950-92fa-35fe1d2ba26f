As a user I want the progress field with hidden labels as before
Given  the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Progress"
And  the user selects the "Are progress labels hidden?" labelled checkbox field on the main page
And  the user ticks the checkbox field
And  the user selects the "Title" labelled text field on the main page
And  the user writes "Test title" in the text field
And  the user selects the "Helper text" labelled text field on the main page
And  the user writes "Test helper text" in the text field
When  the user blurs the text field
Then  element with test id "e-field-bind-field" looks as before