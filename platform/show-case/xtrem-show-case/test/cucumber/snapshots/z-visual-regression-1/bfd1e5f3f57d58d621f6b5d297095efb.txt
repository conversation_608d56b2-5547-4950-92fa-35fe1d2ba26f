As user I want to make sure that the table paging panel looks like before
Given  the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TableWithOptionsMenu/eyJfaWQiOiIyIn0="
And  the user selects the "field" bound table field on the main page
Then  the First page button of the table field is disabled
Then  the Last page button of the table field is disabled
Then  the Previous page button of the table field is disabled
Then  the Next page button of the table field is enabled
Then  the page number of the table field is "Page 1 of more"
Then  the summary row paging information of the table field is "1 to 20 of more"
And  element with css selector ".ag-paging-page-summary-panel" looks as before