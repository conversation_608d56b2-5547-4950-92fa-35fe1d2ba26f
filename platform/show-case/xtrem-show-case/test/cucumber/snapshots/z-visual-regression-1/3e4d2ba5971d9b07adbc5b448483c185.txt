As a user I want see the navigation panel in a full width dialog if the corresponding config property is set
Given  the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Dialogs"
And  the "Dialogs" titled page is displayed
And  selects the "Page dialog" labelled navigation anchor on the main page
And  the user waits 2 seconds
And  the user selects the "Path" labelled text field on the main page
When  the user writes "@sage/xtrem-show-case/ShowCaseProduct" in the text field
And  the user selects the "Render main list" labelled checkbox field on the main page
When  the user ticks the checkbox field
And  the user selects the "pageDialogIsFullscreen" bound checkbox field on the main page
When  the user ticks the checkbox field
When  the user clicks in the "pageDialogButton" bound button field on the main page
And  the dialog title is "Products"
When  the user selects the "$navigationPanel" bound table field on a full width modal
And  the user selects the row 1 of the table field
Then  the value of the "product" bound nested text field of the selected row in the table field is "Anisette - Mcguiness"
Then  element with css selector ".e-dialog-body .e-table-field-desktop-wrapper" looks as before