As a user I want the section validation messages to be displayed in dialogs on desktop
Given  the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Dialogs"
Then  the "Dialogs" titled page is displayed
When  the user selects the "pageDialogIsRightAligned" bound checkbox field on the main page
And  the user clicks in the checkbox field
And  the user selects the "pageDialogPath" bound text field on the main page
And  the user writes "@sage/xtrem-show-case/TabSectionValidation" in the text field
And  the user selects the "pageDialogSize" bound select field on the main page
And  the user clicks in the select field
And  the user selects "medium" in the select field
And  the user clicks in the "pageDialogButton" bound button field on the main page
And  the user waits 2 seconds
And  the user clicks the "validate" bound business action button on the sidebar
Then  element with css selector "div[data-element='sidebar'][role='dialog']" looks as before