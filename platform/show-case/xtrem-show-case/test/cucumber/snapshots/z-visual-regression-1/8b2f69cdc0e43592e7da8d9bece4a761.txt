As a user I want a page dialog's header section to be rendered as a tab in the page when the page is rendered on desktop
Given  the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Dialogs"
When  selects the "Page dialog" labelled navigation anchor on the main page
And  the user selects the "Path" labelled text field on the main page
And  the user writes "@sage/xtrem-show-case/StandardShowCaseInvoice" in the text field
And  the user selects the "pageDialogQueryParameterName" bound text field on the main page
And  the user writes "_id" in the text field
And  the user selects the "pageDialogQueryParameterValue" bound text field on the main page
And  the user writes "11" in the text field
And  the user clicks in the "pageDialogAddParameter" bound button field on the main page
And  the user clicks in the "pageDialogButton" bound button field on the main page
Then  the dialog title is "Invoice 11"
And  element with css selector "[role='dialog']" looks as before