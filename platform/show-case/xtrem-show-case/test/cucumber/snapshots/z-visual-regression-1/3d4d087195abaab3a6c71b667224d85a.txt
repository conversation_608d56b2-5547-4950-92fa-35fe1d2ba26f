As a developer I want to make sure that destructive dropdown actions look like as before on mobile
Given  the user opens the application on a mobile using the following link: "@sage/xtrem-show-case/ShowCaseInvoice/eyJfaWQiOiI1In0="
Then  the "Invoice 5 05/19/2020" titled page is displayed
And  the user waits 1 seconds
Then  element with css selector ".e-table-field-mobile-rows .e-action-popover-mobile-button" looks as before