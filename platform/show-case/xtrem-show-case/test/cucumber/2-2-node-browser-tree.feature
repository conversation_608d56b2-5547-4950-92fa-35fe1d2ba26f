Feature: 2-2 Node browser tree
    # Tests the node browser tree component functionality, including search, expand/collapse, selection of hierarchical data elements, and control the node browser tree state.

    Scenario: As an ATP XTreeM user I can search in the node browser tree
        XT-53380
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/NodeBrowserTreePage"
        Then the "Node Browser Tree" titled page is displayed

        And the user selects the "Node Browser Tree" labelled node-browser-tree field on the main page
        When the user searches for "Amount" in the node-browser-tree field
        And the user selects the tree-view element of level "1" with text "Amount" in the node-browser-tree field
        And the user clears the search field of the node-browser-tree field
        And the user selects the tree-view element of level "1" with text "Barcode" in the node-browser-tree field


    Sc<PERSON>rio: As an ATP XTreeM user I can expand or collapse the node-browser-tree element
        XT-53380
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/NodeBrowserTreePage"
        Then the "Node Browser Tree" titled page is displayed

        And the user selects the "Node Browser Tree" labelled node-browser-tree field on the main page

        And the user selects the tree-view element of level "1" with text "Certificate" in the node-browser-tree field
        And the user expands the tree-view element in the node-browser-tree field
        Then the tree-view element of the node-browser-tree field is expanded
        And the user collapses the tree-view element in the node-browser-tree field
        Then the tree-view element of the node-browser-tree field is collapsed


    Scenario: As an ATP XTreeM user I can tick or untick the node-browser-tree element
        XT-53380
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/NodeBrowserTreePage"
        Then the "Node Browser Tree" titled page is displayed

        And the user selects the "Node Browser Tree" labelled node-browser-tree field on the main page

        And the user selects the tree-view element of level "1" with text "_id" in the node-browser-tree field
        And the user ticks the tree-view element in the node-browser-tree field
        Then the tree-view element of the node-browser-tree field is ticked

        And the user selects the tree-view element of level "1" with text "Certificate" in the node-browser-tree field
        And the user expands the tree-view element in the node-browser-tree field
        And the user selects the tree-view element of level "2" with text "_id" in the node-browser-tree field
        And the user ticks the tree-view element in the node-browser-tree field
        Then the tree-view element of the node-browser-tree field is ticked
        And the user selects the tree-view element of level "2" with text "Download URL" in the node-browser-tree field
        And the user ticks the tree-view element in the node-browser-tree field
        Then the tree-view element of the node-browser-tree field is ticked

        And the user unticks the tree-view element in the node-browser-tree field
        Then the tree-view element of the node-browser-tree field is unticked

        And the user selects the tree-view element of level "2" with text "File name" in the node-browser-tree field
        And the user ticks the tree-view element in the node-browser-tree field


    Scenario: As an ATP XTreeM user I can verify the visibility state of the title node-browser-tree
        XT-55594
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/NodeBrowserTreePage"
        Then the "Node Browser Tree" titled page is displayed

        And the user selects the "Title" labelled text field on the main page
        And the user writes "My Title" in the text field

        And the user selects the "field" bound node-browser-tree field on the main page
        Then the title of the node-browser-tree field is displayed
        Then the title of the node-browser-tree field is "My Title"

        And the user selects the "Is title hidden" labelled checkbox field on the main page
        And the user ticks the checkbox field

        And the user selects the "field" bound node-browser-tree field on the main page
        Then the title of the node-browser-tree field is hidden



    Scenario: As an ATP XTreeM user I can verify the visibility state of the helper node-browser-tree
        XT-55594
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/NodeBrowserTreePage"
        Then the "Node Browser Tree" titled page is displayed

        And the user selects the "Helper text" labelled text field on the main page
        And the user writes "My Helper Text" in the text field

        And the user selects the "field" bound node-browser-tree field on the main page
        Then the helper text of the node-browser-tree field is displayed
        Then the helper text of the node-browser-tree field is "My Helper Text"

        And the user selects the "Is helper text hidden" labelled checkbox field on the main page
        And the user ticks the checkbox field

        And the user selects the "field" bound node-browser-tree field on the main page
        Then the helper text of the node-browser-tree field is hidden


    Scenario: As an ATP XTreeM user I can verify the enabled or disabled state of node-browser-tree
        XT-55594
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/NodeBrowserTreePage"
        Then the "Node Browser Tree" titled page is displayed

        And the user selects the "field" bound node-browser-tree field on the main page
        And the node-browser-tree field is enabled

        And the user selects the "Is disabled" labelled checkbox field on the main page
        And the user ticks the checkbox field

        And the user selects the "field" bound node-browser-tree field on the main page
        And the node-browser-tree field is disabled


    Scenario: As an ATP XTreeM user I can verify the read-only state of node-browser-tree
        XT-55594
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/NodeBrowserTreePage"
        Then the "Node Browser Tree" titled page is displayed

        And the user selects the "Is read only" labelled checkbox field on the main page
        And the user ticks the checkbox field

        And the user selects the "field" bound node-browser-tree field on the main page
        And the node-browser-tree field is read-only


    Scenario: As an ATP XTreeM user I can verify the visibility state of node-browser-tree
        XT-55594
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/NodeBrowserTreePage"
        Then the "Node Browser Tree" titled page is displayed

        Then the "Node Browser Tree" labelled node-browser-tree field on the main page is displayed

        And the user selects the "Is hidden" labelled checkbox field on the main page
        And the user ticks the checkbox field

        Then the "Node Browser Tree" labelled node-browser-tree field on the main page is hidden
