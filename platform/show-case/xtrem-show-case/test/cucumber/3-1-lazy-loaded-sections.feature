Feature: 3-1 Lazy loaded sections
    # Tests the lazy loading mechanism for UI sections and tabs, verifying that content loads progressively and correctly as users interact with the interface

    Scenario: As a user I want lazy loaded tabs to progressivly load
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/LazyLoadedShowCaseProvider/eyJfaWQiOiIyIn0="
        Then the "Provider 2" titled page is displayed
        Given the user selects the "Name" labelled text field on the main page
        Then the value of the text field is "Amazon"
        When selects the "Products" labelled navigation anchor on the main page
        And the user selects the "products" bound table field on the main page
        And the user selects the row 1 of the table field
        Then the value of the "product" labelled nested text field of the selected row in the table field is "Beef - Bresaola"
        When selects the "Another section" labelled navigation anchor on the main page
        Given the user selects the "Address" labelled text field on the main page
        Then the value of the text field is "Carrer Jesús <PERSON> 2Sant Cugat del VallèsSpain"

    Scenario: As a user I want to modify the first section but I want to keep the second section unchanged
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/LazyLoadedShowCaseProvider/eyJfaWQiOiIyIn0="
        Then the "Provider 2" titled page is displayed
        Given the user selects the "Integer" labelled numeric field on the main page
        Then the value of the numeric field is "2"
        When the user writes "3" in the numeric field
        And the user presses Tab
        And the user clicks the "Save" labelled business action button on the main page
        And a toast with text "Record updated" is displayed
        Then the user navigates to the following link: "@sage/xtrem-show-case/LazyLoadedShowCaseProvider/eyJfaWQiOiIyIn0="
        Given the user selects the "Integer" labelled numeric field on the main page
        Then the value of the numeric field is "3"
        When the user writes "2" in the numeric field
        When selects the "Products" labelled navigation anchor on the main page
        And the user selects the "products" bound table field on the main page
        And the user selects the row 1 of the table field
        Then the value of the "product" labelled nested text field of the selected row in the table field is "Beef - Bresaola"
        When selects the "Another section" labelled navigation anchor on the main page
        Given the user selects the "Address" labelled text field on the main page
        Then the value of the text field is "Carrer Jesús Serra Santamans 2Sant Cugat del VallèsSpain"
        And the user clicks the "Save" labelled business action button on the main page
        And a toast with text "Record updated" is displayed

    Scenario: As a user I want to modify records on a lazy loaded table and save it
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/LazyLoadedShowCaseProvider/eyJfaWQiOiIyIn0="
        Then the "Provider 2" titled page is displayed
        Given the user selects the "Name" labelled text field on the main page
        Then the value of the text field is "Amazon"
        When selects the "Products" labelled navigation anchor on the main page
        And the user selects the "products" bound table field on the main page
        And the user selects the row 4 of the table field
        Then the value of the "product" labelled nested text field of the selected row in the table field is "Garbag Bags - Black"
        When the user writes "Garbag Bags - White" in the "product" bound nested numeric field of the selected row in the table field
        And the user clicks the "Save" labelled business action button on the main page
        And a toast with text "Record updated" is displayed
        Then the user navigates to the following link: "@sage/xtrem-show-case/LazyLoadedShowCaseProvider/eyJfaWQiOiIyIn0="
        Then the "Provider 2" titled page is displayed
        When selects the "Products" labelled navigation anchor on the main page
        And the user selects the "products" bound table field on the main page
        And the user selects the row 4 of the table field
        Then the value of the "product" labelled nested text field of the selected row in the table field is "Garbag Bags - White"
        When the user writes "Garbag Bags - Black" in the "product" bound nested numeric field of the selected row in the table field
        And the user clicks the "Save" labelled business action button on the main page
        And a toast with text "Record updated" is displayed
