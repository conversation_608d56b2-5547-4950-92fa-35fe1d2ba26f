Feature: 2-1 Image
    # Tests the image field component across different devices, verifying image upload, display, and removal functionality


    Scenario Outline: <Device> - As and ATP XTreeM user I can verify the image is defined or undifined
        XT-56122
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Image"
        Then the "Image" titled page is displayed

        Given the user selects the "field" bound image field on the main page
        Then the image field is undefined

        When the user adds the "./image/sagelogo.png" image to the image field
        And the user waits 2 seconds
        # And takes a screenshot

        Given the user selects the "field" bound image field on the main page
        Then the image field is defined
        When the user removes the image from the image field
        Then the image field is undefined
        # And takes a screenshot

        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |


    Scenario Outline: <Device> - As and ATP XTreeM user I can verify the image field title value and visibility
        XT-56122
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Image"
        Then the "Image" titled page is displayed

        Given the user selects the "Title" labelled text field on the main page
        When the user writes "Sample title" in the text field
        And the user presses Tab

        Given the user selects the "field" bound image field on the main page
        When the user adds the "./image/sagelogo.png" image to the image field
        Then the title of the image field is "Sample title"
        Then the title of the image field is displayed

        Given the user selects the "Is Title Hidden?" labelled checkbox field on the main page
        When the user ticks the checkbox field

        Given the user selects the "field" bound image field on the main page
        Then the title of the image field is hidden
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |


    Scenario Outline: <Device> - As and ATP XTreeM user I can verify the image field helper text value and visibility
        XT-56122
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Image"
        Then the "Image" titled page is displayed

        Given the user selects the "Helper text" labelled text field on the main page
        When the user writes "Sample text" in the text field
        And the user presses Tab

        Given the user selects the "field" bound image field on the main page
        When the user adds the "./image/sagelogo.png" image to the image field
        Then the helper text of the image field is "Sample text"
        Then the helper text of the image field is displayed

        Given the user selects the "Is Helper Text Hidden?" labelled checkbox field on the main page
        When the user ticks the checkbox field

        Given the user selects the "field" bound image field on the main page
        Then the helper text of the image field is hidden
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |


    Scenario Outline: <Device> - As and ATP XTreeM user I can verify if the image (defined) field is displayed or hidden
        XT-56122
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Image"
        Then the "Image" titled page is displayed

        Given the user selects the "field" bound image field on the main page
        When the user adds the "./image/sagelogo.png" image to the image field
        And the user waits 2 seconds
        Then the image field is defined
        Then the "field" bound image field on the main page is displayed

        Given the user selects the "Is Hidden?" labelled checkbox field on the main page
        When the user ticks the checkbox field
        Then the "field" bound image field on the main page is hidden
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |


    Scenario Outline: <Device> - As and ATP XTreeM user I can verify if the image (undefined) field is displayed or hidden
        XT-56122
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Image"
        Then the "Image" titled page is displayed

        Given the user selects the "field" bound image field on the main page
        Then the image field is undefined
        Then the "field" bound image field on the main page is displayed

        Given the user selects the "Is Hidden?" labelled checkbox field on the main page
        When the user ticks the checkbox field
        Then the "field" bound image field on the main page is hidden
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |


    Scenario Outline:<Device> - As and ATP XTreeM user I can verify if the image (defined) field is enabled or disabled
        XT-56122
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Image"
        Then the "Image" titled page is displayed

        Given the user selects the "field" bound image field on the main page
        When the user adds the "./image/sagelogo.png" image to the image field
        And the user waits 2 seconds
        Then the image field is defined
        Then the image field is enabled

        Given the user selects the "Is Disabled?" labelled checkbox field on the main page
        When the user ticks the checkbox field

        Given the user selects the "field" bound image field on the main page
        Then the image field is disabled
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |


    Scenario Outline:<Device> - As and ATP XTreeM user I can verify if the image (undefined) field is enabled or disabled
        XT-56122
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Image"
        Then the "Image" titled page is displayed

        Given the user selects the "field" bound image field on the main page
        Then the image field is undefined
        Then the image field is enabled

        Given the user selects the "Is Disabled?" labelled checkbox field on the main page
        When the user ticks the checkbox field

        Given the user selects the "field" bound image field on the main page
        Then the image field is disabled
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |


    Scenario Outline: <Device> - As and ATP XTreeM user I can verify if the image (defined) field is read-only
        XT-56122
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Image"
        Then the "Image" titled page is displayed

        Given the user selects the "field" bound image field on the main page
        When the user adds the "./image/sagelogo.png" image to the image field
        And the user waits 2 seconds
        Then the image field is defined

        And the user selects the "Is Readonly?" labelled checkbox field on the main page
        When the user ticks the checkbox field

        And  the user selects the "field" bound image field on the main page
        Then the image field is read-only
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> - As and ATP XTreeM user I can verify if the image (undefined) field is read-only
        XT-56122
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Image"
        Then the "Image" titled page is displayed

        And the user selects the "Is Readonly?" labelled checkbox field on the main page
        When the user ticks the checkbox field

        And  the user selects the "field" bound image field on the main page
        Then the image field is undefined
        Then the image field is read-only
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |
