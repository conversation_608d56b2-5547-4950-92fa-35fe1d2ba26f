Feature: 3-2 Step sequence
    # Tests the functionality of the step-sequence component, focusing on title visibility, navigation between steps, and interaction with step content

    Scenario: As an ATP XTreeM User I can verify the title of the step-sequence
        XT-27726
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/StepSequence"
        Then the "Field - Step Sequence" titled page is displayed

        When the user selects the "Title" labelled text field on the main page
        And the user writes "My Title" in the text field
        And the user blurs the text field

        And the user selects the "My Title" labelled step-sequence field on the main page
        Then the title of the step-sequence field is "My Title"
        Then the title of the step-sequence field is displayed
        # And takes a screenshot


        When the user selects the "Is Title Hidden?" labelled checkbox field on the main page
        And the user ticks the checkbox field

        And the user selects the "field" bound step-sequence field on the main page
        Then the title of the step-sequence field is hidden
    # And takes a screenshot


    Scenario: As an ATP XTreeM User I can verify the helper text of the step-sequence
        XT-27726
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/StepSequence"
        Then the "Field - Step Sequence" titled page is displayed

        When the user selects the "Helper Text" labelled text field on the main page
        And the user writes "Sample Text" in the text field
        And the user blurs the text field

        And the user selects the "field" bound step-sequence field on the main page
        Then the helper text of the step-sequence field is "Sample Text"
        Then the helper text of the step-sequence field is displayed
        # And takes a screenshot

        When the user selects the "Is Helper Text Hidden?" labelled checkbox field on the main page
        And the user ticks the checkbox field

        And the user selects the "field" bound step-sequence field on the main page
        Then the helper text of the step-sequence field is hidden
    # And takes a screenshot



    Scenario: As an ATP XTreeM User I can verify the status of the Horiztonal step-sequence item
        XT-27726

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/StepSequence"
        Then the "Field - Step Sequence" titled page is displayed

        And the user selects the "field" bound step-sequence field on the main page
        Then the status of the "Create" item of the step-sequence is complete
        Then the status of the "Approve" item of the step-sequence is complete
        Then the status of the "Order" item of the step-sequence is complete
        Then the status of the "Receive" item of the step-sequence is current
        Then the status of the "Invoice" item of the step-sequence is incomplete
        # And takes a screenshot

        When the user selects the "Title" labelled text field on the main page
        And the user writes "My Title" in the text field
        And the user blurs the text field

        And the user selects the "My Title" labelled step-sequence field on the main page
        Then the status of the "Create" item of the step-sequence is complete
        Then the status of the "Approve" item of the step-sequence is complete
        Then the status of the "Order" item of the step-sequence is complete
        Then the status of the "Receive" item of the step-sequence is current
        Then the status of the "Invoice" item of the step-sequence is incomplete
    # And takes a screenshot

    Scenario: As an ATP XTreeM User I can verify the status of the Vertical step-sequence item
        XT-27726

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/StepSequence"
        Then the "Field - Step Sequence" titled page is displayed

        And the user selects the "field2" bound step-sequence field on the main page
        Then the status of the "Create" item of the step-sequence is complete
        Then the status of the "Approve" item of the step-sequence is current
        Then the status of the "Order" item of the step-sequence is incomplete
        Then the status of the "Receive" item of the step-sequence is incomplete
        Then the status of the "Invoice" item of the step-sequence is incomplete
    # And takes a screenshot


    Scenario: As an ATP XTreeM User I can verify the orientation of the step-sequence
        XT-27726
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/StepSequence"
        Then the "Field - Step Sequence" titled page is displayed

        When the user selects the "field" bound step-sequence field on the main page
        Then the orientation of the step-sequence field is horizontal
        # And takes a screenshot

        When the user selects the "field2" bound step-sequence field on the main page
        Then the orientation of the step-sequence field is vertical
    # And takes a screenshot

    Scenario: As an ATP XTreeM User I can verify the visibility the step-sequence
        XT-27726

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/StepSequence"
        Then the "Field - Step Sequence" titled page is displayed

        When the user selects the "Title" labelled text field on the main page
        And the user writes "My Title" in the text field
        And the user blurs the text field

        Then the "field" bound step-sequence field on the main page is displayed
        Then the "My Title" labelled step-sequence field on the main page is displayed
        # And takes a screenshot

        When the user selects the "Is Hidden?" labelled checkbox field on the main page
        And the user ticks the checkbox field

        Then the "field" bound step-sequence field on the main page is hidden
        Then the "My Title" labelled step-sequence field on the main page is hidden
# And takes a screenshot
