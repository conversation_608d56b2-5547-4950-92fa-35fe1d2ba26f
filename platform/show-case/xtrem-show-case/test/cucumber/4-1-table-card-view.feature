Feature: 4-1 Table card view
    # Tests the table interaction rendered as table cards across different devices (desktop / mobile / tablet).

    Scenario Outline: <Device> - As a user I can click on a card
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/TableCardView/eyJfaWQiOiIyIn0="
        And the user selects the "field" bound table field on the main page
        And the user clicks the card 2 in the table field
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> - As a user I can tick or tick a card
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/TableCardView/eyJfaWQiOiIyIn0="
        And the user selects the "field" bound table field on the main page
        And the user ticks the main checkbox of the card 2 in the table field
        And the user unticks the main checkbox of the card 2 in the table field
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> - As a user I can tick or tick a card with text
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/TableCardView/eyJfaWQiOiIyIn0="
        And the user selects the "field" bound table field on the main page
        And the user ticks the main checkbox of the card with the text "55.19" in the table field
        And the user unticks the main checkbox of the card with the text "55.19" in the table field
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> - As a user I can check the value of the nested field in the table field
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/TableCardView/eyJfaWQiOiIyIn0="
        And the user selects the "field" bound table field on the main page
        Then the value of the "amount" bound nested label field of the card 2 in the table field is "$ 55.19"
        Then the value of the "category" bound nested select field of the card 2 in the table field is "Good"
        Then the value of the "releaseDate" bound nested date field of the card 2 in the table field is "05/30/2020"
        Then the value of the "provider" bound nested reference field of the card 2 in the table field is "Amazon"
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> - As a user I can click on the filter button of the card list header
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/TableCardView/eyJfaWQiOiIyIn0="
        And the user selects the "field" bound table field on the main page
        And the user clicks the "Filter" button in the header of the table field
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> - As a user I can click on the dropdown action of the table field
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/TableCardView"
        And the user selects the "field" bound table field on the main page
        And the user clicks the "Add" dropdown action of the card 2 in the table field
        And the user clicks the "OK" button of the Message dialog on the main page
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> - As a user I can click on the dropdown action of the table field 2
        Given the user opens the application on a mobile using the following link: "@sage/xtrem-show-case/TableCardView"
        And the user selects the "field" bound table field on the main page
        And the user clicks the <DropdownAction> dropdown action of the card 1 in the table field
        Examples:
            | DropdownAction   |
            | "Add"            |
            | "Maybe disabled" |
            | "Action no icon" |

    Scenario Outline: <Device> - As a user I can check if the dropdown action of the card list is enabled or disabled
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/TableCardView/eyJfaWQiOiIyIn0="
        And the user selects the "field" bound table field on the main page
        Then the "Add" dropdown action of the card 2 in the table field is enabled
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> - As an ATP XTreeM user I can control the image visibility in the card list row
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/TableCardView/eyJfaWQiOiIyIn0="
        And the user selects the "field" bound table field on the main page
        Then the "imageField" bound nested image field of the card 1 in the table field is defined
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario: Mobile -  As a user I can check the value of the nested field in the header card list
        Given the user opens the application on a mobile using the following link: "@sage/xtrem-show-case/ShowCaseProductHeaderCard/eyJfaWQiOiIyMTAifQ=="
        Then the value of the "amount" bound nested numeric field in the header card is "104.57"
        Then the value of the "product" bound nested text field in the header card is "Nantucket - 518ml"
        Then the value of the "description" bound nested text field in the header card is "Extended"
        Then the value of the "amount" bound nested numeric field in the header card is "104.57"
        Then the value of the "qty" bound nested numeric field in the header card is "1"

    Scenario: Mobile -  As a user I can verify image in the card list header is defined
        Given the user opens the application on a mobile using the following link: "@sage/xtrem-show-case/ShowCaseProductHeaderCard/eyJfaWQiOiIyMTAifQ=="
        Then the "imageField" bound nested image field on the header card is defined
    # And takes a screenshot

    Scenario: Mobile - As a user I can verify all the cards in the table field are selected / unselected
        Given the user opens the application on a mobile using the following link: "@sage/xtrem-show-case/TableCardView/eyJfaWQiOiIyIn0="
        Then the titled page containing "Field - Table card view" is displayed
        And the user selects the "field" bound table field on the main page
        Then all the cards in the table field are unselected
        And the user ticks the main checkbox of the card 1 in the table field
        And the user ticks the main checkbox of the card 2 in the table field
        And the user ticks the main checkbox of the card 3 in the table field
        And the user ticks the main checkbox of the card 4 in the table field
        And the user ticks the main checkbox of the card 5 in the table field
        And the user ticks the main checkbox of the card 6 in the table field
        And the user ticks the main checkbox of the card 7 in the table field
        And the user ticks the main checkbox of the card 8 in the table field
        And the user ticks the main checkbox of the card 9 in the table field
        And the user ticks the main checkbox of the card 10 in the table field
        And the user ticks the main checkbox of the card 11 in the table field
        And the user ticks the main checkbox of the card 12 in the table field
        And the user ticks the main checkbox of the card 13 in the table field
        And the user ticks the main checkbox of the card 14 in the table field
        And the user ticks the main checkbox of the card 15 in the table field
        And the user ticks the main checkbox of the card 16 in the table field
        And the user ticks the main checkbox of the card 17 in the table field
        And the user ticks the main checkbox of the card 18 in the table field
        And the user ticks the main checkbox of the card 19 in the table field
        And the user ticks the main checkbox of the card 20 in the table field
        Then all the cards in the table field are selected
