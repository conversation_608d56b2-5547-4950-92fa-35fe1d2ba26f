Feature: 1-1 Bulk actions
    # Tests the functionality of performing bulk operations on multiple selected table rows, including selection controls, action buttons, and confirmation dialogs

    <PERSON><PERSON><PERSON>: As a user I want to be able to perform a bulk action on multiple rows of a table
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseProvider"
        Then the "Providers" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        When the user ticks the main checkbox of the selected row in the table field
        Then the bulk action bar of the table field has 1 items
        And the "Modify" labelled bulk action button of the table field is displayed
        And the "Export" labelled bulk action button of the table field is displayed
        And the "Print" labelled bulk action button of the table field is displayed
        And the "Delete" labelled bulk action button of the table field is displayed
        And the clear selection button on the bulk action bar of the table field is displayed
        And the user selects the row 2 of the table field
        When the user ticks the main checkbox of the selected row in the table field
        Then the bulk action bar of the table field has 2 items
        When the user clicks the "Export" labelled bulk action button of the table field
        Then a warn dialog appears on the main page
        And the text in the header of the dialog is "Export"
        And the text in the body of the dialog is "Perform this action on the selected items: 2"
        When the user clicks the "OK" button of the Confirm dialog
        Then a toast containing text "Action started on the selected items." is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        Then the selected row of the table field is unselected
        And the user selects the row 2 of the table field
        Then the selected row of the table field is unselected

    Scenario: As a user I want to be able to see a notification when a bulk action fails
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseProvider"
        Then the "Providers" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        When the user selects the row 1 of the table field
        When the user ticks the main checkbox of the selected row in the table field
        Then the "Modify" labelled bulk action button of the table field is displayed
        When the user clicks the "Modify" labelled bulk action button of the table field
        And the user clicks the "OK" button of the Confirm dialog
        Then a error toast containing text "The action could not be started. Try again." is displayed


    Scenario: As a user I want to stop seeing bulk action buttons
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseProvider"
        Then the "Providers" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        When the user ticks the main checkbox of the selected row in the table field
        Then the bulk action bar of the table field is displayed
        When the user unticks the main checkbox of the selected row in the table field
        Then the bulk action bar of the table field is hidden

    Scenario: As a user I want to be able to clear selection of multiple rows
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseProvider"
        Then the "Providers" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        When the user ticks the main checkbox of the selected row in the table field
        And the user selects the row 2 of the table field
        When the user ticks the main checkbox of the selected row in the table field
        Then the bulk action bar of the table field has 2 items
        When the user clicks the clear selection button on the bulk action bar of the table field
        And the user selects the row 1 of the table field
        Then the selected row of the table field is unselected
        And the user selects the row 2 of the table field
        Then the selected row of the table field is unselected
        When the user selects the row 1 of the table field
        And the user ticks the main checkbox of the selected row in the table field
        And the user clicks the clear selection button on the bulk action bar of the table field
        And the user ticks the main checkbox of the selected row in the table field
        And the user clicks the clear selection button on the bulk action bar of the table field
        And the user ticks the main checkbox of the selected row in the table field
        Then the bulk action bar of the table field has 1 items
        When the user clicks the clear selection button on the bulk action bar of the table field
        And the user selects the row 1 of the table field
        Then the selected row of the table field is unselected

    Scenario: As a user I want to perform a bulk action using the select all option in the table
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseProduct"
        Then the "Products" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        When the user filters the "Product" labelled column in the table field with value "Wine"
        And the user selects the row 1 of the table field
        Then the value of the "product" bound nested text field of the selected row in the table field is "Cheese - Wine"
        When the user selects all rows of the table field
        Then the bulk action bar of the table field has 37 items

        And the user selects the row 1 of the table field
        Then the selected row of the table field is selected
        And the user selects the row 10 of the table field
        Then the selected row of the table field is selected
        When the user clicks the "Export" labelled bulk action button of the table field
        Then a warn dialog appears on the main page
        And the text in the header of the dialog is "Export"
        And the text in the body of the dialog is "Perform this action on the selected items: 37"
        When the user clicks the "OK" button of the Confirm dialog
        Then a toast containing text "Action started on the selected items." is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        Then the selected row of the table field is unselected
        And the user selects the row 10 of the table field
        Then the selected row of the table field is unselected

    Scenario: As a user I want to perform bulk actions on a large number of rows but only on the records I need
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseProduct"
        Then the "Products" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        When the user filters the "Product" labelled column in the table field with value "Wine"
        And the user selects the row 1 of the table field
        Then the value of the "product" bound nested text field of the selected row in the table field is "Cheese - Wine"
        When the user selects all rows of the table field
        Then the bulk action bar of the table field has 37 items
        And the user selects the row 1 of the table field
        When the user unticks the main checkbox of the selected row in the table field
        Then the bulk action bar of the table field has 36 items
        When the user clicks the "Export" labelled bulk action button of the table field
        Then a warn dialog appears on the main page
        And the text in the header of the dialog is "Export"
        And the text in the body of the dialog is "Perform this action on the selected items: 36"

    Scenario: As a user I want to be able to unselect all rows once I selected them
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseProduct"
        Then the "Products" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        When the user selects all rows of the table field
        Then the bulk action bar of the table field has 503 items
        When the user unselects all rows of the table field
        Then the bulk action bar of the table field is hidden

    Scenario: As a user I want to be able to set parameters for a bulk mutation
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseProduct"
        Then the "Products" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 35 of the table field
        Then the value of the "product" bound nested label field of the selected row in the table field is "Beer - Original Organic Lager"
        And the user selects the row 36 of the table field
        Then the value of the "product" bound nested label field of the selected row in the table field is "Beer - true North Strong Ale"
        And the user selects the row 35 of the table field
        And the user ticks the main checkbox of the selected row in the table field
        And the user selects the row 36 of the table field
        And the user ticks the main checkbox of the selected row in the table field
        Then the bulk action bar of the table field has 2 items
        Then the user clicks the "Add prefix to name" labelled bulk action button of the table field
        And the text in the header of the dialog is "Mass product renaming"
        And the user selects the "Prefix" labelled text field on a modal
        And the user writes "ZZZZ" in the text field
        And the user clicks the "Confirm" labelled business action button on a modal
        And the text in the body of the dialog is "Perform this action on the selected items: 2"
        Then the user clicks the "OK" button of the Confirm dialog
        Then a toast containing text "Action started on the selected items." is displayed
        When the user navigates to the following link: "@sage/xtrem-show-case/ShowCaseProduct"
        Then the "Products" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        Then the user filters the "product" bound column in the table field with value "ZZZZ"
        And the user selects the row 1 of the table field
        Then the value of the "product" bound nested label field of the selected row in the table field is "ZZZZ Beer - Original Organic Lager"
        And the user selects the row 2 of the table field
        Then the value of the "product" bound nested label field of the selected row in the table field is "ZZZZ Beer - true North Strong Ale"

        # restore the data
        Then the user navigates to the following link: "@sage/xtrem-show-case/ShowCaseProduct/eyJfaWQiOiI0MDQifQ=="
        Given the user selects the "Product" labelled text field on the main page
        When the user writes "Beer - Original Organic Lager" in the text field
        And the user clicks the "Save" labelled business action button on the main page
        And the user waits 1 second
        Then the user navigates to the following link: "@sage/xtrem-show-case/ShowCaseProduct/eyJfaWQiOiIxNTkifQ=="
        Given the user selects the "Product" labelled text field on the main page
        When the user writes "Beer - true North Strong Ale" in the text field
        And the user clicks the "Save" labelled business action button on the main page
        And the user waits 1 second

    Scenario: As a user I want the async export bulk action to skip the template selection if there is only one template assoicated with the page node
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseCountry"
        Then the "Countries" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        When the user ticks the main checkbox of the selected row in the table field
        And the user selects the row 2 of the table field
        When the user ticks the main checkbox of the selected row in the table field
        Then the bulk action bar of the table field has 2 items
        And the "asyncExport" bound bulk action button of the table field is displayed
        When the user clicks the "asyncExport" bound bulk action button of the table field
        Then a warn dialog appears on the main page
        And the text in the header of the dialog is "Export"
        And the text in the body of the dialog is "Perform this action on the selected items: 2"
        When the user clicks the "OK" button of the Confirm dialog
        Then a toast containing text "Action started on the selected items." is displayed
        When the user navigates to the following link: "@sage/xtrem-communication/SysNotificationState"
        Then the "Batch task history" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        When the user filters the "User" labelled column in the table field with value "<EMAIL>"
        And the user selects the row 1 of the table field
        And the user clicks the "Status" labelled nested field of the selected row in the table field
        And the user selects the "Parameters" labelled table field on the main page
        And the user selects the row 2 of the table field
        Then the value of the "Value" labelled nested numeric field of the selected row in the table field is "ShowCaseCountry"

    Scenario: As a user I want the async export bulk action to display the template selection dialog
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseProvider"
        Then the "Providers" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        When the user ticks the main checkbox of the selected row in the table field
        And the user selects the row 2 of the table field
        When the user ticks the main checkbox of the selected row in the table field
        Then the bulk action bar of the table field has 2 items
        And the "asyncExport" bound bulk action button of the table field is displayed
        When the user clicks the "asyncExport" bound bulk action button of the table field
        And the text in the header of the dialog is "Select export template"
        And the user selects the "Export template" labelled reference field on a modal
        Then the value of the reference field is "ShowCaseProvider"
        When the user clears the reference field
        When the user writes "Provider template" in the reference field
        And the user selects "Provider template" in the reference field
        And the user clicks the "Confirm" labelled business action button on a modal
        Then a warn dialog appears on the main page
        And the text in the header of the dialog is "Export"
        And the text in the body of the dialog is "Perform this action on the selected items: 2"
        When the user clicks the "OK" button of the Confirm dialog
        Then a toast containing text "Action started on the selected items." is displayed
        When the user navigates to the following link: "@sage/xtrem-communication/SysNotificationState"
        Then the "Batch task history" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        When the user filters the "User" labelled column in the table field with value "<EMAIL>"
        And the user selects the row 1 of the table field
        And the user clicks the "Status" labelled nested field of the selected row in the table field
        And the user selects the "Parameters" labelled table field on the main page
        And the user selects the row 2 of the table field
        And the value of the "Value" labelled nested numeric field of the selected row in the table field is "providerTemplate"

    Scenario: As a user I want to make sure bulk actions work after I open and close a record
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseProduct"
        Then the "Products" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "Anisette - Mcguiness" in the "Product" labelled column header of the table field
        And the user clicks the "Product" labelled nested field of the selected row in the table field
        Then the "Product Anisette - Mcguiness" titled page is displayed
        When the user clicks the "Close record" icon in the header on the main page
        Then the "Products" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row 1 of the table field
        When the user ticks the main checkbox of the selected row in the table field
        Then the bulk action bar of the table field has 1 items
        And the user selects the row 2 of the table field
        When the user ticks the main checkbox of the selected row in the table field
        Then the bulk action bar of the table field has 2 items

    Scenario: As a user I want to be able to execute bulk actions on reference field groupped main list data
        XT-70835
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseProduct"
        Then the "Products" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        When the user clicks the "Open column panel" labelled button of the table field
        Then the "Column settings" titled sidebar is displayed
        And the table column configuration with name "Supplier" on the sidebar is unticked
        When the user ticks the table column configuration with "Supplier" name on the sidebar
        And the user presses Escape
        Then the group by option in the header menu of the "Supplier" labelled column of the table field is displayed
        Then the user clicks the group by option in the header menu of the "Supplier" labelled column of the table field
        And the user selects the row 1 of the table field
        When the user ticks the main checkbox of the selected row in the table field
        And the user selects the row 2 of the table field
        When the user ticks the main checkbox of the selected row in the table field
        Then the bulk action bar of the table field has 500 items
