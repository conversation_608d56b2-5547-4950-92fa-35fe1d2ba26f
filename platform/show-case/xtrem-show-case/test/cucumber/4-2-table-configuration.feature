Feature: 4-2 Table configuration
    # Tests table configuration functionality, verifying column header customization, visibility options, and persistence of table display preferences

    Sc<PERSON>rio: As an ATP XTreeM user I can setup the data table column header configuration
        XT-42103


        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Table/eyJfaWQiOiIyIn0="
        Then the "Field - Table" titled page is displayed

        Given the user selects the "field" bound table field on the main page
        When the user clicks the "Open column panel" labelled button of the table field
        Then the "Column settings" titled sidebar is displayed

        When searches for "Pro" in the lookup dialog on the sidebar

        Then the table column configuration with name "<PERSON>" on the sidebar is ticked
        Then the table column configuration with name "<PERSON>" on the sidebar is ticked
        Then the table column configuration with name "Provider" on the sidebar is ticked
        # And takes a screenshot

        When the user unticks the table column configuration with "Progress" name on the sidebar
        Then the table column configuration with name "Progress" on the sidebar is unticked
        # And takes a screenshot

        When the user ticks the table column configuration with "Progress" name on the sidebar
        Then the table column configuration with name "<PERSON>" on the sidebar is ticked
    # And takes a screenshot


    Scenario: As an ATP XTreeM user I can verify if the data table column is locked
        XT-42103


        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Table/eyJfaWQiOiIyIn0="
        Then the "Field - Table" titled page is displayed

        Given the user selects the "field" bound table field on the main page
        When the user clicks the "Open column panel" labelled button of the table field
        Then the "Column settings" titled sidebar is displayed


        Then the table column configuration with name "Tax" on the sidebar is locked
        Then the table column configuration with name "Product" on the sidebar is unlocked
    # And takes a screenshot


    Scenario: As an ATP XTreeM user I can verify the data table column header configuration order
        XT-42103

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Table/eyJfaWQiOiIyIn0="
        Then the "Field - Table" titled page is displayed

        Given the user selects the "field" bound table field on the main page
        When the user clicks the "Open column panel" labelled button of the table field
        Then the "Column settings" titled sidebar is displayed


        Then the table column configuration on the sidebar are displayed in the following order "Id,Product,Description,Quantity,Fixed Quantity"
# And takes a screenshot
