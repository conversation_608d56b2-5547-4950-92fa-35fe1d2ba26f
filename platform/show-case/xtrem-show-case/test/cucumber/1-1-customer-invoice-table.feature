Feature: 1-1 Customer invoice table
    # Tests filtering capabilities of the customer invoice table using different quantifier types (_atLeast, _atMost, _every, _none)
    Test _atLeast, _atMost, _every and _none quantifiers
    Scenario: As a user I want to filter the table with using an _atLeast quantifier
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseCustomerInvoiceTable/eyJfaWQiOiIxMjgifQ=="
        And the user selects the "shouldFilter" bound switch field on the main page
        When the user clicks in the switch field
        And the user selects the "numberOfCases" bound numeric field on the main page
        And the user writes "2" in the numeric field
        And the user selects the "quantityOfProducts" bound numeric field on the main page
        And the user writes "11" in the numeric field
        And the user selects the "greaterThanProductQuantity" bound switch field on the main page
        When the user clicks in the switch field
        And the user clicks the "applyFilter" bound button on the main page
        And the user selects the "field" bound table field on the main page
        And the user selects the row 1 of the table field
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "09/08/2019"
        And the user selects the row 2 of the table field
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "04/17/2020"
        And the user selects the "quantityOfProducts" bound numeric field on the main page
        And the user writes "13" in the numeric field
        And the user selects the "greaterThanProductQuantity" bound switch field on the main page
        When the user clicks in the switch field
        And the user clicks the "applyFilter" bound button on the main page
        And the user selects the "field" bound table field on the main page
        And the user selects the row 1 of the table field
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "09/08/2019"

    Scenario: As a user I want to filter the table with using an _every quantifier
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseCustomerInvoiceTable/eyJfaWQiOiIxMjgifQ=="
        And the user selects the "shouldFilter" bound switch field on the main page
        When the user clicks in the switch field
        And the user selects the "operatorType" bound select field on the main page
        And the user writes "every" in the select field
        # And the user selects "every" in the select field
        And the user selects the "quantityOfProducts" bound numeric field on the main page
        And the user writes "10" in the numeric field
        And the user selects the "greaterThanProductQuantity" bound switch field on the main page
        When the user clicks in the switch field
        And the user clicks the "applyFilter" bound button on the main page
        And the user selects the "field" bound table field on the main page
        And the user selects the row 1 of the table field
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "02/22/2020"
        And the user selects the row 2 of the table field
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "04/17/2020"
        And the user selects the "quantityOfProducts" bound numeric field on the main page
        And the user writes "15" in the numeric field
        And the user selects the "greaterThanProductQuantity" bound switch field on the main page
        When the user clicks in the switch field
        And the user clicks the "applyFilter" bound button on the main page
        And the user selects the "field" bound table field on the main page
        And the user selects the row 1 of the table field
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "09/08/2019"
        And the user selects the row 2 of the table field
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "02/22/2020"
        And the user selects the row 3 of the table field
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "04/22/2020"

    Scenario: As a user I want to filter the table with using a _none quantifier
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseCustomerInvoiceTable/eyJfaWQiOiIxMjgifQ=="
        And the user selects the "shouldFilter" bound switch field on the main page
        When the user clicks in the switch field
        And the user selects the "operatorType" bound select field on the main page
        And the user writes "none" in the select field
        # And the user selects "none" in the select field
        And the user selects the "quantityOfOrders" bound numeric field on the main page
        And the user writes "21" in the numeric field
        And the user selects the "greaterThanOrderQuantity" bound switch field on the main page
        When the user clicks in the switch field
        And the user clicks the "applyFilter" bound button on the main page
        And the user selects the "field" bound table field on the main page
        And the user selects the row 1 of the table field
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "04/17/2020"
        And the user selects the "quantityOfOrders" bound numeric field on the main page
        And the user writes "7" in the numeric field
        And the user selects the "greaterThanOrderQuantity" bound switch field on the main page
        When the user clicks in the switch field
        And the user clicks the "applyFilter" bound button on the main page
        And the user selects the "field" bound table field on the main page
        And the user selects the row 1 of the table field
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "09/08/2019"
        And the user selects the row 2 of the table field
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "02/22/2020"
        And the user selects the row 3 of the table field
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "04/22/2020"

    Scenario: As a user I want to filter the table with using an _atMost quantifier
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseCustomerInvoiceTable/eyJfaWQiOiIxMjgifQ=="
        And the user selects the "shouldFilter" bound switch field on the main page
        When the user clicks in the switch field
        And the user selects the "operatorType" bound select field on the main page
        And the user writes "at most" in the select field
        # And the user selects "at most" in the select field
        And the user selects the "numberOfCases" bound numeric field on the main page
        And the user writes "0" in the numeric field
        And the user selects the "quantityOfOrders" bound numeric field on the main page
        And the user writes "20" in the numeric field
        And the user selects the "greaterThanOrderQuantity" bound switch field on the main page
        When the user clicks in the switch field
        And the user clicks the "applyFilter" bound button on the main page
        And the user selects the "field" bound table field on the main page
        And the user selects the row 1 of the table field
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "04/17/2020"
        And the user selects the "numberOfCases" bound numeric field on the main page
        And the user writes "1" in the numeric field
        And the user selects the "quantityOfOrders" bound numeric field on the main page
        And the user writes "21" in the numeric field
        And the user selects the "greaterThanOrderQuantity" bound switch field on the main page
        When the user clicks in the switch field
        And the user clicks the "applyFilter" bound button on the main page
        And the user selects the "field" bound table field on the main page
        And the user selects the row 1 of the table field
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "09/08/2019"
        And the user selects the row 2 of the table field
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "02/22/2020"
        And the user selects the row 3 of the table field
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "04/22/2020"

    Scenario: As a user I want to filter the table with using an _atLeast quantifier and change it to another _atLeast quantifier
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseCustomerInvoiceTable/eyJfaWQiOiIxMjgifQ=="
        And the user selects the "shouldFilter" bound switch field on the main page
        When the user clicks in the switch field
        And the user selects the "numberOfCases" bound numeric field on the main page
        And the user writes "2" in the numeric field
        And the user selects the "quantityOfProducts" bound numeric field on the main page
        And the user writes "11" in the numeric field
        And the user selects the "greaterThanProductQuantity" bound switch field on the main page
        When the user clicks in the switch field
        And the user clicks the "applyFilter" bound button on the main page
        And the user selects the "field" bound table field on the main page
        And the user selects the row 1 of the table field
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "09/08/2019"
        And the user selects the row 2 of the table field
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "04/17/2020"
        And the user selects the "quantityOfOrders" bound numeric field on the main page
        And the user writes "20" in the numeric field
        And the user selects the "greaterThanOrderQuantity" bound switch field on the main page
        When the user clicks in the switch field
        And the user selects the "quantityOfProducts" bound numeric field on the main page
        And the user clears the numeric field
        And the user clicks the "applyFilter" bound button on the main page
        And the user selects the "field" bound table field on the main page
        And the user selects the row 1 of the table field
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "09/08/2019"

    Scenario: As a user I want to filter the table with using an _atLeast quantifier and change it to an _atMost quantifier
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseCustomerInvoiceTable/eyJfaWQiOiIxMjgifQ=="
        And the user selects the "shouldFilter" bound switch field on the main page
        When the user clicks in the switch field
        And the user selects the "numberOfCases" bound numeric field on the main page
        And the user writes "2" in the numeric field
        And the user selects the "quantityOfProducts" bound numeric field on the main page
        And the user writes "11" in the numeric field
        And the user selects the "greaterThanProductQuantity" bound switch field on the main page
        When the user clicks in the switch field
        And the user clicks the "applyFilter" bound button on the main page
        And the user selects the "field" bound table field on the main page
        And the user selects the row 1 of the table field
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "09/08/2019"
        And the user selects the row 2 of the table field
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "04/17/2020"
        And the user selects the "operatorType" bound select field on the main page
        And the user writes "at most" in the select field
        # And the user selects "at most" in the select field
        And the user selects the "numberOfCases" bound numeric field on the main page
        And the user writes "1" in the numeric field
        And the user selects the "quantityOfProducts" bound numeric field on the main page
        And the user clears the numeric field
        And the user selects the "quantityOfOrders" bound numeric field on the main page
        And the user writes "21" in the numeric field
        And the user selects the "greaterThanOrderQuantity" bound switch field on the main page
        When the user clicks in the switch field
        And the user clicks the "applyFilter" bound button on the main page
        And the user selects the "field" bound table field on the main page
        And the user selects the row 1 of the table field
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "02/22/2020"
        And the user selects the row 2 of the table field
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "04/17/2020"
        And the user selects the row 3 of the table field
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "04/22/2020"

    Scenario: As a user I want to filter the table with using an _atLeast quantifier and then remove the filter
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseCustomerInvoiceTable/eyJfaWQiOiIxMjgifQ=="
        And the user selects the "shouldFilter" bound switch field on the main page
        When the user clicks in the switch field
        And the user selects the "numberOfCases" bound numeric field on the main page
        And the user writes "2" in the numeric field
        And the user selects the "quantityOfProducts" bound numeric field on the main page
        And the user writes "11" in the numeric field
        And the user selects the "greaterThanProductQuantity" bound switch field on the main page
        When the user clicks in the switch field
        And the user clicks the "applyFilter" bound button on the main page
        And the user selects the "field" bound table field on the main page
        And the user selects the row 1 of the table field
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "09/08/2019"
        And the user selects the row 2 of the table field
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "04/17/2020"
        And the user selects the "shouldFilter" bound switch field on the main page
        When the user clicks in the switch field
        And the user selects the "field" bound table field on the main page
        And the user selects the row 1 of the table field
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "09/08/2019"
        And the user selects the row 2 of the table field
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "02/22/2020"
        And the user selects the row 3 of the table field
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "04/17/2020"
        And the user selects the row 4 of the table field
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "04/22/2020"
