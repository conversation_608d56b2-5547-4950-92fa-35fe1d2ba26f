Feature: 3-2 Stored values
    # Tests the capability to select fields, input or select values and control the values using stored values (values stored into a variable defined by the user).

    ##-----------------------------------------Stored values and standalone fields-----------------------------------------------------

    Scenario: 1 - As an ATP XTreeM user I can write into a code editor field and control the value using stored value
        XT-35081
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-show-case/PluginMonaco/eyJfaWQiOiIxMDYifQ=="

        And the user selects the "notes" bound code editor field on the main page
        And the user stores the value of the code editor field with the key "[ENV_REFFIELD01]"
        # And takes a screenshot
        And the user clears the code editor field

        And the user writes "Sample text: [ENV_REFFIELD01]" in the code editor field
        Then the value of the code editor field is "Sample text: [ENV_REFFIELD01]"
    # And takes a screenshot

    @isHidden
    Scenario: 2 - As an ATP XTreeM user I can select into a dropdown-list field and control the value using stored value
        XT-35081
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-show-case/DropdownList"
        Then the "Field - Dropdown List" titled page is displayed

        And the user selects the "Dropdown List Field (Options)" labelled dropdown-list field on the main page
        And the user clicks in the dropdown-list field
        And the user selects "Two" in the dropdown-list field

        And the user stores the value of the dropdown-list field with the key "[ENV_REFFIELD02A]"
        # And takes a screenshot

        And the user selects the "Dropdown List Field (Options)" labelled dropdown-list field on the main page
        And the user clicks in the dropdown-list field
        And the user selects "One" in the dropdown-list field

        And the user selects the "Dropdown List Field (Options)" labelled dropdown-list field on the main page
        And the user clicks in the dropdown-list field
        And the user selects "[ENV_REFFIELD02A]" in the dropdown-list field
        Then the value of the dropdown-list field is "[ENV_REFFIELD02A]"
        # And takes a screenshot

        #Hide the dropdown-list field
        And the user selects the "Is hidden" labelled checkbox field on the main page
        And the user clicks in the checkbox field

        And the user selects the "field1" bound dropdown-list field on the main page
        And the user stores the value of the dropdown-list field with the key "[ENV_REFFIELD02B]"
        Then the value of the dropdown-list field is "[ENV_REFFIELD02B]"
    # And takes a screenshot

    @isHidden
    Scenario: 3 - As an ATP XTreeM user I can write and select into a filter select field and control the value using stored value
        XT-35081
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-show-case/FilterSelect"
        Then the "Field - Filter Select" titled page is displayed

        And the user selects the "Filter Select Field" labelled filter select field on the main page
        When the user writes "Veal Inside - Provimi" in the filter select field
        And the user selects "Veal Inside - Provimi" in the filter select field
        And the user stores the value of the filter select field with the key "[ENV_REFFIELD03A]"
        # And takes a screenshot
        And the user clears the filter select field

        When the user writes "[ENV_REFFIELD03A]" in the filter select field
        And the user selects "[ENV_REFFIELD03A]" in the filter select field
        Then the value of the filter select field is "[ENV_REFFIELD03A]"
        # And takes a screenshot

        #Hide the filter-select field
        And the user selects the "Is hidden" labelled checkbox field on the main page
        And the user clicks in the checkbox field

        And the user selects the "field" bound filter select field on the main page
        And the user stores the value of the filter select field with the key "[ENV_REFFIELD03B]"
        Then the value of the filter select field is "[ENV_REFFIELD03B]"
    # And takes a screenshot


    Scenario: 4 - As an ATP XTreeM user I can write into a graphiql editor field and control the value using stored value
        XT-35081
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-show-case/PluginGraphiql/eyJfaWQiOiIxMDYifQ=="
        Then the "Field - GraphiQL Plugin" titled page is displayed

        And the user selects the "queryText" bound graphiql editor field on the main page
        And the user writes "--This is a Query Text----" in the graphiql editor field
        And the user stores the value of the graphiql editor field with the key "[ENV_REFFIELD04]"
        # And takes a screenshot
        And the user clears the graphiql editor field

        And the user writes "Sample text: [ENV_REFFIELD04]" in the graphiql editor field
        Then the value of the graphiql editor field is "Sample text: [ENV_REFFIELD04]"
    # And takes a screenshot

    @isHidden
    Scenario: 5 - As an ATP XTreeM user I can write and select into a multi dropdown field and control the value using stored value
        XT-35081
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-show-case/MultiDropdown"
        Then the "Field - Multi-dropdown List" titled page is displayed

        Given the user selects the "field1" bound multi dropdown field on the main page
        When the user clicks in the multi dropdown field
        And the user selects "Five" in the multi dropdown field
        And the user stores the value of the multi dropdown field with the key "[ENV_REFFIELD05A]"
        # And takes a screenshot
        And the user clears the multi dropdown field

        When the user clicks in the multi dropdown field
        And the user selects "Four" in the multi dropdown field
        And the user stores the value of the multi dropdown field with the key "[ENV_REFFIELD05B]"
        # And takes a screenshot
        And the user clears the multi dropdown field

        When the user clicks in the multi dropdown field
        And the user selects "[ENV_REFFIELD05A] | [ENV_REFFIELD05B]" in the multi dropdown field
        Then the value of the multi dropdown field is "[ENV_REFFIELD05A], [ENV_REFFIELD05B]"
        # And takes a screenshot

        #Hide the multi dropdown field
        And the user selects the "Is hidden" labelled checkbox field on the main page
        And the user clicks in the checkbox field

        And the user selects the "field1" bound multi dropdown field on the main page
        And the user stores the value of the multi dropdown field with the key "[ENV_REFFIELD05C]"
        Then the value of the multi dropdown field is "[ENV_REFFIELD05C]"
    # And takes a screenshot

    @isHidden
    Scenario: 6 - As an ATP XTreeM user I can write and select into a multi reference field and control the value using stored value
        XT-35081
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-show-case/MultiReference"
        Then the "Field - Multi-reference" titled page is displayed

        Given the user selects the "field" bound multi reference field on the main page
        When the user writes "Wine -" in the multi reference field
        And the user selects "Wine - Redchard Merritt" in the multi reference field
        And the user stores the value of the multi reference field with the key "[ENV_REFFIELD06A]"
        # And takes a screenshot
        And the user clears the multi reference field

        When the user writes "Wine -" in the multi reference field
        And the user selects "Wine - Sherry Dry Sack, William" in the multi reference field
        And the user stores the value of the multi reference field with the key "[ENV_REFFIELD06B]"
        # And takes a screenshot
        And the user clears the multi reference field

        When the user writes "Wine -" in the multi reference field
        And the user selects "[ENV_REFFIELD06A]|[ENV_REFFIELD06B]" in the multi reference field
        Then the value of the multi reference field is "[ENV_REFFIELD06A]|[ENV_REFFIELD06B]"
        # And takes a screenshot

        #Hide the multi reference field
        And the user selects the "Is hidden" labelled checkbox field on the main page
        And the user clicks in the checkbox field

        And the user selects the "field" bound multi reference field on the main page
        And the user stores the value of the multi reference field with the key "[ENV_REFFIELD06C]"
        Then the value of the multi reference field is "[ENV_REFFIELD06C]"
    # And takes a screenshot

    @isHidden
    Scenario: 7 - As an ATP XTreeM user I can write into a numeric field and control the value using stored value
        XT-35081
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-show-case/Numeric"
        Then the "Numeric" titled page is displayed

        And  the user selects the "field" bound numeric field on the main page
        And the user writes "59.99" in the numeric field
        And the user stores the value of the numeric field with the key "[ENV_REFFIELD07A]"
        # And takes a screenshot
        And the user clears the numeric field

        And the user writes "[ENV_REFFIELD07A]" in the numeric field
        Then the value of the numeric field is "[ENV_REFFIELD07A]"
        # And takes a screenshot

        #Hide the numeric field
        And the user selects the "Is hidden" labelled checkbox field on the main page
        And the user clicks in the checkbox field

        And the user selects the "field" bound numeric field on the main page
        And the user stores the value of the numeric field with the key "[ENV_REFFIELD07B]"
        Then the value of the numeric field is "[ENV_REFFIELD07B]"
    # And takes a screenshot

    @isHidden
    Scenario: 8 - As an ATP XTreeM user I can write and select into a reference field and control the value using stored value
        XT-35081
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-show-case/Reference"
        Then the "Field - Reference" titled page is displayed

        And the user selects the "field" bound reference field on the main page
        When the user writes "Wine - Crozes Hermitage E." in the reference field
        And the user selects "Wine - Crozes Hermitage E." in the reference field
        And the user stores the value of the reference field with the key "[ENV_REFFIELD08A]"
        # And takes a screenshot
        And the user clears the reference field

        When the user writes "[ENV_REFFIELD08A]" in the reference field
        And the user selects "[ENV_REFFIELD08A]" in the reference field
        Then the value of the reference field is "[ENV_REFFIELD08A]"
        # And takes a screenshot

        #Hide the reference field
        And the user selects the "Is hidden" labelled checkbox field on the main page
        And the user clicks in the checkbox field

        And the user selects the "field" bound reference field on the main page
        And the user stores the value of the reference field with the key "[ENV_REFFIELD08B]"
        Then the value of the reference field is "[ENV_REFFIELD08B]"
    # And takes a screenshot

    @isHidden
    Scenario: 9 - As an ATP XTreeM user I can write into a rich text field and control the value using stored value
        XT-35081
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-show-case/RichText"
        Then the "Field - Rich text" titled page is displayed

        And the user selects the "field" bound rich text field on the main page
        And the user clears the rich text field
        And the user writes "I enter a value" in the rich text field
        And the user stores the value of the rich text field with the key "[ENV_REFFIELD09A]"
        # And takes a screenshot
        And the user clears the rich text field

        And the user writes "Sample text: [ENV_REFFIELD09A]" in the rich text field
        Then the value of the rich text field is "Sample text: [ENV_REFFIELD09A]"
        # And takes a screenshot

        #Hide the rich text field
        And the user selects the "Is hidden" labelled checkbox field on the main page
        And the user clicks in the checkbox field

        And the user selects the "field" bound rich text field on the main page
        And the user stores the value of the rich text field with the key "[ENV_REFFIELD09B]"
        Then the value of the rich text field is "[ENV_REFFIELD09B]"
    # And takes a screenshot

    @isHidden
    Scenario: 10 - As an ATP XTreeM user I can write and select into a select field and control the value using stored value
        XT-35081
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-show-case/Select"
        Then the "Field - Select" titled page is displayed

        And the user selects the "field" bound select field on the main page
        When the user writes "Good" in the select field
        And the user selects "Good" in the select field
        And the user stores the value of the select field with the key "[ENV_REFFIELD10A]"
        # And takes a screenshot
        And the user clears the select field

        When the user writes "[ENV_REFFIELD10A]" in the select field
        And the user selects "[ENV_REFFIELD10A]" in the select field
        Then the value of the select field is "[ENV_REFFIELD10A]"
        # And takes a screenshot

        #Hide the select field
        And the user selects the "Is hidden" labelled checkbox field on the main page
        And the user clicks in the checkbox field

        And the user selects the "field" bound select field on the main page
        And the user stores the value of the select field with the key "[ENV_REFFIELD10B]"
        Then the value of the select field is "[ENV_REFFIELD10B]"
    # And takes a screenshot

    @isHidden
    Scenario: 11 - As an ATP XTreeM user I can write into a text field and control the value using stored value
        XT-35081
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-show-case/Text"
        Then the "Text" titled page is displayed

        And the user selects the "field" bound text field on the main page
        And the user writes "I enter a value" in the text field
        And the user stores the value of the text field with the key "[ENV_REFFIELD11A]"
        # And takes a screenshot
        And the user clears the text field

        And the user selects the "field" bound text field on the main page
        And the user writes "I enter another value" in the text field
        And the user stores the value of the text field with the key "[ENV_REFFIELD11B]"
        # And takes a screenshot
        And the user clears the text field

        And the user writes "Sample text: [ENV_REFFIELD11A] and [ENV_REFFIELD11B]" in the text field
        Then the value of the text field is "Sample text: [ENV_REFFIELD11A] and [ENV_REFFIELD11B]"
        # And takes a screenshot

        #Hide the text field
        And the user selects the "Is hidden" labelled checkbox field on the main page
        And the user clicks in the checkbox field

        And the user selects the "field" bound text field on the main page
        And the user stores the value of the text field with the key "[ENV_REFFIELD11C]"
        Then the value of the text field is "[ENV_REFFIELD11C]"
    # And takes a screenshot

    @isHidden
    Scenario: 12 - As an ATP XTreeM user I can write into a text area field and control the value using stored value
        XT-35081
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-show-case/TextArea"
        Then the "Text area" titled page is displayed

        And the user selects the "field" bound text area field on the main page
        And the user writes "I enter a value" in the text area field
        And the user stores the value of the text area field with the key "[ENV_REFFIELD12A]"
        # And takes a screenshot

        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-show-case/TextArea"
        Then the "Text area" titled page is displayed

        And the user writes "Sample text: [ENV_REFFIELD12A]" in the text area field
        Then the value of the text area field is "Sample text: [ENV_REFFIELD12A]"
        # And takes a screenshot

        #Hide the text area field
        And the user selects the "Is hidden" labelled checkbox field on the main page
        And the user clicks in the checkbox field

        And the user selects the "field" bound text area field on the main page
        And the user stores the value of the text area field with the key "[ENV_REFFIELD12B]"
        Then the value of the text area field is "[ENV_REFFIELD12B]"
    # And takes a screenshot

    @isHidden
    Scenario: 13 - As an ATP XTreeM user I can control the value stored of a label field
        XT-35081
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-show-case/Label"
        Then the "Field - Label" titled page is displayed

        And the user selects the "value" bound text field on the main page
        And the user writes "label value" in the text field
        And the user blurs the text field

        And the user selects the "field" bound label field on the main page
        And the user stores the value of the label field with the key "[ENV_REFFIELD13A]"
        Then the value of the label field is "[ENV_REFFIELD13A]"
        # And takes a screenshot

        #Hide the label field
        And the user selects the "Is hidden" labelled checkbox field on the main page
        And the user clicks in the checkbox field

        And the user selects the "field" bound label field on the main page
        And the user stores the value of the label field with the key "[ENV_REFFIELD13B]"
        Then the value of the label field is "[ENV_REFFIELD13B]"
    # And takes a screenshot


    ##-----------------------------------------Stored values and Navigation panel--------------------------------------------------

    Scenario: 1 - As an ATP XTreeM user I select a record in the navigation panel using stored value
        XT-35081
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-show-case/NavigationPanel/eyJfaWQiOiI3MyJ9"
        Then the "Navigation Panel" titled page is displayed

        And the user selects the "Id" labelled text field on the main page
        And the user stores the value of the text field with the key "[ENV_PRODUCT01]"
        # And takes a screenshot

        And the user selects the "Product" labelled text field on the main page
        And the user stores the value of the text field with the key "[ENV_PRODUCT02]"
        # And takes a screenshot

        #First method: selecting the row by rowid
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-show-case/NavigationPanel/"
        Then the "Navigation Panel" titled page is displayed

        And the user searches for "[ENV_PRODUCT01]" in the navigation panel
        Then the search field value in the navigation panel is "[ENV_PRODUCT01]"
        And the user clicks the "first" navigation panel's row
        # And takes a screenshot

        And the user selects the "Product" labelled text field on the main page
        Then the value of the text field is "[ENV_PRODUCT02]"

        #Second method: selecting the row with stored value
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-show-case/NavigationPanel/"
        Then the "Navigation Panel" titled page is displayed

        And the user searches for "[ENV_PRODUCT01]" in the navigation panel
        Then the search field value in the navigation panel is "[ENV_PRODUCT01]"
        When the user clicks the record with the text "[ENV_PRODUCT02]" in the navigation panel
        # And takes a screenshot

        And the user selects the "Product" labelled text field on the main page
        Then the value of the text field is "[ENV_PRODUCT02]"
    # And takes a screenshot

    ##-----------------------------------------Stored values and Nested grid----------------------------------------------------------------

    Scenario: 1 - As an ATP XTreeM user I can select a nested grid row using stored value
        XT-36431
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-show-case/Text"
        Then the "Text" titled page is displayed

        And the user selects the "field" bound text field on the main page
        And the user writes "71" in the text field
        And the user stores the value of the text field with the key "[ENV_ID01]"
        # And takes a screenshot

        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-show-case/NestedGrid/eyJfaWQiOiI2In0="
        Then the "Field - NestedGrid" titled page is displayed
        And the user selects the "field" bound nested grid field on the main page
        And the user selects row with text "127" in column with header "Id" in the nested grid field
        And the user expands the selected row of the nested grid field
        And the user selects row with text "3[ENV_ID01]" in column with header "Id" in the nested grid field
        When the user writes "500" in the "Total Product Quantity" labelled nested numeric field of the selected row in the nested grid field
        Then the value of the "Total Product Quantity" labelled nested numeric field of the selected row in the nested grid field is "500"
        # And takes a screenshot
        And the user ticks the main checkbox of the selected row in the nested grid field
        And the user expands the selected row of the nested grid field
        # And takes a screenshot
        And the user unticks the main checkbox of the selected row in the nested grid field
        And the user collapses the selected row of the nested grid field


    Scenario: 2 - As an ATP XTreeM user I can verify and store value in nested grid nested field
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-show-case/NestedGrid/eyJfaWQiOiI2In0="
        Then the "Field - NestedGrid" titled page is displayed
        And the user selects the "field" bound nested grid field on the main page
        And the user selects row with text "127" in column with header "Id" in the nested grid field
        And the user expands the selected row of the nested grid field
        And the user selects row with text "371" in column with header "Id" in the nested grid field
        And the user expands the selected row of the nested grid field
        And the user selects row with text "853" in column with header "Id" in the nested grid field
        Then the user stores the value of the "Quantity" labelled nested numeric field of the selected row in the nested grid field with key "[ENV_QTY_01]"
        Then the value of the "Quantity" labelled nested numeric field of the selected row in the nested grid field is "[ENV_QTY_01]"
        Then the user stores the value of the "netPrice" bound nested numeric field of the selected row in the nested grid field with key "[ENV_NET_PRICE_01]"
        Then the value of the "netPrice" bound nested numeric field of the selected row in the nested grid field is "[ENV_NET_PRICE_01]"
        # And takes a screenshot

        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-show-case/Text"
        Then the "Text" titled page is displayed

        And the user selects the "field" bound text field on the main page
        And the user writes "[ENV_QTY_01]" in the text field
        Then the value of the text field is "[ENV_QTY_01]"
        # And takes a screenshot
        And the user writes "[ENV_NET_PRICE_01]" in the text field
        Then the value of the text field is "[ENV_NET_PRICE_01]"
    # And takes a screenshot

    Scenario: 3 - As an ATP XTreeM user I write value in nested grid nested field using stored value

        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-show-case/Text"
        Then the "Text" titled page is displayed

        And the user selects the "field" bound text field on the main page
        And the user writes "45" in the text field
        And the user stores the value of the text field with the key "[ENV_QTY_02]"

        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-show-case/NestedGrid/eyJfaWQiOiI2In0="
        Then the "Field - NestedGrid" titled page is displayed
        And the user selects the "field" bound nested grid field on the main page
        And the user selects row with text "127" in column with header "Id" in the nested grid field
        And the user expands the selected row of the nested grid field
        And the user selects row with text "371" in column with header "Id" in the nested grid field
        And the user expands the selected row of the nested grid field
        And the user selects row with text "853" in column with header "Id" in the nested grid field
        And the user writes "[ENV_QTY_02]" in the "Quantity" labelled nested numeric field of the selected row in the nested grid field
        Then the value of the "Quantity" labelled nested numeric field of the selected row in the nested grid field is "[ENV_QTY_02]"
    # And takes a screenshot

    ##-----------------------------------------Stored values and Nested grid filter----------------------------------------------------------------
    @simple_filter
    Scenario: 1 - As an ATP / XTreeM user I can filter a nested grid column using both value and filter type using stored value
        XT-86987

        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-show-case/Text"
        Then the "Text" titled page is displayed

        And the user selects the "field" bound text field on the main page
        And the user writes "127" in the text field
        And the user stores the value of the text field with the key "[ENV_ID01]"

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/NestedGrid/eyJfaWQiOiI2In0="
        Then the "Field - NestedGrid" titled page is displayed
        #filter the main level
        When the user selects the "field" bound nested grid field on the main page
        And the user filters the "Id" labelled column in the nested grid field with filter "Equal to" and value "[ENV_ID01]"
        And the user selects row with text "[ENV_ID01]" in column with header "Id" in the nested grid field
        Then the value of the "Id" labelled nested text field of the selected row in the nested grid field is "[ENV_ID01]"
    # And takes a screenshot



    @mini_filter
    Scenario: 2 - As an ATP / XTreeM user I can filter nested grid mini filter or custom reference filter using stored value
        XT-86987
        #using advanced filter step definitions
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-show-case/Text"
        Then the "Text" titled page is displayed

        And the user selects the "field" bound text field on the main page
        And the user writes "In store" in the text field
        And the user stores the value of the text field with the key "[ENV_ORDER_TYPE]"

        And the user writes "Pasta - Lasagne, Fresh" in the text field
        And the user stores the value of the text field with the key "[ENV_PRODUCT]"

        #Using advanced filter step definitions
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/NestedGrid/eyJfaWQiOiI2In0="
        When the user selects the "field" bound nested grid field on the main page

        And the user selects row with text "127" in column with header "Id" in the nested grid field
        And the user writes "[ENV_ORDER_TYPE]" in the "Order Type" labelled nested dropdown-list field of the selected row in the nested grid field

        #filter the main level / mini filter
        And the user opens the filter of the "Order Type" labelled column in the nested grid field
        And the user searches "[ENV_ORDER_TYPE]" in the filter of the nested grid field
        And the user ticks the item with text "[ENV_ORDER_TYPE]" in the filter of the nested grid field
        And the user closes the filter of the "Order Type" labelled column in the nested grid field
        Then the value of the "Order Type" labelled nested dropdown-list field of the selected row in the nested grid field is "[ENV_ORDER_TYPE]"
        # And takes a screenshot

        And the user selects row with text "127" in column with header "Id" in the nested grid field
        And the user expands the selected row of the nested grid field

        #filter the sub level / custom reference filter
        And the user selects row with text "371" in column with header "Id" in the nested grid field
        And the user expands the selected row of the nested grid field

        And the user opens the filter of the "Product" labelled column in the nested grid field
        And the user searches "[ENV_PRODUCT]" in the filter of the nested grid field
        And the user ticks the item with text "[ENV_PRODUCT]" in the filter of the nested grid field
        And the user closes the filter of the "Product" labelled column in the nested grid field
        And the user selects row with text "853" in column with header "Id" in the nested grid field
        Then the value of the "Product" labelled nested reference field of the selected row in the nested grid field is "[ENV_PRODUCT]"
    # And takes a screenshot


    ##-----------------------------------------Stored values and card list--------------------------------------------------------------

    Scenario: 1 - As an ATP XTreeM user I can select a table card list using stored values and select or unselect the main checkbox
        XT-36425
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-show-case/Text"
        Then the "Text" titled page is displayed

        And the user selects the "field" bound text field on the main page
        And the user writes "06.99" in the text field
        And the user stores the value of the text field with the key "[ENV_PRICE01]"
        # And takes a screenshot

        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-show-case/TableCardView/eyJfaWQiOiIyIn0="
        Then the "Field - Table card view" titled page is displayed

        And the user selects the "field" bound table field on the main page
        And the user ticks the main checkbox of the card with the text "55.19" in the table field
        # And takes a screenshot
        And the user unticks the main checkbox of the card with the text "55.19" in the table field
        # And takes a screenshot

        And the user ticks the main checkbox of the card with the text "1[ENV_PRICE01]" in the table field
        # And takes a screenshot
        And the user unticks the main checkbox of the card with the text "1[ENV_PRICE01]" in the table field
    # And takes a screenshot

    Scenario: 2 - As an ATP XTreeM user I can store and verify the value in a table card list using a stored value
        XT-89467
        Given the user opens the application on a mobile using the following link: "@sage/xtrem-show-case/TableCardView/eyJfaWQiOiIyIn0="
        Then the "Field - Table card view" titled page is displayed

        And the user selects the "field" bound table field on the main page
        And the user stores the value of the "amount" bound nested label field of the card 2 in the table field with the key "[ENV_AMOUNT]"
        Then the value of the "amount" bound nested label field of the card 2 in the table field is "[ENV_AMOUNT]"
        Then the value of the "amount" bound nested label field of the card 2 in the table field is "$ 55.19"
        And the user stores the value of the "category" bound nested select field of the card 2 in the table field with the key "[ENV_CATEGORY]"
        Then the value of the "category" bound nested select field of the card 2 in the table field is "[ENV_CATEGORY]"
        Then the value of the "category" bound nested select field of the card 2 in the table field is "Good"
        And the user stores the value of the "releaseDate" bound nested date field of the card 2 in the table field with the key "[ENV_R_DATE]"
        Then the value of the "releaseDate" bound nested date field of the card 2 in the table field is "[ENV_R_DATE]"
        Then the value of the "releaseDate" bound nested date field of the card 2 in the table field is "05/30/2020"
        And the user stores the value of the "provider" bound nested reference field of the card 2 in the table field with the key "[ENV_PROVIDER]"
        Then the value of the "provider" bound nested reference field of the card 2 in the table field is "[ENV_PROVIDER]"
        Then the value of the "provider" bound nested reference field of the card 2 in the table field is "Amazon"
    # And takes a screenshot

    Scenario: 3 - Mobile - As an ATP XTreeM user I can verify the value of the card header using a stored value
        XT-89467

        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-show-case/Text"
        Then the "Text" titled page is displayed

        And the user selects the "field" bound text field on the main page
        And the user writes "Extended" in the text field
        And the user stores the value of the text field with the key "[ENV_DESCRIPTION]"


        Given the user opens the application on a mobile using the following link: "@sage/xtrem-show-case/ShowCaseProductHeaderCard/eyJfaWQiOiIyMTAifQ=="
        Then the value of the "description" bound nested text field in the header card is "[ENV_DESCRIPTION]"
        Then the value of the "description" bound nested text field in the header card is "Extended"
    # And takes a screenshot

    Scenario: 4 - Mobile - As a user I can store and verify the value of the nested grid card fields using a stored value
        XT-89467

        Given the user opens the application on a mobile using the following link: "@sage/xtrem-show-case/NestedGrid/eyJfaWQiOiI2In0="
        And the user selects the "field" bound nested grid field on the main page
        And the user stores the value of the "_id" bound nested text field of the card 1 in the nested grid field with the key "[ENV_ID]"
        Then the value of the "_id" bound nested text field of the card 1 in the nested grid field is "[ENV_ID]"
        Then the value of the "_id" bound nested text field of the card 1 in the nested grid field is "127"
        And the user clicks the card 1 in the nested grid field
        Then the value in the header of the mobile nested grid field is "[ENV_ID]"
        And the user stores the value of the "customer" bound nested reference field of the card 2 in the nested grid field with the key "[ENV_CUSTOMER]"
        Then the value of the "customer" bound nested reference field of the card 2 in the nested grid field is "[ENV_CUSTOMER]"
        Then the value of the "customer" bound nested reference field of the card 2 in the nested grid field is "Anderson and Sons"
        And the user stores the value of the "totalProductQty" bound nested numeric field of the card 2 in the nested grid field with the key "[ENV_QTY]"
        Then the value of the "totalProductQty" bound nested numeric field of the card 2 in the nested grid field is "[ENV_QTY]"
        Then the value of the "totalProductQty" bound nested numeric field of the card 2 in the nested grid field is "97"
        And the user stores the value of the "_id" bound nested text field of the card 2 in the nested grid field with the key "[ENV_ID]"
        Then the value of the "_id" bound nested text field of the card 2 in the nested grid field is "[ENV_ID]"
        Then the value of the "_id" bound nested text field of the card 2 in the nested grid field is "371"
        And the user stores the value of the "purchaseDate" bound nested date field of the card 2 in the nested grid field with the key "[ENV_PUR_DATE]"
        Then the value of the "purchaseDate" bound nested date field of the card 2 in the nested grid field is "[ENV_PUR_DATE]"
        Then the value of the "purchaseDate" bound nested date field of the card 2 in the nested grid field is "04/23/2020"


    # And takes a screenshot

    ##-----------------------------------------Stored values and data table filter column--------------------------------------------------------------------
    Scenario: 1 - As an ATP XTreeM user I can filter the table column header using stored value
        XT-36434
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-show-case/Text"
        Then the "Text" titled page is displayed

        And the user selects the "field" bound text field on the main page
        And the user writes "Appetizer - Cheese" in the text field
        And the user stores the value of the text field with the key "[ENV_PRODUCT01]"
        # And takes a screenshot
        And the user clears the text field


        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-show-case/Table"
        Then the "Field - Table" titled page is displayed

        And the user selects the "field" bound table field on the main page
        When the user filters the "product" bound column in the table field with value "Apricots Fresh"
        Then the user selects the row 1 of the table field
        And the value of the "product" bound nested text field of the selected row in the table field is "Apricots Fresh"
        # And takes a screenshot

        When the user filters the "product" bound column in the table field with value "[ENV_PRODUCT01] Bites"
        And the user selects the "field" bound table field on the main page
        Then the user selects the row 1 of the table field
        And the value of the "product" bound nested text field of the selected row in the table field is "[ENV_PRODUCT01] Bites"
    # And takes a screenshot


    Scenario: 2 - As an ATP XTreeM user I can filter a value in the data table custom reference using stored value
        XT-49267
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-show-case/Text"
        Then the "Text" titled page is displayed

        And the user selects the "field" bound text field on the main page
        And the user writes "Ali Express" in the text field
        And the user stores the value of the text field with the key "[ENV_PROVIDER01]"
        # And takes a screenshot
        And the user clears the text field

        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-show-case/TableNestedReference/eyJfaWQiOiIxIn0="
        And the user selects the "field" bound table field on the main page
        When the user opens the filter of the "Provider" labelled column in the table field
        And the user searches "Ali" in the filter of the table field
        And the user ticks the item with text "[ENV_PROVIDER01]" in the filter of the table field
        And the user unticks the item with text "[ENV_PROVIDER01]" in the filter of the table field

    ##-----------------------------------------Stored values and data table nested fields-------------------------------------
    Scenario: 1 - As an ATP XTreeM user I can interact with data table nested field using stored value

        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-show-case/Text"
        Then the "Text" titled page is displayed

        And the user selects the "field" bound text field on the main page
        And the user writes "Decathlon" in the text field
        And the user stores the value of the text field with the key "[ENV_PROVIDER01]"
        # And takes a screenshot
        And the user clears the text field

        And the user writes "99.54" in the text field
        And the user stores the value of the text field with the key "[ENV_PRICE01]"
        # And takes a screenshot
        And the user clears the text field


        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-show-case/Table"
        Then the "Field - Table" titled page is displayed

        And the user selects the "field" bound table field on the main page

        #Store the value of the nested cell using rowid
        And the user selects the row 4 of the table field
        And the user stores the value of the "Id" labelled nested text field of the selected row in the table field with the key "[ENV_ID01]"
        Then the value of the "Id" labelled nested numeric field of the selected row in the table field is "[ENV_ID01]"
        # And takes a screenshot

        #Write stored value using dynamic row
        When the user selects the row with text "[ENV_ID01]" in the "Id" labelled column header of the table field
        And the user writes "[ENV_PRICE01]" in the "List Price" labelled nested numeric field of the selected row in the table field
        And the user presses Tab
        Then the value of the "List Price" labelled nested numeric field of the selected row in the table field is "[ENV_PRICE01]"
        # And takes a screenshot

        #Write / select stored value using dynamic row
        And the user writes "Deca" in the "Provider" labelled nested reference field of the selected row in the table field
        And the user selects "[ENV_PROVIDER01]" in the "Provider" labelled nested field of the selected row in the table field
        Then the value of the "Provider" labelled nested numeric field of the selected row in the table field is "[ENV_PROVIDER01]"
        # And takes a screenshot

        #Store the value of the nested cell using dynamic row
        And the user stores the value of the "Provider" labelled nested text field of the selected row in the table field with the key "[ENV_PROVIDER02]"
        # And takes a screenshot
        And the user waits 3 seconds

        #Write stored value using rowid
        And the user selects the row 5 of the table field
        And the user writes "[ENV_PRICE01]" in the "List Price" labelled nested numeric field of the selected row in the table field
        And the user presses Tab
        Then the value of the "List Price" labelled nested numeric field of the selected row in the table field is "[ENV_PRICE01]"
        # And takes a screenshot

        #Write /select stored value using rowid
        And the user writes "Deca" in the "Provider" labelled nested reference field of the selected row in the table field
        And the user selects "[ENV_PROVIDER02]" in the "Provider" labelled nested field of the selected row in the table field
        Then the value of the "Provider" labelled nested numeric field of the selected row in the table field is "[ENV_PROVIDER02]"
    # And takes a screenshot

    ##-----------------------------------------Stored values and summary data table nested fields-------------------------------------

    Scenario: 1 - As an ATP XTreeM user I can interact with summary data table nested field using stored value

        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-show-case/Text"
        Then the "Text" titled page is displayed

        And the user selects the "field" bound text field on the main page
        And the user writes "Apple - Macintosh" in the text field
        And the user stores the value of the text field with the key "[ENV_PRODUCT01]"
        # And takes a screenshot
        And the user clears the text field

        And the user writes "21" in the text field
        And the user stores the value of the text field with the key "[ENV_QTY01]"
        # And takes a screenshot
        And the user clears the text field

        #Open summary table functionality
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-show-case/TableSummary/eyJfaWQiOiIyIn0="
        Then the "Field - Table Summary" titled page is displayed
        And the user selects the "Summary Table Title" labelled summary table field on the main page

        #select row dynamically using stored value
        And the user selects the row with text "[ENV_PRODUCT01]" in the "Product" labelled column header of the summary table field

        #Verify value using stored value
        Then the value of the "Quantity" labelled nested numeric field of the selected row in the summary table field is "[ENV_QTY01]"

        #Store the quantity value
        And the user stores the value of the "Quantity" labelled nested numeric field of the selected row in the summary table field with the key "[ENV_QTY02]"

        #Verify value using stored value
        Then the value of the "Quantity" labelled nested numeric field of the selected row in the summary table field is "[ENV_QTY02]"
    # And takes a screenshot


    ##-----------------------------------Stored values and Title page-------------------------------------------
    Scenario: 1 - As an ATP/XTreeM user I can verify the title of the page using a stored value
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-show-case/ShowCaseCustomer/eyJfaWQiOiI0MCJ9"
        Then the "Customer Abshire-Greenfelder" titled page is displayed

        And the user selects the "name" bound text field on the main page
        And the user stores the value of the text field with the key "[ENV_CUSTOMER01]"

        Then the "Customer [ENV_CUSTOMER01]" titled page is displayed
        Then the titled page containing "Customer [ENV_CUSTOMER01]" is displayed
        Then the titled page containing "Customer" is displayed
        Then the titled page containing "[ENV_CUSTOMER01]" is displayed

    Scenario: 2 - As an ATP/XTreeM user I can verify the subtitle of the page using a stored value
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-authorization/SiteGroup/eyJfaWQiOiIxNSJ9"
        Then the "S1" titled page is displayed
        Then the "Site group" subtitled page is displayed

        And the user selects the "name" bound text field on the main page
        And the user writes "Site group" in the text field
        And the user stores the value of the text field with the key "[ENV_SITEGROUP01]"

        Then the "[ENV_SITEGROUP01]" subtitled page is displayed
        Then the subtitled page containing "[ENV_SITEGROUP01]" is displayed
        Then the subtitled page containing "Site group" is displayed
        Then the subtitled page containing "[ENV_SITEGROUP01]" is displayed


    ##-----------------------------------Stored values and Confirmation dialog-------------------------------------------
    Scenario: 1 - As an ATP / XTreeM user I can extract the text from the confirmation dialog into a stored value
        Given the user opens the application on a mobile using the following link: "@sage/xtrem-show-case/Dialogs"
        And the user selects the "confirmationDialogIsFullscreen" bound checkbox field on the main page
        When the user ticks the checkbox field
        When the user clicks in the "confirmationDialogButton" bound button field on the main page
        Then an info dialog appears on a full width modal
        Then the text in the header of the dialog is "Confirmation dialog" on a full width modal
        Then the text in the body of the dialog is "The actual confirmation message" on a full width modal
        And the user extracts the value from the confirmation dialog starting at 11 for 12 characters and stores it in key "[ENV_VARIABLE]"
        And takes a screenshot
        When the user clicks the "Accept" button of the Confirm dialog on a full width modal
        Then an info dialog disappears on a full width modal

        Given the user opens the application on a mobile using the following link: "@sage/xtrem-show-case/Text"
        And the user selects the "field" bound text field on the main page
        And the user writes "[ENV_VARIABLE]" in the text field
        Then the value of the text field is "[ENV_VARIABLE]"
