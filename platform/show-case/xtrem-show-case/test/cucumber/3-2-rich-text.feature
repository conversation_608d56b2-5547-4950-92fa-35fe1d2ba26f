Feature: 3-2 Rich text
   # Tests rich text editing functionality across different devices, verifying text entry, formatting options, and content persistence and control the rich text field state.

   Scenario Outline: <Device> - As an ATP XTreeM User I can vrite and verify a rich text field using label
      XT-25838
      Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/RichText"
      Then the "Field - Rich text" titled page is displayed

      Given the user selects the "Title" labelled text field on the main page
      When the user writes "Sample title" in the text field
      And the user presses Tab

      Given the user selects the "Sample title" labelled rich text field on the main page
      When the user clears the rich text field
      When the user writes "some information ........and more" in the rich text field
      Then the value of the rich text field is "some information ........and more"
      Examples:
         | Device  |
         | desktop |
         | tablet  |
         | mobile  |


   Scenario Outline: <Device> - As an ATP XTreeM User I can vrite and verify a rich text field using bind
      XT-25838
      Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/RichText"
      Then the "Field - Rich text" titled page is displayed

      Given the user selects the "field" bound rich text field on the main page
      When the user clears the rich text field
      And the user writes "some more information ........and even more" in the rich text field
      Then the value of the rich text field is "some more information ........and even more"
      Examples:
         | Device  |
         | desktop |
         | tablet  |
         | mobile  |


   Scenario Outline: <Device> - As an ATP XTreeM User I can scroll to and blur the rich text field
      XT-25838
      Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/RichText"
      Then the "Field - Rich text" titled page is displayed

      Given the user selects the "field" bound rich text field on the main page
      And the user scrolls to the rich text field
      And the user blurs the rich text field
      Examples:
         | Device  |
         | desktop |
         | tablet  |
         | mobile  |

   #rich text field - set / check properties

   Scenario Outline: <Device> - As and ATP XTreeM user I can verify the rich text field title
      XT-25838
      Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/RichText"
      Then the "Field - Rich text" titled page is displayed

      Given the user selects the "Title" labelled text field on the main page
      When the user writes "Sample title" in the text field
      And the user presses Tab

      Given the user selects the "field" bound rich text field on the main page
      When the user clicks in the rich text field
      Then the title of the rich text field is "Sample title"
      Then the title of the rich text field is displayed

      Given the user selects the "Is title hidden" labelled checkbox field on the main page
      When the user ticks the checkbox field

      Given the user selects the "field" bound rich text field on the main page
      Then the title of the rich text field is hidden
      Examples:
         | Device  |
         | desktop |
         | tablet  |
         | mobile  |


   Scenario Outline: <Device> - As and ATP XTreeM user I can verify the rich text field helper text
      XT-25838
      Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/RichText"
      Then the "Field - Rich text" titled page is displayed

      Given the user selects the "Helper text" labelled text field on the main page
      When the user writes "Sample text" in the text field
      And the user presses Tab

      Given the user selects the "field" bound rich text field on the main page
      When the user clicks in the rich text field
      Then the helper text of the rich text field is "Sample text"
      Then the helper text of the rich text field is displayed

      Given the user selects the "Is helper text hidden" labelled checkbox field on the main page
      When the user ticks the checkbox field

      Given the user selects the "field" bound rich text field on the main page
      Then the helper text of the rich text field is hidden
      Examples:
         | Device  |
         | desktop |
         | tablet  |
         | mobile  |


   Scenario Outline: <Device> - As and ATP XTreeM user I can verify if the rich text field is displayed or hidden
      XT-25838
      Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/RichText"
      Then the "Field - Rich text" titled page is displayed

      Given the user selects the "field" bound rich text field on the main page
      When the user clicks in the rich text field
      Then the "field" bound rich text field on the main page is displayed

      Given the user selects the "Is hidden" labelled checkbox field on the main page
      When the user ticks the checkbox field
      Then the "field" bound rich text field on the main page is hidden
      Examples:
         | Device  |
         | desktop |
         | tablet  |
         | mobile  |


   Scenario Outline:<Device> - As and ATP XTreeM user I can verify if the rich text field is enabled or disabled
      XT-25838
      Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/RichText"
      Then the "Field - Rich text" titled page is displayed

      Given the user selects the "field" bound rich text field on the main page
      Then the rich text field is enabled

      Given the user selects the "Is disabled" labelled checkbox field on the main page
      When the user ticks the checkbox field

      Given the user selects the "field" bound rich text field on the main page
      Then the rich text field is disabled
      Examples:
         | Device  |
         | desktop |
         | tablet  |
         | mobile  |


   Scenario Outline: <Device> - As and ATP XTreeM user I can verify if the rich text field is read-only
      XT-25838
      Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/RichText"
      Then the "Field - Rich text" titled page is displayed

      Given the user selects the "Is readOnly" labelled checkbox field on the main page
      When the user ticks the checkbox field

      Given the user selects the "field" bound rich text field on the main page
      Then the rich text field is read-only
      Examples:
         | Device  |
         | desktop |
         | tablet  |
         | mobile  |


   Scenario Outline: As an ATP XTreeM User I can click the <ButtonName> button of the rich text field
      XT-25838
      Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/RichText"
      Then the "Field - Rich text" titled page is displayed

      Given the user selects the "field" bound rich text field on the main page
      When the user clears the rich text field
      And the user writes "some more information ........and even more" in the rich text field
      Then the value of the rich text field is "some more information ........and even more"

      And the user waits 2 seconds
      When the user selects all the content of the rich text field

      When the user clicks in the <ButtonName> button of the rich text field
      And the user waits 2 seconds
      Examples:
         | ButtonName      |
         | "Bold"          |
         | "Italic"        |
         | "Underline"     |
         | "Strikethrough" |


   Scenario: As an ATP XTreeM User I can select the all content of the rich text field
      XT-25838
      Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/RichText"
      Then the "Field - Rich text" titled page is displayed

      Given the user selects the "field" bound rich text field on the main page
      And the user writes "some more information ........and even more" in the rich text field
      And the user selects all the content of the rich text field
      And the user waits 2 seconds

# Scenario: Disable or enable the rich text field
#     Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/RichText"
#     When the user clicks in the "Is disabled" labelled checkbox field on the main page
#     Then the "field" bound rich text field on the main page is disabled
#     And the user clicks in the "Is disabled" labelled checkbox field on the main page
#     Then the "field" bound rich text field on the main page is enabled

# Scenario: Field marked as invalid on invalid value
#     Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/RichText"
#     When the user clicks in the "mandatory" bound rich text field on the main page
#     And blurs the "mandatory" bound rich text field on the main page
#     Then the "mandatory" bound date field on the main page is invalid

# Scenario: Hide or display the rich text field
#     Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/RichText"
#     When the user clicks in the "Is hidden" labelled checkbox field on the main page
#     Then the "field" bound rich text field on the main page is hidden
#     And the user clicks in the "Is hidden" labelled checkbox field on the main page
#     Then the "field" bound rich text field on the main page is displayed

# Scenario: Set the field as read-only
#     Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/RichText"
#     When the user clicks in the "Is readOnly" labelled checkbox field on the main page
#     Then the "field" bound rich text field on the main page is read-only

# Scenario: Set the helper text of the rich text field
#     Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/RichText"
#     When the user writes "Sample text" to the "Helper text" labelled text field on the main page
#     Then the helper text of the "field" bound rich text field on the main page is "Sample text"

# Scenario: Set the title of the rich text field
#     Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/RichText"
#     When the user writes "Sample title" to the "Title" labelled text field on the main page
#     Then the title of the "field" bound rich text field on the main page is "Sample title"

# Scenario: Set the value of the rich text field
#     Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/RichText"
#     When the user writes "Sample text" to the "Value" labelled rich text field on the main page
#     Then the value of the "field" bound rich text field on the main page is "Sample text"
#     And the "field" bound rich text field on the main page is valid

# Scenario: Trigger custom click event handler
#     Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/RichText"
#     Then the "clickTriggered" bound label field on the main page is hidden
#     When the user clicks in the "field" bound rich text field on the main page
#     Then the "clickTriggered" bound label field on the main page is displayed
