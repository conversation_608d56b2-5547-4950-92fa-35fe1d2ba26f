Feature: 4-2 Table sidebar
    # Tests the table sidebar component functionality, verifying opening/closing behavior, data display in the sidebar, and interactions with inline editing features

    <PERSON><PERSON><PERSON>: As a user I want to be able to open and close the table sidebar multiple times
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/StandardShowCaseProvider/eyJfaWQiOiI0In0="
        Then the "Provider 4" titled page is displayed
        When the user selects the "products" bound table field on the main page
        And the user selects the row 1 of the table field
        And the user clicks the "Edit on sidebar" dropdown action of the selected row of the table field
        And the user selects the "Product" labelled text field on the sidebar
        Then the value of the text field is "Rice"
        When the user clicks the Close button of the dialog on the sidebar
        And the user selects the row 1 of the table field
        And the user clicks the "Edit on sidebar" dropdown action of the selected row of the table field
        And the user selects the "Product" labelled text field on the sidebar
        Then the value of the text field is "Rice"

    Scenario: As a user I want the table bound values to render on the sidebar
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TableWithSidebar/eyJfaWQiOiIyIn0="
        Then the "Field - Table - With sidebar" titled page is displayed
        And the user selects the "field" bound table field on the main page
        And the user selects the row 1 of the table field
        When the user clicks the "Edit on sidebar" dropdown action of the selected row of the table field
        And the user selects the "Product" labelled text field on the sidebar
        Then the value of the text field is "Appetiser - Bought"
        And the user selects the "Tax" labelled numeric field on the sidebar
        Then the value of the numeric field is "85.59"
        And the user selects the "Quantity" labelled numeric field on the sidebar
        Then the value of the numeric field is "16"
        And the user selects the "Description" labelled text field on the sidebar
        Then the value of the text field is "frame"
        And the user selects the "Net price" labelled numeric field on the sidebar
        Then the value of the numeric field is "19.16"
        And the user selects the "List price" labelled numeric field on the sidebar
        Then the value of the numeric field is "9.94"
        And the user selects the "Provider" labelled reference field on the sidebar
        Then the value of the reference field is "Amazon"
        Then selects the "some other section" labelled navigation anchor on the sidebar
        And the user selects the "Date" labelled date field on the sidebar
        Then the value of the date field is "07/27/2020"

    Scenario: As a user I want to add new items from the sidebar and then from the phantom row - tab in view mode
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TableWithSidebar/eyJfaWQiOiIyIn0="
        Then the "Field - Table - With sidebar" titled page is displayed
        When the user selects the "field" bound table field on the main page
        And the user adds a new table row to the table field using the sidebar
        And the user selects the "Product" labelled text field on the sidebar
        And the user writes "AAAA Apple" in the text field
        And the user clicks the "Apply" button of the dialog on the sidebar
        And the user selects the "field" bound table field on the main page
        And the user selects the row 1 of the table field
        Then the value of the "product" bound nested text field of the selected row in the table field is "AAAA Apple"
        And the user selects the floating row of the table field
        When the user writes "AAAA Bread" in the "product" bound nested text field of the selected row in the table field
        And the user presses Tab
        And the user presses Tab
        And the user presses Tab
        And the user presses Tab
        And the user presses Tab
        And the user presses Tab
        And the user presses Tab
        And the user presses Tab
        And the user presses Tab
        And the user presses Tab
        And the user presses Tab
        And the user presses Tab
        And the user presses Tab
        Then the element with the following selector is focused: ".ag-cell-not-inline-editing button[aria-label='Cancel']"
        And the user presses Tab
        And the user selects the row 2 of the table field
        Then the value of the "product" bound nested text field of the selected row in the table field is "AAAA Bread"

    Scenario: As a user I want to add new items from the sidebar and then from the phantom row - tab in edit mode
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TableWithSidebar/eyJfaWQiOiIyIn0="
        Then the "Field - Table - With sidebar" titled page is displayed
        When the user selects the "field" bound table field on the main page
        And the user adds a new table row to the table field using the sidebar
        And the user selects the "Product" labelled text field on the sidebar
        And the user writes "AAAA Apple" in the text field
        And the user clicks the "Apply" button of the dialog on the sidebar
        And the user selects the "field" bound table field on the main page
        And the user selects the row 1 of the table field
        Then the value of the "product" bound nested text field of the selected row in the table field is "AAAA Apple"
        And the user selects the floating row of the table field
        When the user writes "AAAA Bread" in the "product" bound nested text field of the selected row in the table field
        And the user presses Enter
        And the user presses Tab
        And the user presses Tab
        And the user presses Tab
        And the user presses Tab
        And the user presses Tab
        And the user presses Tab
        And the user presses Tab
        And the user presses Tab
        And the user presses Tab
        And the user presses Tab
        And the user presses Tab
        And the user presses Tab
        And the user presses Tab
        And the user presses Tab
        And the user presses Tab
        And the user presses Tab
        And the user presses Tab
        And the user presses Tab
        And the user presses Tab
        And the user presses Tab
        Then the element with the following selector is focused: ".ag-popup-editor button[aria-label='Cancel']"
        And the user presses Tab
        And the user selects the row 2 of the table field
        Then the value of the "product" bound nested text field of the selected row in the table field is "AAAA Bread"

    Scenario: As a user I want to make changes on the sidebar but only apply them to the table when I click the Apply button
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TableWithSidebar/eyJfaWQiOiIyIn0="
        Then the "Field - Table - With sidebar" titled page is displayed
        And the user selects the "field" bound table field on the main page
        And the user selects the row 1 of the table field
        When the user clicks the "Edit on sidebar" dropdown action of the selected row of the table field
        And the user selects the "Product" labelled text field on the sidebar
        Then the value of the text field is "Appetiser - Bought"
        And the user selects the "field" bound table field on the main page
        And the user selects the row 1 of the table field
        Then the value of the "product" bound nested text field of the selected row in the table field is "Appetiser - Bought"
        When the user writes "UPDATED" in the text field
        And the user selects the "field" bound table field on the main page
        And the user selects the row 1 of the table field
        Then the value of the "product" bound nested text field of the selected row in the table field is "Appetiser - Bought"
        Then the value of the text field is "UPDATED"
        When the user clicks the "Apply" button of the dialog on the sidebar
        And the user selects the "field" bound table field on the main page
        And the user selects the row 1 of the table field
        Then the value of the "product" bound nested text field of the selected row in the table field is "UPDATED"

    Scenario: As a user I want to save the changes I make on the sidebar
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TableWithSidebar/eyJfaWQiOiIyIn0="
        Then the "Field - Table - With sidebar" titled page is displayed
        And the user selects the "field" bound table field on the main page
        And the user selects the row 1 of the table field
        When the user clicks the "Edit on sidebar" dropdown action of the selected row of the table field
        And the user selects the "Product" labelled text field on the sidebar
        Then the value of the text field is "Appetiser - Bought"
        And the user selects the "field" bound table field on the main page
        And the user selects the row 1 of the table field
        Then the value of the "product" bound nested text field of the selected row in the table field is "Appetiser - Bought"
        When the user writes "AAAA Appetiser - Bought" in the text field
        And the user selects the "field" bound table field on the main page
        And the user selects the row 1 of the table field
        Then the value of the "product" bound nested text field of the selected row in the table field is "Appetiser - Bought"
        Then the value of the text field is "AAAA Appetiser - Bought"
        When the user clicks the "Apply" button of the dialog on the sidebar
        And the user selects the "field" bound table field on the main page
        And the user selects the row 1 of the table field
        Then the value of the "product" bound nested text field of the selected row in the table field is "AAAA Appetiser - Bought"
        And the user clicks the "Save" labelled business action button on the main page
        And the user waits 1 second
        When the user navigates to the following link: "@sage/xtrem-show-case/TableWithSidebar/eyJfaWQiOiIyIn0="
        And the user selects the "field" bound table field on the main page
        And the user selects the row 1 of the table field
        Then the value of the "product" bound nested text field of the selected row in the table field is "AAAA Appetiser - Bought"
        When the user clicks the "Edit on sidebar" dropdown action of the selected row of the table field
        And the user selects the "Product" labelled text field on the sidebar
        Then the value of the text field is "AAAA Appetiser - Bought"
        When the user writes "Appetiser - Bought" in the text field
        When the user clicks the "Apply" button of the dialog on the sidebar
        And the user selects the "field" bound table field on the main page
        And the user selects the row 1 of the table field
        Then the value of the "product" bound nested text field of the selected row in the table field is "Appetiser - Bought"
        And the user clicks the "Save" labelled business action button on the main page
        And the user waits 1 second

    Scenario: As a user I want to cancel the changes I make on the sidebar
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TableWithSidebar/eyJfaWQiOiIyIn0="
        Then the "Field - Table - With sidebar" titled page is displayed
        And the user selects the "field" bound table field on the main page
        And the user selects the row 1 of the table field
        When the user clicks the "Edit on sidebar" dropdown action of the selected row of the table field
        And the user selects the "Product" labelled text field on the sidebar
        Then the value of the text field is "Appetiser - Bought"
        And the user selects the "field" bound table field on the main page
        And the user selects the row 1 of the table field
        Then the value of the "product" bound nested text field of the selected row in the table field is "Appetiser - Bought"
        When the user writes "AAAA Appetiser - Bought" in the text field
        Then the value of the "product" bound nested text field of the selected row in the table field is "Appetiser - Bought"
        Then the value of the text field is "AAAA Appetiser - Bought"
        When the user clicks the "Cancel" button of the dialog on the sidebar
        And the user waits 1 second
        And the user selects the "field" bound table field on the main page
        And the user selects the row 1 of the table field
        Then the value of the "product" bound nested text field of the selected row in the table field is "Appetiser - Bought"

    Scenario: As a user I want to use the lookup dialog of a reference field on the sidebar
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TableWithSidebar/eyJfaWQiOiIyIn0="
        Then the "Field - Table - With sidebar" titled page is displayed
        And the user selects the "field" bound table field on the main page
        And the user selects the row 1 of the table field
        When the user clicks the "Edit on sidebar" dropdown action of the selected row of the table field
        And the user selects the "Provider" labelled reference field on the sidebar
        Then the value of the reference field is "Amazon"
        When the user clicks the lookup button of the reference field
        And the user selects the "provider" bound table field on a modal
        And the user selects the row 3 of the table field
        Then the value of the "Provider" labelled nested text field of the selected row in the table field is "Decathlon"
        When the user clicks the "Provider" labelled nested field of the selected row in the table field
        And the user waits 2 seconds
        Then the value of the reference field is "Decathlon"
        # We need to wait so the lookup dialog fades out
        And the user waits 2 seconds
        When the user clicks the "Apply" button of the dialog on the sidebar
        And the user selects the "field" bound table field on the main page
        And the user selects the row 1 of the table field
        Then the value of the "Provider" labelled nested reference field of the selected row in the table field is "Decathlon"

    Scenario: As a user I want the clean sidebar to close if I cancel it
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TableWithSidebar/eyJfaWQiOiIyIn0="
        And the "Field - Table - With sidebar" titled page is displayed
        And the user selects the "field" bound table field on the main page
        And the user selects the row 1 of the table field
        When the user clicks the "Edit on sidebar" dropdown action of the selected row of the table field
        When the user clicks the "Cancel" button of the dialog on the sidebar
        Then no dialogs are displayed

    Scenario: As a user I want the dirty sidebar to display a warning if I close it
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TableWithSidebar/eyJfaWQiOiIyIn0="
        And the "Field - Table - With sidebar" titled page is displayed
        And the user selects the "field" bound table field on the main page
        And the user selects the row 1 of the table field
        When the user clicks the "Edit on sidebar" dropdown action of the selected row of the table field
        And the user selects the "Product" labelled text field on the sidebar
        When the user writes "UPDATED" in the text field
        When the user clicks the "Cancel" button of the dialog on the sidebar
        Then a warn dialog appears
        And the text in the body of the dialog is "Leave and discard your changes?"
        When the user clicks the "Discard" button of the Confirm dialog
        Then no dialogs are displayed

    Scenario: As a user I want to continously create new records
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TableWithSidebar/eyJfaWQiOiIyIn0="
        And the "Field - Table - With sidebar" titled page is displayed
        And the user selects the "field" bound table field on the main page
        When the user adds a new table row to the table field using the sidebar
        And the user selects the "Product" labelled text field on the sidebar
        When the user writes "AAAA Apple" in the text field
        And the user selects the "Quantity" labelled numeric field on the sidebar
        When the user writes "20" in the numeric field
        And the user selects the "List price" labelled numeric field on the sidebar
        When the user writes "10" in the numeric field
        And the user selects the "Net price" labelled numeric field on the sidebar
        Then the value of the numeric field is "9.00"
        And the user selects the "Description" labelled text field on the sidebar
        When the user writes "AAAA TEST description" in the text field
        When the user clicks the "Apply and add new" button of the dialog on the sidebar
        And the user selects the "Product" labelled text field on the sidebar
        When the user writes "AAAB Apple" in the text field
        And the user selects the "Quantity" labelled numeric field on the sidebar
        When the user writes "21" in the numeric field
        And the user selects the "List price" labelled numeric field on the sidebar
        When the user writes "11" in the numeric field
        And the user selects the "Net price" labelled numeric field on the sidebar
        Then the value of the numeric field is "9.90"
        And the user selects the "Description" labelled text field on the sidebar
        When the user writes "AAAB TEST description" in the text field
        When the user clicks the "Apply" button of the dialog on the sidebar
        And the user waits 1 seconds
        And the user selects the "field" bound table field on the main page
        And the user selects the row 1 of the table field
        Then the value of the "product" bound nested text field of the selected row in the table field is "AAAA Apple"
        And the value of the "description" bound nested text field of the selected row in the table field is "AAAA TEST description"
        And the value of the "listPrice" bound nested numeric field of the selected row in the table field is "10.00"
        And the value of the "netPrice" bound nested numeric field of the selected row in the table field is "9.00"
        And the user selects the row 2 of the table field
        And the value of the "product" bound nested text field of the selected row in the table field is "AAAB Apple"
        And the value of the "description" bound nested text field of the selected row in the table field is "AAAB TEST description"
        And the value of the "listPrice" bound nested numeric field of the selected row in the table field is "11.00"
        And the value of the "netPrice" bound nested numeric field of the selected row in the table field is "9.90"

    Scenario: As a user I want to know if a field in my sidebar has errors
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TableWithSidebar/eyJfaWQiOiIyIn0="
        And the "Field - Table - With sidebar" titled page is displayed
        And the user selects the "field" bound table field on the main page
        And the user selects the row 1 of the table field
        When the user clicks the "Edit on sidebar" dropdown action of the selected row of the table field
        Then the user selects the "Quantity" labelled numeric field on the sidebar
        And the user writes "-2" in the numeric field
        And the user presses Enter
        Then the numeric field is invalid
        And the "Apply" button of the dialog is disabled on the sidebar

    Scenario: As a user I want to be prevented from applying an invalid sidebar
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TableWithSidebar/eyJfaWQiOiIyIn0="
        And the "Field - Table - With sidebar" titled page is displayed
        And the user selects the "field" bound table field on the main page
        When the user adds a new table row to the table field using the sidebar
        When the user clicks the "Apply" button of the dialog on the sidebar
        And the user selects the "Product" labelled text field on the sidebar
        Then the text field is invalid

    Scenario: As a user I want to remove a table record from the sidebar
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TableWithSidebar/eyJfaWQiOiIxIn0="
        Then the "Field - Table - With sidebar" titled page is displayed
        When the user selects the "field" bound table field on the main page
        And the user selects the row with text "Beans - French" in the "product" bound column header of the table field
        And the user clicks the "Edit on sidebar" dropdown action of the selected row of the table field
        Then the "Beans - French" titled sidebar is displayed
        Then the "Field - Table - With sidebar" titled page is displayed
        When the user clicks the "remove" labelled more actions button in the sidebar header
        Then no dialogs are displayed
        And the "Field - Table - With sidebar" titled page is displayed
