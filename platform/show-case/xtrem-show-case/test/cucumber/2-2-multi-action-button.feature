Feature: 2-2-multi-action-button
    # Tests multi-action button components, verifying dropdown menu functionality and triggering of different actions from a single button group

    Scenario: As an ATP/XTreeM user I can interact with multi action button in the navigation panel full width

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/NavigationPanelWithMultiCreateAction"
        Then the "Products" titled page is displayed

        Given the user clicks the "List" labelled multi action button on the navigation panel
        # And takes a screenshot
        Then a toast containing text "This should handle the custom" is displayed


    Scenario: As an ATP/XTreeM user I can interact with multi action button in the navigation panel


        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/NavigationPanelWithMultiCreateAction"
        Then the "Products" titled page is displayed

        Given the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        And the user clicks the "Product" labelled nested field of the selected row in the table field

        Given the user clicks the "List" labelled multi action button on the navigation panel
        Then a toast containing text "This should handle the custom" is displayed
