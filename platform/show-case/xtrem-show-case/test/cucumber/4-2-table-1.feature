Feature: 4-2 Table 1
    Scenario: As a user I want to click in a specific column
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Table"
        When the user selects the "field" bound table field on the main page
        And the user selects the row 4 of the table field
        And the user clicks the "st" bound nested field of the selected row in the table field

    Sc<PERSON>rio: As a user I want get the total price of selected items
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Table"
        When the user selects the "field" bound table field on the main page
        And the user selects the row 8 of the table field
        And the user ticks the main checkbox of the selected row in the table field
        And the user selects the "tableSelectedTotal" bound numeric field on the main page
        Then the value of the numeric field is "56.35"

    Scenario: As a user I want to check enabled dropdown actions
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Table"
        And the user selects the "field" bound table field on the main page
        When the user selects the row 8 of the table field
        When the user writes "11" in the "qty" bound nested numeric field of the selected row in the table field
        Then the "Maybe Disabled" dropdown action of the selected row in the table field is enabled
        When the user writes "6" in the "qty" bound nested numeric field of the selected row in the table field
        Then the "Maybe Disabled" dropdown action of the selected row in the table field is disabled

    # Flaky scenario, investigate later
    # Scenario: As an ATP XTreeM user I can verify the state visibility of the dropdown action of the selected row of the data table
    #     XT-59070
    #     Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Table"
    #     And the "Field - Table" titled page is displayed
    #     And the user selects the "field" bound table field on the main page
    #     And the user selects the row with text "Banana - Green" in the "Product" labelled column header of the table field
    #     Then the "Add" dropdown action of the selected row in the table field is displayed
    #     Then the "Maybe disabled" dropdown action of the selected row in the table field is displayed
    #     Then the "Action no icon" dropdown action of the selected row in the table field is displayed
    #     Then the "Refresh record" dropdown action of the selected row in the table field is displayed
    #     Then the "Remove" dropdown action of the selected row in the table field is displayed
    #     Then the "DROPDOWN_ACTION_NOT_FOUND" dropdown action of the selected row in the table field is hidden

    Scenario: As an ATP XTreeM user I can verify the state visibility of the dropdown action of the specific row of the data table
        XT-59070
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Table"
        And the "Field - Table" titled page is displayed
        And the user selects the "field" bound table field on the main page
        When the user selects the row 2 of the table field
        Then the "Maybe disabled" dropdown action of the selected row in the table field is displayed
        Then the user waits for 1 second
        When the user selects the row 1 of the table field
        Then the "Action no icon" dropdown action of the selected row in the table field is displayed
        Then the user waits for 1 second
        When the user selects the row 2 of the table field
        Then the "Refresh record" dropdown action of the selected row in the table field is displayed
        Then the user waits for 1 second
        When the user selects the row 1 of the table field
        Then the "Remove" dropdown action of the selected row in the table field is displayed
        Then the user waits for 1 second
        Then the "DROPDOWN_ACTION_NOT_FOUND" dropdown action of the selected row in the table field is hidden

    Scenario: As an ATP XTreeM user I can verify the enabled or disabled state of the dropdown action of the selected row of the data table
        XT-59070
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Table"
        And the "Field - Table" titled page is displayed
        And the user selects the "field" bound table field on the main page
        And the user selects the row with text "Appetizer - Cheese Bites" in the "Product" labelled column header of the table field
        Then the "Maybe disabled" dropdown action of the selected row in the table field is disabled
        Then the "Action no icon" dropdown action of the selected row in the table field is disabled
        Then the "Refresh record" dropdown action of the selected row in the table field is enabled
        Then the "Remove" dropdown action of the selected row in the table field is enabled


    Scenario: As an ATP XTreeM user I can verify the enabled or disabled state of the dropdown action of the specific row of the data table
        XT-59070
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Table"
        And the "Field - Table" titled page is displayed
        And the user selects the "field" bound table field on the main page
        Then the user selects the row 2 of the table field
        And the "Maybe disabled" dropdown action of the selected row in the table field is disabled
        And the "Action no icon" dropdown action of the selected row in the table field is disabled
        And the "Refresh record" dropdown action of the selected row in the table field is enabled
        Then the user selects the row 1 of the table field
        And the "Remove" dropdown action of the selected row in the table field is enabled
    # And takes a screenshot

    Scenario: As an ATP XTreeM user I can verify  the state visibility of the header action button of the data table
        XT-59070
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-import-export/ImportData"
        And selects the "Import results" labelled navigation anchor on the main page
        And the user selects the "importResults" bound table field on the main page
        Then the "Refresh" labelled header action button of the table field is displayed
        Then the "ACTION_NOT_FOUND" labelled header action button of the table field is hidden

    Scenario: As an ATP XTreeM user I can verify  the state visibility of the button of the data table
        XT-59070
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Table"
        When the user selects the "field" bound table field on the main page
        Then the "Show table filters" labelled button of the table field is displayed
        Then the "Open column panel" labelled button of the table field is displayed
        Then the "BUTTON_NOT_FOUND" labelled button of the table field is hidden

    Scenario: As an ATP XTreeM user I can verify the enabled or disabled state of the header action button of the data table
        XT-60356
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-import-export/ImportData"
        And selects the "Import results" labelled navigation anchor on the main page
        And the user selects the "importResults" bound table field on the main page
        Then the "Refresh" labelled header action button of the table field is enabled

    Scenario: As an ATP XTreeM user I can verify the enabled or disabled state of the button of the data table
        XT-60356
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Table"
        When the user selects the "field" bound table field on the main page
        Then the "Show table filters" labelled button of the table field is enabled
        Then the "Open column panel" labelled button of the table field is enabled

    Scenario: As an app developer I want nested reference values to be correctly set and passed to the onChange listener
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Table/eyJfaWQiOiIyIn0="
        And the user selects the "field" bound table field on the main page
        When the user selects the row 1 of the table field
        And the user writes "Ali Express" in the "provider__textField" bound nested reference field of the selected row in the table field
        And the user presses Enter
        Then a toast containing text "New provider set: Ali Express" is displayed

    # DO NOT FIX NOW

    # Scenario: As a user I want to check disabled dropdown actions
    #     Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Table"
    #     And the user selects the "field" bound table field on the main page
    #     When the user writes "9" in the "qty" bound nested numeric field of row 4 in the table field
    #     Then the "Maybe Disabled" dropdown action of row 4 in the table field is disabled

    # Flaky scenario, to be investigated
    # Scenario: As a user I want to modify the table content
    #     Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Table"
    #     And the user selects the "field" bound table field on the main page
    #     When the user writes "My product" in the "product" bound nested numeric field of row 1 in the table field
    #     Then the value of the "product" bound nested text field of row 1 in the table field is "My product"
    #     When the user writes "1234" in the "qty" bound nested numeric field of row 8 in the table field
    #     Then the value of the "qty" bound nested text field of row 8 in the table field is "1234"

    # Flaky scenario, to be investigated
    # Scenario: As a user I want to see the value of the text field
    #     Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Table/eyJfaWQiOjJ9"
    #     And the user selects the "field" bound table field on the main page
    #     Then the value of the "product" bound nested text field of row 1 in the table field is "Appetiser - Bought"

    Scenario: Value of table nested labels
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Table/eyJfaWQiOjJ9"
        And the user selects the "field" bound table field on the main page
        When the user selects the row 1 of the table field
        Then the value of the "st" bound nested label field of the selected row in the table field is "54"

    Scenario: As an ATP / XTreeM user I want to control the value of the nested progress field of the selected table row
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Table/eyJfaWQiOjJ9"
        Then the user selects the "field" bound table field on the main page
        And the user selects the row with text "Appetiser - Bought" in the "Product" labelled column header of the table field
        Then the value of the "progress" bound nested progress field of the selected row in the table field is "10 %"
        Then the value of the "progress" bound nested progress field of the selected row in the table field is "10"

    Scenario: As an ATP / XTreeM user I want to control the value of the nested progress of the floating row in the table
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Table/eyJfaWQiOjJ9"
        Then the user selects the "field" bound table field on the main page
        And the user selects the floating row of the table field
        Then the value of the "progress" bound nested progress field of the selected row in the table field is "0%"

    Scenario: As a user I want the set scale to limit the decimal places of input
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Table"
        And the user selects the "field" bound table field on the main page
        And the user selects the row 1 of the table field
        When the user writes "1.234" in the "listPrice" bound nested numeric field of the selected row in the table field
        Then the value of the "listPrice" labelled nested text field of the selected row in the table field is "1.23"

    Scenario: As a user I want load data to a transient table and test event listeners
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TransientTable"
        When the user clicks the "loadButton" bound button on the main page
        And the user selects the "field" bound table field on the main page
        And the user selects the row 2 of the table field
        Then the value of the "product" bound nested text field of the selected row in the table field is "Veal Inside - Provimi"
        When the user ticks the main checkbox of the selected row in the table field
        And the user selects the "tableSelectedTotal" bound numeric field on the main page
        Then the value of the numeric field is "115.91"
        And the user selects the "field" bound table field on the main page
        And the user selects the row 2 of the table field
        When the user writes "1" in the "qty" bound nested numeric field of the selected row in the table field
        And the user selects the "tableSelectedTotal" bound numeric field on the main page
        Then the value of the numeric field is "60.49"
        And the user selects the "field" bound table field on the main page
        And the user selects the row 2 of the table field
        When the user writes "4" in the "qty" bound nested numeric field of the selected row in the table field
        And the user selects the "tableSelectedTotal" bound numeric field on the main page
        Then the value of the numeric field is "241.96"


    # Scenario: As a developer I want to select rows even before the table is rendered
    #     XT-3449
    #     Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TransientTable"
    #     When the user clicks the "preselectButton" bound button on the main page
    #     Then the "field" bound table field on the main page is displayed
    #     And the user selects the "field" bound table field on the main page
    #     Then the row 1 of the table field is selected
    #     And the row 2 of the table field is unselected
    #     And the row 3 of the table field is selected

    Scenario Outline: As a user I want to use insert decimal numbers to the table using different locales
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TableNestedReference"
        When the user switches language to <Language>
        Then the user navigates to the following link: "@sage/xtrem-show-case/TableNestedReference/eyJfaWQiOiIxIn0="
        And the user selects the "field" bound table field on the main page
        And the user selects the row 1 of the table field
        And the user writes <Value> in the "Price" labelled nested numeric field of the selected row in the table field
        Then the value of the "Price" labelled nested text field of the selected row in the table field is <Expected>
        # The space in the expected French number is a special, non breaking space
        Examples:
            | Language     | Value     | Expected       |
            | "French"     | "1234,56" | "1 234,56"     |
            | "English US" | "1"       | "1.00"         |
            | "German"     | "1"       | "1,00"         |
            | "English US" | "1000000" | "1,000,000.00" |
            | "German"     | "1000000" | "1.000.000,00" |
            | "English US" | "-4.321"  | "-4.32"        |
            | "German"     | "-4,321"  | "-4,32"        |
            | "English US" | ".32"     | "0.32"         |
            | "German"     | ",32"     | "0,32"         |

    Scenario: As a developer I want programatically select and unselect rows in a table
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Table"
        When the user clicks the "selectRecord" bound button on the main page
        When the user clicks the "recalculateTotal" bound button on the main page
        And the user selects the "tableSelectedTotal" bound numeric field on the main page
        Then the value of the numeric field is "91.26"
        When the user clicks the "unselectRecord" bound button on the main page
        When the user clicks the "recalculateTotal" bound button on the main page
        And the user selects the "tableSelectedTotal" bound numeric field on the main page
        Then the value of the numeric field is "0.00"


    Scenario: As a developer I want to programatically unselect all rows in a table
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Table"
        When the user selects the "field" bound table field on the main page
        And the user selects the row 1 of the table field
        When the user ticks the main checkbox of the selected row in the table field
        And the user selects the row 2 of the table field
        When the user ticks the main checkbox of the selected row in the table field
        And the user selects the row 3 of the table field
        When the user ticks the main checkbox of the selected row in the table field
        And the user selects the row 4 of the table field
        When the user ticks the main checkbox of the selected row in the table field
        And the user selects the "tableSelectedTotal" bound numeric field on the main page
        Then the value of the numeric field is "397.55"
        # We have to wait here so the notifications are cleared up
        And the user waits 5 seconds
        When the user clicks the "unselectAllRecords" bound button on the main page
        When the user clicks the "recalculateTotal" bound button on the main page
        And the user selects the "tableSelectedTotal" bound numeric field on the main page
        Then the value of the numeric field is "0.00"

    Scenario: As a functional developer I want all and selections to be preserved upon toggling floating filters
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Table"
        And the user selects the "field" bound table field on the main page
        And the user selects the row 1 of the table field
        When the user ticks the main checkbox of the selected row in the table field
        And the user selects the "tableSelectedTotal" bound numeric field on the main page
        Then the value of the numeric field is "106.99"
        And the user selects the "field" bound table field on the main page
        When the user clicks the "Show table filters" labelled button of the table field
        And the user selects the "tableSelectedTotal" bound numeric field on the main page
        Then the value of the numeric field is "106.99"
        And the user selects the "field" bound table field on the main page
        When the user clicks the "Hide table filters" labelled button of the table field
        And the user selects the "tableSelectedTotal" bound numeric field on the main page
        Then the value of the numeric field is "106.99"

    Scenario: As a functional developer I want all modifications and selections to be preserved upon changing a filter
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Table"
        And the user selects the "field" bound table field on the main page
        And the user selects the row 1 of the table field
        When the user ticks the main checkbox of the selected row in the table field
        And the user selects the "tableSelectedTotal" bound numeric field on the main page
        Then the value of the numeric field is "106.99"
        And the user selects the "field" bound table field on the main page
        And the user selects the row 1 of the table field
        When the user writes "17" in the "Quantity" labelled nested numeric field of the selected row in the table field
        Then the value of the "Quantity" labelled nested numeric field of the selected row in the table field is "17"
        And the user selects the "tableSelectedTotal" bound numeric field on the main page
        Then the value of the numeric field is "168.98"
        When the user clicks in the "filterTableButton" bound button field on the main page
        And the user selects the "tableSelectedTotal" bound numeric field on the main page
        Then the value of the numeric field is "168.98"
        And the user selects the "field" bound table field on the main page
        And the user selects the row 1 of the table field
        Then the value of the "Quantity" labelled nested numeric field of the selected row in the table field is "17"

    Scenario: As a developer I want to force redraw the table as I update callback referred values
        XT-1978
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Table"
        And the user selects the "field" bound table field on the main page
        And the user selects the row 2 of the table field
        When the value of the "Quantity" labelled nested numeric field of the selected row in the table field is "6"
        And the user selects the "Quantity value prefix" labelled text field on the main page
        And the user writes "EUR" in the text field
        And the user selects the "field" bound table field on the main page
        And the user selects the row 2 of the table field
        Then the value of the "Quantity" labelled nested numeric field of the selected row in the table field is "6"
        When the user clicks in the "redrawButton" bound button field on the main page
        And the user selects the "field" bound table field on the main page
        And the user selects the row 2 of the table field
        Then the value of the "Quantity" labelled nested numeric field of the selected row in the table field is "EUR 6"

    Scenario: As a developer I want to trigger force redraw automatically in my on change callback
        XT-1978
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Table"
        And the user selects the "field" bound table field on the main page
        And the user selects the row 2 of the table field
        Then the value of the "Quantity" labelled nested numeric field of the selected row in the table field is "6"
        And the user selects the "Quantity value scale (auto redraw)" labelled numeric field on the main page
        Then the user writes "2" in the numeric field
        And the user blurs the numeric field
        And the user selects the "field" bound table field on the main page
        And the user selects the row 2 of the table field
        Then the value of the "Quantity" labelled nested numeric field of the selected row in the table field is "6.00"

    # Scenario: As a developer I want the effect of a transient input nested field when saving a row
    #     XT-593
    #     Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Table"
    #     And the user selects the "field" bound table field on the main page
    #     Then the value of the "Quantity" labelled nested numeric field of row 3 in the table field is "21"
    #     When the user writes "42" in the "fixedQuantity" bound nested numeric field of row 3 in the table field
    #     And the user selects the "Save" labelled business action button on the main page
    #     And the user selects the "field" bound table field on the main page
    #     Then the value of the "Quantity" labelled nested numeric field of row 3 in the table field is "42"
    #     Then the value of the "Fixed Quantity" labelled nested numeric field of row 3 in the table field is ""

    Scenario: As a developer I want set table column title by a dynamic callback
        XT-2178
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Table"
        And the user selects the "restrictedResultSet" bound table field on the main page
        And the user selects the row 2 of the table field
        Then the value of the "Dynamically allocated title" labelled nested text field of the selected row in the table field is "good"

    Scenario: As a developer I don't want the table to send values to the server that are not on the _Input node type
        # XT-2877 In this scenario the "Net Price" column is a "getValue" calculated property which is not on the input graph type.
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseProvider/eyJfaWQiOiIyIn0="
        And the user selects the "products" bound table field on the main page
        And the user selects the row 3 of the table field
        Then the value of the "Description" labelled nested text field of the selected row in the table field is "multi-state"
        Then the value of the "Net Price" labelled nested numeric field of the selected row in the table field is "108.41"
        And the user writes "test value" in the "Description" labelled nested text field of the selected row in the table field
        And the user writes "123" in the "Net Price" labelled nested numeric field of the selected row in the table field
        Then the value of the "Net Price" labelled nested numeric field of the selected row in the table field is "123.00"
        And the user clicks the "Save" labelled business action button on the main page
        When the user clicks the "OK" button of the Message dialog on the main page
        And the user selects the "products" bound table field on the main page
        And the user selects the row 3 of the table field
        Then the value of the "Description" labelled nested text field of the selected row in the table field is "test value"
        And the user writes "multi-state" in the "Description" labelled nested text field of the selected row in the table field
        Then the value of the "Net Price" labelled nested numeric field of the selected row in the table field is "108.41"
        And the user clicks the "Save" labelled business action button on the main page
        When the user clicks the "OK" button of the Message dialog on the main page

    Scenario: As a user I want bulk actions to be displayed
        # XT-33508
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseProvider"
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        When the user ticks the main checkbox of the selected row in the table field
        And the user waits 1 second
        Then element with test id "e-field-bind-$navigationPanel" looks as before

    Scenario: As a user I want the bulk action bar to be displayed over the header bar
        # XT-35287
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/NavigationPanelComplexCard"
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        When the user ticks the main checkbox of the selected row in the table field
        And the user waits 1 second
        Then element with test id "e-field-bind-$navigationPanel" looks as before

    Scenario: As a user I want to be able to execute a bulk action
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseProvider"
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        When the user selects the row 1 of the table field
        When the user ticks the main checkbox of the selected row in the table field
        Then the "export" bound button field on the main page is displayed
        When the user clicks in the "export" bound button field on the main page
        And the user clicks the "Ok" button of the Custom dialog
        Then a toast containing text "Action started on the selected items." is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        Then the selected row of the table field is unselected

    Scenario: As a user I want to be notified if a bulk action cannot be started
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseProvider"
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        When the user ticks the main checkbox of the selected row in the table field
        Then the "modify" bound button field on the main page is displayed
        When the user clicks in the "modify" bound button field on the main page
        And the user clicks the "Ok" button of the Custom dialog
        Then a error toast containing text "The action could not be started. Try again." is displayed


    # Scenario: As a user I want to be able to change and save a nested filter select field by typing an existing value
    #     XT-12563
    #     Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-show-case/Table/eyJfaWQiOjJ9"
    #     And the user selects the "field" bound table field on the main page
    #     Then the value of the "email" bound nested filter select field of row 5 in the table field is ""
    #     When the user writes "<EMAIL>" in the "email" bound nested filter select field of row 5 in the table field
    #     Then the value of the "email" bound nested filter select field of row 5 in the table field is "<EMAIL>"
    #     When the user selects the "Save" labelled business action button on the main page
    #     Then the value of the "email" bound nested filter select field of row 5 in the table field is "<EMAIL>"

    # Scenario: As a user I want to be able to change and save a nested filter select field by typing a new value
    #     XT-12563
    #     Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-show-case/Table/eyJfaWQiOjJ9"
    #     And the user selects the "field" bound table field on the main page
    #     Then the value of the "email" bound nested filter select field of row 7 in the table field is ""
    #     When the user writes "<EMAIL>" in the "email" bound nested filter select field of row 7 in the table field
    #     Then the value of the "email" bound nested filter select field of row 7 in the table field is "<EMAIL>"
    #     When the user selects the "Save" labelled business action button on the main page
    #     Then the value of the "email" bound nested filter select field of row 7 in the table field is "<EMAIL>"

    Scenario: As a user I want to be able to change the value of a nested filter select field using the keyboard only
        XT-12563
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-show-case/Table/eyJfaWQiOjJ9"
        And the user selects the "field" bound table field on the main page
        And the user selects the row 5 of the table field
        Then the value of the "email" bound nested filter select field of the selected row in the table field is ""
        When the user writes "rp" in the "email" bound nested filter select field of the selected row in the table field
        And the user waits 1 second
        And the user presses ArrowDown
        And the user presses Enter
        And the user selects the "field" bound table field on the main page
        And the user selects the row 5 of the table field
        Then the value of the "email" bound nested filter select field of the selected row in the table field is "rp"
        When the user writes "rp" in the "email" bound nested filter select field of the selected row in the table field
        And the user waits 1 second
        And the user presses ArrowDown
        And the user presses ArrowDown
        And the user presses Enter
        And the user selects the "field" bound table field on the main page
        And the user selects the row 5 of the table field
        Then the value of the "email" bound nested filter select field of the selected row in the table field is "<EMAIL>"

    # Invalid test cannot update a vital parent
    # Scenario: As a user I want to be able to change and save a nested reference field by typing a value
    #    XT-12563
    #    Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-show-case/Table/eyJfaWQiOjJ9"
    #    And the user selects the "field" bound table field on the main page
    #    Then the value of the "Provider" labelled nested reference field of row 6 in the table field is "Amazon"
    #    When the user selects the "field" bound table field on the main page
    #    And the user writes "Ali Express" in the "Provider" labelled nested reference field of row 6 in the table field
    #    Then the user selects the "field" bound table field on the main page
    #    Then the value of the "Provider" labelled nested reference field of row 6 in the table field is "Ali Express"
    #    When the user selects the "Save" labelled business action button on the main page
    #    When the user navigates to the following link: "@sage/xtrem-show-case/Table/eyJfaWQiOiIxIn0="
    #    Then the user selects the "field" bound table field on the main page
    #    Then the value of the "_id" bound nested text field of row 7 in the table field is "398"

    Scenario: As a user I want to be able to change the value of a nested reference field using the keyboard only
        XT-12563
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-show-case/Table/eyJfaWQiOjJ9"
        And the user selects the "field" bound table field on the main page
        And the user selects the row 6 of the table field
        Then the value of the "Provider" labelled nested reference field of the selected row in the table field is "Amazon"
        When the user writes "A" in the "Provider" labelled nested reference field of the selected row in the table field
        And the user waits 1 second
        And the user presses ArrowDown
        And the user presses Enter
        Then a toast containing text "New provider set: Ali Express" is displayed
        And the user dismisses all the toasts
        And the user selects the "field" bound table field on the main page
        And the user selects the row 6 of the table field
        Then the value of the "Provider" labelled nested reference field of the selected row in the table field is "Ali Express"
        When the user writes "A" in the "Provider" labelled nested reference field of the selected row in the table field
        And the user waits 1 second
        And the user presses ArrowDown
        And the user presses ArrowDown
        And the user presses Enter
        Then a toast containing text "New provider set: Amazon" is displayed
        And the user dismisses all the toasts
        And the user selects the "field" bound table field on the main page
        And the user selects the row 6 of the table field
        Then the value of the "Provider" labelled nested reference field of the selected row in the table field is "Amazon"

    Scenario: As a user I want to be able to change the value of a nested reference field after clearing it
        XT-30605
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-show-case/Table/eyJfaWQiOjJ9"
        And the user selects the "field" bound table field on the main page
        When the user selects the row 6 of the table field
        Then the value of the "Provider" labelled nested reference field of the selected row in the table field is "Amazon"
        When the user writes "A" in the "Provider" labelled nested reference field of the selected row in the table field
        And the user presses Backspace
        And the user presses Enter
        Then a toast containing text "New provider set:" is displayed
        And the user dismisses all the toasts
        And the user selects the "field" bound table field on the main page
        Then the value of the "Provider" labelled nested reference field of the selected row in the table field is ""
        When the user writes "Ama" in the "Provider" labelled nested reference field of the selected row in the table field
        And the user selects "Amazon" in the "Provider" labelled nested field of the selected row in the table field
        Then a toast containing text "New provider set: Amazon" is displayed
        And the user dismisses all the toasts
        And the user selects the "field" bound table field on the main page
        Then the value of the "Provider" labelled nested reference field of the selected row in the table field is "Amazon"

    Scenario: As a user I want to be able to select a value in a nested reference field of the data table
        XT-12733
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TableNestedReference/eyJfaWQiOiIxIn0="
        And the user selects the "field" bound table field on the main page
        And the user selects the row 2 of the table field
        Then the value of the "Provider" labelled nested reference field of the selected row in the table field is "Amazon"
        When the user writes "Ali" in the "Provider" labelled nested reference field of the selected row in the table field
        And the user selects "Ali Express" in the "Provider" labelled nested field of the selected row in the table field
        Then the value of the "Provider" labelled nested reference field of the selected row in the table field is "Ali Express"

    Scenario: As a developer I want to conditionally update function filters without errors
        X3-252737
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TableFilterRefresh/eyJfaWQiOiIyIn0="
        And the user selects the "table" bound table field on the main page
        And the user selects the row 1 of the table field
        Then the value of the "netPrice" bound nested numeric field of the selected row in the table field is "19.16"
        And the user selects the "priceFilter" bound numeric field on the main page
        When the user writes "20" in the numeric field
        And the user blurs the numeric field
        And the user selects the "table" bound table field on the main page
        And the user selects the row 1 of the table field
        Then the value of the "netPrice" bound nested numeric field of the selected row in the table field is "69.74"
        When the user ticks the main checkbox of the selected row in the table field
        And the user selects the "priceFilter" bound numeric field on the main page
        When the user writes "70" in the numeric field
        And the user selects the "descriptionFilter" bound text field on the main page
        When the user writes "zero" in the text field
        And the user blurs the text field
        And the user selects the "table" bound table field on the main page
        And the user selects the row 1 of the table field
        Then the value of the "netPrice" bound nested numeric field of the selected row in the table field is "73.91"

    # Scenario: As a functional developer I would like to remap records before they are loaded to the table
    #     XT-11834
    #     Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TableRemapped/eyJfaWQiOiIyIn0="
    #     And the user selects the "field" bound table field on the main page
    #     Then the value of the "Transient Calculated" labelled nested text field of row 1 in the table field is "NOT SET Appetiser - Bought"
    #     Then the value of the "Transient Calculated" labelled nested text field of row 20 in the table field is "NOT SET Bread - Ciabatta Buns"

    # Scenario: As a functional developer I would like to remap records using information from locale storage
    #     XT-11834
    #     Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TableRemapped/eyJfaWQiOiIyIn0="
    #     And the user selects the "Set prefix to storage" labelled text field on the main page
    #     Then the user writes "Sample" in the text field
    #     And the user clicks in the "refreshTable" bound button field on the main page
    #     And the user selects the "field" bound table field on the main page
    #     Then the value of the "Transient Calculated" labelled nested text field of row 1 in the table field is "Sample Appetiser - Bought"
    #     Then the value of the "Transient Calculated" labelled nested text field of row 20 in the table field is "Sample Bread - Ciabatta Buns"

    Scenario: As a functional developer I want to refresh records from the server
        XT-19777 / XT-20927
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Table"
        And the user selects the "field" bound table field on the main page
        When the user selects the row 2 of the table field
        Then the value of the "product" bound nested text field of the selected row in the table field is "Appetizer - Cheese Bites"
        When the user writes "Test value" in the "product" bound nested numeric field of the selected row in the table field
        Then the value of the "product" bound nested text field of the selected row in the table field is "Test value"
        When the user clicks the "Refresh record" dropdown action of the selected row of the table field
        Then the value of the "product" bound nested text field of the selected row in the table field is "Appetizer - Cheese Bites"


    # The following scenario doesn't provide reliable values see https://jira.sage.com/browse/XT-41173
    # Scenario: As a user I want to see errors in my table
    #    Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-show-case/TableValidation/eyJfaWQiOiIyIn0="
    #    And the user selects the "Min Quantity" labelled numeric field on the main page
    #    When the user writes "4" in the numeric field
    #    And the user selects the "Integer" labelled numeric field on the main page
    #    When the user writes "0" in the numeric field
    #    And the user selects the "field" bound table field on the main page
    #    When the user writes "-4" in the "st" bound nested numeric field of row 1 in the table field
    #    And the user selects the "Save" labelled business action button on the main page
    #    And the user selects the "field" bound table field on the main page
    #    Then a total of "51" errors is found in the table field


    Scenario: As an ATP XTreeM user I want to be able to erase values from nested fields in a table field
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Table"
        And the user selects the "field" bound table field on the main page
        And the user selects the row 1 of the table field
        When the user writes "" in the "List Price" labelled nested numeric field of the selected row in the table field
        Then the value of the "List Price" labelled nested numeric field of the selected row in the table field is ""
        When the user writes "" in the "Description" labelled nested text field of the selected row in the table field
        Then the value of the "Description" labelled nested text field of the selected row in the table field is ""
        When the user writes "" in the "Date" labelled nested date field of the selected row in the table field
        Then the value of the "Date" labelled nested date field of the selected row in the table field is ""
