#XT-63843 - Test flacky . Need to be fxed and then reactivated.
Feature: 5 Y dashboard widget edition in a different dashboard
    # Tests the cross-dashboard widget editing capability, verifying that widgets can be properly modified when they are used in multiple dashboards

    @ClearDashboards
    Scenario: As I user I want to edit a widget added to a different dashboard than the one I am working on
        # first dashboard with first widget
        Given the user opens the application on a desktop
        Then the dashboard page is displayed
        And the "Create a dashboard to get started." subtitled empty dashboard is displayed
        When the user clicks the create button on the dashboard
        Then the dashboard creation dialog is displayed
        When the user selects the template with title "Showcase dashboard" in the dashboard creation dialog
        And the user clicks the "next" button in the dashboard creation dialog
        Then the "Showcase dashboard" titled dashboard in the dashboard editor is displayed

        # Widget selection step
        When the user clicks the "createAWidget" labelled button in the dashboard editor navigation panel
        Then the "New widget" titled widget editor dialog is displayed
        And the value of the step title of the widget editor dialog is "1. Select a widget to get started"
        When the user writes "Show Case Product" in the "basic-node" dropdown field in the widget editor dialog
        And the user presses Enter
        And the user writes "Bar Chart Widget" in the "basic-title" text field in the widget editor dialog
        And the user selects the "BAR_CHART" widget card in the widget editor dialog
        Then the "BAR_CHART" widget card in the widget editor dialog is selected

        # widget data step
        When the user clicks the "next" button in the widget editor dialog
        Then the value of the step title of the widget editor dialog is "2. Select the data to add to your widget"
        When the user selects the "Net price" tree-view element in the widget editor dialog
        And the user selects the "Ending date" tree-view element in the widget editor dialog
        And the user clicks the "next" button in the widget editor dialog

        #widget content step
        Then the value of the step title of the widget editor dialog is "3. Add your content"
        When the user writes "Ending Date" in the "horizontal-axis" dropdown field in the widget editor dialog
        And the user presses Enter
        And the user writes "Year" in the "group-by" dropdown field in the widget editor dialog
        And the user presses Enter
        And the user clicks the "Add value" table button in the widget editor dialog
        And the user writes "Net Price" in the "content-property" dropdown field of row "1" in the widget editor dialog
        And the user presses Enter
        Then the value of the "content-label" text field of row "1" in the widget editor dialog is "Net price"
        When the user writes "2" in the "content-formatting" text field of row "1" in the widget editor dialog
        And the user presses Enter
        And the user writes "Distinct count" in the "grouping-method" dropdown field of row "1" in the widget editor dialog
        And the user presses Enter
        And the user clicks the "next" button in the widget editor dialog

        #widget filter step
        Then the value of the step title of the widget editor dialog is "4. Add your filters"
        When the user clicks the "Add filter" table button in the widget editor dialog
        And the user writes "Net Price" in the "filter-property" dropdown field of row "1" in the widget editor dialog
        And the user presses Enter
        And the user writes "Less than" in the "filter-type" text field of row "1" in the widget editor dialog
        And the user presses Enter
        And the user writes "230" in the "filter-value" dropdown field of row "1" in the widget editor dialog
        And the user presses Enter

        #widget sorting step
        And the user clicks the "next" button in the widget editor dialog
        Then the value of the step title of the widget editor dialog is "5. Define sorting"
        When the user clicks the "Add a sort condition" table button in the widget editor dialog
        And the user writes "Ending Date" in the "sorting-property" dropdown field of row "1" in the widget editor dialog
        And the user presses Enter
        And the user writes "Descending" in the "sorting-order" dropdown field of row "1" in the widget editor dialog
        And the user presses Enter

        #widget layout step
        And the user clicks the "next" button in the widget editor dialog
        Then the value of the step title of the widget editor dialog is "6. Create your layout"
        When the user ticks the "See all" checkbox field in the widget editor dialog
        And the user writes "ShowCase - Product dialog" in the "layout-page-seeAllAction" dropdown field in the widget editor dialog
        And the user presses Enter
        And the user ticks the "Create" checkbox field in the widget editor dialog
        And the user writes "ShowCase - Product" in the "layout-page-createAction" dropdown field in the widget editor dialog
        And the user presses Enter
        And the user clicks the "add" button in the widget editor dialog
        Then the "Bar Chart Widget" titled widget in the dashboard editor is displayed
        And the user waits 1 second
        And the "save" button in the dashboard editor footer is enabled
        When the user clicks the "save" button in the dashboard editor footer
        And the user dismisses all the toasts
        Then the "Showcase dashboard" titled dashboard is displayed
        And the "Bar Chart Widget" titled widget in the dashboard is displayed

        When the user clicks the "create" labelled CRUD button in the dashboard action menu
        Then the dashboard creation dialog is displayed
        When the user selects the template with title "Blank template" in the dashboard creation dialog
        And the user clicks the "next" button in the dashboard creation dialog
        Then the "New dashboard" titled dashboard in the dashboard editor is displayed
        When the user clicks the "cancel" button in the dashboard editor footer
        Then the "New dashboard" titled dashboard is displayed
        When the user selects the "Showcase dashboard" labelled tab in the dashboard
        And the user clicks the "edit" labelled CRUD button in the dashboard action menu
        Then the "Showcase dashboard" titled dashboard in the dashboard editor is displayed
        When the user selects the "Bar Chart Widget" titled bar-chart widget field in the dashboard editor
        And the user clicks the "edit" more actions button in the header of the tile-indicator widget field
        Then the "Edit widget" titled widget editor dialog is displayed
        And the value of the "basic-title" text field in the widget editor dialog is "Bar Chart Widget"
        And the "BAR_CHART" widget card in the widget editor dialog is selected

        When the user clicks the "next" button in the widget editor dialog
        Then the value of the step title of the widget editor dialog is "2. Select the data to add to your widget"
        And the "Net price" tree-view element in the widget editor dialog is checked
        And the "Ending date" tree-view element in the widget editor dialog is checked

        When the user clicks the "next" button in the widget editor dialog
        Then the value of the step title of the widget editor dialog is "3. Add your content"
        And the value of the "horizontal-axis" dropdown field in the widget editor dialog is "Ending date"
        And the value of the "group-by" dropdown field in the widget editor dialog is "Year"
        And the value of the "content-property" dropdown field of row "1" in the widget editor dialog is "Net price"
        And the value of the "content-label" text field of row "1" in the widget editor dialog is "Net price"
        And the value of the "content-formatting" text field of row "1" in the widget editor dialog is "2"
        And the value of the "grouping-method" dropdown field of row "1" in the widget editor dialog is "Distinct count"

        When the user clicks the "next" button in the widget editor dialog
        Then the value of the step title of the widget editor dialog is "4. Add your filters"
        And the value of the "filter-property" dropdown field of row "1" in the widget editor dialog is "Net price"
        And the value of the "filter-type" dropdown field of row "1" in the widget editor dialog is "Less than"
        And the value of the "filter-value" text field of row "1" in the widget editor dialog is "230"

        When the user clicks the "next" button in the widget editor dialog
        Then the value of the step title of the widget editor dialog is "5. Define sorting"
        And the value of the "sorting-property" dropdown field of row "1" in the widget editor dialog is "Ending date"
        And the value of the "sorting-order" dropdown field of row "1" in the widget editor dialog is "Descending"

        When the user clicks the "next" button in the widget editor dialog
        Then the value of the step title of the widget editor dialog is "6. Create your layout"
        And the "See all" checkbox field in the widget editor dialog is checked
        And the value of the "layout-page-seeAllAction" dropdown field in the widget editor dialog is "ShowCase - Product dialog"
        And the "Create" checkbox field in the widget editor dialog is checked
        # And the value of the "layout-page-createAction" dropdown field in the widget editor dialog is "ShowCase - Product"
        And the "update" button in the widget editor dialog is enabled
