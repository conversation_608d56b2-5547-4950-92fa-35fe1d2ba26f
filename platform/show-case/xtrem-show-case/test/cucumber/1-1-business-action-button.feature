Feature: 1-1 Business action button
  # Tests business action button functionality, verifying that button clicks trigger appropriate backend actions and update UI state

  Scenario: As a user I want to perform an action using a business action button
    Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TableMobileCardViewWithOptionMenuAndSearchBox"
    Then the "Table with Mobile Card View option menu and search" titled page is displayed
    And the user selects the "field" bound table field on the main page
    Then the table field is empty
    When the user clicks the "Load" labelled business action button of the table field
    Then the table field is not empty


  Scenario: As a user I want to perform an action using a business action button that triggers a notification
    Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TableRemapped/eyJfaWQiOiIyIn0="
    Then the "Field - Table - Remapped value" titled page is displayed
    And the user selects the "field" bound table field on the main page
    When the user clicks the "itDoesNothing" labelled business action button of the table field
    Then a toast containing text "I told you." is displayed
