Feature: 3-2 Sticker
    # Tests the sticker component functionality in the navigation bar across different device types, verifying click interactions and dialog displays

    ##################################################################################################################
    # Localhost
    ##################################################################################################################

    Scenario Outline: As a user I want to click on the sticker icon in the navigation bar in desktop mode
        Given the user opens the application on a desktop
        When the user clicks the <Icon> sticker in the navigation bar
        And the text in the header of the dialog is <Title>
        And the user clicks the Close button of the dialog on the main page
        Examples:
            | Icon                  | Title       |
            | "Printer destination" | "Section 1" |
            | "User Profile"        | "Profile"   |

    Scenario Outline: As a user I want to click on the sticker icon in the navigation bar in tablet mode
        Given the user opens the application on a tablet
        When the user clicks the <Icon> sticker in the navigation bar
        And the text in the header of the dialog is <Title>
        And the user clicks the Close button of the dialog on the main page
        Examples:
            | Icon                  | Title       |
            | "Printer destination" | "Section 1" |
            | "User Profile"        | "Profile"   |

    Scenario Outline: As a user I want to click on the sticker icon in the navigation bar in mobile mode
        Given the user opens the application on a mobile
        When the user clicks the <Icon> sticker in the navigation bar
        And the text in the header of the dialog is <Title>
        And the user clicks the Close button of the dialog on the main page
        Examples:
            | Icon                  | Title       |
            | "Printer destination" | "Section 1" |
            | "User Profile"        | "Profile"   |

    Scenario Outline: As a user I want the sticker business actions to be displayed
        X3-241827
        Given the user opens the application on a desktop
        When the user clicks the <Icon> sticker in the navigation bar
        Then the "First" labelled business action button on a modal is visible
        Examples:
            | Icon                  | Title       |
            | "Printer destination" | "Section 1" |

    Scenario: As a user I want the sticker to close when the finish action is triggered
        X3-310410
        Given the user opens the application on a desktop
        Then the dashboard page is displayed
        And no dialogs are displayed
        When the user clicks the "Printer destination" sticker in the navigation bar
        Then the text in the header of the dialog is "Section 1"
        When the user clicks the "finishButton" bound button on a modal
        Then no dialogs are displayed

##################################################################################################################
# export TARGET_URL=https://adc-latest.qa-sagex3.com/handheld
# export loginUserName=<EMAIL>
# export loginPassword=Sagetest01
##################################################################################################################

# Scenario Outline: As a user I want to click on the sticker icon in the navigation bar in desktop mode
#     Given the user opens the application on a desktop
#     When the user clicks the <Icon> sticker in the navigation bar
#     And the text in the header of the dialog is <Title>
#     And the user clicks the Close button of the dialog on the main page
#     Examples:
#         | Icon                 | Title                |
#         | "Print destinations" | "Print destinations" |
#         | "Site"               | "Site"               |

# Scenario Outline: As a user I want to click on the sticker icon in the navigation bar in tablet mode
#     Given the user opens the application on a tablet
#     When the user clicks the <Icon> sticker in the navigation bar
#     And the text in the header of the dialog is <Title>
#     And the user clicks the Close button of the dialog on the main page
#     Examples:
#         | Icon                 | Title                |
#         | "Print destinations" | "Print destinations" |
#         | "Site"               | "Site"               |

# Scenario Outline: As a user I want to click on the sticker icon in the navigation bar in mobile mode
#     Given the user opens the application on a mobile
#     When the user clicks the <Icon> sticker in the navigation bar
#     And the text in the header of the dialog is <Title>
#     And the user Clicks the Close button of the dialog on the main page
#     Examples:
#         | Icon                 | Title                |
#         | "Print destinations" | "Print destinations" |
#         | "Site"               | "Site"               |
