Feature: Internal component error

    # Scenario: As a user I want to receive an error message when saving a page with and internal component error
    #     XT-3091
    #     Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseProduct"
    #     And the user clicks on the record with the text "Appetiser - Bought" in the navigation panel
    #     And the value of the "Description" labelled text field on the main page is "frame"
    #     When the user writes "....." to the "Ending date" labelled date field on the main page
    #     And the value of the "Description" labelled text field on the main page is "frame"
    #     And the user clicks on the save CRUD button on the main page
    #     Then an error dialog appears
    #     And the text in the header of the dialog is "Validation Error"
    #     And the text in the body of the dialog is "Invalid date value provided"
