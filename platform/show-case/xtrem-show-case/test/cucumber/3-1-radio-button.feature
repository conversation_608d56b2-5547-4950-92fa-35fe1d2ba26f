Feature: 3-1 Radio button
   # Tests the radio button component across different devices, verifying proper selection, value persistence, visual indicators, and control the radio button field state.

   Scenario Outline: <Device> - As an ATP XTreeM User I can select and verify the selected option of the radio button field using label
      XT-25838
      Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/RadioButtons"
      Then the "Field - Radio" titled page is displayed

      Given the user selects the "Title" labelled text field on the main page
      When the user writes "Sample title" in the text field
      And the user presses Tab

      Given the user selects the "Sample title" labelled radio field on the main page
      When the user selects the value "great" in the radio field
      Then the value "great" of the radio field is selected
      Then the value "notBad" of the radio field is not selected
      Then the value "awful" of the radio field is not selected
      Examples:
         | Device  |
         | desktop |
         | tablet  |
         | mobile  |


   Scenario Outline: <Device> - As an ATP XTreeM User I can select and verify the selected option of the radio button field using bind
      XT-25838
      Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/RadioButtons"
      Then the "Field - Radio" titled page is displayed

      Given the user selects the "field" bound radio field on the main page
      When the user selects the label "Good" in the radio field
      Then the value "good" of the radio field is selected
      Then the value "great" of the radio field is not selected
      Then the value "notBad" of the radio field is not selected
      Examples:
         | Device  |
         | desktop |
         | tablet  |
         | mobile  |

   #radio button Field: Set / check properties

   Scenario Outline: <Device> - As and ATP XTreeM user I can verify the radio field title
      XT-25838
      Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/RadioButtons"
      Then the "Field - Radio" titled page is displayed

      Given the user selects the "Title" labelled text field on the main page
      When the user writes "Sample title" in the text field
      And the user presses Tab

      Given the user selects the "field" bound radio field on the main page
      Then the title of the radio field is "Sample title"
      Then the title of the radio field is displayed

      Given the user selects the "Is title hidden" labelled checkbox field on the main page
      When the user ticks the checkbox field

      Given the user selects the "field" bound radio field on the main page
      Then the title of the radio field is hidden
      Examples:
         | Device  |
         | desktop |
         | tablet  |
         | mobile  |


   Scenario Outline: <Device> - As and ATP XTreeM user I can verify the radio field helper text
      XT-25838
      Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/RadioButtons"
      Then the "Field - Radio" titled page is displayed

      Given the user selects the "Helper text" labelled text field on the main page
      When the user writes "Sample text" in the text field
      And the user presses Tab

      Given the user selects the "field" bound radio field on the main page
      Then the helper text of the radio field is "Sample text"
      Then the helper text of the radio field is displayed

      Given the user selects the "Is helper text hidden" labelled checkbox field on the main page
      When the user ticks the checkbox field

      Given the user selects the "field" bound radio field on the main page
      Then the helper text of the radio field is hidden
      Examples:
         | Device  |
         | desktop |
         | tablet  |
         | mobile  |


   Scenario Outline: <Device> - As and ATP XTreeM user I can verify if the radio field is displayed or hidden
      XT-25838
      Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/RadioButtons"
      Then the "Field - Radio" titled page is displayed

      Then the "field" bound radio field on the main page is displayed

      Given the user selects the "Is hidden" labelled checkbox field on the main page
      When the user ticks the checkbox field

      Then the "field" bound radio field on the main page is hidden
      Examples:
         | Device  |
         | desktop |
         | tablet  |
         | mobile  |


   Scenario Outline:<Device> - As and ATP XTreeM user I can verify if the radio field is enabled or disabled
      XT-25838
      Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/RadioButtons"
      Then the "Field - Radio" titled page is displayed

      Given the user selects the "field" bound radio field on the main page
      Then the radio field is enabled

      Given the user selects the "Is disabled" labelled checkbox field on the main page
      When the user ticks the checkbox field

      Given the user selects the "field" bound radio field on the main page
      Then the radio field is disabled
      Examples:
         | Device  |
         | desktop |
         | tablet  |
         | mobile  |


   Scenario Outline: <Device> - As and ATP XTreeM user I can verify if the radio field is read-only
      XT-25838
      Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/RadioButtons"
      Then the "Field - Radio" titled page is displayed

      Given the user selects the "Is readOnly" labelled checkbox field on the main page
      When the user ticks the checkbox field

      Given the user selects the "field" bound radio field on the main page
      Then the radio field is read-only
      Examples:
         | Device  |
         | desktop |
         | tablet  |
         | mobile  |


   Scenario: Test with longer text in the radio button
      Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/RadioButtons"
      Then the "Field - Radio" titled page is displayed

      Given the user selects the "Options set in file" labelled radio field on the main page
      When the user selects the label "a very long option, just to test line breaks" in the radio field
      Then the value "a very long option, just to test line breaks" of the radio field is selected


   Scenario: Trigger custom change event handler
      Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/RadioButtons"
      Then the "Field - Radio" titled page is displayed

      And the "changeTriggered" bound label field on the main page is hidden

      Given the user selects the "field" bound radio field on the main page
      When the user selects the value "great" in the radio field

      Then the "changeTriggered" bound label field on the main page is displayed
