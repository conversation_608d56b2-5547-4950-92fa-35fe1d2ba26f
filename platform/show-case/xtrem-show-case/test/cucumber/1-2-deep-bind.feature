Feature: 1-2 Deep bind
    # Tests the functionality of deeply bound fields, ensuring that nested property data can be properly read, updated, and persisted

    Scenario: As a user I want to read and update deeply bound text fields
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/DeepBindingSimpleFields/eyJfaWQiOiIyMSJ9"
        And the user selects the "Address line 1" labelled text field on the main page
        Then the value of the text field is "32 London Bridge St"
        And the user selects the "Address name" labelled text field on the main page
        Then the value of the text field is "London Office"
        And the user selects the "Address line 1" labelled text field on the main page
        When the user writes "Building 1, Exchange Quay" in the text field
        And the user selects the "Address name" labelled text field on the main page
        When the user writes "Manchester office" in the text field
        And the user clicks the "Save" labelled business action button on the main page
        And the user waits 1 second
        Then the user navigates to the following link: "@sage/xtrem-show-case/DeepBindingSimpleFields/eyJfaWQiOiIyMSJ9"
        And the user selects the "Address line 1" labelled text field on the main page
        Then the value of the text field is "Building 1, Exchange Quay"
        And the user selects the "Address name" labelled text field on the main page
        Then the value of the text field is "Manchester office"
        And the user selects the "Address line 1" labelled text field on the main page
        When the user writes "32 London Bridge St" in the text field
        And the user selects the "Address name" labelled text field on the main page
        When the user writes "London Office" in the text field
        And the user clicks the "Save" labelled business action button on the main page
        And the user waits 1 second
        Then the user navigates to the following link: "@sage/xtrem-show-case/DeepBindingSimpleFields/eyJfaWQiOiIyMSJ9"
        And the user selects the "Address line 1" labelled text field on the main page
        Then the value of the text field is "32 London Bridge St"
        And the user selects the "Address name" labelled text field on the main page
        Then the value of the text field is "London Office"


    Scenario: As a user I want to use the text searchbox on the navigation panel filter to search by deeply bound field values
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/DeepBindingSimpleFields/eyJfaWQiOiIxMDYifQ=="
        And the user selects the "Name" labelled text field on the main page
        Then the value of the text field is "Anisette - Mcguiness"
        And the user selects the "Address line 1" labelled text field on the main page
        Then the value of the text field is ""
        And the user selects the "Address name" labelled text field on the main page
        Then the value of the text field is ""
        Then the user opens the navigation panel
        And the user searches for "London Bridge" in the navigation panel
        And the user clicks the record with the text "Cream Of Tartar" in the navigation panel
        And the user selects the "Name" labelled text field on the main page
        Then the value of the text field is "Cream Of Tartar"
        And the user selects the "Address line 1" labelled text field on the main page
        Then the value of the text field is "32 London Bridge St"
        And the user selects the "Address name" labelled text field on the main page
        Then the value of the text field is "London Office"

    Scenario: As a user I want to sort the navigation panel table by fields which are deeply bound
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/DeepBindingSimpleFields"
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        Then the value of the "Origin address" labelled nested text field of the selected row in the table field is ""
        And the user selects the row 2 of the table field
        Then the value of the "Origin address" labelled nested text field of the selected row in the table field is ""
        And the user selects the row 3 of the table field
        Then the value of the "Origin address" labelled nested text field of the selected row in the table field is ""
        When the user clicks the "Origin address" labelled column of the table field
        And the user selects the row 1 of the table field
        Then the value of the "Origin address" labelled nested text field of the selected row in the table field is ""
        When the user clicks the "Origin address" labelled column of the table field
        Then the value of the "Origin address" labelled nested text field of the selected row in the table field is "Carrer Jesús Serra Santamans 2"
        And the user selects the row 2 of the table field
        Then the value of the "Origin address" labelled nested text field of the selected row in the table field is "32 London Bridge St"
        And the user selects the row 3 of the table field
        Then the value of the "Origin address" labelled nested text field of the selected row in the table field is "10 Pl. de Belgique"
        When the user clicks the "Origin country" labelled column of the table field
        And the user selects the row 1 of the table field
        Then the value of the "Origin country" labelled nested text field of the selected row in the table field is ""
        When the user clicks the "Origin country" labelled column of the table field
        Then the value of the "Origin country" labelled nested text field of the selected row in the table field is "UK"
        And the user selects the row 2 of the table field
        Then the value of the "Origin country" labelled nested text field of the selected row in the table field is "FR"
        And the user selects the row 3 of the table field
        Then the value of the "Origin country" labelled nested text field of the selected row in the table field is "ES"

    Scenario: As a user I want to filter the navigation panel table by fields which are deeply bound
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/DeepBindingSimpleFields"
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        Then the value of the "Origin address" labelled nested text field of the selected row in the table field is ""
        And the user filters the "Origin address" labelled column in the table field with value "Bridge"
        Then the value of the "Origin address" labelled nested text field of the selected row in the table field is "32 London Bridge St"
        When the user filters the "Origin address" labelled column in the table field with value "Serra"
        Then the value of the "Origin address" labelled nested text field of the selected row in the table field is "Carrer Jesús Serra Santamans 2"

    Scenario: As a user I want to see that the server side data loads into my deeply bound columns
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TableDeepBoundColumns/eyJfaWQiOiIxIn0="
        Then the "Table - Deep cell binding" titled page is displayed
        And the user selects the "Products" labelled table field on the main page
        And the user selects the row 6 of the table field
        Then the value of the "Address name" labelled nested text field of the selected row in the table field is "Paris Office"
        Then the value of the "Address line 1" labelled nested text field of the selected row in the table field is "10 Pl. de Belgique"
        Then the value of the "Address line 2" labelled nested text field of the selected row in the table field is "La Garenne-Colombes"
        Then the value of the "City" labelled nested text field of the selected row in the table field is "Paris"
        Then the value of the "Country" labelled nested reference field of the selected row in the table field is "France"

    Scenario: As a user I want to use sort by a deeply bound reference field
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TableDeepBoundColumns/eyJfaWQiOiIxIn0="
        Then the "Table - Deep cell binding" titled page is displayed
        And the user selects the "Products" labelled table field on the main page
        And the user selects the row 1 of the table field
        Then the value of the "Country" labelled nested reference field of the selected row in the table field is ""
        When the user clicks the "Country" labelled column of the table field
        And the user clicks the "Country" labelled column of the table field
        Then the value of the "Country" labelled nested reference field of the selected row in the table field is "United Kingdom"
        And the user selects the row 2 of the table field
        Then the value of the "Country" labelled nested reference field of the selected row in the table field is "Spain"
        And the user selects the row 3 of the table field
        Then the value of the "Country" labelled nested reference field of the selected row in the table field is "France"

    Scenario: As a user I want to use filter by a deeply bound reference field
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TableDeepBoundColumns/eyJfaWQiOiIxIn0="
        Then the "Table - Deep cell binding" titled page is displayed
        And the user selects the "Products" labelled table field on the main page
        And the user selects the row 1 of the table field
        Then the value of the "Address name" labelled nested text field of the selected row in the table field is ""
        When the user opens the filter of the "Country" labelled column in the table field
        And the user searches "France" in the filter of the table field
        And the user ticks the item with text "France" in the filter of the table field
        And the user closes the filter of the "Country" labelled column in the table field
        Then the value of the "Country" labelled nested reference field of the selected row in the table field is "France"
        Then the value of the "Address name" labelled nested text field of the selected row in the table field is "Paris Office"

    Scenario: As a user I want to use filter and order by a deeply bound reference field
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-show-case/TableDeepBoundColumns/eyJfaWQiOiIxIn0="
        Then the "Table - Deep cell binding" titled page is displayed
        When the user selects the "Products" labelled table field on the main page
        When the user clicks the "Country" labelled column of the table field
        And the user selects the row 1 of the table field
        Then the value of the "Country" labelled nested reference field of the selected row in the table field is ""
        When the user opens the filter of the "Country" labelled column in the table field
        And the user searches "Spain" in the filter of the table field
        And the user ticks the item with text "Spain" in the filter of the table field
        And the user closes the filter of the "Country" labelled column in the table field
        Then the value of the "Address name" labelled nested text field of the selected row in the table field is "Sant Cugat Office"
        When the user opens the filter of the "Country" labelled column in the table field
        And the user searches "France" in the filter of the table field
        And the user ticks the item with text "France" in the filter of the table field
        And the user closes the filter of the "Country" labelled column in the table field
        Then the value of the "Country" labelled nested reference field of the selected row in the table field is "France"
        Then the value of the "Address name" labelled nested text field of the selected row in the table field is "Paris Office"
        And the user selects the row 2 of the table field
        Then the value of the "Country" labelled nested reference field of the selected row in the table field is "Spain"
        Then the value of the "Address name" labelled nested text field of the selected row in the table field is "Sant Cugat Office"

    Scenario: As a user I want to update a vital record from using deeply bound table columns
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TableDeepBoundColumns/eyJfaWQiOiIxIn0="
        Then the "Table - Deep cell binding" titled page is displayed
        And the user selects the "Products" labelled table field on the main page
        And the user selects the row 14 of the table field
        Then the value of the "Country" labelled nested reference field of the selected row in the table field is ""
        And the user clicks the "Country" labelled nested field of the selected row in the table field
        And the user opens the lookup dialog in the "Country" labelled nested reference field of the selected row in the table field
        And the user selects the "originAddress.country" bound table field on a modal
        When the user selects the row 2 of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        And the user selects the "Products" labelled table field on the main page
        And the user selects the row 14 of the table field
        Then the value of the "Country" labelled nested reference field of the selected row in the table field is "Albania"
        And the user writes "Sage GmbH" in the "Address name" labelled nested text field of the selected row in the table field
        And the user writes "Franklinstraße 61-63" in the "Address line 1" labelled nested text field of the selected row in the table field
        And the user writes "60486" in the "Address line 2" labelled nested text field of the selected row in the table field
        And the user writes "Frankfurt am Main" in the "City" labelled nested text field of the selected row in the table field
        Then the value of the "Address name" labelled nested text field of the selected row in the table field is "Sage GmbH"
        Then the value of the "Address line 1" labelled nested text field of the selected row in the table field is "Franklinstraße 61-63"
        Then the value of the "Address line 2" labelled nested text field of the selected row in the table field is "60486"
        Then the value of the "City" labelled nested text field of the selected row in the table field is "Frankfurt am Main"
        Then the value of the "Country" labelled nested reference field of the selected row in the table field is "Albania"
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast with text "Record updated" is displayed

        Then the user navigates to the following link: "@sage/xtrem-show-case/TableDeepBoundColumns/eyJfaWQiOiIxIn0="
        And the user selects the "Products" labelled table field on the main page
        And the user selects the row 14 of the table field
        Then the value of the "Address name" labelled nested text field of the selected row in the table field is "Sage GmbH"
        Then the value of the "Address line 1" labelled nested text field of the selected row in the table field is "Franklinstraße 61-63"
        Then the value of the "Address line 2" labelled nested text field of the selected row in the table field is "60486"
        Then the value of the "City" labelled nested text field of the selected row in the table field is "Frankfurt am Main"
        Then the value of the "Country" labelled nested reference field of the selected row in the table field is "Albania"
        And the user writes "" in the "Address name" labelled nested text field of the selected row in the table field
        And the user writes "" in the "Country" labelled nested reference field of the selected row in the table field
        Then the value of the "Country" labelled nested reference field of the selected row in the table field is ""
        And the user writes "" in the "Address line 1" labelled nested text field of the selected row in the table field
        And the user writes "" in the "Address line 2" labelled nested text field of the selected row in the table field
        And the user writes "" in the "City" labelled nested text field of the selected row in the table field
        And the user presses Tab
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast with text "Record updated" is displayed

        Then the user navigates to the following link: "@sage/xtrem-show-case/TableDeepBoundColumns/eyJfaWQiOiIxIn0="
        And the user selects the "Products" labelled table field on the main page
        And the user selects the row 14 of the table field
        Then the value of the "Address name" labelled nested text field of the selected row in the table field is ""
        Then the value of the "Address line 1" labelled nested text field of the selected row in the table field is ""
        Then the value of the "Address line 2" labelled nested text field of the selected row in the table field is ""
        Then the value of the "City" labelled nested text field of the selected row in the table field is ""
        Then the value of the "Country" labelled nested reference field of the selected row in the table field is ""
