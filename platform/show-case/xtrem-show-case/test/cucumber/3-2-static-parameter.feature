Feature: 3-2-static-parameter
    # Tests the static parameter feature, verifying that predefined parameters can be properly used for selecting values in reference fields and other input components

    @isHidden
    Scenario: 1 - As an ATP XTreeM user I can write and select into a reference field using the static parameter
        XT-35081
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Reference"
        Then the "Field - Reference" titled page is displayed

        And the user selects the "field" bound reference field on the main page
        When the user writes "param:testUser" in the reference field
    #And takes a screenshot


    @isHidden
    Scenario: 2 - As an ATP XTreeM user I can write into a rich text field and control the value using the static parameter
        XT-35081
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/RichText"
        Then the "Field - Rich text" titled page is displayed

        And the user selects the "field" bound rich text field on the main page
        And the user clears the rich text field
        And the user writes "param:testUser" in the rich text field
        Then the value of the rich text field is "param:testUser"
        #And takes a screenshot

        #Hide the rich text field
        And the user selects the "Is hidden" labelled checkbox field on the main page
        And the user clicks in the checkbox field

        And the user selects the "field" bound rich text field on the main page
        Then the value of the rich text field is "param:testUser"
    #And takes a screenshot


    Scenario: 3 - As an ATP XTreeM user I can write into a text field and control the value using the static parameter
        XT-35081
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Text"
        Then the "Text" titled page is displayed

        And the user selects the "field" bound text field on the main page
        And the user writes "param:testUser" in the text field
        Then the value of the text field is "param:testUser"
        #And takes a screenshot

        #Hide the text field
        And the user selects the "Is hidden" labelled checkbox field on the main page
        And the user clicks in the checkbox field

        And the user selects the "field" bound text field on the main page
        Then the value of the text field is "param:testUser"
    #And takes a screenshot


    @isHidden
    Scenario: 4 - As an ATP XTreeM user I can write into a text area field and control the value using the static parameter
        XT-35081
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TextArea"
        Then the "Text area" titled page is displayed

        And the user selects the "field" bound text area field on the main page
        And the user writes "param:testUser" in the text area field
        And the user blurs the text area field
        Then the value of the text area field is "param:testUser"
        #And takes a screenshot

        #Hide the text area field
        And the user selects the "Is hidden" labelled checkbox field on the main page
        And the user clicks in the checkbox field

        And the user selects the "field" bound text area field on the main page
        Then the value of the text area field is "param:testUser"
