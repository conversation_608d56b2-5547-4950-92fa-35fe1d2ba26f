Feature: 2-2 Nested grid
    # Tests the nested grid component across different devices, verifying display options, data manipulation, and navigation within hierarchical grid structures

    Scenario Outline: <Device> - As a user I want to display or hide the title of the nested grid field
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/NestedGrid/eyJfaWQiOiI2In0="
        And the user selects the "field" bound nested grid field on the main page
        And the user selects the "Title" labelled text field on the main page
        When the user writes "this is the title" in the text field
        Then the title of the nested grid field is displayed
        And the title of the nested grid field is "this is the title"
        And the user selects the "Is title hidden" labelled checkbox field on the main page
        And the user clicks in the checkbox field
        Then the title of the nested grid field is hidden
        Examples:
            | Device  |
            | desktop |

    Scenario: As an app developer I want to add a new nested grid records with default values
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/NestedGrid/eyJfaWQiOiIzIn0="
        Then the "Field - NestedGrid" titled page is displayed
        When the user selects the "field" bound nested grid field on the main page
        And the selected nested grid field is displayed
        And the user selects the floating row of the selected nested grid field at level 1
        When the user clicks the "addNewOrder" bound button on the main page
        When the user selects the "field" bound nested grid field on the main page
        Then the user selects the row with the following content in the nested grid field
            | columnHeader | cellText   |
            | Id           | -2         |
            | Order Date   | 01/01/2025 |
            | Order Type   |            |
        When the user clicks the "addNewInvoice" bound button on the main page
        Then a toast containing text "A new invoice has been added to the order with ID -2" is displayed
        And the user dismisses all the toasts
        When the user clicks the "addNewInvoiceLine" bound button on the main page
        Then a toast containing text "A new invoice line has been added to the invoice with ID -3 under order with ID -2" is displayed
        And the user dismisses all the toasts
        And the user expands the selected row of the nested grid field
        Then a toast containing text "Expanded order with ID -2 and 1 children" is displayed
        And the user dismisses all the toasts
        When the user selects the "field" bound nested grid field on the main page
        Then the user selects the row with the following content in the nested grid field
            | columnHeader           | cellText   |
            | Customer               |            |
            | Id                     | -3         |
            | Total Product Quantity | 0          |
            | Purchase Date          | 01/01/2025 |
        And the user expands the selected row of the nested grid field
        Then a toast containing text "Expanded invoice with ID -3 and 1 children" is displayed
        And the user dismisses all the toasts
        When the user selects the "field" bound nested grid field on the main page
        Then the user selects the row with the following content in the nested grid field
            | columnHeader | cellText |
            | Id           | -4       |
            | Quantity     | 1        |
            | Net Price    | 0.00     |
            | Product      |          |

    Scenario: As a user I want to be able to add and remove rows from a nested grid field's nested level
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/NestedGrid/eyJfaWQiOiI3In0="
        Then the "Field - NestedGrid" titled page is displayed
        When the user selects the "activatableField" bound nested grid field on the main page
        And the selected nested grid field is displayed
        Then the user selects the row with the following content in the nested grid field
            | columnHeader | cellText   |
            | Id           | 106        |
            | Order Date   | 01/01/2021 |
        And the user expands the selected row of the nested grid field
        Then the user selects the row with the following content in the nested grid field
            | columnHeader  | cellText   |
            | Id            | 78         |
            | Purchase Date | 04/30/2020 |
        And the user expands the selected row of the nested grid field
        Then the nested grid field is not empty at level 3 with parent id 78
        Then the user selects the row with the following content in the nested grid field
            | columnHeader | cellText            |
            | Id           | 896                 |
            | Quantity     | 50                  |
            | Net Price    | 8,391.01            |
            | Product      | Wine - Toasted Head |
        And the user clicks the "Remove" action of the selected row in the nested grid field
        Then the nested grid field is empty at level 3 with parent id 78
        When the user selects the floating row of the selected nested grid field at level 3 with parent id 78
        And the user writes "10" in the "Quantity" labelled nested date field of the selected floating row in the nested grid field
        And the user presses Control+Enter
        Then the nested grid field is not empty at level 3 with parent id 78

    Scenario: As an app developer I want to add a new nested grid records with default values - mobile
        Given the user opens the application on a mobile using the following link: "@sage/xtrem-show-case/NestedGrid/eyJfaWQiOiIzIn0="
        Then the "Field - NestedGrid" titled page is displayed
        When the user clicks the "addNewOrder" bound button on the main page
        When the user selects the "field" bound nested grid field on the main page
        And the value of the "Id" labelled nested text field of the card 1 in the nested grid field is "-1"
        And the value of the "Order Date" labelled nested date field of the card 1 in the nested grid field is "01/01/2025"
        When the user clicks the "addNewInvoice" bound button on the main page
        Then a toast containing text "A new invoice has been added to the order with ID -1" is displayed
        And the user dismisses all the toasts
        When the user clicks the card 1 in the nested grid field
        Then the value of the "Id" labelled nested text field of the card 1 in the nested grid field is "-2"
        And the value of the "Purchase Date" labelled nested date field of the card 1 in the nested grid field is "01/01/2025"
        When the user clicks the "addNewInvoiceLine" bound button on the main page
        Then a toast containing text "A new invoice line has been added to the invoice with ID -2 under order with ID -1" is displayed
        And the user dismisses all the toasts
        When the user clicks the card 1 in the nested grid field
        Then the value of the "Id" labelled nested text field of the card 1 in the nested grid field is "-3"
        And the value of the "Quantity" labelled nested numeric field of the card 1 in the nested grid field is "1"

    Scenario Outline: <Device> - As a user I want to display or hide the helper text of the nested grid field
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/NestedGrid/eyJfaWQiOiI2In0="
        And the user selects the "field" bound nested grid field on the main page
        And the user selects the "Helper text" labelled text field on the main page
        When the user writes "this is the helper text" in the text field
        Then the helper text of the nested grid field is displayed
        And the helper text of the nested grid field is "this is the helper text"
        When the user selects the "Is helper text hidden" labelled checkbox field on the main page
        And the user clicks in the checkbox field
        Then the helper text of the nested grid field is hidden
        Examples:
            | Device  |
            | desktop |

    Scenario Outline: <Device> - As a user I want to display or hide the nested grid field
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/NestedGrid/eyJfaWQiOiI2In0="
        And the user selects the "field" bound nested grid field on the main page
        And the selected nested grid field is displayed
        When the user selects the "Is hidden" labelled checkbox field on the main page
        And the user clicks in the checkbox field
        Then the selected nested grid field is hidden
        Examples:
            | Device  |
            | desktop |


    Scenario Outline: <Device> - As a user I want to click on the dropdown action of a given row
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/NestedGrid/eyJfaWQiOiI2In0="
        And the user selects the "field" bound nested grid field on the main page
        When the user selects row with text "127" in column with header "Id" in the nested grid field
        And the user expands the selected row of the nested grid field
        When the user selects row with text "371" in column with header "Id" in the nested grid field
        #And the user expands the selected row of the nested grid field
        And the user clicks the "Row Action" action of the selected row in the nested grid field
        #@XT-38312
        Examples:
            | Device  |
            | desktop |

    Scenario Outline: <Device> - As a user I want to expand or collapse a given row
        XT-89822
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/NestedGrid/eyJfaWQiOiI2In0="
        And the user selects the "field" bound nested grid field on the main page
        When the user selects row with text "127" in column with header "Id" in the nested grid field

        # expand rows and check onLevelExpanded defined event is triggered and children are shown
        And the user expands the selected row of the nested grid field
        Then the value of the toast is "Expanded order with ID 127 and 8 children"
        When the user dismisses all the toasts
        And the user selects row with text "371" in column with header "Id" in the nested grid field
        And the user expands the selected row of the nested grid field
        Then the value of the toast is "Expanded invoice with ID 371 and 3 children"

        # collapse rows and check if the children are hidden
        When the user collapses the selected row of the nested grid field
        Then the row with text "892" in column with header "Id" in the nested grid field is hidden
        When the user selects row with text "127" in column with header "Id" in the nested grid field
        And the user collapses the selected row of the nested grid field
        Then the row with text "371" in column with header "Id" in the nested grid field is hidden
        Examples:
            | Device  |
            | desktop |

    Scenario: As a user I want to select or unselect the main checkbox of a given row
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/NestedGrid/eyJfaWQiOiI2In0="
        When the user selects the "field" bound nested grid field on the main page
        And the user selects row with text "127" in column with header "Id" in the nested grid field
        And the user ticks the main checkbox of the selected row in the nested grid field
        Then the main checkbox of the selected row in the nested grid field is checked
        And a toast containing text "Order with ID 127 (2021-01-01 | ) SELECTED." is displayed
        When the user unticks the main checkbox of the selected row in the nested grid field
        Then the main checkbox of the selected row in the nested grid field is unchecked
        And a toast containing text "Order with ID 127 (2021-01-01 | ) UNSELECTED." is displayed

    #@XT-38312
    Scenario: As a user I want to click on the dropdown action of a given row 2
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/NestedGrid/eyJfaWQiOiIxNSJ9"
        And the user selects the "field" bound nested grid field on the main page
        When the user selects row with text "64" in column with header "Id" in the nested grid field
        And the user expands the selected row of the nested grid field
        When the user selects row with text "375" in column with header "Id" in the nested grid field
        And the user expands the selected row of the nested grid field
        When the user clicks the "Row Action" action of the selected row in the nested grid field
        And the user clicks the "OK" button of the Custom dialog on the main page
        When the user selects row with text "16" in column with header "Id" in the nested grid field
        And the user expands the selected row of the nested grid field
        When the user selects row with text "320" in column with header "Id" in the nested grid field
        And the user expands the selected row of the nested grid field

    #@XT-38312
    Scenario: As a user I want to select a given row with data table
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/NestedGrid/eyJfaWQiOiIxNSJ9"
        And the user selects the "field" bound nested grid field on the main page
        When the user selects the row with the following content in the nested grid field
            | columnHeader | cellText   |
            | Id           | 64         |
            | Order Date   | 01/01/2021 |
        And the user expands the selected row of the nested grid field
        When the user selects the row with the following content in the nested grid field
            | columnHeader           | cellText   |
            | Id                     | 375        |
            | Total Product Quantity | 111        |
            | Purchase Date          | 08/03/2020 |
        And the user expands the selected row of the nested grid field
        When the user selects the row with the following content in the nested grid field
            | columnHeader | cellText         |
            | Id           | 709              |
            | Quantity     | 29               |
            | Net Price    | 7,897.67         |
            | Product      | Vodka - Smirnoff |
        When the user clicks the "Random Action" action of the selected row in the nested grid field

    # XT-59347
    Scenario: As a user I want a parent record to be selected if when all of its children are selected
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/NestedGrid/eyJfaWQiOiIxODYifQ=="
        And the user selects the "field" bound nested grid field on the main page
        And the user selects row with text "96" in column with header "Id" in the nested grid field
        When the user expands the selected row of the nested grid field
        And the user selects the row with the following content in the nested grid field
            | columnHeader           | cellText     |
            | Customer               | Jenkins-Dare |
            | Id                     | 323          |
            | Total Product Quantity | 0            |
            | Purchase Date          | 03/18/2020   |
        And the user ticks the main checkbox of the selected row in the nested grid field
        Then a toast containing text "Invoice with ID 323 (19 | 2020-03-18 | 0) SELECTED." is displayed
        When the user selects the row with the following content in the nested grid field
            | columnHeader           | cellText         |
            | Customer               | MacGyver-Pollich |
            | Id                     | 322              |
            | Total Product Quantity | 0                |
            | Purchase Date          | 03/09/2020       |
        And the user ticks the main checkbox of the selected row in the nested grid field
        Then a toast containing text "Invoice with ID 322 (4 | 2020-03-09 | 0) SELECTED." is displayed
        When the user selects the row with the following content in the nested grid field
            | columnHeader           | cellText             |
            | Customer               | Greenfelder and Sons |
            | Id                     | 10                   |
            | Total Product Quantity | 71                   |
            | Purchase Date          | 04/13/2020           |
        And the user ticks the main checkbox of the selected row in the nested grid field
        Then a toast containing text "Invoice with ID 10 (67 | 2020-04-13 | 71) SELECTED." is displayed
        And a toast containing text "Order with ID 96 (2021-01-01 | ) SELECTED." is displayed
        When the user expands the selected row of the nested grid field
        Then a toast containing text "Invoice Line with ID 142 (4438.81 | 25 | | 158) SELECTED." is displayed
        And a toast containing text "Invoice Line with ID 141 (4804.99 | 46 | | 481) SELECTED." is displayed
        When the user selects the row with the following content in the nested grid field
            | columnHeader | cellText         |
            | Id           | 142              |
            | Quantity     | 25               |
            | Net Price    | 4,438.81         |
            | Product      | Hinge W Undercut |
        And the user unticks the main checkbox of the selected row in the nested grid field
        Then a toast containing text "Invoice Line with ID 142 (4438.81 | 25 | | 158) UNSELECTED." is displayed
        And a toast containing text "Invoice with ID 10 (67 | 2020-04-13 | 71) UNSELECTED." is displayed
        And a toast containing text "Order with ID 96 (2021-01-01 | ) UNSELECTED." is displayed
        When the user selects the row with the following content in the nested grid field
            | columnHeader           | cellText             |
            | Customer               | Greenfelder and Sons |
            | Id                     | 10                   |
            | Total Product Quantity | 71                   |
            | Purchase Date          | 04/13/2020           |
        And the user ticks the main checkbox of the selected row in the nested grid field
        Then a toast containing text "Invoice Line with ID 142 (4438.81 | 25 | | 158) SELECTED." is displayed
        And a toast containing text "Invoice with ID 10 (67 | 2020-04-13 | 71) SELECTED." is displayed
        And a toast containing text "Order with ID 96 (2021-01-01 | ) SELECTED." is displayed

    Scenario Outline: <Device> - As a user I want to write and check the value of a nested grid cell
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/NestedGrid/eyJfaWQiOiI2In0="
        And the user selects the "field" bound nested grid field on the main page
        And the user selects row with text "127" in column with header "Id" in the nested grid field
        When the user writes "10/15/2021" in the "Order Date" labelled nested date field of the selected row in the nested grid field
        Then the value of the "Order Date" labelled nested date field of the selected row in the nested grid field is "10/15/2021"
        And the user expands the selected row of the nested grid field
        And the user selects row with text "472" in column with header "Id" in the nested grid field
        And the value of the "Total Product Quantity" labelled nested numeric field of the selected row in the nested grid field is "176"
        And the value of the "Purchase Date" labelled nested date field of the selected row in the nested grid field is "10/29/2019"
        And the user expands the selected row of the nested grid field
        And the user selects row with text "290" in column with header "Id" in the nested grid field
        And the value of the "Quantity" labelled nested numeric field of the selected row in the nested grid field is "86"
        And the value of the "Net Price" labelled nested numeric field of the selected row in the nested grid field is "5,041.27"
        And the value of the "Product" labelled nested reference field of the selected row in the nested grid field is "Turnip - Wax"
        When the user writes "50" in the "Quantity" labelled nested numeric field of the selected row in the nested grid field
        Then the value of the "Quantity" labelled nested numeric field of the selected row in the nested grid field is "50"
        When the user writes "1234.5" in the "Net Price" labelled nested numeric field of the selected row in the nested grid field
        Then the value of the "Net Price" labelled nested numeric field of the selected row in the nested grid field is "1,234.50"
        When the user writes "Watercress" in the "Product" labelled nested reference field of the selected row in the nested grid field
        Then the value of the "Product" labelled nested reference field of the selected row in the nested grid field is "Watercress"
        Examples:
            | Device  |
            | desktop |

    #@XT-38312
    Scenario: As a user I want the validation errors of the nested grid to show up on the grid row block
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/NestedGridValidation/eyJfaWQiOiIxODYifQ=="
        And the user selects the "field" bound nested grid field on the main page
        And the user selects row with text "96" in column with header "Id" in the nested grid field
        When the user expands the selected row of the nested grid field
        When the user selects the row with the following content in the nested grid field
            | columnHeader           | cellText   |
            | Id                     | 323        |
            | Total Product Quantity | 0          |
            | Purchase Date          | 03/18/2020 |
        When the user clicks the selected row of the nested grid field
        And the user selects the "Total Product Quantity" labelled numeric field on the detail panel
        Then the numeric field is valid
        When the user writes "-1" in the "Total Product Quantity" labelled nested numeric field of the selected row in the nested grid field
        And the user selects the "Total Product Quantity" labelled numeric field on the detail panel
        Then the numeric field is invalid
        And the user selects the "field" bound nested grid field on the main page
        And the nested grid field is invalid

    #@XT-38312
    Scenario: As a user I want the validation errors of the grid row block to show up on the nested grid
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/NestedGridValidation/eyJfaWQiOiIxODYifQ=="
        And the user selects the "field" bound nested grid field on the main page
        And the user selects row with text "96" in column with header "Id" in the nested grid field
        When the user expands the selected row of the nested grid field
        When the user selects the row with the following content in the nested grid field
            | columnHeader           | cellText   |
            | Id                     | 323        |
            | Total Product Quantity | 0          |
            | Purchase Date          | 03/18/2020 |
        When the user clicks the selected row of the nested grid field
        And the user selects the "Total Product Quantity" labelled numeric field on the detail panel
        Then the numeric field is valid
        When the user selects the "Total Product Quantity" labelled numeric field on the detail panel
        And the user writes "-1" in the numeric field
        And the user selects the "Total Product Quantity" labelled numeric field on the detail panel
        Then the numeric field is invalid
        And the user selects the "field" bound nested grid field on the main page
        And the nested grid field is invalid

    # BL: These tests are not clean, they don't restore the original data
    # #@XT-38312
    # Scenario: As a user I want server-side errors to show on a nested grid (grid row block edit)
    #     Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/NestedGridValidation/eyJfaWQiOiIxODYifQ=="
    #     And the user selects the "field" bound nested grid field on the main page
    #     And the user selects row with text "96" in column with header "Id" in the nested grid field
    #     When the user expands the selected row of the nested grid field
    #     When the user selects the row with the following content in the nested grid field
    #         | columnHeader           | cellText   |
    #         | Id                     | 323        |
    #         | Total Product Quantity | 0          |
    #         | Purchase Date          | 03/18/2020 |
    #     When the user clicks the selected row of the nested grid field
    #     And the user selects the "Purchase Date" labelled date field on the detail panel
    #     Then the date field is valid
    #     And the user writes "03/18/2023" in the date field
    #     When the user clicks the "Save" labelled business action button on the main page
    #     # Then a validation error message is displayed containing text
    #     # """
    #     # Validation errors
    #     # """
    #     And the user selects the "field" bound nested grid field on the main page
    #     # And the nested grid field is invalid
    #     # And the user selects the "Purchase Date" labelled date field on the detail panel
    #     # Then the date field is invalid
    #     # And the user writes "03/18/2020" in the date field
    #     When the user clicks the "Save" labelled business action button on the main page
    #     Then a toast containing text "Record updated" is displayed

    # #@XT-38312
    # Scenario: As a user I want server-side errors to show on a nested grid (grid edit)
    #     Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/NestedGridValidation/eyJfaWQiOiIxODYifQ=="
    #     And the user selects the "field" bound nested grid field on the main page
    #     And the user selects row with text "96" in column with header "Id" in the nested grid field
    #     When the user expands the selected row of the nested grid field
    #     When the user selects the row with the following content in the nested grid field
    #         | columnHeader           | cellText   |
    #         | Id                     | 323        |
    #         | Total Product Quantity | 0          |
    #         | Purchase Date          | 03/18/2023 |
    #     When the user clicks the selected row of the nested grid field
    #     And the user selects the "Purchase Date" labelled date field on the detail panel
    #     Then the date field is valid
    #     And the user writes "03/18/2023" in the "Purchase Date" labelled nested date field of the selected row in the nested grid field
    #     When the user clicks the "Save" labelled business action button on the main page
    #     # Then a validation error message is displayed containing text
    #     #  """
    #     # Validation errors
    #     # """
    #     # And the user selects the "Purchase Date" labelled date field on the detail panel
    #     # Then the date field is invalid
    #     # And the user writes "03/18/2020" in the "Purchase Date" labelled nested date field of the selected row
    #     When the user clicks the "Save" labelled business action button on the main page
    #     Then a toast containing text "Record updated" is displayed

    #@XT-38312
    Scenario: As a user I want client-side errors to show on a nested grid
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/NestedGridValidation/eyJfaWQiOiIxODYifQ=="
        And the user selects the "field" bound nested grid field on the main page
        And the user selects row with text "96" in column with header "Id" in the nested grid field
        When the user expands the selected row of the nested grid field
        When the user selects the row with the following content in the nested grid field
            | columnHeader           | cellText   |
            | Id                     | 10         |
            | Total Product Quantity | 71         |
            | Purchase Date          | 04/13/2020 |
        When the user expands the selected row of the nested grid field
        And the user selects the row with the following content in the nested grid field
            | columnHeader | cellText         |
            | Id           | 142              |
            | Quantity     | 25               |
            | Net Price    | 4,438.81         |
            | Product      | Hinge W Undercut |
        When the user writes "-2" in the "Quantity" labelled nested numeric field of the selected row in the nested grid field
        Then the user selects the "field" bound nested grid field on the main page
        And the nested grid field is invalid
        And the user selects the "Quantity" labelled numeric field on the detail panel
        Then the numeric field is invalid
        When the user clicks the "Save" labelled business action button on the main page
        Then a validation error message is displayed containing text
            """
Validation errors
            """
        And the user dismisses the validation error message
        And the user writes "25" in the "Quantity" labelled nested numeric field of the selected row in the nested grid field
        When the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed

    Scenario: As a user I want to select and unselect rows of a nested grid
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/NestedGrid/eyJfaWQiOiIxODYifQ=="
        And the user selects the "field" bound nested grid field on the main page
        And the user selects row with text "96" in column with header "Id" in the nested grid field
        When the user expands the selected row of the nested grid field
        When the user selects the row with the following content in the nested grid field
            | columnHeader           | cellText   |
            | Id                     | 10         |
            | Total Product Quantity | 71         |
            | Purchase Date          | 04/13/2020 |
        When the user expands the selected row of the nested grid field
        And the user ticks the main checkbox of the selected row in the nested grid field
        And the user selects the "Selected Operations" labelled text area field on the main page
        Then the value of the text area field is ""
        And the user selects the "Selected Invoices" labelled text area field on the main page
        Then the value of the text area field is "10"
        And the user selects the "Selected Invoice Lines" labelled text area field on the main page
        Then the value of the text area field is "141,142"
        When the user selects row with text "96" in column with header "Id" in the nested grid field
        And the user ticks the main checkbox of the selected row in the nested grid field
        And the user selects the "Selected Operations" labelled text area field on the main page
        Then the value of the text area field is "96"
        And the user selects the "Selected Invoices" labelled text area field on the main page
        Then the value of the text area field is "10,322,323"
        And the user selects the "Selected Invoice Lines" labelled text area field on the main page
        Then the value of the text area field is "141,142"
        When the user selects the row with the following content in the nested grid field
            | columnHeader | cellText         |
            | Id           | 142              |
            | Quantity     | 25               |
            | Net Price    | 4,438.81         |
            | Product      | Hinge W Undercut |
        When the user unticks the main checkbox of the selected row in the nested grid field
        And the user selects the "Selected Operations" labelled text area field on the main page
        Then the value of the text area field is ""
        And the user selects the "Selected Invoices" labelled text area field on the main page
        Then the value of the text area field is "322,323"
        And the user selects the "Selected Invoice Lines" labelled text area field on the main page
        Then the value of the text area field is "141"
        When the user selects row with text "96" in column with header "Id" in the nested grid field
        And the user ticks the main checkbox of the selected row in the nested grid field
        And the user selects the "Selected Operations" labelled text area field on the main page
        Then the value of the text area field is "96"
        And the user selects the "Selected Invoices" labelled text area field on the main page
        Then the value of the text area field is "10,322,323"
        And the user selects the "Selected Invoice Lines" labelled text area field on the main page
        Then the value of the text area field is "141,142"
        When the user unticks the main checkbox of the selected row in the nested grid field
        And the user selects the "Selected Operations" labelled text area field on the main page
        Then the value of the text area field is ""
        And the user selects the "Selected Invoices" labelled text area field on the main page
        Then the value of the text area field is ""
        And the user selects the "Selected Invoice Lines" labelled text area field on the main page
        Then the value of the text area field is ""

    Scenario: As a functional developer I want to be able to expand a level for a nested grid
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/NestedGrid/eyJfaWQiOiIxODYifQ=="
        And the user selects the "field" bound nested grid field on the main page
        And the user selects row with text "96" in column with header "Id" in the nested grid field
        When the user expands the selected row of the nested grid field
        When the user selects the row with the following content in the nested grid field
            | columnHeader           | cellText   |
            | Id                     | 10         |
            | Total Product Quantity | 71         |
            | Purchase Date          | 04/13/2020 |
        And the user expands the selected row of the nested grid field
        And the user selects the "Should open level on UI?" labelled checkbox field on the main page
        When the user ticks the checkbox field
        And the user selects the "Parent ID" labelled text field on the main page
        And the user writes "10" in the text field
        And the user selects the "Level" labelled numeric field on the main page
        And the user writes "2" in the numeric field
        And the user clicks in the "fetchLevelButton" bound button field on the main page
        When the user selects the row with the following content in the nested grid field
            | columnHeader | cellText         |
            | Id           | 142              |
            | Quantity     | 25               |
            | Net Price    | 4,438.81         |
            | Product      | Hinge W Undercut |
        Then the value of the "Quantity" labelled nested numeric field of the selected row in the nested grid field is "25"

    Scenario: As a user I want to be able to paginate nested grids
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/NestedGrid/eyJfaWQiOiIxODYifQ=="
        And the user selects the "field" bound nested grid field on the main page
        And the user selects row with text "42" in column with header "Id" in the nested grid field
        When the user expands the selected row of the nested grid field
        When the user clicks the next page button of the selected nested grid field at level 1 with parent id 42
        When the user selects the row with the following content in the nested grid field
            | columnHeader           | cellText   |
            | Id                     | 1          |
            | Total Product Quantity | 567        |
            | Purchase Date          | 06/25/2020 |
        And the user expands the selected row of the nested grid field
        When the user clicks the next page button of the selected nested grid field at level 2 with parent id 1
        Then the user selects the row with the following content in the nested grid field
            | columnHeader | cellText                   |
            | Id           | 713                        |
            | Quantity     | 23                         |
            | Net Price    | 5,463.12                   |
            | Product      | Lemonade - Natural, 591 Ml |
        When the user clicks the previous page button of the selected nested grid field at level 2 with parent id 1
        Then the user selects the row with the following content in the nested grid field
            | columnHeader | cellText             |
            | Id           | 1022                 |
            | Quantity     | 29                   |
            | Net Price    | 10,021.32            |
            | Product      | Vinegar - White Wine |
        When the user clicks the previous page button of the selected nested grid field at level 1 with parent id 42
        Then the user selects the row with the following content in the nested grid field
            | columnHeader           | cellText   |
            | Id                     | 529        |
            | Total Product Quantity | 0          |
            | Purchase Date          | 11/25/2022 |

    #@XT-38312
    Scenario: As a functional developer I want to be able to remap server-side records
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/NestedGridRemapped/eyJfaWQiOiIxODYifQ=="
        And the user selects the "field" bound nested grid field on the main page
        And the option menu of the nested grid field is displayed
        When the user clicks the option menu of the nested grid field
        And the user clicks the "Old orders" value in the option menu of the nested grid field
        And the user selects the "field" bound nested grid field on the main page
        And the user selects row with text "96" in column with header "Id" in the nested grid field

        And the user selects the "Set prefix to storage for level 1" labelled text field on the main page
        And the user writes "level1" in the text field
        And the user selects the "Set prefix to storage for level 2" labelled text field on the main page
        And the user writes "level2" in the text field
        And the user clicks in the "refreshTable" bound button field on the main page


        And the user selects the "field" bound nested grid field on the main page
        And the option menu of the nested grid field is displayed
        When the user clicks the option menu of the nested grid field
        And the user clicks the "Old orders" value in the option menu of the nested grid field
        And the user waits 2 seconds
        And the user selects the "field" bound nested grid field on the main page
        And the user selects row with text "96" in column with header "Id" in the nested grid field

        Then the value of the "Transient calculated" labelled nested text field of the selected row in the nested grid field is "level1 2021-01-01"
        When the user expands the selected row of the nested grid field
        When the user selects the row with the following content in the nested grid field
            | columnHeader           | cellText   |
            | Id                     | 10         |
            | Total Product Quantity | 71         |
            | Purchase Date          | 04/13/2020 |
        Then the value of the "Transient calculated" labelled nested text field of the selected row in the nested grid field is "level2 71"

    # TODO: Nested selector for nested dropdown list component no longer exists after refactor. Dropdown list now uses the same internal components
    #       as the nested select.
    # Scenario Outline: <Device> - As an ATP XTreeM user I can interact with nested drop-down list in nested grid
    #     Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/NestedGrid/eyJfaWQiOiI2In0="
    #     And the user selects the "field" bound nested grid field on the main page
    #     And the user selects row with text "127" in column with header "Id"
    #     When the user selects the "Online" value in the "Order Type" labelled nested drop-down list field of the selected row
    #     Then the value of the "Order Type" labelled nested drop-down list field of the selected row is "Online"
    #     Examples:
    #         | Device  |
    #         | desktop |
    #         | tablet  |
    #         | mobile  |

    #@XT-38312
    Scenario: As an ATP XTreeM user I can call the Refresh record action of nested grid row to retrieve the inital values from the server
        XT-19777 / XT-20927
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/NestedGrid/eyJfaWQiOiI2In0="
        And the user selects the "field" bound nested grid field on the main page
        And the user selects row with text "127" in column with header "Id" in the nested grid field
        When the user expands the selected row of the nested grid field
        And the user selects row with text "472" in column with header "Id" in the nested grid field
        When the user expands the selected row of the nested grid field
        And the user selects row with text "6" in column with header "Id" in the nested grid field
        #Verify nested field values before change
        And the value of the "Quantity" labelled nested numeric field of the selected row in the nested grid field is "90"
        And the value of the "Net Price" labelled nested numeric field of the selected row in the nested grid field is "4,748.94"
        And the value of the "Product" labelled nested reference field of the selected row in the nested grid field is "Ice - Clear, 300 Lb For Carving"
        #Change the nested field values
        When the user writes "50" in the "Quantity" labelled nested numeric field of the selected row in the nested grid field
        When the user writes "1234.5" in the "Net Price" labelled nested numeric field of the selected row in the nested grid field
        When the user writes "Watercress" in the "Product" labelled nested reference field of the selected row in the nested grid field
        #Verify nested field values after change
        Then the value of the "Quantity" labelled nested numeric field of the selected row in the nested grid field is "50"
        Then the value of the "Net Price" labelled nested numeric field of the selected row in the nested grid field is "1,234.50"
        Then the value of the "Product" labelled nested reference field of the selected row in the nested grid field is "Watercress"
        #Call refresh record action
        When the user clicks the "Refresh record" action of the selected row in the nested grid field
        And the user waits 5 seconds
        #Verify nested field values after refresh recrod action is done
        And the value of the "Quantity" labelled nested numeric field of the selected row in the nested grid field is "90"
        And the value of the "Net Price" labelled nested numeric field of the selected row in the nested grid field is "4,748.94"
        And the value of the "Product" labelled nested reference field of the selected row in the nested grid field is "Ice - Clear, 300 Lb For Carving"

    Scenario: As a user I want server-side errors to show on a nested grid (grid row block edit)
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/NestedGridValidation/eyJfaWQiOiIxODYifQ=="
        And the user selects the "field" bound nested grid field on the main page
        And the user selects row with text "42" in column with header "Id" in the nested grid field
        When the user expands the selected row of the nested grid field
        And the user selects row with text "1" in column with header "Id" in the nested grid field
        When the user expands the selected row of the nested grid field
        And the user selects the "field" bound nested grid field on the main page
        And the user clicks the "Product" labelled column of the nested grid field
        And the user selects row with text "353" in column with header "Id" in the nested grid field
        Then the test id "field-0-5" text content is "Apricots Fresh"
        Then the test id "field-1-5" text content is "Bacardi Raspberry"
        Then the test id "field-2-5" text content is "Beans - French"
        Then the test id "field-3-5" text content is "Boogies"

    Scenario: As an ATP XTreeM user I can verify the footer pagin information of the nested grid field
        XT-59072
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/NestedGrid/eyJfaWQiOiI5In0="
        And the user selects the "field" bound nested grid field on the main page
        Then the First page button of the nested grid field is disabled
        Then the Last page button of the nested grid field is disabled
        Then the Previous page button of the nested grid field is disabled
        Then the Next page button of the nested grid field is disabled
        Then the page number of the nested grid field is "Page 1 of 1"
        Then the summary row paging information of the nested grid field is "1 to 2 of 2"


    Scenario: As a user I want to be able to paginate nested grids on the intermediate levels
        XT-61413
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/NestedGrid/eyJfaWQiOiIxODYifQ=="

        And the user selects the "field" bound nested grid field on the main page
        And the user selects row with text "42" in column with header "Id" in the nested grid field
        When the user expands the selected row of the nested grid field

        Then the Previous page button of the nested grid field is disabled at level 1 with parent id 42
        Then the Next page button of the nested grid field is enabled at level 1 with parent id 42
        Then the First page button of the nested grid field is disabled at level 1 with parent id 42
        Then the Last page button of the nested grid field is disabled at level 1 with parent id 42
        Then the page number of the nested grid field is "Page 1 of more" at level 1 with parent id 42
        Then the summary row paging information of the nested grid field is "1 to 20 of more" at level 1 with parent id 42
        # And takes a screenshot

        When the user clicks the next page button of the selected nested grid field at level 1 with parent id 42

        Then the Previous page button of the nested grid field is enabled at level 1 with parent id 42
        Then the Next page button of the nested grid field is disabled at level 1 with parent id 42
        Then the First page button of the nested grid field is enabled at level 1 with parent id 42
        Then the Last page button of the nested grid field is disabled at level 1 with parent id 42
        Then the page number of the nested grid field is "Page 2 of 2" at level 1 with parent id 42
        Then the summary row paging information of the nested grid field is "21 to 32 of 32" at level 1 with parent id 42
        # And takes a screenshot

        And the user selects row with text "187" in column with header "Id" in the nested grid field
        And the user expands the selected row of the nested grid field

        Then the Previous page button of the nested grid field is disabled at level 2 with parent id 187
        Then the Next page button of the nested grid field is disabled at level 2 with parent id 187
        Then the First page button of the nested grid field is disabled at level 2 with parent id 187
        Then the Last page button of the nested grid field is disabled at level 2 with parent id 187
        Then the page number of the nested grid field is "Page 1 of 1" at level 2 with parent id 187
        Then the summary row paging information of the nested grid field is "1 to 3 of 3" at level 2 with parent id 187
    # And takes a screenshot


    # export TARGET_URL=https://cluster-ci.dev-sagextrem.com/
    # export loginUserName=<EMAIL>
    # export loginPassword=Sagetest01
    # export tenantName="[demo] Continuous Integration"

    # Scenario: As a user I want to click on the dropdown action of a given row
    #     Given the user opens the application on a desktop using the following link: "@sage/xtrem-technical-data/Formula/eyJfaWQiOiI4In0="
    #     And the "FORMULA" subtitled page is displayed
    #     When selects the "Formulation" labelled navigation anchor
    #     And the "Formulation" labelled navigation anchor is selected
    #     And the user selects the "steps" bound nested grid field on the main page
    #     And the user selects row with text "5" in column with header "Step number"
    #     And the user clicks the "Add" action of the selected row in the nested grid field

    # Scenario: As a user I want to click on the dropdown action of a given row 2
    #     Given the user opens the application on a desktop using the following link: "@sage/xtrem-technical-data/Routing/eyJfaWQiOiIzIn0="
    #     And the "Bottle gel Hydro formula 2" titled page is displayed
    #     And scrolls to the "Operations" labelled block
    #     And the user selects the "operations" bound nested grid field on the main page
    #     And the user selects row with text "5" in column with header "Operation number"
    #     And the user expands the selected row of the nested grid field
    #     And the user selects row with text "CUVE300L" in column with header "ID"
    #     And the user expands the selected row of the nested grid field
    #     And the user clicks the "Edit" action of the selected row in the nested grid field

    # Scenario: As a user I want to expand or collapse a given row
    #     Given the user opens the application on a desktop using the following link: "@sage/xtrem-technical-data/Routing/eyJfaWQiOiIzIn0="
    #     And the "Bottle gel Hydro formula 2" titled page is displayed
    #     And scrolls to the "Operations" labelled block
    #     And the user selects row with text "5" in column with header "Operation number"
    #     And the user expands the selected row of the nested grid field
    #     And the user collapses the selected row of the nested grid field

    # Scenario: As a user I want to select a given row with data table
    #     Given the user opens the application on a desktop using the following link: "@sage/xtrem-technical-data/Routing/eyJfaWQiOiIzIn0="
    #     And the "Bottle gel Hydro formula 2" titled page is displayed
    #     And scrolls to the "Operations" labelled block
    #     And the user selects row with the following content
    #         | columnHeader     | cellText       |
    #         | Operation number | 5              |
    #         | Name             | Bottle filling |
    #         | Setup time       | 25             |
    #     And the user expands the selected row of the nested grid field
    #     And the user selects row with the following content
    #         | columnHeader | cellText                          |
    #         | ID           | CUVE300L                          |
    #         | Name         | Cuve solution capacité 300 Litres |
    #         | Type         | MachineResource                   |
    #     And the user expands the selected row of the nested grid field
    #     And the user selects row with the following content
    #         | columnHeader | cellText           |
    #         | ID           | CHELAB             |
    #         | Name         | Chemical Operators |
    #         | Type         | GroupResource      |
    #         | Setup time   | 10                 |
    #     When the user clicks the "Edit" action of the selected row in the nested grid field

    Scenario: Nested grid component should handle ordered queries correctly
        XT-70237
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseCustomer/eyJfaWQiOiIxOSJ9"
        Then the "Customer Jenkins-Dare" titled page is displayed
        When the user selects the "invoices" bound nested grid field on the main page
        Then the user selects row with text "16300" in column with header "(Sort Value)" in the nested grid field
        When the user clicks the next page button of the selected nested grid field at level 0
        Then the user selects row with text "51800" in column with header "(Sort Value)" in the nested grid field
