Feature: 2-2 Multi reference
    # Tests the multi reference field component across different devices, verifying selection of multiple reference values and proper binding to data collections and control the multi reference state.

    Scenario Outline: <Device> - As an ATP XTreeM User I can select and verify the selected option of the multi reference field using label
        XT-25838
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/MultiReference"
        Then the "Field - Multi-reference" titled page is displayed
        Given the user selects the "Title" labelled text field on the main page
        When the user writes "Sample title" in the text field
        And the user presses Tab
        Given the user selects the "Sample title" labelled multi reference field on the main page
        When the user writes "Wine -" in the multi reference field
        And the user selects "Wine - Red<PERSON> Merritt | Wine - Sherry Dry Sack, William" in the multi reference field
        Then the value of the multi reference field is "Wine - Redchard Merritt|Wine - Sherry Dry Sack, William"
        And the user clears the multi reference field
        Examples:
            | Device  |
            | desktop |

    Scenario Outline: <Device> - As an ATP XTreeM User I can select and verify the selected option of the multi reference field using bind
        XT-25838
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/MultiReference"
        Then the "Field - Multi-reference" titled page is displayed
        Given the user selects the "field" bound multi reference field on the main page
        When the user writes "Beef - " in the multi reference field
        And the user selects "Beef - Tender Tips | Beef - Chuck, Boneless" in the multi reference field
        Then the value of the multi reference field is "Beef - Tender Tips|Beef - Chuck, Boneless"
        And the user clears the multi reference field
        Examples:
            | Device  |
            | desktop |

    Scenario Outline: <Device> - As an ATP XTreeM User I can scroll to the multi reference field
        XT-25838
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/MultiReference"
        Then the "Field - Multi-reference" titled page is displayed
        Given the user selects the "field" bound multi reference field on the main page
        When the user scrolls to the multi reference field
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario: As an ATP XTreeM User I can set the focus on the multi reference field
        XT-25838
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/MultiReference"
        Then the "Field - Multi-reference" titled page is displayed
        When the user clicks in the "focus" bound button field on the main page
        Given the user selects the "field" bound multi reference field on the main page
        Then the focus on the multi reference field is visible

    Scenario Outline: <Device> - As and ATP XTreeM user I can verify the multi reference field title
        XT-25838
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/MultiReference"
        Then the "Field - Multi-reference" titled page is displayed
        Given the user selects the "Title" labelled text field on the main page
        When the user writes "Sample title" in the text field
        And the user presses Tab
        Given the user selects the "field" bound multi reference field on the main page
        When the user clicks in the multi reference field
        Then the title of the multi reference field is "Sample title"
        Then the title of the multi reference field is displayed
        Given the user selects the "Is title hidden" labelled checkbox field on the main page
        When the user ticks the checkbox field
        Given the user selects the "field" bound multi reference field on the main page
        Then the title of the multi reference field is hidden
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> - As and ATP XTreeM user I can verify the multi reference field helper text
        XT-25838
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/MultiReference"
        Then the "Field - Multi-reference" titled page is displayed
        Given the user selects the "Helper text" labelled text field on the main page
        When the user writes "Sample text" in the text field
        And the user presses Tab
        Given the user selects the "field" bound multi reference field on the main page
        When the user clicks in the multi reference field
        Then the helper text of the multi reference field is "Sample text"
        Then the helper text of the multi reference field is displayed
        Given the user selects the "Is helper text hidden" labelled checkbox field on the main page
        When the user ticks the checkbox field
        Given the user selects the "field" bound multi reference field on the main page
        Then the helper text of the multi reference field is hidden
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> - As and ATP XTreeM user I can verify if the multi reference field is displayed or hidden
        XT-25838
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/MultiReference"
        Then the "Field - Multi-reference" titled page is displayed
        Given the user selects the "field" bound multi reference field on the main page
        When the user clicks in the multi reference field
        Then the "field" bound multi reference field on the main page is displayed
        Given the user selects the "Is hidden" labelled checkbox field on the main page
        When the user ticks the checkbox field
        Then the "field" bound multi reference field on the main page is hidden
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline:<Device> - As and ATP XTreeM user I can verify if the multi reference field is enabled or disabled
        XT-25838
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/MultiReference"
        Then the "Field - Multi-reference" titled page is displayed
        Given the user selects the "field" bound multi reference field on the main page
        Then the multi reference field is enabled
        Given the user selects the "Is disabled" labelled checkbox field on the main page
        When the user ticks the checkbox field
        Given the user selects the "field" bound multi reference field on the main page
        Then the multi reference field is disabled
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> - As and ATP XTreeM user I can verify if the multi reference field is read-only
        XT-25838
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/MultiReference"
        Then the "Field - Multi-reference" titled page is displayed
        Given the user selects the "Is readOnly" labelled checkbox field on the main page
        When the user ticks the checkbox field
        Given the user selects the "field" bound multi reference field on the main page
        Then the multi reference field is read-only
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario: As a user I want to select multiple values from a multi-reference field
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/MultiReference"
        Then the "Field - Multi-reference" titled page is displayed
        Given the user selects the "field" bound multi reference field on the main page
        When the user writes "Wine - " in the multi reference field
        Then at least the following list of options is displayed for the multi reference field: "Wine - Redchard Merritt | Wine - Sherry Dry Sack, William"
        And the user selects "Wine - Redchard Merritt | Wine - Sherry Dry Sack, William" in the multi reference field
        Then the value of the multi reference field is "Wine - Redchard Merritt|Wine - Sherry Dry Sack, William"

    Scenario: As a user I want to unselect selected values from a mutli-reference field
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/MultiReference"
        Then the "Field - Multi-reference" titled page is displayed
        Given the user selects the "field" bound multi reference field on the main page
        When the user writes "Wine - " in the multi reference field
        And the user selects "Wine - Redchard Merritt | Wine - Sherry Dry Sack, William" in the multi reference field
        Then the value of the multi reference field is "Wine - Redchard Merritt|Wine - Sherry Dry Sack, William"
        When the user writes "Wine - " in the multi reference field
        And the user selects "Wine - Sherry Dry Sack, William" in the multi reference field
        Then the value of the multi reference field is "Wine - Redchard Merritt"

    Scenario: As a functional dev I want my additional records to be in the lookup dialog for multi reference field
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/MultiReference"
        Then the "Field - Multi-reference" titled page is displayed
        Given the user selects the "Additional records in the lookup dialog" labelled multi reference field on the main page
        When the user clicks the lookup button of the multi reference field
        And the user selects the "additionalRecords" bound table field on a modal
        And the user selects the row 1 of the table field
        Then the value of the "Product" labelled nested text field of the selected row in the table field is "Some product"

    Scenario: As an developer I would like to set the lookup's title so that I can improve the user's experience
        Given the user opens the application on a mobile using the following link: "@sage/xtrem-show-case/MultiReference"
        Then the "Field - Multi-reference" titled page is displayed
        Given the user selects the "field" bound multi reference field on the main page
        When the user clicks the lookup button of the multi reference field
        Then the dialog title is "Select Products"

    Scenario: As a user I want to verify if min and max validations are working for the multi reference field
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/MultiReference"
        Then the "Field - Multi-reference" titled page is displayed
        When the user selects the "Title" labelled text field on the main page
        And the user writes "Multi-reference" in the text field
        When the user selects the "Set Minimum of Selectable Items" labelled numeric field on the main page
        And the user writes "2" in the numeric field
        And the user selects the "Set Maximum of Selectable Items" labelled numeric field on the main page
        And the user writes "4" in the numeric field
        And the user selects the "Multi-reference" labelled multi reference field on the main page
        And the user clicks in the multi reference field
        When the user writes "Spinach - " in the multi reference field
        And the user selects "Spinach - Baby" in the multi reference field
        And the "Multi-reference the minimum number of selectable items is 2" validation error message of the multi reference field is displayed
        Then the value of the multi reference field is "Spinach - Baby"
        When the user writes "Spinach - " in the multi reference field
        And the user selects "Spinach - Frozen" in the multi reference field
        And the "Multi-reference the minimum number of selectable items is 2" validation error message of the multi reference field is hidden
        Then the value of the multi reference field is "Spinach - Baby|Spinach - Frozen"
        When the user writes "Wine" in the multi reference field
        And the user selects "Rice Wine - Aji Mirin | Wine - Magnotta, White | Wine - Cahors Ac 2000, Clos" in the multi reference field
        Then the "Multi-reference the maximum number of selectable items is 4" validation error message of the multi reference field is displayed
        When the user clears the multi reference field
        And the user waits 1 second
        Then the "Multi-reference the minimum number of selectable items is 2" validation error message of the multi reference field is displayed

    Scenario: As a user I want the select all function to work as expected
        XT-92736
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/MultiReference"
        Then the "Field - Multi-reference" titled page is displayed
        When the user selects the "field" bound multi reference field on the main page
        And the user clicks the lookup button of the multi reference field
        And the user selects the "field" bound table field on a modal
        And the user selects all rows of the table field
        And the user selects the row 1 of the table field
        Then the selected row of the table field is selected
        And the user selects the row 2 of the table field
        And the selected row of the table field is selected
        And the user selects the row 3 of the table field
        And the selected row of the table field is selected
        And the user selects the row 4 of the table field
        And the selected row of the table field is selected
        And the user selects the row 5 of the table field
        And the selected row of the table field is selected
        And the user selects the row 2 of the table field
        When the user unticks the main checkbox of the selected row in the table field
        And the user selects the row 1 of the table field
        And the selected row of the table field is selected
        And the user selects the row 2 of the table field
        And the selected row of the table field is unselected
        And the user selects the row 3 of the table field
        And the selected row of the table field is selected
        And the user selects the row 4 of the table field
        And the selected row of the table field is selected
        And the user selects the row 5 of the table field
        And the selected row of the table field is selected
        When the user selects all rows of the table field
        And the user unselects all rows of the table field
        And the user selects the row 1 of the table field
        And the selected row of the table field is unselected
        And the user selects the row 2 of the table field
        And the selected row of the table field is unselected
        And the user selects the row 3 of the table field
        And the selected row of the table field is unselected
        And the user selects the row 4 of the table field
        And the selected row of the table field is unselected
        And the user selects the row 5 of the table field
        And the selected row of the table field is unselected
        When the user selects all rows of the table field
        And the user clicks the select action on the lookup dialog
        Then the value of the multi reference field contains the following: "Special Paimei rice"
        Then the value of the multi reference field contains the following: "Veal Inside - Provimi"
        And the user clicks the lookup button of the multi reference field
        And the user selects the "field" bound table field on a modal
        And the user selects the row 1 of the table field
        And the selected row of the table field is selected
        And the user selects the row 2 of the table field
        And the selected row of the table field is selected
        And the user selects the row 3 of the table field
        And the selected row of the table field is selected
        And the user selects the row 4 of the table field
        And the selected row of the table field is selected
        And the user selects the row 5 of the table field
        And the selected row of the table field is selected
        And the user selects the row 2 of the table field
        When the user unticks the main checkbox of the selected row in the table field
        And the user clicks the select action on the lookup dialog
        Then the value of the multi reference field contains the following: "Special Paimei rice"
        Then the value of the multi reference field does not contain the following: "Veal Inside - Provimi"
        And the user clicks the lookup button of the multi reference field
        And the user selects the "field" bound table field on a modal
        And the user selects the row 1 of the table field
        And the selected row of the table field is selected
        And the user selects the row 2 of the table field
        And the selected row of the table field is unselected
        And the user selects the row 3 of the table field
        And the selected row of the table field is selected
        And the user selects the row 5 of the table field
        And the selected row of the table field is selected

    Scenario: As a user I want to be able to select, cancel and close the lookup dialog
        XT-96804
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/MultiReference"
        Then the "Field - Multi-reference" titled page is displayed
        When the user selects the "field" bound multi reference field on the main page
        And the user clicks the lookup button of the multi reference field
        And the user selects the "field" bound table field on a modal
        And the user selects all rows of the table field
        And the user clicks the select action on the lookup dialog
        Then the value of the multi reference field contains the following: "Special Paimei rice"
        Then the value of the multi reference field contains the following: "Veal Inside - Provimi"
        And the user clicks the lookup button of the multi reference field
        And the user selects the "field" bound table field on a modal
        And the user selects the row 2 of the table field
        And the selected row of the table field is selected
        When the user unticks the main checkbox of the selected row in the table field
        And the selected row of the table field is unselected
        And the user clicks the select action on the lookup dialog
        Then the value of the multi reference field contains the following: "Special Paimei rice"
        Then the value of the multi reference field does not contain the following: "Veal Inside - Provimi"
        And the user clicks the lookup button of the multi reference field
        And the user selects the "field" bound table field on a modal
        And the user selects all rows of the table field
        And the user unselects all rows of the table field
        And the user selects the row 1 of the table field
        And the selected row of the table field is unselected
        When the user ticks the main checkbox of the selected row in the table field
        And the selected row of the table field is selected
        And the user selects the row 2 of the table field
        And the selected row of the table field is unselected
        When the user ticks the main checkbox of the selected row in the table field
        And the selected row of the table field is selected
        And the user clicks the select action on the lookup dialog
        Then the value of the multi reference field is "Spinach - Baby|Veal Inside - Provimi"
        And the user clicks the lookup button of the multi reference field
        And the user selects the "field" bound table field on a modal
        And the user selects the row 1 of the table field
        And the selected row of the table field is selected
        And the user selects the row 2 of the table field
        And the selected row of the table field is selected
        And the user selects the row 3 of the table field
        And the selected row of the table field is unselected
        When the user ticks the main checkbox of the selected row in the table field
        And the selected row of the table field is selected
        And the user clicks the cancel action on the lookup dialog
        Then the value of the multi reference field is "Spinach - Baby|Veal Inside - Provimi"
        And the user clicks the lookup button of the multi reference field
        And the user selects the "field" bound table field on a modal
        And the user selects the row 1 of the table field
        And the selected row of the table field is selected
        And the user selects the row 2 of the table field
        And the selected row of the table field is selected
        And the user selects the row 3 of the table field
        And the selected row of the table field is unselected
        When the user ticks the main checkbox of the selected row in the table field
        And the selected row of the table field is selected
        And the user clicks the Close button of the dialog on the main page
        Then the value of the multi reference field is "Spinach - Baby|Veal Inside - Provimi"
        And the user clicks the lookup button of the multi reference field
        And the user selects the "field" bound table field on a modal
        And the user selects the row 1 of the table field
        And the selected row of the table field is selected
        And the user selects the row 2 of the table field
        And the selected row of the table field is selected
        When the user unticks the main checkbox of the selected row in the table field
        And the selected row of the table field is unselected
        And the user clicks the select action on the lookup dialog
        Then the value of the multi reference field is "Spinach - Baby"
