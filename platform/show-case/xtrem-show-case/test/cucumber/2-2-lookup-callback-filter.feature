Feature: 2-2 Lookup callback filter
    # Tests reference field lookup with callback filtering functionality, verifying custom filtering logic applied to lookup dialog results

    <PERSON><PERSON><PERSON>: As a user, I want to search a reference lookup with a filter callback that returns hidden properties
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Reference"
        And the user selects the "restrictedResultByCallbackFilter" bound reference field on the main page
        When the user clicks the lookup button of the reference field
        And the user waits 1 seconds
        And the user selects the "restrictedResultByCallbackFilter" bound table field on a modal
        And the user filters the "product" bound column in the table field with value "Syrup - Chocolate"
        When the user selects the row 1 of the table field
        When the user clicks the "product" bound nested field of the selected row in the table field
        And the user selects the "restrictedResultByCallbackFilter" bound reference field on the main page
        Then the value of the reference field is "Syrup - Chocolate"

    #    Scenario: As a user I want to search on a reference lookup of a field that has a filter callback that returns a combinad filter statement with properties that are not displayed on the lookup table
    #       This scenario is to be implemented under XT-605
    #        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Reference"
    #        When the user clicks on the lookup button of the "restrictedResultByCombinedCallbackFilter" bound reference field on the main page
    #        And the user filters the column "Product" in the "restrictedResultByCombinedCallbackFilter" bound table field with value "Bag Stand" in a modal
    #        And the user clicks on the "product" bound nested text field of row 1 of the "restrictedResultByCombinedCallbackFilter" bound table field
    #        Then the value of the "restrictedResultByCombinedCallbackFilter" bound reference field on the main page is "Bag Stand"

    Scenario: As a user, I want to search a reference lookup with hidden filter properties
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Reference"
        And the user selects the "restrictedResultByFilter" bound reference field on the main page
        When the user clicks the lookup button of the reference field
        And the user selects the "restrictedResultByFilter" bound table field on a modal
        And the user filters the "product" bound column in the table field with value "Spinach - Baby"
        And the user selects the row 1 of the table field
        When the user clicks the "product" bound nested field of the selected row in the table field
        And the user selects the "restrictedResultByFilter" bound reference field on the main page
        Then the value of the reference field is "Spinach - Baby"

    Scenario: As a user, I want to search a filter select lookup with hidden filter properties
        Given the user opens the application on a mobile using the following link: "@sage/xtrem-show-case/FilterSelect"
        And the user selects the "restrictedResultByFilter" bound filter select field on the main page
        When the user clicks the lookup button of the filter select field
        And the user selects the "restrictedResultByFilter" bound table field on a full width modal
        And the user filters the mobile lookup table with "Spinach - Baby" query
        And the user selects the "restrictedResultByFilter" bound filter select field on the main page
        And the user selects "Spinach - Baby" in the filter select field
        Then the value of the filter select field is "Spinach - Baby"


    # Scenario: As a user I want to search on a filter select lookup of a field that has a filter callback that returns a combinad filter statement with properties that are not displayed on the lookup table
    #     Given the user opens the application on a mobile using the following link: "@sage/xtrem-show-case/FilterSelect"
    #     When the user clicks on the lookup button of the "restrictedResultByCombinedCallbackFilter" bound filter select field on the main page
    #     And the user filters "restrictedResultByCombinedCallbackFilter" bound mobile lookup table with "Bag Stand" query
    #     And selects "Bag Stand" in the "restrictedResultByCombinedCallbackFilter" bound filter select field on the main page
    #     Then the value of the "restrictedResultByCombinedCallbackFilter" bound filter select field on the main page is "Bag Stand"

    Scenario: As a user, I want to select and control the value in the Filter Select dropdown using a label on mobile
        Given the user opens the application on a mobile using the following link: "@sage/xtrem-show-case/FilterSelect"
        And the user selects the "restrictedResultByFilter" bound filter select field on the main page
        When the user clicks the lookup button of the filter select field
        And the user selects the "restrictedResultByFilter" bound table field on a full width modal
        And the user filters the mobile lookup table with "Salt - Table" query
        And the user selects the "restrictedResultByFilter" bound filter select field on the main page
        And the user selects "Salt - Table" in the filter select field
        Then the value of the filter select field is "Salt - Table"
