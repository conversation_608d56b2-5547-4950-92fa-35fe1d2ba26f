Feature: 4-2-workflow-designer-control-logs-and-variables


    Scenario: As an ATP / XTReeM user , I can trigger the workflow and control the logs and variables
        XT-90124
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-workflow/WorkflowDefinition"
        Then the "Workflows" titled page is displayed

        When the user selects the "Workflows" labelled table field on the main page
        And the user clicks the "Create" labelled business action button of the table field

        And the user selects the "Name" labelled text field on the main page
        And the user writes "SiteGroupWorkflow" in the text field

        When the user selects the "Workflow" labelled workflow designer field on the main page
        And the user clicks the "Add a trigger event" button in the workflow designer field

        #For workflow create
        When the user searches for "Entity cre" in the selection card on the Workflow Designer add sidebar
        When the user selects the "Entity created" selection card on the workflow designer add sidebar

        And the user clicks the "Next" button on the workflow designer add sidebar
        And the user selects the "Trigger title" labelled text field on the sidebar
        And the user writes "SiteGroupCreate" in the text field
        And the user selects the "Record type" labelled reference field on the sidebar
        And the user writes "Site" in the reference field
        And the user selects "Site group" in the reference field

        And the user clicks the "Confirm" labelled business action button on the sidebar

        When the user selects the titled "SiteGroupCreate" workflow node in the workflow designer field

        And the user clicks the "Add action" action of the workflow node in the workflow designer field

        #For workflow update
        When the user searches for "Update" in the selection card on the Workflow Designer add sidebar
        When the user selects the "Update entity" selection card on the workflow designer add sidebar

        And the user clicks the "Next" button on the workflow designer add sidebar
        And the user selects the "Action title" labelled text field on the sidebar
        And the user writes "SiteGroupUpdate" in the text field
        And the user clicks the "selectVariablesToUpdateButton" bound button on the sidebar
        And the user selects the "selectedProperties" bound node-browser-tree field on a modal
        And the user selects the tree-view element of level "1" with text "Name" in the node-browser-tree field
        And the user ticks the tree-view element in the node-browser-tree field

        And the user clicks the "OK" labelled business action button on a modal

        And the user selects the "New values" labelled dynamic-pod field on the sidebar
        And the user writes "MysiteGroup2" in the "Name" labelled nested dynamic-select field of the dynamic-pod field

        And the user clicks the "Confirm" labelled business action button on the sidebar

        When the user selects the titled "SiteGroupUpdate" workflow node in the workflow designer field

        And the user selects the "Status" labelled dropdown-list field on the main page
        When the user clicks in the dropdown-list field
        And the user selects "Production" in the dropdown-list field

        And the user clicks the "Save" labelled business action button on the main page

        And a toast containing text "Record created" is displayed


        #----------------------------------------------------Site Group creation----------------------------------------------------
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-authorization/SiteGroup"
        And the user waits 2 seconds

        And the user clicks the "Create" labelled business action button on the navigation panel


        And the user selects the "Active" labelled switch field on the main page
        And the user clicks in the switch field

        And the user selects the "Name" labelled text field on the main page
        And the user writes "MysiteGroup" in the text field

        And the user selects the "id" bound text field on the main page
        And the user writes "MysiteGroupID" in the text field

        And the user selects the "Site group" labelled multi reference field on the main page
        When the user writes "CAC" in the multi reference field
        And the user selects "CAC01" in the multi reference field

        And the user clicks the "Save" labelled business action button on the sidebar

        And a toast containing text "Record created" is displayed

        # #Control the workflow has update the site group Name
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-authorization/SiteGroup"
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row with text "MysiteGroupID" in the "ID" labelled column header of the table field
        Then the value of the "Name" labelled nested text field of the selected row in the table field is "MysiteGroup2"


        # -------------------------------------Workflow log - control and view detail ----------------------------------
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-workflow/WorkflowDefinition"
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row with text "SiteGroupWorkflow" in the "Name" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field

        Then the titled page containing "Workflow SiteGroupWorkflow" is displayed


        #Control the log
        And selects the "Log" labelled navigation anchor on the main page

        And the user selects the "Logs" labelled table field on the main page
        And the user selects the row with text "Success" in the "Status" labelled column header of the table field
        And the user ticks the main checkbox of the selected row in the table field

        #Control the Event log
        And the user selects the "Event log" labelled table field on the main page
        And the user selects the row with text "entity-created/_generic-1" in the "Step" labelled column header of the table field
        Then the value of the "Time stamp" labelled nested text field of the selected row in the table field is a non locale date with value "T"
        Then the value of the "Time stamp" labelled nested text field of the selected row in the table field is a non locale date with value "Y-M-T"
        Then the value of the "Status" labelled nested label field of the selected row in the table field is "Success"


        Then the value of the "Message" labelled nested text field of the selected row in the table field contains the pattern
            """
            "siteGroup":{"_id":"*","id":"MysiteGroupID","_updateTick":1}
            """

        Then the value of the "Message" labelled nested text field of the selected row in the table field contains the pattern
            """
            {
                "siteGroup": {
                    "_id": "*",
                    "id": "MysiteGroupID",
                    "_updateTick": 1
                }
            }
            """


        And the user selects the row with text "update-entity/_generic-1" in the "Step" labelled column header of the table field
        Then the value of the "Time stamp" labelled nested text field of the selected row in the table field is a non locale date with value "Y-M-T"
        Then the value of the "Status" labelled nested label field of the selected row in the table field is "Success"

        Then the value of the "Message" labelled nested text field of the selected row in the table field contains the pattern
            """
            "siteGroup":{"_id":"*","id":"MysiteGroupID"}
            """

        Then the value of the "Message" labelled nested text field of the selected row in the table field contains the pattern
            """
            {
                "siteGroup": {
                    "_id": "*",
                    "id": "MysiteGroupID"
                }
            }
            """

        #Control the variable
        And the user selects the "Variable details" labelled text area field on the main page

        Then the value of the text area field is
            """
            _id: 30
            siteGroup:
                    id: 'MysiteGroupID'
                    _id: '30'
                    _updateTick: 1
            _updateTick: 1
            """

        Then the value of the text area field contains
            """
            siteGroup:
                    id: 'MysiteGroupID'
            """


        #View details
        When the user selects the "Logs" labelled table field on the main page
        And the user selects the row with text "Success" in the "Status" labelled column header of the table field
        And the user ticks the main checkbox of the selected row in the table field
        And the user clicks the "View details" dropdown action of the selected row of the table field

        #view workflow node
        When the user selects the "Workflow" labelled workflow designer field on the main page
        And the user selects the titled "SiteGroupCreate" workflow node in the workflow designer field
        And the user clicks the "view" icon of the workflow node in the workflow designer field
        And takes a screenshot
