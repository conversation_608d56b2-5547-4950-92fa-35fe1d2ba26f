Feature: 3-1 Reference
    # Tests the reference field component, verifying selection, search functionality, value persistence, validation across different devices and control the reference field state.

    Scenario Outline: <Device> - As an ATP XTreeM User I can write select and verify the selected option of the reference field using label
        XT-25838
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Reference"
        Then the "Field - Reference" titled page is displayed

        Given the user selects the "Title" labelled text field on the main page
        When the user writes "Sample title" in the text field
        And the user presses Tab

        Given the user selects the "Sample title" labelled reference field on the main page
        When the user writes "Win" in the reference field
        And the user selects "Wine - Crozes Hermitage E." in the reference field
        Then the value of the reference field is "Wine - Crozes Hermitage E."
        And the user clears the reference field
        Examples:
            | Device  |
            | desktop |


    Scenario Outline: <Device> - As an ATP XTreeM User I can write select and verify the selected option of the reference field using bind
        XT-25838
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Reference"
        Then the "Field - Reference" titled page is displayed

        Given the user selects the "field" bound reference field on the main page
        When the user writes "Beef" in the reference field
        And the user selects "Beef - Tender Tips" in the reference field
        Then the value of the reference field is "Beef - Tender Tips"
        And the user clears the reference field
        Examples:
            | Device  |
            | desktop |

    #Reference field - Desktop mode - Select or write and control value
    Scenario: As a user I want to check values inside reference lookup dialog on desktop using bind
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Reference"
        Then the "Field - Reference" titled page is displayed

        Given the user selects the "field" bound reference field on the main page
        When the user clicks the lookup button of the reference field

        And the user selects the "field" bound table field on a modal
        And the user selects the row 1 of the table field
        Then the value of the "Product" labelled nested text field of the selected row in the table field is "Spinach - Baby"
        When the user clicks the "Product" labelled nested field of the selected row in the table field

        Then the value of the reference field is "Spinach - Baby"


    Scenario Outline: <Device> - As an ATP XTreeM User I verify the list of option of the reference field
        XT-64121
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Reference"
        Then the "Field - Reference" titled page is displayed

        Given the user selects the "field" bound reference field on the main page
        When the user writes "Bee" in the reference field
        Then at least the following list of options is displayed for the reference field:"Beef - Bresaola | Beef - Chuck, Boneless | Beef - Ground Lean Fresh"
        Examples:
            | Device  |
            | desktop |


    Scenario: As a user I want to set the value and control the helper text of the reference field on desktop using label
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Reference"
        Then the "Field - Reference" titled page is displayed

        Given the user selects the "Value" labelled reference field on the main page
        When the user writes "Win" in the reference field
        And the user selects "Wine - Crozes Hermitage E." in the reference field
        Then the value of the reference field is "Wine - Crozes Hermitage E."
        Then the helper text of the reference field is "multi-state"

    Scenario: As a user I want to filter within the lookup dialog
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Reference"
        Then the "Field - Reference" titled page is displayed

        Given the user selects the "field" bound reference field on the main page
        When the user clicks the lookup button of the reference field
        And the user selects the "field" bound table field on a modal
        And the user filters the "PRODUCT" labelled column in the table field with value "Spinach - Baby"
        And the user selects the row 1 of the table field
        Then the value of the "product" bound nested text field of the selected row in the table field is "Spinach - Baby"


    Scenario: As a user I want to set the value and the helper text from the lookup dialog
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Reference"
        Then the "Field - Reference" titled page is displayed

        Given the user selects the "field" bound reference field on the main page
        When the user clicks the lookup button of the reference field
        And the user selects the "field" bound table field on a modal
        And the user selects the row 1 of the table field
        When the user clicks the "description" bound nested field of the selected row in the table field

        Given the user selects the "field" bound reference field on the main page
        Then the value of the reference field is "Spinach - Baby"
        Then the helper text of the reference field is "Public-key"


    #Reference field - Tablet /Mobile mode - Select and control value

    Scenario Outline: <Device> - As a user I want to set the value and control the helper text of the reference field on mobile using bind
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Reference"
        Then the "Field - Reference" titled page is displayed

        Given the user selects the "field" bound reference field on the main page
        When the user clicks the lookup button of the reference field
        And the user selects "Bag Stand" in the reference field
        Then the value of the reference field is "Bag Stand"
        Then the helper text of the reference field is "encryption"
        Examples:
            | Device |
            | tablet |
            | mobile |

    Scenario: As a user I want to set the value and control the helper text of the reference field on mobile using label
        Then the "Field - Reference" titled page is displayed

        Given the user opens the application on a mobile using the following link: "@sage/xtrem-show-case/Reference"

        Given the user selects the "Value" labelled reference field on the main page
        When the user clicks the lookup button of the reference field
        And searches for "Win" in the lookup dialog
        Then a list of options is displayed for the reference field
        And the user selects "Chicken - Wings, Tip Off" in the reference field
        Then the value of the reference field is "Chicken - Wings, Tip Off"
        Then the helper text of the reference field is "Managed"


    Scenario Outline: <Device> - As an ATP XTreeM User I verify the reference table field in mobile mode is empty
        XT-64121
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Reference"
        Then the "Field - Reference" titled page is displayed

        Given the user selects the "field" bound reference field on the main page
        When the user writes "Incorrect" in the reference field
        And the user selects the "field" bound table field on a full width modal
        And the table field is empty
        Examples:
            | Device |
            | mobile |

    #Reference field - Tablet mode - Select and control value

    #    Scenario: As a user I want to set the value and control the helper text of the reference field on tablet using label
    #        Given the user opens the application on a tablet using the following link: "@sage/xtrem-show-case/Reference"
    #        When the user clicks on the lookup button of the "Value" labelled reference field on the main page
    #        And searches for "Win" in the lookup dialog
    #        Then a list of options is displayed for the "Value" labelled reference field on the main page
    #        And selects "Chicken Breast Wing On" in the "Value" labelled reference field on the main page
    #        Then the value of the "Value" labelled reference field on the main page is "Chicken Breast Wing On"
    #        And the helper text of the "Value" labelled reference field on the main page is "Stand-alone"

    Scenario Outline: <Device> - As an ATP XTreeM User I can scroll to and blur the reference field
        XT-25838
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Reference"
        Then the "Field - Reference" titled page is displayed

        Given the user selects the "field" bound reference field on the main page
        And the user scrolls to the reference field
        And the user blurs the reference field
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    # Reference field - set / check properties

    Scenario Outline: <Device> - As and ATP XTreeM user I can verify the reference field title
        XT-25838
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Reference"
        Then the "Field - Reference" titled page is displayed

        Given the user selects the "Title" labelled text field on the main page
        When the user writes "Sample title" in the text field
        And the user presses Tab

        Given the user selects the "field" bound reference field on the main page
        When the user clicks in the reference field
        Then the title of the reference field is "Sample title"
        Then the title of the reference field is displayed

        Given the user selects the "Is title hidden" labelled checkbox field on the main page
        When the user ticks the checkbox field

        Given the user selects the "field" bound reference field on the main page
        Then the title of the reference field is hidden
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |


    Scenario Outline: <Device> - As and ATP XTreeM user I can verify the reference field helper text
        XT-25838
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Reference"
        Then the "Field - Reference" titled page is displayed

        Given the user selects the "Helper text" labelled text field on the main page
        When the user writes "Sample text" in the text field
        And the user presses Tab

        Given the user selects the "field" bound reference field on the main page
        When the user clicks in the reference field
        Then the helper text of the reference field is "Sample text"
        Then the helper text of the reference field is displayed

        Given the user selects the "Is helper text hidden" labelled checkbox field on the main page
        When the user ticks the checkbox field

        Given the user selects the "field" bound reference field on the main page
        Then the helper text of the reference field is hidden
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |


    Scenario Outline: <Device> - As and ATP XTreeM user I can verify if the reference field is displayed or hidden
        XT-25838
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Reference"
        Then the "Field - Reference" titled page is displayed

        Given the user selects the "field" bound reference field on the main page
        When the user clicks in the reference field
        Then the "field" bound reference field on the main page is displayed

        Given the user selects the "Is hidden" labelled checkbox field on the main page
        When the user ticks the checkbox field
        Then the "field" bound reference field on the main page is hidden
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |


    Scenario Outline:<Device> - As and ATP XTreeM user I can verify if the reference field is enabled or disabled
        XT-25838
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Reference"
        Then the "Field - Reference" titled page is displayed

        Given the user selects the "field" bound reference field on the main page
        Then the reference field is enabled

        Given the user selects the "Is disabled" labelled checkbox field on the main page
        When the user ticks the checkbox field

        Given the user selects the "field" bound reference field on the main page
        Then the reference field is disabled
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |


    Scenario Outline: <Device> - As and ATP XTreeM user I can verify if the reference field is read-only
        XT-25838
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Reference"
        Then the "Field - Reference" titled page is displayed

        Given the user selects the "Is readOnly" labelled checkbox field on the main page
        When the user ticks the checkbox field

        Given the user selects the "field" bound reference field on the main page
        Then the reference field is read-only
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario: As a user I want to see suggestions displayed in the dropdown as I type a value
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Reference"
        Then the "Field - Reference" titled page is displayed

        Given the user selects the "Value" labelled reference field on the main page
        When the user writes "Win" in the reference field
        Then a list of options is displayed for the reference field


    Scenario: As a user I want the lookup dialog items to display in order using multiple sorting conditions
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Reference"
        Then the "Field - Reference" titled page is displayed

        And scrolls to the "Order block" labelled block

        Given the user selects the "orderedField2" bound reference field on the main page
        When the user clicks the lookup button of the reference field
        And the user selects the "orderedField2" bound table field on a modal
        And the user selects the row 1 of the table field
        Then the value of the "ID" labelled nested text field of the selected row in the table field is "2"
        Then the value of the "Description" labelled nested text field of the selected row in the table field is "zero tolerance"
        And the user selects the row 2 of the table field
        Then the value of the "ID" labelled nested text field of the selected row in the table field is "498"
        Then the value of the "Description" labelled nested text field of the selected row in the table field is "zero tolerance"

    # TO FIX
    # Scenario: As a user I want my nested nested reference fields to load data into a table
    #     Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TableNestedReference"
    #     And the user clicks on the record with the text "1" in the navigation panel
    #     Then the value of the "Provider" labelled nested reference field of row 1 in the "field" bound table field is "Ali Express"
    #     And the value of the "Provider" labelled nested reference field of row 2 in the "field" bound table field is "Amazon"
    #     And the value of the "Provider" labelled nested reference field of row 3 in the "field" bound table field is "Ali Express"
    #     Then the user clicks on the record with the text "2" in the navigation panel
    #     Then the value of the "Provider" labelled nested reference field of row 1 in the "field" bound table field is "Ali Express"
    #     Then the value of the "Provider" labelled nested reference field of row 4 in the "field" bound table field is "Amazon"

    ##Test Flacky: fail randomly on the last step definition: focus on the reference field is visible
    # Scenario: As a user I want the reference component to maintain focus when the lookup dialog is closed
    #     XT-4433
    #     Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Reference"
    #     Then the "Field - Reference" titled page is displayed

    #     Given the user selects the "field" bound reference field on the main page
    #     When the user clicks the lookup button of the reference field
    #     And the user clicks on the "description" bound nested text field of row 1 of the "field" bound table field in a modal

    #     Given the user selects the "field" bound reference field on the main page
    #     Then the focus on the reference field is visible

    Scenario: As a user I want to unset an auto-select enabled reference field's value
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Reference"
        Then the "Field - Reference" titled page is displayed

        Given the user selects the "Autoselect field value" labelled text field on the main page
        Then the value of the text field is "NO VALUE"

        Given the user selects the "Autoselect" labelled reference field on the main page
        When the user writes "qwer" in the reference field
        And the user blurs the reference field
        Then the value of the text field is "NO VALUE"
        Then the value of the reference field is ""

        When the user writes "Wine - Crozes" in the reference field
        And the user blurs the reference field
        Then the value of the text field is "Wine - Crozes Hermitage E."
        Then the value of the reference field is "Wine - Crozes Hermitage E."

        When the user clears the reference field
        And the user blurs the reference field
        Then the value of the text field is "NO VALUE"
        Then the value of the reference field is ""


    Scenario: As a user I want to unset the helper text of the reference field
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Reference"
        Then the "Field - Reference" titled page is displayed

        Given the user selects the "field" bound reference field on the main page
        Then the helper text of the reference field is hidden

        When the user writes "Win" in the reference field
        And the user selects "Wine - Crozes Hermitage E." in the reference field
        Then the value of the reference field is "Wine - Crozes Hermitage E."
        Then the helper text of the reference field is "multi-state"

        When the user clears the reference field
        Given the user selects the "Title" labelled text field on the main page
        When the user clicks in the text field
        Then the helper text of the reference field is hidden


    Scenario: As a user I want to see suggestions displayed in the dropdown as I type an integer value with multiplie results
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ReferenceWithInteger"
        Then the "With integer value field" titled page is displayed

        Given the user selects the "field" bound reference field on the main page
        When the user writes "13" in the reference field
        Then a list of options is displayed for the reference field

    Scenario: As a user I want to see suggestions displayed in the dropdown as I type an integer value with a single result
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ReferenceWithInteger"
        Then the "With integer value field" titled page is displayed

        Given the user selects the "field" bound reference field on the main page
        When the user writes "42" in the reference field
        Then a list of options is displayed for the reference field


    Scenario: As a user I want to set the value of a reference field by its helper text field
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Reference"
        Then the "Field - Reference" titled page is displayed

        Given the user selects the "Autoselect field value" labelled text field on the main page
        Then the value of the text field is "NO VALUE"

        Given the user selects the "Autoselect" labelled reference field on the main page
        When the user writes "3rd generation" in the reference field
        And the user blurs the reference field
        Then the value of the text field is "Bacardi Raspberry"
        Then the value of the reference field is "Bacardi Raspberry"


    Scenario: As a user I want to set the value of a reference field by its column content
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Reference"
        Then the "Field - Reference" titled page is displayed
        Given the user selects the "Suggestions from column data" labelled reference field on the main page
        When the user writes "3rd generation" in the reference field
        And the user blurs the reference field
        Then the value of the reference field is "Bacardi Raspberry"


    Scenario Outline: As a developer I would like to set the lookup's title so that I can improve the <Device> user's experience
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Reference"
        Then the "Field - Reference" titled page is displayed

        Given the user selects the "Restricted result set" labelled reference field on the main page
        When the user clicks the lookup button of the reference field
        Then the dialog title is "Test"
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> - As and ATP XTreeM user I can verify if the lookup of the reference field is displayed
        XT-25838
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Reference"
        Then the "Field - Reference" titled page is displayed

        Given the user selects the "field" bound reference field on the main page
        Then the lookup button of the reference field is displayed

        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |


    # ------------------- reference field tunnel tests ------------------------
    Scenario Outline: As a user I want to to access a reference field directly from the page I am working on
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/StandardShowCaseProduct/eyJfaWQiOiIzMTMifQ=="
        Then the "Product 313" titled page is displayed
        When the user selects the "provider" bound reference field on the main page
        Then the reference field tunnel link is displayed
        When the user clicks the reference field tunnel link
        Then an info dialog appears on a full width modal
        And the user waits 2 seconds
        And the dialog title is "Provider 2" on a full width modal
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |


    Scenario Outline: As a user I want to be reminded I cannot use an already opened tunnel
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/StandardShowCaseProduct/eyJfaWQiOiIxMDYifQ=="
        Then the "Product 106" titled page is displayed
        When the user selects the "provider" bound reference field on the main page
        Then the reference field tunnel link is displayed
        When the user clicks the reference field tunnel link
        Then an info dialog appears on a full width modal
        When the user waits 2 seconds
        Then the dialog title is "Provider 1" on a full width modal
        When the user selects the "Flagship Product" labelled reference field on a full width modal
        And the user clicks the lookup button of the reference field
        And the user waits 1 second
        And the user clicks the create button of the lookup dialog on the main page
        Then a warn dialog appears on the main page
        And the text in the body of the dialog is "This page is already used below, please close that one first to be able to open this link, or open it on a new tab."
        When the user clicks the "OK" button of the Confirm dialog
        Then the dialog title is "Provider 1" on a full width modal
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |


    Scenario: As a user I want to create a new value for a reference field using the create link button in the reference field lookup
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/StandardShowCaseProduct/eyJfaWQiOiIxMDYifQ=="
        Then the "Product 106" titled page is displayed
        When the user selects the "provider" bound reference field on the main page
        And the user clears the reference field
        And the user clicks the "Create new item" action link in the reference field lookup
        Then an info dialog appears on a full width modal
        And the user waits 2 seconds
        Then the dialog title is "Providers" on a full width modal

    Scenario Outline: <Device> - As a user I want to create a new value for a reference field using the create button in the reference field dialog
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/StandardShowCaseProduct/eyJfaWQiOiIxMDYifQ=="
        Then the "Product 106" titled page is displayed
        When the user selects the "provider" bound reference field on the main page
        And the user clicks the lookup button of the reference field
        Then an info dialog appears on the main page
        When the user clicks the create button of the lookup dialog on the main page
        Then an info dialog appears on a full width modal
        And the user waits 2 seconds
        Then the dialog title is "Provider" on a full width modal
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> - As a user I want to navigate through a tunnel using breadcrumbs
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/StandardShowCaseProduct/eyJfaWQiOiIxMDYifQ=="
        Then the "Product 106" titled page is displayed
        When the user selects the "provider" bound reference field on the main page
        Then the reference field tunnel link is displayed
        When the user clicks the reference field tunnel link
        Then an info dialog appears on a full width modal
        When the user waits 2 seconds
        Then the dialog title is "Provider 1" on a full width modal
        When the user selects the "Address" labelled reference field on a full width modal
        And the user clicks the reference field tunnel link
        Then an info dialog appears on a full width modal
        And the user waits 2 seconds
        When the user clicks the breadcrumb with text "Provider - Ali Express" on a full width modal
        And the user waits 2 seconds
        Then the dialog title is "Provider 1" on a full width modal
        When the user clicks the breadcrumb with text "Product 106" on a full width modal
        Then the "Product 106" titled page is displayed
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> - As a user I want to be reminded to save changes I have made to a reference field in a tunnel
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/StandardShowCaseProduct/eyJfaWQiOiIzMTMifQ=="
        Then the "Product 313" titled page is displayed
        When the user selects the "provider" bound reference field on the main page
        Then the reference field tunnel link is displayed
        When the user clicks the reference field tunnel link
        Then an info dialog appears on a full width modal
        And the user waits 2 seconds
        When the user selects the "address" labelled reference field on a full width modal
        And the user clears the reference field
        And the user clicks the breadcrumb with text "Product 313" on a full width modal
        Then a warn dialog appears on the main page
        And the text in the body of the dialog is "Leave and discard your changes?"
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

# Scenario: As a user I want to be add reference fields with default bindings
#     XT-62745
#     Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/StandardShowCaseProvider/eyJfaWQiOiIyIn0="
#     Then the "Provider 2" titled page is displayed
#     When the user selects the "flagshipProduct" bound reference field on the main page
#     And the user clears the reference field
#     And the user writes "Ketchup" in the reference field
#     And the user selects "Ketchup - Tomato" in the reference field
#     Then the value of the reference field is "Ketchup - Tomato"
#     When the user clicks the "Save" labelled business action button on the main page
#     And the user navigates to the following link: "@sage/xtrem-show-case/ReferenceWithDefault/eyJfaWQiOiIyIn0="
#     Then the "Reference Field (Default Bindings)" titled page is displayed
#     When the user selects the "field" bound reference field on the main page
#     Then the title of the reference field is "Show case product"
#     And the value of the reference field is "Ketchup - Tomato"
#     And the helper text of the reference field is "Ketchup - Tomato"
#     When the user selects the "fieldWithPartial" bound reference field on the main page
#     Then the title of the reference field is "Partially Custom Reference"
#     And the value of the reference field is "Ketchup - Tomato"
#     And the helper text of the reference field is "5610665154199831"
#     When the user selects the "fieldWithFull" bound reference field on the main page
#     Then the title of the reference field is "Fully Custom Reference"
#     And the value of the reference field is "Ketchup - Tomato"
#     And the helper text of the reference field is "5610665154199831"
