Feature: 1-2 Field Focus
    # Tests the focus behavior of different field types, ensuring programmatic focus and keyboard navigation work correctly across form components

    Scenario: As a application developer I want the text field's focus to work as expected
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Text"
        When the user clicks in the "focus" bound button field on the main page
        Then the element with the following selector is focused: "[data-testid='e-text-field e-field-bind-field'] input[id='Text-field']"

    Scenario: As a application developer I want the numeric field's focus to work as expected
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Numeric"
        When the user clicks in the "focus" bound button field on the main page
        Then the element with the following selector is focused: "[data-testid='e-numeric-field e-field-bind-field'] input[id='Numeric-field']"
