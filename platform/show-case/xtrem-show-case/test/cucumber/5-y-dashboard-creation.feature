Feature: 5 Y dashboard creation
    # Tests the dashboard creation process, including template selection, naming, cancellation, and initial dashboard setup

    @ClearDashboardsBefore
    Scenario: As a user I want to be able to cancel creating a new dashboard
        Given the user opens the application on a desktop
        And the dashboard page is displayed
        Then the "Create a dashboard to get started." subtitled empty dashboard is displayed
        When the user clicks the create button on the dashboard
        Then the dashboard creation dialog is displayed
        When the user clicks the "cancel" button in the dashboard creation dialog
        Then the "Create a dashboard to get started." subtitled empty dashboard is displayed

    @ClearDashboards
    Scenario: As a user I want to create the first dashboard using a blank template dashboard
        Given the user opens the application on a desktop
        And the dashboard page is displayed
        Then the "Create a dashboard to get started." subtitled empty dashboard is displayed
        When the user clicks the create button on the dashboard
        # the creation dialog
        Then the dashboard creation dialog is displayed
        And the dashboard creation dialog description is "Select a template to get started or build your own dashboard. You can customize any dashboard by adding or removing widgets."
        And the "Blank template" template in the dashboard creation dialog is displayed
        And the "next" button in the dashboard creation dialog is disabled
        And the "cancel" button in the dashboard creation dialog is enabled
        When the user selects the template with title "Blank template" in the dashboard creation dialog
        Then the template with title "Blank template" in the dashboard creation dialog is selected
        And the "next" button in the dashboard creation dialog is enabled
        When the user clicks the "next" button in the dashboard creation dialog
        # edit the new dashboard
        Then the "New dashboard" titled dashboard in the dashboard editor is displayed
        And the "Add a widget to customize your dashboard." subtitled blank dashboard is displayed
        And the "undo" button in the dashboard editor is disabled
        And the "redo" button in the dashboard editor is disabled
        And the "cancel" button in the dashboard editor footer is enabled
        And the "save" button in the dashboard editor footer is disabled
        When the user clicks the edit dashboard title icon
        And the user writes "First Dashboard" in the dashboard title
        And the user presses Enter
        Then the "First Dashboard" titled dashboard in the dashboard editor is displayed
        And the "undo" button in the dashboard editor is enabled
        When the user clicks the "undo" button in the dashboard editor
        Then the "New dashboard" titled dashboard in the dashboard editor is displayed
        And the "redo" button in the dashboard editor is enabled
        When the user clicks the "redo" button in the dashboard editor
        Then the "First Dashboard" titled dashboard in the dashboard editor is displayed
        # save the new dashboard
        When the user clicks the "save" button in the dashboard editor footer
        Then a toast containing text "Dashboard saved." is displayed
        And the user dismisses all the toasts
        # closing the dashboard editor
        Then the "First Dashboard" titled dashboard is displayed
        # adding a widget to the blank dashboard
        When the user clicks the create button on the dashboard
        Then the "First Dashboard" titled dashboard in the dashboard editor is displayed
        When the user clicks the "cancel" button in the dashboard editor footer
        And the user waits 1 second
        Then the "First Dashboard" titled dashboard is displayed

    @ClearDashboards
    Scenario: As a user I want to create multiple dashboards
        Given the user opens the application on a desktop
        Then the "Create a dashboard to get started." subtitled empty dashboard is displayed
        When the user clicks the create button on the dashboard
        Then the dashboard creation dialog is displayed
        When the user selects the template with title "Blank template" in the dashboard creation dialog
        And the user clicks the "next" button in the dashboard creation dialog
        Then the "New dashboard" titled dashboard in the dashboard editor is displayed
        When the user clicks the "cancel" button in the dashboard editor footer
        Then the "New dashboard" titled dashboard is displayed
        And the "create" labelled tab in the dashboard is hidden
        And the user waits 1 seconds
        When the user clicks the "create" labelled CRUD button in the dashboard action menu
        Then the dashboard creation dialog is displayed
        And the user waits 1 seconds
        When the user selects the template with title "Showcase dashboard" in the dashboard creation dialog
        And the user clicks the "next" button in the dashboard creation dialog
        Then the "Showcase dashboard" titled dashboard in the dashboard editor is displayed
        When the user clicks the "cancel" button in the dashboard editor footer
        Then the "Showcase dashboard" titled dashboard is displayed
        And the "newDashboard" labelled tab in the dashboard is displayed
        And the "showcaseDashboard" labelled tab in the dashboard is displayed
        And the "create" labelled tab in the dashboard is displayed

    @ClearDashboards
    Scenario: As a user I want to create a dashboard using the Create CRUD action button
        Given the user opens the application on a desktop
        And the dashboard page is displayed
        Then the "Create a dashboard to get started." subtitled empty dashboard is displayed
        When the user clicks the create button on the dashboard
        Then the dashboard creation dialog is displayed
        When the user selects the template with title "Blank template" in the dashboard creation dialog
        And the user clicks the "next" button in the dashboard creation dialog
        Then the "New dashboard" titled dashboard in the dashboard editor is displayed
        When the user clicks the edit dashboard title icon
        And the user writes "First dashboard" in the dashboard title
        And the user presses Enter
        Then the "First dashboard" titled dashboard in the dashboard editor is displayed
        When the user clicks the "save" button in the dashboard editor footer
        Then a toast containing text "Dashboard saved." is displayed
        And the user dismisses all the toasts
        And the user waits 1 second
        Then the "First dashboard" titled dashboard is displayed
        When the user clicks the "create" labelled CRUD button in the dashboard action menu
        Then the dashboard creation dialog is displayed
        When the user selects the template with title "Blank template" in the dashboard creation dialog
        And the user clicks the "next" button in the dashboard creation dialog
        Then the "New dashboard" titled dashboard in the dashboard editor is displayed
        When the user clicks the "cancel" button in the dashboard editor footer
        And the user waits 1 second
        Then the "New dashboard" titled dashboard is displayed
        And the "firstDashboard" labelled tab in the dashboard is displayed
        And the "newDashboard" labelled tab in the dashboard is displayed
