Feature: 4-2 Validation error messages
    # Tests the display and verification of validation error messages, ensuring proper presentation of form validation failures with detailed error information

    Sc<PERSON>rio: As an ATP XTreeM user I can verify the validation error message with exact match
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TableValidation/eyJfaWQiOiIyIn0="
        Then the "Field - Table Validations" titled page is displayed
        And the user selects the "Min Quantity" labelled numeric field on the main page
        And the user writes "1" in the numeric field
        And the user presses Tab
        And the user clicks the "Save" labelled business action button on the main page
        Then a validation error message is displayed with text
            """
            Validation errors
            You have 11 errors in the following grid: Table title.
            Id: 276 - Ordered Quantity: Enter a value greater than 1 in the  field.
            Id: 282 - Ordered Quantity: Enter a value greater than 1 in the  field.
            Id: 292 - Ordered Quantity: Enter a value greater than 1 in the  field.
            Id: 308 - Ordered Quantity: Enter a value greater than 1 in the  field.
            Id: 337 - Ordered Quantity: Enter a value greater than 1 in the  field.
            Id: 365 - Ordered Quantity: Enter a value greater than 1 in the  field.
            Id: 372 - Ordered Quantity: Enter a value greater than 1 in the  field.
            Id: 466 - Ordered Quantity: Enter a value greater than 1 in the  field.
            Id: 468 - Ordered Quantity: Enter a value greater than 1 in the  field.
            Id: 267 - Ordered Quantity: Enter a value greater than 1 in the  field.
            and 1 more error
            """
        # And takes a screenshot
        And the user dismisses the validation error message
    # And takes a screenshot


    Scenario: As an ATP XTreeM user I can verify the validation error message with contain
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TableValidation/eyJfaWQiOiIyIn0="
        Then the "Field - Table Validations" titled page is displayed
        And the user selects the "Min Quantity" labelled numeric field on the main page
        And the user writes "1" in the numeric field
        And the user presses Tab
        And the user clicks the "Save" labelled business action button on the main page
        Then a validation error message is displayed containing text
            """
            Validation errors
            You have 11 errors in the following grid: Table title.
            Id: 276 - Ordered Quantity: Enter a value greater than 1 in the  field
            """
        # And takes a screenshot
        And the user dismisses the validation error message
