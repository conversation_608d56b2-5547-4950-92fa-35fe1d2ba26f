Feature: 4-2 Table row
    # Tests the selection functionality of table rows, verifying that rows can be properly selected, unselected, expanded, collapsed, interact with dropdown row action and control its state, then interact with slected row inline action.


    Scenario: As an ATP XTreeM user I can verify if the row (using rowid) is selected or not selected
        XT-52580

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseProvider"
        Then the "Providers" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        When the user ticks the main checkbox of the selected row in the table field
        Then the selected row of the table field is selected
        When the user unticks the main checkbox of the selected row in the table field
        And the selected row of the table field is unselected


    <PERSON><PERSON>rio: As an ATP XTreeM user I can verify if the selected row is selected or not selected
        XT-52580

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseProvider"
        Then the "Providers" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row with text "Amazon" in the "Name" labelled column header of the table field
        When the user ticks the main checkbox of the selected row in the table field
        Then the selected row of the table field is selected
        When the user unticks the main checkbox of the selected row in the table field
        Then the selected row of the table field is unselected

    Scenario: As an ATP XTreeM user I can expand or collapse row (using rowid)
        XT-52580

        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-show-case/ShowCaseInvoice"
        Then the "Invoices" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        Then the value of the "customer__name" bound nested reference field of the selected row in the table field is "Schinner; Kling and Toy"
        Then the group by option in the header menu of the "purchaseDate" bound column of the table field is displayed
        When the user clicks the group by year option in the header menu of the "purchaseDate" bound column of the table field
        And the user selects the row 2 of the table field
        And the user expands the selected row of the table field
        And the user collapses the selected row of the table field


    Scenario: As an ATP XTreeM user I can expand or collapse selected row
        XT-52580

        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-show-case/ShowCaseInvoice"
        Then the "Invoices" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        Then the value of the "customer__name" bound nested reference field of the selected row in the table field is "Schinner; Kling and Toy"
        Then the group by option in the header menu of the "purchaseDate" bound column of the table field is displayed
        When the user clicks the group by year option in the header menu of the "purchaseDate" bound column of the table field
        And the user selects the row with text "2020 (340)" in the "purchaseDate" bound column header of the table field
        And the user expands the selected row of the table field
        And the user collapses the selected row of the table field

    Scenario: As an ATP XTreeM user I can verify if the dropdown action of the row (using rowid) is enabled or disabled (more action)
        XT-52580
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Table"
        And the user selects the "field" bound table field on the main page
        And the user selects the row 8 of the table field
        When the user writes "11" in the "qty" bound nested numeric field of the selected row in the table field
        Then the "Maybe Disabled" dropdown action of the selected row in the table field is enabled
        And a toast containing text "Beef - Bresaola" is displayed
        Then the user waits for 1 second
        When the user clicks the "Maybe Disabled" dropdown action of the selected row of the table field
        When the user writes "6" in the "qty" bound nested numeric field of the selected row in the table field
        Then the "Maybe Disabled" dropdown action of the selected row in the table field is disabled


    Scenario: As an ATP XTreeM user I can verify if the dropdown action of the selected row is enabled or disabled (more action)
        XT-52580
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Table"
        And the user selects the "field" bound table field on the main page
        And the user selects the row with text "Beef - Ground Lean Fresh" in the "Product" labelled column header of the table field
        When the user writes "11" in the "qty" bound nested numeric field of the selected row in the table field
        Then the "Maybe Disabled" dropdown action of the selected row in the table field is enabled
        When the user clicks the "Maybe Disabled" dropdown action of the selected row of the table field
        And a toast containing text "Beef - Ground Lean Fresh" is displayed
        When the user writes "6" in the "qty" bound nested numeric field of the selected row in the table field
        Then the "Maybe Disabled" dropdown action of the selected row in the table field is disabled


    Scenario: As an ATP XTreeM user I can verify if the dropdown action of the row (using rowid) is enabled  (single icon)
        XT-52580
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TableWithSidebar/eyJfaWQiOiIyIn0="
        Then the "Field - Table - With sidebar" titled page is displayed
        And the user selects the "field" bound table field on the main page
        And the user selects the row 1 of the table field
        Then the "Edit on sidebar" dropdown action of the selected row in the table field is enabled
        And the user clicks the "Edit on sidebar" dropdown action of the selected row of the table field
        And the "Appetiser - Bought" titled sidebar is displayed


    Scenario: As an ATP XTreeM user I can verify if the dropdown action of the selected row is enabled  (single icon)
        XT-52580
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TableWithSidebar/eyJfaWQiOiIyIn0="
        Then the "Field - Table - With sidebar" titled page is displayed
        And the user selects the "field" bound table field on the main page
        And the user selects the row with text "Appetiser - Bought" in the "Product" labelled column header of the table field
        Then the "Edit on sidebar" dropdown action of the selected row in the table field is enabled
        And the user clicks the "Edit on sidebar" dropdown action of the selected row of the table field
        And the "Appetiser - Bought" titled sidebar is displayed

    Scenario: As an ATP XTreeM user I can interact with the row (using rowid) inline action
        XT-52580
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/RowActions"
        Then the "Row actions" titled page is displayed
        And the user selects the "table9" bound table field on the main page
        When the user selects the row 1 of the table field
        And the "Delivery" inline action button of the selected row in the table field is displayed
        And the "Clock" inline action button of the selected row in the table field is displayed
        And the "Key" inline action button of the selected row in the table field is displayed
        # And takes a screenshot
        When the user hovers over the "Delivery" inline action button of the selected row in the table field
        Then the value of the inline action tooltip in the table field is "Delivery"
        When the user clicks the "Delivery" inline action button of the selected row in the table field
        Then a success dialog appears on the main page
    # And takes a screenshot


    Scenario: As an ATP XTreeM user I can interact with the selected row inline action
        XT-52580
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/RowActions"
        Then the "Row actions" titled page is displayed
        And the user selects the "table9" bound table field on the main page
        And the user selects the row with text "a" in the "Mandatory" labelled column header of the table field
        And the "Delivery" inline action button of the selected row in the table field is displayed
        And the "Clock" inline action button of the selected row in the table field is displayed
        And the "Key" inline action button of the selected row in the table field is displayed
        # And takes a screenshot
        When the user hovers over the "Delivery" inline action button of the selected row in the table field
        Then the value of the inline action tooltip in the table field is "Delivery"
        When the user clicks the "Delivery" inline action button of the selected row in the table field
        Then a success dialog appears on the main page
    # And takes a screenshot

    Scenario: As a user, I want to trigger a custom action on a table
        XT-93569
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TableMultiActionButton/eyJfaWQiOiIyIn0="
        Then the "Table - with multi action button" titled page is displayed
        When the user selects the "table" bound table field on the main page
        And the user clicks the "Custom add line action" labelled multi action of the table field
        Then a info toast containing text "Custom action is triggered!!" is displayed


    Scenario: As a user, I want to add a new row in sidebar when the add row in sidebar action is included in a multi-action dropdown
        XT-93569
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TableMultiActionButton/eyJfaWQiOiIyIn0="
        Then the "Table - with multi action button" titled page is displayed
        When the user selects the "tableWithSidebar" bound table field on the main page
        And the user clicks the "Add line in panel" labelled multi action of the table field
        Then the "Products with sidebar" titled sidebar is displayed
