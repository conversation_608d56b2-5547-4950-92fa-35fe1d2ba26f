Feature: 4-2 Workflow Designer
    # Tests the workflow designer component functionality, including creating new workflows, adding triggers and actions, and verifying the visual representation of workflow elements

    <PERSON><PERSON><PERSON>: As a user I want to select the workflow designer field and verify the text
        XT-90124
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-workflow/WorkflowDefinition"
        Then the "Workflows" titled page is displayed

        When the user selects the "Workflows" labelled table field on the main page
        And the user clicks the "Create" labelled business action button of the table field

        When the user selects the "Workflow" labelled workflow designer field on the main page
        Then the text content in the workflow designer field is "This workflow is currently empty"

    Scenario: As a user I want to add a trigger to the workflow designer field
        XT-90124
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-workflow/WorkflowDefinition"
        Then the "Workflows" titled page is displayed

        When the user selects the "Workflows" labelled table field on the main page
        And the user clicks the "Create" labelled business action button of the table field

        When the user selects the "Workflow" labelled workflow designer field on the main page
        And the user clicks the "Add a trigger event" button in the workflow designer field

        When the user selects the "Entity created" selection card on the workflow designer add sidebar
        And the user clicks the "Next" button on the workflow designer add sidebar
        And the user selects the "Trigger title" labelled text field on the sidebar
        And the user writes "Test 1" in the text field
        And the user selects the "Record type" labelled reference field on the sidebar
        And the user writes "Company" in the reference field
        And the user selects "Company" in the reference field

        And the user clicks the "Confirm" labelled business action button on the sidebar
        And the user waits 2 seconds

    Scenario: As a user I want to interact with the toolbar of the workflow designer field
        XT-90124
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-workflow/WorkflowDefinition"
        Then the "Workflows" titled page is displayed

        When the user selects the "Workflows" labelled table field on the main page
        And the user clicks the "Create" labelled business action button of the table field

        When the user selects the "Workflow" labelled workflow designer field on the main page
        And the user clicks the "Add a trigger event" button in the workflow designer field

        When the user selects the "Entity created" selection card on the workflow designer add sidebar
        And the user clicks the "Next" button on the workflow designer add sidebar
        And the user selects the "Trigger title" labelled text field on the sidebar
        And the user writes "Test 1" in the text field
        And the user selects the "Record type" labelled reference field on the sidebar
        And the user writes "Company" in the reference field
        And the user selects "Company" in the reference field

        And the user clicks the "Confirm" labelled business action button on the sidebar

        And the user clicks the "Zoom in" action in the toolbar of the workflow designer
        And the user clicks the "Zoom in" action in the toolbar of the workflow designer
        And the user clicks the "Zoom in" action in the toolbar of the workflow designer

        And the user clicks the "Zoom out" action in the toolbar of the workflow designer
        And the user clicks the "Zoom out" action in the toolbar of the workflow designer
        And the user clicks the "Zoom out" action in the toolbar of the workflow designer

        And the user clicks the "Fit view to screen" action in the toolbar of the workflow designer
        And the user waits 2 seconds

    Scenario: As a user I want to add and edit a trigger to the workflow designer field
        XT-90124
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-workflow/WorkflowDefinition"
        Then the "Workflows" titled page is displayed

        When the user selects the "Workflows" labelled table field on the main page
        And the user clicks the "Create" labelled business action button of the table field

        When the user selects the "Workflow" labelled workflow designer field on the main page
        And the user clicks the "Add a trigger event" button in the workflow designer field

        When the user selects the "Entity created" selection card on the workflow designer add sidebar
        And the user clicks the "Next" button on the workflow designer add sidebar
        And the user selects the "Trigger title" labelled text field on the sidebar
        And the user writes "Test 1" in the text field
        And the user selects the "Record type" labelled reference field on the sidebar
        And the user writes "Company" in the reference field
        And the user selects "Company" in the reference field

        And the user clicks the "Confirm" labelled business action button on the sidebar

        When the user selects the titled "Test 1" workflow node in the workflow designer field
        And the user clicks the "edit" icon of the workflow node in the workflow designer field
        And the user selects the "Trigger title" labelled text field on the sidebar
        And the user writes "Test 2" in the text field
        And the user clicks the "Confirm" labelled business action button on the sidebar
        And the user waits 2 seconds

    Scenario: As a user I want to click the add step action on a node to create a condition in the workflow designer field
        XT-90124
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-workflow/WorkflowDefinition"
        Then the "Workflows" titled page is displayed

        When the user selects the "Workflows" labelled table field on the main page
        And the user clicks the "Create" labelled business action button of the table field

        When the user selects the "Workflow" labelled workflow designer field on the main page
        And the user clicks the "Add a trigger event" button in the workflow designer field

        When the user selects the "Entity created" selection card on the workflow designer add sidebar
        And the user clicks the "Next" button on the workflow designer add sidebar
        And the user selects the "Trigger title" labelled text field on the sidebar
        And the user writes "Test 1" in the text field
        And the user selects the "Record type" labelled reference field on the sidebar
        And the user writes "Company" in the reference field
        And the user selects "Company" in the reference field

        And the user clicks the "Confirm" labelled business action button on the sidebar

        When the user selects the titled "Test 1" workflow node in the workflow designer field
        And the user clicks the "Add step" action of the workflow node in the workflow designer field

        When the user selects the "Condition" selection card on the workflow designer add sidebar
        And the user clicks the "Next" button on the workflow designer add sidebar
        And the user selects the "Condition title" labelled text field on the sidebar
        And the user writes "Condition 1" in the text field
        And the user clicks the "Confirm" labelled business action button on the sidebar

        When the user selects the titled "Condition 1" workflow node in the workflow designer field
        And the user waits 2 seconds

    Scenario: As a user I want to click the add action action on a node to create an action in the workflow designer field
        XT-90124
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-workflow/WorkflowDefinition"
        Then the "Workflows" titled page is displayed

        When the user selects the "Workflows" labelled table field on the main page
        And the user clicks the "Create" labelled business action button of the table field

        When the user selects the "Workflow" labelled workflow designer field on the main page
        And the user clicks the "Add a trigger event" button in the workflow designer field

        When the user selects the "Entity created" selection card on the workflow designer add sidebar
        And the user clicks the "Next" button on the workflow designer add sidebar
        And the user selects the "Trigger title" labelled text field on the sidebar
        And the user writes "Test 1" in the text field
        And the user selects the "Record type" labelled reference field on the sidebar
        And the user writes "Company" in the reference field
        And the user selects "Company" in the reference field

        And the user clicks the "Confirm" labelled business action button on the sidebar

        When the user selects the titled "Test 1" workflow node in the workflow designer field
        And the user clicks the "Add action" action of the workflow node in the workflow designer field

        When the user selects the "Update entity" selection card on the workflow designer add sidebar
        And the user clicks the "Next" button on the workflow designer add sidebar
        And the user selects the "Action title" labelled text field on the sidebar
        And the user writes "Action 1" in the text field
        And the user clicks the "selectVariablesToUpdateButton" bound button on the sidebar
        And the user selects the "selectedProperties" bound node-browser-tree field on a modal
        And the user selects the tree-view element of level "1" with text "ID" in the node-browser-tree field
        And the user ticks the tree-view element in the node-browser-tree field

        And the user clicks the "OK" labelled business action button on a modal

        And the user clicks the "Confirm" labelled business action button on the sidebar

        When the user selects the titled "Action 1" workflow node in the workflow designer field
        And the user waits 2 seconds

    Scenario: As a user I want to click the add condition and add an If true / else branch on the conditional workflow node
        XT-94760
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-workflow/WorkflowDefinition"
        Then the "Workflows" titled page is displayed
        When the user selects the "Workflows" labelled table field on the main page
        And the user clicks the "Create" labelled business action button of the table field
        When the user selects the "Workflow" labelled workflow designer field on the main page
        And the user clicks the "Add a trigger event" button in the workflow designer field

        When the user selects the "Entity created" selection card on the workflow designer add sidebar
        And the user clicks the "Next" button on the workflow designer add sidebar
        And the user selects the "Trigger title" labelled text field on the sidebar
        And the user writes "Test 1" in the text field
        And the user selects the "Record type" labelled reference field on the sidebar
        And the user writes "Company" in the reference field
        And the user selects "Company" in the reference field

        And the user clicks the "Confirm" labelled business action button on the sidebar
        When the user selects the titled "Test 1" workflow node in the workflow designer field
        And the user clicks the "Add condition" action of the workflow node in the workflow designer field
        And the user selects the "Condition title" labelled text field on the sidebar
        And the user writes "Condition 1" in the text field

        And the user selects the "addIfTrueBranch" labelled checkbox field on the sidebar
        And the user ticks the checkbox field
        And the user selects the "addIfFalseBranch" labelled checkbox field on the sidebar
        And the user ticks the checkbox field
        And the user clicks the "Confirm" labelled business action button on the sidebar

        When the user selects the titled "Condition 1" workflow node in the workflow designer field
        And the user clicks the "If true" left button of the workflow node in the workflow designer field
        And the user clicks the "Cancel" button on the workflow designer add sidebar
        And the user clicks the "else" right button of the workflow node in the workflow designer field
        And the user clicks the "Cancel" button on the workflow designer add sidebar
