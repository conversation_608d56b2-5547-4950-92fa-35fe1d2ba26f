Feature: 2-2 Monaco plugin
    # Tests the Monaco code editor plugin functionality, verifying code input, syntax highlighting, and proper integration with data storage

    Scenario: As a user I want to add source code to an existing monaco plugin
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/PluginMonaco/eyJfaWQiOiIxMDYifQ=="
        Then the user opens the navigation panel
        And the user searches for "386" in the navigation panel
        And the user clicks the record with the text "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON> and <PERSON>" in the navigation panel
        And the user selects the "Id" labelled text field on the main page
        Then the value of the text field is "386"
        Given the user selects the "notes" bound code editor field on the main page
        When the user writes "---------notes---------------" in the code editor field
        Given the user selects the "Code" labelled code editor field on the main page
        When the user writes "---This is a Monaco test--------" in the code editor field
        Then the "notes" bound code editor field on the main page is displayed
        Then the "Code" labelled code editor field on the main page is displayed

    Scenario: As a user I can verify if the code editor field is read-only
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/PluginMonaco/eyJfaWQiOiIxMDYifQ=="
        Then the user opens the navigation panel
        And the user searches for "386" in the navigation panel
        And the user clicks the record with the text "Abshire; Gislason and <PERSON>" in the navigation panel
        Given the user selects the "Is readOnly" labelled checkbox field on the main page
        When the user ticks the checkbox field
        Given the user selects the "notes" bound code editor field on the main page
        Then the code editor field is read-only

    # Scenario: As a user I want to make sure the popup suggestions contain the query properties that are defined by the Data Query
    #     Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/PluginMonaco/eyJfaWQiOiIxMDYifQ=="
    #     Then the user opens the navigation panel
    #     And the user searches for "386" in the navigation panel
    #     And the user clicks on the record with the text "Abshire; Gislason and Ortiz" in the navigation panel
    #     And the value of the "Id" labelled text field on the main page is "386"
    #     And the user clicks in the "language" bound select field on the main page
    #     Given the user selects the "language" bound select field on the main page
    #     And the user writes "handlebars" in the select field
    #     And the user selects "handlebars" in the select field
    #     Then the value of the select field is "handlebars"
    #     And the user waits 2 seconds
    #     Given the user selects the "notes" bound code editor field on the main page
    #     And the user inserts line 1 in the code editor field
    #     And the user inserts line 2 in the code editor field
    #     When the user writes "{{" to line 2 of the code editor field
    #     Then the suggestion on the code editor field is "xtremSystem.company"

    Scenario: As a an ATP XTreeM user I can write and verify the value of the code editor field
        XT-25838
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/PluginMonaco/eyJfaWQiOiIxMDYifQ=="
        Then the user opens the navigation panel
        And the user searches for "386" in the navigation panel
        And the user clicks the record with the text "Abshire; Gislason and Ortiz" in the navigation panel
        And the user selects the "Id" labelled text field on the main page
        Then the value of the text field is "386"
        Given the user selects the "Code" labelled code editor field on the main page
        Then the value of the code editor field is "transform intuitive vortals"
        When the user clears the code editor field
        When the user writes "---This is a Monaco test--------" in the code editor field
        And the user inserts line 1 in the code editor field
        And the user writes "test this" to line 2 of the code editor field
        Then the value of the code editor field is "---This is a Monaco test--------\ntest this"
        Then the value of the code editor field is "---This is a Monaco test-------- test this"
        When the user clears the code editor field

    Scenario: As an ATP XTreeM user I can verify the csv format of the import template
        XT-48704
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-import-export/ImportExportTemplate/eyJfaWQiOiIxIn0="
        When the user selects the "csvTemplateDisplay" bound code editor field on the main page
        Then the value of the code editor field is
            """
"!name";"*description";"*parentPackage";"activeTemplate";"reportType";"isFactory";"#variables";"!_sortValue";"*name";"title";"isMandatory";"type";"dataType";""
            """
