Feature: 3-2 Row actions
      # Tests the inline row action functionality in tables, verifying that contextual actions can be performed on specific rows

      Scenario: As a user I want to perform inline actions in a table where they are available
            Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/RowActions"
            Then the "Row actions" titled page is displayed
            And the user selects the "table9" bound table field on the main page
            And the user selects the row 1 of the table field
            And the "Delivery" inline action button of the selected row in the table field is displayed
            And the "Clock" inline action button of the selected row in the table field is displayed
            And the "Key" inline action button of the selected row in the table field is displayed
            When the user hovers over the "Delivery" inline action button of the selected row in the table field
            And the user waits 2 seconds
            Then the value of the inline action tooltip in the table field is "Delivery"
            When the user hovers over the "Clock" inline action button of the selected row in the table field
            And the user waits 2 seconds
            Then the value of the inline action tooltip in the table field is "Clock"
            When the user hovers over the "Key" inline action button of the selected row in the table field
            And the user waits 2 seconds
            Then the value of the inline action tooltip in the table field is "Key"
            And the user selects the row 2 of the table field
            When the user hovers over the "Delivery" inline action button of the selected row in the table field
            And the user waits 2 seconds
            Then the value of the inline action tooltip in the table field is "Delivery"
            When the user clicks the "Delivery" inline action button of the selected row in the table field
            And the user waits 2 seconds
            Then a success dialog appears on the main page
            When the user clicks the "OK" button of the Confirm dialog
            Then the "Row actions" titled page is displayed
            And the user selects the "table9" bound table field on the main page
            When the user selects the row 1 of the table field
            And the user clicks the "Key" inline action button of the selected row in the table field
            And the user waits 2 seconds
            Then a success dialog appears on the main page
