Feature: 1-2 Filter select
   # Tests the functionality of the filter select component across different devices, verifying user interactions such as selection, clearing, and validation of filter options

   # Filter select - Desktop -  Select & control value

   Scenario Outline: <Device> - As an ATP XTreeM User I can click than select and verify the selected option of the filter-select field using label
      XT-25838
      Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/FilterSelect"
      Then the "Field - Filter Select" titled page is displayed

      Given the user selects the "Title" labelled text field on the main page
      When the user writes "Sample title" in the text field
      And the user presses Tab

      Given the user selects the "Sample title" labelled filter select field on the main page
      When the user clicks in the filter select field
      And the user selects "Appetizer - Cheese Bites" in the filter select field
      Then the value of the filter select field is "Appetizer - Cheese Bites"
      And the user clears the filter select field
      Examples:
         | Device  |
         | desktop |


   Scenario Outline: <Device> - As an ATP XTreeM User I can click than select and verify the selected option of the filter-select field using bind
      XT-25838
      Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/FilterSelect"
      Then the "Field - Filter Select" titled page is displayed

      Given the user selects the "field" bound filter select field on the main page
      When the user clicks in the filter select field
      And the user selects "Appetizer - Southwestern" in the filter select field
      Then the value of the filter select field is "Appetizer - Southwestern"
      And the user clears the filter select field
      Examples:
         | Device  |
         | desktop |

   # Filter select - Desktop - write & control existing value

   Scenario Outline: <Device> - As an ATP XTreeM User I can write than select and verify the selected option of the filter-select field using label
      XT-25838
      Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/FilterSelect"
      Then the "Field - Filter Select" titled page is displayed

      Given the user selects the "Title" labelled text field on the main page
      When the user writes "Sample title" in the text field
      And the user presses Tab

      Given the user selects the "Sample title" labelled filter select field on the main page
      When the user writes "Veal Inside - Provimi" in the filter select field
      And the user selects "Veal Inside - Provimi" in the filter select field
      Then the value of the filter select field is "Veal Inside - Provimi"
      And the user clears the filter select field
      Examples:
         | Device  |
         | desktop |


   Scenario Outline: <Device> - As an ATP XTreeM User I can write than select and verify the selected option of the filter-select field using bind
      XT-25838
      Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/FilterSelect"
      Then the "Field - Filter Select" titled page is displayed

      Given the user selects the "field" bound filter select field on the main page
      When the user writes "Carbonated Water - Raspberry" in the filter select field
      And the user selects "Carbonated Water - Raspberry" in the filter select field
      Then the value of the filter select field is "Carbonated Water - Raspberry"
      And the user clears the filter select field
      Examples:
         | Device  |
         | desktop |


   # Filter select - Desktop - write & control new value

   Scenario Outline: <Device> - As an ATP XTreeM User I can write a new value in the filter-select field
      XT-25838
      Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/FilterSelect"
      Then the "Field - Filter Select" titled page is displayed

      Given the user selects the "Title" labelled text field on the main page
      When the user writes "Sample title" in the text field
      And the user presses Tab


      Given the user selects the "Sample title" labelled filter select field on the main page
      When the user writes "Yumi tuna salad" in the filter select field
      And the user selects "Yumi tuna salad (New)" in the filter select field
      Examples:
         | Device  |
         | desktop |


   # Filter select - Tablet / mobile -  Select & control value
   Scenario Outline:  <Device> - As an ATP XTreeM User I can click than select and verify the selected option of the filter-select field using label
      XT-25838
      Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/FilterSelect"
      Then the "Field - Filter Select" titled page is displayed

      Given the user selects the "Title" labelled text field on the main page
      When the user writes "Sample title" in the text field
      And the user presses Tab

      Given the user selects the "Sample title" labelled filter select field on the main page
      When the user clicks the lookup button of the filter select field
      And the user selects "Appetizer - Crab And Brie" in the filter select field
      Then the value of the filter select field is "Appetizer - Crab And Brie"
      Examples:
         | Device |
         | tablet |
         | mobile |


   Scenario Outline:  <Device> - As an ATP XTreeM User I can click than select and verify the selected option of the filter-select field using bind
      XT-25838
      Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/FilterSelect"
      Then the "Field - Filter Select" titled page is displayed

      Given the user selects the "field" bound filter select field on the main page
      When the user clicks the lookup button of the filter select field
      And the user selects "Appetiser - Bought" in the filter select field
      Then the value of the filter select field is "Appetiser - Bought"
      Examples:
         | Device |
         | tablet |
         | mobile |


   # Filter select - Mobile mode - write & control existing value

   Scenario Outline: <Device> - As an ATP XTreeM User I can write than select and verify the selected option of the filter-select field using label
      XT-25838
      Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/FilterSelect"
      Then the "Field - Filter Select" titled page is displayed

      Given the user selects the "Filter Select Field" labelled filter select field on the main page
      When the user writes "Gelatine Leaves" in the filter select field
      And the user selects "Gelatine Leaves - Bulk" in the filter select field
      Then the value of the filter select field is "Gelatine Leaves - Bulk"
      And the user clears the filter select field
      Examples:
         | Device |
         | tablet |
         | mobile |


   Scenario Outline: <Device> - As an ATP XTreeM User I can write than select and verify the selected option of the filter-select field using bind
      XT-25838
      Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/FilterSelect"
      Then the "Field - Filter Select" titled page is displayed

      Given the user selects the "field" bound filter select field on the main page
      When the user writes "Veal Inside" in the filter select field
      And the user selects "Veal Inside - Provimi" in the filter select field
      Then the value of the filter select field is "Veal Inside - Provimi"
      And the user clears the filter select field
      Examples:
         | Device |
         | tablet |
         | mobile |


   Scenario Outline: <Device> - As an ATP XTreeM User I verify the list of option of the filter select field
      XT-64121
      Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/FilterSelect"
      Then the "Field - Filter Select" titled page is displayed

      Given the user selects the "field" bound filter select field on the main page
      When the user clicks in the filter select field
      Then at least the following list of options is displayed for the filter select field:"Anisette - Mcguiness | Appetiser - Bought | Appetizer - Cheese Bites"
      When the user clears the filter select field

      And the user writes "Seabass" in the filter select field
      Then at least the following list of options is displayed for the filter select field:"Seabass (New)"
      Examples:
         | Device  |
         | desktop |

   # Filter select - Tablet / mobile - write & control new value

   Scenario Outline: <Device> - As an ATP XTreeM User I can write a new value in the filter-select field
      XT-25838
      Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/FilterSelect"
      Then the "Field - Filter Select" titled page is displayed

      Given the user selects the "Filter Select Field" labelled filter select field on the main page
      When the user clicks the lookup button of the filter select field
      And searches for "SaladYM" in the lookup dialog
      And the user selects "SaladYM (New)" in the filter select field
      Examples:
         | Device |
         | tablet |
         | mobile |


   Scenario Outline: <Device> - As an ATP XTreeM User I can scroll to the filter select field
      XT-25838
      Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/FilterSelect"
      Then the "Field - Filter Select" titled page is displayed

      Given the user selects the "field" bound filter select field on the main page
      And the user scrolls to the filter select field
      Examples:
         | Device  |
         | desktop |
         | tablet  |
         | mobile  |

   Scenario: As an ATP XTreeM User I can set the focus on the filter select field
      XT-25838
      Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/FilterSelect"
      Then the "Field - Filter Select" titled page is displayed

      When the user clicks in the "focus" bound button field on the main page
      Given the user selects the "field" bound filter select field on the main page
      Then the focus on the filter select field is visible


   # Filter select Field: Set / check properties

   Scenario Outline: <Device> - As and ATP XTreeM user I can verify the filter select field title
      XT-25838
      Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/FilterSelect"
      Then the "Field - Filter Select" titled page is displayed

      Given the user selects the "Title" labelled text field on the main page
      When the user writes "Sample title" in the text field
      And the user presses Tab

      Given the user selects the "field" bound filter select field on the main page
      When the user clicks in the filter select field
      Then the title of the filter select field is "Sample title"
      Then the title of the filter select field is displayed

      Given the user selects the "Is Title Hidden" labelled checkbox field on the main page
      When the user ticks the checkbox field

      Given the user selects the "field" bound filter select field on the main page
      Then the title of the filter select field is hidden
      Examples:
         | Device  |
         | desktop |
         | tablet  |
         | mobile  |


   Scenario Outline: <Device> - As and ATP XTreeM user I can verify the filter select field helper text
      XT-25838
      Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/FilterSelect"
      Then the "Field - Filter Select" titled page is displayed

      Given the user selects the "Helper Text" labelled text field on the main page
      When the user writes "Sample text" in the text field
      And the user presses Tab

      Given the user selects the "field" bound filter select field on the main page
      When the user clicks in the filter select field
      Then the helper text of the filter select field is "Sample text"
      Then the helper text of the filter select field is displayed

      Given the user selects the "Is Helper Text Hidden" labelled checkbox field on the main page
      When the user ticks the checkbox field

      Given the user selects the "field" bound filter select field on the main page
      Then the helper text of the filter select field is hidden
      Examples:
         | Device  |
         | desktop |
         | tablet  |
         | mobile  |


   Scenario Outline: <Device> - As and ATP XTreeM user I can verify if the filter select field is displayed or hidden
      XT-25838
      Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/FilterSelect"
      Then the "Field - Filter Select" titled page is displayed

      Given the user selects the "field" bound filter select field on the main page
      When the user clicks in the filter select field
      Then the "field" bound filter select field on the main page is displayed

      Given the user selects the "Is Hidden" labelled checkbox field on the main page
      When the user ticks the checkbox field
      Then the "field" bound filter select field on the main page is hidden
      Examples:
         | Device  |
         | desktop |
         | tablet  |
         | mobile  |


   Scenario Outline:<Device> - As and ATP XTreeM user I can verify if the filter select field is enabled or disabled
      XT-25838
      Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/FilterSelect"
      Then the "Field - Filter Select" titled page is displayed

      Given the user selects the "field" bound filter select field on the main page
      Then the filter select field is enabled

      Given the user selects the "Is Disabled" labelled checkbox field on the main page
      When the user ticks the checkbox field

      Given the user selects the "field" bound filter select field on the main page
      Then the filter select field is disabled
      Examples:
         | Device  |
         | desktop |
         | tablet  |
         | mobile  |


   Scenario Outline: <Device> - As and ATP XTreeM user I can verify if the filter select field is read-only
      XT-25838
      Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/FilterSelect"
      Then the "Field - Filter Select" titled page is displayed

      Given the user selects the "Is Read-Only" labelled checkbox field on the main page
      When the user ticks the checkbox field

      Given the user selects the "field" bound filter select field on the main page
      Then the filter select field is read-only
      Examples:
         | Device  |
         | desktop |
         | tablet  |
         | mobile  |


   Scenario: Set existing value into filter select field using functional code on mobile
      Given the user opens the application on a mobile using the following link: "@sage/xtrem-show-case/FilterSelect"
      Then the "Field - Filter Select" titled page is displayed

      When the user clicks the "setExistingValue" bound button on the main page

      Given the user selects the "field" bound filter select field on the main page
      Then the value of the filter select field is "Spinach - Baby"

   Scenario: Set new value into filter select field using functional code on mobile
      Given the user opens the application on a mobile using the following link: "@sage/xtrem-show-case/FilterSelect"
      When the user selects the "field" bound filter select field on the main page
      Then the value of the filter select field is ""

      When the user clicks the "setNewValue" bound button on the main page
      And the user selects the "field" bound filter select field on the main page
      Then the value of the filter select field is "Hola"

   Scenario: As an ATP XTreeM user I want to unset the value of a filter select field with min lookup characters greater than zero
      Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/FilterSelect"
      Then the "Field - Filter Select" titled page is displayed

      And the user selects the "With 3 min lookup characters value" labelled text field on the main page
      Then the value of the text field is "NO VALUE"

      When the user selects the "With 3 min lookup characters" labelled filter select field on the main page
      And the user writes "Apple" in the filter select field
      And the user selects "Juice - Apple Cider" in the filter select field

      When the user selects the "With 3 min lookup characters value" labelled text field on the main page
      Then the value of the text field is "Juice - Apple Cider"
      And the user clears the filter select field

      When the user selects the "With 3 min lookup characters value" labelled text field on the main page
      Then the value of the text field is "NO VALUE"


   Scenario: As a developer I want to prevent a XSS attack for a filter-select on desktop
      Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/FilterSelect"
      Then the "Field - Filter Select" titled page is displayed

      Given the user selects the "Filter Select Field" labelled filter select field on the main page
      When the user writes "XSS<details/open/ontoggle=alert(document.location)>" in the filter select field

      Given the user selects the "field" bound filter select field on the main page
      Then the value of the filter select field is "XSS<details/open/ontoggle=alert(document.location)>"
      When the user clicks in the filter select field
      Then the options of the filter select field include "XSS<details/open/ontoggle=alert(document.location)> (New)"


   Scenario: As a developer I want to prevent a XSS attack for a filter-select on mobile
      Given the user opens the application on a mobile using the following link: "@sage/xtrem-show-case/FilterSelect"
      Then the "Field - Filter Select" titled page is displayed

      Given the user selects the "Filter Select Field" labelled filter select field on the main page
      When the user writes "XSS<details/open/ontoggle=alert(document.location)>" in the filter select field

      Given the user selects the "field" bound filter select field on the main page
      Then the options of the filter select field include "XSS<details/open/ontoggle=alert(document.location)> (New)"


   Scenario: As an developer I would like to set the lookup's title so that I can improve the user's experience
      Given the user opens the application on a mobile using the following link: "@sage/xtrem-show-case/FilterSelect"
      Then the "Field - Filter Select" titled page is displayed

      Given the user selects the "Filter Select Field" labelled filter select field on the main page
      When the user clicks the lookup button of the filter select field
      Then the dialog title is "Select product"

   Scenario: As a user I want the helper text to remain in place even if I select a value
      XT-39479
      Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/FilterSelect"
      Then the "Field - Filter Select" titled page is displayed
      Given the user selects the "Helper text" labelled text field on the main page
      When the user writes "Sample text" in the text field
      And the user presses Tab
      Given the user selects the "field" bound filter select field on the main page
      Then the helper text of the filter select field is "Sample text"
      When the user writes "Veal Inside" in the filter select field
      And the user selects "Veal Inside - Provimi" in the filter select field
      Then the value of the filter select field is "Veal Inside - Provimi"
      Then the helper text of the filter select field is "Sample text"
      And the user clears the filter select field
      Then the value of the filter select field is ""
      Then the helper text of the filter select field is "Sample text"
