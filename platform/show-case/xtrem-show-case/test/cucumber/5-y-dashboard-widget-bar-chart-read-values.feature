Feature: 5 Y dashboard widget bar chart read values
    # Tests the reading and verification of data values in bar chart widgets, focusing on aggregation methods and data representation
    @ClearDashboards
    Scenario: As a user I want to see the default value for chart aggregation methods
        Given the user opens the application on a desktop
        And the dashboard page is displayed
        Then the "Create a dashboard to get started." subtitled empty dashboard is displayed
        When the user clicks the create button on the dashboard
        Then the dashboard creation dialog is displayed
        When the user selects the template with title "Showcase dashboard" in the dashboard creation dialog
        And the user clicks the "next" button in the dashboard creation dialog
        Then the "Showcase dashboard" titled dashboard in the dashboard editor is displayed

        # Widget selection step
        When the user clicks the "createAWidget" labelled button in the dashboard editor navigation panel
        Then the "New widget" titled widget editor dialog is displayed
        And the value of the step title of the widget editor dialog is "1. Select a widget to get started"
        And the "cancel" button in the widget editor dialog is enabled
        And the "next" button in the widget editor dialog is disabled
        When the user writes "Bar Chart Widget" in the "basic-title" text field in the widget editor dialog
        And the user presses Enter
        And the user writes "My demo category" in the "basic-category" dropdown field in the widget editor dialog
        And the user presses Enter
        And the user writes "Show Case Product" in the "basic-node" dropdown field in the widget editor dialog
        And the user presses Enter

        # table widget
        And the user selects the "BAR_CHART" widget card in the widget editor dialog
        Then the "BAR_CHART" widget card in the widget editor dialog is selected
        And the "next" button in the widget editor dialog is enabled
        When the user clicks the "next" button in the widget editor dialog
        Then the value of the step title of the widget editor dialog is "2. Select the data to add to your widget"
        When the user clicks the "next" button in the widget editor dialog
        Then the value of the step title of the widget editor dialog is "2. Select the data to add to your widget"
        When the user selects the "Category" tree-view element in the widget editor dialog
        And the user selects the "Net price" tree-view element in the widget editor dialog
        And the user clicks the "next" button in the widget editor dialog
        Then the value of the step title of the widget editor dialog is "3. Add your content"
        And the "horizontal-axis" dropdown field in the widget editor dialog is enabled
        And the "group-by" dropdown field in the widget editor dialog is hidden
        When the user writes "Category" in the "horizontal-axis" dropdown field in the widget editor dialog
        And the user presses Enter
        And the user clicks the "Add value" table button in the widget editor dialog
        Then the "content-property" dropdown field of row "1" in the widget editor dialog is enabled
        When the user writes "Net Price" in the "content-property" dropdown field of row "1" in the widget editor dialog
        And the user presses Enter
        Then the value of the "content-label" text field of row "1" in the widget editor dialog is "Net price"
        And the value of the "grouping-method" dropdown field of row "1" in the widget editor dialog is "Sum"
