Feature: 1-1 Date
    # Tests the date field component across different devices, verifying functionality with various entry methods (hardcoded dates, dynamic date generation), validating properties, formatting, and date picker interactions.

    Scenario Outline: <Device> - As an ATP XTreeM User I can vrite and verify a date field using label
        XT-25838
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/DateField"
        Then the "Field - Date" titled page is displayed

        Given the user selects the "Title" labelled text field on the main page
        When the user writes "Sample title" in the text field
        And the user selects the "Placeholder" labelled text field on the main page
        And the user clicks in the text field

        Given the user selects the "Sample title" labelled date field on the main page
        When the user writes "08/23/2020" in the date field
        Then the value of the date field is "08/23/2020"
        And the user clears the date field
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |


    Scenario Outline: <Device> - As an ATP XTreeM User I can vrite and verify a date field using bind
        XT-25838
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/DateField"
        Then the "Field - Date" titled page is displayed

        Given the user selects the "field" bound date field on the main page
        When the user writes "09/20/2020" in the date field
        Then the value of the date field is "09/20/2020"
        And the user clears the date field
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |


    Scenario Outline: <Device> - As an ATP XTreeM User I can scroll to and blur the date field
        XT-25838
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/DateField"
        Then the "Field - Date" titled page is displayed

        Given the user selects the "field" bound date field on the main page
        And the user scrolls to the date field
        And the user blurs the date field
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    #Date Field: Set / check properties

    Scenario Outline: <Device> - As and ATP XTreeM user I can verify the date field title
        XT-25838
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/DateField"
        Then the "Field - Date" titled page is displayed

        Given the user selects the "Title" labelled text field on the main page
        When the user writes "Sample title" in the text field
        And the user presses Tab

        Given the user selects the "field" bound date field on the main page
        Then the title of the date field is "Sample title"
        Then the title of the date field is displayed

        Given the user selects the "Is title hidden" labelled checkbox field on the main page
        When the user ticks the checkbox field

        Given the user selects the "field" bound date field on the main page
        Then the title of the date field is hidden
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |


    Scenario Outline: <Device> - As and ATP XTreeM user I can verify the date field helper text
        XT-25838
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/DateField"
        Then the "Field - Date" titled page is displayed

        Given the user selects the "Helper text" labelled text field on the main page
        When the user writes "Sample text" in the text field
        And the user presses Tab

        Given the user selects the "field" bound date field on the main page
        Then the helper text of the date field is "Sample text"
        Then the helper text of the date field is displayed

        Given the user selects the "Is helper text hidden" labelled checkbox field on the main page
        When the user ticks the checkbox field

        Given the user selects the "field" bound date field on the main page
        Then the helper text of the date field is hidden
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |


    Scenario Outline: <Device> - As and ATP XTreeM user I can verify if the date field is displayed or hidden
        XT-25838
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/DateField"
        Then the "Field - Date" titled page is displayed

        Given the user selects the "field" bound date field on the main page
        When the user clicks in the date field
        Then the "field" bound date field on the main page is displayed

        Given the user selects the "Is hidden" labelled checkbox field on the main page
        When the user ticks the checkbox field
        Then the "field" bound date field on the main page is hidden
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |


    Scenario Outline:<Device> - As and ATP XTreeM user I can verify if the date field is enabled or disabled
        XT-25838
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/DateField"
        Then the "Field - Date" titled page is displayed

        Given the user selects the "field" bound date field on the main page
        Then the date field is enabled

        Given the user selects the "Is disabled" labelled checkbox field on the main page
        When the user ticks the checkbox field

        Given the user selects the "field" bound date field on the main page
        Then the date field is disabled
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |


    Scenario Outline: <Device> - As and ATP XTreeM user I can verify if the date field is read-only
        XT-25838
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/DateField"
        Then the "Field - Date" titled page is displayed

        Given the user selects the "Is readOnly" labelled checkbox field on the main page
        When the user ticks the checkbox field

        Given the user selects the "field" bound date field on the main page
        Then the date field is read-only
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |


    Scenario: As a developer I want to validate mandatory fields using functional code (X3-223028)
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/DateField"
        Then the "Field - Date" titled page is displayed

        And scrolls to the "Additional examples" labelled block
        And the user clicks the "validateMandatory" bound button on the main page
        Then an info dialog appears on the main page
        And the text in the header of the dialog is "Validation result"
        And the text in the body of the dialog is "You need to select or enter a value."
        When the user presses Escape
        And the user clicks the "validateBlock" bound button on the main page
        Then an info dialog appears on the main page
        And the text in the header of the dialog is "Validation result"
        And the text in the body of the dialog is "You need to select or enter a value."


    Scenario: ui.formatDateToCurrentLocale (XT-19602)
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/DateField"
        Then the "Field - Date" titled page is displayed

        When the user selects the "dateFormatsField" bound text field on the main page
        Then the value of the text field is "07/22/2019 July July 2019 07/22/2019 07/2019 07/22"

    @empty_date
    Scenario: As a user I want to set date fields to empty and save them
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/PageDefaultCrudActions/eyJfaWQiOiI0MDcifQ=="
        Given the user selects the "Ending Date" labelled date field on the main page
        When the user writes "11/11/2021" in the date field
        Then the value of the date field is "11/11/2021"
        And the user selects the "Fixed Quantity" labelled numeric field on the main page
        When the user writes "2" in the numeric field
        And the user clicks the "Save" labelled business action button on the main page
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/PageDefaultCrudActions/eyJfaWQiOiI0MDcifQ=="
        Given the user selects the "Ending Date" labelled date field on the main page
        Then the value of the date field is "11/11/2021"
        When the user clears the date field
        Then the value of the date field is ""
        And the user selects the "Fixed Quantity" labelled numeric field on the main page
        When the user writes "2" in the numeric field
        And the user clicks the "Save" labelled business action button on the main page
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/PageDefaultCrudActions/eyJfaWQiOiI0MDcifQ=="
        Given the user selects the "Ending Date" labelled date field on the main page
        Then the value of the date field is ""

    # ----------------Date format / Languages specificites---------------------------
    #----------------Dynamic date generation &  Languages specificites---------------------------

    # date separator depends on the language selected
    @generic_date
    Scenario Outline: As an XTreeM user I can set the date in language <Language>

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/DateField"
        And the user switches language to <Language>
        And the user refreshes the screen

        Then the "Field - Date" titled page is displayed

        #Set the date to the current day, current month , current year
        Given the user selects the "field" bound date field on the main page
        And the user writes a generated date in the date field with value <Value1>
        Then the value of the date field is a generated date with value <Value1>
        # And takes a screenshot

        #Set the date to the current day +1 , current month - 1 , current year -1
        And the user clears the date field
        And the user writes a generated date in the date field with value <Value2>
        Then the value of the date field is a generated date with value <Value2>
        # And takes a screenshot

        #Set the date to the Last day of the current month and the current year
        And the user clears the date field
        And the user writes a generated date in the date field with value <Value3>
        Then the value of the date field is a generated date with value <Value3>
        # And takes a screenshot

        Examples:
            | Language     | Value1  | Value2              | Value3  |
            | "English US" | "M/T/Y" | "(M-1)/(T+1)/(Y-1)" | "M/L/Y" |
            | "English GB" | "T/M/Y" | "(T+1)/(M-1)/(Y-1)" | "L/M/Y" |
            | "Spanish"    | "T/M/Y" | "(T+1)/(M-1)/(Y-1)" | "L/M/Y" |
            | "French"     | "T/M/Y" | "(T+1)/(M-1)/(Y-1)" | "L/M/Y" |
            | "German"     | "T.M.Y" | "(T+1).(M-1).(Y-1)" | "L.M.Y" |
            | "Polish"     | "T.M.Y" | "(T+1).(M-1).(Y-1)" | "L.M.Y" |
            | "Portuguese" | "T/M/Y" | "(T+1)/(M-1)/(Y-1)" | "L/M/Y" |
            | "Chinese"    | "Y/M/T" | "(Y-1)/(M-1)/(T+1)" | "Y/M/L" |

    @generic_date
    Scenario Outline: As an XTreeM user I can set the date in language US in desktop mode - <Value1>

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/DateField"
        And the user switches language to "English US"
        And the user refreshes the screen

        Then the "Field - Date" titled page is displayed

        #Set the date to the current day, current month , current year
        Given the user selects the "field" bound date field on the main page
        And the user writes a generated date in the date field with value <Value1>
        Then the value of the date field is a generated date with value <Value1>
        # And takes a screenshot

        Examples:
            | Value1              |
            | "T"                 |
            | "T-3"               |
            | "T+2"               |
            | "L"                 |
            | "M/L/Y"             |
            | "(M+2)/(T+3)/(Y-3)" |
            | "(M-1)/L/Y"         |
            | "02/L/(Y)"          |
            | "02/L/(Y-1)"        |
            | "02/L/2028"         |
            | "02/L/2024"         |
            | "02/L/2023"         |
            | "02/L/2022"         |
            | "01/L/2024"         |

    @generic_date
    Scenario Outline: As an XTreeM user I can set the date in language US in mobile - <Value1>

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/DateField"
        And the user switches language to "English US"
        And the user refreshes the screen
        Then the "Field - Date" titled page is displayed
        Given the user opens the application on a mobile using the following link: "@sage/xtrem-show-case/DateField"

        #Set the date to the current day, current month , current year
        Given the user selects the "field" bound date field on the main page
        And the user writes a generated date in the date field with value <Value1>
        Then the value of the date field is a generated date with value <Value1>
        # And takes a screenshot

        Examples:
            | Value1              |
            | "T"                 |
            | "T-3"               |
            | "T+2"               |
            | "L"                 |
            | "M/L/Y"             |
            | "(M+2)/(T+3)/(Y-3)" |
            | "(M-1)/L/Y"         |
            | "02/L/(Y)"          |
            | "02/L/(Y-1)"        |
            | "02/L/2028"         |
            | "02/L/2024"         |
            | "02/L/2023"         |
            | "02/L/2022"         |
            | "01/L/2024"         |

    # date separator depends on the language selected
    @generic_date
    Scenario Outline: As an XTreeM user I can calculate the next specific date from today in language <Language>

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/DateField"
        And the user switches language to <Language>
        And the user refreshes the screen

        Then the "Field - Date" titled page is displayed

        #Set the date to the current day, current month , current year
        Given the user selects the "field" bound date field on the main page
        And the user writes a generated date with value <Value1> from today to the selected date field
        Then the value of the date field is a generated date from today with value <Value1>
        # And takes a screenshot

        Examples:
            | Language     | Value1             |
            | "English US" | "1st Tuesday"      |
            | "English GB" | "1st Tuesday + 2"  |
            | "Spanish"    | "2nd Monday"       |
            | "French"     | "2nd Monday + 3"   |
            | "German"     | "3rd Friday"       |
            | "Polish"     | "3rd Friday + 1"   |
            | "Portuguese" | "4th Thursday"     |
            | "Chinese"    | "4th Thursday + 1" |

    @readOnly_generated_date
    Scenario Outline: <Device> - As an ATP XTreeM user I can verify a generated date if the date field is read-only
        XT-66305
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/DateField"
        Then the "Field - Date" titled page is displayed

        Given the user selects the "value" bound date field on the main page
        And the user writes a generated date in the date field with value "T"
        Then the value of the date field is a generated date with value "T"

        Given the user selects the "Is readOnly" labelled checkbox field on the main page
        When the user ticks the checkbox field

        Given the user selects the "field" bound date field on the main page
        Then the date field is read-only

        Then the value of the date field is a generated date with value "T"
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    @readOnly_harcoded_date
    Scenario Outline: <Device> - As an ATP XTreeM user I can verify a harcoded date if the date field is read-only
        XT-66305
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/DateField"
        Then the "Field - Date" titled page is displayed

        Given the user selects the "value" bound date field on the main page
        When the user writes "09/20/2021" in the date field
        Then the value of the date field is "09/20/2021"

        Given the user selects the "Is readOnly" labelled checkbox field on the main page
        When the user ticks the checkbox field

        Given the user selects the "field" bound date field on the main page
        Then the date field is read-only

        Then the value of the date field is "09/20/2021"
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    @generic_date
    Scenario Outline: <Device> - As an ATP XTreeM user I can verify the date is enabled / selected / not-selected using dynamic date
        XT-64130
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/DateField"
        Then the "Field - Date" titled page is displayed

        Given the user selects the "field" bound date field on the main page
        When the user writes a generated date in the date field with value "03/01/Y"
        Then the dates from "03/01/Y" to "03/31/Y" are enabled
        And the date equal to "03/01/Y" is selected
        And the date equal to "03/02/Y" is not selected
        When the user writes a generated date in the date field with value "03/02/Y"
        Then the date equal to "03/01/Y" is not selected
        And the date equal to "03/02/Y" is selected
        And the date equal to "03/03/Y" is not selected
        When the user writes a generated date in the date field with value "03/31/Y"
        Then the date equal to "03/30/Y" is not selected
        And the date equal to "03/31/Y" is selected
        And the date equal to "04/01/Y" is not selected
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |


    @generic_date
    Scenario Outline: <Device> - As an ATP XTreeM user I can verify the range of date is enabled / disabled / out of period using dynamic date in <datePicker> Date picker
        XT-64130
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/DateField"
        Then the "Field - Date" titled page is displayed

        Given the user selects the <datePicker> bound date field on the main page
        When the user stores the date value of the first day of the date picker with key "[ENV_FIRSTDATE]"
        And the user stores the date value of the last day of the date picker with key "[ENV_LASTDATE]"
        Then the dates from <enabledStartDate> to <enabledEndDate> are enabled
        And the dates from <disabledStartDate> to <disabledEndDate> are disabled
        And the dates from <outOfPeriodStartDate> to <outOfPeriodEndDate> are out of period
        Examples:
            | Device  | datePicker | enabledStartDate  | enabledEndDate   | disabledStartDate | disabledEndDate  | outOfPeriodStartDate | outOfPeriodEndDate |
            | desktop | "maxDate"  | "[ENV_FIRSTDATE]" | "M/T/Y"          | "M/(T+1)/Y"       | "[ENV_LASTDATE]" | "[ENV_FIRSTDATE]"    | "(M-1)/L/Y"        |
            | tablet  | "maxDate"  | "[ENV_FIRSTDATE]" | "M/T/Y"          | "M/(T+1)/Y"       | "[ENV_LASTDATE]" | "[ENV_FIRSTDATE]"    | "(M-1)/L/Y"        |
            | mobile  | "maxDate"  | "[ENV_FIRSTDATE]" | "M/T/Y"          | "M/(T+1)/Y"       | "[ENV_LASTDATE]" | "[ENV_FIRSTDATE]"    | "(M-1)/L/Y"        |
            | desktop | "minDate"  | "M/T/Y"           | "[ENV_LASTDATE]" | "[ENV_FIRSTDATE]" | "M/(T-1)/Y"      | "(M+1)/01/Y"         | "[ENV_LASTDATE]"   |
            | tablet  | "minDate"  | "M/T/Y"           | "[ENV_LASTDATE]" | "[ENV_FIRSTDATE]" | "M/(T-1)/Y"      | "(M+1)/01/Y"         | "[ENV_LASTDATE]"   |
            | mobile  | "minDate"  | "M/T/Y"           | "[ENV_LASTDATE]" | "[ENV_FIRSTDATE]" | "M/(T-1)/Y"      | "(M+1)/01/Y"         | "[ENV_LASTDATE]"   |


    @generic_date
    Scenario Outline: <Language> - As an ATP XTreeM user I can verify the range of date is enabled / disabled / out of period using dynamic date in <datePicker> Date picker
        XT-67255
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/DateField"
        And the user switches language to <Language>
        And the user refreshes the screen
        Then the "Field - Date" titled page is displayed

        Given the user selects the <datePicker> bound date field on the main page
        When the user stores the date value of the first day of the date picker with key "[ENV_FIRSTDATE]"
        And the user stores the date value of the last day of the date picker with key "[ENV_LASTDATE]"
        Then the dates from <enabledStartDate> to <enabledEndDate> are enabled
        And the dates from <disabledStartDate> to <disabledEndDate> are disabled
        And the dates from <outOfPeriodStartDate> to <outOfPeriodEndDate> are out of period

        Examples:
            | Language     | datePicker | enabledStartDate  | enabledEndDate   | disabledStartDate | disabledEndDate  | outOfPeriodStartDate | outOfPeriodEndDate |
            | "English GB" | "maxDate"  | "[ENV_FIRSTDATE]" | "T/M/Y"          | "(T+1)/M/Y"       | "[ENV_LASTDATE]" | "[ENV_FIRSTDATE]"    | "L/(M-1)/Y"        |
            | "Spanish"    | "maxDate"  | "[ENV_FIRSTDATE]" | "T/M/Y"          | "(T+1)/M/Y"       | "[ENV_LASTDATE]" | "[ENV_FIRSTDATE]"    | "L/(M-1)/Y"        |
            | "French"     | "maxDate"  | "[ENV_FIRSTDATE]" | "T/M/Y"          | "(T+1)/M/Y"       | "[ENV_LASTDATE]" | "[ENV_FIRSTDATE]"    | "L/(M-1)/Y"        |
            | "German"     | "maxDate"  | "[ENV_FIRSTDATE]" | "T.M.Y"          | "(T+1).M.Y"       | "[ENV_LASTDATE]" | "[ENV_FIRSTDATE]"    | "L.(M-1).Y"        |
            | "Polish"     | "maxDate"  | "[ENV_FIRSTDATE]" | "T.M.Y"          | "(T+1).M.Y"       | "[ENV_LASTDATE]" | "[ENV_FIRSTDATE]"    | "L.(M-1).Y"        |
            | "Portuguese" | "maxDate"  | "[ENV_FIRSTDATE]" | "T/M/Y"          | "(T+1)/M/Y"       | "[ENV_LASTDATE]" | "[ENV_FIRSTDATE]"    | "L/(M-1)/Y"        |
            | "Chinese"    | "maxDate"  | "[ENV_FIRSTDATE]" | "Y/M/T"          | "Y/M/(T+1)"       | "[ENV_LASTDATE]" | "[ENV_FIRSTDATE]"    | "Y/(M-1)/L"        |
            | "English GB" | "minDate"  | "T/M/Y"           | "[ENV_LASTDATE]" | "[ENV_FIRSTDATE]" | "(T-1)/M/Y"      | "01/(M+1)/Y"         | "[ENV_LASTDATE]"   |
            | "Spanish"    | "minDate"  | "T/M/Y"           | "[ENV_LASTDATE]" | "[ENV_FIRSTDATE]" | "(T-1)/M/Y"      | "01/(M+1)/Y"         | "[ENV_LASTDATE]"   |
            | "French"     | "minDate"  | "T/M/Y"           | "[ENV_LASTDATE]" | "[ENV_FIRSTDATE]" | "(T-1)/M/Y"      | "01/(M+1)/Y"         | "[ENV_LASTDATE]"   |
            | "German"     | "minDate"  | "T.M.Y"           | "[ENV_LASTDATE]" | "[ENV_FIRSTDATE]" | "(T-1).M.Y"      | "01.(M+1).Y"         | "[ENV_LASTDATE]"   |
            | "Polish"     | "minDate"  | "T.M.Y"           | "[ENV_LASTDATE]" | "[ENV_FIRSTDATE]" | "(T-1).M.Y"      | "01.(M+1).Y"         | "[ENV_LASTDATE]"   |
            | "Portuguese" | "minDate"  | "T/M/Y"           | "[ENV_LASTDATE]" | "[ENV_FIRSTDATE]" | "(T-1)/M/Y"      | "01/(M+1)/Y"         | "[ENV_LASTDATE]"   |
            | "Chinese"    | "minDate"  | "Y/M/T"           | "[ENV_LASTDATE]" | "[ENV_FIRSTDATE]" | "Y/M/(T-1)"      | "Y/(M+1)/01"         | "[ENV_LASTDATE]"   |

    Scenario Outline: <Device> - As an ATP XTreeM User I can write a date to the date field without separators
        XT-95945
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/DateField"
        Then the "Field - Date" titled page is displayed

        Given the user selects the "Title" labelled text field on the main page
        When the user writes "Sample title" in the text field
        And the user selects the "Placeholder" labelled text field on the main page
        And the user clicks in the text field

        Given the user selects the "Sample title" labelled date field on the main page
        When the user writes "08232020" in the date field
        Then the value of the date field is "08/23/2020"
        And the user clears the date field
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |
