Feature: 5 Y dashboard widget search
    # Tests the widget search functionality in the dashboard editor, verifying search results display and filtering based on user input

    @ClearDashboards
    Scenario: As a user I want to search for a widget in the widget list
        Given the user opens the application on a desktop
        And the dashboard page is displayed
        Then the "Create a dashboard to get started." subtitled empty dashboard is displayed
        When the user clicks the create button on the dashboard
        Then the dashboard creation dialog is displayed
        When the user selects the template with title "Blank template" in the dashboard creation dialog
        And the user clicks the "next" button in the dashboard creation dialog
        Then the "New dashboard" titled dashboard in the dashboard editor is displayed
        When the user searches for "xyz" in the navigation panel
        Then the navigation panel is empty
        When the user searches for "lis" in the navigation panel
        When the user clicks the Add button of the "List of users" titled widget card in the dashboard editor navigation panel
        Then the "List of users" titled widget in the dashboard editor is displayed
        When the user clicks the "save" button in the dashboard editor footer
        Then a toast containing text "Dashboard saved." is displayed
        And the user dismisses all the toasts
        And the user waits 1 second
        Then the "New dashboard" titled dashboard is displayed
        And the "List of users" titled widget in the dashboard editor is displayed
