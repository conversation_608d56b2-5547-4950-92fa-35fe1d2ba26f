Feature: 5-y-dashboard-widget-resize
    # Tests the resizing functionality of dashboard widgets, ensuring users can adjust widget dimensions to optimize dashboard layout

    @ClearsDashboards
    Scenario: Create a dashboard add a widget and resize it

        Given the user opens the application on a HD desktop
        ##Create the dashboard
        And the dashboard page is displayed
        Then the "Create a dashboard to get started." subtitled empty dashboard is displayed
        When the user clicks the create button on the dashboard

        #Select showcase dashboard
        When the user selects the template with title "Blank template" in the dashboard creation dialog
        Then the template with title "Blank template" in the dashboard creation dialog is selected
        When the user clicks the "Next" button in the dashboard creation dialog

        #Verify the widgets in the Dashboard editor
        Then the "New dashboard" titled dashboard in the dashboard editor is displayed

        # Add widget
        When the user searches for "Invoices" in the navigation panel
        When the user clicks the Add button of the "Invoices" titled widget card in the dashboard editor navigation panel

        #resize the widget
        And the user selects the "Invoices" titled table widget field in the dashboard editor
        And the user increases the widget field by 250,250 pixels
        # And takes a screenshot
        And the user waits 3 seconds
        And the user decreases the widget field by 100,100 pixels
        # And takes a screenshot

        And the user clicks the "cancel" button in the dashboard editor footer
        And the user clicks the "Yes" button of the Confirm dialog on the main page
        Then the "New dashboard" titled dashboard is displayed
