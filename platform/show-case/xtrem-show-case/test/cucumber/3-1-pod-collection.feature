Feature: 3-1 Pod collection
    # Tests the pod collection component, verifying the ability to add, select, manage collection items across different device type, and control the pod collection field state.

    Scenario Outline: <Device> - As a user I can add a pod collection item
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/PodCollection/eyJfaWQiOiIyIn0="
        And the user selects the "Title" labelled text field on the main page
        When the user writes "Pod Collection Title" in the text field
        And the user selects the "Can add item" labelled checkbox field on the main page
        When the user ticks the checkbox field
        And the user selects the "Pod Collection Title" labelled pod collection field on the main page
        When the user clicks the "Add an item" button of the selected pod collection field
        Then a toast with text "A new pod was added" is displayed
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> - And the user clicks the Remove button of the Confirm dialog
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/PodCollection/eyJfaWQiOiIyIn0="
        And the user selects the "Title" labelled text field on the main page
        When the user writes "Pod Collection Title" in the text field
        And the user selects the "Can remove item" labelled checkbox field on the main page
        When the user ticks the checkbox field
        And the user selects the "Pod Collection Title" labelled pod collection field on the main page
        And the user selects the "Mints - Striped Red 85" labelled pod collection item of the selected pod collection field
        When the user selects the "Close" icon of the selected pod collection item
        And the user clicks the "Remove" button of the Confirm dialog
        Then the selected pod collection item is hidden
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> - And the user clicks the Remove button of the Confirm dialog
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/PodCollection/eyJfaWQiOiIyIn0="
        And the user selects the "Title" labelled text field on the main page
        When the user writes "Pod Collection Title" in the text field
        And the user selects the "Can remove item" labelled checkbox field on the main page
        When the user ticks the checkbox field
        And the user selects the "Pod Collection Title" labelled pod collection field on the main page
        And the user selects the "Ecolab - Solid Fusion 27" labelled pod collection item of the selected pod collection field
        When the user selects the "Close" icon of the selected pod collection item
        And the user clicks the "Remove" button of the Confirm dialog
        Then the selected pod collection item is hidden
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> - As a user I can remove a pod collection item using id with the Close icon
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/PodCollection/eyJfaWQiOiIyIn0="
        And the user selects the "Title" labelled text field on the main page
        When the user writes "Pod Collection Title" in the text field
        And the user selects the "Can remove item" labelled checkbox field on the main page
        When the user ticks the checkbox field
        And the user selects the "lines" bound pod collection field on the main page
        And the user selects the "856" id pod collection item of the selected pod collection field
        When the user selects the "Close" icon of the selected pod collection item
        And the user clicks the "Remove" button of the Confirm dialog
        Then the selected pod collection item is hidden
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> - As a user I can select the action of a selected pod collection item
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/PodCollection/eyJfaWQiOiIyIn0="
        And the user selects the "lines" bound pod collection field on the main page
        And the user selects the "Appetizer - Cheese Bites 320" labelled pod collection item of the selected pod collection field
        When the user clicks the "Add" action of the selected pod collection item
        Then an info dialog appears on the main page
        Then the text in the header of the dialog is "Add Row Action was clicked"
        Then the text in the body of the dialog is "Product: Appetizer - Cheese Bites Row: 378"
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> - As a user I can select the main checkbox of a selected pod collection item
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/PodCollection/eyJfaWQiOiIyIn0="
        And the user selects the "Title" labelled text field on the main page
        When the user writes "Pod Collection Title" in the text field
        And the user selects the "Can select" labelled checkbox field on the main page
        When the user ticks the checkbox field
        And the user selects the "Pod Collection Title" labelled pod collection field on the main page
        And the user selects the "Appetizer - Cheese Bites 320" labelled pod collection item of the selected pod collection field
        And the user selects the main checkbox of the selected pod collection item
        And the user unselects the main checkbox of the selected pod collection item
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> - As a user I can verify if the pod collection field is enabled or disabled
        XT-4581
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/PodCollection/eyJfaWQiOiIyIn0="
        And the user selects the "Title" labelled text field on the main page
        When the user writes "Pod Collection Title" in the text field
        And the user selects the "Pod Collection Title" labelled pod collection field on the main page
        Then the selected pod collection field is enabled
        And the user selects the "Is Disabled" labelled checkbox field on the main page
        When the user ticks the checkbox field
        Then the selected pod collection field is disabled
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> - As a user  I can verify if the pod collection field is read-only
        XT-4581
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/PodCollection/eyJfaWQiOiIyIn0="
        And the user selects the "Title" labelled text field on the main page
        When the user writes "Pod Collection Title" in the text field
        And the user selects the "Pod Collection Title" labelled pod collection field on the main page
        And the user selects the "Is Read-only" labelled checkbox field on the main page
        When the user ticks the checkbox field
        Then the selected pod collection field is read-only
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> - As a user I can verify the pod collection title value
        XT-4581
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/PodCollection/eyJfaWQiOiIyIn0="
        And the user selects the "Title" labelled text field on the main page
        When the user writes "Pod Collection Title" in the text field
        And the user selects the "Pod Collection Title" labelled pod collection field on the main page
        Then the title of the selected pod collection field is "Pod Collection Title"
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> - As a user I can verify the pod collection Helper text value
        XT-4581
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/PodCollection/eyJfaWQiOiIyIn0="
        And the user selects the "Title" labelled text field on the main page
        When the user writes "Pod Collection Title" in the text field
        And the user selects the "Helper Text" labelled text field on the main page
        When the user writes "Pod Collection helper text" in the text field
        And the user selects the "Pod Collection Title" labelled pod collection field on the main page
        Then the helper text of the selected pod collection field is "Pod Collection helper text"
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario: As an application developer I want to define validation rules for the pod collection field
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/PodCollection/eyJfaWQiOiI1In0="
        Then the "Pod Collection" titled page is displayed
        And the user selects the "Can remove item" labelled checkbox field on the main page
        When the user ticks the checkbox field
        And the user selects the "Lines" labelled pod collection field on the main page
        Then the pod collection field is valid
        And the user selects the "Beer - true North Strong Ale 22" labelled pod collection item of the selected pod collection field
        When the user selects the "Close" icon of the selected pod collection item
        And the user clicks the "Remove" button of the Confirm dialog
        Then the pod collection field is invalid
