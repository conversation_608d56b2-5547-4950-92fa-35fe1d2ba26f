Feature: 5 Y dashboard widget tile indicator edition
    # Tests the editing functionality of indicator tile widgets, including customization of data sources, thresholds, and visual presentation

    @ClearDashboards
    Scenario: As a user I want to edit an indicator-tile widget
        # create widget
        Given the user opens the application on a desktop
        And the dashboard page is displayed
        Then the "Create a dashboard to get started." subtitled empty dashboard is displayed
        When the user clicks the create button on the dashboard
        Then the dashboard creation dialog is displayed
        When the user selects the template with title "Factory Dashboard" in the dashboard creation dialog
        And the user clicks the "next" button in the dashboard creation dialog
        Then the "Factory Dashboard" titled dashboard in the dashboard editor is displayed
        When the user clicks the "createAWidget" labelled button in the dashboard editor navigation panel
        Then the "New widget" titled widget editor dialog is displayed
        And the value of the step title of the widget editor dialog is "1. Select a widget to get started"
        When the user writes "Test Indicator Tile Widget" in the "basic-title" text field in the widget editor dialog
        And the user presses Enter
        And the user writes "Show Case Provider" in the "basic-node" dropdown field in the widget editor dialog
        And the user presses Enter
        And the user selects the "INDICATOR_TILE" widget card in the widget editor dialog
        Then the "INDICATOR_TILE" widget card in the widget editor dialog is selected
        When the user clicks the "next" button in the widget editor dialog
        Then the value of the step title of the widget editor dialog is "2. Select the data to add to your widget"
        When the user selects the "Text field" tree-view element in the widget editor dialog
        And the user selects the "Integer field" tree-view element in the widget editor dialog
        And the user selects the "Date field" tree-view element in the widget editor dialog
        And the user clicks the "next" button in the widget editor dialog
        Then the value of the step title of the widget editor dialog is "3. Add your content"
        Then the "grouping-method" dropdown field in the widget editor dialog is disabled
        And the "next" button in the widget editor dialog is disabled
        When the user writes "Integer field" in the "grouping-property" dropdown field in the widget editor dialog
        Then the "grouping-method" dropdown field in the widget editor dialog is enabled
        When the user writes "Maximum" in the "grouping-method" dropdown field in the widget editor dialog
        And the user presses Enter
        Then the "next" button in the widget editor dialog is enabled
        When the user clicks the "next" button in the widget editor dialog
        Then the value of the step title of the widget editor dialog is "4. Add your filters"
        When the user clicks the "Add filter" table button in the widget editor dialog
        Then the "filter-property" dropdown field of row "1" in the widget editor dialog is enabled
        When the user writes "Text field" in the "filter-property" dropdown field of row "1" in the widget editor dialog
        And the user presses Enter
        And the user writes "Contains" in the "filter-type" dropdown field of row "1" in the widget editor dialog
        And the user presses Enter
        And the user writes "a" in the "filter-value" text field of row "1" in the widget editor dialog
        And the user clicks the "add" action button of row "1" in the widget editor dialog
        Then the "filter-property" dropdown field of row "2" in the widget editor dialog is enabled
        When the user writes "Integer field" in the "filter-property" dropdown field of row "2" in the widget editor dialog
        And the user presses Enter
        Then the "filter-type" dropdown field of row "2" in the widget editor dialog is enabled
        And the user writes "Between" in the "filter-type" dropdown field of row "2" in the widget editor dialog
        Then the "filter-value-min" text field of row "2" in the widget editor dialog is enabled
        And the "filter-value-max" text field of row "2" in the widget editor dialog is enabled
        When the user writes "3" in the "filter-value-min" text field of row "2" in the widget editor dialog
        Then the "Invalid integer" validation error of row "2" in the widget editor dialog is displayed
        And the user writes "10" in the "filter-value-max" text field of row "2" in the widget editor dialog
        Then the "Invalid range" validation error of row "2" in the widget editor dialog is hidden
        When the user clicks the "add" action button of row "2" in the widget editor dialog
        Then the "filter-property" dropdown field of row "3" in the widget editor dialog is enabled
        When the user writes "Date field" in the "filter-property" dropdown field of row "3" in the widget editor dialog
        Then the "filter-type" dropdown field of row "3" in the widget editor dialog is enabled
        When the user writes "Between" in the "filter-type" dropdown field of row "3" in the widget editor dialog
        And the user presses Enter
        Then the "start" date field of row "3" in the widget editor dialog is displayed
        And the "end" date field of row "3" in the widget editor dialog is displayed
        When the user writes "02/16/2022" in the "start" date field of row "3" in the widget editor dialog
        And the user presses Tab
        Then the "Invalid date" validation error of row "3" in the widget editor dialog is displayed
        When the user writes "02/17/2023" in the "end" date field of row "3" in the widget editor dialog
        And the user presses Tab
        Then the "Invalid date" validation error of row "3" in the widget editor dialog is hidden
        When the user clicks the "next" button in the widget editor dialog
        Then the value of the step title of the widget editor dialog is "5. Create your layout"
        When the user writes "Accounting" in the "layout-icon" dropdown field in the widget editor dialog
        And the user presses Enter
        And the user writes "2" in the "layout-decimal-digits" text field in the widget editor dialog
        And the user presses Enter
        And the user writes "new" in the "layout-subtitle" text field in the widget editor dialog
        And the user presses Enter
        And the user ticks the "See all" checkbox field in the widget editor dialog
        And the user writes "Showcase - Provider" in the "layout-page-seeAllAction" dropdown field in the widget editor dialog
        And the user presses Enter
        And the user ticks the "Create" checkbox field in the widget editor dialog
        And the user writes "Showcase - Provider" in the "layout-page-createAction" dropdown field in the widget editor dialog
        And the user presses Enter
        Then the "add" button in the widget editor dialog is enabled
        When the user clicks the "add" button in the widget editor dialog
        Then the "Factory Dashboard" titled dashboard in the dashboard editor is displayed
        And the "Test Indicator Tile Widget" titled widget in the dashboard editor is displayed
        And the user waits 1 second
        When the user clicks the "save" button in the dashboard editor footer
        Then a toast containing text "Dashboard saved." is displayed
        When the user dismisses all the toasts
        Then the "Factory Dashboard" titled dashboard is displayed
        And the "Test Indicator Tile Widget" titled widget in the dashboard is displayed


        # edit the widget

        # checking the previously set values are displayed correctly
        When the user clicks the "Edit" labelled CRUD button in the dashboard action menu
        Then the "Test Indicator Tile Widget" titled widget in the dashboard editor is displayed
        When the user selects the "Test Indicator Tile Widget" titled tile-indicator widget field in the dashboard editor
        And the user clicks the "edit" more actions button in the header of the tile-indicator widget field
        Then the "Edit widget" titled widget editor dialog is displayed
        And the value of the step title of the widget editor dialog is "1. Select a widget to get started"
        And the value of the "basic-title" text field in the widget editor dialog is "Test Indicator Tile Widget"
        And the value of the "basic-category" dropdown field in the widget editor dialog is ""
        And the "basic-node" dropdown field in the widget editor dialog is hidden
        And the "INDICATOR_TILE" widget card in the widget editor dialog is hidden
        And the "TABLE" widget card in the widget editor dialog is hidden
        And the "next" button in the widget editor dialog is enabled
        When the user clicks the "next" button in the widget editor dialog
        Then the value of the step title of the widget editor dialog is "2. Select the data to add to your widget"
        And the "Date field" tree-view element in the widget editor dialog is checked
        And the "Integer field" tree-view element in the widget editor dialog is checked
        And the "Text field" tree-view element in the widget editor dialog is checked
        When the user clicks the "next" button in the widget editor dialog
        Then the value of the step title of the widget editor dialog is "3. Add your content"
        And the value of the "grouping-property" dropdown field in the widget editor dialog is "Integer field"
        And the value of the "grouping-method" dropdown field in the widget editor dialog is "Maximum"
        When the user clicks the "next" button in the widget editor dialog
        Then the value of the step title of the widget editor dialog is "4. Add your filters"
        And the value of the "filter-property" dropdown field of row "1" in the widget editor dialog is "Text field"
        And the value of the "filter-type" dropdown field of row "1" in the widget editor dialog is "Contains"
        And the value of the "filter-value" text field of row "1" in the widget editor dialog is "a"
        And the value of the "filter-property" dropdown field of row "2" in the widget editor dialog is "Integer field"
        And the value of the "filter-type" dropdown field of row "2" in the widget editor dialog is "Between"
        And the value of the "filter-value-min" text field of row "2" in the widget editor dialog is "3"
        And the value of the "filter-value-max" text field of row "2" in the widget editor dialog is "10"
        When the user clicks the "next" button in the widget editor dialog
        Then the value of the step title of the widget editor dialog is "5. Create your layout"
        And the value of the "layout-icon" dropdown field in the widget editor dialog is "Accounting"
        And the value of the "layout-decimal-digits" text field in the widget editor dialog is "2"
        And the value of the "layout-subtitle" text field in the widget editor dialog is "new"
        And the "See all" checkbox field in the widget editor dialog is checked
        And the value of the "layout-page-seeAllAction" dropdown field in the widget editor dialog is "ShowCase - Provider"
        And the "Create" checkbox field in the widget editor dialog is checked
        And the value of the "layout-page-createAction" dropdown field in the widget editor dialog is "ShowCase - Provider"

        # modiying the values of the widget
        When the user clicks the "previous" button in the widget editor dialog
        Then the value of the step title of the widget editor dialog is "4. Add your filters"
        When the user clicks the "previous" button in the widget editor dialog
        Then the value of the step title of the widget editor dialog is "3. Add your content"
        When the user clicks the "previous" button in the widget editor dialog
        Then the value of the step title of the widget editor dialog is "2. Select the data to add to your widget"
        When the user clicks the "previous" button in the widget editor dialog
        Then the value of the step title of the widget editor dialog is "1. Select a widget to get started"
        When the user writes "Edited indicator tile widget" in the "basic-title" text field in the widget editor dialog
        When the user writes "My other demo category" in the "basic-category" dropdown field in the widget editor dialog
        And the user presses Enter
        And the user clicks the "next" button in the widget editor dialog
        Then the value of the step title of the widget editor dialog is "2. Select the data to add to your widget"
        When the user unselects the "Text field" tree-view element in the widget editor dialog
        And the user clicks the "next" button in the widget editor dialog
        Then the value of the step title of the widget editor dialog is "3. Add your content"
        And the user clicks the "next" button in the widget editor dialog
        Then the value of the step title of the widget editor dialog is "4. Add your filters"
        # check that filter for "Text field" has been removed
        And the value of the "filter-property" dropdown field of row "1" in the widget editor dialog is "Integer field"
        And the value of the "filter-type" dropdown field of row "1" in the widget editor dialog is "Between"
        And the value of the "filter-value-min" text field of row "1" in the widget editor dialog is "3"
        And the value of the "filter-value-max" text field of row "1" in the widget editor dialog is "10"
        When the user clicks the "next" button in the widget editor dialog
        Then the value of the step title of the widget editor dialog is "5. Create your layout"
        And the "update" button in the widget editor dialog is enabled
        When the user writes "Animal" in the "layout-icon" dropdown field in the widget editor dialog
        And the user presses Enter
        And the user writes "3" in the "layout-decimal-digits" text field in the widget editor dialog
        And the user presses Enter
        And the user writes "edited" in the "layout-subtitle" text field in the widget editor dialog
        And the user unticks the "See all" checkbox field in the widget editor dialog
        Then the "layout-title-seeAllAction" text field in the widget editor dialog is disabled
        And the "layout-page-seeAllAction" dropdown field in the widget editor dialog is disabled
        And the "See all" preview button in the widget editor dialog is hidden
        When the user ticks the "See all" checkbox field in the widget editor dialog
        Then the "See all" preview button in the widget editor dialog is displayed
        And the "layout-page-seeAllAction" dropdown field in the widget editor dialog is enabled
        When the user writes "Inline Edit" in the "layout-page-seeAllAction" dropdown field in the widget editor dialog
        And the user presses Enter
        And the user unticks the "Create" checkbox field in the widget editor dialog
        Then the "layout-title-createAction" text field in the widget editor dialog is disabled
        And the "layout-page-createAction" dropdown field in the widget editor dialog is disabled
        And the "Create" preview button in the widget editor dialog is hidden
        When the user ticks the "Create" checkbox field in the widget editor dialog
        Then the "Create" preview button in the widget editor dialog is displayed
        And the "layout-page-createAction" dropdown field in the widget editor dialog is enabled
        When the user writes "Inline Edit" in the "layout-page-createAction" dropdown field in the widget editor dialog
        And the user presses Enter
        And the user clicks the "update" button in the widget editor dialog
        Then the "Factory Dashboard" titled dashboard in the dashboard editor is displayed
        And the "Edited indicator tile widget" titled widget in the dashboard editor is displayed
        When the user clicks the "save" button in the dashboard editor footer
        And the user dismisses all the toasts
        Then the "Factory Dashboard" titled dashboard is displayed
        And the "Edited indicator tile widget" titled widget in the dashboard is displayed
