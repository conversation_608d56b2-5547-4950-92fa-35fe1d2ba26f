@date_grouped
Feature: 1-1 A grouping main list
    # Tests the list grouping functionality across different data types, verifying grouping by date fields (year, month, day), text fields, and numeric values

    Scenario: As a user I want to be able to group the main list by a date's year
        XT-33695
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-show-case/ShowCaseInvoice"
        Then the "Invoices" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        Then the value of the "customer__name" bound nested reference field of the selected row in the table field is "<PERSON><PERSON><PERSON>; <PERSON><PERSON> and <PERSON>"
        When the user clicks the group by year option in the header menu of the "purchaseDate" bound column of the table field
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "2019 (168)"
        And the user selects the row 2 of the table field
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "2020 (340)"
        And the user selects the row 3 of the table field
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "2021 (11)"
        And the user selects the row 4 of the table field
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "2022 (10)"
        And the user selects the row 1 of the table field
        When the user expands the selected row of the table field
        And the user selects the row 2 of the table field
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "12/10/2019"
        Then the value of the "_id" bound nested text field of the selected row in the table field is "INV 7"
        And the user selects the row 3 of the table field
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "09/03/2019"
        Then the value of the "_id" bound nested text field of the selected row in the table field is "INV 13"
        And the user selects the row 1 of the table field
        When the user collapses the selected row of the table field
        And the user selects the row 2 of the table field
        When the user expands the selected row of the table field
        And the user selects the row 3 of the table field
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "06/25/2020"
        Then the value of the "_id" bound nested text field of the selected row in the table field is "INV 1"
        And the user selects the row 4 of the table field
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "03/22/2020"
        Then the value of the "_id" bound nested text field of the selected row in the table field is "INV 2"
        And the user selects the row 2 of the table field
        When the user collapses the selected row of the table field
        # descending order
        When the user clicks the "purchaseDate" bound column of the table field
        And the user selects the row 1 of the table field
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "2022 (10)"
        And the user selects the row 2 of the table field
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "2021 (11)"
        And the user selects the row 3 of the table field
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "2020 (340)"
        And the user selects the row 4 of the table field
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "2019 (168)"
        And the user selects the row 1 of the table field
        When the user expands the selected row of the table field
        And the user selects the row 2 of the table field
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "11/25/2022"
        Then the value of the "_id" bound nested text field of the selected row in the table field is "INV 529"
        And the user selects the row 3 of the table field
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "09/30/2022"
        Then the value of the "_id" bound nested text field of the selected row in the table field is "INV 528"
        And the user selects the row 1 of the table field
        When the user collapses the selected row of the table field
        And the user selects the row 2 of the table field
        When the user expands the selected row of the table field
        And the user selects the row 3 of the table field
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "12/25/2021"
        Then the value of the "_id" bound nested text field of the selected row in the table field is "INV 517"
        And the user selects the row 4 of the table field
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "12/25/2021"
        Then the value of the "_id" bound nested text field of the selected row in the table field is "INV 518"
        And the user selects the row 2 of the table field
        When the user collapses the selected row of the table field
        # filter
        And the user filters the "purchaseDate" bound column in the table field with value "09/09/2019"
        And the user selects the row 1 of the table field
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "2019 (2)"
        When the user expands the selected row of the table field
        And the user selects the row 2 of the table field
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "09/09/2019"
        Then the value of the "_id" bound nested text field of the selected row in the table field is "INV 23"
        And the user selects the row 3 of the table field
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "09/09/2019"
        Then the value of the "_id" bound nested text field of the selected row in the table field is "INV 136"
        And the user selects the row 1 of the table field
        When the user collapses the selected row of the table field
        When the user filters the "_id" bound column in the table field with value "23"
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "2019 (1)"
        When the user expands the selected row of the table field
        And the user selects the row 2 of the table field
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "09/09/2019"
        Then the value of the "_id" bound nested text field of the selected row in the table field is "INV 23"
        And the user selects the row 1 of the table field
        When the user collapses the selected row of the table field
        # ungroup
        Then the ungroup option in the header menu of the "purchaseDate" bound column of the table field is displayed
        When the user clicks the ungroup option in the header menu of the "purchaseDate" bound column of the table field
        And the user selects the row 1 of the table field
        Then the value of the "_id" bound nested text field of the selected row in the table field is "INV 23"

    Scenario: As a user I want to be able to group the main list by a date's month
        XT-33695
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-show-case/ShowCaseInvoice"
        Then the "Invoices" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        Then the value of the "customer__name" bound nested reference field of the selected row in the table field is "Schinner; Kling and Toy"
        Then the group by option in the header menu of the "purchaseDate" bound column of the table field is displayed
        When the user clicks the group by month option in the header menu of the "purchaseDate" bound column of the table field
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "August 2019 (2)"
        And the user selects the row 2 of the table field
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "September 2019 (51)"
        And the user selects the row 3 of the table field
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "October 2019 (35)"
        And the user selects the row 20 of the table field
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "July 2021 (1)"
        And the user selects the row 1 of the table field
        When the user expands the selected row of the table field
        And the user selects the row 2 of the table field
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "08/31/2019"
        Then the value of the "_id" bound nested date field of the selected row in the table field is "INV 38"
        And the user selects the row 1 of the table field
        When the user collapses the selected row of the table field
        And the user selects the row 2 of the table field
        When the user expands the selected row of the table field
        And the user selects the row 3 of the table field
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "09/03/2019"
        Then the value of the "_id" bound nested date field of the selected row in the table field is "INV 13"
        And the user selects the row 4 of the table field
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "09/06/2019"
        Then the value of the "_id" bound nested date field of the selected row in the table field is "INV 16"
        And the user selects the row 18 of the table field
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "09/16/2019"
        Then the value of the "_id" bound nested date field of the selected row in the table field is "INV 191"
        And the user selects the row 2 of the table field
        When the user collapses the selected row of the table field
        # pagination
        And the user selects the row 20 of the table field
        When the user clicks the "purchaseDate" bound nested field of the selected row in the table field
        And the user presses ArrowDown
        And the user presses ArrowDown
        And the user presses ArrowDown
        And the user presses ArrowDown
        And the user presses ArrowDown
        And the user presses ArrowDown
        And the user presses ArrowDown
        And the user presses ArrowDown
        And the user presses ArrowDown
        And the user presses ArrowDown
        And the user waits 2 seconds
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 28 of the table field
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "November 2022 (1)"
        # descending order
        When the user clicks the "purchaseDate" bound column of the table field
        And the user selects the row 1 of the table field
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "November 2022 (1)"
        And the user selects the row 2 of the table field
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "September 2022 (1)"
        And the user selects the row 3 of the table field
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "August 2022 (3)"
        And the user selects the row 20 of the table field
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "April 2020 (45)"
        When the user clicks the "purchaseDate" bound nested field of the selected row in the table field
        # pagination
        And the user presses ArrowDown
        And the user presses ArrowDown
        And the user presses ArrowDown
        And the user waits 2 seconds
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 22 of the table field
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "February 2020 (46)"
        # expand a couple of groups
        And the user selects the row 1 of the table field
        When the user expands the selected row of the table field
        And the user selects the row 2 of the table field
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "11/25/2022"
        Then the value of the "_id" bound nested date field of the selected row in the table field is "INV 529"
        And the user selects the row 1 of the table field
        When the user collapses the selected row of the table field
        And the user selects the row 2 of the table field
        When the user expands the selected row of the table field
        And the user selects the row 3 of the table field
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "09/30/2022"
        Then the value of the "_id" bound nested date field of the selected row in the table field is "INV 528"
        And the user selects the row 2 of the table field
        When the user collapses the selected row of the table field
        # filter
        And the user filters the "purchaseDate" bound column in the table field with value "09/09/2019"
        And the user selects the row 1 of the table field
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "September 2019 (2)"
        When the user expands the selected row of the table field
        And the user selects the row 2 of the table field
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "09/09/2019"
        Then the value of the "_id" bound nested date field of the selected row in the table field is "INV 23"
        And the user selects the row 3 of the table field
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "09/09/2019"
        Then the value of the "_id" bound nested date field of the selected row in the table field is "INV 136"
        And the user selects the row 1 of the table field
        When the user collapses the selected row of the table field
        And the user filters the "_id" bound column in the table field with value "23"
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "September 2019 (1)"
        When the user expands the selected row of the table field
        And the user selects the row 2 of the table field
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "09/09/2019"
        Then the value of the "_id" bound nested date field of the selected row in the table field is "INV 23"
        # ungroup
        Then the ungroup option in the header menu of the "purchaseDate" bound column of the table field is displayed
        When the user clicks the ungroup option in the header menu of the "purchaseDate" bound column of the table field
        And the user selects the row 1 of the table field
        Then the value of the "_id" bound nested text field of the selected row in the table field is "INV 23"

    Scenario: As a user I want to be able to group the main list by a date's day
        XT-33695
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-show-case/ShowCaseInvoice"
        Then the "Invoices" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        Then the value of the "customer__name" bound nested reference field of the selected row in the table field is "Schinner; Kling and Toy"
        Then the group by option in the header menu of the "purchaseDate" bound column of the table field is displayed
        When the user clicks the group by day option in the header menu of the "purchaseDate" bound column of the table field
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "08/31/2019 (2)"
        And the user selects the row 2 of the table field
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "09/01/2019 (1)"
        And the user selects the row 3 of the table field
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "09/02/2019 (1)"
        And the user selects the row 1 of the table field
        When the user expands the selected row of the table field
        And the user selects the row 2 of the table field
        Then the value of the "_id" bound nested text field of the selected row in the table field is "INV 38"
        Then the value of the "customer__name" bound nested reference field of the selected row in the table field is "Mohr LLC"
        And the user selects the row 3 of the table field
        Then the value of the "_id" bound nested text field of the selected row in the table field is "INV 241"
        Then the value of the "customer__name" bound nested reference field of the selected row in the table field is "Gibson-Breitenberg"
        And the user selects the row 1 of the table field
        When the user collapses the selected row of the table field
        And the user selects the row 2 of the table field
        When the user expands the selected row of the table field
        And the user selects the row 3 of the table field
        Then the value of the "_id" bound nested text field of the selected row in the table field is "INV 125"
        Then the value of the "customer__name" bound nested reference field of the selected row in the table field is "Walsh; Robel and Hirthe"
        And the user selects the row 2 of the table field
        When the user collapses the selected row of the table field
        And the user selects the row 20 of the table field
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "09/21/2019 (2)"
        When the user clicks the "purchaseDate" bound nested field of the selected row in the table field
        # pagination
        And the user presses ArrowDown
        And the user presses ArrowDown
        And the user presses ArrowDown
        And the user presses ArrowDown
        And the user presses ArrowDown
        And the user presses ArrowDown
        And the user presses ArrowDown
        And the user presses ArrowDown
        And the user presses ArrowDown
        And the user presses ArrowDown
        And the user waits 2 seconds
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 40 of the table field
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "10/17/2019 (3)"
        # descending order
        When the user clicks the "purchaseDate" bound column of the table field
        And the user waits 3 seconds
        And the user selects the row 31 of the table field
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "08/21/2020 (1)"
        And the user selects the row 20 of the table field
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "01/25/2021 (1)"
        And the user selects the row 10 of the table field
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "06/25/2022 (1)"
        And the user selects the row 1 of the table field
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "11/25/2022 (1)"
        And the user selects the row 2 of the table field
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "09/30/2022 (1)"
        And the user selects the row 3 of the table field
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "08/29/2022 (1)"
        And the user selects the row 1 of the table field
        When the user clicks the "purchaseDate" bound nested field of the selected row in the table field
        When the user expands the selected row of the table field
        And the user selects the row 2 of the table field
        Then the value of the "_id" bound nested text field of the selected row in the table field is "INV 529"
        Then the value of the "customer__name" bound nested reference field of the selected row in the table field is "Jenkins-Dare"
        And the user selects the row 1 of the table field
        When the user collapses the selected row of the table field
        And the user selects the row 2 of the table field
        When the user clicks the "purchaseDate" bound nested field of the selected row in the table field
        When the user expands the selected row of the table field
        And the user selects the row 3 of the table field
        Then the value of the "_id" bound nested text field of the selected row in the table field is "INV 528"
        Then the value of the "customer__name" bound nested reference field of the selected row in the table field is "Jenkins-Dare"
        And the user selects the row 2 of the table field
        When the user collapses the selected row of the table field
        And the user selects the row 20 of the table field
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "01/25/2021 (1)"
        When the user clicks the "purchaseDate" bound nested field of the selected row in the table field
        And the user presses ArrowDown
        And the user presses ArrowDown
        And the user presses ArrowDown
        And the user presses ArrowDown
        And the user presses ArrowDown
        And the user presses ArrowDown
        And the user presses ArrowDown
        And the user presses ArrowDown
        And the user presses ArrowDown
        And the user presses ArrowDown
        And the user selects the row 40 of the table field
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "08/10/2020 (3)"
        # filter
        When the user filters the "purchaseDate" bound column in the table field with value "09/09/2019"
        And the user selects the row 1 of the table field
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "09/09/2019 (2)"
        When the user expands the selected row of the table field
        And the user selects the row 2 of the table field
        Then the value of the "_id" bound nested text field of the selected row in the table field is "INV 23"
        Then the value of the "customer__name" bound nested reference field of the selected row in the table field is "Kessler; Parker and Haley"
        And the user selects the row 3 of the table field
        Then the value of the "_id" bound nested text field of the selected row in the table field is "INV 136"
        Then the value of the "customer__name" bound nested reference field of the selected row in the table field is "Spinka Inc"
        And the user selects the row 1 of the table field
        When the user collapses the selected row of the table field
        When the user filters the "_id" bound column in the table field with value "23"
        And the user selects the row 1 of the table field
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "09/09/2019 (1)"
        When the user expands the selected row of the table field
        And the user selects the row 2 of the table field
        Then the value of the "_id" bound nested text field of the selected row in the table field is "INV 23"

        Then the value of the "customer__name" bound nested reference field of the selected row in the table field is "Kessler; Parker and Haley"
        # ungroup
        Then the ungroup option in the header menu of the "purchaseDate" bound column of the table field is displayed
        When the user clicks the ungroup option in the header menu of the "purchaseDate" bound column of the table field
        And the user selects the row 1 of the table field
        Then the value of the "_id" bound nested text field of the selected row in the table field is "INV 23"

    Scenario: As a user I want to be able to group the main list by a text field and then group by a date field correctly
        XT-45806
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-show-case/ShowCaseInvoice"
        Then the "Invoices" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        Then the value of the "customer__name" bound nested reference field of the selected row in the table field is "Schinner; Kling and Toy"
        When the user clicks the group by option in the header menu of the "customer__name" bound column of the table field
        Then the value of the "customer__name" bound nested reference field of the selected row in the table field is "No value (1)"
        And the user selects the row 2 of the table field
        Then the value of the "customer__name" bound nested reference field of the selected row in the table field is "Abshire; Gislason and Ortiz (1)"
        Then the group by option in the header menu of the "purchaseDate" bound column of the table field is displayed
        When the user clicks the group by year option in the header menu of the "purchaseDate" bound column of the table field
        And the user selects the row 1 of the table field
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "2019 (168)"

    Scenario: As a user I want to be able to group the main list by by a date field and then group by a text field correctly
        XT-72041
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-show-case/ShowCaseInvoice"
        Then the "Invoices" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        Then the value of the "customer__name" bound nested reference field of the selected row in the table field is "Schinner; Kling and Toy"
        When the group by option in the header menu of the "purchaseDate" bound column of the table field is displayed
        When the user clicks the group by year option in the header menu of the "purchaseDate" bound column of the table field
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "2019 (168)"
        When the user clicks the group by option in the header menu of the "customer__name" bound column of the table field
        Then the value of the "customer__name" bound nested reference field of the selected row in the table field is "No value (1)"
        And the user selects the row 2 of the table field
        Then the value of the "customer__name" bound nested reference field of the selected row in the table field is "Abshire; Gislason and Ortiz (1)"

    # Flaky test due to bug XT-93663
    Scenario: As a user I want to be able to sort reference group columns alphabetically
        XT-38306
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseInvoice"
        Then the "Invoices" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        Then the value of the "customer__name" bound nested reference field of the selected row in the table field is "Schinner; Kling and Toy"
        When the user clicks the group by option in the header menu of the "customer__name" bound column of the table field
        # items are displayed in ascending order
        Then the value of the "customer__name" bound nested reference field of the selected row in the table field is "No value (1)"
        And the user selects the row 2 of the table field
        Then the value of the "customer__name" bound nested reference field of the selected row in the table field is "Abshire; Gislason and Ortiz (1)"
        And the user selects the row 1 of the table field
        When the user expands the selected row of the table field
        And the user selects the row 2 of the table field
        Then the value of the "_id" bound nested text field of the selected row in the table field is "INV 507"
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "10/13/2020"
        And the user selects the row 1 of the table field
        When the user collapses the selected row of the table field
        And the user selects the row 2 of the table field
        When the user expands the selected row of the table field
        And the user selects the row 3 of the table field
        Then the value of the "_id" bound nested text field of the selected row in the table field is "INV 386"
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "09/07/2019"
        And the user selects the row 2 of the table field
        When the user collapses the selected row of the table field
        And the user selects the row 21 of the table field
        Then the value of the "customer__name" bound nested reference field of the selected row in the table field is "Botsford-Mertz (5)"
        # test group pagination by loading the second page of groups
        And the user selects the row 20 of the table field
        When the user clicks the "customer__name" bound nested field of the selected row in the table field
        And the user presses ArrowDown
        And the user presses ArrowDown
        And the user presses ArrowDown
        And the user presses ArrowDown
        And the user presses ArrowDown
        And the user presses ArrowDown
        And the user presses ArrowDown
        And the user presses ArrowDown
        And the user presses ArrowDown
        And the user presses ArrowDown
        And the user waits 3 seconds
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 41 of the table field
        Then the value of the "customer__name" bound nested reference field of the selected row in the table field is "Effertz; Okuneva and Strosin (6)"
        # items are displayed in descending order
        When  the user selects the "$navigationPanel" bound table field on the main page
        When the user clicks the "customer__name" bound column of the table field
        And the user selects the row 31 of the table field
        Then the value of the "customer__name" bound nested reference field of the selected row in the table field is "Schuster and Sons (2)"
        And the user selects the row 20 of the table field
        Then the value of the "customer__name" bound nested reference field of the selected row in the table field is "Strosin; Lueilwitz and Stamm (4)"
        And the user selects the row 10 of the table field
        Then the value of the "customer__name" bound nested reference field of the selected row in the table field is "Weber LLC (2)"
        And the user selects the row 1 of the table field
        Then the value of the "customer__name" bound nested reference field of the selected row in the table field is "Zulauf-Hansen (3)"
        When the user clicks the "customer__name" bound nested field of the selected row in the table field
        When the user expands the selected row of the table field
        And the user selects the row 2 of the table field
        Then the value of the "_id" bound nested text field of the selected row in the table field is "INV 42"
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "10/12/2019"
        And the user selects the row 3 of the table field
        Then the value of the "_id" bound nested text field of the selected row in the table field is "INV 155"
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "08/28/2020"
        And the user selects the row 1 of the table field
        When the user collapses the selected row of the table field
        And the user selects the row 2 of the table field
        When the user clicks the "customer__name" bound nested field of the selected row in the table field
        When the user expands the selected row of the table field
        And the user selects the row 3 of the table field
        Then the value of the "_id" bound nested text field of the selected row in the table field is "INV 57"
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "03/25/2020"
        And the user selects the row 2 of the table field
        When the user collapses the selected row of the table field
        And the user selects the row 20 of the table field
        Then the value of the "customer__name" bound nested reference field of the selected row in the table field is "Strosin; Lueilwitz and Stamm (4)"
        # test group pagination by loading the second page of groups
        When the user clicks the "customer__name" bound nested field of the selected row in the table field
        And the user presses ArrowDown
        And the user presses ArrowDown
        And the user presses ArrowDown
        And the user presses ArrowDown
        And the user presses ArrowDown
        And the user presses ArrowDown
        And the user presses ArrowDown
        And the user presses ArrowDown
        And the user presses ArrowDown
        And the user presses ArrowDown
        And the user waits 3 seconds
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user waits 3 seconds
        And the user selects the row 40 of the table field
        Then the value of the "customer__name" bound nested reference field of the selected row in the table field is "Satterfield-Wintheiser (3)"

    Scenario: As a user I want to be able to group and ungroup a table having a reference to different nodes with the same value
        XT-80054
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-show-case/ShowCaseProviderAddress"
        Then the "Provider addresses" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        Then the value of the "country__name" bound nested reference field of the selected row in the table field is "Andorra"
        Then the value of the "Name" labelled nested text field of the selected row in the table field is "Andorra Office"
        And the user selects the row 2 of the table field
        Then the value of the "country__name" bound nested reference field of the selected row in the table field is "Andorra"
        Then the value of the "Name" labelled nested text field of the selected row in the table field is "Andorra Warehouse"
        Then the group by option in the header menu of the "country__name" bound column of the table field is displayed
        When the user clicks the group by option in the header menu of the "country__name" bound column of the table field
        And the user selects the row 1 of the table field
        Then the value of the "country__name" bound nested reference field of the selected row in the table field is "Andorra (1)"
        And the user selects the row 2 of the table field
        Then the value of the "country__name" bound nested reference field of the selected row in the table field is "Andorra (1)"
        And the ungroup option in the header menu of the "country__name" bound column of the table field is displayed
        And the group by option in the header menu of the "country__name" bound column of the table field is hidden
        And the user selects the row 1 of the table field
        When the user expands the selected row of the table field
        And the user selects the row 2 of the table field
        Then the value of the "Name" labelled nested text field of the selected row in the table field is "Andorra Office"
        And the user selects the row 3 of the table field
        When the user expands the selected row of the table field
        And the user selects the row 4 of the table field
        Then the value of the "Name" labelled nested text field of the selected row in the table field is "Andorra Warehouse"
        When the user clicks the ungroup option in the header menu of the "country__name" bound column of the table field
        And the user selects the row 1 of the table field
        Then the value of the "country__name" bound nested reference field of the selected row in the table field is "Andorra"
        And the user selects the row 2 of the table field
        Then the value of the "country__name" bound nested reference field of the selected row in the table field is "Andorra"

    Scenario: As a user I want to be able to group and ungroup a table by a reference column
        XT-33693
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-show-case/ShowCaseInvoice"
        Then the "Invoices" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        Then the value of the "customer__name" bound nested reference field of the selected row in the table field is "Schinner; Kling and Toy"
        Then the group by option in the header menu of the "customer__name" bound column of the table field is displayed
        When the user clicks the group by option in the header menu of the "customer__name" bound column of the table field
        Then the value of the "customer__name" bound nested reference field of the selected row in the table field is "No value (1)"
        And the user selects the row 2 of the table field
        Then the value of the "customer__name" bound nested reference field of the selected row in the table field is "Abshire; Gislason and Ortiz (1)"
        And the user selects the row 3 of the table field
        Then the value of the "customer__name" bound nested reference field of the selected row in the table field is "Adams; Okuneva and Ebert (4)"
        And the user selects the row 4 of the table field
        Then the value of the "customer__name" bound nested reference field of the selected row in the table field is "Amazon (2)"
        Then the ungroup option in the header menu of the "customer__name" bound column of the table field is displayed
        Then the group by option in the header menu of the "customer__name" bound column of the table field is hidden
        When the user clicks the ungroup option in the header menu of the "customer__name" bound column of the table field
        And the user selects the row 1 of the table field
        Then the value of the "customer__name" bound nested reference field of the selected row in the table field is "Schinner; Kling and Toy"

    Scenario: As a user I want to be able to filter the groups of a table grouped by a reference column
        XT-33693
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-show-case/ShowCaseInvoice"
        Then the "Invoices" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        Then the value of the "customer__name" bound nested reference field of the selected row in the table field is "Schinner; Kling and Toy"
        Then the group by option in the header menu of the "customer__name" bound column of the table field is displayed
        When the user clicks the group by option in the header menu of the "customer__name" bound column of the table field
        Then the value of the "customer__name" bound nested reference field of the selected row in the table field is "No value (1)"
        And the user selects the row 2 of the table field
        Then the value of the "customer__name" bound nested reference field of the selected row in the table field is "Abshire; Gislason and Ortiz (1)"
        And the user filters the "customer__name" bound column in the table field with value "Brown and Sons"
        And the user selects the row 1 of the table field
        Then the value of the "customer__name" bound nested reference field of the selected row in the table field is "Brown and Sons (1)"
        When the user expands the selected row of the table field
        And the user selects the row 2 of the table field
        Then the value of the "_id" bound nested text field of the selected row in the table field is "INV 370"
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "06/17/2020"
        And the user selects the row 1 of the table field
        When the user collapses the selected row of the table field
        When the user filters the "customer__name" bound column in the table field with value "Dibbert and Sons"
        Then the value of the "customer__name" bound nested reference field of the selected row in the table field is "Brown and Sons (1)"
        And the user selects the row 2 of the table field
        Then the value of the "customer__name" bound nested reference field of the selected row in the table field is "Dibbert and Sons (2)"
        When the user expands the selected row of the table field
        And the user selects the row 3 of the table field
        Then the value of the "_id" bound nested text field of the selected row in the table field is "INV 321"
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "12/19/2019"
        And the user selects the row 4 of the table field
        Then the value of the "_id" bound nested text field of the selected row in the table field is "INV 467"
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "12/15/2019"
        And the user selects the row 2 of the table field
        When the user collapses the selected row of the table field

    Scenario: As a user I want to be able to filter the children of a table grouped by a reference column
        XT-33693
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-show-case/ShowCaseInvoice"
        Then the "Invoices" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the group by option in the header menu of the "customer__name" bound column of the table field is displayed
        When the user clicks the group by option in the header menu of the "customer__name" bound column of the table field
        And the user selects the row 1 of the table field
        Then the value of the "customer__name" bound nested reference field of the selected row in the table field is "No value (1)"
        And the user selects the row 2 of the table field
        Then the value of the "customer__name" bound nested reference field of the selected row in the table field is "Abshire; Gislason and Ortiz (1)"
        When the user filters the "customer__name" bound column in the table field with value "Dibbert and Sons"
        And the user selects the row 1 of the table field
        Then the value of the "customer__name" bound nested reference field of the selected row in the table field is "Dibbert and Sons (2)"
        When the user filters the "_id" bound column in the table field with value "321"
        Then the value of the "customer__name" bound nested reference field of the selected row in the table field is "Dibbert and Sons (1)"
        When the user expands the selected row of the table field
        And the user selects the row 2 of the table field
        Then the value of the "_id" bound nested text field of the selected row in the table field is "INV 321"
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "12/19/2019"
        And the user selects the row 1 of the table field
        When the user collapses the selected row of the table field

    Scenario: As a user I want to be able to group and ungroup a table by a label column
        XT-33693
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-show-case/ShowCaseProduct"
        Then the "Products" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        When the user clicks the "Hide table filters" labelled button of the table field
        And the user selects the row 1 of the table field
        Then the value of the "category" bound nested reference field of the selected row in the table field is "Good"
        Then the group by option in the header menu of the "category" bound column of the table field is displayed
        When the user clicks the group by option in the header menu of the "category" bound column of the table field
        Then the value of the "category" bound nested label field of the selected row in the table field is "No value (26)"
        And the user selects the row 2 of the table field
        Then the value of the "category" bound nested label field of the selected row in the table field is "Awful (11)"
        And the user selects the row 3 of the table field
        Then the value of the "category" bound nested label field of the selected row in the table field is "Good (439)"
        And the user selects the row 4 of the table field
        Then the value of the "category" bound nested label field of the selected row in the table field is "Not bad (9)"
        And the user selects the row 5 of the table field
        Then the value of the "category" bound nested label field of the selected row in the table field is "Ok (18)"
        And the user selects the row 1 of the table field
        When the user expands the selected row of the table field
        And the user selects the row 2 of the table field
        Then the value of the "product" bound nested text field of the selected row in the table field is "Banana - Green"
        Then the value of the "_id" bound nested date field of the selected row in the table field is "485"
        And the user selects the row 3 of the table field
        Then the value of the "product" bound nested text field of the selected row in the table field is "Beer - Heinekin"
        Then the value of the "_id" bound nested date field of the selected row in the table field is "488"
        And the user selects the row 1 of the table field
        When the user collapses the selected row of the table field
        And the user selects the row 2 of the table field
        When the user expands the selected row of the table field
        And the user selects the row 3 of the table field
        Then the value of the "product" bound nested text field of the selected row in the table field is "Arizona - Plum Green Tea"
        Then the value of the "_id" bound nested date field of the selected row in the table field is "74"
        And the user selects the row 4 of the table field
        Then the value of the "product" bound nested text field of the selected row in the table field is "Coffee - Irish Cream"
        Then the value of the "_id" bound nested date field of the selected row in the table field is "130"
        And the user selects the row 2 of the table field
        When the user collapses the selected row of the table field
        And the user selects the row 3 of the table field
        When the user expands the selected row of the table field
        And the user selects the row 4 of the table field
        Then the value of the "product" bound nested text field of the selected row in the table field is "Anisette - Mcguiness"
        Then the value of the "_id" bound nested date field of the selected row in the table field is "106"
        And the user selects the row 5 of the table field
        Then the value of the "product" bound nested text field of the selected row in the table field is "Appetiser - Bought"
        Then the value of the "_id" bound nested date field of the selected row in the table field is "313"
        And the user selects the row 3 of the table field
        When the user collapses the selected row of the table field
        Then the ungroup option in the header menu of the "category" bound column of the table field is displayed
        Then the group by option in the header menu of the "category" bound column of the table field is hidden
        When the user clicks the ungroup option in the header menu of the "category" bound column of the table field
        And the user selects the row 1 of the table field
        Then the value of the "category" bound nested reference field of the selected row in the table field is "Good"

    Scenario: As a user I want to be able to group the main list by a label column with sorting
        XT-33695
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-show-case/ShowCaseProduct"
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        Then the value of the "category" bound nested reference field of the selected row in the table field is "Good"
        And the group by option in the header menu of the "category" bound column of the table field is displayed
        When the user clicks the group by option in the header menu of the "category" bound column of the table field
        Then the value of the "category" bound nested label field of the selected row in the table field is "No value (26)"
        And the user selects the row 2 of the table field
        Then the value of the "category" bound nested label field of the selected row in the table field is "Awful (11)"
        And the user selects the row 3 of the table field
        Then the value of the "category" bound nested label field of the selected row in the table field is "Good (439)"
        And the user selects the row 4 of the table field
        Then the value of the "category" bound nested label field of the selected row in the table field is "Not bad (9)"
        And the user selects the row 5 of the table field
        Then the value of the "category" bound nested label field of the selected row in the table field is "Ok (18)"
        # sorting
        When the user clicks the "category" bound column of the table field
        And the user selects the row 1 of the table field
        Then the value of the "category" bound nested label field of the selected row in the table field is "Ok (18)"
        And the user selects the row 2 of the table field
        Then the value of the "category" bound nested label field of the selected row in the table field is "Not bad (9)"
        And the user selects the row 3 of the table field
        Then the value of the "category" bound nested label field of the selected row in the table field is "Good (439)"
        And the user selects the row 4 of the table field
        Then the value of the "category" bound nested label field of the selected row in the table field is "Awful (11)"
        And the user selects the row 5 of the table field
        Then the value of the "category" bound nested label field of the selected row in the table field is "No value (26)"
        And the user selects the row 1 of the table field
        When the user expands the selected row of the table field
        And the user selects the row 2 of the table field
        Then the value of the "product" bound nested text field of the selected row in the table field is "Spice - Paprika"
        Then the value of the "_id" bound nested date field of the selected row in the table field is "10"
        And the user selects the row 3 of the table field
        Then the value of the "product" bound nested text field of the selected row in the table field is "Wine - Redchard Merritt"
        Then the value of the "_id" bound nested date field of the selected row in the table field is "18"
        And the user selects the row 1 of the table field
        When the user collapses the selected row of the table field
        And the user selects the row 2 of the table field
        When the user expands the selected row of the table field
        And the user selects the row 3 of the table field
        Then the value of the "product" bound nested text field of the selected row in the table field is "Spice - Onion Powder Granulated"
        Then the value of the "_id" bound nested date field of the selected row in the table field is "42"
        And the user selects the row 4 of the table field
        Then the value of the "product" bound nested text field of the selected row in the table field is "Cleaner - Lime Away"
        Then the value of the "_id" bound nested date field of the selected row in the table field is "48"
        And the user selects the row 2 of the table field
        When the user collapses the selected row of the table field
        # filtering
        And the user filters the "category" bound column in the table field with value "Ok"
        And the user selects the row 1 of the table field
        Then the value of the "category" bound nested label field of the selected row in the table field is "Ok (18)"
        When the user filters the "description" bound column in the table field with value "synerg"
        Then the value of the "category" bound nested label field of the selected row in the table field is "Ok (2)"
        When the user expands the selected row of the table field
        And the user selects the row 2 of the table field
        Then the value of the "product" bound nested text field of the selected row in the table field is "Wine - Redchard Merritt"
        Then the value of the "_id" bound nested date field of the selected row in the table field is "18"
        When the user filters the "product" bound column in the table field with value "pastry"
        And the user selects the row 1 of the table field
        Then the value of the "category" bound nested label field of the selected row in the table field is "Ok (1)"
        When the user expands the selected row of the table field
        And the user selects the row 2 of the table field
        Then the value of the "product" bound nested text field of the selected row in the table field is "Pastry - Banana Muffin - Mini"
        Then the value of the "_id" bound nested date field of the selected row in the table field is "96"

    Scenario: As a user I want to be able to group by a nullable date column
        XT-46350
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-show-case/ShowCaseProduct"
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        When the user clicks the "Open column panel" labelled button of the table field
        Then the "Column settings" titled sidebar is displayed
        And the table column configuration with name "Ending date" on the sidebar is unticked
        When the user ticks the table column configuration with "Ending date" name on the sidebar
        And the table column configuration with name "Ending date" on the sidebar is ticked
        When the user clicks the Close button of the dialog on the sidebar
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        Then the "endingDate" bound column in the table field is displayed
        When the group by option in the header menu of the "endingDate" bound column of the table field is displayed
        When the user clicks the group by year option in the header menu of the "endingDate" bound column of the table field
        And the user selects the row 1 of the table field
        Then the value of the "Ending date" labelled nested date field of the selected row in the table field is "No value (501)"
        And the user selects the row 2 of the table field
        Then the value of the "Ending date" labelled nested date field of the selected row in the table field is "2019 (2)"
        And the user selects the row 1 of the table field
        When the user expands the selected row of the table field
        And the user selects the row 17 of the table field
        Then the value of the "product" bound nested text field of the selected row in the table field is "Bag Stand"
        Then the value of the "_id" bound nested date field of the selected row in the table field is "7"
        And the user selects the row 19 of the table field
        Then the value of the "product" bound nested text field of the selected row in the table field is "Barramundi"
        Then the value of the "_id" bound nested date field of the selected row in the table field is "29"
        And the user selects the row 1 of the table field
        When the user collapses the selected row of the table field
        And the user selects the row 2 of the table field
        When the user expands the selected row of the table field
        And the user selects the row 3 of the table field
        Then the value of the "product" bound nested text field of the selected row in the table field is "Ice Cream - Super Sandwich"
        Then the value of the "_id" bound nested date field of the selected row in the table field is "3"
        And the user selects the row 4 of the table field
        Then the value of the "product" bound nested text field of the selected row in the table field is "Veal Inside - Provimi"
        Then the value of the "_id" bound nested date field of the selected row in the table field is "2"
        And the user selects the row 2 of the table field
        When the user collapses the selected row of the table field

    Scenario: As a user I want to be able to group by a nullable date column
        XT-46350
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-show-case/ShowCaseProduct"
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        When the user clicks the "Open column panel" labelled button of the table field
        Then the "Column settings" titled sidebar is displayed
        And the table column configuration with name "Ending date" on the sidebar is unticked
        When the user ticks the table column configuration with "Ending date" name on the sidebar
        And the table column configuration with name "Ending date" on the sidebar is ticked
        When the user clicks the Close button of the dialog on the sidebar
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        Then the "endingDate" bound column in the table field is displayed
        When the group by option in the header menu of the "endingDate" bound column of the table field is displayed
        When the user clicks the group by year option in the header menu of the "endingDate" bound column of the table field
        And the user selects the row 1 of the table field
        Then the value of the "Ending date" labelled nested date field of the selected row in the table field is "No value (501)"
        And the user selects the row 2 of the table field
        Then the value of the "Ending date" labelled nested date field of the selected row in the table field is "2019 (2)"

    Scenario: As a user I want to be able to filter the children of a table grouped by a label column
        XT-33693
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-show-case/ShowCaseProduct"
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        Then the value of the "category" bound nested reference field of the selected row in the table field is "Good"
        Then the group by option in the header menu of the "category" bound column of the table field is displayed
        When the user clicks the group by option in the header menu of the "category" bound column of the table field
        Then the value of the "category" bound nested label field of the selected row in the table field is "No value (26)"
        And the user filters the "product" bound column in the table field with filter "Contains" and value "wine"
        Then the value of the "category" bound nested label field of the selected row in the table field is "No value (1)"
        And the user selects the row 2 of the table field
        Then the value of the "category" bound nested label field of the selected row in the table field is "Awful (2)"
        And the user selects the row 3 of the table field
        Then the value of the "category" bound nested label field of the selected row in the table field is "Good (29)"
        And the user selects the row 4 of the table field
        Then the value of the "category" bound nested label field of the selected row in the table field is "Not bad (3)"
        And the user selects the row 5 of the table field
        Then the value of the "category" bound nested label field of the selected row in the table field is "Ok (2)"
        And the user selects the row 1 of the table field
        When the user expands the selected row of the table field
        And the user selects the row 2 of the table field
        Then the value of the "product" bound nested text field of the selected row in the table field is "Wine - Cotes Du Rhone"
        Then the value of the "_id" bound nested date field of the selected row in the table field is "490"
        And the user selects the row 1 of the table field
        When the user collapses the selected row of the table field
        And the user selects the row 2 of the table field
        When the user expands the selected row of the table field
        And the user selects the row 3 of the table field
        Then the value of the "product" bound nested text field of the selected row in the table field is "Wine - Muscadet Sur Lie"
        Then the value of the "_id" bound nested date field of the selected row in the table field is "463"
        And the user selects the row 2 of the table field
        When the user collapses the selected row of the table field
        And the user selects the row 5 of the table field
        When the user expands the selected row of the table field
        And the user selects the row 6 of the table field
        Then the value of the "product" bound nested text field of the selected row in the table field is "Wine - Redchard Merritt"
        Then the value of the "_id" bound nested date field of the selected row in the table field is "18"
        And the user selects the row 5 of the table field
        When the user collapses the selected row of the table field

    Scenario: As a user I want to be able to group and ungroup a table by a label column
        XT-55445
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-show-case/ShowCaseProduct"
        Then the "Products" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        When the user clicks the "Hide table filters" labelled button of the table field
        And the user selects the row 1 of the table field
        Then the value of the "category" bound nested reference field of the selected row in the table field is "Good"
        Then the group by option in the header menu of the "category" bound column of the table field is displayed
        When the user clicks the group by option in the header menu of the "category" bound column of the table field
        Then the value of the "category" bound nested label field of the selected row in the table field is "No value (26)"
        And the user selects the row 2 of the table field
        Then the value of the "category" bound nested label field of the selected row in the table field is "Awful (11)"
        And the user ticks the main checkbox of the selected row in the table field
        Then the bulk action bar of the table field has 11 items

    Scenario: As a user I want to be able to see aggregated group totals on the main list
        XT-70467
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/StandardShowCaseProduct"
        Then the "Products" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        When the user clicks the "Open column panel" labelled button of the table field
        Then the "Column settings" titled sidebar is displayed
        When the user ticks the table column configuration with "Provider" name on the sidebar
        When the user ticks the table column configuration with "Category" name on the sidebar
        And the user presses Escape
        Then the group by option in the header menu of the "Provider" labelled column of the table field is displayed
        Then the user clicks the group by option in the header menu of the "Provider" labelled column of the table field
        And the user selects the row 3 of the table field
        When the user expands the selected row of the table field
        And the user selects the row 4 of the table field
        Then the value of the "Product" labelled nested text field of the selected row in the table field is "Rice"
        And the user selects the row 5 of the table field
        Then the value of the "Product" labelled nested text field of the selected row in the table field is "1"
        Then the value of the "Description" labelled nested text field of the selected row in the table field is "task-force"
        Then the group by option in the header menu of the "Category" labelled column of the table field is displayed
        Then the user clicks the group by option in the header menu of the "Category" labelled column of the table field
        And the user selects the row 2 of the table field
        When the user expands the selected row of the table field
        And the user selects the row 14 of the table field
        Then the value of the "Product" labelled nested text field of the selected row in the table field is "11"
        Then the value of the "Description" labelled nested text field of the selected row in the table field is "asynchronous"

    Scenario: As a uer I want the total count to update even if I have multiple pages of grouped data
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/StandardShowCaseProduct"
        Then the "Products" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        When the user clicks the "Open column panel" labelled button of the table field
        Then the "Column settings" titled sidebar is displayed
        When the user ticks the table column configuration with "Release date" name on the sidebar
        When the user clicks the Close button of the dialog on the sidebar
        When the user clicks the group by day option in the header menu of the "releaseDate" labelled column of the table field
        When the user selects all rows of the table field
        Then the bulk action bar of the table field has 503 items
        And the user selects the row 1 of the table field
        And the user unticks the main checkbox of the selected row in the table field
        Then the bulk action bar of the table field has 502 items
        And the user selects the row 13 of the table field
        And the user unticks the main checkbox of the selected row in the table field
        Then the bulk action bar of the table field has 497 items

    Scenario: As a user I want to group by deeply bound reference fields
        XT-87743
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/MainListWithDeepBoundReference"
        Then the "Products" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        Then the group by option in the header menu of the "Designer employee country" labelled column of the table field is displayed
        When the user clicks the group by option in the header menu of the "Designer employee country" labelled column of the table field
        And the user selects the row 1 of the table field
        Then the value of the "Designer employee country" labelled nested reference field of the selected row in the table field is "No value (494)"
        And the user selects the row 2 of the table field
        Then the value of the "Designer employee country" labelled nested reference field of the selected row in the table field is "Brazil (5)"
        When the user expands the selected row of the table field
        And the user selects the row 3 of the table field
        Then the value of the "Product" labelled nested text field of the selected row in the table field is "Apple - Macintosh"
        Then the value of the "ID" labelled nested date field of the selected row in the table field is "388"

    Scenario: As a user I want to be able to select and unselect products when grouped by supplier
        XT-94489
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseProduct"
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        When the user clicks the "Open column panel" labelled button of the table field
        Then the "Column settings" titled sidebar is displayed
        And the table column configuration with name "Supplier" on the sidebar is unticked
        When the user ticks the table column configuration with "Supplier" name on the sidebar
        And the table column configuration with name "Supplier" on the sidebar is ticked
        When the user clicks the Close button of the dialog on the sidebar
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        Then the "Supplier" labelled column in the table field is displayed
        When the group by option in the header menu of the "Supplier" labelled column of the table field is displayed
        When the user clicks the group by option in the header menu of the "Supplier" labelled column of the table field
        When the user selects all rows of the table field
        Then the bulk action bar of the table field has 503 items
        And the user selects the row 2 of the table field
        And the user unticks the main checkbox of the selected row in the table field
        Then the bulk action bar of the table field has 252 items
        When the user expands the selected row of the table field
        And the user selects the row 3 of the table field
        Then the value of the "ID" labelled nested text field of the selected row in the table field is "313"
        And the user ticks the main checkbox of the selected row in the table field
        Then the bulk action bar of the table field has 253 items
        And the user selects the row 2 of the table field
        And the user ticks the main checkbox of the selected row in the table field
        Then the bulk action bar of the table field has 503 items
        And the user selects the row 3 of the table field
        And the user unticks the main checkbox of the selected row in the table field
        Then the bulk action bar of the table field has 502 items
        When the user clicks the ungroup option in the header menu of the "Supplier" labelled column of the table field
        Then the bulk action bar of the table field is hidden

    Scenario: As a user I want to be able to select and unselect products when grouped by ending date
        XT-94489
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseProduct"
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        When the user clicks the "Open column panel" labelled button of the table field
        Then the "Column settings" titled sidebar is displayed
        And the table column configuration with name "Ending date" on the sidebar is unticked
        When the user ticks the table column configuration with "Ending date" name on the sidebar
        And the table column configuration with name "Ending date" on the sidebar is ticked
        When the user clicks the Close button of the dialog on the sidebar
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        Then the "endingDate" bound column in the table field is displayed
        When the group by option in the header menu of the "endingDate" bound column of the table field is displayed
        When the user clicks the group by year option in the header menu of the "endingDate" bound column of the table field
        When the user selects all rows of the table field
        Then the bulk action bar of the table field has 503 items
        And the user selects the row 2 of the table field
        And the user unticks the main checkbox of the selected row in the table field
        Then the bulk action bar of the table field has 501 items
        And the user selects the row 1 of the table field
        When the user expands the selected row of the table field
        And the user selects the row 2 of the table field
        Then the value of the "ID" labelled nested text field of the selected row in the table field is "106"
        And the user ticks the main checkbox of the selected row in the table field
        Then the bulk action bar of the table field has 500 items
        And the user selects the row 1 of the table field
        And the user ticks the main checkbox of the selected row in the table field
        Then the bulk action bar of the table field has 501 items
        When the user clicks the ungroup option in the header menu of the "endingDate" bound column of the table field
        Then the bulk action bar of the table field is hidden


    Scenario: As a user I want to be able to select and unselect products when grouped by category
        XT-94489
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseProduct"
        Then the "Products" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        When the user clicks the group by option in the header menu of the "Category" labelled column of the table field
        Then the bulk action bar of the table field is hidden
        And the user selects the row 1 of the table field
        And the user ticks the main checkbox of the selected row in the table field
        Then the bulk action bar of the table field has 26 items
        And the user selects the row 2 of the table field
        And the user ticks the main checkbox of the selected row in the table field
        Then the bulk action bar of the table field has 37 items
        And the user selects the row 1 of the table field
        When the user expands the selected row of the table field
        And the user selects the row 2 of the table field
        Then the value of the "ID" labelled nested text field of the selected row in the table field is "485"
        And the user unticks the main checkbox of the selected row in the table field
        Then the bulk action bar of the table field has 36 items
        And the user ticks the main checkbox of the selected row in the table field
        Then the bulk action bar of the table field has 37 items
        And the user selects the row 3 of the table field
        And the user unticks the main checkbox of the selected row in the table field
        Then the bulk action bar of the table field has 36 items
        And the user selects the row 1 of the table field
        And the user ticks the main checkbox of the selected row in the table field
        Then the bulk action bar of the table field has 37 items
        When the user selects all rows of the table field
        Then the bulk action bar of the table field has 503 items
        And the user selects the row 2 of the table field
        Then the value of the "ID" labelled nested text field of the selected row in the table field is "485"
        And the user unticks the main checkbox of the selected row in the table field
        Then the bulk action bar of the table field has 502 items
        And the user selects the row 3 of the table field
        And the user unticks the main checkbox of the selected row in the table field
        Then the bulk action bar of the table field has 501 items
        And the user ticks the main checkbox of the selected row in the table field
        Then the bulk action bar of the table field has 502 items
        And the user selects the row 1 of the table field
        And the user ticks the main checkbox of the selected row in the table field
        Then the bulk action bar of the table field has 503 items
        And the user unticks the main checkbox of the selected row in the table field
        Then the bulk action bar of the table field has 477 items
        When the user clicks the ungroup option in the header menu of the "Category" labelled column of the table field
        Then the bulk action bar of the table field is hidden

    Scenario: As a user I want to be able to select and unselect rows in a table field
        XT-94489
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseInvoice"
        Then the "Invoices" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        And the user ticks the main checkbox of the selected row in the table field
        Then the bulk action bar of the table field has 1 items
        And the user selects the row 2 of the table field
        And the user ticks the main checkbox of the selected row in the table field
        Then the bulk action bar of the table field has 2 items
        And the user selects the row 1 of the table field
        And the user unticks the main checkbox of the selected row in the table field
        Then the bulk action bar of the table field has 1 items
        And the user selects the row 2 of the table field
        And the user unticks the main checkbox of the selected row in the table field
        Then the bulk action bar of the table field is hidden
        When the user clicks the group by option in the header menu of the "Customer" labelled column of the table field
        Then the bulk action bar of the table field is hidden
        And the user selects the row 3 of the table field
        And the user ticks the main checkbox of the selected row in the table field
        Then the bulk action bar of the table field has 4 items
        And the user unticks the main checkbox of the selected row in the table field
        Then the bulk action bar of the table field is hidden
        And the user ticks the main checkbox of the selected row in the table field
        Then the bulk action bar of the table field has 4 items
        And the user selects the row 3 of the table field
        When the user expands the selected row of the table field
        And the user selects the row 4 of the table field
        Then the selected row of the table field is selected
        And the user selects the row 5 of the table field
        Then the selected row of the table field is selected
        And the user selects the row 6 of the table field
        Then the selected row of the table field is selected
        And the user selects the row 7 of the table field
        Then the selected row of the table field is selected
        And the user selects the row 4 of the table field
        And the user unticks the main checkbox of the selected row in the table field
        Then the selected row of the table field is unselected
        Then the bulk action bar of the table field has 3 items
        And the user selects the row 5 of the table field
        And the user unticks the main checkbox of the selected row in the table field
        Then the selected row of the table field is unselected
        Then the bulk action bar of the table field has 2 items
        And the user selects the row 3 of the table field
        And the user ticks the main checkbox of the selected row in the table field
        Then the bulk action bar of the table field has 4 items
        And the user unticks the main checkbox of the selected row in the table field
        Then the bulk action bar of the table field is hidden
        When the user selects all rows of the table field
        Then the bulk action bar of the table field has 529 items
        And the user selects the row 3 of the table field
        And the user unticks the main checkbox of the selected row in the table field
        Then the bulk action bar of the table field has 525 items
        And the user selects the row 4 of the table field
        And the user ticks the main checkbox of the selected row in the table field
        Then the bulk action bar of the table field has 526 items
        And the user selects the row 5 of the table field
        And the user ticks the main checkbox of the selected row in the table field
        Then the bulk action bar of the table field has 527 items
        And the user selects the row 3 of the table field
        And the user ticks the main checkbox of the selected row in the table field
        Then the bulk action bar of the table field has 529 items
        And the user selects the row 42 of the table field
        Then the selected row of the table field is selected
        When the user unselects all rows of the table field
        Then the bulk action bar of the table field is hidden
        And the user selects the row 1 of the table field
        Then the selected row of the table field is unselected
        And the user selects the row 42 of the table field
        Then the selected row of the table field is unselected
        When the user filters the "ID" labelled column in the table field with value "2"
        Then the floating filter value of the "ID" labelled column of the table field is "2"
        When the user selects all rows of the table field
        Then the bulk action bar of the table field has 188 items
        And the user selects the row 1 of the table field
        And the user unticks the main checkbox of the selected row in the table field
        Then the bulk action bar of the table field has 186 items
        And the user expands the selected row of the table field
        And the user selects the row 2 of the table field
        Then the selected row of the table field is unselected
        And the user selects the row 3 of the table field
        Then the selected row of the table field is unselected
        And the user selects the row 2 of the table field
        And the user ticks the main checkbox of the selected row in the table field
        Then the bulk action bar of the table field has 187 items
        And the user selects the row 3 of the table field
        And the user ticks the main checkbox of the selected row in the table field
        Then the bulk action bar of the table field has 188 items
        And the user selects the row 42 of the table field
        Then the selected row of the table field is selected
        When the user selects all rows of the table field
        Then the bulk action bar of the table field has 188 items
        When the user unselects all rows of the table field
        Then the bulk action bar of the table field is hidden
        And the user selects the row 42 of the table field
        Then the selected row of the table field is unselected

    Scenario: As a user I want to be able to select all rows in the main list after grouping and filtering
        XT-98341
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-show-case/ShowCaseInvoice"
        Then the "Invoices" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        Then the value of the "customer__name" bound nested reference field of the selected row in the table field is "Schinner; Kling and Toy"
        When the user clicks the group by year option in the header menu of the "purchaseDate" bound column of the table field
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "2019 (168)"
        And the user filters the "purchaseDate" bound column in the table field with value "09/09/2019"
        And the user selects the row 1 of the table field
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "2019 (2)"
        When the user selects all rows of the table field
        Then the bulk action bar of the table field has 2 items
        When the user unselects all rows of the table field
        Then the bulk action bar of the table field is hidden
