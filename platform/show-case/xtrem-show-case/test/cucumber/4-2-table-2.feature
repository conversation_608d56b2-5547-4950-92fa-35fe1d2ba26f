Feature: 4-2 Table 2
    # Tests advanced table functionality, including quick row addition with default values, column sorting, and complex data manipulation scenarios

    Scenario: As a user I want to get default values for quick add row
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseInvoice/eyJfaWQiOiIkbmV3In0="
        When the user selects the "Order" labelled reference field on the main page
        And the user writes "Nitzsche LLC" in the reference field
        And the user selects the "Order" labelled reference field on the main page
        And the user selects "Nitzsche LLC" in the reference field
        And the user selects the "Lines" labelled table field on the main page
        And the user selects the floating row of the table field
        When the user writes "Pepper - Black, Ground" in the "Product" labelled nested reference field of the selected row in the table field
        And the user selects "Pepper - Black, Ground" in the "Product" labelled nested field of the selected row in the table field
        When the user writes "40" in the "Ordered Quantity" labelled nested text field of the selected row in the table field
        And the user presses Tab
        Then the value of the "Comment" labelled nested text field of the selected row in the table field is "40 items supplied to Nitzsche LLC"
        And the user presses Control+Enter
        And the user waits 3 seconds
        Then the user clicks the "Save" labelled business action button on the main page
        And a toast with text "Record created" is displayed


    Scenario: As a functional dev I want to hide my Description column from both grid and grid row block
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseProvider/eyJfaWQiOiIxIn0="
        When the user clicks in the "hideDescriptionButton" bound button field on the main page
        And the user selects the "products" bound table field on the main page
        And the user selects the row 1 of the table field
        And the user clicks the "Open in Detail panel" dropdown action of the selected row of the table field
        Then the test id "e-text-field e-field-label-description e-field-bind-description" not exists in the page

    Scenario: As a user I want to add a column to the ones displayed by default in the grid of the navigation panel
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseProduct"
        Then the "Products" titled page is displayed
        # decorator isHiddenOnMainField not set
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        Then the "Description" labelled column in the table field is displayed
        # decorator isHiddenOnMainField true
        Then the "Supplier" labelled column in the table field is hidden
        When the user clicks the "Open column panel" labelled button of the table field
        Then the "Column settings" titled sidebar is displayed
        And the table column configuration with name "Description" on the sidebar is ticked
        And the table column configuration with name "Supplier" on the sidebar is unticked
        When the user ticks the table column configuration with "Supplier" name on the sidebar
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        Then the "Supplier" labelled column in the table field is displayed

    Scenario: As a user I want to add a column to the ones displayed by default in the grid of a table
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/InlineEdit/eyJfaWQiOiIyIn0="
        Then the "Inline Edit" titled page is displayed
        # decorator isHiddenOnMainField not set
        And the user selects the "field" bound table field on the main page
        Then the "Net Price" labelled column in the table field is displayed
        # decorator isHiddenOnMainField true
        And the "List Price" labelled column in the table field is hidden
        When the user clicks the "Open column panel" labelled button of the table field
        Then the "Column settings" titled sidebar is displayed
        And the table column configuration with name "Net Price" on the sidebar is ticked
        And the table column configuration with name "List Price" on the sidebar is unticked
        When the user ticks the table column configuration with "List Price" name on the sidebar
        And the user selects the "field" bound table field on the main page
        Then the "List Price" labelled column in the table field is displayed

    Scenario: As a applicative developer I want to handle unselection of pre-selected records
        XT-47699
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TableUnselectEvent/eyJfaWQiOiIxIn0="
        Then the "Provider 1" titled page is displayed
        And the user selects the "products" bound table field on the main page
        And the user selects the row 1 of the table field
        Then the selected row of the table field is selected
        When the user unticks the main checkbox of the selected row in the table field
        Then a toast containing text "Product "249" was unselected." is displayed
        And the user selects the "products" bound table field on the main page
        Then the selected row of the table field is unselected

    # To be reimplemented with saved views
    # Scenario: As a user I want hidden or displayed columns and the column configuration screen to be in sync
    #     XT-47944
    #     Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseProduct"
    #     And the user selects the "$navigationPanel" bound table field on the navigation panel
    #     Then the "Supplier" labelled column in the table field is hidden
    #     When the user clicks the "Open column panel" labelled button of the table field
    #     Then the table column configuration with name "Supplier" on the sidebar is unticked
    #     When the user ticks the table column configuration with "Supplier" name on the sidebar
    #     And the user selects the "$navigationPanel" bound table field on the navigation panel
    #     Then the "Supplier" labelled column in the table field is displayed
    #     Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseProduct"
    #     And the user selects the "$navigationPanel" bound table field on the navigation panel
    #     Then the "Supplier" labelled column in the table field is displayed
    #     When the user clicks the "Open column panel" labelled button of the table field
    #     Then the table column configuration with name "Supplier" on the sidebar is ticked

    Scenario: As a user I want to sort rows that I just added by the phanrom row
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseInvoice/eyJfaWQiOiIkbmV3In0="
        When the user selects the "Order" labelled reference field on the main page
        And the user writes "Nitzsche LLC" in the reference field
        And the user selects the "Order" labelled reference field on the main page
        And the user selects "Nitzsche LLC" in the reference field
        And the user selects the "Lines" labelled table field on the main page
        And the user selects the floating row of the table field
        And the user writes "Pepper - Black, Ground" in the "Product" labelled nested reference field of the selected row in the table field
        And the user presses Enter
        And the user selects the "Lines" labelled table field on the main page
        And the user writes "50" in the "Ordered Quantity" labelled nested text field of the selected row in the table field
        And the user presses Enter
        And the user presses Control+Enter
        And the user selects the "Lines" labelled table field on the main page
        And the user writes "Dried Apple" in the "Product" labelled nested reference field of the selected row in the table field
        And the user presses Enter
        And the user selects the "Lines" labelled table field on the main page
        And the user writes "40" in the "Ordered Quantity" labelled nested text field of the selected row in the table field
        And the user presses Control+Enter
        And the user selects the "Lines" labelled table field on the main page
        And the user writes "Snapple - Iced Tea Peach" in the "Product" labelled nested reference field of the selected row in the table field
        And the user presses Enter
        And the user selects the "Lines" labelled table field on the main page
        And the user writes "20" in the "Ordered Quantity" labelled nested text field of the selected row in the table field
        And the user waits 1 seconds
        And the user presses Control+Enter
        And the user waits 1 seconds
        And the user selects the "Lines" labelled table field on the main page
        When the user clicks the "product__product" bound column of the table field
        And the user selects the row 1 of the table field
        Then the value of the "Product" labelled nested reference field of the selected row in the table field is "Dried Apple"
        And the user selects the row 2 of the table field
        Then the value of the "Product" labelled nested reference field of the selected row in the table field is "Pepper - Black, Ground"
        And the user selects the row 3 of the table field
        Then the value of the "Product" labelled nested reference field of the selected row in the table field is "Snapple - Iced Tea Peach"

    Scenario: As a user I want to continously create new records using the tab key
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseInvoice/eyJfaWQiOiI1In0="
        And the "Invoice 5 05/19/2020" titled page is displayed
        And the user selects the "Lines" labelled table field on the main page
        And the user selects the floating row of the table field
        And the user writes "Apple - Macintosh" in the "Product" labelled nested reference field of the selected row in the table field
        And the user presses Enter
        And the user writes "32" in the "Ordered Quantity" labelled nested numeric field of the selected row in the table field
        And the user writes "32.34" in the "Net Price" labelled nested numeric field of the selected row in the table field
        And the user writes "Test value" in the "Comment" labelled nested text field of the selected row in the table field
        And the user presses Tab
        And the user waits 1 seconds
        And the user presses Tab
        And the user waits 1 seconds
        And the user selects the "Lines" labelled table field on the main page
        And the user writes "Arizona - Plum Green Tea" in the "Product" labelled nested reference field of the selected row in the table field
        And the user presses Enter
        And the user selects the "Lines" labelled table field on the main page
        And the user selects the floating row of the table field
        And the user writes "33" in the "Ordered Quantity" labelled nested numeric field of the selected row in the table field
        And the user writes "33.34" in the "Net Price" labelled nested numeric field of the selected row in the table field
        And the user writes "Test value" in the "Comment" labelled nested text field of the selected row in the table field
        And the user presses Tab
        And the user waits 1 seconds
        And the user presses Tab
        And the user waits 1 seconds
        And the user selects the "Lines" labelled table field on the main page
        And the user selects the row 2 of the table field
        And the value of the "Product" labelled nested reference field of the selected row in the table field is "Apple - Macintosh"
        And the value of the "Ordered Quantity" labelled nested numeric field of the selected row in the table field is "32"
        And the user selects the row 3 of the table field
        And the value of the "Product" labelled nested reference field of the selected row in the table field is "Arizona - Plum Green Tea"
        And the value of the "Ordered Quantity" labelled nested numeric field of the selected row in the table field is "33"

    Scenario: As a user I want to interact with the custom item actions
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TableWithSidebar/eyJfaWQiOiIyIn0="
        Then the "Field - Table - With sidebar" titled page is displayed
        And the user selects the "field" bound table field on the main page
        Then the user clicks the "Custom add line action" labelled add action of the table field
        Then a toast with text "Custom action is triggered!!" is displayed
        And the user dismisses all the toasts
        And the user waits 1 seconds
        Then the user clicks the "customAddLine" bound add action of the table field
        Then a toast with text "Custom action is triggered!!" is displayed
        And the user dismisses all the toasts

    Scenario: As a user I want to perform the actions in the submenus of a table
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/RowActions"
        Then the "Row actions" titled page is displayed
        When selects the "Unique actions" labelled navigation anchor on the main page
        When the user selects the "table6" bound table field on the main page
        And the user selects the row with text "a" in the "mandatory" bound column header of the table field
        And the user clicks the "Add" dropdown action of the selected row of the table field
        And the user clicks the "Add multiple" submenu option of the more actions dropdown of the selected row in the table field
        And the user clicks the "Add two" submenu option of the more actions dropdown of the selected row in the table field
        Then a success dialog appears on the main page
        And the user dismisses all the toasts

    Scenario: User wants clear all added filter
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TableWithOptionsMenu/eyJfaWQiOiIyIn0="
        And the user selects the "field" bound table field on the main page
        When the user clicks the "Show table filters" labelled button of the table field
        Then the floating filter value of the "product" bound column of the table field is ""
        When the user filters the "product" bound column in the table field with value "wine"
        And the user selects the row 1 of the table field
        Then the value of the "product" bound nested text field of the selected row in the table field is "Cheese - Wine"
        When the user clicks the remove all filters button in the table field
        Then the value of the "product" bound nested text field of the selected row in the table field is "Appetiser - Bought"

    Scenario: As a user I want to retain the group configuration when I change the floating filter config
        XT-53421
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-show-case/ShowCaseProduct"
        Then the "Products" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        And the value of the "Product" labelled nested text field of the selected row in the table field is "Anisette - Mcguiness"
        And the value of the "Category" labelled nested label field of the selected row in the table field is "Good"
        Then the group by option in the header menu of the "Category" labelled column of the table field is displayed
        Then the user clicks the group by option in the header menu of the "Category" labelled column of the table field
        And the value of the "Category" labelled nested label field of the selected row in the table field is "No value (26)"
        And the user selects the row 2 of the table field
        And the value of the "Category" labelled nested label field of the selected row in the table field is "Awful (11)"
        Then the user clicks the "Hide table filters" labelled button of the table field
        And the user selects the row 1 of the table field
        And the value of the "Category" labelled nested label field of the selected row in the table field is "No value (26)"
        And the user selects the row 2 of the table field
        And the value of the "Category" labelled nested label field of the selected row in the table field is "Awful (11)"
        Then the user clicks the "Show table filters" labelled button of the table field
        And the user selects the row 1 of the table field
        And the value of the "Category" labelled nested label field of the selected row in the table field is "No value (26)"
        And the user selects the row 2 of the table field
        And the value of the "Category" labelled nested label field of the selected row in the table field is "Awful (11)"

    Scenario: As a user I want to keep the grouping configuration on the table when I change the options menu
        XT-55168
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/NavigationPanelWithDataOptions"
        Then the "Products" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row 1 of the table field
        Then the value of the "Provider" labelled nested reference field of the selected row in the table field is "Ali Express"
        When the user clicks the group by option in the header menu of the "Provider" labelled column of the table field
        Then the value of the "Provider" labelled nested reference field of the selected row in the table field is "Ali Express (8)"
        When the user selects the "Negative Categories" dropdown option in the navigation panel
        Then the value of the "Provider" labelled nested reference field of the selected row in the table field is "Ali Express (2)"
        When the user selects the "All Categories" dropdown option in the navigation panel
        Then the value of the "Provider" labelled nested reference field of the selected row in the table field is "Ali Express (10)"

    Scenario: As a user I want to keep the filter that is applied to the table after grouping and clicked the refresh button
        XT-55750
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseProduct"
        Then the "Products" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        Then the group by option in the header menu of the "Category" labelled column of the table field is displayed
        Then the user clicks the group by option in the header menu of the "Category" labelled column of the table field
        And the user selects the row 1 of the table field
        And the value of the "Category" labelled nested label field of the selected row in the table field is "No value (26)"
        And the user clicks the "Hide table filters" labelled button of the table field
        When the user filters the "product" bound column in the table field with value "beer"
        And the value of the "Category" labelled nested label field of the selected row in the table field is "No value (1)"
        And the user clicks the "Refresh" labelled button of the table field
        And the value of the "Category" labelled nested label field of the selected row in the table field is "No value (1)"
        And the user opens the filter of the "product" bound column in the table field
        Then the search value of the filter in the table field is "beer"

    Scenario: As a tester I want to make sure the filter button works on mobile tables
        Given the user opens the application on a mobile using the following link: "@sage/xtrem-show-case/ShowCaseProvider/eyJfaWQiOiIkbmV3In0="
        Then the "Provider" titled page is displayed
        When the user selects the "products" bound table field on the main page
        And the user clicks the "Open filter" labelled button of the table field
        Then a success dialog appears on a full width modal


    Scenario: As a user I want to able to navigate with the the keyboard in the table header
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseProvider"
        Then the "Providers" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        Then the user selects all rows of the table field
        Then the bulk action bar of the table field has 5 items
        Then the user selects all rows of the table field
        And the user presses Tab
        Then the user checks if the "Name" labelled column in the table field header is focused


    Scenario: As a user I want to check is the phantom row loaded the right default value
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TableWithSidebar/eyJfaWQiOiIyIn0="
        Then the "Field - Table - With sidebar" titled page is displayed
        And the user selects the "field" bound table field on the main page
        And the user selects the floating row of the table field
        Then the value of the "Provider" labelled nested reference field of the selected row in the table field is "Amazon"

    Scenario: As an ATP XTreeM user I can verify the pagin information of the data table field
        XT-59072
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TableWithOptionsMenu/eyJfaWQiOiIyIn0="
        And the user selects the "field" bound table field on the main page
        Then the First page button of the table field is disabled
        Then the Last page button of the table field is disabled
        Then the Previous page button of the table field is disabled
        Then the Next page button of the table field is enabled
        Then the page number of the table field is "Page 1 of more"
        Then the summary row paging information of the table field is "1 to 20 of more"
        And the user clicks the next page button of the table field
        Then the page number of the table field is "Page 2 of more"
        Then the summary row paging information of the table field is "21 to 40 of more"

    Scenario: As a user I want the Excel/CSV export to load all available data
        XT-67706
        XT-67578
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseProduct"
        Then the "Products" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user clicks the excel export button of the table field
        And the user selects the row 50 of the table field
        Then the value of the "Product" labelled nested text field of the selected row in the table field is "Bread - Raisin"

    Scenario: As a user I expect I can infinite paginate my main list after set a order by column
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/StandardShowCaseProduct"
        Then the "Products" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        When the user clicks the "Product" labelled column of the table field
        And the user selects the row 73 of the table field
        Then the value of the "Product" labelled nested reference field of the selected row in the table field is "Tomatoes - Grape"

    @generic_date
    Scenario Outline: User wants to use relative date filter in table
        XT-82098
        # Set date for a product so it can be seen when filtering using relative date filter
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseProduct/eyJfaWQiOiIzNjkifQ=="
        And the user selects the "Release date" labelled date field on the main page
        And the user writes a generated date in the date field with value <DateInRange>
        And the user clicks the "Save" labelled business action button on the main page

        # Set date for a product outside of the checked period
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseProduct/eyJfaWQiOiIyODEifQ=="
        And the user selects the "Release date" labelled date field on the main page
        And the user writes a generated date in the date field with value <DateOutOfRange1>
        And the user clicks the "Save" labelled business action button on the main page

        # Set date for a product outside of the checked period
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseProduct/eyJfaWQiOiIxMjQifQ=="
        And the user selects the "Release date" labelled date field on the main page
        And the user writes a generated date in the date field with value <DateOutOfRange2>
        And the user clicks the "Save" labelled business action button on the main page

        # Filter table and check result
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TableWithOptionsMenu/eyJfaWQiOiIyIn0="
        And the user selects the "field" bound table field on the main page
        And the user selects the row 1 of the table field
        Then the value of the "Product" labelled nested date field of the selected row in the table field is "Appetiser - Bought"
        When the user clicks the "Show table filters" labelled button of the table field
        And the user filters the "Date" labelled column in the table field with filter type <Filter>
        Then the value of the "Product" labelled nested date field of the selected row in the table field is "Cake Sheet Combo Party Pack"

        Examples:
            | Filter           | DateInRange  | DateOutOfRange1 | DateOutOfRange2 |
            | "Previous day"   | "M/(T-1)/Y"  | "M/(T-2)/Y"     | "M/T/Y"         |
            | "Current day"    | "M/T/Y"      | "M/(T-1)/Y"     | "M/(T+1)/Y"     |
            | "Previous week"  | "M/(T-7)/Y"  | "M/T/Y"         | "M/(T-14)/Y"    |
            | "Current week"   | "M/T/Y"      | "M/(T-7)/Y"     | "M/(T+7)/Y"     |
            | "Next day"       | "M/(T+1)/Y"  | "M/(T+2)/Y"     | "M/T/Y"         |
            | "Next week"      | "M/(T+7)/Y"  | "M/T/Y"         | "M/T+14/Y"      |
            | "Next month"     | "(M+1)/T/Y"  | "M/T/Y"         | "(M+2)/T/Y"     |
            | "Next year"      | "M/T/(Y+1)"  | "M/T/Y"         | "M/T/(Y+2)"     |
            | "Previous month" | "(M-1)/T/Y"  | "M/T/Y"         | "(M-2)/T/Y"     |
            | "Previous year"  | "M/T/(Y-1)"  | "M/T/Y"         | "M/T/(Y-2)"     |
            | "Current month"  | "M/T/Y"      | "(M-1)/T/Y"     | "(M+2)/T/Y"     |
            | "Current year"   | "M/T/Y"      | "M/T/(Y-1)"     | "M/T/(Y+1)Y"    |
            | "Last 7 days"    | "M/(T-7)/Y"  | "M/(T-8)/Y"     | "M/(T+1)/Y"     |
            | "Last 30 days"   | "M/(T-30)/Y" | "M/(T-31)/Y"    | "M/(T+1)/Y"     |

    Scenario: As a user I want my table field to preserve my table sorting settings on a table field
        XT-89728
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/StandardShowCaseProvider/eyJfaWQiOiIyIn0="
        Then the "Provider 2" titled page is displayed
        And the user selects the "products" bound table field on the main page
        And the user selects the row 1 of the table field
        Then the value of the "Product" labelled nested text field of the selected row in the table field is "Beef - Bresaola"
        And the user selects the row 2 of the table field
        Then the value of the "Product" labelled nested text field of the selected row in the table field is "Truffle - Peelings"
        And the user selects the row 3 of the table field
        Then the value of the "Product" labelled nested text field of the selected row in the table field is "Thyme - Dried"
        When the user clicks the "product" bound column of the table field
        And the user selects the row 1 of the table field
        Then the value of the "Product" labelled nested text field of the selected row in the table field is "Appetiser - Bought"
        And the user selects the row 2 of the table field
        Then the value of the "Product" labelled nested text field of the selected row in the table field is "Appetizer - Cheese Bites"
        And the user selects the row 3 of the table field
        Then the value of the "Product" labelled nested text field of the selected row in the table field is "Apple - Macintosh"
        Then the user navigates to the following link: "@sage/xtrem-show-case/StandardShowCaseProvider/eyJfaWQiOiIyIn0="
        Then the "Provider 2" titled page is displayed
        And the user selects the "products" bound table field on the main page
        And the user selects the row 1 of the table field
        Then the value of the "Product" labelled nested text field of the selected row in the table field is "Appetiser - Bought"
        And the user selects the row 2 of the table field
        Then the value of the "Product" labelled nested text field of the selected row in the table field is "Appetizer - Cheese Bites"
        And the user selects the row 3 of the table field
        Then the value of the "Product" labelled nested text field of the selected row in the table field is "Apple - Macintosh"

    Scenario: As a user I want my table field to preserve my column visibility settings on a table field
        XT-89728
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/StandardShowCaseProvider/eyJfaWQiOiIyIn0="
        Then the "Provider 2" titled page is displayed
        And the user selects the "products" bound table field on the main page
        Then the "Description" labelled column in the table field is displayed
        Then the "Hot" labelled column in the table field is displayed
        Then the "Category" labelled column in the table field is displayed
        When the user clicks the "Open column panel" labelled button of the table field
        Then the "Column settings" titled sidebar is displayed
        When the user unticks the table column configuration with "Description" name on the sidebar
        When the user unticks the table column configuration with "Hot" name on the sidebar
        When the user unticks the table column configuration with "Category" name on the sidebar
        Then the "Description" labelled column in the table field is hidden
        Then the "Hot" labelled column in the table field is hidden
        Then the "Category" labelled column in the table field is hidden
        Then the user navigates to the following link: "@sage/xtrem-show-case/StandardShowCaseProvider/eyJfaWQiOiIyIn0="
        Then the "Provider 2" titled page is displayed
        And the user selects the "products" bound table field on the main page
        Then the "Description" labelled column in the table field is hidden
        Then the "Hot" labelled column in the table field is hidden
        Then the "Category" labelled column in the table field is hidden

    Scenario: As a user, I want to make changes to the configuration of my tables and see my changes persisted

        # table sorting
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseInvoice/eyJfaWQiOiIxIn0="
        Then the "Invoice 1 06/25/2020" titled page is displayed

        When the user selects the "Lines" labelled table field on the main page
        And the user selects the row 1 of the table field
        Then the value of the "Product" labelled nested text field of the selected row in the table field is "Vinegar - White Wine"

        When the user clicks the "Product" labelled column of the table field
        And the user selects the row 1 of the table field
        Then the value of the "Product" labelled nested text field of the selected row in the table field is "Apricots Fresh"

        When the user navigates to the following link: "@sage/xtrem-show-case/ShowCaseInvoice/eyJfaWQiOiIxIn0="
        Then the "Invoice 1 06/25/2020" titled page is displayed

        When the user clicks the "Product" labelled column of the table field
        And the user selects the row 1 of the table field
        Then the value of the "Product" labelled nested text field of the selected row in the table field is "Apricots Fresh"

        When the user navigates to the following link: "@sage/xtrem-show-case/ShowCaseInvoice/eyJfaWQiOiIxIn0="
        Then the "Invoice 1 06/25/2020" titled page is displayed
        When the user selects the "Lines" labelled table field on the main page
        And the user selects the row 1 of the table field
        Then the value of the "Product" labelled nested text field of the selected row in the table field is "Apricots Fresh"

        # hide a column
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseInvoice/eyJfaWQiOiIzIn0="
        Then the "Invoice 3 02/06/2020" titled page is displayed

        When the user selects the "Lines" labelled table field on the main page
        Then the "Comment" labelled column in the table field is displayed

        When the user clicks the "Open column panel" labelled button of the table field
        Then the "Column settings" titled sidebar is displayed

        When the user unticks the table column configuration with "Comment" name on the sidebar
        Then the table column configuration with name "Comment" on the sidebar is unticked

        When the user clicks the Close button of the dialog on the sidebar
        And the user selects the "Lines" labelled table field on the main page
        Then the "Comments" labelled column in the table field is hidden

        When the user navigates to the following link: "@sage/xtrem-show-case/ShowCaseInvoice/eyJfaWQiOiIzIn0="
        Then the "Invoice 3 02/06/2020" titled page is displayed

        When the user selects the "Lines" labelled table field on the main page
        Then the "Comment" labelled column in the table field is hidden

        # display a column
        When the user clicks the "Open column panel" labelled button of the table field
        Then the "Column settings" titled sidebar is displayed

        When the user ticks the table column configuration with "Comment" name on the sidebar
        Then the table column configuration with name "Comment" on the sidebar is ticked

        When the user clicks the Close button of the dialog on the sidebar
        And the user selects the "Lines" labelled table field on the main page
        Then the "Comment" labelled column in the table field is displayed

        When the user navigates to the following link: "@sage/xtrem-show-case/ShowCaseInvoice/eyJfaWQiOiIzIn0="
        Then the "Invoice 3 02/06/2020" titled page is displayed

        When the user selects the "Lines" labelled table field on the main page
        Then the "Comment" labelled column in the table field is displayed
