Feature: 3-1 Page fragment
    # Tests the page fragment component functionality, verifying that reusable UI segments are properly displayed, loaded, and integrated within pages

    Scenario: As a tester I want to check page fragment works as expected
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseProductWithFragment/eyJfaWQiOiIxMDYifQ=="
        Then the "Product 106" titled page is displayed
        And the "Product designed by" labelled reference field on the main page is displayed
        And the "List price" labelled numeric field on the main page is displayed
        And the "Amount" labelled numeric field on the main page is displayed
        And the "Discount" labelled numeric field on the main page is displayed
        And the "Net Price" labelled numeric field on the main page is displayed
        And the "Tax" labelled numeric field on the main page is displayed
        When the user clicks the "Validate prices" labelled button on the main page
        And the user selects the "Amount" labelled numeric field on the main page
        Then the numeric field is invalid
        When the user clicks the "Show prices" labelled button on the main page
        Then the "Product designed by" labelled reference field on the main page is hidden
        And the "List price" labelled numeric field on the main page is hidden
        And the "Amount" labelled numeric field on the main page is hidden
        And the "Discount" labelled numeric field on the main page is hidden
        And the "Net Price" labelled numeric field on the main page is hidden
        And the "Tax" labelled numeric field on the main page is hidden
        When the user clicks the "Show prices" labelled button on the main page
        Then the "Product designed by" labelled reference field on the main page is displayed
        And the "List price" labelled numeric field on the main page is displayed
        And the "Amount" labelled numeric field on the main page is displayed
        And the "Discount" labelled numeric field on the main page is displayed
        And the "Net Price" labelled numeric field on the main page is displayed
        And the "Tax" labelled numeric field on the main page is displayed
