Feature: 5 Y dashboard widget tile indicator verification
    # Tests the verification of indicator tile widget values and appearance, ensuring correct display of metrics and threshold-based indicators

    @ClearDashboards
    Scenario: As an ATP XTreeM user I can verify the tile-indicator widget
        XT-33332
        Given the user opens the application on a desktop
        #Create the dashboard
        And the dashboard page is displayed
        Then the "Create a dashboard to get started." subtitled empty dashboard is displayed
        When the user clicks the create button on the dashboard

        #Select showcase dashboard
        When the user selects the template with title "Showcase dashboard" in the dashboard creation dialog
        Then the template with title "Showcase dashboard" in the dashboard creation dialog is selected
        When the user clicks the "Next" button in the dashboard creation dialog

        Then the "Showcase dashboard" titled dashboard in the dashboard editor is displayed

        When the user clicks the "createAWidget" labelled button in the dashboard editor navigation panel
        Then the "New widget" titled widget editor dialog is displayed
        And the value of the step title of the widget editor dialog is "1. Select a widget to get started"
        And the user writes "Number of Employee" in the "basic-title" text field in the widget editor dialog
        When the user writes "My demo category" in the "basic-category" dropdown field in the widget editor dialog
        And the user presses Tab
        And the user writes "Show case employee" in the "basic-node" dropdown field in the widget editor dialog
        And the user presses Tab
        And the user selects the "INDICATOR_TILE" widget card in the widget editor dialog
        And the user clicks the "next" button in the widget editor dialog

        # Data selection step
        Then the value of the step title of the widget editor dialog is "2. Select the data to add to your widget"
        When the user searches for "Email" in the widget editor dialog
        Then the "Email" tree-view element in the widget editor dialog is displayed
        When the user selects the "Email" tree-view element in the widget editor dialog
        And the user clicks the "next" button in the widget editor dialog

        # Content selection step
        Then the value of the step title of the widget editor dialog is "3. Add your content"
        When the user writes "Email" in the "grouping-property" dropdown field in the widget editor dialog
        And the user presses Tab
        And the user clicks the "next" button in the widget editor dialog

        # Filter selection step
        Then the value of the step title of the widget editor dialog is "4. Add your filters"
        And the user clicks the "next" button in the widget editor dialog

        # Layout selection step
        Then the value of the step title of the widget editor dialog is "5. Create your layout"
        When the user writes "Payroll" in the "layout-icon" dropdown field in the widget editor dialog
        And the user presses Tab

        # saving the created widget
        When the user clicks the "add" button in the widget editor dialog
        Then the "Showcase dashboard" titled dashboard in the dashboard editor is displayed
        And the "Number of Employee" titled widget in the dashboard editor is displayed
        And the user waits 1 second
        When the user clicks the "Save" button in the dashboard editor footer
        Then a toast containing text "Dashboard saved." is displayed
        When the user dismisses all the toasts
        Then the user clicks the "Edit" labelled CRUD button in the dashboard action menu

        #Verify the tile indicator in the dashboard editor
        And the user selects the "Number of Employee" titled tile-indicator widget field in the dashboard editor
        Then the value of the tile-indicator widget field is "1,000"
        # And takes a screenshot

        And the user clicks the "Cancel" button in the dashboard editor footer
        Then the "Showcase dashboard" titled dashboard is displayed

        #Verify the tile indicator in the dashboard
        And the user selects the "Number of Employee" titled tile-indicator widget field in the dashboard
        Then the value of the tile-indicator widget field is "1,000"
