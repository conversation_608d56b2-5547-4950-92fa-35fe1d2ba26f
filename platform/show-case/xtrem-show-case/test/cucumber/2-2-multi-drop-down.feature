Feature: 2-2 Multi drop down
   # Tests the multi dropdown component across different devices, verifying multiple selection capability, value display, interaction with the dropdown list and control the multi dropdown state.

   Scenario Outline: <Device> - As an ATP XTreeM User I can select and verify the selected option of the multi drop-down list field using label
      XT-25838
      Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/MultiDropdown"
      Then the "Field - Multi-dropdown List" titled page is displayed
      Given the user selects the "Title" labelled text field on the main page
      When the user writes "Sample title" in the text field
      And the user presses Tab
      Given the user selects the "Sample title" labelled multi dropdown field on the main page
      When the user clicks in the multi dropdown field
      Then at least the following list of options is displayed for the multi dropdown field: "Five |  Four |  One"
      And the user selects "Five | Four" in the multi dropdown field
      Then the value of the multi dropdown field is "Five, Four"
      And the user clears the multi dropdown field
      Examples:
         | Device  |
         | desktop |
         | tablet  |
         | mobile  |

   Scenario Outline: <Device> - As an ATP XTreeM User I can select and verify the selected option of the multi drop-down list field using bind
      XT-25838
      Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/MultiDropdown"
      Then the "Field - Multi-dropdown List" titled page is displayed
      Given the user selects the "field1" bound multi dropdown field on the main page
      When the user clicks in the multi dropdown field
      And the user selects "Five | Four" in the multi dropdown field
      Then the value of the multi dropdown field is "Five, Four"
      And the user clears the multi dropdown field
      Examples:
         | Device  |
         | desktop |
         | tablet  |
         | mobile  |

   Scenario Outline: <Device> - As an ATP XTreeM User I can scroll to the multi drop-down list field
      XT-25838
      Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/MultiDropdown"
      Then the "Field - Multi-dropdown List" titled page is displayed
      Given the user selects the "field1" bound multi dropdown field on the main page
      When the user scrolls to the multi dropdown field
      Examples:
         | Device  |
         | desktop |
         | tablet  |
         | mobile  |

   Scenario: As an ATP XTreeM User I can set the focus on the multi dropdown field
      XT-25838
      Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/MultiDropdown"
      Then the "Field - Multi-dropdown List" titled page is displayed
      When the user clicks in the "focus" bound button field on the main page
      Given the user selects the "field1" bound multi dropdown field on the main page
      Then the focus on the multi dropdown field is visible

   Scenario Outline: <Device> - As and ATP XTreeM user I can verify the multi drop-down list field title
      XT-25838
      Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/MultiDropdown"
      Then the "Field - Multi-dropdown List" titled page is displayed
      Given the user selects the "Title" labelled text field on the main page
      When the user writes "Sample title" in the text field
      And the user presses Tab
      Given the user selects the "field1" bound multi dropdown field on the main page
      When the user clicks in the multi dropdown field
      Then the title of the multi dropdown field is "Sample title"
      Then the title of the multi dropdown field is displayed
      Given the user selects the "Is Title Hidden" labelled checkbox field on the main page
      When the user ticks the checkbox field
      Given the user selects the "field1" bound multi dropdown field on the main page
      Then the title of the multi dropdown field is hidden
      Examples:
         | Device  |
         | desktop |
         | tablet  |
         | mobile  |

   Scenario Outline: <Device> - As and ATP XTreeM user I can verify the multi drop-down list field helper text
      XT-25838
      Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/MultiDropdown"
      Then the "Field - Multi-dropdown List" titled page is displayed
      Given the user selects the "Helper Text" labelled text field on the main page
      When the user writes "Sample text" in the text field
      And the user presses Tab
      Given the user selects the "field1" bound multi dropdown field on the main page
      When the user clicks in the multi dropdown field
      Then the helper text of the multi dropdown field is "Sample text"
      Then the helper text of the multi dropdown field is displayed
      Given the user selects the "Is Helper Text Hidden" labelled checkbox field on the main page
      When the user ticks the checkbox field
      Given the user selects the "field1" bound multi dropdown field on the main page
      Then the helper text of the multi dropdown field is hidden
      Examples:
         | Device  |
         | desktop |
         | tablet  |
         | mobile  |

   Scenario Outline: <Device> - As and ATP XTreeM user I can verify if the multi drop-down list field is displayed or hidden
      XT-25838
      Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/MultiDropdown"
      Then the "Field - Multi-dropdown List" titled page is displayed
      Given the user selects the "field1" bound multi dropdown field on the main page
      When the user clicks in the multi dropdown field
      Then the "field1" bound multi dropdown field on the main page is displayed
      Given the user selects the "Is Hidden" labelled checkbox field on the main page
      When the user ticks the checkbox field
      Then the "field1" bound multi dropdown field on the main page is hidden
      Examples:
         | Device  |
         | desktop |
         | tablet  |
         | mobile  |

   Scenario Outline:<Device> - As and ATP XTreeM user I can verify if the multi drop-down list field is enabled or disabled
      XT-25838
      Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/MultiDropdown"
      Then the "Field - Multi-dropdown List" titled page is displayed
      Given the user selects the "field1" bound multi dropdown field on the main page
      Then the multi dropdown field is enabled
      Given the user selects the "Is Disabled" labelled checkbox field on the main page
      When the user ticks the checkbox field
      Given the user selects the "field1" bound multi dropdown field on the main page
      Then the multi dropdown field is disabled
      Examples:
         | Device  |
         | desktop |
         | tablet  |
         | mobile  |

   Scenario Outline: <Device> - As and ATP XTreeM user I can verify if the multi drop-down list field is read-only
      XT-25838
      Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/MultiDropdown"
      Then the "Field - Multi-dropdown List" titled page is displayed
      Given the user selects the "Is Read-Only" labelled checkbox field on the main page
      When the user ticks the checkbox field
      Given the user selects the "field1" bound multi dropdown field on the main page
      Then the multi dropdown field is read-only
      Examples:
         | Device  |
         | desktop |
         | tablet  |
         | mobile  |

   Scenario Outline: As a user I want to verify if min and max validations are working for the multi dropdown field
      Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/MultiDropdown"
      Then the "Field - Multi-dropdown List" titled page is displayed
      When the user selects the "Set Minimum of Selectable Items" labelled numeric field on the main page
      And the user writes "2" in the numeric field
      And the user selects the "Set Maximum of Selectable Items" labelled numeric field on the main page
      And the user writes "4" in the numeric field
      And the user selects the "With Options" labelled multi dropdown field on the main page
      And the user clicks in the multi dropdown field
      And the user selects "One" in the multi dropdown field
      Then the "With Options the minimum number of selectable items is 2" validation error message of the multi dropdown field is displayed
      When the user selects "Two" in the multi dropdown field
      Then the "With Options the minimum number of selectable items is 2" validation error message of the multi dropdown field is hidden
      And the user selects "Three | Four" in the multi dropdown field
      Then the "With Options the maximum number of selectable items is 4" validation error message of the multi dropdown field is hidden
      And the user selects "Five" in the multi dropdown field
      Then the "With Options the maximum number of selectable items is 4" validation error message of the multi dropdown field is displayed
      When the user clears the multi dropdown field
      And the user waits 1 second
      Then the "With Options the minimum number of selectable items is 2" validation error message of the multi dropdown field is displayed
      Examples:
         | Device  |
         | desktop |
         | tablet  |
         | mobile  |
