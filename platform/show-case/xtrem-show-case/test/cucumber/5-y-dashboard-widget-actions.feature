Feature: 5 Y dashboard widget actions
    # Tests the interaction capabilities with dashboard widgets, focusing on actions available through the widget header menu or in the widget.

    @ClearDashboardsBefore
    @ClearDashboards
    Scenario: As an ATP XTreeM user I can interact with the widget more action header button in the dashboard
        XT-33280 XT-33282

        Given the user opens the application on a desktop

        #Create the dashboard
        And the dashboard page is displayed
        Then the "Create a dashboard to get started." subtitled empty dashboard is displayed
        When the user clicks the create button on the dashboard

        #Select showcase dashboard
        When the user selects the template with title "Showcase dashboard" in the dashboard creation dialog
        Then the template with title "Showcase dashboard" in the dashboard creation dialog is selected

        When the user clicks the "Next" button in the dashboard creation dialog

        #Verify the widgets in the Dashboard editor
        Then the "Showcase dashboard" titled dashboard in the dashboard editor is displayed

        #Save the dashboard
        When the user clicks the edit dashboard title icon
        And the user writes "My Showcase dashboard" in the dashboard title
        And the user presses Enter
        And the user clicks the "Save" button in the dashboard editor footer
        Then a toast containing text "Dashboard saved." is displayed
        Then the "My Showcase dashboard" titled dashboard is displayed

        #basic widget
        When the user selects the "List of users" titled basic widget field in the dashboard
        Then the "Refresh" more actions button in the header of the basic widget field is enabled
        Then the "Refresh" more actions button in the header of the basic widget field is displayed
        And the user clicks the "Refresh" more actions button in the header of the basic widget field

        #gauge widget
        When the user selects the "Ratio of administrators" titled gauge widget field in the dashboard
        Then the "Refresh" more actions button in the header of the gauge widget field is enabled
        Then the "Refresh" more actions button in the header of the gauge widget field is displayed
        And the user clicks the "Refresh" more actions button in the header of the gauge widget field

        #tile-indicator widget
        When the user selects the "Number of users" titled tile-indicator widget field in the dashboard
        Then the "Refresh" more actions button in the header of the tile-indicator widget field is enabled
        Then the "Refresh" more actions button in the header of the tile-indicator widget field is displayed
        And the user clicks the "Refresh" more actions button in the header of the tile-indicator widget field

        #table widget
        When the user selects the "Users" titled table widget field in the dashboard
        Then the "Refresh" more actions button in the header of the table widget field is enabled
        Then the "Refresh" more actions button in the header of the table widget field is displayed
        And the user clicks the "Refresh" more actions button in the header of the table widget field

        #line-chart widget
        When the user selects the "Products" titled line-chart widget field in the dashboard
        Then the "Refresh" more actions button in the header of the line-chart widget field is enabled
        Then the "Refresh" more actions button in the header of the line-chart widget field is displayed
        And the user clicks the "Refresh" more actions button in the header of the line-chart widget field

        #bar-chart widget
        When the user selects the "Products" titled bar-chart widget field in the dashboard
        Then the "Refresh" more actions button in the header of the bar-chart widget field is enabled
        Then the "Refresh" more actions button in the header of the bar-chart widget field is displayed
        And the user clicks the "Refresh" more actions button in the header of the bar-chart widget field

        #visual-process widget
        When the user selects the "Suppliers" titled visual-process widget field in the dashboard
        Then the "Refresh" more actions button in the header of the visual-process widget field is enabled
        Then the "Refresh" more actions button in the header of the visual-process widget field is displayed
        And the user clicks the "Refresh" more actions button in the header of the visual-process widget field

    @ClearDashboardsBefore
    @ClearDashboards
    Scenario: As an ATP XTreeM user I can interact with the widget more action header button in the dashboard editor
        XT-33280 XT-33282

        Given the user opens the application on a desktop

        #Create the dashboard
        And the dashboard page is displayed
        Then the "Create a dashboard to get started." subtitled empty dashboard is displayed
        When the user clicks the create button on the dashboard

        #Select showcase dashboard
        When the user selects the template with title "Showcase dashboard" in the dashboard creation dialog
        Then the template with title "Showcase dashboard" in the dashboard creation dialog is selected
        When the user clicks the "Next" button in the dashboard creation dialog

        #Verify the widgets in the Dashboard editor
        Then the "Showcase dashboard" titled dashboard in the dashboard editor is displayed

        #Save the dashboard
        When the user clicks the edit dashboard title icon
        And the user writes "My Showcase dashboard Editor" in the dashboard title
        And the user presses Enter
        And the user clicks the "Save" button in the dashboard editor footer
        Then a toast containing text "Dashboard saved." is displayed
        Then the "My Showcase dashboard Editor" titled dashboard is displayed
        When the user clicks the "Edit" labelled CRUD button in the dashboard action menu
        Then the "My Showcase dashboard Editor" titled dashboard in the dashboard editor is displayed

        #basic widget
        When the user selects the "List of users" titled basic widget field in the dashboard editor
        Then the "Refresh" more actions button in the header of the basic widget field is hidden

        #gauge widget
        When the user selects the "Ratio of administrators" titled gauge widget field in the dashboard editor
        Then the "Refresh" more actions button in the header of the gauge widget field is hidden

        #tile-indicator widget
        And the user selects the "Number of users" titled tile-indicator widget field in the dashboard editor
        And the "Refresh" more actions button in the header of the tile-indicator widget field is hidden

        #table widget
        When the user selects the "Users" titled table widget field in the dashboard editor
        Then the "Refresh" more actions button in the header of the table widget field is hidden

        #line-chart widget
        When the user selects the "Products" titled line-chart widget field in the dashboard editor
        Then the "Refresh" more actions button in the header of the line-chart widget field is hidden

        #bar-chart widget
        When the user selects the "Products" titled bar-chart widget field in the dashboard editor
        Then the "Refresh" more actions button in the header of the bar-chart widget field is hidden

        #visual-process widget
        When the user selects the "Suppliers" titled visual-process widget field in the dashboard editor
        Then the "Refresh" more actions button in the header of the visual-process widget field is hidden

    @ClearDashboardsBefore
    @ClearDashboards
    Scenario: As an ATP XTreeM user I can interact with the widget button in the dashboard
        XT-38894

        Given the user opens the application on a desktop

        #Create the dashboard
        And the dashboard page is displayed
        Then the "Create a dashboard to get started." subtitled empty dashboard is displayed
        When the user clicks the create button on the dashboard

        #Select showcase dashboard
        When the user selects the template with title "Showcase dashboard" in the dashboard creation dialog
        Then the template with title "Showcase dashboard" in the dashboard creation dialog is selected
        When the user clicks the "Next" button in the dashboard creation dialog

        #Verify the widgets in the Dashboard editor
        Then the "Showcase dashboard" titled dashboard in the dashboard editor is displayed

        #Save the dashboard
        When the user clicks the edit dashboard title icon
        And the user writes "My Showcase dashboard" in the dashboard title
        And the user presses Enter
        And the user clicks the "Save" button in the dashboard editor footer
        Then a toast containing text "Dashboard saved." is displayed
        Then the "My Showcase dashboard" titled dashboard is displayed

        #Interact with widget action
        When the user selects the "Users" titled table widget field in the dashboard
        Then the "Disabled action" button of the table widget field is disabled
        Then the "Disabled action" button of the table widget field is displayed

        When the user selects the "Products" titled line-chart widget field in the dashboard
        Then the "Action" button of the line-chart widget field is enabled
        Then the "Action" button of the line-chart widget field is displayed
        And the user clicks the "Action" button of the line-chart widget field

        When the user selects the "Products" titled bar-chart widget field in the dashboard
        Then the "Action" button of the bar-chart widget field is enabled
        Then the "Action" button of the bar-chart widget field is displayed
        And the user clicks the "Action" button of the bar-chart widget field

    @ClearDashboardsBefore
    @ClearDashboards
    Scenario: As an ATP XTreeM user I can interact with the widget button in the dashboard editor
        XT-38894

        Given the user opens the application on a HD desktop

        #Create the dashboard
        And the dashboard page is displayed
        Then the "Create a dashboard to get started." subtitled empty dashboard is displayed
        When the user clicks the create button on the dashboard

        #Select showcase dashboard
        When the user selects the template with title "Showcase dashboard" in the dashboard creation dialog
        Then the template with title "Showcase dashboard" in the dashboard creation dialog is selected
        When the user clicks the "Next" button in the dashboard creation dialog

        #Verify the widgets in the Dashboard editor
        Then the "Showcase dashboard" titled dashboard in the dashboard editor is displayed

        #Save the dashboard
        When the user clicks the edit dashboard title icon
        And the user writes "My Showcase dashboard Editor" in the dashboard title
        And the user presses Enter
        And the user clicks the "Save" button in the dashboard editor footer
        Then a toast containing text "Dashboard saved." is displayed
        Then the "My Showcase dashboard Editor" titled dashboard is displayed
        When the user clicks the "Edit" labelled CRUD button in the dashboard action menu
        Then the "My Showcase dashboard Editor" titled dashboard in the dashboard editor is displayed

        When the user selects the "Users" titled table widget field in the dashboard editor
        Then the "Disabled action" button of the table widget field is disabled
        Then the "Disabled action" button of the table widget field is displayed

        When the user selects the "Products" titled line-chart widget field in the dashboard editor
        Then the "Action" button of the line-chart widget field is enabled
        Then the "Action" button of the line-chart widget field is displayed
        And the user clicks the "Action" button of the line-chart widget field

        When the user selects the "Products" titled bar-chart widget field in the dashboard editor
        Then the "Action" button of the bar-chart widget field is enabled
        Then the "Action" button of the bar-chart widget field is displayed
        And the user clicks the "Action" button of the bar-chart widget field

        #cancel dashboard editor
        When the user clicks the "cancel" button in the dashboard editor footer
