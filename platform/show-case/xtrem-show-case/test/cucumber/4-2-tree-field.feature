Feature: 4-2 Tree field
        # Tests the hierarchical tree field component, verifying expand/collapse functionality and access to nested data within the tree structure
        Scenario: As a user I want to open and close a tree field
                Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseEmployee/eyJfaWQiOiIxIn0="
                Then the "Employee Perle Felkin" titled page is displayed
                And the user selects the "Team members" labelled tree field on the main page
                And the user selects the row 1 of the tree field
                Then the value of the "First name" labelled nested text field of the selected row in the tree field is "<PERSON><PERSON>"
                Then the value of the "Last name" labelled nested text field of the selected row in the tree field is "<PERSON>ley"
                When the user selects the row 3 of the tree field
                And the user expands the selected row of the tree field
                And the user selects the row 4 of the tree field
                Then the value of the "First name" labelled nested text field of the selected row in the tree field is "Yurik"
                Then the value of the "Last name" labelled nested text field of the selected row in the tree field is "Undy"
                When the user selects the row 5 of the tree field
                And the user expands the selected row of the tree field
                # We add these checks to scroll the table and enable row rendering as we gradually approach the target
                And the user selects the row 6 of the tree field
                Then the value of the "First name" labelled nested text field of the selected row in the tree field is "<PERSON><PERSON>"
                Then the value of the "Last name" labelled nested text field of the selected row in the tree field is "<PERSON>a"
                And the user selects the row 19 of the tree field
                Then the value of the "First name" labelled nested text field of the selected row in the tree field is "Lennie"
                Then the value of the "Last name" labelled nested text field of the selected row in the tree field is "Artist"
                And the user selects the row 29 of the tree field
                Then the value of the "First name" labelled nested text field of the selected row in the tree field is "Errick"
                Then the value of the "Last name" labelled nested text field of the selected row in the tree field is "Liggins"
                And the user selects the row 39 of the tree field
                Then the value of the "First name" labelled nested text field of the selected row in the tree field is "Nicolina"
                Then the value of the "Last name" labelled nested text field of the selected row in the tree field is "Rennebeck"
                And the user selects the row 49 of the tree field
                Then the value of the "First name" labelled nested text field of the selected row in the tree field is "Danika"

                Then the value of the "Last name" labelled nested text field of the selected row in the tree field is "Colbert"
                And the user selects the row 59 of the tree field
                Then the value of the "First name" labelled nested text field of the selected row in the tree field is "Nollie"
                Then the value of the "Last name" labelled nested text field of the selected row in the tree field is "Casemore"
                And the user selects the row 69 of the tree field
                Then the value of the "First name" labelled nested text field of the selected row in the tree field is "Carlotta"
                Then the value of the "Last name" labelled nested text field of the selected row in the tree field is "MacEveley"
                # Same backwards
                And the user selects the row 59 of the tree field
                Then the value of the "First name" labelled nested text field of the selected row in the tree field is "Nollie"
                Then the value of the "Last name" labelled nested text field of the selected row in the tree field is "Casemore"
                And the user selects the row 49 of the tree field
                Then the value of the "First name" labelled nested text field of the selected row in the tree field is "Danika"
                Then the value of the "Last name" labelled nested text field of the selected row in the tree field is "Colbert"
                And the user selects the row 39 of the tree field
                Then the value of the "First name" labelled nested text field of the selected row in the tree field is "Nicolina"
                Then the value of the "Last name" labelled nested text field of the selected row in the tree field is "Rennebeck"
                And the user selects the row 29 of the tree field
                Then the value of the "First name" labelled nested text field of the selected row in the tree field is "Errick"
                Then the value of the "Last name" labelled nested text field of the selected row in the tree field is "Liggins"
                And the user selects the row 19 of the tree field
                Then the value of the "First name" labelled nested text field of the selected row in the tree field is "Lennie"
                Then the value of the "Last name" labelled nested text field of the selected row in the tree field is "Artist"
                And the user selects the row 9 of the tree field
                Then the value of the "First name" labelled nested text field of the selected row in the tree field is "Brooks"
                Then the value of the "Last name" labelled nested text field of the selected row in the tree field is "Bennit"
                When the user selects the row 3 of the tree field
                And the user collapses the selected row of the tree field
                And the user selects the row 4 of the tree field
                Then the value of the "First name" labelled nested text field of the selected row in the tree field is "Aldon"
                Then the value of the "Last name" labelled nested text field of the selected row in the tree field is "McGrane"

        Scenario: As a user I want to be able to have the same child record on different branches of the tree
                Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TreeFieldErrorSpike/eyJfaWQiOiIxIn0="
                Then the "Tree Field Error" titled page is displayed
                And the user selects the "treeComponents" bound tree field on the main page
                When the user selects the row 2 of the tree field
                And the user expands the selected row of the tree field
                And the user selects the row 3 of the tree field
                Then the value of the "Item name" labelled nested text field of the selected row in the tree field is "Tire"
                When the user selects the row 1 of the tree field
                And the user expands the selected row of the tree field
                And the user selects the row 2 of the tree field
                Then the value of the "Item name" labelled nested text field of the selected row in the tree field is "Tire"
                When the user selects the row 4 of the tree field
                And the user expands the selected row of the tree field
                And the user selects the row 5 of the tree field
                Then the value of the "Item name" labelled nested text field of the selected row in the tree field is "Brake"
                When the user selects the row 2 of the tree field
                And the user expands the selected row of the tree field
                And the user selects the row 3 of the tree field
                Then the value of the "Item name" labelled nested text field of the selected row in the tree field is "Brake"
