Feature: 2-1 Graphiql plugin
    # Tests the GraphiQL plugin functionality, verifying proper integration of the GraphQL query editor and execution of GraphQL queries

    Scenario: As a user I want to add a graphiql editor to an existing graphiql plugin
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/PluginGraphiql/eyJfaWQiOiIxMDYifQ=="
        Then the "Field - GraphiQL Plugin" titled page is displayed

        Then the user opens the navigation panel
        And the user searches for "386" in the navigation panel
        And the user clicks the record with the text "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON> and <PERSON>" in the navigation panel
        And the user selects the "Id" labelled text field on the main page
        Then the value of the text field is "386"

        Given the user selects the "queryText" bound graphiql editor field on the main page
        When the user writes "-------Query Text--------" in the graphiql editor field

        Given the user selects the "Data Query" labelled graphiql editor field on the main page
        When the user writes "--This is a Query Text----" in the graphiql editor field

        Then the "queryText" bound graphiql editor field on the main page is displayed
        Then the "Data Query" labelled graphiql editor field on the main page is displayed


    Scenario: As a user I can verify if the graphiql editor field is read-only
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/PluginGraphiql/eyJfaWQiOiIxMDYifQ=="
        Then the "Field - GraphiQL Plugin" titled page is displayed

        Then the user opens the navigation panel
        And the user searches for "386" in the navigation panel
        And the user clicks the record with the text "Abshire; Gislason and Ortiz" in the navigation panel

        Given the user selects the "Is readOnly" labelled checkbox field on the main page
        When the user ticks the checkbox field

        Given the user selects the "queryText" bound graphiql editor field on the main page
        Then the graphiql editor field is read-only


    Scenario: as a an ATP XTreeM user I can write and verify the value of the graphiql editor field using label
        XT-25838
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/PluginGraphiql/eyJfaWQiOiIxMDYifQ=="
        Then the "Field - GraphiQL Plugin" titled page is displayed

        Then the user opens the navigation panel
        And the user searches for "386" in the navigation panel
        And the user clicks the record with the text "Abshire; Gislason and Ortiz" in the navigation panel
        And the user selects the "Id" labelled text field on the main page
        Then the value of the text field is "386"

        Given the user selects the "Data Query" labelled graphiql editor field on the main page
        When the user writes "query" in the graphiql editor field
        And the user inserts line 1 in the graphiql editor field
        And the user writes "fragment" to line 2 of the graphiql editor field
        Then the value of the graphiql editor field is "fragment query"
        And the user clears the graphiql editor field


# Scenario: As a an ATP XTreeM user I can write and verify the value of the graphiql editor field using bind
# XT-25838
#     Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/PluginGraphiql/eyJfaWQiOiIxMDYifQ=="
#     Then the user opens the navigation panel
#     And the user searches for "386" in the navigation panel
#     And the user clicks on the record with the text "Abshire; Gislason and Ortiz" in the navigation panel
#     And the value of the "Id" labelled text field on the main page is "386"

#     Given the user selects the "queryText" bound graphiql editor field on the main page
#     When the user writes "-------Another Query Text--------" in the graphiql editor field
#     Then the value of the graphiql editor field is "-------Another Query Text--------"
#     And the user clears the graphiql editor field
