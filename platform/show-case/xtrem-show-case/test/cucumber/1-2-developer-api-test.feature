Feature: 1-2 Developer Api Test
    # Tests developer API functionality, verifying service options can be correctly enabled/disabled and accessed through the developer interface

    Scenario: As a functional developer I want to check isServiceOptionEnabled is working
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/DeveloperApiTest"
        And the user selects the "showCaseOption1" labelled text field on the main page
        Then the value of the text field is "Disabled"

        And the user selects the "showCaseOption2" labelled text field on the main page
        Then the value of the text field is "Disabled"

        And the user selects the "showCaseOption3" labelled text field on the main page
        Then the value of the text field is "Enabled"
