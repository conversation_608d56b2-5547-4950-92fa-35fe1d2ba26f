Feature: 3-2 Switch
    # Tests the switch field component across different devices, verifying proper toggling between ON/OFF states and value persistence and control the switch field state.


    Scenario Outline: <Device> - As an ATP XTreeM User I can turn on or off the swithc field using label
        XT-25838
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Switch"
        Then the "Switch" titled page is displayed

        Given the user selects the "Title" labelled text field on the main page
        When the user writes "Sample title" in the text field
        And the user presses Tab

        Given the user selects the "Value" labelled switch field on the main page
        And the user turns the switch field "OFF"
        Then the switch field is set to "OFF"

        Given the user selects the "Sample title" labelled switch field on the main page
        When the user turns the switch field "ON"
        Then the switch field is set to "ON"
        When the user turns the switch field "OFF"
        Then the switch field is set to "OFF"
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |


    Scenario Outline: <Device> - As an ATP XTreeM User I can turn on or off the switch field using bind
        XT-25838
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Switch"
        Then the "Switch" titled page is displayed

        Given the user selects the "Value" labelled switch field on the main page
        And the user turns the switch field "OFF"
        Then the switch field is set to "OFF"

        Given the user selects the "field" bound switch field on the main page
        When the user turns the switch field "ON"
        Then the switch field is set to "ON"
        When the user turns the switch field "OFF"
        Then the switch field is set to "OFF"
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    #switch  field - set / check properties

    Scenario Outline: <Device> - As and ATP XTreeM user I can verify the switch field title
        XT-25838
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Switch"
        Then the "Switch" titled page is displayed

        Given the user selects the "Title" labelled text field on the main page
        When the user writes "Sample title" in the text field
        And the user presses Tab

        Given the user selects the "field" bound switch field on the main page
        Then the title of the switch field is "Sample title"
        Then the title of the switch field is displayed

        Given the user selects the "Is title hidden" labelled switch field on the main page
        When the user turns the switch field "ON"

        Given the user selects the "field" bound switch field on the main page
        Then the title of the switch field is hidden
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |


    Scenario Outline: <Device> - As and ATP XTreeM user I can verify the switch field helper text
        XT-25838
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Switch"
        Then the "Switch" titled page is displayed

        Given the user selects the "Helper text" labelled text field on the main page
        When the user writes "Sample text" in the text field
        And the user presses Tab

        Given the user selects the "field" bound switch field on the main page
        Then the helper text of the switch field is "Sample text"
        Then the helper text of the switch field is displayed

        Given the user selects the "Is helper text hidden" labelled switch field on the main page
        When the user turns the switch field "ON"
        Given the user selects the "field" bound switch field on the main page
        Then the helper text of the switch field is hidden
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |


    Scenario Outline: <Device> - As and ATP XTreeM user I can verify if the switch field is displayed or hidden
        XT-25838
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Switch"
        Then the "Switch" titled page is displayed

        Given the user selects the "field" bound switch field on the main page
        Then the "field" bound switch field on the main page is displayed

        Given the user selects the "Is hidden" labelled switch field on the main page
        When the user turns the switch field "ON"
        Then the "field" bound switch field on the main page is hidden
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |


    Scenario Outline:<Device> - As and ATP XTreeM user I can verify if the switch field is enabled or disabled
        XT-25838
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Switch"
        Then the "Switch" titled page is displayed

        Given the user selects the "field" bound switch field on the main page
        Then the switch field is enabled

        Given the user selects the "Is disabled" labelled switch field on the main page
        When the user turns the switch field "ON"

        Given the user selects the "field" bound switch field on the main page
        Then the switch field is disabled
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |


    Scenario Outline: <Device> - As and ATP XTreeM user I can verify if the switch field is read-only
        XT-25838
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Switch"
        Then the "Switch" titled page is displayed

        Given the user selects the "Is readOnly" labelled switch field on the main page
        When the user turns the switch field "ON"

        Given the user selects the "field" bound switch field on the main page
        Then the switch field is read-only
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |


    Scenario: Trigger custom change event handler
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Switch"
        Then the "Switch" titled page is displayed
        Given the user selects the "field" bound switch field on the main page
        When the user turns the switch field "ON"
        Then the switch field is set to "ON"

        Then  the "changeTriggered" bound label field on the main page is hidden

        Given the user selects the "field" bound switch field on the main page
        When the user turns the switch field "OFF"
        Then the switch field is set to "OFF"

        Then the "changeTriggered" bound label field on the main page is displayed


    Scenario: Trigger custom click event handler
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Switch"
        Then the "Switch" titled page is displayed

        And the "clickTriggered" bound label field on the main page is hidden

        Given the user selects the "field" bound switch field on the main page
        When the user turns the switch field "OFF"
        Then the switch field is set to "OFF"

        Then the "clickTriggered" bound label field on the main page is displayed


    Scenario Outline: <Label> Toggle switch buttons
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Switch"
        Then the "Switch" titled page is displayed

        Given the user selects the <Label> labelled switch field on the main page
        Then the switch field is set to "OFF"

        Given the user selects the <Label> labelled switch field on the main page
        When the user turns the switch field "ON"
        Then the switch field is set to "ON"
        Examples:
            | Label                   |
            | "Is disabled"           |
            | "Is large"              |
            | "Is helper text hidden" |
            | "Is hidden"             |
            | "Is readOnly"           |
            | "Is title hidden"       |


    Scenario: Set the title help of the switch field
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Switch"
        Then the "Switch" titled page is displayed

        Given the user selects the "Title" labelled text field on the main page
        When the user writes "Sample title" in the text field
        And the user presses Tab

        Given the user selects the "Title Help" labelled text field on the main page
        When the user writes "Simple title help" in the text field
        And the user presses Tab

        Given the user selects the "field" bound switch field on the main page
        Then the title help of the switch field is "Simple title help"


    Scenario:Set the status of the switch field
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Switch"
        Then the "Switch" titled page is displayed

        Given the user selects the "value" bound switch field on the main page
        Then the switch field is set to "ON"
        Given the user selects the "field" bound switch field on the main page
        Then the switch field is set to "ON"

        Given the user selects the "value" bound switch field on the main page
        When the user turns the switch field "OFF"
        Then the switch field is set to "OFF"

        Given the user selects the "field" bound switch field on the main page
        Then the switch field is set to "OFF"


    Scenario: Set the focus on the switch field
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Switch"
        Then the "Switch" titled page is displayed

        When the user clicks in the "focus" bound button field on the main page
        Given the user selects the "field" bound switch field on the main page
        Then the focus on the switch field is visible


    Scenario: Set the switch field to large size
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Switch"
        Then the "Switch" titled page is displayed

        Given the user selects the "Is large" labelled switch field on the main page
        When the user turns the switch field "ON"

        Given the user selects the "field" bound switch field on the main page
        Then the size of the switch field is large
