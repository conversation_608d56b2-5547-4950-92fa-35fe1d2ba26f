Feature: 5 Y Single instance
        # Tests that require sequential execution to verify stateful behaviors, such as table sorting persistence across page navigation
        This feature file contains tests that are sensitive for parallel execution.

    Scenario: As a user I don't want the reset sorting on my custom view to mess up my table sorting
        XT-94994 XT-94879
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TableSortedBySortValue/eyJfaWQiOiIyIn0="
        Then the "Table sorted by sort order" titled page is displayed
        And the user selects the "products" bound table field on the main page
        And the user selects the row 6 of the table field
        Then the value of the "Product" labelled nested text field of the selected row in the table field is "Bols Melon Liqueur"
        And the user clicks the "product" bound column of the table field
        And the user waits for 2 seconds
        Then the value of the "Product" labelled nested text field of the selected row in the table field is "Artichoke - Fresh"
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TableSortedBySortValue/eyJfaWQiOiIyIn0="
        Then the "Table sorted by sort order" titled page is displayed
        And the user selects the "products" bound table field on the main page
        And the user selects the row 6 of the table field
        Then the value of the "Product" labelled nested text field of the selected row in the table field is "Artichoke - Fresh"
        And the user clicks the "product" bound column of the table field
        And the user waits for 2 seconds
        And the value of the "Product" labelled nested text field of the selected row in the table field is "Wine - Vidal Icewine Magnotta"
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TableSortedBySortValue/eyJfaWQiOiIyIn0="
        Then the "Table sorted by sort order" titled page is displayed
        And the user selects the "products" bound table field on the main page
        And the user selects the row 6 of the table field
        Then the value of the "Product" labelled nested text field of the selected row in the table field is "Wine - Vidal Icewine Magnotta"
        And the user clicks the "product" bound column of the table field
        And the user waits for 2 seconds
        And the value of the "Product" labelled nested text field of the selected row in the table field is "Chocolate - Milk"
        Then the "Table sorted by sort order" titled page is displayed
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TableSortedBySortValue/eyJfaWQiOiIyIn0="
        And the user selects the "products" bound table field on the main page
        And the user selects the row 6 of the table field
        Then the value of the "Product" labelled nested text field of the selected row in the table field is "Chocolate - Milk"

    Scenario: As a user I want to fiter a table using tabs
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TableOptionsMenu/eyJfaWQiOiIyIn0="
        Then the "Field - Table (Options Menu)" titled page is displayed
        When selects the "Tabs" labelled navigation anchor on the main page
        Then the "Tabs" labelled navigation anchor is selected
        And the user selects the "tableWithTabsOptionMenu" labelled table field on the main page
        When the user selects the "qualityIssues" tab of the table field
        And the user selects the row 1 of the table field
        Then the value of the "Id" labelled nested text field of the selected row in the table field is "474"
        When the user selects the "all" tab of the table field
        Then the value of the "Id" labelled nested text field of the selected row in the table field is "500"

    Scenario: As a user I want to use custom views in my main lists

        #create custom view
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseProduct"
        Then the "Products" titled page is displayed

        When the user selects the "$navigationPanel" bound table field on the main page
        Then the view dropdown of the table field is displayed
        Then the value of the view dropdown of the table field is "Default view"

        # make columns visible
        When the user clicks the "Open column panel" labelled button of the table field
        Then the "Column settings" titled sidebar is displayed

        When the user ticks the table column configuration with "Ending date" name on the sidebar
        Then the table column configuration with name "Ending date" on the sidebar is ticked

        When the user ticks the table column configuration with "Supplier" name on the sidebar
        Then the table column configuration with name "Supplier" on the sidebar is ticked

        When the user ticks the table column configuration with "List Price" name on the sidebar
        Then the table column configuration with name "List Price" on the sidebar is ticked

        # make columns invisible
        When the user unticks the table column configuration with "Description" name on the sidebar
        And the user clicks the Close button of the dialog on the sidebar
        And the user selects the "$navigationPanel" bound table field on the main page
        Then the "Supplier" labelled column in the table field is displayed
        And the "Ending date" labelled column in the table field is displayed
        And the "Description" labelled column in the table field is hidden
        And the "List Price" labelled column in the table field is displayed

        # group by a column (e.g. date)
        When the user clicks the group by year option in the header menu of the "endingDate" bound column of the table field
        And the user selects the row 1 of the table field
        Then the value of the "endingDate" bound nested reference field of the selected row in the table field is "No value (501)"

        # filter the table
        When the user filters the "Product" labelled column in the table field with filter "Contains" and value "prov"
        And the user selects the row 1 of the table field
        Then the value of the "endingDate" bound nested reference field of the selected row in the table field is "No value (3)"
        And the user selects the row 2 of the table field
        Then the value of the "endingDate" bound nested reference field of the selected row in the table field is "2019 (1)"
        And the user waits 2 seconds


        # sort the table
        When the user clicks the "endingDate" bound column of the table field
        And the user selects the row 1 of the table field
        Then the value of the "endingDate" bound nested reference field of the selected row in the table field is "2019 (1)"
        And the user selects the row 2 of the table field
        Then the value of the "endingDate" bound nested reference field of the selected row in the table field is "No value (3)"


        #save the custom view
        When the user opens the view dropdown menu of the table field
        And the user clicks the "Save" button of the view dropdown of the table field
        Then an info dialog appears on the main page
        And the dialog title is "Create a view" on the main page
        When the user selects the "Name" labelled text field on a modal
        And the user writes "Custom view 1" in the text field
        And the user presses Enter
        And the user clicks the "Save" labelled business action button on a modal
        Then the "Products" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        Then the "Supplier" labelled column in the table field is displayed
        And the "Ending date" labelled column in the table field is displayed
        And the "Description" labelled column in the table field is hidden
        And the user selects the row 1 of the table field
        Then the value of the "endingDate" bound nested reference field of the selected row in the table field is "2019 (1)"
        Then the value of the view dropdown of the table field is "Custom view 1"

        #create and edit a custom view based on the previously created one
        When the user opens the view dropdown menu of the table field
        And the user selects the "Default view" option of the view dropdown of the table field
        Then the value of the view dropdown of the table field is "Default view"
        And the user selects the row 1 of the table field
        Then the value of the "product" bound nested text field of the selected row in the table field is "Anisette - Mcguiness"
        And the "Supplier" labelled column in the table field is hidden
        And the "endingDate" labelled column in the table field is hidden
        And the "Description" labelled column in the table field is displayed

        When the user selects the "Custom view 1" option of the view dropdown of the table field
        And the user selects the row 1 of the table field
        Then the value of the "endingDate" bound nested reference field of the selected row in the table field is "2019 (1)"

        When the user opens the view dropdown menu of the table field
        And the user clicks the "Save as" button of the view dropdown of the table field
        Then an info dialog appears on the main page

        When the user selects the "Name" labelled text field on a modal
        And the user writes "Custom view 2" in the text field
        And the user selects the "description" bound text area field on a modal
        And the user presses Enter
        And the user writes "Custom view 2 description" in the text area field
        And the user presses Enter
        And the user clicks the "Save" labelled business action button on a modal
        Then the "Products" titled page is displayed

        When the user selects the "$navigationPanel" bound table field on the main page
        Then the value of the view dropdown of the table field is "Custom view 2"

        When the user opens the view dropdown menu of the table field
        And the user clicks the "Manage" button of the view dropdown of the table field
        Then an info dialog appears on the main page
        And the dialog title is "Manage views" on the main page

        When the user selects the "clientSettings" bound table field on a modal
        And the user selects the row 1 of the table field
        Then the value of the "title" bound nested text field of the selected row in the table field is "Custom view 1"
        And the user selects the row 2 of the table field
        Then the value of the "title" bound nested text field of the selected row in the table field is "Custom view 2"

        When the user selects the row 1 of the table field
        And the user writes "Custom view 3" in the "title" bound nested text field of the selected row in the table field
        And the user presses Enter
        And the user selects the row 2 of the table field
        And the user writes "Custom view 4" in the "title" bound nested text field of the selected row in the table field
        And the user presses Enter
        And the user clicks the "Save" labelled business action button on a modal
        Then the "Products" titled page is displayed

        When the user selects the "$navigationPanel" bound table field on the main page
        Then the value of the view dropdown of the table field is "Custom view 4"

        #return to Default view by removing custom views
        When the user opens the view dropdown menu of the table field
        And the user clicks the "Manage" button of the view dropdown of the table field
        Then an info dialog appears on the main page

        When the user selects the "clientSettings" bound table field on a modal
        And the user selects the row 1 of the table field
        And the user clicks the "Remove" dropdown action of the selected row of the table field
        And the user clicks the "OK" button of the Confirm dialog
        And the user clicks the "Remove" dropdown action of the selected row of the table field
        And the user clicks the "OK" button of the Confirm dialog
        And the user clicks the "Save" labelled business action button on a modal
        Then the "Products" titled page is displayed

        When the user selects the "$navigationPanel" bound table field on the main page
        Then the value of the view dropdown of the table field is "Default view"
        And the user selects the row 1 of the table field
        Then the value of the "product" bound nested text field of the selected row in the table field is "Anisette - Mcguiness"

    Scenario: As a user, I want my changes to a main list configuration not to persist if I didn´t create a custom view for my changes
        # making a column visible
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseProduct"
        Then the "Products" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        Then the view dropdown of the table field is displayed
        Then the value of the view dropdown of the table field is "Default view"

        # columns made visible
        When the user clicks the "Open column panel" labelled button of the table field
        Then the "Column settings" titled sidebar is displayed

        When the user ticks the table column configuration with "Ending date" name on the sidebar
        Then the table column configuration with name "Ending date" on the sidebar is ticked

        When the user clicks the Close button of the dialog on the sidebar
        And the user selects the "$navigationPanel" bound table field on the main page
        Then the "endingDate" bound column in the table field is displayed

        When the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseProduct"
        Then the "Products" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        Then the "endingDate" bound column in the table field is hidden

        #columns made hidden
        When the user clicks the "Open column panel" labelled button of the table field
        Then the "Column settings" titled sidebar is displayed

        When the user unticks the table column configuration with "Description" name on the sidebar
        Then the table column configuration with name "Description" on the sidebar is unticked

        When the user clicks the Close button of the dialog on the sidebar
        And the user selects the "$navigationPanel" bound table field on the main page
        Then the "Description" labelled column in the table field is hidden

        Then the user navigates to the following link: "@sage/xtrem-show-case/ShowCaseProduct"
        Then the "Products" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        Then the "Description" labelled column in the table field is displayed

        #grouped tables
        Then the user navigates to the following link: "@sage/xtrem-show-case/ShowCaseProduct"
        Then the "Products" titled page is displayed

        When the user selects the "$navigationPanel" bound table field on the main page
        Then the view dropdown of the table field is displayed
        Then the value of the view dropdown of the table field is "Default view"

        When the user clicks the group by option in the header menu of the "category" bound column of the table field
        And the user waits 2 seconds
        And the user selects the row 1 of the table field
        Then the value of the "category" bound nested reference field of the selected row in the table field is "No value (26)"

        Then the user navigates to the following link: "@sage/xtrem-show-case/ShowCaseProduct"
        Then the "Products" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row 1 of the table field
        Then the value of the "product" bound nested reference field of the selected row in the table field is "Anisette - Mcguiness"

        #sorted tables
        Then the user navigates to the following link: "@sage/xtrem-show-case/ShowCaseProduct"
        Then the "Products" titled page is displayed

        When the user selects the "$navigationPanel" bound table field on the main page
        And the user clicks the "category" bound column of the table field
        And the user selects the row 1 of the table field
        Then the value of the "product" bound nested reference field of the selected row in the table field is "Soup - Tomato Mush. Florentine"

        Then the user navigates to the following link: "@sage/xtrem-show-case/ShowCaseProduct"
        Then the "Products" titled page is displayed

        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row 1 of the table field
        Then the value of the "product" bound nested reference field of the selected row in the table field is "Anisette - Mcguiness"

        #filtered tables
        Then the user navigates to the following link: "@sage/xtrem-show-case/ShowCaseProduct"
        Then the "Products" titled page is displayed

        When the user selects the "$navigationPanel" bound table field on the main page
        And the user filters the "Product" labelled column in the table field with filter "Contains" and value "prov"
        And the user selects the row 1 of the table field
        Then the value of the "product" bound nested reference field of the selected row in the table field is "Cheese - Provolone"

        Then the user navigates to the following link: "@sage/xtrem-show-case/ShowCaseProduct"
        Then the "Products" titled page is displayed

        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row 1 of the table field
        Then the value of the "product" bound nested reference field of the selected row in the table field is "Anisette - Mcguiness"
