Feature: 4-2 time
    # Tests the time field component across different devices, verifying time input, formatting, validation, and interactions with the time picker interface and control the time field state.

    Scenario Outline: <Device> - As an ATP XTreeM User I can write and verify a time field using label
        XT-92858
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Time"
        Then the "Time" titled page is displayed

        Given the user selects the "Title" labelled text field on the main page
        When the user writes "Sample title" in the text field
        And the user presses Tab

        Given the user selects the "Sample title" labelled time field on the main page
        When the user writes "12:15" in the time field
        And the user clicks the "PM" toggle button of the time field
        Then the value of the time field is "12:15"
        # And takes a screenshot
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> - As an ATP XTreeM User I can write and verify a time field using bind
        XT-92858
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Time"
        Then the "Time" titled page is displayed

        Given the user selects the "field" bound time field on the main page
        When the user writes "09:20" in the time field
        And the user clicks the "AM" toggle button of the time field
        Then the value of the time field is "09:20"
        # And takes a screenshot
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |


    #time  field - set / check properties

    Scenario Outline: <Device> - As and ATP XTreeM user I can verify the time field title
        XT-92858
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Time"
        Then the "Time" titled page is displayed

        Given the user selects the "Title" labelled text field on the main page
        When the user writes "Sample title" in the text field
        And the user presses Tab

        Given the user selects the "field" bound time field on the main page
        Then the title of the time field is "Sample title"
        Then the title of the time field is displayed
        # And takes a screenshot

        Given the user selects the "Is title hidden" labelled checkbox field on the main page
        When the user ticks the checkbox field

        Given the user selects the "field" bound time field on the main page
        Then the title of the time field is hidden
        # And takes a screenshot
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |


    Scenario Outline: <Device> - As and ATP XTreeM user I can verify the time field helper text
        XT-92858
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Time"
        Then the "Time" titled page is displayed

        Given the user selects the "Helper text" labelled text field on the main page
        When the user writes "Sample text" in the text field
        And the user presses Tab

        Given the user selects the "field" bound time field on the main page
        Then the helper text of the time field is "Sample text"
        Then the helper text of the time field is displayed
        # And takes a screenshot

        Given the user selects the "Is helper text hidden" labelled checkbox field on the main page
        When the user ticks the checkbox field

        Given the user selects the "field" bound time field on the main page
        Then the helper text of the time field is hidden
        # And takes a screenshot
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> - As and ATP XTreeM user I can verify if the time field is displayed or hidden
        XT-92858
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Time"
        Then the "Time" titled page is displayed

        Then the "field" bound time field on the main page is displayed
        Given the user selects the "field" bound time field on the main page
        When the user writes "09:20" in the time field
        And the user clicks the "AM" toggle button of the time field
        # And takes a screenshot

        Given the user selects the "Is hidden" labelled checkbox field on the main page
        When the user ticks the checkbox field
        Then the "field" bound time field on the main page is hidden
        Then the value of the time field is "09:20"
        # And takes a screenshot
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |


    Scenario Outline:<Device> - As and ATP XTreeM user I can verify if the time field is enabled or disabled
        XT-92858
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Time"
        Then the "Time" titled page is displayed

        Given the user selects the "field" bound time field on the main page
        Then the time field is enabled
        When the user writes "09:20" in the time field
        And the user clicks the "AM" toggle button of the time field
        Then the value of the time field is "09:20"
        # And takes a screenshot

        Given the user selects the "Is disabled" labelled checkbox field on the main page
        When the user ticks the checkbox field

        Given the user selects the "field" bound time field on the main page
        Then the time field is disabled
        Then the value of the time field is "09:20"
        # And takes a screenshot
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |


    Scenario Outline: <Device> - As and ATP XTreeM user I can verify if the time field is read-only
        XT-92858
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Time"
        Then the "Time" titled page is displayed

        Given the user selects the "field" bound time field on the main page
        When the user writes "09:20" in the time field
        And the user clicks the "AM" toggle button of the time field

        Given the user selects the "Is read only" labelled checkbox field on the main page
        When the user ticks the checkbox field

        Then the time field is read-only
        Then the value of the time field is "09:20"
        # And takes a screenshot
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> - As an ATP XTreeM User I can verify the validity of the time field
        XT-92858
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Time"
        Then the "Time" titled page is displayed

        Given the user selects the "field" bound time field on the main page
        Then the "Value cannot be 13:00:00" validation error message of the time field is hidden
        # And takes a screenshot
        When the user writes "01:00" in the time field
        And the user clicks the "PM" toggle button of the time field
        Then the "Value cannot be 13:00:00" validation error message of the time field is displayed
        # And takes a screenshot
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> - As an ATP XTreeM User I can verify the time field is mandatory
        XT-92858
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Time"
        Then the "Time" titled page is displayed

        Given the user selects the "Title" labelled text field on the main page
        When the user writes "Sample title" in the text field
        And the user presses Tab

        Given the user selects the "Is mandatory" labelled checkbox field on the main page
        When the user ticks the checkbox field

        Given the user selects the "field" bound time field on the main page
        Then the time field is mandatory
        # And takes a screenshot
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> - As an app developer I can verify setting time field value via control object
        XT-67003
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Time"
        Then the "Time" titled page is displayed
        When the user selects the "setHours" bound numeric field on the main page
        And the user writes "9" in the numeric field
        And the user clicks in the "focus" bound button field on the main page
        And the user selects the "field" bound time field on the main page
        Then the value of the time field is "09:00"
        When the user selects the "setMinutes" bound numeric field on the main page
        And the user writes "59" in the numeric field
        And the user clicks in the "focus" bound button field on the main page
        And the user selects the "field" bound time field on the main page
        Then the value of the time field is "09:59"

        When the user selects the "setHours" bound numeric field on the main page
        And the user writes "25" in the numeric field
        And the user clicks in the "focus" bound button field on the main page
        Then an error dialog appears on the main page
        When the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Time"
        Then the "Time" titled page is displayed
        When the user selects the "setMinutes" bound numeric field on the main page
        And the user writes "60" in the numeric field
        And the user clicks in the "focus" bound button field on the main page
        Then an error dialog appears on the main page

        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: As a user I can verify time field value when it is being cleared for locale <Language>
        XT-67003
        # check minutes segment is left empty when it is cleared and blurred
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Time"
        When the user switches language to <Language>
        Then the "Time" titled page is displayed
        When the user selects the "field" bound time field on the main page
        And the user writes "11:59" in the time field
        Then the value of the time field is "11:59"
        When the user presses Shift+Tab
        And the user presses Delete
        And the user presses Shift+Tab
        Then the value of the hours segment of the time field is "11"
        And the value of the minutes segment of the time field is ""

        # check hours segment is left empty when it is cleared and blurred
        When the user navigates to the following link: "@sage/xtrem-show-case/Time"
        Then the "Time" titled page is displayed
        When the user selects the "field" bound time field on the main page
        And the user writes "11:59" in the time field
        Then the value of the time field is "11:59"
        When the user presses Shift+Tab
        And the user presses Shift+Tab
        And the user presses Delete
        And the user presses Tab
        Then the value of the hours segment of the time field is ""
        And the value of the minutes segment of the time field is "59"

        # check hours and minutes are left empty when they are cleared and time field is blurred
        When the user navigates to the following link: "@sage/xtrem-show-case/Time"
        Then the "Time" titled page is displayed
        When the user selects the "field" bound time field on the main page
        And the user writes "11:59" in the time field
        Then the value of the time field is "11:59"
        When the user presses Shift+Tab
        And the user presses Delete
        And the user presses Shift+Tab
        And the user presses Delete
        And the user presses Shift+Tab
        Then the value of the hours segment of the time field is ""
        And the value of the minutes segment of the time field is ""

        # check minutes segment is filled with '00' when it is cleared and time field is blurred
        When the user navigates to the following link: "@sage/xtrem-show-case/Time"
        Then the "Time" titled page is displayed
        When the user selects the "field" bound time field on the main page
        And the user writes "11:59" in the time field
        Then the value of the time field is "11:59"
        When the user presses Shift+Tab
        And the user presses Delete
        And the user presses Tab
        Then the value of the time field is "11:00"

        # check hours segment is filled with '00' or '12' when it is cleared and time field is blurred
        When the user navigates to the following link: "@sage/xtrem-show-case/Time"
        Then the "Time" titled page is displayed
        When the user selects the "field" bound time field on the main page
        And the user writes "11:59" in the time field
        Then the value of the time field is "11:59"
        When the user presses Shift+Tab
        And the user presses Shift+Tab
        And the user presses Delete
        And the user presses Shift+Tab
        Then the value of the time field is <Time>

        Examples:
            | Language     | Time    |
            | "English US" | "12:59" |
            | "English GB" | "00:59" |
            | "Spanish"    | "00:59" |
            | "French"     | "00:59" |
            | "German"     | "00:59" |
            | "Polish"     | "00:59" |
            | "Portuguese" | "00:59" |
            | "Chinese"    | "00:59" |
