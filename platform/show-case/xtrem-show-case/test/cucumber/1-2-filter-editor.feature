Feature: 1-2 Filter editor
    # Tests the filter editor component functionality, including adding filter conditions, selecting filter properties, operators, and values

    Scenario: As a user I want to apply filters using the filter editor
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/FilterEditor"
        Then the "Filter Editor" titled page is displayed
        When the user selects the "field" bound filter editor field on the main page
        And the user clicks the "Add filter" button of the filter editor field
        Then the "filter-property" dropdown field of row "1" in the filter editor field is displayed
        And the "filter-property" dropdown field of row "1" in the filter editor field is enabled
        And the "filter-type" dropdown field of row "1" in the filter editor field is displayed
        And the "filter-type" dropdown field of row "1" in the filter editor field is disabled
        And the "filter-value" text field of row "1" in the filter editor field is displayed
        And the "filter-value" text field of row "1" in the filter editor field is disabled
        And the "add" action button of row "1" in the filter editor field is displayed
        And the "remove" action button of row "1" in the filter editor field is displayed
        When the user writes "Email" in the "filter-property" dropdown field of row "1" in the filter editor field
        Then the "filter-type" dropdown field of row "1" in the filter editor field is enabled
        When the user writes "Contains" in the "filter-type" dropdown field of row "1" in the filter editor field
        And the user presses Enter
        Then the "filter-value" text field of row "1" in the filter editor field is enabled
        When the user clicks the "filter-parameter" switch of row "1" in the filter editor field
        Then the "filter-value" text field of row "1" in the filter editor field is hidden
        And the "filter-parameter" switch of row "1" in the filter editor field is "on"
        When the user clicks the "filter-parameter" switch of row "1" in the filter editor field
        Then the "filter-parameter" switch of row "1" in the filter editor field is "off"
        Then the "filter-value" text field of row "1" in the filter editor field is enabled
        When the user writes "a" in the "filter-value" text field of row "1" in the filter editor field
        And the user clicks the "add" action button of row "1" in the filter editor field
        Then the "filter-property" dropdown field of row "2" in the filter editor field is displayed
        And the "filter-type" dropdown field of row "2" in the filter editor field is displayed
        And the "filter-type" dropdown field of row "2" in the filter editor field is disabled
        And the "filter-value" text field of row "2" in the filter editor field is displayed
        When the user clicks the "remove" action button of row "2" in the filter editor field
        Then the "filter-property" dropdown field of row "2" in the filter editor field is hidden
        And the "filter-type" dropdown field of row "2" in the filter editor field is hidden
        And the "filter-type" dropdown field of row "2" in the filter editor field is hidden
        And the "filter-value" text field of row "2" in the filter editor field is hidden
        When the user clears the "filter-type" dropdown field of row "1" in the filter editor field
        Then the "filter-value" text field of row "1" in the filter editor field is disabled
        When the user clears the "filter-property" dropdown field of row "1" in the filter editor field
        Then the "filter-type" text field of row "1" in the filter editor field is disabled
