Feature: 3-1 Pod collection nested field
    # Tests pod collection components with nested fields, verifying selection of collection items and proper interaction with fields within selected pods

    Scenario Outline: <Device> - As a user I can write and check the value in the nested field of the selected pod collection item
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/PodCollection/eyJfaWQiOiIyIn0="
        And the user selects the "Title" labelled text field on the main page
        When the user writes "Pod Collection Title" in the text field
        And the user selects the "Pod Collection Title" labelled pod collection field on the main page
        And the user selects the "Mints - Striped Red 85" labelled pod collection item of the selected pod collection field
        And the user writes "Pepper - Chillies, Crushed" in the "Product" labelled nested reference field of the selected pod collection item
        Then the value of the "Product" labelled nested reference field of the selected pod collection item is "Pepper - Chillies, Crushed"
        And the user writes "70" in the "Ordered Quantity" labelled nested numeric field of the selected pod collection item
        Then the value of the "Ordered Quantity" labelled nested numeric field of the selected pod collection item is "70"
        And the user writes "4,999.00" in the "Net Price" labelled nested numeric field of the selected pod collection item
        Then the user presses Tab
        Then the value of the "Net Price" labelled nested numeric field of the selected pod collection item is "4,999.00"
        And the user writes "This is a comment" in the "Comments" labelled nested text area field of the selected pod collection item
        Then the value of the "Comments" labelled nested text area field of the selected pod collection item is "This is a comment"
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> - As a user I can write and check the value in the nested labelled field of the selected pod collection item selected with label
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/PodCollection/eyJfaWQiOiIyIn0="
        And the user selects the "Title" labelled text field on the main page
        When the user writes "Pod Collection Title" in the text field
        And the user selects the "Pod Collection Title" labelled pod collection field on the main page
        And the user selects the "Shrimp - 16/20, Iqf, Shell On 51" labelled pod collection item of the selected pod collection field
        And the user writes "Veal Inside - Provimi" in the "Product" labelled nested reference field of the selected pod collection item
        And the user writes "90" in the "Ordered Quantity" labelled nested numeric field of the selected pod collection item
        And the user writes "7,999.00" in the "Net Price" labelled nested numeric field of the selected pod collection item
        And the user writes "This is a comment using label" in the "Comments" labelled nested text area field of the selected pod collection item
        Then the user presses Tab
        And the user selects the "Pod Collection Title" labelled pod collection field on the main page
        And the user selects the "Veal Inside - Provimi 2" labelled pod collection item of the selected pod collection field
        Then the value of the "Product" labelled nested reference field of the selected pod collection item is "Veal Inside - Provimi"
        Then the value of the "Ordered Quantity" labelled nested numeric field of the selected pod collection item is "90"
        Then the value of the "Net Price" labelled nested numeric field of the selected pod collection item is "7,999.00"
        Then the value of the "Comments" labelled nested text area field of the selected pod collection item is "This is a comment using label"
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> - As a user I can write and check the value in the nested bind field of the selected pod collection item selected with id
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/PodCollection/eyJfaWQiOiIyIn0="
        And the user selects the "Title" labelled text field on the main page
        When the user writes "Pod Collection Title" in the text field
        And the user selects the "Pod Collection Title" labelled pod collection field on the main page
        And the user selects the "800" id pod collection item of the selected pod collection field
        And the user writes "Ice Cream - Super Sandwich" in the "product" bound nested reference field of the selected pod collection item
        And the user writes "91" in the "orderQuantity" bound nested numeric field of the selected pod collection item
        And the user writes "8,999.00" in the "netPrice" bound nested numeric field of the selected pod collection item
        Then the user presses Tab
        And the user writes "This is a comment using bind" in the "comments" bound nested text area field of the selected pod collection item
        And the user selects the "Pod Collection Title" labelled pod collection field on the main page
        And the user selects the "800" id pod collection item of the selected pod collection field
        Then the value of the "product" bound nested reference field of the selected pod collection item is "Ice Cream - Super Sandwich"
        Then the value of the "orderQuantity" bound nested numeric field of the selected pod collection item is "91"
        Then the value of the "netPrice" bound nested numeric field of the selected pod collection item is "8,999.00"
        Then the value of the "comments" bound nested text area field of the selected pod collection item is "This is a comment using bind"
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> - As a user I can select or unselect and to verify the state of the nested checkbox field of the selected pod collection item
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/TransientPod"
        When the user clicks in the "loadButton" bound button field on the main page
        And the user selects the "Large selectable, selectable pods" labelled pod collection field on the main page
        And the user selects the "Item 3" labelled pod collection item of the selected pod collection field
        And the user unselects the "Hot" labelled nested checkbox field of the selected pod collection item
        Then the "Hot" labelled nested checkbox field of the selected pod collection item is unselected
        And the user selects the "Hot" labelled nested checkbox field of the selected pod collection item
        Then the "Hot" labelled nested checkbox field of the selected pod collection item is selected
        And the user unselects the "hotProduct" bound nested checkbox field of the selected pod collection item
        Then the "hotProduct" bound nested checkbox field of the selected pod collection item is unselected
        And the user selects the "hotProduct" bound nested checkbox field of the selected pod collection item
        Then the "hotProduct" bound nested checkbox field of the selected pod collection item is selected
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> - As a user I can verify the value of the nested progress field of the selected pod collection item
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/TransientPod"
        When the user clicks in the "loadButton" bound button field on the main page
        And the user selects the "Large selectable, selectable pods" labelled pod collection field on the main page
        And the user selects the "Item 3" labelled pod collection item of the selected pod collection field
        Then the value of the "Progress" labelled nested progress field of the selected pod collection item is "50 %"
        Then the value of the "progress" bound nested progress field of the selected pod collection item is "50 %"
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> - As a user I can verify the value of the nested header pill label field of the selected pod collection item
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/PodCollectionWithValidation/eyJfaWQiOiIyIn0="
        And the user selects the "Title" labelled text field on the main page
        And the user writes "Pod collection title" in the text field
        And the user selects the "Pod collection title" labelled pod collection field on the main page
        And the user selects the "Blueberries 152" labelled pod collection item of the selected pod collection field
        Then the value of the "netPrice" bound nested label field of the selected pod collection item is "7334.59"
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> - As a user I can click than verifiy options than write than select a value in the multi reference field nested in the pod collection item
        XT-43549
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/NestedPodCollection/eyJfaWQiOiIxIn0="
        And the user selects the "providers" bound pod collection field on the main page
        And the user selects the "1" id pod collection item of the selected pod collection field
        And the user clicks in the "Addresses" labelled nested multi reference field of the selected pod collection item
        And at least the following list of options is displayed "Barcelona Office|London Office" in the "Addresses" labelled nested multi reference field of the selected pod collection item
        And the user clears the "Addresses" labelled nested multi reference field of the selected pod collection item
        And the value of the "Addresses" labelled nested multi reference field of the selected pod collection item is ""
        And the user writes "Office" in the "Addresses" labelled nested multi reference field of the selected pod collection item
        And the user selects "Barcelona Office|London Office|Paris Office" in the "Addresses" labelled nested multi reference field of the selected pod collection item
        And the value of the "Addresses" labelled nested multi reference field of the selected pod collection item is "Barcelona Office|London Office|Paris Office"
        And the user clears the "Addresses" labelled nested multi reference field of the selected pod collection item
        And the value of the "Addresses" labelled nested multi reference field of the selected pod collection item is ""
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> - As a user I can click then verifiy options than  write than select a value in the multi dropdown field nested in the pod collection item
        XT-43549
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/ShowCaseProviderAsPods/eyJfaWQiOiIxIn0="
        And the user selects the "products" bound pod collection field on the main page
        And the user selects the "Veal Inside - Provimi | 2" labelled pod collection item of the selected pod collection field
        And the user clicks in the "Subcategories" labelled nested multi dropdown field of the selected pod collection item
        And at least the following list of options is displayed "Awful|Good|Great|Not bad" in the "Subcategories" labelled nested multi dropdown field of the selected pod collection item
        And the user clears the "Subcategories" labelled nested multi dropdown field of the selected pod collection item
        And the value of the "Subcategories" labelled nested multi dropdown field of the selected pod collection item is ""
        And the user selects "Awful | Good" in the "Subcategories" labelled nested multi dropdown field of the selected pod collection item
        Then the value of the "Subcategories" labelled nested multi dropdown field of the selected pod collection item is "Awful, Good"
        And the user clears the "Subcategories" labelled nested multi dropdown field of the selected pod collection item
        And the value of the "Subcategories" labelled nested multi dropdown field of the selected pod collection item is ""
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> - As a user I can verify the value of the nested switch in the pod collection
        XT-44593
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/ShowCaseProviderAsPods/eyJfaWQiOiIxIn0="
        And the user selects the "Products" labelled pod collection field on the main page
        And the user selects the "Veal Inside - Provimi | 2" labelled pod collection item of the selected pod collection field
        Then the value of the "Feature product?" labelled nested switch field of the selected pod collection item is set to "OFF"
        And the user clicks the "Feature product?" labelled nested switch field of the selected pod collection item
        Then the value of the "Feature product?" labelled nested switch field of the selected pod collection item is set to "ON"
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> - As a user I want to be able to set date-time or no start/end date time for the date-time range field in the pod collection
        XT-78128
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/ShowCaseProviderAsPods/eyJfaWQiOiI1In0="
        And the user selects the "Manufacterd within" labelled date-time-range field on the main page

        # set date time range and check the values
        And the user selects the "January" month of the start date-time-range field
        And the user selects the "2024" year of the start date-time-range field
        And the user selects the "7" day in the start date-time-range field
        And the user writes "08:45" in time field of the start date-time-range field
        And the user clicks the "PM" toggle button of the start date-time-range field
        And the user selects the "December" month of the end date-time-range field
        And the user selects the "2027" year of the end date-time-range field
        And the user selects the "31" day in the end date-time-range field
        And the user writes "07:30" in time field of the end date-time-range field
        And the user clicks the "Save" labelled business action button on the main page
        Then the value of the start date-time-range field is "01/07/2024 - 08:45 PM"
        And the value of the end date-time-range field is "12/31/2027 - 07:30 AM"

        # set No start and No end date time range and check the values
        When the user ticks the "No end date" checkbox of the end date-time-range field
        And the user ticks the "No start date" checkbox of the start date-time-range field
        And the user clicks the "Save" labelled business action button on the main page
        Then the value of the start date-time-range field is ""
        And the value of the end date-time-range field is ""
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |
