Feature: 1-1 Browser display
    # Tests responsive display behavior across different device types, ensuring proper rendering of tables and other UI elements

    Scenario Outline: As an ATP XTreeM user I can read a table in a <Device>
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/TableWithOptionsMenu/eyJfaWQiOiIyIn0="
        And the "Field - Table" titled page is displayed
        And the user selects the "field" bound table field on the main page
        And the user selects the row with text "313" in the "_id" bound column header of the table field
        Then the value of the "Product" labelled nested text field of the selected row in the table field is "Appetiser - Bought"
        # And takes a screenshot
        Examples:
            | Device            |
            | desktop           |
            | HD desktop        |
            | ultrawide desktop |

    Scenario Outline: As an ATP XTreeM user I can read a table in a <Device>
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/TableWithOptionsMenu/eyJfaWQiOiIyIn0="
        And the "Field - Table" titled page is displayed
        And the user selects the "field" bound table field on the main page
        Then the value of the "amount" bound nested label field of the card 1 in the table field is "$ 106.99"
        # And takes a screenshot
        Examples:
            | Device |
            | tablet |
            | mobile |
