Feature: 3-1 Pod nested field
    # Tests pod component with nested fields, verifying proper display, data binding, and interactions with fields contained within pod containers

    Scenario Outline: <Device> - As a user I want to verify a value to the nested field in the pod field
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/PodField/eyJfaWQiOiIxMDYifQ=="
        Then the "Pod as a field" titled page is displayed

        # Lines commented out due to bug XT-19261

        #selection using label
        And the user selects the "Provider" labelled pod field on the main page
        Then the value of the "Text field" labelled nested text field in the pod field is "Ali Express"
        # Then the value of the "Numeric field" labelled nested numeric field in the pod field is "1"
        # Then the value of the "Date field" labelled nested date field in the pod field is "2019-07-01"

        #selection using bind
        And the user selects the "provider" bound pod field on the main page
        Then the value of the "textField" bound nested text field in the pod field is "Ali Express"
        # Then the value of the "integerField" bound nested numeric field in the pod field is "1"
        # Then the value of the "dateField" bound nested date field in the pod field is "2019-07-01"

        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> - As a user I want to verify a value to the nested field in the pod block
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/PodBlock/eyJfaWQiOiIxMDYifQ=="
        Then the "Pod as a block" titled page is displayed

        # Lines commented out due to bug XT-19261

        #selection using label
        And the user selects the "Provider" labelled pod field on the main page
        Then the value of the "Text field" labelled nested text field in the pod field is "Ali Express"
        # Then the value of the "Numeric field" labelled nested numeric field in the pod field is "1"
        # Then the value of the "Date field" labelled nested date field in the pod field is "2019-07-01"
        Then the value of the "Address" labelled nested text area field in the pod field is "Av Diagonal 200 Barcelona Spain"

        #selection using bind
        And the user selects the "provider" bound pod field on the main page
        Then the value of the "textField" bound nested text field in the pod field is "Ali Express"
        # Then the value of the "integerField" bound nested numeric field in the pod field is "1"
        # Then the value of the "dateField" bound nested date field in the pod field is "2019-07-01"
        Then the value of the "concatenatedAddress" bound nested text area field in the pod field is "Av Diagonal 200 Barcelona Spain"
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |


    Scenario Outline: <Device> - As a user I want to verify a value of the nested field in a read-only pod field
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/PodField/eyJfaWQiOiIxMDYifQ=="
        Then the "Pod as a field" titled page is displayed

        And the user selects the "Provider" labelled pod field on the main page
        Then the pod field is enabled
        And the user selects the "Is readOnly" labelled checkbox field on the main page
        When the user ticks the checkbox field
        And the user selects the "Provider" labelled pod field on the main page
        Then the pod field is read-only
        Then the value of the "Text field" labelled nested text field in the pod field is "Ali Express"
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |


    Scenario Outline: <Device> - As a user I want to verify a value of the nested field in a read-only pod block
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/PodBlock/eyJfaWQiOiIxMDYifQ=="
        Then the "Pod as a block" titled page is displayed

        And the user selects the "Provider" labelled pod field on the main page
        Then the pod field is enabled
        And the user selects the "Is readOnly" labelled checkbox field on the main page
        When the user ticks the checkbox field
        And the user selects the "Provider" labelled pod field on the main page
        Then the pod field is read-only
        Then the value of the "Text field" labelled nested text field in the pod field is "Ali Express"
        Then the value of the "Address" labelled nested text area field in the pod field is "Av Diagonal 200 Barcelona Spain"
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |


    Scenario Outline: <Device> - As a user I want to verify a value of the nested field in a disabled pod field
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/PodField/eyJfaWQiOiIxMDYifQ=="
        Then the "Pod as a field" titled page is displayed

        And the user selects the "Provider" labelled pod field on the main page
        Then the pod field is enabled
        And the user selects the "Is disabled" labelled checkbox field on the main page
        When the user ticks the checkbox field
        And the user selects the "Provider" labelled pod field on the main page
        Then the pod field is disabled
        Then the value of the "Text field" labelled nested text field in the pod field is "Ali Express"
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |


    Scenario Outline: <Device> - As a user I want to verify a value of the nested field in a disabled pod block
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/PodBlock/eyJfaWQiOiIxMDYifQ=="
        Then the "Pod as a block" titled page is displayed

        And the user selects the "Provider" labelled pod field on the main page
        Then the pod field is enabled
        And the user selects the "Is disabled" labelled checkbox field on the main page
        When the user ticks the checkbox field
        And the user selects the "Provider" labelled pod field on the main page
        Then the pod field is disabled
        Then the value of the "Text field" labelled nested text field in the pod field is "Ali Express"
        Then the value of the "Address" labelled nested text area field in the pod field is "Av Diagonal 200 Barcelona Spain"
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

# Scenario Outline: <Device> - As a user I want to verify the error message for a missing pod
#     Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/PodField/eyJfaWQiOiIxMDYifQ=="
#     And the user selects the "Not existing" labelled pod field on the main page
#     Then the value of the "Text field" labelled nested text field in the pod field is "Ali Express"
#     Examples:
#         | Device  |
#         | desktop |

# Scenario Outline: <Device> - As a user I want to verify the error message for a missing nested field in the pod
#     Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/PodField/eyJfaWQiOiIxMDYifQ=="
#     And the user selects the "Provider" labelled pod field on the main page
#     Then the value of the "Not existing" labelled nested text field in the pod field is "Ali Express"
#     Examples:
#         | Device  |
#         | desktop |

# Scenario Outline: <Device> - As a user I want to verify the error message for an incorrect value in the nested field in the pod
#     Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/PodField/eyJfaWQiOiIxMDYifQ=="
#     And the user selects the "Provider" labelled pod field on the main page
#     Then the value of the "Text field" labelled nested text field in the pod field is "Incorrect value"
#     Examples:
#         | Device  |
#         | desktop |


# ##########
# # export TARGET_URL="https://login.eu.dev-sagextrem.com/unsecuredevlogin?cluster=cluster-ci&tenant=Mi3rlSNd5hwAqy5or3gTT&user=<EMAIL>"
# ##########
# Scenario: desktop - As a user I want to check a value in the text area field in pod
#     Given the user opens the application on a desktop using the following link: "@sage/xtrem-purchasing/PurchaseReturn"
#     Then the "PURCHASE RETURN" subtitled page is displayed
#     And the user selects the "Supplier" labelled reference field on the main page
#     When the user writes "Carlson Filtration" in the reference field
#     And the user clicks the lookup button of the reference field
#     Then the value of the "Name" labelled nested text field of row 1 in the "supplier" bound table field is "Carlson Filtration"
#     And the user clicks on the "Name" labelled nested text field of row 1 of the "supplier" bound table field in a modal
#     Then the value of the reference field is "Carlson Filtration"
#     And the user selects the "Return to address" labelled pod field on the main page
#     Then the value of the "concatenatedAddress" bound nested text area field in the pod field is "Carlson Filtration 136 Wexford Run Road SEWICKLEY PA 15143 United States of America"
#     Then the value of the "locationPhoneNumber" bound nested text field in the pod field is "7244443819"
