Feature: 3-2 Service option
        # Tests service option configuration functionality, verifying that service states can be viewed, selected, and properly displayed in the interface

        Scenario: Navigate to the page and select the record
                Given the user opens the application on a desktop using the following link: "@sage/xtrem-system/ServiceOptionState"
                Then the "Service options" titled page is displayed
                And the user selects the "$navigationPanel" bound table field on the navigation panel
                And the user filters the "Name" labelled column in the table field with value "showCaseOptionHighLevel"
                Then the user selects the row 1 of the table field
                And the value of the "Active" labelled nested checkbox field of the selected row in the table field is "false"
                And the user clicks the "Activate" dropdown action of the selected row of the table field
                #When the user clicks the "Activate" dropdown action of the selected row of the table field
                Then a warn dialog appears on the main page
                When the user clicks the "OK" button of the Confirm dialog

                Then the "Service options" titled page is displayed
                And the user selects the "$navigationPanel" bound table field on the navigation panel
                And the user filters the "Name" labelled column in the table field with value "showCaseOptionHighLevel"
                Then the user selects the row 1 of the table field
                And the value of the "Active" labelled nested checkbox field of the selected row in the table field is "true"
                And the user clicks the "Deactivate" dropdown action of the selected row of the table field
                Then a warn dialog appears on the main page
                When the user clicks the "OK" button of the Confirm dialog

                Then the "Service options" titled page is displayed
                And the user selects the "$navigationPanel" bound table field on the navigation panel
                And the user filters the "Name" labelled column in the table field with value "showCaseOptionHighLevel"
                And the user selects the row 1 of the table field
                Then the value of the "Active" labelled nested checkbox field of the selected row in the table field is "false"
