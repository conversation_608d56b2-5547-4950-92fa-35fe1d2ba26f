Feature: 1-1-date-time-range
    # Tests the date-time range component functionality across different devices, ensuring users can properly select date ranges with accurate time selection and format validation

    Scenario Outline: <Device> - As an ATP XTreeM User I can set a date time range field
        XT-85798
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/DatetimeRange"
        Then the "Date-time Range" titled page is displayed

        Given the user selects the "Title" labelled text field on the main page
        When the user writes "Sample title" in the text field
        And the user blurs the text field

        Given the user selects the "Sample title" labelled date-time-range field on the main page

        And the user selects the "April" month of the start date-time-range field
        And the user selects the "2025" year of the start date-time-range field
        And the user selects the "4" day in the start date-time-range field
        And the user writes "12:15" in time field of the start date-time-range field
        And the user clicks the "PM" toggle button of the start date-time-range field
        Then the value of the start date-time-range field is "04/04/2025 - 12:15 PM"
        # And takes a screenshot

        And the user selects the "May" month of the end date-time-range field
        And the user selects the "2026" year of the end date-time-range field
        And the user selects the "31" day in the end date-time-range field
        And the user writes "07:30" in time field of the end date-time-range field
        And the user clicks the "PM" toggle button of the end date-time-range field
        Then the value of the end date-time-range field is "05/31/2026 - 07:30 PM"
        And the user leaves the focus from the end date-time-range field
        # And takes a screenshot

        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> - As an ATP XTreeM User I can request no start / end date
        XT-85798
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/DatetimeRange"
        Then the "Date-time Range" titled page is displayed

        Given the user selects the "Title" labelled text field on the main page
        When the user writes "Sample title" in the text field
        And the user blurs the text field

        Given the user selects the "Sample title" labelled date-time-range field on the main page
        And the user unticks the "No start date" checkbox of the start date-time-range field
        # And takes a screenshot
        And the user ticks the "No start date" checkbox of the start date-time-range field
        # And takes a screenshot
        And the user unticks the "No end date" checkbox of the end date-time-range field
        # And takes a screenshot
        And the user ticks the "No end date" checkbox of the end date-time-range field
        # And takes a screenshot
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |


    Scenario Outline: <Device> - As an ATP XTreeM User I can verify the time zone value
        XT-85798
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/DatetimeRange"
        Then the "Date-time Range" titled page is displayed

        Given the user selects the "Title" labelled text field on the main page
        When the user writes "Sample title" in the text field
        And the user blurs the text field

        Given the user selects the "Sample title" labelled date-time-range field on the main page
        And the user selects the "January" month of the start date-time-range field
        And the user selects the "2025" year of the start date-time-range field
        And the user selects the "20" day in the start date-time-range field
        And the user writes "12:15" in time field of the start date-time-range field
        And the user clicks the "PM" toggle button of the start date-time-range field
        Then the value of the start date-time-range field is "01/20/2025 - 12:15 PM"
        # And takes a screenshot

        And the user clicks the time zone field in the start date-time-range field
        Then the time-zone value in the start date-time-range field is "GMT"
        # And takes a screenshot

        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |


    # time  field - set / check properties

    Scenario Outline: <Device> - As and ATP XTreeM user I can verify the date-time-range field title
        XT-85804
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/DatetimeRange"
        Then the "Date-time Range" titled page is displayed

        Given the user selects the "Title" labelled text field on the main page
        When the user writes "Sample title" in the text field
        And the user presses Tab

        Given the user selects the "field" bound date-time-range field on the main page
        Then the title of the date-time-range field is "Sample title"
        Then the title of the date-time-range field is displayed
        # And takes a screenshot

        Given the user selects the "Is title hidden" labelled checkbox field on the main page
        When the user ticks the checkbox field

        Given the user selects the "field" bound date-time-range field on the main page
        Then the title of the date-time-range field is hidden
        # And takes a screenshot
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |


    Scenario Outline: <Device> - As and ATP XTreeM user I can verify the date-time-range field helper text
        XT-85804
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/DatetimeRange"
        Then the "Date-time Range" titled page is displayed

        Given the user selects the "Helper text" labelled text field on the main page
        When the user writes "Sample text" in the text field
        And the user presses Tab

        Given the user selects the "field" bound date-time-range field on the main page
        Then the helper text of the date-time-range field is "Sample text"
        Then the helper text of the date-time-range field is displayed
        # And takes a screenshot

        Given the user selects the "Is helper text hidden" labelled checkbox field on the main page
        When the user ticks the checkbox field

        Given the user selects the "field" bound date-time-range field on the main page
        Then the helper text of the date-time-range field is hidden
        # And takes a screenshot
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |


    Scenario Outline: <Device> - As and ATP XTreeM user I can verify if the date-time-range field is displayed or hidden
        XT-85804
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/DatetimeRange"
        Then the "Date-time Range" titled page is displayed

        Then the "field" bound date-time-range field on the main page is displayed
        # And takes a screenshot

        Given the user selects the "Is hidden" labelled checkbox field on the main page
        When the user ticks the checkbox field
        Then the "field" bound date-time-range field on the main page is hidden
        # And takes a screenshot
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline:<Device> - As and ATP XTreeM user I can verify if the date-time-range field is enabled or disabled
        XT-85804
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/DatetimeRange"
        Then the "Date-time Range" titled page is displayed

        Given the user selects the "field" bound date-time-range field on the main page
        Then the date-time-range field is enabled
        # And takes a screenshot

        Given the user selects the "Is disabled" labelled checkbox field on the main page
        When the user ticks the checkbox field

        Given the user selects the "field" bound date-time-range field on the main page
        Then the date-time-range field is disabled
        # And takes a screenshot
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |


    Scenario Outline: <Device> - As and ATP XTreeM user I can verify if the date-time-range field is read-only
        XT-85804
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/DatetimeRange"
        Then the "Date-time Range" titled page is displayed

        Given the user selects the "field" bound date-time-range field on the main page
        Given the user selects the "Is read only" labelled checkbox field on the main page
        When the user ticks the checkbox field

        Then the date-time-range field is read-only
        # And takes a screenshot
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |


    Scenario Outline: <Device> - As an ATP XTreeM User I can verify the date-time-range field is mandatory
        XT-85804
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/DatetimeRange"
        Then the "Date-time Range" titled page is displayed

        Given the user selects the "Title" labelled text field on the main page
        When the user writes "Sample title" in the text field
        And the user presses Tab

        Given the user selects the "Is mandatory" labelled checkbox field on the main page
        When the user ticks the checkbox field

        Given the user selects the "field" bound date-time-range field on the main page
        Then the date-time-range field is mandatory
        # And takes a screenshot
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |


    Scenario Outline: <Device> - As an ATP XTreeM User I can verify the time zone value is displayed / hidden
        XT-85798
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/DatetimeRange"
        Then the "Date-time Range" titled page is displayed

        Given the user selects the "Title" labelled text field on the main page
        When the user writes "Sample title" in the text field
        And the user blurs the text field

        Given the user selects the "Sample title" labelled date-time-range field on the main page
        Then the time-zone in the date-time-range field is displayed
        # And takes a screenshot

        Given the user selects the "Timezone hidden" labelled checkbox field on the main page
        When the user ticks the checkbox field

        Given the user selects the "Sample title" labelled date-time-range field on the main page
        Then the time-zone in the date-time-range field is hidden
        # And takes a screenshot

        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: As a user I can set a date time range and it is localized for locale <Language>
        XT-81904
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-show-case/StandardShowCaseProduct/eyJfaWQiOiIyNjYifQ=="
        When the user switches language to <Language>
        And the user refreshes the screen
        Then the "Product 266" titled page is displayed

        # set date time range and check the values
        When the user selects the "Manufactured within" labelled date-time-range field on the main page
        And the user selects the "April" month of the start date-time-range field
        And the user selects the "2025" year of the start date-time-range field
        And the user selects the "7" day in the start date-time-range field
        And the user writes <Time> in time field of the start date-time-range field
        And <Click PM Step for enUS>
        And the user selects the "May" month of the end date-time-range field
        And the user selects the "2026" year of the end date-time-range field
        And the user selects the "31" day in the end date-time-range field
        And the user writes "07:30" in time field of the end date-time-range field
        And the user presses Tab
        And the user presses Tab
        And the user presses Tab
        And the user clicks the "$standardSaveAction" bound business action button on the main page
        And the user refreshes the screen
        Then the value of the start date-time-range field is <Start Date-Time>
        And the value of the end date-time-range field is <End Date-Time>

        # set No start and No end date time range and check the values
        When the user ticks the <No end date> checkbox of the end date-time-range field
        And the user ticks the <No start date> checkbox of the start date-time-range field
        And the user presses Shift+Tab
        And the user presses Shift+Tab
        And the user presses Shift+Tab
        And the user clicks the "$standardSaveAction" bound business action button on the main page
        And the user refreshes the screen
        Then the value of the start date-time-range field is ""
        And the value of the end date-time-range field is ""

        Examples:
            | Language     | Time    | Start Date-Time         | End Date-Time           | No start date          | No end date          | Click PM Step for enUS                                                    |
            | "English US" | "10:15" | "04/07/2025 - 10:15 PM" | "05/31/2026 - 07:30 AM" | "No start date"        | "No end date"        | the user clicks the "PM" toggle button of the start date-time-range field |
            | "English GB" | "22:15" | "07/04/2025 - 22:15"    | "31/05/2026 - 07:30"    | "No start date"        | "No end date"        | the user waits for 0 seconds                                              |
            | "Spanish"    | "22:15" | "07/04/2025 - 22:15"    | "31/05/2026 - 07:30"    | "Sin fecha de inicio"  | "Sin fecha de fin"   | the user waits for 0 seconds                                              |
            | "French"     | "22:15" | "07/04/2025 - 22:15"    | "31/05/2026 - 07:30"    | "Pas de date de début" | "Pas de date de fin" | the user waits for 0 seconds                                              |
            | "German"     | "22:15" | "07.04.2025 - 22:15"    | "31.05.2026 - 07:30"    | "Kein Startdatum"      | "Kein Enddatum"      | the user waits for 0 seconds                                              |
            # to be uncommented when Polish translation is added XT-95961 is fixed
            # | "Polish"     | "22:15" | "07.04.2025 - 22:15"    | "31.05.2026 - 07:30"    | "No start date"        | "No end date"        | the user waits for 0 seconds                                              |
            | "Portuguese" | "22:15" | "07/04/2025 - 22:15"    | "31/05/2026 - 07:30"    | "Sem data de início"   | "Sem data de fim"    | the user waits for 0 seconds                                              |
            | "Chinese"    | "22:15" | "2025/04/07 - 22:15"    | "2026/05/31 - 07:30"    | "无开始日期"           | "无结束日期"         | the user waits for 0 seconds                                              |
