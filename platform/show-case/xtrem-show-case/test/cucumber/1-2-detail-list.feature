Feature: 1-2 Detail list
    # Tests the display and verification of different field types within a detail list, including text fields, numeric fields, link fields, and conditional display elements


    Sc<PERSON>rio: As an ATP XTreeM user I can verifiy the value in a detail list
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/DetailList/eyJfaWQiOiIxIn0="
        Then the title of the item on the row "1" in the detail list is "ID:"
        Then the value of the "ID" labelled nested text field of the row 1 in the detail list is "1"

        Then the title of the item on the row "2" in the detail list is "Description"
        Then the value of the "Description" labelled nested text field of the row 2 in the detail list is "Public-key"

        Then the title of the item on the row "4" in the detail list is "Quantity"
        Then the value of the "Quantity" labelled nested numeric field of the row 4 in the detail list is "2"

        Then the title of the item on the row "5" in the detail list is "Product"
        Then the value of the "Product" labelled nested link field of the row 5 in the detail list is "Spinach - Baby"

        Then the title of the item on the row "6" in the detail list is "Conditional display"
        Then the value of the "Conditional display" labelled nested label field of the row 6 in the detail list is "Only displayed with even IDs"

        Then the title of the item on the row "9" in the detail list is "Provider"
        Then the value of the "Provider" labelled nested reference field of the row 9 in the detail list is "Ali Express"

        Then the title of the item on the row "10" in the detail list is "ID:"
        Then the value of the "ID" labelled nested text field of the row 10 in the detail list is "2"

        Then the title of the item on the row "11" in the detail list is "Description"
        Then the value of the "Description" labelled nested text field of the row 11 in the detail list is "zero tolerance"


    Scenario: AS an ATP XTreeM user I can click on the link field in a detail list
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/DetailList/eyJfaWQiOiIxIn0="

        Then the title of the item on the row "5" in the detail list is "Product"
        Then the value of the "Product" labelled nested link field of the row 5 in the detail list is "Spinach - Baby"
        When the user selects the nested link field with the value "Spinach - Baby" of the row "5" in the detail list
        Then the "Product Spinach - Baby" titled page is displayed
