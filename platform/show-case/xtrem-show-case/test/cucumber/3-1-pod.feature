Feature: 3-1 Pod
    # Tests the pod component functionality, including helper text display, field validation, container behavior across different devices and control the pod field state.

    Scenario Outline: <Device> - As a user I want to verify the helper text of the pod field
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/PodField/eyJfaWQiOiIxMDYifQ=="
        Then the "Pod as a field" titled page is displayed
        And the user selects the "Helper text" labelled text field on the main page
        When the user writes "Sample text" in the text field
        And the user blurs the text field
        And the user selects the "Provider" labelled pod field on the main page
        Then the helper text of the pod field is "Sample text"
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> - As a user I want to verify the helper text of the pod block
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/PodBlock/eyJfaWQiOiIxMDYifQ=="
        Then the "Pod as a block" titled page is displayed
        And the user selects the "Helper text" labelled text field on the main page
        When the user writes "Sample text" in the text field
        And the user blurs the text field
        And the user selects the "provider" bound pod field on the main page
        Then the helper text of the pod field is "Sample text"
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> - As a user I want to verify the pod field is enabled or disabled
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/PodField/eyJfaWQiOiIxMDYifQ=="
        Then the "Pod as a field" titled page is displayed
        And the user selects the "Provider" labelled pod field on the main page
        And the pod field is enabled
        And the user selects the "Is disabled" labelled checkbox field on the main page
        When the user ticks the checkbox field
        And the user selects the "Provider" labelled pod field on the main page
        Then the pod field is disabled
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> - As a user I want to verify the pod block is enabled or disabled
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/PodBlock/eyJfaWQiOiIxMDYifQ=="
        Then the "Pod as a block" titled page is displayed
        And the user selects the "Provider" labelled pod field on the main page
        And the pod field is enabled
        And the user selects the "Is disabled" labelled checkbox field on the main page
        When the user ticks the checkbox field
        And the user selects the "Provider" labelled pod field on the main page
        Then the pod field is disabled
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> - As a user I can add a pod field (defined as field)
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/PodField/eyJfaWQiOiIkbmV3In0="
        Then the "Pod as a field" titled page is displayed
        When the user selects the "Provider" labelled pod field on the main page
        And the user clicks the "Add an item" button of the pod field
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> - As a user I can add a pod field (defined as block)
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/PodBlock/eyJfaWQiOiIxMDYifQ=="
        Then the "Pod as a block" titled page is displayed
        And the user selects the "Is removable" labelled checkbox field on the main page
        When the user ticks the checkbox field
        And the user selects the "Provider" labelled pod field on the main page
        And the user clicks the "Close" icon of the pod field
        When the user selects the "Provider" labelled pod field on the main page
        And the user clicks the "Add an item" button of the pod field
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> - As a user I can remove a pod field (defined as field)
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/PodField/eyJfaWQiOiIxMDYifQ=="
        Then the "Pod as a field" titled page is displayed
        And the user selects the "Is removable" labelled checkbox field on the main page
        When the user ticks the checkbox field
        And the user selects the "Provider" labelled pod field on the main page
        And the user clicks the "Close" icon of the pod field
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> - As a user I can remove a pod field (defined as block)
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/PodBlock/eyJfaWQiOiIxMDYifQ=="
        Then the "Pod as a block" titled page is displayed
        And the user selects the "Is removable" labelled checkbox field on the main page
        When the user ticks the checkbox field
        And the user selects the "Provider" labelled pod field on the main page
        And the user clicks the "Close" icon of the pod field
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> - As a user I want to verify the helper text of the pod field is displayed or hidden
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/PodField/eyJfaWQiOiIxMDYifQ=="
        Then the "Pod as a field" titled page is displayed
        And the user selects the "Helper text" labelled text field on the main page
        When the user writes "Sample text" in the text field
        And the user blurs the text field
        And the user selects the "Provider" labelled pod field on the main page
        Then the helper text of the pod field is "Sample text"
        Then the helper text of the pod field is displayed
        And the user selects the "Is helper text hidden" labelled checkbox field on the main page
        When the user ticks the checkbox field
        And the user selects the "Provider" labelled pod field on the main page
        Then the helper text of the pod field is hidden
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> - As a user I want to verify the helper text of the pod block is displayed or hidden
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/PodBlock/eyJfaWQiOiIxMDYifQ=="
        Then the "Pod as a block" titled page is displayed
        And the user selects the "Helper text" labelled text field on the main page
        When the user writes "Sample text" in the text field
        And the user blurs the text field
        And the user selects the "Provider" labelled pod field on the main page
        Then the helper text of the pod field is "Sample text"
        Then the helper text of the pod field is displayed
        And the user selects the "Is helper text hidden" labelled checkbox field on the main page
        When the user ticks the checkbox field
        And the user selects the "Provider" labelled pod field on the main page
        Then the helper text of the pod field is hidden
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> - As a user I want to verify the pod field is diplayed or hidden
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/PodField/eyJfaWQiOiIxMDYifQ=="
        Then the "Pod as a field" titled page is displayed
        And the "Provider" labelled pod field on the main page is displayed
        And the user selects the "Is hidden" labelled checkbox field on the main page
        When the user ticks the checkbox field
        Then the "Provider" labelled pod field on the main page is hidden
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> - As a user I want to verify the pod block is diplayed or hidden
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/PodBlock/eyJfaWQiOiIxMDYifQ=="
        Then the "Pod as a block" titled page is displayed
        And the "Provider" labelled pod field on the main page is displayed
        And the user selects the "Is hidden" labelled checkbox field on the main page
        When the user ticks the checkbox field
        Then the "Provider" labelled pod field on the main page is hidden
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> - As a user I want to verify the pod field is read-only
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/PodField/eyJfaWQiOiIxMDYifQ=="
        Then the "Pod as a field" titled page is displayed
        And the user selects the "Is readOnly" labelled checkbox field on the main page
        When the user ticks the checkbox field
        And the user selects the "Provider" labelled pod field on the main page
        Then the pod field is read-only
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> - As a user I want to verify the pod block is read-only
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/PodBlock/eyJfaWQiOiIxMDYifQ=="
        Then the "Pod as a block" titled page is displayed
        And the user selects the "Is readOnly" labelled checkbox field on the main page
        When the user ticks the checkbox field
        And the user selects the "Provider" labelled pod field on the main page
        Then the pod field is read-only
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> - As a user I want to verify the title of the pod field is displayed or hidden
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/PodField/eyJfaWQiOiIxMDYifQ=="
        Then the "Pod as a field" titled page is displayed
        And the user selects the "Title" labelled text field on the main page
        When the user writes "Sample title" in the text field
        And the user blurs the text field
        And the user selects the "Sample title" labelled pod field on the main page
        Then the title of the pod field is "Sample title"
        Then the title of the pod field is displayed
        And the user selects the "Is title hidden" labelled checkbox field on the main page
        When the user ticks the checkbox field
        And the user selects the "Sample title" labelled pod field on the main page
        Then the title of the pod field is hidden
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> - As a user I want to verify the title of the pod field
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/PodField/eyJfaWQiOiIxMDYifQ=="
        Then the "Pod as a field" titled page is displayed
        And the user selects the "Title" labelled text field on the main page
        When the user writes "Sample title" in the text field
        And the user blurs the text field
        And the user selects the "provider" bound pod field on the main page
        Then the title of the pod field is "Sample title"
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> -  As a user I want to verify the title of the pod block
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/PodBlock/eyJfaWQiOiIxMDYifQ=="
        Then the "Pod as a block" titled page is displayed
        And the user selects the "Title" labelled text field on the main page
        When the user writes "Sample title" in the text field
        And the user blurs the text field
        And the user selects the "provider" bound pod field on the main page
        Then the title of the pod field is "Sample title"
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> - As a user I can click the action of the given pod field
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/PodBlock/eyJfaWQiOiIxMDYifQ=="
        Then the "Pod as a block" titled page is displayed
        When the user selects the "Provider" labelled pod field on the main page
        And the user clicks the "Action 1" action of the pod field
        Then a toast containing text "Action 1 triggered" is displayed
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> -  As a user I can verify the pod field action is enabled or disabled
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/PodBlock/eyJfaWQiOiIxMDYifQ=="
        Then the "Pod as a block" titled page is displayed
        And the user selects the "Provider" labelled pod field on the main page
        And the action "Action 1" of the pod field is enabled
        And the user selects the "Disable action 1" labelled checkbox field on the main page
        When the user ticks the checkbox field
        Then the user selects the "Provider" labelled pod field on the main page
        And the action "Action 1" of the pod field is disabled
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> -  As a user I can verify the pod field action is displayed or hidden
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/PodBlock/eyJfaWQiOiIxMDYifQ=="
        Then the "Pod as a block" titled page is displayed
        And the user selects the "Provider" labelled pod field on the main page
        And the action "Action 2" of the pod field is displayed
        And the user selects the "Hide action 2" labelled checkbox field on the main page
        When the user ticks the checkbox field
        Then the user selects the "Provider" labelled pod field on the main page
        And the action "Action 2" of the pod field is hidden
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> - As an ATP XTreeM user I can verify the pod field has no data to display
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/PodField/eyJfaWQiOiIxMDYifQ=="
        And the "Pod as a field" titled page is displayed
        When the user selects the "Is removable" labelled checkbox field on the main page
        And the user ticks the checkbox field
        And the user selects the "Provider" labelled pod field on the main page
        And the pod field is not empty
        When the user clicks the "Close" icon of the pod field
        Then the pod field is empty
        And the pod field header container value is "No data to display."
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> - As an ATP XTreeM user I can verify the pod block has no data to display
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/PodBlock/eyJfaWQiOiIxMDYifQ=="
        Then the "Pod as a block" titled page is displayed
        When the user selects the "Is removable" labelled checkbox field on the main page
        And the user ticks the checkbox field
        And the user selects the "Provider" labelled pod field on the main page
        And the pod field is not empty
        When the user clicks the "Close" icon of the pod field
        Then the pod field is empty
        And the pod field header container value is "No data to display."
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |
