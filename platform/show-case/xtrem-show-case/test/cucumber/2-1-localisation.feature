Feature: 2-1 Localisation
    # Tests internationalization functionality across UI components, verifying that text elements are properly localized according to selected language settings

    Scenario: As a developer I want the decorator to be localised
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ActionButtons"
        And the user selects the "resultField" bound text field on the main page
        Then the title of the text field is "Selected action"
        When the user switches language to "Spanish"
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ActionButtons"
        And the user selects the "resultField" bound text field on the main page
        Then the title of the text field is "Acción seleccionada"


    Scenario: As a developer I want the localize function to translate runtime messages
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ActionButtons"
        And the user clicks the "Save" labelled business action button on the main page
        And the user selects the "resultField" bound text field on the main page
        Then the value of the text field is "Save CRUD button"
        Then the user switches language to "Spanish"
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ActionButtons"
        And the user clicks the "Save" labelled business action button on the main page
        And the user selects the "resultField" bound text field on the main page
        Then the value of the text field is "Guardar botón CRUD"

    # Scenario: As a developer I want the localize radio button members
    #    Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseProduct/eyJfaWQiOiIxNDIifQ=="
    #    Then the user switches language to "French"
    #    Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseProduct/eyJfaWQiOiIxNDIifQ=="
    #    Then  the value of the "Prix Net" labelled numeric field on the main page is "36.72"
    #    When the user selects the label "Affreux" in the "Catégorie" labelled radio field on the main page
    #    Then the value "awful" in the "Catégorie" labelled radio field on the main page is selected
    #    When the user selects the label "Pas mal" in the "Catégorie" labelled radio field on the main page
    #    Then the value "notBad" in the "Catégorie" labelled radio field on the main page is selected
    #    When the user selects the label "Ok" in the "Catégorie" labelled radio field on the main page
    #    Then the value "ok" in the "Catégorie" labelled radio field on the main page is selected
    #    When the user selects the label "Bon" in the "Catégorie" labelled radio field on the main page
    #    Then the value "good" in the "Catégorie" labelled radio field on the main page is selected
    #    When the user selects the label "Super" in the "Catégorie" labelled radio field on the main page
    #    Then the value "great" in the "Catégorie" labelled radio field on the main page is selected

    Scenario: As a developer I want the localize the page plular object name
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseProduct"
        Then the "Products" titled page is displayed
        Then the user switches language to "French"
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseProduct"
        Then the "Produits" titled page is displayed

    Scenario: As a developer I want the localize the page singular object name
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseProduct/eyJfaWQiOiIxNDIifQ=="
        Then the "Product Cake - Cake Sheet Macaroon" titled page is displayed
        Then the user switches language to "French"
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseProduct/eyJfaWQiOiIxNDIifQ=="
        Then the "Produit Cake - Cake Sheet Macaroon" titled page is displayed

    # Scenario: As a developer I want the localize field labels
    #    Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseProduct"
    #    Then the user switches language to "French"
    #    Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseProduct/eyJfaWQiOiIxNDIifQ=="
    #    Then  the value of the "Prix Net" labelled numeric field on the main page is "36.72"
    #    Then  the value of the "Produit" labelled text field on the main page is "Cake - Cake Sheet Macaroon"
    #    Then  the value of the "VAT" labelled numeric field on the main page is "89.32"

    Scenario: As a user I want to see the value of the nested numeric field formatted according with User's locale
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Table/eyJfaWQiOjJ9"
        And the user selects the "field" bound table field on the main page
        And the user selects the row 1 of the table field
        Then  the value of the "listPrice" bound nested numeric field of the selected row in the table field is "9.94"
        When the user switches language to "Spanish"
        And the user navigates to the following link: "@sage/xtrem-show-case/Table/eyJfaWQiOjJ9"
        And the user selects the "field" bound table field on the main page
        And the user selects the row 1 of the table field
        Then  the value of the "listPrice" bound nested numeric field of the selected row in the table field is "9,94"

    Scenario: As a user I want to set the value of the nested numeric field formatted according with User's locale
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Table/eyJfaWQiOjJ9"
        Then the "Field - Table" titled page is displayed
        And the user selects the "field" bound table field on the main page
        And the user selects the row 1 of the table field
        And the user writes "16000" in the "qty" bound nested numeric field of the selected row in the table field
        Then  the value of the "qty" bound nested numeric field of the selected row in the table field is "16,000"
        When the user switches language to "Spanish"
        And the user navigates to the following link: "@sage/xtrem-show-case/Table/eyJfaWQiOjJ9"
        And the user selects the "field" bound table field on the main page
        And the user selects the row 1 of the table field
        And the user writes "16000" in the "qty" bound nested numeric field of the selected row in the table field
        Then  the value of the "qty" bound nested numeric field of the selected row in the table field is "16.000"

    Scenario: As a user I want to to see the value of the numeric field formatted according with User's locale
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Numeric"
        And the user selects the "field" bound numeric field on the main page
        Then the value of the numeric field is "50.72"
        When the user switches language to "Spanish"
        And the user navigates to the following link: "@sage/xtrem-show-case/Numeric"
        And the user selects the "field" bound numeric field on the main page
        Then the value of the numeric field is "50,72"

    Scenario: As a user I want to to set the value of the numeric field formatted according with User's locale
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Numeric"
        When the user selects the "field" bound numeric field on the main page
        And the user writes "16000" in the numeric field
        Then the value of the numeric field is "16,000.00"
        When the user switches language to "Spanish"
        And the user navigates to the following link: "@sage/xtrem-show-case/Numeric"
        And the user selects the "field" bound numeric field on the main page
        And the user writes "16000" in the numeric field
        Then the value of the numeric field is "16.000,00"

    @hardcoded_date
    Scenario Outline: Set the value of the date field in desktop for language <Language> in desktop mode
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/DateField"
        When the user switches language to <Language>
        And the user refreshes the screen
        And the user selects the "field" bound date field on the main page
        And the user writes <Value> in the date field
        Then the value of the date field is <Value>
        # And takes a screenshot
        Examples:
            | Language     | Value        |
            | "English US" | "10/12/2020" |
            | "English GB" | "12/10/2020" |
            | "Spanish"    | "12/10/2020" |
            | "French"     | "12/10/2020" |
            | "German"     | "12.10.2020" |
            | "Polish"     | "12.10.2020" |
            | "Portuguese" | "12/10/2020" |
            | "Chinese"    | "2020/10/12" |

    # Scenario Outline: Set the value of the date field in mobile
    #     Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/DateField"
    #     When the user switches language to <Language>
    #     And the user opens the application on a mobile using the following link: "@sage/xtrem-show-case/DateField"
    #     And the user selects the "field" bound date field on the main page
    #     And the user writes "2020-10-12" in the date field
    #     Then the value of the date field is <Value>
    #     Examples:
    #         | Language     | Value        |
    #         | "English US" | "10/12/2020" |
    #         | "English GB" | "12/10/2020" |
    #         | "Spanish"    | "12/10/2020" |
    #         | "French"     | "12/10/2020" |
    #         | "German"     | "12.10.2020" |
    #         | "Polish"     | "12.10.2020" |
    #         | "Portuguese" | "12/10/2020" |
    #         | "Chinese"    | "2020/10/12" |

    @hardcoded_date
    Scenario Outline: Set the value of the date field when entered in local format for language <Language> in desktop mode
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/DateField"
        When the user switches language to <Language>
        And the user refreshes the screen
        And the user selects the "field" bound date field on the main page
        And the user writes <Date> in the date field
        Then the value of the date field is <Value>
        # And takes a screenshot
        Examples:
            | Language     | Date         | Value        |
            | "English US" | "10/12/2020" | "10/12/2020" |
            | "English GB" | "12/10/2020" | "12/10/2020" |
            | "Spanish"    | "12/10/2020" | "12/10/2020" |
            | "French"     | "12/10/2020" | "12/10/2020" |
            | "German"     | "12.10.2020" | "12.10.2020" |
            | "Polish"     | "12.10.2020" | "12.10.2020" |
            | "Portuguese" | "12/10/2020" | "12/10/2020" |
            | "Chinese"    | "2020/10/12" | "2020/10/12" |

    @hardcoded_date
    Scenario Outline: Set the value of the date field when entered in local format for language <Language> in mobile mode
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/DateField"
        When the user switches language to <Language>
        And the user refreshes the screen
        And the user selects the "field" bound date field on the main page
        And the user writes <Date> in the date field
        Then the value of the date field is <Value>
        # And takes a screenshot
        Examples:
            | Language     | Date         | Value        |
            | "English US" | "10/12/2020" | "10/12/2020" |
            | "English GB" | "12/10/2020" | "12/10/2020" |
            | "Spanish"    | "12/10/2020" | "12/10/2020" |
            | "French"     | "12/10/2020" | "12/10/2020" |
            | "German"     | "12.10.2020" | "12.10.2020" |
            | "Polish"     | "12.10.2020" | "12.10.2020" |
            | "Portuguese" | "12/10/2020" | "12/10/2020" |
            | "Chinese"    | "2020/10/12" | "2020/10/12" |
