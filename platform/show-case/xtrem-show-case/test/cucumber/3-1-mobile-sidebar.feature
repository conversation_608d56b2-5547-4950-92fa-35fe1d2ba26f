Feature: 3-1 Mobile Sidebar
    # Tests the sidebar functionality in mobile view, verifying proper display, interaction, and data editing capabilities within the mobile layout

    Scenario: As a user I want to Add a new row to a table in mobile view
        Given the user opens the application on a mobile using the following link: "@sage/xtrem-show-case/TableWithSidebar/eyJfaWQiOiIyIn0="
        Then the "Field - Table - With sidebar" titled page is displayed
        When the user selects the "field" bound table field on the main page
        And the user adds a new row to the mobile table
        Then the "Products" titled mobile sidebar is displayed
        When the user selects the "product" bound text field on the mobile sidebar
        And the user writes "New Product" in the text field
        And the user blurs the text field
        When the user clicks the "Cancel" button of the dialog on a full width modal
        Then a warn dialog appears on the sidebar
        When the user clicks the "Discard" button of the Confirm dialog on the sidebar
        Then the "Field - Table - With sidebar" titled page is displayed
