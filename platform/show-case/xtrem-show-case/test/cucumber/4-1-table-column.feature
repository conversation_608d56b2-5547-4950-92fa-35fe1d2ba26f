Feature: 4-1 Table column
    # Tests the column functionality within tables, including filtering capabilities, visibility controls, and interaction behaviors for table column configuration

    Scenario: As a developer I want to be able to toggle floating filters
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Table"
        Then the "Field - Table" titled page is displayed
        And the user selects the "field" bound table field on the main page
        When the user clicks the "Show table filters" labelled button of the table field
        And the user clicks the "Hide table filters" labelled button of the table field

    Scenario: As an ATP / XTreeM user I want to control whether the floating filters are enabled or disabled
        XT-86978
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Table"
        Then the "Field - Table" titled page is displayed
        And the user selects the "field" bound table field on the main page
        When the user clicks the "Show table filters" labelled button of the table field
        Then the floating filter of the "Amount" labelled column of the table field is enabled
        Then the floating filter of the "Provider" labelled column of the table field is disabled
    # And takes a screenshot

    Scenario: As a developer I want to check if a column is displayed with bind
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Table/eyJfaWQiOjJ9"
        Then the "Field - Table" titled page is displayed
        And the user selects the "field" bound table field on the main page
        Then the "netPrice" bound column in the table field is displayed
        And the user selects the "field" bound table field on the main page
    # And the "description" bound column in the table field is hidden
    # And the user selects the "columnsList" bound text field on the main page
    # Then the value of the text field is "description"
    # When the user clicks in the "showColumnButton" bound button field on the main page
    # Then the user selects the "field" bound table field on the main page
    # And the "description" bound column in the table field is displayed
    # And the user selects the "columnsList" bound text field on the main page
    # When the user writes "description, netPrice" in the text field
    # And the user clicks in the "hideColumnButton" bound button field on the main page
    # Then the "netPrice, description" bound columns are hidden in the "field" bound table field in the main page

    Scenario: As a developer I want to check if a column is displayed with label
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Table/eyJfaWQiOjJ9"
        Then the "Field - Table" titled page is displayed
        And the user selects the "field" bound table field on the main page
        Then the "Net Price" labelled column in the table field is displayed
    # And the "Description" labelled column in the table field is hidden
    # And the user selects the "columnsList" bound text field on the main page
    # Then the value of the text field is "description"
    # When the user clicks in the "showColumnButton" bound button field on the main page
    # And the user selects the "field" bound table field on the main page
    # Then the "Description" labelled column in the table field is displayed
    # And the user selects the "columnsList" bound text field on the main page
    # When the user writes "description, netPrice" in the text field
    # And the user clicks in the "hideColumnButton" bound button field on the main page
    # # Then the "Net Price, Description" labelled columns are hidden in the "field" bound table field in the main page


    Scenario: As a User I want to order by the Provider Reference field in my table
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TableNestedReference/eyJfaWQiOiI1MDAifQ=="
        Then the "Field - Table (nested reference)" titled page is displayed
        And the user selects the "field" bound table field on the main page
        And the user clicks the "product__provider__textField" bound column of the table field
        And the user selects the row 1 of the table field
        Then the value of the "Provider" labelled nested reference field of the selected row in the table field is "Amazon"
        And the user selects the row 2 of the table field
        Then the value of the "Provider" labelled nested reference field of the selected row in the table field is "Amazon"
        And the user selects the row 3 of the table field
        Then the value of the "Provider" labelled nested reference field of the selected row in the table field is "Barcelona Activa"
        And the user selects the row 4 of the table field
        Then the value of the "Provider" labelled nested reference field of the selected row in the table field is "Decathlon"

    Scenario: As a user I want to filter a transient table
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TransientTable"
        Then the "Field - Table (Transient)" titled page is displayed
        When the user clicks the "loadButton" bound button on the main page
        And the user selects the "field" bound table field on the main page
        When the user selects the row 2 of the table field
        And the user writes "Test value" in the "product" bound nested numeric field of the selected row in the table field
        When the user filters the "product" bound column in the table field with filter "Contains" and value "t v"
        And the user selects the row 1 of the table field
        Then the value of the "product" bound nested text field of the selected row in the table field is "Test value"

    Scenario Outline: As a user I want to filter columns with different criteria
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Table"
        Then the "Field - Table" titled page is displayed
        And the user selects the "field" bound table field on the main page
        When the user filters the "product" bound column in the table field with filter "Contains" and value <Value>
        And the user selects the row 1 of the table field
        Then the value of the "product" bound nested text field of the selected row in the table field is <Text>
        Examples:
            | Value     | Text                  |
            | "Beef"    | "Beef - Bresaola"     |
            | "Truffle" | "Truffle - Peelings"  |
            | "Thyme"   | "Thyme - Dried"       |
            | "Black"   | "Garbag Bags - Black" |

    @custom_reference_filter
    Scenario: As a user I want to clear the filters for a reference field
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseInvoice/eyJfaWQiOiIxIn0="
        Then the "Invoice 1 06/25/2020" titled page is displayed
        And the user selects the "lines" bound table field on the main page
        And the user selects the row 1 of the table field
        Then the value of the "product" labelled nested text field of the selected row in the table field is "Vinegar - White Wine"
        Then the clear selected items link in the filter menu of the "product" labelled column of the table field is disabled
        When the user opens the filter of the "product" labelled column in the table field
        And the user searches "Anisette - Mcguiness" in the filter of the table field
        And the user ticks the item with text "Anisette - Mcguiness" in the filter of the table field
        When the user closes the filter of the "product" labelled column in the table field
        Then the table field is empty
        When the user opens the filter of the "product" labelled column in the table field
        And the clear selected items link in the filter menu of the "product" labelled column of the table field is enabled
        When the user clicks the clear selected items link in the filter menu of the "product" labelled column of the table field
        When the user opens the filter of the "product" labelled column in the table field
        Then the clear selected items link in the filter menu of the "product" labelled column of the table field is disabled
        When the user closes the filter of the "product" labelled column in the table field
        And the user selects the row 1 of the table field
        Then the value of the "product" labelled nested text field of the selected row in the table field is "Vinegar - White Wine"


    @custom_reference_filter
    Scenario: As an ATP XTreeM user I want to filter the data table custom reference column header
        XT-49267

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TableNestedReference/eyJfaWQiOiIxIn0="
        Then the "Field - Table (nested reference)" titled page is displayed
        And the user selects the "field" bound table field on the main page
        When the user opens the filter of the "Provider" labelled column in the table field
        And the user searches "Ali express" in the filter of the table field
        Then the search value of the filter in the table field is "Ali express"


    @custom_reference_filter
    Scenario: As a user I want to filter a single value in a data table custom reference filter and the filter are displayed
        XT-72625
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseInvoice/eyJfaWQiOiIxIn0="
        Then the "Invoice 1 06/25/2020" titled page is displayed
        And the user selects the "lines" bound table field on the main page
        When the user clicks the "Show table filters" labelled button of the table field
        When the user filters the "product" labelled column in the table field with value "Pasta - Lasagne, Fresh"
        And the user selects the row 1 of the table field
        Then the value of the "product" labelled nested text field of the selected row in the table field is "Pasta - Lasagne, Fresh"
    # And takes a screenshot

    @custom_reference_filter
    Scenario: As a user I want to filter a single value in a data table custom reference filter and the filters are hidden
        XT-72625
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseInvoice/eyJfaWQiOiIxIn0="
        Then the "Invoice 1 06/25/2020" titled page is displayed
        And the user selects the "lines" bound table field on the main page
        When the user filters the "product" labelled column in the table field with value "Pasta - Lasagne, Fresh"
        And the user selects the row 1 of the table field
        Then the value of the "product" labelled nested text field of the selected row in the table field is "Pasta - Lasagne, Fresh"
    # And takes a screenshot


    @custom_reference_filter
    Scenario: As a user I want to filter several values in a data table custom reference filter and the filter are displayed
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseInvoice/eyJfaWQiOiIxIn0="
        Then the "Invoice 1 06/25/2020" titled page is displayed
        And the user selects the "lines" bound table field on the main page
        When the user clicks the "Show table filters" labelled button of the table field
        When the user opens the filter of the "product" labelled column in the table field
        And the user searches "Pasta" in the filter of the table field
        And the user ticks the item with text "Pasta - Bauletti, Chicken White" in the filter of the table field
        And the user ticks the item with text "Pasta - Lasagna, Dry" in the filter of the table field
        And the user ticks the item with text "Pasta - Lasagne, Fresh" in the filter of the table field
        # And takes a screenshot
        When the user closes the filter of the "product" labelled column in the table field
        And the user selects the row 1 of the table field
        Then the value of the "product" labelled nested text field of the selected row in the table field is "Pasta - Lasagne, Fresh"
    # And takes a screenshot

    @custom_reference_filter
    Scenario: As a user I want to filter several values in a data table custom reference filter and the filter are hidden
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseInvoice/eyJfaWQiOiIxIn0="
        Then the "Invoice 1 06/25/2020" titled page is displayed
        And the user selects the "lines" bound table field on the main page
        When the user opens the filter of the "product" labelled column in the table field
        And the user searches "Pasta" in the filter of the table field
        And the user ticks the item with text "Pasta - Bauletti, Chicken White" in the filter of the table field
        And the user ticks the item with text "Pasta - Lasagna, Dry" in the filter of the table field
        And the user ticks the item with text "Pasta - Lasagne, Fresh" in the filter of the table field
        # And takes a screenshot
        When the user closes the filter of the "product" labelled column in the table field
        And the user selects the row 1 of the table field
        Then the value of the "product" labelled nested text field of the selected row in the table field is "Pasta - Lasagne, Fresh"
    # And takes a screenshot


    @custom_reference_filter
    Scenario: As an ATP XTreeM user I can clear the selected items link in the data table custom reference filter
        XT-49267

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TableNestedReference/eyJfaWQiOiIxIn0="
        Then the "Field - Table (nested reference)" titled page is displayed
        And the user selects the "field" bound table field on the main page
        When the user opens the filter of the "Provider" labelled column in the table field
        And the user searches "Ali" in the filter of the table field
        Then the clear selected items link in the filter menu of the "Provider" labelled column of the table field is disabled
        When the user ticks the item with text "Ali Express" in the filter of the table field
        Then the clear selected items link in the filter menu of the "Provider" labelled column of the table field is enabled
        And the user clicks the clear selected items link in the filter menu of the "Provider" labelled column of the table field

    @custom_reference_filter
    Scenario: As an ATP XTreeM user I can tick or untick the record of the data table custom reference filter
        XT-49267
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TableNestedReference/eyJfaWQiOiIxIn0="
        Then the "Field - Table (nested reference)" titled page is displayed
        And the user selects the "field" bound table field on the main page
        When the user opens the filter of the "Provider" labelled column in the table field
        And the user searches "Ali" in the filter of the table field
        And the user ticks the item with text "Ali Express" in the filter of the table field
        And the user unticks the item with text "Ali Express" in the filter of the table field


    @simple_filter
    Scenario: As a developer I want to be able to search with floating filters
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Table"
        Then the "Field - Table" titled page is displayed
        When the user selects the "restrictedResultSet" bound table field on the main page
        When the user clicks the "Show table filters" labelled button of the table field
        When the user filters the "product" bound column in the table field with filter "Contains" and value "wine"
        And the user selects the row 1 of the table field
        Then the value of the "product" bound nested text field of the selected row in the table field is "Wine - Cotes Du Rhone"


    @simple_filter
    Scenario: As a user I want to filter tables using numeric columns using decimal point separator
        XT-77087
        XT-58369
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseProduct"
        And the user selects the "$navigationPanel" bound table field on the main page
        When the user clicks the "Open column panel" labelled button of the table field
        When the user ticks the table column configuration with "Net Price" name on the sidebar
        And the user presses Escape
        And the user selects the row 1 of the table field
        Then the value of the "product" bound nested text field of the selected row in the table field is "Anisette - Mcguiness"
        When the user filters the "netPrice" bound column in the table field with value "52.35"
        Then the value of the "product" bound nested text field of the selected row in the table field is "Appetizer - Cheese Bites"

    @simple_filter
    Scenario: As a user I want to filter tables using numeric columns using decimal comma separator
        XT-77087
        XT-58369
        Given the user opens the application on a desktop
        When the user switches language to "German"
        Then the user navigates to the following link: "@sage/xtrem-show-case/ShowCaseProduct"
        And the user selects the "$navigationPanel" bound table field on the main page
        When the user clicks the "Spaltenbereich öffnen" labelled button of the table field
        When the user ticks the table column configuration with "Net Price" name on the sidebar
        And the user presses Escape
        And the user selects the row 1 of the table field
        Then the value of the "product" bound nested text field of the selected row in the table field is "Anisette - Mcguiness"
        When the user filters the "netPrice" bound column in the table field with filter "Gleich" and value "52,35"
        Then the value of the "product" bound nested text field of the selected row in the table field is "Appetizer - Cheese Bites"

    @simple_filter
    Scenario: As a user I want to filter in a data table for a specific value and filter type
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseProduct"
        Then the titled page containing "Products" is displayed
        And the user selects the "$navigationPanel" bound table field on the main page
        When the user filters the "Product" labelled column in the table field with value "Anisette"
        And the user selects the row 1 of the table field
        Then the value of the "product" labelled nested text field of the selected row in the table field is "Anisette - Mcguiness"
        And the user clicks the remove all filters button in the table field
        And the user filters the "Product" labelled column in the table field with value "Anisette"
        Then the value of the "product" labelled nested text field of the selected row in the table field is "Anisette - Mcguiness"
        And the user filters the "Product" labelled column in the table field with value "Appetizer"
        Then the value of the "product" labelled nested text field of the selected row in the table field is "Appetizer - Cheese Bites"
        And the user filters the "Product" labelled column in the table field with filter "Contains" and value "Anisette"
        And the user filters the "Product" labelled column in the table field with filter "Does not contain" and value "Anisette"
        And the user filters the "Product" labelled column in the table field with filter "Starts with" and value "Anisette"
        And the user filters the "Product" labelled column in the table field with filter "Ends with" and value "Anisette"
        And the user filters the "Product" labelled column in the table field with filter "Equal to" and value "Anisette"
        And the user filters the "Product" labelled column in the table field with filter "Not equal to" and value "Anisette"
        And the user filters the "Product" labelled column in the table field with filter "Greater than or equal to" and value "Anisette"
        And the user filters the "Product" labelled column in the table field with filter "Less than or equal to" and value "Anisette"
        And the user filters the "Product" labelled column in the table field with filter "In range" and value "Anisette"
    # And the user filters the "Product" labelled column in the table field with filter "Does not exist" and value "Anisette"


    @mini_filter
    Scenario: As an ATP XTreeM user I want to filter a single value in a data table mini filter and the filters are displayed
        XT-86987
        #simple step definition
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseProduct"
        Then the "Products" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        When the user filters the "Category" labelled column in the table field with value "Good"
        And the user selects the row with text "Anisette - Mcguiness" in the "Product" labelled column header of the table field
        Then the value of the "Category" labelled nested label field of the selected row in the table field is "Good"

    @mini_filter
    Scenario: As an ATP XTreeM user I want to filter a single value in a data table mini filter and the filters are hidden
        XT-86987
        #simple step definition
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseProduct"
        Then the "Products" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        When the user clicks the "Hide table filters" labelled button of the table field
        When the user filters the "Category" labelled column in the table field with value "Good"
        And the user selects the row with text "Anisette - Mcguiness" in the "Product" labelled column header of the table field
        Then the value of the "Category" labelled nested label field of the selected row in the table field is "Good"

    #Syntax to refactor
    @mini_filter
    Scenario: As an ATP XTreeM user I want to filter several values in a data table mini filter and the filters are displayed
        XT-86987
        #advanced step definition
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseProduct"
        Then the "Products" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        When the user opens the filter of the "Category" labelled column in the table field
        And the user searches "Good" in the filter of the table field
        And the user ticks the item with text "Good" in the filter of the table field
        And the user ticks the item with text "Great" in the filter of the table field
        # And takes a screenshot
        When the user closes the filter of the "Category" labelled column in the table field
        And the user selects the row with text "Anisette - Mcguiness" in the "Product" labelled column header of the table field
        Then the value of the "Category" labelled nested label field of the selected row in the table field is "Good"


    @mini_filter
    Scenario: As an ATP XTreeM user I want to filter several values in a data table mini filter and the filters are hidden
        XT-86987
        #advanced step definition
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseProduct"
        Then the "Products" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        When the user clicks the "Hide table filters" labelled button of the table field
        When the user opens the filter of the "Category" labelled column in the table field
        And the user searches "G" in the filter of the table field
        And the user ticks the item with text "Good" in the filter of the table field
        And the user ticks the item with text "Great" in the filter of the table field
        # And takes a screenshot
        When the user closes the filter of the "Category" labelled column in the table field
        And the user selects the row with text "Anisette - Mcguiness" in the "Product" labelled column header of the table field
        Then the value of the "Category" labelled nested label field of the selected row in the table field is "Good"

    @mini_filter
    Scenario: As an ATP XTreeM user I can tick or untick the record of the data table mini filter
        XT-86987
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseProduct"
        Then the "Products" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        When the user opens the filter of the "Category" labelled column in the table field
        And the user searches "Good" in the filter of the table field
        And the user ticks the item with text "Good" in the filter of the table field
        And the user unticks the item with text "Good" in the filter of the table field
