Feature: 3-2 Server validation
    # Tests server-side validation functionality, ensuring proper display of validation error messages next to fields and in error notifications

    <PERSON><PERSON><PERSON>: As a user I want to see a server side validation error displayed next to the field and an error notification
        XT-7160
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/PageDefaultCrudActions/eyJfaWQiOiI0NDQifQ=="
        And the user selects the "Net Price" labelled numeric field on the main page
        When the user writes "-4" in the numeric field
        Then the numeric field is valid
        When the user clicks the "Save" labelled business action button on the main page
        And the user selects the "Net Price" labelled numeric field on the main page
        Then the numeric field is invalid
        Then a validation error message is displayed containing text
            """
            Validation errors

            Net price: You cannot enter a negative value in the  field.
            """

    Scenario: As a user I want to see multiple server side validation errors displayed next to the fields and an error notification
        XT-7160
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/PageDefaultCrudActions/eyJfaWQiOiI0NDQifQ=="
        And the user selects the "Ending date" labelled date field on the main page
        When the user writes "01/01/2020" in the date field
        And the user selects the "Net Price" labelled numeric field on the main page
        When the user writes "-4" in the numeric field
        And the user selects the "Net Price" labelled numeric field on the main page
        Then the numeric field is valid
        And the user selects the "Ending date" labelled date field on the main page
        Then the date field is valid
        When the user clicks the "Save" labelled business action button on the main page
        And the user selects the "Net Price" labelled numeric field on the main page
        Then the numeric field is invalid
        And the user selects the "Ending date" labelled date field on the main page
        Then the date field is invalid
        Then a validation error message is displayed containing text
            """
            Validation errors

            Ending date: You cannot enter a value less than 2020-12-05 in the  field.

            Net price: You cannot enter a negative value in the  field.
            """

    Scenario: As a user I want to see global server errors displayed in a notification
        XT-7160
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/PageDefaultCrudActions/eyJfaWQiOiI0NDQifQ=="
        And the user selects the "Stock" labelled numeric field on the main page
        When the user writes "-4" in the numeric field
        And the user blurs the numeric field
        When the user clicks the "Save" labelled business action button on the main page
        Then a validation error message is displayed containing text
            """
            Validation errors
            Some global error
            """

# # Scenario: As a user I want to see global server errors displayed
# #     XT-20055
# #     Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseProduct/eyJfaWQiOiIxMDYifQ=="
# #     And the user clicks on the record with the text "Appetiser - Bought" in the navigation panel
# #     And the user writes "-4" to the "Quantity" labelled numeric field on the main page
# #     When the user clicks on the item "Save" of the popover action button in the footer
# #     Then the "Quantity" labelled numeric field on the main page is invalid
