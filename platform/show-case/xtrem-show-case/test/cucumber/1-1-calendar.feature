Feature: 1-1 Calendar
  # Tests the calendar component across different devices, verifying display of events, selection of dates, and various calendar view modes

  #Calendar Set / check properties

  Scenario Outline: <Device> - As and ATP XTreeM user I can verify the calendar field title
    XT-25838
    Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Calendar/eyJfaWQiOiIxIn0="
    Then the "Field - Calendar" titled page is displayed

    Given the user selects the "Title" labelled text field on the main page
    When the user writes "Sample title" in the text field
    And the user presses Tab

    Given the user selects the "field" bound calendar field on the main page
    Then the title of the calendar field is "Sample title"
    Then the title of the calendar field is displayed

    Given the user selects the "Is title hidden" labelled checkbox field on the main page
    When the user ticks the checkbox field

    Given the user selects the "field" bound calendar field on the main page
    Then the title of the calendar field is hidden
    Examples:
      | Device  |
      | desktop |
      | tablet  |
      | mobile  |


  Scenario Outline: <Device> - As and ATP XTreeM user I can verify the calendar field helper text
    XT-25838
    Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Calendar/eyJfaWQiOiIxIn0="
    Then the "Field - Calendar" titled page is displayed

    Given the user selects the "Helper text" labelled text field on the main page
    When the user writes "Sample text" in the text field
    And the user presses Tab

    Given the user selects the "field" bound calendar field on the main page
    Then the helper text of the calendar field is "Sample text"
    Then the helper text of the calendar field is displayed

    Given the user selects the "Is helper text hidden" labelled checkbox field on the main page
    When the user ticks the checkbox field

    Given the user selects the "field" bound calendar field on the main page
    Then the helper text of the calendar field is hidden
    Examples:
      | Device  |
      | desktop |
      | tablet  |
      | mobile  |


  Scenario Outline: <Device> - As and ATP XTreeM user I can verify if the calendar field is displayed or hidden
    XT-25838
    Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Calendar/eyJfaWQiOiIxIn0="
    Then the "Field - Calendar" titled page is displayed

    Given the user selects the "field" bound calendar field on the main page
    Then the "field" bound calendar field on the main page is displayed

    Given the user selects the "Is hidden" labelled checkbox field on the main page
    When the user ticks the checkbox field
    Then the "field" bound calendar field on the main page is hidden
    Examples:
      | Device  |
      | desktop |
      | tablet  |
      | mobile  |


  Scenario Outline:<Device> - As and ATP XTreeM user I can verify if the calendar field is enabled or disabled
    XT-25838
    Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Calendar/eyJfaWQiOiIxIn0="
    Then the "Field - Calendar" titled page is displayed

    Given the user selects the "field" bound calendar field on the main page
    Then the calendar field is enabled

    Given the user selects the "Is disabled" labelled checkbox field on the main page
    When the user ticks the checkbox field

    Given the user selects the "field" bound calendar field on the main page
    Then the calendar field is disabled
    Examples:
      | Device  |
      | desktop |
      | tablet  |
      | mobile  |


  Scenario: As a user I want to have different views on the calendar
    Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Calendar"
    Then the "Field - Calendar" titled page is displayed

    When the user selects the "field" bound calendar field on the main page
    And the user switches to the "Month" view on the calendar field
    Then the calendar field in "Month" view is set to "M/T/Y"
    When the user switches to the "Day" view on the calendar field
    Then the calendar field in "Day" view is set to "M/T/Y"
    When the user switches to the "Week" view on the calendar field
    Then the calendar field in "Week" view is set to "M/T/Y"
    When the user switches to the "3 days" view on the calendar field
    Then the calendar field in "3 days" view is set to "M/T/Y"


  Scenario Outline: As a user I want to step <Steps> <Direction> on the <View> calendar
    Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Calendar"
    Then the "Field - Calendar" titled page is displayed

    When the user selects the "field" bound calendar field on the main page
    And the user switches to the <View> view on the calendar field
    And the user navigates <Steps> time <Direction> on the calendar field
    Then the calendar field in <View> view is set to <CalendarFieldValue>
    Examples:
      | View     | Steps | Direction | CalendarFieldValue |
      | "Day"    | "1"   | forward   | "M/(T+1)/Y"        |
      | "Day"    | "2"   | forward   | "M/(T+2)/Y"        |
      | "Day"    | "1"   | backward  | "M/(T-1)/Y"        |
      | "Week"   | "1"   | forward   | "M/(T+7)/Y"        |
      | "Week"   | "1"   | backward  | "M/(T-7)/Y"        |
      | "Week"   | "2"   | backward  | "M/(T-14)/Y"       |
      | "Month"  | "1"   | forward   | "(M+1)/T/Y"        |
      | "Month"  | "2"   | forward   | "(M+2)/T/Y"        |
      | "Month"  | "1"   | backward  | "(M-1)/T/Y"        |
      | "3 days" | "1"   | forward   | "M/(T+3)/Y"        |
      | "3 days" | "1"   | backward  | "M/(T-3)/Y"        |
      | "3 days" | "2"   | backward  | "M/(T-6)/Y"        |


  Scenario: As a user I want to see day events on the calendar labelled calendar field
    Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Calendar"
    Then the "Field - Calendar" titled page is displayed

    Given the user selects the "field" bound calendar field on the main page
    When the user steps to "October 2019" on the calendar field
    When the user switches to the "Day" view on the calendar field
    Then the user checks if there are events for "October 1, 2019" on the calendar field


  Scenario: As a user I want to read the value of a day event on the calendar labelled calendar field
    Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Calendar"
    Then the "Field - Calendar" titled page is displayed

    Given the user selects the "field" bound calendar field on the main page
    When the user steps to "October 2019" on the calendar field
    When the user switches to the "Day" view on the calendar field
    Then the value of event number "1" for "October 1, 2019" on the calendar field is "Steampan - Foil"
