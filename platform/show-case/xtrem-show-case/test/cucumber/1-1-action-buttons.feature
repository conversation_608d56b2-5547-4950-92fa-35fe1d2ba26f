Feature: 1-1 Action buttons
    # Tests the visibility and functionality of business action buttons across different device types, focusing on conditional display based on device and application state

    Sc<PERSON><PERSON>: As a user I want business actions with callback definition to be hidden on mobile
        Given the user opens the application on a mobile using the following link: "@sage/xtrem-show-case/ActionButtonsCallbackHidden"
        Then the "Business action 2" labelled business action button on the main page is visible
        Then the "Business action 1" labelled business action button on the main page is visible
        Given the user selects the "Some actions hidden?" labelled switch field on the main page
        When the user clicks in the switch field
        Then the "Business action 2" labelled business action button on the main page is hidden
        Then the "Business action 1" labelled business action button on the main page is visible
        Given the user selects the "Some actions hidden?" labelled switch field on the main page
        When the user clicks in the switch field
        Then the "Business action 2" labelled business action button on the main page is visible
        Then the "Business action 1" labelled business action button on the main page is visible

    Scenario: As a user I want business actions with callback definition to be hidden on desktop
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ActionButtonsCallbackHidden"
        Then the "Business action 2" labelled business action button on the main page is visible
        Then the "Business action 1" labelled business action button on the main page is visible
        Given the user selects the "Some actions hidden?" labelled switch field on the main page
        When the user clicks in the switch field
        Then the "Business action 2" labelled business action button on the main page is hidden
        Then the "Business action 1" labelled business action button on the main page is visible
        Given the user selects the "Some actions hidden?" labelled switch field on the main page
        When the user clicks in the switch field
        Then the "Business action 2" labelled business action button on the main page is visible
        Then the "Business action 1" labelled business action button on the main page is visible

    Scenario: CRUD action buttons can be disabled and enabled by using callbacks
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ActionButtonsCallbackHidden"
        Then the "Save" labelled business action button on the main page is visible
        Given the user selects the "Some actions hidden?" labelled switch field on the main page
        When the user clicks in the switch field
        Then the "Save" labelled business action button on the main page is hidden
        Given the user selects the "Some actions hidden?" labelled switch field on the main page
        When the user clicks in the switch field
        Then the "Save" labelled business action button on the main page is visible

    Scenario: Business actions can define a click event handler
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ActionButtons"
        When the user clicks the "Business action 2" labelled business action button on the main page
        Given the user selects the "Selected action" labelled text field on the main page
        Then the value of the text field is "Business action 2"

    Scenario: CRUD action buttons can be disabled and enabled
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ActionButtons"
        Then the "Save" labelled business action button on the main page is enabled
        When the user clicks in the "Toggle Save CRUD action disabled" labelled button field on the main page
        Then the "Save" labelled business action button on the main page is disabled
        When the user clicks in the "Toggle Save CRUD action disabled" labelled button field on the main page
        Then the "Save" labelled business action button on the main page is enabled

    Scenario: CRUD action buttons can define a click event handler
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ActionButtons"
        When the user clicks the "Delete" labelled more actions button in the header
        Given the user selects the "Selected action" labelled text field on the main page
        Then the value of the text field is "Delete CRUD button"

    Scenario: As a user, I want to check a record audit data
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseProvider/eyJfaWQiOiIyIn0="
        Then the "Provider 2" titled page is displayed
        When the user clicks the "Record history" labelled more actions button in the header
        Then an info dialog appears on the main page
        And the text in the header of the dialog is "Record history"
        And the text in the body of the dialog contains "Created" on the main page
        When the user clicks the "OK" button of the Confirm dialog
        Then the "Provider 2" titled page is displayed
