Feature: 2-2 Nested default values
    # Tests server control rules for nested default values, ensuring that default values are properly applied to nested fields through server-side logic

    Scenario: As a user I want the nested default values populated when I add a new row
        The nested default values are fetched when a new record in non-transient, node-bound nesting field is requested

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Table"
        When the user clicks in the "addNewItemWithDefaults" bound button field on the main page
        And the user selects the "field" bound table field on the main page
        And the user selects the row 1 of the table field
        Then the value of the "Quantity" labelled nested numeric field of the selected row in the table field is "1"
        Then the value of the "st" bound nested label field of the selected row in the table field is "1"

    Scenario: As a user I want the nested default values to be calculated as I change a cell in a newly added row
        Bulk discount calculation is set in the server: the net price property getting a discount if the quantity is higher than 10

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Table"
        When the user clicks in the "addNewItemWithDefaults" bound button field on the main page
        And the user selects the "field" bound table field on the main page
        And the user selects the row 1 of the table field
        Then the value of the "Quantity" labelled nested numeric field of the selected row in the table field is "1"
        And the user writes "9" in the "Quantity" labelled nested numeric field of the selected row in the table field
        Then the value of the "Net Price" labelled nested numeric field of the selected row in the table field is "0.00"
        And the user writes "50.00" in the "List Price" labelled nested numeric field of the selected row in the table field
        And the user writes "20" in the "Quantity" labelled nested numeric field of the selected row in the table field
        Then the value of the "Net Price" labelled nested numeric field of the selected row in the table field is "45.00"

    Scenario: As a user I do not want the default values to overwrite my manual edits
        The net price is expected to remain 100 as the user manually edits it, even if there is a control rule that sets it based on the list price

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Table"
        When the user clicks in the "addNewItemWithDefaults" bound button field on the main page
        And the user selects the "field" bound table field on the main page
        And the user selects the row 1 of the table field
        And the user writes "100.00" in the "Net Price" labelled nested numeric field of the selected row in the table field
        And the user writes "50.00" in the "List Price" labelled nested numeric field of the selected row in the table field
        Then the value of the "Net Price" labelled nested numeric field of the selected row in the table field is "100.00"
    Scenario: As a user I want default values to be triggered on table rows and affect normal page fields
        XT-1204 There is a total field which is populated by a server-side default value which is calculated by the total of all field rows

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseInvoice"
        And the user clicks the "Create" labelled business action button on the navigation panel
        When the user clicks in the "addNewLineButton" bound button field on the main page
        And the user selects the "Lines" labelled table field on the main page
        And the user selects the row 1 of the table field
        When the user writes "123" in the "Ordered Quantity" labelled nested numeric field of the selected row in the table field
        Then the value of the "Discount Type" labelled nested select field of the selected row in the table field is "Compensation"
        And the user selects the "Total quantity" labelled numeric field on the main page
        Then the value of the numeric field is "123"
        When the user clicks in the "addNewLineButton" bound button field on the main page
        When the user selects the "Lines" labelled table field on the main page
        And the user selects the row 2 of the table field
        When the user writes "5" in the "Ordered Quantity" labelled nested numeric field of the selected row in the table field
        And the user selects the "Total quantity" labelled numeric field on the main page
        Then the value of the numeric field is "128"

    Scenario: As a user I want to be able to add and remove rows from a table field
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseInvoice"
        And the user clicks the "Create" labelled business action button on the navigation panel
        And the user selects the "Lines" labelled table field on the main page
        And the table field is empty
        When the user clicks in the "addNewLineButton" bound button field on the main page
        Then the table field is not empty
        And the user selects the row 1 of the table field
        When the user clicks the "Remove" dropdown action of the selected row of the table field
        Then the table field is empty
