Feature: 4-2 Validation
  # Tests various form validation scenarios, including conditional validation rules, hidden field validation exclusion, and validation error triggering

  <PERSON><PERSON><PERSON>: As a developer I want hidden fields to be excluded from validation
    Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ConditionalValidation"
    When the user clicks in the "validateBlock2Button" bound button field on the main page
    Then an info dialog appears on the main page
    And the text in the body of the dialog is "You need to enter a value."
    When the user clicks the "OK" button of the Message dialog on the main page
    And the user clicks in the "hideField3Button" bound button field on the main page
    Then the "testField3" bound text field on the main page is hidden
    When the user clicks in the "validateBlock2Button" bound button field on the main page
    Then an info dialog appears on the main page
    And the text in the body of the dialog is "All good."

  Scenario: As a developer I want disabled fields to be excluded from validation
    Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ConditionalValidation"
    When the user clicks in the "validateBlock2Button" bound button field on the main page
    Then an info dialog appears on the main page
    And the text in the body of the dialog is "You need to enter a value."
    When the user clicks the "OK" button of the Message dialog on the main page
    And the user clicks in the "disableField3Button" bound button field on the main page
    And the user selects the "testField3" bound text field on the main page
    Then the text field is disabled
    When the user clicks in the "validateBlock2Button" bound button field on the main page
    Then an info dialog appears on the main page
    And the text in the body of the dialog is "All good."

  Scenario: As a developer I want to define page level validation rules
    Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ConditionalValidation"
    And the user clicks in the "hideField3Button" bound button field on the main page
    And the user clicks in the "validatePageButton" bound button field on the main page
    Then an info dialog appears on the main page
    And the text in the body of the dialog is "You need to enter a value. You need to enter a value."
    When the user clicks the "OK" button of the Message dialog on the main page
    And the user selects the "Test field 1" labelled text field on the main page
    When the user writes "Test 1" in the text field
    And the user selects the "Test field 2" labelled text field on the main page
    When the user writes "Test 2" in the text field
    When the user clicks in the "validatePageButton" bound button field on the main page
    Then an info dialog appears on the main page
    And the text in the body of the dialog is "Test field 1 and test field 2 must have the same value."
    When the user clicks the "OK" button of the Message dialog on the main page
    And the user selects the "Test field 2" labelled text field on the main page
    When the user writes "Test 1" in the text field
    And the user clicks in the "validatePageButton" bound button field on the main page
    Then an info dialog appears on the main page
    And the text in the body of the dialog is "All good."


  Scenario: As user I want the children fields of a disabled section to be disabled and excluded from validations
    XT-10531
    Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ConditionalValidationDisabled"
    And the user selects the "Test field 1" labelled text field on the main page
    Then the text field is enabled
    And the user selects the "Test field 2" labelled text field on the main page
    Then the text field is enabled
    And the user selects the "Test field 3" labelled text field on the main page
    Then the text field is enabled
    When the user clicks the "validatePageButton" bound button on the main page
    And the text in the body of the dialog is "You need to enter a value. You need to enter a value. You need to enter a value."
    When the user clicks the "OK" button of the Confirm dialog
    And the user selects the "testField1" bound text field on the main page
    Then the text field is invalid
    And the user selects the "testField2" bound text field on the main page
    Then the text field is invalid
    And the user selects the "testField3" bound text field on the main page
    Then the text field is invalid
    And the user clicks the "disableTestSection" bound button on the main page
    And the user selects the "testField1" bound text field on the main page
    Then the text field is disabled
    And the user selects the "testField2" bound text field on the main page
    Then the text field is disabled
    And the user selects the "testField3" bound text field on the main page
    Then the text field is disabled
    And the user clicks the "validatePageButton" bound button on the main page
    And the text in the body of the dialog is "All good."
    When the user clicks the "OK" button of the Confirm dialog
    And the user selects the "testField1" bound text field on the main page
    Then the text field is valid
    And the user selects the "testField2" bound text field on the main page
    Then the text field is valid
    And the user selects the "testField3" bound text field on the main page
    Then the text field is valid

  Scenario: As user I want the children fields of a disabled block to be disabled and excluded from validations
    XT-10531
    Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ConditionalValidationDisabled"
    And the user selects the "testField1" bound text field on the main page
    Then the text field is enabled
    And the user selects the "testField2" bound text field on the main page
    Then the text field is enabled
    And the user selects the "testField3" bound text field on the main page
    Then the text field is enabled
    And the user clicks the "validatePageButton" bound button on the main page
    And the text in the body of the dialog is "You need to enter a value. You need to enter a value. You need to enter a value."
    When the user clicks the "OK" button of the Confirm dialog
    And the user selects the "testField1" bound text field on the main page
    Then the text field is invalid
    And the user selects the "testField2" bound text field on the main page
    Then the text field is invalid
    And the user selects the "testField3" bound text field on the main page
    Then the text field is invalid
    And the user clicks the "disableTestBlock1" bound button on the main page
    And the user selects the "testField1" bound text field on the main page
    Then the text field is disabled
    And the user selects the "testField2" bound text field on the main page
    Then the text field is disabled
    And the user selects the "testField3" bound text field on the main page
    Then the text field is enabled
    And the user clicks the "validatePageButton" bound button on the main page
    And the text in the body of the dialog is "You need to enter a value."
    When the user clicks the "OK" button of the Confirm dialog
    And the user selects the "testField1" bound text field on the main page
    Then the text field is valid
    And the user selects the "testField2" bound text field on the main page
    Then the text field is valid
    And the user selects the "testField3" bound text field on the main page
    Then the text field is invalid
