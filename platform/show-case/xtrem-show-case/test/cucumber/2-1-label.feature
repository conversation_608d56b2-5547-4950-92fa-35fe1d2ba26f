Feature: 2-1 Label
    # Tests the label component across different devices and control the label field selection, the label value or control the different states of the label field.

    Scenario Outline: <Device> - As an ATP XTreeM User I can verify the label field using label
        XT-25838
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Label"
        Then the "Field - Label" titled page is displayed


        Given the user selects the "Title" labelled text field on the main page
        When the user writes "Sample title" in the text field
        Given the user selects the "Value" labelled text field on the main page
        When the user writes "Sample text" in the text field
        And the user presses Tab

        Given the user selects the "Sample title" labelled label field on the main page
        Then the value of the label field is "Sample text"
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |


    Scenario Outline: <Device> - As an ATP XTreeM User I can verify the label field using bind
        XT-25838
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Label"
        Then the "Field - Label" titled page is displayed

        Given the user selects the "Value" labelled text field on the main page
        When the user writes "Sample text" in the text field
        And the user presses Tab

        Given the user selects the "field" bound label field on the main page
        Then the value of the label field is "Sample text"
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |


    Scenario Outline: <Device> - As an ATP XTreeM User I can scroll to the label field
        XT-25838
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Label"
        Then the "Field - Label" titled page is displayed

        Given the user selects the "field" bound label field on the main page
        And the user scrolls to the label field
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    #Label Field: Set / check properties

    Scenario Outline: <Device> - As and ATP XTreeM user I can verify the label field title
        XT-25838
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Label"
        Then the "Field - Label" titled page is displayed

        Given the user selects the "Title" labelled text field on the main page
        When the user writes "Sample title" in the text field
        And the user presses Tab

        Given the user selects the "field" bound label field on the main page
        When the user clicks in the label field
        Then the title of the label field is "Sample title"
        Then the title of the label field is displayed

        Given the user selects the "Is title hidden" labelled checkbox field on the main page
        When the user ticks the checkbox field

        Given the user selects the "field" bound label field on the main page
        Then the title of the label field is hidden
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |


    Scenario Outline: <Device> - As and ATP XTreeM user I can verify the label field helper text
        XT-25838
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Label"
        Then the "Field - Label" titled page is displayed

        Given the user selects the "Helper text" labelled text field on the main page
        When the user writes "Sample text" in the text field
        And the user presses Tab

        Given the user selects the "field" bound label field on the main page
        When the user clicks in the label field
        Then the helper text of the label field is "Sample text"
        Then the helper text of the label field is displayed

        Given the user selects the "Is helper text hidden" labelled checkbox field on the main page
        When the user ticks the checkbox field

        Given the user selects the "field" bound label field on the main page
        Then the helper text of the label field is hidden
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |


    Scenario Outline: <Device> - As and ATP XTreeM user I can verify if the label field is displayed or hidden
        XT-25838
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Label"
        Then the "Field - Label" titled page is displayed

        Given the user selects the "field" bound label field on the main page
        When the user clicks in the label field
        Then the "field" bound label field on the main page is displayed

        Given the user selects the "Is hidden" labelled checkbox field on the main page
        When the user ticks the checkbox field
        Then the "field" bound label field on the main page is hidden
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |


    Scenario Outline: <Device> - As and ATP XTreeM user I can verify if the label field is enabled or disabled
        XT-25838
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Label"
        Then the "Field - Label" titled page is displayed

        Given the user selects the "field" bound label field on the main page
        Then the label field is enabled

        Given the user selects the "Is disabled" labelled checkbox field on the main page
        When the user ticks the checkbox field

        Given the user selects the "field" bound label field on the main page
        Then the label field is disabled
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |


    Scenario: Trigger custom click event handler
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Label"
        Then the "Field - Label" titled page is displayed

        Then the "clickTriggered" bound label field on the main page is hidden
        Given the user selects the "field" bound label field on the main page
        When the user clicks in the label field
        Then the "clickTriggered" bound label field on the main page is displayed
