Feature: 1-1 Crud duplicate
    # Tests the record duplication functionality, including button state management, dirty data handling, and duplication workflow
    XT-26572

    Scenario: As a user I want the duplicate button to be displayed but disabled if I enter an empty page
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseCountry"
        And the user clicks the "Create" labelled business action button on the navigation panel
        Then the "Duplicate" labelled button in the header is displayed
        And the "Duplicate" labelled button in the header is disabled

    Scenario: As a user I want the duplicate button to be enabled if I enter page with a record
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseCountry/eyJfaWQiOiIyIn0="
        Then the "Duplicate" labelled button in the header is enabled

    Sc<PERSON>rio: As a user I want the dirty dialog displayed if I click the duplicate button and my page is dirty
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseCountry/eyJfaWQiOiIyIn0="
        Given the user selects the "Name" labelled text field on the main page
        When the user writes "test" in the text field
        And the user clicks the "Duplicate" labelled button in the header
        Then an info dialog appears on the main page
        And the user clicks the "Discard" button of the Confirm dialog
        Given the user selects the "Country Code" labelled text field on the main page
        Then the value of the text field is "NEW_CODE_FR"

    Scenario: As a user I want the duplicate button to be displayed but disabled if I enter an empty page
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseCountry/eyJfaWQiOiIkbmV3In0="
        Then the "Duplicate" labelled button in the header is displayed
        And the "Duplicate" labelled button in the header is disabled

    Scenario: As a user I want duplicate action to duplicate the record and execute the duplication control rules
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseCountry/eyJfaWQiOiIyIn0="
        Given the user selects the "Country Code" labelled text field on the main page
        Then the value of the text field is "FR"
        And the user clicks the "Duplicate" labelled button in the header
        Then a validation error message is displayed containing text
            """
            Validation errors
            """
    Scenario: As a user i want to duplicate a site group record and be able to edit the id and name before the record is duplicated
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-authorization/SiteGroup/eyJfaWQiOiIxNSJ9"
        And the user clicks the "duplicate" labelled button in the header
        And the user selects the "id" labelled text field on a modal
        And the user writes "s1_duplication" in the text field
        And the user selects the "name" labelled text field on a modal
        And the user writes "s1_name" in the text field
        And the user selects the "isActive" bound switch field on a modal
        And the switch field is set to "ON"
        And the user turns the switch field "OFF"
        And the user clicks the "Duplicate" labelled business action button on a modal
        And the user navigates to the following link: "@sage/xtrem-authorization/SiteGroup"
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        And the user clicks the "name" bound nested field of the selected row in the table field
        And the user searches for "s1_duplication" in the navigation panel
        And the user clicks the record with the text "s1_duplication" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And the user selects the "isActive" bound switch field on the main page
        And the switch field is set to "OFF"
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        Then a toast containing text "Record deleted" is displayed

    Scenario: As a user I should not be able to duplicate a record with the same ID as an existing record
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-authorization/SiteGroup/eyJfaWQiOiIxNSJ9"
        And the user clicks the "Duplicate" labelled button in the header
        And the user selects the "id" labelled text field on a modal
        And the user writes "S1" in the text field
        And the user selects the "name" labelled text field on a modal
        And the user writes "S1_name" in the text field
        Then a validation error message is displayed containing text
            """
            ID: The operation failed because the record already exists.
            """
        And the user clicks the "Cancel" labelled business action button on a modal
        And the "Site group" subtitled page is displayed
