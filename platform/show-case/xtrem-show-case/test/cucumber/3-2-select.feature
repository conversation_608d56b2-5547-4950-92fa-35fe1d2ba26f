Feature: 3-2 Select
   # Tests the select field component across different devices, verifying selection of options, value display, interaction with the select field and control the select field state.

   Scenario Outline: <Device> - As an ATP XTreeM User I can click select and verify the selected option of the select field using label
      XT-25838
      Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Select"
      Then the "Field - Select" titled page is displayed

      Given the user selects the "Value" labelled select field on the main page
      When the user clicks in the select field
      And the user selects "Not bad" in the select field
      Then the value of the select field is "Not bad"
      Examples:
         | Device  |
         | desktop |
         | tablet  |
         | mobile  |


   Scenario Outline: <Device> - As an ATP XTreeM User I can click select and verify the selected option of the select field using bind
      XT-25838
      Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Select"
      Then the "Field - Select" titled page is displayed

      Given the user selects the "value" bound select field on the main page
      When the user clicks in the select field
      And the user selects "Awful" in the select field
      Then the value of the select field is "Awful"
      Examples:
         | Device  |
         | desktop |
         | tablet  |
         | mobile  |



   Scenario Outline: <Device> - As an ATP XTreeM User I can write select and verify the selected option of the select field using label
      XT-25838
      Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Select"
      Then the "Field - Select" titled page is displayed

      Given the user selects the "Title" labelled text field on the main page
      When the user writes "Sample title" in the text field
      And the user presses Tab

      Given the user selects the "Sample title" labelled select field on the main page
      When the user writes "Not" in the select field
      And the user selects "Not bad" in the select field
      Then the value of the select field is "Not bad"
      And the user clears the select field
      Examples:
         | Device  |
         | desktop |
         | tablet  |
         | mobile  |


   Scenario Outline: <Device> - As an ATP XTreeM User I can write select and verify the selected option of the select field using bind
      XT-25838
      Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Select"
      Then the "Field - Select" titled page is displayed

      Given the user selects the "field" bound select field on the main page
      When the user writes "Go" in the select field
      And the user selects "Good" in the select field
      Then the value of the select field is "Good"
      And the user clears the select field
      Examples:
         | Device  |
         | desktop |
         | tablet  |
         | mobile  |

   Scenario Outline: <Device> - As an ATP XTreeM User I verify the list of option of the select field
      XT-64121
      Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Select"
      Then the "Field - Select" titled page is displayed

      Given the user selects the "Value" labelled select field on the main page
      When the user clicks in the select field
      Then at least the following list of options is displayed for the select field:"Great | Good | Ok | Not bad"

      And the user writes "ZZZ" in the select field
      Then at least the following list of options is displayed for the select field:"No items found"
      Examples:
         | Device  |
         | desktop |
         | tablet  |
         | mobile  |


   Scenario Outline: <Device> - As an ATP XTreeM User I can scroll to the select field
      XT-25838
      Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Select"
      Then the "Field - Select" titled page is displayed

      Given the user selects the "field" bound select field on the main page
      And the user scrolls to the select field
      Examples:
         | Device  |
         | desktop |
         | tablet  |
         | mobile  |

   #select field - set / check properties

   Scenario Outline: <Device> - As and ATP XTreeM user I can verify the select field title
      XT-25838
      Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Select"
      Then the "Field - Select" titled page is displayed

      Given the user selects the "Title" labelled text field on the main page
      When the user writes "Sample title" in the text field
      And the user presses Tab

      Given the user selects the "field" bound select field on the main page
      When the user clicks in the select field
      Then the title of the select field is "Sample title"
      Then the title of the select field is displayed

      Given the user selects the "Is title hidden" labelled checkbox field on the main page
      When the user ticks the checkbox field

      Given the user selects the "field" bound select field on the main page
      Then the title of the select field is hidden
      Examples:
         | Device  |
         | desktop |
         | tablet  |
         | mobile  |


   Scenario Outline: <Device> - As and ATP XTreeM user I can verify the select field helper text
      XT-25838
      Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Select"
      Then the "Field - Select" titled page is displayed

      Given the user selects the "Helper text" labelled text field on the main page
      When the user writes "Sample text" in the text field
      And the user presses Tab

      Given the user selects the "field" bound select field on the main page
      When the user clicks in the select field
      Then the helper text of the select field is "Sample text"
      Then the helper text of the select field is displayed

      Given the user selects the "Is helper text hidden" labelled checkbox field on the main page
      When the user ticks the checkbox field

      Given the user selects the "field" bound select field on the main page
      Then the helper text of the select field is hidden
      Examples:
         | Device  |
         | desktop |
         | tablet  |
         | mobile  |


   Scenario Outline: <Device> - As and ATP XTreeM user I can verify if the select field is displayed or hidden
      XT-25838
      Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Select"
      Then the "Field - Select" titled page is displayed

      Given the user selects the "field" bound select field on the main page
      When the user clicks in the select field
      Then the "field" bound select field on the main page is displayed

      Given the user selects the "Is hidden" labelled checkbox field on the main page
      When the user ticks the checkbox field
      Then the "field" bound select field on the main page is hidden
      Examples:
         | Device  |
         | desktop |
         | tablet  |
         | mobile  |


   Scenario Outline: <Device> - As and ATP XTreeM user I can verify if the select field is enabled or disabled
      XT-25838
      Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Select"
      Then the "Field - Select" titled page is displayed

      Given the user selects the "field" bound select field on the main page
      Then the select field is enabled

      Given the user selects the "Is disabled" labelled checkbox field on the main page
      When the user ticks the checkbox field

      Given the user selects the "field" bound select field on the main page
      Then the select field is disabled
      Examples:
         | Device  |
         | desktop |
         | tablet  |
         | mobile  |


   Scenario Outline: <Device> - As and ATP XTreeM user I can verify if the select field is read-only
      XT-25838
      Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Select"
      Then the "Field - Select" titled page is displayed

      Given the user selects the "Is readOnly" labelled checkbox field on the main page
      When the user ticks the checkbox field

      Given the user selects the "field" bound select field on the main page
      Then the select field is read-only
      Examples:
         | Device  |
         | desktop |
         | tablet  |
         | mobile  |


   Scenario: Trigger custom change event handler
      Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Select"
      Then the "Field - Select" titled page is displayed

      Given the user selects the "field" bound select field on the main page
      When the user clicks in the select field
      And the user selects "Ok" in the select field
      Then the "changeTriggered" bound label field on the main page is displayed


   Scenario: Set the Placeholder of the Select field
      Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Select"
      Then the "Field - Select" titled page is displayed

      Given the user selects the "Placeholder" labelled text field on the main page
      When the user writes "Sample text" in the text field
      And the user blurs the text field
      Given the user selects the "field" bound select field on the main page
      Then the placeholder of the select field is "Sample text"


   Scenario Outline: <Device> - Set the Value of the Select field
      Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Select"
      Then the "Field - Select" titled page is displayed

      Given the user selects the "Value" labelled select field on the main page
      When the user clicks in the select field
      And the user selects "Ok" in the select field

      Given the user selects the "field" bound select field on the main page
      Then the value of the select field is "Ok"
      Examples:
         | Device  |
         | desktop |
         | tablet  |
         | mobile  |

   Scenario: As an ATP XTreeM User I can set the focus on the Select field
      Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Select"
      Then the "Field - Select" titled page is displayed

      When the user clicks in the "focus" bound button field on the main page
      Given the user selects the "field" bound select field on the main page
      Then the focus on the select field is visible


   Scenario: As a user I want to make sure a valid selected item is kept when options are changed dynamically
      Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Select"
      Then the "Field - Select" titled page is displayed

      Given the user selects the "Dynamic" labelled select field on the main page
      When the user clicks in the select field
      And the user selects "Awesome" in the select field
      Then the value of the select field is "Awesome"

      When the user clicks in the "setOptions" bound button field on the main page
      Given the user selects the "Dynamic" labelled select field on the main page
      Then the value of the select field is "Awesome"

      When the user writes "Excellent" in the select field
      And the user selects "Excellent" in the select field
      Then the value of the select field is "Excellent"


   Scenario: As a user I want to make sure an invalid selected item is unselected when options are changed dynamically
      Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Select"
      Then the "Field - Select" titled page is displayed

      Given the user selects the "Dynamic" labelled select field on the main page
      When the user clicks in the select field
      And the user selects "Awful" in the select field
      Then the value of the select field is "Awful"

      When the user clicks in the "setOptions" bound button field on the main page
      Given the user selects the "Dynamic" labelled select field on the main page
      Then the value of the select field is ""

      Given the user selects the "Dynamic" labelled select field on the main page
      When the user clicks in the select field
      And the user selects "Excellent" in the select field
      Then the value of the select field is "Excellent"

   Scenario: As a user I want the helper text to remain in place even if I select a value
      XT-39479
      Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Select"
      Then the "Field - Select" titled page is displayed
      Given the user selects the "Helper text" labelled text field on the main page
      When the user writes "Sample text" in the text field
      And the user presses Tab
      Given the user selects the "field" bound select field on the main page
      Then the helper text of the select field is "Sample text"
      When the user writes "Good" in the select field
      And the user selects "Good" in the select field
      Then the value of the select field is "Good"
      Then the helper text of the select field is "Sample text"
      And the user clears the select field
      Then the value of the select field is ""
      Then the helper text of the select field is "Sample text"
