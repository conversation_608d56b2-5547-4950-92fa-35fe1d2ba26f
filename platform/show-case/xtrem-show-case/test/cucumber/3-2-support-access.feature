Feature: 3-2 Support access
    # Tests support access functionality, verifying that technical support representatives can properly access user accounts and view relevant history

    Feature Description
    Scenario: Open page

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-authorization/SupportAccessHistory"
    #  Then the "Support access" title page is displayed

    Scenario: Allow support access
        And the user selects the "forTime" bound numeric field on the main page
        And the user clicks in the numeric field
        And the user writes "2" in the numeric field
        And the user selects the "Units" labelled dropdown-list field on the main page
        And the user writes "Hours" in the dropdown-list field
        And the value of the dropdown-list field is "Hours"
        And the user clicks in the "allowAccess" bound button field on the main page
        And the user selects the "Support access history" labelled table field on the main page
        Then the user selects the row 1 of the table field
        And the value of the "status" bound nested label field of the selected row in the table field is "Open"
    Scenario: Extend support access
        And the user selects the "forTime" bound numeric field on the main page
        And the user clicks in the numeric field
        And the user writes "2" in the numeric field
        And the user selects the "Units" labelled dropdown-list field on the main page
        And the user writes "Hours" in the dropdown-list field
        And the value of the dropdown-list field is "Hours"
        And the user clicks in the "extendAccess" bound button field on the main page
        And the user selects the "Support access history" labelled table field on the main page
        Then the user selects the row 1 of the table field
        And the value of the "status" bound nested label field of the selected row in the table field is "Open"
    Scenario: Revoke support access
        And the user clicks in the "revokeAccess" bound button field on the main page
        And the user selects the "Support access history" labelled table field on the main page
        Then the user selects the row 1 of the table field
        And the value of the "status" bound nested label field of the selected row in the table field is "Closed"
