# Run this as export TARGET_URL=https://showcase.dev-sagextrem.com/ export loginUserName=<EMAIL> export loginPassword=xxx export timeout=60000 && pnpm run xtrem test z-create-show-case-users.feature --integration

Feature: z-create-show-case-users
  # Tests and automates the creation of user accounts for showcase demonstration purposes, ensuring proper user setup with correct permissions and roles
  Scenario Outline: As show-case admin I want to create accounts for all users
    Given the user opens the application on a desktop
    Given the user opens the application on a desktop using the following link: "@sage/xtrem-authorization/User"
    Then the "Users" titled page is displayed
    And the user selects the "firstName" bound text field on the main page
    And the user clicks in the text field
    And the user selects the "firstName" bound text field on the main page
    And the user writes "<FirstName>" in the text field
    And the user selects the "lastName" bound text field on the main page
    And the user clicks in the text field
    And the user selects the "lastName" bound text field on the main page
    And the user writes "<LastName>" in the text field
    And the user selects the "email" bound text field on the main page
    And the user clicks in the text field
    And the user selects the "email" bound text field on the main page
    And the user writes "<Email>" in the text field
    And the user clicks the "Save" labelled business action button on the main page
    And a toast with text "Record created" is displayed
    And the user searches for "<Email>" in the navigation panel
    And the user clicks the record with the text "<LastName>" in the navigation panel
    Examples:
      | FirstName  | LastName    | Email                       |
      | Admire     | Ngobeni     | <EMAIL>     |
      | Anelle     | Basson      | <EMAIL>      |
      | Bandarra   | Bruno       | <EMAIL>     |
      | Barbaud    | Romain      | <EMAIL>     |
      | Benjamin   | Dean        | <EMAIL>      |
      | Benoit     | Fournier    | <EMAIL>    |
      | Bruno      | Jouhier     | <EMAIL>      |
      | Christophe | Minost      | <EMAIL>  |
      | Cristian   | Popescu     | <EMAIL>    |
      | Cyril      | Chaize      | <EMAIL>       |
      | Cyrille    | Hamon       | <EMAIL>      |
      | David      | Bouffort    | <EMAIL>     |
      | Elisabeth  | Brasille    | <EMAIL> |
      | Fabien     | Bajeot      | <EMAIL>      |
      | Gaetan     | Antic       | <EMAIL>       |
      | Shavonne   | Hendricks   | <EMAIL> |
      | Gonzalo    | Geis        | <EMAIL>       |
      | Oliver     | Partida     | <EMAIL>     |
      | Guillermo  | Gallo       | <EMAIL>    |
      | Ivan       | Gonzalez    | <EMAIL>      |
      | Neville    | Gallimore   | <EMAIL>  |
      | Matteo     | Ciccone     | <EMAIL>     |
      | Mayoc      | Van Niekerk | <EMAIL>   |
      | Sebastien  | Lecoeur     | <EMAIL>  |
      | Yannick    | Mouhat      | <EMAIL>     |
      | Ben        | Fison       | <EMAIL>          |
      | Jordi      | Lagunas     | <EMAIL>      |
      | Stephen    | Williams    | <EMAIL>   |
      | Kiran      | Ramnath     | <EMAIL>      |
      | Eric       | Boyard      | <EMAIL>        |
      | Eric       | Boureau     | <EMAIL>       |
