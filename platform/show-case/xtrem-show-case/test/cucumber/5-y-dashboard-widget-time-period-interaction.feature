Feature: 5 Y dashboard widget time period interaction
    # Tests the time period selection functionality for dashboard widgets, ensuring users can modify and apply time filters to dashboard data

    @ClearDashboards
    Scenario: As an ATP / XTreeM user I can select the period of the table widget in the dashboard

        Given the user opens the application on a HD desktop
        And the dashboard page is displayed
        Then the "Create a dashboard to get started." subtitled empty dashboard is displayed
        When the user clicks the create button on the dashboard
        Then the dashboard creation dialog is displayed

        When the user selects the template with title "Blank template" in the dashboard creation dialog
        And the template with title "Blank template" in the dashboard creation dialog is selected
        And the user clicks the "next" button in the dashboard creation dialog
        Then the "New dashboard" titled dashboard in the dashboard editor is displayed

        # Add widgets
        When the user searches for "Detailed list with products" in the navigation panel
        When the user clicks the Add button of the "Products" titled widget card in the dashboard editor navigation panel
        And the user waits for 2 seconds

        #resize the widgets
        And the user selects the "Products" titled table widget field in the dashboard editor
        And the user increases the widget field by 250,250 pixels

        #Interact with the table widget period element in the dashboard editor
        And the user clicks the "previous" period button in the table widget
        And the user clicks the "next" period button in the table widget

        And the user selects the "day" period type toggle button in the table widget
        And the user selects the "Dec 22, 2024" date period in the table widget

        And the user selects the "week" period type toggle button in the table widget
        And the user selects the "Week 24, 2024" date period in the table widget

        And the user selects the "month" period type toggle button in the table widget
        And the user selects the "June 2024" date period in the table widget

        #save the dashboard
        And the user clicks the "Save" button in the dashboard editor footer
        Then a toast containing text "Dashboard saved." is displayed
        Then the "New dashboard" titled dashboard is displayed


        #Interact with the table widget period element in the dashboard
        And the user selects the "Products" titled table widget field in the dashboard
        And the user clicks the "previous" period button in the table widget
        And the user clicks the "next" period button in the table widget

        And the user selects the "day" period type toggle button in the table widget
        And the user selects the "Dec 22, 2024" date period in the table widget

        And the user selects the "week" period type toggle button in the table widget
        And the user selects the "Week 24, 2024" date period in the table widget

        And the user selects the "month" period type toggle button in the table widget
        And the user selects the "June 2024" date period in the table widget
        And takes a screenshot
