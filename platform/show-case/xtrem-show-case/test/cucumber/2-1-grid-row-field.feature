Feature: 2-1 Grid row field
    # Tests the grid row component which binds to individual records in a table, enabling streamlined inline editing of record data

    The grid row block is bound to a single record of a table field. It allows block like editing of a record and
    automatically bound to the collection value.

    Scenario: As a user I want the grid row block to represent a record I select on a table
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TableWithDetailPanel/eyJfaWQiOiIyIn0="
        And the user selects the "Products" labelled table field on the main page
        And the user selects the row 4 of the table field
        Then the value of the "Id" labelled nested text field of the selected row in the table field is "283"
        Then the value of the "Quantity" labelled nested numeric field of the selected row in the table field is "16"
        Then the value of the "Description" labelled nested text field of the selected row in the table field is "open system"
        And the user selects the row 6 of the table field
        Then the value of the "Id" labelled nested text field of the selected row in the table field is "398"
        Then the value of the "Quantity" labelled nested numeric field of the selected row in the table field is "11"
        Then the value of the "List Price" labelled nested numeric field of the selected row in the table field is "72.65"
        Then the value of the "Description" labelled nested text field of the selected row in the table field is "bi-directional"
        And the user selects the row 4 of the table field
        And the user clicks the "Description" labelled nested field of the selected row in the table field
        And the user selects the "Id" labelled text field on the detail panel
        Then the value of the text field is "283"
        And the user selects the "Description" labelled text field on the detail panel
        Then the value of the text field is "open system"
        And the user selects the "List Price" labelled numeric field on the detail panel
        Then the value of the numeric field is "33.30"
        And the user selects the "Quantity" labelled numeric field on the detail panel
        Then the value of the numeric field is "16"
        And the user selects the "Products" labelled table field on the main page
        And the user selects the row 6 of the table field
        When the user clicks the "Description" labelled nested field of the selected row in the table field
        And the user selects the "Id" labelled text field on the detail panel
        Then the value of the text field is "398"
        And the user selects the "Description" labelled text field on the detail panel
        Then the value of the text field is "bi-directional"
        And the user selects the "List Price" labelled numeric field on the detail panel
        Then the value of the numeric field is "72.65"
        And the user selects the "Quantity" labelled numeric field on the detail panel
        Then the value of the numeric field is "11"


    Scenario: As a user I want the table to update when I modify a field on the grid row block
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TableWithDetailPanel/eyJfaWQiOiIyIn0="
        And the user selects the "Products" labelled table field on the main page
        And the user selects the row 4 of the table field
        Then the value of the "Description" labelled nested text field of the selected row in the table field is "open system"
        When the user clicks the "Description" labelled nested field of the selected row in the table field
        And the user selects the "Description" labelled text field on the detail panel
        Then the value of the text field is "open system"
        When the user writes "Test value" in the text field
        And the user blurs the text field
        And the user selects the "Products" labelled table field on the main page
        And the user selects the row 4 of the table field
        Then the value of the "Description" labelled nested text field of the selected row in the table field is "Test value"

    Scenario: As a user I want the grid row panel to update when I modify the record on the table
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TableWithDetailPanel/eyJfaWQiOiIyIn0="
        And the user selects the "Products" labelled table field on the main page
        And the user selects the row 4 of the table field
        Then the value of the "Description" labelled nested text field of the selected row in the table field is "open system"
        When the user clicks the "Description" labelled nested field of the selected row in the table field
        And the user selects the "Description" labelled text field on the detail panel
        Then the value of the text field is "open system"
        And the user selects the "Products" labelled table field on the main page
        And the user selects the row 4 of the table field
        And the user writes "Test Value" in the "Description" labelled nested numeric field of the selected row in the table field
        And the user selects the "Description" labelled text field on the detail panel
        Then the value of the text field is "Test Value"


    Scenario: As a user I want to use the lookup dialog of a reference field on the grid row block
        XT-4788
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TableWithDetailPanel/eyJfaWQiOiIyIn0="
        And the user selects the "Products" labelled table field on the main page
        And the user selects the row 4 of the table field
        Then the value of the "Provider" labelled nested reference field of the selected row in the table field is "Amazon"
        When the user clicks the "Description" labelled nested field of the selected row in the table field
        And the user selects the "Provider" labelled reference field on the detail panel
        And the user clicks the lookup button of the reference field
        And the user selects the "provider" bound table field on a modal
        And the user selects the row 1 of the table field
        Then the value of the "Provider" labelled nested text field of the selected row in the table field is "Ali Express"
        And the user clicks the "provider" labelled nested field of the selected row in the table field
        And the user selects the "Products" labelled table field on the main page
        And the user selects the row 4 of the table field
        Then the value of the "Provider" labelled nested reference field of the selected row in the table field is "Ali Express"

    Scenario: As a user I want the grid row panel to display nested dropdown-list fields
        XT-31822
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/GridRowBlockWithDropdown/eyJfaWQiOiIxIn0="
        And the user selects the "Products" labelled table field on the main page
        And the user selects the row 1 of the table field
        When the user clicks the "Id" labelled nested field of the selected row in the table field
        And the user selects the "Id" labelled text field on the detail panel
        Then the value of the text field is "249"
        And the user selects the "Category" labelled dropdown-list field on the detail panel
        Then the value of the dropdown-list field is "Awful"
        And the user selects the "Product" labelled text field on the detail panel
        Then the value of the text field is "Soup - Boston Clam Chowder"
