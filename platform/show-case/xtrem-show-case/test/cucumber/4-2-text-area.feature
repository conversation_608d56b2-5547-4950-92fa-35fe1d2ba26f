Feature: 4-2 Text area
   # Tests the text area component across different devices, ensuring proper input, validation and display of multi-line text content and control the text area field state.

   Scenario Outline: <Device> - As an ATP XTreeM User I can vrite and verify a text area field using label
      XT-25838
      Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/TextArea"
      Then the "Text area" titled page is displayed

      Given the user selects the "Title" labelled text field on the main page
      When the user writes "Sample title" in the text field
      And the user presses Tab

      Given the user selects the "Sample title" labelled text area field on the main page
      When the user writes "some information ........and more" in the text area field
      Then the value of the text area field is "some information ........and more"
      Examples:
         | Device  |
         | desktop |
         | tablet  |
         | mobile  |


   Scenario Outline: <Device> - As an ATP XTreeM User I can vrite and verify a text area field using bind
      XT-25838
      Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/TextArea"
      Then the "Text area" titled page is displayed

      Given the user selects the "field" bound text area field on the main page
      And the user writes "some more information ........and even more" in the text area field
      Then the value of the text area field is "some more information ........and even more"
      Examples:
         | Device  |
         | desktop |
         | tablet  |
         | mobile  |

   #text area  field - set / check properties

   Scenario Outline: <Device> - As and ATP XTreeM user I can verify the text area field title
      XT-25838
      Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/TextArea"
      Then the "Text area" titled page is displayed

      Given the user selects the "Title" labelled text field on the main page
      When the user writes "Sample title" in the text field
      And the user presses Tab

      Given the user selects the "field" bound text area field on the main page
      Then the title of the text area field is "Sample title"
      Then the title of the text area field is displayed

      Given the user selects the "Is title hidden" labelled checkbox field on the main page
      When the user ticks the checkbox field

      Given the user selects the "field" bound text area field on the main page
      Then the title of the text area field is hidden
      Examples:
         | Device  |
         | desktop |
         | tablet  |
         | mobile  |


   Scenario Outline: <Device> - As and ATP XTreeM user I can verify the text area field helper text
      XT-25838
      Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/TextArea"
      Then the "Text area" titled page is displayed

      Given the user selects the "Helper text" labelled text field on the main page
      When the user writes "Sample text" in the text field
      And the user presses Tab

      Given the user selects the "field" bound text area field on the main page
      Then the helper text of the text area field is "Sample text"
      Then the helper text of the text area field is displayed

      Given the user selects the "Is helper text hidden" labelled checkbox field on the main page
      When the user ticks the checkbox field

      Given the user selects the "field" bound text area field on the main page
      Then the helper text of the text area field is hidden
      Examples:
         | Device  |
         | desktop |
         | tablet  |
         | mobile  |


   Scenario Outline: <Device> - As and ATP XTreeM user I can verify if the text area field is displayed or hidden
      XT-25838
      Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/TextArea"
      Then the "Text area" titled page is displayed

      Given the user selects the "field" bound text area field on the main page
      Then the "field" bound text area field on the main page is displayed

      Given the user selects the "Is hidden" labelled checkbox field on the main page
      When the user ticks the checkbox field
      Then the "field" bound text area field on the main page is hidden
      Examples:
         | Device  |
         | desktop |
         | tablet  |
         | mobile  |


   Scenario Outline:<Device> - As and ATP XTreeM user I can verify if the text area field is enabled or disabled
      XT-25838
      Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/TextArea"
      Then the "Text area" titled page is displayed

      Given the user selects the "field" bound text area field on the main page
      Then the text area field is enabled

      Given the user selects the "Is disabled" labelled checkbox field on the main page
      When the user ticks the checkbox field

      Given the user selects the "field" bound text area field on the main page
      Then the text area field is disabled
      Examples:
         | Device  |
         | desktop |
         | tablet  |
         | mobile  |


   Scenario Outline: <Device> - As and ATP XTreeM user I can verify if the text area field is read-only
      XT-25838
      Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/TextArea"
      Then the "Text area" titled page is displayed

      Given the user selects the "Is read only" labelled checkbox field on the main page
      When the user ticks the checkbox field

      Given the user selects the "field" bound text area field on the main page
      Then the text area field is read-only
      Examples:
         | Device  |
         | desktop |
         | tablet  |
         | mobile  |
