Feature: Z visual regression 2
    # Tests additional visual components and interaction behaviors, focusing on dropdown positioning, modal displays, and complex UI element interactions

    Scenario: As a user I want the selet dropdown to appear on the top when I edit a select field on the phantom row
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/HeaderSection/eyJfaWQiOiIyIn0="
        Then the "Provider Amazon" titled page is displayed
        When selects the "Products" labelled navigation anchor on the main page
        When the user selects the header section toggle button in the header
        And the user selects the "products" bound table field on the main page
        And the user selects the floating row of the table field
        When the user clicks the "category" bound nested field of the selected row in the table field
        And the user waits 3 seconds
        Then element with css selector "[data-testid='e-table-field-products'] [data-testid='e-ui-select-dropdown']" looks as before

    Scenario: As a developer I want the empty navigation panel to look like before on desktop
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/NavigationPanelEmpty"
        And the user waits 1 seconds
        Then element with test id "e-field-bind-$navigationPanel" looks as before

    <PERSON><PERSON>rio: As a developer I want the empty navigation panel to look like before on mobile
        Given the user opens the application on a mobile using the following link: "@sage/xtrem-show-case/NavigationPanelEmpty"
        And the user waits 1 seconds
        Then element with test id "e-field-bind-$navigationPanel" looks as before

    Scenario: As a developer I want the no results found navigation panel to look like before on desktop
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseProduct"
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user filters the "Product" labelled column in the table field with value "Non existing product"
        And the user presses Escape
        And the user waits 1 seconds
        Then element with test id "e-field-bind-$navigationPanel" looks as before

    Scenario: As a developer I want the no results found navigation panel to look like before on mobile
        Given the user opens the application on a mobile using the following link: "@sage/xtrem-show-case/ShowCaseProduct"
        And the user searches for "Non existing product" in the navigation panel
        And the user waits 1 seconds
        Then element with test id "e-field-bind-$navigationPanel" looks as before

    Scenario: As a developer I want the no results found navigation panel to look like before on desktop in split mode
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseProduct/eyJfaWQiOiIxMDYifQ=="
        Then the user opens the navigation panel
        And the user searches for "Non existing product" in the navigation panel
        Then element with test id "e-field-bind-$navigationPanel" looks as before

    Scenario: As a developer I want the filter label pills on the navigation panel to look like before
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseProduct"
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        When the user filters the "Product" labelled column in the table field with value "Ranchero super large product name to be wrapped with ellipsis"
        And the user presses Escape
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        When the user clicks the "product" bound nested field of the selected row in the table field
        Then element with class "e-filter-label-wrapper" looks as before

    Scenario: As a user I want to toggle a detail panel section and ensure that it displays the inner content
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Accordion/eyJfaWQiOiIxIn0="
        Then element with test id "e-detail-panel-sections" looks as before

    Scenario: As a user I want the detail panel section close button to look like before
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/InlineEdit/eyJfaWQiOiIxIn0="
        Then element with css selector ".e-detail-panel-close" looks as before

    Scenario: As a user I want the header of an invalid nested referece column to look like before
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/InlineEdit/eyJfaWQiOiIxIn0="
        When the user selects the "field" bound table field on the main page
        And the user selects the row 1 of the table field
        And the user writes "" in the "Provider" labelled nested reference field of the selected row in the table field
        Then the "Table title" labelled table field on the main page is invalid
        And element with css selector ".e-nested-header-label-provider" looks as before

    Scenario: As a user I want to see the provided image in the header
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseProvider/eyJfaWQiOiIxIn0="
        Then element with css selector ".e-header-image-container" looks as before

    Scenario: As a user I want to see the initials in the header
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseProvider/eyJfaWQiOiI1In0="
        Then element with css selector ".e-header-image-container" looks as before

    # Scenario: As a user, I want to create the preview of an Indicator Tile widget
    #     Given the user opens the application on a desktop
    #     And the dashboard page is displayed
    #     Then the "Create a dashboard to get started." subtitled empty dashboard is displayed
    #     When the user clicks the create button on the dashboard
    #     Then the dashboard creation dialog is displayed
    #     When the user selects the template 1 in the dashboard creation dialog
    #     And the user clicks the "next" button in the dashboard creation dialog
    #     Then the "New dashboard" titled dashboard in the dashboard editor is displayed
    #     When the user clicks the "createAWidget" labelled button in the dashboard editor navigation panel
    #     Then the "New widget" titled widget editor dialog is displayed
    #     And the value of the step title of the widget editor dialog is "1. Select a widget to get started"
    #     When the user writes "My demo category" in the "basic-category" dropdown field in the widget editor dialog
    #     And the user presses Enter
    #     And the user writes "Show Case Provider" in the "basic-node" dropdown field in the widget editor dialog
    #     And the user presses Enter
    #     And the user writes "Indicator Tile Widget" in the "basic-title" text field in the widget editor dialog
    #     And the user selects the "INDICATOR_TILE" widget card in the widget editor dialog
    #     Then the "INDICATOR_TILE" widget card in the widget editor dialog is selected
    #     When the user clicks the "next" button in the widget editor dialog
    #     Then the value of the step title of the widget editor dialog is "2. Select the data to add to your widget"
    #     When the user selects the "Text field" tree-view element in the widget editor dialog
    #     And the user selects the "Integer field" tree-view element in the widget editor dialog
    #     And the user clicks the "next" button in the widget editor dialog
    #     Then the value of the step title of the widget editor dialog is "3. Add your content"
    #     When the user writes "Integer Field" in the "grouping-property" dropdown field in the widget editor dialog
    #     And the user presses Enter
    #     And the user writes "Maximum" in the "grouping-method" dropdown field in the widget editor dialog
    #     And the user presses Enter
    #     And the user clicks the "next" button in the widget editor dialog
    #     Then the value of the step title of the widget editor dialog is "4. Add your filters"
    #     And the "Add filter" table button in the widget editor dialog is displayed
    #     When the user clicks the "Add filter" table button in the widget editor dialog
    #     Then the "filter-property" dropdown field of row "1" in the widget editor dialog is enabled
    #     When the user writes "Text field" in the "filter-property" dropdown field of row "1" in the widget editor dialog
    #     And the user presses Enter
    #     And the user writes "Contains" in the "filter-type" dropdown field of row "1" in the widget editor dialog
    #     And the user presses Enter
    #     And the user writes "a" in the "filter-value" text field of row "1" in the widget editor dialog
    #     And the user clicks the "add" action button of row "1" in the widget editor dialog
    #     Then the "filter-property" dropdown field of row "2" in the widget editor dialog is enabled
    #     When the user writes "Integer field" in the "filter-property" dropdown field of row "2" in the widget editor dialog
    #     And the user presses Enter
    #     Then the "filter-type" dropdown field of row "2" in the widget editor dialog is enabled
    #     And the user writes "Between" in the "filter-type" dropdown field of row "2" in the widget editor dialog
    #     Then the "filter-value-min" text field of row "2" in the widget editor dialog is enabled
    #     And the "filter-value-max" text field of row "2" in the widget editor dialog is enabled
    #     When the user writes "3" in the "filter-value-min" text field of row "2" in the widget editor dialog
    #     Then the "Invalid integer" validation error of row "2" in the widget editor dialog is displayed
    #     When the user writes "10" in the "filter-value-max" text field of row "2" in the widget editor dialog
    #     Then the "Invalid integer" validation error of row "2" in the widget editor dialog is hidden
    #     When the user clicks the "next" button in the widget editor dialog
    #     Then the value of the step title of the widget editor dialog is "5. Create your layout"
    #     And the "layout-icon" dropdown field in the widget editor dialog is displayed
    #     And the "layout-decimal-digits" text field in the widget editor dialog is enabled
    #     And the "layout-subtitle" text field in the widget editor dialog is enabled
    #     When the user writes "Accounting" in the "layout-icon" dropdown field in the widget editor dialog
    #     And the user writes "2" in the "layout-decimal-digits" text field in the widget editor dialog
    #     And the user writes "new" in the "layout-subtitle" text field in the widget editor dialog
    #     Then element with test id "simple-indicator-PREVIEW_WIDGET" looks as before

    #     #clening up data
    #     When the user clicks the "cancel" button in the widget editor dialog
    #     Then a warn dialog appears on the main page
    #     When the user clicks the "Yes" button of the Confirm dialog
    #     Then the "New dashboard" titled dashboard in the dashboard editor is displayed
    #     And the user waits 1 second
    #     When the user clicks the "cancel" button in the dashboard editor footer
    #     Then the "New dashboard" titled dashboard is displayed
    #     And the user waits 1 second
    #     When the user clicks the "delete" labelled CRUD button in the dashboard action menu
    #     Then a warn dialog appears on the main page
    #     And the user waits 1 second
    #     When the user clicks the "OK" button of the Confirm dialog on the main page
    #     Then a toast containing text "Dashboard deleted." is displayed
    #     And the user dismisses all the toasts
    #     Then the "Create a dashboard to get started." subtitled empty dashboard is displayed
    #     And the user waits 1 second


    # Scenario: As a user, I want to create the preview of a Table widget
    #     Given the user opens the application on a desktop
    #     And the dashboard page is displayed
    #     Then the "Create a dashboard to get started." subtitled empty dashboard is displayed
    #     When the user clicks the create button on the dashboard
    #     Then the dashboard creation dialog is displayed
    #     When the user selects the template 10 in the dashboard creation dialog
    #     And the user clicks the "next" button in the dashboard creation dialog
    #     When the user clicks the "createAWidget" labelled button in the dashboard editor navigation panel
    #     Then the "New widget" titled widget editor dialog is displayed
    #     And the value of the step title of the widget editor dialog is "1. Select a widget to get started"
    #     When the user writes "Table Widget" in the "basic-title" text field in the widget editor dialog
    #     And the user presses Enter
    #     And the user writes "My demo category" in the "basic-category" dropdown field in the widget editor dialog
    #     And the user presses Enter
    #     And the user writes "Show Case Provider" in the "basic-node" dropdown field in the widget editor dialog
    #     And the user presses Enter
    #     And the user selects the "TABLE" widget card in the widget editor dialog
    #     Then the "TABLE" widget card in the widget editor dialog is selected
    #     When the user clicks the "next" button in the widget editor dialog
    #     Then the value of the step title of the widget editor dialog is "2. Select the data to add to your widget"
    #     When the user selects the "Text field" tree-view element in the widget editor dialog
    #     And the user selects the "Integer field" tree-view element in the widget editor dialog
    #     And the user selects the "Date field" tree-view element in the widget editor dialog
    #     And the user selects the "Boolean field" tree-view element in the widget editor dialog
    #     And the user selects the "Min quantity" tree-view element in the widget editor dialog
    #     And the user clicks the "next" button in the widget editor dialog
    #     Then the value of the step title of the widget editor dialog is "3. Add your content"
    #     When the user clicks the "Add column" table button in the widget editor dialog
    #     Then the "content-property" dropdown field of row "1" in the widget editor dialog is enabled
    #     When the user writes "Text Field" in the "content-property" dropdown field of row "1" in the widget editor dialog
    #     And the user presses Enter
    #     Then the value of the "content-title" text field of row "1" in the widget editor dialog is "Text field"
    #     And the value of the "content-presentation" paragraph field of row "1" in the widget editor dialog is "Text"
    #     # go back to data step & uncheck Text Field to check that it's deleted from the content
    #     When the user clicks the "previous" button in the widget editor dialog
    #     Then the value of the step title of the widget editor dialog is "2. Select the data to add to your widget"
    #     And the user unselects the "Text field" tree-view element in the widget editor dialog
    #     When the user clicks the "next" button in the widget editor dialog
    #     Then the value of the step title of the widget editor dialog is "3. Add your content"
    #     And the "Add column" table button in the widget editor dialog is displayed
    #     When the user clicks the "previous" button in the widget editor dialog
    #     Then the value of the step title of the widget editor dialog is "2. Select the data to add to your widget"
    #     When the user selects the "Text field" tree-view element in the widget editor dialog
    #     And the user clicks the "next" button in the widget editor dialog
    #     Then the value of the step title of the widget editor dialog is "3. Add your content"
    #     When the user clicks the "Add column" table button in the widget editor dialog
    #     # Text Field reselected
    #     Then the "content-property" dropdown field of row "1" in the widget editor dialog is enabled
    #     When the user writes "Text Field" in the "content-property" dropdown field of row "1" in the widget editor dialog
    #     And the user presses Enter
    #     Then the value of the "content-title" text field of row "1" in the widget editor dialog is "Text field"
    #     And the value of the "content-presentation" paragraph field of row "1" in the widget editor dialog is "Text"
    #     When the user clicks the "add" action button of row "1" in the widget editor dialog
    #     Then the "content-property" dropdown field of row "2" in the widget editor dialog is enabled
    #     When the user writes "Date Field" in the "content-property" dropdown field of row "2" in the widget editor dialog
    #     And the user presses Enter
    #     Then the value of the "content-title" text field of row "2" in the widget editor dialog is "Date field"
    #     And the value of the "content-presentation" paragraph field of row "2" in the widget editor dialog is "Date"
    #     When the user clicks the "add" action button of row "2" in the widget editor dialog
    #     Then the "content-property" dropdown field of row "3" in the widget editor dialog is enabled
    #     When the user writes "Integer Field" in the "content-property" dropdown field of row "3" in the widget editor dialog
    #     And the user presses Enter
    #     Then the value of the "content-title" text field of row "3" in the widget editor dialog is "Integer field"
    #     When the user writes "Numeric" in the "content-presentation" text field of row "3" in the widget editor dialog
    #     And the user presses Enter
    #     And the user clicks the "add" action button of row "3" in the widget editor dialog
    #     Then the "content-property" dropdown field of row "4" in the widget editor dialog is enabled
    #     And the user writes "Min Quantity" in the "content-property" dropdown field of row "4" in the widget editor dialog
    #     Then the value of the "content-title" text field of row "4" in the widget editor dialog is "Min quantity"
    #     And the "content-presentation" dropdown field of row "4" in the widget editor dialog is enabled
    #     When the user writes "Progress" in the "content-presentation" dropdown field of row "4" in the widget editor dialog
    #     And the user presses Enter
    #     And the user clicks the "next" button in the widget editor dialog
    #     Then the value of the step title of the widget editor dialog is "4. Add your filters"
    #     When the user clicks the "Add filter" table button in the widget editor dialog
    #     Then the "filter-property" dropdown field of row "1" in the widget editor dialog is enabled
    #     When the user writes "Text field" in the "filter-property" dropdown field of row "1" in the widget editor dialog
    #     And the user presses Enter
    #     Then the "filter-type" dropdown field of row "1" in the widget editor dialog is enabled
    #     When the user writes "Contains" in the "filter-type" dropdown field of row "1" in the widget editor dialog
    #     And the user presses Enter
    #     Then the "filter-value" text field of row "1" in the widget editor dialog is enabled
    #     And the user writes "a" in the "filter-value" text field of row "1" in the widget editor dialog
    #     And the user presses Enter
    #     And the user clicks the "next" button in the widget editor dialog
    #     Then the value of the step title of the widget editor dialog is "5. Define sorting"
    #     And the "Add a sort condition" table button in the widget editor dialog is displayed
    #     When the user clicks the "Add a sort condition" table button in the widget editor dialog
    #     Then the "sorting-property" dropdown field of row "1" in the widget editor dialog is enabled
    #     And the "sorting-order" dropdown field of row "1" in the widget editor dialog is enabled
    #     When the user writes "Text Field" in the "sorting-property" dropdown field of row "1" in the widget editor dialog
    #     And the user presses Enter
    #     And the user writes "Ascending" in the "sorting-order" dropdown field of row "1" in the widget editor dialog
    #     And the user presses Enter
    #     When the user clicks the "next" button in the widget editor dialog
    #     Then the value of the step title of the widget editor dialog is "6. Create your layout"
    #     And element with test id "table-PREVIEW_WIDGET" looks as before
    #     #cleaning up data
    #     When the user clicks the "cancel" button in the widget editor dialog
    #     Then a warn dialog appears on the main page
    #     When the user selects the Yes button of the Confirm dialog
    #     Then the "Showcase dashboard" titled dashboard in the dashboard editor is displayed
    #     And the user waits 1 second
    #     When the user clicks the "cancel" button in the dashboard editor footer
    #     Then the "Showcase dashboard" titled dashboard is displayed
    #     And the user waits 1 second
    #     When the user clicks the "delete" labelled CRUD button in the dashboard action menu
    #     Then a warn dialog appears on the main page
    #     And the user waits 1 second
    #     When the user clicks the "OK" button of the Confirm dialog on the main page
    #     Then a toast containing text "Dashboard deleted." is displayed
    #     And the user dismisses all the toasts
    #     Then the "Create a dashboard to get started." subtitled empty dashboard is displayed
    #     And the user waits 1 second

    # Scenario: As a user, I want to check the numeric field looks like as before
    #     Given the user opens the application on a desktop
    #     And the dashboard page is displayed
    #     Then the "Create a dashboard to get started." subtitled empty dashboard is displayed
    #     When the user clicks the create button on the dashboard
    #     Then the dashboard creation dialog is displayed
    #     When the user selects the template 10 in the dashboard creation dialog
    #     And the user clicks the "next" button in the dashboard creation dialog
    #     When the user clicks the "createAWidget" labelled button in the dashboard editor navigation panel
    #     Then the "New widget" titled widget editor dialog is displayed
    #     And the value of the step title of the widget editor dialog is "1. Select a widget to get started"
    #     When the user writes "Table Widget" in the "basic-title" text field in the widget editor dialog
    #     And the user presses Enter
    #     And the user writes "My demo category" in the "basic-category" dropdown field in the widget editor dialog
    #     And the user presses Enter
    #     And the user writes "Show Case Provider" in the "basic-node" dropdown field in the widget editor dialog
    #     And the user presses Enter
    #     And the user selects the "TABLE" widget card in the widget editor dialog
    #     Then the "TABLE" widget card in the widget editor dialog is selected
    #     When the user clicks the "next" button in the widget editor dialog
    #     Then the value of the step title of the widget editor dialog is "2. Select the data to add to your widget"
    #     When the user selects the "Integer field" tree-view element in the widget editor dialog
    #     And the user clicks the "next" button in the widget editor dialog
    #     Then the value of the step title of the widget editor dialog is "3. Add your content"
    #     When the user clicks the "Add column" table button in the widget editor dialog
    #     Then the "content-property" dropdown field of row "1" in the widget editor dialog is enabled
    #     When the user writes "Integer Field" in the "content-property" dropdown field of row "1" in the widget editor dialog
    #     And the user presses Enter
    #     Then the value of the "content-title" text field of row "1" in the widget editor dialog is "Integer field"
    #     And the "content-presentation" dropdown field of row "1" in the widget editor dialog is enabled
    #     When the user writes "Numeric" in the "content-presentation" dropdown field of row "1" in the widget editor dialog
    #     And the user presses Enter
    #     And the user clicks the "next" button in the widget editor dialog
    #     Then the value of the step title of the widget editor dialog is "4. Add your filters"
    #     And the user presses Enter
    #     Then the value of the step title of the widget editor dialog is "5. Define sorting"
    #     And the user clicks the "next" button in the widget editor dialog
    #     Then the value of the step title of the widget editor dialog is "6. Create your layout"
    #     And element with test id "table-PREVIEW_WIDGET" looks as before

    Scenario: As a user I want the tile field to look grey if disabled
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TileContainerPage/eyJfaWQiOiIxMDYifQ=="
        Then the "Tile Container" titled page is displayed
        And the user selects the "Is net price disabled" labelled checkbox field on the main page
        When the user ticks the checkbox field
        Then element with test id "e-field-label-netPrice" looks as before

    Scenario: As a user I want the table sidebar on desktop to look like as before
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TableWithSidebar/eyJfaWQiOiIyIn0="
        And the "Field - Table - With sidebar" titled page is displayed
        And the user selects the "field" bound table field on the main page
        And the user selects the row 7 of the table field
        And the user clicks the "Edit on sidebar" dropdown action of the selected row of the table field
        Then element with css selector "[data-element='sidebar']" looks as before

    Scenario: As a user I want the select all checkbox to look like before when no all partial rows are selected
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseProduct"
        Then element with class "ag-header-select-all" looks as before
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        When the user selects all rows of the table field
        Then element with class "ag-header-select-all" looks as before
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        When the user unticks the main checkbox of the selected row in the table field
        Then element with class "ag-header-select-all" looks as before

    Scenario: As a user I want to know if a field in my sidebar has errors
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TableWithSidebar/eyJfaWQiOiIyIn0="
        And the "Field - Table - With sidebar" titled page is displayed
        And the user selects the "field" bound table field on the main page
        And the user selects the row 1 of the table field
        And the user clicks the "Edit on sidebar" dropdown action of the selected row of the table field
        Then the user selects the "Quantity" labelled numeric field on the sidebar
        And the user writes "-2" in the numeric field
        And the user presses Enter
        Then the numeric field is invalid
        And the "Apply" button of the dialog is disabled on the sidebar
        Then element with css selector "[data-element='sidebar']" looks as before

    Scenario: As a user I want table rows line numbers to look as before
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/NestedGridWithLineNumbers/eyJfaWQiOiI0MCJ9"
        Then element with class "e-table-field-line-number" looks as before

    # @ClearDashboards
    # Scenario: As I user I want the bar chart and its preview to work correctly
    # Given the user opens the application on a desktop
    # Then the dashboard page is displayed
    # And the "Create a dashboard to get started." subtitled empty dashboard is displayed
    # When the user clicks the create button on the dashboard
    # Then the dashboard creation dialog is displayed
    # When the user selects the template 10 in the dashboard creation dialog
    # And the user clicks the "next" button in the dashboard creation dialog
    # Then the "Showcase dashboard" titled dashboard in the dashboard editor is displayed

    # # Widget selection step
    # When the user clicks the "createAWidget" labelled button in the dashboard editor navigation panel
    # Then the "New widget" titled widget editor dialog is displayed
    # And the value of the step title of the widget editor dialog is "1. Select a widget to get started"
    # And the "cancel" button in the widget editor dialog is enabled
    # And the "next" button in the widget editor dialog is disabled
    # When the user writes "My demo category" in the "basic-category" dropdown field in the widget editor dialog
    # And the user presses Enter
    # And the user writes "Show Case Product" in the "basic-node" dropdown field in the widget editor dialog
    # And the user presses Enter
    # And the user writes "Bar Chart Widget" in the "basic-title" text field in the widget editor dialog
    # And the user selects the "LINE_CHART" widget card in the widget editor dialog
    # Then the "LINE_CHART" widget card in the widget editor dialog is selected
    # When the user selects the "BAR_CHART" widget card in the widget editor dialog
    # Then the "LINE_CHART" widget card in the widget editor dialog is unselected
    # And the "BAR_CHART" widget card in the widget editor dialog is selected
    # And the "next" button in the widget editor dialog is enabled

    # # widget data step
    # When the user clicks the "next" button in the widget editor dialog
    # Then the value of the step title of the widget editor dialog is "2. Select the data to add to your widget"
    # And the "previous" button in the widget editor dialog is enabled
    # And the "cancel" button in the widget editor dialog is enabled
    # And the "next" button in the widget editor dialog is disabled
    # When the user searches for "Price" in the widget editor dialog
    # And the user presses Enter
    # Then the "Net price" tree-view element in the widget editor dialog is displayed
    # And the "List price" tree-view element in the widget editor dialog is displayed
    # And the "Amount" tree-view element in the widget editor dialog is hidden
    # When the user clears the search field in the widget editor dialog
    # Then the "Amount" tree-view element in the widget editor dialog is displayed
    # When the user selects the "Net price" tree-view element in the widget editor dialog
    # Then the "next" button in the widget editor dialog is disabled
    # When the user selects the "List price" tree-view element in the widget editor dialog
    # Then the "next" button in the widget editor dialog is enabled
    # When the user selects the "Ending date" tree-view element in the widget editor dialog
    # Then the "next" button in the widget editor dialog is enabled
    # When the user unselects the "List price" tree-view element in the widget editor dialog
    # And the user unselects the "Net price" tree-view element in the widget editor dialog
    # Then the "next" button in the widget editor dialog is disabled
    # When the user selects the "List price" tree-view element in the widget editor dialog
    # Then the "next" button in the widget editor dialog is enabled
    # When the user selects the "Net price" tree-view element in the widget editor dialog
    # And the user selects the "Amount" tree-view element in the widget editor dialog
    # And the user selects the "Fixed quantity" tree-view element in the widget editor dialog
    # And the user selects the "Qty" tree-view element in the widget editor dialog
    # And the user selects the "Discount" tree-view element in the widget editor dialog
    # And the user selects the "Category" tree-view element in the widget editor dialog

    # #widget content step
    # And the user clicks the "next" button in the widget editor dialog
    # Then the value of the step title of the widget editor dialog is "3. Add your content"
    # And the "horizontal-axis" dropdown field in the widget editor dialog is enabled
    # And the "group-by" dropdown field in the widget editor dialog is hidden
    # When the user writes "Ending Date" in the "horizontal-axis" dropdown field in the widget editor dialog
    # And the user presses Enter
    # Then the "group-by" dropdown field in the widget editor dialog is enabled
    # When the user writes "Month" in the "group-by" dropdown field in the widget editor dialog
    # And the user presses Enter
    # And the user clicks the "Add value" table button in the widget editor dialog
    # Then the "content-property" dropdown field of row "1" in the widget editor dialog is enabled
    # When the user writes "Net Price" in the "content-property" dropdown field of row "1" in the widget editor dialog
    # And the user presses Enter
    # Then the "content-label" text field of row "1" in the widget editor dialog is enabled
    # And the "content-formatting" text field of row "1" in the widget editor dialog is enabled
    # And the value of the "content-label" text field of row "1" in the widget editor dialog is "Net price"
    # When the user writes "2" in the "content-formatting" text field of row "1" in the widget editor dialog
    # And the user presses Enter
    # Then the "grouping-method" dropdown field of row "1" in the widget editor dialog is enabled
    # When the user writes "Distinct count" in the "grouping-method" dropdown field of row "1" in the widget editor dialog
    # And the user presses Enter
    # Then the "next" button in the widget editor dialog is enabled
    # When the user clicks the "add" action button of row "1" in the widget editor dialog
    # Then the "content-property" dropdown field of row "2" in the widget editor dialog is enabled
    # When the user writes "List Price" in the "content-property" dropdown field of row "2" in the widget editor dialog
    # Then the value of the "content-label" text field of row "2" in the widget editor dialog is "List price"
    # When the user writes "1" in the "content-formatting" text field of row "2" in the widget editor dialog
    # And the user presses Enter
    # And the user writes "Distinct count" in the "grouping-method" dropdown field of row "2" in the widget editor dialog
    # And the user presses Enter
    # And the user clicks the "add" action button of row "2" in the widget editor dialog
    # Then the "content-property" dropdown field of row "3" in the widget editor dialog is enabled
    # When the user writes "Amount" in the "content-property" dropdown field of row "3" in the widget editor dialog
    # Then the value of the "content-label" text field of row "3" in the widget editor dialog is "Amount"
    # When the user writes "1" in the "content-formatting" text field of row "3" in the widget editor dialog
    # And the user presses Enter
    # And the user writes "Distinct Count" in the "grouping-method" dropdown field of row "3" in the widget editor dialog
    # And the user presses Enter
    # And the user clicks the "add" action button of row "3" in the widget editor dialog
    # Then the "content-property" dropdown field of row "4" in the widget editor dialog is enabled
    # When the user writes "Net Price" in the "content-property" dropdown field of row "4" in the widget editor dialog
    # And the user presses Enter
    # Then the value of the "content-label" text field of row "4" in the widget editor dialog is "Net price"
    # And the user writes "Maximum" in the "grouping-method" dropdown field of row "4" in the widget editor dialog
    # And the user presses Enter
    # And the user clicks the "add" action button of row "4" in the widget editor dialog
    # Then the "content-property" dropdown field of row "5" in the widget editor dialog is enabled
    # When the user writes "Discount" in the "content-property" dropdown field of row "5" in the widget editor dialog
    # And the user presses Enter
    # Then the value of the "content-label" text field of row "5" in the widget editor dialog is "Discount"
    # When the user writes "Distinct count" in the "grouping-method" dropdown field of row "5" in the widget editor dialog
    # And the user presses Enter
    # Then the "add" action button of row "5" in the widget editor dialog is hidden

    # #widget filter step
    # When the user clicks the "next" button in the widget editor dialog
    # Then the value of the step title of the widget editor dialog is "4. Add your filters"
    # When the user clicks the "Add filter" table button in the widget editor dialog
    # Then the "filter-property" dropdown field of row "1" in the widget editor dialog is enabled
    # And the "filter-type" text field of row "1" in the widget editor dialog is disabled
    # And the "filter-value" text field of row "1" in the widget editor dialog is disabled
    # When the user writes "Ending Date" in the "filter-property" dropdown field of row "1" in the widget editor dialog
    # And the user presses Enter
    # Then the "filter-type" text field of row "1" in the widget editor dialog is enabled
    # When the user writes "Less than or equal to" in the "filter-type" text field of row "1" in the widget editor dialog
    # And the user presses Enter
    # Then the "filter-value" text field of row "1" in the widget editor dialog is enabled
    # When the user writes "04/27/2023" in the "filter-value" dropdown field of row "1" in the widget editor dialog
    # And the user presses Enter
    # And the user clicks the "add" action button of row "1" in the widget editor dialog
    # And the user writes "Amount" in the "filter-property" dropdown field of row "2" in the widget editor dialog
    # And the user presses Enter
    # And the user writes "Less than" in the "filter-type" text field of row "2" in the widget editor dialog
    # And the user presses Enter
    # And the user writes "100" in the "filter-value" text field of row "2" in the widget editor dialog
    # And the user presses Enter
    # And the user clicks the "add" action button of row "2" in the widget editor dialog
    # And the user writes "Category" in the "filter-property" dropdown field of row "3" in the widget editor dialog
    # And the user presses Enter
    # # Then the value of the "filter-type" text field of row "3" in the widget editor dialog is "Equals"
    # And the user writes "Does not equal" in the "filter-type" text field of row "3" in the widget editor dialog
    # And the user presses Enter
    # And the user writes "Awful" in the "filter-value" text field of row "3" in the widget editor dialog
    # And the user presses Enter
    # And the user writes "Ok" in the "filter-value" text field of row "3" in the widget editor dialog
    # And the user presses Enter
    # And the user presses Tab

    # #widget sorting step
    # And the user clicks the "next" button in the widget editor dialog
    # Then the value of the step title of the widget editor dialog is "5. Define sorting"
    # When the user clicks the "Add a sort condition" table button in the widget editor dialog
    # Then the "sorting-property" dropdown field of row "1" in the widget editor dialog is enabled
    # And the "sorting-order" dropdown field of row "1" in the widget editor dialog is enabled
    # When the user writes "Ending Date" in the "sorting-property" dropdown field of row "1" in the widget editor dialog
    # And the user presses Enter
    # And the user writes "Ascending" in the "sorting-order" dropdown field of row "1" in the widget editor dialog
    # And the user presses Enter

    # #widget layout step
    # And the user clicks the "next" button in the widget editor dialog
    # Then the value of the step title of the widget editor dialog is "6. Create your layout"
    # And the "layout-vertical-axis-label" text field in the widget editor dialog is enabled
    # And the "layout-horizontal-axis-label" text field in the widget editor dialog is enabled
    # When the user writes "time" in the "layout-horizontal-axis-label" text field in the widget editor dialog
    # And the user presses Enter
    # And the user writes "quantities and price" in the "layout-vertical-axis-label" text field in the widget editor dialog
    # And the user waits 2 seconds
    # And element with test id "db-widget-container-barChartWidget" looks as before

    Scenario: As a user I want dependent fields in the sidebar to update when source field is changed
        XT-50323
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TableWithSidebar/eyJfaWQiOiIyIn0="
        Then the "Field - Table - With sidebar" titled page is displayed
        When the user selects the "field" bound table field on the main page
        And the user selects the row 1 of the table field
        And the user clicks the "Edit on sidebar" dropdown action of the selected row of the table field
        When the user selects the "Provider" labelled reference field on the sidebar
        Then the value of the reference field is "Amazon"
        And element with test id "e-field-bind-amount" looks as before
        When the user selects the "Provider" labelled reference field on the sidebar
        And the user writes "Decathlon" in the reference field
        And the user selects "Decathlon" in the reference field
        Then the value of the reference field is "Decathlon"
        And element with test id "e-field-bind-amount" looks as before

    Scenario: As a user I want to have different header section bottom padding depending on having a tile container or not
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/HeaderSection/eyJfaWQiOiIyIn0="
        Then the "Provider Amazon" titled page is displayed
        When selects the "Config section" labelled navigation anchor on the main page
        When the user selects the "Is tile container hidden?" labelled checkbox field on the main page
        When the user ticks the checkbox field
        And the user waits 2 seconds
        Then element with test id "e-header-section" looks as before

    Scenario: As a user I want to see the card correctly with the text truncated in tablet mode
        Given the user opens the application on a tablet using the following link: "@sage/xtrem-show-case/TableCardViewWithSingleDropdownAction/eyJfaWQiOiI1In0="
        Then the "Field - Table - Card view with single dropdown action" titled page is displayed
        Then element with css selector ".e-page-body-container .e-card" looks as before

    Scenario: As a user I want to see the card correctly with the text truncated in mobile mode
        Given the user opens the application on a mobile using the following link: "@sage/xtrem-show-case/TableCardViewWithSingleDropdownAction/eyJfaWQiOiI1In0="
        Then the "Field - Table - Card view with single dropdown action" titled page is displayed
        Then element with css selector ".e-page-body-container .e-card" looks as before

    # Scenario: As a user I want to add a watermark over my Report Template so that I have a way to make sure my documents are safe
    #     Given the user opens the application on a desktop using the following link: "@sage/xtrem-reporting/ReportTemplate"
    #     Then the "Report templates" titled page is displayed
    #     When the user clicks the "Create" labelled business action button on the main page
    #     Then the "Report template" titled page is displayed
    #     When the user selects the "Name *" labelled text field on the main page
    #     And the user writes "Report Template with Watermark" in the text field
    #     And the user selects the "Report *" labelled reference field on the main page
    #     And the user clicks the lookup button of the reference field
    #     And the user selects the "report" bound table field on a modal
    #     And the user selects the row with text "activeTemplateReport" in the "Name" labelled column header of the table field
    #     And the user clicks the "Name" labelled nested field of the selected row in the table field
    #     And selects the "Template" labelled navigation anchor on the main page
    #     And the user selects the "template" bound form designer field on the main page
    #     And the user clicks in the body of the form designer
    #     And the user clicks the "Show/Hide fields panel" button in the form designer toolbar
    #     Then the "Fields" panel in the form designer is displayed
    #     When the user selects the node-step-tree in the form designer on the main page
    #     And the user selects the tree-view element of level "1" with text "Company" in the node-step-tree of the form designer
    #     And the user clicks the tree-view element in the node-step-tree of the form designer
    #     And the title of the form designer editor dialog is "Insert an object type"
    #     And the title step of the form designer editor dialog is "1. Insert mode"
    #     And the card with "Table" value in the selection card of the form designer editor dialog is unselected
    #     When the user selects the card with "Table" value in the selection card of the form designer editor dialog
    #     Then the card with "Table" value in the selection card of the form designer editor dialog is selected
    #     When the user clicks the "Next" button of the form designer editor dialog
    #     Then the title of the form designer editor dialog is "Insert an object type"
    #     And the title step of the form designer editor dialog is "2. Column selection"
    #     And the user selects the node-step-tree in the form designer on a modal
    #     When the user selects the tree-view element of level "1" with text "Active" in the node-step-tree of the form designer
    #     And the user ticks the tree-view element in the node-step-tree of the form designer
    #     And the user clicks the "Next" button of the form designer editor dialog
    #     Then the title step of the form designer editor dialog is "3. Define content"
    #     When the user clicks the "Next" button of the form designer editor dialog
    #     Then the title step of the form designer editor dialog is "4. Define filters"
    #     When the user clicks the "Confirm" button of the form designer editor dialog
    #     And the user clicks the "Show/Hide fields panel" button in the form designer toolbar
    #     Then the "Fields" panel in the form designer is hidden
    #     When the user clicks the "Preview" labelled business action button on the main page
    #     And the user waits 10 seconds
    #     Then element with css selector ".e-section.e-single-section.e-section-context-dialog" looks as before
    #     When the user clicks the Close button of the dialog on the main page
    #     And the user selects the "Settings" labelled tab in the detail panel
    #     And the user selects the "Watermark" labelled text field on the detail panel
    #     And the user writes "test" in the text field
    #     And the user presses Enter
    #     When the user clicks the "Preview" labelled business action button on the main page
    #     And the user waits 10 seconds
    #     Then element with css selector ".e-section.e-single-section.e-section-context-dialog" looks as before

    @ClearDashboards
    Scenario: As a user, I want to see long content in table widget
        Given the user opens the application on a HD desktop
        And the dashboard page is displayed
        Then the "Create a dashboard to get started." subtitled empty dashboard is displayed

        # step 1: create a dashboard with a table widget that uses Invoices which has long content
        When the user clicks the create button on the dashboard
        Then the dashboard creation dialog is displayed
        When the user selects the template 10 in the dashboard creation dialog
        And the user clicks the "next" button in the dashboard creation dialog
        Then the "Showcase dashboard" titled dashboard in the dashboard editor is displayed
        When the user clicks the "cancel" button in the dashboard editor footer
        Then the "Showcase dashboard" titled dashboard is displayed

        # step 2: check how long content is displayed in the table widget, card and table view
        When the user selects the "Invoices" titled table widget field in the dashboard
        And the user scrolls down to the card with text "SCINV000070" and card section "Title" of the table widget field
        Then element with test id "db-widget-container-invoices" looks as before
        When the user clicks the "Switch to table view" toggle button in the header of the table widget field
        And the user scrolls down to the row with text "SCINV000068" and column header "Invoice Number" of the table widget field
        And the user selects the row with text "SCINV000068" and column header "Invoice Number" of the table widget field
        And the user expands the row of the table widget field
        And the user selects the row with text "SCINV000070" and column header "Invoice Number" of the table widget field
        And the user expands the row of the table widget field
        Then element with test id "db-widget-container-invoices" looks as before

    @ClearDashboards
    Scenario: As a user I want to see a filter menu and business icons in a table widget
        Given the user opens the application on a desktop
        And the dashboard page is displayed
        Then the "Create a dashboard to get started." subtitled empty dashboard is displayed
        When the user clicks the create button on the dashboard
        Then the dashboard creation dialog is displayed
        And the dashboard creation dialog description is "Select a template to get started or build your own dashboard. You can customize any dashboard by adding or removing widgets."
        And the "Blank template" template in the dashboard creation dialog is displayed
        When the user selects the template 1 in the dashboard creation dialog
        Then the template 1 in the dashboard creation dialog is selected
        When the user clicks the "next" button in the dashboard creation dialog
        Then the "New dashboard" titled dashboard in the dashboard editor is displayed
        When the user clicks the Add button of the "Users" titled widget card in the dashboard editor navigation panel
        Then the "Users" titled widget in the dashboard editor is displayed
        When the user selects the "Users" titled table widget field in the dashboard editor
        Then the "Filter menu" filter dropdown of the table widget field is displayed
        When the user clicks the "Save" button in the dashboard editor footer
        Then the "New dashboard" titled dashboard is displayed
        When the user selects the "Users" titled table widget field in the dashboard
        And the user waits 2 seconds
        When the user selects "Inactive accounts" in the "Filter menu" filter dropdown of the table widget field
        Then element with test id "db-widget-container-users" looks as before
        When the user clicks the "Switch to table view" toggle button in the header of the table widget field
        And the user waits 2 seconds
        Then element with test id "db-widget-container-users" looks as before

    @ClearDashboards
    Scenario: As a user, I want to see table widget row actions and icons
        Given the user opens the application on a HD desktop
        And the dashboard page is displayed
        Then the "Create a dashboard to get started." subtitled empty dashboard is displayed

        # step 1: create a dashboard with a table widget that has row actions
        When the user clicks the create button on the dashboard
        Then the dashboard creation dialog is displayed
        When the user selects the template 10 in the dashboard creation dialog
        And the user clicks the "next" button in the dashboard creation dialog
        Then the "Showcase dashboard" titled dashboard in the dashboard editor is displayed
        When the user clicks the "cancel" button in the dashboard editor footer
        Then the "Showcase dashboard" titled dashboard is displayed

        # step 2: check how row actions are displayed in the table widget, card and table view
        When the user selects the "Users with total count" titled table widget field in the dashboard
        And the user selects the card 1 of the table widget field
        And the user clicks row actions button of the selected card of the table widget field
        Then element with test id "db-widget-container-usersWithTotalCount" looks as before
        When the user clicks the "Switch to table view" toggle button in the header of the table widget field
        And the user selects the row with text "Support Readonly" and column header "Last name" of the table widget field
        And the user clicks row actions button of the selected row of the table widget field
        Then element with test id "db-widget-container-usersWithTotalCount" looks as before

        When the user selects the "Invoices" titled table widget field in the dashboard
        Then element with test id "db-widget-container-invoices" looks as before
        When the user clicks the "Switch to table view" toggle button in the header of the table widget field
        Then element with test id "db-widget-container-invoices" looks as before

    @ClearDashboardsBefore
    @ClearDashboards
    Scenario: As a user I want to see that the contact cards widget is displayed correctly
        XT-73229, XT-86840, XT-86833, XT-88035, XT-86923
        # step 1: create a dashboard with contact cards widget
        Given the user opens the application on a HD desktop
        When the user clicks the create button on the dashboard
        And the user selects the template with title "Blank template" in the dashboard creation dialog
        And the user clicks the "next" button in the dashboard creation dialog
        Then the "New dashboard" titled dashboard in the dashboard editor is displayed
        When the user clicks the Add button of the "Contact cards" titled widget card in the dashboard editor navigation panel
        And the user selects the "Contact cards" titled contact-card widget field in the dashboard editor
        And the user increases the widget field by 50,280 pixels
        And the user clicks the "Save" button in the dashboard editor footer
        Then the "New dashboard" titled dashboard is displayed

        # step 2: check how the contact cards widget is displayed based on the selected contact and address
        When the user selects the "Contact cards" titled contact-card widget field in the dashboard
        Then the value of the "Select contact" filter dropdown of the contact-card widget field is "John Doe"
        And element with test id "db-widget-container-contactCards" looks as before
        When the user clicks the "Switch to site view" toggle button in the header of the contact-card widget field
        Then the value of the "Select address" filter dropdown of the contact-card widget field is "Av Diagonal 200BarcelonaSpain"
        And element with test id "db-widget-container-contactCards" looks as before
        When the user selects "Carrer Jesús Serra Santamans 2 Sant Cugat del Vallès Spain" in the "Select address" filter dropdown of the contact-card widget field
        Then the value of the "Select address" filter dropdown of the contact-card widget field is "Carrer Jesús Serra Santamans 2Sant Cugat del VallèsSpain"
        When the user selects "Jane Doe" in the "Select contact" filter dropdown of the contact-card widget field
        Then the value of the "Select contact" filter dropdown of the contact-card widget field is "Jane Doe"
        And element with test id "db-widget-container-contactCards" looks as before
        When the user clicks the "Switch to contact view" toggle button in the header of the contact-card widget field
        And the user selects "John Smith" in the "Select contact" filter dropdown of the contact-card widget field
        Then the value of the "Select contact" filter dropdown of the contact-card widget field is "John Smith"
        And element with test id "db-widget-container-contactCards" looks as before
        And the user clears the value of the "Select contact" filter dropdown of the contact-card widget field
        Then the value of the "Select contact" filter dropdown of the contact-card widget field is ""
        And element with test id "db-widget-container-contactCards" looks as before

    Scenario Outline: As a developer I want the preview component to render <DocumentType> document to render correctly
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Preview"
        Then the "Preview" titled page is displayed
        And the user selects the header section toggle button in the header
        Then element with test id "<TestId>" looks as before

        Examples:
            | DocumentType     | TestId                      |
            | empty            | e-field-bind-empty          |
            | JSON             | e-field-bind-json           |
            | XML              | e-field-bind-xml            |
            | CSV              | e-field-bind-csv            |
            | text             | e-field-bind-txt            |
            | JPG              | e-field-bind-jpg            |
            | PNG              | e-field-bind-png            |
            | SVG              | e-field-bind-svg            |
            | multi page TIFF  | e-field-bind-multiPageTiff  |
            | single page TIFF | e-field-bind-singlePageTiff |
            | multi page PDF   | e-field-bind-multiPagePdf   |
            | single page PDF  | e-field-bind-singlePagePdf  |
            | multi page DOCX  | e-field-bind-multiPageDocx  |
            | single page DOCX | e-field-bind-singlePageDocx |

    @ClearDashboards
    Scenario: As a user I want to see data represented in a donut chart widget
        Given the user opens the application on a HD desktop
        And the dashboard page is displayed
        Then the "Create a dashboard to get started." subtitled empty dashboard is displayed

        # step 1: create a dashboard with a chart widget
        When the user clicks the create button on the dashboard
        Then the dashboard creation dialog is displayed
        When the user selects the template 10 in the dashboard creation dialog
        And the user clicks the "next" button in the dashboard creation dialog
        Then the "Showcase dashboard" titled dashboard in the dashboard editor is displayed
        When the user clicks the "cancel" button in the dashboard editor footer
        Then the "Showcase dashboard" titled dashboard is displayed

        # step 2: check how the donut chart widget looks
        When the user selects the "Pie Chart" titled pie-chart widget field in the dashboard
        Then element with test id "db-widget-container-pieChart" looks as before
        When the user clicks the "Switch to card view" toggle button in the header of the pie-chart widget field
        And the user waits 2 seconds
        Then element with test id "db-widget-container-pieChart" looks as before

    @ClearDashboards
    Scenario: As a user I want to see data represented in an indicator tile group widget
        XT-72631
        Given the user opens the application on a HD desktop
        And the dashboard page is displayed
        Then the "Create a dashboard to get started." subtitled empty dashboard is displayed
        When the user clicks the create button on the dashboard
        Then the dashboard creation dialog is displayed
        When the user selects the template 10 in the dashboard creation dialog
        And the user clicks the "next" button in the dashboard creation dialog
        Then the "Showcase dashboard" titled dashboard in the dashboard editor is displayed
        When the user clicks the "cancel" button in the dashboard editor footer
        Then the "Showcase dashboard" titled dashboard is displayed
        And element with class "db-widget-type-tile-group-indicator" looks as before

    Scenario: As a user I want to check conditional block value types for document without parameters and conditions limit
        XT-71894
        # open the conditional block of an existing report template which does not have parameters
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-reporting/ReportTemplate/eyJfaWQiOiIxNSJ9"
        Then the "Report template conditionalBlockNoVariableReportTemplate" titled page is displayed
        When selects the "Template" labelled navigation anchor on the main page
        And the user selects the "template" bound form designer field on the main page
        And the user selects the "1st" occurrence of "ShowCaseProvider" table in the form designer body
        And the user clicks the "Conditional block" button in the form designer toolbar
        Then the title of the form designer editor dialog is "Conditional block"

        # check that it is not possible to add more than 10 rows
        When the user clicks the "Add condition" table button in the form designer widget editor
        And the user clicks the "add" action button of row "1" in the form designer widget editor
        And the user clicks the "add" action button of row "2" in the form designer widget editor
        And the user clicks the "add" action button of row "3" in the form designer widget editor
        And the user clicks the "add" action button of row "4" in the form designer widget editor
        And the user clicks the "add" action button of row "5" in the form designer widget editor
        And the user clicks the "add" action button of row "6" in the form designer widget editor
        And the user clicks the "add" action button of row "7" in the form designer widget editor
        And the user clicks the "add" action button of row "8" in the form designer widget editor
        And the user clicks the "add" action button of row "9" in the form designer widget editor
        Then the "add" action button of row "10" the form designer widget editor is hidden

        # check that Value type 1 is "Property" static text and Value type 2 is dropdown with options "Property" and "Constant"
        # and that row 10 does not display the add button
        When the user opens the "Value 1" dropdown field of row "10" in the form designer editor
        When the user selects the "Date field" option in the "Value 1" dropdown field of row "10" in the form designer editor
        And the user opens the "Value Type 2" dropdown field of row "10" in the form designer editor
        Then element with css selector "[role='dialog']" looks as before

    Scenario: As a user I want to check report template Edit conditions button visibility
        XT-71894
        # step 1: open an existing report template that has a conditional block
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-reporting/ReportTemplate/eyJfaWQiOiIxNCJ9"
        Then the "Report template conditionalBlockTemplate" titled page is displayed

        # step 2: check Edit conditions button is not shown when a conditional block is not selected
        When selects the "Template" labelled navigation anchor on the main page
        And the user selects the "template" bound form designer field on the main page
        And the user selects the "1st" occurrence of "ShowCaseProvider" table in the form designer body
        And the user clicks the "Show/Hide formatting panel" button in the form designer toolbar
        And the user selects the cell with the "Date field" column header of the query table body in the table of the form designer
        Then the "Edit conditions" button in the formatting panel is hidden
        And element with class "document-editor-right-panel-body" looks as before

        # step 3: check Edit conditions button is shown when a conditional block is selected
        When the user selects the "1st" occurrence of "ShowCaseProvider" table in the form designer body
        And the user selects the cell with the "Name" column header of the query table body in the table of the form designer
        And the user clicks in the selected cell of the table in the form designer
        Then the "Edit conditions" button in the formatting panel is displayed
        And element with class "document-editor-right-panel-body" looks as before

        # step 4: check condition editor dialog can be opened and conditions are shown
        When the user clicks the "Edit conditions" button in the formatting panel of the form designer
        Then the title of the form designer editor dialog is "Conditional block"
        And element with css selector "[role='dialog']" looks as before

    Scenario: As a user I want to check 360 view toggle ON, OFF, disabled state
        XT-85643
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/StandardShowCaseProvider/eyJfaWQiOiIxIn0="
        Then the 360 view switch in the header is OFF
        And element with test id "e-page-360-switch" looks as before
        When the user clicks the 360 view switch in the header
        Then the 360 view switch in the header is ON
        And element with test id "e-page-360-switch" looks as before
        When the user clicks the 360 view switch in the header
        Then the 360 view switch in the header is OFF
        When the user selects the "Address" labelled reference field on the main page
        And the user clears the reference field
        Then the 360 view switch in the header is disabled
        And element with test id "e-page-360-switch" looks as before

    Scenario Outline: As a user I want to configure report template margins for locale <Language>
        XT-66938
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-reporting/ReportTemplate/eyJfaWQiOiIxNiJ9"
        Then the "Report template filledPageTemplate" titled page is displayed

        # switch locale and make sure margin values are proper
        When the user switches language to <Language>
        And the user refreshes the screen
        And the user selects the "defaultTopMargin" bound numeric field on the main page
        And the user writes <Value> in the numeric field
        And the user presses Tab
        And the user selects the "defaultBottomMargin" bound numeric field on the main page
        And the user writes <Value> in the numeric field
        And the user presses Tab
        And the user selects the "defaultLeftMargin" bound numeric field on the main page
        And the user writes <Value> in the numeric field
        And the user presses Tab
        And the user selects the "defaultRightMargin" bound numeric field on the main page
        And the user writes <Value> in the numeric field
        And the user presses Tab
        And the user clicks the "save" bound business action button on the main page
        And a success toast containing text <Toast> is displayed
        And the user waits 3 seconds
        Then element with test id "e-field-bind-pageFormatBlock" looks as before

        # check how preview looks with set margins
        And the user clicks the "extendPreview" bound business action button on the main page
        Then element with css selector "[data-testid*='pdfDialogPreview'] [data-page-number='1']" looks as before

        Examples:
            | Language     | Value | Toast                            |
            | "English US" | "2.1" | "Record updated"                 |
            | "English GB" | "2.2" | "Record updated"                 |
            | "Spanish"    | "2,3" | "El registro se ha actualizado." |
            | "French"     | "2,4" | "Enregistrement mis à jour"      |
            | "German"     | "2,5" | "Datensatz aktualisiert"         |
            | "Polish"     | "2.6" | "Zaktualizowano rekord"          |
            | "Portuguese" | "2,7" | "Registro atualizado"            |
            | "Chinese"    | "2.8" | "记录已更新"                     |

    Scenario: As a user I want report template header and footer to have page number and total number of pages
        XT-66590
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-reporting/ReportTemplate/eyJfaWQiOiIxNiJ9"
        Then the "Report template filledPageTemplate" titled page is displayed

        # check page number and total number of pages cannot be added to body
        When selects the "Template" labelled navigation anchor on the main page
        And the user selects the "template" bound form designer field on the main page
        And the user clicks in the body of the form designer
        And the user clicks the "Show/hide document properties panel" button in the form designer toolbar
        Then the "Fields" panel in the form designer is displayed
        And element with test id "e-data-step-tree-container" looks as before

        # check page number and total number of pages can be added to header and footer
        When the user clicks in the header of the form designer
        Then element with test id "e-data-step-tree-container" looks as before
        When the user selects the node-step-tree in the form designer on the main page
        And the user selects the tree-view element of level "1" with text "Total number of pages" in the node-step-tree of the form designer
        And the user clicks the tree-view element in the node-step-tree of the form designer
        When the user clicks in the footer of the form designer
        Then element with test id "e-data-step-tree-container" looks as before
        When the user selects the node-step-tree in the form designer on the main page
        And the user selects the tree-view element of level "1" with text "Page number" in the node-step-tree of the form designer
        And the user clicks the tree-view element in the node-step-tree of the form designer

        # check how preview looks with added page number to footer and total number of pages to header
        And the user waits 3 seconds
        And the user clicks the "extendPreview" bound business action button on the main page
        Then element with css selector "[data-testid*='pdfDialogPreview'] [data-page-number='1']" looks as before
        And element with css selector "[data-testid*='pdfDialogPreview'] [data-page-number='2']" looks as before
        And element with css selector "[data-testid*='pdfDialogPreview'] [data-page-number='6']" looks as before

    Scenario: As a user I want see report template with different page orientation
        XT-66662
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-reporting/ReportTemplate/eyJfaWQiOiIxNiJ9"
        Then the "Report template filledPageTemplate" titled page is displayed

        # check landscape orientation
        When the user selects the "defaultPageOrientation" bound dropdown-list field on the main page
        And the user clicks in the dropdown-list field
        Then element with css selector "[data-testid*='defaultPageOrientation'] [data-testid='e-ui-select-dropdown']" looks as before
        When the user selects "Landscape" in the dropdown-list field
        And the user selects the "Settings" labelled tab in the detail panel
        Then element with css selector "[data-testid*='detailPanelSettingsBlock'] [data-testid*='pageOrientation']" looks as before
        When the user clicks the "extendPreview" bound business action button on the main page
        Then element with css selector "[data-testid*='pdfDialogPreview'] [data-page-number='1']" looks as before

        # check portrait orientation
        When the user refreshes the screen
        And the user selects the "defaultPageOrientation" bound dropdown-list field on the main page
        And the user clicks in the dropdown-list field
        When the user selects "Portrait" in the dropdown-list field
        And the user selects the "Settings" labelled tab in the detail panel
        Then element with css selector "[data-testid*='detailPanelSettingsBlock'] [data-testid*='pageOrientation']" looks as before
        # wait for preview to be regenerated
        And the user waits 3 seconds
        When the user clicks the "extendPreview" bound business action button on the main page
        Then element with css selector "[data-testid*='pdfDialogPreview'] [data-page-number='1']" looks as before

    @ClearDashboardsBefore
    Scenario Outline: As a user I want the see responsiveness in dashboard editor columns when using <Viewport> device
        XT-86166
        # open the dashboard editor on a specific resolution
        Given the user opens the application on a <Viewport>
        Then the dashboard page is displayed
        When the user clicks the create button on the dashboard
        Then the dashboard creation dialog is displayed
        When the user selects the template with title "Blank template" in the dashboard creation dialog
        And the user clicks the "next" button in the dashboard creation dialog
        Then the "New dashboard" titled dashboard in the dashboard editor is displayed
        When the user clicks the Add button of the "Invoices" titled widget card in the dashboard editor navigation panel
        Then the "Invoices" titled widget in the dashboard editor is displayed
        # in case of small device minimize the sidebar
        When <SmallDeviceMinimizeSidebarLargeDeviceDoNothing>

        # check how the dashboard looks
        Then element with <SelectorType> "<Selector>" looks as before
        Examples:
            | Viewport          | SmallDeviceMinimizeSidebarLargeDeviceDoNothing                                                  | SelectorType | Selector                |
            | ultrawide desktop | the user waits 0 second                                                                         | test id      | e-dashboard-editor-body |
            | HD desktop        | the user waits 0 second                                                                         | test id      | e-dashboard-editor-body |
            | desktop           | the user waits 0 second                                                                         | test id      | e-dashboard-editor-body |
            | tablet            | the user clicks the "toggleWidgetList" labelled button in the dashboard editor navigation panel | test id      | e-dashboard-editor-body |
            | mobile            | the user clicks the "toggleWidgetList" labelled button in the dashboard editor navigation panel | test id      | e-dashboard-editor-body |

    Scenario: As a user I want to use text stream variables in report template
        XT-62260

        # Prepare a report template
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-reporting/ReportTemplate"
        When the user clicks the "Create" labelled business action button on the navigation panel
        Then the "Report template" titled page is displayed
        When the user selects the "Name" labelled text field on the main page
        And the user writes "textStreamTemplate" in the text field
        And the user presses Enter
        And the user selects the "Report" labelled reference field on the main page
        And the user writes "variableTestReport" in the reference field
        And the user selects "variableTestReport" in the reference field
        And the user presses Enter
        And selects the "Template" labelled navigation anchor on the main page
        And the user selects the "template" bound form designer field on the main page
        And the user clicks in the body of the form designer

        # Check text stream is available in document properties panel
        And the user clicks the "Show/hide document properties panel" button in the form designer toolbar
        Then the "Fields" panel in the form designer is displayed
        And element with test id "e-data-step-tree-container" looks as before

        # Insert table with "Document" text stream field
        When the user clicks the "Show/hide fields panel" button in the form designer toolbar
        Then the "Fields" panel in the form designer is displayed
        When the user searches "Show case provider" in the node-step-tree of the form designer on the main page
        And the user selects the node-step-tree in the form designer on the main page
        And the user selects the tree-view element of level "1" with text "Show case provider" in the node-step-tree of the form designer
        And the user clicks the tree-view element in the node-step-tree of the form designer
        Then the title of the form designer editor dialog is "Insert an object type"
        And the title step of the form designer editor dialog is "1. Insert mode"
        When the user selects the card with "Table" value in the selection card of the form designer editor dialog
        And the user clicks the "Next" button of the form designer editor dialog
        Then the title step of the form designer editor dialog is "2. Column selection"
        When the user selects the node-step-tree in the form designer on a modal
        And the user selects the tree-view element of level "1" with text "Document" in the node-step-tree of the form designer
        And the user ticks the tree-view element in the node-step-tree of the form designer
        And the user clicks the "Next" button of the form designer editor dialog

        # Check group not shown for "Document" text stream field, error message and next button disabled
        Then the title step of the form designer editor dialog is "3. Define content"
        And element with css selector "[role='dialog']" looks as before

        # Add additional columns to the table
        And the user clicks the "Previous" button of the form designer editor dialog
        And the user selects the tree-view element of level "1" with text "Integer field" in the node-step-tree of the form designer
        And the user ticks the tree-view element in the node-step-tree of the form designer
        And the user selects the tree-view element of level "1" with text "Text field" in the node-step-tree of the form designer
        And the user ticks the tree-view element in the node-step-tree of the form designer
        And the user clicks the "Next" button of the form designer editor dialog

        # Check "Document" cannot be first in group error message, operation and sorting now shown, next button disabled
        Then the title step of the form designer editor dialog is "3. Define content"
        And element with css selector "[role='dialog']" looks as before

        # Drag "Document" text stream down so it's not the first in group
        When the user drags row "1" down "2" rows in the form designer editor

        # Try to change "Document" text stream to group 2 thus making it first in that group and check error message is shown
        When the user writes "Group 2" in the "Group" dropdown-list field of row "3" in the form designer widget editor
        And the user presses Tab
        Then a error toast with text "Non-sortable properties cannot be the first items in the group. Select other properties." is displayed

        # Check change to group 2 for "Document" text stream is not applied, everything is valid and next button is enabled
        When the user dismisses all the toasts
        And the user presses Tab
        Then element with css selector "[role='dialog']" looks as before

        # Check cannot use "Document" text stream as filter
        When the user clicks the "Next" button of the form designer editor dialog
        Then the title step of the form designer editor dialog is "4. Define filters"
        When the user clicks the "Add filter" table button in the form designer widget editor
        And the user opens the "property" dropdown field of row "1" in the form designer editor
        Then element with css selector "[role='dialog']" looks as before

        # Check report templated preview is generated with "Document" text stream property
        When the user presses Tab
        And the user clicks the "Confirm" button of the form designer editor dialog
        # wait for preview to be generated
        And the user waits 4 seconds
        And the user clicks the "extendPreview" bound business action button on the main page
        Then element with css selector "[data-testid*='pdfDialogPreview'] [data-page-number='1']" looks as before

    Scenario Outline: As a user I want <Currency> to be displayed in <Language>
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Numeric"
        When the user switches language to <Language>
        Then the "Numeric" titled page is displayed
        Given the user selects the "Scale" labelled numeric field on the main page
        And the user clicks in the numeric field
        And the user writes "1" in the numeric field
        And the user blurs the numeric field
        And the user clears the numeric field
        And the user waits 1 second
        Then the user selects the "unit" bound dropdown-list field on the main page
        When the user clicks in the dropdown-list field
        And the user selects <Currency> in the dropdown-list field
        And the user waits 1 second
        Then element with test id "e-field-bind-field" looks as before
        Examples:
            | Language     | Currency |
            | "English US" | "EUR"    |
            | "German"     | "EUR"    |
            | "English US" | "HUF"    |
            | "German"     | "HUF"    |

    Scenario Outline: <Device> - As a user I want to see five lines in my navigation panel card
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/CardField/eyJfaWQiOiIxMDYifQ=="
        Then the "Field - Card" titled page is displayed
        When the user opens the navigation panel
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the value of the "product" bound nested text field of the card 1 in the table field is "Anisette - Mcguiness"
        And the value of the "description" bound nested text field of the card 1 in the table field is "complexity"
        And the value of the "barcode" bound nested text field of the card 1 in the table field is "3546705000074574"
        And the value of the "releaseDate" bound nested date field of the card 1 in the table field is "01/02/2020"
        And the value of the "_id" bound nested text field of the card 1 in the table field is "106"
        And the value of the "listPrice" bound nested numeric field of the card 1 in the table field is "19"
        And the value of the "category" bound nested label field of the card 1 in the table field is "Good"
        And the value of the "tax" bound nested text field of the card 1 in the table field is "7.53"
        And element with test id "e-card" looks as before
        Examples:
            | Device  |
            | desktop |
            | mobile  |

    Scenario: As a user I want to see the lookup dialog in mobile with the correct size
        Given the user opens the application on a mobile using the following link: "@sage/xtrem-show-case/Reference"
        Then the "Field - Reference" titled page is displayed
        Given the user selects the "Show case product" labelled reference field on the main page
        When the user writes "synergy" in the reference field
        Then a list of options is displayed for the reference field
        Then the user waits 5 seconds
        Then element with test id "e-table-field-mobile-rows" looks as before
