Feature: 3-1 Reference adc
    # Tests the automatic data completion functionality in reference fields, verifying that fields are populated correctly when a unique match is found

    <PERSON><PERSON><PERSON>: As a user I want to get my field auto completed if there is only one match
        Given the user opens the application on a mobile using the following link: "@sage/xtrem-show-case/ReferenceForAdcPoc"
        And the user selects the "Product 1" labelled reference field on the main page
        And the user writes "3550952118857" in the reference field
        And the user waits 1 second
        Then the value of the reference field is "Bread - Ciabatta Buns"
        Then the helper text of the reference field is "moderator"

    <PERSON><PERSON><PERSON>: As a user I want to have the lookup dialog displayed if there are multiplie matches
        Given the user opens the application on a mobile using the following link: "@sage/xtrem-show-case/ReferenceForAdcPoc"
        And the user selects the "Product 1" labelled reference field on the main page
        And the user writes "3576421148211549" in the reference field
        And the user selects the "field1" bound table field on a full width modal
        And the user clicks the card 1 in the table field
        Then the value of the reference field is "<PERSON><PERSON><PERSON><PERSON>"
        Then the helper text of the reference field is "Synergized"

    Scenario: As a user I want clear several reference fields
        XT-86658
        Given the user opens the application on a mobile using the following link: "@sage/xtrem-show-case/ReferenceForAdcPoc"
        Then the "Reference ADC usecase" titled page is displayed
        And the user selects the "Product 1" labelled reference field on the main page
        And the user writes "Spinach - Baby" in the reference field
        Then the value of the reference field is "Spinach - Baby"

        And the user selects the "Product 2" labelled reference field on the main page
        And the user writes "Veal Inside - Provimi" in the reference field
        Then the value of the reference field is "Veal Inside - Provimi"

        And the user selects the "Product 3" labelled reference field on the main page
        And the user writes "Pepper - Chillies, Crushed" in the reference field
        Then the value of the reference field is "Pepper - Chillies, Crushed"

        And the user selects the "Product 1" labelled reference field on the main page
        And the user clears the reference field
        Then the value of the reference field is ""

        And the user selects the "Product 2" labelled reference field on the main page
        And the user clears the reference field
        Then the value of the reference field is ""

        And the user selects the "Product 3" labelled reference field on the main page
        And the user clears the reference field
        Then the value of the reference field is ""
