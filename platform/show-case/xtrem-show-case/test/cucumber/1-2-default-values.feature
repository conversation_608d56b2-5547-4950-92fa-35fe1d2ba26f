Feature: 1-2 Default values
    # Tests default value functionality, ensuring that server control rules correctly populate initial field values when creating new records

    Scenario: As a user I want the default values populated when I open an empty record
        The default values are fetched when an empty, non-transient, node-bound page is opened

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/PageDefaultCrudActions"
        And the user clicks the "Create" labelled business action button on the navigation panel
        And the user selects the "Quantity" labelled numeric field on the main page
        Then the value of the numeric field is "1"
        And the user selects the "Stock" labelled numeric field on the main page
        Then the value of the numeric field is "1"

    Scenario: As a user I want the default values to be calculated as I fill the form
        Bulk discount calculation is set in the server: the net price property getting a discount if the quantity is higher than 10

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/PageDefaultCrudActions"
        And the user clicks the "Create" labelled business action button on the navigation panel
        And the user selects the "Quantity" labelled numeric field on the main page
        Then the value of the numeric field is "1"
        When the user writes "9" in the numeric field
        And the user selects the "List Price" labelled numeric field on the main page
        When the user writes "50.00" in the numeric field
        And the user blurs the numeric field
        And the user selects the "Net Price" labelled numeric field on the main page
        Then the value of the numeric field is "50.00"
        And the user selects the "Quantity" labelled numeric field on the main page
        When the user writes "20" in the numeric field
        And the user blurs the numeric field
        And the user selects the "Net Price" labelled numeric field on the main page
        Then the value of the numeric field is "45.00"


    Scenario: As a user I do not want the default values to overwrite my manual edits
        The net price is expected to remain 100 as the user manually edits it, even if there is a control rule that sets it based on the list price

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/PageDefaultCrudActions"
        And the user clicks the "Create" labelled business action button on the navigation panel
        And the user selects the "Net Price" labelled numeric field on the main page
        When the user writes "100.00" in the numeric field
        And the user selects the "List Price" labelled numeric field on the main page
        When the user writes "50.00" in the numeric field
        And the user selects the "Net Price" labelled numeric field on the main page
        Then the value of the numeric field is "100.00"

    Scenario: As a functional developer I want to fetch a default value on demand
        XT-4396
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/PageDefaultCrudActions"
        And the user clicks the "Create" labelled business action button on the navigation panel
        And the user selects the "Quantity" labelled numeric field on the main page
        When the user writes "11" in the numeric field
        And the user selects the "List Price" labelled numeric field on the main page
        When the user writes "50.00" in the numeric field
        And the user selects the "Net Price" labelled numeric field on the main page
        When the user writes "123.00" in the numeric field
        Then the value of the numeric field is "123.00"
        When the user clicks the "singleFieldDefaultButton" bound button on the main page
        And the user selects the "Net Price" labelled numeric field on the main page
        Then the value of the numeric field is "45.00"


    Scenario: As a functional developer I want to fetch a default value on demand but not apply it to the screen
        XT-4396
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/PageDefaultCrudActions"
        And the user clicks the "Create" labelled business action button on the navigation panel
        And the user selects the "Quantity" labelled numeric field on the main page
        When the user writes "11" in the numeric field
        And the user selects the "List Price" labelled numeric field on the main page
        When the user writes "50.00" in the numeric field
        And the user selects the "Net Price" labelled numeric field on the main page
        Then the value of the numeric field is "45.00"
        When the user writes "123.00" in the numeric field
        Then the value of the numeric field is "123.00"
        And the user selects the "singleFieldDefaultCheckbox" bound checkbox field on the main page
        When the user ticks the checkbox field
        When the user clicks the "singleFieldDefaultButton" bound button on the main page
        And the user selects the "Net Price" labelled numeric field on the main page
        Then the value of the numeric field is "123.00"
        Then the value of the toast is "The value is 45"


    Scenario: As a functional developer I want to fetch multiple default values on demand
        XT-4396
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/PageDefaultCrudActions"
        And the user clicks the "Create" labelled business action button on the navigation panel
        And the user selects the "Quantity" labelled numeric field on the main page
        When the user writes "11" in the numeric field
        And the user selects the "Stock" labelled numeric field on the main page
        When the user writes "789" in the numeric field
        And the user selects the "List Price" labelled numeric field on the main page
        When the user writes "50.00" in the numeric field
        And the user selects the "Net Price" labelled numeric field on the main page
        Then the value of the numeric field is "45.00"
        When the user writes "123.00" in the numeric field
        Then the value of the numeric field is "123.00"
        And the user selects the "Stock" labelled numeric field on the main page
        Then the value of the numeric field is "789"
        When the user clicks the "multipleFieldDefaultButton" bound button on the main page
        And the user selects the "Net Price" labelled numeric field on the main page
        Then the value of the numeric field is "45.00"
        And the user selects the "Stock" labelled numeric field on the main page
        Then the value of the numeric field is "1"


    Scenario: As a functional developer I want to fetch multiple default values on demand but not apply them to screen
        XT-4396
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/PageDefaultCrudActions"
        And the user clicks the "Create" labelled business action button on the navigation panel
        And the user selects the "Quantity" labelled numeric field on the main page
        When the user writes "11" in the numeric field
        And the user selects the "Stock" labelled numeric field on the main page
        When the user writes "789" in the numeric field
        And the user selects the "List Price" labelled numeric field on the main page
        When the user writes "50.00" in the numeric field
        And the user selects the "Net Price" labelled numeric field on the main page
        Then the value of the numeric field is "45.00"
        When the user writes "123.00" in the numeric field
        Then the value of the numeric field is "123.00"
        And the user selects the "Stock" labelled numeric field on the main page
        Then the value of the numeric field is "789"
        And the user selects the "multipleFieldDefaultCheckbox" bound checkbox field on the main page
        When the user ticks the checkbox field
        When the user clicks the "multipleFieldDefaultButton" bound button on the main page
        And the user selects the "Net Price" labelled numeric field on the main page
        Then the value of the numeric field is "123.00"
        And the user selects the "Stock" labelled numeric field on the main page
        Then the value of the numeric field is "789"

    Scenario: As a functional developer I want to fetch multiple default values on demand on pages that edit a non-new record
        XT-4396
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/PageDefaultCrudActions/eyJfaWQiOiI0NjQifQ=="
        And the user selects the "Quantity" labelled numeric field on the main page
        When the user writes "11" in the numeric field
        And the user selects the "Stock" labelled numeric field on the main page
        When the user writes "789" in the numeric field
        And the user selects the "List Price" labelled numeric field on the main page
        When the user writes "50.00" in the numeric field
        And the user selects the "Net Price" labelled numeric field on the main page
        When the user writes "3223.00" in the numeric field
        Then the value of the numeric field is "3,223.00"
        And the user selects the "Stock" labelled numeric field on the main page
        Then the value of the numeric field is "789"
        When the user clicks the "multipleFieldDefaultButton" bound button on the main page
        And the user selects the "Net Price" labelled numeric field on the main page
        Then the value of the numeric field is "45.00"

    Scenario: As a user I want validation messages to remain on screen even if default values are fetched
        XT-6976
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseProduct"
        And the user clicks the "Create" labelled business action button on the navigation panel
        And the user selects the "Product" labelled text field on the main page
        Then the text field is valid
        When the user writes "Sample text" in the text field
        And the user blurs the text field
        And the user clears the text field
        Then the text field is invalid
        And the user selects the "Quantity" labelled numeric field on the main page
        When the user writes "12345" in the numeric field
        And the user selects the "Product" labelled text field on the main page
        Then the text field is invalid
