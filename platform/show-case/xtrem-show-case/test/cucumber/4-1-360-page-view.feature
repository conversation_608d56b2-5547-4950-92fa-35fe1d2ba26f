Feature: 4-1 360 Page View
    # Tests the 360-degree view functionality, verifying tab navigation, content display, and interaction with related data across different entity views

    Scenario: As a user I want to interact with the tabs of a table when no query parameters are defined
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/StandardShowCaseProvider/eyJfaWQiOiIxIn0="
        Then the "Provider 1" titled page is displayed
        And the "Products" labelled navigation anchor is selected
        When the user selects the "products" labelled table field on the main page
        Then the user selects the row 1 of the table field
        And the value of the "product" bound nested text field of the selected row in the table field is "Spinach - Baby"
        When selects the "Misc" labelled navigation anchor on the main page
        Then the "Date" labelled date field on the main page is displayed
        And the "Integer" labelled numeric field on the main page is displayed

    Scenario: As a user I want to interact with the tabs of a table on a page that has _SELECTED_SECTION_ID defined
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/StandardShowCaseProvider/?_id=2&_SELECTED_SECTION_ID=miscSection"
        Then the "Provider 2" titled page is displayed
        And the "Misc" labelled navigation anchor is selected
        Then the "Date" labelled date field on the main page is displayed
        And the "Integer" labelled numeric field on the main page is displayed
        When selects the "Products" labelled navigation anchor on the main page
        And the user selects the "products" labelled table field on the main page
        Then the user selects the row 1 of the table field
        And the value of the "product" bound nested text field of the selected row in the table field is "Beef - Bresaola"

    Scenario: As a user when I navigate from a 360 view ON page and I return back I want the 360 view to still be ON
        XT-87817
        # the checking of the 360 view switch state might have to be revisited once XT-90749 is implemented
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/StandardShowCaseProvider/eyJfaWQiOiIxIn0="
        Then the "Provider 1" titled page is displayed
        And the 360 view switch in the header is OFF

        # enable 360 view
        When the user clicks the 360 view switch in the header
        And the user dismisses all the toasts
        Then the 360 view switch in the header is ON
        And the "Users" titled widget in the dashboard is displayed

        # navigate away from the 360 view page
        When the user selects the "Users" titled table widget field in the dashboard
        And the user selects the card 2 of the table widget field
        And the user clicks the link of the row 2 on the left of the card of the table widget field
        Then the "User Acme Support" titled page is displayed

        # navigate back to the 360 view page and check 360 view switch is ON
        When the user navigates back using the browser back button
        Then the "Provider 1" titled page is displayed
        And the 360 view switch in the header is ON
        And the "Users" titled widget in the dashboard is displayed

    Scenario: As a user I want to check 360 view switch availability and functionality
        XT-72769
        # access a page where 360 view is available
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/StandardShowCaseProvider/eyJfaWQiOiIxIn0="
        Then the "Provider 1" titled page is displayed
        And the 360 view switch in the header is OFF

        # enable and check 360 view is displayed
        When the user clicks the 360 view switch in the header
        Then the 360 view switch in the header is ON
        And the "Users" titled widget in the dashboard is displayed

        # make page dirty and check 360 view switch is disabled
        When the user clicks the 360 view switch in the header
        Then the 360 view switch in the header is OFF
        When the user selects the "Address" labelled reference field on the main page
        And the user clears the reference field
        Then the 360 view switch in the header is disabled

        # make page not dirty again, save and check 360 view switch is available again
        When the user clicks the lookup button of the reference field
        And the user selects the "siteAddress" bound table field on a modal
        And the user filters the "Name" labelled column in the table field with value "Barcelona"
        And the user selects the row 1 of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        And the user clicks the "Save" labelled business action button on the main page
        Then the 360 view switch in the header is OFF

        # switch 360 view ON again and check the 360 view is displayed
        When the user clicks the 360 view switch in the header
        Then the 360 view switch in the header is ON
        And the "Users" titled widget in the dashboard is displayed
