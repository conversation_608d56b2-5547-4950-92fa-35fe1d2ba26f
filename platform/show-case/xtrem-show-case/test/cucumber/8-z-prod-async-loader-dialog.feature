Feature: 8 Z prod async loader dialog
    # Tests the notification center functionality in production environments, verifying notification creation, display, interactions, and dismissal behaviors

    Scenario Outline: As a user I want to be able receive notification when async mutation is successful
        XT-96565
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Dialogs"
        And the "Dialogs" titled page is displayed
        And the user selects the "Customer" labelled reference field on the main page
        When the user writes "Anderson and Sons" in the reference field
        And the user selects "Anderson and Sons" in the reference field
        And the user selects the "New name" labelled text field on the main page
        When the user writes "Anderson and Sons RENAMED" in the text field
        And the user selects the "mockedProcessingTime" bound numeric field on the main page
        And the user writes "10000" in the numeric field
        And the user blurs the numeric field
        When the user clicks in the "asyncMutationButton" bound button field on the main page
        And the user waits 8 seconds
        Then an info dialog appears on the main page
        And the text in the header of the dialog is "Your operation is still in progress"
        When <Step>
        And the user waits 2 seconds
        Given the "unread" notifications icon in the actions header is displayed
        When the user clicks the notifications icon in the actions header

        Then the notification with title "Customer renamed" in the notification center is displayed
        And the user selects the notification card with title "Customer renamed"
        Then the title of the notification card is "Customer renamed"
        Then the relative date of the notification card is "less than a minute ago"
        Then the description of the notification card is "Customer Anderson and Sons has been renamed to Anderson and Sons RENAMED"
        # Record verification and clean up
        When the user clicks the "Delete" notification card header action
        And the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/StandardShowCaseCustomer/eyJfaWQiOiIxNDEifQ=="
        Then the "Customer Anderson and Sons RENAMED" titled page is displayed
        And the user selects the "name" labelled text field on the main page
        And the user writes "Anderson and Sons" in the text field
        And the user clicks the "$standardSaveAction" bound business action button on the main page
        Then the "Customer Anderson and Sons" titled page is displayed

        Examples:
            | Step                                                            |
            | the user clicks the "Notify me" button of the Confirm dialog    |
            | the user clicks the Close button of the dialog on the main page |

    Scenario: As a user when I stop an async mutation the record is not changed and I don't receive a notification
        XT-96565
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Dialogs"
        Then the "Dialogs" titled page is displayed
        When the user selects the "Customer" labelled reference field on the main page
        And the user writes "Anderson and Sons" in the reference field
        And the user selects "Anderson and Sons" in the reference field
        And the user selects the "New name" labelled text field on the main page
        And the user writes "Anderson and Sons RENAMED" in the text field
        And the user selects the "mockedProcessingTime" bound numeric field on the main page
        And the user writes "10000" in the numeric field
        And the user blurs the numeric field
        And the user selects the "canUserStopAsyncMutation" bound checkbox field on the main page
        And the user ticks the checkbox field
        And the user clicks in the "asyncMutationButton" bound button field on the main page
        And the user waits 8 seconds
        Then an info dialog appears on the main page
        And the text in the header of the dialog is "Your operation is still in progress"
        When the user clicks the "Stop" button of the Confirm dialog
        And the user waits 4 seconds
        And the user clicks the notifications icon in the actions header
        Then the notification with title "Customer renamed" in the notification center is hidden

        # Record verification
        When the user navigates to the following link: "@sage/xtrem-show-case/StandardShowCaseCustomer/eyJfaWQiOiIxNDEifQ=="
        Then the "Customer Anderson and Sons" titled page is displayed

    Scenario: As a user I want my async mutation to finish when I navigate away from the loader and I don't receive a notification
        XT-96565
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Dialogs"
        Then the "Dialogs" titled page is displayed
        When the user selects the "Customer" labelled reference field on the main page
        And the user writes "Anderson and Sons" in the reference field
        And the user selects "Anderson and Sons" in the reference field
        And the user selects the "New name" labelled text field on the main page
        And the user writes "Anderson and Sons RENAMED" in the text field
        And the user selects the "mockedProcessingTime" bound numeric field on the main page
        And the user writes "10000" in the numeric field
        And the user blurs the numeric field
        And the user clicks in the "asyncMutationButton" bound button field on the main page
        And the user waits 4 seconds
        And the user navigates to the following link: "@sage/xtrem-show-case/StandardShowCaseCustomer"
        Then the "Customers" titled page is displayed
        When the user waits 8 seconds
        And the user clicks the notifications icon in the actions header
        Then the notification with title "Customer renamed" in the notification center is hidden

        # Record verification and clean up
        When the user navigates to the following link: "@sage/xtrem-show-case/StandardShowCaseCustomer/eyJfaWQiOiIxNDEifQ=="
        Then the "Customer Anderson and Sons RENAMED" titled page is displayed
        When the user selects the "name" labelled text field on the main page
        And the user writes "Anderson and Sons" in the text field
        And the user clicks the "$standardSaveAction" bound business action button on the main page
        Then the "Customer Anderson and Sons" titled page is displayed
