Feature: 1-1 <PERSON><PERSON>
    # Tests basic button functionality, including enabling/disabling state changes, visibility toggles, and click interactions

    Scenario: Disable or enable the button field
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Button"
        Given the user selects the "Is disabled" labelled checkbox field on the main page
        When the user ticks the checkbox field
        Then the "field" bound button field on the main page is disabled
        When the user unticks the checkbox field
        Then the "field" bound button field on the main page is enabled

    Scenario: Hide or display the button field
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Button"
        Given the user selects the "Is hidden" labelled checkbox field on the main page
        When the user ticks the checkbox field
        Then the "field" bound button field on the main page is hidden
        When the user unticks the checkbox field
        Then the "field" bound button field on the main page is displayed

    Scenario: Set the helper text of the button field
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Button"
        Given the user selects the "Helper text" labelled text field on the main page
        When the user writes "Sample text" in the text field
        Then the helper text of the "field" bound button field on the main page is "Sample text"


    Scenario: Set the title of the button field
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Button"
        Given the user selects the "Title" labelled text field on the main page
        When the user writes "Sample title" in the text field
        Then the title of the "field" bound button field on the main page is "Sample title"

    Scenario: Trigger custom click event handler
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Button"
        Then the "clickTriggered" bound label field on the main page is hidden
        When the user clicks in the "field" bound button field on the main page
        Then the "clickTriggered" bound label field on the main page is displayed
