Feature: 1-1 Aggregate field
    # Tests the display and functionality of aggregated values in navigation panel tables, verifying correct calculation and navigation between aggregated data and detailed records

    Scenario: As a user I want to see aggregated values on the navigation panel table
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Aggregate"
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And  the user selects the row 1 of the table field
        Then the value of the "Provider" labelled nested text field of the selected row in the table field is "Ali Express"
        Then the value of the "Max product list price" labelled nested aggregate field of the selected row in the table field is "75"
        When the user clicks the "Provider" labelled nested field of the selected row in the table field
        Given the user selects the "_id" bound text field on the main page
        Then the value of the text field is "1"
        When the user clicks the "Close record" icon in the header on the main page
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And  the user selects the row 3 of the table field
        Then the value of the "Provider" labelled nested text field of the selected row in the table field is "Barcelona Activa"
        Then the value of the "Max product list price" labelled nested aggregate field of the selected row in the table field is "5"
        When the user clicks the "Provider" labelled nested field of the selected row in the table field
        Given the user selects the "_id" bound text field on the main page
        Then the value of the text field is "4"
