Feature: 2-2 Option menu
  # Tests the functionality of the option menu component for filtering nested grid data, verifying that users can select options and see the grid content update accordingly

  # #nested grid
  Scenario: As a user I want to filter a nested grid using the option menu
    Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/NestedGridRemapped/eyJfaWQiOiI0In0="
    Then the "Field - NestedGrid - Remapped value" titled page is displayed
    And the user selects the "field" bound nested grid field on the main page
    And the option menu of the nested grid field is displayed
    When the user clicks the option menu of the nested grid field
    And the user clicks the "Old orders" value in the option menu of the nested grid field
    And the user selects the "field" bound nested grid field on the main page
    And the user selects row with text "158" in column with header "id" in the nested grid field
    Then the value of the "_id" bound nested text field of the selected row in the nested grid field is "158"
