Feature: 2-2 Mutation operations
    # Tests the update functionality for records, particularly focusing on the CRUD operations for records with no ID fields

    Scenario: As a user I want to update a record with no ID field (X3-194849)
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/CrudUpdate/eyJfaWQiOiIxIn0="
        And the user selects the "Some decimal info" labelled text field on the main page
        When the user writes "1.23" in the text field
        And the user clicks the "Save" labelled business action button on the main page
        Then the user navigates to the following link: "@sage/xtrem-show-case/CrudUpdate/eyJfaWQiOiIxIn0="
        And the user selects the "Some decimal info" labelled text field on the main page
        Then the value of the text field is "1.23"
        When the user writes "2.34" in the text field
        And the user clicks the "Save" labelled business action button on the main page
        Then the user navigates to the following link: "@sage/xtrem-show-case/CrudUpdate/eyJfaWQiOiIxIn0="
        And the user selects the "Some decimal info" labelled text field on the main page
        Then the value of the text field is "2.34"
