Feature: 1-2 Dialogs
    # Tests various dialog types and behaviors, including message dialogs, confirmation dialogs, custom dialogs, and their display options

    Scenario: Verify the title of the dialog
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Dialogs"
        When the user clicks in the "messageDialogButton" bound button field on the main page
        Then an info dialog appears on the main page
        Then the dialog title is "Message dialog" on the main page
        And the user waits 1 seconds
        And the user clicks the Close button of the dialog on the main page

        And the user selects the "messageDialogIsFullscreen" bound checkbox field on the main page
        When the user ticks the checkbox field
        When the user clicks in the "messageDialogButton" bound button field on the main page
        Then the dialog title is "Message dialog" on a full width modal
        And the user waits 1 seconds
        And the user clicks the Close button of the dialog on a full width modal
        And the user selects the "messageDialogIsFullscreen" bound checkbox field on the main page
        When the user unticks the checkbox field

        And the user selects the "messageDialogIsRightAligned" bound checkbox field on the main page
        When the user ticks the checkbox field
        When the user clicks in the "messageDialogButton" bound button field on the main page
        Then the dialog title is "Message dialog" on the sidebar
        And the user waits 1 seconds
        And the user clicks the Close button of the dialog on the sidebar

    Scenario Outline: Message dialog is displayed
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Dialogs"
        When the user clicks in the "messageDialogButton" bound button field on the main page
        Then an info dialog appears on the main page
        And the text in the header of the dialog is "Message dialog"
        And the text in the body of the dialog is "The actual message"
        And the "Accept" button of the dialog is displayed
        And the "Accept" button of the dialog is enabled
        Examples:
            | Device  |
            | desktop |
            | tablet  |

    Scenario Outline: Message dialog is displayed fullscreen
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Dialogs"
        And the user selects the "messageDialogIsFullscreen" bound checkbox field on the main page
        When the user ticks the checkbox field
        And the user clicks in the "messageDialogButton" bound button field on the main page
        Then an info dialog appears on a full width modal
        Examples:
            | Device  |
            | desktop |
            | tablet  |

    Scenario: Info dialog is always displayed fullscreen in mobile
        Given the user opens the application on a mobile using the following link: "@sage/xtrem-show-case/Dialogs"
        When the user clicks in the "messageDialogButton" bound button field on the main page
        Then an info dialog appears on a full width modal

    Scenario Outline: Message dialog has disabled Accept button
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Dialogs"
        And the user selects the "messageDialogAcceptButtonIsDisabled" bound checkbox field on the main page
        When the user ticks the checkbox field
        And the user clicks in the "messageDialogButton" bound button field on the main page
        Then an info dialog appears on the main page
        And the "Accept" button of the dialog is displayed
        And the "Accept" button of the dialog is disabled
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: Message dialog has hidden Accept button
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Dialogs"
        And the user selects the "messageDialogAcceptButtonIsHidden" bound checkbox field on the main page
        When the user ticks the checkbox field
        And the user clicks in the "messageDialogButton" bound button field on the main page
        Then an info dialog appears on the main page
        And the "Accept" button of the dialog is hidden
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario: Message dialog is displayed right aligned
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Dialogs"
        And the user selects the "messageDialogIsRightAligned" bound checkbox field on the main page
        When the user ticks the checkbox field
        And the user clicks in the "messageDialogButton" bound button field on the main page
        Then an info dialog appears on the sidebar


    Scenario: Custom dialog is displayed
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Dialogs"
        When the user clicks in the "customDialogButton" bound button field on the main page
        Then an info dialog appears on the main page
        And the text in the header of the dialog is "Custom dialog"
        And the user selects the "customDialogContentField" bound icon field on a modal
        Then the title of the icon field is "Example field"
        And the "Accept" button of the dialog is displayed
        And the "Accept" button of the dialog is enabled

    Scenario: As a user I want a prompt to be displayed when I am closing a dirty page dialog
        XT-658

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Dialogs"
        When the user clicks in the "pageDialogButton" bound button field on the main page
        Then an info dialog appears on the main page
        And the user selects the "Helper Text" labelled text field on a modal
        And the user writes "Test" in the text field
        And the user clicks the Close button of the dialog on the main page
        Then a warn dialog appears on the main page

    Scenario: As a user I want to close a non-dirty page dialog
        XT-658

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Dialogs"
        When the user clicks in the "pageDialogButton" bound button field on the main page
        Then an info dialog appears on the main page
        And the user clicks the Close button of the dialog on the main page
        Then no dialogs are displayed

    Scenario: As a developer I want to override the dirty dialog and close the page dialog even if it is dirty
        XT-658

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Dialogs"
        And the user selects the "Skip dirty check" labelled checkbox field on the main page
        When the user ticks the checkbox field
        When the user clicks in the "pageDialogButton" bound button field on the main page
        And the user selects the "Helper Text" labelled text field on a modal
        And the user writes "Test" in the text field
        Then an info dialog appears on the main page
        And the user clicks the Close button of the dialog on the main page
        Then no dialogs are displayed

    Scenario: As a user I want if my dialog contains a crud action to be closed after click on save button
        XT-17625

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Dialogs"
        And the user selects the "Path" labelled text field on the main page
        When the user writes "@sage/xtrem-show-case/PageDefaultCrudActionsOverriden/eyJfaWQiOiIzMTMifQ==" in the text field
        When the user clicks in the "pageDialogAddParameter" bound button field on the main page
        And the user selects the "pageDialogQueryParameters" bound table field on the main page
        And the user selects the row 1 of the table field
        And the user writes "_id" in the "name" bound nested text field of the selected row in the table field
        And the user writes "313" in the "value" bound nested text field of the selected row in the table field
        When the user clicks in the "pageDialogButton" bound button field on the main page
        And the user selects the "Product" labelled text field on a modal
        And the user writes "Patata" in the text field
        Then the user presses Enter
        And the user clicks the "Save" labelled business action button on a modal
        Then no dialogs are displayed
        When the user clicks in the "pageDialogButton" bound button field on the main page
        And the user selects the "Product" labelled text field on a modal
        Then the value of the text field is "Patata"
        And the user selects the "Product" labelled text field on a modal
        And the user writes "Appetiser - Bought" in the text field
        Then the user presses Enter
        And the user clicks the "Save" labelled business action button on a modal
        Then no dialogs are displayed

    Scenario Outline: <Device> - Interaction with Accept button in Message dialog on the main page
        XT-23834
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Dialogs"
        When the user clicks in the "messageDialogButton" bound button field on the main page
        Then an info dialog appears on the main page
        Then the dialog title is "Message dialog" on the main page
        Then the text in the header of the dialog is "Message dialog" on the main page
        Then the text in the body of the dialog is "The actual message" on the main page
        Then the text in the body of the dialog contains "message" on the main page
        # And takes a screenshot
        When the user clicks the "Accept" button of the Message dialog on the main page
        Then an info dialog disappears on the main page
        Examples:
            | Device  |
            | desktop |

    Scenario Outline: <Device> - Interaction with Accept button in Message dialog on a full width modal
        XT-23834
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Dialogs"
        And the user selects the "messageDialogIsFullscreen" bound checkbox field on the main page
        When the user ticks the checkbox field
        When the user clicks in the "messageDialogButton" bound button field on the main page
        Then an info dialog appears on a full width modal
        Then the dialog title is "Message dialog" on a full width modal
        Then the text in the header of the dialog is "Message dialog" on a full width modal
        Then the text in the body of the dialog is "The actual message" on a full width modal
        Then the text in the body of the dialog contains "message" on a full width modal
        # And takes a screenshot
        When the user clicks the "Accept" button of the Message dialog on a full width modal
        Then an info dialog disappears on a full width modal
        Examples:
            | Device  |
            | desktop |

    Scenario Outline: <Device> - Interaction with Accept button in Message dialog on the sidebar
        XT-23834
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Dialogs"
        And the user selects the "messageDialogIsRightAligned" bound checkbox field on the main page
        When the user ticks the checkbox field
        When the user clicks in the "messageDialogButton" bound button field on the main page
        Then an info dialog appears on the sidebar
        Then the dialog title is "Message dialog" on the sidebar
        Then the text in the header of the dialog is "Message dialog" on the sidebar
        Then the text in the body of the dialog is "The actual message" on the sidebar
        Then the text in the body of the dialog contains "message" on the sidebar
        # And takes a screenshot
        When the user clicks the "Accept" button of the Message dialog on the sidebar
        Then an info dialog disappears on a full width modal
        Examples:
            | Device  |
            | desktop |

    Scenario Outline: <Device> - Interaction with buttons in Confirm dialog on the main page
        XT-23834
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Dialogs"
        When the user clicks in the "confirmationDialogButton" bound button field on the main page
        Then an info dialog appears on the main page
        Then the text in the header of the dialog is "Confirmation dialog" on the main page
        Then the text in the body of the dialog is "The actual confirmation message" on the main page
        Then the text in the body of the dialog contains "confirmation message" on the main page
        # And takes a screenshot
        When the user clicks the "Accept" button of the Confirm dialog on the main page
        Then an info dialog disappears on the main page
        When the user clicks in the "confirmationDialogButton" bound button field on the main page
        Then an info dialog appears on the main page
        When the user clicks the "Cancel" button of the Confirm dialog on the main page
        Then an info dialog disappears on the main page
        Examples:
            | Device  |
            | desktop |

    Scenario Outline: <Device> - Interaction with buttons in Confirm dialog on a full width modal
        XT-23834
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Dialogs"
        And the user selects the "confirmationDialogIsFullscreen" bound checkbox field on the main page
        When the user ticks the checkbox field
        When the user clicks in the "confirmationDialogButton" bound button field on the main page
        Then an info dialog appears on a full width modal
        Then the text in the header of the dialog is "Confirmation dialog" on a full width modal
        Then the text in the body of the dialog is "The actual confirmation message" on a full width modal
        Then the text in the body of the dialog contains "confirmation message" on a full width modal
        # And takes a screenshot
        When the user clicks the "Accept" button of the Confirm dialog on a full width modal
        Then an info dialog disappears on a full width modal
        When the user clicks in the "confirmationDialogButton" bound button field on the main page
        Then an info dialog appears on a full width modal
        When the user clicks the "Cancel" button of the Confirm dialog on a full width modal
        Then an info dialog disappears on a full width modal
        Examples:
            | Device  |
            | desktop |

    Scenario Outline: <Device> - Interaction with buttons in Confirm dialog on the sidebar
        XT-23834
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Dialogs"
        And the user selects the "confirmationDialogIsRightAligned" bound checkbox field on the main page
        When the user ticks the checkbox field
        When the user clicks in the "confirmationDialogButton" bound button field on the main page
        Then an info dialog appears on the sidebar
        Then the text in the header of the dialog is "Confirmation dialog" on the sidebar
        Then the text in the body of the dialog is "The actual confirmation message" on the sidebar
        Then the text in the body of the dialog contains "confirmation message" on the sidebar
        # And takes a screenshot
        When the user clicks the "Accept" button of the Confirm dialog on the sidebar
        Then an info dialog disappears on the sidebar
        When the user clicks in the "confirmationDialogButton" bound button field on the main page
        Then an info dialog appears on the sidebar
        When the user clicks the "Cancel" button of the Confirm dialog on the sidebar
        Then an info dialog disappears on the sidebar
        Examples:
            | Device  |
            | desktop |

    Scenario Outline: <Device> - Interaction with buttons in Custom dialog on the main page
        XT-23834
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Dialogs"
        When the user clicks in the "customDialogButton" bound button field on the main page
        Then an info dialog appears on the main page
        Then the text in the header of the dialog is "Custom dialog" on the main page
        # And takes a screenshot
        When the user clicks the "Accept" button of the Custom dialog on the main page
        Then an info dialog disappears on the main page
        When the user clicks in the "customDialogButton" bound button field on the main page
        Then an info dialog appears on the main page
        When the user clicks the "Cancel" button of the Custom dialog on the main page
        Then an info dialog disappears on the main page
        Examples:
            | Device  |
            | desktop |

    Scenario Outline: <Device> - Interaction with buttons in Custom dialog on a full width modal
        XT-23834
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Dialogs"
        And the user selects the "customDialogIsFullscreen" bound checkbox field on the main page
        When the user ticks the checkbox field
        When the user clicks in the "customDialogButton" bound button field on the main page
        Then an info dialog appears on a full width modal
        Then the text in the header of the dialog is "Custom dialog" on a full width modal
        # And takes a screenshot
        When the user clicks the "Accept" button of the Custom dialog on a full width modal
        Then an info dialog disappears on the main page
        When the user clicks in the "customDialogButton" bound button field on the main page
        Then an info dialog appears on a full width modal
        When the user clicks the "Cancel" button of the Custom dialog on a full width modal
        Then an info dialog disappears on a full width modal
        Examples:
            | Device  |
            | desktop |

    Scenario Outline: <Device> - Interaction with buttons in Custom dialog on the sidebar
        XT-23834
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Dialogs"
        And the user selects the "customDialogIsRightAligned" bound checkbox field on the main page
        When the user ticks the checkbox field
        When the user clicks in the "customDialogButton" bound button field on the main page
        Then an info dialog appears on the sidebar
        Then the text in the header of the dialog is "Custom dialog" on the sidebar
        # And takes a screenshot
        When the user clicks the "Accept" button of the Custom dialog on the sidebar
        Then an info dialog disappears on the sidebar
        When the user clicks in the "customDialogButton" bound button field on the main page
        Then an info dialog appears on the sidebar
        When the user clicks the "Cancel" button of the Custom dialog on the sidebar
        Then an info dialog disappears on the sidebar
        Examples:
            | Device  |
            | desktop |

    Scenario Outline: <Device> - Interaction with Error dialog
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Dialogs"
        When the user clicks in the "errorDialogField" bound button field on the main page
        Then an error dialog appears on the main page
        Then the text in the header of the error dialog is "Error" on the main page
        Then the text in the body of the error dialog is "THIS IS AN ERROR!\n\nShow technical details" on the main page
        Then the text in the body of the error dialog contains "THIS IS AN ERROR!" on the main page
        # And takes a screenshot
        And the user clicks the Close button of the dialog on the main page
        Examples:
            | Device  |
            | desktop |

    Scenario: As a user I want to use a wizard page in a page dialog
        XT-45210

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Dialogs"
        And the user selects the "Path" labelled text field on the main page
        When the user writes "@sage/xtrem-show-case/Wizard" in the text field
        When the user clicks in the "pageDialogButton" bound button field on the main page
        And the user selects the "Invalid under 10" labelled numeric field on a modal
        And the user writes "10" in the numeric field
        And the user clicks the "This is a custom next label" labelled business action button on a modal
        And the user selects the "Mandatory" labelled text field on a modal
        And the user writes "Hi there" in the text field
        And the user clicks the "Next" labelled business action button on a modal
        And the user selects the "Another text field" labelled text field on a modal
        And the user writes "abc123" in the text field
        And the user clicks the "Finish" labelled business action button on a modal
        Then no dialogs are displayed
        Then a toast with text "Dialog result :{ 'minValueNumber': 10, 'mandatoryText': 'Hi there', 'easyLabel': 'abc123' }" is displayed
        And the user dismisses all the toasts

    Scenario: As a user I want to be prevented from proceeding if the current stage of the wizard is invalid
        XT-45210

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Dialogs"
        And the user selects the "Path" labelled text field on the main page
        When the user writes "@sage/xtrem-show-case/Wizard" in the text field
        When the user clicks in the "pageDialogButton" bound button field on the main page
        And the user selects the "Invalid under 10" labelled numeric field on a modal
        And the user writes "9" in the numeric field
        And the user clicks the "This is a custom next label" labelled business action button on a modal
        Then a validation error message is displayed containing text
            """
            Invalid under 10: Minimum value is 10
            """
        And the user dismisses the validation error message
        And the user writes "10" in the numeric field
        And the user clicks the "This is a custom next label" labelled business action button on a modal
        Then the "Mandatory" labelled text field on a modal is displayed
        And the user clicks the "Next" labelled business action button on a modal
        Then a validation error message is displayed containing text
            """
            Mandatory: You need to enter a value.
            """
        And the user dismisses the validation error message
        And the user selects the "Mandatory" labelled text field on a modal
        And the user writes "Hi there" in the text field
        And the user clicks the "Next" labelled business action button on a modal
        And the user selects the "Another text field" labelled text field on a modal
        And the user writes "abc1234" in the text field
        And the user clicks the "Finish" labelled business action button on a modal
        Then a validation error message is displayed containing text
            """
            Another text field: Invalid
            """
        And the user dismisses the validation error message
        And the user writes "abc123" in the text field
        And the user dismisses all the toasts
        And the user clicks the "Finish" labelled business action button on a modal
        Then a toast with text "Dialog result :{ 'minValueNumber': 10, 'mandatoryText': 'Hi there', 'easyLabel': 'abc123' }" is displayed
        And the user dismisses all the toasts


    Scenario: Interaction with fields in on a full with wizard dialog
        XT-57723

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Dialogs"
        And the "Dialogs" titled page is displayed

        And selects the "Page dialog" labelled navigation anchor on the main page

        And the user selects the "pageDialogIsFullscreen" bound checkbox field on the main page
        And the user ticks the checkbox field

        And the user selects the "Path" labelled text field on the main page
        When the user writes "@sage/xtrem-show-case/Link" in the text field

        When the user clicks in the "pageDialogButton" bound button field on the main page

        And the dialog title is "Field - Link"

        And the user selects the "Helper text" labelled text field on a full width modal
        And the user writes "My helper text" in the text field
        And the user blurs the text field

        And the user selects the "field" bound link field on a full width modal
        And the user clicks in the link field

        And an info dialog appears on the main page
        And the text in the body of the dialog contains "Leave and discard your changes?" on the main page
        And the user clicks the "Go back" button of the Confirm dialog

        And the user selects the "Is disabled" labelled checkbox field on a full width modal
        And the user ticks the checkbox field

        And the user selects the "Parameters" labelled table field on a full width modal

        And the user clicks the Close button of the dialog on a full width modal

    Scenario: As a developer I want to set the values of the fields of the page dialog
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Dialogs"
        And the "Dialogs" titled page is displayed

        And selects the "Page dialog" labelled navigation anchor on the main page
        And the user waits 2 seconds

        And the user selects the "Path" labelled text field on the main page
        When the user writes "@sage/xtrem-show-case/ShowCaseProduct" in the text field

        When the user clicks in the "pageDialogAddValue" bound button field on the main page
        And the user selects the "pageDialogValues" bound table field on the main page

        And the user selects the row 1 of the table field
        And the user writes "product" in the "name" bound nested text field of the selected row in the table field
        And the user writes "Patata" in the "value" bound nested text field of the selected row in the table field
        When the user clicks in the "pageDialogAddValue" bound button field on the main page
        And the user selects the "pageDialogValues" bound table field on the main page
        And the user selects the row 2 of the table field
        And the user writes "description" in the "name" bound nested text field of the selected row in the table field
        And the user writes "Potato" in the "value" bound nested text field of the selected row in the table field

        When the user clicks in the "pageDialogButton" bound button field on the main page

        And the dialog title is "Product Patata"

        And the user selects the "Product" labelled text field on a modal
        Then the value of the text field is "Patata"
        And the user selects the "Description" labelled text field on a modal
        Then the value of the text field is "Potato"

    Scenario: As a developer I want to delete values of the page dialog
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Dialogs"
        And the "Dialogs" titled page is displayed

        And selects the "Page dialog" labelled navigation anchor on the main page
        And the user waits 2 seconds

        When the user clicks in the "pageDialogAddValue" bound button field on the main page
        And the user selects the "pageDialogValues" bound table field on the main page

        And the user selects the row 1 of the table field
        And the user writes "product" in the "name" bound nested text field of the selected row in the table field
        And the user writes "Patata" in the "value" bound nested text field of the selected row in the table field
        And the user clicks the "Remove" dropdown action of the selected row of the table field

    Scenario: As a user I want see the navigation panel in a full width dialog if the corresponding config property is set
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Dialogs"
        And the "Dialogs" titled page is displayed
        And selects the "Page dialog" labelled navigation anchor on the main page
        And the user waits 2 seconds
        And the user selects the "Path" labelled text field on the main page
        When the user writes "@sage/xtrem-show-case/ShowCaseProduct" in the text field
        And the user selects the "Render main list" labelled checkbox field on the main page
        When the user ticks the checkbox field
        And the user selects the "pageDialogIsFullscreen" bound checkbox field on the main page
        When the user ticks the checkbox field
        When the user clicks in the "pageDialogButton" bound button field on the main page
        And the dialog title is "Products"
        When the user selects the "$navigationPanel" bound table field on a full width modal
        And the user selects the row 1 of the table field
        Then the value of the "product" bound nested text field of the selected row in the table field is "Anisette - Mcguiness"

    Scenario: As a user I want default values to be displayed when page is loaded with initial values
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Dialogs"
        When the user clicks in the "pageDialogAddValue" bound button field on the main page
        And the user selects the "pageDialogValues" bound table field on the main page
        And the user selects the row 1 of the table field
        And the user writes "qty" in the "name" bound nested text field of the selected row in the table field
        And the user writes "10" in the "value" bound nested text field of the selected row in the table field
        When the user clicks in the "pageDialogAddValue" bound button field on the main page
        And the user selects the "pageDialogValues" bound table field on the main page
        And the user selects the row 2 of the table field
        And the user writes "listPrice" in the "name" bound nested text field of the selected row in the table field
        And the user writes "100.00" in the "value" bound nested text field of the selected row in the table field
        When the user clicks in the "pageDialogAddParameter" bound button field on the main page
        And the user selects the "pageDialogQueryParameters" bound table field on the main page
        And the user selects the row 1 of the table field
        And the user writes "_id" in the "name" bound nested text field of the selected row in the table field
        And the user writes "$new" in the "value" bound nested text field of the selected row in the table field
        When the user clicks in the "pageDialogShowCaseProductButton" bound button field on the main page
        And the user selects the "Net price" labelled numeric field on a modal
        Then the value of the numeric field is "100.00"

    Scenario: As a user I do not want the default values to overwrite the page values
        The net price is expected to remain 100.00 as the user manually edits it, even if there is a control rule that sets it based on the list price

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Dialogs"
        When the user clicks in the "pageDialogAddValue" bound button field on the main page
        And the user selects the "pageDialogValues" bound table field on the main page
        And the user selects the row 1 of the table field
        And the user writes "netPrice" in the "name" bound nested text field of the selected row in the table field
        And the user writes "100.00" in the "value" bound nested text field of the selected row in the table field
        When the user clicks in the "pageDialogShowCaseProductButton" bound button field on the main page
        And the user selects the "listPrice" bound numeric field on a modal
        And the user writes "20" in the numeric field
        And the user selects the "Net price" labelled numeric field on a modal
        Then the value of the numeric field is "100.00"

    Scenario: As a user I want the save button to be activated after making field dirty with page initial values

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Dialogs"
        When the user clicks in the "pageDialogAddValue" bound button field on the main page
        And the user selects the "pageDialogValues" bound table field on the main page
        And the user selects the row 1 of the table field
        And the user writes "firstName" in the "name" bound nested text field of the selected row in the table field
        And the user writes "test" in the "value" bound nested text field of the selected row in the table field
        When the user clicks in the "pageDialogShowCaseEmployeeButton" bound button field on the main page
        Then the "Save" labelled business action button on a modal is disabled
        And the user selects the "lastName" bound text field on a modal
        And the user writes "John" in the text field
        And the user blurs the text field
        Then the "Save" labelled business action button on a modal is enabled

    Scenario: As an ATP/ XTreeM user I can open and select an item within the lookup dialog
        XT-94361
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/CustomLookupDialog"
        Then the titled page containing "Dialog - Custom Lookup" is displayed

        And the user clicks the "openMultiSelectionLookupAutoConfigured" bound button on the main page
        And the user selects the "$applicationCodeLookup" bound table field on a modal
        And the user selects the row with text "Anisette - Mcguiness" in the "Product" labelled column header of the table field
        And the user ticks the main checkbox of the selected row in the table field
        # And takes a screenshot
        And the user clicks the "Select" button of the Lookup dialog
# And takes a screenshot
