Feature: 3-2 Async loader dialog

    Scenario: As a user I want to be able to run a long async mutation
        XT-96565
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Dialogs"
        And the "Dialogs" titled page is displayed
        And the user selects the "Customer" labelled reference field on the main page
        When the user writes "Bechtelar and Sons" in the reference field
        And the user selects "Bechtelar and Sons" in the reference field
        And the user selects the "New name" labelled text field on the main page
        When the user writes "Be<PERSON><PERSON><PERSON> and Sons RENAMED" in the text field
        And the user selects the "mockedProcessingTime" bound numeric field on the main page
        And the user writes "19000" in the numeric field
        And the user blurs the numeric field
        When the user clicks in the "asyncMutationButton" bound button field on the main page
        And the user waits 8 seconds
        Then an info dialog appears on the main page
        And the text in the header of the dialog is "Your operation is still in progress"
        When the user clicks the "Keep waiting" button of the Confirm dialog
        And the user waits 8 seconds
        Then an info dialog appears on the main page
        And the text in the header of the dialog is "Your operation is still in progress"
        When the user clicks the "Keep waiting" button of the Confirm dialog
        And the user waits 2 seconds
        Then a toast with text "New customer name is successfully applied: Bechtelar and Sons RENAMED" is displayed
        # Record verification and clean up
        Then the user navigates to the following link: "@sage/xtrem-show-case/StandardShowCaseCustomer/eyJfaWQiOiIxNTcifQ=="
        Then the "Customer Bechtelar and Sons RENAMED" titled page is displayed
        And the user selects the "name" labelled text field on the main page
        And the user writes "Bechtelar and Sons" in the text field
        And the user clicks the "$standardSaveAction" bound business action button on the main page
        Then the "Customer Bechtelar and Sons" titled page is displayed

    Scenario: As a user I want to be able to run a medium async mutation
        XT-96565
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Dialogs"
        And the "Dialogs" titled page is displayed
        And the user selects the "Customer" labelled reference field on the main page
        When the user writes "Bechtelar and Sons" in the reference field
        And the user selects "Bechtelar and Sons" in the reference field
        And the user selects the "New name" labelled text field on the main page
        When the user writes "Bechtelar and Sons RENAMED" in the text field
        And the user selects the "mockedProcessingTime" bound numeric field on the main page
        And the user writes "10000" in the numeric field
        And the user blurs the numeric field
        When the user clicks in the "asyncMutationButton" bound button field on the main page
        And the user waits 8 seconds
        Then an info dialog appears on the main page
        And the text in the header of the dialog is "Your operation is still in progress"
        When the user clicks the "Keep waiting" button of the Confirm dialog
        And the user waits 2 seconds
        Then a toast with text "New customer name is successfully applied: Bechtelar and Sons RENAMED" is displayed
        # Record verification and clean up
        Then the user navigates to the following link: "@sage/xtrem-show-case/StandardShowCaseCustomer/eyJfaWQiOiIxNTcifQ=="
        Then the "Customer Bechtelar and Sons RENAMED" titled page is displayed
        And the user selects the "name" labelled text field on the main page
        And the user writes "Bechtelar and Sons" in the text field
        And the user clicks the "$standardSaveAction" bound business action button on the main page
        Then the "Customer Bechtelar and Sons" titled page is displayed

    Scenario: As a user I want to be able to run a short async mutation
        XT-96565
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Dialogs"
        And the "Dialogs" titled page is displayed
        And the user selects the "Customer" labelled reference field on the main page
        When the user writes "Bechtelar and Sons" in the reference field
        And the user selects "Bechtelar and Sons" in the reference field
        And the user selects the "New name" labelled text field on the main page
        When the user writes "Bechtelar and Sons RENAMED" in the text field
        And the user selects the "mockedProcessingTime" bound numeric field on the main page
        And the user writes "2000" in the numeric field
        And the user blurs the numeric field
        When the user clicks in the "asyncMutationButton" bound button field on the main page
        And the user waits 2 seconds
        Then a toast with text "New customer name is successfully applied: Bechtelar and Sons RENAMED" is displayed
        # Record verification and clean up
        Then the user navigates to the following link: "@sage/xtrem-show-case/StandardShowCaseCustomer/eyJfaWQiOiIxNTcifQ=="
        Then the "Customer Bechtelar and Sons RENAMED" titled page is displayed
        And the user selects the "name" labelled text field on the main page
        And the user writes "Bechtelar and Sons" in the text field
        And the user clicks the "$standardSaveAction" bound business action button on the main page
        Then the "Customer Bechtelar and Sons" titled page is displayed

    Scenario: As a user I want to see a toast error message when an async mutation fails
        XT-96565
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Dialogs"
        Then the "Dialogs" titled page is displayed
        When the user selects the "Customer" labelled reference field on the main page
        And the user writes "Bechtelar and Sons" in the reference field
        And the user selects "Bechtelar and Sons" in the reference field
        And the user selects the "New name" labelled text field on the main page
        And the user writes "Bechtelar and Sons RENAMED" in the text field
        And the user selects the "mockedProcessingTime" bound numeric field on the main page
        And the user writes "10000" in the numeric field
        And the user blurs the numeric field
        And the user selects the "shouldOperationFail" bound checkbox field on the main page
        And the user ticks the checkbox field
        And the user clicks in the "asyncMutationButton" bound button field on the main page
        And the user waits 8 seconds
        Then an info dialog appears on the main page
        And the text in the header of the dialog is "Your operation is still in progress"
        When the user clicks the "Keep waiting" button of the Confirm dialog
        And the user waits 4 seconds
        Then a error toast containing text "Error: Async test mutation failed." is displayed
        # Record verification
        When the user navigates to the following link: "@sage/xtrem-show-case/StandardShowCaseCustomer/eyJfaWQiOiIxNTcifQ=="
        Then the "Customer Bechtelar and Sons" titled page is displayed
