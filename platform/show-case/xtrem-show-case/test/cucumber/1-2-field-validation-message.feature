Feature: 1-2 Field validation message
    # Tests field validation error messages across different field types, verifying proper display and styling of validation feedback

    #----------------dropdown-list field ---------------

    Scenario: dropdown-list field - As an ATP XTreeM I can verify the validation error message
        XT-25834
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/DropdownList"
        Then the "Field - Dropdown List" titled page is displayed

        Given the user selects the "Dropdown List with Custom Validation" labelled dropdown-list field on the main page
        When the user clicks in the dropdown-list field
        And the user selects "Error" in the dropdown-list field
        Then the "Oh, you selected an error." validation error message of the dropdown-list field is displayed
        When the user clicks in the dropdown-list field
        And the user selects "Success" in the dropdown-list field
        Then the "Oh, you selected an error." validation error message of the dropdown-list field is hidden


    Scenario: dropdown-list field - As an ATP XTreeM I can verify the validation warning message
        XT-25834
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/DropdownList"
        Then the "Field - Dropdown List" titled page is displayed

        Given the user selects the "warningMessageField" bound dropdown-list field on the main page
        When the user clicks in the dropdown-list field
        And the user selects "Awful" in the dropdown-list field
        Then the "Wow, warning!" validation warning message of the dropdown-list field is displayed


    Scenario: dropdown-list field - As an ATP XTreeM I can verify the validation info message
        XT-25834
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/DropdownList"
        Then the "Field - Dropdown List" titled page is displayed

        Given the user selects the "infoMessageField" bound dropdown-list field on the main page
        When the user clicks in the dropdown-list field
        And the user selects "Awful" in the dropdown-list field
        Then the "Wow, warning!" validation info message of the dropdown-list field is displayed


    # ----------------multi dropdown field ---------------

    Scenario: multi dropdown field - As an ATP XTreeM I can verify the validation error message
        XT-25834
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/MultiDropdown"
        Then the "Field - Multi-dropdown List" titled page is displayed

        Given the user selects the "With Custom Validation" labelled multi dropdown field on the main page
        When the user clicks in the multi dropdown field
        And the user selects "Error" in the multi dropdown field
        Then the "Oh, you selected an error." validation error message of the multi dropdown field is displayed
        When the user clicks in the multi dropdown field
        And the user selects "Error | Success" in the multi dropdown field
        Then the "Oh, you selected an error." validation error message of the multi dropdown field is hidden


    Scenario: multi dropdown field - As an ATP XTreeM I can verify the validation warning message
        XT-25834
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/MultiDropdown"
        Then the "Field - Multi-dropdown List" titled page is displayed

        Given the user selects the "warningMessageField" bound multi dropdown field on the main page
        When the user clicks in the multi dropdown field
        And the user selects "Awful" in the multi dropdown field
        Then the "Wow, warning!" validation warning message of the multi dropdown field is displayed


    Scenario: multi dropdown field - As an ATP XTreeM I can verify the validation info message
        XT-25834
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/MultiDropdown"
        Then the "Field - Multi-dropdown List" titled page is displayed

        Given the user selects the "infoMessageField" bound multi dropdown field on the main page
        When the user clicks in the multi dropdown field
        And the user selects "Awful" in the multi dropdown field
        Then the "Wow, warning!" validation info message of the multi dropdown field is displayed


    #----------------multi reference field ---------------

    Scenario: multi reference field - As an ATP XTreeM I can verify the validation warning message
        XT-25834
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/MultiReference"
        Then the "Field - Multi-reference" titled page is displayed

        Given the user selects the "warningMessageField" bound multi reference field on the main page
        When the user clicks in the multi reference field
        Then the "Wow, warning!" validation warning message of the multi reference field is displayed


    Scenario: multi reference field - As an ATP XTreeM I can verify the validation info message
        XT-25834
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/MultiReference"
        Then the "Field - Multi-reference" titled page is displayed

        Given the user selects the "infoMessageField" bound multi reference field on the main page
        When the user clicks in the multi reference field
        Then the "Wow, warning!" validation info message of the multi reference field is displayed


    #----------------reference field ---------------

    Scenario: reference field - As an ATP XTreeM I can verify the validation warning message
        XT-25834
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Reference"
        Then the "Field - Reference" titled page is displayed

        Given the user selects the "warningMessageField" bound reference field on the main page
        When the user clicks in the reference field
        Then the "Wow, warning!" validation warning message of the reference field is displayed


    Scenario: reference field - As an ATP XTreeM I can verify the validation info message
        XT-25834
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Reference"
        Then the "Field - Reference" titled page is displayed

        Given the user selects the "infoMessageField" bound reference field on the main page
        When the user clicks in the reference field
        Then the "Wow, warning!" validation info message of the reference field is displayed


    #----------------select field ---------------

    Scenario: select field - As an ATP XTreeM I can verify the validation warning message
        XT-25834
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Select"
        Then the "Field - Select" titled page is displayed

        Given the user selects the "warningMessageField" bound select field on the main page
        When the user clicks in the select field
        And the user selects "Awful" in the select field
        Then the "Wow, warning!" validation warning message of the select field is displayed


    Scenario: select field - As an ATP XTreeM I can verify the validation info message
        XT-25834
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Select"
        Then the "Field - Select" titled page is displayed

        Given the user selects the "infoMessageField" bound select field on the main page
        When the user clicks in the select field
        And the user selects "Awful" in the select field
        Then the "Wow, warning!" validation info message of the select field is displayed


    #----------------text field ---------------

    Scenario: text field - As an ATP XTreeM I can verify the validation warning message
        XT-25834
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Text"
        Then the "Text" titled page is displayed

        Given the user selects the "warningMessageField" bound text field on the main page
        When the user writes "Warning" in the text field
        Then the "Wow, warning!" validation warning message of the text field is displayed


    Scenario: text field - As an ATP XTreeM I can verify the validation info message
        XT-25834
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Text"
        Then the "Text" titled page is displayed

        Given the user selects the "infoMessageField" bound text field on the main page
        When the user writes "Info" in the text field
        Then the "Wow, warning!" validation info message of the text field is displayed


    #----------------text area ---------------

    Scenario: text area field - As an ATP XTreeM I can verify the validation warning message
        XT-25834
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TextArea"
        Then the "Text area" titled page is displayed

        Given the user selects the "warningMessageField" bound text area field on the main page
        When the user writes "Warning" in the text area field
        Then the "Wow, warning!" validation warning message of the text area field is displayed


    Scenario: text area field - As an ATP XTreeM I can verify the validation info message
        XT-25834
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TextArea"
        Then the "Text area" titled page is displayed

        Given the user selects the "infoMessageField" bound text area field on the main page
        When the user writes "Info" in the text area field
        Then the "Wow, warning!" validation info message of the text area field is displayed
