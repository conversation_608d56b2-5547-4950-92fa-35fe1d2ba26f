Feature: 1-2 Form designer
    # Tests functionality of the form designer component across different devices, focusing on interactions with various form areas (header, body, footer)

    Scenario Outline: <Device> - As an ATP XTreeM User I can click on the <Location> of the form designer field using bind
        XT-64031
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/FormDesigner"
        Then the "Form Designer" titled page is displayed
        And the "field" bound form designer field on the main page is displayed

        When the user selects the "field" bound form designer field on the main page
        And the user clicks in the <Location> of the form designer
        # And takes a screenshot

        Examples:
            | Device  | Location |
            | desktop | header   |
            | desktop | body     |
            | desktop | footer   |


    Scenario Outline: <Device> - As an ATP XTreeM User I can click on the <Location> of the form designer field using label
        XT-64031
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/FormDesigner"
        Then the "Form Designer" titled page is displayed
        And the "field" bound form designer field on the main page is displayed

        Given the user selects the "Title" labelled text field on the main page
        When the user writes "Sample title" in the text field
        And the user presses Tab

        When the user selects the "Sample title" labelled form designer field on the main page
        And the user clicks in the <Location> of the form designer
        # And takes a screenshot

        Examples:
            | Device  | Location |
            | desktop | header   |
            | desktop | body     |
            | desktop | footer   |


    Scenario Outline: <Device> - As an ATP XTreeM User I can write in the <Location> of the form designer field using bind
        XT-64031
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/FormDesigner"
        Then the "Form Designer" titled page is displayed
        And the "field" bound form designer field on the main page is displayed

        When the user selects the "field" bound form designer field on the main page
        And the user writes "Test text" in the <Location> of the form designer
        # And takes a screenshot

        Examples:
            | Device  | Location |
            | desktop | header   |
            | desktop | body     |
            | desktop | footer   |


    Scenario Outline: <Device> - As an ATP XTreeM User I can write in the <Location> of the form designer field using label
        XT-64031
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/FormDesigner"
        Then the "Form Designer" titled page is displayed
        And the "field" bound form designer field on the main page is displayed

        Given the user selects the "Title" labelled text field on the main page
        When the user writes "Sample title" in the text field
        And the user presses Tab

        When the user selects the "Sample title" labelled form designer field on the main page
        And the user writes "Test text" in the <Location> of the form designer
        # And takes a screenshot

        Examples:
            | Device  | Location |
            | desktop | header   |
            | desktop | body     |
            | desktop | footer   |


    Scenario Outline: <Device> - As an ATP XTreeM User I can select an element on a given line in the <Location> of the form designer field using bind
        XT-64031
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/FormDesigner"
        Then the "Form Designer" titled page is displayed
        And the "field" bound form designer field on the main page is displayed

        When the user selects the "field" bound form designer field on the main page
        And the user writes "Test" in the <Location> of the form designer
        And the user presses Enter
        And the user writes "this" in the <Location> of the form designer
        And the user presses Enter
        And the user writes "text" in the <Location> of the form designer

        And the user selects the element on line 2 in the <Location> of the form designer
        # And takes a screenshot

        Examples:
            | Device  | Location |
            | desktop | header   |
            | desktop | body     |
            | desktop | footer   |


    Scenario Outline: <Device> - As an ATP XTreeM User I can select all the text in the <Location> of the form designer field using bind
        XT-64031
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/FormDesigner"
        Then the "Form Designer" titled page is displayed
        And the "field" bound form designer field on the main page is displayed

        When the user selects the "field" bound form designer field on the main page
        And the user writes "Test" in the <Location> of the form designer
        And the user presses Enter
        And the user writes "this" in the <Location> of the form designer
        And the user presses Enter
        And the user writes "text" in the <Location> of the form designer

        And the user selects all the content of the <Location> of the form designer
        # And takes a screenshot

        Examples:
            | Device  | Location |
            | desktop | header   |
            | desktop | body     |
            | desktop | footer   |


    Scenario Outline: <Device> - As and ATP XTreeM user I can verify the form designer field title
        XT-64031
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/FormDesigner"
        Then the "Form Designer" titled page is displayed
        And the "field" bound form designer field on the main page is displayed

        Given the user selects the "Title" labelled text field on the main page
        When the user writes "Sample title" in the text field
        And the user presses Tab

        Given the user selects the "field" bound form designer field on the main page
        Then the title of the form designer field is "Sample title"
        Then the title of the form designer field is displayed

        Given the user selects the "Is title hidden" labelled checkbox field on the main page
        When the user ticks the checkbox field

        Given the user selects the "field" bound form designer field on the main page
        Then the title of the form designer field is hidden
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |


    Scenario Outline: <Device> - As and ATP XTreeM user I can verify the form designer field helper text
        XT-64031
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/FormDesigner"
        Then the "Form Designer" titled page is displayed
        And the "field" bound form designer field on the main page is displayed

        Given the user selects the "Helper text" labelled text field on the main page
        When the user writes "Sample text" in the text field
        And the user presses Tab

        Given the user selects the "field" bound form designer field on the main page
        Then the helper text of the form designer field is "Sample text"
        Then the helper text of the form designer field is displayed

        Given the user selects the "Is helper text hidden" labelled checkbox field on the main page
        When the user ticks the checkbox field

        Given the user selects the "field" bound form designer field on the main page
        Then the helper text of the form designer field is hidden
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |


    Scenario Outline: <Device> - As and ATP XTreeM user I can verify if the form designer field is displayed or hidden
        XT-64031
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/FormDesigner"
        Then the "Form Designer" titled page is displayed
        And the "field" bound form designer field on the main page is displayed

        Given the user selects the "field" bound form designer field on the main page
        Then the "field" bound form designer field on the main page is displayed

        Given the user selects the "Is hidden" labelled checkbox field on the main page
        When the user ticks the checkbox field
        Then the "field" bound form designer field on the main page is hidden
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |


    Scenario Outline: <Device> - As and ATP XTreeM user I can verify if the form designer field is enabled or disabled
        XT-64031
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/FormDesigner"
        Then the "Form Designer" titled page is displayed
        And the "field" bound form designer field on the main page is displayed

        Given the user selects the "field" bound form designer field on the main page
        Then the form designer field is enabled

        Given the user selects the "Is disabled" labelled checkbox field on the main page
        When the user ticks the checkbox field

        Given the user selects the "field" bound form designer field on the main page
        Then the form designer field is disabled
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |


    Scenario Outline: As and ATP XTreeM user I can click the formatting button <ButtonName> in the form designer field tooldbar
        XT-64035
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/FormDesigner"
        Then the "Form Designer" titled page is displayed
        And the "field" bound form designer field on the main page is displayed

        When the user selects the "field" bound form designer field on the main page
        And the user writes "Test text" in the body of the form designer

        And the user selects all the content of the body of the form designer

        And the user clicks the <ButtonName> button in the form designer toolbar
        # And takes a screenshot
        Examples:
            | ButtonName      |
            | "Numbered List" |
            | "Bulleted List" |
            | "Bold"          |
            | "Italic"        |
            | "Underline"     |
            | "Block quote"   |


    Scenario: As and ATP XTreeM user I can click the undo / redo button in the form designer field tooldbar
        XT-64035
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/FormDesigner"
        Then the "Form Designer" titled page is displayed
        And the "field" bound form designer field on the main page is displayed

        When the user selects the "field" bound form designer field on the main page
        And the user writes "Test text" in the body of the form designer

        And the user selects all the content of the body of the form designer

        And the user clicks the "Undo" button in the form designer toolbar
        # And takes a screenshot
        And the user clicks the "Redo" button in the form designer toolbar
    # And takes a screenshot


    Scenario Outline: As and ATP XTreeM user I can select a formatting option <optionName> in the form designer field tooldbar
        XT-64035
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/FormDesigner"
        Then the "Form Designer" titled page is displayed
        And the "field" bound form designer field on the main page is displayed

        When the user selects the "field" bound form designer field on the main page
        And the user writes "Test text" in the body of the form designer

        And the user selects all the content of the body of the form designer

        And the user clicks the <ButtonName> button in the form designer toolbar
        And the user selects the <optionName> option in the form designer toolbar
        # And takes a screenshot

        Examples:
            | ButtonName       | optionName      |
            | "Heading"        | "Heading 2"     |
            | "Font size"      | "16"            |
            | "Font family"    | "Georgia"       |
            | "Text alignment" | "Align center"  |
            | "Formatting"     | "Strikethrough" |


    Scenario Outline: <Device> - As and ATP XTreeM user I can add a table to the form designer field
        XT-64035
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/FormDesigner"
        Then the "Form Designer" titled page is displayed
        And the "field" bound form designer field on the main page is displayed

        When the user selects the "field" bound form designer field on the main page
        And the user clicks in the body of the form designer
        And the user inserts a table with 3 rows and 7 columns in the form designer
        And the user clicks in the header of the form designer
        # And takes a screenshot

        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |


    Scenario: As and ATP XTreeM user I can set the font / background color in the form designer field tooldbar
        XT-64035
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/FormDesigner"
        Then the "Form Designer" titled page is displayed
        And the "field" bound form designer field on the main page is displayed

        When the user selects the "field" bound form designer field on the main page
        And the user writes "Test text" in the body of the form designer

        And the user selects all the content of the body of the form designer

        And the user sets the font color to "#FFBC19" in the form designer
        And the user clicks in the header of the form designer
        # And takes a screenshot

        And the user selects all the content of the body of the form designer
        And the user sets the background color to "#00324C" in the form designer
        And the user clicks in the header of the form designer
    # And takes a screenshot


    Scenario Outline: As and ATP XTreeM user I can click the action button <ButtonName> in the form designer field tooldbar
        XT-64035
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/FormDesigner"
        Then the "Form Designer" titled page is displayed
        And the "field" bound form designer field on the main page is displayed

        When the user selects the "field" bound form designer field on the main page
        And the user writes "Test text" in the body of the form designer

        And the user clicks the <ButtonName> button in the form designer toolbar
        # And takes a screenshot
        Examples:
            | ButtonName                     |
            | "Page break"                   |
            | "Insert unbreakable container" |
            | "Zoom in"                      |
            | "Zoom out"                     |


    Scenario Outline: As and ATP XTreeM user I can click the panel button <ButtonName> in the form designer field tooldbar
        XT-64035
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/FormDesigner"
        Then the "Form Designer" titled page is displayed
        And the "field" bound form designer field on the main page is displayed

        When the user selects the "field" bound form designer field on the main page

        And the user clicks the <ButtonName> button in the form designer toolbar
        # And takes a screenshot
        Then the <PanelName> panel in the form designer is displayed

        When the user clicks the Close button of the <PanelName> panel in the form designer
        # And takes a screenshot
        Then the <PanelName> panel in the form designer is hidden

        Examples:
            | ButtonName                            | PanelName    |
            | "Show/Hide fields panel"              | "Fields"     |
            | "Show/Hide document properties panel" | "Fields"     |
            | "Show/Hide formatting panel"          | "Formatting" |


    Scenario Outline: <Device> - As and ATP XTreeM user I can interact with the form designer field tooldbar buttons
        XT-64035
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/FormDesigner"
        Then the "Form Designer" titled page is displayed
        And the "field" bound form designer field on the main page is displayed

        When the user selects the "field" bound form designer field on the main page
        And the user writes "Test text" in the body of the form designer

        And the user selects all the content of the body of the form designer

        And the user clicks the "Bold" button in the form designer toolbar
        # And takes a screenshot

        And the user clicks the "Font Size" button in the form designer toolbar
        And the user selects the "16" option in the form designer toolbar
        # And takes a screenshot

        And the user clicks the "Formatting" button in the form designer toolbar
        And the user selects the "Strikethrough" option in the form designer toolbar
        # And takes a screenshot

        And the user sets the font color to "#FFBC19" in the form designer
        And the user clicks in the header of the form designer
        # And takes a screenshot

        And the user selects all the content of the body of the form designer
        And the user sets the background color to "#00324C" in the form designer
        And the user clicks in the header of the form designer
        # And takes a screenshot

        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |


    Scenario Outline: <Device> - As and ATP XTreeM user I can verify if a panel in the form designer field is displayed or hidden
        XT-64035
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/FormDesigner"
        Then the "Form Designer" titled page is displayed
        And the "field" bound form designer field on the main page is displayed

        When the user selects the "field" bound form designer field on the main page
        And the user clicks the "Show/Hide fields panel" button in the form designer toolbar
        # And takes a screenshot

        Then the "Fields" panel in the form designer is displayed

        When the user clicks the "Show/Hide formatting panel" button in the form designer toolbar
        # And takes a screenshot

        Then the "Formatting" panel in the form designer is displayed

        When the user clicks the Close button of the "Formatting" panel in the form designer
        # And takes a screenshot

        Then the "Formatting" panel in the form designer is hidden

        When the user clicks the Close button of the "Fields" panel in the form designer
        # And takes a screenshot

        Then the "Fields" panel in the form designer is hidden
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |


    Scenario Outline: <Device> - As an ATP XTreeM user I can interact with the node-step-tree
        XT-64045
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/FormDesigner"
        Then the "Form Designer" titled page is displayed
        And the "field" bound form designer field on the main page is displayed

        When the user selects the "field" bound form designer field on the main page
        And the user clicks in the body of the form designer

        And the user clicks the "Show/Hide fields panel" button in the form designer toolbar
        # And takes a screenshot

        Then the "Fields" panel in the form designer is displayed

        When the user searches "Custom field" in the node-step-tree of the form designer on the main page

        And the user selects the node-step-tree in the form designer on the main page

        And the user selects the tree-view element of level "1" with text "Custom field" in the node-step-tree of the form designer
        And the user clicks the tree-view element in the node-step-tree of the form designer
        # And takes a screenshot

        And the title of the form designer editor dialog is "Insert an object type"
        And the title step of the form designer editor dialog is "1. Insert mode"
        And the card with "Table" value in the selection card of the form designer editor dialog is unselected
        And the user selects the card with "Table" value in the selection card of the form designer editor dialog
        And the card with "Table" value in the selection card of the form designer editor dialog is selected
        # And takes a screenshot

        And the user clicks the "Next" button of the form designer editor dialog
        And the title of the form designer editor dialog is "Insert an object type"
        And the title step of the form designer editor dialog is "2. Column selection"

        And the user selects the node-step-tree in the form designer on a modal

        And the user selects the tree-view element of level "1" with text "Anchor property" in the node-step-tree of the form designer

        And the user expands the tree-view element in the node-step-tree of the form designer
        # And takes a screenshot

        # nothing should happen
        And the user expands the tree-view element in the node-step-tree of the form designer
        # And takes a screenshot

        And the user selects the tree-view element of level "2" with text "Factory" in the node-step-tree of the form designer

        And the user expands the tree-view element in the node-step-tree of the form designer

        And the user selects the tree-view element of level "3" with text "Extends" in the node-step-tree of the form designer
        And the user expands the tree-view element in the node-step-tree of the form designer
        # And takes a screenshot

        And the user collapses the tree-view element in the node-step-tree of the form designer
        # And takes a screenshot

        And the user selects the tree-view element of level "3" with text "Package" in the node-step-tree of the form designer
        And the user expands the tree-view element in the node-step-tree of the form designer

        And the user selects the tree-view element of level "4" with text "Name" in the node-step-tree of the form designer
        And the user ticks the tree-view element in the node-step-tree of the form designer
        # And takes a screenshot

        # nothing should happen
        And the user ticks the tree-view element in the node-step-tree of the form designer
        # And takes a screenshot

        And the user selects the tree-view element of level "4" with text "Title" in the node-step-tree of the form designer
        And the user ticks the tree-view element in the node-step-tree of the form designer
        # And takes a screenshot

        And the user unticks the tree-view element in the node-step-tree of the form designer
        # And takes a screenshot

        # should tick and untick the element without checking state first.
        And the user clicks the tree-view element in the node-step-tree of the form designer
        # And takes a screenshot

        And the user clicks the tree-view element in the node-step-tree of the form designer
        # And takes a screenshot

        And the user clicks the Close button of the form designer editor dialog

        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> - As an ATP / XTreeM user, I can interact with the form editor dialog
        XT-64883
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/FormDesigner"
        And the "Form Designer" titled page is displayed
        And the "field" bound form designer field on the main page is displayed
        And the user selects the "field" bound form designer field on the main page
        And the user clicks the "Show/Hide fields panel" button in the form designer toolbar
        And the "Fields" panel in the form designer is displayed

        When the user searches "Custom field" in the node-step-tree of the form designer on the main page
        And the user selects the node-step-tree in the form designer on the main page
        And the user selects the tree-view element of level "1" with text "Custom field" in the node-step-tree of the form designer
        And the user clicks the tree-view element in the node-step-tree of the form designer

        And the title of the form designer editor dialog is "Insert an object type"
        And the title step of the form designer editor dialog is "1. Insert mode"
        And the card with "Table" value in the selection card of the form designer editor dialog is unselected
        # And takes a screenshot
        And the user selects the card with "Table" value in the selection card of the form designer editor dialog
        And the card with "Table" value in the selection card of the form designer editor dialog is selected
        # And takes a screenshot
        And the user clicks the "Next" button of the form designer editor dialog
        And the "Next" button of the form designer editor dialog is disabled
        And the "Previous" button of the form designer editor dialog is enabled

        And the title of the form designer editor dialog is "Insert an object type"
        And the title step of the form designer editor dialog is "2. Column selection"
        # And takes a screenshot
        And the user clicks the "Previous" button of the form designer editor dialog
        And the "Next" button of the form designer editor dialog is enabled
        # And takes a screenshot
        And the user clicks the Close button of the form designer editor dialog
        And the user waits for 2 seconds
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |


    Scenario Outline: <Device> - As an ATP / XTreeM user, I can interact with the widget editor dialog
        XT-64884
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/FormDesigner"
        And the "Form Designer" titled page is displayed
        And the "field" bound form designer field on the main page is displayed
        And the user selects the "field" bound form designer field on the main page
        And the user clicks in the body of the form designer

        And the user clicks the "Show/Hide fields panel" button in the form designer toolbar
        And the "Fields" panel in the form designer is displayed

        When the user searches "Show case invoice" in the node-step-tree of the form designer on the main page
        And the user selects the node-step-tree in the form designer on the main page
        And the user selects the tree-view element of level "1" with text "Show case invoice" in the node-step-tree of the form designer
        And the user clicks the tree-view element in the node-step-tree of the form designer

        And the title of the form designer editor dialog is "Insert an object type"
        And the title step of the form designer editor dialog is "1. Insert mode"
        And the user selects the card with "Table" value in the selection card of the form designer editor dialog
        And the card with "Table" value in the selection card of the form designer editor dialog is selected
        And the user clicks the "Next" button of the form designer editor dialog

        And the title of the form designer editor dialog is "Insert an object type"
        And the title step of the form designer editor dialog is "2. Column selection"
        And the user selects the node-step-tree in the form designer on a modal

        And the user selects the tree-view element of level "1" with text "Customer" in the node-step-tree of the form designer
        And the user expands the tree-view element in the node-step-tree of the form designer

        And the user selects the tree-view element of level "2" with text "Name" in the node-step-tree of the form designer
        And the user ticks the tree-view element in the node-step-tree of the form designer

        And the user selects the tree-view element of level "1" with text "Customer" in the node-step-tree of the form designer
        And the user collapses the tree-view element in the node-step-tree of the form designer

        And the user selects the tree-view element of level "1" with text "Order" in the node-step-tree of the form designer
        And the user expands the tree-view element in the node-step-tree of the form designer

        And the user selects the tree-view element of level "2" with text "Details" in the node-step-tree of the form designer
        And the user ticks the tree-view element in the node-step-tree of the form designer
        And the user selects the tree-view element of level "2" with text "Order date" in the node-step-tree of the form designer
        And the user ticks the tree-view element in the node-step-tree of the form designer
        And the user selects the tree-view element of level "2" with text "Order type" in the node-step-tree of the form designer
        And the user ticks the tree-view element in the node-step-tree of the form designer

        And the user selects the tree-view element of level "1" with text "Order" in the node-step-tree of the form designer
        And the user expands the tree-view element in the node-step-tree of the form designer

        And the user selects the tree-view element of level "1" with text "Purchase date" in the node-step-tree of the form designer
        And the user ticks the tree-view element in the node-step-tree of the form designer

        And the user clicks the "Next" button of the form designer editor dialog

        And the title of the form designer editor dialog is "Insert an object type"
        And the title step of the form designer editor dialog is "3. Define content"

        When the user writes "Customer name" in the "Title" text field of row "1" in the form designer widget editor
        Then the value of the "Title" text field of row "1" in the form designer widget editor is "Customer name"
        # And takes a screenshot

        When the user clears the "Title" text field of row "2" in the form designer widget editor
        # And takes a screenshot

        And the user writes "Order details" in the "Title" text field of row "2" in the form designer widget editor

        Then the "Operation" dropdown-list field of row "2" in the form designer widget editor dialog is hidden
        Then the "Operation" dropdown-list field of row "1" in the form designer widget editor dialog is displayed
        # And takes a screenshot

        And the user writes "Group 2" in the "Group" dropdown-list field of row "3" in the form designer widget editor
        Then the value of the "Group" dropdown-list field of row "5" in the form designer widget editor is "Group 2"
        # And takes a screenshot

        And the user clicks the "Next" button of the form designer editor dialog

        And the title of the form designer editor dialog is "Insert an object type"
        And the title step of the form designer editor dialog is "4. Define filters"


        When the user clicks the "Add filter" table button in the form designer widget editor
        # And takes a screenshot

        Then the "Filter type" dropdown-list field of row "1" in the form designer widget editor is disabled
        # And takes a screenshot

        And the user writes "Order date" in the "Property" dropdown-list field of row "1" in the form designer widget editor

        Then the "Filter type" dropdown-list field of row "1" in the form designer widget editor is enabled
        # And takes a screenshot

        And the user writes "Greater than" in the "Filter type" dropdown-list field of row "1" in the form designer widget editor
        And the user writes "01/29/2024" in the "Filter value" date field of row "1" in the form designer widget editor
        Then the value of the "Filter value" date field of row "1" in the form designer widget editor is "01/29/2024"
        # And takes a screenshot

        Then the "add" action button of row "1" the form designer widget editor is displayed

        When the user clicks the "add" action button of row "1" in the form designer widget editor
        # And takes a screenshot

        Then the "add" action button of row "1" the form designer widget editor is hidden
        Then the "add" action button of row "2" the form designer widget editor is displayed
        Then the "remove" action button of row "1" the form designer widget editor is displayed
        # And takes a screenshot

        And the user clicks the Close button of the form designer editor dialog
        And the user waits for 2 seconds

        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline:<Device> - As an ATP XTreeM user I can interact with the form designer table cell
        XT-65171 XT-70697
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/FormDesigner"
        Then the "Form Designer" titled page is displayed
        And the "field" bound form designer field on the main page is displayed

        When the user selects the "field" bound form designer field on the main page
        And the user clicks in the body of the form designer

        And the user clicks the "Show/Hide fields panel" button in the form designer toolbar
        # And takes a screenshot

        Then the "Fields" panel in the form designer is displayed

        When the user searches "Show case invoice" in the node-step-tree of the form designer on the main page

        And the user selects the node-step-tree in the form designer on the main page

        And the user selects the tree-view element of level "1" with text "Show case invoice" in the node-step-tree of the form designer
        And the user clicks the tree-view element in the node-step-tree of the form designer
        # And takes a screenshot

        And the title of the form designer editor dialog is "Insert an object type"
        And the title step of the form designer editor dialog is "1. Insert mode"
        And the user selects the card with "Table" value in the selection card of the form designer editor dialog

        And the user clicks the "Next" button of the form designer editor dialog
        And the title of the form designer editor dialog is "Insert an object type"
        And the title step of the form designer editor dialog is "2. Column selection"

        And the user selects the node-step-tree in the form designer on a modal

        And the user selects the tree-view element of level "1" with text "Customer" in the node-step-tree of the form designer
        And the user expands the tree-view element in the node-step-tree of the form designer

        And the user selects the tree-view element of level "2" with text "Name" in the node-step-tree of the form designer
        And the user ticks the tree-view element in the node-step-tree of the form designer

        And the user selects the tree-view element of level "1" with text "Customer" in the node-step-tree of the form designer
        And the user collapses the tree-view element in the node-step-tree of the form designer

        And the user selects the tree-view element of level "1" with text "Order" in the node-step-tree of the form designer
        And the user expands the tree-view element in the node-step-tree of the form designer

        And the user selects the tree-view element of level "2" with text "Details" in the node-step-tree of the form designer
        And the user ticks the tree-view element in the node-step-tree of the form designer
        And the user selects the tree-view element of level "2" with text "Order date" in the node-step-tree of the form designer
        And the user ticks the tree-view element in the node-step-tree of the form designer
        And the user selects the tree-view element of level "2" with text "Order type" in the node-step-tree of the form designer
        And the user ticks the tree-view element in the node-step-tree of the form designer

        And the user selects the tree-view element of level "1" with text "Order" in the node-step-tree of the form designer
        And the user expands the tree-view element in the node-step-tree of the form designer

        And the user selects the tree-view element of level "1" with text "Purchase date" in the node-step-tree of the form designer
        And the user ticks the tree-view element in the node-step-tree of the form designer

        And the user clicks the "Next" button of the form designer editor dialog

        And the user writes "Group 2" in the "Group" dropdown-list field of row "3" in the form designer widget editor
        And the user writes "Group 3" in the "Group" dropdown-list field of row "4" in the form designer widget editor

        And the user clicks the "Next" button of the form designer editor dialog

        And the user clicks the "Confirm" button of the form designer editor dialog

        And the user clicks the "Show/Hide fields panel" button in the form designer toolbar

        And the user selects the "1st" occurrence of "ShowCaseInvoice" table in the form designer body
        And the user selects the cell with the "Name" column header of the query table header in the table of the form designer
        And the user clicks in the selected cell of the table in the form designer
        And the user writes "Order Name" in the selected cell of the table in the form designer
        Then the value in the selected cell of the table in the form designer is "Order Name"
        # And takes a screenshot
        And the user selects the cell with the "Order date" column header of the query table footer group number "1" in the table of the form designer
        And the user writes "some text 1" in the selected cell of the table in the form designer
        Then the value in the selected cell of the table in the form designer is "some text 1"
        # And takes a screenshot
        And the user selects the cell with the "Purchase date" column header of the query table footer group number "2" in the table of the form designer
        And the user writes "some text 2" in the selected cell of the table in the form designer
        Then the value in the selected cell of the table in the form designer is "some text 2"
        # And takes a screenshot
        And the user selects the cell with the "Details" column header of the query table footer in the table of the form designer
        And the user writes "some text 3" in the selected cell of the table in the form designer
        Then the value in the selected cell of the table in the form designer is "some text 3"
        And the user selects the cell with the "Order date" column header of the query table body in the table of the form designer
        And the user clicks in the selected cell of the table in the form designer
        # And takes a screenshot
        And the user clicks the "Show/Hide formatting panel" button in the form designer toolbar
        # And takes a screenshot
        And the user clicks the "Insert column" button in the formatting panel of the form designer
        # And takes a screenshot
        And the user clicks the "Center" button of the "Cell horizontal alignment" toggle button group in the formatting panel of the form designer
        # And takes a screenshot
        And the user sets the background color to "#004D86" in the formatting panel of the form designer
        # And takes a screenshot
        And the user sets the border color to "#198E59" in the formatting panel of the form designer
        # And takes a screenshot
        And the user selects the "3px" option in the "Border width" dropdown field of the formatting panel of the form designer
        # And takes a screenshot
        And the user selects the "dashed" option in the "Border style" dropdown field of the formatting panel of the form designer
        # And takes a screenshot
        And the user writes "12" in the "Minimum cell height" text field of the formatting panel of the form designer
        # And takes a screenshot

        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> - As an ATP XTreeM user I can interact with the form designer data container
        XT-65171
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/FormDesigner"
        Then the "Form Designer" titled page is displayed
        And the "field" bound form designer field on the main page is displayed

        When the user selects the "field" bound form designer field on the main page
        And the user clicks in the body of the form designer

        And the user clicks the "Show/Hide fields panel" button in the form designer toolbar
        # And takes a screenshot

        Then the "Fields" panel in the form designer is displayed

        When the user searches "Show case invoice" in the node-step-tree of the form designer on the main page

        And the user selects the node-step-tree in the form designer on the main page

        And the user selects the tree-view element of level "1" with text "Show case invoice" in the node-step-tree of the form designer
        And the user clicks the tree-view element in the node-step-tree of the form designer
        # And takes a screenshot

        And the title of the form designer editor dialog is "Insert an object type"
        And the title step of the form designer editor dialog is "1. Insert mode"
        And the user selects the card with "Data container" value in the selection card of the form designer editor dialog
        # And takes a screenshot

        And the user clicks the "Next" button of the form designer editor dialog
        And the user clicks the "Next" button of the form designer editor dialog
        And the user clicks the "Confirm" button of the form designer editor dialog
        And the user clicks the "Show/Hide fields panel" button in the form designer toolbar

        And the user selects the "1st" occurrence of "ShowCaseInvoice" data container in the form designer body
        And the user clicks in the data container of the form designer
        And the user writes "some text" in the selected data container of the form designer
        Then the value in the selected data container of the form designer is "some text"
        # And takes a screenshot
        Examples:
            | Device  |
            | desktop |

    Scenario Outline:<Device> - As an ATP XTreeM user I can interact with the insert paragraph before / after of the form designer data container
        XT-70681
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/FormDesigner"
        Then the "Form Designer" titled page is displayed
        And the "field" bound form designer field on the main page is displayed

        When the user selects the "field" bound form designer field on the main page
        And the user clicks in the body of the form designer

        And the user writes "Paragraph before" in the body of the form designer
        And the user presses Enter
        And the user presses Enter
        And the user writes "Paragraph after" in the body of the form designer

        And the user selects the element on line 2 in the body of the form designer

        And the user clicks the "Show/Hide fields panel" button in the form designer toolbar

        Then the "Fields" panel in the form designer is displayed

        When the user searches "Show case invoice" in the node-step-tree of the form designer on the main page

        And the user selects the node-step-tree in the form designer on the main page

        And the user selects the tree-view element of level "1" with text "Show case invoice" in the node-step-tree of the form designer
        And the user clicks the tree-view element in the node-step-tree of the form designer

        And the title of the form designer editor dialog is "Insert an object type"
        And the title step of the form designer editor dialog is "1. Insert mode"
        And the user selects the card with "Data container" value in the selection card of the form designer editor dialog

        And the user clicks the "Next" button of the form designer editor dialog
        And the user clicks the "Next" button of the form designer editor dialog
        And the user clicks the "Confirm" button of the form designer editor dialog
        And the user clicks the "Show/Hide fields panel" button in the form designer toolbar

        And the user selects the "1st" occurrence of "ShowCaseInvoice" data container in the form designer body
        And the user clicks in the data container of the form designer

        When the user clicks the insert paragraph before block of the selected data container in the form designer
        And the user clicks in the data container of the form designer
        # And takes a screenshot

        When the user selects the "1st" occurrence of "ShowCaseInvoice" data container in the form designer body
        When the user clicks the insert paragraph after block of the selected data container in the form designer
        And the user clicks in the data container of the form designer
        # And takes a screenshot

        When the user clicks in the data container of the form designer
        And the user clicks the "Insert unbreakable container" button in the form designer toolbar
        # And takes a screenshot

        When the user selects the unbreakable container in the data container of the form designer
        And the user writes "some text" in the selected unbreakable container of the form designer
        Then the value in the selected unbreakable container of the form designer is "some text"
        # And takes a screenshot

        And the user clicks the insert paragraph before block of the selected unbreakable container of the form designer
        And the user clicks in the selected unbreakable container of the form designer
        # And takes a screenshot

        And the user clicks the insert paragraph after block of the selected unbreakable container of the form designer
        And the user clicks in the selected unbreakable container of the form designer
        # And takes a screenshot

        Examples:
            | Device  |
            | desktop |

    Scenario Outline:<Device> - As an ATP XTreeM user I can interact with the insert paragraph before / after of the form designer table
        XT-70681
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/FormDesigner"
        Then the "Form Designer" titled page is displayed
        And the "field" bound form designer field on the main page is displayed

        When the user selects the "field" bound form designer field on the main page
        And the user clicks in the body of the form designer
        And the user writes "Paragraph before" in the body of the form designer
        And the user presses Enter
        And the user presses Enter
        And the user writes "Paragraph after" in the body of the form designer

        And the user selects the element on line 2 in the body of the form designer

        And the user clicks the "Show/Hide fields panel" button in the form designer toolbar
        # And takes a screenshot

        Then the "Fields" panel in the form designer is displayed

        When the user searches "Show case invoice" in the node-step-tree of the form designer on the main page

        And the user selects the node-step-tree in the form designer on the main page

        And the user selects the tree-view element of level "1" with text "Show case invoice" in the node-step-tree of the form designer
        And the user clicks the tree-view element in the node-step-tree of the form designer
        # And takes a screenshot

        And the title of the form designer editor dialog is "Insert an object type"
        And the title step of the form designer editor dialog is "1. Insert mode"
        And the user selects the card with "Table" value in the selection card of the form designer editor dialog

        And the user clicks the "Next" button of the form designer editor dialog
        And the title of the form designer editor dialog is "Insert an object type"
        And the title step of the form designer editor dialog is "2. Column selection"

        And the user selects the node-step-tree in the form designer on a modal

        And the user selects the tree-view element of level "1" with text "Customer" in the node-step-tree of the form designer
        And the user expands the tree-view element in the node-step-tree of the form designer

        And the user selects the tree-view element of level "2" with text "Name" in the node-step-tree of the form designer
        And the user ticks the tree-view element in the node-step-tree of the form designer

        And the user selects the tree-view element of level "1" with text "Customer" in the node-step-tree of the form designer
        And the user collapses the tree-view element in the node-step-tree of the form designer

        And the user selects the tree-view element of level "1" with text "Order" in the node-step-tree of the form designer
        And the user expands the tree-view element in the node-step-tree of the form designer

        And the user selects the tree-view element of level "2" with text "Details" in the node-step-tree of the form designer
        And the user ticks the tree-view element in the node-step-tree of the form designer
        And the user selects the tree-view element of level "2" with text "Order date" in the node-step-tree of the form designer
        And the user ticks the tree-view element in the node-step-tree of the form designer
        And the user selects the tree-view element of level "2" with text "Order type" in the node-step-tree of the form designer
        And the user ticks the tree-view element in the node-step-tree of the form designer

        And the user selects the tree-view element of level "1" with text "Order" in the node-step-tree of the form designer
        And the user expands the tree-view element in the node-step-tree of the form designer

        And the user selects the tree-view element of level "1" with text "Purchase date" in the node-step-tree of the form designer
        And the user ticks the tree-view element in the node-step-tree of the form designer

        And the user clicks the "Next" button of the form designer editor dialog

        And the user writes "Group 2" in the "Group" dropdown-list field of row "3" in the form designer widget editor
        And the user writes "Group 3" in the "Group" dropdown-list field of row "4" in the form designer widget editor

        And the user clicks the "Next" button of the form designer editor dialog

        And the user clicks the "Confirm" button of the form designer editor dialog

        And the user clicks the "Show/Hide fields panel" button in the form designer toolbar

        And the user selects the "1st" occurrence of "ShowCaseInvoice" table in the form designer body

        When the user clicks the insert paragraph before block of the selected table in the form designer
        # And takes a screenshot

        When the user clicks the insert paragraph after block of the selected table in the form designer
        # And takes a screenshot

        And the user selects the cell with the "Name" column header of the query table body in the table of the form designer
        And the user clicks in the selected cell of the table in the form designer
        And the user clicks the "Insert unbreakable container" button in the form designer toolbar
        # And takes a screenshot

        When the user selects the unbreakable container in the selected cell of the table in the form designer
        And the user writes "some text" in the selected unbreakable container of the form designer
        Then the value in the selected unbreakable container of the form designer is "some text"
        # And takes a screenshot

        And the user clicks the insert paragraph before block of the selected unbreakable container of the form designer
        And the user clicks in the selected unbreakable container of the form designer
        # And takes a screenshot

        And the user clicks the insert paragraph after block of the selected unbreakable container of the form designer
        And the user clicks in the selected unbreakable container of the form designer
        # And takes a screenshot

        And the user selects the cell with the "Order date" column header of the query table body in the table of the form designer
        And the user clicks in the selected cell of the table in the form designer
        And the user clicks the "Insert unbreakable container" button in the form designer toolbar
        # And takes a screenshot

        When the user selects the unbreakable container in the selected cell of the table in the form designer
        And the user clicks in the selected unbreakable container of the form designer
        # And takes a screenshot

        And the user clicks the insert paragraph before block of the selected unbreakable container of the form designer
        And the user clicks in the selected unbreakable container of the form designer
        # And takes a screenshot

        And the user clicks the insert paragraph after block of the selected unbreakable container of the form designer
        And the user clicks in the selected unbreakable container of the form designer
        # And takes a screenshot
        Examples:
            | Device  |
            | desktop |
