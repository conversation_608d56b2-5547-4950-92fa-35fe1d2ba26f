Feature: 3-1 Page dirty status
    # Tests page modification tracking functionality, verifying that dirty state detection works properly when fields are modified and appropriate callbacks are triggered

    <PERSON><PERSON><PERSON>: As a developer I want the page dirty callbacks to be called when the page becomes dirty
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/PageDirtyState/eyJfaWQiOiIxMiJ9"
        And the user selects the "status" bound text field on the main page
        Then the value of the text field is ""
        And the user selects the "Description" labelled text field on the main page
        And the user writes "test" in the text field
        And the user presses Tab
        And the user selects the "status" bound text field on the main page
        Then the value of the text field is "The page is dirty"

    Scenario: Disable or enable the button field
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Button"
        When the user selects the "Set title dirty" labelled checkbox field on the main page
        And the user clicks in the checkbox field

    Scenario: Set the title to isDirty
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Button"
        And the user selects the "setTitleDirty" labelled checkbox field on the main page
        When the user ticks the checkbox field
        And the user selects the "transient" bound text field on the main page
        Then the value of the text field is "Is dirty"
        And the user selects the "setTitleDirty" labelled checkbox field on the main page
        When the user unticks the checkbox field
        And the user selects the "transient" bound text field on the main page
        Then the value of the text field is "Not dirty"


    Scenario: As a developer I want to make sure that the dirty state is updated in the consumer when I manually update the dirty state of a field
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Dialogs"
        And the user selects the "Path" labelled text field on the main page
        And the user writes "@sage/xtrem-show-case/Button" in the text field
        When the user clicks in the "pageDialogButton" bound button field on the main page
        And the user selects the "setTitleDirty" labelled checkbox field on a modal
        When the user ticks the checkbox field
        And the user selects the "transient" bound text field on a modal
        Then the value of the text field is "Is dirty"
        And the user clicks the Close button of the dialog on the main page
        When the user clicks the "Go Back" button of the Confirm dialog
        And the user selects the "setTitleDirty" labelled checkbox field on a modal
        When the user unticks the checkbox field
        And the user selects the "transient" bound text field on a modal
        Then the value of the text field is "Not dirty"
        When the user clicks the Close button of the dialog on the main page
        Then no dialogs are displayed
