Feature: 1-1 Auto configuration
    # Tests automatic field configuration capabilities, verifying that field titles and properties are correctly set through configuration metadata
    Scenario: As a developer I want to set field titles automatically
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/AutoConfigShowCaseProduct/eyJfaWQiOiIzMDYifQ=="
        Then the "Product 306" titled page is displayed
        When the user selects the "Product" labelled text field on the main page
        Then the text field appears
        When the user selects the "Description" labelled text field on the main page
        Then the text field appears
        When the user selects the "Provider" labelled reference field on the main page
        Then the reference field tunnel link is displayed
        When the user selects the "Tax" labelled numeric field on the main page
        Then the numeric field appears

    Scenario: As a developer I want to override automatically set field titles
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/AutoConfigShowCaseProduct/eyJfaWQiOiIzMDYifQ=="
        Then the "Product 306" titled page is displayed
        When the user selects the "qty" bound numeric field on the main page
        Then the title of the numeric field is "Quantity"

    Scenario: As a developer I want the tunnel links to set automatically
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/AutoConfigShowCaseProduct/eyJfaWQiOiIzMDYifQ=="
        Then the "Product 306" titled page is displayed
        When the user selects the "Provider" labelled reference field on the main page
        Then the reference field tunnel link is displayed
        When the user clicks the reference field tunnel link
        Then an info dialog appears on a full width modal
        When the user waits 2 seconds
        Then the dialog title is "Provider 2" on a full width modal

    Scenario: As a developer I want the max length validation to be set automatically on text fields
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/AutoConfigShowCaseProduct/eyJfaWQiOiIzMDYifQ=="
        Then the "Product 306" titled page is displayed
        When the user selects the "Product" labelled text field on the main page
        And the user writes "very very long text, very very long text, very very long text, very very long text, very very long text, very very long text, very very long text, very very long text, very very long text, very very long text, very very long text, very very long text, very very long text, very very long text, very very long text, very very long text, very very long text, very very long text, very very long text, very very long text, very very long text, very very long text, very very long text, very very long text, very very long text, very very long text, very very long text, very very long text, very very long text, very very long text, very very long text, very very long text, very very long text, very very long text, very very long text, very very long text, very very long text, very very long text, very very long text, very very long text, very very long text, very very long text, very very long text, very very long text, very very long text, very very long text, very very long text, very very long text, very very long text, very very long text, very very long text, very very long text, very very long text, very very long text, very very long text, very very long text, very very long text, very very long text, very very long text, very very long text" in the text field
        And the user blurs the text field
        Then the text field is invalid

    Scenario: As a developer I want the max and min validation to be set automatically on numeric fields
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/AutoConfigShowCaseProduct/eyJfaWQiOiIzMDYifQ=="
        Then the "Product 306" titled page is displayed
        When the user selects the "Tax" labelled numeric field on the main page
        And the user writes "*********" in the numeric field
        And the user blurs the text field
        Then the numeric field is invalid
        And the user writes "10" in the numeric field
        And the user blurs the text field
        Then the numeric field is valid
        And the user writes "-*********" in the numeric field
        And the user blurs the text field
        Then the numeric field is invalid

    Scenario: As a developer I want the enum type to be set automatically on select fields
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/AutoConfigShowCaseProduct/eyJfaWQiOiIzMDYifQ=="
        Then the "Product 306" titled page is displayed
        When the user selects the "Category" labelled select field on the main page
        When the user clicks in the select field
        And the user clears the select field
        Then at least the following list of options is displayed for the select field:"Great | Good | Ok | Not bad"

    Scenario: As a developer I want the navigation panel to be automaticall configured
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/AutoConfigShowCaseProduct"
        Then the "Products" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row 10 of the table field
        Then the value of the "Product" labelled nested text field of the selected row in the table field is "Apricots Fresh"
        Then the value of the "Description" labelled nested text field of the selected row in the table field is "web-enabled"
        Then the value of the "Qty" labelled nested numeric field of the selected row in the table field is "22"
        Then the value of the "Release date" labelled nested date field of the selected row in the table field is "12/28/2020"
        Then the value of the "Amount" labelled nested date field of the selected row in the table field is "117.130"
        Then the value of the "Tax" labelled nested date field of the selected row in the table field is "39.230"
        Then the value of the "Category" labelled nested select field of the selected row in the table field is "Good"

    Scenario: As a developer I want fields bound to non-savable properties to be disabled
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/AutoConfigShowCaseProduct/eyJfaWQiOiIzMDYifQ=="
        Then the "Product 306" titled page is displayed
        When the user selects the "Total" labelled numeric field on the main page
        And the numeric field is disabled

    Scenario: As a developer I want the lookup dialog to be auto configured
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/AutoConfigShowCaseProduct/eyJfaWQiOiIzMDYifQ=="
        Then the "Product 306" titled page is displayed
        When the user selects the "Provider" labelled reference field on the main page
        When the user clicks the lookup button of the reference field
        And the user selects the "provider" bound table field on a modal
        And the user selects the row 5 of the table field
        Then the value of the "Text field" labelled nested text field of the selected row in the table field is "Pai Mei"
        Then the value of the "Integer field" labelled nested numeric field of the selected row in the table field is "4"
        Then the value of the "Date field" labelled nested date field of the selected row in the table field is "07/25/2022"

    Scenario: As a user I want transient reference fields to be automatically configured based on their node property
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Reference"
        Then the "Field - Reference" titled page is displayed
        When the user selects the "autoConfigCountry" bound reference field on the main page
        Then the title of the reference field is "Show case country"
        When the user clicks the lookup button of the reference field
        And the user selects the "autoConfigCountry" bound table field on a modal
        And the user selects the row 5 of the table field
        Then the value of the "Name" labelled nested text field of the selected row in the table field is "Andorra"
        Then the value of the "Code" labelled nested text field of the selected row in the table field is "AD"
        Then the value of the "Phone Country Code" labelled nested numeric field of the selected row in the table field is "376"
        When the user clicks the "Name" labelled nested field of the selected row in the table field
        Then the value of the reference field is "Andorra"
        Then the helper text of the reference field is "AD"
