Feature: 4-1 Table nested field
    # Tests the functionality of nested fields within table components, ensuring proper data interaction, display, and navigation for nested data structures

    # ----------------------- to reactivate when XT-39028 is fixed -----------------------
    #use of the row ID
    @table_scroll
    Scenario: As a user I interact with the data table nested field of the specific row number
        XT-39028

        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-show-case/Table"
        Then the "Field - Table" titled page is displayed
        And the user selects the "field" bound table field on the main page
        When the user selects the row 4 of the table field
        When the user writes "14" in the "Quantity" labelled nested numeric field of the selected row in the table field
        Then the value of the "Quantity" labelled nested numeric field of the selected row in the table field is "14"

        When the user clicks the "Hot" labelled nested field of the selected row in the table field

        Then the "Hot (Switch)" labelled nested switch field of the selected row in the table field is set to "OFF"
        When the user clicks the "Hot (Switch)" labelled nested field of the selected row in the table field
        Then the "Hot (Switch)" labelled nested switch field of the selected row in the table field is set to "ON"

        And the user writes "<PERSON>" in the "Provider" labelled nested reference field of the selected row in the table field
        And the user selects "Ali Express" in the "Provider" labelled nested field of the selected row in the table field
        Then the value of the "Provider" labelled nested reference field of the selected row in the table field is "Ali Express"

        When the user writes "04/25/2023" in the "Date" labelled nested date field of the selected row in the table field
        And the user waits for 3 seconds
        Then the value of the "Date" labelled nested date field of the selected row in the table field is "04/25/2023"

        And the user writes "aker" in the "Email" labelled nested filter select field of the selected row in the table field
        And the user selects "<EMAIL>" in the "Email" labelled nested field of the selected row in the table field
        Then the value of the "Email" labelled nested filter select field of the selected row in the table field is "<EMAIL>"

        When the user clicks the "Category" labelled nested field of the selected row in the table field
        And the user selects "Good" in the "Category" labelled nested field of the selected row in the table field
        Then the value of the "Category" labelled nested select field of the selected row in the table field is "Good"

    # ----------------------- to reactivate when XT-39028 is fixed -----------------------
    #use of the selected row (dynamic)
    @table_scroll
    Scenario: As a user I interact with the data table nested field of the selected row
        XT-39077

        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-show-case/Table"
        Then the "Field - Table" titled page is displayed
        And the user selects the "field" bound table field on the main page

        # #row 2
        And the user selects the row with text "Appetizer - Cheese Bites" in the "Product" labelled column header of the table field
        When the user writes "14" in the "Quantity" labelled nested numeric field of the selected row in the table field
        Then the value of the "Quantity" labelled nested numeric field of the selected row in the table field is "14"

        When the user clicks the "Hot" labelled nested field of the selected row in the table field

        Then the "Hot (Switch)" labelled nested switch field of the selected row in the table field is set to "OFF"
        When the user clicks the "Hot (Switch)" labelled nested field of the selected row in the table field
        Then the "Hot (Switch)" labelled nested switch field of the selected row in the table field is set to "ON"

        And the user writes "Ali" in the "Provider" labelled nested reference field of the selected row in the table field
        And the user selects "Ali Express" in the "Provider" labelled nested field of the selected row in the table field
        Then the value of the "Provider" labelled nested reference field of the selected row in the table field is "Ali Express"

        When the user writes "04/25/2023" in the "Date" labelled nested date field of the selected row in the table field
        And the user waits for 3 seconds
        Then the value of the "Date" labelled nested date field of the selected row in the table field is "04/25/2023"

        And the user writes "aker" in the "Email" labelled nested filter select field of the selected row in the table field
        And the user selects "<EMAIL>" in the "Email" labelled nested field of the selected row in the table field
        Then the value of the "Email" labelled nested filter select field of the selected row in the table field is "<EMAIL>"

        When the user clicks the "Category" labelled nested field of the selected row in the table field
        And the user selects "Good" in the "Category" labelled nested field of the selected row in the table field
        Then the value of the "Category" labelled nested select field of the selected row in the table field is "Good"

    Scenario: As a user I can open the lookup dialog of the data table nested reference field of the specific row number
        XT-39028

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/InlineEdit/eyJfaWQiOiIyIn0="
        Then the "Inline Edit" titled page is displayed
        And the user selects the "field" bound table field on the main page
        When the user selects the row 1 of the table field
        When the user clicks the "Provider" labelled nested field of the selected row in the table field
        And the user opens the lookup dialog in the "Provider" labelled nested reference field of the selected row in the table field
        And the user selects the "provider" bound table field on a modal
        Then the user clicks the "ID" labelled nested field of the selected row in the table field
        And the user selects the "field" bound table field on the main page
        And the value of the "Provider" labelled nested reference field of the selected row in the table field is "Ali Express"

    Scenario: As a user I want to open the lookup dialog of the data table nested reference field of the selected row
        XT-39077

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TableNestedReference/eyJfaWQiOiIxIn0="
        And the user selects the "field" bound table field on the main page

        And the user selects the row with text "27" in the "Quantity" labelled column header of the table field
        When the user clicks the "Provider" labelled nested field of the selected row in the table field
        And the user opens the lookup dialog in the "Provider" labelled nested reference field of the selected row in the table field
        # And takes a screenshot

        And the user selects the "product" bound table field on a modal
        And the user selects the row with text "Veal Inside - Provimi" in the "Product" labelled column header of the table field
        And the user clicks the "Product" labelled nested field of the selected row in the table field

        And the user selects the "field" bound table field on the main page
        And the user selects the row with text "27" in the "Quantity" labelled column header of the table field
        Then the value of the "Provider" labelled nested reference field of the selected row in the table field is "Ali Express"
    # And takes a screenshot

    Scenario: As a user I can open the lookup dialog of the data table nested reference field of the floating row
        XT-64336

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TableNestedReference/eyJfaWQiOiIxIn0="
        Then the "Field - Table (nested reference)" titled page is displayed
        And the user selects the "field" bound table field on the main page
        And the user selects the floating row of the table field

        When the user clicks the "Provider" labelled nested field of the selected row in the table field
        And the user opens the lookup dialog in the "Provider" labelled nested reference field of the selected row in the table field
        And the user selects the "product" bound table field on a modal
        And the user selects the row 1 of the table field
        Then the user clicks the "Provider" labelled nested field of the selected row in the table field
        And the user selects the "field" bound table field on the main page
        And the user selects the floating row of the table field
        And the value of the "Provider" labelled nested reference field of the selected row in the table field is "Ali Express"

    Scenario: As a user I can click the tunnel link of the data table nested reference field of the specific row number
        XT-70489
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseProvider/eyJfaWQiOiIxIn0="
        Then the "Provider 1" titled page is displayed
        And the user selects the "Products" labelled table field on the main page
        When the user selects the row 1 of the table field
        When the user clicks the "City" labelled nested field of the selected row in the table field
        When the user clicks on the tunnel link in the "City" labelled nested reference field of the selected row in the table field
        Then an info dialog appears on a full width modal
        When the user waits 2 seconds
        Then the dialog title is "Provider addresses" on a full width modal

    #use of the floating row
    Scenario: As a user I want to be able to trigger the onRowAdded event
        XT-32470
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-show-case/InlineEdit/eyJfaWQiOiIyIn0="
        When the user selects the "field" bound table field on the main page
        And the user selects the floating row of the table field
        And the user writes "bread" in the "product" bound nested text field of the selected row in the table field
        And the user selects the "field" bound table field on the main page
        And the user writes "10" in the "netPrice" bound nested numeric field of the selected row in the table field
        And the user selects the "field" bound table field on the main page
        And the user selects the floating row of the table field
        And the user writes "Amazon" in the "provider__textField" bound nested reference field of the selected row in the table field
        And the user presses Enter
        When the user selects the "field" bound table field on the main page
        And the user selects the floating row of the table field
        And the user clicks the "product" bound nested field of the selected row in the table field
        And the user presses Control+Enter
        Then a toast containing text "Row added successfully - id: -1, _id: -1, amount: 0, category: null, description: , hotProduct: false, listPrice: 0, netPrice: 10, product: bread, provider: Amazon, qty: 1, st: 1, tax: 0" is displayed

    Scenario: As an ATP XTreeM user I can interact with nested switch in table
        XT-43513
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Table"
        Then the "Field - Table" titled page is displayed
        And the user selects the "field" bound table field on the main page
        And the user selects the row 3 of the table field
        Then the "Hot (Switch)" labelled nested switch field of the selected row in the table field is set to "OFF"
        When the user clicks the "Hot (Switch)" labelled nested field of the selected row in the table field
        Then the "Hot (Switch)" labelled nested switch field of the selected row in the table field is set to "ON"

    # This scenario is commented out because it cannot work with the current table implementation.
    # There are two columns labelled "Product" with the same binding name, making it impossible to distinguish between them.

    # Scenario: As a user, I want to change the value of a reference field in a read only table.
    #     Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Table/eyJfaWQiOiIyIn0="
    #     Then the "Field - Table" titled page is displayed
    #     When the user selects the "tableInReadOnlyMode" labelled table field on the main page
    #     And the user selects the row with text "Appetiser - Bought" in the "Product" labelled column header of the table field
    #     And the user clicks the "_id" bound nested field of the selected row in the table field
    #     And the user presses Tab
    #     And the user presses Tab
    #     And the user presses Tab
    #     And the user presses Tab
    #     And the user presses Tab
    #     And the user presses Tab
    #     And the user presses Tab
    #     And the user presses Tab
    #     And the user presses Tab
    #     And the user presses Tab
    #     And the user presses Tab
    #     And the user presses Tab
    #     And the user presses Tab
    #     And the user presses Tab
    #     And the user presses Tab
    #     And the user presses Tab
    #     And the user clicks the "provider" labelled nested field of the selected row in the table field
    #     Then the tunnel link in the "provider" labelled nested reference field of the selected row in the table field is displayed
    #     When the user clicks on the tunnel link in the "provider" labelled nested reference field of the selected row in the table field
    #     Then an info dialog appears on a full width modal
    #     And the user waits 2 seconds
    #     And the dialog title is "Provider 2" on a full width modal
    #     When the user selects the "Name" labelled text field on a full width modal
    #     And the user writes "Amazon!" in the text field
    #     And the user presses Enter
    #     And the user clicks the "Save" labelled business action button on a full width modal
    #     Then the "Field - Table" titled page is displayed
    #     When the user selects the "tableInReadOnlyMode" labelled table field on the main page
    #     And the user selects the row with text "Appetiser - Bought" in the "Product" labelled column header of the table field
    #     And the user clicks the "_id" bound nested field of the selected row in the table field
    #     And the user presses Tab
    #     And the user presses Tab
    #     And the user presses Tab
    #     And the user presses Tab
    #     And the user presses Tab
    #     And the user presses Tab
    #     And the user presses Tab
    #     And the user presses Tab
    #     And the user presses Tab
    #     And the user presses Tab
    #     And the user presses Tab
    #     And the user presses Tab
    #     And the user presses Tab
    #     And the user presses Tab
    #     And the user presses Tab
    #     And the user presses Tab
    #     And the user clicks the "provider" labelled nested field of the selected row in the table field
    #     Then the tunnel link in the "provider" labelled nested reference field of the selected row in the table field is displayed
    #     When the user clicks on the tunnel link in the "provider" labelled nested reference field of the selected row in the table field
    #     Then an info dialog appears on a full width modal
    #     And the user waits 2 seconds
    #     And the dialog title is "Provider 2" on a full width modal
    #     When the user selects the "Name" labelled text field on a full width modal
    #     And the user writes "Amazon" in the text field
    #     And the user presses Enter
    #     And the user clicks the "Save" labelled business action button on a full width modal
    #     Then the "Field - Table" titled page is displayed

    Scenario: As a user, I would like to toggle the floating filters and still be able to filter the data in the nested reference field.
        XT-79087
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TableNestedReference"
        Then the "Field - Table (nested reference)" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        Then the group by option in the header menu of the "Purchase date" labelled column of the table field is displayed
        Then the user clicks the group by month option in the header menu of the "Purchase date" labelled column of the table field
        And the user selects the row 1 of the table field
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "August 2019 (2)"
        And the user clicks the "Hide table filters" labelled button of the table field
        Then the user clicks the "Show table filters" labelled button of the table field
        Then the user clicks the "Hide table filters" labelled button of the table field
        Then the user clicks the "Show table filters" labelled button of the table field
        When the user filters the "purchaseDate" bound column in the table field with value "08/31/2019"
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "August 2019 (2)"

    # TODO: breaks with ag grid update XT-62332
    # Scenario: As an ATP XTreeM user I can select the option of the nested field in the data table floating row
    #     XT-55302
    #     Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TableWithSidebar/eyJfaWQiOiIyIn0="
    #     Then the "Field - Table - With sidebar" titled page is displayed
    #     And the user selects the "field" bound table field on the main page
    #     And the user adds a new table row to the table field
    #     And the user selects the floating row of the table field
    #     And the user writes "Apple Juice" in the "Product" labelled nested text field of the selected row in the table field
    #     And the user writes "10" in the "Quantity" labelled nested text field of the selected row in the table field
    #     And the user writes "Ama" in the "Provider" labelled nested reference field of the selected row in the table field
    #     And the user selects "Amazon" in the "Provider" labelled nested field of the selected row in the table field
    #     And the user writes "02/20/2023" in the "Date" labelled nested date field of the selected row in the table field
    #     And the user writes "acox2t" in the "Email" labelled nested filter select field of the selected row in the table field
    #     And the user selects "<EMAIL>" in the "Email" labelled nested field of the selected row in the table field
    #     And the user clicks the "Category" labelled nested field of the selected row in the table field
    #     And the user selects "Good" in the "Category" labelled nested field of the selected row in the table field


    # TODO: Nested selector for nested dropdown list component no longer exists after refactor. Dropdown list now uses the same internal components
    #       as the nested select.
    # Scenario: As an ATP XTreeM user I can interact with nested drop-down list in grid
    #     Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/DropdownList"
    #     When selects the "Nested DropdownList" labelled navigation anchor on the main page
    #     Then the "Nested DropdownList" labelled navigation anchor is selected
    #     And the user clicks on the "Category (Dropdown)" labelled nested drop-down list field of row 2 of the "Table with nested DropdownList field" labelled table field in the main page
    #     And selects "Not bad" in the "Category (Dropdown)" labelled nested drop-down list field of row 2 in the "Table with nested DropdownList field" labelled table field in the main page
    #     Then the value of the "Category (Dropdown)" labelled nested drop-down list field of row 2 in the "Table with nested DropdownList field" labelled table field is "Not bad"

    # The following scenario is blocked by the fix of XT-50725
    # Scenario: As a user I want to check that a field i a table is readonly
    # Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseProvider/eyJfaWQiOiIyIn0="
    # Then the "Provider 2" titled page is displayed
    # When the user selects the "products" bound table field on the main page
    # And the user selects row with text "Organized" in column with header "Description"
    # And the user waits 10 seconds
    # Then the "description" labelled nested text field of row 3 in the table field is editable


    #----------------Dynamic date generation---------------------------
    # Following examples are in en-US languages
    @generic_date
    Scenario Outline: As a XTreeM user I can set and read the generated date with <Value> of the selected row
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Table"
        And the "Field - Table" titled page is displayed
        And the user selects the "field" bound table field on the main page
        And the user selects the row with text "Banana - Green" in the "Product" labelled column header of the table field
        And the user writes a generated date with value <Value> in the "Date" labelled nested date field of the selected row in the table field
        And the user presses Enter
        # And takes a screenshot
        And the value of the "Date" labelled nested date field of the selected row in the table field is a generated date with value <Value>
        # And takes a screenshot
        Examples:
            | Value        |
            | "T"          |
            | "T+1"        |
            | "T-3"        |
            | "L"          |
            | "M/T/Y"      |
            | "01/30/Y"    |
            | "12/L/Y"     |
            | "02/L/Y"     |
            | "02/L/(Y+1)" |


    # @generic_date
    # Scenario Outline: As a XTreeM user I can set and read the generated date with <Value> of a given row
    #     Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-show-case/Table"
    #     And the "Field - Table" titled page is displayed
    #     And the user selects the "field" bound table field on the main page
    #     And the user writes a generated date with value <Value> in the "Date" labelled nested date field of row 1 in the table field
    #     And the user presses Enter
    #     # And takes a screenshot
    #     And the value of the "Date" labelled nested date field of row 1 in the table field is a generated date with value <Value>
    #     # And takes a screenshot
    #     Examples:
    #         | Value        |
    #         | "T"          |
    #         | "T+1"        |
    #         | "T-3"        |
    #         | "L"          |
    #         | "M/T/Y"      |
    #         | "01/30/Y"    |
    #         | "12/L/Y"     |
    #         | "02/L/Y"     |
    #         | "02/L/(Y+1)" |

    @generic_date
    Scenario Outline: As a XTreeM user I can set and read a generated date in language <Language> of the selected row
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Table"
        And the user switches language to <Language>
        And the user refreshes the screen
        And the "Field - Table" titled page is displayed
        And the user selects the "field" bound table field on the main page

        And the user selects the row with text "Banana - Green" in the "Product" labelled column header of the table field
        And the user writes a generated date with value <Value1> in the "Date" labelled nested date field of the selected row in the table field
        And the user presses Enter
        # And takes a screenshot
        And the value of the "Date" labelled nested date field of the selected row in the table field is a generated date with value <Value1>

        Examples:
            | Language     | Value1  |
            | "English US" | "M/T/Y" |
            | "English GB" | "T/M/Y" |
            | "Spanish"    | "T/M/Y" |
            | "French"     | "T/M/Y" |
            | "German"     | "T.M.Y" |
            | "Polish"     | "T.M.Y" |
            | "Portuguese" | "T/M/Y" |
            | "Chinese"    | "Y/M/T" |

    @hardcoded_date
    Scenario Outline: As a XTreeM user I can set and read a harcoded date <Value> in language <Language> of the selected row
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Table"
        And the user switches language to <Language>
        And the user refreshes the screen
        And the "Field - Table" titled page is displayed
        And the user selects the "field" bound table field on the main page
        And the user selects the row with text "Banana - Green" in the "Product" labelled column header of the table field
        And the user writes <Value> in the "Date" labelled nested date field of the selected row in the table field
        And the user presses Enter
        # And takes a screenshot
        And the value of the "Date" labelled nested date field of the selected row in the table field is <Value>
        # And takes a screenshot
        Examples:
            | Language     | Value        |
            | "English US" | "10/12/2020" |
    # | "English GB" | "12/10/2020" |
    # | "Spanish"    | "12/10/2020" |
    # | "French"     | "12/10/2020" |
    # | "German"     | "12.10.2020" |
    # | "Polish"     | "12.10.2020" |
    # | "Portuguese" | "12/10/2020" |
    # | "Chinese"    | "2020/10/12" |


    @generic_date
    Scenario Outline: As a XTreeM user I can calculate the next specific date from today in language <Language> for the selected row
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Table"
        And the user switches language to <Language>
        And the user refreshes the screen
        And the "Field - Table" titled page is displayed
        And the user selects the "field" bound table field on the main page

        And the user selects the row with text "Banana - Green" in the "Product" labelled column header of the table field
        And the user writes a generated date with value <Value1> from today in the "Date" labelled nested date field of the selected row in the table field
        And the user presses Enter
        # And takes a screenshot
        And the value of the "Date" labelled nested date field of the selected row in the table field is a generated date from today with value <Value1>

        Examples:
            | Language     | Value1             |
            | "English US" | "1st Tuesday"      |
            | "English GB" | "1st Tuesday + 2"  |
            | "Spanish"    | "2nd Monday"       |
            | "French"     | "2nd Monday + 3"   |
            | "German"     | "3rd Friday"       |
            | "Polish"     | "3rd Friday + 1"   |
            | "Portuguese" | "4th Thursday"     |
            | "Chinese"    | "4th Thursday + 1" |


    @generic_date
    Scenario Outline: As a XTreeM user I can calculate the next specific date from today in language <Language> for a given row
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Table"
        And the user switches language to <Language>
        And the user refreshes the screen
        And the "Field - Table" titled page is displayed
        And the user selects the "field" bound table field on the main page
        And the user selects the row 1 of the table field
        And the user writes a generated date with value <Value1> from today in the "Date" labelled nested date field of the selected row in the table field
        And the user presses Enter
        # And takes a screenshot
        And the value of the "Date" labelled nested date field of the selected row in the table field is a generated date from today with value <Value1>
        Examples:
            | Language     | Value1             |
            | "English US" | "1st Tuesday + 2 " |



    #-------------------------data table Scroll regression tests---------------------------
    # @table_scroll
    # Scenario: As a user I can scroll and edit fields from left to right and vis versa of the specific row id
    #     XT-56897

    #     Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Table"
    #     Then the "Field - Table" titled page is displayed
    #     And the user selects the "field" bound table field on the main page

    #     When the user writes "14" in the "Quantity" labelled nested numeric field of row 4 in the table field
    #     Then the value of the "Quantity" labelled nested numeric field of row 4 in the table field is "14"

    #     When the user clicks the "Category" labelled nested field of row 4 in the table field
    #     And the user selects "Good" in the "Category" labelled nested field of row 4 in the table field
    #     Then the value of the "Category" labelled nested select field of row 4 in the table field is "Good"

    #     When the user writes "15" in the "Quantity" labelled nested numeric field of row 5 in the table field
    #     Then the value of the "Quantity" labelled nested numeric field of row 5 in the table field is "15"

    #     When the user clicks the "Category" labelled nested field of row 4 in the table field
    #     And the user selects "Good" in the "Category" labelled nested field of row 5 in the table field
    #     Then the value of the "Category" labelled nested select field of row 5 in the table field is "Good"

    @table_scroll
    Scenario: As a user I can scroll and edit fields from left to right and vis versa of the selected row
        XT-56897

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Table"
        Then the "Field - Table" titled page is displayed
        And the user selects the "field" bound table field on the main page

        And the user selects the row with text "Appetizer - Cheese Bites" in the "Product" labelled column header of the table field
        When the user writes "14" in the "Quantity" labelled nested numeric field of the selected row in the table field
        Then the value of the "Quantity" labelled nested numeric field of the selected row in the table field is "14"

        When the user clicks the "Category" labelled nested field of the selected row in the table field
        And the user selects "Good" in the "Category" labelled nested field of the selected row in the table field
        Then the value of the "Category" labelled nested select field of the selected row in the table field is "Good"

        And the user selects the row with text "Apple - Northern Spy" in the "Product" labelled column header of the table field
        When the user writes "14" in the "Quantity" labelled nested numeric field of the selected row in the table field
        Then the value of the "Quantity" labelled nested numeric field of the selected row in the table field is "14"

        When the user clicks the "Category" labelled nested field of the selected row in the table field
        And the user selects "Good" in the "Category" labelled nested field of the selected row in the table field
        Then the value of the "Category" labelled nested select field of the selected row in the table field is "Good"

    @table_scroll
    Scenario: As a user I can scroll and edit fields from left to right and vis versa of the floating row
        XT-56897
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TableWithSidebar/eyJfaWQiOiIyIn0="
        Then the "Field - Table - With sidebar" titled page is displayed
        And the user selects the "field" bound table field on the main page
        And the user adds a new table row to the table field
        And the user selects the floating row of the table field
        And the user writes "Apple Juice" in the "Product" labelled nested text field of the selected row in the table field

        And the user clicks the "Category" labelled nested field of the selected row in the table field
        And the user selects "Good" in the "Category" labelled nested field of the selected row in the table field
        Then the value of the "Category" labelled nested select field of the selected row in the table field is "Good"
        And the user presses Enter

        And the user adds a new table row to the table field
        And the user selects the floating row of the table field
        And the user writes "Citron Juice" in the "Product" labelled nested text field of the selected row in the table field

        And the user clicks the "Category" labelled nested field of the selected row in the table field
        And the user selects "Good" in the "Category" labelled nested field of the selected row in the table field
        Then the value of the "Category" labelled nested select field of the selected row in the table field is "Good"
        And the user presses Enter

    @table_scroll
    Scenario: As a user I can scroll and edit fields from left to right and vis versa of a specific selected row
        XT-94088
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TableWithSidebar/eyJfaWQiOiIyIn0="
        Then the "Field - Table - With sidebar" titled page is displayed
        And the user selects the "field" bound table field on the main page
        And the user selects the row 3 of the table field
        And the user writes "Pink Apple Juice" in the "Product" labelled nested text field of the selected row in the table field
        And the user clicks the "Category" labelled nested field of the selected row in the table field
        And the user selects "Good" in the "Category" labelled nested field of the selected row in the table field
        Then the value of the "Category" labelled nested select field of the selected row in the table field is "Good"
        # And the user presses Enter
        And the user selects the row 4 of the table field
        And the user writes "Yellow Lemon Juice" in the "Product" labelled nested text field of the selected row in the table field
        And the user clicks the "Category" labelled nested field of the selected row in the table field
        And the user selects "Good" in the "Category" labelled nested field of the selected row in the table field
        Then the value of the "Category" labelled nested select field of the selected row in the table field is "Good"

    Scenario: As a user, I can set date time or no start/end date time for the date time range field within the table
        XT-78015
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TableWithSidebar/eyJfaWQiOiIyIn0="
        Then the "Field - Table - With sidebar" titled page is displayed

        # check set date time range field with start and end date
        When the user selects the "field" bound table field on the main page
        And the user selects the row with text "Apple - Macintosh" in the "Product" labelled column header of the table field
        And the user selects the "Manufacterd within" labelled nested date-time-range field of the selected row in the table field
        And the user selects the "December" month of start nested date-time-range field of the selected row in the table field
        And the user selects the "2023" year of start nested date-time-range field of the selected row in the table field
        And the user selects the "17" day of start nested date-time-range field of the selected row in the table field
        And the user writes "12.30" in time field of the start nested date-time-range field of the selected row in the table field
        And the value of the start nested date-time-range field of the selected row in the table field is "12/17/2023 - 12:30 AM"
        And the user clicks the "PM" toggle button of the start nested date-time-range field of the selected row in the table field
        And the value of the start nested date-time-range field of the selected row in the table field is "12/17/2023 - 12:30 PM"
        And the user selects the "January" month of end nested date-time-range field of the selected row in the table field
        And the user selects the "2024" year of end nested date-time-range field of the selected row in the table field
        And the user selects the "17" day of end nested date-time-range field of the selected row in the table field
        And the user writes "12.30" in time field of the end nested date-time-range field of the selected row in the table field
        And the value of the end nested date-time-range field of the selected row in the table field is "01/17/2024 - 12:30 AM"
        And the user clicks the "PM" toggle button of the end nested date-time-range field of the selected row in the table field
        Then the value of the end nested date-time-range field of the selected row in the table field is "01/17/2024 - 12:30 PM"
        When the user leaves the focus from the end nested date-time-range field of the selected row in the table field
        And the user clicks the "Save" labelled business action button on the main page
        And the user selects the row with text "Apple - Macintosh" in the "Product" labelled column header of the table field
        And the user selects the "Manufacterd within" labelled nested date-time-range field of the selected row in the table field
        Then the value of the start and end nested date-time-range field of the selected row in the table field is "12/17/2023 12:30 PM - 01/17/2024 12:30 PM"

        # check set date time range field with No start and No end date
        When the user ticks the "No start date" checkbox of the start nested date-time-range field of the selected row in the table field
        Then the value of the start nested date-time-range field of the selected row in the table field is ""
        When the user ticks the "No end date" checkbox of the end nested date-time-range field of the selected row in the table field
        Then the value of the end nested date-time-range field of the selected row in the table field is ""
        When the user leaves the focus from the end nested date-time-range field of the selected row in the table field
        And the user clicks the "Save" labelled business action button on the main page
        And the user selects the row with text "Apple - Macintosh" in the "Product" labelled column header of the table field
        And the user selects the "Manufacterd within" labelled nested date-time-range field of the selected row in the table field
        Then the value of the start and end nested date-time-range field of the selected row in the table field is ""


    Scenario: As a user, I can verify the time zone value
        XT-78015
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TableWithSidebar/eyJfaWQiOiIyIn0="
        Then the "Field - Table - With sidebar" titled page is displayed
        And the user selects the "field" bound table field on the main page
        And the user selects the row with text "Apple - Macintosh" in the "Product" labelled column header of the table field
        And the user selects the "Manufacterd within" labelled nested date-time-range field of the selected row in the table field
        And the user selects the "December" month of start nested date-time-range field of the selected row in the table field
        And the user selects the "2023" year of start nested date-time-range field of the selected row in the table field
        And the user selects the "17" day of start nested date-time-range field of the selected row in the table field
        And the user writes "12.30" in time field of the start nested date-time-range field of the selected row in the table field
        And the value of the start nested date-time-range field of the selected row in the table field is "12/17/2023 - 12:30 AM"
        And the user clicks the time zone field in the start nested date-time-range field of the selected row in the table field
        And the time-zone value in the start nested date-time-range field of the selected row in the table field is "GMT"
