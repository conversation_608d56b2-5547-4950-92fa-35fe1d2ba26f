Feature: 1-1 Content table
        # Tests the dynamic content table creation functionality, allowing users to define table columns, properties, and display options at runtime

        Scenario: As a user, I want to create a table using the content table field
                Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ContentTable"
                Then the "Content Table" titled page is displayed
                When the user selects the "node" labelled reference field on the main page
                And the user writes "Show case provider" in the reference field
                And the user presses Enter
                And the user selects the "field" bound content table field on the main page
                And the user clicks the "Add column" button of the content table field
                Then the "content-property" dropdown field of row "1" in the content table field is displayed
                And the "content-property" dropdown field of row "1" in the content table field is enabled
                And the "content-title" text field of row "1" in the content table field is displayed
                And the "content-title" text field of row "1" in the content table field is disabled
                And the "content-group" dropdown field of row "1" in the content table field is displayed
                And the "content-group" dropdown field of row "1" in the content table field is enabled
                And the "add" action button of row "1" in the content table field is displayed
                And the "remove" action button of row "1" in the content table field is displayed
                And the value of the "content-group" dropdown field of row "1" in the content table field is "Group 1"
                When the user writes "Text field" in the "content-property" dropdown field of row "1" in the content table field
                Then the "content-title" text field of row "1" in the content table field is enabled
                And the value of the "content-title" text field of row "1" in the content table field is "Text field"
                And the "content-operation" dropdown field of row "1" in the content table field is enabled
                When the user writes "Distinct count" in the "content-operation" dropdown field of row "1" in the content table field
                Then the value of the "content-sorting" dropdown field of row "1" in the content table field is "Ascending"
                When the user clicks the "add" action button of row "1" in the content table field
                Then the "content-property" dropdown field of row "2" in the content table field is displayed
                And the "content-property" dropdown field of row "2" in the content table field is enabled
                And the "content-title" text field of row "2" in the content table field is displayed
                And the "content-title" text field of row "2" in the content table field is disabled
                And the "content-group" dropdown field of row "2" in the content table field is displayed
                And the "content-group" dropdown field of row "2" in the content table field is enabled
                And the value of the "content-group" dropdown field of row "2" in the content table field is "Group 1"
                When the user writes "_id" in the "content-property" dropdown field of row "2" in the content table field
                Then the value of the "content-title" text field of row "2" in the content table field is "_id"
                When the user writes "Distinct count" in the "content-operation" dropdown field of row "2" in the content table field
                And the user clicks the "add" action button of row "2" in the content table field
                Then the value of the "content-group" dropdown field of row "3" in the content table field is "Group 1"
                When the user writes "Integer field" in the "content-property" dropdown field of row "3" in the content table field
                And the user writes "Minimum" in the "content-operation" dropdown field of row "3" in the content table field
                And the user clicks the "add" action button of row "3" in the content table field
                Then the value of the "content-group" dropdown field of row "4" in the content table field is "Group 1"
                When the user writes "Additional info" in the "content-property" dropdown field of row "4" in the content table field
                And the user clicks the "add" action button of row "4" in the content table field
                And the user writes "Min quantity" in the "content-property" dropdown field of row "5" in the content table field
                And the user writes "Sum" in the "content-operation" dropdown field of row "5" in the content table field
                And the user clicks the "add" action button of row "5" in the content table field
                And the user writes "Date field" in the "content-property" dropdown field of row "6" in the content table field
                And the user writes "Group 2" in the "content-group" dropdown field of row "6" in the content table field
                And the user writes "Minimum" in the "content-operation" dropdown field of row "6" in the content table field
                Then the value of the "content-sorting" dropdown field of row "6" in the content table field is "Ascending"
