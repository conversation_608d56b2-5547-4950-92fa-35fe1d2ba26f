Feature: 5 Y dashboard widget table creation
    # Tests the creation process of table widgets in dashboards, verifying data source configuration, column selection, and formatting options

    @ClearDashboards
    Scenario: As a user I want to create a widget using the create button in the Dashboard Editor chosing the Table format
        Given the user opens the application on a desktop
        And the dashboard page is displayed
        Then the "Create a dashboard to get started." subtitled empty dashboard is displayed
        When the user clicks the create button on the dashboard
        Then the dashboard creation dialog is displayed
        When the user selects the template with title "Showcase dashboard" in the dashboard creation dialog
        And the user clicks the "next" button in the dashboard creation dialog
        Then the "Showcase dashboard" titled dashboard in the dashboard editor is displayed

        # Widget selection step
        When the user clicks the "createAWidget" labelled button in the dashboard editor navigation panel
        Then the "New widget" titled widget editor dialog is displayed
        And the value of the step title of the widget editor dialog is "1. Select a widget to get started"
        And the "cancel" button in the widget editor dialog is enabled
        And the "next" button in the widget editor dialog is disabled
        When the user writes "Table Widget" in the "basic-title" text field in the widget editor dialog
        And the user presses Enter
        And the user writes "My demo category" in the "basic-category" dropdown field in the widget editor dialog
        And the user presses Enter
        And the user writes "Show Case Provider" in the "basic-node" dropdown field in the widget editor dialog
        And the user presses Enter
        And the user selects the "TABLE" widget card in the widget editor dialog
        Then the "TABLE" widget card in the widget editor dialog is selected
        And the "next" button in the widget editor dialog is enabled
        When the user clicks the "next" button in the widget editor dialog
        Then the value of the step title of the widget editor dialog is "2. Select the data to add to your widget"

        # Data selection step
        When the user clicks the "next" button in the widget editor dialog
        Then the value of the step title of the widget editor dialog is "2. Select the data to add to your widget"
        And the "next" button in the widget editor dialog is disabled
        When the user searches for "Text field" in the widget editor dialog
        Then the "Text field" tree-view element in the widget editor dialog is displayed
        And the "Integer field" tree-view element in the widget editor dialog is hidden
        And the "Date field" tree-view element in the widget editor dialog is hidden
        And the "Boolean field" tree-view element in the widget editor dialog is hidden
        And the "Min quantity" tree-view element in the widget editor dialog is hidden
        When the user selects the "Text field" tree-view element in the widget editor dialog
        Then the "next" button in the widget editor dialog is enabled
        When the user clears the search field in the widget editor dialog
        And the "Integer field" tree-view element in the widget editor dialog is displayed
        And the "Date field" tree-view element in the widget editor dialog is displayed
        And the "Boolean field" tree-view element in the widget editor dialog is displayed
        And the "Min quantity" tree-view element in the widget editor dialog is displayed
        When the user selects the "Integer field" tree-view element in the widget editor dialog
        And the user selects the "Date field" tree-view element in the widget editor dialog
        And the user selects the "Boolean field" tree-view element in the widget editor dialog
        And the user selects the "Min quantity" tree-view element in the widget editor dialog


        # Content selection step
        And the user clicks the "next" button in the widget editor dialog
        Then the value of the step title of the widget editor dialog is "3. Add your content"
        Then the "Add column" table button in the widget editor dialog is displayed
        When the user clicks the "Add column" table button in the widget editor dialog
        Then the "content-property" dropdown field of row "1" in the widget editor dialog is enabled
        When the user writes "Text field" in the "content-property" dropdown field of row "1" in the widget editor dialog
        And the user presses Enter
        Then the value of the "content-title" text field of row "1" in the widget editor dialog is "Text field"
        And the value of the "content-presentation" paragraph field of row "1" in the widget editor dialog is "Text"
        When the user clicks the "add" action button of row "1" in the widget editor dialog
        Then the "content-property" dropdown field of row "2" in the widget editor dialog is enabled
        When the user writes "Date field" in the "content-property" dropdown field of row "2" in the widget editor dialog
        And the user presses Enter
        Then the value of the "content-title" text field of row "2" in the widget editor dialog is "Date field"
        And the value of the "content-presentation" paragraph field of row "2" in the widget editor dialog is "Date"
        When the user clicks the "add" action button of row "2" in the widget editor dialog
        Then the "content-property" dropdown field of row "3" in the widget editor dialog is enabled
        When the user writes "Integer field" in the "content-property" dropdown field of row "3" in the widget editor dialog
        And the user presses Enter
        Then the value of the "content-title" text field of row "3" in the widget editor dialog is "Integer field"
        When the user writes "Numeric" in the "content-presentation" text field of row "3" in the widget editor dialog
        And the user presses Enter
        Then the "content-divisor" text field of row "3" in the widget editor dialog is enabled
        And the user clicks the "add" action button of row "3" in the widget editor dialog
        Then the "content-property" dropdown field of row "4" in the widget editor dialog is enabled
        And the user writes "Min quantity" in the "content-property" dropdown field of row "4" in the widget editor dialog
        Then the value of the "content-title" text field of row "4" in the widget editor dialog is "Min quantity"
        And the "content-presentation" dropdown field of row "4" in the widget editor dialog is enabled
        When the user writes "Progress" in the "content-presentation" dropdown field of row "4" in the widget editor dialog
        And the user presses Enter
        And the user clicks the "next" button in the widget editor dialog

        # Filter selection dialog
        Then the value of the step title of the widget editor dialog is "4. Add your filters"
        And the "Add filter" table button in the widget editor dialog is displayed
        When the user clicks the "Add filter" table button in the widget editor dialog
        Then the "filter-property" dropdown field of row "1" in the widget editor dialog is enabled
        When the user writes "Text field" in the "filter-property" dropdown field of row "1" in the widget editor dialog
        And the user presses Enter
        Then the "filter-type" dropdown field of row "1" in the widget editor dialog is enabled
        When the user writes "Contains" in the "filter-type" dropdown field of row "1" in the widget editor dialog
        And the user presses Enter
        Then the "filter-value" text field of row "1" in the widget editor dialog is enabled
        And the user writes "a" in the "filter-value" text field of row "1" in the widget editor dialog
        And the user presses Enter
        And the user clicks the "add" action button of row "1" in the widget editor dialog
        Then the "filter-property" dropdown field of row "2" in the widget editor dialog is enabled
        When the user writes "Integer field" in the "filter-property" dropdown field of row "2" in the widget editor dialog
        And the user presses Enter
        Then the "filter-type" dropdown field of row "2" in the widget editor dialog is enabled
        When the user writes "Between" in the "filter-type" dropdown field of row "2" in the widget editor dialog
        And the user presses Enter
        Then the "filter-value-min" text field of row "2" in the widget editor dialog is enabled
        And the "filter-value-max" text field of row "2" in the widget editor dialog is enabled
        When the user writes "10" in the "filter-value-min" text field of row "2" in the widget editor dialog
        And the user presses Enter
        Then the "Invalid integer" validation error of row "2" in the widget editor dialog is displayed
        When the user writes "1" in the "filter-value-max" text field of row "2" in the widget editor dialog
        And the user presses Enter
        Then the "Invalid range" validation error of row "2" in the widget editor dialog is displayed
        When the user writes "1" in the "filter-value-min" text field of row "2" in the widget editor dialog
        And the user presses Enter
        And the user writes "4" in the "filter-value-max" text field of row "2" in the widget editor dialog
        And the user presses Enter
        And the user clicks the "next" button in the widget editor dialog

        # Sorting selection dialog
        Then the value of the step title of the widget editor dialog is "5. Define sorting"
        And the "Add a sort condition" table button in the widget editor dialog is displayed
        When the user clicks the "Add a sort condition" table button in the widget editor dialog
        Then the "sorting-property" dropdown field of row "1" in the widget editor dialog is enabled
        And the "sorting-order" dropdown field of row "1" in the widget editor dialog is enabled
        When the user writes "Text field" in the "sorting-property" dropdown field of row "1" in the widget editor dialog
        And the user presses Enter
        And the user writes "Ascending" in the "sorting-order" dropdown field of row "1" in the widget editor dialog
        And the user presses Enter
        And the user clicks the "add" action button of row "1" in the widget editor dialog
        Then the "sorting-property" dropdown field of row "2" in the widget editor dialog is enabled
        And the "sorting-order" dropdown field of row "2" in the widget editor dialog is enabled
        When the user clicks the "remove" action button of row "2" in the widget editor dialog
        Then the "sorting-property" dropdown field of row "2" in the widget editor dialog is hidden
        And the "sorting-order" dropdown field of row "2" in the widget editor dialog is hidden
        When the user clicks the "next" button in the widget editor dialog

        # Layout creation step
        Then the value of the step title of the widget editor dialog is "6. Create your layout"
        When the user ticks the "See all" checkbox field in the widget editor dialog
        Then the "layout-title-seeAllAction" text field in the widget editor dialog is enabled
        And the "layout-page-seeAllAction" dropdown field in the widget editor dialog is enabled
        When the user unticks the "See all" checkbox field in the widget editor dialog
        Then the "layout-title-seeAllAction" text field in the widget editor dialog is disabled
        And the "layout-page-seeAllAction" dropdown field in the widget editor dialog is disabled
        When the user ticks the "Create" checkbox field in the widget editor dialog
        Then the "layout-title-createAction" text field in the widget editor dialog is enabled
        And the "layout-page-createAction" dropdown field in the widget editor dialog is enabled
        When the user unticks the "Create" checkbox field in the widget editor dialog
        Then the "layout-title-createAction" text field in the widget editor dialog is disabled
        And the "layout-page-createAction" dropdown field in the widget editor dialog is disabled
        When the user ticks the "See all" checkbox field in the widget editor dialog
        Then the "See all" preview button in the widget editor dialog is displayed
        When the user unticks the "See all" checkbox field in the widget editor dialog
        Then the "See all" preview button in the widget editor dialog is hidden
        When the user ticks the "Create" checkbox field in the widget editor dialog
        Then the "Create" preview button in the widget editor dialog is displayed
        When the user unticks the "Create" checkbox field in the widget editor dialog
        Then the "Create" preview button in the widget editor dialog is hidden
        When the user ticks the "See all" checkbox field in the widget editor dialog
        And the user writes "Showcase - Provider" in the "layout-page-seeAllAction" dropdown field in the widget editor dialog
        And the user presses Enter
        And the user ticks the "Create" checkbox field in the widget editor dialog
        And the user writes "Showcase - Provider" in the "layout-page-createAction" dropdown field in the widget editor dialog
        And the user presses Enter
        Then the "add" button in the widget editor dialog is enabled
        When the user clicks the "add" button in the widget editor dialog


        # navigating to widget-linked pages
        Then the "Showcase dashboard" titled dashboard in the dashboard editor is displayed
        And the "table widget" titled widget in the dashboard editor is displayed
        When the user clicks the "save" button in the dashboard editor footer
        Then a toast containing text "Dashboard saved." is displayed
        When the user dismisses all the toasts
        Then the "Showcase dashboard" titled dashboard is displayed
        And the "table widget" titled widget in the dashboard is displayed
        When the user selects the "table widget" titled table widget field in the dashboard
        Then the "See all" button of the table widget field is enabled
        And the "Create" button of the table widget field is enabled
        When the user clicks the "See all" button of the table widget field
        Then the "Providers" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the value of the option menu of the table field is "My selected data"
        When the user opens the application on a desktop
        Then the "Showcase dashboard" titled dashboard is displayed
        And the "table widget" titled widget in the dashboard is displayed
        When the user selects the "table widget" titled table widget field in the dashboard
        And the user clicks the "Create" button of the table widget field
        Then the "Provider" titled page is displayed

    @ClearDashboards
    Scenario: As a user I want to create a table widget with specific filters
        XT-81309, XT-81905
        # Set Ending date for a product to next month
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseProduct/eyJfaWQiOiIxNDIifQ=="
        And the user selects the "Ending date" labelled date field on the main page
        And the user writes a generated date in the date field with value "(M+1)/T/Y"
        And the user stores the value of the date field with the key "[ENV_ENDING_DATE]"
        And the user clicks the "Save" labelled business action button on the main page

        # Create new dashboard
        When the user opens the application on a desktop
        And the dashboard page is displayed
        Then the "Create a dashboard to get started." subtitled empty dashboard is displayed
        When the user clicks the create button on the dashboard
        Then the dashboard creation dialog is displayed
        When the user selects the template with title "Blank template" in the dashboard creation dialog
        And the user clicks the "next" button in the dashboard creation dialog
        Then the "New dashboard" titled dashboard in the dashboard editor is displayed

        # Widget selection step
        When the user clicks the "createAWidget" labelled button in the dashboard editor navigation panel
        Then the "New widget" titled widget editor dialog is displayed
        And the value of the step title of the widget editor dialog is "1. Select a widget to get started"
        And the "cancel" button in the widget editor dialog is enabled
        And the "next" button in the widget editor dialog is disabled
        When the user writes "Table Widget" in the "basic-title" text field in the widget editor dialog
        And the user presses Enter
        And the user writes "My demo category" in the "basic-category" dropdown field in the widget editor dialog
        And the user presses Enter
        And the user writes "Show Case Product" in the "basic-node" dropdown field in the widget editor dialog
        And the user presses Enter
        And the user selects the "TABLE" widget card in the widget editor dialog
        Then the "TABLE" widget card in the widget editor dialog is selected
        And the "next" button in the widget editor dialog is enabled
        When the user clicks the "next" button in the widget editor dialog
        Then the value of the step title of the widget editor dialog is "2. Select the data to add to your widget"

        # Data selection step
        When the user clicks the "next" button in the widget editor dialog
        Then the value of the step title of the widget editor dialog is "2. Select the data to add to your widget"
        And the "next" button in the widget editor dialog is disabled
        When the user searches for "Product" in the widget editor dialog
        And the user selects the "Product" tree-view element in the widget editor dialog
        And the user clears the search field in the widget editor dialog
        And the user searches for "Description" in the widget editor dialog
        And the user selects the "Description" tree-view element in the widget editor dialog
        And the user clears the search field in the widget editor dialog
        And the user searches for "Ending date" in the widget editor dialog
        And the user selects the "Ending date" tree-view element in the widget editor dialog
        And the user clears the search field in the widget editor dialog
        And the user searches for "List price" in the widget editor dialog
        And the user selects the "List price" tree-view element in the widget editor dialog
        And the user clears the search field in the widget editor dialog
        And the user searches for "Hot product" in the widget editor dialog
        And the user selects the "Hot product" tree-view element in the widget editor dialog
        When the user clicks the "next" button in the widget editor dialog
        Then the value of the step title of the widget editor dialog is "3. Add your content"

        # Content selection step
        When the user clicks the "Add column" table button in the widget editor dialog
        And the user writes "Product" in the "content-property" dropdown field of row "1" in the widget editor dialog
        And the user presses Enter
        And the user clicks the "add" action button of row "1" in the widget editor dialog
        And the user writes "Description" in the "content-property" dropdown field of row "2" in the widget editor dialog
        And the user presses Enter
        And the user clicks the "add" action button of row "2" in the widget editor dialog
        And the user writes "Ending date" in the "content-property" dropdown field of row "3" in the widget editor dialog
        And the user presses Enter
        And the user clicks the "add" action button of row "3" in the widget editor dialog
        And the user writes "List price" in the "content-property" text field of row "4" in the widget editor dialog
        And the user presses Enter
        And the user clicks the "add" action button of row "4" in the widget editor dialog
        And the user writes "Hot product" in the "content-property" text field of row "5" in the widget editor dialog
        And the user presses Enter
        And the user clicks the "next" button in the widget editor dialog
        Then the value of the step title of the widget editor dialog is "4. Add your filters"

        # Filter selection step
        When the user clicks the "Add filter" table button in the widget editor dialog
        And the user writes "Product" in the "filter-property" dropdown field of row "1" in the widget editor dialog
        And the user presses Tab
        And the user writes "Greater than" in the "filter-type" dropdown field of row "1" in the widget editor dialog
        And the user presses Tab
        And the user writes "Cake" in the "filter-value" text field of row "1" in the widget editor dialog
        And the user presses Tab
        And the user clicks the "add" action button of row "1" in the widget editor dialog
        And the user writes "List price" in the "filter-property" dropdown field of row "2" in the widget editor dialog
        And the user presses Tab
        And the user writes "Does not equal" in the "filter-type" dropdown field of row "2" in the widget editor dialog
        And the user presses Tab
        And the user writes "23" in the "filter-value" text field of row "2" in the widget editor dialog
        And the user presses Tab
        And the user clicks the "add" action button of row "2" in the widget editor dialog
        And the user writes "Ending date" in the "filter-property" dropdown field of row "3" in the widget editor dialog
        And the user presses Tab
        And the user writes "Time frame" in the "filter-type" dropdown field of row "3" in the widget editor dialog
        And the user presses Tab
        And the user writes "Next month" in the "filter-value-timeframe" dropdown field of row "3" in the widget editor dialog
        And the user presses Tab
        And the user clicks the "add" action button of row "3" in the widget editor dialog
        And the user writes "Description" in the "filter-property" dropdown field of row "4" in the widget editor dialog
        And the user presses Tab
        And the user writes "Empty" in the "filter-type" dropdown field of row "4" in the widget editor dialog
        And the user presses Tab
        And the user clicks the "add" action button of row "4" in the widget editor dialog
        And the user writes "Hot product" in the "filter-property" dropdown field of row "5" in the widget editor dialog
        And the user presses Tab
        And the user writes "True" in the "filter-value" text field of row "5" in the widget editor dialog
        And the user presses Tab
        And the user clicks the "next" button in the widget editor dialog

        # Save widget
        When the user clicks the "next" button in the widget editor dialog
        And the user clicks the "add" button in the widget editor dialog
        And the user clicks the "save" button in the dashboard editor footer
        Then the "New dashboard" titled dashboard is displayed
        And the "table widget" titled widget in the dashboard is displayed

        # Check widget display the expected data based on the set filters
        When the user selects the "Table Widget" titled table widget field in the dashboard
        And the user selects the row with text "Cake - Cake Sheet Macaroon" and column header "Product" of the table widget field
        And the value of the cell with column header "Description" of the row of the table widget field is ""
        And the value of the cell with column header "Ending date" of the row of the table widget field is "[ENV_ENDING_DATE]"
        And the value of the cell with column header "List price" of the row of the table widget field is "72"
        And the value of the cell with column header "Hot product" of the row of the table widget field is "True"
