Feature: 4-2 Text
    # Tests text component across different devices, verifying text entry, validation, clearing operations, and formatting options and control the text field state.

    Scenario Outline: <Device> - As an ATP XTreeM User I can write and verify a text field using label
        XT-25838
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Text"
        Then the "Text" titled page is displayed

        Given the user selects the "Title" labelled text field on the main page
        When the user writes "Sample title" in the text field
        And the user presses Tab

        Given the user selects the "Sample title" labelled text field on the main page
        When the user writes "test" in the text field
        Then the value of the text field is "test"
        And the user clears the text field
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> - As an ATP XTreeM User I can write and verify a text field using bind
        XT-25838
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Text"
        Then the "Text" titled page is displayed

        Given the user selects the "field" bound text field on the main page
        When the user writes "test again" in the text field
        Then the value of the text field is "test again"
        And the user clears the text field
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> - As an ATP XTreeM User I can scroll to and blur the text field
        XT-25838
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Text"
        Then the "Text" titled page is displayed

        Given the user selects the "field" bound text field on the main page
        And the user scrolls to the text field
        And the user blurs the text field
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    #text  field - set / check properties

    Scenario Outline: <Device> - As and ATP XTreeM user I can verify the text field title
        XT-25838
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Text"
        Then the "Text" titled page is displayed

        Given the user selects the "Title" labelled text field on the main page
        When the user writes "Sample title" in the text field
        And the user presses Tab

        Given the user selects the "field" bound text field on the main page
        When the user clicks in the text field
        Then the title of the text field is "Sample title"
        Then the title of the text field is displayed

        Given the user selects the "Is title hidden" labelled checkbox field on the main page
        When the user ticks the checkbox field

        Given the user selects the "field" bound text field on the main page
        Then the title of the text field is hidden
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |


    Scenario Outline: <Device> - As and ATP XTreeM user I can verify the text field helper text
        XT-25838
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Text"
        Then the "Text" titled page is displayed

        Given the user selects the "Helper text" labelled text field on the main page
        When the user writes "Sample text" in the text field
        And the user presses Tab

        Given the user selects the "field" bound text field on the main page
        When the user clicks in the text field
        Then the helper text of the text field is "Sample text"
        Then the helper text of the text field is displayed

        Given the user selects the "Is helper text hidden" labelled checkbox field on the main page
        When the user ticks the checkbox field

        Given the user selects the "field" bound text field on the main page
        Then the helper text of the text field is hidden
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |


    Scenario Outline: <Device> - As and ATP XTreeM user I can verify if the text field is displayed or hidden
        XT-25838
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Text"
        Then the "Text" titled page is displayed

        Given the user selects the "field" bound text field on the main page
        When the user clicks in the text field
        Then the "field" bound text field on the main page is displayed

        Given the user selects the "Is hidden" labelled checkbox field on the main page
        When the user ticks the checkbox field
        Then the "field" bound text field on the main page is hidden
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |


    Scenario Outline:<Device> - As and ATP XTreeM user I can verify if the text field is enabled or disabled
        XT-25838
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Text"
        Then the "Text" titled page is displayed

        Given the user selects the "field" bound text field on the main page
        Then the text field is enabled

        Given the user selects the "Is disabled" labelled checkbox field on the main page
        When the user ticks the checkbox field

        Given the user selects the "field" bound text field on the main page
        Then the text field is disabled
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |


    Scenario Outline: <Device> - As and ATP XTreeM user I can verify if the text field is read-only
        XT-25838
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Text"
        Then the "Text" titled page is displayed

        Given the user selects the "Is read only" labelled checkbox field on the main page
        When the user ticks the checkbox field

        Given the user selects the "field" bound text field on the main page
        Then the text field is read-only
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |


    Scenario: Set the value of the text field
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Text"
        Then the "Text" titled page is displayed

        Given the user selects the "Value" labelled text field on the main page
        When the user writes "Sample text" in the text field
        And the user presses Tab
        Given the user selects the "field" bound text field on the main page
        Then the value of the text field is "Sample text"


    Scenario: Trigger custom change event handler
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Text"
        Then the "Text" titled page is displayed

        Then the "changeTriggered" bound label field on the main page is hidden

        Given the user selects the "field" bound text field on the main page
        When the user writes "change" in the text field

        Then the "changeTriggered" bound label field on the main page is displayed

    Scenario: Trigger custom click event handler
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Text"
        Then the "Text" titled page is displayed

        Then the "clickTriggered" bound label field on the main page is hidden

        Given the user selects the "field" bound text field on the main page
        When the user clicks in the text field

        Then the "clickTriggered" bound label field on the main page is displayed

    Scenario: Field marked as invalid on invalid max length
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Text"
        Then the "Text" titled page is displayed

        Given the user selects the "Max length" labelled numeric field on the main page
        When the user writes "5" in the numeric field

        Given the user selects the "field" bound text field on the main page
        When the user writes "abcdefghijk" in the text field
        And the user blurs the text field
        Then the text field is invalid

        When the user writes "abcd" in the text field
        And the user blurs the text field
        Then the text field is valid


    Scenario: Field marked as invalid on invalid min length
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Text"
        Then the "Text" titled page is displayed

        Given the user selects the "Min length" labelled numeric field on the main page
        When the user writes "5" in the numeric field

        Given the user selects the "field" bound text field on the main page
        When the user writes "abcd" in the text field
        And the user blurs the text field
        Then the text field is invalid

        When the user writes "abcdefghijk" in the text field
        And the user blurs the text field
        Then the text field is valid


    Scenario: Field marked as invalid on invalid value
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Text"
        Then the "Text" titled page is displayed

        Given the user selects the "Mandatory" labelled text field on the main page
        When the user clicks in the text field
        When the user writes "aasdbcd" in the text field
        And the user blurs the text field
        And the user clears the text field
        And the user blurs the text field
        Then the text field is invalid

        When the user writes "2" in the text field
        And the user blurs the text field
        Then the text field is valid


    Scenario: As an app dev I want to be able to trigger the onChange event of a text field
        XT-89823
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/OnChange"
        When the user clicks the "button1" bound button on the main page
        Then a toast containing text "Field #1 onChange triggered" is displayed
        And the user dismisses all the toasts
        When the user clicks the "button2" bound button on the main page
        Then a toast containing text "Error on field #2 caught by button: An error occurred" is displayed
        And the user dismisses all the toasts
        When the user clicks the "button3" bound button on the main page
        Then a toast containing text "Error on field #3 caught by app logic: An error occurred" is displayed
        And the user dismisses all the toasts
        When the user clicks the "button4" bound button on the main page
        Then a error toast containing text "Error caught by page. Error on button4: An error occurred" is displayed

    Scenario: As a developer I want to set text field properties by callbacks
        XT-2178 XT-1978
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Text"
        Then the "Text" titled page is displayed

        Given the user selects the "Title" labelled text field on the main page
        When the user writes "Callback title" in the text field

        Given the user selects the "Is Read-Only" labelled checkbox field on the main page
        When the user ticks the checkbox field

        Given the user selects the "Value" labelled text field on the main page
        When the user writes "Callback text" in the text field

        Given the user selects the "fieldCallback" bound text field on the main page
        Then the text field is read-only
        Then the value of the text field is "Callback text"
        Then the title of the text field is "Callback title"
