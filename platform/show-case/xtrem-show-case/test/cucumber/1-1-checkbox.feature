Feature: 1-1 Checkbox
    # Tests the checkbox component across different devices, verifying selection states, toggle functionality, and binding to boolean data values

    Scenario Outline: <Device> - As an ATP XTreeM User I can tick or untick a checkbox and verify its value using label
        XT-25838
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Checkbox"
        Then the "Field - Checkbox" titled page is displayed

        Given the user selects the "Title" labelled text field on the main page
        When the user writes "Sample title" in the text field
        And the user presses Tab

        Given the user selects the "Sample title" labelled checkbox field on the main page
        And the user ticks the checkbox field
        Then the value of the checkbox field is "true"
        And the user unticks the checkbox field
        Then the value of the checkbox field is "false"
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |


    Scenario Outline: <Device> - As an ATP XTreeM User I can tick or untick a checkbox and verify its value using bind
        XT-25838
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Checkbox"
        Then the "Field - Checkbox" titled page is displayed

        Given the user selects the "field" bound checkbox field on the main page
        And the user ticks the checkbox field
        Then the value of the checkbox field is "true"
        And the user unticks the checkbox field
        Then the value of the checkbox field is "false"
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |


    Scenario Outline: <Device> - As an ATP XTreeM User I scroll to and blur the checkbox field
        XT-25838
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Checkbox"
        Then the "Field - Checkbox" titled page is displayed

        Given the user selects the "field" bound checkbox field on the main page
        When the user scrolls to the checkbox field
        And the user blurs the checkbox field
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |


    #Checkbox Field: Set / check properties

    Scenario Outline: <Device> - As and ATP XTreeM user I can verify the checkbox field title
        XT-25838
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Checkbox"
        Then the "Field - Checkbox" titled page is displayed

        Given the user selects the "Title" labelled text field on the main page
        When the user writes "Sample title" in the text field
        And the user presses Tab

        Given the user selects the "field" bound checkbox field on the main page
        Then the title of the checkbox field is "Sample title"
        Then the title of the checkbox field is displayed

        Given the user selects the "Is title hidden" labelled checkbox field on the main page
        When the user ticks the checkbox field

        Given the user selects the "field" bound checkbox field on the main page
        Then the title of the checkbox field is hidden

        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |


    Scenario Outline: <Device> - As and ATP XTreeM user I can verify the checkbox field helper text
        XT-25838
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Checkbox"
        Then the "Field - Checkbox" titled page is displayed

        Given the user selects the "Helper text" labelled text field on the main page
        When the user writes "Sample text" in the text field
        And the user presses Tab

        Given the user selects the "field" bound checkbox field on the main page
        Then the helper text of the checkbox field is "Sample text"
        Then the helper text of the checkbox field is displayed

        Given the user selects the "Is helper text hidden" labelled checkbox field on the main page
        When the user ticks the checkbox field

        Given the user selects the "field" bound checkbox field on the main page
        Then the helper text of the checkbox field is hidden

        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |


    Scenario Outline: <Device> - As and ATP XTreeM user I can verify if the checkbox field is displayed or hidden
        XT-25838
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Checkbox"
        Then the "Field - Checkbox" titled page is displayed

        Given the user selects the "field" bound checkbox field on the main page
        When the user clicks in the checkbox field
        Then the "field" bound checkbox field on the main page is displayed

        Given the user selects the "Is hidden" labelled checkbox field on the main page
        When the user ticks the checkbox field
        Then the "field" bound checkbox field on the main page is hidden
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |


    Scenario Outline:<Device> - As and ATP XTreeM user I can verify if the checkbox field is enabled or disabled
        XT-25838
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Checkbox"
        Then the "Field - Checkbox" titled page is displayed

        Given the user selects the "field" bound checkbox field on the main page
        Then the checkbox field is enabled

        Given the user selects the "Is disabled" labelled checkbox field on the main page
        When the user ticks the checkbox field

        Given the user selects the "field" bound checkbox field on the main page
        Then the checkbox field is disabled
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |


    Scenario Outline: <Device> - As and ATP XTreeM user I can verify if the checkbox field is read-only
        XT-25838
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Checkbox"
        Then the "Field - Checkbox" titled page is displayed

        Given the user selects the "Is readOnly" labelled checkbox field on the main page
        When the user ticks the checkbox field

        Given the user selects the "field" bound checkbox field on the main page
        Then the checkbox field is read-only

        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |


    Scenario: Trigger custom click event handler
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Checkbox"
        Then the "Field - Checkbox" titled page is displayed

        Given the user selects the "clickTriggered" bound label field on the main page
        Then the "clickTriggered" bound label field on the main page is hidden

        When the user selects the "field" bound checkbox field on the main page
        Then the value of the checkbox field is "true"
        And the user unticks the checkbox field
        Then the "clickTriggered" bound label field on the main page is displayed


    Scenario: Trigger mandatory checkbox validation error
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-show-case/Checkbox"
        Then the "Field - Checkbox" titled page is displayed

        Given the user selects the "mandatory" bound checkbox field on the main page
        And the user ticks the checkbox field
        And the user unticks the checkbox field
        And the user hovers over the checkbox field
        Then the "You need to select this checkbox." validation error message of the checkbox field is displayed
        When the user ticks the checkbox field
        Then the "You need to select this checkbox." validation error message of the checkbox field is hidden
