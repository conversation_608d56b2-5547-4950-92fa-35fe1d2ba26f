Feature: 3-2 Static content
    # Tests the static content field component, focusing on text truncation behaviors, display formatting, and proper rendering across different devices

    Scenario Outline: <Device> - As a user I would like to see that text is truncated by the static content field
        XT-37774
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/StaticContentField/eyJfaWQiOiIyIn0="
        Then the "Static Content Field" titled page is displayed
        Given the user selects the "Number of visible lines" labelled dropdown-list field on the main page
        When the user clicks in the dropdown-list field
        And the user selects "2" in the dropdown-list field
        Then the value of the dropdown-list field is "2"
        And element with test id "e-field-bind-boundField" looks as before
        And element with test id "e-field-bind-longContentField" looks as before
        And element with test id "e-field-bind-markdownField" looks as before
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |


    Scenario: As an ATP / XTreeM user I can verify the short value of the static-content
        XT-85552
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/StaticContentField/eyJfaWQiOiIyIn0="
        Then the "Static Content Field" titled page is displayed

        Given the user selects the "boundField" bound static-content field on the main page
        Then the value of the static-content field is "Amazon"
    # And takes a screenshot

    Scenario: As an ATP / XTreeM user I can verify the markdown value of the static-content
        XT-85552
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/StaticContentField/eyJfaWQiOiIyIn0="
        Then the "Static Content Field" titled page is displayed

        Given the user selects the "markdownField" bound static-content field on the main page
        Then the value of the static-content field is
            """
            Hi there!

            This is some markdown content.

            See

            It can do

            bullet points

            And the dangerous tags are escaped: <iframe src="http://wwww.sage.com"></iframe>

            <script>alert("this could be dangerous");</script>
            """
    # And takes a screenshot

    Scenario: As an ATP / XTreeM user I can verify the long value of the static-content field
        XT-85552
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/StaticContentField/eyJfaWQiOiIyIn0="
        Then the "Static Content Field" titled page is displayed

        And the user selects the "Title" labelled text field on the main page
        And the user writes "My Title" in the text field
        And the user blurs the text field

        Given the user selects the "My Title " labelled static-content field on the main page
        Then the value of the static-content field is
            """
            This is some long content
            with line breaks and
            it can be translated too.
            """
    # And takes a screenshot


    Scenario: As an ATP / XTreeM user I can verify the value of the static content title
        XT-85552
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/StaticContentField/eyJfaWQiOiIyIn0="
        Then the "Static Content Field" titled page is displayed

        And the user selects the "Title" labelled text field on the main page
        And the user writes "My Title" in the text field
        And the user blurs the text field

        Given the user selects the "longContentField" bound static-content field on the main page
        Then the title of the static-content field is "My Title"
    # And takes a screenshot


    Scenario: As an ATP / XTreeM user I can verify the value of the static content helper text
        XT-85552
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/StaticContentField/eyJfaWQiOiIyIn0="
        Then the "Static Content Field" titled page is displayed

        And the user selects the "Helper text" labelled text field on the main page
        And the user writes "My Helper text" in the text field
        And the user blurs the text field

        Given the user selects the "longContentField" bound static-content field on the main page
        Then the helper text of the static-content field is "My Helper text"
    # And takes a screenshot


    Scenario: As an ATP / XTreeM user I can verify the static-content field is displayed / hidden
        XT-85552
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/StaticContentField/eyJfaWQiOiIyIn0="
        Then the "Static Content Field" titled page is displayed

        And the user selects the "Title" labelled text field on the main page
        And the user writes "My Title" in the text field
        And the user blurs the text field

        Then the "My Title" labelled static-content field on the main page is displayed
        # And takes a screenshot

        Then the user selects the "Is hidden" labelled checkbox field on the main page
        When the user ticks the checkbox field
        Then the "longContentField" bound static-content field on the main page is hidden
# And takes a screenshot
