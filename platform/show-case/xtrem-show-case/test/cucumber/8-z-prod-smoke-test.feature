Feature: 8 Z prod smoke test
    # Tests the basic functionality of the production system through smoke testing, verifying that all key pages load correctly and critical features operate as expected

    Scenario Outline: As a user I want to make sure that each page loads without any problems on desktop
        Given the user opens the application on a desktop using the following link: <Page>
        Then the <Title> titled page is displayed

        Examples:
            | Page                                               | Title                                             |
            | "@sage/xtrem-show-case/BindProperty"               | "Bind property"                                   |
            | "@sage/xtrem-show-case/CrudUpdateFile"             | "CRUD - Update"                                   |
            | "@sage/xtrem-show-case/CrudUpdate"                 | "CRUD - Update"                                   |
            | "@sage/xtrem-show-case/Dialogs"                    | "Dialogs"                                         |
            | "@sage/xtrem-show-case/Interactions"               | "Event listeners"                                 |
            | "@sage/xtrem-show-case/Aggregate"                  | "Field - Aggregate"                               |
            | "@sage/xtrem-show-case/Button"                     | "Field - Button"                                  |
            | "@sage/xtrem-show-case/Calendar"                   | "Field - Calendar"                                |
            | "@sage/xtrem-show-case/Chart"                      | "Field - Chart"                                   |
            | "@sage/xtrem-show-case/ChartPie"                   | "Field - Chart - Pie"                             |
            | "@sage/xtrem-show-case/Checkbox"                   | "Field - Checkbox"                                |
            | "@sage/xtrem-show-case/Count"                      | "Field - Count"                                   |
            | "@sage/xtrem-show-case/DateField"                  | "Field - Date"                                    |
            | "@sage/xtrem-show-case/DetailList"                 | "Field - Detail list"                             |
            | "@sage/xtrem-show-case/DropdownList"               | "Field - Dropdown List"                           |
            | "@sage/xtrem-show-case/FileUpload"                 | "Field - File Upload"                             |
            | "@sage/xtrem-show-case/Icon"                       | "Field - Icon"                                    |
            | "@sage/xtrem-show-case/Label"                      | "Field - Label"                                   |
            | "@sage/xtrem-show-case/Link"                       | "Field - Link"                                    |
            | "@sage/xtrem-show-case/NestedPodBlock"             | "Pod Block with Nested Reference Arrays"          |
            | "@sage/xtrem-show-case/NestedPodCollection"        | "Pod Collection With nested Reference Arrays"     |
            | "@sage/xtrem-show-case/NestedPodField"             | "Pod Field with Nested Reference Arrays"          |
            | "@sage/xtrem-show-case/NestedGrid"                 | "Field - NestedGrid"                              |
            | "@sage/xtrem-show-case/NestedVitalPodBlock"        | "Vital Pod Block with Nested Reference Arrays"    |
            | "@sage/xtrem-show-case/NestedVitalPod"             | "Vital Pod Field with Nested Reference Arrays"    |
            | "@sage/xtrem-show-case/Numeric"                    | "Numeric"                                         |
            | "@sage/xtrem-show-case/PluginGraphiql"             | "Field - GraphiQL Plugin"                         |
            | "@sage/xtrem-show-case/PluginMonaco"               | "Field - Monaco Plugin"                           |
            | "@sage/xtrem-show-case/PluginPdf"                  | "Field - PDF Plugin"                              |
            | "@sage/xtrem-show-case/Progress"                   | "Field - Progress"                                |
            | "@sage/xtrem-show-case/RadioButtons"               | "Field - Radio"                                   |
            | "@sage/xtrem-show-case/Reference"                  | "Field - Reference"                               |
            | "@sage/xtrem-show-case/RichText"                   | "Field - Rich text"                               |
            | "@sage/xtrem-show-case/Select"                     | "Field - Select"                                  |
            | "@sage/xtrem-show-case/Switch"                     | "Switch"                                          |
            | "@sage/xtrem-show-case/Separator"                  | "Separator"                                       |
            | "@sage/xtrem-show-case/Table"                      | "Field - Table"                                   |
            | "@sage/xtrem-show-case/TableDefaultValues"         | "Field - Table (Default Values)"                  |
            | "@sage/xtrem-show-case/TableNestedReference"       | "Field - Table (nested reference)"                |
            | "@sage/xtrem-show-case/TransientTable"             | "Field - Table (Transient)"                       |
            | "@sage/xtrem-show-case/TableCardView"              | "Field - Table card view"                         |
            | "@sage/xtrem-show-case/Text"                       | "Text"                                            |
            | "@sage/xtrem-show-case/TextArea"                   | "Text area"                                       |
            | "@sage/xtrem-show-case/ToggleButtons"              | "Field - Toggle Buttons"                          |
            | "@sage/xtrem-show-case/ShowCaseEmployee"           | "Employees"                                       |
            | "@sage/xtrem-show-case/Focus"                      | "Fields focus"                                    |
            | "@sage/xtrem-show-case/NavigationPanel"            | "Navigation Panel"                                |
            | "@sage/xtrem-show-case/NavigationPanelOnly"        | "Navigation Panel - No page body, only nav panel" |
            | "@sage/xtrem-show-case/NavigationPanelComplexCard" | "Navigation Panel - Complex Card"                 |
            | "@sage/xtrem-show-case/Toasts"                     | "Toasts"                                          |
            | "@sage/xtrem-show-case/ActionButtons"              | "Page - Action Buttons"                           |
            | "@sage/xtrem-show-case/ConditionalValidation"      | "Page - Conditional validation"                   |
            | "@sage/xtrem-show-case/PageDefaultCrudActions"     | "Page - Default CRUD Actions"                     |
            | "@sage/xtrem-show-case/PageDirtyState"             | "Page - Dirty state on fields"                    |
            | "@sage/xtrem-show-case/PageHeaderCard"             | "Two lines"                                       |
            | "@sage/xtrem-show-case/DetailPanel"                | "Page - Detail panel"                             |
            | "@sage/xtrem-show-case/Wizard"                     | "Page - Wizard Layout"                            |
            | "@sage/xtrem-show-case/Responsiveness"             | "Responsiveness"                                  |
            | "@sage/xtrem-show-case/ShowCaseProduct"            | "Products"                                        |
            | "@sage/xtrem-show-case/ShowCaseProvider"           | "Providers"                                       |
            | "@sage/xtrem-show-case/Alignment"                  | "Sections - Alignment"                            |

    Scenario: As a user I want to make sure that an error dialog appears if the page does not load on desktop
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Wazup"
        Then an error dialog appears on the main page

    Scenario: As a user I want to create a new product record in the database with basic control rules
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseProduct/$new"
        And the user selects the "Product" labelled text field on the main page
        And the user writes "Test Product" in the text field
        And the user selects the "Description" labelled text field on the main page
        And the user writes "Test Description" in the text field
        And the user selects the "Provider" labelled reference field on the main page
        When the user writes "Ama" in the reference field
        And the user selects "Amazon" in the reference field
        And the user selects the "Category (Dropdown)" labelled dropdown-list field on the main page
        And the user clicks in the dropdown-list field
        And the user selects "Great" in the dropdown-list field
        And the user clicks the "Save" labelled business action button on the main page
        And the user selects the "Quantity" labelled numeric field on the main page
        Then the value of the numeric field is "1"
        And the user selects the "Stock" labelled numeric field on the main page
        Then the value of the numeric field is "1"
        And the user selects the "List Price" labelled numeric field on the main page
        Then the value of the numeric field is "0.00"

    Scenario: As a user I want my fields to be bound to the dataset
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseProvider/eyJfaWQiOjF9"
        And the user selects the "ID" labelled text field on the main page
        Then the value of the text field is "1"
        And the user selects the "Integer" labelled numeric field on the main page
        Then the value of the numeric field is "1"
        And the user selects the "Decimal" labelled numeric field on the main page
        Then the value of the numeric field is "2.34"

    # Scenario: As a user I want to click in a specific column of a table
    #    Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Table"
    #    When the user clicks on the "st" bound nested label field of row 4 of the "field" bound table field in the main page
    #    Then an info dialog appears on the main page

    Scenario: As a user I want get the total price of selected items of a table
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Table"
        And the user selects the "field" bound table field on the main page
        And the user selects the row 8 of the table field
        When the user ticks the main checkbox of the selected row in the table field
        And the user selects the "tableSelectedTotal" bound numeric field on the main page
        Then the value of the numeric field is "56.35"

    # XT-62332 TODO: ag-grid v31.0.3 apparently has some accesibility issue
    # Scenario: As a user I want to check I do not have accesibility violations on the Table - Deep cell binding page
    #      Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TableDeepBoundColumns"
    #      Then the "Table - Deep cell binding" titled page is displayed
    #      And the page does not have any "critical" accessibility violations
    #      And the page does not have any "serious" accessibility violations
    #      And the page does not have any "moderate" accessibility violations
    #      And the page does not have any "minor" accessibility violations

    # XT-65067 TODO: breaks with upgrade to React 18
    # Scenario: As a user I want to check I do not have accesibility violations on the Page - Tabs Layout page
    #     Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/PageTabs"
    #     Then the "Page - Tabs Layout" titled page is displayed
    #     And the page does not have any "critical" accessibility violations
    #     And the page does not have any "serious" accessibility violations
    #     And the page does not have any "moderate" accessibility violations
    #     And the page does not have any "minor" accessibility violations
    #     When selects the "Not Hidden Section" labelled navigation anchor on the main page
    #     Then the page does not have any "critical" accessibility violations
    #     And the page does not have any "serious" accessibility violations
    #     And the page does not have any "moderate" accessibility violations
    #     And the page does not have any "minor" accessibility violations
    #     When selects the "Hidden Section" labelled navigation anchor on the main page
    #     Then the page does not have any "critical" accessibility violations
    #     And the page does not have any "serious" accessibility violations
    #     And the page does not have any "moderate" accessibility violations
    #     And the page does not have any "minor" accessibility violations

    Scenario: As a user I want to check I do not have critical accessibility violations on pages with Table - Date floating filter
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseInvoice"
        Then the "Invoices" titled page is displayed
        And the user executes an accessibility tests scan
        And the page does not have any "critical" accessibility violations

# Scenario: As a user I want to check if I have unread notifications
#     Given the user opens the application on a desktop
#     Then the "unread" notifications icon in the actions header is displayed
#     When the user clicks the notifications icon in the actions header
#     Then the notification with title "Lorem ipsum dolor" in the notification center is displayed
