Feature: 5 Y dashboard widget table read values
    # Tests the verification of data values displayed in table widgets, ensuring correct formatting and display of different data types

    @ClearDashboards
    Scenario: As a user I want to see the default values for a numeric fields
        Given the user opens the application on a desktop
        And the dashboard page is displayed
        Then the "Create a dashboard to get started." subtitled empty dashboard is displayed
        When the user clicks the create button on the dashboard
        Then the dashboard creation dialog is displayed
        When the user selects the template with title "Showcase dashboard" in the dashboard creation dialog
        And the user clicks the "next" button in the dashboard creation dialog
        Then the "Showcase dashboard" titled dashboard in the dashboard editor is displayed

        # Widget selection step
        When the user clicks the "createAWidget" labelled button in the dashboard editor navigation panel
        Then the "New widget" titled widget editor dialog is displayed
        And the value of the step title of the widget editor dialog is "1. Select a widget to get started"
        And the "cancel" button in the widget editor dialog is enabled
        And the "next" button in the widget editor dialog is disabled
        When the user writes "Table Widget" in the "basic-title" text field in the widget editor dialog
        And the user presses Enter
        And the user writes "My demo category" in the "basic-category" dropdown field in the widget editor dialog
        And the user presses Enter
        And the user writes "Show Case Provider" in the "basic-node" dropdown field in the widget editor dialog
        And the user presses Enter

        # table widget
        And the user selects the "TABLE" widget card in the widget editor dialog
        Then the "TABLE" widget card in the widget editor dialog is selected
        And the "next" button in the widget editor dialog is enabled
        When the user clicks the "next" button in the widget editor dialog
        Then the value of the step title of the widget editor dialog is "2. Select the data to add to your widget"
        When the user clicks the "next" button in the widget editor dialog
        Then the value of the step title of the widget editor dialog is "2. Select the data to add to your widget"
        When the user selects the "Integer field" tree-view element in the widget editor dialog
        And the user clicks the "next" button in the widget editor dialog
        Then the value of the step title of the widget editor dialog is "3. Add your content"
        Then the "Add column" table button in the widget editor dialog is displayed
        When the user clicks the "Add column" table button in the widget editor dialog
        Then the "content-property" dropdown field of row "1" in the widget editor dialog is enabled
        When the user writes "Integer field" in the "content-property" dropdown field of row "1" in the widget editor dialog
        And the user presses Enter
        Then the value of the "content-title" text field of row "1" in the widget editor dialog is "Integer field"
        And the value of the "content-presentation" dropdown field of row "1" in the widget editor dialog is "Numeric"
        And the value of the "content-formatting" text field of row "1" in the widget editor dialog is "0"

        # clean up data
        When the user clicks the "cancel" button in the widget editor dialog
        Then a warn dialog appears on the main page
        When the user clicks the "Yes" button of the Confirm dialog
        Then the "Showcase dashboard" titled dashboard in the dashboard editor is displayed
        When the user clicks the "cancel" button in the dashboard editor footer
        Then the "Showcase dashboard" titled dashboard is displayed

    @ClearDashboards
    Scenario: As a user I want to see the default values for a text field
        Given the user opens the application on a desktop
        And the dashboard page is displayed
        Then the "Create a dashboard to get started." subtitled empty dashboard is displayed
        When the user clicks the create button on the dashboard
        Then the dashboard creation dialog is displayed
        When the user selects the template with title "Showcase dashboard" in the dashboard creation dialog
        And the user clicks the "next" button in the dashboard creation dialog
        Then the "Showcase dashboard" titled dashboard in the dashboard editor is displayed

        # Widget selection step
        When the user clicks the "createAWidget" labelled button in the dashboard editor navigation panel
        Then the "New widget" titled widget editor dialog is displayed
        And the value of the step title of the widget editor dialog is "1. Select a widget to get started"
        And the "cancel" button in the widget editor dialog is enabled
        And the "next" button in the widget editor dialog is disabled
        When the user writes "Table Widget" in the "basic-title" text field in the widget editor dialog
        And the user presses Enter
        And the user writes "My demo category" in the "basic-category" dropdown field in the widget editor dialog
        And the user presses Enter
        And the user writes "Show Case Provider" in the "basic-node" dropdown field in the widget editor dialog
        And the user presses Enter

        # table widget
        And the user selects the "TABLE" widget card in the widget editor dialog
        Then the "TABLE" widget card in the widget editor dialog is selected
        And the "next" button in the widget editor dialog is enabled
        When the user clicks the "next" button in the widget editor dialog
        Then the value of the step title of the widget editor dialog is "2. Select the data to add to your widget"
        When the user clicks the "next" button in the widget editor dialog
        Then the value of the step title of the widget editor dialog is "2. Select the data to add to your widget"
        When the user selects the "Text field" tree-view element in the widget editor dialog
        And the user clicks the "next" button in the widget editor dialog
        Then the value of the step title of the widget editor dialog is "3. Add your content"
        Then the "Add column" table button in the widget editor dialog is displayed
        When the user clicks the "Add column" table button in the widget editor dialog
        Then the "content-property" dropdown field of row "1" in the widget editor dialog is enabled
        When the user writes "Text field" in the "content-property" dropdown field of row "1" in the widget editor dialog
        And the user presses Enter
        Then the value of the "content-title" text field of row "1" in the widget editor dialog is "Text field"
        And the value of the "content-presentation" paragraph field of row "1" in the widget editor dialog is "Text"
