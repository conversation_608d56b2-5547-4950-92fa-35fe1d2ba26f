Feature: 4-2 Vital pod nested field
    # Tests the vital pod component with nested fields, verifying data entry, field interactions and validation within complex nested structures.

    Scenario Outline: <Device> - As a user I want to write and verify a value of the nested field in the vital pod field
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/VitalPodField/eyJfaWQiOiIyMSJ9"
        Then the "Field - Vital Pod (as Field)" titled page is displayed
        Then the user selects the "originAddress" bound vital pod field on the main page
        And the vital pod field is enabled
        #selection using label
        And the user selects the "Address" labelled vital pod field on the main page
        And the user writes "My address 2A" in the "Line2" labelled nested text field of the vital pod field
        Then the value of the "Line2" labelled nested text field in the vital pod field is "My address 2A"
        And the user writes "United States" in the "Country" labelled nested reference field of the vital pod field
        And the user selects "United States" in the "Country" labelled nested reference field of the vital pod field
        Then the value of the "Country" labelled nested reference field in the vital pod field is "United States"
        #selection using bind
        When the user selects the "originAddress" bound vital pod field on the main page
        And the user writes "My address 2B" in the "addressLine2" bound nested text field of the vital pod field
        And the value of the "addressLine2" bound nested text field in the vital pod field is "My address 2B"
        #Make sure the value has been previously properly selected.
        And the user selects the "Address" labelled vital pod field on the main page
        Then the value of the "country" bound nested reference field in the vital pod field is "United States"
        And the user writes "Spain" in the "country" bound nested reference field of the vital pod field
        And the user selects "Spain" in the "country" bound nested reference field of the vital pod field
        Then the value of the "country" bound nested reference field in the vital pod field is "Spain"
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> - As a user I want to write and verify a value of the nested field in the vital pod block
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/VitalPodBlock/eyJfaWQiOiIyMSJ9"
        Then the "Field - Vital Pod (as Block)" titled page is displayed
        Then the user selects the "originAddress" bound vital pod field on the main page
        And the vital pod field is enabled
        #Selection using label
        When the user selects the "Address" labelled vital pod field on the main page
        And the user writes "My address 2A" in the "Line2" labelled nested text field of the vital pod field
        Then the value of the "Line2" labelled nested text field in the vital pod field is "My address 2A"
        And the user selects the "Country" labelled reference field on the main page
        When the user scrolls to the reference field
        When the user selects the "Address" labelled vital pod field on the main page
        And the user writes "United States" in the "Country" labelled nested reference field of the vital pod field
        And the user selects "United States" in the "Country" labelled nested reference field of the vital pod field
        Then the value of the "Country" labelled nested reference field in the vital pod field is "United States"
        #Selection using bind
        When the user selects the "originAddress" bound vital pod field on the main page
        And the user writes "My address 2B" in the "addressLine2" bound nested text field of the vital pod field
        Then the value of the "addressLine2" bound nested text field in the vital pod field is "My address 2B"
        #Make sure the value has been previously properly selected.
        And the user selects the "Address" labelled vital pod field on the main page
        Then the value of the "country" bound nested reference field in the vital pod field is "United States"
        And the user writes "Spain" in the "country" bound nested reference field of the vital pod field
        And the user selects "Spain" in the "country" bound nested reference field of the vital pod field
        Then the value of the "country" bound nested reference field in the vital pod field is "Spain"
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> - As a user I want to verify a value of the nested field in a read-only vital pod field
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/VitalPodField/eyJfaWQiOiIyMSJ9"
        Then the "Field - Vital Pod (as Field)" titled page is displayed
        And the user selects the "Address" labelled vital pod field on the main page
        Then the vital pod field is enabled
        And the user selects the "Is readOnly" labelled checkbox field on the main page
        When the user ticks the checkbox field
        And the user selects the "Address" labelled vital pod field on the main page
        Then the vital pod field is read-only
        Then the value of the "Country" labelled nested reference field in the vital pod field is "United Kingdom"
        Then the value of the "Name" labelled nested text field in the vital pod field is "London Office"
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> - As a user I want to verify a value of the nested field in a read-only vital pod block
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/VitalPodBlock/eyJfaWQiOiIyMSJ9"
        Then the "Field - Vital Pod (as Block)" titled page is displayed
        And the user selects the "Address" labelled vital pod field on the main page
        Then the vital pod field is enabled
        And the user selects the "Is readOnly" labelled checkbox field on the main page
        When the user ticks the checkbox field
        Then the user selects the "Address" labelled vital pod field on the main page
        Then the vital pod field is read-only
        Then the value of the "Country" labelled nested reference field in the vital pod field is "United Kingdom"
        Then the value of the "Name" labelled nested text field in the vital pod field is "London Office"
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> - As a user I want to verify a value of the nested field in a disabled vital pod field
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/VitalPodField/eyJfaWQiOiIyMSJ9"
        Then the "Field - Vital Pod (as Field)" titled page is displayed
        And the user selects the "Address" labelled vital pod field on the main page
        Then the vital pod field is enabled
        And the user selects the "Is disabled" labelled checkbox field on the main page
        When the user ticks the checkbox field
        And the user selects the "Address" labelled vital pod field on the main page
        Then the vital pod field is disabled
        Then the value of the "Country" labelled nested reference field in the vital pod field is "United Kingdom"
        Then the value of the "Name" labelled nested text field in the vital pod field is "London Office"
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> - As a user I want to verify a value of the nested field in a disabled vital pod block
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/VitalPodBlock/eyJfaWQiOiIyMSJ9"
        Then the "Field - Vital Pod (as Block)" titled page is displayed
        And the user selects the "Address" labelled vital pod field on the main page
        Then the vital pod field is enabled
        And the user selects the "Is disabled" labelled checkbox field on the main page
        When the user ticks the checkbox field
        And the user selects the "Address" labelled vital pod field on the main page
        Then the vital pod field is disabled
        Then the value of the "Country" labelled nested reference field in the vital pod field is "United Kingdom"
        Then the value of the "Name" labelled nested text field in the vital pod field is "London Office"
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> - As a user I want to write and check a value to the field in the vital pod with switch
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/VitalPodWithSwitch/eyJfaWQiOiIxMDYifQ=="
        Then the "Field - Vital Pod (with Nested Switch)" titled page is displayed
        And the user selects the "field" bound vital pod field on the main page
        Then the title of the vital pod field is "Provider"
        #Selection using label
        When the user selects the "Provider" labelled vital pod field on the main page
        And the user writes "Test" in the "Text Field" labelled nested text field of the vital pod field
        Then the value of the "Text Field" labelled nested text field in the vital pod field is "Test"
        When the user clicks the "Boolean Field" labelled nested switch field of the vital pod field
        Then the "Boolean Field" labelled nested switch field in the vital pod field is set to "ON"
        #Selection using bind
        When the user selects the "field" bound vital pod field on the main page
        And the user writes "Test2" in the "textField" bound nested text field of the vital pod field
        Then the value of the "textField" bound nested text field in the vital pod field is "Test2"
        When the user clicks the "Boolean Field" labelled nested switch field of the vital pod field
        Then the "booleanField" bound nested switch field in the vital pod field is set to "OFF"
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> - As a user I want to verify a value of the nested switch in a read-only vital pod with switch
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/VitalPodWithSwitch/eyJfaWQiOiIxMDYifQ=="
        Then the "Field - Vital Pod (with Nested Switch)" titled page is displayed
        And the user selects the "field" bound vital pod field on the main page
        Then the title of the vital pod field is "Provider"
        And the user selects the "IsReadOnly" labelled switch field on the main page
        When the user turns the switch field "ON"
        And the user selects the "Provider" labelled vital pod field on the main page
        Then the value of the "Text Field" labelled nested text field in the vital pod field is "Ali Express"
        Then the "booleanField" bound nested switch field in the vital pod field is set to "OFF"
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: As an ATP XTreeM user I can interact with nested switch in vital pod and verify its value
        XT-43513
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/VitalPodWithSwitch/eyJfaWQiOiIxMDYifQ=="
        Then the "Field - Vital Pod (with Nested Switch)" titled page is displayed
        And the user selects the "field" bound vital pod field on the main page
        Then the "Boolean Field" labelled nested switch field in the vital pod field is set to "OFF"
        When the user clicks the "Boolean Field" labelled nested switch field of the vital pod field
        Then the "Boolean Field" labelled nested switch field in the vital pod field is set to "ON"
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> - As an ATP XTreeM user I can interact with multi reference in vital pod fields
        XT-44680
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/NestedVitalPodBlock/eyJfaWQiOiIzNCJ9"
        Then the "Vital Pod Block with Nested Reference Arrays" titled page is displayed
        And the user selects the "provider" bound vital pod field on the main page
        And the user clicks in the "Addresses" labelled nested multi reference field of the vital pod field
        And at least the following list of options is displayed "Barcelona Office|London Office|Paris Office" in the "Addresses" labelled nested multi reference field of the vital pod field
        And the user clears the "Addresses" labelled nested multi reference field of the vital pod field
        Then the value of the "Addresses" labelled nested multi reference field in the vital pod field is ""
        And the user writes "Ando" in the "Addresses" labelled nested multi reference field of the vital pod field
        And the user selects "Andorra Office|Andorra Warehouse" in the "Addresses" labelled nested multi reference field of the vital pod field
        Then the value of the "Addresses" labelled nested multi reference field in the vital pod field is "Andorra Office|Andorra Warehouse"
        And the user clears the "Addresses" labelled nested multi reference field of the vital pod field
        Then the value of the "Addresses" labelled nested multi reference field in the vital pod field is ""
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> - As an ATP XTreeM user I can interact with multi dropdown in vital pod fields
        XT-44680
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/NestedVitalPodBlock/eyJfaWQiOiIzNCJ9"
        Then the "Vital Pod Block with Nested Reference Arrays" titled page is displayed
        And the user selects the "provider" bound vital pod field on the main page
        And the user clicks in the "Ratings" labelled nested multi dropdown field of the vital pod field
        And at least the following list of options is displayed "Average|Excellent|Good" in the "Ratings" labelled nested multi dropdown field of the vital pod field
        And the user selects "Average | Excellent" in the "Ratings" labelled nested multi dropdown field of the vital pod field
        Then the value of the "Ratings" labelled nested multi dropdown field in the vital pod field is "Average, Excellent"
        And the user clears the "Ratings" labelled nested multi dropdown field of the vital pod field
        Then the value of the "Ratings" labelled nested multi dropdown field in the vital pod field is ""
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

# Scenario Outline: <Device> - As a user I want to verify the error message for a missing pod
#     Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/VitalPodField/eyJfaWQiOiIyMSJ9"
#     And the user selects the "Not Existing" labelled vital pod field on the main page
#     When the user writes "Test" in the "line2" labelled nested text field of the vital pod field
#     Examples:
#         | Device  |
#         | desktop |

# Scenario Outline: <Device> - As a user I want to verify the error message for a missing nested field in the pod
#     Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/VitalPodField/eyJfaWQiOiIyMSJ9"
#     And the user selects the "Address" labelled vital pod field on the main page
#     When the user writes "Test" in the "Not Existing" labelled nested text field of the vital pod field
#     Examples:
#         | Device  |
#         | desktop |

# Scenario Outline: <Device> - As a user I want to verify the error message for an incorrect value in the nested field in the pod
#     Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/VitalPodField/eyJfaWQiOiIyMSJ9"
#     And the user selects the "Address" labelled vital pod field on the main page
#     Then the value of the "name" bound nested text field in the vital pod field is "IncorrectValue"
#     Examples:
#         | Device  |
#         | desktop |

# #########
# export TARGET_URL="https://login.eu.dev-sagextrem.com/unsecuredevlogin?cluster=cluster-ci&tenant=Mi3rlSNd5hwAqy5or3gTT&user=<EMAIL>"
# #########
# Scenario: desktop - As a user I want to check a value in the text area field in the vital pod
#     Given the user opens the application on a desktop using the following link: "@sage/xtrem-purchasing/PurchaseOrder"
#     Then the "PURCHASE ORDER" subtitled page is displayed
#     And the user selects the "Supplier" labelled reference field on the main page
#     When the user writes "Carlson Filtration" in the reference field
#     And the user clicks the lookup button of the reference field
#     Then the value of the "Name" labelled nested text field of row 1 in the "supplier" bound table field is "Carlson Filtration"
#     And the user clicks on the "Name" labelled nested text field of row 1 of the "supplier" bound table field in a modal
#     Then the value of the reference field is "Carlson Filtration"
#     Then the value of the "concatenatedAddress" bound nested text area field in the vital pod field is "Carlson Filtration 136 Wexford Run Road SEWICKLEY PA 15143 United States of America"
#     Then the value of the "locationPhoneNumber" bound nested text field in the vital pod field is "7244443819"
