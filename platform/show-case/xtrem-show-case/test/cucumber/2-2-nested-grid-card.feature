Feature: 2-2-nested-grid-card
    # Tests the nested grid interaction rendered as nested grid cards across different devices (mobile / tablet).


    Scenario Outline: <Device> - As a user I want to display or hide the title of the nested grid card field
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/NestedGrid/eyJfaWQiOiI2In0="
        And the user selects the "field" bound nested grid field on the main page
        And the user selects the "Title" labelled text field on the main page
        When the user writes "this is the title" in the text field
        Then the title of the nested grid field is displayed
        # And takes a screenshot
        And the title of the nested grid field is "this is the title"
        And the user selects the "Is title hidden" labelled checkbox field on the main page
        And the user clicks in the checkbox field
        Then the title of the nested grid field is hidden
        # And takes a screenshot
        Examples:
            | Device |
            | tablet |
            | mobile |


    Scenario Outline: <Device> - As a user I want to display or hide the helper text of the nested grid card field
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/NestedGrid/eyJfaWQiOiI2In0="
        And the user selects the "field" bound nested grid field on the main page
        And the user selects the "Helper text" labelled text field on the main page
        When the user writes "this is the helper text" in the text field
        Then the helper text of the nested grid field is displayed
        And the helper text of the nested grid field is "this is the helper text"
        # And takes a screenshot
        When the user selects the "Is helper text hidden" labelled checkbox field on the main page
        And the user clicks in the checkbox field
        Then the helper text of the nested grid field is hidden
        # And takes a screenshot
        Examples:
            | Device |
            | tablet |
            | mobile |


    Scenario Outline: <Device> - As a user I want to display or hide the nested grid card field
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/NestedGrid/eyJfaWQiOiI2In0="
        And the user selects the "field" bound nested grid field on the main page
        And the selected nested grid field is displayed
        # And takes a screenshot
        When the user selects the "Is hidden" labelled checkbox field on the main page
        And the user clicks in the checkbox field
        Then the selected nested grid field is hidden
        # And takes a screenshot
        Examples:
            | Device |
            | tablet |
            | mobile |


    Scenario Outline: <Device> - As a user I can click the dropdown action of the nested grid card
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/NestedGrid/eyJfaWQiOiI2In0="
        And the user selects the "field" bound nested grid field on the main page
        Then the value of the "_id" bound nested text field of the card 1 in the nested grid field is "127"
        And the user clicks the card 1 in the nested grid field
        Then the value of the "_id" bound nested text field of the card 2 in the nested grid field is "371"
        And the user clicks the card 2 in the nested grid field
        # And takes a screenshot
        And the "Random Action" dropdown action of the card 1 in the nested grid field is enabled
        And the user clicks the "Random Action" dropdown action of the card 1 in the nested grid field
        And a toast containing text "You triggered the action on 892 - Soup - Campbells Chili Veg" is displayed
        Examples:
            | Device |
            | tablet |
            | mobile |


    Scenario Outline: <Device> - As a user can navigate to the next / previous level of the nested grid card
        XT-89822
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/NestedGrid/eyJfaWQiOiI2In0="
        And the user selects the "field" bound nested grid field on the main page
        Then the value of the "_id" bound nested text field of the card 1 in the nested grid field is "127"

        # navigate to the next levels and check onLevelExpanded defined event is triggered and corresponding level data is displayed
        When the user clicks the card 1 in the nested grid field
        Then the value of the toast is "Expanded order with ID 127 and 8 children"
        And the value in the header of the mobile nested grid field is "127"
        And the level in the header of the mobile nested grid field is "2/3"
        # And takes a screenshot
        And the value of the "_id" bound nested text field of the card 1 in the nested grid field is "472"
        When the user dismisses all the toasts
        And the user clicks the card 1 in the nested grid field
        Then the value of the toast is "Expanded invoice with ID 472 and 2 children"
        And the value in the header of the mobile nested grid field is "Maggio; Stamm and Torp"
        And the level in the header of the mobile nested grid field is "3/3"
        And the value of the "_id" bound nested text field of the card 1 in the nested grid field is "290"

        # navigate to previous levels and check the corresponding level data is displayed
        When the user clicks the back arrow in the header of the mobile nested grid field
        Then the value of the "_id" bound nested text field of the card 1 in the nested grid field is "472"
        When the user clicks the back arrow in the header of the mobile nested grid field
        Then the value of the "_id" bound nested text field of the card 1 in the nested grid field is "127"
        # And takes a screenshot
        Examples:
            | Device |
            | tablet |
            | mobile |

    Scenario Outline: <Device> - As a user I want to tick or untick the main checkbox of a nested grid card
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/NestedGrid/eyJfaWQiOiI2In0="
        When the user selects the "field" bound nested grid field on the main page
        Then the value of the "_id" bound nested text field of the card 1 in the nested grid field is "127"
        When the user ticks the main checkbox of the card with the text "127" in the nested grid field
        Then all the cards in the nested grid field are selected
        And a toast containing text "Order with ID 127 (2021-01-01 | ) SELECTED." is displayed
        When the user unticks the main checkbox of the card with the text "127" in the nested grid field
        Then all the cards in the nested grid field are unselected
        And a toast containing text "Order with ID 127 (2021-01-01 | ) UNSELECTED." is displayed
        Examples:
            | Device |
            | tablet |
            | mobile |


    Scenario Outline: <Device> - As a user I can verify the value of the nested grid card fields
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/NestedGrid/eyJfaWQiOiI2In0="
        And the user selects the "field" bound nested grid field on the main page
        Then the value of the "_id" bound nested text field of the card 1 in the nested grid field is "127"
        Then the value of the "orderDate" bound nested date field of the card 1 in the nested grid field is "01/01/2021"
        # And takes a screenshot
        Examples:
            | Device |
            | tablet |
            | mobile |


    # Scenario Outline: <Device> - As a user I can add a line to the nested grid card
    #     Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/NestedGrid/eyJfaWQiOiI2In0="
    #     And the user selects the "activatableField" bound nested grid field on the main page
    #     And the user adds a new row to the nested grid field
    #     And the user selects the "Id" labelled text field on the sidebar
    #     Then the value of the text field is "-1"
    #     # And takes a screenshot
    #     Examples:
    #         | Device |
    #         | tablet |

    Scenario Outline: <Device> - As a user I can add a line to the nested grid card
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/NestedGrid/eyJfaWQiOiI2In0="
        And the user selects the "activatableField" bound nested grid field on the main page
        And the user adds a new row to the nested grid field
        And the user selects the "Id" labelled text field on the mobile sidebar
        Then the value of the text field is "-1"
        # And takes a screenshot
        Examples:
            | Device |
            | mobile |

    Scenario: Mobile - As a user I can verify all the cards in the nested grid field are selected / unselected
        Given the user opens the application on a mobile using the following link: "@sage/xtrem-show-case/NestedGrid/eyJfaWQiOiI5In0="
        Then the titled page containing "Field - NestedGrid" is displayed
        And the user selects the "field" bound nested grid field on the main page
        Then all the cards in the nested grid field are unselected
        And the user ticks the main checkbox of the card 1 in the nested grid field
        And the user ticks the main checkbox of the card 2 in the nested grid field
        Then all the cards in the nested grid field are selected
