Feature: 3-2 Smoke test
    # Tests basic application functionality by verifying that all key pages load correctly without errors, ensuring core system stability

    Scenario Outline: As a user I want to make sure that each page loads without any problems on desktop
        Given the user opens the application on a desktop using the following link: <Page>
        Then the <Title> titled page is displayed

        Examples:
            | Page                                               | Title                                             |
            | "@sage/xtrem-show-case/BindProperty"               | "Bind property"                                   |
            | "@sage/xtrem-show-case/CrudUpdateFile"             | "CRUD - Update"                                   |
            | "@sage/xtrem-show-case/CrudUpdate"                 | "CRUD - Update"                                   |
            | "@sage/xtrem-show-case/Dialogs"                    | "Dialogs"                                         |
            | "@sage/xtrem-show-case/Interactions"               | "Event listeners"                                 |
            | "@sage/xtrem-show-case/Aggregate"                  | "Field - Aggregate"                               |
            | "@sage/xtrem-show-case/Button"                     | "Field - Button"                                  |
            | "@sage/xtrem-show-case/Calendar"                   | "Field - Calendar"                                |
            | "@sage/xtrem-show-case/Chart"                      | "Field - Chart"                                   |
            | "@sage/xtrem-show-case/ChartPie"                   | "Field - Chart - Pie"                             |
            | "@sage/xtrem-show-case/Checkbox"                   | "Field - Checkbox"                                |
            | "@sage/xtrem-show-case/Count"                      | "Field - Count"                                   |
            | "@sage/xtrem-show-case/DateField"                  | "Field - Date"                                    |
            | "@sage/xtrem-show-case/DetailList"                 | "Field - Detail list"                             |
            | "@sage/xtrem-show-case/DropdownList"               | "Field - Dropdown List"                           |
            | "@sage/xtrem-show-case/FileUpload"                 | "Field - File Upload"                             |
            | "@sage/xtrem-show-case/Icon"                       | "Field - Icon"                                    |
            | "@sage/xtrem-show-case/Label"                      | "Field - Label"                                   |
            | "@sage/xtrem-show-case/Link"                       | "Field - Link"                                    |
            | "@sage/xtrem-show-case/NestedPodBlock"             | "Pod Block with Nested Reference Arrays"          |
            | "@sage/xtrem-show-case/NestedPodCollection"        | "Pod Collection With nested Reference Arrays"     |
            | "@sage/xtrem-show-case/NestedPodField"             | "Pod Field with Nested Reference Arrays"          |
            | "@sage/xtrem-show-case/NestedGrid"                 | "Field - NestedGrid"                              |
            | "@sage/xtrem-show-case/NestedVitalPodBlock"        | "Vital Pod Block with Nested Reference Arrays"    |
            | "@sage/xtrem-show-case/NestedVitalPod"             | "Vital Pod Field with Nested Reference Arrays"    |
            | "@sage/xtrem-show-case/Numeric"                    | "Numeric"                                         |
            | "@sage/xtrem-show-case/PluginGraphiql"             | "Field - GraphiQL Plugin"                         |
            | "@sage/xtrem-show-case/PluginMonaco"               | "Field - Monaco Plugin"                           |
            | "@sage/xtrem-show-case/PluginPdf"                  | "Field - PDF Plugin"                              |
            | "@sage/xtrem-show-case/Progress"                   | "Field - Progress"                                |
            | "@sage/xtrem-show-case/RadioButtons"               | "Field - Radio"                                   |
            | "@sage/xtrem-show-case/Reference"                  | "Field - Reference"                               |
            | "@sage/xtrem-show-case/RichText"                   | "Field - Rich text"                               |
            | "@sage/xtrem-show-case/Select"                     | "Field - Select"                                  |
            | "@sage/xtrem-show-case/Switch"                     | "Switch"                                          |
            | "@sage/xtrem-show-case/Separator"                  | "Separator"                                       |
            | "@sage/xtrem-show-case/Table"                      | "Field - Table"                                   |
            | "@sage/xtrem-show-case/TableDefaultValues"         | "Field - Table (Default Values)"                  |
            | "@sage/xtrem-show-case/TableNestedReference"       | "Field - Table (nested reference)"                |
            | "@sage/xtrem-show-case/TransientTable"             | "Field - Table (Transient)"                       |
            | "@sage/xtrem-show-case/TableCardView"              | "Field - Table card view"                         |
            | "@sage/xtrem-show-case/Text"                       | "Text"                                            |
            | "@sage/xtrem-show-case/TextArea"                   | "Text area"                                       |
            | "@sage/xtrem-show-case/ToggleButtons"              | "Field - Toggle Buttons"                          |
            | "@sage/xtrem-show-case/ShowCaseEmployee"           | "Employees"                                       |
            | "@sage/xtrem-show-case/Focus"                      | "Fields focus"                                    |
            | "@sage/xtrem-show-case/NavigationPanel"            | "Navigation Panel"                                |
            | "@sage/xtrem-show-case/NavigationPanelOnly"        | "Navigation Panel - No page body, only nav panel" |
            | "@sage/xtrem-show-case/NavigationPanelComplexCard" | "Navigation Panel - Complex Card"                 |
            | "@sage/xtrem-show-case/Toasts"                     | "Toasts"                                          |
            | "@sage/xtrem-show-case/ActionButtons"              | "Page - Action Buttons"                           |
            | "@sage/xtrem-show-case/ConditionalValidation"      | "Page - Conditional validation"                   |
            | "@sage/xtrem-show-case/PageDefaultCrudActions"     | "Page - Default CRUD Actions"                     |
            | "@sage/xtrem-show-case/PageDirtyState"             | "Page - Dirty state on fields"                    |
            | "@sage/xtrem-show-case/PageHeaderCard"             | "Two lines"                                       |
            | "@sage/xtrem-show-case/DetailPanel"                | "Page - Detail panel"                             |
            | "@sage/xtrem-show-case/Wizard"                     | "Page - Wizard Layout"                            |
            | "@sage/xtrem-show-case/Responsiveness"             | "Responsiveness"                                  |
            | "@sage/xtrem-show-case/ShowCaseProduct"            | "Products"                                        |
            | "@sage/xtrem-show-case/ShowCaseProvider"           | "Providers"                                       |
            | "@sage/xtrem-show-case/Alignment"                  | "Sections - Alignment"                            |

    Scenario: As a user I want to make sure that an error dialog appears if the page does not load on desktop
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Wazup"
        Then an error dialog appears on the main page

    @ClearDashboardsBefore
    Scenario: As a user with special requirements would like to use the landing page
        Given the user opens the application on a desktop
        And the dashboard page is displayed
        Then the "Create a dashboard to get started." subtitled empty dashboard is displayed
        And the user executes an accessibility tests scan
        And the page does not have any "critical" accessibility violations
        And the page does not have any "serious" accessibility violations

    Scenario: As a user with special requirements would like to use the product show case page
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseProduct"
        Then the "Products" titled page is displayed
        When the user executes an accessibility tests scan
        Then the page does not have any "critical" accessibility violations
        And the page does not have any "serious" accessibility violations
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        And the user clicks the "Product" labelled nested field of the selected row in the table field
        Then the "Product Anisette - Mcguiness" titled page is displayed
        When the user executes an accessibility tests scan
        Then the page does not have any "critical" accessibility violations
        And the page does not have any "serious" accessibility violations
