Feature: 3-2 Selection card
    # Tests the selection card component across different devices, verifying selection/deselection behavior and state changes

    Scenario Outline: As a user I want to interact with a selection card on a page
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/SelectionCard"
        Then the "Field - SelectionCard" titled page is displayed
        Given the user selects the "inlineOption" bound selection card field on the main page
        When the user selects the card with "giraffe" value in the selection card
        Then the card with "giraffe" value in the selection card is selected
        And the card with "dog" value in the selection card is unselected
        When the user selects the card with "dog" value in the selection card
        Then the card with "dog" value in the selection card is selected
        And the card with "giraffe" value in the selection card is unselected
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |
