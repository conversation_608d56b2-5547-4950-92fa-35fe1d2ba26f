Feature: 2-1 G custom fields 2
    # Tests advanced custom field functionality, verifying custom field display in tables, sidebars, and proper data binding with custom fields

    Scenario: As a user I want to declare a custom field and see it in table and sidebar
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-customization/CustomField/eyJfaWQiOiIkbmV3In0="
        Then the "Custom field" titled page is displayed
        And the user selects the "Record type *" labelled reference field on the main page
        When the user clicks the lookup button of the reference field
        And the user selects the "node" bound table field on a modal
        When the user filters the "Record type" labelled column in the table field with value "Show Case Product"
        And the user selects the row 1 of the table field
        And the user clicks the "Record type" labelled nested field of the selected row in the table field
        And the user selects the "Technical name" labelled text field on the main page
        When the user writes "CustomTextField" in the text field
        And the user selects the "Anchor field *" labelled reference field on the main page
        When the user clicks the lookup button of the reference field
        And the user selects the "anchorProperty" bound table field on a modal
        When the user filters the "`Technical name" labelled column in the table field with value "Description"
        And the user selects the row 1 of the table field
        And the user clicks the "Technical name" labelled nested field of the selected row in the table field
        Then the user selects the "Display also on" labelled multi dropdown field on the main page
        When the user clicks in the multi dropdown field
        And the user selects "Page" in the multi dropdown field
        When the user selects the "Data type" labelled dropdown-list field on the main page
        When the user clicks in the dropdown-list field
        And the user selects "String" in the dropdown-list field
        And the user selects the "Field type" labelled dropdown-list field on the main page
        Then the value of the dropdown-list field is "Text field"
        And the user clicks the "Save" labelled business action button on the main page
        And the user navigates to the following link: "@sage/xtrem-show-case/TableWithSidebar/eyJfaWQiOiIyIn0="
        Then the "Field - Table - With sidebar" titled page is displayed
        When the user selects the "field" bound table field on the main page
        And the user selects the row 1 of the table field
        Then the value of the "Custom Text Field" labelled nested text field of the selected row in the table field is ""
        When the user clicks the "Edit on sidebar" dropdown action of the selected row of the table field
        And the user selects the "Custom Text Field" labelled text field on the sidebar
        And the user writes "UPDATED" in the text field
        And the user clicks the "Apply" button of the dialog on the sidebar
        And the user selects the row 1 of the table field
        Then the value of the "Custom Text Field" labelled nested text field of the selected row in the table field is "UPDATED"
        When the user clicks the "Edit on sidebar" dropdown action of the selected row of the table field
        And the user selects the "Custom Text Field" labelled text field on the sidebar
        Then the value of the text field is "UPDATED"

        # Cleanup
        When the user clicks the "Cancel" button of the dialog on the sidebar
        And the user navigates to the following link: "@sage/xtrem-customization/CustomField"
        Then the "Custom fields" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        When the user filters the "name" bound column in the table field with value "CustomTextField"
        And the user selects the row 1 of the table field
        And the user clicks the "name" bound nested field of the selected row in the table field
        And the user waits 1 second
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        And the user clicks the "Delete" button of the Confirm dialog
        Then a toast containing text "Record deleted" is displayed

    Scenario: As a user I want to create a custom text field for a non-vital collection
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-customization/CustomField"
        Then the "Custom fields" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user clicks the "Create" labelled business action button of the table field
        Then the text in the header of the dialog is "Create custom field"
        When the user selects the "Record type *" labelled reference field on a modal
        And the user clicks the lookup button of the reference field
        And the user selects the "node" bound table field on a modal
        When the user filters the "Record type" labelled column in the table field with value "Show case employee"
        When the user selects the row 1 of the table field
        And the user clicks the "Record type" labelled nested field of the selected row in the table field
        And the user clicks the "Next" labelled business action button on a modal
        And the user selects the "Field label *" labelled text field on a modal
        And the user writes "custom text field" in the text field
        And the user selects the "Field type" labelled dropdown-list field on a modal
        And the user clicks in the dropdown-list field
        And the user selects "Text field" in the dropdown-list field
        And the user clicks the "Next" labelled business action button on a modal
        And the user selects the "Anchor field *" labelled reference field on a modal
        And the user clicks the lookup button of the reference field
        And the user selects the "anchorProperty" bound table field on a modal
        And the user filters the "Name" labelled column in the table field with value "First name"
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        And the user selects the "Position relative to anchor field *" labelled dropdown-list field on a modal
        And the user clicks in the dropdown-list field
        And the user selects "Before" in the dropdown-list field
        And the user selects the "Display on *" labelled multi dropdown field on a modal
        Then the value of the multi dropdown field is "Page"
        When the user clicks the "Next" labelled business action button on a modal
        And the user clicks the "Next" labelled business action button on a modal
        And the user selects the "Required" labelled checkbox field on a modal
        And the user ticks the checkbox field
        And the user clicks the "Finish" labelled business action button on a modal
        Then the "Custom field ShowCaseEmployee - customTextField" titled page is displayed
        When the user navigates to the following link: "@sage/xtrem-show-case/ShowCaseCountry/eyJfaWQiOiIxIn0="
        Then the "Country ES" titled page is displayed
        When the user selects the "employeesInCountry" bound table field on the main page
        Then the "custom text field " labelled column in the table field is displayed
        And the "custom text field" labelled nested text field of the selected row in the table field is readonly

        # Cleanup
        And the user navigates to the following link: "@sage/xtrem-customization/CustomField"
        Then the "Custom fields" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        When the user filters the "name" bound column in the table field with value "customTextField"
        And the user clicks the "name" bound nested field of the selected row in the table field
        And the user waits 1 second
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        And the user clicks the "Delete" button of the Confirm dialog
        Then a toast containing text "Record deleted" is displayed


    Scenario: As a user I want to create a custom numeric field for a non-vital collection
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-customization/CustomField"
        Then the "Custom fields" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user clicks the "Create" labelled business action button of the table field
        Then the text in the header of the dialog is "Create custom field"
        When the user selects the "Record type *" labelled reference field on a modal
        And the user clicks the lookup button of the reference field
        And the user selects the "node" bound table field on a modal
        When the user filters the "Record type" labelled column in the table field with value "Show case employee"
        And the user selects the row 1 of the table field
        And the user clicks the "Record type" labelled nested field of the selected row in the table field
        And the user clicks the "Next" labelled business action button on a modal
        And the user selects the "Field label *" labelled text field on a modal
        And the user writes "custom numeric field" in the text field
        And the user selects the "Field type" labelled dropdown-list field on a modal
        And the user clicks in the dropdown-list field
        And the user selects "Numeric field" in the dropdown-list field
        And the user clicks the "Next" labelled business action button on a modal
        And the user selects the "Anchor field *" labelled reference field on a modal
        And the user clicks the lookup button of the reference field
        And the user selects the "anchorProperty" bound table field on a modal
        And the user filters the "Name" labelled column in the table field with value "First name"
        And the user selects the row 1 of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        And the user selects the "Position relative to anchor field *" labelled dropdown-list field on a modal
        And the user clicks in the dropdown-list field
        And the user selects "Before" in the dropdown-list field
        And the user selects the "Display on *" labelled multi dropdown field on a modal
        Then the value of the multi dropdown field is "Page"
        When the user clicks the "Next" labelled business action button on a modal
        And the user clicks the "Next" labelled business action button on a modal
        And the user selects the "Required" labelled checkbox field on a modal
        And the user ticks the checkbox field
        And the user clicks the "Finish" labelled business action button on a modal
        Then the "Custom field ShowCaseEmployee - customNumericField" titled page is displayed
        When the user navigates to the following link: "@sage/xtrem-show-case/ShowCaseCountry/eyJfaWQiOiIxIn0="
        Then the "Country ES" titled page is displayed
        When the user selects the "employeesInCountry" bound table field on the main page
        Then the "custom numeric field " labelled column in the table field is displayed
        And the user selects the row 1 of the table field
        And the "custom numeric field" labelled nested numeric field of the selected row in the table field is readonly

        # Cleanup
        And the user navigates to the following link: "@sage/xtrem-customization/CustomField"
        Then the "Custom fields" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        When the user filters the "name" bound column in the table field with value "customNumericField"
        And the user selects the row 1 of the table field
        And the user clicks the "name" bound nested field of the selected row in the table field
        And the user waits 1 second
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        And the user clicks the "Delete" button of the Confirm dialog
        Then a toast containing text "Record deleted" is displayed


    Scenario: As a user I want to create a custom date field for a non-vital collection
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-customization/CustomField"
        Then the "Custom fields" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user clicks the "Create" labelled business action button of the table field
        Then the text in the header of the dialog is "Create custom field"
        When the user selects the "Record type *" labelled reference field on a modal
        And the user clicks the lookup button of the reference field
        And the user selects the "node" bound table field on a modal
        When the user filters the "Record type" labelled column in the table field with value "Show case employee"
        And the user selects the row 1 of the table field
        And the user clicks the "Record type" labelled nested field of the selected row in the table field
        And the user clicks the "Next" labelled business action button on a modal
        And the user selects the "Field label *" labelled text field on a modal
        And the user writes "custom date field" in the text field
        And the user selects the "Field type" labelled dropdown-list field on a modal
        And the user clicks in the dropdown-list field
        And the user selects "Date" in the dropdown-list field
        And the user clicks the "Next" labelled business action button on a modal
        And the user selects the "Anchor field *" labelled reference field on a modal
        And the user clicks the lookup button of the reference field
        And the user selects the "anchorProperty" bound table field on a modal
        And the user filters the "Name" labelled column in the table field with value "First name"
        And the user selects the row 1 of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        And the user selects the "Position relative to anchor field *" labelled dropdown-list field on a modal
        And the user clicks in the dropdown-list field
        And the user selects "Before" in the dropdown-list field
        And the user selects the "Display on *" labelled multi dropdown field on a modal
        Then the value of the multi dropdown field is "Page"
        When the user clicks the "Next" labelled business action button on a modal
        And the user clicks the "Next" labelled business action button on a modal
        And the user selects the "Required" labelled checkbox field on a modal
        And the user ticks the checkbox field
        And the user clicks the "Finish" labelled business action button on a modal
        Then the "Custom field ShowCaseEmployee - customDateField" titled page is displayed
        When the user navigates to the following link: "@sage/xtrem-show-case/ShowCaseCountry/eyJfaWQiOiIxIn0="
        Then the "Country ES" titled page is displayed
        When the user selects the "employeesInCountry" bound table field on the main page
        Then the "custom date field " labelled column in the table field is displayed
        And the user selects the row 1 of the table field
        And the "custom date field" labelled nested date field of the selected row in the table field is readonly

        # Cleanup
        And the user navigates to the following link: "@sage/xtrem-customization/CustomField"
        Then the "Custom fields" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        When the user filters the "name" bound column in the table field with value "customDateField"
        And the user selects the row 1 of the table field
        And the user clicks the "name" bound nested field of the selected row in the table field
        And the user waits 1 second
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        And the user clicks the "Delete" button of the Confirm dialog
        Then a toast containing text "Record deleted" is displayed

    Scenario: As a user I want to create a custom switch field for a non-vital collection
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-customization/CustomField"
        Then the "Custom fields" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user clicks the "Create" labelled business action button of the table field
        Then the text in the header of the dialog is "Create custom field"
        When the user selects the "Record type *" labelled reference field on a modal
        And the user clicks the lookup button of the reference field
        And the user selects the "node" bound table field on a modal
        When the user filters the "Record type" labelled column in the table field with value "Show case employee"
        And the user selects the row 1 of the table field
        And the user clicks the "Record type" labelled nested field of the selected row in the table field
        And the user clicks the "Next" labelled business action button on a modal
        And the user selects the "Field label *" labelled text field on a modal
        And the user writes "custom switch field" in the text field
        And the user selects the "Field type" labelled dropdown-list field on a modal
        And the user clicks in the dropdown-list field
        And the user selects "On/Off switch" in the dropdown-list field
        And the user clicks the "Next" labelled business action button on a modal
        And the user selects the "Anchor field *" labelled reference field on a modal
        And the user clicks the lookup button of the reference field
        And the user selects the "anchorProperty" bound table field on a modal
        And the user filters the "Name" labelled column in the table field with value "First name"
        And the user selects the row 1 of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        And the user selects the "Position relative to anchor field *" labelled dropdown-list field on a modal
        And the user clicks in the dropdown-list field
        And the user selects "Before" in the dropdown-list field
        And the user selects the "Display on *" labelled multi dropdown field on a modal
        Then the value of the multi dropdown field is "Page"
        When the user clicks the "Next" labelled business action button on a modal
        And the user clicks the "Next" labelled business action button on a modal
        And the user selects the "Required" labelled checkbox field on a modal
        And the user ticks the checkbox field
        And the user clicks the "Finish" labelled business action button on a modal
        Then the "Custom field ShowCaseEmployee - customSwitchField" titled page is displayed
        When the user navigates to the following link: "@sage/xtrem-show-case/ShowCaseCountry/eyJfaWQiOiIxIn0="
        Then the "Country ES" titled page is displayed
        When the user selects the "employeesInCountry" bound table field on the main page
        Then the "custom switch field " labelled column in the table field is displayed
        And the user selects the row 1 of the table field
        And the "custom switch field" labelled nested switch field of the selected row in the table field is readonly

        # Cleanup
        And the user navigates to the following link: "@sage/xtrem-customization/CustomField"
        Then the "Custom fields" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        When the user filters the "name" bound column in the table field with value "customSwitchField"
        And the user selects the row 1 of the table field
        And the user clicks the "name" bound nested field of the selected row in the table field
        And the user waits 1 second
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        And the user clicks the "Delete" button of the Confirm dialog
        Then a toast containing text "Record deleted" is displayed


    Scenario: As a user I want to create a custom checkbox field for a non-vital collection
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-customization/CustomField"
        Then the "Custom fields" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user clicks the "Create" labelled business action button of the table field
        Then the text in the header of the dialog is "Create custom field"
        When the user selects the "Record type *" labelled reference field on a modal
        And the user clicks the lookup button of the reference field
        And the user selects the "node" bound table field on a modal
        When the user filters the "Record type" labelled column in the table field with value "Show case employee"
        And the user selects the row 1 of the table field
        And the user clicks the "Record type" labelled nested field of the selected row in the table field
        And the user clicks the "Next" labelled business action button on a modal
        And the user selects the "Field label *" labelled text field on a modal
        And the user writes "custom checkbox field" in the text field
        And the user selects the "Field type" labelled dropdown-list field on a modal
        And the user clicks in the dropdown-list field
        And the user selects "Checkbox" in the dropdown-list field
        And the user clicks the "Next" labelled business action button on a modal
        And the user selects the "Anchor field *" labelled reference field on a modal
        And the user clicks the lookup button of the reference field
        And the user selects the "anchorProperty" bound table field on a modal
        And the user filters the "Name" labelled column in the table field with value "Last name"
        And the user selects the row 1 of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        And the user selects the "Position relative to anchor field *" labelled dropdown-list field on a modal
        And the user clicks in the dropdown-list field
        And the user selects "After" in the dropdown-list field
        And the user selects the "Display on *" labelled multi dropdown field on a modal
        Then the value of the multi dropdown field is "Page"
        When the user clicks the "Next" labelled business action button on a modal
        And the user clicks the "Next" labelled business action button on a modal
        And the user selects the "Required" labelled checkbox field on a modal
        And the user ticks the checkbox field
        And the user clicks the "Finish" labelled business action button on a modal
        Then the "Custom field ShowCaseEmployee - customCheckboxField" titled page is displayed
        When the user navigates to the following link: "@sage/xtrem-show-case/ShowCaseCountry/eyJfaWQiOiIxIn0="
        Then the "Country ES" titled page is displayed
        When the user selects the "employeesInCountry" bound table field on the main page
        Then the "custom checkbox field " labelled column in the table field is displayed
        And the user selects the row 1 of the table field
        And the "custom checkbox field" labelled nested checkbox field of the selected row in the table field is readonly

        # Cleanup
        And the user navigates to the following link: "@sage/xtrem-customization/CustomField"
        Then the "Custom fields" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        When the user filters the "name" bound column in the table field with value "customCheckboxField"
        And the user selects the row 1 of the table field
        And the user clicks the "name" bound nested field of the selected row in the table field
        And the user waits 1 second
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        And the user clicks the "Delete" button of the Confirm dialog
        Then a toast containing text "Record deleted" is displayed

    # XT-70024: be carefull this test interweasved with ofther feature when they are split in different feature files and executed in parallel
    Scenario: As a user I want to create a custom dropdown list field for a non-vital collection
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-customization/CustomField"
        Then the "Custom fields" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user clicks the "Create" labelled business action button of the table field
        Then the text in the header of the dialog is "Create custom field"
        When the user selects the "Record type *" labelled reference field on a modal
        And the user clicks the lookup button of the reference field
        And the user selects the "node" bound table field on a modal
        When the user filters the "Record type" labelled column in the table field with value "Show case employee"
        And the user selects the row 1 of the table field
        And the user clicks the "Record type" labelled nested field of the selected row in the table field
        And the user clicks the "Next" labelled business action button on a modal
        And the user selects the "Field label *" labelled text field on a modal
        And the user writes "custom dropdown-list field" in the text field
        And the user selects the "Field type" labelled dropdown-list field on a modal
        And the user clicks in the dropdown-list field
        And the user selects "Drop-down list" in the dropdown-list field
        And the user clicks the "Next" labelled business action button on a modal
        And the user selects the "options" bound table field on a modal
        And the user adds a new table row to the table field
        And the user selects the floating row of the table field
        And the user writes "value1" in the "Drop-down list entry" labelled nested numeric field of the selected row in the table field
        And the user presses Control+Enter
        And the user writes "value2" in the "Drop-down list entry" labelled nested text field of the selected row in the table field
        And the user presses Control+Enter
        And the user clicks the "Next" labelled business action button on a modal
        And the user selects the "Anchor field *" labelled reference field on a modal
        And the user clicks the lookup button of the reference field
        And the user selects the "anchorProperty" bound table field on a modal
        And the user filters the "Name" labelled column in the table field with value "Last name"
        And the user selects the row 1 of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        And the user selects the "Position relative to anchor field *" labelled dropdown-list field on a modal
        And the user clicks in the dropdown-list field
        And the user selects "After" in the dropdown-list field
        And the user selects the "Display on *" labelled multi dropdown field on a modal
        Then the value of the multi dropdown field is "Page"
        When the user clicks the "Next" labelled business action button on a modal
        And the user clicks the "Next" labelled business action button on a modal
        And the user selects the "Required" labelled checkbox field on a modal
        And the user ticks the checkbox field
        And the user clicks the "Finish" labelled business action button on a modal
        Then the "Custom field ShowCaseEmployee - customDropdownListField" titled page is displayed
        When the user navigates to the following link: "@sage/xtrem-show-case/ShowCaseCountry/eyJfaWQiOiIxIn0="
        Then the "Country ES" titled page is displayed
        When the user selects the "employeesInCountry" bound table field on the main page
        Then the "custom dropdown-list field" labelled column in the table field is displayed
        And the user selects the row 1 of the table field
        And the "custom dropdown-list field" labelled nested checkbox field of the selected row in the table field is readonly

        # Cleanup
        And the user navigates to the following link: "@sage/xtrem-customization/CustomField"
        Then the "Custom fields" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        When the user filters the "name" bound column in the table field with value "customDropdownListField"
        And the user selects the row 1 of the table field
        And the user clicks the "name" bound nested field of the selected row in the table field
        And the user waits 1 second
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        And the user clicks the "Delete" button of the Confirm dialog
        Then a toast containing text "Record deleted" is displayed


    Scenario: As a user I want to edit custom fields on tables and sidebars that are anchored to a deep bound vital field
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-customization/CustomField"
        Then the "Custom fields" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user clicks the "Create" labelled business action button of the table field
        Then the text in the header of the dialog is "Create custom field"
        When the user selects the "Record type *" labelled reference field on a modal
        And the user clicks the lookup button of the reference field
        And the user selects the "node" bound table field on a modal
        When the user filters the "Record type" labelled column in the table field with value "Show case product origin address"
        And the user selects the row 1 of the table field
        And the user clicks the "Record type" labelled nested field of the selected row in the table field
        And the user clicks the "Next" labelled business action button on a modal
        And the user selects the "Field label *" labelled text field on a modal
        And the user writes "Custom deep bound field" in the text field
        And the user selects the "Field type" labelled dropdown-list field on a modal
        And the user clicks in the dropdown-list field
        And the user selects "Text field" in the dropdown-list field
        And the user clicks the "Next" labelled business action button on a modal
        And the user selects the "Anchor field *" labelled reference field on a modal
        And the user clicks the lookup button of the reference field
        And the user selects the "anchorProperty" bound table field on a modal
        And the user filters the "Name" labelled column in the table field with value "Address line 1"
        And the user selects the row 1 of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        And the user selects the "Position relative to anchor field *" labelled dropdown-list field on a modal
        And the user clicks in the dropdown-list field
        And the user selects "After" in the dropdown-list field
        And the user selects the "Display on *" labelled multi dropdown field on a modal
        Then the value of the multi dropdown field is "Page"
        When the user clicks the "Next" labelled business action button on a modal
        And the user clicks the "Next" labelled business action button on a modal
        And the user clicks the "Finish" labelled business action button on a modal
        Then the "Custom field ShowCaseProductOriginAddress - customDeepBoundField" titled page is displayed
        When the user navigates to the following link: "@sage/xtrem-show-case/TableDeepBoundColumns/eyJfaWQiOiIxIn0="
        Then the "Table - Deep cell binding" titled page is displayed
        When the user selects the "products" bound table field on the main page
        And the user selects the row 6 of the table field
        Then the value of the "Custom deep bound field" labelled nested text field of the selected row in the table field is ""
        When the user writes "Test value" in the "Custom deep bound field" labelled nested text field of the selected row in the table field
        And the user clicks the "Save" labelled business action button on the main page
        When the user navigates to the following link: "@sage/xtrem-show-case/TableDeepBoundColumns/eyJfaWQiOiIxIn0="
        Then the "Table - Deep cell binding" titled page is displayed
        When the user selects the "products" bound table field on the main page
        And the user selects the row 6 of the table field
        Then the value of the "Custom deep bound field" labelled nested text field of the selected row in the table field is "Test value"
        When the user writes "" in the "Custom deep bound field" labelled nested text field of the selected row in the table field
        And the user clicks the "Save" labelled business action button on the main page
        When the user navigates to the following link: "@sage/xtrem-show-case/TableDeepBoundColumns/eyJfaWQiOiIxIn0="
        Then the "Table - Deep cell binding" titled page is displayed
        When the user selects the "products" bound table field on the main page
        And the user selects the row 6 of the table field
        Then the value of the "Custom deep bound field" labelled nested text field of the selected row in the table field is ""
        And the user clicks the "Edit on sidebar" inline action button of the selected row in the table field
        And the user selects the "Custom deep bound field" labelled text field on the sidebar
        Then the value of the text field is ""
        When the user writes "Test value" in the text field
        When the user clicks the "Apply" button of the dialog on the sidebar
        And the user clicks the "Save" labelled business action button on the main page
        And the user waits 1 second
        And the user navigates to the following link: "@sage/xtrem-show-case/TableDeepBoundColumns/eyJfaWQiOiIxIn0="
        Then the "Table - Deep cell binding" titled page is displayed
        When the user selects the "products" bound table field on the main page
        And the user selects the row 6 of the table field
        Then the value of the "Custom deep bound field" labelled nested text field of the selected row in the table field is "Test value"
        When the user writes "" in the "Custom deep bound field" labelled nested text field of the selected row in the table field
        And the user clicks the "Save" labelled business action button on the main page

        # Cleanup
        And the user navigates to the following link: "@sage/xtrem-customization/CustomField"
        Then the "Custom fields" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        When the user filters the "name" bound column in the table field with value "customDeepBoundField"
        And the user selects the row 1 of the table field
        And the user clicks the "name" bound nested field of the selected row in the table field
        And the user waits 1 second
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        And the user clicks the "Delete" button of the Confirm dialog
        Then a toast containing text "Record deleted" is displayed


    # XT-70024: be carefull this test interweasved with ofther feature when they are split in different feature files and executed in parallel
    Scenario: As a user I want to edit custom fields on the page body and use it on the navigation panel
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-customization/CustomField"
        Then the "Custom fields" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user clicks the "Create" labelled business action button of the table field
        Then the text in the header of the dialog is "Create custom field"
        When the user selects the "Record type *" labelled reference field on a modal
        And the user clicks the lookup button of the reference field
        And the user selects the "node" bound table field on a modal
        When the user filters the "Record type" labelled column in the table field with value "Show case product origin address"
        And the user selects the row 1 of the table field
        And the user clicks the "Record type" labelled nested field of the selected row in the table field
        And the user clicks the "Next" labelled business action button on a modal
        And the user selects the "Field label *" labelled text field on a modal
        And the user writes "Custom deep text field" in the text field
        And the user selects the "Field type" labelled dropdown-list field on a modal
        And the user clicks in the dropdown-list field
        And the user selects "Text field" in the dropdown-list field
        And the user clicks the "Next" labelled business action button on a modal
        And the user selects the "Anchor field *" labelled reference field on a modal
        And the user clicks the lookup button of the reference field
        And the user selects the "anchorProperty" bound table field on a modal
        And the user filters the "Name" labelled column in the table field with value "Address line 1"
        And the user selects the row 1 of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        And the user selects the "Position relative to anchor field *" labelled dropdown-list field on a modal
        And the user clicks in the dropdown-list field
        And the user selects "After" in the dropdown-list field
        And the user selects the "Display on *" labelled multi dropdown field on a modal
        When the user clicks in the multi dropdown field
        And the user selects "Navigation bar" in the multi dropdown field
        When the user clicks the "Next" labelled business action button on a modal
        And the user clicks the "Next" labelled business action button on a modal
        And the user clicks the "Finish" labelled business action button on a modal
        Then the "Custom field ShowCaseProductOriginAddress - customDeepTextField" titled page is displayed


        When the user navigates to the following link: "@sage/xtrem-show-case/DeepBindingSimpleFields/eyJfaWQiOiIxNiJ9"
        Then the "Simple fields" titled page is displayed
        When the user selects the "Custom deep text field" labelled text field on the main page
        Then the text field appears
        When the user writes "Test value" in the text field
        And the user clicks the "Save" labelled business action button on the main page
        When the user navigates to the following link: "@sage/xtrem-show-case/DeepBindingSimpleFields"
        Then the "Simple fields" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        When the user clicks the "Custom deep text field" labelled column of the table field
        And the user clicks the "Custom deep text field" labelled column of the table field
        And the user selects the row 1 of the table field
        Then the value of the "product" labelled nested text field of the selected row in the table field is "Soup - Beef, Base Mix"
        Then the value of the "Custom deep text field" labelled nested text field of the selected row in the table field is "Test value"
        When the user clicks the "Product" labelled column of the table field
        And the user selects the row 1 of the table field
        Then the value of the "product" labelled nested text field of the selected row in the table field is "Anisette - Mcguiness"

        When the user filters the "Custom deep text field" labelled column in the table field with value "Test value"
        Then the value of the "product" labelled nested text field of the selected row in the table field is "Soup - Beef, Base Mix"

        When the user navigates to the following link: "@sage/xtrem-show-case/DeepBindingSimpleFields/eyJfaWQiOiIxNiJ9"
        When the user selects the "Custom deep text field" labelled text field on the main page
        Then the text field appears
        Then the value of the text field is "Test value"
        And the user clears the text field
        And the user clicks the "Save" labelled business action button on the main page

        # Cleanup
        And the user navigates to the following link: "@sage/xtrem-customization/CustomField"
        Then the "Custom fields" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        When the user filters the "name" bound column in the table field with value "customDeepTextField"
        And the user selects the row 1 of the table field
        And the user clicks the "name" bound nested field of the selected row in the table field
        And the user waits 1 second
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        And the user clicks the "Delete" button of the Confirm dialog
        Then a toast containing text "Record deleted" is displayed

    # XT-70024: be carefull this test interweasved with ofther feature when they are split in different feature files and executed in parallel
    Scenario: As a user I want to filter by a text custom field on tables and the main list
        # Create text field
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-customization/CustomField/eyJfaWQiOiIkbmV3In0="
        Then the "Custom field" titled page is displayed
        And the user selects the "Record type *" labelled reference field on the main page
        When the user clicks the lookup button of the reference field
        And the user selects the "node" bound table field on a modal
        And the user filters the "Record type" labelled column in the table field with value "Show Case Product"
        And the user selects the row 1 of the table field
        And the user clicks the "Record type" labelled nested field of the selected row in the table field
        And the user selects the "Technical name *" labelled text field on the main page
        When the user writes "filterTestTextField" in the text field
        And the user selects the "Anchor field *" labelled reference field on the main page
        When the user clicks the lookup button of the reference field
        And the user selects the "anchorProperty" bound table field on a modal
        And the user filters the "Technical name" labelled column in the table field with value "Description"
        And the user selects the row 1 of the table field
        And the user clicks the "Technical name" labelled nested field of the selected row in the table field
        Then the user selects the "Display also on" labelled multi dropdown field on the main page
        When the user clicks in the multi dropdown field
        And the user selects "Page | Navigation bar" in the multi dropdown field
        When the user selects the "Data type" labelled dropdown-list field on the main page
        When the user clicks in the dropdown-list field
        And the user selects "String" in the dropdown-list field
        And the user selects the "Title" labelled text field on the main page
        When the user writes "Simple text field" in the text field
        And the user clicks the "Save" labelled business action button on the main page

        # Define some test data to the test columns
        When the user navigates to the following link: "@sage/xtrem-show-case/ShowCaseProduct/eyJfaWQiOiI0OTUifQ=="
        Then the "Product Chocolate - Milk" titled page is displayed
        Then the user selects the "Simple text field" labelled text field on the main page
        When the user writes "abc" in the text field
        And the user waits 1 second
        And the user clicks the "Save" labelled business action button on the main page
        When the user navigates to the following link: "@sage/xtrem-show-case/ShowCaseProduct/eyJfaWQiOiI0NTgifQ=="
        Then the "Product Instant Coffee" titled page is displayed
        Then the user selects the "Simple text field" labelled text field on the main page
        When the user writes "cde" in the text field
        And the user waits 1 second
        And the user clicks the "Save" labelled business action button on the main page

        # Search on the main list by using the custom fields
        When the user navigates to the following link: "@sage/xtrem-show-case/ShowCaseProduct"
        Then the "Products" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user clicks the "Hide table filters" labelled button of the table field
        And the user filters the "Simple text field" labelled column in the table field with value "c"
        And the user selects the row 1 of the table field
        Then the value of the "Product" labelled nested text field of the selected row in the table field is "Chocolate - Milk"
        And the user selects the row 2 of the table field
        Then the value of the "Product" labelled nested text field of the selected row in the table field is "Instant Coffee"
        And the user filters the "Simple text field" labelled column in the table field with value "cd"

        # Search on record table by using the custom fields
        When the user navigates to the following link: "@sage/xtrem-show-case/ShowCaseProvider/eyJfaWQiOiIyIn0="
        Then the "Provider 2" titled page is displayed
        And the user selects the "Products" labelled table field on the main page
        And the user filters the "Simple text field" labelled column in the table field with value "c"
        And the user selects the row 1 of the table field
        Then the value of the "Product" labelled nested link field of the selected row in the table field is "Instant Coffee"
        And the user selects the row 2 of the table field
        Then the value of the "Product" labelled nested link field of the selected row in the table field is "Chocolate - Milk"
        And the user filters the "Simple text field" labelled column in the table field with value "cd"
        And the user selects the row 1 of the table field
        And the value of the "Product" labelled nested link field of the selected row in the table field is "Instant Coffee"


        # Cleanup
        Then the user navigates to the following link: "@sage/xtrem-customization/CustomField"
        Then the "Custom fields" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user filters the "name" bound column in the table field with value "filterTestTextField"
        And the user selects the row 1 of the table field
        And the user clicks the "name" bound nested field of the selected row in the table field
        And the user waits 1 second
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        And the user clicks the "Delete" button of the Confirm dialog
        Then a toast containing text "Record deleted" is displayed


    # XT-70024: be carefull this test interweasved with ofther feature when they are split in different feature files and executed in parallel
    Scenario: As a user I want to filter by a numeric custom field on tables and the main list
        # Create numeric field
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-customization/CustomField/eyJfaWQiOiIkbmV3In0="
        Then the "Custom field" titled page is displayed
        And the user selects the "Record type *" labelled reference field on the main page
        When the user clicks the lookup button of the reference field
        And the user selects the "node" bound table field on a modal
        And the user filters the "Record type" labelled column in the table field with value "Show Case Product"
        And the user selects the row 1 of the table field
        And the user clicks the "Record type" labelled nested field of the selected row in the table field
        And the user selects the "Technical name *" labelled text field on the main page
        When the user writes "filterTestNumericField" in the text field
        And the user selects the "Anchor field *" labelled reference field on the main page
        When the user clicks the lookup button of the reference field
        And the user selects the "anchorProperty" bound table field on a modal
        And the user filters the "Technical name" labelled column in the table field with value "Description"
        And the user selects the row 1 of the table field
        And the user clicks the "Technical name" labelled nested field of the selected row in the table field
        Then the user selects the "Display also on" labelled multi dropdown field on the main page
        When the user clicks in the multi dropdown field
        And the user selects "Page | Navigation bar" in the multi dropdown field
        When the user selects the "Data type" labelled dropdown-list field on the main page
        When the user clicks in the dropdown-list field
        And the user selects "Decimal" in the dropdown-list field
        And the user selects the "Title" labelled text field on the main page
        When the user writes "Simple numeric field" in the text field
        And the user clicks the "Save" labelled business action button on the main page

        # Define some test data to the test columns
        When the user navigates to the following link: "@sage/xtrem-show-case/ShowCaseProduct/eyJfaWQiOiI0OTUifQ=="
        Then the "Product Chocolate - Milk" titled page is displayed
        Then the user selects the "Simple numeric field" labelled numeric field on the main page
        When the user writes "4" in the numeric field
        And the user waits 1 second
        And the user clicks the "Save" labelled business action button on the main page
        When the user navigates to the following link: "@sage/xtrem-show-case/ShowCaseProduct/eyJfaWQiOiI0NTgifQ=="
        Then the "Product Instant Coffee" titled page is displayed
        Then the user selects the "Simple numeric field" labelled numeric field on the main page
        When the user writes "5" in the numeric field
        And the user waits 1 second
        And the user clicks the "Save" labelled business action button on the main page

        # Search on the main list by using the custom fields
        When the user navigates to the following link: "@sage/xtrem-show-case/ShowCaseProduct"
        Then the "Products" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        #And the user clicks the "Hide table filters" labelled button of the table field
        And the user filters the "Simple numeric field" labelled column in the table field with value "4"
        And the user selects the row 1 of the table field
        Then the value of the "Product" labelled nested text field of the selected row in the table field is "Chocolate - Milk"
        And the user filters the "Simple numeric field" labelled column in the table field with value "5"
        And the user selects the row 1 of the table field
        Then the value of the "Product" labelled nested text field of the selected row in the table field is "Instant Coffee"

        # Search on record table by using the custom fields
        When the user navigates to the following link: "@sage/xtrem-show-case/ShowCaseProvider/eyJfaWQiOiIyIn0="
        Then the "Provider 2" titled page is displayed
        And the user selects the "Products" labelled table field on the main page
        And the user filters the "Simple numeric field" labelled column in the table field with value "4"
        And the user selects the row 1 of the table field
        Then the value of the "Product" labelled nested link field of the selected row in the table field is "Chocolate - Milk"
        And the user filters the "Simple numeric field" labelled column in the table field with value "5"
        And the user selects the row 1 of the table field
        Then the value of the "Product" labelled nested link field of the selected row in the table field is "Instant Coffee"

        # Cleanup
        Then the user navigates to the following link: "@sage/xtrem-customization/CustomField"
        Then the "Custom fields" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user clicks the "Show table filters" labelled button of the table field
        And the user filters the "name" bound column in the table field with value "filterTestNumericField"
        And the user selects the row 1 of the table field
        And the user clicks the "name" bound nested field of the selected row in the table field
        And the user waits 1 second
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        And the user clicks the "Delete" button of the Confirm dialog
        Then a toast containing text "Record deleted" is displayed
