Feature: 1-2 Dropdown list
    # Tests the dropdown list component across different devices, verifying option selection, value display, and interaction with dropdown menus

    Scenario Outline: <Device> - As an ATP XTreeM User I can select and verify the selected option of the drop-down list field using label
        XT-25838
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/DropdownList"
        Then the "Field - Dropdown List" titled page is displayed
        Given the user selects the "Dropdown List Field (Options)" labelled dropdown-list field on the main page
        When the user clicks in the dropdown-list field
        And the user selects "Two" in the dropdown-list field
        Then the value of the dropdown-list field is "Two"
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> - As an ATP XTreeM User I can select and verify the selected option of the drop-down list field using bind
        XT-25838
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/DropdownList"
        Then the "Field - Dropdown List" titled page is displayed
        Given the user selects the "field1" bound dropdown-list field on the main page
        When the user clicks in the dropdown-list field
        And the user selects "Three" in the dropdown-list field
        Then the value of the dropdown-list field is "Three"
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: As an ATP XTreeM user I can verify the drop-down list has an empty value
        XT-49077
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/DropdownList"
        Then the "Field - Dropdown List" titled page is displayed

        Given the user selects the "With empty value" labelled dropdown-list field on the main page
        When the user clicks in the dropdown-list field
        And the user selects " " in the dropdown-list field
        Then the value of the dropdown-list field is " "
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |


    Scenario Outline: <Device> - As a user I want to verify the value of a disabled drop-down list field
        XT-25838
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/DropdownList"
        Then the "Field - Dropdown List" titled page is displayed
        Given the user selects the "field1" bound dropdown-list field on the main page
        When the user clicks in the dropdown-list field
        And the user selects "Three" in the dropdown-list field
        Given the user selects the "Is Disabled" labelled checkbox field on the main page
        When the user clicks in the checkbox field
        Then the value of the dropdown-list field is "Three"
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> - As a user I want to verify the value of a read-only drop-down list field
        XT-25838
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/DropdownList"
        Then the "Field - Dropdown List" titled page is displayed
        Given the user selects the "field1" bound dropdown-list field on the main page
        When the user clicks in the dropdown-list field
        And the user selects "Three" in the dropdown-list field
        Given the user selects the "Is Read-Only" labelled checkbox field on the main page
        When the user clicks in the checkbox field
        Then the value of the dropdown-list field is "Three"
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> - As an ATP XTreeM User I verify the list of option of the dropdown-list field
        XT-64121
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/DropdownList"
        Then the "Field - Dropdown List" titled page is displayed
        Given the user selects the "Dropdown List Field (Options)" labelled dropdown-list field on the main page
        When the user clicks in the dropdown-list field
        Then at least the following list of options is displayed for the dropdown-list field:"One | Two | Three"
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |


    Scenario Outline: <Device> - As a user I can scroll to the drop-down list field
        XT-25838
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/DropdownList"
        Then the "Field - Dropdown List" titled page is displayed
        Given the user selects the "field1" bound dropdown-list field on the main page
        And the user scrolls to the dropdown-list field
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario: As an ATP XTreeM User I can set the focus on the drop-down list field
        XT-25838
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/DropdownList"
        Then the "Field - Dropdown List" titled page is displayed
        When the user clicks in the "focus" bound button field on the main page
        Given the user selects the "field1" bound dropdown-list field on the main page
        Then the focus on the dropdown-list field is visible

    Scenario Outline: <Device> - As and ATP XTreeM user I can verify the drop-down list field title
        XT-25838
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/DropdownList"
        Then the "Field - Dropdown List" titled page is displayed
        Given the user selects the "Title" labelled text field on the main page
        When the user writes "Sample title" in the text field
        And the user presses Tab
        Given the user selects the "field1" bound dropdown-list field on the main page
        When the user clicks in the dropdown-list field
        Then the title of the dropdown-list field is "Sample title"
        Then the title of the dropdown-list field is displayed
        Given the user selects the "Is Title Hidden" labelled checkbox field on the main page
        When the user ticks the checkbox field
        Given the user selects the "field1" bound dropdown-list field on the main page
        Then the title of the dropdown-list field is hidden
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> - As and ATP XTreeM user I can verify the drop-down list field helper text
        XT-25838
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/DropdownList"
        Then the "Field - Dropdown List" titled page is displayed
        Given the user selects the "Helper Text" labelled text field on the main page
        When the user writes "Sample text" in the text field
        And the user presses Tab
        Given the user selects the "field1" bound dropdown-list field on the main page
        When the user clicks in the dropdown-list field
        Then the helper text of the dropdown-list field is "Sample text"
        Then the helper text of the dropdown-list field is displayed
        Given the user selects the "Is Helper Text Hidden" labelled checkbox field on the main page
        When the user ticks the checkbox field
        Given the user selects the "field1" bound dropdown-list field on the main page
        Then the helper text of the dropdown-list field is hidden
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> - As and ATP XTreeM user I can verify if the drop-down list field is displayed or hidden
        XT-25838
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/DropdownList"
        Then the "Field - Dropdown List" titled page is displayed
        Given the user selects the "field1" bound dropdown-list field on the main page
        When the user clicks in the dropdown-list field
        Then the "field1" bound dropdown-list field on the main page is displayed
        Given the user selects the "Is Hidden" labelled checkbox field on the main page
        When the user ticks the checkbox field
        Then the "field1" bound dropdown-list field on the main page is hidden
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> - As and ATP XTreeM user I can verify if the drop-down list field is enabled or disabled
        XT-25838
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/DropdownList"
        Then the "Field - Dropdown List" titled page is displayed
        Given the user selects the "field1" bound dropdown-list field on the main page
        Then the dropdown-list field is enabled
        Given the user selects the "Is Disabled" labelled checkbox field on the main page
        When the user ticks the checkbox field
        Given the user selects the "field1" bound dropdown-list field on the main page
        Then the dropdown-list field is disabled
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> - As and ATP XTreeM user I can verify if the drop-down List field is read-only
        XT-25838
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/DropdownList"
        Then the "Field - Dropdown List" titled page is displayed
        Given the user selects the "Is Read-Only" labelled checkbox field on the main page
        When the user ticks the checkbox field
        Given the user selects the "field1" bound dropdown-list field on the main page
        Then the dropdown-list field is read-only
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    # INFO: This test is to ensure the dropdown list field is updated correctly, when it's value is set to null via the functional code (XT-25175)
    Scenario: As an ATP XTreeM user I can verify the drop-down list field is set properly when the value is set to null
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/DropdownList"
        Then the "Field - Dropdown List" titled page is displayed
        Given the user selects the "Field #1 with Empty Option" labelled dropdown-list field on the main page
        Then the value of the dropdown-list field is " "
        When the user clicks in the dropdown-list field
        And the user selects "Banana" in the dropdown-list field
        Then the value of the dropdown-list field is "Banana"
        When the user clicks the "valueSetterButton1" bound button on the main page
        Given the user selects the "Field #1 with Empty Option" labelled dropdown-list field on the main page
        Then the value of the dropdown-list field is " "
        Given the user selects the "Field #2 without Empty Option" labelled dropdown-list field on the main page
        When the user clicks in the dropdown-list field
        And the user selects "Banana" in the dropdown-list field
        Then the value of the dropdown-list field is "Banana"
        When the user clicks the "valueSetterButton2" bound button on the main page
        Given the user selects the "Field #2 without Empty Option" labelled dropdown-list field on the main page
        Then the value of the dropdown-list field is ""

    # INFO: This test is to ensure the dropdown list value is updated correctly, when the user navigates between items using the navigation panel.
    Scenario: As a user I want to check the value of the dropdown list field when navigating between items
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseProduct/eyJfaWQiOiIxMDYifQ=="
        Then the "Product Anisette - Mcguiness" titled page is displayed
        When the user selects the "Category (Dropdown)" labelled dropdown-list field on the main page
        Then the value of the dropdown-list field is "Good"
        When the user opens the navigation panel
        And the user clicks the record with the text "Appetizer - Crab And Brie" in the navigation panel
        Then the "Product Appetizer - Crab And Brie" titled page is displayed
        And the value of the dropdown-list field is "Ok"

    Scenario Outline: <Device> - As a user I want to check if the Dropdown List field is mandatory
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/DropdownList"
        Then the "Field - Dropdown List" titled page is displayed
        When the user selects the "Is Mandatory" labelled checkbox field on the main page
        And the user ticks the checkbox field
        And the user clicks the "validate" bound button on the main page
        And the user selects the "Dropdown List Field (Options) *" labelled dropdown-list field on the main page
        And the user hovers over the dropdown-list field
        Then the "You need to select a value." validation error message of the dropdown-list field is displayed
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario: As a user I want to check the error message of an incorrect Dropdown List field value
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/DropdownList"
        Then the "Field - Dropdown List" titled page is displayed
        When the user selects the "Info, warning and validation" labelled dropdown-list field on the main page
        When the user clicks in the dropdown-list field
        And the user selects "Good" in the dropdown-list field
        Then the "Error message" validation error message of the dropdown-list field is displayed

    Scenario: As a user I want to check the info message of a Dropdown List field value
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/DropdownList"
        Then the "Field - Dropdown List" titled page is displayed
        When the user selects the "With info message" labelled dropdown-list field on the main page
        When the user clicks in the dropdown-list field
        And the user selects "Not bad" in the dropdown-list field
        Then the "Wow, warning!" validation info message of the dropdown-list field is displayed
