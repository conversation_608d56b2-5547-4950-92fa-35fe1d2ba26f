Feature: 1-1 Bound fields
    # Tests the data binding functionality between UI fields and their respective dataset values, verifying correct display of different data types (text, integer, decimal)

    Scenario Outline: As a user I want my fields to be bound to the dataset
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseProvider/<Record>"
        Given the user selects the "ID" labelled text field on the main page
        Then the value of the text field is "<ID>"
        Given the user selects the "Integer" labelled numeric field on the main page
        Then the value of the numeric field is "<Integer>"
        Given the user selects the "Decimal" labelled numeric field on the main page
        Then the value of the numeric field is "<Decimal>"
        Examples:
            | Record       | ID | Integer | Decimal |
            | eyJfaWQiOjF9 | 1  | 1       | 2.34    |
            | eyJfaWQiOjJ9 | 2  | 2       | 3.43    |
