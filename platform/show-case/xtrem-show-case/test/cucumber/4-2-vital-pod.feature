Feature: 4-2 Vital pod
    # Tests the vital pod component across different devices, verifying display of helper text, field validation, and complex data entry within the pod structure and control vital pod field state.

    Scenario Outline: <Device> - As a user I want to verify the helper text of the vital pod field
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/VitalPodField/eyJfaWQiOiIxIn0="
        Then the "Field - Vital Pod (as Field)" titled page is displayed

        And the user selects the "Helper text" labelled text field on the main page
        When the user writes "This is a test" in the text field
        And the user blurs the text field
        And the user selects the "Address" labelled vital pod field on the main page
        Then the helper text of the vital pod field is "This is a test"
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> - As a user I want to verify the helper text of the vital pod block
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/VitalPodBlock/eyJfaWQiOiIxIn0="
        Then the "Field - Vital Pod (as Block)" titled page is displayed

        And the user selects the "Helper text" labelled text field on the main page
        When the user writes "This is a test" in the text field
        And the user blurs the text field
        And the user selects the "Address" labelled vital pod field on the main page
        Then the helper text of the vital pod field is "This is a test"
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> - As a user I want to verify the vital pod field is enabled or disabled
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/VitalPodField/eyJfaWQiOiIxIn0="
        Then the "Field - Vital Pod (as Field)" titled page is displayed

        And the user selects the "Address" labelled vital pod field on the main page
        Then the vital pod field is enabled
        And the user selects the "Is disabled" labelled checkbox field on the main page
        When the user ticks the checkbox field
        And the user selects the "Address" labelled vital pod field on the main page
        Then the vital pod field is disabled
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> - As a user I want to verify the vital pod block is enabled or disabled
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/VitalPodBlock/eyJfaWQiOiIxIn0="
        Then the "Field - Vital Pod (as Block)" titled page is displayed

        And the user selects the "Address" labelled vital pod field on the main page
        Then the vital pod field is enabled
        And the user selects the "Is disabled" labelled checkbox field on the main page
        When the user ticks the checkbox field
        And the user selects the "Address" labelled vital pod field on the main page
        Then the vital pod field is disabled
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> - As a user I can add a vital pod field (defined as field)
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/VitalPodField/eyJfaWQiOiIzMjAifQ=="
        Then the "Field - Vital Pod (as Field)" titled page is displayed

        When the user selects the "Address" labelled vital pod field on the main page
        And the user clicks the "Add an item" button of the vital pod field
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> - As a user I can add a vital pod field (defined as block)
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/VitalPodBlock/eyJfaWQiOiIzMjAifQ=="
        Then the "Field - Vital Pod (as Block)" titled page is displayed

        When the user selects the "Address" labelled vital pod field on the main page
        And the user clicks the "Add an item" button of the vital pod field
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> - As a user I can remove a vital pod field (defined as field)
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/VitalPodField/eyJfaWQiOiIxMDYifQ=="
        Then the "Field - Vital Pod (as Field)" titled page is displayed

        When the user selects the "Address" labelled vital pod field on the main page
        And the user clicks the "Add an item" button of the vital pod field
        And the user selects the "Is removable" labelled checkbox field on the main page
        When the user ticks the checkbox field
        And the user selects the "Address" labelled vital pod field on the main page
        And the user clicks the "Close" icon of the vital pod field
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> - As a user I can remove a vital pod field (defined as block)
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/VitalPodBlock/eyJfaWQiOiIxMDYifQ=="
        Then the "Field - Vital Pod (as Block)" titled page is displayed

        When the user selects the "Address" labelled vital pod field on the main page
        And the user clicks the "Add an item" button of the vital pod field
        And the user selects the "Is removable" labelled checkbox field on the main page
        When the user ticks the checkbox field
        And the user selects the "Address" labelled vital pod field on the main page
        And the user clicks the "Close" icon of the vital pod field
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> - As a user I want to verify the helper text of the vital pod field is displayed or hidden
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/VitalPodField/eyJfaWQiOiIxIn0="
        Then the "Field - Vital Pod (as Field)" titled page is displayed

        And the user selects the "Helper text" labelled text field on the main page
        When the user writes "This is a test" in the text field
        And the user blurs the text field
        And the user selects the "Address" labelled vital pod field on the main page
        Then the helper text of the vital pod field is displayed
        And the user selects the "Is helper text hidden" labelled checkbox field on the main page
        When the user ticks the checkbox field
        And the user selects the "Address" labelled vital pod field on the main page
        Then the helper text of the vital pod field is hidden
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> - As a user I want to verify the helper text of the vital pod block is displayed or hidden
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/VitalPodBlock/eyJfaWQiOiIxIn0="
        Then the "Field - Vital Pod (as Block)" titled page is displayed

        And the user selects the "Helper text" labelled text field on the main page
        When the user writes "This is a test" in the text field
        And the user blurs the text field
        And the user selects the "Address" labelled vital pod field on the main page
        Then the helper text of the vital pod field is displayed
        And the user selects the "Is helper text hidden" labelled checkbox field on the main page
        When the user ticks the checkbox field
        And the user selects the "Address" labelled vital pod field on the main page
        Then the helper text of the vital pod field is hidden
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> - As a user I want to verify the vital pod field is displayed or hidden
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/VitalPodField/eyJfaWQiOiIxIn0="
        Then the "Field - Vital Pod (as Field)" titled page is displayed

        And the "Address" labelled vital pod field on the main page is displayed
        And the user selects the "Is hidden" labelled checkbox field on the main page
        When the user ticks the checkbox field
        Then the "Address" labelled vital pod field on the main page is hidden
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> - As a user I want to verify the vital pod block is displayed or hidden
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/VitalPodBlock/eyJfaWQiOiIxIn0="
        Then the "Field - Vital Pod (as Block)" titled page is displayed

        And the "Address" labelled vital pod field on the main page is displayed
        And the user selects the "Is hidden" labelled checkbox field on the main page
        When the user ticks the checkbox field
        Then the "Address" labelled vital pod field on the main page is hidden
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> - As a user I want to verify the vital pod field is read-only
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/VitalPodField/eyJfaWQiOiIxIn0="
        Then the "Field - Vital Pod (as Field)" titled page is displayed

        And the user selects the "Is readOnly" labelled checkbox field on the main page
        When the user ticks the checkbox field
        And the user selects the "Address" labelled vital pod field on the main page
        Then the vital pod field is read-only
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> - As a user I want to verify the vital pod block is read-only
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/VitalPodBlock/eyJfaWQiOiIxIn0="
        Then the "Field - Vital Pod (as Block)" titled page is displayed

        And the user selects the "Is readOnly" labelled checkbox field on the main page
        When the user ticks the checkbox field
        And the user selects the "Address" labelled vital pod field on the main page
        Then the vital pod field is read-only
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> -  As a user I want to verify the title of the vital pod field is displayed or hidden
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/VitalPodField/eyJfaWQiOiIxIn0="
        Then the "Field - Vital Pod (as Field)" titled page is displayed

        And the user selects the "Title" labelled text field on the main page
        When the user writes "This is a test" in the text field
        And the user blurs the text field
        And the user selects the "originAddress" bound vital pod field on the main page
        Then the title of the vital pod field is displayed
        And the user selects the "Is title hidden" labelled checkbox field on the main page
        When the user ticks the checkbox field
        And the user selects the "originAddress" bound vital pod field on the main page
        Then the title of the vital pod field is hidden
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> - As a user I want to verify the title of the vital pod field
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/VitalPodField/eyJfaWQiOiIxIn0="
        Then the "Field - Vital Pod (as Field)" titled page is displayed

        And the user selects the "Title" labelled text field on the main page
        When the user writes "This is a test" in the text field
        And the user blurs the text field
        And the user selects the "This is a test" labelled vital pod field on the main page
        Then the title of the vital pod field is "This is a test"
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> - As a user I want to verify the title of the vital pod block
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/VitalPodBlock/eyJfaWQiOiIxIn0="
        Then the "Field - Vital Pod (as Block)" titled page is displayed

        And the user selects the "Title" labelled text field on the main page
        When the user writes "This is a test" in the text field
        And the user blurs the text field
        And the user selects the "This is a test" labelled vital pod field on the main page
        Then the title of the vital pod field is "This is a test"
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> - As a user I can click the action of the given Vital Pod Field
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/VitalPodBlock/eyJfaWQiOiIxIn0="
        Then the "Field - Vital Pod (as Block)" titled page is displayed

        When the user selects the "Address" labelled vital pod field on the main page
        And the user clicks the "Action 1" action of the vital pod field
        Then a toast containing text "Action 1 triggered" is displayed
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> -  As a user I can verify the vital pod field action is enabled or disabled
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/VitalPodBlock/eyJfaWQiOiIxIn0="
        Then the "Field - Vital Pod (as Block)" titled page is displayed

        And the user selects the "Address" labelled vital pod field on the main page
        And the action "Action 1" of the vital pod field is enabled
        And the user selects the "Disable action 1" labelled checkbox field on the main page
        When the user ticks the checkbox field
        Then the user selects the "Address" labelled vital pod field on the main page
        And the action "Action 1" of the vital pod field is disabled
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> -  As a user I can verify the vital pod field action is displayed or hidden
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/VitalPodBlock/eyJfaWQiOiIxIn0="
        Then the "Field - Vital Pod (as Block)" titled page is displayed

        When the user selects the "Address" labelled vital pod field on the main page
        And the user clicks the "Add an item" button of the vital pod field
        And the action "Action 2" of the vital pod field is displayed
        And the user selects the "Hide action 2" labelled checkbox field on the main page
        When the user ticks the checkbox field
        Then the user selects the "Address" labelled vital pod field on the main page
        And the action "Action 2" of the vital pod field is hidden
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> - As an ATP XTreeM user I can verify the vital pod field has no data to display
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/VitalPodField/eyJfaWQiOiIxIn0="
        And the "Field - Vital Pod (as Field)" titled page is displayed
        And the user selects the "Address" labelled vital pod field on the main page
        Then the vital pod field is empty
        And the vital pod field header container value is "No data to display."
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> - As an ATP XTreeM user I can verify the vital pod block has no data to display
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/VitalPodBlock/eyJfaWQiOiIxIn0="
        Then the "Field - Vital Pod (as Block)" titled page is displayed
        And the user selects the "Address" labelled vital pod field on the main page
        Then the vital pod field is empty
        And the vital pod field header container value is "No data to display."
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |
