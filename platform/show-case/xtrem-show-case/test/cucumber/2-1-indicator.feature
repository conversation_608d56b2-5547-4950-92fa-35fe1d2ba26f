Feature: 2-1 Indicator
    # Tests the indicator component to verify the selection and control the value of regular fields (such as text field, numeric fields, reference fields etc..) rendered as tile field.

    Scenario: As an ATP XTreeM user I can verify the tile field

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TileContainerPage/eyJfaWQiOiIxMDYifQ=="
        Then the "Tile Container" titled page is displayed

        And the user selects the "_id" bound tile text field on the main page
        Then the value of the tile text field is "*106"
        And the user selects the "listPrice" bound tile numeric field on the main page
        Then the value of the tile numeric field is "18.94"
        And the user selects the "provider" bound tile reference field on the main page
        Then the value of the tile reference field is "Ali Express"
        And the user selects the "releaseDate" bound tile date field on the main page
        Then the value of the tile date field is "01/02/2020"
        And the user selects the "category2" bound tile dropdown-list field on the main page
        Then the value of the tile dropdown-list field is "Good"

        And the user selects the "ID" labelled tile text field on the main page
        Then the value of the tile text field is "*106"
        And the user selects the "LIST PRICE" labelled tile numeric field on the main page
        Then the value of the tile numeric field is "18.94"
        And the user selects the "PROVIDER" labelled tile reference field on the main page
        Then the value of the tile reference field is "Ali Express"
        And the user selects the "RELEASE DATE" labelled tile date field on the main page
        Then the value of the tile date field is "01/02/2020"
        And the user selects the "CATEGORY (DROPDOWN)" labelled tile dropdown-list field on the main page
        Then the value of the tile dropdown-list field is "Good"


    Scenario: As an ATP XTreeM user I can verify the tile field is enable or disabled

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TileContainerPage/eyJfaWQiOiIxMDYifQ=="
        Then the "Tile Container" titled page is displayed

        And the user selects the "ID" labelled tile text field on the main page
        Then the tile text field is enabled
        And the user selects the "LIST PRICE" labelled tile numeric field on the main page
        Then the tile numeric field is enabled
        And the user selects the "PROVIDER" labelled tile reference field on the main page
        Then the tile reference field is enabled
        And the user selects the "RELEASE DATE" labelled tile date field on the main page
        Then the tile date field is enabled
        And the user selects the "CATEGORY (DROPDOWN)" labelled tile dropdown-list field on the main page
        Then the tile dropdown-list field is enabled

        And the user selects the "Is tile container disabled" labelled checkbox field on the main page
        When the user ticks the checkbox field

        And the user selects the "ID" labelled tile text field on the main page
        Then the tile text field is disabled
        And the user selects the "LIST PRICE" labelled tile numeric field on the main page
        Then the tile numeric field is disabled
        And the user selects the "PROVIDER" labelled tile reference field on the main page
        Then the tile reference field is disabled
        And the user selects the "RELEASE DATE" labelled tile date field on the main page
        Then the tile date field is disabled
        And the user selects the "CATEGORY (DROPDOWN)" labelled tile dropdown-list field on the main page
        Then the tile dropdown-list field is disabled


    Scenario: As an ATP XTreeM user I can click the tile field
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/HeaderSection/eyJfaWQiOiIxIn0="
        Then the "Provider Ali Express" titled page is displayed

        And the user selects the "textField" bound tile text field on the main page
        When the user clicks in the tile text field

        Then a toast with text "name is clicked" is displayed
