Feature: 3-1 Progress
    # Tests the progress field component across different devices, verifying correct display of percentage values and visual indicators, and control the progress field state.

    Scenario Outline: <Device> - As an ATP XTreeM User I can verify the progress field using label
        XT-25838
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Progress"
        Then the "Field - Progress" titled page is displayed

        Given the user selects the "Title" labelled text field on the main page
        When the user writes "Sample title" in the text field
        And the user presses Tab

        Given the user selects the "Sample title" labelled progress field on the main page
        Then the value of the progress field is "50 %"
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |


    Scenario Outline: <Device> - As an ATP XTreeM User I can verify the progress field using bind
        XT-25838
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Progress"
        Then the "Field - Progress" titled page is displayed

        Given the user selects the "field" bound progress field on the main page
        Then the value of the progress field is "50 %"
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> - As an ATP XTreeM User I can scroll to the progress field
        XT-25838
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Progress"
        Then the "Field - Progress" titled page is displayed

        Given the user selects the "field" bound progress field on the main page
        And the user scrolls to the progress field
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    #progress Field: Set / check properties

    Scenario Outline: <Device> - As and ATP XTreeM user I can verify the progress field title
        XT-25838
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Progress"
        Then the "Field - Progress" titled page is displayed

        Given the user selects the "Title" labelled text field on the main page
        When the user writes "Sample title" in the text field
        And the user presses Tab

        Given the user selects the "field" bound progress field on the main page
        When the user clicks in the progress field
        Then the title of the progress field is "Sample title"
        Then the title of the progress field is displayed

        Given the user selects the "Is title hidden" labelled checkbox field on the main page
        When the user ticks the checkbox field

        Given the user selects the "field" bound progress field on the main page
        Then the title of the progress field is hidden
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |


    Scenario Outline: <Device> - As and ATP XTreeM user I can verify the progress field helper text
        XT-25838
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Progress"
        Then the "Field - Progress" titled page is displayed

        Given the user selects the "Helper text" labelled text field on the main page
        When the user writes "Sample text" in the text field
        And the user presses Tab

        Given the user selects the "field" bound progress field on the main page
        When the user clicks in the progress field
        Then the helper text of the progress field is "Sample text"
        Then the helper text of the progress field is displayed

        Given the user selects the "Is helper text hidden" labelled checkbox field on the main page
        When the user ticks the checkbox field

        Given the user selects the "field" bound progress field on the main page
        Then the helper text of the progress field is hidden
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |


    Scenario Outline: <Device> - As and ATP XTreeM user I can verify if the progress field is displayed or hidden
        XT-25838
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Progress"
        Then the "Field - Progress" titled page is displayed

        Given the user selects the "field" bound progress field on the main page
        When the user clicks in the progress field
        Then the "field" bound progress field on the main page is displayed

        Given the user selects the "Is hidden" labelled checkbox field on the main page
        When the user ticks the checkbox field
        Then the "field" bound progress field on the main page is hidden
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |


    Scenario Outline:<Device> - As and ATP XTreeM user I can verify if the progress field is enabled disabled
        XT-25838
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Progress"
        Then the "Field - Progress" titled page is displayed

        Given the user selects the "field" bound progress field on the main page
        Then the progress field is enabled

        Given the user selects the "Is disabled" labelled checkbox field on the main page
        When the user ticks the checkbox field

        Given the user selects the "field" bound progress field on the main page
        Then the progress field is disabled
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |


    Scenario: Set the value of the progress field
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Progress"
        Then the "Field - Progress" titled page is displayed

        Given the user selects the "value" labelled numeric field on the main page
        When the user writes "50" in the numeric field

        Given the user selects the "field" bound progress field on the main page
        Then the value of the progress field is "50 %"


    Scenario: Trigger custom click event handler
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Progress"
        Then the "Field - Progress" titled page is displayed
        Then the "clickTriggered" bound label field on the main page is hidden
        Given the user selects the "field" bound progress field on the main page
        When the user clicks in the progress field
        Then the "clickTriggered" bound label field on the main page is displayed
