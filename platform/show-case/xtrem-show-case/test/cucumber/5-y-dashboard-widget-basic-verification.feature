Feature: 5 Y dashboard widget basic verification
    # Tests the verification of basic widget values in dashboards, ensuring correct display and content of simple text-based widgets

    @ClearDashboards
    Scenario: As an ATP XTreeM user I can verify the value of the basic dashboard widget
        XT-33335

        Given the user opens the application on a desktop
        #Create the dashboard
        And the dashboard page is displayed
        Then the "Create a dashboard to get started." subtitled empty dashboard is displayed
        When the user clicks the create button on the dashboard

        #Select showcase dashboard
        When the user selects the template with title "Showcase dashboard" in the dashboard creation dialog
        Then the template with title "Showcase dashboard" in the dashboard creation dialog is selected
        When the user clicks the "Next" button in the dashboard creation dialog

        #Verify the basic widgets in the Dashboard editor
        Then the "Showcase dashboard" titled dashboard in the dashboard editor is displayed
        And the user selects the "List of users" titled basic widget field in the dashboard editor
        Then the value of the basic widget field contains
            """
            Admin Persona (<EMAIL>)
            Admin Test (<EMAIL>)
            adrienne authier (<EMAIL>)
            aghate vauloup (<EMAIL>)
            alex thelion (<EMAIL>)
            alexandre lacazette (<EMAIL>)
            alexandre pato (<EMAIL>)
            alphonse davies (<EMAIL>)
            amaury durant (<EMAIL>)
            andrew garfield (<EMAIL>)
            angelo orie (<EMAIL>)
            Ann Lew (<EMAIL>)
            antoine griezmann (<EMAIL>)
            antoinette boucher (<EMAIL>)
            API 0000000000 (<EMAIL>)
            armand vaillancourt (<EMAIL>)
            astrid moquin (<EMAIL>)
            avril dastous (<EMAIL>)
            becky gee (<EMAIL>)
            Ben Ten (<EMAIL>)
            """
        And the user clicks the "cancel" button in the dashboard editor footer
        Then the "Showcase dashboard" titled dashboard is displayed

        #Verify the basic widgets in the Dashboard
        And the user selects the "List of users" titled basic widget field in the dashboard
        Then the value of the basic widget field contains
            """
            alexandre lacazette (<EMAIL>)
            alexandre pato (<EMAIL>)
            alphonse davies (<EMAIL>)
            amaury durant (<EMAIL>)
            andrew garfield (<EMAIL>)
            angelo orie (<EMAIL>)
            Ann Lew (<EMAIL>)
            antoine griezmann (<EMAIL>)
            antoinette boucher (<EMAIL>)
            """
