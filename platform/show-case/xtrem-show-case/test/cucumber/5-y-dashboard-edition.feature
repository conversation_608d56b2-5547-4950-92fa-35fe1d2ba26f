Feature: 5 Y dashboard edition
    # Tests the dashboard editing capabilities, including entering and exiting edit mode, adding widgets, and saving changes to existing dashboards

    @ClearDashboards
    Scenario: As a user I want to edit my dashboards
        # create first dashboard
        Given the user opens the application on a desktop
        And the dashboard page is displayed
        Then the "Create a dashboard to get started." subtitled empty dashboard is displayed
        When the user clicks the create button on the dashboard
        Then the dashboard creation dialog is displayed
        When the user selects the template with title "Blank template" in the dashboard creation dialog
        And the user clicks the "next" button in the dashboard creation dialog
        Then the "New dashboard" titled dashboard in the dashboard editor is displayed
        When the user clicks the "cancel" button in the dashboard editor footer
        And the user waits 1 second
        Then the "New dashboard" titled dashboard is displayed
        # edit the created dashboard
        When the user clicks the "edit" labelled CRUD button in the dashboard action menu
        Then the "New dashboard" titled dashboard in the dashboard editor is displayed
        # add a widget from the widget list
        And the "Widgets" titled header in the dashboard editor navigation panel is displayed
        And the "myDemoCategory" titled category in the dashboard editor navigation panel is displayed
        And the "myOtherDemoCategory" titled category in the dashboard editor navigation panel is displayed
        And the "others" titled category in the dashboard editor navigation panel is displayed
        And the value of the "title" bound nested text field on the "5" navigation panel's row is "Suppliers"
        And the value of the "title" bound nested text field on the "6" navigation panel's row is "Users"
        And the value of the "description" bound nested text field on the "6" navigation panel's row is "Detailed list about the current users"
        And the value of the "title" bound nested text field on the "9" navigation panel's row is "List of users"
        And the value of the "title" bound nested text field on the "11" navigation panel's row is "Products"
        And the value of the "title" bound nested text field on the "12" navigation panel's row is "Products"
        And the value of the "title" bound nested text field on the "13" navigation panel's row is "Number of users"
        And the value of the "title" bound nested text field on the "14" navigation panel's row is "Ratio of administrators"
        And the value of the "title" bound nested text field on the "15" navigation panel's row is "System version"
        Then the user searches for "Suppliers" in the navigation panel
        When the user clicks the Add button of the "Suppliers" titled widget card in the dashboard editor navigation panel
        Then the "Suppliers" titled widget in the dashboard editor is displayed
        # edit the title of the dashboard
        When the user clicks the edit dashboard title icon
        And the user writes "New title" in the dashboard title
        And the user presses Enter
        Then the "New title" titled dashboard in the dashboard editor is displayed
        When the user clicks the "save" button in the dashboard editor footer
        Then a toast containing text "Dashboard saved." is displayed
        And the user dismisses all the toasts
        And the user waits 1 second
        Then the "New title" titled dashboard is displayed
        And the "Suppliers" titled widget in the dashboard is displayed
        # create a new dashboard
        When the user clicks the "create" labelled CRUD button in the dashboard action menu
        Then the dashboard creation dialog is displayed
        When the user selects the template with title "Blank template" in the dashboard creation dialog
        And the user clicks the "next" button in the dashboard creation dialog
        Then the "New dashboard" titled dashboard in the dashboard editor is displayed
        When the user clicks the "cancel" button in the dashboard editor footer
        And the user waits 1 second
        Then the "New dashboard" titled dashboard is displayed
        When the user clicks the "edit" labelled CRUD button in the dashboard action menu
        Then the "New dashboard" titled dashboard in the dashboard editor is displayed
        # switch between dashboards while editing
        When the user selects the "New title" labelled tab in dashboard edit mode
        Then the "Suppliers" titled widget in the dashboard is displayed
        When the user clicks the "cancel" button in the dashboard editor footer
        And the user waits 1 second
        Then the "New title" titled dashboard is displayed

    @ClearDashboards
    Scenario: As a user I want to see a hard-coded total count in my widget
        Given the user opens the application on a desktop
        And the dashboard page is displayed
        Then the "Create a dashboard to get started." subtitled empty dashboard is displayed
        When the user clicks the create button on the dashboard
        Then the dashboard creation dialog is displayed
        And the dashboard creation dialog description is "Select a template to get started or build your own dashboard. You can customize any dashboard by adding or removing widgets."
        And the "Blank template" template in the dashboard creation dialog is displayed
        When the user selects the template with title "Blank template" in the dashboard creation dialog
        Then the template with title "Blank template" in the dashboard creation dialog is selected
        When the user clicks the "next" button in the dashboard creation dialog
        Then the "New dashboard" titled dashboard in the dashboard editor is displayed
        When the user clicks the Add button of the "Users with total count" titled widget card in the dashboard editor navigation panel
        Then the "Users with total count" titled widget in the dashboard editor is displayed
        When the user clicks the "save" button in the dashboard editor footer
        Then a toast containing text "Dashboard saved." is displayed
        And the user waits 1 second
        Then the "New dashboard" titled dashboard is displayed
        And the "Users with total count" titled widget in the dashboard is displayed
        When the user selects the "Users with total count" titled table widget field in the dashboard
        Then the total count of records in the table widget fields is "230"
