Feature: 1-2 Function imports
  # Tests cross-package function import capabilities, verifying that imported validation functions correctly process field data and display appropriate error messages

  Sc<PERSON>rio: Imported functions from another package work as expected
    Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/FunctionsPage"
    And the user selects the "email" bound text field on the main page
    When the user writes "invalid mail" in the text field
    Then the text field is invalid
    When the user writes "<EMAIL>" in the text field
    And the user blurs the text field
    Then the text field is valid
    And the user selects the "password" bound text field on the main page
    When the user writes "1" in the text field
    And the user blurs the text field
    Then the text field is invalid
    When the user writes "1bA_111$1" in the text field
    And the user blurs the text field
    Then the text field is valid
    When the user clicks in the "button" bound button field on the main page
    Then the value of the toast is "Congratulations, you are now registered!"
