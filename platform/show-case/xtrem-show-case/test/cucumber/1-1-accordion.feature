Feature: 1-1 Accordion
    # Tests the accordion component functionality, verifying expansion and collapse behavior of grouped content sections

    Scenario: As an XTreeM user I can interact with the accordion on the main page
        XT-8854
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Accordion/eyJfaWQiOiIxIn0="
        Then the "Section - Accordion mode" titled page is displayed

        Given the user expands accordion with title "Block 1" on the main page
        Then the accordion with title "Block 1" is expanded
        # And takes a screenshot


        Given the user collapses accordion with title "Block 1" on the main page
        Then the accordion with title "Block 1" is collapsed
    # And takes a screenshot



    Scenario: As an XTreeM user I can interact with the accordion on the detail panel
        XT-8854
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Accordion/eyJfaWQiOiIxIn0="
        Then the "Section - Accordion mode" titled page is displayed

        Given the user expands accordion with title "Second section block" on the detail panel
        Then the accordion with title "Second section block" is expanded
        # And takes a screenshot

        Given the user collapses accordion with title "Second section block" on the detail panel
        Then the accordion with title "Second section block" is collapsed
# And takes a screenshot
