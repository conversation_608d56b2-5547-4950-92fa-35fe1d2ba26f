Feature: 3-2 Report template
    # Tests the functionality of report templates, verifying that HTML templates can be correctly viewed and that their content renders properly with dynamic data

    Scenario: As an ATP / XTreeM user, I can verify the HTML content of the report template
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-reporting/ReportTemplate"
        Then the "Report templates" titled page is displayed

        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row with text "activeTemplate" in the "Name" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field

        Then the "Report template activeTemplate" titled page is displayed

        And selects the "HTML template" labelled navigation anchor on the main page

        Given the user selects the "externalHtmlTemplate" bound code editor field on the main page
        Then the html value of the code editor field is
            """
            <div>
                <h1 class="header">{{codeBlockResult}} Company: {{xtremSystem.company.query.edges.[0].node.id}}</h1>
                <h2 class="header">Active: {{xtremSystem.company.query.edges.[0].node.isActive}}</h2>
                <table>
                    <tr>
                        <th class="detail-row">Sites:</th>
                    </tr>
                    {{#each xtremSystem.company.query.edges.[0].node.sites.query.edges}}
                    <tr>
                        <td><li>{{node.id}}</li></td>
                    </tr>
                    {{/each}}
                </table>
            </div>
            """
        # And takes a screenshot

        Then the html value of the code editor field contains
            """
            <div>
                <h1 class="header">{{codeBlockResult}} Company: {{xtremSystem.company.query.edges.[0].node.id}}</h1>
                <h2 class="header">Active: {{xtremSystem.company.query.edges.[0].node.isActive}}</h2>
                <table>
            """
    # And takes a screenshot

    Scenario: As a user, I want to filter a report template using a reference type filter

        # step 1: create a report with a reference type variable
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-reporting/Report"
        Then the "Reports" titled page is displayed
        When the user clicks the "Create" labelled multi action button on the navigation panel
        Then the "Report" titled page is displayed
        When the user selects the "Name" labelled text field on the main page
        And the user writes "referenceFilterReport" in the text field
        And the user presses Enter
        And the user selects the "Parent package" labelled select field on the main page
        And the user clicks in the select field
        And the user selects "xtrem-show-case" in the select field
        And the user presses Enter
        # And the user selects the "printingType" labelled select field on the main page
        # And the user writes "Not applicable" in the select field
        # And the user selects "Not applicable" in the select field
        And the user selects the "Description" labelled text area field on the main page
        And the user writes "description" in the text area field
        And the user presses Enter
        And selects the "Variables" labelled navigation anchor on the main page
        And the "Variables" labelled navigation anchor is selected
        And the user selects the "variables" bound table field on the main page
        And the user adds a new table row to the table field
        And the user selects the floating row of the table field
        And the user writes "Provider" in the "Variable name" labelled nested text field of the selected row in the table field
        And the user presses Enter
        And the user writes "Provider" in the "Variable title" labelled nested text field of the selected row in the table field
        And the user presses Enter
        And the user clicks the "Variable type" labelled nested field of the selected row in the table field
        And the user selects "Reference" in the "Variable type" labelled nested field of the selected row in the table field
        Then the value of the "Variable type" labelled nested select field of the selected row in the table field is "Reference"
        And the user clicks the "Data type" labelled nested field of the selected row in the table field
        And the user writes "provider" in the "Data type" labelled nested select field of the selected row in the table field
        And the user selects "Show case provider" in the "Data type" labelled nested field of the selected row in the table field
        And the user presses Tab
        And the user presses Tab
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record created" is displayed

        # step 2: create a report template based on the reference variable report and filter it
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-reporting/ReportTemplate"
        Then the "Report templates" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel
        Then the "Report template" titled page is displayed
        When the user selects the "Name" labelled text field on the main page
        And the user writes "referenceFilterReportTemplate" in the text field
        And the user presses Enter
        And the user selects the "Report" labelled reference field on the main page
        And the user writes "reference" in the reference field
        And the user selects "referenceFilterReport" in the reference field
        And the user presses Enter
        And the user selects the "Top margin" labelled numeric field on the main page
        And the user writes "1" in the numeric field
        And the user presses Enter
        And the user selects the "Bottom margin" labelled numeric field on the main page
        And the user writes "1" in the numeric field
        And the user presses Enter
        And selects the "Template" labelled navigation anchor on the main page
        And the user selects the "template" bound form designer field on the main page
        And the user clicks in the body of the form designer
        And the user clicks the "Show/hide fields panel" button in the form designer toolbar
        Then the "Fields" panel in the form designer is displayed
        When the user searches "Show case provider" in the node-step-tree of the form designer on the main page
        And the user selects the node-step-tree in the form designer on the main page
        And the user selects the tree-view element of level "1" with text "Show case provider" in the node-step-tree of the form designer
        And the user clicks the tree-view element in the node-step-tree of the form designer
        Then the title of the form designer editor dialog is "Insert an object type"
        And the title step of the form designer editor dialog is "1. Insert mode"

        When the user selects the card with "Table" value in the selection card of the form designer editor dialog
        And the user clicks the "Next" button of the form designer editor dialog
        Then the title step of the form designer editor dialog is "2. Column selection"

        When the user selects the node-step-tree in the form designer on a modal
        And the user selects the tree-view element of level "1" with text "_id" in the node-step-tree of the form designer
        And the user ticks the tree-view element in the node-step-tree of the form designer
        And the user selects the tree-view element of level "1" with text "Text field" in the node-step-tree of the form designer
        And the user ticks the tree-view element in the node-step-tree of the form designer
        And the user clicks the "Next" button of the form designer editor dialog
        Then the title step of the form designer editor dialog is "3. Define content"

        When the user clicks the "Next" button of the form designer editor dialog
        Then the title step of the form designer editor dialog is "4. Define filters"

        When the user clicks the "Add filter" table button in the form designer widget editor
        And the user writes "_id" in the "Property" dropdown-list field of row "1" in the form designer widget editor
        And the user writes "matches" in the "Filter type" dropdown-list field of row "1" in the form designer widget editor
        And the user clicks the "parameter" switch of row "1" in the form designer widget editor
        And the user writes "Provider" in the "Filter value" dropdown-list field of row "1" in the form designer widget editor
        And the user clicks the "Confirm" button of the form designer editor dialog
        Then the "Report template" titled page is displayed

        # cleaning up the data
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-reporting/Report"
        Then the "Reports" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user filters the "name" bound column in the table field with value "referenceFilterReport"
        And the user presses Escape
        And the user selects the row with text "referenceFilterReport" in the "name" bound column header of the table field
        And the user clicks the "name" bound nested field of the selected row in the table field
        Then the "Report referenceFilterReport" titled page is displayed
        When the user clicks the "Delete" labelled more actions button in the header
        Then a Confirm dialog is displayed on the main page
        When the user clicks the "Delete" button of the Confirm dialog on the main page
        Then the "Reports" titled page is displayed

    Scenario: As a user I want to modify HTML Template and the Header and Footer in HTML advanced mode
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-reporting/ReportTemplate/eyJfaWQiOiI5In0="
        Then selects the "Basic details" labelled navigation anchor on the main page
        Then the user selects the "Report" labelled reference field on the main page
        And the user writes "testReport" in the reference field
        And the user selects "testReport" in the reference field

        # Write HTML Template
        When selects the "HTML template" labelled navigation anchor on the main page
        And the user waits 1 seconds
        And the user selects the "externalHtmlTemplate" bound code editor field on the main page
        And the user writes "<div><h1>Test</h1></div>" in the code editor field
        # Write Header and Footer
        When selects the "Header and footer" labelled navigation anchor on the main page
        And the user waits 1 seconds
        And the user selects the "externalHeaderHtmlTemplate" bound code editor field on the main page
        And the user writes "<div><h1>Header</h1></div>" in the code editor field
        And the user selects the "externalFooterHtmlTemplate" bound code editor field on the main page
        And the user writes "<div><h1>Footer</h1></div>" in the code editor field
        When the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed

    Scenario: As a user I want to be able to use additional filters in report template
        XT-81309, XT-81905

        # Prepare a report template
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-reporting/ReportTemplate"
        When the user clicks the "Create" labelled business action button on the navigation panel
        Then the "Report template" titled page is displayed
        When the user selects the "Name" labelled text field on the main page
        And the user writes "additionalFiltersTemplate" in the text field
        And the user presses Enter
        And the user selects the "Report" labelled reference field on the main page
        And the user writes "testReport" in the reference field
        And the user selects "testReport" in the reference field
        And the user presses Enter
        And selects the "Template" labelled navigation anchor on the main page
        And the user selects the "template" bound form designer field on the main page
        And the user clicks in the body of the form designer
        And the user clicks the "Show/hide fields panel" button in the form designer toolbar
        Then the "Fields" panel in the form designer is displayed

        # Insert table fields
        When the user searches "Show case product" in the node-step-tree of the form designer on the main page
        And the user selects the node-step-tree in the form designer on the main page
        And the user selects the tree-view element of level "1" with text "Show case product" in the node-step-tree of the form designer
        And the user clicks the tree-view element in the node-step-tree of the form designer
        Then the title of the form designer editor dialog is "Insert an object type"
        And the title step of the form designer editor dialog is "1. Insert mode"
        When the user selects the card with "Table" value in the selection card of the form designer editor dialog
        And the user clicks the "Next" button of the form designer editor dialog
        Then the title step of the form designer editor dialog is "2. Column selection"
        When the user selects the node-step-tree in the form designer on a modal
        And the user selects the tree-view element of level "1" with text "Product" in the node-step-tree of the form designer
        And the user ticks the tree-view element in the node-step-tree of the form designer
        And the user selects the tree-view element of level "1" with text "Description" in the node-step-tree of the form designer
        And the user ticks the tree-view element in the node-step-tree of the form designer
        And the user selects the tree-view element of level "1" with text "Ending date" in the node-step-tree of the form designer
        And the user ticks the tree-view element in the node-step-tree of the form designer
        And the user selects the tree-view element of level "1" with text "List price" in the node-step-tree of the form designer
        And the user ticks the tree-view element in the node-step-tree of the form designer
        And the user selects the tree-view element of level "1" with text "Hot product" in the node-step-tree of the form designer
        And the user ticks the tree-view element in the node-step-tree of the form designer
        And the user clicks the "Next" button of the form designer editor dialog
        Then the title step of the form designer editor dialog is "3. Define content"

        # Define filters select from the additional filter
        When the user clicks the "Next" button of the form designer editor dialog
        Then the title step of the form designer editor dialog is "4. Define filters"
        When the user clicks the "Add filter" table button in the form designer widget editor
        And the user writes "Product" in the "Property" dropdown-list field of row "1" in the form designer widget editor
        And the user presses Tab
        And the user writes "Less than or equal to" in the "Filter type" dropdown-list field of row "1" in the form designer widget editor
        And the user presses Tab
        And the user writes "Cake" in the "filter-value" text field of row "1" in the widget editor dialog
        And the user presses Tab
        And the user clicks the "add" action button of row "1" in the widget editor dialog
        And the user writes "List price" in the "filter-property" dropdown field of row "2" in the widget editor dialog
        And the user presses Tab
        And the user writes "Does not equal" in the "filter-type" dropdown field of row "2" in the widget editor dialog
        And the user presses Tab
        And the user writes "23" in the "filter-value" text field of row "2" in the widget editor dialog
        And the user presses Tab
        And the user clicks the "add" action button of row "2" in the widget editor dialog
        And the user writes "Ending date" in the "filter-property" dropdown field of row "3" in the widget editor dialog
        And the user presses Tab
        And the user writes "Time frame" in the "filter-type" dropdown field of row "3" in the widget editor dialog
        And the user presses Tab
        And the user writes "Previous day" in the "filter-value-timeframe" dropdown field of row "3" in the widget editor dialog
        And the user presses Tab
        And the user clicks the "add" action button of row "3" in the widget editor dialog
        And the user writes "Description" in the "filter-property" dropdown field of row "4" in the widget editor dialog
        And the user presses Tab
        And the user writes "Not empty" in the "filter-type" dropdown field of row "4" in the widget editor dialog
        And the user presses Tab
        And the user clicks the "add" action button of row "4" in the widget editor dialog
        And the user writes "Hot product" in the "filter-property" dropdown field of row "5" in the widget editor dialog
        And the user presses Tab
        And the user writes "False" in the "filter-value" text field of row "5" in the widget editor dialog
        And the user presses Tab
        And the user clicks the "Confirm" button of the form designer editor dialog
        Then the "Report template" titled page is displayed
        When the user clicks the "Save" labelled business action button on the main page
        Then the "Report template additionalFiltersTemplate" titled page is displayed

        # Cleaning up the data
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-reporting/ReportTemplate"
        Then the "Report templates" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user filters the "name" bound column in the table field with value "additionalFiltersTemplate"
        And the user presses Escape
        And the user selects the row with text "additionalFiltersTemplate" in the "name" bound column header of the table field
        And the user clicks the "name" bound nested field of the selected row in the table field
        Then the "Report template additionalFiltersTemplate" titled page is displayed
        When the user clicks the "Delete" labelled more actions button in the header
        Then a Confirm dialog is displayed on the main page
        When the user clicks the "Delete" button of the Confirm dialog on the main page
        Then the "Report templates" titled page is displayed

    Scenario: As a user I want to check the Conditional block button states in report template editor
        XT-71894
        # step 1: open an existing empty report template
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-reporting/ReportTemplate/eyJfaWQiOiIxMyJ9"
        Then the "Report template emptyTemplate" titled page is displayed

        # step 2: check Conditional block button is disabled
        When selects the "Template" labelled navigation anchor on the main page
        And the user selects the "template" bound form designer field on the main page
        And the user clicks in the body of the form designer
        Then the "Conditional block" button in the form designer toolbar is disabled

        # step 3: insert query table data context and select it
        When the user clicks in the body of the form designer
        And the user clicks the "Show/hide fields panel" button in the form designer toolbar
        And the user searches "Show case provider" in the node-step-tree of the form designer on the main page
        And the user selects the node-step-tree in the form designer on the main page
        And the user selects the tree-view element of level "1" with text "Show case provider" in the node-step-tree of the form designer
        And the user clicks the tree-view element in the node-step-tree of the form designer
        And the title step of the form designer editor dialog is "1. Insert mode"
        And the user selects the card with "Table" value in the selection card of the form designer editor dialog
        And the user clicks the "Next" button of the form designer editor dialog
        And the user selects the node-step-tree in the form designer on a modal
        And the user selects the tree-view element of level "1" with text "_id" in the node-step-tree of the form designer
        And the user ticks the tree-view element in the node-step-tree of the form designer
        And the user selects the tree-view element of level "1" with text "Text field" in the node-step-tree of the form designer
        And the user ticks the tree-view element in the node-step-tree of the form designer
        And the user clicks the "Next" button of the form designer editor dialog
        And the user clicks the "Next" button of the form designer editor dialog
        And the user clicks the "Confirm" button of the form designer editor dialog
        Then the "Report template emptyTemplate" titled page is displayed

        # step 4: check Conditional block button is enabled
        When the user selects the "1st" occurrence of "ShowCaseProvider" table in the form designer body
        Then the "Conditional block" button in the form designer toolbar is enabled
