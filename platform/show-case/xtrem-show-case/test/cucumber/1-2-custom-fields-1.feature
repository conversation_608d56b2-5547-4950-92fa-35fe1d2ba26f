Feature: 1-2 Custom fields 1
    # Tests custom field creation and management functionality, including creating, editing, and using custom-defined fields in records

    Scenario: As a user I want to open the customization dialog from a page
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseProvider/eyJfaWQiOiIxIn0="
        Then the "Provider 1" titled page is displayed
        And no dialogs are displayed
        When the user clicks the "Create field" labelled more actions button in the header
        And the text in the header of the dialog is "Create custom field"

    Scenario: As a user I want to declare a custom field and edit its value
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-customization/CustomField/eyJfaWQiOiIkbmV3In0="
        Then the "Custom field" titled page is displayed
        And the user selects the "Record type *" labelled reference field on the main page
        When the user clicks the lookup button of the reference field
        And the user selects the "node" bound table field on a modal
        When the user filters the "Record type" labelled column in the table field with value "Show Case Product"
        And the user selects the row 1 of the table field
        And the user clicks the "Record type" labelled nested field of the selected row in the table field
        And the user selects the "Technical name *" labelled text field on the main page
        When the user writes "simpleStringField" in the text field
        And the user selects the "Anchor field *" labelled reference field on the main page
        When the user clicks the lookup button of the reference field
        And the user selects the "anchorProperty" bound table field on a modal
        When the user filters the "Technical name" labelled column in the table field with value "Description"
        And the user selects the row 1 of the table field
        And the user clicks the "Technical name" labelled nested field of the selected row in the table field
        Then the user selects the "Display also on" labelled multi dropdown field on the main page
        When the user clicks in the multi dropdown field
        And the user selects "Page" in the multi dropdown field
        When the user selects the "Data type" labelled dropdown-list field on the main page
        When the user clicks in the dropdown-list field
        And the user selects "String" in the dropdown-list field
        When the user selects the "Field type" labelled dropdown-list field on the main page
        Then the value of the dropdown-list field is "Text field"
        And the user selects the "Helper text" labelled text field on the main page
        When the user writes "Test helper text" in the text field
        And the user clicks the "Save" labelled business action button on the main page
        When the user navigates to the following link: "@sage/xtrem-show-case/ShowCaseProduct"
        Then the "Products" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        Then the "simpleStringField" labelled column in the table field is hidden
        And the user navigates to the following link: "@sage/xtrem-show-case/ShowCaseProduct/eyJfaWQiOiIzNzUifQ=="
        Then the "Product Flax Seed" titled page is displayed
        Then the user selects the "simpleStringField" labelled text field on the main page
        Then the helper text of the text field is "Test helper text"
        When the user writes "Test value" in the text field
        And the user clicks the "Save" labelled business action button on the main page
        When the user navigates to the following link: "@sage/xtrem-show-case/ShowCaseProduct/eyJfaWQiOiIzNzUifQ=="
        Then the "Product Flax Seed" titled page is displayed
        Then the user selects the "simpleStringField" labelled text field on the main page
        Then the value of the text field is "Test value"
        And the user clears the text field
        And the user clicks the "Save" labelled business action button on the main page
        # Cleanup
        Then the user navigates to the following link: "@sage/xtrem-customization/CustomField"
        Then the "Custom fields" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        When the user filters the "name" bound column in the table field with value "simpleStringField"
        And the user selects the row 1 of the table field
        And the user clicks the "name" bound nested field of the selected row in the table field
        And the user waits 1 second
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        And the user clicks the "Delete" button of the Confirm dialog
        Then a toast containing text "Record deleted" is displayed

    Scenario: As a user I want to declare a custom field and see it in the navigation panel
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-customization/CustomField/eyJfaWQiOiIkbmV3In0="
        Then the "Custom field" titled page is displayed
        And the user selects the "Record type *" labelled reference field on the main page
        When the user clicks the lookup button of the reference field
        And the user selects the "node" bound table field on a modal
        When the user filters the "Record type" labelled column in the table field with value "Show Case Product"
        And the user selects the row 1 of the table field
        And the user clicks the "Record type" labelled nested field of the selected row in the table field
        And the user selects the "Technical name *" labelled text field on the main page
        When the user writes "simpleStringField" in the text field
        And the user selects the "Anchor field *" labelled reference field on the main page
        When the user clicks the lookup button of the reference field
        And the user selects the "anchorProperty" bound table field on a modal
        When the user filters the "Technical name" labelled column in the table field with value "Description"
        And the user selects the row 1 of the table field
        And the user clicks the "Technical name" labelled nested field of the selected row in the table field
        Then the user selects the "Display also on" labelled multi dropdown field on the main page
        When the user clicks in the multi dropdown field
        And the user selects "Page | Navigation bar" in the multi dropdown field
        When the user selects the "Data type" labelled dropdown-list field on the main page
        When the user clicks in the dropdown-list field
        And the user selects "String" in the dropdown-list field
        When the user selects the "Field type" labelled dropdown-list field on the main page
        Then the value of the dropdown-list field is "Text field"
        And the user clicks the "Save" labelled business action button on the main page
        When the user navigates to the following link: "@sage/xtrem-show-case/ShowCaseProduct/eyJfaWQiOiIzNzUifQ=="
        Then the "Product Flax Seed" titled page is displayed
        Then the user selects the "simpleStringField" labelled text field on the main page
        When the user writes "Test value" in the text field
        And the user clicks the "Save" labelled business action button on the main page
        When the user navigates to the following link: "@sage/xtrem-show-case/ShowCaseProduct"
        Then the "Products" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        When the user filters the "product" bound column in the table field with value "Flax Seed"
        And the user selects the row 1 of the table field
        Then the value of the "simpleStringField" labelled nested text field of the selected row in the table field is "Test value"
        # Cleanup
        And the user selects the row 1 of the table field
        And the user clicks the "Product" labelled nested field of the selected row in the table field
        Then the "Product Flax Seed" titled page is displayed
        Then the user selects the "simpleStringField" labelled text field on the main page
        And the user clears the text field
        And the user clicks the "Save" labelled business action button on the main page
        When the user navigates to the following link: "@sage/xtrem-customization/CustomField"
        Then the "Custom fields" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        When the user filters the "name" bound column in the table field with value "simpleStringField"
        And the user selects the row 1 of the table field
        And the user clicks the "name" bound nested field of the selected row in the table field
        And the user waits 1 second
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        And the user clicks the "Delete" button of the Confirm dialog
        Then a toast containing text "Record deleted" is displayed

    Scenario: As a user I want to declare a custom field and see it in lookup dialogs
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-customization/CustomField/eyJfaWQiOiIkbmV3In0="
        Then the "Custom field" titled page is displayed
        And the user selects the "Record type *" labelled reference field on the main page
        When the user clicks the lookup button of the reference field
        And the user selects the "node" bound table field on a modal
        When the user filters the "Record type" labelled column in the table field with value "Show Case Product"
        And the user selects the row 1 of the table field
        And the user clicks the "Record type" labelled nested field of the selected row in the table field
        And the user selects the "Technical name *" labelled text field on the main page
        When the user writes "simpleStringField" in the text field
        And the user selects the "Anchor field *" labelled reference field on the main page
        When the user clicks the lookup button of the reference field
        And the user selects the "anchorProperty" bound table field on a modal
        When the user filters the "Technical name" labelled column in the table field with value "Description"
        And the user selects the row 1 of the table field
        And the user clicks the "Technical name" labelled nested field of the selected row in the table field
        Then the user selects the "Display also on" labelled multi dropdown field on the main page
        When the user clicks in the multi dropdown field
        And the user selects "Page | Lookup" in the multi dropdown field
        When the user selects the "Data type" labelled dropdown-list field on the main page
        When the user clicks in the dropdown-list field
        And the user selects "String" in the dropdown-list field
        When the user selects the "Field type" labelled dropdown-list field on the main page
        Then the value of the dropdown-list field is "Text field"
        And the user clicks the "Save" labelled business action button on the main page
        And the user navigates to the following link: "@sage/xtrem-show-case/ShowCaseProduct/eyJfaWQiOiIzNzUifQ=="
        Then the "Product Flax Seed" titled page is displayed
        Then the user selects the "simpleStringField" labelled text field on the main page
        When the user writes "Test value" in the text field
        And the user clicks the "Save" labelled business action button on the main page
        # Reference test
        And the user navigates to the following link: "@sage/xtrem-show-case/Reference"
        Then the "Field - Reference" titled page is displayed
        And the user selects the "field" bound reference field on the main page
        When the user clicks the lookup button of the reference field
        When the user selects the "field" bound table field on a modal
        When the user filters the "product" bound column in the table field with value "Flax Seed"
        And the user selects the row 1 of the table field
        Then the value of the "simpleStringField" labelled nested text field of the selected row in the table field is "Test value"
        # Lookup without anchor test
        When the user navigates to the following link: "@sage/xtrem-show-case/HeaderSection/eyJfaWQiOiIyIn0="
        And the user selects the "Flagship Product!" labelled pod field on the main page
        And the user clicks the "Add an item" button of the pod field
        And the user selects the "flagshipProduct" bound table field on a modal
        When the user filters the "product" bound column in the table field with value "Flax Seed"
        And the user selects the row 1 of the table field
        Then the value of the "simpleStringField" labelled nested text field of the selected row in the table field is "Test value"

        # Cleanup
        Then the user navigates to the following link: "@sage/xtrem-show-case/ShowCaseProduct/eyJfaWQiOiIzNzUifQ=="
        Then the "Product Flax Seed" titled page is displayed
        Then the user selects the "simpleStringField" labelled text field on the main page
        And the user clears the text field
        And the user clicks the "Save" labelled business action button on the main page
        When the user navigates to the following link: "@sage/xtrem-customization/CustomField"
        Then the "Custom fields" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        When the user filters the "name" bound column in the table field with value "simpleStringField"
        And the user selects the row 1 of the table field
        And the user clicks the "name" bound nested field of the selected row in the table field
        And the user waits 1 second
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        And the user clicks the "Delete" button of the Confirm dialog
        Then a toast containing text "Record deleted" is displayed

    Scenario: As a user I want to declare a custom fields and edit them in tables
        # Define first field
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-customization/CustomField/eyJfaWQiOiIkbmV3In0="
        Then the "Custom field" titled page is displayed
        And the user selects the "Record type *" labelled reference field on the main page
        When the user clicks the lookup button of the reference field
        And the user selects the "node" bound table field on a modal
        When the user filters the "Record type" labelled column in the table field with value "Show Case Product"
        And the user selects the row 1 of the table field
        And the user clicks the "Record type" labelled nested field of the selected row in the table field
        And the user selects the "Technical name *" labelled text field on the main page
        When the user writes "simpleStringField" in the text field
        And the user selects the "Anchor field *" labelled reference field on the main page
        When the user clicks the lookup button of the reference field
        And the user selects the "anchorProperty" bound table field on a modal
        When the user filters the "Technical name" labelled column in the table field with value "Description"
        And the user selects the row 1 of the table field
        And the user clicks the "Technical name" labelled nested field of the selected row in the table field
        Then the user selects the "Display also on" labelled multi dropdown field on the main page
        When the user clicks in the multi dropdown field
        And the user selects "Page" in the multi dropdown field
        When the user selects the "Data type" labelled dropdown-list field on the main page
        When the user clicks in the dropdown-list field
        And the user selects "String" in the dropdown-list field
        When the user selects the "Field type" labelled dropdown-list field on the main page
        Then the value of the dropdown-list field is "Text field"
        And the user clicks the "Save" labelled business action button on the main page
        # Define second field
        Then the user navigates to the following link: "@sage/xtrem-customization/CustomField/eyJfaWQiOiIkbmV3In0="
        And the user selects the "Record type *" labelled reference field on the main page
        When the user clicks the lookup button of the reference field
        And the user selects the "node" bound table field on a modal
        When the user filters the "Record type" labelled column in the table field with value "Show Case Product"
        And the user selects the row 1 of the table field
        And the user clicks the "Record type" labelled nested field of the selected row in the table field
        And the user selects the "Technical name *" labelled text field on the main page
        When the user writes "simpleFloatField" in the text field
        And the user selects the "Anchor field *" labelled reference field on the main page
        When the user clicks the lookup button of the reference field
        And the user selects the "anchorProperty" bound table field on a modal
        When the user filters the "Technical name" labelled column in the table field with value "Category"
        And the user selects the row 1 of the table field
        And the user clicks the "Technical name" labelled nested field of the selected row in the table field
        Then the user selects the "Display also on" labelled multi dropdown field on the main page
        When the user clicks in the multi dropdown field
        And the user selects "Page" in the multi dropdown field
        When the user selects the "Data type" labelled dropdown-list field on the main page
        When the user clicks in the dropdown-list field
        And the user selects "Float" in the dropdown-list field
        When the user selects the "Field type" labelled dropdown-list field on the main page
        Then the value of the dropdown-list field is "Numeric field"
        And selects the "Validation" labelled navigation anchor on the main page
        And the user selects the "Scale" labelled numeric field on the main page
        When the user writes "2" in the numeric field

        And the user clicks the "Save" labelled business action button on the main page
        # Edit data in table row
        And the user navigates to the following link: "@sage/xtrem-show-case/ShowCaseProvider/eyJfaWQiOiIzIn0="
        Then the "Provider 3" titled page is displayed
        And the user selects the "Products" labelled table field on the main page
        When the user selects the row 1 of the table field
        When the user writes "123.45" in the "simpleFloatField" labelled nested numeric field of the selected row in the table field
        When the user writes "Test value" in the "simpleStringField" labelled nested text field of the selected row in the table field
        And the user clicks the "Save" labelled business action button on the main page
        # Check if the data got saved on the table
        And the user navigates to the following link: "@sage/xtrem-show-case/ShowCaseProvider/eyJfaWQiOiIzIn0="
        Then the "Provider 3" titled page is displayed
        And the user selects the "Products" labelled table field on the main page
        And the user selects the row 1 of the table field
        Then the value of the "simpleFloatField" labelled nested numeric field of the selected row in the table field is "123.45"
        And the user selects the row 1 of the table field
        Then the value of the "simpleStringField" labelled nested text field of the selected row in the table field is "Test value"
        # Check if the values are accessible from a normal page
        Then the user navigates to the following link: "@sage/xtrem-show-case/ShowCaseProduct/eyJfaWQiOiI1MDEifQ=="
        Then the "Product bicycle" titled page is displayed
        Then the user selects the "simpleStringField" labelled text field on the main page
        Then the value of the text field is "Test value"
        And the user clears the text field
        Then the user selects the "simpleFloatField" labelled numeric field on the main page
        Then the value of the numeric field is "123.45"
        And the user clears the numeric field

        And the user clicks the "Save" labelled business action button on the main page

        # Cleanup
        Then the user navigates to the following link: "@sage/xtrem-customization/CustomField"
        Then the "Custom fields" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        When the user filters the "name" bound column in the table field with value "simpleStringField"
        And the user selects the row 1 of the table field
        And the user clicks the "name" bound nested field of the selected row in the table field
        And the user waits 1 second
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        And the user clicks the "Delete" button of the Confirm dialog
        Then a toast containing text "Record deleted" is displayed
        Then the "Custom fields" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        When the user filters the "name" bound column in the table field with value "simpleFloatField"
        And the user selects the row 1 of the table field
        And the user clicks the "name" bound nested field of the selected row in the table field
        And the user waits 1 second
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        And the user clicks the "Delete" button of the Confirm dialog
        Then a toast containing text "Record deleted" is displayed

    Scenario: As a user I want to declare a custom field and edit it on a pod collection
        # Field definition
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-customization/CustomField/eyJfaWQiOiIkbmV3In0="
        Then the "Custom field" titled page is displayed
        And the user selects the "Record type *" labelled reference field on the main page
        When the user clicks the lookup button of the reference field
        And the user selects the "node" bound table field on a modal
        When the user filters the "Record type" labelled column in the table field with value "Show Case Invoice Line"
        And the user selects the row 1 of the table field
        And the user clicks the "Record type" labelled nested field of the selected row in the table field
        And the user selects the "Technical name *" labelled text field on the main page
        When the user writes "testSwitchProperty" in the text field
        And the user selects the "Anchor field *" labelled reference field on the main page
        When the user clicks the lookup button of the reference field
        And the user selects the "anchorProperty" bound table field on a modal
        When the user filters the "Technical name" labelled column in the table field with value "Net Price"
        And the user selects the row 1 of the table field
        And the user clicks the "Technical name" labelled nested field of the selected row in the table field
        Then the user selects the "Display also on" labelled multi dropdown field on the main page
        When the user clicks in the multi dropdown field
        And the user selects "Page" in the multi dropdown field
        When the user selects the "Data type" labelled dropdown-list field on the main page
        When the user clicks in the dropdown-list field
        And the user selects "Boolean" in the dropdown-list field
        When the user selects the "Field type" labelled dropdown-list field on the main page
        When the user clicks in the dropdown-list field
        And the user selects "Switch field" in the dropdown-list field

        And the user clicks the "Save" labelled business action button on the main page

        # Check the switch
        When the user navigates to the following link: "@sage/xtrem-show-case/PodCollection/eyJfaWQiOiIyIn0="
        Then the "Pod Collection" titled page is displayed
        And the user selects the "lines" bound pod collection field on the main page
        And the user selects the "Mints - Striped Red 85" labelled pod collection item of the selected pod collection field
        Then the value of the "testSwitchProperty" labelled nested switch field of the selected pod collection item is set to "OFF"
        And the user clicks the "testSwitchProperty" labelled nested switch field of the selected pod collection item
        Then the value of the "testSwitchProperty" labelled nested switch field of the selected pod collection item is set to "ON"
        And the user clicks the "Save" labelled business action button on the main page
        # Check if value was actually saved
        When the user navigates to the following link: "@sage/xtrem-show-case/PodCollection/eyJfaWQiOiIyIn0="
        Then the "Pod Collection" titled page is displayed
        And the user selects the "lines" bound pod collection field on the main page
        And the user selects the "Mints - Striped Red 85" labelled pod collection item of the selected pod collection field
        Then the value of the "testSwitchProperty" labelled nested switch field of the selected pod collection item is set to "ON"
        And the user clicks the "testSwitchProperty" labelled nested switch field of the selected pod collection item
        Then the value of the "testSwitchProperty" labelled nested switch field of the selected pod collection item is set to "OFF"
        And the user clicks the "Save" labelled business action button on the main page
        # Cleanup
        Then the user navigates to the following link: "@sage/xtrem-customization/CustomField"
        Then the "Custom fields" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user filters the "name" bound column in the table field with value "testSwitchProperty"
        And the user selects the row 1 of the table field
        And the user clicks the "name" bound nested field of the selected row in the table field
        And the user waits 1 second
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        And the user clicks the "Delete" button of the Confirm dialog
        Then a toast containing text "Record deleted" is displayed

    Scenario: As a user I want to declare a custom field and edit it on a vital pod
        # Field definition
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-customization/CustomField/eyJfaWQiOiIkbmV3In0="
        Then the "Custom field" titled page is displayed
        And the user selects the "Record type *" labelled reference field on the main page
        When the user clicks the lookup button of the reference field
        And the user selects the "node" bound table field on a modal
        When the user filters the "Record type" labelled column in the table field with value "Show Case Product Origin Address"
        And the user selects the row 1 of the table field
        And the user clicks the "Record type" labelled nested field of the selected row in the table field
        And the user selects the "Technical name *" labelled text field on the main page
        When the user writes "testTextProperty" in the text field
        And the user selects the "Anchor field *" labelled reference field on the main page
        When the user clicks the lookup button of the reference field
        And the user selects the "anchorProperty" bound table field on a modal
        When the user filters the "Technical name" labelled column in the table field with value "Country"
        And the user selects the row 1 of the table field
        And the user clicks the "Technical name" labelled nested field of the selected row in the table field
        Then the user selects the "Display also on" labelled multi dropdown field on the main page
        When the user clicks in the multi dropdown field
        And the user selects "Page" in the multi dropdown field
        When the user selects the "Data type" labelled dropdown-list field on the main page
        When the user clicks in the dropdown-list field
        And the user selects "String" in the dropdown-list field
        And the user clicks the "Save" labelled business action button on the main page

        # Check the switch
        When the user navigates to the following link: "@sage/xtrem-show-case/VitalPodBlock/eyJfaWQiOiIxNiJ9"
        Then the "Field - Vital Pod (as Block)" titled page is displayed
        And the user selects the "Address" labelled vital pod field on the main page
        And the user writes "Test Value" in the "testTextProperty" labelled nested text field of the vital pod field
        And the user clicks the "Save" labelled business action button on the main page

        # Check saved value
        When the user navigates to the following link: "@sage/xtrem-show-case/VitalPodBlock/eyJfaWQiOiIxNiJ9"
        Then the "Field - Vital Pod (as Block)" titled page is displayed
        And the user selects the "Address" labelled vital pod field on the main page
        Then the value of the "testTextProperty" labelled nested text field in the vital pod field is "Test Value"
        And the user writes "" in the "testTextProperty" labelled nested text field of the vital pod field
        And the user clicks the "Save" labelled business action button on the main page

        # Cleanup
        Then the user navigates to the following link: "@sage/xtrem-customization/CustomField"
        Then the "Custom fields" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        When the user filters the "name" bound column in the table field with value "testTextProperty"
        And the user selects the row 1 of the table field
        And the user clicks the "name" bound nested field of the selected row in the table field
        And the user waits 1 second
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        And the user clicks the "Delete" button of the Confirm dialog
        Then a toast containing text "Record deleted" is displayed

    Scenario: As a user I want to use the "Display error" filtering feature with custom fields
        XT-87745
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-customization/CustomField/eyJfaWQiOiIkbmV3In0="
        Then the "Custom field" titled page is displayed
        And the user selects the "Record type *" labelled reference field on the main page
        When the user clicks the lookup button of the reference field
        And the user selects the "node" bound table field on a modal
        When the user filters the "Record type" labelled column in the table field with value "Show Case Product"
        And the user selects the row 1 of the table field
        And the user clicks the "Record type" labelled nested field of the selected row in the table field
        And the user selects the "Technical name *" labelled text field on the main page
        When the user writes "simpleStringField" in the text field
        And the user selects the "Anchor field *" labelled reference field on the main page
        When the user clicks the lookup button of the reference field
        And the user selects the "anchorProperty" bound table field on a modal
        When the user filters the "Technical name" labelled column in the table field with value "Description"
        And the user selects the row 1 of the table field
        And the user clicks the "Technical name" labelled nested field of the selected row in the table field
        Then the user selects the "Display also on" labelled multi dropdown field on the main page
        When the user clicks in the multi dropdown field
        And the user selects "Page" in the multi dropdown field
        When the user selects the "Data type" labelled dropdown-list field on the main page
        When the user clicks in the dropdown-list field
        And the user selects "String" in the dropdown-list field
        When the user selects the "Field type" labelled dropdown-list field on the main page
        Then the value of the dropdown-list field is "Text field"
        And the user clicks the "Save" labelled business action button on the main page

        And the user navigates to the following link: "@sage/xtrem-show-case/ShowCaseProvider/eyJfaWQiOiIzIn0="
        Then the "Provider 3" titled page is displayed
        And the user selects the "Products" labelled table field on the main page
        When the user selects the row 1 of the table field
        And the user writes "-4" in the "qty" bound nested numeric field of the selected row in the table field
        And the user writes "atest" in the "simpleStringField" labelled nested text field of the selected row in the table field
        And the user clicks the "Save" labelled business action button on the main page
        And the user selects the "Products" labelled table field on the main page
        Then a total of "1" errors is found in the table field
        And the user filters by errors the table field
        And the user selects the row 1 of the table field
        Then the value of the "qty" bound nested numeric field of the selected row in the table field is "-4.00"
        Then the value of the "simpleStringField" labelled nested text field of the selected row in the table field is "atest"

        # Cleanup
        Then the user navigates to the following link: "@sage/xtrem-customization/CustomField"
        Then the "Custom fields" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        When the user filters the "name" bound column in the table field with value "simpleStringField"
        And the user selects the row 1 of the table field
        And the user clicks the "name" bound nested field of the selected row in the table field
        And the user waits 1 second
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        And the user clicks the "Delete" button of the Confirm dialog
        Then a toast containing text "Record deleted" is displayed
        Then the "Custom fields" titled page is displayed
