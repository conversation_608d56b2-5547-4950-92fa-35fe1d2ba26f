Feature: 4-2 Table keyboard
    # Tests keyboard navigation and interaction within table components, including cell navigation, editing functionality, and keyboard shortcut behavior

    <PERSON><PERSON><PERSON>: As a user I can use the keyboard to navigate forward and backward through all table cells in edit mode
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/InlineEdit/eyJfaWQiOiIyIn0="
        Then the "Inline Edit" titled page is displayed
        And the user selects the "field" bound table field on the main page
        And the user selects the row 1 of the table field
        When the user clicks the "Product" labelled nested field of the selected row in the table field
        Then the element with the following selector is focused: "[row-index='0'] .ag-cell-inline-editing[aria-colindex='4'] input"
        When the user presses Tab
        Then the element with the following selector is focused: "[row-index='0'] .ag-cell-inline-editing[aria-colindex='5'] input"
        When the user presses Tab
        Then the element with the following selector is focused: "[row-index='0'] .ag-cell-inline-editing[aria-colindex='7'] input"
        When the user presses Tab
        Then the element with the following selector is focused: "[row-index='0'] .ag-cell-inline-editing[aria-colindex='8'] input"
        When the user presses Tab
        Then the element with the following selector is focused: "[row-index='0'] .ag-cell-inline-editing[aria-colindex='9'] input"
        When the user presses Tab
        Then the element with the following selector is focused: "[row-index='0'] .ag-cell-inline-editing[aria-colindex='10'] input"
        When the user presses Tab
        Then the element with the following selector is focused: "[row-index='0'] .ag-cell-inline-editing[aria-colindex='13'] input"
        When the user presses Tab
        Then the element with the following selector is focused: "[data-testid='field-0-13-input']"
        When the user presses Tab
        Then the element with the following selector is focused: "[data-testid='field-0-13-input-chevron']"
        When the user presses Tab
        Then the element with the following selector is focused: "[data-testid='field-0-14-input']"
        When the user presses Tab
        Then the element with the following selector is focused: "[data-testid='field-0-14-input']+div.e-ui-select-lookup-button-plain>button"
        When the user presses Tab
        Then the element with the following selector is focused: "[row-index='0'] .ag-cell-inline-editing[aria-colindex='16'] input"
        When the user presses Tab
        Then the element with the following selector is focused: ".ag-popup .e-table-field-actions-container"
        When the user presses Tab
        Then the element with the following selector is focused: ".ag-theme-balham.ag-popup [data-component='action-popover-button']"
        When the user presses Tab
        Then the element with the following selector is focused: "[row-index='1'] .ag-cell-inline-editing[aria-colindex='4'] input"
        When the user presses Shift+Tab
        Then the element with the following selector is focused: ".ag-popup .e-table-field-actions-container"
        When the user presses Shift+Tab
        Then the element with the following selector is focused: ".ag-theme-balham.ag-popup [data-component='action-popover-button']"
        When the user presses Shift+Tab
        Then the element with the following selector is focused: "[row-index='0'] .ag-cell-inline-editing[aria-colindex='16'] input"
        When the user presses Shift+Tab
        Then the element with the following selector is focused: "[data-testid='field-0-14-input']"
        When the user presses Shift+Tab
        Then the element with the following selector is focused: "[data-testid='field-0-13-input']"
        When the user presses Shift+Tab
        Then the element with the following selector is focused: "[row-index='0'] .ag-cell-inline-editing[aria-colindex='13'] input"
        When the user presses Shift+Tab
        Then the element with the following selector is focused: "[row-index='0'] .ag-cell-inline-editing[aria-colindex='10'] input"
        When the user presses Shift+Tab
        Then the element with the following selector is focused: "[row-index='0'] .ag-cell-inline-editing[aria-colindex='9'] input"
        When the user presses Shift+Tab
        Then the element with the following selector is focused: "[row-index='0'] .ag-cell-inline-editing[aria-colindex='8'] input"
        When the user presses Shift+Tab
        Then the element with the following selector is focused: "[row-index='0'] .ag-cell-inline-editing[aria-colindex='7'] input"
        When the user presses Shift+Tab
        Then the element with the following selector is focused: "[row-index='0'] .ag-cell-inline-editing[aria-colindex='5'] input"
        When the user presses Shift+Tab
        Then the element with the following selector is focused: "[row-index='0'] .ag-cell-inline-editing[aria-colindex='4'] input"


    Scenario: As a user I can use the Space key to check/uncheck a nested checkbox field
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/InlineEdit/eyJfaWQiOiIyIn0="
        Then the "Inline Edit" titled page is displayed
        And the user selects the "field" bound table field on the main page
        And the user selects the row 1 of the table field
        When the user clicks the "Net Price" labelled nested field of the selected row in the table field
        Then the element with the following selector is focused: "[row-index='0'] .ag-cell-inline-editing[aria-colindex='7'] input"
        When the user presses Tab
        Then the element with the following selector is focused: "[row-index='0'] .ag-cell-inline-editing[aria-colindex='8'] input"
        Then the value of the "Hot" labelled nested checkbox field of the selected row in the table field is "false"
        When the user presses Space
        Then the value of the "Hot" labelled nested checkbox field of the selected row in the table field is "true"
        And the element with the following selector is focused: "[row-index='0'] .ag-cell-inline-editing[aria-colindex='8'] input"
        When the user presses Space
        Then the value of the "Hot" labelled nested checkbox field of the selected row in the table field is "false"
        And the element with the following selector is focused: "[row-index='0'] .ag-cell-inline-editing[aria-colindex='8'] input"

    Scenario: As a user I can click to check/uncheck a nested checkbox field
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/InlineEdit/eyJfaWQiOiIyIn0="
        Then the "Inline Edit" titled page is displayed
        And the user selects the "field" bound table field on the main page
        And the user selects the row 1 of the table field
        Then the value of the "Hot" labelled nested checkbox field of the selected row in the table field is "false"
        When the user clicks the "Hot" labelled nested field of the selected row in the table field
        Then the value of the "Hot" labelled nested checkbox field of the selected row in the table field is "true"
        And the element with the following selector is focused: "[row-index='0'] [aria-colindex='8'] input"
        When the user clicks the "Hot" labelled nested field of the selected row in the table field
        Then the value of the "Hot" labelled nested checkbox field of the selected row in the table field is "false"
        And the element with the following selector is focused: "[row-index='0'] [aria-colindex='8'] input"

    Scenario: As a user I can use the Space key to toggle a nested switch field
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/InlineEdit/eyJfaWQiOiIyIn0="
        Then the "Inline Edit" titled page is displayed
        And the user selects the "field" bound table field on the main page
        And the user selects the row 1 of the table field
        When the user clicks the "Ordered Quantity" labelled nested field of the selected row in the table field
        Then the element with the following selector is focused: "[row-index='0'] .ag-cell-inline-editing[aria-colindex='9'] input"
        When the user presses Tab
        Then the element with the following selector is focused: "[row-index='0'] .ag-cell-inline-editing[aria-colindex='10'] input"
        Then the value of the "Hot" labelled nested checkbox field of the selected row in the table field is "false"
        When the user presses Space
        Then the value of the "Hot" labelled nested checkbox field of the selected row in the table field is "true"
        And the element with the following selector is focused: "[row-index='0'] .ag-cell-inline-editing[aria-colindex='10'] input"
        When the user presses Space
        Then the value of the "Hot" labelled nested checkbox field of the selected row in the table field is "false"
        And the element with the following selector is focused: "[row-index='0'] .ag-cell-inline-editing[aria-colindex='10'] input"

    Scenario: As a user I can click to toggle a nested switch field
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/InlineEdit/eyJfaWQiOiIyIn0="
        Then the "Inline Edit" titled page is displayed
        And the user selects the "field" bound table field on the main page
        And the user selects the row 1 of the table field
        Then the value of the "Hot" labelled nested checkbox field of the selected row in the table field is "false"
        When the user clicks the "Hot (Switch)" labelled nested field of the selected row in the table field
        Then the value of the "Hot" labelled nested checkbox field of the selected row in the table field is "true"
        And the element with the following selector is focused: "[row-index='0'] [aria-colindex='10'] input"
        When the user clicks the "Hot (Switch)" labelled nested field of the selected row in the table field
        Then the value of the "Hot" labelled nested checkbox field of the selected row in the table field is "false"
        And the element with the following selector is focused: "[row-index='0'] [aria-colindex='10'] input"

    Scenario: As a user I can use the keyboard to navigate forward and backward through all table actions
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/RowActions"
        Then the "Row actions" titled page is displayed
        And the user selects the "table16" bound table field on the main page
        And the user selects the row 1 of the table field
        When the user clicks the "Optional" labelled nested field of the selected row in the table field
        Then the element with the following selector is focused: "[row-index='0'] .ag-cell-inline-editing[aria-colindex='5'] input"
        When the user presses Tab
        Then the element with the following selector is focused: ".ag-popup .e-table-field-actions-container"
        When the user presses Tab
        Then the element with the following selector is focused: ".ag-popup-editor > .e-table-field-actions-container > button:nth-of-type(1)"
        When the user presses Shift+Tab
        Then the element with the following selector is focused: "[row-index='0'] .ag-cell-inline-editing[aria-colindex='5'] input"
        When the user presses Tab
        When the user presses Tab
        When the user presses Tab
        Then the element with the following selector is focused: ".ag-popup-editor > .e-table-field-actions-container > button:nth-of-type(2)"
        When the user presses Shift+Tab
        Then the element with the following selector is focused: ".ag-popup-editor > .e-table-field-actions-container > button:nth-of-type(1)"
        When the user presses Tab
        When the user presses Tab
        Then the element with the following selector is focused: ".ag-popup-editor > .e-table-field-actions-container > button:nth-of-type(3)"
        When the user presses Tab
        Then the element with the following selector is focused: ".ag-popup-editor > .e-table-field-actions-container > *:nth-child(4) [data-component='action-popover-button']"
        When the user presses Shift+Tab
        Then the element with the following selector is focused: ".ag-popup-editor > .e-table-field-actions-container > button:nth-of-type(3)"
        When the user presses Tab
        When the user presses Tab
        Then the element with the following selector is focused: "[row-index='1'] .ag-cell-inline-editing[aria-colindex='3'] input"
        When the user presses Shift+Tab
        Then the element with the following selector is focused: ".ag-popup .e-table-field-actions-container"

    Scenario: As a user I can Control+C to copy a value from a table cell
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Table/eyJfaWQiOiIyIn0="
        Then the "Field - Table" titled page is displayed
        And the user selects the "field" bound table field on the main page
        And the user selects the row 1 of the table field
        When the user clicks the "Id" labelled nested field of the selected row in the table field
        And the user presses Ctrl+c
        Then the value of the clipboard is "313"
        When the user presses Tab
        And the user presses Ctrl+c
        Then the value of the clipboard is "Appetiser - Bought"
        When the user presses Tab
        And the user presses Ctrl+c
        Then the value of the clipboard is "frame"
        When the user presses Tab
        And the user presses Ctrl+c
        Then the value of the clipboard is "16"
        When the user presses Tab
        And the user presses Ctrl+c
        Then the value of the clipboard is ""
        When the user presses Tab
        And the user presses Ctrl+c
        Then the value of the clipboard is "9.94"
        When the user presses Tab
        And the user presses Ctrl+c
        Then the value of the clipboard is "19.16"
        When the user presses Tab
        And the user presses Ctrl+c
        Then the value of the clipboard is "Medium"
        When the user presses Tab
        And the user presses Ctrl+c
        Then the value of the clipboard is "54"
        When the user presses Tab
        And the user presses Ctrl+c
        Then the value of the clipboard is "false"
        When the user presses Tab
        And the user presses Ctrl+c
        Then the value of the clipboard is "false"
        When the user presses Tab
        And the user presses Ctrl+c
        Then the value of the clipboard is "http://appe.sage.com"
        When the user presses Tab
        And the user presses Ctrl+c
        Then the value of the clipboard is "10"
        When the user presses Tab
        And the user presses Ctrl+c
        Then the value of the clipboard is "85.59"
        When the user presses Tab
        And the user presses Ctrl+c
        Then the value of the clipboard is "106.99"
        When the user presses Tab
        And the user presses Ctrl+c
        Then the value of the clipboard is "Amazon"
        When the user presses Tab
        And the user presses Ctrl+c
        Then the value of the clipboard is "Amazon"
        When the user presses Tab
        And the user presses Ctrl+c
        Then the value of the clipboard is ""
        When the user presses Tab
        And the user presses Ctrl+c
        Then the value of the clipboard is "2020-07-27"
        When the user presses Tab
        And the user presses Ctrl+c
        Then the value of the clipboard is ""
        When the user presses Tab
        And the user presses Ctrl+c
        Then the value of the clipboard is "good"

    Scenario: As a user I can Control+V to paste a value in table cells
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Table/eyJfaWQiOiIyIn0="
        Then the "Field - Table" titled page is displayed
        And the user selects the "field" bound table field on the main page
        And the user selects the row 1 of the table field
        When the user clicks the "Id" labelled nested field of the selected row in the table field
        And the user presses Ctrl+c
        Then the value of the clipboard is "313"
        When the user presses ArrowRight
        And the user presses Ctrl+v
        Then the value of the "Product" labelled nested text field of the selected row in the table field is "313"
        When the user presses ArrowRight
        And the user presses Ctrl+v
        Then the value of the "Description" labelled nested text field of the selected row in the table field is "313"
        When the user presses ArrowRight
        And the user presses Ctrl+v
        Then the value of the "Quantity" labelled nested text field of the selected row in the table field is "313"
        When the user presses ArrowRight
        And the user presses Ctrl+v
        Then the value of the "Fixed Quantity" labelled nested text field of the selected row in the table field is "313"
        When the user presses ArrowRight
        And the user presses Ctrl+v
        Then the value of the "List Price" labelled nested text field of the selected row in the table field is "313.00"
        When the user presses ArrowRight
        When the user presses ArrowRight
        When the user presses ArrowRight
        When the user presses ArrowRight
        When the user presses ArrowRight
        When the user presses ArrowRight
        When the user presses ArrowRight
        When the user presses ArrowRight
        And the user presses Ctrl+v
        Then the value of the "Tax" labelled nested text field of the selected row in the table field is "T 313.00"
        When the user presses ArrowRight
        When the user presses ArrowRight
        And the user presses Ctrl+v
        Then the value of the "Provider" labelled nested text field of the selected row in the table field is "Amazon"
        When the user presses ArrowRight
        When the user presses ArrowRight
        And the user presses Ctrl+v
        Then the value of the "Email" labelled nested text field of the selected row in the table field is "313"
        When the user presses ArrowRight
        And the user presses Ctrl+v
        Then the value of the "Date" labelled nested text field of the selected row in the table field is "07/27/2020"
        When the user presses ArrowRight
        When the user presses ArrowRight
        And the user presses Ctrl+v
        Then the value of the "Category" labelled nested text field of the selected row in the table field is "Good"
        And the user writes "Ali Express" in the "provider__textField" bound nested reference field of the selected row in the table field
        And the user presses Enter
        Then a toast containing text "New provider set: Ali Express" is displayed
        When the user dismisses all the toasts
        When the user clicks the "Amount" labelled nested field of the selected row in the table field
        When the user presses ArrowRight
        And the user presses Ctrl+c
        Then the value of the clipboard is "Ali Express"
        When the user presses ArrowDown
        And the user presses Ctrl+v
        Then the user selects the row 2 of the table field
        Then the value of the "Provider" labelled nested text field of the selected row in the table field is "Ali Express"

    Scenario: As a user I can access row actions using my keyboard
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/RowActions"
        Then the "Row actions" titled page is displayed
        And the user selects the "table91" bound table field on the main page
        And the user selects the row 1 of the table field
        Then the value of the "Readonly" labelled nested text field of the selected row in the table field is "a"
        When the user clicks the "Readonly" labelled nested field of the selected row in the table field
        When the user presses Tab
        When the user presses Tab
        Then the element with the following selector is focused: "[data-testid~='e-field-bind-table91'] [row-index='0'] .ag-cell-not-inline-editing[aria-colindex='6']"
        When the user presses Tab
        Then the element with the following selector is focused: "[data-testid~='e-field-bind-table91'] [row-index='0'] .ag-cell-not-inline-editing[aria-colindex='6'] [data-component='action-popover-button']"
        When the user presses Enter
        Then the element with the following selector is focused: "[data-component='action-popover'] button:first-of-type"
        When the user presses Escape
        Then the element with the following selector is focused: "[data-testid~='e-field-bind-table91'] [row-index='0'] .ag-cell-not-inline-editing[aria-colindex='6'] [data-component='action-popover-button']"
        When the user presses Tab
        Then the element with the following selector is focused: "[data-testid~='e-field-bind-table91'] [row-index='1'] .ag-cell-not-inline-editing[aria-colindex='3']"
        When the user presses Shift+Tab
        Then the element with the following selector is focused: "[data-testid~='e-field-bind-table91'] [row-index='0'] .ag-cell-not-inline-editing[aria-colindex='6']"
        When the user presses Shift+Tab
        Then the element with the following selector is focused: "[data-testid~='e-field-bind-table91'] [row-index='0'] .ag-cell-not-inline-editing[aria-colindex='6'] [data-component='action-popover-button']"
        When the user presses Shift+Tab
        Then the element with the following selector is focused: "[data-testid~='e-field-bind-table91'] [row-index='0'] .ag-cell-not-inline-editing[aria-colindex='5']"
        When the user presses Enter
        Then the element with the following selector is focused: "[data-testid~='e-field-bind-table91'] [row-index='0'] .ag-cell-inline-editing[aria-colindex='5'] input"
        When the user presses Tab
        Then the element with the following selector is focused: ".ag-popup .e-table-field-actions-container"
        When the user presses Tab
        Then the element with the following selector is focused: ".ag-popup [data-component='action-popover-button']"
        When the user presses Enter
        Then the element with the following selector is focused: "[data-component='action-popover'] button:first-of-type"
        When the user presses Escape
        Then the element with the following selector is focused: ".ag-popup [data-component='action-popover-button']"
        When the user presses Tab
        Then the element with the following selector is focused: "[data-testid~='e-field-bind-table91'] [row-index='1'] .ag-cell-inline-editing[aria-colindex='3'] input"
        When the user presses Shift+Tab
        Then the element with the following selector is focused: ".ag-popup .e-table-field-actions-container"
        When the user presses Shift+Tab
        Then the element with the following selector is focused: ".ag-popup [data-component='action-popover-button']"
        When the user presses Shift+Tab
        Then the element with the following selector is focused: "[data-testid~='e-field-bind-table91'] [row-index='0'] .ag-cell-inline-editing[aria-colindex='5'] input"
        And the user selects the "table15" bound table field on the main page
        And the user selects the row 1 of the table field
        Then the value of the "Readonly" labelled nested text field of the selected row in the table field is "a"
        When the user clicks the "Readonly" labelled nested field of the selected row in the table field
        When the user presses Tab
        When the user presses Tab
        Then the element with the following selector is focused: "[data-testid~='e-field-bind-table15'] [row-index='0'] .ag-cell-not-inline-editing[aria-colindex='6']"
        When the user presses Tab
        Then the element with the following selector is focused: "[data-testid~='e-field-bind-table15'] [row-index='0'] .ag-cell-not-inline-editing[aria-colindex='6'] .e-table-field-actions-container button:nth-of-type(1)"
        When the user presses Tab
        Then the element with the following selector is focused: "[data-testid~='e-field-bind-table15'] [row-index='0'] .ag-cell-not-inline-editing[aria-colindex='6'] .e-table-field-actions-container button:nth-of-type(2)"
        When the user presses Tab
        Then the element with the following selector is focused: "[data-testid~='e-field-bind-table15'] [row-index='0'] .ag-cell-not-inline-editing[aria-colindex='6'] .e-table-field-actions-container button:nth-of-type(3)"
        When the user presses Tab
        Then the element with the following selector is focused: "[data-testid~='e-field-bind-table15'] [row-index='0'] .ag-cell-not-inline-editing[aria-colindex='6'] .e-table-field-actions-container [data-component='action-popover-button']"
        When the user presses Enter
        Then the element with the following selector is focused: "[data-component='action-popover'] button:first-of-type"
        When the user presses Escape
        Then the element with the following selector is focused: "[data-testid~='e-field-bind-table15'] [row-index='0'] .ag-cell-not-inline-editing[aria-colindex='6'] [data-component='action-popover-button']"
        When the user presses Tab
        Then the element with the following selector is focused: "[data-testid~='e-field-bind-table15'] [row-index='1'] .ag-cell-not-inline-editing[aria-colindex='3']"
        When the user presses Shift+Tab
        Then the element with the following selector is focused: "[data-testid~='e-field-bind-table15'] [row-index='0'] .ag-cell-not-inline-editing[aria-colindex='6']"
        When the user presses Shift+Tab
        Then the element with the following selector is focused: "[data-testid~='e-field-bind-table15'] [row-index='0'] .ag-cell-not-inline-editing[aria-colindex='6'] [data-component='action-popover-button']"
        When the user presses Shift+Tab
        Then the element with the following selector is focused: "[data-testid~='e-field-bind-table15'] [row-index='0'] .ag-cell-not-inline-editing[aria-colindex='6'] .e-table-field-actions-container button:nth-of-type(3)"
        When the user presses Shift+Tab
        Then the element with the following selector is focused: "[data-testid~='e-field-bind-table15'] [row-index='0'] .ag-cell-not-inline-editing[aria-colindex='6'] .e-table-field-actions-container button:nth-of-type(2)"
        When the user presses Shift+Tab
        Then the element with the following selector is focused: "[data-testid~='e-field-bind-table15'] [row-index='0'] .ag-cell-not-inline-editing[aria-colindex='6'] .e-table-field-actions-container button:nth-of-type(1)"
        When the user presses Shift+Tab
        Then the element with the following selector is focused: "[data-testid~='e-field-bind-table15'] [row-index='0'] .ag-cell-not-inline-editing[aria-colindex='5']"
        When the user presses Enter
        Then the element with the following selector is focused: "[data-testid~='e-field-bind-table15'] [row-index='0'] .ag-cell-inline-editing[aria-colindex='5'] input"
        When the user presses Tab
        Then the element with the following selector is focused: ".ag-popup .e-table-field-actions-container"
        When the user presses Tab
        Then the element with the following selector is focused: ".ag-popup .e-table-field-actions-container button:nth-of-type(1)"
        When the user presses Tab
        Then the element with the following selector is focused: ".ag-popup .e-table-field-actions-container button:nth-of-type(2)"
        When the user presses Tab
        Then the element with the following selector is focused: ".ag-popup .e-table-field-actions-container button:nth-of-type(3)"
        When the user presses Tab
        Then the element with the following selector is focused: ".ag-popup .e-table-field-actions-container [data-component='action-popover-button']"
        When the user presses Enter
        Then the element with the following selector is focused: "[data-component='action-popover'] button:first-of-type"
        When the user presses Escape
        Then the element with the following selector is focused: ".ag-popup .e-table-field-actions-container [data-component='action-popover-button']"
        When the user presses Tab
        Then the element with the following selector is focused: "[data-testid~='e-field-bind-table15'] [row-index='1'] .ag-cell-inline-editing[aria-colindex='3'] input"
        When the user presses Shift+Tab
        Then the element with the following selector is focused: ".ag-popup .e-table-field-actions-container"
        When the user presses Shift+Tab
        Then the element with the following selector is focused: ".ag-popup [data-component='action-popover-button']"
        When the user presses Shift+Tab
        Then the element with the following selector is focused: ".ag-popup .e-table-field-actions-container button:nth-of-type(3)"
        When the user presses Shift+Tab
        Then the element with the following selector is focused: ".ag-popup .e-table-field-actions-container button:nth-of-type(2)"
        When the user presses Shift+Tab
        Then the element with the following selector is focused: ".ag-popup .e-table-field-actions-container button:nth-of-type(1)"
        When the user presses Shift+Tab
        Then the element with the following selector is focused: "[data-testid~='e-field-bind-table15'] [row-index='0'] .ag-cell-inline-editing[aria-colindex='5'] input"


    Scenario: As a user I can hit Enter to execute a table row action - standard mode
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/RowActions"
        Then the "Row actions" titled page is displayed
        And the user selects the "table112" bound table field on the main page
        And the user selects the row 1 of the table field
        When the user clicks the "Readonly" labelled nested field of the selected row in the table field
        When the user presses Tab
        When the user presses Tab
        When the user presses Tab
        Then the element with the following selector is focused: "[data-testid~='e-field-bind-table112'] [row-index='0'] .ag-cell-not-inline-editing[aria-colindex='6'] .e-table-field-actions-container button:nth-of-type(1)"
        When the user presses Enter
        Then an info dialog appears on the main page

    Scenario: As a user I can hit Enter to execute a table row action - edit mode
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/RowActions"
        Then the "Row actions" titled page is displayed
        And the user selects the "table112" bound table field on the main page
        And the user selects the row 1 of the table field
        When the user clicks the "Optional" labelled nested field of the selected row in the table field
        When the user presses Tab
        When the user presses Tab
        Then the element with the following selector is focused: ".ag-popup .e-table-field-actions-container button:nth-of-type(1)"
        When the user presses Enter
        Then an info dialog appears on the main page
