Feature: 2-1 Navigation arrows
     # Tests the record navigation functionality, verifying that users can navigate between sequential records using previous and next arrows

     # previous and next navigation arrow buttons
     Scenario Outline: <Device> - As a user I want to be able to navigate to previous record from an open record
          Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/ShowCaseInvoice/eyJfaWQiOiIyIn0="
          Then the "Invoice 2 03/22/2020" titled page is displayed
          And the "previous" navigation arrow button in the header on the main page is displayed
          When the user clicks the "previous" navigation arrow button in the header on the main page
          Then the "Invoice 1 06/25/2020" titled page is displayed
          Examples:
               | Device  |
               | desktop |
               | tablet  |

     Scenario Outline: <Device> - As a user I want to be able to navigate to next record from an open record
          Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/ShowCaseInvoice/eyJfaWQiOiIyIn0="
          Then the "Invoice 2 03/22/2020" titled page is displayed
          And the "next" navigation arrow button in the header on the main page is displayed
          When the user clicks the "next" navigation arrow button in the header on the main page
          Then the "Invoice 3 02/06/2020" titled page is displayed
          Examples:
               | Device  |
               | desktop |
               | tablet  |

     Scenario Outline: <Device> As a user I want to be see I have reached the first record
          Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/ShowCaseInvoice/eyJfaWQiOiIxIn0="
          Then the "Invoice 1 06/25/2020" titled page is displayed
          And the "previous" navigation arrow button in the header on the main page is disabled
          And the "next" navigation arrow button in the header on the main page is enabled
          Examples:
               | Device  |
               | desktop |
               | tablet  |


     Scenario Outline: <Device> As a user I want to be see I have reached the last record
          Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/ShowCaseInvoice/eyJfaWQiOiI1MjkifQ=="
          Then the "Invoice 529 11/25/2022" titled page is displayed
          And the "next" navigation arrow button in the header on the main page is disabled
          And the "previous" navigation arrow button in the header on the main page is enabled
          Examples:
               | Device  |
               | desktop |
               | tablet  |

     Scenario Outline: <Device> As a user when I can search for records in the split view list I don´t want to use navigation arrows
          Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/ShowCaseInvoice/eyJfaWQiOiIyIn0="
          Then the "Invoice 2 03/22/2020" titled page is displayed
          When the user opens the navigation panel
          Then the "previous" navigation arrow button in the header on the main page is hidden
          And the "next" navigation arrow button in the header on the main page is hidden
          Examples:
               | Device  |
               | desktop |
               | tablet  |


     Scenario Outline: <Device> As a user when I edit a record and move to the next I need a warning I have unsaved data
          Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/ShowCaseProvider/eyJfaWQiOiIxIn0="
          Then the "Provider 1" titled page is displayed
          When the user selects the "Integer" labelled numeric field on the main page
          And the user writes "100" in the numeric field
          And the user clicks the "next" navigation arrow button in the header on the main page
          Then a warn dialog appears on the main page
          When the user clicks the "Discard" button of the Confirm dialog
          Then the "Provider 2" titled page is displayed
          And the user selects the "Integer" labelled numeric field on the main page
          Then the value of the numeric field is "2"
          Examples:
               | Device  |
               | desktop |
               | tablet  |

     Scenario Outline: <Device> As a user when I edit a record and move to the previous I need a warning I have unsaved data
          Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/ShowCaseProvider/eyJfaWQiOiIyIn0="
          Then the "Provider 2" titled page is displayed
          When the user selects the "Integer" labelled numeric field on the main page
          And the user writes "100" in the numeric field
          And the user clicks the "previous" navigation arrow button in the header on the main page
          Then a warn dialog appears on the main page
          When the user clicks the "Discard" button of the Confirm dialog
          Then the "Provider 1" titled page is displayed
          And the user selects the "Integer" labelled numeric field on the main page
          Then the value of the numeric field is "1"
          Examples:
               | Device  |
               | desktop |
               | tablet  |

     Scenario: As a user I want to check return arrow availability on pages and navigate back using the return arrow
          XT-90749
          # navigate through the application and check return arrow is not displayed when not necessary
          Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseProviderReturnArrow"
          Then the "Providers" titled page is displayed
          And the "return" navigation arrow button in the header on the main page is hidden
          When the user selects the "$navigationPanel" bound table field on the main page
          And the user selects the row 1 of the table field
          And the user clicks the "Name" labelled nested field of the selected row in the table field
          Then the "Provider 1" titled page is displayed
          And the "return" navigation arrow button in the header on the main page is hidden
          When the user selects the "$navigationPanel" bound table field on the navigation panel
          And the user clicks the card 2 in the table field
          Then the "Provider 2" titled page is displayed
          And the "return" navigation arrow button in the header on the main page is hidden

          # check return arrow is displayed when necessary
          When the user selects the "products" bound table field on the main page
          And the user selects the row 1 of the table field
          And the user clicks the "Product" labelled nested field of the selected row in the table field
          And the user presses Enter
          Then the "Product Beef - Bresaola" titled page is displayed
          And the "return" navigation arrow button in the header on the main page is displayed
          When the user opens the navigation panel
          And the user selects the "$navigationPanel" bound table field on the navigation panel
          And the user clicks the card 2 in the table field
          Then the "Product Appetiser - Bought" titled page is displayed
          And the "return" navigation arrow button in the header on the main page is displayed
          When the user selects the "Go to ShowCaseInvoice" labelled link field on the main page
          And the user clicks in the link field
          Then the "Invoices" titled page is displayed
          And the "return" navigation arrow button in the header on the main page is displayed
          When the user selects the "$navigationPanel" bound table field on the main page
          And the user selects the row 2 of the table field
          And the user clicks the "Customer" labelled nested field of the selected row in the table field
          Then the "Invoice 2 03/22/2020" titled page is displayed
          And the "return" navigation arrow button in the header on the main page is displayed
          When the user selects the "$navigationPanel" bound table field on the navigation panel
          And the user clicks the card 3 in the table field
          Then the "Invoice 3 02/06/2020" titled page is displayed
          And the "return" navigation arrow button in the header on the main page is displayed

          # navigate back using the return arrow
          When the user clicks the "return" navigation arrow button in the header on the main page
          Then the "Product Appetiser - Bought" titled page is displayed
          When the user clicks the "return" navigation arrow button in the header on the main page
          Then the "Provider 2" titled page is displayed
          And the "return" navigation arrow button in the header on the main page is hidden

     Scenario: As a user I want to check return arrow no longer available when navigating directly to another URL
          XT-90749
          # navigate through the application until return arrow is displayed
          Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseProviderReturnArrow"
          Then the "Providers" titled page is displayed
          When the user selects the "$navigationPanel" bound table field on the main page
          And the user selects the row 1 of the table field
          And the user clicks the "Name" labelled nested field of the selected row in the table field
          Then the "Provider 1" titled page is displayed
          When the user selects the "$navigationPanel" bound table field on the navigation panel
          And the user selects the "products" bound table field on the main page
          And the user selects the row 1 of the table field
          And the user clicks the "Product" labelled nested field of the selected row in the table field
          And the user presses Enter
          Then the "Product Spinach - Baby" titled page is displayed
          And the "return" navigation arrow button in the header on the main page is displayed

          # check return arrow is not displayed when navigating directly to another URL
          When the user navigates to the following link: "@sage/xtrem-show-case/ShowCaseInvoice"
          Then the "return" navigation arrow button in the header on the main page is hidden

     @ClearDashboards
     @ClearDashboardsBefore
     Scenario: As a user I want to check return arrow when navigating from dashboard
          XT-90749
          # make sure dashboard has a widget from which to navigate
          Given the user opens the application on a desktop
          And the dashboard page is displayed
          Then the "Create a dashboard to get started." subtitled empty dashboard is displayed
          When the user clicks the create button on the dashboard
          Then the dashboard creation dialog is displayed
          When the user selects the template 10 in the dashboard creation dialog
          And the user clicks the "next" button in the dashboard creation dialog
          Then the "Showcase dashboard" titled dashboard in the dashboard editor is displayed
          When the user clicks the "cancel" button in the dashboard editor footer
          Then the "Showcase dashboard" titled dashboard is displayed
          And the "Users" titled widget in the dashboard is displayed

          # check return arrow is displayed when navigating away from the dashboard
          When the user selects the "Users" titled table widget field in the dashboard
          And the user selects the card 2 of the table widget field
          And the user clicks the link of the row 2 on the left of the card of the table widget field
          Then the "User Acme Support" titled page is displayed
          And the "return" navigation arrow button in the header on the main page is displayed
          When the user opens the navigation panel
          And the user selects the "$navigationPanel" bound table field on the navigation panel
          And the user clicks the card 3 in the table field
          Then the "User aghate vauloup" titled page is displayed
          And the "return" navigation arrow button in the header on the main page is displayed

          # check using the return arrow navigates back to the dashboard
          When the user clicks the "return" navigation arrow button in the header on the main page
          Then the "Showcase dashboard" titled dashboard is displayed


     Scenario: As a user I want to check return arrow when navigating from 360 view
          XT-90749
          # navigate to a page where 360 view is available and check return arrow is not displayed
          Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/StandardShowCaseProvider/eyJfaWQiOiIxIn0="
          Then the "Provider 1" titled page is displayed
          And the "return" navigation arrow button in the header on the main page is hidden
          When the user opens the navigation panel
          And the user selects the "$navigationPanel" bound table field on the navigation panel
          And the user clicks the card 2 in the table field
          Then the "Provider 2" titled page is displayed
          And the "return" navigation arrow button in the header on the main page is hidden

          # turn on 360 view and navigate away from the 360 view page and check return arrow is displayed
          When the user clicks the 360 view switch in the header
          Then the 360 view switch in the header is ON
          And the "Users" titled widget in the dashboard is displayed
          When the user selects the "Users" titled table widget field in the dashboard
          And the user selects the card 2 of the table widget field
          And the user clicks the link of the row 2 on the left of the card of the table widget field
          Then the "User Acme Support" titled page is displayed
          And the "return" navigation arrow button in the header on the main page is displayed

          # navigate back to the 360 view page using the return arrow and check 360 view is ON
          When the user clicks the "return" navigation arrow button in the header on the main page
          Then the "Provider 2" titled page is displayed
          And the 360 view switch in the header is ON
          And the "Users" titled widget in the dashboard is displayed
