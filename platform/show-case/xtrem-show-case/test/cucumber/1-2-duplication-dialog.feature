Feature: 1-2 Duplication dialog
    # Tests record duplication functionality, verifying mandatory field validation, field value retention, and successful creation of duplicated records

    Scenario: As a user, I want to duplicate a record that has duplication mandatory fields
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseProduct/eyJfaWQiOiIzMTMifQ=="
        Then the "Product Appetiser - Bought" titled page is displayed
        When the user clicks the "Duplicate" labelled button in the header
        And the user waits 2 seconds
        Then an info dialog appears on the main page
        And the dialog title is "Duplicate record" on the main page
        And the dialog subtitle is "Complete the fields below to duplicate this record." on the main page
        And the "Product" labelled text field on a modal is displayed
        And the "Description" labelled text field on a modal is displayed
        And the "Provider" labelled reference field on a modal is displayed
        When the user selects the "Product" labelled text field on a modal
        Then the value of the text field is "New Appetiser - Bought"
        When the user selects the "Description" labelled text field on a modal
        Then the value of the text field is "frame"
        When the user selects the "Provider" labelled reference field on a modal
        Then the value of the reference field is "Amazon"
        And the reference field tunnel link is displayed
        When the user clicks the "Duplicate" labelled business action button on a modal
        Then the "Product New Appetiser - Bought" titled page is displayed
        And a success toast containing text "Record was duplicated successfully." is displayed

        # mandatory field validations
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseProduct/eyJfaWQiOiIzMTMifQ=="
        Then the "Product Appetiser - Bought" titled page is displayed
        When the user clicks the "Duplicate" labelled button in the header
        And the user waits 2 seconds
        Then an info dialog appears on the main page
        And the dialog title is "Duplicate record" on the main page
        And the dialog subtitle is "Complete the fields below to duplicate this record." on the main page
        When the user selects the "Product" labelled text field on a modal
        And the user clears the text field
        And the user presses Enter
        Then a error toast containing text "You need to enter a value." is displayed
        When the user writes "New product" in the text field
        And the user selects the "Provider" labelled reference field on a modal
        And the user clears the reference field
        And the user presses Enter
        And the user clicks the "Duplicate" labelled business action button on a modal
        And the user waits 2 seconds
        Then a validation error message is displayed containing text
            """
             ShowCaseProduct.provider: Mandatory property
            """
