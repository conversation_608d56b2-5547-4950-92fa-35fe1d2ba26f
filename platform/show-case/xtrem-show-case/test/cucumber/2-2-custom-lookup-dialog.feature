Feature: 2-2 Custom Lookup Dialog
    # Tests the functionality of custom lookup dialogs, including automatic configuration of both single and multiple selection dialogs with verification of displayed columns and selection behavior
    Scenario: As a user I want to see a fully automatically configured lookup dialog
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/CustomLookupDialog"
        Then the "Dialog - Custom Lookup" titled page is displayed
        When the user clicks in the "openSingleSelectionLookupAutoConfigured" bound button field on the main page
        And the user selects the "$applicationCodeLookup" bound table field on a modal
        Then the "Product" labelled column in the table field is displayed
        And the "Description" labelled column in the table field is displayed
        And the "Hot Product" labelled column in the table field is displayed
        And the "Qty" labelled column in the table field is displayed
        And the "Category" labelled column in the table field is displayed
        And the "Release date" labelled column in the table field is displayed
        And the user selects the row 5 of the table field
        When the user clicks the "Product" labelled nested field of the selected row in the table field
        Then no dialogs are displayed
        Then a toast containing text "Appetizer - Crab And Brie" is displayed

    Scenario: As a user I want to see a fully automatically configured multi lookup dialog
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/CustomLookupDialog"
        Then the "Dialog - Custom Lookup" titled page is displayed
        When the user clicks in the "openMultiSelectionLookupAutoConfigured" bound button field on the main page
        And the user selects the "$applicationCodeLookup" bound table field on a modal
        Then the "Product" labelled column in the table field is displayed
        And the "Description" labelled column in the table field is displayed
        And the "Hot Product" labelled column in the table field is displayed
        And the "Qty" labelled column in the table field is displayed
        And the "Category" labelled column in the table field is displayed
        And the "Release date" labelled column in the table field is displayed
        And the user selects the row 5 of the table field
        When the user ticks the main checkbox of the selected row in the table field
        And the user selects the row 7 of the table field
        When the user ticks the main checkbox of the selected row in the table field
        And the user clicks the "Select" button of the dialog on the main page
        Then no dialogs are displayed
        Then a toast containing text "Appetizer - Crab And Brie" is displayed
        Then a toast containing text "Apple - Macintosh" is displayed

    Scenario: As a user I want to see a multi lookup dialog and modify certain columns which get auto selected and then select a few more
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/CustomLookupDialog"
        Then the "Dialog - Custom Lookup" titled page is displayed
        When the user clicks in the "openMultiSelectionLookupWithEditableColumns" bound button field on the main page
        And the user selects the "$applicationCodeLookup" bound table field on a modal
        Then the "Product" labelled column in the table field is displayed
        And the "Description" labelled column in the table field is displayed
        And the "ID" labelled column in the table field is displayed
        And the "Price" labelled column in the table field is displayed
        And the user selects the row 5 of the table field
        When the user writes "123" in the "Price" labelled nested numeric field of the selected row in the table field
        And the user selects the row 7 of the table field
        When the user writes "456" in the "Price" labelled nested numeric field of the selected row in the table field
        And the user selects the row 8 of the table field
        When the user ticks the main checkbox of the selected row in the table field
        And the user clicks the "Select" button of the dialog on the main page
        Then no dialogs are displayed
        Then a toast containing text "Apricots Fresh - 123" is displayed
        Then a toast containing text "Banana - Green - 456" is displayed
        Then a toast containing text "Beef - Bresaola - 26.28" is displayed

    Scenario: As a user I want the select button to be disabled if the modifications on the lookup dialog are invalid
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/CustomLookupDialog"
        Then the "Dialog - Custom Lookup" titled page is displayed
        When the user clicks in the "openMultiSelectionLookupWithEditableColumns" bound button field on the main page
        And the user selects the "$applicationCodeLookup" bound table field on a modal
        Then the "Product" labelled column in the table field is displayed
        And the "Description" labelled column in the table field is displayed
        And the "ID" labelled column in the table field is displayed
        And the "Price" labelled column in the table field is displayed
        And the "Select" button of the dialog is enabled
        And the user selects the row 5 of the table field
        When the user writes "1234" in the "Price" labelled nested numeric field of the selected row in the table field
        And the "Select" button of the dialog is disabled
        And the user selects the row 7 of the table field
        When the user writes "456" in the "Price" labelled nested numeric field of the selected row in the table field
        And the "Select" button of the dialog is enabled
        And the user clicks the "Select" button of the dialog on the main page
        Then a toast containing text "Banana - Green - 456" is displayed

    Scenario: As a user I want to see a lookup dialog with remapped columns
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/CustomLookupDialog"
        Then the "Dialog - Custom Lookup" titled page is displayed
        When the user clicks in the "openRemappedLookupData" bound button field on the main page
        And the user selects the "$applicationCodeLookup" bound table field on a modal
        And the user selects the row 1 of the table field
        Then the value of the "Price" labelled nested text field of the selected row in the table field is "1,084.56"
        And the user selects the row 2 of the table field
        Then the value of the "Price" labelled nested text field of the selected row in the table field is "1,024.15"
