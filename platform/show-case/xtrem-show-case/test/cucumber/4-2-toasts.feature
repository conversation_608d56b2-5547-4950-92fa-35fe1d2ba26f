Feature: 4-2 Toasts
    # Tests toast notification functionality, verifying proper display and dismissal of system notifications across different types (error, success, info)

    Scenario: As an ATP XTreeM user I can verifiy the notification

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Toasts"
        Then the "Toasts" titled page is displayed
        And the user selects the "Message" labelled text field on the main page
        And the user writes "Toast verification message" in the text field
        And the user selects the "Timeout" labelled numeric field on the main page
        And the user writes "10000" in the numeric field
        And the user blurs the numeric field
        And the user selects the "toastsType" bound select field on the main page
        And the user clicks in the select field
        And the user clears the select field
        And the user selects "info" in the select field
        When the user clicks the "toastsButton" bound button on the main page
        And no error toast or validation error message is displayed
        Then a toast with text "Toast verification message" is displayed
        Then a toast containing text "message" is displayed
        Then the value of the toast is "Toast verification message"
        Then a info toast with text "Toast verification message" is displayed
        Then a info toast containing text "Toast verification message" is displayed
        And the user dismisses all the toasts

    Scenario: As an ATP XTreeM user I can verifiy the notification when multiple toasts are returned
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Toasts"
        Then the "Toasts" titled page is displayed
        And the user selects the "Message" labelled text field on the main page
        And the user writes "multiple toasts" in the text field
        And the user blurs the text field
        When the user clicks the "toastsButton" bound button on the main page
        When the user clicks the "toastsButton" bound button on the main page
        When the user clicks the "toastsButton" bound button on the main page
        And the user selects the "Message" labelled text field on the main page
        And the user writes "other toasts" in the text field
        And the user blurs the text field
        When the user clicks the "toastsButton" bound button on the main page
        # make sure the last notification returned is not disrupted by the previous notifications returned
        Then a toast with text "other toasts" is displayed
    # And the user waits 3 seconds
    # only the last notification should be displayed while the previous ones have been closed
    # And takes a screenshot

    Scenario Outline: As an ATP / XTreeM user I can verify the <Type> type of the toast returned by exact match
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Toasts"
        Then the "Toasts" titled page is displayed
        And the user selects the "Message" labelled text field on the main page
        And the user writes "Toast verification message" in the text field
        And the user blurs the text field
        And the user selects the "toastsType" bound select field on the main page
        And the user clicks in the select field
        And the user clears the select field
        And at least the following list of options is displayed for the select field:"default | error | info | success | warning"
        And the user selects "<Type>" in the select field
        When the user clicks the "toastsButton" bound button on the main page
        Then a <Type> toast with text "Toast verification message" is displayed
        And the user dismisses all the toasts
        Examples:
            | Type    |
            | default |
            | error   |
            | info    |
            | success |
            | warning |

    Scenario Outline: As an ATP / XTreeM user I can verify the <Type> type of the toast returned by contains
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Toasts"
        Then the "Toasts" titled page is displayed
        And the user selects the "Message" labelled text field on the main page
        And the user writes "Toast verification message" in the text field
        And the user blurs the text field
        And the user selects the "toastsType" bound select field on the main page
        And the user clicks in the select field
        And the user clears the select field
        And at least the following list of options is displayed for the select field:"default | error | info | success | warning"
        And the user selects "<Type>" in the select field
        When the user clicks the "toastsButton" bound button on the main page
        Then a <Type> toast containing text "Toast verification message" is displayed
        And the user dismisses all the toasts
        Examples:
            | Type    |
            | default |
            | error   |
            | info    |
            | success |
            | warning |
