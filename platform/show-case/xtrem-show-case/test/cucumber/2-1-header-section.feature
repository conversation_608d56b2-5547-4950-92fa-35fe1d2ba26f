Feature: 2-1 Header section
    # Tests the page header section functionality, verifying field interactions and data display in the prominent header section.

    <PERSON>enario: As a developer I want my automation script to interact with fields in the header section
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/HeaderSection/eyJfaWQiOiIxIn0="
        Then the "Provider Ali Express" titled page is displayed
        When the user selects the "ID" labelled text field on the main page
        Then the value of the text field is "1"
        When the user selects the "Date" labelled date field on the main page
        Then the value of the date field is "07/01/2019"
        When the user selects the "ID" labelled text field on the main page
        And the user writes "2" in the text field
        Then the value of the text field is "2"

    Sc<PERSON>rio: As a user I want to hide the header section using the toggle button
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/HeaderSection/eyJfaWQiOiIyIn0="
        Then the "Provider Amazon" titled page is displayed
        And the "ID" labelled text field on the main page is displayed
        And the "Date" labelled date field on the main page is displayed
        And the user selects the header section toggle button in the header
        #hide header section
        And the "ID" labelled text field on the main page is hidden
        And the "Date" labelled date field on the main page is hidden
        When the user selects the header section toggle button in the header
        #display header section
        Then the "ID" labelled text field on the main page is displayed
        And the "Date" labelled date field on the main page is displayed
