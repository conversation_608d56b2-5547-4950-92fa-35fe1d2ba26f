Feature: 4-2 Vital payload reference
    # Tests vital payload reference functionality, verifying the ability to create records and reference them within the same interface using specialized reference fields
    XT-7031

    Scenario: As a user I want to add a vital record to a table and then refer to it using a reference field that is bound to the same node type
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/VitalPayloadReference/eyJfaWQiOiIxIn0="
        And the user selects the "products" bound table field on the main page
        And the user clicks the "addNewRecord" bound action of the table field
        And the user selects the "Product" labelled text field on the sidebar
        And the user writes "Test Apple" in the text field
        And the user selects the "Description" labelled text field on the sidebar
        And the user writes "Benchmark apple" in the text field
        And the user selects the "Quantity" labelled numeric field on the sidebar
        And the user writes "10" in the numeric field
        And the user selects the "List price" labelled numeric field on the sidebar
        And the user writes "2000" in the numeric field
        And the user selects the "Tax" labelled numeric field on the sidebar
        And the user writes "400" in the numeric field
        And the user selects the "Amount" labelled numeric field on the sidebar
        And the user writes "2400" in the numeric field
        And the user selects the "Ending date" labelled date field on the sidebar
        And the user writes a generated date in the date field with value "T"
        And the user selects the "Category Select" labelled radio field on the sidebar
        When the user selects the value "great" in the radio field
        Then the value "great" of the radio field is selected
        When the user clicks the "Add product" labelled business action button on the sidebar
        And the user selects the "Flagship Product" labelled reference field on the main page
        When the user clicks the lookup button of the reference field
        And the user selects the "flagshipProduct" bound table field on a modal
        And the user filters the "description" bound column in the table field with value "Benchmark apple"
        When the user clicks the "Id" labelled column of the table field
        When the user clicks the "Id" labelled column of the table field
        And the user selects the row 1 of the table field
        And the user clicks the "Id" labelled nested field of the selected row in the table field
        And the user selects the "Flagship Product" labelled reference field on the main page
        Then the value of the reference field is "Benchmark apple"
        Then the helper text of the reference field is "-1"
        When the user clicks the "Save" labelled business action button on the main page
        # When the user selects the "Save" labelled business action button on the main page
        And a toast containing text "Record updated" is displayed
        And the user selects the "Flagship Product" labelled reference field on the main page
        Then the value of the reference field is "Benchmark apple"
        #XT-23692 / refactored and fixed
        And the user navigates to the following link: "@sage/xtrem-show-case/ShowCaseProduct/eyJfaWQiOiIxMDYifQ=="
        Then the user opens the navigation panel
        And the user searches for "Test Apple" in the navigation panel
        And the user clicks the record with the text "Test Apple" in the navigation panel
        And the user selects the "Description" labelled text field on the main page
        Then the value of the text field is "Benchmark apple"
        And the user selects the "Quantity" labelled numeric field on the main page
        Then the value of the numeric field is "10"

        Then the user navigates to the following link: "@sage/xtrem-show-case/VitalPayloadReference/eyJfaWQiOiIxIn0="
        And the user selects the "Flagship Product" labelled reference field on the main page
        And the user clears the reference field

        Then the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record updated" is displayed

        And the user navigates to the following link: "@sage/xtrem-show-case/ShowCaseProduct"
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user filters the "Product" labelled column in the table field with value "Test Apple"
        And the user selects the row 1 of the table field
        And the user clicks the "Product" labelled nested field of the selected row in the table field

        And the user selects the "Description" labelled text field on the main page
        Then the value of the text field is "Benchmark apple"
        And the user selects the "Quantity" labelled numeric field on the main page
        Then the value of the numeric field is "10"

        Then the user clicks the "Delete" labelled more actions button in the header
        And the text in the body of the dialog is "You are about to delete this record." on the main page
        And the user clicks the "Delete" button of the Message dialog

        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user filters the "Product" labelled column in the table field with value "Test Apple"
        And the table field is empty
