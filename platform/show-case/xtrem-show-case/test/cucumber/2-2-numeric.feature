Feature: 2-2 Numeric
    # Tests the numeric field component across different devices, verifying data entry, formatting, and validation of numeric values

    Scenario Outline: <Device> - As an ATP XTreeM User I can write and verify a numeric field using label
        XT-25838
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Numeric"
        Then the "Numeric" titled page is displayed

        Given the user selects the "Title" labelled text field on the main page
        When the user writes "Sample title" in the text field
        And the user presses Tab

        Given the user selects the "Sample title" labelled numeric field on the main page
        When the user writes "70.77" in the numeric field
        Then the value of the numeric field is "70.77"
        And the user clears the numeric field
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |


    Scenario Outline: <Device> - As an ATP XTreeM User I can write and verify a numeric field using bind
        XT-25838
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Numeric"
        Then the "Numeric" titled page is displayed

        Given the user selects the "field" bound numeric field on the main page
        When the user writes "99.88" in the numeric field
        Then the value of the numeric field is "99.88"
        And the user clears the numeric field
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |


    Scenario Outline: <Device> - As an ATP XTreeM User I can scroll to and blur the numeric field
        XT-25838
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Numeric"

        Given the user selects the "field" bound numeric field on the main page
        And the user scrolls to the numeric field
        And the user blurs the numeric field
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    #numeric Field: Set / check properties

    Scenario Outline: <Device> - As and ATP XTreeM user I can verify the numeric field title
        XT-25838
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Numeric"
        Then the "Numeric" titled page is displayed

        Given the user selects the "Title" labelled text field on the main page
        When the user writes "Sample title" in the text field
        And the user presses Tab

        Given the user selects the "field" bound numeric field on the main page
        When the user clicks in the numeric field
        Then the title of the numeric field is "Sample title"
        Then the title of the numeric field is displayed

        Given the user selects the "Is title hidden" labelled checkbox field on the main page
        When the user ticks the checkbox field

        Given the user selects the "field" bound numeric field on the main page
        Then the title of the numeric field is hidden
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |


    Scenario Outline: <Device> - As and ATP XTreeM user I can verify the numeric field helper text
        XT-25838
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Numeric"
        Then the "Numeric" titled page is displayed

        Given the user selects the "Helper text" labelled text field on the main page
        When the user writes "Sample text" in the text field
        And the user presses Tab

        Given the user selects the "field" bound numeric field on the main page
        When the user clicks in the numeric field
        Then the helper text of the numeric field is "Sample text"
        Then the helper text of the numeric field is displayed

        Given the user selects the "Is helper text hidden" labelled checkbox field on the main page
        When the user ticks the checkbox field

        Given the user selects the "field" bound numeric field on the main page
        Then the helper text of the numeric field is hidden
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |


    Scenario Outline: <Device> - As and ATP XTreeM user I can verify if the numeric field is displayed or hidden
        XT-25838
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Numeric"
        Then the "Numeric" titled page is displayed

        Given the user selects the "field" bound numeric field on the main page
        When the user clicks in the numeric field
        Then the "field" bound numeric field on the main page is displayed

        Given the user selects the "Is hidden" labelled checkbox field on the main page
        When the user ticks the checkbox field
        Then the "field" bound numeric field on the main page is hidden
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |


    Scenario Outline:<Device> - As and ATP XTreeM user I can verify if the numeric field is enabled or disabled
        XT-25838
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Numeric"
        Then the "Numeric" titled page is displayed

        Given the user selects the "field" bound numeric field on the main page
        Then the numeric field is enabled

        Given the user selects the "Is disabled" labelled checkbox field on the main page
        When the user ticks the checkbox field

        Given the user selects the "field" bound numeric field on the main page
        Then the numeric field is disabled
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |


    Scenario Outline: <Device> - As and ATP XTreeM user I can verify if the numeric field is read-only
        XT-25838
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Numeric"
        Then the "Numeric" titled page is displayed

        Given the user selects the "Is read only" labelled checkbox field on the main page
        When the user ticks the checkbox field

        Given the user selects the "field" bound numeric field on the main page
        Then the numeric field is read-only
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario: Field marked as invalid on invalid max value
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Numeric"
        Then the "Numeric" titled page is displayed
        When the user selects the "Max value" labelled numeric field on the main page
        And the user writes "8" in the numeric field
        And the user blurs the numeric field
        When the user selects the "field" bound numeric field on the main page
        And the user writes "10" in the numeric field
        And the user blurs the numeric field
        Then the numeric field is invalid
        When the user selects the "field" bound numeric field on the main page
        And the user writes "5" in the numeric field
        And the user blurs the numeric field
        Then the numeric field is valid

    Scenario: Field marked as invalid on invalid min value
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Numeric"
        Then the "Numeric" titled page is displayed
        When the user selects the "Min value" labelled numeric field on the main page
        And the user writes "8" in the numeric field
        And the user blurs the numeric field
        When the user selects the "field" bound numeric field on the main page
        And the user writes "5" in the numeric field
        And the user blurs the numeric field
        Then the numeric field is invalid
        When the user selects the "field" bound numeric field on the main page
        And the user writes "10" in the numeric field
        And the user blurs the numeric field
        Then the numeric field is valid

    Scenario: As a user I want fields with isNonZero value validation rule to be highlighted with zero scale
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Numeric"
        Then the "Numeric" titled page is displayed
        When the user selects the "Non-Zero integer" labelled numeric field on the main page
        And the user writes "0" in the numeric field
        Then the numeric field is invalid
        When the user selects the "Non-Zero integer" labelled numeric field on the main page
        And the user writes "123" in the numeric field
        Then the numeric field is valid

    Scenario: Field marked as invalid on invalid value
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Numeric"
        Then the "Numeric" titled page is displayed
        When the user selects the "Mandatory" labelled numeric field on the main page
        And the user clicks in the numeric field
        And the user writes "1" in the numeric field
        And the user blurs the numeric field
        And the user clears the numeric field
        Then the numeric field is invalid
        When the user selects the "Mandatory" labelled numeric field on the main page
        And the user writes "2" in the numeric field
        And the user blurs the numeric field
        Then the numeric field is valid


    Scenario: Set the value of the numeric field
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Numeric"
        Then the "Numeric" titled page is displayed

        Given the user selects the "Scale" labelled numeric field on the main page
        And the user writes "0" in the numeric field

        Given the user selects the "field" bound numeric field on the main page
        When the user clicks in the numeric field
        And the user writes "12300" in the numeric field
        Then the value of the numeric field is "12,300"


    Scenario: Set the value of a numeric field with 2 digit precision
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Numeric"
        Then the "Numeric" titled page is displayed

        Given the user selects the "Scale" labelled numeric field on the main page
        And the user writes "2" in the numeric field

        Given the user selects the "field" bound numeric field on the main page
        And the user writes "12.34" in the numeric field
        Then the value of the numeric field is "12.34"
        And the user writes "12.355" in the numeric field
        Then the value of the numeric field is "12.35"


    Scenario: Trigger custom click event handler
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Numeric"
        Then the "Numeric" titled page is displayed

        Then the "clickTriggered" bound label field on the main page is hidden

        Given the user selects the "field" bound numeric field on the main page
        When the user clicks in the numeric field

        Then the "clickTriggered" bound label field on the main page is displayed

    Scenario Outline: <Language> - As a user I want to use insert decimal numbers to the numeric field using different locales
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Numeric"
        When the user switches language to <Language>
        Then the user navigates to the following link: "@sage/xtrem-show-case/Numeric"
        Then the "Numeric" titled page is displayed

        Given the user selects the "field" bound numeric field on the main page
        And the user clears the numeric field
        And the user writes <Value> in the numeric field
        Then the value of the numeric field is <Expected>

        # The space in the expected French number is a special, non breaking space
        Examples:
            | Language     | Value     | Expected       |
            | "English US" | ".32"     | "0.32"         |
            | "German"     | ",32"     | "0,32"         |
            | "French"     | "1234,56" | "1 234,56"     |
            | "English US" | "1"       | "1.00"         |
            | "German"     | "1"       | "1,00"         |
            | "English US" | "1000000" | "1,000,000.00" |
            | "German"     | "1000000" | "1.000.000,00" |
            | "English US" | "-4.321"  | "-4.32"        |
            | "German"     | "-4,321"  | "-4,32"        |

    Scenario: As a user I want the numeric field to only call the nested onChange handler, when the value has actually changed
        XT-71061
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TableSidebarFocus/eyJfaWQiOiIxIn0="
        Then the "Table with Sidebar - Focus Scenario" titled page is displayed
        When the user selects the "products" bound table field on the main page
        And the user selects the row 1 of the table field
        And the user clicks the "Edit Product" inline action button of the selected row in the table field
        And the user selects the "netPrice" bound numeric field on the sidebar
        Then the value of the numeric field is "25.48"
        When the user writes "25.00" in the numeric field
        And the user presses Tab
        Then a Confirm dialog is displayed
        And the text in the header of the dialog is "Are you sure you want to change the price?"
        When the user clicks the "Confirm" button of the dialog
        And the user waits 1 second
        Then the value of the numeric field is "25.00"
        When the user selects the "netPrice" bound numeric field on the sidebar
        And the user writes "25.00" in the numeric field
        And the user presses Tab
        Then no Confirm dialog is displayed
        And the value of the numeric field is "25.00"
