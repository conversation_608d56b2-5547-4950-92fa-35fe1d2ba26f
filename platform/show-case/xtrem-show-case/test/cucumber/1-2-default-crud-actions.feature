Feature: 1-2 Default crud actions
    # Tests standard CRUD action functionality, verifying that default operations like delete properly trigger confirmation prompts and execute data modifications

    <PERSON><PERSON><PERSON>: As a developer I want to the standard delete CRUD action to direct to display a prompt
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/PageDefaultCrudActions/eyJfaWQiOiIxODkifQ=="
        And the user selects the "Product" labelled text field on the main page
        Then the value of the text field is "Godiva White Chocolate"
        When the user clicks the "Delete" labelled more actions button in the header
        Then an info dialog appears on the main page
        And the text in the header of the dialog is "Overridden dialog title"
