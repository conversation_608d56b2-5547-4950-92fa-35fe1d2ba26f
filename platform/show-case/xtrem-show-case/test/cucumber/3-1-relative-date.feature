#e-table-field-lines
Feature: 3-1 Relative date
    # Tests the relative date field component, ensuring it correctly displays and manages date values relative to the current date and control the value returned based on the user language.

    Scenario Outline: As a user I want to check the value of the relative date field
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/RelativeDate/eyJfaWQiOiIyIn0="
        Then the "Field - Relative date" titled page is displayed
        And the user selects the "today" bound relative date field on the main page
        Then the value of the relative date field is "today"
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: As a user I want to check the value of the relative date field in Spanish
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/RelativeDate/eyJfaWQiOiIyIn0="
        When the user switches language to "Spanish"
        And the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/RelativeDate/eyJfaWQiOiIyIn0="
        Then the "Field - Relative date" titled page is displayed
        And the user selects the "today" bound relative date field on the main page
        Then the value of the relative date field is "hoy"
        Examples:
            | Device  |
            | desktop |
            | tablet  |

    Scenario Outline: As a user I want to check the value of the relative date field from 5 years ago
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/RelativeDate/eyJfaWQiOiIyIn0="
        Then the "Field - Relative date" titled page is displayed
        And the user selects the "fiveyearsago" bound relative date field on the main page
        Then the value of the relative date field is "5 years ago"
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: As a user I want to check the value of the relative date field from yesterday
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/RelativeDate/eyJfaWQiOiIyIn0="
        Then the "Field - Relative date" titled page is displayed
        And the user selects the "yesterday" bound relative date field on the main page
        Then the value of the relative date field is "yesterday"
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: As a user I want to check the value of the relative date field from yesterday in Spanish
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/RelativeDate/eyJfaWQiOiIyIn0="
        When the user switches language to "Spanish"
        And the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/RelativeDate/eyJfaWQiOiIyIn0="
        Then the "Field - Relative date" titled page is displayed
        And the user selects the "yesterday" bound relative date field on the main page
        Then the value of the relative date field is "ayer"
        Examples:
            | Device  |
            | desktop |
            | tablet  |

    Scenario: As a user I want to check the value of the relative date in a table
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/RelativeDate/eyJfaWQiOiIyIn0="
        Then the "Field - Relative date" titled page is displayed
        And the user selects the "table" bound table field on the main page
        And the user selects the row 1 of the table field
        Then the value of the "Release date" labelled nested relative date field of the selected row in the table field is "5 years ago"
