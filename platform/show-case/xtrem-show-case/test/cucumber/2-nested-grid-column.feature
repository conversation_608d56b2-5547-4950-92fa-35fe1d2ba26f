Feature: 2 nested grid column
    # Tests filtering capabilities within nested grid columns, verifying that column-specific filters work correctly for data in nested grid structures

    @simple_filter
    Scenario: As an ATP / XTreeM user can filter a value in a nested grid column using a simple filter
        XT-86987
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/NestedGrid/eyJfaWQiOiI2In0="
        Then the "Field - NestedGrid" titled page is displayed
        When the user selects the "field" bound nested grid field on the main page
        #filter the main level
        And the user filters the "Id" labelled column in the nested grid field with value "127"
        And the user selects row with text "127" in column with header "Id" in the nested grid field
        Then the value of the "Id" labelled nested text field of the selected row in the nested grid field is "127"
        # And takes a screenshot

        And the user expands the selected row of the nested grid field
        #filter the sub level
        And the user filters the "Total Product Quantity" labelled column in the nested grid field with value "97"
        And the user selects row with text "97" in column with header "Total Product Quantity" in the nested grid field
        Then the value of the "Total Product Quantity" labelled nested text field of the selected row in the nested grid field is "97"
        # And takes a screenshot

        And the user expands the selected row of the nested grid field
        #filter the sub level / on change
        And the user filters the "Total Product Quantity" labelled column in the nested grid field with value "44"
        And the user selects row with text "44" in column with header "Total Product Quantity" in the nested grid field
        Then the value of the "Total Product Quantity" labelled nested text field of the selected row in the nested grid field is "44"
    # And takes a screenshot


    @simple_filter
    Scenario: As an ATP / XTreeM user I can filter  a nested grid column using both value and filter type in a simple filter
        XT-86987
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/NestedGrid/eyJfaWQiOiI2In0="
        Then the "Field - NestedGrid" titled page is displayed
        When the user selects the "field" bound nested grid field on the main page

        #filter the main level
        And the user filters the "Id" labelled column in the nested grid field with filter "Equal to" and value "127"
        And the user selects row with text "127" in column with header "Id" in the nested grid field
        Then the value of the "Id" labelled nested text field of the selected row in the nested grid field is "127"
        # And takes a screenshot

        And the user expands the selected row of the nested grid field
        #filter the sub level
        And the user filters the "Total Product Quantity" labelled column in the nested grid field with filter "Equal to" and value "97"
        And the user selects row with text "97" in column with header "Total Product Quantity" in the nested grid field
        Then the value of the "Id" labelled nested text field of the selected row in the nested grid field is "371"
        # And takes a screenshot


        #filter the sub level / on change
        And the user filters the "Total Product Quantity" labelled column in the nested grid field with filter "Equal to" and value "44"
        And the user selects row with text "44" in column with header "Total Product Quantity" in the nested grid field
        Then the value of the "Id" labelled nested text field of the selected row in the nested grid field is "305"
    # And takes a screenshot


    @mini_filter @custom_reference_filter
    Scenario: As an ATP / XTreeM user I can filter a value in the nested grid column using a mini filer or custom reference filter
        XT-86987
        #Using simple filter step definitions
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/NestedGrid/eyJfaWQiOiI2In0="
        When the user selects the "field" bound nested grid field on the main page

        And the user selects row with text "127" in column with header "Id" in the nested grid field
        And the user writes "In store" in the "Order Type" labelled nested dropdown-list field of the selected row in the nested grid field
        And the value of the "Order Type" labelled nested dropdown-list field of the selected row in the nested grid field is "In store"

        #filter the main level / mini filter
        And the user filters the "Order Type" labelled column in the nested grid field with value "In store"
        And the user selects row with text "In store" in column with header "Order Type" in the nested grid field
        Then the value of the "Id" labelled nested dropdown-list field of the selected row in the nested grid field is "127"
        # And takes a screenshot

        And the user selects row with text "127" in column with header "Id" in the nested grid field
        And the user expands the selected row of the nested grid field

        #filter the sub level / custom-reference filter
        And the user selects row with text "371" in column with header "Id" in the nested grid field
        And the user expands the selected row of the nested grid field

        And the user filters the "Product" labelled column in the nested grid field with value "Pasta - Lasagne, Fresh"
        And the user selects row with text "853" in column with header "Id" in the nested grid field
        Then the value of the "Product" labelled nested text field of the selected row in the nested grid field is "Pasta - Lasagne, Fresh"
    # And takes a screenshot

    @mini_filter @custom_reference_filter
    Scenario: As an ATP / XTreeM user I can filter a value in a nested grid column using a mini filer or custom reference filter
        XT-86987
        #Using advanced filter step definitions
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/NestedGrid/eyJfaWQiOiI2In0="
        When the user selects the "field" bound nested grid field on the main page

        And the user selects row with text "127" in column with header "Id" in the nested grid field
        And the user writes "In store" in the "Order Type" labelled nested dropdown-list field of the selected row in the nested grid field

        #filter the main level / mini filter
        And the user opens the filter of the "Order Type" labelled column in the nested grid field
        And the user searches "In st" in the filter of the nested grid field
        And the user ticks the item with text "In store" in the filter of the nested grid field
        And the user closes the filter of the "Order Type" labelled column in the nested grid field
        Then the value of the "Order Type" labelled nested dropdown-list field of the selected row in the nested grid field is "In store"
        # And takes a screenshot

        And the user selects row with text "127" in column with header "Id" in the nested grid field
        And the user expands the selected row of the nested grid field

        #filter the sub level / custom-reference filter
        And the user selects row with text "371" in column with header "Id" in the nested grid field
        And the user expands the selected row of the nested grid field

        And the user opens the filter of the "Product" labelled column in the nested grid field
        And the user searches "Pasta" in the filter of the nested grid field
        And the user ticks the item with text "Pasta - Lasagne, Fresh" in the filter of the nested grid field
        And the user closes the filter of the "Product" labelled column in the nested grid field
    # And takes a screenshot


    @simple_filter
    Scenario Outline: As an ATP / XTreeM I can use relative date filter in nested grid field
        XT-86987
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/NestedGrid/eyJfaWQiOiI2In0="
        When the user selects the "field" bound nested grid field on the main page

        And the user selects row with text "127" in column with header "Id" in the nested grid field
        And the value of the "Order Date" labelled nested date field of the selected row in the nested grid field is "01/01/2021"

        #filter the main level
        And the user filters the "Order Date" labelled column in the nested grid field with filter type <Filter>
        # And takes a screenshot

        #filter the main level / on change
        And the user filters the "Order Date" labelled column in the nested grid field with filter "Equal to" and value "01/01/2021"

        And the user selects row with text "127" in column with header "Id" in the nested grid field
        And the user expands the selected row of the nested grid field

        #filter the sub level
        And the user filters the "Purchase Date" labelled column in the nested grid field with filter type <Filter>

        # And takes a screenshot
        Examples:
            | Filter          |
            | "Previous year" |
