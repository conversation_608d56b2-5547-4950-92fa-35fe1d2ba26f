Feature: 3-2 Sidebar
    # Tests sidebar functionality for data entry and editing, focusing on form field interactions within the sidebar panel
    ## Disabled due this Feature is using the old way to add records from the sidebar instead of the new way and is failing due to that
    ## This needs a refactor to use the new way to add records from the sidebar
    @date_sidebar
    Scenario: Interact with the sidebar - Desktop mode - Bind
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Table"
        And the user selects the "field" bound table field on the main page
        And the user adds a new table row to the table field using the sidebar

        #Text field
        And the user selects the "product" bound text field on the sidebar
        When the user writes "Apple Imac 20 inch" in the text field
        Then the value of the text field is "Apple Imac 20 inch"

        #Numeric field
        And the user selects the "qty" bound numeric field on the sidebar
        When the user writes "10" in the numeric field
        And the user selects the "listPrice" bound numeric field on the sidebar
        When the user writes "1000" in the numeric field
        And the user selects the "tax" bound numeric field on the sidebar
        When the user writes "120" in the numeric field

        And the user selects the "qty" bound numeric field on the sidebar
        When the user clicks in the numeric field
        Then the value of the numeric field is "10"
        And the user selects the "listPrice" bound numeric field on the sidebar
        Then the value of the numeric field is "1,000.00"
        And the user selects the "tax" bound numeric field on the sidebar
        Then the value of the numeric field is "120.00"
        And the user selects the "netPrice" bound numeric field on the sidebar
        Then the value of the numeric field is "1,000.00"

        #Reference field
        And the user selects the "provider" bound reference field on the sidebar
        When the user writes "Amazon" in the reference field
        And the user selects "Amazon" in the reference field
        Then the value of the reference field is "Amazon"

        #Date field
        And the user selects the "releaseDate" bound date field on the sidebar
        When the user writes "12/20/2021" in the date field
        Then the value of the date field is "12/20/2021"
        And the user selects the "endingDate" bound date field on the sidebar
        When the user writes "12/31/2021" in the date field
        Then the value of the date field is "12/31/2021"

    Scenario: Interact with the sidebar - Desktop mode - Label
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Table"
        And the user selects the "field" bound table field on the main page
        And the user adds a new table row to the table field using the sidebar

        #Text field
        And the user selects the "Product" labelled text field on the sidebar
        When the user writes "Apple Imac 27 inch" in the text field
        Then the value of the text field is "Apple Imac 27 inch"

        #Numeric field
        And the user selects the "Quantity" labelled numeric field on the sidebar
        When the user writes "10" in the numeric field
        And the user selects the "List price" labelled numeric field on the sidebar
        When the user writes "2000" in the numeric field
        And the user selects the "Tax" labelled numeric field on the sidebar
        When the user writes "400" in the numeric field

        And the user selects the "Quantity" labelled numeric field on the sidebar
        When the user clicks in the numeric field
        Then the value of the numeric field is "10"
        And the user selects the "List price" labelled numeric field on the sidebar
        Then the value of the numeric field is "2,000.00"
        And the user selects the "Tax" labelled numeric field on the sidebar

        #Reference field
        And the user selects the "Provider" labelled reference field on the sidebar
        When the user writes "Amazon" in the reference field
        And the user selects "Amazon" in the reference field
        Then the value of the reference field is "Amazon"

        #Date field
        And the user selects the "Date" labelled date field on the sidebar
        When the user writes "12/20/2021" in the date field
        Then the value of the date field is "12/20/2021"
        And the user selects the "Ending date" labelled date field on the sidebar
        When the user writes "12/31/2021" in the date field
        Then the value of the date field is "12/31/2021"


    Scenario: As a user I want click the action button in the sidebar header
        XT-94978
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TableWithSidebar/eyJfaWQiOiIxIn0="
        Then the "Field - Table - With sidebar" titled page is displayed
        When the user selects the "field" bound table field on the main page
        And the user selects the row with text "Anisette - Mcguiness" in the "product" bound column header of the table field
        And the user clicks the "Edit on sidebar" dropdown action of the selected row of the table field
        Then the "Anisette - Mcguiness" titled sidebar is displayed
        When the user clicks the "Lorem" labelled action button in the sidebar header
        And a toast containing text "You clicked Lorem 106 Anisette - Mcguiness" is displayed


    Scenario: As a user I want click the more action button in the sidebar header
        XT-94978
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TableWithSidebar/eyJfaWQiOiIxIn0="
        Then the "Field - Table - With sidebar" titled page is displayed
        When the user selects the "field" bound table field on the main page
        And the user selects the row with text "Anisette - Mcguiness" in the "product" bound column header of the table field
        And the user clicks the "Edit on sidebar" dropdown action of the selected row of the table field
        Then the "Anisette - Mcguiness" titled sidebar is displayed
        When the user clicks the "Do stuff" labelled more actions button in the sidebar header
        And a toast containing text "It did stuff! 106 Anisette - Mcguiness" is displayed

    Scenario: As a user I want to be able to set date-time or no start/end date time for the date-time range field in sidebar
        XT-78128
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TableWithSidebar"
        When the user selects the "field" bound table field on the main page

        # create a new record with value for the date-time range field
        And the user adds a new table row to the table field using the sidebar
        And the user selects the "Product" labelled text field on the sidebar
        And the user writes "A date-time range test" in the text field
        And the user selects the "Manufacterd within" labelled date-time-range field on the sidebar
        And the user selects the "December" month of the end date-time-range field
        And the user selects the "2027" year of the end date-time-range field
        And the user selects the "31" day in the end date-time-range field
        And the user writes "07:30" in time field of the end date-time-range field
        And the user selects the "January" month of the start date-time-range field
        And the user selects the "2024" year of the start date-time-range field
        And the user selects the "7" day in the start date-time-range field
        And the user writes "08:45" in time field of the start date-time-range field
        # And the user clicks the "PM" toggle button of the start date-time-range field
        And the user clicks the "Apply" button of the dialog on the sidebar
        And the user clicks the "Save" labelled business action button on the main page

        # check the created record value for the date-time range field
        And the user selects the row with text "A date-time range test" in the "Product" labelled column header of the table field
        And the user selects the "Manufacterd within" labelled nested date-time-range field of the selected row in the table field
        Then the value of the start and end nested date-time-range field of the selected row in the table field is "01/07/2024 08:45 AM - 12/31/2027 07:30 AM"
        When the user clicks the "Edit on sidebar" dropdown action of the selected row of the table field
        And the user selects the "Manufacterd within" labelled date-time-range field on the sidebar
        Then the value of the start date-time-range field is "01/07/2024 - 08:45 AM"
        And the value of the end date-time-range field is "12/31/2027 - 07:30 AM"

        # edit and set date-time range field to no start and end date and check the setting is saved
        When the user ticks the "No end date" checkbox of the end date-time-range field
        And the user ticks the "No start date" checkbox of the start date-time-range field
        And the user clicks the "Apply" button of the dialog on the sidebar
        And the user clicks the "Save" labelled business action button on the main page
        And the user selects the row with text "A date-time range test" in the "Product" labelled column header of the table field
        And the user selects the "Manufacterd within" labelled nested date-time-range field of the selected row in the table field
        Then the value of the start and end nested date-time-range field of the selected row in the table field is ""
        When the user clicks the "Edit on sidebar" dropdown action of the selected row of the table field
        And the user selects the "Manufacterd within" labelled date-time-range field on the sidebar
        Then the value of the start date-time-range field is ""
        And the value of the end date-time-range field is ""

        # clean up the created record
        Then the user clicks the "remove" labelled more actions button in the sidebar header
        And the user clicks the "Save" labelled business action button on the main page
