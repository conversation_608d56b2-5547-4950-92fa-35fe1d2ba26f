Feature: 5 Y dashboard widget bar chart edition
    # Tests the creation and editing of bar chart widgets in dashboards, verifying configuration options and visualization settings

    @ClearDashboards
    Scenario: As I user I want to create and update a bar chart widget
        Given the user opens the application on a desktop
        Then the dashboard page is displayed
        And the "Create a dashboard to get started." subtitled empty dashboard is displayed
        When the user clicks the create button on the dashboard
        Then the dashboard creation dialog is displayed
        When the user selects the template with title "Showcase dashboard" in the dashboard creation dialog
        And the user clicks the "next" button in the dashboard creation dialog
        Then the "Showcase dashboard" titled dashboard in the dashboard editor is displayed

        # Widget selection step
        When the user clicks the "createAWidget" labelled button in the dashboard editor navigation panel
        Then the "New widget" titled widget editor dialog is displayed
        And the value of the step title of the widget editor dialog is "1. Select a widget to get started"
        And the "cancel" button in the widget editor dialog is enabled
        And the "next" button in the widget editor dialog is disabled
        When the user writes "My demo category" in the "basic-category" dropdown field in the widget editor dialog
        And the user presses Enter
        And the user writes "Show Case Product" in the "basic-node" dropdown field in the widget editor dialog
        And the user presses Enter
        And the user writes "Bar Chart Widget" in the "basic-title" text field in the widget editor dialog
        And the user selects the "LINE_CHART" widget card in the widget editor dialog
        Then the "LINE_CHART" widget card in the widget editor dialog is selected
        When the user selects the "BAR_CHART" widget card in the widget editor dialog
        Then the "LINE_CHART" widget card in the widget editor dialog is unselected
        And the "BAR_CHART" widget card in the widget editor dialog is selected
        And the "next" button in the widget editor dialog is enabled

        # widget data step
        When the user clicks the "next" button in the widget editor dialog
        Then the value of the step title of the widget editor dialog is "2. Select the data to add to your widget"
        And the "previous" button in the widget editor dialog is enabled
        And the "cancel" button in the widget editor dialog is enabled
        And the "next" button in the widget editor dialog is disabled
        When the user searches for "Price" in the widget editor dialog
        And the user presses Enter
        Then the "Net price" tree-view element in the widget editor dialog is displayed
        And the "List price" tree-view element in the widget editor dialog is displayed
        And the "Amount" tree-view element in the widget editor dialog is hidden
        When the user clears the search field in the widget editor dialog
        Then the "Amount" tree-view element in the widget editor dialog is displayed
        When the user selects the "Net price" tree-view element in the widget editor dialog
        Then the "next" button in the widget editor dialog is disabled
        When the user selects the "List price" tree-view element in the widget editor dialog
        Then the "next" button in the widget editor dialog is enabled
        When the user selects the "Ending date" tree-view element in the widget editor dialog
        Then the "next" button in the widget editor dialog is enabled
        When the user unselects the "List price" tree-view element in the widget editor dialog
        And the user unselects the "Net price" tree-view element in the widget editor dialog
        Then the "next" button in the widget editor dialog is disabled
        When the user selects the "List price" tree-view element in the widget editor dialog
        Then the "next" button in the widget editor dialog is enabled
        When the user selects the "Net price" tree-view element in the widget editor dialog
        And the user selects the "Amount" tree-view element in the widget editor dialog
        And the user selects the "Fixed quantity" tree-view element in the widget editor dialog
        And the user selects the "Qty" tree-view element in the widget editor dialog
        And the user selects the "Discount" tree-view element in the widget editor dialog
        And the user selects the "Category" tree-view element in the widget editor dialog

        #widget content step
        And the user clicks the "next" button in the widget editor dialog
        Then the value of the step title of the widget editor dialog is "3. Add your content"
        And the "horizontal-axis" dropdown field in the widget editor dialog is enabled
        And the "group-by" dropdown field in the widget editor dialog is hidden
        When the user writes "Ending Date" in the "horizontal-axis" dropdown field in the widget editor dialog
        And the user presses Enter
        Then the "group-by" dropdown field in the widget editor dialog is enabled
        When the user writes "Month" in the "group-by" dropdown field in the widget editor dialog
        And the user presses Enter
        And the user clicks the "Add value" table button in the widget editor dialog
        Then the "content-property" dropdown field of row "1" in the widget editor dialog is enabled
        When the user writes "Net Price" in the "content-property" dropdown field of row "1" in the widget editor dialog
        And the user presses Enter
        Then the "content-label" text field of row "1" in the widget editor dialog is enabled
        And the "content-formatting" text field of row "1" in the widget editor dialog is enabled
        And the value of the "content-label" text field of row "1" in the widget editor dialog is "Net price"
        When the user writes "2" in the "content-formatting" text field of row "1" in the widget editor dialog
        Then the "content-divisor" text field of row "1" in the widget editor dialog is enabled
        And the user presses Enter
        Then the "grouping-method" dropdown field of row "1" in the widget editor dialog is enabled
        When the user writes "Distinct count" in the "grouping-method" dropdown field of row "1" in the widget editor dialog
        And the user presses Enter
        Then the "next" button in the widget editor dialog is enabled
        When the user clicks the "add" action button of row "1" in the widget editor dialog
        Then the "content-property" dropdown field of row "2" in the widget editor dialog is enabled
        When the user writes "List Price" in the "content-property" dropdown field of row "2" in the widget editor dialog
        Then the value of the "content-label" text field of row "2" in the widget editor dialog is "List price"
        When the user writes "1" in the "content-formatting" text field of row "2" in the widget editor dialog
        And the user presses Enter
        And the user writes "Distinct count" in the "grouping-method" dropdown field of row "2" in the widget editor dialog
        And the user presses Enter
        And the user clicks the "add" action button of row "2" in the widget editor dialog
        Then the "content-property" dropdown field of row "3" in the widget editor dialog is enabled
        When the user writes "Amount" in the "content-property" dropdown field of row "3" in the widget editor dialog
        Then the value of the "content-label" text field of row "3" in the widget editor dialog is "Amount"
        When the user writes "1" in the "content-formatting" text field of row "3" in the widget editor dialog
        And the user presses Enter
        And the user writes "Distinct Count" in the "grouping-method" dropdown field of row "3" in the widget editor dialog
        And the user presses Enter
        And the user clicks the "add" action button of row "3" in the widget editor dialog
        Then the "content-property" dropdown field of row "4" in the widget editor dialog is enabled
        When the user writes "Net Price" in the "content-property" dropdown field of row "4" in the widget editor dialog
        And the user presses Enter
        Then the value of the "content-label" text field of row "4" in the widget editor dialog is "Net price"
        And the user writes "Maximum" in the "grouping-method" dropdown field of row "4" in the widget editor dialog
        And the user presses Enter
        And the user clicks the "add" action button of row "4" in the widget editor dialog
        Then the "content-property" dropdown field of row "5" in the widget editor dialog is enabled
        When the user writes "Discount" in the "content-property" dropdown field of row "5" in the widget editor dialog
        And the user presses Enter
        Then the value of the "content-label" text field of row "5" in the widget editor dialog is "Discount"
        When the user writes "Distinct count" in the "grouping-method" dropdown field of row "5" in the widget editor dialog
        And the user presses Enter
        Then the "add" action button of row "5" in the widget editor dialog is hidden


        #widget filter step
        When the user clicks the "next" button in the widget editor dialog
        Then the value of the step title of the widget editor dialog is "4. Add your filters"
        When the user clicks the "Add filter" table button in the widget editor dialog
        Then the "filter-property" dropdown field of row "1" in the widget editor dialog is enabled
        And the "filter-type" text field of row "1" in the widget editor dialog is disabled
        And the "filter-value" text field of row "1" in the widget editor dialog is disabled
        When the user writes "Ending Date" in the "filter-property" dropdown field of row "1" in the widget editor dialog
        And the user presses Enter
        Then the "filter-type" text field of row "1" in the widget editor dialog is enabled
        When the user writes "Less than or equal to" in the "filter-type" text field of row "1" in the widget editor dialog
        And the user presses Enter
        Then the "filter-value" text field of row "1" in the widget editor dialog is enabled
        When the user writes "04/27/2023" in the "filter-value" date field of row "1" in the widget editor dialog

        And the user presses Enter
        And the user clicks the "add" action button of row "1" in the widget editor dialog
        And the user writes "Amount" in the "filter-property" dropdown field of row "2" in the widget editor dialog
        And the user presses Enter
        And the user writes "Less than" in the "filter-type" text field of row "2" in the widget editor dialog
        And the user presses Enter
        And the user writes "100" in the "filter-value" text field of row "2" in the widget editor dialog
        And the user presses Enter
        And the user clicks the "add" action button of row "2" in the widget editor dialog
        And the user writes "Category" in the "filter-property" dropdown field of row "3" in the widget editor dialog
        And the user presses Enter
        # Then the value of the "filter-type" text field of row "3" in the widget editor dialog is "Equals"
        And the user writes "Does not equal" in the "filter-type" text field of row "3" in the widget editor dialog
        And the user presses Enter
        And the user writes "Awful" in the "filter-value" text field of row "3" in the widget editor dialog
        And the user presses Enter
        And the user writes "Ok" in the "filter-value" text field of row "3" in the widget editor dialog
        And the user presses Enter
        And the user presses Tab

        #widget sorting step
        And the user clicks the "next" button in the widget editor dialog
        Then the value of the step title of the widget editor dialog is "5. Define sorting"
        When the user clicks the "Add a sort condition" table button in the widget editor dialog
        Then the "sorting-property" dropdown field of row "1" in the widget editor dialog is enabled
        And the "sorting-order" dropdown field of row "1" in the widget editor dialog is enabled
        When the user writes "Ending Date" in the "sorting-property" dropdown field of row "1" in the widget editor dialog
        And the user presses Enter
        And the user writes "Ascending" in the "sorting-order" dropdown field of row "1" in the widget editor dialog
        And the user presses Enter

        #widget layout step
        And the user clicks the "next" button in the widget editor dialog
        Then the value of the step title of the widget editor dialog is "6. Create your layout"
        And the "layout-vertical-axis-label" text field in the widget editor dialog is enabled
        And the "layout-horizontal-axis-label" text field in the widget editor dialog is enabled
        When the user writes "time" in the "layout-horizontal-axis-label" text field in the widget editor dialog
        And the user presses Enter
        And the user writes "quantities and price" in the "layout-vertical-axis-label" text field in the widget editor dialog
        And the user presses Enter
        And the user ticks the "See all" checkbox field in the widget editor dialog
        Then the "layout-title-seeAllAction" dropdown field in the widget editor dialog is enabled
        And the "layout-page-seeAllAction" dropdown field in the widget editor dialog is enabled
        And the "See all" preview button in the widget editor dialog is displayed
        When the user writes "ShowCase - Product" in the "layout-page-seeAllAction" dropdown field in the widget editor dialog
        And the user presses Enter
        And the user ticks the "Create" checkbox field in the widget editor dialog
        Then the "layout-title-createAction" dropdown field in the widget editor dialog is enabled
        And the "layout-page-createAction" dropdown field in the widget editor dialog is enabled
        And the "Create" preview button in the widget editor dialog is displayed
        When the user writes "ShowCase - Product" in the "layout-page-createAction" dropdown field in the widget editor dialog
        And the user presses Enter
        Then the "add" button in the widget editor dialog is enabled
        When the user clicks the "add" button in the widget editor dialog
        Then the "Bar Chart Widget" titled widget in the dashboard editor is displayed
        And the user waits 1 second
        And the "save" button in the dashboard editor footer is enabled
        When the user clicks the "save" button in the dashboard editor footer
        And the user dismisses all the toasts
        Then the "Showcase dashboard" titled dashboard is displayed
        And the "Bar Chart Widget" titled widget in the dashboard is displayed

        #editing the widget
        # 1. checking all values are persisted
        When the user clicks the "Edit" labelled CRUD button in the dashboard action menu
        Then the "Bar Chart Widget" titled widget in the dashboard editor is displayed
        When the user selects the "Bar Chart Widget" titled bar-chart widget field in the dashboard editor
        And the user clicks the "edit" more actions button in the header of the tile-indicator widget field
        Then the "Edit widget" titled widget editor dialog is displayed
        And the value of the step title of the widget editor dialog is "1. Select a widget to get started"
        And the value of the "basic-title" dropdown field in the widget editor dialog is "Bar Chart Widget"
        And the value of the "basic-category" dropdown field in the widget editor dialog is "My demo category"
        And the "BAR_CHART" widget card in the widget editor dialog is selected

        When the user clicks the "next" button in the widget editor dialog
        Then the value of the step title of the widget editor dialog is "2. Select the data to add to your widget"
        And the "Ending date" tree-view element in the widget editor dialog is checked
        And the "Amount" tree-view element in the widget editor dialog is checked
        And the "Discount" tree-view element in the widget editor dialog is checked
        And the "Fixed quantity" tree-view element in the widget editor dialog is checked
        And the "List price" tree-view element in the widget editor dialog is checked
        And the "Net price" tree-view element in the widget editor dialog is checked
        And the "Qty" tree-view element in the widget editor dialog is checked
        And the "Category" tree-view element in the widget editor dialog is checked

        When the user clicks the "next" button in the widget editor dialog
        Then the value of the step title of the widget editor dialog is "3. Add your content"
        And the value of the "horizontal-axis" dropdown field in the widget editor dialog is "Ending date"
        And the value of the "group-by" dropdown field in the widget editor dialog is "Month"
        And the value of the "content-property" dropdown field of row "1" in the widget editor dialog is "Net price"
        And the value of the "content-label" dropdown field of row "1" in the widget editor dialog is "Net price"
        And the value of the "content-formatting" dropdown field of row "1" in the widget editor dialog is "2"
        And the value of the "grouping-method" dropdown field of row "1" in the widget editor dialog is "Distinct count"

        And the value of the "content-property" dropdown field of row "2" in the widget editor dialog is "List price"
        And the value of the "content-label" dropdown field of row "2" in the widget editor dialog is "List price"
        And the value of the "content-formatting" dropdown field of row "2" in the widget editor dialog is "1"
        And the value of the "grouping-method" dropdown field of row "2" in the widget editor dialog is "Distinct count"

        And the value of the "content-property" dropdown field of row "3" in the widget editor dialog is "Amount"
        And the value of the "content-label" dropdown field of row "3" in the widget editor dialog is "Amount"
        And the value of the "content-formatting" dropdown field of row "3" in the widget editor dialog is "1"
        And the value of the "grouping-method" dropdown field of row "3" in the widget editor dialog is "Distinct count"

        And the value of the "content-property" dropdown field of row "4" in the widget editor dialog is "Net price"
        And the value of the "content-label" dropdown field of row "4" in the widget editor dialog is "Net price"
        And the value of the "grouping-method" dropdown field of row "4" in the widget editor dialog is "Maximum"

        And the value of the "content-property" dropdown field of row "5" in the widget editor dialog is "Discount"
        And the value of the "content-label" dropdown field of row "5" in the widget editor dialog is "Discount"
        And the value of the "grouping-method" dropdown field of row "5" in the widget editor dialog is "Distinct count"

        When the user clicks the "next" button in the widget editor dialog
        Then the value of the step title of the widget editor dialog is "4. Add your filters"
        And the value of the "filter-property" dropdown field of row "1" in the widget editor dialog is "Ending date"
        And the value of the "filter-type" dropdown field of row "1" in the widget editor dialog is "Less than or equal to"
        And the value of the "filter-value" dropdown field of row "1" in the widget editor dialog is "04/27/2023"

        And the value of the "filter-property" dropdown field of row "2" in the widget editor dialog is "Amount"
        And the value of the "filter-type" dropdown field of row "2" in the widget editor dialog is "Less than"
        And the value of the "filter-value" dropdown field of row "2" in the widget editor dialog is "100"

        And the value of the "filter-property" dropdown field of row "3" in the widget editor dialog is "Category"
        And the value of the "filter-type" dropdown field of row "3" in the widget editor dialog is "Does not equal"

        When the user clicks the "next" button in the widget editor dialog
        Then the value of the step title of the widget editor dialog is "5. Define sorting"
        And the value of the "sorting-property" dropdown field of row "1" in the widget editor dialog is "Ending date"
        And the value of the "sorting-order" dropdown field of row "1" in the widget editor dialog is "Ascending"
        And the user clicks the "next" button in the widget editor dialog
        # FJ 15 01 2024
        #        When the user presses Enter

        Then the value of the step title of the widget editor dialog is "6. Create your layout"
        And the value of the "layout-horizontal-axis-label" text field in the widget editor dialog is "time"
        And the value of the "layout-vertical-axis-label" text field in the widget editor dialog is "quantities and price"
        And the value of the "layout-max-num-allowed" text field in the widget editor dialog is "20"
        And the "See all" checkbox field in the widget editor dialog is checked
        And the value of the "layout-title-seeAllAction" text field in the widget editor dialog is "See all"
        # And the value of the "layout-page-seeAllAction" dropdown field in the widget editor dialog is "ShowCase - Product"
        And the "Create" checkbox field in the widget editor dialog is checked
        And the value of the "layout-title-createAction" text field in the widget editor dialog is "Create"
        # And the value of the "layout-page-createAction" dropdown field in the widget editor dialog is "ShowCase - Product"
        And the "update" button in the widget editor dialog is enabled

        # changing the values
        When the user clicks the "previous" button in the widget editor dialog
        Then the value of the step title of the widget editor dialog is "5. Define sorting"
        When the user clicks the "previous" button in the widget editor dialog
        Then the value of the step title of the widget editor dialog is "4. Add your filters"
        When the user clicks the "previous" button in the widget editor dialog
        Then the value of the step title of the widget editor dialog is "3. Add your content"
        When the user clicks the "previous" button in the widget editor dialog
        Then the value of the step title of the widget editor dialog is "2. Select the data to add to your widget"
        When the user clicks the "previous" button in the widget editor dialog
        Then the value of the step title of the widget editor dialog is "1. Select a widget to get started"

        # edit data
        When the user writes "Edited Bar Chart Widget" in the "basic-title" text field in the widget editor dialog
        And the user presses Enter
        And the user writes "My other demo category" in the "basic-category" dropdown field in the widget editor dialog
        And the user presses Enter
        And the user clicks the "next" button in the widget editor dialog
        Then the value of the step title of the widget editor dialog is "2. Select the data to add to your widget"
        When the user unselects the "Ending date" tree-view element in the widget editor dialog
        Then the "next" button in the widget editor dialog is enabled
        When the user selects the "Product" tree-view element in the widget editor dialog
        Then the "next" button in the widget editor dialog is enabled


        When the user clicks the "next" button in the widget editor dialog
        Then the value of the step title of the widget editor dialog is "3. Add your content"
        And the value of the "horizontal-axis" dropdown field in the widget editor dialog is ""
        And the "next" button in the widget editor dialog is disabled
        When the user writes "Product" in the "horizontal-axis" dropdown field in the widget editor dialog
        And the user presses Enter
        Then the "next" button in the widget editor dialog is enabled


        When the user clicks the "next" button in the widget editor dialog
        Then the value of the step title of the widget editor dialog is "4. Add your filters"
        When the user clicks the "add" action button of row "2" in the widget editor dialog
        And the user writes "Product" in the "filter-property" dropdown field of row "3" in the widget editor dialog
        And the user presses Enter
        And the user writes "Contains" in the "filter-type" dropdown field of row "3" in the widget editor dialog
        And the user presses Enter
        And the user writes "w" in the "filter-value" text field of row "3" in the widget editor dialog
        And the user presses Enter

        And the user clicks the "next" button in the widget editor dialog
        Then the value of the step title of the widget editor dialog is "5. Define sorting"
        And the "Add a sort condition" table button in the widget editor dialog is displayed
        When the user clicks the "Add a sort condition" table button in the widget editor dialog
        When the user writes "Product" in the "sorting-property" dropdown field of row "1" in the widget editor dialog
        And the user presses Enter
        And the user writes "Descending" in the "sorting-order" dropdown field of row "1" in the widget editor dialog
        And the user presses Enter

        And the user clicks the "next" button in the widget editor dialog
        Then the value of the step title of the widget editor dialog is "6. Create your layout"

        When the user clicks the "update" button in the widget editor dialog
        Then the "Edited Bar Chart Widget" titled widget in the dashboard editor is displayed
        And the user waits 1 second
        And the "save" button in the dashboard editor footer is enabled
        When the user clicks the "save" button in the dashboard editor footer
        And the user dismisses all the toasts
        Then the "Showcase dashboard" titled dashboard is displayed
        And the "Edited Bar Chart Widget" titled widget in the dashboard is displayed

        # edit widget type
        When the user clicks the "Edit" labelled CRUD button in the dashboard action menu
        Then the "Edited Bar Chart Widget" titled widget in the dashboard editor is displayed
        When the user selects the "Edited Bar Chart Widget" titled bar-chart widget field in the dashboard editor
        And the user clicks the "edit" more actions button in the header of the tile-indicator widget field
        Then the "Edit widget" titled widget editor dialog is displayed
        And the value of the step title of the widget editor dialog is "1. Select a widget to get started"
        When the user writes "Line Chart Widget" in the "basic-title" text field in the widget editor dialog
        And the user presses Enter
        And the user selects the "LINE_CHART" widget card in the widget editor dialog
        Then the "LINE_CHART" widget card in the widget editor dialog is selected
        And the "BAR_CHART" widget card in the widget editor dialog is unselected

        # edit content
        When the user clicks the "next" button in the widget editor dialog
        Then the value of the step title of the widget editor dialog is "2. Select the data to add to your widget"
        And the "Ending date" tree-view element in the widget editor dialog is unchecked
        And the "Amount" tree-view element in the widget editor dialog is unchecked
        And the "Discount" tree-view element in the widget editor dialog is unchecked
        And the "Fixed quantity" tree-view element in the widget editor dialog is unchecked
        And the "List price" tree-view element in the widget editor dialog is unchecked
        And the "Net price" tree-view element in the widget editor dialog is unchecked
        And the "Qty" tree-view element in the widget editor dialog is unchecked
        And the "next" button in the widget editor dialog is disabled
