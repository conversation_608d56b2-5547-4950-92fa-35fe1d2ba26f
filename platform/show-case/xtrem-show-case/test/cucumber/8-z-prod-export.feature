Feature: 8 Z prod export
    # Tests the data export functionality in production environments, verifying the ability to correctly export data in various formats from different views

    Feature Description

    Scenario: Export data
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-authorization/User"
        And the "Users" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        And the user ticks the main checkbox of the selected row in the table field
        And the user selects the row 2 of the table field
        And the user ticks the main checkbox of the selected row in the table field
        #And the user ticks the main checkbox of row 3 in the table field
        #And the user selects all rows of the table field
        And the user clicks the "Export" labelled bulk action button of the table field
        And the text in the header of the dialog is "Select export template"
        And the user clicks the "Confirm" labelled business action button on a modal
        And the text in the body of the dialog is "Perform this action on the selected items: 2"
        And the user clicks the "OK" button of the Confirm dialog
        And the user waits 50 seconds
        #And the user navigates to the following link: "@sage/xtrem-authorization/User"
        And the user clicks the notifications icon in the actions header
        And the user selects the notification card with title "The export with this template ended successfully: User."
        And the user clicks the "Download file" notification card body action
