Feature: 3-1 Parameters
# Tests parameterized testing capabilities, demonstrating how environment-specific parameters can be used across different test environments and user contexts

# tenantName=functional_test_atp_ci
# Scenario: Environment "https://cluster-ci.eu.dev-sagextrem.com/"
#     Given the user is logged into the system in desktop mode using the "param:loginUserName" user name and "param:loginPassword" password
#     Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/LocationMassCreation"
#     Then the "Location mass creation" titled page is displayed
#     And the user logs out of the system
#     Given the user is logged into the system in desktop mode using the "param:loginUserName2" user name and "param:loginPassword2" password
#     Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/LocationMassCreation"
#     Then the "Location mass creation" titled page is displayed
#     And the user logs out of the system

# Scenario: Environment "http://showcase.dev-sagextrem.com/"
#     Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/LocationMassCreation"
#     Then the "Location mass creation" titled page is displayed

# TARGET_URL="https://adc-latest.sageerpx3.com/handheld"
# endPointName1="X3PRD / X3RBTWRK"
# Scenario: Environment "https://adc-latest.sageerpx3.com/handheld"
#     Given the user is logged into the system in mobile mode using the "param:loginUserName" user name and "param:loginPassword" password
#     When the user selects the "param:endPointName1" endpoint
#     Then the "param:endPointName1" endpoint is selected
#     And the user logs out of the system
#     Given the user is logged into the system in mobile mode using the "param:loginUserName2" user name and "param:loginPassword2" password
#     And the user logs out of the system
