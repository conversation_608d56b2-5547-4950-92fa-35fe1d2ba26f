Feature: 3-1 Pull request
    # Tests basic integration points used in CI/CD pull request validation, focusing on core data creation and validation flows
    This feature is used to execute basic integration tests on pull requests

    Scenario: As a user I want to create a new product record in the database with basic control rules
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseProduct/$new"
        Then the "Product" titled page is displayed
        And the user selects the "Product" labelled text field on the main page
        When the user writes "Test Product" in the text field
        And the user selects the "Description" labelled text field on the main page
        When the user writes "Test Description" in the text field
        And the user selects the "Provider" labelled reference field on the main page
        When the user writes "Ama" in the reference field
        And the user selects "Amazon" in the reference field
        Then the value of the reference field is "Amazon"
        And the user selects the "Category (Dropdown)" labelled dropdown-list field on the main page
        And the user clicks in the dropdown-list field
        And the user selects "Great" in the dropdown-list field
        And the user clicks the "Save" labelled business action button on the main page
        And the user selects the "Quantity" labelled numeric field on the main page
        Then the value of the numeric field is "1"
        # The stock field behaves erratically on Azure : issue XT-41222 --> Not reproduced anymore. line reactivated
        And the user selects the "Stock" labelled numeric field on the main page
        Then the value of the numeric field is "1"
        And the user selects the "List Price" labelled numeric field on the main page
        Then the value of the numeric field is "0.00"


    Scenario: As a user I want my fields to be bound to the dataset
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseProvider/eyJfaWQiOjF9"
        Then the "Provider 1" titled page is displayed
        And the user selects the "ID" labelled text field on the main page
        Then the value of the text field is "1"
        And the user selects the "Integer" labelled numeric field on the main page
        Then the value of the numeric field is "1"
        And the user selects the "Decimal" labelled numeric field on the main page
        Then the value of the numeric field is "2.34"

    # Bug created: https://jira.sage.com/browse/XT-30321
    # Scenario: As a user I want to click in a specific column of a table
    #    Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Table"
    #    Then the "Field - Table" titled page is displayed
    #    When the user clicks on the "st" bound nested label field of row 4 of the "field" bound table field in the main page
    #    Then an info dialog appears on the main page

    Scenario: As a user I want get the total price of selected items of a table
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Table"
        Then the "Field - Table" titled page is displayed
        And the user selects the "field" bound table field on the main page
        And the user selects the row 8 of the table field
        When the user ticks the main checkbox of the selected row in the table field
        And the user selects the "tableSelectedTotal" bound numeric field on the main page
        Then the value of the numeric field is "56.35"

    Scenario: As a user I want to filter columns with different criteria
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Table"
        Then the "Field - Table" titled page is displayed
        And the user selects the "field" bound table field on the main page
        When the user filters the "product" bound column in the table field with value "Beef"
        And the user selects the row 1 of the table field
        Then the value of the "product" bound nested text field of the selected row in the table field is "Beef - Bresaola"

    Scenario: As a user I want load data to a transient table and test event listeners
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TransientTable"
        Then the "Field - Table (Transient)" titled page is displayed
        When the user clicks the "loadButton" bound button on the main page
        And the user selects the "field" bound table field on the main page
        And the user selects the row 2 of the table field
        And the value of the "product" bound nested text field of the selected row in the table field is "Veal Inside - Provimi"
        And the user ticks the main checkbox of the selected row in the table field
        And the user selects the "tableSelectedTotal" bound numeric field on the main page
        Then the value of the numeric field is "115.91"
        And the user selects the "field" bound table field on the main page
        And the user selects the row 2 of the table field
        When the user writes "1" in the "qty" bound nested numeric field of the selected row in the table field
        And the user selects the "tableSelectedTotal" bound numeric field on the main page
        Then the value of the numeric field is "60.49"
        And the user selects the "field" bound table field on the main page
        And the user selects the row 2 of the table field
        When the user writes "4" in the "qty" bound nested numeric field of the selected row in the table field
        And the user selects the "tableSelectedTotal" bound numeric field on the main page
        Then the value of the numeric field is "241.96"


    Scenario: As a user I want to filter a transient table
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TransientTable"
        Then the "Field - Table (Transient)" titled page is displayed
        When the user clicks the "loadButton" bound button on the main page
        And the user selects the "field" bound table field on the main page
        And the user selects the row 2 of the table field
        When the user writes "Test value" in the "product" bound nested text field of the selected row in the table field
        When the user filters the "product" bound column in the table field with value "t v"
        And the user selects the row 1 of the table field
        Then the value of the "product" bound nested text field of the selected row in the table field is "Test value"

    Scenario: As a user I want to check values inside reference lookup dialog on desktop using bind
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Reference"
        Then the "Field - Reference" titled page is displayed
        And the user selects the "field" bound reference field on the main page
        When the user clicks the lookup button of the reference field
        And the user selects the "field" bound table field on a modal
        And the user selects the row 1 of the table field
        Then the value of the "Product" labelled nested text field of the selected row in the table field is "Spinach - Baby"
        When the user clicks the "Product" labelled nested field of the selected row in the table field
        And the user selects the "field" bound reference field on the main page
        Then the value of the reference field is "Spinach - Baby"
