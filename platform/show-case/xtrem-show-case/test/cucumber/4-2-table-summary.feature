Feature: 4-2 Table summary
    # Tests table summary component, verifying the table summary selection, row selection and nested field value and controlling the table summary field state.

    Scenario: As an ATP XTreeM user I can interact with the summary table nested fields using label
        XT-55192
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TableSummary/eyJfaWQiOiIyIn0="
        Then the "Field - Table Summary" titled page is displayed
        And the user selects the "Summary Table Title" labelled summary table field on the main page
        And the user selects the row with text "Apple - Macintosh" in the "Product" labelled column header of the summary table field
        Then the value of the "Quantity" labelled nested numeric field of the selected row in the summary table field is "21"
        And the user stores the value of the "Provider" labelled nested reference field of the selected row in the summary table field with the key "[ENV_PROVIDER01]"

    Scenario: As an ATP XTreeM user I can interact with the summary table nested fields using bind
        XT-55192
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TableSummary/eyJfaWQiOiIyIn0="
        Then the "Field - Table Summary" titled page is displayed
        And the user selects the "field" bound summary table field on the main page
        And the user selects the row with text "Apple - Macintosh" in the "product" bound column header of the summary table field
        Then the value of the "qty" bound nested numeric field of the selected row in the summary table field is "21"
        And the user selects the row with text "Beef - Tenderloin - Aa" in the "product" bound column header of the summary table field
        Then the value of the "category" bound nested label field of the selected row in the summary table field is "High"
        Then the value of the "provider" bound nested reference field of the selected row in the summary table field is "Amazon"
        And the user stores the value of the "provider" labelled nested reference field of the selected row in the summary table field with the key "[ENV_PROVIDER01]"


    Scenario Outline: <Device> - As and ATP XTreeM user I can verify the summary table field title
        XT-55191
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/TableSummary/eyJfaWQiOiIyIn0="
        Then the "Field - Table Summary" titled page is displayed

        Given the user selects the "Title" labelled text field on the main page
        When the user writes "My Title" in the text field
        And the user presses Tab

        Given the user selects the "field" bound summary table field on the main page
        Then the title of the summary table field is displayed
        Then the title of the summary table field is "My Title"

        Given the user selects the "Is title hidden" labelled checkbox field on the main page
        When the user ticks the checkbox field

        Given the user selects the "field" bound summary table field on the main page
        Then the title of the summary table field is hidden
        Examples:
            | Device  |
            | desktop |


    Scenario Outline: <Device> - As and ATP XTreeM user I can verify the summary table field helper text
        XT-55191
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/TableSummary/eyJfaWQiOiIyIn0="
        Then the "Field - Table Summary" titled page is displayed

        Given the user selects the "Helper text" labelled text field on the main page
        When the user writes "My Helper Text" in the text field
        And the user presses Tab

        Given the user selects the "field" bound summary table field on the main page
        Then the helper text of the summary table field is displayed
        Then the helper text of the summary table field is "My Helper Text"

        Given the user selects the "Is helper text hidden" labelled checkbox field on the main page
        When the user ticks the checkbox field

        Given the user selects the "field" bound summary table field on the main page
        Then the helper text of the summary table field is hidden
        Examples:
            | Device  |
            | desktop |


    Scenario Outline: <Device> - As and ATP XTreeM user I can verify if the summary table field is displayed or hidden
        XT-55191
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/TableSummary/eyJfaWQiOiIyIn0="
        Then the "Field - Table Summary" titled page is displayed

        Given the user selects the "field" bound summary table field on the main page
        Then the "field" bound summary table field on the main page is displayed

        Given the user selects the "Is hidden" labelled checkbox field on the main page
        When the user ticks the checkbox field
        Then the "field" bound summary table field on the main page is hidden
        Examples:
            | Device  |
            | desktop |


    Scenario Outline:<Device> - As and ATP XTreeM user I can verify if the summary table field is enabled or disabled
        XT-55191
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/TableSummary/eyJfaWQiOiIyIn0="
        Then the "Field - Table Summary" titled page is displayed

        Given the user selects the "field" bound summary table field on the main page
        Then the summary table field is enabled

        Given the user selects the "Is disabled" labelled checkbox field on the main page
        When the user ticks the checkbox field

        Given the user selects the "field" bound summary table field on the main page
        Then the summary table field is disabled
        Examples:
            | Device  |
            | desktop |


    Scenario: As an ATP XTreeM user I can verify state and visibility of the summary tables when all fields are displayed and enabled
        XT-55191
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TableSummary/eyJfaWQiOiIyIn0="
        Then the "Field - Table Summary" titled page is displayed
        And the "Summary Table Title" labelled summary table field on the main page is displayed
        And the user selects the "Summary Table Title" labelled summary table field on the main page
        And the summary table field is enabled
        And the title of the summary table field is displayed
        And the title of the summary table field is "Summary Table Title"
        And the helper text of the summary table field is displayed
        And the helper text of the summary table field is "Helper text here."

    Scenario: As an ATP XTreeM user I can verify state and visibility of the summary tables when all fields are hidden and disabled
        XT-55191
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TableSummary/eyJfaWQiOiIyIn0="
        Then the "Field - Table Summary" titled page is displayed
        And the user selects the "Summary Table Title" labelled summary table field on the main page

        Given the user selects the "Is title hidden" labelled checkbox field on the main page
        When the user ticks the checkbox field
        Then the title of the summary table field is hidden

        Given the user selects the "Is helper text hidden" labelled checkbox field on the main page
        When the user ticks the checkbox field
        Then the helper text of the summary table field is hidden

        Given the user selects the "Is disabled" labelled checkbox field on the main page
        When the user ticks the checkbox field
        Then the summary table field is disabled

        Given the user selects the "Is hidden" labelled checkbox field on the main page
        When the user ticks the checkbox field
        Then the "Summary Table Title" labelled summary table field on the main page is hidden
