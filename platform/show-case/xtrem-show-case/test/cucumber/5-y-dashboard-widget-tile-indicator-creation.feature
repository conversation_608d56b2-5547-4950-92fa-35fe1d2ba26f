Feature: 5 Y dashboard widget tile indicator creation
    # Tests the creation process of indicator tile widgets, verifying configuration options for numerical metrics with visual indicators

    @ClearDashboards
    Scenario: As a user I want to create a widget using the create button in the Dashboard Editor choosing the Indicator Tile format
        Given the user opens the application on a desktop
        And the dashboard page is displayed
        Then the "Create a dashboard to get started." subtitled empty dashboard is displayed
        When the user clicks the create button on the dashboard
        Then the dashboard creation dialog is displayed
        When the user selects the template with title "Blank template" in the dashboard creation dialog
        And the user clicks the "next" button in the dashboard creation dialog
        Then the "New dashboard" titled dashboard in the dashboard editor is displayed

        # Widget selection step
        When the user clicks the "createAWidget" labelled button in the dashboard editor navigation panel
        Then the "New widget" titled widget editor dialog is displayed
        And the value of the step title of the widget editor dialog is "1. Select a widget to get started"
        And the "cancel" button in the widget editor dialog is enabled
        And the "next" button in the widget editor dialog is disabled
        When the user writes "My demo category" in the "basic-category" dropdown field in the widget editor dialog
        And the user presses Enter
        And the user writes "Show Case Provider" in the "basic-node" dropdown field in the widget editor dialog
        And the user presses Enter
        And the user writes "Indicator Tile Widget" in the "basic-title" text field in the widget editor dialog
        And the user selects the "INDICATOR_TILE" widget card in the widget editor dialog
        Then the "INDICATOR_TILE" widget card in the widget editor dialog is selected
        And the "next" button in the widget editor dialog is enabled
        When the user clicks the "next" button in the widget editor dialog
        Then the value of the step title of the widget editor dialog is "2. Select the data to add to your widget"
        And the value of the step subtitle of the widget editor dialog is "Select a field for grouping and fields to use as filters."
        And the "previous" button in the widget editor dialog is enabled
        And the "next" button in the widget editor dialog is disabled
        When the user clicks the "previous" button in the widget editor dialog
        Then the value of the step title of the widget editor dialog is "1. Select a widget to get started"
        And the "previous" button in the widget editor dialog is hidden
        And the "cancel" button in the widget editor dialog is enabled
        And the "next" button in the widget editor dialog is enabled
        When the user selects the "TABLE" widget card in the widget editor dialog
        Then the "TABLE" widget card in the widget editor dialog is selected
        And the "INDICATOR_TILE" widget card in the widget editor dialog is unselected
        When the user selects the "INDICATOR_TILE" widget card in the widget editor dialog
        Then the "INDICATOR_TILE" widget card in the widget editor dialog is selected

        # Data selection step
        When the user clicks the "next" button in the widget editor dialog
        Then the value of the step title of the widget editor dialog is "2. Select the data to add to your widget"
        And the "next" button in the widget editor dialog is disabled
        When the user searches for "Text field" in the widget editor dialog
        Then the "Text field" tree-view element in the widget editor dialog is displayed
        And the "Integer field" tree-view element in the widget editor dialog is hidden
        When the user selects the "Text field" tree-view element in the widget editor dialog
        Then the "next" button in the widget editor dialog is enabled
        When the user clears the search field in the widget editor dialog
        And the user selects the "Integer field" tree-view element in the widget editor dialog
        And the user selects the "Date field" tree-view element in the widget editor dialog


        # Content selection step
        And the user clicks the "next" button in the widget editor dialog
        Then the value of the step title of the widget editor dialog is "3. Add your content"
        Then the "grouping-method" dropdown field in the widget editor dialog is disabled
        And the "next" button in the widget editor dialog is disabled
        When the user writes "Integer field" in the "grouping-property" dropdown field in the widget editor dialog
        Then the "grouping-method" dropdown field in the widget editor dialog is enabled
        When the user writes "Maximum" in the "grouping-method" dropdown field in the widget editor dialog
        And the user presses Enter
        Then the "content-divisor" text field in the widget editor dialog is enabled
        And the value of the "content-divisor" text field in the widget editor dialog is "1"
        And the user presses Enter

        # Filter selection step
        Then the "next" button in the widget editor dialog is enabled
        When the user clicks the "next" button in the widget editor dialog
        Then the value of the step title of the widget editor dialog is "4. Add your filters"
        And the "Add filter" table button in the widget editor dialog is displayed
        When the user clicks the "Add filter" table button in the widget editor dialog
        Then the "filter-property" dropdown field of row "1" in the widget editor dialog is enabled
        When the user writes "Text field" in the "filter-property" dropdown field of row "1" in the widget editor dialog
        And the user presses Enter
        And the user writes "Contains" in the "filter-type" dropdown field of row "1" in the widget editor dialog
        And the user presses Enter
        And the user writes "a" in the "filter-value" text field of row "1" in the widget editor dialog
        And the user clicks the "add" action button of row "1" in the widget editor dialog
        Then the "filter-property" dropdown field of row "2" in the widget editor dialog is enabled
        When the user writes "Integer field" in the "filter-property" dropdown field of row "2" in the widget editor dialog
        And the user presses Enter
        Then the "filter-type" dropdown field of row "2" in the widget editor dialog is enabled
        And the user writes "Between" in the "filter-type" dropdown field of row "2" in the widget editor dialog
        Then the "filter-value-min" text field of row "2" in the widget editor dialog is enabled
        And the "filter-value-max" text field of row "2" in the widget editor dialog is enabled
        When the user writes "3" in the "filter-value-min" text field of row "2" in the widget editor dialog
        Then the "Invalid integer" validation error of row "2" in the widget editor dialog is displayed
        And the user writes "1" in the "filter-value-max" text field of row "2" in the widget editor dialog
        And the "Invalid range" validation error of row "2" in the widget editor dialog is displayed
        When the user writes "10" in the "filter-value-max" text field of row "2" in the widget editor dialog
        Then the "Invalid range" validation error of row "2" in the widget editor dialog is hidden
        When the user clicks the "add" action button of row "2" in the widget editor dialog
        Then the "filter-property" dropdown field of row "3" in the widget editor dialog is enabled
        When the user writes "Date field" in the "filter-property" dropdown field of row "3" in the widget editor dialog
        Then the "filter-type" dropdown field of row "3" in the widget editor dialog is enabled
        When the user writes "Between" in the "filter-type" dropdown field of row "3" in the widget editor dialog
        And the user presses Enter
        Then the "start" date field of row "3" in the widget editor dialog is displayed
        And the "end" date field of row "3" in the widget editor dialog is displayed
        When the user writes "02/16/2022" in the "start" date field of row "3" in the widget editor dialog
        And the user presses Tab
        Then the "Invalid date" validation error of row "3" in the widget editor dialog is displayed
        When the user writes "02/17/2023" in the "end" date field of row "3" in the widget editor dialog
        And the user presses Tab
        Then the "Invalid date" validation error of row "3" in the widget editor dialog is hidden
        When the user clicks the "remove" action button of row "3" in the widget editor dialog
        Then the "filter-property" dropdown field of row "3" in the widget editor dialog is hidden

        # Layout selection step
        When the user clicks the "next" button in the widget editor dialog
        Then the value of the step title of the widget editor dialog is "5. Create your layout"
        And the "layout-icon" dropdown field in the widget editor dialog is displayed
        And the "layout-decimal-digits" text field in the widget editor dialog is enabled
        And the "layout-subtitle" text field in the widget editor dialog is enabled
        When the user writes "Accounting" in the "layout-icon" dropdown field in the widget editor dialog
        And the user writes "2" in the "layout-decimal-digits" text field in the widget editor dialog
        And the user writes "new" in the "layout-subtitle" text field in the widget editor dialog
        When the user ticks the "See all" checkbox field in the widget editor dialog
        Then the "See all" preview button in the widget editor dialog is displayed
        When the user unticks the "See all" checkbox field in the widget editor dialog
        Then the "See all" preview button in the widget editor dialog is hidden
        When the user ticks the "Create" checkbox field in the widget editor dialog
        Then the "Create" preview button in the widget editor dialog is displayed
        When the user unticks the "Create" checkbox field in the widget editor dialog
        Then the "Create" preview button in the widget editor dialog is hidden
        When the user ticks the "See all" checkbox field in the widget editor dialog
        And the user writes "Showcase - Provider" in the "layout-page-seeAllAction" dropdown field in the widget editor dialog
        And the user presses Enter
        And the user ticks the "Create" checkbox field in the widget editor dialog
        And the user writes "Showcase - Provider" in the "layout-page-createAction" dropdown field in the widget editor dialog
        And the user presses Enter
        Then the "add" button in the widget editor dialog is enabled

        # saving the created widget
        When the user clicks the "add" button in the widget editor dialog
        Then the "New dashboard" titled dashboard in the dashboard editor is displayed
        And the "Indicator Tile Widget" titled widget in the dashboard editor is displayed
        And the user waits 1 second
        When the user clicks the "save" button in the dashboard editor footer
        Then a toast containing text "Dashboard saved." is displayed
        When the user dismisses all the toasts
        Then the "New dashboard" titled dashboard is displayed


        # navigating to widget-linked pages
        And the "Indicator Tile Widget" titled widget in the dashboard is displayed
        When the user selects the "Indicator Tile Widget" titled tile-indicator widget field in the dashboard
        Then the "See all" button of the table widget field is enabled
        And the "Create" button of the table widget field is enabled
        When the user clicks the "See all" button of the table widget field
        Then the "Providers" titled page is displayed
        When the user opens the application on a desktop
        Then the "New dashboard" titled dashboard is displayed
        And the "Indicator Tile Widget" titled widget in the dashboard is displayed
        When the user selects the "Indicator Tile Widget" titled tile-indicator widget field in the dashboard
        And the user clicks the "Create" button of the table widget field
        Then the "Provider" titled page is displayed

    @ClearDashboards
    Scenario: As a user I want to create an indicator widget using divisor value
        Given the user opens the application on a desktop
        And the dashboard page is displayed
        Then the "Create a dashboard to get started." subtitled empty dashboard is displayed
        When the user clicks the create button on the dashboard
        Then the dashboard creation dialog is displayed
        When the user selects the template with title "Blank template" in the dashboard creation dialog
        And the user clicks the "next" button in the dashboard creation dialog
        Then the "New dashboard" titled dashboard in the dashboard editor is displayed

        # Widget selection step
        When the user clicks the "createAWidget" labelled button in the dashboard editor navigation panel
        Then the "New widget" titled widget editor dialog is displayed
        And the value of the step title of the widget editor dialog is "1. Select a widget to get started"
        And the "cancel" button in the widget editor dialog is enabled
        And the "next" button in the widget editor dialog is disabled
        When the user writes "My demo category" in the "basic-category" dropdown field in the widget editor dialog
        And the user presses Enter
        And the user writes "Show Case Provider" in the "basic-node" dropdown field in the widget editor dialog
        And the user presses Enter
        And the user writes "Indicator Tile Widget" in the "basic-title" text field in the widget editor dialog
        And the user selects the "INDICATOR_TILE" widget card in the widget editor dialog
        Then the "INDICATOR_TILE" widget card in the widget editor dialog is selected
        And the "next" button in the widget editor dialog is enabled
        When the user clicks the "next" button in the widget editor dialog
        Then the value of the step title of the widget editor dialog is "2. Select the data to add to your widget"
        And the "previous" button in the widget editor dialog is enabled
        When the user clicks the "previous" button in the widget editor dialog
        Then the value of the step title of the widget editor dialog is "1. Select a widget to get started"
        And the "previous" button in the widget editor dialog is hidden
        And the "cancel" button in the widget editor dialog is enabled
        And the "next" button in the widget editor dialog is enabled
        When the user selects the "TABLE" widget card in the widget editor dialog
        Then the "TABLE" widget card in the widget editor dialog is selected
        And the "INDICATOR_TILE" widget card in the widget editor dialog is unselected
        When the user selects the "INDICATOR_TILE" widget card in the widget editor dialog
        Then the "INDICATOR_TILE" widget card in the widget editor dialog is selected

        # Data selection step
        When the user clicks the "next" button in the widget editor dialog
        Then the value of the step title of the widget editor dialog is "2. Select the data to add to your widget"
        And the "next" button in the widget editor dialog is disabled
        When the user searches for "Text field" in the widget editor dialog
        Then the "Text field" tree-view element in the widget editor dialog is displayed
        And the "Integer field" tree-view element in the widget editor dialog is hidden
        When the user selects the "Text field" tree-view element in the widget editor dialog
        Then the "next" button in the widget editor dialog is enabled
        When the user clears the search field in the widget editor dialog
        And the user selects the "Integer field" tree-view element in the widget editor dialog
        And the user selects the "Date field" tree-view element in the widget editor dialog


        # Content selection step
        And the user clicks the "next" button in the widget editor dialog
        Then the value of the step title of the widget editor dialog is "3. Add your content"
        Then the "grouping-method" dropdown field in the widget editor dialog is disabled
        And the "next" button in the widget editor dialog is disabled
        When the user writes "Integer field" in the "grouping-property" dropdown field in the widget editor dialog
        Then the "grouping-method" dropdown field in the widget editor dialog is enabled
        When the user writes "Maximum" in the "grouping-method" dropdown field in the widget editor dialog
        And the user presses Enter
        Then the "content-divisor" text field in the widget editor dialog is enabled
        When the user writes "10" in the "content-divisor" text field in the widget editor dialog
        And the value of the "content-divisor" text field in the widget editor dialog is "10"
        And the user presses Enter

        # Filter selection step
        Then the "next" button in the widget editor dialog is enabled
        When the user clicks the "next" button in the widget editor dialog
        Then the value of the step title of the widget editor dialog is "4. Add your filters"
        And the "Add filter" table button in the widget editor dialog is displayed
        When the user clicks the "Add filter" table button in the widget editor dialog
        Then the "filter-property" dropdown field of row "1" in the widget editor dialog is enabled
        When the user writes "Text field" in the "filter-property" dropdown field of row "1" in the widget editor dialog
        And the user presses Enter
        And the user writes "Contains" in the "filter-type" dropdown field of row "1" in the widget editor dialog
        And the user presses Enter
        And the user writes "a" in the "filter-value" text field of row "1" in the widget editor dialog
        And the user clicks the "add" action button of row "1" in the widget editor dialog
        Then the "filter-property" dropdown field of row "2" in the widget editor dialog is enabled
        When the user writes "Integer field" in the "filter-property" dropdown field of row "2" in the widget editor dialog
        And the user presses Enter
        Then the "filter-type" dropdown field of row "2" in the widget editor dialog is enabled
        And the user writes "Between" in the "filter-type" dropdown field of row "2" in the widget editor dialog
        Then the "filter-value-min" text field of row "2" in the widget editor dialog is enabled
        And the "filter-value-max" text field of row "2" in the widget editor dialog is enabled
        When the user writes "3" in the "filter-value-min" text field of row "2" in the widget editor dialog
        Then the "Invalid integer" validation error of row "2" in the widget editor dialog is displayed
        And the user writes "1" in the "filter-value-max" text field of row "2" in the widget editor dialog
        And the "Invalid range" validation error of row "2" in the widget editor dialog is displayed
        When the user writes "10" in the "filter-value-max" text field of row "2" in the widget editor dialog
        Then the "Invalid range" validation error of row "2" in the widget editor dialog is hidden
        When the user clicks the "add" action button of row "2" in the widget editor dialog
        Then the "filter-property" dropdown field of row "3" in the widget editor dialog is enabled
        When the user writes "Date field" in the "filter-property" dropdown field of row "3" in the widget editor dialog
        Then the "filter-type" dropdown field of row "3" in the widget editor dialog is enabled
        When the user writes "Between" in the "filter-type" dropdown field of row "3" in the widget editor dialog
        And the user presses Enter
        Then the "start" date field of row "3" in the widget editor dialog is displayed
        And the "end" date field of row "3" in the widget editor dialog is displayed
        When the user writes "02/16/2022" in the "start" date field of row "3" in the widget editor dialog
        And the user presses Tab
        Then the "Invalid date" validation error of row "3" in the widget editor dialog is displayed
        When the user writes "02/17/2023" in the "end" date field of row "3" in the widget editor dialog
        And the user presses Tab
        Then the "Invalid date" validation error of row "3" in the widget editor dialog is hidden
        When the user clicks the "remove" action button of row "3" in the widget editor dialog
        Then the "filter-property" dropdown field of row "3" in the widget editor dialog is hidden

        # Layout selection step
        When the user clicks the "next" button in the widget editor dialog
        Then the value of the step title of the widget editor dialog is "5. Create your layout"
        And the "layout-icon" dropdown field in the widget editor dialog is displayed
        And the "layout-decimal-digits" text field in the widget editor dialog is enabled
        And the "layout-subtitle" text field in the widget editor dialog is enabled
        When the user writes "Accounting" in the "layout-icon" dropdown field in the widget editor dialog
        And the user writes "2" in the "layout-decimal-digits" text field in the widget editor dialog
        And the user writes "new" in the "layout-subtitle" text field in the widget editor dialog
        When the user ticks the "See all" checkbox field in the widget editor dialog
        Then the "See all" preview button in the widget editor dialog is displayed
        When the user unticks the "See all" checkbox field in the widget editor dialog
        Then the "See all" preview button in the widget editor dialog is hidden
        When the user ticks the "Create" checkbox field in the widget editor dialog
        Then the "Create" preview button in the widget editor dialog is displayed
        When the user unticks the "Create" checkbox field in the widget editor dialog
        Then the "Create" preview button in the widget editor dialog is hidden
        When the user ticks the "See all" checkbox field in the widget editor dialog
        And the user writes "Showcase - Provider" in the "layout-page-seeAllAction" dropdown field in the widget editor dialog
        And the user presses Enter
        And the user ticks the "Create" checkbox field in the widget editor dialog
        And the user writes "Showcase - Provider" in the "layout-page-createAction" dropdown field in the widget editor dialog
        And the user presses Enter
        Then the "add" button in the widget editor dialog is enabled

        # saving the created widget
        When the user clicks the "add" button in the widget editor dialog
        Then the "New dashboard" titled dashboard in the dashboard editor is displayed
        And the "Indicator Tile Widget" titled widget in the dashboard editor is displayed
        And the user selects the "Indicator Tile Widget" titled tile-indicator widget field in the dashboard editor
        Then the value of the tile-indicator widget field is "0.40"
        And the user waits 1 second
        When the user clicks the "save" button in the dashboard editor footer
        Then a toast containing text "Dashboard saved." is displayed
        When the user dismisses all the toasts
        Then the "New dashboard" titled dashboard is displayed
