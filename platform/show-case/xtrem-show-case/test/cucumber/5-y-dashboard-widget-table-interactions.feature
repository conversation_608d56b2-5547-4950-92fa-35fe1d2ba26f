Feature: 5 Y dashboard widget table interactions
    # Tests user interactions with table widgets in dashboards, including view switching (card/table), sorting, filtering, and row selection

    @ClearDashboards
    Scenario: As an ATP XTreeM user I can switch to card or table view on the dashboard (editor) table widget
        XT-33282

        Given the user opens the application on a desktop
        #Create the dashboard
        And the dashboard page is displayed
        Then the "Create a dashboard to get started." subtitled empty dashboard is displayed
        When the user clicks the create button on the dashboard

        #Select showcase dashboard
        When the user selects the template with title "Showcase dashboard" in the dashboard creation dialog
        Then the template with title "Showcase dashboard" in the dashboard creation dialog is selected
        When the user clicks the "Next" button in the dashboard creation dialog

        #Verify the widgets in the Dashboard editor
        Then the "Showcase dashboard" titled dashboard in the dashboard editor is displayed
        And the user clicks the "cancel" button in the dashboard editor footer
        Then the "Showcase dashboard" titled dashboard is displayed

        #Table widget swith view in the dashboard
        When the user selects the "Users" titled table widget field in the dashboard
        And the user clicks the "Switch to table view" toggle button in the header of the table widget field
        And the user clicks the "Switch to card view" toggle button in the header of the table widget field

        #Switch to dashboard editor
        When the user clicks the "edit" labelled CRUD button in the dashboard action menu
        Then the "Showcase dashboard" titled dashboard in the dashboard editor is displayed
        And the user dismisses all the toasts

        #Table widget swith view in the dashboard editor
        And the user selects the "Users" titled table widget field in the dashboard editor
        And the user clicks the "Switch to table view" toggle button in the header of the table widget field
        And the user clicks the "Switch to card view" toggle button in the header of the table widget field

        #cancel dashboard editor
        When the user clicks the "cancel" button in the dashboard editor footer

    @ClearDashboards
    Scenario: As an ATP XTreeM user I can interact with the table widget in card view
        XT-33283

        Given the user opens the application on a HD desktop
        #Create the dashboard
        And the dashboard page is displayed
        Then the "Create a dashboard to get started." subtitled empty dashboard is displayed
        When the user clicks the create button on the dashboard

        #Select showcase dashboard
        When the user selects the template with title "Showcase dashboard" in the dashboard creation dialog
        Then the template with title "Showcase dashboard" in the dashboard creation dialog is selected
        When the user clicks the "Next" button in the dashboard creation dialog

        #Verify the widgets in the Dashboard editor
        Then the "Showcase dashboard" titled dashboard in the dashboard editor is displayed
        And the user clicks the "cancel" button in the dashboard editor footer
        Then the "Showcase dashboard" titled dashboard is displayed

        #Verify the value of the of the table widget in card view
        And the user selects the "Users" titled table widget field in the dashboard
        And the user clicks the "Switch to card view" toggle button in the header of the table widget field
        And the user selects the card 2 of the table widget field
        Then the value of the row 1 on the left of the card of the table widget field is "Acme"
        Then the value of the row 1 on the right of the card of the table widget field is "Support"
        Then the value of the row 2 on the left of the card of the table widget field is "<EMAIL>"
        Then the value of the row 2 on the right of the card of the table widget field is "system"
        And the user selects the card 5 of the table widget field
        Then the value of the row 1 on the left of the card of the table widget field is "adrienne"
        Then the value of the row 1 on the right of the card of the table widget field is "authier"
        Then the value of the row 2 on the left of the card of the table widget field is "<EMAIL>"
        Then the value of the row 2 on the right of the card of the table widget field is "application"

        #Select / unselect all the cards of the table widget
        And the user selects the "Users" titled table widget field in the dashboard
        And the user waits 1 second
        And the user clicks the "Switch to card view" toggle button in the header of the table widget field
        And the user ticks all the cards of the table widget field
        And the user waits 1 second
        And the user unticks all the cards of the table widget field

        #Select / unselect a card and use the action to the table widget
        And the user selects the "Users" titled table widget field in the dashboard
        And the user clicks the "Switch to card view" toggle button in the header of the table widget field
        And the user selects the card 2 of the table widget field
        And the user ticks the card of the table widget field
        And the user clicks the "Do something" button of the table widget field
        And a toast with text "Well you cannot do much with these users" is displayed
        And the user unticks the card of the table widget field

        #click the link in the card of the table widget
        And the user selects the "Users" titled table widget field in the dashboard
        And the user clicks the "Switch to card view" toggle button in the header of the table widget field
        And the user selects the card 3 of the table widget field
        And the user selects the card 4 of the table widget field
        And the user selects the card 5 of the table widget field
        And the user waits 1 second
        Then the value of the row 1 on the left of the card of the table widget field is "adrienne"
        And the user clicks the link of the row 2 on the left of the card of the table widget field
        Then the "User adrienne authier" titled page is displayed
        And the user selects the "First name" labelled text field on the main page
        Then the value of the text field is "adrienne"
        And the user selects the "Last name" labelled text field on the main page
        Then the value of the text field is "authier"

        #Verify the number of records in card view on the table widget
        Given the user opens the application on a desktop
        Then the "Showcase dashboard" titled dashboard is displayed
        # Flacky - bug XT-60322
        When the user selects the "Users" titled table widget field in the dashboard
        And the user clicks the "Switch to card view" toggle button in the header of the table widget field
        Then the number of records in the table widget fields is "20 +"
        When the user scrolls to the last card of the table widget field
        And the user scrolls to the first card of the table widget field

    @ClearDashboards
    Scenario: As an ATP XTreeM user I can interact with table widget in table view
        XT-33291
        Given the user opens the application on a desktop
        #Create the dashboard
        And the dashboard page is displayed
        Then the "Create a dashboard to get started." subtitled empty dashboard is displayed
        When the user clicks the create button on the dashboard

        #Select showcase dashboard
        When the user selects the template with title "Showcase dashboard" in the dashboard creation dialog
        Then the template with title "Showcase dashboard" in the dashboard creation dialog is selected
        When the user clicks the "Next" button in the dashboard creation dialog

        #Verify the widgets in the Dashboard editor
        Then the "Showcase dashboard" titled dashboard in the dashboard editor is displayed
        And the user clicks the "cancel" button in the dashboard editor footer
        Then the "Showcase dashboard" titled dashboard is displayed

        #Verify the value of the of the table widget in table view
        And the user selects the "Users" titled table widget field in the dashboard
        And the user clicks the "Switch to table view" toggle button in the header of the table widget field
        And the user selects the row with text "adrienne" and column header "First name" of the table widget field
        And the value of the cell with column header "User type" of the row of the table widget field is "application"
        And the user selects the row with text "aghate" and column header "First name" of the table widget field
        And the value of the cell with column header "Last name" of the row of the table widget field is "vauloup"

        #Select / unselect all the row of the table widget
        And the user selects the "Users" titled table widget field in the dashboard
        And the user waits 1 second
        And the user clicks the "Switch to table view" toggle button in the header of the table widget field
        And the user ticks all the rows of the table widget field
        And the user unticks all the rows of the table widget field

        #Select / unselect a row and use the action to the table widget
        And the user selects the "Users" titled table widget field in the dashboard
        And the user clicks the "Switch to table view" toggle button in the header of the table widget field
        And the user selects the row with text "aghate" and column header "First name" of the table widget field
        And the user ticks the row of the table widget field
        And the user clicks the "Do something" button of the table widget field
        And a toast with text "Well you cannot do much with these users" is displayed
        And the user unticks the row of the table widget field

        #Click the link in the row of the table widget
        And the user selects the "Users" titled table widget field in the dashboard
        And the user clicks the "Switch to table view" toggle button in the header of the table widget field
        And the user selects the row with text "adrienne" and column header "First name" of the table widget field
        And the user clicks the link of the cell with column header "Email" of the row of the table widget field
        Then the "User adrienne authier" titled page is displayed
        And the user selects the "First name" labelled text field on the main page
        Then the value of the text field is "adrienne"
        And the user selects the "Last name" labelled text field on the main page
        Then the value of the text field is "authier"


        # Verify the number of records in table view on the table widget
        Given the user opens the application on a desktop
        Then the "Showcase dashboard" titled dashboard is displayed
        When the user selects the "Users" titled table widget field in the dashboard
        And the user clicks the "Switch to table view" toggle button in the header of the table widget field
        Then the number of records in the table widget fields is "20 +"
        When the user scrolls to the last row of the table widget field
        And the user scrolls to the first row of the table widget field

    # Scenario: As an ATP XTreeM user I can count and scroll to the last record in the table widget in table view
    #     Given the user opens the application on a desktop
    #     #Create the dashboard
    #     And the dashboard page is displayed
    #     Then the "Create a dashboard to get started." subtitled empty dashboard is displayed
    #     When the user clicks the create button on the dashboard

    #     #Select showcase dashboard
    #     When the user selects the template with title "Showcase dashboard" in the dashboard creation dialog
    #     Then the template with title "Showcase dashboard" in the dashboard creation dialog is selected
    #     When the user clicks the "Next" button in the dashboard creation dialog

    #     Then the "Showcase dashboard" titled dashboard in the dashboard editor is displayed

    #     When the user clicks the "createAWidget" labelled button in the dashboard editor navigation panel
    #     Then the "New widget" titled widget editor dialog is displayed
    #     And the value of the step title of the widget editor dialog is "1. Select a widget to get started"
    #     And the user writes "List of Country" in the "basic-title" text field in the widget editor dialog
    #     When the user writes "My demo category" in the "basic-category" dropdown field in the widget editor dialog
    #     And the user presses Tab
    #     And the user writes "Show case country" in the "basic-node" dropdown field in the widget editor dialog
    #     And the user presses Tab
    #     And the user selects the "TABLE" widget card in the widget editor dialog
    #     And the user clicks the "next" button in the widget editor dialog

    #     # Data selection step
    #     Then the value of the step title of the widget editor dialog is "2. Select the data to add to your widget"
    #     And the user selects the "Code" tree-view element in the widget editor dialog
    #     And the user selects the "Name" tree-view element in the widget editor dialog
    #     And the user clicks the "next" button in the widget editor dialog

    #     # Content selection step
    #     Then the value of the step title of the widget editor dialog is "3. Add your content"
    #     Then the "Add column" table button in the widget editor dialog is displayed
    #     When the user clicks the "Add column" table button in the widget editor dialog
    #     When the user writes "Code" in the "content-property" dropdown field of row "1" in the widget editor dialog
    #     And the user presses Tab
    #     When the user clicks the "add" action button of row "1" in the widget editor dialog
    #     And the user writes "Name" in the "content-property" dropdown field of row "2" in the widget editor dialog
    #     And the user presses Tab
    #     And the user clicks the "next" button in the widget editor dialog

    #     # Filter selection step
    #     Then the value of the step title of the widget editor dialog is "4. Add your filters"
    #     And the user clicks the "next" button in the widget editor dialog

    #     # Sorting selection dialog
    #     Then the value of the step title of the widget editor dialog is "5. Define sorting"
    #     And the user clicks the "next" button in the widget editor dialog

    #     # Layout selection step
    #     Then the value of the step title of the widget editor dialog is "6. Create your layout"

    #     # saving the created widget
    #     When the user clicks the "add" button in the widget editor dialog
    #     Then the "Showcase dashboard" titled dashboard in the dashboard editor is displayed
    #     And the "List of Country" titled widget in the dashboard editor is displayed
    #     And the user waits 1 second
    #     When the user clicks the "Save" button in the dashboard editor footer
    #     Then a toast containing text "Dashboard saved." is displayed
    #     When the user dismisses all the toasts
    #     And the user clicks the "Cancel" button in the dashboard editor footer
    #     Then the "Showcase dashboard" titled dashboard is displayed

    #     #Verify the number of records in table view on the table widget
    #     Given the user opens the application on a desktop
    #     Then the "Showcase dashboard" titled dashboard is displayed
    #     When the user selects the "List of Country" titled table widget field in the dashboard
    #     Then the number of records in the table widget fields is "20 +"
    #     When the user scrolls to the last row of the table widget field
    #     And the user waits 10 seconds
    #     Then the number of records in the table widget fields is "240"
    #     And the user scrolls to the first row of the table widget field

    #     #Delete the dahboard
    #     Given the user opens the application on a desktop
    #     When the user clicks the "Delete" labelled CRUD button in the dashboard action menu
    #     Then a warn dialog appears on the main page
    #     And the user waits 1 second
    #     When the user clicks the "OK" button of the Confirm dialog on the main page
    #     Then a toast containing text "Dashboard deleted." is displayed
    #     Then the "Create a dashboard to get started." subtitled empty dashboard is displayed

    @ClearDashboards
    Scenario: As a user I want use table widget row actions
        Given the user opens the application on a HD desktop
        And the dashboard page is displayed
        Then the "Create a dashboard to get started." subtitled empty dashboard is displayed

        # step 1: create a dashboard with a table widget that has row actions
        When the user clicks the create button on the dashboard
        Then the dashboard creation dialog is displayed
        When the user selects the template with title "Showcase dashboard" in the dashboard creation dialog
        And the user clicks the "next" button in the dashboard creation dialog
        Then the "Showcase dashboard" titled dashboard in the dashboard editor is displayed
        When the user clicks the "cancel" button in the dashboard editor footer
        Then the "Showcase dashboard" titled dashboard is displayed

        # step 2: use row dropdown action on table widget card mode and check result of the action
        When the user selects the "Users with total count" titled table widget field in the dashboard
        And the user selects the card 1 of the table widget field
        And the user clicks the "Open" dropdown action of the selected card of the table widget field
        Then the "User Acme Support Readonly" titled page is displayed

        # step 3: use row dropdown action on table widget table mode and check result of the action
        When the user opens the application on a HD desktop
        And the dashboard page is displayed
        When the user selects the "Users with total count" titled table widget field in the dashboard
        And the user clicks the "Switch to table view" toggle button in the header of the table widget field
        And the user selects the row with text "Support Readonly" and column header "Last name" of the table widget field
        And the user clicks the "Open" dropdown action of the selected row of the table widget field
        Then the "User Acme Support Readonly" titled page is displayed

    @ClearDashboardsBefore
    @ClearDashboards
    Scenario: As a user, I want to execute mutations on a table widget

        Given the user opens the application on a desktop
        And the dashboard page is displayed
        Then the "Create a dashboard to get started." subtitled empty dashboard is displayed
        When the user clicks the create button on the dashboard
        Then the dashboard creation dialog is displayed
        And the dashboard creation dialog description is "Select a template to get started or build your own dashboard. You can customize any dashboard by adding or removing widgets."
        And the "Blank template" template in the dashboard creation dialog is displayed
        And the "next" button in the dashboard creation dialog is disabled
        And the "cancel" button in the dashboard creation dialog is enabled
        When the user selects the template with title "Blank template" in the dashboard creation dialog
        Then the template with title "Blank template" in the dashboard creation dialog is selected
        And the "next" button in the dashboard creation dialog is enabled
        When the user clicks the "next" button in the dashboard creation dialog
        Then the "New dashboard" titled dashboard in the dashboard editor is displayed
        And the "Add a widget to customize your dashboard." subtitled blank dashboard is displayed
        When the user searches for "Invoices" in the navigation panel
        And the user clicks the Add button of the "Invoices" titled widget card in the dashboard editor navigation panel
        And the user selects the "Invoices" titled table widget field in the dashboard editor
        And the user increases the widget field by 250,250 pixels
        And the user clicks the "Save" button in the dashboard editor footer
        Then the "New dashboard" titled dashboard is displayed
        When the user selects the "Invoices" titled table widget field in the dashboard
        And the user selects the card 1 of the table widget field
        And the user clicks the "Tag" dropdown action of the selected card of the table widget field
        Then a success toast containing text "Custom mutation executed successfully" is displayed
        When the user dismisses all the toasts
        And the user waits 2 seconds
        And the user selects the "Invoices" titled table widget field in the dashboard
        And the user clicks the "Switch to table view" toggle button in the header of the table widget field
        And the user selects the row with text "SCINV000002" and column header "Invoice Number" of the table widget field
        And the user clicks the "Tag" dropdown action of the selected row of the table widget field
        And the user waits 1 second
        Then a info toast containing text "Custom mutation executed successfully" is displayed

    @ClearDashboardsBefore
    @ClearDashboards
    Scenario: As a user, when I access a record through a tunnel I want the record's label to be displayed in the header
        XT-87203
        # prepare a record from where to access the tunnel
        Given the user opens the application on a desktop
        When the user clicks the create button on the dashboard
        And the user selects the template with title "Blank template" in the dashboard creation dialog
        And the user clicks the "next" button in the dashboard creation dialog
        Then the "New dashboard" titled dashboard in the dashboard editor is displayed
        When the user clicks the Add button of the "Invoice lines" titled widget card in the dashboard editor navigation panel
        Then the "Invoice lines" titled widget in the dashboard editor is displayed
        When the user clicks the "Save" button in the dashboard editor footer
        Then the "New dashboard" titled dashboard is displayed

        # access the tunnel
        When the user selects the "Invoice lines" titled table widget field in the dashboard
        And the user selects the row with text "Tarts Assorted" and column header "Product" of the table widget field
        And the user clicks the link of the cell with column header "Product" of the row of the table widget field
        Then an info dialog appears on a full width modal
        When the user waits 2 seconds

        # check the record pill label is displayed in the header
        Then the text in the header pill label of the dialog is "Good" on a full width modal
