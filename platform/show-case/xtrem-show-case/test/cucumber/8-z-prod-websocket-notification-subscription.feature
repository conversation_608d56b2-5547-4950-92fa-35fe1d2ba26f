Feature: 8-z-prod-websocket-notification-subscription
    # Tests the websocket notification subscription functionality in production environments, verifying the node create, update and delete events

    Scenario: As an ATP / XTreeM user, I can verify that a page that subscribes to server side notifications events will log the events
        XT-90215
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Subscription"
        Then the "Subscription" titled page is displayed
        When the user opens a new tab and navigates to the following link: "@sage/xtrem-show-case/ShowCaseCountry"
        Then the "Countries" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel
        And the user selects the "name" bound text field on the main page
        And the user clicks in the text field
        And the user writes "create subscription check" in the text field
        And the user selects the "code" bound text field on the main page
        And the user clicks in the text field
        And the user writes "XO" in the text field
        And the user clicks the "Save" labelled business action button on the main page
        And the user selects the "name" bound text field on the main page
        And the user clicks in the text field
        And the user writes "update subscription check" in the text field
        And the user blurs the text field
        And the user clicks the "Save" labelled business action button on the main page
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        And the user switches to the first opened tab
        Then the "Subscription" titled page is displayed
        When the user selects the "textArea" bound text area field on the main page
        Then the value of the text area field contains "ShowCaseCountry/create"
        And the value of the text area field contains "ShowCaseCountry/update"
        And the value of the text area field contains "ShowCaseCountry/delete"
