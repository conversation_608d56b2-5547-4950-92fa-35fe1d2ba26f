Feature: 4-2 Table validation
    # Tests table field validation functionality, control the display of validation errors message or tooltip when the data table or table row contain errors.

    Sc<PERSON>rio: As an ATP XTreeM user I can control the errors of the table field
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TableValidation/eyJfaWQiOiIyIn0="
        Then the "Field - Table Validations" titled page is displayed

        #Controlling the table / column / row  don't contain any error
        And the user selects the "field" bound table field on the main page
        Then the table has "0" error
        Then the "Ordered Quantity" labelled column in the table field contains no error
        And the user selects the row 12 of the table field
        Then the "Ordered Quantity" labelled nested numeric field of the selected row in the table field contains no error
        And the user selects the row with text "Bread - Ciabatta Buns" in the "Product" labelled column header of the table field
        Then the "Ordered Quantity" labelled nested numeric field of the selected row in the table field contains no error
        # And takes a screenshot

        And the user selects the "Min Quantity" labelled numeric field on the main page
        And the user writes "1" in the numeric field
        And the user clicks the "Save" labelled business action button on the main page

        #Validation error returned
        Then a validation error message is displayed containing text
            """
            Validation errors
            You have 11 errors in the following grid: Table title.
            """

        # And takes a screenshot
        And the user dismisses the validation error message


        #Controling number of errors and the error message of the table field
        And the user selects the "field" bound table field on the main page
        When the user hovers the validation error icon of the table field
        Then the table has "11" errors
        Then the table contains errors
            """
            You have 11 errors in the following grid: Table title.
            Id: 276 - Ordered Quantity: Enter a value greater than 1 in the  field.
            Id: 282 - Ordered Quantity: Enter a value greater than 1 in the  field.
            """
        # And takes a screenshot
        And the user closes the global validation error panel of the table field

        #Controlling the column contains errors
        Then the "Ordered Quantity" labelled column in the table field contains errors


        #Controling the error message at line level selecting the row by rowID
        When the user selects the row 12 of the table field
        Then the "Ordered Quantity" labelled nested numeric field of the selected row in the table field contains errors
        When the user clicks the main validation error icon of the selected row in the table field
        # And takes a screenshot
        Then the validation error tooltip containing text "Ordered Quantity Enter a value greater than 1 in the  field" in the table field is displayed

        #Controling the error message at line level selecting the row dynamically
        And the user selects the row with text "Bread - Ciabatta Buns" in the "Product" labelled column header of the table field
        Then the "Ordered Quantity" labelled nested numeric field of the selected row in the table field contains errors
        Then the user clicks the main validation error icon of the selected row in the table field
        # And takes a screenshot
        Then the validation error tooltip containing text "Ordered Quantity Enter a value greater than 1 in the  field" in the table field is displayed
