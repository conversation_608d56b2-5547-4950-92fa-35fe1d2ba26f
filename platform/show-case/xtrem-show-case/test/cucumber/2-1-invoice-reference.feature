Feature: 2-1 Invoice reference
    # Tests the reference field component with various quantifier operators for filtering records, ensuring correct display and selection of related invoice data
    Test _atLeast, _atMost, _every and _none quantifiers

    Scenario: As a user I want to see records in the reference field without any quatifier operator in the filter
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseInvoiceReference"
        And the user selects the "field" bound reference field on the main page
        When the user clicks the lookup button of the reference field
        Then the dialog title is "Invoices"
        And the user selects the "field" bound table field on a modal
        Then the "customer__name" bound column in the table field is displayed
        And the user waits 1 second
        When the user opens the filter of the "customer__name" bound column in the table field
        And the user searches "<PERSON>; <PERSON><PERSON><PERSON> and <PERSON>" in the filter of the table field
        And the user ticks the item with text "<PERSON>; <PERSON><PERSON><PERSON> and <PERSON>" in the filter of the table field
        And the user closes the filter of the "customer__name" bound column in the table field
        And  the user selects the row 1 of the table field
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "04/22/2020"
        And the user selects the row 2 of the table field
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "04/17/2020"
        And the user selects the row 3 of the table field
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "02/22/2020"
        And the user selects the row 4 of the table field
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "09/08/2019"

    Scenario: As a user I want to filter the reference using an _atLeast quantifier
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseInvoiceReference"
        And the user selects the "shouldFilter" bound switch field on the main page
        And the user clicks in the switch field
        And the user selects the "numberOfCases" bound numeric field on the main page
        And the user writes "2" in the numeric field
        And the user selects the "quantityOfProducts" bound numeric field on the main page
        And the user writes "11" in the numeric field
        And the user selects the "greaterThanProductQuantity" bound switch field on the main page
        And the user clicks in the switch field
        And the user waits 1 second
        And the user clicks the "applyFilter" bound button on the main page
        And the user selects the "field" bound reference field on the main page
        When the user clicks the lookup button of the reference field
        Then the dialog title is "Invoices"
        And the user selects the "field" bound table field on a modal
        Then the "customer__name" bound column in the table field is displayed
        And the user waits 1 second
        When the user opens the filter of the "customer__name" bound column in the table field
        And the user searches "Adams; Okuneva and Ebert" in the filter of the table field
        And the user ticks the item with text "Adams; Okuneva and Ebert" in the filter of the table field
        And the user closes the filter of the "customer__name" bound column in the table field
        And  the user selects the row 1 of the table field
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "04/17/2020"
        And the user selects the row 2 of the table field
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "09/08/2019"

    #Test 1 - XT-35650
    Scenario: As a user I want to filter the reference lookup dialog in mobile using an _atLeast quantifier
        Given the user opens the application on a mobile using the following link: "@sage/xtrem-show-case/ShowCaseInvoiceReference"
        And the user selects the "shouldFilter" bound switch field on the main page
        And the user clicks in the switch field
        And the user selects the "numberOfCases" bound numeric field on the main page
        And the user writes "2" in the numeric field
        And the user selects the "quantityOfProducts" bound numeric field on the main page
        And the user writes "11" in the numeric field
        And the user selects the "greaterThanProductQuantity" bound switch field on the main page
        And the user clicks in the switch field
        And the user clicks the "applyFilter" bound button on the main page
        And the user selects the "field" bound reference field on the main page
        And the user writes "Adams; Okuneva and Ebert" in the reference field
        And the user selects the "field" bound table field on a full width modal
        And the user clicks the card 1 in the table field
        And the user selects the "field" bound reference field on the main page
        Then the value of the reference field is "Adams; Okuneva and Ebert"
        Then the helper text of the reference field is "272"
        And the user selects the "quantityOfProducts" bound numeric field on the main page
        And the user writes "13" in the numeric field
        # And takes a screenshot
        And the user selects the "greaterThanProductQuantity" bound switch field on the main page
        And the user clicks in the switch field
        And the user clicks the "applyFilter" bound button on the main page
        And the user selects the "field" bound reference field on the main page
        And the user writes "Adams; Okuneva and Ebert" in the reference field
        Then the value of the reference field is "Adams; Okuneva and Ebert"
        Then the helper text of the reference field is "497"


    Scenario: As a user I want to filter the reference using an _every quantifier
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseInvoiceReference"
        And the user selects the "shouldFilter" bound switch field on the main page
        And the user clicks in the switch field
        And the user selects the "operatorType" bound select field on the main page
        And the user writes "every" in the select field
        And the user selects "every" in the select field
        And the user selects the "quantityOfProducts" bound numeric field on the main page
        And the user writes "10" in the numeric field
        And the user selects the "greaterThanProductQuantity" bound switch field on the main page
        And the user clicks in the switch field
        And the user clicks the "applyFilter" bound button on the main page
        And the user waits 2 second
        And the user selects the "field" bound reference field on the main page
        When the user clicks the lookup button of the reference field
        Then the dialog title is "Invoices"
        And the user selects the "field" bound table field on a modal
        Then the "customer__name" bound column in the table field is displayed
        And the user waits 1 second
        When the user opens the filter of the "customer__name" bound column in the table field
        And the user searches "Adams; Okuneva and Ebert" in the filter of the table field
        And the user ticks the item with text "Adams; Okuneva and Ebert" in the filter of the table field
        And the user closes the filter of the "customer__name" bound column in the table field
        And  the user selects the row 1 of the table field
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "04/17/2020"
        And the user selects the row 2 of the table field
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "02/22/2020"

    #Test 2 - XT-35650
    Scenario: As a user I want to filter the reference lookup dialog in mobile using an _every quantifier
        Given the user opens the application on a mobile using the following link: "@sage/xtrem-show-case/ShowCaseInvoiceReference"
        And the user selects the "shouldFilter" bound switch field on the main page
        And the user clicks in the switch field
        And the user selects the "operatorType" bound select field on the main page
        And the user writes "every" in the select field
        And the user selects "every" in the select field
        And the user selects the "quantityOfProducts" bound numeric field on the main page
        And the user writes "10" in the numeric field
        And the user selects the "greaterThanProductQuantity" bound switch field on the main page
        And the user clicks in the switch field
        And the user clicks the "applyFilter" bound button on the main page
        And the user dismisses all the toasts
        And the user selects the "field" bound reference field on the main page
        And the user writes "Adams; Okuneva and Ebert" in the reference field
        And the user selects the "field" bound table field on a full width modal
        And the user clicks the card 2 in the table field
        And the user selects the "field" bound reference field on the main page
        Then the value of the reference field is "Adams; Okuneva and Ebert"
        Then the helper text of the reference field is "438"
        And the user selects the "quantityOfProducts" bound numeric field on the main page
        And the user writes "15" in the numeric field
        And the user selects the "greaterThanProductQuantity" bound switch field on the main page
        And the user clicks in the switch field
        And the user clicks the "applyFilter" bound button on the main page
        And the user dismisses all the toasts
        And the user selects the "field" bound reference field on the main page
        And the user clears the reference field
        And the user writes "Adams; Okuneva and Ebert" in the reference field
        And the user selects the "field" bound table field on a full width modal
        And the user clicks the card 3 in the table field
        Then the value of the reference field is "Adams; Okuneva and Ebert"
        Then the helper text of the reference field is "497"

    Scenario: As a user I want to filter the reference using a _none quantifier
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseInvoiceReference"
        And the user selects the "shouldFilter" bound switch field on the main page
        And the user clicks in the switch field
        And the user selects the "operatorType" bound select field on the main page
        And the user writes "none" in the select field
        And the user selects "none" in the select field
        And the user selects the "quantityOfOrders" bound numeric field on the main page
        And the user writes "21" in the numeric field
        And the user selects the "greaterThanOrderQuantity" bound switch field on the main page
        And the user clicks in the switch field
        And the user clicks the "applyFilter" bound button on the main page
        And the user waits 2 second
        And the user selects the "field" bound reference field on the main page
        When the user clicks the lookup button of the reference field
        Then the dialog title is "Invoices"
        And the user selects the "field" bound table field on a modal
        Then the "customer__name" bound column in the table field is displayed
        And the user waits 1 second
        When the user opens the filter of the "customer__name" bound column in the table field
        And the user searches "Adams; Okuneva and Ebert" in the filter of the table field
        And the user ticks the item with text "Adams; Okuneva and Ebert" in the filter of the table field
        And the user closes the filter of the "customer__name" bound column in the table field
        And  the user selects the row 1 of the table field
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "04/17/2020"

    Scenario: As a user I want to filter the reference lookup dialog in mobile using a _none quantifier
        Given the user opens the application on a mobile using the following link: "@sage/xtrem-show-case/ShowCaseInvoiceReference"
        And the user selects the "shouldFilter" bound switch field on the main page
        And the user clicks in the switch field
        And the user selects the "operatorType" bound select field on the main page
        And the user writes "none" in the select field
        And the user selects "none" in the select field
        And the user selects the "quantityOfOrders" bound numeric field on the main page
        And the user writes "21" in the numeric field
        And the user selects the "greaterThanOrderQuantity" bound switch field on the main page
        And the user clicks in the switch field
        And the user clicks the "applyFilter" bound button on the main page
        And the user dismisses all the toasts
        And the user selects the "field" bound reference field on the main page
        And the user writes "Adams; Okuneva and Ebert" in the reference field
        And the user selects the "field" bound reference field on the main page
        Then the value of the reference field is "Adams; Okuneva and Ebert"
        Then the helper text of the reference field is "272"
        And the user selects the "quantityOfOrders" bound numeric field on the main page
        And the user writes "7" in the numeric field
        And the user selects the "greaterThanOrderQuantity" bound switch field on the main page
        And the user clicks in the switch field
        And the user clicks the "applyFilter" bound button on the main page
        And the user dismisses all the toasts
        And the user selects the "field" bound reference field on the main page
        And the user clears the reference field
        And the user writes "Adams; Okuneva and Ebert" in the reference field
        And the user selects the "field" bound table field on a full width modal
        And the user clicks the card 3 in the table field
        And the user selects the "field" bound reference field on the main page
        Then the value of the reference field is "Adams; Okuneva and Ebert"
        Then the helper text of the reference field is "497"

    Scenario: As a user I want to filter the reference using an _atMost quantifier
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseInvoiceReference"
        And the user selects the "shouldFilter" bound switch field on the main page
        And the user clicks in the switch field
        And the user waits 1 second
        And the user selects the "operatorType" bound select field on the main page
        And the user writes "at most" in the select field
        And the user selects "at most" in the select field
        And the user selects the "numberOfCases" bound numeric field on the main page
        And the user writes "0" in the numeric field
        And the user selects the "quantityOfOrders" bound numeric field on the main page
        And the user writes "20" in the numeric field
        And the user selects the "greaterThanOrderQuantity" bound switch field on the main page
        And the user clicks in the switch field
        And the user clicks the "applyFilter" bound button on the main page
        And the user waits 2 second
        And the user selects the "field" bound reference field on the main page
        When the user clicks the lookup button of the reference field
        Then the dialog title is "Invoices"
        And the user selects the "field" bound table field on a modal
        Then the "customer__name" bound column in the table field is displayed
        And the user waits 1 second
        When the user opens the filter of the "customer__name" bound column in the table field
        And the user searches "Adams; Okuneva and Ebert" in the filter of the table field
        And the user ticks the item with text "Adams; Okuneva and Ebert" in the filter of the table field
        And the user closes the filter of the "customer__name" bound column in the table field
        And  the user selects the row 1 of the table field
        Then the value of the "purchaseDate" bound nested date field of the selected row in the table field is "04/17/2020"

    #Test 3 - XT-35650
    Scenario: As a user I want to filter the reference lookup dialog in mobile using an _atMost quantifier
        Given the user opens the application on a mobile using the following link: "@sage/xtrem-show-case/ShowCaseInvoiceReference"
        And the user selects the "shouldFilter" bound switch field on the main page
        When the user clicks in the switch field
        And the user selects the "operatorType" bound select field on the main page
        And the user writes "at most" in the select field
        And the user selects the "numberOfCases" bound numeric field on the main page
        And the user writes "0" in the numeric field
        And the user selects the "quantityOfOrders" bound numeric field on the main page
        And the user writes "20" in the numeric field
        And the user selects the "greaterThanOrderQuantity" bound switch field on the main page
        When the user clicks in the switch field
        And the user clicks the "applyFilter" bound button on the main page
        And the user selects the "field" bound reference field on the main page
        And the user writes "Adams; Okuneva and Ebert" in the reference field
        Then the value of the reference field is "Adams; Okuneva and Ebert"
        Then the helper text of the reference field is "272"
        And the user selects the "numberOfCases" bound numeric field on the main page
        And the user writes "1" in the numeric field
        And the user selects the "quantityOfOrders" bound numeric field on the main page
        And the user writes "21" in the numeric field
        And the user selects the "greaterThanOrderQuantity" bound switch field on the main page
        When the user clicks in the switch field
        And the user clicks the "applyFilter" bound button on the main page
        And the user selects the "field" bound reference field on the main page
        And the user clears the reference field
        And the user writes "Adams; Okuneva and Ebert" in the reference field
        And the user selects the "field" bound table field on a full width modal
        And the user clicks the card 3 in the table field
        Then the value of the reference field is "Adams; Okuneva and Ebert"
        Then the helper text of the reference field is "497"

    #Test 4 - XT-35650
    Scenario: As a user I want to filter the reference lookup dialog in mobile using an _atLeast quantifier and change it to another _atLeast quantifier
        Given the user opens the application on a mobile using the following link: "@sage/xtrem-show-case/ShowCaseInvoiceReference"
        And the user selects the "shouldFilter" bound switch field on the main page
        When the user clicks in the switch field
        And the user selects the "numberOfCases" bound numeric field on the main page
        And the user writes "2" in the numeric field
        And the user selects the "quantityOfProducts" bound numeric field on the main page
        And the user writes "11" in the numeric field
        And the user selects the "greaterThanProductQuantity" bound switch field on the main page
        When the user clicks in the switch field
        And the user clicks the "applyFilter" bound button on the main page
        And the user dismisses all the toasts
        And the user selects the "field" bound reference field on the main page
        And the user writes "Adams; Okuneva and Ebert" in the reference field
        And the user selects the "field" bound table field on a full width modal
        And the user clicks the card 1 in the table field
        Then the value of the reference field is "Adams; Okuneva and Ebert"
        Then the helper text of the reference field is "272"
        And the user selects the "quantityOfOrders" bound numeric field on the main page
        And the user writes "20" in the numeric field
        And the user selects the "greaterThanOrderQuantity" bound switch field on the main page
        When the user clicks in the switch field
        And the user selects the "quantityOfProducts" bound numeric field on the main page
        And the user clears the numeric field
        # And takes a screenshot
        And the user clicks the "applyFilter" bound button on the main page
        And the user dismisses all the toasts
        And the user selects the "field" bound reference field on the main page
        And the user clears the reference field
        And the user writes "Adams; Okuneva and Ebert" in the reference field
        Then the value of the reference field is "Adams; Okuneva and Ebert"
        Then the helper text of the reference field is "497"

    #Test 5 - XT-35650
    Scenario: As a user I want to filter the reference lookup dialog in mobile using an _atLeast quantifier and change it to an _atMost quantifier
        Given the user opens the application on a mobile using the following link: "@sage/xtrem-show-case/ShowCaseInvoiceReference"
        And the user selects the "shouldFilter" bound switch field on the main page
        And the user clicks in the switch field
        And the user selects the "numberOfCases" bound numeric field on the main page
        And the user writes "2" in the numeric field
        And the user selects the "quantityOfProducts" bound numeric field on the main page
        And the user writes "11" in the numeric field
        And the user selects the "greaterThanProductQuantity" bound switch field on the main page
        And the user clicks in the switch field
        And the user clicks the "applyFilter" bound button on the main page
        And the user selects the "field" bound reference field on the main page
        And the user writes "Adams; Okuneva and Ebert" in the reference field
        And the user selects the "field" bound table field on a full width modal
        And the user clicks the card 1 in the table field
        And the user selects the "field" bound reference field on the main page
        Then the value of the reference field is "Adams; Okuneva and Ebert"
        Then the helper text of the reference field is "272"
        And the user selects the "operatorType" bound select field on the main page
        And the user writes "at most" in the select field
        And the user selects the "numberOfCases" bound numeric field on the main page
        And the user writes "1" in the numeric field
        And the user selects the "quantityOfProducts" bound numeric field on the main page
        And the user writes "21" in the numeric field
        And the user selects the "greaterThanOrderQuantity" bound switch field on the main page
        And the user clicks in the switch field
        And the user waits 1 second
        And the user clicks the "applyFilter" bound button on the main page
        And the user selects the "field" bound reference field on the main page
        And the user clears the reference field
        And the user writes "Adams; Okuneva and Ebert" in the reference field
        And the user selects the "field" bound table field on a full width modal
        And the user clicks the card 2 in the table field
        Then the value of the reference field is "Adams; Okuneva and Ebert"
        Then the helper text of the reference field is "272"

    #Test 6 - XT-35650
    Scenario: As a user I want to filter the reference lookup dialog in mobile using an _atLeast quantifier and then remove the filter
        Given the user opens the application on a mobile using the following link: "@sage/xtrem-show-case/ShowCaseInvoiceReference"
        And the user selects the "shouldFilter" bound switch field on the main page
        When the user clicks in the switch field
        And the user selects the "numberOfCases" bound numeric field on the main page
        And the user writes "2" in the numeric field
        And the user selects the "quantityOfProducts" bound numeric field on the main page
        And the user writes "11" in the numeric field
        And the user selects the "greaterThanProductQuantity" bound switch field on the main page
        When the user clicks in the switch field
        And the user clicks the "applyFilter" bound button on the main page
        And the user selects the "field" bound reference field on the main page
        And the user writes "Adams; Okuneva and Ebert" in the reference field
        And the user selects the "field" bound table field on a full width modal
        And the user clicks the card 1 in the table field
        Then the value of the reference field is "Adams; Okuneva and Ebert"
        Then the helper text of the reference field is "272"
        And the user selects the "shouldFilter" bound switch field on the main page
        When the user clicks in the switch field
        And the user selects the "field" bound reference field on the main page
        And the user clears the reference field
        And the user writes "Adams; Okuneva and Ebert" in the reference field
        And the user selects the "field" bound table field on a full width modal
        And the user clicks the card 1 in the table field
        Then the value of the reference field is "Adams; Okuneva and Ebert"
        Then the helper text of the reference field is "204"

    Scenario: As a user I want to be able to select matching items from the lookup dialog of reference field on mobile
        Given the user opens the application on a mobile using the following link: "@sage/xtrem-show-case/ShowCaseInvoiceReference"
        And the user selects the "shouldFilter" bound switch field on the main page
        And the user clicks in the switch field
        And the user selects the "operatorType" bound select field on the main page
        And the user writes "every" in the select field
        And the user selects "every" in the select field
        And the user selects the "quantityOfProducts" bound numeric field on the main page
        And the user writes "10" in the numeric field
        And the user selects the "greaterThanProductQuantity" bound switch field on the main page
        And the user clicks in the switch field
        And the user clicks the "applyFilter" bound button on the main page
        And the user dismisses all the toasts
        When the user selects the "field" bound reference field on the main page
        When the user writes "A" in the reference field
        And the user selects the "field" bound table field on a full width modal
        And the user clicks the Close button of the dialog on a full width modal
        When the user selects the "field" bound reference field on the main page
        Then the value of the reference field is ""
        And the user writes "Adams; Okuneva and Ebert" in the reference field
        And the user selects the "field" bound table field on a full width modal
        And the user clicks the card 2 in the table field
        And the user selects the "field" bound reference field on the main page
        Then the value of the reference field is "Adams; Okuneva and Ebert"
        Then the helper text of the reference field is "438"
        And the user writes "Adams; Okuneva and Eber" in the reference field
        When the user selects the "field" bound table field on a full width modal
        And the user clicks the card 2 in the table field
        When the user selects the "field" bound reference field on the main page
        Then the value of the reference field is "Adams; Okuneva and Ebert"
        Then the helper text of the reference field is "438"
        When the user writes "Adams; Okuneva and Eber" in the reference field
        And the user selects the "field" bound table field on a full width modal
        And the user clicks the card 1 in the table field
        When the user selects the "field" bound reference field on the main page
        Then the value of the reference field is "Adams; Okuneva and Ebert"
        Then the helper text of the reference field is "272"
        When the user writes "Adams; Okuneva and Eber" in the reference field
        And the user blurs the reference field
        Then the value of the reference field is "Adams; Okuneva and Ebert"
