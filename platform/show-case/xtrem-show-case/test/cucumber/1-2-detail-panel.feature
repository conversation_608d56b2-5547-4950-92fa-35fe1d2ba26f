Feature: 1-2 Detail panel
    # Tests the functionality of the detail panel component, including showing/hiding the panel, navigating between different sections using tabs, and verifying content display within the panel

    Scenario: As a user I want to display or hide the detail panel
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/DetailPanel/eyJfaWQiOiIxMDYifQ=="
        And the detail panel is displayed
        Then the "Time tracking" labelled titled detail panel header is displayed
        # And takes a screenshot
        When the user clicks in the "toggleDetailPanelButton" bound button field on the main page
        Then the detail panel is hidden
        Then the "Time tracking" labelled titled detail panel header is hidden
    # And takes a screenshot

    Scenario: As a user I want to display sections in the detail panel by clicking on tabs by their index
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/DetailPanel/eyJfaWQiOiIxMDYifQ=="
        When the user selects the tab 1 in the detail panel
        Then the "firstSectionBlock" labelled block container on the detail panel is displayed
        And the title of the "firstSectionBlock" labelled block container on the detail panel is "First section block"
        And the "detailPanelProductCategory" bound text field on the detail panel is displayed
        When the user selects the tab 2 in the detail panel
        Then the "detailPanelBlock4" bound block container on the detail panel is displayed
        And the title of the "detailPanelBlock4" bound block container on the detail panel is "Draft section block"
        And the "detailPanelNote" bound text area field on the detail panel is displayed
        Then the user selects the tab 3 in the detail panel
        And the "Third section block" labelled block container on the detail panel is displayed
        Then the user selects the tab 4 in the detail panel
        And the "Forth section block" labelled block container on the detail panel is displayed
        Then the user selects the tab 5 in the detail panel
        And the "Fifth section block" labelled block container on the detail panel is displayed
        When the user selects the tab 2 in the detail panel
        Then the "detailPanelBlock4" bound block container on the detail panel is displayed

    Scenario: As a user I want to display sections in the detail panel by clicking on tabs by label
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/DetailPanel/eyJfaWQiOiIxMDYifQ=="
        When the user selects the "One section" labelled tab in the detail panel
        Then the "firstSectionBlock" labelled block container on the detail panel is displayed
        And the title of the "firstSectionBlock" labelled block container on the detail panel is "First section block"
        And the "detailPanelProductCategory" bound text field on the detail panel is displayed
        When the user selects the "Another section" labelled tab in the detail panel
        Then the "detailPanelBlock4" bound block container on the detail panel is displayed
        And the title of the "detailPanelBlock4" bound block container on the detail panel is "Draft section block"
        And the "detailPanelNote" bound text area field on the detail panel is displayed
        When the user selects the "Third" labelled tab in the detail panel
        And the "Third section block" labelled block container on the detail panel is displayed
        When the user selects the "Forth" labelled tab in the detail panel
        And the "Forth section block" labelled block container on the detail panel is displayed
        When the user selects the "Fifth" labelled tab in the detail panel
        And the "Fifth section block" labelled block container on the detail panel is displayed
        When the user selects the "Another section" labelled tab in the detail panel
        Then the "detailPanelBlock4" bound block container on the detail panel is displayed

    Scenario: As a user I want to close the detail panel
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/DetailPanel/eyJfaWQiOiIxMDYifQ=="
        And the detail panel is displayed
        When the user clicks the detail panel closing button
        Then the detail panel is hidden

    Scenario: As a user I want to display or hide each action button in the detail panel
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/DetailPanel/eyJfaWQiOiIxMDYifQ=="
        And the "Action 1" labelled business action button on the detail panel is visible
        And the "Action 2" labelled business action button on the detail panel is visible
        When the user selects the "Action 1 isHidden" labelled checkbox field on the main page
        And the user clicks in the checkbox field
        And the user selects the "Action 2 isHidden" labelled checkbox field on the main page
        And the user clicks in the checkbox field
        Then the "Action 1" labelled business action button on the detail panel is hidden
        Then the "Action 2" labelled business action button on the detail panel is hidden

    Scenario: As a user I want to display or hide all action buttons in the detail panel
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/DetailPanel/eyJfaWQiOiIxMDYifQ=="
        And the "Action 1" labelled business action button on the detail panel is visible
        And the "Action 2" labelled business action button on the detail panel is visible
        When the user clicks in the "toggleAllActionsHidden" bound button field on the main page
        Then the "Action 1" labelled business action button on the detail panel is hidden
        Then the "Action 2" labelled business action button on the detail panel is hidden

    Scenario: As a user I want to enable or disable action buttons in the detail panel
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/DetailPanel/eyJfaWQiOiIxMDYifQ=="
        And the "Action 1" labelled business action button on the detail panel is enabled
        And the "Action 2" labelled business action button on the detail panel is enabled
        When the user selects the "Action 1 isDisabled" labelled checkbox field on the main page
        And the user clicks in the checkbox field
        When the user selects the "Action 2 isDisabled" labelled checkbox field on the main page
        And the user clicks in the checkbox field
        And the "Action 1" labelled business action button on the detail panel is disabled
        And the "Action 2" labelled business action button on the detail panel is disabled
