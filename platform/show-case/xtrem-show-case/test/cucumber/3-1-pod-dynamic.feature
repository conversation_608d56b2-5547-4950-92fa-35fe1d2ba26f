Feature: 3-1 Pod dynamic
    # Tests the pod component functionality, including helper text display, field validation, container behavior across different devices and control the pod dynamic field state.

    <PERSON><PERSON><PERSON>: As an XTreeM user, I can interact with dynamic pod field
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/DynamicPodField/eyJfaWQiOiIxIn0="
        Then the "Dynamic Pod Field" titled page is displayed
        And the user selects the "Dynamic Pod field" labelled dynamic-pod field on the main page
        And the user clicks the "Add an item" button of the dynamic-pod field


        #Write in nested text field
        And the user writes "Test" in the "Contact name" labelled nested text field of the dynamic-pod field
        And the user writes "Test" in the "Contact phone number" labelled nested text field of the dynamic-pod field

        #Write in nested text area field
        And the user writes "Test A" in the "Internal notes" labelled nested text area field of the dynamic-pod field
        And the user writes "Test B" in the "Internal notes" labelled nested text area field of the dynamic-pod field

        #Write and select value in nested reference field
        And the user writes "Fra" in the "Show case country" labelled nested reference field of the dynamic-pod field
        And at least the following list of options is displayed "France" in the "Show case country" labelled nested reference field of the dynamic-pod field
        And the user selects "France" in the "Show case country" labelled nested reference field of the dynamic-pod field


        #Click nested switch field
        And the user clicks the "Hot (Switch)" labelled nested switch field of the dynamic-pod field

        #Select a value into a nested dynamic select field
        And the user clicks the "Open list" action button of nested "Dynamic select" labelled dynamic-select field of the dynamic-pod field
        And at least the following list of options is displayed "One|Two|Three" in the "Dynamic select" labelled nested dynamic-select field of the dynamic-pod field
        And the user selects "One" in the "Dynamic select" labelled nested dynamic-select field of the dynamic-pod field

        #Verify the value of the nested field of the dynamic pod
        Then the value of the "Contact name" labelled nested text field in the dynamic-pod field is "Test"
        Then the value of the "Contact phone number" labelled nested text field in the dynamic-pod field is "Test"
        Then the value of the "Internal notes" labelled nested text area field in the dynamic-pod field is "Test B"
        Then the value of the "Show case country" labelled nested reference field in the dynamic-pod field is "France"
        Then the "Hot (Switch)" labelled nested switch field in the dynamic-pod field is set to "ON"
        Then the value of the "Dynamic select" labelled nested dynamic-select field in the dynamic-pod field is "1"

    Scenario Outline: <Device> - As a user I want to verify the dynamic pod field is empty / not empty
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/DynamicPodField/eyJfaWQiOiIxIn0="
        Then the "Dynamic Pod Field" titled page is displayed

        And the user selects the "Dynamic Pod field" labelled dynamic-pod field on the main page
        And the dynamic-pod field header container value is "No data to display."
        And the dynamic-pod field is empty
        And the user clicks the "Add an item" button of the dynamic-pod field
        And the dynamic-pod field is not empty
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |


    Scenario Outline: <Device> - As a user I want to verify the title of the dynamic pod field
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/DynamicPodField/eyJfaWQiOiIxIn0="
        Then the "Dynamic Pod Field" titled page is displayed
        And the user selects the "Dynamic Pod field" labelled dynamic-pod field on the main page
        And the user clicks the "Add an item" button of the dynamic-pod field

        And the user selects the "Title" labelled text field on the main page
        When the user writes "Sample Title" in the text field
        And the user blurs the text field

        And the user selects the "Sample Title" labelled dynamic-pod field on the main page
        Then the title of the dynamic-pod field is displayed
        Then the title of the dynamic-pod field is "Sample Title"

        And the user selects the "Is Title hidden" labelled checkbox field on the main page
        When the user ticks the checkbox field

        And the user selects the "additionalInfo" bound dynamic-pod field on the main page
        Then the title of the dynamic-pod field is hidden
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |


    Scenario Outline: <Device> - As a user I want to verify the helper text of the dynamic pod field
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/DynamicPodField/eyJfaWQiOiIxIn0="
        Then the "Dynamic Pod Field" titled page is displayed
        And the user selects the "Dynamic Pod field" labelled dynamic-pod field on the main page
        And the user clicks the "Add an item" button of the dynamic-pod field


        And the user selects the "Helper text" labelled text field on the main page
        When the user writes "Sample text" in the text field
        And the user blurs the text field

        And the user selects the "Dynamic Pod field" labelled dynamic-pod field on the main page
        Then the helper text of the dynamic-pod field is displayed
        Then the helper text of the dynamic-pod field is "Sample text"

        And the user selects the "Is helper text hidden" labelled checkbox field on the main page
        When the user ticks the checkbox field

        And the user selects the "Dynamic Pod field" labelled dynamic-pod field on the main page
        Then the helper text of the dynamic-pod field is hidden
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |


    Scenario Outline: <Device> - As a user I want to verify the dynamic pod field is enabled or disabled
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/DynamicPodField/eyJfaWQiOiIxIn0="
        Then the "Dynamic Pod Field" titled page is displayed

        And the user selects the "Dynamic Pod field" labelled dynamic-pod field on the main page
        And the user clicks the "Add an item" button of the dynamic-pod field

        And the user selects the "Dynamic Pod field" labelled dynamic-pod field on the main page
        Then the dynamic-pod field is enabled
        And the user writes "Test" in the "Contact name" labelled nested text field of the dynamic-pod field
        And the user presses Tab

        And the user selects the "Is disabled" labelled checkbox field on the main page
        When the user ticks the checkbox field

        Then the dynamic-pod field is disabled
        Then the value of the "Contact name" labelled nested text field in the dynamic-pod field is "Test"

        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: <Device> - As a user I want to verify the dynamic pod field is read-only
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/DynamicPodField/eyJfaWQiOiIxIn0="
        Then the "Dynamic Pod Field" titled page is displayed

        And the user selects the "Dynamic Pod field" labelled dynamic-pod field on the main page
        And the user clicks the "Add an item" button of the dynamic-pod field

        And the user selects the "Dynamic Pod field" labelled dynamic-pod field on the main page
        Then the dynamic-pod field is enabled
        And the user writes "Test" in the "Contact name" labelled nested text field of the dynamic-pod field
        And the user presses Tab

        And the user selects the "Is readOnly" labelled checkbox field on the main page
        When the user ticks the checkbox field

        Then the dynamic-pod field is read-only
        Then the value of the "Contact name" labelled nested text field in the dynamic-pod field is "Test"
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |


    Scenario Outline: <Device> - As a user I want to verify the dynamic pod field is displayed / hidden
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/DynamicPodField/eyJfaWQiOiIxIn0="
        Then the "Dynamic Pod Field" titled page is displayed

        And the user selects the "Dynamic Pod field" labelled dynamic-pod field on the main page
        And the user clicks the "Add an item" button of the dynamic-pod field

        Then the "Dynamic Pod field" labelled dynamic-pod field on the main page is displayed

        And the user selects the "Is hidden" labelled checkbox field on the main page
        When the user ticks the checkbox field

        Then the "additionalInfo" bound dynamic-pod field on the main page is hidden
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |
