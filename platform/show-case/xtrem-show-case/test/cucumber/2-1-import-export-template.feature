Feature: 2-1 Import export template
    # Tests the import/export template functionality, verifying creation, configuration, and use of templates for data import and export operations

    Scenario: Create an import export template
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-import-export/ImportExportTemplate"
        And the user clicks the "Create" labelled business action button on the navigation panel
        And the user selects the "id" bound text field on the main page
        And the user clicks in the text field
        And the user writes "TestTemplate1" in the text field
        And the user selects the "name" bound text field on the main page
        And the user clicks in the text field
        And the user writes "TestTemplate1" in the text field
        And the user selects the "nodeName" bound select field on the main page
        And the user clicks in the select field
        And the user selects "User" in the select field
        Then the value of the select field is "User"
        And the user selects the "Template use" labelled dropdown-list field on the main page
        Then the value of the dropdown-list field is "Import and export"
        When the user clicks the "Save" labelled business action button on the main page
    #Then no dialogs are displayed
    #Then a toast with text "Record created" is displayed
    #Then the user dismisses all the toasts

    Sc<PERSON>rio: Delete add and move lines in grid
        And the user searches for "TestTemplate1" in the navigation panel
        And the user clicks the record with the text "TestTemplate1" in the navigation panel
        And the user selects the "name" bound text field on the main page
        Then the value of the text field is "TestTemplate1"
        And the user selects the "columnsTable" bound table field on the main page
        And the user selects the row 3 of the table field
        Then the value of the "Field" labelled nested label field of the selected row in the table field is "firstName"
        And the user clicks the "Move down" inline action button of the selected row in the table field
        And the user selects the row 4 of the table field
        Then the value of the "Field" labelled nested label field of the selected row in the table field is "firstName"
        And the user selects the "columnsTable" bound table field on the main page
        And the user selects the row 6 of the table field
        And the user clicks the "Delete" dropdown action of the selected row of the table field
        And the user selects the row 2 of the table field
        And the user clicks the "Insert" inline action button of the selected row in the table field
        And the user selects the "nodeProperty" bound table field on a modal
        And the user filters the "name" bound column in the table field with value "photo"
        And the user selects the row 1 of the table field
        And the user clicks the "name" bound nested field of the selected row in the table field
        And the user clicks the "Save" labelled business action button on the main page
        Then the value of the toast is "Record updated"

    Scenario: Delete import template
        And the user searches for "TestTemplate1" in the navigation panel
        And the user clicks the record with the text "TestTemplate1" in the navigation panel
        And the user selects the "columnsTable" bound table field on the main page
        And the user selects the row 3 of the table field
        Then the value of the "Description" labelled nested label field of the selected row in the table field is "photo"
        And the user selects the row 5 of the table field
        Then the value of the "Field" labelled nested label field of the selected row in the table field is "firstName"
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        Then a toast containing text "Record deleted" is displayed

    Scenario: Import the file
        XT-42531

        #Create the import template
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-import-export/ImportExportTemplate"
        And the "Import and export templates" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel
        And the user selects the "Node name" labelled select field on the main page
        And the user writes "Act" in the select field
        And the user selects "Activity" in the select field
        And the user clicks the "Save" labelled business action button on the main page
        Then the value of the toast is "Record created"

        #Import file
        Then the user navigates to the following link: "@sage/xtrem-import-export/ImportData"
        And the "Data import" titled page is displayed
        And the user selects the "Select file" labelled file deposit field on the main page
        And the user adds the file "./import/import-template-activity-activity01.csv" to the file deposit field

        And the user selects the "importData" bound table field on the main page
        And the user selects the row 1 of the table field
        When the user clicks the "Template" labelled nested field of the selected row in the table field
        And the user writes "Activ" in the "Template" labelled nested select field of the selected row in the table field
        And the user selects "Activity" in the "Template" labelled nested field of the selected row in the table field
        And the user clicks the "Import" labelled business action button on the main page
        Then the text in the body of the dialog is "Do you want to import the file?"
        And the user clicks the "OK" button of the Confirm dialog
        Then a toast with text "import-template-activity-activity01.csv has been submitted for processing." is displayed
        And the user dismisses all the toasts

        #Check import results
        And selects the "Import results" labelled navigation anchor on the main page
        And the user waits 3 seconds
        And the user selects the "importResults" bound table field on the main page
        When the user clicks the "Refresh" labelled header action button of the table field
        #XT-67983 - step to reactivate once bug is fixed
        #Then the value of the "Status" labelled nested label field of row 1 in the table field is "Completed"

        #Click import file and remove it
        And selects the "Data" labelled navigation anchor on the main page
        And the user selects the "Select file" labelled file deposit field on the main page
        And the user clicks the "import-template-activity-activity01.csv" file in the file deposit field
        And the user removes the file from the file deposit field
        And the user selects the "csvFile" bound file deposit field on the main page
        And the user adds the file "./import/import-template-activity-activity02.csv" to the file deposit field


    Scenario: As an ATP XTreeM user I can verify the csv format of the import template
        #   XT-48704

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-import-export/ImportExportTemplate/"
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        When the user filters the "ID" labelled column in the table field with value "Site"
        And the user selects the row 2 of the table field
        And the user clicks the "ID" labelled nested field of the selected row in the table field
        And the "Import and export templates" titled page is displayed
        When the user selects the "CSV template" labelled code editor field on the main page
        Then the value of the code editor field is
            """
            "!id";"*name";"description";"isActive";"*legalCompany";""
            """
        And the user selects the "Data type" labelled checkbox field on the main page
        Then the value of the checkbox field is "false"
        And the user ticks the checkbox field
        Then the value of the code editor field is
            """
            "!id";"*name";"description";"isActive";"*legalCompany";""
            "string";"string";"string";"boolean";"reference";"IGNORE"
            """
        And the user selects the "Description" labelled checkbox field on the main page
        Then the value of the checkbox field is "false"
        And the user ticks the checkbox field
        Then the value of the code editor field is
            """
            "!id";"*name";"description";"isActive";"*legalCompany";""
            "string";"string";"string";"boolean";"reference";"IGNORE"
            "id";"name";"description";"is active (false/true)";"legal company (#id)";"IGNORE"

            """
        And the user selects the "Locale" labelled checkbox field on the main page
        Then the value of the checkbox field is "false"
        And the user ticks the checkbox field
        Then the value of the code editor field is
            """
            "!id";"*name";"description";"isActive";"*legalCompany";""
            "string";"string";"string";"boolean";"reference";"IGNORE"
            "id";"name";"description";"is active (false/true)";"legal company (#id)";"IGNORE"
            "";"";"";"";"";"IGNORE"
            """
    Scenario: As an ATP XTreeM user I want to export user data
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-authorization/User"
        And the "Users" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        When the user ticks the main checkbox of the selected row in the table field
        Then the bulk action bar of the table field has 1 items
        When the user clicks the "Export" labelled bulk action button of the table field
        Then a warn dialog appears on the main page
        And the user clicks the "Confirm" labelled business action button on a modal
        And the text in the header of the dialog is "Export"
        And the text in the body of the dialog is "Perform this action on the selected items: 1"
        When the user clicks the "OK" button of the Confirm dialog
        Then a toast containing text "Action started on the selected items." is displayed
        When the user navigates to the following link: "@sage/xtrem-communication/SysNotificationState"
        Then the "Batch task history" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row with text "Export" in the "Operation" labelled column header of the table field
        #When the user filters the "Operation" labelled column in the table field with value "Export"
        And the user selects the row 1 of the table field
        And the user clicks the "Status" labelled nested field of the selected row in the table field
        And the user selects the "Parameters" labelled table field on the main page
        And the user selects the row 2 of the table field
        Then the value of the "Value" labelled nested label field of the selected row in the table field is "User"
        And the user selects the "Status" labelled label field on the main page
        Then the value of the label field is "Success"
