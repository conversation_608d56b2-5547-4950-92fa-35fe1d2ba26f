Feature: 5 Y dashboard duplication
    # Tests the functionality of dashboard duplication, verifying that users can create, customize, and save dashboards with widgets

    @ClearDashboards
    Scenario: As a user I want to duplicate an existing dashboard
        Given the user opens the application on a desktop
        And the dashboard page is displayed
        Then the "Create a dashboard to get started." subtitled empty dashboard is displayed
        When the user clicks the create button on the dashboard
        Then the dashboard creation dialog is displayed
        When the user selects the template with title "Blank template" in the dashboard creation dialog
        And the user clicks the "next" button in the dashboard creation dialog
        Then the "New dashboard" titled dashboard in the dashboard editor is displayed
        When the user clicks the edit dashboard title icon
        And the user writes "First dashboard" in the dashboard title
        And the user presses Enter
        Then the "First dashboard" titled dashboard in the dashboard editor is displayed
        And the "Widgets" titled header in the dashboard editor navigation panel is displayed
        When the user clicks the Add button of the "Users" titled widget card in the dashboard editor navigation panel
        Then the "Users" titled widget in the dashboard editor is displayed
        When the user clicks the "save" button in the dashboard editor footer
        Then a toast containing text "Dashboard saved." is displayed
        When the user dismisses all the toasts
        Then the "First dashboard" titled dashboard is displayed
        And the "Users" titled widget in the dashboard is displayed
        When the user clicks the "duplication" labelled CRUD button in the dashboard action menu
        Then a toast containing text "Dashboard duplicated." is displayed
        When the user dismisses all the toasts
        Then the "First dashboard 2" titled dashboard is displayed
        And the "Users" titled widget in the dashboard is displayed
