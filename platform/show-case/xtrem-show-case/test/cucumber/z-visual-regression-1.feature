Feature: Z visual regression 1
    # Tests the visual appearance and layout consistency across various components, focusing on UI rendering and validating that elements display correctly

    <PERSON><PERSON><PERSON>: As a user I want the validation errros from the server displayed
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Numeric"
        And the user selects the "Prefix" labelled text field on the main page
        And the user writes "Pre" in the text field
        And the user selects the "Postfix" labelled text field on the main page
        And the user writes "Post" in the text field
        And the user selects the "Postfix" labelled text field on the main page
        And the user blurs the text field
        Then element with test id "e-field-bind-field" looks as before

    <PERSON><PERSON><PERSON>: As a user I the long field title to be truncated on a switch component
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Switch"
        And the user selects the "Title" labelled text field on the main page
        And the user writes "a very long title a very long title a very long title a very long title a very long title " in the text field
        And the user selects the "Title" labelled text field on the main page
        And the user blurs the text field
        Then element with test id "e-field-bind-field" looks as before

    <PERSON><PERSON><PERSON>: As a user I want the validation errros from the server displayed
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Numeric"
        When the user selects the "field" bound numeric field on the main page
        And the user clears the numeric field
        And the user selects the "Prefix" labelled text field on the main page
        And the user writes "Pre" in the text field
        And the user selects the "Postfix" labelled text field on the main page
        And the user writes "Post" in the text field
        And the user selects the "Placeholder" labelled text field on the main page
        And the user writes "XXXX" in the text field
        And the user blurs the text field
        And the user waits 3 seconds
        Then element with test id "e-field-bind-field" looks as before

    Scenario Outline: As a developer I want <Selector> on <Path> to look like as before using <Viewport> device
        Given the user opens the application on a <Viewport> using the following link: "<Path>"
        And the user waits 1 seconds
        Then element with <SelectorType> "<Selector>" looks as before
        Examples:
            | Selector                                                       | SelectorType | Viewport | Path                                                              |
            | e-detail-list-item-bind-product                                | test id      | mobile   | @sage/xtrem-show-case/DetailList/eyJfaWQiOiIxIn0=                 |
            | e-detail-list-item-label-conditionalDisplay                    | test id      | mobile   | @sage/xtrem-show-case/DetailList/eyJfaWQiOiIxIn0=                 |
            | e-detail-list-item-label-id                                    | test id      | mobile   | @sage/xtrem-show-case/DetailList/eyJfaWQiOiIxIn0=                 |
            | e-field-bind-originAddress                                     | test id      | desktop  | @sage/xtrem-show-case/VitalPodBlock/eyJfaWQiOiIxIn0=              |
            | e-pod-header-label                                             | class        | desktop  | @sage/xtrem-show-case/NestedVitalPodBlock/eyJfaWQiOiIxMDYifQ==    |
            | e-page-footer                                                  | class        | desktop  | @sage/xtrem-show-case/PageDefaultCrudActions/eyJfaWQiOiIkbmV3In0= |
            | e-page-footer                                                  | class        | desktop  | @sage/xtrem-show-case/ActionButtons                               |
            | e-page-footer                                                  | class        | mobile   | @sage/xtrem-show-case/ActionButtons                               |
            | e-card-body                                                    | class        | mobile   | @sage/xtrem-show-case/PageHeaderCardThreeLines                    |
            | e-card-body                                                    | class        | mobile   | @sage/xtrem-show-case/PageHeaderCard                              |
            | e-card-body                                                    | class        | mobile   | @sage/xtrem-show-case/PageHeaderCard                              |
            | e-card-body                                                    | class        | mobile   | @sage/xtrem-show-case/ShowCaseProductHeaderCard                   |
            | e-pod-field                                                    | test id      | desktop  | @sage/xtrem-show-case/PodComponentStates/eyJfaWQiOiI0NjcifQ==     |
            | e-vital-pod-field                                              | test id      | desktop  | @sage/xtrem-show-case/PodComponentStates/eyJfaWQiOiI0NjcifQ==     |
            | e-detail-panel-header                                          | class        | mobile   | @sage/xtrem-show-case/TableWithDetailPanel/eyJfaWQiOiIyIn0=       |
            | .e-page-body .e-table-field                                    | css selector | desktop  | @sage/xtrem-show-case/Table/eyJfaWQiOiIyIn0=                      |
            | e-table-summary-field                                          | class        | desktop  | @sage/xtrem-show-case/TableSummary/eyJfaWQiOiIyIn0=               |
            | [data-testid~="e-field-label-tableWithSectionAsParent"]        | css selector | desktop  | @sage/xtrem-show-case/Table/eyJfaWQiOiIyIn0=                      |
            | [data-testid~="e-field-label-summaryTableWithSectionAsParent"] | css selector | desktop  | @sage/xtrem-show-case/TableSummary/eyJfaWQiOiIyIn0=               |
            | [data-testid~="e-field-label-nestedGridWithSectionAsParent"]   | css selector | desktop  | @sage/xtrem-show-case/NestedGrid/eyJfaWQiOiI3In0=                 |
            | e-header                                                       | class        | desktop  | @sage/xtrem-show-case/NavigationPanel/eyJfaWQiOiIxMDYifQ==        |
            | [data-testid~="e-field-label-tableInReadOnlyMode"]             | css selector | desktop  | @sage/xtrem-show-case/Table/eyJfaWQiOiIyIn0=                      |
            | .ag-header-container                                           | css selector | desktop  | @sage/xtrem-show-case/Table/eyJfaWQiOiIyIn0=                      |
            | e-header-section                                               | test id      | desktop  | @sage/xtrem-show-case/HeaderSection/eyJfaWQiOiIxIn0=              |
            | e-table-field                                                  | test id      | desktop  | @sage/xtrem-show-case/NavigationPanelEmpty                        |
            | e-field-bind-field                                             | test id      | desktop  | @sage/xtrem-show-case/FileDeposit                                 |
            | e-header-title                                                 | class        | desktop  | @sage/xtrem-show-case/PageWithLongTitle                           |

    Scenario Outline: As a developer I want the nav panel <Selector> on <Path> to look like as before using <Viewport> device
        Given the user opens the application on a <Viewport> using the following link: "<Path>"
        And the user opens the navigation panel
        And the user waits 1 seconds
        Then element with <SelectorType> "<Selector>" looks as before
        Examples:
            | Selector | SelectorType | Viewport | Path                                                              |
            | e-card   | class        | mobile   | @sage/xtrem-show-case/NavigationPanelComplexCard/eyJfaWQiOiI3MyJ9 |
            | e-card   | class        | desktop  | @sage/xtrem-show-case/NavigationPanelComplexCard/eyJfaWQiOiI3MyJ9 |
            | e-card   | class        | mobile   | @sage/xtrem-show-case/CrudUpdateFile/eyJfaWQiOiIxIn0=             |
            | e-card   | class        | desktop  | @sage/xtrem-show-case/CrudUpdateFile/eyJfaWQiOiIxIn0=             |

    Scenario Outline: As a developer I want <Selector> pod variant to look as before using <Viewport> device
        Given the user opens the application on a <Viewport> using the following link: "@sage/xtrem-show-case/PodComponentStates/eyJfaWQiOiI0NjcifQ=="
        Then element with test id "<Selector>" looks as before
        Examples:
            | Selector                                      | Viewport |
            | e-field-bind-enabledValuePodBlock             | desktop  |
            | e-field-bind-disabledValuePodBlock            | desktop  |
            | e-field-bind-readonlyValuePodBlock            | desktop  |
            | e-field-bind-enabledEmptyPodBlock             | desktop  |
            | e-field-bind-disabledEmptyPodBlock            | desktop  |
            | e-field-bind-readonlyEmptyPodBlock            | desktop  |
            | e-field-bind-customEmptyPodBlock              | desktop  |
            | e-field-bind-enabledValueVitalPodBlock        | desktop  |
            | e-field-bind-disabledValueVitalPodBlock       | desktop  |
            | e-field-bind-readonlyValueVitalPodBlock       | desktop  |
            | e-field-bind-enabledEmptyVitalPodBlock        | desktop  |
            | e-field-bind-disabledEmptyVitalPodBlock       | desktop  |
            | e-field-bind-customEmptyVitalPodBlock         | desktop  |
            | e-field-bind-enabledValuePodField             | desktop  |
            | e-field-bind-disabledValuePodField            | desktop  |
            | e-field-bind-readonlyValuePodField            | desktop  |
            | e-field-bind-enabledEmptyPodField             | desktop  |
            | e-field-bind-disabledEmptyPodField            | desktop  |
            | e-field-bind-readonlyEmptyPodField            | desktop  |
            | e-field-bind-customEmptyPodField              | desktop  |
            | e-field-bind-enabledValueVitalPodField        | desktop  |
            | e-field-bind-disabledValueVitalPodField       | desktop  |
            | e-field-bind-readonlyValueVitalPodField       | desktop  |
            | e-field-bind-enabledEmptyVitalPodField        | desktop  |
            | e-field-bind-disabledEmptyVitalPodField       | desktop  |
            | e-field-bind-readonlyEmptyVitalPodField       | desktop  |
            | e-field-bind-customEmptyVitalPodField         | desktop  |
            | e-field-bind-enabledValuePodFieldInfo         | desktop  |
            | e-field-bind-enabledValuePodFieldWarning      | desktop  |
            | e-field-bind-enabledValueVitalPodFieldInfo    | desktop  |
            | e-field-bind-enabledValueVitalPodFieldWarning | desktop  |
            | e-field-bind-enabledValuePodBlockInfo         | desktop  |
            | e-field-bind-enabledValuePodBlockWarning      | desktop  |
            | e-field-bind-enabledValueVitalPodBlockInfo    | desktop  |
            | e-field-bind-enabledValueVitalPodBlockWarning | desktop  |
            | e-field-bind-customEmptyVitalPodBlock         | mobile   |
            | e-field-bind-enabledValuePodField             | mobile   |
            | e-field-bind-disabledValuePodField            | mobile   |
            | e-field-bind-readonlyValuePodField            | mobile   |
            | e-field-bind-enabledEmptyPodField             | mobile   |
            | e-field-bind-disabledEmptyPodField            | mobile   |
            | e-field-bind-readonlyEmptyPodField            | mobile   |
            | e-field-bind-customEmptyPodField              | mobile   |
            | e-field-bind-enabledValueVitalPodField        | mobile   |
            | e-field-bind-disabledValueVitalPodField       | mobile   |
            | e-field-bind-readonlyValueVitalPodField       | mobile   |
            | e-field-bind-enabledEmptyVitalPodField        | mobile   |
            | e-field-bind-disabledEmptyVitalPodField       | mobile   |
            | e-field-bind-readonlyEmptyVitalPodField       | mobile   |
            | e-field-bind-customEmptyVitalPodField         | mobile   |
            | e-field-bind-enabledValuePodBlock             | mobile   |
            | e-field-bind-disabledValuePodBlock            | mobile   |
            | e-field-bind-readonlyValuePodBlock            | mobile   |
            | e-field-bind-enabledEmptyPodBlock             | mobile   |
            | e-field-bind-disabledEmptyPodBlock            | mobile   |
            | e-field-bind-readonlyEmptyPodBlock            | mobile   |
            | e-field-bind-customEmptyPodBlock              | mobile   |
            | e-field-bind-enabledValueVitalPodBlock        | mobile   |
            | e-field-bind-disabledValueVitalPodBlock       | mobile   |
            | e-field-bind-readonlyValueVitalPodBlock       | mobile   |
            | e-field-bind-enabledEmptyVitalPodBlock        | mobile   |
            | e-field-bind-disabledEmptyVitalPodBlock       | mobile   |
            | e-field-bind-enabledValuePodFieldInfo         | mobile   |
            | e-field-bind-enabledValuePodFieldWarning      | mobile   |
            | e-field-bind-enabledValueVitalPodFieldInfo    | mobile   |
            | e-field-bind-enabledValueVitalPodFieldWarning | mobile   |
            | e-field-bind-enabledValuePodBlockInfo         | mobile   |
            | e-field-bind-enabledValuePodBlockWarning      | mobile   |
            | e-field-bind-enabledValueVitalPodBlockInfo    | mobile   |
            | e-field-bind-enabledValueVitalPodBlockWarning | mobile   |

    Scenario Outline: As a developer I want the PodBlock's numeric-field to look as before using <Viewport> device
        Given the user opens the application on a <Viewport> using the following link: "@sage/xtrem-show-case/PodBlock/eyJfaWQiOiIxMDYifQ=="
        Then element with <SelectorType> "<Selector>" looks as before
        Examples:
            | SelectorType | Selector        | Viewport |
            | test id      | e-numeric-field | desktop  |
            | test id      | e-numeric-field | mobile   |

    Scenario Outline: As a developer I want the PodField's numeric-field to look as before using <Viewport> device
        Given the user opens the application on a <Viewport> using the following link: "@sage/xtrem-show-case/PodField/eyJfaWQiOiIxMDYifQ=="
        Then element with <SelectorType> "<Selector>" looks as before
        Examples:
            | SelectorType | Selector        | Viewport |
            | test id      | e-numeric-field | desktop  |
            | test id      | e-numeric-field | mobile   |

    Scenario Outline: As a developer I want my <Selector> into the detail panel to look as before using <Viewport> device
        Given the user opens the application on a <Viewport> using the following link: "@sage/xtrem-show-case/ShowCaseProvider/eyJfaWQiOiIxIn0="
        And the user selects the "products" bound table field on the main page
        And the user selects the row 1 of the table field
        When the user clicks the "Open in Detail panel" dropdown action of the selected row of the table field
        Then element with <SelectorType> "<Selector>" looks as before
        Examples:
            | Selector     | SelectorType | Viewport |
            | e-card-field | class        | desktop  |

    Scenario: As a Dev I want my Table without data after search to look as before using desktop device
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TableSummary/eyJfaWQiOiIyIn0="
        When selects the "Empty Example" labelled navigation anchor on the main page
        Then element with class "e-table-summary-field-body" looks as before

    Scenario: As a developer I want the empty table to look like as before
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TransientTable"
        # wait for column headers to finish loading
        And the user waits 1 second
        Then element with test id "e-table-field" looks as before

    Scenario: As a developer I want the navigation panel with no results to look like as before
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/NavigationPanel/eyJfaWQiOiIxMDYifQ=="
        And the user opens the navigation panel
        When the user searches for "wztr1" in the navigation panel
        Then element with class "e-page-navigation-panel-body" looks as before

    Scenario: As a developer I want my message dialog to look as before
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Dialogs"
        When the user clicks in the "messageDialogButton" bound button field on the main page
        Then element with css selector "div[role='dialog'][data-component*='dialog']" looks as before

    Scenario: As a developer I want my confirm dialog to look as before
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Dialogs"
        When the user clicks in the "confirmationDialogButton" bound button field on the main page
        Then element with css selector "div[role='dialog'][data-component*='dialog']" looks as before

    Scenario: As a developer I want my custom dialog to look as before
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Dialogs"
        When the user clicks in the "customDialogButton" bound button field on the main page
        Then element with css selector "div[role='dialog'][data-component*='dialog']" looks as before

    Scenario: As a developer I want my error dialog to look as before
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Dialogs"
        When the user clicks in the "errorDialogField" bound button field on the main page
        Then element with css selector "div[role='dialog'][data-component*='dialog']" looks as before

    Scenario: As a developer I want my page dialog to look as before
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Dialogs"
        When the user selects the "Path" labelled text field on the main page
        And the user writes "@sage/xtrem-show-case/CardField" in the text field
        And the user clicks in the "pageDialogButton" bound button field on the main page
        Then element with css selector "[data-component='dialog'][aria-modal=true]" looks as before

    # Scenario: As a developer I want my page dialog right-aligned to look as before
    #     Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Dialogs"
    #     When the user selects the "Path" labelled text field on the main page
    #     And the user writes "@sage/xtrem-show-case/CardField" in the text field
    #     And the user selects the "pageDialogIsRightAligned" bound checkbox field on the main page
    #     And the user clicks in the checkbox field
    #     And the user clicks in the "pageDialogButton" bound button field on the main page
    #     Then element with css selector "[data-element='sidebar']" looks as before

    Scenario: As a developer I want my page dialog fullscreen to look as before
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Dialogs"
        When the user selects the "Path" labelled text field on the main page
        And the user writes "@sage/xtrem-show-case/CardField" in the text field
        And the user selects the "pageDialogIsFullscreen" bound checkbox field on the main page
        And the user clicks in the checkbox field
        And the user clicks in the "pageDialogButton" bound button field on the main page
        Then element with css selector "[data-element='dialog-full-screen']" looks as before

    Scenario Outline: As a developer I want the <Selector> field's error states to look as before using <Viewport> device
        Given the user opens the application on a <Viewport> using the following link: "@sage/xtrem-show-case/FieldsWithValidationErrors"
        When the user clicks the "Validate" labelled business action button on the main page
        Then element with <SelectorType> "<Selector>" looks as before
        Examples:
            | SelectorType | Selector                | Viewport |
            | test id      | e-text-field            | mobile   |
            | test id      | e-text-area-field       | mobile   |
            | test id      | e-numeric-field         | mobile   |
            | test id      | e-date-field            | mobile   |
            | test id      | e-dropdown-list-field   | mobile   |
            | test id      | e-select-field          | mobile   |
            | test id      | e-reference-field       | mobile   |
            | test id      | e-filter-select-field   | mobile   |
            | test id      | e-multi-dropdown-field  | mobile   |
            | test id      | e-multi-reference-field | mobile   |
            | test id      | e-text-field            | desktop  |
            | test id      | e-text-area-field       | desktop  |
            | test id      | e-numeric-field         | desktop  |
            | test id      | e-date-field            | desktop  |
            | test id      | e-dropdown-list-field   | desktop  |
            | test id      | e-select-field          | desktop  |
            | test id      | e-reference-field       | desktop  |
            | test id      | e-filter-select-field   | desktop  |
            | test id      | e-multi-dropdown-field  | desktop  |
            | test id      | e-multi-reference-field | desktop  |

    Scenario: As a user I want the detail panel tab container to look like as before when overloaded
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/DetailPanel/"
        When the user selects the tab 1 in the detail panel
        Then the "firstSectionBlock" labelled block container on the detail panel is displayed
        Then element with css selector ".e-detail-panel-tab-container" looks as before

    Scenario: As a user I want the detail panel tab container to look like as before
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/DetailPanel/"
        When the user selects the "Fifth detail panel section hidden" labelled checkbox field on the main page
        And the user clicks in the checkbox field
        When the user selects the "Fourth detail panel section hidden" labelled checkbox field on the main page
        And the user clicks in the checkbox field
        When the user selects the "Third detail panel section hidden" labelled checkbox field on the main page
        And the user clicks in the checkbox field
        When the user selects the tab 1 in the detail panel
        Then the "firstSectionBlock" labelled block container on the detail panel is displayed
        Then element with css selector ".e-detail-panel-tab-container" looks as before

    Scenario: As a developer I want the empty state mobile table to look as before
        Given the user opens the application on a mobile using the following link: "@sage/xtrem-show-case/TransientTable"
        Then element with class "e-table-field-mobile-rows" looks as before

    Scenario: As a user I want table column settings to look like before
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Table/eyJfaWQiOiIyIn0="
        Then the "Field - Table" titled page is displayed
        When the user selects the "field" bound table field on the main page
        And the user clicks the "Open column panel" labelled button of the table field
        Then the "Column settings" titled sidebar is displayed
        Then element with css selector ".e-table-configuration-dialog-columns" looks as before

    Scenario Outline: As a developer I want table cell "<ElementName>" to look like as before
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-show-case/Table"
        Then element with css selector "<Selector>" looks as before
        Examples:
            | ElementName          | Selector                                                           |
            | Not editable field   | [row-index='1'] .e-nested-cell-bind-_id[aria-colindex='3']         |
            | Text field           | [row-index='1'] .e-nested-cell-bind-product[aria-colindex='4']     |
            | Numeric field        | [row-index='1'] .e-nested-cell-bind-qty[aria-colindex='6']         |
            | Label                | [row-index='1'] .e-nested-cell-label-indicator[aria-colindex='10'] |
            | Checkbox (Checked)   | [row-index='1'] .e-nested-cell-bind-hotProduct[aria-colindex='12'] |
            | Checkbox (Unchecked) | [row-index='4'] .e-nested-cell-bind-hotProduct[aria-colindex='12'] |
            | Switch (ON)          | [row-index='1'] .e-nested-cell-bind-hotProduct[aria-colindex='13'] |
            | Switch (OFF)         | [row-index='4'] .e-nested-cell-bind-hotProduct[aria-colindex='13'] |

    Scenario Outline: As a developer I want the GraphQL <Path> page to load
        Given the user opens the application on a desktop using the following link: "<Path>"
        And the user waits 1 seconds
        Then element with class "product-name" looks as before
        Examples:
            | Path     |
            | metadata |
            | explorer |

    # Scenario: As a developer I want to make sure the lookup dialog on mobile looks like as before
    #     Given the user opens the application on a mobile using the following link: "@sage/xtrem-show-case/Reference"
    #     And the user clicks on the lookup button of the "field" bound reference field on the main page
    #     And the user waits 1 seconds
    #     Then element with class "e-lookup-dialog" looks as before

    # Scenario: As a developer I want to make sure that the the list of users widget looks like before
    #     Given the user opens the application on a desktop
    #     And the user waits 3 seconds
    #     Then element with test id "basic-1" looks as before

    # Scenario: As a developer I want to make sure that the the number of users widget looks like before
    #     Given the user opens the application on a desktop
    #     And the user waits 3 seconds
    #     Then element with test id "tile-indicator-2" looks as before

    # Scenario: As a developer I want to make sure that the the users widget looks like before
    #     Given the user opens the application on a desktop
    #     And the user waits 3 seconds
    #     Then element with test id "table-4" looks as before

    # Scenario: As a developer I want to make sure that the the list of products line chart widget looks like before
    #     Given the user opens the application on a desktop
    #     And the user waits 3 seconds
    #     Then element with test id "line-chart-6" looks as before

    # Scenario: As a developer I want to make sure that the the supplier visual process widget looks like before
    #     Given the user opens the application on a desktop
    #     And the user waits 3 seconds
    #     Then element with test id "visual-process-7" looks as before

    # Scenario: As a developer I want to make sure that the the supplier visual gauge widget looks like before
    #     Given the user opens the application on a desktop
    #     And the user waits 3 seconds
    #     Then element with test id "gauge-8" looks as before

    # Scenario: As a developer I want to make sure that the tab container on the dashboard looks like as before
    #     Given the user opens the application on a desktop
    #     And the user waits 3 seconds
    #     Then element with class "e-dashboard-selection-tab-container" looks as before

    Scenario Outline: As a developer I want the <Plugin> page to load
        Given the user opens the application on a desktop using the following link: "<Path>"
        And the user waits 1 seconds
        Then element with <SelectorType> "<Selector>" looks as before
        Examples:
            | Plugin        | Path                                                      | SelectorType | Selector                |
            | Monaco Editor | @sage/xtrem-show-case/PluginMonaco/eyJfaWQiOiIyNzIifQ==   | test id      | e-field-bind-notes      |
            | GraphiQL      | @sage/xtrem-show-case/PluginGraphiql/eyJfaWQiOiI0NTUifQ== | test id      | e-field-label-dataQuery |

    Scenario: As a developer I want to make sure that the rich text editor looks like as it used to on mobile
        Given the user opens the application on a mobile using the following link: "@sage/xtrem-show-case/RichText"
        Then the "Field - Rich text" titled page is displayed
        And the user waits 1 seconds
        Then element with test id "e-field-bind-field" looks as before

    Scenario: As a developer I want to make sure that the rich text editor looks like as it used to on desktop
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/RichText"
        Then the "Field - Rich text" titled page is displayed
        And the user waits 1 seconds
        Then element with test id "e-field-bind-field" looks as before

    Scenario: As a developer I want to make sure that the rich text editor looks like as it used to on desktop when is readOnly
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/RichText"
        Then the "Field - Rich text" titled page is displayed
        When the user selects the "isReadOnly" bound checkbox field on the main page
        And the user clicks in the checkbox field
        And the user waits 1 seconds
        Then element with test id "e-field-bind-field" looks as before

    Scenario: As a developer I want to make sure that destructive dropdown actions look like as before on desktop
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseInvoice/eyJfaWQiOiI1In0="
        Then the "Invoice 5 05/19/2020" titled page is displayed
        And the user waits 1 seconds
        Then element with css selector "[row-index='0'] [col-id='__actions']" looks as before

    Scenario: As a developer I want to make sure that destructive dropdown actions look like as before on mobile
        Given the user opens the application on a mobile using the following link: "@sage/xtrem-show-case/ShowCaseInvoice/eyJfaWQiOiI1In0="
        Then the "Invoice 5 05/19/2020" titled page is displayed
        And the user waits 1 seconds
        Then element with css selector ".e-table-field-mobile-rows .e-action-popover-mobile-button" looks as before

    Scenario: As a developer I want to make sure that default step sequence field look like as before on desktop
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/StepSequence"
        Then the "Field - Step Sequence" titled page is displayed
        And the user waits 1 seconds
        Then element with test id "e-field-bind-field" looks as before

    Scenario: As a developer I want to make sure that step sequence renders after value updates on desktop
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/StepSequence"
        Then the "Field - Step Sequence" titled page is displayed
        When the user clicks in the "next" bound button field on the main page
        And the user waits 1 seconds
        Then element with test id "e-field-bind-field" looks as before

    Scenario Outline: As a user I want single table fields in a section to use all available space in the container when <Status>
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/HeaderSection/<Record>"
        Then the "<Title>" titled page is displayed
        When selects the "Products" labelled navigation anchor on the main page
        And the user waits 2 seconds
        Then element with test id "e-field-bind-productSection" looks as before
        Examples:
            | Status       | Title                | Record           |
            | overflows    | Provider Ali Express | eyJfaWQiOiIxIn0= |
            | less content | Provider Decathlon   | eyJfaWQiOiIzIn0= |

    Scenario: As a user I want the section validation messages to be displayed in dialogs on desktop
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Dialogs"
        Then the "Dialogs" titled page is displayed
        When the user selects the "pageDialogIsRightAligned" bound checkbox field on the main page
        And the user clicks in the checkbox field
        And the user selects the "pageDialogPath" bound text field on the main page
        And the user writes "@sage/xtrem-show-case/TabSectionValidation" in the text field
        And the user selects the "pageDialogSize" bound select field on the main page
        And the user clicks in the select field
        And the user selects "medium" in the select field
        And the user clicks in the "pageDialogButton" bound button field on the main page
        And the user waits 2 seconds
        And the user clicks the "validate" bound business action button on the sidebar
        Then element with css selector "div[data-element='sidebar'][role='dialog']" looks as before

    Scenario: As user I want to make sure that the table paging panel looks like before
        XT-97899
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TableWithOptionsMenu/eyJfaWQiOiIyIn0="
        And the user selects the "field" bound table field on the main page
        Then the First page button of the table field is disabled
        Then the Last page button of the table field is disabled
        Then the Previous page button of the table field is disabled
        Then the Next page button of the table field is enabled
        Then the page number of the table field is "Page 1 of more"
        Then the summary row paging information of the table field is "1 to 20 of more"
        And element with css selector ".ag-paging-page-summary-panel" looks as before

    Scenario: As a user I want to make sure that the empty state of a table with a phantom row looks like before
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseInvoice/eyJfaWQiOiIxOSJ9"
        And the user waits 3 seconds
        Then element with test id "e-table-field-lines" looks as before

    Scenario: As a user I want to make sure that the empty state of a table with a disabled phantom row looks like before
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseInvoice/eyJfaWQiOiIxOSJ9"
        And the user selects the "isPhantomRowDisabled" bound checkbox field on the main page
        And the user clicks in the checkbox field
        Then a toast containing text "Disable/Enable phantom row was triggered" is displayed
        Then element with test id "e-table-field-lines" looks as before

    Scenario: As a user I want to make sure that the table card view with an icon looks like as before
        Given the user opens the application on a mobile using the following link: "@sage/xtrem-show-case/TableMobileCardViewWithIcon"
        And the user clicks in the "button" bound button field on the main page
        And the user waits 3 seconds
        Then element with css selector ".e-table-field-mobile-rows .e-card" looks as before

    Scenario: As a user I want to make sure that the table card view with an image field looks like as before
        Given the user opens the application on a mobile using the following link: "@sage/xtrem-show-case/TableCardView/eyJfaWQiOiIyIn0="
        And the user waits 1 seconds
        Then element with css selector ".e-table-field-mobile-rows .e-image-field-content-wrapper" looks as before

    Scenario: As a user I want to make sure that the table card view with reference fields lookup looks like as before
        Given the user opens the application on a mobile using the following link: "@sage/xtrem-show-case/ReferenceForAdcPoc"
        And the user selects the "field1" bound reference field on the main page
        Then the lookup button of the reference field is displayed
        When the user clicks the lookup button of the reference field
        And the user waits 1 seconds
        Then element with css selector ".e-table-field-mobile-rows .e-card" looks as before

    # Scenario: As a user I want to make sure that the table editable field borders looks like as before
    #     Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Table/eyJfaWQiOiIxIn0="
    #     When the user clicks on the "provider" labelled nested reference field of row 1 of the "field" bound table field in the main page
    #     Then element with test id "e-field-bind-field" looks as before

    @ClearDashboards
    Scenario: As a user I want the dashboard tabs to be positioned correctly
        Given the user opens the application on a desktop
        And the dashboard page is displayed
        Then the "Create a dashboard to get started." subtitled empty dashboard is displayed
        When the user clicks the create button on the dashboard
        # the creation dialog
        Then the dashboard creation dialog is displayed
        And the dashboard creation dialog description is "Select a template to get started or build your own dashboard. You can customize any dashboard by adding or removing widgets."
        And the "Blank template" template in the dashboard creation dialog is displayed
        And the "next" button in the dashboard creation dialog is disabled
        And the "cancel" button in the dashboard creation dialog is enabled
        When the user selects the template 1 in the dashboard creation dialog
        Then the template 1 in the dashboard creation dialog is selected
        And the "next" button in the dashboard creation dialog is enabled
        When the user clicks the "next" button in the dashboard creation dialog
        # edit the new dashboard
        Then the "New dashboard" titled dashboard in the dashboard editor is displayed
        # closing the dashboard editor
        When the user clicks the "cancel" button in the dashboard editor footer
        Then the "New dashboard" titled dashboard is displayed
        Then element with class "e-dashboard" looks as before

    Scenario: As a user I want the progress field to display by default as before
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Progress"
        And the user selects the "Title" labelled text field on the main page
        And the user writes "Test title" in the text field
        And the user selects the "Helper text" labelled text field on the main page
        And the user writes "Test helper text" in the text field
        When the user blurs the text field
        Then element with test id "e-field-bind-field" looks as before

    Scenario: As a user I want the progress field with custom progress to display labels as before
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Progress"
        And the user selects the "Max progress label" labelled text field on the main page
        And the user writes "MAX" in the text field
        And the user selects the "Current progress label" labelled text field on the main page
        And the user writes "CURRENT" in the text field
        When the user blurs the text field
        Then element with test id "e-field-bind-field" looks as before

    Scenario: As a user I want the progress field with hidden labels as before
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Progress"
        And the user selects the "Are progress labels hidden?" labelled checkbox field on the main page
        And the user ticks the checkbox field
        And the user selects the "Title" labelled text field on the main page
        And the user writes "Test title" in the text field
        And the user selects the "Helper text" labelled text field on the main page
        And the user writes "Test helper text" in the text field
        When the user blurs the text field
        Then element with test id "e-field-bind-field" looks as before

    Scenario: As a user I want the card field with a progress bar to look like as before
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/CardField/eyJfaWQiOiIxIn0="
        When selects the "Transient example" labelled navigation anchor on the main page
        Then element with test id "e-field-bind-progressBar" looks as before

    Scenario Outline: As a user I want the mobile table card field with a progress bar to look like as before on <Device>
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/TableMobileCardViewWithProgressBar"
        When the user clicks in the "button" bound button field on the main page
        Then element with css selector "[data-testid~='e-field-bind-field'] .e-table-field-mobile-rows [data-testid~='e-card']" looks as before
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: As a user I want the mobile table card field with a progress bar with hidden labels to look like as before on <Device>
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/TableMobileCardViewWithProgressBar"
        When the user clicks in the "button" bound button field on the main page
        Then element with css selector "[data-testid~='e-field-bind-hiddenLabelsField'] .e-table-field-mobile-rows [data-testid~='e-card']" looks as before
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario Outline: As a developer I want the mobile table header to display business action and text search input in card mode to look like as before on <Viewport>
        Given the user opens the application on a <Viewport> using the following link: "@sage/xtrem-show-case/TableMobileCardViewWithOptionMenuAndSearchBox"
        And the user waits 1 seconds
        Then element with css selector ".e-table-field-mobile .e-field-header-wrapper" looks as before
        Examples:
            | Viewport |
            | tablet   |
            | desktop  |

    Scenario: As a developer I want the mobile table header to display business action and text search input in card mode to look like as before on mobile
        Given the user opens the application on a mobile using the following link: "@sage/xtrem-show-case/TableMobileCardViewWithOptionMenuAndSearchBox"
        And the user waits 1 seconds
        Then element with css selector ".e-table-field-mobile .e-mobile-table-header" looks as before

    Scenario Outline: As a developer I want the table header's option menu types to look like as before on <Viewport>
        Given the user opens the application on a <Viewport> using the following link: "@sage/xtrem-show-case/TableOptionsMenu/eyJfaWQiOiIyIn0="
        Then the "Field - Table (Options Menu)" titled page is displayed
        When selects the "Dropdown" labelled navigation anchor on the main page
        Then element with css selector ".e-table-field[data-testid~='e-field-bind-dropdownField'] .e-field-header" looks as before
        When selects the "Tabs" labelled navigation anchor on the main page
        Then element with css selector ".e-table-field[data-testid~='e-field-bind-tabsField'] .e-field-header" looks as before
        When selects the "Toggle" labelled navigation anchor on the main page
        Then element with css selector ".e-table-field[data-testid~='e-field-bind-toggleField'] .e-field-header" looks as before
        When selects the "Dropdown (w/o Search)" labelled navigation anchor on the main page
        Then element with css selector ".e-table-field[data-testid~='e-field-bind-searchlessDropdownField'] .e-field-header" looks as before
        When selects the "Tabs (w/o Search)" labelled navigation anchor on the main page
        Then element with css selector ".e-table-field[data-testid~='e-field-bind-searchlessTabsField'] .e-field-header" looks as before
        Examples:
            | Viewport |
            | desktop  |
            | tablet   |
            | mobile   |

    # Scenario: As an ATP XTreeM User I can see the image of a reference field in the input
    #     XT-25838
    #     Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Reference"
    #     Then the "Field - Reference" titled page is displayed
    #     And the user selects the "Title" labelled text field on the main page
    #     When the user writes "Sample title" in the text field
    #     And the user selects the "field" bound reference field on the main page
    #     When the user writes "Tea - Decaf 1 Cup" in the reference field
    #     And the user selects "Tea - Decaf 1 Cup" in the reference field
    #     Then element with test id "e-field-bind-field" looks as before

    Scenario: As a user I want to toggle the header section and ensure that it looks like as before
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/HeaderSection/eyJfaWQiOiIyIn0="
        Then the "Provider Amazon" titled page is displayed
        When selects the "Products" labelled navigation anchor on the main page
        Then element with test id "e-field-bind-products" looks as before
        When the user selects the header section toggle button in the header
        Then element with test id "e-field-bind-products" looks as before
        When the user selects the header section toggle button in the header
        Then element with test id "e-field-bind-products" looks as before

    Scenario Outline:  As a user I want the header row to look like as before on <Device>
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/HeaderSection/eyJfaWQiOiIyIn0="
        Then the "Provider Amazon" titled page is displayed
        Then element with class "e-header-title-row" looks as before
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario: As a user I want see the dialogs enforcing a fixed height
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-reporting/ReportTemplate/eyJfaWQiOiIzIn0="
        Then the "Report template usersByType" titled page is displayed
        When the user clicks the "Preview" labelled business action button on the main page
        And the user waits 10 seconds
        Then an info dialog appears on the main page
        And the text in the header of the dialog is "Document preview"
        Then element with test id "e-dialog-body" looks as before

    # BL: Flaky test scenario
    # Scenario: As a user I want see tunnel dialogs enforcing a fixed height
    #     Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/StandardShowCaseProduct/413"
    #     Then the "Product 413" titled page is displayed
    #     When the user selects the "Provider" labelled reference field on the main page
    #     Then the reference field tunnel link is displayed
    #     When the user clicks the reference field tunnel link
    #     Then an info dialog appears on a full width modal
    #     When the user waits 2 seconds
    #     Then the dialog title is "Provider 2" on a full width modal
    #     And selects the "Products" labelled navigation anchor on a full width modal
    #     And the user selects the "products" bound table field on a full width modal
    #     Then element with test id "e-table-field-products" looks as before

    Scenario: As a user I want see the navigation panel in a full width dialog if the corresponding config property is set
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Dialogs"
        And the "Dialogs" titled page is displayed
        And selects the "Page dialog" labelled navigation anchor on the main page
        And the user waits 2 seconds
        And the user selects the "Path" labelled text field on the main page
        When the user writes "@sage/xtrem-show-case/ShowCaseProduct" in the text field
        And the user selects the "Render main list" labelled checkbox field on the main page
        When the user ticks the checkbox field
        And the user selects the "pageDialogIsFullscreen" bound checkbox field on the main page
        When the user ticks the checkbox field
        When the user clicks in the "pageDialogButton" bound button field on the main page
        And the dialog title is "Products"
        When the user selects the "$navigationPanel" bound table field on a full width modal
        And the user selects the row 1 of the table field
        Then the value of the "product" bound nested text field of the selected row in the table field is "Anisette - Mcguiness"
        Then element with css selector ".e-dialog-body .e-table-field-desktop-wrapper" looks as before

    Scenario: As a user I want see autocomplete on text fields
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Text"
        And the "Text" titled page is displayed
        And the user selects the "Text Field with autocomplete" labelled text field on the main page
        When the user writes "pur" in the text field
        Then element with test id "e-field-label-textFieldWithAutocomplete" looks as before

    Scenario: As a user I want to see if Filter Editor looks like before
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/FilterEditor"
        And the "Filter Editor" titled page is displayed
        When the user selects the "field" bound filter editor field on the main page
        And the user clicks the "Add filter" button of the filter editor field
        Then element with test id "e-filter-editor-field-wrapper" looks as before

    Scenario: As a user I want to see if Filter Editor looks like before
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/FilterEditor"
        And the "Filter Editor" titled page is displayed
        Then the user selects the "podMode" bound checkbox field on the main page
        And the user ticks the checkbox field
        When the user selects the "field" bound filter editor field on the main page
        And the user clicks the "Add filter" button of the filter editor field
        Then element with test id "e-filter-editor-field-wrapper" looks as before

    Scenario: As a user I want to see multiple cards in a single row on the mobile card view
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TableMobileCardViewWithMultiCardLines"
        Then the "Mobile card view with multi card lines" titled page is displayed
        And the user selects the "field" bound table field on the main page
        Then the table field is empty
        When the user clicks the "Load" labelled business action button of the table field
        Then element with test id "e-table-field-mobile-rows" looks as before

    Scenario: As a user I want to see nested grid with infinite scroll
        XT-89791
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-show-case/NestedGridInfiniteScroll/eyJfaWQiOiIxNTkifQ=="
        Then the "Field - NestedGrid Infinite Scroll" titled page is displayed
        When the user selects the "field" bound nested grid field on the main page
        And the user clicks the "ID" labelled column of the nested grid field
        And the user selects row with text "31" in column with header "Id" in the nested grid field
        And the user expands the selected row of the nested grid field
        Then element with test id "e-page-body" looks as before
        When the user scrolls down to the row with text "420" and column header "Id" in the nested grid field
        Then element with test id "e-page-body" looks as before

    Scenario Outline: As a user I want to check time field for locale <Language>
        XT-67003
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-show-case/Time"
        When the user switches language to <Language>
        Then the "Time" titled page is displayed

        When the user selects the "field" bound time field on the main page
        And the user writes <Time> in the time field
        Then element with test id "e-time-field" looks as before

        Examples:
            | Language     | Time    |
            | "English US" | "11:59" |
            | "English GB" | "23:59" |
            | "Spanish"    | "23:59" |
            | "French"     | "23:59" |
            | "German"     | "23:59" |
            | "Polish"     | "23:59" |
            | "Portuguese" | "23:59" |
            | "Chinese"    | "23:59" |

    Scenario: As a user I want row actions to look the same as before - view mode
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/RowActions"
        Then the "Row actions" titled page is displayed
        And the user selects the "table91" bound table field on the main page
        And the user selects the row 1 of the table field
        Then the value of the "Readonly" labelled nested text field of the selected row in the table field is "a"
        Then element with css selector "[data-testid~='e-field-bind-table91'] [row-index='0'] [col-id='__actions']" looks as before
        And the user selects the "table112" bound table field on the main page
        Then the value of the "Readonly" labelled nested text field of the selected row in the table field is "a"
        And element with css selector "[data-testid~='e-field-bind-table112'] [row-index='0'] [col-id='__actions']" looks as before
        And the user selects the "table16" bound table field on the main page
        Then the value of the "Readonly" labelled nested text field of the selected row in the table field is "a"
        And element with css selector "[data-testid~='e-field-bind-table16'] [row-index='0'] [col-id='__actions']" looks as before

    Scenario: As a user I want row actions to look the same as before - edit mode
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/RowActions"
        Then the "Row actions" titled page is displayed
        And the user selects the "table91" bound table field on the main page
        And the user selects the row 1 of the table field
        Then the value of the "Readonly" labelled nested text field of the selected row in the table field is "a"
        When the user clicks the "Optional" labelled nested field of the selected row in the table field
        And the user presses Tab
        Then the element with the following selector is focused: ".ag-popup .e-table-field-actions-container"
        Then element with css selector ".ag-popup .e-table-field-actions-container" looks as before
        And the user presses Escape
        And the user selects the "table112" bound table field on the main page
        Then the value of the "Readonly" labelled nested text field of the selected row in the table field is "a"
        When the user clicks the "Optional" labelled nested field of the selected row in the table field
        And the user presses Tab
        Then the element with the following selector is focused: ".ag-popup .e-table-field-actions-container"
        Then element with css selector ".ag-popup .e-table-field-actions-container" looks as before

    Scenario Outline: As a user I want a page dialog's header section to be rendered between the tabs and page content when the page is rendered on <Device>
        XT-92407
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Dialogs"
        When selects the "Page dialog" labelled navigation anchor on the main page
        And the user selects the "Path" labelled text field on the main page
        And the user writes "@sage/xtrem-show-case/StandardShowCaseInvoice" in the text field
        And the user selects the "pageDialogQueryParameterName" bound text field on the main page
        And the user writes "_id" in the text field
        And the user selects the "pageDialogQueryParameterValue" bound text field on the main page
        And the user writes "11" in the text field
        And the user clicks in the "pageDialogAddParameter" bound button field on the main page
        And the user clicks in the "pageDialogButton" bound button field on the main page
        Then the dialog title is "Invoice 11"
        And element with css selector "[role='dialog']" looks as before
        Examples:
            | Device |
            | tablet |
            | mobile |

    Scenario Outline: As a user I want a page dialog's header section to be rendered between the tabs and page content when the page is rendered fullscreen on <Device>
        XT-92407
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/Dialogs"
        When selects the "Page dialog" labelled navigation anchor on the main page
        And the user selects the "pageDialogIsFullscreen" bound checkbox field on the main page
        And the user ticks the checkbox field
        And the user selects the "Path" labelled text field on the main page
        And the user writes "@sage/xtrem-show-case/StandardShowCaseInvoice" in the text field
        And the user selects the "pageDialogQueryParameterName" bound text field on the main page
        And the user writes "_id" in the text field
        And the user selects the "pageDialogQueryParameterValue" bound text field on the main page
        And the user writes "11" in the text field
        And the user clicks in the "pageDialogAddParameter" bound button field on the main page
        And the user clicks in the "pageDialogButton" bound button field on the main page
        Then the dialog title is "Invoice 11"
        And element with css selector "[role='dialog']" looks as before
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

    Scenario: As a user I want a page dialog's header section to be rendered as a tab in the page when the page is rendered on desktop
        XT-92407
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Dialogs"
        When selects the "Page dialog" labelled navigation anchor on the main page
        And the user selects the "Path" labelled text field on the main page
        And the user writes "@sage/xtrem-show-case/StandardShowCaseInvoice" in the text field
        And the user selects the "pageDialogQueryParameterName" bound text field on the main page
        And the user writes "_id" in the text field
        And the user selects the "pageDialogQueryParameterValue" bound text field on the main page
        And the user writes "11" in the text field
        And the user clicks in the "pageDialogAddParameter" bound button field on the main page
        And the user clicks in the "pageDialogButton" bound button field on the main page
        Then the dialog title is "Invoice 11"
        And element with css selector "[role='dialog']" looks as before

    Scenario: As a user I want date time range field and picker to look the same as before
        XT-81904
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-show-case/DatetimeRange"
        Then the "Date-time Range" titled page is displayed

        # start date-time-range field with date
        When the user selects the "field" bound date-time-range field on the main page
        And the user selects the "April" month of the start date-time-range field
        And the user selects the "2025" year of the start date-time-range field
        And the user selects the "14" day in the start date-time-range field
        And the user writes "12:15" in time field of the start date-time-range field
        Then element with class "e-datetime-range-container" looks as before
        And element with css selector "#--DatetimeRange-bind-field-start" looks as before

        # end date-time-range field with date
        When the user selects the "April" month of the end date-time-range field
        And the user selects the "2025" year of the end date-time-range field
        And the user selects the "18" day in the end date-time-range field
        And the user writes "07:30" in time field of the end date-time-range field
        Then element with class "e-datetime-range-container" looks as before
        Then element with css selector "#--DatetimeRange-bind-field-end" looks as before

        # start date-time-range field without date
        And the user ticks the "No start date" checkbox of the start date-time-range field
        Then element with class "e-datetime-range-container" looks as before
        And element with css selector "#--DatetimeRange-bind-field-start" looks as before

        # end date-time-range field without date
        And the user ticks the "No end date" checkbox of the end date-time-range field
        Then element with class "e-datetime-range-container" looks as before
        And element with css selector "#--DatetimeRange-bind-field-end" looks as before
