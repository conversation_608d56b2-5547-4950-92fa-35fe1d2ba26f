Feature: 2-1 Navigation
    # Tests navigation functionality across the application, including section anchors, routing, and page refresh capabilities

    # TODO Create an Scrollable page and use it for this test in all the breakpoints

    Scenario: As a user I want to scroll to sections by using the navigation anchors
        Given the user opens the application on a mobile using the following link: "@sage/xtrem-show-case/Alignment"
        When selects the "Another Section" labelled navigation anchor on the main page
        Then the "Another Section" labelled navigation anchor is selected

    <PERSON><PERSON><PERSON>: As a user I want to restore the page to its original value using the router.refresh function
        XT-658
        Given the user opens the application on a mobile using the following link: "@sage/xtrem-show-case/ShowCaseProduct/eyJfaWQiOiI0OTMifQ=="
        And the user selects the "Product" labelled text field on the main page
        When the user writes "My test value" in the text field
        Then the value of the text field is "My test value"
        And the user selects the "Category Select" labelled radio field on the main page
        When the user selects the value "awful" in the radio field
        Then the value "awful" of the radio field is selected
        And the user selects the "List Price" labelled numeric field on the main page
        When the user writes "123.45" in the numeric field
        Then the value of the numeric field is "123.45"
        When the user clicks the "Refresh" labelled business action button on the main page
        Then a warn dialog appears on a full width modal
        And the text in the body of the dialog is "Leave and discard your changes?"
        When the user clicks the "Discard" button of the Confirm dialog
        And the user selects the "Product" labelled text field on the main page
        Then the value of the text field is "Coconut - Whole"
        And the user selects the "List Price" labelled numeric field on the main page
        Then the value of the numeric field is "58.52"
        Then the value "good" of the radio field is not selected

    Scenario: As a user I want to preserve the current values if I have unsaved changes and click no on the prompt dialog
        XT-658
        Given the user opens the application on a mobile using the following link: "@sage/xtrem-show-case/ShowCaseProduct/eyJfaWQiOiI0OTMifQ=="
        And the user selects the "Product" labelled text field on the main page
        When the user writes "My test value" in the text field
        Then the value of the text field is "My test value"
        And the user selects the "Category Select" labelled radio field on the main page
        When the user selects the value "awful" in the radio field
        Then the value "awful" of the radio field is selected
        And the user selects the "List Price" labelled numeric field on the main page
        When the user writes "123.45" in the numeric field
        Then the value of the numeric field is "123.45"
        When the user clicks the "Refresh" labelled business action button on the main page
        Then a warn dialog appears on a full width modal
        And the text in the body of the dialog is "Leave and discard your changes?"
        When the user clicks the "Go back" button of the Confirm dialog
        And the user selects the "Product" labelled text field on the main page
        Then the value of the text field is "My test value"
        And the user selects the "List Price" labelled numeric field on the main page
        Then the value of the numeric field is "123.45"
        And the user selects the "Category Select" labelled radio field on the main page
        Then the value "awful" of the radio field is selected

    Scenario: As a user I want to preserve the current values if I have unsaved changes and dismiss the prompt dialog
        XT-658
        Given the user opens the application on a mobile using the following link: "@sage/xtrem-show-case/ShowCaseProduct/eyJfaWQiOiI0OTMifQ=="
        And the user selects the "Product" labelled text field on the main page
        When the user writes "My test value" in the text field
        Then the value of the text field is "My test value"
        And the user selects the "Category Select" labelled radio field on the main page
        When the user selects the value "awful" in the radio field
        Then the value "awful" of the radio field is selected
        And the user selects the "List Price" labelled numeric field on the main page
        When the user writes "123.45" in the numeric field
        Then the value of the numeric field is "123.45"
        When the user clicks the "Refresh" labelled business action button on the main page
        Then a warn dialog appears on a full width modal
        And the text in the body of the dialog is "Leave and discard your changes?"
        And the user clicks the Close button of the dialog on the main page
        And the user selects the "Product" labelled text field on the main page
        Then the value of the text field is "My test value"
        And the user selects the "List Price" labelled numeric field on the main page
        Then the value of the numeric field is "123.45"
        And the user selects the "Category Select" labelled radio field on the main page
        Then the value "awful" of the radio field is selected

    Scenario: As a user I want to restore the page to its original value using the router.emptyPage function
        Given the user opens the application on a mobile using the following link: "@sage/xtrem-show-case/ShowCaseProduct/eyJfaWQiOiI0OTMifQ=="
        And the user selects the "Product" labelled text field on the main page
        Then the value of the text field is "Coconut - Whole"
        And the user selects the "List Price" labelled numeric field on the main page
        Then the value of the numeric field is "58.52"
        And the user selects the "Category Select" labelled radio field on the main page
        Then the value "good" of the radio field is not selected
        When the user clicks the "Empty page" labelled business action button on the main page
        And the user selects the "List Price" labelled numeric field on the main page
        Then the value of the numeric field is "0.00"
        And the user selects the "Product" labelled text field on the main page
        Then the value of the text field is ""
        And the user selects the "Category Select" labelled radio field on the main page
        Then the value "good" of the radio field is not selected

    Scenario: As a user I want to navigate by the navigation panel
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseProduct/eyJfaWQiOiIxMDYifQ=="
        Then the user opens the navigation panel
        And the user selects the "Description" labelled text field on the main page
        Then the value of the text field is "complexity"
        When the user clicks the record with the text "Appetiser - Bought" in the navigation panel
        And the user selects the "Description" labelled text field on the main page
        Then the value of the text field is "frame"
        And the user selects the "Provider" labelled reference field on the main page
        Then the value of the reference field is "Amazon"
        When the user clicks the record with the text "Appetizer - Southwestern" in the navigation panel
        And the user selects the "Description" labelled text field on the main page
        Then the value of the text field is "Inverse"
        And the user selects the "Provider" labelled reference field on the main page
        Then the value of the reference field is "Ali Express"
        When the user clicks the record with the text "Apple - Macintosh" in the navigation panel
        And the user selects the "Description" labelled text field on the main page
        Then the value of the text field is "Horizontal"
        And the user selects the "Provider" labelled reference field on the main page
        Then the value of the reference field is "Amazon"

    Scenario: As a user I want to search on the navigation panel
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseProduct/eyJfaWQiOiIxMDYifQ=="
        Then the user opens the navigation panel
        And the user selects the "Description" labelled text field on the main page
        Then the value of the text field is "complexity"
        Then the user searches for "beef" in the navigation panel
        When the user clicks the record with the text "Beef - Chuck, Boneless" in the navigation panel
        And the user selects the "Description" labelled text field on the main page
        Then the value of the text field is "Universal"
        When the user clicks the record with the text "Beef - Tender Tips" in the navigation panel
        And the user selects the "Description" labelled text field on the main page
        Then the value of the text field is "interactive"

    Scenario: As a user I want to be prompted if I navigate by the navigation panel and have dirty records
        XT-658
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseProduct/eyJfaWQiOiIxMDYifQ=="
        Then the user opens the navigation panel
        And the user selects the "Description" labelled text field on the main page
        Then the value of the text field is "complexity"
        When the user writes "My test value" in the text field
        And the user searches for "Dried Apple" in the navigation panel
        When the user clicks the record with the text "Dried Apple" in the navigation panel
        Then a warn dialog appears on the main page
        Then the text in the body of the dialog is "Leave and discard your changes?"
        When the user clicks the "Discard" button of the Confirm dialog
        And the user selects the "Description" labelled text field on the main page
        Then the value of the text field is "Up-sized"
        And the user selects the "Product" labelled text field on the main page
        Then the value of the text field is "Dried Apple"

    Scenario: As a user I want to be prompted if I navigate by the navigation panel and have dirty records and stop the navigation event
        XT-658
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseProduct/eyJfaWQiOiIxMDYifQ=="
        Then the user opens the navigation panel
        And the user selects the "Description" labelled text field on the main page
        Then the value of the text field is "complexity"
        When the user writes "My test value" in the text field
        And the user searches for "Dried Apple" in the navigation panel
        When the user clicks the record with the text "Dried Apple" in the navigation panel
        Then a warn dialog appears on the main page
        Then the text in the body of the dialog is "Leave and discard your changes?"
        When the user clicks the "Go back" button of the Confirm dialog
        And the user selects the "Description" labelled text field on the main page
        Then the value of the text field is "My test value"

    Scenario: As a functional developer I want the first record not to have previous record indication
        X3-245952 & X3-245791
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/NavigationPanelComplexCard/eyJfaWQiOiIxMDYifQ=="
        Then the user opens the navigation panel
        And the user selects the "hasPreviousRecord" labelled checkbox field on the main page
        Then the value of the checkbox field is "false"
        And the user selects the "hasNextRecord" labelled checkbox field on the main page
        Then the value of the checkbox field is "true"
        When the user clicks the "Next Item" labelled business action button on the main page
        And the user selects the "hasPreviousRecord" labelled checkbox field on the main page
        Then the value of the checkbox field is "true"
        And the user selects the "hasNextRecord" labelled checkbox field on the main page
        Then the value of the checkbox field is "true"
        When the user clicks the "Previous Item" labelled business action button on the main page
        And the user selects the "hasPreviousRecord" labelled checkbox field on the main page
        Then the value of the checkbox field is "false"
        And the user selects the "hasNextRecord" labelled checkbox field on the main page
        Then the value of the checkbox field is "true"

    Scenario: As a functional developer I want the next record router action to work across navigation panel pages
        X3-245952 & X3-245791
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/NavigationPanelComplexCard/eyJfaWQiOiI3NyJ9"
        And the user selects the "hasPreviousRecord" labelled checkbox field on the main page
        Then the value of the checkbox field is "true"
        And the user selects the "hasNextRecord" labelled checkbox field on the main page
        Then the value of the checkbox field is "true"
        And the user selects the "Product" labelled text field on the main page
        Then the value of the text field is "Beans - Black Bean, Dry"
        And the user selects the "Id" labelled text field on the main page
        Then the value of the text field is "77"
        When the user clicks the "Next Item" labelled business action button on the main page
        And the user selects the "hasPreviousRecord" labelled checkbox field on the main page
        Then the value of the checkbox field is "true"
        And the user selects the "hasNextRecord" labelled checkbox field on the main page
        Then the value of the checkbox field is "true"
        And the user selects the "Product" labelled text field on the main page
        Then the value of the text field is "Beans - Butter Lrg Lima"
        And the user selects the "Id" labelled text field on the main page
        Then the value of the text field is "103"
        When the user clicks the "Next Item" labelled business action button on the main page
        And the user selects the "hasPreviousRecord" labelled checkbox field on the main page
        Then the value of the checkbox field is "true"
        And the user selects the "hasNextRecord" labelled checkbox field on the main page
        Then the value of the checkbox field is "true"
        And the user selects the "Product" labelled text field on the main page
        Then the value of the text field is "Beans - Butter Lrg Lima"
        And the user selects the "Id" labelled text field on the main page
        Then the value of the text field is "196"
        When the user clicks the "Next Item" labelled business action button on the main page

    Scenario: As a functional developer I want the next record router action to work at the end of the selection
        X3-245952 & X3-245791
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/NavigationPanelComplexCard/eyJfaWQiOiIzNjIifQ=="
        And the user selects the "hasPreviousRecord" labelled checkbox field on the main page
        Then the value of the checkbox field is "true"
        And the user selects the "hasNextRecord" labelled checkbox field on the main page
        Then the value of the checkbox field is "true"
        And the user selects the "Product" labelled text field on the main page
        Then the value of the text field is "Yucca"
        And the user selects the "Id" labelled text field on the main page
        Then the value of the text field is "362"
        When the user clicks the "Next Item" labelled business action button on the main page
        And the user selects the "hasPreviousRecord" labelled checkbox field on the main page
        Then the value of the checkbox field is "true"
        And the user selects the "hasNextRecord" labelled checkbox field on the main page
        Then the value of the checkbox field is "false"
        And the user selects the "Product" labelled text field on the main page
        Then the value of the text field is "Yukon Jack"
        And the user selects the "Id" labelled text field on the main page
        Then the value of the text field is "34"
        When the user clicks the "Previous Item" labelled business action button on the main page
        And the user selects the "hasPreviousRecord" labelled checkbox field on the main page
        Then the value of the checkbox field is "true"
        And the user selects the "hasNextRecord" labelled checkbox field on the main page
        Then the value of the checkbox field is "true"

    Scenario: As a user I want the record opened if there is only one matching record in the search
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/NavigationPanelComplexCard/eyJfaWQiOiIxMDYifQ=="
        Then the user opens the navigation panel
        Then the user searches for "Bread - Onion Focaccia" in the navigation panel
        And the user selects the "Id" labelled text field on the main page
        Then the value of the text field is "289"
        And the user selects the "Product" labelled text field on the main page
        Then the value of the text field is "Bread - Onion Focaccia"

    Scenario Outline: <Device> - As a user I want to verify the value of the nested field of the navigation panel row
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/NavigationPanelComplexCard/eyJfaWQiOiIxMDYifQ=="
        Then the user opens the navigation panel
        # And the user waits 2 seconds
        When the user searches for "Appetizer - Cheese Bites" in the navigation panel
        Then the value of the "product" bound nested text field on the "1" navigation panel's row is "Appetizer - Cheese Bites"
        Then the value of the "provider" bound nested reference field on the "1" navigation panel's row is "Ali Express"
        Then the value of the "releaseDate" bound nested text field on the "1" navigation panel's row is "2019-11-30"
        Then the value of the "category" bound nested label field on the "1" navigation panel's row is "Good"
        Examples:
            | Device  |
            | desktop |
            | tablet  |

    Scenario: mobile - As a user I want to verify the value of the nested field of the navigation panel row
        Given the user opens the application on a mobile using the following link: "@sage/xtrem-show-case/NavigationPanelComplexCard/eyJfaWQiOiIxMDYifQ=="
        Then the user opens the navigation panel
        # And the user waits 4 seconds
        When the user searches for "Appetizer - Cheese Bites" in the navigation panel
        Then the value of the "product" bound nested text field on the "1" navigation panel's row is "Appetizer - Cheese Bites"
        Then the value of the "provider" bound nested reference field on the "1" navigation panel's row is "Ali Express"
        Then the value of the "releaseDate" bound nested text field on the "1" navigation panel's row is "2019-11-30"
        Then the value of the "category" bound nested label field on the "1" navigation panel's row is "Good"

    Scenario: As a developer I want to select the first item from the navigation panel using application code
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/NavigationPanel"
        And the user selects the "Id" labelled text field on the main page
        Then the value of the text field is "106"

    Scenario Outline: As a user I want to search on the navigation panel using ISO date with <Language> locale
        Given the user opens the application on a desktop
        When the user switches language to <Language>
        Then the user navigates to the following link: "@sage/xtrem-show-case/NavigationPanelComplexCard/eyJfaWQiOiIxMDYifQ=="
        Then the user opens the navigation panel
        Then the user searches for "2020-12-02" in the navigation panel
        And the user clicks the record with the text "Sansho Powder" in the navigation panel
        And the user selects the "Product" labelled text field on the main page
        Then the value of the text field is "Sansho Powder"
        Examples:
            | Language     |
            | "English US" |
            | "Spanish"    |
            | "French"     |
            | "German"     |

    Scenario Outline: As a user I want to search on the navigation panel using localized date with <Language> locale
        Given the user opens the application on a desktop
        When the user switches language to <Language>
        Then the user navigates to the following link: "@sage/xtrem-show-case/NavigationPanelComplexCard/eyJfaWQiOiIxMDYifQ=="
        Then the user opens the navigation panel
        Then the user searches for "<Date>" in the navigation panel
        And the user clicks the record with the text "Sansho Powder" in the navigation panel
        And the user selects the "Product" labelled text field on the main page
        Then the value of the text field is "Sansho Powder"
        Examples:
            | Language     | Date      |
            | "English US" | 12/2/2020 |
            | "Spanish"    | 2/12/2020 |
            | "French"     | 2/12/2020 |
            | "German"     | 2.12.2020 |

    Scenario: As a user I want to filter on the full-width navigation panel
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseProduct"
        And the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row 1 of the table field
        Then the value of the "product" bound nested text field of the selected row in the table field is "Anisette - Mcguiness"
        And the user filters the "product" bound column in the table field with value "apple"
        Then the value of the "product" bound nested text field of the selected row in the table field is "Apple - Macintosh"

    Scenario: As a user I want to sort the full-width navigation panel
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseProduct"
        And the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row 1 of the table field
        Then the value of the "product" bound nested text field of the selected row in the table field is "Anisette - Mcguiness"
        And the user clicks the "product" bound column of the table field
        Then  the value of the "product" bound nested text field of the selected row in the table field is "Yukon Jack"

    Scenario: As a user I want to combine the navigation panel top search and the free text search
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/NavigationPanelComplexCard/eyJfaWQiOiIxMDYifQ=="
        Then the user opens the navigation panel
        And the user searches for "Decathlon" in the navigation panel
        And the user clicks the record with the text "bicycle" in the navigation panel
        And the user clicks the "Only from Ali" toggle button in the navigation panel
        And the user searches for "Decathlon" in the navigation panel
        Then the navigation panel is empty

    Scenario: As a user I want to step through a filtered navigation panel and make sure that the filter is applied to additionally fetch pages
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/NavigationPanelComplexCard/eyJfaWQiOiI1MyJ9"
        And the user opens the navigation panel
        When the user clicks the "Only from Ali" toggle button in the navigation panel
        And the user selects the "Product" labelled text field on the main page
        Then the value of the text field is "Appetizer - Southwestern"
        And the user selects the "Provider" labelled reference field on the main page
        Then the value of the reference field is "Ali Express"
        When the user clicks the "Next Item" labelled business action button on the main page
        And the user selects the "Product" labelled text field on the main page
        Then the value of the text field is "Apple - Royal Gala"
        And the user selects the "Provider" labelled reference field on the main page
        Then the value of the reference field is "Ali Express"
        When the user clicks the "Next Item" labelled business action button on the main page
        And the user selects the "Product" labelled text field on the main page
        Then the value of the text field is "Arizona - Plum Green Tea"
        And the user selects the "Provider" labelled reference field on the main page
        Then the value of the reference field is "Ali Express"
        When the user clicks the "Next Item" labelled business action button on the main page
        And the user selects the "Product" labelled text field on the main page
        Then the value of the text field is "Asparagus - Frozen"
        And the user selects the "Provider" labelled reference field on the main page
        Then the value of the reference field is "Ali Express"
        When the user clicks the "Next Item" labelled business action button on the main page
        And the user selects the "Product" labelled text field on the main page
        Then the value of the text field is "Bacardi Raspberry"
        And the user selects the "Provider" labelled reference field on the main page
        Then the value of the reference field is "Ali Express"
        When the user clicks the "Next Item" labelled business action button on the main page
        And the user selects the "Product" labelled text field on the main page
        Then the value of the text field is "Bacardi Raspberry"
        And the user selects the "Provider" labelled reference field on the main page
        Then the value of the reference field is "Ali Express"
        When the user clicks the "Next Item" labelled business action button on the main page
        And the user selects the "Product" labelled text field on the main page
        Then the value of the text field is "Bag Stand"
        And the user selects the "Provider" labelled reference field on the main page
        Then the value of the reference field is "Ali Express"
        When the user clicks the "Next Item" labelled business action button on the main page
        And the user selects the "Product" labelled text field on the main page
        Then the value of the text field is "Barramundi"
        And the user selects the "Provider" labelled reference field on the main page
        Then the value of the reference field is "Ali Express"
        When the user clicks the "Next Item" labelled business action button on the main page
        And the user selects the "Product" labelled text field on the main page
        Then the value of the text field is "Beans - Black Bean, Dry"
        And the user selects the "Provider" labelled reference field on the main page
        Then the value of the reference field is "Ali Express"
        When the user clicks the "Next Item" labelled business action button on the main page
        And the user selects the "Product" labelled text field on the main page
        Then the value of the text field is "Beans - Butter Lrg Lima"
        And the user selects the "Provider" labelled reference field on the main page
        Then the value of the reference field is "Ali Express"
        When the user clicks the "Next Item" labelled business action button on the main page
        And the user selects the "Product" labelled text field on the main page
        Then the value of the text field is "Beans - Butter Lrg Lima"
        And the user selects the "Provider" labelled reference field on the main page
        Then the value of the reference field is "Ali Express"
        When the user clicks the "Next Item" labelled business action button on the main page
        And the user selects the "Product" labelled text field on the main page
        Then the value of the text field is "Beans - French"
        And the user selects the "Provider" labelled reference field on the main page
        Then the value of the reference field is "Ali Express"
        When the user clicks the "Next Item" labelled business action button on the main page
        And the user selects the "Product" labelled text field on the main page
        Then the value of the text field is "Beef - Chuck, Boneless"
        And the user selects the "Provider" labelled reference field on the main page
        Then the value of the reference field is "Ali Express"
        When the user clicks the "Next Item" labelled business action button on the main page
        And the user selects the "Product" labelled text field on the main page
        Then the value of the text field is "Beef - Ground Lean Fresh"
        And the user selects the "Provider" labelled reference field on the main page
        Then the value of the reference field is "Ali Express"
        When the user clicks the "Next Item" labelled business action button on the main page
        And the user selects the "Product" labelled text field on the main page
        Then the value of the text field is "Beef - Tender Tips"
        And the user selects the "Provider" labelled reference field on the main page
        Then the value of the reference field is "Ali Express"
        When the user clicks the "Next Item" labelled business action button on the main page
        And the user selects the "Product" labelled text field on the main page
        Then the value of the text field is "Beer - Alexander Kieths, Pale Ale"
        And the user selects the "Provider" labelled reference field on the main page
        Then the value of the reference field is "Ali Express"
        When the user clicks the "Next Item" labelled business action button on the main page
        And the user selects the "Product" labelled text field on the main page
        Then the value of the text field is "Beer - true North Strong Ale"
        And the user selects the "Provider" labelled reference field on the main page
        Then the value of the reference field is "Ali Express"
        When the user clicks the "Next Item" labelled business action button on the main page


    Scenario Outline: <Device>: As a user I can select a record in the navigation panel full width and then close the main page
        XT-27815
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/NavigationPanelComplexCard"
        Then the "Navigation Panel - Complex Card" titled page is displayed

        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        When the user clicks the "product" bound nested field of the selected row in the table field

        Then the "Navigation panel examples" subtitled page is displayed
        And the user selects the "Product" labelled text field on the main page
        Then the value of the text field is "Anisette - Mcguiness"

        And the user clicks the "Close record" icon in the header on the main page

        Then the "Navigation Panel - Complex Card" titled page is displayed

        Examples:
            | Device  |
            | desktop |

    Scenario Outline: <Device>: As a user I can select a record in the navigation panel full width and then close the main page
        XT-27815
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/NavigationPanelComplexCard"
        Then the "Navigation Panel - Complex Card" titled page is displayed

        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user clicks the card 1 in the table field

        Then the "Navigation panel examples" subtitled page is displayed
        And the user selects the "Product" labelled text field on the main page
        Then the value of the text field is "Anisette - Mcguiness"

        And the user clicks the "Close record" icon in the header on the main page

        Examples:
            | Device |
            | tablet |

    Scenario: As a user I want the floating filter value to remain when I close the record detail
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseProduct"
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        When the user filters the "product" bound column in the table field with value "Anisette - Mcguiness"
        And the user selects the row 1 of the table field
        And the user clicks the "Product" labelled nested field of the selected row in the table field
        And the user clicks the "Close record" icon in the header on the main page
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        Then the floating filter value of the "Product" labelled column of the table field is "Anisette - Mcguiness"
        And the user selects the row 1 of the table field
        Then the value of the "Product" labelled nested label field of the selected row in the table field is "Anisette - Mcguiness"
        Then the value of the "ID" labelled nested text field of the selected row in the table field is "106"

    Scenario: orderBy decorator in navigation panel (full-width view)
        XT-32889
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/NavigationPanelCustomSorting"
        And the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row 1 of the table field
        Then the value of the "product" bound nested text field of the selected row in the table field is "Dried Cranberries"
        Then the value of the "releaseDate" bound nested text field of the selected row in the table field is "2021-01-30"

    Scenario: orderBy decorator in navigation panel (split view)
        XT-32889
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/NavigationPanelCustomSorting/eyJfaWQiOiI1MDIifQ=="
        When the user opens the navigation panel
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        Then  the value of the "product" bound nested text field of the card 1 in the table field is "Dried Cranberries"
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        Then  the value of the "product" bound nested text field of the card 2 in the table field is "bicycle"

    ####Flacky : XT-66577 - bug to fix
    # Scenario: As a user I want to be able to sort reference group columns alphabetically
    #     XT-38306
    #     Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseInvoice"
    #     And the user selects the "$navigationPanel" bound table field on the main page
    #     Then the value of the "customer__name" bound nested reference field of row 1 in the table field is "Schinner; Kling and Toy"
    #     When the user clicks the group by option in the header menu of the "customer__name" bound column of the table field
    #     # items are displayed in ascending order
    #     Then the value of the "customer__name" bound nested reference field of row 1 in the table field is "No value (23)"
    #     Then  the value of the "customer__name" bound nested reference field of row 2 in the table field is "Abshire; Gislason and Ortiz (1)"
    #     Then  the value of the "customer__name" bound nested reference field of row 21 in the table field is "Botsford-Mertz (5)"
    #     # test group pagination by loading the second page of groups
    #     When the user clicks the "customer__name" bound nested field of row 20 in the table field
    #     And the user presses ArrowDown
    #     And the user presses ArrowDown
    #     And the user presses ArrowDown
    #     And the user presses ArrowDown
    #     And the user presses ArrowDown
    #     And the user presses ArrowDown
    #     And the user presses ArrowDown
    #     And the user presses ArrowDown
    #     And the user presses ArrowDown
    #     And the user presses ArrowDown
    #     And the user waits 3 seconds
    #     And the user selects the "$navigationPanel" bound table field on the main page
    #     Then the value of the "customer__name" bound nested reference field of row 41 in the table field is "Effertz; Okuneva and Strosin (6)"
    #     # items are displayed in ascending order
    #     When the user clicks the "customer__name" bound column of the table field
    #     And the user selects the "$navigationPanel" bound table field on the main page
    #     Then the value of the "customer__name" bound nested reference field of row 31 in the table field is "Schuster and Sons (2)"
    #     Then  the value of the "customer__name" bound nested reference field of row 20 in the table field is "Strosin; Lueilwitz and Stamm (4)"
    #     Then  the value of the "customer__name" bound nested reference field of row 10 in the table field is "Weber LLC (2)"
    #     Then  the value of the "customer__name" bound nested reference field of row 1 in the table field is "Zulauf-Hansen (3)"
    #     # test group pagination by loading the second page of groups
    #     When the user clicks the "customer__name" bound nested field of row 20 in the table field
    #     And the user presses ArrowDown
    #     And the user presses ArrowDown
    #     And the user presses ArrowDown
    #     And the user presses ArrowDown
    #     And the user presses ArrowDown
    #     And the user presses ArrowDown
    #     And the user presses ArrowDown
    #     And the user presses ArrowDown
    #     And the user presses ArrowDown
    #     And the user presses ArrowDown
    #     And the user waits 3 seconds
    #     And the user selects the "$navigationPanel" bound table field on the main page
    #     And the user waits 3 seconds
    #     Then the value of the "customer__name" bound nested reference field of row 40 in the table field is "Satterfield-Wintheiser (3)"

    # TODO: add filter support for labels/enums
    # Scenario: As a user I want to be able to filter the groups of a table grouped by a label column
    #     XT-33693
    #     Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-show-case/ShowCaseProduct"
    #     Then the value of the "category" bound nested reference field of row 1 in the "$navigationPanel" bound table field is "Good"
    #     And the group by option in the header menu of the "category" bound column of the "$navigationPanel" bound table field on the main page is displayed
    #     When the user clicks the group by option in the header menu of the "category" bound column of the "$navigationPanel" bound table field on the main page
    #     Then the value of the "category" bound nested label field of row 1 in the "$navigationPanel" bound table field is "Awful (11)"
    #     When the user filters the "category" bound column in the "$navigationPanel" bound table field with value "Ok" in the main page
    #     Then the value of the "category" bound nested label field of row 1 in the "$navigationPanel" bound table field is "Ok (19)"

    Scenario: As a user I want to be able to filter the children of a table grouped by a label column
        XT-33693
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-show-case/ShowCaseProduct"
        And the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row 1 of the table field
        Then the value of the "category" bound nested reference field of the selected row in the table field is "Good"
        And the group by option in the header menu of the "category" bound column of the table field is displayed
        When the user clicks the group by option in the header menu of the "category" bound column of the table field
        Then the value of the "category" bound nested label field of the selected row in the table field is "No value (26)"
        And the user selects the row 2 of the table field
        Then  the value of the "category" bound nested label field of the selected row in the table field is "Awful (11)"
        When the user filters the "product" bound column in the table field with value "wine"
        And the user selects the row 1 of the table field
        Then the value of the "category" bound nested label field of the selected row in the table field is "No value (1)"
        And the user selects the row 2 of the table field
        Then the value of the "category" bound nested label field of the selected row in the table field is "Awful (2)"
        And the user selects the row 3 of the table field
        Then the value of the "category" bound nested label field of the selected row in the table field is "Good (29)"
        And the user selects the row 4 of the table field
        Then the value of the "category" bound nested label field of the selected row in the table field is "Not bad (3)"
        And the user selects the row 5 of the table field
        Then the value of the "category" bound nested label field of the selected row in the table field is "Ok (2)"

    Scenario: As a user I want to trigger a simple row action on the main list
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/NavigationPanelComplexCard"
        Then the "Navigation Panel - Complex Card" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row 2 of the table field
        Then the value of the "product" bound nested text field of the selected row in the table field is "Appetiser - Bought"
        When the user clicks the "Just display a notification" dropdown action of the selected row of the table field
        Then a toast containing text "This is Appetiser - Bought" is displayed

    Scenario: As a user I want to trigger a simple row action on the main list that refreshes the record
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/NavigationPanelComplexCard"
        Then the "Navigation Panel - Complex Card" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row 2 of the table field
        Then the value of the "product" bound nested text field of the selected row in the table field is "Appetiser - Bought"
        When the user clicks the "Change name to banana" dropdown action of the selected row of the table field
        Then the value of the "product" bound nested text field of the selected row in the table field is "Banana"

        # restore the data
        Then the user navigates to the following link: "@sage/xtrem-show-case/ShowCaseProduct/eyJfaWQiOiIzMTMifQ=="
        Given the user selects the "Product" labelled text field on the main page
        When the user writes "Appetiser - Bought" in the text field
        And the user clicks the "Save" labelled business action button on the main page
        And the user waits 1 second

    Scenario: As a user I want to trigger a simple row action on the main list that refreshes the full list
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/NavigationPanelComplexCard"
        Then the "Navigation Panel - Complex Card" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row 2 of the table field
        Then the value of the "product" bound nested text field of the selected row in the table field is "Appetiser - Bought"
        When the user clicks the "Add an X in front of product name" dropdown action of the selected row of the table field
        Then the value of the "product" bound nested text field of the selected row in the table field is "Appetizer - Cheese Bites"

        # restore the data
        Then the user navigates to the following link: "@sage/xtrem-show-case/ShowCaseProduct/eyJfaWQiOiIzMTMifQ=="
        Given the user selects the "Product" labelled text field on the main page
        Then the value of the text field is "X Appetiser - Bought"
        When the user writes "Appetiser - Bought" in the text field
        And the user clicks the "Save" labelled business action button on the main page
        And the user waits 1 second

    Scenario: As a user I want to trigger a simple line action on the main list that refreshes the record
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/NavigationPanelComplexCard"
        Then the "Navigation Panel - Complex Card" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row 2 of the table field
        Then the value of the "product" bound nested text field of the selected row in the table field is "Appetiser - Bought"
        When the user clicks the "Change name to banana" inline action button of the selected row in the table field
        Then the value of the "product" bound nested text field of the selected row in the table field is "Banana"

        # restore the data
        Then the user navigates to the following link: "@sage/xtrem-show-case/ShowCaseProduct/eyJfaWQiOiIzMTMifQ=="
        Given the user selects the "Product" labelled text field on the main page
        When the user writes "Appetiser - Bought" in the text field
        And the user clicks the "Save" labelled business action button on the main page
        And the user waits 1 second

    Scenario: As a user I want to trigger a simple inline action on the main list that refreshes the full list
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/NavigationPanelComplexCard"
        Then the "Navigation Panel - Complex Card" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row 2 of the table field
        Then the value of the "product" bound nested text field of the selected row in the table field is "Appetiser - Bought"
        When the user clicks the "Add an X in front of product name" inline action button of the selected row in the table field
        Then the value of the "product" bound nested text field of the selected row in the table field is "Appetizer - Cheese Bites"

        # restore the data
        Then the user navigates to the following link: "@sage/xtrem-show-case/ShowCaseProduct/eyJfaWQiOiIzMTMifQ=="
        Given the user selects the "Product" labelled text field on the main page
        Then the value of the text field is "X Appetiser - Bought"
        When the user writes "Appetiser - Bought" in the text field
        And the user clicks the "Save" labelled business action button on the main page
        And the user waits 1 second

    Scenario: As a user I want the search on the navigation panel in split view to be discarded when going back to the main list
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseProduct"
        Then the "Products" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row 1 of the table field
        Then the value of the "Product" labelled nested text field of the selected row in the table field is "Anisette - Mcguiness"
        When the user filters the "Product" labelled column in the table field with value "wine"
        Then the value of the "Product" labelled nested reference field of the selected row in the table field is "Cheese - Wine"
        When the user clicks the "Product" labelled nested field of the selected row in the table field
        Then the "Product Cheese - Wine" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        Then  the value of the "product" bound nested text field of the card 1 in the table field is "Cheese - Wine"
        When the user searches for "ruby" in the navigation panel
        Then  the value of the "product" bound nested text field of the card 1 in the table field is "Wine - Rubyport"
        When the user clicks the "Close record" icon in the header on the main page
        Then the "Products" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row 1 of the table field
        Then the value of the "Product" labelled nested text field of the selected row in the table field is "Cheese - Wine"
        When the user clicks the "Product" labelled nested field of the selected row in the table field
        Then the "Product Cheese - Wine" titled page is displayed
        When the user removes the "Product contains wine." filter from the header of the navigation panel
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        Then  the value of the "product" bound nested text field of the card 1 in the table field is "Anisette - Mcguiness"
        When the user clicks the "Close record" icon in the header on the main page
        Then the "Products" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row 1 of the table field
        Then the value of the "Product" labelled nested reference field of the selected row in the table field is "Anisette - Mcguiness"

    Scenario: As a user I want the options menu to function as expected
        XT-45411
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/NavigationPanelWithDataOptions"
        Then the "Products" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row 1 of the table field
        Then the value of the "category" bound nested text field of the selected row in the table field is "Good"
        And the user selects the row 2 of the table field
        Then the value of the "category" bound nested text field of the selected row in the table field is "Good"
        And the user selects the row 3 of the table field
        Then the value of the "category" bound nested text field of the selected row in the table field is "Good"
        And the user selects the row 4 of the table field
        Then the value of the "category" bound nested text field of the selected row in the table field is "Good"
        And the user selects the row 5 of the table field
        Then the value of the "category" bound nested text field of the selected row in the table field is "Good"
        And the user selects the row 6 of the table field
        Then the value of the "category" bound nested text field of the selected row in the table field is "Good"
        And the user selects the row 7 of the table field
        Then the value of the "category" bound nested text field of the selected row in the table field is "Good"
        And the user selects the row 8 of the table field
        Then the value of the "category" bound nested text field of the selected row in the table field is "Ok"
        When the user selects the "Negative Categories" dropdown option in the navigation panel
        And the user selects the row 1 of the table field
        Then the value of the "category" bound nested text field of the selected row in the table field is "Awful"
        And the user selects the row 2 of the table field
        Then the value of the "category" bound nested text field of the selected row in the table field is "Awful"

    Scenario: As a user I want the header items count field to display the number of items selected
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/NavigationPanelWithDataOptions"
        And the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the "Positive Categories" dropdown option in the navigation panel
        When the user selects all rows of the table field
        Then the bulk action bar of the table field has 8 items
        When the user unselects all rows of the table field
        And the user selects the "Negative Categories" dropdown option in the navigation panel
        When the user selects all rows of the table field
        Then the bulk action bar of the table field has 2 items
        When the user unselects all rows of the table field
        And the user selects the "All Categories" dropdown option in the navigation panel
        When the user selects all rows of the table field
        Then the bulk action bar of the table field has 10 items

    Scenario: As a user I want the options menu to function as expected in full-width view
        XT-48173
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TableWithOptionsMenu/"
        Then the "Field - Table" titled page is displayed
        When the user clicks the "Close record" icon in the header on the main page
        And the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row 1 of the table field
        Then the value of the "textField" bound nested text field of the selected row in the table field is "Ali Express"
        When the user selects the "Amazon" dropdown option in the navigation panel
        And the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row 1 of the table field
        Then the value of the "textField" bound nested text field of the selected row in the table field is "Amazon"

    Scenario: As a user I want the refresh action to keep the filter and the sorting order of the main list
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/NavigationPanelComplexCard"
        Then the "Navigation Panel - Complex Card" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        Then the value of the "product" bound nested text field of the selected row in the table field is "Anisette - Mcguiness"
        When the user filters the "product" bound column in the table field with value "baby"
        Then the value of the "product" bound nested text field of the selected row in the table field is "Lobster - Baby, Boiled"
        And the user clicks the "product" bound column of the table field
        And the user waits 1 second
        And the user clicks the "product" bound column of the table field
        And the user waits 1 second
        And the user clicks the "product" bound column of the table field
        And the user waits 1 second
        And the user selects the row 1 of the table field
        Then the value of the "product" bound nested text field of the selected row in the table field is "Lobster - Baby, Boiled"
        And the user selects the row 2 of the table field
        Then the value of the "product" bound nested text field of the selected row in the table field is "Spinach - Baby"
        When the user selects the row 1 of the table field
        When the user clicks the "Add an X in front of product name" dropdown action of the selected row of the table field
        Then the value of the "product" bound nested text field of the selected row in the table field is "Spinach - Baby"
        And the user selects the row 2 of the table field
        Then the value of the "product" bound nested text field of the selected row in the table field is "X Lobster - Baby, Boiled"
        And the user clicks the "product" bound column of the table field
        And the user waits 1 second
        And the user clicks the "product" bound column of the table field
        And the user waits 1 second
        And the user clicks the "product" bound column of the table field
        And the user waits 1 second
        And the user selects the row 1 of the table field
        Then the value of the "product" bound nested text field of the selected row in the table field is "Spinach - Baby"
        And the user selects the row 2 of the table field
        Then the value of the "product" bound nested text field of the selected row in the table field is "X Lobster - Baby, Boiled"
        # restore the data
        Then the user navigates to the following link: "@sage/xtrem-show-case/ShowCaseProduct/eyJfaWQiOiIzMDMifQ=="
        Then the "Product X Lobster - Baby, Boiled" titled page is displayed
        Given the user selects the "Product" labelled text field on the main page
        Then the value of the text field is "X Lobster - Baby, Boiled"
        When the user writes "Lobster - Baby, Boiled" in the text field
        And the user clicks the "Save" labelled business action button on the main page
        And the user waits 1 second

    Scenario: As a user I want to access the data of technical fields in navigation panel line actions
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/NavigationPanelComplexCard"
        Then the "Navigation Panel - Complex Card" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row 2 of the table field
        Then the value of the "product" bound nested text field of the selected row in the table field is "Appetiser - Bought"
        When the user clicks the "An action referring to a technical field" dropdown action of the selected row of the table field
        Then a toast containing text "This product costs 9.94" is displayed

    Scenario: As a user I want to use filter pills in the Navigation Panel
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseProduct"
        Then the "Products" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user clicks the "Hide table filters" labelled button of the table field
        And the user filters the "Description" labelled column in the table field with value "zero"
        And the user selects the row 1 of the table field
        Then the value of the "Product" labelled nested text field of the selected row in the table field is "Appetizer - Cheese Bites"
        When the user clicks the "Product" labelled nested field of the selected row in the table field
        Then the "Product Appetizer - Cheese Bites" titled page is displayed
        Then the pill containing the "contains" filter with value "zero" for the "description" column in the navigation panel is displayed
        When the user removes the pill containing the "contains" filter with value "zero" for the "description" column in the navigation panel
        Then the pill containing the "contains" filter with value "zero" for the "description" column in the navigation panel is hidden
        When the user clicks the "Close record" icon in the header on the main page
        Then the "Products" titled page is displayed
        And the user selects the row 1 of the table field
        Then the value of the "Product" labelled nested text field of the selected row in the table field is "Anisette - Mcguiness"

    Scenario: As a user I want the selected items count to be correct after scrolling to get new data
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseProduct"
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        When the user selects all rows of the table field
        And the user selects the row 21 of the table field
        Then the value of the "Product" labelled nested text field of the selected row in the table field is "Beans - Butter Lrg Lima"
        And the user selects the row 41 of the table field
        Then the value of the "Product" labelled nested text field of the selected row in the table field is "Boogies"
        Then the bulk action bar of the table field has 503 items
        When the user selects the row 41 of the table field
        And the user unticks the main checkbox of the selected row in the table field
        Then the bulk action bar of the table field has 502 items
        And the user ticks the main checkbox of the selected row in the table field
        Then the bulk action bar of the table field has 503 items


    Scenario: As a user I want to make sure the filters remain after table refresh
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseProduct"
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user clicks the "Hide table filters" labelled button of the table field
        When the user filters the "product" bound column in the table field with value "tr"
        And the user clicks the "Refresh" labelled button of the table field
    # #Test incorrect :  step definition used is for custom refernece filter whi:e it is a regular filter.
    # # ATP / XT  enhanceemnt or refactoring  is needed to be able to open / close any filter XT-71159
    # And the user opens the filter of the "product" bound column in the table field
    # Then the search value of the filter in the table field is "tr"

    Scenario Outline: <Device> -  As an ATP / XTReeM user I can open / close the navigation panel
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/ShowCaseProduct/eyJfaWQiOiIxMDYifQ=="
        Then the "Product Anisette - Mcguiness" titled page is displayed
        And the user opens the navigation panel
        # And takes a screenshot
        And the user closes the navigation panel
        # And takes a screenshot
        Examples:
            | Device     |
            | HD desktop |
            | desktop    |
            | tablet     |
            | mobile     |

    Scenario: As a user I want the "My selected data" navigation panel filter to take priority over any previously selected option menu
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/NavigationPanelHidden"
        Then the "Navigation Panel - With Hidden Nested Items" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        Then the value of the "product" bound nested text field of the selected row in the table field is "Anisette - Mcguiness"
        When the user selects the "Amazon" dropdown option in the navigation panel
        Then the value of the "product" bound nested text field of the selected row in the table field is "Appetiser - Bought"
        # BL: Recover these lines after view management is implemented
        # Then  the value of the option menu of the table field is "Amazon"
        # When the user navigates to the following link: "@sage/xtrem-show-case/NavigationPanelHidden"
        # Then the "Navigation Panel - With Hidden Nested Items" titled page is displayed
        # Then  the value of the "product" bound nested text field of row 1 in the table field is "Appetiser - Bought"
        # Then  the value of the option menu of the table field is "Amazon"
        When the user navigates to the following link: "@sage/xtrem-show-case/NavigationPanelHidden/eyJfZmlsdGVyIjoie1wicHJvZHVjdFwiOntcIl9hbmRcIjpbe1wiX3JlZ2V4XCI6XCJUZWFcIixcIl9vcHRpb25zXCI6XCJpXCJ9XX19In0="
        Then the "Navigation Panel - With Hidden Nested Items" titled page is displayed
        Then  the value of the option menu of the table field is "My selected data"
        And the user selects the row 1 of the table field
        Then the value of the "product" bound nested text field of the selected row in the table field is "Arizona - Plum Green Tea"
        And the user selects the row 2 of the table field
        Then the value of the "product" bound nested text field of the selected row in the table field is "Beef Flat Iron Steak"

    Scenario: As a user I want to see the section on the page when I open it using a deep URL
        XT-73231
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/StandardShowCaseProvider/?_id=2&_SELECTED_SECTION_ID=miscSection"
        Then the "Provider 2" titled page is displayed
        Then the "Misc" labelled navigation anchor is selected
        When the user navigates to the following link: "@sage/xtrem-show-case/StandardShowCaseProvider/?_id=2"
        Then the "Provider 2" titled page is displayed
        Then the "Products" labelled navigation anchor is selected

    Scenario: As a user I want see the attachment section on a page that manages attachments
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/StandardShowCaseInvoice/5"
        Then the "Invoice 5" titled page is displayed
        When selects the "Attachments" labelled navigation anchor on the main page
        Then the "Attachments" labelled navigation anchor is selected

    Scenario Outline: <Device> - As a user I want to search and clear the navigation panel
        Given the user opens the application on a <Device> using the following link: "@sage/xtrem-show-case/ShowCaseProduct/eyJfaWQiOiIxMDYifQ=="
        Then the user opens the navigation panel
        And the user selects the "Description" labelled text field on the main page
        Then the value of the text field is "complexity"
        Then the user searches for "beef" in the navigation panel
        Then the search field value in the navigation panel is "beef"
        # And takes a screenshot
        And the user clears the search field in the navigation panel
        Then the search field value in the navigation panel is ""
        # And takes a screenshot
        Examples:
            | Device  |
            | desktop |
            | tablet  |
            | mobile  |

# This is a test on SDMO environment
# Scenario: As a user, I can test the authorizations via the personna
#     Given the user opens the application on a desktop
#     When the user opens the persona
#     And the user clicks the Close button of the dialog on the main page
#     And the "Items" menu item on the sitemap is displayed
#     And the user clicks the "Items" menu item
#     And the "Item" sub menu item on the sitemap is displayed
#     And the user clicks the "Items" menu item
#     And the "Item" sub menu item on the sitemap is hidden
#     And the user maximizes the sitemap
#     And the user minimizes the sitemap
#     And the "Items" menu item on the sitemap is displayed
#     And the user clicks the "Items" menu item
#     And the "Item" sub menu item on the sitemap is displayed
#     And the user clicks the "Item" sub menu item
#     And the "Item" sub menu item on the sitemap is hidden
#     And the "Items" menu item on the sitemap is displayed
#     And the user clicks the "Items" menu item
#     And the "Item data" sub menu item on the sitemap is displayed
#     And the user expands the "Item data" sub menu item
#     And the "Bill of materials" sub menu item on the sitemap is displayed
#     And the user clicks the "Bill of materials" sub menu item
#     And the "Bill of materials" sub menu item on the sitemap is hidden
