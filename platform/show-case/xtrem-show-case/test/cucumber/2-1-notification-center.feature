Feature: 2-1 Notification center
    # Tests the creation and delivery of notifications through the Notification Center, including customization of notification properties like title, description, level and urgency


    Scenario: As an ATP XTreeM user I can receive a notification from the Notification Center page

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/NotificationCenter"
        And the "Notification center" titled page is displayed
        And the user selects the "Title" labelled text field on the main page
        And the user writes "Test notification" in the text field
        And the user selects the "Description" labelled text area field on the main page
        And the user writes "Test description" in the text area field
        And the user selects the "Level" labelled dropdown-list field on the main page
        And the user clicks in the dropdown-list field
        And the user selects "Error" in the dropdown-list field
        And the value of the dropdown-list field is "Error"
        And the user selects the "Icon" labelled dropdown-list field on the main page
        And the user clicks in the dropdown-list field
        And the user selects "add" in the dropdown-list field
        And the value of the dropdown-list field is "add"
        And the user selects the "Is urgent?" labelled checkbox field on the main page
        And the user ticks the checkbox field
        And the value of the checkbox field is "true"
        And the user clicks the "Trigger notification" labelled business action button on the main page

        And the user selects the "Title" labelled text field on the main page
        And the user writes "Test notification" in the text field
        And the user selects the "Description" labelled text area field on the main page
        And the user writes "Test description" in the text area field
        And the user selects the "Level" labelled dropdown-list field on the main page
        And the user clicks in the dropdown-list field
        And the user selects "Warning" in the dropdown-list field
        And the value of the dropdown-list field is "Warning"
        And the user selects the "Icon" labelled dropdown-list field on the main page
        And the user clicks in the dropdown-list field
        And the user selects "admin" in the dropdown-list field
        And the value of the dropdown-list field is "admin"
        And the user clicks the "Trigger notification" labelled business action button on the main page

        And the user selects the "Title" labelled text field on the main page
        And the user writes "Test notification" in the text field
        And the user selects the "Description" labelled text area field on the main page
        And the user writes "Test description" in the text area field
        And the user selects the "Level" labelled dropdown-list field on the main page
        And the user clicks in the dropdown-list field
        And the user selects "Info" in the dropdown-list field
        And the value of the dropdown-list field is "Info"
        And the user selects the "Icon" labelled dropdown-list field on the main page
        And the user clicks in the dropdown-list field
        And the user selects "alert" in the dropdown-list field
        And the value of the dropdown-list field is "alert"
        And the user clicks the "Trigger notification" labelled business action button on the main page

        And the user selects the "Title" labelled text field on the main page
        And the user writes "Test notification" in the text field
        And the user selects the "Description" labelled text area field on the main page
        And the user writes "Test description" in the text area field
        And the user selects the "Level" labelled dropdown-list field on the main page
        And the user clicks in the dropdown-list field
        And the user selects "Success" in the dropdown-list field
        And the value of the dropdown-list field is "Success"
        And the user selects the "Icon" labelled dropdown-list field on the main page
        And the user clicks in the dropdown-list field
        And the user selects "bin" in the dropdown-list field
        And the value of the dropdown-list field is "bin"
        And the user clicks the "Trigger notification" labelled business action button on the main page

        And the user selects the "Title" labelled text field on the main page
        And the user writes "Test notification" in the text field
        And the user selects the "Description" labelled text area field on the main page
        And the user writes "Test description" in the text area field
        And the user selects the "Level" labelled dropdown-list field on the main page
        And the user clicks in the dropdown-list field
        And the user selects "Approval" in the dropdown-list field
        And the value of the dropdown-list field is "Approval"
        And the user selects the "Icon" labelled dropdown-list field on the main page
        And the user clicks in the dropdown-list field
        And the user selects "bank" in the dropdown-list field
        And the value of the dropdown-list field is "bank"
        And the user clicks the "Trigger notification" labelled business action button on the main page
