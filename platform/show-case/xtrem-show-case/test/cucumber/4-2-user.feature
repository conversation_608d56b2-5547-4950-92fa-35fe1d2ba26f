Feature: 4-2 User
    # Tests user management functionality, including creating new users with personal information and verifying the user creation workflow
    Scenario: Open page
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-authorization/User"
        Then the "Users" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel
        Then the "User" titled page is displayed

    Scenario: Create user
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-authorization/User"
        Then the "Users" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel
        And the user selects the "firstName" bound text field on the main page
        And the user clicks in the text field
        And the user writes "<PERSON>" in the text field
        And the user selects the "lastName" bound text field on the main page
        And the user clicks in the text field
        And the user writes "Maximoff" in the text field
        And the user selects the "email" bound text field on the main page
        And the user clicks in the text field
        And the user writes "<EMAIL>" in the text field
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast with text "Record created" is displayed
        When the user searches for "<PERSON>" in the navigation panel
        And the user clicks the record with the text "<PERSON>" in the navigation panel
        Then the "User Wanda Maximoff" titled page is displayed
