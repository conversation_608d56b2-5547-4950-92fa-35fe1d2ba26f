import { ClientArtifactMetadata } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as fs from 'fs';
import * as path from 'path';

describe('Integrity of .meta.json file, IMPORTANT: requires a build of the package so the file is constructed before the test run', () => {
    let productMeta: ClientArtifactMetadata;
    let providerMeta: ClientArtifactMetadata;

    before(() => {
        productMeta = JSON.parse(
            fs.readFileSync(path.resolve('./build/lib/pages/do-not-change-this-page.meta.json'), 'utf-8'),
        );
        providerMeta = JSON.parse(
            fs.readFileSync(path.resolve('./build/lib/pages/do-not-change-this-page-either.meta.json'), 'utf-8'),
        );
    });

    it('Integrity of nodes', () =>
        assert.deepEqual(providerMeta.nodes, [
            '@sage/xtrem-show-case/ShowCaseProvider',
            '@sage/xtrem-show-case/ShowCaseProduct',
            '@sage/xtrem-show-case/ShowCaseProviderAddress',
            '@sage/xtrem-show-case/ShowCaseCountry',
        ]));

    it('Integrity of pageAccess', () =>
        assert.deepEqual(providerMeta.pageAccess, {
            node: '@sage/xtrem-show-case/ShowCaseProvider',
            bind: '$read',
        }));

    it('Integrity of pageNode', () =>
        assert.deepEqual(providerMeta.pageNode, '@sage/xtrem-show-case/ShowCaseProvider'));

    it('Integrity of className', () => assert.deepEqual(providerMeta.className, 'DoNotChangeThisPageEither'));

    it('Integrity of authorizationCode', () => assert.deepEqual(providerMeta.authorizationCode, 'SHCPRVD'));

    it('Integrity of category', () => assert.deepEqual(providerMeta.category, 'SHOWCASE'));

    it('Integrity of plugins', () => assert.deepEqual(providerMeta.plugins, []));

    it('Integrity of md5', () => assert.deepEqual(providerMeta.md5, '01d82d5d24dbd5f5bdcad6a55b985b55'));

    it('Integrity of parentMenuItem', () =>
        assert.deepEqual(providerMeta.parentMenuItem, '@sage/xtrem-show-case/application-pages'));

    it('Integrity of priority', () => assert.deepEqual(providerMeta.priority, 200));

    it('Integrity of literals', () =>
        assert.deepEqual(productMeta.literals, {
            strings: [
                '@sage/xtrem-show-case/pages__do_not_change_this_page____title',
                '@sage/xtrem-show-case/pages__do_not_change_this_page____objectTypeSingular',
                '@sage/xtrem-show-case/pages__do_not_change_this_page____objectTypePlural',
                '@sage/xtrem-show-case/pages__do_not_change_this_page____navigationPanel__bulkActions__title',
                '@sage/xtrem-show-case/pages__do_not_change_this_page____navigationPanel__listItem__title__title',
                '@sage/xtrem-show-case/pages__do_not_change_this_page____navigationPanel__listItem__titleRight__title',
                '@sage/xtrem-show-case/pages__do_not_change_this_page____navigationPanel__listItem__line2__title',
                '@sage/xtrem-show-case/pages__do_not_change_this_page____navigationPanel__listItem__listPrice__title',
                '@sage/xtrem-show-case/pages__do_not_change_this_page____navigationPanel__listItem__category__title',
                '@sage/xtrem-show-case/pages__do_not_change_this_page____navigationPanel__listItem__netPrice__title',
                '@sage/xtrem-show-case/pages__do_not_change_this_page____navigationPanel__listItem__tax__title',
                '@sage/xtrem-show-case/pages__do_not_change_this_page____navigationPanel__listItem__hotProduct__title',
                '@sage/xtrem-show-case/pages__do_not_change_this_page____navigationPanel__listItem__provider__title',
                '@sage/xtrem-show-case/pages__do_not_change_this_page___id____title',
                '@sage/xtrem-show-case/pages__do_not_change_this_page__product____title',
                '@sage/xtrem-show-case/pages__do_not_change_this_page__description____title',
                '@sage/xtrem-show-case/pages__do_not_change_this_page__hotProduct____title',
                '@sage/xtrem-show-case/pages__do_not_change_this_page__qty____title',
                '@sage/xtrem-show-case/pages__do_not_change_this_page__st____title',
                '@sage/xtrem-show-case/pages__do_not_change_this_page__listPrice____title',
                '@sage/xtrem-show-case/pages__do_not_change_this_page__progress____title',
                '@sage/xtrem-show-case/pages__do_not_change_this_page__tax____title',
                '@sage/xtrem-show-case/pages__do_not_change_this_page__netPrice____title',
                '@sage/xtrem-show-case/pages__do_not_change_this_page__discount____title',
                '@sage/xtrem-show-case/pages__do_not_change_this_page__amount____title',
                '@sage/xtrem-show-case/pages__do_not_change_this_page__provider____title',
                '@sage/xtrem-show-case/pages__do_not_change_this_page__releaseDate____title',
                '@sage/xtrem-show-case/pages__do_not_change_this_page__endingDate____title',
                '@sage/xtrem-show-case/pages__do_not_change_this_page__category____title',
                '@sage/xtrem-show-case/pages__do_not_change_this_page__category2____title',
                '@sage/xtrem-show-case/pages__do_not_change_this_page__subcategories____title',
                '@sage/xtrem-show-case/pages__do_not_change_this_page__entries____title',
                '@sage/xtrem-show-case/pages__do_not_change_this_page__certificate____title',
                '@sage/xtrem-show-case/pages__do_not_change_this_page__createShowCaseProduct____title',
                '@sage/xtrem-show-case/pages__do_not_change_this_page__saveShowCaseProduct____title',
                '@sage/xtrem-show-case/pages__do_not_change_this_page__accessControlledBlock____title',
                '@sage/xtrem-show-case/pages__do_not_change_this_page__blockAccessControlledField1____helperText',
                '@sage/xtrem-show-case/pages__do_not_change_this_page__blockAccessControlledField1____title',
                '@sage/xtrem-show-case/pages__do_not_change_this_page__blockAccessControlledField2____helperText',
                '@sage/xtrem-show-case/pages__do_not_change_this_page__blockAccessControlledField2____title',
                '@sage/xtrem-show-case/pages__do_not_change_this_page__refreshPage____title',
                '@sage/xtrem-show-case/pages__do_not_change_this_page__emptyPage____title',
                '@sage/xtrem-show-case/pages__do_not_change_this_page__customSave____title',
                '@sage/xtrem-show-case/pages__do_not_change_this_page__deleteShowCaseProduct____title',
                '@sage/xtrem-show-case/delete-dialog-title',
                '@sage/xtrem-show-case/delete-dialog-content',
                '@sage/xtrem-show-case/delete-confirmation',
            ],
            enums: ['@sage/xtrem-show-case/ShowCaseProductCategory'],
        }));
});
