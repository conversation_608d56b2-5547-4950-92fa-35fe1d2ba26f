import { Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import { showCaseProductCategoryDataType } from '../../lib/enums/_index';

describe('Localize enum values', () => {
    it('single value using "base" locale', () =>
        Test.withContext(context => {
            assert.equal(showCaseProductCategoryDataType.getLocalizedValue(context, 'notBad'), 'Not bad');
        }));

    it('single value using "en-US" locale', () =>
        Test.withContext(
            context => {
                assert.equal(showCaseProductCategoryDataType.getLocalizedValue(context, 'notBad'), 'Not bad');
            },
            { locale: 'en-US' },
        ));

    it('single value using "fr-FR" locale', () =>
        Test.withContext(
            context => {
                assert.equal(showCaseProductCategoryDataType.getLocalizedValue(context, 'notBad'), 'Pas mal');
            },
            { locale: 'fr-FR' },
        ));

    it('all values using "base" locale', () =>
        Test.withContext(context => {
            assert.deepEqual(showCaseProductCategoryDataType.getLocalizedValues(context), [
                'Great',
                'Good',
                'Ok',
                'Not bad',
                'Awful',
            ]);
        }));

    it('all values using "en-US" locale', () =>
        Test.withContext(
            context => {
                assert.deepEqual(showCaseProductCategoryDataType.getLocalizedValues(context), [
                    'Great',
                    'Good',
                    'Ok',
                    'Not bad',
                    'Awful',
                ]);
            },
            { locale: 'en-US' },
        ));

    it('all values using "fr-FR" locale', () =>
        Test.withContext(
            context => {
                assert.deepEqual(showCaseProductCategoryDataType.getLocalizedValues(context), [
                    'Super',
                    'Bon',
                    'Ok',
                    'Pas mal',
                    'Affreux',
                ]);
            },
            { locale: 'fr-FR' },
        ));

    it("should throw exception if member value doesn't exist", () =>
        Test.withContext(context => {
            assert.throw(
                () => showCaseProductCategoryDataType.getLocalizedValue(context, 'XXX'),
                'ShowCaseProductCategory: Invalid member value, XXX',
            );
        }));
});
