import { Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import { nodes, serviceOptions } from '../../lib';

describe('Service Options', () => {
    before(() => {});

    it('Create a showCaseProduct with an inactive option', () =>
        Test.withContext(async context => {
            const node = await context.create(nodes.ShowCaseProduct, {});
            assert.isNull(
                await node.discount,
                'ShowCaseProduct.discount: must be null when service option is inactive',
            );
        }));

    it('Create a showCaseProduct with an active option', () =>
        Test.withContext(
            async context => {
                const node = await context.create(nodes.ShowCaseProduct, {});
                assert.equal(await node.discount, 20);
            },
            {
                testActiveServiceOptions: [serviceOptions.showCaseDiscountOption],
            },
        ));
    it('test a service option wich is activated with the test command line', () =>
        Test.withContext(async context => {
            assert.isFalse(await context.isServiceOptionEnabled(serviceOptions.showCaseDiscountOption));
            assert.isTrue(await context.isServiceOptionEnabled(serviceOptions.showCaseWorkInProgressOption));
        }));
});
