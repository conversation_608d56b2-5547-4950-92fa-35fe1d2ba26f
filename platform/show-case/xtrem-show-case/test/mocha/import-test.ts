import { Context, PubSub, Test, TextStream, date } from '@sage/xtrem-core';
import { Decimal } from '@sage/xtrem-decimal';
import * as xtremImportExport from '@sage/xtrem-import-export';
import * as xtremUpload from '@sage/xtrem-upload';
import { assert } from 'chai';
import * as fs from 'fs';
import * as path from 'path';
import { promisify } from 'util';
import * as xtremShowCase from '../../index';

export async function sleepMillis(ms: number): Promise<void> {
    await promisify(setTimeout)(ms);
}

interface TestCasePayload {
    contextValue: { replyTopic: string };
    result: string;
    reason?: string;
}

async function mockScanResultListener(uploadedFileId: number, payload: TestCasePayload): Promise<void> {
    await Test.withCommittedContext(
        async context => {
            const uploadedFile = await context.read(
                xtremUpload.nodes.UploadedFile,
                { _id: uploadedFileId },
                { forUpdate: true },
            );

            // See https://confluence.sage.com/pages/viewpage.action?spaceKey=XTREEM&title=Async+Context+Module
            if (payload.result !== 'success') {
                let rejectReason = payload.reason;
                if (!rejectReason) rejectReason = `Upload rejected: ${payload.result}`;
                await uploadedFile.$.set({ status: 'rejected', rejectReason });
            } else {
                await uploadedFile.$.set({ status: 'verified' });
            }

            await uploadedFile.$.save();
        },
        { source: 'listener' },
    );
}

interface BatchImportResult {
    xtremImportExport: {
        importExportTemplate: {
            batchImport: {
                start: {
                    trackingId: string;
                };
            };
        };
    };
}

function runBatchImport(mutation: string): Promise<BatchImportResult> {
    return Test.withReadonlyContext(context => context.executeGraphql<BatchImportResult>(mutation), {
        source: 'graphql',
    });
}

async function getFlatImportResult(
    context: Context,
    uploadedFileId: string,
): Promise<xtremImportExport.nodes.ImportResult | null> {
    const importResults = await context
        .query(xtremImportExport.nodes.ImportResult, { filter: { uploadedFile: uploadedFileId } })
        .toArray();
    return importResults.length === 0 ? null : importResults[0];
}

async function pollFlatImportResult(
    context: Context,
    uploadedFileId: string,
    startStamp?: number,
): Promise<xtremImportExport.nodes.ImportResult> {
    const timeoutMillis = 60000;
    const pollingMillis = 2000;
    const now = Date.now();
    const startedAt = startStamp ?? now;
    if (now - startedAt > timeoutMillis) {
        throw new Error(`polling has timed out: the maximum time allowed was ${timeoutMillis} ms`);
    }
    await sleepMillis(pollingMillis);
    const importResult = await getFlatImportResult(context, uploadedFileId);
    const status = await importResult?.status;
    if (importResult && status && !['pending', 'inProgress'].includes(status || '')) {
        return importResult;
    }
    return pollFlatImportResult(context, uploadedFileId, startedAt);
}

async function executeImport(
    context: Context,
    templateId: string,
    csvPath: string,
): Promise<xtremImportExport.nodes.ImportResult> {
    const uploadResult = await xtremImportExport.uploadTestFile(path.resolve(csvPath));

    await mockScanResultListener(uploadResult.uploadedFileId, {
        contextValue: { replyTopic: 'UploadedFile/InfrastructureComplete' },
        result: 'success',
    });
    const mutation = `mutation { xtremImportExport {  importExportTemplate { batchImport{ start (
            templateId: "${templateId}"
            uploadedFileId: "${uploadResult.uploadedFileId}"
            options : {dryRun:false, doInsert:true, doUpdate:false, maxErrorCount:1000}
        ) { trackingId }}}}}`;
    await runBatchImport(mutation);

    const result = await pollFlatImportResult(context, uploadResult.uploadedFileId.toString());
    return result;
}
async function importShowCaseCustomers(context: Context): Promise<void> {
    const importResult = await executeImport(
        context,
        'ShowCaseCustomer',
        './test/fixtures/csv-files/show-case-customer.csv',
    );
    assert.equal(await importResult.rowsProcessed, 3);
    assert.equal(await importResult.numberOfRowsInError, 0);

    // Verify that Steve Hair was added to the list:
    const showCaseCustomer = await context.tryRead(xtremShowCase.nodes.ShowCaseCustomer, { name: 'Sage' });
    assert.isNotNull(showCaseCustomer);
    assert.equal(await showCaseCustomer!.name, 'Sage');
}

async function importShowCaseProvidersAddresses(context: Context): Promise<void> {
    const importResult = await executeImport(
        context,
        'ShowCaseProviderAddress',
        './test/fixtures/csv-files/show-case-provider-address.csv',
    );
    assert.equal(await importResult.rowsProcessed, 2);
    assert.equal(await importResult.numberOfRowsInError, 0);

    // Verify that Steve Hair was added to the list:
    const showCaseProviderAddress = await context.tryRead(xtremShowCase.nodes.ShowCaseProviderAddress, {
        name: 'LaFraise Cycles - Office',
    });
    assert.isNotNull(showCaseProviderAddress);
    assert.equal(await showCaseProviderAddress!.name, 'LaFraise Cycles - Office');
    assert.equal(await showCaseProviderAddress!.addressLine1, '117 Rue Montgolfier');
    assert.equal(await showCaseProviderAddress!.city, 'Roubaix');
}

async function importShowCaseProviders(context: Context): Promise<void> {
    const importResult = await executeImport(
        context,
        'ShowCaseProvider',
        './test/fixtures/csv-files/show-case-provider.csv',
    );
    assert.equal(await importResult.rowsProcessed, 2);
    assert.equal(await importResult.numberOfRowsInError, 0);

    const showCaseProvider1 = await context.tryRead(xtremShowCase.nodes.ShowCaseProvider, {
        textField: 'LaFraise Cycles',
    });
    assert.isNotNull(showCaseProvider1);
    assert.equal(await showCaseProvider1!.textField, 'LaFraise Cycles');
    assert.equal(await showCaseProvider1!.integerField, 1);
    assert.isTrue(await showCaseProvider1!.booleanField);
    assert.isTrue(Decimal.isDecimal(await showCaseProvider1!.decimalField));
    assert.isTrue((await showCaseProvider1!.decimalField) === (Decimal.make('2.34') as any));
    assert.deepEqual(await showCaseProvider1!.dateField, date.make(2019, 7, 1));
    assert.equal(await showCaseProvider1!.minQuantity, 1);
    const showCaseProvider1Products = showCaseProvider1!.products;
    assert.equal(await showCaseProvider1Products.length, 1);

    const products = (await showCaseProvider1Products.toArray()) as xtremShowCase.nodes.ShowCaseProduct[];
    const product = products[0];
    assert.equal(await product.product, 'Randonneur de Dries');
    assert.equal(await product.barcode, '1001001');
    assert.equal(await product.description, 'Velo de randonnee');
    assert.equal(await product.hotProduct, true);
    assert.equal(await product.fixedQuantity, 2);
    assert.equal(await product.st, 249);
    assert.isTrue((await product.listPrice) === (Decimal.make('72.56') as any));
    assert.equal(await product.progress, 50);
    assert.isTrue((await product.tax) === (Decimal.make('66.63') as any));
    assert.isTrue((await product.amount) === (Decimal.make('79.89') as any));
    assert.equal(await product!.email, '<EMAIL>');
    assert.deepEqual(await product.releaseDate, date.make(2019, 9, 19));
    assert.deepEqual(await product.endingDate, date.make(2022, 9, 19));
    assert.equal(await product.category, 'great');
    assert.deepEqual(await product.subcategories, ['notBad', 'awful']);
    assert.equal(await product.qty, 10);
    assert.isTrue((await product.netPrice) === (Decimal.make('1760.09') as any));

    const productOriginAddress1 = await product.originAddress;
    assert.isNotNull(productOriginAddress1);
    assert.equal(await productOriginAddress1!.name, 'Newcastle Upon Tyne');
    assert.equal(await (await productOriginAddress1!.country).code, 'UK');

    const showCaseProvider2 = await context.tryRead(xtremShowCase.nodes.ShowCaseProvider, {
        textField: 'Cycles Catting',
    });
    assert.isNotNull(showCaseProvider2);
    assert.equal(await showCaseProvider2!.integerField, 2);
    assert.equal(await showCaseProvider2!.textField, 'Cycles Catting');
    assert.isFalse(await showCaseProvider2!.booleanField);
    assert.isTrue(Decimal.isDecimal(await showCaseProvider2!.decimalField));
    assert.isTrue((await showCaseProvider2!.decimalField) === (Decimal.make('3.43') as any));
    assert.deepEqual(await showCaseProvider2!.dateField, date.make(1999, 6, 2));
    assert.equal(await showCaseProvider2!.minQuantity, 2);
    assert.equal(await showCaseProvider2!.products.length, 1);
}

async function importShowCaseOrders(context: Context): Promise<void> {
    const importResult = await executeImport(context, 'ShowCaseOrder', './test/fixtures/csv-files/show-case-order.csv');
    assert.equal(await importResult.rowsProcessed, 3);
    assert.equal(await importResult.numberOfRowsInError, 0);

    const showCaseOrder = await context.tryRead(xtremShowCase.nodes.ShowCaseOrder, { _id: 201 });
    assert.isNotNull(showCaseOrder);
    assert.isNotNull(await showCaseOrder!.details);
    assert.equal((await showCaseOrder!.details).compareTo(TextStream.fromString('Diverse real-time architecture ')), 0);
}

async function importShowCaseInvoices(context: Context): Promise<void> {
    const importResult = await executeImport(
        context,
        'ShowCaseInvoice',
        './test/fixtures/csv-files/show-case-invoice.csv',
    );
    assert.equal(await importResult.rowsProcessed, 7);
    assert.equal(await importResult.numberOfRowsInError, 0);

    const showCaseInvoice = await context.tryRead(xtremShowCase.nodes.ShowCaseInvoice, { _id: 531 });
    assert.isNotNull(showCaseInvoice);
    assert.equal((await showCaseInvoice!.order)._id, 202);
    assert.equal(await (await showCaseInvoice!.customer)!.name, 'Sage');
    assert.equal((await showCaseInvoice!.notes).compareTo(TextStream.fromString('Mobility')), 0);
    assert.deepEqual(await showCaseInvoice!.purchaseDate, date.make(2022, 2, 14));

    const showCaseInvoiceLines = showCaseInvoice!.lines;
    assert.equal(await showCaseInvoiceLines.length, 2);

    const lines = (await showCaseInvoiceLines.toArray()) as xtremShowCase.nodes.ShowCaseInvoiceLine[];
    const showCaseInvoiceLine = lines[0];

    assert.equal(await showCaseInvoiceLine.orderQuantity, 15);
    assert.equal(await showCaseInvoiceLine.comments, 'Atrium');
    assert.equal(await (await showCaseInvoiceLine.product).product, 'Special Paimei rice');
    assert.isTrue((await showCaseInvoiceLine!.netPrice) === (Decimal.make('2678') as any));
    assert.equal(await showCaseInvoiceLine.discountType, 'newProductPromotion');
}

async function cleanUp(): Promise<void> {
    await Test.withCommittedContext(async context => {
        await context
            .query(xtremShowCase.nodes.ShowCaseInvoice, {
                filter: {
                    customer: { name: { _in: ['Buckingham', 'Elysée', 'Sage'] } },
                },
                forUpdate: true,
            })
            .forEach(invoice => invoice.$.delete());
        await context.deleteMany(xtremShowCase.nodes.ShowCaseCustomer, {
            name: { _in: ['Buckingham', 'Elysée', 'Sage'] },
        });
        await context.deleteMany(xtremShowCase.nodes.ShowCaseProvider, {
            textField: { _in: ['LaFraise Cycles', 'Cycles Catting'] },
        });
        await context.deleteMany(xtremShowCase.nodes.ShowCaseProviderAddress, {
            name: { _in: ['LaFraise Cycles', 'Cycles Catting'] },
        });

        const csvDir = path.join(__dirname, `../../../../${context.tenantId}`);
        if (fs.existsSync(csvDir)) fs.rmSync(csvDir, { recursive: true });

        await PubSub.publish(context, 'clear_test_notification_queues', {
            tenantId: null,
            applicationName: Test.application.name,
        });
    });
}

describe('Import showCase elements', () => {
    it('Import showCase elements', () =>
        Test.withContext(async context => {
            // Append the list of customers
            await importShowCaseCustomers(context);

            // Append the list of providers' addresses
            await importShowCaseProvidersAddresses(context);

            // Append the list of providers
            await importShowCaseProviders(context);

            // Append the list of orders
            await importShowCaseOrders(context);

            // Append the list of invoices
            await importShowCaseInvoices(context);
        }));
    after(() => cleanUp());
});
