import { Test } from '@sage/xtrem-core';
// Import xtrem-scheduler to force injection of batch context hooks
import '@sage/xtrem-scheduler';
import { assert } from 'chai';
import { ShowCaseProduct, sleepMillis } from '../../lib/nodes/show-case-product';

describe('Bulk mutation', () => {
    function startMutation(filter: string) {
        return Test.withReadonlyContext(
            async context => {
                const mutation = `mutation { xtremShowCase { showCaseProduct { testBulkMutation { start(filter: "${filter}") { trackingId } } } } }`;
                const response = await context.executeGraphql<{
                    xtremShowCase: { showCaseProduct: { testBulkMutation: { start: { trackingId: string } } } };
                }>(mutation);
                return response.xtremShowCase.showCaseProduct.testBulkMutation.start.trackingId;
            },
            {
                source: 'graphql',
            },
        );
    }

    async function trackMutation(trackingId: string): Promise<{ status: string; result?: string[]; reason?: string }> {
        const productNames = [] as string[];

        ShowCaseProduct.emitter.on('productBulkMutation', (productName: string) => productNames.push(productName));

        for (let i = 0; i < 20; i += 1) {
            const result = await Test.withReadonlyContext(
                async context => {
                    const query = `query { xtremShowCase { showCaseProduct { testBulkMutation { track(trackingId: "${trackingId}") { status, result, errorMessage } } } } }`;

                    const response = await context.executeGraphql<{
                        xtremShowCase: {
                            showCaseProduct: {
                                testBulkMutation: {
                                    track: { status: string; result: boolean; errorMessage: string };
                                };
                            };
                        };
                    }>(query);
                    return response.xtremShowCase.showCaseProduct.testBulkMutation.track;
                },
                {
                    source: 'graphql',
                },
            );

            switch (result.status) {
                case 'success':
                    return { status: 'success', result: productNames };
                case 'pending':
                case 'running':
                    break;
                case 'error':
                    assert.fail(`got error: ${result.errorMessage}`);
                    break;
                case 'stopRequested':
                    break;
                case 'stopped':
                    return { status: 'stopped', result: productNames, reason: result.errorMessage };
                default:
                    assert.fail(`bad status: ${result.status}`);
            }
            await sleepMillis(100);
        }
        throw new Error('polling loop did not return result');
    }

    async function runMutation(filter: string) {
        const trackingId = await startMutation(filter);
        return trackMutation(trackingId);
    }

    function stopMutation(trackingId: string, reason: string) {
        return Test.withReadonlyContext(
            async context => {
                const mutation = `mutation { xtremShowCase { showCaseProduct { testBulkMutation { stop(trackingId: "${trackingId}", reason: "${reason}") } } } }`;
                const response = await context.executeGraphql<{
                    xtremShowCase: { showCaseProduct: { testBulkMutation: { stop: boolean } } };
                }>(mutation);
                return response.xtremShowCase.showCaseProduct.testBulkMutation.stop;
            },
            {
                source: 'graphql',
            },
        );
    }

    it('can execute a bulk mutation', async () => {
        assert.deepEqual(await runMutation('{ _id: { _lt: 3 } }'), {
            status: 'success',
            result: ['Spinach - Baby', 'Veal Inside - Provimi'],
        });
    });

    it('can stop a bulk mutation', async () => {
        const trackingId = await startMutation('{}');
        setTimeout(
            () =>
                stopMutation(trackingId, 'testing').then(
                    result => assert.isTrue(result),
                    // eslint-disable-next-line no-console
                    err => console.error(err),
                ),
            500,
        );
        const response = await trackMutation(trackingId);

        assert.include(response, { status: 'stopped', reason: 'testing' });
        const processed = response.result || [];
        // We have a 100 ms sleep in the item mutation and we stop after 500 ms so we can expected between 3 and 5 processed items before the stop
        assert.isAtLeast(processed.length, 3);
        assert.isAtMost(processed.length, 5);
    });
});
