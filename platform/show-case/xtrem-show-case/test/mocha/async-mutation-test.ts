import { Test } from '@sage/xtrem-core';
// Import xtrem-scheduler to force injection of batch context hooks
import '@sage/xtrem-scheduler';
import { assert } from 'chai';
import { sleepMillis } from '../../lib/nodes/show-case-product';

describe('Async mutation', () => {
    function startMutation() {
        return Test.withReadonlyContext(
            async context => {
                const mutation =
                    'mutation { xtremShowCase { showCaseCustomer { testAsyncMutation { start(customer: {name:"As<PERSON> Customer",contactPerson:"<PERSON>",email:"<EMAIL>"}) { trackingId } } } } }';
                const response = await context.executeGraphql<{
                    xtremShowCase: { showCaseCustomer: { testAsyncMutation: { start: { trackingId: string } } } };
                }>(mutation);
                return response.xtremShowCase.showCaseCustomer.testAsyncMutation.start.trackingId;
            },
            {
                source: 'graphql',
            },
        );
    }

    async function trackMutation(trackingId: string): Promise<{ status: string; result?: string; reason?: string }> {
        for (let i = 0; i < 50; i += 1) {
            const result = await Test.withReadonlyContext(
                async context => {
                    const query = `query { xtremShowCase { showCaseCustomer { testAsyncMutation { track(trackingId: "${trackingId}") { status, result, errorMessage } } } } }`;

                    const response = await context.executeGraphql<{
                        xtremShowCase: {
                            showCaseCustomer: {
                                testAsyncMutation: {
                                    track: { status: string; result: string; errorMessage: string };
                                };
                            };
                        };
                    }>(query);
                    return response.xtremShowCase.showCaseCustomer.testAsyncMutation.track;
                },
                {
                    source: 'graphql',
                },
            );
            switch (result.status) {
                case 'success':
                    return { status: 'success', result: result.result };
                case 'pending':
                case 'running':
                    break;
                case 'error':
                    assert.fail(`got error: ${result.errorMessage}`);
                    break;
                case 'stopRequested':
                    break;
                case 'stopped':
                    return { status: 'stopped', result: '', reason: result.errorMessage };
                default:
                    assert.fail(`bad status: ${result.status}`);
            }
            await sleepMillis(100);
        }
        throw new Error('polling loop did not return result');
    }

    async function runMutation() {
        const trackingId = await startMutation();
        return trackMutation(trackingId);
    }

    it('can execute an async mutation', async () => {
        assert.deepEqual(await runMutation(), {
            status: 'success',
            result: 'Async Customer',
        });
    });
});
