{"fromVersion": "48.0.29", "toVersion": "48.0.30", "gitHead": "35ed75c3f1075aebc79fab472513c27bcb1e08c1", "commands": [{"isSysPool": true, "sql": ["", "", "CREATE EXTENSION IF NOT EXISTS pgcrypto;", ""]}, {"isSysPool": true, "sql": ["", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.get_config(setting_name varchar)", "RETURNS varchar AS", "$$", "declare", "    setting_value varchar;", "BEGIN", "    SELECT current_setting(setting_name) into setting_value;", "    RETURN setting_value;", "EXCEPTION", "    WHEN OTHERS THEN", "    RETURN '';", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.drop_triggers(schema_name varchar, name_filter varchar DEFAULT '') RETURNS integer AS", "$$", "DECLARE", "    triggerRecord RECORD;", "    record_count integer;", "begin", "\trecord_count = 0;", "    FOR triggerRecord IN", "    \tSELECT trigger_name, event_object_table", "    \tFROM information_schema.triggers", "    \tWHERE trigger_schema = schema_name AND (name_filter = '' OR event_object_table = name_filter)", "\tLOOP", "\t\trecord_count = record_count + 1;", "        EXECUTE 'DROP TRIGGER ' || triggerRecord.trigger_name || ' ON ' || schema_name || '.\"' || triggerRecord.event_object_table || '\";';", "    END LOOP;", "", "    RETURN record_count;", "END;", "$$ LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.drop_notify_functions(schema_name varchar, name_filter varchar DEFAULT '') RETURNS integer AS", "$$", "DECLARE", "    triggerRecord RECORD;", "    record_count integer;", "BEGIN", "    record_count = 0;", "    FOR triggerRecord IN", "    SELECT routine_name", "    FROM information_schema.routines", "    WHERE specific_schema = schema_name and routine_name IN (name_filter || '_notify_deleted', name_filter || '_notify_created', name_filter || '_notify_updated')", "    LOOP", "        record_count = record_count + 1;", "        EXECUTE 'DROP FUNCTION ' || schema_name || '.' || triggerRecord.routine_name || ';';", "    END LOOP;", "", "    RETURN record_count;", "END;", "$$ LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.insert_table()", "RETURNS TRIGGER", "AS", "$$", "    DECLARE", "        USER_ID VARCHAR;", "    BEGIN", "        SELECT %%SCHEMA_NAME%%.get_config('xtrem.transaction_user_id') INTO USER_ID;", "        IF (USER_ID != '') THEN", "            NEW._create_user :=  CAST(USER_ID AS INT8);", "            NEW._update_user :=  CAST(USER_ID AS INT8);", "        END IF;", "        IF (NEW._create_stamp IS NULL) THEN", "            NEW._create_stamp := NOW();", "        END IF;", "        IF (NEW._update_stamp IS NULL) THEN", "            NEW._update_stamp := NOW();", "        END IF;", "        IF (NEW._update_tick IS NULL) THEN", "            NEW._update_tick := 1;", "        END IF;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.insert_shared_table()", "RETURNS TRIGGER", "AS", "$$", "    BEGIN", "        IF (NEW._create_stamp IS NULL) THEN", "            NEW._create_stamp := NOW();", "        END IF;", "        IF (NEW._update_stamp IS NULL) THEN", "            NEW._update_stamp := NOW();", "        END IF;", "        IF (NEW._update_tick IS NULL) THEN", "            NEW._update_tick := 1;", "        END IF;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.update_table()", "RETURNS TRIGGER", "AS", "$$", "    DECLARE", "        USER_ID VARCHAR;", "    BEGIN", "        SELECT %%SCHEMA_NAME%%.get_config('xtrem.transaction_user_id') INTO USER_ID;", "        IF (USER_ID != '') THEN", "            NEW._update_user :=  CAST(USER_ID AS INT8);", "        END IF;", "        NEW._update_stamp := NOW();", "        NEW._update_tick := OLD._update_tick + 1;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.update_shared_table()", "RETURNS TRIGGER", "AS", "$$", "    BEGIN", "        NEW._update_stamp := NOW();", "        NEW._update_tick := OLD._update_tick + 1;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.set_sync_tick()", "RETURNS TRIGGER", "AS", "$$", "    BEGIN", "        NEW._sync_tick :=  pg_current_xact_id();", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.signedInt32(a bigint)", "RETURNS bigint AS", "$$", "DECLARE", "BEGIN", "\t-- Convert to 32 bit signed (if leftmost bit is 1, it's a negative number)", "  \tIF (a > 2^31) THEN", "    \tRETURN a - (2^32)::bigint;", "  \tEND IF;", "  \tRETURN a;", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.imul(a bigint, b bigint)", "RETURNS bigint AS", "$$", "DECLARE", "    aHi bigint;", "    aLo bigint;", "    bHi bigint;", "    bLo bigint;", "    res bigint;", "BEGIN", "    aHi = %%SCHEMA_NAME%%.zeroFillShift(a, 16) & 65535;", "    aLo = a & 65535;", "    bHi = %%SCHEMA_NAME%%.zeroFillShift(b, 16) & 65535;", "    bLo = b & 65535;", "    res = ((aLo * bLo) + %%SCHEMA_NAME%%.zeroFillShift(((aHi * bLo + aLo * bHi) << 16) % (2^32)::bigint, 0)) | 0;", "    RETURN %%SCHEMA_NAME%%.signedInt32(res);", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.zeroFillShift(a bigint, b int)", "RETURNS bigint AS", "$$", "DECLARE", "  \tres bigint;", "BEGIN", "\tIF (a < 0) THEN", "\t\tres = a + 2^32;", "\tELSE", "\t\tres = a;", "\tEND IF;", "\tres = res >> b;", "\tRETURN res;", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.nanoid(\"size\" int4 DEFAULT 21)", "    RETURNS text", "    LANGUAGE plpgsql", "    STABLE", "    AS", "    $$", "    DECLARE", "        id text := '';", "        i int := 0;", "        urlAl<PERSON><PERSON> char(64) := 'ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW';", "        bytes bytea;", "        byte int;", "        pos int;", "    BEGIN", "        SELECT gen_random_bytes(size) INTO bytes;", "    WHILE i < size LOOP", "        byte := get_byte(bytes, i);", "        pos := (byte & 63) + 1; -- + 1 because substr starts at 1", "        id := id || substr(urlAlphabet, pos, 1);", "        i = i + 1;", "    END LOOP;", "    RETURN id;", "    END", "    $$", "    ;", " ", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.audit_table()", "RETURNS TRIGGER", "AS", "$$", "    DECLARE", "        is_audit_enabled BOOLEAN;", "        p_root_table_name VARCHAR;", "        login_email VARCHAR;", "        user_id INT8;", "        log_record RECORD;", "    BEGIN", "        SELECT %%SCHEMA_NAME%%.get_config('xtrem.is_audit_enabled') INTO is_audit_enabled;", "        IF NOT is_audit_enabled THEN", "            RETURN NEW;", "        END IF;", "        p_root_table_name := TG_ARGV[0];", "        SELECT %%SCHEMA_NAME%%.get_config('xtrem.login_email') INTO login_email;", "        SELECT %%SCHEMA_NAME%%.get_config('xtrem.transaction_user_id')::INT8 INTO user_id;", "        SELECT * FROM %%SCHEMA_NAME%%.sys_audit_log", "        WHERE root_table_name = p_root_table_name", "            AND record_id = COALESCE(NEW._id, OLD._id)", "            AND transaction_id::TEXT = pg_current_xact_id()::TEXT", "        INTO log_record;", "        IF log_record IS NULL THEN", "            RAISE NOTICE 'Inserting new audit log record %:%', p_root_table_name, NEW._id;", "            IF p_root_table_name = TG_TABLE_NAME THEN", "                INSERT INTO %%SCHEMA_NAME%%.sys_audit_log (root_table_name, _tenant_id, record_id, operation, login_email, timestamp, transaction_id, record_data, old_update_tick, new_update_tick, _create_user, _update_user)", "                VALUES (p_root_table_name, COALESCE(NEW._tenant_id, OLD._tenant_id), COALESCE(NEW._id, OLD._id), TG_OP::%%SCHEMA_NAME%%.audit_operation_enum, login_email, NOW(), pg_current_xact_id(), to_json(NEW), OLD._update_tick, NEW._update_tick, user_id, user_id);", "            ELSE", "                INSERT INTO %%SCHEMA_NAME%%.sys_audit_log (root_table_name, _tenant_id, record_id, operation, login_email, timestamp, transaction_id, record_data, old_update_tick, new_update_tick)", "                VALUES (p_root_table_name, COALESCE(NEW._tenant_id, OLD._tenant_id), COALESCE(NEW._id, OLD._id), TG_OP::%%SCHEMA_NAME%%.audit_operation_enum, login_email, NOW(), pg_current_xact_id(), to_json(NEW), NULL, NULL);", "            END IF;", "            RAISE NOTICE 'Inserted  new audit log record root_table=%, table=%, _id=%', p_root_table_name, TG_TABLE_NAME, NEW._id;", "        ELSE", "            RAISE NOTICE 'Updating audit log record %:%', p_root_table_name, NEW._id;", "            UPDATE %%SCHEMA_NAME%%.sys_audit_log", "            SET record_data = log_record.record_data || to_jsonb(NEW)", "            WHERE root_table_name = p_root_table_name", "                AND record_id = NEW._id", "                AND transaction_id = pg_current_xact_id()::TEXT;", "            RAISE NOTICE 'Updated  audit log record %:%', p_root_table_name, NEW._id;", "        END IF;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", ""]}, {"isSysPool": true, "sql": "DROP INDEX IF EXISTS %%SCHEMA_NAME%%.sys_audit_log_ind0;"}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.workflow_process ADD COLUMN IF NOT EXISTS execution_user INT8;", "COMMENT ON COLUMN %%SCHEMA_NAME%%.workflow_process.execution_user IS '{", "  \"type\": \"reference\",", "  \"isSystem\": false,", "  \"targetTableName\": \"user\",", "  \"isSelfReference\": false", "}';"]}, {"isSysPool": false, "sql": "DELETE FROM %%SCHEMA_NAME%%.workflow_process;", "actionDescription": "Resets the workflow_process table"}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS workflow_definition_audit ON %%SCHEMA_NAME%%.workflow_definition;", "DROP TRIGGER IF EXISTS workflow_definition_audit ON %%SCHEMA_NAME%%.workflow_definition;", "DROP TRIGGER IF EXISTS workflow_definition_audit ON %%SCHEMA_NAME%%.workflow_definition;"], "args": []}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.workflow_process ADD CONSTRAINT \"workflow_process_execution_user_fk\" FOREIGN KEY(_tenant_id,execution_user) REFERENCES %%SCHEMA_NAME%%.user(_tenant_id,_id) ON DELETE RESTRICT DEFERRABLE DEFERRABLE;", "COMMENT ON CONSTRAINT workflow_process_execution_user_fk ON %%SCHEMA_NAME%%.workflow_process IS '{", "  \"targetTableName\": \"user\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"execution_user\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"restrict\",", "  \"isDeferrable\": true", "}';;"]}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.workflow_process ALTER COLUMN execution_user SET NOT NULL;"}, {"isSysPool": true, "sql": "CREATE UNIQUE INDEX sys_audit_log_ind0 ON %%SCHEMA_NAME%%.sys_audit_log(_tenant_id ASC,root_table_name ASC,record_id ASC,COALESCE(new_update_tick, (- ((2)::bigint ^ (62)::bigint))::bigint) ASC);"}, {"action": "reload_setup_data", "args": {"factory": "SysEnumTransformation"}}, {"action": "reload_setup_data", "args": {"factory": "SysEnumMapping"}}, {"isSysPool": true, "sql": ["COMMENT ON CONSTRAINT workflow_process_execution_user_fk ON %%SCHEMA_NAME%%.workflow_process IS '{", "  \"targetTableName\": \"user\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"execution_user\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"restrict\",", "  \"isDeferrable\": true", "}';"]}], "data": {"SysEnumTransformation": {"metadata": {"rootFactoryName": "SysEnumTransformation", "name": "SysEnumTransformation", "naturalKeyColumns": ["_tenant_id", "local_enum", "remote_app", "remote_app_version", "id"], "columns": [{"name": "_vendor", "type": "reference", "isNullable": true, "targetFactoryName": "SysVendor"}, {"name": "id", "type": "string"}, {"name": "local_enum", "type": "reference", "targetFactoryName": "MetaDataType"}, {"name": "is_active", "type": "boolean", "isOwnedByCustomer": true}, {"name": "remote_app", "type": "reference", "targetFactoryName": "SysApp"}, {"name": "remote_app_version", "type": "string"}, {"name": "remote_enum", "type": "string"}]}, "rows": []}, "SysEnumMapping": {"metadata": {"isVitalChild": true, "isVitalCollectionChild": true, "rootFactoryName": "SysEnumMapping", "name": "SysEnumMapping", "naturalKeyColumns": ["_tenant_id", "transform", "local_enum_value", "remote_enum_value"], "columns": [{"name": "_vendor", "type": "reference", "isNullable": true, "targetFactoryName": "SysVendor"}, {"name": "_sort_value", "type": "integer"}, {"name": "transform", "type": "reference", "targetFactoryName": "SysEnumTransformation"}, {"name": "local_enum_value", "type": "string"}, {"name": "remote_enum_value", "type": "string"}], "vitalParentColumn": {"name": "transform", "type": "reference", "targetFactoryName": "SysEnumTransformation"}}, "rows": []}}}