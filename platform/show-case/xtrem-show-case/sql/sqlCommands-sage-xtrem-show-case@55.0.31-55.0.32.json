{"fromVersion": "55.0.31", "toVersion": "55.0.32", "gitHead": "973c2b045cc3062fd5c86e1e7477c6387ee36bb1", "commands": [{"isSysPool": true, "sql": ["", "", "CREATE EXTENSION IF NOT EXISTS pgcrypto;", ""]}, {"isSysPool": true, "sql": ["", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.get_config(setting_name varchar)", "RETURNS varchar AS", "$$", "declare", "    setting_value varchar;", "BEGIN", "    SELECT current_setting(setting_name) into setting_value;", "    RETURN setting_value;", "EXCEPTION", "    WHEN OTHERS THEN", "    RETURN NULL;", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.drop_triggers(schema_name varchar, name_filter varchar DEFAULT '') RETURNS integer AS", "$$", "DECLARE", "    triggerRecord RECORD;", "    record_count integer;", "begin", "\trecord_count = 0;", "    FOR triggerRecord IN", "    \tSELECT trigger_name, event_object_table", "    \tFROM information_schema.triggers", "    \tWHERE trigger_schema = schema_name AND (name_filter = '' OR event_object_table = name_filter)", "\tLOOP", "\t\trecord_count = record_count + 1;", "        EXECUTE 'DROP TRIGGER ' || triggerRecord.trigger_name || ' ON ' || schema_name || '.\"' || triggerRecord.event_object_table || '\";';", "    END LOOP;", "", "    RETURN record_count;", "END;", "$$ LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.drop_notify_functions(schema_name varchar, name_filter varchar DEFAULT '') RETURNS integer AS", "$$", "DECLARE", "    triggerRecord RECORD;", "    record_count integer;", "BEGIN", "    record_count = 0;", "    FOR triggerRecord IN", "    SELECT routine_name", "    FROM information_schema.routines", "    WHERE specific_schema = schema_name and routine_name IN (name_filter || '_notify_deleted', name_filter || '_notify_created', name_filter || '_notify_updated')", "    LOOP", "        record_count = record_count + 1;", "        EXECUTE 'DROP FUNCTION ' || schema_name || '.' || triggerRecord.routine_name || ';';", "    END LOOP;", "", "    RETURN record_count;", "END;", "$$ LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.insert_table()", "RETURNS TRIGGER", "AS", "$$", "    DECLARE", "        USER_ID VARCHAR;", "    BEGIN", "        SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.transaction_user_id'), '') INTO USER_ID;", "        IF (USER_ID <> '') THEN", "            NEW._create_user :=  CAST(USER_ID AS INT8);", "            NEW._update_user :=  CAST(USER_ID AS INT8);", "        END IF;", "        IF (NEW._create_stamp IS NULL) THEN", "            NEW._create_stamp := NOW();", "        END IF;", "        IF (NEW._update_stamp IS NULL) THEN", "            NEW._update_stamp := NOW();", "        END IF;", "        IF (NEW._update_tick IS NULL) THEN", "            NEW._update_tick := 1;", "        END IF;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.insert_shared_table()", "RETURNS TRIGGER", "AS", "$$", "    BEGIN", "        IF (NEW._create_stamp IS NULL) THEN", "            NEW._create_stamp := NOW();", "        END IF;", "        IF (NEW._update_stamp IS NULL) THEN", "            NEW._update_stamp := NOW();", "        END IF;", "        IF (NEW._update_tick IS NULL) THEN", "            NEW._update_tick := 1;", "        END IF;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.update_table()", "RETURNS TRIGGER", "AS", "$$", "    DECLARE", "        USER_ID VARCHAR;", "    BEGIN", "        SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.transaction_user_id'), '') INTO USER_ID;", "        IF (USER_ID <> '') THEN", "            NEW._update_user :=  CAST(USER_ID AS INT8);", "        END IF;", "        NEW._update_stamp := NOW();", "        NEW._update_tick := OLD._update_tick + 1;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.update_shared_table()", "RETURNS TRIGGER", "AS", "$$", "    BEGIN", "        NEW._update_stamp := NOW();", "        NEW._update_tick := OLD._update_tick + 1;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.set_sync_tick()", "RETURNS TRIGGER", "AS", "$$", "    BEGIN", "        NEW._sync_tick :=  pg_current_xact_id();", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.signedInt32(a bigint)", "RETURNS bigint AS", "$$", "DECLARE", "BEGIN", "\t-- Convert to 32 bit signed (if leftmost bit is 1, it's a negative number)", "  \tIF (a > 2^31) THEN", "    \tRETURN a - (2^32)::bigint;", "  \tEND IF;", "  \tRETURN a;", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.imul(a bigint, b bigint)", "RETURNS bigint AS", "$$", "DECLARE", "    aHi bigint;", "    aLo bigint;", "    bHi bigint;", "    bLo bigint;", "    res bigint;", "BEGIN", "    aHi = %%SCHEMA_NAME%%.zeroFillShift(a, 16) & 65535;", "    aLo = a & 65535;", "    bHi = %%SCHEMA_NAME%%.zeroFillShift(b, 16) & 65535;", "    bLo = b & 65535;", "    res = ((aLo * bLo) + %%SCHEMA_NAME%%.zeroFillShift(((aHi * bLo + aLo * bHi) << 16) % (2^32)::bigint, 0)) | 0;", "    RETURN %%SCHEMA_NAME%%.signedInt32(res);", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.zeroFillShift(a bigint, b int)", "RETURNS bigint AS", "$$", "DECLARE", "  \tres bigint;", "BEGIN", "\tIF (a < 0) THEN", "\t\tres = a + 2^32;", "\tELSE", "\t\tres = a;", "\tEND IF;", "\tres = res >> b;", "\tRETURN res;", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.nanoid(\"size\" int4 DEFAULT 21)", "    RETURNS text", "    LANGUAGE plpgsql", "    STABLE", "    AS", "    $$", "    DECLARE", "        id text := '';", "        i int := 0;", "        urlAl<PERSON><PERSON> char(64) := 'ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW';", "        bytes bytea;", "        byte int;", "        pos int;", "    BEGIN", "        SELECT gen_random_bytes(size) INTO bytes;", "    WHILE i < size LOOP", "        byte := get_byte(bytes, i);", "        pos := (byte & 63) + 1; -- + 1 because substr starts at 1", "        id := id || substr(urlAlphabet, pos, 1);", "        i = i + 1;", "    END LOOP;", "    RETURN id;", "    END", "    $$", "    ;", " ", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.audit_table()", "RETURNS TRIGGER", "AS", "$$", "    DECLARE", "        -- parameters", "        p_root_table_name VARCHAR;", "        p_constructor VARCHAR;", "", "        -- audit variables", "        is_audit_enabled VARCHAR;", "        tenant_id VARCHAR;", "        rid INT8;", "        login_email VARCHAR;", "        user_id VARCHAR;", "        locale VARCHAR;", "        log_record RECORD;", "", "        -- notify variables", "        origin_id VARCHAR;", "        notify_all_disabled VARCHAR;", "        notify_tenant_disabled VARCHAR;", "        notification_id VARCHAR;", "        user_email VARCHAR;", "        constructor VARCHAR;", "        event VARCHAR;", "        topic VARCHAR;", "        envelope VARCHAR;", "        payload VARCHAR;", "    BEGIN", "        p_root_table_name := TG_ARGV[0];", "        p_constructor := TG_ARGV[1];", "", "        SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.is_audit_enabled'), 'false') INTO is_audit_enabled;", "        IF (is_audit_enabled <> 'true') THEN", "            RETURN NEW;", "        END IF;", "        SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.login_email'), '') INTO login_email;", "        SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.transaction_user_id'), '') INTO user_id;", "        SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.locale'), 'base') INTO locale;", "", "        tenant_id := COALESCE(NEW._tenant_id, OLD._tenant_id);", "        rid := COALESCE(NEW._id, OLD._id);", "", "        IF p_constructor != '' THEN", "            constructor := p_constructor;", "        ELSE", "            constructor := COALESCE(NEW._constructor, OLD._constructor);", "        END IF;", "", "", "        SELECT * FROM %%SCHEMA_NAME%%.sys_audit_log", "        WHERE root_table_name = p_root_table_name", "            AND record_id = rid", "            AND transaction_id::TEXT = pg_current_xact_id()::TEXT", "        INTO log_record;", "", "        IF log_record IS NULL THEN", "            RAISE NOTICE 'Inserting new audit log record %:%', p_root_table_name, NEW._id;", "            IF p_root_table_name = TG_TABLE_NAME THEN", "                INSERT INTO %%SCHEMA_NAME%%.sys_audit_log (node_name, root_table_name, _tenant_id, record_id, operation, login_email, timestamp, transaction_id, record_data, old_update_tick, new_update_tick, _create_user, _update_user)", "                VALUES (constructor, p_root_table_name, tenant_id, rid, TG_OP::%%SCHEMA_NAME%%.audit_operation_enum, login_email, NOW(), pg_current_xact_id(), to_json(NEW), OLD._update_tick, NEW._update_tick, user_id::INT8, user_id::INT8);", "            ELSE", "                INSERT INTO %%SCHEMA_NAME%%.sys_audit_log (node_name, root_table_name, _tenant_id, record_id, operation, login_email, timestamp, transaction_id, record_data, old_update_tick, new_update_tick)", "                VALUES (constructor, p_root_table_name, tenant_id, rid, TG_OP::%%SCHEMA_NAME%%.audit_operation_enum, login_email, NOW(), pg_current_xact_id(), to_json(NEW), NULL, NULL);", "            END IF;", "            RAISE NOTICE 'Inserted  new audit log record root_table=%, table=%, _id=%', p_root_table_name, TG_TABLE_NAME, NEW._id;", "        ELSE", "            RAISE NOTICE 'Updating audit log record %:%', p_root_table_name, NEW._id;", "            UPDATE %%SCHEMA_NAME%%.sys_audit_log", "            SET record_data = log_record.record_data || to_jsonb(NEW)", "            WHERE root_table_name = p_root_table_name", "                AND record_id = NEW._id", "                AND transaction_id = pg_current_xact_id()::TEXT;", "            RAISE NOTICE 'Updated  audit log record %:%', p_root_table_name, NEW._id;", "        END IF;", "", "        IF p_root_table_name = TG_TABLE_NAME THEN", "            SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.notification.disable.ALL'), 'false') INTO notify_all_disabled;", "            SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.notification.disable.t_' || tenant_id), 'false') INTO notify_tenant_disabled;", "", "            IF (notify_all_disabled <> 'true' and notify_tenant_disabled <> 'true') THEN", "                SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.origin_id'), '') INTO origin_id;", "                SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.user_email'), '') INTO user_email;", "                SELECT %%SCHEMA_NAME%%.nanoid() INTO notification_id;", "", "                CASE TG_OP", "                    WHEN 'INSERT' THEN event := 'created';", "                    WHEN 'UPDATE' THEN event := 'updated';", "                    WHEN 'DELETE' THEN event := 'deleted';", "                END CASE;", "", "                topic := constructor || '/' || event;", "                payload := '{ \"_id\":' || rid || ', \"_updateTick\":' || COALESCE(NEW._update_tick, OLD._update_tick) || '}';", "", "                RAISE NOTICE 'Inserted new notification %:%', topic, notification_id;", "                INSERT INTO %%SCHEMA_NAME%%.sys_notification", "                    (tenant_id, origin_id, notification_id, reply_id, reply_topic, user_email, login, locale,", "                    topic, payload, status, _source_id, _update_tick, _create_stamp, _update_stamp)", "                VALUES (tenant_id, origin_id, notification_id, '', '', user_email, login_email, locale,", "                    topic, payload, 'pending', '', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);", "", "                RAISE NOTICE 'Notifying %:%', TG_OP, event;", "                PERFORM pg_notify('notification_queued', '{\"data\":\"{\\\"topic\\\":\\\"' || event || '\\\"}\"}');", "            END IF;", "        END IF;", "", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", ""]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.sys_audit_log ADD COLUMN IF NOT EXISTS node_name VARCHAR(80) COLLATE \"und-x-icu\" DEFAULT '';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.sys_audit_log.node_name IS '{", "  \"type\": \"string\",", "  \"isSystem\": false,", "  \"maxLength\": 80", "}';"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_audit_log AS t0 SET node_name=$1 WHERE (((t0.node_name IS NULL) OR (t0.node_name = $2)))", "args": ["", ""], "actionDescription": "Auto data action for property SysAuditLog.nodeName"}, {"isSysPool": true, "sql": "ALTER TYPE %%SCHEMA_NAME%%.workflow_definition_status_enum ADD VALUE IF NOT EXISTS 'test'  BEFORE 'draft' ;"}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.workflow_definition RENAME ", "            draft_user TO test_user"], "actionDescription": "WorkflowDefinition: rename property 'draftUser' to 'testUser'"}, {"isSysPool": true, "sql": ["DO", "            $$", "            BEGIN", "                ALTER TABLE %%SCHEMA_NAME%%.workflow_definition RENAME CONSTRAINT \"workflow_definition_draft_user_fk\" TO \"workflow_definition_test_user_fk\";", "            exception ", "                   when undefined_object then", "            end;", "            $$"], "actionDescription": "WorkflowDefinition: rename property 'draftUser' to 'testUser'"}, {"isSysPool": false, "sql": ["", "        DO $$", "        BEGIN", "            UPDATE %%SCHEMA_NAME%%.sys_audit_log SET node_name =", "                CASE", "                    WHEN record_data->'_constructor' IS NOT NULL THEN (record_data::jsonb->>'_constructor')::text", "                    ELSE REPLACE(INITCAP(REPLACE(root_table_name, '_', ' ')), ' ', '')", "                END;", "        END; $$;", "        "], "actionDescription": "Initialize value of node_name column in sys_audit_log table"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sys_audit_log ALTER COLUMN node_name SET NOT NULL;"}, {"isSysPool": true, "sql": "UPDATE %%SCHEMA_NAME%%.workflow_definition SET status = CASE status::TEXT WHEN 'draft' THEN 'test' ELSE  status END WHERE status::TEXT IN ('draft');"}, {"isSysPool": true, "sql": "ALTER TYPE %%SCHEMA_NAME%%.workflow_definition_status_enum RENAME TO workflow_definition_status_enum__old"}, {"isSysPool": true, "sql": ["", "                DO $$", "                    BEGIN", "                        IF NOT EXISTS (", "                        SELECT 1 FROM pg_type t", "                        LEFT JOIN pg_namespace p ON t.typnamespace=p.oid", "                        WHERE t.typname='workflow_definition_status_enum' AND p.nspname='%%SCHEMA_NAME%%'", "                        ) THEN", "                            CREATE TYPE %%SCHEMA_NAME%%.workflow_definition_status_enum AS ENUM('test','production');", "                        END IF;", "                    END", "                $$;", "                "]}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.workflow_definition ALTER COLUMN status TYPE %%SCHEMA_NAME%%.workflow_definition_status_enum USING status::TEXT::%%SCHEMA_NAME%%.workflow_definition_status_enum;"}, {"isSysPool": true, "sql": "DROP TYPE IF EXISTS %%SCHEMA_NAME%%.workflow_definition_status_enum__old  ;"}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["allow to display changelog in the app", "experimental", false, "@sage/xtrem-system", false, "changelog"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["enable dev-only features", "experimental", false, "@sage/xtrem-system", false, "devTools"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Is a demo tenant", "released", false, "@sage/xtrem-system", false, "isDemoTenant"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Enable PIN code authentication feature", "released", false, "@sage/xtrem-system", false, "sysDeviceToken"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Tags (not yet released)", "experimental", false, "@sage/xtrem-system", false, "tags"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Notification center", "experimental", false, "@sage/xtrem-communication", false, "notificationCenter"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Authorization access control", "released", false, "@sage/xtrem-authorization", false, "authorizationServiceOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Auditing option", "experimental", false, "@sage/xtrem-auditing", false, "auditing"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Auditing option", "workInProgress", true, "@sage/xtrem-auditing", false, "auditingOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Workflow option", "experimental", false, "@sage/xtrem-workflow", false, "workflow"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Workflow advanced features (not yet released)", "workInProgress", false, "@sage/xtrem-workflow", false, "workflowAdvanced"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Workflow option (obsolete)", "workInProgress", true, "@sage/xtrem-workflow", false, "workflowOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Synchronization", "released", false, "@sage/xtrem-interop", true, "synchronizationServiceOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["enable ReportAssignment feature", "workInProgress", false, "@sage/xtrem-reporting", false, "reportAssignment"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["showCase discount option", "experimental", false, "@sage/xtrem-show-case", false, "showCaseDiscountOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["experimental option", "experimental", false, "@sage/xtrem-show-case", false, "showCaseExperimentalOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["showCaseOption's hight level description", "released", false, "@sage/xtrem-show-case", false, "showCaseOptionHighLevel"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["showCaseOption1's description", "released", true, "@sage/xtrem-show-case", false, "showCaseOption1"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["showCaseOption2's description", "released", true, "@sage/xtrem-show-case", false, "showCaseOption2"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["A workInProgress service option that can be loaded on a development environment", "workInProgress", false, "@sage/xtrem-show-case", true, "showCaseOption3"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Work in progress options cannot be activated", "workInProgress", false, "@sage/xtrem-show-case", false, "showCaseWorkInProgressOption"]}, {"isSysPool": false, "sql": "NOTIFY invalidate_category_cache, '{\"data\":\"{\\\"tenantId\\\":null,\\\"category\\\":\\\"$SHARED_NODE.SysServiceOption\\\"}\",\"containerId\":\"x3-devops00L5L5-74416\",\"excludeSelf\":true}';", "args": []}, {"isSysPool": false, "sql": ["SELECT", "                _id, email, is_active, first_name, last_name,", "                 is_administrator, is_api_user, is_demo_persona, operator_id", "            FROM %%SCHEMA_NAME%%.user WHERE _tenant_id=$1 AND email = $2"], "args": ["777777777777777777777", "<EMAIL>"], "actionDescription": "Reload setup layer for factories Role,RoleActivity,WorkflowStepTemplate,SysJobSchedule"}, {"action": "reload_setup_data", "args": {"factory": "Role"}}, {"action": "reload_setup_data", "args": {"factory": "RoleActivity"}}, {"action": "reload_setup_data", "args": {"factory": "WorkflowStepTemplate"}}, {"action": "reload_setup_data", "args": {"factory": "SysJobSchedule"}}, {"isSysPool": true, "sql": ["COMMENT ON CONSTRAINT workflow_definition_test_user_fk ON %%SCHEMA_NAME%%.workflow_definition IS '{", "  \"targetTableName\": \"user\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"test_user\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"noAction\",", "  \"isDeferrable\": true", "}';"]}], "data": {"Role": {"metadata": {"rootFactoryName": "Role", "name": "Role", "naturalKeyColumns": ["_tenant_id", "id"], "columns": [{"name": "_vendor", "type": "reference", "isNullable": true, "targetFactoryName": "SysVendor"}, {"name": "is_active", "type": "boolean"}, {"name": "name", "type": "string", "isLocalized": true}, {"name": "description", "type": "string"}, {"name": "id", "type": "string"}, {"name": "is_billing_role", "type": "boolean"}]}, "rows": [["sage", "Y", "{\"en\":\"IT manager\"}", null, "100", "N"], ["sage", "Y", "{\"en\":\"Sales manager\"}", null, "200", "N"], ["sage", "Y", "{\"en\":\"Sales administrator\"}", null, "300", "N"], ["sage", "Y", "{\"en\":\"Purchasing manager\"}", null, "400", "N"], ["sage", "Y", "{\"en\":\"Buyer\"}", null, "500", "N"], ["sage", "Y", "{\"en\":\"Inventory manager\"}", null, "600", "N"], ["sage", "Y", "{\"en\":\"Inventory operator\"}", null, "700", "N"], ["sage", "Y", "{\"en\":\"Design/Production engineer\"}", null, "800", "N"], ["sage", "Y", "{\"en\":\"Production manager\"}", null, "900", "N"], ["sage", "Y", "{\"en\":\"Production operator\"}", null, "1000", "N"], ["sage", "Y", "{\"en\":\"Administrator - Technical\"}", null, "Admin - Technical", "N"], ["sage", "Y", "{\"en\":\"Administrator\"}", null, "Admin", "N"], ["sage", "Y", "{\"en\":\"Support Access Read-only\"}", null, "Support User Read-only", "N"], ["sage", "Y", "{\"en\":\"Support Access\"}", null, "Support User", "N"], ["sage", "Y", "{\"en\":\"Operational User\"}", null, "Operational User", "Y"], ["sage", "Y", "{\"en\":\"Business User\"}", null, "Business User", "Y"], ["sage", "Y", "{\"en\":\"Controller/Finance manager\",\"base\":\"Controller/Finance manager\",\"en-US\":\"Controller/Finance manager\"}", null, "1100", "N"]]}, "RoleActivity": {"metadata": {"isVitalChild": true, "isVitalCollectionChild": true, "rootFactoryName": "RoleActivity", "name": "RoleActivity", "naturalKeyColumns": ["_tenant_id", "role", "activity"], "columns": [{"name": "_vendor", "type": "reference", "isNullable": true, "targetFactoryName": "SysVendor"}, {"name": "_sort_value", "type": "integer"}, {"name": "role", "type": "reference", "targetFactoryName": "Role"}, {"name": "activity", "type": "reference", "targetFactoryName": "Activity"}, {"name": "has_all_permissions", "type": "boolean"}, {"name": "permissions", "type": "stringArray"}, {"name": "is_active", "type": "boolean"}], "vitalParentColumn": {"name": "role", "type": "reference", "targetFactoryName": "Role"}}, "rows": [["sage", "17200", "100", "groupRoleSite", "Y", "[]", "Y"], ["sage", "200", "100", "user", "Y", "[]", "Y"], ["sage", "15600", "100", "siteGroup", "Y", "[]", "Y"], ["sage", "15700", "100", "site", "Y", "[]", "Y"], ["sage", "15600", "100", "company", "Y", "[]", "Y"], ["sage", "22200", "100", "serviceOptionState", "Y", "[]", "Y"], ["sage", "3700", "200", "siteGroup", "N", "[\"read\"]", "Y"], ["sage", "3800", "200", "user", "Y", "[]", "Y"], ["sage", "3900", "200", "site", "N", "[\"read\"]", "Y"], ["sage", "3500", "200", "company", "N", "[\"read\"]", "Y"], ["sage", "3600", "200", "sysNotificationHistory", "N", "[\"read\"]", "Y"], ["sage", "3400", "300", "company", "N", "[\"read\"]", "Y"], ["sage", "3900", "400", "siteGroup", "N", "[\"read\"]", "Y"], ["sage", "4000", "400", "site", "N", "[\"read\"]", "Y"], ["sage", "3700", "400", "company", "N", "[\"read\"]", "Y"], ["sage", "3800", "400", "sysNotificationHistory", "N", "[\"read\"]", "Y"], ["sage", "2500", "500", "company", "N", "[\"read\"]", "Y"], ["sage", "6200", "600", "company", "N", "[\"read\"]", "Y"], ["sage", "6300", "700", "company", "N", "[\"read\"]", "Y"], ["sage", "6400", "600", "siteGroup", "N", "[\"read\"]", "Y"], ["sage", "6500", "600", "user", "Y", "[]", "Y"], ["sage", "6600", "600", "site", "N", "[\"read\"]", "Y"], ["sage", "6300", "600", "sysNotificationHistory", "N", "[\"read\"]", "Y"], ["sage", "6500", "800", "siteGroup", "N", "[\"read\"]", "Y"], ["sage", "6600", "800", "user", "Y", "[]", "Y"], ["sage", "6700", "800", "site", "N", "[\"read\"]", "Y"], ["sage", "6400", "800", "sysNotificationHistory", "N", "[\"read\"]", "Y"], ["sage", "3500", "900", "siteGroup", "N", "[\"read\"]", "Y"], ["sage", "6700", "900", "user", "Y", "[]", "Y"], ["sage", "6800", "900", "site", "N", "[\"read\"]", "Y"], ["sage", "3400", "900", "sysNotificationHistory", "N", "[\"read\"]", "Y"], ["sage", "100", "Support User", "role", "Y", "[]", "Y"], ["sage", "200", "Support User", "user", "Y", "[]", "Y"], ["sage", "300", "Support User", "siteGroup", "Y", "[]", "Y"], ["sage", "400", "Support User", "groupRoleSite", "Y", "[]", "Y"], ["sage", "500", "Support User", "supportAccessHistory", "Y", "[]", "Y"], ["sage", "600", "Support User", "site", "Y", "[]", "Y"], ["sage", "21400", "Support User", "company", "Y", "[]", "Y"], ["sage", "21500", "Support User", "tenant", "Y", "[]", "Y"], ["sage", "21400", "Support User", "sysNotificationHistory", "Y", "[]", "Y"], ["sage", "21500", "Support User", "sysNotificationState", "Y", "[]", "Y"], ["sage", "22200", "Support User", "serviceOptionState", "Y", "[]", "Y"], ["sage", "22300", "Support User", "sysTag", "Y", "[]", "Y"], ["sage", "100", "Support User Read-only", "role", "N", "[\"read\"]", "Y"], ["sage", "200", "Support User Read-only", "user", "N", "[\"read\"]", "Y"], ["sage", "300", "Support User Read-only", "siteGroup", "N", "[\"read\"]", "Y"], ["sage", "400", "Support User Read-only", "groupRoleSite", "N", "[\"read\"]", "Y"], ["sage", "500", "Support User Read-only", "supportAccessHistory", "N", "[\"read\"]", "Y"], ["sage", "600", "Support User Read-only", "site", "N", "[\"read\"]", "Y"], ["sage", "21400", "Support User Read-only", "company", "N", "[\"read\"]", "Y"], ["sage", "21500", "Support User Read-only", "tenant", "N", "[\"read\"]", "Y"], ["sage", "21400", "Support User Read-only", "sysNotificationHistory", "N", "[\"read\"]", "Y"], ["sage", "21500", "Support User Read-only", "sysNotificationState", "N", "[\"read\"]", "Y"], ["sage", "22200", "Support User Read-only", "serviceOptionState", "N", "[\"read\"]", "Y"], ["sage", "22300", "Support User Read-only", "sysTag", "N", "[\"read\"]", "Y"], ["sage", "100", "Admin", "role", "Y", "[]", "Y"], ["sage", "200", "Admin", "user", "Y", "[]", "Y"], ["sage", "300", "Admin", "siteGroup", "Y", "[]", "Y"], ["sage", "400", "Admin", "groupRoleSite", "Y", "[]", "Y"], ["sage", "500", "Admin", "supportAccessHistory", "Y", "[]", "Y"], ["sage", "600", "Admin", "site", "Y", "[]", "Y"], ["sage", "700", "Admin", "tenant", "Y", "[]", "Y"], ["sage", "12400", "Admin", "sysNotificationHistory", "Y", "[]", "Y"], ["sage", "12200", "Admin", "company", "Y", "[]", "Y"], ["sage", "12500", "Admin", "sysNotificationState", "Y", "[]", "Y"], ["sage", "13800", "Admin", "serviceOptionState", "Y", "[]", "Y"], ["sage", "13900", "Admin", "sysTag", "Y", "[]", "Y"], ["sage", "13500", "Business User", "role", "Y", "[]", "Y"], ["sage", "13600", "Business User", "user", "Y", "[]", "Y"], ["sage", "13700", "Business User", "siteGroup", "Y", "[]", "Y"], ["sage", "13800", "Business User", "groupRoleSite", "Y", "[]", "Y"], ["sage", "13900", "Business User", "supportAccessHistory", "Y", "[]", "Y"], ["sage", "14200", "Business User", "tenant", "Y", "[]", "Y"], ["sage", "14100", "Business User", "company", "Y", "[]", "Y"], ["sage", "14150", "Business User", "sysNotificationHistory", "Y", "[]", "Y"], ["sage", "14170", "Business User", "sysNotificationState", "Y", "[]", "Y"], ["sage", "14180", "Business User", "site", "Y", "[]", "Y"], ["sage", "17200", "Business User", "serviceOptionState", "Y", "[]", "Y"], ["sage", "530", "1100", "company", "N", "[\"read\",\"create\",\"update\",\"delete\"]", "Y"], ["sage", "15700", "100", "customField", "Y", "[]", "Y"], ["sage", "4300", "200", "customField", "Y", "[]", "Y"], ["sage", "4400", "400", "customField", "Y", "[]", "Y"], ["sage", "7000", "600", "customField", "Y", "[]", "Y"], ["sage", "7100", "800", "customField", "Y", "[]", "Y"], ["sage", "7200", "900", "customField", "Y", "[]", "Y"], ["sage", "21600", "Support User", "customField", "Y", "[]", "Y"], ["sage", "21600", "Support User Read-only", "customField", "N", "[\"read\"]", "Y"], ["sage", "13000", "Admin", "customField", "Y", "[]", "Y"], ["sage", "900", "Business User", "customField", "Y", "[]", "Y"], ["sage", "15500", "100", "dashboardActivity", "N", "[\"read\"]", "Y"], ["sage", "3100", "200", "dashboardActivity", "Y", "[]", "Y"], ["sage", "2100", "300", "dashboardActivity", "Y", "[]", "Y"], ["sage", "3300", "400", "dashboardActivity", "Y", "[]", "Y"], ["sage", "2200", "500", "dashboardActivity", "Y", "[]", "Y"], ["sage", "5900", "600", "dashboardActivity", "Y", "[]", "Y"], ["sage", "6000", "700", "dashboardActivity", "Y", "[]", "Y"], ["sage", "3500", "800", "dashboardActivity", "Y", "[]", "Y"], ["sage", "3000", "900", "dashboardActivity", "N", "[]", "Y"], ["sage", "1900", "1000", "dashboardActivity", "N", "[]", "Y"], ["sage", "80", "Operational User", "dashboardActivity", "Y", "[\"read\"]", "Y"], ["sage", "700", "Admin", "dashboardActivity", "Y", "[]", "Y"], ["sage", "21100", "Support User", "dashboardActivity", "Y", "[]", "Y"], ["sage", "20900", "Support User Read-only", "dashboardActivity", "N", "[\"read\"]", "Y"], ["sage", "1000", "Business User", "dashboardActivity", "Y", "[]", "Y"], ["sage", "10", "1100", "dashboardActivity", "N", "[\"read\",\"manage\"]", "Y"], ["sage", "100", "Admin", "workflowDefinition", "Y", null, "Y"], ["sage", "3000", "200", "sysJobSchedule", "N", "[\"read\"]", "Y"], ["sage", "3200", "400", "sysJobSchedule", "N", "[\"read\"]", "Y"], ["sage", "5400", "600", "sysJobSchedule", "N", "[\"read\"]", "Y"], ["sage", "3400", "800", "sysJobSchedule", "N", "[\"read\"]", "Y"], ["sage", "2900", "900", "sysJobSchedule", "N", "[\"read\"]", "Y"], ["sage", "700", "Admin", "sysJobSchedule", "Y", "[]", "Y"], ["sage", "1500", "Admin - Technical", "sysJobSchedule", "Y", "[]", "Y"], ["sage", "21100", "Support User", "sysJobSchedule", "Y", "[]", "Y"], ["sage", "20900", "Support User Read-only", "sysJobSchedule", "N", "[\"read\"]", "Y"], ["sage", "1700", "Business User", "sysJobSchedule", "Y", "[]", "Y"], ["sage", "3200", "200", "importData", "Y", "[]", "Y"], ["sage", "2200", "300", "importData", "Y", "[]", "Y"], ["sage", "3400", "400", "importData", "Y", "[]", "Y"], ["sage", "2300", "500", "importData", "Y", "[]", "Y"], ["sage", "6000", "600", "importData", "Y", "[]", "Y"], ["sage", "6100", "700", "importData", "Y", "[]", "Y"], ["sage", "3600", "800", "importData", "Y", "[]", "Y"], ["sage", "3100", "900", "importData", "N", "[]", "Y"], ["sage", "2000", "1000", "importData", "N", "[]", "Y"], ["sage", "15500", "100", "importExportTemplate", "Y", "[]", "Y"], ["sage", "3300", "200", "importExportTemplate", "Y", "[]", "Y"], ["sage", "2300", "300", "importExportTemplate", "Y", "[]", "Y"], ["sage", "3500", "400", "importExportTemplate", "Y", "[]", "Y"], ["sage", "2400", "500", "importExportTemplate", "Y", "[]", "Y"], ["sage", "6100", "600", "importExportTemplate", "Y", "[]", "Y"], ["sage", "6200", "700", "importExportTemplate", "Y", "[]", "Y"], ["sage", "6300", "800", "importExportTemplate", "Y", "[]", "Y"], ["sage", "3200", "900", "importExportTemplate", "N", "[]", "Y"], ["sage", "2100", "1000", "importExportTemplate", "N", "[]", "Y"], ["sage", "11800", "Support User", "importData", "Y", "[]", "Y"], ["sage", "12900", "Support User", "importExportTemplate", "Y", "[]", "Y"], ["sage", "11800", "Support User Read-only", "importData", "N", "[\"read\"]", "Y"], ["sage", "12900", "Support User Read-only", "importExportTemplate", "N", "[\"read\"]", "Y"], ["sage", "11900", "Admin", "importData", "Y", "[]", "Y"], ["sage", "12000", "Admin", "importExportTemplate", "Y", "[]", "Y"], ["sage", "1100", "Business User", "importData", "Y", "[]", "Y"], ["sage", "1200", "Business User", "importExportTemplate", "Y", "[]", "Y"], ["sage", "3900", "200", "reportResource", "Y", "[]", "Y"], ["sage", "4000", "200", "reportStyleVariable", "Y", "[]", "Y"], ["sage", "4100", "200", "reportTemplate", "Y", "[]", "Y"], ["sage", "4200", "200", "report", "Y", "[]", "Y"], ["sage", "3500", "300", "reportResource", "Y", "[]", "Y"], ["sage", "3600", "300", "reportStyleVariable", "Y", "[]", "Y"], ["sage", "3700", "300", "reportTemplate", "Y", "[]", "Y"], ["sage", "3800", "300", "report", "Y", "[]", "Y"], ["sage", "4000", "400", "reportResource", "Y", "[]", "Y"], ["sage", "4100", "400", "reportStyleVariable", "Y", "[]", "Y"], ["sage", "4200", "400", "reportTemplate", "Y", "[]", "Y"], ["sage", "4300", "400", "report", "Y", "[]", "Y"], ["sage", "2600", "500", "reportResource", "Y", "[]", "Y"], ["sage", "2700", "500", "reportStyleVariable", "Y", "[]", "Y"], ["sage", "2800", "500", "reportTemplate", "Y", "[]", "Y"], ["sage", "2900", "500", "report", "Y", "[]", "Y"], ["sage", "6600", "600", "reportResource", "Y", "[]", "Y"], ["sage", "6700", "600", "reportStyleVariable", "Y", "[]", "Y"], ["sage", "6800", "600", "reportTemplate", "Y", "[]", "Y"], ["sage", "6900", "600", "report", "Y", "[]", "Y"], ["sage", "6400", "700", "reportResource", "Y", "[]", "Y"], ["sage", "6500", "700", "reportStyleVariable", "Y", "[]", "Y"], ["sage", "6600", "700", "reportTemplate", "Y", "[]", "Y"], ["sage", "6700", "700", "report", "Y", "[]", "Y"], ["sage", "6700", "800", "reportResource", "Y", "[]", "Y"], ["sage", "6800", "800", "reportStyleVariable", "Y", "[]", "Y"], ["sage", "6900", "800", "reportTemplate", "Y", "[]", "Y"], ["sage", "7000", "800", "report", "Y", "[]", "Y"], ["sage", "6800", "900", "reportResource", "Y", "[]", "Y"], ["sage", "6900", "900", "reportStyleVariable", "Y", "[]", "Y"], ["sage", "7000", "900", "reportTemplate", "Y", "[]", "Y"], ["sage", "7100", "900", "report", "Y", "[]", "Y"], ["sage", "2200", "1000", "reportResource", "Y", "[]", "Y"], ["sage", "2300", "1000", "reportStyleVariable", "Y", "[]", "Y"], ["sage", "2400", "1000", "reportTemplate", "Y", "[]", "Y"], ["sage", "2500", "1000", "report", "Y", "[]", "Y"], ["sage", "20000", "Support User", "reportStyleVariable", "Y", "[]", "Y"], ["sage", "20100", "Support User", "reportTemplate", "Y", "[]", "Y"], ["sage", "20200", "Support User", "report", "Y", "[]", "Y"], ["sage", "21300", "Support User", "reportResource", "Y", "[]", "Y"], ["sage", "20000", "Support User Read-only", "reportStyleVariable", "N", "[\"read\"]", "Y"], ["sage", "20100", "Support User Read-only", "reportTemplate", "N", "[\"read\"]", "Y"], ["sage", "20200", "Support User Read-only", "report", "N", "[\"read\"]", "Y"], ["sage", "21100", "Support User Read-only", "reportResource", "N", "[\"read\"]", "Y"], ["sage", "12600", "Admin", "reportResource", "Y", "[]", "Y"], ["sage", "12700", "Admin", "reportStyleVariable", "Y", "[]", "Y"], ["sage", "12800", "Admin", "reportTemplate", "Y", "[]", "Y"], ["sage", "12900", "Admin", "report", "Y", "[]", "Y"], ["sage", "5200", "Admin", "sysEmailConfig", "Y", null, "Y"]]}, "WorkflowStepTemplate": {"metadata": {"rootFactoryName": "WorkflowStepTemplate", "name": "WorkflowStepTemplate", "naturalKeyColumns": ["_tenant_id", "step_constructor", "variant"], "columns": [{"name": "_vendor", "type": "reference", "isNullable": true, "targetFactoryName": "SysVendor"}, {"name": "step_constructor", "type": "string"}, {"name": "variant", "type": "string"}, {"name": "factory", "type": "reference", "isNullable": true, "targetFactoryName": "MetaNodeFactory"}, {"name": "is_active", "type": "boolean"}, {"name": "title", "type": "string", "isLocalized": true, "isOwnedByCustomer": true}, {"name": "description", "type": "string", "isLocalized": true, "isOwnedByCustomer": true}, {"name": "icon", "type": "enum", "isOwnedByCustomer": true, "enumMembers": ["addons", "bright", "clock", "print", "mail", "megaphone", "accounting", "binocular", "database", "pencil", "connected", "hourglass", "undo"]}, {"name": "color", "type": "string", "isOwnedByCustomer": true}, {"name": "service_options", "type": "referenceArray", "targetFactoryName": "MetaServiceOption"}, {"name": "config_data", "type": "json", "isOwnedByCustomer": true}]}, "rows": [["sage", "entity-created", "_generic", null, "Y", "{\"base\":\"<PERSON><PERSON><PERSON> created\"}", "{\"base\":\"This event is triggered when a new instance of the selected entity type is created in the system.\"}", "addons", "335b70ff", "[]", null], ["sage", "entity-deleted", "_generic", null, "Y", "{\"base\":\"<PERSON><PERSON><PERSON> deleted\"}", "{\"base\":\"This event is triggered when an instance of the selected entity type is deleted from the system.\"}", "undo", "000000ff", "[]", null], ["sage", "entity-updated", "_generic", null, "Y", "{\"base\":\"En<PERSON><PERSON> updated\"}", "{\"base\":\"This event is triggered when an instance of the selected entity type is updated in the system.\"}", "bright", "0060a7ff", "[]", null], ["sage", "test-started", "_generic", null, "Y", "{\"base\":\"Test scenario started\"}", "{\"base\":\"This event is triggered when a workflow test scenario starts.\"}", "binocular", "000000ff", "[]", null], ["sage", "condition", "_generic", null, "Y", "{\"base\":\"Condition\"}", "{\"base\":\"Allows the workflow logic to be branched based on a condition.\"}", "connected", "ef6700ff", "[]", null], ["sage", "calculate", "_generic", null, "Y", "{\"base\":\"Calculate\"}", "{\"base\":\"Calculates a value using simple mathematical operations using variable from the previous steps.\"}", "accounting", "335b70ff", "[]", null], ["sage", "delete-entity", "_generic", null, "Y", "{\"base\":\"Delete an entity\"}", "{\"base\":\"Deletes an entity from the database.\"}", "undo", "335b70ff", "[]", null], ["sage", "read-entity", "_generic", null, "Y", "{\"base\":\"Read a record\"}", "{\"base\":\"Reads a record by its ID field and use its value in subsequent steps.\"}", "binocular", "335b70ff", "[]", null], ["sage", "schedule", "_generic", null, "Y", "{\"base\":\"Schedule\"}", "{\"base\":\"This event is triggered on based on a predefined schedule.\"}", "clock", "0060a7ff", "[]", null], ["sage", "send-user-notification", "_generic", null, "Y", "{\"base\":\"Send a notification\"}", "{\"base\":\"This action sends a notification to a set of users.\"}", "megaphone", "335b70ff", "[]", null], ["sage", "test-stub", "_generic", null, "Y", "{\"base\":\"Test stub\"}", "{\"base\":\"Ends the test scenario.\"}", "megaphone", "335b70ff", "[]", null], ["sage", "update-entity", "_generic", null, "Y", "{\"base\":\"Update an entity\"}", "{\"base\":\"Updates a record in the database.\"}", "pencil", "335b70ff", "[]", null], ["sage", "wait", "_generic", null, "Y", "{\"base\":\"Wait\"}", "{\"base\":\"Waits for a predefined period before taking any further actions.\"}", "hourglass", "335b70ff", "[\"workflowAdvanced\"]", null], ["sage", "mutation", "_generic", null, "Y", "{\"base\":\"Mutation\"}", "{\"base\":\"Allows the workflow logic to execute a mutation.\"}", "connected", "ef6700ff", "[\"workflowAdvanced\"]", null], ["sage", "transfer-attachment", "_generic", null, "Y", "{\"base\":\"Share attachment\"}", "{\"base\":\"This action shares an attachement from one entity to another.\"}", "connected", "335b70ff", null, null], ["sage", "print-document", "_generic", null, "Y", "{\"base\":\"Print a document\"}", "{\"base\":\"Generates a document using a predefined template.\"}", "print", "335b70ff", null, null], ["sage", "send-email", "_generic", null, "Y", "{\"base\":\"Send an email\"}", "{\"base\":\"This action sends an email using a predefined email template.\"}", "mail", "335b70ff", null, null]]}, "SysJobSchedule": {"metadata": {"rootFactoryName": "SysJobSchedule", "name": "SysJobSchedule", "naturalKeyColumns": ["_tenant_id", "id", "execution_user"], "columns": [{"name": "_vendor", "type": "reference", "isNullable": true, "targetFactoryName": "SysVendor"}, {"name": "operation", "type": "reference", "targetFactoryName": "MetaNodeOperation"}, {"name": "description", "type": "string"}, {"name": "is_active", "type": "boolean", "isOwnedByCustomer": true}, {"name": "start_stamp", "type": "datetime", "isNullable": true}, {"name": "end_stamp", "type": "datetime", "isNullable": true}, {"name": "execution_user", "type": "reference", "targetFactoryName": "User"}, {"name": "execution_locale", "type": "string"}, {"name": "parameter_values", "type": "json"}, {"name": "cron_schedule", "type": "string"}, {"name": "time_zone", "type": "string"}, {"name": "id", "type": "string"}]}, "rows": [["sage", "SysNotificationState|purgeHistory|start", "Purge history - 3 months", "Y", null, null, "<EMAIL>", "en-US", "{\"unit\":\"months\",\"duration\":\"3\"}", "0 1 1 * *", "Europe/Paris", "purgeHistory_1"], ["sage", "SysNotificationState|purgeHistory|start", "Purge sync history - 2 weeks", "Y", null, null, "<EMAIL>", "en-US", "{\n    \"unit\": \"weeks\",\n    \"duration\": \"2\",\n    \"operationNames\": [\"SysSynchronizationTarget.synchronize\"]\n}\n", "30 1 * * *", "Europe/Paris", "purgeSyncHistory_1"], ["sage", "SysClientNotification|purgeSysClientNotification|start", "\"info\" client notification will automatically be deleted after seven days", "Y", null, null, "<EMAIL>", "en-US", "{\"unit\":\"days\",\"level\":\"info\",\"duration\":\"7\"}", "0 0 * * *", "Europe/Paris", "purgeSysClientNotification_1"], ["sage", "SysClientNotification|purgeSysClientNotification|start", "\"success\" client notification will automatically be deleted after seven days", "Y", null, null, "<EMAIL>", "en-US", "{\"unit\":\"days\",\"level\":\"success\",\"duration\":\"7\"}", "0 0 * * *", "Europe/Paris", "purgeSysClientNotification_2"], ["sage", "SysClientNotification|purgeSysClientNotification|start", "\"warning\" client notification will automatically be deleted after seven days", "Y", null, null, "<EMAIL>", "en-US", "{\"unit\":\"days\",\"level\":\"warning\",\"duration\":\"7\"}", "0 0 * * *", "Europe/Paris", "purgeSysClientNotification_3"], ["sage", "SysClientNotification|purgeSysClientNotification|start", "\"error\" client notification will automatically be deleted after seven days", "Y", null, null, "<EMAIL>", "en-US", "{\"unit\":\"days\",\"level\":\"error\",\"duration\":\"7\"}", "0 0 * * *", "Europe/Paris", "purgeSysClientNotification_4"], ["sage", "SysChore|purgeContentAddressableTables|start", "Content-addressable tables are automatically purged once a month at 3.10AM on the 28th", "Y", null, null, "<EMAIL>", "en-US", "{}", "10 3 28 * *", "Europe/Paris", "purgeContentAddressableTables"], ["sage", "UploadedFile|purge|start", "Purge attachments and association attachments", "Y", null, null, "<EMAIL>", "en-US", "{\"unit\":\"days\",\"level\":\"info\",\"duration\":\"1\"}", "0 0 * * *", "Europe/Paris", "purgeAttachment_"]]}}}