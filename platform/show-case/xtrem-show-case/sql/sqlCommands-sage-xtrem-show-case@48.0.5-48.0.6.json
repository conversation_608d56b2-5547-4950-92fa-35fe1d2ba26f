{"fromVersion": "48.0.5", "toVersion": "48.0.6", "gitHead": "e56f3eb12f08b39a581d144989aacb36d54662d3", "commands": [{"isSysPool": true, "sql": ["", "", "CREATE EXTENSION IF NOT EXISTS pgcrypto;", ""]}, {"isSysPool": true, "sql": ["", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.get_config(setting_name varchar)", "RETURNS varchar AS", "$$", "declare", "    setting_value varchar;", "BEGIN", "    SELECT current_setting(setting_name) into setting_value;", "    RETURN setting_value;", "EXCEPTION", "    WHEN OTHERS THEN", "    RETURN '';", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.drop_triggers(schema_name varchar, name_filter varchar DEFAULT '') RETURNS integer AS", "$$", "DECLARE", "    triggerRecord RECORD;", "    record_count integer;", "begin", "\trecord_count = 0;", "    FOR triggerRecord IN", "    \tSELECT trigger_name, event_object_table", "    \tFROM information_schema.triggers", "    \tWHERE trigger_schema = schema_name AND (name_filter = '' OR event_object_table = name_filter)", "\tLOOP", "\t\trecord_count = record_count + 1;", "        EXECUTE 'DROP TRIGGER ' || triggerRecord.trigger_name || ' ON ' || schema_name || '.\"' || triggerRecord.event_object_table || '\";';", "    END LOOP;", "", "    RETURN record_count;", "END;", "$$ LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.drop_notify_functions(schema_name varchar, name_filter varchar DEFAULT '') RETURNS integer AS", "$$", "DECLARE", "    triggerRecord RECORD;", "    record_count integer;", "BEGIN", "    record_count = 0;", "    FOR triggerRecord IN", "    SELECT routine_name", "    FROM information_schema.routines", "    WHERE specific_schema = schema_name and routine_name IN (name_filter || '_notify_deleted', name_filter || '_notify_created', name_filter || '_notify_updated')", "    LOOP", "        record_count = record_count + 1;", "        EXECUTE 'DROP FUNCTION ' || schema_name || '.' || triggerRecord.routine_name || ';';", "    END LOOP;", "", "    RETURN record_count;", "END;", "$$ LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.insert_table()", "RETURNS TRIGGER", "AS", "$$", "    DECLARE", "        USER_ID VARCHAR;", "    BEGIN", "        SELECT %%SCHEMA_NAME%%.get_config('xtrem.transaction_user_id') INTO USER_ID;", "        IF (USER_ID != '') THEN", "            NEW._create_user :=  CAST(USER_ID AS INT8);", "            NEW._update_user :=  CAST(USER_ID AS INT8);", "        END IF;", "        IF (NEW._create_stamp IS NULL) THEN", "            NEW._create_stamp := NOW();", "        END IF;", "        IF (NEW._update_stamp IS NULL) THEN", "            NEW._update_stamp := NOW();", "        END IF;", "        IF (NEW._update_tick IS NULL) THEN", "            NEW._update_tick := 1;", "        END IF;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.insert_shared_table()", "RETURNS TRIGGER", "AS", "$$", "    BEGIN", "        IF (NEW._create_stamp IS NULL) THEN", "            NEW._create_stamp := NOW();", "        END IF;", "        IF (NEW._update_stamp IS NULL) THEN", "            NEW._update_stamp := NOW();", "        END IF;", "        IF (NEW._update_tick IS NULL) THEN", "            NEW._update_tick := 1;", "        END IF;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.update_table()", "RETURNS TRIGGER", "AS", "$$", "    DECLARE", "        USER_ID VARCHAR;", "    BEGIN", "        SELECT %%SCHEMA_NAME%%.get_config('xtrem.transaction_user_id') INTO USER_ID;", "        IF (USER_ID != '') THEN", "            NEW._update_user :=  CAST(USER_ID AS INT8);", "        END IF;", "        NEW._update_stamp := NOW();", "        NEW._update_tick := OLD._update_tick + 1;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.update_shared_table()", "RETURNS TRIGGER", "AS", "$$", "    BEGIN", "        NEW._update_stamp := NOW();", "        NEW._update_tick := OLD._update_tick + 1;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.set_sync_tick()", "RETURNS TRIGGER", "AS", "$$", "    BEGIN", "        NEW._sync_tick :=  pg_current_xact_id();", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.signedInt32(a bigint)", "RETURNS bigint AS", "$$", "DECLARE", "BEGIN", "\t-- Convert to 32 bit signed (if leftmost bit is 1, it's a negative number)", "  \tIF (a > 2^31) THEN", "    \tRETURN a - (2^32)::bigint;", "  \tEND IF;", "  \tRETURN a;", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.imul(a bigint, b bigint)", "RETURNS bigint AS", "$$", "DECLARE", "    aHi bigint;", "    aLo bigint;", "    bHi bigint;", "    bLo bigint;", "    res bigint;", "BEGIN", "    aHi = %%SCHEMA_NAME%%.zeroFillShift(a, 16) & 65535;", "    aLo = a & 65535;", "    bHi = %%SCHEMA_NAME%%.zeroFillShift(b, 16) & 65535;", "    bLo = b & 65535;", "    res = ((aLo * bLo) + %%SCHEMA_NAME%%.zeroFillShift(((aHi * bLo + aLo * bHi) << 16) % (2^32)::bigint, 0)) | 0;", "    RETURN %%SCHEMA_NAME%%.signedInt32(res);", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.zeroFillShift(a bigint, b int)", "RETURNS bigint AS", "$$", "DECLARE", "  \tres bigint;", "BEGIN", "\tIF (a < 0) THEN", "\t\tres = a + 2^32;", "\tELSE", "\t\tres = a;", "\tEND IF;", "\tres = res >> b;", "\tRETURN res;", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.nanoid(\"size\" int4 DEFAULT 21)", "    RETURNS text", "    LANGUAGE plpgsql", "    STABLE", "    AS", "    $$", "    DECLARE", "        id text := '';", "        i int := 0;", "        urlAl<PERSON><PERSON> char(64) := 'ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW';", "        bytes bytea;", "        byte int;", "        pos int;", "    BEGIN", "        SELECT gen_random_bytes(size) INTO bytes;", "    WHILE i < size LOOP", "        byte := get_byte(bytes, i);", "        pos := (byte & 63) + 1; -- + 1 because substr starts at 1", "        id := id || substr(urlAlphabet, pos, 1);", "        i = i + 1;", "    END LOOP;", "    RETURN id;", "    END", "    $$", "    ;", " ", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.audit_table()", "RETURNS TRIGGER", "AS", "$$", "    DECLARE", "        is_audit_enabled BOOLEAN;", "        p_root_table_name VARCHAR;", "        login_email VARCHAR;", "        user_id INT8;", "        log_record RECORD;", "    BEGIN", "        SELECT %%SCHEMA_NAME%%.get_config('xtrem.is_audit_enabled') INTO is_audit_enabled;", "        IF NOT is_audit_enabled THEN", "            RETURN NEW;", "        END IF;", "        p_root_table_name := TG_ARGV[0];", "        SELECT %%SCHEMA_NAME%%.get_config('xtrem.login_email') INTO login_email;", "        SELECT %%SCHEMA_NAME%%.get_config('xtrem.transaction_user_id')::INT8 INTO user_id;", "        SELECT * FROM %%SCHEMA_NAME%%.sys_audit_log", "        WHERE root_table_name = p_root_table_name", "            AND record_id = COALESCE(NEW._id, OLD._id)", "            AND transaction_id::TEXT = pg_current_xact_id()::TEXT", "        INTO log_record;", "        IF log_record IS NULL THEN", "            RAISE NOTICE 'Inserting new audit log record %:%', p_root_table_name, NEW._id;", "            IF p_root_table_name = TG_TABLE_NAME THEN", "                INSERT INTO %%SCHEMA_NAME%%.sys_audit_log (root_table_name, _tenant_id, record_id, operation, login_email, timestamp, transaction_id, record_data, old_update_tick, new_update_tick, _create_user, _update_user)", "                VALUES (p_root_table_name, COALESCE(NEW._tenant_id, OLD._tenant_id), COALESCE(NEW._id, OLD._id), TG_OP::%%SCHEMA_NAME%%.audit_operation_enum, login_email, NOW(), pg_current_xact_id(), to_json(NEW), OLD._update_tick, NEW._update_tick, user_id, user_id);", "            ELSE", "                INSERT INTO %%SCHEMA_NAME%%.sys_audit_log (root_table_name, _tenant_id, record_id, operation, login_email, timestamp, transaction_id, record_data, old_update_tick, new_update_tick)", "                VALUES (p_root_table_name, COALESCE(NEW._tenant_id, OLD._tenant_id), COALESCE(NEW._id, OLD._id), TG_OP::%%SCHEMA_NAME%%.audit_operation_enum, login_email, NOW(), pg_current_xact_id(), to_json(NEW), NULL, NULL);", "            END IF;", "            RAISE NOTICE 'Inserted  new audit log record root_table=%, table=%, _id=%', p_root_table_name, TG_TABLE_NAME, NEW._id;", "        ELSE", "            RAISE NOTICE 'Updating audit log record %:%', p_root_table_name, NEW._id;", "            UPDATE %%SCHEMA_NAME%%.sys_audit_log", "            SET record_data = log_record.record_data || to_jsonb(NEW)", "            WHERE root_table_name = p_root_table_name", "                AND record_id = NEW._id", "                AND transaction_id = pg_current_xact_id()::TEXT;", "            RAISE NOTICE 'Updated  audit log record %:%', p_root_table_name, NEW._id;", "        END IF;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", ""]}, {"isSysPool": true, "sql": "DROP INDEX IF EXISTS %%SCHEMA_NAME%%.sys_audit_log_ind0;"}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS workflow_definition_audit ON %%SCHEMA_NAME%%.workflow_definition;", "DROP TRIGGER IF EXISTS workflow_definition_audit ON %%SCHEMA_NAME%%.workflow_definition;", "DROP TRIGGER IF EXISTS workflow_definition_audit ON %%SCHEMA_NAME%%.workflow_definition;"], "args": []}, {"isSysPool": true, "sql": "CREATE UNIQUE INDEX sys_audit_log_ind0 ON %%SCHEMA_NAME%%.sys_audit_log(_tenant_id ASC,root_table_name ASC,record_id ASC,COALESCE(new_update_tick, (- ((2)::bigint ^ (62)::bigint))::bigint) ASC);"}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Is a demo tenant", "released", false, "@sage/xtrem-system", false, "isDemoTenant"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["enable dev-only features", "experimental", false, "@sage/xtrem-system", false, "devTools"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["allow to display changelog in the app", "experimental", false, "@sage/xtrem-system", false, "changelog"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Notification center", "experimental", false, "@sage/xtrem-communication", false, "notificationCenter"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Authorization access control", "released", false, "@sage/xtrem-authorization", false, "authorizationServiceOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Auditing option", "workInProgress", false, "@sage/xtrem-auditing", false, "auditing"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Auditing option", "workInProgress", true, "@sage/xtrem-auditing", false, "auditingOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Workflow option", "workInProgress", false, "@sage/xtrem-workflow", false, "workflow"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Workflow option", "workInProgress", true, "@sage/xtrem-workflow", false, "workflowOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Synchronization", "experimental", false, "@sage/xtrem-interop", false, "synchronizationServiceOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["showCase discount option", "experimental", false, "@sage/xtrem-show-case", false, "showCaseDiscountOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["experimental option", "experimental", false, "@sage/xtrem-show-case", false, "showCaseExperimentalOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["showCaseOption's hight level description", "released", false, "@sage/xtrem-show-case", false, "showCaseOptionHighLevel"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["showCaseOption1's description", "released", true, "@sage/xtrem-show-case", false, "showCaseOption1"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["showCaseOption2's description", "released", true, "@sage/xtrem-show-case", false, "showCaseOption2"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["A workInProgress service option that can be loaded on a development environment", "workInProgress", false, "@sage/xtrem-show-case", true, "showCaseOption3"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Work in progress options cannot be activated", "workInProgress", false, "@sage/xtrem-show-case", false, "showCaseWorkInProgressOption"]}, {"isSysPool": false, "sql": "NOTIFY invalidate_category_cache, '{\"data\":\"{\\\"tenantId\\\":null,\\\"category\\\":\\\"$SHARED_NODE.SysServiceOption\\\"}\",\"containerId\":\"x3-devops00E0I8-64560\",\"excludeSelf\":true}';", "args": []}, {"action": "reload_setup_data", "args": {"factory": "SysChangelog"}}], "data": {"SysChangelog": {"metadata": {"isSharedByAllTenants": true, "rootFactoryName": "SysChangelog", "name": "SysChangelog", "naturalKeyColumns": ["hash"], "columns": [{"name": "hash", "type": "string"}, {"name": "message", "type": "string"}, {"name": "change_date", "type": "datetime"}]}, "rows": [["e56f3eb12f0", "chore: bump patch version", "2024-09-23T20:05:04+00:00"], ["5a87bce2b56", "fix(xtrem-cli-atp): XT-70642 ignore context => false (#21194)", "2024-09-23T18:30:05+02:00"], ["aba39063fb9", "feat(x3-stock): X3-319765 - Pick tickets pages - Hide site (#21089)", "2024-09-23T18:19:30+02:00"], ["905a8fca395", "feat(xtrem-supply-chain): XT-75659 stock transfer shipment from stock transfer order (#21174)", "2024-09-23T16:21:13+02:00"], ["38d32aba18f", "feat(finance): XT-72334 new test (#21215)", "2024-09-23T13:38:53+02:00"], ["3469dc50ff2", "feat(xtrem-finance): XT-78343 deal AU as GB (#21158)", "2024-09-23T11:59:20+01:00"], ["4c191c6806d", "feat(finance): XT-78017 AU legislation (#20832)", "2024-09-23T12:58:39+02:00"], ["a69423a1de2", "fix(manufacturing): XT-78691 Work in progress cost with cost value NaN (#21085)", "2024-09-23T12:16:02+02:00"], ["2660f711bb2", "feat(xtrem-x3-inventory): X3-317352 (#21216)", "2024-09-23T11:41:18+02:00"], ["e2a57231215", "feat(x3-purchasing): X3-319739 - Receipt pages - Hide site (#21073)", "2024-09-23T09:02:13+02:00"], ["7803cceee34", "chore: updated docker pnpm-lock.yaml files", "2024-09-22T21:28:17+00:00"], ["e0f75aa9102", "chore: commit upgrades", "2024-09-22T21:08:37+00:00"], ["f7a8e96fcc5", "chore: bump patch version", "2024-09-22T20:07:44+00:00"], ["7329bf9ea28", "chore: updated docker pnpm-lock.yaml files", "2024-09-21T21:26:36+00:00"], ["1b569f3e04f", "chore: commit upgrades", "2024-09-21T21:07:24+00:00"], ["bebd3518829", "chore: bump patch version", "2024-09-21T20:08:01+00:00"], ["fb028084def", "fix: X3-318409 allow transient input fields in node details query (#21197)", "2024-09-21T00:04:24+02:00"], ["ae94a1d8657", "chore: updated docker pnpm-lock.yaml files", "2024-09-20T21:41:39+00:00"], ["e6f14e54124", "chore: commit upgrades", "2024-09-20T21:23:11+00:00"], ["7046b64daa8", "chore: bump patch version", "2024-09-20T20:08:00+00:00"], ["c1d2c490710", "feat(supply<PERSON><PERSON><PERSON>): stock-transfer-shipment-controls (#20827)", "2024-09-20T22:15:21+03:00"], ["52c288ecad4", "feat: pendo ids poc (#21201)", "2024-09-20T20:24:26+02:00"], ["3c2d83c36f6", "fix(xtrem-cli-atp): XT-52263 - rollback (#21211)", "2024-09-20T20:47:37+03:00"], ["1bdfd371dbc", "feat(shopfloor): XAPPSF-671 re-structure time tracking sync nodes. (#21160)", "2024-09-20T18:42:07+02:00"], ["0b214f67b91", "feat(all): fixes unit tests manuf and finance (#21165)", "2024-09-20T17:32:45+02:00"], ["e5b12374d00", "feat(purchasing): Refactoring for Purchasing package XT-78911 (#21079)", "2024-09-20T17:31:19+02:00"], ["a5cf54f49ac", "fix: XT-75843 special case for _factory (#21187)", "2024-09-20T08:23:47-07:00"], ["07b74843bdc", "fix(xtrem-cli-atp): XT-73885 - fix focus issue on card elements (#21178)", "2024-09-20T17:07:37+02:00"], ["4e29b1b5146", "fix: XT-77443 implement access check on import (#21189)", "2024-09-20T07:25:40-07:00"], ["a5445bea228", "perf: [Revert] increase the yield modulo of the async array loops (#21204)", "2024-09-20T15:53:32+02:00"], ["4c90bf54501", "feat: xtrem-interop change debug loggers into verbose, missed loggers (#21200)", "2024-09-20T15:07:27+02:00"], ["a5cc403dbad", "feat(xtrem-finance): XT-77622 countra account on automatically generated balance lines (#21177)", "2024-09-20T14:04:43+01:00"], ["f5a904b7009", "feat(xtrem-purchasing): XT-78988 Refactor purchase-order-suggestion file (#21107)", "2024-09-20T15:58:55+03:00"], ["75cb18bd973", "feat(customData): fix enum type XT-76725 (#21149)", "2024-09-20T14:52:01+02:00"], ["7e89844fd99", "fix(supply-chain): XT-76766 stock-transfer fixes after qa (#21190)", "2024-09-20T13:04:05+02:00"], ["0a760c908e9", "feat(xtrem-purchasing): XT-78987 Refactor purchase-order-table-panel file (#21105)", "2024-09-20T13:55:56+03:00"], ["0d06335009f", "feat(xtrem-cli-atp): refactor table object XT-78496 (#21026)", "2024-09-20T12:55:23+02:00"], ["8fc774fe773", "chore(deps): progress on cleanup root package (#21050)", "2024-09-20T12:27:34+02:00"], ["a137921a3a1", "fix(distribution): XT-78885 approval management in site (#21193)", "2024-09-20T12:00:58+02:00"], ["ef12230f87f", "feat(supply-chain): XT-77685-stock-transfer-order-smoke-tests (#20932)", "2024-09-20T11:58:08+02:00"], ["ee695c3a303", "feat(xtrem-purchasing): XT-78280 Refactor purchase-receipt file (#20941)", "2024-09-20T12:46:11+03:00"], ["10a4c409bcc", "feat(purchasing): XT-78364 Refactor purchase-receipt-table-panel file (#20988)", "2024-09-20T12:14:52+03:00"], ["4fd578be8a6", "feat(automationcrew): XT-79366-maintenance (#21191)", "2024-09-20T11:39:34+03:00"], ["fb33882af59", "feat: XT-78326 unbilled receivable problem 2 after refactoring (#21157)", "2024-09-20T09:51:23+02:00"], ["ff0750aeb97", "feat(masterData): _constructor on baseDocument naturalKey (#20970)", "2024-09-20T08:50:21+02:00"], ["45b23c3c798", "feat(manufacturing): XT-79207-refactoring-ft (#21180)", "2024-09-20T00:37:29+03:00"], ["943febcdaa8", "feat(automationcrew): XT-79209-maintenance (#21182)", "2024-09-20T00:36:56+03:00"], ["bb5b7c8bbe5", "feat(distribution): XT-79207 refactor tests (#21172)", "2024-09-19T21:36:37+00:00"], ["a14ba2246bf", "chore: prevent renovate auto-merge during release period (#21181)", "2024-09-19T23:31:10+02:00"], ["92b83cee26f", "fix: refresh active apps XT-79353 (#21184)", "2024-09-19T23:30:23+02:00"], ["39f65d983ef", "chore: updated docker pnpm-lock.yaml files", "2024-09-19T21:19:08+00:00"], ["871853ef11b", "chore: commit upgrades", "2024-09-19T21:03:24+00:00"], ["695afd4a782", "chore: bump patch version", "2024-09-19T20:04:47+00:00"], ["4901cd625e5", "feat: XAPPSF-357 sync scheduling (#20499)", "2024-09-19T21:24:13+02:00"], ["798ec99e733", "feat(supply-chain): XT-73134-stock-transfer-shipment-page-actions (#21134)", "2024-09-19T21:22:12+02:00"], ["ac19eed98ae", "fix(xtrem-cli-atp): XT-79139_incorrect_version_of_chrome (#21150)", "2024-09-19T21:01:28+02:00"], ["c24c04189eb", "fix(xtrem-cli-atp): XT-52263 fixed by clicked on card (#21015)", "2024-09-19T21:36:12+03:00"], ["35d5bca59cc", "feat(xtrem-x3-inventory): X3-317350 (#21170)", "2024-09-19T19:59:19+02:00"], ["9193b5ae447", "feat(x3-stock): X3-319740 - Stock controls pages - Hide site (#21083)", "2024-09-19T19:49:13+02:00"], ["ef68ac4656d", "chore: updated docker pnpm-lock.yaml files", "2024-09-19T16:07:43+00:00"], ["de4bd1bb3d4", "chore: commit upgrades", "2024-09-19T15:49:46+00:00"], ["52d831d0118", "chore: bump patch version", "2024-09-19T14:35:41+00:00"], ["9b9d46219b4", "chore: updated docker pnpm-lock.yaml files", "2024-09-19T14:10:24+00:00"], ["f91538394d0", "shopfloor-main: manual empty file to jump from 47.0.30 to 48.0.0", "2024-09-19T13:09:19+00:00"], ["4d21a8f2b2d", "xtrem-show-case: manual empty file to jump from 47.0.30 to 48.0.0", "2024-09-19T13:09:14+00:00"], ["bfb5509b118", "xtrem-glossary: manual empty file to jump from 47.0.30 to 48.0.0", "2024-09-19T13:09:10+00:00"], ["0d1cd446da3", "xtrem-services-main: manual empty file to jump from 47.0.30 to 48.0.0", "2024-09-19T13:09:05+00:00"], ["f24f600637d", "chore: bump major version", "2024-09-19T13:08:57+00:00"], ["6fc3cffa9e1", "feat(distribution): XT-79077 refactor tests (#21151)", "2024-09-19T09:08:45+00:00"], ["04ac7243894", "chore: updated docker pnpm-lock.yaml files", "2024-09-19T08:46:47+00:00"], ["8f3c2889b18", "chore: commit upgrades", "2024-09-19T08:31:16+00:00"], ["dddb749620a", "chore: bump patch version", "2024-09-19T07:34:07+00:00"], ["68a831a297d", "feat: XT-79043 fix outbound property transform (#21162)", "2024-09-19T09:29:35+02:00"], ["3d3383bc104", "fix: XT-78961: fixed selection of report parameters (#21166)", "2024-09-19T09:28:53+02:00"], ["84a3b6230d4", "fix(stock): XT-77011 Stock reorder calculation refactor (#20995)", "2024-09-19T09:28:23+02:00"], ["21e5c974f12", "chore: updated docker pnpm-lock.yaml files", "2024-09-18T21:40:02+00:00"], ["68f23d45b3e", "chore: commit upgrades", "2024-09-18T21:20:57+00:00"], ["bfd72e0cd07", "chore: bump patch version", "2024-09-18T20:04:48+00:00"], ["d934f080a78", "chore: updated docker pnpm-lock.yaml files (#21164)", "2024-09-18T19:19:10+02:00"], ["cd0c7d9fa72", "fix: XT-78583 temporary fix of \"delete attribute timeout\" issue for v47 (#20996) (#21156)", "2024-09-18T18:38:48+02:00"], ["9f3deeaca8f", "fix: OR filters on a single column XT-79085 (#21148)", "2024-09-18T18:07:02+02:00"], ["eae89647ca6", "fix: overriding problematic sinon version XT-79083 (#21155)", "2024-09-18T17:49:32+02:00"], ["7be2a68ea9a", "feat: xtrem-interop change debug loggers into verbose (#21153)", "2024-09-18T17:48:37+02:00"], ["61fbc6c9b31", "fix: custom table filter component spacing XT-79013 (#21154)", "2024-09-18T17:07:51+02:00"], ["56df2080fa1", "fix: set translations on nested grid child levels XT-79010 (#21152)", "2024-09-18T16:34:18+02:00"], ["e7bf37998f5", "perf: increase the yield modulo of the async array loops (#21141)", "2024-09-18T16:21:08+02:00"], ["dc17f8d209e", "feat(automationcrew): XT-79079-automation-maintanance (#21140)", "2024-09-18T16:42:48+03:00"], ["66670a43258", "feat(distribution): XT-78963 refactor tests (#21133)", "2024-09-18T13:42:32+00:00"], ["8c418f9e33a", "feat(automationcrew): XT-61877-automation-maintanance (#21122)", "2024-09-18T16:42:11+03:00"], ["0117c95b7d4", "feat(finance): XT-78963-refactoring-ft (#21118)", "2024-09-18T16:41:54+03:00"], ["931126e5f3d", "feat: filter collections of MetaNodeFactory prior to applying paging options in xtrem-app-metadata (#21129)", "2024-09-18T14:09:41+02:00"], ["464f10436c6", "fix: partial select state on select all checkbox XT-79088 (#21135)", "2024-09-18T13:52:03+02:00"], ["6e24be2956a", "feat(interop): XT-79042 add duplication (#21124)", "2024-09-18T11:46:56+01:00"], ["48da69d63f8", "chore(deps): update dependency @types/nodemailer to v6.4.16 (#21131)", "2024-09-18T07:32:45+02:00"], ["2e429b2dccd", "chore(deps): update dependency sass to v1.79.1 (#21132)", "2024-09-18T07:32:40+02:00"], ["37ddf544d7e", "feat: XT-79000 workflow option off by default in unit tests (#21116)", "2024-09-18T00:09:30+02:00"], ["df92412419c", "chore(deps): update non-major types dependencies (#21127)", "2024-09-18T00:03:12+02:00"], ["d28cb5438c1", "fix(deps): update dependency thread-loader to v4.0.3 (#21128)", "2024-09-18T00:03:07+02:00"], ["ce3c4e2f22e", "chore: updated docker pnpm-lock.yaml files", "2024-09-17T21:39:14+00:00"], ["6e2f401a105", "chore: commit upgrades", "2024-09-17T21:21:46+00:00"], ["bca4d4fdf47", "chore: bump patch version", "2024-09-17T20:08:21+00:00"], ["f8590070251", "fix(cs-crew): Added a new error message on stop button when status is not running (#21076)", "2024-09-17T23:00:22+05:30"], ["ebeb3023d9d", "feat(xtrem-finance): XT-78835 DATEV export - Add mandatory header line to export file structure (#21090)", "2024-09-17T19:21:06+02:00"], ["ed092d8e72e", "fix: XT-75843: expose _factory without authorization (#21121)", "2024-09-17T09:43:15-07:00"], ["dc5bdc4a850", "feat(xtrem-intacct): XT-73401 Message updates when activating/deactivating intacct (#21119)", "2024-09-17T17:30:19+01:00"], ["706718503cf", "feat(master-data): XT-71264 revert BE bind to supplier on page (#20877)", "2024-09-17T18:06:07+02:00"], ["5705be70cd7", "fix: XT-77841 - remove default navigation panel from standart cost ca… (#20965)", "2024-09-17T21:30:20+05:30"], ["1f159f5c578", "feat(shopfloor): XAPPSF-670-Time-tracking-header-incorporate-check-isSynced-property (#21123)", "2024-09-17T16:55:07+01:00"], ["e346f4bbdcc", "feat(basalt): fix convertJoin for expression XT-74164 (#21120)", "2024-09-17T17:52:14+02:00"], ["ce8c9904411", "fix(xtrem-purchasing): XT-72299 (#20402)", "2024-09-17T17:47:53+02:00"], ["e466d9df5db", "fix: XT-78652 Modify filter to include extension properties (#21110)", "2024-09-17T16:10:41+02:00"], ["95f2a715fd3", "fix(stock-data): XT-79017 Auth stock allocation (#21113)", "2024-09-17T15:34:00+02:00"], ["b13e0775499", "fix: XT-77728 - MRP calculation justification dialog error (#20849)", "2024-09-17T19:01:47+05:30"], ["96ee7551a94", "fix(purchasing): XT-77386 Auth purchase return delete (#21115)", "2024-09-17T15:27:43+02:00"], ["0bb67321f87", "fix(stock-data): XT-78567 allocate serial numbers (#21001)", "2024-09-17T15:20:29+02:00"], ["706cc44dfd1", "feat(xtrem-cli-dev): XT-70937 add timeoutWaitForLoading (#20753)", "2024-09-17T13:45:01+02:00"], ["ae8c754647a", "feat(xtrem-intacct): XT-73401 Business rule on Intacct service option update (#21024)", "2024-09-17T11:35:24+01:00"], ["efefa4c73ef", "feat(xtrem-x3-inventory): X3-320079 (#21106)", "2024-09-17T12:30:47+02:00"], ["dd145ac65d7", "feat(xtrem-purchasing): XT-71593-purchase-order-line-filter-site (#21082)", "2024-09-17T11:32:48+02:00"], ["1f3aeabdfb6", "chore(deps): update non-major types dependencies (#21097)", "2024-09-17T11:02:49+02:00"], ["bd8ef502e2e", "feat: XT-78326 unbilled accounts receivable (#21092)", "2024-09-17T10:56:18+02:00"], ["ef52ac2e2af", "feat(distribution): XT-78736 small refactoring (#21100)", "2024-09-17T08:42:08+00:00"], ["a8316f7a556", "feat(shopfloor): XAPPSF-667 stop time treatment for setup (#21086)", "2024-09-17T10:35:07+02:00"], ["7dc83d20e11", "fix(interop): XT-78853 fix query results test (#21048)", "2024-09-17T08:53:34+01:00"], ["1cf644136ca", "feat(xtrem-stock): XT-74949 default values stock details (#21101)", "2024-09-17T09:44:24+02:00"], ["10f8d8f556f", "feat(xtrem-stock): XT-77823 (purchasing): XT-76004 (#20997)", "2024-09-17T09:35:37+02:00"], ["f7ca78dfc9b", "feat(xtrem-sales): XT-75982-sales-credit-memo-create-button (#21045)", "2024-09-17T09:20:04+02:00"], ["581ec5d2820", "chore(deps): update storybook monorepo to v8.3.1 (#21102)", "2024-09-17T07:32:49+02:00"], ["026d564df6c", "fix(deps): update dependency puppeteer to v23.3.1 (#21104)", "2024-09-17T07:32:45+02:00"], ["9e735ec20db", "fix(queue): exit on missing queue at runtime XT-78910 (#21081)", "2024-09-17T00:16:48+02:00"], ["3e91795369d", "chore(deps): update dependency eslint to v8.57.1 (#21098)", "2024-09-17T00:03:15+02:00"], ["8fa9302133c", "chore: updated docker pnpm-lock.yaml files", "2024-09-16T21:37:15+00:00"], ["***********", "chore: commit upgrades", "2024-09-16T21:18:10+00:00"], ["8d3c4a469ae", "chore: bump patch version", "2024-09-16T20:04:29+00:00"], ["83b5939698e", "fix: XT-78939 make workflow engine work in cluster (#21088)", "2024-09-16T19:57:29+02:00"], ["e48b4c33c78", "feat(xtrem-cli-atp): XT-77366 - add max instance atp parameter (#21025)", "2024-09-16T19:50:35+02:00"], ["dd17405d8c8", "feat: XT-78326 unbilled accounts payable user notification pr2 (#21084)", "2024-09-16T18:08:30+02:00"], ["eb9b95bee86", "fix: hide print button XT-78503 (#21040)", "2024-09-16T17:27:27+02:00"], ["8c9955f6c7b", "feat: XT-78886 move assertDeepPartialMatch and add unit test (#21071)", "2024-09-16T16:52:26+02:00"], ["65a5f72f558", "fix(xtrem-purchasing): XT-71707 Price updated from supplier prices (#21067)", "2024-09-16T16:17:12+02:00"], ["8de3c4f962d", "feat(supply-chain): XT-71716-stock-transfer-shipment-page-1 (#20945)", "2024-09-16T15:33:57+02:00"], ["2d0ce1fbf6f", "fix: fix add and apply new for zebra size + minor fixes in ADC sizes XT-78218 (#21018)", "2024-09-16T15:28:15+02:00"], ["58718723d12", "fix(pipelines): XT-78849 - discard accessibility tests (#21069)", "2024-09-16T15:25:07+02:00"], ["59007aa6863", "feat(xtrem-cli-atp): XT-75649 - add showcase for multi-action button (#21075)", "2024-09-16T15:09:52+02:00"], ["05b4770518f", "fix: filter editor parameter dropdown XT-77459 XT-72848 (#21077)", "2024-09-16T15:06:01+02:00"], ["877d5de8bf5", "fix(manufer): X3-319935 fix-material-consumption-scripts (#21072)", "2024-09-16T13:48:06+01:00"], ["afdd8af1ae5", "feat: XT-68810 purchase order line inquiry using mainlist (#20373)", "2024-09-16T12:56:06+02:00"], ["9ed98249fdd", "feat(xtrem-stock): xt-77004 fix lint errors on xtrem stock - 3rd batch (#20809)", "2024-09-16T12:01:51+03:00"], ["df3f99037d1", "feat: XT-78833 review send mail workflow action (#21053)", "2024-09-16T09:35:22+02:00"], ["9c363869d7c", "chore(deps): update dependency sinon to v19 (#21063)", "2024-09-16T09:02:37+02:00"], ["0133aab2bc2", "fix(deps): update dependency chokidar to v4 (#21064)", "2024-09-16T09:02:33+02:00"], ["03ed2ff5e52", "feat(actions): implements action id to dropdown and inline actions XT-77205 (#20963)", "2024-09-16T08:29:41+02:00"], ["0e0dfb3226a", "fix(deps): update dependency newrelic to v12.5.0 (#21062)", "2024-09-16T08:02:54+02:00"], ["2623faabb8b", "fix(deps): update dependency json-to-graphql-query to v2.3.0 (#21061)", "2024-09-16T02:32:51+02:00"], ["d2fd0ff2561", "fix(deps): update dependency express to v4.21.0 (#21060)", "2024-09-16T02:07:10+02:00"], ["bab6825432b", "fix(deps): update dependency react-redux to v9 (#20908)", "2024-09-16T00:03:00+02:00"], ["4b27264029c", "chore(deps): update dependency eslint-plugin-react to v7.36.1 (#21059)", "2024-09-16T00:02:45+02:00"], ["5bbd9aa74b5", "chore: updated docker pnpm-lock.yaml files", "2024-09-15T21:43:05+00:00"], ["a14ee60f2d5", "chore: commit upgrades", "2024-09-15T21:24:59+00:00"], ["060c6befe5e", "chore: bump patch version", "2024-09-15T20:08:03+00:00"], ["8f41aef6b0c", "fix(deps): update dependency eslint-plugin-unused-imports to v4.1.4 (#21058)", "2024-09-15T21:02:48+02:00"], ["7d7427d8351", "fix(deps): update dependency react-monaco-editor to ^0.56.0 (#19081)", "2024-09-15T17:33:27+02:00"], ["2859ad0e453", "chore(deps): update dependency @faker-js/faker to v9.0.1 (#21055)", "2024-09-15T17:33:03+02:00"], ["1e72f8d7bf7", "chore(deps): update dependency tsx to v4.19.1 (#21056)", "2024-09-15T17:32:58+02:00"], ["3104d515963", "chore(deps): update non-major types dependencies (#21003)", "2024-09-15T14:32:21+02:00"], ["7d731f25989", "chore(deps): update dependency @chromatic-com/storybook to v2.0.2 (#21051)", "2024-09-15T14:03:02+02:00"], ["af732e1cddb", "fix: relax cli smoke tests (#21054)", "2024-09-15T10:10:08+02:00"], ["099ceb84a80", "chore: updated docker pnpm-lock.yaml files", "2024-09-14T21:47:06+00:00"], ["cd4736b3e51", "chore: commit upgrades", "2024-09-14T21:23:59+00:00"], ["e604b623747", "chore: bump patch version", "2024-09-14T20:04:49+00:00"], ["702baa8d510", "feat: XT-78333 review entity-updated event (#21021)", "2024-09-14T17:40:07+02:00"], ["f4b38d028fe", "chore: updated docker pnpm-lock.yaml files", "2024-09-13T21:35:19+00:00"], ["c9aeb490cc6", "chore: commit upgrades", "2024-09-13T21:17:25+00:00"], ["216483a0897", "chore: bump patch version", "2024-09-13T20:04:26+00:00"], ["6acb1cbbdc5", "feat(shopfloor): XAPPSF-668-add-sync-reference-timeTrackingHeader-node (#20975)", "2024-09-13T18:46:29+01:00"], ["9500f4c71fb", "fix(xtrem-purchasing): XT-77727 PO landed cost payload added to PR payload if it exists (#20764)", "2024-09-13T18:14:15+01:00"], ["0d313ca483f", "feat(wh-stock): X3-319946 - fix navigator filter (#21047)", "2024-09-13T17:36:40+02:00"], ["9dc0d4519b9", "fix(apps): alive status from interop url XT-78770 (#21041)", "2024-09-13T16:23:08+02:00"], ["8eed875a563", "fix(xtrem-cli-atp): XT-78252 - add compatibility with phone and tablet for nested grid tables (#20967)", "2024-09-13T16:09:10+02:00"], ["fbabb1c07c3", "feat: XT-78333 add xtrem-auditing dependency to xtrem-workflow (#21032)", "2024-09-13T15:56:22+02:00"], ["76cd0fafc98", "fix: XT-74274 min max conversion fix (#21033)", "2024-09-13T06:54:30-07:00"], ["6793d4ea00b", "feat(wh-stock): X3-319946 - Fix miscellaneous texts (#21038)", "2024-09-13T15:28:52+02:00"], ["30386c86aef", "feat(xtrem-x3-inventory): X3-319963 (#21043)", "2024-09-13T15:22:28+02:00"], ["bb45c0db873", "fix(manufacturing): XT-77691 wrong time unit (#21037)", "2024-09-13T15:04:18+02:00"], ["eef2e6e523d", "feat(interop): XAPPSF-353 update connector pages (#21000)", "2024-09-13T13:57:53+01:00"], ["aaa1fd705a1", "chore(deps): update storybook monorepo to v8.3.0 (#21004)", "2024-09-13T12:32:16+02:00"], ["8654d6e0c34", "feat(inquiry): XT-64095 unbilled infinite scroll and notification (#20823)", "2024-09-13T12:28:18+02:00"], ["cea5bd8966d", "fix: XT-78532 Only add explicit grants from activities (#21008)", "2024-09-13T12:04:27+02:00"], ["d59fbc20e15", "fix(xtrem-cli-atp): XT-77436 package clean (#21031)", "2024-09-13T12:00:53+02:00"], ["e9bb06d77a8", "feat(xtrem-x3-inventory): X3-319938 (#21036)", "2024-09-13T11:55:13+02:00"], ["23bf438bc07", "feat(xtrem-master-data): XT-74649 total stock item page (#20989)", "2024-09-13T10:44:37+02:00"], ["d4cdc2e885f", "feat(xtrem-purchasing): XT-78537 Refactor purchase-receipt-matching file (#20991)", "2024-09-13T11:13:17+03:00"], ["bd9b57d3b27", "feat: XT-74094 Stock journal inquiry link to journal entry line (#20618)", "2024-09-13T09:54:28+02:00"], ["f3d5f3089f2", "fix(xtrem-master-data): XT-69636 It's not possible to remove a Ship-to address for a customer (#21011)", "2024-09-13T09:31:59+02:00"], ["be24ef9d484", "fix(stock): XT-77496 document status (#20729)", "2024-09-13T09:13:10+02:00"], ["3f836f87869", "feat(xtrem-x3-inventory): X3-317349 (#21012)", "2024-09-13T08:50:05+02:00"], ["b70ab79faf1", "chore: update aws-sdk (#21034)", "2024-09-13T07:41:56+02:00"], ["b74b5a5cf13", "chore: updated docker pnpm-lock.yaml files", "2024-09-12T21:31:42+00:00"], ["d3bcbc6ada3", "chore: commit upgrades", "2024-09-12T21:13:46+00:00"], ["20c2fc1a1d7", "chore: bump patch version", "2024-09-12T20:04:24+00:00"], ["394edda8d13", "fix: XT-78511 invoke delete rules on association child (#21005)", "2024-09-12T20:02:49+02:00"], ["6faf216d366", "fix(xtrem-cli-atp): XT-77887 fixed Closing of the Filter dropdown , by clicking outside on the 'PageTitle' , the Closing has a bug in the ui (#20802)", "2024-09-12T20:48:14+03:00"], ["0bf0b28082e", "feat: XT-78325 add _updateTick to the graphql schema (#20928)", "2024-09-12T19:18:03+02:00"], ["596a420dbdb", "feat: allow reference parameters to be associated with _id fields XT-78707 (#21029)", "2024-09-12T19:10:47+02:00"], ["211b860b43c", "feat: X3-315422 - fix problem in node freightContainer (#21028)", "2024-09-12T17:06:31+02:00"], ["f9451ce4cff", "fix: not allowing sorting on non sortable columns XT-78546 (#21010)", "2024-09-12T15:49:15+02:00"], ["ece37af853d", "feat(xtrem-finance): XT-74196 export business relations (#20944)", "2024-09-12T14:28:32+01:00"], ["cf99bfe73c1", "feat: X3-315422 - Adding node freightContainer (#19834)", "2024-09-12T14:47:05+02:00"], ["a9d051b8354", "feat: X3-315346 - move node Container to package x3-master-data (#19835)", "2024-09-12T14:41:32+02:00"], ["bed3286a037", "feat: X3-315318 - Adding node SHIPTRACK (#19836)", "2024-09-12T14:20:36+02:00"], ["92d1af26940", "feat(xtrem-sales): xt-77464 move files from shared to client-functions folder (#20923)", "2024-09-12T14:03:25+03:00"], ["1788cf23580", "feat(xtrem-stock): xt-77003 fix lint errors on xtrem stock - 2nd batch (#20638)", "2024-09-12T11:23:51+03:00"], ["b1700578b89", "feat(distribution): XT-78518 refactor tests (#21006)", "2024-09-12T06:11:43+00:00"], ["a44c2114034", "chore: updated docker pnpm-lock.yaml files", "2024-09-11T21:20:26+00:00"], ["54730fe3f34", "chore: commit upgrades", "2024-09-11T21:04:21+00:00"], ["dba726aa775", "chore: bump patch version", "2024-09-11T20:04:27+00:00"], ["10f24d6e474", "fix: XT-77776 allow direct update in case of vital child (#20985)", "2024-09-11T12:58:19-07:00"], ["4b02376e843", "feat(finance): XT-72334 data creation (#20999)", "2024-09-11T18:35:21+02:00"], ["aeab29cc40a", "feat(apps): propagate source email to x3 XT-78507 (#20971)", "2024-09-11T18:11:22+02:00"], ["4108ecd53d6", "fix(x3-manufacturing): X3-319844-remove-site-mobile (#20993)", "2024-09-11T16:37:33+01:00"], ["0276df70684", "feat(datatypes): default location details issue XT-77381 (#20953)", "2024-09-11T16:38:57+02:00"], ["63f6af184e6", "feat(finance): XT-78187 fix test data and fix posting class definition (#20982)", "2024-09-11T14:14:27+00:00"], ["fd6271de179", "fix(x3-stock): X3-319760 (#20957)", "2024-09-11T15:58:21+02:00"], ["83d6b870ba9", "fix: autosize translation key XT-78229 (#20969)", "2024-09-11T15:55:05+02:00"], ["62ef8d5b909", "feat(master-data): XT-71264 revert BE bind to customer on page XT-78030 (#19371)", "2024-09-11T15:20:58+02:00"], ["09769c8dadf", "feat: XT-77718 Make Pendo API key configurable by product (#20987)", "2024-09-11T15:13:42+02:00"], ["589bd376172", "fix(master-data): Error when creating an Item sequence number via the 'Select Item ID sequence number dialogue (#20699)", "2024-09-11T18:12:03+05:30"], ["8ee763f9fa9", "chore: add pnpm sha", "2024-09-11T12:40:22+02:00"], ["971e1709f31", "fix(cs-crew): Job schedule error when trying to create a record (#20961)", "2024-09-11T16:11:06+05:30"], ["a8074ed1518", "feat(xtrem-purchasing): XT-73214 Unit tests unbilled account payable (#20853)", "2024-09-11T10:06:03+01:00"], ["63ef2c150b3", "feat(xtrem-services): XT-73992 refactor stock transfer order controls to move to base nodes (#20931)", "2024-09-11T11:04:10+02:00"], ["a57e86a34ee", "feat(wh-services): X3-317546 - Implement mutations for sticker site depositor (#19915)", "2024-09-11T10:18:38+02:00"], ["b03d04b4663", "feat(distribution): xt-76744 (#20964)", "2024-09-11T10:45:49+03:00"], ["16a30a06d0d", "fix(x3-manufacturing): X3-318965-material-consumption-multiple-lots-d… (#20896)", "2024-09-11T08:43:01+01:00"], ["405f979df3c", "feat(supply-chain): XT-71716-stock-transfer-order-work-order (#20935)", "2024-09-11T09:08:31+02:00"], ["dd7ce7678e0", "fix(deps): update aws-sdk-js-v3 monorepo to v3.649.0 (#20979)", "2024-09-11T08:02:59+02:00"], ["8ee0956f9c2", "chore(deps): update dependency sinon to v18.0.1 (#20977)", "2024-09-11T07:32:39+02:00"], ["943408e5105", "feat(finance): XT-78398 Fix dates in finance-flow-contra-account (#20976)", "2024-09-11T05:11:01+00:00"], ["a3dec8d81e6", "chore(deps): update dependency sinon to v18.0.1 (#20978)", "2024-09-11T02:06:44+02:00"], ["899e27e69cb", "chore(deps): update dependency @chromatic-com/storybook to v2 (#20974)", "2024-09-11T00:03:10+02:00"], ["1a3d4a0512a", "feat(automationcrew): XT-61877-automation-debt-add-test-data (#20968)", "2024-09-11T00:46:02+03:00"], ["14a5aa83e8b", "fix(deps): update dependency express to v4.20.0 (#20973)", "2024-09-10T23:32:34+02:00"], ["c39481731c4", "chore: updated docker pnpm-lock.yaml files", "2024-09-10T21:20:33+00:00"], ["00dd7e65c01", "chore: commit upgrades", "2024-09-10T21:04:33+00:00"], ["7d24868f3a9", "chore: bump patch version", "2024-09-10T20:04:06+00:00"], ["ec8c7472ef3", "fix(xtrem-cli-atp): XT-77436-workaround-to-fix-pipeline-issue-with-xt… (#20962)", "2024-09-10T15:52:24+02:00"], ["449d68a6161", "fix(xtrem-cli-atp): XT-78039 - add nested grid title mobile management for visibility (#20880)", "2024-09-10T15:06:15+02:00"], ["9ae13b3d026", "fix(shopfloor): XAPPSF-667 taking the stop time right from the click (#20954)", "2024-09-10T15:04:44+02:00"], ["83dc131f35a", "fix(deps): update typescript-es<PERSON> monorepo to v7.18.0 (#20589)", "2024-09-10T14:02:36+02:00"], ["073d39fea40", "feat(automationcrew): XT-78287-maintenance (#20943)", "2024-09-10T14:55:29+03:00"], ["59ff6160479", "fix: XT-64508 Fix doc path (#20958)", "2024-09-10T12:06:33+02:00"], ["7c800f31ad4", "fix(manufacturing): XT-78138 repost production and material tracking (#20947)", "2024-09-10T10:48:55+02:00"], ["d1963278a92", "feat(manufacturing): XT-70651 unique work order number (#20952)", "2024-09-10T09:44:34+02:00"], ["8e869a4bb62", "fix(sales): XT-77688-changes made in allLinesAreAllocated (#20933)", "2024-09-10T11:26:47+05:30"], ["9e0d3f2ba7d", "fix(deps): update dependency body-parser to v1.20.3 (#20950)", "2024-09-10T07:32:10+02:00"], ["638092c605d", "chore(deps): update pnpm to v9.10.0 (#20951)", "2024-09-10T07:32:05+02:00"], ["77cda174330", "chore(deps): update dependency node-mocks-http to v1.16.0 (#20949)", "2024-09-10T00:32:17+02:00"], ["2c4ed37f440", "feat(shopfloor): XT-76340 update shopfloor x3 (#20891)", "2024-09-09T23:05:21+01:00"], ["97756098fc0", "fix(deps): update dependency ts-graphviz to v2.1.3 (#20948)", "2024-09-10T00:02:47+02:00"], ["68ab5e90b31", "chore: updated docker pnpm-lock.yaml files", "2024-09-09T21:46:32+00:00"], ["158746c29c9", "chore: commit upgrades", "2024-09-09T21:27:58+00:00"], ["4c1b1419abd", "chore: bump patch version", "2024-09-09T20:04:20+00:00"], ["4386f62c669", "fix: incomplete sanitization XT-78266 (#20927)", "2024-09-09T18:14:09+02:00"], ["a7351ac22af", "chore: progress on root package cleanup (#20940)", "2024-09-09T18:13:24+02:00"], ["0eb79b72596", "feat(reference): xt-78287 (#20942)", "2024-09-09T18:56:30+03:00"], ["4d9ab815f97", "fix: XT-77443 implement access check on export (#20914)", "2024-09-09T08:41:14-07:00"], ["62c290093f2", "feat(xtrem-finance): XT-74180 DATEV accounts export basic changes (#20893)", "2024-09-09T16:31:13+01:00"], ["572e3281939", "fix(xtrem-manufacturing): XT-70651 work orders with same number (#20816)", "2024-09-09T15:26:30+01:00"], ["1a24dc9547a", "feat(xtrem-finance): XT-74207 datev journal entry output node (#20936)", "2024-09-09T15:24:54+01:00"], ["4abb7c4fc2a", "feat(w3-stock): X3-319677 - Mutation now return Return only document id (#20889)", "2024-09-09T15:40:56+02:00"], ["299fc6c39e3", "feat(wh-stock): X3-261140 - WH Mobile Automation - View Stock by location (#20281)", "2024-09-09T15:11:01+02:00"], ["f17e19ec2a8", "feat: update subheader in ADC X3-319640 (#20929)", "2024-09-09T15:08:27+02:00"], ["66f161b1e3b", "fix: restore pnpm-lock (#20934)", "2024-09-09T14:40:46+02:00"], ["65ffdd7b103", "feat(xtrem-purchasing): XT-78172 Refactor purchase-requisition-table-panel (#20892)", "2024-09-09T13:03:46+03:00"], ["287b39e0e3e", "fix(supply-chain): XT-77877 Work order planning for items both manufactured and purchased (#20888)", "2024-09-09T10:59:30+02:00"], ["508c42a0852", "fix(purchasing): XT-77724 Purchase order creation from MRP calculation result (#20862)", "2024-09-09T10:56:32+02:00"], ["1da2fae5285", "refactor: XT-78276 do not export node.$.state outside of xtrem-core (#20909)", "2024-09-09T10:39:50+02:00"], ["23619d3d25c", "feat(supply-chain): XT-75380-stock-transfer-order-page (#20723)", "2024-09-09T10:11:30+02:00"], ["f188957c450", "feat(customField): fix filtering in custom field XT-76725 (#20882)", "2024-09-09T10:09:41+02:00"], ["4ddeeb4bb62", "chore(renovate): monorepo webdriverio dashboard approval", "2024-09-09T08:45:37+02:00"], ["0d8c4c3f97f", "feat(x3-stock): X3-300420 (#20707)", "2024-09-09T08:35:33+02:00"], ["4cffdc21fc4", "fix(deps): update dependency url-search-params-polyfill to v8 (#20922)", "2024-09-09T08:02:45+02:00"], ["fd3cb54086d", "fix(deps): update dependency showdown to v2 (#20916)", "2024-09-09T01:32:28+02:00"], ["005e9110cc6", "chore: updated docker pnpm-lock.yaml files", "2024-09-08T21:34:40+00:00"], ["a7834da7262", "chore: commit upgrades", "2024-09-08T21:16:22+00:00"], ["ae226632df3", "chore: bump patch version", "2024-09-08T20:04:22+00:00"], ["91dd910a222", "fix(deps): update dependency i18n-js to v4 (#20869)", "2024-09-08T21:03:07+02:00"], ["5cbeedded87", "fix: sidebar close icon XT-78135 (#20886)", "2024-09-08T20:32:22+02:00"], ["f14a3ecc68b", "fix(deps): update dependency file-type to v19.5.0 (#20915)", "2024-09-07T23:30:53+02:00"], ["86770eacf35", "chore: updated docker pnpm-lock.yaml files", "2024-09-07T21:22:57+00:00"], ["aab85f02967", "chore: commit upgrades", "2024-09-07T21:06:38+00:00"], ["3dd3b87c32a", "chore: bump patch version", "2024-09-07T20:04:22+00:00"], ["b0636f906fc", "fix(deps): update dependency rxjs to v7 (#20913)", "2024-09-07T20:31:18+02:00"], ["26449537be2", "feat(apps): endpoint ping button XT-78181 (#20865)", "2024-09-07T17:28:32+02:00"], ["651339c857f", "fix: XT-76752 random timestamp precision error in tests (#20910)", "2024-09-07T16:40:14+02:00"], ["5965eb27a99", "fix(deps): update dependency puppeteer to v23 (#20907)", "2024-09-07T15:01:08+02:00"], ["3876ec64296", "fix(deps): update dependency newrelic to v12 (#20906)", "2024-09-07T11:01:14+02:00"], ["9e1c7c8eee2", "fix: report template filter value not aligned XT-78132 (#20887)", "2024-09-07T10:57:31+02:00"], ["a63f736fd97", "fix(deps): update dependency mssql to v11 (#20905)", "2024-09-07T10:31:09+02:00"], ["9e9fbbbd5f1", "chore: remove invisible char in renovate config", "2024-09-07T09:27:24+02:00"], ["51404af68c6", "fix(deps): update dependency lighthouse to v12.2.1 (#20900)", "2024-09-07T08:31:39+02:00"], ["eff25ab93cf", "chore(deps): update dependency @chromatic-com/storybook to v1.9.0 (#20901)", "2024-09-07T08:31:34+02:00"], ["3781738b75d", "fix(deps): update dependency minimatch to v10 (#20904)", "2024-09-07T08:31:27+02:00"], ["3b1528194b0", "docs: XT-69536 unit test order by _factory.title (#19727)", "2024-09-06T23:44:35+02:00"], ["ae1b96735bb", "chore: updated docker pnpm-lock.yaml files", "2024-09-06T21:27:14+00:00"], ["38aa08194d6", "chore: commit upgrades", "2024-09-06T21:11:03+00:00"], ["4447e5d2bc3", "chore: bump patch version", "2024-09-06T20:08:03+00:00"], ["ab00729728f", "feat: XT-78265 node.$.factory API (#20898)", "2024-09-06T20:11:54+02:00"], ["02fff9133a1", "fix: XT-76752 convert postgres SQL timestamptz with millisecond precision (#20846)", "2024-09-06T11:01:55-07:00"], ["7bfc1f77d41", "perf: XT-78258 speed up TS compilation by simplifying PropertyPaths type definition (#20897)", "2024-09-06T19:29:18+02:00"], ["3e724704e24", "fix: XT-78239 delete schedule entry for content addressable GC (#20881)", "2024-09-06T18:39:15+02:00"], ["963f1c15642", "fix: turbo config to account for global pnpm-lock.yaml file (#20885)", "2024-09-06T18:33:30+02:00"], ["4209780644c", "feat: XT-77777 audit triggers (#20838)", "2024-09-06T18:32:48+02:00"], ["d8bed313fd2", "feat(finance): XT-71865 new feature file (#20895)", "2024-09-06T18:20:38+02:00"], ["6eff289214b", "fix(stock): XT-77838 NaN value when a variance is 0 (#20791)", "2024-09-06T17:33:51+02:00"], ["7e2d70e987b", "fix(deps): update dependency debug to v4.3.7 (#20871)", "2024-09-06T17:31:57+02:00"], ["9b7b1ac5f20", "feat(finance): XT-71865 data-creation (#20890)", "2024-09-06T17:25:17+02:00"], ["badbf577d25", "chore: review global hash to include full pnpm-lock.yaml (#20884)", "2024-09-06T15:40:34+02:00"], ["6053dca2b24", "feat(w3-stock): X3-319665 - Mutation now return Return only document id (#20879)", "2024-09-06T15:11:15+02:00"], ["283bdc3a831", "feat(purchasing): XT-77763 Refactor purchase-requisition file (#20763)", "2024-09-06T15:57:44+03:00"], ["10dc0e7a5d6", "feat: XT-78178 polymorphic collection data in context.create (#20873)", "2024-09-06T14:57:04+02:00"], ["b83b679f753", "fix(x3-stock): X3-319524 (#20857)", "2024-09-06T14:41:51+02:00"], ["5df62c74a72", "fix: XT-78016 fix cascaded deferred saves (#20859)", "2024-09-06T14:34:01+02:00"], ["839be77c957", "feat(xtrem-purchasing): XT-76820 Purchase-return post comment fixes (#20761)", "2024-09-06T15:13:42+03:00"], ["6f48e1fcb4a", "fix(deps): update dependency lighthouse to v12 (#20872)", "2024-09-06T12:32:00+02:00"], ["a6407d7ce22", "feat: XT-76001 <PERSON><PERSON> duplicatedValue override (#20737)", "2024-09-06T12:09:30+02:00"], ["bebf93aa1f7", "fix: revert gms-chat-ui lib to 1.3 (#20874)", "2024-09-06T11:49:17+02:00"], ["5b17901d1b7", "fix: turbo config to account for package.json & pnpm-lock.yaml files (#20876)", "2024-09-06T11:48:50+02:00"], ["564220798dc", "feat(automationcrew): XT-78080 maintenance (#20856)", "2024-09-06T12:22:40+03:00"], ["188e78cf1f8", "fix(purchasing): XT-69961Unit-of-measure-not-displaying (#20681)", "2024-09-06T14:37:57+05:30"], ["8bcc0ae6a44", "feat(xtrem-x3-inventory): X3-312991 (#20817)", "2024-09-06T10:57:25+02:00"], ["b193fab0d23", "fix(xtrem-cli-atp): XT-77436 workaround to fix pipeline issue with xtrem-cli-dev (#20858)", "2024-09-06T09:45:04+01:00"], ["df3774a7b7d", "feat: bulkaction and table options telemetry calls are updated XT-77206 (#20821)", "2024-09-06T09:38:57+02:00"], ["80ed6fda782", "fix(manufacturing): XT-77691 wrong time unit (#20814)", "2024-09-06T09:36:01+02:00"], ["5d50835e5a8", "fix: compilation issues on reporting (#20875)", "2024-09-06T09:33:36+02:00"], ["0c290bd107d", "fix: XT-78183 update of _updateXxx columns in root table (#20866)", "2024-09-06T07:19:10+02:00"], ["d3b63d6f13f", "fix(deps): update dependency eslint-plugin-unused-imports to v4 (#20867)", "2024-09-06T02:32:31+02:00"], ["20537013438", "fix(deps): update dependency glob to v11 (#20868)", "2024-09-06T02:06:14+02:00"], ["fe93df20c03", "fix(deps): update dependency @xmldom/xmldom to v0.9.2 (#20863)", "2024-09-06T00:02:59+02:00"], ["48844219d48", "fix(deps): update dependency framer-motion to v11.5.4 (#20864)", "2024-09-06T00:02:55+02:00"], ["a183481c6c4", "chore: updated docker pnpm-lock.yaml files", "2024-09-05T21:29:14+00:00"], ["353b13e2742", "chore: commit upgrades", "2024-09-05T21:11:22+00:00"], ["1407da814be", "chore: bump patch version", "2024-09-05T20:04:45+00:00"], ["f43bc023030", "feat(automationcrew): XT-59739-automation-dept-and-data (#20850)", "2024-09-05T21:36:19+03:00"], ["4117d56d433", "feat(finance): XT-78023 finance maintenance fix (#20841)", "2024-09-05T18:35:46+00:00"], ["aeb5e1c3f01", "chore: micromatch version bump (#20854)", "2024-09-05T19:49:39+02:00"], ["086f9c5bd35", "fix: print button alignment (#20851)", "2024-09-05T19:49:16+02:00"], ["128309df142", "feat: XT-78084 include _updateTick into factory.properties (#20847)", "2024-09-05T18:39:22+02:00"], ["f35168a3292", "fix(x3-stock): X3-318076 (#20799)", "2024-09-05T16:55:34+02:00"], ["a4b6ea6dc78", "feat(manufaturing): xt-78080 (#20852)", "2024-09-05T17:51:46+03:00"], ["d8eb4b05538", "fix(manufacturing): XT-77318 Work order created from a sales order line (#20770)", "2024-09-05T15:58:49+02:00"], ["a798c477f29", "feat(xtrem-sales): xt-77462 update eslint version and fix lint errors - 3rd batch (#20637)", "2024-09-05T16:54:00+03:00"], ["effc9658191", "fix(deps): update aws-sdk-js-v3 monorepo to v3.645.0 (#20840)", "2024-09-05T14:02:21+02:00"], ["a44e6175b5b", "feat: workflow editor undo-redo XT-77249 (#20643)", "2024-09-05T13:30:51+02:00"], ["f228fbed44e", "fix: error sending email test template XT-77833 (#20828)", "2024-09-05T13:05:54+02:00"], ["ef9060e9889", "feat(reference): xt-78080 (#20848)", "2024-09-05T13:02:50+03:00"], ["9090ff47ec5", "chore(deps): update dependency sass to v1.78.0 (#20839)", "2024-09-05T11:02:23+02:00"], ["1752524ae1b", "fix(xtrem-cli-atp): XT-77420 fixed issue 'querySelectorAll' error on 'parentElement()' (#20733)", "2024-09-05T10:58:01+03:00"], ["dce9db2b6f1", "fix(deps): update dependency framer-motion to v11.5.2 (#20844)", "2024-09-05T08:02:52+02:00"], ["820efd2c1f7", "chore(deps): update dependency @faker-js/faker to v9 (#20845)", "2024-09-05T08:02:47+02:00"], ["98ab41ae99d", "chore(deps): update dependency @types/node to v20.16.5 (#20842)", "2024-09-05T07:32:43+02:00"], ["9c8113747bb", "fix(deps): update dependency eslint-plugin-import to v2.30.0 (#20843)", "2024-09-05T07:32:39+02:00"], ["cd679da0416", "fix(deps): update dependency eslint-plugin-unicorn to v55 (#20782)", "2024-09-05T00:32:42+02:00"], ["abfc9005e24", "chore(deps): update dependency tsd to v0.31.2 (#20836)", "2024-09-05T00:32:38+02:00"], ["766892271df", "chore(deps): update dependency eslint-plugin-jsx-a11y to v6.10.0 (#20837)", "2024-09-05T00:32:34+02:00"], ["9b360d75b62", "chore: updated docker pnpm-lock.yaml files", "2024-09-04T22:02:30+00:00"], ["9756dc0d8e3", "chore: commit upgrades", "2024-09-04T21:40:07+00:00"], ["f1a3f52ad39", "chore: bump patch version", "2024-09-04T20:04:42+00:00"], ["83fa50e2d6f", "fix: XT-77523 Refactor using readline, pause and resume on drain (#20833)", "2024-09-04T21:00:41+02:00"], ["c243491d6ad", "fix: X3-319578 allocate the factory on external manager earlier (#20822)", "2024-09-04T20:26:35+02:00"], ["44845baa117", "feat(apps): timeout of interop graphql app to 120sec XT-74719 (#20824)", "2024-09-04T19:12:14+02:00"], ["2596d1e3b4a", "chore(deps): cleanup root package - request, webdriverio (#20829)", "2024-09-04T18:07:25+02:00"], ["53df7aee163", "feat(wh-stock-data): X3-304144 (#20302)", "2024-09-04T17:52:22+02:00"], ["256ecba28eb", "feat(apps): track records sync failures XT-72576 (#20804)", "2024-09-04T17:45:13+02:00"], ["9b610726237", "feat(shopfloor): XAPPSF-645-clock-out-incorporate-sync-transfer-time-tracking (#20831)", "2024-09-04T16:39:02+01:00"], ["0007c472db9", "fix: regex variables in reports XT-78004 (#20825)", "2024-09-04T15:38:32+02:00"], ["cf01a557e19", "fix: XT-77795 performance refactoring on duplicate mutation and getDuplicate query (#20771)", "2024-09-04T15:34:56+02:00"], ["945eded91be", "feat(automationcrew): XT-77384-maintenance-new (#20826)", "2024-09-04T16:29:06+03:00"], ["1243d5f2969", "chore: add types to conditional editor table XT-76808 (#20815)", "2024-09-04T12:33:47+02:00"], ["1181ee072c5", "feat(xtrem-sales): XT-74599 Unbilled accounts receivable unit tests (#20773)", "2024-09-04T09:21:05+01:00"], ["94ce65afdb2", "feat(sales): get rid of flushDefferedActions [Performance] (#20615)", "2024-09-04T10:12:33+02:00"], ["cfc07143a30", "feat(xtrem-purchasing): XT-77545 Refactor purchase-return-matching file (#20698)", "2024-09-04T11:07:27+03:00"], ["aefdc776250", "fix(deps): update dependency @xmldom/xmldom to ^0.9.0 (#20712)", "2024-09-04T10:02:23+02:00"], ["e23964d82cc", "feat: add support for range filter parameters XT-77423 (#20798)", "2024-09-04T07:38:33+02:00"], ["fbc092fc7f5", "fix(deps): update dependency nodemailer to v6.9.15 (#20807)", "2024-09-04T07:32:32+02:00"], ["8af1fe38a89", "chore(deps): update dependency @types/node to v20.16.4 (#20812)", "2024-09-04T07:02:41+02:00"], ["81f26427e4e", "fix(deps): update dependency yaml to v2.5.1 (#20813)", "2024-09-04T07:02:37+02:00"], ["21ff536e9af", "chore(deps): update dependency eslint-plugin-react to v7.35.2 (#20810)", "2024-09-04T02:06:29+02:00"], ["e28498ab57b", "chore(deps): update dependency http-proxy-middleware to v3.0.2 (#20811)", "2024-09-04T02:06:25+02:00"], ["a2c3e54deb8", "fix: XT-74029 fix sql conversion non stored prop (#20806)", "2024-09-03T15:19:32-07:00"], ["2879e807cea", "chore(deps): update dependency webpack-dev-server to v5.1.0 (#20808)", "2024-09-03T23:32:34+02:00"], ["28389ee8c9f", "chore: updated docker pnpm-lock.yaml files", "2024-09-03T21:23:56+00:00"], ["255bea5dee3", "chore: commit upgrades", "2024-09-03T21:07:20+00:00"], ["7efebb395cd", "chore: bump patch version", "2024-09-03T20:04:21+00:00"], ["079b90be456", "feat: X3-317441 wh-services unit test config (#20731)", "2024-09-03T21:23:17+02:00"], ["39c74a9bd49", "fix: XT-77960 fix saving of log data (#20805)", "2024-09-03T21:18:18+02:00"], ["97cae4332a9", "feat(manufacturing): XT-77501 Improve work order cucumber test (#20795)", "2024-09-03T19:25:34+02:00"], ["639566faac0", "fix(xtrem-finance): XT-77853 demo layer updates for DATEV 2 (#20794)", "2024-09-03T18:02:54+02:00"], ["1f40b39b041", "fix(stock): xt-77948-automated-test-refactoring-rename-status-column-… (#20796)", "2024-09-03T18:41:37+03:00"], ["22e7d4ed0f6", "feat(distribtion): Manage line to line associations (#20740)", "2024-09-03T17:22:16+02:00"], ["9f63f5c7116", "chore(deps): update dependency c8 to v10 (#20591)", "2024-09-03T16:32:17+02:00"], ["46cf3bf6515", "test: XT-68880 automation tests (#20788)", "2024-09-03T15:29:18+02:00"], ["e3842d5adb1", "feat: overriding icons on ag-grid tables, carbon font update XT-77551 (#20784)", "2024-09-03T14:49:56+02:00"], ["568b798b709", "fix: XT-77523 Resolve on output stream finish event (#20757)", "2024-09-03T11:18:58+02:00"], ["637ec2efc62", "fix: XT-77766 upgrade fails on inherited index (#20769)", "2024-09-03T09:14:33+02:00"], ["0f78d82b752", "feat: add isServiceOptionEnabled to $ in order to check if the service is enabled XT-77103 (#20767)", "2024-09-03T08:25:12+02:00"], ["fdd3bee8d22", "feat(finance): XT-77356 finance maintenance fix (#20772)", "2024-09-03T06:14:55+00:00"], ["cad0835502c", "feat(flat-table): ensures dialogs are usable on tablet-sized devices XT-74169 (#20754)", "2024-09-03T08:05:52+02:00"], ["f9f9e925635", "chore(deps): update dependency http-proxy-middleware to v3.0.1 (#20774)", "2024-09-03T07:02:49+02:00"], ["0ae20c1dc52", "chore(deps): update dependency eslint-plugin-react to v7.35.1 (#20780)", "2024-09-03T07:02:41+02:00"], ["dac836db7c8", "fix(deps): update dependency @sage/gms-chat-ui to v1.10.2 (#20778)", "2024-09-03T02:06:18+02:00"], ["f3dd311631f", "fix: XT-77839 use paging when dequeuing messages and notifications (#20758) (#20776)", "2024-09-02T23:36:29+02:00"], ["d4e58c6f0fe", "fix(deps): update dependency @sage/bms-dashboard to v1.74.4 (#20748)", "2024-09-02T23:32:41+02:00"], ["b1d5f666be9", "fix(deps): update dependency axios to v1.7.7 (#20775)", "2024-09-02T23:32:35+02:00"], ["15bc37dfc81", "chore: updated docker pnpm-lock.yaml files", "2024-09-02T21:22:13+00:00"], ["a42f06f8007", "chore: commit upgrades", "2024-09-02T21:05:51+00:00"], ["fb1adc00378", "chore: bump patch version", "2024-09-02T20:04:18+00:00"], ["4cadc299df5", "fix: XT-77443 implement access check on property level (#20716)", "2024-09-02T12:50:22-07:00"], ["42dfaaa83b2", "feat: XT-77777 create xtrem-auditing package (#20744)", "2024-09-02T21:48:05+02:00"], ["48b76943270", "fix(xtrem-cli-atp): XT-77621 change relative path to deprecated pipelines / remove v2 header for tenant deletion (#20686)", "2024-09-02T21:02:33+02:00"], ["360104a52b5", "fix(deps): update dependency avatax to v24.8.2 (#20749)", "2024-09-02T21:02:20+02:00"], ["37dc1a1ccdc", "chore(deps): update non-major types dependencies (#20746)", "2024-09-02T20:02:21+02:00"], ["3298f7d9f79", "feat(wh-services): X3-261140 - update artefacts (#20752)", "2024-09-02T17:10:15+02:00"], ["66db380dfd1", "docs: add docs for canAddNewLine option XT-74844 (#20765)", "2024-09-02T17:03:10+02:00"], ["c96eb5cf66e", "feat(xtrem-x3-inventory): X3-315822 (#20766)", "2024-09-02T16:42:44+02:00"], ["687fd730a6e", "fix(deps): update dependency axios to v1.7.7 (#20747)", "2024-09-02T16:32:13+02:00"], ["a0bb9a1919e", "feat: add phantom row to nested grid XT-74844 (#20743)", "2024-09-02T15:42:36+02:00"], ["4bbcd881f61", "fix: X3-319501 change import reference shared to core in generation (#20759)", "2024-09-02T15:15:06+02:00"], ["4ae2b37c99e", "chore(deps): renovate dashbaord approval for webdriverio monorepo", "2024-09-02T14:51:06+02:00"], ["03aac3cbd38", "feat(supply-chain): XT-71718 add sequence number setup for stock transfer order and shipment (#20640)", "2024-09-02T15:24:18+03:00"], ["23fff7338d7", "feat: XT-72452 revert exclusive execution of node sync using global lock (#20755)", "2024-09-02T13:25:35+02:00"], ["4516f8e1f04", "fix: make ingredient page clean on save XT-75889 (#20751)", "2024-09-02T12:26:51+02:00"], ["ffd3432c128", "fix(manufacturing): XT-70991 Allocations tab in Stock detailed inquiry (#20734)", "2024-09-02T15:19:13+05:30"], ["ad6d66c8192", "feat(workflow): XT-77726 add notifies decorator (#20732)", "2024-09-02T10:00:17+01:00"], ["40716426a69", "fix(finance): XT-77095 added tax line table (#20500)", "2024-09-02T14:12:23+05:30"], ["019a2ae8294", "fix(company): XT-69376 - set active switch to on by default in business entity contact panel (#20660)", "2024-09-02T13:42:57+05:30"], ["2d7cc2b513d", "feat(distribution): XT-56453 sales notes propagation (#20738)", "2024-09-02T07:50:08+00:00"], ["39fb8ca3308", "feat(distribution): XT-60319 purchase notes propagation (#20739)", "2024-09-02T07:49:40+00:00"], ["c9b917ba244", "feat(apps): http url datatype XT-72576 (#20727)", "2024-09-02T09:35:41+02:00"], ["80c6061fa33", "feat(reference): XT-75381 datacreation (#20700)", "2024-09-02T09:31:08+02:00"], ["ea353beaf1d", "chore: updated docker pnpm-lock.yaml files", "2024-09-01T21:20:34+00:00"], ["7dd7d94fc64", "chore: commit upgrades", "2024-09-01T21:02:12+00:00"], ["d58de7027df", "chore: bump patch version", "2024-09-01T20:07:35+00:00"], ["871805ac988", "chore: updated docker pnpm-lock.yaml files", "2024-08-31T21:22:52+00:00"], ["3571e676eef", "chore: commit upgrades", "2024-08-31T21:04:45+00:00"], ["8bf156d9909", "chore: bump patch version", "2024-08-31T20:10:41+00:00"], ["18d28abdda3", "chore: updated docker pnpm-lock.yaml files", "2024-08-30T21:18:45+00:00"], ["7d2c5aa2068", "chore: commit upgrades", "2024-08-30T21:03:40+00:00"], ["1db48096209", "chore: bump patch version", "2024-08-30T20:07:46+00:00"], ["6c8adc8f4fc", "fix: XT-77764 inheritance of 'notifies' attribute (#20728)", "2024-08-30T17:23:00+02:00"], ["9c09c1808c2", "fix(manufacturing): XT-77501 test if level is undefined (#20720)", "2024-08-30T15:28:01+02:00"], ["5a5744e26d7", "fix: do not fetch nav panel items for duplication dialog XT-77779 (#20735)", "2024-08-30T15:25:51+02:00"], ["0704b2c5049", "fix: bulk header not appearing XT-76677 (#20736)", "2024-08-30T15:14:46+02:00"], ["59f28d31338", "feat: custom number filter on tables XT-77087 XT-58369 (#20722)", "2024-08-30T14:58:23+02:00"], ["8647d68576b", "feat(xtrem-cli-atp): XT-76180 verify a block is displayed (#20656)", "2024-08-30T14:54:11+02:00"], ["7992926de90", "fix: Date-time range title XT-76678 (#20730)", "2024-08-30T14:49:32+02:00"], ["bde79951fd6", "fix: Dialog showcase dropdown action XT-76448 (#20717)", "2024-08-30T14:22:32+02:00"], ["87bb29fc406", "feat: XT-75265 <PERSON> (#20697)", "2024-08-30T13:49:12+02:00"], ["aa0e380a45f", "feat(xtrem-intacct): XT-76741 hide the intacct activation service option (#20701)", "2024-08-30T12:13:56+01:00"], ["368cd9457c0", "feat: XT-72452 exclusive execution of node sync using global lock (#20704)", "2024-08-30T10:44:52+02:00"], ["d9dbbe74b11", "feat(shopfloor): XAPPSF-539-tracking-operator-refactoring (#20705)", "2024-08-30T09:34:16+01:00"], ["a0e4d86d9e8", "chore: revert sys-changelog when fixing csv layers (#20706)", "2024-08-30T10:14:14+02:00"], ["a8c76bd1581", "feat: custom number floating filter on tables XT-77087 (#20703)", "2024-08-30T09:51:47+02:00"], ["733790f16ae", "fix(intacct-finance): XT-77093 secure the creditLimit value when synchronizing with intacct (#20648)", "2024-08-30T09:08:28+02:00"], ["447870167d5", "fix(xtrem-purhasing): XT-77385 purchase credit memo link not working (#20662)", "2024-08-30T02:53:02+01:00"], ["600975f086f", "chore(deps): update dependency @chromatic-com/storybook to v1.8.0 (#20711)", "2024-08-30T02:06:00+02:00"], ["daf1ff78800", "fix(deps): update dependency framer-motion to v11.3.31 (#20710)", "2024-08-30T01:32:07+02:00"], ["63bd3a11961", "chore(deps): update dependency uglify-js to v3.19.3 (#20709)", "2024-08-29T23:32:16+02:00"], ["046a2eacb59", "chore: updated docker pnpm-lock.yaml files", "2024-08-29T21:15:34+00:00"], ["b59535b5419", "chore: commit upgrades", "2024-08-29T21:00:19+00:00"], ["ef3d054e1bb", "chore: bump patch version", "2024-08-29T20:04:14+00:00"], ["2c98a3bc039", "chore(deps): update dependency @testing-library/react to v16.0.1 (#20708)", "2024-08-29T21:31:46+02:00"], ["899596d8187", "fix: x3 unit tests dup ip address (#20672)", "2024-08-29T18:27:29+02:00"], ["92a1a14c9b5", "fix: XT-75717 dependsOn typing on property overrides (#20702)", "2024-08-29T18:01:56+02:00"], ["26a9380006b", "fix: XT-77523 Fix line based stream processing (#20690)", "2024-08-29T15:57:24+02:00"], ["b8c36dad361", "fix(xtrem-cli-atp): XT-77063 -fix the timeout error when selecting row before scrolling (#20569)", "2024-08-29T15:35:45+03:00"], ["be6facf666b", "fix(xtrem-cli-atp): XT-77223 adding missing data value case (#20634)", "2024-08-29T15:32:37+03:00"], ["20110817ace", "fix(xtrem-manufacturing): XT-76916 Duplication of a work order with a text component gives error (#20689)", "2024-08-29T14:27:34+02:00"], ["e686e3f408e", "chore: remove mxx cli package (#20695)", "2024-08-29T13:58:53+02:00"], ["0d536cc2c17", "fix: add max length limit to dashboard title input XT-77594 (#20692)", "2024-08-29T13:54:23+02:00"], ["efc519c95b5", "fix: filter out binary stream properties from widget editor XT-77590 (#20693)", "2024-08-29T13:54:11+02:00"], ["40d04a8301d", "feat(xtrem-supply-chain): XT-77036 stock transfer order refactoring after transfer shipment base node refactoring (#20613)", "2024-08-29T13:23:49+02:00"], ["1e0dd326847", "feat(finance): XT-68686 feature files (#20696)", "2024-08-29T13:19:10+02:00"], ["2743b12c989", "feat: XT-72452 add app to sync state (#20664)", "2024-08-29T12:26:52+02:00"], ["352865420b6", "feat(finance): XT-68686 data creation (#20683)", "2024-08-29T10:56:57+02:00"], ["25570fa8992", "fix(xtrem-cli-atp): XT-75711 sales return request - robot fails to scroll to end of panel (#20261)", "2024-08-29T08:45:18+02:00"], ["2832998e41b", "chore: updated translation files 20240828.1 (#20674)", "2024-08-29T08:02:31+02:00"], ["88a2950c4c7", "fix(deps): update cucumber (major) (#20679)", "2024-08-29T07:43:37+02:00"], ["95434631660", "fix(deps): update dependency @sage/intacct-controlid-provider to v3 (#20680)", "2024-08-29T07:02:34+02:00"], ["4c9c0708b04", "chore(deps): update dependency @types/pg to v8.11.8 (#20677)", "2024-08-29T02:06:11+02:00"], ["8bf1655feae", "feat(apps): purge sync history older than 2 weeks XT-75408 (#20666)", "2024-08-28T23:47:01+02:00"], ["65b1cfdc619", "fix(deps): update dependency @sage/visual-process-editor to v1.11.6 (#20675)", "2024-08-28T23:32:03+02:00"], ["33da36831f7", "chore: updated docker pnpm-lock.yaml files", "2024-08-28T21:24:21+00:00"], ["5452c2fdb90", "chore: commit upgrades", "2024-08-28T21:09:17+00:00"], ["a49fca752f9", "chore: bump patch version", "2024-08-28T20:07:48+00:00"], ["a5274dd1610", "fix: XT-77523 <PERSON><PERSON> from dump delete files while processing (#20671)", "2024-08-28T20:16:54+02:00"], ["06e1607b8a7", "fix: downgrade chatbot client lib XT-77514 (#20669)", "2024-08-28T18:59:01+02:00"], ["50db10866b1", "fix: custom select fields on form designer tables XT-77571 (#20667)", "2024-08-28T18:31:17+02:00"], ["3814098586a", "feat(purchasing): XT-76820 Refactor purchase-return file (#20387)", "2024-08-28T17:25:16+03:00"], ["e3846a49276", "fix(stock): XT-76932 fix sub-assembly required quantity (#20421)", "2024-08-28T14:17:34+01:00"], ["37f759199ac", "fix: remove page dialog values XT-76448 (#20655)", "2024-08-28T14:46:16+02:00"], ["2470e9ca7c9", "fix: table action color highlight fix (#20661)", "2024-08-28T14:44:39+02:00"], ["dd4393e49d7", "feat: improve cloudbeaver setup (#20645)", "2024-08-28T14:21:10+02:00"], ["63db887e378", "fix: csp to allow youtube for pendo XT-77513 (#20659)", "2024-08-28T13:57:04+02:00"], ["fdd83cada8a", "fix(xtrem-cli-atp): XT-75649 interact with multi action button (#20510)", "2024-08-28T13:01:20+02:00"], ["1928276c14f", "feat(xtrem-sales): xt-77462 update eslint version and fix lint errors - 2nd batch (#20636)", "2024-08-28T13:11:08+03:00"], ["9e201c27f8e", "feat(xtrem-sales): xt-77462 update eslint version and fix lint errors -1st batch (#20635)", "2024-08-28T13:09:39+03:00"], ["acf102cdae3", "fix(stock): XT-73292 stock receipt post lock (#20624)", "2024-08-28T11:25:21+02:00"], ["a1b787e3da4", "feat: XT-75479 ServiceOption change callbacks (#20447)", "2024-08-28T10:47:24+02:00"], ["ad7bc899a0a", "fix(reporting): XT-65361 header-body-overlapping (#20610)", "2024-08-28T13:53:42+05:30"], ["58b854aff7f", "chore(deps): update non-major types dependencies (#20653)", "2024-08-28T09:32:06+02:00"], ["7f7e3de8d0f", "feat(getDefaults): remove logger on defaultValue error XT-74726 (#20229)", "2024-08-28T08:57:16+02:00"], ["a6d142d802b", "fix(x3-stock): X3-318905 (#20464)", "2024-08-28T08:51:35+02:00"], ["83e759f79dd", "chore(deps): update dependency @testing-library/dom to v10.4.0 (#20654)", "2024-08-28T07:02:24+02:00"], ["38a4bc17036", "chore(deps): update dependency @types/chai to v4.3.19 (#20651)", "2024-08-28T02:07:02+02:00"], ["f27ad11bec6", "chore(deps): update testing-library (major) (#20652)", "2024-08-28T02:06:49+02:00"], ["2b7b03c5356", "chore(deps): update dependency tsx to v4.19.0 (#20649)", "2024-08-27T23:32:28+02:00"], ["3d8e6d7d6b1", "chore(deps): update dependency webpack-merge to v6 (#20650)", "2024-08-27T23:32:24+02:00"], ["8805e892d21", "chore: updated docker pnpm-lock.yaml files", "2024-08-27T21:15:35+00:00"], ["22da18998d3", "chore: commit upgrades", "2024-08-27T21:00:34+00:00"], ["3ef82a1c4cd", "chore: bump patch version", "2024-08-27T20:04:20+00:00"], ["054bbd88477", "feat(shopfloor): XAPPSF-528 Generate new sync tracking document (#20631)", "2024-08-27T19:10:47+01:00"], ["13cddf17f89", "feat: XT-76340 loosen controls on order by and filter property mappings and apply paging options on raw results (#20626)", "2024-08-27T19:09:07+02:00"], ["dcf24695883", "feat: add add line button to mobile table XT-68880 (#20482)", "2024-08-27T17:23:05+02:00"], ["43168543bad", "feat(xtrem-stock): XT-68114 stock-issue-page (#20485)", "2024-08-27T16:58:58+02:00"], ["85af938fc4d", "feat(tc): APINODGEN - Transient property not set to true (#20642)", "2024-08-27T15:44:27+02:00"], ["de27a57b26e", "feat(xtrem-cli-atp): XT-76567 title subTitle page verification (#20622)", "2024-08-27T12:20:23+02:00"], ["a2519c16750", "feat(purchasing): XT-76783 Refactor unbilled-account-payable-inquiry file (#20344)", "2024-08-27T13:04:23+03:00"], ["3f316a6fc9b", "feat: chain step deletion from workflow editor XT-77248 (#20550)", "2024-08-27T11:26:13+02:00"], ["abc1234ae5c", "feat: upgrade ag-grid to v32.1.0 XT-75075 (#20449)", "2024-08-27T11:10:04+02:00"], ["74193db6137", "feat(xtrem-stock): xt-77002 fix lint errors on xtrem stock - 1st batch (#20611)", "2024-08-27T11:29:16+03:00"], ["9220370e72f", "feat(manufacturing): XT-76340 update sdmo for shopfloor (#20424)", "2024-08-27T09:25:58+01:00"], ["351e8e5933e", "feat(test): XT-77379 fix accessibility test (#20620)", "2024-08-27T09:29:28+02:00"], ["8807fdf34c1", "chore(deps): update dependency @types/chai to v4.3.18 (#20627)", "2024-08-27T07:26:37+02:00"], ["cb4783c6c03", "chore(deps): update dependency ts-essentials to v10 (#20629)", "2024-08-27T07:20:46+02:00"], ["c0657b83257", "chore(deps): update dependency ts-morph to v23 (#20630)", "2024-08-27T01:31:51+02:00"], ["d8799485cfe", "chore: updated docker pnpm-lock.yaml files", "2024-08-26T23:17:02+00:00"], ["5a94b294325", "chore: commit upgrades", "2024-08-26T23:06:25+00:00"], ["19e49b3db5b", "chore: bump patch version", "2024-08-26T22:05:10+00:00"], ["eba18ebbfbe", "chore(deps): revert ATP deps updates (#20625)", "2024-08-26T22:47:10+02:00"], ["52568a2f88e", "fix(manufacturing): XT-77112 fix work order component description (#20487)", "2024-08-26T20:21:25+02:00"], ["6b790eec84f", "fix(master): XT-76097 Sequence number is active (#20549)", "2024-08-26T18:01:15+02:00"], ["92328b3a7c3", "fix(core): fixes duplicate issue on subnoding XT-75846 (#20383)", "2024-08-26T17:50:24+02:00"], ["1b06fbcde8d", "fix: X3-316202 - display with unit's number of decimals (#20137)", "2024-08-26T17:18:50+02:00"], ["bead6024e67", "fix: sorting on deep computed properties XT-75167 (#20621)", "2024-08-26T16:35:55+02:00"], ["5b1e06bb8c7", "fix: not possible to add line from purchase receipt XT-75382 (#20619)", "2024-08-26T15:30:58+02:00"], ["f056b78f64a", "feat(apps): improve connector error handling XT-75401 (#20559)", "2024-08-26T14:52:20+02:00"], ["36c3260bc46", "chore: identify files raising an error 123 (#20561)", "2024-08-26T14:17:45+02:00"], ["a7f95667379", "chore(deps): remove overrides fast-xml-parser (#20616)", "2024-08-26T14:14:05+02:00"], ["d30f52fec2a", "chore(deps): update dependency supertest to v7 (#20608)", "2024-08-26T12:31:39+02:00"], ["9873d3f723e", "feat(tc): <PERSON><PERSON> into fallback joins branch (#20614)", "2024-08-26T11:35:39+02:00"], ["b0e16ef59a3", "fix(xtrem-cli-atp): XT-75831 added wait for element to exist before c… (#20509)", "2024-08-26T11:51:51+03:00"], ["ec059ae7c7a", "chore(guard): compare full version first (#20612)", "2024-08-26T10:43:18+02:00"], ["fa6343e16b5", "fix(xtrem-purchasing): XT-73827 purchase receipt origin link (#19914)", "2024-08-26T09:24:33+01:00"], ["90d01979206", "feat(x3-stock): X3-300416-qa-fix (#20366)", "2024-08-26T09:04:22+02:00"], ["ed10bb4db12", "chore(deps): update dependency style-loader to v4 (#20606)", "2024-08-26T07:31:55+02:00"], ["6bf18f74e33", "fix(deps): update dependency @sage/gms-chat-ui to v1.9.17 (#20607)", "2024-08-26T07:31:50+02:00"], ["9f9ec66dde4", "chore(deps): update dependency rimraf to v6 (#20605)", "2024-08-26T07:02:33+02:00"], ["3ff4aa9798a", "chore(deps): update dependency sinon to v18 (#20604)", "2024-08-26T02:05:49+02:00"], ["66fd4318336", "chore(deps): update dependency rimraf to v5.0.10 (#20603)", "2024-08-26T00:02:10+02:00"], ["4b1a1262d8a", "chore(deps): update dependency rimraf to v6 (#20601)", "2024-08-25T23:32:07+02:00"], ["9ccbe9a8194", "chore(deps): update dependency sass-loader to v16 (#20602)", "2024-08-25T23:32:03+02:00"], ["a863ac492ef", "chore: updated docker pnpm-lock.yaml files", "2024-08-25T21:19:21+00:00"], ["c30eb79183c", "chore: commit upgrades", "2024-08-25T21:03:17+00:00"], ["8bd009d092b", "chore: bump patch version", "2024-08-25T20:04:00+00:00"], ["0cfc9954ce4", "chore(deps): update dependency jsdom to v25 (#20599)", "2024-08-25T20:02:26+02:00"], ["c97ea13f7c2", "chore: updated translation files 20240823.2 (#20560)", "2024-08-25T18:40:33+02:00"], ["e7042bf455e", "chore(deps): update dependency fake-indexeddb to v6 (#20598)", "2024-08-25T16:32:00+02:00"], ["b61f24197d7", "chore(deps): update dependency jsdom to v24.1.3 (#20597)", "2024-08-25T16:01:52+02:00"], ["149406b747f", "chore(deps): update dependency jsdom to v24.1.2 (#20595)", "2024-08-25T13:32:03+02:00"], ["8e44d900bc7", "chore(deps): update dependency css-minimizer-webpack-plugin to v7 (#20594)", "2024-08-25T11:31:45+02:00"], ["8ea61b87e5a", "chore(deps): update dependency ts-patch to v3.2.1 (#20539)", "2024-08-25T10:13:10+02:00"], ["d6b724424c8", "fix(deps): update dependency tslib to v2.7.0 (#20585)", "2024-08-25T02:31:51+02:00"], ["f0370e4e897", "fix(deps): update dependency yaml to v2.5.0 (#20588)", "2024-08-25T02:31:46+02:00"], ["bf64c6b08a6", "fix(deps): update dependency winston to v3.14.2 (#20587)", "2024-08-25T02:04:55+02:00"], ["8b6f4f2830b", "fix(deps): update dependency wdio-cucumberjs-json-reporter to v5.2.1 (#20586)", "2024-08-25T01:31:17+02:00"], ["46895479a14", "chore(deps): update dependency tsx to v4.18.0 (#20583)", "2024-08-24T23:31:03+02:00"], ["8997d202410", "fix(deps): update dependency re2 to v1.21.4 (#20584)", "2024-08-24T23:30:59+02:00"], ["e6a2d5ab590", "chore: updated docker pnpm-lock.yaml files", "2024-08-24T21:13:38+00:00"], ["13b91359e8d", "chore: commit upgrades", "2024-08-24T20:58:49+00:00"], ["4aa7ef3e54a", "chore: bump patch version", "2024-08-24T20:03:36+00:00"], ["ef0e24cc5d1", "fix(deps): update dependency graphiql to v3.7.1 (#20580)", "2024-08-24T20:01:09+02:00"], ["14c7015eeb7", "chore(deps): update dependency http-proxy-middleware to v3 (#20582)", "2024-08-24T19:01:12+02:00"], ["23198df064b", "fix(deps): update dependency monaco-editor to ^0.51.0 (#20579)", "2024-08-24T18:31:11+02:00"], ["d76ceac53a6", "fix(deps): update dependency oracledb to v6.6.0 (#20581)", "2024-08-24T18:31:04+02:00"], ["910b1bf73d3", "fix(deps): update dependency graphiql to v3.7.0 (#20578)", "2024-08-24T17:30:47+02:00"], ["1093fb30936", "fix(deps): update dependency file-type to v19.4.1 (#20577)", "2024-08-24T14:31:18+02:00"], ["64c4f99c02f", "fix(deps): update dependency esquery to v1.6.0 (#20576)", "2024-08-24T14:01:16+02:00"], ["8751b6b02e2", "fix(deps): update dependency eslint-plugin-mocha to v10.5.0 (#20575)", "2024-08-24T12:31:03+02:00"], ["4e311ec0b86", "fix(deps): update dependency core-js to v3.38.1 (#20574)", "2024-08-24T11:01:02+02:00"], ["aebb6047aac", "fix(deps): update dependency @sage/gms-chat-ui to v1.9.13 (#20572)", "2024-08-24T08:31:05+02:00"], ["748600c3f5c", "fix(deps): update dependency @sage/bms-dashboard to v1.74.1 (#20571)", "2024-08-24T07:10:20+02:00"], ["bbeceee1cbb", "feat: document assignment and print button on pages XT-75167 (#20475)", "2024-08-24T07:09:40+02:00"], ["aaf78449fbd", "fix(deps): update dependency @cucumber/cucumber to v10.9.0 (#20570)", "2024-08-24T07:01:29+02:00"], ["f0e8f4408d3", "fix(deps): update dependency avatax to v24.6.4 (#20573)", "2024-08-24T07:01:14+02:00"], ["849ea3aaf0d", "fix(deps): update dependency framer-motion to v11.3.30 (#20567)", "2024-08-24T02:04:12+02:00"], ["457c7c17a76", "chore(deps): update dependency @testing-library/jest-dom to v6.5.0 (#20568)", "2024-08-24T01:31:46+02:00"], ["a6275f85727", "chore: update pnpm to v9.8.0 (#20563)", "2024-08-24T00:06:36+02:00"], ["a1a91b56c1f", "chore(deps): update dependency ts-jest to v29.2.5 (#20565)", "2024-08-23T23:31:55+02:00"], ["cade0f13cb9", "fix(deps): update dependency axios to v1.7.5 (#20566)", "2024-08-23T23:31:51+02:00"], ["d8e65a378d0", "chore: updated docker pnpm-lock.yaml files", "2024-08-23T21:16:24+00:00"], ["48548c4b701", "chore: commit upgrades", "2024-08-23T21:01:03+00:00"], ["5c4247f053d", "chore: bump patch version", "2024-08-23T20:04:38+00:00"], ["9c042904f97", "fix: XT-73624 special cases for _id (#20562)", "2024-08-23T11:35:53-07:00"], ["ecd2e27b6b7", "feat: X3-319163 improve fallback join generation (#20564)", "2024-08-23T20:24:40+02:00"], ["31b25449f40", "feat(finance): XT-77259 small adjustments (#20527)", "2024-08-23T17:24:22+00:00"], ["537970c63c8", "feat: XT-76340 X3 _id natural key filter (#20558)", "2024-08-23T18:28:19+02:00"], ["1712f950a56", "feat(distribution): XT-74991-fix-base-line-to-base-outbound-line-properties (#20552)", "2024-08-23T16:13:50+02:00"], ["b818460e4bd", "feat(xtrem-sales): XT-72777 Fixed remaining comments from previous PR (#20224)", "2024-08-23T15:43:03+03:00"], ["421c2927834", "feat(xtrem-sales): xt-76096 fix lint errors on xtrem sales - 3rd batch (#20337)", "2024-08-23T15:42:44+03:00"], ["049cff9725a", "feat(apps-error-handling): enhance logMessage XT-75401 (#20503)", "2024-08-23T14:36:23+02:00"], ["47234f3f5bd", "feat(xtrem-cli-atp): XT-70937 - add missing parameters (#20553)", "2024-08-23T14:28:22+02:00"], ["ad58751b4d8", "fix(uxcrew): XT-77197 error on category tunnel item page (#20537)", "2024-08-23T13:35:03+02:00"], ["1b7e1d60b1c", "feat(xtrem-stock-data): XT-77209-quality-control-item-site (#20521)", "2024-08-23T13:31:58+02:00"], ["bddbfae188c", "feat(xtrem-cli-atp): XT-76322 yml parameters missing (#20548)", "2024-08-23T12:59:40+02:00"], ["b0eadc0f596", "feat(finance): XT-68686 expense revenue account by tax (#20551)", "2024-08-23T12:20:48+02:00"], ["d38218b6088", "chore(deps): update dependency webpack to v5.94.0 (#20544)", "2024-08-23T11:02:17+02:00"], ["e9ba79e4072", "chore(deps): update non-major types dependencies (#20545)", "2024-08-23T11:02:11+02:00"], ["e5875875496", "feat(shopfloor): XAPPSF-527-clock-out-operator-disconnect-logged-in-user (#20530)", "2024-08-23T09:45:33+01:00"], ["74deb89d4df", "feat: auto setup turbo and cloudbeaver (#20538)", "2024-08-23T09:45:08+02:00"], ["8f2606a27e9", "fix: workflow component improvements XT-76833 XT-76834 (#20498)", "2024-08-23T08:47:34+02:00"], ["b11b891c46a", "feat: improved file deposit component that supports read only mode XT-77079 (#20529)", "2024-08-23T08:47:15+02:00"], ["0725a16d1d0", "feat: XT-77213 review print-document workflow action (#20501)", "2024-08-23T08:31:19+02:00"], ["5b3abf2cf8a", "fix(deps): update aws-sdk-js-v3 monorepo to v3.637.0 (#20547)", "2024-08-23T08:02:14+02:00"], ["43621cf12bb", "chore(deps): update storybook monorepo to v8.2.9 (#20546)", "2024-08-23T07:31:41+02:00"], ["0e5805e2f8c", "chore(deps): update dependency eslint-plugin-jsx-a11y to v6.9.0 (#20514)", "2024-08-23T02:05:53+02:00"], ["295bede67b3", "chore(deps): update dependency uglify-js to v3.19.2 (#20542)", "2024-08-23T02:05:46+02:00"], ["b884cf3ce8a", "chore(deps): update dependency v8-to-istanbul to v9.3.0 (#20543)", "2024-08-23T01:31:30+02:00"], ["ba0f54deab0", "chore(deps): update dependency tsx to v4.17.0 (#20540)", "2024-08-22T23:31:51+02:00"], ["5bedb075e6d", "chore: updated docker pnpm-lock.yaml files", "2024-08-22T21:23:08+00:00"], ["f5b71454159", "chore: commit upgrades", "2024-08-22T21:04:27+00:00"], ["7a89ea2a2d8", "chore: bump patch version", "2024-08-22T20:04:42+00:00"], ["307d2690a78", "feat(finance): XT-77154 small refactoring due to refresh of the screen (#20505)", "2024-08-22T19:13:19+00:00"], ["92c77527944", "fix: prevent sorting of children of computed objects XT-75414 (#20502)", "2024-08-22T21:10:14+02:00"], ["30a891ea7d6", "fix(xtrem-cli-atp): XT-58851 - Timeout management - refactoring (#20271)", "2024-08-22T20:05:08+02:00"], ["671e1823696", "feat(xtrem-cli-atp): XT-76814 store the value of a hidden field (#20457)", "2024-08-22T17:54:44+02:00"], ["ca0cc69aae2", "docs: Remove query example from GraphiQL X3-314535 (#20526)", "2024-08-22T17:46:10+02:00"], ["d66573689b8", "fix(xtrem-cli-atp): XT-75744 adding if condition to verify that actio… (#20235)", "2024-08-22T18:16:20+03:00"], ["503f0048b5c", "fix: passing on record context to lookup dialog XT-77135 (#20523)", "2024-08-22T16:52:56+02:00"], ["e16b770744a", "feat(supply-chain): XT-71714-stock-transfer-shipment-nodes (#20455)", "2024-08-22T17:30:44+03:00"], ["0a0c1931036", "feat: XT-72452 stop sync of frozen properties (#20528)", "2024-08-22T16:21:27+02:00"], ["fa8f5a2ef4d", "feat: XT-76340 fixes on xtrem-app-metadata (#20531)", "2024-08-22T16:17:38+02:00"], ["9e26357682d", "feat(xtrem-stock): XT-77176-delete-stock-receipt (#20507)", "2024-08-22T15:52:30+02:00"], ["68760faa6da", "feat(xtrem-sales): xt-72777 fix lint errors on xtrem sales - 2nd batch (#20143)", "2024-08-22T16:06:10+03:00"], ["7bcdbfdf3a1", "chore: updated docker pnpm-lock.yaml files", "2024-08-22T12:25:08+00:00"], ["fc7f103f9c7", "chore: commit upgrades", "2024-08-22T12:08:30+00:00"], ["f005ed4f10f", "chore: bump patch version", "2024-08-22T11:15:16+00:00"], ["2a166205130", "chore: updated docker pnpm-lock.yaml files", "2024-08-22T10:53:46+00:00"], ["d37f77cbd37", "shopfloor-main: manual empty file to jump from 46.0.38 to 47.0.0", "2024-08-22T10:10:55+00:00"], ["fbd773ef37c", "xtrem-show-case: manual empty file to jump from 46.0.38 to 47.0.0", "2024-08-22T10:10:52+00:00"], ["1ad097456d7", "xtrem-glossary: manual empty file to jump from 46.0.38 to 47.0.0", "2024-08-22T10:10:48+00:00"], ["95a40bd3469", "xtrem-services-main: manual empty file to jump from 46.0.38 to 47.0.0", "2024-08-22T10:10:44+00:00"], ["a96db100988", "chore: bump major version", "2024-08-22T10:10:39+00:00"], ["c31a3a54053", "chore: check node version on postinstall (#20522)", "2024-08-22T11:49:04+02:00"], ["33bf80d8fbb", "chore: unlock pr failure (#20525)", "2024-08-22T11:42:05+02:00"], ["397922aee22", "chore(deps): update dependency node-mocks-http to v1.15.1 (#20518)", "2024-08-22T10:32:30+02:00"], ["807711e867b", "chore: updated docker pnpm-lock.yaml files", "2024-08-22T07:36:51+00:00"], ["d5851366e0a", "chore: commit upgrades", "2024-08-22T07:22:04+00:00"], ["e4ef9a02b34", "chore: bump patch version", "2024-08-22T06:27:51+00:00"], ["441b0ec253e", "chore(deps): update dependency mocha to v10.7.3 (#20516)", "2024-08-22T07:32:49+02:00"], ["3e8153e7630", "chore(deps): update dependency node to v20.17.0 (#20517)", "2024-08-22T07:32:43+02:00"], ["4d46d411e0d", "chore(deps): update dependency ts-jest to v29.2.4 (#20519)", "2024-08-22T07:02:57+02:00"], ["3ec7fc59183", "chore(deps): update dependency mime-db to v1.53.0 (#20515)", "2024-08-22T02:06:46+02:00"], ["21e81cedf75", "chore(deps): update dependency allure-commandline to v2.30.0 (#20494)", "2024-08-21T23:32:55+02:00"], ["0173558b108", "fix(deps): update dependency framer-motion to v11.3.29 (#20511)", "2024-08-21T23:32:50+02:00"], ["66c04025971", "chore(deps): update dependency chai to v4.5.0 (#20512)", "2024-08-21T23:32:44+02:00"], ["0635cc359cf", "chore: updated docker pnpm-lock.yaml files", "2024-08-21T21:10:50+00:00"], ["aacd5470978", "chore: commit upgrades", "2024-08-21T20:56:17+00:00"], ["dfd07a5d601", "chore: bump patch version", "2024-08-21T20:03:55+00:00"], ["29d6a4587bf", "fix(deps): update dependency nodemailer to v6.9.14 (#20431)", "2024-08-21T19:32:28+02:00"], ["594beb74c9e", "chore(deps): update aws-sdk to v3.635.0 and dedup (#20506)", "2024-08-21T17:40:49+02:00"], ["f924b73bd60", "fix: fix not click row card if popover is clicked (#20504)", "2024-08-21T16:30:56+02:00"], ["a7b7d19944d", "fix: XT-76230 Resolve on end callback (#20508)", "2024-08-21T16:10:36+02:00"], ["aa093e18e40", "fix: XT-77164 takeOne conversion to SQL (#20496)", "2024-08-21T11:26:03+02:00"], ["c3e2a93b15e", "feat: XT-77070 fix remote interop package name (#20484)", "2024-08-21T09:15:29+02:00"], ["976ea2f9837", "fix: filter not working in report template XT-76213 (#20452)", "2024-08-21T09:14:13+02:00"], ["e8b18c79f21", "fix(deps): update dependency react-aria-components to v1.3.3 (#20492)", "2024-08-21T07:32:17+02:00"], ["86286e86347", "chore(deps): update dependency axe-core to v4.10.0 (#20495)", "2024-08-21T07:32:07+02:00"], ["e8a73d17332", "fix: check sql file in binary mode XT-77139 (#20486)", "2024-08-21T07:06:22+02:00"], ["e2de722c73a", "chore(deps): update dependency @chromatic-com/storybook to v1.7.0 (#20493)", "2024-08-21T07:02:42+02:00"], ["c3d15029003", "fix(deps): update dependency react-aria-components to v1.3.2 (#20491)", "2024-08-21T01:03:15+02:00"], ["100fc7d2d2f", "fix(deps): update dependency axios to v1.7.4 (#20488)", "2024-08-21T00:32:44+02:00"], ["00224182557", "fix(deps): update dependency dayjs to v1.11.13 (#20489)", "2024-08-21T00:32:40+02:00"], ["e7a2018a56d", "fix(deps): update dependency framer-motion to v11.3.28 (#20490)", "2024-08-21T00:32:36+02:00"], ["fcc8ade3ec3", "chore: updated docker pnpm-lock.yaml files", "2024-08-20T22:16:24+00:00"], ["e411784c147", "chore: commit upgrades", "2024-08-20T21:54:26+00:00"], ["6f6da03013e", "chore: bump patch version", "2024-08-20T20:04:35+00:00"], ["1c908f9cffe", "chore(deps): update dependency typescript-json-schema to ^0.65.0 (#20468)", "2024-08-20T20:02:46+02:00"], ["4e4aee9afa2", "feat: XT-77127 review read-entity action (#20480)", "2024-08-20T19:57:38+02:00"], ["8671c8d850b", "fix(finance-data): XT-75168 SDMO Stock Receipt GL Account Posting (#20319)", "2024-08-20T19:47:31+02:00"], ["697afed7b45", "feat(uxcrew): XT-76983 obsolete item adjusments (#20414)", "2024-08-20T19:16:57+02:00"], ["d07ed1ade88", "fix: upgrade recording fail on enum default value XT-77139 (#20483)", "2024-08-20T18:23:39+02:00"], ["9fc53f4e25d", "feat(finance): XT-77099 tests refactoring (#20481)", "2024-08-20T15:45:51+00:00"], ["bdcadac078b", "fix(sales): XT-44479 Minimum order quantity is being applied to shipments, invoices and returns (#20416)", "2024-08-20T17:19:43+02:00"], ["a527ed93fc6", "fix(deps): update dependency winston-transport to v4.7.1 (#20434)", "2024-08-20T17:02:51+02:00"], ["fbcd2d67176", "feat: XT-77070 exclude sync state and manage properties from tenant export (#20478)", "2024-08-20T15:57:19+02:00"], ["fb2b4b1e99d", "feat: XT-77129 prettify json files in layers (#20479)", "2024-08-20T15:47:06+02:00"], ["df901efc6e1", "chore: axios upgrade 1.7.4 CVE-2024-39338 (#20448)", "2024-08-20T15:34:20+02:00"], ["d2da281781c", "fix(xtrem-stock): XT-76012 stock adjustment status resync (#20327)", "2024-08-20T15:34:03+02:00"], ["33c83ee0125", "fix(pipelines): X3-317775 enabling wh services release image (#20473)", "2024-08-20T14:31:51+02:00"], ["94f2d761755", "feat: XT-75583 start interop service (#20460)", "2024-08-20T12:21:51+02:00"], ["7de8f04af70", "fix: fix animations for going level deeper or back in nested-grid mobile XT-74848 XT-68881 (#20381)", "2024-08-20T11:47:05+02:00"], ["e3bf755ad53", "feat(manufacturing): xt-77027-maintenance (#20451)", "2024-08-20T12:28:50+03:00"], ["0122ffc6e11", "feat(xtrem-finance): XT-74187 DATEV export: account, customer and supplier extraction (#20438)", "2024-08-20T11:27:58+02:00"], ["55f93543e95", "fix: restore esbuild-loader dependency version (#20439)", "2024-08-20T11:02:24+02:00"], ["1783f148b4b", "fix(xtrem-stock): XT-76784 Dimensions are not recorded for attributes Project Employee and task in Stock receipts (#20441)", "2024-08-20T09:59:50+02:00"], ["184fa62990c", "feat: XT-77096 review condition action (#20463)", "2024-08-20T09:29:57+02:00"], ["3b543e1e83d", "chore: missing await in ui tests (#20467)", "2024-08-20T09:19:06+02:00"], ["b2f628d46f2", "feat: refactored track events XT-75170 (#20456)", "2024-08-20T09:16:50+02:00"], ["f1d41e6b1bb", "docs(dashboard): adds documentation for table widget's date filter XT-67129 (#20445)", "2024-08-20T09:14:28+02:00"], ["4e6567b6982", "feat(xtrem-master-data): XT-75121-customer-supplier-lookup-category (#20273)", "2024-08-20T09:13:36+02:00"], ["90134beda74", "fix: pnpm-lock file (#20472)", "2024-08-20T08:57:20+02:00"], ["c250fb27545", "fix(deps): update dependency tslib to v2.6.3 (#20433)", "2024-08-20T02:06:42+02:00"], ["ed7e7b74221", "fix(deps): update dependency xml-formatter to v3.6.3 (#20436)", "2024-08-20T02:06:38+02:00"], ["62b4f5e398e", "fix(deps): update dependency workerpool to v9.1.3 (#20435)", "2024-08-20T01:34:33+02:00"], ["390ec88f567", "chore(deps): update dependency @chromatic-com/storybook to v1.6.1 (#20465)", "2024-08-20T01:34:25+02:00"], ["0b84b1ab1dc", "fix(deps): update dependency logform to v2.6.1 (#20427)", "2024-08-20T01:03:54+02:00"], ["acdb591d1fc", "fix(deps): update dependency minimatch to v9.0.5 (#20429)", "2024-08-20T01:03:50+02:00"], ["64aab56da61", "fix(deps): update dependency mssql to v10.0.4 (#20430)", "2024-08-20T01:03:45+02:00"], ["42dd1f41d29", "fix(deps): update dependency prom-client to v15.1.3 (#20432)", "2024-08-20T01:03:36+02:00"], ["909c224b174", "fix(deps): update dependency dompurify to v3.1.6 (#20425)", "2024-08-20T00:35:03+02:00"], ["e8ce2b13954", "chore(deps): update dependency mini-css-extract-plugin to v2.9.1 (#20462)", "2024-08-20T00:34:24+02:00"], ["7912e99a377", "fix(deps): update dependency looks-same to v9.0.1 (#20428)", "2024-08-19T23:05:14+02:00"], ["158034de6c3", "fix(deps): update elliptic to v6.5.7 XT-77059 [security] (#20442)", "2024-08-19T20:31:32+02:00"], ["446fd0e4094", "feat: XT-77075 review calculate action (#20459)", "2024-08-19T19:27:33+02:00"], ["e39ec9aa671", "feat: XAPPSF-519 controls on property mapping (#20450)", "2024-08-19T18:30:54+02:00"], ["b3f0c771b2f", "fix: XT-77040 fixed the optim (#20454)", "2024-08-19T16:03:02+02:00"], ["6c6d06ad53b", "feat(sales): XT-71087 pro forma invoice form template (#20444)", "2024-08-19T15:10:56+02:00"], ["e3b7160c0cc", "fix(master-data): XT-74724 Location mass creation error when the same storage zone is created (#20264)", "2024-08-19T14:53:08+02:00"], ["c1a09d5ef85", "chore(deps): revert update dependency esbuild-loader to v4", "2024-08-19T14:47:45+02:00"], ["98625fea33e", "feat(manufacturing): XT-74020 allow phantom item for excluded WO component (#20243)", "2024-08-19T14:24:46+02:00"], ["6c6b30d36e3", "feat(automationcrew): XT-77027-maintenance (#20443)", "2024-08-19T14:55:18+03:00"], ["08683a83fcf", "perf: XT-77040 improve registration into rawStringCache (#20440)", "2024-08-19T12:21:12+02:00"], ["ec83f12e092", "feat(xtrem-services): XT-76764 stock transfer order nodes (#20361)", "2024-08-19T11:00:35+02:00"], ["ca0f90fc738", "feat(xtrem-master-data): XT-68103-stock-receipt-header (#20347)", "2024-08-19T10:37:36+02:00"], ["ad4622361c4", "fix(purchasing): XT-70070 delete line not saved (#20417)", "2024-08-19T09:55:56+02:00"], ["5da3d42970b", "feat: XT-76691 Unit test for workflow entity-created event (#20437)", "2024-08-19T09:49:00+02:00"], ["f5660d8a7f4", "chore: updated docker pnpm-lock.yaml files", "2024-08-18T21:19:59+00:00"], ["cf6a858a427", "chore: commit upgrades", "2024-08-18T21:01:49+00:00"], ["5adfa501665", "chore: bump patch version", "2024-08-18T20:04:17+00:00"], ["f9737d845a3", "feat(automationcrew): XT-76976-maintenance (#20419)", "2024-08-18T18:47:32+03:00"], ["4fa35827def", "chore: updated docker pnpm-lock.yaml files", "2024-08-17T21:28:01+00:00"], ["06ebc01c9b7", "chore: commit upgrades", "2024-08-17T21:08:42+00:00"], ["115cd384f1b", "chore: bump patch version", "2024-08-17T20:04:25+00:00"], ["9d32bf5a86e", "feat: XT-76972 first brew of workflow test framework (#20423)", "2024-08-17T18:56:31+02:00"], ["4cf1f3ad3f1", "chore: updated docker pnpm-lock.yaml files", "2024-08-16T21:34:45+00:00"], ["849c7d25020", "chore: commit upgrades", "2024-08-16T21:17:25+00:00"], ["c76d5af02ba", "chore: bump patch version", "2024-08-16T20:04:26+00:00"], ["50acf4514cd", "chore(deps): update istanbuljs monorepo (#20398)", "2024-08-16T17:32:46+02:00"], ["698c2a5ec06", "feat(shopfloor): XAPPSF-630 quantities scale. (#20422)", "2024-08-16T17:27:34+02:00"], ["c9f69d9ce02", "fix(x3-manufacturing): X3-318682-material-consumption-lpn-empty (#20420)", "2024-08-16T15:16:18+01:00"], ["d56ae1117b3", "feat(automationcrew): XT-58270-automation-debt-fifo-scenario (#20351)", "2024-08-16T16:43:40+03:00"], ["6fde87568aa", "feat: XT-73922 remote delete mutation (#20418)", "2024-08-16T15:41:04+02:00"], ["1f5d9dc473a", "chore(deps): update newrelic (#20399)", "2024-08-16T14:02:37+02:00"], ["e33cdcae088", "feat(xtrem-master-data): XT-76804-item-site-title-adjustment (#20356)", "2024-08-16T12:52:24+02:00"], ["ca916ea6fa0", "feat(finance): XT-76974 refactoring to remove redundant calls by the … (#20413)", "2024-08-16T10:47:00+00:00"], ["8fc92a40f13", "feat: XT-73922 remote update mutation (#20405)", "2024-08-16T11:06:21+02:00"], ["f028dea4b5c", "fix(deps): update dependency debug to v4.3.6 (#20412)", "2024-08-16T11:02:43+02:00"], ["56fe177e209", "feat: XT-76691 review and improve entity-created config page (#20374)", "2024-08-16T09:11:04+02:00"], ["d8aeea2b6b6", "feat(shopfloor): XAPPSF-43 completed quantity (#20404)", "2024-08-16T08:51:37+02:00"], ["088425ec724", "fix(deps): update dependency @sage/xtrem-infra to v1.0.9 (#20409)", "2024-08-16T07:32:51+02:00"], ["c4b56fc21b7", "fix(deps): update dependency csv-stringify to v6.5.1 (#20410)", "2024-08-16T07:32:43+02:00"], ["29705ccd77c", "fix(deps): update dependency dayjs to v1.11.12 (#20411)", "2024-08-16T07:02:44+02:00"], ["1b3b803e0c7", "fix(deps): update dependency @sage/intacct-controlid-provider to v2.1.2 (#20407)", "2024-08-16T01:32:21+02:00"], ["e430649e1fb", "chore: fix renovate config", "2024-08-15T23:31:36+02:00"], ["56e2d0d8ff2", "chore: updated docker pnpm-lock.yaml files", "2024-08-15T21:23:11+00:00"], ["5aa4d25f438", "chore: commit upgrades", "2024-08-15T21:07:35+00:00"], ["242d673e9b9", "chore: bump patch version", "2024-08-15T20:03:48+00:00"], ["6c784c843b7", "fix(x3-manufacturing): X3-317985-production-reporting-packingUnit (#20403)", "2024-08-15T15:46:22+01:00"], ["3a1b388ed6b", "feat(pipelines): XT-76576 - force test to stop when scenario fails fo… (#20389)", "2024-08-15T16:18:42+02:00"], ["937ce1c3ce9", "fix(xtrem-manufacturing): XT-76301 work order close button visibility (#20376)", "2024-08-15T14:52:28+02:00"], ["fbdbb317170", "feat(shopfloor): XAPPSF-526-clock-out-button-clockin-status-node-tracking (#20400)", "2024-08-15T10:17:59+01:00"], ["9abbb705f3f", "fix: XT-76913 Extend<This> type definition (#20391)", "2024-08-14T23:37:02+02:00"], ["0565584854c", "chore: revert turbo to v1.13.2 (#20395)", "2024-08-14T23:23:09+02:00"], ["e99ee0d0857", "chore: updated docker pnpm-lock.yaml files", "2024-08-14T21:15:45+00:00"], ["c665b1e6df1", "chore: commit upgrades", "2024-08-14T21:01:10+00:00"], ["abe755fb7f7", "chore: bump patch version", "2024-08-14T20:03:49+00:00"], ["66fec85876a", "feat: printing manager XT-75265 (#20396)", "2024-08-14T21:41:31+02:00"], ["273f65734e0", "feat: XT-73922 fix remote info cache (#20397)", "2024-08-14T21:17:48+02:00"], ["203cab66070", "chore(deps): update dependency mini-css-extract-plugin to v2.9.0 (#18548)", "2024-08-14T19:05:19+02:00"], ["407d085d9a3", "fix: too many listeners for abort event XT-76364 (#20388)", "2024-08-14T17:47:22+02:00"], ["03b3a1c0091", "feat: XT-68791 sales order line inqury based on mainlist (#20382)", "2024-08-14T16:44:45+02:00"], ["29852cdfb90", "feat(xtrem-cli-atp): XT-76576 stop test execution when scenario fails (#20364)", "2024-08-14T16:44:24+02:00"], ["0a875019ea4", "feat(authorization): add site filter on stockjournal XT-75057 (#20091)", "2024-08-14T16:00:44+02:00"], ["fb33bc0a491", "feat(automationcrew): XT-76842-maintenance-fix (#20385)", "2024-08-14T17:00:12+03:00"], ["bc6181c0ea0", "feat(xtrem-finance): XT-74097 DATEV export parameters (#20067)", "2024-08-14T15:50:19+02:00"], ["b7510d29801", "fix(xtrem-finance): XT-75523 tax details missing (#20345)", "2024-08-14T14:50:21+02:00"], ["3ffb5b97117", "feat(xtrem-tax): XT-76638 Page-extensions refactoring (#20355)", "2024-08-14T15:39:46+03:00"], ["2cdbd3746b6", "fix: custom field metadata query XT-76825 (#20377)", "2024-08-14T14:25:05+02:00"], ["6440e86af2d", "fix: use x3-ubuntu pool for x3 dev packs", "2024-08-14T12:17:45+02:00"], ["7fd706faceb", "fix: use x3-ubuntu pool for x3 dev packs", "2024-08-14T12:17:25+02:00"], ["a8604ce3b68", "feat(purchasing): createPurchaseOrder from purchase requisition XT-70141 #performance (#20360)", "2024-08-14T12:00:06+02:00"], ["f06d21552ed", "feat(xtrem-tax): XT-76588 Second batch pages refactoring (#20309)", "2024-08-14T12:49:32+03:00"], ["3dc38a49ae3", "fix: use x3-ubuntu pool for x3 dev packs (#20375)", "2024-08-14T11:47:56+02:00"], ["7d3020b0e7d", "fix: fix style for detail panel due to grey header added (#20378)", "2024-08-14T11:19:43+02:00"], ["ef18c8b786d", "fix: await user events in front-end tests (#20359)", "2024-08-14T09:33:01+02:00"], ["405c332cd55", "feat(finance): XT-76753 refactoring due to receipt screen changes (#20352)", "2024-08-14T07:18:41+00:00"], ["2bc9169c2e7", "chore(deps): update dependency esbuild-loader to v4 (#18115)", "2024-08-14T09:02:35+02:00"], ["1df434ea9d2", "fix: X3-318763 - fix shortage with detailed allocation (#20335)", "2024-08-14T08:47:50+02:00"], ["3c0d23db48c", "fix(sales): XT-76539 Sales Shipment control on quantity (#20348)", "2024-08-14T08:39:30+02:00"], ["abab798cf0a", "chore(deps): update dependency @testing-library/jest-dom to v6.4.8 (#20368)", "2024-08-14T08:02:50+02:00"], ["c0c33de4999", "chore(deps): update dependency jsdom to v24.1.1 (#20369)", "2024-08-14T07:32:37+02:00"], ["486e6de9348", "chore(deps): update dependency rimraf to v5.0.10 (#20370)", "2024-08-14T07:32:33+02:00"], ["1e762512dbb", "chore(deps): update dependency tsd to v0.31.1 (#20371)", "2024-08-14T07:02:54+02:00"], ["c76ff492e99", "chore(deps): update dependency turbo to v1.13.4 (#20372)", "2024-08-14T07:02:50+02:00"], ["319efdf8a1e", "fix(deps): update sage cirrus (#19074)", "2024-08-14T00:02:51+02:00"], ["a39b71cebc9", "chore(deps): update dependency @storybook/addon-webpack5-compiler-swc to v1.0.5 (#20363)", "2024-08-14T00:02:48+02:00"], ["f09a3caf90a", "chore: updated docker pnpm-lock.yaml files", "2024-08-13T21:52:04+00:00"], ["a319bcc8b0d", "chore: commit upgrades", "2024-08-13T21:32:58+00:00"], ["79d300b4fe1", "chore: bump patch version", "2024-08-13T20:04:05+00:00"], ["7f39f6d2982", "chore(deps): update amannn/action-semantic-pull-request action to v5.5.3 (#20362)", "2024-08-13T21:02:46+02:00"], ["149da1e3ab6", "chore(deps): update dependency sass to v1.77.8 (#19079)", "2024-08-13T20:03:16+02:00"], ["2803defd82d", "fix(deps): update aws-sdk-js-v3 monorepo to v3.629.0 (#20357)", "2024-08-13T20:03:09+02:00"], ["7960572ff0a", "chore(deps): update non-major types dependencies (#19073)", "2024-08-13T19:32:25+02:00"], ["ab2bc36721e", "feat(xtrem-cli-atp): XT-76561 allure report maximum call stack size exceeded (#20297)", "2024-08-13T19:12:06+02:00"], ["df29ea5e1b5", "fix(deps): update postgresql (#20358)", "2024-08-13T19:02:37+02:00"], ["daff11aedd7", "feat: XT-73922 manual testing fixes 3 (#20340)", "2024-08-13T18:27:53+02:00"], ["9b58bbf6fdd", "chore: fix release guard commit date", "2024-08-13T16:55:46+02:00"], ["3936d34a562", "feat(distribution): More properties on baseDocuments XT-71136 (#20346)", "2024-08-13T16:11:09+02:00"], ["f824d50b805", "feat(purchasing): #Performance get rid of flushDefered Actions XT-70141 (#20339)", "2024-08-13T16:10:39+02:00"], ["f4d709ffdd1", "feat: more tag events (#20334)", "2024-08-13T16:02:38+02:00"], ["b66d0c385eb", "fix(deps): update dependency puppeteer to v22.15.0 (#19080)", "2024-08-13T15:32:58+02:00"], ["4e6d8004ac3", "chore(deps): update dependency eslint-plugin-react to v7.35.0 (#19379)", "2024-08-13T15:32:54+02:00"], ["d15fdc8a80f", "fix(manufacturing): XT-73121 purchase credit memo status (#20285)", "2024-08-13T13:41:01+02:00"], ["3e7a628161a", "feat(finance): xt-76755-maintenance (#20349)", "2024-08-13T14:38:06+03:00"], ["f1148da3829", "feat: XT-64095 Refactoring unbilled account payable inquiry (#20317)", "2024-08-13T13:08:05+02:00"], ["e09f2ba4403", "feat(pipelines): XT-76618 - remove variable (#20343)", "2024-08-13T11:42:47+02:00"], ["b7830c6b775", "feat(purchasing): XT-76300 Fix reorder-purchase-order-panel file (#20329)", "2024-08-13T12:37:59+03:00"], ["c3982f108cc", "feat: add new record button to nested grid mobile XT-74848 (#20293)", "2024-08-13T11:21:54+02:00"], ["c47da936a9c", "feat(xtrem-intacct-finance): XT-76356 Company doApPosting and doArPosting with no default configuration active on intacct (#20289)", "2024-08-13T10:18:52+01:00"], ["f885492cef2", "feat(distribution): Base document refactoring XT-71136 (#20336)", "2024-08-13T10:49:59+02:00"], ["94b479f3286", "fix(conditional-block): ensures node is updated XT-72990 (#20322)", "2024-08-13T10:44:02+02:00"], ["324f3d5be19", "test(tests): XT-76756 - tag functional tests (#20342)", "2024-08-13T10:41:13+02:00"], ["6022d9f3d94", "chore: remove ag-grid deps from root (#20332)", "2024-08-13T09:40:50+02:00"], ["e9a050bcdc9", "feat: XT-76691 refactoring of entity created step (server side) (#20338)", "2024-08-13T09:19:05+02:00"], ["880b4097450", "fix(xtrem-manufacturing): XT-75503 work order component lines allocate stock action disabled (#20316)", "2024-08-13T07:36:47+01:00"], ["e526da3ff2a", "chore: updated docker pnpm-lock.yaml files", "2024-08-12T22:01:51+00:00"], ["419cb61d9df", "chore: commit upgrades", "2024-08-12T21:42:13+00:00"], ["63793902236", "chore: bump patch version", "2024-08-12T20:04:32+00:00"], ["c3b8d3e78b5", "fix(deps): update dependency @ag-grid-enterprise/charts to v32 [security] (#20328)", "2024-08-12T18:32:31+02:00"], ["344ebb03ca3", "feat(finance): XT-76667 refactoring due to receipt screen changes part 2 (#20333)", "2024-08-12T15:34:40+00:00"], ["45598e6b611", "feat(tc): create bpexcept node x3 295446 (#20312)", "2024-08-12T16:33:35+02:00"], ["7b33d9ede2c", "feat(finance): XT-76545-fix-failures-and-uncomment-tests (#20323)", "2024-08-12T16:35:09+03:00"], ["66001e9e500", "feat(sales): XT-67647 sales credit memo form report (#20324)", "2024-08-12T15:29:54+02:00"], ["aac0a598b5d", "feat(pipelines): XT-76618 - add jira ticket creation capability to sdmo release functional-test pipelines (#20308)", "2024-08-12T14:49:39+02:00"], ["50d5c2f002f", "feat(sales): XT-67646 sales invoice form report (#20320)", "2024-08-12T13:16:18+02:00"], ["0e9a1b4ac8c", "feat(finance): XT-76667 refactoring due to receipt screen changes (#20318)", "2024-08-12T10:58:24+00:00"], ["70640d594e8", "feat(distribution): Base Document Refactoring - (#20250)", "2024-08-12T12:53:35+02:00"], ["4fdce4eb2e2", "feat: XT-76501 refactor xtrem-workflow-test and make it reliable (#20314)", "2024-08-12T10:48:05+02:00"], ["49893982dff", "feat(sales): XT- 67642 sales shipment packing slip report (#20315)", "2024-08-12T09:46:23+02:00"], ["282ffbcb6ef", "fix(financeData): XT-76637 Dropdown-data-mapping-fix (#20313)", "2024-08-12T10:30:37+03:00"], ["8b355495feb", "chore: updated docker pnpm-lock.yaml files", "2024-08-11T21:19:44+00:00"], ["76ca5674971", "chore: commit upgrades", "2024-08-11T21:01:42+00:00"], ["bdec8451aa5", "chore: bump patch version", "2024-08-11T20:07:41+00:00"], ["51faf82085e", "feat: prefix integration test execution with numbers XT-72852 (#20299)", "2024-08-11T20:33:57+02:00"], ["8f4fd5a2d95", "chore: updated docker pnpm-lock.yaml files", "2024-08-10T21:25:51+00:00"], ["0af33db9431", "chore: commit upgrades", "2024-08-10T21:06:33+00:00"], ["047e183520b", "chore: bump patch version", "2024-08-10T20:07:45+00:00"], ["1652a187a71", "chore: updated docker pnpm-lock.yaml files", "2024-08-09T21:16:12+00:00"], ["8432394194e", "chore: commit upgrades", "2024-08-09T20:58:22+00:00"], ["c3c9858c57e", "chore: bump patch version", "2024-08-09T20:04:16+00:00"], ["4577a12094c", "feat(distribution): XT-76603-refactoring-ft (#20311)", "2024-08-09T16:36:01+03:00"], ["1ecb01cdabc", "feat(shopfloor): XAPPSF-486 tracking stop times (#20295)", "2024-08-09T14:45:48+02:00"], ["f489d507546", "feat(shopfloor): XAPPSF-468-clock-in-new-operator-node-create-mutation (#20310)", "2024-08-09T12:54:53+01:00"], ["b9a789154d4", "feat(xtrem-stock): XT-68371-stock-receipt-single-status (#20161)", "2024-08-09T13:40:39+02:00"], ["0ef2a3a1d4c", "feat(automationcrew): XT-58270-automation-debt-db (#20307)", "2024-08-09T12:43:44+03:00"], ["dfe6b3c922b", "feat(shopfloor): XAPPSF-551 Create tracking sync header and detail nodes (#20301)", "2024-08-09T10:35:22+01:00"], ["7b910788b1d", "feat(xtrem-master-data): XT-72140 Moved shared folder files from pages in master-data to client-functions (#20158)", "2024-08-09T11:27:26+03:00"], ["20494885147", "refactor: XT-76501 refactoring and review of workflow engine classes (2) (#20305)", "2024-08-08T23:19:00+02:00"], ["5b2fafe13d6", "chore: updated docker pnpm-lock.yaml files", "2024-08-08T21:13:36+00:00"], ["ac2abd8a30e", "chore: commit upgrades", "2024-08-08T20:59:03+00:00"], ["1b590080884", "chore: bump patch version", "2024-08-08T20:03:51+00:00"], ["96677f192a4", "feat(sales): XT-67641 sales order confirmation form report (#20296)", "2024-08-08T20:44:22+02:00"], ["af1fe736e23", "feat: XT-73922 manual testing fixes (#20306)", "2024-08-08T20:24:52+02:00"], ["dd6d7ea3219", "feat: XT-76340 add sync attributes to node extensions (#20304)", "2024-08-08T19:56:39+02:00"], ["999fddeb025", "feat(financeData): XT-72474 post-PR final fixes (#20105)", "2024-08-08T20:02:08+03:00"], ["ff21fd3a0f3", "fix(sales): XT-75311 sales shipment data fix (#20288)", "2024-08-08T18:29:04+02:00"], ["2349e3725e3", "feat(xtrem-supply-chain): XT-74991 remove stock transfer order node (#20300)", "2024-08-08T18:25:05+02:00"], ["95ffae0f6f7", "feat: X3-315126 remove .npmignore from generator, update test (#20303)", "2024-08-08T16:53:40+02:00"], ["75c9e19a69f", "feat(xtrem-tax): XT-76500 First batch pages refactoring (#20255)", "2024-08-08T17:38:00+03:00"], ["ce7968fd103", "feat(finance): XT-XT-76545-cleanup (#20298)", "2024-08-08T16:46:20+03:00"], ["ca7c187b688", "fix: validation error in sales order XT-75099 (#20277)", "2024-08-08T14:18:02+02:00"], ["c29bb258341", "feat(purchasing): XT-69581 purchase order form report template (#20294)", "2024-08-08T11:27:25+02:00"], ["5d93bd5f8a8", "feat: XT-73922 allow non-mutable collections in mapping (#20291)", "2024-08-08T11:04:45+02:00"], ["2dd78839a6d", "fix(xtrem-finance-data): XT-76357 Posting class definition - Validation error when setting an additional criteria (#20284)", "2024-08-08T09:47:37+01:00"], ["54e655d375c", "feat: adapt nested grid component for mobile view XT-68881 (#20185)", "2024-08-08T10:44:48+02:00"], ["12fc4f7aa2f", "feat(finance): XT-76497-maintenance (#20290)", "2024-08-08T11:37:53+03:00"], ["8f7601ef840", "chore: updated docker pnpm-lock.yaml files", "2024-08-07T21:26:24+00:00"], ["a10d4b1259d", "chore: commit upgrades", "2024-08-07T21:06:25+00:00"], ["7a8e33c9b2b", "chore: bump patch version", "2024-08-07T20:04:14+00:00"], ["7bde46f0244", "feat: XT-76501 documentation and review of workflow engine classes (#20287)", "2024-08-07T21:46:21+02:00"], ["27749b21e0c", "fix(xtrem-cli-atp): XT-76443 secure elem click rather than jsclick (#20266)", "2024-08-07T20:44:57+02:00"], ["602e77155f5", "feat: XT-73922 use lodash set (#20286)", "2024-08-07T19:44:01+02:00"], ["2689faf4bb2", "feat: XT-73922 external node create mutation showcase tests (#20279)", "2024-08-07T17:52:47+02:00"], ["c2daaafab27", "feat(xtrem-cli-atp): XT-74595 allure report attachment for static parameters (#20267)", "2024-08-07T17:42:46+02:00"], ["1cd127983e5", "feat(shopfloor): XAPPSF-469-clock-in-request-operator (#20278)", "2024-08-07T16:27:43+01:00"], ["76ebbbfd69a", "feat(wh-stock): X3-316336 - Fix reason method blocked by inventory (#20274)", "2024-08-07T16:08:56+02:00"], ["7bdf2f4dbf9", "feat(xtrem-intacct-finance): XT-73405 Supplier integration to Intacct - France (#20268)", "2024-08-07T12:41:51+01:00"], ["8f548d28062", "feat: x3 315126 refac npm<PERSON><PERSON> to package json (#20276)", "2024-08-07T12:17:23+02:00"], ["fdc92f2f198", "feat(synchronisationIntacct): Reset third party id for intacct XT-57349 (#20228)", "2024-08-07T12:14:39+02:00"], ["63b75e87dae", "feat: XT-73922 external node create mutation (#20275)", "2024-08-07T11:27:08+02:00"], ["ef1f43cbd60", "feat(sales): XT-67640 sales order quote form report (#20206)", "2024-08-07T10:38:43+02:00"], ["bf439459ea8", "feat(uxcrew): XT-73234 error message inactive status (#20219)", "2024-08-07T10:24:45+02:00"], ["68d7c10a3fd", "feat: XT-75265 Printing manager for print settings meta data (#20269)", "2024-08-07T09:37:09+02:00"], ["1d7f26767ca", "chore: updated docker pnpm-lock.yaml files", "2024-08-06T21:16:18+00:00"], ["bb57b7644ee", "chore: commit upgrades", "2024-08-06T21:00:38+00:00"], ["c6997c66822", "chore: bump patch version", "2024-08-06T20:07:43+00:00"], ["8bb523b7505", "feat(pipelines): XT- 73674 - mobile automation pipeline - create jira ticket (#20215)", "2024-08-06T20:52:16+02:00"], ["dc5f7d33c99", "refactor: XT-76464 renaming of Step + WorkflowAction and WorkflowEvent subclasses (#20270)", "2024-08-06T19:38:40+02:00"], ["1930a0084c8", "feat(finance): XT-76438 move test to prereq (#20272)", "2024-08-06T15:22:06+00:00"], ["66854d2bffb", "feat(xtrem-intacct-finance): XT-73976 AP and AR integration for the TAXSOLUTIONID and TAXENTRIES for France (#20170)", "2024-08-06T15:12:51+01:00"], ["c13883dc49f", "fix(xtrem-cli-atp): XT-75748 fixed the false positive in finding tab (#20253)", "2024-08-06T17:05:27+03:00"], ["8c3d8d4a0b5", "chore(master-data): XT-999999 remove hack code (#20232)", "2024-08-06T13:06:12+01:00"], ["7be3678e7ed", "feat: XT-76447 rename workflow pages (#20263)", "2024-08-06T14:05:00+02:00"], ["4ac24ce89c4", "feat(purchasing): Loggers review XT-71136 (#20262)", "2024-08-06T13:54:49+02:00"], ["f0ed7f64bc8", "feat: XT-73922 transform outbound node payload (#20256)", "2024-08-06T13:11:29+02:00"], ["f60161c931b", "feat(finance): XT-76165-refactoring-fix-seed (#20259)", "2024-08-06T10:53:16+00:00"], ["8df0b19bc18", "feat(purchasing): XT-763000 xtrem-purchasing linter (#20240)", "2024-08-06T13:07:06+03:00"], ["8efe6f691f6", "fix(manufacturing): XT-75601 Work order status (#20258)", "2024-08-06T11:55:06+02:00"], ["3e1cee5ace4", "feat(wh-stock): X3-316336 - Add view stock by product page (#20047)", "2024-08-06T10:34:28+02:00"], ["ef3699d1a4c", "fix(x3-stock): X3-300399 (#20180)", "2024-08-06T10:31:11+02:00"], ["a09148b115d", "feat(purchasing): delete flushDeferredAction - Receipt - XT-70142 (#18827)", "2024-08-06T10:13:42+02:00"], ["1d756ab8ebb", "feat: XT-76250 Workflow (#16287)", "2024-08-06T08:25:31+02:00"], ["8e3bfe73d99", "chore: updated docker pnpm-lock.yaml files", "2024-08-05T21:45:26+00:00"], ["788647fa448", "chore: commit upgrades", "2024-08-05T21:21:10+00:00"], ["37b4b7ec53b", "chore: bump patch version", "2024-08-05T20:04:48+00:00"], ["fe4f6bfd820", "feat(xtrem-master-data): XT-64127 sequence number item page (#19964)", "2024-08-05T15:12:42+02:00"], ["02b0eaff8de", "feat(shopfloor): XAPPSF-557 add isSynced property and extended unit test (#20254)", "2024-08-05T13:44:17+01:00"], ["fcfd77aa8df", "fix(stock): XT-69985 Stock issue line deletion of a serial managed item fails (#20041)", "2024-08-05T12:41:09+02:00"], ["ac610aa7d24", "fix: XT-75717 typing of dependsOn decorator (#20244)", "2024-08-05T12:28:00+02:00"], ["69fe4f9ede8", "feat(xtrem-master-data): XT-75784-item-stock-default-value (#20222)", "2024-08-05T11:55:41+02:00"], ["1dc0cd5c7ea", "feat(sal): complete node creation linked to Sales order (#20241)", "2024-08-05T11:35:28+02:00"], ["f709a969f13", "chore: updated docker pnpm-lock.yaml files", "2024-08-05T09:24:18+00:00"], ["26973acc0c5", "chore: commit upgrades", "2024-08-05T09:06:11+00:00"], ["51b09a67a5f", "chore: bump patch version", "2024-08-05T08:17:36+00:00"], ["458c60a2adc", "feat(finance): XT-76165-refactoring-ft (#20249)", "2024-08-05T08:12:35+00:00"], ["9a8f5a4cbbc", "fix(xtrem-cli-atp): XT-76336 - fix sageid authentication method (#20247)", "2024-08-05T10:02:35+02:00"], ["4a291b43478", "fix(xtrem-sales): XT-75569 error sending email (#20176)", "2024-08-05T08:37:22+01:00"], ["fb7d3667795", "feat(x3-stock): X3-300414 (#19626)", "2024-08-05T09:25:42+02:00"], ["1463a3e7900", "chore: updated docker pnpm-lock.yaml files", "2024-08-04T21:13:22+00:00"], ["08950e046e6", "chore: commit upgrades", "2024-08-04T20:55:25+00:00"], ["17e42f98998", "chore: bump patch version", "2024-08-04T20:07:31+00:00"], ["617af4e9ce4", "chore: updated docker pnpm-lock.yaml files", "2024-08-03T21:16:20+00:00"], ["b0a47203c2f", "chore: commit upgrades", "2024-08-03T20:57:23+00:00"], ["4b46af1fece", "chore: bump patch version", "2024-08-03T20:04:18+00:00"], ["9055c2f476f", "chore: updated docker pnpm-lock.yaml files", "2024-08-02T21:10:27+00:00"], ["4db0fd86355", "chore: commit upgrades", "2024-08-02T20:52:47+00:00"], ["576c253d0b3", "chore: bump patch version", "2024-08-02T20:04:26+00:00"], ["e73dfd139a2", "feat(technical-data): XT-75524 no routing for phantom (#20165)", "2024-08-02T19:22:06+02:00"], ["9e7dae2e266", "fix(stock): XT-75988 fix the rounding calculation of the value change for a new item site cost (#20233)", "2024-08-02T18:38:26+02:00"], ["6db7ec482e2", "feat(shopfloor): XAPPSF-533 re engineering start tracking (#20210)", "2024-08-02T18:18:10+02:00"], ["587a9b54992", "fix: joins on base inbound and outbound lines nodes (#20237)", "2024-08-02T16:26:44+02:00"], ["4b317e9aa9d", "chore: updated docker pnpm-lock.yaml files", "2024-08-02T13:46:22+00:00"], ["d306755ca87", "chore: commit upgrades", "2024-08-02T13:29:07+00:00"], ["b7e623d6e03", "chore: bump patch version", "2024-08-02T12:39:16+00:00"], ["35da0c6a6e1", "fix: XT-999999 try to fix patch-release (#20238)", "2024-08-02T14:31:25+02:00"], ["35c144a3d0d", "test(tests): XT-75762 - fix stock-flow-stock-value-change-average-cost (#20236)", "2024-08-02T14:15:57+02:00"], ["bbeb9b81fb4", "fix: XT-75631 Check missing overrides after extensions are loaded (#20225)", "2024-08-01T19:39:16+02:00"], ["7c449577518", "feat(stock): XT-74158 phantoms in mrp (#20167)", "2024-08-01T18:21:28+02:00"], ["6b6c945f4f5", "feat: XT-73922 fix remote app version filter when fetching transform (#20230)", "2024-08-01T18:00:20+02:00"], ["ea97f53730d", "feat(codespaces): replace nvm auto hook to include corepack auto enable (#20212)", "2024-08-01T17:36:43+02:00"], ["6625e7d174d", "feat: XT-70742 update pipeline for artillery tests (#20076)", "2024-08-01T17:31:18+02:00"], ["436caaa9375", "feat(reference-data): XT-74019 New crud on phantom item (#20231)", "2024-08-01T17:23:49+02:00"], ["6252e0fa677", "feat(pipelines): XT-74593 - activate allure on ST / FT pipelines (#20031)", "2024-08-01T17:00:22+02:00"], ["e24de4cd42d", "feat(pipelines): XT-74339 - Refactor notifications using Power Automate workflow (#19919)", "2024-08-01T16:59:28+02:00"], ["67294d0f15d", "feat(distribution): continue on base document refactoring (#20188)", "2024-08-01T16:28:27+02:00"], ["62f3514ae7f", "feat(xtrem-tax): XT-71854 Linter activation on xtrem-tax folder (#20028)", "2024-08-01T16:49:24+03:00"], ["aa55f5b7585", "feat(automationcrew): XT-76167 maintanance-data-update (#20227)", "2024-08-01T16:43:28+03:00"], ["543817e6c71", "feat(distribution): XT-76165-refactoring-ft (#20223)", "2024-08-01T16:25:33+03:00"], ["e18ed89f403", "fix(purchasing): XT-75006 Allocate stock for purchase return line (#20189)", "2024-08-01T15:00:40+02:00"], ["ce4177405d2", "feat(automationcrew): XT-76167 maintanance (#20226)", "2024-08-01T15:30:10+03:00"], ["fa4d3ad36f4", "fix(purchasing): XT-74665 adjust taxes (#20199)", "2024-08-01T12:22:59+02:00"], ["ede877f4132", "docs(datetime-range): adds path to datetime range documentation XT-50757 (#20221)", "2024-08-01T10:39:02+02:00"], ["695d22183f3", "feat: XT-73922 fixes from manual testing 1 (#20217)", "2024-08-01T10:27:32+02:00"], ["4b1c6447fa2", "feat(manufacturing): XT-72982 Async-methods-fixes (#19841)", "2024-08-01T10:53:15+03:00"], ["ed4e4d65084", "chore: updated docker pnpm-lock.yaml files", "2024-07-31T21:15:04+00:00"], ["f497f03f1e4", "chore: commit upgrades", "2024-07-31T20:59:16+00:00"], ["82215ed03fa", "chore: bump patch version", "2024-07-31T20:04:03+00:00"], ["48fbbbf9cd7", "fix: nested field sidebar context node XT-75738 (#20207)", "2024-07-31T20:14:27+02:00"], ["39c613ce48a", "feat(automationcrew): XT-76075 maintanance (#20213)", "2024-07-31T17:05:26+03:00"], ["2282057c090", "feat(shopfloor): XAPPSF-525 set SDMO mappings to inactive (#20209)", "2024-07-31T14:43:17+01:00"], ["5515690728b", "fix: X3-317874 - set numberOdDecimals's unit stock (#20200)", "2024-07-31T14:00:48+02:00"], ["2df0ed20a58", "fix(manufacturing): XT-75308 Time tracking new operation (#20152)", "2024-07-31T13:48:08+02:00"], ["fbf105c71b9", "feat(xtrem-sales): xt-72777 fix lint errors on xtrem sales - 1st batch (#20048)", "2024-07-31T14:02:45+03:00"], ["b9dcb64e495", "feat(finance): XT-76080 demo layer (#20203)", "2024-07-31T13:00:18+02:00"], ["2d6f087072d", "chore: updated docker pnpm-lock.yaml files", "2024-07-31T10:35:10+00:00"], ["22edd332539", "chore: commit upgrades", "2024-07-31T10:15:58+00:00"], ["6777506631e", "chore: bump patch version", "2024-07-31T08:59:15+00:00"], ["abc9170b29d", "feat: XT-74720 refactor reference and collection mapping (#20166)", "2024-07-31T10:40:28+02:00"], ["87783d1fcc3", "fix: XT-73851 Use AWS Region if no queue specific region (#20195)", "2024-07-31T10:33:51+02:00"], ["3c2e0ca3b63", "feat(automationcrew): XT-75888 maintanance-fix (#20191)", "2024-07-31T11:27:33+03:00"], ["d6c2f273c5d", "fix: parameter select value in filter table XT-72848 (#19917)", "2024-07-31T09:56:25+02:00"], ["656cdb55991", "feat: add menu item page XT-61572 (#20186)", "2024-07-31T09:15:11+02:00"], ["826de646a56", "chore: commit upgrades", "2024-07-30T20:57:50+00:00"], ["92776fabc41", "chore: bump patch version", "2024-07-30T20:04:09+00:00"], ["2b27d857b84", "fix: null value filter XT-74097 (#20192)", "2024-07-30T20:38:20+02:00"], ["8e64d6aea4e", "feat(distribution): XT-75886 refactoring ft (#20194)", "2024-07-30T18:51:24+03:00"], ["49456bef7a7", "fix(x3-services): X3-316725 (#20197)", "2024-07-30T17:38:56+02:00"], ["a0790b406aa", "feat(showcase-sales): XT-72444 update showcase apps (#20169)", "2024-07-30T16:21:49+01:00"], ["3c9a5279237", "fix: X3-317936 - using getNumberOfDecimal (#20190)", "2024-07-30T16:42:17+02:00"], ["bd12b30f7bd", "feat: display site and depositor in a subheader X3-316971 (#20032)", "2024-07-30T15:38:56+02:00"], ["aaad94ae697", "feat(xtrem-standalone): raises test coverage to over 80% XT-67977 (#20160)", "2024-07-30T15:22:06+02:00"], ["ad5762c9bea", "feat(automationcrew): XT-75886-maintenance-fix (#20183)", "2024-07-30T15:14:22+03:00"], ["d42acc771c8", "feat(xtrem-finance): XT-74098 DATEV export 03 Account output node (#20182)", "2024-07-30T13:08:52+02:00"], ["7d0a51fc292", "fix: fast-xml-parser upgrade CVE-2024-41818 XT-75992 (#20179)", "2024-07-30T13:00:17+02:00"], ["6fd961109ed", "feat(xtrem-finance-data): XT-73403 intacct tenant configuration for France (#20061)", "2024-07-30T10:48:03+01:00"], ["db5f953dd11", "fix(purchasing): XT-75006 Allocate stock for purchase return line (#20177)", "2024-07-30T11:46:44+02:00"], ["4262da49b3d", "feat(xtrem-finance): XT-74125 DATEV export 02 header output node (#20164)", "2024-07-30T11:17:03+02:00"], ["0b09e4546cc", "fix: delete cached entry if decryption fails XT-75836 (#20173)", "2024-07-30T10:30:47+02:00"], ["c2dd74f2648", "fix: my selected data option to take presedence on main list XT-75827 (#20159)", "2024-07-30T10:03:52+02:00"], ["641c752fbd6", "fix(stock): XT-75417 work order close (#20068)", "2024-07-30T09:35:22+02:00"], ["c4b395213c3", "feat(service-options): allow workInProgress service options in clusterCi XT-75109 (#20153)", "2024-07-30T09:33:57+02:00"], ["136b2cf1866", "feat(xtrem-cli-atp): XT-74594 allure report local reporting (#20060)", "2024-07-30T09:27:09+02:00"], ["facba223d6a", "chore: updated docker pnpm-lock.yaml files", "2024-07-29T21:14:20+00:00"], ["a69b8adb28d", "chore: commit upgrades", "2024-07-29T20:58:44+00:00"], ["eb4761ffd1e", "chore: bump patch version", "2024-07-29T20:04:12+00:00"], ["47c847e5943", "fix(xtrem-cli-atp): XT-74596 fixed the ScrollIntoview on Desktop and HD and Ultrawide for Expand step (#19943)", "2024-07-29T20:57:40+03:00"], ["9087f2d8aed", "fix(finance): XT-72885 unclear message on posting class (#20154)", "2024-07-29T17:40:46+02:00"], ["344121ce52b", "feat(financeData): fix message (#20155)", "2024-07-29T17:24:36+02:00"], ["d11b969cf54", "feat(distribution): New distribution package & move BaseDistributionDocuments XT-71136 (#20078)", "2024-07-29T16:19:32+02:00"], ["8f194942245", "feat: improved manufacturing inquiry pages (#20145)", "2024-07-29T13:09:56+02:00"], ["e7749cee363", "feat(xtrem-finance): XT-73401 Business rule on Intacct service option update functions (#20121)", "2024-07-29T09:27:45+01:00"], ["3f6c8538739", "feat(xtrem-technical-data): XT-75527 Fixed various lint issues on tehcnical-data module (#19524)", "2024-07-29T09:55:58+03:00"], ["3374c3a7ae5", "chore: updated docker pnpm-lock.yaml files", "2024-07-28T21:18:31+00:00"], ["381eea77ce1", "chore: commit upgrades", "2024-07-28T20:59:56+00:00"], ["4c386efef80", "chore: bump patch version", "2024-07-28T20:07:48+00:00"], ["26b9d66116e", "chore: updated docker pnpm-lock.yaml files", "2024-07-27T21:16:01+00:00"], ["9e2fb9c1a59", "chore: commit upgrades", "2024-07-27T21:00:10+00:00"], ["c6d628bddd2", "chore: bump patch version", "2024-07-27T20:07:11+00:00"], ["38b4a865255", "fix: proxy to server on internet XT-75771 (#20150)", "2024-07-27T09:41:03+02:00"], ["328d486bd3d", "feat: datetime range field XT-50757 (#19617)", "2024-07-27T08:02:39+02:00"], ["cc2263db6bc", "feat: add missing vital relations and associations XT-61571 (#20134)", "2024-07-27T07:58:05+02:00"], ["d82fc4d3358", "chore: updated docker pnpm-lock.yaml files", "2024-07-26T21:17:58+00:00"], ["1cfa38dc23e", "chore: commit upgrades", "2024-07-26T21:02:31+00:00"], ["ef46c7f6990", "chore: bump patch version", "2024-07-26T20:08:08+00:00"], ["bb64748099a", "feat(xtrem-x3-inventory): X3-318165 (#20148)", "2024-07-26T18:49:32+02:00"], ["631eeb3dec5", "feat(shopfloor): XT-75765 add / update mapping setup data (#20149)", "2024-07-26T16:59:37+01:00"], ["ea597196602", "feat(stock): XT-74159 standard cost with phantoms (#20035)", "2024-07-26T17:33:02+02:00"], ["c478fbb8117", "feat: XT-74347 refactor enum mapping with collection (#20144)", "2024-07-26T16:58:18+02:00"], ["09330152b6d", "feat: XT-73922 enhance payload to include natural keys for references (#20141)", "2024-07-26T16:56:37+02:00"], ["52d5ea556ab", "chore: update node to v20.16.0 (#20136)", "2024-07-26T16:48:23+02:00"], ["e7d872baf57", "fix: XT-75726 fix upgrade (#20146)", "2024-07-26T16:30:57+02:00"], ["b0bdd0e0e15", "feat(xtrem-x3-inventory): X3-318136 (#20135)", "2024-07-26T16:29:37+02:00"], ["aef6c829401", "fix: xtrem-config-applicatives-template.yml (#20139)", "2024-07-26T15:47:45+02:00"], ["8af1b21ecf2", "feat(sal): X3-316274 a sales quotes query (#20092)", "2024-07-26T15:40:56+02:00"], ["db0409b7bb5", "fix(x3-finance): X3-317834 Lost matching properties from JournalEntry… (#20107)", "2024-07-26T14:13:09+01:00"], ["cc989583c11", "fix(xtrem-finance-data): XT-72885 clear the selected options array after each check (#20104)", "2024-07-26T14:04:50+01:00"], ["df03d059b0d", "feat: main lists with reference tunnels for inquiry pages XT-75755 (#20130)", "2024-07-26T14:32:32+02:00"], ["5a9d43aab25", "fix: revert \"feat(reference): XT-75381 Data creation\" (#20128)", "2024-07-26T13:55:38+02:00"], ["8e103342575", "feat(xtrem-master-data): XT-75121-customer-supplier-selection-category (#20070)", "2024-07-26T13:48:09+02:00"], ["aca3f0a9a66", "fix: node details expansion logic XT-75652 (#20122)", "2024-07-26T13:08:36+02:00"], ["4d22ae9fc86", "fix: allow visio files on page drop XT-75691 (#20131)", "2024-07-26T12:59:49+02:00"], ["efa42965704", "feat: XT-73922 external query showcase tests (#20117)", "2024-07-26T12:05:23+02:00"], ["c4dcae94439", "chore: update pnpm to v9.6.0 XT-73967 (#20127)", "2024-07-26T12:03:53+02:00"], ["4bfa7932f68", "fix: X3-317542 api helper send null value to X3 if prop undefined (#20129)", "2024-07-26T11:42:13+02:00"], ["69d1f8e6669", "fix: XT-72509 (#19966)", "2024-07-26T11:08:57+02:00"], ["befb4ed96ea", "feat(shopfloor): XAPPSF-44 Tracking start times (#20087)", "2024-07-26T10:54:39+02:00"], ["9aad428c774", "feat: removed tunnel links from main list reference pages (#20103)", "2024-07-26T10:24:33+02:00"], ["6b6f09f30c3", "fix(xtrem-purchasing): XT-73994 On a partially invoiced purchase invoice, canceling after changing the invoiced quantity resets the line to the original receipt quantity. (#20115)", "2024-07-26T10:12:44+02:00"], ["81e4d128ed1", "chore: updated translation files 20240725.1 (#20116)", "2024-07-26T09:11:45+02:00"], ["67b2282c319", "fix: relax csrf prevention for api token XT-75658 (#20120)", "2024-07-26T08:46:54+02:00"], ["3a525cb2506", "fix(xtrem-purchasing): XT-74021 partial purchase invoice message error (#20111)", "2024-07-26T08:36:01+02:00"], ["8907e612b36", "fix: csv formula escaping XT-74701 (#20119)", "2024-07-25T23:25:59+02:00"], ["26fad454128", "chore: updated docker pnpm-lock.yaml files", "2024-07-25T21:16:41+00:00"], ["f22c5ebbafc", "chore: commit upgrades", "2024-07-25T20:59:13+00:00"], ["e29e9e1b8b5", "chore: bump patch version", "2024-07-25T20:04:54+00:00"], ["e8be1cf41bd", "feat: XT-73922 collection query options (#20118)", "2024-07-25T21:16:36+02:00"], ["d07817d3902", "feat: add ingredient page to xtrem-restaurant XT-61571 (#20086)", "2024-07-25T20:32:48+02:00"], ["31b1a2c5315", "feat(shopfloor): XT-75679 use node storage to disable sync buttons (#20102)", "2024-07-25T19:23:34+01:00"], ["2d800ebaf51", "feat: fetch items example, exposed helper (#20064)", "2024-07-25T20:14:36+02:00"], ["d7daeaed348", "feat(master-data): XT-74019 Data creation for phantom BoMs (#20106)", "2024-07-25T19:47:39+02:00"], ["3023c8cc6aa", "feat: XT-74660 Duplicate with vital reference in collection (#20088)", "2024-07-25T19:39:14+02:00"], ["7ab30a6e6b6", "feat(queues): original queueUrl for single dead-letter queue XT-72981 (#19886)", "2024-07-25T19:38:33+02:00"], ["75561dbc2eb", "fix: node name expansion logic fix XT-75712 (#20109)", "2024-07-25T18:58:30+02:00"], ["3ce7a4417f6", "feat(xtrem-x3-inventory): X3-318109 (#20108)", "2024-07-25T18:10:53+02:00"], ["6d1e7c6eb8d", "fix: use previously cached nodeTypes for attachments XT-75656 XT-75691 (#20096)", "2024-07-25T17:29:11+02:00"], ["3a8430a8e73", "fix: read-only checkbox error message XT-75666 (#20097)", "2024-07-25T17:22:30+02:00"], ["f1998ca5a78", "fix(xtrem-cli-atp): XT-74396 the robot is failing to click the dropdown field on the table (#20017)", "2024-07-25T17:04:41+02:00"], ["8011129db70", "fix(master-data): XT-72819 No setup data for CAN and AU countries (#19924)", "2024-07-25T16:40:44+02:00"], ["e658837f632", "fix(xtrem-stock): XT-75111 wrong text for reason (#20011)", "2024-07-25T15:41:20+02:00"], ["dff2ff776e4", "fix(xtrem-cli-atp): XT-74618 multiple spaces rm by wdio gettext (#20044)", "2024-07-25T15:35:14+02:00"], ["e2d855472f5", "feat(xtrem-manufacturing): XT-73420-add-tracking-date (#20010)", "2024-07-25T14:44:57+02:00"], ["f5df4373ae9", "feat(manufacturing): xt-75642 update sales return request created message (#20095)", "2024-07-25T15:33:16+03:00"], ["3cd3fe6d62e", "feat(manufacturing): xt-75642 update create message (#20094)", "2024-07-25T15:26:49+03:00"], ["61fdbd0c3cb", "feat(xtrem-financeData): XT-72254 Linter activation on pages for finance data (#19463)", "2024-07-25T15:25:57+03:00"], ["a2762b97a73", "feat(manufacturing): xt-75642 update delete message (#20093)", "2024-07-25T15:25:15+03:00"], ["5096024dd36", "feat(automationcrew): XT-75592-maintenance-fix (#20065)", "2024-07-25T12:48:32+03:00"], ["42b9a13d26a", "test: XT-61488-automation-tests XT-75150 (#19762)", "2024-07-25T10:28:11+02:00"], ["a47ba3b7531", "feat(xtrem-master-data): XT-68373-default-value-for-stock-movements (#19782)", "2024-07-25T09:49:23+02:00"], ["c6af2a2138d", "feat(reference): XT-75381 Data creation (#20036)", "2024-07-25T09:39:33+02:00"], ["78579b2fe7f", "fix(sales): Smoke test credit memo (#20079)", "2024-07-25T09:27:08+02:00"], ["3ee2d7e00bb", "fix: XT-75262 fix translation texts management (#20085)", "2024-07-24T23:59:26-07:00"], ["d3689be686d", "feat: XT-73922 external query (#20053)", "2024-07-25T06:23:29+02:00"], ["4924d66594d", "fix: XT-75594 do not try to load record with empty key (#20082)", "2024-07-25T05:07:41+02:00"], ["4db91d0120a", "chore: updated docker pnpm-lock.yaml files", "2024-07-24T21:12:19+00:00"], ["6684dec73fd", "chore: commit upgrades", "2024-07-24T20:57:18+00:00"], ["229464bcf98", "chore: bump patch version", "2024-07-24T20:03:59+00:00"], ["dc4d6086dc1", "feat(artillery): change request by axios XT-99999 (#19983)", "2024-07-24T21:41:11+02:00"], ["2796106bde0", "fix: added missing validation callback to pod collection XT-75630 (#20077)", "2024-07-24T20:23:48+02:00"], ["a118f687b7a", "feat(xtrem-tax): XT-73404-tax-setup (#20045)", "2024-07-24T20:12:41+02:00"], ["c51a79c982b", "feat(shopfloor): XT-75597 create mapping data (#20071)", "2024-07-24T17:12:19+01:00"], ["2ecfe235c0d", "fix: verify file type by extension if no mimetype found XT-74558 (#20073)", "2024-07-24T17:54:12+02:00"], ["46d4a66d531", "feat: 360 view widget example XT-72629 (#20029)", "2024-07-24T17:53:21+02:00"], ["b8f11fbe308", "fix(master-data): XT-74687 fix merge issue (#20066)", "2024-07-24T16:44:11+01:00"], ["ec890c9c82a", "feat(xtrem-finance-data): XT-73396 Legislation and company changes Intacct France (#20012)", "2024-07-24T15:36:20+01:00"], ["4a96479de7d", "feat(xtrem-x3-inventory): X3-318029 (#20072)", "2024-07-24T16:19:01+02:00"], ["aefe52d6fd5", "feat(automationcrew): XT-75592 -maintenance-fix (#20059)", "2024-07-24T15:44:28+03:00"], ["4cad01e8435", "fix(x3-manufacturing): X3-306529-missing-lot (#20015)", "2024-07-24T13:28:37+01:00"], ["b69f8a43a84", "feat(xtrem-x3-inventory): X3-318019 (#20062)", "2024-07-24T13:46:20+02:00"], ["c230ebfb84b", "fix(xtrem-purchasing): XT-72369 wrong bill-by-address (#20022)", "2024-07-24T12:25:47+01:00"], ["48d276e9775", "feat(wh-master-data): X3-317679 - Move wh-structure-data package and enums to wh-master-data (#19927)", "2024-07-24T11:44:48+02:00"], ["95b8fe8688f", "feat(sal): new field in sales order for b2g x3 316604 (#20030)", "2024-07-24T11:04:59+02:00"], ["d488cd8d504", "fix: XT-75437 reject defaultValue initialized with date, datetime, ... (#20051)", "2024-07-24T10:58:51+02:00"], ["1e464c09316", "fix(purchasing): XT-74835 purchase order add buttons (#19984)", "2024-07-24T10:44:38+02:00"], ["7d8365d8586", "feat(shopfloor): XT-74347 enum mapping page (#19831)", "2024-07-24T09:20:20+01:00"], ["bf72ec96289", "feat(xtrem-supply-chain): XT-74845 stock transfer order service option (#19901)", "2024-07-24T10:08:30+02:00"], ["42c61de4251", "fix(xtrem-cli-atp): XT-75483 - maintenance automation crew - hard click (#20040)", "2024-07-24T10:58:12+03:00"], ["b1a502e626c", "fix(xtrem-cli-atp): XT-73124 fix left column scrolling off the left of the table (#19678)", "2024-07-24T08:00:24+01:00"], ["9780daf0941", "fix: query for group bulk actions XT-74681 (#20052)", "2024-07-24T08:54:17+02:00"], ["956a5535603", "chore: updated docker pnpm-lock.yaml files", "2024-07-23T21:04:29+00:00"], ["30a79e3818b", "chore: commit upgrades", "2024-07-23T20:50:49+00:00"], ["ca49906231c", "chore: bump patch version", "2024-07-23T20:03:59+00:00"], ["e1972915c2b", "feat(manufacturing): XT-74020 phantom in work orders (#19952)", "2024-07-23T20:00:41+02:00"], ["b09d315ed93", "fix: only activate tunnel link if the user has access to the referred object XT-75052 (#19978)", "2024-07-23T19:57:02+02:00"], ["2173558e156", "feat(manufacturing): xt-75444 update number to 963 (#20046)", "2024-07-23T19:21:57+03:00"], ["3cb763d6607", "feat(root): date.today & datetime.now callback XT-71982 (#20039)", "2024-07-23T16:43:06+02:00"], ["bf518ea96e9", "fix: XT-75218 fix invalid _values_hash in db (#20008)", "2024-07-23T16:30:51+02:00"], ["7cbf580ac21", "fix: pass in merged data types to nav panel functions XT-75208 (#20034)", "2024-07-23T15:04:34+02:00"], ["620b73fcd52", "feat(automationcrew): XT-75136-maintenance (#19995)", "2024-07-23T16:00:30+03:00"], ["4b3763339f5", "fix: XT-73922 fix operation transformer (#20033)", "2024-07-23T13:13:33+02:00"], ["fabd7143ef5", "feat(master-data): XT-74687 add site default (#19897)", "2024-07-23T12:11:14+01:00"], ["97f18e60bf5", "feat(performance): release perf validation - create item and dupl. bill of order ... XT-75050 (#19970)", "2024-07-23T16:09:27+05:30"], ["640fc7ba727", "fix: visio mime-type detection XT-74558 (#19989)", "2024-07-23T12:20:50+02:00"], ["9d9ccd7e071", "fix(sales): XT-69692 Issue on mass shipment page when filtering sales order (#19883)", "2024-07-23T11:52:37+02:00"], ["be7e5b894e1", "fix(purchasing): XT-74702 Missing Return-to address in Purchase returns (#19947)", "2024-07-23T11:51:18+02:00"], ["1c34a52f769", "feat: create bare inter-site-transfer sub-nodes - XT-72106 (#19932)", "2024-07-23T11:25:14+02:00"], ["7d81022d41c", "fix(xtrem-manufacturing): XT-73533 unable to exclude text components from work orders (#19913)", "2024-07-23T10:01:21+01:00"], ["de0f3548d46", "fix(purchasing): XT-74212 Create receipt from PO (#19933)", "2024-07-23T10:02:12+02:00"], ["e4558c0f05b", "fix: expose fetch items callback of node browser tree (#20016)", "2024-07-23T09:10:40+02:00"], ["2af85fca57e", "fix: XT-69262 remove _constructor in sub node order by (#19990)", "2024-07-22T23:01:14-07:00"], ["6564938377e", "chore: updated docker pnpm-lock.yaml files", "2024-07-22T21:08:25+00:00"], ["c107ea59961", "chore: commit upgrades", "2024-07-22T20:51:32+00:00"], ["01d5c2ba8f4", "chore: bump patch version", "2024-07-22T20:04:14+00:00"], ["1b3657019d4", "fix(xtrem-finance-data): Xt-75187 Refactoring expense revenue account determination by tax (#19987)", "2024-07-22T19:18:26+01:00"], ["dc091cf1be1", "chore(guard): fix typo XT-74863", "2024-07-22T19:39:07+02:00"], ["133decee84d", "chore(guard): fix no check issue XT-74863", "2024-07-22T19:16:41+02:00"], ["b2c5ecc1295", "chore: improve merging prevention XT-74863 (#20018)", "2024-07-22T19:02:34+02:00"], ["42895d22833", "feat(automationcrew): XT-75344-maintenance-fix (#20013)", "2024-07-22T17:45:14+03:00"], ["eef4e3ce6d7", "chore(guard): temporarily disable merging prevention XT-74863 (#20014)", "2024-07-22T16:16:15+02:00"], ["8dabf2efebb", "feat(xtrem-master-data): XT-72846 refactor remaining pages (#19595)", "2024-07-22T17:05:52+03:00"], ["42962b049c7", "feat: 360 view switch XT-72629 (#19869)", "2024-07-22T15:42:52+02:00"], ["8e9329f211c", "feat(finance): xt-75269-maintenance (#19994)", "2024-07-22T16:16:07+03:00"], ["a8b3539d654", "feat(xtrem-finance): XT-73659 Adapt controls on customer, supplier and account to settings from DATEV configuration (#19916)", "2024-07-22T15:05:48+02:00"], ["1d0879f3200", "feat(automationcrew): XT-68769-maintenance-fix (#19944)", "2024-07-22T13:42:51+03:00"], ["24557b790b0", "feat: XT-74703 fix 'upgrade-from-s3-backup' pipeline (#19996)", "2024-07-22T12:31:11+02:00"], ["a87f640dda6", "feat(shopfloor): XAPPSF-465-enhance-operator-node (#19888)", "2024-07-22T10:57:35+01:00"], ["20e77739211", "feat(wh-system): X3-317732 - add new parametersUserValues mutations (#19975)", "2024-07-22T11:45:39+02:00"], ["59d7c0154f0", "feat(manufacturing): DataType on WorkOrderReleasedItem (#19971)", "2024-07-22T11:38:50+02:00"], ["d6bcffa34be", "fix(stock): XT-75207 Fix item record not found (#19977)", "2024-07-22T11:29:04+02:00"], ["306d2087d84", "feat(xtrem-finance): XT-73866 DATEV master data tax (#19930)", "2024-07-22T10:53:49+02:00"], ["5431b7c4f5d", "fix: dev image to not fail on elevate policy XT-75314 (#19999)", "2024-07-22T10:20:28+02:00"], ["07f7e009ca8", "perf: XT-75239 improve performance of nodeDetails metadata queries (#19985)", "2024-07-22T10:09:26+02:00"], ["3a539a0111a", "chore: updated docker pnpm-lock.yaml files", "2024-07-21T21:12:31+00:00"], ["32f55f438fd", "chore: commit upgrades", "2024-07-21T20:55:11+00:00"], ["01a1724cad9", "chore: bump patch version", "2024-07-21T20:04:24+00:00"], ["b61e89e7971", "chore: updated docker pnpm-lock.yaml files", "2024-07-20T21:12:15+00:00"], ["c3f52ebab72", "chore: commit upgrades", "2024-07-20T20:55:33+00:00"], ["5c637e2dad0", "chore: bump patch version", "2024-07-20T20:09:01+00:00"], ["643c1994ae6", "chore: updated docker pnpm-lock.yaml files", "2024-07-19T21:34:08+00:00"], ["0cdacf0e43d", "chore: commit upgrades", "2024-07-19T21:16:10+00:00"], ["918e1eef769", "chore: bump patch version", "2024-07-19T20:04:14+00:00"], ["04f8eaae205", "feat: XT-73922 interop query client - map response and implement remote mappings (#19986)", "2024-07-19T21:08:51+02:00"], ["c9832a5dc05", "chore(images): check if compliant with Sage Elevate security policy (#19967)", "2024-07-19T18:55:24+02:00"], ["adecfa181a6", "refactor: migrate from ag-grid-chart to recharts in order to solve sec issue XT-74850 (#19949)", "2024-07-19T18:28:23+02:00"], ["a5796cc6c54", "feat: send parent element id to pendo on tunnel interactions XT-75259 (#19979)", "2024-07-19T18:15:32+02:00"], ["8cc36f710ae", "fix: resolve sticker screen element in filter callback X3-317458 (#19981)", "2024-07-19T17:30:18+02:00"], ["68270583bf8", "feat(tc): GEODE - Common data operations to move to supervisor operat… (#19980)", "2024-07-19T16:26:26+02:00"], ["bdeabf6f135", "fix: autosize column calls XT-72486 (#19599)", "2024-07-19T15:43:32+02:00"], ["743b0356ba4", "feat: add sidebar to nested grids XT-74200 (#19961)", "2024-07-19T15:09:55+02:00"], ["8201b3ceed3", "feat: XT-73922 interop query client clean up (#19969)", "2024-07-19T14:49:25+02:00"], ["45490c88263", "feat: XT-74703 fix 'upgrade-from-s3-backup' pipeline (#19955)", "2024-07-19T14:45:02+02:00"], ["f92eba760e3", "feat(xtrem-x3-inventory): X3-317821 (#19974)", "2024-07-19T14:44:06+02:00"], ["ef376f3c8d8", "feat(stock): XT-72888 use built-in validation (#19519)", "2024-07-19T12:27:26+01:00"], ["875254a5083", "feat(finance): xt-74823-maintenance (#19920)", "2024-07-19T13:46:42+03:00"], ["6e8a5e6a963", "feat(master-data): XT-74160 set phantomItemOption to \"experimental\" status (#19958)", "2024-07-19T12:30:59+02:00"], ["b8faed659a9", "feat(xtrem-x3-inventory): X3-317807 (#19968)", "2024-07-19T12:20:24+02:00"], ["9a52e7debf6", "fix: X3-313197 - fix reference<PERSON><PERSON> (#19793)", "2024-07-19T11:53:47+02:00"], ["e294c2d42e1", "chore: reformatted code in xtrem-ui (#19965)", "2024-07-19T11:51:10+02:00"], ["38270aa8ac0", "fix(x3-stock): X3-316429 (#19695)", "2024-07-19T11:18:34+02:00"], ["13e7425e12b", "fix: X3-313377 - fix the problem of button (#19950)", "2024-07-19T11:16:48+02:00"], ["3a7b58ce472", "feat: XT-74747 check 'dependsOnSetupData' attribute (#19936)", "2024-07-19T11:00:10+02:00"], ["3f4c1c4f698", "feat: XT-73922 interop query client (#19862)", "2024-07-19T10:48:08+02:00"], ["a47cfb6d758", "fix: cache static information in the client (#19934)", "2024-07-19T10:37:49+02:00"], ["e4cd4570c6e", "feat(xtrem-cli-atp): XT-74592 allure report on pull request (#19839)", "2024-07-19T10:31:14+02:00"], ["ce108d9f123", "fix(x3-manufacturing): X3-316465-components-non-managed (#19908)", "2024-07-19T09:20:15+01:00"], ["97010140813", "fix: filter in range text swapped XT-74462 (#19942)", "2024-07-19T10:18:26+02:00"], ["c5712fa036c", "feat(pipelines): XT-72499 - add shopfloor ST pipelines V2 (#19620)", "2024-07-19T09:45:40+02:00"], ["35958f178cb", "feat(xtrem-x3-inventory): X3-317486 (#19923)", "2024-07-19T09:34:32+02:00"], ["f0afd41ad55", "feat(xtrem-x3-inventory): X3-317768 (#19954)", "2024-07-19T09:32:49+02:00"], ["afeb06809f0", "chore(deps): update ws to v8.17.1 (#19962)", "2024-07-19T09:32:29+02:00"], ["8ba068fefd3", "fix: remove onblur closing from tenant selector XT-73460 (#19941)", "2024-07-19T09:15:43+02:00"], ["e453ad7ed78", "feat(technical-data): XT-74019 phantom in BOMs (#19856)", "2024-07-19T08:24:41+02:00"], ["6d0757f1c26", "chore: change S3 configuration test in start script (#19871)", "2024-07-19T08:18:03+02:00"], ["9bdeb9ebb07", "fix: overflow of helper text for select XT-73512 (#19956)", "2024-07-19T07:48:38+02:00"], ["7a8da32190e", "chore: updated translation files 20240718.1 (#19960)", "2024-07-19T07:47:41+02:00"], ["eb53b3a43c6", "chore: updated docker pnpm-lock.yaml files", "2024-07-18T21:42:01+00:00"], ["ab0c7da2d41", "chore: commit upgrades", "2024-07-18T21:20:18+00:00"], ["83b47bb8adf", "chore: bump patch version", "2024-07-18T20:04:56+00:00"], ["356157c46a2", "fix: XT-75208 fix performance of the \"default reference to single value\" feature (#19877)", "2024-07-18T20:32:05+02:00"], ["8fff284692e", "chore: updated docker pnpm-lock.yaml files", "2024-07-18T17:14:06+00:00"], ["134bd96ea9d", "chore: commit upgrades", "2024-07-18T17:00:15+00:00"], ["e7e20518a45", "chore: bump patch version", "2024-07-18T16:12:59+00:00"], ["aeeb8245db9", "chore: updated docker pnpm-lock.yaml files", "2024-07-18T16:00:07+00:00"], ["d718b04d13f", "shopfloor-main: manual empty file to jump from 45.0.30 to 46.0.0", "2024-07-18T15:20:13+00:00"], ["f4deeaf03b2", "xtrem-show-case: manual empty file to jump from 45.0.30 to 46.0.0", "2024-07-18T15:20:09+00:00"], ["3565af97291", "xtrem-glossary: manual empty file to jump from 45.0.30 to 46.0.0", "2024-07-18T15:20:05+00:00"], ["03c4d04b7be", "xtrem-services-main: manual empty file to jump from 45.0.30 to 46.0.0", "2024-07-18T15:20:01+00:00"], ["f5253ae9081", "chore: bump major version", "2024-07-18T15:19:57+00:00"], ["d2b22cb2cd3", "chore(deps): braces v3.0.3, puppeteer v22.13.1 (#19951)", "2024-07-18T17:16:04+02:00"], ["a6d10bdf99a", "perf: XT-75140 throttle allocation of sql connections (master) (#19948)", "2024-07-18T13:52:11+02:00"], ["af8ec2bd534", "chore(deps): cumulative updates (#19900)", "2024-07-18T10:06:57+02:00"], ["7cb7be1a9fa", "perf: XT-75012 reimplement collection.sum with reduce (#19912) (#19931)", "2024-07-18T09:58:02+02:00"], ["a7f2c18e036", "chore: updated docker pnpm-lock.yaml files", "2024-07-17T21:11:23+00:00"], ["0611cd737d1", "chore: commit upgrades", "2024-07-17T20:57:37+00:00"], ["a2fcad8f619", "chore: bump patch version", "2024-07-17T20:10:00+00:00"], ["46e992979e7", "fix: XT-74378 restrict reference defaulting to Site and Company to fix performance issues (#19878) (#19937)", "2024-07-17T21:23:43+02:00"], ["4a499fd8138", "feat(automationcrew): XT-68769-maintenance (#19939)", "2024-07-17T21:01:47+03:00"], ["02053bd9fc7", "feat: XT-73468 Upgrade dependency (#19928)", "2024-07-17T19:08:40+02:00"], ["79040a9b7ee", "feat(xtrem-finance-data): XT-43390 Expense revenue account determination by tax (#19790)", "2024-07-17T15:35:59+01:00"], ["087d75cfb07", "fix: set infinite scroll to true if we have fixedHeight in a table XT-73392 (#19922)", "2024-07-17T12:32:52+02:00"], ["f42799b44be", "feat: disable tunnel links on main lists XT-61488 (#19756)", "2024-07-17T12:27:44+02:00"], ["6c64087c6c0", "chore: prod debug proxy server (#19819)", "2024-07-17T12:11:03+02:00"], ["697457f40b3", "feat(apps): add alive status XT-74705 (#19911)", "2024-07-17T09:45:26+02:00"], ["be25d90095f", "fix: XT-73808 (#19847)", "2024-07-17T09:00:12+02:00"], ["e1e4e463c41", "chore: updated docker pnpm-lock.yaml files", "2024-07-16T21:30:38+00:00"], ["c5491aca91c", "chore: commit upgrades", "2024-07-16T21:10:51+00:00"], ["24707b798f9", "chore: bump patch version", "2024-07-16T20:05:53+00:00"], ["aafecc4eb6c", "fix(xtrem-cli-atp): XT-74693 form designer table Last character removed (#19902)", "2024-07-16T17:54:44+02:00"], ["96a24012fde", "feat: XT-74745 browser, page and pdf timeout parameters (#19880)", "2024-07-16T08:31:45-07:00"], ["f2cf9a05e39", "feat(wh-service): X3-317191 - implement sticker site depositor (#19785)", "2024-07-16T17:17:07+02:00"], ["91ac4a0f6cf", "fix(xtrem-finance): XT-74744 Warning message on missing tax code wrongly shown on account page (#19904)", "2024-07-16T16:46:05+02:00"], ["00e5697da81", "feat: XT-74703 add 'schema --check' command (#19885)", "2024-07-16T16:28:20+02:00"], ["114406d73ea", "fix: XT-69386 Populate access controlled list after filling all grants (#18574)", "2024-07-16T15:08:01+02:00"], ["591775bbd23", "feat: XT-73022 Reset out of sync sequences (#19823)", "2024-07-16T15:07:49+02:00"], ["a47ddf82709", "feat(masterData): BaseDocument / BaseDocumentItemLine XT-74849 (#19879)", "2024-07-16T15:01:06+02:00"], ["73c066aa6a5", "feat(xtrem-x3-inventory): X3-317183 (#19903)", "2024-07-16T15:00:44+02:00"], ["55a60be0c7e", "fix(xtrem-purchasing): XT-73515 Cannot modify purchase invoice lines once status is set to variance approved (#19892)", "2024-07-16T15:00:00+02:00"], ["eb21c63f530", "feat(xtrem-finance): XT-73642 DATEV configuration (#19824)", "2024-07-16T14:47:38+02:00"], ["7fd36bf4676", "feat(wh-services): X3-317364 - Implement supervisor functions (#19800)", "2024-07-16T14:33:49+02:00"], ["9edb8e3fca0", "fix(xtrem-cli-atp): XT-73124 fixed the moveTo mouse action which was … (#19861)", "2024-07-16T15:26:02+03:00"], ["58a5cac7eb7", "fix(reporting): XT-74623 Import export templates (#19872)", "2024-07-16T14:02:50+02:00"], ["6f539449f62", "feat(finance): XT-74626-maintenance (#19868)", "2024-07-16T14:14:03+03:00"], ["0ae88b7550f", "feat: XT-68769 poc stock journal inquiry as main list (#19840)", "2024-07-16T12:51:42+02:00"], ["169a3e06ae7", "feat(inventory): XT-67738 display titled page (#19891)", "2024-07-16T12:35:58+02:00"], ["999e23703ad", "feat(queues): exit if listening on missing queue XT-74653 (#19854)", "2024-07-16T12:14:34+02:00"], ["3928b95a291", "fix(stock): XT-67738 Stock valuation page name of the page is wrongly changed to plural form (#19848)", "2024-07-16T12:09:05+02:00"], ["2f8e7511bc2", "fix(stock): XT-73267 stock valuation showing blank (#19736)", "2024-07-16T11:50:28+02:00"], ["cc092e2457b", "feat(xtrem-stock): XT-68362-reason-code (#19894)", "2024-07-16T11:17:13+02:00"], ["6a0424ab744", "fix(stock-data): XT-74361 avoid serial number deletion when already transacted (#19826)", "2024-07-16T11:00:55+02:00"], ["a4439fb6da2", "fix(xtrem-cli-atp): XT-72491 report designer - write in the table data container unbreakble container (#19798)", "2024-07-16T10:45:13+02:00"], ["0c7a23b5baf", "feat(xtrem-stock): XT-68362 stock reason node enhancements (#19814)", "2024-07-16T09:25:13+02:00"], ["3e6265f2d0e", "feat(xtrem-cli-atp): XT-72499_Multi_app _shopfloor_issue (#19882)", "2024-07-16T09:20:09+02:00"], ["87bcc410349", "chore: updated docker pnpm-lock.yaml files", "2024-07-15T21:10:28+00:00"], ["18fcf08418c", "chore: commit upgrades", "2024-07-15T20:53:50+00:00"], ["9c258bedb15", "chore: bump patch version", "2024-07-15T20:05:04+00:00"], ["3e82c288f01", "fix(finance-data): XT-74723 fix dimension read (#19884)", "2024-07-15T20:52:07+01:00"], ["9f77783cb5c", "fix(xtrem-cli-atp): xt-74563 - fix dialog title verification and distribution test (#19821)", "2024-07-15T20:12:11+03:00"], ["7262a648a09", "fix(sales): XT-74377 taxes field not populating during creation of sales order (#19828)", "2024-07-15T18:03:07+02:00"], ["94b6074be01", "chore: updated docker pnpm-lock.yaml files", "2024-07-15T14:12:48+00:00"], ["89bc4f02436", "chore: commit upgrades", "2024-07-15T13:59:14+00:00"], ["110c6246ffa", "chore: bump patch version", "2024-07-15T13:10:15+00:00"], ["db7ca5cbbc8", "feat(xtrem-landed-cost): XT-72250 fix lint on landed cost (#19447)", "2024-07-15T15:40:36+03:00"], ["1a6eae97dae", "feat(master): XT-74687 add company/site default (#19865)", "2024-07-15T13:33:57+01:00"], ["95faef87548", "chore(deps): update ajv to version 8.17.1 (#19873)", "2024-07-15T14:15:10+02:00"], ["4179027ea0b", "fix(sales): XT-73814 Bulk print for credit memo (#19846)", "2024-07-15T14:09:50+02:00"], ["1b1fb69ff32", "feat: unit tests for ui-components XT-67976 (#19752)", "2024-07-15T13:14:43+02:00"], ["1535e937471", "fix(sales): XT-73568 Line amount incl tax (#19805)", "2024-07-15T13:06:14+02:00"], ["4c578d02246", "feat(queues): app prefix separated by 2 hyphens XT-74653 (#19845)", "2024-07-15T12:46:10+02:00"], ["c54221d8ca3", "feat(master-data): XT-73133 add more titles (#19815)", "2024-07-15T10:55:39+01:00"], ["0a44f2bd8a0", "chore: updated translation files 20240712.1 (#19860)", "2024-07-15T11:29:28+02:00"], ["f37bb7b33a2", "feat(xtrem-finance): XT-72930 Linter activation for finance (#19538)", "2024-07-15T12:19:06+03:00"], ["6a006b7fb0d", "fix(supply-chain): XT-73969 Fix error message on purchase order planning (#19833)", "2024-07-15T11:17:56+02:00"], ["0e4651e5b51", "fix(purchasing): XT-73027 Supplier purchase documents (#19786)", "2024-07-15T11:17:47+02:00"], ["bef1a5ece7d", "feat(xtrem-sales): XT-70493-PO-sales-shipment-invoice (#19668)", "2024-07-15T09:37:04+02:00"], ["20429171da4", "feat(xtrem-master-data): XT-64227-customer-supplier-sequence-number (#19539)", "2024-07-15T09:26:58+02:00"], ["26ded1cf7b2", "feat(distribution): XT-71982 verify defaulted order date (#19842)", "2024-07-15T09:21:22+02:00"], ["c86fd0ac186", "feat(supply<PERSON><PERSON><PERSON>): XT-73838 Linter activation on supply chain (#19702)", "2024-07-15T10:19:26+03:00"], ["7e56744be77", "feat(manufacturing): XT-72982 Linter activation and imports fixes on manufacturing folder (#19803)", "2024-07-15T10:14:23+03:00"], ["f04b63a4af7", "chore: updated docker pnpm-lock.yaml files", "2024-07-14T21:19:30+00:00"], ["fe90d733057", "chore: commit upgrades", "2024-07-14T21:01:21+00:00"], ["a615d440384", "chore: bump patch version", "2024-07-14T20:04:14+00:00"], ["3a5e2f2e8e7", "chore: updated docker pnpm-lock.yaml files", "2024-07-13T21:14:21+00:00"], ["42bfd41c694", "chore: commit upgrades", "2024-07-13T20:57:48+00:00"], ["52078796f18", "chore: bump patch version", "2024-07-13T20:07:20+00:00"], ["62026779119", "chore: updated docker pnpm-lock.yaml files", "2024-07-12T21:16:27+00:00"], ["9f79b299e07", "chore: commit upgrades", "2024-07-12T21:01:43+00:00"], ["4537fc6610a", "chore: bump patch version", "2024-07-12T20:07:25+00:00"], ["49588d94bcc", "feat: XT-72634 extend node structure to implement mapping (#19794)", "2024-07-12T18:30:23+02:00"], ["7ae8aa73e23", "fix(pipelines): XT-74379 - skipped FT packages when executing ST command (#19838)", "2024-07-12T18:27:28+02:00"], ["c1c6af66bc1", "feat(sql): revert read-only replica call XT-74500 (#19852)", "2024-07-12T18:20:57+02:00"], ["2c63bc84205", "feat(xtrem-x3-inventory): X3-317479 (#19822)", "2024-07-12T17:04:07+02:00"], ["6c10b588968", "fix: cache issues (#19844)", "2024-07-12T15:57:49+02:00"], ["8b570b8f0cf", "feat: XT-74087 fixed lint error", "2024-07-12T14:36:56+01:00"], ["90db391a20a", "feat: XT-74087 refactor attributes and dimensions to be able to use them easily in reports, dashboards, etc (#19749)", "2024-07-12T14:34:55+01:00"], ["6055e401754", "fix: XT-73192 fix schema inconsistencies (#19829)", "2024-07-12T11:07:44+02:00"], ["ed49e3e708d", "chore: updated docker pnpm-lock.yaml files", "2024-07-11T21:11:21+00:00"], ["a8081e36909", "chore: commit upgrades", "2024-07-11T20:53:21+00:00"], ["7552c3f2820", "chore: bump patch version", "2024-07-11T20:04:37+00:00"], ["52bf88f63f5", "fix: handle to save new promise if isUncommited is true XT-74006 (#19825)", "2024-07-11T21:54:52+02:00"], ["0d358e3b829", "feat(apps): apps health check XT-72451 (#19809)", "2024-07-11T19:47:32+02:00"], ["94a938efd26", "fix(distribution): sales order currency conversion test fix (#19813)", "2024-07-11T19:22:47+02:00"], ["1b8c3dacd0f", "fix(purchasing): purchase order date XT-71982 (#19799)", "2024-07-11T19:22:27+02:00"], ["fed7a10d989", "feat(manufacturing): XT-74430 default dimensions test refactoring (#19816)", "2024-07-11T19:16:11+02:00"], ["b78e35b062c", "fix(xtrem-services-main): XT-74379 - add missing test command (#19806)", "2024-07-11T18:59:28+02:00"], ["954d680c445", "feat(finance): xt-74270-maintenance (#19820)", "2024-07-11T19:30:36+03:00"], ["a3183c7180c", "feat(shopfloor): XAPPSF-509-statuses-localized (#19827)", "2024-07-11T17:03:02+01:00"], ["7ac1ceaa265", "feat(master-data): XT-74016 phantom in items (#19784)", "2024-07-11T17:23:06+02:00"], ["3e6584a94c4", "feat(distribution): XT-73562 fixed step without a waitForClickable (#19726)", "2024-07-11T16:50:50+03:00"], ["51bb2e73db0", "feat(wh-service): X3-317193 - implement sticker destination (#19783)", "2024-07-11T15:15:21+02:00"], ["099cc72eefe", "feat(shopfloor): XAPPSF-472-Tracking-detail-page-WorkOrderOperationResource-progressStatus (#19812)", "2024-07-11T11:40:44+01:00"], ["56b3131fd50", "chore: updated docker pnpm-lock.yaml files", "2024-07-10T21:13:40+00:00"], ["5f61b6bb1db", "chore: commit upgrades", "2024-07-10T20:57:55+00:00"], ["45cf97557d0", "chore: bump patch version", "2024-07-10T20:04:10+00:00"], ["0a1021902ad", "fix: XT-999999 fix missing i18n key for factory property (#19811)", "2024-07-10T12:49:49-07:00"], ["b24c9d3e624", "feat: X3-314533 api helper send null to X3 (#19808)", "2024-07-10T20:57:39+02:00"], ["5acff9e6410", "fix: add missing error stroke to time field XT-73539 (#19802)", "2024-07-10T19:29:20+02:00"], ["6489b9a7499", "feat(xtrem-finance): XT-73661 DATEV master data account (#19777)", "2024-07-10T18:48:20+02:00"], ["be05105013f", "fix: show errors in sidebar table + fix phantom reset XT-74006 (#19761)", "2024-07-10T17:18:33+02:00"], ["0d67dde0cd9", "fix: pagination on deeply nested collections XT-74433 (#19801)", "2024-07-10T17:18:26+02:00"], ["98ba484c65e", "feat: XT-68501 unit test and doc for isVitalChildReference on node extension (#19558)", "2024-07-10T08:17:46-07:00"], ["4cf48c0d60a", "fix: conditional block with custom fields XT-74470 (#19796)", "2024-07-10T16:01:09+02:00"], ["ff3cee2cf7d", "feat(shopfloor): XAPPSF-470 workOrderOperationProgressStatus changes. (#19797)", "2024-07-10T15:24:56+02:00"], ["4a8271f04a5", "feat(wh-services): X3-316866 - API GEODE - implement some collections (#19666)", "2024-07-10T13:08:56+02:00"], ["5d4f83b0370", "feat(pipelines): - XT-72500 - add glossary ST pipeline v2 (#19619)", "2024-07-10T12:57:09+02:00"], ["1c53f00ec5d", "feat(inventory): XT-74270 stock data creation (#19789)", "2024-07-10T12:43:33+02:00"], ["42cfe50f282", "feat: extending nested fields on detail list XT-68882 (#19795)", "2024-07-10T12:40:54+02:00"], ["69d63ec2506", "feat(inventory): XT-74270 default dimensions test refactoring (#19788)", "2024-07-10T12:11:46+02:00"], ["2052cd0e756", "feat(shopfloor): XAPPSF-471-WorkOrderOperationResource-new-property-progressStatus (#19792)", "2024-07-10T10:48:25+01:00"], ["1dacab2cd66", "fix(test): XT-73167 fixed flaky test and update test data in feature file (#19773)", "2024-07-10T11:56:41+03:00"], ["a52251ad3df", "feat: XT-74087 fix natural key resolution in layer load (#19764)", "2024-07-10T07:01:50+01:00"], ["daf463ff506", "chore: updated docker pnpm-lock.yaml files", "2024-07-09T21:09:08+00:00"], ["b46f60ba7ff", "chore: commit upgrades", "2024-07-09T20:51:48+00:00"], ["776a3b23e72", "chore: bump patch version", "2024-07-09T20:04:18+00:00"], ["9fdcbef41f4", "feat: XT-72453 interop operation references in parameters and results (#19745)", "2024-07-09T20:16:19+02:00"], ["c41e3267149", "feat: X3-317240 replace unit by packingUnit (#19763)", "2024-07-09T17:30:18+02:00"], ["368355bd678", "fix(manufacturing): XT-74145 fix rounding (#19741)", "2024-07-09T15:37:05+01:00"], ["bac91b03c22", "feat(main): change eslintrc-base same as xtrem/ eslint-truth XT-74269 (#19781)", "2024-07-09T16:00:34+02:00"], ["9167e05de4f", "feat(wh-structure-data): X3-317194 - Add menu-items (#19776)", "2024-07-09T14:32:06+02:00"], ["c7cf272884b", "feat(distribution): XT-74181 default dimensions test refactoring (#19774)", "2024-07-09T14:22:01+02:00"], ["2a1ef723621", "feat(report): fix missing var in print preview XT-73340 (#19698)", "2024-07-09T13:50:01+02:00"], ["58ba65abdb5", "feat(apps): propagate accept-language XT-72449 (#19778)", "2024-07-09T13:44:51+02:00"], ["144dc1e547a", "fix(xtrem-purchasing): XT-73363 resend purchase invoice credit memo finance notification (#19743)", "2024-07-09T12:15:08+01:00"], ["45eed39e75f", "fix: X3-317104 fix dev dependencies on dev pack (#19779)", "2024-07-09T12:45:15+02:00"], ["57b357f1c72", "fix(xtrem-finance): XT-73461 fix the controls on the retry button on finance notification page master (#19771)", "2024-07-09T11:30:18+01:00"], ["22a09eb9442", "fix(apps): mutual tls app name XT-64193 (#19766)", "2024-07-09T10:09:43+02:00"], ["b7843f890d8", "feat(reference-data): XT-74181 item data update (#19772)", "2024-07-09T09:42:17+02:00"], ["cfe6f4d17a6", "feat(distribution): XT-72577 Test update to cover partial invoicing of purchase receipt (#19765)", "2024-07-09T09:24:42+02:00"], ["e26d088148c", "feat(xtrem-finance): XT-73368 DATEV master data customer and supplier (#19751)", "2024-07-09T09:20:15+02:00"], ["ac3077835b4", "feat(github): prevent merging outdated branch XT-65507 (#19450)", "2024-07-08T23:25:21+02:00"], ["9df57baeaa9", "chore: updated docker pnpm-lock.yaml files", "2024-07-08T21:13:49+00:00"], ["40f5e95749e", "chore: commit upgrades", "2024-07-08T20:55:09+00:00"], ["867476c52e7", "chore: bump patch version", "2024-07-08T20:04:33+00:00"], ["145884712b0", "feat: XT-73646 Terminate report generation when max number of pages is exceeded (#19717)", "2024-07-08T08:00:55-07:00"], ["a699a002c5f", "fix(xtrem-cli-atp): add regex XT-73518 (#19719)", "2024-07-08T16:34:34+02:00"], ["3faccf50160", "fix(xtrem-cli-atp): XT-50145 display of validation errors In cells column headers (#19344)", "2024-07-08T16:27:59+02:00"], ["e8f01c2e50a", "fix(stock): XT-71706 create FIFO tier if valuation method has changed (#19657)", "2024-07-08T16:10:04+02:00"], ["277f12d1eb5", "feat(automation): XT-73124 tests refactoring (#19757)", "2024-07-08T15:23:16+02:00"], ["4bdbc7f0cb2", "feat: user friendly file names added for visio documents XT-73632 (#19759)", "2024-07-08T15:19:07+02:00"], ["8edf60a97a4", "feat(apps): app name in log XT-72449 (#19747)", "2024-07-08T14:11:04+02:00"], ["cdd3d00b5e5", "feat(apps): rename supply-chain queue XT-72236 (#19753)", "2024-07-08T14:08:32+02:00"], ["b7b27542eb9", "fix: filter on boolean properties in wizards XT-74002 (#19754)", "2024-07-08T13:25:56+02:00"], ["9e34edf5980", "feat(uxcrew): XT-73671 sequence number add link (#19696)", "2024-07-08T13:13:37+02:00"], ["6cc56711455", "fix: XT-74090 fix upgrade from s3 pipeline (#19758)", "2024-07-08T12:52:11+02:00"], ["bc79f2f98d1", "feat(shopfloor): XAPPSF-73-machine-header-page-computed-data (#19713)", "2024-07-08T11:10:34+01:00"], ["e426fe3a403", "fix(xtrem-finance): XT-73860 journal entry type line on journal entry balance line (#19706)", "2024-07-08T10:24:33+01:00"], ["796bad36240", "chore: updated translation files 20240705.1 (#19742)", "2024-07-08T11:10:12+02:00"], ["6deff9a3e4e", "chore: updated docker pnpm-lock.yaml files", "2024-07-07T21:12:45+00:00"], ["a82ccdca39a", "chore: commit upgrades", "2024-07-07T20:54:50+00:00"], ["b733ed9c259", "chore: bump patch version", "2024-07-07T20:03:43+00:00"], ["766255cad82", "feat: XT-74087 fixed computation of _valuesHash when loading layers data (#19748)", "2024-07-07T10:30:42+02:00"], ["86254722ebf", "chore: updated docker pnpm-lock.yaml files", "2024-07-06T21:13:25+00:00"], ["f049a1ad1e3", "chore: commit upgrades", "2024-07-06T20:55:45+00:00"], ["65892c7bb61", "chore: bump patch version", "2024-07-06T20:04:02+00:00"], ["75b6a863e50", "chore: updated docker pnpm-lock.yaml files", "2024-07-05T21:19:29+00:00"], ["ba6dbdf898a", "chore: commit upgrades", "2024-07-05T21:02:52+00:00"], ["a0594b32dd6", "chore: bump patch version", "2024-07-05T20:04:07+00:00"], ["2fedae8fb84", "feat(queues): fix queue conf XT-72236 (#19740)", "2024-07-05T17:34:52+02:00"], ["6f3056041c8", "feat: X3-315903 Adding and renaming fields in product node (#19687)", "2024-07-05T16:54:20+02:00"], ["24de1e3f2c4", "feat: X3-315994 - x3services Pick ticket global allocation reviewed - fixes (#19436)", "2024-07-05T16:08:40+02:00"], ["dbd9d1e59f9", "fix(x3-manufacturing): X3-308531-ignore-not-managed-products (#19737)", "2024-07-05T14:36:52+01:00"], ["cf135aa1d29", "fix: XT-74087 upgrade issue during sync of _valuesHash (#19729)", "2024-07-05T15:20:07+02:00"], ["7f6efc7869e", "feat(x3-stock): X3-300394 (#19540)", "2024-07-05T15:03:40+02:00"], ["8a268fee2b9", "fix(xtrem-documentation): XT-72588 fixing documentation pipeline (#19730)", "2024-07-05T14:26:25+02:00"], ["9708c52c275", "fix: node docker image XT-73859 (#19734)", "2024-07-05T13:22:19+02:00"], ["eb4f6ef173a", "fix: docker images rate limit XT-73859 (#19731)", "2024-07-05T11:51:22+02:00"], ["e388e8a8995", "fix(stock-data): XT-72892 Repair stock receipt stuck with unappropriate error status (#19689)", "2024-07-05T10:48:28+02:00"], ["6e69ad1bde0", "fix(block): ensures block validation works for extension pages XT-72965 (#19712)", "2024-07-05T10:28:56+02:00"], ["f95366c3eb5", "feat: proxy renaming XT-72346 (#19724)", "2024-07-05T10:15:53+02:00"], ["977f9a3ccc8", "fix(master-data): XT-72652 Fix error message when creating BE from grid (#19716)", "2024-07-05T08:53:10+02:00"], ["1be2766d445", "feat(xtrem-finance): XT-73275 DATEV prerequisites master data service option and company (#19699)", "2024-07-04T23:54:58+02:00"], ["64e1fc7914a", "chore: updated docker pnpm-lock.yaml files", "2024-07-04T21:24:39+00:00"], ["e23b013713d", "chore: commit upgrades", "2024-07-04T21:04:50+00:00"], ["a0b66881f6c", "chore: bump patch version", "2024-07-04T20:04:18+00:00"], ["30bc1e755c0", "chore(deps): dependabot package update (#19725)", "2024-07-04T19:52:29+02:00"], ["3f4640ffb1a", "fix(xtrem-finance): XT-73313 Customer and Supplier ID character limit exceeds allowable character limit in Intacct (#19659)", "2024-07-04T17:23:40+02:00"], ["cb122e7937f", "fix(xtrem-manufacturing): XT-73533 unable to exclude text components from work orders (#19694)", "2024-07-04T14:41:51+01:00"], ["573357ad897", "feat(xtrem-master-data): XT-72141 fix lint issues on pages (#19449)", "2024-07-04T15:13:31+03:00"], ["6871c73b2e6", "feat(queues): sort routing to avoid diff XT-72346 (#19718)", "2024-07-04T13:29:38+02:00"], ["e1d58cf5caf", "fix(purchasing): XT-73121 cucumber test update (#19688)", "2024-07-04T11:04:20+02:00"], ["8fd5a632702", "fix(xtrem-core): XT-70843 impossible to import report templates (#19373)", "2024-07-04T10:24:42+02:00"], ["5d0e73da93b", "refactor: revert and add some recommendation from PR (#19708)", "2024-07-04T10:23:20+02:00"], ["e977c3acb8b", "fix: XT-73966 fix self reference property _id on create mutation output (#19714)", "2024-07-03T23:58:10-07:00"], ["be8ec04c82b", "feat: XT-69536 generate api.d.ts files with _factory property (#19715)", "2024-07-04T01:43:09+02:00"], ["04de6164d6a", "chore: updated docker pnpm-lock.yaml files", "2024-07-03T21:15:19+00:00"], ["06ec53b3604", "chore: commit upgrades", "2024-07-03T20:58:05+00:00"], ["4b1aa0dbb3e", "chore: bump patch version", "2024-07-03T20:08:14+00:00"], ["3565ba10f39", "docs: XT-69536 documented _factory system property (#19710)", "2024-07-03T17:05:10+02:00"], ["a0ad6809efb", "feat: XT-69536 _factory system property (#19693)", "2024-07-03T17:04:55+02:00"], ["c5300218331", "feat(queues): regen queue conf XT-72236 (#19677)", "2024-07-03T16:06:07+02:00"], ["a4469aca8b5", "fix: empty & notEmpty value type XT-73631 (#19705)", "2024-07-03T15:15:01+02:00"], ["dbb2f5448e2", "refactor: cleanup of bugs and code smells from Sonarcloud (#19662)", "2024-07-03T14:58:27+02:00"], ["58db08e4940", "fix(xtrem-core,xtrem-data-management): XT-69506 checking and re-computing _valuesHash when importing a tenant or restoring one (#19141)", "2024-07-03T14:57:16+02:00"], ["9d1556b6566", "fix(xtrem-cli-atp): XT-72804 - update parameters-atp template (#19701)", "2024-07-03T13:00:30+02:00"], ["74c7b96b535", "fix(shopfloor): XAPPSF-345 fix menu item (#19691)", "2024-07-03T10:29:30+01:00"], ["70b0a117a8c", "feat(attachments): allow visio mimetypes XT-73859 (#19692)", "2024-07-03T11:12:26+02:00"], ["b341f1d4615", "feat(shopfloor): XAPPSF-386 date filter to work order operation list (#19685)", "2024-07-03T10:35:39+02:00"], ["88e39b22963", "feat(reference-data): XT-73606 data creation (#19674)", "2024-07-03T09:35:54+02:00"], ["0d0f9511711", "fix: nested reference field value parser XT-73498 (#19670)", "2024-07-03T09:31:18+02:00"], ["863657a2f6e", "fix: tsconfig rootDir values (#19686)", "2024-07-03T09:14:28+02:00"], ["cfe604bb4dd", "chore: updated docker pnpm-lock.yaml files", "2024-07-02T21:14:35+00:00"], ["e8430525e2a", "chore: commit upgrades", "2024-07-02T20:56:49+00:00"], ["c14347723f0", "chore: bump patch version", "2024-07-02T20:04:10+00:00"], ["f72823dbba4", "feat(pipelines): XT-72507 - pipelines v2 multi app qa env (#19508)", "2024-07-02T16:49:12+02:00"], ["555c78a7cec", "feat(xtrem-finance): XT-72336 Feature DATEV prerequisites tax (#19676)", "2024-07-02T14:11:53+02:00"], ["d5f069d4eff", "feat(stock): XT-71172 tests refactoring (#19680)", "2024-07-02T13:53:52+02:00"], ["ee7edd051c3", "fix: tsconfig for xtrem-ui-components and xtrem-document-editor (#19679)", "2024-07-02T13:19:36+02:00"], ["e1b4cb51260", "fix(xtrem-finance): XT-71860 Journal entry inquiry - Page displays navigation panel (#19654)", "2024-07-02T12:44:57+02:00"], ["34fcd05d3bb", "docs: XT-73658 docs for functions in context.select selector (#19681)", "2024-07-02T12:41:08+02:00"], ["047aee68497", "feat(manufacturing): XT-72982 Linter activation on manufacturing folder (#19671)", "2024-07-02T13:05:21+03:00"], ["dfed4e0d22b", "feat: XT-73658 functions in context.select selectors (#19672)", "2024-07-02T11:33:33+02:00"], ["9bab605484b", "chore: updated docker pnpm-lock.yaml files", "2024-07-01T21:27:26+00:00"], ["cd21b45bc4f", "chore: commit upgrades", "2024-07-01T21:09:47+00:00"], ["926586962e0", "chore: bump patch version", "2024-07-01T20:04:14+00:00"], ["5e7fe213ae9", "feat: XT-72453 operation mapping improvements 1 (#19658)", "2024-07-01T17:30:33+02:00"], ["f6f1be8d0d7", "fix(reporting): XT-72973 Print report name header (#19663)", "2024-07-01T16:55:06+02:00"], ["9b2e03addc5", "fix(purchasing): XT-73121 purchase credit memo post fail (#19653)", "2024-07-01T15:26:04+02:00"], ["453ac91acbf", "feat(xtrem-finance): XT-71092 feature DATEV prerequisites counter account (#19623)", "2024-07-01T15:25:45+02:00"], ["c8bfa370897", "feat(uxcrew): XT-67022 update sequence number (#19464)", "2024-07-01T13:19:14+02:00"], ["e84c2ad9bbf", "feat(wh-services): X3-315712 - API GEODE - implement wh stock (#19614)", "2024-07-01T10:12:01+02:00"], ["e7aff96e8a0", "fix: startsWith, endsWith and contains condition implementation XT-73559 (#19651)", "2024-07-01T08:46:14+02:00"], ["f67810b18f7", "chore: updated docker pnpm-lock.yaml files", "2024-06-30T21:16:17+00:00"], ["c31f7e73bd9", "chore: commit upgrades", "2024-06-30T20:58:31+00:00"], ["acf071d485d", "chore: bump patch version", "2024-06-30T20:06:42+00:00"], ["96a9b14821e", "chore: updated translation files ********.1 (#19656)", "2024-06-30T08:00:12+02:00"], ["ec3605005d4", "chore: updated docker pnpm-lock.yaml files", "2024-06-29T21:16:21+00:00"], ["831be118114", "chore: commit upgrades", "2024-06-29T20:58:39+00:00"], ["450370f8871", "chore: bump patch version", "2024-06-29T20:07:11+00:00"], ["44731760ffe", "chore: updated docker pnpm-lock.yaml files", "2024-06-28T21:09:50+00:00"], ["08bc4e4dca9", "chore: commit upgrades", "2024-06-28T20:55:13+00:00"], ["bac4706b6a7", "chore: bump patch version", "2024-06-28T20:03:47+00:00"], ["13172062dfc", "feat(distribution): XT-73529 automation maintenance (#19655)", "2024-06-28T19:29:49+02:00"], ["0cd8eeacd31", "fix(xtrem-cli-atp): selector change XT-73138 (#19631)", "2024-06-28T16:48:09+02:00"], ["04936cbe7fb", "chore: do not remove tsbuildinfo on build (#19650)", "2024-06-28T16:28:34+02:00"], ["d549d7b77f2", "feat(postgres): add read-only instance support XT-73361 (#19644)", "2024-06-28T16:25:58+02:00"], ["2badecad06b", "feat(shopfloor): XAPPSF-437-workorder-header-page-computed-data (#19652)", "2024-06-28T15:16:02+01:00"], ["45475d31a9a", "feat: read-only reference cell tunnel link XT-61488 (#19618)", "2024-06-28T15:15:27+02:00"], ["1b6ce00836c", "fix(stock-data): XT-72589 test that a document is completed before calling the 'onceStockCompleted' function (#19527)", "2024-06-28T15:13:43+02:00"], ["a3930221f1a", "fix(manufacturing): XT-70305 update filter to remove deprecated variables (#19648)", "2024-06-28T15:08:38+02:00"], ["6ec1d374123", "feat(reference-data): XT-73529 company mandatory dimensions refactoring (#19646)", "2024-06-28T14:22:46+02:00"], ["61484110644", "feat(shopfloor): XAPPSF-431 missing cursor added. (#19647)", "2024-06-28T13:48:40+02:00"], ["0572c5010e2", "feat: XT-72453 rename node synchronization decorators (#19636)", "2024-06-28T13:06:09+02:00"], ["c48a7a24d1c", "fix(xtrem-finance): XT-73115 journal entrry lines ordered by sortValue ascending (#19570)", "2024-06-28T11:43:08+01:00"], ["1e48e240e77", "feat(xtrem-mrp-data): XT-72526 Fixed lint errors for mrp data module (#19451)", "2024-06-28T11:56:52+03:00"], ["e3f04b4c56c", "feat: add turbo setup script and pipeline (#19635)", "2024-06-28T10:54:20+02:00"], ["536e7eac7c7", "fix: main list selection checkbox issue XT-73507 (#19634)", "2024-06-28T10:28:15+02:00"], ["ab7e484c0d9", "feat: XT-69511 expose isVitalParentInput flag (#19643)", "2024-06-28T01:25:05-07:00"], ["65a0f72c790", "fix(manufacturing): XT-72996 the close button on WO depends on the status of the WO (#19554)", "2024-06-28T09:56:27+02:00"], ["66b3bd91d1b", "fix: snapshots updated", "2024-06-28T08:34:48+02:00"], ["fb755db534f", "chore: updated docker pnpm-lock.yaml files", "2024-06-27T21:07:00+00:00"], ["52db6357703", "chore: commit upgrades", "2024-06-27T20:53:22+00:00"], ["fd46edb5c69", "chore: bump patch version", "2024-06-27T20:06:27+00:00"], ["a46c20132f0", "fix: condition editor date type serialization XT-72984 (#19632)", "2024-06-27T19:10:28+02:00"], ["a98b6edd863", "fix(manufer): X3-316747 fix-scripts (#19633)", "2024-06-27T17:05:23+01:00"], ["ba176cbbadf", "feat: X3-315304 - Adding node shipmentDocument (#19630)", "2024-06-27T16:44:41+02:00"], ["108748d88d5", "fix(pipelines): XT-72804 - change URL (#19627)", "2024-06-27T16:32:25+02:00"], ["da149aa562f", "docs: XT-73062 collection.takeOne (#19628)", "2024-06-27T16:31:34+02:00"], ["3ae31134d93", "feat: XT-70175 refactor focus on cell editors (#19256)", "2024-06-27T15:37:49+02:00"], ["9645949eaa1", "fix(xtrem-finance): XT-72620 unbilled AP/AR inquiry, lines wrongly shown although invoiced (#19585)", "2024-06-27T15:36:01+02:00"], ["a0e86ec1ff2", "fix(xtrem-finance): XT-69375 Tax detail lines table displaying (#19615)", "2024-06-27T14:26:21+01:00"], ["18f1fef5d5e", "feat(shopfloor): XAPPSF-71-workorder-header-page-computed-data (#19629)", "2024-06-27T13:26:59+01:00"], ["d96ad78ae6a", "feat: XT-73062 improve takeOne (#19561)", "2024-06-27T13:05:28+02:00"], ["cf365c84e89", "fix(xtrem-sales): XT-67898 wrong warning saving sales order (#19565)", "2024-06-27T11:42:24+01:00"], ["b8cb375cb23", "fix(xtrem-finance-data): XT-68690 don't show a value on project attribute of 'Set dimensions' dialog if it has the same id as the employee attribute (#19621)", "2024-06-27T12:28:35+02:00"], ["b4300f1d300", "fix(sales): XT-73441 bulk print text (#19622)", "2024-06-27T11:47:13+02:00"], ["4abb532b0c3", "fix(stock-data): XT-72892 Repair stock receipt stuck with unappropriate error status (#19576)", "2024-06-27T11:05:43+02:00"], ["f32a3a34aad", "fix(xtrem-master-data): XT-68201 item creation error (#18917)", "2024-06-27T10:04:46+01:00"], ["dded2fd8608", "feat(xtrem-manufacturing): XT-72522 planned setup (#19608)", "2024-06-27T09:59:22+02:00"], ["14a66c71a7b", "chore: updated docker pnpm-lock.yaml files", "2024-06-26T21:14:40+00:00"], ["aee67894dfe", "chore: commit upgrades", "2024-06-26T20:56:50+00:00"], ["aa53ab3b390", "chore: bump patch version", "2024-06-26T20:04:23+00:00"]]}}}