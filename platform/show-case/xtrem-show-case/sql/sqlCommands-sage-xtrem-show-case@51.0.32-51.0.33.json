{"fromVersion": "51.0.32", "toVersion": "51.0.33", "gitHead": "c987065b86cbb55437edf0e89aa6344120e0501e", "commands": [{"isSysPool": true, "sql": ["", "", "CREATE EXTENSION IF NOT EXISTS pgcrypto;", ""]}, {"isSysPool": true, "sql": ["", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.get_config(setting_name varchar)", "RETURNS varchar AS", "$$", "declare", "    setting_value varchar;", "BEGIN", "    SELECT current_setting(setting_name) into setting_value;", "    RETURN setting_value;", "EXCEPTION", "    WHEN OTHERS THEN", "    RETURN NULL;", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.drop_triggers(schema_name varchar, name_filter varchar DEFAULT '') RETURNS integer AS", "$$", "DECLARE", "    triggerRecord RECORD;", "    record_count integer;", "begin", "\trecord_count = 0;", "    FOR triggerRecord IN", "    \tSELECT trigger_name, event_object_table", "    \tFROM information_schema.triggers", "    \tWHERE trigger_schema = schema_name AND (name_filter = '' OR event_object_table = name_filter)", "\tLOOP", "\t\trecord_count = record_count + 1;", "        EXECUTE 'DROP TRIGGER ' || triggerRecord.trigger_name || ' ON ' || schema_name || '.\"' || triggerRecord.event_object_table || '\";';", "    END LOOP;", "", "    RETURN record_count;", "END;", "$$ LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.drop_notify_functions(schema_name varchar, name_filter varchar DEFAULT '') RETURNS integer AS", "$$", "DECLARE", "    triggerRecord RECORD;", "    record_count integer;", "BEGIN", "    record_count = 0;", "    FOR triggerRecord IN", "    SELECT routine_name", "    FROM information_schema.routines", "    WHERE specific_schema = schema_name and routine_name IN (name_filter || '_notify_deleted', name_filter || '_notify_created', name_filter || '_notify_updated')", "    LOOP", "        record_count = record_count + 1;", "        EXECUTE 'DROP FUNCTION ' || schema_name || '.' || triggerRecord.routine_name || ';';", "    END LOOP;", "", "    RETURN record_count;", "END;", "$$ LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.insert_table()", "RETURNS TRIGGER", "AS", "$$", "    DECLARE", "        USER_ID VARCHAR;", "    BEGIN", "        SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.transaction_user_id'), '') INTO USER_ID;", "        IF (USER_ID <> '') THEN", "            NEW._create_user :=  CAST(USER_ID AS INT8);", "            NEW._update_user :=  CAST(USER_ID AS INT8);", "        END IF;", "        IF (NEW._create_stamp IS NULL) THEN", "            NEW._create_stamp := NOW();", "        END IF;", "        IF (NEW._update_stamp IS NULL) THEN", "            NEW._update_stamp := NOW();", "        END IF;", "        IF (NEW._update_tick IS NULL) THEN", "            NEW._update_tick := 1;", "        END IF;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.insert_shared_table()", "RETURNS TRIGGER", "AS", "$$", "    BEGIN", "        IF (NEW._create_stamp IS NULL) THEN", "            NEW._create_stamp := NOW();", "        END IF;", "        IF (NEW._update_stamp IS NULL) THEN", "            NEW._update_stamp := NOW();", "        END IF;", "        IF (NEW._update_tick IS NULL) THEN", "            NEW._update_tick := 1;", "        END IF;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.update_table()", "RETURNS TRIGGER", "AS", "$$", "    DECLARE", "        USER_ID VARCHAR;", "    BEGIN", "        SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.transaction_user_id'), '') INTO USER_ID;", "        IF (USER_ID <> '') THEN", "            NEW._update_user :=  CAST(USER_ID AS INT8);", "        END IF;", "        NEW._update_stamp := NOW();", "        NEW._update_tick := OLD._update_tick + 1;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.update_shared_table()", "RETURNS TRIGGER", "AS", "$$", "    BEGIN", "        NEW._update_stamp := NOW();", "        NEW._update_tick := OLD._update_tick + 1;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.set_sync_tick()", "RETURNS TRIGGER", "AS", "$$", "    BEGIN", "        NEW._sync_tick :=  pg_current_xact_id();", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.signedInt32(a bigint)", "RETURNS bigint AS", "$$", "DECLARE", "BEGIN", "\t-- Convert to 32 bit signed (if leftmost bit is 1, it's a negative number)", "  \tIF (a > 2^31) THEN", "    \tRETURN a - (2^32)::bigint;", "  \tEND IF;", "  \tRETURN a;", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.imul(a bigint, b bigint)", "RETURNS bigint AS", "$$", "DECLARE", "    aHi bigint;", "    aLo bigint;", "    bHi bigint;", "    bLo bigint;", "    res bigint;", "BEGIN", "    aHi = %%SCHEMA_NAME%%.zeroFillShift(a, 16) & 65535;", "    aLo = a & 65535;", "    bHi = %%SCHEMA_NAME%%.zeroFillShift(b, 16) & 65535;", "    bLo = b & 65535;", "    res = ((aLo * bLo) + %%SCHEMA_NAME%%.zeroFillShift(((aHi * bLo + aLo * bHi) << 16) % (2^32)::bigint, 0)) | 0;", "    RETURN %%SCHEMA_NAME%%.signedInt32(res);", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.zeroFillShift(a bigint, b int)", "RETURNS bigint AS", "$$", "DECLARE", "  \tres bigint;", "BEGIN", "\tIF (a < 0) THEN", "\t\tres = a + 2^32;", "\tELSE", "\t\tres = a;", "\tEND IF;", "\tres = res >> b;", "\tRETURN res;", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.nanoid(\"size\" int4 DEFAULT 21)", "    RETURNS text", "    LANGUAGE plpgsql", "    STABLE", "    AS", "    $$", "    DECLARE", "        id text := '';", "        i int := 0;", "        urlAl<PERSON><PERSON> char(64) := 'ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW';", "        bytes bytea;", "        byte int;", "        pos int;", "    BEGIN", "        SELECT gen_random_bytes(size) INTO bytes;", "    WHILE i < size LOOP", "        byte := get_byte(bytes, i);", "        pos := (byte & 63) + 1; -- + 1 because substr starts at 1", "        id := id || substr(urlAlphabet, pos, 1);", "        i = i + 1;", "    END LOOP;", "    RETURN id;", "    END", "    $$", "    ;", " ", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.audit_table()", "RETURNS TRIGGER", "AS", "$$", "    DECLARE", "        -- parameters", "        p_root_table_name VARCHAR;", "        p_constructor VARCHAR;", "", "        -- audit variables", "        is_audit_enabled VARCHAR;", "        tenant_id VARCHAR;", "        rid INT8;", "        login_email VARCHAR;", "        user_id VARCHAR;", "        locale VARCHAR;", "        log_record RECORD;", "", "        -- notify variables", "        origin_id VARCHAR;", "        notify_all_disabled VARCHAR;", "        notify_tenant_disabled VARCHAR;", "        notification_id VARCHAR;", "        user_email VARCHAR;", "        constructor VARCHAR;", "        event VARCHAR;", "        topic VARCHAR;", "        envelope VARCHAR;", "        payload VARCHAR;", "    BEGIN", "        p_root_table_name := TG_ARGV[0];", "        p_constructor := TG_ARGV[1];", "", "        SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.is_audit_enabled'), 'false') INTO is_audit_enabled;", "        IF (is_audit_enabled <> 'true') THEN", "            RETURN NEW;", "        END IF;", "        SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.login_email'), '') INTO login_email;", "        SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.transaction_user_id'), '') INTO user_id;", "        SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.locale'), 'base') INTO locale;", "", "        tenant_id := COALESCE(NEW._tenant_id, OLD._tenant_id);", "        rid := COALESCE(NEW._id, OLD._id);", "", "        SELECT * FROM %%SCHEMA_NAME%%.sys_audit_log", "        WHERE root_table_name = p_root_table_name", "            AND record_id = rid", "            AND transaction_id::TEXT = pg_current_xact_id()::TEXT", "        INTO log_record;", "", "        IF log_record IS NULL THEN", "            RAISE NOTICE 'Inserting new audit log record %:%', p_root_table_name, NEW._id;", "            IF p_root_table_name = TG_TABLE_NAME THEN", "                INSERT INTO %%SCHEMA_NAME%%.sys_audit_log (root_table_name, _tenant_id, record_id, operation, login_email, timestamp, transaction_id, record_data, old_update_tick, new_update_tick, _create_user, _update_user)", "                VALUES (p_root_table_name, tenant_id, rid, TG_OP::%%SCHEMA_NAME%%.audit_operation_enum, login_email, NOW(), pg_current_xact_id(), to_json(NEW), OLD._update_tick, NEW._update_tick, user_id::INT8, user_id::INT8);", "            ELSE", "                INSERT INTO %%SCHEMA_NAME%%.sys_audit_log (root_table_name, _tenant_id, record_id, operation, login_email, timestamp, transaction_id, record_data, old_update_tick, new_update_tick)", "                VALUES (p_root_table_name, tenant_id, rid, TG_OP::%%SCHEMA_NAME%%.audit_operation_enum, login_email, NOW(), pg_current_xact_id(), to_json(NEW), NULL, NULL);", "            END IF;", "            RAISE NOTICE 'Inserted  new audit log record root_table=%, table=%, _id=%', p_root_table_name, TG_TABLE_NAME, NEW._id;", "        ELSE", "            RAISE NOTICE 'Updating audit log record %:%', p_root_table_name, NEW._id;", "            UPDATE %%SCHEMA_NAME%%.sys_audit_log", "            SET record_data = log_record.record_data || to_jsonb(NEW)", "            WHERE root_table_name = p_root_table_name", "                AND record_id = NEW._id", "                AND transaction_id = pg_current_xact_id()::TEXT;", "            RAISE NOTICE 'Updated  audit log record %:%', p_root_table_name, NEW._id;", "        END IF;", "", "        IF p_root_table_name = TG_TABLE_NAME THEN", "            SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.notification.disable.ALL'), 'false') INTO notify_all_disabled;", "            SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.notification.disable.t_' || tenant_id), 'false') INTO notify_tenant_disabled;", "", "            IF (notify_all_disabled <> 'true' and notify_tenant_disabled <> 'true') THEN", "                SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.origin_id'), '') INTO origin_id;", "                SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.user_email'), '') INTO user_email;", "                SELECT %%SCHEMA_NAME%%.nanoid() INTO notification_id;", "", "                IF p_constructor != '' THEN", "                    constructor := p_constructor;", "                ELSE", "                    constructor := COALESCE(NEW._constructor, OLD._constructor);", "                END IF;", "", "                CASE TG_OP", "                    WHEN 'INSERT' THEN event := 'created';", "                    WHEN 'UPDATE' THEN event := 'updated';", "                    WHEN 'DELETE' THEN event := 'deleted';", "                END CASE;", "", "                topic := constructor || '/' || event;", "                payload := '{ \"_id\":' || rid || ', \"_updateTick\":' || COALESCE(NEW._update_tick, OLD._update_tick) || '}';", "", "                RAISE NOTICE 'Inserted new notification %:%', topic, notification_id;", "                INSERT INTO %%SCHEMA_NAME%%.sys_notification", "                    (tenant_id, origin_id, notification_id, reply_id, reply_topic, user_email, login, locale,", "                    topic, payload, status, _source_id, _update_tick, _create_stamp, _update_stamp)", "                VALUES (tenant_id, origin_id, notification_id, '', '', user_email, login_email, locale,", "                    topic, payload, 'pending', '', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);", "", "                RAISE NOTICE 'Notifying %:%', TG_OP, event;", "                PERFORM pg_notify('notification_queued', '{\"data\":\"{\\\"topic\\\":\\\"' || event || '\\\"}\"}');", "            END IF;", "        END IF;", "", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", ""]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.uploaded_file ADD COLUMN IF NOT EXISTS _tags _INT8;", "COMMENT ON COLUMN %%SCHEMA_NAME%%.uploaded_file._tags IS '{", "  \"type\": \"referenceArray\",", "  \"isSystem\": true", "}';"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.uploaded_file AS t0 SET _tags=$1 WHERE ((t0._tags IS NULL))", "args": [[]], "actionDescription": "Auto data action for property UploadedFile._tags"}, {"isSysPool": true, "sql": ["CREATE TABLE %%SCHEMA_NAME%%.email_reply_to (_tenant_id VARCHAR(21) COLLATE \"und-x-icu\" DEFAULT '' NOT NULL, _id SERIAL8 NOT NULL, package INT8, node INT8, company INT8, site INT8, emails _VARCHAR NOT NULL, id VARCHAR(300) COLLATE \"und-x-icu\" DEFAULT '' NOT NULL, _vendor INT8, _create_user INT8 NOT NULL, _update_user INT8 NOT NULL, _create_stamp TIMESTAMPTZ(3) DEFAULT now() NOT NULL, _update_stamp TIMESTAMPTZ(3) DEFAULT now() NOT NULL, _update_tick INT8 NOT NULL, _source_id VARCHAR(128) COLLATE \"und-x-icu\" DEFAULT '' NOT NULL, _custom_data JSONB,CONSTRAINT \"email_reply_to_PK\" PRIMARY KEY(_tenant_id,_id));CREATE UNIQUE INDEX email_reply_to_ind0 ON %%SCHEMA_NAME%%.email_reply_to(_tenant_id ASC,id ASC);", "COMMENT ON TABLE %%SCHEMA_NAME%%.email_reply_to IS '{", "  \"isSharedByAllTenants\": false,", "  \"isSetupNode\": true,", "  \"naturalKey\": [", "    \"id\"", "  ]", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.email_reply_to._tenant_id IS '{", "  \"type\": \"string\",", "  \"isSystem\": true,", "  \"maxLength\": 21", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.email_reply_to._id IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true,", "  \"isAutoIncrement\": true", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.email_reply_to.package IS '{", "  \"type\": \"reference\",", "  \"isSystem\": false,", "  \"targetTableName\": \"meta_package\",", "  \"isSelfReference\": false", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.email_reply_to.node IS '{", "  \"type\": \"reference\",", "  \"isSystem\": false,", "  \"targetTableName\": \"meta_node_factory\",", "  \"isSelfReference\": false", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.email_reply_to.company IS '{", "  \"type\": \"reference\",", "  \"isSystem\": false,", "  \"targetTableName\": \"company\",", "  \"isSelfReference\": false", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.email_reply_to.site IS '{", "  \"type\": \"reference\",", "  \"isSystem\": false,", "  \"targetTableName\": \"site\",", "  \"isSelfReference\": false", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.email_reply_to.emails IS '{", "  \"type\": \"stringArray\",", "  \"isSystem\": false", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.email_reply_to.id IS '{", "  \"type\": \"string\",", "  \"isSystem\": false,", "  \"maxLength\": 300", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.email_reply_to._vendor IS '{", "  \"type\": \"reference\",", "  \"isSystem\": true,", "  \"targetTableName\": \"sys_vendor\",", "  \"isSelfReference\": false", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.email_reply_to._create_user IS '{", "  \"type\": \"reference\",", "  \"isSystem\": true,", "  \"targetTableName\": \"user\",", "  \"isSelfReference\": false", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.email_reply_to._update_user IS '{", "  \"type\": \"reference\",", "  \"isSystem\": true,", "  \"targetTableName\": \"user\",", "  \"isSelfReference\": false", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.email_reply_to._create_stamp IS '{", "  \"type\": \"datetime\",", "  \"isSystem\": true", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.email_reply_to._update_stamp IS '{", "  \"type\": \"datetime\",", "  \"isSystem\": true", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.email_reply_to._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.email_reply_to._source_id IS '{", "  \"type\": \"string\",", "  \"isSystem\": true,", "  \"maxLength\": 128", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.email_reply_to._custom_data IS '{", "  \"type\": \"json\",", "  \"isSystem\": true", "}';"]}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER insert_table", "            BEFORE INSERT ON %%SCHEMA_NAME%%.email_reply_to", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.insert_table();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;", "DO $$ BEGIN", "            CREATE TRIGGER update_table", "            BEFORE UPDATE ON %%SCHEMA_NAME%%.email_reply_to", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.update_table();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": "GRANT DELETE, INSERT, UPDATE, SELECT on %%SCHEMA_NAME%%.email_reply_to to xtrem"}, {"isSysPool": true, "sql": "GRANT USAGE, SELECT ON SEQUENCE %%SCHEMA_NAME%%.email_reply_to__id_seq TO xtrem"}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.sys_client_user_settings ADD COLUMN IF NOT EXISTS _sort_value INT8;", "UPDATE %%SCHEMA_NAME%%.sys_client_user_settings AS t0 SET _sort_value=_id  * 100 WHERE _sort_value IS NULL;", "ALTER TABLE %%SCHEMA_NAME%%.sys_client_user_settings ALTER COLUMN _sort_value SET DEFAULT (currval((pg_get_serial_sequence('%%SCHEMA_NAME%%.sys_client_user_settings'::text, '_id'::text))::regclass) * 100);", "COMMENT ON COLUMN %%SCHEMA_NAME%%.sys_client_user_settings._sort_value IS '{", "  \"type\": \"integer\",", "  \"isSystem\": false", "}';"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_client_user_settings AS t0 SET _sort_value=(currval((pg_get_serial_sequence('%%SCHEMA_NAME%%.sys_client_user_settings'::text, '_id'::text))::regclass) * 100) /*%%DB_COMMAND%%*/ WHERE ((t0._sort_value IS NULL))", "args": [], "actionDescription": "Auto data action for property SysClientUserSettings._sortValue"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sys_tag ALTER COLUMN color DROP NOT NULL;"}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.sys_tag ADD COLUMN IF NOT EXISTS _vendor INT8, ADD COLUMN IF NOT EXISTS _custom_data JSONB, ADD COLUMN IF NOT EXISTS rgb_color VARCHAR(6) COLLATE \"und-x-icu\" DEFAULT '';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.sys_tag._vendor IS '{", "  \"type\": \"reference\",", "  \"isSystem\": true,", "  \"targetTableName\": \"sys_vendor\",", "  \"isSelfReference\": false", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.sys_tag._custom_data IS '{", "  \"type\": \"json\",", "  \"isSystem\": true", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.sys_tag.rgb_color IS '{", "  \"type\": \"string\",", "  \"isSystem\": false,", "  \"maxLength\": 6", "}';"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_tag AS t0 SET _custom_data=$1 WHERE ((t0._custom_data IS NULL))", "args": [{}], "actionDescription": "Auto data action for property SysTag._customData"}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_tag AS t0 SET rgb_color=$1 WHERE (((t0.rgb_color IS NULL) OR (t0.rgb_color = $2)))", "args": ["", ""], "actionDescription": "Auto data action for property SysTag.rgbColor"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.workflow_definition DROP CONSTRAINT IF EXISTS workflow_definition_execution_user_override_fk;"}, {"isSysPool": true, "sql": ["", "                DO $$", "                    BEGIN", "                        IF NOT EXISTS (", "                        SELECT 1 FROM pg_type t", "                        LEFT JOIN pg_namespace p ON t.typnamespace=p.oid", "                        WHERE t.typname='workflow_definition_status_enum' AND p.nspname='%%SCHEMA_NAME%%'", "                        ) THEN", "                            CREATE TYPE %%SCHEMA_NAME%%.workflow_definition_status_enum AS ENUM('draft','production');", "                        END IF;", "                    END", "                $$;", "                "]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.workflow_definition ADD COLUMN IF NOT EXISTS status %%SCHEMA_NAME%%.workflow_definition_status_enum, ADD COLUMN IF NOT EXISTS draft_user INT8;", "COMMENT ON COLUMN %%SCHEMA_NAME%%.workflow_definition.status IS '{", "  \"type\": \"enum\",", "  \"isSystem\": false,", "  \"enumTypeName\": \"workflow_definition_status_enum\"", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.workflow_definition.draft_user IS '{", "  \"type\": \"reference\",", "  \"isSystem\": false,", "  \"targetTableName\": \"user\",", "  \"isSelfReference\": false", "}';"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.workflow_definition AS t0 SET status=($1)::%%SCHEMA_NAME%%.workflow_definition_status_enum WHERE ((t0.status IS NULL))", "args": ["draft"], "actionDescription": "Auto data action for property WorkflowDefinition.status"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.workflow_process ALTER COLUMN execution_user DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.workflow_process DROP CONSTRAINT IF EXISTS workflow_process_execution_user_fk;"}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.workflow_process ADD COLUMN IF NOT EXISTS triggering_user INT8;", "COMMENT ON COLUMN %%SCHEMA_NAME%%.workflow_process.triggering_user IS '{", "  \"type\": \"reference\",", "  \"isSystem\": false,", "  \"targetTableName\": \"user\",", "  \"isSelfReference\": false", "}';"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.workflow_definition SET draft_user = _create_user", "args": [], "actionDescription": "Fix null values in workflow_definition.draft_user"}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.workflow_process SET triggering_user = _create_user", "args": [], "actionDescription": "Fix null values in workflow_process.triggering_user"}, {"isSysPool": true, "sql": ["CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.sys_tag_cascade_delete_from_reference_arrays()", "            RETURNS TRIGGER", "            AS", "            $$", "            BEGIN", "                UPDATE %%SCHEMA_NAME%%.uploaded_file SET _tags = array_remove(_tags, OLD._id) WHERE _tenant_id = OLD._tenant_id;", "                RETURN OLD;", "            END;", "            $$", "            LANGUAGE plpgsql;", "DO $$ BEGIN", "            CREATE TRIGGER cascade_delete_from_reference_arrays", "            AFTER DELETE ON %%SCHEMA_NAME%%.sys_tag", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.sys_tag_cascade_delete_from_reference_arrays();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.workflow_process ADD CONSTRAINT \"workflow_process_triggering_user_fk\" FOREIGN KEY(_tenant_id,triggering_user) REFERENCES %%SCHEMA_NAME%%.user(_tenant_id,_id) ON DELETE RESTRICT DEFERRABLE DEFERRABLE;", "COMMENT ON CONSTRAINT workflow_process_triggering_user_fk ON %%SCHEMA_NAME%%.workflow_process IS '{", "  \"targetTableName\": \"user\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"triggering_user\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"restrict\",", "  \"isDeferrable\": true", "}';;"]}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.workflow_process DROP COLUMN IF EXISTS execution_user;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.workflow_process ALTER COLUMN triggering_user SET NOT NULL;"}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.workflow_definition ADD CONSTRAINT \"workflow_definition_draft_user_fk\" FOREIGN KEY(_tenant_id,draft_user) REFERENCES %%SCHEMA_NAME%%.user(_tenant_id,_id) ON DELETE RESTRICT DEFERRABLE DEFERRABLE;", "COMMENT ON CONSTRAINT workflow_definition_draft_user_fk ON %%SCHEMA_NAME%%.workflow_definition IS '{", "  \"targetTableName\": \"user\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"draft_user\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"restrict\",", "  \"isDeferrable\": true", "}';;"]}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.workflow_definition DROP COLUMN IF EXISTS execution_user_override;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.workflow_definition ALTER COLUMN status SET NOT NULL, ALTER COLUMN draft_user SET NOT NULL;"}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.sys_tag ADD CONSTRAINT \"sys_tag__vendor_fk\" FOREIGN KEY(_vendor) REFERENCES %%SCHEMA_NAME%%.sys_vendor(_id) ON DELETE NO ACTION DEFERRABLE;", "COMMENT ON CONSTRAINT sys_tag__vendor_fk ON %%SCHEMA_NAME%%.sys_tag IS '{", "  \"targetTableName\": \"sys_vendor\",", "  \"columns\": {", "    \"_vendor\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"noAction\",", "  \"isDeferrable\": true", "}';;"]}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sys_tag DROP COLUMN IF EXISTS color;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sys_tag ALTER COLUMN rgb_color SET NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sys_client_user_settings DROP CONSTRAINT IF EXISTS sys_client_user_settings_user_fk;"}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.sys_client_user_settings ADD CONSTRAINT \"sys_client_user_settings_user_fk\" FOREIGN KEY(_tenant_id,\"user\") REFERENCES %%SCHEMA_NAME%%.user(_tenant_id,_id) ON DELETE CASCADE DEFERRABLE DEFERRABLE;", "COMMENT ON CONSTRAINT sys_client_user_settings_user_fk ON %%SCHEMA_NAME%%.sys_client_user_settings IS '{", "  \"targetTableName\": \"user\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"user\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"cascade\",", "  \"isDeferrable\": true", "}';;"]}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sys_client_user_settings ALTER COLUMN _sort_value SET NOT NULL;"}, {"isSysPool": true, "sql": "CREATE  INDEX sys_client_user_settings_ind1 ON %%SCHEMA_NAME%%.sys_client_user_settings(_tenant_id ASC,\"user\" ASC,_sort_value ASC);"}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.email_reply_to ADD CONSTRAINT \"email_reply_to__vendor_fk\" FOREIGN KEY(_vendor) REFERENCES %%SCHEMA_NAME%%.sys_vendor(_id) ON DELETE NO ACTION DEFERRABLE;", "COMMENT ON CONSTRAINT email_reply_to__vendor_fk ON %%SCHEMA_NAME%%.email_reply_to IS '{", "  \"targetTableName\": \"sys_vendor\",", "  \"columns\": {", "    \"_vendor\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"noAction\",", "  \"isDeferrable\": true", "}';;"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.email_reply_to ADD CONSTRAINT \"email_reply_to__update_user_fk\" FOREIGN KEY(_tenant_id,_update_user) REFERENCES %%SCHEMA_NAME%%.user(_tenant_id,_id) ON DELETE RESTRICT DEFERRABLE DEFERRABLE;", "COMMENT ON CONSTRAINT email_reply_to__update_user_fk ON %%SCHEMA_NAME%%.email_reply_to IS '{", "  \"targetTableName\": \"user\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"_update_user\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"restrict\",", "  \"isDeferrable\": true", "}';;"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.email_reply_to ADD CONSTRAINT \"email_reply_to__create_user_fk\" FOREIGN KEY(_tenant_id,_create_user) REFERENCES %%SCHEMA_NAME%%.user(_tenant_id,_id) ON DELETE RESTRICT DEFERRABLE DEFERRABLE;", "COMMENT ON CONSTRAINT email_reply_to__create_user_fk ON %%SCHEMA_NAME%%.email_reply_to IS '{", "  \"targetTableName\": \"user\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"_create_user\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"restrict\",", "  \"isDeferrable\": true", "}';;"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.email_reply_to ADD CONSTRAINT \"email_reply_to_package_fk\" FOREIGN KEY(package) REFERENCES %%SCHEMA_NAME%%.meta_package(_id) ON DELETE NO ACTION DEFERRABLE;", "COMMENT ON CONSTRAINT email_reply_to_package_fk ON %%SCHEMA_NAME%%.email_reply_to IS '{", "  \"targetTableName\": \"meta_package\",", "  \"columns\": {", "    \"package\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"noAction\",", "  \"isDeferrable\": true", "}';;"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.email_reply_to ADD CONSTRAINT \"email_reply_to_node_fk\" FOREIGN KEY(node) REFERENCES %%SCHEMA_NAME%%.meta_node_factory(_id) ON DELETE NO ACTION DEFERRABLE;", "COMMENT ON CONSTRAINT email_reply_to_node_fk ON %%SCHEMA_NAME%%.email_reply_to IS '{", "  \"targetTableName\": \"meta_node_factory\",", "  \"columns\": {", "    \"node\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"noAction\",", "  \"isDeferrable\": true", "}';;"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.email_reply_to ADD CONSTRAINT \"email_reply_to_company_fk\" FOREIGN KEY(_tenant_id,company) REFERENCES %%SCHEMA_NAME%%.company(_tenant_id,_id) ON DELETE NO ACTION DEFERRABLE;", "COMMENT ON CONSTRAINT email_reply_to_company_fk ON %%SCHEMA_NAME%%.email_reply_to IS '{", "  \"targetTableName\": \"company\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"company\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"noAction\",", "  \"isDeferrable\": true", "}';;"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.email_reply_to ADD CONSTRAINT \"email_reply_to_site_fk\" FOREIGN KEY(_tenant_id,site) REFERENCES %%SCHEMA_NAME%%.site(_tenant_id,_id) ON DELETE NO ACTION DEFERRABLE;", "COMMENT ON CONSTRAINT email_reply_to_site_fk ON %%SCHEMA_NAME%%.email_reply_to IS '{", "  \"targetTableName\": \"site\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"site\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"noAction\",", "  \"isDeferrable\": true", "}';;"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.email_reply_to ADD CONSTRAINT \"email_reply_to__tenant_id_fk\" FOREIGN KEY(_tenant_id) REFERENCES %%SCHEMA_NAME%%.sys_tenant(tenant_id) ON DELETE RESTRICT DEFERRABLE DEFERRABLE;", "COMMENT ON CONSTRAINT email_reply_to__tenant_id_fk ON %%SCHEMA_NAME%%.email_reply_to IS '{", "  \"targetTableName\": \"sys_tenant\",", "  \"columns\": {", "    \"_tenant_id\": \"tenant_id\"", "  },", "  \"onDeleteBehaviour\": \"restrict\",", "  \"isDeferrable\": true", "}';;"]}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.uploaded_file ALTER COLUMN _tags SET NOT NULL;"}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Is a demo tenant", "released", false, "@sage/xtrem-system", false, "isDemoTenant"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["enable dev-only features", "experimental", false, "@sage/xtrem-system", false, "devTools"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["allow to display changelog in the app", "experimental", false, "@sage/xtrem-system", false, "changelog"]}, {"isSysPool": false, "sql": ["INSERT INTO %%SCHEMA_NAME%%.sys_service_option", "(_update_tick,_source_id,package,option_name,description,status,is_hidden,is_active_by_default)", "VALUES ($1,$2,$3,$4,$5,$6,$7,$8)", "RETURNING _create_stamp,_update_stamp,_id"], "args": [1, "", "@sage/xtrem-system", "sysTag", "Tags (not yet released)", "experimental", false, false]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Notification center", "experimental", false, "@sage/xtrem-communication", false, "notificationCenter"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Authorization access control", "released", false, "@sage/xtrem-authorization", false, "authorizationServiceOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Auditing option", "experimental", false, "@sage/xtrem-auditing", false, "auditing"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Auditing option", "workInProgress", true, "@sage/xtrem-auditing", false, "auditingOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Workflow option", "experimental", false, "@sage/xtrem-workflow", false, "workflow"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Workflow advanced features (not yet released)", "workInProgress", false, "@sage/xtrem-workflow", false, "workflowAdvanced"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Workflow option (obsolete)", "workInProgress", true, "@sage/xtrem-workflow", false, "workflowOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Synchronization", "released", false, "@sage/xtrem-interop", false, "synchronizationServiceOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["enable ReportAssignment feature", "workInProgress", false, "@sage/xtrem-reporting", false, "reportAssignment"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["showCase discount option", "experimental", false, "@sage/xtrem-show-case", false, "showCaseDiscountOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["experimental option", "experimental", false, "@sage/xtrem-show-case", false, "showCaseExperimentalOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["showCaseOption's hight level description", "released", false, "@sage/xtrem-show-case", false, "showCaseOptionHighLevel"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["showCaseOption1's description", "released", true, "@sage/xtrem-show-case", false, "showCaseOption1"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["showCaseOption2's description", "released", true, "@sage/xtrem-show-case", false, "showCaseOption2"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["A workInProgress service option that can be loaded on a development environment", "workInProgress", false, "@sage/xtrem-show-case", true, "showCaseOption3"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Work in progress options cannot be activated", "workInProgress", false, "@sage/xtrem-show-case", false, "showCaseWorkInProgressOption"]}, {"isSysPool": false, "sql": "NOTIFY invalidate_category_cache, '{\"data\":\"{\\\"tenantId\\\":null,\\\"category\\\":\\\"$SHARED_NODE.SysServiceOption\\\"}\",\"containerId\":\"x3-devops00HMYR-79638\",\"excludeSelf\":true}';", "args": []}, {"action": "reload_setup_data", "args": {"factory": "EmailReplyTo"}}, {"action": "reload_setup_data", "args": {"factory": "SysServiceOptionState"}}, {"isSysPool": true, "sql": ["COMMENT ON TABLE %%SCHEMA_NAME%%.uploaded_file IS '{", "  \"isSharedByAllTenants\": false,", "  \"hasTags\": true", "}';;COMMENT ON CONSTRAINT email_reply_to__vendor_fk ON %%SCHEMA_NAME%%.email_reply_to IS '{", "  \"targetTableName\": \"sys_vendor\",", "  \"columns\": {", "    \"_vendor\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"noAction\",", "  \"isDeferrable\": true", "}';;COMMENT ON CONSTRAINT email_reply_to__update_user_fk ON %%SCHEMA_NAME%%.email_reply_to IS '{", "  \"targetTableName\": \"user\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"_update_user\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"restrict\",", "  \"isDeferrable\": true", "}';;COMMENT ON CONSTRAINT email_reply_to__create_user_fk ON %%SCHEMA_NAME%%.email_reply_to IS '{", "  \"targetTableName\": \"user\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"_create_user\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"restrict\",", "  \"isDeferrable\": true", "}';;COMMENT ON CONSTRAINT email_reply_to_package_fk ON %%SCHEMA_NAME%%.email_reply_to IS '{", "  \"targetTableName\": \"meta_package\",", "  \"columns\": {", "    \"package\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"noAction\",", "  \"isDeferrable\": true", "}';;COMMENT ON CONSTRAINT email_reply_to_node_fk ON %%SCHEMA_NAME%%.email_reply_to IS '{", "  \"targetTableName\": \"meta_node_factory\",", "  \"columns\": {", "    \"node\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"noAction\",", "  \"isDeferrable\": true", "}';;COMMENT ON CONSTRAINT email_reply_to_company_fk ON %%SCHEMA_NAME%%.email_reply_to IS '{", "  \"targetTableName\": \"company\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"company\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"noAction\",", "  \"isDeferrable\": true", "}';;COMMENT ON CONSTRAINT email_reply_to_site_fk ON %%SCHEMA_NAME%%.email_reply_to IS '{", "  \"targetTableName\": \"site\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"site\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"noAction\",", "  \"isDeferrable\": true", "}';;COMMENT ON CONSTRAINT email_reply_to__tenant_id_fk ON %%SCHEMA_NAME%%.email_reply_to IS '{", "  \"targetTableName\": \"sys_tenant\",", "  \"columns\": {", "    \"_tenant_id\": \"tenant_id\"", "  },", "  \"onDeleteBehaviour\": \"restrict\",", "  \"isDeferrable\": true", "}';;COMMENT ON TABLE %%SCHEMA_NAME%%.sys_client_user_settings IS '{", "  \"isSharedByAllTenants\": false,", "  \"isSetupNode\": true,", "  \"naturalKey\": [", "    \"user\",", "    \"screenId\",", "    \"elementId\",", "    \"title\"", "  ]", "}';;COMMENT ON CONSTRAINT sys_client_user_settings_user_fk ON %%SCHEMA_NAME%%.sys_client_user_settings IS '{", "  \"targetTableName\": \"user\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"user\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"cascade\",", "  \"isDeferrable\": true", "}';;COMMENT ON TABLE %%SCHEMA_NAME%%.sys_tag IS '{", "  \"isSharedByAllTenants\": false,", "  \"isSetupNode\": true,", "  \"naturalKey\": [", "    \"name\"", "  ]", "}';;COMMENT ON CONSTRAINT sys_tag__vendor_fk ON %%SCHEMA_NAME%%.sys_tag IS '{", "  \"targetTableName\": \"sys_vendor\",", "  \"columns\": {", "    \"_vendor\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"noAction\",", "  \"isDeferrable\": true", "}';;COMMENT ON CONSTRAINT workflow_definition_draft_user_fk ON %%SCHEMA_NAME%%.workflow_definition IS '{", "  \"targetTableName\": \"user\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"draft_user\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"restrict\",", "  \"isDeferrable\": true", "}';;COMMENT ON CONSTRAINT workflow_process_triggering_user_fk ON %%SCHEMA_NAME%%.workflow_process IS '{", "  \"targetTableName\": \"user\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"triggering_user\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"restrict\",", "  \"isDeferrable\": true", "}';"]}], "data": {"EmailReplyTo": {"metadata": {"rootFactoryName": "EmailReplyTo", "name": "EmailReplyTo", "naturalKeyColumns": ["_tenant_id", "id"], "columns": [{"name": "_vendor", "type": "reference", "isNullable": true, "targetFactoryName": "SysVendor"}, {"name": "package", "type": "reference", "isNullable": true, "targetFactoryName": "MetaPackage"}, {"name": "node", "type": "reference", "isNullable": true, "targetFactoryName": "MetaNodeFactory"}, {"name": "company", "type": "reference", "isNullable": true, "targetFactoryName": "Company"}, {"name": "site", "type": "reference", "isNullable": true, "targetFactoryName": "Site"}, {"name": "emails", "type": "stringArray"}, {"name": "id", "type": "string"}]}, "rows": [["", null, null, null, null, "[\"no-reply\"]", "no-reply"]]}, "SysServiceOptionState": {"metadata": {"rootFactoryName": "SysServiceOptionState", "name": "SysServiceOptionState", "naturalKeyColumns": ["_tenant_id", "service_option"], "columns": [{"name": "is_activable", "type": "boolean"}, {"name": "is_active", "type": "boolean"}, {"name": "service_option", "type": "reference", "targetFactoryName": "SysServiceOption"}]}, "rows": [["Y", null, "devTools"], ["Y", null, "isDemoTenant"], ["Y", null, "changelog"], ["Y", null, "sysTag"], ["Y", null, "notificationCenter"], ["Y", null, "authorizationServiceOption"], ["Y", null, "auditingOption"], ["Y", null, "auditing"], ["Y", null, "workflow"], ["Y", null, "workflowAdvanced"], ["N", null, "workflowOption"], ["Y", null, "synchronizationServiceOption"], ["Y", null, "reportAssignment"], ["Y", "N", "showCaseOptionHighLevel"], ["Y", "N", "showCaseWorkInProgressOption"], ["Y", "Y", "showCaseOption3"], ["Y", "N", "showCaseOption2"], ["Y", "N", "showCaseOption1"], ["Y", "N", "showCaseExperimentalOption"], ["Y", "N", "showCaseDiscountOption"]]}}}