{"fromVersion": "48.0.36", "toVersion": "48.0.37", "gitHead": "9efdfd73e32583fa2919ff480de6ad76fb329467", "commands": [{"isSysPool": true, "sql": ["", "", "CREATE EXTENSION IF NOT EXISTS pgcrypto;", ""]}, {"isSysPool": true, "sql": ["", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.get_config(setting_name varchar)", "RETURNS varchar AS", "$$", "declare", "    setting_value varchar;", "BEGIN", "    SELECT current_setting(setting_name) into setting_value;", "    RETURN setting_value;", "EXCEPTION", "    WHEN OTHERS THEN", "    RETURN '';", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.drop_triggers(schema_name varchar, name_filter varchar DEFAULT '') RETURNS integer AS", "$$", "DECLARE", "    triggerRecord RECORD;", "    record_count integer;", "begin", "\trecord_count = 0;", "    FOR triggerRecord IN", "    \tSELECT trigger_name, event_object_table", "    \tFROM information_schema.triggers", "    \tWHERE trigger_schema = schema_name AND (name_filter = '' OR event_object_table = name_filter)", "\tLOOP", "\t\trecord_count = record_count + 1;", "        EXECUTE 'DROP TRIGGER ' || triggerRecord.trigger_name || ' ON ' || schema_name || '.\"' || triggerRecord.event_object_table || '\";';", "    END LOOP;", "", "    RETURN record_count;", "END;", "$$ LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.drop_notify_functions(schema_name varchar, name_filter varchar DEFAULT '') RETURNS integer AS", "$$", "DECLARE", "    triggerRecord RECORD;", "    record_count integer;", "BEGIN", "    record_count = 0;", "    FOR triggerRecord IN", "    SELECT routine_name", "    FROM information_schema.routines", "    WHERE specific_schema = schema_name and routine_name IN (name_filter || '_notify_deleted', name_filter || '_notify_created', name_filter || '_notify_updated')", "    LOOP", "        record_count = record_count + 1;", "        EXECUTE 'DROP FUNCTION ' || schema_name || '.' || triggerRecord.routine_name || ';';", "    END LOOP;", "", "    RETURN record_count;", "END;", "$$ LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.insert_table()", "RETURNS TRIGGER", "AS", "$$", "    DECLARE", "        USER_ID VARCHAR;", "    BEGIN", "        SELECT %%SCHEMA_NAME%%.get_config('xtrem.transaction_user_id') INTO USER_ID;", "        IF (USER_ID != '') THEN", "            NEW._create_user :=  CAST(USER_ID AS INT8);", "            NEW._update_user :=  CAST(USER_ID AS INT8);", "        END IF;", "        IF (NEW._create_stamp IS NULL) THEN", "            NEW._create_stamp := NOW();", "        END IF;", "        IF (NEW._update_stamp IS NULL) THEN", "            NEW._update_stamp := NOW();", "        END IF;", "        IF (NEW._update_tick IS NULL) THEN", "            NEW._update_tick := 1;", "        END IF;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.insert_shared_table()", "RETURNS TRIGGER", "AS", "$$", "    BEGIN", "        IF (NEW._create_stamp IS NULL) THEN", "            NEW._create_stamp := NOW();", "        END IF;", "        IF (NEW._update_stamp IS NULL) THEN", "            NEW._update_stamp := NOW();", "        END IF;", "        IF (NEW._update_tick IS NULL) THEN", "            NEW._update_tick := 1;", "        END IF;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.update_table()", "RETURNS TRIGGER", "AS", "$$", "    DECLARE", "        USER_ID VARCHAR;", "    BEGIN", "        SELECT %%SCHEMA_NAME%%.get_config('xtrem.transaction_user_id') INTO USER_ID;", "        IF (USER_ID != '') THEN", "            NEW._update_user :=  CAST(USER_ID AS INT8);", "        END IF;", "        NEW._update_stamp := NOW();", "        NEW._update_tick := OLD._update_tick + 1;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.update_shared_table()", "RETURNS TRIGGER", "AS", "$$", "    BEGIN", "        NEW._update_stamp := NOW();", "        NEW._update_tick := OLD._update_tick + 1;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.set_sync_tick()", "RETURNS TRIGGER", "AS", "$$", "    BEGIN", "        NEW._sync_tick :=  pg_current_xact_id();", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.signedInt32(a bigint)", "RETURNS bigint AS", "$$", "DECLARE", "BEGIN", "\t-- Convert to 32 bit signed (if leftmost bit is 1, it's a negative number)", "  \tIF (a > 2^31) THEN", "    \tRETURN a - (2^32)::bigint;", "  \tEND IF;", "  \tRETURN a;", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.imul(a bigint, b bigint)", "RETURNS bigint AS", "$$", "DECLARE", "    aHi bigint;", "    aLo bigint;", "    bHi bigint;", "    bLo bigint;", "    res bigint;", "BEGIN", "    aHi = %%SCHEMA_NAME%%.zeroFillShift(a, 16) & 65535;", "    aLo = a & 65535;", "    bHi = %%SCHEMA_NAME%%.zeroFillShift(b, 16) & 65535;", "    bLo = b & 65535;", "    res = ((aLo * bLo) + %%SCHEMA_NAME%%.zeroFillShift(((aHi * bLo + aLo * bHi) << 16) % (2^32)::bigint, 0)) | 0;", "    RETURN %%SCHEMA_NAME%%.signedInt32(res);", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.zeroFillShift(a bigint, b int)", "RETURNS bigint AS", "$$", "DECLARE", "  \tres bigint;", "BEGIN", "\tIF (a < 0) THEN", "\t\tres = a + 2^32;", "\tELSE", "\t\tres = a;", "\tEND IF;", "\tres = res >> b;", "\tRETURN res;", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.nanoid(\"size\" int4 DEFAULT 21)", "    RETURNS text", "    LANGUAGE plpgsql", "    STABLE", "    AS", "    $$", "    DECLARE", "        id text := '';", "        i int := 0;", "        urlAl<PERSON><PERSON> char(64) := 'ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW';", "        bytes bytea;", "        byte int;", "        pos int;", "    BEGIN", "        SELECT gen_random_bytes(size) INTO bytes;", "    WHILE i < size LOOP", "        byte := get_byte(bytes, i);", "        pos := (byte & 63) + 1; -- + 1 because substr starts at 1", "        id := id || substr(urlAlphabet, pos, 1);", "        i = i + 1;", "    END LOOP;", "    RETURN id;", "    END", "    $$", "    ;", " ", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.audit_table()", "RETURNS TRIGGER", "AS", "$$", "    DECLARE", "        -- parameters", "        p_root_table_name VARCHAR;", "        p_constructor VARCHAR;", "", "        -- audit variables", "        is_audit_enabled BOOLEAN;", "        tenant_id VARCHAR;", "        rid INT8;", "        login_email VARCHAR;", "        user_id INT8;", "        log_record RECORD;", "", "        -- notify variables", "        origin_id VARCHAR;", "        notify_all_disabled VARCHAR;", "        notify_tenant_disabled VARCHAR;", "        notification_id VARCHAR;", "        user_email VARCHAR;", "        constructor VARCHAR;", "        event VARCHAR;", "        topic VARCHAR;", "        envelope VARCHAR;", "        payload VARCHAR;", "    BEGIN", "        p_root_table_name := TG_ARGV[0];", "        p_constructor := TG_ARGV[1];", "", "        SELECT %%SCHEMA_NAME%%.get_config('xtrem.is_audit_enabled') INTO is_audit_enabled;", "        IF NOT is_audit_enabled THEN", "            RETURN NEW;", "        END IF;", "        SELECT %%SCHEMA_NAME%%.get_config('xtrem.login_email') INTO login_email;", "        SELECT %%SCHEMA_NAME%%.get_config('xtrem.transaction_user_id')::INT8 INTO user_id;", "", "        tenant_id := COALESCE(NEW._tenant_id, OLD._tenant_id);", "        rid := COALESCE(NEW._id, OLD._id);", "", "        SELECT * FROM %%SCHEMA_NAME%%.sys_audit_log", "        WHERE root_table_name = p_root_table_name", "            AND record_id = rid", "            AND transaction_id::TEXT = pg_current_xact_id()::TEXT", "        INTO log_record;", "", "        IF log_record IS NULL THEN", "            RAISE NOTICE 'Inserting new audit log record %:%', p_root_table_name, NEW._id;", "            IF p_root_table_name = TG_TABLE_NAME THEN", "                INSERT INTO %%SCHEMA_NAME%%.sys_audit_log (root_table_name, _tenant_id, record_id, operation, login_email, timestamp, transaction_id, record_data, old_update_tick, new_update_tick, _create_user, _update_user)", "                VALUES (p_root_table_name, tenant_id, rid, TG_OP::%%SCHEMA_NAME%%.audit_operation_enum, login_email, NOW(), pg_current_xact_id(), to_json(NEW), OLD._update_tick, NEW._update_tick, user_id, user_id);", "            ELSE", "                INSERT INTO %%SCHEMA_NAME%%.sys_audit_log (root_table_name, _tenant_id, record_id, operation, login_email, timestamp, transaction_id, record_data, old_update_tick, new_update_tick)", "                VALUES (p_root_table_name, tenant_id, rid, TG_OP::%%SCHEMA_NAME%%.audit_operation_enum, login_email, NOW(), pg_current_xact_id(), to_json(NEW), NULL, NULL);", "            END IF;", "            RAISE NOTICE 'Inserted  new audit log record root_table=%, table=%, _id=%', p_root_table_name, TG_TABLE_NAME, NEW._id;", "        ELSE", "            RAISE NOTICE 'Updating audit log record %:%', p_root_table_name, NEW._id;", "            UPDATE %%SCHEMA_NAME%%.sys_audit_log", "            SET record_data = log_record.record_data || to_jsonb(NEW)", "            WHERE root_table_name = p_root_table_name", "                AND record_id = NEW._id", "                AND transaction_id = pg_current_xact_id()::TEXT;", "            RAISE NOTICE 'Updated  audit log record %:%', p_root_table_name, NEW._id;", "        END IF;", "", "        IF p_root_table_name = TG_TABLE_NAME THEN", "            SELECT %%SCHEMA_NAME%%.get_config('xtrem.notification.disable.ALL') INTO notify_all_disabled;", "            SELECT %%SCHEMA_NAME%%.get_config('xtrem.notification.disable.t_' || tenant_id) INTO notify_tenant_disabled;", "", "            IF (notify_all_disabled <> 'true' and notify_tenant_disabled <> 'true') THEN", "                SELECT %%SCHEMA_NAME%%.get_config('xtrem.origin_id') INTO origin_id;", "                SELECT %%SCHEMA_NAME%%.get_config('xtrem.user_email') INTO user_email;", "                SELECT %%SCHEMA_NAME%%.nanoid() INTO notification_id;", "", "                IF p_constructor != '' THEN", "                    constructor := p_constructor;", "                ELSE", "                    constructor := COALESCE(NEW._constructor, OLD._constructor);", "                END IF;", "", "                CASE TG_OP", "                    WHEN 'INSERT' THEN event := 'created';", "                    WHEN 'UPDATE' THEN event := 'updated';", "                    WHEN 'DELETE' THEN event := 'deleted';", "                END CASE;", "", "                topic := constructor || '/' || event;", "                payload := '{ \"_id\":' || rid || ', \"_updateTick\":' || COALESCE(NEW._update_tick, OLD._update_tick) || '}';", "", "                RAISE NOTICE 'Inserted new notification %:%', topic, notification_id;", "                INSERT INTO %%SCHEMA_NAME%%.sys_notification", "                    (tenant_id, origin_id, notification_id, reply_id, reply_topic, user_email, login, locale,", "                    topic, payload, status, _source_id, _update_tick, _create_stamp, _update_stamp)", "                VALUES (tenant_id, origin_id, notification_id, '', '', user_email, login_email, '',", "                    topic, payload, 'pending', '', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);", "", "                RAISE NOTICE 'Notifying %:%', TG_OP, event;", "                PERFORM pg_notify('notification_queued', '{\"data\":\"{\\\"topic\\\":\\\"' || event || '\\\"}\"}');", "            END IF;", "        END IF;", "", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", ""]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Is a demo tenant", "released", false, "@sage/xtrem-system", false, "isDemoTenant"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["enable dev-only features", "experimental", false, "@sage/xtrem-system", false, "devTools"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["allow to display changelog in the app", "experimental", false, "@sage/xtrem-system", false, "changelog"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Notification center", "experimental", false, "@sage/xtrem-communication", false, "notificationCenter"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Authorization access control", "released", false, "@sage/xtrem-authorization", false, "authorizationServiceOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Auditing option", "workInProgress", false, "@sage/xtrem-auditing", false, "auditing"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Auditing option", "workInProgress", true, "@sage/xtrem-auditing", false, "auditingOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Workflow option", "workInProgress", false, "@sage/xtrem-workflow", false, "workflow"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Workflow option", "workInProgress", true, "@sage/xtrem-workflow", false, "workflowOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Synchronization", "experimental", false, "@sage/xtrem-interop", false, "synchronizationServiceOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["showCase discount option", "experimental", false, "@sage/xtrem-show-case", false, "showCaseDiscountOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["experimental option", "experimental", false, "@sage/xtrem-show-case", false, "showCaseExperimentalOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["showCaseOption's hight level description", "released", false, "@sage/xtrem-show-case", false, "showCaseOptionHighLevel"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["showCaseOption1's description", "released", true, "@sage/xtrem-show-case", false, "showCaseOption1"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["showCaseOption2's description", "released", true, "@sage/xtrem-show-case", false, "showCaseOption2"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["A workInProgress service option that can be loaded on a development environment", "workInProgress", false, "@sage/xtrem-show-case", true, "showCaseOption3"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Work in progress options cannot be activated", "workInProgress", false, "@sage/xtrem-show-case", false, "showCaseWorkInProgressOption"]}, {"isSysPool": false, "sql": "NOTIFY invalidate_category_cache, '{\"data\":\"{\\\"tenantId\\\":null,\\\"category\\\":\\\"$SHARED_NODE.SysServiceOption\\\"}\",\"containerId\":\"x3-devops00EXIL-58613\",\"excludeSelf\":true}';", "args": []}, {"action": "reload_setup_data", "args": {"factory": "SysChangelog"}}], "data": {"SysChangelog": {"metadata": {"isSharedByAllTenants": true, "rootFactoryName": "SysChangelog", "name": "SysChangelog", "naturalKeyColumns": ["hash"], "columns": [{"name": "hash", "type": "string"}, {"name": "message", "type": "string"}, {"name": "change_date", "type": "datetime"}]}, "rows": [["a8438127f05", "chore: bump patch version", "2024-10-22T20:04:16+00:00"], ["b4db8f096a4", "feat: XT-81874 test basic diamond topologies (#21825)", "2024-10-22T20:34:07+02:00"], ["b4a89bcf49a", "feat(interop): XT-80900 add sync button (#21822)", "2024-10-22T19:07:31+01:00"], ["a99b8338a12", "feat(sales): XT-67644 sales shipment picklist form editor report (#21811)", "2024-10-22T20:38:05+05:30"], ["f1ff7ab40f8", "feat(x3-inventory): X3-321778 - Mobile Aut. subcontract-transfer-Opti… (#21820)", "2024-10-22T16:56:37+02:00"], ["79f55244c76", "feat(manufacturing): work order pick list report new template (#21816)", "2024-10-22T20:26:02+05:30"], ["f4f58a60fa4", "feat(xtrem-cli-atp): use objects as function params XT-79122 (#21418)", "2024-10-22T16:24:16+02:00"], ["d3585c8db60", "feat(shoppfloor): XAPPSF-905 add time tracking page (#21818)", "2024-10-22T15:22:13+01:00"], ["d46b8d54dee", "feat: AP automation POC XT-50308 XT-81557 (#21721)", "2024-10-22T16:20:04+02:00"], ["aa391ec84c2", "fix(purchasing): XT-80926 purchase order gross price (#21742)", "2024-10-22T16:18:38+02:00"], ["3cd8996699c", "fix(master-data): XT-71612 Item prices end validity date for supplier page (#21799)", "2024-10-22T14:20:04+02:00"], ["40a84b2088a", "feat(x3-inventory): X3-321722 - Mobile Aut. Intersite transfer- updat… (#21817)", "2024-10-22T13:45:08+02:00"], ["89144b14a59", "fix(sales): XT-71193 dirty page display when hitting cancel on sidebar (#21735)", "2024-10-22T16:29:14+05:30"], ["6eb09b294c8", "fix: reset table when a sortBy is applied in main list context XT-71106 (#21797)", "2024-10-22T12:34:53+02:00"], ["72f13807218", "fix: XT-81569 revert fixed delete-tenant (#21769) (#21814)", "2024-10-22T12:34:35+02:00"], ["a5a908bd93a", "test: XT-73231-integration-tests (#21789)", "2024-10-22T12:33:16+02:00"], ["f82f6ff7de9", "test: XT-78925-integration-tests (#21800)", "2024-10-22T12:31:06+02:00"], ["f74a7206f0e", "fix(xtrem-stock): XT-81682-posting-tab-error-details (#21795)", "2024-10-22T12:15:01+02:00"], ["5ba6a67acc0", "feat(xtrem-services): XT-79487 stock transfer shipment repost (#21803)", "2024-10-22T12:12:19+02:00"], ["da0eea3b8a8", "feat(all): XT-66253 move service option to workInProgress for frp1000 bankManager sageHr shopify (#21578)", "2024-10-22T11:23:13+02:00"], ["6cb8ddd3dcf", "feat(interop): XT-79074 add vendor (#21807)", "2024-10-22T09:45:26+01:00"], ["26cc7a89b9b", "fix(xtrem-cli-atp): XT-81721_4-Vital-pod-nested-field (#21805)", "2024-10-22T10:28:43+02:00"], ["cc9f6b5f853", "test: visual regression XT-73452 (#21734)", "2024-10-22T11:18:48+03:00"], ["52eace1f3ed", "fix(automationcrew): XT-81614 (#21809)", "2024-10-22T07:49:40+00:00"], ["6de2b6de34e", "feat: x3-321125 add the companyName1 and 2 (#21796)", "2024-10-22T09:01:11+02:00"], ["7f370ffa4da", "feat(xtrem-master-data): XT-79489-item-site-stock-rules (#21783)", "2024-10-22T07:47:12+02:00"], ["23ed8ff3f5d", "chore(deps): update dependency eslint-plugin-jsx-a11y to v6.10.1 (#21808)", "2024-10-22T01:03:18+02:00"], ["f24946d71fd", "chore: updated docker pnpm-lock.yaml files", "2024-10-21T22:52:47+00:00"], ["a72cab00498", "chore: commit upgrades", "2024-10-21T22:35:31+00:00"], ["eaf53a8d6b3", "chore: bump patch version", "2024-10-21T21:27:29+00:00"], ["617dff91cb3", "fix: XT-81342 fix upgrade of set_sync_tick trigger (#21786)", "2024-10-21T23:23:11+02:00"], ["dd30b26853d", "feat(shopfloor): XAPPSF-880 update resource widget (#21747)", "2024-10-21T20:40:23+01:00"], ["e63a2609aa5", "feat(finance+reference+stock): XT-81614-refactoring-ft (#21791)", "2024-10-21T22:32:38+03:00"], ["5d7cb3b6c52", "feat(uxcrew): XT-81456 sort on primary customer (#21794)", "2024-10-21T21:19:57+02:00"], ["eb2be9f4fd8", "feat(x3-stock): X3-317337 (#21633)", "2024-10-21T19:29:36+02:00"], ["dcf8b69d82e", "feat: X3-318278 add arg for base64 password (#21806)", "2024-10-21T19:24:43+02:00"], ["2219a062009", "fix: Update prepare-test-results-for-publishing.sh (#21802)", "2024-10-21T19:22:22+02:00"], ["67df7733303", "fix: X3-321670 tedious date group by (#21804)", "2024-10-21T19:07:42+02:00"], ["7d20707697c", "fix: X3-321713 date management (#21798)", "2024-10-21T18:47:24+02:00"], ["ffeaba6734e", "feat(x3-connector): XT-81381 update export value (#21801)", "2024-10-21T17:02:52+01:00"], ["183de9303d4", "fix: X3-318278 fix X3 ping route (#21790)", "2024-10-21T15:46:38+02:00"], ["b39b299bf9e", "fix: add totalCount callback documentation XT-78925-doc (#21792)", "2024-10-21T15:21:09+02:00"], ["9dd5f5cc727", "fix(shopfloor): XAPPSF-894 fix tracking counter reduce calls to isOperatorId (#21793)", "2024-10-21T14:10:48+01:00"], ["52fe1588deb", "chore(deps): update dependency @testing-library/jest-dom to v6.6.2 (#21781)", "2024-10-21T14:59:01+02:00"], ["c22aec022fc", "feat(stock): improve stock movement management for intersite transfer (#21762)", "2024-10-21T14:16:55+02:00"], ["2a1adb11011", "feat(x3-inventory): X3-321711 - Automated test - MobilePickTicket- va… (#21785)", "2024-10-21T14:02:58+02:00"], ["cf7e9301abd", "fix(finance): XT-80801 change category name (#21787)", "2024-10-21T11:35:53+01:00"], ["eebb5e7972d", "feat: XT-81377 SysNodeTransformation refactoring (#21775)", "2024-10-21T12:30:33+02:00"], ["82dfa6b48d7", "fix(xtrem-cli-atp): add scrolluntilclickable XT-80836 (#21701)", "2024-10-21T12:29:05+02:00"], ["f8631c8c70a", "feat(tc): update nodes sinvoice pinvoice pbsupplier step2 x3 321464 (#21768)", "2024-10-21T10:39:24+02:00"], ["5125e3e2a30", "feat(xtrem-supply-chain): XT-78091 generic email functionality fix (#21459)", "2024-10-21T09:45:13+03:00"], ["00596895352", "chore: updated docker pnpm-lock.yaml files", "2024-10-20T21:41:27+00:00"], ["db0543cccc8", "chore: commit upgrades", "2024-10-20T21:22:52+00:00"], ["bf06985be24", "chore: bump patch version", "2024-10-20T20:04:05+00:00"], ["37cd2595dcf", "fix(stock): XT-79393 remaining changes on timeout error pr (#21304)", "2024-10-20T15:36:40+02:00"], ["70908078d41", "chore: updated docker pnpm-lock.yaml files", "2024-10-19T21:45:20+00:00"], ["641ae7e78ff", "chore: commit upgrades", "2024-10-19T21:28:49+00:00"], ["8c7dc3b2635", "chore: bump patch version", "2024-10-19T20:04:39+00:00"], ["643e1af6d79", "chore: updated translation files 20241018.1 (#21774)", "2024-10-19T21:49:07+02:00"], ["8e7bd58d0aa", "fix: XT-81382 small improvement to the audit_table trigger function + hide query-data action (#21764)", "2024-10-19T13:39:23+02:00"], ["cf646f9c80e", "chore(deps): update storybook monorepo to v8.3.6 (#21778)", "2024-10-19T09:30:57+02:00"], ["92ff0a2d3d5", "fix(deps): update dependency use-debounce to v10.0.4 (#21779)", "2024-10-19T09:30:52+02:00"], ["65457cb0e26", "chore(deps): update dependency @types/lodash to v4.17.11 (#21777)", "2024-10-19T01:02:54+02:00"], ["2583c4f6140", "chore: updated docker pnpm-lock.yaml files", "2024-10-18T21:59:34+00:00"], ["0b01c05f91e", "chore: commit upgrades", "2024-10-18T21:42:55+00:00"], ["b4f95633cba", "chore: bump patch version", "2024-10-18T20:20:39+00:00"], ["d87d14c7b7c", "fix: XT-81382 improve error handling on step.skip() (#21776)", "2024-10-18T22:01:04+02:00"], ["037f6255642", "fix: XT-81569 fixed delete-tenant (#21769)", "2024-10-18T21:50:46+02:00"], ["498d5affae3", "feat(x3-inventory): X3-321681 - Automated test - Update-stock-change-… (#21772)", "2024-10-18T19:02:07+02:00"], ["4303c154276", "feat(x3-inventory): X3-321669 - Update-SubcontractTransfer, stock-cha… (#21771)", "2024-10-18T19:01:57+02:00"], ["991ff3aefe8", "fix: X3-319144 empty date send as null (#21651)", "2024-10-18T19:00:25+02:00"], ["ac40e9f280a", "feat(x3-stock): X3-319698 (#21426)", "2024-10-18T18:05:48+02:00"], ["cda33408707", "feat(x3-stock): X3-319700 (#21456)", "2024-10-18T17:58:10+02:00"], ["31d0c44a0b0", "feat(xtrem-supply-chain): XT-80051 Stock Transfer Shipment Fixes (#21767)", "2024-10-18T17:07:08+02:00"], ["6e68289f82b", "feat(uxcrew): XT-81453 add export button on item in customer (#21755)", "2024-10-18T15:56:51+02:00"], ["07617618240", "feat: XT-81362 Removal of 'Serial number information' dropdown action (#21760)", "2024-10-18T15:51:18+02:00"], ["90725398075", "fix(scheduler): manage parameter array and reference type XT-80892 (#21756)", "2024-10-18T14:08:57+02:00"], ["58025787410", "chore: revert ubuntu v2 pool change (#21766)", "2024-10-18T14:04:01+02:00"], ["60061dc365a", "feat(master): XT-79218 supplier authorization refactoring (#21758)", "2024-10-18T14:01:25+02:00"], ["2d45390fe23", "feat(shopfloor): XAPPSF-842 tracking resource page and format start end date (#21761)", "2024-10-18T13:22:33+02:00"], ["f54531f8227", "fix(xtrem-supply-chain): XT-81279 dimension management corrections (#21671)", "2024-10-18T13:03:51+02:00"], ["5201143054a", "feat: XT-80837 test new Azure agents pool (#21533)", "2024-10-18T11:53:07+02:00"], ["221c69afeb4", "feat: X3-319164 sort collections in api payload (#21759)", "2024-10-18T11:20:26+02:00"], ["9a1c00956c2", "fix(master-data): XT-77776 Item supplier prices import (#21676)", "2024-10-18T10:47:38+02:00"], ["6ea5d9d0ac2", "fix(master-data): XT-81267 Item currency in sales block (#21733)", "2024-10-18T10:04:42+02:00"], ["4c8f509cd72", "feat: XT-71547 stock detailed inquiry (#21736)", "2024-10-18T09:44:18+02:00"], ["ffcde81303c", "chore(renovate): restore comments and prettify", "2024-10-18T09:26:40+02:00"], ["dacafa1c76f", "fix: deep binded image for header XT-72628 (#21746)", "2024-10-18T09:14:18+02:00"], ["79b5d1381f1", "fix(deps): update dependency thread-loader to v4.0.4 (#21754)", "2024-10-18T09:11:23+02:00"], ["93962699b0f", "fix(xtrem-cli-atp): XT-80511 robot clicks on link instead of field (#21588)", "2024-10-18T09:08:40+02:00"], ["5151e705364", "feat(xtrem-stock): XT-80871 set dimensions stock adjustment improvement (#21741)", "2024-10-18T08:53:45+02:00"], ["b3264f9f016", "chore(deps): update dependency html-webpack-plugin to v5.6.2 (#21749)", "2024-10-18T08:02:53+02:00"], ["f8a9ee9f454", "fix(deps): update dependency sanitize-html to v2.13.1 (#21750)", "2024-10-18T02:32:33+02:00"], ["07202524251", "chore(config): migrate renovate config (#21753)", "2024-10-18T02:32:29+02:00"], ["17fc4f033de", "fix: (sales-shipment) manage sales shipment button [77505] (#20790)", "2024-10-18T05:41:26+05:30"], ["1bb0981e06b", "chore: updated docker pnpm-lock.yaml files", "2024-10-17T21:39:47+00:00"], ["175f2747a57", "chore: commit upgrades", "2024-10-17T21:23:08+00:00"], ["fcdb5fb06cf", "chore: bump patch version", "2024-10-17T20:07:56+00:00"], ["b098060441d", "fix: XT-81377 paging filter date with array utils (#21748)", "2024-10-17T21:29:47+02:00"], ["bf5ffe1b042", "feat: XT-81377 compute SysApp version (#21745)", "2024-10-17T21:02:33+02:00"], ["e0561be4bc7", "feat(x3-inventory): X3-321599 - Automated test - UpdateMobileSubcontr… (#21740)", "2024-10-17T17:45:24+02:00"], ["9c7410b723f", "fix: group key for reference fields XT-80054 (#21712)", "2024-10-17T17:10:31+02:00"], ["8f4ebfb9b21", "feat(shopfloor): XAPPSF-802-Filter-shop-floor-X3-import-data-site-isManufacturing (#21723)", "2024-10-17T15:33:16+01:00"], ["49ada2b3364", "feat(xtrem-supply-chain): XT-72190 refactor stock transfer shipment post to stock to create receipt once stock posting completed (#21695)", "2024-10-17T16:31:53+02:00"], ["f3ca85c4bf3", "fix: XT-81382 fix updated event trigger for sub-nodes (#21728)", "2024-10-17T16:06:59+02:00"], ["638fb8ba89d", "fix(master-data): proper json parsing for application ui code (#21720)", "2024-10-17T15:48:04+02:00"], ["1cc1a85672d", "feat(xtrem-supply-chain): XT-75097 stock transfer receipt controls (#21619)", "2024-10-17T15:34:57+03:00"], ["0ebfa0874f9", "feat(xtrem-stock): XT-68144-stock-adjustment-displayStatus (#21614)", "2024-10-17T14:20:43+02:00"], ["a227d59c708", "feat(xtrem-x3-services): X3-321473 (#21732)", "2024-10-17T14:17:19+02:00"], ["07134a0c874", "feat: add pie chart widget XT-74223 (#21587)", "2024-10-17T15:10:29+03:00"], ["3821ce46f4a", "feat(shopfloor): XAPPSF-842 common functions refactor (#21729)", "2024-10-17T12:44:53+01:00"], ["bf3b575676f", "fix(manufacturing): XT-77360 Fix Setup time unit appears twice in Column settings (#21699)", "2024-10-17T15:05:38+05:30"], ["da0eefe7c38", "fix(master-data): XT-71611 Dates for item supplier prices (#21599)", "2024-10-17T10:54:41+02:00"], ["440edba3587", "fix: pass contextRecord to referenceFieldSuggestions in filter component (#21705)", "2024-10-17T09:34:26+02:00"], ["58a4673afe1", "feat(tc): update nodes sinvoice pinvoice pbsupplier (#21698)", "2024-10-17T09:14:22+02:00"], ["2525e224378", "feat(xtrem-finance): XT-80370 ease control on unposted apar invoices (#21673)", "2024-10-17T07:50:10+01:00"], ["b27cb9e06d3", "fix: XT-71822 Remove read operation to lookup access only (#21458)", "2024-10-17T07:34:56+02:00"], ["e143867c075", "chore(deps): update dependency axe-core to v4.10.1 (#21726)", "2024-10-17T07:32:40+02:00"], ["cee7fc41d32", "chore(deps): update dependency @types/node to v20.16.12 (#21730)", "2024-10-17T07:32:35+02:00"], ["185ff698223", "fix(deps): update dependency @sage/bms-dashboard to v1.87.5 (#21727)", "2024-10-17T00:03:03+02:00"], ["cc874765b94", "chore: updated docker pnpm-lock.yaml files", "2024-10-16T21:43:04+00:00"], ["b13c020d7ee", "chore: commit upgrades", "2024-10-16T21:25:32+00:00"], ["35ed75c3f10", "chore: bump patch version", "2024-10-16T20:04:14+00:00"], ["9f70f3309b6", "fix: XT-78834 fix send-user-notification action with executionUser (#21714)", "2024-10-16T21:59:18+02:00"], ["568d2ef0314", "feat: X3-318278 update service install/uninstall for izpack (#21724)", "2024-10-16T20:11:52+02:00"], ["93f641ea50c", "test(tests): XT-81323 - comment screenshot on demand (#21697)", "2024-10-16T20:03:11+02:00"], ["1e482c082c5", "feat(shopfloor): XAPPSF-804 clock-out on resource and work order widget. (#21716)", "2024-10-16T18:52:41+02:00"], ["d0b9c407f12", "feat(x3-inventory): X3-321458 - distribution-flow-pick-ticket - the \"… (#21702)", "2024-10-16T18:35:31+02:00"], ["5d548479bf5", "fix: XT-81342 upgrade dedicated functions for triggers (#21715)", "2024-10-16T18:20:06+02:00"], ["569502d896a", "fix(x3-purchasing): x3-320748 update the close line status (#21677)", "2024-10-16T17:56:55+02:00"], ["eecf8d9b7ab", "feat(uxcrew): XT-78731 add address on customer main list (#21653)", "2024-10-16T17:36:50+02:00"], ["ae3c0f0b23e", "feat(xtrem-finance): XT-79714 Tax detail type - intacct integration and upgrade script (#21680)", "2024-10-16T17:23:51+02:00"], ["057aa88971e", "chore: sonarcloud scan stuck (#21708)", "2024-10-16T16:50:09+02:00"], ["bd7a0f77a52", "feat(x3-inventory): X3-321385 - Fix automated tests - Misc Issue - Qt… (#21713)", "2024-10-16T16:47:05+02:00"], ["24d2e842422", "feat: XT-80334 check dates (#21434)", "2024-10-16T16:44:56+02:00"], ["9158304d28d", "fix(distribution): XT-81339 allocated quantity (#21710)", "2024-10-16T16:27:27+02:00"], ["927e1eba835", "feat(xtrem-x3-services): X3-316080 (#21682)", "2024-10-16T16:17:14+02:00"], ["058cbdf5e8c", "feat(uxcrew): XT-78730 category on item lookup (#21467)", "2024-10-16T15:41:22+02:00"], ["26637a84eae", "feat(xtrem-stock): XT-80725-stock-pages-stock-details-before-save (#21532)", "2024-10-16T15:35:49+02:00"], ["414aafd1ff7", "feat(xtrem-cli-atp): XT-79001 nested grid tests in mobile (#21399)", "2024-10-16T15:33:57+02:00"], ["6f1ba189018", "feat(purchasing): Base doc refactoring XT-78370 (#21495)", "2024-10-16T15:31:39+02:00"], ["307045e8c05", "chore: remove doc-node dependencies (#21700)", "2024-10-16T14:43:15+02:00"], ["02325421dce", "fix(shopfloor): XT-81327 enum mappings (#21707)", "2024-10-16T13:11:42+01:00"], ["45b2afb3421", "fix(distribution): XT-81272 refresh navigationpanel after submit approval (#21703)", "2024-10-16T13:20:47+02:00"], ["42f1c972cef", "feat(xtrem-stock): XT-75885 posting tab stock (#21607)", "2024-10-16T12:44:44+02:00"], ["3f3ffe12342", "fix(master-data): XT-81042 Fix customer creation from existing BE (#21613)", "2024-10-16T12:00:12+02:00"], ["53991402326", "feat(shopfloor): XAPPSF-825 add operator side bar and move functions to common location (#21687)", "2024-10-16T10:01:39+01:00"], ["03ca407aa95", "fix: XT-80908 detect NaN in decimal and accept Infinities (#21689)", "2024-10-16T10:53:11+02:00"], ["2baf8d8e0ae", "fix(xtrem-tax): XT-81305 Wrong control on discount if Intacct is active for invoices and credit memos (#21694)", "2024-10-16T10:50:10+02:00"], ["8ad1f5bb082", "feat(xtrem-finance): XT-74233 datev warnings (#21659)", "2024-10-16T09:36:59+01:00"], ["c8e0a0d9126", "fix(distribution): XT-81314 allocation transfer for stock shipment creation (#21683)", "2024-10-16T10:35:11+02:00"], ["0a36985468b", "feat(intacctFinance): linter intacct finance (#21466)", "2024-10-16T10:30:21+02:00"], ["25a9bb12688", "fix(xtrem-cli-atp): XT-74177 fixed by adding waitFor<PERSON>romise (#21621)", "2024-10-16T10:58:54+03:00"], ["9db87a19902", "feat: total count for table widgets XT-78925 (#21561)", "2024-10-16T09:19:17+02:00"], ["4e77dedfe17", "fix(xtrem-master-data): XT-81304 remove values from reset frequency enum (#21679)", "2024-10-16T08:40:33+02:00"], ["42bbfe401d7", "fix(deps): update dependency puppeteer to v23.5.3 (#21693)", "2024-10-16T08:03:19+02:00"], ["327641cee67", "fix(deps): update dependency moment-timezone to v0.5.46 (#21692)", "2024-10-16T07:32:38+02:00"], ["95c70bec964", "fix(deps): update dependency @xmldom/xmldom to v0.9.4 (#21690)", "2024-10-16T00:03:07+02:00"], ["dad4e5a57b4", "fix(deps): update dependency form-data to v4.0.1 (#21691)", "2024-10-16T00:03:02+02:00"], ["35a5eda697c", "chore: updated docker pnpm-lock.yaml files", "2024-10-15T21:44:28+00:00"], ["bebd6b42266", "chore: commit upgrades", "2024-10-15T21:27:03+00:00"], ["9f924d726d8", "chore: bump patch version", "2024-10-15T20:08:23+00:00"], ["979366a10f4", "fix: aspect ratio on header images, not to display if no nav panel image is set XT-72628 (#21685)", "2024-10-15T21:16:32+02:00"], ["cb46edc8064", "feat(shopfloor): XT-81240 cache local nodes (#21686)", "2024-10-15T19:51:32+01:00"], ["94365f72036", "fix(sales): XT-78653 error when printing a sales invoice (#21563)", "2024-10-15T19:03:59+02:00"], ["81950af90fc", "feat(pipelines): XT-76251 - decomission v1 pipelines & code (#21484)", "2024-10-15T18:59:48+02:00"], ["f4ab9619303", "feat: XT-80915 filter variables by type (#21622)", "2024-10-15T18:05:50+02:00"], ["6c4feb4205e", "feat(xtrem-purchasing): XT-80573 Move shared and utils folders (#21510)", "2024-10-15T19:01:57+03:00"], ["acbbec7fcd1", "feat: circular header image XT-72628 (#21681)", "2024-10-15T17:49:21+02:00"], ["1961682ff76", "feat(shopfloor): XT-81240 cache local nodes (#21684)", "2024-10-15T16:29:28+01:00"], ["4c084a52718", "feat(shopfloor): XAPPSF-498 Part 2 (#21678)", "2024-10-15T15:36:39+01:00"], ["71b7bc9e295", "feat(x3-inventory): X3-319871 - pick ticket for products managed by r… (#21660)", "2024-10-15T16:05:50+02:00"], ["b795dad82b2", "fix: tech debt shopify XT-78512 (#21656)", "2024-10-15T15:55:51+02:00"], ["7d098e171dc", "fix: readme and design XT-72628 (#21672)", "2024-10-15T15:32:03+02:00"], ["9e9c223aa95", "feat(shopfloor): XAPPSF-802-Filter-shop-floor-X3-import-data-X3-ismanufacturing-site-generation (#21674)", "2024-10-15T14:23:33+01:00"], ["4373da61a25", "feat: select sections by query params XT-73231 (#21675)", "2024-10-15T15:07:25+02:00"], ["8d51725f43e", "feat(purchasing): XT-81090 fix widgets (#21655)", "2024-10-15T13:54:40+01:00"], ["c15775557fd", "fix: XT-81242 manually filter and order by computed mapped properties only (#21670)", "2024-10-15T13:08:00+02:00"], ["1c9ce9bad81", "fix(stock-data): XT-80526 available quantity in the detailed stock allocation (#21518)", "2024-10-15T12:39:33+02:00"], ["a6db719b4df", "feat(sales): XT-81089 add sales widgets (#21654)", "2024-10-15T11:22:48+01:00"], ["571c508bd4d", "feat: sample long content widget XT-73452 (#21667)", "2024-10-15T11:45:33+02:00"], ["e5b6c5c93d1", "fix: date picker style fixes XT-81233 XT-81234 (#21666)", "2024-10-15T10:27:22+02:00"], ["e5454c0836e", "feat(xtrem-purchasing): XT-80529 Refactor page-extensions files (#21457)", "2024-10-15T11:23:10+03:00"], ["0deaf1b6c42", "feat: XT-76592 fixe sales order (#20497)", "2024-10-15T13:29:46+05:30"], ["a0b4da84dda", "fix: the PO line to the SO line with assign order XT-66203 (#21242)", "2024-10-15T13:16:57+05:30"], ["60a76baabe4", "fix(deps): update dependency form-data to v4.0.1 (#21664)", "2024-10-15T08:29:26+02:00"], ["d95e828ae29", "feat(xtrem-finance-data): XT-72190 Revert getStockMovementsAmounts fu… (#21663)", "2024-10-15T08:17:24+02:00"], ["9f2c330ea6d", "fix: visual regression test scenario fix (#21662)", "2024-10-15T07:52:33+02:00"], ["9afff2c7ca1", "feat(xtrem-stock): XT-77202 set dimensions impro (#21597)", "2024-10-15T07:51:19+02:00"], ["99ce64825cc", "fix(deps): update dependency @xmldom/xmldom to v0.9.4 (#21661)", "2024-10-15T02:32:35+02:00"], ["c8f3ffbcaac", "feat(xtrem-tax): XT-79317 Discout penalty - Invoice and Credit memo controls (#21584)", "2024-10-15T01:31:02+01:00"], ["0eaca08ad57", "chore: updated docker pnpm-lock.yaml files", "2024-10-14T21:38:24+00:00"], ["e9cc312065c", "chore: commit upgrades", "2024-10-14T21:21:46+00:00"], ["60a85780860", "chore: bump patch version", "2024-10-14T20:04:34+00:00"], ["c8f6113a873", "feat: add contact card widget XT-73229 (#21553)", "2024-10-14T21:48:00+02:00"], ["d11326c4955", "feat: header image is added XT-72628 (#21449)", "2024-10-14T18:23:34+02:00"], ["e2595984e03", "fix: upgrade monaco wrapper to fix onChange events on Header and Footer report template (#21657)", "2024-10-14T18:20:42+02:00"], ["3c03c815667", "feat(xtrem-finance): XT-79705 Tax detail type - node and page (#21572)", "2024-10-14T17:18:32+02:00"], ["0e9ffe15502", "feat(distribution): XT-80816 item control removal (#21559)", "2024-10-14T15:52:36+02:00"], ["a6318bf4874", "feat(manufacturing): xt-81078 (#21649)", "2024-10-14T16:12:05+03:00"], ["6ff78b081b7", "fix: destructive popover icon color XT-81059 (#21652)", "2024-10-14T15:06:31+02:00"], ["5fd063f8a3c", "feat: XT-81147 enhance error message (#21650)", "2024-10-14T13:57:24+02:00"], ["dbd40bdbfcf", "feat(xtrem-supply-chain): XT-72191 stock transfer shipment posting tab (#21509)", "2024-10-14T13:40:14+02:00"], ["97bd8f6d8b3", "feat(xtrem-services): XT-80858 stock transfer demo data layer (#21574)", "2024-10-14T13:39:53+02:00"], ["48f03ad5e5a", "fix(deps): update dependency dompurify to v3.1.7 (#21643)", "2024-10-14T13:32:48+02:00"], ["776b9fff9d0", "fix: visual regression test execution fix (#21608)", "2024-10-14T12:59:01+02:00"], ["5f69f139227", "fix: save displayed when they should not XT-76574 (#21448)", "2024-10-14T15:55:31+05:30"], ["f50375ddf86", "fix(xtrem-manufacturing): XT-79970 operation tracking repost duplicate journals (#21543)", "2024-10-14T11:29:18+02:00"], ["14b95be2698", "fix(xtrem-finance): XT-80893 Countra account on automatically generated balance lines (#21632)", "2024-10-14T09:31:29+01:00"], ["8c4416b6bc1", "feat(shopfloor): XAPPSF-700 resource node spike (#21460)", "2024-10-14T09:05:08+01:00"], ["6398d402156", "fix(x3-purchasing-sales): X3-319448 (#21213)", "2024-10-14T09:44:24+02:00"], ["117b86c88b6", "chore: updated translation files ********.1 (#21636)", "2024-10-14T09:07:36+02:00"], ["3f69bf367a1", "ci: copy sage ai artifact pipeline update #2", "2024-10-14T08:46:53+02:00"], ["d9fb524c967", "ci: copy sage ai artifact pipeline update", "2024-10-14T08:41:20+02:00"], ["042a99c238d", "chore: updated docker pnpm-lock.yaml files", "2024-10-13T21:58:25+00:00"], ["5a67d767900", "chore: commit upgrades", "2024-10-13T21:37:56+00:00"], ["5d7080d31d8", "chore: bump patch version", "2024-10-13T20:08:25+00:00"], ["10932c23982", "feat(finance): XT-81076-refactoring-ft (#21629)", "2024-10-13T20:25:22+03:00"], ["74964d47020", "feat(reference): xt-81078 (#21628)", "2024-10-13T11:28:06+03:00"], ["f48295656ac", "chore: updated docker pnpm-lock.yaml files", "2024-10-12T21:36:45+00:00"], ["5c2dd559f30", "chore: commit upgrades", "2024-10-12T21:20:25+00:00"], ["51e4e0626ca", "chore: bump patch version", "2024-10-12T20:07:43+00:00"], ["7e76a5c4e3b", "fix(deps): update dependency express to v4.21.1 (#21645)", "2024-10-12T14:01:23+02:00"], ["fddd10d9dec", "fix(deps): update dependency cookie-parser to v1.4.7 (#21642)", "2024-10-12T08:30:46+02:00"], ["4a9b962bf97", "fix(deps): update dependency chokidar to v4.0.1 (#21641)", "2024-10-12T08:01:34+02:00"], ["ddb2af921f7", "fix(deps): update dependency @xmldom/xmldom to v0.9.3 (#21640)", "2024-10-12T07:31:16+02:00"], ["ff6c968c621", "chore(deps): update newrel<PERSON> (#21639)", "2024-10-12T02:04:35+02:00"], ["c7daf913e61", "chore(deps): update dependency @types/react-dom to v18.3.1 (#21637)", "2024-10-12T00:03:28+02:00"], ["2751068f6e1", "chore(deps): update dependency sass to v1.79.5 (#21638)", "2024-10-12T00:03:23+02:00"], ["23bcf5390d1", "chore: updated docker pnpm-lock.yaml files", "2024-10-11T21:48:16+00:00"], ["f73c8f53dd2", "chore: commit upgrades", "2024-10-11T21:31:26+00:00"], ["917d73acd47", "chore: bump patch version", "2024-10-11T20:08:14+00:00"], ["0c951b5c283", "chore(deps): update storybook monorepo to v8.3.5 (#21569)", "2024-10-11T19:32:39+02:00"], ["40d4c15b8d6", "fix(distribution): XT-80334 Fix terminology for subWorkDays function (#21626)", "2024-10-11T19:16:30+02:00"], ["bbd3e576899", "feat: XT-77246 set isCached on mapping nodes (#21631)", "2024-10-11T18:08:45+02:00"], ["724dd4cd44b", "fix(deps): update dependency @internationalized/date to v3.5.6 (#21571)", "2024-10-11T17:32:10+02:00"], ["0d55c5dddef", "feat(export): reduce chunkSize XT-80712 (#21620)", "2024-10-11T17:16:45+02:00"], ["9b4f1551885", "chore: remove x3 parameter from patch release (#21609)", "2024-10-11T16:34:53+02:00"], ["4600acea844", "feat(finance): XT-78111 data creation (#21439)", "2024-10-11T15:33:19+02:00"], ["546be498e5d", "fix(distribution): XT-79961 consistent messages in stock transfer pages (v2) (#21558)", "2024-10-11T14:50:26+02:00"], ["75e92522b4f", "feat(uxcrew): XT-76589 sequence number value (#21623)", "2024-10-11T14:42:08+02:00"], ["7e5cdc03538", "fix: XT-80881 fix upgrade of service options (#21604)", "2024-10-11T14:33:46+02:00"], ["dbc849b1f51", "feat(distribution): XT-80051 Update stock order status when deleting shipment (#21504)", "2024-10-11T14:26:56+02:00"], ["55a775ec38c", "chore(deps): update non-major types dependencies (#21568)", "2024-10-11T14:02:23+02:00"], ["e7e55374736", "feat(distribution): XT-81076 small fixes (#21616)", "2024-10-11T14:06:19+03:00"], ["605d76f767b", "fix: XT-80911 disable notifications when enabling auditing (#21615)", "2024-10-11T12:37:24+02:00"], ["f5c1b7ed6c5", "feat(xtrem-supply-chain): XT-72190 stock transfer shipment post to stock and finance posting (#21556)", "2024-10-11T11:29:19+02:00"], ["9404ed67417", "fix(automationcrew): XT-80967 small refactoring and use of postrequisites (#21612)", "2024-10-11T12:21:10+03:00"], ["c16afe07c1b", "fix(shopfloor): XAPPSF-786 resource value for queries corrected. (#21611)", "2024-10-11T11:17:54+02:00"], ["9ece7d6798f", "fix: (urgent) disable broken workflow date tests (#21618)", "2024-10-11T10:59:14+02:00"], ["ad44fa6b4fa", "feat(shopfloor): XAPPSF-805 remove redundant menu options (#21593)", "2024-10-11T10:31:50+02:00"], ["cf2ec775cb9", "feat(x3-inventory): X3-317363 -pick ticket-detailed allocation-choose the stock (#21581)", "2024-10-11T10:16:54+02:00"], ["8edf45c6442", "fix(codespace): reset cloudbeaver admin setup (#21592)", "2024-10-11T09:39:04+02:00"], ["54f045826b3", "feat(finance): XT-80801 add new widgets (#21527)", "2024-10-11T08:38:34+01:00"], ["7d644763c78", "fix(x3-system): X3-321099 - Replace zero by empty default date (#21557)", "2024-10-11T09:15:31+02:00"], ["9de048741ad", "fix(xtrem-purchasing): XT-80343 authorisation error message when submitting purchase requisition for approval (#21595)", "2024-10-11T04:43:03+02:00"], ["c48c7138d29", "fix(xtrem-cli-atp): add scroll XT-73517 (#19832)", "2024-10-11T00:33:50+02:00"], ["9006c949caf", "chore: updated docker pnpm-lock.yaml files", "2024-10-10T22:19:33+00:00"], ["98f7d90baf9", "chore: commit upgrades", "2024-10-10T22:01:50+00:00"], ["2df704e6ad9", "chore: bump patch version", "2024-10-10T20:41:35+00:00"], ["d55651c9e8d", "feat: XT-80914 - move the isVariable fields (#21606)", "2024-10-10T22:37:02+02:00"], ["d1c0004de3f", "chore: delete old x3 from repo (#21603)", "2024-10-10T22:36:36+02:00"], ["3a2bd59fe44", "feat: XT-80911 create missing audit records when activating auditing service option (#21605)", "2024-10-10T21:09:53+02:00"], ["1d5e099f677", "fix(xtrem-cli-atp): XT-80992 Javascript heap out of memory (#21580)", "2024-10-10T17:46:05+02:00"], ["b64b84cc7a8", "fix: excel/csv export on transient infinite scroll tables XT-80592 (#21598)", "2024-10-10T17:32:39+02:00"], ["684ac4ae1b7", "feat: XT-80913 substitute variables in all config values (#21579)", "2024-10-10T16:46:50+02:00"], ["a81a845d7e9", "chore: codeowners review for terminology (#21586)", "2024-10-10T16:43:39+02:00"], ["afae1af3c2f", "fix: mobile action popover on sidebar XT-80924 (#21590)", "2024-10-10T16:07:37+02:00"], ["1ca4a63e331", "feat(xtrem-finance): XT-78035 Set DATEV service option to 'released' (#21594)", "2024-10-10T16:06:56+02:00"], ["dcc39803f40", "fix: date range filter state fix XT-79683 (#21589)", "2024-10-10T15:53:49+02:00"], ["78ec95958c3", "feat(shopfloor): XAPPSF-802-XAPPSF-802-Filter-shop-floor-import-data-new-status_draft (#21596)", "2024-10-10T14:45:21+01:00"], ["b16d99b0202", "feat(shopfloor): XAPPSF-498 add estimated time side bar (#21547)", "2024-10-10T14:40:54+01:00"], ["b7070526aeb", "fix(distribution): XT-80334 Refactor the subWorkDays function to avoid infinite loop (#21469)", "2024-10-10T15:29:20+02:00"], ["9933c7d9209", "feat: X3-319164 pass deleted collection lines in X3 API payload (#21577)", "2024-10-10T15:25:13+02:00"], ["f28e70265f8", "feat(uxcrew): XT-73239 sequence number initialization (#21007)", "2024-10-10T15:00:56+02:00"], ["f96ca97b256", "chore(deps): aws-sdk v3 updates back to normal", "2024-10-10T14:06:35+02:00"], ["2670f16296f", "feat(shopfloor): XAPPSF-669-Operation-done-fix-decimal-places-completed-rejected-quantities (#21570)", "2024-10-10T11:24:02+01:00"], ["7d809bbfbbe", "fix: endpoint id naming and helper texts XT-80865 (#21566)", "2024-10-10T12:07:59+02:00"], ["4b0af829d83", "feat(interop): XT-80918 fix duplicate (#21564)", "2024-10-10T10:50:31+01:00"], ["d47cf3a5d32", "chore: disable sonarcloud (#21575)", "2024-10-10T11:26:58+02:00"], ["7ea6cc6d306", "feat(automationcrew): XT-80969-maintenance (#21573)", "2024-10-10T12:16:07+03:00"], ["62accb37229", "feat(xtrem-supply-chain): XT-80775 set dimensions stock transfer shipment (#21565)", "2024-10-10T10:23:17+02:00"], ["1d0b51353f1", "feat(xtrem-stock): XT-80102-stock-adjustement-stockDetailsStatus (#21494)", "2024-10-10T09:56:19+02:00"], ["59365be153a", "feat(shopfloor): XAPPSF-786 Synced paused tracking detail (#21555)", "2024-10-10T09:45:40+02:00"], ["6f4646c41ef", "fix(xtrem-cli-atp): xt-77042 uncommented the scrollTo (#21537)", "2024-10-10T10:31:26+03:00"], ["f1490102d08", "feat(attachment-uploads): Set NoTTL to attachments and upload XT-80917 (#21560)", "2024-10-10T09:10:37+02:00"], ["320abd11bd4", "fix(x3-stock): X3-320125 (#21515)", "2024-10-10T07:27:52+02:00"], ["9b3184c95b0", "chore: updated docker pnpm-lock.yaml files", "2024-10-09T21:58:06+00:00"], ["5ff9e72398e", "chore: commit upgrades", "2024-10-09T21:39:46+00:00"], ["f71a8053063", "chore: bump patch version", "2024-10-09T20:14:27+00:00"], ["50abf9e191c", "feat: XT-80908 to<PERSON><PERSON><PERSON><PERSON>, to<PERSON><PERSON><PERSON>, to<PERSON><PERSON><PERSON> (#21552)", "2024-10-09T22:02:07+02:00"], ["95c142d6828", "feat: XT-78834 review send user notification (#21567)", "2024-10-09T22:01:31+02:00"], ["ddaa0f650ba", "feat: XT-80912 enable workflow delete by deleting processes (#21562)", "2024-10-09T21:46:17+02:00"], ["730dd3aea52", "fix: XAPPSF-801 sql reference properties (#21423)", "2024-10-09T20:54:44+02:00"], ["6ff9d4<PERSON>ea4", "chore(deps): update non-major types dependencies (#21167)", "2024-10-09T20:02:57+02:00"], ["da985c3fc1b", "feat(wh-services): X3-320539 - Fix naming and update graphQL tests (#21482)", "2024-10-09T18:09:04+02:00"], ["35827fe7c44", "chore(deps): aws-sdk v3 updates subject to approval", "2024-10-09T18:03:01+02:00"], ["3e106303e21", "fix: XT-80017 empty reference array (#21550)", "2024-10-09T16:10:03+02:00"], ["14a10780403", "test: XT-72991-report-test-data (#21551)", "2024-10-09T16:06:29+02:00"], ["4fec31a3f76", "fix: stock service typing per movement type (#21545)", "2024-10-09T16:05:23+02:00"], ["c9176ee6275", "fix: XT-80881 fix upgrade issues with activities and service options (#21546)", "2024-10-09T15:09:47+02:00"], ["e5786807809", "feat(xtrem-purchasing): XT-80770 Post-merge file fixes (#21517)", "2024-10-09T14:53:46+03:00"], ["0ae5ce6ec42", "feat(xtrem-master-data): XT-79315 Change paymentTerm properties from xtrem-intacct-finance to xtrem-master-data (#21410)", "2024-10-09T10:13:31+01:00"], ["ef7fef3f178", "feat(x3-inventory): X3-321097 - <PERSON><PERSON><PERSON> upd following calendar pb serie2 (#21523)", "2024-10-09T11:01:08+02:00"], ["836778d66b8", "fix(purchasing): XT-80406 Purchase requisition gross price change (#21500)", "2024-10-09T10:34:50+02:00"], ["0192b799a1c", "chore(deps): update dependency sass to v1.79.4 (#21541)", "2024-10-09T10:32:36+02:00"], ["23f5cd7a5d8", "feat(distribution): XT-79346 switch intersiteStockTransferOption to experimental (#21526)", "2024-10-09T10:09:53+02:00"], ["0004bb1c496", "feat(automationcrew): XT-80715-maintenance (#21529)", "2024-10-09T10:35:52+03:00"], ["b4ff710c27e", "fix(vital-pod): ensures Pendo telemetry event handler is passed on XT-77205 (#21531)", "2024-10-09T08:26:41+02:00"], ["f9950383c4e", "chore(deps): update dependency sass-loader to v16.0.2 (#21542)", "2024-10-09T08:02:53+02:00"], ["eb975335ddc", "chore(deps): update dependency node-mocks-http to v1.16.1 (#21540)", "2024-10-09T02:07:04+02:00"], ["520fae8fcc1", "chore(deps): update dependency http-proxy-middleware to v3.0.3 (#21538)", "2024-10-09T00:05:54+02:00"], ["854a209ebbc", "chore: updated docker pnpm-lock.yaml files", "2024-10-08T21:35:42+00:00"], ["910a2db0424", "chore: commit upgrades", "2024-10-08T21:18:33+00:00"], ["2045bcc770b", "chore: bump patch version", "2024-10-08T20:04:30+00:00"], ["df32663cfb9", "chore(deps): update dependency jsdom to v25.0.1 (#21539)", "2024-10-08T21:59:02+02:00"], ["67ed740f17d", "feat: XT-80326 terminology review (#21427)", "2024-10-08T21:52:27+02:00"], ["6585b939dc2", "feat: upgrade carbon to 134.1.2 + breaking changes fixes XT-76072 (#21137)", "2024-10-08T21:43:37+02:00"], ["12c42ea10f1", "feat: XT-78834 review send-user-notification action (#21536)", "2024-10-08T21:02:54+02:00"], ["36baa8c8e4f", "fix(xtrem-supply-chain): XT-75449 update mandatory dimension message (#21508)", "2024-10-08T20:37:25+02:00"], ["593f05c8a06", "feat(distribution): XT-80671 stock on hand and stock available in stock transfer order page (#21522)", "2024-10-08T20:36:15+02:00"], ["b72aaf5bc17", "feat(supply-chain): XT-78878 create menu item for stock transfer (#21486)", "2024-10-08T20:07:09+02:00"], ["5f572a7a0a1", "feat(xtrem-finance): XT-80100 Upgrade script to set legislation and tax solution for Australia (#21519)", "2024-10-08T18:33:51+02:00"], ["f86fba1fdd2", "fix: XT-80839 catch errors in setImmediate callbacks (#21534)", "2024-10-08T18:32:23+02:00"], ["9a892324334", "feat(xtrem-finance): XT-80068 fix linter issues on finance package (#21530)", "2024-10-08T17:19:58+02:00"], ["0e35e90d10b", "fix: state of column panel after filter toggle XT-80530 (#21524)", "2024-10-08T17:12:30+02:00"], ["a964ffaac84", "fix: X3-320773 - Expected quantity is missing (#21513)", "2024-10-08T16:43:40+02:00"], ["f47c8ad97ab", "fix: improve unit-test cases (#21481)", "2024-10-08T15:54:37+02:00"], ["1b6b2b80fd6", "fix: menu item tunnel link fix XT-79155 (#21521)", "2024-10-08T15:34:45+02:00"], ["5fc1228f189", "fix: XT-80575 allow skipping _sortValue for creation mode (#21506)", "2024-10-08T06:02:27-07:00"], ["a60a0724d9a", "feat(finance): XT-80713 small refactoring (#21514)", "2024-10-08T12:21:36+00:00"], ["8edf5dff139", "feat(date-range): Add included end in date range XT-80480 (#21435)", "2024-10-08T13:51:11+02:00"], ["7a1839be0e3", "feat(xtrem-service-fabric): Update eslint version and fix lint errors on xtrem-service-fabric (#21463)", "2024-10-08T14:42:41+03:00"], ["335d1412807", "feat(xtrem-finance): XT-80068 Add address data to DATEV extraction and export of customers and suppliers (#21474)", "2024-10-08T13:14:14+02:00"], ["896a553f898", "feat: XT-80687 only generate \"old\" variables for stored properties (#21501)", "2024-10-08T12:48:00+02:00"], ["ac75e291fa6", "feat(purchasing): purchase receipt report new template (#21329)", "2024-10-08T15:09:15+05:30"], ["0120f37d800", "feat(dateTime): enhancements XT-80334 - dayName (#21476)", "2024-10-08T11:33:03+02:00"], ["bcf88506fb2", "fix: X3-319188 add api-pack to devDep (#21363)", "2024-10-08T11:00:36+02:00"], ["3ecda14cb72", "feat(master): XT-76100 authorization customer (#21078)", "2024-10-08T10:52:05+02:00"], ["4172a626332", "feat(xtrem-stock-data): XT-71852 fix lint issues on pages (#19466)", "2024-10-08T11:44:03+03:00"], ["fdce9cf7157", "chore: do not consider ack vuln as relevant (#21503)", "2024-10-08T10:05:57+02:00"], ["ff4ee9cfcb3", "fix: cached factory with isStoredEncrypted properties XT-78365 (#21052)", "2024-10-08T09:53:47+02:00"], ["470e8032e06", "test: XT-79374-automation-tests (#21461)", "2024-10-08T09:52:38+02:00"], ["1b13f2d12a8", "fix(finance): XT-80300 unable to sync sdmo account (#21390)", "2024-10-08T09:34:45+02:00"], ["23c2dea82b3", "fix(xtrem-import-export): XT-62839 adding 'All' filtering entry in navigation list for import export templates page (#21496)", "2024-10-08T09:25:20+02:00"], ["77780d1c7ee", "feat(xtrem-finance): XT-74210 add extract and export as dropdown actions to main list (#21492)", "2024-10-08T08:51:26+02:00"], ["ff890e30404", "feat(xtrem-purchasing): XT-80427 Refactor utils and shared folders (#21414)", "2024-10-08T09:08:17+03:00"], ["154b8ec651d", "chore(deps): update dependency @faker-js/faker to v9.0.3 (#21507)", "2024-10-08T07:32:36+02:00"], ["0a586826be6", "fix(deps): update dependency puppeteer to v23.5.1 (#21168)", "2024-10-08T07:02:55+02:00"], ["eab258dce18", "fix(deps): update aws-sdk-js-v3 monorepo to v3.666.0 (#21057)", "2024-10-08T00:03:16+02:00"], ["1e7acfe2d7f", "fix: XT-80656 fix condition helpers (#21490)", "2024-10-07T23:44:52+02:00"], ["1b6ea1465ae", "chore: updated docker pnpm-lock.yaml files", "2024-10-07T21:38:45+00:00"], ["f058a680d05", "chore: commit upgrades", "2024-10-07T21:18:55+00:00"], ["8d905ab09dd", "chore: bump patch version", "2024-10-07T20:05:42+00:00"], ["6b81c2d67f6", "feat(technical-data): XT-80584 remove formulas (#21470)", "2024-10-07T18:34:04+01:00"], ["ec69ac35374", "feat(services): XT-80247 - functional-tests add postrequisites (#21477)", "2024-10-07T18:40:30+02:00"], ["23caadfaee9", "feat: XT-80639 Improve calculate action (#21489)", "2024-10-07T18:28:38+02:00"], ["c5cdd3dec7c", "feat(pipelines): XT-80596 - preprod -deliver smoke test minimum pipeline (#21497)", "2024-10-07T18:13:36+02:00"], ["02d3af14cc2", "fix(x3-stock): X3-316527 (#21291)", "2024-10-07T17:20:04+02:00"], ["a2f1311316e", "feat: making has360View callback, add on360ViewSwitched event (#21485)", "2024-10-07T16:24:05+02:00"], ["45c796999de", "feat: X3-313583 filtering in index on denorm (#21450)", "2024-10-07T15:44:28+02:00"], ["c21da74bec1", "feat(x3-inventory): X3-317359 - create a shortage-pick ticket global allocated (#21480)", "2024-10-07T14:53:17+02:00"], ["50068070474", "feat(x3-stock): X3-320614 (#21472)", "2024-10-07T14:45:24+02:00"], ["50a00e542a1", "feat(xtrem-stock-blend-po): Update eslint version and fix lint errors on mashups folder (#21405)", "2024-10-07T15:03:32+03:00"], ["dc8b7355050", "fix: filter component issue fixes XT-80594 XT-80595 (#21479)", "2024-10-07T13:59:12+02:00"], ["d5aa17cd59f", "fix(supply-chain): XT-80020 stock transfer order reopen action (#21483)", "2024-10-07T13:50:04+02:00"], ["46db9fc51b8", "feat(xtrem-stock): XT-73186 filter on location stock details (#21392)", "2024-10-07T13:02:44+02:00"], ["e7a8c09cc35", "feat(distribution): XT-80541 small fixes (#21465)", "2024-10-07T09:30:59+00:00"], ["59b85bb201f", "feat: X3-316983 - using isLocationPreloaded parameter in intersite receipt (#21441)", "2024-10-07T10:59:39+02:00"], ["41a4de92daf", "fix(master-data): XT-79769 Weekly shift details for resource pages (#21384)", "2024-10-07T10:53:39+02:00"], ["cc33264195b", "feat(xtrem-intacct-gateway): Update eslint version and fix lint errors on xtrem-intacct-gateway (#21473)", "2024-10-07T11:43:16+03:00"], ["99d46964002", "fix(xtrem-supply-chain): XT-75449 default dimension management add shipping site back to default dimensions (#21471)", "2024-10-07T10:33:52+02:00"], ["72b2d09a0d0", "feat(xtrem-stock-blend-po-so): Update eslint version and fix lint errors on mashups folder (#21408)", "2024-10-07T10:16:57+03:00"], ["3810dc2a435", "feat(xtrem-purchasing): XT-79548 Refactor purchase-invoice file (#21302)", "2024-10-07T09:38:16+03:00"], ["91fc1223389", "feat(pipelines): XT-72525 - production pipelines for multiapp v2 (#21314)", "2024-10-07T08:20:35+02:00"], ["8c761e835ae", "feat(shopfloor): XAPPSF-737-different-operator (#21413)", "2024-10-07T08:10:48+02:00"], ["3c6364c5fdc", "chore: updated docker pnpm-lock.yaml files", "2024-10-06T21:56:30+00:00"], ["c7865b13ccd", "chore: commit upgrades", "2024-10-06T21:32:35+00:00"], ["aba5fca53c5", "chore: bump patch version", "2024-10-06T20:08:51+00:00"], ["cb97d9d8003", "chore: updated docker pnpm-lock.yaml files", "2024-10-05T21:53:58+00:00"], ["72090986114", "chore: commit upgrades", "2024-10-05T21:30:04+00:00"], ["b1071d60bb6", "chore: bump patch version", "2024-10-05T20:08:40+00:00"], ["b891853ef9b", "chore: updated docker pnpm-lock.yaml files", "2024-10-04T21:40:33+00:00"], ["9de526c05a0", "chore: commit upgrades", "2024-10-04T21:21:06+00:00"], ["1154fc8a650", "chore: bump patch version", "2024-10-04T20:09:08+00:00"], ["503700a8312", "feat: XT-80520 more workflow condition tests (#21468)", "2024-10-04T21:56:33+02:00"], ["a31162c46b6", "feat(xtrem-supply-chain): XT-75660 stock transfer receipt mutation (#21447)", "2024-10-04T19:17:25+02:00"], ["3ac89b0a82d", "feat(technicalData): Bom compute value to getValues XT-78300 (#21420)", "2024-10-04T17:45:53+02:00"], ["0a4a6239772", "fix(xtrem-cli-atp): xt-80358 fixed label div (#21451)", "2024-10-04T18:40:33+03:00"], ["1606d5dcbaa", "feat(intacct): XT-78789 - linter (#21455)", "2024-10-04T17:18:26+02:00"], ["89b42d63a3b", "feat: X3-316964 - use isLocationPreloaded parameter (#20075)", "2024-10-04T17:06:49+02:00"], ["85fac0beffc", "fix: XT-80323 code refactoring (#21436)", "2024-10-04T16:12:26+02:00"], ["9e66ef60120", "feat(xtrem-purchasing): XT-80209 Refactor purchase-credit-memo files (#21375)", "2024-10-04T15:12:27+03:00"], ["376e426a404", "fix(xtrem-purchasing): XT-75811 Handle the applyDefaultSupplier on purchase receipt (#21454)", "2024-10-04T14:09:10+02:00"], ["0ffacf6db7c", "feat(distribution): XT-72116 stock transfer receipt phase 2 (#21438)", "2024-10-04T14:02:04+02:00"], ["6e6548b84b7", "feat(shopfloor): X3-320563-Workcenter-Operation-lines-fix (#21442)", "2024-10-04T12:11:22+01:00"], ["87279c12175", "feat(xtrem-supply-chain): XT-75449 set default dimensions stock tranfer order (#21445)", "2024-10-04T12:20:50+02:00"], ["fb073e43a64", "feat(tc): sales quotes linked to bpartner x3 320647 (#21452)", "2024-10-04T12:14:21+02:00"], ["e18bc3d25af", "feat: XT-80520 review workflow conditions (#21443)", "2024-10-04T12:02:06+02:00"], ["814f3dc3f13", "fix(cs-crew): XT-79468 Bill of Materials with more than 20 component lines only show the first 20 on the Multilevel View (#21373)", "2024-10-04T12:03:38+05:30"], ["09cacd716d8", "chore: updated docker pnpm-lock.yaml files", "2024-10-03T21:30:46+00:00"], ["6b929979a80", "chore: commit upgrades", "2024-10-03T21:09:21+00:00"], ["ed7d3a01c1c", "chore: bump patch version", "2024-10-03T20:05:05+00:00"], ["bd87e747b80", "feat(distribution): XT-80485 small adjustments (#21433)", "2024-10-03T19:12:01+00:00"], ["ac6295e6d8a", "fix: isPhantomRowDisabled not hiding the phantom row XT-80227 (#21431)", "2024-10-03T19:23:57+02:00"], ["8617f89bcda", "fix: remove default value hack code XT-70238 (#19186)", "2024-10-03T19:09:00+02:00"], ["4dda428e0ab", "feat(xtrem-finance-data): XT-75449 dimension definition level and default setup data (#21437)", "2024-10-03T18:58:55+02:00"], ["a536c9ab194", "fix(distribution): XT-80400 add stock transfer smoke tests (#21429)", "2024-10-03T16:01:34+02:00"], ["3a7635b3259", "fix: XT-80323 add nanoid to avoid collision in filename (#21421)", "2024-10-03T06:45:12-07:00"], ["fd440fb69da", "feat(x3-inventory): X3-320806 - <PERSON><PERSON><PERSON>Recei<PERSON>,unlocking scripts (#21415)", "2024-10-03T15:09:27+02:00"], ["07af32ec83b", "feat(xtrem-stock): XT-80014-stock-post-button-primary (#21342)", "2024-10-03T15:07:27+02:00"], ["58a703cdd80", "feat(master-data): XT-71267 revert BE bind to site on page (#21030)", "2024-10-03T14:57:12+02:00"], ["522c18a74e3", "feat(stock): manage stock transfer shipment stock movement - XT-72372 (#21337)", "2024-10-03T14:40:03+02:00"], ["d50cfba2d4f", "fix: purchase receipt post button displayed twice XT-62735 (#21344)", "2024-10-03T16:08:41+05:30"], ["a477b7cdd15", "fix(tax): XT-79675 Correct UK taxes (#21409)", "2024-10-03T11:08:43+02:00"], ["53197e1eca0", "feat(automationcrew): XT-80230-maintenance (#21357)", "2024-10-03T12:05:21+03:00"], ["a38f19bf75e", "fix: date filter on widgets is not working XT-79680 (#21380)", "2024-10-03T10:16:33+02:00"], ["32f9bd54fd4", "feat(xtrem-purchasing): XT-79549 Refactor 2 purchase-invoice files (#21352)", "2024-10-03T11:09:42+03:00"], ["f4af90269c6", "feat: rethrow original exception from error handler X3-320667 (#21425)", "2024-10-03T10:06:07+02:00"], ["b955236554f", "feat: XT-80326 display log and variables in executions tab (#21424)", "2024-10-03T09:43:36+02:00"], ["700815b10e1", "feat(xtrem-finance-data): XT-72190 stock transfer posting finance posting setup data (#21402)", "2024-10-03T09:40:22+02:00"], ["465aabb11d5", "feat(manufacturing): xt-80362 (#21411)", "2024-10-03T10:35:21+03:00"], ["9ea3946aebf", "fix: XT-80446 set natural key isMandatory in the grid (#21422)", "2024-10-02T20:24:32-07:00"], ["372ea69f8a5", "chore: updated docker pnpm-lock.yaml files", "2024-10-02T21:35:47+00:00"], ["d7ddac56c9e", "chore: commit upgrades", "2024-10-02T21:16:28+00:00"], ["d610778b7f9", "chore: bump patch version", "2024-10-02T20:05:08+00:00"], ["c5294eb98df", "chore: updated translation files 20241002.1 (#21419)", "2024-10-02T20:52:02+02:00"], ["552f387d56b", "feat(purchase): BasePurchaseDocument - natural key XT-78372 (#21283)", "2024-10-02T20:01:58+02:00"], ["bfc4b193423", "feat(xtrem-supply-chain): XT-78091 generic email functionality (#21341)", "2024-10-02T19:52:52+03:00"], ["000390b897b", "fix(stockData): item site ext ts to sql compliant - XT-78300 (#21259)", "2024-10-02T18:28:27+02:00"], ["f7b2c7841a4", "feat: XT-80334 check date format (#21412)", "2024-10-02T17:47:26+02:00"], ["de124958c62", "feat(wh-master-data): X3-320539 - Remove improper node-functions located in root (#21403)", "2024-10-02T16:59:09+02:00"], ["df45241524c", "fix: XT-80224 remove natural key from payload in update mode (#21388)", "2024-10-02T06:17:35-07:00"], ["2b64ef5dcad", "chore: XT-999999 delete xtrem-workflow-test (#21394)", "2024-10-02T15:16:18+02:00"], ["a4fe9eee880", "fix: XT-79412 fix upgrade issues when removing a level of abstraction (#21395)", "2024-10-02T15:11:50+02:00"], ["96a31570ed8", "feat(shopfloor): XAPPSF-497 detail panel operation details (#21404)", "2024-10-02T13:52:12+01:00"], ["f885e43c18d", "feat: XT-80380 Json type (#21400)", "2024-10-02T14:12:27+02:00"], ["ed83249f5e0", "feat: expose original error details on server error object X3-320667 (#21389)", "2024-10-02T13:42:02+02:00"], ["ba7f8e9e51a", "fix: upgrade webpack XT-80366 (#21393)", "2024-10-02T12:32:30+02:00"], ["d0d9a31cd7e", "fix: XT-76001 Fix duplicatedValue override and tests (#21379)", "2024-10-02T12:26:59+02:00"], ["6d29b64464e", "fix: nested grid with sidebar showcase page XT-79169 (#21391)", "2024-10-02T11:23:40+02:00"], ["5e4a6e13385", "feat(distribution): XT-75498 Stock transfer receipt: main list and basic page creation (#21351)", "2024-10-02T08:47:38+02:00"], ["69ae73bc89d", "feat(xtrem-avalara-gateway): XT-78777 Move files from shared to client-functions (#21339)", "2024-10-02T08:01:25+03:00"], ["594f58b0b6f", "feat(pipelines): XT-72508 - pipelines multi app v2 for pre-production (#19945)", "2024-10-02T00:34:29+02:00"], ["1257467d110", "chore: updated docker pnpm-lock.yaml files", "2024-10-01T21:55:09+00:00"], ["fbe18bb6db5", "chore: commit upgrades", "2024-10-01T21:33:30+00:00"], ["1a1d9d8fd75", "chore: bump patch version", "2024-10-01T20:05:01+00:00"], ["b874b092c40", "feat(manufacturing): XT-74766 New test for work order creation including phantom (#21386)", "2024-10-01T20:44:40+02:00"], ["1e1c1c7406f", "feat(finance): XT-80228-refactoring-ft (#21372)", "2024-10-01T21:39:15+03:00"], ["241638bb897", "fix: code smell fixes (#21381)", "2024-10-01T20:06:11+02:00"], ["e0c95b0e956", "fix(landed-cost): XT-80223 Fix landed cost allocation panel from purchase invoice (#21364)", "2024-10-01T18:18:00+02:00"], ["e3be6ccee62", "feat: XT-80326 add \"Executions\" tab to workflow page (#21376)", "2024-10-01T18:02:30+02:00"], ["d64174d10ce", "feat(xtrem-finance): XT-74210 DATEV export journal entry lines (#21260)", "2024-10-01T17:48:06+02:00"], ["ddf13da2d5f", "feat: ck editor upgrade to V43 XT-79989 (#21361)", "2024-10-01T17:38:28+02:00"], ["c71824b55b8", "feat(x3-inventory): X3-317355 - Pick ticket that is detailed allocated and applied a destination location (#21358)", "2024-10-01T16:40:09+02:00"], ["bf266695f42", "feat(manufacturing): xt-80230 (#21378)", "2024-10-01T17:26:07+03:00"], ["4f53a3ea1fc", "feat(xtrem-finance): XT-78771 datev activities (#21320)", "2024-10-01T15:22:29+01:00"], ["178ccbb7bc7", "feat(xtrem-finance): XT-79082 Allow DATEV IDs lower than 1000 on accounts (#21350)", "2024-10-01T16:20:41+02:00"], ["afa13857486", "fix: XT-75981 is frozen fix test (#21382)", "2024-10-01T16:13:47+02:00"], ["003c52f9fc3", "feat: XT-75981 is<PERSON><PERSON>zen override (#21366)", "2024-10-01T16:05:17+02:00"], ["998ab4e1fbc", "fix(performance-tests): XT-78504 no thrown error if access token not found (#21338)", "2024-10-01T15:35:51+02:00"], ["c5bbdc713c9", "fix: XT-79845 fix parsing of order by in array utils (#21355)", "2024-10-01T15:33:48+02:00"], ["25d3894f31b", "feat(shopfloor): XAPPSF-496 add work order operation detail panel (#21370)", "2024-10-01T14:15:45+01:00"], ["cf34e9157f6", "fix: workflow node removal fixes XT-80089 XT-80087 (#21371)", "2024-10-01T14:37:04+02:00"], ["c75cbb1a363", "chore: upgrade ua-parser-js CVE-2021-4229 XT-80309 (#21367)", "2024-10-01T14:33:55+02:00"], ["2a7238f2017", "fix: clear workflow component on navigating to new record XT-80085 (#21365)", "2024-10-01T14:15:15+02:00"], ["54b8500fc8d", "fix(xtrem): XT-79940 - update code owner (#21369)", "2024-10-01T14:03:59+02:00"], ["2d3043ae022", "fix: order by query arg generation with properties starting with underscore XT-74164 (#20687)", "2024-10-01T13:13:27+02:00"], ["e01184238cf", "fix(stock-data): XT-80082 fix server side imports (#21349)", "2024-10-01T11:54:18+01:00"], ["20b71bef43a", "fix(purchasing): XT-79419 update status of purchase invoice (#21247)", "2024-10-01T11:55:35+02:00"], ["26af7dc5abf", "feat: not to display save button on clear workflow page XT-80076 (#21360)", "2024-10-01T11:24:42+02:00"], ["33ab0369393", "fix(purchasing): Cannot add line in a \"Confirmed\" PO because the quantity field is grayed out (#20930)", "2024-10-01T14:52:22+05:30"], ["31b99f49b59", "feat(wh-stock): X3-320601 - Display stock nature code (#21324)", "2024-10-01T10:21:59+02:00"], ["36e6e68b635", "fix: X3-319144 allow falsy values like '' and 0 on external storage references (#21356)", "2024-10-01T09:53:55+02:00"], ["a60e6416714", "fix(sales): XT-78896 sales order tax details (#21109)", "2024-10-01T09:25:03+02:00"], ["4d91dd70639", "chore: updated docker pnpm-lock.yaml files", "2024-09-30T21:37:25+00:00"], ["c62ba029f2c", "chore: commit upgrades", "2024-09-30T21:18:44+00:00"], ["2be7e18fc21", "chore: bump patch version", "2024-09-30T20:05:40+00:00"], ["09279af5727", "fix: show/hide floating filters in the grouped table XT-79087 (#21354)", "2024-09-30T20:58:46+02:00"], ["54c4481073e", "fix(xtrem-cli-atp): XT-75314 force update of puppeteer-core ws version (#21318)", "2024-09-30T19:31:43+01:00"], ["51bd9e52b89", "fix: XT-79821 import data - rollback transaction when dataInputError raised (#21326)", "2024-09-30T09:36:36-07:00"], ["3151f61b520", "feat: XT-80198 JSON field workaround (#21353)", "2024-09-30T18:01:49+02:00"], ["d92117c7278", "fix(manufacturing): XT-79978 Release item lookup for work order creation (#21305)", "2024-09-30T17:34:45+02:00"], ["1db6235d61c", "feat: X3-316976 - Be able to feed or not the receiving location (#21295)", "2024-09-30T16:35:21+02:00"], ["8d60d26a26a", "feat(refactoring): xt-80001 (#21347)", "2024-09-30T16:18:00+03:00"], ["76c0032d6cc", "fix: documentation/services/ not sync in Confluence (#21321)", "2024-09-30T14:43:38+02:00"], ["f0a52956578", "feat: XT-79075 add x3-connector image parameter to release patch job (#21340)", "2024-09-30T14:12:02+02:00"], ["642513ca958", "fix: XT-80066 lost selection in print-document dialog (#21345)", "2024-09-30T14:09:37+02:00"], ["f02149384b6", "fix(xtrem-sales): XT-78493 Handle the index out of bounds error on Unbilled accounts receivable (#21287)", "2024-09-30T13:31:07+02:00"], ["1843812a116", "fix(xtrem-purchasing): XT-77689 Handle the Apply default supplier button on purchase requisition. (#21224)", "2024-09-30T13:30:36+02:00"], ["f4e1198a12b", "feat(xtrem-finance): XT-77762 Disable duplication on DATEV export page (#21319)", "2024-09-30T11:49:05+01:00"], ["dc26174015a", "fix(xtrem-intacct-gateway): XT-78568 Discount payment before date taking into account invoiceDate and creditMemoDate (#21272)", "2024-09-30T11:02:20+01:00"], ["2d5cd35eec8", "feat(distribution): XT79999 Prevent concurrent issues with custom fields (#21332)", "2024-09-30T08:09:10+00:00"], ["a94523e2d02", "fix(purchasing): XT-79523 Fix landed cost allocation from purchase invoice (#21294)", "2024-09-30T09:53:19+02:00"], ["b6cf98d2122", "feat(x3-inventory): X3-320535 - shortage on a pick ticket case2 (#21311)", "2024-09-30T09:18:29+02:00"], ["150496bab6d", "feat(xtrem-supply-chain): XT-75449 stock transfer dimension management (#21244)", "2024-09-30T08:42:53+02:00"], ["6e27cc42f72", "fix(xtrem-stock): XT-78682 stock detail line status (#21298)", "2024-09-30T08:19:55+02:00"], ["ee102394b5f", "chore: updated docker pnpm-lock.yaml files", "2024-09-29T21:56:01+00:00"], ["beb0d8e0336", "chore: commit upgrades", "2024-09-29T21:33:00+00:00"], ["667ea58245b", "chore: bump patch version", "2024-09-29T20:08:24+00:00"], ["f0a01166e06", "chore: updated docker pnpm-lock.yaml files", "2024-09-29T15:02:05+00:00"], ["c7e62f9a370", "chore: commit upgrades", "2024-09-29T14:44:45+00:00"], ["c0eae00e423", "chore: bump patch version", "2024-09-29T13:40:27+00:00"], ["73dead6b71a", "feat: XT-79504 fix release patch (#21328)", "2024-09-29T15:34:28+02:00"], ["8b85bde8678", "feat(distribution): XT-79999 small adjustments (#21323)", "2024-09-29T06:38:26+00:00"], ["d3c90de66fb", "chore: updated docker pnpm-lock.yaml files", "2024-09-28T21:47:47+00:00"], ["8da66fdccd2", "chore: commit upgrades", "2024-09-28T21:29:05+00:00"], ["87f460c1b24", "chore: bump patch version", "2024-09-28T20:08:10+00:00"], ["657f3efdd8d", "chore: updated docker pnpm-lock.yaml files", "2024-09-27T21:53:44+00:00"], ["739d6aaed97", "chore: commit upgrades", "2024-09-27T21:33:47+00:00"], ["a14aa6452c8", "chore: bump patch version", "2024-09-27T20:04:30+00:00"], ["a174740401e", "fix: XT-79956 improve access control to meta node factory (#21297)", "2024-09-27T10:33:56-07:00"], ["3347a8409fc", "fix(master-data): XT-79414 Night shift detail (#21286)", "2024-09-27T19:09:29+02:00"], ["12cbade7c62", "fix: XT-79845 expand node order by using natural key (#21290)", "2024-09-27T17:31:34+02:00"], ["4fba3fe74ec", "feat: XT-79504 X3 Connector SQL Upgrade - recording and testing (#21307)", "2024-09-27T17:31:08+02:00"], ["bc9c1f5cc43", "feat(xtrem-stock): xt-78962 fix lint errors on xtrem stock(pages & page-extensions) - second batch (#21096)", "2024-09-27T16:55:52+03:00"], ["a9581fda3e9", "feat(shopfloor): XAPPSF-770-create-Workcenter-Operation-Node (#21308)", "2024-09-27T14:29:10+01:00"], ["34d6c9d7e54", "fix(manufer): x3-320500 fix prerequisites script (#21288)", "2024-09-27T14:28:28+01:00"], ["055a3033e9d", "feat(x3-stock): X3-319991 (#21136)", "2024-09-27T15:17:05+02:00"], ["499f53f1573", "feat(xtrem-cli-atp): XT-72904 - Refactor step definitions utils (#21111)", "2024-09-27T15:09:22+02:00"], ["00e2e580233", "feat(xtrem-purchasing): XT-78547 Refactor purchase-order file (#21039)", "2024-09-27T14:45:37+03:00"], ["a86019255af", "feat(xtrem-avalara-gateway): xt-78775 fix lint errors on xtrem-avalara-gateway II (#21169)", "2024-09-27T14:34:26+03:00"], ["a7aac64e23c", "feat: router function to navigate to the landing page (#21299)", "2024-09-27T12:43:14+02:00"], ["6d5dd9b1c61", "fix: add isDevImage parameter to shopfloor and x3 connector pipelines (#21301)", "2024-09-27T10:25:21+02:00"], ["f576389461f", "feat(shopfloor): XAPPSF-725 clock out warning if incomplete events exist (#21292)", "2024-09-27T09:18:31+01:00"], ["e7c9b6dcf3c", "feat(xtrem-finance-data): XT-77179 Improve control of DATEV ID on account duplication (#21285)", "2024-09-27T09:07:18+01:00"], ["bcb10fc07a3", "feat(automationcrew): XT-79822-automation-purchase (#21282)", "2024-09-27T10:42:08+03:00"], ["d56d9b686cb", "chore: updated docker pnpm-lock.yaml files", "2024-09-26T21:24:17+00:00"], ["6bcfb9a3446", "chore: commit upgrades", "2024-09-26T21:07:49+00:00"], ["3ec08bfefb3", "chore: bump patch version", "2024-09-26T20:04:08+00:00"], ["0239c6d660a", "feat(shopfloor): XT-79043 test mutations (#21163)", "2024-09-26T19:42:33+01:00"], ["25befe25a4d", "fix(xtrem-cli-atp): XT-79006_new fixed Filter (#21144)", "2024-09-26T20:41:41+03:00"], ["58a1ee1cc1c", "feat(xtrem-cli-atp): XT-79940 - remove basalt from ST / FT cucumber tests packages (#21289)", "2024-09-26T19:33:43+02:00"], ["ebb0e9c5d37", "feat(x3-inventory): X3-317354 - shortage on a pick ticket case1 (#21293)", "2024-09-26T19:05:38+02:00"], ["6253e0f373f", "test(x3-inventory): X3-319792 - Remove testing site in header (#21281)", "2024-09-26T18:42:19+02:00"], ["fd78e5bf8f1", "test(x3-inventory): X3-319778 - Remove testing site in header (#21284)", "2024-09-26T18:42:04+02:00"], ["86c65cae71d", "fix(sales & purchase): XT-79723 - Change the fallback value from \"\" to 0 (#21261)", "2024-09-26T17:09:15+03:00"], ["91a71f6cdc8", "fix: enable the column panel button on empty tables (#21279)", "2024-09-26T15:59:37+02:00"], ["a2f4a29ce18", "fix(distribution): XT-79893 update stock order status once shipment is created (#21278)", "2024-09-26T15:12:17+02:00"], ["87f68ef1b4d", "feat(xtrem-cli-atp): XT-76573 execute smoke test minimum (#20984)", "2024-09-26T14:57:13+02:00"], ["d25bda6ecf2", "feat(x3-inventory): X3-320489 - script upd following calendar issue (#21269)", "2024-09-26T14:40:02+02:00"], ["1cdf2ad5823", "fix: XT-78746 fix isStored vs getValue (#21275)", "2024-09-26T05:33:54-07:00"], ["387c990a871", "fix: XAPPSF-762 cursor filter (#21280)", "2024-09-26T12:39:12+02:00"], ["229197b04ca", "feat(xtrem-stock): xt-78002 fix lint errors on xtrem stock(client-functions) - first batch (#21095)", "2024-09-26T12:30:04+03:00"], ["0ce1670732f", "feat(x3-stock): x3 319504 (#21017)", "2024-09-26T10:43:35+02:00"], ["6b273ee2df3", "feat(manufacturing): xt-79697 (#21277)", "2024-09-26T11:30:54+03:00"], ["960692417c3", "feat(automationcrew): XT-79822-automation-debt-db (#21270)", "2024-09-26T10:14:56+03:00"], ["10f7256de43", "feat(xtrem-avalara-gateway): xt-78776 fix lint errors on xtrem-avalara-gateway III (#21196)", "2024-09-26T09:33:42+03:00"], ["56838b3a5da", "feat(xtrem-avalara-gateway): xt-78774 fix lint errors on xtrem-avalara-gateway I (#21126)", "2024-09-26T09:32:40+03:00"], ["a6063051900", "feat(purchasing): add naturalKey to purchaseRequisition XT-78370 (#21274)", "2024-09-26T08:05:29+02:00"], ["4a0a5a2b670", "fix: XAPPSF-761 set inbound empty reference to null (#21276)", "2024-09-26T00:48:42+02:00"], ["4deff68cd97", "chore: updated docker pnpm-lock.yaml files", "2024-09-25T21:23:34+00:00"], ["5eebd618319", "chore: commit upgrades", "2024-09-25T21:06:50+00:00"], ["99072b933b1", "chore: bump patch version", "2024-09-25T20:04:07+00:00"], ["b51e6e7934d", "feat(shopfloor): XAPPSF-666-Reengineer-time-tracking-sync-header-detail-mutation (#21271)", "2024-09-25T19:11:00+01:00"], ["66f1c8f81b2", "fix: XT-79043 interop mutations with collections (#21266)", "2024-09-25T18:45:17+02:00"], ["ff1a999c78b", "feat(xtrem-finance): XT-74208 amounts on datev journal entry lines always positive (#21263)", "2024-09-25T16:41:32+01:00"], ["b4e5cb5c7a2", "feat(x3-stock): X3-319759 (#21179)", "2024-09-25T16:56:07+02:00"], ["b57975f00b2", "feat(manufer): x3-319814 new-material-consumption-script (#21232)", "2024-09-25T14:56:30+01:00"], ["9b8b3d7f895", "fix: XT-79535 improve log for special vital mutation (#21252)", "2024-09-25T06:56:11-07:00"], ["c7bef599083", "feat(automationcrew): XT-79554-automation-debt (#21264)", "2024-09-25T16:49:16+03:00"], ["575d735d7bd", "fix(distribution): XT-78885 fix approval switches in site page (#21265)", "2024-09-25T15:42:23+02:00"], ["4ac7c38fd76", "fix(import-export-tenant): daily export small tenant pipeline (#21257)", "2024-09-25T15:10:29+02:00"], ["1253364d0dc", "feat(x3-stock): X3-319778 - LPN transfert pages - Hide site (#21094)", "2024-09-25T15:07:10+02:00"], ["c7c4e0b7377", "refactor: actionId decorator renamed to id (#21256)", "2024-09-25T14:23:59+02:00"], ["85216e7b5de", "feat(xtrem-distribution): XT-79516 base-line-to-base-outbound-line delete node and table (#21255)", "2024-09-25T14:06:24+02:00"], ["9a797bf2003", "fix: XT-74237 speed up tenants deletion (#21226)", "2024-09-25T13:27:47+02:00"], ["6fc0e678880", "fix(distribution): XT-76693 fix stock transfer order approval from email (#21214)", "2024-09-25T12:38:50+02:00"], ["efab18aba11", "feat(x3-stock): X3-319792 - others pages - Hide site (#21099)", "2024-09-25T12:38:02+02:00"], ["00e8c9247ba", "fix: header sizing with detail list XT-79470 (#21254)", "2024-09-25T12:04:57+02:00"], ["090e1e55e82", "feat(x3-stock): X3-319800 - By identifier pages - Hide site (#21108)", "2024-09-25T11:29:43+02:00"], ["95d56bb075d", "fix: XT-79351 multiple unique indexes are forbidden only for setup nodes (#21253)", "2024-09-25T11:22:20+02:00"], ["2e1cb165676", "feat(distribution): XT-75062 stock transfer receipt sequence number (#21248)", "2024-09-25T10:59:11+02:00"], ["69d7026bd46", "feat(shopfloor): XAPPSF-711 (#21258)", "2024-09-25T09:25:19+01:00"], ["3248ffc7f98", "feat(xtrem-finance): XT-74209 DATEV page with extracted journal entry lines (#21171)", "2024-09-25T10:23:41+02:00"], ["f0a7ac2f0ad", "fix: XT-78516 alias Operation in generation of api.d.ts (#21185)", "2024-09-25T10:04:02+02:00"], ["762910d85a0", "feat(shopfloor): XAPPSF-730 Reworked handling of clock in/out button. (#21238)", "2024-09-25T09:20:53+02:00"], ["174d89d3b2d", "fix: sales order Tax details failing to open XT-77498 (#20981)", "2024-09-25T11:57:51+05:30"], ["e1f8a384ae6", "fix(table/pod): fixes issue where dropdown actions were not tracked X… (#21203)", "2024-09-25T07:35:41+02:00"], ["a0fae064e5a", "fix(Purchase): XT-79239 Error when selecting the supplier (#21220)", "2024-09-25T10:10:11+05:30"], ["3feab033bcc", "test(x3-stock): X3-319765 - Updates pick ticket tests for navigation panel behavior change for list and ticket (#21251)", "2024-09-25T06:38:26+02:00"], ["6ae415ebdba", "fix(stock): XT-78087 Cannot update conversions in Unit of measure (#21138)", "2024-09-25T09:41:46+05:30"], ["bf2f25795bc", "fix(Purchasing): disable phantom row when status \"Approved\" in Purchase requisition (#21198)", "2024-09-25T09:41:11+05:30"], ["76972f531d2", "chore: updated docker pnpm-lock.yaml files", "2024-09-24T21:40:32+00:00"], ["f276e55bc39", "chore: commit upgrades", "2024-09-24T21:20:55+00:00"], ["e0bf80429a4", "chore: bump patch version", "2024-09-24T20:04:26+00:00"], ["6ebbcd3fc1a", "fix(performance-tests): XT-78504 sales order complete flow scenario refactoring (#21117)", "2024-09-24T21:09:37+02:00"], ["17e4d8316f9", "feat(xtrem-finance): XT-74208 datev journal entry extraction (#21093)", "2024-09-24T17:36:43+01:00"], ["6e8c0e2e234", "feat(baseDoc): adding new unit tests XT-78511 (#20998)", "2024-09-24T18:13:51+02:00"], ["3e5326d757b", "fix(xtrem-cli-atp): XT-77436 fix cucumber dependencies to match wdio (#21207)", "2024-09-24T15:52:38+01:00"], ["6419bd49bb0", "feat(x3-inventory): X3-320393 - pick-ticket (#21246)", "2024-09-24T16:05:22+02:00"], ["7b05fe81a91", "fix(export-import-tenants): XT-79164 updating the new export-import url used by cloud manager v2 (#21239)", "2024-09-24T15:50:35+02:00"], ["12d59c95ffc", "fix: XT-75843 fix link, switch from _constructor to _factory (#21235)", "2024-09-24T06:36:24-07:00"], ["085b525eb6c", "fix: select filter on custom fields XT-74203 (#20326)", "2024-09-24T14:11:51+02:00"], ["a18f6968d58", "feat(cleanup): xt-79209 (#21241)", "2024-09-24T14:44:23+03:00"], ["606d40c90aa", "feat(wh-stock): X3-261140 - Update inventory reason and add decimals support (#21199)", "2024-09-24T10:38:56+02:00"], ["33487cac715", "feat(pipelines): XT-73270 - wh mobile automation pipelines and architecture - v3 (#21212)", "2024-09-24T10:19:16+02:00"], ["6bc489f7b19", "feat(shopfloor): XAPPSF-740 add site to tracking header and sync nodes (#21230)", "2024-09-24T09:17:11+01:00"], ["47a21ba79c4", "feat(purchasing): XT-79147 Refactor purchase-order-approval file (#21147)", "2024-09-24T11:16:04+03:00"], ["e974ea73335", "feat(purchasing): XT-79049 Refactor purchase-order-assignment-details-panel file (#21146)", "2024-09-24T11:15:52+03:00"], ["42e1c0fa09c", "feat(xtrem-purchasing): XT-79015 Refactor purchase-order-line-inquiry file (#21143)", "2024-09-24T11:15:41+03:00"], ["f19ee36cca9", "feat(data-time): fix DateRange includes XT-78884 (#21208)", "2024-09-24T09:55:02+02:00"], ["e1c72e2e805", "feat(distribution): XT-79364 move test to prereq (#21221)", "2024-09-24T07:37:52+00:00"], ["7f4282c6989", "fix(xtrem-finance-data): XT-79510 Fix regression for default dimensions on WO order to order (#21233)", "2024-09-24T09:01:25+02:00"], ["a191deea60a", "chore: updated docker pnpm-lock.yaml files", "2024-09-23T21:50:52+00:00"], ["e59b1244de7", "chore: commit upgrades", "2024-09-23T21:28:48+00:00"], ["e56f3eb12f0", "chore: bump patch version", "2024-09-23T20:05:04+00:00"], ["5a87bce2b56", "fix(xtrem-cli-atp): XT-70642 ignore context => false (#21194)", "2024-09-23T18:30:05+02:00"], ["aba39063fb9", "feat(x3-stock): X3-319765 - Pick tickets pages - Hide site (#21089)", "2024-09-23T18:19:30+02:00"], ["905a8fca395", "feat(xtrem-supply-chain): XT-75659 stock transfer shipment from stock transfer order (#21174)", "2024-09-23T16:21:13+02:00"], ["38d32aba18f", "feat(finance): XT-72334 new test (#21215)", "2024-09-23T13:38:53+02:00"], ["3469dc50ff2", "feat(xtrem-finance): XT-78343 deal AU as GB (#21158)", "2024-09-23T11:59:20+01:00"], ["4c191c6806d", "feat(finance): XT-78017 AU legislation (#20832)", "2024-09-23T12:58:39+02:00"], ["a69423a1de2", "fix(manufacturing): XT-78691 Work in progress cost with cost value NaN (#21085)", "2024-09-23T12:16:02+02:00"], ["2660f711bb2", "feat(xtrem-x3-inventory): X3-317352 (#21216)", "2024-09-23T11:41:18+02:00"], ["e2a57231215", "feat(x3-purchasing): X3-319739 - Receipt pages - Hide site (#21073)", "2024-09-23T09:02:13+02:00"], ["7803cceee34", "chore: updated docker pnpm-lock.yaml files", "2024-09-22T21:28:17+00:00"], ["e0f75aa9102", "chore: commit upgrades", "2024-09-22T21:08:37+00:00"], ["f7a8e96fcc5", "chore: bump patch version", "2024-09-22T20:07:44+00:00"], ["7329bf9ea28", "chore: updated docker pnpm-lock.yaml files", "2024-09-21T21:26:36+00:00"], ["1b569f3e04f", "chore: commit upgrades", "2024-09-21T21:07:24+00:00"], ["bebd3518829", "chore: bump patch version", "2024-09-21T20:08:01+00:00"], ["fb028084def", "fix: X3-318409 allow transient input fields in node details query (#21197)", "2024-09-21T00:04:24+02:00"], ["ae94a1d8657", "chore: updated docker pnpm-lock.yaml files", "2024-09-20T21:41:39+00:00"], ["e6f14e54124", "chore: commit upgrades", "2024-09-20T21:23:11+00:00"], ["7046b64daa8", "chore: bump patch version", "2024-09-20T20:08:00+00:00"], ["c1d2c490710", "feat(supply<PERSON><PERSON><PERSON>): stock-transfer-shipment-controls (#20827)", "2024-09-20T22:15:21+03:00"], ["52c288ecad4", "feat: pendo ids poc (#21201)", "2024-09-20T20:24:26+02:00"], ["3c2d83c36f6", "fix(xtrem-cli-atp): XT-52263 - rollback (#21211)", "2024-09-20T20:47:37+03:00"], ["1bdfd371dbc", "feat(shopfloor): XAPPSF-671 re-structure time tracking sync nodes. (#21160)", "2024-09-20T18:42:07+02:00"], ["0b214f67b91", "feat(all): fixes unit tests manuf and finance (#21165)", "2024-09-20T17:32:45+02:00"], ["e5b12374d00", "feat(purchasing): Refactoring for Purchasing package XT-78911 (#21079)", "2024-09-20T17:31:19+02:00"], ["a5cf54f49ac", "fix: XT-75843 special case for _factory (#21187)", "2024-09-20T08:23:47-07:00"], ["07b74843bdc", "fix(xtrem-cli-atp): XT-73885 - fix focus issue on card elements (#21178)", "2024-09-20T17:07:37+02:00"], ["4e29b1b5146", "fix: XT-77443 implement access check on import (#21189)", "2024-09-20T07:25:40-07:00"], ["a5445bea228", "perf: [Revert] increase the yield modulo of the async array loops (#21204)", "2024-09-20T15:53:32+02:00"], ["4c90bf54501", "feat: xtrem-interop change debug loggers into verbose, missed loggers (#21200)", "2024-09-20T15:07:27+02:00"], ["a5cc403dbad", "feat(xtrem-finance): XT-77622 countra account on automatically generated balance lines (#21177)", "2024-09-20T14:04:43+01:00"], ["f5a904b7009", "feat(xtrem-purchasing): XT-78988 Refactor purchase-order-suggestion file (#21107)", "2024-09-20T15:58:55+03:00"], ["75cb18bd973", "feat(customData): fix enum type XT-76725 (#21149)", "2024-09-20T14:52:01+02:00"], ["7e89844fd99", "fix(supply-chain): XT-76766 stock-transfer fixes after qa (#21190)", "2024-09-20T13:04:05+02:00"], ["0a760c908e9", "feat(xtrem-purchasing): XT-78987 Refactor purchase-order-table-panel file (#21105)", "2024-09-20T13:55:56+03:00"], ["0d06335009f", "feat(xtrem-cli-atp): refactor table object XT-78496 (#21026)", "2024-09-20T12:55:23+02:00"], ["8fc774fe773", "chore(deps): progress on cleanup root package (#21050)", "2024-09-20T12:27:34+02:00"], ["a137921a3a1", "fix(distribution): XT-78885 approval management in site (#21193)", "2024-09-20T12:00:58+02:00"], ["ef12230f87f", "feat(supply-chain): XT-77685-stock-transfer-order-smoke-tests (#20932)", "2024-09-20T11:58:08+02:00"], ["ee695c3a303", "feat(xtrem-purchasing): XT-78280 Refactor purchase-receipt file (#20941)", "2024-09-20T12:46:11+03:00"], ["10a4c409bcc", "feat(purchasing): XT-78364 Refactor purchase-receipt-table-panel file (#20988)", "2024-09-20T12:14:52+03:00"], ["4fd578be8a6", "feat(automationcrew): XT-79366-maintenance (#21191)", "2024-09-20T11:39:34+03:00"], ["fb33882af59", "feat: XT-78326 unbilled receivable problem 2 after refactoring (#21157)", "2024-09-20T09:51:23+02:00"], ["ff0750aeb97", "feat(masterData): _constructor on baseDocument naturalKey (#20970)", "2024-09-20T08:50:21+02:00"], ["45b23c3c798", "feat(manufacturing): XT-79207-refactoring-ft (#21180)", "2024-09-20T00:37:29+03:00"], ["943febcdaa8", "feat(automationcrew): XT-79209-maintenance (#21182)", "2024-09-20T00:36:56+03:00"], ["bb5b7c8bbe5", "feat(distribution): XT-79207 refactor tests (#21172)", "2024-09-19T21:36:37+00:00"], ["a14ba2246bf", "chore: prevent renovate auto-merge during release period (#21181)", "2024-09-19T23:31:10+02:00"], ["92b83cee26f", "fix: refresh active apps XT-79353 (#21184)", "2024-09-19T23:30:23+02:00"], ["39f65d983ef", "chore: updated docker pnpm-lock.yaml files", "2024-09-19T21:19:08+00:00"], ["871853ef11b", "chore: commit upgrades", "2024-09-19T21:03:24+00:00"], ["695afd4a782", "chore: bump patch version", "2024-09-19T20:04:47+00:00"], ["4901cd625e5", "feat: XAPPSF-357 sync scheduling (#20499)", "2024-09-19T21:24:13+02:00"], ["798ec99e733", "feat(supply-chain): XT-73134-stock-transfer-shipment-page-actions (#21134)", "2024-09-19T21:22:12+02:00"], ["ac19eed98ae", "fix(xtrem-cli-atp): XT-79139_incorrect_version_of_chrome (#21150)", "2024-09-19T21:01:28+02:00"], ["c24c04189eb", "fix(xtrem-cli-atp): XT-52263 fixed by clicked on card (#21015)", "2024-09-19T21:36:12+03:00"], ["35d5bca59cc", "feat(xtrem-x3-inventory): X3-317350 (#21170)", "2024-09-19T19:59:19+02:00"], ["9193b5ae447", "feat(x3-stock): X3-319740 - Stock controls pages - Hide site (#21083)", "2024-09-19T19:49:13+02:00"], ["ef68ac4656d", "chore: updated docker pnpm-lock.yaml files", "2024-09-19T16:07:43+00:00"], ["de4bd1bb3d4", "chore: commit upgrades", "2024-09-19T15:49:46+00:00"], ["52d831d0118", "chore: bump patch version", "2024-09-19T14:35:41+00:00"], ["9b9d46219b4", "chore: updated docker pnpm-lock.yaml files", "2024-09-19T14:10:24+00:00"], ["f91538394d0", "shopfloor-main: manual empty file to jump from 47.0.30 to 48.0.0", "2024-09-19T13:09:19+00:00"], ["4d21a8f2b2d", "xtrem-show-case: manual empty file to jump from 47.0.30 to 48.0.0", "2024-09-19T13:09:14+00:00"], ["bfb5509b118", "xtrem-glossary: manual empty file to jump from 47.0.30 to 48.0.0", "2024-09-19T13:09:10+00:00"], ["0d1cd446da3", "xtrem-services-main: manual empty file to jump from 47.0.30 to 48.0.0", "2024-09-19T13:09:05+00:00"], ["f24f600637d", "chore: bump major version", "2024-09-19T13:08:57+00:00"], ["6fc3cffa9e1", "feat(distribution): XT-79077 refactor tests (#21151)", "2024-09-19T09:08:45+00:00"], ["04ac7243894", "chore: updated docker pnpm-lock.yaml files", "2024-09-19T08:46:47+00:00"], ["8f3c2889b18", "chore: commit upgrades", "2024-09-19T08:31:16+00:00"], ["dddb749620a", "chore: bump patch version", "2024-09-19T07:34:07+00:00"], ["68a831a297d", "feat: XT-79043 fix outbound property transform (#21162)", "2024-09-19T09:29:35+02:00"], ["3d3383bc104", "fix: XT-78961: fixed selection of report parameters (#21166)", "2024-09-19T09:28:53+02:00"], ["84a3b6230d4", "fix(stock): XT-77011 Stock reorder calculation refactor (#20995)", "2024-09-19T09:28:23+02:00"], ["21e5c974f12", "chore: updated docker pnpm-lock.yaml files", "2024-09-18T21:40:02+00:00"], ["68f23d45b3e", "chore: commit upgrades", "2024-09-18T21:20:57+00:00"], ["bfd72e0cd07", "chore: bump patch version", "2024-09-18T20:04:48+00:00"], ["d934f080a78", "chore: updated docker pnpm-lock.yaml files (#21164)", "2024-09-18T19:19:10+02:00"], ["cd0c7d9fa72", "fix: XT-78583 temporary fix of \"delete attribute timeout\" issue for v47 (#20996) (#21156)", "2024-09-18T18:38:48+02:00"], ["9f3deeaca8f", "fix: OR filters on a single column XT-79085 (#21148)", "2024-09-18T18:07:02+02:00"], ["eae89647ca6", "fix: overriding problematic sinon version XT-79083 (#21155)", "2024-09-18T17:49:32+02:00"], ["7be2a68ea9a", "feat: xtrem-interop change debug loggers into verbose (#21153)", "2024-09-18T17:48:37+02:00"], ["61fbc6c9b31", "fix: custom table filter component spacing XT-79013 (#21154)", "2024-09-18T17:07:51+02:00"], ["56df2080fa1", "fix: set translations on nested grid child levels XT-79010 (#21152)", "2024-09-18T16:34:18+02:00"], ["e7bf37998f5", "perf: increase the yield modulo of the async array loops (#21141)", "2024-09-18T16:21:08+02:00"], ["dc17f8d209e", "feat(automationcrew): XT-79079-automation-maintanance (#21140)", "2024-09-18T16:42:48+03:00"], ["66670a43258", "feat(distribution): XT-78963 refactor tests (#21133)", "2024-09-18T13:42:32+00:00"], ["8c418f9e33a", "feat(automationcrew): XT-61877-automation-maintanance (#21122)", "2024-09-18T16:42:11+03:00"], ["0117c95b7d4", "feat(finance): XT-78963-refactoring-ft (#21118)", "2024-09-18T16:41:54+03:00"], ["931126e5f3d", "feat: filter collections of MetaNodeFactory prior to applying paging options in xtrem-app-metadata (#21129)", "2024-09-18T14:09:41+02:00"], ["464f10436c6", "fix: partial select state on select all checkbox XT-79088 (#21135)", "2024-09-18T13:52:03+02:00"], ["6e24be2956a", "feat(interop): XT-79042 add duplication (#21124)", "2024-09-18T11:46:56+01:00"], ["48da69d63f8", "chore(deps): update dependency @types/nodemailer to v6.4.16 (#21131)", "2024-09-18T07:32:45+02:00"], ["2e429b2dccd", "chore(deps): update dependency sass to v1.79.1 (#21132)", "2024-09-18T07:32:40+02:00"], ["37ddf544d7e", "feat: XT-79000 workflow option off by default in unit tests (#21116)", "2024-09-18T00:09:30+02:00"], ["df92412419c", "chore(deps): update non-major types dependencies (#21127)", "2024-09-18T00:03:12+02:00"], ["d28cb5438c1", "fix(deps): update dependency thread-loader to v4.0.3 (#21128)", "2024-09-18T00:03:07+02:00"], ["ce3c4e2f22e", "chore: updated docker pnpm-lock.yaml files", "2024-09-17T21:39:14+00:00"], ["6e2f401a105", "chore: commit upgrades", "2024-09-17T21:21:46+00:00"], ["bca4d4fdf47", "chore: bump patch version", "2024-09-17T20:08:21+00:00"], ["f8590070251", "fix(cs-crew): Added a new error message on stop button when status is not running (#21076)", "2024-09-17T23:00:22+05:30"], ["ebeb3023d9d", "feat(xtrem-finance): XT-78835 DATEV export - Add mandatory header line to export file structure (#21090)", "2024-09-17T19:21:06+02:00"], ["ed092d8e72e", "fix: XT-75843: expose _factory without authorization (#21121)", "2024-09-17T09:43:15-07:00"], ["dc5bdc4a850", "feat(xtrem-intacct): XT-73401 Message updates when activating/deactivating intacct (#21119)", "2024-09-17T17:30:19+01:00"], ["706718503cf", "feat(master-data): XT-71264 revert BE bind to supplier on page (#20877)", "2024-09-17T18:06:07+02:00"], ["5705be70cd7", "fix: XT-77841 - remove default navigation panel from standart cost ca… (#20965)", "2024-09-17T21:30:20+05:30"], ["1f159f5c578", "feat(shopfloor): XAPPSF-670-Time-tracking-header-incorporate-check-isSynced-property (#21123)", "2024-09-17T16:55:07+01:00"], ["e346f4bbdcc", "feat(basalt): fix convertJoin for expression XT-74164 (#21120)", "2024-09-17T17:52:14+02:00"], ["ce8c9904411", "fix(xtrem-purchasing): XT-72299 (#20402)", "2024-09-17T17:47:53+02:00"], ["e466d9df5db", "fix: XT-78652 Modify filter to include extension properties (#21110)", "2024-09-17T16:10:41+02:00"], ["95f2a715fd3", "fix(stock-data): XT-79017 Auth stock allocation (#21113)", "2024-09-17T15:34:00+02:00"], ["b13e0775499", "fix: XT-77728 - MRP calculation justification dialog error (#20849)", "2024-09-17T19:01:47+05:30"], ["96ee7551a94", "fix(purchasing): XT-77386 Auth purchase return delete (#21115)", "2024-09-17T15:27:43+02:00"], ["0bb67321f87", "fix(stock-data): XT-78567 allocate serial numbers (#21001)", "2024-09-17T15:20:29+02:00"], ["706cc44dfd1", "feat(xtrem-cli-dev): XT-70937 add timeoutWaitForLoading (#20753)", "2024-09-17T13:45:01+02:00"], ["ae8c754647a", "feat(xtrem-intacct): XT-73401 Business rule on Intacct service option update (#21024)", "2024-09-17T11:35:24+01:00"], ["efefa4c73ef", "feat(xtrem-x3-inventory): X3-320079 (#21106)", "2024-09-17T12:30:47+02:00"], ["dd145ac65d7", "feat(xtrem-purchasing): XT-71593-purchase-order-line-filter-site (#21082)", "2024-09-17T11:32:48+02:00"], ["1f3aeabdfb6", "chore(deps): update non-major types dependencies (#21097)", "2024-09-17T11:02:49+02:00"], ["bd8ef502e2e", "feat: XT-78326 unbilled accounts receivable (#21092)", "2024-09-17T10:56:18+02:00"], ["ef52ac2e2af", "feat(distribution): XT-78736 small refactoring (#21100)", "2024-09-17T08:42:08+00:00"], ["a8316f7a556", "feat(shopfloor): XAPPSF-667 stop time treatment for setup (#21086)", "2024-09-17T10:35:07+02:00"], ["7dc83d20e11", "fix(interop): XT-78853 fix query results test (#21048)", "2024-09-17T08:53:34+01:00"], ["1cf644136ca", "feat(xtrem-stock): XT-74949 default values stock details (#21101)", "2024-09-17T09:44:24+02:00"], ["10f8d8f556f", "feat(xtrem-stock): XT-77823 (purchasing): XT-76004 (#20997)", "2024-09-17T09:35:37+02:00"], ["f7ca78dfc9b", "feat(xtrem-sales): XT-75982-sales-credit-memo-create-button (#21045)", "2024-09-17T09:20:04+02:00"], ["581ec5d2820", "chore(deps): update storybook monorepo to v8.3.1 (#21102)", "2024-09-17T07:32:49+02:00"], ["026d564df6c", "fix(deps): update dependency puppeteer to v23.3.1 (#21104)", "2024-09-17T07:32:45+02:00"], ["9e735ec20db", "fix(queue): exit on missing queue at runtime XT-78910 (#21081)", "2024-09-17T00:16:48+02:00"], ["3e91795369d", "chore(deps): update dependency eslint to v8.57.1 (#21098)", "2024-09-17T00:03:15+02:00"], ["8fa9302133c", "chore: updated docker pnpm-lock.yaml files", "2024-09-16T21:37:15+00:00"], ["***********", "chore: commit upgrades", "2024-09-16T21:18:10+00:00"], ["8d3c4a469ae", "chore: bump patch version", "2024-09-16T20:04:29+00:00"], ["83b5939698e", "fix: XT-78939 make workflow engine work in cluster (#21088)", "2024-09-16T19:57:29+02:00"], ["e48b4c33c78", "feat(xtrem-cli-atp): XT-77366 - add max instance atp parameter (#21025)", "2024-09-16T19:50:35+02:00"], ["dd17405d8c8", "feat: XT-78326 unbilled accounts payable user notification pr2 (#21084)", "2024-09-16T18:08:30+02:00"], ["eb9b95bee86", "fix: hide print button XT-78503 (#21040)", "2024-09-16T17:27:27+02:00"], ["8c9955f6c7b", "feat: XT-78886 move assertDeepPartialMatch and add unit test (#21071)", "2024-09-16T16:52:26+02:00"], ["65a5f72f558", "fix(xtrem-purchasing): XT-71707 Price updated from supplier prices (#21067)", "2024-09-16T16:17:12+02:00"], ["8de3c4f962d", "feat(supply-chain): XT-71716-stock-transfer-shipment-page-1 (#20945)", "2024-09-16T15:33:57+02:00"], ["2d0ce1fbf6f", "fix: fix add and apply new for zebra size + minor fixes in ADC sizes XT-78218 (#21018)", "2024-09-16T15:28:15+02:00"], ["58718723d12", "fix(pipelines): XT-78849 - discard accessibility tests (#21069)", "2024-09-16T15:25:07+02:00"], ["59007aa6863", "feat(xtrem-cli-atp): XT-75649 - add showcase for multi-action button (#21075)", "2024-09-16T15:09:52+02:00"], ["05b4770518f", "fix: filter editor parameter dropdown XT-77459 XT-72848 (#21077)", "2024-09-16T15:06:01+02:00"], ["877d5de8bf5", "fix(manufer): X3-319935 fix-material-consumption-scripts (#21072)", "2024-09-16T13:48:06+01:00"], ["afdd8af1ae5", "feat: XT-68810 purchase order line inquiry using mainlist (#20373)", "2024-09-16T12:56:06+02:00"], ["9ed98249fdd", "feat(xtrem-stock): xt-77004 fix lint errors on xtrem stock - 3rd batch (#20809)", "2024-09-16T12:01:51+03:00"], ["df3f99037d1", "feat: XT-78833 review send mail workflow action (#21053)", "2024-09-16T09:35:22+02:00"], ["9c363869d7c", "chore(deps): update dependency sinon to v19 (#21063)", "2024-09-16T09:02:37+02:00"], ["0133aab2bc2", "fix(deps): update dependency chokidar to v4 (#21064)", "2024-09-16T09:02:33+02:00"], ["03ed2ff5e52", "feat(actions): implements action id to dropdown and inline actions XT-77205 (#20963)", "2024-09-16T08:29:41+02:00"], ["0e0dfb3226a", "fix(deps): update dependency newrelic to v12.5.0 (#21062)", "2024-09-16T08:02:54+02:00"], ["2623faabb8b", "fix(deps): update dependency json-to-graphql-query to v2.3.0 (#21061)", "2024-09-16T02:32:51+02:00"], ["d2fd0ff2561", "fix(deps): update dependency express to v4.21.0 (#21060)", "2024-09-16T02:07:10+02:00"], ["bab6825432b", "fix(deps): update dependency react-redux to v9 (#20908)", "2024-09-16T00:03:00+02:00"], ["4b27264029c", "chore(deps): update dependency eslint-plugin-react to v7.36.1 (#21059)", "2024-09-16T00:02:45+02:00"], ["5bbd9aa74b5", "chore: updated docker pnpm-lock.yaml files", "2024-09-15T21:43:05+00:00"], ["a14ee60f2d5", "chore: commit upgrades", "2024-09-15T21:24:59+00:00"], ["060c6befe5e", "chore: bump patch version", "2024-09-15T20:08:03+00:00"], ["8f41aef6b0c", "fix(deps): update dependency eslint-plugin-unused-imports to v4.1.4 (#21058)", "2024-09-15T21:02:48+02:00"], ["7d7427d8351", "fix(deps): update dependency react-monaco-editor to ^0.56.0 (#19081)", "2024-09-15T17:33:27+02:00"], ["2859ad0e453", "chore(deps): update dependency @faker-js/faker to v9.0.1 (#21055)", "2024-09-15T17:33:03+02:00"], ["1e72f8d7bf7", "chore(deps): update dependency tsx to v4.19.1 (#21056)", "2024-09-15T17:32:58+02:00"], ["3104d515963", "chore(deps): update non-major types dependencies (#21003)", "2024-09-15T14:32:21+02:00"], ["7d731f25989", "chore(deps): update dependency @chromatic-com/storybook to v2.0.2 (#21051)", "2024-09-15T14:03:02+02:00"], ["af732e1cddb", "fix: relax cli smoke tests (#21054)", "2024-09-15T10:10:08+02:00"], ["099ceb84a80", "chore: updated docker pnpm-lock.yaml files", "2024-09-14T21:47:06+00:00"], ["cd4736b3e51", "chore: commit upgrades", "2024-09-14T21:23:59+00:00"], ["e604b623747", "chore: bump patch version", "2024-09-14T20:04:49+00:00"], ["702baa8d510", "feat: XT-78333 review entity-updated event (#21021)", "2024-09-14T17:40:07+02:00"], ["f4b38d028fe", "chore: updated docker pnpm-lock.yaml files", "2024-09-13T21:35:19+00:00"], ["c9aeb490cc6", "chore: commit upgrades", "2024-09-13T21:17:25+00:00"], ["216483a0897", "chore: bump patch version", "2024-09-13T20:04:26+00:00"], ["6acb1cbbdc5", "feat(shopfloor): XAPPSF-668-add-sync-reference-timeTrackingHeader-node (#20975)", "2024-09-13T18:46:29+01:00"], ["9500f4c71fb", "fix(xtrem-purchasing): XT-77727 PO landed cost payload added to PR payload if it exists (#20764)", "2024-09-13T18:14:15+01:00"], ["0d313ca483f", "feat(wh-stock): X3-319946 - fix navigator filter (#21047)", "2024-09-13T17:36:40+02:00"], ["9dc0d4519b9", "fix(apps): alive status from interop url XT-78770 (#21041)", "2024-09-13T16:23:08+02:00"], ["8eed875a563", "fix(xtrem-cli-atp): XT-78252 - add compatibility with phone and tablet for nested grid tables (#20967)", "2024-09-13T16:09:10+02:00"], ["fbabb1c07c3", "feat: XT-78333 add xtrem-auditing dependency to xtrem-workflow (#21032)", "2024-09-13T15:56:22+02:00"], ["76cd0fafc98", "fix: XT-74274 min max conversion fix (#21033)", "2024-09-13T06:54:30-07:00"], ["6793d4ea00b", "feat(wh-stock): X3-319946 - Fix miscellaneous texts (#21038)", "2024-09-13T15:28:52+02:00"], ["30386c86aef", "feat(xtrem-x3-inventory): X3-319963 (#21043)", "2024-09-13T15:22:28+02:00"], ["bb45c0db873", "fix(manufacturing): XT-77691 wrong time unit (#21037)", "2024-09-13T15:04:18+02:00"], ["eef2e6e523d", "feat(interop): XAPPSF-353 update connector pages (#21000)", "2024-09-13T13:57:53+01:00"], ["aaa1fd705a1", "chore(deps): update storybook monorepo to v8.3.0 (#21004)", "2024-09-13T12:32:16+02:00"], ["8654d6e0c34", "feat(inquiry): XT-64095 unbilled infinite scroll and notification (#20823)", "2024-09-13T12:28:18+02:00"], ["cea5bd8966d", "fix: XT-78532 Only add explicit grants from activities (#21008)", "2024-09-13T12:04:27+02:00"], ["d59fbc20e15", "fix(xtrem-cli-atp): XT-77436 package clean (#21031)", "2024-09-13T12:00:53+02:00"], ["e9bb06d77a8", "feat(xtrem-x3-inventory): X3-319938 (#21036)", "2024-09-13T11:55:13+02:00"], ["23bf438bc07", "feat(xtrem-master-data): XT-74649 total stock item page (#20989)", "2024-09-13T10:44:37+02:00"], ["d4cdc2e885f", "feat(xtrem-purchasing): XT-78537 Refactor purchase-receipt-matching file (#20991)", "2024-09-13T11:13:17+03:00"], ["bd9b57d3b27", "feat: XT-74094 Stock journal inquiry link to journal entry line (#20618)", "2024-09-13T09:54:28+02:00"], ["f3d5f3089f2", "fix(xtrem-master-data): XT-69636 It's not possible to remove a Ship-to address for a customer (#21011)", "2024-09-13T09:31:59+02:00"], ["be24ef9d484", "fix(stock): XT-77496 document status (#20729)", "2024-09-13T09:13:10+02:00"], ["3f836f87869", "feat(xtrem-x3-inventory): X3-317349 (#21012)", "2024-09-13T08:50:05+02:00"], ["b70ab79faf1", "chore: update aws-sdk (#21034)", "2024-09-13T07:41:56+02:00"], ["b74b5a5cf13", "chore: updated docker pnpm-lock.yaml files", "2024-09-12T21:31:42+00:00"], ["d3bcbc6ada3", "chore: commit upgrades", "2024-09-12T21:13:46+00:00"], ["20c2fc1a1d7", "chore: bump patch version", "2024-09-12T20:04:24+00:00"], ["394edda8d13", "fix: XT-78511 invoke delete rules on association child (#21005)", "2024-09-12T20:02:49+02:00"], ["6faf216d366", "fix(xtrem-cli-atp): XT-77887 fixed Closing of the Filter dropdown , by clicking outside on the 'PageTitle' , the Closing has a bug in the ui (#20802)", "2024-09-12T20:48:14+03:00"], ["0bf0b28082e", "feat: XT-78325 add _updateTick to the graphql schema (#20928)", "2024-09-12T19:18:03+02:00"], ["596a420dbdb", "feat: allow reference parameters to be associated with _id fields XT-78707 (#21029)", "2024-09-12T19:10:47+02:00"], ["211b860b43c", "feat: X3-315422 - fix problem in node freightContainer (#21028)", "2024-09-12T17:06:31+02:00"], ["f9451ce4cff", "fix: not allowing sorting on non sortable columns XT-78546 (#21010)", "2024-09-12T15:49:15+02:00"], ["ece37af853d", "feat(xtrem-finance): XT-74196 export business relations (#20944)", "2024-09-12T14:28:32+01:00"], ["cf99bfe73c1", "feat: X3-315422 - Adding node freightContainer (#19834)", "2024-09-12T14:47:05+02:00"], ["a9d051b8354", "feat: X3-315346 - move node Container to package x3-master-data (#19835)", "2024-09-12T14:41:32+02:00"], ["bed3286a037", "feat: X3-315318 - Adding node SHIPTRACK (#19836)", "2024-09-12T14:20:36+02:00"], ["92d1af26940", "feat(xtrem-sales): xt-77464 move files from shared to client-functions folder (#20923)", "2024-09-12T14:03:25+03:00"], ["1788cf23580", "feat(xtrem-stock): xt-77003 fix lint errors on xtrem stock - 2nd batch (#20638)", "2024-09-12T11:23:51+03:00"], ["b1700578b89", "feat(distribution): XT-78518 refactor tests (#21006)", "2024-09-12T06:11:43+00:00"], ["a44c2114034", "chore: updated docker pnpm-lock.yaml files", "2024-09-11T21:20:26+00:00"], ["54730fe3f34", "chore: commit upgrades", "2024-09-11T21:04:21+00:00"], ["dba726aa775", "chore: bump patch version", "2024-09-11T20:04:27+00:00"], ["10f24d6e474", "fix: XT-77776 allow direct update in case of vital child (#20985)", "2024-09-11T12:58:19-07:00"], ["4b02376e843", "feat(finance): XT-72334 data creation (#20999)", "2024-09-11T18:35:21+02:00"], ["aeab29cc40a", "feat(apps): propagate source email to x3 XT-78507 (#20971)", "2024-09-11T18:11:22+02:00"], ["4108ecd53d6", "fix(x3-manufacturing): X3-319844-remove-site-mobile (#20993)", "2024-09-11T16:37:33+01:00"], ["0276df70684", "feat(datatypes): default location details issue XT-77381 (#20953)", "2024-09-11T16:38:57+02:00"], ["63f6af184e6", "feat(finance): XT-78187 fix test data and fix posting class definition (#20982)", "2024-09-11T14:14:27+00:00"], ["fd6271de179", "fix(x3-stock): X3-319760 (#20957)", "2024-09-11T15:58:21+02:00"], ["83d6b870ba9", "fix: autosize translation key XT-78229 (#20969)", "2024-09-11T15:55:05+02:00"], ["62ef8d5b909", "feat(master-data): XT-71264 revert BE bind to customer on page XT-78030 (#19371)", "2024-09-11T15:20:58+02:00"], ["09769c8dadf", "feat: XT-77718 Make Pendo API key configurable by product (#20987)", "2024-09-11T15:13:42+02:00"], ["589bd376172", "fix(master-data): Error when creating an Item sequence number via the 'Select Item ID sequence number dialogue (#20699)", "2024-09-11T18:12:03+05:30"], ["8ee763f9fa9", "chore: add pnpm sha", "2024-09-11T12:40:22+02:00"], ["971e1709f31", "fix(cs-crew): Job schedule error when trying to create a record (#20961)", "2024-09-11T16:11:06+05:30"], ["a8074ed1518", "feat(xtrem-purchasing): XT-73214 Unit tests unbilled account payable (#20853)", "2024-09-11T10:06:03+01:00"], ["63ef2c150b3", "feat(xtrem-services): XT-73992 refactor stock transfer order controls to move to base nodes (#20931)", "2024-09-11T11:04:10+02:00"], ["a57e86a34ee", "feat(wh-services): X3-317546 - Implement mutations for sticker site depositor (#19915)", "2024-09-11T10:18:38+02:00"], ["b03d04b4663", "feat(distribution): xt-76744 (#20964)", "2024-09-11T10:45:49+03:00"], ["16a30a06d0d", "fix(x3-manufacturing): X3-318965-material-consumption-multiple-lots-d… (#20896)", "2024-09-11T08:43:01+01:00"], ["405f979df3c", "feat(supply-chain): XT-71716-stock-transfer-order-work-order (#20935)", "2024-09-11T09:08:31+02:00"], ["dd7ce7678e0", "fix(deps): update aws-sdk-js-v3 monorepo to v3.649.0 (#20979)", "2024-09-11T08:02:59+02:00"], ["8ee0956f9c2", "chore(deps): update dependency sinon to v18.0.1 (#20977)", "2024-09-11T07:32:39+02:00"], ["943408e5105", "feat(finance): XT-78398 Fix dates in finance-flow-contra-account (#20976)", "2024-09-11T05:11:01+00:00"], ["a3dec8d81e6", "chore(deps): update dependency sinon to v18.0.1 (#20978)", "2024-09-11T02:06:44+02:00"], ["899e27e69cb", "chore(deps): update dependency @chromatic-com/storybook to v2 (#20974)", "2024-09-11T00:03:10+02:00"], ["1a3d4a0512a", "feat(automationcrew): XT-61877-automation-debt-add-test-data (#20968)", "2024-09-11T00:46:02+03:00"], ["14a5aa83e8b", "fix(deps): update dependency express to v4.20.0 (#20973)", "2024-09-10T23:32:34+02:00"], ["c39481731c4", "chore: updated docker pnpm-lock.yaml files", "2024-09-10T21:20:33+00:00"], ["00dd7e65c01", "chore: commit upgrades", "2024-09-10T21:04:33+00:00"], ["7d24868f3a9", "chore: bump patch version", "2024-09-10T20:04:06+00:00"], ["ec8c7472ef3", "fix(xtrem-cli-atp): XT-77436-workaround-to-fix-pipeline-issue-with-xt… (#20962)", "2024-09-10T15:52:24+02:00"], ["449d68a6161", "fix(xtrem-cli-atp): XT-78039 - add nested grid title mobile management for visibility (#20880)", "2024-09-10T15:06:15+02:00"], ["9ae13b3d026", "fix(shopfloor): XAPPSF-667 taking the stop time right from the click (#20954)", "2024-09-10T15:04:44+02:00"], ["83dc131f35a", "fix(deps): update typescript-es<PERSON> monorepo to v7.18.0 (#20589)", "2024-09-10T14:02:36+02:00"], ["073d39fea40", "feat(automationcrew): XT-78287-maintenance (#20943)", "2024-09-10T14:55:29+03:00"], ["59ff6160479", "fix: XT-64508 Fix doc path (#20958)", "2024-09-10T12:06:33+02:00"], ["7c800f31ad4", "fix(manufacturing): XT-78138 repost production and material tracking (#20947)", "2024-09-10T10:48:55+02:00"], ["d1963278a92", "feat(manufacturing): XT-70651 unique work order number (#20952)", "2024-09-10T09:44:34+02:00"], ["8e869a4bb62", "fix(sales): XT-77688-changes made in allLinesAreAllocated (#20933)", "2024-09-10T11:26:47+05:30"], ["9e0d3f2ba7d", "fix(deps): update dependency body-parser to v1.20.3 (#20950)", "2024-09-10T07:32:10+02:00"], ["638092c605d", "chore(deps): update pnpm to v9.10.0 (#20951)", "2024-09-10T07:32:05+02:00"], ["77cda174330", "chore(deps): update dependency node-mocks-http to v1.16.0 (#20949)", "2024-09-10T00:32:17+02:00"], ["2c4ed37f440", "feat(shopfloor): XT-76340 update shopfloor x3 (#20891)", "2024-09-09T23:05:21+01:00"], ["97756098fc0", "fix(deps): update dependency ts-graphviz to v2.1.3 (#20948)", "2024-09-10T00:02:47+02:00"], ["68ab5e90b31", "chore: updated docker pnpm-lock.yaml files", "2024-09-09T21:46:32+00:00"], ["158746c29c9", "chore: commit upgrades", "2024-09-09T21:27:58+00:00"], ["4c1b1419abd", "chore: bump patch version", "2024-09-09T20:04:20+00:00"], ["4386f62c669", "fix: incomplete sanitization XT-78266 (#20927)", "2024-09-09T18:14:09+02:00"], ["a7351ac22af", "chore: progress on root package cleanup (#20940)", "2024-09-09T18:13:24+02:00"], ["0eb79b72596", "feat(reference): xt-78287 (#20942)", "2024-09-09T18:56:30+03:00"], ["4d9ab815f97", "fix: XT-77443 implement access check on export (#20914)", "2024-09-09T08:41:14-07:00"], ["62c290093f2", "feat(xtrem-finance): XT-74180 DATEV accounts export basic changes (#20893)", "2024-09-09T16:31:13+01:00"], ["572e3281939", "fix(xtrem-manufacturing): XT-70651 work orders with same number (#20816)", "2024-09-09T15:26:30+01:00"], ["1a24dc9547a", "feat(xtrem-finance): XT-74207 datev journal entry output node (#20936)", "2024-09-09T15:24:54+01:00"], ["4abb7c4fc2a", "feat(w3-stock): X3-319677 - Mutation now return Return only document id (#20889)", "2024-09-09T15:40:56+02:00"], ["299fc6c39e3", "feat(wh-stock): X3-261140 - WH Mobile Automation - View Stock by location (#20281)", "2024-09-09T15:11:01+02:00"], ["f17e19ec2a8", "feat: update subheader in ADC X3-319640 (#20929)", "2024-09-09T15:08:27+02:00"], ["66f161b1e3b", "fix: restore pnpm-lock (#20934)", "2024-09-09T14:40:46+02:00"], ["65ffdd7b103", "feat(xtrem-purchasing): XT-78172 Refactor purchase-requisition-table-panel (#20892)", "2024-09-09T13:03:46+03:00"], ["287b39e0e3e", "fix(supply-chain): XT-77877 Work order planning for items both manufactured and purchased (#20888)", "2024-09-09T10:59:30+02:00"], ["508c42a0852", "fix(purchasing): XT-77724 Purchase order creation from MRP calculation result (#20862)", "2024-09-09T10:56:32+02:00"], ["1da2fae5285", "refactor: XT-78276 do not export node.$.state outside of xtrem-core (#20909)", "2024-09-09T10:39:50+02:00"], ["23619d3d25c", "feat(supply-chain): XT-75380-stock-transfer-order-page (#20723)", "2024-09-09T10:11:30+02:00"], ["f188957c450", "feat(customField): fix filtering in custom field XT-76725 (#20882)", "2024-09-09T10:09:41+02:00"], ["4ddeeb4bb62", "chore(renovate): monorepo webdriverio dashboard approval", "2024-09-09T08:45:37+02:00"], ["0d8c4c3f97f", "feat(x3-stock): X3-300420 (#20707)", "2024-09-09T08:35:33+02:00"], ["4cffdc21fc4", "fix(deps): update dependency url-search-params-polyfill to v8 (#20922)", "2024-09-09T08:02:45+02:00"], ["fd3cb54086d", "fix(deps): update dependency showdown to v2 (#20916)", "2024-09-09T01:32:28+02:00"], ["005e9110cc6", "chore: updated docker pnpm-lock.yaml files", "2024-09-08T21:34:40+00:00"], ["a7834da7262", "chore: commit upgrades", "2024-09-08T21:16:22+00:00"], ["ae226632df3", "chore: bump patch version", "2024-09-08T20:04:22+00:00"], ["91dd910a222", "fix(deps): update dependency i18n-js to v4 (#20869)", "2024-09-08T21:03:07+02:00"], ["5cbeedded87", "fix: sidebar close icon XT-78135 (#20886)", "2024-09-08T20:32:22+02:00"], ["f14a3ecc68b", "fix(deps): update dependency file-type to v19.5.0 (#20915)", "2024-09-07T23:30:53+02:00"], ["86770eacf35", "chore: updated docker pnpm-lock.yaml files", "2024-09-07T21:22:57+00:00"], ["aab85f02967", "chore: commit upgrades", "2024-09-07T21:06:38+00:00"], ["3dd3b87c32a", "chore: bump patch version", "2024-09-07T20:04:22+00:00"], ["b0636f906fc", "fix(deps): update dependency rxjs to v7 (#20913)", "2024-09-07T20:31:18+02:00"], ["26449537be2", "feat(apps): endpoint ping button XT-78181 (#20865)", "2024-09-07T17:28:32+02:00"], ["651339c857f", "fix: XT-76752 random timestamp precision error in tests (#20910)", "2024-09-07T16:40:14+02:00"], ["5965eb27a99", "fix(deps): update dependency puppeteer to v23 (#20907)", "2024-09-07T15:01:08+02:00"], ["3876ec64296", "fix(deps): update dependency newrelic to v12 (#20906)", "2024-09-07T11:01:14+02:00"], ["9e1c7c8eee2", "fix: report template filter value not aligned XT-78132 (#20887)", "2024-09-07T10:57:31+02:00"], ["a63f736fd97", "fix(deps): update dependency mssql to v11 (#20905)", "2024-09-07T10:31:09+02:00"], ["9e9fbbbd5f1", "chore: remove invisible char in renovate config", "2024-09-07T09:27:24+02:00"], ["51404af68c6", "fix(deps): update dependency lighthouse to v12.2.1 (#20900)", "2024-09-07T08:31:39+02:00"], ["eff25ab93cf", "chore(deps): update dependency @chromatic-com/storybook to v1.9.0 (#20901)", "2024-09-07T08:31:34+02:00"], ["3781738b75d", "fix(deps): update dependency minimatch to v10 (#20904)", "2024-09-07T08:31:27+02:00"], ["3b1528194b0", "docs: XT-69536 unit test order by _factory.title (#19727)", "2024-09-06T23:44:35+02:00"], ["ae1b96735bb", "chore: updated docker pnpm-lock.yaml files", "2024-09-06T21:27:14+00:00"], ["38aa08194d6", "chore: commit upgrades", "2024-09-06T21:11:03+00:00"], ["4447e5d2bc3", "chore: bump patch version", "2024-09-06T20:08:03+00:00"], ["ab00729728f", "feat: XT-78265 node.$.factory API (#20898)", "2024-09-06T20:11:54+02:00"], ["02fff9133a1", "fix: XT-76752 convert postgres SQL timestamptz with millisecond precision (#20846)", "2024-09-06T11:01:55-07:00"], ["7bfc1f77d41", "perf: XT-78258 speed up TS compilation by simplifying PropertyPaths type definition (#20897)", "2024-09-06T19:29:18+02:00"], ["3e724704e24", "fix: XT-78239 delete schedule entry for content addressable GC (#20881)", "2024-09-06T18:39:15+02:00"], ["963f1c15642", "fix: turbo config to account for global pnpm-lock.yaml file (#20885)", "2024-09-06T18:33:30+02:00"], ["4209780644c", "feat: XT-77777 audit triggers (#20838)", "2024-09-06T18:32:48+02:00"], ["d8bed313fd2", "feat(finance): XT-71865 new feature file (#20895)", "2024-09-06T18:20:38+02:00"], ["6eff289214b", "fix(stock): XT-77838 NaN value when a variance is 0 (#20791)", "2024-09-06T17:33:51+02:00"], ["7e2d70e987b", "fix(deps): update dependency debug to v4.3.7 (#20871)", "2024-09-06T17:31:57+02:00"], ["9b7b1ac5f20", "feat(finance): XT-71865 data-creation (#20890)", "2024-09-06T17:25:17+02:00"], ["badbf577d25", "chore: review global hash to include full pnpm-lock.yaml (#20884)", "2024-09-06T15:40:34+02:00"], ["6053dca2b24", "feat(w3-stock): X3-319665 - Mutation now return Return only document id (#20879)", "2024-09-06T15:11:15+02:00"], ["283bdc3a831", "feat(purchasing): XT-77763 Refactor purchase-requisition file (#20763)", "2024-09-06T15:57:44+03:00"], ["10dc0e7a5d6", "feat: XT-78178 polymorphic collection data in context.create (#20873)", "2024-09-06T14:57:04+02:00"], ["b83b679f753", "fix(x3-stock): X3-319524 (#20857)", "2024-09-06T14:41:51+02:00"], ["5df62c74a72", "fix: XT-78016 fix cascaded deferred saves (#20859)", "2024-09-06T14:34:01+02:00"], ["839be77c957", "feat(xtrem-purchasing): XT-76820 Purchase-return post comment fixes (#20761)", "2024-09-06T15:13:42+03:00"], ["6f48e1fcb4a", "fix(deps): update dependency lighthouse to v12 (#20872)", "2024-09-06T12:32:00+02:00"], ["a6407d7ce22", "feat: XT-76001 <PERSON><PERSON> duplicatedValue override (#20737)", "2024-09-06T12:09:30+02:00"], ["bebf93aa1f7", "fix: revert gms-chat-ui lib to 1.3 (#20874)", "2024-09-06T11:49:17+02:00"], ["5b17901d1b7", "fix: turbo config to account for package.json & pnpm-lock.yaml files (#20876)", "2024-09-06T11:48:50+02:00"], ["564220798dc", "feat(automationcrew): XT-78080 maintenance (#20856)", "2024-09-06T12:22:40+03:00"], ["188e78cf1f8", "fix(purchasing): XT-69961Unit-of-measure-not-displaying (#20681)", "2024-09-06T14:37:57+05:30"], ["8bcc0ae6a44", "feat(xtrem-x3-inventory): X3-312991 (#20817)", "2024-09-06T10:57:25+02:00"], ["b193fab0d23", "fix(xtrem-cli-atp): XT-77436 workaround to fix pipeline issue with xtrem-cli-dev (#20858)", "2024-09-06T09:45:04+01:00"], ["df3774a7b7d", "feat: bulkaction and table options telemetry calls are updated XT-77206 (#20821)", "2024-09-06T09:38:57+02:00"], ["80ed6fda782", "fix(manufacturing): XT-77691 wrong time unit (#20814)", "2024-09-06T09:36:01+02:00"], ["5d50835e5a8", "fix: compilation issues on reporting (#20875)", "2024-09-06T09:33:36+02:00"], ["0c290bd107d", "fix: XT-78183 update of _updateXxx columns in root table (#20866)", "2024-09-06T07:19:10+02:00"], ["d3b63d6f13f", "fix(deps): update dependency eslint-plugin-unused-imports to v4 (#20867)", "2024-09-06T02:32:31+02:00"], ["20537013438", "fix(deps): update dependency glob to v11 (#20868)", "2024-09-06T02:06:14+02:00"], ["fe93df20c03", "fix(deps): update dependency @xmldom/xmldom to v0.9.2 (#20863)", "2024-09-06T00:02:59+02:00"], ["48844219d48", "fix(deps): update dependency framer-motion to v11.5.4 (#20864)", "2024-09-06T00:02:55+02:00"], ["a183481c6c4", "chore: updated docker pnpm-lock.yaml files", "2024-09-05T21:29:14+00:00"], ["353b13e2742", "chore: commit upgrades", "2024-09-05T21:11:22+00:00"], ["1407da814be", "chore: bump patch version", "2024-09-05T20:04:45+00:00"], ["f43bc023030", "feat(automationcrew): XT-59739-automation-dept-and-data (#20850)", "2024-09-05T21:36:19+03:00"], ["4117d56d433", "feat(finance): XT-78023 finance maintenance fix (#20841)", "2024-09-05T18:35:46+00:00"], ["aeb5e1c3f01", "chore: micromatch version bump (#20854)", "2024-09-05T19:49:39+02:00"], ["086f9c5bd35", "fix: print button alignment (#20851)", "2024-09-05T19:49:16+02:00"], ["128309df142", "feat: XT-78084 include _updateTick into factory.properties (#20847)", "2024-09-05T18:39:22+02:00"], ["f35168a3292", "fix(x3-stock): X3-318076 (#20799)", "2024-09-05T16:55:34+02:00"], ["a4b6ea6dc78", "feat(manufaturing): xt-78080 (#20852)", "2024-09-05T17:51:46+03:00"], ["d8eb4b05538", "fix(manufacturing): XT-77318 Work order created from a sales order line (#20770)", "2024-09-05T15:58:49+02:00"], ["a798c477f29", "feat(xtrem-sales): xt-77462 update eslint version and fix lint errors - 3rd batch (#20637)", "2024-09-05T16:54:00+03:00"], ["effc9658191", "fix(deps): update aws-sdk-js-v3 monorepo to v3.645.0 (#20840)", "2024-09-05T14:02:21+02:00"], ["a44e6175b5b", "feat: workflow editor undo-redo XT-77249 (#20643)", "2024-09-05T13:30:51+02:00"], ["f228fbed44e", "fix: error sending email test template XT-77833 (#20828)", "2024-09-05T13:05:54+02:00"], ["ef9060e9889", "feat(reference): xt-78080 (#20848)", "2024-09-05T13:02:50+03:00"], ["9090ff47ec5", "chore(deps): update dependency sass to v1.78.0 (#20839)", "2024-09-05T11:02:23+02:00"], ["1752524ae1b", "fix(xtrem-cli-atp): XT-77420 fixed issue 'querySelectorAll' error on 'parentElement()' (#20733)", "2024-09-05T10:58:01+03:00"], ["dce9db2b6f1", "fix(deps): update dependency framer-motion to v11.5.2 (#20844)", "2024-09-05T08:02:52+02:00"], ["820efd2c1f7", "chore(deps): update dependency @faker-js/faker to v9 (#20845)", "2024-09-05T08:02:47+02:00"], ["98ab41ae99d", "chore(deps): update dependency @types/node to v20.16.5 (#20842)", "2024-09-05T07:32:43+02:00"], ["9c8113747bb", "fix(deps): update dependency eslint-plugin-import to v2.30.0 (#20843)", "2024-09-05T07:32:39+02:00"], ["cd679da0416", "fix(deps): update dependency eslint-plugin-unicorn to v55 (#20782)", "2024-09-05T00:32:42+02:00"], ["abfc9005e24", "chore(deps): update dependency tsd to v0.31.2 (#20836)", "2024-09-05T00:32:38+02:00"], ["766892271df", "chore(deps): update dependency eslint-plugin-jsx-a11y to v6.10.0 (#20837)", "2024-09-05T00:32:34+02:00"], ["9b360d75b62", "chore: updated docker pnpm-lock.yaml files", "2024-09-04T22:02:30+00:00"], ["9756dc0d8e3", "chore: commit upgrades", "2024-09-04T21:40:07+00:00"], ["f1a3f52ad39", "chore: bump patch version", "2024-09-04T20:04:42+00:00"], ["83fa50e2d6f", "fix: XT-77523 Refactor using readline, pause and resume on drain (#20833)", "2024-09-04T21:00:41+02:00"], ["c243491d6ad", "fix: X3-319578 allocate the factory on external manager earlier (#20822)", "2024-09-04T20:26:35+02:00"], ["44845baa117", "feat(apps): timeout of interop graphql app to 120sec XT-74719 (#20824)", "2024-09-04T19:12:14+02:00"], ["2596d1e3b4a", "chore(deps): cleanup root package - request, webdriverio (#20829)", "2024-09-04T18:07:25+02:00"], ["53df7aee163", "feat(wh-stock-data): X3-304144 (#20302)", "2024-09-04T17:52:22+02:00"], ["256ecba28eb", "feat(apps): track records sync failures XT-72576 (#20804)", "2024-09-04T17:45:13+02:00"], ["9b610726237", "feat(shopfloor): XAPPSF-645-clock-out-incorporate-sync-transfer-time-tracking (#20831)", "2024-09-04T16:39:02+01:00"], ["0007c472db9", "fix: regex variables in reports XT-78004 (#20825)", "2024-09-04T15:38:32+02:00"], ["cf01a557e19", "fix: XT-77795 performance refactoring on duplicate mutation and getDuplicate query (#20771)", "2024-09-04T15:34:56+02:00"], ["945eded91be", "feat(automationcrew): XT-77384-maintenance-new (#20826)", "2024-09-04T16:29:06+03:00"], ["1243d5f2969", "chore: add types to conditional editor table XT-76808 (#20815)", "2024-09-04T12:33:47+02:00"], ["1181ee072c5", "feat(xtrem-sales): XT-74599 Unbilled accounts receivable unit tests (#20773)", "2024-09-04T09:21:05+01:00"], ["94ce65afdb2", "feat(sales): get rid of flushDefferedActions [Performance] (#20615)", "2024-09-04T10:12:33+02:00"], ["cfc07143a30", "feat(xtrem-purchasing): XT-77545 Refactor purchase-return-matching file (#20698)", "2024-09-04T11:07:27+03:00"], ["aefdc776250", "fix(deps): update dependency @xmldom/xmldom to ^0.9.0 (#20712)", "2024-09-04T10:02:23+02:00"], ["e23964d82cc", "feat: add support for range filter parameters XT-77423 (#20798)", "2024-09-04T07:38:33+02:00"], ["fbc092fc7f5", "fix(deps): update dependency nodemailer to v6.9.15 (#20807)", "2024-09-04T07:32:32+02:00"], ["8af1fe38a89", "chore(deps): update dependency @types/node to v20.16.4 (#20812)", "2024-09-04T07:02:41+02:00"], ["81f26427e4e", "fix(deps): update dependency yaml to v2.5.1 (#20813)", "2024-09-04T07:02:37+02:00"], ["21ff536e9af", "chore(deps): update dependency eslint-plugin-react to v7.35.2 (#20810)", "2024-09-04T02:06:29+02:00"], ["e28498ab57b", "chore(deps): update dependency http-proxy-middleware to v3.0.2 (#20811)", "2024-09-04T02:06:25+02:00"], ["a2c3e54deb8", "fix: XT-74029 fix sql conversion non stored prop (#20806)", "2024-09-03T15:19:32-07:00"], ["2879e807cea", "chore(deps): update dependency webpack-dev-server to v5.1.0 (#20808)", "2024-09-03T23:32:34+02:00"], ["28389ee8c9f", "chore: updated docker pnpm-lock.yaml files", "2024-09-03T21:23:56+00:00"], ["255bea5dee3", "chore: commit upgrades", "2024-09-03T21:07:20+00:00"], ["7efebb395cd", "chore: bump patch version", "2024-09-03T20:04:21+00:00"], ["079b90be456", "feat: X3-317441 wh-services unit test config (#20731)", "2024-09-03T21:23:17+02:00"], ["39c74a9bd49", "fix: XT-77960 fix saving of log data (#20805)", "2024-09-03T21:18:18+02:00"], ["97cae4332a9", "feat(manufacturing): XT-77501 Improve work order cucumber test (#20795)", "2024-09-03T19:25:34+02:00"], ["639566faac0", "fix(xtrem-finance): XT-77853 demo layer updates for DATEV 2 (#20794)", "2024-09-03T18:02:54+02:00"], ["1f40b39b041", "fix(stock): xt-77948-automated-test-refactoring-rename-status-column-… (#20796)", "2024-09-03T18:41:37+03:00"], ["22e7d4ed0f6", "feat(distribtion): Manage line to line associations (#20740)", "2024-09-03T17:22:16+02:00"], ["9f63f5c7116", "chore(deps): update dependency c8 to v10 (#20591)", "2024-09-03T16:32:17+02:00"], ["46cf3bf6515", "test: XT-68880 automation tests (#20788)", "2024-09-03T15:29:18+02:00"], ["e3842d5adb1", "feat: overriding icons on ag-grid tables, carbon font update XT-77551 (#20784)", "2024-09-03T14:49:56+02:00"], ["568b798b709", "fix: XT-77523 Resolve on output stream finish event (#20757)", "2024-09-03T11:18:58+02:00"], ["637ec2efc62", "fix: XT-77766 upgrade fails on inherited index (#20769)", "2024-09-03T09:14:33+02:00"], ["0f78d82b752", "feat: add isServiceOptionEnabled to $ in order to check if the service is enabled XT-77103 (#20767)", "2024-09-03T08:25:12+02:00"], ["fdd3bee8d22", "feat(finance): XT-77356 finance maintenance fix (#20772)", "2024-09-03T06:14:55+00:00"], ["cad0835502c", "feat(flat-table): ensures dialogs are usable on tablet-sized devices XT-74169 (#20754)", "2024-09-03T08:05:52+02:00"], ["f9f9e925635", "chore(deps): update dependency http-proxy-middleware to v3.0.1 (#20774)", "2024-09-03T07:02:49+02:00"], ["0ae20c1dc52", "chore(deps): update dependency eslint-plugin-react to v7.35.1 (#20780)", "2024-09-03T07:02:41+02:00"], ["dac836db7c8", "fix(deps): update dependency @sage/gms-chat-ui to v1.10.2 (#20778)", "2024-09-03T02:06:18+02:00"], ["f3dd311631f", "fix: XT-77839 use paging when dequeuing messages and notifications (#20758) (#20776)", "2024-09-02T23:36:29+02:00"], ["d4e58c6f0fe", "fix(deps): update dependency @sage/bms-dashboard to v1.74.4 (#20748)", "2024-09-02T23:32:41+02:00"], ["b1d5f666be9", "fix(deps): update dependency axios to v1.7.7 (#20775)", "2024-09-02T23:32:35+02:00"], ["15bc37dfc81", "chore: updated docker pnpm-lock.yaml files", "2024-09-02T21:22:13+00:00"], ["a42f06f8007", "chore: commit upgrades", "2024-09-02T21:05:51+00:00"], ["fb1adc00378", "chore: bump patch version", "2024-09-02T20:04:18+00:00"], ["4cadc299df5", "fix: XT-77443 implement access check on property level (#20716)", "2024-09-02T12:50:22-07:00"], ["42dfaaa83b2", "feat: XT-77777 create xtrem-auditing package (#20744)", "2024-09-02T21:48:05+02:00"], ["48b76943270", "fix(xtrem-cli-atp): XT-77621 change relative path to deprecated pipelines / remove v2 header for tenant deletion (#20686)", "2024-09-02T21:02:33+02:00"], ["360104a52b5", "fix(deps): update dependency avatax to v24.8.2 (#20749)", "2024-09-02T21:02:20+02:00"], ["37dc1a1ccdc", "chore(deps): update non-major types dependencies (#20746)", "2024-09-02T20:02:21+02:00"], ["3298f7d9f79", "feat(wh-services): X3-261140 - update artefacts (#20752)", "2024-09-02T17:10:15+02:00"], ["66db380dfd1", "docs: add docs for canAddNewLine option XT-74844 (#20765)", "2024-09-02T17:03:10+02:00"], ["c96eb5cf66e", "feat(xtrem-x3-inventory): X3-315822 (#20766)", "2024-09-02T16:42:44+02:00"], ["687fd730a6e", "fix(deps): update dependency axios to v1.7.7 (#20747)", "2024-09-02T16:32:13+02:00"], ["a0bb9a1919e", "feat: add phantom row to nested grid XT-74844 (#20743)", "2024-09-02T15:42:36+02:00"], ["4bbcd881f61", "fix: X3-319501 change import reference shared to core in generation (#20759)", "2024-09-02T15:15:06+02:00"], ["4ae2b37c99e", "chore(deps): renovate dashbaord approval for webdriverio monorepo", "2024-09-02T14:51:06+02:00"], ["03aac3cbd38", "feat(supply-chain): XT-71718 add sequence number setup for stock transfer order and shipment (#20640)", "2024-09-02T15:24:18+03:00"], ["23fff7338d7", "feat: XT-72452 revert exclusive execution of node sync using global lock (#20755)", "2024-09-02T13:25:35+02:00"], ["4516f8e1f04", "fix: make ingredient page clean on save XT-75889 (#20751)", "2024-09-02T12:26:51+02:00"], ["ffd3432c128", "fix(manufacturing): XT-70991 Allocations tab in Stock detailed inquiry (#20734)", "2024-09-02T15:19:13+05:30"], ["ad6d66c8192", "feat(workflow): XT-77726 add notifies decorator (#20732)", "2024-09-02T10:00:17+01:00"], ["40716426a69", "fix(finance): XT-77095 added tax line table (#20500)", "2024-09-02T14:12:23+05:30"], ["019a2ae8294", "fix(company): XT-69376 - set active switch to on by default in business entity contact panel (#20660)", "2024-09-02T13:42:57+05:30"], ["2d7cc2b513d", "feat(distribution): XT-56453 sales notes propagation (#20738)", "2024-09-02T07:50:08+00:00"], ["39fb8ca3308", "feat(distribution): XT-60319 purchase notes propagation (#20739)", "2024-09-02T07:49:40+00:00"], ["c9b917ba244", "feat(apps): http url datatype XT-72576 (#20727)", "2024-09-02T09:35:41+02:00"], ["80c6061fa33", "feat(reference): XT-75381 datacreation (#20700)", "2024-09-02T09:31:08+02:00"], ["ea353beaf1d", "chore: updated docker pnpm-lock.yaml files", "2024-09-01T21:20:34+00:00"], ["7dd7d94fc64", "chore: commit upgrades", "2024-09-01T21:02:12+00:00"], ["d58de7027df", "chore: bump patch version", "2024-09-01T20:07:35+00:00"], ["871805ac988", "chore: updated docker pnpm-lock.yaml files", "2024-08-31T21:22:52+00:00"], ["3571e676eef", "chore: commit upgrades", "2024-08-31T21:04:45+00:00"], ["8bf156d9909", "chore: bump patch version", "2024-08-31T20:10:41+00:00"], ["18d28abdda3", "chore: updated docker pnpm-lock.yaml files", "2024-08-30T21:18:45+00:00"], ["7d2c5aa2068", "chore: commit upgrades", "2024-08-30T21:03:40+00:00"], ["1db48096209", "chore: bump patch version", "2024-08-30T20:07:46+00:00"], ["6c8adc8f4fc", "fix: XT-77764 inheritance of 'notifies' attribute (#20728)", "2024-08-30T17:23:00+02:00"], ["9c09c1808c2", "fix(manufacturing): XT-77501 test if level is undefined (#20720)", "2024-08-30T15:28:01+02:00"], ["5a5744e26d7", "fix: do not fetch nav panel items for duplication dialog XT-77779 (#20735)", "2024-08-30T15:25:51+02:00"], ["0704b2c5049", "fix: bulk header not appearing XT-76677 (#20736)", "2024-08-30T15:14:46+02:00"], ["59f28d31338", "feat: custom number filter on tables XT-77087 XT-58369 (#20722)", "2024-08-30T14:58:23+02:00"], ["8647d68576b", "feat(xtrem-cli-atp): XT-76180 verify a block is displayed (#20656)", "2024-08-30T14:54:11+02:00"], ["7992926de90", "fix: Date-time range title XT-76678 (#20730)", "2024-08-30T14:49:32+02:00"], ["bde79951fd6", "fix: Dialog showcase dropdown action XT-76448 (#20717)", "2024-08-30T14:22:32+02:00"], ["87bb29fc406", "feat: XT-75265 <PERSON> (#20697)", "2024-08-30T13:49:12+02:00"], ["aa0e380a45f", "feat(xtrem-intacct): XT-76741 hide the intacct activation service option (#20701)", "2024-08-30T12:13:56+01:00"], ["368cd9457c0", "feat: XT-72452 exclusive execution of node sync using global lock (#20704)", "2024-08-30T10:44:52+02:00"], ["d9dbbe74b11", "feat(shopfloor): XAPPSF-539-tracking-operator-refactoring (#20705)", "2024-08-30T09:34:16+01:00"], ["a0e4d86d9e8", "chore: revert sys-changelog when fixing csv layers (#20706)", "2024-08-30T10:14:14+02:00"], ["a8c76bd1581", "feat: custom number floating filter on tables XT-77087 (#20703)", "2024-08-30T09:51:47+02:00"], ["733790f16ae", "fix(intacct-finance): XT-77093 secure the creditLimit value when synchronizing with intacct (#20648)", "2024-08-30T09:08:28+02:00"], ["447870167d5", "fix(xtrem-purhasing): XT-77385 purchase credit memo link not working (#20662)", "2024-08-30T02:53:02+01:00"], ["600975f086f", "chore(deps): update dependency @chromatic-com/storybook to v1.8.0 (#20711)", "2024-08-30T02:06:00+02:00"], ["daf1ff78800", "fix(deps): update dependency framer-motion to v11.3.31 (#20710)", "2024-08-30T01:32:07+02:00"], ["63bd3a11961", "chore(deps): update dependency uglify-js to v3.19.3 (#20709)", "2024-08-29T23:32:16+02:00"], ["046a2eacb59", "chore: updated docker pnpm-lock.yaml files", "2024-08-29T21:15:34+00:00"], ["b59535b5419", "chore: commit upgrades", "2024-08-29T21:00:19+00:00"], ["ef3d054e1bb", "chore: bump patch version", "2024-08-29T20:04:14+00:00"], ["2c98a3bc039", "chore(deps): update dependency @testing-library/react to v16.0.1 (#20708)", "2024-08-29T21:31:46+02:00"], ["899596d8187", "fix: x3 unit tests dup ip address (#20672)", "2024-08-29T18:27:29+02:00"], ["92a1a14c9b5", "fix: XT-75717 dependsOn typing on property overrides (#20702)", "2024-08-29T18:01:56+02:00"], ["26a9380006b", "fix: XT-77523 Fix line based stream processing (#20690)", "2024-08-29T15:57:24+02:00"], ["b8c36dad361", "fix(xtrem-cli-atp): XT-77063 -fix the timeout error when selecting row before scrolling (#20569)", "2024-08-29T15:35:45+03:00"], ["be6facf666b", "fix(xtrem-cli-atp): XT-77223 adding missing data value case (#20634)", "2024-08-29T15:32:37+03:00"], ["20110817ace", "fix(xtrem-manufacturing): XT-76916 Duplication of a work order with a text component gives error (#20689)", "2024-08-29T14:27:34+02:00"], ["e686e3f408e", "chore: remove mxx cli package (#20695)", "2024-08-29T13:58:53+02:00"], ["0d536cc2c17", "fix: add max length limit to dashboard title input XT-77594 (#20692)", "2024-08-29T13:54:23+02:00"], ["efc519c95b5", "fix: filter out binary stream properties from widget editor XT-77590 (#20693)", "2024-08-29T13:54:11+02:00"], ["40d04a8301d", "feat(xtrem-supply-chain): XT-77036 stock transfer order refactoring after transfer shipment base node refactoring (#20613)", "2024-08-29T13:23:49+02:00"], ["1e0dd326847", "feat(finance): XT-68686 feature files (#20696)", "2024-08-29T13:19:10+02:00"], ["2743b12c989", "feat: XT-72452 add app to sync state (#20664)", "2024-08-29T12:26:52+02:00"], ["352865420b6", "feat(finance): XT-68686 data creation (#20683)", "2024-08-29T10:56:57+02:00"], ["25570fa8992", "fix(xtrem-cli-atp): XT-75711 sales return request - robot fails to scroll to end of panel (#20261)", "2024-08-29T08:45:18+02:00"], ["2832998e41b", "chore: updated translation files 20240828.1 (#20674)", "2024-08-29T08:02:31+02:00"], ["88a2950c4c7", "fix(deps): update cucumber (major) (#20679)", "2024-08-29T07:43:37+02:00"], ["95434631660", "fix(deps): update dependency @sage/intacct-controlid-provider to v3 (#20680)", "2024-08-29T07:02:34+02:00"], ["4c9c0708b04", "chore(deps): update dependency @types/pg to v8.11.8 (#20677)", "2024-08-29T02:06:11+02:00"], ["8bf1655feae", "feat(apps): purge sync history older than 2 weeks XT-75408 (#20666)", "2024-08-28T23:47:01+02:00"], ["65b1cfdc619", "fix(deps): update dependency @sage/visual-process-editor to v1.11.6 (#20675)", "2024-08-28T23:32:03+02:00"], ["33da36831f7", "chore: updated docker pnpm-lock.yaml files", "2024-08-28T21:24:21+00:00"], ["5452c2fdb90", "chore: commit upgrades", "2024-08-28T21:09:17+00:00"], ["a49fca752f9", "chore: bump patch version", "2024-08-28T20:07:48+00:00"], ["a5274dd1610", "fix: XT-77523 <PERSON><PERSON> from dump delete files while processing (#20671)", "2024-08-28T20:16:54+02:00"], ["06e1607b8a7", "fix: downgrade chatbot client lib XT-77514 (#20669)", "2024-08-28T18:59:01+02:00"], ["50db10866b1", "fix: custom select fields on form designer tables XT-77571 (#20667)", "2024-08-28T18:31:17+02:00"], ["3814098586a", "feat(purchasing): XT-76820 Refactor purchase-return file (#20387)", "2024-08-28T17:25:16+03:00"], ["e3846a49276", "fix(stock): XT-76932 fix sub-assembly required quantity (#20421)", "2024-08-28T14:17:34+01:00"], ["37f759199ac", "fix: remove page dialog values XT-76448 (#20655)", "2024-08-28T14:46:16+02:00"], ["2470e9ca7c9", "fix: table action color highlight fix (#20661)", "2024-08-28T14:44:39+02:00"], ["dd4393e49d7", "feat: improve cloudbeaver setup (#20645)", "2024-08-28T14:21:10+02:00"], ["63db887e378", "fix: csp to allow youtube for pendo XT-77513 (#20659)", "2024-08-28T13:57:04+02:00"], ["fdd83cada8a", "fix(xtrem-cli-atp): XT-75649 interact with multi action button (#20510)", "2024-08-28T13:01:20+02:00"], ["1928276c14f", "feat(xtrem-sales): xt-77462 update eslint version and fix lint errors - 2nd batch (#20636)", "2024-08-28T13:11:08+03:00"], ["9e201c27f8e", "feat(xtrem-sales): xt-77462 update eslint version and fix lint errors -1st batch (#20635)", "2024-08-28T13:09:39+03:00"], ["acf102cdae3", "fix(stock): XT-73292 stock receipt post lock (#20624)", "2024-08-28T11:25:21+02:00"], ["a1b787e3da4", "feat: XT-75479 ServiceOption change callbacks (#20447)", "2024-08-28T10:47:24+02:00"], ["ad7bc899a0a", "fix(reporting): XT-65361 header-body-overlapping (#20610)", "2024-08-28T13:53:42+05:30"], ["58b854aff7f", "chore(deps): update non-major types dependencies (#20653)", "2024-08-28T09:32:06+02:00"], ["7f7e3de8d0f", "feat(getDefaults): remove logger on defaultValue error XT-74726 (#20229)", "2024-08-28T08:57:16+02:00"], ["a6d142d802b", "fix(x3-stock): X3-318905 (#20464)", "2024-08-28T08:51:35+02:00"], ["83e759f79dd", "chore(deps): update dependency @testing-library/dom to v10.4.0 (#20654)", "2024-08-28T07:02:24+02:00"], ["38a4bc17036", "chore(deps): update dependency @types/chai to v4.3.19 (#20651)", "2024-08-28T02:07:02+02:00"], ["f27ad11bec6", "chore(deps): update testing-library (major) (#20652)", "2024-08-28T02:06:49+02:00"], ["2b7b03c5356", "chore(deps): update dependency tsx to v4.19.0 (#20649)", "2024-08-27T23:32:28+02:00"], ["3d8e6d7d6b1", "chore(deps): update dependency webpack-merge to v6 (#20650)", "2024-08-27T23:32:24+02:00"], ["8805e892d21", "chore: updated docker pnpm-lock.yaml files", "2024-08-27T21:15:35+00:00"], ["22da18998d3", "chore: commit upgrades", "2024-08-27T21:00:34+00:00"], ["3ef82a1c4cd", "chore: bump patch version", "2024-08-27T20:04:20+00:00"], ["054bbd88477", "feat(shopfloor): XAPPSF-528 Generate new sync tracking document (#20631)", "2024-08-27T19:10:47+01:00"], ["13cddf17f89", "feat: XT-76340 loosen controls on order by and filter property mappings and apply paging options on raw results (#20626)", "2024-08-27T19:09:07+02:00"], ["dcf24695883", "feat: add add line button to mobile table XT-68880 (#20482)", "2024-08-27T17:23:05+02:00"], ["43168543bad", "feat(xtrem-stock): XT-68114 stock-issue-page (#20485)", "2024-08-27T16:58:58+02:00"], ["85af938fc4d", "feat(tc): APINODGEN - Transient property not set to true (#20642)", "2024-08-27T15:44:27+02:00"], ["de27a57b26e", "feat(xtrem-cli-atp): XT-76567 title subTitle page verification (#20622)", "2024-08-27T12:20:23+02:00"], ["a2519c16750", "feat(purchasing): XT-76783 Refactor unbilled-account-payable-inquiry file (#20344)", "2024-08-27T13:04:23+03:00"], ["3f316a6fc9b", "feat: chain step deletion from workflow editor XT-77248 (#20550)", "2024-08-27T11:26:13+02:00"], ["abc1234ae5c", "feat: upgrade ag-grid to v32.1.0 XT-75075 (#20449)", "2024-08-27T11:10:04+02:00"], ["74193db6137", "feat(xtrem-stock): xt-77002 fix lint errors on xtrem stock - 1st batch (#20611)", "2024-08-27T11:29:16+03:00"], ["9220370e72f", "feat(manufacturing): XT-76340 update sdmo for shopfloor (#20424)", "2024-08-27T09:25:58+01:00"], ["351e8e5933e", "feat(test): XT-77379 fix accessibility test (#20620)", "2024-08-27T09:29:28+02:00"], ["8807fdf34c1", "chore(deps): update dependency @types/chai to v4.3.18 (#20627)", "2024-08-27T07:26:37+02:00"], ["cb4783c6c03", "chore(deps): update dependency ts-essentials to v10 (#20629)", "2024-08-27T07:20:46+02:00"], ["c0657b83257", "chore(deps): update dependency ts-morph to v23 (#20630)", "2024-08-27T01:31:51+02:00"], ["d8799485cfe", "chore: updated docker pnpm-lock.yaml files", "2024-08-26T23:17:02+00:00"], ["5a94b294325", "chore: commit upgrades", "2024-08-26T23:06:25+00:00"], ["19e49b3db5b", "chore: bump patch version", "2024-08-26T22:05:10+00:00"], ["eba18ebbfbe", "chore(deps): revert ATP deps updates (#20625)", "2024-08-26T22:47:10+02:00"], ["52568a2f88e", "fix(manufacturing): XT-77112 fix work order component description (#20487)", "2024-08-26T20:21:25+02:00"], ["6b790eec84f", "fix(master): XT-76097 Sequence number is active (#20549)", "2024-08-26T18:01:15+02:00"], ["92328b3a7c3", "fix(core): fixes duplicate issue on subnoding XT-75846 (#20383)", "2024-08-26T17:50:24+02:00"], ["1b06fbcde8d", "fix: X3-316202 - display with unit's number of decimals (#20137)", "2024-08-26T17:18:50+02:00"], ["bead6024e67", "fix: sorting on deep computed properties XT-75167 (#20621)", "2024-08-26T16:35:55+02:00"], ["5b1e06bb8c7", "fix: not possible to add line from purchase receipt XT-75382 (#20619)", "2024-08-26T15:30:58+02:00"], ["f056b78f64a", "feat(apps): improve connector error handling XT-75401 (#20559)", "2024-08-26T14:52:20+02:00"], ["36c3260bc46", "chore: identify files raising an error 123 (#20561)", "2024-08-26T14:17:45+02:00"], ["a7f95667379", "chore(deps): remove overrides fast-xml-parser (#20616)", "2024-08-26T14:14:05+02:00"], ["d30f52fec2a", "chore(deps): update dependency supertest to v7 (#20608)", "2024-08-26T12:31:39+02:00"], ["9873d3f723e", "feat(tc): <PERSON><PERSON> into fallback joins branch (#20614)", "2024-08-26T11:35:39+02:00"], ["b0e16ef59a3", "fix(xtrem-cli-atp): XT-75831 added wait for element to exist before c… (#20509)", "2024-08-26T11:51:51+03:00"], ["ec059ae7c7a", "chore(guard): compare full version first (#20612)", "2024-08-26T10:43:18+02:00"], ["fa6343e16b5", "fix(xtrem-purchasing): XT-73827 purchase receipt origin link (#19914)", "2024-08-26T09:24:33+01:00"], ["90d01979206", "feat(x3-stock): X3-300416-qa-fix (#20366)", "2024-08-26T09:04:22+02:00"], ["ed10bb4db12", "chore(deps): update dependency style-loader to v4 (#20606)", "2024-08-26T07:31:55+02:00"], ["6bf18f74e33", "fix(deps): update dependency @sage/gms-chat-ui to v1.9.17 (#20607)", "2024-08-26T07:31:50+02:00"], ["9f9ec66dde4", "chore(deps): update dependency rimraf to v6 (#20605)", "2024-08-26T07:02:33+02:00"], ["3ff4aa9798a", "chore(deps): update dependency sinon to v18 (#20604)", "2024-08-26T02:05:49+02:00"], ["66fd4318336", "chore(deps): update dependency rimraf to v5.0.10 (#20603)", "2024-08-26T00:02:10+02:00"], ["4b1a1262d8a", "chore(deps): update dependency rimraf to v6 (#20601)", "2024-08-25T23:32:07+02:00"], ["9ccbe9a8194", "chore(deps): update dependency sass-loader to v16 (#20602)", "2024-08-25T23:32:03+02:00"], ["a863ac492ef", "chore: updated docker pnpm-lock.yaml files", "2024-08-25T21:19:21+00:00"], ["c30eb79183c", "chore: commit upgrades", "2024-08-25T21:03:17+00:00"], ["8bd009d092b", "chore: bump patch version", "2024-08-25T20:04:00+00:00"], ["0cfc9954ce4", "chore(deps): update dependency jsdom to v25 (#20599)", "2024-08-25T20:02:26+02:00"], ["c97ea13f7c2", "chore: updated translation files 20240823.2 (#20560)", "2024-08-25T18:40:33+02:00"], ["e7042bf455e", "chore(deps): update dependency fake-indexeddb to v6 (#20598)", "2024-08-25T16:32:00+02:00"], ["b61f24197d7", "chore(deps): update dependency jsdom to v24.1.3 (#20597)", "2024-08-25T16:01:52+02:00"], ["149406b747f", "chore(deps): update dependency jsdom to v24.1.2 (#20595)", "2024-08-25T13:32:03+02:00"], ["8e44d900bc7", "chore(deps): update dependency css-minimizer-webpack-plugin to v7 (#20594)", "2024-08-25T11:31:45+02:00"], ["8ea61b87e5a", "chore(deps): update dependency ts-patch to v3.2.1 (#20539)", "2024-08-25T10:13:10+02:00"], ["d6b724424c8", "fix(deps): update dependency tslib to v2.7.0 (#20585)", "2024-08-25T02:31:51+02:00"], ["f0370e4e897", "fix(deps): update dependency yaml to v2.5.0 (#20588)", "2024-08-25T02:31:46+02:00"], ["bf64c6b08a6", "fix(deps): update dependency winston to v3.14.2 (#20587)", "2024-08-25T02:04:55+02:00"], ["8b6f4f2830b", "fix(deps): update dependency wdio-cucumberjs-json-reporter to v5.2.1 (#20586)", "2024-08-25T01:31:17+02:00"], ["46895479a14", "chore(deps): update dependency tsx to v4.18.0 (#20583)", "2024-08-24T23:31:03+02:00"], ["8997d202410", "fix(deps): update dependency re2 to v1.21.4 (#20584)", "2024-08-24T23:30:59+02:00"], ["e6a2d5ab590", "chore: updated docker pnpm-lock.yaml files", "2024-08-24T21:13:38+00:00"], ["13b91359e8d", "chore: commit upgrades", "2024-08-24T20:58:49+00:00"], ["4aa7ef3e54a", "chore: bump patch version", "2024-08-24T20:03:36+00:00"], ["ef0e24cc5d1", "fix(deps): update dependency graphiql to v3.7.1 (#20580)", "2024-08-24T20:01:09+02:00"], ["14c7015eeb7", "chore(deps): update dependency http-proxy-middleware to v3 (#20582)", "2024-08-24T19:01:12+02:00"], ["23198df064b", "fix(deps): update dependency monaco-editor to ^0.51.0 (#20579)", "2024-08-24T18:31:11+02:00"], ["d76ceac53a6", "fix(deps): update dependency oracledb to v6.6.0 (#20581)", "2024-08-24T18:31:04+02:00"], ["910b1bf73d3", "fix(deps): update dependency graphiql to v3.7.0 (#20578)", "2024-08-24T17:30:47+02:00"], ["1093fb30936", "fix(deps): update dependency file-type to v19.4.1 (#20577)", "2024-08-24T14:31:18+02:00"], ["64c4f99c02f", "fix(deps): update dependency esquery to v1.6.0 (#20576)", "2024-08-24T14:01:16+02:00"], ["8751b6b02e2", "fix(deps): update dependency eslint-plugin-mocha to v10.5.0 (#20575)", "2024-08-24T12:31:03+02:00"], ["4e311ec0b86", "fix(deps): update dependency core-js to v3.38.1 (#20574)", "2024-08-24T11:01:02+02:00"], ["aebb6047aac", "fix(deps): update dependency @sage/gms-chat-ui to v1.9.13 (#20572)", "2024-08-24T08:31:05+02:00"], ["748600c3f5c", "fix(deps): update dependency @sage/bms-dashboard to v1.74.1 (#20571)", "2024-08-24T07:10:20+02:00"], ["bbeceee1cbb", "feat: document assignment and print button on pages XT-75167 (#20475)", "2024-08-24T07:09:40+02:00"], ["aaf78449fbd", "fix(deps): update dependency @cucumber/cucumber to v10.9.0 (#20570)", "2024-08-24T07:01:29+02:00"], ["f0e8f4408d3", "fix(deps): update dependency avatax to v24.6.4 (#20573)", "2024-08-24T07:01:14+02:00"], ["849ea3aaf0d", "fix(deps): update dependency framer-motion to v11.3.30 (#20567)", "2024-08-24T02:04:12+02:00"], ["457c7c17a76", "chore(deps): update dependency @testing-library/jest-dom to v6.5.0 (#20568)", "2024-08-24T01:31:46+02:00"], ["a6275f85727", "chore: update pnpm to v9.8.0 (#20563)", "2024-08-24T00:06:36+02:00"], ["a1a91b56c1f", "chore(deps): update dependency ts-jest to v29.2.5 (#20565)", "2024-08-23T23:31:55+02:00"], ["cade0f13cb9", "fix(deps): update dependency axios to v1.7.5 (#20566)", "2024-08-23T23:31:51+02:00"], ["d8e65a378d0", "chore: updated docker pnpm-lock.yaml files", "2024-08-23T21:16:24+00:00"], ["48548c4b701", "chore: commit upgrades", "2024-08-23T21:01:03+00:00"], ["5c4247f053d", "chore: bump patch version", "2024-08-23T20:04:38+00:00"], ["9c042904f97", "fix: XT-73624 special cases for _id (#20562)", "2024-08-23T11:35:53-07:00"], ["ecd2e27b6b7", "feat: X3-319163 improve fallback join generation (#20564)", "2024-08-23T20:24:40+02:00"], ["31b25449f40", "feat(finance): XT-77259 small adjustments (#20527)", "2024-08-23T17:24:22+00:00"], ["537970c63c8", "feat: XT-76340 X3 _id natural key filter (#20558)", "2024-08-23T18:28:19+02:00"], ["1712f950a56", "feat(distribution): XT-74991-fix-base-line-to-base-outbound-line-properties (#20552)", "2024-08-23T16:13:50+02:00"], ["b818460e4bd", "feat(xtrem-sales): XT-72777 Fixed remaining comments from previous PR (#20224)", "2024-08-23T15:43:03+03:00"], ["421c2927834", "feat(xtrem-sales): xt-76096 fix lint errors on xtrem sales - 3rd batch (#20337)", "2024-08-23T15:42:44+03:00"], ["049cff9725a", "feat(apps-error-handling): enhance logMessage XT-75401 (#20503)", "2024-08-23T14:36:23+02:00"], ["47234f3f5bd", "feat(xtrem-cli-atp): XT-70937 - add missing parameters (#20553)", "2024-08-23T14:28:22+02:00"], ["ad58751b4d8", "fix(uxcrew): XT-77197 error on category tunnel item page (#20537)", "2024-08-23T13:35:03+02:00"], ["1b7e1d60b1c", "feat(xtrem-stock-data): XT-77209-quality-control-item-site (#20521)", "2024-08-23T13:31:58+02:00"], ["bddbfae188c", "feat(xtrem-cli-atp): XT-76322 yml parameters missing (#20548)", "2024-08-23T12:59:40+02:00"], ["b0eadc0f596", "feat(finance): XT-68686 expense revenue account by tax (#20551)", "2024-08-23T12:20:48+02:00"], ["d38218b6088", "chore(deps): update dependency webpack to v5.94.0 (#20544)", "2024-08-23T11:02:17+02:00"], ["e9ba79e4072", "chore(deps): update non-major types dependencies (#20545)", "2024-08-23T11:02:11+02:00"], ["e5875875496", "feat(shopfloor): XAPPSF-527-clock-out-operator-disconnect-logged-in-user (#20530)", "2024-08-23T09:45:33+01:00"], ["74deb89d4df", "feat: auto setup turbo and cloudbeaver (#20538)", "2024-08-23T09:45:08+02:00"], ["8f2606a27e9", "fix: workflow component improvements XT-76833 XT-76834 (#20498)", "2024-08-23T08:47:34+02:00"], ["b11b891c46a", "feat: improved file deposit component that supports read only mode XT-77079 (#20529)", "2024-08-23T08:47:15+02:00"], ["0725a16d1d0", "feat: XT-77213 review print-document workflow action (#20501)", "2024-08-23T08:31:19+02:00"], ["5b3abf2cf8a", "fix(deps): update aws-sdk-js-v3 monorepo to v3.637.0 (#20547)", "2024-08-23T08:02:14+02:00"], ["43621cf12bb", "chore(deps): update storybook monorepo to v8.2.9 (#20546)", "2024-08-23T07:31:41+02:00"], ["0e5805e2f8c", "chore(deps): update dependency eslint-plugin-jsx-a11y to v6.9.0 (#20514)", "2024-08-23T02:05:53+02:00"], ["295bede67b3", "chore(deps): update dependency uglify-js to v3.19.2 (#20542)", "2024-08-23T02:05:46+02:00"], ["b884cf3ce8a", "chore(deps): update dependency v8-to-istanbul to v9.3.0 (#20543)", "2024-08-23T01:31:30+02:00"], ["ba0f54deab0", "chore(deps): update dependency tsx to v4.17.0 (#20540)", "2024-08-22T23:31:51+02:00"], ["5bedb075e6d", "chore: updated docker pnpm-lock.yaml files", "2024-08-22T21:23:08+00:00"], ["f5b71454159", "chore: commit upgrades", "2024-08-22T21:04:27+00:00"], ["7a89ea2a2d8", "chore: bump patch version", "2024-08-22T20:04:42+00:00"], ["307d2690a78", "feat(finance): XT-77154 small refactoring due to refresh of the screen (#20505)", "2024-08-22T19:13:19+00:00"], ["92c77527944", "fix: prevent sorting of children of computed objects XT-75414 (#20502)", "2024-08-22T21:10:14+02:00"], ["30a891ea7d6", "fix(xtrem-cli-atp): XT-58851 - Timeout management - refactoring (#20271)", "2024-08-22T20:05:08+02:00"], ["671e1823696", "feat(xtrem-cli-atp): XT-76814 store the value of a hidden field (#20457)", "2024-08-22T17:54:44+02:00"], ["ca0cc69aae2", "docs: Remove query example from GraphiQL X3-314535 (#20526)", "2024-08-22T17:46:10+02:00"], ["d66573689b8", "fix(xtrem-cli-atp): XT-75744 adding if condition to verify that actio… (#20235)", "2024-08-22T18:16:20+03:00"], ["503f0048b5c", "fix: passing on record context to lookup dialog XT-77135 (#20523)", "2024-08-22T16:52:56+02:00"], ["e16b770744a", "feat(supply-chain): XT-71714-stock-transfer-shipment-nodes (#20455)", "2024-08-22T17:30:44+03:00"], ["0a0c1931036", "feat: XT-72452 stop sync of frozen properties (#20528)", "2024-08-22T16:21:27+02:00"], ["fa8f5a2ef4d", "feat: XT-76340 fixes on xtrem-app-metadata (#20531)", "2024-08-22T16:17:38+02:00"], ["9e26357682d", "feat(xtrem-stock): XT-77176-delete-stock-receipt (#20507)", "2024-08-22T15:52:30+02:00"], ["68760faa6da", "feat(xtrem-sales): xt-72777 fix lint errors on xtrem sales - 2nd batch (#20143)", "2024-08-22T16:06:10+03:00"], ["7bcdbfdf3a1", "chore: updated docker pnpm-lock.yaml files", "2024-08-22T12:25:08+00:00"], ["fc7f103f9c7", "chore: commit upgrades", "2024-08-22T12:08:30+00:00"], ["f005ed4f10f", "chore: bump patch version", "2024-08-22T11:15:16+00:00"], ["2a166205130", "chore: updated docker pnpm-lock.yaml files", "2024-08-22T10:53:46+00:00"], ["d37f77cbd37", "shopfloor-main: manual empty file to jump from 46.0.38 to 47.0.0", "2024-08-22T10:10:55+00:00"], ["fbd773ef37c", "xtrem-show-case: manual empty file to jump from 46.0.38 to 47.0.0", "2024-08-22T10:10:52+00:00"], ["1ad097456d7", "xtrem-glossary: manual empty file to jump from 46.0.38 to 47.0.0", "2024-08-22T10:10:48+00:00"], ["95a40bd3469", "xtrem-services-main: manual empty file to jump from 46.0.38 to 47.0.0", "2024-08-22T10:10:44+00:00"], ["a96db100988", "chore: bump major version", "2024-08-22T10:10:39+00:00"], ["c31a3a54053", "chore: check node version on postinstall (#20522)", "2024-08-22T11:49:04+02:00"], ["33bf80d8fbb", "chore: unlock pr failure (#20525)", "2024-08-22T11:42:05+02:00"], ["397922aee22", "chore(deps): update dependency node-mocks-http to v1.15.1 (#20518)", "2024-08-22T10:32:30+02:00"], ["807711e867b", "chore: updated docker pnpm-lock.yaml files", "2024-08-22T07:36:51+00:00"], ["d5851366e0a", "chore: commit upgrades", "2024-08-22T07:22:04+00:00"], ["e4ef9a02b34", "chore: bump patch version", "2024-08-22T06:27:51+00:00"], ["441b0ec253e", "chore(deps): update dependency mocha to v10.7.3 (#20516)", "2024-08-22T07:32:49+02:00"], ["3e8153e7630", "chore(deps): update dependency node to v20.17.0 (#20517)", "2024-08-22T07:32:43+02:00"], ["4d46d411e0d", "chore(deps): update dependency ts-jest to v29.2.4 (#20519)", "2024-08-22T07:02:57+02:00"], ["3ec7fc59183", "chore(deps): update dependency mime-db to v1.53.0 (#20515)", "2024-08-22T02:06:46+02:00"], ["21e81cedf75", "chore(deps): update dependency allure-commandline to v2.30.0 (#20494)", "2024-08-21T23:32:55+02:00"], ["0173558b108", "fix(deps): update dependency framer-motion to v11.3.29 (#20511)", "2024-08-21T23:32:50+02:00"], ["66c04025971", "chore(deps): update dependency chai to v4.5.0 (#20512)", "2024-08-21T23:32:44+02:00"], ["0635cc359cf", "chore: updated docker pnpm-lock.yaml files", "2024-08-21T21:10:50+00:00"], ["aacd5470978", "chore: commit upgrades", "2024-08-21T20:56:17+00:00"], ["dfd07a5d601", "chore: bump patch version", "2024-08-21T20:03:55+00:00"], ["29d6a4587bf", "fix(deps): update dependency nodemailer to v6.9.14 (#20431)", "2024-08-21T19:32:28+02:00"], ["594beb74c9e", "chore(deps): update aws-sdk to v3.635.0 and dedup (#20506)", "2024-08-21T17:40:49+02:00"], ["f924b73bd60", "fix: fix not click row card if popover is clicked (#20504)", "2024-08-21T16:30:56+02:00"], ["a7b7d19944d", "fix: XT-76230 Resolve on end callback (#20508)", "2024-08-21T16:10:36+02:00"], ["aa093e18e40", "fix: XT-77164 takeOne conversion to SQL (#20496)", "2024-08-21T11:26:03+02:00"], ["c3e2a93b15e", "feat: XT-77070 fix remote interop package name (#20484)", "2024-08-21T09:15:29+02:00"], ["976ea2f9837", "fix: filter not working in report template XT-76213 (#20452)", "2024-08-21T09:14:13+02:00"], ["e8b18c79f21", "fix(deps): update dependency react-aria-components to v1.3.3 (#20492)", "2024-08-21T07:32:17+02:00"], ["86286e86347", "chore(deps): update dependency axe-core to v4.10.0 (#20495)", "2024-08-21T07:32:07+02:00"], ["e8a73d17332", "fix: check sql file in binary mode XT-77139 (#20486)", "2024-08-21T07:06:22+02:00"], ["e2de722c73a", "chore(deps): update dependency @chromatic-com/storybook to v1.7.0 (#20493)", "2024-08-21T07:02:42+02:00"], ["c3d15029003", "fix(deps): update dependency react-aria-components to v1.3.2 (#20491)", "2024-08-21T01:03:15+02:00"], ["100fc7d2d2f", "fix(deps): update dependency axios to v1.7.4 (#20488)", "2024-08-21T00:32:44+02:00"], ["00224182557", "fix(deps): update dependency dayjs to v1.11.13 (#20489)", "2024-08-21T00:32:40+02:00"], ["e7a2018a56d", "fix(deps): update dependency framer-motion to v11.3.28 (#20490)", "2024-08-21T00:32:36+02:00"], ["fcc8ade3ec3", "chore: updated docker pnpm-lock.yaml files", "2024-08-20T22:16:24+00:00"], ["e411784c147", "chore: commit upgrades", "2024-08-20T21:54:26+00:00"], ["6f6da03013e", "chore: bump patch version", "2024-08-20T20:04:35+00:00"], ["1c908f9cffe", "chore(deps): update dependency typescript-json-schema to ^0.65.0 (#20468)", "2024-08-20T20:02:46+02:00"], ["4e4aee9afa2", "feat: XT-77127 review read-entity action (#20480)", "2024-08-20T19:57:38+02:00"], ["8671c8d850b", "fix(finance-data): XT-75168 SDMO Stock Receipt GL Account Posting (#20319)", "2024-08-20T19:47:31+02:00"], ["697afed7b45", "feat(uxcrew): XT-76983 obsolete item adjusments (#20414)", "2024-08-20T19:16:57+02:00"], ["d07ed1ade88", "fix: upgrade recording fail on enum default value XT-77139 (#20483)", "2024-08-20T18:23:39+02:00"], ["9fc53f4e25d", "feat(finance): XT-77099 tests refactoring (#20481)", "2024-08-20T15:45:51+00:00"], ["bdcadac078b", "fix(sales): XT-44479 Minimum order quantity is being applied to shipments, invoices and returns (#20416)", "2024-08-20T17:19:43+02:00"], ["a527ed93fc6", "fix(deps): update dependency winston-transport to v4.7.1 (#20434)", "2024-08-20T17:02:51+02:00"], ["fbcd2d67176", "feat: XT-77070 exclude sync state and manage properties from tenant export (#20478)", "2024-08-20T15:57:19+02:00"], ["fb2b4b1e99d", "feat: XT-77129 prettify json files in layers (#20479)", "2024-08-20T15:47:06+02:00"], ["df901efc6e1", "chore: axios upgrade 1.7.4 CVE-2024-39338 (#20448)", "2024-08-20T15:34:20+02:00"], ["d2da281781c", "fix(xtrem-stock): XT-76012 stock adjustment status resync (#20327)", "2024-08-20T15:34:03+02:00"], ["33c83ee0125", "fix(pipelines): X3-317775 enabling wh services release image (#20473)", "2024-08-20T14:31:51+02:00"], ["94f2d761755", "feat: XT-75583 start interop service (#20460)", "2024-08-20T12:21:51+02:00"], ["7de8f04af70", "fix: fix animations for going level deeper or back in nested-grid mobile XT-74848 XT-68881 (#20381)", "2024-08-20T11:47:05+02:00"], ["e3bf755ad53", "feat(manufacturing): xt-77027-maintenance (#20451)", "2024-08-20T12:28:50+03:00"], ["0122ffc6e11", "feat(xtrem-finance): XT-74187 DATEV export: account, customer and supplier extraction (#20438)", "2024-08-20T11:27:58+02:00"], ["55f93543e95", "fix: restore esbuild-loader dependency version (#20439)", "2024-08-20T11:02:24+02:00"], ["1783f148b4b", "fix(xtrem-stock): XT-76784 Dimensions are not recorded for attributes Project Employee and task in Stock receipts (#20441)", "2024-08-20T09:59:50+02:00"], ["184fa62990c", "feat: XT-77096 review condition action (#20463)", "2024-08-20T09:29:57+02:00"], ["3b543e1e83d", "chore: missing await in ui tests (#20467)", "2024-08-20T09:19:06+02:00"], ["b2f628d46f2", "feat: refactored track events XT-75170 (#20456)", "2024-08-20T09:16:50+02:00"], ["f1d41e6b1bb", "docs(dashboard): adds documentation for table widget's date filter XT-67129 (#20445)", "2024-08-20T09:14:28+02:00"], ["4e6567b6982", "feat(xtrem-master-data): XT-75121-customer-supplier-lookup-category (#20273)", "2024-08-20T09:13:36+02:00"], ["90134beda74", "fix: pnpm-lock file (#20472)", "2024-08-20T08:57:20+02:00"], ["c250fb27545", "fix(deps): update dependency tslib to v2.6.3 (#20433)", "2024-08-20T02:06:42+02:00"], ["ed7e7b74221", "fix(deps): update dependency xml-formatter to v3.6.3 (#20436)", "2024-08-20T02:06:38+02:00"], ["62b4f5e398e", "fix(deps): update dependency workerpool to v9.1.3 (#20435)", "2024-08-20T01:34:33+02:00"], ["390ec88f567", "chore(deps): update dependency @chromatic-com/storybook to v1.6.1 (#20465)", "2024-08-20T01:34:25+02:00"], ["0b84b1ab1dc", "fix(deps): update dependency logform to v2.6.1 (#20427)", "2024-08-20T01:03:54+02:00"], ["acdb591d1fc", "fix(deps): update dependency minimatch to v9.0.5 (#20429)", "2024-08-20T01:03:50+02:00"], ["64aab56da61", "fix(deps): update dependency mssql to v10.0.4 (#20430)", "2024-08-20T01:03:45+02:00"], ["42dd1f41d29", "fix(deps): update dependency prom-client to v15.1.3 (#20432)", "2024-08-20T01:03:36+02:00"], ["909c224b174", "fix(deps): update dependency dompurify to v3.1.6 (#20425)", "2024-08-20T00:35:03+02:00"], ["e8ce2b13954", "chore(deps): update dependency mini-css-extract-plugin to v2.9.1 (#20462)", "2024-08-20T00:34:24+02:00"], ["7912e99a377", "fix(deps): update dependency looks-same to v9.0.1 (#20428)", "2024-08-19T23:05:14+02:00"], ["158034de6c3", "fix(deps): update elliptic to v6.5.7 XT-77059 [security] (#20442)", "2024-08-19T20:31:32+02:00"], ["446fd0e4094", "feat: XT-77075 review calculate action (#20459)", "2024-08-19T19:27:33+02:00"], ["e39ec9aa671", "feat: XAPPSF-519 controls on property mapping (#20450)", "2024-08-19T18:30:54+02:00"], ["b3f0c771b2f", "fix: XT-77040 fixed the optim (#20454)", "2024-08-19T16:03:02+02:00"], ["6c6d06ad53b", "feat(sales): XT-71087 pro forma invoice form template (#20444)", "2024-08-19T15:10:56+02:00"], ["e3b7160c0cc", "fix(master-data): XT-74724 Location mass creation error when the same storage zone is created (#20264)", "2024-08-19T14:53:08+02:00"], ["c1a09d5ef85", "chore(deps): revert update dependency esbuild-loader to v4", "2024-08-19T14:47:45+02:00"], ["98625fea33e", "feat(manufacturing): XT-74020 allow phantom item for excluded WO component (#20243)", "2024-08-19T14:24:46+02:00"], ["6c6b30d36e3", "feat(automationcrew): XT-77027-maintenance (#20443)", "2024-08-19T14:55:18+03:00"], ["08683a83fcf", "perf: XT-77040 improve registration into rawStringCache (#20440)", "2024-08-19T12:21:12+02:00"], ["ec83f12e092", "feat(xtrem-services): XT-76764 stock transfer order nodes (#20361)", "2024-08-19T11:00:35+02:00"], ["ca0f90fc738", "feat(xtrem-master-data): XT-68103-stock-receipt-header (#20347)", "2024-08-19T10:37:36+02:00"], ["ad4622361c4", "fix(purchasing): XT-70070 delete line not saved (#20417)", "2024-08-19T09:55:56+02:00"], ["5da3d42970b", "feat: XT-76691 Unit test for workflow entity-created event (#20437)", "2024-08-19T09:49:00+02:00"], ["f5660d8a7f4", "chore: updated docker pnpm-lock.yaml files", "2024-08-18T21:19:59+00:00"], ["cf6a858a427", "chore: commit upgrades", "2024-08-18T21:01:49+00:00"], ["5adfa501665", "chore: bump patch version", "2024-08-18T20:04:17+00:00"], ["f9737d845a3", "feat(automationcrew): XT-76976-maintenance (#20419)", "2024-08-18T18:47:32+03:00"], ["4fa35827def", "chore: updated docker pnpm-lock.yaml files", "2024-08-17T21:28:01+00:00"], ["06ebc01c9b7", "chore: commit upgrades", "2024-08-17T21:08:42+00:00"], ["115cd384f1b", "chore: bump patch version", "2024-08-17T20:04:25+00:00"], ["9d32bf5a86e", "feat: XT-76972 first brew of workflow test framework (#20423)", "2024-08-17T18:56:31+02:00"], ["4cf1f3ad3f1", "chore: updated docker pnpm-lock.yaml files", "2024-08-16T21:34:45+00:00"], ["849c7d25020", "chore: commit upgrades", "2024-08-16T21:17:25+00:00"], ["c76d5af02ba", "chore: bump patch version", "2024-08-16T20:04:26+00:00"], ["50acf4514cd", "chore(deps): update istanbuljs monorepo (#20398)", "2024-08-16T17:32:46+02:00"], ["698c2a5ec06", "feat(shopfloor): XAPPSF-630 quantities scale. (#20422)", "2024-08-16T17:27:34+02:00"], ["c9f69d9ce02", "fix(x3-manufacturing): X3-318682-material-consumption-lpn-empty (#20420)", "2024-08-16T15:16:18+01:00"], ["d56ae1117b3", "feat(automationcrew): XT-58270-automation-debt-fifo-scenario (#20351)", "2024-08-16T16:43:40+03:00"], ["6fde87568aa", "feat: XT-73922 remote delete mutation (#20418)", "2024-08-16T15:41:04+02:00"], ["1f5d9dc473a", "chore(deps): update newrelic (#20399)", "2024-08-16T14:02:37+02:00"], ["e33cdcae088", "feat(xtrem-master-data): XT-76804-item-site-title-adjustment (#20356)", "2024-08-16T12:52:24+02:00"], ["ca916ea6fa0", "feat(finance): XT-76974 refactoring to remove redundant calls by the … (#20413)", "2024-08-16T10:47:00+00:00"], ["8fc92a40f13", "feat: XT-73922 remote update mutation (#20405)", "2024-08-16T11:06:21+02:00"], ["f028dea4b5c", "fix(deps): update dependency debug to v4.3.6 (#20412)", "2024-08-16T11:02:43+02:00"], ["56fe177e209", "feat: XT-76691 review and improve entity-created config page (#20374)", "2024-08-16T09:11:04+02:00"], ["d8aeea2b6b6", "feat(shopfloor): XAPPSF-43 completed quantity (#20404)", "2024-08-16T08:51:37+02:00"], ["088425ec724", "fix(deps): update dependency @sage/xtrem-infra to v1.0.9 (#20409)", "2024-08-16T07:32:51+02:00"], ["c4b56fc21b7", "fix(deps): update dependency csv-stringify to v6.5.1 (#20410)", "2024-08-16T07:32:43+02:00"], ["29705ccd77c", "fix(deps): update dependency dayjs to v1.11.12 (#20411)", "2024-08-16T07:02:44+02:00"], ["1b3b803e0c7", "fix(deps): update dependency @sage/intacct-controlid-provider to v2.1.2 (#20407)", "2024-08-16T01:32:21+02:00"], ["e430649e1fb", "chore: fix renovate config", "2024-08-15T23:31:36+02:00"], ["56e2d0d8ff2", "chore: updated docker pnpm-lock.yaml files", "2024-08-15T21:23:11+00:00"], ["5aa4d25f438", "chore: commit upgrades", "2024-08-15T21:07:35+00:00"], ["242d673e9b9", "chore: bump patch version", "2024-08-15T20:03:48+00:00"], ["6c784c843b7", "fix(x3-manufacturing): X3-317985-production-reporting-packingUnit (#20403)", "2024-08-15T15:46:22+01:00"], ["3a1b388ed6b", "feat(pipelines): XT-76576 - force test to stop when scenario fails fo… (#20389)", "2024-08-15T16:18:42+02:00"], ["937ce1c3ce9", "fix(xtrem-manufacturing): XT-76301 work order close button visibility (#20376)", "2024-08-15T14:52:28+02:00"], ["fbdbb317170", "feat(shopfloor): XAPPSF-526-clock-out-button-clockin-status-node-tracking (#20400)", "2024-08-15T10:17:59+01:00"], ["9abbb705f3f", "fix: XT-76913 Extend<This> type definition (#20391)", "2024-08-14T23:37:02+02:00"], ["0565584854c", "chore: revert turbo to v1.13.2 (#20395)", "2024-08-14T23:23:09+02:00"], ["e99ee0d0857", "chore: updated docker pnpm-lock.yaml files", "2024-08-14T21:15:45+00:00"], ["c665b1e6df1", "chore: commit upgrades", "2024-08-14T21:01:10+00:00"], ["abe755fb7f7", "chore: bump patch version", "2024-08-14T20:03:49+00:00"], ["66fec85876a", "feat: printing manager XT-75265 (#20396)", "2024-08-14T21:41:31+02:00"], ["273f65734e0", "feat: XT-73922 fix remote info cache (#20397)", "2024-08-14T21:17:48+02:00"], ["203cab66070", "chore(deps): update dependency mini-css-extract-plugin to v2.9.0 (#18548)", "2024-08-14T19:05:19+02:00"], ["407d085d9a3", "fix: too many listeners for abort event XT-76364 (#20388)", "2024-08-14T17:47:22+02:00"], ["03b3a1c0091", "feat: XT-68791 sales order line inqury based on mainlist (#20382)", "2024-08-14T16:44:45+02:00"], ["29852cdfb90", "feat(xtrem-cli-atp): XT-76576 stop test execution when scenario fails (#20364)", "2024-08-14T16:44:24+02:00"], ["0a875019ea4", "feat(authorization): add site filter on stockjournal XT-75057 (#20091)", "2024-08-14T16:00:44+02:00"], ["fb33bc0a491", "feat(automationcrew): XT-76842-maintenance-fix (#20385)", "2024-08-14T17:00:12+03:00"], ["bc6181c0ea0", "feat(xtrem-finance): XT-74097 DATEV export parameters (#20067)", "2024-08-14T15:50:19+02:00"], ["b7510d29801", "fix(xtrem-finance): XT-75523 tax details missing (#20345)", "2024-08-14T14:50:21+02:00"], ["3ffb5b97117", "feat(xtrem-tax): XT-76638 Page-extensions refactoring (#20355)", "2024-08-14T15:39:46+03:00"], ["2cdbd3746b6", "fix: custom field metadata query XT-76825 (#20377)", "2024-08-14T14:25:05+02:00"], ["6440e86af2d", "fix: use x3-ubuntu pool for x3 dev packs", "2024-08-14T12:17:45+02:00"], ["7fd706faceb", "fix: use x3-ubuntu pool for x3 dev packs", "2024-08-14T12:17:25+02:00"], ["a8604ce3b68", "feat(purchasing): createPurchaseOrder from purchase requisition XT-70141 #performance (#20360)", "2024-08-14T12:00:06+02:00"], ["f06d21552ed", "feat(xtrem-tax): XT-76588 Second batch pages refactoring (#20309)", "2024-08-14T12:49:32+03:00"], ["3dc38a49ae3", "fix: use x3-ubuntu pool for x3 dev packs (#20375)", "2024-08-14T11:47:56+02:00"], ["7d3020b0e7d", "fix: fix style for detail panel due to grey header added (#20378)", "2024-08-14T11:19:43+02:00"], ["ef18c8b786d", "fix: await user events in front-end tests (#20359)", "2024-08-14T09:33:01+02:00"], ["405c332cd55", "feat(finance): XT-76753 refactoring due to receipt screen changes (#20352)", "2024-08-14T07:18:41+00:00"], ["2bc9169c2e7", "chore(deps): update dependency esbuild-loader to v4 (#18115)", "2024-08-14T09:02:35+02:00"], ["1df434ea9d2", "fix: X3-318763 - fix shortage with detailed allocation (#20335)", "2024-08-14T08:47:50+02:00"], ["3c0d23db48c", "fix(sales): XT-76539 Sales Shipment control on quantity (#20348)", "2024-08-14T08:39:30+02:00"], ["abab798cf0a", "chore(deps): update dependency @testing-library/jest-dom to v6.4.8 (#20368)", "2024-08-14T08:02:50+02:00"], ["c0c33de4999", "chore(deps): update dependency jsdom to v24.1.1 (#20369)", "2024-08-14T07:32:37+02:00"], ["486e6de9348", "chore(deps): update dependency rimraf to v5.0.10 (#20370)", "2024-08-14T07:32:33+02:00"], ["1e762512dbb", "chore(deps): update dependency tsd to v0.31.1 (#20371)", "2024-08-14T07:02:54+02:00"], ["c76ff492e99", "chore(deps): update dependency turbo to v1.13.4 (#20372)", "2024-08-14T07:02:50+02:00"], ["319efdf8a1e", "fix(deps): update sage cirrus (#19074)", "2024-08-14T00:02:51+02:00"], ["a39b71cebc9", "chore(deps): update dependency @storybook/addon-webpack5-compiler-swc to v1.0.5 (#20363)", "2024-08-14T00:02:48+02:00"], ["f09a3caf90a", "chore: updated docker pnpm-lock.yaml files", "2024-08-13T21:52:04+00:00"], ["a319bcc8b0d", "chore: commit upgrades", "2024-08-13T21:32:58+00:00"], ["79d300b4fe1", "chore: bump patch version", "2024-08-13T20:04:05+00:00"], ["7f39f6d2982", "chore(deps): update amannn/action-semantic-pull-request action to v5.5.3 (#20362)", "2024-08-13T21:02:46+02:00"], ["149da1e3ab6", "chore(deps): update dependency sass to v1.77.8 (#19079)", "2024-08-13T20:03:16+02:00"], ["2803defd82d", "fix(deps): update aws-sdk-js-v3 monorepo to v3.629.0 (#20357)", "2024-08-13T20:03:09+02:00"], ["7960572ff0a", "chore(deps): update non-major types dependencies (#19073)", "2024-08-13T19:32:25+02:00"], ["ab2bc36721e", "feat(xtrem-cli-atp): XT-76561 allure report maximum call stack size exceeded (#20297)", "2024-08-13T19:12:06+02:00"], ["df29ea5e1b5", "fix(deps): update postgresql (#20358)", "2024-08-13T19:02:37+02:00"], ["daff11aedd7", "feat: XT-73922 manual testing fixes 3 (#20340)", "2024-08-13T18:27:53+02:00"], ["9b58bbf6fdd", "chore: fix release guard commit date", "2024-08-13T16:55:46+02:00"], ["3936d34a562", "feat(distribution): More properties on baseDocuments XT-71136 (#20346)", "2024-08-13T16:11:09+02:00"], ["f824d50b805", "feat(purchasing): #Performance get rid of flushDefered Actions XT-70141 (#20339)", "2024-08-13T16:10:39+02:00"], ["f4d709ffdd1", "feat: more tag events (#20334)", "2024-08-13T16:02:38+02:00"], ["b66d0c385eb", "fix(deps): update dependency puppeteer to v22.15.0 (#19080)", "2024-08-13T15:32:58+02:00"], ["4e6d8004ac3", "chore(deps): update dependency eslint-plugin-react to v7.35.0 (#19379)", "2024-08-13T15:32:54+02:00"], ["d15fdc8a80f", "fix(manufacturing): XT-73121 purchase credit memo status (#20285)", "2024-08-13T13:41:01+02:00"], ["3e7a628161a", "feat(finance): xt-76755-maintenance (#20349)", "2024-08-13T14:38:06+03:00"], ["f1148da3829", "feat: XT-64095 Refactoring unbilled account payable inquiry (#20317)", "2024-08-13T13:08:05+02:00"], ["e09f2ba4403", "feat(pipelines): XT-76618 - remove variable (#20343)", "2024-08-13T11:42:47+02:00"], ["b7830c6b775", "feat(purchasing): XT-76300 Fix reorder-purchase-order-panel file (#20329)", "2024-08-13T12:37:59+03:00"], ["c3982f108cc", "feat: add new record button to nested grid mobile XT-74848 (#20293)", "2024-08-13T11:21:54+02:00"], ["c47da936a9c", "feat(xtrem-intacct-finance): XT-76356 Company doApPosting and doArPosting with no default configuration active on intacct (#20289)", "2024-08-13T10:18:52+01:00"], ["f885492cef2", "feat(distribution): Base document refactoring XT-71136 (#20336)", "2024-08-13T10:49:59+02:00"], ["94b479f3286", "fix(conditional-block): ensures node is updated XT-72990 (#20322)", "2024-08-13T10:44:02+02:00"], ["324f3d5be19", "test(tests): XT-76756 - tag functional tests (#20342)", "2024-08-13T10:41:13+02:00"], ["6022d9f3d94", "chore: remove ag-grid deps from root (#20332)", "2024-08-13T09:40:50+02:00"], ["e9a050bcdc9", "feat: XT-76691 refactoring of entity created step (server side) (#20338)", "2024-08-13T09:19:05+02:00"], ["880b4097450", "fix(xtrem-manufacturing): XT-75503 work order component lines allocate stock action disabled (#20316)", "2024-08-13T07:36:47+01:00"], ["e526da3ff2a", "chore: updated docker pnpm-lock.yaml files", "2024-08-12T22:01:51+00:00"], ["419cb61d9df", "chore: commit upgrades", "2024-08-12T21:42:13+00:00"], ["63793902236", "chore: bump patch version", "2024-08-12T20:04:32+00:00"], ["c3b8d3e78b5", "fix(deps): update dependency @ag-grid-enterprise/charts to v32 [security] (#20328)", "2024-08-12T18:32:31+02:00"], ["344ebb03ca3", "feat(finance): XT-76667 refactoring due to receipt screen changes part 2 (#20333)", "2024-08-12T15:34:40+00:00"], ["45598e6b611", "feat(tc): create bpexcept node x3 295446 (#20312)", "2024-08-12T16:33:35+02:00"], ["7b33d9ede2c", "feat(finance): XT-76545-fix-failures-and-uncomment-tests (#20323)", "2024-08-12T16:35:09+03:00"], ["66001e9e500", "feat(sales): XT-67647 sales credit memo form report (#20324)", "2024-08-12T15:29:54+02:00"], ["aac0a598b5d", "feat(pipelines): XT-76618 - add jira ticket creation capability to sdmo release functional-test pipelines (#20308)", "2024-08-12T14:49:39+02:00"], ["50d5c2f002f", "feat(sales): XT-67646 sales invoice form report (#20320)", "2024-08-12T13:16:18+02:00"], ["0e9a1b4ac8c", "feat(finance): XT-76667 refactoring due to receipt screen changes (#20318)", "2024-08-12T10:58:24+00:00"], ["70640d594e8", "feat(distribution): Base Document Refactoring - (#20250)", "2024-08-12T12:53:35+02:00"], ["4fdce4eb2e2", "feat: XT-76501 refactor xtrem-workflow-test and make it reliable (#20314)", "2024-08-12T10:48:05+02:00"], ["49893982dff", "feat(sales): XT- 67642 sales shipment packing slip report (#20315)", "2024-08-12T09:46:23+02:00"], ["282ffbcb6ef", "fix(financeData): XT-76637 Dropdown-data-mapping-fix (#20313)", "2024-08-12T10:30:37+03:00"], ["8b355495feb", "chore: updated docker pnpm-lock.yaml files", "2024-08-11T21:19:44+00:00"], ["76ca5674971", "chore: commit upgrades", "2024-08-11T21:01:42+00:00"], ["bdec8451aa5", "chore: bump patch version", "2024-08-11T20:07:41+00:00"], ["51faf82085e", "feat: prefix integration test execution with numbers XT-72852 (#20299)", "2024-08-11T20:33:57+02:00"], ["8f4fd5a2d95", "chore: updated docker pnpm-lock.yaml files", "2024-08-10T21:25:51+00:00"], ["0af33db9431", "chore: commit upgrades", "2024-08-10T21:06:33+00:00"], ["047e183520b", "chore: bump patch version", "2024-08-10T20:07:45+00:00"], ["1652a187a71", "chore: updated docker pnpm-lock.yaml files", "2024-08-09T21:16:12+00:00"], ["8432394194e", "chore: commit upgrades", "2024-08-09T20:58:22+00:00"], ["c3c9858c57e", "chore: bump patch version", "2024-08-09T20:04:16+00:00"], ["4577a12094c", "feat(distribution): XT-76603-refactoring-ft (#20311)", "2024-08-09T16:36:01+03:00"], ["1ecb01cdabc", "feat(shopfloor): XAPPSF-486 tracking stop times (#20295)", "2024-08-09T14:45:48+02:00"], ["f489d507546", "feat(shopfloor): XAPPSF-468-clock-in-new-operator-node-create-mutation (#20310)", "2024-08-09T12:54:53+01:00"], ["b9a789154d4", "feat(xtrem-stock): XT-68371-stock-receipt-single-status (#20161)", "2024-08-09T13:40:39+02:00"], ["0ef2a3a1d4c", "feat(automationcrew): XT-58270-automation-debt-db (#20307)", "2024-08-09T12:43:44+03:00"], ["dfe6b3c922b", "feat(shopfloor): XAPPSF-551 Create tracking sync header and detail nodes (#20301)", "2024-08-09T10:35:22+01:00"], ["7b910788b1d", "feat(xtrem-master-data): XT-72140 Moved shared folder files from pages in master-data to client-functions (#20158)", "2024-08-09T11:27:26+03:00"], ["20494885147", "refactor: XT-76501 refactoring and review of workflow engine classes (2) (#20305)", "2024-08-08T23:19:00+02:00"], ["5b2fafe13d6", "chore: updated docker pnpm-lock.yaml files", "2024-08-08T21:13:36+00:00"], ["ac2abd8a30e", "chore: commit upgrades", "2024-08-08T20:59:03+00:00"], ["1b590080884", "chore: bump patch version", "2024-08-08T20:03:51+00:00"], ["96677f192a4", "feat(sales): XT-67641 sales order confirmation form report (#20296)", "2024-08-08T20:44:22+02:00"], ["af1fe736e23", "feat: XT-73922 manual testing fixes (#20306)", "2024-08-08T20:24:52+02:00"], ["dd6d7ea3219", "feat: XT-76340 add sync attributes to node extensions (#20304)", "2024-08-08T19:56:39+02:00"], ["999fddeb025", "feat(financeData): XT-72474 post-PR final fixes (#20105)", "2024-08-08T20:02:08+03:00"], ["ff21fd3a0f3", "fix(sales): XT-75311 sales shipment data fix (#20288)", "2024-08-08T18:29:04+02:00"], ["2349e3725e3", "feat(xtrem-supply-chain): XT-74991 remove stock transfer order node (#20300)", "2024-08-08T18:25:05+02:00"], ["95ffae0f6f7", "feat: X3-315126 remove .npmignore from generator, update test (#20303)", "2024-08-08T16:53:40+02:00"], ["75c9e19a69f", "feat(xtrem-tax): XT-76500 First batch pages refactoring (#20255)", "2024-08-08T17:38:00+03:00"], ["ce7968fd103", "feat(finance): XT-XT-76545-cleanup (#20298)", "2024-08-08T16:46:20+03:00"], ["ca7c187b688", "fix: validation error in sales order XT-75099 (#20277)", "2024-08-08T14:18:02+02:00"], ["c29bb258341", "feat(purchasing): XT-69581 purchase order form report template (#20294)", "2024-08-08T11:27:25+02:00"], ["5d93bd5f8a8", "feat: XT-73922 allow non-mutable collections in mapping (#20291)", "2024-08-08T11:04:45+02:00"], ["2dd78839a6d", "fix(xtrem-finance-data): XT-76357 Posting class definition - Validation error when setting an additional criteria (#20284)", "2024-08-08T09:47:37+01:00"], ["54e655d375c", "feat: adapt nested grid component for mobile view XT-68881 (#20185)", "2024-08-08T10:44:48+02:00"], ["12fc4f7aa2f", "feat(finance): XT-76497-maintenance (#20290)", "2024-08-08T11:37:53+03:00"], ["8f7601ef840", "chore: updated docker pnpm-lock.yaml files", "2024-08-07T21:26:24+00:00"], ["a10d4b1259d", "chore: commit upgrades", "2024-08-07T21:06:25+00:00"], ["7a8e33c9b2b", "chore: bump patch version", "2024-08-07T20:04:14+00:00"], ["7bde46f0244", "feat: XT-76501 documentation and review of workflow engine classes (#20287)", "2024-08-07T21:46:21+02:00"], ["27749b21e0c", "fix(xtrem-cli-atp): XT-76443 secure elem click rather than jsclick (#20266)", "2024-08-07T20:44:57+02:00"], ["602e77155f5", "feat: XT-73922 use lodash set (#20286)", "2024-08-07T19:44:01+02:00"], ["2689faf4bb2", "feat: XT-73922 external node create mutation showcase tests (#20279)", "2024-08-07T17:52:47+02:00"]]}}}