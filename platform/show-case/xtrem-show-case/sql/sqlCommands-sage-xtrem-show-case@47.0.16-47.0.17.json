{"fromVersion": "47.0.16", "toVersion": "47.0.17", "gitHead": "4447e5d2bc3e87155b82874e39975126916d8dd0", "commands": [{"action": "system_upgrade", "args": {"version": "47.0.17"}}, {"isSysPool": true, "sql": "ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.activity ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.attachment ALTER COLUMN last_modified TYPE TIMESTAMPTZ(3) USING last_modified,ALTER COLUMN last_download_date TYPE TIMESTAMPTZ(3) USING last_download_date,ALTER COLUMN orphaned_date TYPE TIMESTAMPTZ(3) USING orphaned_date,ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.attachment_association ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.company ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.custom_field ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.dashboard ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.dashboard_item ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.group_role ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.group_role_site ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.group_site ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.import_export_template ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.import_result ALTER COLUMN start_stamp TYPE TIMESTAMPTZ(3) USING start_stamp,ALTER COLUMN end_stamp TYPE TIMESTAMPTZ(3) USING end_stamp,ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.locale ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.meta_activity ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.meta_activity_permission ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.meta_data_type ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.meta_node_factory ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.meta_node_operation ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.meta_node_property ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.meta_package ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.meta_service_option ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.report ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.report_resource ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.report_style_variable ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.report_template ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.report_translatable_text ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.report_variable ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.report_wizard ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.restricted_node ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.restricted_node_user_grant ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.role ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.role_activity ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.role_to_role ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.show_case_bill_of_material ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.show_case_component ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.show_case_country ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.show_case_customer ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.show_case_employee ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.show_case_invoice ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.show_case_invoice_line ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.show_case_item ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.show_case_node_browser_tree ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.show_case_order ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.show_case_product ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.show_case_product_origin_address ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.show_case_provider ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.show_case_provider_address ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.show_case_time ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.site ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.site_group ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.site_group_to_site ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.site_group_to_site_group ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.support_access_history ALTER COLUMN start_time TYPE TIMESTAMPTZ(3) USING start_time,ALTER COLUMN end_time TYPE TIMESTAMPTZ(3) USING end_time,ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.sys_app ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.sys_changelog ALTER COLUMN change_date TYPE TIMESTAMPTZ(3) USING change_date,ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.sys_client_notification ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.sys_client_notification_action ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.sys_client_user_settings ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.sys_csv_checksum ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.sys_custom_record ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.sys_custom_sql_history ALTER COLUMN start_date_time TYPE TIMESTAMPTZ(3) USING start_date_time,ALTER COLUMN end_date_time TYPE TIMESTAMPTZ(3) USING end_date_time,ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.sys_customer ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.sys_enum_mapping ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.sys_enum_transformation ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.sys_global_lock ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.sys_job_schedule ALTER COLUMN start_stamp TYPE TIMESTAMPTZ(3) USING start_stamp,ALTER COLUMN end_stamp TYPE TIMESTAMPTZ(3) USING end_stamp,ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.sys_message ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.sys_message_history ALTER COLUMN send_stamp TYPE TIMESTAMPTZ(3) USING send_stamp,ALTER COLUMN received_stamp TYPE TIMESTAMPTZ(3) USING received_stamp,ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.sys_node_mapping ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.sys_node_transformation ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.sys_notification ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.sys_notification_history ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.sys_notification_log_entry ALTER COLUMN timestamp TYPE TIMESTAMPTZ(3) USING timestamp,ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.sys_notification_state ALTER COLUMN time_started TYPE TIMESTAMPTZ(3) USING time_started,ALTER COLUMN time_ended TYPE TIMESTAMPTZ(3) USING time_ended,ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.sys_operation_transformation ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.sys_pack_allocation ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.sys_pack_version ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.sys_patch_history ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.sys_service_option ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.sys_service_option_state ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.sys_service_option_to_service_option ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.sys_synchronization_state ALTER COLUMN succcess_stamp TYPE TIMESTAMPTZ(3) USING succcess_stamp,ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.sys_tenant ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.sys_upgrade ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.sys_vendor ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.test_async_mutation_on_specific_queue ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.uploaded_file ALTER COLUMN last_modified TYPE TIMESTAMPTZ(3) USING last_modified,ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.user ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.user_billing_role ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.user_group ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.user_navigation ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.user_preferences ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.widget_category ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.workflow_definition ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.workflow_diagram ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.workflow_process ALTER COLUMN started_at TYPE TIMESTAMPTZ(3) USING started_at,ALTER COLUMN completed_at TYPE TIMESTAMPTZ(3) USING completed_at,ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp", "args": []}, {"isSysPool": true, "sql": ["", "", "CREATE EXTENSION IF NOT EXISTS pgcrypto;", ""]}, {"isSysPool": true, "sql": ["", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.get_config(setting_name varchar)", "RETURNS varchar AS", "$$", "declare", "    setting_value varchar;", "BEGIN", "    SELECT current_setting(setting_name) into setting_value;", "    RETURN setting_value;", "EXCEPTION", "    WHEN OTHERS THEN", "    RETURN '';", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.drop_triggers(schema_name varchar, name_filter varchar DEFAULT '') RETURNS integer AS", "$$", "DECLARE", "    triggerRecord RECORD;", "    record_count integer;", "begin", "\trecord_count = 0;", "    FOR triggerRecord IN", "    \tSELECT trigger_name, event_object_table", "    \tFROM information_schema.triggers", "    \tWHERE trigger_schema = schema_name AND (name_filter = '' OR event_object_table = name_filter)", "\tLOOP", "\t\trecord_count = record_count + 1;", "        EXECUTE 'DROP TRIGGER ' || triggerRecord.trigger_name || ' ON ' || schema_name || '.\"' || triggerRecord.event_object_table || '\";';", "    END LOOP;", "", "    RETURN record_count;", "END;", "$$ LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.drop_notify_functions(schema_name varchar, name_filter varchar DEFAULT '') RETURNS integer AS", "$$", "DECLARE", "    triggerRecord RECORD;", "    record_count integer;", "BEGIN", "    record_count = 0;", "    FOR triggerRecord IN", "    SELECT routine_name", "    FROM information_schema.routines", "    WHERE specific_schema = schema_name and routine_name IN (name_filter || '_notify_deleted', name_filter || '_notify_created', name_filter || '_notify_updated')", "    LOOP", "        record_count = record_count + 1;", "        EXECUTE 'DROP FUNCTION ' || schema_name || '.' || triggerRecord.routine_name || ';';", "    END LOOP;", "", "    RETURN record_count;", "END;", "$$ LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.insert_table()", "RETURNS TRIGGER", "AS", "$$", "    DECLARE", "        USER_ID VARCHAR;", "    BEGIN", "        SELECT %%SCHEMA_NAME%%.get_config('xtrem.transaction_user_id') INTO USER_ID;", "        IF (USER_ID != '') THEN", "            NEW._create_user :=  CAST(USER_ID AS INT8);", "            NEW._update_user :=  CAST(USER_ID AS INT8);", "        END IF;", "        IF (NEW._create_stamp IS NULL) THEN", "            NEW._create_stamp := NOW();", "        END IF;", "        IF (NEW._update_stamp IS NULL) THEN", "            NEW._update_stamp := NOW();", "        END IF;", "        IF (NEW._update_tick IS NULL) THEN", "            NEW._update_tick := 1;", "        END IF;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.insert_shared_table()", "RETURNS TRIGGER", "AS", "$$", "    BEGIN", "        IF (NEW._create_stamp IS NULL) THEN", "            NEW._create_stamp := NOW();", "        END IF;", "        IF (NEW._update_stamp IS NULL) THEN", "            NEW._update_stamp := NOW();", "        END IF;", "        IF (NEW._update_tick IS NULL) THEN", "            NEW._update_tick := 1;", "        END IF;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.update_table()", "RETURNS TRIGGER", "AS", "$$", "    DECLARE", "        USER_ID VARCHAR;", "    BEGIN", "        SELECT %%SCHEMA_NAME%%.get_config('xtrem.transaction_user_id') INTO USER_ID;", "        IF (USER_ID != '') THEN", "            NEW._update_user :=  CAST(USER_ID AS INT8);", "        END IF;", "        NEW._update_stamp := NOW();", "        NEW._update_tick := OLD._update_tick + 1;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.update_shared_table()", "RETURNS TRIGGER", "AS", "$$", "    BEGIN", "        NEW._update_stamp := NOW();", "        NEW._update_tick := OLD._update_tick + 1;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.set_sync_tick()", "RETURNS TRIGGER", "AS", "$$", "    BEGIN", "        NEW._sync_tick :=  pg_current_xact_id();", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.signedInt32(a bigint)", "RETURNS bigint AS", "$$", "DECLARE", "BEGIN", "\t-- Convert to 32 bit signed (if leftmost bit is 1, it's a negative number)", "  \tIF (a > 2^31) THEN", "    \tRETURN a - (2^32)::bigint;", "  \tEND IF;", "  \tRETURN a;", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.imul(a bigint, b bigint)", "RETURNS bigint AS", "$$", "DECLARE", "    aHi bigint;", "    aLo bigint;", "    bHi bigint;", "    bLo bigint;", "    res bigint;", "BEGIN", "    aHi = %%SCHEMA_NAME%%.zeroFillShift(a, 16) & 65535;", "    aLo = a & 65535;", "    bHi = %%SCHEMA_NAME%%.zeroFillShift(b, 16) & 65535;", "    bLo = b & 65535;", "    res = ((aLo * bLo) + %%SCHEMA_NAME%%.zeroFillShift(((aHi * bLo + aLo * bHi) << 16) % (2^32)::bigint, 0)) | 0;", "    RETURN %%SCHEMA_NAME%%.signedInt32(res);", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.zeroFillShift(a bigint, b int)", "RETURNS bigint AS", "$$", "DECLARE", "  \tres bigint;", "BEGIN", "\tIF (a < 0) THEN", "\t\tres = a + 2^32;", "\tELSE", "\t\tres = a;", "\tEND IF;", "\tres = res >> b;", "\tRETURN res;", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.nanoid(\"size\" int4 DEFAULT 21)", "    RETURNS text", "    LANGUAGE plpgsql", "    STABLE", "    AS", "    $$", "    DECLARE", "        id text := '';", "        i int := 0;", "        urlAl<PERSON><PERSON> char(64) := 'ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW';", "        bytes bytea;", "        byte int;", "        pos int;", "    BEGIN", "        SELECT gen_random_bytes(size) INTO bytes;", "    WHILE i < size LOOP", "        byte := get_byte(bytes, i);", "        pos := (byte & 63) + 1; -- + 1 because substr starts at 1", "        id := id || substr(urlAlphabet, pos, 1);", "        i = i + 1;", "    END LOOP;", "    RETURN id;", "    END", "    $$", "    ;", " ", "", ""]}, {"isSysPool": false, "sql": ["", "            DELETE FROM %%SCHEMA_NAME%%.sys_notification_state st", "                USING %%SCHEMA_NAME%%.sys_job_schedule sched", "                WHERE", "                    st._tenant_id = sched._tenant_id", "                AND st.schedule = sched._id", "                AND sched.id = 'purgeContentAddressableTables';", "", "            DELETE FROM %%SCHEMA_NAME%%.sys_job_schedule sched", "                WHERE sched.id = 'purgeContentAddressableTables';", "            "], "actionDescription": "Delete schedule entries for garbage collection of  content addressable tables"}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Is a demo tenant", "released", false, "@sage/xtrem-system", false, "isDemoTenant"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["enable dev-only features", "experimental", false, "@sage/xtrem-system", false, "devTools"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["allow to display changelog in the app", "experimental", false, "@sage/xtrem-system", false, "changelog"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Notification center", "experimental", false, "@sage/xtrem-communication", false, "notificationCenter"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Authorization access control", "released", false, "@sage/xtrem-authorization", false, "authorizationServiceOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Workflow option", "workInProgress", false, "@sage/xtrem-workflow", false, "workflowOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Synchronization", "experimental", false, "@sage/xtrem-interop", false, "synchronizationServiceOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["showCase discount option", "experimental", false, "@sage/xtrem-show-case", false, "showCaseDiscountOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["experimental option", "experimental", false, "@sage/xtrem-show-case", false, "showCaseExperimentalOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["showCaseOption's hight level description", "released", false, "@sage/xtrem-show-case", false, "showCaseOptionHighLevel"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["showCaseOption1's description", "released", true, "@sage/xtrem-show-case", false, "showCaseOption1"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["showCaseOption2's description", "released", true, "@sage/xtrem-show-case", false, "showCaseOption2"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["A workInProgress service option that can be loaded on a development environment", "workInProgress", false, "@sage/xtrem-show-case", true, "showCaseOption3"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Work in progress options cannot be activated", "workInProgress", false, "@sage/xtrem-show-case", false, "showCaseWorkInProgressOption"]}, {"isSysPool": false, "sql": "NOTIFY invalidate_category_cache, '{\"data\":\"{\\\"tenantId\\\":null,\\\"category\\\":\\\"$SHARED_NODE.SysServiceOption\\\"}\",\"containerId\":\"x3-devops00DHPA-64524\",\"excludeSelf\":true}';", "args": []}, {"action": "reload_setup_data", "args": {"factory": "SysJobSchedule"}}, {"action": "reload_setup_data", "args": {"factory": "SysServiceOptionState"}}], "data": {"SysJobSchedule": {"metadata": {"rootFactoryName": "SysJobSchedule", "name": "SysJobSchedule", "naturalKeyColumns": ["_tenant_id", "id", "execution_user"], "columns": [{"name": "_vendor", "type": "reference", "isNullable": true, "targetFactoryName": "SysVendor"}, {"name": "operation", "type": "reference", "targetFactoryName": "MetaNodeOperation"}, {"name": "description", "type": "string"}, {"name": "is_active", "type": "boolean"}, {"name": "start_stamp", "type": "datetime", "isNullable": true}, {"name": "end_stamp", "type": "datetime", "isNullable": true}, {"name": "execution_user", "type": "reference", "targetFactoryName": "User"}, {"name": "execution_locale", "type": "string"}, {"name": "parameter_values", "type": "json"}, {"name": "cron_schedule", "type": "string"}, {"name": "time_zone", "type": "string"}, {"name": "id", "type": "string"}]}, "rows": [["sage", "SysNotificationState|purgeHistory|start", "Purge history - 3 months", "Y", null, null, "<EMAIL>", "en-US", "{\"unit\":\"months\",\"duration\":\"3\"}", "0 1 1 * *", "Europe/Paris", "purgeHistory_1"], ["sage", "SysNotificationState|purgeHistory|start", "Purge sync history - 2 weeks", "Y", null, null, "<EMAIL>", "en-US", "{\n    \"unit\": \"weeks\",\n    \"duration\": \"2\",\n    \"operationNames\": [\"SysSynchronizationTarget.synchronize\"]\n}\n", "30 1 * * *", "Europe/Paris", "purgeSyncHistory_1"], ["sage", "SysClientNotification|purgeSysClientNotification|start", "\"info\" client notification will automatically be deleted after seven days", "Y", null, null, "<EMAIL>", "en-US", "{\"unit\":\"days\",\"level\":\"info\",\"duration\":\"7\"}", "0 0 * * *", "Europe/Paris", "purgeSysClientNotification_1"], ["sage", "SysClientNotification|purgeSysClientNotification|start", "\"success\" client notification will automatically be deleted after seven days", "Y", null, null, "<EMAIL>", "en-US", "{\"unit\":\"days\",\"level\":\"success\",\"duration\":\"7\"}", "0 0 * * *", "Europe/Paris", "purgeSysClientNotification_2"], ["sage", "SysClientNotification|purgeSysClientNotification|start", "\"warning\" client notification will automatically be deleted after seven days", "Y", null, null, "<EMAIL>", "en-US", "{\"unit\":\"days\",\"level\":\"warning\",\"duration\":\"7\"}", "0 0 * * *", "Europe/Paris", "purgeSysClientNotification_3"], ["sage", "SysClientNotification|purgeSysClientNotification|start", "\"error\" client notification will automatically be deleted after seven days", "Y", null, null, "<EMAIL>", "en-US", "{\"unit\":\"days\",\"level\":\"error\",\"duration\":\"7\"}", "0 0 * * *", "Europe/Paris", "purgeSysClientNotification_4"], ["sage", "Attachment|purge|start", "Purge attachments and association attachments", "Y", null, null, "<EMAIL>", "en-US", "{\"unit\":\"days\",\"level\":\"info\",\"duration\":\"1\"}", "0 0 * * *", "Europe/Paris", "purgeAttachment_"]]}, "SysServiceOptionState": {"metadata": {"rootFactoryName": "SysServiceOptionState", "name": "SysServiceOptionState", "naturalKeyColumns": ["_tenant_id", "service_option"], "columns": [{"name": "is_activable", "type": "boolean"}, {"name": "is_active", "type": "boolean"}, {"name": "service_option", "type": "reference", "targetFactoryName": "SysServiceOption"}]}, "rows": [["Y", null, "devTools"], ["Y", null, "isDemoTenant"], ["Y", null, "changelog"], ["Y", null, "notificationCenter"], ["Y", null, "authorizationServiceOption"], ["Y", null, "workflowOption"], ["Y", null, "synchronizationServiceOption"], ["Y", "N", "showCaseOptionHighLevel"], ["Y", "N", "showCaseWorkInProgressOption"], ["Y", "Y", "showCaseOption3"], ["Y", "N", "showCaseOption2"], ["Y", "N", "showCaseOption1"], ["Y", "N", "showCaseExperimentalOption"], ["Y", "N", "showCaseDiscountOption"]]}}}