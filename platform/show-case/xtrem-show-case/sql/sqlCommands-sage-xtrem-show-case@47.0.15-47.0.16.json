{"fromVersion": "47.0.15", "toVersion": "47.0.16", "gitHead": "1407da814beda9b810563b7965f7810c1dcca2b1", "commands": [{"isSysPool": true, "sql": ["", "", "CREATE EXTENSION IF NOT EXISTS pgcrypto;", ""]}, {"isSysPool": true, "sql": ["", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.get_config(setting_name varchar)", "RETURNS varchar AS", "$$", "declare", "    setting_value varchar;", "BEGIN", "    SELECT current_setting(setting_name) into setting_value;", "    RETURN setting_value;", "EXCEPTION", "    WHEN OTHERS THEN", "    RETURN '';", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.drop_triggers(schema_name varchar, name_filter varchar DEFAULT '') RETURNS integer AS ", "$$", "DECLARE", "    triggerRecord RECORD;", "    record_count integer;", "begin", "\trecord_count = 0;", "    FOR triggerRecord IN ", "    \tSELECT trigger_name, event_object_table ", "    \tFROM information_schema.triggers ", "    \tWHERE trigger_schema = schema_name AND (name_filter = '' OR event_object_table = name_filter)", "\tLOOP", "\t\trecord_count = record_count + 1;", "        EXECUTE 'DROP TRIGGER ' || triggerRecord.trigger_name || ' ON ' || schema_name || '.\"' || triggerRecord.event_object_table || '\";';", "    END LOOP;", "", "    RETURN record_count;", "END;", "$$ LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.drop_notify_functions(schema_name varchar, name_filter varchar DEFAULT '') RETURNS integer AS ", "$$", "DECLARE", "    triggerRecord RECORD;", "    record_count integer;", "BEGIN", "    record_count = 0;", "    FOR triggerRecord IN ", "    SELECT routine_name", "    FROM information_schema.routines", "    WHERE specific_schema = schema_name and routine_name IN (name_filter || '_notify_deleted', name_filter || '_notify_created', name_filter || '_notify_updated')", "    LOOP", "        record_count = record_count + 1;", "        EXECUTE 'DROP FUNCTION ' || schema_name || '.' || triggerRecord.routine_name || ';';", "    END LOOP;", "", "    RETURN record_count;", "END;", "$$ LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.insert_table()", "RETURNS TRIGGER", "AS", "$$", "    DECLARE", "        USER_ID VARCHAR;", "    BEGIN ", "        SELECT %%SCHEMA_NAME%%.get_config('xtrem.transaction_user_id') INTO USER_ID;", "        IF (USER_ID != '') THEN", "            NEW._create_user :=  CAST(USER_ID AS INT8);", "            NEW._update_user :=  CAST(USER_ID AS INT8);", "        END IF;", "        IF (NEW._create_stamp IS NULL) THEN", "            NEW._create_stamp := NOW();", "        END IF;", "        IF (NEW._update_stamp IS NULL) THEN", "            NEW._update_stamp := NOW();", "        END IF;", "        IF (NEW._update_tick IS NULL) THEN", "            NEW._update_tick := 1;", "        END IF;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.insert_shared_table()", "RETURNS TRIGGER", "AS", "$$", "    BEGIN ", "        IF (NEW._create_stamp IS NULL) THEN", "            NEW._create_stamp := NOW();", "        END IF;", "        IF (NEW._update_stamp IS NULL) THEN", "            NEW._update_stamp := NOW();", "        END IF;", "        IF (NEW._update_tick IS NULL) THEN", "            NEW._update_tick := 1;", "        END IF;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.update_table()", "RETURNS TRIGGER", "AS", "$$", "    DECLARE", "        USER_ID VARCHAR;", "    BEGIN", "        SELECT %%SCHEMA_NAME%%.get_config('xtrem.transaction_user_id') INTO USER_ID;", "        IF (USER_ID != '') THEN", "            NEW._update_user :=  CAST(USER_ID AS INT8);", "        END IF;", "        NEW._update_stamp := NOW();", "        NEW._update_tick := OLD._update_tick + 1;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.update_shared_table()", "RETURNS TRIGGER", "AS", "$$", "    BEGIN", "        NEW._update_stamp := NOW();", "        NEW._update_tick := OLD._update_tick + 1;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.set_sync_tick()", "RETURNS TRIGGER", "AS", "$$", "    BEGIN ", "        NEW._sync_tick :=  pg_current_xact_id();", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.signedInt32(a bigint)", "RETURNS bigint AS", "$$", "DECLARE", "BEGIN", "\t-- Convert to 32 bit signed (if leftmost bit is 1, it's a negative number)", "  \tIF (a > 2^31) THEN", "    \tRETURN a - (2^32)::bigint;", "  \tEND IF;", "  \tRETURN a;", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.imul(a bigint, b bigint)", "RETURNS bigint AS", "$$", "DECLARE", "    aHi bigint;", "    aLo bigint;", "    bHi bigint;", "    bLo bigint;", "    res bigint;", "BEGIN", "    aHi = %%SCHEMA_NAME%%.zeroFillShift(a, 16) & 65535;", "    aLo = a & 65535;", "    bHi = %%SCHEMA_NAME%%.zeroFillShift(b, 16) & 65535;", "    bLo = b & 65535;", "    res = ((aLo * bLo) + %%SCHEMA_NAME%%.zeroFillShift(((aHi * bLo + aLo * bHi) << 16) % (2^32)::bigint, 0)) | 0;", "    RETURN %%SCHEMA_NAME%%.signedInt32(res);", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.zeroFillShift(a bigint, b int)", "RETURNS bigint AS", "$$", "DECLARE", "  \tres bigint;", "BEGIN", "\tIF (a < 0) THEN", "\t\tres = a + 2^32;", "\tELSE", "\t\tres = a;", "\tEND IF;", "\tres = res >> b;", "\tRETURN res;", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.nanoid(\"size\" int4 DEFAULT 21)", "    RETURNS text", "    LANGUAGE plpgsql", "    STABLE", "    AS", "    $$", "    DECLARE", "        id text := '';", "        i int := 0;", "        urlAl<PERSON><PERSON> char(64) := 'ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW';", "        bytes bytea;", "        byte int;", "        pos int;", "    BEGIN", "        SELECT gen_random_bytes(size) INTO bytes;", "    WHILE i < size LOOP", "        byte := get_byte(bytes, i);", "        pos := (byte & 63) + 1; -- + 1 because substr starts at 1", "        id := id || substr(urlAlphabet, pos, 1);", "        i = i + 1;", "    END LOOP;", "    RETURN id;", "    END", "    $$", "    ;", " ", ""]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Is a demo tenant", "released", false, "@sage/xtrem-system", false, "isDemoTenant"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["enable dev-only features", "experimental", false, "@sage/xtrem-system", false, "devTools"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["allow to display changelog in the app", "experimental", false, "@sage/xtrem-system", false, "changelog"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Notification center", "experimental", false, "@sage/xtrem-communication", false, "notificationCenter"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Authorization access control", "released", false, "@sage/xtrem-authorization", false, "authorizationServiceOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Workflow option", "workInProgress", false, "@sage/xtrem-workflow", false, "workflowOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Synchronization", "experimental", false, "@sage/xtrem-interop", false, "synchronizationServiceOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["showCase discount option", "experimental", false, "@sage/xtrem-show-case", false, "showCaseDiscountOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["experimental option", "experimental", false, "@sage/xtrem-show-case", false, "showCaseExperimentalOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["showCaseOption's hight level description", "released", false, "@sage/xtrem-show-case", false, "showCaseOptionHighLevel"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["showCaseOption1's description", "released", true, "@sage/xtrem-show-case", false, "showCaseOption1"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["showCaseOption2's description", "released", true, "@sage/xtrem-show-case", false, "showCaseOption2"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["A workInProgress service option that can be loaded on a development environment", "workInProgress", false, "@sage/xtrem-show-case", true, "showCaseOption3"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Work in progress options cannot be activated", "workInProgress", false, "@sage/xtrem-show-case", false, "showCaseWorkInProgressOption"]}, {"isSysPool": false, "sql": "NOTIFY invalidate_category_cache, '{\"data\":\"{\\\"tenantId\\\":null,\\\"category\\\":\\\"$SHARED_NODE.SysServiceOption\\\"}\",\"containerId\":\"x3-devops00DGRF-64729\",\"excludeSelf\":true}';", "args": []}, {"isSysPool": true, "sql": ["COMMENT ON COLUMN %%SCHEMA_NAME%%.sys_customer._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.sys_tenant._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.sys_changelog._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.sys_csv_checksum._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.sys_pack_version._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.sys_service_option._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.sys_service_option_to_service_option._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.sys_upgrade._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.sys_vendor._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.user._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.user_navigation._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.user_preferences._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.sys_message._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.sys_message_history._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.sys_notification._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.sys_notification_history._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.activity._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.group_role_site._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.restricted_node._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.restricted_node_user_grant._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.role._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.role_activity._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.role_to_role._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.site_group._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.site_group_to_site_group._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.support_access_history._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.user_billing_role._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.user_group._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.meta_activity._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.meta_activity_permission._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.meta_node_factory._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.meta_node_operation._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.meta_node_property._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.meta_package._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.meta_service_option._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.custom_field._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.dashboard._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.dashboard_item._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.widget_category._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.workflow_diagram._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.sys_job_schedule._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.attachment._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.attachment_association._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.uploaded_file._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.import_export_template._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.import_result._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.sys_app._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.sys_node_transformation._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.sys_operation_transformation._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.sys_synchronization_state._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.report_resource._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.report_style_variable._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.report_template._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.report_translatable_text._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.report_wizard._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.show_case_country._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.show_case_customer._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.show_case_employee._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.show_case_item._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.show_case_node_browser_tree._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.show_case_order._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.show_case_provider._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.show_case_provider_address._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.show_case_time._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.test_async_mutation_on_specific_queue._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.show_case_bill_of_material._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.sys_global_lock._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.company._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.locale._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.site._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.sys_client_notification._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.sys_client_notification_action._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.sys_client_user_settings._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.sys_custom_record._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.sys_custom_sql_history._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.sys_pack_allocation._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.sys_patch_history._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.sys_service_option_state._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.sys_notification_state._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.group_role._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.group_site._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.site_group_to_site._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.meta_data_type._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.workflow_definition._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.workflow_process._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.sys_enum_transformation._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.sys_node_mapping._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.report._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.report_variable._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.show_case_invoice._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.show_case_product._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.show_case_product_origin_address._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.show_case_component._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.sys_notification_log_entry._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.sys_enum_mapping._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.show_case_invoice_line._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';"]}], "data": {}}