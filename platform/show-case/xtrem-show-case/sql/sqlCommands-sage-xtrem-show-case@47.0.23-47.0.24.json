{"fromVersion": "47.0.23", "toVersion": "47.0.24", "gitHead": "216483a0897602a4dc3f002ec78c28f97d7b452c", "commands": [{"isSysPool": true, "sql": ["", "", "CREATE EXTENSION IF NOT EXISTS pgcrypto;", ""]}, {"isSysPool": true, "sql": ["", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.get_config(setting_name varchar)", "RETURNS varchar AS", "$$", "declare", "    setting_value varchar;", "BEGIN", "    SELECT current_setting(setting_name) into setting_value;", "    RETURN setting_value;", "EXCEPTION", "    WHEN OTHERS THEN", "    RETURN '';", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.drop_triggers(schema_name varchar, name_filter varchar DEFAULT '') RETURNS integer AS", "$$", "DECLARE", "    triggerRecord RECORD;", "    record_count integer;", "begin", "\trecord_count = 0;", "    FOR triggerRecord IN", "    \tSELECT trigger_name, event_object_table", "    \tFROM information_schema.triggers", "    \tWHERE trigger_schema = schema_name AND (name_filter = '' OR event_object_table = name_filter)", "\tLOOP", "\t\trecord_count = record_count + 1;", "        EXECUTE 'DROP TRIGGER ' || triggerRecord.trigger_name || ' ON ' || schema_name || '.\"' || triggerRecord.event_object_table || '\";';", "    END LOOP;", "", "    RETURN record_count;", "END;", "$$ LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.drop_notify_functions(schema_name varchar, name_filter varchar DEFAULT '') RETURNS integer AS", "$$", "DECLARE", "    triggerRecord RECORD;", "    record_count integer;", "BEGIN", "    record_count = 0;", "    FOR triggerRecord IN", "    SELECT routine_name", "    FROM information_schema.routines", "    WHERE specific_schema = schema_name and routine_name IN (name_filter || '_notify_deleted', name_filter || '_notify_created', name_filter || '_notify_updated')", "    LOOP", "        record_count = record_count + 1;", "        EXECUTE 'DROP FUNCTION ' || schema_name || '.' || triggerRecord.routine_name || ';';", "    END LOOP;", "", "    RETURN record_count;", "END;", "$$ LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.insert_table()", "RETURNS TRIGGER", "AS", "$$", "    DECLARE", "        USER_ID VARCHAR;", "    BEGIN", "        SELECT %%SCHEMA_NAME%%.get_config('xtrem.transaction_user_id') INTO USER_ID;", "        IF (USER_ID != '') THEN", "            NEW._create_user :=  CAST(USER_ID AS INT8);", "            NEW._update_user :=  CAST(USER_ID AS INT8);", "        END IF;", "        IF (NEW._create_stamp IS NULL) THEN", "            NEW._create_stamp := NOW();", "        END IF;", "        IF (NEW._update_stamp IS NULL) THEN", "            NEW._update_stamp := NOW();", "        END IF;", "        IF (NEW._update_tick IS NULL) THEN", "            NEW._update_tick := 1;", "        END IF;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.insert_shared_table()", "RETURNS TRIGGER", "AS", "$$", "    BEGIN", "        IF (NEW._create_stamp IS NULL) THEN", "            NEW._create_stamp := NOW();", "        END IF;", "        IF (NEW._update_stamp IS NULL) THEN", "            NEW._update_stamp := NOW();", "        END IF;", "        IF (NEW._update_tick IS NULL) THEN", "            NEW._update_tick := 1;", "        END IF;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.update_table()", "RETURNS TRIGGER", "AS", "$$", "    DECLARE", "        USER_ID VARCHAR;", "    BEGIN", "        SELECT %%SCHEMA_NAME%%.get_config('xtrem.transaction_user_id') INTO USER_ID;", "        IF (USER_ID != '') THEN", "            NEW._update_user :=  CAST(USER_ID AS INT8);", "        END IF;", "        NEW._update_stamp := NOW();", "        NEW._update_tick := OLD._update_tick + 1;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.update_shared_table()", "RETURNS TRIGGER", "AS", "$$", "    BEGIN", "        NEW._update_stamp := NOW();", "        NEW._update_tick := OLD._update_tick + 1;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.set_sync_tick()", "RETURNS TRIGGER", "AS", "$$", "    BEGIN", "        NEW._sync_tick :=  pg_current_xact_id();", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.signedInt32(a bigint)", "RETURNS bigint AS", "$$", "DECLARE", "BEGIN", "\t-- Convert to 32 bit signed (if leftmost bit is 1, it's a negative number)", "  \tIF (a > 2^31) THEN", "    \tRETURN a - (2^32)::bigint;", "  \tEND IF;", "  \tRETURN a;", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.imul(a bigint, b bigint)", "RETURNS bigint AS", "$$", "DECLARE", "    aHi bigint;", "    aLo bigint;", "    bHi bigint;", "    bLo bigint;", "    res bigint;", "BEGIN", "    aHi = %%SCHEMA_NAME%%.zeroFillShift(a, 16) & 65535;", "    aLo = a & 65535;", "    bHi = %%SCHEMA_NAME%%.zeroFillShift(b, 16) & 65535;", "    bLo = b & 65535;", "    res = ((aLo * bLo) + %%SCHEMA_NAME%%.zeroFillShift(((aHi * bLo + aLo * bHi) << 16) % (2^32)::bigint, 0)) | 0;", "    RETURN %%SCHEMA_NAME%%.signedInt32(res);", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.zeroFillShift(a bigint, b int)", "RETURNS bigint AS", "$$", "DECLARE", "  \tres bigint;", "BEGIN", "\tIF (a < 0) THEN", "\t\tres = a + 2^32;", "\tELSE", "\t\tres = a;", "\tEND IF;", "\tres = res >> b;", "\tRETURN res;", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.nanoid(\"size\" int4 DEFAULT 21)", "    RETURNS text", "    LANGUAGE plpgsql", "    STABLE", "    AS", "    $$", "    DECLARE", "        id text := '';", "        i int := 0;", "        urlAl<PERSON><PERSON> char(64) := 'ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW';", "        bytes bytea;", "        byte int;", "        pos int;", "    BEGIN", "        SELECT gen_random_bytes(size) INTO bytes;", "    WHILE i < size LOOP", "        byte := get_byte(bytes, i);", "        pos := (byte & 63) + 1; -- + 1 because substr starts at 1", "        id := id || substr(urlAlphabet, pos, 1);", "        i = i + 1;", "    END LOOP;", "    RETURN id;", "    END", "    $$", "    ;", " ", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.audit_table()", "RETURNS TRIGGER", "AS", "$$", "    DECLARE", "        is_audit_enabled BOOLEAN;", "        p_root_table_name VARCHAR;", "        login_email VARCHAR;", "        user_id INT8;", "        log_record RECORD;", "    BEGIN", "        SELECT %%SCHEMA_NAME%%.get_config('xtrem.is_audit_enabled') INTO is_audit_enabled;", "        IF NOT is_audit_enabled THEN", "            RETURN NEW;", "        END IF;", "        p_root_table_name := TG_ARGV[0];", "        SELECT %%SCHEMA_NAME%%.get_config('xtrem.login_email') INTO login_email;", "        SELECT %%SCHEMA_NAME%%.get_config('xtrem.transaction_user_id')::INT8 INTO user_id;", "        SELECT * FROM %%SCHEMA_NAME%%.sys_audit_log", "        WHERE root_table_name = p_root_table_name", "            AND record_id = COALESCE(NEW._id, OLD._id)", "            AND transaction_id::TEXT = pg_current_xact_id()::TEXT", "        INTO log_record;", "        IF log_record IS NULL THEN", "            RAISE NOTICE 'Inserting new audit log record %:%', p_root_table_name, NEW._id;", "            IF p_root_table_name = TG_TABLE_NAME THEN", "                INSERT INTO %%SCHEMA_NAME%%.sys_audit_log (root_table_name, _tenant_id, record_id, operation, login_email, timestamp, transaction_id, record_data, old_update_tick, new_update_tick, _create_user, _update_user)", "                VALUES (p_root_table_name, COALESCE(NEW._tenant_id, OLD._tenant_id), COALESCE(NEW._id, OLD._id), TG_OP::%%SCHEMA_NAME%%.audit_operation_enum, login_email, NOW(), pg_current_xact_id(), to_json(NEW), OLD._update_tick, NEW._update_tick, user_id, user_id);", "            ELSE", "                INSERT INTO %%SCHEMA_NAME%%.sys_audit_log (root_table_name, _tenant_id, record_id, operation, login_email, timestamp, transaction_id, record_data, old_update_tick, new_update_tick)", "                VALUES (p_root_table_name, COALESCE(NEW._tenant_id, OLD._tenant_id), COALESCE(NEW._id, OLD._id), TG_OP::%%SCHEMA_NAME%%.audit_operation_enum, login_email, NOW(), pg_current_xact_id(), to_json(NEW), NULL, NULL);", "            END IF;", "            RAISE NOTICE 'Inserted  new audit log record root_table=%, table=%, _id=%', p_root_table_name, TG_TABLE_NAME, NEW._id;", "        ELSE", "            RAISE NOTICE 'Updating audit log record %:%', p_root_table_name, NEW._id;", "            UPDATE %%SCHEMA_NAME%%.sys_audit_log", "            SET record_data = log_record.record_data || to_jsonb(NEW)", "            WHERE root_table_name = p_root_table_name", "                AND record_id = NEW._id", "                AND transaction_id = pg_current_xact_id()::TEXT;", "            RAISE NOTICE 'Updated  audit log record %:%', p_root_table_name, NEW._id;", "        END IF;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", ""]}, {"isSysPool": true, "sql": ["", "                DO $$", "                    BEGIN", "                        IF NOT EXISTS (", "                        SELECT 1 FROM pg_type t", "                        LEFT JOIN pg_namespace p ON t.typnamespace=p.oid", "                        WHERE t.typname='audit_operation_enum' AND p.nspname='%%SCHEMA_NAME%%'", "                        ) THEN", "                            CREATE TYPE %%SCHEMA_NAME%%.audit_operation_enum AS ENUM('INSERT','UPDATE','DELETE');", "                        END IF;", "                    END", "                $$;", "                "]}, {"isSysPool": true, "sql": ["CREATE TABLE %%SCHEMA_NAME%%.sys_audit_log (_tenant_id VARCHAR(21) COLLATE \"und-x-icu\" DEFAULT '' NOT NULL, _id SERIAL8 NOT NULL, root_table_name VARCHAR(80) COLLATE \"und-x-icu\" DEFAULT '' NOT NULL, record_id INT8 NOT NULL, operation %%SCHEMA_NAME%%.audit_operation_enum NOT NULL, login_email VARCHAR(100) COLLATE \"und-x-icu\" DEFAULT '' NOT NULL, timestamp TIMESTAMPTZ(3) NOT NULL, transaction_id VARCHAR(16) COLLATE \"und-x-icu\" DEFAULT '' NOT NULL, old_update_tick INT8, new_update_tick INT8, record_data JSONB, _create_user INT8 NOT NULL, _update_user INT8 NOT NULL, _create_stamp TIMESTAMPTZ(3) DEFAULT now() NOT NULL, _update_stamp TIMESTAMPTZ(3) DEFAULT now() NOT NULL, _update_tick INT8 NOT NULL, _source_id VARCHAR(128) COLLAT<PERSON> \"und-x-icu\" DEFAULT '' NOT NULL,CONSTRAINT \"sys_audit_log_PK\" PRIMARY KEY(_tenant_id,_id));CREATE UNIQUE INDEX sys_audit_log_ind0 ON %%SCHEMA_NAME%%.sys_audit_log(_tenant_id ASC,root_table_name ASC,record_id ASC,COALESCE(new_update_tick, (- ((2)::bigint ^ (62)::bigint))::bigint) ASC);", "CREATE UNIQUE INDEX sys_audit_log_ind1 ON %%SCHEMA_NAME%%.sys_audit_log(_tenant_id ASC,root_table_name ASC,transaction_id ASC,record_id ASC);", "CREATE UNIQUE INDEX sys_audit_log_ind2 ON %%SCHEMA_NAME%%.sys_audit_log(_tenant_id ASC,login_email ASC,timestamp ASC,_id ASC);", "COMMENT ON TABLE %%SCHEMA_NAME%%.sys_audit_log IS '{", "  \"isSharedByAllTenants\": false", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.sys_audit_log._tenant_id IS '{", "  \"type\": \"string\",", "  \"isSystem\": true,", "  \"maxLength\": 21", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.sys_audit_log._id IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true,", "  \"isAutoIncrement\": true", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.sys_audit_log.root_table_name IS '{", "  \"type\": \"string\",", "  \"isSystem\": false,", "  \"maxLength\": 80", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.sys_audit_log.record_id IS '{", "  \"type\": \"integer\",", "  \"isSystem\": false", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.sys_audit_log.operation IS '{", "  \"type\": \"enum\",", "  \"isSystem\": false,", "  \"enumTypeName\": \"audit_operation_enum\"", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.sys_audit_log.login_email IS '{", "  \"type\": \"string\",", "  \"isSystem\": false,", "  \"maxLength\": 100", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.sys_audit_log.timestamp IS '{", "  \"type\": \"datetime\",", "  \"isSystem\": false", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.sys_audit_log.transaction_id IS '{", "  \"type\": \"string\",", "  \"isSystem\": false,", "  \"maxLength\": 16", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.sys_audit_log.old_update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": false", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.sys_audit_log.new_update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": false", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.sys_audit_log.record_data IS '{", "  \"type\": \"json\",", "  \"isSystem\": false", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.sys_audit_log._create_user IS '{", "  \"type\": \"reference\",", "  \"isSystem\": true,", "  \"targetTableName\": \"user\",", "  \"isSelfReference\": false", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.sys_audit_log._update_user IS '{", "  \"type\": \"reference\",", "  \"isSystem\": true,", "  \"targetTableName\": \"user\",", "  \"isSelfReference\": false", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.sys_audit_log._create_stamp IS '{", "  \"type\": \"datetime\",", "  \"isSystem\": true", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.sys_audit_log._update_stamp IS '{", "  \"type\": \"datetime\",", "  \"isSystem\": true", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.sys_audit_log._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.sys_audit_log._source_id IS '{", "  \"type\": \"string\",", "  \"isSystem\": true,", "  \"maxLength\": 128", "}';"]}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER insert_table", "            BEFORE INSERT ON %%SCHEMA_NAME%%.sys_audit_log", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.insert_table();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;", "DO $$ BEGIN", "            CREATE TRIGGER update_table", "            BEFORE UPDATE ON %%SCHEMA_NAME%%.sys_audit_log", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.update_table();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": "GRANT DELETE, INSERT, UPDATE, SELECT on %%SCHEMA_NAME%%.sys_audit_log to xtrem"}, {"isSysPool": true, "sql": "GRANT USAGE, SELECT ON SEQUENCE %%SCHEMA_NAME%%.sys_audit_log__id_seq TO xtrem"}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.sys_audit_log ADD CONSTRAINT \"sys_audit_log__update_user_fk\" FOREIGN KEY(_tenant_id,_update_user) REFERENCES %%SCHEMA_NAME%%.user(_tenant_id,_id) ON DELETE RESTRICT DEFERRABLE DEFERRABLE;", "COMMENT ON CONSTRAINT sys_audit_log__update_user_fk ON %%SCHEMA_NAME%%.sys_audit_log IS '{", "  \"targetTableName\": \"user\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"_update_user\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"restrict\",", "  \"isDeferrable\": true", "}';;"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.sys_audit_log ADD CONSTRAINT \"sys_audit_log__create_user_fk\" FOREIGN KEY(_tenant_id,_create_user) REFERENCES %%SCHEMA_NAME%%.user(_tenant_id,_id) ON DELETE RESTRICT DEFERRABLE DEFERRABLE;", "COMMENT ON CONSTRAINT sys_audit_log__create_user_fk ON %%SCHEMA_NAME%%.sys_audit_log IS '{", "  \"targetTableName\": \"user\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"_create_user\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"restrict\",", "  \"isDeferrable\": true", "}';;"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.sys_audit_log ADD CONSTRAINT \"sys_audit_log__tenant_id_fk\" FOREIGN KEY(_tenant_id) REFERENCES %%SCHEMA_NAME%%.sys_tenant(tenant_id) ON DELETE RESTRICT DEFERRABLE DEFERRABLE;", "COMMENT ON CONSTRAINT sys_audit_log__tenant_id_fk ON %%SCHEMA_NAME%%.sys_audit_log IS '{", "  \"targetTableName\": \"sys_tenant\",", "  \"columns\": {", "    \"_tenant_id\": \"tenant_id\"", "  },", "  \"onDeleteBehaviour\": \"restrict\",", "  \"isDeferrable\": true", "}';;"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Is a demo tenant", "released", false, "@sage/xtrem-system", false, "isDemoTenant"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["enable dev-only features", "experimental", false, "@sage/xtrem-system", false, "devTools"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["allow to display changelog in the app", "experimental", false, "@sage/xtrem-system", false, "changelog"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Notification center", "experimental", false, "@sage/xtrem-communication", false, "notificationCenter"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Authorization access control", "released", false, "@sage/xtrem-authorization", false, "authorizationServiceOption"]}, {"isSysPool": false, "sql": ["INSERT INTO %%SCHEMA_NAME%%.sys_service_option", "(_update_tick,_source_id,package,option_name,description,status,is_hidden,is_active_by_default)", "VALUES ($1,$2,$3,$4,$5,$6,$7,$8)", "RETURNING _create_stamp,_update_stamp,_id"], "args": [1, "", "@sage/xtrem-auditing", "auditingOption", "Auditing option", "workInProgress", false, false]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Workflow option", "workInProgress", false, "@sage/xtrem-workflow", false, "workflowOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Synchronization", "experimental", false, "@sage/xtrem-interop", false, "synchronizationServiceOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["showCase discount option", "experimental", false, "@sage/xtrem-show-case", false, "showCaseDiscountOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["experimental option", "experimental", false, "@sage/xtrem-show-case", false, "showCaseExperimentalOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["showCaseOption's hight level description", "released", false, "@sage/xtrem-show-case", false, "showCaseOptionHighLevel"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["showCaseOption1's description", "released", true, "@sage/xtrem-show-case", false, "showCaseOption1"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["showCaseOption2's description", "released", true, "@sage/xtrem-show-case", false, "showCaseOption2"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["A workInProgress service option that can be loaded on a development environment", "workInProgress", false, "@sage/xtrem-show-case", true, "showCaseOption3"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Work in progress options cannot be activated", "workInProgress", false, "@sage/xtrem-show-case", false, "showCaseWorkInProgressOption"]}, {"isSysPool": false, "sql": "NOTIFY invalidate_category_cache, '{\"data\":\"{\\\"tenantId\\\":null,\\\"category\\\":\\\"$SHARED_NODE.SysServiceOption\\\"}\",\"containerId\":\"x3-devops00DPL8-64748\",\"excludeSelf\":true}';", "args": []}, {"action": "reload_setup_data", "args": {"factory": "SysNodeTransformation"}}, {"action": "reload_setup_data", "args": {"factory": "SysNodeMapping"}}, {"isSysPool": true, "sql": ["COMMENT ON CONSTRAINT sys_audit_log__update_user_fk ON %%SCHEMA_NAME%%.sys_audit_log IS '{", "  \"targetTableName\": \"user\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"_update_user\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"restrict\",", "  \"isDeferrable\": true", "}';;COMMENT ON CONSTRAINT sys_audit_log__create_user_fk ON %%SCHEMA_NAME%%.sys_audit_log IS '{", "  \"targetTableName\": \"user\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"_create_user\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"restrict\",", "  \"isDeferrable\": true", "}';;COMMENT ON CONSTRAINT sys_audit_log__tenant_id_fk ON %%SCHEMA_NAME%%.sys_audit_log IS '{", "  \"targetTableName\": \"sys_tenant\",", "  \"columns\": {", "    \"_tenant_id\": \"tenant_id\"", "  },", "  \"onDeleteBehaviour\": \"restrict\",", "  \"isDeferrable\": true", "}';"]}], "data": {"SysNodeTransformation": {"metadata": {"rootFactoryName": "SysNodeTransformation", "name": "SysNodeTransformation", "naturalKeyColumns": ["_tenant_id", "id"], "columns": [{"name": "_vendor", "type": "reference", "isNullable": true, "targetFactoryName": "SysVendor"}, {"name": "id", "type": "string"}, {"name": "is_active", "type": "boolean", "isOwnedByCustomer": true}, {"name": "remote_app", "type": "reference", "targetFactoryName": "SysApp"}, {"name": "remote_app_version", "type": "string"}, {"name": "remote_node_name", "type": "string"}, {"name": "local_node", "type": "reference", "targetFactoryName": "MetaNodeFactory"}, {"name": "local_node_full_name", "type": "string"}, {"name": "filter", "type": "json", "isNullable": true}, {"name": "version", "type": "string"}, {"name": "last_error", "type": "date", "isNullable": true}, {"name": "last_sync", "type": "date", "isNullable": true}]}, "rows": []}, "SysNodeMapping": {"metadata": {"isVitalChild": true, "isVitalCollectionChild": true, "rootFactoryName": "SysNodeMapping", "name": "SysNodeMapping", "naturalKeyColumns": ["_tenant_id", "transform", "_sort_value"], "columns": [{"name": "_vendor", "type": "reference", "isNullable": true, "targetFactoryName": "SysVendor"}, {"name": "_sort_value", "type": "integer"}, {"name": "transform", "type": "reference", "targetFactoryName": "SysNodeTransformation"}, {"name": "local_property", "type": "string"}, {"name": "remote_property", "type": "string"}, {"name": "kind", "type": "enum", "enumMembers": ["path_to_path", "path_to_constant", "path_to_function", "constant_to_path", "function_to_path"]}], "vitalParentColumn": {"name": "transform", "type": "reference", "targetFactoryName": "SysNodeTransformation"}}, "rows": []}}}