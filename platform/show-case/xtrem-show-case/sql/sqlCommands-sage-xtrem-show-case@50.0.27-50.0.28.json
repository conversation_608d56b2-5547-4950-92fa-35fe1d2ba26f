{"fromVersion": "50.0.27", "toVersion": "50.0.28", "gitHead": "886a33ddcc6cd091eac485958f9e1b7ac356b305", "commands": [{"isSysPool": true, "sql": ["", "", "CREATE EXTENSION IF NOT EXISTS pgcrypto;", ""]}, {"isSysPool": true, "sql": ["", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.get_config(setting_name varchar)", "RETURNS varchar AS", "$$", "declare", "    setting_value varchar;", "BEGIN", "    SELECT current_setting(setting_name) into setting_value;", "    RETURN setting_value;", "EXCEPTION", "    WHEN OTHERS THEN", "    RETURN NULL;", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.drop_triggers(schema_name varchar, name_filter varchar DEFAULT '') RETURNS integer AS", "$$", "DECLARE", "    triggerRecord RECORD;", "    record_count integer;", "begin", "\trecord_count = 0;", "    FOR triggerRecord IN", "    \tSELECT trigger_name, event_object_table", "    \tFROM information_schema.triggers", "    \tWHERE trigger_schema = schema_name AND (name_filter = '' OR event_object_table = name_filter)", "\tLOOP", "\t\trecord_count = record_count + 1;", "        EXECUTE 'DROP TRIGGER ' || triggerRecord.trigger_name || ' ON ' || schema_name || '.\"' || triggerRecord.event_object_table || '\";';", "    END LOOP;", "", "    RETURN record_count;", "END;", "$$ LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.drop_notify_functions(schema_name varchar, name_filter varchar DEFAULT '') RETURNS integer AS", "$$", "DECLARE", "    triggerRecord RECORD;", "    record_count integer;", "BEGIN", "    record_count = 0;", "    FOR triggerRecord IN", "    SELECT routine_name", "    FROM information_schema.routines", "    WHERE specific_schema = schema_name and routine_name IN (name_filter || '_notify_deleted', name_filter || '_notify_created', name_filter || '_notify_updated')", "    LOOP", "        record_count = record_count + 1;", "        EXECUTE 'DROP FUNCTION ' || schema_name || '.' || triggerRecord.routine_name || ';';", "    END LOOP;", "", "    RETURN record_count;", "END;", "$$ LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.insert_table()", "RETURNS TRIGGER", "AS", "$$", "    DECLARE", "        USER_ID VARCHAR;", "    BEGIN", "        SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.transaction_user_id'), '') INTO USER_ID;", "        IF (USER_ID <> '') THEN", "            NEW._create_user :=  CAST(USER_ID AS INT8);", "            NEW._update_user :=  CAST(USER_ID AS INT8);", "        END IF;", "        IF (NEW._create_stamp IS NULL) THEN", "            NEW._create_stamp := NOW();", "        END IF;", "        IF (NEW._update_stamp IS NULL) THEN", "            NEW._update_stamp := NOW();", "        END IF;", "        IF (NEW._update_tick IS NULL) THEN", "            NEW._update_tick := 1;", "        END IF;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.insert_shared_table()", "RETURNS TRIGGER", "AS", "$$", "    BEGIN", "        IF (NEW._create_stamp IS NULL) THEN", "            NEW._create_stamp := NOW();", "        END IF;", "        IF (NEW._update_stamp IS NULL) THEN", "            NEW._update_stamp := NOW();", "        END IF;", "        IF (NEW._update_tick IS NULL) THEN", "            NEW._update_tick := 1;", "        END IF;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.update_table()", "RETURNS TRIGGER", "AS", "$$", "    DECLARE", "        USER_ID VARCHAR;", "    BEGIN", "        SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.transaction_user_id'), '') INTO USER_ID;", "        IF (USER_ID <> '') THEN", "            NEW._update_user :=  CAST(USER_ID AS INT8);", "        END IF;", "        NEW._update_stamp := NOW();", "        NEW._update_tick := OLD._update_tick + 1;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.update_shared_table()", "RETURNS TRIGGER", "AS", "$$", "    BEGIN", "        NEW._update_stamp := NOW();", "        NEW._update_tick := OLD._update_tick + 1;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.set_sync_tick()", "RETURNS TRIGGER", "AS", "$$", "    BEGIN", "        NEW._sync_tick :=  pg_current_xact_id();", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.signedInt32(a bigint)", "RETURNS bigint AS", "$$", "DECLARE", "BEGIN", "\t-- Convert to 32 bit signed (if leftmost bit is 1, it's a negative number)", "  \tIF (a > 2^31) THEN", "    \tRETURN a - (2^32)::bigint;", "  \tEND IF;", "  \tRETURN a;", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.imul(a bigint, b bigint)", "RETURNS bigint AS", "$$", "DECLARE", "    aHi bigint;", "    aLo bigint;", "    bHi bigint;", "    bLo bigint;", "    res bigint;", "BEGIN", "    aHi = %%SCHEMA_NAME%%.zeroFillShift(a, 16) & 65535;", "    aLo = a & 65535;", "    bHi = %%SCHEMA_NAME%%.zeroFillShift(b, 16) & 65535;", "    bLo = b & 65535;", "    res = ((aLo * bLo) + %%SCHEMA_NAME%%.zeroFillShift(((aHi * bLo + aLo * bHi) << 16) % (2^32)::bigint, 0)) | 0;", "    RETURN %%SCHEMA_NAME%%.signedInt32(res);", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.zeroFillShift(a bigint, b int)", "RETURNS bigint AS", "$$", "DECLARE", "  \tres bigint;", "BEGIN", "\tIF (a < 0) THEN", "\t\tres = a + 2^32;", "\tELSE", "\t\tres = a;", "\tEND IF;", "\tres = res >> b;", "\tRETURN res;", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.nanoid(\"size\" int4 DEFAULT 21)", "    RETURNS text", "    LANGUAGE plpgsql", "    STABLE", "    AS", "    $$", "    DECLARE", "        id text := '';", "        i int := 0;", "        urlAl<PERSON><PERSON> char(64) := 'ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW';", "        bytes bytea;", "        byte int;", "        pos int;", "    BEGIN", "        SELECT gen_random_bytes(size) INTO bytes;", "    WHILE i < size LOOP", "        byte := get_byte(bytes, i);", "        pos := (byte & 63) + 1; -- + 1 because substr starts at 1", "        id := id || substr(urlAlphabet, pos, 1);", "        i = i + 1;", "    END LOOP;", "    RETURN id;", "    END", "    $$", "    ;", " ", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.audit_table()", "RETURNS TRIGGER", "AS", "$$", "    DECLARE", "        -- parameters", "        p_root_table_name VARCHAR;", "        p_constructor VARCHAR;", "", "        -- audit variables", "        is_audit_enabled VARCHAR;", "        tenant_id VARCHAR;", "        rid INT8;", "        login_email VARCHAR;", "        user_id VARCHAR;", "        locale VARCHAR;", "        log_record RECORD;", "", "        -- notify variables", "        origin_id VARCHAR;", "        notify_all_disabled VARCHAR;", "        notify_tenant_disabled VARCHAR;", "        notification_id VARCHAR;", "        user_email VARCHAR;", "        constructor VARCHAR;", "        event VARCHAR;", "        topic VARCHAR;", "        envelope VARCHAR;", "        payload VARCHAR;", "    BEGIN", "        p_root_table_name := TG_ARGV[0];", "        p_constructor := TG_ARGV[1];", "", "        SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.is_audit_enabled'), 'false') INTO is_audit_enabled;", "        IF (is_audit_enabled <> 'true') THEN", "            RETURN NEW;", "        END IF;", "        SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.login_email'), '') INTO login_email;", "        SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.transaction_user_id'), '') INTO user_id;", "        SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.locale'), 'base') INTO locale;", "", "        tenant_id := COALESCE(NEW._tenant_id, OLD._tenant_id);", "        rid := COALESCE(NEW._id, OLD._id);", "", "        SELECT * FROM %%SCHEMA_NAME%%.sys_audit_log", "        WHERE root_table_name = p_root_table_name", "            AND record_id = rid", "            AND transaction_id::TEXT = pg_current_xact_id()::TEXT", "        INTO log_record;", "", "        IF log_record IS NULL THEN", "            RAISE NOTICE 'Inserting new audit log record %:%', p_root_table_name, NEW._id;", "            IF p_root_table_name = TG_TABLE_NAME THEN", "                INSERT INTO %%SCHEMA_NAME%%.sys_audit_log (root_table_name, _tenant_id, record_id, operation, login_email, timestamp, transaction_id, record_data, old_update_tick, new_update_tick, _create_user, _update_user)", "                VALUES (p_root_table_name, tenant_id, rid, TG_OP::%%SCHEMA_NAME%%.audit_operation_enum, login_email, NOW(), pg_current_xact_id(), to_json(NEW), OLD._update_tick, NEW._update_tick, user_id::INT8, user_id::INT8);", "            ELSE", "                INSERT INTO %%SCHEMA_NAME%%.sys_audit_log (root_table_name, _tenant_id, record_id, operation, login_email, timestamp, transaction_id, record_data, old_update_tick, new_update_tick)", "                VALUES (p_root_table_name, tenant_id, rid, TG_OP::%%SCHEMA_NAME%%.audit_operation_enum, login_email, NOW(), pg_current_xact_id(), to_json(NEW), NULL, NULL);", "            END IF;", "            RAISE NOTICE 'Inserted  new audit log record root_table=%, table=%, _id=%', p_root_table_name, TG_TABLE_NAME, NEW._id;", "        ELSE", "            RAISE NOTICE 'Updating audit log record %:%', p_root_table_name, NEW._id;", "            UPDATE %%SCHEMA_NAME%%.sys_audit_log", "            SET record_data = log_record.record_data || to_jsonb(NEW)", "            WHERE root_table_name = p_root_table_name", "                AND record_id = NEW._id", "                AND transaction_id = pg_current_xact_id()::TEXT;", "            RAISE NOTICE 'Updated  audit log record %:%', p_root_table_name, NEW._id;", "        END IF;", "", "        IF p_root_table_name = TG_TABLE_NAME THEN", "            SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.notification.disable.ALL'), 'false') INTO notify_all_disabled;", "            SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.notification.disable.t_' || tenant_id), 'false') INTO notify_tenant_disabled;", "", "            IF (notify_all_disabled <> 'true' and notify_tenant_disabled <> 'true') THEN", "                SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.origin_id'), '') INTO origin_id;", "                SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.user_email'), '') INTO user_email;", "                SELECT %%SCHEMA_NAME%%.nanoid() INTO notification_id;", "", "                IF p_constructor != '' THEN", "                    constructor := p_constructor;", "                ELSE", "                    constructor := COALESCE(NEW._constructor, OLD._constructor);", "                END IF;", "", "                CASE TG_OP", "                    WHEN 'INSERT' THEN event := 'created';", "                    WHEN 'UPDATE' THEN event := 'updated';", "                    WHEN 'DELETE' THEN event := 'deleted';", "                END CASE;", "", "                topic := constructor || '/' || event;", "                payload := '{ \"_id\":' || rid || ', \"_updateTick\":' || COALESCE(NEW._update_tick, OLD._update_tick) || '}';", "", "                RAISE NOTICE 'Inserted new notification %:%', topic, notification_id;", "                INSERT INTO %%SCHEMA_NAME%%.sys_notification", "                    (tenant_id, origin_id, notification_id, reply_id, reply_topic, user_email, login, locale,", "                    topic, payload, status, _source_id, _update_tick, _create_stamp, _update_stamp)", "                VALUES (tenant_id, origin_id, notification_id, '', '', user_email, login_email, locale,", "                    topic, payload, 'pending', '', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);", "", "                RAISE NOTICE 'Notifying %:%', TG_OP, event;", "                PERFORM pg_notify('notification_queued', '{\"data\":\"{\\\"topic\\\":\\\"' || event || '\\\"}\"}');", "            END IF;", "        END IF;", "", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", ""]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Is a demo tenant", "released", false, "@sage/xtrem-system", false, "isDemoTenant"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["enable dev-only features", "experimental", false, "@sage/xtrem-system", false, "devTools"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["allow to display changelog in the app", "experimental", false, "@sage/xtrem-system", false, "changelog"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Notification center", "experimental", false, "@sage/xtrem-communication", false, "notificationCenter"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Authorization access control", "released", false, "@sage/xtrem-authorization", false, "authorizationServiceOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Auditing option", "workInProgress", false, "@sage/xtrem-auditing", false, "auditing"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Auditing option", "workInProgress", true, "@sage/xtrem-auditing", false, "auditingOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Workflow option", "workInProgress", false, "@sage/xtrem-workflow", false, "workflow"]}, {"isSysPool": false, "sql": ["INSERT INTO %%SCHEMA_NAME%%.sys_service_option", "(_update_tick,_source_id,package,option_name,description,status,is_hidden,is_active_by_default)", "VALUES ($1,$2,$3,$4,$5,$6,$7,$8)", "RETURNING _create_stamp,_update_stamp,_id"], "args": [1, "", "@sage/xtrem-workflow", "workflowAdvanced", "Workflow advanced features (not yet released)", "workInProgress", false, false]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Workflow option (obsolete)", "workInProgress", true, "@sage/xtrem-workflow", false, "workflowOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Synchronization", "released", false, "@sage/xtrem-interop", false, "synchronizationServiceOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["showCase discount option", "experimental", false, "@sage/xtrem-show-case", false, "showCaseDiscountOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["experimental option", "experimental", false, "@sage/xtrem-show-case", false, "showCaseExperimentalOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["showCaseOption's hight level description", "released", false, "@sage/xtrem-show-case", false, "showCaseOptionHighLevel"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["showCaseOption1's description", "released", true, "@sage/xtrem-show-case", false, "showCaseOption1"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["showCaseOption2's description", "released", true, "@sage/xtrem-show-case", false, "showCaseOption2"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["A workInProgress service option that can be loaded on a development environment", "workInProgress", false, "@sage/xtrem-show-case", true, "showCaseOption3"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Work in progress options cannot be activated", "workInProgress", false, "@sage/xtrem-show-case", false, "showCaseWorkInProgressOption"]}, {"isSysPool": false, "sql": "NOTIFY invalidate_category_cache, '{\"data\":\"{\\\"tenantId\\\":null,\\\"category\\\":\\\"$SHARED_NODE.SysServiceOption\\\"}\",\"containerId\":\"x3-devops00GO5M-64909\",\"excludeSelf\":true}';", "args": []}, {"action": "reload_setup_data", "args": {"factory": "Role"}}, {"action": "reload_setup_data", "args": {"factory": "RoleActivity"}}, {"action": "reload_setup_data", "args": {"factory": "ImportExportTemplate"}}, {"action": "reload_setup_data", "args": {"factory": "SysServiceOptionState"}}], "data": {"Role": {"metadata": {"rootFactoryName": "Role", "name": "Role", "naturalKeyColumns": ["_tenant_id", "id"], "columns": [{"name": "_vendor", "type": "reference", "isNullable": true, "targetFactoryName": "SysVendor"}, {"name": "is_active", "type": "boolean"}, {"name": "name", "type": "string", "isLocalized": true}, {"name": "description", "type": "string"}, {"name": "id", "type": "string"}, {"name": "is_billing_role", "type": "boolean"}]}, "rows": [["sage", "Y", "{\"en\":\"IT manager\"}", null, "100", "N"], ["sage", "Y", "{\"en\":\"Sales manager\"}", null, "200", "N"], ["sage", "Y", "{\"en\":\"Sales administrator\"}", null, "300", "N"], ["sage", "Y", "{\"en\":\"Purchasing manager\"}", null, "400", "N"], ["sage", "Y", "{\"en\":\"Buyer\"}", null, "500", "N"], ["sage", "Y", "{\"en\":\"Inventory manager\"}", null, "600", "N"], ["sage", "Y", "{\"en\":\"Inventory operator\"}", null, "700", "N"], ["sage", "Y", "{\"en\":\"Design/Production engineer\"}", null, "800", "N"], ["sage", "Y", "{\"en\":\"Production manager\"}", null, "900", "N"], ["sage", "Y", "{\"en\":\"Production operator\"}", null, "1000", "N"], ["sage", "Y", "{\"en\":\"Administrator - Technical\"}", null, "Admin - Technical", "N"], ["sage", "Y", "{\"en\":\"Administrator\"}", null, "Admin", "N"], ["sage", "Y", "{\"en\":\"Support Access Read-only\"}", null, "Support User Read-only", "N"], ["sage", "Y", "{\"en\":\"Support Access\"}", null, "Support User", "N"], ["sage", "Y", "{\"en\":\"Operational User\"}", null, "Operational User", "Y"], ["sage", "Y", "{\"en\":\"Business User\"}", null, "Business User", "Y"], ["sage", "Y", "{\"en\":\"Controller/Finance manager\",\"base\":\"Controller/Finance manager\",\"en-US\":\"Controller/Finance manager\"}", null, "1100", "N"]]}, "RoleActivity": {"metadata": {"isVitalChild": true, "isVitalCollectionChild": true, "rootFactoryName": "RoleActivity", "name": "RoleActivity", "naturalKeyColumns": ["_tenant_id", "role", "activity"], "columns": [{"name": "_vendor", "type": "reference", "isNullable": true, "targetFactoryName": "SysVendor"}, {"name": "_sort_value", "type": "integer"}, {"name": "role", "type": "reference", "targetFactoryName": "Role"}, {"name": "activity", "type": "reference", "targetFactoryName": "Activity"}, {"name": "has_all_permissions", "type": "boolean"}, {"name": "permissions", "type": "stringArray"}, {"name": "is_active", "type": "boolean"}], "vitalParentColumn": {"name": "role", "type": "reference", "targetFactoryName": "Role"}}, "rows": [["sage", "15600", "100", "groupRoleSite", "Y", "[]", "Y"], ["sage", "200", "100", "user", "Y", "[]", "Y"], ["sage", "15600", "100", "siteGroup", "Y", "[]", "Y"], ["sage", "15700", "100", "site", "Y", "[]", "Y"], ["sage", "3700", "200", "siteGroup", "N", "[\"read\"]", "Y"], ["sage", "3800", "200", "user", "Y", "[]", "Y"], ["sage", "3900", "200", "site", "N", "[\"read\"]", "Y"], ["sage", "3900", "400", "siteGroup", "N", "[\"read\"]", "Y"], ["sage", "4000", "400", "site", "N", "[\"read\"]", "Y"], ["sage", "6400", "600", "siteGroup", "N", "[\"read\"]", "Y"], ["sage", "6500", "600", "user", "Y", "[]", "Y"], ["sage", "6600", "600", "site", "N", "[\"read\"]", "Y"], ["sage", "6500", "800", "siteGroup", "N", "[\"read\"]", "Y"], ["sage", "6600", "800", "user", "Y", "[]", "Y"], ["sage", "6700", "800", "site", "N", "[\"read\"]", "Y"], ["sage", "3500", "900", "siteGroup", "N", "[\"read\"]", "Y"], ["sage", "6700", "900", "user", "Y", "[]", "Y"], ["sage", "6800", "900", "site", "N", "[\"read\"]", "Y"], ["sage", "100", "Support User", "role", "Y", "[]", "Y"], ["sage", "200", "Support User", "user", "Y", "[]", "Y"], ["sage", "300", "Support User", "siteGroup", "Y", "[]", "Y"], ["sage", "400", "Support User", "groupRoleSite", "Y", "[]", "Y"], ["sage", "500", "Support User", "supportAccessHistory", "Y", "[]", "Y"], ["sage", "600", "Support User", "site", "Y", "[]", "Y"], ["sage", "100", "Support User Read-only", "role", "N", "[\"read\"]", "Y"], ["sage", "200", "Support User Read-only", "user", "N", "[\"read\"]", "Y"], ["sage", "300", "Support User Read-only", "siteGroup", "N", "[\"read\"]", "Y"], ["sage", "400", "Support User Read-only", "groupRoleSite", "N", "[\"read\"]", "Y"], ["sage", "500", "Support User Read-only", "supportAccessHistory", "N", "[\"read\"]", "Y"], ["sage", "600", "Support User Read-only", "site", "N", "[\"read\"]", "Y"], ["sage", "100", "Admin", "role", "Y", "[]", "Y"], ["sage", "200", "Admin", "user", "Y", "[]", "Y"], ["sage", "300", "Admin", "siteGroup", "Y", "[]", "Y"], ["sage", "400", "Admin", "groupRoleSite", "Y", "[]", "Y"], ["sage", "500", "Admin", "supportAccessHistory", "Y", "[]", "Y"], ["sage", "600", "Admin", "site", "Y", "[]", "Y"], ["sage", "15600", "100", "company", "Y", "[]", "Y"], ["sage", "3500", "200", "company", "N", "[\"read\"]", "Y"], ["sage", "3400", "300", "company", "N", "[\"read\"]", "Y"], ["sage", "3700", "400", "company", "N", "[\"read\"]", "Y"], ["sage", "2500", "500", "company", "N", "[\"read\"]", "Y"], ["sage", "6200", "600", "company", "N", "[\"read\"]", "Y"], ["sage", "6300", "700", "company", "N", "[\"read\"]", "Y"], ["sage", "21400", "Support User", "company", "Y", "[]", "Y"], ["sage", "21500", "Support User", "serviceOptionState", "Y", "[]", "Y"], ["sage", "21400", "Support User Read-only", "company", "N", "[\"read\"]", "Y"], ["sage", "21500", "Support User Read-only", "serviceOptionState", "N", "[\"read\"]", "Y"], ["sage", "12300", "Admin", "serviceOptionState", "Y", "[]", "Y"], ["sage", "12200", "Admin", "company", "Y", "[]", "Y"], ["sage", "3600", "200", "sysNotificationHistory", "N", "[\"read\"]", "Y"], ["sage", "3800", "400", "sysNotificationHistory", "N", "[\"read\"]", "Y"], ["sage", "6300", "600", "sysNotificationHistory", "N", "[\"read\"]", "Y"], ["sage", "6400", "800", "sysNotificationHistory", "N", "[\"read\"]", "Y"], ["sage", "3400", "900", "sysNotificationHistory", "N", "[\"read\"]", "Y"], ["sage", "12400", "Admin", "sysNotificationHistory", "Y", "[]", "Y"], ["sage", "21400", "Support User", "sysNotificationHistory", "Y", "[]", "Y"], ["sage", "21400", "Support User Read-only", "sysNotificationHistory", "N", "[\"read\"]", "Y"], ["sage", "12500", "Admin", "sysNotificationState", "Y", "[]", "Y"], ["sage", "21500", "Support User", "sysNotificationState", "Y", "[]", "Y"], ["sage", "21500", "Support User Read-only", "sysNotificationState", "N", "[\"read\"]", "Y"], ["sage", "13500", "Business User", "role", "Y", "[]", "Y"], ["sage", "13600", "Business User", "user", "Y", "[]", "Y"], ["sage", "13700", "Business User", "siteGroup", "Y", "[]", "Y"], ["sage", "13800", "Business User", "groupRoleSite", "Y", "[]", "Y"], ["sage", "13900", "Business User", "supportAccessHistory", "Y", "[]", "Y"], ["sage", "14200", "Business User", "serviceOptionState", "Y", "[]", "Y"], ["sage", "14100", "Business User", "company", "Y", "[]", "Y"], ["sage", "14150", "Business User", "sysNotificationHistory", "Y", "[]", "Y"], ["sage", "14170", "Business User", "sysNotificationState", "Y", "[]", "Y"], ["sage", "14180", "Business User", "site", "Y", "[]", "Y"], ["sage", "530", "1100", "company", "N", "[\"read\",\"create\",\"update\",\"delete\"]", "Y"], ["sage", "15700", "100", "customField", "Y", "[]", "Y"], ["sage", "4300", "200", "customField", "Y", "[]", "Y"], ["sage", "4400", "400", "customField", "Y", "[]", "Y"], ["sage", "7000", "600", "customField", "Y", "[]", "Y"], ["sage", "7100", "800", "customField", "Y", "[]", "Y"], ["sage", "7200", "900", "customField", "Y", "[]", "Y"], ["sage", "21600", "Support User", "customField", "Y", "[]", "Y"], ["sage", "21600", "Support User Read-only", "customField", "N", "[\"read\"]", "Y"], ["sage", "13000", "Admin", "customField", "Y", "[]", "Y"], ["sage", "900", "Business User", "customField", "Y", "[]", "Y"], ["sage", "15500", "100", "dashboardActivity", "N", "[\"read\"]", "Y"], ["sage", "3100", "200", "dashboardActivity", "Y", "[]", "Y"], ["sage", "2100", "300", "dashboardActivity", "Y", "[]", "Y"], ["sage", "3300", "400", "dashboardActivity", "Y", "[]", "Y"], ["sage", "2200", "500", "dashboardActivity", "Y", "[]", "Y"], ["sage", "5900", "600", "dashboardActivity", "Y", "[]", "Y"], ["sage", "6000", "700", "dashboardActivity", "Y", "[]", "Y"], ["sage", "3500", "800", "dashboardActivity", "Y", "[]", "Y"], ["sage", "3000", "900", "dashboardActivity", "N", "[]", "Y"], ["sage", "1900", "1000", "dashboardActivity", "N", "[]", "Y"], ["sage", "80", "Operational User", "dashboardActivity", "Y", "[\"read\"]", "Y"], ["sage", "700", "Admin", "dashboardActivity", "Y", "[]", "Y"], ["sage", "21100", "Support User", "dashboardActivity", "Y", "[]", "Y"], ["sage", "20900", "Support User Read-only", "dashboardActivity", "N", "[\"read\"]", "Y"], ["sage", "1000", "Business User", "dashboardActivity", "Y", "[]", "Y"], ["sage", "10", "1100", "dashboardActivity", "N", "[\"read\",\"manage\"]", "Y"], ["sage", "3000", "200", "sysJobSchedule", "N", "[\"read\"]", "Y"], ["sage", "3200", "400", "sysJobSchedule", "N", "[\"read\"]", "Y"], ["sage", "5400", "600", "sysJobSchedule", "N", "[\"read\"]", "Y"], ["sage", "3400", "800", "sysJobSchedule", "N", "[\"read\"]", "Y"], ["sage", "2900", "900", "sysJobSchedule", "N", "[\"read\"]", "Y"], ["sage", "700", "Admin", "sysJobSchedule", "Y", "[]", "Y"], ["sage", "1500", "Admin - Technical", "sysJobSchedule", "Y", "[]", "Y"], ["sage", "21100", "Support User", "sysJobSchedule", "Y", "[]", "Y"], ["sage", "20900", "Support User Read-only", "sysJobSchedule", "N", "[\"read\"]", "Y"], ["sage", "1700", "Business User", "sysJobSchedule", "Y", "[]", "Y"], ["sage", "3200", "200", "importData", "Y", "[]", "Y"], ["sage", "2200", "300", "importData", "Y", "[]", "Y"], ["sage", "3400", "400", "importData", "Y", "[]", "Y"], ["sage", "2300", "500", "importData", "Y", "[]", "Y"], ["sage", "6000", "600", "importData", "Y", "[]", "Y"], ["sage", "6100", "700", "importData", "Y", "[]", "Y"], ["sage", "3600", "800", "importData", "Y", "[]", "Y"], ["sage", "3100", "900", "importData", "N", "[]", "Y"], ["sage", "2000", "1000", "importData", "N", "[]", "Y"], ["sage", "15500", "100", "importExportTemplate", "Y", "[]", "Y"], ["sage", "3300", "200", "importExportTemplate", "Y", "[]", "Y"], ["sage", "2300", "300", "importExportTemplate", "Y", "[]", "Y"], ["sage", "3500", "400", "importExportTemplate", "Y", "[]", "Y"], ["sage", "2400", "500", "importExportTemplate", "Y", "[]", "Y"], ["sage", "6100", "600", "importExportTemplate", "Y", "[]", "Y"], ["sage", "6200", "700", "importExportTemplate", "Y", "[]", "Y"], ["sage", "6300", "800", "importExportTemplate", "Y", "[]", "Y"], ["sage", "3200", "900", "importExportTemplate", "N", "[]", "Y"], ["sage", "2100", "1000", "importExportTemplate", "N", "[]", "Y"], ["sage", "11800", "Support User", "importData", "Y", "[]", "Y"], ["sage", "12900", "Support User", "importExportTemplate", "Y", "[]", "Y"], ["sage", "11800", "Support User Read-only", "importData", "N", "[\"read\"]", "Y"], ["sage", "12900", "Support User Read-only", "importExportTemplate", "N", "[\"read\"]", "Y"], ["sage", "11900", "Admin", "importData", "Y", "[]", "Y"], ["sage", "12000", "Admin", "importExportTemplate", "Y", "[]", "Y"], ["sage", "1100", "Business User", "importData", "Y", "[]", "Y"], ["sage", "1200", "Business User", "importExportTemplate", "Y", "[]", "Y"], ["sage", "3900", "200", "reportResource", "Y", "[]", "Y"], ["sage", "4000", "200", "reportStyleVariable", "Y", "[]", "Y"], ["sage", "4100", "200", "reportTemplate", "Y", "[]", "Y"], ["sage", "4200", "200", "report", "Y", "[]", "Y"], ["sage", "3500", "300", "reportResource", "Y", "[]", "Y"], ["sage", "3600", "300", "reportStyleVariable", "Y", "[]", "Y"], ["sage", "3700", "300", "reportTemplate", "Y", "[]", "Y"], ["sage", "3800", "300", "report", "Y", "[]", "Y"], ["sage", "4000", "400", "reportResource", "Y", "[]", "Y"], ["sage", "4100", "400", "reportStyleVariable", "Y", "[]", "Y"], ["sage", "4200", "400", "reportTemplate", "Y", "[]", "Y"], ["sage", "4300", "400", "report", "Y", "[]", "Y"], ["sage", "2600", "500", "reportResource", "Y", "[]", "Y"], ["sage", "2700", "500", "reportStyleVariable", "Y", "[]", "Y"], ["sage", "2800", "500", "reportTemplate", "Y", "[]", "Y"], ["sage", "2900", "500", "report", "Y", "[]", "Y"], ["sage", "6600", "600", "reportResource", "Y", "[]", "Y"], ["sage", "6700", "600", "reportStyleVariable", "Y", "[]", "Y"], ["sage", "6800", "600", "reportTemplate", "Y", "[]", "Y"], ["sage", "6900", "600", "report", "Y", "[]", "Y"], ["sage", "6400", "700", "reportResource", "Y", "[]", "Y"], ["sage", "6500", "700", "reportStyleVariable", "Y", "[]", "Y"], ["sage", "6600", "700", "reportTemplate", "Y", "[]", "Y"], ["sage", "6700", "700", "report", "Y", "[]", "Y"], ["sage", "6700", "800", "reportResource", "Y", "[]", "Y"], ["sage", "6800", "800", "reportStyleVariable", "Y", "[]", "Y"], ["sage", "6900", "800", "reportTemplate", "Y", "[]", "Y"], ["sage", "7000", "800", "report", "Y", "[]", "Y"], ["sage", "6800", "900", "reportResource", "Y", "[]", "Y"], ["sage", "6900", "900", "reportStyleVariable", "Y", "[]", "Y"], ["sage", "7000", "900", "reportTemplate", "Y", "[]", "Y"], ["sage", "7100", "900", "report", "Y", "[]", "Y"], ["sage", "2200", "1000", "reportResource", "Y", "[]", "Y"], ["sage", "2300", "1000", "reportStyleVariable", "Y", "[]", "Y"], ["sage", "2400", "1000", "reportTemplate", "Y", "[]", "Y"], ["sage", "2500", "1000", "report", "Y", "[]", "Y"], ["sage", "20000", "Support User", "reportStyleVariable", "Y", "[]", "Y"], ["sage", "20100", "Support User", "reportTemplate", "Y", "[]", "Y"], ["sage", "20200", "Support User", "report", "Y", "[]", "Y"], ["sage", "21300", "Support User", "reportResource", "Y", "[]", "Y"], ["sage", "20000", "Support User Read-only", "reportStyleVariable", "N", "[\"read\"]", "Y"], ["sage", "20100", "Support User Read-only", "reportTemplate", "N", "[\"read\"]", "Y"], ["sage", "20200", "Support User Read-only", "report", "N", "[\"read\"]", "Y"], ["sage", "21100", "Support User Read-only", "reportResource", "N", "[\"read\"]", "Y"], ["sage", "12600", "Admin", "reportResource", "Y", "[]", "Y"], ["sage", "12700", "Admin", "reportStyleVariable", "Y", "[]", "Y"], ["sage", "12800", "Admin", "reportTemplate", "Y", "[]", "Y"], ["sage", "12900", "Admin", "report", "Y", "[]", "Y"], ["sage", "5200", "Admin", "sysEmailConfig", "Y", null, "Y"]]}, "ImportExportTemplate": {"metadata": {"rootFactoryName": "ImportExportTemplate", "name": "ImportExportTemplate", "naturalKeyColumns": ["_tenant_id", "id"], "columns": [{"name": "_vendor", "type": "reference", "isNullable": true, "targetFactoryName": "SysVendor"}, {"name": "id", "type": "string"}, {"name": "name", "type": "string", "isLocalized": true}, {"name": "description", "type": "string", "isLocalized": true}, {"name": "node_name", "type": "string"}, {"name": "is_active", "type": "boolean", "isOwnedByCustomer": true}, {"name": "is_default", "type": "boolean", "isOwnedByCustomer": true}, {"name": "template_use", "type": "enum", "enumMembers": ["importOnly", "exportOnly", "importAndExport"]}, {"name": "default_parameters", "type": "json"}, {"name": "csv_template", "type": "json"}]}, "rows": [["sage", "Report", "{\"en\":\"Report\",\"base\":\"Report\",\"en-US\":\"Report\"}", "{\"en\":\"\",\"base\":\"\",\"en-US\":\"\"}", "Report", "Y", "Y", "importAndExport", "{\n    \"delimiter\": \";\",\n    \"dateFormat\": \"YYYY-MM-DD\",\n    \"decimalPoint\": \".\",\n    \"quoteCharacter\": \"'\",\n    \"escapeCharacter\": \"\\\\\"\n}\n", "{\n    \"data\": [\n        {\n            \"_id\": \"0\",\n            \"path\": \"!name\",\n            \"locale\": \"\",\n            \"dataType\": \"string\",\n            \"isCustom\": false,\n            \"description\": \"name\"\n        },\n        {\n            \"_id\": \"10\",\n            \"path\": \"*description\",\n            \"locale\": \"\",\n            \"dataType\": \"string\",\n            \"isCustom\": false,\n            \"description\": \"description\"\n        },\n        {\n            \"_id\": \"20\",\n            \"path\": \"*parentPackage\",\n            \"locale\": \"\",\n            \"dataType\": \"string\",\n            \"isCustom\": false,\n            \"description\": \"parent package\"\n        },\n        {\n            \"_id\": \"30\",\n            \"path\": \"activeTemplate\",\n            \"locale\": \"\",\n            \"dataType\": \"reference\",\n            \"isCustom\": false,\n            \"description\": \"active template (#name)\"\n        },\n        {\n            \"_id\": \"40\",\n            \"path\": \"reportType\",\n            \"locale\": \"\",\n            \"dataType\": \"enum(printedDocument,email)\",\n            \"isCustom\": false,\n            \"description\": \"report type\"\n        },\n        {\n            \"_id\": \"50\",\n            \"path\": \"isFactory\",\n            \"locale\": \"\",\n            \"dataType\": \"boolean\",\n            \"isCustom\": false,\n            \"description\": \"is factory (false/true)\"\n        },\n        {\n            \"_id\": \"60\",\n            \"path\": \"#variables\",\n            \"locale\": \"\",\n            \"dataType\": \"collection\",\n            \"isCustom\": false,\n            \"description\": \"variables\"\n        },\n        {\n            \"_id\": \"70\",\n            \"path\": \"!_sortValue\",\n            \"locale\": \"\",\n            \"dataType\": \"integer\",\n            \"isCustom\": false,\n            \"description\": \"sort value\"\n        },\n        {\n            \"_id\": \"80\",\n            \"path\": \"*name\",\n            \"locale\": \"\",\n            \"dataType\": \"string\",\n            \"isCustom\": false,\n            \"description\": \"name\"\n        },\n        {\n            \"_id\": \"90\",\n            \"path\": \"title\",\n            \"locale\": \"base\",\n            \"dataType\": \"localized text\",\n            \"isCustom\": false,\n            \"description\": \"\\\"default locale: base, other locales can be specified with (locale-name), for example \\\"\\\"title (de-DE)\\\"\\\". Available locales are (en-GB, fr-FR, ...). You can also duplicate the columns to import several translations\\\"\"\n        },\n        {\n            \"_id\": \"100\",\n            \"path\": \"isMandatory\",\n            \"locale\": \"\",\n            \"dataType\": \"boolean\",\n            \"isCustom\": false,\n            \"description\": \"is mandatory (false/true)\"\n        },\n        {\n            \"_id\": \"110\",\n            \"path\": \"type\",\n            \"locale\": \"\",\n            \"dataType\": \"enum(boolean,string,byte,short,integer,decimal,float,double,enum,date,time,datetime,uuid,binaryStream,textStream,json,reference,collection,jsonReference,integerArray,enumArray,referenceArray,stringArray,integerRange,decimalRange,dateRange,datetimeRange)\",\n            \"isCustom\": false,\n            \"description\": \"type\"\n        },\n        {\n            \"_id\": \"120\",\n            \"path\": \"dataType\",\n            \"locale\": \"\",\n            \"dataType\": \"reference\",\n            \"isCustom\": false,\n            \"description\": \"data type (#name)\"\n        }\n    ]\n}\n"], ["sage", "ReportTemplate", "{\"en\":\"ReportTemplate\",\"base\":\"ReportTemplate\",\"en-US\":\"ReportTemplate\"}", "{\"en\":\"\",\"base\":\"\",\"en-US\":\"\"}", "ReportTemplate", "Y", "Y", "importAndExport", "{\n    \"delimiter\": \";\",\n    \"dateFormat\": \"YYYY-MM-DD\",\n    \"decimalPoint\": \".\",\n    \"quoteCharacter\": \"'\",\n    \"escapeCharacter\": \"\\\\\"\n}\n", "{\n    \"data\": [\n        {\n            \"_id\": \"0\",\n            \"path\": \"!name\",\n            \"locale\": \"\",\n            \"dataType\": \"string\",\n            \"isCustom\": false,\n            \"description\": \"name\"\n        },\n        {\n            \"_id\": \"10\",\n            \"path\": \"htmlTemplate\",\n            \"locale\": \"\",\n            \"dataType\": \"textStream\",\n            \"isCustom\": false,\n            \"description\": \"html template\"\n        },\n        {\n            \"_id\": \"20\",\n            \"path\": \"attachmentTemplate\",\n            \"locale\": \"\",\n            \"dataType\": \"textStream\",\n            \"isCustom\": false,\n            \"description\": \"attachment template\"\n        },\n        {\n            \"_id\": \"30\",\n            \"path\": \"attachmentName\",\n            \"locale\": \"\",\n            \"dataType\": \"string\",\n            \"isCustom\": false,\n            \"description\": \"attachment name\"\n        },\n        {\n            \"_id\": \"40\",\n            \"path\": \"attachmentMimeType\",\n            \"locale\": \"\",\n            \"dataType\": \"string\",\n            \"isCustom\": false,\n            \"description\": \"attachment mime type\"\n        },\n        {\n            \"_id\": \"50\",\n            \"path\": \"headerHtmlTemplate\",\n            \"locale\": \"\",\n            \"dataType\": \"textStream\",\n            \"isCustom\": false,\n            \"description\": \"header html template\"\n        },\n        {\n            \"_id\": \"60\",\n            \"path\": \"footerHtmlTemplate\",\n            \"locale\": \"\",\n            \"dataType\": \"textStream\",\n            \"isCustom\": false,\n            \"description\": \"footer html template\"\n        },\n        {\n            \"_id\": \"70\",\n            \"path\": \"styleSheet\",\n            \"locale\": \"\",\n            \"dataType\": \"textStream\",\n            \"isCustom\": false,\n            \"description\": \"style sheet\"\n        },\n        {\n            \"_id\": \"80\",\n            \"path\": \"query\",\n            \"locale\": \"\",\n            \"dataType\": \"textStream\",\n            \"isCustom\": false,\n            \"description\": \"query\"\n        },\n        {\n            \"_id\": \"90\",\n            \"path\": \"code\",\n            \"locale\": \"\",\n            \"dataType\": \"textStream\",\n            \"isCustom\": false,\n            \"description\": \"code\"\n        },\n        {\n            \"_id\": \"100\",\n            \"path\": \"report\",\n            \"locale\": \"\",\n            \"dataType\": \"reference\",\n            \"isCustom\": false,\n            \"description\": \"report (#name)\"\n        },\n        {\n            \"_id\": \"110\",\n            \"path\": \"isFactory\",\n            \"locale\": \"\",\n            \"dataType\": \"boolean\",\n            \"isCustom\": false,\n            \"description\": \"is factory (false/true)\"\n        },\n        {\n            \"_id\": \"120\",\n            \"path\": \"isExpertDocument\",\n            \"locale\": \"\",\n            \"dataType\": \"boolean\",\n            \"isCustom\": false,\n            \"description\": \"is expert document (false/true)\"\n        },\n        {\n            \"_id\": \"130\",\n            \"path\": \"baseLocale\",\n            \"locale\": \"\",\n            \"dataType\": \"enum(ar_SA,de_DE,en_GB,en_US,es_ES,fr_FR,it_IT,pl_PL,pt_BR,pt_PT,zh_CN)\",\n            \"isCustom\": false,\n            \"description\": \"base locale\"\n        },\n        {\n            \"_id\": \"140\",\n            \"path\": \"defaultPaperFormat\",\n            \"locale\": \"\",\n            \"dataType\": \"enum(letter,legal,tabloid,ledger,a0,a1,a2,a3,a4,a5,a6)\",\n            \"isCustom\": false,\n            \"description\": \"default paper format\"\n        },\n        {\n            \"_id\": \"150\",\n            \"path\": \"defaultPageOrientation\",\n            \"locale\": \"\",\n            \"dataType\": \"enum(portrait,landscape)\",\n            \"isCustom\": false,\n            \"description\": \"default page orientation\"\n        },\n        {\n            \"_id\": \"160\",\n            \"path\": \"defaultLeftMargin\",\n            \"locale\": \"\",\n            \"dataType\": \"decimal\",\n            \"isCustom\": false,\n            \"description\": \"default left margin\"\n        },\n        {\n            \"_id\": \"170\",\n            \"path\": \"defaultRightMargin\",\n            \"locale\": \"\",\n            \"dataType\": \"decimal\",\n            \"isCustom\": false,\n            \"description\": \"default right margin\"\n        },\n        {\n            \"_id\": \"180\",\n            \"path\": \"defaultTopMargin\",\n            \"locale\": \"\",\n            \"dataType\": \"decimal\",\n            \"isCustom\": false,\n            \"description\": \"default top margin\"\n        },\n        {\n            \"_id\": \"190\",\n            \"path\": \"defaultBottomMargin\",\n            \"locale\": \"\",\n            \"dataType\": \"decimal\",\n            \"isCustom\": false,\n            \"description\": \"default bottom margin\"\n        },\n        {\n            \"_id\": \"200\",\n            \"path\": \"isDefaultHeaderFooter\",\n            \"locale\": \"\",\n            \"dataType\": \"boolean\",\n            \"isCustom\": false,\n            \"description\": \"is default header footer (false/true)\"\n        },\n        {\n            \"_id\": \"210\",\n            \"path\": \"/reportWizard\",\n            \"locale\": \"\",\n            \"dataType\": \"reference\",\n            \"isCustom\": false,\n            \"description\": \"report wizard (#reportTemplate)\"\n        },\n        {\n            \"_id\": \"220\",\n            \"path\": \"*id\",\n            \"locale\": \"\",\n            \"dataType\": \"string\",\n            \"isCustom\": false,\n            \"description\": \"id\"\n        },\n        {\n            \"_id\": \"230\",\n            \"path\": \"*name\",\n            \"locale\": \"\",\n            \"dataType\": \"string\",\n            \"isCustom\": false,\n            \"description\": \"name\"\n        },\n        {\n            \"_id\": \"240\",\n            \"path\": \"*description\",\n            \"locale\": \"\",\n            \"dataType\": \"string\",\n            \"isCustom\": false,\n            \"description\": \"description\"\n        },\n        {\n            \"_id\": \"250\",\n            \"path\": \"*dataSource\",\n            \"locale\": \"\",\n            \"dataType\": \"reference\",\n            \"isCustom\": false,\n            \"description\": \"data source (#name)\"\n        },\n        {\n            \"_id\": \"260\",\n            \"path\": \"selectedProperties\",\n            \"locale\": \"\",\n            \"dataType\": \"json\",\n            \"isCustom\": false,\n            \"description\": \"selected properties\"\n        },\n        {\n            \"_id\": \"270\",\n            \"path\": \"filters\",\n            \"locale\": \"\",\n            \"dataType\": \"json\",\n            \"isCustom\": false,\n            \"description\": \"filters\"\n        },\n        {\n            \"_id\": \"280\",\n            \"path\": \"parameters\",\n            \"locale\": \"\",\n            \"dataType\": \"json\",\n            \"isCustom\": false,\n            \"description\": \"parameters\"\n        },\n        {\n            \"_id\": \"290\",\n            \"path\": \"content\",\n            \"locale\": \"\",\n            \"dataType\": \"json\",\n            \"isCustom\": false,\n            \"description\": \"content\"\n        },\n        {\n            \"_id\": \"300\",\n            \"path\": \"templateType\",\n            \"locale\": \"\",\n            \"dataType\": \"enum(list,form,advanced)\",\n            \"isCustom\": false,\n            \"description\": \"template type\"\n        },\n        {\n            \"_id\": \"310\",\n            \"path\": \"#translatableTexts\",\n            \"locale\": \"\",\n            \"dataType\": \"collection\",\n            \"isCustom\": false,\n            \"description\": \"translatable texts\"\n        },\n        {\n            \"_id\": \"320\",\n            \"path\": \"!_sortValue\",\n            \"locale\": \"\",\n            \"dataType\": \"integer\",\n            \"isCustom\": false,\n            \"description\": \"sort value\"\n        },\n        {\n            \"_id\": \"330\",\n            \"path\": \"originalText\",\n            \"locale\": \"\",\n            \"dataType\": \"reference\",\n            \"isCustom\": false,\n            \"description\": \"original text (#reportTemplate|_sortValue)\"\n        },\n        {\n            \"_id\": \"340\",\n            \"path\": \"hash\",\n            \"locale\": \"\",\n            \"dataType\": \"string\",\n            \"isCustom\": false,\n            \"description\": \"hash\"\n        },\n        {\n            \"_id\": \"350\",\n            \"path\": \"locale\",\n            \"locale\": \"\",\n            \"dataType\": \"enum(ar_SA,de_DE,en_GB,en_US,es_ES,fr_FR,it_IT,pl_PL,pt_BR,pt_PT,zh_CN)\",\n            \"isCustom\": false,\n            \"description\": \"locale\"\n        },\n        {\n            \"_id\": \"360\",\n            \"path\": \"isBaseLocale\",\n            \"locale\": \"\",\n            \"dataType\": \"boolean\",\n            \"isCustom\": false,\n            \"description\": \"is base locale (false/true)\"\n        },\n        {\n            \"_id\": \"370\",\n            \"path\": \"text\",\n            \"locale\": \"\",\n            \"dataType\": \"string\",\n            \"isCustom\": false,\n            \"description\": \"text\"\n        }\n    ]\n}\n"], ["sage", "Company", "{\"en\":\"Company\",\"base\":\"Company\",\"en-US\":\"Company\"}", "{\"en\":\"\",\"base\":\"\",\"en-US\":\"\"}", "Company", "Y", "Y", "importAndExport", "{\n    \"delimiter\": \";\",\n    \"dateFormat\": \"YYYY-MM-DD\",\n    \"decimalPoint\": \".\",\n    \"quoteCharacter\": \"'\",\n    \"escapeCharacter\": \"\\\\\"\n}\n", "{\n    \"data\": [\n        {\n            \"_id\": \"0\",\n            \"path\": \"!id\",\n            \"locale\": \"\",\n            \"dataType\": \"string\",\n            \"isCustom\": false,\n            \"description\": \"id\"\n        },\n        {\n            \"_id\": \"10\",\n            \"path\": \"isActive\",\n            \"locale\": \"\",\n            \"dataType\": \"boolean\",\n            \"isCustom\": false,\n            \"description\": \"is active (true/false)\"\n        },\n        {\n            \"_id\": \"20\",\n            \"path\": \"*name\",\n            \"locale\": \"\",\n            \"dataType\": \"string\",\n            \"isCustom\": false,\n            \"description\": \"name\"\n        },\n        {\n            \"_id\": \"30\",\n            \"path\": \"description\",\n            \"locale\": \"\",\n            \"dataType\": \"string\",\n            \"isCustom\": false,\n            \"description\": \"description\"\n        },\n        {\n            \"_id\": \"40\",\n            \"path\": \"*legislation\",\n            \"locale\": \"\",\n            \"dataType\": \"reference\",\n            \"isCustom\": false,\n            \"description\": \"legislation (#id)\"\n        },\n        {\n            \"_id\": \"50\",\n            \"path\": \"chartOfAccount\",\n            \"locale\": \"\",\n            \"dataType\": \"reference\",\n            \"isCustom\": false,\n            \"description\": \"chart of account (#id)\"\n        },\n        {\n            \"_id\": \"60\",\n            \"path\": \"siren\",\n            \"locale\": \"\",\n            \"dataType\": \"string\",\n            \"isCustom\": false,\n            \"description\": \"siren\"\n        },\n        {\n            \"_id\": \"70\",\n            \"path\": \"naf\",\n            \"locale\": \"\",\n            \"dataType\": \"string\",\n            \"isCustom\": false,\n            \"description\": \"naf\"\n        },\n        {\n            \"_id\": \"80\",\n            \"path\": \"rcs\",\n            \"locale\": \"\",\n            \"dataType\": \"string\",\n            \"isCustom\": false,\n            \"description\": \"rcs\"\n        },\n        {\n            \"_id\": \"90\",\n            \"path\": \"legalForm\",\n            \"locale\": \"\",\n            \"dataType\": \"enum(SARL,EURL,SELARL,SA,SAS,SASU,SNC,SCP)\",\n            \"isCustom\": false,\n            \"description\": \"legal form\"\n        },\n        {\n            \"_id\": \"100\",\n            \"path\": \"*country\",\n            \"locale\": \"\",\n            \"dataType\": \"reference\",\n            \"isCustom\": false,\n            \"description\": \"country (#id)\"\n        },\n        {\n            \"_id\": \"110\",\n            \"path\": \"*currency\",\n            \"locale\": \"\",\n            \"dataType\": \"reference\",\n            \"isCustom\": false,\n            \"description\": \"currency (#id)\"\n        },\n        {\n            \"_id\": \"120\",\n            \"path\": \"sequenceNumberId\",\n            \"locale\": \"\",\n            \"dataType\": \"string\",\n            \"isCustom\": false,\n            \"description\": \"sequence number id\"\n        },\n        {\n            \"_id\": \"130\",\n            \"path\": \"customerOnHoldCheck\",\n            \"locale\": \"\",\n            \"dataType\": \"enum(blocking,warning,none)\",\n            \"isCustom\": false,\n            \"description\": \"customer on hold check\"\n        },\n        {\n            \"_id\": \"140\",\n            \"path\": \"postingClass\",\n            \"locale\": \"\",\n            \"dataType\": \"reference\",\n            \"isCustom\": false,\n            \"description\": \"posting class (#id)\"\n        },\n        {\n            \"_id\": \"150\",\n            \"path\": \"taxEngine\",\n            \"locale\": \"\",\n            \"dataType\": \"enum(avalaraAvaTax,genericTaxCalculation)\",\n            \"isCustom\": false,\n            \"description\": \"tax engine\"\n        },\n        {\n            \"_id\": \"160\",\n            \"path\": \"doStockPosting\",\n            \"locale\": \"\",\n            \"dataType\": \"boolean\",\n            \"isCustom\": false,\n            \"description\": \"do stock posting (false/true)\"\n        },\n        {\n            \"_id\": \"170\",\n            \"path\": \"doWipPosting\",\n            \"locale\": \"\",\n            \"dataType\": \"boolean\",\n            \"isCustom\": false,\n            \"description\": \"do wip posting (false/true)\"\n        },\n        {\n            \"_id\": \"180\",\n            \"path\": \"doArPosting\",\n            \"locale\": \"\",\n            \"dataType\": \"boolean\",\n            \"isCustom\": false,\n            \"description\": \"do ar posting (false/true)\"\n        },\n        {\n            \"_id\": \"190\",\n            \"path\": \"doApPosting\",\n            \"locale\": \"\",\n            \"dataType\": \"boolean\",\n            \"isCustom\": false,\n            \"description\": \"do ap posting (false/true)\"\n        },\n        {\n            \"_id\": \"200\",\n            \"path\": \"doNonAbsorbedPosting\",\n            \"locale\": \"\",\n            \"dataType\": \"boolean\",\n            \"isCustom\": false,\n            \"description\": \"do non absorbed posting (false/true)\"\n        },\n        {\n            \"_id\": \"210\",\n            \"path\": \"doUpdateArAmountPaid\",\n            \"locale\": \"\",\n            \"dataType\": \"boolean\",\n            \"isCustom\": false,\n            \"description\": \"do update ar amount paid (false/true)\"\n        },\n        {\n            \"_id\": \"220\",\n            \"path\": \"serviceFabricExternalId\",\n            \"locale\": \"\",\n            \"dataType\": \"string\",\n            \"isCustom\": false,\n            \"description\": \"service fabric external id\"\n        },\n        {\n            \"_id\": \"230\",\n            \"path\": \"serviceFabricCompanyId\",\n            \"locale\": \"\",\n            \"dataType\": \"string\",\n            \"isCustom\": false,\n            \"description\": \"service fabric company id\"\n        },\n        {\n            \"_id\": \"240\",\n            \"path\": \"#addresses\",\n            \"locale\": \"\",\n            \"dataType\": \"collection\",\n            \"isCustom\": false,\n            \"description\": \"addresses\"\n        },\n        {\n            \"_id\": \"250\",\n            \"path\": \"isActive#1\",\n            \"locale\": \"\",\n            \"dataType\": \"boolean\",\n            \"isCustom\": false,\n            \"description\": \"is active (true/false)\"\n        },\n        {\n            \"_id\": \"260\",\n            \"path\": \"*name#1\",\n            \"locale\": \"\",\n            \"dataType\": \"string\",\n            \"isCustom\": false,\n            \"description\": \"name\"\n        },\n        {\n            \"_id\": \"270\",\n            \"path\": \"addressLine1\",\n            \"locale\": \"\",\n            \"dataType\": \"string\",\n            \"isCustom\": false,\n            \"description\": \"address line 1\"\n        },\n        {\n            \"_id\": \"280\",\n            \"path\": \"addressLine2\",\n            \"locale\": \"\",\n            \"dataType\": \"string\",\n            \"isCustom\": false,\n            \"description\": \"address line 2\"\n        },\n        {\n            \"_id\": \"290\",\n            \"path\": \"city\",\n            \"locale\": \"\",\n            \"dataType\": \"string\",\n            \"isCustom\": false,\n            \"description\": \"city\"\n        },\n        {\n            \"_id\": \"300\",\n            \"path\": \"region\",\n            \"locale\": \"\",\n            \"dataType\": \"string\",\n            \"isCustom\": false,\n            \"description\": \"region\"\n        },\n        {\n            \"_id\": \"310\",\n            \"path\": \"postcode\",\n            \"locale\": \"\",\n            \"dataType\": \"string\",\n            \"isCustom\": false,\n            \"description\": \"postcode\"\n        },\n        {\n            \"_id\": \"320\",\n            \"path\": \"country\",\n            \"locale\": \"\",\n            \"dataType\": \"reference\",\n            \"isCustom\": false,\n            \"description\": \"country (#id)\"\n        },\n        {\n            \"_id\": \"330\",\n            \"path\": \"locationPhoneNumber\",\n            \"locale\": \"\",\n            \"dataType\": \"string\",\n            \"isCustom\": false,\n            \"description\": \"location phone number\"\n        },\n        {\n            \"_id\": \"340\",\n            \"path\": \"isPrimary\",\n            \"locale\": \"\",\n            \"dataType\": \"boolean\",\n            \"isCustom\": false,\n            \"description\": \"is primary (false/true)\"\n        },\n        {\n            \"_id\": \"350\",\n            \"path\": \"##contacts\",\n            \"locale\": \"\",\n            \"dataType\": \"collection\",\n            \"isCustom\": false,\n            \"description\": \"contacts\"\n        },\n        {\n            \"_id\": \"360\",\n            \"path\": \"isActive#2\",\n            \"locale\": \"\",\n            \"dataType\": \"boolean\",\n            \"isCustom\": false,\n            \"description\": \"is active (false/true)\"\n        },\n        {\n            \"_id\": \"370\",\n            \"path\": \"*title\",\n            \"locale\": \"\",\n            \"dataType\": \"enum(ms,mr,dr,mrs)\",\n            \"isCustom\": false,\n            \"description\": \"title\"\n        },\n        {\n            \"_id\": \"380\",\n            \"path\": \"*firstName\",\n            \"locale\": \"\",\n            \"dataType\": \"string\",\n            \"isCustom\": false,\n            \"description\": \"first name\"\n        },\n        {\n            \"_id\": \"390\",\n            \"path\": \"*lastName\",\n            \"locale\": \"\",\n            \"dataType\": \"string\",\n            \"isCustom\": false,\n            \"description\": \"last name\"\n        },\n        {\n            \"_id\": \"400\",\n            \"path\": \"preferredName\",\n            \"locale\": \"\",\n            \"dataType\": \"string\",\n            \"isCustom\": false,\n            \"description\": \"preferred name\"\n        },\n        {\n            \"_id\": \"410\",\n            \"path\": \"role\",\n            \"locale\": \"\",\n            \"dataType\": \"enum(mainContact,commercialContact,financialContact)\",\n            \"isCustom\": false,\n            \"description\": \"role\"\n        },\n        {\n            \"_id\": \"420\",\n            \"path\": \"position\",\n            \"locale\": \"\",\n            \"dataType\": \"string\",\n            \"isCustom\": false,\n            \"description\": \"position\"\n        },\n        {\n            \"_id\": \"430\",\n            \"path\": \"locationPhoneNumber#1\",\n            \"locale\": \"\",\n            \"dataType\": \"string\",\n            \"isCustom\": false,\n            \"description\": \"location phone number\"\n        },\n        {\n            \"_id\": \"440\",\n            \"path\": \"email\",\n            \"locale\": \"\",\n            \"dataType\": \"string\",\n            \"isCustom\": false,\n            \"description\": \"email\"\n        },\n        {\n            \"_id\": \"450\",\n            \"path\": \"isPrimary#1\",\n            \"locale\": \"\",\n            \"dataType\": \"boolean\",\n            \"isCustom\": false,\n            \"description\": \"is primary (false/true)\"\n        },\n        {\n            \"_id\": \"460\",\n            \"path\": \"#attributeTypes\",\n            \"locale\": \"\",\n            \"dataType\": \"collection\",\n            \"isCustom\": false,\n            \"description\": \"attribute types\"\n        },\n        {\n            \"_id\": \"470\",\n            \"path\": \"*attributeType\",\n            \"locale\": \"\",\n            \"dataType\": \"reference\",\n            \"isCustom\": false,\n            \"description\": \"attribute type (#id)\"\n        },\n        {\n            \"_id\": \"480\",\n            \"path\": \"isRequired\",\n            \"locale\": \"\",\n            \"dataType\": \"boolean\",\n            \"isCustom\": false,\n            \"description\": \"is required (true/false)\"\n        },\n        {\n            \"_id\": \"490\",\n            \"path\": \"#dimensionTypes\",\n            \"locale\": \"\",\n            \"dataType\": \"collection\",\n            \"isCustom\": false,\n            \"description\": \"dimension types\"\n        },\n        {\n            \"_id\": \"500\",\n            \"path\": \"*dimensionType\",\n            \"locale\": \"\",\n            \"dataType\": \"reference\",\n            \"isCustom\": false,\n            \"description\": \"dimension type (#docProperty)\"\n        },\n        {\n            \"_id\": \"510\",\n            \"path\": \"isRequired#1\",\n            \"locale\": \"\",\n            \"dataType\": \"boolean\",\n            \"isCustom\": false,\n            \"description\": \"is required (true/false)\"\n        },\n        {\n            \"_id\": \"520\",\n            \"path\": \"#defaultAttributes\",\n            \"locale\": \"\",\n            \"dataType\": \"collection\",\n            \"isCustom\": false,\n            \"description\": \"default attributes\"\n        },\n        {\n            \"_id\": \"530\",\n            \"path\": \"!attributeType\",\n            \"locale\": \"\",\n            \"dataType\": \"reference\",\n            \"isCustom\": false,\n            \"description\": \"attribute type (#id)\"\n        },\n        {\n            \"_id\": \"540\",\n            \"path\": \"!dimensionDefinitionLevel\",\n            \"locale\": \"\",\n            \"dataType\": \"enum(manufacturingDirect,salesDirect,stockDirect,purchasingDirect,manufacturingOrderToOrder,purchasingOrderToOrder)\",\n            \"isCustom\": false,\n            \"description\": \"dimension definition level\"\n        },\n        {\n            \"_id\": \"550\",\n            \"path\": \"masterDataDefault\",\n            \"locale\": \"\",\n            \"dataType\": \"enum(site,sourceDocument,customer,supplier)\",\n            \"isCustom\": false,\n            \"description\": \"master data default\"\n        },\n        {\n            \"_id\": \"560\",\n            \"path\": \"#defaultDimensions\",\n            \"locale\": \"\",\n            \"dataType\": \"collection\",\n            \"isCustom\": false,\n            \"description\": \"default dimensions\"\n        },\n        {\n            \"_id\": \"570\",\n            \"path\": \"!dimensionType\",\n            \"locale\": \"\",\n            \"dataType\": \"reference\",\n            \"isCustom\": false,\n            \"description\": \"dimension type (#docProperty)\"\n        },\n        {\n            \"_id\": \"580\",\n            \"path\": \"!dimensionDefinitionLevel#1\",\n            \"locale\": \"\",\n            \"dataType\": \"enum(manufacturing,sales)\",\n            \"isCustom\": false,\n            \"description\": \"dimension definition level\"\n        },\n        {\n            \"_id\": \"590\",\n            \"path\": \"masterDataDefault#1\",\n            \"locale\": \"\",\n            \"dataType\": \"enum(site,sourceDocument,customer,supplier)\",\n            \"isCustom\": false,\n            \"description\": \"master data default\"\n        }\n    ]\n}\n"], ["sage", "Site", "{\"en\":\"Site\",\"base\":\"Site\",\"en-US\":\"Site\"}", "{\"en\":\"\",\"base\":\"\",\"en-US\":\"\"}", "Site", "Y", "Y", "importAndExport", "{\n    \"delimiter\": \";\",\n    \"dateFormat\": \"YYYY-MM-DD\",\n    \"decimalPoint\": \".\",\n    \"quoteCharacter\": \"'\",\n    \"escapeCharacter\": \"\\\\\"\n}\n", "{\n    \"data\": [\n        {\n            \"_id\": \"0\",\n            \"path\": \"!id\",\n            \"locale\": \"\",\n            \"dataType\": \"string\",\n            \"isCustom\": false,\n            \"description\": \"id\"\n        },\n        {\n            \"_id\": \"10\",\n            \"path\": \"*name\",\n            \"locale\": \"\",\n            \"dataType\": \"string\",\n            \"isCustom\": false,\n            \"description\": \"name\"\n        },\n        {\n            \"_id\": \"20\",\n            \"path\": \"description\",\n            \"locale\": \"\",\n            \"dataType\": \"string\",\n            \"isCustom\": false,\n            \"description\": \"description\"\n        },\n        {\n            \"_id\": \"30\",\n            \"path\": \"isActive\",\n            \"locale\": \"\",\n            \"dataType\": \"boolean\",\n            \"isCustom\": false,\n            \"description\": \"is active (false/true)\"\n        },\n        {\n            \"_id\": \"40\",\n            \"path\": \"*legalCompany\",\n            \"locale\": \"\",\n            \"dataType\": \"reference\",\n            \"isCustom\": false,\n            \"description\": \"legal company (#id)\"\n        },\n        {\n            \"_id\": \"50\",\n            \"path\": \"*businessEntity\",\n            \"locale\": \"\",\n            \"dataType\": \"reference\",\n            \"isCustom\": false,\n            \"description\": \"business entity (#id)\"\n        },\n        {\n            \"_id\": \"60\",\n            \"path\": \"isFinance\",\n            \"locale\": \"\",\n            \"dataType\": \"boolean\",\n            \"isCustom\": false,\n            \"description\": \"is finance (false/true)\"\n        },\n        {\n            \"_id\": \"70\",\n            \"path\": \"isPurchase\",\n            \"locale\": \"\",\n            \"dataType\": \"boolean\",\n            \"isCustom\": false,\n            \"description\": \"is purchase (false/true)\"\n        },\n        {\n            \"_id\": \"80\",\n            \"path\": \"isInventory\",\n            \"locale\": \"\",\n            \"dataType\": \"boolean\",\n            \"isCustom\": false,\n            \"description\": \"is inventory (false/true)\"\n        },\n        {\n            \"_id\": \"90\",\n            \"path\": \"isSales\",\n            \"locale\": \"\",\n            \"dataType\": \"boolean\",\n            \"isCustom\": false,\n            \"description\": \"is sales (false/true)\"\n        },\n        {\n            \"_id\": \"100\",\n            \"path\": \"isManufacturing\",\n            \"locale\": \"\",\n            \"dataType\": \"boolean\",\n            \"isCustom\": false,\n            \"description\": \"is manufacturing (false/true)\"\n        },\n        {\n            \"_id\": \"110\",\n            \"path\": \"isProjectManagement\",\n            \"locale\": \"\",\n            \"dataType\": \"boolean\",\n            \"isCustom\": false,\n            \"description\": \"is project management (false/true)\"\n        },\n        {\n            \"_id\": \"120\",\n            \"path\": \"primaryAddress\",\n            \"locale\": \"\",\n            \"dataType\": \"reference\",\n            \"isCustom\": false,\n            \"description\": \"primary address (#businessEntity|_sortValue)\"\n        },\n        {\n            \"_id\": \"130\",\n            \"path\": \"financialSite\",\n            \"locale\": \"\",\n            \"dataType\": \"reference\",\n            \"isCustom\": false,\n            \"description\": \"financial site (#id)\"\n        },\n        {\n            \"_id\": \"140\",\n            \"path\": \"isLocationManaged\",\n            \"locale\": \"\",\n            \"dataType\": \"boolean\",\n            \"isCustom\": false,\n            \"description\": \"is location managed (false/true)\"\n        },\n        {\n            \"_id\": \"150\",\n            \"path\": \"defaultLocation\",\n            \"locale\": \"\",\n            \"dataType\": \"reference\",\n            \"isCustom\": false,\n            \"description\": \"default location (#id|locationZone)\"\n        },\n        {\n            \"_id\": \"160\",\n            \"path\": \"sequenceNumberId\",\n            \"locale\": \"\",\n            \"dataType\": \"string\",\n            \"isCustom\": false,\n            \"description\": \"sequence number id\"\n        },\n        {\n            \"_id\": \"165\",\n            \"path\": \"timeZone\",\n            \"locale\": \"\",\n            \"dataType\": \"string\",\n            \"isCustom\": false,\n            \"description\": \"time zone\"\n        },\n        {\n            \"_id\": \"170\",\n            \"path\": \"defaultStockStatus\",\n            \"locale\": \"\",\n            \"dataType\": \"reference\",\n            \"isCustom\": false,\n            \"description\": \"default stock status (#id)\"\n        },\n        {\n            \"_id\": \"180\",\n            \"path\": \"isPurchaseRequisitionApprovalManaged\",\n            \"locale\": \"\",\n            \"dataType\": \"boolean\",\n            \"isCustom\": false,\n            \"description\": \"is purchase requisition approval managed (true/false)\"\n        },\n        {\n            \"_id\": \"190\",\n            \"path\": \"purchaseRequisitionDefaultApprover\",\n            \"locale\": \"\",\n            \"dataType\": \"reference\",\n            \"isCustom\": false,\n            \"description\": \"purchase requisition default approver (#email)\"\n        },\n        {\n            \"_id\": \"200\",\n            \"path\": \"purchaseRequisitionSubstituteApprover\",\n            \"locale\": \"\",\n            \"dataType\": \"reference\",\n            \"isCustom\": false,\n            \"description\": \"purchase requisition substitute approver (#email)\"\n        },\n        {\n            \"_id\": \"210\",\n            \"path\": \"isPurchaseOrderApprovalManaged\",\n            \"locale\": \"\",\n            \"dataType\": \"boolean\",\n            \"isCustom\": false,\n            \"description\": \"is purchase order approval managed (true/false)\"\n        },\n        {\n            \"_id\": \"220\",\n            \"path\": \"purchaseOrderDefaultApprover\",\n            \"locale\": \"\",\n            \"dataType\": \"reference\",\n            \"isCustom\": false,\n            \"description\": \"purchase order default approver (#email)\"\n        },\n        {\n            \"_id\": \"230\",\n            \"path\": \"purchaseOrderSubstituteApprover\",\n            \"locale\": \"\",\n            \"dataType\": \"reference\",\n            \"isCustom\": false,\n            \"description\": \"purchase order substitute approver (#email)\"\n        },\n        {\n            \"_id\": \"240\",\n            \"path\": \"isSalesReturnRequestApprovalManaged\",\n            \"locale\": \"\",\n            \"dataType\": \"boolean\",\n            \"isCustom\": false,\n            \"description\": \"is sales return request approval managed (true/false)\"\n        },\n        {\n            \"_id\": \"250\",\n            \"path\": \"salesReturnRequestDefaultApprover\",\n            \"locale\": \"\",\n            \"dataType\": \"reference\",\n            \"isCustom\": false,\n            \"description\": \"sales return request default approver (#email)\"\n        },\n        {\n            \"_id\": \"260\",\n            \"path\": \"salesReturnRequestSubstituteApprover\",\n            \"locale\": \"\",\n            \"dataType\": \"reference\",\n            \"isCustom\": false,\n            \"description\": \"sales return request substitute approver (#email)\"\n        }\n    ]\n}\n"], ["sage", "User", "{\"en\":\"User\",\"base\":\"User\",\"en-US\":\"User\"}", "{\"en\":\"\",\"base\":\"\",\"en-US\":\"\"}", "User", "Y", "Y", "importAndExport", "{\n    \"delimiter\": \";\",\n    \"dateFormat\": \"YYYY-MM-DD\",\n    \"decimalPoint\": \".\",\n    \"quoteCharacter\": \"'\",\n    \"escapeCharacter\": \"\\\\\"\n}\n", "{\n    \"data\": [\n        {\n            \"_id\": \"0\",\n            \"path\": \"!email\",\n            \"locale\": \"\",\n            \"dataType\": \"string\",\n            \"isCustom\": false,\n            \"description\": \"email\"\n        },\n        {\n            \"_id\": \"10\",\n            \"path\": \"isFirstAdminUser\",\n            \"locale\": \"\",\n            \"dataType\": \"boolean\",\n            \"isCustom\": false,\n            \"description\": \"is first admin user (false/true)\"\n        },\n        {\n            \"_id\": \"20\",\n            \"path\": \"*firstName\",\n            \"locale\": \"\",\n            \"dataType\": \"string\",\n            \"isCustom\": false,\n            \"description\": \"first name\"\n        },\n        {\n            \"_id\": \"30\",\n            \"path\": \"*lastName\",\n            \"locale\": \"\",\n            \"dataType\": \"string\",\n            \"isCustom\": false,\n            \"description\": \"last name\"\n        },\n        {\n            \"_id\": \"40\",\n            \"path\": \"isActive\",\n            \"locale\": \"\",\n            \"dataType\": \"boolean\",\n            \"isCustom\": false,\n            \"description\": \"is active (true/false)\"\n        },\n        {\n            \"_id\": \"50\",\n            \"path\": \"photo\",\n            \"locale\": \"\",\n            \"dataType\": \"binaryStream\",\n            \"isCustom\": false,\n            \"description\": \"photo\"\n        },\n        {\n            \"_id\": \"60\",\n            \"path\": \"userType\",\n            \"locale\": \"\",\n            \"dataType\": \"enum(application,system)\",\n            \"isCustom\": false,\n            \"description\": \"user type\"\n        },\n        {\n            \"_id\": \"70\",\n            \"path\": \"isAdministrator\",\n            \"locale\": \"\",\n            \"dataType\": \"boolean\",\n            \"isCustom\": false,\n            \"description\": \"is administrator (false/true)\"\n        },\n        {\n            \"_id\": \"80\",\n            \"path\": \"isApiUser\",\n            \"locale\": \"\",\n            \"dataType\": \"boolean\",\n            \"isCustom\": false,\n            \"description\": \"is api user (false/true)\"\n        },\n        {\n            \"_id\": \"90\",\n            \"path\": \"intacctId\",\n            \"locale\": \"\",\n            \"dataType\": \"string\",\n            \"isCustom\": false,\n            \"description\": \"intacct id\"\n        },\n        {\n            \"_id\": \"100\",\n            \"path\": \"recordNo\",\n            \"locale\": \"\",\n            \"dataType\": \"integer\",\n            \"isCustom\": false,\n            \"description\": \"record no\"\n        },\n        {\n            \"_id\": \"110\",\n            \"path\": \"isIntacct\",\n            \"locale\": \"\",\n            \"dataType\": \"boolean\",\n            \"isCustom\": false,\n            \"description\": \"is intacct (false/true)\"\n        },\n        {\n            \"_id\": \"120\",\n            \"path\": \"/preferences\",\n            \"locale\": \"\",\n            \"dataType\": \"reference\",\n            \"isCustom\": false,\n            \"description\": \"preferences (#user)\"\n        },\n        {\n            \"_id\": \"130\",\n            \"path\": \"isWelcomeMailSent\",\n            \"locale\": \"\",\n            \"dataType\": \"boolean\",\n            \"isCustom\": false,\n            \"description\": \"is welcome mail sent (false/true)\"\n        },\n        {\n            \"_id\": \"140\",\n            \"path\": \"/navigation\",\n            \"locale\": \"\",\n            \"dataType\": \"reference\",\n            \"isCustom\": false,\n            \"description\": \"navigation (#user)\"\n        },\n        {\n            \"_id\": \"150\",\n            \"path\": \"history\",\n            \"locale\": \"\",\n            \"dataType\": \"stringArray\",\n            \"isCustom\": false,\n            \"description\": \"history\"\n        },\n        {\n            \"_id\": \"160\",\n            \"path\": \"bookmarks\",\n            \"locale\": \"\",\n            \"dataType\": \"stringArray\",\n            \"isCustom\": false,\n            \"description\": \"bookmarks\"\n        },\n        {\n            \"_id\": \"170\",\n            \"path\": \"/billingRole\",\n            \"locale\": \"\",\n            \"dataType\": \"reference\",\n            \"isCustom\": false,\n            \"description\": \"billing role (#user)\"\n        },\n        {\n            \"_id\": \"180\",\n            \"path\": \"*role\",\n            \"locale\": \"\",\n            \"dataType\": \"reference\",\n            \"isCustom\": false,\n            \"description\": \"role (#id)\"\n        },\n        {\n            \"_id\": \"190\",\n            \"path\": \"#authorizationGroup\",\n            \"locale\": \"\",\n            \"dataType\": \"collection\",\n            \"isCustom\": false,\n            \"description\": \"authorization group\"\n        },\n        {\n            \"_id\": \"200\",\n            \"path\": \"isActive#1\",\n            \"locale\": \"\",\n            \"dataType\": \"boolean\",\n            \"isCustom\": false,\n            \"description\": \"is active (true/false)\"\n        },\n        {\n            \"_id\": \"210\",\n            \"path\": \"*group\",\n            \"locale\": \"\",\n            \"dataType\": \"reference\",\n            \"isCustom\": false,\n            \"description\": \"group (#id)\"\n        }\n    ]\n}\n"], ["sage", "Role", "{\"en\":\"Role\",\"base\":\"Role\",\"en-US\":\"Role\"}", "{\"en-US\":\"\"}", "Role", "Y", "Y", "importAndExport", "{\n    \"delimiter\": \";\",\n    \"dateFormat\": \"YYYY-MM-DD\",\n    \"decimalPoint\": \".\",\n    \"quoteCharacter\": \"'\",\n    \"escapeCharacter\": \"\\\\\"\n}\n", "{\n    \"data\": [\n        {\n            \"_id\": \"0\",\n            \"path\": \"isActive\",\n            \"locale\": \"\",\n            \"dataType\": \"boolean\",\n            \"isCustom\": false,\n            \"description\": \"is active (true/false)\"\n        },\n        {\n            \"_id\": \"10\",\n            \"path\": \"name\",\n            \"locale\": \"en-US\",\n            \"dataType\": \"localized text\",\n            \"isCustom\": false,\n            \"description\": \"\\\"default locale: en-US, other locales can be specified with (locale-name), for example \\\"\\\"name (de-DE)\\\"\\\". Available locales are (en-US, ...). You can also duplicate the columns to import several translations\\\"\"\n        },\n        {\n            \"_id\": \"20\",\n            \"path\": \"description\",\n            \"locale\": \"\",\n            \"dataType\": \"string\",\n            \"isCustom\": false,\n            \"description\": \"description\"\n        },\n        {\n            \"_id\": \"30\",\n            \"path\": \"!id\",\n            \"locale\": \"\",\n            \"dataType\": \"string\",\n            \"isCustom\": false,\n            \"description\": \"id\"\n        },\n        {\n            \"_id\": \"40\",\n            \"path\": \"isBillingRole\",\n            \"locale\": \"\",\n            \"dataType\": \"boolean\",\n            \"isCustom\": false,\n            \"description\": \"is billing role (false/true)\"\n        },\n        {\n            \"_id\": \"50\",\n            \"path\": \"#roles\",\n            \"locale\": \"\",\n            \"dataType\": \"collection\",\n            \"isCustom\": false,\n            \"description\": \"roles\"\n        },\n        {\n            \"_id\": \"60\",\n            \"path\": \"!_sortValue\",\n            \"locale\": \"\",\n            \"dataType\": \"integer\",\n            \"isCustom\": false,\n            \"description\": \"sort value\"\n        },\n        {\n            \"_id\": \"70\",\n            \"path\": \"*role\",\n            \"locale\": \"\",\n            \"dataType\": \"reference\",\n            \"isCustom\": false,\n            \"description\": \"role (#id)\"\n        },\n        {\n            \"_id\": \"80\",\n            \"path\": \"#activities\",\n            \"locale\": \"\",\n            \"dataType\": \"collection\",\n            \"isCustom\": false,\n            \"description\": \"activities\"\n        },\n        {\n            \"_id\": \"90\",\n            \"path\": \"_sortValue\",\n            \"locale\": \"\",\n            \"dataType\": \"integer\",\n            \"isCustom\": false,\n            \"description\": \"sort value\"\n        },\n        {\n            \"_id\": \"100\",\n            \"path\": \"!activity\",\n            \"locale\": \"\",\n            \"dataType\": \"reference\",\n            \"isCustom\": false,\n            \"description\": \"activity (#name)\"\n        },\n        {\n            \"_id\": \"110\",\n            \"path\": \"hasAllPermissions\",\n            \"locale\": \"\",\n            \"dataType\": \"boolean\",\n            \"isCustom\": false,\n            \"description\": \"has all permissions (false/true)\"\n        },\n        {\n            \"_id\": \"120\",\n            \"path\": \"permissions\",\n            \"locale\": \"\",\n            \"dataType\": \"stringArray\",\n            \"isCustom\": false,\n            \"description\": \"permissions\"\n        },\n        {\n            \"_id\": \"130\",\n            \"path\": \"isActive#1\",\n            \"locale\": \"\",\n            \"dataType\": \"boolean\",\n            \"isCustom\": false,\n            \"description\": \"is active (false/true)\"\n        }\n    ]\n}\n"], ["sage", "ShowCaseCountry", "{\"en\":\"ShowCaseCountry\",\"en-US\":\"ShowCaseCountry\"}", "{\"en\":\"Template for importing ShowCaseCountries\",\"en-US\":\"Template for importing ShowCaseCountries\"}", "ShowCaseCountry", "Y", "Y", "importAndExport", "{\n    \"delimiter\": \";\",\n    \"dateFormat\": \"YYYY-MM-DD\",\n    \"decimalPoint\": \".\",\n    \"quoteCharacter\": \"'\",\n    \"escapeCharacter\": \"\\\\\"\n}\n", "{\n    \"data\": [\n        {\n            \"_id\": \"0\",\n            \"path\": \"!code\",\n            \"locale\": \"\",\n            \"dataType\": \"string\",\n            \"description\": \"code\"\n        },\n        {\n            \"_id\": \"10\",\n            \"path\": \"phoneCountryCode\",\n            \"locale\": \"\",\n            \"dataType\": \"integer\",\n            \"description\": \"phoneCountryCode\"\n        },\n        {\n            \"_id\": \"20\",\n            \"path\": \"name\",\n            \"locale\": \"\",\n            \"dataType\": \"string\",\n            \"description\": \"name\"\n        }\n    ]\n}\n"], ["sage", "ShowCaseCustomer", "{\"en\":\"ShowCaseCustomer\",\"en-US\":\"ShowCaseCustomer\"}", "{\"en\":\"Template for importing ShowCaseCustomers\",\"en-US\":\"Template for importing ShowCaseCustomers\"}", "ShowCaseCustomer", "Y", "Y", "importAndExport", "{\n    \"delimiter\": \";\",\n    \"dateFormat\": \"YYYY-MM-DD\",\n    \"decimalPoint\": \".\",\n    \"quoteCharacter\": \"'\",\n    \"escapeCharacter\": \"\\\\\"\n}\n", "{\n    \"data\": [\n        {\n            \"_id\": \"0\",\n            \"path\": \"!name\",\n            \"locale\": \"\",\n            \"dataType\": \"string\",\n            \"description\": \"name\"\n        },\n        {\n            \"_id\": \"10\",\n            \"path\": \"contact<PERSON><PERSON>\",\n            \"locale\": \"\",\n            \"dataType\": \"string\",\n            \"description\": \"contact<PERSON><PERSON>\"\n        },\n        {\n            \"_id\": \"20\",\n            \"path\": \"email\",\n            \"locale\": \"\",\n            \"dataType\": \"string\",\n            \"description\": \"email\"\n        }\n    ]\n}\n"], ["sage", "ShowCaseProviderAddress", "{\"en\":\"ShowCaseProviderAddress\",\"en-US\":\"ShowCaseProviderAddress\"}", "{\"en\":\"Template for importing ShowCaseProviderAddresses\",\"en-US\":\"Template for importing ShowCaseProviderAddresses\"}", "ShowCaseProviderAddress", "Y", "Y", "importAndExport", "{\n    \"delimiter\": \";\",\n    \"dateFormat\": \"YYYY-MM-DD\",\n    \"decimalPoint\": \".\",\n    \"quoteCharacter\": \"'\",\n    \"escapeCharacter\": \"\\\\\"\n}\n", "{\n    \"data\": [\n        {\n            \"_id\": \"0\",\n            \"path\": \"!name\",\n            \"locale\": \"\",\n            \"dataType\": \"string\",\n            \"description\": \"name\"\n        },\n        {\n            \"_id\": \"10\",\n            \"path\": \"addressLine1\",\n            \"locale\": \"\",\n            \"dataType\": \"string\",\n            \"description\": \"addressLine1\"\n        },\n        {\n            \"_id\": \"20\",\n            \"path\": \"addressLine2\",\n            \"locale\": \"\",\n            \"dataType\": \"string\",\n            \"description\": \"addressLine2\"\n        },\n        {\n            \"_id\": \"30\",\n            \"path\": \"city\",\n            \"locale\": \"\",\n            \"dataType\": \"string\",\n            \"description\": \"city\"\n        },\n        {\n            \"_id\": \"40\",\n            \"path\": \"country\",\n            \"locale\": \"\",\n            \"dataType\": \"reference\",\n            \"description\": \"country\"\n        }\n    ]\n}\n"], ["sage", "ShowCaseProvider", "{\"en\":\"ShowCaseProvider\",\"en-US\":\"ShowCaseProvider\"}", "{\"en\":\"Template for importing ShowCaseProviders\",\"en-US\":\"Template for importing ShowCaseProviders\"}", "ShowCaseProvider", "Y", "Y", "importAndExport", "{\n    \"delimiter\": \";\",\n    \"dateFormat\": \"YYYY-MM-DD\",\n    \"decimalPoint\": \".\",\n    \"quoteCharacter\": \"'\",\n    \"escapeCharacter\": \"\\\\\"\n}\n", "{\n    \"data\": [\n        {\n            \"_id\": \"0\",\n            \"path\": \"!textField\",\n            \"locale\": \"\",\n            \"dataType\": \"string\",\n            \"description\": \"textField\"\n        },\n        {\n            \"_id\": \"10\",\n            \"path\": \"integerField\",\n            \"locale\": \"\",\n            \"dataType\": \"integer\",\n            \"description\": \"integerField\"\n        },\n        {\n            \"_id\": \"20\",\n            \"path\": \"decimalField\",\n            \"locale\": \"\",\n            \"dataType\": \"decimal\",\n            \"description\": \"decimalField\"\n        },\n        {\n            \"_id\": \"30\",\n            \"path\": \"booleanField\",\n            \"locale\": \"\",\n            \"dataType\": \"boolean\",\n            \"description\": \"booleanField\"\n        },\n        {\n            \"_id\": \"40\",\n            \"path\": \"dateField\",\n            \"locale\": \"\",\n            \"dataType\": \"date\",\n            \"description\": \"dateField\"\n        },\n        {\n            \"_id\": \"50\",\n            \"path\": \"document\",\n            \"locale\": \"\",\n            \"dataType\": \"textStream\",\n            \"description\": \"\"\n        },\n        {\n            \"_id\": \"60\",\n            \"path\": \"logo\",\n            \"locale\": \"\",\n            \"dataType\": \"binaryStream\",\n            \"description\": \"\"\n        },\n        {\n            \"_id\": \"70\",\n            \"path\": \"siteAddress(name)\",\n            \"locale\": \"\",\n            \"dataType\": \"reference\",\n            \"description\": \"\"\n        },\n        {\n            \"_id\": \"80\",\n            \"path\": \"addresses\",\n            \"locale\": \"\",\n            \"dataType\": \"referenceArray\",\n            \"description\": \"\"\n        },\n        {\n            \"_id\": \"90\",\n            \"path\": \"flagshipProduct(_id)\",\n            \"locale\": \"\",\n            \"dataType\": \"reference\",\n            \"description\": \"\"\n        },\n        {\n            \"_id\": \"100\",\n            \"path\": \"item(_id)\",\n            \"locale\": \"\",\n            \"dataType\": \"reference\",\n            \"description\": \"\"\n        },\n        {\n            \"_id\": \"110\",\n            \"path\": \"minQuantity\",\n            \"locale\": \"\",\n            \"dataType\": \"integer\",\n            \"description\": \"\"\n        },\n        {\n            \"_id\": \"120\",\n            \"path\": \"ratings\",\n            \"locale\": \"\",\n            \"dataType\": \"enumArray\",\n            \"description\": \"\"\n        },\n        {\n            \"_id\": \"130\",\n            \"path\": \"#products\",\n            \"locale\": \"\",\n            \"dataType\": \"collection\",\n            \"description\": \"\"\n        },\n        {\n            \"_id\": \"140\",\n            \"path\": \"!product\",\n            \"locale\": \"\",\n            \"dataType\": \"string\",\n            \"description\": \"\"\n        },\n        {\n            \"_id\": \"150\",\n            \"path\": \"barcode\",\n            \"locale\": \"\",\n            \"dataType\": \"string\",\n            \"description\": \"\"\n        },\n        {\n            \"_id\": \"160\",\n            \"path\": \"description\",\n            \"locale\": \"\",\n            \"dataType\": \"string\",\n            \"description\": \"\"\n        },\n        {\n            \"_id\": \"170\",\n            \"path\": \"hotProduct\",\n            \"locale\": \"\",\n            \"dataType\": \"boolean\",\n            \"description\": \"\"\n        },\n        {\n            \"_id\": \"180\",\n            \"path\": \"fixedQuantity\",\n            \"locale\": \"\",\n            \"dataType\": \"integer\",\n            \"description\": \"\"\n        },\n        {\n            \"_id\": \"190\",\n            \"path\": \"st\",\n            \"locale\": \"\",\n            \"dataType\": \"integer\",\n            \"description\": \"\"\n        },\n        {\n            \"_id\": \"200\",\n            \"path\": \"listPrice\",\n            \"locale\": \"\",\n            \"dataType\": \"decimal\",\n            \"description\": \"\"\n        },\n        {\n            \"_id\": \"210\",\n            \"path\": \"progress\",\n            \"locale\": \"\",\n            \"dataType\": \"integer\",\n            \"description\": \"\"\n        },\n        {\n            \"_id\": \"220\",\n            \"path\": \"tax\",\n            \"locale\": \"\",\n            \"dataType\": \"decimal\",\n            \"description\": \"\"\n        },\n        {\n            \"_id\": \"230\",\n            \"path\": \"amount\",\n            \"locale\": \"\",\n            \"dataType\": \"decimal\",\n            \"description\": \"\"\n        },\n        {\n            \"_id\": \"240\",\n            \"path\": \"email\",\n            \"locale\": \"\",\n            \"dataType\": \"string\",\n            \"description\": \"\"\n        },\n        {\n            \"_id\": \"250\",\n            \"path\": \"releaseDate\",\n            \"locale\": \"\",\n            \"dataType\": \"date\",\n            \"description\": \"\"\n        },\n        {\n            \"_id\": \"260\",\n            \"path\": \"endingDate\",\n            \"locale\": \"\",\n            \"dataType\": \"date\",\n            \"description\": \"\"\n        },\n        {\n            \"_id\": \"270\",\n            \"path\": \"category\",\n            \"locale\": \"\",\n            \"dataType\": \"enum\",\n            \"description\": \"\"\n        },\n        {\n            \"_id\": \"280\",\n            \"path\": \"subcategories\",\n            \"locale\": \"\",\n            \"dataType\": \"enumArray\",\n            \"description\": \"\"\n        },\n        {\n            \"_id\": \"290\",\n            \"path\": \"entries\",\n            \"locale\": \"\",\n            \"dataType\": \"stringArray\",\n            \"description\": \"\"\n        },\n        {\n            \"_id\": \"300\",\n            \"path\": \"qty\",\n            \"locale\": \"\",\n            \"dataType\": \"integer\",\n            \"description\": \"\"\n        },\n        {\n            \"_id\": \"310\",\n            \"path\": \"netPrice\",\n            \"locale\": \"\",\n            \"dataType\": \"decimal\",\n            \"description\": \"\"\n        },\n        {\n            \"_id\": \"320\",\n            \"path\": \"//originAddress\",\n            \"locale\": \"\",\n            \"dataType\": \"reference\",\n            \"description\": \"\"\n        },\n        {\n            \"_id\": \"330\",\n            \"path\": \"name\",\n            \"locale\": \"\",\n            \"dataType\": \"string\",\n            \"description\": \"\"\n        },\n        {\n            \"_id\": \"340\",\n            \"path\": \"addressLine1\",\n            \"locale\": \"\",\n            \"dataType\": \"string\",\n            \"description\": \"\"\n        },\n        {\n            \"_id\": \"350\",\n            \"path\": \"addressLine2\",\n            \"locale\": \"\",\n            \"dataType\": \"string\",\n            \"description\": \"\"\n        },\n        {\n            \"_id\": \"360\",\n            \"path\": \"city\",\n            \"locale\": \"\",\n            \"dataType\": \"string\",\n            \"description\": \"\"\n        },\n        {\n            \"_id\": \"370\",\n            \"path\": \"country\",\n            \"locale\": \"\",\n            \"dataType\": \"reference\",\n            \"description\": \"\"\n        }\n    ]\n}\n"], ["sage", "ShowCaseOrder", "{\"en\":\"ShowCaseOrder\",\"en-US\":\"ShowCaseOrder\"}", "{\"en\":\"Template for importing ShowCaseOrders\",\"en-US\":\"Template for importing ShowCaseOrders\"}", "ShowCaseOrder", "Y", "Y", "importAndExport", "{\n    \"delimiter\": \";\",\n    \"dateFormat\": \"YYYY-MM-DD\",\n    \"decimalPoint\": \".\",\n    \"quoteCharacter\": \"'\",\n    \"escapeCharacter\": \"\\\\\"\n}\n", "{\n    \"data\": [\n        {\n            \"_id\": \"0\",\n            \"path\": \"customer(name)\",\n            \"locale\": \"\",\n            \"dataType\": \"string\",\n            \"description\": \"acustomer(name)\"\n        },\n        {\n            \"_id\": \"10\",\n            \"path\": \"orderDate\",\n            \"locale\": \"\",\n            \"dataType\": \"date\",\n            \"description\": \"orderDate\"\n        },\n        {\n            \"_id\": \"20\",\n            \"path\": \"details\",\n            \"locale\": \"\",\n            \"dataType\": \"textStream\",\n            \"description\": \"details\"\n        }\n    ]\n}\n"], ["sage", "ShowCaseInvoice", "{\"en\":\"ShowCaseInvoice\",\"en-US\":\"ShowCaseInvoice\"}", "{\"en\":\"Template for importing ShowCaseInvoices\",\"en-US\":\"Template for importing ShowCaseInvoices\"}", "ShowCaseInvoice", "Y", "Y", "importAndExport", "{\n    \"delimiter\": \";\",\n    \"dateFormat\": \"YYYY-MM-DD\",\n    \"decimalPoint\": \".\",\n    \"quoteCharacter\": \"'\",\n    \"escapeCharacter\": \"\\\\\"\n}\n", "{\n    \"data\": [\n        {\n            \"_id\": \"0\",\n            \"path\": \"customer(name)\",\n            \"locale\": \"\",\n            \"dataType\": \"string\",\n            \"description\": \"customer(name)\"\n        },\n        {\n            \"_id\": \"10\",\n            \"path\": \"purchaseDate\",\n            \"locale\": \"\",\n            \"dataType\": \"date\",\n            \"description\": \"purchaseDate\"\n        },\n        {\n            \"_id\": \"20\",\n            \"path\": \"notes\",\n            \"locale\": \"\",\n            \"dataType\": \"textStream\",\n            \"description\": \"notes\"\n        },\n        {\n            \"_id\": \"30\",\n            \"path\": \"pdf\",\n            \"locale\": \"\",\n            \"dataType\": \"binaryStream\",\n            \"description\": \"pdf\"\n        },\n        {\n            \"_id\": \"40\",\n            \"path\": \"queryText\",\n            \"locale\": \"\",\n            \"dataType\": \"textStream\",\n            \"description\": \"queryText\"\n        },\n        {\n            \"_id\": \"50\",\n            \"path\": \"order(_id)\",\n            \"locale\": \"\",\n            \"dataType\": \"reference\",\n            \"description\": \"order(_id)\"\n        },\n        {\n            \"_id\": \"60\",\n            \"path\": \"#lines\",\n            \"locale\": \"\",\n            \"dataType\": \"collection\",\n            \"description\": \"#lines\"\n        },\n        {\n            \"_id\": \"70\",\n            \"path\": \"orderQuantity\",\n            \"locale\": \"\",\n            \"dataType\": \"integer\",\n            \"description\": \"orderQuantity\"\n        },\n        {\n            \"_id\": \"80\",\n            \"path\": \"comments\",\n            \"locale\": \"\",\n            \"dataType\": \"string\",\n            \"description\": \"comments\"\n        },\n        {\n            \"_id\": \"90\",\n            \"path\": \"product(_id)\",\n            \"locale\": \"\",\n            \"dataType\": \"reference\",\n            \"description\": \"product\"\n        },\n        {\n            \"_id\": \"100\",\n            \"path\": \"pdf\",\n            \"locale\": \"\",\n            \"dataType\": \"binaryStream\",\n            \"description\": \"pdf\"\n        },\n        {\n            \"_id\": \"110\",\n            \"path\": \"netPrice\",\n            \"locale\": \"\",\n            \"dataType\": \"decimal\",\n            \"description\": \"netPrice\"\n        },\n        {\n            \"_id\": \"120\",\n            \"path\": \"discountType\",\n            \"locale\": \"\",\n            \"dataType\": \"enum\",\n            \"description\": \"discountType\"\n        }\n    ]\n}\n"]]}, "SysServiceOptionState": {"metadata": {"rootFactoryName": "SysServiceOptionState", "name": "SysServiceOptionState", "naturalKeyColumns": ["_tenant_id", "service_option"], "columns": [{"name": "is_activable", "type": "boolean"}, {"name": "is_active", "type": "boolean"}, {"name": "service_option", "type": "reference", "targetFactoryName": "SysServiceOption"}]}, "rows": [["Y", null, "devTools"], ["Y", null, "isDemoTenant"], ["Y", null, "changelog"], ["Y", null, "notificationCenter"], ["Y", null, "authorizationServiceOption"], ["Y", null, "auditingOption"], ["Y", null, "auditing"], ["Y", null, "workflow"], ["Y", null, "workflowAdvanced"], ["N", null, "workflowOption"], ["Y", null, "synchronizationServiceOption"], ["Y", "N", "showCaseOptionHighLevel"], ["Y", "N", "showCaseWorkInProgressOption"], ["Y", "Y", "showCaseOption3"], ["Y", "N", "showCaseOption2"], ["Y", "N", "showCaseOption1"], ["Y", "N", "showCaseExperimentalOption"], ["Y", "N", "showCaseDiscountOption"]]}}}