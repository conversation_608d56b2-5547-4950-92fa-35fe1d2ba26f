{"extends": "../../../tsconfig-base.json", "compilerOptions": {"outDir": "build", "rootDir": ".", "baseUrl": ".", "composite": true}, "include": ["api.d.ts"], "references": [{"path": "../../../system/xtrem-authorization/api"}, {"path": "../../../front-end/xtrem-client"}, {"path": "../../../system/xtrem-communication/api"}, {"path": "../../../system/xtrem-dashboard/api"}, {"path": "../../../system/xtrem-import-export/api"}, {"path": "../../../system/xtrem-mailer/api"}, {"path": "../../../system/xtrem-reporting/api"}, {"path": "../../../system/xtrem-routing/api"}, {"path": "../../../system/xtrem-scheduler/api"}, {"path": "../../../system/xtrem-system/api"}]}