declare module '@sage/xtrem-show-case-api-partial' {
    import type { Package as SageXtremAuditing$Package } from '@sage/xtrem-auditing-api';
    import type { Package as SageXtremAuthorization$Package } from '@sage/xtrem-authorization-api';
    import type { Package as SageXtremCommunication$Package } from '@sage/xtrem-communication-api';
    import type { Package as SageXtremCustomization$Package } from '@sage/xtrem-customization-api';
    import type { Package as SageXtremDashboard$Package } from '@sage/xtrem-dashboard-api';
    import type { Package as SageXtremImportExport$Package } from '@sage/xtrem-import-export-api';
    import type { Package as SageXtremInterop$Package } from '@sage/xtrem-interop-api';
    import type { Package as SageXtremMailer$Package } from '@sage/xtrem-mailer-api';
    import type { MetaNodeFactory, Package as SageXtremMetadata$Package } from '@sage/xtrem-metadata-api';
    import type { Package as SageXtremReporting$Package } from '@sage/xtrem-reporting-api';
    import type { Package as SageXtremRouting$Package } from '@sage/xtrem-routing-api';
    import type { Package as SageXtremScheduler$Package } from '@sage/xtrem-scheduler-api';
    import type { Package as SageXtremSystem$Package, SysVendor, User } from '@sage/xtrem-system-api';
    import type {
        AttachmentAssociation,
        AttachmentAssociationInput,
        Package as SageXtremUpload$Package,
        UploadedFile,
    } from '@sage/xtrem-upload-api';
    import type { Package as SageXtremWorkflow$Package } from '@sage/xtrem-workflow-api';
    import type {
        AggregateQueryOperation,
        AggregateReadOperation,
        AsyncOperation,
        BinaryStream,
        ClientCollection,
        ClientNode,
        ClientNodeInput,
        CreateOperation,
        DeleteOperation,
        DuplicateOperation,
        GetDefaultsOperation,
        GetDuplicateOperation,
        Operation as Node$Operation,
        QueryOperation,
        ReadOperation,
        TextStream,
        UpdateByIdOperation,
        UpdateOperation,
        VitalClientNode,
        VitalClientNodeInput,
        decimal,
        integer,
    } from '@sage/xtrem-client';
    export interface ShowCaseDiscountType$Enum {
        newProductPromotion: 0;
        compensation: 1;
    }
    export type ShowCaseDiscountType = keyof ShowCaseDiscountType$Enum;
    export interface ShowCaseOrderType$Enum {
        online: 0;
        inStore: 1;
    }
    export type ShowCaseOrderType = keyof ShowCaseOrderType$Enum;
    export interface ShowCaseProductCategory$Enum {
        great: 0;
        good: 1;
        ok: 2;
        notBad: 3;
        awful: 4;
    }
    export type ShowCaseProductCategory = keyof ShowCaseProductCategory$Enum;
    export interface ShowCaseProviderRating$Enum {
        excellent: 0;
        good: 1;
        average: 2;
        poor: 3;
        horrible: 4;
    }
    export type ShowCaseProviderRating = keyof ShowCaseProviderRating$Enum;
    export interface ShowCaseCountry extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        code: string;
        phoneCountryCode: integer;
        name: string;
        employeesInCountry: ClientCollection<ShowCaseEmployee>;
    }
    export interface ShowCaseCountryInput extends ClientNodeInput {
        code?: string;
        phoneCountryCode?: integer | string;
        name?: string;
    }
    export interface ShowCaseCountryBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        code: string;
        phoneCountryCode: integer;
        name: string;
        employeesInCountry: ClientCollection<ShowCaseEmployee>;
    }
    export interface ShowCaseCountry$Mutations {
        generateFakeNotification: Node$Operation<
            {
                title: string;
                description: string;
                icon: string;
                level: SysClientNotificationLevel;
                shouldDisplayToast: boolean | string;
                actions?: {
                    _id?: string;
                    link?: string;
                    title?: string;
                    style?: SysClientNotificationActionStyle;
                }[];
            },
            boolean
        >;
    }
    export interface ShowCaseCountry$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface ShowCaseCountry$Operations {
        query: QueryOperation<ShowCaseCountry>;
        read: ReadOperation<ShowCaseCountry>;
        aggregate: {
            read: AggregateReadOperation<ShowCaseCountry>;
            query: AggregateQueryOperation<ShowCaseCountry>;
        };
        create: CreateOperation<ShowCaseCountryInput, ShowCaseCountry>;
        getDuplicate: GetDuplicateOperation<ShowCaseCountry>;
        duplicate: DuplicateOperation<string, ShowCaseCountryInput, ShowCaseCountry>;
        update: UpdateOperation<ShowCaseCountryInput, ShowCaseCountry>;
        updateById: UpdateByIdOperation<ShowCaseCountryInput, ShowCaseCountry>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        mutations: ShowCaseCountry$Mutations;
        asyncOperations: ShowCaseCountry$AsyncOperations;
        getDefaults: GetDefaultsOperation<ShowCaseCountry>;
    }
    export interface ShowCaseCustomer extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        name: string;
        contactPerson: string;
        email: string;
        invoices: ClientCollection<ShowCaseInvoice>;
        orders: ClientCollection<ShowCaseOrder>;
    }
    export interface ShowCaseCustomerInput extends ClientNodeInput {
        name?: string;
        contactPerson?: string;
        email?: string;
        orders?: Partial<ShowCaseOrderInput>[];
    }
    export interface ShowCaseCustomerBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        name: string;
        contactPerson: string;
        email: string;
        invoices: ClientCollection<ShowCaseInvoice>;
        orders: ClientCollection<ShowCaseOrderBinding>;
    }
    export interface ShowCaseCustomer$Mutations {
        testMutation: Node$Operation<
            {
                something?: string;
            },
            string
        >;
        anotherTestMutation: Node$Operation<
            {
                something?: string;
            },
            string
        >;
    }
    export interface ShowCaseCustomer$AsyncOperations {
        testAsyncMutation: AsyncOperation<
            {
                customer?: ShowCaseCustomerInput;
            },
            string
        >;
        asyncTestMutation: AsyncOperation<
            {
                customerId: string;
                newName: string;
                mockedProcessingTime: integer | string;
                shouldFail?: boolean | string;
            },
            {
                name: string;
                _id: string;
            }
        >;
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface ShowCaseCustomer$Operations {
        query: QueryOperation<ShowCaseCustomer>;
        read: ReadOperation<ShowCaseCustomer>;
        aggregate: {
            read: AggregateReadOperation<ShowCaseCustomer>;
            query: AggregateQueryOperation<ShowCaseCustomer>;
        };
        create: CreateOperation<ShowCaseCustomerInput, ShowCaseCustomer>;
        getDuplicate: GetDuplicateOperation<ShowCaseCustomer>;
        update: UpdateOperation<ShowCaseCustomerInput, ShowCaseCustomer>;
        updateById: UpdateByIdOperation<ShowCaseCustomerInput, ShowCaseCustomer>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        mutations: ShowCaseCustomer$Mutations;
        asyncOperations: ShowCaseCustomer$AsyncOperations;
        getDefaults: GetDefaultsOperation<ShowCaseCustomer>;
    }
    export interface ShowCaseEmployee extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        firstName: string;
        lastName: string;
        city: string;
        email: string;
        country: ShowCaseCountry;
        manager: ShowCaseEmployee;
        teamMembers: ClientCollection<ShowCaseEmployee>;
    }
    export interface ShowCaseEmployeeInput extends ClientNodeInput {
        firstName?: string;
        lastName?: string;
        city?: string;
        email?: string;
        country?: integer | string;
        manager?: integer | string;
    }
    export interface ShowCaseEmployeeBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        firstName: string;
        lastName: string;
        city: string;
        email: string;
        country: ShowCaseCountry;
        manager: ShowCaseEmployee;
        teamMembers: ClientCollection<ShowCaseEmployee>;
    }
    export interface ShowCaseEmployee$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface ShowCaseEmployee$Lookups {
        country: QueryOperation<ShowCaseCountry>;
        manager: QueryOperation<ShowCaseEmployee>;
    }
    export interface ShowCaseEmployee$Operations {
        query: QueryOperation<ShowCaseEmployee>;
        read: ReadOperation<ShowCaseEmployee>;
        aggregate: {
            read: AggregateReadOperation<ShowCaseEmployee>;
            query: AggregateQueryOperation<ShowCaseEmployee>;
        };
        create: CreateOperation<ShowCaseEmployeeInput, ShowCaseEmployee>;
        getDuplicate: GetDuplicateOperation<ShowCaseEmployee>;
        update: UpdateOperation<ShowCaseEmployeeInput, ShowCaseEmployee>;
        updateById: UpdateByIdOperation<ShowCaseEmployeeInput, ShowCaseEmployee>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: ShowCaseEmployee$AsyncOperations;
        lookups(dataOrId: string | { data: ShowCaseEmployeeInput }): ShowCaseEmployee$Lookups;
        getDefaults: GetDefaultsOperation<ShowCaseEmployee>;
    }
    export interface ShowCaseInvoice extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        purchaseDate: string;
        notes: TextStream;
        totalProductQty: integer;
        pdf: BinaryStream;
        queryText: TextStream;
        order: ShowCaseOrder;
        _attachments: ClientCollection<AttachmentAssociation>;
        customer: ShowCaseCustomer;
        lines: ClientCollection<ShowCaseInvoiceLine>;
    }
    export interface ShowCaseInvoiceInput extends VitalClientNodeInput {
        purchaseDate?: string;
        notes?: TextStream;
        pdf?: BinaryStream;
        queryText?: TextStream;
        _attachments?: Partial<AttachmentAssociationInput>[];
        customer?: integer | string;
        lines?: Partial<ShowCaseInvoiceLineInput>[];
    }
    export interface ShowCaseInvoiceBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        purchaseDate: string;
        notes: TextStream;
        totalProductQty: integer;
        pdf: BinaryStream;
        queryText: TextStream;
        order: ShowCaseOrder;
        _attachments: ClientCollection<AttachmentAssociation>;
        customer: ShowCaseCustomer;
        lines: ClientCollection<ShowCaseInvoiceLineBinding>;
    }
    export interface ShowCaseInvoice$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface ShowCaseInvoice$Lookups {
        customer: QueryOperation<ShowCaseCustomer>;
    }
    export interface ShowCaseInvoice$Operations {
        query: QueryOperation<ShowCaseInvoice>;
        read: ReadOperation<ShowCaseInvoice>;
        aggregate: {
            read: AggregateReadOperation<ShowCaseInvoice>;
            query: AggregateQueryOperation<ShowCaseInvoice>;
        };
        create: CreateOperation<ShowCaseInvoiceInput, ShowCaseInvoice>;
        getDuplicate: GetDuplicateOperation<ShowCaseInvoice>;
        update: UpdateOperation<ShowCaseInvoiceInput, ShowCaseInvoice>;
        updateById: UpdateByIdOperation<ShowCaseInvoiceInput, ShowCaseInvoice>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: ShowCaseInvoice$AsyncOperations;
        lookups(dataOrId: string | { data: ShowCaseInvoiceInput }): ShowCaseInvoice$Lookups;
        getDefaults: GetDefaultsOperation<ShowCaseInvoice>;
    }
    export interface ShowCaseInvoiceLine extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        invoice: ShowCaseInvoice;
        orderQuantity: integer;
        comments: string;
        product: ShowCaseProduct;
        netPrice: string;
        priceDiscount: string;
        discountType: ShowCaseDiscountType;
    }
    export interface ShowCaseInvoiceLineInput extends VitalClientNodeInput {
        orderQuantity?: integer | string;
        comments?: string;
        product?: integer | string;
        netPrice?: decimal | string;
        priceDiscount?: decimal | string;
        discountType?: ShowCaseDiscountType;
    }
    export interface ShowCaseInvoiceLineBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        invoice: ShowCaseInvoice;
        orderQuantity: integer;
        comments: string;
        product: ShowCaseProduct;
        netPrice: string;
        priceDiscount: string;
        discountType: ShowCaseDiscountType;
    }
    export interface ShowCaseInvoiceLine$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface ShowCaseInvoiceLine$Lookups {
        product: QueryOperation<ShowCaseProduct>;
    }
    export interface ShowCaseInvoiceLine$Operations {
        query: QueryOperation<ShowCaseInvoiceLine>;
        read: ReadOperation<ShowCaseInvoiceLine>;
        aggregate: {
            read: AggregateReadOperation<ShowCaseInvoiceLine>;
            query: AggregateQueryOperation<ShowCaseInvoiceLine>;
        };
        create: CreateOperation<ShowCaseInvoiceLineInput, ShowCaseInvoiceLine>;
        getDuplicate: GetDuplicateOperation<ShowCaseInvoiceLine>;
        update: UpdateOperation<ShowCaseInvoiceLineInput, ShowCaseInvoiceLine>;
        updateById: UpdateByIdOperation<ShowCaseInvoiceLineInput, ShowCaseInvoiceLine>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: ShowCaseInvoiceLine$AsyncOperations;
        lookups(dataOrId: string | { data: ShowCaseInvoiceLineInput }): ShowCaseInvoiceLine$Lookups;
        getDefaults: GetDefaultsOperation<ShowCaseInvoiceLine>;
    }
    export interface ShowCaseItem extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        quantity: integer;
        unitPrice: integer;
        name: string;
        providers: ClientCollection<ShowCaseProvider>;
    }
    export interface ShowCaseItemInput extends ClientNodeInput {
        quantity?: integer | string;
        unitPrice?: integer | string;
        name?: string;
    }
    export interface ShowCaseItemBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        quantity: integer;
        unitPrice: integer;
        name: string;
        providers: ClientCollection<ShowCaseProvider>;
    }
    export interface ShowCaseItem$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface ShowCaseItem$Operations {
        query: QueryOperation<ShowCaseItem>;
        read: ReadOperation<ShowCaseItem>;
        aggregate: {
            read: AggregateReadOperation<ShowCaseItem>;
            query: AggregateQueryOperation<ShowCaseItem>;
        };
        create: CreateOperation<ShowCaseItemInput, ShowCaseItem>;
        getDuplicate: GetDuplicateOperation<ShowCaseItem>;
        update: UpdateOperation<ShowCaseItemInput, ShowCaseItem>;
        updateById: UpdateByIdOperation<ShowCaseItemInput, ShowCaseItem>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: ShowCaseItem$AsyncOperations;
        getDefaults: GetDefaultsOperation<ShowCaseItem>;
    }
    export interface ShowCaseNodeBrowserTree extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        checkedItems: string;
    }
    export interface ShowCaseNodeBrowserTreeInput extends ClientNodeInput {
        checkedItems?: string;
    }
    export interface ShowCaseNodeBrowserTreeBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        checkedItems: any;
    }
    export interface ShowCaseNodeBrowserTree$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface ShowCaseNodeBrowserTree$Operations {
        query: QueryOperation<ShowCaseNodeBrowserTree>;
        read: ReadOperation<ShowCaseNodeBrowserTree>;
        aggregate: {
            read: AggregateReadOperation<ShowCaseNodeBrowserTree>;
            query: AggregateQueryOperation<ShowCaseNodeBrowserTree>;
        };
        create: CreateOperation<ShowCaseNodeBrowserTreeInput, ShowCaseNodeBrowserTree>;
        getDuplicate: GetDuplicateOperation<ShowCaseNodeBrowserTree>;
        update: UpdateOperation<ShowCaseNodeBrowserTreeInput, ShowCaseNodeBrowserTree>;
        updateById: UpdateByIdOperation<ShowCaseNodeBrowserTreeInput, ShowCaseNodeBrowserTree>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: ShowCaseNodeBrowserTree$AsyncOperations;
        getDefaults: GetDefaultsOperation<ShowCaseNodeBrowserTree>;
    }
    export interface ShowCaseOrder extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        customer: ShowCaseCustomer;
        orderDate: string;
        details: TextStream;
        invoices: ClientCollection<ShowCaseInvoice>;
        orderType: ShowCaseOrderType;
        _attachments: ClientCollection<AttachmentAssociation>;
    }
    export interface ShowCaseOrderInput extends VitalClientNodeInput {
        orderDate?: string;
        details?: TextStream;
        invoices?: Partial<ShowCaseInvoiceInput>[];
        orderType?: ShowCaseOrderType;
        _attachments?: Partial<AttachmentAssociationInput>[];
    }
    export interface ShowCaseOrderBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        customer: ShowCaseCustomer;
        orderDate: string;
        details: TextStream;
        invoices: ClientCollection<ShowCaseInvoiceBinding>;
        orderType: ShowCaseOrderType;
        _attachments: ClientCollection<AttachmentAssociation>;
    }
    export interface ShowCaseOrder$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface ShowCaseOrder$Operations {
        query: QueryOperation<ShowCaseOrder>;
        read: ReadOperation<ShowCaseOrder>;
        aggregate: {
            read: AggregateReadOperation<ShowCaseOrder>;
            query: AggregateQueryOperation<ShowCaseOrder>;
        };
        create: CreateOperation<ShowCaseOrderInput, ShowCaseOrder>;
        getDuplicate: GetDuplicateOperation<ShowCaseOrder>;
        update: UpdateOperation<ShowCaseOrderInput, ShowCaseOrder>;
        updateById: UpdateByIdOperation<ShowCaseOrderInput, ShowCaseOrder>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: ShowCaseOrder$AsyncOperations;
        getDefaults: GetDefaultsOperation<ShowCaseOrder>;
    }
    export interface ShowCaseProduct extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        product: string;
        barcode: string;
        description: string;
        hotProduct: boolean;
        fixedQuantity: integer;
        st: integer;
        listPrice: string;
        progress: integer;
        discount: integer;
        tax: string;
        amount: string;
        total: string;
        provider: ShowCaseProvider;
        email: string;
        computedEmail: string;
        importedAt: string;
        releaseDate: string;
        endingDate: string;
        category: ShowCaseProductCategory;
        subcategories: ShowCaseProductCategory[];
        entries: string[];
        designerEmployee: ShowCaseEmployee;
        originAddress: ShowCaseProductOriginAddress;
        certificate: UploadedFile;
        createdAt: string;
        manufacturedWithin: string;
        imageField: BinaryStream;
        _attachments: ClientCollection<AttachmentAssociation>;
        qty: integer;
        netPrice: string;
    }
    export interface ShowCaseProductInput extends VitalClientNodeInput {
        product?: string;
        barcode?: string;
        description?: string;
        hotProduct?: boolean | string;
        fixedQuantity?: integer | string;
        st?: integer | string;
        listPrice?: decimal | string;
        progress?: integer | string;
        discount?: integer | string;
        tax?: decimal | string;
        amount?: decimal | string;
        email?: string;
        importedAt?: string;
        releaseDate?: string;
        endingDate?: string;
        category?: ShowCaseProductCategory;
        subcategories?: ShowCaseProductCategory[];
        entries?: string[];
        designerEmployee?: integer | string;
        originAddress?: ShowCaseProductOriginAddressInput;
        certificate?: integer | string;
        createdAt?: string;
        manufacturedWithin?: string;
        _attachments?: Partial<AttachmentAssociationInput>[];
        qty?: integer | string;
        netPrice?: decimal | string;
    }
    export interface ShowCaseProductBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        product: string;
        barcode: string;
        description: string;
        hotProduct: boolean;
        fixedQuantity: integer;
        st: integer;
        listPrice: string;
        progress: integer;
        discount: integer;
        tax: string;
        amount: string;
        total: string;
        provider: ShowCaseProvider;
        email: string;
        computedEmail: string;
        importedAt: string;
        releaseDate: string;
        endingDate: string;
        category: ShowCaseProductCategory;
        subcategories: ShowCaseProductCategory[];
        entries: string[];
        designerEmployee: ShowCaseEmployee;
        originAddress: ShowCaseProductOriginAddressBinding;
        certificate: UploadedFile;
        createdAt: string;
        manufacturedWithin: string;
        imageField: BinaryStream;
        _attachments: ClientCollection<AttachmentAssociation>;
        qty: integer;
        netPrice: string;
    }
    export interface ShowCaseProduct$AsyncOperations {
        export: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
        testBulkMutation: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
        addPrefixToProductName: AsyncOperation<
            {
                filter?: string;
                prefix?: string;
            },
            boolean
        >;
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface ShowCaseProduct$Lookups {
        designerEmployee: QueryOperation<ShowCaseEmployee>;
        certificate: QueryOperation<UploadedFile>;
    }
    export interface ShowCaseProduct$Operations {
        query: QueryOperation<ShowCaseProduct>;
        read: ReadOperation<ShowCaseProduct>;
        aggregate: {
            read: AggregateReadOperation<ShowCaseProduct>;
            query: AggregateQueryOperation<ShowCaseProduct>;
        };
        create: CreateOperation<ShowCaseProductInput, ShowCaseProduct>;
        getDuplicate: GetDuplicateOperation<ShowCaseProduct>;
        duplicate: DuplicateOperation<string, ShowCaseProductInput, ShowCaseProduct>;
        update: UpdateOperation<ShowCaseProductInput, ShowCaseProduct>;
        updateById: UpdateByIdOperation<ShowCaseProductInput, ShowCaseProduct>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: ShowCaseProduct$AsyncOperations;
        lookups(dataOrId: string | { data: ShowCaseProductInput }): ShowCaseProduct$Lookups;
        getDefaults: GetDefaultsOperation<ShowCaseProduct>;
    }
    export interface ShowCaseProductOriginAddress extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        product: ShowCaseProduct;
        name: string;
        addressLine1: string;
        addressLine2: string;
        city: string;
        country: ShowCaseCountry;
    }
    export interface ShowCaseProductOriginAddressInput extends VitalClientNodeInput {
        name?: string;
        addressLine1?: string;
        addressLine2?: string;
        city?: string;
        country?: integer | string;
        zip?: string;
    }
    export interface ShowCaseProductOriginAddressBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        product: ShowCaseProduct;
        name: string;
        addressLine1: string;
        addressLine2: string;
        city: string;
        country: ShowCaseCountry;
        zip: string;
    }
    export interface ShowCaseProductOriginAddress$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface ShowCaseProductOriginAddress$Lookups {
        country: QueryOperation<ShowCaseCountry>;
    }
    export interface ShowCaseProductOriginAddress$Operations {
        query: QueryOperation<ShowCaseProductOriginAddress>;
        read: ReadOperation<ShowCaseProductOriginAddress>;
        aggregate: {
            read: AggregateReadOperation<ShowCaseProductOriginAddress>;
            query: AggregateQueryOperation<ShowCaseProductOriginAddress>;
        };
        create: CreateOperation<ShowCaseProductOriginAddressInput, ShowCaseProductOriginAddress>;
        getDuplicate: GetDuplicateOperation<ShowCaseProductOriginAddress>;
        duplicate: DuplicateOperation<string, ShowCaseProductOriginAddressInput, ShowCaseProductOriginAddress>;
        update: UpdateOperation<ShowCaseProductOriginAddressInput, ShowCaseProductOriginAddress>;
        updateById: UpdateByIdOperation<ShowCaseProductOriginAddressInput, ShowCaseProductOriginAddress>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: ShowCaseProductOriginAddress$AsyncOperations;
        lookups(dataOrId: string | { data: ShowCaseProductOriginAddressInput }): ShowCaseProductOriginAddress$Lookups;
        getDefaults: GetDefaultsOperation<ShowCaseProductOriginAddress>;
    }
    export interface ShowCaseProvider extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        textField: string;
        integerField: integer;
        decimalField: string;
        booleanField: boolean;
        dateField: string;
        products: ClientCollection<ShowCaseProduct>;
        document: TextStream;
        logo: BinaryStream;
        siteAddress: ShowCaseProviderAddress;
        concatenatedAddress: string;
        addresses: ShowCaseProviderAddress[];
        flagshipProduct: ShowCaseProduct;
        item: ShowCaseItem;
        minQuantity: integer;
        ratings: ShowCaseProviderRating[];
        additionalInfo: string;
    }
    export interface ShowCaseProviderInput extends ClientNodeInput {
        textField?: string;
        integerField?: integer | string;
        decimalField?: decimal | string;
        booleanField?: boolean | string;
        dateField?: string;
        products?: Partial<ShowCaseProductInput>[];
        document?: TextStream;
        logo?: BinaryStream;
        siteAddress?: integer | string;
        addresses?: (integer | string)[];
        flagshipProduct?: integer | string;
        item?: integer | string;
        minQuantity?: integer | string;
        ratings?: ShowCaseProviderRating[];
        additionalInfo?: string;
    }
    export interface ShowCaseProviderBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        textField: string;
        integerField: integer;
        decimalField: string;
        booleanField: boolean;
        dateField: string;
        products: ClientCollection<ShowCaseProductBinding>;
        document: TextStream;
        logo: BinaryStream;
        siteAddress: ShowCaseProviderAddress;
        concatenatedAddress: string;
        addresses: ShowCaseProviderAddress[];
        flagshipProduct: ShowCaseProduct;
        item: ShowCaseItem;
        minQuantity: integer;
        ratings: ShowCaseProviderRating[];
        additionalInfo: any;
    }
    export interface ShowCaseProvider$AsyncOperations {
        export: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface ShowCaseProvider$Lookups {
        siteAddress: QueryOperation<ShowCaseProviderAddress>;
        flagshipProduct: QueryOperation<ShowCaseProduct>;
        item: QueryOperation<ShowCaseItem>;
    }
    export interface ShowCaseProvider$Operations {
        query: QueryOperation<ShowCaseProvider>;
        read: ReadOperation<ShowCaseProvider>;
        aggregate: {
            read: AggregateReadOperation<ShowCaseProvider>;
            query: AggregateQueryOperation<ShowCaseProvider>;
        };
        create: CreateOperation<ShowCaseProviderInput, ShowCaseProvider>;
        getDuplicate: GetDuplicateOperation<ShowCaseProvider>;
        duplicate: DuplicateOperation<string, ShowCaseProviderInput, ShowCaseProvider>;
        update: UpdateOperation<ShowCaseProviderInput, ShowCaseProvider>;
        updateById: UpdateByIdOperation<ShowCaseProviderInput, ShowCaseProvider>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: ShowCaseProvider$AsyncOperations;
        lookups(dataOrId: string | { data: ShowCaseProviderInput }): ShowCaseProvider$Lookups;
        getDefaults: GetDefaultsOperation<ShowCaseProvider>;
    }
    export interface ShowCaseProviderAddress extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        name: string;
        addressLine1: string;
        addressLine2: string;
        city: string;
        country: ShowCaseCountry;
    }
    export interface ShowCaseProviderAddressInput extends ClientNodeInput {
        name?: string;
        addressLine1?: string;
        addressLine2?: string;
        city?: string;
        country?: integer | string;
        zip?: string;
    }
    export interface ShowCaseProviderAddressBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        name: string;
        addressLine1: string;
        addressLine2: string;
        city: string;
        country: ShowCaseCountry;
        zip: string;
    }
    export interface ShowCaseProviderAddress$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface ShowCaseProviderAddress$Lookups {
        country: QueryOperation<ShowCaseCountry>;
    }
    export interface ShowCaseProviderAddress$Operations {
        query: QueryOperation<ShowCaseProviderAddress>;
        read: ReadOperation<ShowCaseProviderAddress>;
        aggregate: {
            read: AggregateReadOperation<ShowCaseProviderAddress>;
            query: AggregateQueryOperation<ShowCaseProviderAddress>;
        };
        create: CreateOperation<ShowCaseProviderAddressInput, ShowCaseProviderAddress>;
        getDuplicate: GetDuplicateOperation<ShowCaseProviderAddress>;
        update: UpdateOperation<ShowCaseProviderAddressInput, ShowCaseProviderAddress>;
        updateById: UpdateByIdOperation<ShowCaseProviderAddressInput, ShowCaseProviderAddress>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: ShowCaseProviderAddress$AsyncOperations;
        lookups(dataOrId: string | { data: ShowCaseProviderAddressInput }): ShowCaseProviderAddress$Lookups;
        getDefaults: GetDefaultsOperation<ShowCaseProviderAddress>;
    }
    export interface ShowCaseTime extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        startTime: string;
        endTime: string;
    }
    export interface ShowCaseTimeInput extends ClientNodeInput {
        _vendor?: integer | string;
        startTime?: string;
        endTime?: string;
    }
    export interface ShowCaseTimeBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        startTime: string;
        endTime: string;
    }
    export interface ShowCaseTime$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface ShowCaseTime$Lookups {
        _vendor: QueryOperation<SysVendor>;
    }
    export interface ShowCaseTime$Operations {
        query: QueryOperation<ShowCaseTime>;
        read: ReadOperation<ShowCaseTime>;
        aggregate: {
            read: AggregateReadOperation<ShowCaseTime>;
            query: AggregateQueryOperation<ShowCaseTime>;
        };
        create: CreateOperation<ShowCaseTimeInput, ShowCaseTime>;
        getDuplicate: GetDuplicateOperation<ShowCaseTime>;
        update: UpdateOperation<ShowCaseTimeInput, ShowCaseTime>;
        updateById: UpdateByIdOperation<ShowCaseTimeInput, ShowCaseTime>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: ShowCaseTime$AsyncOperations;
        lookups(dataOrId: string | { data: ShowCaseTimeInput }): ShowCaseTime$Lookups;
        getDefaults: GetDefaultsOperation<ShowCaseTime>;
    }
    export interface TestAsyncMutationOnSpecificQueue extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        name: string;
    }
    export interface TestAsyncMutationOnSpecificQueueInput extends ClientNodeInput {
        name?: string;
    }
    export interface TestAsyncMutationOnSpecificQueueBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        name: string;
    }
    export interface TestAsyncMutationOnSpecificQueue$AsyncOperations {
        asyncMutationOnSpecificQueue: AsyncOperation<
            {
                val: integer | string;
            },
            integer
        >;
        asyncMutationToProcessExit: AsyncOperation<{}, string>;
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface TestAsyncMutationOnSpecificQueue$Operations {
        query: QueryOperation<TestAsyncMutationOnSpecificQueue>;
        read: ReadOperation<TestAsyncMutationOnSpecificQueue>;
        aggregate: {
            read: AggregateReadOperation<TestAsyncMutationOnSpecificQueue>;
            query: AggregateQueryOperation<TestAsyncMutationOnSpecificQueue>;
        };
        update: UpdateOperation<TestAsyncMutationOnSpecificQueueInput, TestAsyncMutationOnSpecificQueue>;
        updateById: UpdateByIdOperation<TestAsyncMutationOnSpecificQueueInput, TestAsyncMutationOnSpecificQueue>;
        asyncOperations: TestAsyncMutationOnSpecificQueue$AsyncOperations;
        getDefaults: GetDefaultsOperation<TestAsyncMutationOnSpecificQueue>;
    }
    export interface ShowCaseComponent extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        billOfMaterial: ShowCaseBillOfMaterial;
        componentNumber: integer;
        item: ShowCaseItem;
        bom: ShowCaseBillOfMaterial;
    }
    export interface ShowCaseComponentInput extends VitalClientNodeInput {
        componentNumber?: integer | string;
        item?: integer | string;
    }
    export interface ShowCaseComponentBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        billOfMaterial: ShowCaseBillOfMaterial;
        componentNumber: integer;
        item: ShowCaseItem;
        bom: ShowCaseBillOfMaterial;
    }
    export interface ShowCaseComponent$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface ShowCaseComponent$Lookups {
        item: QueryOperation<ShowCaseItem>;
        bom: QueryOperation<ShowCaseBillOfMaterial>;
    }
    export interface ShowCaseComponent$Operations {
        query: QueryOperation<ShowCaseComponent>;
        read: ReadOperation<ShowCaseComponent>;
        aggregate: {
            read: AggregateReadOperation<ShowCaseComponent>;
            query: AggregateQueryOperation<ShowCaseComponent>;
        };
        create: CreateOperation<ShowCaseComponentInput, ShowCaseComponent>;
        getDuplicate: GetDuplicateOperation<ShowCaseComponent>;
        update: UpdateOperation<ShowCaseComponentInput, ShowCaseComponent>;
        updateById: UpdateByIdOperation<ShowCaseComponentInput, ShowCaseComponent>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: ShowCaseComponent$AsyncOperations;
        lookups(dataOrId: string | { data: ShowCaseComponentInput }): ShowCaseComponent$Lookups;
        getDefaults: GetDefaultsOperation<ShowCaseComponent>;
    }
    export interface ShowCaseBillOfMaterial extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        item: ShowCaseItem;
        site: string;
        components: ClientCollection<ShowCaseComponent>;
    }
    export interface ShowCaseBillOfMaterialInput extends ClientNodeInput {
        item?: integer | string;
        site?: string;
        components?: Partial<ShowCaseComponentInput>[];
    }
    export interface ShowCaseBillOfMaterialBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        item: ShowCaseItem;
        site: string;
        components: ClientCollection<ShowCaseComponentBinding>;
    }
    export interface ShowCaseBillOfMaterial$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface ShowCaseBillOfMaterial$Lookups {
        item: QueryOperation<ShowCaseItem>;
    }
    export interface ShowCaseBillOfMaterial$Operations {
        query: QueryOperation<ShowCaseBillOfMaterial>;
        read: ReadOperation<ShowCaseBillOfMaterial>;
        aggregate: {
            read: AggregateReadOperation<ShowCaseBillOfMaterial>;
            query: AggregateQueryOperation<ShowCaseBillOfMaterial>;
        };
        create: CreateOperation<ShowCaseBillOfMaterialInput, ShowCaseBillOfMaterial>;
        getDuplicate: GetDuplicateOperation<ShowCaseBillOfMaterial>;
        update: UpdateOperation<ShowCaseBillOfMaterialInput, ShowCaseBillOfMaterial>;
        updateById: UpdateByIdOperation<ShowCaseBillOfMaterialInput, ShowCaseBillOfMaterial>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: ShowCaseBillOfMaterial$AsyncOperations;
        lookups(dataOrId: string | { data: ShowCaseBillOfMaterialInput }): ShowCaseBillOfMaterial$Lookups;
        getDefaults: GetDefaultsOperation<ShowCaseBillOfMaterial>;
    }
    export interface Package {
        '@sage/xtrem-show-case/ShowCaseCountry': ShowCaseCountry$Operations;
        '@sage/xtrem-show-case/ShowCaseCustomer': ShowCaseCustomer$Operations;
        '@sage/xtrem-show-case/ShowCaseEmployee': ShowCaseEmployee$Operations;
        '@sage/xtrem-show-case/ShowCaseInvoice': ShowCaseInvoice$Operations;
        '@sage/xtrem-show-case/ShowCaseInvoiceLine': ShowCaseInvoiceLine$Operations;
        '@sage/xtrem-show-case/ShowCaseItem': ShowCaseItem$Operations;
        '@sage/xtrem-show-case/ShowCaseNodeBrowserTree': ShowCaseNodeBrowserTree$Operations;
        '@sage/xtrem-show-case/ShowCaseOrder': ShowCaseOrder$Operations;
        '@sage/xtrem-show-case/ShowCaseProduct': ShowCaseProduct$Operations;
        '@sage/xtrem-show-case/ShowCaseProductOriginAddress': ShowCaseProductOriginAddress$Operations;
        '@sage/xtrem-show-case/ShowCaseProvider': ShowCaseProvider$Operations;
        '@sage/xtrem-show-case/ShowCaseProviderAddress': ShowCaseProviderAddress$Operations;
        '@sage/xtrem-show-case/ShowCaseTime': ShowCaseTime$Operations;
        '@sage/xtrem-show-case/TestAsyncMutationOnSpecificQueue': TestAsyncMutationOnSpecificQueue$Operations;
        '@sage/xtrem-show-case/ShowCaseComponent': ShowCaseComponent$Operations;
        '@sage/xtrem-show-case/ShowCaseBillOfMaterial': ShowCaseBillOfMaterial$Operations;
    }
    export interface GraphApi
        extends Package,
            SageXtremAuditing$Package,
            SageXtremAuthorization$Package,
            SageXtremCommunication$Package,
            SageXtremCustomization$Package,
            SageXtremDashboard$Package,
            SageXtremImportExport$Package,
            SageXtremInterop$Package,
            SageXtremMailer$Package,
            SageXtremMetadata$Package,
            SageXtremReporting$Package,
            SageXtremRouting$Package,
            SageXtremScheduler$Package,
            SageXtremSystem$Package,
            SageXtremUpload$Package,
            SageXtremWorkflow$Package {}
}
declare module '@sage/xtrem-show-case-api' {
    export type * from '@sage/xtrem-show-case-api-partial';
}
declare module '@sage/xtrem-auditing-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-show-case-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-authorization-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-show-case-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-communication-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-show-case-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-customization-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-show-case-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-dashboard-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-show-case-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-import-export-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-show-case-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-interop-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-show-case-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-mailer-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-show-case-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-metadata-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-show-case-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-reporting-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-show-case-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-routing-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-show-case-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-scheduler-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-show-case-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-system-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-show-case-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-upload-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-show-case-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-workflow-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-show-case-api';
    export interface GraphApi extends GraphApiExtension {}
}
