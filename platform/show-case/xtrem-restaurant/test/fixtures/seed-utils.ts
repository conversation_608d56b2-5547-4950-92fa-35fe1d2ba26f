import { faker } from '@faker-js/faker';
import {
    AddressInput,
    AllergenInput,
    CountryInput,
    CurrencyInput,
    IngredientInput,
    ItemIngredientInput,
    MenuItemInput,
    OrderInput,
    OrderItemInput,
    RestaurantInput,
    TableInput,
    UnitInput,
} from '@sage/xtrem-restaurant-api';
import * as _ from 'lodash';
import * as fs from 'node:fs';
import * as path from 'node:path';
import { memoizeUnique } from './memoize-unique';

const { kebabCase } = _;

// https://github.com/ryu1kn/csv-writer/issues/92
const csvWriter = require('csv-writer');

const maxRetries = 100000;
const idStore = {};
const unitStore = {};
const currencyStore = {};
const countryStore = {};

function getRandomId(): string {
    return memoizeUnique(faker.number.int, { store: idStore, maxRetries })({ min: 1, max: 100000 }).toString();
}

export function removeTestFolders() {
    const folders = ['allergen', 'currency', 'item', 'restaurant'];
    folders.forEach(folder => {
        const directory = path.join(__dirname, `../../data/layers/test/${folder}`);
        if (fs.existsSync(directory)) {
            fs.rmSync(directory, { recursive: true });
        }
        fs.mkdirSync(directory, { recursive: true });
    });
}
function getRandomColor() {
    const randomNumber = faker.number.float({ min: 0, max: 1 });
    return `#${Math.floor(randomNumber * 16777215).toString(16)}`;
}

export function getImageSvgContent(text: string): string {
    return `<svg xmlns="http://www.w3.org/2000/svg" version="1.1" baseProfile="full" width="640" height="480"><rect width="100%" height="100%" fill="${getRandomColor()}"/><text x="320" y="240" font-size="20" alignment-baseline="middle" text-anchor="middle" fill="white">${text}</text></svg>`;
}

export function createRandomCountry() {
    return {
        name: memoizeUnique(() => faker.location.country(), { store: countryStore, maxRetries })(),
        phoneCountryCode: Number(faker.location.countryCode({ variant: 'numeric' })),
    } as const satisfies CountryInput;
}

export function createRandomCurrency() {
    const { name, symbol } = memoizeUnique(() => faker.finance.currency(), { store: currencyStore, maxRetries })();
    const fileName = `icon--${kebabCase(name)}.svg`;
    const directory = path.join(__dirname, '../../data/layers/test/currency');
    const filePath = path.join(directory, fileName);
    fs.writeFileSync(filePath, getImageSvgContent(symbol || name));
    return {
        name,
        symbol,
        decimalDigits: faker.number.int({ min: 0, max: 5 }),
        icon: `file:${fileName}`,
    } as const satisfies Omit<CurrencyInput, 'icon'> & { icon: string };
}

export function createRandomUnit() {
    return {
        symbol: memoizeUnique(() => faker.helpers.arrayElement(['Kg', 'g', 'L']), { store: unitStore, maxRetries })(),
        description: faker.commerce.productDescription(),
        numberOfDecimals: faker.number.int({ min: 0, max: 5 }),
    } as const satisfies UnitInput;
}

export function createRandomAddress({
    country,
    restaurant,
}: {
    country: string;
    _sortValue: number;
    restaurant: string;
}) {
    return {
        name: faker.company.buzzPhrase(),
        line1: faker.location.streetAddress(),
        line2: faker.location.secondaryAddress(),
        city: faker.location.city(),
        country,
        restaurant,
        postCode: faker.location.zipCode(),
        // relations
    } as const satisfies AddressInput & { restaurant: string };
}

export function createRestaurant({ currency, name }: { currency: string; name: string }) {
    const fileName = `table-layout-chart--${kebabCase(name)}.json`;
    const directory = path.join(__dirname, '../../data/layers/test/restaurant');
    const hasLayoutChart = faker.datatype.boolean();
    if (hasLayoutChart) {
        const filePath = path.join(directory, fileName);
        fs.copyFileSync(
            path.join(__dirname, `table-layout-chart-${faker.number.int({ min: 1, max: 9 })}.json`),
            filePath,
        );
    }
    return {
        name,
        phoneNumber: faker.string.numeric({ length: 9 }),
        capacity: faker.number.int({ min: 15, max: 1000 }),
        currency,
        tableLayoutChart: hasLayoutChart ? `file:${fileName}` : '',
    } as const satisfies Omit<RestaurantInput, 'tableLayoutChart'> & { tableLayoutChart: string };
}

export function createIngredient({ unit, name }: { unit?: string; name: string }) {
    return {
        name,
        unit,
    } as const satisfies IngredientInput;
}

export function createRandomAllergen({ name }: { name: string }) {
    const fileName = `icon--${kebabCase(name)}.svg`;
    const directory = path.join(__dirname, '../../data/layers/test/allergen');
    const filePath = path.join(directory, fileName);
    fs.writeFileSync(filePath, getImageSvgContent(name));
    return {
        name,
        icon: `file:${fileName}`,
    } as const satisfies Omit<AllergenInput, 'icon'> & { icon: string };
}

export function createRandomItemIngredient({
    ingredient,
    item,
    quantity,
}: {
    ingredient: string;
    item: string;
    quantity: number | undefined;
}) {
    return {
        ingredient,
        item,
        quantity: quantity === undefined ? undefined : faker.number.int({ min: 1, max: 2000 }),
    } as const satisfies ItemIngredientInput & { ingredient: string };
}

export function createRandomMenuItem({
    item,
    restaurant,
    _sortValue,
}: {
    item: string;
    restaurant: string;
    _sortValue: number;
}) {
    return {
        restaurant,
        item,
        _sortValue,
        price: faker.number.float({ min: 3, max: 200, multipleOf: 0.25 }),
        isActive: faker.datatype.boolean(0.5),
    } as const satisfies MenuItemInput & { restaurant: string; _sortValue: number };
}

export function createRandomTable({
    restaurant,
    name,
    _sortValue,
}: {
    restaurant: string;
    name: string;
    _sortValue: number;
}) {
    return {
        name,
        restaurant,
        capacity: faker.number.int({ min: 2, max: 20 }),
        _sortValue,
    } as const satisfies TableInput & { restaurant: string; _sortValue: number };
}

export function createRandomOrder({ table, _sortValue }: { table: string; _sortValue: number }) {
    return {
        _id: getRandomId(),
        table,
        _sortValue,
    } as const satisfies OrderInput & { table: string; _sortValue: number };
}

export function createRandomOrderItem({
    menuItem,
    order,
    _sortValue,
}: {
    menuItem: string;
    order: string;
    _sortValue: number;
}) {
    return {
        order,
        menuItem,
        _sortValue,
        unitPrice: faker.number.float({ min: 3, max: 200, multipleOf: 0.25 }),
        quantity: faker.number.int({ min: 1, max: 20 }),
        notes: faker.lorem.sentences(2),
        status: faker.helpers.arrayElement(['new', 'preparing', 'delivered']),
    } as const satisfies OrderItemInput & { order: string; _sortValue: number };
}

export const COUNTRIES_LENGTH = 30;
export const CURRENCIES_LENGTH = COUNTRIES_LENGTH;
export const UNITS_LENGTH = 3;
export const RESTAURANTS_LENGTH = 50;
export const ADDRESSES_LENGTH = RESTAURANTS_LENGTH;
export const INGREDIENTS_LENGTH = 100;
export const ALLERGENS_LENGTH = 10;
export const MENU_ITEMS_LENGTH = 1000; // 50 items (see recipes.json) => 20 items/menu;
export const TABLES_LENGTH = RESTAURANTS_LENGTH * 14;
export const ORDERS_LENGTH = TABLES_LENGTH * 3;
export const ORDER_ITEM_LENGTH = ORDERS_LENGTH * 3; // 6300

export function getN<T>(n: number, mapfn: (v: unknown, i: number) => T) {
    return Array.from({ length: n }, mapfn);
}
const camelToSnakeCase = (str: string) => str.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);

export function writeCsv({ filePath, data }: { filePath: string; data: Record<string, any>[] }) {
    if (data.length < 1) {
        throw new Error('Empty data');
    }
    const columns = Object.keys(data[0]);
    const header = columns.map(c => {
        const snakeCased = camelToSnakeCase(c).replace(/[0-9]+/g, digits => `_${digits.toLowerCase()}`);
        return { id: c, title: snakeCased };
    });
    const writer = csvWriter.createObjectCsvWriter({
        path: filePath,
        header,
        fieldDelimiter: ';',
        alwaysQuote: true,
    });
    return writer.writeRecords(
        data.map(d =>
            columns.reduce<Record<string, any>>((acc, column) => {
                acc[column] = typeof d[column] === 'object' && 'value' in d[column] ? d[column].value : d[column];
                return acc;
            }, {}),
        ),
    );
}
