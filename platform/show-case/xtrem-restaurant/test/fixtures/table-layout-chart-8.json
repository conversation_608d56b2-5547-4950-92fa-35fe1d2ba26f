{"acts": {}, "layersArr": [{"xpropsArr": [{"xanchors": [{"type": null, "x": 11, "y": 12}, {"type": null, "x": 171, "y": 12}, {"type": null, "x": 171, "y": 92}, {"type": null, "x": 11, "y": 92}], "xtext": "<TEXTFORMAT><P>4</P></TEXTFORMAT>", "xdrawBehaviorCode": "K_API_RECT", "xstrokeProps": {"xtype": "solidstroke", "xthickness": 1, "xcolor": "#000000", "xalpha": 100, "xstart": {"xtype": "none"}, "xend": {"xtype": "none"}}, "xfillProps": {"xtype": "solidfill", "xcolor": "#FFFFFF", "xalpha": 100, "xgtype": "linear"}, "xshadowProps": {"xtype": "none"}, "xtextFormat": {}, "uniqueID": "65kx8nsi2inr2rq42f5"}, {"xanchors": [{"type": null, "x": 11, "y": 100}, {"type": null, "x": 171, "y": 100}, {"type": null, "x": 171, "y": 180}, {"type": null, "x": 11, "y": 180}], "xtext": "<TEXTFORMAT><P>4</P></TEXTFORMAT>", "xdrawBehaviorCode": "K_API_RECT", "xstrokeProps": {"xtype": "solidstroke", "xthickness": 1, "xcolor": "#000000", "xalpha": 100, "xstart": {"xtype": "none"}, "xend": {"xtype": "none"}}, "xfillProps": {"xtype": "solidfill", "xcolor": "#FFFFFF", "xalpha": 100, "xgtype": "linear"}, "xshadowProps": {"xtype": "none"}, "xcaptionPos": {"xleft": 11, "xtop": 100}, "xtextFormat": {}, "uniqueID": "ckfr9gsyake6p9oahmp"}, {"xanchors": [{"type": null, "x": 12, "y": 191}, {"type": null, "x": 172, "y": 191}, {"type": null, "x": 172, "y": 271}, {"type": null, "x": 12, "y": 271}], "xtext": "<TEXTFORMAT><P>4</P></TEXTFORMAT>", "xdrawBehaviorCode": "K_API_RECT", "xstrokeProps": {"xtype": "solidstroke", "xthickness": 1, "xcolor": "#000000", "xalpha": 100, "xstart": {"xtype": "none"}, "xend": {"xtype": "none"}}, "xfillProps": {"xtype": "solidfill", "xcolor": "#FFFFFF", "xalpha": 100, "xgtype": "linear"}, "xshadowProps": {"xtype": "none"}, "xtextFormat": {}, "uniqueID": "xg0242zmk0bsb5q0agz"}, {"xanchors": [{"type": null, "x": 12, "y": 280}, {"type": null, "x": 172, "y": 280}, {"type": null, "x": 172, "y": 360}, {"type": null, "x": 12, "y": 360}], "xtext": "<TEXTFORMAT><P>4</P></TEXTFORMAT>", "xdrawBehaviorCode": "K_API_RECT", "xstrokeProps": {"xtype": "solidstroke", "xthickness": 1, "xcolor": "#000000", "xalpha": 100, "xstart": {"xtype": "none"}, "xend": {"xtype": "none"}}, "xfillProps": {"xtype": "solidfill", "xcolor": "#FFFFFF", "xalpha": 100, "xgtype": "linear"}, "xshadowProps": {"xtype": "none"}, "xcaptionPos": {"xleft": 12, "xtop": 280}, "xtextFormat": {}, "uniqueID": "48on19pu3ikrjktuf1d"}, {"xanchors": [{"type": null, "x": 181, "y": 14}, {"type": null, "x": 341, "y": 14}, {"type": null, "x": 341, "y": 94}, {"type": null, "x": 181, "y": 94}], "xtext": "<TEXTFORMAT><P>4</P></TEXTFORMAT>", "xdrawBehaviorCode": "K_API_RECT", "xstrokeProps": {"xtype": "solidstroke", "xthickness": 1, "xcolor": "#000000", "xalpha": 100, "xstart": {"xtype": "none"}, "xend": {"xtype": "none"}}, "xfillProps": {"xtype": "solidfill", "xcolor": "#FFFFFF", "xalpha": 100, "xgtype": "linear"}, "xshadowProps": {"xtype": "none"}, "xtextFormat": {}, "uniqueID": "h57ikkcp4018z5puzrr"}, {"xanchors": [{"type": null, "x": 180, "y": 101}, {"type": null, "x": 340, "y": 101}, {"type": null, "x": 340, "y": 181}, {"type": null, "x": 180, "y": 181}], "xtext": "<TEXTFORMAT><P>4</P></TEXTFORMAT>", "xdrawBehaviorCode": "K_API_RECT", "xstrokeProps": {"xtype": "solidstroke", "xthickness": 1, "xcolor": "#000000", "xalpha": 100, "xstart": {"xtype": "none"}, "xend": {"xtype": "none"}}, "xfillProps": {"xtype": "solidfill", "xcolor": "#FFFFFF", "xalpha": 100, "xgtype": "linear"}, "xshadowProps": {"xtype": "none"}, "xtextFormat": {}, "uniqueID": "gasnup0wycgs9771wqf"}, {"xanchors": [{"type": null, "x": 180, "y": 193}, {"type": null, "x": 340, "y": 193}, {"type": null, "x": 340, "y": 273}, {"type": null, "x": 180, "y": 273}], "xtext": "<TEXTFORMAT><P>4</P></TEXTFORMAT>", "xdrawBehaviorCode": "K_API_RECT", "xstrokeProps": {"xtype": "solidstroke", "xthickness": 1, "xcolor": "#000000", "xalpha": 100, "xstart": {"xtype": "none"}, "xend": {"xtype": "none"}}, "xfillProps": {"xtype": "solidfill", "xcolor": "#FFFFFF", "xalpha": 100, "xgtype": "linear"}, "xshadowProps": {"xtype": "none"}, "xtextFormat": {}, "uniqueID": "c5wr0hzbo8qn50zlzz9"}, {"xanchors": [{"type": null, "x": 179, "y": 280}, {"type": null, "x": 339, "y": 280}, {"type": null, "x": 339, "y": 360}, {"type": null, "x": 179, "y": 360}], "xtext": "<TEXTFORMAT><P>4</P></TEXTFORMAT>", "xdrawBehaviorCode": "K_API_RECT", "xstrokeProps": {"xtype": "solidstroke", "xthickness": 1, "xcolor": "#000000", "xalpha": 100, "xstart": {"xtype": "none"}, "xend": {"xtype": "none"}}, "xfillProps": {"xtype": "solidfill", "xcolor": "#FFFFFF", "xalpha": 100, "xgtype": "linear"}, "xshadowProps": {"xtype": "none"}, "xtextFormat": {}, "uniqueID": "fi0siju6ym3g3lbnf6p"}, {"xanchors": [{"type": null, "x": 348, "y": 14}, {"type": null, "x": 508, "y": 14}, {"type": null, "x": 508, "y": 94}, {"type": null, "x": 348, "y": 94}], "xtext": "<TEXTFORMAT><P>4</P></TEXTFORMAT>", "xdrawBehaviorCode": "K_API_RECT", "xstrokeProps": {"xtype": "solidstroke", "xthickness": 1, "xcolor": "#000000", "xalpha": 100, "xstart": {"xtype": "none"}, "xend": {"xtype": "none"}}, "xfillProps": {"xtype": "solidfill", "xcolor": "#FFFFFF", "xalpha": 100, "xgtype": "linear"}, "xshadowProps": {"xtype": "none"}, "xtextFormat": {}, "uniqueID": "rmcggr3xz4btiz87h8v"}, {"xanchors": [{"type": null, "x": 348, "y": 100}, {"type": null, "x": 508, "y": 100}, {"type": null, "x": 508, "y": 180}, {"type": null, "x": 348, "y": 180}], "xtext": "<TEXTFORMAT><P>4</P></TEXTFORMAT>", "xdrawBehaviorCode": "K_API_RECT", "xstrokeProps": {"xtype": "solidstroke", "xthickness": 1, "xcolor": "#000000", "xalpha": 100, "xstart": {"xtype": "none"}, "xend": {"xtype": "none"}}, "xfillProps": {"xtype": "solidfill", "xcolor": "#FFFFFF", "xalpha": 100, "xgtype": "linear"}, "xshadowProps": {"xtype": "none"}, "xtextFormat": {}, "uniqueID": "wpvva5c4f4yvirv07p2"}, {"xanchors": [{"type": null, "x": 349, "y": 193}, {"type": null, "x": 509, "y": 193}, {"type": null, "x": 509, "y": 273}, {"type": null, "x": 349, "y": 273}], "xtext": "<TEXTFORMAT><P>4</P></TEXTFORMAT>", "xdrawBehaviorCode": "K_API_RECT", "xstrokeProps": {"xtype": "solidstroke", "xthickness": 1, "xcolor": "#000000", "xalpha": 100, "xstart": {"xtype": "none"}, "xend": {"xtype": "none"}}, "xfillProps": {"xtype": "solidfill", "xcolor": "#FFFFFF", "xalpha": 100, "xgtype": "linear"}, "xshadowProps": {"xtype": "none"}, "xtextFormat": {}, "uniqueID": "yom4c31o6mntimnl08t"}], "alpha": 100, "visible": true, "lock": false, "id": 0}], "reachedGroupNum": 27, "contentSize": {"xheight": 360, "xwidth": 509}, "docDims": {"xheight": 640, "xwidth": 860, "xtop": 0, "xleft": 0}, "currentLayerId": 1}