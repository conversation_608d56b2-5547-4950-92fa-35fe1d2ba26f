export enum Difficulty {
    Easy = 'Easy',
    Medium = 'Medium',
}

export interface Recipe {
    id: number;
    name: string;
    ingredients: string[];
    instructions: string[];
    prepTimeMinutes: number;
    cookTimeMinutes: number;
    servings: number;
    difficulty: Difficulty;
    cuisine: string;
    caloriesPerServing: number;
    tags: string[];
    userId: number;
    image: string;
    photo: string;
    rating: number;
    reviewCount: number;
    mealType: string[];
}

export interface RecipesCollection {
    recipes: Recipe[];
    total: number;
    skip: number;
    limit: number;
}
