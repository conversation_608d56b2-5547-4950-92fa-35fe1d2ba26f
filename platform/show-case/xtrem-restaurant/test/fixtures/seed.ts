import { faker } from '@faker-js/faker';
import { ItemCategory$Enum, ItemInput } from '@sage/xtrem-restaurant-api';
import * as fs from 'fs';
import { isEqual, snakeCase } from 'lodash';
import * as path from 'path';
import { Store, memoizeUnique } from './memoize-unique';
import { ALLERGENS, RESTAURANTS } from './seed-data';
import type { Recipe, RecipesCollection } from './seed-types';
import {
    ADDRESSES_LENGTH,
    COUNTRIES_LENGTH,
    CURRENCIES_LENGTH,
    MENU_ITEMS_LENGTH,
    ORDERS_LENGTH,
    ORDER_ITEM_LENGTH,
    RESTAURANTS_LENGTH,
    TABLES_LENGTH,
    UNITS_LENGTH,
    createIngredient,
    createRandomAddress,
    createRandomAllergen,
    createRandomCountry,
    createRandomCurrency,
    createRandomItemIngredient,
    createRandomMenuItem,
    createRandomOrder,
    createRandomOrderItem,
    createRandomTable,
    createRandomUnit,
    createRestaurant,
    getN,
    removeTestFolders,
    writeCsv,
} from './seed-utils';

class Seeder {
    #recipes: Recipe[] = [];

    #testDataPath: string = path.join(__dirname, '../../data/layers/test');

    #menuItemsStore: Store<number[]> = {};

    #orderItemsStore: Store<readonly [number, number]> = {};

    #countries: ReturnType<typeof createRandomCountry>[] = [];

    #currencies: ReturnType<typeof createRandomCurrency>[] = [];

    #units: ReturnType<typeof createRandomUnit>[] = [];

    #addresses: ReturnType<typeof createRandomAddress>[] = [];

    #items: (Omit<ItemInput, 'photo' | 'cookingInstructions'> & { photo: string; cookingInstructions: string })[] = [];

    #ingredients: ReturnType<typeof createIngredient>[] = [];

    #restaurants: ReturnType<typeof createRestaurant>[] = [];

    #allergens: ReturnType<typeof createRandomAllergen>[] = [];

    #allergenIngredients: { allergen: string; ingredient: string }[] = [];

    #tables: ReturnType<typeof createRandomTable>[] = [];

    #orders: (ReturnType<typeof createRandomOrder> & {
        status?: 'new' | 'preparing' | 'delivered';
        totalPrice?: number;
    })[] = [];

    #orderItems: ReturnType<typeof createRandomOrderItem>[] = [];

    #menuItems: ReturnType<typeof createRandomMenuItem>[] = [];

    #itemIngredients: ReturnType<typeof createRandomItemIngredient>[] = [];

    init(testDataPath?: string) {
        faker.seed(123);
        const fileContent = fs.readFileSync(path.join(__dirname, 'recipes.json'), 'utf-8');
        const db = JSON.parse(fileContent) as RecipesCollection;
        this.#recipes = db.recipes;
        if (testDataPath) {
            this.#testDataPath = testDataPath;
        }
    }

    #createCountries() {
        this.#countries = getN(COUNTRIES_LENGTH, createRandomCountry);
        writeCsv({ filePath: path.join(this.#testDataPath, 'country.csv'), data: this.#countries });
        return this;
    }

    #createCurrencies() {
        this.#currencies = getN(CURRENCIES_LENGTH, createRandomCurrency);
        writeCsv({ filePath: path.join(this.#testDataPath, 'currency.csv'), data: this.#currencies });
        return this;
    }

    #createUnits() {
        this.#units = getN(UNITS_LENGTH, createRandomUnit);
        writeCsv({ filePath: path.join(this.#testDataPath, 'unit.csv'), data: this.#units });
        return this;
    }

    #createAddresses() {
        this.#addresses = getN(ADDRESSES_LENGTH, (_, i) =>
            createRandomAddress({
                country: this.#countries[i % this.#countries.length].name,
                _sortValue: (i + 1) * 100,
                restaurant: this.#restaurants[i % this.#restaurants.length].name,
            }),
        );
        writeCsv({ filePath: path.join(this.#testDataPath, 'address.csv'), data: this.#addresses });
        return this;
    }

    #createTables() {
        this.#tables = getN(TABLES_LENGTH, (_, i) =>
            createRandomTable({
                restaurant: this.#restaurants[i % this.#restaurants.length].name,
                name: `Table ${Math.floor(i / this.#restaurants.length) + 1}`,
                _sortValue: (i + 1) * 100,
            }),
        );
        writeCsv({ filePath: path.join(this.#testDataPath, 'table.csv'), data: this.#tables });
        return this;
    }

    #createOrders() {
        this.#orders = getN(ORDERS_LENGTH, (_, i) =>
            createRandomOrder({
                table: `${this.#tables[i % this.#tables.length].name}|${this.#tables[i % this.#tables.length].restaurant}`,
                _sortValue: (i + 1) * 100,
            }),
        );
        // set order statuses before writing CSV file
        return this;
    }

    #createOrderItems() {
        this.#orderItems = getN(ORDER_ITEM_LENGTH, (_, i) => {
            const [menuItemIndex, orderIndex] = memoizeUnique(
                () => {
                    const orderIdx = faker.number.int({ min: 0, max: this.#orders.length - 1 });
                    const order = this.#orders[orderIdx]!;
                    const restaurantName = order.table.split('|')[1];
                    const menuItems = this.#menuItems.filter(menuItem => menuItem.restaurant === restaurantName);
                    const menuItem = faker.helpers.arrayElement(menuItems);
                    const menuItemIdx = this.#menuItems.findIndex(mItem => isEqual(mItem, menuItem));
                    return [menuItemIdx, orderIdx] as const;
                },
                { store: this.#orderItemsStore, maxRetries: 10000 },
            )();
            return createRandomOrderItem({
                menuItem: `${this.#menuItems[menuItemIndex].restaurant}|${this.#menuItems[menuItemIndex].item}`,
                order: this.#orders[orderIndex]._id!,
                _sortValue: (i + 1) * 100,
            });
        });
        writeCsv({ filePath: path.join(this.#testDataPath, 'order-item.csv'), data: this.#orderItems });
        return this;
    }

    #createItems() {
        this.#items = this.#recipes.map(({ name, prepTimeMinutes, mealType, instructions, id, ingredients, photo }) => {
            const photoFileName = `photo--${id}.webp`;
            const cookingInstructionsFileName = `cooking-instructions--${id}.txt`;
            const directory = path.join(__dirname, '../../data/layers/test/item');
            fs.writeFileSync(path.join(directory, photoFileName), photo.split('data:image/png;base64,')[1], {
                encoding: 'base64',
            });
            fs.writeFileSync(path.join(directory, cookingInstructionsFileName), instructions.join(' '));
            return {
                _id: String(id),
                price: String(faker.number.float({ min: 3, max: 200, multipleOf: 0.25 })),
                name,
                description: new Intl.ListFormat().format(ingredients),
                preparationTime: prepTimeMinutes,
                category: snakeCase(
                    mealType.find(t => !['Breakfast', 'Lunch', 'Dinner'].includes(t)) ?? (mealType[0] as any),
                ) as keyof ItemCategory$Enum,
                photo: `file:${photoFileName}`,
                cookingInstructions: `file:${cookingInstructionsFileName}`,
            };
        });
        writeCsv({ filePath: path.join(this.#testDataPath, 'item.csv'), data: this.#items });
        return this;
    }

    #createIngredients() {
        this.#ingredients = Array.from(new Set(this.#recipes.flatMap(r => r.ingredients))).map(i =>
            createIngredient({ name: i }),
        );
        writeCsv({ filePath: path.join(this.#testDataPath, 'ingredient.csv'), data: this.#ingredients });
        return this;
    }

    #createAllergens() {
        this.#allergens = ALLERGENS.map(a => createRandomAllergen({ name: a }));
        writeCsv({ filePath: path.join(this.#testDataPath, 'allergen.csv'), data: this.#allergens });
        return this;
    }

    #createIngredientAllergens() {
        this.#allergenIngredients = this.#ingredients.flatMap(ingredient => {
            const allergens = faker.helpers.arrayElements(this.#allergens, { min: 1, max: 5 });
            return allergens.map(allergen => {
                return {
                    ingredient: ingredient.name,
                    allergen: allergen.name,
                };
            });
        });
        writeCsv({
            filePath: path.join(this.#testDataPath, 'allergen-ingredient.csv'),
            data: this.#allergenIngredients,
        });

        return this;
    }

    #createRestaurants() {
        this.#restaurants = getN(RESTAURANTS_LENGTH, (_, i) =>
            createRestaurant({
                currency: this.#currencies[i % this.#currencies.length].name,
                name: RESTAURANTS[i],
            }),
        );
        writeCsv({ filePath: path.join(this.#testDataPath, 'restaurant.csv'), data: this.#restaurants });
        return this;
    }

    #createMenuItems() {
        this.#menuItems = getN(MENU_ITEMS_LENGTH, (_, i) => {
            const [itemIndex, restaurantIndex] = memoizeUnique(
                () => {
                    return [
                        faker.number.int({ min: 0, max: this.#items.length - 1 }),
                        faker.number.int({ min: 0, max: this.#restaurants.length - 1 }),
                    ];
                },
                { store: this.#menuItemsStore, maxRetries: 10000 },
            )();
            return createRandomMenuItem({
                item: this.#items[itemIndex]._id!,
                restaurant: this.#restaurants[restaurantIndex].name,
                _sortValue: (i + 1) * 100,
            });
        });
        writeCsv({ filePath: path.join(this.#testDataPath, 'menu-item.csv'), data: this.#menuItems });
        return this;
    }

    #createItemIngredients() {
        this.#itemIngredients = this.#recipes.flatMap(({ ingredients, name: recipeName }) =>
            ingredients.map(ingredient => {
                return createRandomItemIngredient({
                    ingredient: this.#ingredients.find(i => i.name === ingredient)?.name!,
                    item: this.#items.find(i => i.name === recipeName)?._id!,
                    quantity: undefined,
                });
            }),
        );
        writeCsv({ filePath: path.join(this.#testDataPath, 'item-ingredient.csv'), data: this.#itemIngredients });
        return this;
    }

    #updateOrderStatusesAndTotalPrice() {
        this.#orders = this.#orders.map(order => {
            const orderItems = this.#orderItems.filter(orderItem => orderItem.order === order._id);
            const totalPrice = orderItems.reduce(
                (acc, curr) => acc + Number(curr.quantity ?? 0) * Number(curr.unitPrice ?? 0),
                0,
            );
            order.totalPrice = totalPrice;
            if (orderItems.length === 0) {
                order.status = 'new';
            } else if (!orderItems.some(orderItem => orderItem.status !== 'delivered')) {
                order.status = 'delivered';
            } else {
                order.status = 'preparing';
            }
            return order;
        });
        writeCsv({ filePath: path.join(this.#testDataPath, 'order.csv'), data: this.#orders });
        return this;
    }

    #removeTestFolders() {
        removeTestFolders();
        return this;
    }

    build() {
        if (this.#recipes.length === 0) {
            this.init();
        }
        this.#removeTestFolders()
            .#createCountries()
            .#createCurrencies()
            .#createItems()
            .#createUnits()
            .#createRestaurants()
            .#createAddresses()
            .#createIngredients()
            .#createAllergens()
            .#createIngredientAllergens()
            .#createItemIngredients()
            .#createMenuItems()
            .#createTables()
            .#createOrders()
            .#createOrderItems()
            .#updateOrderStatusesAndTotalPrice();
    }
}

function main() {
    new Seeder().build();
}

main();
