// @ts-ignore
import { ItemInput } from '@sage/xtrem-restaurant-api';
import axios from 'axios';
import * as fs from 'fs/promises';
import * as path from 'path';
import { RecipesCollection } from './seed-types';

async function getBase64(url: string) {
    const response = await axios.get(url, {
        responseType: 'arraybuffer',
    });

    return Buffer.from(response.data, 'binary').toString('base64');
}

// Recipes are taken from https://dummyjson.com/docs/recipes
async function main() {
    const fileContent = await fs.readFile(path.join(__dirname, 'original-recipes.json'), 'utf-8');
    const db = JSON.parse(fileContent) as RecipesCollection;
    const items: (Omit<ItemInput, 'ingredients'> & { ingredients: string[] })[] = [];
    const recipesLength = db.recipes.length;
    // eslint-disable-next-line no-console
    console.log('Downloading images...');
    for (let i = 0; i < recipesLength; i += 1) {
        // eslint-disable-next-line no-console
        console.log(`${Math.floor((i / recipesLength) * 100)}%...`);
        const recipe = db.recipes[i];
        const photo = await getBase64(recipe.image);
        items.push({
            ...recipe,
            photo: { value: `data:image/png;base64,${photo}` },
        });
    }
    db.recipes = db.recipes.map((r, i) => ({ ...r, photo: items[i].photo?.value ?? '' }));
    await fs.writeFile(path.join(__dirname, 'recipes.json'), JSON.stringify(db, null, 2));
    // eslint-disable-next-line no-console
    console.log('Images downloaded.');
}

// eslint-disable-next-line no-void
void main();
