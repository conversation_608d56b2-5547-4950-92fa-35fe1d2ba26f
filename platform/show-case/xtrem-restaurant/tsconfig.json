{"extends": "../../tsconfig-base.json", "compilerOptions": {"outDir": "build", "rootDir": ".", "baseUrl": ".", "target": "es2022", "module": "CommonJS", "moduleResolution": "node"}, "include": ["index.ts", "lib/**/*", "api/api.d.ts", "test/**/*.ts", "test/**/*.json"], "exclude": [".eslintrc*.cjs", "lib/pages/**/*", "lib/widgets/**/*", "lib/page-extensions/**/*", "lib/page-fragments/**/*", "lib/stickers/**/*", "lib/i18n/**/*", "**/*.feature", "lib/client-functions/**/*"], "references": [{"path": "../../system/xtrem-authorization"}, {"path": "../../cli/xtrem-cli"}, {"path": "../../front-end/xtrem-client"}, {"path": "../../system/xtrem-communication"}, {"path": "../../back-end/xtrem-config"}, {"path": "../../back-end/xtrem-core"}, {"path": "../../system/xtrem-customization"}, {"path": "../../system/xtrem-dashboard"}, {"path": "../../shared/xtrem-date-time"}, {"path": "../../shared/xtrem-decimal"}, {"path": "../../shared/xtrem-i18n"}, {"path": "../../system/xtrem-import-export"}, {"path": "../../system/xtrem-interop"}, {"path": "../../system/xtrem-mailer"}, {"path": "../../system/xtrem-metadata"}, {"path": "../../system/xtrem-reporting"}, {"path": "../../system/xtrem-routing"}, {"path": "../../system/xtrem-scheduler"}, {"path": "../../back-end/xtrem-service"}, {"path": "../../shared/xtrem-shared"}, {"path": "../../front-end/xtrem-standalone"}, {"path": "../../system/xtrem-system"}, {"path": "../../front-end/xtrem-ui"}, {"path": "../../front-end/xtrem-ui-plugin-graphiql"}, {"path": "../../front-end/xtrem-ui-plugin-monaco"}, {"path": "../../front-end/xtrem-ui-plugin-pdf"}, {"path": "../../system/xtrem-upload"}, {"path": "../../back-end/eslint-plugin-xtrem"}, {"path": "../../system/xtrem-authorization/api"}, {"path": "../../system/xtrem-import-export/api"}, {"path": "api"}, {"path": "../../system/xtrem-system/api"}]}