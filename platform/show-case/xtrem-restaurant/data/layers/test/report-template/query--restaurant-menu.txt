query {
    foGfTsDi: xtremRestaurant {
        restaurant {
            query (filter: "{}", orderBy: "{}", first: 1) {
                edges {
                    node {
                        name
                        _id
                    }
                }
            }
        }
    }
    wvpYuDYO: xtremRestaurant {
        menuItem {
            query (filter: "{\"item\":{\"category\":{\"_and\":[{\"_in\":[\"salad\"]}]}},\"isActive\":{\"_and\":[{\"_eq\":true}]}}", orderBy: "{\"isActive\":1,\"item\":{\"name\":1,\"description\":1,\"category\":1,\"price\":1}}") {
                edges {
                    node {
                        _id
                        item {
                            name
                            description
                            price
                        }
                        restaurant {
                            currency {
                                symbol
                            }
                        }
                    }
                }
            }
        }
    }
    LXURkAvV: xtremRestaurant {
        menuItem {
            query (filter: "{\"item\":{\"category\":{\"_and\":[{\"_in\":[\"appetizer\"]}]}},\"isActive\":{\"_and\":[{\"_eq\":true}]}}", orderBy: "{\"isActive\":1,\"item\":{\"name\":1,\"description\":1,\"price\":1,\"category\":1}}") {
                edges {
                    node {
                        _id
                        item {
                            name
                            description
                            price
                        }
                        restaurant {
                            currency {
                                symbol
                            }
                        }
                    }
                }
            }
        }
    }
    FiiqAMRV: xtremRestaurant {
        menuItem {
            query (filter: "{\"item\":{\"category\":{\"_and\":[{\"_in\":[\"seafood\"]}]}},\"isActive\":{\"_and\":[{\"_eq\":true}]}}", orderBy: "{\"isActive\":1,\"item\":{\"name\":1,\"description\":1,\"price\":1,\"category\":1}}") {
                edges {
                    node {
                        _id
                        item {
                            name
                            description
                            price
                        }
                        restaurant {
                            currency {
                                symbol
                            }
                        }
                    }
                }
            }
        }
    }
    optKjPdm: xtremRestaurant {
        menuItem {
            query (filter: "{\"item\":{\"category\":{\"_and\":[{\"_in\":[\"steak\"]}]}},\"isActive\":{\"_and\":[{\"_eq\":true}]}}", orderBy: "{\"isActive\":1,\"item\":{\"name\":1,\"description\":1,\"price\":1,\"category\":1}}") {
                edges {
                    node {
                        _id
                        item {
                            name
                            description
                            price
                        }
                        restaurant {
                            currency {
                                symbol
                            }
                        }
                    }
                }
            }
        }
    }
    ufxYdmha: xtremRestaurant {
        menuItem {
            query (filter: "{\"isActive\":{\"_and\":[{\"_eq\":true}]},\"item\":{\"category\":{\"_and\":[{\"_in\":[\"vegetarian\"]}]}}}", orderBy: "{\"isActive\":1,\"item\":{\"name\":1,\"description\":1,\"category\":1,\"price\":1}}") {
                edges {
                    node {
                        _id
                        item {
                            name
                            description
                            price
                        }
                        restaurant {
                            currency {
                                symbol
                            }
                        }
                    }
                }
            }
        }
    }
    SDzRDaPt: xtremRestaurant {
        menuItem {
            query (filter: "{\"isActive\":{\"_and\":[{\"_eq\":true}]},\"item\":{\"category\":{\"_and\":[{\"_in\":[\"dessert\"]}]}}}", orderBy: "{\"isActive\":1,\"item\":{\"name\":1,\"description\":1,\"category\":1,\"price\":1}}") {
                edges {
                    node {
                        _id
                        item {
                            name
                            description
                            price
                        }
                        restaurant {
                            currency {
                                symbol
                            }
                        }
                    }
                }
            }
        }
    }
    begpEcqm: xtremRestaurant {
        menuItem {
            query (filter: "{\"isActive\":{\"_and\":[{\"_eq\":true}]},\"item\":{\"category\":{\"_and\":[{\"_in\":[\"dessert\"]}]}}}", orderBy: "{\"isActive\":1,\"item\":{\"name\":1,\"description\":1,\"price\":1,\"category\":1}}") {
                edges {
                    node {
                        _id
                        item {
                            name
                            description
                            price
                        }
                        restaurant {
                            currency {
                                symbol
                            }
                        }
                    }
                }
            }
        }
    }
    LIIIubJn: xtremRestaurant {
        menuItem {
            query (filter: "{\"isActive\":{\"_and\":[{\"_eq\":true}]},\"item\":{\"category\":{\"_and\":[{\"_in\":[\"coffesAndTeas\"]}]}}}", orderBy: "{\"isActive\":1,\"item\":{\"name\":1,\"description\":1,\"category\":1,\"price\":1}}") {
                edges {
                    node {
                        _id
                        item {
                            name
                            description
                            price
                        }
                        restaurant {
                            currency {
                                symbol
                            }
                        }
                    }
                }
            }
        }
    }
}