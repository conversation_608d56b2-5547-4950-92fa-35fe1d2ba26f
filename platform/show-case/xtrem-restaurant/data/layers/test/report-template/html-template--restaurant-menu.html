<p><strong>{{ translatedContent "b61541208db7fa7dba42c85224405911" }}</strong></p><section class="record-context" data-context-object-type="Restaurant" data-context-object-path="xtremRestaurant.restaurant.query.edges.0.node" data-context-filter="[]" data-context-list-order="{}" data-alias="foGfTsDi"><!--{{#with foGfTsDi.restaurant.query.edges.0.node}}--><div class="report-context-body"><p><i>{{ translatedContent "9819fce8eebe00198efa537109e99037" }} &nbsp;</i><span style="color:#198E59;"><strong><span class="property" data-property-display-label="Name" data-property-data-type="String" data-property-name="name" data-property-data-format="" data-property-parent-context="Restaurant">{{name}}</span></strong></span></p></div><!--{{/with}}--><span class="report-context-footer">&nbsp;</span></section><p><strong>{{ translatedContent "077f254caf8c0229a3d29bc994f4e713" }}</strong></p><table class="query-table" data-context-object-type="MenuItem" data-context-object-path="xtremRestaurant.menuItem.query.edges" data-context-filter="[{&quot;_id&quot;:&quot;1&quot;,&quot;label&quot;:&quot;Category&quot;,&quot;filterType&quot;:&quot;set&quot;,&quot;filterValue&quot;:[&quot;salad&quot;],&quot;data&quot;:{&quot;type&quot;:&quot;@sage/xtrem-restaurant/ItemCategory&quot;,&quot;kind&quot;:&quot;ENUM&quot;,&quot;enumValues&quot;:[&quot;starter&quot;,&quot;mainCourse&quot;,&quot;salad&quot;,&quot;appetizer&quot;,&quot;seafood&quot;,&quot;steak&quot;,&quot;vegetarian&quot;,&quot;sideDish&quot;,&quot;dessert&quot;,&quot;beverage&quot;,&quot;coffesAndTeas&quot;],&quot;isCollection&quot;:false,&quot;node&quot;:&quot;ItemCategory&quot;,&quot;name&quot;:&quot;category&quot;,&quot;canFilter&quot;:true,&quot;canSort&quot;:true,&quot;label&quot;:&quot;Category&quot;,&quot;isOnInputType&quot;:true,&quot;isOnOutputType&quot;:true,&quot;dataType&quot;:&quot;ItemCategoryDataType&quot;,&quot;targetNode&quot;:&quot;&quot;,&quot;enumType&quot;:&quot;@sage/xtrem-restaurant/ItemCategory&quot;,&quot;isCustom&quot;:false,&quot;isMutable&quot;:false,&quot;iconType&quot;:&quot;csv&quot;},&quot;id&quot;:&quot;item.category&quot;,&quot;labelPath&quot;:&quot;Item.Category&quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Category&quot;,&quot;data&quot;:{&quot;type&quot;:&quot;@sage/xtrem-restaurant/ItemCategory&quot;,&quot;kind&quot;:&quot;ENUM&quot;,&quot;enumValues&quot;:[&quot;starter&quot;,&quot;mainCourse&quot;,&quot;salad&quot;,&quot;appetizer&quot;,&quot;seafood&quot;,&quot;steak&quot;,&quot;vegetarian&quot;,&quot;sideDish&quot;,&quot;dessert&quot;,&quot;beverage&quot;,&quot;coffesAndTeas&quot;],&quot;isCollection&quot;:false,&quot;node&quot;:&quot;ItemCategory&quot;,&quot;name&quot;:&quot;category&quot;,&quot;canFilter&quot;:true,&quot;canSort&quot;:true,&quot;label&quot;:&quot;Category&quot;,&quot;isOnInputType&quot;:true,&quot;isOnOutputType&quot;:true,&quot;dataType&quot;:&quot;ItemCategoryDataType&quot;,&quot;targetNode&quot;:&quot;&quot;,&quot;enumType&quot;:&quot;@sage/xtrem-restaurant/ItemCategory&quot;,&quot;isCustom&quot;:false,&quot;isMutable&quot;:false,&quot;iconType&quot;:&quot;csv&quot;},&quot;id&quot;:&quot;item.category&quot;,&quot;key&quot;:&quot;item.category&quot;,&quot;labelKey&quot;:&quot;Item.Category&quot;,&quot;labelPath&quot;:&quot;Item.Category&quot;}},{&quot;_id&quot;:&quot;2&quot;,&quot;label&quot;:&quot;Is active&quot;,&quot;filterType&quot;:&quot;matches&quot;,&quot;filterValue&quot;:&quot;true&quot;,&quot;data&quot;:{&quot;type&quot;:&quot;Boolean&quot;,&quot;kind&quot;:&quot;SCALAR&quot;,&quot;isCollection&quot;:false,&quot;name&quot;:&quot;isActive&quot;,&quot;canFilter&quot;:true,&quot;canSort&quot;:true,&quot;label&quot;:&quot;Is active&quot;,&quot;isOnInputType&quot;:true,&quot;isOnOutputType&quot;:true,&quot;dataType&quot;:&quot;&quot;,&quot;targetNode&quot;:&quot;&quot;,&quot;enumType&quot;:null,&quot;isCustom&quot;:false,&quot;isMutable&quot;:false,&quot;iconType&quot;:&quot;csv&quot;},&quot;id&quot;:&quot;isActive&quot;,&quot;labelPath&quot;:&quot;Is active&quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Is active&quot;,&quot;data&quot;:{&quot;type&quot;:&quot;Boolean&quot;,&quot;kind&quot;:&quot;SCALAR&quot;,&quot;isCollection&quot;:false,&quot;name&quot;:&quot;isActive&quot;,&quot;canFilter&quot;:true,&quot;canSort&quot;:true,&quot;label&quot;:&quot;Is active&quot;,&quot;isOnInputType&quot;:true,&quot;isOnOutputType&quot;:true,&quot;dataType&quot;:&quot;&quot;,&quot;targetNode&quot;:&quot;&quot;,&quot;enumType&quot;:null,&quot;isCustom&quot;:false,&quot;isMutable&quot;:false,&quot;iconType&quot;:&quot;csv&quot;},&quot;id&quot;:&quot;isActive&quot;,&quot;key&quot;:&quot;isActive&quot;,&quot;labelKey&quot;:&quot;Is active&quot;,&quot;labelPath&quot;:&quot;Is active&quot;}}]" data-context-list-order="{&quot;isActive&quot;:&quot;ascending&quot;,&quot;item.name&quot;:&quot;ascending&quot;,&quot;item.description&quot;:&quot;ascending&quot;,&quot;item.category&quot;:&quot;ascending&quot;,&quot;item.price&quot;:&quot;ascending&quot;}" data-alias="wvpYuDYO"><thead class="query-table-head"><tr class="query-table-row"><td class="query-table-cell" style="border:1px solid transparent;padding:2px;"><p><i>{{ translatedContent "2434620737ad5ca502fad9b813430ee1" }}</i></p></td></tr></thead><tbody class="query-table-body"><!--{{#each wvpYuDYO.menuItem.query.edges}}{{#with node}}--><tr class="query-table-row"><td class="query-table-cell" style="border:1px solid transparent;padding:2px;"><figure class="table"><table><tbody><tr><td style="background-color:transparent;border-color:transparent;"><p><i><strong><span class="property" data-property-display-label="Name" data-property-data-type="String" data-property-name="item.name" data-property-data-format="" data-property-parent-context="MenuItem">{{item.name}}</span></strong></i></p><p><span class="property" data-property-display-label="Description" data-property-data-type="String" data-property-name="item.description" data-property-data-format="" data-property-parent-context="MenuItem">{{item.description}}</span></p></td><td style="background-color:transparent;border-color:transparent;text-align:right;"><i>{{ translatedContent "f2c12ae14393ffc83551d56f32bb03cc" }}&nbsp;</i><span class="property" data-property-display-label="Symbol" data-property-data-type="String" data-property-name="restaurant.currency.symbol" data-property-data-format="" data-property-parent-context="MenuItem">{{restaurant.currency.symbol}}</span><i><span class="property" data-property-display-label="Price" data-property-data-type="Decimal" data-property-name="item.price" data-property-data-format="" data-property-parent-context="MenuItem">{{item.price}}</span></i></td></tr></tbody></table></figure></td></tr><tr class="query-table-row" data-hidden="1"><td class="query-table-cell"><p>&nbsp;</p></td></tr><!--{{/with}}{{/each}}--><tr class="query-table-row" data-hidden="1"><td class="query-table-cell"><p>&nbsp;</p></td></tr></tbody><tfoot class="query-table-footer"><tr class="query-table-row"><td class="query-table-cell" style="border-color:transparent;"><p>&nbsp;</p></td></tr></tfoot></table><p>&nbsp;</p><table class="query-table" data-context-object-type="MenuItem" data-context-object-path="xtremRestaurant.menuItem.query.edges" data-context-filter="[{&quot;_id&quot;:&quot;1&quot;,&quot;label&quot;:&quot;Category&quot;,&quot;filterType&quot;:&quot;set&quot;,&quot;filterValue&quot;:[&quot;appetizer&quot;],&quot;data&quot;:{&quot;type&quot;:&quot;@sage/xtrem-restaurant/ItemCategory&quot;,&quot;kind&quot;:&quot;ENUM&quot;,&quot;enumValues&quot;:[&quot;starter&quot;,&quot;mainCourse&quot;,&quot;salad&quot;,&quot;appetizer&quot;,&quot;seafood&quot;,&quot;steak&quot;,&quot;vegetarian&quot;,&quot;sideDish&quot;,&quot;dessert&quot;,&quot;beverage&quot;,&quot;coffesAndTeas&quot;],&quot;isCollection&quot;:false,&quot;node&quot;:&quot;ItemCategory&quot;,&quot;name&quot;:&quot;category&quot;,&quot;canFilter&quot;:true,&quot;canSort&quot;:true,&quot;label&quot;:&quot;Category&quot;,&quot;isOnInputType&quot;:true,&quot;isOnOutputType&quot;:true,&quot;dataType&quot;:&quot;ItemCategoryDataType&quot;,&quot;targetNode&quot;:&quot;&quot;,&quot;enumType&quot;:&quot;@sage/xtrem-restaurant/ItemCategory&quot;,&quot;isCustom&quot;:false,&quot;isMutable&quot;:false,&quot;iconType&quot;:&quot;csv&quot;},&quot;id&quot;:&quot;item.category&quot;,&quot;labelPath&quot;:&quot;Item.Category&quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Category&quot;,&quot;data&quot;:{&quot;type&quot;:&quot;@sage/xtrem-restaurant/ItemCategory&quot;,&quot;kind&quot;:&quot;ENUM&quot;,&quot;enumValues&quot;:[&quot;starter&quot;,&quot;mainCourse&quot;,&quot;salad&quot;,&quot;appetizer&quot;,&quot;seafood&quot;,&quot;steak&quot;,&quot;vegetarian&quot;,&quot;sideDish&quot;,&quot;dessert&quot;,&quot;beverage&quot;,&quot;coffesAndTeas&quot;],&quot;isCollection&quot;:false,&quot;node&quot;:&quot;ItemCategory&quot;,&quot;name&quot;:&quot;category&quot;,&quot;canFilter&quot;:true,&quot;canSort&quot;:true,&quot;label&quot;:&quot;Category&quot;,&quot;isOnInputType&quot;:true,&quot;isOnOutputType&quot;:true,&quot;dataType&quot;:&quot;ItemCategoryDataType&quot;,&quot;targetNode&quot;:&quot;&quot;,&quot;enumType&quot;:&quot;@sage/xtrem-restaurant/ItemCategory&quot;,&quot;isCustom&quot;:false,&quot;isMutable&quot;:false,&quot;iconType&quot;:&quot;csv&quot;},&quot;id&quot;:&quot;item.category&quot;,&quot;key&quot;:&quot;item.category&quot;,&quot;labelKey&quot;:&quot;Item.Category&quot;,&quot;labelPath&quot;:&quot;Item.Category&quot;}},{&quot;_id&quot;:&quot;2&quot;,&quot;label&quot;:&quot;Is active&quot;,&quot;filterType&quot;:&quot;matches&quot;,&quot;filterValue&quot;:&quot;true&quot;,&quot;data&quot;:{&quot;type&quot;:&quot;Boolean&quot;,&quot;kind&quot;:&quot;SCALAR&quot;,&quot;isCollection&quot;:false,&quot;name&quot;:&quot;isActive&quot;,&quot;canFilter&quot;:true,&quot;canSort&quot;:true,&quot;label&quot;:&quot;Is active&quot;,&quot;isOnInputType&quot;:true,&quot;isOnOutputType&quot;:true,&quot;dataType&quot;:&quot;&quot;,&quot;targetNode&quot;:&quot;&quot;,&quot;enumType&quot;:null,&quot;isCustom&quot;:false,&quot;isMutable&quot;:false,&quot;iconType&quot;:&quot;csv&quot;},&quot;id&quot;:&quot;isActive&quot;,&quot;labelPath&quot;:&quot;Is active&quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Is active&quot;,&quot;data&quot;:{&quot;type&quot;:&quot;Boolean&quot;,&quot;kind&quot;:&quot;SCALAR&quot;,&quot;isCollection&quot;:false,&quot;name&quot;:&quot;isActive&quot;,&quot;canFilter&quot;:true,&quot;canSort&quot;:true,&quot;label&quot;:&quot;Is active&quot;,&quot;isOnInputType&quot;:true,&quot;isOnOutputType&quot;:true,&quot;dataType&quot;:&quot;&quot;,&quot;targetNode&quot;:&quot;&quot;,&quot;enumType&quot;:null,&quot;isCustom&quot;:false,&quot;isMutable&quot;:false,&quot;iconType&quot;:&quot;csv&quot;},&quot;id&quot;:&quot;isActive&quot;,&quot;key&quot;:&quot;isActive&quot;,&quot;labelKey&quot;:&quot;Is active&quot;,&quot;labelPath&quot;:&quot;Is active&quot;}}]" data-context-list-order="{&quot;isActive&quot;:&quot;ascending&quot;,&quot;item.name&quot;:&quot;ascending&quot;,&quot;item.description&quot;:&quot;ascending&quot;,&quot;item.price&quot;:&quot;ascending&quot;,&quot;item.category&quot;:&quot;ascending&quot;}" data-alias="LXURkAvV"><thead class="query-table-head"><tr class="query-table-row"><td class="query-table-cell" style="border:1px solid transparent;padding:2px;"><p><i>{{ translatedContent "c727d514dd93c1703c60806feafa4e51" }}</i></p></td></tr></thead><tbody class="query-table-body"><!--{{#each LXURkAvV.menuItem.query.edges}}{{#with node}}--><tr class="query-table-row"><td class="query-table-cell" style="border:1px solid transparent;padding:2px;"><figure class="table"><table><tbody><tr><td style="background-color:transparent;border-color:transparent;"><p><i><strong><span class="property" data-property-display-label="Name" data-property-data-type="String" data-property-name="item.name" data-property-data-format="" data-property-parent-context="MenuItem">{{item.name}}</span></strong></i></p><p><span class="property" data-property-display-label="Description" data-property-data-type="String" data-property-name="item.description" data-property-data-format="" data-property-parent-context="MenuItem">{{item.description}}</span></p></td><td style="background-color:transparent;border-color:transparent;text-align:right;"><i>{{ translatedContent "f2c12ae14393ffc83551d56f32bb03cc" }}&nbsp;</i><span class="property" data-property-display-label="Symbol" data-property-data-type="String" data-property-name="restaurant.currency.symbol" data-property-data-format="" data-property-parent-context="MenuItem">{{restaurant.currency.symbol}}</span><i><span class="property" data-property-display-label="Price" data-property-data-type="Decimal" data-property-name="item.price" data-property-data-format="" data-property-parent-context="MenuItem">{{item.price}}</span></i></td></tr></tbody></table></figure></td></tr><tr class="query-table-row" data-hidden="1"><td class="query-table-cell"><p>&nbsp;</p></td></tr><!--{{/with}}{{/each}}--><tr class="query-table-row" data-hidden="1"><td class="query-table-cell"><p>&nbsp;</p></td></tr></tbody><tfoot class="query-table-footer"><tr class="query-table-row"><td class="query-table-cell" style="border-color:transparent;"><p>&nbsp;</p></td></tr></tfoot></table><p>&nbsp;</p><p><strong>{{ translatedContent "bcda782ef929426f2859aa4724a3e9a8" }}</strong></p><table class="query-table" data-context-object-type="MenuItem" data-context-object-path="xtremRestaurant.menuItem.query.edges" data-context-filter="[{&quot;_id&quot;:&quot;1&quot;,&quot;label&quot;:&quot;Category&quot;,&quot;filterType&quot;:&quot;set&quot;,&quot;filterValue&quot;:[&quot;seafood&quot;],&quot;data&quot;:{&quot;type&quot;:&quot;@sage/xtrem-restaurant/ItemCategory&quot;,&quot;kind&quot;:&quot;ENUM&quot;,&quot;enumValues&quot;:[&quot;starter&quot;,&quot;mainCourse&quot;,&quot;salad&quot;,&quot;appetizer&quot;,&quot;seafood&quot;,&quot;steak&quot;,&quot;vegetarian&quot;,&quot;sideDish&quot;,&quot;dessert&quot;,&quot;beverage&quot;,&quot;coffesAndTeas&quot;],&quot;isCollection&quot;:false,&quot;node&quot;:&quot;ItemCategory&quot;,&quot;name&quot;:&quot;category&quot;,&quot;canFilter&quot;:true,&quot;canSort&quot;:true,&quot;label&quot;:&quot;Category&quot;,&quot;isOnInputType&quot;:true,&quot;isOnOutputType&quot;:true,&quot;dataType&quot;:&quot;ItemCategoryDataType&quot;,&quot;targetNode&quot;:&quot;&quot;,&quot;enumType&quot;:&quot;@sage/xtrem-restaurant/ItemCategory&quot;,&quot;isCustom&quot;:false,&quot;isMutable&quot;:false,&quot;iconType&quot;:&quot;csv&quot;},&quot;id&quot;:&quot;item.category&quot;,&quot;labelPath&quot;:&quot;Item.Category&quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Category&quot;,&quot;data&quot;:{&quot;type&quot;:&quot;@sage/xtrem-restaurant/ItemCategory&quot;,&quot;kind&quot;:&quot;ENUM&quot;,&quot;enumValues&quot;:[&quot;starter&quot;,&quot;mainCourse&quot;,&quot;salad&quot;,&quot;appetizer&quot;,&quot;seafood&quot;,&quot;steak&quot;,&quot;vegetarian&quot;,&quot;sideDish&quot;,&quot;dessert&quot;,&quot;beverage&quot;,&quot;coffesAndTeas&quot;],&quot;isCollection&quot;:false,&quot;node&quot;:&quot;ItemCategory&quot;,&quot;name&quot;:&quot;category&quot;,&quot;canFilter&quot;:true,&quot;canSort&quot;:true,&quot;label&quot;:&quot;Category&quot;,&quot;isOnInputType&quot;:true,&quot;isOnOutputType&quot;:true,&quot;dataType&quot;:&quot;ItemCategoryDataType&quot;,&quot;targetNode&quot;:&quot;&quot;,&quot;enumType&quot;:&quot;@sage/xtrem-restaurant/ItemCategory&quot;,&quot;isCustom&quot;:false,&quot;isMutable&quot;:false,&quot;iconType&quot;:&quot;csv&quot;},&quot;id&quot;:&quot;item.category&quot;,&quot;key&quot;:&quot;item.category&quot;,&quot;labelKey&quot;:&quot;Item.Category&quot;,&quot;labelPath&quot;:&quot;Item.Category&quot;}},{&quot;_id&quot;:&quot;2&quot;,&quot;label&quot;:&quot;Is active&quot;,&quot;filterType&quot;:&quot;matches&quot;,&quot;filterValue&quot;:&quot;true&quot;,&quot;data&quot;:{&quot;type&quot;:&quot;Boolean&quot;,&quot;kind&quot;:&quot;SCALAR&quot;,&quot;isCollection&quot;:false,&quot;name&quot;:&quot;isActive&quot;,&quot;canFilter&quot;:true,&quot;canSort&quot;:true,&quot;label&quot;:&quot;Is active&quot;,&quot;isOnInputType&quot;:true,&quot;isOnOutputType&quot;:true,&quot;dataType&quot;:&quot;&quot;,&quot;targetNode&quot;:&quot;&quot;,&quot;enumType&quot;:null,&quot;isCustom&quot;:false,&quot;isMutable&quot;:false,&quot;iconType&quot;:&quot;csv&quot;},&quot;id&quot;:&quot;isActive&quot;,&quot;labelPath&quot;:&quot;Is active&quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Is active&quot;,&quot;data&quot;:{&quot;type&quot;:&quot;Boolean&quot;,&quot;kind&quot;:&quot;SCALAR&quot;,&quot;isCollection&quot;:false,&quot;name&quot;:&quot;isActive&quot;,&quot;canFilter&quot;:true,&quot;canSort&quot;:true,&quot;label&quot;:&quot;Is active&quot;,&quot;isOnInputType&quot;:true,&quot;isOnOutputType&quot;:true,&quot;dataType&quot;:&quot;&quot;,&quot;targetNode&quot;:&quot;&quot;,&quot;enumType&quot;:null,&quot;isCustom&quot;:false,&quot;isMutable&quot;:false,&quot;iconType&quot;:&quot;csv&quot;},&quot;id&quot;:&quot;isActive&quot;,&quot;key&quot;:&quot;isActive&quot;,&quot;labelKey&quot;:&quot;Is active&quot;,&quot;labelPath&quot;:&quot;Is active&quot;}}]" data-context-list-order="{&quot;isActive&quot;:&quot;ascending&quot;,&quot;item.name&quot;:&quot;ascending&quot;,&quot;item.description&quot;:&quot;ascending&quot;,&quot;item.price&quot;:&quot;ascending&quot;,&quot;item.category&quot;:&quot;ascending&quot;}" data-alias="FiiqAMRV"><thead class="query-table-head"><tr class="query-table-row"><td class="query-table-cell" style="border:1px solid transparent;padding:2px;"><p><i>{{ translatedContent "dfc18591f5577a4ca6306f98cbb4090a" }}</i></p></td></tr></thead><tbody class="query-table-body"><!--{{#each FiiqAMRV.menuItem.query.edges}}{{#with node}}--><tr class="query-table-row"><td class="query-table-cell" style="border:1px solid transparent;padding:2px;"><figure class="table"><table><tbody><tr><td style="background-color:transparent;border-color:transparent;"><p><i><strong><span class="property" data-property-display-label="Name" data-property-data-type="String" data-property-name="item.name" data-property-data-format="" data-property-parent-context="MenuItem">{{item.name}}</span></strong></i></p><p><span class="property" data-property-display-label="Description" data-property-data-type="String" data-property-name="item.description" data-property-data-format="" data-property-parent-context="MenuItem">{{item.description}}</span></p></td><td style="background-color:transparent;border-color:transparent;text-align:right;"><i>{{ translatedContent "f2c12ae14393ffc83551d56f32bb03cc" }}&nbsp;</i><span class="property" data-property-display-label="Symbol" data-property-data-type="String" data-property-name="restaurant.currency.symbol" data-property-data-format="" data-property-parent-context="MenuItem">{{restaurant.currency.symbol}}</span><i><span class="property" data-property-display-label="Price" data-property-data-type="Decimal" data-property-name="item.price" data-property-data-format="" data-property-parent-context="MenuItem">{{item.price}}</span></i></td></tr></tbody></table></figure></td></tr><tr class="query-table-row" data-hidden="1"><td class="query-table-cell"><p>&nbsp;</p></td></tr><!--{{/with}}{{/each}}--><tr class="query-table-row" data-hidden="1"><td class="query-table-cell"><p>&nbsp;</p></td></tr></tbody><tfoot class="query-table-footer"><tr class="query-table-row"><td class="query-table-cell"><p>&nbsp;</p></td></tr></tfoot></table><p>&nbsp;</p><table class="query-table" data-context-object-type="MenuItem" data-context-object-path="xtremRestaurant.menuItem.query.edges" data-context-filter="[{&quot;_id&quot;:&quot;1&quot;,&quot;label&quot;:&quot;Category&quot;,&quot;filterType&quot;:&quot;set&quot;,&quot;filterValue&quot;:[&quot;steak&quot;],&quot;data&quot;:{&quot;type&quot;:&quot;@sage/xtrem-restaurant/ItemCategory&quot;,&quot;kind&quot;:&quot;ENUM&quot;,&quot;enumValues&quot;:[&quot;starter&quot;,&quot;mainCourse&quot;,&quot;salad&quot;,&quot;appetizer&quot;,&quot;seafood&quot;,&quot;steak&quot;,&quot;vegetarian&quot;,&quot;sideDish&quot;,&quot;dessert&quot;,&quot;beverage&quot;,&quot;coffesAndTeas&quot;],&quot;isCollection&quot;:false,&quot;node&quot;:&quot;ItemCategory&quot;,&quot;name&quot;:&quot;category&quot;,&quot;canFilter&quot;:true,&quot;canSort&quot;:true,&quot;label&quot;:&quot;Category&quot;,&quot;isOnInputType&quot;:true,&quot;isOnOutputType&quot;:true,&quot;dataType&quot;:&quot;ItemCategoryDataType&quot;,&quot;targetNode&quot;:&quot;&quot;,&quot;enumType&quot;:&quot;@sage/xtrem-restaurant/ItemCategory&quot;,&quot;isCustom&quot;:false,&quot;isMutable&quot;:false,&quot;iconType&quot;:&quot;csv&quot;},&quot;id&quot;:&quot;item.category&quot;,&quot;labelPath&quot;:&quot;Item.Category&quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Category&quot;,&quot;data&quot;:{&quot;type&quot;:&quot;@sage/xtrem-restaurant/ItemCategory&quot;,&quot;kind&quot;:&quot;ENUM&quot;,&quot;enumValues&quot;:[&quot;starter&quot;,&quot;mainCourse&quot;,&quot;salad&quot;,&quot;appetizer&quot;,&quot;seafood&quot;,&quot;steak&quot;,&quot;vegetarian&quot;,&quot;sideDish&quot;,&quot;dessert&quot;,&quot;beverage&quot;,&quot;coffesAndTeas&quot;],&quot;isCollection&quot;:false,&quot;node&quot;:&quot;ItemCategory&quot;,&quot;name&quot;:&quot;category&quot;,&quot;canFilter&quot;:true,&quot;canSort&quot;:true,&quot;label&quot;:&quot;Category&quot;,&quot;isOnInputType&quot;:true,&quot;isOnOutputType&quot;:true,&quot;dataType&quot;:&quot;ItemCategoryDataType&quot;,&quot;targetNode&quot;:&quot;&quot;,&quot;enumType&quot;:&quot;@sage/xtrem-restaurant/ItemCategory&quot;,&quot;isCustom&quot;:false,&quot;isMutable&quot;:false,&quot;iconType&quot;:&quot;csv&quot;},&quot;id&quot;:&quot;item.category&quot;,&quot;key&quot;:&quot;item.category&quot;,&quot;labelKey&quot;:&quot;Item.Category&quot;,&quot;labelPath&quot;:&quot;Item.Category&quot;}},{&quot;_id&quot;:&quot;2&quot;,&quot;label&quot;:&quot;Is active&quot;,&quot;filterType&quot;:&quot;matches&quot;,&quot;filterValue&quot;:&quot;true&quot;,&quot;data&quot;:{&quot;type&quot;:&quot;Boolean&quot;,&quot;kind&quot;:&quot;SCALAR&quot;,&quot;isCollection&quot;:false,&quot;name&quot;:&quot;isActive&quot;,&quot;canFilter&quot;:true,&quot;canSort&quot;:true,&quot;label&quot;:&quot;Is active&quot;,&quot;isOnInputType&quot;:true,&quot;isOnOutputType&quot;:true,&quot;dataType&quot;:&quot;&quot;,&quot;targetNode&quot;:&quot;&quot;,&quot;enumType&quot;:null,&quot;isCustom&quot;:false,&quot;isMutable&quot;:false,&quot;iconType&quot;:&quot;csv&quot;},&quot;id&quot;:&quot;isActive&quot;,&quot;labelPath&quot;:&quot;Is active&quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Is active&quot;,&quot;data&quot;:{&quot;type&quot;:&quot;Boolean&quot;,&quot;kind&quot;:&quot;SCALAR&quot;,&quot;isCollection&quot;:false,&quot;name&quot;:&quot;isActive&quot;,&quot;canFilter&quot;:true,&quot;canSort&quot;:true,&quot;label&quot;:&quot;Is active&quot;,&quot;isOnInputType&quot;:true,&quot;isOnOutputType&quot;:true,&quot;dataType&quot;:&quot;&quot;,&quot;targetNode&quot;:&quot;&quot;,&quot;enumType&quot;:null,&quot;isCustom&quot;:false,&quot;isMutable&quot;:false,&quot;iconType&quot;:&quot;csv&quot;},&quot;id&quot;:&quot;isActive&quot;,&quot;key&quot;:&quot;isActive&quot;,&quot;labelKey&quot;:&quot;Is active&quot;,&quot;labelPath&quot;:&quot;Is active&quot;}}]" data-context-list-order="{&quot;isActive&quot;:&quot;ascending&quot;,&quot;item.name&quot;:&quot;ascending&quot;,&quot;item.description&quot;:&quot;ascending&quot;,&quot;item.price&quot;:&quot;ascending&quot;,&quot;item.category&quot;:&quot;ascending&quot;}" data-alias="optKjPdm"><thead class="query-table-head"><tr class="query-table-row"><td class="query-table-cell" style="border:1px solid transparent;padding:2px;"><p><i>{{ translatedContent "c6f72a8d784afe3c83f91c326139c3bd" }}</i></p></td></tr></thead><tbody class="query-table-body"><!--{{#each optKjPdm.menuItem.query.edges}}{{#with node}}--><tr class="query-table-row"><td class="query-table-cell" style="border:1px solid transparent;padding:2px;"><figure class="table"><table><tbody><tr><td style="background-color:transparent;border-color:transparent;"><p><i><strong><span class="property" data-property-display-label="Name" data-property-data-type="String" data-property-name="item.name" data-property-data-format="" data-property-parent-context="MenuItem">{{item.name}}</span></strong></i></p><p><span class="property" data-property-display-label="Description" data-property-data-type="String" data-property-name="item.description" data-property-data-format="" data-property-parent-context="MenuItem">{{item.description}}</span></p></td><td style="background-color:transparent;border-color:transparent;text-align:right;"><i>{{ translatedContent "f2c12ae14393ffc83551d56f32bb03cc" }}&nbsp;</i><span class="property" data-property-display-label="Symbol" data-property-data-type="String" data-property-name="restaurant.currency.symbol" data-property-data-format="" data-property-parent-context="MenuItem">{{restaurant.currency.symbol}}</span><i><span class="property" data-property-display-label="Price" data-property-data-type="Decimal" data-property-name="item.price" data-property-data-format="" data-property-parent-context="MenuItem">{{item.price}}</span></i></td></tr></tbody></table></figure></td></tr><tr class="query-table-row" data-hidden="1"><td class="query-table-cell"><p>&nbsp;</p></td></tr><!--{{/with}}{{/each}}--><tr class="query-table-row" data-hidden="1"><td class="query-table-cell"><p>&nbsp;</p></td></tr></tbody><tfoot class="query-table-footer"><tr class="query-table-row"><td class="query-table-cell"><p>&nbsp;</p></td></tr></tfoot></table><p>&nbsp;</p><table class="query-table" data-context-object-type="MenuItem" data-context-object-path="xtremRestaurant.menuItem.query.edges" data-context-filter="[{&quot;_id&quot;:&quot;1&quot;,&quot;label&quot;:&quot;Is active&quot;,&quot;filterType&quot;:&quot;matches&quot;,&quot;filterValue&quot;:&quot;true&quot;,&quot;data&quot;:{&quot;type&quot;:&quot;Boolean&quot;,&quot;kind&quot;:&quot;SCALAR&quot;,&quot;isCollection&quot;:false,&quot;name&quot;:&quot;isActive&quot;,&quot;canFilter&quot;:true,&quot;canSort&quot;:true,&quot;label&quot;:&quot;Is active&quot;,&quot;isOnInputType&quot;:true,&quot;isOnOutputType&quot;:true,&quot;dataType&quot;:&quot;&quot;,&quot;targetNode&quot;:&quot;&quot;,&quot;enumType&quot;:null,&quot;isCustom&quot;:false,&quot;isMutable&quot;:false,&quot;iconType&quot;:&quot;csv&quot;},&quot;id&quot;:&quot;isActive&quot;,&quot;labelPath&quot;:&quot;Is active&quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Is active&quot;,&quot;data&quot;:{&quot;type&quot;:&quot;Boolean&quot;,&quot;kind&quot;:&quot;SCALAR&quot;,&quot;isCollection&quot;:false,&quot;name&quot;:&quot;isActive&quot;,&quot;canFilter&quot;:true,&quot;canSort&quot;:true,&quot;label&quot;:&quot;Is active&quot;,&quot;isOnInputType&quot;:true,&quot;isOnOutputType&quot;:true,&quot;dataType&quot;:&quot;&quot;,&quot;targetNode&quot;:&quot;&quot;,&quot;enumType&quot;:null,&quot;isCustom&quot;:false,&quot;isMutable&quot;:false,&quot;iconType&quot;:&quot;csv&quot;},&quot;id&quot;:&quot;isActive&quot;,&quot;key&quot;:&quot;isActive&quot;,&quot;labelKey&quot;:&quot;Is active&quot;,&quot;labelPath&quot;:&quot;Is active&quot;}},{&quot;_id&quot;:&quot;2&quot;,&quot;label&quot;:&quot;Category&quot;,&quot;filterType&quot;:&quot;set&quot;,&quot;filterValue&quot;:[&quot;vegetarian&quot;],&quot;data&quot;:{&quot;type&quot;:&quot;@sage/xtrem-restaurant/ItemCategory&quot;,&quot;kind&quot;:&quot;ENUM&quot;,&quot;enumValues&quot;:[&quot;starter&quot;,&quot;mainCourse&quot;,&quot;salad&quot;,&quot;appetizer&quot;,&quot;seafood&quot;,&quot;steak&quot;,&quot;vegetarian&quot;,&quot;sideDish&quot;,&quot;dessert&quot;,&quot;beverage&quot;,&quot;coffesAndTeas&quot;],&quot;isCollection&quot;:false,&quot;node&quot;:&quot;ItemCategory&quot;,&quot;name&quot;:&quot;category&quot;,&quot;canFilter&quot;:true,&quot;canSort&quot;:true,&quot;label&quot;:&quot;Category&quot;,&quot;isOnInputType&quot;:true,&quot;isOnOutputType&quot;:true,&quot;dataType&quot;:&quot;ItemCategoryDataType&quot;,&quot;targetNode&quot;:&quot;&quot;,&quot;enumType&quot;:&quot;@sage/xtrem-restaurant/ItemCategory&quot;,&quot;isCustom&quot;:false,&quot;isMutable&quot;:false,&quot;iconType&quot;:&quot;csv&quot;},&quot;id&quot;:&quot;item.category&quot;,&quot;labelPath&quot;:&quot;Item.Category&quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Category&quot;,&quot;data&quot;:{&quot;type&quot;:&quot;@sage/xtrem-restaurant/ItemCategory&quot;,&quot;kind&quot;:&quot;ENUM&quot;,&quot;enumValues&quot;:[&quot;starter&quot;,&quot;mainCourse&quot;,&quot;salad&quot;,&quot;appetizer&quot;,&quot;seafood&quot;,&quot;steak&quot;,&quot;vegetarian&quot;,&quot;sideDish&quot;,&quot;dessert&quot;,&quot;beverage&quot;,&quot;coffesAndTeas&quot;],&quot;isCollection&quot;:false,&quot;node&quot;:&quot;ItemCategory&quot;,&quot;name&quot;:&quot;category&quot;,&quot;canFilter&quot;:true,&quot;canSort&quot;:true,&quot;label&quot;:&quot;Category&quot;,&quot;isOnInputType&quot;:true,&quot;isOnOutputType&quot;:true,&quot;dataType&quot;:&quot;ItemCategoryDataType&quot;,&quot;targetNode&quot;:&quot;&quot;,&quot;enumType&quot;:&quot;@sage/xtrem-restaurant/ItemCategory&quot;,&quot;isCustom&quot;:false,&quot;isMutable&quot;:false,&quot;iconType&quot;:&quot;csv&quot;},&quot;id&quot;:&quot;item.category&quot;,&quot;key&quot;:&quot;item.category&quot;,&quot;labelKey&quot;:&quot;Item.Category&quot;,&quot;labelPath&quot;:&quot;Item.Category&quot;}}]" data-context-list-order="{&quot;isActive&quot;:&quot;ascending&quot;,&quot;item.name&quot;:&quot;ascending&quot;,&quot;item.description&quot;:&quot;ascending&quot;,&quot;item.category&quot;:&quot;ascending&quot;,&quot;item.price&quot;:&quot;ascending&quot;}" data-alias="ufxYdmha"><thead class="query-table-head"><tr class="query-table-row"><td class="query-table-cell" style="border:1px solid transparent;padding:2px;"><p><i>{{ translatedContent "fec3fc458d3f87ca09a4187589d0abfa" }}</i></p></td></tr></thead><tbody class="query-table-body"><!--{{#each ufxYdmha.menuItem.query.edges}}{{#with node}}--><tr class="query-table-row"><td class="query-table-cell" style="border:1px solid transparent;padding:2px;"><figure class="table"><table><tbody><tr><td style="background-color:transparent;border-color:transparent;"><p><i><strong><span class="property" data-property-display-label="Name" data-property-data-type="String" data-property-name="item.name" data-property-data-format="" data-property-parent-context="MenuItem">{{item.name}}</span></strong></i></p><p><span class="property" data-property-display-label="Description" data-property-data-type="String" data-property-name="item.description" data-property-data-format="" data-property-parent-context="MenuItem">{{item.description}}</span></p></td><td style="background-color:transparent;border-color:transparent;text-align:right;"><i>{{ translatedContent "f2c12ae14393ffc83551d56f32bb03cc" }}&nbsp;</i><span class="property" data-property-display-label="Symbol" data-property-data-type="String" data-property-name="restaurant.currency.symbol" data-property-data-format="" data-property-parent-context="MenuItem">{{restaurant.currency.symbol}}</span><i><span class="property" data-property-display-label="Price" data-property-data-type="Decimal" data-property-name="item.price" data-property-data-format="" data-property-parent-context="MenuItem">{{item.price}}</span></i></td></tr></tbody></table></figure></td></tr><tr class="query-table-row" data-hidden="1"><td class="query-table-cell"><p>&nbsp;</p></td></tr><!--{{/with}}{{/each}}--><tr class="query-table-row" data-hidden="1"><td class="query-table-cell"><p>&nbsp;</p></td></tr></tbody><tfoot class="query-table-footer"><tr class="query-table-row"><td class="query-table-cell"><p>&nbsp;</p></td></tr></tfoot></table><p>&nbsp;</p><table class="query-table" data-context-object-type="MenuItem" data-context-object-path="xtremRestaurant.menuItem.query.edges" data-context-filter="[{&quot;_id&quot;:&quot;1&quot;,&quot;label&quot;:&quot;Is active&quot;,&quot;filterType&quot;:&quot;matches&quot;,&quot;filterValue&quot;:&quot;true&quot;,&quot;data&quot;:{&quot;type&quot;:&quot;Boolean&quot;,&quot;kind&quot;:&quot;SCALAR&quot;,&quot;isCollection&quot;:false,&quot;name&quot;:&quot;isActive&quot;,&quot;canFilter&quot;:true,&quot;canSort&quot;:true,&quot;label&quot;:&quot;Is active&quot;,&quot;isOnInputType&quot;:true,&quot;isOnOutputType&quot;:true,&quot;dataType&quot;:&quot;&quot;,&quot;targetNode&quot;:&quot;&quot;,&quot;enumType&quot;:null,&quot;isCustom&quot;:false,&quot;isMutable&quot;:false,&quot;iconType&quot;:&quot;csv&quot;},&quot;id&quot;:&quot;isActive&quot;,&quot;labelPath&quot;:&quot;Is active&quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Is active&quot;,&quot;data&quot;:{&quot;type&quot;:&quot;Boolean&quot;,&quot;kind&quot;:&quot;SCALAR&quot;,&quot;isCollection&quot;:false,&quot;name&quot;:&quot;isActive&quot;,&quot;canFilter&quot;:true,&quot;canSort&quot;:true,&quot;label&quot;:&quot;Is active&quot;,&quot;isOnInputType&quot;:true,&quot;isOnOutputType&quot;:true,&quot;dataType&quot;:&quot;&quot;,&quot;targetNode&quot;:&quot;&quot;,&quot;enumType&quot;:null,&quot;isCustom&quot;:false,&quot;isMutable&quot;:false,&quot;iconType&quot;:&quot;csv&quot;},&quot;id&quot;:&quot;isActive&quot;,&quot;key&quot;:&quot;isActive&quot;,&quot;labelKey&quot;:&quot;Is active&quot;,&quot;labelPath&quot;:&quot;Is active&quot;}},{&quot;_id&quot;:&quot;2&quot;,&quot;label&quot;:&quot;Category&quot;,&quot;filterType&quot;:&quot;set&quot;,&quot;filterValue&quot;:[&quot;dessert&quot;],&quot;data&quot;:{&quot;type&quot;:&quot;@sage/xtrem-restaurant/ItemCategory&quot;,&quot;kind&quot;:&quot;ENUM&quot;,&quot;enumValues&quot;:[&quot;starter&quot;,&quot;mainCourse&quot;,&quot;salad&quot;,&quot;appetizer&quot;,&quot;seafood&quot;,&quot;steak&quot;,&quot;vegetarian&quot;,&quot;sideDish&quot;,&quot;dessert&quot;,&quot;beverage&quot;,&quot;coffesAndTeas&quot;],&quot;isCollection&quot;:false,&quot;node&quot;:&quot;ItemCategory&quot;,&quot;name&quot;:&quot;category&quot;,&quot;canFilter&quot;:true,&quot;canSort&quot;:true,&quot;label&quot;:&quot;Category&quot;,&quot;isOnInputType&quot;:true,&quot;isOnOutputType&quot;:true,&quot;dataType&quot;:&quot;ItemCategoryDataType&quot;,&quot;targetNode&quot;:&quot;&quot;,&quot;enumType&quot;:&quot;@sage/xtrem-restaurant/ItemCategory&quot;,&quot;isCustom&quot;:false,&quot;isMutable&quot;:false,&quot;iconType&quot;:&quot;csv&quot;},&quot;id&quot;:&quot;item.category&quot;,&quot;labelPath&quot;:&quot;Item.Category&quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Category&quot;,&quot;data&quot;:{&quot;type&quot;:&quot;@sage/xtrem-restaurant/ItemCategory&quot;,&quot;kind&quot;:&quot;ENUM&quot;,&quot;enumValues&quot;:[&quot;starter&quot;,&quot;mainCourse&quot;,&quot;salad&quot;,&quot;appetizer&quot;,&quot;seafood&quot;,&quot;steak&quot;,&quot;vegetarian&quot;,&quot;sideDish&quot;,&quot;dessert&quot;,&quot;beverage&quot;,&quot;coffesAndTeas&quot;],&quot;isCollection&quot;:false,&quot;node&quot;:&quot;ItemCategory&quot;,&quot;name&quot;:&quot;category&quot;,&quot;canFilter&quot;:true,&quot;canSort&quot;:true,&quot;label&quot;:&quot;Category&quot;,&quot;isOnInputType&quot;:true,&quot;isOnOutputType&quot;:true,&quot;dataType&quot;:&quot;ItemCategoryDataType&quot;,&quot;targetNode&quot;:&quot;&quot;,&quot;enumType&quot;:&quot;@sage/xtrem-restaurant/ItemCategory&quot;,&quot;isCustom&quot;:false,&quot;isMutable&quot;:false,&quot;iconType&quot;:&quot;csv&quot;},&quot;id&quot;:&quot;item.category&quot;,&quot;key&quot;:&quot;item.category&quot;,&quot;labelKey&quot;:&quot;Item.Category&quot;,&quot;labelPath&quot;:&quot;Item.Category&quot;}}]" data-context-list-order="{&quot;isActive&quot;:&quot;ascending&quot;,&quot;item.name&quot;:&quot;ascending&quot;,&quot;item.description&quot;:&quot;ascending&quot;,&quot;item.category&quot;:&quot;ascending&quot;,&quot;item.price&quot;:&quot;ascending&quot;}" data-alias="SDzRDaPt"><thead class="query-table-head"><tr class="query-table-row"><td class="query-table-cell" style="border:1px solid transparent;padding:2px;"><p><strong>{{ translatedContent "66ea83dd5375f75118335d2b82e2fb29" }}</strong></p></td></tr></thead><tbody class="query-table-body"><!--{{#each SDzRDaPt.menuItem.query.edges}}{{#with node}}--><tr class="query-table-row"><td class="query-table-cell" style="border:1px solid transparent;padding:2px;"><figure class="table"><table><tbody><tr><td style="background-color:transparent;border-color:transparent;"><p><i><strong><span class="property" data-property-display-label="Name" data-property-data-type="String" data-property-name="item.name" data-property-data-format="" data-property-parent-context="MenuItem">{{item.name}}</span></strong></i></p><p><span class="property" data-property-display-label="Description" data-property-data-type="String" data-property-name="item.description" data-property-data-format="" data-property-parent-context="MenuItem">{{item.description}}</span></p></td><td style="background-color:transparent;border-color:transparent;text-align:right;"><i>{{ translatedContent "f2c12ae14393ffc83551d56f32bb03cc" }}&nbsp;</i><span class="property" data-property-display-label="Symbol" data-property-data-type="String" data-property-name="restaurant.currency.symbol" data-property-data-format="" data-property-parent-context="MenuItem">{{restaurant.currency.symbol}}</span><i><span class="property" data-property-display-label="Price" data-property-data-type="Decimal" data-property-name="item.price" data-property-data-format="" data-property-parent-context="MenuItem">{{item.price}}</span></i></td></tr></tbody></table></figure></td></tr><tr class="query-table-row" data-hidden="1"><td class="query-table-cell"><p>&nbsp;</p></td></tr><!--{{/with}}{{/each}}--><tr class="query-table-row" data-hidden="1"><td class="query-table-cell"><p>&nbsp;</p></td></tr></tbody><tfoot class="query-table-footer"><tr class="query-table-row"><td class="query-table-cell"><p>&nbsp;</p></td></tr></tfoot></table><p>&nbsp;</p><table class="query-table" data-context-object-type="MenuItem" data-context-object-path="xtremRestaurant.menuItem.query.edges" data-context-filter="[{&quot;_id&quot;:&quot;1&quot;,&quot;label&quot;:&quot;Is active&quot;,&quot;filterType&quot;:&quot;matches&quot;,&quot;filterValue&quot;:&quot;true&quot;,&quot;data&quot;:{&quot;type&quot;:&quot;Boolean&quot;,&quot;kind&quot;:&quot;SCALAR&quot;,&quot;isCollection&quot;:false,&quot;name&quot;:&quot;isActive&quot;,&quot;canFilter&quot;:true,&quot;canSort&quot;:true,&quot;label&quot;:&quot;Is active&quot;,&quot;isOnInputType&quot;:true,&quot;isOnOutputType&quot;:true,&quot;dataType&quot;:&quot;&quot;,&quot;targetNode&quot;:&quot;&quot;,&quot;enumType&quot;:null,&quot;isCustom&quot;:false,&quot;isMutable&quot;:false,&quot;iconType&quot;:&quot;csv&quot;},&quot;id&quot;:&quot;isActive&quot;,&quot;labelPath&quot;:&quot;Is active&quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Is active&quot;,&quot;data&quot;:{&quot;type&quot;:&quot;Boolean&quot;,&quot;kind&quot;:&quot;SCALAR&quot;,&quot;isCollection&quot;:false,&quot;name&quot;:&quot;isActive&quot;,&quot;canFilter&quot;:true,&quot;canSort&quot;:true,&quot;label&quot;:&quot;Is active&quot;,&quot;isOnInputType&quot;:true,&quot;isOnOutputType&quot;:true,&quot;dataType&quot;:&quot;&quot;,&quot;targetNode&quot;:&quot;&quot;,&quot;enumType&quot;:null,&quot;isCustom&quot;:false,&quot;isMutable&quot;:false,&quot;iconType&quot;:&quot;csv&quot;},&quot;id&quot;:&quot;isActive&quot;,&quot;key&quot;:&quot;isActive&quot;,&quot;labelKey&quot;:&quot;Is active&quot;,&quot;labelPath&quot;:&quot;Is active&quot;}},{&quot;_id&quot;:&quot;2&quot;,&quot;label&quot;:&quot;Category&quot;,&quot;filterType&quot;:&quot;set&quot;,&quot;filterValue&quot;:[&quot;dessert&quot;],&quot;data&quot;:{&quot;type&quot;:&quot;@sage/xtrem-restaurant/ItemCategory&quot;,&quot;kind&quot;:&quot;ENUM&quot;,&quot;enumValues&quot;:[&quot;starter&quot;,&quot;mainCourse&quot;,&quot;salad&quot;,&quot;appetizer&quot;,&quot;seafood&quot;,&quot;steak&quot;,&quot;vegetarian&quot;,&quot;sideDish&quot;,&quot;dessert&quot;,&quot;beverage&quot;,&quot;coffesAndTeas&quot;],&quot;isCollection&quot;:false,&quot;node&quot;:&quot;ItemCategory&quot;,&quot;name&quot;:&quot;category&quot;,&quot;canFilter&quot;:true,&quot;canSort&quot;:true,&quot;label&quot;:&quot;Category&quot;,&quot;isOnInputType&quot;:true,&quot;isOnOutputType&quot;:true,&quot;dataType&quot;:&quot;ItemCategoryDataType&quot;,&quot;targetNode&quot;:&quot;&quot;,&quot;enumType&quot;:&quot;@sage/xtrem-restaurant/ItemCategory&quot;,&quot;isCustom&quot;:false,&quot;isMutable&quot;:false,&quot;iconType&quot;:&quot;csv&quot;},&quot;id&quot;:&quot;item.category&quot;,&quot;labelPath&quot;:&quot;Item.Category&quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Category&quot;,&quot;data&quot;:{&quot;type&quot;:&quot;@sage/xtrem-restaurant/ItemCategory&quot;,&quot;kind&quot;:&quot;ENUM&quot;,&quot;enumValues&quot;:[&quot;starter&quot;,&quot;mainCourse&quot;,&quot;salad&quot;,&quot;appetizer&quot;,&quot;seafood&quot;,&quot;steak&quot;,&quot;vegetarian&quot;,&quot;sideDish&quot;,&quot;dessert&quot;,&quot;beverage&quot;,&quot;coffesAndTeas&quot;],&quot;isCollection&quot;:false,&quot;node&quot;:&quot;ItemCategory&quot;,&quot;name&quot;:&quot;category&quot;,&quot;canFilter&quot;:true,&quot;canSort&quot;:true,&quot;label&quot;:&quot;Category&quot;,&quot;isOnInputType&quot;:true,&quot;isOnOutputType&quot;:true,&quot;dataType&quot;:&quot;ItemCategoryDataType&quot;,&quot;targetNode&quot;:&quot;&quot;,&quot;enumType&quot;:&quot;@sage/xtrem-restaurant/ItemCategory&quot;,&quot;isCustom&quot;:false,&quot;isMutable&quot;:false,&quot;iconType&quot;:&quot;csv&quot;},&quot;id&quot;:&quot;item.category&quot;,&quot;key&quot;:&quot;item.category&quot;,&quot;labelKey&quot;:&quot;Item.Category&quot;,&quot;labelPath&quot;:&quot;Item.Category&quot;}}]" data-context-list-order="{&quot;isActive&quot;:&quot;ascending&quot;,&quot;item.name&quot;:&quot;ascending&quot;,&quot;item.description&quot;:&quot;ascending&quot;,&quot;item.price&quot;:&quot;ascending&quot;,&quot;item.category&quot;:&quot;ascending&quot;}" data-alias="begpEcqm"><thead class="query-table-head"><tr class="query-table-row"><td class="query-table-cell" style="border:1px solid transparent;padding:2px;"><p><strong>{{ translatedContent "e0e8b1de3583536cb5f8e70daf86144f" }}</strong></p></td></tr></thead><tbody class="query-table-body"><!--{{#each begpEcqm.menuItem.query.edges}}{{#with node}}--><tr class="query-table-row"><td class="query-table-cell" style="border:1px solid transparent;padding:2px;"><figure class="table"><table><tbody><tr><td style="background-color:transparent;border-color:transparent;"><p><i><strong><span class="property" data-property-display-label="Name" data-property-data-type="String" data-property-name="item.name" data-property-data-format="" data-property-parent-context="MenuItem">{{item.name}}</span></strong></i></p><p><span class="property" data-property-display-label="Description" data-property-data-type="String" data-property-name="item.description" data-property-data-format="" data-property-parent-context="MenuItem">{{item.description}}</span></p></td><td style="background-color:transparent;border-color:transparent;text-align:right;"><i>{{ translatedContent "f2c12ae14393ffc83551d56f32bb03cc" }}&nbsp;</i><span class="property" data-property-display-label="Symbol" data-property-data-type="String" data-property-name="restaurant.currency.symbol" data-property-data-format="" data-property-parent-context="MenuItem">{{restaurant.currency.symbol}}</span><i><span class="property" data-property-display-label="Price" data-property-data-type="Decimal" data-property-name="item.price" data-property-data-format="" data-property-parent-context="MenuItem">{{item.price}}</span></i></td></tr></tbody></table></figure></td></tr><tr class="query-table-row" data-hidden="1"><td class="query-table-cell"><p>&nbsp;</p></td></tr><!--{{/with}}{{/each}}--><tr class="query-table-row" data-hidden="1"><td class="query-table-cell"><p>&nbsp;</p></td></tr></tbody><tfoot class="query-table-footer"><tr class="query-table-row"><td class="query-table-cell"><p>&nbsp;</p></td></tr></tfoot></table><p>&nbsp;</p><table class="query-table" data-context-object-type="MenuItem" data-context-object-path="xtremRestaurant.menuItem.query.edges" data-context-filter="[{&quot;_id&quot;:&quot;1&quot;,&quot;label&quot;:&quot;Is active&quot;,&quot;filterType&quot;:&quot;matches&quot;,&quot;filterValue&quot;:&quot;true&quot;,&quot;data&quot;:{&quot;type&quot;:&quot;Boolean&quot;,&quot;kind&quot;:&quot;SCALAR&quot;,&quot;isCollection&quot;:false,&quot;name&quot;:&quot;isActive&quot;,&quot;canFilter&quot;:true,&quot;canSort&quot;:true,&quot;label&quot;:&quot;Is active&quot;,&quot;isOnInputType&quot;:true,&quot;isOnOutputType&quot;:true,&quot;dataType&quot;:&quot;&quot;,&quot;targetNode&quot;:&quot;&quot;,&quot;enumType&quot;:null,&quot;isCustom&quot;:false,&quot;isMutable&quot;:false,&quot;iconType&quot;:&quot;csv&quot;},&quot;id&quot;:&quot;isActive&quot;,&quot;labelPath&quot;:&quot;Is active&quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Is active&quot;,&quot;data&quot;:{&quot;type&quot;:&quot;Boolean&quot;,&quot;kind&quot;:&quot;SCALAR&quot;,&quot;isCollection&quot;:false,&quot;name&quot;:&quot;isActive&quot;,&quot;canFilter&quot;:true,&quot;canSort&quot;:true,&quot;label&quot;:&quot;Is active&quot;,&quot;isOnInputType&quot;:true,&quot;isOnOutputType&quot;:true,&quot;dataType&quot;:&quot;&quot;,&quot;targetNode&quot;:&quot;&quot;,&quot;enumType&quot;:null,&quot;isCustom&quot;:false,&quot;isMutable&quot;:false,&quot;iconType&quot;:&quot;csv&quot;},&quot;id&quot;:&quot;isActive&quot;,&quot;key&quot;:&quot;isActive&quot;,&quot;labelKey&quot;:&quot;Is active&quot;,&quot;labelPath&quot;:&quot;Is active&quot;}},{&quot;_id&quot;:&quot;2&quot;,&quot;label&quot;:&quot;Category&quot;,&quot;filterType&quot;:&quot;set&quot;,&quot;filterValue&quot;:[&quot;coffesAndTeas&quot;],&quot;data&quot;:{&quot;type&quot;:&quot;@sage/xtrem-restaurant/ItemCategory&quot;,&quot;kind&quot;:&quot;ENUM&quot;,&quot;enumValues&quot;:[&quot;starter&quot;,&quot;mainCourse&quot;,&quot;salad&quot;,&quot;appetizer&quot;,&quot;seafood&quot;,&quot;steak&quot;,&quot;vegetarian&quot;,&quot;sideDish&quot;,&quot;dessert&quot;,&quot;beverage&quot;,&quot;coffesAndTeas&quot;],&quot;isCollection&quot;:false,&quot;node&quot;:&quot;ItemCategory&quot;,&quot;name&quot;:&quot;category&quot;,&quot;canFilter&quot;:true,&quot;canSort&quot;:true,&quot;label&quot;:&quot;Category&quot;,&quot;isOnInputType&quot;:true,&quot;isOnOutputType&quot;:true,&quot;dataType&quot;:&quot;ItemCategoryDataType&quot;,&quot;targetNode&quot;:&quot;&quot;,&quot;enumType&quot;:&quot;@sage/xtrem-restaurant/ItemCategory&quot;,&quot;isCustom&quot;:false,&quot;isMutable&quot;:false,&quot;iconType&quot;:&quot;csv&quot;},&quot;id&quot;:&quot;item.category&quot;,&quot;labelPath&quot;:&quot;Item.Category&quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Category&quot;,&quot;data&quot;:{&quot;type&quot;:&quot;@sage/xtrem-restaurant/ItemCategory&quot;,&quot;kind&quot;:&quot;ENUM&quot;,&quot;enumValues&quot;:[&quot;starter&quot;,&quot;mainCourse&quot;,&quot;salad&quot;,&quot;appetizer&quot;,&quot;seafood&quot;,&quot;steak&quot;,&quot;vegetarian&quot;,&quot;sideDish&quot;,&quot;dessert&quot;,&quot;beverage&quot;,&quot;coffesAndTeas&quot;],&quot;isCollection&quot;:false,&quot;node&quot;:&quot;ItemCategory&quot;,&quot;name&quot;:&quot;category&quot;,&quot;canFilter&quot;:true,&quot;canSort&quot;:true,&quot;label&quot;:&quot;Category&quot;,&quot;isOnInputType&quot;:true,&quot;isOnOutputType&quot;:true,&quot;dataType&quot;:&quot;ItemCategoryDataType&quot;,&quot;targetNode&quot;:&quot;&quot;,&quot;enumType&quot;:&quot;@sage/xtrem-restaurant/ItemCategory&quot;,&quot;isCustom&quot;:false,&quot;isMutable&quot;:false,&quot;iconType&quot;:&quot;csv&quot;},&quot;id&quot;:&quot;item.category&quot;,&quot;key&quot;:&quot;item.category&quot;,&quot;labelKey&quot;:&quot;Item.Category&quot;,&quot;labelPath&quot;:&quot;Item.Category&quot;}}]" data-context-list-order="{&quot;isActive&quot;:&quot;ascending&quot;,&quot;item.name&quot;:&quot;ascending&quot;,&quot;item.description&quot;:&quot;ascending&quot;,&quot;item.category&quot;:&quot;ascending&quot;,&quot;item.price&quot;:&quot;ascending&quot;}" data-alias="LIIIubJn"><thead class="query-table-head"><tr class="query-table-row"><td class="query-table-cell" style="border:1px solid transparent;padding:2px;"><p><strong>{{ translatedContent "131b5f719ee7e34a94b293724591ae27" }}</strong></p></td></tr></thead><tbody class="query-table-body"><!--{{#each LIIIubJn.menuItem.query.edges}}{{#with node}}--><tr class="query-table-row"><td class="query-table-cell" style="border:1px solid transparent;padding:2px;"><figure class="table"><table><tbody><tr><td style="background-color:transparent;border-color:transparent;"><p><i><strong><span class="property" data-property-display-label="Name" data-property-data-type="String" data-property-name="item.name" data-property-data-format="" data-property-parent-context="MenuItem">{{item.name}}</span></strong></i></p><p><span class="property" data-property-display-label="Description" data-property-data-type="String" data-property-name="item.description" data-property-data-format="" data-property-parent-context="MenuItem">{{item.description}}</span></p></td><td style="background-color:transparent;border-color:transparent;text-align:right;"><i>{{ translatedContent "f2c12ae14393ffc83551d56f32bb03cc" }}&nbsp;</i><span class="property" data-property-display-label="Symbol" data-property-data-type="String" data-property-name="restaurant.currency.symbol" data-property-data-format="" data-property-parent-context="MenuItem">{{restaurant.currency.symbol}}</span><i><span class="property" data-property-display-label="Price" data-property-data-type="Decimal" data-property-name="item.price" data-property-data-format="" data-property-parent-context="MenuItem">{{item.price}}</span></i></td></tr></tbody></table></figure></td></tr><tr class="query-table-row" data-hidden="1"><td class="query-table-cell"><p>&nbsp;</p></td></tr><!--{{/with}}{{/each}}--><tr class="query-table-row" data-hidden="1"><td class="query-table-cell"><p>&nbsp;</p></td></tr></tbody><tfoot class="query-table-footer"><tr class="query-table-row"><td class="query-table-cell"><p>&nbsp;</p></td></tr></tfoot></table><p>&nbsp;</p>