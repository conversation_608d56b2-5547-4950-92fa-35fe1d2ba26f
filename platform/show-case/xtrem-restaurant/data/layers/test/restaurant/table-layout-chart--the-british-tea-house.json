{"acts": {}, "layersArr": [{"xpropsArr": [{"xanchors": [{"type": null, "x": 13.999999999999984, "y": 16.999999999999975}, {"type": null, "x": 83.99999999999999, "y": 16.999999999999975}, {"type": null, "x": 83.99999999999999, "y": 96.99999999999993}, {"type": null, "x": 13.999999999999984, "y": 96.99999999999993}], "xtext": "<TEXTFORMAT><P>2</P></TEXTFORMAT>", "xdrawBehaviorCode": "K_API_RECT", "xstrokeProps": {"xtype": "solidstroke", "xthickness": 1, "xcolor": "#000000", "xalpha": 100, "xstart": {"xtype": "none"}, "xend": {"xtype": "none"}}, "xfillProps": {"xtype": "solidfill", "xcolor": "#FFFFFF", "xalpha": 100, "xgtype": "linear"}, "xshadowProps": {"xtype": "none"}, "xcaptionPos": {"xleft": 13.999999999999984, "xtop": 16.999999999999975}, "xtextFormat": {}, "uniqueID": "8z051756t65tsm1dtsf"}, {"xanchors": [{"type": null, "x": 95.9999999999999, "y": 16.99999999999997}, {"type": null, "x": 165, "y": 16.99999999999997}, {"type": null, "x": 165, "y": 96.99999999999993}, {"type": null, "x": 95.9999999999999, "y": 96.99999999999993}], "xtext": "<TEXTFORMAT><P>2</P></TEXTFORMAT>", "xdrawBehaviorCode": "K_API_RECT", "xstrokeProps": {"xtype": "solidstroke", "xthickness": 1, "xcolor": "#000000", "xalpha": 100, "xstart": {"xtype": "none"}, "xend": {"xtype": "none"}}, "xfillProps": {"xtype": "solidfill", "xcolor": "#FFFFFF", "xalpha": 100, "xgtype": "linear"}, "xshadowProps": {"xtype": "none"}, "xcaptionPos": {"xleft": 95.9999999999999, "xtop": 16.99999999999997}, "xtextFormat": {}, "uniqueID": "lme0ybziqii2249q2rs"}, {"xanchors": [{"type": null, "x": 13.999999999999996, "y": 105.00000000000011}, {"type": null, "x": 83, "y": 105.00000000000011}, {"type": null, "x": 83, "y": 185.0000000000001}, {"type": null, "x": 13.999999999999996, "y": 185.0000000000001}], "xtext": "<TEXTFORMAT><P>2</P></TEXTFORMAT>", "xdrawBehaviorCode": "K_API_RECT", "xstrokeProps": {"xtype": "solidstroke", "xthickness": 1, "xcolor": "#000000", "xalpha": 100, "xstart": {"xtype": "none"}, "xend": {"xtype": "none"}}, "xfillProps": {"xtype": "solidfill", "xcolor": "#FFFFFF", "xalpha": 100, "xgtype": "linear"}, "xshadowProps": {"xtype": "none"}, "xcaptionPos": {"xleft": 13.999999999999996, "xtop": 105.00000000000011}, "xtextFormat": {}, "uniqueID": "j1l1f9sn1zg620e8yow"}, {"xanchors": [{"type": null, "x": 94.99999999999994, "y": 105.00000000000011}, {"type": null, "x": 164, "y": 105.00000000000011}, {"type": null, "x": 164, "y": 184.99999999999997}, {"type": null, "x": 94.99999999999994, "y": 184.99999999999997}], "xtext": "<TEXTFORMAT><P>2</P></TEXTFORMAT>", "xdrawBehaviorCode": "K_API_RECT", "xstrokeProps": {"xtype": "solidstroke", "xthickness": 1, "xcolor": "#000000", "xalpha": 100, "xstart": {"xtype": "none"}, "xend": {"xtype": "none"}}, "xfillProps": {"xtype": "solidfill", "xcolor": "#FFFFFF", "xalpha": 100, "xgtype": "linear"}, "xshadowProps": {"xtype": "none"}, "xcaptionPos": {"xleft": 94.99999999999994, "xtop": 105.00000000000011}, "xtextFormat": {}, "uniqueID": "n1b3kzqubydryvo8cw1"}, {"xanchors": [{"type": null, "x": 13.999999999999998, "y": 191.9999999999998}, {"type": null, "x": 84, "y": 191.9999999999998}, {"type": null, "x": 84, "y": 271.99999999999966}, {"type": null, "x": 13.999999999999998, "y": 271.99999999999966}], "xtext": "<TEXTFORMAT><P>2</P></TEXTFORMAT>", "xdrawBehaviorCode": "K_API_RECT", "xstrokeProps": {"xtype": "solidstroke", "xthickness": 1, "xcolor": "#000000", "xalpha": 100, "xstart": {"xtype": "none"}, "xend": {"xtype": "none"}}, "xfillProps": {"xtype": "solidfill", "xcolor": "#FFFFFF", "xalpha": 100, "xgtype": "linear"}, "xshadowProps": {"xtype": "none"}, "xcaptionPos": {"xleft": 13.999999999999998, "xtop": 191.9999999999998}, "xtextFormat": {}, "uniqueID": "3t8xvx9f4mn5txtl3qk"}, {"xanchors": [{"type": null, "x": 93.99999999999996, "y": 193.00000000000003}, {"type": null, "x": 165.99999999999994, "y": 193.00000000000003}, {"type": null, "x": 165.99999999999994, "y": 273}, {"type": null, "x": 93.99999999999996, "y": 273}], "xtext": "<TEXTFORMAT><P>2</P></TEXTFORMAT>", "xdrawBehaviorCode": "K_API_RECT", "xstrokeProps": {"xtype": "solidstroke", "xthickness": 1, "xcolor": "#000000", "xalpha": 100, "xstart": {"xtype": "none"}, "xend": {"xtype": "none"}}, "xfillProps": {"xtype": "solidfill", "xcolor": "#FFFFFF", "xalpha": 100, "xgtype": "linear"}, "xshadowProps": {"xtype": "none"}, "xcaptionPos": {"xleft": 93.99999999999996, "xtop": 193.00000000000003}, "xtextFormat": {}, "uniqueID": "dt5jtwt60yf9ejjtpff"}, {"xanchors": [{"type": null, "x": 176, "y": 18}, {"type": null, "x": 336, "y": 18}, {"type": null, "x": 336, "y": 98}, {"type": null, "x": 176, "y": 98}], "xtext": "<TEXTFORMAT><P>4</P></TEXTFORMAT>", "xdrawBehaviorCode": "K_API_RECT", "xstrokeProps": {"xtype": "solidstroke", "xthickness": 1, "xcolor": "#000000", "xalpha": 100, "xstart": {"xtype": "none"}, "xend": {"xtype": "none"}}, "xfillProps": {"xtype": "solidfill", "xcolor": "#FFFFFF", "xalpha": 100, "xgtype": "linear"}, "xshadowProps": {"xtype": "none"}, "xtextFormat": {}, "uniqueID": "hdttvaj6qouq543xps7"}, {"xanchors": [{"type": null, "x": 175, "y": 106}, {"type": null, "x": 335, "y": 106}, {"type": null, "x": 335, "y": 186}, {"type": null, "x": 175, "y": 186}], "xtext": "<TEXTFORMAT><P>4</P></TEXTFORMAT>", "xdrawBehaviorCode": "K_API_RECT", "xstrokeProps": {"xtype": "solidstroke", "xthickness": 1, "xcolor": "#000000", "xalpha": 100, "xstart": {"xtype": "none"}, "xend": {"xtype": "none"}}, "xfillProps": {"xtype": "solidfill", "xcolor": "#FFFFFF", "xalpha": 100, "xgtype": "linear"}, "xshadowProps": {"xtype": "none"}, "xtextFormat": {}, "uniqueID": "59sco6zf0nceq7od29w"}, {"xanchors": [{"type": null, "x": 176, "y": 193.00000000000003}, {"type": null, "x": 336, "y": 193.00000000000003}, {"type": null, "x": 336, "y": 273}, {"type": null, "x": 176, "y": 273}], "xtext": "<TEXTFORMAT><P>4</P></TEXTFORMAT>", "xdrawBehaviorCode": "K_API_RECT", "xstrokeProps": {"xtype": "solidstroke", "xthickness": 1, "xcolor": "#000000", "xalpha": 100, "xstart": {"xtype": "none"}, "xend": {"xtype": "none"}}, "xfillProps": {"xtype": "solidfill", "xcolor": "#FFFFFF", "xalpha": 100, "xgtype": "linear"}, "xshadowProps": {"xtype": "none"}, "xcaptionPos": {"xleft": 176, "xtop": 193.00000000000003}, "xtextFormat": {}, "uniqueID": "a7dmfmrtu9fv5dciyjp"}, {"xanchors": [{"type": null, "x": 15.000000000000005, "y": 281.9999999999997}, {"type": null, "x": 83, "y": 281.9999999999997}, {"type": null, "x": 83, "y": 361.9999999999997}, {"type": null, "x": 15.000000000000005, "y": 361.9999999999997}], "xtext": "<TEXTFORMAT><P>2</P></TEXTFORMAT>", "xdrawBehaviorCode": "K_API_RECT", "xstrokeProps": {"xtype": "solidstroke", "xthickness": 1, "xcolor": "#000000", "xalpha": 100, "xstart": {"xtype": "none"}, "xend": {"xtype": "none"}}, "xfillProps": {"xtype": "solidfill", "xcolor": "#FFFFFF", "xalpha": 100, "xgtype": "linear"}, "xshadowProps": {"xtype": "none"}, "xcaptionPos": {"xleft": 15.000000000000005, "xtop": 281.9999999999997}, "xtextFormat": {}, "uniqueID": "ze9mmy32ygbzm9lcdil"}, {"xanchors": [{"type": null, "x": 91.99999999999997, "y": 281.9999999999997}, {"type": null, "x": 166.00000000000003, "y": 281.9999999999997}, {"type": null, "x": 166.00000000000003, "y": 361.9999999999997}, {"type": null, "x": 91.99999999999997, "y": 361.9999999999997}], "xtext": "<TEXTFORMAT><P>2</P></TEXTFORMAT>", "xdrawBehaviorCode": "K_API_RECT", "xstrokeProps": {"xtype": "solidstroke", "xthickness": 1, "xcolor": "#000000", "xalpha": 100, "xstart": {"xtype": "none"}, "xend": {"xtype": "none"}}, "xfillProps": {"xtype": "solidfill", "xcolor": "#FFFFFF", "xalpha": 100, "xgtype": "linear"}, "xshadowProps": {"xtype": "none"}, "xcaptionPos": {"xleft": 91.99999999999997, "xtop": 281.9999999999997}, "xtextFormat": {}, "uniqueID": "5adovz1sgv9pfoqpq6p"}], "alpha": 100, "visible": true, "lock": false, "id": 0}], "reachedGroupNum": 27, "contentSize": {"xheight": 361.9999999999997, "xwidth": 336}, "docDims": {"xheight": 640, "xwidth": 860, "xtop": 0, "xleft": 0}, "currentLayerId": 1}