{"acts": {}, "layersArr": [{"xpropsArr": [{"xanchors": [{"type": null, "x": 14, "y": 28}, {"type": null, "x": 174, "y": 28}, {"type": null, "x": 174, "y": 108}, {"type": null, "x": 14, "y": 108}], "xtext": "<TEXTFORMAT><P>4</P></TEXTFORMAT>", "xdrawBehaviorCode": "K_API_RECT", "xstrokeProps": {"xtype": "solidstroke", "xthickness": 1, "xcolor": "#000000", "xalpha": 100, "xstart": {"xtype": "none"}, "xend": {"xtype": "none"}}, "xfillProps": {"xtype": "solidfill", "xcolor": "#FFFFFF", "xalpha": 100, "xgtype": "linear"}, "xshadowProps": {"xtype": "none"}, "xtextFormat": {}, "uniqueID": "24jchhd1z8txekqaqgq"}, {"xanchors": [{"type": null, "x": 15, "y": 121}, {"type": null, "x": 175, "y": 121}, {"type": null, "x": 175, "y": 201}, {"type": null, "x": 15, "y": 201}], "xtext": "<TEXTFORMAT><P>4</P></TEXTFORMAT>", "xdrawBehaviorCode": "K_API_RECT", "xstrokeProps": {"xtype": "solidstroke", "xthickness": 1, "xcolor": "#000000", "xalpha": 100, "xstart": {"xtype": "none"}, "xend": {"xtype": "none"}}, "xfillProps": {"xtype": "solidfill", "xcolor": "#FFFFFF", "xalpha": 100, "xgtype": "linear"}, "xshadowProps": {"xtype": "none"}, "xtextFormat": {}, "uniqueID": "okbcio5zvky3ffnbulx"}, {"xanchors": [{"type": null, "x": 17, "y": 217}, {"type": null, "x": 177, "y": 217}, {"type": null, "x": 177, "y": 297}, {"type": null, "x": 17, "y": 297}], "xtext": "<TEXTFORMAT><P>4</P></TEXTFORMAT>", "xdrawBehaviorCode": "K_API_RECT", "xstrokeProps": {"xtype": "solidstroke", "xthickness": 1, "xcolor": "#000000", "xalpha": 100, "xstart": {"xtype": "none"}, "xend": {"xtype": "none"}}, "xfillProps": {"xtype": "solidfill", "xcolor": "#FFFFFF", "xalpha": 100, "xgtype": "linear"}, "xshadowProps": {"xtype": "none"}, "xtextFormat": {}, "uniqueID": "pfsdwn5um6jgy4r5yc3"}, {"xanchors": [{"type": null, "x": 15, "y": 310}, {"type": null, "x": 175, "y": 310}, {"type": null, "x": 175, "y": 390}, {"type": null, "x": 15, "y": 390}], "xtext": "<TEXTFORMAT><P>4</P></TEXTFORMAT>", "xdrawBehaviorCode": "K_API_RECT", "xstrokeProps": {"xtype": "solidstroke", "xthickness": 1, "xcolor": "#000000", "xalpha": 100, "xstart": {"xtype": "none"}, "xend": {"xtype": "none"}}, "xfillProps": {"xtype": "solidfill", "xcolor": "#FFFFFF", "xalpha": 100, "xgtype": "linear"}, "xshadowProps": {"xtype": "none"}, "xtextFormat": {}, "uniqueID": "m9db6cajsxd5y3w9o2i"}, {"xanchors": [{"type": null, "x": 186, "y": 311}, {"type": null, "x": 346, "y": 311}, {"type": null, "x": 346, "y": 391}, {"type": null, "x": 186, "y": 391}], "xtext": "<TEXTFORMAT><P>4</P></TEXTFORMAT>", "xdrawBehaviorCode": "K_API_RECT", "xstrokeProps": {"xtype": "solidstroke", "xthickness": 1, "xcolor": "#000000", "xalpha": 100, "xstart": {"xtype": "none"}, "xend": {"xtype": "none"}}, "xfillProps": {"xtype": "solidfill", "xcolor": "#FFFFFF", "xalpha": 100, "xgtype": "linear"}, "xshadowProps": {"xtype": "none"}, "xtextFormat": {}, "uniqueID": "h8dlj6g3nlaz6zqlivc"}, {"xanchors": [{"type": null, "x": 186, "y": 217}, {"type": null, "x": 346, "y": 217}, {"type": null, "x": 346, "y": 297}, {"type": null, "x": 186, "y": 297}], "xtext": "<TEXTFORMAT><P>4</P></TEXTFORMAT>", "xdrawBehaviorCode": "K_API_RECT", "xstrokeProps": {"xtype": "solidstroke", "xthickness": 1, "xcolor": "#000000", "xalpha": 100, "xstart": {"xtype": "none"}, "xend": {"xtype": "none"}}, "xfillProps": {"xtype": "solidfill", "xcolor": "#FFFFFF", "xalpha": 100, "xgtype": "linear"}, "xshadowProps": {"xtype": "none"}, "xtextFormat": {}, "uniqueID": "d4rrexlbvkqvtzs4c75"}, {"xanchors": [{"type": null, "x": 184, "y": 121}, {"type": null, "x": 344, "y": 121}, {"type": null, "x": 344, "y": 201}, {"type": null, "x": 184, "y": 201}], "xtext": "<TEXTFORMAT><P>4</P></TEXTFORMAT>", "xdrawBehaviorCode": "K_API_RECT", "xstrokeProps": {"xtype": "solidstroke", "xthickness": 1, "xcolor": "#000000", "xalpha": 100, "xstart": {"xtype": "none"}, "xend": {"xtype": "none"}}, "xfillProps": {"xtype": "solidfill", "xcolor": "#FFFFFF", "xalpha": 100, "xgtype": "linear"}, "xshadowProps": {"xtype": "none"}, "xtextFormat": {}, "uniqueID": "cyilouapafqi4d90xt6"}, {"xanchors": [{"type": null, "x": 182, "y": 28}, {"type": null, "x": 342, "y": 28}, {"type": null, "x": 342, "y": 108}, {"type": null, "x": 182, "y": 108}], "xtext": "<TEXTFORMAT><P>4</P></TEXTFORMAT>", "xdrawBehaviorCode": "K_API_RECT", "xstrokeProps": {"xtype": "solidstroke", "xthickness": 1, "xcolor": "#000000", "xalpha": 100, "xstart": {"xtype": "none"}, "xend": {"xtype": "none"}}, "xfillProps": {"xtype": "solidfill", "xcolor": "#FFFFFF", "xalpha": 100, "xgtype": "linear"}, "xshadowProps": {"xtype": "none"}, "xtextFormat": {}, "uniqueID": "ueebeth2g5ek8sxyok3"}, {"xanchors": [{"type": null, "x": 351, "y": 28}, {"type": null, "x": 511, "y": 28}, {"type": null, "x": 511, "y": 108}, {"type": null, "x": 351, "y": 108}], "xtext": "<TEXTFORMAT><P>4</P></TEXTFORMAT>", "xdrawBehaviorCode": "K_API_RECT", "xstrokeProps": {"xtype": "solidstroke", "xthickness": 1, "xcolor": "#000000", "xalpha": 100, "xstart": {"xtype": "none"}, "xend": {"xtype": "none"}}, "xfillProps": {"xtype": "solidfill", "xcolor": "#FFFFFF", "xalpha": 100, "xgtype": "linear"}, "xshadowProps": {"xtype": "none"}, "xtextFormat": {}, "uniqueID": "oqakuhj5e9a0fri619p"}, {"xanchors": [{"type": null, "x": 351, "y": 121}, {"type": null, "x": 511, "y": 121}, {"type": null, "x": 511, "y": 201}, {"type": null, "x": 351, "y": 201}], "xtext": "<TEXTFORMAT><P>4</P></TEXTFORMAT>", "xdrawBehaviorCode": "K_API_RECT", "xstrokeProps": {"xtype": "solidstroke", "xthickness": 1, "xcolor": "#000000", "xalpha": 100, "xstart": {"xtype": "none"}, "xend": {"xtype": "none"}}, "xfillProps": {"xtype": "solidfill", "xcolor": "#FFFFFF", "xalpha": 100, "xgtype": "linear"}, "xshadowProps": {"xtype": "none"}, "xtextFormat": {}, "uniqueID": "zrry4ub7pnx6s9hbd36"}, {"xanchors": [{"type": null, "x": 353, "y": 218}, {"type": null, "x": 513, "y": 218}, {"type": null, "x": 513, "y": 298}, {"type": null, "x": 353, "y": 298}], "xtext": "<TEXTFORMAT><P>4</P></TEXTFORMAT>", "xdrawBehaviorCode": "K_API_RECT", "xstrokeProps": {"xtype": "solidstroke", "xthickness": 1, "xcolor": "#000000", "xalpha": 100, "xstart": {"xtype": "none"}, "xend": {"xtype": "none"}}, "xfillProps": {"xtype": "solidfill", "xcolor": "#FFFFFF", "xalpha": 100, "xgtype": "linear"}, "xshadowProps": {"xtype": "none"}, "xtextFormat": {}, "uniqueID": "mcgylx5oquw4eqiyejx"}, {"xanchors": [{"type": null, "x": 355, "y": 311}, {"type": null, "x": 515, "y": 311}, {"type": null, "x": 515, "y": 391}, {"type": null, "x": 355, "y": 391}], "xtext": "<TEXTFORMAT><P>4</P></TEXTFORMAT>", "xdrawBehaviorCode": "K_API_RECT", "xstrokeProps": {"xtype": "solidstroke", "xthickness": 1, "xcolor": "#000000", "xalpha": 100, "xstart": {"xtype": "none"}, "xend": {"xtype": "none"}}, "xfillProps": {"xtype": "solidfill", "xcolor": "#FFFFFF", "xalpha": 100, "xgtype": "linear"}, "xshadowProps": {"xtype": "none"}, "xtextFormat": {}, "uniqueID": "eq4juj3510nqhu73nfz"}, {"xanchors": [{"type": null, "x": 15.000000000000002, "y": 402.99999999999994}, {"type": null, "x": 512.9999999999999, "y": 402.99999999999994}, {"type": null, "x": 512.9999999999999, "y": 482.9999999999999}, {"type": null, "x": 15.000000000000002, "y": 482.9999999999999}], "xtext": "<TEXTFORMAT><P>10</P></TEXTFORMAT>", "xdrawBehaviorCode": "K_API_RECT", "xstrokeProps": {"xtype": "solidstroke", "xthickness": 1, "xcolor": "#000000", "xalpha": 100, "xstart": {"xtype": "none"}, "xend": {"xtype": "none"}}, "xfillProps": {"xtype": "solidfill", "xcolor": "#FFFFFF", "xalpha": 100, "xgtype": "linear"}, "xshadowProps": {"xtype": "none"}, "xcaptionPos": {"xleft": 15.000000000000002, "xtop": 402.99999999999994}, "xtextFormat": {}, "uniqueID": "r5emhjpgandz7uh20w0"}, {"xanchors": [{"type": null, "x": 15, "y": 499.00000000000034}, {"type": null, "x": 515, "y": 499.00000000000034}, {"type": null, "x": 515, "y": 579.0000000000003}, {"type": null, "x": 15, "y": 579.0000000000003}], "xtext": "<TEXTFORMAT><P>10</P></TEXTFORMAT>", "xdrawBehaviorCode": "K_API_RECT", "xstrokeProps": {"xtype": "solidstroke", "xthickness": 1, "xcolor": "#000000", "xalpha": 100, "xstart": {"xtype": "none"}, "xend": {"xtype": "none"}}, "xfillProps": {"xtype": "solidfill", "xcolor": "#FFFFFF", "xalpha": 100, "xgtype": "linear"}, "xshadowProps": {"xtype": "none"}, "xcaptionPos": {"xleft": 15, "xtop": 499.00000000000034}, "xtextFormat": {}, "uniqueID": "5fmro59r6puuo1anhnp"}], "alpha": 100, "visible": true, "lock": false, "id": 0}], "reachedGroupNum": 27, "contentSize": {"xheight": 579.0000000000003, "xwidth": 515}, "docDims": {"xheight": 640, "xwidth": 860, "xtop": 0, "xleft": 0}, "currentLayerId": 1}