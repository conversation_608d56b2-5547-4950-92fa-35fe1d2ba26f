{"acts": {}, "layersArr": [{"xpropsArr": [{"xanchors": [{"type": null, "x": 31, "y": 121}, {"type": null, "x": 111, "y": 41}, {"type": null, "x": 191, "y": 121}, {"type": null, "x": 111, "y": 201}], "xtext": "<TEXTFORMAT><P>8</P></TEXTFORMAT>", "xdrawBehaviorCode": "K_API_ELLIPSE", "xstrokeProps": {"xtype": "solidstroke", "xthickness": 1, "xcolor": "#000000", "xalpha": 100, "xstart": {"xtype": "none"}, "xend": {"xtype": "none"}}, "xfillProps": {"xtype": "solidfill", "xcolor": "#FFFFFF", "xalpha": 100, "xgtype": "linear"}, "xshadowProps": {"xtype": "none"}, "xtextFormat": {}, "uniqueID": "aloxopj9r6ir3kqmrzq"}, {"xanchors": [{"type": null, "x": 247.99999999999966, "y": 81.99999999999999}, {"type": null, "x": 535, "y": 81.99999999999999}, {"type": null, "x": 535, "y": 161.99999999999994}, {"type": null, "x": 247.99999999999966, "y": 161.99999999999994}], "xtext": "<TEXTFORMAT><P>6</P></TEXTFORMAT>", "xdrawBehaviorCode": "K_API_RECT", "xstrokeProps": {"xtype": "solidstroke", "xthickness": 1, "xcolor": "#000000", "xalpha": 100, "xstart": {"xtype": "none"}, "xend": {"xtype": "none"}}, "xfillProps": {"xtype": "solidfill", "xcolor": "#FFFFFF", "xalpha": 100, "xgtype": "linear"}, "xshadowProps": {"xtype": "none"}, "xcaptionPos": {"xleft": 247.99999999999966, "xtop": 81.99999999999999}, "xtextFormat": {}, "uniqueID": "xnsauzn4rhh1dxk5krw"}, {"xanchors": [{"type": null, "x": 53, "y": 235}, {"type": null, "x": 213, "y": 235}, {"type": null, "x": 213, "y": 315}, {"type": null, "x": 53, "y": 315}], "xtext": "<TEXTFORMAT><P>4</P></TEXTFORMAT>", "xdrawBehaviorCode": "K_API_RECT", "xstrokeProps": {"xtype": "solidstroke", "xthickness": 1, "xcolor": "#000000", "xalpha": 100, "xstart": {"xtype": "none"}, "xend": {"xtype": "none"}}, "xfillProps": {"xtype": "solidfill", "xcolor": "#FFFFFF", "xalpha": 100, "xgtype": "linear"}, "xshadowProps": {"xtype": "none"}, "xtextFormat": {}, "uniqueID": "zulk6fh9xdweqpci14z"}, {"xanchors": [{"type": null, "x": 239, "y": 235}, {"type": null, "x": 399, "y": 235}, {"type": null, "x": 399, "y": 315}, {"type": null, "x": 239, "y": 315}], "xtext": "<TEXTFORMAT><P>4</P></TEXTFORMAT>", "xdrawBehaviorCode": "K_API_RECT", "xstrokeProps": {"xtype": "solidstroke", "xthickness": 1, "xcolor": "#000000", "xalpha": 100, "xstart": {"xtype": "none"}, "xend": {"xtype": "none"}}, "xfillProps": {"xtype": "solidfill", "xcolor": "#FFFFFF", "xalpha": 100, "xgtype": "linear"}, "xshadowProps": {"xtype": "none"}, "xtextFormat": {}, "uniqueID": "wu2ueqzfwen6uu3qz78"}, {"xanchors": [{"type": null, "x": 412, "y": 235}, {"type": null, "x": 572, "y": 235}, {"type": null, "x": 572, "y": 315}, {"type": null, "x": 412, "y": 315}], "xtext": "<TEXTFORMAT><P>4</P></TEXTFORMAT>", "xdrawBehaviorCode": "K_API_RECT", "xstrokeProps": {"xtype": "solidstroke", "xthickness": 1, "xcolor": "#000000", "xalpha": 100, "xstart": {"xtype": "none"}, "xend": {"xtype": "none"}}, "xfillProps": {"xtype": "solidfill", "xcolor": "#FFFFFF", "xalpha": 100, "xgtype": "linear"}, "xshadowProps": {"xtype": "none"}, "xtextFormat": {}, "uniqueID": "9vbuhp00iynlgov2eqg"}, {"xanchors": [{"type": null, "x": 53, "y": 332}, {"type": null, "x": 213, "y": 332}, {"type": null, "x": 213, "y": 412}, {"type": null, "x": 53, "y": 412}], "xtext": "<TEXTFORMAT><P>4</P></TEXTFORMAT>", "xdrawBehaviorCode": "K_API_RECT", "xstrokeProps": {"xtype": "solidstroke", "xthickness": 1, "xcolor": "#000000", "xalpha": 100, "xstart": {"xtype": "none"}, "xend": {"xtype": "none"}}, "xfillProps": {"xtype": "solidfill", "xcolor": "#FFFFFF", "xalpha": 100, "xgtype": "linear"}, "xshadowProps": {"xtype": "none"}, "xtextFormat": {}, "uniqueID": "blcvpuaowokdb8ohtql"}, {"xanchors": [{"type": null, "x": 237, "y": 332}, {"type": null, "x": 397, "y": 332}, {"type": null, "x": 397, "y": 412}, {"type": null, "x": 237, "y": 412}], "xtext": "<TEXTFORMAT><P>4</P></TEXTFORMAT>", "xdrawBehaviorCode": "K_API_RECT", "xstrokeProps": {"xtype": "solidstroke", "xthickness": 1, "xcolor": "#000000", "xalpha": 100, "xstart": {"xtype": "none"}, "xend": {"xtype": "none"}}, "xfillProps": {"xtype": "solidfill", "xcolor": "#FFFFFF", "xalpha": 100, "xgtype": "linear"}, "xshadowProps": {"xtype": "none"}, "xtextFormat": {}, "uniqueID": "eyflivmnecjzrx1mmlk"}, {"xanchors": [{"type": null, "x": 414, "y": 333}, {"type": null, "x": 574, "y": 333}, {"type": null, "x": 574, "y": 413}, {"type": null, "x": 414, "y": 413}], "xtext": "<TEXTFORMAT><P>4</P></TEXTFORMAT>", "xdrawBehaviorCode": "K_API_RECT", "xstrokeProps": {"xtype": "solidstroke", "xthickness": 1, "xcolor": "#000000", "xalpha": 100, "xstart": {"xtype": "none"}, "xend": {"xtype": "none"}}, "xfillProps": {"xtype": "solidfill", "xcolor": "#FFFFFF", "xalpha": 100, "xgtype": "linear"}, "xshadowProps": {"xtype": "none"}, "xtextFormat": {}, "uniqueID": "lc36h156fipo1571uua"}, {"xanchors": [{"type": null, "x": 55, "y": 433}, {"type": null, "x": 215, "y": 433}, {"type": null, "x": 215, "y": 513}, {"type": null, "x": 55, "y": 513}], "xtext": "<TEXTFORMAT><P>4</P></TEXTFORMAT>", "xdrawBehaviorCode": "K_API_RECT", "xstrokeProps": {"xtype": "solidstroke", "xthickness": 1, "xcolor": "#000000", "xalpha": 100, "xstart": {"xtype": "none"}, "xend": {"xtype": "none"}}, "xfillProps": {"xtype": "solidfill", "xcolor": "#FFFFFF", "xalpha": 100, "xgtype": "linear"}, "xshadowProps": {"xtype": "none"}, "xtextFormat": {}, "uniqueID": "odkbesilkren45vmqdx"}, {"xanchors": [{"type": null, "x": 233, "y": 435}, {"type": null, "x": 393, "y": 435}, {"type": null, "x": 393, "y": 515}, {"type": null, "x": 233, "y": 515}], "xtext": "<TEXTFORMAT><P>4</P></TEXTFORMAT>", "xdrawBehaviorCode": "K_API_RECT", "xstrokeProps": {"xtype": "solidstroke", "xthickness": 1, "xcolor": "#000000", "xalpha": 100, "xstart": {"xtype": "none"}, "xend": {"xtype": "none"}}, "xfillProps": {"xtype": "solidfill", "xcolor": "#FFFFFF", "xalpha": 100, "xgtype": "linear"}, "xshadowProps": {"xtype": "none"}, "xtextFormat": {}, "uniqueID": "jy7rz7tju0tkyp0ln10"}, {"xanchors": [{"type": null, "x": 413, "y": 436}, {"type": null, "x": 573, "y": 436}, {"type": null, "x": 573, "y": 516}, {"type": null, "x": 413, "y": 516}], "xtext": "<TEXTFORMAT><P>4</P></TEXTFORMAT>", "xdrawBehaviorCode": "K_API_RECT", "xstrokeProps": {"xtype": "solidstroke", "xthickness": 1, "xcolor": "#000000", "xalpha": 100, "xstart": {"xtype": "none"}, "xend": {"xtype": "none"}}, "xfillProps": {"xtype": "solidfill", "xcolor": "#FFFFFF", "xalpha": 100, "xgtype": "linear"}, "xshadowProps": {"xtype": "none"}, "xtextFormat": {}, "uniqueID": "19yswe4ixx8footp5me"}], "alpha": 100, "visible": true, "lock": false, "id": 0}], "reachedGroupNum": 27, "contentSize": {"xheight": 516, "xwidth": 574}, "docDims": {"xheight": 640, "xwidth": 860, "xtop": 0, "xleft": 0}, "currentLayerId": 1}