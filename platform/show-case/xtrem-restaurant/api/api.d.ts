declare module '@sage/xtrem-restaurant-api-partial' {
    import type { Package as SageXtremAuditing$Package } from '@sage/xtrem-auditing-api';
    import type { Package as SageXtremAuthorization$Package } from '@sage/xtrem-authorization-api';
    import type { Package as SageXtremCommunication$Package } from '@sage/xtrem-communication-api';
    import type { Package as SageXtremCustomization$Package } from '@sage/xtrem-customization-api';
    import type { Package as SageXtremDashboard$Package } from '@sage/xtrem-dashboard-api';
    import type { Package as SageXtremImportExport$Package } from '@sage/xtrem-import-export-api';
    import type { Package as SageXtremInterop$Package } from '@sage/xtrem-interop-api';
    import type { Package as SageXtremMailer$Package } from '@sage/xtrem-mailer-api';
    import type { MetaNodeFactory, Package as SageXtremMetadata$Package } from '@sage/xtrem-metadata-api';
    import type { Package as SageXtremReporting$Package } from '@sage/xtrem-reporting-api';
    import type { Package as SageXtremRouting$Package } from '@sage/xtrem-routing-api';
    import type { Package as SageXtremScheduler$Package } from '@sage/xtrem-scheduler-api';
    import type { Package as SageXtremSystem$Package, User } from '@sage/xtrem-system-api';
    import type { Package as SageXtremUpload$Package } from '@sage/xtrem-upload-api';
    import type { Package as SageXtremWorkflow$Package } from '@sage/xtrem-workflow-api';
    import type {
        AggregateQueryOperation,
        AggregateReadOperation,
        AsyncOperation,
        BinaryStream,
        ClientCollection,
        ClientNode,
        ClientNodeInput,
        CreateOperation,
        DeleteOperation,
        DuplicateOperation,
        GetDefaultsOperation,
        GetDuplicateOperation,
        QueryOperation,
        ReadOperation,
        TextStream,
        UpdateByIdOperation,
        UpdateOperation,
        VitalClientNode,
        VitalClientNodeInput,
        decimal,
        integer,
    } from '@sage/xtrem-client';
    export interface OrderStatus$Enum {
        new: 0;
        preparing: 1;
        delivered: 2;
    }
    export type OrderStatus = keyof OrderStatus$Enum;
    export interface ItemCategory$Enum {
        appetizer: 0;
        beverage: 1;
        breakfast: 2;
        dessert: 3;
        dinner: 4;
        lunch: 5;
        side_dish: 6;
        snack: 7;
        snacks: 8;
    }
    export type ItemCategory = keyof ItemCategory$Enum;
    export interface Address extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        name: string;
        line1: string;
        line2: string;
        city: string;
        country: Country;
        restaurant: Restaurant;
    }
    export interface AddressInput extends VitalClientNodeInput {
        name?: string;
        line1?: string;
        line2?: string;
        postCode?: string;
        city?: string;
        country?: integer | string;
    }
    export interface AddressBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        name: string;
        line1: string;
        line2: string;
        postCode: string;
        city: string;
        country: Country;
        restaurant: Restaurant;
    }
    export interface Address$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface Address$Lookups {
        country: QueryOperation<Country>;
    }
    export interface Address$Operations {
        query: QueryOperation<Address>;
        read: ReadOperation<Address>;
        aggregate: {
            read: AggregateReadOperation<Address>;
            query: AggregateQueryOperation<Address>;
        };
        create: CreateOperation<AddressInput, Address>;
        getDuplicate: GetDuplicateOperation<Address>;
        duplicate: DuplicateOperation<string, AddressInput, Address>;
        update: UpdateOperation<AddressInput, Address>;
        updateById: UpdateByIdOperation<AddressInput, Address>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: Address$AsyncOperations;
        lookups(dataOrId: string | { data: AddressInput }): Address$Lookups;
        getDefaults: GetDefaultsOperation<Address>;
    }
    export interface Allergen extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        name: string;
        icon: BinaryStream;
        ingredients: ClientCollection<AllergenIngredient>;
    }
    export interface AllergenInput extends ClientNodeInput {
        name?: string;
        icon?: BinaryStream;
        ingredients?: Partial<AllergenIngredientInput>[];
    }
    export interface AllergenBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        name: string;
        icon: BinaryStream;
        ingredients: ClientCollection<AllergenIngredient>;
    }
    export interface Allergen$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface Allergen$Operations {
        query: QueryOperation<Allergen>;
        read: ReadOperation<Allergen>;
        aggregate: {
            read: AggregateReadOperation<Allergen>;
            query: AggregateQueryOperation<Allergen>;
        };
        create: CreateOperation<AllergenInput, Allergen>;
        getDuplicate: GetDuplicateOperation<Allergen>;
        duplicate: DuplicateOperation<string, AllergenInput, Allergen>;
        update: UpdateOperation<AllergenInput, Allergen>;
        updateById: UpdateByIdOperation<AllergenInput, Allergen>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: Allergen$AsyncOperations;
        getDefaults: GetDefaultsOperation<Allergen>;
    }
    export interface AllergenIngredient extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        allergen: Allergen;
        ingredient: Ingredient;
    }
    export interface AllergenIngredientInput extends VitalClientNodeInput {
        allergen?: integer | string;
    }
    export interface AllergenIngredientBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        allergen: Allergen;
        ingredient: Ingredient;
    }
    export interface AllergenIngredient$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface AllergenIngredient$Lookups {
        allergen: QueryOperation<Allergen>;
    }
    export interface AllergenIngredient$Operations {
        query: QueryOperation<AllergenIngredient>;
        read: ReadOperation<AllergenIngredient>;
        aggregate: {
            read: AggregateReadOperation<AllergenIngredient>;
            query: AggregateQueryOperation<AllergenIngredient>;
        };
        create: CreateOperation<AllergenIngredientInput, AllergenIngredient>;
        getDuplicate: GetDuplicateOperation<AllergenIngredient>;
        duplicate: DuplicateOperation<string, AllergenIngredientInput, AllergenIngredient>;
        update: UpdateOperation<AllergenIngredientInput, AllergenIngredient>;
        updateById: UpdateByIdOperation<AllergenIngredientInput, AllergenIngredient>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: AllergenIngredient$AsyncOperations;
        lookups(dataOrId: string | { data: AllergenIngredientInput }): AllergenIngredient$Lookups;
        getDefaults: GetDefaultsOperation<AllergenIngredient>;
    }
    export interface Country extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        phoneCountryCode: integer;
        name: string;
        addresses: ClientCollection<Address>;
    }
    export interface CountryInput extends ClientNodeInput {
        phoneCountryCode?: integer | string;
        name?: string;
    }
    export interface CountryBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        phoneCountryCode: integer;
        name: string;
        addresses: ClientCollection<Address>;
    }
    export interface Country$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface Country$Operations {
        query: QueryOperation<Country>;
        read: ReadOperation<Country>;
        aggregate: {
            read: AggregateReadOperation<Country>;
            query: AggregateQueryOperation<Country>;
        };
        create: CreateOperation<CountryInput, Country>;
        getDuplicate: GetDuplicateOperation<Country>;
        duplicate: DuplicateOperation<string, CountryInput, Country>;
        update: UpdateOperation<CountryInput, Country>;
        updateById: UpdateByIdOperation<CountryInput, Country>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: Country$AsyncOperations;
        getDefaults: GetDefaultsOperation<Country>;
    }
    export interface Currency extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        name: string;
        symbol: string;
        decimalDigits: integer;
        icon: BinaryStream;
    }
    export interface CurrencyInput extends ClientNodeInput {
        name?: string;
        symbol?: string;
        decimalDigits?: integer | string;
        icon?: BinaryStream;
    }
    export interface CurrencyBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        name: string;
        symbol: string;
        decimalDigits: integer;
        icon: BinaryStream;
    }
    export interface Currency$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface Currency$Operations {
        query: QueryOperation<Currency>;
        read: ReadOperation<Currency>;
        aggregate: {
            read: AggregateReadOperation<Currency>;
            query: AggregateQueryOperation<Currency>;
        };
        create: CreateOperation<CurrencyInput, Currency>;
        getDuplicate: GetDuplicateOperation<Currency>;
        duplicate: DuplicateOperation<string, CurrencyInput, Currency>;
        update: UpdateOperation<CurrencyInput, Currency>;
        updateById: UpdateByIdOperation<CurrencyInput, Currency>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: Currency$AsyncOperations;
        getDefaults: GetDefaultsOperation<Currency>;
    }
    export interface Ingredient extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        name: string;
        unit: Unit;
        allergens: ClientCollection<AllergenIngredient>;
    }
    export interface IngredientInput extends ClientNodeInput {
        name?: string;
        unit?: integer | string;
        allergens?: Partial<AllergenIngredientInput>[];
    }
    export interface IngredientBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        name: string;
        unit: Unit;
        allergens: ClientCollection<AllergenIngredientBinding>;
    }
    export interface Ingredient$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface Ingredient$Lookups {
        unit: QueryOperation<Unit>;
    }
    export interface Ingredient$Operations {
        query: QueryOperation<Ingredient>;
        read: ReadOperation<Ingredient>;
        aggregate: {
            read: AggregateReadOperation<Ingredient>;
            query: AggregateQueryOperation<Ingredient>;
        };
        create: CreateOperation<IngredientInput, Ingredient>;
        getDuplicate: GetDuplicateOperation<Ingredient>;
        duplicate: DuplicateOperation<string, IngredientInput, Ingredient>;
        update: UpdateOperation<IngredientInput, Ingredient>;
        updateById: UpdateByIdOperation<IngredientInput, Ingredient>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: Ingredient$AsyncOperations;
        lookups(dataOrId: string | { data: IngredientInput }): Ingredient$Lookups;
        getDefaults: GetDefaultsOperation<Ingredient>;
    }
    export interface ItemIngredient extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        quantity: integer;
        ingredient: Ingredient;
        item: Item;
    }
    export interface ItemIngredientInput extends VitalClientNodeInput {
        quantity?: integer | string;
        item?: integer | string;
    }
    export interface ItemIngredientBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        quantity: integer;
        ingredient: Ingredient;
        item: Item;
    }
    export interface ItemIngredient$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface ItemIngredient$Lookups {
        item: QueryOperation<Item>;
    }
    export interface ItemIngredient$Operations {
        query: QueryOperation<ItemIngredient>;
        read: ReadOperation<ItemIngredient>;
        aggregate: {
            read: AggregateReadOperation<ItemIngredient>;
            query: AggregateQueryOperation<ItemIngredient>;
        };
        create: CreateOperation<ItemIngredientInput, ItemIngredient>;
        getDuplicate: GetDuplicateOperation<ItemIngredient>;
        duplicate: DuplicateOperation<string, ItemIngredientInput, ItemIngredient>;
        update: UpdateOperation<ItemIngredientInput, ItemIngredient>;
        updateById: UpdateByIdOperation<ItemIngredientInput, ItemIngredient>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: ItemIngredient$AsyncOperations;
        lookups(dataOrId: string | { data: ItemIngredientInput }): ItemIngredient$Lookups;
        getDefaults: GetDefaultsOperation<ItemIngredient>;
    }
    export interface Item extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        price: string;
        name: string;
        description: string;
        preparationTime: integer;
        category: ItemCategory;
        photo: BinaryStream;
        cookingInstructions: TextStream;
        ingredients: ClientCollection<ItemIngredient>;
        orderItems: ClientCollection<OrderItem>;
        menuItems: ClientCollection<MenuItem>;
    }
    export interface ItemInput extends ClientNodeInput {
        price?: decimal | string;
        name?: string;
        description?: string;
        preparationTime?: integer | string;
        category?: ItemCategory;
        photo?: BinaryStream;
        cookingInstructions?: TextStream;
        ingredients?: Partial<ItemIngredientInput>[];
        menuItems?: Partial<MenuItemInput>[];
    }
    export interface ItemBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        price: string;
        name: string;
        description: string;
        preparationTime: integer;
        category: ItemCategory;
        photo: BinaryStream;
        cookingInstructions: TextStream;
        ingredients: ClientCollection<ItemIngredient>;
        orderItems: ClientCollection<OrderItem>;
        menuItems: ClientCollection<MenuItem>;
    }
    export interface Item$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface Item$Operations {
        query: QueryOperation<Item>;
        read: ReadOperation<Item>;
        aggregate: {
            read: AggregateReadOperation<Item>;
            query: AggregateQueryOperation<Item>;
        };
        create: CreateOperation<ItemInput, Item>;
        getDuplicate: GetDuplicateOperation<Item>;
        duplicate: DuplicateOperation<string, ItemInput, Item>;
        update: UpdateOperation<ItemInput, Item>;
        updateById: UpdateByIdOperation<ItemInput, Item>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: Item$AsyncOperations;
        getDefaults: GetDefaultsOperation<Item>;
    }
    export interface MenuItem extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        price: string;
        isActive: boolean;
        restaurant: Restaurant;
        item: Item;
    }
    export interface MenuItemInput extends VitalClientNodeInput {
        price?: decimal | string;
        isActive?: boolean | string;
        item?: integer | string;
    }
    export interface MenuItemBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        price: string;
        isActive: boolean;
        restaurant: Restaurant;
        item: Item;
    }
    export interface MenuItem$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface MenuItem$Lookups {
        item: QueryOperation<Item>;
    }
    export interface MenuItem$Operations {
        query: QueryOperation<MenuItem>;
        read: ReadOperation<MenuItem>;
        aggregate: {
            read: AggregateReadOperation<MenuItem>;
            query: AggregateQueryOperation<MenuItem>;
        };
        create: CreateOperation<MenuItemInput, MenuItem>;
        getDuplicate: GetDuplicateOperation<MenuItem>;
        duplicate: DuplicateOperation<string, MenuItemInput, MenuItem>;
        update: UpdateOperation<MenuItemInput, MenuItem>;
        updateById: UpdateByIdOperation<MenuItemInput, MenuItem>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: MenuItem$AsyncOperations;
        lookups(dataOrId: string | { data: MenuItemInput }): MenuItem$Lookups;
        getDefaults: GetDefaultsOperation<MenuItem>;
    }
    export interface OrderItem extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        unitPrice: string;
        quantity: integer;
        notes: string;
        status: OrderStatus;
        menuItem: MenuItem;
        order: Order;
    }
    export interface OrderItemInput extends VitalClientNodeInput {
        unitPrice?: decimal | string;
        quantity?: integer | string;
        notes?: string;
        status?: OrderStatus;
        menuItem?: integer | string;
    }
    export interface OrderItemBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        unitPrice: string;
        quantity: integer;
        notes: string;
        status: OrderStatus;
        menuItem: MenuItem;
        order: Order;
    }
    export interface OrderItem$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface OrderItem$Lookups {
        menuItem: QueryOperation<MenuItem>;
    }
    export interface OrderItem$Operations {
        query: QueryOperation<OrderItem>;
        read: ReadOperation<OrderItem>;
        aggregate: {
            read: AggregateReadOperation<OrderItem>;
            query: AggregateQueryOperation<OrderItem>;
        };
        create: CreateOperation<OrderItemInput, OrderItem>;
        getDuplicate: GetDuplicateOperation<OrderItem>;
        duplicate: DuplicateOperation<string, OrderItemInput, OrderItem>;
        update: UpdateOperation<OrderItemInput, OrderItem>;
        updateById: UpdateByIdOperation<OrderItemInput, OrderItem>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: OrderItem$AsyncOperations;
        lookups(dataOrId: string | { data: OrderItemInput }): OrderItem$Lookups;
        getDefaults: GetDefaultsOperation<OrderItem>;
    }
    export interface Order extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        table: Table;
        items: ClientCollection<OrderItem>;
        status: OrderStatus;
        totalPrice: string;
    }
    export interface OrderInput extends VitalClientNodeInput {
        items?: Partial<OrderItemInput>[];
    }
    export interface OrderBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        table: Table;
        items: ClientCollection<OrderItemBinding>;
        status: OrderStatus;
        totalPrice: string;
    }
    export interface Order$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface Order$Operations {
        query: QueryOperation<Order>;
        read: ReadOperation<Order>;
        aggregate: {
            read: AggregateReadOperation<Order>;
            query: AggregateQueryOperation<Order>;
        };
        create: CreateOperation<OrderInput, Order>;
        getDuplicate: GetDuplicateOperation<Order>;
        duplicate: DuplicateOperation<string, OrderInput, Order>;
        update: UpdateOperation<OrderInput, Order>;
        updateById: UpdateByIdOperation<OrderInput, Order>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: Order$AsyncOperations;
        getDefaults: GetDefaultsOperation<Order>;
    }
    export interface Restaurant extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        name: string;
        phoneNumber: string;
        capacity: integer;
        currency: Currency;
        address: Address;
        tables: ClientCollection<Table>;
        menuItems: ClientCollection<MenuItem>;
        tableLayoutChart: TextStream;
    }
    export interface RestaurantInput extends ClientNodeInput {
        name?: string;
        phoneNumber?: string;
        capacity?: integer | string;
        currency?: integer | string;
        address?: AddressInput;
        tables?: Partial<TableInput>[];
        menuItems?: Partial<MenuItemInput>[];
        tableLayoutChart?: TextStream;
    }
    export interface RestaurantBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        name: string;
        phoneNumber: string;
        capacity: integer;
        currency: Currency;
        address: AddressBinding;
        tables: ClientCollection<TableBinding>;
        menuItems: ClientCollection<MenuItemBinding>;
        tableLayoutChart: TextStream;
    }
    export interface Restaurant$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface Restaurant$Lookups {
        currency: QueryOperation<Currency>;
    }
    export interface Restaurant$Operations {
        query: QueryOperation<Restaurant>;
        read: ReadOperation<Restaurant>;
        aggregate: {
            read: AggregateReadOperation<Restaurant>;
            query: AggregateQueryOperation<Restaurant>;
        };
        create: CreateOperation<RestaurantInput, Restaurant>;
        getDuplicate: GetDuplicateOperation<Restaurant>;
        duplicate: DuplicateOperation<string, RestaurantInput, Restaurant>;
        update: UpdateOperation<RestaurantInput, Restaurant>;
        updateById: UpdateByIdOperation<RestaurantInput, Restaurant>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: Restaurant$AsyncOperations;
        lookups(dataOrId: string | { data: RestaurantInput }): Restaurant$Lookups;
        getDefaults: GetDefaultsOperation<Restaurant>;
    }
    export interface Table extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        name: string;
        capacity: integer;
        restaurant: Restaurant;
        orders: ClientCollection<Order>;
    }
    export interface TableInput extends VitalClientNodeInput {
        name?: string;
        capacity?: integer | string;
        orders?: Partial<OrderInput>[];
    }
    export interface TableBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        name: string;
        capacity: integer;
        restaurant: Restaurant;
        orders: ClientCollection<OrderBinding>;
    }
    export interface Table$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface Table$Operations {
        query: QueryOperation<Table>;
        read: ReadOperation<Table>;
        aggregate: {
            read: AggregateReadOperation<Table>;
            query: AggregateQueryOperation<Table>;
        };
        create: CreateOperation<TableInput, Table>;
        getDuplicate: GetDuplicateOperation<Table>;
        duplicate: DuplicateOperation<string, TableInput, Table>;
        update: UpdateOperation<TableInput, Table>;
        updateById: UpdateByIdOperation<TableInput, Table>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: Table$AsyncOperations;
        getDefaults: GetDefaultsOperation<Table>;
    }
    export interface Unit extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        description: string;
        numberOfDecimals: integer;
        symbol: string;
    }
    export interface UnitInput extends ClientNodeInput {
        description?: string;
        numberOfDecimals?: integer | string;
        symbol?: string;
    }
    export interface UnitBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        description: string;
        numberOfDecimals: integer;
        symbol: string;
    }
    export interface Unit$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface Unit$Operations {
        query: QueryOperation<Unit>;
        read: ReadOperation<Unit>;
        aggregate: {
            read: AggregateReadOperation<Unit>;
            query: AggregateQueryOperation<Unit>;
        };
        asyncOperations: Unit$AsyncOperations;
        getDefaults: GetDefaultsOperation<Unit>;
    }
    export interface Package {
        '@sage/xtrem-restaurant/Address': Address$Operations;
        '@sage/xtrem-restaurant/Allergen': Allergen$Operations;
        '@sage/xtrem-restaurant/AllergenIngredient': AllergenIngredient$Operations;
        '@sage/xtrem-restaurant/Country': Country$Operations;
        '@sage/xtrem-restaurant/Currency': Currency$Operations;
        '@sage/xtrem-restaurant/Ingredient': Ingredient$Operations;
        '@sage/xtrem-restaurant/ItemIngredient': ItemIngredient$Operations;
        '@sage/xtrem-restaurant/Item': Item$Operations;
        '@sage/xtrem-restaurant/MenuItem': MenuItem$Operations;
        '@sage/xtrem-restaurant/OrderItem': OrderItem$Operations;
        '@sage/xtrem-restaurant/Order': Order$Operations;
        '@sage/xtrem-restaurant/Restaurant': Restaurant$Operations;
        '@sage/xtrem-restaurant/Table': Table$Operations;
        '@sage/xtrem-restaurant/Unit': Unit$Operations;
    }
    export interface GraphApi
        extends Package,
            SageXtremAuditing$Package,
            SageXtremAuthorization$Package,
            SageXtremCommunication$Package,
            SageXtremCustomization$Package,
            SageXtremDashboard$Package,
            SageXtremImportExport$Package,
            SageXtremInterop$Package,
            SageXtremMailer$Package,
            SageXtremMetadata$Package,
            SageXtremReporting$Package,
            SageXtremRouting$Package,
            SageXtremScheduler$Package,
            SageXtremSystem$Package,
            SageXtremUpload$Package,
            SageXtremWorkflow$Package {}
}
declare module '@sage/xtrem-restaurant-api' {
    export type * from '@sage/xtrem-restaurant-api-partial';
}
declare module '@sage/xtrem-auditing-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-restaurant-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-authorization-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-restaurant-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-communication-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-restaurant-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-customization-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-restaurant-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-dashboard-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-restaurant-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-import-export-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-restaurant-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-interop-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-restaurant-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-mailer-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-restaurant-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-metadata-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-restaurant-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-reporting-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-restaurant-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-routing-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-restaurant-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-scheduler-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-restaurant-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-system-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-restaurant-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-upload-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-restaurant-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-workflow-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-restaurant-api';
    export interface GraphApi extends GraphApiExtension {}
}
