import * as ui from '@sage/xtrem-ui';
import { OrderStatus } from '@sage/xtrem-restaurant-api';

export function getOrderStatusColor(status: OrderStatus): string {
    switch (status) {
        case 'delivered':
            return ui.tokens.colorsSemanticPositive600;
        case 'preparing':
            return ui.tokens.colorsSemanticNeutral600;
        case 'new':
            return ui.tokens.colorsSemanticCaution650;
        default:
            return ui.tokens.colorsUtilityYin065;
    }
}
