{"@sage/xtrem-restaurant/data_types__item_category_enum__name": "Item category enum", "@sage/xtrem-restaurant/data_types__order_status_enum__name": "Order status enum", "@sage/xtrem-restaurant/enums__item_category__appetizer": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-restaurant/enums__item_category__beverage": "Beverage", "@sage/xtrem-restaurant/enums__item_category__breakfast": "Breakfast", "@sage/xtrem-restaurant/enums__item_category__dessert": "Dessert", "@sage/xtrem-restaurant/enums__item_category__dinner": "Dinner", "@sage/xtrem-restaurant/enums__item_category__lunch": "Lunch", "@sage/xtrem-restaurant/enums__item_category__side_dish": "Side dish", "@sage/xtrem-restaurant/enums__item_category__snack": "Snack", "@sage/xtrem-restaurant/enums__item_category__snacks": "Snacks", "@sage/xtrem-restaurant/enums__order_status__delivered": "Delivered", "@sage/xtrem-restaurant/enums__order_status__new": "New", "@sage/xtrem-restaurant/enums__order_status__preparing": "Preparing", "@sage/xtrem-restaurant/menu_item__application-pages": "Restaurant pages", "@sage/xtrem-restaurant/menu_item__chart-field": "Chart Field", "@sage/xtrem-restaurant/menu_item__containers": "Containers", "@sage/xtrem-restaurant/menu_item__fields": "Fields", "@sage/xtrem-restaurant/menu_item__misc": "Misc", "@sage/xtrem-restaurant/menu_item__multi-reference-fields": "Multi-reference", "@sage/xtrem-restaurant/menu_item__navigation-panel": "Navigation Panel", "@sage/xtrem-restaurant/menu_item__nested-grid": "Nested Grid", "@sage/xtrem-restaurant/menu_item__nested-pod": "Nested Pod", "@sage/xtrem-restaurant/menu_item__plugins": "Plugins", "@sage/xtrem-restaurant/menu_item__pod": "Pod", "@sage/xtrem-restaurant/menu_item__pod-collection": "Pod Collection", "@sage/xtrem-restaurant/menu_item__reference-fields": "Reference", "@sage/xtrem-restaurant/menu_item__table-field": "Table Field", "@sage/xtrem-restaurant/menu_item__text-field": "Text", "@sage/xtrem-restaurant/menu_item__utils": "Utils", "@sage/xtrem-restaurant/menu_item__validation": "Validation", "@sage/xtrem-restaurant/menu_item__vital-pod": "Vital pod", "@sage/xtrem-restaurant/nodes__address__asyncMutation__asyncExport": "Export", "@sage/xtrem-restaurant/nodes__address__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-restaurant/nodes__address__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-restaurant/nodes__address__node_name": "Address", "@sage/xtrem-restaurant/nodes__address__property__city": "City", "@sage/xtrem-restaurant/nodes__address__property__country": "Country", "@sage/xtrem-restaurant/nodes__address__property__line1": "Line 1", "@sage/xtrem-restaurant/nodes__address__property__line2": "Line 2", "@sage/xtrem-restaurant/nodes__address__property__name": "Name", "@sage/xtrem-restaurant/nodes__address__property__postCode": "Post code", "@sage/xtrem-restaurant/nodes__address__property__restaurant": "Restaurant", "@sage/xtrem-restaurant/nodes__allergen__asyncMutation__asyncExport": "Export", "@sage/xtrem-restaurant/nodes__allergen__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-restaurant/nodes__allergen__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-restaurant/nodes__allergen__node_name": "Allergen", "@sage/xtrem-restaurant/nodes__allergen__property__icon": "Icon", "@sage/xtrem-restaurant/nodes__allergen__property__ingredients": "Ingredients", "@sage/xtrem-restaurant/nodes__allergen__property__name": "Name", "@sage/xtrem-restaurant/nodes__allergen_ingredient__asyncMutation__asyncExport": "Export", "@sage/xtrem-restaurant/nodes__allergen_ingredient__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-restaurant/nodes__allergen_ingredient__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-restaurant/nodes__allergen_ingredient__node_name": "Allergen ingredient", "@sage/xtrem-restaurant/nodes__allergen_ingredient__property__allergen": "Allergen", "@sage/xtrem-restaurant/nodes__allergen_ingredient__property__ingredient": "Ingredient", "@sage/xtrem-restaurant/nodes__country__asyncMutation__asyncExport": "Export", "@sage/xtrem-restaurant/nodes__country__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-restaurant/nodes__country__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-restaurant/nodes__country__node_name": "Country", "@sage/xtrem-restaurant/nodes__country__property__addresses": "Addresses", "@sage/xtrem-restaurant/nodes__country__property__name": "Name", "@sage/xtrem-restaurant/nodes__country__property__phoneCountryCode": "Phone country code", "@sage/xtrem-restaurant/nodes__currency__asyncMutation__asyncExport": "Export", "@sage/xtrem-restaurant/nodes__currency__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-restaurant/nodes__currency__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-restaurant/nodes__currency__node_name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-restaurant/nodes__currency__property__decimalDigits": "Decimal digits", "@sage/xtrem-restaurant/nodes__currency__property__icon": "Icon", "@sage/xtrem-restaurant/nodes__currency__property__name": "Name", "@sage/xtrem-restaurant/nodes__currency__property__symbol": "Symbol", "@sage/xtrem-restaurant/nodes__ingredient__asyncMutation__asyncExport": "Export", "@sage/xtrem-restaurant/nodes__ingredient__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-restaurant/nodes__ingredient__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-restaurant/nodes__ingredient__node_name": "Ingredient", "@sage/xtrem-restaurant/nodes__ingredient__property__allergens": "Allergens", "@sage/xtrem-restaurant/nodes__ingredient__property__name": "Name", "@sage/xtrem-restaurant/nodes__ingredient__property__unit": "Unit", "@sage/xtrem-restaurant/nodes__item__asyncMutation__asyncExport": "Export", "@sage/xtrem-restaurant/nodes__item__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-restaurant/nodes__item__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-restaurant/nodes__item__node_name": "<PERSON><PERSON>", "@sage/xtrem-restaurant/nodes__item__property__category": "Category", "@sage/xtrem-restaurant/nodes__item__property__cookingInstructions": "Cooking instructions", "@sage/xtrem-restaurant/nodes__item__property__description": "Description", "@sage/xtrem-restaurant/nodes__item__property__ingredients": "Ingredients", "@sage/xtrem-restaurant/nodes__item__property__menuItems": "Menu items", "@sage/xtrem-restaurant/nodes__item__property__name": "Name", "@sage/xtrem-restaurant/nodes__item__property__orderItems": "Order items", "@sage/xtrem-restaurant/nodes__item__property__photo": "Photo", "@sage/xtrem-restaurant/nodes__item__property__preparationTime": "Preparation time", "@sage/xtrem-restaurant/nodes__item__property__price": "Price", "@sage/xtrem-restaurant/nodes__item_ingredient__asyncMutation__asyncExport": "Export", "@sage/xtrem-restaurant/nodes__item_ingredient__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-restaurant/nodes__item_ingredient__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-restaurant/nodes__item_ingredient__node_name": "Item ingredient", "@sage/xtrem-restaurant/nodes__item_ingredient__property__ingredient": "Ingredient", "@sage/xtrem-restaurant/nodes__item_ingredient__property__item": "<PERSON><PERSON>", "@sage/xtrem-restaurant/nodes__item_ingredient__property__quantity": "Quantity", "@sage/xtrem-restaurant/nodes__menu_item__asyncMutation__asyncExport": "Export", "@sage/xtrem-restaurant/nodes__menu_item__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-restaurant/nodes__menu_item__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-restaurant/nodes__menu_item__node_name": "Menu item", "@sage/xtrem-restaurant/nodes__menu_item__property__isActive": "Is active", "@sage/xtrem-restaurant/nodes__menu_item__property__item": "<PERSON><PERSON>", "@sage/xtrem-restaurant/nodes__menu_item__property__price": "Price", "@sage/xtrem-restaurant/nodes__menu_item__property__restaurant": "Restaurant", "@sage/xtrem-restaurant/nodes__order__asyncMutation__asyncExport": "Export", "@sage/xtrem-restaurant/nodes__order__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-restaurant/nodes__order__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-restaurant/nodes__order__node_name": "Order", "@sage/xtrem-restaurant/nodes__order__property__items": "Items", "@sage/xtrem-restaurant/nodes__order__property__status": "Status", "@sage/xtrem-restaurant/nodes__order__property__table": "Table", "@sage/xtrem-restaurant/nodes__order__property__totalPrice": "Total price", "@sage/xtrem-restaurant/nodes__order_item__asyncMutation__asyncExport": "Export", "@sage/xtrem-restaurant/nodes__order_item__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-restaurant/nodes__order_item__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-restaurant/nodes__order_item__node_name": "Order item", "@sage/xtrem-restaurant/nodes__order_item__property__menuItem": "Menu item", "@sage/xtrem-restaurant/nodes__order_item__property__notes": "Notes", "@sage/xtrem-restaurant/nodes__order_item__property__order": "Order", "@sage/xtrem-restaurant/nodes__order_item__property__quantity": "Quantity", "@sage/xtrem-restaurant/nodes__order_item__property__status": "Status", "@sage/xtrem-restaurant/nodes__order_item__property__unitPrice": "Unit price", "@sage/xtrem-restaurant/nodes__restaurant__asyncMutation__asyncExport": "Export", "@sage/xtrem-restaurant/nodes__restaurant__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-restaurant/nodes__restaurant__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-restaurant/nodes__restaurant__node_name": "Restaurant", "@sage/xtrem-restaurant/nodes__restaurant__property__address": "Address", "@sage/xtrem-restaurant/nodes__restaurant__property__capacity": "Capacity", "@sage/xtrem-restaurant/nodes__restaurant__property__currency": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-restaurant/nodes__restaurant__property__menuItems": "Menu items", "@sage/xtrem-restaurant/nodes__restaurant__property__name": "Name", "@sage/xtrem-restaurant/nodes__restaurant__property__phoneNumber": "Phone number", "@sage/xtrem-restaurant/nodes__restaurant__property__tableLayoutChart": "Table layout chart", "@sage/xtrem-restaurant/nodes__restaurant__property__tables": "Tables", "@sage/xtrem-restaurant/nodes__table__asyncMutation__asyncExport": "Export", "@sage/xtrem-restaurant/nodes__table__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-restaurant/nodes__table__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-restaurant/nodes__table__node_name": "Table", "@sage/xtrem-restaurant/nodes__table__property__capacity": "Capacity", "@sage/xtrem-restaurant/nodes__table__property__name": "Name", "@sage/xtrem-restaurant/nodes__table__property__orders": "Orders", "@sage/xtrem-restaurant/nodes__table__property__restaurant": "Restaurant", "@sage/xtrem-restaurant/nodes__unit__asyncMutation__asyncExport": "Export", "@sage/xtrem-restaurant/nodes__unit__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-restaurant/nodes__unit__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-restaurant/nodes__unit__node_name": "Unit", "@sage/xtrem-restaurant/nodes__unit__property__description": "Description", "@sage/xtrem-restaurant/nodes__unit__property__numberOfDecimals": "Number of decimals", "@sage/xtrem-restaurant/nodes__unit__property__symbol": "Symbol", "@sage/xtrem-restaurant/package__name": "Sage xtrem restaurant", "@sage/xtrem-restaurant/pages__ingredient____navigationPanel__emptyStateClickableText": "Create a new restaurant ingredient", "@sage/xtrem-restaurant/pages__ingredient____navigationPanel__listItem__title__title": "Name", "@sage/xtrem-restaurant/pages__ingredient____objectTypePlural": "Restaurant Ingredients", "@sage/xtrem-restaurant/pages__ingredient____objectTypeSingular": "Restaurant Ingredient", "@sage/xtrem-restaurant/pages__ingredient____title": "Xtreem Restaurant Ingredient", "@sage/xtrem-restaurant/pages__ingredient__allergens____title": "Allergens", "@sage/xtrem-restaurant/pages__ingredient__headerSection____title": "Ingredient", "@sage/xtrem-restaurant/pages__ingredient__items____columns__title__category": "Category", "@sage/xtrem-restaurant/pages__ingredient__items____columns__title__name": "<PERSON><PERSON>", "@sage/xtrem-restaurant/pages__ingredient__items____columns__title__photo": "Item picture", "@sage/xtrem-restaurant/pages__ingredient__items____columns__title__price": "Price", "@sage/xtrem-restaurant/pages__ingredient__items____emptyStateText": "There are no items containing this ingredient.", "@sage/xtrem-restaurant/pages__ingredient__items____title": "Items containing ingredient", "@sage/xtrem-restaurant/pages__ingredient__itemsSection____title": "Items", "@sage/xtrem-restaurant/pages__ingredient__name____title": "Name", "@sage/xtrem-restaurant/pages__ingredient__save____title": "Save", "@sage/xtrem-restaurant/pages__ingredient__unit____title": "Unit", "@sage/xtrem-restaurant/pages__menu_item____navigationPanel__emptyStateClickableText": "Create a new menu item", "@sage/xtrem-restaurant/pages__menu_item____navigationPanel__listItem__image__title": "Image", "@sage/xtrem-restaurant/pages__menu_item____navigationPanel__listItem__line2__title": "Restaurant", "@sage/xtrem-restaurant/pages__menu_item____navigationPanel__listItem__line2Right__title": "Available", "@sage/xtrem-restaurant/pages__menu_item____navigationPanel__listItem__title__title": "<PERSON><PERSON>", "@sage/xtrem-restaurant/pages__menu_item____objectTypePlural": "Menu items", "@sage/xtrem-restaurant/pages__menu_item____objectTypeSingular": "Menu item", "@sage/xtrem-restaurant/pages__menu_item____title": "Xtreem Restaurant Menu item", "@sage/xtrem-restaurant/pages__menu_item__isActive____title": "Available", "@sage/xtrem-restaurant/pages__menu_item__price____title": "Wholesale price", "@sage/xtrem-restaurant/pages__menu_item__section____title": "Menu item", "@sage/xtrem-restaurant/pages__order____navigationPanel__emptyStateClickableText": "Place a new order", "@sage/xtrem-restaurant/pages__order____navigationPanel__listItem__line2__title": "Table", "@sage/xtrem-restaurant/pages__order____navigationPanel__listItem__line2Right__title": "Total price", "@sage/xtrem-restaurant/pages__order____navigationPanel__listItem__title__title": "Restaurant", "@sage/xtrem-restaurant/pages__order____navigationPanel__listItem__titleRight__title": "Order status", "@sage/xtrem-restaurant/pages__order____objectTypePlural": "Orders", "@sage/xtrem-restaurant/pages__order____objectTypeSingular": "Order", "@sage/xtrem-restaurant/pages__order____title": "Xtreem Restaurant Orders", "@sage/xtrem-restaurant/pages__order__headerSection____title": "Details", "@sage/xtrem-restaurant/pages__order__orderItemsTab____title": "Order content", "@sage/xtrem-restaurant/pages__order__orders____columns__columns__menuItem__item__name__title": "Photo", "@sage/xtrem-restaurant/pages__order__orders____columns__columns__menuItem__item__name__title__2": "Category", "@sage/xtrem-restaurant/pages__order__orders____columns__columns__menuItem__item__name__title__3": "<PERSON><PERSON>", "@sage/xtrem-restaurant/pages__order__orders____columns__columns__menuItem__item__name__title__4": "Price", "@sage/xtrem-restaurant/pages__order__orders____columns__columns__menuItem__item__name__title__5": "Description", "@sage/xtrem-restaurant/pages__order__orders____columns__columns__menuItem__item__name__title__6": "Preparation time", "@sage/xtrem-restaurant/pages__order__orders____columns__title___createUser__displayName": "Placed by", "@sage/xtrem-restaurant/pages__order__orders____columns__title__menuItem__isActive": "Stock", "@sage/xtrem-restaurant/pages__order__orders____columns__title__menuItem__item__category": "Category", "@sage/xtrem-restaurant/pages__order__orders____columns__title__menuItem__item__name": "Menu item", "@sage/xtrem-restaurant/pages__order__orders____columns__title__menuItem__item__preparationTime": "Preparation time", "@sage/xtrem-restaurant/pages__order__orders____columns__title__quantity": "Quantity", "@sage/xtrem-restaurant/pages__order__orders____columns__title__status": "Item status", "@sage/xtrem-restaurant/pages__order__orders____columns__title__unitPrice": "Unit price", "@sage/xtrem-restaurant/pages__order__orders____dropdownActions__title": "View details", "@sage/xtrem-restaurant/pages__order__orders____emptyStateText": "Begin adding items to your order.", "@sage/xtrem-restaurant/pages__order__orders____mobileCard__line2__prefix": "Quantity", "@sage/xtrem-restaurant/pages__order__orders____mobileCard__line2__title": "Quantity", "@sage/xtrem-restaurant/pages__order__orders____mobileCard__line2Right__title": "Price", "@sage/xtrem-restaurant/pages__order__orders____mobileCard__line3Right__title": "Stock", "@sage/xtrem-restaurant/pages__order__orders____mobileCard__title__title": "<PERSON><PERSON>", "@sage/xtrem-restaurant/pages__order__orders____mobileCard__titleRight__title": "Status", "@sage/xtrem-restaurant/pages__order__orders____sidebar__headerQuickActions__title": "Remove item from order", "@sage/xtrem-restaurant/pages__order__orders____sidebar__title": "Order item", "@sage/xtrem-restaurant/pages__order__orders____title": "Order items", "@sage/xtrem-restaurant/pages__order__restaurant____columns__title__address__city": "City", "@sage/xtrem-restaurant/pages__order__restaurant____columns__title__address__country__name": "Country", "@sage/xtrem-restaurant/pages__order__restaurant____columns__title__capacity": "Capacity", "@sage/xtrem-restaurant/pages__order__restaurant____columns__title__currency__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-restaurant/pages__order__restaurant____columns__title__name": "Restaurant", "@sage/xtrem-restaurant/pages__order__restaurant____columns__title__phoneNumber": "Phone number", "@sage/xtrem-restaurant/pages__order__restaurant____title": "Restaurant", "@sage/xtrem-restaurant/pages__order__sidePanelCookingInstructions____title": "Cooking instructions", "@sage/xtrem-restaurant/pages__order__sidePanelDescription____title": "Description", "@sage/xtrem-restaurant/pages__order__sidePanelIngredientsGrid____columns__title__ingredient__name": "Ingredient", "@sage/xtrem-restaurant/pages__order__sidePanelIngredientsGrid____columns__title__quantity": "Quantity", "@sage/xtrem-restaurant/pages__order__sidePanelIngredientsGrid____emptyStateText": "The ingredients are not available.", "@sage/xtrem-restaurant/pages__order__sidePanelIngredientsGrid____title": "Main ingredients", "@sage/xtrem-restaurant/pages__order__sidePanelNotes____title": "Notes", "@sage/xtrem-restaurant/pages__order__sidePanelOrderItemStatus____title": "Item status", "@sage/xtrem-restaurant/pages__order__status____title": "Order status", "@sage/xtrem-restaurant/pages__order__table____columns__title__capacity": "Capacity", "@sage/xtrem-restaurant/pages__order__table____columns__title__name": "Table name", "@sage/xtrem-restaurant/pages__order__table____columns__title__restaurant__name": "Restaurant", "@sage/xtrem-restaurant/pages__order__table____title": "Table", "@sage/xtrem-restaurant/pages__order__totalPrice____title": "Total price", "@sage/xtrem-restaurant/pages__restaurant____navigationPanel__emptyStateClickableText": "Create a new restaurant", "@sage/xtrem-restaurant/pages__restaurant____navigationPanel__listItem__line2__title": "City", "@sage/xtrem-restaurant/pages__restaurant____navigationPanel__listItem__line2Right__title": "Country", "@sage/xtrem-restaurant/pages__restaurant____navigationPanel__listItem__title__title": "Name", "@sage/xtrem-restaurant/pages__restaurant____navigationPanel__listItem__titleRight__title": "Telephone number", "@sage/xtrem-restaurant/pages__restaurant____objectTypePlural": "Restaurants", "@sage/xtrem-restaurant/pages__restaurant____objectTypeSingular": "Restaurant", "@sage/xtrem-restaurant/pages__restaurant____title": "Xtreem Restaurant", "@sage/xtrem-restaurant/pages__restaurant__address____columns__columns__country__name__title": "Country", "@sage/xtrem-restaurant/pages__restaurant__address____columns__columns__country__name__title__2": "Country code", "@sage/xtrem-restaurant/pages__restaurant__address____columns__title__city": "City", "@sage/xtrem-restaurant/pages__restaurant__address____columns__title__country__name": "Country", "@sage/xtrem-restaurant/pages__restaurant__address____columns__title__line1": "Address", "@sage/xtrem-restaurant/pages__restaurant__address____columns__title__line2": "Address Line 2", "@sage/xtrem-restaurant/pages__restaurant__address____columns__title__name": "Name", "@sage/xtrem-restaurant/pages__restaurant__address____dropdownActions__title": "Edit", "@sage/xtrem-restaurant/pages__restaurant__address____dropdownActions__title__2": "Read-only", "@sage/xtrem-restaurant/pages__restaurant__address____title": "Location", "@sage/xtrem-restaurant/pages__restaurant__avgMenu____title": "Average menu price", "@sage/xtrem-restaurant/pages__restaurant__capacity____title": "Capacity", "@sage/xtrem-restaurant/pages__restaurant__currency____columns__title__decimalDigits": "Decimal digits", "@sage/xtrem-restaurant/pages__restaurant__currency____columns__title__name": "Name", "@sage/xtrem-restaurant/pages__restaurant__currency____columns__title__symbol": "Symbol", "@sage/xtrem-restaurant/pages__restaurant__currency____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-restaurant/pages__restaurant__field____title": "Table layout", "@sage/xtrem-restaurant/pages__restaurant__headerSection____title": "Details", "@sage/xtrem-restaurant/pages__restaurant__infoTab____title": "Information", "@sage/xtrem-restaurant/pages__restaurant__menuItems____columns__columns__item__name__title": "Photo", "@sage/xtrem-restaurant/pages__restaurant__menuItems____columns__columns__item__name__title__2": "<PERSON><PERSON>", "@sage/xtrem-restaurant/pages__restaurant__menuItems____columns__columns__item__name__title__3": "Description", "@sage/xtrem-restaurant/pages__restaurant__menuItems____columns__columns__item__name__title__4": "Price", "@sage/xtrem-restaurant/pages__restaurant__menuItems____columns__columns__item__name__title__5": "Preparation time", "@sage/xtrem-restaurant/pages__restaurant__menuItems____columns__title__isActive": "Available", "@sage/xtrem-restaurant/pages__restaurant__menuItems____columns__title__item__category": "Category", "@sage/xtrem-restaurant/pages__restaurant__menuItems____columns__title__item__description": "Menu item description", "@sage/xtrem-restaurant/pages__restaurant__menuItems____columns__title__item__name": "Menu item", "@sage/xtrem-restaurant/pages__restaurant__menuItems____columns__title__item__preparationTime": "Preparation time", "@sage/xtrem-restaurant/pages__restaurant__menuItems____columns__title__item__price": "Wholesale price", "@sage/xtrem-restaurant/pages__restaurant__menuItems____columns__title__price": "Menu price", "@sage/xtrem-restaurant/pages__restaurant__menuItems____emptyStateText": "No menu items to display... yet!", "@sage/xtrem-restaurant/pages__restaurant__menuItems____inlineActions__title": "Open in sidebar", "@sage/xtrem-restaurant/pages__restaurant__menuItems____inlineActions__title__2": "Remove item from menu", "@sage/xtrem-restaurant/pages__restaurant__menuItems____mobileCard__line2Right__title": "Menu price", "@sage/xtrem-restaurant/pages__restaurant__menuItems____mobileCard__title__title": "<PERSON><PERSON>", "@sage/xtrem-restaurant/pages__restaurant__menuItems____mobileCard__titleRight__title": "Available", "@sage/xtrem-restaurant/pages__restaurant__menuItems____optionsMenu__title": "All", "@sage/xtrem-restaurant/pages__restaurant__menuItems____optionsMenu__title__2": "Available", "@sage/xtrem-restaurant/pages__restaurant__menuItems____optionsMenu__title__3": "Unavailable", "@sage/xtrem-restaurant/pages__restaurant__menuItems____sidebar__headerQuickActions__title": "Delete", "@sage/xtrem-restaurant/pages__restaurant__menuItems____title": "Culinary selections", "@sage/xtrem-restaurant/pages__restaurant__menuTab____title": "<PERSON><PERSON>", "@sage/xtrem-restaurant/pages__restaurant__name____title": "Name", "@sage/xtrem-restaurant/pages__restaurant__phoneNumber____title": "Telephone Number", "@sage/xtrem-restaurant/pages__restaurant__sidePanelCookingInstructions____title": "Cooking instructions", "@sage/xtrem-restaurant/pages__restaurant__sidePanelIngredientsGrid____columns__title__ingredient__name": "Ingredient", "@sage/xtrem-restaurant/pages__restaurant__sidePanelIngredientsGrid____columns__title__quantity": "Quantity", "@sage/xtrem-restaurant/pages__restaurant__sidePanelIngredientsGrid____emptyStateText": "The ingredients are not available.", "@sage/xtrem-restaurant/pages__restaurant__sidePanelIngredientsGrid____title": "Main ingredients", "@sage/xtrem-restaurant/pages__restaurant__tableCount____title": "Number of tables", "@sage/xtrem-restaurant/pages__restaurant__tablesGrid____levels__columns__columns__menuItem__item__name__title": "Photo", "@sage/xtrem-restaurant/pages__restaurant__tablesGrid____levels__columns__columns__menuItem__item__name__title__2": "<PERSON><PERSON>", "@sage/xtrem-restaurant/pages__restaurant__tablesGrid____levels__columns__columns__menuItem__item__name__title__3": "Description", "@sage/xtrem-restaurant/pages__restaurant__tablesGrid____levels__columns__columns__menuItem__item__name__title__4": "Price", "@sage/xtrem-restaurant/pages__restaurant__tablesGrid____levels__columns__columns__menuItem__item__name__title__5": "Preparation time", "@sage/xtrem-restaurant/pages__restaurant__tablesGrid____levels__columns__title__capacity": "Table capacity", "@sage/xtrem-restaurant/pages__restaurant__tablesGrid____levels__columns__title__menuItem__item__name": "Menu item", "@sage/xtrem-restaurant/pages__restaurant__tablesGrid____levels__columns__title__name": "Table name", "@sage/xtrem-restaurant/pages__restaurant__tablesGrid____levels__columns__title__notes": "Notes", "@sage/xtrem-restaurant/pages__restaurant__tablesGrid____levels__columns__title__quantity": "Quantity", "@sage/xtrem-restaurant/pages__restaurant__tablesGrid____levels__columns__title__status": "Order status", "@sage/xtrem-restaurant/pages__restaurant__tablesGrid____levels__columns__title__status__2": "Item status", "@sage/xtrem-restaurant/pages__restaurant__tablesGrid____levels__columns__title__totalPrice": "Total order price", "@sage/xtrem-restaurant/pages__restaurant__tablesGrid____levels__columns__title__unitPrice": "Unit rice", "@sage/xtrem-restaurant/pages__restaurant__tablesGrid____levels__emptyStateText": "This restaurant does not have any tables.", "@sage/xtrem-restaurant/pages__restaurant__tablesGrid____levels__emptyStateText__2": "Looks like this table is taking a breather.", "@sage/xtrem-restaurant/pages__restaurant__tablesGrid____levels__emptyStateText__3": "This order is feeling a bit lonely without its culinary delights!", "@sage/xtrem-restaurant/pages__restaurant__tablesGrid____title": "Dining area", "@sage/xtrem-restaurant/pages__restaurant__tablesTab____title": "Tables", "@sage/xtrem-restaurant/pages__restaurant_item____navigationPanel__emptyStateClickableText": "Create a new restaurant item", "@sage/xtrem-restaurant/pages__restaurant_item____navigationPanel__listItem__line2__title": "Price", "@sage/xtrem-restaurant/pages__restaurant_item____navigationPanel__listItem__line2Right__postfix": "min", "@sage/xtrem-restaurant/pages__restaurant_item____navigationPanel__listItem__line2Right__title": "Preparation time", "@sage/xtrem-restaurant/pages__restaurant_item____navigationPanel__listItem__title__title": "Name", "@sage/xtrem-restaurant/pages__restaurant_item____navigationPanel__listItem__titleRight__title": "Category", "@sage/xtrem-restaurant/pages__restaurant_item____objectTypePlural": "Restaurant Items", "@sage/xtrem-restaurant/pages__restaurant_item____objectTypeSingular": "Restaurant Item", "@sage/xtrem-restaurant/pages__restaurant_item____title": "Xtreem Restaurant Item", "@sage/xtrem-restaurant/pages__restaurant_item__cookingInstructions____title": "Instructions", "@sage/xtrem-restaurant/pages__restaurant_item__description____title": "Description", "@sage/xtrem-restaurant/pages__restaurant_item__headerSection____title": "<PERSON><PERSON>", "@sage/xtrem-restaurant/pages__restaurant_item__menuItems____columns__title__isActive": "Available", "@sage/xtrem-restaurant/pages__restaurant_item__menuItems____columns__title__item__category": "Category", "@sage/xtrem-restaurant/pages__restaurant_item__menuItems____columns__title__item__price": "Wholesale price", "@sage/xtrem-restaurant/pages__restaurant_item__menuItems____columns__title__price": "Menu price", "@sage/xtrem-restaurant/pages__restaurant_item__menuItems____columns__title__restaurant__name": "Restaurant name", "@sage/xtrem-restaurant/pages__restaurant_item__menuItems____emptyStateText": "No menu items to display... yet!", "@sage/xtrem-restaurant/pages__restaurant_item__menuItems____optionsMenu__title": "All", "@sage/xtrem-restaurant/pages__restaurant_item__menuItems____optionsMenu__title__2": "Available", "@sage/xtrem-restaurant/pages__restaurant_item__menuItems____optionsMenu__title__3": "Unavailable", "@sage/xtrem-restaurant/pages__restaurant_item__menuItems____title": "Item in restaurants", "@sage/xtrem-restaurant/pages__restaurant_item__menuItemsSection____title": "Item in restaurants", "@sage/xtrem-restaurant/pages__restaurant_item__name____title": "Name", "@sage/xtrem-restaurant/pages__restaurant_item__photo____title": "Photo"}