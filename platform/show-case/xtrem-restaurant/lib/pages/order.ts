import { ExtractEdgesPartial } from '@sage/xtrem-client';
import {
    <PERSON><PERSON><PERSON>cy,
    GraphApi,
    Ingredient,
    Item,
    ItemIngredient,
    MenuItem,
    OrderItem,
    Order as OrderNode,
    OrderStatus,
    Restaurant,
    RestaurantBinding,
    Table,
} from '@sage/xtrem-restaurant-api';
import { User } from '@sage/xtrem-system-api';
import * as ui from '@sage/xtrem-ui';
import { getOrderStatusColor } from '../client-functions/pill-helpers';
import { applicationPages } from '../menu-items/application-pages';

@ui.decorators.page<Order, OrderNode>({
    authorizationCode: 'SHCPRDT',
    module: 'restaurant',
    title: 'Xtreem Restaurant Orders',
    objectTypeSingular: 'Order',
    objectTypePlural: 'Orders',
    mode: 'tabs',
    headerLabel() {
        return this.status;
    },
    idField() {
        return !this.$.recordId ? '' : `${this.restaurant?.value?.name} ${this.table?.value?.name}`;
    },
    node: '@sage/xtrem-restaurant/Order',
    menuItem: applicationPages,
    onLoad() {
        this.$standardCancelAction.isDisabled = true;
        this.$standardSaveAction.isDisabled = true;
    },
    onDirtyStateUpdated(isDirty) {
        this.$standardSaveAction.isDisabled = !isDirty;
        this.$standardCancelAction.isDisabled = !isDirty;
    },
    navigationPanel: {
        orderBy: { status: 1, table: { name: 1 } },
        emptyStateClickableText: 'Place a new order',
        listItem: {
            title: ui.nestedFields.reference<Order, OrderNode, Restaurant>({
                title: 'Restaurant',
                bind: { table: { restaurant: true } },
                valueField: 'name',
            }),
            titleRight: ui.nestedFields.label({ bind: 'status', title: 'Order status' }),
            line2: ui.nestedFields.reference<Order, OrderNode, Table>({
                title: 'Table',
                bind: 'table',
                valueField: 'name',
            }),
            line2Right: ui.nestedFields.numeric({
                bind: 'totalPrice',
                title: 'Total price',
                scale(_, rowValue) {
                    return rowValue?.table?.restaurant?.currency?.decimalDigits ?? 2;
                },
            }),
            decimalPlaces: ui.nestedFields.technical({
                bind: { table: { restaurant: { currency: { decimalDigits: true } } } },
            }),
            // TODO: calculate on server
            // progress: ui.nestedFields.progress({
            //     bind: 'progress',
            //     title: 'Items delivered',
            //     color(value, rowValue) {
            //         return
            //             (rowValue.progress === 0)
            //             ? ui.tokens.colorsSemanticNegative600
            //             : ui.tokens.colorsSemanticPositive600;
            //     },
            // }),
        },
        async optionsMenu(graph) {
            const result = await graph
                .node('@sage/xtrem-restaurant/Restaurant')
                .query(
                    ui.queryUtils.edgesSelector<RestaurantBinding>(
                        {
                            name: true,
                            _id: true,
                        },
                        {
                            first: 100,
                        },
                    ),
                )
                .execute();
            const restaurants = result.edges.map(edge => {
                return {
                    title: `${edge.node.name}`,
                    graphQLFilter: { table: { restaurant: { _id: { _eq: edge.node._id } } } },
                };
            });
            return [
                {
                    title: 'All restaurants',
                    graphQLFilter: {},
                },
                ...restaurants,
            ];
        },
    },
    headerSection() {
        return this.headerSection;
    },
    businessActions() {
        return [this.$standardSaveAction, this.$standardCancelAction];
    },
    createAction() {
        return this.$standardNewAction;
    },
    headerQuickActions() {
        return [this.$standardOpenRecordHistoryAction];
    },
})
export class Order extends ui.Page<GraphApi, OrderNode> {
    @ui.decorators.labelField<Order>({
        bind: 'status',
        title: 'Order status',
        map(value) {
            return ui.localizeEnumMember('@sage/xtrem-restaurant/OrderStatus', value);
        },
        color: ui.tokens.colorsYang100,
        borderColor(status: OrderStatus) {
            return getOrderStatusColor(status);
        },
        backgroundColor(status: OrderStatus) {
            return getOrderStatusColor(status);
        },
    })
    status: ui.fields.Label<OrderStatus, Order>;

    @ui.decorators.section<Order>({
        title: 'Details',
        isTitleHidden: true,
    })
    headerSection: ui.containers.Section<Order>;

    @ui.decorators.block<Order>({
        isTitleHidden: true,
        parent() {
            return this.headerSection;
        },
        width: 'extra-large',
    })
    topHeaderBlock: ui.containers.Block<Order>;

    @ui.decorators.stepSequenceField<Order>({
        parent() {
            return this.topHeaderBlock;
        },
        bind: 'status',
        isFullWidth: true,
        isTitleHidden: true,
        optionType: '@sage/xtrem-restaurant/OrderStatus',
    })
    orderWorkflow: ui.fields.StepSequence<Order>;

    @ui.decorators.referenceField<Order, Restaurant>({
        parent() {
            return this.topHeaderBlock;
        },
        title: 'Restaurant',
        bind: { table: { restaurant: true } },
        node: '@sage/xtrem-restaurant/Restaurant',
        valueField: 'name',
        helperTextField: { address: { city: true } },
        width: 'medium',
        isMandatory: true,
        isReadOnly() {
            return !!this.$.recordId;
        },
        icon: 'shop',
        columns: [
            ui.nestedFields.text({ bind: 'name', title: 'Restaurant' }),
            ui.nestedFields.text({ bind: 'phoneNumber', title: 'Phone number' }),
            ui.nestedFields.numeric({ bind: 'capacity', title: 'Capacity' }),
            ui.nestedFields.text({ bind: { address: { city: true } }, title: 'City' }),
            ui.nestedFields.text({ bind: { address: { country: { name: true } } }, title: 'Country' }),
            ui.nestedFields.reference<Order, Restaurant, Currency>({
                bind: 'currency',
                title: 'Currency',
                valueField: 'name',
            }),
        ],
        onChange() {
            this.table.isDisabled = this.restaurant.value === null;
            this.table.refresh();
            this.orders.value = [];
        },
    })
    restaurant: ui.fields.Reference<Restaurant, Order>;

    @ui.decorators.referenceField<Order, Table>({
        parent() {
            return this.topHeaderBlock;
        },
        title: 'Table',
        node: '@sage/xtrem-restaurant/Table',
        bind: 'table',
        valueField: 'name',
        helperTextField: 'capacity',
        width: 'medium',
        minLookupCharacters: 0,
        isReadOnly() {
            return this.status.value === 'delivered';
        },
        isDisabled() {
            if (this.$.recordId) return false;
            return this.restaurant.value === null;
        },
        isMandatory: true,
        icon: 'contact_card',
        columns: [
            ui.nestedFields.text({ bind: 'name', title: 'Table name' }),
            ui.nestedFields.reference<Order, Table, Restaurant>({
                title: 'Restaurant',
                bind: 'restaurant',
                valueField: 'name',
                icon: 'shop',
                isReadOnly: true,
            }),
            ui.nestedFields.numeric({ bind: 'capacity', title: 'Capacity' }),
        ],
        filter() {
            return this.restaurant.value ? { restaurant: { _id: this.restaurant.value._id } } : undefined;
        },
    })
    table: ui.fields.Reference<Table, Order>;

    @ui.decorators.numericField<Order>({
        parent() {
            return this.topHeaderBlock;
        },
        title: 'Total price',
        width: 'medium',
        isReadOnly: true,
        icon: 'cash',
        scale() {
            return this.decimalDigits.value ?? 2;
        },
    })
    totalPrice: ui.fields.Numeric<Order>;

    @ui.decorators.numericField<Order>({
        isHidden: true,
        bind: { table: { restaurant: { currency: { decimalDigits: true } } } },
    })
    decimalDigits: ui.fields.Numeric<Order>;

    @ui.decorators.section<Order>({
        title: 'Order content',
    })
    orderItemsTab: ui.containers.Section<Order>;

    @ui.decorators.tableField<Order, OrderItem>({
        parent() {
            return this.orderItemsTab;
        },
        title: 'Order items',
        isDisabled() {
            return !this.restaurant.value || !this.table.value;
        },
        emptyStateText: 'Begin adding items to your order.',
        canAddNewLine: true,
        bind: 'items',
        canSelect: false,
        node: '@sage/xtrem-restaurant/OrderItem',
        orderBy: { status: 1, menuItem: { item: { category: 1, name: 1 } } },
        columns: [
            ui.nestedFields.icon({
                title: 'Stock',
                isTransient: true,
                bind: { menuItem: { isActive: true } },

                map(_value, rowValue) {
                    if (!rowValue?.menuItem.isActive && rowValue?.status === 'new') return 'warning';
                    return '';
                },
            }),
            ui.nestedFields.label({
                title: 'Item status',
                bind: 'status',
                map(v) {
                    return ui.localizeEnumMember('@sage/xtrem-restaurant/OrderStatus', v);
                },
                color: ui.tokens.colorsYang100,
                borderColor(value: OrderStatus) {
                    return getOrderStatusColor(value);
                },
                backgroundColor(value: OrderStatus) {
                    return getOrderStatusColor(value);
                },
            }),
            ui.nestedFields.reference<Order, OrderItem, User>({
                node: '@sage/xtrem-system/User',
                bind: '_createUser',
                valueField: 'displayName',
                title: 'Placed by',
                imageField: { photo: true },
                isReadOnly: true,
            }),
            ui.nestedFields.label({
                bind: { menuItem: { item: { category: true } } },
                title: 'Category',
                map(v) {
                    return ui.localizeEnumMember('@sage/xtrem-restaurant/ItemCategory', v);
                },
            }),
            ui.nestedFields.reference<Order, OrderItem, MenuItem>({
                bind: 'menuItem',
                tunnelPage: '@sage/xtrem-restaurant/MenuItem',
                title: 'Menu item',
                valueField: { item: { name: true } },
                imageField: { item: { photo: true } },
                minLookupCharacters: 0,
                isMandatory: true,
                isFullWidth: true,
                isHelperTextHidden: true,
                orderBy: { item: { category: 1, name: 1 } },
                shouldSuggestionsIncludeColumns: true,
                filter() {
                    if (!this.restaurant.value?._id) {
                        return undefined;
                    }
                    return {
                        restaurant: { _id: { _eq: this.restaurant.value._id } },
                        isActive: true,
                    };
                },
                isReadOnly(_value, rowValue) {
                    if (!rowValue?.status) {
                        return false;
                    }

                    return rowValue?.status !== 'new';
                },
                warningMessage(_, rowValue) {
                    if (!rowValue?.menuItem.isActive && rowValue?.status === 'new')
                        return 'This culinary selection may no longer be available.';
                    return '';
                },
                async onChange(_, rowData: ExtractEdgesPartial<OrderItem>) {
                    if (rowData.menuItem) {
                        rowData.unitPrice = rowData.menuItem.price;
                        this.orders.addOrUpdateRecordValue(rowData);
                        await this.bindSidePanel(rowData);
                    } else {
                        this.sidePanelCookingInstructions.value = '';
                        this.sidePanelIngredientsGrid.value = [];
                    }
                },
                columns: [
                    ui.nestedFields.image({ bind: { item: { photo: true } }, title: 'Photo' }),
                    ui.nestedFields.label({
                        bind: { item: { category: true } },
                        title: 'Category',
                        map(v) {
                            return ui.localizeEnumMember('@sage/xtrem-restaurant/ItemCategory', v);
                        },
                    }),
                    ui.nestedFields.text({ bind: { item: { name: true } }, title: 'Item' }),
                    ui.nestedFields.numeric({
                        bind: 'price',
                        title: 'Price',
                        scale() {
                            return this.decimalDigits.value ?? 2;
                        },
                    }),
                    ui.nestedFields.text({ bind: { item: { description: true } }, title: 'Description' }),
                    ui.nestedFields.numeric({ bind: { item: { preparationTime: true } }, title: 'Preparation time' }),
                ],
            }),
            ui.nestedFields.numeric({
                bind: 'unitPrice',
                title: 'Unit price',
                isReadOnly: true,
                scale() {
                    return this.decimalDigits.value ?? 2;
                },
            }),
            ui.nestedFields.numeric({
                bind: 'quantity',
                title: 'Quantity',
                isReadOnly(_value, rowValue) {
                    if (!rowValue?.status) {
                        return false;
                    }

                    return rowValue?.status !== 'new';
                },
            }),
            ui.nestedFields.technical({
                bind: 'notes',
            }),
            ui.nestedFields.technical({
                bind: {
                    menuItem: { item: { cookingInstructions: { value: true } } },
                },
            }),
            ui.nestedFields.technical({
                bind: {
                    menuItem: {
                        isActive: true,
                    },
                },
            }),
            ui.nestedFields.numeric({
                bind: {
                    menuItem: {
                        item: {
                            preparationTime: true,
                        },
                    },
                },
                title: 'Preparation time',
                isFullWidth: true,
                isReadOnly: true,
                isHiddenOnMainField: true,
                icon: 'clock',
            }),
        ],
        mobileCard: {
            image: ui.nestedFields.image({
                bind: {
                    menuItem: {
                        item: {
                            photo: true,
                        },
                    },
                },
            }),
            title: ui.nestedFields.reference<Order, OrderItem, Item>({
                bind: { menuItem: { item: true } },
                node: '@sage/xtrem-restaurant/Item',
                valueField: 'name',
                title: 'Item',
            }),
            titleRight: ui.nestedFields.label({
                bind: 'status',
                title: 'Status',
                color: ui.tokens.colorsYang100,
                borderColor(value: OrderStatus) {
                    return getOrderStatusColor(value);
                },
                backgroundColor(value: OrderStatus) {
                    return getOrderStatusColor(value);
                },
                map(v) {
                    return ui.localizeEnumMember('@sage/xtrem-restaurant/OrderStatus', v);
                },
            }),
            line2: ui.nestedFields.numeric({
                bind: 'quantity',
                title: 'Quantity',
                prefix: 'Quantity',
            }),
            line2Right: ui.nestedFields.numeric({
                bind: 'unitPrice',
                title: 'Price',
                scale() {
                    return this.decimalDigits.value ?? 2;
                },
            }),
            line3: ui.nestedFields.text({
                bind: {
                    menuItem: {
                        item: {
                            description: true,
                        },
                    },
                },
            }),
            line3Right: ui.nestedFields.icon({
                title: 'Stock',
                isTransient: true,
                bind: { menuItem: { isActive: true } },

                map(_value, rowValue) {
                    if (!rowValue?.menuItem.isActive && rowValue?.status === 'new') return 'warning';
                    return 'tick_circle';
                },
            }),
        },
        dropdownActions: [
            {
                icon: 'box_arrow_left',
                title: 'View details',
                async onClick(rowId) {
                    this.orders.openSidebar(rowId);
                },
            },
        ],
        sidebar: {
            // TODO: as an improvement, type the recordvalue to be extractedgees of orderitem matteo
            title: 'Order item',
            headerQuickActions: [
                {
                    icon: 'bin',
                    title: 'Remove item from order',
                    isDestructive: true,
                    isDisabled(_, rowItem) {
                        if (!rowItem?.status) {
                            return false;
                        }

                        return rowItem?.status !== 'new';
                    },
                    async onClick(recordId, rowItem) {
                        await this.$.dialog.confirmation(
                            'warn',
                            'Are you sure?',
                            rowItem.menuItem // TODO: item can be null?
                                ? `Do you want to remove ${rowItem.menuItem?.item?.name} from this order?`
                                : `Do you want to remove this culinary selection from this order?`,
                        );
                        this.orders.removeRecord(recordId);
                    },
                },
            ],
            async onRecordOpened(_, recordValue) {
                if (recordValue) this.bindSidePanel(recordValue as unknown as ExtractEdgesPartial<OrderItem>);
            },
            async onRecordConfirmed(_, recordValue) {
                if (recordValue) {
                    recordValue.notes = this.sidePanelNotes.value ?? '';
                    recordValue.status = this.sidePanelOrderItemStatus.value as OrderStatus;
                    this.orders.addOrUpdateRecordValue(recordValue as unknown as ExtractEdgesPartial<OrderItem>);
                }
            },
            layout() {
                return {
                    overview: {
                        title: 'Overview',
                        blocks: {
                            statusAndDetails: {
                                title: 'Status and details',
                                fields: [
                                    this.sidePanelOrderItemStatus,
                                    'menuItem',
                                    this.sidePanelDescription,
                                    'quantity',
                                    'unitPrice',
                                    this.sidePanelNotes,
                                ],
                            },
                        },
                    },
                    preparation: {
                        title: 'Preparation',
                        blocks: {
                            ingredients: {
                                title: 'Basic information',
                                fields: [
                                    this.sidePanelIngredientsGrid,
                                    this.sidePanelCookingInstructions,
                                    {
                                        menuItem: {
                                            item: {
                                                preparationTime: true,
                                            },
                                        },
                                    },
                                ],
                            },
                        },
                    },
                };
            },
        },
    })
    orders: ui.fields.Table<OrderItem, Order>;

    @ui.decorators.staticContentField({
        isTransient: true,
        title: 'Description',
        isFullWidth: true,
        maxVisibleLines: 1,
        bind: {
            menuItem: {
                item: {
                    description: { value: true },
                },
            },
        },
    })
    sidePanelDescription: ui.fields.StaticContent<Order>;

    @ui.decorators.richTextField<Order>({
        isTransient: true,
        title: 'Notes',
        isFullWidth: true,
        bind: 'notes',
        height: '200px',
        capabilities: ['bold', 'underline', 'history', 'fontColor', 'italic', 'lists', 'fontBackgroundColor'],
    })
    sidePanelNotes: ui.fields.RichText<Order>;

    @ui.decorators.toggleField<Order>({
        isTransient: true,
        isFullWidth: true,
        bind: 'status',
        optionType: '@sage/xtrem-restaurant/OrderStatus',
        size: 'large',
        title: 'Item status',
        isTitleHidden: true,
        mapIcon(status: OrderStatus) {
            switch (status) {
                case 'delivered':
                    return 'tick_circle';
                case 'preparing':
                    return 'in_progress';
                default:
                    return 'clock';
            }
        },
    })
    sidePanelOrderItemStatus: ui.fields.Toggle<OrderStatus, Order>;

    @ui.decorators.tableSummaryField<Order, ItemIngredient>({
        title: 'Main ingredients',
        isTransient: true,
        node: '@sage/xtrem-restaurant/ItemIngredient',
        emptyStateText: 'The ingredients are not available.',
        columns: [
            ui.nestedFields.numeric({
                bind: 'quantity',
                title: 'Quantity',
            }),
            ui.nestedFields.reference<Order, ItemIngredient, Ingredient>({
                bind: 'ingredient',
                node: '@sage/xtrem-restaurant/Ingredient',
                valueField: 'name',
                title: 'Ingredient',
                helperTextField: {
                    unit: {
                        description: true,
                    },
                },
            }),
        ],
    })
    sidePanelIngredientsGrid: ui.fields.TableSummary<ItemIngredient, Order>;

    @ui.decorators.textAreaField<Order>({
        isTransient: true,
        title: 'Cooking instructions',
        isFullWidth: true,
        bind: { menuItem: { item: { cookingInstructions: { value: true } } } },
        isReadOnly: true,
    })
    sidePanelCookingInstructions: ui.fields.TextArea<Order>;

    private async bindSidePanel(rowValue: ExtractEdgesPartial<OrderItem>) {
        const result = await this.$.graph
            .node('@sage/xtrem-restaurant/ItemIngredient')
            .query(
                ui.queryUtils.edgesSelector(
                    {
                        _id: true,
                        quantity: true,
                        item: {
                            name: true,
                        },
                        ingredient: {
                            name: true,
                            unit: {
                                description: true,
                                numberOfDecimals: true,
                                symbol: true,
                            },
                        },
                    },
                    {
                        filter: {
                            item: { _id: rowValue.menuItem?.item?._id },
                        },
                    },
                ),
            )
            .execute();

        this.sidePanelDescription.value = rowValue.menuItem?.item?.description ?? null;
        this.sidePanelNotes.value = rowValue.notes ?? null ?? '';
        this.sidePanelIngredientsGrid.value = [...result.edges.map(e => e.node)];
        this.sidePanelCookingInstructions.value = rowValue.menuItem?.item?.cookingInstructions?.value ?? null;

        if (!rowValue?.status) {
            this.sidePanelNotes.isReadOnly = false;
        } else {
            this.sidePanelNotes.isReadOnly = rowValue?.status !== 'new';
            this.sidePanelOrderItemStatus.value = rowValue?.status;
        }
    }
}
