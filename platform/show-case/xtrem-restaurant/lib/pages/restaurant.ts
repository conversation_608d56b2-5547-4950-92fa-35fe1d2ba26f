import {
    Address,
    Country,
    Currency,
    GraphApi,
    Ingredient,
    Item,
    ItemIngredient,
    MenuItem,
    Order,
    OrderItem,
    Table,
    Restaurant as xtremRestaurantNode,
} from '@sage/xtrem-restaurant-api';
import * as ui from '@sage/xtrem-ui';
import { applicationPages } from '../menu-items/application-pages';

// TODO: Localization, error handling
@ui.decorators.page<Restaurant, xtremRestaurantNode>({
    authorizationCode: 'SHCPRDT',
    module: 'restaurant',
    title: 'Xtreem Restaurant',
    objectTypeSingular: 'Restaurant',
    objectTypePlural: 'Restaurants',
    mode: 'tabs',
    idField() {
        return this.name;
    },
    node: '@sage/xtrem-restaurant/Restaurant',
    menuItem: applicationPages,

    onLoad() {
        this.$standardCancelAction.isDisabled = true;
        this.$standardSaveAction.isDisabled = true;
    },

    onDirtyStateUpdated(isDirty) {
        this.$standardSaveAction.isDisabled = !isDirty;
        this.$standardCancelAction.isDisabled = !isDirty;

        if (!isDirty) {
            this.avgMenu.icon = undefined;
        }
    },

    navigationPanel: {
        orderBy: { name: 1 },
        emptyStateClickableText: 'Create a new restaurant',
        listItem: {
            title: ui.nestedFields.text({ bind: 'name', title: 'Name' }),
            titleRight: ui.nestedFields.text({
                bind: 'phoneNumber',
                title: 'Telephone number',
                prefix(_value, rowData) {
                    return rowData?.address?.country?.phoneCountryCode
                        ? `+${rowData?.address?.country?.phoneCountryCode}`
                        : '';
                },
            }),
            line2: ui.nestedFields.reference<Restaurant, xtremRestaurantNode, Address>({
                bind: 'address',
                node: '@sage/xtrem-restaurant/Address',
                title: 'City',
                valueField: 'city',
            }),
            line2Right: ui.nestedFields.reference<Restaurant, xtremRestaurantNode, Country>({
                bind: {
                    address: {
                        country: true,
                    },
                },
                node: '@sage/xtrem-restaurant/Country',
                valueField: 'name',
                title: 'Country',
            }),
            countryCode: ui.nestedFields.technical({
                bind: {
                    address: {
                        country: {
                            phoneCountryCode: true,
                        },
                    },
                },
            }),
            countryId: ui.nestedFields.technical({
                bind: {
                    address: {
                        country: {
                            _id: true,
                        },
                    },
                },
            }),
        },
        async optionsMenu(graph) {
            const result = await graph
                .node('@sage/xtrem-restaurant/Country')
                .query({ edges: { node: { name: true, _id: true } } })
                .execute();
            const countries = result.edges.map(edge => {
                return {
                    title: `${edge.node.name}`,
                    graphQLFilter: { address: { country: { _id: { _eq: edge.node._id } } } },
                };
            });
            return [
                {
                    title: 'All countries',
                    graphQLFilter: {},
                },
                ...countries,
            ];
        },
    },
    headerSection() {
        return this.headerSection;
    },
    businessActions() {
        return [this.$standardSaveAction, this.$standardCancelAction];
    },
    createAction() {
        return this.$standardNewAction;
    },
    headerQuickActions() {
        return [this.$standardOpenRecordHistoryAction];
    },
    headerDropDownActions() {
        return [this.$standardDeleteAction];
    },
})
export class Restaurant extends ui.Page<GraphApi, xtremRestaurantNode> {
    @ui.decorators.section<Restaurant>({
        title: 'Details',
        isTitleHidden: true,
    })
    headerSection: ui.containers.Section<Restaurant>;

    @ui.decorators.block<Restaurant>({
        isTitleHidden: true,
        parent() {
            return this.headerSection;
        },
        width: 'extra-large',
    })
    topHeaderBlock: ui.containers.Block<Restaurant>;

    @ui.decorators.textField<Restaurant>({
        parent() {
            return this.topHeaderBlock;
        },
        title: 'Name',
        width: 'medium',
        isReadOnly() {
            return !!this.$.recordId;
        },
        icon: 'shop',
        isMandatory: true,
    })
    name: ui.fields.Text<Restaurant>;

    @ui.decorators.textField<Restaurant>({
        parent() {
            return this.topHeaderBlock;
        },
        title: 'Telephone Number',
        width: 'medium',
        isReadOnly: false,
        icon: 'phone',
        prefix() {
            return this.address.value?.country.phoneCountryCode ?? '';
        },
        isMandatory: true,
    })
    phoneNumber: ui.fields.Text<Restaurant>;

    @ui.decorators.numericField<Restaurant>({
        parent() {
            return this.topHeaderBlock;
        },
        title: 'Capacity',
        width: 'small',
        icon: 'people',
        isMandatory: true,
    })
    capacity: ui.fields.Numeric<Restaurant>;

    @ui.decorators.tile<Restaurant>({
        parent() {
            return this.headerSection;
        },
        isHidden() {
            return !this.$.recordId;
        },
    })
    tileContainer: ui.containers.Tile<Restaurant>;

    // TODO: BUG - Not calculated when no tables (or maybe new). To investigate.
    @ui.decorators.aggregateField<Restaurant>({
        parent() {
            return this.tileContainer;
        },
        bind: 'menuItems',
        title: 'Average menu price',
        width: 'medium',
        aggregateOn: 'price',
        aggregationMethod: 'avg',
        filter: {
            isActive: { _eq: true },
        },
        scale() {
            return this.currency.value?.decimalDigits ?? 2;
        },
        postfix() {
            return this.currency.value?.symbol ?? '';
        },
    })
    avgMenu: ui.fields.Aggregate<Restaurant>;

    @ui.decorators.countField<Restaurant>({
        parent() {
            return this.tileContainer;
        },
        bind: 'tables',
        title: 'Number of tables',
        width: 'medium',
    })
    tableCount: ui.fields.Count<Restaurant>;

    @ui.decorators.section<Restaurant>({
        title: 'Information',
    })
    infoTab: ui.containers.Section<Restaurant>;

    @ui.decorators.vitalPodField<Restaurant, Address>({
        parent() {
            return this.infoTab;
        },
        node: '@sage/xtrem-restaurant/Address',
        title: 'Location',
        width: 'small',
        isReadOnly() {
            return !!this.$.recordId;
        },
        columns: [
            ui.nestedFields.text({ bind: 'name', title: 'Name', width: 'extra-large', isMandatory: true }),
            ui.nestedFields.text({ bind: 'line1', title: 'Address', width: 'small', isMandatory: true }),
            ui.nestedFields.text({ bind: 'line2', title: 'Address Line 2', width: 'small', isMandatory: false }),
            ui.nestedFields.text({ bind: 'city', title: 'City', width: 'small', isMandatory: true }),
            ui.nestedFields.reference<Restaurant, Address, Country>({
                bind: 'country',
                node: '@sage/xtrem-restaurant/Country',
                valueField: 'name',
                title: 'Country',
                width: 'small',
                minLookupCharacters: 0,
                isReadOnly() {
                    return !!this.$.recordId;
                },
                columns: [
                    ui.nestedFields.text({ bind: 'name', title: 'Country' }),
                    ui.nestedFields.numeric({
                        bind: 'phoneCountryCode',
                        title: 'Country code',
                        canFilter: false,
                    }),
                ],
                isMandatory: true,
                onChange(_id, value) {
                    this.phoneNumber.prefix = value.country.phoneCountryCode;
                },
            }),
        ],
        dropdownActions: [
            {
                icon: 'edit',
                title: 'Edit',
                async onClick() {
                    this.address.isReadOnly = false;
                },
                isDisabled() {
                    return !this.address.isReadOnly;
                },
            },
            {
                icon: 'tick_thick',
                title: 'Read-only',
                async onClick() {
                    this.address.isReadOnly = true;
                },
                isDisabled() {
                    return this.address.isReadOnly;
                },
            },
        ],
    })
    address: ui.fields.VitalPod<Address, Restaurant>;

    @ui.decorators.podField<Restaurant, Currency>({
        parent() {
            return this.infoTab;
        },
        node: '@sage/xtrem-restaurant/Currency',
        title: 'Currency',
        width: 'small',
        columns: [
            ui.nestedFields.text({ bind: 'name', title: 'Name', width: 'extra-large' }),
            ui.nestedFields.text({ bind: 'symbol', title: 'Symbol', width: 'small' }),
            ui.nestedFields.numeric({ bind: 'decimalDigits', title: 'Decimal digits', width: 'small' }),
        ],
        isReadOnly: false,
        isMandatory: true,
    })
    currency: ui.fields.Pod<Currency, Restaurant>;

    @ui.decorators.block<Restaurant>({
        parent() {
            return this.infoTab;
        },
        isTitleHidden: true,
        width: 'medium',
    })
    tableLayoutBlock: ui.containers.Block<Restaurant>;

    @ui.decorators.visualProcessField<Restaurant>({
        isReadOnly: false,
        bind: 'tableLayoutChart',
        parent() {
            return this.tableLayoutBlock;
        },
        title: 'Table layout',
    })
    field: ui.fields.VisualProcess;

    @ui.decorators.section<Restaurant>({
        title: 'Menu',
    })
    menuTab: ui.containers.Section<Restaurant>;

    // TODO: BUG Footer weird on small screen
    @ui.decorators.tableField<Restaurant, MenuItem>({
        parent() {
            return this.menuTab;
        },
        title: 'Culinary selections',
        node: '@sage/xtrem-restaurant/MenuItem',
        bind: 'menuItems',
        emptyStateText: 'No menu items to display... yet!',
        isReadOnly: false,
        canSelect: false,
        canAddNewLine: true,
        orderBy: { item: { category: -1, name: -1 } },
        optionsMenu: [
            {
                title: 'All',
                graphQLFilter: {},
            },
            {
                title: 'Available',
                graphQLFilter: {
                    isActive: true,
                },
            },
            {
                title: 'Unavailable',
                graphQLFilter: {
                    isActive: false,
                },
            },
        ],
        optionsMenuType: 'toggle',
        onChange() {
            this.flagAvgMenuCount();
        },
        columns: [
            ui.nestedFields.switch({
                bind: 'isActive',
                title: 'Available',
            }),
            ui.nestedFields.label({
                map(v) {
                    return ui.localizeEnumMember('@sage/xtrem-restaurant/ItemCategory', v);
                },
                bind: {
                    item: {
                        category: true,
                    },
                },
                borderColor: ui.tokens.colorsUtilityMajor400,
                backgroundColor: ui.tokens.colorsUtilityMajor400,
                color: ui.tokens.colorsUtilityYang100,
                title: 'Category',
            }),
            ui.nestedFields.reference<Restaurant, MenuItem, Item>({
                // TODO: BUG Lookup doesn't return all values until sorted
                bind: 'item',
                node: '@sage/xtrem-restaurant/Item',
                title: 'Menu item',
                valueField: 'name',
                helperTextField: 'description',
                imageField: 'photo', // TODO: create bug
                minLookupCharacters: 0,
                isMandatory: true,
                isFullWidth: true,
                tunnelPage: '@sage/xtrem-restaurant/MenuItem',
                columns: [
                    ui.nestedFields.image({ bind: 'photo', title: 'Photo' }),
                    ui.nestedFields.text({ bind: 'name', title: 'Item' }),
                    ui.nestedFields.text({ bind: 'description', title: 'Description' }),
                    ui.nestedFields.numeric({
                        bind: 'price',
                        title: 'Price',
                        scale() {
                            return this.currency.value?.decimalDigits ?? 2;
                        },
                        postfix() {
                            return this.currency.value?.symbol ?? '';
                        },
                    }),
                    ui.nestedFields.numeric({ bind: 'preparationTime', title: 'Preparation time' }),
                    ui.nestedFields.technical({
                        bind: {
                            cookingInstructions: { value: true },
                        },
                    }),
                ],
                onChange(_id, value) {
                    if (value.item) {
                        this.bindPreparationTab(value.item?._id, value.item.cookingInstructions?.value);
                    } else {
                        this.sidePanelIngredientsGrid.value = [];
                        this.sidePanelCookingInstructions.value = '';
                    }
                },
            }),
            ui.nestedFields.text({
                bind: {
                    item: {
                        description: true,
                    },
                },
                title: 'Menu item description',
                isReadOnly: true,
            }),
            ui.nestedFields.numeric({
                bind: 'price',
                title: 'Menu price',
                scale() {
                    return this.currency.value?.decimalDigits ?? 2;
                },
                postfix() {
                    return this.currency.value?.symbol ?? '';
                },
                isMandatory: true,
            }),
            ui.nestedFields.numeric({
                bind: {
                    item: {
                        price: true,
                    },
                },
                title: 'Wholesale price',
                isReadOnly: true,
                scale() {
                    return this.currency.value?.decimalDigits ?? 2;
                },
                postfix() {
                    return this.currency.value?.symbol ?? '';
                },
            }),
            ui.nestedFields.numeric({
                bind: {
                    item: {
                        preparationTime: true,
                    },
                },
                title: 'Preparation time',
                isReadOnly: true,
            }),
            ui.nestedFields.technical({
                bind: {
                    item: {
                        cookingInstructions: { value: true },
                    },
                },
            }),
        ],
        mobileCard: {
            image: ui.nestedFields.image({
                bind: {
                    item: {
                        photo: true,
                    },
                },
            }),
            title: ui.nestedFields.reference<Restaurant, MenuItem, Item>({
                bind: 'item',
                node: '@sage/xtrem-restaurant/Item',
                tunnelPage: '@sage/xtrem-restaurant/RestaurantItem',
                valueField: 'name',
                title: 'Menu Item',
            }),
            titleRight: ui.nestedFields.label({
                bind: 'isActive',
                title: 'Available',
                color: ui.tokens.colorsYang100,
                borderColor(value) {
                    return value ? ui.tokens.colorsSemanticPositive600 : ui.tokens.colorsSemanticNegative600;
                },
                backgroundColor(value) {
                    return value ? ui.tokens.colorsSemanticPositive600 : ui.tokens.colorsSemanticNegative600;
                },
                map(value) {
                    return value ? 'Available' : 'Not Available';
                },
            }),
            line2: ui.nestedFields.text({
                bind: {
                    item: {
                        description: true,
                    },
                },
            }),
            line2Right: ui.nestedFields.numeric({
                bind: 'price',
                title: 'Menu price',
                scale() {
                    return this.currency.value?.decimalDigits ?? 2;
                },
                postfix() {
                    return this.currency.value?.symbol ?? '';
                },
            }),
        },
        inlineActions: [
            {
                icon: 'box_arrow_left',
                title: 'Open in sidebar',
                async onClick(rowId) {
                    this.menuItems.openSidebar(rowId);
                },
            },
            {
                icon: 'bin',
                title: 'Remove item from menu',
                isDestructive: true,
                async onClick(recordId) {
                    await this.$.dialog.confirmation(
                        'warn',
                        'Are you sure?',
                        `Do you want to remove this culinary selection from the restaurant's menu?`,
                    );
                    this.menuItems.removeRecord(recordId);
                    this.flagAvgMenuCount();
                },
            },
        ],
        sidebar: {
            async onRecordOpened(_id, recordValue) {
                if (recordValue?.item) {
                    this.bindPreparationTab(recordValue.item?._id, recordValue.item?.cookingInstructions?.value);
                } // else {}  // TODO: clear both fields
            },
            async onRecordConfirmed(_id, recordValue) {
                if (recordValue) {
                    this.flagAvgMenuCount();
                }
            },
            headerQuickActions: [
                {
                    icon: 'bin',
                    title: 'Delete',
                    isDestructive: true, // TODO: BUG - not dislayed as destructive?
                    async onClick(recordId, rowItem) {
                        await this.$.dialog.confirmation(
                            'warn',
                            'Are you sure?',
                            rowItem.item
                                ? `Do you want to remove ${rowItem.item?.name} from the restaurant's menu?`
                                : `Do you want to remove this culinary selection from the restaurant's menu?`,
                        );
                        this.menuItems.removeRecord(recordId);
                    },
                },
            ],
            layout() {
                return {
                    detailedInformation: {
                        title: 'Detailed information',
                        blocks: {
                            restaurantSpecificItemInfo: {
                                title: 'Pricing & availability',
                                fields: ['isActive', 'item', 'price'],
                            },
                            globalItemInfo: {
                                title: 'Vendor product data',
                                fields: [
                                    {
                                        item: {
                                            category: true,
                                        },
                                    },
                                    {
                                        item: {
                                            price: true,
                                        },
                                    },
                                ],
                            },
                        },
                    },
                    preparation: {
                        title: 'Preparation',
                        blocks: {
                            ingredients: {
                                title: 'Basic information',
                                fields: [
                                    this.sidePanelIngredientsGrid,
                                    this.sidePanelCookingInstructions,
                                    {
                                        item: {
                                            preparationTime: true,
                                        },
                                    },
                                ],
                            },
                        },
                    },
                };
            },
        },
    })
    menuItems: ui.fields.Table<MenuItem, Restaurant>;

    @ui.decorators.textAreaField<Restaurant>({
        isTransient: true,
        title: 'Cooking instructions',
        isFullWidth: true,
        bind: { cookingInstructions: { value: true } },
        isReadOnly: true,
    })
    sidePanelCookingInstructions: ui.fields.TextArea<Restaurant>;

    @ui.decorators.section<Restaurant>({
        title: 'Tables',
    })
    tablesTab: ui.containers.Section<Restaurant>;

    // TODO: Make tables vital
    @ui.decorators.nestedGridField<Restaurant, [Table, Order, OrderItem]>({
        title: 'Dining area',
        bind: 'tables',
        parent() {
            return this.tablesTab;
        },
        canSelect: false,
        levels: [
            {
                node: '@sage/xtrem-restaurant/Table',
                emptyStateText: 'This restaurant does not have any tables.',
                childProperty: 'orders',
                columns: [
                    ui.nestedFields.text({
                        bind: 'name',
                        title: 'Table name',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.numeric({
                        bind: 'capacity',
                        title: 'Table capacity',
                        isReadOnly: true,
                    }),
                ],
            },
            {
                node: '@sage/xtrem-restaurant/Order',
                emptyStateText: 'Looks like this table is taking a breather.',
                childProperty: 'items',
                columns: [
                    ui.nestedFields.label({
                        bind: 'status',
                        title: 'Order status',
                        map(v) {
                            return ui.localizeEnumMember('@sage/xtrem-restaurant/OrderStatus', v);
                        },
                    }),
                    ui.nestedFields.numeric({
                        // TODO: Can we calculate this
                        bind: 'totalPrice',
                        title: 'Total order price',
                        isReadOnly: true,
                        scale() {
                            return this.currency.value?.decimalDigits ?? 2;
                        },
                        postfix() {
                            return this.currency.value?.symbol ?? '';
                        },
                    }),
                ],
            },
            {
                node: '@sage/xtrem-restaurant/OrderItem',
                emptyStateText: 'This order is feeling a bit lonely without its culinary delights!',
                columns: [
                    ui.nestedFields.label({
                        bind: 'status',
                        title: 'Item status',
                        map(v) {
                            return ui.localizeEnumMember('@sage/xtrem-restaurant/OrderStatus', v);
                        },
                    }),
                    ui.nestedFields.reference<Restaurant, OrderItem, MenuItem>({
                        bind: 'menuItem',
                        node: '@sage/xtrem-restaurant/MenuItem',
                        tunnelPage: '@sage/xtrem-restaurant/MenuItem',
                        title: 'Menu item',
                        valueField: { item: { name: true } },
                        helperTextField: { item: { description: true } },
                        imageField: { item: { photo: true } },
                        minLookupCharacters: 0,
                        isMandatory: true,
                        isFullWidth: true,
                        isReadOnly: true,
                        columns: [
                            ui.nestedFields.image({ bind: { item: { photo: true } }, title: 'Photo' }),
                            ui.nestedFields.text({ bind: { item: { name: true } }, title: 'Item' }),
                            ui.nestedFields.text({ bind: { item: { description: true } }, title: 'Description' }),
                            ui.nestedFields.numeric({
                                bind: 'price',
                                title: 'Price',
                                scale() {
                                    return this.currency.value?.decimalDigits ?? 2;
                                },
                                postfix() {
                                    return this.currency.value?.symbol ?? '';
                                },
                            }),
                            ui.nestedFields.numeric({
                                bind: { item: { preparationTime: true } },
                                title: 'Preparation time',
                            }),
                        ],
                    }),
                    ui.nestedFields.numeric({
                        bind: 'unitPrice',
                        title: 'Unit rice',
                        isReadOnly: true,
                        scale() {
                            return this.currency.value?.decimalDigits ?? 2;
                        },
                        postfix() {
                            return this.currency.value?.symbol ?? '';
                        },
                    }),
                    ui.nestedFields.numeric({
                        bind: 'quantity',
                        title: 'Quantity',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.text({
                        bind: 'notes',
                        title: 'Notes',
                        isReadOnly: true,
                    }),
                ],
            },
        ],
    })
    tablesGrid: ui.fields.NestedGrid<[Table, Order, OrderItem], Restaurant>;

    // TODO: Empty state?
    @ui.decorators.tableSummaryField<Restaurant, ItemIngredient>({
        title: 'Main ingredients',
        isTransient: true,
        node: '@sage/xtrem-restaurant/ItemIngredient',
        emptyStateText: 'The ingredients are not available.',
        columns: [
            ui.nestedFields.numeric({
                bind: 'quantity',
                title: 'Quantity',
            }),
            ui.nestedFields.reference<Restaurant, ItemIngredient, Ingredient>({
                bind: 'ingredient',
                node: '@sage/xtrem-restaurant/Ingredient',
                valueField: 'name',
                title: 'Ingredient',
                helperTextField: {
                    unit: {
                        description: true,
                    },
                },
            }),
        ],
    })
    sidePanelIngredientsGrid: ui.fields.TableSummary<ItemIngredient, Restaurant>;

    private async bindPreparationTab(itemId: string, cookingInstructions: string) {
        const result = await this.$.graph
            .node('@sage/xtrem-restaurant/ItemIngredient')
            .query(
                ui.queryUtils.edgesSelector(
                    {
                        _id: true,
                        quantity: true,
                        item: {
                            name: true,
                        },
                        ingredient: {
                            name: true,
                            unit: {
                                description: true,
                                numberOfDecimals: true,
                                symbol: true,
                            },
                        },
                    },
                    {
                        filter: {
                            item: { _id: itemId },
                        },
                    },
                ),
            )
            .execute();

        this.sidePanelIngredientsGrid.value = [...result.edges.map(e => e.node)];
        this.sidePanelCookingInstructions.value = cookingInstructions;
    }

    private flagAvgMenuCount() {
        this.avgMenu.icon = 'draft';
        this.avgMenu.iconColor = ui.tokens.colorsSemanticCaution500;
    }
}
