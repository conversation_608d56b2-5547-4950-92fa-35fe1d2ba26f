import { GraphApi, Ingredient as IngredientNode, Unit, AllergenIngredient, Item } from '@sage/xtrem-restaurant-api';
import * as ui from '@sage/xtrem-ui';
import { applicationPages } from '../menu-items/application-pages';
import { extractEdges } from '@sage/xtrem-client';

@ui.decorators.page<Ingredient, IngredientNode>({
    authorizationCode: 'SHCPRDT',
    module: 'restaurant',
    title: 'Xtreem Restaurant Ingredient',
    objectTypeSingular: 'Restaurant Ingredient',
    objectTypePlural: 'Restaurant Ingredients',
    mode: 'tabs',
    idField() {
        return this.name;
    },
    node: '@sage/xtrem-restaurant/Ingredient',
    menuItem: applicationPages,
    async onLoad() {
        const allAllergens = extractEdges(
            await this.$.graph
                .node('@sage/xtrem-restaurant/AllergenIngredient')
                .aggregate.query(
                    ui.queryUtils.edgesSelector({
                        group: { allergen: { name: { _by: 'value' }, _id: { _by: 'value' } } },
                        values: {
                            _id: { distinctCount: true },
                        },
                    }),
                )
                .execute(),
        );
        this.allAllergens = allAllergens.map(a => ({ name: a.group.allergen.name, _id: a.group.allergen._id }));
        this.allergens.options = allAllergens.map(a => a.group.allergen.name);
        if (this.$.recordId) {
            const allergens = await this.$.graph
                .node('@sage/xtrem-restaurant/AllergenIngredient')
                .query(
                    ui.queryUtils.edgesSelector<
                        AllergenIngredient,
                        { allergen: { name: true }; ingredient: { name: true } }
                    >(
                        { allergen: { name: true }, ingredient: { name: true } },
                        { filter: { ingredient: { _id: { _eq: this.$.recordId } } }, first: 500 },
                    ),
                )
                .execute();
            this.allergens.value = allergens.edges.map(a => a.node.allergen.name);

            const items = await this.$.graph
                .node('@sage/xtrem-restaurant/Item')
                .query(
                    ui.queryUtils.edgesSelector<
                        Item,
                        {
                            _id: true;
                            name: true;
                            photo: { value: true };
                            description: true;
                            category: true;
                            price: true;
                        }
                    >(
                        {
                            _id: true,
                            name: true,
                            photo: { value: true },
                            description: true,
                            category: true,
                            price: true,
                        },
                        {
                            filter: {
                                ingredients: { _atLeast: 1, ingredient: { _id: { _eq: this.$.recordId } } },
                            },
                            first: 500,
                        },
                    ),
                )
                .execute();
            this.items.value = items.edges.map(i => i.node);
        } else {
            this.allergens.value = [];
            this.items.value = [];
        }
        this.$standardSaveAction.isDisabled = true;
        this.$standardCancelAction.isDisabled = true;
    },
    onDirtyStateUpdated(isDirty) {
        this.$standardSaveAction.isDisabled = !isDirty;
        this.$standardCancelAction.isDisabled = !isDirty;
    },
    navigationPanel: {
        orderBy: { name: 1 },
        emptyStateClickableText: 'Create a new restaurant ingredient',
        listItem: {
            title: ui.nestedFields.text({ bind: 'name', title: 'Name' }),
        },
    },
    businessActions() {
        return [this.save, this.$standardCancelAction];
    },
    createAction() {
        return this.$standardNewAction;
    },
    headerQuickActions() {
        return [this.$standardOpenRecordHistoryAction];
    },
    headerDropDownActions() {
        return [this.$standardDeleteAction];
    },
})
export class Ingredient extends ui.Page<GraphApi, IngredientNode> {
    protected allAllergens: { name: string; _id: string }[] = [];

    protected saveAllergens(): Promise<{
        _id: string;
    }> {
        return this.$.graph
            .node('@sage/xtrem-restaurant/Ingredient')
            .update(
                { _id: true },
                {
                    data: {
                        _id: this.$.recordId,
                        allergens: (this.allergens.value ?? []).map(a => ({
                            allergen: this.allAllergens.find(allergen => allergen.name === a)!._id,
                        })),
                    },
                },
            )
            .execute();
    }

    @ui.decorators.pageAction<Ingredient>({
        title: 'Save',
        async onClick() {
            try {
                if (this.$.recordId) {
                    await this.$.graph.update();
                    await this.saveAllergens();
                    this.$.showToast('Ingredient updated', { type: 'success' });
                } else {
                    await this.$.graph.create();
                    await this.saveAllergens();
                    this.$.showToast('Ingredient created', { type: 'success' });
                }
                this.$.setPageClean();
                this.$.finish();
            } catch (err) {
                this.$.showToast('Something went wrong', { type: 'error' });
            }
        },
    })
    save: ui.PageAction<Ingredient>;

    @ui.decorators.section<Ingredient>({
        title: 'Ingredient',
        isTitleHidden: true,
    })
    headerSection: ui.containers.Section<Ingredient>;

    @ui.decorators.block<Ingredient>({
        isTitleHidden: true,
        parent() {
            return this.headerSection;
        },
        width: 'extra-large',
    })
    topHeaderBlock: ui.containers.Block<Ingredient>;

    @ui.decorators.textField<Ingredient>({
        parent() {
            return this.topHeaderBlock;
        },
        title: 'Name',
        isFullWidth: true,
        isMandatory: true,
    })
    name: ui.fields.Text<Ingredient>;

    @ui.decorators.referenceField<Ingredient, Unit>({
        parent() {
            return this.topHeaderBlock;
        },
        isFullWidth: true,
        valueField: 'symbol',
        title: 'Unit',
        minLookupCharacters: 0,
    })
    unit: ui.fields.Reference<Unit, Ingredient>;

    @ui.decorators.multiDropdownField<Ingredient>({
        parent() {
            return this.topHeaderBlock;
        },
        isTransient: true,
        isFullWidth: true,
        title: 'Allergens',
    })
    allergens: ui.fields.MultiDropdown<Ingredient>;

    @ui.decorators.section<Ingredient>({
        title: 'Items',
        isTitleHidden: true,
    })
    itemsSection: ui.containers.Section<Ingredient>;

    @ui.decorators.tableField<Ingredient, Item>({
        parent() {
            return this.itemsSection;
        },
        title: 'Items containing ingredient',
        isTransient: true,
        emptyStateText: 'There are no items containing this ingredient.',
        isReadOnly: true,
        canSelect: false,
        columns: [
            ui.nestedFields.image({
                bind: {
                    photo: true,
                },
                title: 'Item picture',
            }),
            ui.nestedFields.link({
                bind: {
                    name: true,
                },
                page(_, rowValue) {
                    return rowValue?._id
                        ? `@sage/xtrem-restaurant/RestaurantItem/${btoa(JSON.stringify({ _id: rowValue._id }))}`
                        : '';
                },
                title: 'Item',
            }),
            ui.nestedFields.select({
                bind: {
                    category: true,
                },
                optionType: '@sage/xtrem-restaurant/ItemCategory',
                title: 'Category',
            }),
            ui.nestedFields.numeric({
                bind: {
                    price: true,
                },
                title: 'Price',
            }),
        ],
    })
    items: ui.fields.Table<Item, Ingredient>;
}
