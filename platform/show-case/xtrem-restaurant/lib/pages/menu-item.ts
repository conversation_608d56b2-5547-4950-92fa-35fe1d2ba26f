import { Graph<PERSON><PERSON>, Item, MenuItem as MenuItemNode, Restaurant } from '@sage/xtrem-restaurant-api';
import * as ui from '@sage/xtrem-ui';
import { applicationPages } from '../menu-items/application-pages';

@ui.decorators.page<MenuItem, MenuItemNode>({
    authorizationCode: 'SHCPRDT',
    module: 'restaurant',
    title: 'Xtreem Restaurant Menu item',
    objectTypeSingular: 'Menu item',
    objectTypePlural: 'Menu items',
    mode: 'tabs',
    idField() {
        return this.item;
    },
    node: '@sage/xtrem-restaurant/MenuItem',
    menuItem: applicationPages,
    async onLoad() {
        this.$standardSaveAction.isDisabled = true;
        this.$standardCancelAction.isDisabled = true;
    },
    onDirtyStateUpdated(isDirty) {
        this.$standardSaveAction.isDisabled = !isDirty;
        this.$standardCancelAction.isDisabled = !isDirty;
    },
    navigationPanel: {
        orderBy: { item: { name: 1 }, restaurant: { name: 1 } },
        emptyStateClickableText: 'Create a new menu item',
        listItem: {
            image: ui.nestedFields.image<MenuItem, MenuItemNode>({
                title: 'Image',
                bind: {
                    item: { photo: true },
                },
            }),
            title: ui.nestedFields.reference<MenuItem, MenuItemNode, Item>({
                bind: { item: true },
                title: 'Item',
                valueField: 'name',
                node: '@sage/xtrem-restaurant/Item',
                tunnelPage: '@sage/xtrem-restaurant/RestaurantItem',
            }),
            line2: ui.nestedFields.reference<MenuItem, MenuItemNode, Restaurant>({
                bind: { restaurant: true },
                title: 'Restaurant',
                valueField: 'name',
                node: '@sage/xtrem-restaurant/Restaurant',
                tunnelPage: '@sage/xtrem-restaurant/Restaurant',
            }),
            line2Right: ui.nestedFields.switch({ bind: 'isActive', title: 'Available' }),
        },
    },
    businessActions() {
        return [this.$standardSaveAction, this.$standardCancelAction];
    },
    createAction() {
        return this.$standardNewAction;
    },
    headerQuickActions() {
        return [this.$standardOpenRecordHistoryAction];
    },
    headerDropDownActions() {
        return [this.$standardDeleteAction];
    },
})
export class MenuItem extends ui.Page<GraphApi, MenuItemNode> {
    @ui.decorators.section<MenuItem>({
        title: 'Menu item',
        isTitleHidden: true,
    })
    section: ui.containers.Section<MenuItem>;

    @ui.decorators.block<MenuItem>({
        isTitleHidden: true,
        parent() {
            return this.section;
        },
        width: 'extra-large',
    })
    block: ui.containers.Block<MenuItem>;

    @ui.decorators.referenceField<MenuItem, Item>({
        bind: { item: true },
        valueField: 'name',
        helperTextField: 'name',
        imageField: 'photo',
        node: '@sage/xtrem-restaurant/Item',
        parent() {
            return this.block;
        },
        isFullWidth: true,
        tunnelPage: '@sage/xtrem-restaurant/RestaurantItem',
        minLookupCharacters: 0,
        columns: [
            ui.nestedFields.image({ bind: { photo: true } }),
            ui.nestedFields.text({ bind: { name: true } }),
            ui.nestedFields.label({
                bind: {
                    category: true,
                },
                map(v) {
                    return ui.localizeEnumMember('@sage/xtrem-restaurant/ItemCategory', v);
                },
                borderColor: ui.tokens.colorsUtilityMajor400,
                backgroundColor: ui.tokens.colorsUtilityMajor400,
                color: ui.tokens.colorsUtilityYang100,
            }),
        ],
        isReadOnly() {
            return Boolean(this.$.recordId);
        },
    })
    item: ui.fields.Reference<MenuItemNode, MenuItem>;

    @ui.decorators.referenceField<MenuItem, Restaurant>({
        bind: { restaurant: true },
        valueField: 'name',
        node: '@sage/xtrem-restaurant/Item',
        parent() {
            return this.block;
        },
        isFullWidth: true,
        tunnelPage: '@sage/xtrem-restaurant/Restaurant',
        isReadOnly() {
            return Boolean(this.$.recordId);
        },
        minLookupCharacters: 0,
        orderBy: { address: { country: { name: 1 } } },
        columns: [
            ui.nestedFields.text({ bind: { address: { line1: true } } }),
            ui.nestedFields.text({
                bind: { address: { city: true } },
            }),
            ui.nestedFields.text({
                bind: { address: { country: { name: true } } },
            }),
        ],
    })
    restaurant: ui.fields.Reference<MenuItemNode, MenuItem>;

    @ui.decorators.switchField<MenuItem>({
        title: 'Available',
        bind: { isActive: true },
        parent() {
            return this.block;
        },
    })
    isActive: ui.fields.Switch<MenuItem>;

    @ui.decorators.numericField<MenuItem>({
        title: 'Wholesale price',
        bind: { price: true },
        scale: 2,
        parent() {
            return this.block;
        },
    })
    price: ui.fields.Numeric<MenuItem>;
}
