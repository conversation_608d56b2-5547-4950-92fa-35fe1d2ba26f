import { GraphApi, MenuItem, Item as xtremRestaurantItem } from '@sage/xtrem-restaurant-api';
import * as ui from '@sage/xtrem-ui';
import { applicationPages } from '../menu-items/application-pages';

@ui.decorators.page<RestaurantItem, xtremRestaurantItem>({
    authorizationCode: 'SHCPRDT',
    module: 'restaurant',
    title: 'Xtreem Restaurant Item',
    objectTypeSingular: 'Restaurant Item',
    objectTypePlural: 'Restaurant Items',
    mode: 'tabs',
    idField() {
        return this.name;
    },
    node: '@sage/xtrem-restaurant/Item',
    menuItem: applicationPages,
    onLoad() {
        this.$standardSaveAction.isDisabled = true;
        this.$standardCancelAction.isDisabled = true;
    },

    onDirtyStateUpdated(isDirty) {
        this.$standardSaveAction.isDisabled = !isDirty;
        this.$standardCancelAction.isDisabled = !isDirty;
    },

    navigationPanel: {
        orderBy: { name: 1 },
        emptyStateClickableText: 'Create a new restaurant item',
        listItem: {
            title: ui.nestedFields.text({ bind: 'name', title: 'Name' }),
            titleRight: ui.nestedFields.label({
                map(v) {
                    return ui.localizeEnumMember('@sage/xtrem-restaurant/ItemCategory', v);
                },
                bind: {
                    category: true,
                },
                borderColor: ui.tokens.colorsUtilityMajor400,
                backgroundColor: ui.tokens.colorsUtilityMajor400,
                color: ui.tokens.colorsUtilityYang100,
                title: 'Category',
            }),
            line2: ui.nestedFields.numeric({
                bind: 'price',
                title: 'Price',
            }),
            line2Right: ui.nestedFields.numeric({ bind: 'preparationTime', title: 'Preparation time', postfix: 'min' }),
        },
    },
    businessActions() {
        return [this.$standardSaveAction, this.$standardCancelAction];
    },
    createAction() {
        return this.$standardNewAction;
    },
    headerQuickActions() {
        return [this.$standardOpenRecordHistoryAction];
    },
    headerDropDownActions() {
        return [this.$standardDeleteAction];
    },
})
export class RestaurantItem extends ui.Page<GraphApi> {
    @ui.decorators.section<RestaurantItem>({
        title: 'Item',
        isTitleHidden: true,
    })
    headerSection: ui.containers.Section;

    @ui.decorators.block<RestaurantItem>({
        isTitleHidden: true,
        parent() {
            return this.headerSection;
        },
        width: 'extra-large',
    })
    topHeaderBlock: ui.containers.Block;

    @ui.decorators.textField<RestaurantItem>({
        parent() {
            return this.topHeaderBlock;
        },
        title: 'Name',
        width: 'medium',
        isMandatory: true,
    })
    name: ui.fields.Text;

    @ui.decorators.textAreaField<RestaurantItem>({
        parent() {
            return this.topHeaderBlock;
        },
        title: 'Description',
        width: 'medium',
        isMandatory: true,
        isFullWidth: true,
    })
    description: ui.fields.TextArea;

    @ui.decorators.richTextField<RestaurantItem>({
        parent() {
            return this.topHeaderBlock;
        },
        title: 'Instructions',
        isFullWidth: true,
    })
    cookingInstructions: ui.fields.RichText;

    @ui.decorators.imageField<RestaurantItem>({
        parent() {
            return this.topHeaderBlock;
        },
        title: 'Photo',
        placeholderValue() {
            return this.name.value ?? '';
        },
        shape: 'circle',
        width: 'medium',
    })
    photo: ui.fields.Image;

    @ui.decorators.section<RestaurantItem>({
        title: 'Item in restaurants',
        isTitleHidden: true,
    })
    menuItemsSection: ui.containers.Section;

    @ui.decorators.tableField<RestaurantItem, MenuItem>({
        parent() {
            return this.menuItemsSection;
        },
        title: 'Item in restaurants',
        isTitleHidden: true,
        bind: 'menuItems',
        emptyStateText: 'No menu items to display... yet!',
        isReadOnly: true,
        canSelect: false,
        orderBy: { item: { category: -1, name: -1 } },
        optionsMenu: [
            {
                title: 'All',
                graphQLFilter: {},
            },
            {
                title: 'Available',
                graphQLFilter: {
                    isActive: true,
                },
            },
            {
                title: 'Unavailable',
                graphQLFilter: {
                    isActive: false,
                },
            },
        ],
        optionsMenuType: 'toggle',
        columns: [
            ui.nestedFields.switch({
                bind: 'isActive',
                title: 'Available',
            }),
            ui.nestedFields.reference({
                bind: { restaurant: true },
                valueField: 'name',
                node: '@sage/xtrem-restaurant/Restaurant',
                tunnelPage: '@sage/xtrem-restaurant/Restaurant',
                title: 'Restaurant name',
            }),
            ui.nestedFields.numeric({
                bind: 'price',
                title: 'Menu price',
            }),
            ui.nestedFields.numeric({
                bind: {
                    item: {
                        price: true,
                    },
                },
                title: 'Wholesale price',
            }),
            ui.nestedFields.label({
                map(v) {
                    return ui.localizeEnumMember('@sage/xtrem-restaurant/ItemCategory', v);
                },
                bind: {
                    item: {
                        category: true,
                    },
                },
                borderColor: ui.tokens.colorsUtilityMajor400,
                backgroundColor: ui.tokens.colorsUtilityMajor400,
                color: ui.tokens.colorsUtilityYang100,
                title: 'Category',
            }),
        ],
    })
    menuItems: ui.fields.Table<MenuItem, RestaurantItem>;
}
