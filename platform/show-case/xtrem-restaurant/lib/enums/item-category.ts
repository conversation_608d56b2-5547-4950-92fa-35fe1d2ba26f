import { EnumDataType } from '@sage/xtrem-core';

export enum ItemCategoryEnum {
    'appetizer',
    'beverage',
    'breakfast',
    'dessert',
    'dinner',
    'lunch',
    'side_dish',
    'snack',
    'snacks',
}

export type ItemCategory = keyof typeof ItemCategoryEnum;

export const ItemCategoryDataType = new EnumDataType<ItemCategory>({
    enum: ItemCategoryEnum,
    filename: __filename,
});
