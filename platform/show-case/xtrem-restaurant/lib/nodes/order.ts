import { Collection, Node, Reference, decimal, decorators, useDefaultValue } from '@sage/xtrem-core';
import { dataTypes } from '@sage/xtrem-system';
import * as xtremRestaurant from '../../index';

@decorators.node<Order>({
    storage: 'sql',
    canCreate: true,
    canDelete: true,
    canDeleteMany: true,
    canRead: true,
    canSearch: true,
    canUpdate: true,
    canDuplicate: true,
    isPublished: true,
    isCustomizable: true,
    indexes: [{ orderBy: { _id: -1 }, isUnique: true, isNaturalKey: true }],
    isVitalCollectionChild: true,
})
export class Order extends Node {
    protected async getStatus() {
        const items = this.items;
        const areAllOrdersNew = !(await items.some(async i => (await i.status) !== 'new'));
        if ((await items.length) === 0 || areAllOrdersNew) {
            return 'new';
        }
        const areAllItemsDelivered = !(await items.some(async i => (await i.status) !== 'delivered'));
        return areAllItemsDelivered ? 'delivered' : 'preparing';
    }

    @decorators.enumProperty<Order, 'status'>({
        isPublished: true,
        isStoredOutput: true,
        dependsOn: [{ items: ['status'] }],
        dataType: () => xtremRestaurant.enums.OrderTypeDataType,
        defaultValue() {
            return this.getStatus();
        },
        updatedValue() {
            return this.getStatus();
        },
    })
    readonly status: Promise<xtremRestaurant.enums.OrderStatus | null>;

    @decorators.decimalProperty<Order, 'totalPrice'>({
        isPublished: true,
        isStoredOutput: true,
        lookupAccess: true,
        dependsOn: [{ items: ['unitPrice', 'quantity'] }],
        dataType: () => dataTypes.decimal,
        defaultValue() {
            return (
                this.items.reduce(
                    async (accumulator, item) => accumulator + (await item.unitPrice) * (await item.quantity),
                    0,
                ) || 0
            );
        },
        updatedValue: useDefaultValue,
    })
    readonly totalPrice: Promise<decimal>;

    // Relations

    @decorators.referenceProperty<Order, 'table'>({
        isStored: true,
        isPublished: true,
        node: () => xtremRestaurant.nodes.Table,
        isVitalParent: true,
    })
    readonly table: Reference<xtremRestaurant.nodes.Table>;

    @decorators.collectionProperty<Order, 'items'>({
        isPublished: true,
        node: () => xtremRestaurant.nodes.OrderItem,
        reverseReference: 'order',
        isVital: true,
    })
    readonly items: Collection<xtremRestaurant.nodes.OrderItem>;
}
