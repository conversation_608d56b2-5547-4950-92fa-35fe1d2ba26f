import { decorators, Node, Reference } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremRestaurant from '../../index';

@decorators.node<Address>({
    storage: 'sql',
    isPublished: true,
    canCreate: true,
    canRead: true,
    canUpdate: true,
    canDelete: true,
    canDeleteMany: true,
    indexes: [{ orderBy: { name: 1 }, isUnique: true, isNaturalKey: true }],
    isCustomizable: true,
    isVitalReferenceChild: true,
})
export class Address extends Node {
    @decorators.stringProperty<Address, 'name'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isNotEmpty: true,
        dataType: () => xtremSystem.dataTypes.name,
    })
    readonly name: Promise<string>;

    @decorators.stringProperty<Address, 'line1'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.description,
    })
    readonly line1: Promise<string>;

    @decorators.stringProperty<Address, 'line2'>({
        isStored: true,
        isPublished: true,
        isNotEmpty: false,
        dataType: () => xtremSystem.dataTypes.description,
    })
    readonly line2: Promise<string>;

    @decorators.stringProperty<Address, 'postCode'>({
        isPublished: true,
        isTransientInput: true,
        dataType: () => xtremSystem.dataTypes.code,
    })
    readonly postCode: Promise<string>;

    @decorators.stringProperty<Address, 'city'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.name,
    })
    readonly city: Promise<string>;

    // Relations

    @decorators.referenceProperty<Address, 'country'>({
        isStored: true,
        isPublished: true,
        node: () => xtremRestaurant.nodes.Country,
    })
    readonly country: Reference<xtremRestaurant.nodes.Country>;

    @decorators.referenceProperty<Address, 'restaurant'>({
        isVitalParent: true,
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        node: () => xtremRestaurant.nodes.Restaurant,
    })
    readonly restaurant: Reference<xtremRestaurant.nodes.Restaurant | null>;
}
