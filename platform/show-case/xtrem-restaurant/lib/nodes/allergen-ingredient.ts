import { Node, Reference, decorators } from '@sage/xtrem-core';
import * as xtremRestaurant from '../../index';

@decorators.node<AllergenIngredient>({
    storage: 'sql',
    canCreate: true,
    canDelete: true,
    canDeleteMany: true,
    canRead: true,
    canSearch: true,
    canUpdate: true,
    canDuplicate: true,
    isPublished: true,
    isCustomizable: true,
    indexes: [{ orderBy: { allergen: 1, ingredient: 1 }, isUnique: true, isNaturalKey: true }],
    isVitalCollectionChild: true,
    isAssociationCollectionChild: true,
})
export class AllergenIngredient extends Node {
    // Relations

    @decorators.referenceProperty<AllergenIngredient, 'allergen'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        node: () => xtremRestaurant.nodes.Allergen,
        isAssociationParent: true,
    })
    readonly allergen: Reference<xtremRestaurant.nodes.Allergen | null>;

    @decorators.referenceProperty<AllergenIngredient, 'ingredient'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        node: () => xtremRestaurant.nodes.Ingredient,
        isVitalParent: true,
    })
    readonly ingredient: Reference<xtremRestaurant.nodes.Ingredient | null>;
}
