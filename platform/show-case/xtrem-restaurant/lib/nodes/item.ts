import { BinaryStream, Collection, Node, TextStream, decimal, decorators, integer } from '@sage/xtrem-core';
import * as xtremRestaurant from '../../index';
import * as xtremSystem from '@sage/xtrem-system';

@decorators.node<Item>({
    storage: 'sql',
    canCreate: true,
    canDelete: true,
    canDeleteMany: true,
    canRead: true,
    canSearch: true,
    canUpdate: true,
    canDuplicate: true,
    isPublished: true,
    isCustomizable: true,
    indexes: [{ orderBy: { _id: 1 }, isUnique: true, isNaturalKey: true }],
})
export class Item extends Node {
    @decorators.decimalProperty<Item, 'price'>({
        isPublished: true,
        isStored: true,
        dataType: () => xtremSystem.dataTypes.decimal,
    })
    readonly price: Promise<decimal>;

    @decorators.stringProperty<Item, 'name'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.name,
    })
    readonly name: Promise<string>;

    @decorators.stringProperty<Item, 'description'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.description,
    })
    readonly description: Promise<string>;

    @decorators.integerProperty<Item, 'preparationTime'>({
        isPublished: true,
        isStored: true,
    })
    readonly preparationTime: Promise<integer>;

    @decorators.enumProperty<Item, 'category'>({
        dataType: () => xtremRestaurant.enums.ItemCategoryDataType,
        isStored: true,
        isPublished: true,
        isNullable: true,
    })
    readonly category: Promise<xtremRestaurant.enums.ItemCategory | null>;

    @decorators.binaryStreamProperty<Item, 'photo'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
    })
    readonly photo: Promise<BinaryStream | null>;

    @decorators.textStreamProperty<Item, 'cookingInstructions'>({
        isPublished: true,
        isStored: true,
    })
    readonly cookingInstructions: Promise<TextStream>;

    // Relations

    @decorators.collectionProperty<Item, 'ingredients'>({
        isPublished: true,
        reverseReference: 'item',
        node: () => xtremRestaurant.nodes.ItemIngredient,
        isAssociation: true,
    })
    readonly ingredients: Collection<xtremRestaurant.nodes.ItemIngredient>;

    @decorators.collectionProperty<Item, 'orderItems'>({
        isPublished: true,
        node: () => xtremRestaurant.nodes.OrderItem,
        reverseReference: 'menuItem',
    })
    readonly orderItems: Collection<xtremRestaurant.nodes.OrderItem>;

    @decorators.collectionProperty<Item, 'menuItems'>({
        isPublished: true,
        node: () => xtremRestaurant.nodes.MenuItem,
        reverseReference: 'item',
        isAssociation: true,
    })
    readonly menuItems: Collection<xtremRestaurant.nodes.MenuItem>;
}
