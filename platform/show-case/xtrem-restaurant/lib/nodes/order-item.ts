import { Node, Reference, decimal, decorators, integer } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremRestaurant from '../../index';

@decorators.node<OrderItem>({
    storage: 'sql',
    canCreate: true,
    canDelete: true,
    canDeleteMany: true,
    canRead: true,
    canSearch: true,
    canUpdate: true,
    canDuplicate: true,
    isPublished: true,
    isCustomizable: true,
    indexes: [{ orderBy: { order: 1, menuItem: 1 }, isUnique: true, isNaturalKey: true }],
    isVitalCollectionChild: true,
    isAssociationCollectionChild: true,
})
export class OrderItem extends Node {
    @decorators.decimalProperty<OrderItem, 'unitPrice'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.decimal,
    })
    readonly unitPrice: Promise<decimal>;

    @decorators.integerProperty<OrderItem, 'quantity'>({
        isStored: true,
        isPublished: true,
        defaultValue: 1,
    })
    readonly quantity: Promise<integer>;

    @decorators.stringProperty<OrderItem, 'notes'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.description,
    })
    readonly notes: Promise<string>;

    @decorators.enumProperty<OrderItem, 'status'>({
        dataType: () => xtremRestaurant.enums.OrderTypeDataType,
        isStored: true,
        isPublished: true,
        isNullable: true,
        defaultValue: 'new',
    })
    readonly status: Promise<xtremRestaurant.enums.OrderStatus | null>;

    // Relations

    @decorators.referenceProperty<OrderItem, 'menuItem'>({
        isStored: true,
        isPublished: true,
        node: () => xtremRestaurant.nodes.MenuItem,
        isAssociationParent: true,
    })
    readonly menuItem: Reference<xtremRestaurant.nodes.MenuItem>;

    @decorators.referenceProperty<OrderItem, 'order'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        node: () => xtremRestaurant.nodes.Order,
        isVitalParent: true,
    })
    readonly order: Reference<xtremRestaurant.nodes.Order>;
}
