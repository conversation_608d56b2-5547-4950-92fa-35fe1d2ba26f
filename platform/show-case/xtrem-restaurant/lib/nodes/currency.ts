import { BinaryStream, decorators, Node, StringDataType } from '@sage/xtrem-core';
import { dataTypes } from '@sage/xtrem-system';

@decorators.node<Currency>({
    package: 'xtrem-restaurant',
    storage: 'sql',
    isCached: true,
    isPublished: true,
    canCreate: true,
    canRead: true,
    canUpdate: true,
    canDelete: true,
    canDuplicate: true,
    indexes: [
        {
            orderBy: { name: 1 },
            isUnique: true,
            isNaturalKey: true,
        },
    ],
})
export class Currency extends Node {
    @decorators.stringProperty<Currency, 'name'>({
        isStored: true,
        isPublished: true,
        dataType: () => dataTypes.name,
    })
    readonly name: Promise<string>;

    @decorators.stringProperty<Currency, 'symbol'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 10 }),
        isNotEmpty: true,
        lookupAccess: true,
        duplicateRequiresPrompt: true,
    })
    readonly symbol: Promise<string>;

    @decorators.integerProperty<Currency, 'decimalDigits'>({
        isStored: true,
        isPublished: true,
        defaultValue() {
            return 2;
        },
        lookupAccess: true,
        duplicateRequiresPrompt: true,
    })
    readonly decimalDigits: Promise<number>;

    @decorators.binaryStreamProperty<Currency, 'icon'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
    })
    readonly icon: Promise<BinaryStream | null>;
}
