import { Collection, Node, Reference, decorators, integer } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremRestaurant from '../../index';

@decorators.node<Table>({
    storage: 'sql',
    canCreate: true,
    canDelete: true,
    canDeleteMany: true,
    canRead: true,
    canSearch: true,
    canUpdate: true,
    canDuplicate: true,
    isPublished: true,
    isCustomizable: true,
    indexes: [{ orderBy: { name: 1, restaurant: 1 }, isUnique: true, isNaturalKey: true }],
    isVitalCollectionChild: true,
})
export class Table extends Node {
    @decorators.stringProperty<Table, 'name'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.name,
    })
    readonly name: Promise<string>;

    @decorators.integerProperty<Table, 'capacity'>({
        isStored: true,
        isPublished: true,
    })
    readonly capacity: Promise<integer>;

    // Relations

    @decorators.referenceProperty<Table, 'restaurant'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        node: () => xtremRestaurant.nodes.Restaurant,
        isVitalParent: true,
    })
    readonly restaurant: Reference<xtremRestaurant.nodes.Restaurant | null>;

    @decorators.collectionProperty<Table, 'orders'>({
        isPublished: true,
        node: () => xtremRestaurant.nodes.Order,
        reverseReference: 'table',
        isVital: true,
    })
    readonly orders: Collection<xtremRestaurant.nodes.Order>;
}
