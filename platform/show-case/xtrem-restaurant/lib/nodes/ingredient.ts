import { Collection, Node, Reference, decorators } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremRestaurant from '../../index';

@decorators.node<Ingredient>({
    storage: 'sql',
    canCreate: true,
    canDelete: true,
    canDeleteMany: true,
    canRead: true,
    canSearch: true,
    canUpdate: true,
    canDuplicate: true,
    isPublished: true,
    isCustomizable: true,
    indexes: [{ orderBy: { name: 1 }, isUnique: true, isNaturalKey: true }],
})
export class Ingredient extends Node {
    @decorators.stringProperty<Ingredient, 'name'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.name,
    })
    readonly name: Promise<string>;

    // Relations

    @decorators.referenceProperty<Ingredient, 'unit'>({
        isStored: true,
        isNullable: true,
        isPublished: true,
        lookupAccess: true,
        node: () => xtremRestaurant.nodes.Unit,
    })
    readonly unit: Reference<xtremRestaurant.nodes.Unit | null>;

    @decorators.collectionProperty<Ingredient, 'allergens'>({
        isPublished: true,
        node: () => xtremRestaurant.nodes.AllergenIngredient,
        reverseReference: 'ingredient',
        isVital: true,
    })
    readonly allergens: Collection<xtremRestaurant.nodes.AllergenIngredient>;
}
