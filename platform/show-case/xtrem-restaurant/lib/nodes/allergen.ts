import { BinaryStream, Node, decorators, Collection } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremRestaurant from '../../index';

@decorators.node<Allergen>({
    storage: 'sql',
    canCreate: true,
    canDelete: true,
    canDeleteMany: true,
    canRead: true,
    canSearch: true,
    canUpdate: true,
    canDuplicate: true,
    isPublished: true,
    isCustomizable: true,
    indexes: [{ orderBy: { name: 1 }, isUnique: true, isNaturalKey: true }],
})
export class Allergen extends Node {
    @decorators.stringProperty<Allergen, 'name'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.name,
    })
    readonly name: Promise<string>;

    @decorators.binaryStreamProperty<Allergen, 'icon'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
    })
    readonly icon: Promise<BinaryStream | null>;

    // Relations

    @decorators.collectionProperty<Allergen, 'ingredients'>({
        isPublished: true,
        node: () => xtremRestaurant.nodes.AllergenIngredient,
        reverseReference: 'allergen',
        isAssociation: true,
    })
    readonly ingredients: Collection<xtremRestaurant.nodes.AllergenIngredient>;
}
