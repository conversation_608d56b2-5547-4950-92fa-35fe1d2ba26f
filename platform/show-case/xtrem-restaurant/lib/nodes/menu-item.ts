import { Node, Reference, decimal, decorators } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremRestaurant from '../../index';

@decorators.node<MenuItem>({
    storage: 'sql',
    canCreate: true,
    canDelete: true,
    canDeleteMany: true,
    canRead: true,
    canSearch: true,
    canUpdate: true,
    canDuplicate: true,
    isPublished: true,
    isCustomizable: true,
    indexes: [{ orderBy: { restaurant: 1, item: 1 }, isUnique: true, isNaturalKey: true }],
    isVitalCollectionChild: true,
    isAssociationCollectionChild: true,
})
export class MenuItem extends Node {
    @decorators.decimalProperty<MenuItem, 'price'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.decimal,
    })
    readonly price: Promise<decimal>;

    @decorators.booleanProperty<MenuItem, 'isActive'>({
        isStored: true,
        isPublished: true,
    })
    readonly isActive: Promise<boolean>;

    // Relations

    @decorators.referenceProperty<MenuItem, 'restaurant'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        node: () => xtremRestaurant.nodes.Restaurant,
        isVitalParent: true,
    })
    readonly restaurant: Reference<xtremRestaurant.nodes.Restaurant | null>;

    @decorators.referenceProperty<MenuItem, 'item'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        node: () => xtremRestaurant.nodes.Item,
        isAssociationParent: true,
    })
    readonly item: Reference<xtremRestaurant.nodes.Item | null>;
}
