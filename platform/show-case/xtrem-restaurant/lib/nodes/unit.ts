import { decorators, integer, Node } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';

@decorators.node<Unit>({
    tableName: '',
    package: '',
    storage: 'sql',
    isPublished: true,
    canRead: true,
    canSearch: true,
    indexes: [
        {
            orderBy: {
                symbol: 1,
            },
            isUnique: true,
            isNaturalKey: true,
        },
    ],
})
export class Unit extends Node {
    @decorators.stringProperty<Unit, 'description'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.description,
    })
    readonly description: Promise<string>;

    @decorators.shortProperty<Unit, 'numberOfDecimals'>({
        isPublished: true,
        isStored: true,
    })
    readonly numberOfDecimals: Promise<integer>;

    @decorators.stringProperty<Unit, 'symbol'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.shortDescription,
    })
    readonly symbol: Promise<string>;
}
