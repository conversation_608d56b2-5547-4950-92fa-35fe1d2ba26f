import { Node, Reference, decorators, integer } from '@sage/xtrem-core';
import * as xtremRestaurant from '../../index';

@decorators.node<ItemIngredient>({
    storage: 'sql',
    canCreate: true,
    canDelete: true,
    canDeleteMany: true,
    canRead: true,
    canSearch: true,
    canUpdate: true,
    canDuplicate: true,
    isPublished: true,
    isCustomizable: true,
    indexes: [{ orderBy: { ingredient: 1, item: 1 }, isUnique: true, isNaturalKey: true }],
    isVitalCollectionChild: true,
    isAssociationCollectionChild: true,
})
export class ItemIngredient extends Node {
    @decorators.integerProperty<ItemIngredient, 'quantity'>({
        isPublished: true,
        isStored: true,
    })
    readonly quantity: Promise<integer>;

    // Relations

    @decorators.referenceProperty<ItemIngredient, 'ingredient'>({
        isStored: true,
        isPublished: true,
        node: () => xtremRestaurant.nodes.Ingredient,
        isVitalParent: true,
    })
    readonly ingredient: Reference<xtremRestaurant.nodes.Ingredient>;

    @decorators.referenceProperty<ItemIngredient, 'item'>({
        isStored: true,
        isPublished: true,
        node: () => xtremRestaurant.nodes.Item,
        isAssociationParent: true,
    })
    readonly item: Reference<xtremRestaurant.nodes.Item>;
}
