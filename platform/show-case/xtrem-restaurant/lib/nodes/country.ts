import { Node, decorators, integer, Collection } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremRestaurant from '../../index';

@decorators.node<Country>({
    storage: 'sql',
    canCreate: true,
    canDelete: true,
    canDeleteMany: true,
    canRead: true,
    canSearch: true,
    canUpdate: true,
    canDuplicate: true,
    isPublished: true,
    isCustomizable: true,
    indexes: [{ orderBy: { name: 1 }, isUnique: true, isNaturalKey: true }],
})
export class Country extends Node {
    @decorators.integerProperty<Country, 'phoneCountryCode'>({
        isStored: true,
        isPublished: true,
    })
    readonly phoneCountryCode: Promise<integer>;

    @decorators.stringProperty<Country, 'name'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.name,
    })
    readonly name: Promise<string>;

    // Relations

    @decorators.collectionProperty<Country, 'addresses'>({
        isPublished: true,
        node: () => xtremRestaurant.nodes.Address,
        reverseReference: 'country',
    })
    readonly addresses: Collection<xtremRestaurant.nodes.Address>;
}
