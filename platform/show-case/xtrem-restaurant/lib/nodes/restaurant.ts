import { Collection, Node, Reference, TextStream, TextStreamDataType, decorators, integer } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremRestaurant from '../../index';

@decorators.node<Restaurant>({
    package: 'xtrem-restaurant',
    storage: 'sql',
    canCreate: true,
    canDelete: true,
    canDeleteMany: true,
    canRead: true,
    canSearch: true,
    canUpdate: true,
    canDuplicate: true,
    isPublished: true,
    isCustomizable: true,
    indexes: [{ orderBy: { name: 1 }, isUnique: true, isNaturalKey: true }],
})
export class Restaurant extends Node {
    @decorators.stringProperty<Restaurant, 'name'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.name,
    })
    readonly name: Promise<string>;

    @decorators.stringProperty<Restaurant, 'phoneNumber'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.name,
    })
    readonly phoneNumber: Promise<string>;

    @decorators.integerProperty<Restaurant, 'capacity'>({
        isStored: true,
        isPublished: true,
    })
    readonly capacity: Promise<integer>;

    // Relations

    @decorators.referenceProperty<Restaurant, 'currency'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        node: () => xtremRestaurant.nodes.Currency,
    })
    readonly currency: Reference<xtremRestaurant.nodes.Currency | null>;

    @decorators.referenceProperty<Restaurant, 'address'>({
        isVital: true,
        reverseReference: 'restaurant',
        isPublished: true,
        lookupAccess: true,
        node: () => xtremRestaurant.nodes.Address,
    })
    readonly address: Reference<xtremRestaurant.nodes.Address | null>;

    @decorators.collectionProperty<Restaurant, 'tables'>({
        isPublished: true,
        node: () => xtremRestaurant.nodes.Table,
        reverseReference: 'restaurant',
        isVital: true,
    })
    readonly tables: Collection<xtremRestaurant.nodes.Table>;

    @decorators.collectionProperty<Restaurant, 'menuItems'>({
        isPublished: true,
        node: () => xtremRestaurant.nodes.MenuItem,
        reverseReference: 'restaurant',
        isVital: true,
    })
    readonly menuItems: Collection<xtremRestaurant.nodes.MenuItem>;

    @decorators.textStreamProperty<Restaurant, 'tableLayoutChart'>({
        isPublished: true,
        isStored: true,
        dataType: () =>
            new TextStreamDataType({
                maxLength: 1000000,
                allowedContentTypes: ['text/html', 'application/json', 'text/plain'],
            }),
    })
    tableLayoutChart: Promise<TextStream>;
}
