{"extends": "../../tsconfig-base.json", "compilerOptions": {"outDir": "build", "rootDir": ".", "baseUrl": "."}, "include": ["index.ts", "lib/**/*", "test/**/*", "api/api.d.ts"], "exclude": ["lib/pages/**/*", "lib/widgets/**/*", "lib/page-extensions/**/*", "lib/page-fragments/**/*", "lib/stickers/**/*", "lib/i18n/**/*", "**/*.feature", "lib/client-functions/**/*"], "references": [{"path": "../../system/xtrem-authorization"}, {"path": "../../cli/xtrem-cli"}, {"path": "../../back-end/xtrem-core"}, {"path": "../../shared/xtrem-date-time"}, {"path": "../../shared/xtrem-decimal"}, {"path": "../../system/xtrem-interop"}, {"path": "../../shared/xtrem-shared"}, {"path": "../xtrem-show-case"}, {"path": "../../system/xtrem-system"}, {"path": "../../front-end/xtrem-ui"}, {"path": "../../back-end/eslint-plugin-xtrem"}, {"path": "../xtrem-show-case/api"}, {"path": "api"}]}