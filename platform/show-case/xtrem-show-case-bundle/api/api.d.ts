declare module '@sage/xtrem-show-case-bundle-api-partial' {
    import type { Package as SageXtremAuditing$Package } from '@sage/xtrem-auditing-api';
    import type { Package as SageXtremAuthorization$Package } from '@sage/xtrem-authorization-api';
    import type { Package as SageXtremCommunication$Package } from '@sage/xtrem-communication-api';
    import type { Package as SageXtremCustomization$Package } from '@sage/xtrem-customization-api';
    import type { Package as SageXtremDashboard$Package } from '@sage/xtrem-dashboard-api';
    import type { Package as SageXtremImportExport$Package } from '@sage/xtrem-import-export-api';
    import type { Package as SageXtremInterop$Package } from '@sage/xtrem-interop-api';
    import type { Package as SageXtremMailer$Package } from '@sage/xtrem-mailer-api';
    import type { MetaNodeFactory, Package as SageXtremMetadata$Package } from '@sage/xtrem-metadata-api';
    import type { Package as SageXtremReporting$Package } from '@sage/xtrem-reporting-api';
    import type { Package as SageXtremRouting$Package } from '@sage/xtrem-routing-api';
    import type { Package as SageXtremScheduler$Package } from '@sage/xtrem-scheduler-api';
    import type {
        Package as SageXtremShowCase$Package,
        ShowCaseEmployee,
        ShowCaseProductInput,
        ShowCaseProductOriginAddress,
        ShowCaseProductOriginAddressBinding,
        ShowCaseProductOriginAddressInput,
        ShowCaseProvider,
    } from '@sage/xtrem-show-case-api';
    import type { Package as SageXtremSystem$Package, User } from '@sage/xtrem-system-api';
    import type {
        AttachmentAssociation,
        AttachmentAssociationInput,
        Package as SageXtremUpload$Package,
        UploadedFile,
    } from '@sage/xtrem-upload-api';
    import type { Package as SageXtremWorkflow$Package } from '@sage/xtrem-workflow-api';
    import type {
        AggregateQueryOperation,
        AggregateReadOperation,
        AsyncOperation,
        BinaryStream,
        ClientCollection,
        ClientNode,
        ClientNodeInput,
        CreateOperation,
        DeleteOperation,
        GetDefaultsOperation,
        GetDuplicateOperation,
        QueryOperation,
        ReadOperation,
        UpdateByIdOperation,
        UpdateOperation,
        decimal,
        integer,
    } from '@sage/xtrem-client';
    export interface BiodegradabilityCategory extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        description: string;
        energyToProduce: string;
        percentageOfDegradability: string;
        localSourcing: boolean;
    }
    export interface BiodegradabilityCategoryInput extends ClientNodeInput {
        description?: string;
        energyToProduce?: decimal | string;
        percentageOfDegradability?: decimal | string;
        localSourcing?: boolean | string;
    }
    export interface BiodegradabilityCategoryBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        description: string;
        energyToProduce: string;
        percentageOfDegradability: string;
        localSourcing: boolean;
    }
    export interface BiodegradabilityCategory$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface BiodegradabilityCategory$Operations {
        query: QueryOperation<BiodegradabilityCategory>;
        read: ReadOperation<BiodegradabilityCategory>;
        aggregate: {
            read: AggregateReadOperation<BiodegradabilityCategory>;
            query: AggregateQueryOperation<BiodegradabilityCategory>;
        };
        create: CreateOperation<BiodegradabilityCategoryInput, BiodegradabilityCategory>;
        getDuplicate: GetDuplicateOperation<BiodegradabilityCategory>;
        update: UpdateOperation<BiodegradabilityCategoryInput, BiodegradabilityCategory>;
        updateById: UpdateByIdOperation<BiodegradabilityCategoryInput, BiodegradabilityCategory>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: BiodegradabilityCategory$AsyncOperations;
        getDefaults: GetDefaultsOperation<BiodegradabilityCategory>;
    }
    export interface ShowCaseProductExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        product: string;
        barcode: string;
        description: string;
        hotProduct: boolean;
        fixedQuantity: integer;
        st: integer;
        listPrice: string;
        progress: integer;
        discount: integer;
        tax: string;
        amount: string;
        total: string;
        provider: ShowCaseProvider;
        email: string;
        computedEmail: string;
        importedAt: string;
        releaseDate: string;
        endingDate: string;
        category: ShowCaseProductCategory;
        subcategories: ShowCaseProductCategory[];
        entries: string[];
        designerEmployee: ShowCaseEmployee;
        originAddress: ShowCaseProductOriginAddress;
        certificate: UploadedFile;
        createdAt: string;
        manufacturedWithin: string;
        imageField: BinaryStream;
        biodegradabilityCategory: BiodegradabilityCategory;
        _attachments: ClientCollection<AttachmentAssociation>;
        qty: integer;
        netPrice: string;
    }
    export interface ShowCaseProductInputExtension {
        product?: string;
        barcode?: string;
        description?: string;
        hotProduct?: boolean | string;
        fixedQuantity?: integer | string;
        st?: integer | string;
        listPrice?: decimal | string;
        progress?: integer | string;
        discount?: integer | string;
        tax?: decimal | string;
        amount?: decimal | string;
        email?: string;
        importedAt?: string;
        releaseDate?: string;
        endingDate?: string;
        category?: ShowCaseProductCategory;
        subcategories?: ShowCaseProductCategory[];
        entries?: string[];
        designerEmployee?: integer | string;
        originAddress?: ShowCaseProductOriginAddressInput;
        certificate?: integer | string;
        createdAt?: string;
        manufacturedWithin?: string;
        biodegradabilityCategory?: integer | string;
        _attachments?: Partial<AttachmentAssociationInput>[];
        qty?: integer | string;
        netPrice?: decimal | string;
    }
    export interface ShowCaseProductBindingExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        product: string;
        barcode: string;
        description: string;
        hotProduct: boolean;
        fixedQuantity: integer;
        st: integer;
        listPrice: string;
        progress: integer;
        discount: integer;
        tax: string;
        amount: string;
        total: string;
        provider: ShowCaseProvider;
        email: string;
        computedEmail: string;
        importedAt: string;
        releaseDate: string;
        endingDate: string;
        category: ShowCaseProductCategory;
        subcategories: ShowCaseProductCategory[];
        entries: string[];
        designerEmployee: ShowCaseEmployee;
        originAddress: ShowCaseProductOriginAddressBinding;
        certificate: UploadedFile;
        createdAt: string;
        manufacturedWithin: string;
        imageField: BinaryStream;
        biodegradabilityCategory: BiodegradabilityCategory;
        _attachments: ClientCollection<AttachmentAssociation>;
        qty: integer;
        netPrice: string;
    }
    export interface ShowCaseProductExtension$Lookups {
        biodegradabilityCategory: QueryOperation<BiodegradabilityCategory>;
    }
    export interface ShowCaseProductExtension$Operations {
        lookups(dataOrId: string | { data: ShowCaseProductInput }): ShowCaseProductExtension$Lookups;
    }
    export interface Package {
        '@sage/xtrem-show-case-bundle/BiodegradabilityCategory': BiodegradabilityCategory$Operations;
    }
    export interface GraphApi
        extends Package,
            SageXtremAuditing$Package,
            SageXtremAuthorization$Package,
            SageXtremCommunication$Package,
            SageXtremCustomization$Package,
            SageXtremDashboard$Package,
            SageXtremImportExport$Package,
            SageXtremInterop$Package,
            SageXtremMailer$Package,
            SageXtremMetadata$Package,
            SageXtremReporting$Package,
            SageXtremRouting$Package,
            SageXtremScheduler$Package,
            SageXtremShowCase$Package,
            SageXtremSystem$Package,
            SageXtremUpload$Package,
            SageXtremWorkflow$Package {}
}
declare module '@sage/xtrem-show-case-bundle-api' {
    export type * from '@sage/xtrem-show-case-bundle-api-partial';
}
declare module '@sage/xtrem-auditing-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-show-case-bundle-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-authorization-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-show-case-bundle-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-communication-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-show-case-bundle-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-customization-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-show-case-bundle-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-dashboard-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-show-case-bundle-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-import-export-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-show-case-bundle-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-interop-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-show-case-bundle-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-mailer-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-show-case-bundle-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-metadata-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-show-case-bundle-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-reporting-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-show-case-bundle-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-routing-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-show-case-bundle-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-scheduler-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-show-case-bundle-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-show-case-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-show-case-bundle-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-system-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-show-case-bundle-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-upload-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-show-case-bundle-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-workflow-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-show-case-bundle-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-show-case-api-partial' {
    import type {
        ShowCaseProductBindingExtension,
        ShowCaseProductExtension,
        ShowCaseProductExtension$Lookups,
        ShowCaseProductExtension$Operations,
        ShowCaseProductInputExtension,
    } from '@sage/xtrem-show-case-bundle-api';
    export interface ShowCaseProduct extends ShowCaseProductExtension {}
    export interface ShowCaseProductBinding extends ShowCaseProductBindingExtension {}
    export interface ShowCaseProductInput extends ShowCaseProductInputExtension {}
    export interface ShowCaseProduct$Lookups extends ShowCaseProductExtension$Lookups {}
    export interface ShowCaseProduct$Operations extends ShowCaseProductExtension$Operations {}
}
