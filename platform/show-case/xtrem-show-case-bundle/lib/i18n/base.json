{"@sage/xtrem-show-case-bundle/data_types__default_decimal_data_type__name": "Default decimal data type", "@sage/xtrem-show-case-bundle/data_types__description_data_type__name": "Description data type", "@sage/xtrem-show-case-bundle/data_types__email_data_type__name": "Email data type", "@sage/xtrem-show-case-bundle/node-extensions__show_case_product_extension__property__biodegradabilityCategory": "Biodegradability category", "@sage/xtrem-show-case-bundle/node-extensions__show_case_product_extension__property__description": "Description", "@sage/xtrem-show-case-bundle/nodes__biodegradability_category__asyncMutation__asyncExport": "Export", "@sage/xtrem-show-case-bundle/nodes__biodegradability_category__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-show-case-bundle/nodes__biodegradability_category__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-show-case-bundle/nodes__biodegradability_category__node_name": "Biodegradability category", "@sage/xtrem-show-case-bundle/nodes__biodegradability_category__property__description": "Description", "@sage/xtrem-show-case-bundle/nodes__biodegradability_category__property__energyToProduce": "Energy to produce", "@sage/xtrem-show-case-bundle/nodes__biodegradability_category__property__localSourcing": "Local sourcing", "@sage/xtrem-show-case-bundle/nodes__biodegradability_category__property__percentageOfDegradability": "Percentage of degradability", "@sage/xtrem-show-case-bundle/package__name": "Sage xtrem show case bundle", "@sage/xtrem-show-case-bundle/page-extensions__block_extension__biodegradabilityBlock____title": "Biodegradability", "@sage/xtrem-show-case-bundle/page-extensions__block_extension__biodegradabilityCategory____columns__title___id": "Id", "@sage/xtrem-show-case-bundle/page-extensions__block_extension__biodegradabilityCategory____columns__title__description": "Description", "@sage/xtrem-show-case-bundle/page-extensions__block_extension__biodegradabilityCategory____title": "Biodegradability Category", "@sage/xtrem-show-case-bundle/page-extensions__button_extension__field____helperText": "Extension Helper Text", "@sage/xtrem-show-case-bundle/page-extensions__button_extension__field____title": "Extension Title", "@sage/xtrem-show-case-bundle/page-extensions__calendar_extension__field____helperText": "Extension Helper Text", "@sage/xtrem-show-case-bundle/page-extensions__calendar_extension__field____title": "Extension Title", "@sage/xtrem-show-case-bundle/page-extensions__chart_extension__field____helperText": "Extension Helper Text", "@sage/xtrem-show-case-bundle/page-extensions__chart_extension__field____title": "Extension Title", "@sage/xtrem-show-case-bundle/page-extensions__checkbox_extension__field____helperText": "Extension Helper Text", "@sage/xtrem-show-case-bundle/page-extensions__checkbox_extension__field____title": "Extension Title", "@sage/xtrem-show-case-bundle/page-extensions__checkbox_extension__mandatory____helperText": "Extension Mandatory Helper Text", "@sage/xtrem-show-case-bundle/page-extensions__checkbox_extension__mandatory____title": "Extension Mandatory", "@sage/xtrem-show-case-bundle/page-extensions__count_extension__filteredCountOfProducts____helperText": "Extension with qty field greater than 15", "@sage/xtrem-show-case-bundle/page-extensions__count_extension__filteredCountOfProducts____title": "Extension Total number of products", "@sage/xtrem-show-case-bundle/page-extensions__count_extension__totalCountOfProducts____helperText": "Extension: Total number of products", "@sage/xtrem-show-case-bundle/page-extensions__count_extension__totalCountOfProducts____title": "Extension Title", "@sage/xtrem-show-case-bundle/page-extensions__date_field_extension__customValidation____helperText": "Extension Future date Helper Text", "@sage/xtrem-show-case-bundle/page-extensions__date_field_extension__customValidation____title": "Extension Future date Title", "@sage/xtrem-show-case-bundle/page-extensions__date_field_extension__field____helperText": "Extension Helper Text", "@sage/xtrem-show-case-bundle/page-extensions__date_field_extension__field____title": "Extension Title", "@sage/xtrem-show-case-bundle/page-extensions__date_field_extension__mandatory____helperText": "Extension Mandatory Helper Text", "@sage/xtrem-show-case-bundle/page-extensions__date_field_extension__mandatory____title": "Extension Mandatory Title", "@sage/xtrem-show-case-bundle/page-extensions__detail_list_extension__field____fieldOverrides__title": "Overridden description title", "@sage/xtrem-show-case-bundle/page-extensions__detail_list_extension__field____fields__title": "List price", "@sage/xtrem-show-case-bundle/page-extensions__detail_list_extension__field____helperText": "Extension Helper Text", "@sage/xtrem-show-case-bundle/page-extensions__detail_list_extension__field____title": "Extension Title", "@sage/xtrem-show-case-bundle/page-extensions__dropdown_list_extension__errorHandled____helperText": "Extension onError overridden", "@sage/xtrem-show-case-bundle/page-extensions__dropdown_list_extension__errorHandled____title": "Extension Dropdown List with Error", "@sage/xtrem-show-case-bundle/page-extensions__dropdown_list_extension__field1____helperText": "Extension Helper Text", "@sage/xtrem-show-case-bundle/page-extensions__dropdown_list_extension__field1____title": "Extension Title (Options)", "@sage/xtrem-show-case-bundle/page-extensions__dropdown_list_extension__large____helperText": "Former large size", "@sage/xtrem-show-case-bundle/page-extensions__dropdown_list_extension__large____title": "Extension Medium", "@sage/xtrem-show-case-bundle/page-extensions__dropdown_list_extension__medium____helperText": "Former medium size", "@sage/xtrem-show-case-bundle/page-extensions__dropdown_list_extension__medium____title": "Extension Small", "@sage/xtrem-show-case-bundle/page-extensions__dropdown_list_extension__small____helperText": "Former small size", "@sage/xtrem-show-case-bundle/page-extensions__dropdown_list_extension__small____title": "Extension Large", "@sage/xtrem-show-case-bundle/page-extensions__dropdown_list_extension__validated____helperText": "Extension with Validation overridden", "@sage/xtrem-show-case-bundle/page-extensions__dropdown_list_extension__validated____title": "Extension Dropdown List", "@sage/xtrem-show-case-bundle/page-extensions__file_extension__field____helperText": "Extension Helper Text", "@sage/xtrem-show-case-bundle/page-extensions__file_extension__field____title": "Extension Title", "@sage/xtrem-show-case-bundle/page-extensions__filter_select_extension__field____helperText": "Extension Helper Text", "@sage/xtrem-show-case-bundle/page-extensions__filter_select_extension__field____title": "Extension Title", "@sage/xtrem-show-case-bundle/page-extensions__filter_select_extension__large____helperText": "Former large size", "@sage/xtrem-show-case-bundle/page-extensions__filter_select_extension__large____title": "Extension Medium", "@sage/xtrem-show-case-bundle/page-extensions__filter_select_extension__medium____helperText": "Former medium size", "@sage/xtrem-show-case-bundle/page-extensions__filter_select_extension__medium____title": "Extension Small", "@sage/xtrem-show-case-bundle/page-extensions__filter_select_extension__restrictedResultByCallbackFilter____helperText": "Extension: former filter", "@sage/xtrem-show-case-bundle/page-extensions__filter_select_extension__restrictedResultByCallbackFilter____title": "Extension: Restricted Results by filter callback", "@sage/xtrem-show-case-bundle/page-extensions__filter_select_extension__restrictedResultByFilter____helperText": "Extension: former filter callback", "@sage/xtrem-show-case-bundle/page-extensions__filter_select_extension__restrictedResultByFilter____title": "Extension: Restricted Results by filter", "@sage/xtrem-show-case-bundle/page-extensions__filter_select_extension__small____helperText": "Former small size", "@sage/xtrem-show-case-bundle/page-extensions__filter_select_extension__small____title": "Extension Large", "@sage/xtrem-show-case-bundle/page-extensions__filter_select_extension__validated____helperText": "It turns out Extension doesn't like meat", "@sage/xtrem-show-case-bundle/page-extensions__filter_select_extension__validated____title": "Extension: Filter Select with Custom Validation", "@sage/xtrem-show-case-bundle/page-extensions__icon_extension__field____helperText": "Extension Helper Text", "@sage/xtrem-show-case-bundle/page-extensions__icon_extension__field____title": "Extension Title", "@sage/xtrem-show-case-bundle/page-extensions__label_extension__field____helperText": "Extension Helper Text", "@sage/xtrem-show-case-bundle/page-extensions__label_extension__field____title": "Extension Title", "@sage/xtrem-show-case-bundle/page-extensions__link_extension__field____helperText": "Extension Helper Text", "@sage/xtrem-show-case-bundle/page-extensions__link_extension__field____title": "Extension Title", "@sage/xtrem-show-case-bundle/page-extensions__navigation_panel_empty_extension____navigationPanel__emptyStateClickableText": "Click this extension", "@sage/xtrem-show-case-bundle/page-extensions__navigation_panel_empty_extension____navigationPanel__emptyStateText": "extension test", "@sage/xtrem-show-case-bundle/page-extensions__nested_grid_extension__activatableField____helperText": "Extension Helper Text", "@sage/xtrem-show-case-bundle/page-extensions__nested_grid_extension__activatableField____title": "Extension Title", "@sage/xtrem-show-case-bundle/page-extensions__nested_grid_extension__field____helperText": "Extension Helper Text", "@sage/xtrem-show-case-bundle/page-extensions__nested_grid_extension__field____levels__columnOverrides__title": "Order Date Extended", "@sage/xtrem-show-case-bundle/page-extensions__nested_grid_extension__field____levels__columnOverrides__title__2": "Purchase Date Extended", "@sage/xtrem-show-case-bundle/page-extensions__nested_grid_extension__field____title": "Extension Title", "@sage/xtrem-show-case-bundle/page-extensions__numeric_extension__field____helperText": "Extension Helper Text", "@sage/xtrem-show-case-bundle/page-extensions__numeric_extension__field____title": "Extension Title", "@sage/xtrem-show-case-bundle/page-extensions__page_actions_extension____navigationPanel__dropdownActions__title__extensionAction1": "Extension Action 1", "@sage/xtrem-show-case-bundle/page-extensions__page_actions_extension____navigationPanel__dropdownActions__title__extensionAction2": "Extension Action 2", "@sage/xtrem-show-case-bundle/page-extensions__page_actions_extension__extensionAction1____title": "Extension Action 1", "@sage/xtrem-show-case-bundle/page-extensions__page_actions_extension__extensionAction2____title": "Extension Action 2", "@sage/xtrem-show-case-bundle/page-extensions__page_actions_extension__products____dropdownActions__title__extensionAction1": "Extension Action 1", "@sage/xtrem-show-case-bundle/page-extensions__page_actions_extension__products____dropdownActions__title__extensionAction2": "Extension Action 2", "@sage/xtrem-show-case-bundle/page-extensions__pod_collection_extension__lines____columns__title__invoice__purchaseDate": "Invoice", "@sage/xtrem-show-case-bundle/page-extensions__pod_collection_extension__lines____helperText": "Extension Helper Text", "@sage/xtrem-show-case-bundle/page-extensions__pod_collection_extension__lines____title": "Extension Title", "@sage/xtrem-show-case-bundle/page-extensions__progress_extension__field____helperText": "Extension Helper Text", "@sage/xtrem-show-case-bundle/page-extensions__progress_extension__field____title": "Extension Title", "@sage/xtrem-show-case-bundle/page-extensions__radio_buttons_extension__field____helperText": "Extension Helper Text", "@sage/xtrem-show-case-bundle/page-extensions__radio_buttons_extension__field____title": "Extension Title", "@sage/xtrem-show-case-bundle/page-extensions__radio_buttons_extension__inlineOption____helperText": "Extension: The options are not from an enum", "@sage/xtrem-show-case-bundle/page-extensions__radio_buttons_extension__inlineOption____title": "Extension: Options set in file", "@sage/xtrem-show-case-bundle/page-extensions__reference_extension__field____helperText": "Extension Helper Text", "@sage/xtrem-show-case-bundle/page-extensions__reference_extension__field____title": "Extension Title", "@sage/xtrem-show-case-bundle/page-extensions__reference_for_adc_poc_extension__field1____helperText": "Extension Helper Text", "@sage/xtrem-show-case-bundle/page-extensions__reference_for_adc_poc_extension__field1____title": "Extension Title", "@sage/xtrem-show-case-bundle/page-extensions__rich_text_extension__field____helperText": "Extension Helper Text", "@sage/xtrem-show-case-bundle/page-extensions__rich_text_extension__field____title": "Extension Title", "@sage/xtrem-show-case-bundle/page-extensions__select_extension__field____helperText": "Extension Helper Text", "@sage/xtrem-show-case-bundle/page-extensions__select_extension__field____title": "Extension Title", "@sage/xtrem-show-case-bundle/page-extensions__select_extension__fullWidth____helperText": "Override base field isFullWidth prop", "@sage/xtrem-show-case-bundle/page-extensions__select_extension__fullWidth____title": "Extension Former Full width", "@sage/xtrem-show-case-bundle/page-extensions__select_extension__mandatory____helperText": "Override base field isMandatory prop", "@sage/xtrem-show-case-bundle/page-extensions__select_extension__mandatory____title": "Extension Former Mandatory", "@sage/xtrem-show-case-bundle/page-extensions__show_case_product_extension____navigationPanel__dropdownActions__title": "Extended action at the end", "@sage/xtrem-show-case-bundle/page-extensions__show_case_product_extension____navigationPanel__dropdownActions__title__2": "Extended action inserted before someAction", "@sage/xtrem-show-case-bundle/page-extensions__show_case_product_extension____navigationPanel__dropdownActions__title__3": "Extended action inserted after someAction", "@sage/xtrem-show-case-bundle/page-extensions__show_case_product_extension____navigationPanel__inlineActions__title": "Extended action inserted at the end", "@sage/xtrem-show-case-bundle/page-extensions__show_case_product_extension____navigationPanel__inlineActions__title__2": "Extended action inserted before delete", "@sage/xtrem-show-case-bundle/page-extensions__show_case_product_extension____navigationPanel__inlineActions__title__3": "Extended action inserted after delete", "@sage/xtrem-show-case-bundle/page-extensions__show_case_product_extension____navigationPanel__listItem__line2__title": "Biodegradability Category", "@sage/xtrem-show-case-bundle/page-extensions__show_case_product_extension____navigationPanel__listItem__someRandomIcon__title": "Some extension icon", "@sage/xtrem-show-case-bundle/page-extensions__show_case_product_extension__addProduct____title": "Add", "@sage/xtrem-show-case-bundle/page-extensions__show_case_product_extension__anotherSampleTextField____title": "Extension at the end", "@sage/xtrem-show-case-bundle/page-extensions__show_case_product_extension__aSampleTextField____title": "Extension field title", "@sage/xtrem-show-case-bundle/page-extensions__show_case_product_extension__biodegradabilityCategory____title": "Biodegradability Category", "@sage/xtrem-show-case-bundle/page-extensions__show_case_product_extension__deleteProduct____title": "Delete", "@sage/xtrem-show-case-bundle/page-extensions__show_case_product_extension__extendedBlock1____title": "Extended Block (Before)", "@sage/xtrem-show-case-bundle/page-extensions__show_case_product_extension__extendedBlock2____title": "Extended Block (After)", "@sage/xtrem-show-case-bundle/page-extensions__show_case_product_extension__extendedBlock3____title": "Extended Block", "@sage/xtrem-show-case-bundle/page-extensions__show_case_product_extension__extendedBlock4____title": "Extended Block", "@sage/xtrem-show-case-bundle/page-extensions__show_case_product_extension__extendedSection1____title": "Extended Section (Before)", "@sage/xtrem-show-case-bundle/page-extensions__show_case_product_extension__extendedSection2____title": "Extended Section (After)", "@sage/xtrem-show-case-bundle/page-extensions__show_case_product_extension__extendedText4____helperText": "This field will update the \"product\" field.", "@sage/xtrem-show-case-bundle/page-extensions__show_case_product_extension__extendedText4____title": "Extended Text Field", "@sage/xtrem-show-case-bundle/page-extensions__show_case_product_extension__releaseDate____title": "EXTENSION TITLE", "@sage/xtrem-show-case-bundle/page-extensions__show_case_product_extension__saveProduct____title": "Save", "@sage/xtrem-show-case-bundle/page-extensions__show_case_product_extension__saveProductQuick____title": "Save", "@sage/xtrem-show-case-bundle/page-extensions__static_content_field_extension__exampleField____helperText": "Extension Helper Text", "@sage/xtrem-show-case-bundle/page-extensions__static_content_field_extension__exampleField____title": "Extension Title", "@sage/xtrem-show-case-bundle/page-extensions__switch_extension__field____helperText": "Extension Helper Text", "@sage/xtrem-show-case-bundle/page-extensions__switch_extension__field____title": "Extension Title", "@sage/xtrem-show-case-bundle/page-extensions__table_extension__field____columnOverrides__title": "Provider Extended", "@sage/xtrem-show-case-bundle/page-extensions__table_extension__field____columns__columns__biodegradabilityCategory__description__title": "Description", "@sage/xtrem-show-case-bundle/page-extensions__table_extension__field____columns__columns__biodegradabilityCategory__description__title__2": "Energy to produce", "@sage/xtrem-show-case-bundle/page-extensions__table_extension__field____columns__title": "Transient ext column", "@sage/xtrem-show-case-bundle/page-extensions__table_extension__field____columns__title__2": "Transient ext column 2", "@sage/xtrem-show-case-bundle/page-extensions__table_extension__field____columns__title__3": "Transient number", "@sage/xtrem-show-case-bundle/page-extensions__table_extension__field____columns__title__biodegradabilityCategory__description": "Biodegradability category", "@sage/xtrem-show-case-bundle/page-extensions__table_extension__field____helperText": "Extension Helper Text", "@sage/xtrem-show-case-bundle/page-extensions__table_extension__field____mobileCard__line2__title": "Biodegradability Category", "@sage/xtrem-show-case-bundle/page-extensions__table_extension__field____mobileCard__title__title": "Description", "@sage/xtrem-show-case-bundle/page-extensions__table_extension__field____title": "Extension Title", "@sage/xtrem-show-case-bundle/page-extensions__table_with_detail_panel_extension__demoGridRowBlock____title": "Extension Title", "@sage/xtrem-show-case-bundle/page-extensions__table_with_detail_panel_extension__field____dropdownActions__title": "Extended action inserted at the end", "@sage/xtrem-show-case-bundle/page-extensions__table_with_detail_panel_extension__field____dropdownActions__title__2": "Extended action inserted after edit", "@sage/xtrem-show-case-bundle/page-extensions__table_with_detail_panel_extension__field____dropdownActions__title__3": "Extended action inserted before edit", "@sage/xtrem-show-case-bundle/page-extensions__table_with_detail_panel_extension__field____helperText": "Extension Helper Text", "@sage/xtrem-show-case-bundle/page-extensions__table_with_detail_panel_extension__field____inlineActions__title": "Extended action inserted at the end", "@sage/xtrem-show-case-bundle/page-extensions__table_with_detail_panel_extension__field____inlineActions__title__2": "Extended action inserted before edit", "@sage/xtrem-show-case-bundle/page-extensions__table_with_detail_panel_extension__field____inlineActions__title__3": "Extended action inserted after edit", "@sage/xtrem-show-case-bundle/page-extensions__table_with_detail_panel_extension__field____title": "Extension Title", "@sage/xtrem-show-case-bundle/page-extensions__text_extension__field____helperText": "Extension Helper Text", "@sage/xtrem-show-case-bundle/page-extensions__text_extension__field____title": "Extension Title", "@sage/xtrem-show-case-bundle/page-extensions__text_extension__fullWidth____helperText": "Override base field isFullWidth prop", "@sage/xtrem-show-case-bundle/page-extensions__text_extension__fullWidth____title": "Extension Former Full width", "@sage/xtrem-show-case-bundle/page-extensions__text_extension__mandatory____helperText": "Override base field isMandatory prop", "@sage/xtrem-show-case-bundle/page-extensions__text_extension__mandatory____title": "Extension Former Mandatory", "@sage/xtrem-show-case-bundle/page-extensions__toggle_buttons_extension__field____helperText": "Extension Helper Text", "@sage/xtrem-show-case-bundle/page-extensions__toggle_buttons_extension__field____title": "Extension Title", "@sage/xtrem-show-case-bundle/page-extensions__toggle_buttons_extension__inlineOption____helperText": "Extension: The options are not from an enum", "@sage/xtrem-show-case-bundle/page-extensions__toggle_buttons_extension__inlineOption____title": "Extension: Options set in file", "@sage/xtrem-show-case-bundle/pages__biodegradability_category____title": "Sample page - Biodegradability Category", "@sage/xtrem-show-case-bundle/pages__biodegradability_category__description____title": "Description", "@sage/xtrem-show-case-bundle/pages__biodegradability_category__localSourcing____title": "Local Sourcing", "@sage/xtrem-show-case-bundle/pages__biodegradability_category__percentageOfDegradability____title": "Percentage Of Degradability"}