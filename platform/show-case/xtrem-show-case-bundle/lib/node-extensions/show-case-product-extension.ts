import { decorators, NodeExtension } from '@sage/xtrem-core';
import * as xtremShowCase from '@sage/xtrem-show-case';
import * as xtremShowCaseBundle from '../../index';

@decorators.nodeExtension<ShowCaseProductExtension>({
    extends: () => xtremShowCase.nodes.ShowCaseProduct,
})
export class ShowCaseProductExtension extends NodeExtension<xtremShowCase.nodes.ShowCaseProduct> {
    @decorators.referenceProperty<ShowCaseProductExtension, 'biodegradabilityCategory'>({
        isStored: true,
        isNullable: true,
        isPublished: true,
        node: () => xtremShowCaseBundle.nodes.BiodegradabilityCategory,
    })
    readonly biodegradabilityCategory: Promise<xtremShowCaseBundle.nodes.BiodegradabilityCategory | null>;

    @decorators.stringPropertyOverride<ShowCaseProductExtension, 'description'>({})
    readonly description: Promise<string>;
}

declare module '@sage/xtrem-show-case/lib/nodes/show-case-product' {
    export interface ShowCaseProduct extends ShowCaseProductExtension {}
}
