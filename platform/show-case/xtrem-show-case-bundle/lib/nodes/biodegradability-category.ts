import { decimal, decorators, Node } from '@sage/xtrem-core';
import { defaultDecimalDataType, descriptionDataType } from '../data-types/data-types';

@decorators.node<BiodegradabilityCategory>({
    storage: 'sql',
    canCreate: true,
    canDelete: true,
    canRead: true,
    canSearch: true,
    canUpdate: true,
    isPublished: true,
})
export class BiodegradabilityCategory extends Node {
    @decorators.stringProperty<BiodegradabilityCategory, 'description'>({
        isStored: true,
        isPublished: true,
        dataType: () => descriptionDataType,
    })
    readonly description: Promise<string>;

    @decorators.decimalProperty<BiodegradabilityCategory, 'energyToProduce'>({
        isStored: true,
        isPublished: true,
        dataType: () => defaultDecimalDataType,
    })
    readonly energyToProduce: Promise<decimal>;

    @decorators.decimalProperty<BiodegradabilityCategory, 'percentageOfDegradability'>({
        isStored: true,
        isPublished: true,
        dataType: () => defaultDecimalDataType,
    })
    readonly percentageOfDegradability: Promise<decimal>;

    @decorators.booleanProperty<BiodegradabilityCategory, 'localSourcing'>({
        isStored: true,
        isPublished: true,
    })
    readonly localSourcing: Promise<boolean>;
}
