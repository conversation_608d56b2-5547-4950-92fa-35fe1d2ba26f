import { GraphApi } from '@sage/xtrem-show-case-bundle-api';
import { Checkbox } from '@sage/xtrem-show-case/lib/pages/checkbox';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.pageExtension<CheckboxExtension>({
    extends: '@sage/xtrem-show-case/Checkbox',
})
export class CheckboxExtension extends ui.PageExtension<Checkbox, GraphApi> {
    @ui.decorators.checkboxFieldOverride<CheckboxExtension>({
        title: 'Extension Title',
        helperText: 'Extension Helper Text',
        onClick() {
            this.$.showToast('onClick has been triggered!', { type: 'success' });
        },
        onClickAfter() {
            this.clickAfterTriggered.isHidden = false;
            setTimeout(() => {
                this.clickAfterTriggered.isHidden = true;
            }, 3000);
        },
        onChange() {
            this.$.showToast('onChange has been triggered!', { type: 'success' });
        },
        onChangeAfter() {
            this.changeAfterTriggered.isHidden = false;
            setTimeout(() => {
                this.changeAfterTriggered.isHidden = true;
            }, 3000);
        },
    })
    field: ui.fields.Checkbox;

    @ui.decorators.labelField<CheckboxExtension>({
        parent() {
            return this.fieldBlock;
        },
        isHidden: true,
        borderColor: '00B000',
        map() {
            return 'Click After was triggered';
        },
    })
    clickAfterTriggered: ui.fields.Label;

    @ui.decorators.labelField<CheckboxExtension>({
        parent() {
            return this.fieldBlock;
        },
        isHidden: true,
        borderColor: '00B000',
        map() {
            return 'Change After was triggered';
        },
    })
    changeAfterTriggered: ui.fields.Label;

    @ui.decorators.checkboxFieldOverride<CheckboxExtension>({
        title: 'Extension Mandatory',
        helperText: 'Extension Mandatory Helper Text',
    })
    mandatory: ui.fields.Checkbox;
}
