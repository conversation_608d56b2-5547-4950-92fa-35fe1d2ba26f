import { GraphApi } from '@sage/xtrem-show-case-bundle-api';
import { ShowCaseProduct } from '@sage/xtrem-show-case/lib/pages/show-case-product';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.pageExtension<ShowCaseProductExtension>({
    extends: '@sage/xtrem-show-case/ShowCaseProduct',
    navigationPanel: {
        listItem: {
            line2: ui.nestedFieldExtensions.reference({
                title: 'Biodegradability Category',
                node: '@sage/xtrem-show-case-bundle/BiodegradabilityCategory',
                bind: 'biodegradabilityCategory',
                valueField: 'description',
                insertBefore: 'description',
                canFilter: true,
            }),
            someRandomIcon: ui.nestedFieldExtensions.icon({
                bind: 'nothing',
                title: 'Some extension icon',
                isTransient: true,
                map() {
                    return 'tick';
                },
            }),
        },
        inlineActions: [
            {
                title: 'Extended action inserted at the end',
                refreshesMainList: 'record',
                async onClick() {
                    this.$.dialog.message('info', '', 'Changed name to banana');
                },
                icon: 'favourite',
            },
            {
                title: 'Extended action inserted before delete',
                refreshesMainList: 'record',
                async onClick() {
                    this.$.dialog.message('info', '', 'Changed name to banana');
                },
                icon: 'arrow_left_boxed',
                insertBefore: 'delete',
            },
            {
                title: 'Extended action inserted after delete',
                refreshesMainList: 'record',
                async onClick() {
                    this.$.dialog.message('info', '', 'Changed name to banana');
                },
                icon: 'info',
                insertAfter: 'delete',
            },
        ],
        dropdownActions: [
            {
                title: 'Extended action at the end',
                async onClick() {
                    this.$.dialog.message('info', 'Action at the end', 'Just a notification');
                },
                icon: 'info',
            },
            {
                title: 'Extended action inserted before someAction',
                async onClick() {
                    this.$.dialog.message('info', '', 'Just a notification');
                },
                icon: 'arrow_left_boxed',
                insertBefore: 'someAction',
            },
            {
                title: 'Extended action inserted after someAction',
                async onClick() {
                    this.$.dialog.message('info', '', 'Just a notification');
                },
                icon: 'favourite',
                insertAfter: 'someAction',
            },
        ],
    },
    onLoad() {
        // eslint-disable-next-line no-console
        console.log('on load extension called');
        // eslint-disable-next-line no-console
        this.myVeryFineMethod('open');
        this.block.title = 'This title is set from the extension!';
    },
    onClose() {
        // eslint-disable-next-line no-console
        console.log('on close extension called');
        // eslint-disable-next-line no-console
        this.myVeryFineMethod('close');
    },
    headerDropDownActions() {
        return [this.addProduct, this.saveProduct];
    },
    headerQuickActions() {
        return [this.deleteProduct, this.saveProductQuick];
    },
})
export class ShowCaseProductExtension extends ui.PageExtension<ShowCaseProduct, GraphApi> {
    myVeryFineMethod(eventName: string) {
        this.$.showToast(`A fine notification on ${eventName}`);
        this.aSampleTextField.isDisabled = true;
    }

    @ui.decorators.referenceField<ShowCaseProductExtension>({
        title: 'Biodegradability Category',
        node: '@sage/xtrem-show-case-bundle/BiodegradabilityCategory',
        isAutoSelectEnabled: true,
        insertBefore() {
            return this.description;
        },
        parent() {
            return this.block;
        },
        valueField: 'description',
        onClick() {
            this.listPrice.isDisabled = true;
        },
        onChange() {
            this.$.showToast('Changed');
        },
    })
    biodegradabilityCategory: ui.fields.Reference;

    @ui.decorators.textField<ShowCaseProductExtension>({
        title: 'Extension field title',
        isTransient: true,
        insertBefore() {
            return this.category;
        },
        parent() {
            return this.block;
        },
        onChange() {
            this.$.showToast('Changed');
        },
    })
    aSampleTextField: ui.fields.Text;

    @ui.decorators.textField<ShowCaseProductExtension>({
        isTransient: true,
        parent() {
            return this.block;
        },
        title: 'Extension at the end',
    })
    anotherSampleTextField: ui.fields.Text;

    @ui.decorators.block<ShowCaseProductExtension>({
        insertBefore() {
            return this.block;
        },
        isTransient: true,
        parent() {
            return this.section;
        },
        title: 'Extended Block (Before)',
    })
    extendedBlock1: ui.containers.Block;

    @ui.decorators.block<ShowCaseProductExtension>({
        isTransient: true,
        parent() {
            return this.section;
        },
        title: 'Extended Block (After)',
    })
    extendedBlock2: ui.containers.Block;

    @ui.decorators.section<ShowCaseProductExtension>({
        insertBefore() {
            return this.section;
        },
        isTransient: true,
        title: 'Extended Section (Before)',
    })
    extendedSection1: ui.containers.Section;

    @ui.decorators.block<ShowCaseProductExtension>({
        isTransient: true,
        parent() {
            return this.extendedSection1;
        },
        title: 'Extended Block',
    })
    extendedBlock3: ui.containers.Block;

    @ui.decorators.section<ShowCaseProductExtension>({
        isTransient: true,
        title: 'Extended Section (After)',
    })
    extendedSection2: ui.containers.Section;

    @ui.decorators.block<ShowCaseProductExtension>({
        isTransient: true,
        parent() {
            return this.extendedSection2;
        },
        title: 'Extended Block',
    })
    extendedBlock4: ui.containers.Block;

    @ui.decorators.textField<ShowCaseProductExtension>({
        isTransient: true,
        parent() {
            return this.extendedBlock4;
        },
        onChange() {
            this.product.value = this.extendedText4.value;
        },
        title: 'Extended Text Field',
        helperText: 'This field will update the "product" field.',
    })
    extendedText4: ui.fields.Text;

    @ui.decorators.dateFieldOverride<ShowCaseProductExtension>({
        title: 'EXTENSION TITLE',
        isDisabled: true,
    })
    releaseDate: ui.fields.Date;

    @ui.decorators.pageAction<ShowCaseProductExtension>({
        icon: 'add',
        title: 'Add',
        onClick() {
            this.$.dialog.message('info', 'Add action onClick', 'This is an extended action');
        },
        insertBefore() {
            return this.$standardDeleteAction;
        },
    })
    addProduct: ui.PageAction;

    @ui.decorators.pageAction<ShowCaseProductExtension>({
        icon: 'save',
        title: 'Save',
        onClick() {
            this.$.dialog.message('info', 'Save action onClick', 'This is an extended action');
        },
        insertAfter() {
            return this.$standardDeleteAction;
        },
    })
    saveProduct: ui.PageAction;

    @ui.decorators.pageAction<ShowCaseProductExtension>({
        icon: 'delete',
        title: 'Delete',
        onClick() {
            this.$.dialog.message('info', 'Delete action onClick', 'This is an extended action');
        },
        insertAfter() {
            return this.$standardDuplicateAction;
        },
    })
    deleteProduct: ui.PageAction;

    @ui.decorators.pageAction<ShowCaseProductExtension>({
        icon: 'save',
        title: 'Save',
        onClick() {
            this.$.dialog.message('info', 'Save action onClick', 'This is an extended action');
        },
        insertBefore() {
            return this.$standardDuplicateAction;
        },
    })
    saveProductQuick: ui.PageAction;
}

declare module '@sage/xtrem-show-case/lib/pages/show-case-product' {
    interface ShowCaseProduct extends ShowCaseProductExtension {}
}
