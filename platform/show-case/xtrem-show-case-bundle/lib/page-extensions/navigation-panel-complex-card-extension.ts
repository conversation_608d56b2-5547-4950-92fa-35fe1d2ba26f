import { Graph<PERSON><PERSON> } from '@sage/xtrem-show-case-bundle-api';
import { NavigationPanelComplexCard } from '@sage/xtrem-show-case/lib/pages/navigation-panel-complex-card';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.pageExtension<NavigationPanelComplexCardExtension>({
    extends: '@sage/xtrem-show-case/NavigationPanelComplexCard',
    navigationPanel: {
        optionsMenu: async () => {
            return [
                {
                    title: 'Only from Amazon (ext)',
                    graphQLFilter: { provider: { textField: { _eq: 'Amazon' } } },
                },
            ];
        },
    },
})
export class NavigationPanelComplexCardExtension extends ui.PageExtension<NavigationPanelComplexCard, GraphApi> {}

declare module '@sage/xtrem-show-case/lib/pages/navigation-panel-complex-card' {
    interface NavigationPanelComplexCard extends NavigationPanelComplexCardExtension {}
}
