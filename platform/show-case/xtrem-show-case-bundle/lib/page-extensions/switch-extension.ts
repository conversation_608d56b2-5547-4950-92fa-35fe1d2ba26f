import { GraphApi } from '@sage/xtrem-show-case-bundle-api';
import { Switch } from '@sage/xtrem-show-case/lib/pages/switch';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.pageExtension<SwitchExtension>({
    extends: '@sage/xtrem-show-case/Switch',
})
export class SwitchExtension extends ui.PageExtension<Switch, GraphApi> {
    @ui.decorators.switchFieldOverride<SwitchExtension>({
        title: 'Extension Title',
        helperText: 'Extension Helper Text',
        onClick() {
            this.$.showToast('onClick has been triggered!', { type: 'success' });
        },
        onClickAfter() {
            this.clickAfterTriggered.isHidden = false;
            setTimeout(() => {
                this.clickAfterTriggered.isHidden = true;
            }, 3000);
        },
        onChange() {
            this.$.showToast('onChange has been triggered!', { type: 'success' });
        },
        onChangeAfter() {
            this.changeAfterTriggered.isHidden = false;
            setTimeout(() => {
                this.changeAfterTriggered.isHidden = true;
            }, 3000);
        },
    })
    field: ui.fields.Switch;

    @ui.decorators.labelField<SwitchExtension>({
        parent() {
            return this.fieldBlock;
        },
        isHidden: true,
        borderColor: '00B000',
        map() {
            return 'Click After was triggered';
        },
    })
    clickAfterTriggered: ui.fields.Label;

    @ui.decorators.labelField<SwitchExtension>({
        parent() {
            return this.fieldBlock;
        },
        isHidden: true,
        borderColor: '00B000',
        map() {
            return 'Change After was triggered';
        },
    })
    changeAfterTriggered: ui.fields.Label;
}
