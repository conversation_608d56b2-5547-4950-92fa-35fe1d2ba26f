import { Graph<PERSON>pi } from '@sage/xtrem-show-case-bundle-api';
import { RadioButtons } from '@sage/xtrem-show-case/lib/pages/radio-buttons';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.pageExtension<RadioButtonsExtension>({
    extends: '@sage/xtrem-show-case/RadioButtons',
})
export class RadioButtonsExtension extends ui.PageExtension<RadioButtons, GraphApi> {
    @ui.decorators.radioFieldOverride<RadioButtonsExtension>({
        title: 'Extension Title',
        helperText: 'Extension Helper Text',
        onChange() {
            this.$.showToast('Extension onChange has been triggered!', { type: 'success' });
        },
        onChangeAfter() {
            if (this.field.value !== this.value.value) {
                this.value.value = this.field.value;
                this.changeAfterTriggered.isHidden = false;
                setTimeout(() => {
                    this.changeAfterTriggered.isHidden = true;
                }, 5000);
            }
        },
    })
    field: ui.fields.Radio;

    @ui.decorators.labelField<RadioButtonsExtension>({
        isTransient: true,
        parent() {
            return this.fieldBlock;
        },
        isHidden: true,
        borderColor: '00B000',
        map() {
            return 'onChangeAfter was triggered';
        },
    })
    changeAfterTriggered: ui.fields.Label;

    @ui.decorators.radioFieldOverride<RadioButtonsExtension>({
        title: 'Extension: Options set in file',
        helperText: 'Extension: The options are not from an enum',
        options: [
            'extension dog',
            'extension cat',
            'extension cow',
            'extension a very long option, just to test line breaks',
            'extension giraffe',
        ],
    })
    inlineOption: ui.fields.Radio;
}
