import { GraphApi } from '@sage/xtrem-show-case-bundle-api';
import { FileUpload } from '@sage/xtrem-show-case/lib/pages/file-upload';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.pageExtension<FileExtension>({
    extends: '@sage/xtrem-show-case/FileUpload',
})
export class FileExtension extends ui.PageExtension<FileUpload, GraphApi> {
    @ui.decorators.fileFieldOverride<FileExtension>({
        title: 'Extension Title',
        helperText: 'Extension Helper Text',
        onClick() {
            this.clickTriggered.isHidden = false;
            setTimeout(() => {
                this.clickTriggered.isHidden = true;
            }, 3000);
        },
        onClickAfter() {
            this.$.showToast('onClickAfter has been triggered!', { type: 'success' });
        },
        onChange() {
            this.changeTriggered.isHidden = false;
            setTimeout(() => {
                this.changeTriggered.isHidden = true;
            }, 3000);
        },
        onChangeAfter() {
            this.$.showToast('onChangeAfter has been triggered!', { type: 'success' });
        },
    })
    field: ui.fields.File;

    @ui.decorators.labelField<FileExtension>({
        parent() {
            return this.fieldBlock;
        },
        isHidden: true,
        borderColor: '00B000',
        map() {
            return 'Extension onClick was triggered';
        },
    })
    clickTriggered: ui.fields.Label;

    @ui.decorators.labelField<FileExtension>({
        parent() {
            return this.fieldBlock;
        },
        isHidden: true,
        borderColor: '00B000',
        map() {
            return 'Extension onChange was triggered';
        },
    })
    changeTriggered: ui.fields.Label;
}
