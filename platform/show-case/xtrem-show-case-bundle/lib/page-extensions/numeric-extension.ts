import { GraphApi } from '@sage/xtrem-show-case-bundle-api';
import { Numeric } from '@sage/xtrem-show-case/lib/pages/numeric';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.pageExtension<NumericExtension>({
    extends: '@sage/xtrem-show-case/Numeric',
})
export class NumericExtension extends ui.PageExtension<Numeric, GraphApi> {
    @ui.decorators.numericFieldOverride<NumericExtension>({
        title: 'Extension Title',
        helperText: 'Extension Helper Text',
        onClick() {
            this.$.showToast('Extension onClick has been triggered!', { type: 'success' });
        },
        onClickAfter() {
            this.clickAfterTriggered.isHidden = false;
            setTimeout(() => {
                this.clickAfterTriggered.isHidden = true;
            }, 3000);
        },
        onChange() {
            this.$.showToast('Extension onChange has been triggered!', { type: 'success' });
        },
        onChangeAfter() {
            this.changeAfterTriggered.isHidden = false;
            setTimeout(() => {
                this.changeAfterTriggered.isHidden = true;
            }, 3000);
        },
    })
    field: ui.fields.Numeric;

    @ui.decorators.labelField<NumericExtension>({
        isTransient: true,
        parent() {
            return this.fieldBlock;
        },
        isHidden: true,
        borderColor: '00B000',
        map() {
            return 'onClickAfter was triggered';
        },
    })
    clickAfterTriggered: ui.fields.Label;

    @ui.decorators.labelField<NumericExtension>({
        isTransient: true,
        parent() {
            return this.fieldBlock;
        },
        isHidden: true,
        borderColor: '00B000',
        map() {
            return 'onChangeAfter was triggered';
        },
    })
    changeAfterTriggered: ui.fields.Label;
}
