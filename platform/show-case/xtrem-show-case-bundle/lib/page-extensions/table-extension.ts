import { GraphApi, BiodegradabilityCategory } from '@sage/xtrem-show-case-bundle-api';
import { ShowCaseProduct, ShowCaseProvider } from '@sage/xtrem-show-case-api';
import { Table } from '@sage/xtrem-show-case/lib/pages/table';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.pageExtension<TableExtension>({
    extends: '@sage/xtrem-show-case/Table',
})
export class TableExtension extends ui.PageExtension<Table, GraphApi> {
    @ui.decorators.tableFieldOverride<TableExtension, ShowCaseProduct>({
        title: 'Extension Title',
        helperText: 'Extension Helper Text',
        async onChange() {
            this.calculateTableTotals();
            this.calculateSelectedTotal();

            this.$.showToast('Override Table > onChange');
            await this.sleep();
        },
        async onChangeAfter(_id: string | number) {
            this.$.showToast('Override Table > onChangeAfter');
            await this.sleep();
        },
        onRowSelected() {
            this.$.showToast('onRowSelected extension overrides', { type: 'success' });
            this.calculateSelectedTotal();
        },
        onRowSelectedAfter(_id: string | number, item: any) {
            this.rowSelectedAfterTriggered.isHidden = false;
            setTimeout(() => {
                this.rowSelectedAfterTriggered.isHidden = true;
            }, 3000);
        },
        onRowUnselected() {
            this.$.showToast('onRowUnselected extension overrides', { type: 'success' });
            this.calculateSelectedTotal();
        },
        onRowUnselectedAfter(_id: string | number, item: any) {
            this.rowUnselectedAfterTriggered.isHidden = false;
            setTimeout(() => {
                this.rowUnselectedAfterTriggered.isHidden = true;
            }, 3000);
        },
        mobileCard: {
            title: ui.nestedFields.text<TableExtension, ShowCaseProduct>({
                bind: 'description',
                title: 'Description',
            }),
            titleRight: null,
            line2: ui.nestedFields.reference<TableExtension, ShowCaseProduct, BiodegradabilityCategory>({
                bind: 'biodegradabilityCategory',
                title: 'Biodegradability Category',
                valueField: 'description',
            }),
            line2Right: undefined,
        },
        columnOverrides: [
            ui.nestedFieldOverrides.numeric<TableExtension, ShowCaseProduct>({
                bind: 'qty',
                scale: 1,
                insertBefore: 'biodegradabilityCategory',
                async onChange() {
                    this.$.showToast('Override Table > Quantity > onChange');
                    await this.sleep();
                },
                async onChangeAfter() {
                    this.$.showToast('Override Table > Quantity > onChangeAfter');
                    await this.sleep();
                },
            }),
            ui.nestedFieldOverrides.reference<TableExtension, ShowCaseProduct, ShowCaseProvider>({
                bind: 'provider',
                title: 'Provider Extended',
                valueField: 'textField',
                insertBefore: 'qty',
                onClick(id: string, raw: any) {
                    console.log('Override Table > Provider > onClick', { id, raw });
                },
                onChangeAfter(id: any, raw: any) {
                    console.log('Override Table > Provider > onChangeAfter', { id, raw });
                },
            }),
        ],
        columns: [
            ui.nestedFieldExtensions.reference<TableExtension, ShowCaseProduct, BiodegradabilityCategory>({
                bind: 'biodegradabilityCategory',
                valueField: 'description',
                node: '@sage/xtrem-show-case-bundle/BiodegradabilityCategory',
                insertBefore: 'product',
                columns: [
                    ui.nestedFields.text({ bind: 'description', title: 'Description' }),
                    ui.nestedFields.text({ bind: 'energyToProduce', title: 'Energy to produce' }),
                ],
                title: 'Biodegradability category',
                isAutoSelectEnabled: true,
            }),
            ui.nestedFieldExtensions.text<TableExtension, ShowCaseProduct>({
                bind: 'someTransientFieldAtTheEnd' as any,
                isTransient: true,
                title: 'Transient ext column',
            }),
            ui.nestedFieldExtensions.text<TableExtension, ShowCaseProduct>({
                bind: 'someTransientFieldAtTheVeryEnd' as any,
                isTransient: true,
                title: 'Transient ext column 2',
            }),
            ui.nestedFieldExtensions.numeric<TableExtension, ShowCaseProduct>({
                bind: 'someTransientFieldInTheMiddle' as any,
                isTransient: true,
                title: 'Transient number',
                insertBefore: 'provider',
                scale: 5,
            }),
        ],
    })
    field: ui.fields.Table;

    @ui.decorators.labelField<TableExtension>({
        isTransient: true,
        parent() {
            return this.fieldBlock;
        },
        isHidden: true,
        borderColor: '00B000',
        map() {
            return 'onChangeAfter was triggered';
        },
    })
    changeAfterTriggered: ui.fields.Label;

    @ui.decorators.labelField<TableExtension>({
        isTransient: true,
        parent() {
            return this.fieldBlock;
        },
        isHidden: true,
        borderColor: '00B000',
        map() {
            return 'onRowSelectedAfter was triggered';
        },
    })
    rowSelectedAfterTriggered: ui.fields.Label;

    @ui.decorators.labelField<TableExtension>({
        isTransient: true,
        parent() {
            return this.fieldBlock;
        },
        isHidden: true,
        borderColor: '00B000',
        map() {
            return 'onRowUnselectedAfter was triggered';
        },
    })
    rowUnselectedAfterTriggered: ui.fields.Label;

    sleep() {
        return new Promise<void>(resolve => {
            return setTimeout(resolve, 250);
        });
    }
}
