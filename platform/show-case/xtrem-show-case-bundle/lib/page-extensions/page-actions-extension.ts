import { GraphApi } from '@sage/xtrem-show-case-bundle-api';
import { PageActions } from '@sage/xtrem-show-case/lib/pages/page-actions';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.pageExtension<PageActionsExtension>({
    extends: '@sage/xtrem-show-case/PageActions',
    navigationPanel: {
        dropdownActions: [
            {
                icon: 'graduation_hat',
                id: 'extensionAction1',
                insertAfter: 'separator1',
                onClick() {
                    ui.console.log('This extension action should be inserted after the first separator.');
                },
                title: 'Extension Action 1',
            },
            ui.menuSeparator({ id: 'extensionSeparator1', insertAfter: 'extensionAction1' }),
            {
                icon: 'scan',
                id: 'extensionAction2',
                insertAfter: 'extensionSeparator1',
                onClick() {
                    ui.console.log('This extension action should be inserted after the extension separator.');
                },
                title: 'Extension Action 2',
            },
        ],
    },
    headerDropDownActions() {
        return [this.extensionAction1, this.extensionMenuSeparator1, this.extensionAction2];
    }
})
export class PageActionsExtension extends ui.PageExtension<PageActions, GraphApi> {
    /* ********************** */
    /* Page Actions Extension */
    /* ********************** */

    @ui.decorators.pageAction<PageActionsExtension>({
        icon: 'camera',
        insertAfter() {
            return this.menuSeparator1;
        },
        onClick() {
            ui.console.log('This extension action should be inserted after the first separator.');
        },
        title: 'Extension Action 1',
    })
    extensionAction1: ui.PageAction;

    @ui.decorators.pageAction<PageActionsExtension>({
        isMenuSeparator: true,
        insertAfter() {
            return this.extensionAction1;
        },
    })
    extensionMenuSeparator1: ui.PageAction;

    @ui.decorators.pageAction<PageActionsExtension>({
        icon: 'target',
        insertAfter() {
            return this.extensionMenuSeparator1;
        },
        onClick() {
            ui.console.log('This extension action should be inserted after the extension separator.');
        },
        title: 'Extension Action 2',
    })
    extensionAction2: ui.PageAction;

    /* ************************ */
    /* Products Table Extension */
    /* ************************ */

    @ui.decorators.tableFieldOverride<PageActionsExtension>({
        dropdownActions: [
            {
                icon: 'palm_tree',
                id: 'extensionAction1',
                insertAfter: 'separator1',
                onClick() {
                    ui.console.log('This extension action should be inserted after the first separator.');
                },
                title: 'Extension Action 1',
            },
            ui.menuSeparator({ id: 'extensionSeparator1', insertAfter: 'extensionAction1' }),
            {
                icon: 'video',
                id: 'extensionAction2',
                insertAfter: 'extensionSeparator1',
                onClick() {
                    ui.console.log('This extension action should be inserted after the extension separator.');
                },
                title: 'Extension Action 2',
            },
        ],
    })
    products: ui.fields.Table;
}
