import { GraphApi } from '@sage/xtrem-show-case-bundle-api';
import { Label } from '@sage/xtrem-show-case/lib/pages/label';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.pageExtension<LabelExtension>({
    extends: '@sage/xtrem-show-case/Label',
})
export class LabelExtension extends ui.PageExtension<Label, GraphApi> {
    @ui.decorators.labelFieldOverride<LabelExtension>({
        title: 'Extension Title',
        helperText: 'Extension Helper Text',
        onClick() {
            this.$.showToast('Extension onClick has been triggered!', { type: 'success' });
        },
        onClickAfter() {
            this.clickAfterTriggered.isHidden = false;
            setTimeout(() => {
                this.clickAfterTriggered.isHidden = true;
            }, 3000);
        },
    })
    field: ui.fields.Label;

    @ui.decorators.labelField<LabelExtension>({
        isTransient: true,
        parent() {
            return this.fieldBlock;
        },
        isHidden: true,
        borderColor: '00B000',
        map() {
            return 'onClickAfter was triggered';
        },
    })
    clickAfterTriggered: ui.fields.Label;
}
