import { GraphApi } from '@sage/xtrem-show-case-bundle-api';
import { Link } from '@sage/xtrem-show-case/lib/pages/link';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.pageExtension<LinkExtension>({
    extends: '@sage/xtrem-show-case/Link',
})
export class LinkExtension extends ui.PageExtension<Link, GraphApi> {
    @ui.decorators.linkFieldOverride<LinkExtension>({
        title: 'Extension Title',
        helperText: 'Extension Helper Text',
        onClick() {
            this.$.showToast('Extension onClick has been triggered!', { type: 'success' });
        },
        onClickAfter() {
            this.clickAfterTriggered.isHidden = false;
            setTimeout(() => {
                this.clickAfterTriggered.isHidden = true;
            }, 3000);
        },
        page: '',
    })
    field: ui.fields.Link;

    @ui.decorators.labelField<LinkExtension>({
        isTransient: true,
        parent() {
            return this.fieldBlock;
        },
        isHidden: true,
        borderColor: '00B000',
        map() {
            return 'onClickAfter was triggered';
        },
    })
    clickAfterTriggered: ui.fields.Label;
}
