import { GraphApi } from '@sage/xtrem-show-case-bundle-api';
import { Text } from '@sage/xtrem-show-case/lib/pages/text';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.pageExtension<TextExtension>({
    extends: '@sage/xtrem-show-case/Text',
})
export class TextExtension extends ui.PageExtension<Text, GraphApi> {
    @ui.decorators.textFieldOverride<TextExtension>({
        title: 'Extension Title',
        helperText: 'Extension Helper Text',
        onClick() {
            this.$.showToast('Extension onClick has been triggered!', { type: 'success' });
        },
        onClickAfter() {
            this.clickAfterTriggered.isHidden = false;
            setTimeout(() => {
                this.clickAfterTriggered.isHidden = true;
            }, 3000);
        },
        onChange() {
            this.$.showToast('onChange has been triggered!', { type: 'success' });
        },
        onChangeAfter() {
            this.changeAfterTriggered.isHidden = false;
            setTimeout(() => {
                this.changeAfterTriggered.isHidden = true;
            }, 3000);
        },
    })
    field: ui.fields.Text;

    @ui.decorators.labelField<TextExtension>({
        isTransient: true,
        parent() {
            return this.fieldBlock;
        },
        isHidden: true,
        borderColor: '00B000',
        map() {
            return 'Click After was triggered';
        },
    })
    clickAfterTriggered: ui.fields.Label;

    @ui.decorators.labelField<TextExtension>({
        parent() {
            return this.fieldBlock;
        },
        isHidden: true,
        borderColor: '00B000',
        map() {
            return 'Change After was triggered';
        },
    })
    changeAfterTriggered: ui.fields.Label;

    @ui.decorators.textFieldOverride<TextExtension>({
        title: 'Extension Former Mandatory',
        helperText: 'Override base field isMandatory prop',
        isMandatory: false,
    })
    mandatory: ui.fields.Text;

    @ui.decorators.textFieldOverride<TextExtension>({
        title: 'Extension Former Full width',
        helperText: 'Override base field isFullWidth prop',
        isFullWidth: false,
    })
    fullWidth: ui.fields.Text;
}
