import { Graph<PERSON><PERSON> } from '@sage/xtrem-show-case-bundle-api';
import { StaticContentField } from '@sage/xtrem-show-case/lib/pages/static-content-field';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.pageExtension<StaticContentFieldExtension>({
    extends: '@sage/xtrem-show-case/StaticContentField',
})
export class StaticContentFieldExtension extends ui.PageExtension<StaticContentField, GraphApi> {
    @ui.decorators.staticContentFieldOverride<StaticContentFieldExtension>({
        title: 'Extension Title',
        helperText: 'Extension Helper Text',
        onClick() {
            this.clickTriggered.isHidden = false;
            setTimeout(() => {
                this.clickTriggered.isHidden = true;
            }, 3000);
        },
        onClickAfter() {
            this.$.showToast('onClickAfter has been triggered!', { type: 'success' });
        },
    })
    exampleField: ui.fields.StaticContent;

    @ui.decorators.labelField<StaticContentFieldExtension>({
        parent() {
            return this.block;
        },
        isTransient: true,
        isHidden: true,
        borderColor: '00B000',
        map() {
            return 'Click was triggered';
        },
    })
    clickTriggered: ui.fields.Label;
}
