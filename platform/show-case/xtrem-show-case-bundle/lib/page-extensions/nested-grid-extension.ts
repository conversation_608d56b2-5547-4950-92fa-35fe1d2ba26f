import { Graph<PERSON><PERSON> } from '@sage/xtrem-show-case-bundle-api';
import { ShowCaseOrder, ShowCaseInvoice, ShowCaseInvoiceLine } from '@sage/xtrem-show-case-api';
// @ts-ignore
import { NestedGrid } from '@sage/xtrem-show-case/lib/pages/nested-grid';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.pageExtension<NestedGridExtension>({
    extends: '@sage/xtrem-show-case/NestedGrid',
})
export class NestedGridExtension extends ui.PageExtension<NestedGrid, GraphApi> {
    @ui.decorators.nestedGridFieldOverride<NestedGridExtension, [ShowCaseOrder, ShowCaseInvoice, ShowCaseInvoiceLine]>({
        title: 'Extension Title',
        helperText: 'Extension Helper Text',
        onChange() {
            this.$.showToast('onChange extension overrides', { type: 'success' });
        },
        onChangeAfter(_id: string | number) {
            this.changeAfterTriggered.isHidden = false;
            setTimeout(() => {
                this.changeAfterTriggered.isHidden = true;
            }, 3000);
        },
        onRowSelected() {
            this.$.showToast('onRowSelected extension overrides', { type: 'success' });
        },
        onRowSelectedAfter(_id: string | number, item: any) {
            this.rowSelectedAfterTriggered.isHidden = false;
            setTimeout(() => {
                this.rowSelectedAfterTriggered.isHidden = true;
            }, 3000);
        },
        onRowUnselected() {
            this.$.showToast('onRowUnselected extension overrides', { type: 'success' });
        },
        onRowUnselectedAfter(_id: string | number, item: any) {
            this.rowUnselectedAfterTriggered.isHidden = false;
            setTimeout(() => {
                this.rowUnselectedAfterTriggered.isHidden = true;
            }, 3000);
        },
        levels: [
            {
                columns: [
                    ui.nestedFieldExtensions.reference<NestedGridExtension, ShowCaseOrder>({
                        bind: 'customer',
                        valueField: 'name',
                        node: '@sage/xtrem-show-case/ShowCaseCustomer',
                        insertBefore: '_id',
                    }),
                ],
                columnOverrides: [
                    ui.nestedFieldOverrides.date<NestedGridExtension, ShowCaseOrder>({
                        bind: 'orderDate',
                        title: 'Order Date Extended',
                        onChangeAfter() {
                            this.$.showToast('This is a notification triggered by the extension');
                        },
                    }),
                ],
            },
            {
                columns: [
                    ui.nestedFieldExtensions.numeric<NestedGridExtension, ShowCaseInvoice>({ bind: 'totalProductQty' }),
                ],
                columnOverrides: [
                    ui.nestedFieldOverrides.date<NestedGridExtension, ShowCaseInvoice>({
                        bind: 'purchaseDate',
                        title: 'Purchase Date Extended',
                        onClick(rowId: string, rowData: any) {
                            console.log('click extended');
                        },
                    }),
                ],
            },
            {},
        ],
    })
    field: ui.fields.NestedGrid<[ShowCaseOrder, ShowCaseInvoice, ShowCaseInvoiceLine], NestedGridExtension>;

    @ui.decorators.labelField<NestedGridExtension>({
        isTransient: true,
        parent() {
            return this.fieldBlock;
        },
        isHidden: true,
        borderColor: '00B000',
        map() {
            return 'onChangeAfter was triggered';
        },
    })
    changeAfterTriggered: ui.fields.Label;

    @ui.decorators.labelField<NestedGridExtension>({
        isTransient: true,
        parent() {
            return this.fieldBlock;
        },
        isHidden: true,
        borderColor: '00B000',
        map() {
            return 'onRowSelectedAfter was triggered';
        },
    })
    rowSelectedAfterTriggered: ui.fields.Label;

    @ui.decorators.labelField<NestedGridExtension>({
        isTransient: true,
        parent() {
            return this.fieldBlock;
        },
        isHidden: true,
        borderColor: '00B000',
        map() {
            return 'onRowUnselectedAfter was triggered';
        },
    })
    rowUnselectedAfterTriggered: ui.fields.Label;

    @ui.decorators.nestedGridFieldOverride<NestedGridExtension>({
        title: 'Extension Title',
        helperText: 'Extension Helper Text',
        onChange() {
            this.$.showToast('onChange extension overrides', { type: 'success' });
        },
        onChangeAfter(_id: string | number) {
            this.changeAfterTriggeredActivation.isHidden = false;
            setTimeout(() => {
                this.changeAfterTriggeredActivation.isHidden = true;
            }, 3000);
        },
        onRowActivated() {
            this.$.showToast('onRowActivated extension overrides', { type: 'success' });
        },
        onRowActivatedAfter(_id: string | number, item: any) {
            this.rowActivatedAfterTriggered.isHidden = false;
            setTimeout(() => {
                this.rowActivatedAfterTriggered.isHidden = true;
            }, 3000);
        },
        onRowDeactivated() {
            this.$.showToast('onRowDeactivated extension overrides', { type: 'success' });
        },
        onRowDeactivatedAfter(_id: string | number, item: any) {
            this.rowDeactivatedAfterTriggered.isHidden = false;
            setTimeout(() => {
                this.rowDeactivatedAfterTriggered.isHidden = true;
            }, 3000);
        },
    })
    activatableField: ui.fields.NestedGrid;

    @ui.decorators.labelField<NestedGridExtension>({
        isTransient: true,
        parent() {
            return this.activationBlock;
        },
        isHidden: true,
        borderColor: '00B000',
        map() {
            return 'onChangeAfter was triggered';
        },
    })
    changeAfterTriggeredActivation: ui.fields.Label;

    @ui.decorators.labelField<NestedGridExtension>({
        isTransient: true,
        parent() {
            return this.activationBlock;
        },
        isHidden: true,
        borderColor: '00B000',
        map() {
            return 'onRowActivatedAfter was triggered';
        },
    })
    rowActivatedAfterTriggered: ui.fields.Label;

    @ui.decorators.labelField<NestedGridExtension>({
        isTransient: true,
        parent() {
            return this.activationBlock;
        },
        isHidden: true,
        borderColor: '00B000',
        map() {
            return 'onRowDeactivatedAfter was triggered';
        },
    })
    rowDeactivatedAfterTriggered: ui.fields.Label;
}
