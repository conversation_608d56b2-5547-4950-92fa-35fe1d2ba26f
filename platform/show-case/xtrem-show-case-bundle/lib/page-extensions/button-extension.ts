import { GraphApi } from '@sage/xtrem-show-case-bundle-api';
import { Button } from '@sage/xtrem-show-case/lib/pages/button';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.pageExtension<ButtonExtension>({
    extends: '@sage/xtrem-show-case/Button',
})
export class ButtonExtension extends ui.PageExtension<Button, GraphApi> {
    @ui.decorators.buttonFieldOverride<ButtonExtension>({
        title: 'Extension Title',
        helperText: 'Extension Helper Text',
        map(value) {
            return `Extension ${value}`;
        },
        onClick() {
            this.$.showToast('onClick has been triggered!', { type: 'success' });
        },
        onClickAfter() {
            this.clickAfterTriggered.isHidden = false;
            setTimeout(() => {
                this.clickAfterTriggered.isHidden = true;
            }, 3000);
        },
    })
    field: ui.fields.Button;

    @ui.decorators.labelField<ButtonExtension>({
        parent() {
            return this.fieldBlock;
        },
        isHidden: true,
        borderColor: '00B000',
        map() {
            return 'Click After was triggered';
        },
    })
    clickAfterTriggered: ui.fields.Label;
}
