import { Graph<PERSON><PERSON> } from '@sage/xtrem-show-case-bundle-api';
import { ActionButtons } from '@sage/xtrem-show-case/lib/pages/action-buttons';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.pageExtension<ActionButtonsExtension>({
    extends: '@sage/xtrem-show-case/ActionButtons',
})
export class ActionButtonsExtension extends ui.PageExtension<ActionButtons, GraphApi> {
    @ui.decorators.pageActionOverride<ActionButtonsExtension>({
        onClick() {
            this.$.showToast('onClick overriden', { type: 'success' });
        },
        onClickAfter() {
            this.$.showToast('onClickAfter', { type: 'success' });
        },
    })
    businessAction1: ui.PageAction;
}
