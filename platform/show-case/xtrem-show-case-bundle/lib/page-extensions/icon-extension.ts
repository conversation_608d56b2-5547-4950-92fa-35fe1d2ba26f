import { GraphApi } from '@sage/xtrem-show-case-bundle-api';
import { Icon } from '@sage/xtrem-show-case/lib/pages/icon';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.pageExtension<IconExtension>({
    extends: '@sage/xtrem-show-case/Icon',
})
export class IconExtension extends ui.PageExtension<Icon, GraphApi> {
    @ui.decorators.iconFieldOverride<IconExtension>({
        title: 'Extension Title',
        helperText: 'Extension Helper Text',
        onClick() {
            this.$.showToast('Extension onClick has been triggered!', { type: 'success' });
        },
        onClickAfter() {
            this.clickAfterTriggered.isHidden = false;
            setTimeout(() => {
                this.clickAfterTriggered.isHidden = true;
            }, 3000);
        },
    })
    field: ui.fields.Icon;

    @ui.decorators.labelField<IconExtension>({
        isTransient: true,
        parent() {
            return this.fieldBlock;
        },
        isHidden: true,
        borderColor: '00B000',
        map() {
            return 'Click Afterwas triggered';
        },
    })
    clickAfterTriggered: ui.fields.Label;
}
