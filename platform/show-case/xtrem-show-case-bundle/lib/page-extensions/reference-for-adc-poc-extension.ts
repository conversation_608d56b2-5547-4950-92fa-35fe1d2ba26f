import { GraphApi } from '@sage/xtrem-show-case-bundle-api';
import { ReferenceForAdcPoc } from '@sage/xtrem-show-case/lib/pages/reference-for-adc-poc';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.pageExtension<ReferenceForAdcPocExtension>({
    extends: '@sage/xtrem-show-case/ReferenceForAdcPoc',
})
export class ReferenceForAdcPocExtension extends ui.PageExtension<ReferenceForAdcPoc, GraphApi> {
    @ui.decorators.referenceFieldOverride<ReferenceForAdcPocExtension>({
        title: 'Extension Title',
        helperText: 'Extension Helper Text',
        async onChangeAfter() {
            this.inputValueChangeAfterTriggered.isHidden = false;
            setTimeout(() => {
                this.inputValueChangeAfterTriggered.isHidden = true;
            }, 3000);
        },
    })
    field1: ui.fields.Reference;

    @ui.decorators.labelField<ReferenceForAdcPocExtension>({
        isTransient: true,
        parent() {
            return this.fieldBlock;
        },
        isHidden: true,
        borderColor: '00B000',
        map() {
            return 'Input Value Change After was triggered';
        },
    })
    inputValueChangeAfterTriggered: ui.fields.Label;
}
