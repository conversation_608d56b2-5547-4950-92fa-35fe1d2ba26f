import { GraphApi } from '@sage/xtrem-show-case-bundle-api';
import { Progress } from '@sage/xtrem-show-case/lib/pages/progress';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.pageExtension<ProgressExtension>({
    extends: '@sage/xtrem-show-case/Progress',
})
export class ProgressExtension extends ui.PageExtension<Progress, GraphApi> {
    @ui.decorators.progressFieldOverride<ProgressExtension>({
        title: 'Extension Title',
        helperText: 'Extension Helper Text',
        onClick() {
            this.$.showToast('Extension onClick has been triggered!', { type: 'success' });
        },
        onClickAfter() {
            this.clickAfterTriggered.isHidden = false;
            setTimeout(() => {
                this.clickAfterTriggered.isHidden = true;
            }, 3000);
        },
    })
    field: ui.fields.Progress;

    @ui.decorators.labelField<ProgressExtension>({
        isTransient: true,
        parent() {
            return this.fieldBlock;
        },
        isHidden: true,
        borderColor: '00B000',
        map() {
            return 'onClickAfter was triggered';
        },
    })
    clickAfterTriggered: ui.fields.Label;
}
