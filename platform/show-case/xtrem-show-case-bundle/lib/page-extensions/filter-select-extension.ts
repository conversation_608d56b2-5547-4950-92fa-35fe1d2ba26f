import { GraphApi } from '@sage/xtrem-show-case-bundle-api';
import { FilterSelect } from '@sage/xtrem-show-case/lib/pages/filter-select';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.pageExtension<FilterSelectExtension>({
    extends: '@sage/xtrem-show-case/FilterSelect',
})
export class FilterSelectExtension extends ui.PageExtension<FilterSelect, GraphApi> {
    @ui.decorators.filterSelectFieldOverride<FilterSelectExtension>({
        title: 'Extension Title',
        helperText: 'Extension Helper Text',
        onClick() {
            this.$.showToast('onClick has been triggered!', { type: 'success' });
        },
        onClickAfter() {
            this.clickAfterTriggered.isHidden = false;
            setTimeout(() => {
                this.clickAfterTriggered.isHidden = true;
            }, 3000);
        },
        onChange() {
            this.$.showToast('onChange has been triggered!', { type: 'success' });
        },
        onChangeAfter() {
            this.changeAfterTriggered.isHidden = false;
            setTimeout(() => {
                this.changeAfterTriggered.isHidden = true;
            }, 3000);
        },
    })
    field: ui.fields.FilterSelect;

    @ui.decorators.labelField<FilterSelectExtension>({
        parent() {
            return this.block;
        },
        isHidden: true,
        borderColor: '00B000',
        map() {
            return 'Extension onClickAfter was triggered';
        },
    })
    clickAfterTriggered: ui.fields.Label;

    @ui.decorators.labelField<FilterSelectExtension>({
        parent() {
            return this.block;
        },
        isHidden: true,
        borderColor: '00B000',
        map() {
            return 'Extension onChangeAfter was triggered';
        },
    })
    changeAfterTriggered: ui.fields.Label;

    @ui.decorators.filterSelectFieldOverride<FilterSelectExtension>({
        size: 'large',
        title: 'Extension Large',
        helperText: 'Former small size',
    })
    small: ui.fields.FilterSelect;

    @ui.decorators.filterSelectFieldOverride<FilterSelectExtension>({
        size: 'small',
        title: 'Extension Small',
        helperText: 'Former medium size',
    })
    medium: ui.fields.FilterSelect;

    @ui.decorators.filterSelectFieldOverride<FilterSelectExtension>({
        size: 'medium',
        title: 'Extension Medium',
        helperText: 'Former large size',
    })
    large: ui.fields.FilterSelect;

    @ui.decorators.filterSelectFieldOverride<FilterSelectExtension>({
        title: 'Extension: Restricted Results by filter',
        helperText: 'Extension: former filter callback',
        filter: {
            provider: {
                textField: 'Ali Express',
            },
        },
    })
    restrictedResultByFilter: ui.fields.FilterSelect;

    @ui.decorators.filterSelectFieldOverride<FilterSelectExtension>({
        title: 'Extension: Restricted Results by filter callback',
        helperText: 'Extension: former filter',
        filter() {
            return {
                provider: {
                    textField: {
                        _regex: 'zon',
                    },
                },
            };
        },
    })
    restrictedResultByCallbackFilter: ui.fields.FilterSelect;

    @ui.decorators.filterSelectFieldOverride<FilterSelectExtension>({
        helperText: "It turns out Extension doesn't like meat",
        title: 'Extension: Filter Select with Custom Validation',
        validation(value: string) {
            if (value) {
                return value.toLowerCase().includes('veal') || value.toLowerCase().includes('beef')
                    ? `Eww, Extension doesn't like meat.`
                    : '';
            }
        },
    })
    validated: ui.fields.FilterSelect;
}
