import { GraphApi } from '@sage/xtrem-show-case-bundle-api';
import { ToggleButtons } from '@sage/xtrem-show-case/lib/pages/toggle-buttons';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.pageExtension<ToggleButtonsExtension>({
    extends: '@sage/xtrem-show-case/ToggleButtons',
})
export class ToggleButtonsExtension extends ui.PageExtension<ToggleButtons, GraphApi> {
    @ui.decorators.toggleFieldOverride<ToggleButtonsExtension>({
        title: 'Extension Title',
        helperText: 'Extension Helper Text',
        onClick() {
            this.$.showToast('Extension onClick has been triggered!', { type: 'success' });
        },
        onClickAfter() {
            this.clickAfterTriggered.isHidden = false;
            setTimeout(() => {
                this.clickAfterTriggered.isHidden = true;
            }, 3000);
        },
        onChange() {
            this.$.showToast('Extension onChange has been triggered!', { type: 'success' });
        },
        onChangeAfter() {
            if (this.field.value !== this.value.value) {
                this.value.value = this.field.value;
                this.changeTriggered.isHidden = false;
                setTimeout(() => {
                    this.changeTriggered.isHidden = true;
                }, 5000);
            }
        },
    })
    field: ui.fields.Toggle;

    @ui.decorators.labelField<ToggleButtonsExtension>({
        isTransient: true,
        parent() {
            return this.fieldBlock;
        },
        isHidden: true,
        borderColor: '00B000',
        map() {
            return 'onClickAfter was triggered';
        },
    })
    clickAfterTriggered: ui.fields.Label;

    @ui.decorators.labelFieldOverride<ToggleButtonsExtension>({
        borderColor: '00B000',
        map() {
            return 'onChangeAfter was triggered';
        },
    })
    changeTriggered: ui.fields.Label;

    @ui.decorators.toggleFieldOverride<ToggleButtonsExtension>({
        title: 'Extension: Options set in file',
        helperText: 'Extension: The options are not from an enum',
        options: [
            'extension dog',
            'extension cat',
            'extension cow',
            'extension a very long option, just to test line breaks',
            'extension giraffe',
        ],
    })
    inlineOption: ui.fields.Toggle;
}
