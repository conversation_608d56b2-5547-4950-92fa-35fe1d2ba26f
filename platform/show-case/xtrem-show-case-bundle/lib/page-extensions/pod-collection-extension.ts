import { Graph<PERSON><PERSON> } from '@sage/xtrem-show-case-bundle-api';
import { ShowCaseInvoice, ShowCaseInvoiceLine } from '@sage/xtrem-show-case-api';
import { PodCollection } from '@sage/xtrem-show-case/lib/pages/pod-collection';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.pageExtension<PodCollectionExtension>({
    extends: '@sage/xtrem-show-case/PodCollection',
})
export class PodCollectionExtension extends ui.PageExtension<PodCollection, GraphApi> {
    @ui.decorators.podCollectionFieldOverride<PodCollectionExtension>({
        title: 'Extension Title',
        helperText: 'Extension Helper Text',
        onClick() {
            this.clickTriggered.isHidden = false;
            setTimeout(() => {
                this.clickTriggered.isHidden = true;
            }, 3000);
        },
        onClickAfter() {
            this.$.showToast('onClickAfter has been triggered!', { type: 'info' });
        },
        onChange() {
            this.changeTriggered.isHidden = false;
            setTimeout(() => {
                this.changeTriggered.isHidden = true;
            }, 3000);
        },
        onChangeAfter() {
            this.$.showToast('onChangeAfter has been triggered!', { type: 'info' });
        },
        onRowSelected(_id) {
            this.rowSelectedTriggered.isHidden = false;
            setTimeout(() => {
                this.rowSelectedTriggered.isHidden = true;
            }, 3000);
        },
        onRowSelectedAfter(_id) {
            this.$.showToast(`Extension: Pod selected with ID ${_id}`, { type: 'info' });
        },
        onRowUnselected(_id) {
            this.rowUnselectedTriggered.isHidden = false;
            setTimeout(() => {
                this.rowUnselectedTriggered.isHidden = true;
            }, 3000);
        },
        onRowUnselectedAfter(_id) {
            this.$.showToast(`Extension: Pod unselected with ID ${_id}`, { type: 'info' });
        },
        onRecordAdded() {
            this.recordAddedTriggered.isHidden = false;
            setTimeout(() => {
                this.recordAddedTriggered.isHidden = true;
            }, 3000);
        },
        onRecordAddedAfter() {
            this.$.showToast(`Extension: A new pod was added`, { type: 'info' });
        },
        onRecordRemoved(_id) {
            this.recordRemovedTriggered.isHidden = false;
            setTimeout(() => {
                this.recordRemovedTriggered.isHidden = true;
            }, 3000);
        },
        onRecordRemovedAfter(_id) {
            this.$.showToast(`Extension: A pod was removed with ID: ${_id} :-(`, { type: 'info' });
        },
        onRecordClick(_id) {
            this.recordRemovedTriggered.isHidden = false;
            setTimeout(() => {
                this.recordRemovedTriggered.isHidden = true;
            }, 3000);
        },
        onRecordClickAfter(_id) {
            this.$.showToast('Extension: record clicked triggered!', { type: 'info' });
        },
        canSelect: true,
        canRemoveRecord: true,
        canAddRecord: true,
        columns: [
            ui.nestedFieldExtensions.reference<PodCollection, ShowCaseInvoiceLine, ShowCaseInvoice>({
                bind: 'invoice',
                title: 'Invoice',
                insertBefore: 'orderQuantity',
                valueField: 'purchaseDate',
                node: '@sage/xtrem-show-case/ShowCaseInvoice',
            }),
        ],
    })
    lines: ui.fields.PodCollection;

    @ui.decorators.labelField<PodCollectionExtension>({
        parent() {
            return this.fieldBlock;
        },
        isHidden: true,
        isTransient: true,
        borderColor: '00B000',
        map() {
            return 'Click After was triggered';
        },
    })
    clickTriggered: ui.fields.Label;

    @ui.decorators.labelField<PodCollectionExtension>({
        parent() {
            return this.fieldBlock;
        },
        isHidden: true,
        isTransient: true,
        borderColor: '00B000',
        map() {
            return 'Change was triggered';
        },
    })
    changeTriggered: ui.fields.Label;

    @ui.decorators.labelField<PodCollectionExtension>({
        parent() {
            return this.fieldBlock;
        },
        isHidden: true,
        isTransient: true,
        borderColor: '00B000',
        map() {
            return 'RowSelected was triggered';
        },
    })
    rowSelectedTriggered: ui.fields.Label;

    @ui.decorators.labelField<PodCollectionExtension>({
        parent() {
            return this.fieldBlock;
        },
        isHidden: true,
        isTransient: true,
        borderColor: '00B000',
        map() {
            return 'RowUnselected was triggered';
        },
    })
    rowUnselectedTriggered: ui.fields.Label;

    @ui.decorators.labelField<PodCollectionExtension>({
        parent() {
            return this.fieldBlock;
        },
        isHidden: true,
        isTransient: true,
        borderColor: '00B000',
        map() {
            return 'RecordAdded was triggered';
        },
    })
    recordAddedTriggered: ui.fields.Label;

    @ui.decorators.labelField<PodCollectionExtension>({
        parent() {
            return this.fieldBlock;
        },
        isHidden: true,
        isTransient: true,
        borderColor: '00B000',
        map() {
            return 'RecordRemoved was triggered';
        },
    })
    recordRemovedTriggered: ui.fields.Label;

    @ui.decorators.labelField<PodCollectionExtension>({
        parent() {
            return this.fieldBlock;
        },
        isHidden: true,
        isTransient: true,
        borderColor: '00B000',
        map() {
            return 'RecordClicked was triggered';
        },
    })
    recordClickedTriggered: ui.fields.Label;
}

declare module '@sage/xtrem-show-case/lib/pages/pod-collection' {
    interface PodCollection extends PodCollectionExtension {}
}
