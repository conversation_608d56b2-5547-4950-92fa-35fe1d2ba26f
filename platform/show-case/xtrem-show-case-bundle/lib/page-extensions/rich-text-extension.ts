import { GraphApi } from '@sage/xtrem-show-case-bundle-api';
import { RichText } from '@sage/xtrem-show-case/lib/pages/rich-text';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.pageExtension<RichTextExtension>({
    extends: '@sage/xtrem-show-case/RichText',
})
export class RichTextExtension extends ui.PageExtension<RichText, GraphApi> {
    @ui.decorators.richTextFieldOverride<RichTextExtension>({
        title: 'Extension Title',
        helperText: 'Extension Helper Text',
        onClick() {
            this.$.showToast('Extension onClick has been triggered!', { type: 'success' });
        },
        onClickAfter() {
            this.clickAfterTriggered.isHidden = false;
            setTimeout(() => {
                this.clickAfterTriggered.isHidden = true;
            }, 3000);
        },
        onChange() {
            this.$.showToast('Extension onChange has been triggered!', { type: 'success' });
        },
        onChangeAfter() {
            this.changeAfterTriggered.isHidden = false;
            setTimeout(() => {
                this.changeAfterTriggered.isHidden = true;
            }, 3000);
        },
    })
    field: ui.fields.RichText;

    @ui.decorators.labelField<RichTextExtension>({
        isTransient: true,
        parent() {
            return this.fieldBlock;
        },
        isHidden: true,
        borderColor: '00B000',
        map() {
            return 'onClickAfter was triggered';
        },
    })
    clickAfterTriggered: ui.fields.Label;

    @ui.decorators.labelField<RichTextExtension>({
        isTransient: true,
        parent() {
            return this.fieldBlock;
        },
        isHidden: true,
        borderColor: '00B000',
        map() {
            return 'onChangeAfter was triggered';
        },
    })
    changeAfterTriggered: ui.fields.Label;
}
