import { GraphApi } from '@sage/xtrem-show-case-bundle-api';
import { Count } from '@sage/xtrem-show-case/lib/pages/count';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.pageExtension<CountExtension>({
    extends: '@sage/xtrem-show-case/Count',
})
export class CountExtension extends ui.PageExtension<Count, GraphApi> {
    @ui.decorators.countFieldOverride<CountExtension>({
        title: 'Extension Title',
        helperText: 'Extension: Total number of products',
        onClick() {
            this.$.showToast('Extension onClick has been triggered!', { type: 'success' });
        },
        onClickAfter() {
            this.clickAfterTriggered.isHidden = false;
            setTimeout(() => {
                this.clickAfterTriggered.isHidden = true;
            }, 3000);
        },
    })
    totalCountOfProducts: ui.fields.Count;

    @ui.decorators.countFieldOverride<CountExtension>({
        title: 'Extension Total number of products',
        helperText: 'Extension with qty field greater than 15',
        onClick() {
            this.$.showToast('Extension onClick has been triggered!', { type: 'success' });
        },
        onClickAfter() {
            this.clickAfterTriggered.isHidden = false;
            setTimeout(() => {
                this.clickAfterTriggered.isHidden = true;
            }, 3000);
        },
    })
    filteredCountOfProducts: ui.fields.Count;

    @ui.decorators.labelField<CountExtension>({
        isTransient: true,
        parent() {
            return this.fieldBlock;
        },
        isHidden: true,
        borderColor: '00B000',
        map() {
            return 'Click After was triggered';
        },
    })
    clickAfterTriggered: ui.fields.Label;
}
