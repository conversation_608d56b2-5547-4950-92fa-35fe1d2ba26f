import { Graph<PERSON><PERSON> } from '@sage/xtrem-show-case-bundle-api';
import { DetailList } from '@sage/xtrem-show-case/lib/pages/detail-list';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.pageExtension<DetailListExtension>({
    extends: '@sage/xtrem-show-case/DetailList',
})
export class DetailListExtension extends ui.PageExtension<DetailList, GraphApi> {
    @ui.decorators.detailListFieldOverride<DetailListExtension>({
        title: 'Extension Title',
        helperText: 'Extension Helper Text',
        fields: [
            ui.nestedFieldExtensions.numeric({
                bind: 'listPrice',
                title: 'List price',
                scale: 2,
                insertBefore: 'qty',
            }),
        ],
        fieldOverrides: [
            ui.nestedFieldOverrides.text({
                bind: 'description',
                title: 'Overridden description title',
            }),
        ],
        onRecordClick(item: any) {
            this.$.dialog.message(
                'info',
                'Extension Item clicked',
                `Clicked field ${item.product} with Description ${item.description}`,
            );
        },
        onRecordClickAfter() {
            this.$.showToast('onClickAfter has been triggered!', { type: 'success' });
        },
    })
    field: ui.fields.DetailList;
}
