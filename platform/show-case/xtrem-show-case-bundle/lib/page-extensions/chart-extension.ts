import { GraphApi } from '@sage/xtrem-show-case-bundle-api';
import { Chart } from '@sage/xtrem-show-case/lib/pages/chart';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.pageExtension<ChartExtension>({
    extends: '@sage/xtrem-show-case/Chart',
})
export class ChartExtension extends ui.PageExtension<Chart, GraphApi> {
    @ui.decorators.chartFieldOverride<ChartExtension>({
        title: 'Extension Title',
        helperText: 'Extension Helper Text',
        onClick() {
            this.$.dialog.message('info', 'Extension clicked', 'Extension onClick has been triggered!');
        },
        onClickAfter() {
            this.$.showToast('onClickAfter has been triggered!', { type: 'success' });
        },
    })
    field: ui.fields.Chart;
}
