import { GraphApi } from '@sage/xtrem-show-case-bundle-api';
import { Select } from '@sage/xtrem-show-case/lib/pages/select';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.pageExtension<SelectExtension>({
    extends: '@sage/xtrem-show-case/Select',
})
export class SelectExtension extends ui.PageExtension<Select, GraphApi> {
    @ui.decorators.selectFieldOverride<SelectExtension>({
        title: 'Extension Title',
        helperText: 'Extension Helper Text',
        // onClick() {
        //     this.$.showToast('Extension onClick has been triggered!', {type: 'success'});
        // },
        onClickAfter() {
            this.clickAfterTriggered.isHidden = false;
            setTimeout(() => {
                this.clickAfterTriggered.isHidden = true;
            }, 3000);
        },
        onChange() {
            this.$.showToast('Extension onChange has been triggered!', { type: 'success' });
        },
        onChangeAfter() {
            this.changeAfterTriggered.isHidden = false;
            setTimeout(() => {
                this.changeAfterTriggered.isHidden = true;
            }, 3000);
        },
    })
    field: ui.fields.Select;

    @ui.decorators.labelField<SelectExtension>({
        isTransient: true,
        parent() {
            return this.fieldBlock;
        },
        isHidden: true,
        borderColor: '00B000',
        map() {
            return 'onClickAfter was triggered';
        },
    })
    clickAfterTriggered: ui.fields.Label;

    @ui.decorators.labelField<SelectExtension>({
        isTransient: true,
        parent() {
            return this.fieldBlock;
        },
        isHidden: true,
        borderColor: '00B000',
        map() {
            return 'onChangeAfter was triggered';
        },
    })
    changeAfterTriggered: ui.fields.Label;

    @ui.decorators.selectFieldOverride<SelectExtension>({
        title: 'Extension Former Mandatory',
        helperText: 'Override base field isMandatory prop',
        isMandatory: false,
    })
    mandatory: ui.fields.Select;

    @ui.decorators.selectFieldOverride<SelectExtension>({
        title: 'Extension Former Full width',
        helperText: 'Override base field isFullWidth prop',
        isFullWidth: false,
    })
    fullWidth: ui.fields.Select;
}
