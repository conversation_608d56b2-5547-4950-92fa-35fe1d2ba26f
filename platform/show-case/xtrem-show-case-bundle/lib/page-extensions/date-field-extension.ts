import { GraphA<PERSON> } from '@sage/xtrem-show-case-bundle-api';
import { DateField } from '@sage/xtrem-show-case/lib/pages/date-field';
import * as ui from '@sage/xtrem-ui';
import { DateValue } from '@sage/xtrem-date-time';

@ui.decorators.pageExtension<DateFieldExtension>({
    extends: '@sage/xtrem-show-case/DateField',
})
export class DateFieldExtension extends ui.PageExtension<DateField, GraphApi> {
    @ui.decorators.dateFieldOverride<DateFieldExtension>({
        title: 'Extension Title',
        helperText: 'Extension Helper Text',
        minDate: DateValue.today().begOfYear().toString(),
        maxDate: DateValue.today().endOfYear().toString(),
        onClick() {
            this.$.showToast('Extension onClick has been triggered!', { type: 'success' });
        },
        onClickAfter() {
            this.clickAfterTriggered.isHidden = false;
            setTimeout(() => {
                this.clickAfterTriggered.isHidden = true;
            }, 3000);
        },
        onChange() {
            this.$.showToast('onChange has been triggered!', { type: 'success' });
        },
        onChangeAfter() {
            this.changeAfterTriggered.isHidden = false;
            setTimeout(() => {
                this.changeAfterTriggered.isHidden = true;
            }, 3000);
        },
    })
    field: ui.fields.Date;

    @ui.decorators.labelField<DateFieldExtension>({
        isTransient: true,
        parent() {
            return this.fieldBlock;
        },
        isHidden: true,
        borderColor: '00B000',
        map() {
            return 'Click After was triggered';
        },
    })
    clickAfterTriggered: ui.fields.Label;

    @ui.decorators.labelField<DateFieldExtension>({
        parent() {
            return this.fieldBlock;
        },
        isHidden: true,
        borderColor: '00B000',
        map() {
            return 'Change After was triggered';
        },
    })
    changeAfterTriggered: ui.fields.Label;

    @ui.decorators.dateFieldOverride<DateFieldExtension>({
        title: 'Extension Mandatory Title',
        helperText: 'Extension Mandatory Helper Text',
    })
    mandatory: ui.fields.Date;

    @ui.decorators.dateFieldOverride<DateFieldExtension>({
        title: 'Extension Future date Title',
        helperText: 'Extension Future date Helper Text',
    })
    customValidation: ui.fields.Date;
}
