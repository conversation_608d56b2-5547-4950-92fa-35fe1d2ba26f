import { GraphA<PERSON> } from '@sage/xtrem-show-case-bundle-api';
import { DropdownList } from '@sage/xtrem-show-case/lib/pages/dropdown-list';
import * as ui from '@sage/xtrem-ui';

const map = (value: any) => {
    switch (value) {
        case 'extensionChange':
            return 'Extension Change Event';
        case 'extensionClick':
            return 'Extension Click Event';
        case 'error':
            return 'Error Event';
        default:
            return '';
    }
};
@ui.decorators.pageExtension<DropdownListExtension>({
    extends: '@sage/xtrem-show-case/DropdownList',
})
export class DropdownListExtension extends ui.PageExtension<DropdownList, GraphApi> {
    @ui.decorators.dropdownListFieldOverride<DropdownListExtension>({
        title: 'Extension Title (Options)',
        helperText: 'Extension Helper Text',
        onChange() {
            this.value.value = this.field1.value;
            this.label1.value = 'extensionChange';
            this.label1.borderColor = '00B000';
            setTimeout(() => {
                this.label1.value = '';
            }, 2000);
        },
        onChangeAfter() {
            this.$.showToast('onChangeAfter has been triggered!', { type: 'success' });
        },
        onClick() {
            this.label1.value = 'extensionClick';
            this.label1.borderColor = '00B000';
            setTimeout(() => {
                this.label1.value = '';
            }, 2000);
        },
        onClickAfter() {
            this.$.showToast('onClickAfter has been triggered!', { type: 'success' });
        },
    })
    field1: ui.fields.DropdownList;

    @ui.decorators.labelFieldOverride<DropdownListExtension>({
        map,
    })
    label1: ui.fields.Label;

    @ui.decorators.dropdownListFieldOverride<DropdownListExtension>({
        size: 'large',
        title: 'Extension Large',
        helperText: 'Former small size',
    })
    small: ui.fields.DropdownList;

    @ui.decorators.dropdownListFieldOverride<DropdownListExtension>({
        size: 'small',
        title: 'Extension Small',
        helperText: 'Former medium size',
    })
    medium: ui.fields.DropdownList;

    @ui.decorators.dropdownListFieldOverride<DropdownListExtension>({
        size: 'medium',
        title: 'Extension Medium',
        helperText: 'Former large size',
    })
    large: ui.fields.DropdownList;

    @ui.decorators.dropdownListFieldOverride<DropdownListExtension>({
        options: ['Extension Success', 'Extension Error'],
        title: 'Extension Dropdown List',
        helperText: 'Extension with Validation overridden',
        validation(value) {
            return value === 'Extension Error' ? 'Oh, you selected an error from an extended field.' : '';
        },
    })
    validated: ui.fields.DropdownList;

    @ui.decorators.dropdownListFieldOverride<DropdownListExtension>({
        title: 'Extension Dropdown List with Error',
        helperText: 'Extension onError overridden',
        onError(error) {
            return `This is the overridden message by the extension: ${error.message}`;
        },
    })
    errorHandled: ui.fields.DropdownList;
}
