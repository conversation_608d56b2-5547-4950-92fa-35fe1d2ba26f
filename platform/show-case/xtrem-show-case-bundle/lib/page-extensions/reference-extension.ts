import { GraphApi } from '@sage/xtrem-show-case-bundle-api';
import { Reference } from '@sage/xtrem-show-case/lib/pages/reference';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.pageExtension<ReferenceExtension>({
    extends: '@sage/xtrem-show-case/Reference',
})
export class ReferenceExtension extends ui.PageExtension<Reference, GraphApi> {
    @ui.decorators.referenceFieldOverride<ReferenceExtension>({
        title: 'Extension Title',
        helperText: 'Extension Helper Text',
        onClick() {
            this.$.showToast('Extension onClick has been triggered!', { type: 'success' });
        },
        onClickAfter() {
            this.clickAfterTriggered.isHidden = false;
            setTimeout(() => {
                this.clickAfterTriggered.isHidden = true;
            }, 3000);
        },
        onChange() {
            this.$.showToast('onChange has been triggered!', { type: 'success' });
        },
        onChangeAfter() {
            if (this.field.value !== this.value.value) {
                this.value.value = this.field.value;
                this.changeAfterTriggered.isHidden = false;
                setTimeout(() => {
                    this.changeAfterTriggered.isHidden = true;
                }, 3000);
            }
        },
        onCloseLookupDialog() {
            this.$.showToast('onCloseLookupDialog has been triggered!', { type: 'success' });
        },
        onCloseLookupDialogAfter() {
            this.closeDialogAfterTriggered.isHidden = false;
            setTimeout(() => {
                this.closeDialogAfterTriggered.isHidden = true;
            }, 3000);
        },
        onOpenLookupDialog() {
            this.field.title = 'Extension onOpenLookupDialog title';
        },
        onOpenLookupDialogAfter() {
            this.field.helperText = 'Extension onOpenLookupDialogAfter helper text';
        },
        columns: [
            ui.nestedFieldExtensions.reference({
                bind: 'biodegradabilityCategory',
                valueField: 'description',
                node: '@sage/xtrem-show-case-bundle/BiodegradabilityCategory',
                insertBefore: '_id',
            }),
        ],
    })
    field: ui.fields.Reference;

    @ui.decorators.labelField<ReferenceExtension>({
        isTransient: true,
        parent() {
            return this.fieldBlock;
        },
        isHidden: true,
        borderColor: '00B000',
        map() {
            return 'Click After was triggered';
        },
    })
    clickAfterTriggered: ui.fields.Label;

    @ui.decorators.labelField<ReferenceExtension>({
        parent() {
            return this.fieldBlock;
        },
        isHidden: true,
        borderColor: '00B000',
        map() {
            return 'Change After was triggered';
        },
    })
    changeAfterTriggered: ui.fields.Label;

    @ui.decorators.labelField<ReferenceExtension>({
        isTransient: true,
        parent() {
            return this.fieldBlock;
        },
        isHidden: true,
        borderColor: '00B000',
        map() {
            return 'Close Dialog After was triggered';
        },
    })
    closeDialogAfterTriggered: ui.fields.Label;
}
