import { BiodegradabilityCategory, GraphApi } from '@sage/xtrem-show-case-bundle-api';
import { Block } from '@sage/xtrem-show-case/lib/pages/block';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.pageExtension<BlockExtension>({
    extends: '@sage/xtrem-show-case/Block',
})
export class BlockExtension extends ui.PageExtension<Block, GraphApi> {
    @ui.decorators.block<BlockExtension>({
        parent() {
            return this.section;
        },
        title: 'Biodegradability',
    })
    biodegradabilityBlock: ui.containers.Block;

    @ui.decorators.referenceField<BlockExtension, BiodegradabilityCategory>({
        columns: [
            ui.nestedFields.text({ bind: 'description', title: 'Description' }),
            ui.nestedFields.text({ bind: '_id', title: 'Id' }),
        ],
        node: '@sage/xtrem-show-case-bundle/BiodegradabilityCategory',
        parent() {
            return this.biodegradabilityBlock;
        },
        title: 'Biodegradability Category',
        validation(value: BiodegradabilityCategory) {
            return value.description === 'Milk' ? '' : 'He needs some milk.';
        },
        valueField: 'description',
    })
    biodegradabilityCategory: ui.fields.Reference;

    @ui.decorators.buttonField<BlockExtension>({
        isTransient: true,
        map() {
            return 'Validate';
        },
        async onClick() {
            const validation = await this.biodegradabilityBlock.validate();

            if (validation.length) {
                this.$.showToast(validation.join('\n'), { type: 'error' });
            } else {
                this.$.showToast('Validation successful', { type: 'success' });
            }
        },
        parent() {
            return this.biodegradabilityBlock;
        },
    })
    biodegradabilityButton: ui.fields.Button;
}
