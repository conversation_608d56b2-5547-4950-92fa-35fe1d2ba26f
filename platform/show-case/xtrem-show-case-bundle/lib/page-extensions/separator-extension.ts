import { Graph<PERSON><PERSON> } from '@sage/xtrem-show-case-bundle-api';
import { Separator } from '@sage/xtrem-show-case/lib/pages/separator';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.pageExtension<SeparatorExtension>({
    extends: '@sage/xtrem-show-case/Separator',
})
export class SeparatorExtension extends ui.PageExtension<Separator, GraphApi> {
    @ui.decorators.separatorFieldOverride<SeparatorExtension>({
        onClick() {
            this.clickTriggered.isHidden = false;
            setTimeout(() => {
                this.clickTriggered.isHidden = true;
            }, 3000);
        },
        onClickAfter() {
            this.$.showToast('onClickAfter has been triggered!', { type: 'success' });
        },
        isFullWidth: true,
    })
    field1: ui.fields.Separator;

    @ui.decorators.labelField<SeparatorExtension>({
        parent() {
            return this.fieldBlock1;
        },
        isHidden: true,
        borderColor: '00B000',
        map() {
            return 'Click After was triggered';
        },
    })
    clickTriggered: ui.fields.Label;

    @ui.decorators.separatorFieldOverride<SeparatorExtension>({
        isFullWidth: false,
    })
    field2: ui.fields.Separator;
}
