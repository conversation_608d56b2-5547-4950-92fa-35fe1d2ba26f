import { Graph<PERSON><PERSON> } from '@sage/xtrem-show-case-bundle-api';
import { TableWithDetailPanel } from '@sage/xtrem-show-case/lib/pages/table-with-detail-panel';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.pageExtension<TableWithDetailPanelExtension>({
    extends: '@sage/xtrem-show-case/TableWithDetailPanel',
})
export class TableWithDetailPanelExtension extends ui.PageExtension<TableWithDetailPanel, GraphApi> {
    @ui.decorators.tableFieldOverride<TableWithDetailPanelExtension>({
        title: 'Extension Title',
        helperText: 'Extension Helper Text',
        onRowClick() {
            this.$.showToast('onRowClick extension overrides', { type: 'success' });
        },
        onRowClickAfter(_id: string) {
            this.$.showToast('onRowClickAfter is now implementing row click logic', { type: 'success' });
            this.$.detailPanel.isHidden = false;
            this.demoGridRowBlock.selectedRecordId = _id;
            console.log(_id);

            this.rowClickAfterTriggered.isHidden = false;
            setTimeout(() => {
                this.rowClickAfterTriggered.isHidden = true;
            }, 3000);
        },
        dropdownActions: [
            {
                icon: 'box_arrow_left',
                title: 'Extended action inserted at the end',
                async onClick() {
                    this.$.dialog.message('info', '', 'Changed name to banana');
                },
            },
            {
                icon: 'cross_circle',
                title: 'Extended action inserted after edit',
                async onClick() {
                    this.$.dialog.message('info', 'Delete on sidebar', 'Delete sidebar extension was clicked');
                },
                insertAfter: 'edit',
            },
            {
                icon: 'bin',
                title: 'Extended action inserted before edit',
                isDestructive: true,
                async onClick() {
                    this.$.dialog.message('info', 'Remove on sidebar', 'Remove sidebar extension was clicked');
                },
                insertBefore: 'edit',
            },
        ],
        inlineActions: [
            {
                icon: 'info',
                title: 'Extended action inserted at the end',
                async onClick() {
                    this.$.dialog.message('info', '', 'Changed name to banana');
                },
            },
            {
                icon: 'info',
                title: 'Extended action inserted before edit',
                async onClick() {
                    this.$.dialog.message('info', '', 'Changed name to banana');
                },
                insertBefore: 'edit',
            },
            {
                icon: 'info',
                title: 'Extended action inserted after edit',
                async onClick() {
                    this.$.dialog.message('info', '', 'Changed name to banana');
                },
                insertAfter: 'edit',
            },
        ],
    })
    field: ui.fields.Table;

    @ui.decorators.gridRowBlockOverride<TableWithDetailPanelExtension>({
        title: 'Extension Title',
        fieldFilter(bind: string) {
            return bind !== 'hotProduct';
        },
    })
    demoGridRowBlock: ui.containers.GridRowBlock;

    @ui.decorators.labelField<TableWithDetailPanelExtension>({
        isTransient: true,
        parent() {
            return this.fieldBlock;
        },
        isHidden: true,
        borderColor: '00B000',
        map() {
            return 'onRowUnselectedAfter was triggered';
        },
    })
    rowClickAfterTriggered: ui.fields.Label;
}
