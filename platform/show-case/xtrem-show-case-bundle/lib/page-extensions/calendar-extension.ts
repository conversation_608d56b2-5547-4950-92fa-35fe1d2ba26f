import { GraphApi } from '@sage/xtrem-show-case-bundle-api';
import { Calendar } from '@sage/xtrem-show-case/lib/pages/calendar';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.pageExtension<CalendarExtension>({
    extends: '@sage/xtrem-show-case/Calendar',
})
export class CalendarExtension extends ui.PageExtension<Calendar, GraphApi> {
    @ui.decorators.calendarFieldOverride<CalendarExtension>({
        title: 'Extension Title',
        helperText: 'Extension Helper Text',
        onDayClick(date) {
            this.$.dialog.message('info', 'Extension Day clicked', date && date.toString());
        },
        onDayClickAfter(date) {
            this.$.showToast('onDayClickAfter has been triggered!', { type: 'success' });
        },
        onEventClick(_id, event) {
            this.$.dialog.message('info', 'Extension Event clicked', event.product);
        },
        onEventClickAfter(_id, event) {
            this.$.showToast('onEventClickAfter has been triggered!', { type: 'success' });
        },
    })
    field: ui.fields.Calendar;
}
