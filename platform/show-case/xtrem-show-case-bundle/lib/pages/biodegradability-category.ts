import { GraphApi } from '@sage/xtrem-show-case-bundle-api';
import * as ui from '@sage/xtrem-ui';
import { applicationPages } from '@sage/xtrem-show-case/build/lib/menu-items/application-pages';

@ui.decorators.page<BiodegradabilityCategory>({
    authorizationCode: 'SHCPRDT',
    menuItem: applicationPages,
    module: 'show-case-bundle',
    title: 'Sample page - Biodegradability Category',
    node: '@sage/xtrem-show-case-bundle/BiodegradabilityCategory',
    category: 'SHOWCASE',
    onLoad() {
        this.$.storage.set('filterNavPanelItems', true);
    },
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.text({ bind: 'description', canFilter: false }),
        },
    },
})
export class BiodegradabilityCategory extends ui.Page<GraphApi> {
    @ui.decorators.section<BiodegradabilityCategory>({})
    section: ui.containers.Section;

    @ui.decorators.block<BiodegradabilityCategory>({
        parent() {
            return this.section;
        },
    })
    block: ui.containers.Block;

    @ui.decorators.textField<BiodegradabilityCategory>({
        parent() {
            return this.block;
        },
        title: 'Description',
    })
    description: ui.fields.Text;

    @ui.decorators.numericField<BiodegradabilityCategory>({
        parent() {
            return this.block;
        },
        title: 'Percentage Of Degradability',
    })
    percentageOfDegradability: ui.fields.Numeric;

    @ui.decorators.checkboxField<BiodegradabilityCategory>({
        parent() {
            return this.block;
        },
        title: 'Local Sourcing',
    })
    localSourcing: ui.fields.Checkbox;
}
