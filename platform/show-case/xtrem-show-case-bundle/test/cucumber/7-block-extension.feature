Feature: 7 Block extension
    # Tests the validation behavior of blocks in extension pages, ensuring proper error handling and field validation

    Scenario: As a application developer I want the validation of blocks in extension pages to work as expected
        XT-72965
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Block/eyJfaWQiOiIxMDYifQ=="
        Then the "Block" titled page is displayed
        When the user expands accordion with title "Biodegradability" on the main page
        And the user clicks in the "biodegradabilityButton" bound button field on the main page
        Then the "Biodegradability Category" labelled reference field on the main page is invalid
        And takes a screenshot
