Feature: 7 Numeric extension
    # Tests the extension capabilities of the numeric field component, including custom labels, helper text, and click/change event handlers

    <PERSON><PERSON><PERSON>: As a user I want to extend the numeric field
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Numeric"
        Then the "Extension Title" labelled numeric field on the main page is displayed
        And the user selects the "Extension Title" labelled numeric field on the main page
        Then the helper text of the numeric field is "Extension Helper Text"

    Scenario: Trigger extension's custom click after event handler
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Numeric"
        And the "clickAfterTriggered" bound label field on the main page is hidden
        And the user selects the "field" bound numeric field on the main page
        When the user clicks in the numeric field
        Then the "clickAfterTriggered" bound label field on the main page is displayed

    Scenario: Trigger extension's custom change after event handler
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Numeric"
        And the "changeAfterTriggered" bound label field on the main page is hidden
        And the user selects the "field" bound numeric field on the main page
        When the user writes "5" in the numeric field
        Then the "changeAfterTriggered" bound label field on the main page is displayed
