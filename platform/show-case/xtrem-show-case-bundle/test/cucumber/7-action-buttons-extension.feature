Feature: 7 Action buttons extension
  # Tests the extension capabilities of action buttons, verifying custom event handling and the ability to extend page actions with custom behaviors

  Sc<PERSON>rio: As a user I want to extend page actions
    Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ActionButtons"
    When the user clicks the "Business action 1" labelled business action button on the main page
    Then a toast containing text "onClick overriden" is displayed
    And a toast containing text "onClickAfter" is displayed

  Scenario: As an app developer I want main list, page header, grid row actions separators extendable
    XT-88304

    # check actions and separators on main list
    Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/PageActions"
    When the user selects the "$navigationPanel" bound table field on the main page
    And the user selects the row with text "Ali Express" in the "Name" labelled column header of the table field
    And the user opens the more actions dropdown menu of the selected row of the table field
    Then the dropdown action menu elements of the selected row are:
      | Action 1           |
      | divider            |
      | Extension Action 1 |
      | divider            |
      | Extension Action 2 |
      | Action 2           |
      | divider            |
      | Action 3           |

    # check actions and separators on page header
    When the user clicks the "Name" labelled nested field of the selected row in the table field
    And the user clicks the more actions button in the header
    Then the header actions dropdown menu elements are:
      | Action 1           |
      | divider            |
      | Extension Action 1 |
      | divider            |
      | Extension Action 2 |
      | Action 2           |
      | divider            |
      | Action 3           |

    # check actions and separators on grid row
    When the user selects the "products" bound table field on the main page
    And the user selects the row with text "249" in the "Id" labelled column header of the table field
    And the user opens the more actions dropdown menu of the selected row of the table field
    Then the dropdown action menu elements of the selected row are:
      | Action 1           |
      | divider            |
      | Extension Action 1 |
      | divider            |
      | Extension Action 2 |
      | Action 2           |
      | divider            |
      | Action 3           |
