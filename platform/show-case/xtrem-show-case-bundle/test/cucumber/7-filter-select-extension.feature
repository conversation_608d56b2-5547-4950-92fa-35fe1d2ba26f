Feature: 7 Filter select extension
    # Tests the extension capabilities of the filter select component, including custom labels, helper text, and change event handlers

    <PERSON><PERSON><PERSON>: As a user I want to extend the filter select field
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/FilterSelect"
        Then the "Extension Title" labelled filter select field on the main page is displayed
        And the user selects the "Extension Title" labelled filter select field on the main page
        Then the helper text of the filter select field is "Extension Helper Text"

    Scenario: Trigger extension's custom change after event handler
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/FilterSelect"
        And the "changeAfterTriggered" bound label field on the main page is hidden
        And the user selects the "Extension Title" labelled filter select field on the main page
        When the user clicks in the filter select field
        When the user writes "Veal Inside - Provimi" in the filter select field
        And the user selects "Veal Inside - Provimi" in the filter select field
        Then the "changeAfterTriggered" bound label field on the main page is displayed
