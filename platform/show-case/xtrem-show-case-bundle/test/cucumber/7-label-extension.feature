Feature: 7 Label extension
    # Tests the extension capabilities of the label component, including custom text, helper text, and click event handlers

    <PERSON><PERSON><PERSON>: As a user I want to extend the label field
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Label"
        Then the "Extension Title" labelled label field on the main page is displayed
        And the user selects the "Extension Title" labelled label field on the main page
        Then the helper text of the label field is "Extension Helper Text"


    Scenario: Trigger extension's custom click after event handler
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Label"
        And the "clickAfterTriggered" bound label field on the main page is hidden
        And the user selects the "field" bound label field on the main page
        When the user clicks in the label field
        Then the "clickAfterTriggered" bound label field on the main page is displayed
