Feature: 7 Rich text extension
    # Tests the extension capabilities of the rich text editor component, including custom labels, helper text, and change event handlers

    <PERSON><PERSON><PERSON>: As a user I want to extend the rich text field
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/RichText"
        Then the "Extension Title" labelled rich text field on the main page is displayed
        And the user selects the "Extension Title" labelled rich text field on the main page
        Then the helper text of the rich text field is "Extension Helper Text"

    Scenario: Trigger extension's custom change after event handler
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/RichText"
        And the "changeAfterTriggered" bound label field on the main page is hidden
        And the user selects the "field" bound rich text field on the main page
        When the user writes "Extension" in the rich text field
        Then the "changeAfterTriggered" bound label field on the main page is displayed
