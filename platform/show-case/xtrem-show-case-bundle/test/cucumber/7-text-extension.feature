Feature: 7 Text extension
    # Tests the extension capabilities of the text field component, including custom labels, helper text, and click/change event handlers

    <PERSON><PERSON><PERSON>: As a user I want to extend the text field
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Text"
        Then the "Extension Title" labelled text field on the main page is displayed
        And the user selects the "Extension Title" labelled text field on the main page
        Then the helper text of the text field is "Extension Helper Text"

    Scenario: Trigger extension's custom click after event handler
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Text"
        Then the "clickAfterTriggered" bound label field on the main page is hidden
        And the user selects the "field" bound text field on the main page
        When the user clicks in the text field
        Then the "clickAfterTriggered" bound label field on the main page is displayed

    Scenario: Trigger extension's custom click after event handler
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Text"
        Then the "changeAfterTriggered" bound label field on the main page is hidden
        And the user selects the "field" bound text field on the main page
        When the user writes "Extension" in the text field
        Then the "changeAfterTriggered" bound label field on the main page is displayed
