Feature: 7 Page extension
    # Tests the extension capabilities at the page level, ensuring custom fields can be added to core pages with proper initialization and data binding

    Scenario: As a user I want to see extension fields appearing on the core page
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseProduct/$new"
        And the "Biodegradability Category" labelled reference field on the main page is displayed
        And the "Extension field title" labelled text field on the main page is displayed
        And the "Extension at the end" labelled text field on the main page is displayed

    Scenario: As a user I want to see the load method activated and custom extension member functions executd
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseProduct/$new"
        And the user selects the "Extension field title" labelled text field on the main page
        Then the text field is disabled

    # This scenario is broken because we disabled part of the code that loads CSV data for bundles.
    # Scenario: As a user I want to load data into extension pages
    #     Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseProduct/eyJfaWQiOiIzMTMifQ=="
    #     And the value of the "Biodegradability Category" labelled reference field on the main page is "Cooked meal"
    #     Then the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseProduct/eyJfaWQiOiIzODgifQ=="
    #     And the value of the "Biodegradability Category" labelled reference field on the main page is "Vegetables"

    # TODO WHY IS IT FLAKY?
    #    Scenario: As a user I want to modify an extended page and save its content and later recover it
    #        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseProduct/eyJfaWQiOiI3NCJ9"
    #        And the value of the "Biodegradability Category" labelled reference field on the main page is "Drink"
    #        # Also testing autocomplete and fetching suggestions
    #        And the user writes "Mil" to the "Biodegradability Category" labelled reference field on the main page
    #        And the value of the "Biodegradability Category" labelled reference field on the main page is "Milk"
    #        Then the user selects the "Save" labelled business action button on the main page
    #        And the value of the "Biodegradability Category" labelled reference field on the main page is "Milk"
    #        When the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseProduct/eyJfaWQiOiI3NCJ9"
    # TODO: Investigate why this last line fails
    # And the value of the "Biodegradability Category" labelled reference field on the main page is "Milk"

    Scenario: As a developer I want the extension to be able to interact with control objects of the core extended page
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseProduct/eyJfaWQiOiIzMTMifQ=="
        And the title of the "block" bound block container on the main page is "This title is set from the extension!"
        And the user selects the "Product" labelled text field on the main page
        Then the value of the text field is "Appetiser - Bought"
        And the user selects the "Extended Text Field" labelled text field on the main page
        When the user writes "Sample text" in the text field
        And the user presses Tab
        And the user selects the "Product" labelled text field on the main page
        Then the value of the text field is "Sample text"

    Scenario: As a developer I would like to properties of a base field in the extension
        XT-3372
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/ShowCaseProduct/eyJfaWQiOiIzMTMifQ=="
        And the user selects the "releaseDate" bound date field on the main page
        And the title of the date field is "EXTENSION TITLE"
        And the user selects the "EXTENSION TITLE" labelled date field on the main page
        Then the date field is disabled
