Feature: 7 Checkbox extension
    # Tests the extension capabilities of the checkbox component, including custom labels, helper text, and click/change event handlers

    <PERSON><PERSON><PERSON>: As a user I want to extend the checkbox field
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Checkbox"
        Then the "Extension Title" labelled checkbox field on the main page is displayed
        And the user selects the "Extension Title" labelled checkbox field on the main page
        Then the helper text of the checkbox field is "Extension Helper Text"

    Scenario: Trigger extension's custom click after event handler
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Checkbox"
        Then the "clickAfterTriggered" bound label field on the main page is hidden
        And the user selects the "field" bound checkbox field on the main page
        When the user unticks the checkbox field
        Then the "clickAfterTriggered" bound label field on the main page is displayed

    Scenario: Trigger extension's custom click after event handler
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Checkbox"
        Then the "changeAfterTriggered" bound label field on the main page is hidden
        And the user selects the "field" bound checkbox field on the main page
        When the user unticks the checkbox field
        Then the "changeAfterTriggered" bound label field on the main page is displayed
