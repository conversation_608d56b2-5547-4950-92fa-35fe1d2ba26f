Feature: 7 Button extension
    # Tests the extension capabilities of the button component, including custom labels, helper text, and click event handlers

    <PERSON><PERSON><PERSON>: As a user I want to extend the button field
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Button"
        Then the "Extension Title" labelled button field on the main page is displayed
        And the helper text of the "Extension Title" labelled button field on the main page is "Extension Helper Text"

    Scenario: Trigger extension's custom click after event handler
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Button"
        And the "clickAfterTriggered" bound label field on the main page is hidden
        When the user clicks in the "field" bound button field on the main page
        Then the "clickAfterTriggered" bound label field on the main page is displayed
