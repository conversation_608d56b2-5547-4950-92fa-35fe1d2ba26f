Feature: 7 Progress extension
    # Tests the extension capabilities of the progress field component, including custom labels, helper text, and click event handlers

    <PERSON><PERSON><PERSON>: As a user I want to extend the progress field
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Progress"
        Then the "Extension Title" labelled progress field on the main page is displayed
        And the user selects the "Extension Title" labelled progress field on the main page
        Then the helper text of the progress field is "Extension Helper Text"

    Scenario: Trigger extension's custom click after event handler
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Progress"
        Then the "clickAfterTriggered" bound label field on the main page is hidden
        And the user selects the "field" bound progress field on the main page
        When the user clicks in the progress field
        Then the "clickAfterTriggered" bound label field on the main page is displayed
