Feature: 7 Table extension
    # Tests the extension capabilities of table components, including decimal formatting and custom mobile card layouts.

    # Scenario: As a user I want to extend the table field
    #     Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Table"
    #     Then the "Extension Title" labelled table field on the main page is displayed
    #     And the helper text of the "Extension Title" labelled table field on the main page is "Extension Helper Text"

    # Scenario: As a user I want load data to a transient table and test event listeners
    #     Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Table"
    #     When the user selects row 1 of the "field" bound table field in the main page
    #     Then the "rowSelectedAfterTriggered" bound label field on the main page is displayed
    #     When the user unselects row 1 of the "field" bound table field in the main page
    #     Then the "rowUnselectedAfterTriggered" bound label field on the main page is displayed

    Sc<PERSON><PERSON>: As a user I want the set scale to limit the decimal places of input
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Table"
        When the user selects the "field" bound table field on the main page
        And the user selects the row 1 of the table field
        And the user writes "1.234" in the "listPrice" bound nested numeric field of the selected row in the table field
        And the value of the "listPrice" bound nested numeric field of the selected row in the table field is "1.23"

    Scenario: As an application developer mobile cards can be overriden in an page extension
        Given the user opens the application on a mobile using the following link: "@sage/xtrem-show-case/Table/eyJfaWQiOiIyIn0="
        When the user selects the "field" bound table field on the main page
        Then the value of the "description" bound nested text field of the card 1 in the table field is "frame"
        And the value of the "biodegradabilityCategory" bound nested reference field of the card 1 in the table field is "Cooked meal"

# Scenario: As an application developer I want to override the original table columns
#     Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Table"
#     When the user writes "1" in the "qty" bound nested numeric field of row 1 in the "field" bound table field in the main page
#     Then the value of the "qty" bound nested numeric field of row 1 in the "field" bound table field is "1.0"

# Scenario: As an application developer I want to add field-level events for overriden tables
#     XT-6248
#     Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Table"
#     When the user writes "1" in the "qty" bound nested numeric field of row 1 in the "field" bound table field in the main page
#     Then a toast with text "Override Table > onChange" is displayed
#     Then a toast with text "Override Table > onChangeAfter" is displayed

# Scenario: As an application developer I want to add column-level events for override tables
#     XT-6248
#     Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Table"
#     When the user writes "1" in the "qty" bound nested numeric field of row 1 in the "field" bound table field in the main page
#     Then a toast with text "Override Table > Quantity > onChange" is displayed
#     Then a toast with text "Override Table > Quantity > onChangeAfter" is displayed
