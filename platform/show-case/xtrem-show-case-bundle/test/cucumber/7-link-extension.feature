Feature: 7 Link extension
    # Tests the extension capabilities of the link component, including custom text, helper text, and click event handlers.

    Scenario: As a user I want to extend the link field
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Link"
        Then the "Extension Title" labelled link field on the main page is displayed
        And the user selects the "Extension Title" labelled link field on the main page
        Then the helper text of the link field is "Extension Helper Text"

    Scenario: Trigger extension's custom click after event handler
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Link"
        And the "clickAfterTriggered" bound label field on the main page is hidden
        And the user selects the "field" bound link field on the main page
        When the user clicks in the link field
        Then the "clickAfterTriggered" bound label field on the main page is displayed
