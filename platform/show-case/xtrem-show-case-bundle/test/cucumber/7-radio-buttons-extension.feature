Feature: 7 Radio buttons extension
    # Tests the extension capabilities of the radio button components, including custom text, helper text, and click event handlers.

    Scenario: As a user I want to extend the radio field
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/RadioButtons"
        Then the "Extension Title" labelled radio field on the main page is displayed
        And the user selects the "Extension Title" labelled radio field on the main page
        Then the helper text of the radio field is "Extension Helper Text"

    Scenario: Trigger extension's custom click after event handler
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/RadioButtons"
        Then the "changeAfterTriggered" bound label field on the main page is hidden
        And the user selects the "field" bound radio field on the main page
        When the user selects the label "Great" in the radio field
        Then the "changeAfterTriggered" bound label field on the main page is displayed
