Feature: 7 Reference extension
    # Tests the extension capabilities of the reference field component, including custom labels, helper text, and click/change event handlers

    <PERSON><PERSON><PERSON>: As a user I want to extend the reference field
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Reference"
        Then the "Extension Title" labelled reference field on the main page is displayed
        And the user selects the "Extension Title" labelled reference field on the main page
        Then the helper text of the reference field is "Extension Helper Text"

    Scenario: Trigger extension's custom click after event handler
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Reference"
        Then the "clickAfterTriggered" bound label field on the main page is hidden
        And the user selects the "field" bound reference field on the main page
        When the user clicks in the reference field
        Then the "clickAfterTriggered" bound label field on the main page is displayed

    Scenario: Trigger extension's custom change after event handler
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Reference"
        Then the "changeAfterTriggered" bound label field on the main page is hidden
        And the user selects the "field" bound reference field on the main page
        When the user writes "Wine - Cr" in the reference field
        And the user selects "Wine - Crozes Hermitage E." in the reference field
        Then the "changeAfterTriggered" bound label field on the main page is displayed
