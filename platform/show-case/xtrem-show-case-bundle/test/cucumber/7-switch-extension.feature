Feature: 7 Switch extension
    # Tests the extension capabilities of the switch field component, verifying custom labels, helper text, and click/change event handlers

    <PERSON><PERSON><PERSON>: As a user I want to extend the switch field
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Switch"
        Then the "Extension Title" labelled switch field on the main page is displayed
        And the user selects the "Extension Title" labelled switch field on the main page
        Then the helper text of the switch field is "Extension Helper Text"

    Scenario: Trigger extension's custom click after event handler
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Switch"
        Then the "clickAfterTriggered" bound label field on the main page is hidden
        And the user selects the "field" bound switch field on the main page
        When the user clicks in the switch field
        Then the "clickAfterTriggered" bound label field on the main page is displayed

    Scenario: Trigger extension's custom change after event handler
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Switch"
        Then the "changeAfterTriggered" bound label field on the main page is hidden
        And the user selects the "field" bound switch field on the main page
        When the user clicks in the switch field
        Then the "changeAfterTriggered" bound label field on the main page is displayed
