Feature: 7 Date extension
    # Tests the extension capabilities of the date field component, including custom labels, helper text, and event handlers

    <PERSON><PERSON><PERSON>: As a user I want to extend the date field
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/DateField"
        Then the "Extension Title" labelled date field on the main page is displayed
        And the user selects the "Extension Title" labelled date field on the main page
        Then the helper text of the date field is "Extension Helper Text"

#Scenario: Trigger extension's custom change after event handler
#    Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/DateField"
#    And the "changeAfterTriggered" bound label field on the main page is hidden
#    When the user writes "11-20-2021" to the "field" bound date field on the main page
#    Then the "changeAfterTriggered" bound label field on the main page is displayed
