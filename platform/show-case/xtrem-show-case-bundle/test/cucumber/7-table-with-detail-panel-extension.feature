Feature: 7 Table with detail panel extension
    # Tests the integration between tables and detail panels, verifying row selection, dropdown actions, and dynamic panel content updates

    <PERSON>enario: As a user I want to trigger a dropdown action
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TableWithDetailPanel"
        And the "rowClickAfterTriggered" bound label field on the main page is hidden
        When the user selects the "field" bound table field on the main page
        And the user selects the row 1 of the table field
        And the user clicks the "Description" labelled nested field of the selected row in the table field
    # Then the "rowClickAfterTriggered" bound label field on the main page is displayed

    Scenario: As an application developer I would like to override the fieldFilter property of a grid row block
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/TableWithDetailPanel"
        When the user selects the "field" bound table field on the main page
        And the user selects the row 11 of the table field
        And the user clicks the "Description" labelled nested field of the selected row in the table field
        Then the user selects the "Extension Title" labelled table field on the main page
        And the user selects the row 11 of the table field
        And the value of the "Id" labelled nested text field of the selected row in the table field is "332"
        When the user selects the "field" bound table field on the main page
        And the user selects the row 11 of the table field
        And the user clicks the "Description" labelled nested field of the selected row in the table field
        And the user selects the "Tax" labelled numeric field on the main page
        Then the value of the numeric field is "66.89"
