Feature: 7 Icon extension
    # Tests extension capabilities of icon field components, verifying proper rendering, selection behavior, and click event handlers

    <PERSON><PERSON><PERSON>: As a user I want to extend the icon field
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Icon"
        Then the "Extension Title" labelled icon field on the main page is displayed
        And the user selects the "Extension Title" labelled icon field on the main page
        Then the helper text of the icon field is "Extension Helper Text"


    Scenario: Trigger extension's custom click after event handler
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Icon"
        And the "clickAfterTriggered" bound label field on the main page is hidden
        And the user selects the "field" bound icon field on the main page
        When the user clicks in the icon field
        Then the "clickAfterTriggered" bound label field on the main page is displayed
