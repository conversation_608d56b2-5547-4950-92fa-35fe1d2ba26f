Feature: 7 Count extension
    # Tests the extension capabilities of the count field component, including custom labels, helper text, and click event handlers

    <PERSON><PERSON><PERSON>: As a user I want to extend the count field
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Count"
        Then the "Extension Title" labelled count field on the main page is displayed
        And the user selects the "Extension Title" labelled count field on the main page
        Then the helper text of the count field is "Extension: Total number of products"

    Scenario: Trigger extension's custom click after event handler
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Count"
        And the "clickAfterTriggered" bound label field on the main page is hidden
        And the user selects the "totalCountOfProducts" bound count field on the main page
        When the user clicks in the count field
        Then the "clickAfterTriggered" bound label field on the main page is displayed
