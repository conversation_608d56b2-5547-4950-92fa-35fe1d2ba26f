Feature: 7 Select extension
    # Tests the extension capabilities of the select field component, including custom labels, helper text, and change event handlers

    <PERSON><PERSON><PERSON>: As a user I want to extend the select field
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Select"
        Then the "Extension Title" labelled select field on the main page is displayed
        And the user selects the "Extension Title" labelled select field on the main page
        Then the helper text of the select field is "Extension Helper Text"

    Scenario: Trigger extension's custom change after event handler
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-show-case/Select"
        And the "changeAfterTriggered" bound label field on the main page is hidden
        And the user selects the "Extension Title" labelled select field on the main page
        When the user clicks in the select field
        And the user selects "Not bad" in the select field
        Then the "changeAfterTriggered" bound label field on the main page is displayed
