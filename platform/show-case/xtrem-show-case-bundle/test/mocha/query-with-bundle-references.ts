import { Test } from '@sage/xtrem-core';
import * as xtremShowCase from '@sage/xtrem-show-case';
import { assert } from 'chai';

describe('create query products', () => {
    before(() => {
        // xtremSystem.updateContext();
    });
    it.skip('can query with order by on references', () =>
        Test.withContext(
            async context => {
                const ascending = await context
                    .query(xtremShowCase.nodes.ShowCaseProduct, {
                        first: 2,
                        orderBy: { biodegradabilityCategory: { description: 1 } },
                    })
                    .map(async product => (await product.biodegradabilityCategory)?.description)
                    .toArray();
                assert.deepEqual(ascending, ['Cheese', 'Cheese']);
                const descending = await context
                    .query(xtremShowCase.nodes.ShowCaseProduct, {
                        first: 2,
                        orderBy: { biodegradabilityCategory: { description: -1 } },
                    })
                    .map(async product => (await product.biodegradabilityCategory)?.description)
                    .toArray();
                assert.deepEqual(descending, ['Vegetables', 'Vegetables']);
            },
            { config: { layers: ['setup'] } },
        ));
});
