import * as xtremAuthorization from '@sage/xtrem-authorization';
import { Test } from '@sage/xtrem-core';
import * as xtremShowCase from '@sage/xtrem-show-case';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremShowCaseBundle from '../../index';

describe('create product test', () => {
    before(() => {
        xtremSystem.updateContext();
        xtremAuthorization.updateContext();
    });
    it('can create', () =>
        Test.withContext(
            async context => {
                await context.create(xtremShowCase.nodes.ShowCaseProduct, {});
                await context.create(xtremShowCaseBundle.nodes.BiodegradabilityCategory, {});
            },
            { config: { layers: ['setup'] } },
        ));
});
