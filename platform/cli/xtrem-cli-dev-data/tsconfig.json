{"extends": "../../tsconfig-package.json", "include": ["index.ts", "lib/**/*", "test/**/*"], "compilerOptions": {"baseUrl": ".", "outDir": "build", "rootDir": ".", "skipLibCheck": true, "types": ["node"], "removeComments": false}, "references": [{"path": "../xtrem-cli-dev"}, {"path": "../xtrem-cli-lib"}, {"path": "../../back-end/xtrem-core"}, {"path": "../../back-end/xtrem-data-management"}, {"path": "../../back-end/xtrem-dts-bundle"}, {"path": "../../back-end/xtrem-minify"}]}