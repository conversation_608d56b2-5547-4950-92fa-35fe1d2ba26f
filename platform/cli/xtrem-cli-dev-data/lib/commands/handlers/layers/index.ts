/* eslint-disable no-console */
import { loadTestDatabase } from '@sage/xtrem-cli-dev';
import { cliContext, ExecutionMode, printSuccess, quitWithError } from '@sage/xtrem-cli-lib';
import { ApplicationManager, Test } from '@sage/xtrem-core';
import { extractDatabase } from '../../utils/application';

export interface LayersOptions {
    load?: string;
    extract?: string;
    tenant?: string;
    doNotExitOnErrors?: boolean;
    /**
     * Should the schema be reset before loading data ?
     */
    noSchemaReset?: boolean;
}

export const layers = async (options: LayersOptions) => {
    const { executionMode, dir } = cliContext;
    try {
        if (options.load) {
            const application = await ApplicationManager.getApplication(dir, { applicationType: 'admin-tool' });
            await loadTestDatabase(executionMode, application, 'start', {
                layersAsString: options.load,
            });
            printSuccess(executionMode, 'Test data successfully loaded into the database.');
        } else if (options.extract) {
            const tenant = options.tenant || Test.defaultTenantId;
            await extractDatabase(dir, 'start', options.extract, tenant);
            printSuccess(executionMode, `Data successfully extracted to ${options.extract} for tenant ${tenant}`);
        } else {
            throw Error("Missing option. Either '--load' or '--extract' must be provided.");
        }
    } catch (e) {
        if (options.doNotExitOnErrors) throw e;
        else quitWithError(executionMode, e);
    }

    if (executionMode === ExecutionMode.STANDALONE) process.exit();
};
