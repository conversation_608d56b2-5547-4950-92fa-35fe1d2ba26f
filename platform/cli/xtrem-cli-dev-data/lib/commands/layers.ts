import type { Argv } from 'yargs';
import { LayersOptions } from './handlers/layers';

export const command = 'layers';
export const desc =
    "Performs operations (merge, delete or split) on the test data or extract a tenant's data into supplied layer";

export const builder = (yargs: Argv) =>
    yargs
        .version(false)
        .option('load', {
            desc:
                'Loads the CSV files of the supplied layers into the database. ' +
                'eg. --load setup,demo --tenant 777...777. ',
            type: 'string',
            conflicts: 'extract',
        })
        .option('extract', {
            desc:
                'Extracts the supplied CSV layer. All the CSV files of this layer will be regenerated. ' +
                'eg. --extract demo --tenant 777...777. ',
            type: 'string',
            conflicts: 'load',
        })
        .option('tenant', {
            desc: "ID of the tenant's data being extracted. By default tenant=777...777",
            type: 'string',
        })
        .option('no-schema-reset', {
            desc: "Only relevant for 'LOAD' operation. Will not drop/create the schema before loading data",
        })
        .example('$0 layers --load setup,demo', '');

export const handler = (argv: any) => {
    const options = argv as LayersOptions;
    // Lazy loading of handlers to speed up the CLI loading
    // eslint-disable-next-line global-require
    return require('./handlers/layers').layers(options) as Promise<void>;
};
