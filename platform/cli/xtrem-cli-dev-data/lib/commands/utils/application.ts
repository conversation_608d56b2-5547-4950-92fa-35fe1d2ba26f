import { ApplicationManager, ConfigManager } from '@sage/xtrem-core';
import { DataManagementCli } from '@sage/xtrem-data-management';

export async function extractDatabase(dir: string, source: string, layer: string, tenantId: string): Promise<void> {
    const application = await ApplicationManager.getApplication(dir, { applicationType: 'admin-tool' });

    ConfigManager.load(application.dir, source);
    await DataManagementCli.extractData(application, tenantId, layer);
}
