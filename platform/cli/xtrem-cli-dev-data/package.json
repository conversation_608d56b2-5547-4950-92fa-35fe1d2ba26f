{"name": "@sage/xtrem-cli-dev-data", "description": "Xtrem CLI dev data management", "version": "58.0.2", "author": "sage", "license": "UNLICENSED", "keywords": ["xtrem-cli"], "repository": {"type": "git", "url": "git://github.com/Sage-ERP-X3/xtrem.git"}, "main": "build/index.js", "typings": "build/package-definition.d.ts", "files": ["build", "resources"], "dependencies": {"@sage/xtrem-cli-dev": "workspace:*", "@sage/xtrem-cli-lib": "workspace:*", "@sage/xtrem-core": "workspace:*", "@sage/xtrem-data-management": "workspace:*", "@sage/xtrem-dts-bundle": "workspace:*", "eslint": "^8.49.0", "typescript": "~5.8.3", "yargs": "^18.0.0"}, "devDependencies": {"@sage/xtrem-minify": "workspace:*", "@types/chai": "^4.3.6", "@types/chai-as-promised": "^7.1.6", "@types/node": "^22.10.2", "@types/yargs": "^17.0.24", "chai": "^4.3.10", "chai-as-promised": "^7.1.1", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0"}, "scripts": {"build": "tsc -b -v . && pnpm dts-bundle", "build:binary": "pnpm clean && pnpm build && xtrem-minify -c -z \"build/**/*.js\"", "build:cache": "turbo run build --concurrency=${XTREM_CONCURRENCY:=10}", "clean": "rm -rf build", "dts-bundle": "xtrem-dts-bundle", "lint": "eslint -c .eslintrc.js --ext .ts lib", "lint:filename": "eslint --no-eslintrc -c .eslintrc-filename.js \"**\""}, "c8": {"reporter": ["json", "lcov", "text-summary"], "extension": [".ts"], "exclude": ["**/*.d.ts", "**/*-test.ts"]}}