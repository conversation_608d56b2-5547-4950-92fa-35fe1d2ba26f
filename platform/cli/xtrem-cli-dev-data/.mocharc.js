require('ts-node/register/transpile-only');
const tsConfigPaths = require('tsconfig-paths');
require('chai').use(require('chai-as-promised'));

tsConfigPaths.register({
    baseUrl: './',
    paths: {
        '@sage/xtrem-cli': ['../xtrem-cli/index.ts'],
        '@sage/xtrem-core': ['../xtrem-core/index.ts'],
    },
});

module.exports = {
    exclude: ['**/node_modules/**/*.*'],
    timeout: 180000,
};
