import { assert } from 'chai';
import { createItem, createItemTitle, createItemUrl, readData } from '../lib/utils/navigation-creation';

const sampleData = `---
title: "Client I18n API"
permalink: /guides/client-framework/client-i18n-api
layout: documentation
hide_breadcrumb: false
---


This page describes how to use and design client side artifacts that are suitable for internationalization and translation by the content team.`;

describe('Test if proper item is created', () => {
    it('creates item title', () => {
        assert.equal(createItemTitle(sampleData), 'title: Client I18n API');
    });

    it('creates item url', () => {
        assert.equal(createItemUrl(sampleData), 'url: /client-framework/client-i18n-api');
    });

    it('creates item', () => {
        assert.equal(
            createItem(sampleData),
            '  - title: Client I18n API\n    url: /client-framework/client-i18n-api\n',
        );
    });
});

describe('Test if data is read correctly', () => {
    it('reads data', () => {
        assert.equal(readData('test/sample-file.md').replace(/\n/g, ''), sampleData.replace(/\n/g, ''));
    });
});
