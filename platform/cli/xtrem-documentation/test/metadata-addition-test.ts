import { assert } from 'chai';
import {
    createCategory,
    createMetadata,
    createPermalink,
    createTitle,
    getFirstLineArray,
    removePath,
} from '../lib/utils/metadata';

describe('Test if proper metadata is created', () => {
    it('creates title', () => {
        assert.equal(createTitle(['ETNA', '1234-Abc', 'Doc-123']), 'Doc 123');
    });

    it('creates category', () => {
        assert.equal(createCategory(['ETNA', '1234-Abc', 'Doc-123']), '1234 Abc');
    });

    it('creates permalink', () => {
        assert.equal(createPermalink(['ETNA', '1234-Abc', 'Doc-123']), '/guides/1234-abc/doc-123/');
    });

    it('creates metadata', () => {
        assert.equal(
            createMetadata('Doc-123', '1234-Abc', '/guides/1234-abc/doc-123/'),
            '---\r\ntitle: "Doc-123"\r\ncategory: "1234-Abc"\r\npermalink: /guides/1234-abc/doc-123/\r\nlayout: documentation\r\nhide_breadcrumb: false\r\n---\r\n',
        );
    });
});

describe('Test if first line from data is processed correctly', () => {
    it('gets first line array', () => {
        assert.deepEqual(getFirstLineArray('ETNA/1234-Abc/Doc-123'), ['ETNA', '1234-Abc', 'Doc-123']);
    });
});

describe('Test if PATH is deleted from data, when it exists', () => {
    it('deletes path', () => {
        assert.equal(
            removePath('PATH: ETNA/1234-Abc/Doc-123\r\nLorem ipsum dolor sit amet'),
            '\nLorem ipsum dolor sit amet',
        );
    });
    it('returns when no PATH', () => {
        assert.equal(removePath('Lorem ipsum dolor sit amet'), 'Lorem ipsum dolor sit amet');
    });
});
