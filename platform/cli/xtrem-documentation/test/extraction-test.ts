import { assert } from 'chai';
import { checkIfPathExists, createFoldersAndFiles, printFilesFromDir } from '../lib/utils/extraction';

describe('Test if proper files are created', () => {
    it('creates folders and files', () => {
        assert.equal(createFoldersAndFiles(['./test/doc-test.md'])[0], 'xtreem/development-documentation/xtrem-cli.md');
    });

    it('returns array of copied files', () => {
        assert.equal(printFilesFromDir('./test')[0], 'xtreem/development-documentation/xtrem-cli.md');
    });
});

describe('Test if errors are handled correctly', () => {
    it('returns false when path is wrong', () => {
        assert.equal(checkIfPathExists('this is a wrong path'), false);
    });
});
