import { assert } from 'chai';
import { getData, getFirstLine } from '../lib/utils/get-new-path';

const testData = `PATH: XTREEM/Development+documentation/Xtrem+CLI

The Xtrem CLI is a command line based application that can be used to quickly and easily develop Xtrem applications. It can be used throughout the development life-cycle as it supports amongst others package bootstrapping, text execution, compilation and application deployment.\n`;

describe('Test if we get proper metadata from files', () => {
    it('reads content', () => {
        assert.equal(getData('test/doc-test.md'), testData);
    });

    it('gets first line', () => {
        assert.equal(getFirstLine(testData), 'XTREEM/Development-documentation/Xtrem-CLI');
    });
});
