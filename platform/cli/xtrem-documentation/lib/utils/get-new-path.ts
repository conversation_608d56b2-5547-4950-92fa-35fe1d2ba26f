/* eslint-disable no-console */
const fs = require('fs');

// function, that opens the file from filename, which is a path to the file and returns it's content
export const getData = (fileName: string | undefined) => {
    if (!fileName) console.log('Cannot read filename');
    return fs.readFileSync(fileName).toString();
};

// function, that gets, the first line of .md file's content and returns destination path after removing "PATH: " from it
export const getFirstLine = (data: string | undefined) => {
    if (data) {
        const path = data.split('\n')[0];
        if (path.startsWith('PATH: ')) {
            return path.replace('PATH: ', '').split('+').join('-');
        }
    } else {
        console.log('Cannot read data from md files');
    }
    return undefined;
};
