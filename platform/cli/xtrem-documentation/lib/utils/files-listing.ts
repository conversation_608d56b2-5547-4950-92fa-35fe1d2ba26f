const path = require('path');
const fs = require('fs');

// function, that looks for files, startPath is the path of the root folder- the one it starts looking for files
// filter is filtering which files should be found, and callback is a callbac function
export const listFilesFromDir = (startPath: string, filter: RegExp, callback: any) => {
    if (!fs.existsSync(startPath)) {
        return;
    }

    const files = fs.readdirSync(startPath);

    files.forEach((file: string) => {
        const filename: string = path.join(startPath, file);
        const stat = fs.lstatSync(filename);

        if (stat.isDirectory()) {
            listFilesFromDir(filename, filter, callback);
        } else if (filter.test(file)) callback(filename);
    });
};
