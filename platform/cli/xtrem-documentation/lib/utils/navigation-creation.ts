import { listFilesFromDir } from './files-listing';

const fs = require('fs');

// creating file with initial data
export const createFile = (filePath: string | undefined) => {
    fs.writeFileSync(filePath, 'base: /guides\r\nitems:\r\n');
};

// processing file title from metadata in *.md files
export const createItemTitle = (data: string | undefined) => {
    if (!data) return undefined;
    const title = data.split('\n')[1].replace('"', '').replace('"', '');
    return title;
};

// processing file url from metadata in *.md files
export const createItemUrl = (data: string | undefined) => {
    if (!data) return undefined;
    const url = data.split('\n')[2].replace('permalink', 'url').replace('/guides', '');
    return url;
};

// reading data from *.md files
export const readData = (filename: string | undefined) => {
    if (!filename) return undefined;
    const data = fs.readFileSync(filename).toString();
    return data;
};

// creating a navigation item from processed title and url
export const createItem = (data: string | undefined) => {
    if (!data) return undefined;
    const title = createItemTitle(data);
    const url = createItemUrl(data);
    return `  - ${title}\n    ${url}\n`;
};

// appending navigation item to the navigation file
export const appendNavigationItem = (filePath: string | undefined, item: string | undefined) => {
    if (item) {
        fs.appendFileSync(filePath, item);
    }
};

// looping through *.md files in ETNA/ directory and appending nav items to the nav file
export const appendNavigationItems = (filePath: string | undefined) => {
    listFilesFromDir('etna', /^.+\.md$/, (filename: string | undefined) => {
        appendNavigationItem(filePath, createItem(readData(filename)));
    });
};

// specifying the navigation file and creating the navigation inside of it
export const createNavigation = () => {
    createFile('guides.yml');
    appendNavigationItems('guides.yml');
};
