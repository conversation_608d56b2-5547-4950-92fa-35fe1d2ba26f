import { listFilesFromDir } from './files-listing';
import { getData, getFirstLine } from './get-new-path';

const fs = require('fs');

// function, that checks if folders were created
export const checkIfPathExists = (path: string) => {
    return fs.existsSync(path);
};

// function, that creates folders from directory paths
export const createFolders = (dirPath: string) => {
    fs.mkdirSync(dirPath, { recursive: true });
};

// function, that copies files from their current directories to ETNA folder
export const createFiles = (path: string, newPath: string) => {
    fs.copyFileSync(path, newPath);
};

// function, that fires folders and files creation from array of paths, returns array of paths to files in ETNA folder
export const createFoldersAndFiles = (filenamesArray: Array<string | undefined>) => {
    const targetFilenamesArray = filenamesArray.map((path: string) => {
        let newPath = `${getFirstLine(getData(path))?.toLowerCase()}.md`;
        if (newPath && newPath.includes('xtreem/')) {
            if (newPath === 'xtreem/etna/xtrem-cli.md') {
                newPath = 'xtreem/xtrem-cli/introduction.md';
            }
            const dirPath = newPath.substr(0, newPath.lastIndexOf('/'));
            createFolders(dirPath);
            createFiles(path, newPath);
            if (!checkIfPathExists(dirPath) && !checkIfPathExists(newPath)) {
                throw new Error('Folders and files were not created');
            }
            return newPath;
        }
        return undefined;
    });
    return targetFilenamesArray;
};

// function, that filters .md files from @sage, returns array of paths to copied .md files in ETNA folder
export const printFilesFromDir = (path: string) => {
    const filenamesArray: Array<string> = [];
    listFilesFromDir(path, /^doc(?:-.+|)\.md$/, (filename: string) => {
        if (filename) filenamesArray.push(filename);
    });
    if (filenamesArray) {
        const targetFilenamesArray = createFoldersAndFiles(filenamesArray);
        return targetFilenamesArray;
    }
    return filenamesArray;
};
