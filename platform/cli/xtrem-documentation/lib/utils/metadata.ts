import * as fs from 'fs';
import { getData, getFirstLine } from './get-new-path';

// CREATING METADATA

// creating permalink in metadata from first line (PATH) in *.md files
export const createPermalink = (firstLineArray: string[] | undefined) => {
    if (!firstLineArray) return undefined;
    if (firstLineArray[2] === 'Etna-CLI') return '/guides/xtrem-cli/introduction/';
    return `/guides/${firstLineArray[1]}/${firstLineArray[2]}/${firstLineArray[3] ? `${firstLineArray}/` : ''}`
        .toLowerCase()
        .split('+')
        .join('-');
};

// creating title in metadata from first line (PATH) in *.md files
export const createTitle = (firstLineArray: string[] | undefined) => {
    if (!firstLineArray) return undefined;
    if (firstLineArray.reverse()[0] === 'Etna-CLI') return 'Introduction';
    return firstLineArray[0].split('-').join(' ');
};

// creating category in metadata from first line (PATH) in *.md files
export const createCategory = (firstLineArray: string[] | undefined) => {
    if (!(firstLineArray && firstLineArray.length > 1)) return undefined;
    if (firstLineArray[0] === 'Etna-CLI') return 'Etna CLI';
    return firstLineArray.reverse()[1].split('-').join(' ');
};

// creating metadata from processed title, category and permalink
export const createMetadata = (
    title: string | undefined,
    category: string | undefined,
    permalink: string | undefined,
) => {
    if (!(title && permalink)) return undefined;
    return `---\r\ntitle: "${title}"${
        category ? `\r\ncategory: "${category}"` : ''
    }\r\npermalink: ${permalink}\r\nlayout: documentation\r\nhide_breadcrumb: false\r\n---\r\n`;
};

// GETTING DATA FROM MD FILES

// processing first line in *.md files (PATH) to an array
export const getFirstLineArray = (firstLine: string | undefined) => {
    if (!firstLine) return undefined;
    return firstLine.split('/');
};

// REMOVING PATH

// removing first line in *.md files (PATH)
export const removePath = (data: string | undefined) => {
    if (!data) return undefined;
    const firstLine = data.split('\n')[0];
    if (firstLine.split(': ')[0] === 'PATH') {
        return data.replace(`${firstLine}`, '');
    }
    return data;
};

// ADDING METADATA

export const addMetadata = (fileNamesArray: (string | undefined)[]) => {
    fileNamesArray.forEach((fileName: string | undefined) => {
        if (fileName) {
            const data: string | undefined = getData(fileName);
            const firstLine: string | undefined = getFirstLine(data);
            const firstLineArray: string[] | undefined = getFirstLineArray(firstLine);
            const permalink: string | undefined = createPermalink(firstLineArray);
            const title: string | undefined = createTitle(firstLineArray);
            const category: string | undefined = createCategory(firstLineArray);

            const metadata: string | undefined = createMetadata(title, category, permalink);
            const newData: string | undefined = removePath(data);

            // replacing contents of *.md files
            if (data && metadata && !newData?.toString().includes('---\r\ntitle:')) {
                fs.writeFileSync(fileName, `${metadata}${newData}`);
            } else {
                // eslint-disable-next-line no-console
                console.log(`Couldn't add metadata to ${fileName}`);
            }
        }
    });
};
