#! /bin/bash
git clone --branch qa https://github.com/Sage/sage_developer_api_etna_platform.git DESTINATION
mkdir DESTINATION/_data/sidebars/
mkdir DESTINATION/reference
mkdir DESTINATION/_guides
cp -R etna/* DESTINATION/_guides 
cp -R docs/* DESTINATION/reference
cp -R guides.yml DESTINATION/_data/sidebars/
cd DESTINATION
git add _guides
git add reference
git add _data/sidebars/guides.yml
git commit -m "Add latest guides"
git push
