/* eslint-disable no-console */
import { printFilesFromDir } from './utils/extraction';
import { addMetadata } from './utils/metadata';
import { result } from './utils/move-files';
import { createNavigation } from './utils/navigation-creation';

export const runner = () => {
    addMetadata(printFilesFromDir('../../@sage'));
    createNavigation();
    result('sh lib/utils/commands.sh', (err: any, response: any) => (!err ? console.log(response) : console.log(err)));
};
