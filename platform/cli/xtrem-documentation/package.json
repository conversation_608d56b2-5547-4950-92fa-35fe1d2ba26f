{"name": "@sage/xtrem-documentation", "description": "Xtrem Documentation utility", "version": "58.0.2", "license": "UNLICENSED", "author": "Sage", "publishConfig": {"registry": "https://pkgs.dev.azure.com/Sage-LiveServices/_packaging/Sage-ERP/npm/registry/"}, "repository": {"type": "git", "url": "git://github.com/Sage-ERP-X3/xtrem.git"}, "main": "build/index.js", "typings": "build/package-definition.d.ts", "dependencies": {}, "devDependencies": {"@sage/xtrem-dts-bundle": "workspace:*", "@sage/xtrem-minify": "workspace:*", "@types/chai": "^4.3.6", "@types/mocha": "^10.0.1", "c8": "^10.1.2", "chai": "^4.3.10", "cross-env": "^7.0.3", "eslint": "^8.49.0", "mocha": "^10.8.2", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "~5.8.3"}, "scripts": {"build": "tsc -b -v . && pnpm dts-bundle", "build:binary": "pnpm clean && pnpm build && xtrem-minify -c -z \"build/**/*.js\"", "build:cache": "turbo run build --concurrency=${XTREM_CONCURRENCY:=10}", "clean": "rm -rf build junit-report*", "dts-bundle": "xtrem-dts-bundle", "lint": "eslint -c .eslintrc.js --ext .ts lib test", "lint:filename": "eslint --no-eslintrc -c .eslintrc-filename.js \"**\"", "start": "node bin/xtrem-documentation", "test": "mocha --recursive --exit \"test/**/*@(-|.)test.ts\"", "test:ci": "cross-env JUNIT_REPORT_PATH=junit-report-documentation.xml JUNIT_REPORT_NAME='xtrem-documentation' c8 --reporter=lcov --reporter=json --reporter=cobertura --reporter=text mocha  --recursive --exit \"test/**/*@(-|.)test.ts\" --reporter mocha-jenkins-reporter"}, "c8": {"reporter": ["json", "lcov", "text-summary"], "extension": [".ts"], "exclude": ["**/*.d.ts", "**/*-test.ts"]}}