import { asyncArray } from '@sage/xtrem-core';
import type { Argv } from 'yargs';

export interface PluggableHandlerResult {
    skipDefault?: boolean;
}

export interface CliPlugin {
    name: string;
    exports: any;
}

export class PluginRegistry {
    #plugins: CliPlugin[] = [];

    get plugins(): CliPlugin[] {
        return [...this.#plugins];
    }

    add(plugin: CliPlugin) {
        this.#plugins.push(plugin);
    }
}

export const pluginRegistry = new PluginRegistry();

export function pluggableBuilder(command: string | string[], yargs: Argv): Argv {
    const commandName = getCommandName(command);
    const builders = pluginRegistry.plugins
        .filter(p => typeof p.exports.builder?.[commandName] === 'function')
        .map(p => p.exports.builder[commandName]);
    builders.forEach(builder => builder(yargs));
    return yargs;
}

export async function pluggableHandler(
    command: string | string[],
    args: any,
    commandContext?: any,
): Promise<PluggableHandlerResult> {
    const commandName = getCommandName(command);
    const handlers = pluginRegistry.plugins
        .filter(p => typeof p.exports.handler?.[commandName] === 'function')
        .map(p => p.exports.handler[commandName]);

    let skipDefault = false;
    await asyncArray(handlers).forEach(async handler => {
        let result = handler(args, commandContext);
        if (result instanceof Promise) {
            result = await result;
        }
        skipDefault ||= result?.skipDefault;
    });

    return {
        skipDefault,
    };
}

function getCommandName(command: string | string[]): string {
    return (Array.isArray(command) ? command[0] : command).split(/\s+/)[0];
}
