import { ExecutionMode } from './utils';

export interface CliContext {
    executionMode: ExecutionMode;
    dir: string;
    withExecutionMode: (executionMode: ExecutionMode, body: () => void) => void;
}

class CliContextConcret implements CliContext {
    #executionMode: ExecutionMode;

    #dir: string;

    #options: Record<string, any> = {};

    constructor(executionMode: ExecutionMode, dir: string, options?: Record<string, any>) {
        this.#executionMode = executionMode;
        this.#dir = dir;
        if (options) {
            this.#options = options;
        }
    }

    get executionMode(): ExecutionMode {
        return this.#executionMode;
    }

    set executionMode(executionMode: ExecutionMode) {
        this.#executionMode = executionMode;
    }

    get options(): Record<string, any> {
        return this.#options;
    }

    set options(options: Record<string, any>) {
        this.#options = options;
    }

    get dir(): string {
        return this.#dir;
    }

    set dir(dir: string) {
        this.#dir = dir;
    }

    withExecutionMode(executionMode: ExecutionMode, body: () => void): void {
        const oldMode = this.#executionMode;
        this.#executionMode = executionMode;
        body();
        this.#executionMode = oldMode;
    }
}
export const cliContext = new CliContextConcret(ExecutionMode.STANDALONE, process.cwd());
