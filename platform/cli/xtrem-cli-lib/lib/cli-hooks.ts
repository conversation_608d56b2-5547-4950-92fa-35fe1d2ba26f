import { Application, ContextOptions, Package, StartChannel, SystemError, UpgradeMetricsType } from '@sage/xtrem-core';

export interface CliBundleManager {
    activate(application: Application, tenantId: string, bundleId: string, pack: Package): void;
    deactivate(application: Application, tenantId: string, bundleId: string): void;
}

export type UpgradeMode =
    /**
     * The upgrade will only replay previously recorded SQL queries (will not run any upgrade actions)
     * This mode will raise an error if some recording files are missing.
     * For instance, we are on version 6.0.0 and we want to upgrade to 7.0.0.
     * An error will be raised if no SQL file is available from 6.0.40 to 6.0.48
     */
    | 'replayOnly'
    /**
     * The upgrade will replay previously recorded SQL queries and then run the upgrade actions
     * For instance, we are on version 6.0.0 and we want to upgrade to 7.0.0.
     * We have recorded SQL queries for versions [6.0.0 to 6.0.49[
     * The upgrade will:
     * - replay the SQL queries for versions [6.0.0 to 6.0.49[
     * - run the upgrade actions (and record them) for versions [6.0.49 to 7.0.0[
     */
    | 'replayAndUpgrade'
    /**
     * Same as replayAndUpgrade except that the SQL will be recorded to a local file
     */
    | 'replayAndRecord'
    /**
     * The upgrade will skip the playing of recorded SQL queries
     */
    | 'upgradeOnly';

export interface UpgradeOptions {
    /** Should the upgrade be executed even if the versions match (i.e. the package seems to be up-to-date) */
    force: boolean;

    /**
     * Upgrade mode:  replayOnly / replayAndUpgrade / replayAndRecord / upgradeOnly
     */
    mode: UpgradeMode;

    /**
     * Should all the CSV files from the SETUP layer be reloaded at the end of the upgrade ?
     * This will bypass the lookup from the git repo and reload all the **setup** CSV files
     */
    fullReloadOfSetupLayer: boolean;

    /**
     * Should metrics files be generated ?
     * undefined : no CSV/JSON files, local:only on local, s3: local + S3
     */
    metrics?: UpgradeMetricsType;

    /**
     * Should we skip VACUUM at the end of the upgrade
     */
    skipVacuum?: boolean;
    /**
     * Check that the SQL schema is in sync with node definitions. Raise an error if not.
     */
    checkSchema?: boolean;
    /**
     * The optional name of the cluster to upgrade
     */
    clusterName?: string;
}

export interface CliUpgradeManager {
    upgradeSqlSchemaFromCli(application: Application, options: UpgradeOptions): Promise<void>;
    /**
     * Rename the 'vlatest' folders of upgrades (upgrade suites, system upgrades)
     */
    renameLatestFolders(rootFolder: string): void;
    fixColumnOrder(application: Application): Promise<void>;

    /**
     * Execute a custom SQL script
     * @param path the path to the script to execute
     * @param tenantIds the tenantIds on which the script has to be executed
     * @param dryRun run in dry mode ? (no commit)
     */
    executeCustomSqlScript(application: Application, path: string, tenantIds: string[], dryRun: boolean): Promise<void>;
}

export interface CliDataPatchManager {
    execute(application: Application): void;
    postProcess(application: Application): void;
}

function invalidStubCall(): never {
    throw new SystemError('Invalid stub call');
}

export interface StartApplicationOptions {
    channels: StartChannel[];
}

export interface CliTestManager {
    prepareLoadTestDatabase(application: Application, contextOptions: ContextOptions): Promise<void>;
}

export abstract class CliHooks {
    static packageManager: CliBundleManager = {
        activate: invalidStubCall,
        deactivate: invalidStubCall,
    };

    static upgradeManager: CliUpgradeManager = {
        upgradeSqlSchemaFromCli: invalidStubCall,
        /**
         * Rename the 'vlatest' folders that contain upgrade suites
         */
        renameLatestFolders: invalidStubCall,
        fixColumnOrder: invalidStubCall,
        executeCustomSqlScript: invalidStubCall,
    };

    static dataPatchManager: CliDataPatchManager = {
        execute: invalidStubCall,
        postProcess: invalidStubCall,
    };

    static testManager: CliTestManager = {
        prepareLoadTestDatabase: invalidStubCall,
    };
}
