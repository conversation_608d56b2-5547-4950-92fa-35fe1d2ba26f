import { Logger } from '@sage/xtrem-core';
import { ExecutionMode, printError, printInfo, printWarning } from './common';

/**
 * Subclass of standard xtrem-log logger, to get the fancy cli formatting.
 */
export class CliLogger extends Logger {
    constructor(
        readonly executionMode: ExecutionMode,
        readonly options?: {
            warningsAsErrors?: boolean;
        },
    ) {
        super(__filename, 'xtrem-cli');
    }

    profilerCallback = {
        success: (message: string) => {
            printInfo(this.executionMode, message);
        },
        fail: (message: string) => {
            printError(this.executionMode, message);
        },
    };

    override error(message: string): this['profilerCallback'] {
        printError(this.executionMode, message);
        return this.profilerCallback;
    }

    override warn(message: string): this['profilerCallback'] {
        if (this.options?.warningsAsErrors) throw new Error(message);
        printWarning(this.executionMode, message);
        return this.profilerCallback;
    }

    override info(message: string): this['profilerCallback'] {
        printInfo(this.executionMode, message);
        return this.profilerCallback;
    }

    // Don't override verbose and debug - keep the standard format
}
