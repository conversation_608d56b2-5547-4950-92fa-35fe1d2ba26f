import { Application, ApplicationManager, ApplicationStartServicesOptions } from '@sage/xtrem-core';
import * as path from 'path';
import { ExecutionMode } from './common';

export interface TypescriptObserverInput extends ApplicationStartServicesOptions {
    dir: string;
    deployedApp: { app: Application | null };
    executionMode: ExecutionMode;
    autoReload: boolean;
}

export const unloadApplication = (dir: string, application: Application) => {
    return new Promise<void>((resolve, reject) => {
        const paths = application
            .getPackages()
            .map(a => {
                try {
                    return path.dirname(require.resolve(a.name, { paths: [dir] }));
                } catch (e) {
                    return null;
                }
            })
            .filter(v => !!v) as string[];

        // Add the package's source code
        paths.push(ApplicationManager.getBuildDir(dir));

        const loadedPaths = Object.keys(require.cache);

        // Remove application modules for require 's cache so we load in a fresh copy.
        paths.forEach(modulePath => {
            loadedPaths.forEach(cachePath => {
                if (cachePath.startsWith(modulePath)) {
                    delete require.cache[cachePath];
                }
            });
        });

        // Closing down the http service.
        const graphqlHttpServer = application.graphqlHttpServer;
        if (graphqlHttpServer)
            graphqlHttpServer.close(err => {
                if (err) {
                    reject(err);
                } else {
                    graphqlHttpServer.emit('close');
                    resolve();
                }
            });
    });
};

export const reloadApplication = async (
    deployedApp: { app: Application | null },
    dir: string,
    options: ApplicationStartServicesOptions,
    startServices: (dir: string, options: ApplicationStartServicesOptions) => Promise<void>,
    reload = true,
) => {
    const app = deployedApp.app;
    if (app?.graphqlHttpServer?.listening) {
        await unloadApplication(dir, app);
        deployedApp.app = null;
    }
    if (reload) {
        await startServices(dir, options);
    }
};
