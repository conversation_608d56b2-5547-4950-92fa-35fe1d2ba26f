/* eslint-disable no-console */
import { AnyValue, AsyncResponse, Logger } from '@sage/xtrem-core';
import * as chalk from 'chalk';
import { EventType, broadcastMessage } from './socket-helper';

const logger = new Logger(__filename, 'cli');

export enum ExecutionMode {
    STANDALONE = 'STANDALONE',
    INTEGRATED = 'INTEGRATED',
}

export const plural = (a: string, b: string, count: number) => `${count} ${count === 1 ? a : b}`;

function isErrorObject(err: any): err is Error {
    return err instanceof Error;
}

/**
 * Raise an error when the command line could not be processed
 */
export function raiseErrorOnInvalidCommandLine(): never {
    throw new Error(`Could not parse command line: ${[...process.argv].splice(1).join(' ')}`);
}

export const quitWithError = (mode: ExecutionMode, error: string | Error) => {
    const errorMessage = isErrorObject(error) ? error.message : error;
    if (mode === ExecutionMode.STANDALONE) {
        if (isErrorObject(error)) {
            console.log(error.stack);
        }
        console.log(chalk.red(`   🚨🌋 ${errorMessage}   `));
        broadcastMessage(EventType.FATAL_ERROR, errorMessage);
        process.exit(1);
    } else {
        logger.error(errorMessage);
        throw new Error(errorMessage);
    }
};

/**
 * Are we in a process ?
 * We consider a process starts with a printInfo and ands with a printSuccess/printError
 */
let _inProcess = false;

/**
 * The start time of the current process
 */
let _processStartTime = Date.now();

export const printSuccess = (mode: ExecutionMode, message: string, logDuration = true) => {
    if (mode === ExecutionMode.STANDALONE) {
        if (logDuration) {
            console.log(chalk.green(`   ✅    ${message} (${Date.now() - _processStartTime} ms)  `));
        } else {
            console.log(chalk.green(`   ✅    ${message}`));
        }
        _inProcess = false;
    } else {
        console.log(chalk.green(`   ✅    ${message}`));
    }
};

export const printInfo = (mode: ExecutionMode, message: string) => {
    if (mode === ExecutionMode.STANDALONE) {
        console.log(chalk.blue(`   📢    ${message}   `));
        if (!_inProcess) {
            _inProcess = true;
            _processStartTime = Date.now();
        }
    } else {
        logger.info(message);
    }
};

export const printWarning = (mode: ExecutionMode, message: string) => {
    if (mode === ExecutionMode.STANDALONE) {
        console.log(chalk.yellow(`   ⚠️    ${message}   `));
    } else {
        logger.warn(message);
    }
};

export const printError = (mode: ExecutionMode, message: string) => {
    if (mode === ExecutionMode.STANDALONE) {
        console.log(chalk.red(`   ❌    ${message} (${Date.now() - _processStartTime} ms)  `));
        _inProcess = false;
    } else {
        logger.error(message);
    }
};

export const withTiming = async <T extends AnyValue | void>(
    mode: ExecutionMode,
    options: {
        before?: () => string;
        body: () => AsyncResponse<T>;
        success?: (result: T) => string;
        fail?: (err: Error) => string;
    },
): Promise<T> => {
    const t0 = Date.now();
    if (options.before) printInfo(mode, options.before());
    try {
        const result = await options.body();
        const millis = Date.now() - t0;
        let color = chalk.red;
        if (millis < 1000) {
            color = chalk.gray;
        } else if (millis < 5000) {
            color = chalk.yellow;
        }
        const coloredMillis = color(`(${millis} ms)`);
        if (options.success) printInfo(mode, `${options.success(result)} ${coloredMillis}`);

        return result;
    } catch (err) {
        if (options.fail) printError(mode, options.fail(err));
        throw err;
    }
};

export const isDebugging =
    typeof (global as any) === 'object' || /--debug|--inspect|--inspect-brk/.test(process.execArgv.join(' '));
