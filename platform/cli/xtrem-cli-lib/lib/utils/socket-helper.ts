import { Logger } from '@sage/xtrem-core';
import { debounce } from 'lodash';
import * as path from 'path';
import * as io from 'socket.io';
import * as ts from 'typescript';

const logger = new Logger(__filename, 'xtrem-cli-socket');

const authenticatedClients: io.Socket[] = [];
const errorsToDispatch: CompilationError[] = [];

export enum EventType {
    AUTH = 'AUTH',
    REBOUNCE = 'REBOUNCE',
    FATAL_ERROR = 'FATAL_ERROR',
    COMPILER_STARTED = 'COMPILER_STARTED',
    COMPILER_SUCCESS = 'COMPILER_SUCCESS',
    COMPILER_FAILURE = 'COMPILER_FAILURE',
}

export type ErrorSource = 'server' | 'client';

export interface Event {
    type: EventType;
    data: any;
}

export interface CompilationError {
    code: number;
    file: string;
    line: number;
    character: number;
    message: string;
    source: ErrorSource;
}

export const initiateSocketServer = (
    key: string,
    options?: {
        onRebounce: () => void;
    },
): Promise<void> => {
    return new Promise<void>(resolve => {
        const server = new io.Server();
        server.on('connection', client => {
            client.on(EventType.AUTH, data => {
                if (String(data) === String(key)) {
                    authenticatedClients.push(client);
                    logger.debug(() => `Client connected and authenticated ${client.id}`);
                    resolve(); // Resolve promise when the first client connects.
                } else {
                    logger.warn(() => `Client failed to authenticate: ${client.id}`);
                    client.disconnect();
                }
            });

            client.on(EventType.REBOUNCE, () => {
                if (authenticatedClients.indexOf(client) !== -1) {
                    options?.onRebounce?.();
                }
            });

            client.on(EventType.AUTH, data => {
                if (String(data) === String(key)) {
                    authenticatedClients.push(client);
                    logger.debug(() => `Client connected and authenticated ${client.id}`);
                    resolve(); // Resolve promise when the first client connects.
                } else {
                    logger.warn(() => `Client failed to authenticate: ${client.id}`);
                    client.disconnect();
                }
            });

            client.on('disconnect', () => {
                const index = authenticatedClients.indexOf(client);
                logger.debug(() => `Client was disconnected: ${client.id}`);
                if (index !== -1) {
                    authenticatedClients.splice(index, 1);
                }
            });
        });

        server.listen(4002);
        logger.debug(() => `Compilation event stream has been initialized with the following key: ${key}`);
    });
};

export const broadcastMessage = (type: EventType, data?: any) => {
    logger.debug(() => `Broadcasting message: ${type}`);
    authenticatedClients.forEach(c => {
        c.emit(type, data);
    });
};

const dispatchErrors = () => {
    broadcastMessage(EventType.COMPILER_FAILURE, errorsToDispatch);
    errorsToDispatch.splice(0, errorsToDispatch.length);
};

const debouncedDispatch = debounce(dispatchErrors, 300);

export const addTsDiagnosticToDispatchQueue = (diagnostic: ts.Diagnostic, source: ErrorSource) => {
    if (diagnostic.file && diagnostic.start) {
        const { line, character } = diagnostic.file.getLineAndCharacterOfPosition(diagnostic.start);
        const message = ts.flattenDiagnosticMessageText(diagnostic.messageText, '\n').trim();
        addCompilerErrorToDispatchQueue({
            code: diagnostic.code,
            file: diagnostic.file.fileName,
            line: line + 1,
            character: character + 1,
            message,
            source,
        });
    }
};

export const addTsLoaderErrorToDispatchQueue = (error: string, dir: string) => {
    const lines = error.split('\n');
    if (lines.length > 0) {
        try {
            const errorObject = JSON.parse(lines[lines.length - 1]);

            addCompilerErrorToDispatchQueue({
                code: errorObject.code,
                file: path.isAbsolute(errorObject.file) ? errorObject.file : path.resolve(dir, errorObject.file),
                line: errorObject.line,
                character: errorObject.character,
                message: errorObject.content,
                source: 'client',
            });
            return;
            // eslint-disable-next-line no-empty
        } catch (e) {}
    }

    addCompilerErrorToDispatchQueue({
        code: 1000,
        file: 'UNKNOWN',
        line: 0,
        character: 0,
        message: error,
        source: 'client',
    });
};

export const addCompilerErrorToDispatchQueue = (error: CompilationError) => {
    errorsToDispatch.push(error);
    debouncedDispatch();
};

export const broadcastCompilerSuccessMessage = (type: ErrorSource) => {
    if (errorsToDispatch.length === 0) {
        broadcastMessage(EventType.COMPILER_SUCCESS, type);
    }
};
