import * as fs from 'fs';
import * as path from 'path';

export enum PackagingMode {
    DEV = 'DEV',
    PROD = 'PROD',
}

export enum ArtifactTypes {
    Pages = 'pages',
    Stickers = 'stickers',
    PageExtensions = 'page-extensions',
    PageFragments = 'page-fragments',
}

export const getNpmCommandName = () => (process.platform === 'win32' ? 'npm.cmd' : 'npm');

export function safeRmSync(fileOrDir: string, options?: fs.RmOptions): void {
    try {
        fs.rmSync(fileOrDir, options);
    } catch (e) {
        if (e.code !== 'ENOENT') {
            throw e;
        }
    }
}

export function copyFile(source: string, target: string) {
    let targetFile = target;
    const targetDir = path.dirname(targetFile);

    if (!fs.existsSync(targetDir)) {
        fs.mkdirSync(targetDir);
    }

    if (fs.existsSync(target)) {
        if (fs.lstatSync(target).isDirectory()) {
            targetFile = path.join(target, path.basename(source));
        }
    }

    fs.writeFileSync(targetFile, fs.readFileSync(source));
}

export function copyFolderRecursive(source: string, target: string, noPreserve = false) {
    let files = [];

    const targetFolder = noPreserve ? path.resolve(target) : path.join(target, path.basename(source));
    if (!fs.existsSync(targetFolder)) {
        fs.mkdirSync(targetFolder);
    }

    if (fs.lstatSync(source).isDirectory()) {
        files = fs.readdirSync(source);
        files.forEach(file => {
            const curSource = path.join(source, file);
            if (fs.lstatSync(curSource).isDirectory()) {
                copyFolderRecursive(curSource, targetFolder);
            } else {
                copyFile(curSource, targetFolder);
            }
        });
    }
}
