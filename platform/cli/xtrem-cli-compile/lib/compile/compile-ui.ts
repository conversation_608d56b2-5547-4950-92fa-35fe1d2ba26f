import { ExecutionMode, plural, printInfo, printSuccess } from '@sage/xtrem-cli-lib';
import { glob } from 'glob';
import { CompileOptions } from '.';
import {
    checkTypesOnClientArtifactsAndBuildDeclarations,
    compileClientAndSharedFunctions,
    compilePagesAndStickers,
    isCompilable,
} from './utils/client-compilation';
import { printCompilerErrorMessage } from './watch-client';

export const compileUi = async (executionMode: ExecutionMode, buildDir: string, options: CompileOptions, packageName: string) => {
    const { isProd, isInstrumented, isOnlyChanged } = options;
    if (isOnlyChanged) {
        printInfo(executionMode, 'Only changed client fields will be compiled.');
    }

    compileClientAndSharedFunctions(executionMode, buildDir);
    if (!options.skipClientTypeCheck) {
        checkTypesOnClientArtifactsAndBuildDeclarations(executionMode, buildDir);
    }

    const globOptions = { cwd: buildDir, absolute: true, realpath: true };
    await new Promise<void>((resolve, reject) => {
        const clientArtifacts = glob
            .sync('./lib/{pages,stickers,page-extensions,widgets,page-fragments}/*.{c,}ts', globOptions)
            .filter(isCompilable);

        if (clientArtifacts.length === 0) {
            printInfo(executionMode, 'No client side artifacts found.');
            resolve();
        }

        printInfo(executionMode, 'Compiling client side artifacts...');
        compilePagesAndStickers({ executionMode, buildDir, isProd, isInstrumented, isOnlyChanged, packageName }).subscribe(
            result => {
                const statsObj = result.stats && result.stats.toJson();
                const hasWarnings = statsObj && statsObj.warnings && statsObj.warnings.length > 0;
                const hasErrors = statsObj && statsObj.errors && statsObj.errors.length > 0;
                const isCompilationSuccessful = !result.error && !hasWarnings && !hasErrors;

                if (isCompilationSuccessful) {
                    printSuccess(executionMode, 'Successfully compiled all client side artifacts.');
                    resolve();
                } else {
                    if (!statsObj) {
                        reject(new Error("Couldn't compile client artifacts."));
                    }
                    if (hasWarnings) statsObj?.warnings?.forEach(w => printCompilerErrorMessage(executionMode, w));
                    if (hasErrors) statsObj?.errors?.forEach(e => printCompilerErrorMessage(executionMode, e));
                    reject(
                        new Error(
                            `${plural(
                                'error was',
                                'errors were',
                                statsObj?.errors?.length || 0,
                            )} found during Webpack compilation.`,
                        ),
                    );
                }
            },
        );
    });
};
