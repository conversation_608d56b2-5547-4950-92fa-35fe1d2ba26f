import { ExecutionMode, printInfo, printSuccess } from '@sage/xtrem-cli-lib';
import * as esquery from 'esquery';
import { ArrowFunctionExpression, FunctionDeclaration, FunctionExpression, Node, Property } from 'estree';
import { glob } from 'glob';
import * as lodash from 'lodash';

const xtremBytenode = require('@sage/xtrem-minify');
const parse = require('espree').parse;

function getPreservedRanges(originalSource: string): [number, number][] {
    const ast = parse(originalSource, { ecmaVersion: 2022 });
    // The selectors below are not very specific so we may get some false positives.
    // As a consequence we may preserve the source of some functions for which we don't really need the source code.
    // This is acceptable as long as the selectors remain reasonably specific.

    const registeredFunctions = esquery.query(
        ast,
        'CallExpression:has(SequenceExpression > MemberExpression[property.name="registerSqlFunction"])',
    );
    // If functions are registered keep the whole file because the function and its registration may be in different places.
    // This will work as long as the registration is done in the same file as the function.
    if (registeredFunctions.length > 0) return [[0, originalSource.length]];

    // Methods for which we need to preserve declaration + body
    const fullMethods = (
        [
            ...esquery.query(ast, 'Property[key.name=/^(defaultValue|getValue|filter|isClearedByReset|where)$/]'),
            ...esquery.query(ast, 'Property[key.name=/^(set|join)$/] > ObjectExpression > Property'),
        ] as Property[]
    ).filter(property => property.value.type === 'FunctionExpression');

    const allMethods = (esquery.query(ast, 'Property') as Property[]).filter(
        property => property.value.type === 'FunctionExpression',
    );

    // All other methods and functions. We have to preserve their declarations but we can blank out their bodies.
    // If we don't preserve declarations we get a node crash in DoParseFunction / stack_overflow when generating stack traces
    const emptyMethods = lodash.difference(allMethods, fullMethods);
    const emptyFunctions = esquery.query(ast, 'FunctionDeclaration') as FunctionDeclaration[];
    const emptyArrowFunctions = esquery.query(ast, 'ArrowFunctionExpression') as ArrowFunctionExpression[];

    // The node.range returned by esquery is undefined but we can get the range with start and end.
    const getStart = (node: Node) => (node as any).start;
    const getEnd = (node: Node) => (node as any).end;
    const getRange = (node: Node): [number, number] => [getStart(node), getEnd(node)];

    const declarationStartRange = (declNode: Node, bodyNode: Node) => [getStart(declNode), getStart(bodyNode)];
    const declarationEndRange = (declNode: Node, bodyNode: Node) => [getEnd(bodyNode) - 1, getEnd(declNode)];

    // Generate all the ranges
    const ranges = [
        ...fullMethods.map(getRange),
        ...registeredFunctions.map(getRange),
        ...emptyMethods.map(prop => declarationStartRange(prop, (prop.value as FunctionExpression).body)),
        ...emptyMethods.map(prop => declarationEndRange(prop, (prop.value as FunctionExpression).body)),
        ...emptyFunctions.map(decl => declarationStartRange(decl, decl.body)),
        ...emptyFunctions.map(decl => declarationEndRange(decl, decl.body)),
        ...emptyArrowFunctions.map(decl => declarationStartRange(decl, decl.body)),
        ...emptyArrowFunctions.map(decl => declarationEndRange(decl, decl.body)),
    ] as [number, number][];

    // Filter and sort the ranges
    return ranges.filter(range => range[1] > range[0]).sort((range1, range2) => range1[0] - range2[0]);
}

export const binaryEncoding = async (executionMode: ExecutionMode, dir: string): Promise<void> => {
    printInfo(executionMode, 'Encoding server side artifacts to binary format...');

    const globOptions = {
        cwd: dir,
        absolute: true,
        realpath: true,
        ignore: [
            // index files
            './build/index.js',
            './build/lib/index.js',
            // pages, stickers, menu items, client functions, shared functions
            './build/lib/{pages,sticker,menu-items,client-functions,shared-functions}/**/*.js',
        ],
    };
    // add external nodes to the list of exclusions
    if (/\/(x3-services|wh-services)\//.test(dir.replace(/\\/g, '/'))) {
        globOptions.ignore.push('./build/lib/node*/**/*.js');
    }
    const files = glob.sync('./build/**/*.js', globOptions);

    await xtremBytenode.compileAllFiles(dir, files, {
        compileAsModule: true,
        compress: true,
        deleteSource: true,
        getPreservedRanges,
    });

    printSuccess(executionMode, 'Server side artifacts encoded to binary format.');
};
