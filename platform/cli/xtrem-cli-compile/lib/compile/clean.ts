import { ExecutionMode, printInfo, printSuccess } from '@sage/xtrem-cli-lib';
import * as fs from 'fs';
import { RmOptions } from 'fs';
import * as path from 'path';

function safeRm(fileOrDir: string, options?: RmOptions): void {
    try {
        fs.rmSync(fileOrDir, options);
    } catch (e) {
        if (e.code !== 'ENOENT') {
            throw e;
        }
    }
}

export const clean = (executionMode: ExecutionMode, dir: string, options?: { force: boolean }) => {
    printInfo(executionMode, 'Cleaning working directory...');
    const buildDir = path.resolve(dir, 'build');
    safeRm(buildDir, { recursive: true });
    if (options?.force) {
        const apiDir = path.resolve(dir, 'api');
        const apiPackage = path.resolve(apiDir, 'package.json');
        safeRm(apiPackage);
        const apiDeclaration = path.resolve(apiDir, 'api.d.ts');
        safeRm(apiDeclaration);
    }
    const coverageDir = path.resolve(dir, 'coverage');
    safeRm(coverageDir, { recursive: true });
    const routingPath = path.join(dir, 'routing.json');
    safeRm(routingPath, { force: true });
    printSuccess(executionMode, 'Working directory is clear.');
};
