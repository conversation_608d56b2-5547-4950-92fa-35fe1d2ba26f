import { ExecutionMode, printError, printInfo, printSuccess, quitWithError } from '@sage/xtrem-cli-lib';
import {
    activityNameExtractor,
    dataTypesNameExtractor,
    decimalTransformer,
    enumTransformer,
    menuItemTransformer,
    messageTransformer,
    nodePropertyNameExtractor,
    notificationListenerTransformer,
    serviceOptionNameExtractor,
} from '@sage/xtrem-cli-transformers';
import * as ts from 'typescript';
import { getTsProgram, printCompilerError } from './utils';

export interface CompileServerOptions {
    isUsingReferences?: boolean;
    doNotGenerateRoutingFile?: boolean;
}

export const compileServer = (executionMode: ExecutionMode, dir: string, options?: CompileServerOptions) => {
    printInfo(executionMode, 'Compiling server side artifacts...');
    if (options?.isUsingReferences) {
        printInfo(executionMode, "Project references won't be built since this feature is yet to be implemented :/");
    }

    try {
        const program = getTsProgram(dir);
        const emitResult = program.emit(undefined, undefined, undefined, undefined, {
            before: [
                ...(options?.doNotGenerateRoutingFile ? [] : [notificationListenerTransformer]),
                decimalTransformer,
                messageTransformer,
                nodePropertyNameExtractor,
                serviceOptionNameExtractor,
                activityNameExtractor,
                enumTransformer,
                menuItemTransformer,
                dataTypesNameExtractor,
            ],
        });

        const allDiagnostics = ts.getPreEmitDiagnostics(program).concat(emitResult.diagnostics);

        allDiagnostics.forEach(printCompilerError(executionMode));

        if (allDiagnostics.length === 0) {
            printSuccess(executionMode, 'All server side artifacts compiled.');
        } else {
            quitWithError(executionMode, 'Failed to compile server side artifacts.');
        }
    } catch (e) {
        printError(executionMode, e.message);
        quitWithError(executionMode, 'Failed to compile server side artifacts.');
    }
};
