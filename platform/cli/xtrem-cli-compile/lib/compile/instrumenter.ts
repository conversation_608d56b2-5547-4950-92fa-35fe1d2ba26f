import { ExecutionMode, printInfo, printSuccess } from '@sage/xtrem-cli-lib';
import * as fs from 'fs';
import { glob } from 'glob';
import * as istanbulLibInstrument from 'istanbul-lib-instrument';
import * as path from 'path';

export const instrumentCode = (executionMode: ExecutionMode, dir: string) => {
    printInfo(executionMode, 'Instrumenting artifacts...');

    const files = glob.sync('./build/**/*.js', { cwd: dir, absolute: true, realpath: true });
    const instrumenter = istanbulLibInstrument.createInstrumenter({
        produceSourceMap: true,
        esModules: true,
        compact: false,
        debug: true,
        autoWrap: true,
        preserveComments: true,
    });

    const coverageDir = path.resolve(dir, 'coverage');
    if (!fs.existsSync(coverageDir)) {
        fs.mkdirSync(coverageDir);
    }

    const sourceMapStoreDir = path.resolve(coverageDir, 'source-maps');
    if (!fs.existsSync(sourceMapStoreDir)) {
        fs.mkdirSync(sourceMapStoreDir);
    }

    files.forEach(f => {
        const sourceCodeContent = fs.readFileSync(f, 'utf-8');
        const sourceMapPath = `${f}.map`;
        const sourceMapContent = JSON.parse(fs.readFileSync(sourceMapPath, 'utf-8'));
        // Updating entry to use absolute path to prevent remapping issues
        const reportedPath = path.normalize(f.replace('build', '').replace('.js', '.ts'));
        sourceMapContent.sources = [reportedPath];
        fs.writeFileSync(sourceMapPath, JSON.stringify(sourceMapContent), 'utf-8');

        // const covPath = f.replace('.js', '.cov');
        const result = instrumenter.instrumentSync(sourceCodeContent, reportedPath, sourceMapContent);
        fs.writeFileSync(f, result, 'utf-8');
        const instrumentedSourceMap = instrumenter.lastSourceMap();
        const instrumentedSourceMapPath = sourceMapPath.replace('.map', '.instrumented.map');
        fs.writeFileSync(instrumentedSourceMapPath, JSON.stringify(instrumentedSourceMap), 'utf-8');
    });

    printSuccess(executionMode, 'Artifacts instrumented.');
};
