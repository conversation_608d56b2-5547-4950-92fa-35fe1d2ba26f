import { ExecutionMode, printInfo, printSuccess, printWarning } from '@sage/xtrem-cli-lib';
import { dtsBundleApi } from '@sage/xtrem-dts-bundle';
import * as fs from 'fs';
import { glob } from 'glob';
import * as path from 'path';

// dts-generator generates an invalid definition file if directory contains an empty file.
function checkNoEmptySourceFile(executionMode: ExecutionMode, dir: string) {
    const emptyPath = glob.sync('./**/*.ts', { cwd: dir, absolute: true, realpath: true }).find(
        // test stat size first to only read small files
        p => fs.statSync(p).size < 10 && fs.readFileSync(p, 'utf8').trim().length === 0,
    );
    if (emptyPath) {
        // BL: Temporary change to prevent breaking builds
        printWarning(executionMode, `${emptyPath}: empty file`);
    }
}

export const generateSingleDefinition = (executionMode: ExecutionMode, dir: string) => {
    printInfo(executionMode, 'Creating single definition file...');
    const packageFile = path.resolve(dir, 'package.json');

    checkNoEmptySourceFile(executionMode, dir);

    dtsBundleApi(packageFile, path.resolve(dir, 'build', 'package-definition.d.ts'));

    printSuccess(executionMode, 'Single definition file created.');
};
