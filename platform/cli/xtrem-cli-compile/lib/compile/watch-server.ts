/* eslint-disable no-console */
import {
    broadcastCompilerSuccessMessage,
    broadcastMessage,
    EventType,
    ExecutionMode,
    printInfo,
    printSuccess,
    printWarning,
    reloadApplication,
    TypescriptObserverInput,
} from '@sage/xtrem-cli-lib';
import { decimalTransformer } from '@sage/xtrem-cli-transformers';
import { ApplicationStartServicesOptions } from '@sage/xtrem-core';
import * as path from 'path';
import { Observable, Observer } from 'rxjs';
import * as ts from 'typescript';
import { printCompilerError } from './utils';

const COMPILER_FINISHED_SUCCESSFULLY = 6194;
const COMPILER_FINISHED_WITH_ERRORS = 6193;
const COMPILER_INITIALIZED = 6031;
const COMPILER_DETECTED_CHANGES = 6032;

const handleCompilerEvents =
    (executionMode: ExecutionMode, observer: Observer<boolean>) => (diagnostic: ts.Diagnostic) => {
        switch (diagnostic.code) {
            case COMPILER_FINISHED_SUCCESSFULLY:
                printSuccess(executionMode, 'Typescript compiler finished successfully.');
                broadcastCompilerSuccessMessage('server');
                observer.next(true);
                break;
            case COMPILER_FINISHED_WITH_ERRORS:
                printCompilerError(executionMode)(diagnostic);
                observer.next(false);
                break;
            case COMPILER_INITIALIZED:
                printInfo(executionMode, diagnostic.messageText.toString());
                break;
            case COMPILER_DETECTED_CHANGES:
                printInfo(executionMode, 'Server changes detected!');
                broadcastMessage(EventType.COMPILER_STARTED, 'server');
                break;
            default:
                console.log(diagnostic.messageText, diagnostic.code);
        }
    };

export const startWatchServer = (executionMode: ExecutionMode, dir: string, isUsingReferences: boolean) => {
    if (isUsingReferences) {
        printInfo(executionMode, "Project references won't be built since this feature is yet to be implemented :/");
    }
    return new Observable((obs: Observer<boolean>) => {
        const compilerHost = ts.createWatchCompilerHost(
            path.resolve(dir, 'tsconfig.json'),
            {},
            ts.sys,
            ts.createEmitAndSemanticDiagnosticsBuilderProgram,
            printCompilerError(executionMode),
            handleCompilerEvents(executionMode, obs),
        );

        // Allow pending files to be saved (such as index file updates on deletion of an artifact) before compiler triggers
        compilerHost.setTimeout = (callback: (...args: any[]) => void, ms: number, ...args: any[]) => {
            return setTimeout(callback, Math.max(ms, 2000), ...args);
        };

        const originalAfterProgramCreate = compilerHost.afterProgramCreate;
        compilerHost.afterProgramCreate = builderProgram => {
            const originalEmit = builderProgram.emit;
            builderProgram.emit = (
                targetSourceFile,
                writeFile,
                cancellationToken,
                emitOnlyDtsFiles,
                customTransformers,
            ): ts.EmitResult => {
                const transformers = customTransformers || { before: [] };
                if (!transformers.before) transformers.before = [];
                transformers.before.push(decimalTransformer);
                return originalEmit(targetSourceFile, writeFile, cancellationToken, emitOnlyDtsFiles, transformers);
            };
            if (originalAfterProgramCreate) originalAfterProgramCreate(builderProgram);
        };
        ts.createWatchProgram(compilerHost);
    });
};

export const typescriptObserver = (
    observerInput: TypescriptObserverInput,
    startServices: (dir: string, options: ApplicationStartServicesOptions) => Promise<void>,
): Observer<boolean> => ({
    next: isCompilationSuccessful => {
        const { executionMode, deployedApp, dir, autoReload: autoRebounce } = observerInput;
        if (autoRebounce) {
            reloadApplication(deployedApp, dir, observerInput, startServices, isCompilationSuccessful).catch(err => {
                printWarning(executionMode, 'Failed to reload the application');
                console.log(err);
            });
        }
    },
    error: (err: Error) => printWarning(observerInput.executionMode, `Unexpected error from watch-server: ${err}`),
    complete: () => {},
});
