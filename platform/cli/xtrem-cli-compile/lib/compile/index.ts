import { cliContext, ExecutionMode, printInfo, printSuccess, printWarning, quitWithError } from '@sage/xtrem-cli-lib';
import {
    defaultElasticMq,
    getAllElasticMqConfigs,
    getRoutingData,
    getRoutingFilePath,
    mergeRoutings,
    mergeTranslationFiles,
    QueueConfig,
    requiredQueues,
    Routing,
    updateElasticMqConfigFile,
} from '@sage/xtrem-cli-transformers';
import { generateClientPackage, generateClientPackageJsonFromPackageJson } from '@sage/xtrem-client-gen';
import { verifyDependsOn } from '@sage/xtrem-cop';
import { Application, ApplicationManager, Dict, Package } from '@sage/xtrem-core';
import { resetDictionary } from '@sage/xtrem-i18n';
import { locales } from '@sage/xtrem-shared';
import * as chalk from 'chalk';
import * as fs from 'fs';
import * as _ from 'lodash';
import * as path from 'path';
import { binaryEncoding } from './binary';
import { clean } from './clean';
import { compileServer } from './compile-server';
import { compileUi } from './compile-ui';
import { copyDictionaries } from './copy-dictionaries';
import { generateSingleDefinition } from './dts-generator';
import { instrumentCode } from './instrumenter';

export interface CompileOptions {
    skipClient: boolean;
    skipCop: boolean;
    skipServer: boolean;
    skipClean: boolean;
    skipDts: boolean;
    skipApiClient: boolean;
    force: boolean;
    isInstrumented: boolean;
    isBinary: boolean;
    isProd: boolean;
    isUsingReferences: boolean;
    isOnlyApi: boolean;
    isOnlyChanged: boolean;
    // Skip instantiating of the application, needed when the application is already instantiated on higher level.
    // We need this option when compiling from a service that already is started.
    // This option will not be pass from the CLI, but from directly calls to the compile function.
    skipApplication?: boolean;
    skipClientTypeCheck?: boolean;
}

export * from './utils';
export * from './watch-client';
export * from './watch-server';
export { compileServer };

export async function compile(options: CompileOptions): Promise<void> {
    const { executionMode, dir } = cliContext;
    const {
        skipClient,
        skipCop,
        skipServer,
        skipClean,
        skipDts,
        skipApiClient,
        force,
        isInstrumented,
        isBinary,
        isUsingReferences,
        isOnlyApi,
        isOnlyChanged,
    } = options;

    const dt0 = Date.now();
    if (isOnlyApi) {
        generateClientPackageJsonFromPackageJson(dir);
        process.exit();
    }
    // We don't want test artifacts in the api.d.ts and routing.json files generated by the build
    process.env.XTREM_USE_TEST_APPLICATION = '0';

    if (skipServer && isBinary) {
        quitWithError(
            executionMode,
            'In order to compile to binary format, you have to also compile the server side artifacts.',
        );
    }

    if (force || (!skipClean && !isOnlyChanged)) {
        clean(executionMode, dir, { force });
    } else if (isOnlyChanged) {
        printWarning(executionMode, 'The build folder was not cleaned as the --only-changed flag was applied.');
    } else {
        printInfo(executionMode, 'Skipping build folder cleanup.');
    }

    if (!skipServer) {
        compileServer(executionMode, dir, { isUsingReferences });
    } else if (!skipApiClient) {
        quitWithError(
            executionMode,
            'In order to generate the client API schema, you have to also compile the server side artifacts. Try adding the --skip-api-client flag or removing the --skip-server one',
        );
    } else {
        printInfo(executionMode, 'Skipping server side artifacts.');
    }

    if (!skipServer && !skipDts) {
        generateSingleDefinition(executionMode, dir);
    } else {
        printInfo(
            executionMode,
            `Skipping single declaration file generation. Reason: ${skipServer ? 'skip server' : 'skip DTS'}`,
        );
    }

    if (!options.skipApplication) {
        const application = await ApplicationManager.getApplication(dir, { applicationType: 'dev-tool' });

        checkMainPackageLocales(application, executionMode);

        /** Aggregate the routing tables in the main package but also in intermediate packages that have workflow steps */
        if (application.mainPackage.isMain || application.mainPackage.api.workflowSteps) {
            aggregateNotificationListenerRoutingData(application, executionMode);
            // if the application main package has a lib folder (like glossary) we need to a full compile
            if (!fs.existsSync(path.join(application.mainPackage.dir, 'lib'))) {
                printSuccess(executionMode, `${chalk.bgGreen(' 🌋 ')} It's all done, you are ready to roll!`);
                if (executionMode === ExecutionMode.STANDALONE) {
                    process.exit();
                }
                return;
            }
        }

        if (!skipCop && !path.join(application.mainPackage.dir, '../..').endsWith('/x3')) {
            printInfo(executionMode, 'Verifying code...');
            verifyDependsOn(application);
            const errorCount = await application.verifySqlConversions();
            if (errorCount > 0)
                quitWithError(executionMode, `Found ${errorCount} errors in the getValue/computeValue decorators`);

            printSuccess(executionMode, 'Code was successfully verified.');
        } else {
            printInfo(executionMode, 'Skipping code verification.');
        }

        if (!skipApiClient) {
            printInfo(executionMode, 'Generating API reference TS declaration package...');
            await generateClientPackage(application);
            printSuccess(executionMode, 'API reference TS declaration package generated successfully.');
        } else {
            printInfo(executionMode, 'Skipping API reference TS declaration package generation.');
        }
    }
    if (isInstrumented) {
        instrumentCode(executionMode, dir);
    }

    if (isBinary) {
        await binaryEncoding(executionMode, dir);
    }

    if (!skipClient) {
        if (process.env.SKIP_CLIENT) {
            printWarning(executionMode, 'SKIP_CLIENT environment variable found, skipping client compilation...');
        } else {
            // eslint-disable-next-line import/no-dynamic-require, global-require
            await compileUi(executionMode, dir, options, require(path.resolve(dir, 'package.json')).name);
        }
    } else {
        printInfo(executionMode, 'Skipping client side artifacts.');
    }

    // Reset translations dictionary (see message-transformer)
    printInfo(executionMode, 'Preparing string literal files...');
    mergeTranslationFiles(dir);
    resetDictionary();
    printSuccess(executionMode, 'String literal files prepared.');
    copyDictionaries(executionMode, dir);
    printSuccess(
        executionMode,
        `${chalk.bgGreen(' 🌋 ')} It's all done, you are ready to roll! (${Date.now() - dt0} ms)`,
        false,
    );

    if (executionMode === ExecutionMode.STANDALONE) {
        process.exit();
    }
}

/**
 * Create the 'routing.json' file for the main package from the aggregation of all the rouging.json files of all the packages.
 * The 'elastiqmq.conf' file will also be created for the main package.
 */
function aggregateNotificationListenerRoutingData(application: Application, executionMode: ExecutionMode) {
    let allRoutingData: Routing = {};
    let allTestRoutingData: Routing = {};
    application.getPackages().forEach(pack => {
        // Skip the main package, except for xtrem-show-case which has both isMain and hasListeners flags.
        if (pack.isMain && !pack.hasListeners) return;
        const packageRoutingData = getRoutingDataForPackage(pack, executionMode);
        allRoutingData = mergeRoutings(allRoutingData, packageRoutingData);
        allTestRoutingData = mergeRoutings(allTestRoutingData, packageRoutingData);

        const testRoutingPath = path.join(pack.dir, 'test/fixtures/routing.json');
        if (fs.existsSync(testRoutingPath)) {
            const testRouting = JSON.parse(fs.readFileSync(testRoutingPath, 'utf-8'));
            allTestRoutingData = mergeRoutings(allTestRoutingData, testRouting);
        }
    });
    const workflowTopics = application.workflowManager.getWorkflowTopics();
    const workflowRoutings = workflowTopics.map(topic => ({
        topic,
        queue: 'workflow',
        sourceFileName: '<workflow step>',
    }));
    allRoutingData = mergeRoutings(allRoutingData, { '@sage/xtrem-workflow': workflowRoutings });
    allTestRoutingData = mergeRoutings(allTestRoutingData, { '@sage/xtrem-workflow': workflowRoutings });

    const routingFile = getRoutingFilePath(application.mainPackage.dir);
    printInfo(executionMode, `Writing file ${routingFile}`);
    fs.writeFileSync(routingFile, JSON.stringify(allRoutingData, null, 4), 'utf-8');

    const elasticConf: { queues: Dict<QueueConfig> } = defaultElasticMq;

    // Add the standard queues first, these will be used when the application is started (non-test applications)
    const standardConf = getAllElasticMqConfigs(application.dir, allTestRoutingData, undefined, requiredQueues);
    elasticConf.queues = _.merge(elasticConf.queues, standardConf);

    // Add the unit test queues , these will be used for unit tests
    application.getPackages().forEach(pack => {
        const packName = _.kebabCase(pack.name.split('/').pop());
        const buildConf = getAllElasticMqConfigs(application.dir, allTestRoutingData, packName);
        elasticConf.queues = _.merge(elasticConf.queues, buildConf);
    });

    updateElasticMqConfigFile(application.workspaceId, application.workspaceDir, elasticConf.queues);
}

function getRoutingDataForPackage(pack: Package, executionMode: ExecutionMode): Routing {
    const routingFile = getRoutingFilePath(pack.dir);
    if (fs.existsSync(routingFile)) {
        printInfo(executionMode, `Routing file found in package ${pack.name}: ${routingFile}`);
        return getRoutingData(routingFile);
    }
    return {};
}

/* Check locales in package.json of the main package is well formed */
function checkMainPackageLocales(application: Application, executionMode: ExecutionMode): void {
    if (application.mainPackage.isMain && application.mainPackage.packageJson?.xtrem?.locales) {
        const unknownLocales = application.mainPackage.packageJson.xtrem.locales.filter(
            locale => locale === '' || locales.find(availableLocale => locale === availableLocale) === undefined,
        );
        if (unknownLocales.length > 0)
            quitWithError(executionMode, `Found ${JSON.stringify(unknownLocales)}: unknown locales in package.json`);
    }
}
