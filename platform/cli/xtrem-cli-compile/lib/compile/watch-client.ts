import {
    addTsLoaderErrorToDispatchQueue,
    broadcastCompilerSuccessMessage,
    ExecutionMode,
    plural,
    printInfo,
    printSuccess,
    printWarning,
    TypescriptObserverInput,
} from '@sage/xtrem-cli-lib';
import { ArtifactManager } from '@sage/xtrem-core';
import { glob } from 'glob';
import { Observer } from 'rxjs';
import { compilePagesAndStickers, isCompilable, WebpackResult } from './utils';

export const watchClientArtifacts = (executionMode: ExecutionMode, buildDir: string) => {
    const globOptions = { cwd: buildDir, absolute: true, realpath: true };
    const pages = glob.sync('./lib/pages/*.ts', globOptions).filter(isCompilable);
    const stickers = glob.sync('./lib/stickers/*.ts', globOptions).filter(isCompilable);

    if (pages.length <= 0 && stickers.length <= 0) {
        printInfo(executionMode, 'No client side artifacts found.');
        return undefined;
    }

    return compilePagesAndStickers({
        executionMode,
        buildDir,
        isProd: false,
        isInstrumented: false,
        isWatch: true,
        isOnlyChanged: false,
        packageName: buildDir.split('/').pop() ?? '',
    });
};

export const printCompilerErrorMessage = (
    executionMode: ExecutionMode,
    error: {
        message: string;
        chunkName?: string;
        chunkEntry?: boolean;
        chunkInitial?: boolean;
        file?: string;
        moduleIdentifier?: string;
        moduleName?: string;
        loc?: string;
        chunkId?: string | number;
        moduleId?: string | number;
        moduleTrace?: any;
        details?: any;
        stack?: any;
    },
) => {
    console.log(error);
    try {
        const lines = error.stack?.split('\n');
        const errorObject = JSON.parse(lines[lines.length - 1]);
        printWarning(
            executionMode,
            `${errorObject.file}:${errorObject.line}:${errorObject.character}\n${errorObject.content}`,
        );
    } catch (e) {
        printWarning(executionMode, error.message);
    }
};

export const webpackObserver = ({
    executionMode,
    deployedApp,
    dir,
}: TypescriptObserverInput): Observer<WebpackResult> => ({
    next: result => {
        const statsObj = result.stats && result.stats.toJson();
        const hasWarnings = statsObj && statsObj.warnings && statsObj.warnings.length > 0;
        const hasErrors = statsObj && statsObj.errors && statsObj.errors.length > 0;
        const isCompilationSuccessful = !result.error && !hasWarnings && !hasErrors;
        if (isCompilationSuccessful) {
            if (deployedApp.app) {
                ArtifactManager.clearStored();
                printSuccess(
                    executionMode,
                    'Webpack compiler finished successfully. Please refresh your webpage to see the changes.',
                );
                broadcastCompilerSuccessMessage('client');
                return;
            }

            printSuccess(executionMode, 'Webpack compiler finished successfully.');
            broadcastCompilerSuccessMessage('client');
        } else {
            if (!statsObj) {
                return;
            }
            if (hasWarnings)
                statsObj.warnings?.forEach(w => {
                    addTsLoaderErrorToDispatchQueue(w.message, dir);
                    printCompilerErrorMessage(executionMode, w);
                });
            if (hasErrors)
                statsObj.errors?.forEach(e => {
                    addTsLoaderErrorToDispatchQueue(e.message, dir);
                    printCompilerErrorMessage(executionMode, e);
                });

            printWarning(
                executionMode,
                `${plural(
                    'error was',
                    'errors were',
                    (statsObj.errors ?? []).length,
                )} found during Webpack compilation.\nHot-reloading is enabled though, you can issue another build just by re-saving any page/sticker file.\nNote that the Xtrem server will be restarted automatically upon every successful build.`,
            );
        }
    },
    error: err => printWarning(executionMode, `Unexpected error from watch-client: ${err}`),
    complete: () => { },
});
