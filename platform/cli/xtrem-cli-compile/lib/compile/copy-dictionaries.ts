import { ExecutionMode, printInfo, printSuccess } from '@sage/xtrem-cli-lib';
import * as fs from 'fs';
import { glob } from 'glob';
import * as path from 'path';

export const copyDictionaries = (executionMode: ExecutionMode, dir: string) => {
    printInfo(executionMode, 'Copying string literal files...');
    const i18nSourceDir = path.resolve(dir, 'lib', 'i18n');
    const i18nTargetDir = path.resolve(dir, 'build', 'lib', 'i18n');

    if (fs.existsSync(i18nSourceDir)) {
        if (!fs.existsSync(i18nTargetDir)) {
            fs.mkdirSync(i18nTargetDir);
        }

        glob.sync('./*.json', { cwd: i18nSourceDir, absolute: true, realpath: true }).forEach(f => {
            const basename = path.basename(f);
            const fileTarget = path.resolve(i18nTargetDir, basename);
            fs.copyFileSync(f, fileTarget);
        });
    }
    printSuccess(executionMode, 'String literal files copied.');
};
