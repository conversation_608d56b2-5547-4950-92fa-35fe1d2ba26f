import { ExecutionMode, PackagingMode, printInfo, printSuccess, quitWithError } from '@sage/xtrem-cli-lib';
import { decimalTransformer, messageTransformer, pageMetadataTransformer } from '@sage/xtrem-cli-transformers';
import * as path from 'path';
import { Observable, Observer } from 'rxjs';
import * as ts from 'typescript';
import * as webpack from 'webpack';
import { getTsProgram, printCompilerError } from './server-compilation';
import { XtremClientPlugin } from './xtrem-client-plugin';

const TerserPlugin = require('terser-webpack-plugin');

export const compileClientAndSharedFunctions = (executionMode: ExecutionMode, dir: string) => {
    // transpile client and shared functions
    printInfo(executionMode, 'Compiling client and shared functions...');
    const clientAndSharedProgram = getTsProgram(dir, {
        configFileName: 'tsconfig-artifacts.json',
        includeOverride: ['lib/client-functions', 'lib/shared-functions'],
        tsconfigOverride: {
            declaration: false,
            composite: false,
            declarationMap: false,
        },
    });

    const clientAndSharedDiagnosticsEmitResult = clientAndSharedProgram.emit(
        undefined,
        undefined,
        undefined,
        undefined,
        {
            before: [decimalTransformer, messageTransformer, pageMetadataTransformer],
        },
    );

    const clientAndSharedDiagnostics = ts
        .getPreEmitDiagnostics(clientAndSharedProgram)
        .concat(clientAndSharedDiagnosticsEmitResult.diagnostics);

    clientAndSharedDiagnostics.forEach(printCompilerError(executionMode));

    if (clientAndSharedDiagnostics.length === 0) {
        printSuccess(executionMode, 'Shared and client functions compiled.');
    } else {
        quitWithError(executionMode, 'Failed to compile shared and client functions.');
    }
};

export const checkTypesOnClientArtifactsAndBuildDeclarations = (executionMode: ExecutionMode, dir: string) => {
    // transpile client and shared functions
    printInfo(executionMode, 'Type checking client...');
    const diagnosticsProgram = getTsProgram(dir, {
        configFileName: 'tsconfig-artifacts.json',
        excludeOverride: ['build/*/*.d.ts'],
        tsconfigOverride: {
            strictNullChecks: false,
            skipLibCheck: true,
            emitDeclarationOnly: true,
        },
    });

    const diagnosticsResult = diagnosticsProgram.emit();
    const diagnostics = ts.getPreEmitDiagnostics(diagnosticsProgram).concat(diagnosticsResult.diagnostics);

    diagnostics.forEach(printCompilerError(executionMode));

    if (diagnostics.length === 0) {
        printSuccess(executionMode, 'Type checks passed.');
    } else {
        quitWithError(executionMode, 'Failed to compile client.');
    }
};

export const filterExternals = ({ request }: any): Promise<string | void> => {
    /**
     * BL: The `@sage/xtrem-ui` is part of the front-end application which loads the pages which we compile here.
     * Therefore this dependency can be injected instead of packaging it up, so here we just return an empty module
     * instead of actually packaging it up.
     */
    if (/@sage\/xtrem-ui/.test(request)) {
        return Promise.resolve(`commonjs ${request}`);
    }

    if (/@sage\/xtrem-async-helper/.test(request)) {
        return Promise.resolve(`commonjs ${request}`);
    }

    if (/@sage\/xtrem-i18n/.test(request)) {
        return Promise.resolve(`commonjs ${request}`);
    }

    if (/@sage\/xtrem-date-time/.test(request)) {
        return Promise.resolve(`commonjs ${request}`);
    }

    if (/@sage\/xtrem-decimal/.test(request)) {
        return Promise.resolve(`commonjs ${request}`);
    }

    if (/@sage\/xtrem-shared/.test(request)) {
        return Promise.resolve(`commonjs ${request}`);
    }

    if (/@sage\/xtrem-client/.test(request)) {
        return Promise.resolve(`commonjs ${request}`);
    }

    if (/@sage\/xtrem-core/.test(request)) {
        return Promise.resolve(`commonjs ${request}`);
    }

    if (request === 'lodash') {
        return Promise.resolve(`commonjs ${request}`);
    }

    if (request === 'react') {
        return Promise.resolve(`commonjs ${request}`);
    }

    return Promise.resolve();
};

export const isCompilable = (filePath: string) => {
    const basename = path.basename(filePath);
    return basename !== 'index.ts' && basename !== '_index.ts' && basename.endsWith('.ts');
};

export const getWebpackEntry = (
    executionMode: ExecutionMode,
    dir: string,
    isOnlyChanged: boolean,
): (() => webpack.Entry) => {
    return XtremClientPlugin.getEntries(
        executionMode,
        dir,
        [
            './lib/pages/*.ts',
            './lib/stickers/*.ts',
            './lib/page-extensions/*.ts',
            './lib/page-fragments/*.ts',
            './lib/widgets/*.ts',
        ],
        {
            ignore: '**/index.ts',
            cwd: dir,
            absolute: true,
            realpath: true,
        },
        isOnlyChanged,
    );
};

interface CompileInput {
    executionMode: ExecutionMode;
    buildDir: string;
    isProd: boolean;
    isInstrumented: boolean;
    isFast?: boolean;
    isWatch?: boolean;
    isOnlyChanged?: boolean;
    packageName: string;
}

export const compilePagesAndStickers = ({
    executionMode,
    buildDir,
    isProd,
    isInstrumented,
    isWatch = false,
    isOnlyChanged = false,
    packageName,
}: CompileInput): Observable<WebpackResult> => {
    const entry = getWebpackEntry(executionMode, buildDir, isOnlyChanged);

    const targetDir = path.resolve(buildDir, 'build/lib').toLowerCase();
    const output: webpack.Configuration['output'] = {
        path: targetDir,
        filename: '[name].js',
        // https://webpack.js.org/configuration/output/#librarytarget-assign-properties
        library: {
            name: 'xtremArtifact',
            type: 'assign-properties',
        },
        devtoolNamespace: packageName,
        devtoolModuleFilenameTemplate: 'webpack://[namespace]/[resource-path]',
    };
    const packagingMode = isProd ? PackagingMode.PROD : PackagingMode.DEV;
    return compileFiles({
        buildDir,
        entry,
        output,
        packagingMode,
        isInstrumented,
        options: isWatch ? { watch: true } : {},
    });
};

interface WebpackInput {
    buildDir: string;
    entry: () => webpack.Entry;
    output: webpack.Configuration['output'];
    packagingMode?: PackagingMode;
    isInstrumented: boolean;
    options?: webpack.Configuration;
}
export const compileFiles = ({
    buildDir,
    entry,
    output,
    packagingMode = PackagingMode.DEV,
    isInstrumented,
    options = {},
}: WebpackInput): Observable<WebpackResult> => {
    const devMode = packagingMode === PackagingMode.DEV;
    const isWatchEnabled = (options && options.watch) || false;
    let devtool: string | false = 'source-map';
    if (!isInstrumented) {
        devtool = devMode ? 'eval-source-map' : false;
    }

    // Define the path to the various loaders
    const loaders = {
        tsLoader: require.resolve('ts-loader'),
        threadLoader: require.resolve('thread-loader'),
    };

    const config: webpack.Configuration = {
        entry: entry as webpack.Entry,
        mode: devMode ? 'development' : 'production',
        devtool,
        performance: {
            hints: false,
            maxEntrypointSize: 512000,
            maxAssetSize: 512000,
        },
        optimization:
            packagingMode === PackagingMode.PROD
                ? {
                    minimize: true,
                    minimizer: [
                        new TerserPlugin({
                            terserOptions: {
                                keep_classnames: true,
                                keep_fnames: true,
                            },
                        }),
                    ],
                }
                : undefined,
        ...(isWatchEnabled
            ? {
                watchOptions: {
                    aggregateTimeout: 600,
                },
                devServer: {
                    contentBase: path.resolve(buildDir, 'build/lib').toLowerCase(),
                    compress: true,
                    publicPath: '/',
                    port: 4000,
                    host: '0.0.0.0',
                    historyApiFallback: {
                        index: '/',
                    },
                    proxy: [
                        {
                            target: 'http://localhost:8240',
                        },
                    ],
                },
            }
            : {}),
        output,
        plugins: [
            new XtremClientPlugin(buildDir),
            new webpack.ProvidePlugin({
                Buffer: ['buffer', 'Buffer'],
            }),
        ],
        externals: [filterExternals],
        module: {
            rules: [
                ...(!isWatchEnabled
                    ? [
                        {
                            loader: loaders.threadLoader,
                            options: {
                                workerParallelJobs: 50,
                                workerNodeArgs: ['--max-old-space-size=1024'],
                                poolRespawn: false,
                                poolTimeout: 2000,
                                poolParallelJobs: 50,
                            },
                        },
                    ]
                    : []),
                {
                    test: /\.ts$/,
                    exclude: /node_modules/,
                    loader: loaders.tsLoader,
                    options: {
                        happyPackMode: !isWatchEnabled,
                        colors: false,
                        configFile: path.resolve(buildDir, 'tsconfig-artifacts.json'),
                        errorFormatter: (errorInfo: any) => {
                            return JSON.stringify(errorInfo);
                        },
                        getCustomTransformers: require.resolve('./transformers'),
                    },
                },
            ],
        },
        resolve: {
            fallback: {
                // used in xtrem-reporting
                buffer: require.resolve('buffer/'),
            },
            extensions: ['.ts', '.tsx', '.js', '.json'],
        },
        ...options,
    };

    // TODO: remove any but compile fails on xtrem-avalara-gateway
    return runWebpack({ compiler: (webpack as any)(config), isWatchEnabled });
};

interface RunWebpackOptions {
    compiler: webpack.Compiler;
    isWatchEnabled: boolean;
}

export type WebpackResult = { stats: webpack.Stats | undefined | null; error: Error | undefined | null };
const runWebpack = ({ compiler, isWatchEnabled: watchMode = false }: RunWebpackOptions) => {
    if (watchMode) {
        return new Observable((obs: Observer<WebpackResult>) => {
            compiler.watch({ ignored: /node_modules/, poll: true }, (error, stats) => {
                obs.next({ stats, error });
            });
        });
    }
    return new Observable((obs: Observer<WebpackResult>) => {
        compiler.run((error, stats) => {
            obs.next({ stats, error });
        });
    });
};
