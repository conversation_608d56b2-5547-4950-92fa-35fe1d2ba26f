import { ArtifactTypes, broadcastMessage, EventType, ExecutionMode, printInfo } from '@sage/xtrem-cli-lib';
import {
    getMetadataFilePathForClientFile,
    hashFile,
    isClientArtifactFile,
    messageTransformVisitor,
} from '@sage/xtrem-cli-transformers';
import { Dict } from '@sage/xtrem-core';
import * as fs from 'fs';
import * as glob from 'glob';
import { get, kebabCase, set, uniq } from 'lodash';
import * as path from 'path';
import * as ts from 'typescript';
import * as webpack from 'webpack';

const globParent = require('glob-parent');

// eslint-disable-next-line @sage/redos/no-vulnerable
const propertyNameRegex = /\.([A-Za-z]*)(<[\s\S]*>)?$/;
const artifactFolders: ArtifactTypes[] = [
    ArtifactTypes.Pages,
    ArtifactTypes.Stickers,
    ArtifactTypes.PageExtensions,
    ArtifactTypes.PageFragments,
];
const directories: string[] = [];

// Some parts of it is based on https://github.com/Milanzor/webpack-watched-glob-entries-plugin

/** @internal */

export class XtremClientPlugin implements webpack.WebpackPluginInstance {
    constructor(
        private readonly appDir: string,
        private readonly executionMode: ExecutionMode = ExecutionMode.STANDALONE,
    ) {}

    static getEntries(
        executionMode: ExecutionMode,
        dir: string,
        globs: string[],
        globOptions: glob.GlobOptions,
        isOnlyChanged: boolean,
    ): () => webpack.Entry {
        return () => {
            // Check if globs are provided properly
            if (typeof globs !== 'string' && !Array.isArray(globs)) {
                throw new TypeError('globOptions must be a string or an array of strings');
            }

            // Check globOptions if provided properly
            if (globOptions && typeof globOptions !== 'object') {
                throw new TypeError('globOptions must be an object');
            }

            // Make entries an array
            if (!Array.isArray(globs)) {
                // eslint-disable-next-line no-param-reassign
                globs = [globs];
            }

            //
            let globbedFiles = {};

            // Map through the globs
            globs.forEach(globString => {
                const globBase = globParent(globString);
                // Dont add if its already in the directories
                if (directories.indexOf(globBase) === -1) {
                    directories.push(globBase);
                }

                // Get the globbedFiles
                const files = XtremClientPlugin.getFiles(dir, globString, globOptions, isOnlyChanged);

                // Set the globbed files
                globbedFiles = { ...files, ...globbedFiles };
            });

            if (isOnlyChanged) {
                printInfo(executionMode, `Building the following files:\n${Object.keys(globbedFiles).join('\n')}`);
            }

            return globbedFiles;
        };
    }

    /**
     * Create webpack file entry object
     * @param globString
     * @param globOptions
     * @param basename_as_entry_name
     * @returns {Object}
     */
    static getFiles(dir: string, globString: string, globOptions: glob.GlobOptions, isOnlyChanged: boolean) {
        const files: Dict<string> = {};
        const globBase = globParent(globString);

        glob.sync(globString, globOptions)
            .filter(file => {
                if (!isOnlyChanged) {
                    return true;
                }

                const metadataFilePath = getMetadataFilePathForClientFile(dir, file.toString());
                if (!fs.existsSync(metadataFilePath)) {
                    return true;
                }

                const currentHash = hashFile(file.toString());
                const metadataContent = JSON.parse(fs.readFileSync(metadataFilePath, 'utf-8'));
                return currentHash !== metadataContent.md5;
            })
            .forEach(file => {
                const filePath = path.relative(globBase, file.toString());
                const entryName = path.basename(
                    filePath.replace(path.extname(file.toString()), '').split(path.sep).join('/'),
                );

                const parentDir = path.basename(globBase);

                // Add the entry to the files obj
                files[`${parentDir}/${entryName}`] = file.toString();
            });

        return files;
    }

    apply(compiler: webpack.Compiler) {
        compiler.hooks.afterCompile.tapAsync(this.constructor.name, this.afterCompile.bind(this));
        compiler.hooks.watchRun.tapAsync(`${this.constructor.name}watchRun`, this.afterChangesDetected.bind(this));

        // Add custom resolver
        // To review: It seems to creates a dual package hazard.
        // compiler.hooks.normalModuleFactory.tap(this.constructor.name, nmf => {
        //     nmf.hooks.beforeResolve.tapAsync(this.constructor.name, this.clientResolver.bind(this));
        // });
    }

    afterChangesDetected(_compiler: webpack.Compiler, callback: any) {
        printInfo(this.executionMode, 'Client changes detected!');
        broadcastMessage(EventType.COMPILER_STARTED, 'client');
        callback();
    }

    mapClientUtilityDependencyChain(
        compilation: webpack.Compilation,
        module: webpack.NormalModule,
        knownDependencies: string[],
        checkedDependencies: string[] = [],
    ) {
        module.dependencies.forEach((d: webpack.Dependency) => {
            const dependencyModule = compilation.moduleGraph.getModule(d) as webpack.NormalModule;
            if (!dependencyModule?.resource || knownDependencies.includes(dependencyModule.resource)) {
                // Skip checking modules that were already checked or has no resource path
                return;
            }

            // CommonJsRequireDependency is not exported from webpack so we can't use an instanceof check
            if (
                d.constructor.name === 'CommonJsRequireDependency' &&
                !knownDependencies.includes(dependencyModule.resource) &&
                // Only check client functions and shared functions (but not the ones from other xtrem modules too)
                (dependencyModule.resource.indexOf('lib/client-functions') !== -1 ||
                    dependencyModule.resource.indexOf('lib/shared-functions') !== -1)
            ) {
                knownDependencies.push(dependencyModule.resource);
            }

            if (!checkedDependencies.includes(dependencyModule.resource)) {
                checkedDependencies.push(dependencyModule.resource);
                // Recursively check the dependencies of the dependency
                this.mapClientUtilityDependencyChain(
                    compilation,
                    dependencyModule as webpack.NormalModule,
                    knownDependencies,
                    checkedDependencies,
                );
            }
        });
    }

    extractUsedTranslationKeysToMetaFile(compilation: webpack.Compilation) {
        Array.from(compilation.modules.values()).forEach((module: webpack.NormalModule) => {
            if (
                !module?.resource ||
                !module.resource.startsWith(this.appDir) ||
                !isClientArtifactFile(module.resource)
            ) {
                // Skip any root modules that are not part of the current package or not client artifacts
                return;
            }

            const utilityDependencyModules: string[] = [];
            // Recursively collect all dependency modules that are client utility functions
            this.mapClientUtilityDependencyChain(compilation, module, utilityDependencyModules);
            const dictionary: Dict<string> = {};

            // Loop through the dependencies and extract the dictionary keys using the standard message transformer visitor
            utilityDependencyModules.forEach(d => {
                const content = fs.readFileSync(d, 'utf-8');
                const root = ts.createSourceFile(path.basename(d), content, ts.ScriptTarget.ES2022);
                // We don't actually transform the files, but the visitor needs a transformation context in order to work, this is a read-only operation
                const transformer =
                    (ctx: ts.TransformationContext) =>
                    (file: ts.SourceFile): ts.Node =>
                        ts.visitNode(file, messageTransformVisitor(dictionary, '', file.fileName, ctx, true));
                ts.transform(root, [transformer]);
            });

            const dictionaryKeys = Object.keys(dictionary);
            if (dictionaryKeys.length === 0) {
                return;
            }
            // If any keys found, add them to the metadata file
            const metaFilePath = getMetadataFilePathForClientFile(this.appDir, module.resource);
            const metaFileContent = JSON.parse(fs.readFileSync(metaFilePath, 'utf-8'));
            const existingKeys = get(metaFileContent, 'literals.strings', []);
            set(metaFileContent, 'literals.strings', uniq([...existingKeys, ...dictionaryKeys]));
            fs.writeFileSync(metaFilePath, JSON.stringify(metaFileContent, null, 4));
        });
    }

    afterCompile(compilation: webpack.Compilation, callback: any) {
        try {
            this.extractUsedTranslationKeysToMetaFile(compilation);
            directories.forEach(directory => compilation.contextDependencies.add(directory));
            compilation.fileDependencies.forEach((filePath: string) => {
                if (!filePath.includes('package.json')) {
                    const errors = this.checkUiArtifacts(filePath);
                    errors.forEach(error => {
                        compilation.errors.push(new webpack.WebpackError(error));
                    });
                }
            }, this);
        } catch (e) {
            compilation.errors.push(e);
        } finally {
            callback();
        }
    }

    // // eslint-disable-next-line class-methods-use-this
    // clientResolver(resolveData: any, callback: any) {
    //     const { context, request } = resolveData;
    //     // To investigate: Because of the ESM migration, we need to provide our own resolver for the client functions.
    //     if (request.startsWith('.') && request.includes('/client-functions/')) {
    //         const file = path.resolve(context, request);
    //         if (!file.includes('/build/')) {
    //             resolveData.request = file.replace('/lib/', '/build/lib/');
    //         }
    //     }
    //     callback();
    // }

    private getArtifactsFolderName(filePath: string): ArtifactTypes | undefined {
        let artifactsFolderName;
        const normalizedFile = fs.realpathSync(filePath);
        const artifactsFolder = artifactFolders
            .filter(folderName => fs.existsSync(path.resolve(this.appDir, `lib/${folderName}`)))
            .map(folderName => {
                const folderPath = fs.realpathSync(path.resolve(this.appDir, `lib/${folderName}`));
                return {
                    folderName,
                    folderPath,
                };
            })
            .find(folderData => normalizedFile.startsWith(folderData.folderPath));

        if (artifactsFolder) {
            artifactsFolderName = artifactsFolder.folderName;
        }

        return artifactsFolderName;
    }

    private checkUiArtifacts(filePath: string): string[] {
        const artifactsFolderName = this.getArtifactsFolderName(filePath);
        if (artifactsFolderName && path.basename(filePath) !== 'index.ts' && fs.lstatSync(filePath).isFile()) {
            const content = fs.readFileSync(filePath, 'utf-8');
            const root = ts.createSourceFile(path.basename(filePath), content, ts.ScriptTarget.ES2022);

            const classDeclaration = root.statements.filter(ts.isClassDeclaration).find((cd: ts.ClassDeclaration) => {
                return Boolean(cd && cd.heritageClauses && cd.heritageClauses.length > 0);
            });

            if (classDeclaration) {
                return XtremClientPlugin.checkDecoratorsAndTypes(filePath, root, classDeclaration);
            }
        }
        return [];
    }

    /**
     * Validate the various aspects of the client side artifacts.
     *
     * It looks for various developer issues and rules that cannot be checked or enforced by TypeScript typings such as
     * decorator assignment to the right properties.
     *
     * @param fileName
     * @param root
     * @param classDeclaration
     */
    private static checkDecoratorsAndTypes(
        fileName: string,
        root: ts.SourceFile,
        classDeclaration: ts.ClassDeclaration,
    ): string[] {
        const errors: string[] = [];
        /**
         * Check file name. Due to limitations of the dynamic artifact loader, the artifact class within the file must
         * be called the same as the file, but using camel case conventions.
         */
        const className = classDeclaration.name;
        if (
            className &&
            classDeclaration.name &&
            path.basename(fileName).replace('.ts', '') !== kebabCase(classDeclaration.name.getText(root))
        ) {
            errors.push(
                `Xtrem Page error: in '${fileName}' the class name must be the same as the filename with camel case naming conventions. Class name found: ${className.getText(
                    root,
                )}.`,
            );
        }

        const classDecorators = ts.getDecorators(classDeclaration) || [];
        const isPageFragment = classDecorators.some(decorator => {
            if (ts.isCallExpression(decorator.expression)) {
                const decoratorType = decorator.expression.expression.getText(root);
                return decoratorType.includes('pageFragment');
            }
            return false;
        });

        classDeclaration.members.filter(ts.isPropertyDeclaration).forEach((member: ts.PropertyDeclaration) => {
            const decorator = ts.getDecorators(member)?.[0];
            if (decorator && ts.isCallExpression(decorator.expression)) {
                const propertyAccessExpression = decorator.expression;
                const decoratorType = propertyAccessExpression.expression.getText(root);
                const line = member.type ? `:${root.getLineAndCharacterOfPosition(member.type.pos).line + 1}` : '';
                const memberName = member.name.getText(root);

                /**
                 * Check whether the corresponding decorator function is used for the right class member. For example,
                 * a `ui.fields.Text` field is decorated with the `ui.decorators.textField` decorator.
                 *  */
                const formattedDecoratorType = (decoratorType
                    .replace('Field', '')
                    .replace('extension', '')
                    .replace('Extension', '')
                    .replace('Override', '')
                    .replace('override', '')
                    .match(propertyNameRegex) ?? ['', ''])[1].toLowerCase();
                const propertyType = member.type ? member.type.getText(root) : '';
                const formattedPropertyType = (propertyType.replace('Field', '').match(propertyNameRegex) ?? [
                    '',
                    '',
                ])[1].toLowerCase();
                if (
                    formattedDecoratorType &&
                    formattedPropertyType &&
                    formattedDecoratorType !== formattedPropertyType
                ) {
                    errors.push(
                        `Xtrem Page error: in '${fileName}${line}' the "${memberName}" property is misconfigured because the decorator type "${decoratorType}" doesn't match the "${propertyType}" property type.`,
                    );
                }

                if (isPageFragment && decoratorType && !decoratorType.toLowerCase().includes('field')) {
                    errors.push(
                        `Xtrem Page error: in '${fileName}${line}' the "${memberName}" property is decorated but is not a field in a page fragment.`,
                    );
                }
            }
        });
        return errors;
    }
}
