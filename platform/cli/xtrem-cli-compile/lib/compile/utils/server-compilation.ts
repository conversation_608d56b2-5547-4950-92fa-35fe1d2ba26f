import { addTsDiagnosticToDispatchQueue, ExecutionMode, printWarning } from '@sage/xtrem-cli-lib';
import * as fs from 'fs';
import * as path from 'path';
import * as ts from 'typescript';

function getAccessibleFileSystemEntries(p: string): { files: string[]; directories: string[] } {
    try {
        const entries = fs.readdirSync(p || '.').sort();
        const files: string[] = [];
        const directories: string[] = [];
        entries.forEach(entry => {
            // This is necessary because on some file system node fails to exclude
            // "." and "..". See https://github.com/nodejs/node/issues/4002
            if (entry === '.' || entry === '..' || path.extname(entry) === '.png') {
                return;
            }
            const name = (ts as any).combinePaths(p, entry);

            let stat: any;
            try {
                stat = fs.statSync(name);
            } catch (e) {
                return;
            }

            if (stat.isFile()) {
                files.push(entry);
            } else if (stat.isDirectory()) {
                directories.push(entry);
            }
        });
        return { files, directories };
    } catch (e) {
        return { files: [], directories: [] };
    }
}

export const getTsProgram = (
    dir: string,
    options?: {
        configFileName?: string;
        excludeOverride?: string[];
        includeOverride?: string[];
        tsconfigOverride?: Partial<ts.CompilerOptions>;
    },
) => {
    const files = getFiles(dir, options);
    return ts.createProgram(
        files,
        getCompilerOptions(dir, options?.configFileName || 'tsconfig.json', options?.tsconfigOverride),
    );
};

export const getFiles = (
    dir: string,
    options?: { configFileName?: string; excludeOverride?: string[]; includeOverride?: string[] },
) => {
    const tsconfigPath = path.resolve(dir, options?.configFileName || 'tsconfig.json');
    const {
        include: includes,
        exclude: excludes,
        extends: extensions,
    } = JSON.parse(fs.readFileSync(tsconfigPath, 'utf8'));
    /*
    See https://github.com/microsoft/TypeScript/blob/23f1d5ccb7202a1eac0a853abfdaf060b145a4b6/src/compiler/utilities.ts#L8085
    The "matchFiles" function has the following signature:
    (path: string, extensions: ReadonlyArray<string> | undefined, excludes: ReadonlyArray<string> | undefined, includes: ReadonlyArray<string> | undefined, useCaseSensitiveFileNames: boolean, currentDirectory: string, depth: number | undefined, getFileSystemEntries: (path: string) => FileSystemEntries, realpath: (path: string) => string): string[]
    and is useful in order to get the list of files to be compiled from the "include" and "exclude" properties.
    We need a complete list of files in order to support the "composite" feature and compile project references.
    */

    return (ts as any).matchFiles(
        dir,
        extensions,
        options?.excludeOverride || excludes,
        options?.includeOverride || includes.filter((i: string) => !i.endsWith('.json')),
        true,
        process.cwd(),
        undefined,
        getAccessibleFileSystemEntries,
        ts.sys.realpath,
    );
};

const getCompilerOptionsFromTsConfig = (tsconfigPath: string, basePath?: string) => {
    const configFile = ts.readConfigFile(tsconfigPath, ts.sys.readFile);
    const tsConfig = ts.parseJsonConfigFileContent(configFile.config, ts.sys, basePath || path.dirname(tsconfigPath));
    tsConfig.options.configFilePath = tsconfigPath;
    return tsConfig;
};

export const getCompilerOptions = (
    dir: string,
    configFileNme = 'tsconfig.json',
    tsconfigOverride?: Partial<ts.CompilerOptions>,
): ts.CompilerOptions => {
    const tsconfigPath = path.resolve(dir, configFileNme);
    const buildDir = path.resolve(dir, 'build');
    let compilerOptions: ts.CompilerOptions = {};

    try {
        // The following function not only reads the actual "tsconfig.json" files
        // but also takes care of possible extensions.
        compilerOptions = getCompilerOptionsFromTsConfig(tsconfigPath).options;
        // eslint-disable-next-line no-empty
    } catch (err) {}

    // Target ESM module only if server side compilation (tsconfig.json) and package type is 'module'
    const packageJson = JSON.parse(fs.readFileSync(path.resolve(dir, 'package.json'), 'utf8'));
    const useEsm = configFileNme === 'tsconfig.json' && packageJson.type === 'module';

    const mandatoryOptions: ts.CompilerOptions = {
        module: useEsm ? ts.ModuleKind.NodeNext : ts.ModuleKind.CommonJS,
        moduleResolution: useEsm ? ts.ModuleResolutionKind.NodeNext : ts.ModuleResolutionKind.NodeJs,
        target: useEsm ? ts.ScriptTarget.ESNext : ts.ScriptTarget.ES2022,
        outDir: buildDir,
        rootDir: path.resolve(dir),
        baseUrl: path.resolve(dir),
        experimentalDecorators: true,
        declaration: true,
        declarationMap: true,
        composite: true,
        sourceMap: true,
        stripInternal: true,
        watch: false,
        noUnusedLocals: false,
        noImplicitAny: true,
        noImplicitThis: true,
        strictNullChecks: true,
        useDefineForClassFields: false,
        skipLibCheck: true,
        ...tsconfigOverride,
    };

    return {
        ...compilerOptions,
        ...mandatoryOptions,
    };
};

export const printCompilerError = (executionMode: ExecutionMode) => (diagnostic: ts.Diagnostic) => {
    if (diagnostic.file && diagnostic.start) {
        addTsDiagnosticToDispatchQueue(diagnostic, 'server');
        const { line, character } = diagnostic.file.getLineAndCharacterOfPosition(diagnostic.start);
        const message = ts.flattenDiagnosticMessageText(diagnostic.messageText, '\n').trim();
        printWarning(executionMode, `${diagnostic.file.fileName} (${line + 1},${character + 1}): ${message}`);
    } else {
        printWarning(executionMode, `${ts.flattenDiagnosticMessageText(diagnostic.messageText, '\n').trim()}`);
    }
};
