/** @internal: Don't export this line */
import {
    clientListTransformer,
    decimalTransformer,
    decoratorTransformer,
    messageTransformer,
    pageMetadataTransformer,
} from '@sage/xtrem-cli-transformers';
import '@sage/xtrem-minify';
import * as ts from 'typescript';

// ts-loader expects it to be in this export format.
// eslint-disable-next-line func-names
module.exports = function () {
    return {
        before: [
            decimalTransformer,
            decoratorTransformer,
            messageTransformer,
            pageMetadataTransformer,
            clientListTransformer,
        ] as ts.TransformerFactory<ts.SourceFile>[],
    };
};
