import {
    cliContext,
    ExecutionMode,
    printInfo,
    printSuccess,
    printWarning,
    quitWithError,
    safeRmSync,
} from '@sage/xtrem-cli-lib';
import { mergeTranslationFiles, messageTransformer, pageMetadataTransformer } from '@sage/xtrem-cli-transformers';
import * as fs from 'fs';
import * as path from 'path';
import * as ts from 'typescript';
import { merge } from 'webpack-merge';
import { filterExternals } from '../compile/utils';

// use require to fix "This expression is not callable." error during compilation of xtrem-avalara-gateway
import webpack = require('webpack');

export const compilePlugin = () => {
    const { executionMode, dir } = cliContext;
    const buildDir = path.resolve(dir, 'build');

    const localWebpackConfigPath = path.resolve(dir, 'webpack.config.js');

    // Check package file content.
    const packageFileContent = JSON.parse(fs.readFileSync(path.resolve(dir, 'package.json'), 'utf-8'));
    if (packageFileContent.xtremPlugin !== true) {
        quitWithError(
            executionMode,
            'Is this package really an Xtrem UI plugin? If so make sure to set the `xtremPlugin` property to true in your package file.',
        );
    }

    // Clean up build directory
    if (fs.existsSync(buildDir)) {
        printInfo(executionMode, 'Cleaning build directory...');
        safeRmSync(buildDir, { recursive: true });
        printSuccess(executionMode, 'Build directory is clear.');
    }

    const localWebpackConfig: webpack.Configuration = fs.existsSync(localWebpackConfigPath)
        ? // eslint-disable-next-line import/no-dynamic-require, global-require
          require(localWebpackConfigPath)
        : {};

    printInfo(executionMode, 'Building plugin...');

    // Define the path to the various loaders
    const loaders = {
        tsLoader: require.resolve('ts-loader'),
        urlLoader: require.resolve('url-loader'),
        styleLoader: require.resolve('style-loader'),
        cssLoader: require.resolve('css-loader'),
        sassLoader: require.resolve('sass-loader'),
    };

    // Merge base webpack config with custom configuration declared in the packages root directory
    const config: webpack.Configuration = merge(
        {
            entry: fs.existsSync(path.resolve(dir, 'lib', 'index.tsx')) ? './lib/index.tsx' : './lib/index.ts',
            mode: 'production',
            devtool: false,
            target: 'web',
            context: dir,
            output: {
                path: buildDir,
                libraryTarget: 'commonjs',
                filename: 'index.js',
            },
            externals: [filterExternals],
            plugins: [
                new webpack.optimize.LimitChunkCountPlugin({
                    maxChunks: 1,
                }),
            ],
            module: {
                rules: [
                    {
                        test: /\.tsx?$/,
                        exclude: /node_modules/,
                        loader: loaders.tsLoader,
                        options: {
                            colors: false,
                            configFile: path.resolve(dir, 'tsconfig.json'),
                            errorFormatter: (errorInfo: any) => {
                                return JSON.stringify(errorInfo);
                            },
                            getCustomTransformers: () => ({
                                before: [
                                    // Injecting message transformer to transform `ui.localize` calls.
                                    messageTransformer,
                                    pageMetadataTransformer,
                                ] as ts.TransformerFactory<ts.SourceFile>[],
                            }),
                        },
                    },
                    {
                        test: /\.(png|woff|woff2|eot|ttf|svg|gif)$/,
                        use: [
                            {
                                loader: loaders.urlLoader,
                                options: {
                                    limit: 100000,
                                },
                            },
                        ],
                    },
                    {
                        test: /\.css$/,
                        use: [
                            loaders.styleLoader,
                            {
                                loader: loaders.cssLoader,
                                options: {
                                    url: {
                                        // Exclude URLs starting with '/absolute/path'
                                        filter: (url: string) => !url.startsWith('/'),
                                    },
                                },
                            },
                        ],
                    },
                    {
                        test: /\.scss$/,
                        use: [loaders.styleLoader, loaders.cssLoader, loaders.sassLoader],
                        include: path.resolve(dir, 'lib'),
                    },
                ],
            },
            resolve: {
                extensions: ['.ts', '.tsx', '.js', '.json'],
            },
        },
        localWebpackConfig,
    );

    // Run webpack compilation
    webpack(config).run((err: Error, stats: webpack.Stats) => {
        const statsObj = stats && stats.toJson();
        if (stats.hasWarnings() && statsObj.warnings)
            statsObj.warnings.forEach(w => {
                printWarning(executionMode, w.message);
            });
        if (stats.hasErrors() && statsObj.errors)
            statsObj.errors.forEach(e => {
                printWarning(executionMode, e.message);
            });
        if (err) {
            quitWithError(executionMode, err.message);
        }

        if (stats.hasErrors()) {
            quitWithError(executionMode, 'See compilation errors above.');
        }

        mergeTranslationFiles(dir);
        printSuccess(executionMode, 'Plugin is ready.');

        if (executionMode === ExecutionMode.STANDALONE) {
            process.exit(0);
        }
    });
};
