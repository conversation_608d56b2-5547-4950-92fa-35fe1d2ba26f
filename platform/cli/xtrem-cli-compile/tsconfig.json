{"extends": "../../tsconfig-package.json", "include": ["index.ts", "lib/**/*", "test/**/*"], "exclude": ["test/fixtures/test-app/**/*"], "compilerOptions": {"outDir": "build", "rootDir": ".", "baseUrl": "."}, "references": [{"path": "../../back-end/xtrem-minify"}, {"path": "../xtrem-cli-lib"}, {"path": "../xtrem-cli-transformers"}, {"path": "../xtrem-client-gen"}, {"path": "../xtrem-cop"}, {"path": "../../back-end/xtrem-core"}, {"path": "../../back-end/xtrem-dts-bundle"}, {"path": "../../shared/xtrem-i18n"}, {"path": "../../back-end/xtrem-log"}, {"path": "../../shared/xtrem-shared"}]}