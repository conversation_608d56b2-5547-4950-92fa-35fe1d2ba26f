import { ExecutionMode, printInfo, printSuccess, printWarning, quitWithError } from '@sage/xtrem-cli-lib';
import { ApplicationManager } from '@sage/xtrem-core';
import { MultiTenantsExportOptions, TenantService } from '@sage/xtrem-data-management';
import { TenantOptions } from '.';
import { InitTenantData } from './init-tenant';
import { manageAdminUsers } from './utils';

interface ImportCliConfig extends InitTenantData {
    location: string;
}

export const exportTenant = async (
    executionMode: ExecutionMode,
    dir: string,
    exportConfig: MultiTenantsExportOptions,
): Promise<void> => {
    try {
        const application = await ApplicationManager.getApplication(dir, { applicationType: 'admin-tool' });
        await application.packageManager.validatePackageVersions();
        const { tenantSelector } = exportConfig;

        if (!tenantSelector) {
            printWarning(executionMode, 'tenantSelector missing!');
            return;
        }

        await TenantService.exportMultiTenants(application, exportConfig);
    } catch (e) {
        quitWithError(executionMode, e);
    }
};

function checkInitData(initData: ImportCliConfig): void {
    const { tenant, location, adminUser, adminUsers, customer } = initData;

    if (!tenant.id) {
        throw new Error('Tenant ID missing!');
    }
    if (!tenant.name) {
        throw new Error('Tenant name missing!');
    }

    if (!location) {
        throw new Error('Location missing!');
    }

    if (!adminUser && !adminUsers) {
        throw new Error('Admin user missing!');
    }

    if (!customer) {
        throw new Error('Customer data missing!');
    }
    if (!customer.id) {
        throw new Error('Customer ID missing!');
    }
    if (!customer.name) {
        throw new Error('Customer name missing!');
    }
    if (!initData.tenant.id) {
        throw new Error('Tenant ID missing!');
    }
    if (!initData.tenant.name) {
        throw new Error('Tenant name missing!');
    }
    if (!initData.customer) {
        throw new Error('Customer data missing!');
    }
    if (!initData.customer.id) {
        throw new Error('Customer ID missing!');
    }
    if (!initData.customer.name) {
        throw new Error('Customer name missing!');
    }
}

export const importTenant = async (
    executionMode: ExecutionMode,
    dir: string,
    options: Pick<TenantOptions, 'import' | 'location' | 'chunkSize' | 'keepFiles'>,
): Promise<void> => {
    const { import: base64ImportDataConfig } = options;

    try {
        if (!base64ImportDataConfig) {
            throw new Error('Import data missing!');
        }

        const application = await ApplicationManager.getApplication(dir, { applicationType: 'admin-tool' });
        await application.packageManager.validatePackageVersions();

        let initData = JSON.parse(Buffer.from(base64ImportDataConfig, 'base64').toString()) as ImportCliConfig;

        if (options.location) initData.location = options.location;

        // Manage optional parameters
        initData = {
            ...initData,
            skipWelcomeEmail: !!initData.skipWelcomeEmail,
        };

        checkInitData(initData);

        const { tenant, location, customer } = initData;

        printInfo(executionMode, `Initializing tenant data:${JSON.stringify(initData, null, '\t')}`);

        printInfo(executionMode, `Importing tenant '${tenant.id}' from '${location}'`);
        await TenantService.importTenant(application, {
            tenant,
            location,
            customer,
            chunkSize: options.chunkSize,
            keepFiles: options.keepFiles,
        });

        printSuccess(executionMode, `Successfully imported tenant '${tenant.id}' from '${location}'`);

        await manageAdminUsers(application, executionMode, initData);
    } catch (e) {
        quitWithError(executionMode, e);
    }
};
