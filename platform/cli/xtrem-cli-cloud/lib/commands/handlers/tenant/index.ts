import { cliContext, ExecutionMode, printInfo, raiseErrorOnInvalidCommandLine } from '@sage/xtrem-cli-lib';
import { camelCase } from 'lodash';
import { deleteTenant } from './delete-tenant';
import { executeCustomSql } from './execute-custom-sql';
import { exportTenant, importTenant } from './export-import-tenant';
import { initTenant } from './init-tenant';
import { updateSetupData } from './update-setup-data';
import { updateTenant } from './update-tenant';

export interface TenantOptions {
    init?: string;
    layers?: string;
    update: string;
    updateSetupData?: boolean;
    delete?: string;
    export?: string;
    location?: string;
    import?: string;
    exportId?: string;
    diffCompliant?: boolean;
    timestamp?: boolean;
    keepAllValues?: boolean;
    anonymize?: boolean;
    force?: boolean;
    chunkSize?: number;
    /** Max file size for export in megabytes. If exceeded, the export will be split into files up to this size. */
    maxFileSizeInMb?: number;
    /** Execute a custom SQL script ? */
    executeCustomSql?: boolean;
    /** Keep extracted files after import */
    keepFiles?: boolean;
    /** Run in dry mode ? */
    dryRun?: boolean;
    /** Tenants to process */
    tenants?: string[];
    /**
     * The (optional) name of the cluster
     */
    clusterName?: string;
}

export const tenant = async (options: TenantOptions) => {
    const { executionMode, dir } = cliContext;

    // choose only one option
    const tenantOptions = ['init', 'update', 'delete', 'export', 'import', 'update-setup-data', 'execute-custom-sql'];
    if (
        tenantOptions.filter(tenantOption => {
            const option = (options as any)[camelCase(tenantOption)];
            printInfo(executionMode, `${camelCase(tenantOption)}='${option}'`);
            return option !== undefined;
        }).length !== 1
    ) {
        throw new Error('Missing command');
    }

    const layers = options.layers?.split(',').map(layer => layer.trim());
    if (options.init) {
        printInfo(executionMode, `init layers=[${layers}] data='${options.init}'`);
        await initTenant(executionMode, dir, {
            base64InitData: options.init,
            layers,
        });
    } else if (options.update) {
        await updateTenant(executionMode, dir, {
            base64UpdateData: options.update,
        });
    } else if (options.updateSetupData) {
        await updateSetupData(executionMode, dir, {
            forceReloadOfCsv: options.force,
            clusterName: options.clusterName,
        });
    } else if (options.delete) {
        printInfo(executionMode, `delete tenant='${options.delete}'`);
        await deleteTenant(executionMode, dir, options.delete);
    } else if (options.export) {
        printInfo(executionMode, `export tenantSelector='${options.export}'`);
        printInfo(executionMode, `export anonymize='${options.anonymize}'`);
        await exportTenant(executionMode, dir, {
            tenantSelector: options.export,
            exportId: options.exportId,
            location: options.location,
            diffCompliant: options.diffCompliant,
            // yarg is turning the '--no-timestamp' option into timestamp: false
            noTimestamp: !(options.timestamp ?? true),
            keepAllValues: options.keepAllValues,
            anonymize: options.anonymize,
            keepFiles: options.keepFiles,
            maxFileSizeInMb: options.maxFileSizeInMb,
        });
    } else if (options.import) {
        printInfo(executionMode, `import config='${options.import}'`);
        await importTenant(executionMode, dir, options);
    } else if (options.executeCustomSql) {
        printInfo(executionMode, 'execute custom SQL script');
        await executeCustomSql(executionMode, dir, {
            location: options.location,
            tenants: options.tenants,
            dryRun: options.dryRun,
        });
    } else {
        raiseErrorOnInvalidCommandLine();
    }
    if (executionMode === ExecutionMode.STANDALONE) process.exit();
};
