import { ExecutionMode, printInfo, printSuccess, printWarning, quitWithError } from '@sage/xtrem-cli-lib';
import { ApplicationManager } from '@sage/xtrem-core';
import { TenantService } from '@sage/xtrem-data-management';

export const deleteTenant = async (executionMode: ExecutionMode, dir: string, tenantId: string): Promise<void> => {
    try {
        const application = await ApplicationManager.getApplication(dir, { applicationType: 'admin-tool' });

        await application.packageManager.validatePackageVersions();

        if (!tenantId) {
            printWarning(executionMode, 'Tenant ID missing!');
            return;
        }
        printInfo(executionMode, `Deleting tenant ${tenantId}`);
        await TenantService.deleteTenant(application, tenantId);

        printSuccess(executionMode, `Successfully deleted tenant ${tenantId}`);
    } catch (e) {
        quitWithError(executionMode, e);
    }
};
