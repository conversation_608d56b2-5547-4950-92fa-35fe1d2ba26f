import { ExecutionMode, printInfo, printSuccess, quitWithError } from '@sage/xtrem-cli-lib';
import { ApplicationManager, UserData } from '@sage/xtrem-core';
import { InitTenantOptions, TenantService } from '@sage/xtrem-data-management';
import { manageAdminUsers } from './utils';

export interface InitTenantData extends InitTenantOptions {
    // Admin users management
    // * The administrator's email, firstname, lastname and locale, --deprecated
    // * The list of administrators and their email, firstname, lastname and locale,
    // If both adminUser and adminUsers are provided, the adminUsers will be used.
    adminUser: UserData;
    adminUsers: UserData[];
    skipWelcomeEmail?: boolean;
}

export const initTenant = async (
    executionMode: ExecutionMode,
    dir: string,
    options: {
        base64InitData: string;
        layers?: string[];
        doNotExitOnErrors?: true;
    },
): Promise<void> => {
    try {
        let initData = JSON.parse(Buffer.from(options.base64InitData, 'base64').toString()) as InitTenantData;

        if (!initData.tenant || !initData.tenant.id) {
            throw new Error('Tenant ID missing!');
        }
        if (!initData.customer) {
            throw new Error('Customer data missing!');
        }
        if (!initData.customer.id) {
            throw new Error('Customer ID missing!');
        }
        if (!initData.customer.name) {
            throw new Error('Customer name missing!');
        }

        // Manage optional parameters
        initData = {
            ...initData,
            skipWelcomeEmail: !!initData.skipWelcomeEmail,
        };

        printInfo(executionMode, `Initializing tenant data:${JSON.stringify(initData, null, '\t')}`);

        const application = await ApplicationManager.getApplication(dir, { applicationType: 'admin-tool' });
        await application.packageManager.validatePackageVersions();

        await TenantService.initTenant(application, {
            tenant: {
                id: initData.tenant.id,
                name: initData.tenant.name,
            },
            customer: {
                id: initData.customer.id,
                name: initData.customer.name,
            },
            serviceOptions: initData.serviceOptions,
            // Activate all packages if packs key is empty. Hack waiting for licence implementation.
            allPackages: !initData.packages,
            packages: initData.packages,
            layers: options.layers,
        });
        printSuccess(executionMode, 'Successfully loaded tenant data');

        // Admin users management
        await manageAdminUsers(application, executionMode, initData);
    } catch (e) {
        if (options.doNotExitOnErrors) throw e;
        quitWithError(executionMode, e);
    }
};
