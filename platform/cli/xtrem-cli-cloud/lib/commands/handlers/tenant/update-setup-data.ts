import { ExecutionMode, printInfo, printSuccess, quitWithError } from '@sage/xtrem-cli-lib';
import { ApplicationManager } from '@sage/xtrem-core';
import { TenantService } from '@sage/xtrem-data-management';

export const updateSetupData = async (
    executionMode: ExecutionMode,
    dir: string,
    options: {
        forceReloadOfCsv?: boolean;
        /**
         * The (optional) name of the cluster
         */
        clusterName?: string;
    },
): Promise<void> => {
    try {
        const application = await ApplicationManager.getApplication(dir, { applicationType: 'admin-tool' });
        printInfo(executionMode, `Update setup data for application ${application.name}`);

        await TenantService.updateSetupData(application, options);

        printSuccess(executionMode, 'Successfully updated setup data');
    } catch (e) {
        quitWithError(executionMode, e);
    }
};
