import { ExecutionMode, printInfo, printSuccess } from '@sage/xtrem-cli-lib';
import { Application } from '@sage/xtrem-core';
import { InitTenantData } from './init-tenant';

export async function manageAdminUsers(
    application: Application,
    executionMode: ExecutionMode,
    initData: InitTenantData,
): Promise<void> {
    // * The administrator's email, firstname, lastname and locale, --deprecated
    // * The list of administrators and their email, firstname, lastname and locale,
    // If both adminUser and adminUsers are provided, the adminUsers will be used.

    if (initData.adminUsers) {
        if (initData.adminUser) {
            printInfo(executionMode, 'Both adminUser and adminUsers are provided. Using adminUsers');
        }
        if (initData.adminUsers.length === 0) {
            throw new Error('Empty adminUsers list!');
        }
        printInfo(executionMode, 'Creating/Updating Admin Users');
        for (let i = 0; i < initData.adminUsers.length; i += 1) {
            if (Object.values(initData.adminUsers[i]).filter(value => value == null).length > 0) {
                throw new Error('Missing admin user data in adminUsers input!');
            }
            await application.createAdminUser(initData.tenant.id, initData.adminUsers[i], {
                skipWelcomeEmail: !!initData.skipWelcomeEmail,
                isFirstAdminUser: i === 0,
            });
        }
        printSuccess(executionMode, `Successfully created/updated ${initData.adminUsers.length} admin users`);
    } else {
        if (Object.values(initData.adminUser).filter(value => value == null).length > 0) {
            throw new Error('Missing admin user data in adminUser input!');
        }
        printInfo(executionMode, 'Creating Admin User');
        await application.createAdminUser(initData.tenant.id, initData.adminUser, {
            isFirstAdminUser: true,
            skipWelcomeEmail: !!initData.skipWelcomeEmail,
        });
        printSuccess(executionMode, 'Successfully created admin user');
    }
}
