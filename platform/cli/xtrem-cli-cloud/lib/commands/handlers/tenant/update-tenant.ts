import { ExecutionMode, printInfo, printSuccess, quitWithError } from '@sage/xtrem-cli-lib';
import { ApplicationManager, UserData } from '@sage/xtrem-core';
import { TenantService, UpdateTenantOptions } from '@sage/xtrem-data-management';

export interface UpdateTenantData extends UpdateTenantOptions {
    // Admin users management
    // * The list of administrators and their email, firstname, lastname and locale,
    adminUsers: UserData[];
}

export const updateTenant = async (
    executionMode: ExecutionMode,
    dir: string,
    options: {
        base64UpdateData: string;
        doNotExitOnErrors?: true;
    },
): Promise<void> => {
    try {
        const initData = JSON.parse(Buffer.from(options.base64UpdateData, 'base64').toString()) as UpdateTenantData;

        if (!initData.tenant || !initData.tenant.id) {
            throw new Error('Tenant ID missing!');
        }

        const application = await ApplicationManager.getApplication(dir, { applicationType: 'admin-tool' });
        await application.packageManager.validatePackageVersions();

        if (initData.adminUsers) {
            printInfo(executionMode, 'Creating/Updating Admin Users');
            // eslint-disable-next-line no-restricted-syntax
            for (const adminUser of initData.adminUsers) {
                if (Object.values(adminUser).filter(value => value == null).length > 0) {
                    throw new Error('Missing admin user data in adminUsers input!');
                }
                await application.createAdminUser(initData.tenant.id, adminUser, {
                    skipWelcomeEmail: true,
                    isFirstAdminUser: false,
                });
            }
            printSuccess(executionMode, `Successfully created/updated ${initData.adminUsers.length} admin users`);
        } else {
            printInfo(executionMode, `Updating tenant data:${JSON.stringify(initData, null, '\t')}`);
            await TenantService.updateTenant(application, {
                tenant: { id: initData.tenant.id },
                serviceOptions: initData.serviceOptions,
                allPackages: true,
            });
            printSuccess(executionMode, 'Successfully updated tenant data');
        }
    } catch (e) {
        if (options.doNotExitOnErrors) throw e;
        quitWithError(executionMode, e);
    }
};
