import { CliHooks, ExecutionMode, printSuccess, quitWithError } from '@sage/xtrem-cli-lib';
import { ApplicationManager } from '@sage/xtrem-core';

export const executeCustomSql = async (
    executionMode: ExecutionMode,
    dir: string,
    options: {
        location?: string;
        tenants?: string[];
        dryRun?: boolean;
    },
): Promise<void> => {
    if (options.location == null) throw new Error('No location provided');
    if (options.tenants == null) throw new Error('No tenants provided');
    try {
        const application = await ApplicationManager.getApplication(dir, { applicationType: 'admin-tool' });
        await CliHooks.upgradeManager.executeCustomSqlScript(
            application,
            options.location,
            options.tenants,
            !!options.dryRun,
        );
        printSuccess(executionMode, 'Successfully executed custom SQL');
    } catch (e) {
        quitWithError(executionMode, e);
    }
};
