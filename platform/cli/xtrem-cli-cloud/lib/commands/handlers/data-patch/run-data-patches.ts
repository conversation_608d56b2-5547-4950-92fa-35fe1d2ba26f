import { CliHooks, ExecutionMode, printInfo, printSuccess, quitWithError } from '@sage/xtrem-cli-lib';
import { ApplicationManager } from '@sage/xtrem-core';

/**
 * Run the data patches
 * @param executionMode
 * @param options
 */
export const runDataPatches = async (
    executionMode: ExecutionMode,
    options: {
        dir: string;
        doNotExitOnErrors?: true;
    },
): Promise<void> => {
    try {
        // Make sure the app is loaded
        const application = await ApplicationManager.getApplication(options.dir, { applicationType: 'admin-tool' });

        printInfo(executionMode, `Apply data patches - ${application.schemaName}`);

        CliHooks.dataPatchManager.execute(application);
        printSuccess(executionMode, 'Successfully applied data patches');
    } catch (e) {
        if (options.doNotExitOnErrors) throw e;
        else quitWithError(executionMode, `Apply data patches failed: ${e.stack}`);
    }
};
