import { cliContext, ExecutionMode, raiseErrorOnInvalidCommandLine } from '@sage/xtrem-cli-lib';
import { postProcess } from './post-process';
import { runDataPatches } from './run-data-patches';
import { testDataPatches } from './test-data-patches';

export interface DataPatchOptions {
    run: boolean;
    test: true | string;
    runPostProcess: boolean; // Only valid if run === true,
    skipDbRestore: boolean; // Valid when test
}

export const dataPatch = async (options: DataPatchOptions) => {
    const { executionMode, dir } = cliContext;
    if (options.run) {
        if (options.runPostProcess) {
            await postProcess(executionMode, { ...options, dir });
        } else {
            await runDataPatches(executionMode, { ...options, dir });
        }
    } else if (options.test) {
        await testDataPatches(executionMode, dir, {
            version: options.test,
            skipDbRestore: options.skipDbRestore,
        });
    } else {
        raiseErrorOnInvalidCommandLine();
    }
    if (executionMode === ExecutionMode.STANDALONE) process.exit();
};
