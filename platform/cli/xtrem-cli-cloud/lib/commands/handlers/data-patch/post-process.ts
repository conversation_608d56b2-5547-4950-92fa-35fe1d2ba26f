import { CliHooks, ExecutionMode, printInfo, printSuccess, quitWithError } from '@sage/xtrem-cli-lib';
import { ApplicationManager } from '@sage/xtrem-core';

/**
 * Post-process of the data patches (rename vLatest folders, ...)
 * @param executionMode
 * @param options
 */
export const postProcess = async (executionMode: ExecutionMode, options: { dir: string }): Promise<void> => {
    try {
        // Make sure the app is loaded
        const application = await ApplicationManager.getApplication(options.dir, { applicationType: 'admin-tool' });

        printInfo(executionMode, 'Post processes of data patches');
        CliHooks.dataPatchManager.postProcess(application);
        printSuccess(executionMode, 'Successfully ran the post processes');
    } catch (e) {
        quitWithError(executionMode, `Post-processes failed: ${e.toString()}`);
    }
};
