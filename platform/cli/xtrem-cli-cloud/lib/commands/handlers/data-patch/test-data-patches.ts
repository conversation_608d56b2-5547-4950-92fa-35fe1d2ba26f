import { ExecutionMode, printInfo, printSuccess, quitWithError } from '@sage/xtrem-cli-lib';
import { ApplicationManager, S3Manager } from '@sage/xtrem-core';
import { runDataPatches } from './run-data-patches';

/**
 * Test the data patches
 * @param executionMode
 * @param dir
 * @param options
 */
export const testDataPatches = async (
    executionMode: ExecutionMode,
    dir: string,
    options: {
        version: true | string;
        skipDbRestore: boolean;
    },
): Promise<void> => {
    try {
        const application = await ApplicationManager.getApplication(dir, { applicationType: 'admin-tool' });

        // Note: version is true when --restore-from-s3 was used without providing a version
        const versionToTest = options.version === true ? 'latest' : options.version;

        printInfo(executionMode, `Test data patches on ${application.name}@${versionToTest}`);

        if (!options.skipDbRestore) {
            // Restore the dump from S3
            await new S3Manager(application).restoreSchemaFromS3Bucket(versionToTest);
        }

        // Run the data patches
        await runDataPatches(executionMode, {
            dir,
            doNotExitOnErrors: true,
        });
        printSuccess(executionMode, 'Test successful');
    } catch (e) {
        quitWithError(executionMode, `Test failed: ${e.toString()}`);
    }
};
