import { cliContext, ExecutionMode, printInfo, quitWithError } from '@sage/xtrem-cli-lib';
import { ApplicationManager } from '@sage/xtrem-core';
import { AnonymizationService } from '@sage/xtrem-data-management';

export interface AnonymizeOptions {
    outputPath: string;
}

export const pgAnonymizer = async (anonymizeOptions: AnonymizeOptions): Promise<void> => {
    const { executionMode, dir } = cliContext;
    try {
        const application = await ApplicationManager.getApplication(dir, { applicationType: 'admin-tool' });
        const { outputPath } = anonymizeOptions;
        printInfo(executionMode, `pgAnonymizer outputPath='${outputPath}'`);
        await AnonymizationService.generateRules(application, outputPath);
    } catch (e) {
        quitWithError(executionMode, e);
    }
    if (executionMode === ExecutionMode.STANDALONE) process.exit();
};
