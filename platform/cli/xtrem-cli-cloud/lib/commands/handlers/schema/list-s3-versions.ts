import { ExecutionMode, printInfo, printSuccess, quitWithError } from '@sage/xtrem-cli-lib';
import { ApplicationManager, S3Manager } from '@sage/xtrem-core';

export const listS3Versions = async (executionMode: ExecutionMode, dir: string): Promise<void> => {
    try {
        const application = await ApplicationManager.getApplication(dir, { applicationType: 'admin-tool' });
        printInfo(
            executionMode,
            `List available S3 backups that can be used to generate SQL files for application ${application.name}`,
        );
        const versions = await new S3Manager(application).getAvailableVersions();
        const sep = '\n\t\t- ';
        printInfo(
            executionMode,
            `Available versions compatible with ${application.name}@${application.version} are:${sep}${versions.join(
                sep,
            )}`,
        );

        printSuccess(executionMode, 'Successfully listed versions');
    } catch (e) {
        quitWithError(executionMode, `List S3 versions failed: ${e.toString()}`);
    }
};
