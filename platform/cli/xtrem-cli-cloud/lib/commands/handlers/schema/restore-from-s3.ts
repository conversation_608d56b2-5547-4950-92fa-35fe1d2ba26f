import { ExecutionMode, printInfo, printSuccess, quitWithError } from '@sage/xtrem-cli-lib';
import { ApplicationManager, S3ConfigurationType, S3Manager } from '@sage/xtrem-core';

export const restoreFromS3 = async (
    executionMode: ExecutionMode,
    dir: string,
    version: string,
    s3ConfigurationType: S3ConfigurationType,
    checkSingleSchema: boolean,
    skipValuesHash: boolean,
): Promise<void> => {
    try {
        const application = await ApplicationManager.getApplication(dir, { applicationType: 'admin-tool' });
        // Note: version is true when --restore-from-s3 was used without providing a version
        printInfo(executionMode, `Restore schema ${application.name}@${version === '' ? 'latest' : version} from S3`);
        await new S3Manager(application).restoreSchemaFromS3Bucket(version, s3ConfigurationType, {
            checkSingleSchema,
            skipValuesHash,
        });
        printSuccess(executionMode, 'Successfully restored from S3');
    } catch (e) {
        quitWithError(executionMode, `Restore from S3 failed: ${e.stack}`);
    }
};
