import { ExecutionMode, printInfo, printSuccess, quitWithError } from '@sage/xtrem-cli-lib';
import { ApplicationManager, S3Manager } from '@sage/xtrem-core';

export const dumpToS3 = async (
    executionMode: ExecutionMode,
    dir: string,
    options: { doNotExitOnErrors?: true },
): Promise<void> => {
    try {
        const application = await ApplicationManager.getApplication(dir, { applicationType: 'admin-tool' });

        printInfo(executionMode, `Dump schema ${application.name} to S3`);
        await new S3Manager(application).dumpSchemaToS3Bucket();
        printSuccess(executionMode, 'Successfully dumped to S3');
    } catch (e) {
        if (options.doNotExitOnErrors) throw e;
        else quitWithError(executionMode, `Dump to S3 failed: ${e.toString()}`);
    }
};
