import { cliContext, ExecutionMode, raiseErrorOnInvalidCommandLine } from '@sage/xtrem-cli-lib';
import { S3ConfigurationType } from '@sage/xtrem-core';
import { checkSchema } from './check-schema';
import { create } from './create';
import { dumpToS3 } from './dump-to-s3';
import { fixSchema } from './fix-schema';
import { listS3Versions } from './list-s3-versions';
import { renameSchema } from './rename-schema';
import { reset } from './reset';
import { restoreFromS3 } from './restore-from-s3';

export interface SchemaOptions {
    force: boolean; // Only valid if (isCreate === true) || (testUpgrade != null)
    resetDatabase: boolean; // Only valid if (isCreate === true
    resetSchema: boolean; //  Only valid if (isCreate === true
    isCreate: boolean;
    isDumpToS3: boolean;
    /**
     * Rename the schema to the provided name
     */
    renameTo: string;
    /**
     * Rename the schema from the provided name
     */
    renameFrom: string;
    isListS3Versions: boolean;
    isRestoreFromS3: string;
    isReset: boolean;
    layersAsString?: string;
    s3ConfigType?: string;
    fixColumnOrder?: boolean;
    /**
     * Only when isRestoreFromS3 is set: should an error be raised if there are more than 1 schema
     * in the database after the restore
     */
    checkSingleSchema?: boolean;
    /**
     * Only when isRestoreFromS3 is set: should the re-compute of _valuesHash be skipped ?
     */
    skipValuesHash?: boolean;
    /**
     * Should the creation of the tables be skipped when creating the schema ?
     */
    skipTables?: boolean;
    // Check that the SQL schema is in sync with node definitions. Raise an error if not.
    check?: boolean;
}

export const schema = async (options: SchemaOptions) => {
    const { executionMode, dir } = cliContext;

    if (options.isCreate) {
        await create(executionMode, dir, options);
    } else if (options.isDumpToS3) {
        await dumpToS3(executionMode, dir, { doNotExitOnErrors: true });
    } else if (options.isListS3Versions) {
        await listS3Versions(executionMode, dir);
    } else if (options.isRestoreFromS3 != null) {
        await restoreFromS3(
            executionMode,
            dir,
            options.isRestoreFromS3,
            (options.s3ConfigType as S3ConfigurationType) || 'forSqlFiles',
            !!options.checkSingleSchema,
            !!options.skipValuesHash,
        );
    } else if (options.isReset) {
        // TODO: Remove later - temporary command to reset the schema while the upgrade is finalized
        await reset(executionMode, dir, { layersAsString: options.layersAsString });
    } else if (options.fixColumnOrder) {
        await fixSchema(executionMode, dir, { fixColumnOrder: true });
    } else if (options.renameTo != null) {
        await renameSchema(executionMode, dir, { to: options.renameTo });
    } else if (options.renameFrom != null) {
        await renameSchema(executionMode, dir, { from: options.renameFrom });
    } else if (options.check) {
        await checkSchema(executionMode, dir);
    } else {
        raiseErrorOnInvalidCommandLine();
    }
    if (executionMode === ExecutionMode.STANDALONE) process.exit();
};
