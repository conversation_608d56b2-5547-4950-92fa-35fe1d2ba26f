import { ApplicationManager } from '@sage/xtrem-core';

import { CliHooks, ExecutionMode, printInfo, quitWithError } from '@sage/xtrem-cli-lib';

/**
 * Check that the SQL schema is in sync with node definitions. Raise an error if not.
 */
export const checkSchema = async (executionMode: ExecutionMode, dir: string): Promise<void> => {
    try {
        const application = await ApplicationManager.getApplication(dir, { applicationType: 'admin-tool' });

        printInfo(executionMode, `Check schema ${application.name}`);

        await CliHooks.upgradeManager.upgradeSqlSchemaFromCli(application, {
            mode: 'upgradeOnly',
            checkSchema: true,
            force: true,
            fullReloadOfSetupLayer: false,
        });
    } catch (e) {
        quitWithError(executionMode, `Check failed: ${e.stack}`);
    }
};
