import { CliLogger, ExecutionMode, quitWithError } from '@sage/xtrem-cli-lib';
import { ApplicationManager } from '@sage/xtrem-core';
import { DataManagementCli } from '@sage/xtrem-data-management';
import { create } from './create';

export const reset = async (
    executionMode: ExecutionMode,
    dir: string,
    options: { layersAsString?: string; doNotExitOnErrors?: true },
): Promise<void> => {
    const application = await ApplicationManager.getApplication(dir, { applicationType: 'admin-tool' });
    await DataManagementCli.resetSchema(application, {
        doNotExitOnErrors: options.doNotExitOnErrors,
        layersAsString: options.layersAsString || 'setup',
        logger: new CliLogger(executionMode),
        callbacks: {
            createSchema: () => create(executionMode, dir, { resetSchema: true, doNotExitOnErrors: true }),
            quitWithError: (err: Error) => {
                quitWithError(executionMode, err);
            },
        },
    });
};
