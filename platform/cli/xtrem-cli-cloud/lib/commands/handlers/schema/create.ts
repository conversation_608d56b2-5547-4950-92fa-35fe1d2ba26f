import { ExecutionMode, printInfo, printSuccess, printWarning, quitWithError } from '@sage/xtrem-cli-lib';
import { ApplicationManager } from '@sage/xtrem-core';
import { DataManagementCli } from '@sage/xtrem-data-management';

export const create = async (
    executionMode: ExecutionMode,
    dir: string,
    options: {
        force?: boolean;
        resetDatabase?: boolean;
        resetSchema?: boolean;
        /**
         * Should the creation of tables be skipped ?
         */
        skipTables?: boolean;
        doNotExitOnErrors?: true;
    },
): Promise<void> => {
    try {
        const application = await ApplicationManager.getApplication(dir, { applicationType: 'admin-tool' });

        if (options.force) {
            printWarning(executionMode, '--force option is deprecated. Use --reset-database instead');
            options.resetDatabase = true;
        }
        if (options.resetDatabase) {
            printInfo(executionMode, '!!! Dropping database !!!');
            await DataManagementCli.dropDatabaseIfExists();
        }

        if (options.skipTables) {
            printInfo(
                executionMode,
                `Creating ${application.schemaName} SQL schema WITHOUT ANY table for ${application.name}@${application.version}`,
            );
            await application.packageManager.ensureSchemaExists({ resetSchema: options.resetSchema });
            printSuccess(
                executionMode,
                `Successfully created the ${application.schemaName} SQL Schema (WITHOUT any table)`,
            );
            return;
        }

        printInfo(
            executionMode,
            `Creating ${application.schemaName} SQL schema and its tables for ${application.name}@${application.version}`,
        );
        if (await application.packageManager.createSqlSchemaAndTables({ resetSchema: options.resetSchema })) {
            printSuccess(executionMode, `Successfully created the ${application.schemaName} SQL Schema and its tables`);
        } else {
            const message = `SQL Schema creation of ${application.schemaName} was unsuccessful`;
            if (options.doNotExitOnErrors) throw new Error(message);
            quitWithError(executionMode, message);
        }
    } catch (e) {
        if (options.doNotExitOnErrors) throw e;
        quitWithError(executionMode, e);
    }
};
