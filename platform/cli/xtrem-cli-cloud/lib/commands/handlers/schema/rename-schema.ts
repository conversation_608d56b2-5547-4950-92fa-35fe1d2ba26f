import { ExecutionMode, printInfo, quitWithError } from '@sage/xtrem-cli-lib';
import { Application, ApplicationManager } from '@sage/xtrem-core';

/**
 * Rename a schema
 */
export const renameSchema = async (
    executionMode: ExecutionMode,
    dir: string,
    options: {
        from?: string;
        to?: string;
    },
): Promise<void> => {
    const application = await ApplicationManager.getApplication(dir, { applicationType: 'admin-tool' });

    if (options.from == null && options.to == null)
        throw new Error('You must provide either the old or the new schema name');

    if (options.from != null) {
        if (options.from === '') throw new Error('You must provide the new name of the schema');
        options.to = application.schemaName; // Use the config to get the new schema name
        printInfo(executionMode, `Will use the configuration to get the original schema name: ${options.to}`);
        try {
            await Application.renameSchema(options.from, options.to);
        } catch (e) {
            quitWithError(
                executionMode,
                `Could not rename the schema from ${options.from} to ${options.to}: ${e.stack}`,
            );
        }
    } else if (options.to != null) {
        if (options.to === '') throw new Error('You must provide the old name of the schema');
        options.from = application.schemaName; // Use the config to get the new schema name
        printInfo(executionMode, `Will use the configuration to get the new schema name: ${options.from}`);
        try {
            await Application.renameSchema(options.from, options.to);
        } catch (e) {
            quitWithError(
                executionMode,
                `Could not rename the schema from ${options.from} to ${options.to}: ${e.stack}`,
            );
        }
    }
};
