import { CliHooks, ExecutionMode, quitWithError } from '@sage/xtrem-cli-lib';
import { ApplicationManager } from '@sage/xtrem-core';

/**
 * Fix schema issues:
 * - Order of columns
 * @param executionMode
 * @param dir
 * @param options
 */
export const fixSchema = async (
    executionMode: ExecutionMode,
    dir: string,
    options: { fixColumnOrder: boolean },
): Promise<void> => {
    const application = await ApplicationManager.getApplication(dir, { applicationType: 'admin-tool' });

    try {
        if (options.fixColumnOrder) {
            await CliHooks.upgradeManager.fixColumnOrder(application);
        }
    } catch (e) {
        quitWithError(executionMode, `Fixing of schema failed: ${e.stack}`);
    }
};
