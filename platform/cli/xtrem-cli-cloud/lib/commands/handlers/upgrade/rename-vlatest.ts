import { CliHooks, ExecutionMode, printInfo, printSuccess, quitWithError } from '@sage/xtrem-cli-lib';
import { ApplicationManager } from '@sage/xtrem-core';

/**
 * Rename the vLatest folders
 * @param executionMode
 * @param options
 */
export const renameVlatest = async (
    executionMode: ExecutionMode,
    options: { applicationFolder: string; repoRootFolder: string },
): Promise<void> => {
    // Note: this command can't be ran from the root folder (as there won't be any application to load)
    // Instead, it has to be ran from the xtrem-system folder so that there is a 'dummy' application to create.
    // We need an application to make sure the cliHooks are registered and to be able to invoke
    // functions from xtrem-system.
    try {
        // Make sure the app is loaded
        const application = await ApplicationManager.getApplication(options.applicationFolder, {
            applicationType: 'admin-tool',
        });
        printInfo(executionMode, `Rename 'vLatest' folders from ${application.dir}`);
        CliHooks.upgradeManager.renameLatestFolders(options.repoRootFolder);
        printSuccess(executionMode, "Successfully renamed the 'vlatest' folder");
    } catch (e) {
        quitWithError(executionMode, `Could not rename the 'vlatest' folders: ${e.toString()}`);
    }
};
