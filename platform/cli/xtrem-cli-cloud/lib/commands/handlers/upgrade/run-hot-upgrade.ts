import { ExecutionMode, printInfo, printSuccess, quitWithError } from '@sage/xtrem-cli-lib';
import { ApplicationManager, UpgradeMetricsType } from '@sage/xtrem-core';

/**
 * Run the upgrade process
 * @param executionMode
 * @param options
 */
export const runHotUpgrade = async (
    executionMode: ExecutionMode,
    options: {
        dir: string;
        metrics?: UpgradeMetricsType;
    },
): Promise<void> => {
    try {
        printInfo(executionMode, `Full-command line is ${process.argv.join(' ')}`);
        // Make sure the app is loaded
        const application = await ApplicationManager.getApplication(options.dir, { applicationType: 'admin-tool' });
        printInfo(executionMode, `Hot-upgrading SQL schema - ${application.schemaName}`);

        await application.hotUpgradeManager.executeHotUpgrade(application, options.metrics);

        printSuccess(executionMode, 'Successfully hot-upgraded the SQL Schema');
    } catch (e) {
        quitWithError(executionMode, `SQL Schema hot-upgrade failed: ${e.stack}`);
    }
};
