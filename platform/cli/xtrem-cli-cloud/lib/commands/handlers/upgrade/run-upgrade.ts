import {
    CliHooks,
    ExecutionMode,
    printError,
    printInfo,
    printSuccess,
    quitWithError,
    UpgradeMode,
} from '@sage/xtrem-cli-lib';
import { ApplicationManager, ConfigManager, UpgradeMetricsType } from '@sage/xtrem-core';

/**
 * Run the upgrade process
 * @param executionMode
 * @param options
 */
export const runUpgrade = async (
    executionMode: ExecutionMode,
    options: {
        dir: string;
        dev?: boolean; // Valid when run
        prod?: boolean; // Valid when run
        execute?: boolean; // Valid when run
        record?: boolean; // Valid when run
        /**
         * Should metrics files be generated ?
         * undefined : no CSV/JSON files, local:only on local, s3: local + S3
         */
        metrics?: UpgradeMetricsType;
        /**
         * Should all the CSV files from the SETUP layer be reloaded at the end of the upgrade ?
         * This will bypass the lookup from the git repo and reload all the **setup** CSV files
         */
        fullReloadOfSetupLayer?: boolean;
        force: boolean;
        doNotExitOnErrors?: true;
        skipVacuum: boolean;
        /**
         * The optional name of the cluster to upgrade
         */
        clusterName?: string;
    },
): Promise<void> => {
    try {
        printInfo(executionMode, `Full-command line is ${process.argv.join(' ')}`);
        // Make sure the app is loaded
        const application = await ApplicationManager.getApplication(options.dir, { applicationType: 'admin-tool' });

        let upgradeMode: UpgradeMode;
        if (options.dev) upgradeMode = 'replayAndUpgrade';
        else if (options.prod) upgradeMode = 'replayOnly';
        else if (options.execute) upgradeMode = 'upgradeOnly';
        else upgradeMode = 'replayAndRecord';
        if (options.prod) {
            if (ConfigManager.current.upgrade?.upgradeOnly) {
                upgradeMode = 'upgradeOnly';
                printInfo(
                    executionMode,
                    "WARNING: the 'upgradeOnly' option was activated by config. Option --execute will be used instead of --prod : SQL files won't be played",
                );
            }
            if (ConfigManager.current.upgrade?.fullReloadOfSetupLayer) {
                // Temp hack to allow showcase to run the upgrade engine (showcase image does not have any git installed
                // so all the set CSV files must be reloaded - we can't use the git log to get the accurate list of updated CSV files)
                options.fullReloadOfSetupLayer = true;
                printInfo(
                    executionMode,
                    "WARNING: 'fullReloadOfSetupLayer' option was activated by config. All the setup CSV files will be reloaded at the end of the upgrade",
                );
            }
        }

        printInfo(executionMode, `Upgrading SQL schema - ${application.schemaName} (upgrade mode:${upgradeMode})`);

        await CliHooks.upgradeManager.upgradeSqlSchemaFromCli(application, {
            force: options.force,
            mode: upgradeMode,
            fullReloadOfSetupLayer: !!options.fullReloadOfSetupLayer,
            metrics: options.metrics,
            skipVacuum: options.skipVacuum,
            clusterName: options.clusterName,
        });
        printSuccess(executionMode, `Successfully upgraded schema '${application.schemaName}'`);
    } catch (e) {
        if (options.doNotExitOnErrors) {
            printError(executionMode, e.stack);
            throw e;
        } else quitWithError(executionMode, `SQL Schema upgrade failed: ${e.stack}`);
    }
};
