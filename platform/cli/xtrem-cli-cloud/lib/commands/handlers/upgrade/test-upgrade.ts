import { ExecutionMode, printInfo, quitWithError } from '@sage/xtrem-cli-lib';
import { ApplicationManager, S3Manager } from '@sage/xtrem-core';
import { runUpgrade } from './run-upgrade';

/**
 * Test the upgrade
 * @param executionMode
 * @param dir
 * @param options
 */
export const testUpgrade = async (
    executionMode: ExecutionMode,
    dir: string,
    options: {
        version: true | string;
        skipDbRestore: boolean;
        skipVacuum: boolean;
        /**
         * The optional name of the cluster to upgrade
         */
        clusterName?: string;
    },
): Promise<void> => {
    try {
        const application = await ApplicationManager.getApplication(dir, { applicationType: 'admin-tool' });

        // Note: version is true when --restore-from-s3 was used without providing a version
        const versionToTest = options.version === true ? 'latest' : options.version;

        printInfo(executionMode, `Test upgrade on ${application.name}@${versionToTest}`);

        if (!options.skipDbRestore) {
            // Restore the dump from S3
            await new S3Manager(application).restoreSchemaFromS3Bucket(versionToTest);
        }

        // Run the upgrade
        await runUpgrade(executionMode, {
            dir,
            force: true,
            dev: true,
            doNotExitOnErrors: true,
            skipVacuum: options.skipVacuum,
            clusterName: options.clusterName,
        });
    } catch (e) {
        quitWithError(executionMode, `Upgrade test failed: ${e.stack}`);
    }
};
