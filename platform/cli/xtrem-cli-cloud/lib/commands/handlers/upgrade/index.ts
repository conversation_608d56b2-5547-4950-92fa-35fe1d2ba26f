import { cliContext, ExecutionMode, raiseErrorOnInvalidCommandLine } from '@sage/xtrem-cli-lib';
import { ConfigManager, UpgradeMetricsType } from '@sage/xtrem-core';
import { renameVlatest } from './rename-vlatest';
import { runHotUpgrade } from './run-hot-upgrade';
import { runUpgrade } from './run-upgrade';
import { testUpgrade } from './test-upgrade';

export interface UpgradeOptions {
    hot: boolean; // Run the hot-upgrade
    run: boolean;
    renameVlatest: string; // The root folder of the repo
    test: true | string;
    force: boolean; // Only valid if (test != null)
    dev: boolean; // Valid when run
    prod: boolean; // Valid when run
    record: boolean; // Valid when run
    execute: boolean; // Valid when run
    skipDbRestore: boolean; // Valid when test
    skipVacuum: boolean;
    /**
     * Should metrics files be generated ?
     * undefined : no CSV/JSON files, local:only on local, s3: local + S3
     */
    metrics?: UpgradeMetricsType;
    /**
     * The optional name of the cluster to upgrade
     */
    clusterName?: string;
}

export const upgrade = async (options: UpgradeOptions): Promise<void> => {
    const { executionMode, dir } = cliContext;

    if (ConfigManager.current.deploymentMode === 'development') {
        // the default root user need to be used for upgrade.
        ConfigManager.current.email = '';
        ConfigManager.current.user = '';
    }
    if (options.renameVlatest)
        await renameVlatest(executionMode, { applicationFolder: dir, repoRootFolder: options.renameVlatest });
    else if (options.run) {
        if (!options.prod && !options.dev && !options.record && !options.execute) {
            throw new Error("Either 'prod', 'dev', 'record' or 'execute' is required");
        }
        await runUpgrade(executionMode, { ...options, dir });
    } else if (options.hot) {
        await runHotUpgrade(executionMode, { ...options, dir });
    } else if (options.test) {
        await testUpgrade(executionMode, dir, {
            version: options.test,
            skipDbRestore: options.skipDbRestore,
            skipVacuum: options.skipVacuum,
            clusterName: options.clusterName,
        });
    } else {
        raiseErrorOnInvalidCommandLine();
    }
    if (executionMode === ExecutionMode.STANDALONE) process.exit();
};
