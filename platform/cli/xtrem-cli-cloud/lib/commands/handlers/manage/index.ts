import { cliContext, printWarning } from '@sage/xtrem-cli-lib';
import { tenant } from '../tenant';

export interface ManageOptions {
    initTenant: string;
    updateTenant: string;
    deleteTenant: string;
    layers?: string;
}

export const manage = (options: ManageOptions) => {
    const { executionMode } = cliContext;
    printWarning(executionMode, "'manage' command is deprecated, please use 'tenant' command instead");
    return tenant({
        init: options.initTenant,
        update: options.updateTenant,
        delete: options.deleteTenant,
        layers: options.layers,
    });
};
