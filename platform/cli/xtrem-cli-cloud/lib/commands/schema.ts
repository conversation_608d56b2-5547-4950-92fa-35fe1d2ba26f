import type { Argv } from 'yargs';
import { SchemaOptions } from './handlers/schema';

export const command = 'schema';
export const desc = 'Manage the application schema';

export const builder = (yargs: Argv) =>
    yargs
        .version(false)
        .option('create', {
            desc:
                'Create the SQL schema of the current application. ' +
                'This method will iterate over all the persistent nodes and create their tables, indexes and foreign keys',
            type: 'boolean',
        })
        .option('force', {
            desc: 'Force reset database if it already exists. It only applies to --create',
            type: 'boolean',
            deprecate: true,
            implies: ['create'],
        })
        .option('skip-tables', {
            desc: 'Only create the schema - do not create its tables It only applies to --create',
            type: 'boolean',
            deprecate: true,
            implies: ['create'],
        })
        .option('reset-database', {
            desc: 'Force reset database if it already exists. It only applies to --create',
            type: 'boolean',
            implies: ['create'],
        })
        .option('reset-schema', {
            desc: 'Force reset schema if it already exists. It only applies to --create',
            type: 'boolean',
            implies: ['create'],
        })
        .option('dump-to-s3', {
            desc: 'Dump the current schema to a S3 bucket',
            type: 'boolean',
        })
        .option('check', {
            desc: 'Check that the SQL schema is in sync with node definitions. Raise an error if not.',
            type: 'boolean',
        })
        .option('restore-from-s3', {
            desc:
                'Restore the given version of the current schema from a S3 bucket. ' +
                'If the version is omitted, the latest version will be restored. ' +
                'It is also possible to provide a full S3 URI of a dump to download.' +
                'version x.x.x: restore the x.x.x version that was built by the release-patch pipeline (from a brand new database)',
            type: 'string',
            implies: ['s3-config-type'],
        })
        .option('check-single-schema', {
            desc:
                'Will raise an error if, at the end of the restoration, there are more than 1 schemas in the ' +
                'database (could be related to a configuration mismatch). ' +
                'This flag is mainly intented to be used on CI.',
            type: 'boolean',
            implies: ['restore-from-s3'],
        })
        .option('skip-values-hash', {
            desc:
                'Will skip the re-compute of _valuesHash after the restoration of a backup.\n' +
                'WARNING: this could lead to invalid data.',
            type: 'boolean',
            implies: ['restore-from-s3'],
        })
        .option('s3-config-type', {
            desc:
                'Select S3 configuration type:\n' +
                '- forSqlFiles: database backups used for the generation of SQL files\n' +
                '- sdmo: backup of sdmo (ci), after the last successful upgrade\n' +
                '- sdmo_cu: backup of sdmo (cu) of from beginning of the month\n' +
                '- clusterDevRelease: backup of cluster-release (dev)\n' +
                '- clusterQaRelease: backup of cluster-qa-release\n' +
                '- glossary: backup of glossary, after the last successful upgrade\n' +
                '- showcase: backup of show-case, after the last successful upgrade\n' +
                '- shopfloor: backup of shopfloor (ci), after the last successful upgrade\n' +
                '- shopfloor_cu: backup of shopfloor (cu), after the last successful upgrade\n' +
                '- x3_connector: backup of x3-connector, after the last successful upgrade\n',
            choices: [
                'forSqlFiles',
                'sdmo_cu',
                'clusterCuBackup',
                'sdmo',
                'clusterCiBackup',
                'clusterDevRelease',
                'clusterQaRelease',
                'showcase',
                'glossary',
                'shopfloor',
                'shopfloor_cu',
                'x3_connector',
            ],
            default: 'forSqlFiles',
        })
        .option('list-s3-versions', {
            desc: 'List all the available backups that can be used to generated SQL files for the current application',
            type: 'boolean',
        })
        .option('reset', {
            desc:
                'Back up current application users, recreate SQL schema, initialize tenant data of tenant passed and then recreate users.\n' +
                'WARNING: this is a permanent command and must be used with great care, ' +
                'all data on your schema will be lost when running this command, only users will be recreated.',
            type: 'boolean',
        })
        .option('layers', {
            desc:
                'Layers to load the initial data of the tenant can be provided to the reset command. ' +
                "If no layers are provided, the 'setup' layer will be loaded by default. " +
                'If a list of layers are provided, it must be formatted with a comma separator and no space. ' +
                'For instance: --layers setup,demo-data1,demo-data2',
            type: 'string',
            implies: ['reset'],
        })
        .option('fix-column-order', {
            desc:
                'Special command to the correct the ordering of the columns in tables. ' +
                'Take care that a backup of the database is done prior to executing this command.',
            type: 'boolean',
        })
        .option('rename-to', {
            desc:
                'Rename the schema configured in the xtrem-config.yml file (or xtrem if not set) to the new name provided. ' +
                'This is a dangerous operation and should be used with caution.',
            type: 'string',
        })
        .option('rename-from', {
            desc:
                'Rename the provided schema to the one configured in the xtrem-config.yml file (or xtrem if not set). ' +
                'This is a dangerous operation and should be used with caution.',
            type: 'string',
        })
        .middleware((argv: any) => {
            // create internal aliases to be used by the command options
            argv.isCreate = argv.create;
            argv.isDumpToS3 = argv.dumpToS3;
            argv.isListS3Versions = argv.listS3Versions;
            argv.isRestoreFromS3 = argv.restoreFromS3;
            argv.isReset = argv.reset;
            argv.layersAsString = argv.layers;
            return argv;
        })
        .example('$0 schema --dump-to-s3', 'Dump the current schema to a S3 bucket');

export const handler = (argv: any) =>
    // Lazy loading of handlers to speed up the CLI loading
    // eslint-disable-next-line global-require
    require('./handlers/schema').schema(argv as SchemaOptions) as Promise<void>;
