import type { Argv } from 'yargs';
import { DataPatchOptions } from './handlers/data-patch';

export const command = 'data-patch';
export const desc = 'Manage the data patches';

export const builder = (yargs: Argv) =>
    yargs
        .version(false)
        .option('run', {
            desc: 'Apply all the needed data-patches.',
            type: 'boolean',
        })
        .option('run-post-process', {
            desc: 'TBA',
            type: 'boolean',
            implies: ['run'],
        })
        .option('test', {
            desc: 'Restore the given version of the current schema from a S3 bucket and apply the data patches.',
            type: 'boolean',
        })
        .option('skip-db-restore', {
            desc: 'Skip the db restore from S3',
            type: 'boolean',
            implies: ['test'],
        })
        .example('$0 data-patch --run', 'Apply all the needed data-patches.');

export const handler = (argv: any) =>
    // Lazy loading of handlers to speed up the CLI loading
    // eslint-disable-next-line global-require
    require('./handlers/data-patch').dataPatch(argv as DataPatchOptions) as Promise<void>;
