import type { Argv } from 'yargs';
import { UpgradeOptions } from './handlers/upgrade';

export const command = 'upgrade';
export const desc = 'Manage the upgrades';

export const builder = (yargs: Argv) =>
    yargs
        .version(false)
        .option('hot', {
            desc: 'Hot-upgrades the current application.',
            type: 'boolean',
        })
        .option('run', {
            desc: 'Upgrade all tables of the current application.',
            type: 'boolean',
        })
        .option('prod', {
            desc: 'Plays previously recorded SQL files (does not execute any upgrade action).',
            type: 'boolean',
            implies: ['run'],
        })
        .option('dev', {
            desc: 'Plays previously recorded SQL files and then run the upgrade actions (without recording)',
            type: 'boolean',
            implies: ['run'],
        })
        .option('record', {
            desc: 'Every SQL command executed by the upgrade will be recorded to a local file (CI use only)',
            type: 'boolean',
            implies: ['run'],
        })
        .option('execute', {
            desc: 'Upgrades without playing recorded SQL files',
            type: 'boolean',
            implies: ['run'],
        })
        .option('full-reload-of-setup-layer', {
            desc: 'Should all the CSV files from the SETUP layer be reloaded at the end of the upgrade?',
            type: 'boolean',
            implies: ['run'],
        })
        .option('cluster-name', {
            desc: 'The (optional) name of the cluster to upgrade',
            type: 'string',
        })
        .option('metrics', {
            desc:
                'Should a CSV metrics file be generated ?\n' +
                '- local: a CSV file will be generated locally\n' +
                '- s3: a CSV file will be generated locally and then uploaded to S3',
            type: 'string',
            choices: ['s3', 'local'],
        })
        .option('test', {
            desc: 'Restore the given version of the current schema from a S3 bucket and run the upgrade process.',
            type: 'boolean',
        })
        .option('force', {
            desc: 'Force the test of the upgrade, even if the versions seem to be up-to-date',
            type: 'boolean',
            implies: ['test'],
        })
        .option('skip-db-restore', {
            desc: 'Skip the db restore from S3',
            type: 'boolean',
            implies: ['test'],
        })
        .option('skip-vacuum', {
            desc: 'Skip the db vacuum',
            type: 'boolean',
        })
        .option('rename-vlatest', {
            desc: 'Rename the "vlatest" folders to the current version (from the provided folder)',
            type: 'string',
        })
        .example('$0 upgrade --run --dev', 'Upgrade all table of the current application without recording.');

export const handler = (argv: any) =>
    // Lazy loading of handlers to speed up the CLI loading
    // eslint-disable-next-line global-require
    require('./handlers/upgrade').upgrade(argv as UpgradeOptions) as Promise<void>;

// TODO: update help with the following:
// -------------------------------------
// upgrade
//            [--run (--dev|--prod|--record|--execute) [--install-dependencies]]
//            [--rename-vlatest rootFolder]
//            [--test [version] [--force] [--reset-on-fail] [--skip-db-restore] [--install-dependencies]]
//   Manage the upgrades
//       --run:               Upgrade all tables of the current application.
//                            This method will iterate over all the persistent nodes and create their tables,
//                            indexes and foreign keys. If any version moves backwards the operation will abort.
//       *  --prod            Plays previously recorded SQL files (does not execute any upgrade action).
//                            If the option upgradeOnly is set in xtrem-config.yml the option --execute will superseed --prod
//       *  --install-dependencies
//       *                    Install upgrade bundles dependencies before running the upgrade
//       --test:              Restore the given version of the current schema from a S3 bucket and run the upgrade process.
//       *  --reset-on-fail   (Only valid when called from CI): reset the database if the test failed.
//       *  --install-dependencies
//       *                    Install upgrade bundles dependencies before testing the upgrade
