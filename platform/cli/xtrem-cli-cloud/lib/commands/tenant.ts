import type { Argv } from 'yargs';
import { TenantOptions } from './handlers/tenant';

export const command = 'tenant';
export const desc = 'Manage a tenant';

export const builder = (yargs: Argv) =>
    yargs
        .version(false)
        .option('init', {
            desc: `Initialize a new tenant, loading all the default setup data and creating an admin user
                 of the current application for a given tenant Id. Data must be provided as a JSON object
                 encoded in base64. All data in the JSON object must be provided and will be validated.
                 Initialize a new tenant, loading all the default setup data and creating an admin user
                 of the current application for a given tenant Id. Data must be provided as a JSON object
                 encoded in base64. All data in the JSON object must be provided and will be validated.
                 Data has to contain:
                    * The tenant's id and name ,
                    * The customer's id and name the tenant belongs to,
                    * The administrator's email, firstname, lastname and locale, --deprecated, please use the adminUsers array instead.
                    * The list of administrators and their email, firstname, lastname and locale,
                    * The packages to activate. If empty all packages will be activated.
                If both adminUser and adminUsers are provided, the adminUsers will be used.

                    Data can also contain (optionals):
                    * A list of service options that have to be activated.
                    * skipWelcomeEmail: Skip welcome email to the admin user when the tenant is provioned.
                      default: false

                    These information have to be set as in the following example:
                    {
                        "customer": {
                            "id": "00000000000000000001",
                            "name": "acme"
                        },
                        "tenant": {
                            "id": "000000000000000000000",
                            "name": "dev"
                        },
                        "adminUser": {
                            "email": "<EMAIL>",
                            "firstName": "John",
                            "lastName": "Doe",
                            "locale": "en-US"
                        },
                        "adminUsers": [{
                            "email": "<EMAIL>",
                            "firstName": "John",
                            "lastName": "Doe",
                            "locale": "en-US"
                        },{
                            "email": "<EMAIL>",
                            "firstName": "Jane",
                            "lastName": "Doe",
                            "locale": "en-US"
                        }],
                        "serviceOptions": {
                            "showCaseDiscountOption": true,
                            "showCaseExperimentalOption": false,
                            "showCaseOptionHighLevel": true,
                            "showCaseWorkInProgressOption": false
                        },
                        "packages": ["@sage/xtrem-showcase"],
                        "skipWelcomeEmail":false
                    }'

                    Once encoded in base 64 the previous JSON becomes:
                    ********************************************************************************************************************************************************************************************************************************************************************************************************************
                    example:
                    xtrem tenant --init '********************************************************************************************************************************************************************************************************************************************************************************************************************'
            `,
            type: 'string',
        })
        .option('layers', {
            desc:
                'A list of layers to load the initial data of the tenant can be provided to the init command.' +
                'If no layer is provided, the setup layer will be loaded by default.' +
                'If a list of layers are provided, it must be formatted with a comma separator and no space.' +
                'For instance: --layers=setup,demo-data1,demo-data2',
            type: 'string',
            implies: ['init'],
        })
        .option('update', {
            desc: `Update an existing tenant. Data must be provided as a JSON object encoded in base64.
                There are two cases of updating a tenant:
                1. Updating the service options or the packages of the tenant.
                    All data in the JSON object must be provided and will be validated.
                    Data has to contain:
                            * The tenant's id and name,
                            * Its can also contain a list of service options that has to be activated or deactivated,
                            * New packages to be set activatable.

                    These information have to be set as in the following example:
                        {
                            "tenant": {
                                "id": "000000000000000000000"
                            },
                            "packages": ["@sage/xtrem-show-case"],
                            "serviceOptions": {
                                "showCaseDiscountOption": true,
                                "showCaseExperimentalOption": true,
                                "showCaseOptionHighLevel": true,
                                "showCaseWorkInProgressOption": false
                            }
                        }
                2. Adding the tenant admins: if an user does not exist, we will create an entry to the DB otherwise we update administrator settings accordingly.
                    All data in the JSON object must be provided and will be validated.
                    Data has to contain:
                        * The tenant's id,
                        * The list of administrators and their email, firstname, lastname and locale

                    These information have to be set as in the following example:
                        {
                            "tenant": {
                                "id": "000000000000000000000"
                            },
                            "adminUsers": [{
                                "email": "<EMAIL>",
                                "firstName": "John",
                                "lastName": "Doe",
                                "locale": "en-US"
                            }]
                        }
                    Once encoded in base 64 the previous JSON becomes:
                    ewogICAgICAgICAgICAgICAgICAgICAgICAidGVuYW50IjogewogICAgICAgICAgICAgICAgICAgICAgICAgICAgImlkIjogIjAwMDAwMDAwMDAwMDAwMDAwMDAwMCIKICAgICAgICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgICAgICAgInBhY2thZ2VzIjogWyJAc2FnZS94dHJlbS1zaG93LWNhc2UiXSwKICAgICAgICAgICAgICAgICAgICAgICAgInNlcnZpY2VPcHRpb25zIjogewogICAgICAgICAgICAgICAgICAgICAgICAgICAgInNob3dDYXNlRGlzY291bnRPcHRpb24iOiB0cnVlLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgInNob3dDYXNlRXhwZXJpbWVudGFsT3B0aW9uIjogdHJ1ZSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICJzaG93Q2FzZU9wdGlvbkhpZ2hMZXZlbCI6IHRydWUsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAic2hvd0Nhc2VXb3JrSW5Qcm9ncmVzc09wdGlvbiI6IGZhbHNlCiAgICAgICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgICB9

                    example:
                    xtrem tenant --update 'ewogICAgICAgICAgICAgICAgICAgICAgICAidGVuYW50IjogewogICAgICAgICAgICAgICAgICAgICAgICAgICAgImlkIjogIjAwMDAwMDAwMDAwMDAwMDAwMDAwMCIKICAgICAgICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgICAgICAgInBhY2thZ2VzIjogWyJAc2FnZS94dHJlbS1zaG93LWNhc2UiXSwKICAgICAgICAgICAgICAgICAgICAgICAgInNlcnZpY2VPcHRpb25zIjogewogICAgICAgICAgICAgICAgICAgICAgICAgICAgInNob3dDYXNlRGlzY291bnRPcHRpb24iOiB0cnVlLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgInNob3dDYXNlRXhwZXJpbWVudGFsT3B0aW9uIjogdHJ1ZSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICJzaG93Q2FzZU9wdGlvbkhpZ2hMZXZlbCI6IHRydWUsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAic2hvd0Nhc2VXb3JrSW5Qcm9ncmVzc09wdGlvbiI6IGZhbHNlCiAgICAgICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgICB9'
                Please note, these 2 cases are exclusive, you can't update the package or service options data and add admin users in the same command. If you need to do both, you will have to run 2 commands. If both tenant options and adminUsers are provided, only the adminUsers case will be executed.`,
            type: 'string',
        })
        .option('update-setup-data', {
            desc: 'Update the setup data for all the packages of the tenant.',
            type: 'boolean',
        })
        .option('cluster-name', {
            desc: 'The (optional) cluster name.',
            type: 'string',
            implies: ['update-setup-data'],
        })
        .option('force', {
            desc: 'TBD',
            type: 'boolean',
            implies: ['update-setup-data'],
        })
        .option('delete', {
            desc: 'Delete all tenant data of the current application for a given tenant Id',
            type: 'string',
        })
        .option('export', {
            desc:
                'Export data for all tenants matching a given <tenantSelector>.' +
                '<tenantSelector> can be a tenant id, directory name or directory name glob pattern.' +
                'It will generate 2 csv files per table, one for the non nullable data and one for nullable, then zip all of them' +
                'in data/exports/<tenant-directory-name>/<export-id | directory-name---vv.mm.pp--YYYY.MM.DD.hh.mm.ss>.zip file (vv.mm.pp is the application version)' +
                'The zip will be uploaded to a S3 bucket if a location is provided and the s3 config is set',
            type: 'string',
        })
        .option('export-id', {
            desc: 'An Id to identify this export. This id is used to generate a zip file with the name <export-id>.zip. This option cannot be set if we have multiple tenant exports.',
            type: 'string',
            implies: ['export'],
        })
        .option('location', {
            desc:
                'The location is in the form s3://<bucket-name>/[path-prefix]' +
                'If no path-prefix is provided, the default is to put the <export-id>.zip file on s3://<bucket-name>/exports/<tenant-id>',
            type: 'string',
        })
        .option('keep-all-values', {
            desc:
                'If specified, sensitive data flagged on properties with the exportValue decorator set, will export the actual value ' +
                'contained in the database. The default is not to export these and export the exportValue instead.',
            type: 'boolean',
            implies: ['export'],
        })
        .option('diff-compliant', {
            desc: 'TBD',
            type: 'boolean',
            implies: ['export'],
        })
        .option('timestamp', {
            desc: 'TBD',
            type: 'boolean',
            implies: ['export'],
        })
        .option('import', {
            desc: `Import all tenant data of a given tenantId from the location provided.
                    If the location is an S3 Uri, the zip file is downloaded, extracted and the extracted csv files are then imported.
                    If the --location is a path to a local zip, then similarly it is extracted and the extracted csv files are then imported.
                    If the import fails in the middle of an import the tenant must be deleted before attempting the import again.`,
            type: 'string',
        })
        .option('chunk-size', {
            desc: 'size of buffered records',
            type: 'number',
            implies: ['import'],
        })
        .option('execute-custom-sql', {
            desc: 'Execute a custom SQL script',
            type: 'boolean',
        })
        .option('tenants', {
            desc: 'List of tenants to process',
            type: 'array',
            implies: ['execute-custom-sql'],
            coerce: (options: string[]) => options[0].split(','),
        })
        .option('keep-files', {
            desc: 'Keep extracted files ?',
            type: 'boolean',
            implies: ['import'],
        })
        .option('dry-run', {
            desc: 'Perform a dry-run ?',
            type: 'boolean',
            implies: ['execute-custom-sql'],
        })
        .example([
            [
                "$0 tenant --init '************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************'",
                ' Init a new tenant',
            ],
            [
                "tenant --update '********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************'",
                'Update a tenant',
            ],
            ['$0 tenant --delete {id}', ' Detete the given tenant.'],
            [
                '$0 tenant --export "000000000000000000000" --export-id "my-id" --location s3://my-bucket-name',
                'Export tenant "000000000000000000000" to s3://my-bucket-name with the export id "my-id".',
            ],
            [
                '$0 tenant --export "000000000000000000000" --export-id "my-id" --location s3://my-bucket-name/my/specific/path',
                'Export tenant "000000000000000000000" to s3://my-bucket-name/my/specific/path with the export id "my-id".',
            ],
            [
                '$0 tenant --export "000000000000000000000" --keep-all-values',
                'Export tenant "000000000000000000000" with actual values even if the property is flagged sensitive.',
            ],
            [
                '$0 tenant --export "reference-*"  --location s3://my-bucket-name',
                'Export all the tenant directory names that begin with reference-',
            ],
            [
                '$0 tenant --import {id}',
                ` Import all tenant data of a given tenantId from the location provided.
              If the location is an S3 Uri, the zip file is downloaded, extracted and the extracted csv files are then imported.
              If the --location is a path to a local zip, then similarly it is extracted and the extracted csv files are then imported.
              If the import fails in the middle of an import the tenant must be deleted before attempting the import again.`,
            ],
            ["$0 tenant --execute-custom-sql --location=S3://.... --tenants='tenant1','tenant2'"],
            ["$0 tenant --execute-custom-sql --location=file://.... --tenants='tenant1','tenant2' --dry-run"],
        ]);

export const handler = (argv: any) =>
    // Lazy loading of handlers to speed up the CLI loading
    // eslint-disable-next-line global-require
    require('./handlers/tenant').tenant(argv as TenantOptions) as Promise<void>;
