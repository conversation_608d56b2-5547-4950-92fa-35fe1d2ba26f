import type { Argv } from 'yargs';
import { ManageOptions } from './handlers/manage';

export const command = 'manage';
export const desc = 'Manage the Xtrem application';

export const builder = (yargs: Argv) =>
    yargs
        .version(false)
        .option('init-tenant', {
            desc: 'Initialize a new tenant',
            type: 'string',
        })
        .option('layers', {
            desc: 'A list of layers to load the initial data of the tenant can be provided to the init command.',
            type: 'string',
            implies: ['init-tenant'],
        })
        .option('update-tenant', {
            desc: 'Update an existing tenant.',
            type: 'string',
        })
        .option('delete-tenant', {
            desc: 'Delete all tenant data of the current application for a given tenant Id',
            type: 'string',
        });

export const handler = (argv: any) =>
    // Lazy loading of handlers to speed up the CLI loading
    // eslint-disable-next-line global-require
    require('./handlers/manage').manage(argv as ManageOptions) as Promise<void>;
