import type { Argv } from 'yargs';
import { AnonymizeOptions } from './handlers/pg-anonymizer';

export const command = 'pg-anonymizer';
export const desc = 'Generate rules script for pg anonymizer';

export const builder = (yargs: Argv) =>
    yargs
        .version(false)
        .option('outputPath', {
            desc: 'outputPath for pg-anonymizer rules for xtrem DB',
            type: 'string',
        })
        .example([
            [
                '$0 pg-anonymizer --outputPath {path}',
                ' Export the rules files to path under name pg-anon-rules-v[version]}.sql.',
            ],
        ]);

export const handler = (argv: any) =>
    // Lazy loading of handlers to speed up the CLI loading
    // eslint-disable-next-line global-require
    require('./handlers/pg-anonymizer').pgAnonymizer(argv as AnonymizeOptions) as Promise<void>;
