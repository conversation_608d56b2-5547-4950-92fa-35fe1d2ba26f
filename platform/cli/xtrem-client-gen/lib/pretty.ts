import { EOL } from 'os';
import { Options as PrettierOptions } from 'prettier';
import * as ts from 'typescript';

type Prettier = { format: (source: string, options?: PrettierOptions) => Promise<string> };

const prettierConfig: PrettierOptions = {
    parser: 'typescript',
    singleQuote: true,
    printWidth: 120,
    tabWidth: 4,
    useTabs: false,
    semi: true,
    trailingComma: 'all',
    arrowParens: 'avoid',
};

export async function pretty(nodes: ts.Node | ts.Node[], path = '<none>'): Promise<string> {
    function formatNodes(node: ts.Node | ts.Node[]): string {
        if (Array.isArray(node)) return node.map(formatNodes).join('');
        const resultFile = ts.createSourceFile(path, '', ts.ScriptTarget.Latest, false, ts.ScriptKind.TS);
        const printer = ts.createPrinter({ newLine: ts.NewLineKind.LineFeed });
        return printer.printNode(ts.EmitHint.Unspecified, node, resultFile);
    }

    // Lazy load prettier to save 500ms when the app is started
    // eslint-disable-next-line global-require
    const prettier: Prettier = require('prettier');
    const source = (await prettier.format(formatNodes(nodes), prettierConfig))
        .replace(/(\/\*\* 4GL code: \w+ \*\/)\s*\1/g, '$1') // avoid stuttering on 4GL comments
        // eslint-disable-next-line @sage/redos/no-vulnerable
        .replace(/((?:\n *\/(?:\/|\*\*).*)*\n *@)/g, '\n$1'); // extra newline before decorators
    // Windows environment : the code is generated using '\n' as newlines. To avoid a lot of
    // non meaningfull git differences, we replace these '\n' with '\r\n'.
    // When pushing to git, git will store the text with '\n'
    return EOL === '\r\n' ? source.replace(/\n/g, '\r\n') : source;
}
