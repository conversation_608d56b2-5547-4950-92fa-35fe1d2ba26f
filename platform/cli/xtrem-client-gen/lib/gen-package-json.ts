import { Application, Dict } from '@sage/xtrem-core';
import { EOL } from 'os';
import * as path from 'path';

const safeRequirePackageFile = (packageName: string, dir: string) => {
    const dirParent = path.resolve(dir, '..');
    const dirParentParent = path.resolve(dirParent, '..');
    try {
        // eslint-disable-next-line global-require, import/no-dynamic-require
        return require(require.resolve(`${packageName}/package.json`, { paths: [dir, dirParent, dirParentParent] }));
    } catch (err) {
        return null;
    }
};

export function generateClientPackageJson(app: Application): string {
    const dependencies = {
        ...app.mainPackage.packageJson.peerDependencies,
        ...app.mainPackage.packageJson.dependencies,
    };
    const mainPackageApiDeps = app
        .getPackages()
        .filter(dep => dep.name !== app.name && dependencies[dep.name])
        .map(d => {
            // we need to get the version defined in the main package.json to allow spltting the monorepo
            // and get the right version that can be either 'workspace:*' or a specific semver if outside the monorepo
            return {
                apiDepPackageName: `${d.name}-api`,
                version: dependencies[d.name],
            };
        });

    const pack = {
        name: `${app.name}-api`,
        description: `Client API for ${app.name}`,
        version: app.mainPackage.packageJson.version,
        author: app.mainPackage.packageJson.author,
        license: app.mainPackage.packageJson.license,
        typings: 'api.d.ts',
        dependencies: [
            {
                apiDepPackageName: '@sage/xtrem-client',
                // when building outside of the monorepo we don't want the workspace:* version but the actual version
                version: dependencies['@sage/xtrem-client'] ?? dependencies['@sage/xtrem-core'],
            },
            ...mainPackageApiDeps,
        ]
            .sort((dep1, dep2) => {
                if (dep1.apiDepPackageName > dep2.apiDepPackageName) return 1;
                if (dep1.apiDepPackageName < dep2.apiDepPackageName) return -1;
                return 0;
            })
            .reduce((r, dep) => {
                r[dep.apiDepPackageName] = dep.version;
                return r;
            }, {} as Dict<string>),
    };
    return JSON.stringify(pack, null, 4).replace(/\n/g, EOL) + EOL;
}

export function generateClientPackageJsonFromPackage(dir: string): string {
    // eslint-disable-next-line global-require, import/no-extraneous-dependencies
    const xtremClientPackage = require('@sage/xtrem-client/package.json');
    const prefix = '^';

    // eslint-disable-next-line global-require, import/no-dynamic-require
    const xtremPackage = require(path.resolve(dir, 'package.json'));
    const dependencies = Object.keys(xtremPackage.dependencies)
        .map((d: string) => safeRequirePackageFile(d, dir))
        .filter(d => !!d && Boolean(d.xtrem) === true && d.name !== xtremPackage.name)
        .sort((dep1, dep2) => {
            if (dep1.name > dep2.name) return 1;
            if (dep1.name < dep2.name) return -1;
            return 0;
        })
        .reduce((prevValue: Dict<string>, d: any) => {
            return {
                ...prevValue,
                [`${d.name}-api`]: `${prefix}${d.version}`,
            };
        }, {} as Dict<string>);

    const pack = {
        name: `${xtremPackage.name}-api`,
        description: `Client API for ${xtremPackage.name}`,
        version: xtremPackage.version,
        author: xtremPackage.author,
        license: xtremPackage.license,
        typings: 'api.d.ts',
        dependencies: {
            '@sage/xtrem-client': `${prefix}${xtremClientPackage.version}`,
            ...dependencies,
        },
    };
    return JSON.stringify(pack, null, 4).replace(/\n/g, EOL) + EOL;
}
