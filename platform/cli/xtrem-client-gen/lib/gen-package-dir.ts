import { Application } from '@sage/xtrem-core';
import * as fs from 'fs';
import * as fsp from 'path';
import { generateClientApi } from './gen-api';
import { generateClientPackageJson, generateClientPackageJsonFromPackage } from './gen-package-json';

function createFile(path: string, contents: string) {
    // eslint-disable-next-line no-console
    console.log(`created ${path}`);
    fs.writeFileSync(path, contents, 'utf8');
}

function tryReadFile(path: string) {
    try {
        return fs.readFileSync(path, 'utf8');
    } catch (e) {
        if (e.code === 'ENOENT') {
            return '';
        }
        throw e;
    }
}

export async function generateClientPackage(application: Application) {
    const apiDir = fsp.join(application.dir, 'api');
    if (!fs.existsSync(apiDir)) fs.mkdirSync(apiDir);

    const apiSource = await generateClientApi(application);
    const apiSourcePath = fsp.join(apiDir, 'api.d.ts');
    const packageSource = generateClientPackageJson(application);
    const packageJsonPath = fsp.join(apiDir, 'package.json');
    const currentApiSource = tryReadFile(apiSourcePath);
    if (currentApiSource !== apiSource) {
        createFile(apiSourcePath, apiSource);
    } else {
        // eslint-disable-next-line no-console
        console.log(`skipped (no change) ${apiSourcePath}`);
    }
    const currentPackageSource = tryReadFile(packageJsonPath);
    if (currentPackageSource !== packageSource) {
        createFile(packageJsonPath, packageSource);
    } else {
        // eslint-disable-next-line no-console
        console.log(`skipped (no change) ${packageJsonPath}`);
    }
}

/**
 * This function can create the API package's package.json file without having the main package and its dependencies
 * built.
 * */
export function generateClientPackageJsonFromPackageJson(dir: string) {
    const apiDir = fsp.join(dir, 'api');
    if (!fs.existsSync(apiDir)) fs.mkdirSync(apiDir);

    const packageSource = generateClientPackageJsonFromPackage(dir);
    createFile(fsp.join(apiDir, 'package.json'), packageSource);
}
