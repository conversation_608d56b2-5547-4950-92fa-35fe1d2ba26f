import {
    AnyValue,
    Application,
    ArrayParameter,
    BaseParameter,
    BasePropertyDecorator,
    ConfigManager,
    DataType,
    Dict,
    EnumDataType,
    ExternalOperationDecorator,
    LogicError,
    Node,
    NodeFactory,
    ObjectParameter,
    Package,
    Parameter,
    ParameterTypeName,
    Property,
    ReferenceProperty,
    ReferencePropertyDecorator,
    ScalarParameter,
    TypeName,
    isPropertyInputOnly,
    isPropertyOutputOnly,
} from '@sage/xtrem-core';
import { camelCase, flatten, merge, uniq } from 'lodash';
import * as ts from 'typescript';
import { pretty } from './pretty';

const tsf = ts.factory;

function pascalCase(str: string) {
    const camel = camelCase(str);
    return camel[0].toUpperCase() + camel.substring(1);
}

function compareImports(s1: string, s2: string): number {
    // Sort in increasing ascii order rather than case-insensitive, to match what prettier does.
    // Cast to any to defeat sonar. This is the easiest (and fastest) way to get ascii order.
    return (s1 as any) < (s2 as any) ? -1 : 1;
}

type NodeClass = { new (): Node };

interface ExtensionInfo {
    name: string;
    type: 'node' | 'enum';
    hasQueries?: boolean;
    hasMutations?: boolean;
    hasLookups?: boolean;
    hasAsyncOperations?: boolean;
}

interface Member {
    name: string;
    isNullable?: boolean;
    isMandatory?: boolean;
    type: ParameterTypeName;
    targetFactory?: NodeFactory;
    isVital?: boolean;
    dataType?: DataType<AnyValue, unknown>;
    columnType?: TypeName;
    isTransientInput?: boolean;
    isVitalParentInput?: boolean;
}

enum NodeVariant {
    input,
    output,
    binding,
}

class NodeImport {
    constructor(
        public factory: NodeFactory,
        public variant: NodeVariant,
    ) {}
}

export class ApiGenerator {
    constructor(private readonly pack: Package) {}

    private get application(): Application {
        return this.pack.application;
    }

    private readonly libImports = [] as string[];

    private readonly nodeImports = [] as NodeImport[];

    private readonly enumImports = [] as string[];

    private nodeExtensions = {} as Dict<ExtensionInfo[]>;

    private enumExtensions = {} as Dict<ExtensionInfo[]>;

    private addLibImport(typeName: string) {
        if (!this.libImports.includes(typeName)) this.libImports.push(typeName);
        return typeName;
    }

    private libImportStatements(): ts.Statement[] {
        return this.libImports.length > 0 ? [ApiGenerator.importStatement(this.libImports, '@sage/xtrem-client')] : [];
    }

    private addNodeImport(factory: NodeFactory, variant: NodeVariant, transientOrVital: boolean = false) {
        const pushNodeImport = (nodeVariant: NodeVariant) => {
            if (!this.nodeImports.find(imp => imp.factory === factory && imp.variant === nodeVariant)) {
                this.nodeImports.push(new NodeImport(factory, nodeVariant));
            }
        };

        switch (variant) {
            case NodeVariant.input:
                pushNodeImport(variant);
                return `${factory.name}Input`;
            case NodeVariant.binding:
                if (!transientOrVital) {
                    pushNodeImport(NodeVariant.output);
                    return factory.name;
                }
                pushNodeImport(variant);
                return `${factory.name}Binding`;
            default:
                pushNodeImport(variant);
                return factory.name;
        }
    }

    private addEnumImport(name: string) {
        if (!this.enumImports.includes(name)) this.enumImports.push(name);
        return name;
    }

    private static trimApi(packageName: string) {
        if (!packageName.endsWith('-api')) throw new Error(`${packageName}: invalid api package name`);
        return packageName.replace(/-api$/, '');
    }

    private nodeImportsMap(): Dict<string[]> {
        const map = this.nodeImports.reduce(
            (r, imp) => {
                const pack = imp.factory.package;
                r[pack.name] = r[pack.name] || [];
                switch (imp.variant) {
                    case NodeVariant.input:
                        r[pack.name].push(`${imp.factory.name}Input`);
                        break;
                    case NodeVariant.binding:
                        r[pack.name].push(`${imp.factory.name}Binding`);
                        break;
                    default:
                        r[pack.name].push(imp.factory.name);
                        break;
                }
                return r;
            },
            {} as Dict<string[]>,
        );
        this.pack.application.mainPackage.apiDependencies
            .map(dep => ApiGenerator.trimApi(dep))
            // TODO: remove this reverse hack
            .reverse()
            .forEach(dep => {
                map[dep] = map[dep] || [];
                map[dep].push(`Package as ${ApiGenerator.packageAlias(dep)}`);
            });
        return map;
    }

    private nodeImportStatements(): ts.Statement[] {
        const imports = this.nodeImportsMap();
        return Object.keys(imports)
            .filter(from => from !== this.pack.name)
            .sort(compareImports)
            .map(from => {
                const names = imports[from];
                return ApiGenerator.importStatement(names, `${from}-api`);
            });
    }

    private addNodeExtensionModule(
        factory: NodeFactory,
        { name, hasQueries, hasMutations, hasLookups, hasAsyncOperations }: ExtensionInfo,
    ) {
        const extendedPack = factory.package;
        const nodes = this.nodeExtensions[extendedPack.name] || (this.nodeExtensions[extendedPack.name] = []);
        nodes.push({ name, type: 'node', hasQueries, hasMutations, hasLookups, hasAsyncOperations });
    }

    private addEnumExtensionModule(enumDataType: EnumDataType) {
        const { rootDataType } = enumDataType;
        if (!rootDataType) return;
        if (!ConfigManager.current.storage?.managedExternal)
            throw new Error('Enum extension is only allowed on external applications.');
        const extendedPack = rootDataType.packageName;
        const enumExtensions = this.enumExtensions[extendedPack] || (this.enumExtensions[extendedPack] = []);

        enumExtensions.push({
            name: rootDataType.enumName(),
            type: 'enum',
        });
    }

    // interface Xxx extends XxxExtension {}
    private static interfaceExtensionStatement(name: string, dollarSuffix: string) {
        return tsf.createInterfaceDeclaration(
            [tsf.createToken(ts.SyntaxKind.ExportKeyword)],
            `${name}${dollarSuffix}`,
            [],
            [
                tsf.createHeritageClause(ts.SyntaxKind.ExtendsKeyword, [
                    tsf.createExpressionWithTypeArguments(tsf.createIdentifier(`${name}Extension${dollarSuffix}`), []),
                ]),
            ],
            [],
        );
    }

    private graphExtensionStatements(): ts.Statement[] {
        return [
            ApiGenerator.importStatement(['GraphApi as GraphApiExtension'], `${this.pack.name}-api`),
            ApiGenerator.interfaceExtensionStatement('GraphApi', ''),
        ];
    }

    private graphExtensions(): ts.Statement[] {
        return this.application.mainPackage.apiDependencies
            .map(dep => ApiGenerator.trimApi(dep))
            .filter(dep => dep !== this.application.mainPackage.name)
            .sort(compareImports)
            .map(dep =>
                tsf.createModuleDeclaration(
                    [tsf.createToken(ts.SyntaxKind.DeclareKeyword)],
                    tsf.createStringLiteral(`${dep}-api-partial`),
                    tsf.createModuleBlock(this.graphExtensionStatements()),
                ),
            );
    }

    private extensionStatements(nodeExtensions: ExtensionInfo[], enumExtensions: ExtensionInfo[]) {
        const imports = flatten([
            ...nodeExtensions.map(info => {
                return [
                    `${info.name}Extension`,
                    `${info.name}BindingExtension`,
                    `${info.name}InputExtension`,
                    ...(info.hasLookups ? [`${info.name}Extension$Lookups`] : []),
                    ...(info.hasQueries ? [`${info.name}Extension$Queries`] : []),
                    ...(info.hasMutations ? [`${info.name}Extension$Mutations`] : []),
                    ...(info.hasAsyncOperations ? [`${info.name}Extension$AsyncOperations`] : []),
                    ...(info.hasQueries || info.hasMutations || info.hasLookups || info.hasAsyncOperations
                        ? [`${info.name}Extension$Operations`]
                        : []),
                ];
            }),
            ...enumExtensions.map(info => {
                return [`${info.name}$EnumExtension`];
            }),
        ]);

        const interfaceExtensions = flatten([
            ...nodeExtensions.map(info => {
                return [
                    ApiGenerator.interfaceExtensionStatement(info.name, ''),
                    ApiGenerator.interfaceExtensionStatement(`${info.name}Binding`, ''),
                    ApiGenerator.interfaceExtensionStatement(`${info.name}Input`, ''),
                    ...(info.hasLookups ? [ApiGenerator.interfaceExtensionStatement(info.name, '$Lookups')] : []),
                    ...(info.hasQueries ? [ApiGenerator.interfaceExtensionStatement(info.name, '$Queries')] : []),
                    ...(info.hasMutations ? [ApiGenerator.interfaceExtensionStatement(info.name, '$Mutations')] : []),
                    ...(info.hasAsyncOperations
                        ? [ApiGenerator.interfaceExtensionStatement(info.name, '$AsyncOperations')]
                        : []),
                    ...(info.hasQueries || info.hasMutations || info.hasLookups || info.hasAsyncOperations
                        ? [ApiGenerator.interfaceExtensionStatement(info.name, '$Operations')]
                        : []),
                ];
            }),
            ...enumExtensions.map(info => {
                return [ApiGenerator.interfaceExtensionStatement(`${info.name}$Enum`, '')];
            }),
        ]);

        return [ApiGenerator.importStatement(imports, `${this.pack.name}-api`), ...interfaceExtensions];
    }

    private moduleExtensionStatement(
        from: string,
        nodeExtensions: ExtensionInfo[],
        enumExtensions: ExtensionInfo[],
    ): ts.Statement {
        return tsf.createModuleDeclaration(
            [tsf.createToken(ts.SyntaxKind.DeclareKeyword)],
            tsf.createStringLiteral(`${from}-api-partial`),
            tsf.createModuleBlock(this.extensionStatements(nodeExtensions, enumExtensions)),
        );
    }

    private moduleExtensionsStatements(): ts.Statement[] {
        const imports = uniq(merge(Object.keys(this.nodeExtensions), Object.keys(this.enumExtensions)));
        return imports
            .filter(from => from !== this.pack.name)
            .map(from =>
                this.moduleExtensionStatement(from, this.nodeExtensions[from] || [], this.enumExtensions[from] || []),
            );
    }

    private static importStatement(names: string[], from: string) {
        return tsf.createImportDeclaration(
            [],
            tsf.createImportClause(
                true,
                undefined,
                tsf.createNamedImports(
                    [...names]
                        .sort(compareImports)
                        .map(name => tsf.createImportSpecifier(false, undefined, tsf.createIdentifier(name))),
                ),
            ),
            tsf.createStringLiteral(from),
        );
    }

    private parameterAsProperty(name: string, parameter: Parameter | TypeName) {
        if (typeof parameter === 'string') return { name, type: parameter } as BaseParameter;
        const nodeFn = ApiGenerator.getNodeFn(parameter);
        return {
            ...parameter,
            targetFactory: nodeFn ? this.application.getFactoryByConstructor(nodeFn()) : undefined,
        };
    }

    private createObjectTypeNode(parameter: ObjectParameter & { name: string }, variant: NodeVariant): ts.TypeNode {
        const members = Object.keys(parameter.properties).map(name => {
            const property = this.parameterAsProperty(name, parameter.properties[name]);

            const nodeFn = ApiGenerator.getNodeFn(property);
            const targetFactory = nodeFn ? this.application.getFactoryByConstructor(nodeFn()) : undefined;

            const dataType = (property as ScalarParameter).dataType;
            return tsf.createPropertySignature(
                [],
                name,
                // optional members are only valid in input types
                property.isMandatory || variant === NodeVariant.output
                    ? undefined
                    : tsf.createToken(ts.SyntaxKind.QuestionToken),
                this.memberTypeNode(
                    {
                        ...property,
                        name,
                        targetFactory,
                        columnType:
                            property.type === 'reference' && targetFactory?.storage !== 'external'
                                ? 'integer'
                                : (property as Property).columnType,
                        dataType: dataType ? dataType() : undefined,
                    },
                    variant,
                ),
            );
        });

        return tsf.createTypeLiteralNode(members);
    }

    private createArrayTypeNode(parameter: ArrayParameter & { name: string }, variant: NodeVariant): ts.TypeNode {
        let itemParameter = parameter.item as Parameter;
        if (itemParameter && typeof itemParameter === 'object') {
            const dataType = (itemParameter as ScalarParameter).dataType;
            itemParameter = {
                ...itemParameter,
                dataType: dataType ? dataType() : undefined,
            } as unknown as Parameter;
        }

        const property = this.parameterAsProperty(parameter.name, itemParameter);
        // multi-level items might not have a name specified on lower levels, so we propagate the parent level name
        // to the lower level
        if (!property.name) property.name = parameter.name;

        return tsf.createArrayTypeNode(this.memberTypeNode(property as Member, variant));
    }

    private memberTypeNode(member: Member, variant: NodeVariant): ts.TypeNode {
        // handle nullable by wrapping and recursing with isNullable set to false.
        if (member.isNullable)
            return ApiGenerator.nullableType(this.memberTypeNode({ ...member, isNullable: false }, variant), true);

        const createStringType = () => tsf.createTypeReferenceNode('string', []);
        const createIntegerType = () => tsf.createTypeReferenceNode(this.addLibImport('integer'), []);
        const transientOrVital = member.isVital || member.isTransientInput;
        switch (member.type) {
            case 'boolean':
                return variant === NodeVariant.input
                    ? tsf.createUnionTypeNode([tsf.createTypeReferenceNode('boolean', []), createStringType()])
                    : tsf.createTypeReferenceNode('boolean', []);
            case 'integer':
            case 'short':
                return variant === NodeVariant.input
                    ? tsf.createUnionTypeNode([createIntegerType(), createStringType()])
                    : createIntegerType();
            case 'integerArray': {
                return tsf.createArrayTypeNode(this.memberTypeNode({ ...member, type: 'integer' }, variant));
            }
            case 'enum': {
                const dataType = member.dataType as EnumDataType;
                return tsf.createTypeReferenceNode(this.addEnumImport(`${dataType.enumName()}`));
            }
            case 'enumArray': {
                return tsf.createArrayTypeNode(this.memberTypeNode({ ...member, type: 'enum' }, variant));
            }
            case 'stringArray': {
                return tsf.createArrayTypeNode(this.memberTypeNode({ ...member, type: 'string' }, variant));
            }
            case 'decimal': {
                if (variant === NodeVariant.input) {
                    const decimalType = tsf.createTypeReferenceNode(this.addLibImport('decimal'), []);
                    return tsf.createUnionTypeNode([decimalType, createStringType()]);
                }
                return createStringType();
            }
            case 'float':
            case 'double': {
                const floatType = tsf.createTypeReferenceNode('number', []);
                return variant === NodeVariant.input
                    ? tsf.createUnionTypeNode([floatType, createStringType()])
                    : floatType;
            }
            case 'json': {
                return variant === NodeVariant.binding
                    ? tsf.createTypeReferenceNode('any', [])
                    : tsf.createTypeReferenceNode('string', []);
            }
            case 'date':
            case 'dateRange':
            case 'datetimeRange':
            case 'integerRange':
            case 'decimalRange':
            case 'time':
            case 'datetime':
            case 'string':
                return tsf.createTypeReferenceNode('string', []);
            case 'binaryStream':
                return tsf.createTypeReferenceNode(this.addLibImport('BinaryStream'), []);
            case 'textStream':
                return tsf.createTypeReferenceNode(this.addLibImport('TextStream'), []);
            case 'uuid':
                return tsf.createTypeReferenceNode('string', []);
            case 'jsonReference': // to fix
            case 'reference': {
                const targetFactory = member.targetFactory;
                if (!targetFactory) throw new LogicError('no factory');
                if (member.isVitalParentInput && targetFactory) {
                    return tsf.createTypeReferenceNode(this.addNodeImport(targetFactory, variant), []);
                }
                if (variant === NodeVariant.input && !member.isVital) {
                    return this.memberTypeNode({ ...member, type: member.columnType || 'string' }, variant);
                }
                return tsf.createTypeReferenceNode(this.addNodeImport(targetFactory, variant, transientOrVital), []);
            }
            case 'referenceArray': {
                const targetFactory = member.targetFactory;
                return tsf.createArrayTypeNode(
                    this.memberTypeNode(
                        {
                            ...member,
                            targetFactory,
                            type: 'reference',
                            columnType: targetFactory?.storage !== 'external' ? 'integer' : member.columnType,
                        },
                        variant,
                    ),
                );
            }
            case 'collection': {
                const targetFactory = member.targetFactory;
                if (!targetFactory) throw new LogicError('no target factory');

                if (variant === NodeVariant.input) {
                    if (!targetFactory) throw new LogicError('no target factory');
                    return tsf.createArrayTypeNode(
                        tsf.createTypeReferenceNode('Partial', [
                            tsf.createTypeReferenceNode(this.addNodeImport(targetFactory, variant), []),
                        ]),
                    );
                }

                return tsf.createTypeReferenceNode(this.addLibImport('ClientCollection'), [
                    tsf.createTypeReferenceNode(this.addNodeImport(targetFactory, variant, transientOrVital), []),
                ]);
            }
            case 'object': {
                return this.createObjectTypeNode(member as ObjectParameter & { name: string }, variant);
            }
            case 'array': {
                return this.createArrayTypeNode(member as ArrayParameter & { name: string }, variant);
            }
            case 'instance': {
                const targetFactory = member.targetFactory;
                if (!targetFactory) throw new LogicError('no target factory');
                return tsf.createTypeReferenceNode(this.addNodeImport(targetFactory, variant, transientOrVital), []);
            }

            default:
                throw new Error(`${member.name}: invalid type: ${member.type}`);
        }
    }

    private static getNodeFn(property: any): (() => NodeClass) | undefined {
        // If the property is a Property instance, property.node is not callable.
        // In that case, the callable node attribute is accessible in the Property's decorator.
        return property.decorator ? property.decorator.node : property.node;
    }

    private nodePropertiesSignatures(factory: NodeFactory, variant: NodeVariant): ts.TypeElement[] {
        const properties = factory.properties ? factory.properties.filter(prop => prop.isPublished) : [];

        return properties
            .filter(
                p =>
                    ![
                        '_id',
                        '_tenantId',
                        '_sourceId',
                        '_sortValue',
                        '_action',
                        '_etag',
                        '_customData',
                        '_updateTick',
                    ].includes(p.name),
            )
            .filter(property => {
                // exclude references to unpublished nodes from client api
                if (property.isForeignNodeProperty() && !property.targetFactory.isPublished) {
                    // eslint-disable-next-line no-console
                    console.warn(
                        `${factory.name}.${property.name}: ignored because ${property.targetFactory.name} is not published`,
                    );
                    return false;
                }
                if (variant === NodeVariant.input) {
                    if (['_createUser', '_updateUser'].includes(property.name)) return false;
                    if (isPropertyOutputOnly(property)) return false;
                    if (property.isReferenceProperty() && property.isVitalParent && !property.isVitalParentInput)
                        return false;
                }
                if (variant === NodeVariant.output && isPropertyInputOnly(property)) return false;
                return true;
            })
            .map(property => {
                const isOptional = variant === NodeVariant.input;
                const isAnyCollection =
                    property.isCollectionProperty() &&
                    property.factory.isAbstract &&
                    variant === NodeVariant.output &&
                    property.targetFactory.isAbstract;
                const targetFactory = property.isForeignNodeProperty() ? property.targetFactory : undefined;
                const isVital = property.isForeignNodeProperty() ? property.isVital : false;
                const isVitalParentInput =
                    property.isReferenceProperty() && property.isVitalParent ? property.isVitalParentInput : false;
                const propertyName = isAnyCollection ? `any${pascalCase(property.name)}` : property.name;
                return tsf.createPropertySignature(
                    [],
                    propertyName,
                    isOptional ? tsf.createToken(ts.SyntaxKind.QuestionToken) : undefined,
                    this.memberTypeNode(
                        {
                            name: property.name,
                            type: property.type,
                            targetFactory,
                            isVital,
                            isVitalParentInput,
                            columnType:
                                property.type === 'reference' && targetFactory?.storage !== 'external'
                                    ? 'integer'
                                    : property.columnType,
                            dataType: property.dataType!,
                            isTransientInput: (property as BasePropertyDecorator).isTransientInput,
                        },
                        variant,
                    ),
                );
            });
    }

    private static createExtendsClause(name: string) {
        return tsf.createHeritageClause(ts.SyntaxKind.ExtendsKeyword, [
            tsf.createExpressionWithTypeArguments(tsf.createIdentifier(name), []),
        ]);
    }

    private addNodeDeclaration(factory: NodeFactory, statements: ts.Statement[]) {
        statements.push(
            tsf.createInterfaceDeclaration(
                [tsf.createToken(ts.SyntaxKind.ExportKeyword)],
                factory.name,
                [],
                [
                    ApiGenerator.createExtendsClause(
                        this.addLibImport(factory.isVitalChild ? 'VitalClientNode' : 'ClientNode'),
                    ),
                ],
                this.nodePropertiesSignatures(factory, NodeVariant.output),
            ),
        );
    }

    private addNodeInputDeclaration(factory: NodeFactory, statements: ts.Statement[]) {
        statements.push(
            tsf.createInterfaceDeclaration(
                [tsf.createToken(ts.SyntaxKind.ExportKeyword)],
                `${factory.name}Input`,
                [],
                [
                    ApiGenerator.createExtendsClause(
                        this.addLibImport(factory.isVitalChild ? 'VitalClientNodeInput' : 'ClientNodeInput'),
                    ),
                ],
                this.nodePropertiesSignatures(factory, NodeVariant.input),
            ),
        );
    }

    private addBindingNodeDeclaration(factory: NodeFactory, statements: ts.Statement[]) {
        statements.push(
            tsf.createInterfaceDeclaration(
                [tsf.createToken(ts.SyntaxKind.ExportKeyword)],
                `${factory.name}Binding`,
                [],
                [
                    ApiGenerator.createExtendsClause(
                        this.addLibImport(factory.isVitalChild ? 'VitalClientNode' : 'ClientNode'),
                    ),
                ],
                this.nodePropertiesSignatures(factory, NodeVariant.binding),
            ),
        );
    }

    private addNodeExtensionDeclaration(factory: NodeFactory, statements: ts.Statement[]) {
        statements.push(
            tsf.createInterfaceDeclaration(
                [tsf.createToken(ts.SyntaxKind.ExportKeyword)],
                `${factory.name}Extension`,
                [],
                [],
                this.nodePropertiesSignatures(factory, NodeVariant.output),
            ),
        );
    }

    private static addEnumExtensionDeclaration(enumDataType: EnumDataType, statements: ts.Statement[]) {
        const members = ApiGenerator.getEnumPropertySignatures(enumDataType);
        statements.push(
            tsf.createInterfaceDeclaration(
                [tsf.createToken(ts.SyntaxKind.ExportKeyword)],
                `${enumDataType.rootDataType?.enumName() ?? enumDataType.enumName()}$EnumExtension`,
                [],
                [],
                members,
            ),
        );
    }

    private addNodeInputExtensionDeclaration(factory: NodeFactory, statements: ts.Statement[]) {
        statements.push(
            tsf.createInterfaceDeclaration(
                [tsf.createToken(ts.SyntaxKind.ExportKeyword)],
                `${factory.name}InputExtension`,
                [],
                [],
                this.nodePropertiesSignatures(factory, NodeVariant.input),
            ),
        );
    }

    private addNodeBindingExtensionDeclaration(factory: NodeFactory, statements: ts.Statement[]) {
        statements.push(
            tsf.createInterfaceDeclaration(
                [tsf.createToken(ts.SyntaxKind.ExportKeyword)],
                `${factory.name}BindingExtension`,
                [],
                [],
                this.nodePropertiesSignatures(factory, NodeVariant.binding),
            ),
        );
    }

    private parameterType(name: string, param: TypeName | Parameter, variant: NodeVariant): ts.TypeNode {
        if (typeof param === 'string') return tsf.createTypeReferenceNode(param, []);
        switch (param.type) {
            case 'instance':
                return ApiGenerator.nullableType(
                    tsf.createTypeReferenceNode(
                        this.addNodeImport(this.application.getFactoryByConstructor(param.node()), variant),
                        [],
                    ),
                    !!param.isNullable,
                );
            case 'reference':
                if (variant === NodeVariant.input) {
                    return ApiGenerator.nullableType(tsf.createTypeReferenceNode('string', []), !!param.isNullable);
                }
                return ApiGenerator.nullableType(
                    tsf.createTypeReferenceNode(
                        this.addNodeImport(this.application.getFactoryByConstructor(param.node()), variant),
                        [],
                    ),
                    !!param.isNullable,
                );
            default: {
                const dataType = (param as ScalarParameter).dataType;
                return this.memberTypeNode({ ...param, dataType: dataType ? dataType() : undefined, name }, variant);
            }
        }
    }

    private static nullableType(typeNode: ts.TypeNode, isNullable: boolean): ts.TypeNode {
        return isNullable ? tsf.createUnionTypeNode([typeNode, tsf.createTypeReferenceNode('null')]) : typeNode;
    }

    private operationParametersType(operation: ExternalOperationDecorator) {
        return tsf.createTypeLiteralNode(
            operation.parameters.map(parameter => {
                return tsf.createPropertySignature(
                    [],
                    parameter.name,
                    parameter.isMandatory ? undefined : tsf.createToken(ts.SyntaxKind.QuestionToken),
                    this.parameterType(parameter.name, parameter, NodeVariant.input),
                );
            }),
        );
    }

    private operationTypeAst(operation: ExternalOperationDecorator) {
        const operationAlias = 'Node$Operation';
        this.addLibImport(`Operation as ${operationAlias}`);
        return tsf.createTypeReferenceNode(operationAlias, [
            this.operationParametersType(operation),
            this.parameterType('return', operation.return, NodeVariant.output),
        ]);
    }

    private operationSignatures(operations: ExternalOperationDecorator[] | undefined) {
        if (!(operations && operations.length > 0)) return [];
        return operations.map(operation => {
            return tsf.createPropertySignature([], operation.name, undefined, this.operationTypeAst(operation));
        });
    }

    private asyncOperationTypeAst(start: ExternalOperationDecorator, track: ExternalOperationDecorator) {
        this.addLibImport('AsyncOperation');
        const returnType = (track.return as ObjectParameter).properties.result as Parameter;
        return tsf.createTypeReferenceNode('AsyncOperation', [
            this.operationParametersType(start),
            this.parameterType('return', returnType, NodeVariant.output),
        ]);
    }

    private asyncOperationSignatures(
        operationPairs: { start: ExternalOperationDecorator; track: ExternalOperationDecorator }[] | undefined,
    ) {
        if (!(operationPairs && operationPairs.length > 0)) return [];
        return operationPairs.map(operationPair => {
            return tsf.createPropertySignature(
                [],
                operationPair.start.name,
                undefined,
                this.asyncOperationTypeAst(operationPair.start, operationPair.track),
            );
        });
    }

    private builtinAggregateOperations(nodeName: string) {
        return tsf.createPropertySignature(
            [],
            'aggregate',
            undefined,
            tsf.createTypeLiteralNode([
                this.builtinOperationSignature('read', 'AggregateReadOperation', [nodeName]),
                this.builtinOperationSignature('query', 'AggregateQueryOperation', [nodeName]),
            ]),
        );
    }

    private operationsDeclaration(
        factory: NodeFactory,
        hasQueries: boolean,
        hasMutations: boolean,
        hasAsyncOperations: boolean,
        hasLookups: boolean,
        suffix: string,
    ) {
        const signatures = [] as (ts.PropertySignature | ts.MethodSignature)[];
        const canRead = factory.canRead && !suffix; // for now, omit in extensions
        if (canRead) {
            signatures.push(this.builtinOperationSignature('query', 'QueryOperation', [factory.name]));
            signatures.push(this.builtinOperationSignature('read', 'ReadOperation', [factory.name]));
            signatures.push(this.builtinAggregateOperations(factory.name));
        }
        if (hasQueries) {
            signatures.push(
                tsf.createPropertySignature(
                    [],
                    'queries',
                    undefined,
                    tsf.createTypeReferenceNode(`${factory.name}${suffix}$Queries`, []),
                ),
            );
        }
        const canCreate = factory.canCreate && !suffix; // for now, omit in extensions
        const canDuplicate = factory.canBeDuplicated && canCreate;
        if (canCreate) {
            signatures.push(
                this.builtinOperationSignature('create', 'CreateOperation', [`${factory.name}Input`, factory.name]),
            );
            signatures.push(this.builtinOperationSignature('getDuplicate', 'GetDuplicateOperation', [factory.name]));
            if (canDuplicate) {
                signatures.push(
                    this.builtinOperationSignature('duplicate', 'DuplicateOperation', [
                        'string',
                        `${factory.name}Input`,
                        factory.name,
                    ]),
                );
            }
        }
        const canUpdate = factory.canUpdate && !suffix; // for now, omit in extensions
        if (canUpdate) {
            signatures.push(
                this.builtinOperationSignature('update', 'UpdateOperation', [`${factory.name}Input`, factory.name]),
            );

            signatures.push(
                this.builtinOperationSignature('updateById', 'UpdateByIdOperation', [
                    `${factory.name}Input`,
                    factory.name,
                ]),
            );
        }
        const canDelete = factory.canDelete && !suffix; // for now, omit in extensions
        if (canDelete) {
            signatures.push(this.builtinOperationSignature('delete', 'DeleteOperation', ['{}']));
            signatures.push(this.builtinOperationSignature('deleteById', 'DeleteOperation', ['string']));
        }
        if (hasMutations) {
            signatures.push(
                tsf.createPropertySignature(
                    [],
                    'mutations',
                    undefined,
                    tsf.createTypeReferenceNode(`${factory.name}${suffix}$Mutations`, []),
                ),
            );
        }
        if (hasAsyncOperations) {
            signatures.push(
                tsf.createPropertySignature(
                    [],
                    'asyncOperations',
                    undefined,
                    tsf.createTypeReferenceNode(`${factory.name}${suffix}$AsyncOperations`, []),
                ),
            );
        }
        if (hasLookups) {
            const dataParameterType = `string | { data: ${factory.name}Input  }`;
            const typeReferenceNode = tsf.createTypeReferenceNode(dataParameterType, []);
            signatures.push(
                tsf.createMethodSignature(
                    [],
                    'lookups',
                    undefined,
                    [],
                    [
                        tsf.createParameterDeclaration(
                            undefined,
                            undefined,
                            tsf.createIdentifier('dataOrId'),
                            undefined,
                            typeReferenceNode,
                        ),
                    ],
                    tsf.createTypeReferenceNode(`${factory.name}${suffix}$Lookups`, []),
                ),
            );
            if (suffix) this.addNodeImport(factory, NodeVariant.input);
        }

        if (hasQueries || hasMutations || canCreate || canRead || canUpdate) {
            signatures.push(this.builtinOperationSignature('getDefaults', 'GetDefaultsOperation', [factory.name]));
        }

        return tsf.createInterfaceDeclaration(
            [tsf.createToken(ts.SyntaxKind.ExportKeyword)],
            `${factory.name}${suffix}$Operations`,
            [],
            [],
            signatures,
        );
    }

    private builtinOperationSignature(operationName: string, typeName: string, parameterTypeNames: string[]) {
        return tsf.createPropertySignature(
            [],
            operationName,
            undefined,
            tsf.createTypeReferenceNode(
                this.addLibImport(typeName),
                parameterTypeNames.map(parameterTypeName => tsf.createTypeReferenceNode(parameterTypeName, [])),
            ),
        );
    }

    private addQueriesDeclaration(factory: NodeFactory, statements: ts.Statement[], suffix: string) {
        const querySignatures = this.operationSignatures(
            factory.queries
                ? factory.queries.filter(
                      query => query.isPublished && query.definingPackage === this.pack && !query.action,
                  )
                : [],
        );

        if (querySignatures.length === 0) return false;

        statements.push(
            tsf.createInterfaceDeclaration(
                [tsf.createToken(ts.SyntaxKind.ExportKeyword)],
                `${factory.name}${suffix}$Queries`,
                [],
                [],
                querySignatures,
            ),
        );
        return true;
    }

    private addMutationsDeclaration(factory: NodeFactory, statements: ts.Statement[], suffix: string) {
        const mutationsSignatures = this.operationSignatures(
            factory.mutations
                ? factory.mutations.filter(
                      mutation => mutation.isPublished && mutation.definingPackage === this.pack && !mutation.action,
                  )
                : [],
        );

        if (mutationsSignatures.length === 0) return false;

        statements.push(
            tsf.createInterfaceDeclaration(
                [tsf.createToken(ts.SyntaxKind.ExportKeyword)],
                `${factory.name}${suffix}$Mutations`,
                [],
                [],
                mutationsSignatures,
            ),
        );
        return true;
    }

    private addAsyncOperationsDeclaration(factory: NodeFactory, statements: ts.Statement[], suffix: string) {
        const startMutations = factory.mutations
            ? factory.mutations.filter(
                  mutation =>
                      mutation.isPublished && mutation.definingPackage === this.pack && mutation.action === 'start',
              )
            : [];

        if (startMutations.length === 0) return false;

        const operationPairs = startMutations.map(startMutation => {
            const trackMutation = factory.queries?.find(
                mutation =>
                    mutation.isPublished &&
                    mutation.definingPackage === this.pack &&
                    mutation.action === 'track' &&
                    mutation.name === startMutation.name,
            );
            if (!trackMutation) throw new LogicError(`No track query found for ${startMutation.name}`);
            return {
                start: startMutation,
                track: trackMutation,
            };
        });

        const signatures = this.asyncOperationSignatures(operationPairs);

        statements.push(
            tsf.createInterfaceDeclaration(
                [tsf.createToken(ts.SyntaxKind.ExportKeyword)],
                `${factory.name}${suffix}$AsyncOperations`,
                [],
                [],
                signatures,
            ),
        );
        return true;
    }

    private addLookupsDeclaration(factory: NodeFactory, statements: ts.Statement[], suffix: string) {
        const lookupsSignatures =
            factory.properties &&
            factory.properties
                .filter(
                    property =>
                        property.isPublished &&
                        property.definingPackage === this.pack &&
                        property.name &&
                        property.isReferenceProperty() &&
                        property.canLookup &&
                        !property.isVital &&
                        !property.isVitalParent,
                )
                .map(property => {
                    const decorator = (property as Property as ReferenceProperty)
                        .decorator as ReferencePropertyDecorator;
                    const referenceNode = decorator
                        ? decorator.node().name
                        : (property as unknown as ReferencePropertyDecorator).node().name;
                    return this.builtinOperationSignature(property.name, 'QueryOperation', [referenceNode]);
                });

        if (lookupsSignatures && lookupsSignatures.length) {
            statements.push(
                tsf.createInterfaceDeclaration(
                    [tsf.createToken(ts.SyntaxKind.ExportKeyword)],
                    `${factory.name}${suffix}$Lookups`,
                    [],
                    [],
                    lookupsSignatures,
                ),
            );
            return true;
        }
        return false;
    }

    private apiSignatures(apiNodes: NodeFactory[]): ts.TypeElement[] {
        return apiNodes.map(node => {
            return tsf.createPropertySignature(
                [],
                tsf.createStringLiteral(`${this.pack.name}/${pascalCase(node.name)}`),
                undefined,
                tsf.createTypeReferenceNode(`${node.name}$Operations`, []),
            );
        });
    }

    private static packageAlias(packageName: string) {
        return `${pascalCase(packageName)}$Package`;
    }

    private packageDeclaration(apiNodes: NodeFactory[], apiPublishedNoOperations?: NodeFactory[]) {
        return tsf.createInterfaceDeclaration(
            [tsf.createToken(ts.SyntaxKind.ExportKeyword)],
            'Package',
            [],
            [],
            [
                ...this.apiSignatures(apiNodes),
                ...(apiPublishedNoOperations?.length ? this.apiSignaturesNoOperations(apiPublishedNoOperations) : []),
            ],
        );
    }

    private apiSignaturesNoOperations(apiNodes: NodeFactory[]): ts.TypeElement[] {
        return apiNodes.map(node => {
            return tsf.createPropertySignature(
                [],
                tsf.createStringLiteral(`${this.pack.name}/${pascalCase(node.name)}`),
                undefined,
                tsf.createTypeReferenceNode(`${node.name}`, []),
            );
        });
    }

    private graphDeclaration() {
        const apiDependencies = this.application.mainPackage.apiDependencies;
        const extendsExpression = apiDependencies
            .map(dep => ApiGenerator.trimApi(dep))
            .filter(dep => dep !== this.application.mainPackage.name)
            .map(dep => ApiGenerator.packageAlias(dep))
            .sort(compareImports)
            .map(packageTypeName => {
                return tsf.createExpressionWithTypeArguments(tsf.createIdentifier(packageTypeName), []);
            });

        // add Package to the top of the list
        extendsExpression.unshift(tsf.createExpressionWithTypeArguments(tsf.createIdentifier('Package'), []));

        return tsf.createInterfaceDeclaration(
            [tsf.createToken(ts.SyntaxKind.ExportKeyword)],
            'GraphApi',
            [],
            [tsf.createHeritageClause(ts.SyntaxKind.ExtendsKeyword, extendsExpression)],
            [],
        );
    }

    private static getEnumPropertySignatures(enumDataType: EnumDataType): ts.PropertySignature[] {
        return Object.entries(enumDataType.instanceValues).map(([key, value]) =>
            tsf.createPropertySignature([], key, undefined, tsf.createLiteralTypeNode(tsf.createNumericLiteral(value))),
        );
    }

    private static enumDeclaration(enumDataType: EnumDataType) {
        const members = ApiGenerator.getEnumPropertySignatures(enumDataType);

        const interfaceName = `${enumDataType.enumName()}$Enum`;
        return [
            tsf.createInterfaceDeclaration(
                [tsf.createToken(ts.SyntaxKind.ExportKeyword)],
                interfaceName,
                undefined,
                undefined,
                members,
            ),
            tsf.createTypeAliasDeclaration(
                [tsf.createToken(ts.SyntaxKind.ExportKeyword)],
                enumDataType.enumName(),
                [],
                tsf.createTypeOperatorNode(ts.SyntaxKind.KeyOfKeyword, tsf.createTypeReferenceNode(interfaceName)),
            ),
        ];
    }

    private moduleStatements(): ts.Statement[] {
        const statements = [] as ts.Statement[];
        this.pack
            .getEnumDataTypes()
            .filter(enumDataType => enumDataType.baseDataType == null)
            .forEach(enumDataType => {
                statements.push(...ApiGenerator.enumDeclaration(enumDataType));
            });
        const factories = this.pack.factories;
        const apiFactories = factories
            .filter(factory => factory.isPublished)
            .filter(factory => {
                this.addNodeDeclaration(factory, statements);
                this.addNodeInputDeclaration(factory, statements);
                this.addBindingNodeDeclaration(factory, statements);
                const hasQueries = this.addQueriesDeclaration(factory, statements, '');
                const hasBuiltinQueries = factory.canRead;
                const hasMutations = this.addMutationsDeclaration(factory, statements, '');
                const hasAsyncOperations = this.addAsyncOperationsDeclaration(factory, statements, '');
                const hasLookups = this.addLookupsDeclaration(factory, statements, '');
                const hasBuiltinMutations = factory.canCreate;
                const mustGenerate =
                    factory.isAbstract ||
                    hasQueries ||
                    hasBuiltinQueries ||
                    hasMutations ||
                    hasAsyncOperations ||
                    hasBuiltinMutations ||
                    hasLookups;
                if (mustGenerate)
                    statements.push(
                        this.operationsDeclaration(
                            factory,
                            hasQueries,
                            hasMutations,
                            hasAsyncOperations,
                            hasLookups,
                            '',
                        ),
                    );
                return mustGenerate;
            });
        const extensionFactories = this.pack
            .getExtensionDecorators()
            .map(decorator => this.application.getFactoryByConstructor(decorator.extends()));
        extensionFactories
            .filter(factory => factory.isPublished)
            .forEach(factory => {
                this.addNodeExtensionDeclaration(factory, statements);
                this.addNodeInputExtensionDeclaration(factory, statements);
                this.addNodeBindingExtensionDeclaration(factory, statements);
                const hasQueries = this.addQueriesDeclaration(factory, statements, 'Extension');
                const hasMutations = this.addMutationsDeclaration(factory, statements, 'Extension');
                const hasAsyncOperations = this.addAsyncOperationsDeclaration(factory, statements, 'Extension');
                const hasLookups = this.addLookupsDeclaration(factory, statements, 'Extension');
                const hasOperations = hasQueries || hasMutations || hasAsyncOperations || hasLookups;
                if (hasOperations) {
                    statements.push(
                        this.operationsDeclaration(
                            factory,
                            hasQueries,
                            hasMutations,
                            hasAsyncOperations,
                            hasLookups,
                            'Extension',
                        ),
                    );
                }
                this.addNodeExtensionModule(factory, {
                    name: factory.name,
                    type: 'node',
                    hasQueries,
                    hasMutations,
                    hasAsyncOperations,
                    hasLookups,
                });
            });

        // extended enums
        this.pack
            .getEnumDataTypes()
            .filter(enumDataType => enumDataType.baseDataType != null)
            .forEach(enumDataType => {
                this.addEnumExtensionModule(enumDataType);
                ApiGenerator.addEnumExtensionDeclaration(enumDataType, statements);
            });
        const apiPublishedNotGenerated = factories.filter(
            factory =>
                factory.isPublished &&
                ['sql', 'external'].includes(factory.storage || '') &&
                !apiFactories.includes(factory),
        );
        const packageStatement = this.packageDeclaration(apiFactories, apiPublishedNotGenerated);
        const graphStatement = this.graphDeclaration();
        return [
            ...this.nodeImportStatements(),
            ...this.libImportStatements(),
            ...statements,
            packageStatement,
            graphStatement,
        ];
    }

    // Typescript is confused if extensions are applied directly to the module.
    // I got around it by generating two declarations:
    //
    // declare module 'foo-api-partial' {
    //   body-of-the-module-here
    // }
    //
    // declare module 'foo-api' {
    //   export * from 'foo-api-partial'
    // }
    //
    // Then, the other packages will extend 'foo-api-partial' rather than 'foo-api' and everyone
    // will be happy.
    private moduleDeclarationPartial() {
        return tsf.createModuleDeclaration(
            [tsf.createToken(ts.SyntaxKind.DeclareKeyword)],
            tsf.createStringLiteral(`${this.pack.name}-api-partial`),
            tsf.createModuleBlock(this.moduleStatements()),
        );
    }

    private moduleDeclaration() {
        return tsf.createModuleDeclaration(
            [tsf.createModifier(ts.SyntaxKind.DeclareKeyword)],
            tsf.createStringLiteral(`${this.pack.name}-api`),
            tsf.createModuleBlock([
                tsf.createExportDeclaration(
                    undefined,
                    true,
                    undefined,
                    tsf.createStringLiteral(`${this.pack.name}-api-partial`),
                ),
            ]),
        );
    }

    moduleDeclarations() {
        return [
            this.moduleDeclarationPartial(),
            this.moduleDeclaration(),
            ...this.graphExtensions(),
            ...this.moduleExtensionsStatements(),
        ];
    }
}

export function generateClientApi(app: Application): Promise<string> {
    return pretty(new ApiGenerator(app.mainPackage).moduleDeclarations());
}
