{"name": "@sage/xtrem-client-gen", "description": "Code generator for client api", "version": "58.0.2", "license": "UNLICENSED", "author": "Sage", "publishConfig": {"registry": "https://pkgs.dev.azure.com/Sage-LiveServices/_packaging/Sage-ERP/npm/registry/"}, "repository": {"type": "git", "url": "git://github.com/Sage-ERP-X3/xtrem.git"}, "main": "build/index.js", "types": "build/index.d.ts", "dependencies": {"@sage/xtrem-core": "workspace:*", "lodash": "^4.17.21", "prettier": "^3.3.3", "typescript": "~5.8.3"}, "files": ["build", "README.md", "CHANGELOG.md"], "devDependencies": {"@sage/xtrem-client": "workspace:*", "@sage/xtrem-minify": "workspace:*", "@types/chai": "^4.3.6", "@types/lodash": "^4.14.198", "@types/mocha": "^10.0.1", "@types/node": "^22.10.2", "c8": "^10.1.2", "chai": "^4.3.10", "copyfiles": "^2.1.0", "cross-env": "^7.0.3", "eslint": "^8.49.0", "mocha": "^10.8.2", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0"}, "scripts": {"build": "tsc -b -v .", "build:binary": "pnpm clean && pnpm build && xtrem-minify -c -z \"build/**/*.js\"", "build:cache": "turbo run build --concurrency=${XTREM_CONCURRENCY:=10}", "clean": "rm -rf build junit-report*", "lint": "eslint -c .eslintrc.js --ext .ts lib test", "lint:filename": "eslint --no-eslintrc -c .eslintrc-filename.js \"**\"", "postbuild": "copyfiles test/**/*.json test/**/api.d.ts build", "test": "mocha --recursive --exit \"test/**/*@(-|.)test.ts\"", "test:ci": "cross-env JUNIT_REPORT_PATH=junit-report-client-gen.xml JUNIT_REPORT_NAME='xtrem-client-gen' c8 --reporter=lcov --reporter=json --reporter=cobertura --reporter=text mocha --recursive --exit \"test/**/*@(-|.)test.ts\" --reporter mocha-jenkins-reporter"}, "c8": {"reporter": ["json", "lcov", "text-summary"], "extension": [".ts"], "exclude": ["**/*.d.ts", "**/*-test.ts", ""]}, "gitHead": "f0406ede1639145fdb322ebdf823bc87abddf4b8"}