import { Application, Test } from '@sage/xtrem-core';
import * as modules from './index';

export class TestApplication {
    private static _application: Promise<Application>;

    static get application(): Promise<Application> {
        if (!this._application) {
            this._application = Test.createCliApplication({ api: modules, buildDir: __dirname });
        }
        return this._application;
    }
}
