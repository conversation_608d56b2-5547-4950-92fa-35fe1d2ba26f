declare module '@sage/test-client-gen-app-api-partial' {
    import type { Package as SageTestClientGenDevDep$Package } from '@sage/test-client-gen-dev-dep-api';
    import type {
        AggregateQueryOperation,
        AggregateReadOperation,
        ClientCollection,
        ClientNode,
        ClientNodeInput,
        CreateOperation,
        DeleteOperation,
        GetDefaultsOperation,
        GetDuplicateOperation,
        Operation as Node$Operation,
        QueryOperation,
        ReadOperation,
        UpdateByIdOperation,
        UpdateOperation,
        VitalClientNode,
        VitalClientNodeInput,
        decimal,
        integer,
    } from '@sage/xtrem-client';
    export interface StatusEnum$Enum {
        draft: 1;
        reviewed: 2;
        accepted: 3;
    }
    export type StatusEnum = keyof StatusEnum$Enum;
    export interface TestEnum$Enum {
        value1: 1;
        value2: 2;
        value3: 3;
    }
    export type TestEnum = keyof TestEnum$Enum;
    export interface Address extends VitalClientNode {
        document: Document;
        code: string;
        street: string;
        zip: string;
        country: string;
        concatenate: string;
    }
    export interface AddressInput extends VitalClientNodeInput {
        code?: string;
        street?: string;
        zip?: string;
        city?: string;
    }
    export interface AddressBinding extends VitalClientNode {
        document: Document;
        code: string;
        street: string;
        zip: string;
        city: string;
        country: string;
        concatenate: string;
    }
    export interface Address$Operations {
        query: QueryOperation<Address>;
        read: ReadOperation<Address>;
        aggregate: {
            read: AggregateReadOperation<Address>;
            query: AggregateQueryOperation<Address>;
        };
        getDefaults: GetDefaultsOperation<Address>;
    }
    export interface Document extends ClientNode {
        _constructor: string;
        id: string;
        status: StatusEnum;
        statusList: StatusEnum[];
        numberList: integer[];
        stringList: string[];
        amount: string;
        address: Address;
        addresses: Address[];
        anyLines: ClientCollection<DocumentLine>;
        json: string;
        isStoredOutput: string;
    }
    export interface DocumentInput extends ClientNodeInput {
        _constructor?: string;
        id?: string;
        status?: StatusEnum;
        statusList?: StatusEnum[];
        numberList?: (integer | string)[];
        stringList?: string[];
        amount?: decimal | string;
        address?: AddressInput;
        addresses?: (integer | string)[];
        lines?: Partial<DocumentLineInput>[];
        transient?: string;
        transientRef?: integer | string;
        transientLines?: Partial<DocumentLineInput>[];
        json?: string;
    }
    export interface DocumentBinding extends ClientNode {
        _constructor: string;
        id: string;
        status: StatusEnum;
        statusList: StatusEnum[];
        numberList: integer[];
        stringList: string[];
        amount: string;
        address: AddressBinding;
        addresses: Address[];
        lines: ClientCollection<DocumentLineBinding>;
        transient: string;
        transientRef: AddressBinding;
        transientLines: ClientCollection<DocumentLineBinding>;
        json: any;
        isStoredOutput: string;
    }
    export interface Document$Queries {
        customQuery: Node$Operation<
            {
                id: string;
            },
            Document
        >;
        queryWithComplexOutput: Node$Operation<
            {
                object: {
                    simple?: boolean | string;
                    mandatory: string;
                    nullable?: string | null;
                };
                optionalObjects?: {
                    nestedStrings?: string[];
                    flag?: boolean | string;
                }[];
            },
            {
                object: {
                    simple: boolean;
                    mandatory: string;
                    nullable: string | null;
                };
                optionalObjects: {
                    nestedStrings: string[];
                    flag: boolean;
                }[];
            }
        >;
    }
    export interface Document$Mutations {
        customMutation: Node$Operation<
            {
                id: string;
                stringVal?: string | null;
                intVal?: integer | string;
            },
            string
        >;
        mutationWithReferences: Node$Operation<
            {
                mandatoryReference: string;
                mandatoryNullableReference: string | null;
                optionalReference?: string;
                optionalNullableReference?: string | null;
            },
            {
                mandatoryReference: Address;
                mandatoryNullableReference: Address | null;
                optionalReference: Address;
                optionalNullableReference: Address | null;
            }
        >;
    }
    export interface Document$Lookups {
        transientRef: QueryOperation<Address>;
    }
    export interface Document$Operations {
        query: QueryOperation<Document>;
        read: ReadOperation<Document>;
        aggregate: {
            read: AggregateReadOperation<Document>;
            query: AggregateQueryOperation<Document>;
        };
        queries: Document$Queries;
        create: CreateOperation<DocumentInput, Document>;
        getDuplicate: GetDuplicateOperation<Document>;
        update: UpdateOperation<DocumentInput, Document>;
        updateById: UpdateByIdOperation<DocumentInput, Document>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        mutations: Document$Mutations;
        lookups(dataOrId: string | { data: DocumentInput }): Document$Lookups;
        getDefaults: GetDefaultsOperation<Document>;
    }
    export interface DocumentLine extends VitalClientNode {
        _constructor: string;
        document: Document;
        lineNumber: integer;
        item: string;
        quantity: string;
    }
    export interface DocumentLineInput extends VitalClientNodeInput {
        _constructor?: string;
        lineNumber?: integer | string;
        item?: string;
        quantity?: decimal | string;
    }
    export interface DocumentLineBinding extends VitalClientNode {
        _constructor: string;
        document: Document;
        lineNumber: integer;
        item: string;
        quantity: string;
    }
    export interface DocumentLine$Operations {
        query: QueryOperation<DocumentLine>;
        read: ReadOperation<DocumentLine>;
        aggregate: {
            read: AggregateReadOperation<DocumentLine>;
            query: AggregateQueryOperation<DocumentLine>;
        };
        getDefaults: GetDefaultsOperation<DocumentLine>;
    }
    export interface TestOperationReference extends ClientNode {
        code: string;
        quantity: integer;
    }
    export interface TestOperationReferenceInput extends ClientNodeInput {
        code?: string;
        quantity?: integer | string;
    }
    export interface TestOperationReferenceBinding extends ClientNode {
        code: string;
        quantity: integer;
    }
    export interface TestOperation extends ClientNode {
        code: string;
        value: integer;
    }
    export interface TestOperationInput extends ClientNodeInput {
        code?: string;
        value?: integer | string;
    }
    export interface TestOperationBinding extends ClientNode {
        code: string;
        value: integer;
    }
    export interface TestOperation$Queries {
        queryReturningString: Node$Operation<
            {
                code?: string;
                stringVal?: string;
                intVal?: integer | string;
                enumVal?: TestEnum;
            },
            string
        >;
        queryWithComplexInput: Node$Operation<
            {
                object: {
                    simple?: boolean | string;
                    mandatory: string;
                    nullable?: string | null;
                };
                optionalObjects?: {
                    nestedStrings?: string[] | null;
                    flag?: boolean | string;
                }[];
            },
            string
        >;
        queryWithComplexOutput: Node$Operation<
            {
                object: {
                    simple?: boolean | string;
                    mandatory: string;
                    nullable?: string | null;
                };
                optionalObjects?: {
                    nestedStrings?: string[] | null;
                    flag?: boolean | string;
                }[];
            },
            {
                object: {
                    simple: boolean;
                    mandatory: string;
                    nullable: string | null;
                };
                optionalObjects: {
                    nestedStrings: string[] | null;
                    flag: boolean;
                }[];
            }
        >;
        queryWithReferences: Node$Operation<
            {
                reference?: string;
                nullableReference?: string | null;
                arrayOfReferences?: string[];
                arrayOfNullableReferences?: (string | null)[];
                nested?: {
                    reference?: integer | string;
                    nullableReference?: (integer | string) | null;
                    arrayOfReferences?: string[];
                    arrayOfNullableReferences?: (string | null)[];
                };
            },
            {
                reference: TestOperationReference;
                nullableReference: TestOperationReference | null;
                arrayOfReferences: TestOperationReference[];
                arrayOfNullableReferences: (TestOperationReference | null)[];
                nested: {
                    reference: TestOperationReference;
                    nullableReference: TestOperationReference | null;
                    arrayOfReferences: TestOperationReference[];
                    arrayOfNullableReferences: (TestOperationReference | null)[];
                };
            }
        >;
        queryReturningReference: Node$Operation<
            {
                id?: integer | string;
            },
            TestOperationReference
        >;
        queryReturningSimpleArray: Node$Operation<
            {
                len?: integer | string;
            },
            string[]
        >;
        queryReturningArrayOfObjects: Node$Operation<
            {
                len?: integer | string;
            },
            {
                index: integer;
                text: string;
            }[]
        >;
    }
    export interface TestOperation$Mutations {
        mutationReturningString: Node$Operation<
            {
                code?: string;
                stringVal?: string;
                intVal?: integer | string;
            },
            string
        >;
        mutationReturningNode: Node$Operation<
            {
                code: string;
                intVal?: integer | string;
            },
            TestOperation
        >;
        mutationWithNodeParameter: Node$Operation<
            {
                arg: TestOperationInput;
            },
            TestOperation
        >;
        mutationArrayOfInstanceReturningString: Node$Operation<
            {
                instanceArray?: TestOperationInput[];
            },
            string
        >;
        mutationArrayOfEnums: Node$Operation<
            {
                objectWithArrayOfEnums?: {
                    statusList?: TestEnum[];
                };
            },
            string
        >;
    }
    export interface TestOperation$Operations {
        queries: TestOperation$Queries;
        mutations: TestOperation$Mutations;
        getDefaults: GetDefaultsOperation<TestOperation>;
    }
    export interface Invoice extends ClientNode {
        id: string;
        status: StatusEnum;
        statusList: StatusEnum[];
        numberList: integer[];
        stringList: string[];
        amount: string;
        address: Address;
        addresses: Address[];
        lines: ClientCollection<InvoiceLine>;
        json: string;
        isStoredOutput: string;
    }
    export interface InvoiceInput extends ClientNodeInput {
        id?: string;
        status?: StatusEnum;
        statusList?: StatusEnum[];
        numberList?: (integer | string)[];
        stringList?: string[];
        amount?: decimal | string;
        address?: AddressInput;
        addresses?: (integer | string)[];
        lines?: Partial<InvoiceLineInput>[];
        transient?: string;
        transientRef?: integer | string;
        transientLines?: Partial<DocumentLineInput>[];
        json?: string;
    }
    export interface InvoiceBinding extends ClientNode {
        id: string;
        status: StatusEnum;
        statusList: StatusEnum[];
        numberList: integer[];
        stringList: string[];
        amount: string;
        address: AddressBinding;
        addresses: Address[];
        lines: ClientCollection<InvoiceLineBinding>;
        transient: string;
        transientRef: AddressBinding;
        transientLines: ClientCollection<DocumentLineBinding>;
        json: any;
        isStoredOutput: string;
    }
    export interface Invoice$Queries {
        customQuery: Node$Operation<
            {
                id: string;
            },
            Document
        >;
        queryWithComplexOutput: Node$Operation<
            {
                object: {
                    simple?: boolean | string;
                    mandatory: string;
                    nullable?: string | null;
                };
                optionalObjects?: {
                    nestedStrings?: string[];
                    flag?: boolean | string;
                }[];
            },
            {
                object: {
                    simple: boolean;
                    mandatory: string;
                    nullable: string | null;
                };
                optionalObjects: {
                    nestedStrings: string[];
                    flag: boolean;
                }[];
            }
        >;
    }
    export interface Invoice$Mutations {
        customMutation: Node$Operation<
            {
                id: string;
                stringVal?: string | null;
                intVal?: integer | string;
            },
            string
        >;
        mutationWithReferences: Node$Operation<
            {
                mandatoryReference: string;
                mandatoryNullableReference: string | null;
                optionalReference?: string;
                optionalNullableReference?: string | null;
            },
            {
                mandatoryReference: Address;
                mandatoryNullableReference: Address | null;
                optionalReference: Address;
                optionalNullableReference: Address | null;
            }
        >;
    }
    export interface Invoice$Lookups {
        transientRef: QueryOperation<Address>;
    }
    export interface Invoice$Operations {
        query: QueryOperation<Invoice>;
        read: ReadOperation<Invoice>;
        aggregate: {
            read: AggregateReadOperation<Invoice>;
            query: AggregateQueryOperation<Invoice>;
        };
        queries: Invoice$Queries;
        create: CreateOperation<InvoiceInput, Invoice>;
        getDuplicate: GetDuplicateOperation<Invoice>;
        update: UpdateOperation<InvoiceInput, Invoice>;
        updateById: UpdateByIdOperation<InvoiceInput, Invoice>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        mutations: Invoice$Mutations;
        lookups(dataOrId: string | { data: InvoiceInput }): Invoice$Lookups;
        getDefaults: GetDefaultsOperation<Invoice>;
    }
    export interface InvoiceLine extends VitalClientNode {
        document: Invoice;
        lineNumber: integer;
        item: string;
        quantity: string;
    }
    export interface InvoiceLineInput extends VitalClientNodeInput {
        lineNumber?: integer | string;
        item?: string;
        quantity?: decimal | string;
    }
    export interface InvoiceLineBinding extends VitalClientNode {
        document: Invoice;
        lineNumber: integer;
        item: string;
        quantity: string;
    }
    export interface InvoiceLine$Operations {
        query: QueryOperation<InvoiceLine>;
        read: ReadOperation<InvoiceLine>;
        aggregate: {
            read: AggregateReadOperation<InvoiceLine>;
            query: AggregateQueryOperation<InvoiceLine>;
        };
        getDefaults: GetDefaultsOperation<InvoiceLine>;
    }
    export interface Package {
        '@sage/test-client-gen-app/Address': Address$Operations;
        '@sage/test-client-gen-app/Document': Document$Operations;
        '@sage/test-client-gen-app/DocumentLine': DocumentLine$Operations;
        '@sage/test-client-gen-app/TestOperation': TestOperation$Operations;
        '@sage/test-client-gen-app/Invoice': Invoice$Operations;
        '@sage/test-client-gen-app/InvoiceLine': InvoiceLine$Operations;
        '@sage/test-client-gen-app/TestOperationReference': TestOperationReference;
    }
    export interface GraphApi extends Package, SageTestClientGenDevDep$Package {}
}
declare module '@sage/test-client-gen-app-api' {
    export type * from '@sage/test-client-gen-app-api-partial';
}
declare module '@sage/test-client-gen-dev-dep-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/test-client-gen-app-api';
    export interface GraphApi extends GraphApiExtension {}
}
