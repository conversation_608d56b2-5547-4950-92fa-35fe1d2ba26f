import { decorators, Reference } from '@sage/xtrem-core';
import { DocumentLine } from './document-line';
import { Invoice } from './invoice';

@decorators.subNode<InvoiceLine>({
    isPublished: true,
    extends: () => DocumentLine,
})
export class InvoiceLine extends DocumentLine {
    @decorators.referencePropertyOverride<InvoiceLine, 'document'>({
        node: () => Invoice,
    })
    override readonly document: Reference<Invoice>;
}
