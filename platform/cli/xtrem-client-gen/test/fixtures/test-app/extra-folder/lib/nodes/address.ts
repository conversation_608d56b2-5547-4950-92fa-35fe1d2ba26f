import { decorators, Node, Reference, StringDataType } from '@sage/xtrem-core';
// eslint-disable-next-line import/no-relative-packages
import * as testApp from '../../index';

@decorators.node<Address>({
    isPublished: true,
    storage: 'sql',
    isVitalReferenceChild: true,
    indexes: [{ orderBy: { code: +1 }, isUnique: true }],
})
export class Address extends Node {
    @decorators.referenceProperty<Address, 'document'>({
        isPublished: true,
        isStored: true,
        isVitalParent: true,
        node: () => testApp.nodes.Document,
    })
    readonly document: Reference<testApp.nodes.Document>;

    @decorators.stringProperty<Address, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringDataType({ maxLength: 10 }),
    })
    readonly code: Promise<string>;

    @decorators.stringProperty<Address, 'street'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringDataType({ maxLength: 50 }),
    })
    readonly street: Promise<string>;

    @decorators.stringProperty<Address, 'zip'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringDataType({ maxLength: 50 }),
    })
    readonly zip: Promise<string>;

    @decorators.stringProperty<Address, 'city'>({
        isPublished: true,
        isTransientInput: true,
        dataType: () => new StringDataType({ maxLength: 50 }),
    })
    readonly city: Promise<string>;

    @decorators.stringProperty<Address, 'country'>({
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 50 }),
        getValue(): string {
            return 'default';
        },
    })
    readonly country: Promise<string>;

    @decorators.stringProperty<Address, 'concatenate'>({
        isPublished: true,
        isStoredOutput: true,
        async computeValue() {
            return `${await this.street}, ${await this.city}, ${await this.country}, ${await this.zip}`;
        },
        dataType: () => new StringDataType({ maxLength: 200 }),
        updatedValue() {
            return 'updatedValue';
        },
    })
    readonly concatenate: Promise<string>;
}
