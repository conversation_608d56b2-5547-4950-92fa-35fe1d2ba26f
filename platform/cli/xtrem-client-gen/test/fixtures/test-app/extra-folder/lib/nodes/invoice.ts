import { Collection, decorators } from '@sage/xtrem-core';
import { Document } from './document';
import { InvoiceLine } from './invoice-line';

@decorators.subNode<Invoice>({
    isPublished: true,
    extends: () => Document,
    canRead: true,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
})
export class Invoice extends Document {
    @decorators.collectionPropertyOverride<Invoice, 'lines'>({
        node: () => InvoiceLine,
    })
    override readonly lines: Collection<InvoiceLine>;
}
