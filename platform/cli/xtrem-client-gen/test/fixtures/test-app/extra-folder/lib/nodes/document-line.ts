import { decimal, DecimalDataType, decorators, integer, Node, Reference, StringDataType } from '@sage/xtrem-core';
// eslint-disable-next-line import/no-relative-packages
import * as testApp from '../../index';

@decorators.node<DocumentLine>({
    isVitalCollectionChild: true,
    isAbstract: true,
    isPublished: true,
    storage: 'sql',
    indexes: [{ orderBy: { document: +1, lineNumber: +1 }, isUnique: true }],
})
export class DocumentLine extends Node {
    @decorators.referenceProperty<DocumentLine, 'document'>({
        isVitalParent: true,
        isPublished: true,
        isStored: true,
        node: () => testApp.nodes.Document,
    })
    readonly document: Reference<testApp.nodes.Document>;

    @decorators.integerProperty<DocumentLine, 'lineNumber'>({
        isStored: true,
        isPublished: true,
    })
    readonly lineNumber: Promise<integer>;

    @decorators.stringProperty<DocumentLine, 'item'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringDataType({ maxLength: 50 }),
    })
    readonly item: Promise<string>;

    @decorators.decimalProperty<DocumentLine, 'quantity'>({
        isPublished: true,
        isStored: true,
        dataType: () => new DecimalDataType({ precision: 10, scale: 3 }),
    })
    readonly quantity: Promise<decimal>;
}
