import {
    Collection,
    Context,
    decimal,
    DecimalDataType,
    decorators,
    integer,
    Node,
    Reference,
    StringArrayDataType,
    StringDataType,
} from '@sage/xtrem-core';
// eslint-disable-next-line import/no-relative-packages
import * as testApp from '../../index';
import { statusDataType, StatusEnum } from '../enums/_index';
import { Address } from './address';

type ComplexParameters = {
    object: { simple?: boolean | string; mandatory: string; nullable?: integer | null };
    optionalObjects?: { nestedStrings?: string[]; flag?: boolean | string }[];
};

@decorators.node<Document>({
    isPublished: true,
    isAbstract: true,
    storage: 'sql',
    canRead: true,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    indexes: [{ orderBy: { id: +1 }, isUnique: true }],
})
export class Document extends Node {
    @decorators.stringProperty<Document, 'id'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringDataType({ maxLength: 10 }),
    })
    readonly id: Promise<string>;

    @decorators.enumProperty<Document, 'status'>({
        isPublished: true,
        isStored: true,
        dataType: () => statusDataType,
    })
    readonly status: Promise<StatusEnum>;

    @decorators.enumArrayProperty<Document, 'statusList'>({
        isPublished: true,
        isStored: true,
        dataType: () => statusDataType,
    })
    readonly statusList: Promise<StatusEnum[]>;

    @decorators.integerArrayProperty<Document, 'numberList'>({
        isPublished: true,
        isStored: true,
    })
    readonly numberList: Promise<integer[]>;

    @decorators.stringArrayProperty<Document, 'stringList'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringArrayDataType({ maxLength: 10 }),
    })
    readonly stringList: Promise<string[]>;

    @decorators.decimalProperty<Document, 'amount'>({
        isPublished: true,
        isStored: true,
        dataType: () => new DecimalDataType({ precision: 10, scale: 3 }),
    })
    readonly amount: Promise<decimal>;

    @decorators.referenceProperty<Document, 'address'>({
        isPublished: true,
        isVital: true,
        node: () => testApp.nodes.Address,
        reverseReference: 'document',
    })
    readonly address: Reference<testApp.nodes.Address>;

    @decorators.referenceArrayProperty<Document, 'addresses'>({
        isPublished: true,
        isStored: true,
        onDelete: 'restrict',
        node: () => testApp.nodes.Address,
    })
    readonly addresses: Promise<testApp.nodes.Address[]>;

    @decorators.collectionProperty<Document, 'lines'>({
        isPublished: true,
        isVital: true,
        node: () => testApp.nodes.DocumentLine,
        reverseReference: 'document',
    })
    readonly lines: Collection<testApp.nodes.DocumentLine>;

    @decorators.stringProperty<Document, 'transient'>({
        isPublished: true,
        isTransientInput: true,
        dataType: () => new StringDataType({ maxLength: 10 }),
    })
    readonly transient: Promise<string>;

    @decorators.referenceProperty<Document, 'transientRef'>({
        isPublished: true,
        isTransientInput: true,
        node: () => testApp.nodes.Address,
    })
    readonly transientRef: Reference<testApp.nodes.Address>;

    @decorators.collectionProperty<Document, 'transientLines'>({
        isPublished: true,
        node: () => testApp.nodes.DocumentLine,
        reverseReference: 'document',
        orderBy: { lineNumber: +1 },
        isTransientInput: true,
    })
    readonly transientLines: Collection<testApp.nodes.DocumentLine>;

    @decorators.jsonProperty<Document, 'json'>({
        isPublished: true,
        isStored: true,
    })
    readonly json: Promise<object>;

    @decorators.stringProperty<Document, 'isStoredOutput'>({
        isPublished: true,
        isStoredOutput: true,
        updatedValue: () => 'StoredTest',
        dataType: () => new StringDataType({ maxLength: 10 }),
    })
    readonly isStoredOutput: Promise<string>;

    @decorators.query<typeof Document, 'customQuery'>({
        isPublished: true,
        parameters: [{ name: 'id', type: 'string', isMandatory: true }],
        return: {
            type: 'instance',
            node() {
                return testApp.nodes.Document;
            },
        },
    })
    static customQuery(context: Context, id: string): Promise<Document | null> {
        return context.tryRead(Document, { id });
    }

    @decorators.mutation<typeof Document, 'customMutation'>({
        isPublished: true,
        parameters: [
            { name: 'id', type: 'string', isMandatory: true },
            { name: 'stringVal', type: 'string', isNullable: true },
            { name: 'intVal', type: 'integer' },
        ],
        return: 'string',
    })
    static customMutation(context: Context, id: string, stringVal?: string, intVal?: integer) {
        return `${id}:${stringVal}:${intVal}`;
    }

    @decorators.mutation<typeof Document, 'mutationWithReferences'>({
        isPublished: true,
        parameters: [
            { name: 'mandatoryReference', type: 'reference', node: () => Address, isMandatory: true },
            {
                name: 'mandatoryNullableReference',
                type: 'reference',
                node: () => Address,
                isMandatory: true,
                isNullable: true,
            },
            { name: 'optionalReference', type: 'reference', node: () => Address },
            { name: 'optionalNullableReference', type: 'reference', node: () => Address, isNullable: true },
        ],
        return: {
            type: 'object',
            properties: {
                mandatoryReference: {
                    type: 'reference',
                    node: () => Address,
                    isMandatory: true,
                },
                mandatoryNullableReference: {
                    type: 'reference',
                    node: () => Address,
                    isMandatory: true,
                    isNullable: true,
                },
                optionalReference: {
                    type: 'reference',
                    node: () => Address,
                },
                optionalNullableReference: {
                    type: 'reference',
                    node: () => Address,
                    isNullable: true,
                },
            },
        },
    })
    static mutationWithReferences(
        context: Context,
        mandatoryReference: Address,
        mandatoryNullableReference: Address | null,
        optionalReference?: Address,
        optionalNullableReference?: Address | null,
    ) {
        return { mandatoryReference, mandatoryNullableReference, optionalReference, optionalNullableReference };
    }

    @decorators.query<typeof Document, 'queryWithComplexOutput'>({
        isPublished: true,
        parameters: [
            {
                name: 'object',
                type: 'object',
                isMandatory: true,
                properties: {
                    simple: 'boolean',
                    mandatory: {
                        type: 'string',
                        isMandatory: true,
                    },
                    nullable: {
                        type: 'string',
                        isNullable: true,
                    },
                },
            },
            {
                name: 'optionalObjects',
                type: 'array',
                item: {
                    type: 'object',
                    properties: {
                        nestedStrings: {
                            type: 'array',
                            item: 'string',
                        },
                        flag: 'boolean',
                    },
                },
            },
        ],
        return: {
            type: 'object',
            properties: {
                object: {
                    type: 'object',
                    isMandatory: true,
                    properties: {
                        simple: 'boolean',
                        mandatory: {
                            type: 'string',
                            isMandatory: true,
                        },
                        nullable: {
                            type: 'string',
                            isNullable: true,
                        },
                    },
                },
                optionalObjects: {
                    type: 'array',
                    item: {
                        type: 'object',
                        properties: {
                            nestedStrings: {
                                type: 'array',
                                item: 'string',
                            },
                            flag: 'boolean',
                        },
                    },
                },
            },
        },
    })
    static queryWithComplexOutput(
        context: Context,
        object: ComplexParameters['object'],
        optionalObjects?: ComplexParameters['optionalObjects'],
    ): ComplexParameters {
        return { object, optionalObjects };
    }
}
