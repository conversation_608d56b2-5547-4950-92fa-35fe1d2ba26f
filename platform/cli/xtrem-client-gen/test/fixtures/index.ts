import { Application } from '@sage/xtrem-core';
import { sourceMapSetup } from '@sage/xtrem-log';
// eslint-disable-next-line import/no-relative-packages
import { TestApplication } from './test-app/extra-folder/index';
// eslint-disable-next-line import/no-relative-packages
import { TestExtendedApplication } from './test-extended-app/extra-folder/index';

export function setup(): void {
    sourceMapSetup();
}

// do not import/export directly because we need test-extended-app should not be loaded
// when we generate code for test-app
export function createTestApp(): Promise<Application> {
    return TestApplication.application;
}
export function createTestExtendedApp(): Promise<Application> {
    return TestExtendedApplication.application;
}
