# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

## [24.0.8](https://github.com/compare/...@sage/test-extended-client-gen-app@24.0.8) (2022-10-18)

### Bug Fixes


### Features


# [24.0.6](https://github.com/compare/...@sage/test-extended-client-gen-app@24.0.6) (2022-10-16)

### Bug Fixes


### Features


# [24.0.5](https://github.com/compare/...@sage/test-extended-client-gen-app@24.0.5) (2022-10-15)

### Bug Fixes


### Features


# [24.0.4](https://github.com/compare/...@sage/test-extended-client-gen-app@24.0.4) (2022-10-15)

### Bug Fixes


### Features


# [24.0.3](https://github.com/compare/...@sage/test-extended-client-gen-app@24.0.3) (2022-10-11)

### Bug Fixes


### Features


# [24.0.2](https://github.com/compare/...@sage/test-extended-client-gen-app@24.0.2) (2022-10-11)

### Bug Fixes


### Features


# [24.0.1](https://github.com/compare/...@sage/test-extended-client-gen-app@24.0.1) (2022-10-11)

### Bug Fixes


### Features


# [24.0.0](https://github.com/compare/...@sage/test-extended-client-gen-app@24.0.0) (2022-09-29)

### Bug Fixes


### Features


# [23.0.48](https://github.com/compare/...@sage/test-extended-client-gen-app@23.0.48) (2022-09-29)

### Bug Fixes


### Features


# [23.0.47](https://github.com/compare/...@sage/test-extended-client-gen-app@23.0.47) (2022-09-29)

### Bug Fixes


### Features


# [23.0.46](https://github.com/compare/...@sage/test-extended-client-gen-app@23.0.46) (2022-09-28)

### Bug Fixes


### Features


# [23.0.45](https://github.com/compare/...@sage/test-extended-client-gen-app@23.0.45) (2022-09-28)

### Bug Fixes


### Features


# [23.0.44](https://github.com/compare/...@sage/test-extended-client-gen-app@23.0.44) (2022-09-26)

### Bug Fixes


### Features


# [23.0.43](https://github.com/compare/...@sage/test-extended-client-gen-app@23.0.43) (2022-09-25)

### Bug Fixes


### Features


# [23.0.42](https://github.com/compare/...@sage/test-extended-client-gen-app@23.0.42) (2022-09-24)

### Bug Fixes


### Features


# [23.0.41](https://github.com/compare/...@sage/test-extended-client-gen-app@23.0.41) (2022-09-23)

### Bug Fixes


### Features


# [23.0.40](https://github.com/compare/...@sage/test-extended-client-gen-app@23.0.40) (2022-09-22)

### Bug Fixes


### Features


# [23.0.39](https://github.com/compare/...@sage/test-extended-client-gen-app@23.0.39) (2022-09-21)

### Bug Fixes


### Features


# [23.0.38](https://github.com/compare/...@sage/test-extended-client-gen-app@23.0.38) (2022-09-21)

### Bug Fixes


### Features


# [23.0.37](https://github.com/compare/...@sage/test-extended-client-gen-app@23.0.37) (2022-09-21)

### Bug Fixes


### Features


# [23.0.36](https://github.com/compare/...@sage/test-extended-client-gen-app@23.0.36) (2022-09-19)

### Bug Fixes


### Features


# [23.0.35](https://github.com/compare/...@sage/test-extended-client-gen-app@23.0.35) (2022-09-19)

### Bug Fixes


### Features


# [23.0.34](https://github.com/compare/...@sage/test-extended-client-gen-app@23.0.34) (2022-09-19)

### Bug Fixes


### Features


# [23.0.33](https://github.com/compare/...@sage/test-extended-client-gen-app@23.0.33) (2022-09-19)

### Bug Fixes


### Features


# [23.0.32](https://github.com/compare/...@sage/test-extended-client-gen-app@23.0.32) (2022-09-18)

### Bug Fixes


### Features


# [23.0.31](https://github.com/compare/...@sage/test-extended-client-gen-app@23.0.31) (2022-09-18)

### Bug Fixes


### Features


# [23.0.30](https://github.com/compare/...@sage/test-extended-client-gen-app@23.0.30) (2022-09-18)

### Bug Fixes


### Features


# [23.0.29](https://github.com/compare/...@sage/test-extended-client-gen-app@23.0.29) (2022-09-18)

### Bug Fixes


### Features


# [23.0.28](https://github.com/compare/...@sage/test-extended-client-gen-app@23.0.28) (2022-09-17)

### Bug Fixes


### Features


# [23.0.27](https://github.com/compare/...@sage/test-extended-client-gen-app@23.0.27) (2022-09-17)

### Bug Fixes


### Features


# [23.0.26](https://github.com/compare/...@sage/test-extended-client-gen-app@23.0.26) (2022-09-16)

### Bug Fixes


### Features


# [23.0.25](https://github.com/compare/...@sage/test-extended-client-gen-app@23.0.25) (2022-09-16)

### Bug Fixes


### Features


# [23.0.24](https://github.com/compare/...@sage/test-extended-client-gen-app@23.0.24) (2022-09-16)

### Bug Fixes


### Features


# [23.0.23](https://github.com/compare/...@sage/test-extended-client-gen-app@23.0.23) (2022-09-16)

### Bug Fixes


### Features


# [23.0.22](https://github.com/compare/...@sage/test-extended-client-gen-app@23.0.22) (2022-09-14)

### Bug Fixes


### Features


# [23.0.21](https://github.com/compare/...@sage/test-extended-client-gen-app@23.0.21) (2022-09-14)

### Bug Fixes


### Features


# [23.0.20](https://github.com/compare/...@sage/test-extended-client-gen-app@23.0.20) (2022-09-12)

### Bug Fixes


### Features


# [23.0.19](https://github.com/compare/...@sage/test-extended-client-gen-app@23.0.19) (2022-09-12)

### Bug Fixes


### Features


# [23.0.18](https://github.com/compare/...@sage/test-extended-client-gen-app@23.0.18) (2022-09-11)

### Bug Fixes


### Features


# [23.0.17](https://github.com/compare/...@sage/test-extended-client-gen-app@23.0.17) (2022-09-10)

### Bug Fixes


### Features


# [23.0.16](https://github.com/compare/...@sage/test-extended-client-gen-app@23.0.16) (2022-09-09)

### Bug Fixes


### Features


# [23.0.15](https://github.com/compare/...@sage/test-extended-client-gen-app@23.0.15) (2022-09-09)

### Bug Fixes


### Features


# [23.0.14](https://github.com/compare/...@sage/test-extended-client-gen-app@23.0.14) (2022-09-07)

### Bug Fixes


### Features


# [23.0.13](https://github.com/compare/...@sage/test-extended-client-gen-app@23.0.13) (2022-09-07)

### Bug Fixes


### Features


# [23.0.12](https://github.com/compare/...@sage/test-extended-client-gen-app@23.0.12) (2022-09-06)

### Bug Fixes


### Features


# [23.0.11](https://github.com/compare/...@sage/test-extended-client-gen-app@23.0.11) (2022-09-05)

### Bug Fixes


### Features


# [23.0.10](https://github.com/compare/...@sage/test-extended-client-gen-app@23.0.10) (2022-09-05)

### Bug Fixes


### Features


# [23.0.9](https://github.com/compare/...@sage/test-extended-client-gen-app@23.0.9) (2022-09-04)

### Bug Fixes


### Features


# [23.0.8](https://github.com/compare/...@sage/test-extended-client-gen-app@23.0.8) (2022-09-03)

### Bug Fixes


### Features


# [23.0.7](https://github.com/compare/...@sage/test-extended-client-gen-app@23.0.7) (2022-09-02)

### Bug Fixes


### Features


# [23.0.6](https://github.com/compare/...@sage/test-extended-client-gen-app@23.0.6) (2022-09-01)

### Bug Fixes


### Features


# [23.0.5](https://github.com/compare/...@sage/test-extended-client-gen-app@23.0.5) (2022-08-31)

### Bug Fixes


### Features


# [23.0.4](https://github.com/compare/...@sage/test-extended-client-gen-app@23.0.4) (2022-08-30)

### Bug Fixes


### Features


# [23.0.3](https://github.com/compare/...@sage/test-extended-client-gen-app@23.0.3) (2022-08-30)

### Bug Fixes


### Features


# [23.0.2](https://github.com/compare/...@sage/test-extended-client-gen-app@23.0.2) (2022-08-29)

### Bug Fixes


### Features


# [23.0.1](https://github.com/compare/...@sage/test-extended-client-gen-app@23.0.1) (2022-08-29)

### Bug Fixes


### Features


# [23.0.0](https://github.com/compare/...@sage/test-extended-client-gen-app@23.0.0) (2022-08-26)

### Bug Fixes


### Features


# [22.0.25](https://github.com/compare/...@sage/test-extended-client-gen-app@22.0.25) (2022-08-25)

### Bug Fixes


### Features


# [22.0.24](https://github.com/compare/...@sage/test-extended-client-gen-app@22.0.24) (2022-08-24)

### Bug Fixes


### Features


# [22.0.23](https://github.com/compare/...@sage/test-extended-client-gen-app@22.0.23) (2022-08-23)

### Bug Fixes


### Features


# [22.0.22](https://github.com/compare/...@sage/test-extended-client-gen-app@22.0.22) (2022-08-22)

### Bug Fixes


### Features


# [22.0.21](https://github.com/compare/...@sage/test-extended-client-gen-app@22.0.21) (2022-08-21)

### Bug Fixes


### Features


# [22.0.20](https://github.com/compare/...@sage/test-extended-client-gen-app@22.0.20) (2022-08-19)

### Bug Fixes


### Features


# [22.0.19](https://github.com/compare/...@sage/test-extended-client-gen-app@22.0.19) (2022-08-18)

### Bug Fixes


### Features


# [22.0.18](https://github.com/compare/...@sage/test-extended-client-gen-app@22.0.18) (2022-08-17)

### Bug Fixes


### Features


# [22.0.17](https://github.com/compare/...@sage/test-extended-client-gen-app@22.0.17) (2022-08-16)

### Bug Fixes


### Features


# [22.0.16](https://github.com/compare/...@sage/test-extended-client-gen-app@22.0.16) (2022-08-15)

### Bug Fixes


### Features


# [22.0.15](https://github.com/compare/...@sage/test-extended-client-gen-app@22.0.15) (2022-08-14)

### Bug Fixes


### Features


# [22.0.14](https://github.com/compare/...@sage/test-extended-client-gen-app@22.0.14) (2022-08-13)

### Bug Fixes


### Features


# [22.0.13](https://github.com/compare/...@sage/test-extended-client-gen-app@22.0.13) (2022-08-11)

### Bug Fixes


### Features


# [22.0.12](https://github.com/compare/...@sage/test-extended-client-gen-app@22.0.12) (2022-08-11)

### Bug Fixes


### Features


# [22.0.11](https://github.com/compare/...@sage/test-extended-client-gen-app@22.0.11) (2022-08-09)

### Bug Fixes


### Features


# [22.0.10](https://github.com/compare/...@sage/test-extended-client-gen-app@22.0.10) (2022-08-08)

### Bug Fixes


### Features


# [22.0.9](https://github.com/compare/...@sage/test-extended-client-gen-app@22.0.9) (2022-08-07)

### Bug Fixes


### Features


# [22.0.8](https://github.com/compare/...@sage/test-extended-client-gen-app@22.0.8) (2022-08-06)

### Bug Fixes


### Features


# [22.0.7](https://github.com/compare/...@sage/test-extended-client-gen-app@22.0.7) (2022-08-05)

### Bug Fixes


### Features


# [22.0.6](https://github.com/compare/...@sage/test-extended-client-gen-app@22.0.6) (2022-08-04)

### Bug Fixes


### Features


# [22.0.5](https://github.com/compare/...@sage/test-extended-client-gen-app@22.0.5) (2022-08-01)

### Bug Fixes


### Features


# [22.0.4](https://github.com/compare/...@sage/test-extended-client-gen-app@22.0.4) (2022-07-31)

### Bug Fixes


### Features


# [22.0.3](https://github.com/compare/...@sage/test-extended-client-gen-app@22.0.3) (2022-07-30)

### Bug Fixes


### Features


# [22.0.2](https://github.com/compare/...@sage/test-extended-client-gen-app@22.0.2) (2022-07-29)

### Bug Fixes


### Features


# [22.0.1](https://github.com/compare/...@sage/test-extended-client-gen-app@22.0.1) (2022-07-28)

### Bug Fixes


### Features


# [22.0.0](https://github.com/compare/...@sage/test-extended-client-gen-app@22.0.0) (2022-07-28)

### Bug Fixes


### Features


# [21.0.36](https://github.com/compare/...@sage/test-extended-client-gen-app@21.0.36) (2022-07-28)

### Bug Fixes


### Features


# [21.0.35](https://github.com/compare/...@sage/test-extended-client-gen-app@21.0.35) (2022-07-27)

### Bug Fixes


### Features


# [21.0.34](https://github.com/compare/...@sage/test-extended-client-gen-app@21.0.34) (2022-07-26)

### Bug Fixes


### Features


# [21.0.33](https://github.com/compare/...@sage/test-extended-client-gen-app@21.0.33) (2022-07-25)

### Bug Fixes


### Features


# [21.0.32](https://github.com/compare/...@sage/test-extended-client-gen-app@21.0.32) (2022-07-24)

### Bug Fixes


### Features


# [21.0.31](https://github.com/compare/...@sage/test-extended-client-gen-app@21.0.31) (2022-07-23)

### Bug Fixes


### Features


# [21.0.30](https://github.com/compare/...@sage/test-extended-client-gen-app@21.0.30) (2022-07-22)

### Bug Fixes


### Features


# [21.0.29](https://github.com/compare/...@sage/test-extended-client-gen-app@21.0.29) (2022-07-21)

### Bug Fixes


### Features


# [21.0.28](https://github.com/compare/...@sage/test-extended-client-gen-app@21.0.28) (2022-07-20)

### Bug Fixes


### Features


# [21.0.27](https://github.com/compare/...@sage/test-extended-client-gen-app@21.0.27) (2022-07-19)

### Bug Fixes


### Features


# [21.0.26](https://github.com/compare/...@sage/test-extended-client-gen-app@21.0.26) (2022-07-19)

### Bug Fixes


### Features


# [21.0.25](https://github.com/compare/...@sage/test-extended-client-gen-app@21.0.25) (2022-07-18)

### Bug Fixes


### Features


# [21.0.24](https://github.com/compare/...@sage/test-extended-client-gen-app@21.0.24) (2022-07-17)

### Bug Fixes


### Features


# [21.0.23](https://github.com/compare/...@sage/test-extended-client-gen-app@21.0.23) (2022-07-16)

### Bug Fixes


### Features


# [21.0.22](https://github.com/compare/...@sage/test-extended-client-gen-app@21.0.22) (2022-07-15)

### Bug Fixes


### Features


# [21.0.21](https://github.com/compare/...@sage/test-extended-client-gen-app@21.0.21) (2022-07-14)

### Bug Fixes


### Features


# [21.0.20](https://github.com/compare/...@sage/test-extended-client-gen-app@21.0.20) (2022-07-14)

### Bug Fixes


### Features


# [21.0.19](https://github.com/compare/...@sage/test-extended-client-gen-app@21.0.19) (2022-07-13)

### Bug Fixes


### Features


# [21.0.18](https://github.com/compare/...@sage/test-extended-client-gen-app@21.0.18) (2022-07-13)

### Bug Fixes


### Features


# [21.0.17](https://github.com/compare/...@sage/test-extended-client-gen-app@21.0.17) (2022-07-10)

### Bug Fixes


### Features


# [21.0.16](https://github.com/compare/...@sage/test-extended-client-gen-app@21.0.16) (2022-07-09)

### Bug Fixes


### Features


# [21.0.15](https://github.com/compare/...@sage/test-extended-client-gen-app@21.0.15) (2022-07-08)

### Bug Fixes


### Features


# [21.0.14](https://github.com/compare/...@sage/test-extended-client-gen-app@21.0.14) (2022-07-07)

### Bug Fixes


### Features


# [21.0.13](https://github.com/compare/...@sage/test-extended-client-gen-app@21.0.13) (2022-07-06)

### Bug Fixes


### Features


# [21.0.12](https://github.com/compare/...@sage/test-extended-client-gen-app@21.0.12) (2022-07-05)

### Bug Fixes


### Features


# [21.0.11](https://github.com/compare/...@sage/test-extended-client-gen-app@21.0.11) (2022-07-04)

### Bug Fixes


### Features


# [21.0.10](https://github.com/compare/...@sage/test-extended-client-gen-app@21.0.10) (2022-07-04)

### Bug Fixes


### Features


# [21.0.9](https://github.com/compare/...@sage/test-extended-client-gen-app@21.0.9) (2022-07-02)

### Bug Fixes


### Features


# [21.0.8](https://github.com/compare/...@sage/test-extended-client-gen-app@21.0.8) (2022-07-01)

### Bug Fixes


### Features


# [21.0.7](https://github.com/compare/...@sage/test-extended-client-gen-app@21.0.7) (2022-06-30)

### Bug Fixes


### Features


# [21.0.6](https://github.com/compare/...@sage/test-extended-client-gen-app@21.0.6) (2022-06-29)

### Bug Fixes


### Features


# [21.0.5](https://github.com/compare/...@sage/test-extended-client-gen-app@21.0.5) (2022-06-28)

### Bug Fixes


### Features


# [21.0.4](https://github.com/compare/...@sage/test-extended-client-gen-app@21.0.4) (2022-06-27)

### Bug Fixes


### Features


# [21.0.3](https://github.com/compare/...@sage/test-extended-client-gen-app@21.0.3) (2022-06-26)

### Bug Fixes


### Features


# [21.0.2](https://github.com/compare/...@sage/test-extended-client-gen-app@21.0.2) (2022-06-25)

### Bug Fixes


### Features


# [21.0.1](https://github.com/compare/...@sage/test-extended-client-gen-app@21.0.1) (2022-06-24)

### Bug Fixes


### Features


# [21.0.0](https://github.com/compare/...@sage/test-extended-client-gen-app@21.0.0) (2022-06-23)

### Bug Fixes


### Features


# [20.0.32](https://github.com/compare/...@sage/test-extended-client-gen-app@20.0.32) (2022-06-23)

### Bug Fixes


### Features


# [20.0.31](https://github.com/compare/...@sage/test-extended-client-gen-app@20.0.31) (2022-06-22)

### Bug Fixes


### Features


# [20.0.30](https://github.com/compare/...@sage/test-extended-client-gen-app@20.0.30) (2022-06-21)

### Bug Fixes


### Features


# [20.0.29](https://github.com/compare/...@sage/test-extended-client-gen-app@20.0.29) (2022-06-21)

### Bug Fixes


### Features


# [20.0.28](https://github.com/compare/...@sage/test-extended-client-gen-app@20.0.28) (2022-06-20)

### Bug Fixes


### Features


# [20.0.27](https://github.com/compare/...@sage/test-extended-client-gen-app@20.0.27) (2022-06-19)

### Bug Fixes


### Features


# [20.0.26](https://github.com/compare/...@sage/test-extended-client-gen-app@20.0.26) (2022-06-18)

### Bug Fixes


### Features


# [20.0.25](https://github.com/compare/...@sage/test-extended-client-gen-app@20.0.25) (2022-06-17)

### Bug Fixes

* **client-gen:** XT-27784 fixed typing issue in UI mashup packages ([#7518](https://github.com/issues/7518))  ([4efc9d9](https://github.com/commit/4efc9d992c6c119605567fc8b8697737adc16ba7))

### Features


# [20.0.24](https://github.com/compare/...@sage/test-extended-client-gen-app@20.0.24) (2022-06-16)

### Bug Fixes


### Features


# [20.0.23](https://github.com/compare/...@sage/test-extended-client-gen-app@20.0.23) (2022-06-16)

### Bug Fixes


### Features


# [20.0.22](https://github.com/compare/...@sage/test-extended-client-gen-app@20.0.22) (2022-06-14)

### Bug Fixes


### Features


# [20.0.21](https://github.com/compare/...@sage/test-extended-client-gen-app@20.0.21) (2022-06-14)

### Bug Fixes


### Features


# [20.0.20](https://github.com/compare/...@sage/test-extended-client-gen-app@20.0.20) (2022-06-13)

### Bug Fixes


### Features


# [20.0.19](https://github.com/compare/...@sage/test-extended-client-gen-app@20.0.19) (2022-06-12)

### Bug Fixes


### Features


# [20.0.18](https://github.com/compare/...@sage/test-extended-client-gen-app@20.0.18) (2022-06-11)

### Bug Fixes


### Features


# [20.0.17](https://github.com/compare/...@sage/test-extended-client-gen-app@20.0.17) (2022-06-10)

### Bug Fixes


### Features


# [20.0.16](https://github.com/compare/...@sage/test-extended-client-gen-app@20.0.16) (2022-06-10)

### Bug Fixes


### Features


# [20.0.15](https://github.com/compare/...@sage/test-extended-client-gen-app@20.0.15) (2022-06-08)

### Bug Fixes


### Features


# [20.0.14](https://github.com/compare/...@sage/test-extended-client-gen-app@20.0.14) (2022-06-07)

### Bug Fixes


### Features


# [20.0.13](https://github.com/compare/...@sage/test-extended-client-gen-app@20.0.13) (2022-06-06)

### Bug Fixes


### Features


# [20.0.12](https://github.com/compare/...@sage/test-extended-client-gen-app@20.0.12) (2022-06-05)

### Bug Fixes


### Features


# [20.0.11](https://github.com/compare/...@sage/test-extended-client-gen-app@20.0.11) (2022-06-04)

### Bug Fixes


### Features


# [20.0.10](https://github.com/compare/...@sage/test-extended-client-gen-app@20.0.10) (2022-06-03)

### Bug Fixes


### Features


# [20.0.9](https://github.com/compare/...@sage/test-extended-client-gen-app@20.0.9) (2022-06-03)

### Bug Fixes


### Features


# [20.0.8](https://github.com/compare/...@sage/test-extended-client-gen-app@20.0.8) (2022-06-02)

### Bug Fixes

* package version number fix ([#7344](https://github.com/issues/7344))  ([517e460](https://github.com/commit/517e460cde4848f3559f14bfd79a77835cd78223))

### Features


# [20.0.5](https://github.com/compare/...@sage/test-extended-client-gen-app@20.0.5) (2022-06-02)

### Bug Fixes


### Features


# [20.0.4](https://github.com/compare/...@sage/test-extended-client-gen-app@20.0.4) (2022-06-01)

### Bug Fixes


### Features


# [20.0.3](https://github.com/compare/...@sage/test-extended-client-gen-app@20.0.3) (2022-05-29)

### Bug Fixes


### Features


# [20.0.2](https://github.com/compare/...@sage/test-extended-client-gen-app@20.0.2) (2022-05-28)

### Bug Fixes


### Features


# [20.0.1](https://github.com/compare/...@sage/test-extended-client-gen-app@20.0.1) (2022-05-27)

### Bug Fixes


### Features


# [20.0.0](https://github.com/compare/...@sage/test-extended-client-gen-app@20.0.0) (2022-05-26)

### Bug Fixes


### Features


# [19.0.33](https://github.com/compare/...@sage/test-extended-client-gen-app@19.0.33) (2022-05-26)

### Bug Fixes


### Features


# [19.0.32](https://github.com/compare/...@sage/test-extended-client-gen-app@19.0.32) (2022-05-26)

### Bug Fixes


### Features


# [19.0.31](https://github.com/compare/...@sage/test-extended-client-gen-app@19.0.31) (2022-05-24)

### Bug Fixes


### Features


# [19.0.30](https://github.com/compare/...@sage/test-extended-client-gen-app@19.0.30) (2022-05-24)

### Bug Fixes


### Features


# [19.0.29](https://github.com/compare/...@sage/test-extended-client-gen-app@19.0.29) (2022-05-23)

### Bug Fixes


### Features


# [19.0.28](https://github.com/compare/...@sage/test-extended-client-gen-app@19.0.28) (2022-05-23)

### Bug Fixes


### Features


# [19.0.27](https://github.com/compare/...@sage/test-extended-client-gen-app@19.0.27) (2022-05-22)

### Bug Fixes


### Features


# [19.0.26](https://github.com/compare/...@sage/test-extended-client-gen-app@19.0.26) (2022-05-21)

### Bug Fixes


### Features


# [19.0.25](https://github.com/compare/...@sage/test-extended-client-gen-app@19.0.25) (2022-05-20)

### Bug Fixes


### Features


# [19.0.24](https://github.com/compare/...@sage/test-extended-client-gen-app@19.0.24) (2022-05-20)

### Bug Fixes


### Features


# [19.0.23](https://github.com/compare/...@sage/test-extended-client-gen-app@19.0.23) (2022-05-18)

### Bug Fixes


### Features


# [19.0.22](https://github.com/compare/...@sage/test-extended-client-gen-app@19.0.22) (2022-05-17)

### Bug Fixes


### Features


# [19.0.21](https://github.com/compare/...@sage/test-extended-client-gen-app@19.0.21) (2022-05-16)

### Bug Fixes


### Features


# [19.0.20](https://github.com/compare/...@sage/test-extended-client-gen-app@19.0.20) (2022-05-15)

### Bug Fixes


### Features


# [19.0.19](https://github.com/compare/...@sage/test-extended-client-gen-app@19.0.19) (2022-05-14)

### Bug Fixes


### Features


# [19.0.18](https://github.com/compare/...@sage/test-extended-client-gen-app@19.0.18) (2022-05-13)

### Bug Fixes


### Features


# [19.0.17](https://github.com/compare/...@sage/test-extended-client-gen-app@19.0.17) (2022-05-13)

### Bug Fixes


### Features


# [19.0.16](https://github.com/compare/...@sage/test-extended-client-gen-app@19.0.16) (2022-05-12)

### Bug Fixes


### Features


# [19.0.15](https://github.com/compare/...@sage/test-extended-client-gen-app@19.0.15) (2022-05-11)

### Bug Fixes


### Features


# [19.0.14](https://github.com/compare/...@sage/test-extended-client-gen-app@19.0.14) (2022-05-10)

### Bug Fixes


### Features


# [19.0.13](https://github.com/compare/...@sage/test-extended-client-gen-app@19.0.13) (2022-05-10)

### Bug Fixes


### Features


# [19.0.12](https://github.com/compare/...@sage/test-extended-client-gen-app@19.0.12) (2022-05-09)

### Bug Fixes


### Features


# [19.0.11](https://github.com/compare/...@sage/test-extended-client-gen-app@19.0.11) (2022-05-08)

### Bug Fixes


### Features


# [19.0.10](https://github.com/compare/...@sage/test-extended-client-gen-app@19.0.10) (2022-05-07)

### Bug Fixes


### Features


# [19.0.9](https://github.com/compare/...@sage/test-extended-client-gen-app@19.0.9) (2022-05-06)

### Bug Fixes


### Features


# [19.0.8](https://github.com/compare/...@sage/test-extended-client-gen-app@19.0.8) (2022-05-06)

### Bug Fixes


### Features


# [19.0.7](https://github.com/compare/...@sage/test-extended-client-gen-app@19.0.7) (2022-05-04)

### Bug Fixes


### Features


# [19.0.6](https://github.com/compare/...@sage/test-extended-client-gen-app@19.0.6) (2022-05-03)

### Bug Fixes


### Features


# [19.0.5](https://github.com/compare/...@sage/test-extended-client-gen-app@19.0.5) (2022-05-02)

### Bug Fixes


### Features


# [19.0.4](https://github.com/compare/...@sage/test-extended-client-gen-app@19.0.4) (2022-05-01)

### Bug Fixes


### Features


# [19.0.3](https://github.com/compare/...@sage/test-extended-client-gen-app@19.0.3) (2022-04-30)

### Bug Fixes


### Features


# [19.0.2](https://github.com/compare/...@sage/test-extended-client-gen-app@19.0.2) (2022-04-29)

### Bug Fixes


### Features


# [19.0.1](https://github.com/compare/...@sage/test-extended-client-gen-app@19.0.1) (2022-04-28)

### Bug Fixes


### Features


# [19.0.0](https://github.com/compare/...@sage/test-extended-client-gen-app@19.0.0) (2022-04-28)

### Bug Fixes


### Features


# [18.0.37](https://github.com/compare/...@sage/test-extended-client-gen-app@18.0.37) (2022-04-28)

### Bug Fixes


### Features


# [18.0.36](https://github.com/compare/...@sage/test-extended-client-gen-app@18.0.36) (2022-04-27)

### Bug Fixes


### Features


# [18.0.35](https://github.com/compare/...@sage/test-extended-client-gen-app@18.0.35) (2022-04-27)

### Bug Fixes


### Features


# [18.0.34](https://github.com/compare/...@sage/test-extended-client-gen-app@18.0.34) (2022-04-26)

### Bug Fixes


### Features


# [18.0.33](https://github.com/compare/...@sage/test-extended-client-gen-app@18.0.33) (2022-04-26)

### Bug Fixes


### Features


# [18.0.32](https://github.com/compare/...@sage/test-extended-client-gen-app@18.0.32) (2022-04-25)

### Bug Fixes


### Features


# [18.0.31](https://github.com/compare/...@sage/test-extended-client-gen-app@18.0.31) (2022-04-25)

### Bug Fixes


### Features


# [18.0.30](https://github.com/compare/...@sage/test-extended-client-gen-app@18.0.30) (2022-04-21)

### Bug Fixes


### Features


# [18.0.29](https://github.com/compare/...@sage/test-extended-client-gen-app@18.0.29) (2022-04-21)

### Bug Fixes


### Features


# [18.0.28](https://github.com/compare/...@sage/test-extended-client-gen-app@18.0.28) (2022-04-20)

### Bug Fixes


### Features


# [18.0.27](https://github.com/compare/...@sage/test-extended-client-gen-app@18.0.27) (2022-04-20)

### Bug Fixes


### Features


# [18.0.26](https://github.com/compare/...@sage/test-extended-client-gen-app@18.0.26) (2022-04-18)

### Bug Fixes


### Features


# [18.0.25](https://github.com/compare/...@sage/test-extended-client-gen-app@18.0.25) (2022-04-18)

### Bug Fixes


### Features


# [18.0.24](https://github.com/compare/...@sage/test-extended-client-gen-app@18.0.24) (2022-04-16)

### Bug Fixes


### Features


# [18.0.23](https://github.com/compare/...@sage/test-extended-client-gen-app@18.0.23) (2022-04-15)

### Bug Fixes


### Features


# [18.0.22](https://github.com/compare/...@sage/test-extended-client-gen-app@18.0.22) (2022-04-14)

### Bug Fixes


### Features


# [18.0.21](https://github.com/compare/...@sage/test-extended-client-gen-app@18.0.21) (2022-04-13)

### Bug Fixes


### Features


# [18.0.20](https://github.com/compare/...@sage/test-extended-client-gen-app@18.0.20) (2022-04-12)

### Bug Fixes


### Features


# [18.0.19](https://github.com/compare/...@sage/test-extended-client-gen-app@18.0.19) (2022-04-11)

### Bug Fixes


### Features


# [18.0.18](https://github.com/compare/...@sage/test-extended-client-gen-app@18.0.18) (2022-04-10)

### Bug Fixes


### Features


# [18.0.17](https://github.com/compare/...@sage/test-extended-client-gen-app@18.0.17) (2022-04-09)

### Bug Fixes


### Features


# [18.0.16](https://github.com/compare/...@sage/test-extended-client-gen-app@18.0.16) (2022-04-08)

### Bug Fixes


### Features


# [18.0.15](https://github.com/compare/...@sage/test-extended-client-gen-app@18.0.15) (2022-04-07)

### Bug Fixes


### Features


# [18.0.14](https://github.com/compare/...@sage/test-extended-client-gen-app@18.0.14) (2022-04-06)

### Bug Fixes


### Features


# [18.0.13](https://github.com/compare/...@sage/test-extended-client-gen-app@18.0.13) (2022-04-05)

### Bug Fixes


### Features


# [18.0.12](https://github.com/compare/...@sage/test-extended-client-gen-app@18.0.12) (2022-04-04)

### Bug Fixes


### Features


# [18.0.11](https://github.com/compare/...@sage/test-extended-client-gen-app@18.0.11) (2022-04-03)

### Bug Fixes


### Features


# [18.0.10](https://github.com/compare/...@sage/test-extended-client-gen-app@18.0.10) (2022-04-02)

### Bug Fixes


### Features


# [18.0.9](https://github.com/compare/...@sage/test-extended-client-gen-app@18.0.9) (2022-04-01)

### Bug Fixes


### Features


# [18.0.8](https://github.com/compare/...@sage/test-extended-client-gen-app@18.0.8) (2022-03-31)

### Bug Fixes


### Features


# [18.0.7](https://github.com/compare/...@sage/test-extended-client-gen-app@18.0.7) (2022-03-31)

### Bug Fixes


### Features


# [18.0.6](https://github.com/compare/...@sage/test-extended-client-gen-app@18.0.6) (2022-03-30)

### Bug Fixes


### Features


# [18.0.5](https://github.com/compare/...@sage/test-extended-client-gen-app@18.0.5) (2022-03-29)

### Bug Fixes


### Features


# [18.0.4](https://github.com/compare/...@sage/test-extended-client-gen-app@18.0.4) (2022-03-28)

### Bug Fixes


### Features


# [18.0.3](https://github.com/compare/...@sage/test-extended-client-gen-app@18.0.3) (2022-03-27)

### Bug Fixes


### Features


# [18.0.2](https://github.com/compare/...@sage/test-extended-client-gen-app@18.0.2) (2022-03-26)

### Bug Fixes


### Features


# [18.0.1](https://github.com/compare/...@sage/test-extended-client-gen-app@18.0.1) (2022-03-25)

### Bug Fixes


### Features


# [18.0.0](https://github.com/compare/...@sage/test-extended-client-gen-app@18.0.0) (2022-03-24)

### Bug Fixes


### Features


# [17.0.29](https://github.com/compare/...@sage/test-extended-client-gen-app@17.0.29) (2022-03-24)

### Bug Fixes


### Features


# [17.0.28](https://github.com/compare/...@sage/test-extended-client-gen-app@17.0.28) (2022-03-24)

### Bug Fixes


### Features


# [17.0.27](https://github.com/compare/...@sage/test-extended-client-gen-app@17.0.27) (2022-03-23)

### Bug Fixes


### Features


# [17.0.26](https://github.com/compare/...@sage/test-extended-client-gen-app@17.0.26) (2022-03-22)

### Bug Fixes


### Features


# [17.0.25](https://github.com/compare/...@sage/test-extended-client-gen-app@17.0.25) (2022-03-21)

### Bug Fixes


### Features


# [17.0.24](https://github.com/compare/...@sage/test-extended-client-gen-app@17.0.24) (2022-03-20)

### Bug Fixes


### Features


# [17.0.23](https://github.com/compare/...@sage/test-extended-client-gen-app@17.0.23) (2022-03-20)

### Bug Fixes


### Features


# [17.0.22](https://github.com/compare/...@sage/test-extended-client-gen-app@17.0.22) (2022-03-19)

### Bug Fixes


### Features


# [17.0.21](https://github.com/compare/...@sage/test-extended-client-gen-app@17.0.21) (2022-03-19)

### Bug Fixes


### Features


# [17.0.20](https://github.com/compare/...@sage/test-extended-client-gen-app@17.0.20) (2022-03-18)

### Bug Fixes


### Features


# [17.0.19](https://github.com/compare/...@sage/test-extended-client-gen-app@17.0.19) (2022-03-18)

### Bug Fixes


### Features


# [17.0.18](https://github.com/compare/...@sage/test-extended-client-gen-app@17.0.18) (2022-03-18)

### Bug Fixes


### Features


# [17.0.17](https://github.com/compare/...@sage/test-extended-client-gen-app@17.0.17) (2022-03-17)

### Bug Fixes


### Features


# [17.0.16](https://github.com/compare/...@sage/test-extended-client-gen-app@17.0.16) (2022-03-17)

### Bug Fixes


### Features


# [17.0.15](https://github.com/compare/...@sage/test-extended-client-gen-app@17.0.15) (2022-03-13)

### Bug Fixes


### Features


# [17.0.14](https://github.com/compare/...@sage/test-extended-client-gen-app@17.0.14) (2022-03-10)

### Bug Fixes


### Features


# [17.0.13](https://github.com/compare/...@sage/test-extended-client-gen-app@17.0.13) (2022-03-09)

### Bug Fixes


### Features


# [17.0.12](https://github.com/compare/...@sage/test-extended-client-gen-app@17.0.12) (2022-03-09)

### Bug Fixes


### Features


# [17.0.11](https://github.com/compare/...@sage/test-extended-client-gen-app@17.0.11) (2022-03-08)

### Bug Fixes


### Features


# [17.0.10](https://github.com/compare/...@sage/test-extended-client-gen-app@17.0.10) (2022-03-07)

### Bug Fixes


### Features


# [17.0.9](https://github.com/compare/...@sage/test-extended-client-gen-app@17.0.9) (2022-03-06)

### Bug Fixes


### Features


# [17.0.8](https://github.com/compare/...@sage/test-extended-client-gen-app@17.0.8) (2022-03-05)

### Bug Fixes


### Features


# [17.0.7](https://github.com/compare/...@sage/test-extended-client-gen-app@17.0.7) (2022-03-04)

### Bug Fixes


### Features


# [17.0.6](https://github.com/compare/...@sage/test-extended-client-gen-app@17.0.6) (2022-03-03)

### Bug Fixes


### Features


# [17.0.5](https://github.com/compare/...@sage/test-extended-client-gen-app@17.0.5) (2022-03-03)

### Bug Fixes


### Features


# [17.0.4](https://github.com/compare/...@sage/test-extended-client-gen-app@17.0.4) (2022-03-01)

### Bug Fixes


### Features


# [17.0.3](https://github.com/compare/...@sage/test-extended-client-gen-app@17.0.3) (2022-02-28)

### Bug Fixes


### Features


# [17.0.2](https://github.com/compare/...@sage/test-extended-client-gen-app@17.0.2) (2022-02-27)

### Bug Fixes


### Features


# [17.0.1](https://github.com/compare/...@sage/test-extended-client-gen-app@17.0.1) (2022-02-26)

### Bug Fixes


### Features


# [17.0.0](https://github.com/compare/...@sage/test-extended-client-gen-app@17.0.0) (2022-02-25)

### Bug Fixes


### Features


# [16.0.29](https://github.com/compare/...@sage/test-extended-client-gen-app@16.0.29) (2022-02-24)

### Bug Fixes


### Features


# [16.0.28](https://github.com/compare/...@sage/test-extended-client-gen-app@16.0.28) (2022-02-24)

### Bug Fixes


### Features


# [16.0.27](https://github.com/compare/...@sage/test-extended-client-gen-app@16.0.27) (2022-02-24)

### Bug Fixes


### Features


# [16.0.26](https://github.com/compare/...@sage/test-extended-client-gen-app@16.0.26) (2022-02-23)

### Bug Fixes


### Features


# [16.0.25](https://github.com/compare/...@sage/test-extended-client-gen-app@16.0.25) (2022-02-22)

### Bug Fixes


### Features


# [16.0.24](https://github.com/compare/...@sage/test-extended-client-gen-app@16.0.24) (2022-02-21)

### Bug Fixes


### Features


# [16.0.23](https://github.com/compare/...@sage/test-extended-client-gen-app@16.0.23) (2022-02-20)

### Bug Fixes


### Features


# [16.0.22](https://github.com/compare/...@sage/test-extended-client-gen-app@16.0.22) (2022-02-19)

### Bug Fixes


### Features


# [16.0.21](https://github.com/compare/...@sage/test-extended-client-gen-app@16.0.21) (2022-02-18)

### Bug Fixes


### Features


# [16.0.20](https://github.com/compare/...@sage/test-extended-client-gen-app@16.0.20) (2022-02-17)

### Bug Fixes


### Features


# [16.0.19](https://github.com/compare/...@sage/test-extended-client-gen-app@16.0.19) (2022-02-16)

### Bug Fixes


### Features


# [16.0.18](https://github.com/compare/...@sage/test-extended-client-gen-app@16.0.18) (2022-02-15)

### Bug Fixes


### Features


# [16.0.17](https://github.com/compare/...@sage/test-extended-client-gen-app@16.0.17) (2022-02-13)

### Bug Fixes


### Features


# [16.0.16](https://github.com/compare/...@sage/test-extended-client-gen-app@16.0.16) (2022-02-12)

### Bug Fixes


### Features


# [16.0.15](https://github.com/compare/...@sage/test-extended-client-gen-app@16.0.15) (2022-02-11)

### Bug Fixes


### Features


# [16.0.14](https://github.com/compare/...@sage/test-extended-client-gen-app@16.0.14) (2022-02-10)

### Bug Fixes


### Features


# [16.0.13](https://github.com/compare/...@sage/test-extended-client-gen-app@16.0.13) (2022-02-10)

### Bug Fixes


### Features


# [16.0.12](https://github.com/compare/...@sage/test-extended-client-gen-app@16.0.12) (2022-02-08)

### Bug Fixes


### Features


# [16.0.11](https://github.com/compare/...@sage/test-extended-client-gen-app@16.0.11) (2022-02-07)

### Bug Fixes


### Features


# [16.0.10](https://github.com/compare/...@sage/test-extended-client-gen-app@16.0.10) (2022-02-07)

### Bug Fixes


### Features


# [16.0.9](https://github.com/compare/...@sage/test-extended-client-gen-app@16.0.9) (2022-02-06)

### Bug Fixes


### Features


# [16.0.8](https://github.com/compare/...@sage/test-extended-client-gen-app@16.0.8) (2022-02-05)

### Bug Fixes


### Features


# [16.0.7](https://github.com/compare/...@sage/test-extended-client-gen-app@16.0.7) (2022-02-04)

### Bug Fixes


### Features


# [16.0.6](https://github.com/compare/...@sage/test-extended-client-gen-app@16.0.6) (2022-02-04)

### Bug Fixes


### Features


# [16.0.5](https://github.com/compare/...@sage/test-extended-client-gen-app@16.0.5) (2022-02-02)

### Bug Fixes


### Features


# [16.0.4](https://github.com/compare/...@sage/test-extended-client-gen-app@16.0.4) (2022-02-01)

### Bug Fixes


### Features


# [16.0.3](https://github.com/compare/...@sage/test-extended-client-gen-app@16.0.3) (2022-01-31)

### Bug Fixes


### Features


# [16.0.2](https://github.com/compare/...@sage/test-extended-client-gen-app@16.0.2) (2022-01-30)

### Bug Fixes


### Features


# [16.0.1](https://github.com/compare/...@sage/test-extended-client-gen-app@16.0.1) (2022-01-29)

### Bug Fixes


### Features


# [16.0.0](https://github.com/compare/...@sage/test-extended-client-gen-app@16.0.0) (2022-01-29)

### Bug Fixes


### Features


# [15.0.36](https://github.com/compare/...@sage/test-extended-client-gen-app@15.0.36) (2022-01-28)

### Bug Fixes


### Features


# [15.0.35](https://github.com/compare/...@sage/test-extended-client-gen-app@15.0.35) (2022-01-28)

### Bug Fixes


### Features


# [15.0.34](https://github.com/compare/...@sage/test-extended-client-gen-app@15.0.34) (2022-01-28)

### Bug Fixes


### Features


# [15.0.33](https://github.com/compare/...@sage/test-extended-client-gen-app@15.0.33) (2022-01-26)

### Bug Fixes


### Features


# [15.0.32](https://github.com/compare/...@sage/test-extended-client-gen-app@15.0.32) (2022-01-26)

### Bug Fixes


### Features


# [15.0.31](https://github.com/compare/...@sage/test-extended-client-gen-app@15.0.31) (2022-01-25)

### Bug Fixes


### Features


# [15.0.30](https://github.com/compare/...@sage/test-extended-client-gen-app@15.0.30) (2022-01-25)

### Bug Fixes


### Features


# [15.0.29](https://github.com/compare/...@sage/test-extended-client-gen-app@15.0.29) (2022-01-24)

### Bug Fixes


### Features


# [15.0.28](https://github.com/compare/...@sage/test-extended-client-gen-app@15.0.28) (2022-01-24)

### Bug Fixes


### Features


# [15.0.27](https://github.com/compare/...@sage/test-extended-client-gen-app@15.0.27) (2022-01-23)

### Bug Fixes


### Features


# [15.0.26](https://github.com/compare/...@sage/test-extended-client-gen-app@15.0.26) (2022-01-23)

### Bug Fixes


### Features


# [15.0.25](https://github.com/compare/...@sage/test-extended-client-gen-app@15.0.25) (2022-01-21)

### Bug Fixes


### Features


# [15.0.24](https://github.com/compare/...@sage/test-extended-client-gen-app@15.0.24) (2022-01-18)

### Bug Fixes


### Features


# [15.0.23](https://github.com/compare/...@sage/test-extended-client-gen-app@15.0.23) (2022-01-18)

### Bug Fixes


### Features


# [15.0.22](https://github.com/compare/...@sage/test-extended-client-gen-app@15.0.22) (2022-01-17)

### Bug Fixes


### Features


# [15.0.21](https://github.com/compare/...@sage/test-extended-client-gen-app@15.0.21) (2022-01-16)

### Bug Fixes


### Features


# [15.0.20](https://github.com/compare/...@sage/test-extended-client-gen-app@15.0.20) (2022-01-15)

### Bug Fixes


### Features


# [15.0.19](https://github.com/compare/...@sage/test-extended-client-gen-app@15.0.19) (2022-01-14)

### Bug Fixes


### Features


# [15.0.18](https://github.com/compare/...@sage/test-extended-client-gen-app@15.0.18) (2022-01-14)

### Bug Fixes


### Features


# [15.0.17](https://github.com/compare/...@sage/test-extended-client-gen-app@15.0.17) (2022-01-13)

### Bug Fixes


### Features


# [15.0.16](https://github.com/compare/...@sage/test-extended-client-gen-app@15.0.16) (2022-01-12)

### Bug Fixes


### Features


# [15.0.15](https://github.com/compare/...@sage/test-extended-client-gen-app@15.0.15) (2022-01-11)

### Bug Fixes


### Features


# [15.0.14](https://github.com/compare/...@sage/test-extended-client-gen-app@15.0.14) (2022-01-11)

### Bug Fixes


### Features


# [15.0.13](https://github.com/compare/...@sage/test-extended-client-gen-app@15.0.13) (2022-01-11)

### Bug Fixes


### Features


# [15.0.12](https://github.com/compare/...@sage/test-extended-client-gen-app@15.0.12) (2022-01-10)

### Bug Fixes


### Features


# [15.0.11](https://github.com/compare/...@sage/test-extended-client-gen-app@15.0.11) (2022-01-09)

### Bug Fixes


### Features


# [15.0.10](https://github.com/compare/...@sage/test-extended-client-gen-app@15.0.10) (2022-01-08)

### Bug Fixes


### Features


# [15.0.9](https://github.com/compare/...@sage/test-extended-client-gen-app@15.0.9) (2022-01-07)

### Bug Fixes


### Features


# [15.0.8](https://github.com/compare/...@sage/test-extended-client-gen-app@15.0.8) (2022-01-07)

### Bug Fixes


### Features


# [15.0.7](https://github.com/compare/...@sage/test-extended-client-gen-app@15.0.7) (2022-01-06)

### Bug Fixes


### Features


# [15.0.6](https://github.com/compare/...@sage/test-extended-client-gen-app@15.0.6) (2022-01-05)

### Bug Fixes


### Features


# [15.0.5](https://github.com/compare/...@sage/test-extended-client-gen-app@15.0.5) (2022-01-04)

### Bug Fixes


### Features


# [15.0.4](https://github.com/compare/...@sage/test-extended-client-gen-app@15.0.4) (2022-01-03)

### Bug Fixes


### Features


# [15.0.3](https://github.com/compare/...@sage/test-extended-client-gen-app@15.0.3) (2022-01-02)

### Bug Fixes


### Features


# [15.0.2](https://github.com/compare/...@sage/test-extended-client-gen-app@15.0.2) (2022-01-01)

### Bug Fixes


### Features


# [15.0.1](https://github.com/compare/...@sage/test-extended-client-gen-app@15.0.1) (2021-12-31)

### Bug Fixes


### Features


# [15.0.0](https://github.com/compare/...@sage/test-extended-client-gen-app@15.0.0) (2021-12-30)

### Bug Fixes


### Features


# [14.0.29](https://github.com/compare/...@sage/test-extended-client-gen-app@14.0.29) (2021-12-30)

### Bug Fixes


### Features


# [14.0.28](https://github.com/compare/...@sage/test-extended-client-gen-app@14.0.28) (2021-12-29)

### Bug Fixes


### Features


# [14.0.27](https://github.com/compare/...@sage/test-extended-client-gen-app@14.0.27) (2021-12-28)

### Bug Fixes


### Features


# [14.0.26](https://github.com/compare/...@sage/test-extended-client-gen-app@14.0.26) (2021-12-27)

### Bug Fixes


### Features


# [14.0.25](https://github.com/compare/...@sage/test-extended-client-gen-app@14.0.25) (2021-12-27)

### Bug Fixes


### Features


# [14.0.24](https://github.com/compare/...@sage/test-extended-client-gen-app@14.0.24) (2021-12-22)

### Bug Fixes


### Features


# [14.0.23](https://github.com/compare/...@sage/test-extended-client-gen-app@14.0.23) (2021-12-21)

### Bug Fixes


### Features

* **client-gen:** fix package patch version build-api XT-14252 ([#3947](https://github.com/issues/3947))  ([1be1aba](https://github.com/commit/1be1abaac7b19b16f430d9483272b971152b8cbb))

# [14.0.0](https://github.com/compare/...@sage/test-extended-client-gen-app@14.0.0) (2021-12-15)

### Bug Fixes


### Features


# [14.0.0](https://github.com/compare/...@sage/test-extended-client-gen-app@14.0.0) (2021-12-14)

### Bug Fixes


### Features


# [14.0.0](https://github.com/compare/...@sage/test-extended-client-gen-app@14.0.0) (2021-12-13)

### Bug Fixes


### Features


# [14.0.0](https://github.com/compare/...@sage/test-extended-client-gen-app@14.0.0) (2021-12-12)

### Bug Fixes


### Features


# [14.0.0](https://github.com/compare/...@sage/test-extended-client-gen-app@14.0.0) (2021-12-11)

### Bug Fixes


### Features


# [14.0.0](https://github.com/compare/...@sage/test-extended-client-gen-app@14.0.0) (2021-12-10)

### Bug Fixes


### Features


# [14.0.0](https://github.com/compare/...@sage/test-extended-client-gen-app@14.0.0) (2021-12-09)

### Bug Fixes


### Features


# [14.0.0](https://github.com/compare/...@sage/test-extended-client-gen-app@14.0.0) (2021-12-09)

### Bug Fixes


### Features


# [14.0.0](https://github.com/compare/...@sage/test-extended-client-gen-app@14.0.0) (2021-12-08)

### Bug Fixes


### Features


# [14.0.0](https://github.com/compare/...@sage/test-extended-client-gen-app@14.0.0) (2021-12-07)

### Bug Fixes


### Features


# [14.0.0](https://github.com/compare/...@sage/test-extended-client-gen-app@14.0.0) (2021-12-07)

### Bug Fixes


### Features


# [14.0.0](https://github.com/compare/...@sage/test-extended-client-gen-app@14.0.0) (2021-12-06)

### Bug Fixes


### Features


# [14.0.0](https://github.com/compare/...@sage/test-extended-client-gen-app@14.0.0) (2021-12-05)

### Bug Fixes


### Features


# [14.0.0](https://github.com/compare/...@sage/test-extended-client-gen-app@14.0.0) (2021-12-04)

### Bug Fixes


### Features


# [14.0.0](https://github.com/compare/...@sage/test-extended-client-gen-app@14.0.0) (2021-12-03)

### Bug Fixes


### Features


# [14.0.0](https://github.com/compare/...@sage/test-extended-client-gen-app@14.0.0) (2021-12-03)

### Bug Fixes


### Features


# [14.0.0](https://github.com/compare/...@sage/test-extended-client-gen-app@14.0.0) (2021-12-02)

### Bug Fixes


### Features


# [14.0.0](https://github.com/compare/...@sage/test-extended-client-gen-app@14.0.0) (2021-12-02)

### Bug Fixes


### Features


# [14.0.0](https://github.com/compare/...@sage/test-extended-client-gen-app@14.0.0) (2021-12-02)

### Bug Fixes


### Features


# [14.0.0](https://github.com/compare/...@sage/test-extended-client-gen-app@14.0.0) (2021-12-02)

### Bug Fixes


### Features


# [14.0.0](https://github.com/compare/...@sage/test-extended-client-gen-app@14.0.0) (2021-12-01)

### Bug Fixes


### Features


# [14.0.0](https://github.com/compare/...@sage/test-extended-client-gen-app@14.0.0) (2021-11-29)

### Bug Fixes


### Features


# [14.0.0](https://github.com/compare/...@sage/test-extended-client-gen-app@14.0.0) (2021-11-29)

### Bug Fixes


### Features


# [13.0.0](https://github.com/compare/...@sage/test-extended-client-gen-app@13.0.0) (2021-11-29)

### Bug Fixes


### Features


# [13.0.0](https://github.com/compare/...@sage/test-extended-client-gen-app@13.0.0) (2021-11-28)

### Bug Fixes


### Features


# [13.0.0](https://github.com/compare/...@sage/test-extended-client-gen-app@13.0.0) (2021-11-27)

### Bug Fixes


### Features


# [13.0.0](https://github.com/compare/...@sage/test-extended-client-gen-app@13.0.0) (2021-11-26)

### Bug Fixes


### Features


# [13.0.0](https://github.com/compare/...@sage/test-extended-client-gen-app@13.0.0) (2021-11-25)

### Bug Fixes


### Features


# [13.0.0](https://github.com/compare/...@sage/test-extended-client-gen-app@13.0.0) (2021-11-24)

### Bug Fixes


### Features


# [13.0.0](https://github.com/compare/...@sage/test-extended-client-gen-app@13.0.0) (2021-11-23)

### Bug Fixes


### Features


# [13.0.0](https://github.com/compare/...@sage/test-extended-client-gen-app@13.0.0) (2021-11-22)

### Bug Fixes


### Features


# [13.0.0](https://github.com/compare/...@sage/test-extended-client-gen-app@13.0.0) (2021-11-22)

### Bug Fixes


### Features


# [13.0.0](https://github.com/compare/...@sage/test-extended-client-gen-app@13.0.0) (2021-11-19)

### Bug Fixes


### Features


# [13.0.0](https://github.com/compare/...@sage/test-extended-client-gen-app@13.0.0) (2021-11-18)

### Bug Fixes


### Features


# [13.0.0](https://github.com/compare/...@sage/test-extended-client-gen-app@13.0.0) (2021-11-18)

### Bug Fixes


### Features


# [13.0.0](https://github.com/compare/...@sage/test-extended-client-gen-app@13.0.0) (2021-11-17)

### Bug Fixes


### Features


# [13.0.0](https://github.com/compare/...@sage/test-extended-client-gen-app@13.0.0) (2021-11-17)

### Bug Fixes


### Features


# [13.0.0](https://github.com/compare/...@sage/test-extended-client-gen-app@13.0.0) (2021-11-14)

### Bug Fixes


### Features


# [13.0.0](https://github.com/compare/...@sage/test-extended-client-gen-app@13.0.0) (2021-11-13)

### Bug Fixes


### Features


# [13.0.0](https://github.com/compare/...@sage/test-extended-client-gen-app@13.0.0) (2021-11-12)

### Bug Fixes


### Features


# [13.0.0](https://github.com/compare/...@sage/test-extended-client-gen-app@13.0.0) (2021-11-11)

### Bug Fixes


### Features


# [13.0.0](https://github.com/compare/...@sage/test-extended-client-gen-app@13.0.0) (2021-11-10)

### Bug Fixes


### Features


# [13.0.0](https://github.com/compare/...@sage/test-extended-client-gen-app@13.0.0) (2021-11-09)

### Bug Fixes


### Features


# [13.0.0](https://github.com/compare/...@sage/test-extended-client-gen-app@13.0.0) (2021-11-09)

### Bug Fixes


### Features


# [13.0.0](https://github.com/compare/...@sage/test-extended-client-gen-app@13.0.0) (2021-11-09)

### Bug Fixes


### Features


# [13.0.0](https://github.com/compare/...@sage/test-extended-client-gen-app@13.0.0) (2021-11-08)

### Bug Fixes


### Features


# [13.0.0](https://github.com/compare/...@sage/test-extended-client-gen-app@13.0.0) (2021-11-07)

### Bug Fixes


### Features


# [13.0.0](https://github.com/compare/...@sage/test-extended-client-gen-app@13.0.0) (2021-11-06)

### Bug Fixes


### Features


# [13.0.0](https://github.com/compare/...@sage/test-extended-client-gen-app@13.0.0) (2021-11-05)

### Bug Fixes


### Features


# [13.0.0](https://github.com/compare/...@sage/test-extended-client-gen-app@13.0.0) (2021-11-04)

### Bug Fixes


### Features


# [13.0.0](https://github.com/compare/...@sage/test-extended-client-gen-app@13.0.0) (2021-11-03)

### Bug Fixes


### Features


# [13.0.0](https://github.com/compare/...@sage/test-extended-client-gen-app@13.0.0) (2021-11-03)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-extended-client-gen-app@12.0.0) (2021-11-02)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-extended-client-gen-app@12.0.0) (2021-10-31)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-extended-client-gen-app@12.0.0) (2021-10-30)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-extended-client-gen-app@12.0.0) (2021-10-29)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-extended-client-gen-app@12.0.0) (2021-10-28)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-extended-client-gen-app@12.0.0) (2021-10-28)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-extended-client-gen-app@12.0.0) (2021-10-26)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-extended-client-gen-app@12.0.0) (2021-10-26)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-extended-client-gen-app@12.0.0) (2021-10-25)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-extended-client-gen-app@12.0.0) (2021-10-25)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-extended-client-gen-app@12.0.0) (2021-10-24)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-extended-client-gen-app@12.0.0) (2021-10-24)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-extended-client-gen-app@12.0.0) (2021-10-23)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-extended-client-gen-app@12.0.0) (2021-10-22)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-extended-client-gen-app@12.0.0) (2021-10-22)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-extended-client-gen-app@12.0.0) (2021-10-20)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-extended-client-gen-app@12.0.0) (2021-10-19)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-extended-client-gen-app@12.0.0) (2021-10-18)

### Bug Fixes


### Features

* XT-12338 umbrella package for docker image ([#3250](https://github.com/issues/3250))  ([bfc5d6b](https://github.com/commit/bfc5d6b41e878c383489b6e2a7bd316001c05f25))

# [12.0.0](https://github.com/compare/...@sage/test-extended-client-gen-app@12.0.0) (2021-10-17)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-extended-client-gen-app@12.0.0) (2021-10-17)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-extended-client-gen-app@12.0.0) (2021-10-16)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-extended-client-gen-app@12.0.0) (2021-10-15)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-extended-client-gen-app@12.0.0) (2021-10-14)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-extended-client-gen-app@12.0.0) (2021-10-13)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-extended-client-gen-app@12.0.0) (2021-10-12)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-extended-client-gen-app@12.0.0) (2021-10-12)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-extended-client-gen-app@12.0.0) (2021-10-10)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-extended-client-gen-app@12.0.0) (2021-10-09)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-extended-client-gen-app@12.0.0) (2021-10-08)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-extended-client-gen-app@12.0.0) (2021-10-08)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-extended-client-gen-app@12.0.0) (2021-10-07)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-extended-client-gen-app@12.0.0) (2021-10-06)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-extended-client-gen-app@12.0.0) (2021-10-05)

### Bug Fixes

* platform test fixes after major version bump ([#3026](https://github.com/issues/3026))  ([a55ff9d](https://github.com/commit/a55ff9d41972d5bf69be2d167f3a686e36741aaf))
* XT-999999 fix package versions ([#1780](https://github.com/issues/1780))  ([4bd5d4a](https://github.com/commit/4bd5d4a02fc8959893403c22b7d702f664082be8))

### Features

* XT-4849 envelope handling ([#762](https://github.com/issues/762))  ([13ee155](https://github.com/commit/13ee155498e9e0b65f9313630fce87c2e0364849))
* **core:** XT-10028 test-app renamed in test-schema-gen-app ([#2500](https://github.com/issues/2500))  ([a03e44b](https://github.com/commit/a03e44b6157fffb6b9efd51320a16ace8e264772))
* **core:** XT-10028 upgrade dependencies step 2 ([#2479](https://github.com/issues/2479))  ([cb2053e](https://github.com/commit/cb2053ea4022f2926722e7f1e4aa9eb31a6951c6))
* **core:** XT-9999 fixed packages ([#2090](https://github.com/issues/2090))  ([a0ef4bb](https://github.com/commit/a0ef4bb4f8ab1eb9113e20ba3f98cb97ac5c7fe2))
* **xtrem-client-gen:** XT-4667 add new binding clientNode interface ([#1951](https://github.com/issues/1951))  ([a1b6e63](https://github.com/commit/a1b6e63628240a47899e7951be0cc27eea453b97))

# [11.0.0](https://github.com/Sage-ERP-X3/xtrem/compare/...@sage/test-extended-client-gen-app@11.0.0) (2021-09-22)

### Bug Fixes

* XT-999999 fix package versions ([#1780](https://github.com/Sage-ERP-X3/xtrem/issues/1780))  ([4bd5d4a](https://github.com/Sage-ERP-X3/xtrem/commit/4bd5d4a02fc8959893403c22b7d702f664082be8))

### Features

* XT-4849 envelope handling ([#762](https://github.com/Sage-ERP-X3/xtrem/issues/762))  ([13ee155](https://github.com/Sage-ERP-X3/xtrem/commit/13ee155498e9e0b65f9313630fce87c2e0364849))
* **core:** XT-10028 test-app renamed in test-schema-gen-app ([#2500](https://github.com/Sage-ERP-X3/xtrem/issues/2500))  ([a03e44b](https://github.com/Sage-ERP-X3/xtrem/commit/a03e44b6157fffb6b9efd51320a16ace8e264772))
* **core:** XT-10028 upgrade dependencies step 2 ([#2479](https://github.com/Sage-ERP-X3/xtrem/issues/2479))  ([cb2053e](https://github.com/Sage-ERP-X3/xtrem/commit/cb2053ea4022f2926722e7f1e4aa9eb31a6951c6))
* **core:** XT-9999 fixed packages ([#2090](https://github.com/Sage-ERP-X3/xtrem/issues/2090))  ([a0ef4bb](https://github.com/Sage-ERP-X3/xtrem/commit/a0ef4bb4f8ab1eb9113e20ba3f98cb97ac5c7fe2))
* **xtrem-client-gen:** XT-4667 add new binding clientNode interface ([#1951](https://github.com/Sage-ERP-X3/xtrem/issues/1951))  ([a1b6e63](https://github.com/Sage-ERP-X3/xtrem/commit/a1b6e63628240a47899e7951be0cc27eea453b97))

