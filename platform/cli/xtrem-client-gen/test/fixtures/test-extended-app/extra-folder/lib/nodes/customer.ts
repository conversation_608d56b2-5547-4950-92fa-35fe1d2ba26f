import { decorators, Node, StringDataType } from '@sage/xtrem-core';

@decorators.node<Customer>({
    isPublished: true,
    storage: 'sql',
    indexes: [{ orderBy: { code: +1 }, isUnique: true }],
})
export class Customer extends Node {
    @decorators.stringProperty<Customer, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringDataType({ maxLength: 10 }),
    })
    readonly code: Promise<string>;

    @decorators.stringProperty<Customer, 'name'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringDataType({ maxLength: 50 }),
    })
    readonly name: Promise<string>;
}
