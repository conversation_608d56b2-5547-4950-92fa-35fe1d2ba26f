import { Context, decorators, NodeExtension, Reference } from '@sage/xtrem-core';
// eslint-disable-next-line import/no-relative-packages
import * as testApp from '../../../../test-app/extra-folder/index';
import * as testExtendedApp from '../../index';

@decorators.nodeExtension<DocumentExtension>({
    extends: () => testApp.nodes.Document,
    indexes: [],
})
export class DocumentExtension extends NodeExtension<testApp.nodes.Document> {
    @decorators.referenceProperty<DocumentExtension, 'extraAddress'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        node: () => testApp.nodes.Address,
    })
    readonly extraAddress: Reference<testApp.nodes.Address | null>;

    @decorators.referenceProperty<DocumentExtension, 'customer'>({
        isPublished: true,
        isStored: true,
        node: () => testExtendedApp.nodes.Customer,
    })
    readonly customer: Reference<testExtendedApp.nodes.Customer>;

    @decorators.query<typeof DocumentExtension, 'extensionQuery'>({
        isPublished: true,
        parameters: [{ name: 'id', type: 'string', isMandatory: true }],
        return: {
            type: 'instance',
            node() {
                return testApp.nodes.Document;
            },
        },
    })
    static extensionQuery(context: Context, id: string): Promise<testApp.nodes.Document> {
        return context.read(testApp.nodes.Document, { id });
    }
}

declare module '../../../../test-app/extra-folder/lib/nodes/document' {
    export interface Document extends DocumentExtension {}
}
