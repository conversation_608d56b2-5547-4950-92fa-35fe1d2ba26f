declare module '@sage/test-extended-client-gen-app-api-partial' {
    import type {
        Address,
        AddressBinding,
        AddressInput,
        Document,
        DocumentInput,
        DocumentLine,
        DocumentLineBinding,
        DocumentLineInput,
        Package as SageTestClientGenApp$Package,
    } from '@sage/test-client-gen-app-api';
    import type {
        ClientCollection,
        ClientNode,
        ClientNodeInput,
        GetDefaultsOperation,
        Operation as Node$Operation,
        QueryOperation,
        decimal,
        integer,
    } from '@sage/xtrem-client';
    export interface Customer extends ClientNode {
        code: string;
        name: string;
    }
    export interface CustomerInput extends ClientNodeInput {
        code?: string;
        name?: string;
    }
    export interface CustomerBinding extends ClientNode {
        code: string;
        name: string;
    }
    export interface DocumentExtension {
        _constructor: string;
        id: string;
        status: StatusEnum;
        statusList: StatusEnum[];
        numberList: integer[];
        stringList: string[];
        amount: string;
        address: Address;
        addresses: Address[];
        anyLines: ClientCollection<DocumentLine>;
        json: string;
        isStoredOutput: string;
        extraAddress: Address;
        customer: Customer;
    }
    export interface DocumentInputExtension {
        _constructor?: string;
        id?: string;
        status?: StatusEnum;
        statusList?: StatusEnum[];
        numberList?: (integer | string)[];
        stringList?: string[];
        amount?: decimal | string;
        address?: AddressInput;
        addresses?: (integer | string)[];
        lines?: Partial<DocumentLineInput>[];
        transient?: string;
        transientRef?: integer | string;
        transientLines?: Partial<DocumentLineInput>[];
        json?: string;
        extraAddress?: integer | string;
        customer?: integer | string;
    }
    export interface DocumentBindingExtension {
        _constructor: string;
        id: string;
        status: StatusEnum;
        statusList: StatusEnum[];
        numberList: integer[];
        stringList: string[];
        amount: string;
        address: AddressBinding;
        addresses: Address[];
        lines: ClientCollection<DocumentLineBinding>;
        transient: string;
        transientRef: AddressBinding;
        transientLines: ClientCollection<DocumentLineBinding>;
        json: any;
        isStoredOutput: string;
        extraAddress: Address;
        customer: Customer;
    }
    export interface DocumentExtension$Queries {
        extensionQuery: Node$Operation<
            {
                id: string;
            },
            Document
        >;
    }
    export interface DocumentExtension$Lookups {
        extraAddress: QueryOperation<Address>;
        customer: QueryOperation<Customer>;
    }
    export interface DocumentExtension$Operations {
        queries: DocumentExtension$Queries;
        lookups(dataOrId: string | { data: DocumentInput }): DocumentExtension$Lookups;
        getDefaults: GetDefaultsOperation<Document>;
    }
    export interface Package {
        '@sage/test-extended-client-gen-app/Customer': Customer;
    }
    export interface GraphApi extends Package, SageTestClientGenApp$Package {}
}
declare module '@sage/test-extended-client-gen-app-api' {
    export type * from '@sage/test-extended-client-gen-app-api-partial';
}
declare module '@sage/test-client-gen-app-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/test-extended-client-gen-app-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/test-client-gen-app-api-partial' {
    import type {
        DocumentBindingExtension,
        DocumentExtension,
        DocumentExtension$Lookups,
        DocumentExtension$Operations,
        DocumentExtension$Queries,
        DocumentInputExtension,
    } from '@sage/test-extended-client-gen-app-api';
    export interface Document extends DocumentExtension {}
    export interface DocumentBinding extends DocumentBindingExtension {}
    export interface DocumentInput extends DocumentInputExtension {}
    export interface Document$Lookups extends DocumentExtension$Lookups {}
    export interface Document$Queries extends DocumentExtension$Queries {}
    export interface Document$Operations extends DocumentExtension$Operations {}
}
