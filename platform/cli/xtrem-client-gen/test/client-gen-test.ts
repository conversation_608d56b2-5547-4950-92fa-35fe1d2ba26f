import { assert } from 'chai';
import * as fs from 'fs';
import * as fsp from 'path';
import { generateClient<PERSON><PERSON>, generateClientPackage, generateClientPackageJson } from '../lib/index';
import { createTestApp, createTestExtendedApp, setup } from './fixtures/index';

import Module = require('module');

setup();

// hack to help Application resolve our fixture modules when it analyzes dependencies
function hackModuleResolve() {
    const resolveFileName = (Module as any)._resolveFilename;
    (Module as any)._resolveFilename = function resolveFilename(request: string) {
        if (request === '@sage/test-client-gen-app/package.json')
            return require.resolve('./fixtures/test-app/package.json');
        if (request === '@sage/test-extended-client-gen-app/package.json')
            return require.resolve('./fixtures/test-extended-app/package.json');
        if (request === '@sage/xtrem-service') return require.resolve('../../xtrem-service');
        if (request === '@sage/test-client-gen-app/build/application') {
            return require.resolve('./fixtures/test-app/extra-folder/application');
        }
        // eslint-disable-next-line prefer-rest-params
        return resolveFileName.apply(this, arguments);
    };
}

// execute this one first, before loading test-extended-app, as the extension modifies test-app.
describe('client package generator', () => {
    it('can generate complete client package directory', async () => {
        const app = await createTestApp();
        // Paths were the files will be generated
        // These paths are under `build` because fixtures are not setup as a real project.
        // In a real project, the api dir will be <projectRoot>/api
        const genDir = fsp.join(__dirname, './fixtures/test-app/api');
        const genApiPath = fsp.join(genDir, 'api.d.ts');
        const genPackageJsonPath = fsp.join(genDir, 'package.json');
        // paths where the expected results are
        const refDir = fsp.join(__dirname, './fixtures/test-app/extra-folder/api');
        const refApiPath = fsp.join(refDir, 'api.d.ts');
        const refPackageJsonPath = fsp.join(refDir, 'package.json');
        function cleanup() {
            if (fs.existsSync(genApiPath)) fs.unlinkSync(genApiPath);
            if (fs.existsSync(genPackageJsonPath)) fs.unlinkSync(genPackageJsonPath);
        }

        // Delete files if they were created by a previous run
        cleanup();
        await generateClientPackage(app);
        checkFile(genApiPath, refApiPath);
        checkFile(genPackageJsonPath, refPackageJsonPath, true);
        cleanup();
    });
});

describe('client api generator', () => {
    before(hackModuleResolve);

    it('can generate an api type definition file', async () => {
        const generatedApi = await generateClientApi(await createTestApp());
        const expectedApi = fs.readFileSync(
            fsp.join(__dirname, './fixtures/test-app/extra-folder/api/api.d.ts'),
            'utf8',
        );
        assert.equal(generatedApi, expectedApi);
    });

    it('can generate an api type definition file for an extension package', async () => {
        const generatedApi = await generateClientApi(await createTestExtendedApp());
        const expectedApi = fs.readFileSync(
            fsp.join(__dirname, './fixtures/test-extended-app/extra-folder/api/api.d.ts'),
            'utf8',
        );

        assert.equal(generatedApi, expectedApi);
    });
});

describe('client package.json generator', () => {
    it('can generate package.json for base api', async () => {
        const generatedJson = generateClientPackageJson(await createTestApp());
        const expectedJson = fs.readFileSync(
            fsp.join(__dirname, './fixtures/test-app/extra-folder/api/package.json'),
            'utf8',
        );
        assert.deepEqual(JSON.parse(generatedJson), JSON.parse(expectedJson));
    });

    it('can generate package.json for extension api', async () => {
        const generatedJson = generateClientPackageJson(await createTestExtendedApp());
        const expectedJson = fs.readFileSync(
            fsp.join(__dirname, './fixtures/test-extended-app/extra-folder/api/package.json'),
            'utf8',
        );
        assert.deepEqual(JSON.parse(generatedJson), JSON.parse(expectedJson));
    });
});

const checkFile = (generatedPath: string, expectedPath: string, isJsonContent = false) => {
    assert.notEqual(generatedPath, expectedPath);
    const generatedFile = fs.readFileSync(generatedPath, 'utf8');
    const expectedFile = fs.readFileSync(expectedPath, 'utf8');
    if (isJsonContent) {
        assert.deepEqual(JSON.parse(generatedFile), JSON.parse(expectedFile));
    } else {
        assert.equal(generatedFile, expectedFile);
    }
};
