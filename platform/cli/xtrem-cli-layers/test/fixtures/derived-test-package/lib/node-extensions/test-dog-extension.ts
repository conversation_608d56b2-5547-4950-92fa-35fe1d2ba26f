import { decorators, SubNodeExtension1 } from '@sage/xtrem-core';
// eslint-disable-next-line import/no-relative-packages
import * as baseTestPackage from '../../../base-test-package';

@decorators.subNodeExtension1<TestDogExtension>({
    extends: () => baseTestPackage.nodes.TestDog,
})
export class TestDogExtension extends SubNodeExtension1<baseTestPackage.nodes.TestDog> {
    @decorators.booleanProperty<TestDogExtension, 'barksOften'>({
        isPublished: true,
        isStored: true,
    })
    readonly barksOften: Promise<boolean>;
}

// This declaration does not compile with the hacky test package setup
// But we don't really need it here because our test code does not have any business logic on these nodes
// declare module '@sage/base-test-package/lib/nodes/test-dog' {
//     export interface TestDog extends TestDogExtension {}
// }
