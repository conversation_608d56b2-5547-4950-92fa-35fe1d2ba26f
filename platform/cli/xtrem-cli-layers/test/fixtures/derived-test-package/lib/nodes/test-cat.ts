import { decorators } from '@sage/xtrem-core';
// eslint-disable-next-line import/no-relative-packages
import * as baseTestPackage from '../../../base-test-package';

@decorators.subNode<TestCat>({
    extends: () => baseTestPackage.nodes.TestPet,
    isPublished: true,
})
export class TestCat extends baseTestPackage.nodes.TestPet {
    @decorators.booleanProperty<TestCat, 'catchesMice'>({
        isPublished: true,
        isStored: true,
    })
    readonly catchesMice: Promise<boolean>;
}
