#!/usr/bin/env bash
rm -rf test/fixtures/base-test-package/build
rm -rf test/fixtures/derived-test-package/{build,node_modules}

ln -s ../../../build/test/fixtures/base-test-package test/fixtures/base-test-package/build
ln -s ../../../build/test/fixtures/derived-test-package test/fixtures/derived-test-package/build
mkdir -p test/fixtures/derived-test-package/node_modules/@sage
ln -s ../../../base-test-package test/fixtures/derived-test-package/node_modules/@sage
