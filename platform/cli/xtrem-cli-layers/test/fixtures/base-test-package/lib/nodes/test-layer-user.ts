/** @ignore */ /** */
import { decorators, Node } from '@sage/xtrem-core';
import * as dataTypes from '../data-types/data-types';

@decorators.node<TestLayerUser>({
    isPublished: false,
    storage: 'sql',
    isPlatformNode: true,
    indexes: [
        {
            orderBy: { email: +1 },
            isUnique: true,
            isNaturalKey: true,
        },
    ],
})
export class TestLayerUser extends Node {
    @decorators.stringProperty<TestLayerUser, 'email'>({
        isPublished: true,
        isStored: true,
        dataType: () => dataTypes.name,
    })
    readonly email: Promise<string>;

    @decorators.booleanProperty<TestLayerUser, 'isDemoPersona'>({
        isPublished: true,
        isStored: true,
    })
    readonly isDemoPersona: Promise<boolean>;
}
