import { decorators, Node } from '@sage/xtrem-core';
import { name } from '../data-types/_index';

@decorators.node<TestAddress>({
    isPublished: true,
    storage: 'sql',
    isContentAddressable: true,
})
export class TestAddress extends Node {
    @decorators.stringProperty<TestAddress, 'city'>({
        isStored: true,
        dataType: () => name,
    })
    readonly city: Promise<string>;

    @decorators.stringProperty<TestAddress, 'country'>({
        isStored: true,
        dataType: () => name,
    })
    readonly country: Promise<string>;
}
