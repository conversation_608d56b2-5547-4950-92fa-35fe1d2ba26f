import { decorators, Node, Reference } from '@sage/xtrem-core';
import { name } from '../data-types/_index';
import { TestAddress } from './test-address';

@decorators.node<TestBuilding>({
    isPublished: true,
    storage: 'sql',
    indexes: [{ orderBy: { name: 1 }, isUnique: true, isNaturalKey: true }],
})
export class TestBuilding extends Node {
    @decorators.stringProperty<TestBuilding, 'name'>({
        isPublished: true,
        isStored: true,
        dataType: () => name,
    })
    readonly name: Promise<string>;

    @decorators.referenceProperty<TestBuilding, 'address'>({
        isStored: true,
        isMutable: true,
        node: () => TestAddress,
    })
    readonly address: Reference<TestAddress>;

    @decorators.stringProperty<TestBuilding, 'city'>({
        isPublished: true,
        delegatesTo: { address: 'city' },
    })
    readonly city: Promise<string>;

    @decorators.stringProperty<TestBuilding, 'country'>({
        isPublished: true,
        delegatesTo: { address: 'country' },
    })
    readonly country: Promise<string>;
}
