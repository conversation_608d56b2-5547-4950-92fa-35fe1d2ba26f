{"name": "@sage/base-test-package", "description": "Base test package", "version": "37.0.14", "xtrem": {}, "author": "Sage", "license": "UNLICENSED", "main": "build/index.js", "files": ["build", "data"], "typings": "build/package-definition.d.ts", "dependencies": {"@sage/xtrem-cli-layers": "workspace:*", "@sage/xtrem-core": "workspace:*"}, "devDependencies": {}, "scripts": {"build": "mkdir -p node_modules/@sage && ln -s ../../../base-test-package node_modules/@sage/base-test-package", "build:cache": "turbo run build"}}