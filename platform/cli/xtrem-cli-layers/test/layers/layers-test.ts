import {
    Application,
    ApplicationCreateSqlSchema,
    Context,
    CoreHooks,
    DatabaseSqlContext,
    Test,
} from '@sage/xtrem-core';
import { assert } from 'chai';
import * as fsp from 'path';
import * as sinon from 'sinon';
import { loadLayersFromCsvFiles } from '../../lib/index';
// eslint-disable-next-line import/no-relative-packages
import { TestDog, TestLayerUser } from '../fixtures/base-test-package/lib/nodes/_index';

const rootUser = { _id: 1, email: '<EMAIL>', isDemoPersona: false };

let testApplication: Application;

describe('Layers data manager', () => {
    const sandbox = sinon.createSandbox();
    const schemaName = 'derived_test_package_test';

    before(async () => {
        sandbox.stub(Context, 'tenantManager').get(() => ({
            initializeManager() {},
            ensureTenantExists() {},
            listTenantsIds() {
                return [];
            },
            getTenantsInfo() {
                return [];
            },
            deleteTenant() {},
        }));
        sandbox.stub(Context, 'accessRightsManager').get(
            () =>
                ({
                    createActivities() {},
                    updateActivities() {},
                    deleteActivities() {},
                    getUser() {
                        return rootUser;
                    },
                    supportsPersona() {
                        return false;
                    },
                }) as any,
        );
        const sysManager = CoreHooks.sysManager;
        sandbox.stub(CoreHooks, 'sysManager').get(() => ({
            ...sysManager,
            getUserNode: () => TestLayerUser,
        }));

        const buildDir = fsp.join(__dirname, '../fixtures/derived-test-package/build');

        await new DatabaseSqlContext().dropSchemaIfExists(schemaName);
        await new DatabaseSqlContext().createSchemaIfNotExists(schemaName);

        // Create application and its schema
        testApplication = await Test.createTestApplication({
            buildDir,
            schemaName,
        });
        // Insert sys_tenant record
        await testApplication.withCommittedContext(Test.defaultTenantId, async context => {
            await ApplicationCreateSqlSchema.createTables(context, testApplication.getAllFactories());
            await context.executeSql(
                `INSERT INTO ${testApplication.schemaName}.sys_tenant (_id, tenant_id) VALUES ($1, $2)`,
                [1, Test.defaultTenantId],
            );
            await context.executeSql(
                `INSERT INTO ${testApplication.schemaName}.test_layer_user (_tenant_id, _id, email, is_demo_persona) VALUES ($1, $2, $3, $4)`,
                [Test.defaultTenantId, 1, rootUser.email, true],
            );
        });
    });

    after(() => {
        sandbox.restore();
    });

    it('can insert layers data into sql tables', async () => {
        await loadLayersFromCsvFiles(testApplication, ['test'], Test.defaultTenantId, {});

        // verify that dogs have been inserted
        const dogs = await testApplication.withReadonlyContext(Test.defaultTenantId, context =>
            context.select(TestDog, { name: true, owner: { name: true }, isNiceDog: true }, { filter: {} }),
        );
        assert.deepEqual(dogs, [
            {
                name: 'bear',
                owner: { name: 'tom' },
                isNiceDog: false,
            },
            {
                name: 'daisy',
                owner: { name: 'joe' },
                isNiceDog: true,
            },
            {
                name: 'snoopy',
                owner: { name: 'tom' },
                isNiceDog: true,
            },
        ]);
    });
});
