{"name": "@sage/xtrem-cli-layers", "version": "58.0.2", "description": "Management of CSV layers", "main": "build/index.js", "typings": "build/package-definition.d.ts", "publishConfig": {"registry": "https://pkgs.dev.azure.com/Sage-LiveServices/_packaging/Sage-ERP/npm/registry/"}, "repository": {"type": "git", "url": "git://github.com/Sage-ERP-X3/xtrem.git"}, "author": "Sage", "license": "UNLICENSED", "bugs": {"url": "https://github.com/Sage-ERP-X3/xtrem-platform/issues"}, "homepage": "https://github.com/Sage-ERP-X3/xtrem-platform#readme", "dependencies": {"@aws-sdk/client-sso-oidc": "^3.826.0", "@aws-sdk/client-sts": "^3.826.0", "@sage/xtrem-core": "workspace:*", "@sage/xtrem-file-storage": "^6.1.5", "@sage/xtrem-infrastructure-adapter": "workspace:*", "csv-parse": "^6.0.0", "csv-stringify": "^6.4.0", "fs-extra": "^11.2.0", "glob": "^11.0.0", "lodash": "^4.17.21", "prettier": "^3.3.3", "sinon": "^21.0.0"}, "devDependencies": {"@sage/eslint-plugin-xtrem": "workspace:*", "@sage/xtrem-dts-bundle": "workspace:*", "@sage/xtrem-minify": "workspace:*", "@types/chai": "^4.3.6", "@types/fs-extra": "11.0.4", "@types/lodash": "^4.14.198", "@types/mocha": "^10.0.1", "@types/sinon": "^17.0.0", "c8": "^10.1.2", "chai": "^4.3.10", "chai-as-promised": "^7.1.1", "copyfiles": "^2.1.0", "cross-env": "^7.0.3", "dts-generator": "^3.0.0", "eslint": "^8.49.0", "mocha": "^10.8.2", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "~5.8.3"}, "scripts": {"build": "tsc -b -v . && pnpm dts-bundle", "build:binary": "pnpm clean && pnpm build && xtrem-minify -c -z \"build/**/*.js\"", "build:cache": "turbo run build --concurrency=${XTREM_CONCURRENCY:=10}", "clean": "rm -rf build junit-report*", "copy:template": "copyfiles {lib,test}/**/template-rules.sql build", "dts-bundle": "xtrem-dts-bundle", "dts-gen": "dts-generator --name @sage/xtrem-core --project . --out build/xtrem-core.d.ts --main @sage/xtrem-core/index > dts-generator.log", "lint": "eslint -c .eslintrc.js --ext .ts lib test", "lint:fix": "eslint -c .eslintrc.js --fix --ext .ts lib test", "posttest:ci": "rm -rf build/lib/pages", "prebuild": "pnpm copy:template", "prettier:check": "prettier --list-different \"{lib,test}/**/*.ts\"", "prettier:write": "prettier --write \"{lib,test}/**/*.ts\"", "test": "test/fixtures/link-test-packages.sh && cross-env TZ=CET mocha --recursive \"test/**/*@(-|.)test.ts\" --exit", "test:ci": "test/fixtures/link-test-packages.sh && cross-env TZ=CET JUNIT_REPORT_PATH=junit-report-cli-layers.xml JUNIT_REPORT_NAME='xtrem-cli-layers' c8 --reporter=lcov --reporter=json --reporter=cobertura --reporter=text mocha --recursive --exit \"test/**/*@(-|.)test.ts\" --reporter mocha-jenkins-reporter", "test:ci:allDatabases": "test/fixtures/link-test-packages.sh && pnpm test:ci", "test:coverage": "test/fixtures/link-test-packages.sh && c8 --reporter=html mocha --recursive \"test/**/*@(-|.)test.ts\" --exit", "test:debug": "test/fixtures/link-test-packages.sh && mocha  --inspect-brk --recursive \"test/**/*@(-|.)test.ts\" --exit"}}