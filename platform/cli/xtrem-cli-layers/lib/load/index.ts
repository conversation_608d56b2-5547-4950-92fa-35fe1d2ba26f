import { Application, NodeFactory } from '@sage/xtrem-core';
import { CsvDataset } from '../csv-data/csv-dataset';
import { Loader, WriteLayerOptions } from './loader';

export interface LoadLayerOptions extends WriteLayerOptions {
    onlyFactories?: NodeFactory[];
}

export async function loadLayersFromCsvFiles(
    application: Application,
    layers: string[],
    tenantId: string | null,
    options: LoadLayerOptions = {},
): Promise<void> {
    const dataSet = new CsvDataset(application, layers, {
        onlyFactories: options.onlyFactories,
    });
    await new Loader(dataSet, {
        onlySharedTables: options.onlySharedTables,
        skipSharedTables: options.skipSharedTables,
        skipGlobalCacheInvalidation: options.skipGlobalCacheInvalidation,
    }).fillSqlTables(tenantId);
}
