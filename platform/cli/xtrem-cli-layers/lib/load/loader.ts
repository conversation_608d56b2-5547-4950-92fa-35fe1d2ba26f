import {
    Application,
    asyncArray,
    basicProfiler,
    ConfigManager,
    Context,
    CoreHooks,
    Dict,
    EnumDataType,
    NodeFactory,
    Property,
} from '@sage/xtrem-core';
import { FileTimeToLive } from '@sage/xtrem-file-storage';
import * as xtremInfrastructureAdapter from '@sage/xtrem-infrastructure-adapter';
import * as fs from 'fs';
import * as fps from 'path';
import { FactoryRows } from '../csv-data/csv-dataset';
import { CsvDataset, CsvRow } from '../csv-data/index';
import { loggers } from '../util/loggers';

export const uploadedFilesSubFolder = 'uploaded-files';
/**
 * Entry used to insert data into a SQL table
 */
interface TableEntry {
    factory: NodeFactory;
    properties: Property[];
    hasArrayProperties: boolean;
    parameterValues: (any[] | string)[];
}

/**
 * Options that influence insertion into SQL tables
 */
export interface WriteLayerOptions {
    skipSharedTables?: boolean;
    onlySharedTables?: boolean;
    skipGlobalCacheInvalidation?: boolean;
}

/**
 * @internal
 *
 * Loader that transforms and inserts CSV data into SQL tables.
 * This class only handles insertion of CSV data into an empty schema.
 * See the SetupDataUpgrader class for both inserts and updates.
 */
export class Loader {
    /** The dataSet containing the CSV data */
    readonly #dataSet: CsvDataset;

    /** Table entries, indexed by table names */
    #tableEntries: Dict<TableEntry> = {};

    constructor(
        dataSet: CsvDataset,
        readonly options: WriteLayerOptions,
    ) {
        this.#dataSet = dataSet;
    }

    /** The application */
    get application(): Application {
        return this.#dataSet.application;
    }

    /** Convert a CSV value to SQL */
    private convertToSql(property: Property, csvValue: any): any {
        if (!csvValue) {
            if (property.isNullable) return null;
            if (property.isDecimalProperty()) return 0;
            if (property.isTextStreamProperty()) return '';
            return property.getTypeDefaultValue();
        }
        if (property.isReferenceProperty()) {
            try {
                return this.#dataSet.resolveKey(property, csvValue);
            } catch (err) {
                throw property.logicError(`cannot resolve natural key '${csvValue}': ${err.message}`);
            }
        } else if (property.isArrayProperty()) {
            if (typeof csvValue !== 'string') throw property.logicError(`expected a string, got: ${typeof csvValue}`);
            // compat code
            if (csvValue === '"') return '{}';
            const arrayToProcess = (() => {
                // We allow 2 formats for the arrays: "1,2,3" or "[1,2,3]"
                const json = csvValue[0] === '[' ? csvValue : `[${csvValue}]`;
                try {
                    return JSON.parse(json) as any[];
                } catch (err) {
                    throw property.logicError(`Bad JSON format: ${json}`);
                }
            })();
            if (arrayToProcess.length === 0) return '{}';

            if (
                property.isReferenceArrayProperty() &&
                property.targetFactory.naturalKey != null &&
                typeof arrayToProcess[0] === 'string'
            ) {
                // The property is targetting a factory with a natural key and the array contains strings.
                // The array contains natural keys, not ids.
                const keys = arrayToProcess.map((val: any) => {
                    try {
                        return this.#dataSet.resolveKey(property, val);
                    } catch (err) {
                        throw property.logicError(
                            `cannot resolve natural key '${val}' from list ${csvValue}: ${err.message}`,
                        );
                    }
                });
                return `{${keys.join(',')}}`;
            }
            return `{${arrayToProcess.join(',')}}`;
        } else if (property.isBinaryStreamProperty()) {
            return Buffer.from(csvValue, 'base64');
        }
        return csvValue;
    }

    /** Fill the table entry's SQL parameter values if insertion uses bulk method */
    private fillBulkRowParameterValues(tableEntry: TableEntry, row: CsvRow): void {
        tableEntry.properties.forEach((property, i) => {
            const rowValues = tableEntry.parameterValues[i] as any[];
            const csvValue = row[property.requiredColumnName];
            let sqlValue: any;
            if (property.isReferenceProperty() && row._constructor) {
                const concreteFactory: NodeFactory = this.application.getFactoryByName(row._constructor);
                sqlValue = this.convertToSql(concreteFactory.findProperty(property.name), csvValue);
            } else {
                sqlValue = this.convertToSql(property, csvValue);
            }
            rowValues.push(sqlValue);
        });
    }

    /** Fill the table entry's SQL parameter values if insertion does not use bulk method */
    private fillSlowRowParameterValues(tableEntry: TableEntry, row: CsvRow): void {
        const rowValues = tableEntry.properties.map(property => {
            const csvValue = row[property.requiredColumnName];
            if (property.isReferenceProperty() && row._constructor) {
                const concreteFactory: NodeFactory = this.application.getFactoryByName(row._constructor);
                return this.convertToSql(concreteFactory.findProperty(property.name), csvValue);
            }
            return this.convertToSql(property, csvValue);
        });
        tableEntry.parameterValues.push(rowValues);
    }

    /** Fill the table entry's SQL parameter values */
    private fillRowParameterValues(tableEntry: TableEntry, row: CsvRow): void {
        if (row._id === 1 && tableEntry.factory.name === CoreHooks.sysManager.getUserNode().name) return;
        row._source_id = row.__csvFile.sourceId;
        if (tableEntry.hasArrayProperties) this.fillSlowRowParameterValues(tableEntry, row);
        else this.fillBulkRowParameterValues(tableEntry, row);
    }

    /** Fill the table entries of a node factory */
    private fillFactoryParameterValues(factory: NodeFactory, rows: CsvRow[], concreteFactory?: NodeFactory): void {
        if (factory.baseFactory) {
            this.fillFactoryParameterValues(factory.baseFactory, rows, concreteFactory ?? factory);
        }
        let tableEntry = this.#tableEntries[factory.name];
        if (!tableEntry) {
            const isInherited = (property: Property): boolean =>
                property.isInherited ||
                // TODO: investigate why _create_user and _update_user are not marked as inherited in subclasses
                (property.factory.baseFactory != null && /^_((create|update)_user|vendor)$/.test(property.name));
            const properties = factory.properties.filter(property => !isInherited(property) && property.isStored);
            const hasArrayProperties = properties.some(property => property.isArrayProperty());
            const rowValues = hasArrayProperties ? [] : properties.map(() => []);

            tableEntry = {
                factory,
                properties,
                hasArrayProperties,
                parameterValues: rowValues,
            };
            this.#tableEntries[factory.name] = tableEntry;
        }
        rows.forEach(row => {
            this.fillRowParameterValues(tableEntry, row);
        });
    }

    /** Fill all the table entries */
    private fillAllParameterValues(csvEntries: FactoryRows[]): void {
        csvEntries.forEach(({ factory, rows }) => {
            if (this.options.onlySharedTables && !(factory.isSharedByAllTenants && factory.isSetupNode)) return;

            // Skip abstract classes as this would generate duplicate inserts
            if (factory.isAbstract) return;

            this.fillFactoryParameterValues(factory, rows);
        });
    }

    /** Return the SQL type for a given property - for now, only handle the types used by metadata nodes */
    private getSqlType(property: Property): string {
        switch (property.type) {
            case 'boolean':
                return 'BOOL';
            case 'string':
                if (property.isLocalized) return 'JSONB';
                return 'TEXT';
            case 'integer':
            case 'reference':
                return 'INT8';
            case 'json':
                return 'JSON';
            case 'referenceArray':
                // see https://stackoverflow.com/questions/9159440/array-of-arrays-in-postgresql
                throw property.logicError('unsupported type - incompatible with unnest');
            case 'enum': {
                const dataType = property.dataType as EnumDataType;
                return `${this.application.schemaName}.${dataType.getEnumType().name}`;
            }
            case 'decimal':
                return 'NUMERIC';
            case 'short':
                return 'INT4';
            case 'date':
                return 'DATE';
            case 'dateRange':
                return 'DATERANGE';
            case 'datetime':
                return 'TIMESTAMPTZ(3)';
            case 'time':
                return 'TIME';
            case 'binaryStream':
                return 'BYTEA';
            case 'textStream':
                return 'TEXT';

            default:
                throw property.logicError(`unhandled property type: ${property.type}`);
        }
    }

    /** Add _tenant_id and _update_tick paramters to columnNames and parameters, if necessary */
    private static addExtraSqlParameters(factory: NodeFactory, columnNames: string[], parameters: string[]): void {
        if (!factory.isSharedByAllTenants) {
            columnNames.push('_tenant_id');
            parameters.push(`$${parameters.length + 1}`);
        }
    }

    /** Return the SQL statement for a bulk insert */
    private getBulkInsertSql(tableEntry: TableEntry): string {
        const factory = tableEntry.factory;
        const fullTableName = `${this.application.schemaName}.${factory.tableName}`;
        const columnNames = tableEntry.properties.map(property => property.requiredColumnName);

        // See https://stackoverflow.com/questions/7019831/bulk-batch-update-upsert-in-postgresql
        const parameters = tableEntry.properties.map(
            (property, j) => `unnest($${j + 1}::${this.getSqlType(property)}[])`,
        );

        Loader.addExtraSqlParameters(factory, columnNames, parameters);

        return `/*RAW*/INSERT INTO ${fullTableName}
            (${columnNames.map(name => `"${name}"`)})
            VALUES (${parameters})`;
    }

    /** Insert all the rows into a table, using bulk method */
    private async bulkInsertRows(context: Context, tableEntry: TableEntry): Promise<void> {
        const sql = this.getBulkInsertSql(tableEntry);
        if (!tableEntry.factory.isSharedByAllTenants) {
            if (!context.tenantId) throw tableEntry.factory.logicError('no tenantId in context');
            tableEntry.parameterValues.push(context.tenantId);
        }
        loggers.csv.verbose(
            () => `${tableEntry.factory.tableName}: inserting ${tableEntry.parameterValues[0].length} records (fast)`,
        );
        await context.executeSql(sql, tableEntry.parameterValues);
    }

    /** Return the SQL statement for a slow (non bulk) insert */
    private getSlowInsertSql(tableEntry: TableEntry): string {
        const factory = tableEntry.factory;
        const fullTableName = `${this.application.schemaName}.${factory.tableName}`;
        const columnNames = tableEntry.properties.map(property => property.requiredColumnName);

        const parameters = tableEntry.properties.map((__, j) => `$${j + 1}`);

        Loader.addExtraSqlParameters(factory, columnNames, parameters);

        return `/*RAW*/INSERT INTO ${fullTableName}
            (${columnNames.map(name => `"${name}"`)})
            VALUES (${parameters})`;
    }

    /** Insert all the rows into a table, using slow (non bulk) method */
    private async slowInsertRows(context: Context, tableEntry: TableEntry): Promise<void> {
        const sql = this.getSlowInsertSql(tableEntry);
        loggers.csv.verbose(
            () => `${tableEntry.factory.tableName}: inserting ${tableEntry.parameterValues.length} records (fast)`,
        );
        await asyncArray(tableEntry.parameterValues).forEach(async (args: any[]) => {
            if (!tableEntry.factory.isSharedByAllTenants) {
                if (!context.tenantId) throw tableEntry.factory.logicError('no tenantId in context');
                args.push(context.tenantId);
            }
            await context.executeSql(sql, args);
        });
    }

    /** Insert all the rows into a table */
    private async insertRows(context: Context, tableEntry: TableEntry): Promise<void> {
        try {
            // We cannot use the bulk method if the table has array properties,
            // because of limitations in how unnest handles arrays.
            if (tableEntry.hasArrayProperties) return await this.slowInsertRows(context, tableEntry);
            return await this.bulkInsertRows(context, tableEntry);
        } catch (error) {
            loggers.csv.error(error.stack);
            throw tableEntry.factory.logicError(`cannot save to database: ${error.message}`);
        }
    }

    /** Insert all the rows of all the tables */
    private async insertAllData(context: Context): Promise<void> {
        // Deferring the constraints allows us to insert the rows in a single pass,
        // as the _ids have all been allocated by the data set.
        await context.executeSql('SET CONSTRAINTS ALL DEFERRED', []);
        await asyncArray(this.application.getAllSortedFactories()).forEach(async factory => {
            if (this.options.skipSharedTables && factory.isSharedByAllTenants) return;
            const tableEntry = this.#tableEntries[factory.name];
            if (tableEntry) await this.insertRows(context, tableEntry);
        });
    }

    /**
     * Uploads attachments.
     *
     * @param context - The context object.
     * @param csvEntries - An array of FactoryRows containing the attachments to be uploaded.
     * @returns A Promise that resolves when all attachments have been uploaded.
     */
    // eslint-disable-next-line class-methods-use-this
    private async uploadAttachments(context: Context, csvEntries: FactoryRows[]): Promise<void> {
        await Promise.all(
            csvEntries.map(async ({ factory, rows }) => {
                if (factory.name !== 'UploadedFile') return;
                await Promise.all(
                    rows.map(async row => {
                        const config = ConfigManager.current;
                        if (
                            ConfigManager.current.deploymentMode !== 'development' &&
                            !config?.s3Storage?.s3ClusterBucket
                        ) {
                            loggers.csv.warn(() => `Bucket not configured for uploadedFiles. Skipping upload.`);
                            return;
                        }

                        const { filename, key } = row;

                        // create attachment only if file is prefixed with 'file:'
                        if (!row.key.startsWith('local-')) return;

                        const filePath = fps.join(
                            factory.package.dir,
                            `data/layers/${row.__csvFile.layer}`,
                            row.key.replace('local-', `${uploadedFilesSubFolder}/`),
                        );

                        try {
                            await fs.promises.access(filePath, fs.constants.F_OK);
                        } catch (err) {
                            throw new Error(`File not found: ${filePath}`);
                        }

                        const fileContents = await fs.promises.readFile(filePath, 'utf8');

                        await xtremInfrastructureAdapter.InfrastructureHelper.createFile(
                            context,
                            `${uploadedFilesSubFolder}/${key}`,
                            `Upload file - ${filename} in attachment ${key}`,
                            Buffer.from(fileContents),
                            FileTimeToLive.Expire365Days,
                        );

                        loggers.csv.verbose(() => `${factory.tableName}: Upload file - ${filename} to attachments`);
                    }),
                );
            }),
        );
    }

    /** Load the data from the CSV layer and insert it into the SQL tables */
    async fillSqlTables(tenantId: string | null): Promise<void> {
        await basicProfiler.measure('layer-data-fill-sql', async () => {
            const csvEntries = await this.#dataSet.loadCsvRows();
            await this.#dataSet.prepareForWriting(tenantId);
            this.fillAllParameterValues(csvEntries);
            // let max
            await this.application.asRoot.withCommittedContext(tenantId, async context => {
                await this.uploadAttachments(context, csvEntries);
                await this.insertAllData(context);
            });
            await NodeFactory.fixSequences(this.application, tenantId);
        });
        await basicProfiler.walk((path, item) => {
            if (path[0].startsWith('layer-data-')) loggers.csv.info(`${path.join('/')}: ${item.duration} ms`);
        });
        this.application.clearGlobalCache();
        if (!this.options.skipGlobalCacheInvalidation) {
            await this.application.asRoot.withCommittedContext(tenantId, context =>
                context.application.invalidateGlobalCache(context),
            );
        }
    }
}
