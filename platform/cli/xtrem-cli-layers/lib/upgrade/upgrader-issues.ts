import { ColumnMetadata, Dict, FactoryMetadata, LogicError, SqlFileDataRow } from '@sage/xtrem-core';

type RetryData = { rows: SqlFileDataRow[]; columnNames: string[] };

/**
 * This class will keep track of the issues that are encountered by the Upgrader
 */
export class UpgraderIssues {
    /**
     * The factories that will need to be reloaded before the 2nd pass (i.e. the target factories)
     */
    readonly factoriesToReloadBeforeSecondPass: string[] = [];

    /**
     * The factories that could not be loaded in the 1st pass and need to be retried in the 2nd pass
     */
    readonly namesOfFactoriesToRetry: string[] = [];

    /**
     * The list of rows to be retried, indexed by factoryName
     */
    readonly retryDataByFactoryName: Dict<RetryData> = {};

    /**
     * Register a reference that could not be resolved while running the first pass
     */
    registerReferenceNotFound(factory: FactoryMetadata, refColumnMeta: ColumnMetadata, row: SqlFileDataRow): void {
        if (!refColumnMeta.targetFactoryName)
            throw new LogicError(`${factory.name}.${refColumnMeta.name} is not a reference`);
        const targetFactoryName = refColumnMeta.targetRootFactoryName || refColumnMeta.targetFactoryName;
        if (!this.namesOfFactoriesToRetry.includes(factory.name)) {
            this.namesOfFactoriesToRetry.push(factory.name);
            this.retryDataByFactoryName[factory.name] = {
                rows: [],
                columnNames: [],
            };
        }
        const retryData = this.retryDataByFactoryName[factory.name];
        if (!this.factoriesToReloadBeforeSecondPass.includes(targetFactoryName))
            this.factoriesToReloadBeforeSecondPass.push(targetFactoryName);
        // Note, we have to push a clone as the row will be updated by the 1st pass (__action, ...)
        retryData.rows.push(row);
        const colName = refColumnMeta.name;
        if (colName == null) throw new LogicError('Property has no column name');

        if (!retryData.columnNames.includes(colName)) retryData.columnNames.push(colName);
    }
}
