import {
    Application,
    ColumnMetadata,
    Dict,
    FactoryMetadata,
    NodeFactory,
    Property,
    SqlFileDataSet,
    SqlFileDataSets,
} from '@sage/xtrem-core';
import * as _ from 'lodash';
import { FactoryRows } from '../csv-data/csv-dataset';
import { readSetupRows } from '../csv-data/index';
import { getNaturalKeyColumnNamesFromFactory } from '../util/layer-util';

/**
 * Private utility class to generate upgrade metadata which will be recorded in ReloadCsvAction upgrade actions.
 */
export abstract class UpgradeMetadata {
    /** Should the column be included in the metadata */
    private static keepColumn(columnName: string): boolean {
        return (
            !columnName.startsWith('_') ||
            columnName === '_sort_value' ||
            columnName === '_vendor' ||
            columnName === '_constructor'
        );
    }

    /** Get the upgrade metadata for one node factory */
    public static getFactoryMetadata(
        factory: NodeFactory,
        options: {
            columnNamesToUse?: string[];
            skipNullableColumns: boolean;
            skipInheritedProperties: boolean;
        },
    ): FactoryMetadata {
        const columnMetasByName: Dict<ColumnMetadata> = {};
        const columns = factory.properties
            .filter(property => {
                if (!property.isStored) return false;
                if (options.skipInheritedProperties && property.isInherited) return false;
                if (!this.keepColumn(property.requiredColumnName)) return false;
                if (options.columnNamesToUse != null && options.columnNamesToUse.includes(property.requiredColumnName))
                    return true;
                if (options.skipNullableColumns && property.isNullable) return false;
                return true;
            })
            .map(property => {
                const columnMeta = UpgradeMetadata._getColumnMeta(factory, property);
                columnMetasByName[columnMeta.name] = columnMeta;
                return columnMeta;
            });

        const naturalKeyColumns = [
            ...(factory.isSharedByAllTenants ? [] : ['_tenant_id']),
            ...getNaturalKeyColumnNamesFromFactory(factory),
        ];

        const factoryMeta: FactoryMetadata = {
            isAbstract: factory.isAbstract || undefined,
            isSharedByAllTenants: factory.isSharedByAllTenants || undefined,
            isVitalChild: factory.isVitalChild || undefined,
            isVitalCollectionChild: factory.isVitalCollectionChild || undefined,
            rootFactoryName: factory.rootFactory.name,
            name: factory.name,
            baseFactoryName: factory.baseFactory?.name,
            naturalKeyColumns,
            columns,
        };
        if (factory.isVitalChild) {
            const vitalParentProperty = factory.vitalParentProperty;
            if (vitalParentProperty != null)
                factoryMeta.vitalParentColumn =
                    columnMetasByName[vitalParentProperty.requiredColumnName] ||
                    UpgradeMetadata._getColumnMeta(factory, vitalParentProperty);
        }
        return factoryMeta;
    }

    private static _getColumnMeta(factory: NodeFactory, property: Property): ColumnMetadata {
        const columnMeta: ColumnMetadata = {
            name: property.requiredColumnName,
            type: property.type,
        };
        if (property.isStringProperty() && property.isStoredEncrypted) {
            columnMeta.isEncrypted = true;
        }
        if (property.isLocalized) columnMeta.isLocalized = true;
        if (property.isNullable) columnMeta.isNullable = true;
        if (property.isOwnedByCustomer) columnMeta.isOwnedByCustomer = true;
        const rootPropertyFactory = property.rootProperty.factory;
        if (rootPropertyFactory !== factory) {
            // This property does not belong to the factory itself but to its baseFactory
            columnMeta.definingFactory = rootPropertyFactory.name;
        }
        if (property.isReferenceProperty() || property.isReferenceArrayProperty()) {
            columnMeta.targetFactoryName = property.targetFactory.name;
            if (property.targetFactory.rootFactory !== property.targetFactory)
                columnMeta.targetRootFactoryName = property.targetFactory.rootFactory.name;
        }
        if (property.isEnumProperty()) {
            columnMeta.enumMembers = property.dataType.values as string[];
        }
        return columnMeta;
    }

    /** Add the metadata to the setup rows  */
    private static addMetadataToSetupRows(entry: FactoryRows, dataSets: SqlFileDataSets): SqlFileDataSet {
        const { factory, rows } = entry;
        if (rows.length === 0) throw factory.logicError('no rows');
        const aggregatedRow = {};
        rows.forEach(row => Object.assign(aggregatedRow, row));
        const columnNames = Object.keys(aggregatedRow).filter(columnName => this.keepColumn(columnName));
        const metadata = this.getFactoryMetadata(factory, {
            columnNamesToUse: columnNames,
            skipNullableColumns: true,
            skipInheritedProperties: true,
        });
        const mappedRows = rows.map(row => _.pick(row, columnNames));

        const dataSet = dataSets[factory.name];
        if (dataSet) {
            // abstract factory
            dataSet.metadata.columns = _.uniqBy(
                [...dataSet.metadata.columns, ...metadata.columns],
                column => column.name,
            );
            dataSet.rows.push(...mappedRows);
        } else {
            dataSets[factory.name] = { metadata, rows: mappedRows };
        }
        return { rows, metadata };
    }

    static addFactoryRowsToDataSets(entry: FactoryRows, dataSets: SqlFileDataSets): void {
        if (entry.factory.baseFactory)
            this.addFactoryRowsToDataSets({ factory: entry.factory.baseFactory, rows: entry.rows }, dataSets);
        this.addMetadataToSetupRows(entry, dataSets);
    }
}

/** Read the setup CSV files and return a SQL data set with rows data and metadata */
export async function readSetupDataFromCsvFiles(
    application: Application,
    factories: NodeFactory[],
): Promise<SqlFileDataSets> {
    const factoriesRows = (await readSetupRows(application, factories)).filter(
        factoryRows => factoryRows.rows.length > 0,
    );
    const dataSets = {} as SqlFileDataSets;
    factoriesRows.forEach(entry => UpgradeMetadata.addFactoryRowsToDataSets(entry, dataSets));
    return dataSets;
}
