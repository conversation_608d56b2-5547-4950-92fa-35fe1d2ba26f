import {
    Application,
    ColumnMetadata,
    Context,
    Dict,
    FactoryMetadata,
    LogicError,
    SqlFileDataRow,
    SqlFileDataSet,
    SqlFileDataSets,
    asyncArray,
    typeDefaultValue,
} from '@sage/xtrem-core';
import * as _ from 'lodash';
import { Row, catchableNoNaturalKeyErrorMessage, getNaturalKeyColumnNames, getRowKey } from '../util/layer-util';
import { loggers } from '../util/loggers';
import { UpgradeMetadata } from './upgrade-metadata';
import { UpgraderIssues } from './upgrader-issues';

/**
 * Per-factory CSV data and metadata
 */
interface CsvFactoryDataSet {
    readonly rows: Row[];
    readonly columnNames: string[];
    readonly rowsByNaturalKey: Dict<Row>;
    readonly dataSet: SqlFileDataSet;
}

/**
 * Per-factory indexes read from the SQL database.
 * These indexes allow us to match CSV rows with existing SQL rows and decide whether they should be
 * inserted or updated.
 */
interface SqlFactoryIndexes {
    readonly metadata: FactoryMetadata;
    readonly idsByNaturalKey: Dict<number>;
    readonly naturalKeysById: Dict<string>;
    /**
     * The ids of rows with a _vendor set (before the reloading of the setup CSV file)
     */
    readonly idsWithVendor: number[];
}

type Pass = 'first' | 'second';

const logger = loggers.csv;

type UpgraderOptions = {
    /**
     * Should we always reload CSV files (i.e. skip the check on checksum) ?
     * This flag will mainly be set when recording SQL files
     */
    forceReloadOfCsv?: boolean;
    /**
     * The (optional) data to restore.
     * When not set, the data to restore will be extracted from CSV files from the local filesystem
     * When set, data will contain the data to load, for every factory to reload.
     */
    data?: SqlFileDataSets;
};

/**
 * Upgrades the setup data tables during a ReloadCsvAction upgrade action.
 */
class Upgrader {
    /** The CSV factory data sets, indexed by factory name */
    readonly #csvFactoryDataSetDict = {} as Dict<CsvFactoryDataSet>;

    /** The CSV factory indexes, indexed by factory name */
    #sqlFactoryIndexesDict = {} as Dict<SqlFactoryIndexes>;

    #factoryMetasByName = {} as Dict<FactoryMetadata>;

    readonly #issuesToFix = new UpgraderIssues();

    #currentPass: Pass = 'first';

    constructor(
        /** The application */
        private readonly application: Application,
        /** The context that we will use to insert/update the tables */
        private readonly context: Context,
        /** The CSV data and data sets, obtained from the application and database if recording, from the SQL file if replaying */
        private readonly dataSets: SqlFileDataSets,
        private readonly options: UpgraderOptions,
    ) {}

    /** Get the natural key column names from the metadata, without _tenant_id */
    private static getNaturalKeyColumnNamesFromMetadata(dataSet: SqlFileDataSet): string[] {
        const columnNames = dataSet.metadata.naturalKeyColumns;
        return columnNames[0] === '_tenant_id' ? columnNames.slice(1) : columnNames;
    }

    /** Create the CsvFactoryDataSet and fill it with the dataSet rows */
    private fillCsvFactoryDataSet(dataSet: SqlFileDataSet): void {
        const factoryMeta = dataSet.metadata;
        if (factoryMeta.naturalKeyColumns.length === 0) throw new LogicError(`${factoryMeta.name} : no natural key`);

        const naturalKeyColumnNames = Upgrader.getNaturalKeyColumnNamesFromMetadata(dataSet);

        let rows = dataSet.rows;
        rows.forEach(row => {
            row.__naturalKey = getRowKey(row, naturalKeyColumnNames);
        });

        if (factoryMeta.rootFactoryName != null && factoryMeta.rootFactoryName !== factoryMeta.name) {
            // We are on a sub-factory
            // Replace rows by the ones from the rootFactory and merge the data into the rootRow
            // When a setup node A has 2 base nodes B & C, objects in this.#csvFactoryDataSetDict[A]
            // are a mix of objects (exact same objects, not clones) from this.#csvFactoryDataSetDict[B]
            // and this.#csvFactoryDataSetDict[C].
            const rootDataSet = this.#csvFactoryDataSetDict[factoryMeta.rootFactoryName];
            const hasConstructorInRootFactoryNaturalKeyColumns = rootDataSet.dataSet.metadata.naturalKeyColumns.find(
                kc => kc === '_constructor',
            );

            rows = rows.map(row => {
                const getNaturalKey = (): string => {
                    if (
                        hasConstructorInRootFactoryNaturalKeyColumns &&
                        !factoryMeta.isAbstract // Only concrete factories have a _constructor in the key
                    )
                        return `${factoryMeta.name}|${row.__naturalKey}`;
                    return row.__naturalKey;
                };
                const rootCsvRow = rootDataSet.rowsByNaturalKey[getNaturalKey()];
                // We identify the columns that belong to the current factory (the one we are processing)
                const columnMetasOfCurrentFactory = factoryMeta.columns.filter(col => col.definingFactory == null);
                const rowData = _.pick(
                    row,
                    columnMetasOfCurrentFactory.map(col => col.name),
                );
                // We only need to update the necessary columns
                Object.assign(rootCsvRow, rowData);
                if (!factoryMeta.isAbstract) {
                    // We are on a sub node (the last node of the hierarchy, the concrete one), we
                    // need to store the _constructor that will then be used to insert records into the
                    // root table (see insertRows for more details)
                    rootCsvRow._constructor = factoryMeta.name;
                    rootCsvRow.__naturalKey = getNaturalKey();
                }
                return rootCsvRow;
            });
            dataSet.rows = rows;
        }

        const rowsByNaturalKey = _.zipObject(
            rows.map(row => row.__naturalKey),
            rows,
        );
        const columnNames = dataSet.metadata.columns.map(column => column.name);
        this.#csvFactoryDataSetDict[factoryMeta.name] = {
            rows,
            columnNames,
            rowsByNaturalKey,
            dataSet,
        };
        logger.verbose(
            () =>
                `upgrade setup data read ${rows.length} rows for factory ${factoryMeta.name}:${this.context.tenantId}`,
        );
    }

    /** Convert a SQL value to a CSV value */
    private convertValueFromSql(columnMeta: ColumnMetadata, value: any): string {
        if (value == null) {
            if (!columnMeta.isNullable) throw new LogicError(`${columnMeta.name}: unexpected null value`);
            return '';
        }
        if (columnMeta.type === 'boolean') {
            return value ? 'Y' : 'N';
        }
        if (columnMeta.targetFactoryName) {
            let ref =
                this.#sqlFactoryIndexesDict[columnMeta.targetRootFactoryName || columnMeta.targetFactoryName]
                    ?.naturalKeysById[value];
            if (!ref) {
                // this case could come from a node structure change (1 node splitted into base+ sub node) so the meta info in the json is outdated
                const columnTargetFactoryMeta = this._getFactoryMeta(columnMeta.targetFactoryName);
                if (columnTargetFactoryMeta.rootFactoryName !== columnTargetFactoryMeta.name) {
                    // 2nd try with new reloaded meta
                    ref =
                        this.#sqlFactoryIndexesDict[
                            columnTargetFactoryMeta.rootFactoryName || columnTargetFactoryMeta.name
                        ]?.naturalKeysById[value];
                    if (!ref)
                        throw new LogicError(`${columnTargetFactoryMeta.name} : reference not found for id ${value}`);
                } else throw new LogicError(`${columnMeta.name} : reference not found for id ${value}`);
            }
            if (columnMeta.targetRootFactoryName !== columnMeta.targetFactoryName)
                // subnode does not have _constructor even if the root has one
                return ref.replace(`${columnMeta.targetFactoryName}|`, '');
            return ref;
        }
        return value;
    }

    /** Convert a raw SQL row to CSV format (in place, overwriting the input row) */
    private convertRowFromSql(factoryName: string, row: Row, columnMetas: ColumnMetadata[]): Row | null {
        try {
            columnMetas.forEach(columnMeta => {
                row[columnMeta.name] = this.convertValueFromSql(columnMeta, row[columnMeta.name]);
            });
            return row;
        } catch (error) {
            // Rethrow if we get an error when reading a vendor protected setup record
            if (row._vendor) throw error;
            // Only warn otherwise.
            // We get errors when loading SequenceNumberAssignment records that reference a site or a company
            logger.warn(`${factoryName}: Error converting row from SQL to CSV: ${error.message}`);
            return null;
        }
    }

    /** Get the natural key value of row read from SQL (and already converted to CSV format) */
    private static getSqlRowNaturalKeyColumns(factoryMeta: FactoryMetadata, row: Row): string {
        return getRowKey(row, getNaturalKeyColumnNames(factoryMeta));
    }

    private static _getTableName(factoryMeta: FactoryMetadata): string {
        return _.snakeCase(factoryMeta.name);
    }

    private static _isRootAbstractFactory(factoryMeta: FactoryMetadata): boolean {
        return !!factoryMeta.isAbstract && !factoryMeta.baseFactoryName;
    }

    private static _createSystemColumnMeta(name: string): ColumnMetadata {
        if (!name.startsWith('_')) throw new Error(`${name} is not a system column`);
        return {
            name,
            type: ['_id', '_sort_value'].includes(name) ? 'integer' : 'string',
            isNullable: name === '_vendor',
        } as ColumnMetadata;
    }

    /**
     * Create the factory indexes (by _id, by natural key) and fill it.
     * The indexes are filled by querying the SQL table.
     */
    private async fillSqlFactoryIndexes(factoryMeta: FactoryMetadata, dataSet?: SqlFileDataSet): Promise<void> {
        // This is called several times on the root factory of an inheritance tree. Do it only once.
        if (this.#sqlFactoryIndexesDict[factoryMeta.name]) return;

        logger.verbose(() => `loading SQL keys for factory ${factoryMeta.name} and tenant ${this.context.tenantId}`);

        const columnNames = dataSet
            ? [...Upgrader.getNaturalKeyColumnNamesFromMetadata(dataSet)]
            : [...getNaturalKeyColumnNames(factoryMeta)];
        if (factoryMeta.isVitalCollectionChild && !columnNames.includes('_sort_value')) columnNames.push('_sort_value');

        const vendorColumn = factoryMeta.columns.find(c => c.name === '_vendor');
        if (vendorColumn) {
            columnNames.push(vendorColumn.name);
        }

        const args = [] as any[];
        let tenantCondition = 'TRUE';
        if (!factoryMeta.isSharedByAllTenants) {
            tenantCondition = '_tenant_id = $1';
            args.push(this.context.tenantId);
        }

        const columnMetas = [
            Upgrader._createSystemColumnMeta('_id'),
            ...columnNames.map(name => {
                if (name.startsWith('_')) return Upgrader._createSystemColumnMeta(name);
                const columnMeta = factoryMeta.columns.find(column => column.name === name);
                if (!columnMeta) throw new Error(`No metadata available for column ${factoryMeta.name}.${name}`);
                return columnMeta;
            }),
        ];
        const sql = `SELECT * FROM ${this.context.schemaName}.${Upgrader._getTableName(
            factoryMeta,
        )} WHERE ${tenantCondition}`;

        const rows = (await this.context.executeSql<Row[]>(sql, args))
            .map(row => this.convertRowFromSql(factoryMeta.name, row, columnMetas))
            .filter(row => row != null) as Row[];

        const keys = rows.map(row => Upgrader.getSqlRowNaturalKeyColumns(factoryMeta, row));
        const ids = rows.map(row => row._id);
        const idsByNaturalKey = _.zipObject(keys, ids);
        const naturalKeysById = _.zipObject(ids, keys);
        const idsWithVendor: number[] = vendorColumn ? rows.filter(r => r._vendor).map(r => r._id) : [];

        this.#sqlFactoryIndexesDict[factoryMeta.name] = {
            metadata: factoryMeta,
            idsByNaturalKey,
            naturalKeysById,
            idsWithVendor,
        };
        logger.verbose(
            () =>
                `upgrade setup data read ${rows.length} rows for ${Upgrader._getTableName(factoryMeta)}:${
                    this.context.tenantId
                }`,
        );
    }

    /** Add a factory and all the factories that it references to a dict of factories  */
    private addFactoryToDict(factoriesDict: Dict<FactoryMetadata>, factoryMeta: FactoryMetadata): void {
        if (factoriesDict[factoryMeta.name]) return;
        factoriesDict[factoryMeta.name] = factoryMeta;

        const dataSet = this.dataSets[factoryMeta.name];
        // If there is a dataset, we need to add all the factories referenced by the metadata properties
        // Otherwise we need to add all the factories referenced by the factory's natural key.
        const columnNames = dataSet
            ? dataSet.metadata.columns.map(column => column.name)
            : getNaturalKeyColumnNames(factoryMeta);

        factoryMeta.columns.forEach(columnMeta => {
            if (columnMeta.type !== 'reference' && columnMeta.type !== 'referenceArray') return;
            if (!columnNames.includes(columnMeta.name)) return;
            if (columnMeta.targetFactoryName == null)
                throw new LogicError(
                    `metadata(${factoryMeta.name}.${columnMeta.name}): targetFactoryName is missing on a reference`,
                );
            try {
                const targetFactoryMeta = this._getFactoryMeta(columnMeta.targetFactoryName);
                this.addFactoryToDict(factoriesDict, targetFactoryMeta);
            } catch (error) {
                if (columnMeta.isNullable && error.message.endsWith(catchableNoNaturalKeyErrorMessage)) {
                    logger.warn(
                        `Skipping column ${factoryMeta.name}.${columnMeta.name} because its target factory (${columnMeta.targetFactoryName}) has no natural key`,
                    );
                } else {
                    throw error;
                }
            }
        });
    }

    /** Create and fill all the factory datasets and indexes */
    private async fillAllSqlFactoryIndexes(): Promise<void> {
        // First gather all the factories in a dict, traversing their references.
        const factoriesDict = {} as Dict<FactoryMetadata>;
        Object.values(this.dataSets).forEach(factoryDataSet =>
            this.addFactoryToDict(factoriesDict, factoryDataSet.metadata),
        );

        // Then, create the factory indexes and fill them by querying the SQL tables.
        await asyncArray(this.application.getAllSortedFactories())
            .filter(factory => {
                if (!factoriesDict[factory.name]) return false;
                if (factory.storage !== 'sql') return false;
                return factory.isSetupNode || factory.isPlatformNode;
            })
            .forEach(async factory => {
                const dataSet = this.dataSets[factory.name];
                // The factories that are referenced but don't have CSV setup data do not have a dataSet
                // but we still have to fill their indexes.
                if (dataSet) this.fillCsvFactoryDataSet(dataSet);
                // Sometimes the schema has changed and the root factory is not the same between code base (variable factory)
                // and the database (the dataSet info coming from the sql json). If the database has the root factory name, we take it
                const rootFactoryMeta = this._getFactoryMeta(
                    dataSet?.metadata && dataSet.metadata.rootFactoryName !== factory.rootFactory.name
                        ? dataSet.metadata.rootFactoryName
                        : factory.rootFactory.name,
                );
                await this.fillSqlFactoryIndexes(rootFactoryMeta, dataSet);
            });
    }

    /**
     * Resolve a row from a data set to an existing _id.
     * This is used to decide if the row should be inserted or updated.
     */
    private resolveRowId(factoryMeta: FactoryMetadata, row: Row): number | null {
        if (factoryMeta.rootFactoryName == null) throw new Error(`metadata(${factoryMeta.name}): no rootFactoryName`);

        if (!factoryMeta.isVitalChild)
            return this.#sqlFactoryIndexesDict[factoryMeta.rootFactoryName]?.idsByNaturalKey[row.__naturalKey] || null;
        let key = row.__naturalKey;
        if (factoryMeta.isAbstract)
            // If the vital child factory is an abstract factory, we need to resolve the final factory
            // so we can get the vital parent factory name to complete the key
            key = this.resolveKeyFromVitalChildAbstractFactory(row._constructor, key);

        return this.#sqlFactoryIndexesDict[factoryMeta.rootFactoryName]?.idsByNaturalKey[key] || null;
    }

    /**
     * Resolves the key from the abstract factory. Remember that the root factory name is the only one can have _constructor in the natural key
     * @param factoryName - The name of the factory.
     * @param key - The partial key to resolve.
     * @returns The resolved key.
     * @throws Error if the factory does not exist in the model.
     */
    private resolveKeyFromVitalChildAbstractFactory(factoryName: string, key: string): string {
        const finalFactoryMeta = this._getFactoryMeta(factoryName);
        const vitalParentColumn = finalFactoryMeta.vitalParentColumn;
        if (!vitalParentColumn) {
            throw new LogicError(`${factoryName} is not a vital child`);
        }

        if (
            vitalParentColumn.targetRootFactoryName &&
            vitalParentColumn.targetFactoryName !== vitalParentColumn.targetRootFactoryName
        ) {
            const columnTargetRootFactoryMeta = this._getFactoryMeta(vitalParentColumn.targetRootFactoryName);
            if (columnTargetRootFactoryMeta.naturalKeyColumns.some(keyElement => keyElement === '_constructor'))
                return `${vitalParentColumn.targetFactoryName}|${key}`;
        }
        return key;
    }

    /**
     * Resolve a reference from a data set row to an _id in the database.
     */
    private resolveReferenceId(
        factoryMeta: FactoryMetadata,
        columnMeta: ColumnMetadata,
        rowKey: string,
        row: SqlFileDataRow,
    ): number | null {
        if (!columnMeta.targetFactoryName)
            throw new LogicError(`${factoryMeta.name}.${columnMeta.name} is not a reference`);
        if (!rowKey) {
            if (columnMeta.isNullable) return null;
            throw new LogicError(`${factoryMeta.name}.${columnMeta.name}: no key for non nullable reference`);
        }

        // The '#' syntax is obsolete but we have to handle it for a little while because we may encounter it
        // when replaying ReloadCsvActions.
        let key = rowKey.startsWith('#') ? rowKey.substring(1) : rowKey;

        const columnTargetFactoryMeta = this._getFactoryMeta(columnMeta.targetFactoryName);
        if (columnTargetFactoryMeta.isAbstract) {
            // If we are on an abstract factory
            const rowFinalFactoryMeta = this._getFactoryMeta(row._constructor);
            if (rowFinalFactoryMeta.isVitalChild) {
                // If the vital child factory is an abstract factory, we need to resolve the final factory
                // so we can get the vital parent factory name to complete the key
                key = this.resolveKeyFromVitalChildAbstractFactory(row._constructor, key);
            }
        } else if (
            // we are in subnode
            columnTargetFactoryMeta.rootFactoryName !== columnTargetFactoryMeta.name
        ) {
            const columnTargetRootFactoryMeta = this._getFactoryMeta(columnTargetFactoryMeta.rootFactoryName);
            if (columnTargetRootFactoryMeta.naturalKeyColumns.find(keyElement => keyElement === '_constructor'))
                // if parent has _constructor in the key, we complete its key
                key = `${columnTargetFactoryMeta.name}|${key}`;
        }
        const id =
            this.#sqlFactoryIndexesDict[columnTargetFactoryMeta.rootFactoryName || columnTargetFactoryMeta.name]
                ?.idsByNaturalKey[key];
        if (id == null) {
            // We had a natural key to resolve but it could not be resolved from the database
            // This will happen when we load data for nodes A and B where A has a reference to B and B
            // has a reference (nullable) to A. The toposorted factories will be [B, A]. data will be first
            // loaded for B and the references to new record in A will not yet exist in the table A.
            // For instance, Report.activeTemplate references a ReportTemplate and ReportTemplate.report
            // references a Report
            if (this.#currentPass === 'second')
                throw new LogicError(`${factoryMeta.name}.${columnMeta.name}: reference not found: ${key}`);
            // It's OK, the referenced table will probably be loaded later, let's queue the issue
            // for now, it will then be replayed in a second pass
            this.#issuesToFix.registerReferenceNotFound(factoryMeta, columnMeta, row);
            logger.verbose(
                () => `Failed to resolve ${factoryMeta.name}.${columnMeta.name} = '${key}', will run a second pass`,
            );
        }
        return id;
    }

    /** Convert a value loaded from CSV or from a recording to a SQL value */
    private convertValueToSql(
        factoryMeta: FactoryMetadata,
        columnMeta: ColumnMetadata,
        value: any,
        row: SqlFileDataRow,
    ): any {
        if (columnMeta.type === 'reference') {
            return this.resolveReferenceId(factoryMeta, columnMeta, value, row);
        }
        if (columnMeta.type === 'referenceArray') {
            if (value == null) return columnMeta.isNullable ? null : '{}';
            const refs = JSON.parse(value);
            if (!Array.isArray(refs)) {
                throw new LogicError(
                    `${factoryMeta.name}.${columnMeta.name} : invalid reference array value: ${value}`,
                );
            }
            const ids = refs.map(ref => this.resolveReferenceId(factoryMeta, columnMeta, ref, row));
            if (ids.some(id => id == null)) {
                // Will be resolved in the second pass
                return null;
            }
            return `{${ids.join(',')}}`;
        }

        // We get undefined values for columns that have been omitted in CSV files because all empty
        // replace them by the default value of the type.
        if (value == null || value === '') {
            if (columnMeta.isNullable) return null;
            if (columnMeta.type === 'decimal') return 0;
            if (columnMeta.type === 'textStream') return '';
            // if value == null recurse with a non null default value

            if (value == null) {
                if (columnMeta.isEncrypted) return '';
                const defaultValue = typeDefaultValue(
                    columnMeta.type,
                    columnMeta.isNullable,
                    columnMeta.isLocalized,
                    'sql',
                );
                return this.convertValueToSql(factoryMeta, columnMeta, defaultValue, row);
            }
            return value;
        }

        if (columnMeta.type === 'boolean') {
            if (!/^(Y|N)$/.test(value))
                throw new LogicError(`${factoryMeta.name}.${columnMeta.name} : invalid boolean value: ${value}`);
            return value === 'Y';
        }
        if (columnMeta.type === 'stringArray') {
            if (value === '' || value === '"') return '{}';
            const stripped =
                typeof value === 'string' && value.startsWith('[') && value.endsWith(']')
                    ? value.substring(1, value.length - 1)
                    : value;
            return `{${stripped}}`;
        }
        if (columnMeta.type === 'enum') {
            const members = columnMeta.enumMembers;
            if (members == null || members.length === 0)
                throw new LogicError(
                    `${factoryMeta.name}.${columnMeta.name}: enum members are missing in the metadata`,
                );
            // convert numeric values to their string value (we get them when replaying SQL files)
            if (/^\d+$/.test(value)) {
                const member = members[+value];
                if (member == null)
                    throw new LogicError(
                        `${factoryMeta.name}.${columnMeta.name}: could not map value ${value} to an enum member. Candidates were: ${members}`,
                    );
                return member;
            }
            return value;
        }
        return value;
    }

    /** Convert a row for a SQL update */
    private convertRowForUpdate(
        factoryMeta: FactoryMetadata,
        dataSetRow: Row,
        id: number,
        columnMetas: ColumnMetadata[],
    ): Row {
        // resolve references in dataSetRow
        const convertedRow = { _id: id } as Row;
        columnMetas.forEach(columnMeta => {
            if (columnMeta.isOwnedByCustomer) return;
            convertedRow[columnMeta.name] = this.convertValueToSql(
                factoryMeta,
                columnMeta,
                dataSetRow[columnMeta.name],
                dataSetRow,
            );
        });
        // keep track of the original row so that we can set its __action field after update
        convertedRow.__csvRow = dataSetRow;
        return convertedRow;
    }

    /** Convert a row for a SQL insert */
    private convertRowForInsert(factoryMeta: FactoryMetadata, dataSetRow: Row, columnMetas: ColumnMetadata[]): Row {
        // resolve references in dataSetRow
        const convertedRow = { __naturalKey: dataSetRow.__naturalKey } as Row;
        columnMetas.forEach(columnMeta => {
            convertedRow[columnMeta.name] = this.convertValueToSql(
                factoryMeta,
                columnMeta,
                dataSetRow[columnMeta.name],
                dataSetRow,
            );
        });

        // keep track of the original row so that we can set its _id and __action fields after insert
        convertedRow.__csvRow = dataSetRow;
        return convertedRow;
    }

    /** Insert the rows into the SQL table */
    private async insertRows(factoryMeta: FactoryMetadata, rows: Row[], columnMetas: ColumnMetadata[]): Promise<void> {
        if (rows.length === 0) return;

        const isRootAbstractFactory = Upgrader._isRootAbstractFactory(factoryMeta);
        const hasParentFactory = factoryMeta.name !== factoryMeta.rootFactoryName;
        if (isRootAbstractFactory) {
            // We are inserting records into a root factory, we have to add the _constructor columns
            Upgrader.ensureConstructorColumn(columnMetas);
        }
        let columnMetasOfCurrentFactory = columnMetas;
        if (hasParentFactory)
            // we retrieve the columns that belong to the current factory in order to update them
            // if we have more than 2 levels of hierarchy, we will have to take care of middle abstract factories _constructor
            columnMetasOfCurrentFactory = columnMetas.filter(
                col => col.definingFactory == null || col.name === '_constructor',
            );
        // TODO: optimize with bulk insert, if possible
        if (!factoryMeta.isSharedByAllTenants) {
            columnMetasOfCurrentFactory.push(Upgrader._createSystemColumnMeta('_tenant_id'));
        }
        if (factoryMeta.baseFactoryName) {
            columnMetasOfCurrentFactory.push(Upgrader._createSystemColumnMeta('_id'));
        }
        const paramNames = columnMetasOfCurrentFactory.map((__, i) => `$${i + 1}`);

        const sql = `INSERT INTO ${this.context.schemaName}.${Upgrader._getTableName(factoryMeta)} (${columnMetasOfCurrentFactory.map(
            c => `"${c.name}"`,
        )}) VALUES (${paramNames}) RETURNING _id`;

        await asyncArray(rows).forEach(async row => {
            if (!factoryMeta.isSharedByAllTenants) {
                row._tenant_id = this.context.tenantId;
            }
            if (isRootAbstractFactory) {
                row._constructor = row.__csvRow._constructor;
            }
            if (factoryMeta.baseFactoryName) {
                // Sub-node: the record in the base factory should already have been processed.
                // When the base node was written, its _id was stored in the row (this is the same
                // row object that was used)
                row._id = row.__csvRow._id;
                if (!row._id) throw new LogicError('row _id missing for subclass insert');
                if (row.__csvRow.__action !== 'inserted') {
                    // If we insert a row into a sub table, then a matching record must have
                    // been inserted into its root table.
                    throw new LogicError('new record was not inserted to root table');
                }
            }

            const args = columnMetasOfCurrentFactory.map(columnMeta => row[columnMeta.name]);
            const result = await this.context.executeSql<{ _id: number }>(sql, args);
            if (isRootAbstractFactory) {
                // We are writting a record for the root table of a hierarchy, we have to store
                // the _id returned by the database so that it will be then used when writting records
                // for the sub-tables (it will be the exact same row object)
                row.__csvRow._id = result._id;
            }
            row.__csvRow.__action = 'inserted';

            const sqlEntry = this.#sqlFactoryIndexesDict[factoryMeta.rootFactoryName];
            if (!row.__naturalKey) throw new LogicError(`${factoryMeta.name}: natural key missing: ${result._id}`);
            if (hasParentFactory) {
                if (sqlEntry.idsByNaturalKey[row.__naturalKey] == null) {
                    // The matching record in the base table should already have been processed earlier
                    throw new LogicError(
                        `${factoryMeta.name}: the record was not processed in ${factoryMeta.rootFactoryName} (${row.__naturalKey} / ${result._id})`,
                    );
                }
            } else {
                if (sqlEntry.idsByNaturalKey[row.__naturalKey]) {
                    // We should not process the same record twice for the same table
                    throw new LogicError(
                        `${factoryMeta.name}: row inserted twice: ${row.__naturalKey} / ${result._id}`,
                    );
                }
                sqlEntry.idsByNaturalKey[row.__naturalKey] = result._id;
                sqlEntry.naturalKeysById[result._id] = row.__naturalKey;
            }
        });
        logger.verbose(
            () => `upgrade setup data inserted ${rows.length} rows for ${factoryMeta.name}:${this.context.tenantId}`,
        );
    }

    /**
     * Make sure the _contrsuctor column is declared in the list of columns
     */
    private static ensureConstructorColumn(columnMetas: ColumnMetadata[]): void {
        if (columnMetas.find(c => c.name === '_constructor')) return;
        columnMetas.push(Upgrader._createSystemColumnMeta('_constructor'));
    }

    /** Insert the existing rows in the SQL table */
    private async updateRows(factoryMeta: FactoryMetadata, rows: Row[], columnMetas: ColumnMetadata[]): Promise<void> {
        if (rows.length === 0) return;
        // TODO: optimize with bulk update, if possible
        const updateColumnNames = columnMetas.filter(
            columnMeta => columnMeta.name !== '_tenant_id' && !columnMeta.isOwnedByCustomer,
        );
        const isRootAbstractFactory = Upgrader._isRootAbstractFactory(factoryMeta);
        if (isRootAbstractFactory) {
            // We are updating records from a root factory, we have to add the _constructor columns
            Upgrader.ensureConstructorColumn(updateColumnNames);
        }
        const setClauses = updateColumnNames.map((columnName, i) => `"${columnName.name}"=$${i + 1}`);
        let whereClause = `_id=$${setClauses.length + 1}`;
        if (!factoryMeta.isSharedByAllTenants) whereClause += ` AND _tenant_id=$${setClauses.length + 2}`;

        const sql = `UPDATE ${this.context.schemaName}.${Upgrader._getTableName(
            factoryMeta,
        )} SET ${setClauses} WHERE ${whereClause}`;
        await asyncArray(rows).forEach(async row => {
            if (isRootAbstractFactory) {
                row._constructor = row.__csvRow._constructor;
            }
            const args = updateColumnNames.map(columnMeta => row[columnMeta.name]);
            args.push(row._id);
            if (!factoryMeta.isSharedByAllTenants) args.push(this.context.tenantId);
            const updatedIds = await this.context.executeSql<{ updateCount: number }>(sql, args);
            if (updatedIds.updateCount === 0) {
                throw new Error(`A row for ${factoryMeta.name} was not updated. Sql=${sql}, args=${args.join(',')}`);
            }
            row.__csvRow.__action = 'updated';
        });
        logger.verbose(
            () => `upgrade setup data updated ${rows.length} rows for ${factoryMeta.name}:${this.context.tenantId}`,
        );
    }

    private _getFactoryMeta(factoryName: string): FactoryMetadata {
        const getMeta = (): FactoryMetadata => {
            const factoryIndex = this.#sqlFactoryIndexesDict[factoryName];
            if (factoryIndex != null) return factoryIndex.metadata;
            const dataSet = this.dataSets[factoryName];
            if (dataSet) {
                return dataSet.metadata;
            }
            // We don't have any data/metadata for this factory, let's extract the meta from the model
            // We need to extract the meta from the model because some existing metadata could be obsolete
            const factoryFromModel = this.application.getFactoryByName(factoryName);
            if (factoryFromModel == null) throw new Error(`The factory ${factoryName} does not exist in the model`);
            return UpgradeMetadata.getFactoryMetadata(factoryFromModel, {
                skipInheritedProperties: true,
                skipNullableColumns: false,
            });
        };

        let factoryMeta = this.#factoryMetasByName[factoryName];
        if (factoryMeta == null) {
            factoryMeta = getMeta();
            this.#factoryMetasByName[factoryName] = factoryMeta;
        }
        return factoryMeta;
    }

    /** Return the row that carries the _vendor property for a given row */
    private getVendorRow(factoryMeta: FactoryMetadata, row: Row): Row {
        if (!factoryMeta.isVitalChild) return row;
        let vitalParentColumn = factoryMeta.vitalParentColumn;

        if (vitalParentColumn == null) {
            if (factoryMeta.rootFactoryName === factoryMeta.name)
                throw new LogicError(`No vitalParentColumn for vitalChild factory ${factoryMeta.name}`);
            const rootParentFactory = this._getFactoryMeta(factoryMeta.rootFactoryName);
            vitalParentColumn = rootParentFactory.vitalParentColumn;
            if (vitalParentColumn == null)
                throw new LogicError(`No vitalParentColumn for vitalChild factory ${factoryMeta.rootFactoryName}`);
        }
        const vitalParentFactoryName = vitalParentColumn.targetFactoryName;
        if (vitalParentFactoryName == null)
            throw new LogicError(`${factoryMeta.name}.${vitalParentColumn.name}: no targetFactoryName`);

        let value = row[vitalParentColumn.name];
        const vitalParentFactoryMeta = this._getFactoryMeta(vitalParentFactoryName);

        if (row._constructor) {
            // If we are on an abstract factory, we need to resolve the final factory
            // so we can get the vital parent factory name to complete the key
            value = this.resolveKeyFromVitalChildAbstractFactory(row._constructor, value);
        }

        const vitalParentFactoryDataSet = this.#csvFactoryDataSetDict[vitalParentFactoryName];
        const vitalParent = vitalParentFactoryDataSet.rowsByNaturalKey[value];
        if (!vitalParent) throw new LogicError(`${factoryMeta.name}.${vitalParentColumn.name}: no vital parent`);
        return this.getVendorRow(vitalParentFactoryMeta, vitalParent);
    }

    /**
     * Returns whether a row has to be inserted or updated
     */
    private getSqlActionForRow(
        factoryMeta: FactoryMetadata,
        row: SqlFileDataRow,
        id: number | null,
        vendorRow: Row,
    ): 'update' | 'insert' | 'skip' {
        if (this.#currentPass === 'second') {
            // On the second pass, we are only updating rows from the first pass
            if (id == null)
                throw new LogicError(
                    `natural key ${row.__naturalKey} could not be resolved for factory ${factoryMeta.name}`,
                );
            return 'update';
        }
        if (id) {
            if (row.__action === 'inserted') {
                // Will happen when we are processing a row of a sub table. If the record was inserted in the
                // root table, it has to be inserted in the sub table as well (row.__action was set when
                // the record was inserted into the root table - the rows are shared between factories/subFactories)
                return 'insert';
            }
            if (vendorRow._vendor) {
                // This record is owned by us, we can update it
                return 'update';
            }
            return 'skip';
        }
        if (vendorRow === row) {
            // Either a root of a vital hierachy or a node without vital graph
            return 'insert';
        }
        if (vendorRow.__action === 'inserted') {
            // Vital child of a parent which was inserted
            return 'insert';
        }
        if (!vendorRow._vendor) {
            // root of vital tree was updated, we only update children if the parent has a vendor code
            return 'skip';
        }
        return 'insert';
    }

    private async tableExists(factoryMeta: FactoryMetadata): Promise<boolean> {
        const tableName = ` ${this.context.schemaName}.${Upgrader._getTableName(factoryMeta)}`;
        const exists = await this.context.executeSql<{ to_regclass: string }[]>('SELECT to_regclass($1)', [tableName]);
        return !!exists?.[0].to_regclass;
    }

    /** Upgrade the setup data for a given factory */
    private async upgradeFactorySetupData(dataSet: SqlFileDataSet): Promise<void> {
        const factoryMeta = dataSet.metadata;
        const naturalKeyColumns = factoryMeta.naturalKeyColumns;
        if (naturalKeyColumns.length === 0) throw new LogicError(`${factoryMeta.name} : no natural key`);
        const dataChecksum = this.application.csvChecksumManager.computeChecksum(JSON.stringify(dataSet));
        if (this.#currentPass === 'first') {
            const lastLoadedChecksum = await this.application.csvChecksumManager.getLastAppliedChecksum(
                this.context,
                factoryMeta.name,
            );
            logger.debug(
                () =>
                    `\t- comparing checksums for factory ${factoryMeta.name} on tenant ${this.context.tenantId} : dataSet=${dataChecksum}/db=${lastLoadedChecksum}`,
            );
            if (lastLoadedChecksum === dataChecksum) {
                if (this.options.forceReloadOfCsv) {
                    logger.info(
                        `\t- setup data on factory ${factoryMeta.name} is up-to-date (based on checksum) but reloading was forced - tenant:${this.context.tenantId}.`,
                    );
                } else {
                    // No need to reload the CSVs files for this factory: nothing has changed since the last time it was loaded.
                    logger.info(
                        `\t- setup data on factory ${factoryMeta.name} is up-to-date (based on checksum), re-loading was skipped (tenant:${this.context.tenantId}).`,
                    );
                    return;
                }
            }
        }
        try {
            logger.info(
                () =>
                    `Upgrading setup csv node ${factoryMeta.name} for tenant ${this.context.tenantId}, ${dataSet.rows.length} rows, pass=${this.#currentPass}`,
            );

            // Dispatch the rows in insert and update arrays
            const rowsToInsert = [] as Row[];
            const rowsToUpdate = [] as Row[];

            const columnMetas = dataSet.metadata.columns.filter(column => column.name !== '_update_tick');
            dataSet.rows.forEach(row => {
                const id = this.resolveRowId(factoryMeta, row);
                const vendorRow = this.getVendorRow(factoryMeta, row);
                const sqlAction = this.getSqlActionForRow(factoryMeta, row, id, vendorRow);
                switch (sqlAction) {
                    case 'update':
                        if (id == null) throw new LogicError('id must not be null');
                        rowsToUpdate.push(this.convertRowForUpdate(factoryMeta, row, id, columnMetas));

                        break;
                    case 'insert':
                        rowsToInsert.push(this.convertRowForInsert(factoryMeta, row, columnMetas));
                        break;
                    case 'skip':
                        // Nothing to do
                        break;

                    default:
                        throw new Error(`Unmanaged action for row ${sqlAction}`);
                }
            });

            // Insert and update the rows
            await this.insertRows(factoryMeta, rowsToInsert, columnMetas);
            await this.updateRows(factoryMeta, rowsToUpdate, columnMetas);
            if (this.#currentPass === 'first') {
                await this._fixVendorOfUpdatedRows(factoryMeta, rowsToUpdate);
            }
            logger.info(
                `\t- ${this.context.tenantId}/${factoryMeta.name}: setup data upgraded, ${rowsToInsert.length} rows inserted, ${rowsToUpdate.length} rows updated`,
            );
        } catch (err) {
            logger.error(
                `\t- ${this.context.tenantId}/${factoryMeta.name}: could not upgrade setup data, reason was ${err.message}`,
            );
            throw err;
        }
        if (this.#currentPass === 'first') {
            logger.debug(
                () =>
                    `\t- write checksum ${dataChecksum} for factory ${factoryMeta.name} on tenant ${this.context.tenantId}`,
            );
            await this.application.csvChecksumManager.setLastAppliedChecksum(
                this.context,
                factoryMeta.name,
                dataChecksum,
            );
        }
    }

    /**
     * Fix (if needed) the _vendor column of updated rows
     * Some rows with a vendor may have been lost it. The existing record in db had a vendor set but:
     * - the vendor was removed from the setup CSV file for the line of the record
     * - or the whole record was removed from the CSV file
     * @param factoryMeta
     * @param rowsToUpdate
     */
    private async _fixVendorOfUpdatedRows(factoryMeta: FactoryMetadata, rowsToUpdate: Row[]): Promise<void> {
        const vendorColumn = factoryMeta.columns.find(c => c.name === '_vendor');
        if (vendorColumn == null) {
            // This factory has a no vendor property
            return;
        }
        if (factoryMeta.rootFactoryName !== factoryMeta.name) {
            // We are in subnode
            return;
        }
        // The _ids that were updated for this factory
        const updatedIdsWithVendor = rowsToUpdate.filter(r => r._vendor).map(r => r._id);
        // The _ids of the rows that had a _vendor set in db before the reloading of the CSV file
        const existingIdsWithVendor = this.#sqlFactoryIndexesDict[factoryMeta.name].idsWithVendor;
        const idsToFix = _.difference(existingIdsWithVendor, updatedIdsWithVendor);
        if (idsToFix.length === 0) {
            // No rows to fix
            return;
        }
        logger.info(
            `\t- ${this.context.tenantId}/${factoryMeta.name}: resetting _vendor column from ${
                idsToFix.length
            } records. Natural keys are: [${idsToFix
                .map(id => this.#sqlFactoryIndexesDict[factoryMeta.name].naturalKeysById[id])
                .join()}]`,
        );
        const sqlParts: string[] = [
            `UPDATE ${this.context.schemaName}.${Upgrader._getTableName(factoryMeta)} SET ${
                vendorColumn.name
            }=NULL WHERE _id IN (${idsToFix.join(',')})`,
        ];
        const args: any[] = [];
        if (!factoryMeta.isSharedByAllTenants) {
            sqlParts.push(`AND _tenant_id=$1`);
            args.push(this.context.tenantId);
        }
        const result = await this.context.executeSql<{ updateCount: number }>(sqlParts.join(' '), args);
        if (result.updateCount !== idsToFix.length)
            throw new Error(
                `Inconsistency error: expected to update ${idsToFix.length} records but got ${result.updateCount}`,
            );
    }

    /** Fix a row that we got from a data set  */
    static fixRowFromDataSet(dataSet: SqlFileDataSet, row: Row): void {
        dataSet.metadata.columns.forEach(column => {
            const value = row[column.name];
            if (column.type === 'boolean') {
                if (value != null && !/^(Y|N|$)/.test(value))
                    throw new LogicError(`${dataSet.metadata.name}.${column.name}: invalid boolean value: ${value}`);
                if (!value) row[column.name] = 'N';
            }
        });
    }

    /**
     * Dispatch data set between factories/baseFactories
     * If a factory C inherits from B and B inherits from A (C -> B -> A), and if we are processing a SQL file, then the dataSet will only contain
     * an entry for the factory C. We need to add entries for factories A and B
     */
    private _dispatchDataSets(): void {
        const factoryMetas: FactoryMetadata[] = Object.values(this.dataSets).map(ds => ds.metadata);
        factoryMetas.forEach(factoryMeta => {
            if (factoryMeta.name === factoryMeta.rootFactoryName) {
                // Not a sub-factory
                return;
            }
            // The columns of this metadata do not all belong to the factory, some of them belong to the base factory
            // Group the columns by factory
            const allFactoryNames: string[] = [];
            const getFactoriesTree = (currentFactoryMetadata: FactoryMetadata): void => {
                if (
                    currentFactoryMetadata.baseFactoryName &&
                    currentFactoryMetadata.name !== currentFactoryMetadata.baseFactoryName
                ) {
                    allFactoryNames.push(currentFactoryMetadata.baseFactoryName);
                    getFactoriesTree(this._getFactoryMeta(currentFactoryMetadata.baseFactoryName));
                }
            };
            allFactoryNames.push(factoryMeta.name);
            getFactoriesTree(factoryMeta);
            // allFactoryNames contains factories from the current factory's inheritance tree (A, B, C)
            if (allFactoryNames.length === 1 && allFactoryNames[0] === factoryMeta.name) {
                // allFactoryNames[0] === factoryMeta.name means that the metadata only contains columns from the current factory
                // if allFactoryNames[0] contain other than the current factory name, it means that all the columns are not from current factory
                // (for example, in the sub node we only redefine the column type but not the column name
                // e.g. a reference of base node becomes a reference of subnode of that base node) => we still need to dispatch to that factory
                // Otherwise, there is nothing to dispatch, the metadata only contains columns for the current factory (and none from base factories)
                return;
            }
            const rowsToDispatch = this.dataSets[factoryMeta.name].rows;
            allFactoryNames.forEach(factoryName => {
                // Note: we have to clone the meta because we are going to update it
                const targetMeta: FactoryMetadata = { ...this._getFactoryMeta(factoryName) };
                // Make sure we only keep the columns defined by the factory itself (no inherited column)
                // Note : the result of _getFactoryMeta may come from 2 different sources
                // - the metadata part of a SQL file (it will contain inherited properties)
                // - was built from the factory (will not contain any inherited property)
                targetMeta.columns = targetMeta.columns.filter(
                    c =>
                        // note: c.definingFactory is only set on inherited columns
                        // if we have more than 2 levels of hierarchy, we will have to do take care of middle abstract factories _constructor
                        c.definingFactory == null || c.name === '_constructor',
                );
                rowsToDispatch.forEach(row => {
                    // Complete the constructor column by the name of the current factory
                    row._constructor = targetMeta.isAbstract ? factoryMeta.name : '';
                });
                const newDataSet: SqlFileDataSet = {
                    metadata: targetMeta,
                    // Note, all the factories of the hierachy will have the same (full) rows
                    // We can have multiple concrete classes for the same abstract factory
                    // We need to clone the rows because they could be modified in the subclasses
                    rows: targetMeta.isAbstract ? _.cloneDeep(rowsToDispatch) : rowsToDispatch,
                };
                if (!this.dataSets[factoryName])
                    // We are in a abstract class which could be shared by multiple subclasses
                    this.dataSets[factoryName] = newDataSet;
                else if (factoryName === factoryMeta.name) {
                    // We are in a concrete class
                    this.dataSets[factoryName] = newDataSet;
                } else {
                    // We have to merge the rows of the data set with the existing rows from a different subclass
                    this.dataSets[factoryName].rows.push(...newDataSet.rows);
                }
            });
        });
    }

    /** Upgrade the setup tables from their data set (from CSV if recording, from SQL file if replaying) */
    async upgrade(): Promise<void> {
        if (this.options.data) this._dispatchDataSets();
        await asyncArray(Object.values(this.dataSets)).forEach(async dataSet => {
            // Skip update of setup date if table does not exist.
            // This happens if a table renaming is reverted.
            if (!(await this.tableExists(dataSet.metadata))) {
                logger.warn(`Cannot update setup data for factory ${dataSet.metadata.name}: table does not exist`);
                delete this.dataSets[dataSet.metadata.name];
                return;
            }

            dataSet.rows.forEach(row => Upgrader.fixRowFromDataSet(dataSet, row));
        });

        // Defer the constraints so that we can insert/update all tables in a single pass
        await this.context.executeSql('SET CONSTRAINTS ALL DEFERRED', []);
        await this.fillAllSqlFactoryIndexes();
        await asyncArray(this.application.getAllSortedFactories()).forEach(async factory => {
            const dataSet = this.dataSets[factory.name];
            if (dataSet) {
                await this.upgradeFactorySetupData(dataSet);
            }
        });

        if (this.#issuesToFix.namesOfFactoriesToRetry.length === 0) return;

        logger.info(`Run a second pass for factories ${this.#issuesToFix.namesOfFactoriesToRetry.join(',')}`);

        this.#currentPass = 'second';
        // First, refresh the factory indexes for all the required factories
        await asyncArray(this.#issuesToFix.factoriesToReloadBeforeSecondPass).forEach(async factoryName => {
            const factoryMetadata = this._getFactoryMeta(factoryName);
            delete this.#sqlFactoryIndexesDict[factoryName];
            await this.fillSqlFactoryIndexes(factoryMetadata);
        });

        // Now, we can retry to load the factories that failed on the first pass
        await asyncArray(this.#issuesToFix.namesOfFactoriesToRetry).forEach(async factoryName => {
            const dataSet = this.dataSets[factoryName];
            if (!dataSet) return;
            const retryData = this.#issuesToFix.retryDataByFactoryName[factoryName];
            // We have to build a new metadata that will be used to run the updates
            // (no need to update all the columns that were already set by the 1st pass,
            // we only need to update the columns with issues)
            const metaColumnsByName = dataSet.metadata.columns.reduce((total, metaCol) => {
                total[metaCol.name] = metaCol;
                return total;
            }, {} as Dict<ColumnMetadata>);
            const metadata: FactoryMetadata = {
                ...dataSet.metadata,
                ...{
                    columns: retryData.columnNames.map(colName => metaColumnsByName[colName]),
                },
            };

            await this.upgradeFactorySetupData({
                metadata,
                rows: retryData.rows,
            });
        });
    }
}

// TODO: optimize by ugrading all tenants in a single transaction
/** Update the setup data of a given tenant */
export async function upgradeSetupData(
    application: Application,
    tenantId: string | null,
    dataSets: SqlFileDataSets,
    options: UpgraderOptions,
): Promise<void> {
    const clonedDataSets = _.mapValues(dataSets, dataSet => ({
        metadata: dataSet.metadata,
        rows: dataSet.rows.map(row => ({ ...row })),
    }));
    await application.withCommittedContext(tenantId, context =>
        context.withLocalizedTextAsJson(() => new Upgrader(application, context, clonedDataSets, options).upgrade()),
    );
}
