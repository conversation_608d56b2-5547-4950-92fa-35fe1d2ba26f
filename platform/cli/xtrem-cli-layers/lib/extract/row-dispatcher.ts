import {
    Application,
    Dict,
    integer,
    LogicError,
    NodeFactory,
    Package,
    Property,
    ReferenceProperty,
} from '@sage/xtrem-core';
import * as _ from 'lodash';
import { ExtractDataSet } from './extract-dataset';
import { ExtractPackage, ExtractPackageFactory, SqlRow } from './extract-util';

/**
 * @internal
 *
 * This class dispatches the rows that we read from the SQL tables into their destination packages.
 *
 * A given row will usually be dispatched into several CSV files.
 * One package (the base package) will contain the base data and other packages will contain extension data.
 *
 * The base package is tracked by the _source_id column in the SQL tables. This column value is set by the loader
 * when we load the data from SQL files.
 * For new rows which have been added since the last load, the base package will be their factory's package.
 *
 * The dispatch of extension data is the tricky part. By default a property value is dispatched to the property's
 * `definingPackage` which is different from the factory's package if the property was added by an extension.
 * But this default is overriden if the base package of the row is more specific than the factory's package,
 * and also if the property is a reference which references a row that belongs to a more specific package.
 *
 * To manage this, we use a map of package name to package index. The package index is an integer which increases when
 * we walk down the dependency tree: `xtrem-system` has the lowest value (1) and the main package of the application has
 * the highest value.
 * As every row may be dispatched differently (because of its references) we track the row and column package indexes
 * in row.__packageIndexes, a dict indexed by the column names (with a special '__this' key for the row itself).
 *
 * The dispatch uses two passes:
 * - in the first pass we compute the __packageIndexes of all the rows.
 * - then we dispatch the rows into two levels of dicts (ExtractPackage and ExtractPackageFactory).
 *
 * Once the rows have been dispatched into these dicts, they are processed by the Extractor class to create the
 * CSV files.
 */
export class RowDispatcher {
    #dataSet: ExtractDataSet;

    /**
     * The dictionary of ExtractPackage objects.
     * This is filled in the second stage, when we dispatch rows to the package/factory rows/rowExtensions.
     */
    #extractPackageDict: Dict<ExtractPackage> = {};

    /**
     * Termination condition for the loop that computes the __packageIndexes of all rows.
     * This flag is set to false at the beginning of every computation pass.
     * It is set to true if at least one index is modified during the pass.
     *
     * If it is false at the end of the pass, the computation is complete, otherwise we run another pass.
     * The computation is guaranteed to terminate because the indexes are always increased and their values are bounded.
     */
    #packageIndexesModified = false;

    constructor(
        readonly application: Application,
        readonly layer: string,
    ) {
        this.#dataSet = new ExtractDataSet(application, layer);
        this.createExtractPackages();
    }

    /**
     * Returns the packages sorted from least specific (xtrem-system) to most specific (main package).
     * The package indexes are the indexes (+ 1) in this sorted array
     *
     * application.getPackages() is already sorted from least specific to most specific but we need a stronger sorting here.
     * We need to have all platform packages sorted before non-platform ones so that applicative packages
     * are always considered to be more specific than platform packages.
     * This is necessary to get reports, import-exports, schedule, dashboard, etc. records correctly extracted in
     *their respective applicative packages rather than in the platform package (xtrem-reporting, ...)
     */
    private getSortedPackages(): Package[] {
        const packages = this.application.getPackages();
        const isPlatform = (pack: Package): boolean => !!pack.xtremOptions.isPlatform;
        return [...packages.filter(pack => isPlatform(pack)), ...packages.filter(pack => !isPlatform(pack))];
    }

    /**
     * Create all the extract packages.
     * Called by the RowDispatcher constructor.
     */
    private createExtractPackages(): void {
        this.getSortedPackages().forEach((pack, i) => {
            this.#extractPackageDict[pack.name] = {
                pack,
                packageIndex: i + 1,
                factoryDataDict: {},
            };
        });
    }

    /** Returns the natural key value of a row - this is called by the Extractor */
    getNaturalKeyValueOfRow(factory: NodeFactory, row: SqlRow): string {
        return this.#dataSet.getNaturalKeyValueOfRow(factory, row);
    }

    /** Returns the key value of a row (natural key or id) -  this is called by the Extractor */
    getKeyValue(factory: NodeFactory, id: any, prependConstructor = false): string {
        return this.#dataSet.getKeyValue(factory, id, prependConstructor);
    }

    /** Returns the key value of a row (natural key or id) -  this is called by the Extractor */
    private getExtractPackage(packageName: string): ExtractPackage {
        return this.#extractPackageDict[packageName];
    }

    /** Do we extract the row or skip it */
    private keepRow(factory: NodeFactory, row: SqlRow): boolean {
        return (
            (!row.__sourceInfo || row.__sourceInfo.layer === this.layer) &&
            !(factory.name === 'User' && row.email === '<EMAIL>')
        );
    }

    /** Returns the key properties for a factory (only _id if the factory does not have a natural key) */
    private static getKeyProperties(factory: NodeFactory): Dict<Property> {
        const naturalKey = factory.naturalKey;

        const rootNaturalKey = factory.rootFactory.naturalKey;
        const omitId = !!rootNaturalKey;

        const keyProperties = {} as Dict<Property>;
        // _id and natural key properties go first (for readability)
        if (!omitId) keyProperties._id = factory.findProperty('_id');
        if (naturalKey) {
            naturalKey.forEach(name => {
                const property = factory.findProperty(name);
                keyProperties[property.requiredColumnName] = property;
            });
        }

        return keyProperties;
    }

    /** Returns the ExtractPackageFactory for a package and factory, creating it if necessary  */
    private static makeExtractPackageFactory(
        extractPackage: ExtractPackage,
        factory: NodeFactory,
    ): ExtractPackageFactory {
        let packageFactory = extractPackage.factoryDataDict[factory.name];
        const keyProperties = RowDispatcher.getKeyProperties(factory);
        if (!packageFactory) {
            packageFactory = {
                extractPackage,
                factory,
                keyProperties,
                rowProperties: {},
                rows: [],
                rowExtensionProperties: {},
                rowExtensions: [],
            };
            extractPackage.factoryDataDict[factory.name] = packageFactory;
        }
        return packageFactory;
    }

    /** Returns an extract package by index */
    private getExtractPackageByIndex(index: integer): ExtractPackage {
        const extractPackage = Object.values(this.#extractPackageDict)[index - 1];
        if (!extractPackage) throw new LogicError(`invalid package index: ${index}`);
        return extractPackage;
    }

    /** Returns the factory of a referenced row */
    private getReferencedFactory(property: ReferenceProperty, referencedRow: SqlRow): NodeFactory {
        return referencedRow._constructor
            ? this.application.getFactoryByName(referencedRow._constructor)
            : property.targetFactory;
    }

    /**
     * Returns the default destination package of a reference.
     * Handles special cases like SysServiceOptionState.serviceOption
     * but does not take into account the __packageIndexes.__this of the referenced row.
     */
    private getReferencedPackage(
        property: ReferenceProperty,
        referencedFactory: NodeFactory,
        referencedRow: SqlRow,
    ): ExtractPackage {
        const referencedPackage =
            property.fullName === 'SysServiceOptionState.serviceOption'
                ? this.application.findPackage(referencedRow.package)
                : referencedFactory.package;
        if (!referencedPackage) throw property.logicError('referenced package not found');
        return this.getExtractPackage(referencedPackage.name);
    }

    /** Compute the package index of a reference property in a row */
    private computeReferencePackageIndex(
        rowExtractPackage: ExtractPackage,
        property: ReferenceProperty,
        row: SqlRow,
    ): integer {
        const id = row[property.requiredColumnName];
        if (!id) {
            if (!property.isNullable) throw property.logicError('invalid null reference');
            return 0;
        }
        const referencedRow = this.#dataSet.lookupRowById(property.targetFactory, id);
        if (!referencedRow) throw property.logicError(`reference not found: ${id}`);
        const referencedFactory = this.getReferencedFactory(property, referencedRow);

        let propertyExtractPackage = rowExtractPackage;
        const referencedExtractPackage = this.getReferencedPackage(property, referencedFactory, referencedRow);
        if (referencedExtractPackage.packageIndex > propertyExtractPackage.packageIndex)
            propertyExtractPackage = referencedExtractPackage;

        const definingExtractPackage = this.getExtractPackage(property.definingPackage.name);
        if (definingExtractPackage.packageIndex > propertyExtractPackage.packageIndex)
            propertyExtractPackage = definingExtractPackage;

        if (referencedRow.__packageIndexes.__this > 0) {
            if (referencedRow.__packageIndexes.__this > propertyExtractPackage.packageIndex) {
                propertyExtractPackage = this.getExtractPackageByIndex(referencedRow.__packageIndexes.__this);
            }
        }
        return propertyExtractPackage.packageIndex;
    }

    /** May the property be omitted in the CSV file because of its value */
    private static omitPropertyValue(property: Property, value: any): boolean {
        // omit falsy values except 0, to keep the null / 0 distinction on nullale numeric properties
        if (value == null || value === false || value === '') return true;
        if (property.isIntegerProperty() || property.isDecimalProperty() || property.isFloatingPointProperty()) {
            if (!property.isNullable && parseFloat(value) === 0) return true;
            return false;
        }
        const defValue = property.name === '_customData' ? {} : property.getTypeDefaultValue();
        return _.isEqual(value || '', defValue || '');
    }

    /** Compute the package indexes of a row */
    private computeRowPackageIndexes(factory: NodeFactory, storedProperties: Property[], row: SqlRow): void {
        let rowExtractPackage = this.getExtractPackage(factory.package.name);
        if (!this.keepRow(factory, row)) return;

        if (row.__sourceInfo) {
            const sourceExtractPackage = this.getExtractPackage(row.__sourceInfo.pack);
            if (sourceExtractPackage.packageIndex > rowExtractPackage.packageIndex)
                rowExtractPackage = sourceExtractPackage;
        }

        storedProperties.forEach(property => {
            const columnName = property.requiredColumnName;
            if (property.isReferenceProperty()) {
                const propertyPackageIndex = this.computeReferencePackageIndex(rowExtractPackage, property, row);
                if (propertyPackageIndex > rowExtractPackage.packageIndex) {
                    if (
                        row.__packageIndexes[columnName] > 0 &&
                        property.definingPackage === factory.package &&
                        !property.isNullable
                    ) {
                        rowExtractPackage = this.getExtractPackageByIndex(row.__packageIndexes[columnName]);
                    }
                }
                row.__packageIndexes[columnName] = propertyPackageIndex;
            } else {
                const propertyPackageIndex = RowDispatcher.omitPropertyValue(property, row[columnName])
                    ? 0
                    : this.getExtractPackage(property.definingPackage.name).packageIndex;
                row.__packageIndexes[columnName] = propertyPackageIndex;
            }
        });

        if (row.__packageIndexes.__this < rowExtractPackage.packageIndex) {
            row.__packageIndexes.__this = rowExtractPackage.packageIndex;
            this.#packageIndexesModified = true;
        }
    }

    /** Compute the package indexes of all the rows of a factory */
    private computePackageIndexesOnFactoryRows(factory: NodeFactory): void {
        const rows = this.#dataSet.getFactoryRows(factory);

        if (!rows) return;

        const storedProperties = factory.properties.filter(property => {
            if (!property.isStored) return false;
            if (property.name === '_id') return true;
            if (property.name === '_vendor') return true;
            if (property.name === '_tags') return true;
            if (property.isSystemProperty) return false;
            return true;
        });

        rows.forEach(row => this.computeRowPackageIndexes(factory, storedProperties, row));
    }

    /**
     * Compute the package indexes of all the rows of all factories.
     * Iterate until this.#packageIndexesModified remains false.
     */
    private computeIndexesOnAllRows(): void {
        do {
            this.#packageIndexesModified = false;
            this.application.getAllFactories().forEach(factory => this.computePackageIndexesOnFactoryRows(factory));
        } while (this.#packageIndexesModified);
    }

    private dispatchRow(factoryData: ExtractPackageFactory, row: SqlRow): void {
        if (row.__packageIndexes.__this > factoryData.extractPackage.packageIndex) return;
        const isExtension = row.__packageIndexes.__this < factoryData.extractPackage.packageIndex;
        const propertySet = isExtension ? factoryData.rowExtensionProperties : factoryData.rowProperties;
        const factory = factoryData.factory;

        let hasNonKeyColumns = !isExtension;
        factory.properties.forEach(property => {
            if (!property.isStored) return;
            const columnName = property.requiredColumnName;
            if (columnName === '_id' || factoryData.keyProperties[columnName]) return;
            if (columnName === '__this') return;
            if (columnName === '_vendor' && isExtension) return;
            const packageIndex = row.__packageIndexes[columnName];
            const propertyPackageIndex =
                packageIndex || this.getExtractPackage(property.definingPackage.name).packageIndex;
            if (propertyPackageIndex !== factoryData.extractPackage.packageIndex) {
                if (isExtension) return;
                if (propertyPackageIndex > factoryData.extractPackage.packageIndex) return;
            }
            if (packageIndex) {
                propertySet[property.requiredColumnName] = property;
                hasNonKeyColumns = true;
            } else if (columnName === '_vendor' || (property.isEnumProperty() && !property.isNullable)) {
                propertySet[property.requiredColumnName] = property;
            }
        });

        if (!hasNonKeyColumns && isExtension) return;

        if (isExtension) factoryData.rowExtensions.push(row);
        else factoryData.rows.push(row);
    }

    /**
     * Get the unique package indexes found in an array of rows.
     */
    private static getPackageIndexesUsedByRows(rows: SqlRow[]): integer[] {
        const packageIndexes = [] as integer[];
        rows.forEach(row => {
            Object.values(row.__packageIndexes).forEach(packageIndex => {
                if (packageIndex > 0 && !packageIndexes.includes(packageIndex)) packageIndexes.push(packageIndex);
            });
        });
        return packageIndexes;
    }

    /**
     * Dispatch the rows of a given factory
     */
    private dispatchFactoryRows(factory: NodeFactory): void {
        const rows = this.#dataSet.getFactoryRows(factory);
        if (!rows || rows.length === 0) return;

        // Get the indexes of the packages in which we will generate CSV files.
        const packageIndexes = RowDispatcher.getPackageIndexesUsedByRows(rows);

        // Dispatch the factory's rows in these packages
        packageIndexes.forEach(packageIndex => {
            const extractPackage = this.getExtractPackageByIndex(packageIndex);
            const factoryData = RowDispatcher.makeExtractPackageFactory(extractPackage, factory);
            rows.forEach(row => this.dispatchRow(factoryData, row));
        });
    }

    /**
     * Dispatch all the rows of all factories
     */
    private dispatchAllRows(): void {
        this.application.getAllFactories().forEach(factory => this.dispatchFactoryRows(factory));
    }

    /**
     * Read all the rows and dispatch them
     */
    async readAndDispatchAllRows(tenantId: string): Promise<ExtractPackage[]> {
        await this.#dataSet.readAllRowsFromSql(tenantId);
        this.computeIndexesOnAllRows();
        this.dispatchAllRows();
        return Object.values(this.#extractPackageDict);
    }
}
