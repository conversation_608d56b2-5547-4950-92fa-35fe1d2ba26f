import { Application, Context, Dict, NodeFactory } from '@sage/xtrem-core';
import { parallelForEach } from '../util/layer-util';
import { SqlRow } from './extract-util';

/**
 * @internal
 *
 * The data set that we build when we extract data regenerate the CSV files of a layer.
 * The contents of this data set comes from the SQL tables.
 */
export class ExtractDataSet {
    /**
     * Extracted SQL rows, grouped by factory names.
     * This is filled first, when we read the SQL tables
     */
    #rowsByFactory: Dict<SqlRow[]> = {};

    /**
     * Extracted SQL rows, indexed by root factory name and then _id.
     * This is used to resolve references and replace the _id by the natural key.
     * This is filled first, when we read the SQL tables
     */
    #rowsByRootFactoryAndId: Dict<Dict<SqlRow>> = {};

    constructor(
        readonly application: Application,
        readonly layer: string,
    ) {}

    /** Get the extracted rows for a given factory */
    getFactoryRows(factory: NodeFactory): SqlRow[] {
        return this.#rowsByFactory[factory.name];
    }

    /**
     * Read the rows of a factory from the SQL database and record them
     * into this.#rowsByFactory and this.#rowsByRootFactoryAndId.
     */
    private async readFactoryRowsFromSql(context: Context, factory: NodeFactory): Promise<SqlRow[]> {
        if (!factory.tableName) return [];
        if (factory.isSkippedByLayerExtract) return [];

        // Build the joins with the super factories.
        const getSuperFactories = (f: NodeFactory | undefined): NodeFactory[] =>
            f ? [f, ...getSuperFactories(f.baseFactory)] : [];
        const factories = getSuperFactories(factory);

        const columns = factories.map((__, i) => `T${i}.*`);
        const joins = factories
            .slice(1)
            .map((f, i) => `JOIN ${context.schemaName}.${f.tableName} T${i + 1} ON T${i + 1}._id = T0._id`)
            .join('\n');

        // Build the where clause
        const args = [];
        let where = '';

        if (!factory.isSharedByAllTenants) {
            where = `WHERE T${factories.length - 1}._tenant_id = $1`;
            args.push(context.tenantId);
        }

        // Select the rows
        const rows = await context.executeSql<SqlRow[]>(
            `SELECT ${columns}
            FROM ${context.schemaName}.${factory.tableName} T0
            ${joins}
            ${where}
            ORDER BY T0._id ASC`,
            args,
        );

        // Record the rows into this.#rowsByRootFactoryAndId
        let rowsById = this.#rowsByRootFactoryAndId[factory.rootFactory.name];
        if (!rowsById) {
            rowsById = {};
            this.#rowsByRootFactoryAndId[factory.rootFactory.name] = rowsById;
        }
        rows.forEach(row => {
            row.__sourceInfo = row._source_id ? JSON.parse(row._source_id) : null;
            row.__packageIndexes = { __this: 0 };
            rowsById[row._id] = row;
        });

        // Record the rows into this.#rowsByFactory
        this.#rowsByFactory[factory.name] = rows;
        return rows;
    }

    /**
     * Read the rows of all factories from SQL
     */
    async readAllRowsFromSql(tenantId: string): Promise<void> {
        await parallelForEach(this.application.getAllSortedFactories(), async factory => {
            if (factory.isAbstract) return;
            await this.application.withReadonlyContext(tenantId, async context => {
                await this.readFactoryRowsFromSql(context, factory);
            });
        });
    }

    /**
     * Looks up a row by _id.
     * Returns undefined if the row is not found.
     * This is used to resolve references.
     */
    lookupRowById(factory: NodeFactory, id: any): SqlRow | undefined {
        return this.#rowsByRootFactoryAndId[factory.rootFactory.name]?.[id];
    }

    /** Get the natural key value of a row
     * @param factory The factory of the row
     * @param row The row
     * @param skipConstructor If true, the _constructor property is not included in the natural key
     */
    getNaturalKeyValueOfRow(factory: NodeFactory, row: SqlRow, prependConstructor = false): string {
        if (!factory.naturalKey) return row._id;

        const naturalKey =
            prependConstructor && !factory.naturalKey.includes('_constructor')
                ? ['_constructor', ...factory.naturalKey]
                : factory.naturalKey;

        return naturalKey
            .map(propertyName => {
                const property = factory.findProperty(propertyName);
                const columnName = property.requiredColumnName;
                const value = row[columnName];
                if (!property.isReferenceProperty()) return value;
                // Recurse if the property is a reference
                // If the reference is abstract, we need to include the concrete class name in the key
                // For example: PurchaseDocumentLineTax has a document property which is a reference to abstract BasePurchaseDocumentLine.
                // BasePurchaseDocumentLine has a reference to abstract BasePurchaseDocument.
                // In this case we only need the concrete class for BasePurchaseDocumentLine, for eg. PurchaseInvoiceLine,
                // we can skip PurchaseInvoice as part of the natural key for BasePurchaseDocument
                return this.getKeyValue(property.targetFactory, value, property.targetFactory.isAbstract);
            })
            .join('|');
    }

    /**
     * Converts an _id to a key for a reference column in the CSV.
     * The returned key is the natural key value if the factory has a natural key, the _id otherwise.
     */
    getKeyValue(factory: NodeFactory, id: any, prependConstructor = false): string {
        if (!factory.naturalKey || !id) return id;
        const targetRow = this.lookupRowById(factory, id);
        if (!targetRow) throw factory.logicError(`reference not found: _id=${id}`);
        return this.getNaturalKeyValueOfRow(factory, targetRow, prependConstructor);
    }
}
