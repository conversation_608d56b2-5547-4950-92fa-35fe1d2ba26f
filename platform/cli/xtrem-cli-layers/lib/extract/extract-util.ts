import { Dict, integer, NodeFactory, Package, Property } from '@sage/xtrem-core';
import { Row } from '../util/layer-util';

/**
 * @internal
 * Source info is the JSON structure that we store in the _source_id column when we load the data.
 * It allows us to filter the rows by layer and to extract the rows in the package from which it was loaded.
 */
interface SourceInfo {
    pack: string;
    layer: string;
    isTestFixture: boolean;
}

/**
 * @internal
 * A SQL row
 *
 * We use two extra fields to store extraction metadata:
 *  - __sourceInfo: the parsed _source_id string. We store it to parse it only once.
 *  - __packageIndexes: the index of the package in which the properties will be saved.
 *    Its row extensions, if any, will be in packages with a higher index.
 */
export type SqlRow = Row & {
    _id: string;
    __sourceInfo: SourceInfo | null;
    __packageIndexes: Dict<integer> & { __this: integer };
};

/**
 * @internal
 * ExtractPackage contains the data that will be extracted into a given package
 */
export interface ExtractPackage {
    /** The package */
    pack: Package;

    /**
     * The index of the package.
     * The index increases as we go from low-level packages (xtrem-system) to high level ones (xtrem-services-main).
     * This index allows us to find the lowest package which satisfies all the references of a row or a row-extension.
     */
    packageIndex: number;

    /**
     * The dictionary of factory data for this package (indexed by factory name)
     */
    factoryDataDict: Dict<ExtractPackageFactory>;
}

/**
 * @internal
 * ExtractPackageFactory contains the data that will be extracted for a given factory inside a given package
 */
export interface ExtractPackageFactory {
    /** The package data to which it belongs */
    extractPackage: ExtractPackage;

    /** The factory */
    factory: NodeFactory;

    /** The key properties that we must save in every record, indexed by column name */
    keyProperties: Dict<Property>;

    /** The properties for the _layers_ CSV file, indexed by column name */
    rowProperties: Dict<Property>;
    /** The rows which will be extracted in the _layers_ CSV file */
    rows: SqlRow[];

    /** The properties for the _extension-layers_ CSV file, indexed by column name */
    rowExtensionProperties: Dict<Property>;
    /** The rows which will be extracted in the _extension-layers_ CSV file */
    rowExtensions: SqlRow[];
}
