import { Application, asyncArray, AsyncResponse, Datetime, MimeTypeHelper, Property } from '@sage/xtrem-core';
import { stringify } from 'csv-stringify/sync';
import * as fs from 'fs';
import * as fsExtra from 'fs-extra';
import * as _ from 'lodash';
import * as os from 'os';
import * as fsp from 'path';
import { BuiltInParserName } from 'prettier';
import { uploadedFilesSubFolder } from '../load/loader';
import { escapeEolChars, parallelForEach } from '../util/layer-util';
import { loggers } from '../util/loggers';
import { prettify } from '../util/prettier';
import { ExtractPackage, ExtractPackageFactory, SqlRow } from './extract-util';
import { RowDispatcher } from './row-dispatcher';

/**
 * @internal
 * Type for the callback which provides the filename in which stream data should be extracted
 */
type GetPropertyFilename = (property: Property, row: SqlRow) => string;

/**
 * @internal
 *
 * This class handles the whole CSV extraction process.
 * It iterates through the ExtractPackage and ExtractPackageFactory dicts created by the RowDispatcher.
 * to generate the CSV files in the `layers` and `extension-layers` directories.
 */
export class Extractor {
    /** The row dispatcher which created the ExtractPackage and ExtractPackageFactory dicts */
    #rowDispatcher: RowDispatcher;

    constructor(
        readonly application: Application,
        readonly layer: string,
    ) {
        this.#rowDispatcher = new RowDispatcher(application, layer);
    }

    static fileThreshold = 50;

    /**
     * Guess the extension of the file in which binary stream data will be stored.
     */
    private static async getBinaryStreamExtension(value: any): Promise<string> {
        const type = await MimeTypeHelper.guessFromBuffer(value);
        switch (type) {
            case 'image/webp':
                return 'webp';
            case 'image/jpeg':
                return 'jpeg';
            case 'image/png':
                return 'png';
            case 'image/gif':
                return 'gif';
            case 'image/tiff':
                return 'tiff';
            case 'image/svg+xml':
                return 'svg';
            case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
                return 'docx';
            case 'application/pdf':
                return 'pdf';
            case 'application/json':
                return 'json';
            case 'application/csv':
            case 'text/csv':
                return 'csv';
            case 'text/plain':
                return 'txt';
            case 'application/xml':
                if (value.subarray(0, 100).toString('utf8').includes('<svg ')) {
                    return 'svg';
                }
                return 'xml';
            case 'font/woff2':
                return 'woff2';
            default: {
                return 'bin';
            }
        }
    }

    /**
     * Guess the extension of the file in which text stream data will be stored.
     */
    private static async getTextStreamExtension(property: Property, value: any): Promise<string> {
        if (property.isJsonProperty()) return 'json';
        const type = await MimeTypeHelper.guessFromString(value);
        switch (type) {
            case 'text/html':
                return 'html';
            case 'text/plain':
                return 'txt';
            case 'application/json':
                return 'json';
            default:
                throw property.logicError(`unsupported text type: ${type}`);
        }
    }

    private static getPrettierParser(extension: string): BuiltInParserName | undefined {
        switch (extension) {
            case 'json':
                return 'json';
            case 'graphql':
                return 'graphql';
            case 'yml':
            case 'yaml':
                return 'yaml';
            default:
                return undefined;
        }
    }

    private static async mkdir(dir: string): Promise<void> {
        if (fs.existsSync(dir)) return;
        if (dir.includes('/node_modules/')) {
            throw new Error(`Cannot create directory in node_modules: ${dir}`);
        }
        await fs.promises.mkdir(dir, { recursive: true });
    }

    private static async checkThatFileDidNotChange(
        filename: string,
        content: string | Buffer,
        encoding: BufferEncoding,
    ): Promise<void> {
        const oldContent = await fs.promises.readFile(filename, encoding);
        if (oldContent.toString() === content.toString()) return;

        // Save the file under a different name to make it easy to compare with the original file.
        const newFilename = `${filename}.new`;
        await fs.promises.writeFile(newFilename, content, encoding);
        // Throw an error to indicate that the file has changed.
        throw new Error(`Cannot update file under node_modules.
            New data has been saved in ${newFilename}.
            Compare it with ${filename} to see the changes.`);
    }

    private static async writeFile(
        filename: string,
        content: string | Buffer,
        encoding: BufferEncoding = 'utf8',
    ): Promise<void> {
        if (filename.includes('/node_modules/')) {
            if (!fs.existsSync(filename)) {
                throw new Error(`Cannot create file in node_modules: ${filename}`);
            }
            await this.checkThatFileDidNotChange(filename, content, encoding);
        } else {
            await fs.promises.writeFile(filename, content, encoding);
        }
    }

    /**
     * Save stream data into a file
     */
    private static async saveStreamToFile(property: Property, value: any, filename: string): Promise<string> {
        await this.mkdir(fsp.dirname(filename));
        let ext: string;
        let filenameWithExt: string;
        if (property.isBinaryStreamProperty()) {
            ext = await this.getBinaryStreamExtension(value);
            filenameWithExt = `${filename}.${ext}`;
            await this.writeFile(filenameWithExt, value);
            loggers.csv.info(`saved binary document to file: ${filenameWithExt}`);
        } else {
            ext = property.isJsonProperty() ? 'json' : await this.getTextStreamExtension(property, value);
            filenameWithExt = `${filename}.${ext}`;
            const prettierParser = this.getPrettierParser(ext);
            const content = prettierParser ? await prettify(value, prettierParser) : value;
            await this.writeFile(filenameWithExt, content, 'utf8');
            loggers.csv.info(`saved text document to file: ${filenameWithExt}`);
        }
        return `file:${fsp.basename(filenameWithExt)}`;
    }

    private static convertTextStreamValueToCsv(
        property: Property,
        value: any,
        getPropertyFilename: GetPropertyFilename,
        row: SqlRow,
    ): AsyncResponse<any> {
        if (!value) return null;
        // keep small strings that don't contain newlines into the CSV file
        if (value.length < Extractor.fileThreshold && !value.includes('\n')) return value;
        return Extractor.saveStreamToFile(property, value, getPropertyFilename(property, row));
    }

    private convertArrayValueToCsv(property: Property, value: any): AsyncResponse<any> {
        if (!value) {
            return !property.isNullable ? '[]' : value;
        }

        if (property.isForeignNodeProperty()) {
            if (property.targetFactory.naturalKey != null) {
                // Export the natural key (and not the id) of the record
                const getNaturalKey = (val: any): any => {
                    return this.#rowDispatcher.getKeyValue(
                        property.targetFactory,
                        val,
                        property.targetFactory.isAbstract,
                    );
                };

                if (Array.isArray(value)) {
                    return value.map(getNaturalKey);
                }
                return getNaturalKey(value);
            }
        }

        if (typeof value === 'string' && value.startsWith('{') && value.endsWith('}')) {
            return value.substring(1, value.length - 1).split(',');
        }
        return value;
    }

    /**
     * Convert the value which was read from SQL to a CSV value.
     * If the property is a stream property the value will be stored into a separate file and replaced by
     * a file:filename string in the CSV (unless it is a very short string without newlines).
     */
    private convertValueToCsv(
        property: Property,
        value: any,
        getPropertyFilename: GetPropertyFilename,
        row: SqlRow,
    ): AsyncResponse<any> {
        if (property.isBooleanProperty()) {
            return value ? 'Y' : 'N';
        }
        if (property.isReferenceProperty()) {
            return this.#rowDispatcher.getKeyValue(property.targetFactory, value, property.targetFactory.isAbstract);
        }
        if (property.isIntegerProperty() || property.isDecimalProperty() || property.isFloatingPointProperty()) {
            if (!value && !property.isNullable) return '0';
            return typeof value === 'string' && value?.indexOf('.') > 0 ? value.replace(/\.0+$/, '') : value;
        }
        if (property.isDatetimeProperty()) {
            return value ? Datetime.fromValue(value).toString() : null;
        }
        if (property.isTextStreamProperty()) {
            return Extractor.convertTextStreamValueToCsv(property, value, getPropertyFilename, row);
        }
        if (property.isBinaryStreamProperty()) {
            if (!value) return null;
            return Extractor.saveStreamToFile(property, value, getPropertyFilename(property, row));
        }
        if (property.isJsonProperty()) {
            if (!value) return property.isNullable ? null : '{}';
            // stringify with final newline, to be compatible with prettier.
            let stringified = `${JSON.stringify(value, null, 4)}\n`;
            // if stringified is not too big, try reformatting without newlines to keep it into CSV cell
            if (stringified.length < Extractor.fileThreshold * 1.5) {
                const singleLine = JSON.stringify(value);
                stringified = singleLine.length < Extractor.fileThreshold ? singleLine : stringified;
            }
            return Extractor.convertTextStreamValueToCsv(property, stringified, getPropertyFilename, row);
        }
        if (property.isEnumProperty() && !property.isNullable && !value) {
            return property.dataType.values[0];
        }
        if (property.isArrayProperty()) {
            return this.convertArrayValueToCsv(property, value);
        }
        return value;
    }

    /**
     * Convert a row which was read from SQL to a CSV row.
     */
    private async convertRowToCsv(
        csvProperties: Property[],
        row: SqlRow,
        getPropertyFilename: GetPropertyFilename,
    ): Promise<SqlRow> {
        const mapped = {} as SqlRow;
        await asyncArray(csvProperties).forEach(async property => {
            mapped[property.requiredColumnName] = await this.convertValueToCsv(
                property,
                row[property.requiredColumnName],
                getPropertyFilename,
                row,
            );
        });
        return mapped;
    }

    /**
     * Save a CSV file.
     */
    private static async saveCsvFile(path: string, records: SqlRow[]): Promise<void> {
        const recordsToWrite = records.map(record =>
            _.mapValues(record, value => (typeof value === 'string' ? escapeEolChars(value) : value)),
        );
        const contents = stringify(recordsToWrite, { quoted: true, delimiter: ';', header: true });
        await this.writeFile(path, contents, 'utf8');
    }

    /**
     * Extract an array of rows of a given ExtractPackageFactory to CSV
     */
    private async extractRowsToCsv(
        packageFactory: ExtractPackageFactory,
        { isExtension, isTestFixture }: { isExtension: boolean; isTestFixture: boolean },
    ): Promise<void> {
        const rows = (isExtension ? packageFactory.rowExtensions : packageFactory.rows).filter(
            row => !!row.__sourceInfo?.isTestFixture === isTestFixture,
        );
        if (rows.length === 0) return;

        const pack = packageFactory.extractPackage.pack;
        const dataDir = `${pack.dir}${isTestFixture ? '/test/fixtures' : ''}/data`;
        const dir = `${dataDir}/${isExtension ? 'extension-layers' : 'layers'}/${this.layer}`;

        const { factory } = packageFactory;
        const keyProperties = Object.values(packageFactory.keyProperties);
        const nonKeyPropertiesDict = isExtension ? packageFactory.rowExtensionProperties : packageFactory.rowProperties;
        // The order of Object.values(nonKeyPropertiesDict) is not reliable because this set is built row by row.
        // So we rebuild nonKeyProperties by filtering factory.properties.
        const nonKeyProperties = factory.properties.filter(
            property => property.isStored && nonKeyPropertiesDict[property.requiredColumnName],
        );
        if (nonKeyProperties.length === 0 && isExtension) return;

        const csvProperties = [...keyProperties, ...nonKeyProperties];
        const tableKebabName = _.kebabCase(packageFactory.factory.tableName);
        const getPropertyFilename = (property: Property, row: SqlRow): string => {
            const key = this.#rowDispatcher.getNaturalKeyValueOfRow(packageFactory.factory, row);
            return fsp.join(dir, tableKebabName, `${_.kebabCase(property.name)}--${_.kebabCase(key)}`);
        };
        const rowsToWrite = await asyncArray(rows)
            .map(row => this.convertRowToCsv(csvProperties, row, getPropertyFilename))
            .toArray();
        if (rowsToWrite.length === 0 || csvProperties.length === 0) return;
        const path = fsp.join(dir, `${tableKebabName}.csv`);
        await Extractor.mkdir(dir);
        await Extractor.saveCsvFile(path, rowsToWrite);
    }

    /**
     * Extract the rows of a given ExtractPackageFactory to CSV
     */
    private async extractFactoryRows(packageFactory: ExtractPackageFactory): Promise<void> {
        const factory = packageFactory.factory;
        if (factory.isAbstract) return;
        if (factory.isPlatformNode && !factory.isPlatformNodeExportable) return;
        // SysPackAllocation is marked with isPlatformNodeExportable true because tenant export needs its data
        // but we don't want its data in layers CSV files.
        if (factory.name === 'SysPackAllocation') return;

        if (packageFactory.rows.length > 0) {
            await this.extractRowsToCsv(packageFactory, { isExtension: false, isTestFixture: false });
            await this.extractRowsToCsv(packageFactory, { isExtension: false, isTestFixture: true });
        }
        if (packageFactory.rowExtensions.length > 0) {
            await this.extractRowsToCsv(packageFactory, { isExtension: true, isTestFixture: false });
            await this.extractRowsToCsv(packageFactory, { isExtension: false, isTestFixture: false });
        }
    }

    /**
     * Extract all the rows of an array of ExtraPackage to CSV
     */
    private async extractAllPackageRows(allExtractPackages: ExtractPackage[]): Promise<void> {
        await asyncArray(allExtractPackages).forEach(async extractPackage => {
            await parallelForEach(Object.values(extractPackage.factoryDataDict), factoryData =>
                this.extractFactoryRows(factoryData),
            );
        });
    }

    /**
     * Delete the existing CSV files of the extracted layer in a given package
     */
    private async deleteOldPackageLayer(extractPackage: ExtractPackage): Promise<void> {
        const dirs = ['layers', 'extension-layers'].map(layerDir =>
            fsp.join(extractPackage.pack.dir, `data/${layerDir}/${this.layer}`),
        );
        await parallelForEach(dirs, dir => fs.promises.rm(dir, { recursive: true, force: true }));
    }

    /**
     * Delete the existing CSV files of the extracted layer in all packages
     */
    private async deleteOldLayer(allExtractPackages: ExtractPackage[]): Promise<void> {
        await asyncArray(allExtractPackages).forEach(async extractPackage => {
            await this.deleteOldPackageLayer(extractPackage);
        });
    }

    /**
     * Read the layer data from SQL and regenerate all its CSV files.
     */
    async extractData(tenantId: string): Promise<void> {
        const allExtractPackages = await this.#rowDispatcher.readAndDispatchAllRows(tenantId);
        const tempsDirs = await this.copyTempFiles(allExtractPackages);
        await this.deleteOldLayer(allExtractPackages);
        await this.extractAllPackageRows(allExtractPackages);
        await this.restoreTempFiles(tempsDirs);
    }

    /**
     * Copies temporary files from the specified extract packages to a base temporary directory.
     * @param allExtractPackages - An array of extract packages.
     * @param baseTempDir - The base temporary directory to copy the files to. Defaults to a unique temporary directory.
     * @returns An array of objects containing the source and destination paths of the copied files.
     */
    async copyTempFiles(
        allExtractPackages: ExtractPackage[],
        baseTempDir = fsp.join(os.tmpdir(), `tmp-dir-${Date.now()}`),
    ): Promise<{ src: string; dest: string }[]> {
        await fsExtra.ensureDir(baseTempDir);
        const tempsDirs: { src: string; dest: string }[] = [];
        await asyncArray(allExtractPackages).forEach(async extractPackage => {
            await asyncArray(['layers', 'extension-layers']).forEach(async layerDir => {
                await asyncArray([uploadedFilesSubFolder]).forEach(async dirToCopy => {
                    try {
                        const src = fsp.join(extractPackage.pack.dir, 'data', layerDir, this.layer, dirToCopy);
                        if (fs.existsSync(src)) {
                            const dest = fsp.join(baseTempDir, src);
                            await fsExtra.copy(src, dest);

                            loggers.csv.info(`saved content of ${src} to tmp: ${dest}`);

                            tempsDirs.push({
                                src,
                                dest,
                            });
                        }
                    } catch (error) {
                        throw new Error(error);
                    }
                });
            });
        });
        return tempsDirs;
    }

    /**
     * Restores temporary files from the destination directory to the source directory.
     * @param tempsDirs - An array of objects containing the source and destination directories of the temporary files.
     * @returns A Promise that resolves when all the files have been restored successfully.
     * @throws If an error occurs while restoring the files.
     */
    // eslint-disable-next-line class-methods-use-this
    async restoreTempFiles(tempsDirs: { src: string; dest: string }[]): Promise<void> {
        try {
            await Promise.all(
                tempsDirs.map(async tempsDir => {
                    await fsExtra.copy(tempsDir.dest, tempsDir.src);
                    loggers.csv.info(`restore content of ${tempsDir.src}`);
                    await fsExtra.remove(tempsDir.dest);
                }),
            );
        } catch (error) {
            throw new Error(`An error occurred while restoring files: ${error}`);
        }
    }
}
