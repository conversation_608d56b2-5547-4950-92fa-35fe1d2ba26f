import * as prettier from 'prettier';
import { BuiltInParserName } from 'prettier';

const prettierOptions = {
    singleQuote: true,
    printWidth: 120,
    tabWidth: 4,
    useTabs: false,
    semi: true,
    trailingComma: 'all',
    arrowParens: 'avoid',
    endOfLine: 'lf',
} as const;

export function prettify(source: string, parser: BuiltInParserName): Promise<string> {
    return prettier.format(source, { ...prettierOptions, parser });
}
