import { AnyValue, Dict, FactoryMetadata, LogicError, NodeFactory, asyncArray } from '@sage/xtrem-core';
import * as _ from 'lodash';

export type Row = Dict<any>;

export function pascalCase(str: string): string {
    const camel = _.camelCase(str);
    return _.toUpper(camel[0]) + camel.substring(1);
}

export function camelCase(str: string): string {
    const snake = _.camelCase(str);
    return str[0] === '_' ? `_${snake}` : snake;
}

export function snakeCase(str: string): string {
    const snake = _.snakeCase(str);
    return str[0] === '_' ? `_${snake}` : snake;
}

export function unescapeEolChars(str: string): string {
    return str.replace(/<<\}\}(CR|LF)\{\{>>/g, (__, s) => (s === 'CR' ? '\r' : '\n'));
}

export function escapeEolChars(str: string): string {
    return str.replace(/(\n|\r)/g, s => `<<}}${s === '\n' ? 'LF' : 'CR'}{{>>`);
}

const parallelize = false; // re-enable config setting later

export async function parallelForEach<T extends AnyValue>(
    array: T[],
    body: (elt: T, index: number) => Promise<void>,
): Promise<void> {
    if (parallelize) {
        await Promise.all(array.map(body));
    } else {
        await asyncArray(array).forEach(body);
    }
}

export function parallelMap<T extends AnyValue, R extends AnyValue>(
    array: T[],
    body: (elt: T, index: number) => Promise<R>,
): Promise<R[]> {
    return parallelize ? Promise.all(array.map(body)) : asyncArray(array).map(body).toArray();
}

export function getNaturalKeyColumnNames(factoryMeta: FactoryMetadata): string[] {
    if (factoryMeta.naturalKeyColumns.length === 0) throw new LogicError(`${factoryMeta.name} : no natural key`);
    const columnNames = factoryMeta.naturalKeyColumns;
    return columnNames[0] === '_tenant_id' ? columnNames.slice(1) : columnNames;
}

export const catchableNoNaturalKeyErrorMessage = 'no natural key (should be caught)';

export function getNaturalKeyColumnNamesFromFactory(factory: NodeFactory): string[] {
    if (factory.naturalKey == null || factory.naturalKey.length === 0) {
        throw factory.logicError(catchableNoNaturalKeyErrorMessage);
    }
    return factory.naturalKey.map(snakeCase);
}

export function getRowKey(row: Row, columnNames: string[]): string {
    return columnNames.map(name => row[name]).join('|');
}
