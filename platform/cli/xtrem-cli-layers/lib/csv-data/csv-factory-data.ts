import { DataInputError, Dict, NodeFactory, ReferenceProperty, integer } from '@sage/xtrem-core';
import * as _ from 'lodash';
import { camelCase, parallelForEach, snakeCase } from '../util/layer-util';
import { loggers } from '../util/loggers';
import { CsvDataset, FactoryRows } from './csv-dataset';
import { CsvFile, CsvRow } from './csv-file';

export interface RowLocation {
    /** The file in which the row was found */
    csvFile: CsvFile;

    /** The index of the row in the file */
    rowIndex: integer;
}

/**
 * @internal
 *
 * CsvFactoryData gathers all the information that the dataset needs about a node factory.
 *
 * The dataset creates one CsvFactoryData instance for each factory that has one or more CSV files and also
 * for some system nodes that are referenced by CSV records but are not loaded from CSV.
 * It also creates CsvFactoryData instances for all the ancestors of factories in an inheritance tree.
 */
export class CsvFactoryData implements FactoryRows {
    /**
     * The CSV files found for the factory
     */
    readonly #csvFiles = [] as CsvFile[];

    /**
     * Rows loaded from the CSV files.
     * These are not the raw rows loaded from all the CSV files.
     * They have been aggregated so that there is only one row per key value (natural key or _id)
     */
    readonly rows = [] as CsvRow[];

    /**
     * The last _id which has been allocated to rows.
     * If the factory is part of an inheritance hierarchy, this value is only valid on the root factory data;
     * it is null on the other factories of the hierarchy.
     */
    #lastRowId: number | null;

    /**
     * Index of rows by _id.
     * This index is shared by all the CsvFactoryData objects of an inheritance tree because _ids are unique
     * across the entire hierarchy.
     */
    readonly #rowsById: Dict<CsvRow>;

    /**
     * Index of rows by natural key.
     * This index is null for factories that don't have a natural key.
     * This index is shared by all the CsvFactoryData objects that have the same namespace for natural keys.
     * For an inheritance tree, this namespace is usually the entire tree and the index is shared across the
     * entire tree (like rowsById).
     * But there are some special cases where the natural key is introduced by an intermediate node
     * of the tree rather than at the root. For example, AddressBase does not have a natural key but some of its subnodes have natural keys because they
     * are vital children of nodes with a natural key.
     */
    readonly #rowsByNaturalKey: Dict<CsvRow> | null;

    /**
     * Dict to detect collisions on content addressable rows
     */
    readonly #rowLocationByValuesHash = {} as Dict<RowLocation>;

    constructor(
        /** The dataset */
        private readonly dataset: CsvDataset,
        /** The factory */
        readonly factory: NodeFactory,
        /** The factory data of the root node of the inheritance hierarchy */
        readonly rootFactoryData: CsvFactoryData | null,
        /** The factory data of the root node of the natural key namespace */
        readonly naturalKeyRootFactoryData: CsvFactoryData | null,
    ) {
        if (factory.baseFactory == null) {
            // User table needs special initialization <NAME_EMAIL> user which is not in CSV files
            // and is inserted in the table before loading the CSV data.
            this.#lastRowId = factory.name === 'User' ? 1 : 0;
        } else {
            this.#lastRowId = null;
        }

        this.#rowsById = rootFactoryData ? rootFactoryData.#rowsById : {};

        if (factory.naturalKey) {
            this.#rowsByNaturalKey = naturalKeyRootFactoryData ? naturalKeyRootFactoryData.#rowsByNaturalKey : {};
        } else {
            this.#rowsByNaturalKey = null;
        }
    }

    /**
     * Add a CSV file
     */
    addCsvFile(file: CsvFile): void {
        this.#csvFiles.push(file);
    }

    get rootNaturalKey(): string[] | undefined {
        return this.naturalKeyRootFactoryData?.factory.naturalKey || this.factory.naturalKey;
    }

    /**
     * Lookup a row by its natural key or its _id.
     * Returns null if no row has been found
     */
    lookupRow(key: string | null, id: string | null): CsvRow | null {
        const found = key && this.#rowsByNaturalKey?.[key];
        if (found) {
            if (!id) return found;
            if (found._id) {
                if (found._id !== id)
                    throw this.factory.logicError(`id collision for key ${key}: requested ${id}, found ${found._id}`);
            } else {
                // record the id into the row that we found;
                found._id = id;
            }
            return found;
        }
        if (!id) return null;
        return this.#rowsById[id] || null;
    }

    /**
     * Allocate an _id for a row
     */
    private allocateRowId(): number {
        if (this.#lastRowId == null) throw this.factory.logicError('not a root factory');
        const id = this.#lastRowId + 1;
        this.#lastRowId = id;
        return id;
    }

    /**
     * Allocate an _id for all the rows that don't already have one
     */
    allocateMissingRowIds(): void {
        // if factory does not have a natural key, all rows already have an id.
        if (!this.factory.naturalKey) return;
        const rootFactoryData = this.rootFactoryData || this;
        this.rows.forEach(row => {
            if (!row._id) row._id = rootFactoryData.allocateRowId();
            if (this.factory.isVitalCollectionChild && !this.factory.isAssociationCollectionChild && !row._sort_value)
                throw this.factory.logicError(
                    `${this.factory.name}: _sort_value missing in CSV files for vital collection child`,
                );
        });
    }

    /**
     * Bump this.#lastRowId if a higher _id was read.
     */
    private adjustLastRowId(id: number): void {
        if (this.#lastRowId == null) throw this.factory.logicError('not a root factory');
        if (id > this.#lastRowId) {
            this.#lastRowId = id;
        }
    }

    /**
     * Add a row to the indexes
     */
    addRowToIndexes(row: CsvRow): void {
        if (row.__naturalKey) {
            if (!this.#rowsByNaturalKey) throw this.factory.logicError('no natural key map');
            this.#rowsByNaturalKey[row.__naturalKey] = row;
        }
        if (row._id) {
            this.#rowsById[row._id] = row;

            // keep sequence number above row._id
            const rootFactoryData = this.rootFactoryData || this;
            rootFactoryData.adjustLastRowId(row._id);
        }
    }

    private setValuesHash(row: CsvRow): void {
        const { csvFile, rowIndex } = row.__location;
        let values = _.mapKeys(
            _.omitBy(row, (_value, key) => key.startsWith('__')),
            (_value, key) => camelCase(key),
        );
        values = _.mapValues(values, (value, key) => {
            const property = this.factory.findProperty(key);
            if (!property.isReferenceProperty()) return value;
            const targetFactoryData = this.dataset.getFactoryData(property.targetFactory);
            return targetFactoryData.resolveKey(property, value);
        });
        row._values_hash = this.factory.getValuesHash(values);
        const prevLocation = this.#rowLocationByValuesHash[row._values_hash];
        if (prevLocation)
            throw new DataInputError(
                `Collision between content addressable CSV records\n${csvFile.path}: row ${rowIndex + 1} (line ${
                    rowIndex + 2
                })\n${prevLocation.csvFile.path}: row ${prevLocation.rowIndex + 1} (line ${prevLocation.rowIndex + 2})`,
            );
        this.#rowLocationByValuesHash[row._values_hash] = { csvFile, rowIndex };
    }

    setValuesHashes(): void {
        if (this.factory.isContentAddressable) this.rows.forEach(row => this.setValuesHash(row));
    }

    /**
     * Merge a row with any previous row with the same key or _id.
     */
    private mergeCsvRow(row: CsvRow, keyColumnNames: string[]): void {
        const key = keyColumnNames.map(columnName => row[columnName] || '').join('|');

        const found = this.lookupRow(key, row._id);
        if (found) {
            // preserve found layer if different from setup
            const foundCsvFile = found.__csvFile;
            Object.assign(found, row);
            if (row.__csvFile.layer === 'setup' || row.__csvFile.isExtension) found.__csvFile = foundCsvFile;
        }

        this.addRowToIndexes(found || row);

        if (!found) this.rows.push(row);
    }

    /**
     * Merge the rows read from a CSV file with rows previously read.
     */
    private mergeCsvRows(rows: CsvRow[]): void {
        const naturalKey = this.rootNaturalKey?.map(snakeCase);

        if (naturalKey && this.factory.baseFactory && !this.factory.isAbstract && !naturalKey?.includes('_constructor'))
            naturalKey.unshift('_constructor');

        const keyColumnNames = naturalKey?.map(propertyName => snakeCase(propertyName)) || ['_id'];
        rows.forEach(row => {
            this.mergeCsvRow(row, keyColumnNames);
        });
    }

    /**
     * Read all the CSV files of the factory and merge their rows.
     */
    async readFactoryCsvFiles(): Promise<void> {
        const layers = this.dataset.layers;
        // order is very important, we want to read the setup layer first
        this.#csvFiles.sort((a, b) => layers.indexOf(a.layer) - layers.indexOf(b.layer));
        await parallelForEach(this.#csvFiles, async file => {
            const rows = await file.readCsvFile();
            await file.fixRows(this.factory, rows);

            loggers.csv.verbose(() => `${file.path}: ${rows.length} rows read`);
            this.mergeCsvRows(rows);
        });
    }

    /**
     * Resolve a key value (natural key or _id) for a reference property
     * Returns null if the reference is not found.
     */
    resolveKey(property: ReferenceProperty, key: string): number | null {
        const factory = this.factory;
        let row: CsvRow | null = null;
        let fixedKey = key;
        if (factory.naturalKey) {
            if (!key) return null;
            fixedKey = key.startsWith('#') ? key.substring(1) : key;
            row = this.lookupRow(fixedKey, null);
            // If base class is abstract, include _constructor if not already in the natural key
            if (
                !row &&
                !factory.isAbstract &&
                factory.baseFactory?.isAbstract &&
                factory.naturalKey[0] !== '_constructor' &&
                !fixedKey.startsWith(`${factory.name}|`)
            ) {
                fixedKey = `${factory.name}|${fixedKey}`;
                row = this.lookupRow(fixedKey, null);
            }

            const keyValues = fixedKey.split('|');
            // If more key values than in the natural key, only use the first _constructor
            if (!row && factory.naturalKey.length < keyValues.length) {
                fixedKey = `${keyValues[0]}|${keyValues.slice(2).join('|')}`;
                row = this.lookupRow(fixedKey, null);
            }
        }

        if (!row && /^\d+$/.test(key)) {
            row = this.lookupRow(null, key);
        }
        if (!row && property.isNullable) {
            loggers.csv.warn(`${factory.name}.${key}: key not found in CSV files (ignored)`);
            return null;
        }
        if (!row) {
            const existingKeys = (factory.naturalKey ? this.#rowsByNaturalKey : this.#rowsById) || {};
            throw property.logicError(
                `reference not found: ${fixedKey}, existing keys: [${Object.keys(existingKeys)}]`,
            );
        }
        if (!row._id) throw property.logicError('_id missing');
        return row._id;
    }
}
