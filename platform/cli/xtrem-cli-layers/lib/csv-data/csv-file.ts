import { DataInputError, NodeFactory, Package, Property } from '@sage/xtrem-core';
import { parse } from 'csv-parse';
import * as fs from 'fs';
import * as _ from 'lodash';
import * as fsp from 'path';
import { finished } from 'stream';
import { promisify } from 'util';
import { Row, parallelForEach, snakeCase, unescapeEolChars } from '../util/layer-util';
import { loggers } from '../util/loggers';
import { CsvFactoryData } from './csv-factory-data';

/**
 * @internal
 *
 * A row of a CSV file
 * The __csvFile and __naturalKey fields are information that we add to the CSV row to ease or speed up processing
 * The __ prefix makes them easy to distinguish from normal row fields.
 */
export type CsvRow = Row & { __csvFile: CsvFile; __naturalKey?: string };

/**
 * Build a key for a CSV row
 * Returns the natural key if the factory has one, the _id otherwise.
 */
function recordKey(factory: NodeFactory, row: CsvRow): string {
    if (factory.naturalKey) return factory.naturalKey.map(name => row[snakeCase(name)]).join('_');
    if (!row._id) throw factory.logicError('missing _id');
    return row._id;
}

/**
 * @internal
 *
 *  CsvFile describes a CSV file.
 *
 * The dataset creates one CsvFile instance for each CSV file that it loads.
 */
export class CsvFile {
    /**
     * The sourceId that will be written to the _source_id column when we write the row to SQL
     * We store it here to avoid the allocation of a new string for each row.
     */
    readonly sourceId: string;

    constructor(
        /** The factory data */
        readonly factoryData: CsvFactoryData,
        /** The package */
        readonly pack: Package,
        /** The layer */
        readonly layer: string,
        /** The absolute path to the CSV file */
        readonly path: string,
        /** Was the file found in an extension-layers subdirectory */
        readonly isExtension: boolean,
    ) {
        this.sourceId = JSON.stringify({ pack: pack.name, layer, isTestFixture: path.includes('/test/fixtures/') });
    }

    /**
     * Reads the CSV file
     */
    async readCsvFile(): Promise<CsvRow[]> {
        if (this.factoryData.factory.isAbstract)
            throw this.factoryData.factory.logicError(`${this.path}: csv files not allowed on abstract nodes`);
        const parser = fs.createReadStream(this.path).pipe(
            parse({
                quote: '"',
                delimiter: ';',
                columns: true,
                bom: true,
            }),
        );
        const records = [] as CsvRow[];
        parser
            .on('readable', () => {
                // eslint-disable-next-line no-constant-condition
                while (true) {
                    const row = parser.read();
                    if (!row) break;
                    records.push(row);
                }
            })
            .on('error', err => {
                throw new DataInputError(`${this.path}: ${err.message}`);
            });

        await promisify(finished)(parser);

        return records;
    }

    /**
     * Read binary or text stream data from a separate file.
     */
    private async readStreamFile(property: Property, row: CsvRow, rowIndex: number): Promise<any> {
        const dir = fsp.dirname(this.path);
        const tableKebabName = _.kebabCase(property.factory.name);
        const rowValue = row[property.requiredColumnName];
        const externalFilename = CsvFile._parseExternalFilename(rowValue);
        if (externalFilename == null) {
            // The value does not describe an external file
            return rowValue;
        }

        const key = recordKey(property.factory, row);

        const expectedFilename = `${_.kebabCase(property.name)}--${_.kebabCase(key)}${fsp.extname(externalFilename)}`;
        if (expectedFilename !== externalFilename) {
            throw new Error(
                `${this.path}:${rowIndex} The file ${externalFilename} does not respect the naming convention. It should be ${expectedFilename} (format is property_name--natural_key.xxx)`,
            );
        }

        const filename = fsp.join(dir, tableKebabName, externalFilename);
        try {
            return property.isBinaryStreamProperty()
                ? await fs.promises.readFile(filename)
                : await fs.promises.readFile(filename, 'utf8');
        } catch (err) {
            throw new Error(`${this.path}:${rowIndex}, could not read file ${filename}`);
        }
    }

    /**
     * Try to parse a value that could describe an external file
     */
    private static _parseExternalFilename(value: string): string | undefined {
        // If the value related to a separate file, it will be formated as
        // file:filename
        const match = value.match(/^file:([\w-]+\.\w+)$/);
        if (!match) return undefined;
        return match[1];
    }

    /**
     * Fixes a row value
     */
    private async fixRowValue(property: Property, row: CsvRow, rowIndex: number): Promise<void> {
        if (!property.isStored) return;
        const columnName = property.requiredColumnName;
        let value: any = row[columnName];
        if (!value || typeof value !== 'string') return;

        if (property.isStringProperty()) {
            value = unescapeEolChars(value);
        } else if (property.isBooleanProperty()) {
            // Values 0 and 1 were both treated as false by old code
            // Will autofix later
            if (/\d/.test(value)) {
                loggers.csv.error(`${property.fullName}: BAD BOOLEAN VALUE: ${value}`);
                value = 'F';
            }
        } else if (property.isReferenceProperty()) {
            if (value[0] === '#') {
                value = value.substring(1);
            }
        } else if (property.isEnumProperty()) {
            if (/^\d+$/.test(value)) {
                value = property.dataType.stringValue(+value);
            }
        } else if (property.isBinaryStreamProperty() || property.isTextStreamProperty()) {
            value = await this.readStreamFile(property, row, rowIndex);
            if (property.isTextStreamProperty()) {
                value = unescapeEolChars(value);
            }
        } else if (property.isJsonProperty()) {
            value = await this.readStreamFile(property, row, rowIndex);
        }
        // remove unclosed quote at beginning of string
        if (typeof value === 'string' && value[0] === "'" && value[value.length - 1] !== "'") {
            value = value.substring(1);
        }
        row[columnName] = value;
    }

    /**
     * Fixes all the rows after read (and before merging)
     */
    async fixRows(factory: NodeFactory, rows: CsvRow[]): Promise<void> {
        let warnedAboutId = false;
        const naturalKey = this.factoryData.rootNaturalKey;
        const naturalKeyColumnNames = naturalKey?.map(snakeCase);

        await parallelForEach(rows, async (row, rowIndex) => {
            if (naturalKeyColumnNames) {
                if (row._id) {
                    if (!warnedAboutId) {
                        loggers.csv.warn(`${this.path}: invalid _id column on factory with natural key`);
                        warnedAboutId = true;
                    }
                }
            } else if (!row._id) {
                throw new DataInputError(`${this.path}: missing _id column`);
            }

            await parallelForEach(factory.properties, property => this.fixRowValue(property, row, rowIndex));

            if (row._id) row._id = +row._id;
            if (factory.baseFactory && !factory.isAbstract) {
                row._constructor = factory.nodeConstructor.name;
                if (naturalKeyColumnNames && !naturalKeyColumnNames?.includes('_constructor'))
                    naturalKeyColumnNames.unshift('_constructor');
            }

            if (naturalKeyColumnNames)
                row.__naturalKey = naturalKeyColumnNames.map(columnName => row[columnName]).join('|');
            if (!row._id && /^\|*$/.test(row.__naturalKey || '')) {
                throw factory.logicError(`${this.path}.${rowIndex}: missing key in row: '${JSON.stringify(row)}'`);
            }
            // If content addressable, track row location so that we can give good error messages on conflict
            if (factory.isContentAddressable) row.__location = { csvFile: this, rowIndex };

            if (!row._custom_data && !factory.isSharedByAllTenants) row._custom_data = {};

            // add values for system columns
            row.__csvFile = this;
            row._create_user = '<EMAIL>';
            row._update_user = '<EMAIL>';
        });
    }
}
