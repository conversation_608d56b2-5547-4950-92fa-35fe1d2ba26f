import {
    Application,
    asyncArray,
    basicProfiler,
    CoreHooks,
    Dict,
    LogicError,
    NodeFactory,
    Package,
    ReferenceProperty,
} from '@sage/xtrem-core';
import * as glob from 'glob';
import * as fsp from 'path';
import { parallelForEach, pascalCase, Row, snakeCase } from '../util/layer-util';
import { loggers } from '../util/loggers';
import { CsvFactoryData } from './csv-factory-data';
import { CsvFile, CsvRow } from './csv-file';

/**
 * @internal
 *
 * Interface for entries returned by dataSet.loadCsvRows
 */
export interface FactoryRows {
    factory: NodeFactory;
    rows: CsvRow[];
}

/**
 * @internal
 *
 * Options that influence dataset loading
 */
export interface LoadLayerOptions {
    /**
     * An optional list of node factories which will be loaded.
     * If this list is provided, only the data from these factories and their references will be loaded
     * Otherwise all the factories of the application will be loaded.
     */
    onlyFactories?: NodeFactory[];
}

/**
 * @internal
 *
 * The dataset which is built when we load data from CSV files.
 */
export class CsvDataset {
    /**
     * Factory data objects, indexed by their factory name
     */
    #factoryDataDict: Dict<CsvFactoryData> = {};

    constructor(
        /** The application */
        readonly application: Application,
        /** The layers that we load */
        readonly layers: string[],
        /** The options */
        readonly options: LoadLayerOptions,
    ) {}

    /**
     * Find a factory data object by factory name.
     * Throws an error if the factory is not found.
     */
    getFactoryData(factory: NodeFactory): CsvFactoryData {
        const factoryData = this.#factoryDataDict[factory.name];
        if (!factoryData) throw factory.logicError('factory data not found');
        return factoryData;
    }

    /**
     * Find the root factory of a natural key namespace.
     */
    private getNaturalKeyRootFactory(factory: NodeFactory): NodeFactory | null {
        if (!factory.naturalKey) return null;
        if (factory.baseFactory?.naturalKey) return this.getNaturalKeyRootFactory(factory.baseFactory);
        return factory;
    }

    /**
     * Look up a factory data object and create it if necessary.
     * This method also creates factory data objects for the ancestors of the factory.
     */
    private makeFactoryData(factory: NodeFactory): CsvFactoryData {
        let factoryData = this.#factoryDataDict[factory.name];
        if (factoryData) return factoryData;

        // Create the factory data objects for the ancestors
        if (factory.baseFactory) this.makeFactoryData(factory.baseFactory);

        // Find the root factory data for the _id index
        const rootFactoryData = factory.rootFactory !== factory ? this.getFactoryData(factory.rootFactory) : null;

        // Find the root factory data for the natural key index
        const naturalKeyRootFactory = this.getNaturalKeyRootFactory(factory);
        const naturalKeyRootFactoryData =
            naturalKeyRootFactory && naturalKeyRootFactory !== factory
                ? this.getFactoryData(naturalKeyRootFactory)
                : null;

        // Create the factory data object and register it in the dict.
        factoryData = new CsvFactoryData(this, factory, rootFactoryData, naturalKeyRootFactoryData);
        this.#factoryDataDict[factory.name] = factoryData;
        return factoryData;
    }

    /**
     * Add a CSV file to the factory data object, creating the factory data object if necessary.
     * The properties of the CSV file object are extracted from the absolute path of the file.
     */
    private addCsvFile(pack: Package, path: string): void {
        const [subDir, layer, fileName] = path.split('/').slice(-3);
        if (!this.layers.includes(layer)) return;

        const isExtension = subDir === 'extension-layers';
        const factoryName = pascalCase(fsp.basename(fileName, '.csv'));
        const factory = this.application.tryToGetFactoryByName(factoryName);
        if (!factory) {
            // This happens with factories like ReportTemplate because applicative packages don't depend on xtrem-reporting
            // So we allow it when running unit tests in individual packages, but not when loading from the main package.
            if (this.application.mainPackage.isMain) throw new LogicError(`${path}: missing factory: ${factoryName}`);
            loggers.csv.warn(`${path}: skipped because factory is not imported by package`);
            return;
        }
        // If the onlyFactories option is set, skip the file if the factory is not in the list.
        if (this.options.onlyFactories && !this.options.onlyFactories.includes(factory)) {
            return;
        }

        const factoryData = this.makeFactoryData(factory);
        factoryData.addCsvFile(new CsvFile(factoryData, pack, layer, path, isExtension));
    }

    /**
     * Fill all the CSV files for the application
     */
    private async fillCsvFiles(): Promise<void> {
        await parallelForEach(this.application.getPackages(), async pack => {
            const rootDirs = [pack.dir];
            // Add the test application dir to rootDirs if we find it in the main package.
            if (pack === this.application.mainPackage && this.application.options.testApplicationDir)
                rootDirs.push(this.application.options.testApplicationDir.replace(/\/build\//, '/'));
            // Use a glob to find all the CSV absolute paths for the package.
            const rootDirsGlob = rootDirs.length === 1 ? rootDirs[0] : `{${rootDirs.join()}}`;
            const csvFiles = await glob.glob(`${rootDirsGlob}/data/*layers/*/*.csv`);
            csvFiles.forEach(path => this.addCsvFile(pack, path));
        });
    }

    /**
     * Allocate _id values for all the rows that have a natural key but no _id.
     */
    private allocateMissingRowIds(): void {
        Object.values(this.#factoryDataDict).forEach(factoryData => factoryData.allocateMissingRowIds());
    }

    /**
     * Compute _valuesHash on content addressable factories
     */
    private computeValuesHashes(): void {
        Object.values(this.#factoryDataDict).forEach(factoryData => factoryData.setValuesHashes());
    }

    /**
     * Read the CSV files for all factories
     */
    private async readCsvFiles(): Promise<void> {
        await parallelForEach(Object.values(this.#factoryDataDict), factoryData => factoryData.readFactoryCsvFiles());
    }

    /** Return the row that carries the _vendor property for a given row */
    private getVendorRow(factory: NodeFactory, row: Row): Row {
        if (factory.isVitalChild) {
            const vitalParentProperty = factory.vitalParentProperty;
            const vitalParentFactory = vitalParentProperty.targetFactory;
            let value = row[vitalParentProperty.requiredColumnName];
            // Prepend the constructor to the natural key for subnodes
            // This is needed to resolve references to abstract nodes when loading CSV data
            if (
                vitalParentFactory.baseFactory?.isAbstract ||
                vitalParentFactory.baseFactory?.naturalKey?.[0] === '_constructor'
            ) {
                value = `${vitalParentFactory.nodeConstructor.name}|${value}`;
            }
            const vitalParentFactoryData = this.#factoryDataDict[vitalParentFactory.name];
            const vitalParent = vitalParentFactory.naturalKey
                ? vitalParentFactoryData.lookupRow(value, null)
                : vitalParentFactoryData.lookupRow(null, value);

            if (!vitalParent)
                throw vitalParentProperty.logicError(
                    `Could not find vital parent of record ${row.__naturalKey}, from file ${row.__csvFile.path}`,
                );

            return this.getVendorRow(vitalParentProperty.targetFactory, vitalParent);
        }
        return row;
    }

    /** Fix vendor values for child nodes of setup nodes */
    fixFactoryVendorValues(factoryData: CsvFactoryData): void {
        if (!factoryData.factory.isVitalChild || !factoryData.factory.hasVendorProperty) return;
        factoryData.rows.forEach(row => {
            const vendorRow = this.getVendorRow(factoryData.factory, row);
            row._vendor = vendorRow._vendor;
        });
    }

    private fixVendorValues(): void {
        Object.values(this.#factoryDataDict).forEach(factoryData => this.fixFactoryVendorValues(factoryData));
    }

    /**
     * Load the rows for all the factories, taking into account the onlyFactories option.
     * Returns an array of { factory, rows } objects.
     */
    loadCsvRows(): Promise<FactoryRows[]> {
        return basicProfiler.measure('layer-data-load-csvs', async () => {
            await this.fillCsvFiles();
            await this.readCsvFiles();
            this.fixVendorValues();
            return Object.values(this.#factoryDataDict);
        });
    }

    /**
     * Resolve a key value (natural key or _id) for a reference property
     * Returns null if the reference is not found.
     */
    resolveKey(property: ReferenceProperty, key: string): number | null {
        const factory = property.targetFactory;
        const factoryData = this.getFactoryData(factory);

        // The root natural key may prepend _constructor to the natural key, so we need to prepend the factory name
        // to the key if the natural key starts with _constructor, or if it's a subnode and the node is not abstract
        const rootKey =
            !factory.isAbstract &&
            (this.getNaturalKeyRootFactory(factory)?.naturalKey?.[0] === '_constructor' ||
                (factory.naturalKey && factory.baseFactory))
                ? [factory.nodeConstructor.name, key].join('|')
                : key;
        return factoryData.resolveKey(property, rootKey);
    }

    /**
     * Set row.__naturalKey for system rows that are read from the SQL database
     */
    private addNaturalKeyToRow(factory: NodeFactory, row: Row): CsvRow {
        // The root natural key may be prepended with _constructor.
        // So use it rather than factory.naturalKey to compute the natural key value.
        const naturalKey = this.getNaturalKeyRootFactory(factory)?.naturalKey;
        if (!naturalKey) throw factory.logicError('no natural key');
        row.__naturalKey = naturalKey
            .map(propertyName => {
                const property = factory.findProperty(propertyName);
                const columnName = property.requiredColumnName;
                const value = row[columnName];
                if (!property.isReferenceProperty()) return value;
                const targetFactory = property.targetFactory;
                const targetFactoryData = this.getFactoryData(targetFactory);
                const targetRow = targetFactoryData.lookupRow(null, value);
                if (!targetRow) {
                    if (property.isNullable) return null;
                    throw property.logicError(
                        `cannot build natural key: missing row for ${targetFactory.name}._id = ${value}`,
                    );
                }
                return this.addNaturalKeyToRow(targetFactory, targetRow).__naturalKey;
            })
            .join('|');
        return row as CsvRow;
    }

    /**
     * Create a CsvFactoryData object for a system factory that does not have CSV files (Meta, ...),
     * and fill it by reading from SQL.
     */
    private fillSystemRows(tenantId: string | null, factory: NodeFactory): Promise<void> {
        return this.application.withReadonlyContext(tenantId, async context => {
            if (!factory.naturalKey) return;
            const fullTableName = `${this.application.schemaName}.${factory.tableName}`;
            const naturalKeyColumns = factory.naturalKey.map(name => snakeCase(name));
            const where = factory.isSharedByAllTenants ? '' : ' WHERE _tenant_id = $1';
            const args = factory.isSharedByAllTenants ? [] : [tenantId];
            const sql = `SELECT _id,${naturalKeyColumns} FROM ${fullTableName} ${where}`;
            const rows = await context.executeSql<Dict<Row>[]>(sql, args);
            const factoryData = this.makeFactoryData(factory);
            rows.forEach(row => {
                const csvRow = this.addNaturalKeyToRow(factory, row);
                factoryData.addRowToIndexes(csvRow);
            });
        });
    }

    /**
     * Create extra CsvFactoryData objects for system factories that do not have CSV files (Meta, ...),
     * and fill them by reading from SQL.
     */
    private async fillAllSystemRows(tenantId: string | null): Promise<void> {
        // load system rows sequentially so that we can resolve natural keys
        await asyncArray(
            this.application
                .getAllFactories()
                .filter(
                    factory =>
                        factory.naturalKey &&
                        (factory.name === CoreHooks.sysManager.getUserNode().name ||
                            /^(Activity|Meta.*|SysServiceOption)$/.test(factory.name)),
                ),
        ).forEach(factory => this.fillSystemRows(tenantId, factory));
    }

    /**
     * Prepare the dataset for writing to SQL, by reading system tables from SQL and allocating missing _ids.
     */
    async prepareForWriting(tenantId: string | null): Promise<void> {
        await this.fillAllSystemRows(tenantId);
        this.allocateMissingRowIds();
        this.computeValuesHashes();
    }
}

/**
 * @internal
 *
 * Read all the setup rows for a list of factories.
 * This is called during upgrade to reload the setup data.
 */
export function readSetupRows(application: Application, factories: NodeFactory[]): Promise<FactoryRows[]> {
    const dataSet = new CsvDataset(application, ['setup'], { onlyFactories: factories });
    return dataSet.loadCsvRows();
}
