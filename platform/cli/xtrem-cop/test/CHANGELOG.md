# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

## [24.0.8](https://github.com/compare/...@sage/xtrem-cop-test@24.0.8) (2022-10-18)

### Bug Fixes


### Features


# [24.0.6](https://github.com/compare/...@sage/xtrem-cop-test@24.0.6) (2022-10-16)

### Bug Fixes


### Features


# [24.0.5](https://github.com/compare/...@sage/xtrem-cop-test@24.0.5) (2022-10-15)

### Bug Fixes


### Features


# [24.0.4](https://github.com/compare/...@sage/xtrem-cop-test@24.0.4) (2022-10-15)

### Bug Fixes


### Features


# [24.0.3](https://github.com/compare/...@sage/xtrem-cop-test@24.0.3) (2022-10-11)

### Bug Fixes


### Features


# [24.0.2](https://github.com/compare/...@sage/xtrem-cop-test@24.0.2) (2022-10-11)

### Bug Fixes


### Features


# [24.0.1](https://github.com/compare/...@sage/xtrem-cop-test@24.0.1) (2022-10-11)

### Bug Fixes


### Features

* XT-25752 remove cyrb53 Part 2 ([#9027](https://github.com/issues/9027))  ([3c2e73a](https://github.com/commit/3c2e73a6b54784fcc26a30d79ad9ac2d3b43668d))

# [24.0.0](https://github.com/compare/...@sage/xtrem-cop-test@24.0.0) (2022-09-29)

### Bug Fixes


### Features


# [23.0.48](https://github.com/compare/...@sage/xtrem-cop-test@23.0.48) (2022-09-29)

### Bug Fixes


### Features


# [23.0.47](https://github.com/compare/...@sage/xtrem-cop-test@23.0.47) (2022-09-29)

### Bug Fixes


### Features


# [23.0.46](https://github.com/compare/...@sage/xtrem-cop-test@23.0.46) (2022-09-28)

### Bug Fixes


### Features


# [23.0.45](https://github.com/compare/...@sage/xtrem-cop-test@23.0.45) (2022-09-28)

### Bug Fixes


### Features


# [23.0.44](https://github.com/compare/...@sage/xtrem-cop-test@23.0.44) (2022-09-26)

### Bug Fixes


### Features


# [23.0.43](https://github.com/compare/...@sage/xtrem-cop-test@23.0.43) (2022-09-25)

### Bug Fixes


### Features


# [23.0.42](https://github.com/compare/...@sage/xtrem-cop-test@23.0.42) (2022-09-24)

### Bug Fixes


### Features


# [23.0.41](https://github.com/compare/...@sage/xtrem-cop-test@23.0.41) (2022-09-23)

### Bug Fixes


### Features


# [23.0.40](https://github.com/compare/...@sage/xtrem-cop-test@23.0.40) (2022-09-22)

### Bug Fixes


### Features


# [23.0.39](https://github.com/compare/...@sage/xtrem-cop-test@23.0.39) (2022-09-21)

### Bug Fixes


### Features


# [23.0.38](https://github.com/compare/...@sage/xtrem-cop-test@23.0.38) (2022-09-21)

### Bug Fixes


### Features


# [23.0.37](https://github.com/compare/...@sage/xtrem-cop-test@23.0.37) (2022-09-21)

### Bug Fixes


### Features


# [23.0.36](https://github.com/compare/...@sage/xtrem-cop-test@23.0.36) (2022-09-19)

### Bug Fixes


### Features


# [23.0.35](https://github.com/compare/...@sage/xtrem-cop-test@23.0.35) (2022-09-19)

### Bug Fixes


### Features


# [23.0.34](https://github.com/compare/...@sage/xtrem-cop-test@23.0.34) (2022-09-19)

### Bug Fixes


### Features


# [23.0.33](https://github.com/compare/...@sage/xtrem-cop-test@23.0.33) (2022-09-19)

### Bug Fixes


### Features


# [23.0.32](https://github.com/compare/...@sage/xtrem-cop-test@23.0.32) (2022-09-18)

### Bug Fixes


### Features


# [23.0.31](https://github.com/compare/...@sage/xtrem-cop-test@23.0.31) (2022-09-18)

### Bug Fixes


### Features


# [23.0.30](https://github.com/compare/...@sage/xtrem-cop-test@23.0.30) (2022-09-18)

### Bug Fixes


### Features


# [23.0.29](https://github.com/compare/...@sage/xtrem-cop-test@23.0.29) (2022-09-18)

### Bug Fixes


### Features


# [23.0.28](https://github.com/compare/...@sage/xtrem-cop-test@23.0.28) (2022-09-17)

### Bug Fixes


### Features


# [23.0.27](https://github.com/compare/...@sage/xtrem-cop-test@23.0.27) (2022-09-17)

### Bug Fixes


### Features


# [23.0.26](https://github.com/compare/...@sage/xtrem-cop-test@23.0.26) (2022-09-16)

### Bug Fixes


### Features


# [23.0.25](https://github.com/compare/...@sage/xtrem-cop-test@23.0.25) (2022-09-16)

### Bug Fixes


### Features


# [23.0.24](https://github.com/compare/...@sage/xtrem-cop-test@23.0.24) (2022-09-16)

### Bug Fixes


### Features


# [23.0.23](https://github.com/compare/...@sage/xtrem-cop-test@23.0.23) (2022-09-16)

### Bug Fixes


### Features


# [23.0.22](https://github.com/compare/...@sage/xtrem-cop-test@23.0.22) (2022-09-14)

### Bug Fixes


### Features


# [23.0.21](https://github.com/compare/...@sage/xtrem-cop-test@23.0.21) (2022-09-14)

### Bug Fixes


### Features


# [23.0.20](https://github.com/compare/...@sage/xtrem-cop-test@23.0.20) (2022-09-12)

### Bug Fixes


### Features


# [23.0.19](https://github.com/compare/...@sage/xtrem-cop-test@23.0.19) (2022-09-12)

### Bug Fixes


### Features


# [23.0.18](https://github.com/compare/...@sage/xtrem-cop-test@23.0.18) (2022-09-11)

### Bug Fixes


### Features


# [23.0.17](https://github.com/compare/...@sage/xtrem-cop-test@23.0.17) (2022-09-10)

### Bug Fixes


### Features


# [23.0.16](https://github.com/compare/...@sage/xtrem-cop-test@23.0.16) (2022-09-09)

### Bug Fixes


### Features


# [23.0.15](https://github.com/compare/...@sage/xtrem-cop-test@23.0.15) (2022-09-09)

### Bug Fixes


### Features


# [23.0.14](https://github.com/compare/...@sage/xtrem-cop-test@23.0.14) (2022-09-07)

### Bug Fixes


### Features


# [23.0.13](https://github.com/compare/...@sage/xtrem-cop-test@23.0.13) (2022-09-07)

### Bug Fixes


### Features


# [23.0.12](https://github.com/compare/...@sage/xtrem-cop-test@23.0.12) (2022-09-06)

### Bug Fixes


### Features


# [23.0.11](https://github.com/compare/...@sage/xtrem-cop-test@23.0.11) (2022-09-05)

### Bug Fixes


### Features


# [23.0.10](https://github.com/compare/...@sage/xtrem-cop-test@23.0.10) (2022-09-05)

### Bug Fixes


### Features


# [23.0.9](https://github.com/compare/...@sage/xtrem-cop-test@23.0.9) (2022-09-04)

### Bug Fixes


### Features


# [23.0.8](https://github.com/compare/...@sage/xtrem-cop-test@23.0.8) (2022-09-03)

### Bug Fixes


### Features


# [23.0.7](https://github.com/compare/...@sage/xtrem-cop-test@23.0.7) (2022-09-02)

### Bug Fixes


### Features


# [23.0.6](https://github.com/compare/...@sage/xtrem-cop-test@23.0.6) (2022-09-01)

### Bug Fixes


### Features


# [23.0.5](https://github.com/compare/...@sage/xtrem-cop-test@23.0.5) (2022-08-31)

### Bug Fixes


### Features


# [23.0.4](https://github.com/compare/...@sage/xtrem-cop-test@23.0.4) (2022-08-30)

### Bug Fixes


### Features


# [23.0.3](https://github.com/compare/...@sage/xtrem-cop-test@23.0.3) (2022-08-30)

### Bug Fixes


### Features


# [23.0.2](https://github.com/compare/...@sage/xtrem-cop-test@23.0.2) (2022-08-29)

### Bug Fixes


### Features


# [23.0.1](https://github.com/compare/...@sage/xtrem-cop-test@23.0.1) (2022-08-29)

### Bug Fixes


### Features


# [23.0.0](https://github.com/compare/...@sage/xtrem-cop-test@23.0.0) (2022-08-26)

### Bug Fixes


### Features


# [22.0.25](https://github.com/compare/...@sage/xtrem-cop-test@22.0.25) (2022-08-25)

### Bug Fixes


### Features


# [22.0.24](https://github.com/compare/...@sage/xtrem-cop-test@22.0.24) (2022-08-24)

### Bug Fixes


### Features


# [22.0.23](https://github.com/compare/...@sage/xtrem-cop-test@22.0.23) (2022-08-23)

### Bug Fixes


### Features


# [22.0.22](https://github.com/compare/...@sage/xtrem-cop-test@22.0.22) (2022-08-22)

### Bug Fixes


### Features


# [22.0.21](https://github.com/compare/...@sage/xtrem-cop-test@22.0.21) (2022-08-21)

### Bug Fixes


### Features


# [22.0.20](https://github.com/compare/...@sage/xtrem-cop-test@22.0.20) (2022-08-19)

### Bug Fixes


### Features


# [22.0.19](https://github.com/compare/...@sage/xtrem-cop-test@22.0.19) (2022-08-18)

### Bug Fixes


### Features


# [22.0.18](https://github.com/compare/...@sage/xtrem-cop-test@22.0.18) (2022-08-17)

### Bug Fixes


### Features


# [22.0.17](https://github.com/compare/...@sage/xtrem-cop-test@22.0.17) (2022-08-16)

### Bug Fixes


### Features


# [22.0.16](https://github.com/compare/...@sage/xtrem-cop-test@22.0.16) (2022-08-15)

### Bug Fixes


### Features


# [22.0.15](https://github.com/compare/...@sage/xtrem-cop-test@22.0.15) (2022-08-14)

### Bug Fixes


### Features


# [22.0.14](https://github.com/compare/...@sage/xtrem-cop-test@22.0.14) (2022-08-13)

### Bug Fixes


### Features


# [22.0.13](https://github.com/compare/...@sage/xtrem-cop-test@22.0.13) (2022-08-11)

### Bug Fixes


### Features


# [22.0.12](https://github.com/compare/...@sage/xtrem-cop-test@22.0.12) (2022-08-11)

### Bug Fixes


### Features


# [22.0.11](https://github.com/compare/...@sage/xtrem-cop-test@22.0.11) (2022-08-09)

### Bug Fixes


### Features


# [22.0.10](https://github.com/compare/...@sage/xtrem-cop-test@22.0.10) (2022-08-08)

### Bug Fixes


### Features


# [22.0.9](https://github.com/compare/...@sage/xtrem-cop-test@22.0.9) (2022-08-07)

### Bug Fixes


### Features


# [22.0.8](https://github.com/compare/...@sage/xtrem-cop-test@22.0.8) (2022-08-06)

### Bug Fixes


### Features


# [22.0.7](https://github.com/compare/...@sage/xtrem-cop-test@22.0.7) (2022-08-05)

### Bug Fixes


### Features


# [22.0.6](https://github.com/compare/...@sage/xtrem-cop-test@22.0.6) (2022-08-04)

### Bug Fixes


### Features


# [22.0.5](https://github.com/compare/...@sage/xtrem-cop-test@22.0.5) (2022-08-01)

### Bug Fixes


### Features


# [22.0.4](https://github.com/compare/...@sage/xtrem-cop-test@22.0.4) (2022-07-31)

### Bug Fixes


### Features


# [22.0.3](https://github.com/compare/...@sage/xtrem-cop-test@22.0.3) (2022-07-30)

### Bug Fixes


### Features


# [22.0.2](https://github.com/compare/...@sage/xtrem-cop-test@22.0.2) (2022-07-29)

### Bug Fixes


### Features


# [22.0.1](https://github.com/compare/...@sage/xtrem-cop-test@22.0.1) (2022-07-28)

### Bug Fixes


### Features


# [22.0.0](https://github.com/compare/...@sage/xtrem-cop-test@22.0.0) (2022-07-28)

### Bug Fixes


### Features


# [21.0.36](https://github.com/compare/...@sage/xtrem-cop-test@21.0.36) (2022-07-28)

### Bug Fixes


### Features


# [21.0.35](https://github.com/compare/...@sage/xtrem-cop-test@21.0.35) (2022-07-27)

### Bug Fixes


### Features


# [21.0.34](https://github.com/compare/...@sage/xtrem-cop-test@21.0.34) (2022-07-26)

### Bug Fixes


### Features


# [21.0.33](https://github.com/compare/...@sage/xtrem-cop-test@21.0.33) (2022-07-25)

### Bug Fixes


### Features


# [21.0.32](https://github.com/compare/...@sage/xtrem-cop-test@21.0.32) (2022-07-24)

### Bug Fixes


### Features


# [21.0.31](https://github.com/compare/...@sage/xtrem-cop-test@21.0.31) (2022-07-23)

### Bug Fixes


### Features


# [21.0.30](https://github.com/compare/...@sage/xtrem-cop-test@21.0.30) (2022-07-22)

### Bug Fixes


### Features


# [21.0.29](https://github.com/compare/...@sage/xtrem-cop-test@21.0.29) (2022-07-21)

### Bug Fixes


### Features


# [21.0.28](https://github.com/compare/...@sage/xtrem-cop-test@21.0.28) (2022-07-20)

### Bug Fixes


### Features


# [21.0.27](https://github.com/compare/...@sage/xtrem-cop-test@21.0.27) (2022-07-19)

### Bug Fixes


### Features


# [21.0.26](https://github.com/compare/...@sage/xtrem-cop-test@21.0.26) (2022-07-19)

### Bug Fixes


### Features


# [21.0.25](https://github.com/compare/...@sage/xtrem-cop-test@21.0.25) (2022-07-18)

### Bug Fixes


### Features


# [21.0.24](https://github.com/compare/...@sage/xtrem-cop-test@21.0.24) (2022-07-17)

### Bug Fixes


### Features


# [21.0.23](https://github.com/compare/...@sage/xtrem-cop-test@21.0.23) (2022-07-16)

### Bug Fixes


### Features


# [21.0.22](https://github.com/compare/...@sage/xtrem-cop-test@21.0.22) (2022-07-15)

### Bug Fixes


### Features


# [21.0.21](https://github.com/compare/...@sage/xtrem-cop-test@21.0.21) (2022-07-14)

### Bug Fixes


### Features


# [21.0.20](https://github.com/compare/...@sage/xtrem-cop-test@21.0.20) (2022-07-14)

### Bug Fixes


### Features


# [21.0.19](https://github.com/compare/...@sage/xtrem-cop-test@21.0.19) (2022-07-13)

### Bug Fixes


### Features


# [21.0.18](https://github.com/compare/...@sage/xtrem-cop-test@21.0.18) (2022-07-13)

### Bug Fixes


### Features


# [21.0.17](https://github.com/compare/...@sage/xtrem-cop-test@21.0.17) (2022-07-10)

### Bug Fixes


### Features


# [21.0.16](https://github.com/compare/...@sage/xtrem-cop-test@21.0.16) (2022-07-09)

### Bug Fixes


### Features


# [21.0.15](https://github.com/compare/...@sage/xtrem-cop-test@21.0.15) (2022-07-08)

### Bug Fixes


### Features


# [21.0.14](https://github.com/compare/...@sage/xtrem-cop-test@21.0.14) (2022-07-07)

### Bug Fixes


### Features


# [21.0.13](https://github.com/compare/...@sage/xtrem-cop-test@21.0.13) (2022-07-06)

### Bug Fixes


### Features


# [21.0.12](https://github.com/compare/...@sage/xtrem-cop-test@21.0.12) (2022-07-05)

### Bug Fixes


### Features


# [21.0.11](https://github.com/compare/...@sage/xtrem-cop-test@21.0.11) (2022-07-04)

### Bug Fixes


### Features


# [21.0.10](https://github.com/compare/...@sage/xtrem-cop-test@21.0.10) (2022-07-04)

### Bug Fixes


### Features


# [21.0.9](https://github.com/compare/...@sage/xtrem-cop-test@21.0.9) (2022-07-02)

### Bug Fixes


### Features


# [21.0.8](https://github.com/compare/...@sage/xtrem-cop-test@21.0.8) (2022-07-01)

### Bug Fixes


### Features


# [21.0.7](https://github.com/compare/...@sage/xtrem-cop-test@21.0.7) (2022-06-30)

### Bug Fixes


### Features


# [21.0.6](https://github.com/compare/...@sage/xtrem-cop-test@21.0.6) (2022-06-29)

### Bug Fixes


### Features


# [21.0.5](https://github.com/compare/...@sage/xtrem-cop-test@21.0.5) (2022-06-28)

### Bug Fixes


### Features


# [21.0.4](https://github.com/compare/...@sage/xtrem-cop-test@21.0.4) (2022-06-27)

### Bug Fixes


### Features


# [21.0.3](https://github.com/compare/...@sage/xtrem-cop-test@21.0.3) (2022-06-26)

### Bug Fixes


### Features


# [21.0.2](https://github.com/compare/...@sage/xtrem-cop-test@21.0.2) (2022-06-25)

### Bug Fixes


### Features


# [21.0.1](https://github.com/compare/...@sage/xtrem-cop-test@21.0.1) (2022-06-24)

### Bug Fixes


### Features


# [21.0.0](https://github.com/compare/...@sage/xtrem-cop-test@21.0.0) (2022-06-23)

### Bug Fixes


### Features


# [20.0.32](https://github.com/compare/...@sage/xtrem-cop-test@20.0.32) (2022-06-23)

### Bug Fixes


### Features


# [20.0.31](https://github.com/compare/...@sage/xtrem-cop-test@20.0.31) (2022-06-22)

### Bug Fixes


### Features


# [20.0.30](https://github.com/compare/...@sage/xtrem-cop-test@20.0.30) (2022-06-21)

### Bug Fixes


### Features


# [20.0.29](https://github.com/compare/...@sage/xtrem-cop-test@20.0.29) (2022-06-21)

### Bug Fixes


### Features


# [20.0.28](https://github.com/compare/...@sage/xtrem-cop-test@20.0.28) (2022-06-20)

### Bug Fixes


### Features


# [20.0.27](https://github.com/compare/...@sage/xtrem-cop-test@20.0.27) (2022-06-19)

### Bug Fixes


### Features


# [20.0.26](https://github.com/compare/...@sage/xtrem-cop-test@20.0.26) (2022-06-18)

### Bug Fixes


### Features


# [20.0.25](https://github.com/compare/...@sage/xtrem-cop-test@20.0.25) (2022-06-17)

### Bug Fixes


### Features


# [20.0.24](https://github.com/compare/...@sage/xtrem-cop-test@20.0.24) (2022-06-16)

### Bug Fixes


### Features


# [20.0.23](https://github.com/compare/...@sage/xtrem-cop-test@20.0.23) (2022-06-16)

### Bug Fixes


### Features


# [20.0.22](https://github.com/compare/...@sage/xtrem-cop-test@20.0.22) (2022-06-14)

### Bug Fixes


### Features


# [20.0.21](https://github.com/compare/...@sage/xtrem-cop-test@20.0.21) (2022-06-14)

### Bug Fixes


### Features


# [20.0.20](https://github.com/compare/...@sage/xtrem-cop-test@20.0.20) (2022-06-13)

### Bug Fixes


### Features


# [20.0.19](https://github.com/compare/...@sage/xtrem-cop-test@20.0.19) (2022-06-12)

### Bug Fixes


### Features


# [20.0.18](https://github.com/compare/...@sage/xtrem-cop-test@20.0.18) (2022-06-11)

### Bug Fixes


### Features


# [20.0.17](https://github.com/compare/...@sage/xtrem-cop-test@20.0.17) (2022-06-10)

### Bug Fixes


### Features


# [20.0.16](https://github.com/compare/...@sage/xtrem-cop-test@20.0.16) (2022-06-10)

### Bug Fixes


### Features


# [20.0.15](https://github.com/compare/...@sage/xtrem-cop-test@20.0.15) (2022-06-08)

### Bug Fixes


### Features


# [20.0.14](https://github.com/compare/...@sage/xtrem-cop-test@20.0.14) (2022-06-07)

### Bug Fixes


### Features


# [20.0.13](https://github.com/compare/...@sage/xtrem-cop-test@20.0.13) (2022-06-06)

### Bug Fixes


### Features


# [20.0.12](https://github.com/compare/...@sage/xtrem-cop-test@20.0.12) (2022-06-05)

### Bug Fixes


### Features


# [20.0.11](https://github.com/compare/...@sage/xtrem-cop-test@20.0.11) (2022-06-04)

### Bug Fixes


### Features


# [20.0.10](https://github.com/compare/...@sage/xtrem-cop-test@20.0.10) (2022-06-03)

### Bug Fixes


### Features


# [20.0.9](https://github.com/compare/...@sage/xtrem-cop-test@20.0.9) (2022-06-03)

### Bug Fixes


### Features


# [20.0.8](https://github.com/compare/...@sage/xtrem-cop-test@20.0.8) (2022-06-02)

### Bug Fixes

* package version number fix ([#7344](https://github.com/issues/7344))  ([517e460](https://github.com/commit/517e460cde4848f3559f14bfd79a77835cd78223))

### Features


# [20.0.5](https://github.com/compare/...@sage/xtrem-cop-test@20.0.5) (2022-06-02)

### Bug Fixes


### Features


# [20.0.4](https://github.com/compare/...@sage/xtrem-cop-test@20.0.4) (2022-06-01)

### Bug Fixes


### Features


# [20.0.3](https://github.com/compare/...@sage/xtrem-cop-test@20.0.3) (2022-05-29)

### Bug Fixes


### Features


# [20.0.2](https://github.com/compare/...@sage/xtrem-cop-test@20.0.2) (2022-05-28)

### Bug Fixes


### Features


# [20.0.1](https://github.com/compare/...@sage/xtrem-cop-test@20.0.1) (2022-05-27)

### Bug Fixes


### Features


# [20.0.0](https://github.com/compare/...@sage/xtrem-cop-test@20.0.0) (2022-05-26)

### Bug Fixes


### Features


# [19.0.33](https://github.com/compare/...@sage/xtrem-cop-test@19.0.33) (2022-05-26)

### Bug Fixes


### Features


# [19.0.32](https://github.com/compare/...@sage/xtrem-cop-test@19.0.32) (2022-05-26)

### Bug Fixes


### Features


# [19.0.31](https://github.com/compare/...@sage/xtrem-cop-test@19.0.31) (2022-05-24)

### Bug Fixes


### Features


# [19.0.30](https://github.com/compare/...@sage/xtrem-cop-test@19.0.30) (2022-05-24)

### Bug Fixes


### Features


# [19.0.29](https://github.com/compare/...@sage/xtrem-cop-test@19.0.29) (2022-05-23)

### Bug Fixes


### Features


# [19.0.28](https://github.com/compare/...@sage/xtrem-cop-test@19.0.28) (2022-05-23)

### Bug Fixes


### Features


# [19.0.27](https://github.com/compare/...@sage/xtrem-cop-test@19.0.27) (2022-05-22)

### Bug Fixes


### Features


# [19.0.26](https://github.com/compare/...@sage/xtrem-cop-test@19.0.26) (2022-05-21)

### Bug Fixes


### Features


# [19.0.25](https://github.com/compare/...@sage/xtrem-cop-test@19.0.25) (2022-05-20)

### Bug Fixes


### Features


# [19.0.24](https://github.com/compare/...@sage/xtrem-cop-test@19.0.24) (2022-05-20)

### Bug Fixes


### Features


# [19.0.23](https://github.com/compare/...@sage/xtrem-cop-test@19.0.23) (2022-05-18)

### Bug Fixes


### Features


# [19.0.22](https://github.com/compare/...@sage/xtrem-cop-test@19.0.22) (2022-05-17)

### Bug Fixes


### Features


# [19.0.21](https://github.com/compare/...@sage/xtrem-cop-test@19.0.21) (2022-05-16)

### Bug Fixes


### Features


# [19.0.20](https://github.com/compare/...@sage/xtrem-cop-test@19.0.20) (2022-05-15)

### Bug Fixes


### Features


# [19.0.19](https://github.com/compare/...@sage/xtrem-cop-test@19.0.19) (2022-05-14)

### Bug Fixes


### Features


# [19.0.18](https://github.com/compare/...@sage/xtrem-cop-test@19.0.18) (2022-05-13)

### Bug Fixes


### Features


# [19.0.17](https://github.com/compare/...@sage/xtrem-cop-test@19.0.17) (2022-05-13)

### Bug Fixes


### Features


# [19.0.16](https://github.com/compare/...@sage/xtrem-cop-test@19.0.16) (2022-05-12)

### Bug Fixes


### Features


# [19.0.15](https://github.com/compare/...@sage/xtrem-cop-test@19.0.15) (2022-05-11)

### Bug Fixes


### Features


# [19.0.14](https://github.com/compare/...@sage/xtrem-cop-test@19.0.14) (2022-05-10)

### Bug Fixes


### Features


# [19.0.13](https://github.com/compare/...@sage/xtrem-cop-test@19.0.13) (2022-05-10)

### Bug Fixes


### Features


# [19.0.12](https://github.com/compare/...@sage/xtrem-cop-test@19.0.12) (2022-05-09)

### Bug Fixes


### Features


# [19.0.11](https://github.com/compare/...@sage/xtrem-cop-test@19.0.11) (2022-05-08)

### Bug Fixes


### Features


# [19.0.10](https://github.com/compare/...@sage/xtrem-cop-test@19.0.10) (2022-05-07)

### Bug Fixes


### Features


# [19.0.9](https://github.com/compare/...@sage/xtrem-cop-test@19.0.9) (2022-05-06)

### Bug Fixes


### Features


# [19.0.8](https://github.com/compare/...@sage/xtrem-cop-test@19.0.8) (2022-05-06)

### Bug Fixes


### Features


# [19.0.7](https://github.com/compare/...@sage/xtrem-cop-test@19.0.7) (2022-05-04)

### Bug Fixes


### Features


# [19.0.6](https://github.com/compare/...@sage/xtrem-cop-test@19.0.6) (2022-05-03)

### Bug Fixes


### Features


# [19.0.5](https://github.com/compare/...@sage/xtrem-cop-test@19.0.5) (2022-05-02)

### Bug Fixes


### Features


# [19.0.4](https://github.com/compare/...@sage/xtrem-cop-test@19.0.4) (2022-05-01)

### Bug Fixes


### Features


# [19.0.3](https://github.com/compare/...@sage/xtrem-cop-test@19.0.3) (2022-04-30)

### Bug Fixes


### Features


# [19.0.2](https://github.com/compare/...@sage/xtrem-cop-test@19.0.2) (2022-04-29)

### Bug Fixes


### Features


# [19.0.1](https://github.com/compare/...@sage/xtrem-cop-test@19.0.1) (2022-04-28)

### Bug Fixes


### Features


# [19.0.0](https://github.com/compare/...@sage/xtrem-cop-test@19.0.0) (2022-04-28)

### Bug Fixes


### Features


# [18.0.37](https://github.com/compare/...@sage/xtrem-cop-test@18.0.37) (2022-04-28)

### Bug Fixes


### Features


# [18.0.36](https://github.com/compare/...@sage/xtrem-cop-test@18.0.36) (2022-04-27)

### Bug Fixes


### Features


# [18.0.35](https://github.com/compare/...@sage/xtrem-cop-test@18.0.35) (2022-04-27)

### Bug Fixes


### Features


# [18.0.34](https://github.com/compare/...@sage/xtrem-cop-test@18.0.34) (2022-04-26)

### Bug Fixes


### Features


# [18.0.33](https://github.com/compare/...@sage/xtrem-cop-test@18.0.33) (2022-04-26)

### Bug Fixes


### Features


# [18.0.32](https://github.com/compare/...@sage/xtrem-cop-test@18.0.32) (2022-04-25)

### Bug Fixes


### Features

* improve perf sys pack artifact XT-19528 ([#6479](https://github.com/issues/6479))  ([c224afb](https://github.com/commit/c224afb97fc4cb20104680e6c62ad463b7de510d))

# [18.0.31](https://github.com/compare/...@sage/xtrem-cop-test@18.0.31) (2022-04-25)

### Bug Fixes


### Features


# [18.0.30](https://github.com/compare/...@sage/xtrem-cop-test@18.0.30) (2022-04-21)

### Bug Fixes


### Features


# [18.0.29](https://github.com/compare/...@sage/xtrem-cop-test@18.0.29) (2022-04-21)

### Bug Fixes


### Features


# [18.0.28](https://github.com/compare/...@sage/xtrem-cop-test@18.0.28) (2022-04-20)

### Bug Fixes


### Features


# [18.0.27](https://github.com/compare/...@sage/xtrem-cop-test@18.0.27) (2022-04-20)

### Bug Fixes


### Features


# [18.0.26](https://github.com/compare/...@sage/xtrem-cop-test@18.0.26) (2022-04-18)

### Bug Fixes


### Features


# [18.0.25](https://github.com/compare/...@sage/xtrem-cop-test@18.0.25) (2022-04-18)

### Bug Fixes


### Features


# [18.0.24](https://github.com/compare/...@sage/xtrem-cop-test@18.0.24) (2022-04-16)

### Bug Fixes


### Features


# [18.0.23](https://github.com/compare/...@sage/xtrem-cop-test@18.0.23) (2022-04-15)

### Bug Fixes


### Features


# [18.0.22](https://github.com/compare/...@sage/xtrem-cop-test@18.0.22) (2022-04-14)

### Bug Fixes


### Features


# [18.0.21](https://github.com/compare/...@sage/xtrem-cop-test@18.0.21) (2022-04-13)

### Bug Fixes


### Features


# [18.0.20](https://github.com/compare/...@sage/xtrem-cop-test@18.0.20) (2022-04-12)

### Bug Fixes


### Features


# [18.0.19](https://github.com/compare/...@sage/xtrem-cop-test@18.0.19) (2022-04-11)

### Bug Fixes


### Features


# [18.0.18](https://github.com/compare/...@sage/xtrem-cop-test@18.0.18) (2022-04-10)

### Bug Fixes


### Features


# [18.0.17](https://github.com/compare/...@sage/xtrem-cop-test@18.0.17) (2022-04-09)

### Bug Fixes


### Features


# [18.0.16](https://github.com/compare/...@sage/xtrem-cop-test@18.0.16) (2022-04-08)

### Bug Fixes


### Features


# [18.0.15](https://github.com/compare/...@sage/xtrem-cop-test@18.0.15) (2022-04-07)

### Bug Fixes


### Features


# [18.0.14](https://github.com/compare/...@sage/xtrem-cop-test@18.0.14) (2022-04-06)

### Bug Fixes


### Features


# [18.0.13](https://github.com/compare/...@sage/xtrem-cop-test@18.0.13) (2022-04-05)

### Bug Fixes


### Features


# [18.0.12](https://github.com/compare/...@sage/xtrem-cop-test@18.0.12) (2022-04-04)

### Bug Fixes


### Features


# [18.0.11](https://github.com/compare/...@sage/xtrem-cop-test@18.0.11) (2022-04-03)

### Bug Fixes


### Features


# [18.0.10](https://github.com/compare/...@sage/xtrem-cop-test@18.0.10) (2022-04-02)

### Bug Fixes


### Features


# [18.0.9](https://github.com/compare/...@sage/xtrem-cop-test@18.0.9) (2022-04-01)

### Bug Fixes


### Features


# [18.0.8](https://github.com/compare/...@sage/xtrem-cop-test@18.0.8) (2022-03-31)

### Bug Fixes


### Features


# [18.0.7](https://github.com/compare/...@sage/xtrem-cop-test@18.0.7) (2022-03-31)

### Bug Fixes


### Features


# [18.0.6](https://github.com/compare/...@sage/xtrem-cop-test@18.0.6) (2022-03-30)

### Bug Fixes


### Features


# [18.0.5](https://github.com/compare/...@sage/xtrem-cop-test@18.0.5) (2022-03-29)

### Bug Fixes


### Features


# [18.0.4](https://github.com/compare/...@sage/xtrem-cop-test@18.0.4) (2022-03-28)

### Bug Fixes


### Features


# [18.0.3](https://github.com/compare/...@sage/xtrem-cop-test@18.0.3) (2022-03-27)

### Bug Fixes


### Features


# [18.0.2](https://github.com/compare/...@sage/xtrem-cop-test@18.0.2) (2022-03-26)

### Bug Fixes


### Features


# [18.0.1](https://github.com/compare/...@sage/xtrem-cop-test@18.0.1) (2022-03-25)

### Bug Fixes


### Features


# [18.0.0](https://github.com/compare/...@sage/xtrem-cop-test@18.0.0) (2022-03-24)

### Bug Fixes


### Features


# [17.0.29](https://github.com/compare/...@sage/xtrem-cop-test@17.0.29) (2022-03-24)

### Bug Fixes


### Features


# [17.0.28](https://github.com/compare/...@sage/xtrem-cop-test@17.0.28) (2022-03-24)

### Bug Fixes


### Features


# [17.0.27](https://github.com/compare/...@sage/xtrem-cop-test@17.0.27) (2022-03-23)

### Bug Fixes


### Features


# [17.0.26](https://github.com/compare/...@sage/xtrem-cop-test@17.0.26) (2022-03-22)

### Bug Fixes


### Features


# [17.0.25](https://github.com/compare/...@sage/xtrem-cop-test@17.0.25) (2022-03-21)

### Bug Fixes


### Features


# [17.0.24](https://github.com/compare/...@sage/xtrem-cop-test@17.0.24) (2022-03-20)

### Bug Fixes


### Features


# [17.0.23](https://github.com/compare/...@sage/xtrem-cop-test@17.0.23) (2022-03-20)

### Bug Fixes


### Features


# [17.0.22](https://github.com/compare/...@sage/xtrem-cop-test@17.0.22) (2022-03-19)

### Bug Fixes


### Features


# [17.0.21](https://github.com/compare/...@sage/xtrem-cop-test@17.0.21) (2022-03-19)

### Bug Fixes


### Features


# [17.0.20](https://github.com/compare/...@sage/xtrem-cop-test@17.0.20) (2022-03-18)

### Bug Fixes


### Features


# [17.0.19](https://github.com/compare/...@sage/xtrem-cop-test@17.0.19) (2022-03-18)

### Bug Fixes


### Features


# [17.0.18](https://github.com/compare/...@sage/xtrem-cop-test@17.0.18) (2022-03-18)

### Bug Fixes


### Features


# [17.0.17](https://github.com/compare/...@sage/xtrem-cop-test@17.0.17) (2022-03-17)

### Bug Fixes


### Features


# [17.0.16](https://github.com/compare/...@sage/xtrem-cop-test@17.0.16) (2022-03-17)

### Bug Fixes

* persona undefined XT-20805 ([#5798](https://github.com/issues/5798))  ([49d8117](https://github.com/commit/49d811748fe1772800380efb4dcd0ee8c27b6d72))

### Features


# [17.0.15](https://github.com/compare/...@sage/xtrem-cop-test@17.0.15) (2022-03-13)

### Features


# [17.0.14](https://github.com/compare/...@sage/xtrem-cop-test@17.0.14) (2022-03-10)

### Features


# [17.0.13](https://github.com/compare/...@sage/xtrem-cop-test@17.0.13) (2022-03-09)

### Features


# [17.0.12](https://github.com/compare/...@sage/xtrem-cop-test@17.0.12) (2022-03-09)

### Features


# [17.0.11](https://github.com/compare/...@sage/xtrem-cop-test@17.0.11) (2022-03-08)

### Features


# [17.0.10](https://github.com/compare/...@sage/xtrem-cop-test@17.0.10) (2022-03-07)

### Features


# [17.0.9](https://github.com/compare/...@sage/xtrem-cop-test@17.0.9) (2022-03-06)

### Features


# [17.0.8](https://github.com/compare/...@sage/xtrem-cop-test@17.0.8) (2022-03-05)

### Features


# [17.0.7](https://github.com/compare/...@sage/xtrem-cop-test@17.0.7) (2022-03-04)

### Features


# [17.0.6](https://github.com/compare/...@sage/xtrem-cop-test@17.0.6) (2022-03-03)

### Features


# [17.0.5](https://github.com/compare/...@sage/xtrem-cop-test@17.0.5) (2022-03-03)

### Features


# [17.0.4](https://github.com/compare/...@sage/xtrem-cop-test@17.0.4) (2022-03-01)

### Features


# [17.0.3](https://github.com/compare/...@sage/xtrem-cop-test@17.0.3) (2022-02-28)

### Features


# [17.0.2](https://github.com/compare/...@sage/xtrem-cop-test@17.0.2) (2022-02-27)

### Features


# [17.0.1](https://github.com/compare/...@sage/xtrem-cop-test@17.0.1) (2022-02-26)

### Features


# [17.0.0](https://github.com/compare/...@sage/xtrem-cop-test@17.0.0) (2022-02-25)

### Features


# [16.0.29](https://github.com/compare/...@sage/xtrem-cop-test@16.0.29) (2022-02-24)

### Features


# [16.0.28](https://github.com/compare/...@sage/xtrem-cop-test@16.0.28) (2022-02-24)

### Features


# [16.0.27](https://github.com/compare/...@sage/xtrem-cop-test@16.0.27) (2022-02-24)

### Features


# [16.0.26](https://github.com/compare/...@sage/xtrem-cop-test@16.0.26) (2022-02-23)

### Features


# [16.0.25](https://github.com/compare/...@sage/xtrem-cop-test@16.0.25) (2022-02-22)

### Features


# [16.0.24](https://github.com/compare/...@sage/xtrem-cop-test@16.0.24) (2022-02-21)

### Features


# [16.0.23](https://github.com/compare/...@sage/xtrem-cop-test@16.0.23) (2022-02-20)

### Features


# [16.0.22](https://github.com/compare/...@sage/xtrem-cop-test@16.0.22) (2022-02-19)

### Features


# [16.0.21](https://github.com/compare/...@sage/xtrem-cop-test@16.0.21) (2022-02-18)

### Features


# [16.0.20](https://github.com/compare/...@sage/xtrem-cop-test@16.0.20) (2022-02-17)

### Features


# [16.0.19](https://github.com/compare/...@sage/xtrem-cop-test@16.0.19) (2022-02-16)

### Features


# [16.0.18](https://github.com/compare/...@sage/xtrem-cop-test@16.0.18) (2022-02-15)

### Features


# [16.0.17](https://github.com/compare/...@sage/xtrem-cop-test@16.0.17) (2022-02-13)

### Features


# [16.0.16](https://github.com/compare/...@sage/xtrem-cop-test@16.0.16) (2022-02-12)

### Features


# [16.0.15](https://github.com/compare/...@sage/xtrem-cop-test@16.0.15) (2022-02-11)

### Features


# [16.0.14](https://github.com/compare/...@sage/xtrem-cop-test@16.0.14) (2022-02-10)

### Features


# [16.0.13](https://github.com/compare/...@sage/xtrem-cop-test@16.0.13) (2022-02-10)

### Features


# [16.0.12](https://github.com/compare/...@sage/xtrem-cop-test@16.0.12) (2022-02-08)

### Features


# [16.0.11](https://github.com/compare/...@sage/xtrem-cop-test@16.0.11) (2022-02-07)

### Features


# [16.0.10](https://github.com/compare/...@sage/xtrem-cop-test@16.0.10) (2022-02-07)

### Features


# [16.0.9](https://github.com/compare/...@sage/xtrem-cop-test@16.0.9) (2022-02-06)

### Features


# [16.0.8](https://github.com/compare/...@sage/xtrem-cop-test@16.0.8) (2022-02-05)

### Features


# [16.0.7](https://github.com/compare/...@sage/xtrem-cop-test@16.0.7) (2022-02-04)

### Features


# [16.0.6](https://github.com/compare/...@sage/xtrem-cop-test@16.0.6) (2022-02-04)

### Features


# [16.0.5](https://github.com/compare/...@sage/xtrem-cop-test@16.0.5) (2022-02-02)

### Features


# [16.0.4](https://github.com/compare/...@sage/xtrem-cop-test@16.0.4) (2022-02-01)

### Features


# [16.0.3](https://github.com/compare/...@sage/xtrem-cop-test@16.0.3) (2022-01-31)

### Features


# [16.0.2](https://github.com/compare/...@sage/xtrem-cop-test@16.0.2) (2022-01-30)

### Features


# [16.0.1](https://github.com/compare/...@sage/xtrem-cop-test@16.0.1) (2022-01-29)

### Features


# [16.0.0](https://github.com/compare/...@sage/xtrem-cop-test@16.0.0) (2022-01-29)

### Features


# [15.0.36](https://github.com/compare/...@sage/xtrem-cop-test@15.0.36) (2022-01-28)

### Features


# [15.0.35](https://github.com/compare/...@sage/xtrem-cop-test@15.0.35) (2022-01-28)

### Features


# [15.0.34](https://github.com/compare/...@sage/xtrem-cop-test@15.0.34) (2022-01-28)

### Features


# [15.0.33](https://github.com/compare/...@sage/xtrem-cop-test@15.0.33) (2022-01-26)

### Features


# [15.0.32](https://github.com/compare/...@sage/xtrem-cop-test@15.0.32) (2022-01-26)

### Features


# [15.0.31](https://github.com/compare/...@sage/xtrem-cop-test@15.0.31) (2022-01-25)

### Features


# [15.0.30](https://github.com/compare/...@sage/xtrem-cop-test@15.0.30) (2022-01-25)

### Features


# [15.0.29](https://github.com/compare/...@sage/xtrem-cop-test@15.0.29) (2022-01-24)

### Features


# [15.0.28](https://github.com/compare/...@sage/xtrem-cop-test@15.0.28) (2022-01-24)

### Features


# [15.0.27](https://github.com/compare/...@sage/xtrem-cop-test@15.0.27) (2022-01-23)

### Features


# [15.0.26](https://github.com/compare/...@sage/xtrem-cop-test@15.0.26) (2022-01-23)

### Features


# [15.0.25](https://github.com/compare/...@sage/xtrem-cop-test@15.0.25) (2022-01-21)

### Features

* **demo:** create admin demo persona XT-11483 ([#4782](https://github.com/issues/4782))  ([078633f](https://github.com/commit/078633fafb90c5eb3492d908ad16affd5f88fd8a))
* **demo:** get demo personas and set transaction user (XT-11483) ([#4689](https://github.com/issues/4689))  ([c666ef1](https://github.com/commit/c666ef1e8ead4a8423dd82b091be3de34aafe506))

# [15.0.24](https://github.com/compare/...@sage/xtrem-cop-test@15.0.24) (2022-01-18)

### Features


# [15.0.23](https://github.com/compare/...@sage/xtrem-cop-test@15.0.23) (2022-01-18)

### Features


# [15.0.22](https://github.com/compare/...@sage/xtrem-cop-test@15.0.22) (2022-01-17)

### Features

* **demo:** add login user and persona (XT-11483) ([#4646](https://github.com/issues/4646))  ([8fc8bd2](https://github.com/commit/8fc8bd2081e234efd0842ed520816e7fca52e237))

# [15.0.21](https://github.com/compare/...@sage/xtrem-cop-test@15.0.21) (2022-01-16)

### Features


# [15.0.20](https://github.com/compare/...@sage/xtrem-cop-test@15.0.20) (2022-01-15)

### Features


# [15.0.19](https://github.com/compare/...@sage/xtrem-cop-test@15.0.19) (2022-01-14)

### Features


# [15.0.18](https://github.com/compare/...@sage/xtrem-cop-test@15.0.18) (2022-01-14)

### Features


# [15.0.17](https://github.com/compare/...@sage/xtrem-cop-test@15.0.17) (2022-01-13)

### Features


# [15.0.16](https://github.com/compare/...@sage/xtrem-cop-test@15.0.16) (2022-01-12)

### Features


# [15.0.15](https://github.com/compare/...@sage/xtrem-cop-test@15.0.15) (2022-01-11)

### Features


# [15.0.14](https://github.com/compare/...@sage/xtrem-cop-test@15.0.14) (2022-01-11)

### Features


# [15.0.13](https://github.com/compare/...@sage/xtrem-cop-test@15.0.13) (2022-01-11)

### Features


# [15.0.12](https://github.com/compare/...@sage/xtrem-cop-test@15.0.12) (2022-01-10)

### Features


# [15.0.11](https://github.com/compare/...@sage/xtrem-cop-test@15.0.11) (2022-01-09)

### Features


# [15.0.10](https://github.com/compare/...@sage/xtrem-cop-test@15.0.10) (2022-01-08)

### Features


# [15.0.9](https://github.com/compare/...@sage/xtrem-cop-test@15.0.9) (2022-01-07)

### Features


# [15.0.8](https://github.com/compare/...@sage/xtrem-cop-test@15.0.8) (2022-01-07)

### Features


# [15.0.7](https://github.com/compare/...@sage/xtrem-cop-test@15.0.7) (2022-01-06)

### Features


# [15.0.6](https://github.com/compare/...@sage/xtrem-cop-test@15.0.6) (2022-01-05)

### Features


# [15.0.5](https://github.com/compare/...@sage/xtrem-cop-test@15.0.5) (2022-01-04)

### Features


# [15.0.4](https://github.com/compare/...@sage/xtrem-cop-test@15.0.4) (2022-01-03)

### Features


# [15.0.3](https://github.com/compare/...@sage/xtrem-cop-test@15.0.3) (2022-01-02)

### Features


# [15.0.2](https://github.com/compare/...@sage/xtrem-cop-test@15.0.2) (2022-01-01)

### Features


# [15.0.1](https://github.com/compare/...@sage/xtrem-cop-test@15.0.1) (2021-12-31)

### Features


# [15.0.0](https://github.com/compare/...@sage/xtrem-cop-test@15.0.0) (2021-12-30)

### Features


# [14.0.29](https://github.com/compare/...@sage/xtrem-cop-test@14.0.29) (2021-12-30)

### Features


# [14.0.28](https://github.com/compare/...@sage/xtrem-cop-test@14.0.28) (2021-12-29)

### Features


# [14.0.27](https://github.com/compare/...@sage/xtrem-cop-test@14.0.27) (2021-12-28)

### Features


# [14.0.26](https://github.com/compare/...@sage/xtrem-cop-test@14.0.26) (2021-12-27)

### Features


# [14.0.25](https://github.com/compare/...@sage/xtrem-cop-test@14.0.25) (2021-12-27)

### Features


# [14.0.24](https://github.com/compare/...@sage/xtrem-cop-test@14.0.24) (2021-12-22)

### Features


# [14.0.23](https://github.com/compare/...@sage/xtrem-cop-test@14.0.23) (2021-12-21)

### Features

* **core:** XT-13954 replace a dependency on non vital reference's pr… ([#4270](https://github.com/issues/4270))  ([8bf716c](https://github.com/commit/8bf716c6c0119c149ef88487476be181b113f77c))

# [14.0.22](https://github.com/compare/...@sage/xtrem-cop-test@14.0.22) (2021-12-15)

### Features


# [14.0.21](https://github.com/compare/...@sage/xtrem-cop-test@14.0.21) (2021-12-14)

### Features


# [14.0.20](https://github.com/compare/...@sage/xtrem-cop-test@14.0.20) (2021-12-13)

### Features


# [14.0.19](https://github.com/compare/...@sage/xtrem-cop-test@14.0.19) (2021-12-12)

### Features


# [14.0.18](https://github.com/compare/...@sage/xtrem-cop-test@14.0.18) (2021-12-11)

### Features


# [14.0.17](https://github.com/compare/...@sage/xtrem-cop-test@14.0.17) (2021-12-10)

### Features


# [14.0.16](https://github.com/compare/...@sage/xtrem-cop-test@14.0.16) (2021-12-09)

### Features


# [14.0.15](https://github.com/compare/...@sage/xtrem-cop-test@14.0.15) (2021-12-09)

### Features

* XT-14045 refactor metadata requests aligning X3 page access rights logic to that of the framework ([#4112](https://github.com/issues/4112))  ([1495506](https://github.com/commit/149550671d3aa7cc7b514343e51aadf02a7fb83c))
* **verifyDependsOn:** XT-13954 Fixes explorer ([#4104](https://github.com/issues/4104))  ([a1616f6](https://github.com/commit/a1616f6f702ad3779b1c5f5bdf87586a17aeab1c))

# [14.0.14](https://github.com/compare/...@sage/xtrem-cop-test@14.0.14) (2021-12-08)

### Features


# [14.0.13](https://github.com/compare/...@sage/xtrem-cop-test@14.0.13) (2021-12-07)

### Features


# [14.0.12](https://github.com/compare/...@sage/xtrem-cop-test@14.0.12) (2021-12-07)

### Features

* XT-780 refactor auth operations ([#4071](https://github.com/issues/4071))  ([1e3f7a7](https://github.com/commit/1e3f7a79e318785190b7b350fa7c56f29ed5dc6f))
* **verifyDependsOn:** XT-13954 improvements ([#4054](https://github.com/issues/4054))  ([d370502](https://github.com/commit/d370502d352a96f1aae174e46799b43095ee4e73))

# [14.0.11](https://github.com/compare/...@sage/xtrem-cop-test@14.0.11) (2021-12-06)

### Features

* **auth:** metadata query (XT-12098) ([#4072](https://github.com/issues/4072))  ([8fb5c41](https://github.com/commit/8fb5c415410afdc01b89dbb42788ae7c1b83ad84))

# [14.0.10](https://github.com/compare/...@sage/xtrem-cop-test@14.0.10) (2021-12-05)

### Features


# [14.0.9](https://github.com/compare/...@sage/xtrem-cop-test@14.0.9) (2021-12-04)

### Features


# [14.0.8](https://github.com/compare/...@sage/xtrem-cop-test@14.0.8) (2021-12-03)

### Features

* **basalt:** XT-780 Refactor getUserAccessFor ([#3968](https://github.com/issues/3968))  ([2a50095](https://github.com/commit/2a500952e3654711187d3959cd9b3b8f1e697a29))

# [14.0.7](https://github.com/compare/...@sage/xtrem-cop-test@14.0.7) (2021-12-03)

### Features


# [14.0.6](https://github.com/compare/...@sage/xtrem-cop-test@14.0.6) (2021-12-02)

### Features


# [14.0.5](https://github.com/compare/...@sage/xtrem-cop-test@14.0.5) (2021-12-02)

### Features


# [14.0.4](https://github.com/compare/...@sage/xtrem-cop-test@14.0.4) (2021-12-02)

### Features


# [14.0.3](https://github.com/compare/...@sage/xtrem-cop-test@14.0.3) (2021-12-02)

### Features


# [14.0.2](https://github.com/compare/...@sage/xtrem-cop-test@14.0.2) (2021-12-01)

### Features


# [14.0.1](https://github.com/compare/...@sage/xtrem-cop-test@14.0.1) (2021-11-29)

### Features


# [14.0.0](https://github.com/compare/...@sage/xtrem-cop-test@14.0.0) (2021-11-29)

### Features

* **verifyDependsOn:** XT-13954 depends on inconsistencies ([#3939](https://github.com/issues/3939))  ([1fdee82](https://github.com/commit/1fdee82a8c1fee128a3a411dbd24ff728df8028f))

# [13.0.28](https://github.com/compare/...@sage/xtrem-cop-test@13.0.28) (2021-11-29)

### Features


# [13.0.27](https://github.com/compare/...@sage/xtrem-cop-test@13.0.27) (2021-11-28)

### Features


# [13.0.26](https://github.com/compare/...@sage/xtrem-cop-test@13.0.26) (2021-11-27)

### Features


# [13.0.25](https://github.com/compare/...@sage/xtrem-cop-test@13.0.25) (2021-11-26)

### Features

* **basalt:** XT-780 Refactor auth get allowed codes ([#3744](https://github.com/issues/3744))  ([88e84d1](https://github.com/commit/88e84d1f9b25b7610d23b6de00e75de25302ab97))

# [13.0.24](https://github.com/compare/...@sage/xtrem-cop-test@13.0.24) (2021-11-25)

### Features


# [13.0.23](https://github.com/compare/...@sage/xtrem-cop-test@13.0.23) (2021-11-24)

### Features


# [13.0.22](https://github.com/compare/...@sage/xtrem-cop-test@13.0.22) (2021-11-23)

### Features

* **verifyDependsOn:** XT-13954 depends on warnings ([#3815](https://github.com/issues/3815))  ([eee7cff](https://github.com/commit/eee7cffc727634f119987f384600e90ef51024ff))

# [13.0.21](https://github.com/compare/...@sage/xtrem-cop-test@13.0.21) (2021-11-22)

### Features


# [13.0.20](https://github.com/compare/...@sage/xtrem-cop-test@13.0.20) (2021-11-22)

### Features


# [13.0.19](https://github.com/compare/...@sage/xtrem-cop-test@13.0.19) (2021-11-19)

### Features


# [13.0.18](https://github.com/compare/...@sage/xtrem-cop-test@13.0.18) (2021-11-18)

### Features


# [13.0.17](https://github.com/compare/...@sage/xtrem-cop-test@13.0.17) (2021-11-18)

### Features


# [13.0.16](https://github.com/compare/...@sage/xtrem-cop-test@13.0.16) (2021-11-17)

### Features


# [13.0.15](https://github.com/compare/...@sage/xtrem-cop-test@13.0.15) (2021-11-17)

### Features


# [13.0.14](https://github.com/compare/...@sage/xtrem-cop-test@13.0.14) (2021-11-14)

### Features


# [13.0.13](https://github.com/compare/...@sage/xtrem-cop-test@13.0.13) (2021-11-13)

### Features


# [13.0.12](https://github.com/compare/...@sage/xtrem-cop-test@13.0.12) (2021-11-12)

### Features


# [13.0.11](https://github.com/compare/...@sage/xtrem-cop-test@13.0.11) (2021-11-11)

### Features


# [13.0.10](https://github.com/compare/...@sage/xtrem-cop-test@13.0.10) (2021-11-10)

### Features


# [13.0.9](https://github.com/compare/...@sage/xtrem-cop-test@13.0.9) (2021-11-09)

### Features


# [13.0.8](https://github.com/compare/...@sage/xtrem-cop-test@13.0.8) (2021-11-09)

### Features


# [13.0.7](https://github.com/compare/...@sage/xtrem-cop-test@13.0.7) (2021-11-09)

### Features


# [13.0.6](https://github.com/compare/...@sage/xtrem-cop-test@13.0.6) (2021-11-08)

### Features


# [13.0.5](https://github.com/compare/...@sage/xtrem-cop-test@13.0.5) (2021-11-07)

### Features


# [13.0.4](https://github.com/compare/...@sage/xtrem-cop-test@13.0.4) (2021-11-06)

### Features


# [13.0.3](https://github.com/compare/...@sage/xtrem-cop-test@13.0.3) (2021-11-05)

### Features


# [13.0.2](https://github.com/compare/...@sage/xtrem-cop-test@13.0.2) (2021-11-04)

### Features


# [13.0.1](https://github.com/compare/...@sage/xtrem-cop-test@13.0.1) (2021-11-03)

### Features


# [13.0.0](https://github.com/compare/...@sage/xtrem-cop-test@13.0.0) (2021-11-03)

### Features


# [12.0.35](https://github.com/compare/...@sage/xtrem-cop-test@12.0.35) (2021-11-02)

### Features


# [12.0.34](https://github.com/compare/...@sage/xtrem-cop-test@12.0.34) (2021-10-31)

### Features


# [12.0.33](https://github.com/compare/...@sage/xtrem-cop-test@12.0.33) (2021-10-30)

### Features


# [12.0.32](https://github.com/compare/...@sage/xtrem-cop-test@12.0.32) (2021-10-29)

### Features


# [12.0.31](https://github.com/compare/...@sage/xtrem-cop-test@12.0.31) (2021-10-28)

### Features


# [12.0.30](https://github.com/compare/...@sage/xtrem-cop-test@12.0.30) (2021-10-28)

### Features


# [12.0.29](https://github.com/compare/...@sage/xtrem-cop-test@12.0.29) (2021-10-26)

### Features


# [12.0.28](https://github.com/compare/...@sage/xtrem-cop-test@12.0.28) (2021-10-26)

### Features


# [12.0.27](https://github.com/compare/...@sage/xtrem-cop-test@12.0.27) (2021-10-25)

### Features


# [12.0.26](https://github.com/compare/...@sage/xtrem-cop-test@12.0.26) (2021-10-25)

### Features


# [12.0.25](https://github.com/compare/...@sage/xtrem-cop-test@12.0.25) (2021-10-24)

### Features


# [12.0.24](https://github.com/compare/...@sage/xtrem-cop-test@12.0.24) (2021-10-24)

### Features


# [12.0.23](https://github.com/compare/...@sage/xtrem-cop-test@12.0.23) (2021-10-23)

### Features


# [12.0.22](https://github.com/compare/...@sage/xtrem-cop-test@12.0.22) (2021-10-22)

### Features


# [12.0.21](https://github.com/compare/...@sage/xtrem-cop-test@12.0.21) (2021-10-22)

### Features

* XT-12439 remove sys layer node ([#3276](https://github.com/issues/3276))  ([9218f6a](https://github.com/commit/9218f6a83a4113d313d44474d0be69a72ac60f99))

# [12.0.20](https://github.com/compare/...@sage/xtrem-cop-test@12.0.20) (2021-10-20)

### Features


# [12.0.19](https://github.com/compare/...@sage/xtrem-cop-test@12.0.19) (2021-10-19)

### Features


# [12.0.18](https://github.com/compare/...@sage/xtrem-cop-test@12.0.18) (2021-10-18)

### Features


# [12.0.17](https://github.com/compare/...@sage/xtrem-cop-test@12.0.17) (2021-10-17)

### Features


# [12.0.16](https://github.com/compare/...@sage/xtrem-cop-test@12.0.16) (2021-10-17)

### Features


# [12.0.15](https://github.com/compare/...@sage/xtrem-cop-test@12.0.15) (2021-10-16)

### Features


# [12.0.14](https://github.com/compare/...@sage/xtrem-cop-test@12.0.14) (2021-10-15)

### Features


# [12.0.13](https://github.com/compare/...@sage/xtrem-cop-test@12.0.13) (2021-10-14)

### Features


# [12.0.12](https://github.com/compare/...@sage/xtrem-cop-test@12.0.12) (2021-10-13)

### Features


# [12.0.11](https://github.com/compare/...@sage/xtrem-cop-test@12.0.11) (2021-10-12)

### Features


# [12.0.10](https://github.com/compare/...@sage/xtrem-cop-test@12.0.10) (2021-10-12)

### Features


# [12.0.9](https://github.com/compare/...@sage/xtrem-cop-test@12.0.9) (2021-10-10)

### Features


# [12.0.8](https://github.com/compare/...@sage/xtrem-cop-test@12.0.8) (2021-10-09)

### Features


# [12.0.7](https://github.com/compare/...@sage/xtrem-cop-test@12.0.7) (2021-10-08)

### Features


# [12.0.6](https://github.com/compare/...@sage/xtrem-cop-test@12.0.6) (2021-10-08)

### Features

* sys vendor (XT-9507) ([#3008](https://github.com/issues/3008))  ([048bcde](https://github.com/commit/048bcde976908dafa4893680d757e13cfe4ca17d))

# [12.0.5](https://github.com/compare/...@sage/xtrem-cop-test@12.0.5) (2021-10-07)

### Features


# [12.0.4](https://github.com/compare/...@sage/xtrem-cop-test@12.0.4) (2021-10-06)

### Features


# [12.0.3](https://github.com/compare/...@sage/xtrem-cop-test@12.0.3) (2021-10-05)

### Features

* **setup-data:** Add Sys layer node XT-10475 ([#2823](https://github.com/issues/2823))  ([5dd1ac1](https://github.com/commit/5dd1ac1258e45d0567fc867f3dae5cac5ed4206b))
* **xtrem-cop:** XT-4930 Implemented xtrem-cop package ([#2425](https://github.com/issues/2425))  ([62ff5e6](https://github.com/commit/62ff5e6ab15e46c3e7ac5788d0fd073199fcd63f))

