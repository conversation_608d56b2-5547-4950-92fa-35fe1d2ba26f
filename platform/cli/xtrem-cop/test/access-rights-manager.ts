/* eslint-disable @typescript-eslint/no-unused-vars */
import {
    AccessRightsManager,
    ActivityInfo,
    adminDemoPersona,
    AnyFilterObject,
    AsyncResponse,
    Context,
    CreateAdminUserOptions,
    getTestFixtures,
    NodeFactory,
    TestUser,
    UserAccess,
    UserInfo,
} from '@sage/xtrem-core';

const fixtures = getTestFixtures();
const { TestActivity } = fixtures.nodes;

const mockUser = { _id: 1000000, email: '<EMAIL>' };

export const copAccessRightsManager: AccessRightsManager = {
    getUserAccessFor(
        context: Context,
        nodeName: string,
        propertyOrOperation: string,
        options?: { authorizationCode?: string },
    ): UserAccess {
        return { sites: null, accessCodes: null, status: 'authorized' };
    },

    isAccessCodeAvailable(context: Context, accessCode: string): boolean {
        return true;
    },

    getOperationSecurityFilter(
        context: Context,
        factory: NodeFactory,
        operation: string,
    ): Promise<AnyFilterObject | undefined> {
        return Promise.resolve(undefined);
    },

    createAdminUser(context: Context, data: any, options?: CreateAdminUserOptions): void {},

    createRequiredUsers(context: Context) {},

    ensureAdminPersonaCreated(context: Context): UserInfo {
        return adminDemoPersona;
    },

    getUser(context: Context, email: string): UserInfo {
        return mockUser;
    },

    getCurrentUser(context: Context): UserInfo {
        return mockUser;
    },

    getUserNode() {
        return TestUser;
    },

    // stub implementation for now
    getPermissions(context: Context, activity: string): string[] {
        return ['create', 'read', 'update', 'delete', 'lookup'];
    },

    createActivities(context: Context): void {},

    updateActivities(context: Context): void {},

    deleteActivities(context: Context): void {},

    getActivityNode() {
        return TestActivity;
    },

    getActivitiesInfo(context: Context): ActivityInfo[] {
        return [];
    },

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    supportsPersona(context: Context): boolean {
        return false;
    },

    getPersonaUser(context: Context, email: string): UserInfo | null {
        return null;
    },

    getDemoPersonas(context: Context): UserInfo[] {
        return [];
    },

    invalidateAuthorizationCache(context: Context): Promise<void> {
        return context.invalidateCachedCategory('AUTHORIZATION', {});
    },

    /**
     * Returns whether the 'Authorization access control' service option is enabled
     */
    // eslint-disable-next-line @typescript-eslint/no-unused-vars, class-methods-use-this
    isAuthorizationServiceOptionEnabled(context: Context): AsyncResponse<boolean> {
        return false;
    },
};
