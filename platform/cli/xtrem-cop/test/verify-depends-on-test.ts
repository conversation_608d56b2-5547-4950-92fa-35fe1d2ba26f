import { Application, Context, CoreHooks, Dict, Logger, Test, TestSysVendor, TestUser } from '@sage/xtrem-core';
import { assert, expect } from 'chai';
import * as fsp from 'path';
import * as Sinon from 'sinon';
// eslint-disable-next-line import/no-relative-packages
import { sanitizeCollectedDependencyPaths, sanitizeDependsOn } from '../index';
// eslint-disable-next-line import/no-relative-packages
import { fixDependsOn, verifyDependsOn } from '../lib';
import { copAccessRightsManager } from './access-rights-manager';
import { copDataSettingsManager } from './data-settings-manager';
import {
    TestCopWithDependsOnNode,
    TestCopWithNonVitalReference,
    TestCopWithoutDependsOnNode,
    TestReference,
    TestVitalReferenceChild,
} from './fixtures/nodes';

describe('Verify dependsOn test', () => {
    before(() => {
        // Override sysManager.getUserNode because SQL insert function needs it.
        CoreHooks.sysManager = { ...CoreHooks.sysManager, getUserNode: () => TestUser };
    });
    describe('sanitizeDependsOn function test', () => {
        it("should build path for simple dependency: [ 'costCategoryType' ]", () => {
            const sanitizeFunc = sanitizeDependsOn(['costCategoryType']);
            expect(sanitizeFunc).to.eql(['costCategoryType']);
        });

        it("should build path for simple dependency: [ 'costCategoryType', 'foo' ]", () => {
            const sanitizeFunc = sanitizeDependsOn(['costCategoryType', 'foo']);
            expect(sanitizeFunc).to.eql(['costCategoryType', 'foo']);
        });

        it("should build path for complex dependency: [{ document: [ 'itemSite' ] }]", () => {
            const sanitizeFunc = sanitizeDependsOn([{ document: ['itemSite'] }]);
            expect(sanitizeFunc).to.eql(['document.itemSite']);
        });

        it("should build path for complex dependency: [{ workOrderComponent: [{ workOrder: ['site', 'company'] }]", () => {
            const sanitizeFunc = sanitizeDependsOn([{ workOrderComponent: [{ workOrder: ['site', 'company'] }] }]);
            expect(sanitizeFunc).to.eql(['workOrderComponent.workOrder.site', 'workOrderComponent.workOrder.company']);
        });

        it("should build path for complex dependency: [{ list: ['counter'] }, 'counter', 'list']", () => {
            const sanitizeFunc = sanitizeDependsOn([{ list: ['counter'] }, 'counter', 'list']);
            expect(sanitizeFunc).to.eql(['list.counter', 'counter', 'list']);
        });

        it("should build path for complex dependency: [ 'quantity', 'price', { purchaseInvoice: ['currency'] },' currency', 'purchaseInvoice']", () => {
            const sanitizeFunc = sanitizeDependsOn([
                'quantity',
                'price',
                { purchaseInvoice: ['currency'] },
                'currency',
                'purchaseInvoice',
            ]);
            expect(sanitizeFunc).to.eql([
                'quantity',
                'price',
                'purchaseInvoice.currency',
                'currency',
                'purchaseInvoice',
            ]);
        });
    });

    describe('sanitizeCollectedDependencyPaths function test', () => {
        it("should build path for simple dependency: [ 'this.valuedCost', 'this.quantityInStockUnit' ] ", () => {
            const sanitizeFunc = sanitizeCollectedDependencyPaths(['this.valuedCost', 'this.quantityInStockUnit']);
            expect(sanitizeFunc).to.eql(['valuedCost', 'quantityInStockUnit']);
        });

        it("should build path for complex dependency:  ['this.workOrderLine','this.workOrderLine.totalActualQuantity','this.releasedQuantity','this.workOrderLine.document','this.workOrderLine.document.actualMaterialCost','this.workOrderLine.document.actualProcessCost'] ", () => {
            const sanitizeFunc = sanitizeCollectedDependencyPaths([
                'this.workOrderLine',
                'this.workOrderLine.totalActualQuantity',
                'this.releasedQuantity',
                'this.workOrderLine.document',
                'this.workOrderLine.document.actualMaterialCost',
                'this.workOrderLine.document.actualProcessCost',
            ]);
            expect(sanitizeFunc).to.eql([
                'workOrderLine',
                'workOrderLine.totalActualQuantity',
                'releasedQuantity',
                'workOrderLine.document',
                'workOrderLine.document.actualMaterialCost',
                'workOrderLine.document.actualProcessCost',
            ]);
        });
    });

    describe('Node tests', () => {
        const logger = Logger.getLogger(fsp.join(__dirname, '../lib/verify-depends-on.ts'), 'verifyDependsOn');
        let loggerSpy: sinon.SinonSpy<any>;

        Context.accessRightsManager = copAccessRightsManager;
        Context.dataSettingsManager = copDataSettingsManager;

        before(() => Application.createDbSchema('xtrem_cop_test'));

        beforeEach(() => {
            loggerSpy = Sinon.spy(logger, 'warn');
        });

        afterEach(() => {
            loggerSpy.restore();
        });

        function createApplication(nodes: Dict<any>): Promise<Application> {
            return Test.createTestApplication({
                buildDir: fsp.join(__dirname, 'package.json'),
                api: { nodes: { ...nodes, TestUser, TestSysVendor } },
                schemaName: 'xtrem_cop_test',
            });
        }

        it('should log a warning when a property has a dependency but does not have a dependsOn property attribute specified', async () => {
            Test.application = await createApplication({ TestCopWithoutDependsOnNode });
            verifyDependsOn(Test.application);

            expect(
                loggerSpy.calledWith(
                    // eslint-disable-next-line @typescript-eslint/quotes
                    `<TestCopWithoutDependsOnNode, 'propWithMissingDependsOn'> defaultValue: the dependsOn attribute should be equal to ["value"]`,
                ),
            ).to.equal(true);
            loggerSpy.resetHistory();
        });

        it('should not log a warning when a property has a dependsOn property attribute specified', async () => {
            Test.application = await createApplication({ TestCopWithDependsOnNode, TestVitalReferenceChild });
            verifyDependsOn(Test.application);

            expect(loggerSpy.getCalls()).to.be.instanceof(Array);
            expect(loggerSpy.getCalls()).to.have.lengthOf(0);
            loggerSpy.resetHistory();
        });

        it('test dependsOn with non vital references', async () => {
            Test.application = await createApplication({ TestCopWithNonVitalReference, TestReference });
            verifyDependsOn(Test.application);

            expect(loggerSpy.getCalls()).to.be.instanceof(Array);
            expect(loggerSpy.getCalls()).to.have.lengthOf(0);

            loggerSpy.resetHistory();
        });

        after(() => Application.dropDbSchema('xtrem_cop_test'));
    });

    describe('Tests warnings raised by xtrem-core/node-factory', () => {
        const logger = Logger.getLogger(fsp.join(__dirname, '../../../back-end/xtrem-core/index.ts'), 'node-factory');
        let loggerSpy: sinon.SinonSpy<any>;

        Context.accessRightsManager = copAccessRightsManager;
        Context.dataSettingsManager = copDataSettingsManager;

        before(() => Application.createDbSchema('xtrem_cop_test'));

        beforeEach(() => {
            loggerSpy = Sinon.spy(logger, 'warn');
        });

        afterEach(() => {
            loggerSpy.restore();
        });

        function createApplication(nodes: Dict<any>): Promise<Application> {
            return Test.createTestApplication({
                buildDir: fsp.join(__dirname, 'package.json'),
                api: { nodes: { ...nodes, TestUser, TestSysVendor } },
                schemaName: 'xtrem_cop_test',
            });
        }

        it('Node-factory should log a warning when a property has a dependsOn property on a non vital reference', async () => {
            Test.application = await createApplication({ TestCopWithNonVitalReference, TestReference });

            expect(
                loggerSpy.calledWith(
                    // eslint-disable-next-line @typescript-eslint/quotes
                    `TestCopWithNonVitalReference.details.dependsOn: TestCopWithNonVitalReference.reference is not part of the vital graph. The rule '{"reference":["details"]}' will be ignored.`,
                ),
            ).to.equal(true);

            loggerSpy.resetHistory();
        });

        after(() => Application.dropDbSchema('xtrem_cop_test'));
    });

    describe('Propose fixes for DependsOn rules', () => {
        it('simple rule', () => {
            assert.deepEqual(fixDependsOn([], ['item']), ['item']);
            assert.deepEqual(fixDependsOn([], ['item.stockUnit']), [{ item: ['stockUnit'] }]);
            assert.deepEqual(fixDependsOn(['item'], ['item.stockUnit']), [{ item: ['stockUnit'] }]);
            assert.deepEqual(fixDependsOn([{ item: ['stockUnit'] }], ['item.stockUnit', 'item.stockPrice']), [
                { item: ['stockUnit', 'stockPrice'] },
            ]);

            assert.deepEqual(fixDependsOn(['document'], ['document.site', 'document.site.isFinance']), [
                { document: [{ site: ['isFinance'] }] },
            ]);
        });
    });
});
