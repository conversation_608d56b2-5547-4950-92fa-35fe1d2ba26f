import { Context, DataSettingsManager, Node, StaticThis, TestSysVendor } from '@sage/xtrem-core';

/**
 * A mock for DataSettingsManager: should only be used for unit tests
 */
class DataSettingsManagerMock implements DataSettingsManager {
    // eslint-disable-next-line class-methods-use-this
    getSysVendorNode(): StaticThis<Node> {
        return TestSysVendor;
    }

    // eslint-disable-next-line @typescript-eslint/no-unused-vars, class-methods-use-this
    sageVendorId(context: Context): number {
        return 1;
    }

    // eslint-disable-next-line @typescript-eslint/no-unused-vars, class-methods-use-this
    initializeManager(_context: Context): void {}
}

export const copDataSettingsManager = new DataSettingsManagerMock();
