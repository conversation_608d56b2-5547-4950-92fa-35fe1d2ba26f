import { decorators, integer, Node, Reference, StringDataType } from '@sage/xtrem-core';
import { TestVitalReferenceChild } from '.';

function callExpression(value: integer) {
    return 2 * value;
}

@decorators.node<TestCopWithDependsOnNode>({
    storage: 'sql',
    isPublished: true,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canDeleteMany: true,
    indexes: [],
})
export class TestCopWithDependsOnNode extends Node {
    @decorators.stringProperty<TestCopWithDependsOnNode, 'value'>({
        dataType: () => new StringDataType({ maxLength: 50 }),
        isPublished: true,
        isStored: true,
        defaultValue() {
            return 'default value';
        },
    })
    readonly value: Promise<string>;

    @decorators.stringProperty<TestCopWithDependsOnNode, 'details'>({
        dataType: () => new StringDataType({ maxLength: 50 }),
        isPublished: true,
        isStored: true,
    })
    readonly details: Promise<string>;

    @decorators.stringProperty<TestCopWithDependsOnNode, 'propWithValidDependsOn'>({
        dataType: () => new StringDataType({ maxLength: 50 }),
        isPublished: true,
        isStored: true,
        dependsOn: ['value'],
        defaultValue() {
            return this.value;
        },
    })
    readonly propWithValidDependsOn: Promise<string>;

    @decorators.integerProperty<TestCopWithDependsOnNode, 'testingValue'>({
        isPublished: true,
        isStored: true,
    })
    readonly testingValue: Promise<integer>;

    @decorators.referenceProperty<TestCopWithDependsOnNode, 'vitalChild'>({
        isPublished: true,
        isVital: true,
        node: () => TestVitalReferenceChild,
        reverseReference: 'parent',
    })
    readonly vitalChild: Reference<TestVitalReferenceChild>;

    @decorators.stringProperty<TestCopWithDependsOnNode, 'vitalChildName'>({
        dataType: () => new StringDataType({ maxLength: 50 }),
        isPublished: true,
        isStored: true,
        dependsOn: [{ vitalChild: ['name'] }],
        async updatedValue() {
            return (await this.vitalChild).name;
        },
    })
    readonly vitalChildName: Promise<string>;

    // this._id should not require a dependency on _id:
    @decorators.integerProperty<TestCopWithDependsOnNode, 'dependencyOnId'>({
        isPublished: true,
        isStored: true,
        defaultValue() {
            return this._id;
        },
    })
    readonly dependencyOnId: Promise<integer>;

    // dependencyOnSelf doesn't have to be set in dependsOn
    @decorators.integerProperty<TestCopWithDependsOnNode, 'dependencyOnSelf'>({
        isPublished: true,
        isStored: true,
        dependsOn: ['testingValue'],
        async updatedValue() {
            return (await this.testingValue) < 10 ? this.dependencyOnSelf : this.testingValue;
        },
    })
    readonly dependencyOnSelf: Promise<integer>;

    @decorators.integerProperty<TestCopWithDependsOnNode, 'integerValueA'>({
        isPublished: true,
        isStored: true,
    })
    readonly integerValueA: Promise<integer>;

    @decorators.integerProperty<TestCopWithDependsOnNode, 'integerValueB'>({
        isPublished: true,
        isStored: true,
    })
    readonly integerValueB: Promise<integer>;

    @decorators.integerArrayProperty<TestCopWithDependsOnNode, 'integerArrayVal'>({
        isStored: true,
        isNullable: true,
        isPublished: true,
    })
    readonly integerArrayVal: Promise<integer[]>;

    @decorators.integerProperty<TestCopWithDependsOnNode, 'simpleExpression'>({
        isPublished: true,
        isStored: true,
        dependsOn: ['integerValueA', 'integerValueB'],
        async updatedValue() {
            return (await this.integerValueA) * (await this.integerValueB);
        },
    })
    readonly simpleExpression: Promise<integer>;

    @decorators.integerProperty<TestCopWithDependsOnNode, 'switchStatement'>({
        isPublished: true,
        isStored: true,
        dependsOn: ['integerValueA', 'integerValueB'],
        async updatedValue() {
            switch (await this.integerValueA) {
                case 1: {
                    return this.integerValueA;
                }
                case 2: {
                    return this.integerValueB;
                }
                default:
                    return 0;
            }
        },
    })
    readonly switchStatement: Promise<integer>;

    @decorators.integerProperty<TestCopWithDependsOnNode, 'ifStatement'>({
        isPublished: true,
        isStored: true,
        dependsOn: ['integerValueA', 'integerValueB'],
        async updatedValue() {
            if ((await this.integerValueA) === 1) {
                return this.integerValueA;
            }
            if ((await this.integerValueB) === 0) throw new Error('not supported operation');
            return this.integerValueB;
        },
    })
    readonly ifStatement: Promise<integer>;

    _internalValue = 2;

    @decorators.integerProperty<TestCopWithDependsOnNode, 'variableDeclaration'>({
        isPublished: true,
        isStored: true,
        dependsOn: ['integerValueA'],
        async updatedValue() {
            const value = this._internalValue * (await this.integerValueA);
            return value;
        },
    })
    readonly variableDeclaration: Promise<integer>;

    @decorators.integerProperty<TestCopWithDependsOnNode, 'callExpression'>({
        isPublished: true,
        isStored: true,
        dependsOn: ['integerValueA'],
        async updatedValue() {
            return callExpression(await this.integerValueA);
        },
    })
    readonly callExpression: Promise<integer>;

    @decorators.integerProperty<TestCopWithDependsOnNode, 'arrowFunctionExpression'>({
        isPublished: true,
        isStored: true,
        dependsOn: ['integerArrayVal'],
        async updatedValue() {
            return (await this.integerArrayVal).sort((v1, v2) => v1 - v2)[0];
        },
    })
    readonly arrowFunctionExpression: Promise<integer>;

    @decorators.integerProperty<TestCopWithDependsOnNode, 'arrayExpression'>({
        isPublished: true,
        isStored: true,
        dependsOn: ['integerArrayVal'],
        async updatedValue() {
            return [...[1, 2, 3], ...new Set(await this.integerArrayVal)][0];
        },
    })
    readonly arrayExpression: Promise<integer>;

    @decorators.integerProperty<TestCopWithDependsOnNode, 'sequenceExpression'>({
        isPublished: true,
        isStored: true,
        dependsOn: ['integerValueA', 'integerValueB'],
        async updatedValue() {
            return (await this.integerValueA, await this.integerValueB)
                ? (await this.integerValueA) / (await this.integerValueB)
                : 0;
        },
    })
    readonly sequenceExpression: Promise<integer>;

    @decorators.integerProperty<TestCopWithDependsOnNode, 'length'>({
        isPublished: true,
        isStored: true,
    })
    readonly length: Promise<integer>;

    @decorators.integerProperty<TestCopWithDependsOnNode, 'chainExpression'>({
        isPublished: true,
        isStored: true,
        dependsOn: ['integerArrayVal'],
        async updatedValue() {
            return (await this.integerArrayVal)?.sort((v1, v2) => v1 - v2)[0];
        },
    })
    readonly chainExpression: Promise<integer>;

    @decorators.integerProperty<TestCopWithDependsOnNode, 'lengthMember'>({
        isPublished: true,
        isStored: true,
        dependsOn: ['length'],
        async updatedValue() {
            return (await this.integerArrayVal).length ? (await this.integerArrayVal).length : this.length;
        },
    })
    readonly lengthMember: Promise<integer>;

    @decorators.stringProperty<TestCopWithDependsOnNode, 'templateLiteral'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringDataType({ maxLength: 50 }),
        dependsOn: ['integerValueA', 'integerValueB'],
        async updatedValue() {
            return `${await this.integerValueA}+${await this.integerValueB}`;
        },
    })
    readonly templateLiteral: Promise<string>;
}
