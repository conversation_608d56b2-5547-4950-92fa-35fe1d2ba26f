import { decorators, integer, Node, Reference, StringDataType } from '@sage/xtrem-core';
import { TestReference } from '.';

@decorators.node<TestCopWithNonVitalReference>({
    storage: 'sql',
    isPublished: true,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canDeleteMany: true,
    indexes: [],
})
export class TestCopWithNonVitalReference extends Node {
    @decorators.stringProperty<TestCopWithNonVitalReference, 'value'>({
        dataType: () => new StringDataType({ maxLength: 50 }),
        isPublished: true,
        isStored: true,
        defaultValue() {
            return 'default value';
        },
    })
    readonly value: Promise<string>;

    @decorators.stringProperty<TestCopWithNonVitalReference, 'details'>({
        dataType: () => new StringDataType({ maxLength: 50 }),
        isPublished: true,
        isStored: true,
        dependsOn: [{ reference: ['details'] }],
        async defaultValue() {
            return (await this.reference)?.details;
        },
    })
    readonly details: Promise<string>;

    @decorators.stringProperty<TestCopWithNonVitalReference, 'details2'>({
        dataType: () => new StringDataType({ maxLength: 50 }),
        isPublished: true,
        isStored: true,
        dependsOn: ['reference'],
        async defaultValue() {
            return (await this.reference)?.details;
        },
    })
    readonly details2: Promise<string>;

    @decorators.stringProperty<TestCopWithNonVitalReference, 'details3'>({
        dataType: () => new StringDataType({ maxLength: 50 }),
        isPublished: true,
        isStored: true,
        dependsOn: ['reference'],
        async defaultValue() {
            return (await this.reference).details;
        },
    })
    readonly details3: Promise<string>;

    @decorators.integerProperty<TestCopWithNonVitalReference, 'testingValue'>({
        isPublished: true,
        isStored: true,
    })
    readonly testingValue: Promise<integer>;

    @decorators.referenceProperty<TestCopWithNonVitalReference, 'reference'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        node: () => TestReference,
    })
    readonly reference: Reference<TestReference>;
}
