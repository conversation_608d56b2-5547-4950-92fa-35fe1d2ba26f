import { decorators, Node, StringDataType } from '@sage/xtrem-core';

@decorators.node<TestReference>({
    isPublished: true,
    storage: 'sql',
    indexes: [{ orderBy: { code: 1 }, isUnique: true }],
})
export class TestReference extends Node {
    @decorators.stringProperty<TestReference, 'code'>({
        dataType: () => new StringDataType({ maxLength: 50 }),
        isPublished: true,
        isStored: true,
    })
    readonly code: Promise<string>;

    @decorators.stringProperty<TestReference, 'details'>({
        dataType: () => new StringDataType({ maxLength: 50 }),
        isPublished: true,
        isStored: true,
    })
    readonly details: Promise<string>;
}
