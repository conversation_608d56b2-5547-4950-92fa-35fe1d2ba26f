import { decorators, Node, Reference, StringDataType } from '@sage/xtrem-core';
import { TestCopWithDependsOnNode } from '.';

@decorators.node<TestVitalReferenceChild>({
    isPublished: true,
    storage: 'sql',
    isVitalReferenceChild: true,
    indexes: [{ orderBy: { code: 1 }, isUnique: true }],
})
export class TestVitalReferenceChild extends Node {
    @decorators.stringProperty<TestVitalReferenceChild, 'code'>({
        dataType: () => new StringDataType({ maxLength: 50 }),
        isPublished: true,
        isStored: true,
    })
    readonly code: Promise<string>;

    @decorators.referenceProperty<TestVitalReferenceChild, 'parent'>({
        node: () => TestCopWithDependsOnNode,
        isVitalParent: true,
        isPublished: true,
        isStored: true,
    })
    readonly parent: Reference<TestCopWithDependsOnNode>;

    @decorators.stringProperty<TestVitalReferenceChild, 'name'>({
        dataType: () => new StringDataType({ maxLength: 50 }),
        isPublished: true,
        isStored: true,
    })
    readonly name: Promise<string>;
}
