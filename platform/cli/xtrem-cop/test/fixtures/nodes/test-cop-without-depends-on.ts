import { decorators, Node, StringDataType } from '@sage/xtrem-core';

@decorators.node<TestCopWithoutDependsOnNode>({
    storage: 'sql',
    isPublished: true,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canDeleteMany: true,
    indexes: [],
})
export class TestCopWithoutDependsOnNode extends Node {
    @decorators.stringProperty<TestCopWithoutDependsOnNode, 'value'>({
        dataType: () => new StringDataType({ maxLength: 50 }),
        isPublished: true,
        isStored: true,
        defaultValue() {
            return 'default value';
        },
    })
    readonly value: Promise<string>;

    @decorators.stringProperty<TestCopWithoutDependsOnNode, 'propWithMissingDependsOn'>({
        dataType: () => new StringDataType({ maxLength: 50 }),
        isPublished: true,
        isStored: true,
        defaultValue() {
            return this.value;
        },
    })
    readonly propWithMissingDependsOn: Promise<string>;
}
