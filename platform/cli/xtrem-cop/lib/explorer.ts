import { removeCodeCoverageInstrumentation } from '@sage/xtrem-shared';
import * as estree from 'estree';

const parse = require('espree').parse;

export class ExplorationError extends Error {
    constructor(
        readonly node: estree.BaseNode,
        message: string,
    ) {
        super(message);
    }
}

export class Explorer {
    private static exploreThisExpression(): string {
        return 'this';
    }

    // eslint-disable-next-line class-methods-use-this
    private exploreMemberExpression(expression: estree.MemberExpression): string[] {
        const parent = this.exploreExpression(expression.object);

        if (expression.property.type === 'Identifier') {
            const property = <estree.Identifier>expression.property;
            return [`${parent}.${property.name}`];
        }
        return this.exploreExpression(expression.property);
    }

    private exploreChainExpression(expression: estree.ChainExpression): string[] {
        if (expression.expression.type === 'MemberExpression') {
            return this.exploreMemberExpression(expression.expression);
        }
        if (expression.expression?.type === 'CallExpression') {
            return this.exploreCallExpression(expression.expression);
        }
        throw new ExplorationError(expression.expression, 'kind of chain expression not supported');
    }

    private exploreConditionalExpression(expression: estree.ConditionalExpression): string[] {
        return [
            ...this.exploreExpression(expression.test),
            ...this.exploreExpression(expression.consequent),
            ...this.exploreExpression(expression.alternate),
        ];
    }

    private exploreBinaryExpression(expression: estree.BinaryExpression): string[] {
        return [...this.exploreExpression(expression.left), ...this.exploreExpression(expression.right)];
    }

    private exploreLogicalExpression(expression: estree.LogicalExpression): string[] {
        const explored = [...this.exploreExpression(expression.left), ...this.exploreExpression(expression.right)];

        if (['&&', '||', '??'].indexOf(expression.operator) < 0)
            throw new ExplorationError(expression, `cannot explore operator: ${expression.operator}`);
        return explored;
    }

    private exploreUnaryExpression(expression: estree.UnaryExpression): string[] {
        const explored = this.exploreExpression(expression.argument);
        const op = expression.operator;
        if (['!', '+'].indexOf(op) < 0) {
            throw new ExplorationError(expression, `Unsupported unary operator: ${op}`);
        }
        return explored;
    }

    private exploreTemplateLiteral(expression: estree.TemplateLiteral): string[] {
        let explored = [] as string[];
        expression.expressions.forEach((_, i) => {
            explored = [...explored, ...this.exploreExpression(expression.quasis[i])];
            explored = [...explored, ...this.exploreExpression(expression.expressions[i])];
        });
        explored = [...explored, ...this.exploreExpression(expression.quasis[expression.quasis.length - 1])];
        return explored;
    }

    private exploreBlockStatement(expression: estree.BlockStatement): string[] {
        let explored = [] as string[];

        // eslint-disable-next-line no-return-assign
        expression.body.forEach(statement => (explored = [...explored, ...this.exploreExpression(statement)]));
        return explored;
    }

    private exploreReturnStatement(expression: estree.ReturnStatement): string[] {
        return this.exploreExpression(expression.argument!);
    }

    private exploreSwitchStatement(expression: estree.SwitchStatement): string[] {
        let explored = this.exploreExpression(expression.discriminant);
        expression.cases.forEach((switchCase: estree.SwitchCase) => {
            switchCase.consequent.forEach(
                // eslint-disable-next-line no-return-assign
                statement => (explored = [...explored, ...this.exploreExpression(statement)]),
            );
        });
        return explored;
    }

    private exploreIfStatement(expression: estree.IfStatement): string[] {
        let explored = [...this.exploreExpression(expression.test), ...this.exploreExpression(expression.consequent)];
        if (expression.alternate) explored = [...explored, ...this.exploreExpression(expression.alternate)];
        return explored;
    }

    private exploreCallExpression(expression: estree.CallExpression): string[] {
        let explored = this.exploreExpression(expression.callee);
        if (explored.length === 1) {
            const tokens = this.exploreExpression(expression.callee)[0].split('.');
            if (tokens.length > 1) explored = [tokens.slice(0, tokens.length - 1).join('.')];
        }
        // eslint-disable-next-line no-return-assign
        expression.arguments.forEach(argument => (explored = [...explored, ...this.exploreExpression(argument)]));
        return explored;
    }

    private exploreVariableDeclaration(expression: estree.VariableDeclaration): string[] {
        let explored = [] as string[];
        expression.declarations.forEach(declarator => {
            if (declarator.init) explored = [...explored, ...this.exploreExpression(declarator.init)];
        });
        return explored;
    }

    private exploreProperty(expression: estree.Property): string[] {
        if (expression.key.type !== 'Identifier') {
            throw new Error(`Invalid property type ${expression.key.type}`);
        }
        return this.exploreExpression(expression.value);
    }

    private exploreObject(expression: estree.ObjectExpression): string[] {
        let explored = [] as string[];
        expression.properties.forEach(prop => {
            if (prop.type !== 'Property') {
                throw new Error(`Invalid object member ${prop.type}`);
            }
            explored = [...explored, ...this.exploreProperty(prop as estree.Property)];
        });
        return explored;
    }

    private exploreArrayExpression(expression: estree.ArrayExpression): string[] {
        let explored = [] as string[];
        expression.elements.forEach(element => {
            if (!element) return;
            if (element.type === 'SpreadElement') {
                const spreadElement = <estree.SpreadElement>element;
                explored = [...explored, ...this.exploreExpression(spreadElement.argument)];
            } else {
                explored = [...explored, ...this.exploreExpression(element)];
            }
        });
        return explored;
    }

    private explorerNewExpression(expression: estree.NewExpression): string[] {
        let explored = [] as string[];
        // eslint-disable-next-line no-return-assign
        expression.arguments.forEach(argument => (explored = [...explored, ...this.exploreExpression(argument)]));
        return explored;
    }

    private explorerSequenceExpression(expression: estree.SequenceExpression): string[] {
        let explored = [] as string[];
        // eslint-disable-next-line no-return-assign
        expression.expressions.forEach(element => (explored = [...explored, ...this.exploreExpression(element)]));
        return explored;
    }

    private explorerThrowStatement(expression: estree.ThrowStatement): string[] {
        return this.exploreExpression(expression.argument);
    }

    private exploreExpressionStatement(expression: estree.ExpressionStatement): string[] {
        return this.exploreExpression(expression.expression);
    }

    private exploreAwaitExpression(expression: estree.AwaitExpression): string[] {
        return this.exploreExpression(expression.argument);
    }

    exploreExpression(expression: estree.BaseExpression): string[] {
        switch (expression.type) {
            case 'ExpressionStatement':
                return this.exploreExpressionStatement(<estree.ExpressionStatement>expression);
            case 'BlockStatement':
                return this.exploreBlockStatement(<estree.BlockStatement>expression);
            case 'MemberExpression':
                return this.exploreMemberExpression(<estree.MemberExpression>expression);
            case 'TemplateLiteral':
                return this.exploreTemplateLiteral(<estree.TemplateLiteral>expression);
            case 'ThisExpression':
                return [Explorer.exploreThisExpression()];
            case 'BinaryExpression':
                return this.exploreBinaryExpression(<estree.BinaryExpression>expression);
            case 'LogicalExpression':
                return this.exploreLogicalExpression(<estree.LogicalExpression>expression);
            case 'UnaryExpression':
                return this.exploreUnaryExpression(<estree.UnaryExpression>expression);
            case 'ObjectExpression':
                return this.exploreObject(<estree.ObjectExpression>expression);
            case 'ChainExpression':
                return this.exploreChainExpression(<estree.ChainExpression>expression);
            case 'ConditionalExpression':
                return this.exploreConditionalExpression(<estree.ConditionalExpression>expression);
            case 'ReturnStatement':
                return this.exploreReturnStatement(<estree.ReturnStatement>expression);
            case 'SwitchStatement':
                return this.exploreSwitchStatement(<estree.SwitchStatement>expression);
            case 'IfStatement':
                return this.exploreIfStatement(<estree.IfStatement>expression);
            case 'CallExpression':
                return this.exploreCallExpression(<estree.CallExpression>expression);
            case 'ArrowFunctionExpression':
                return this.exploreArrowFunctionExpression(<estree.ArrowFunctionExpression>expression);
            case 'VariableDeclaration':
                return this.exploreVariableDeclaration(<estree.VariableDeclaration>expression);
            case 'ArrayExpression':
                return this.exploreArrayExpression(<estree.ArrayExpression>expression);
            case 'NewExpression':
                return this.explorerNewExpression(<estree.NewExpression>expression);
            case 'SequenceExpression':
                return this.explorerSequenceExpression(<estree.SequenceExpression>expression);
            case 'AwaitExpression':
                return this.exploreAwaitExpression(<estree.AwaitExpression>expression);
            case 'ThrowStatement':
                return this.explorerThrowStatement(<estree.ThrowStatement>expression);
            case 'TemplateElement':
            case 'Literal':
            case 'Identifier':
                return [];
            default:
                throw new ExplorationError(expression, `invalid expression type: ${expression.type}`);
                break;
        }
        return [];
    }

    exploreFunctionExpression(expression: estree.FunctionExpression | estree.ArrowFunctionExpression): string[] {
        const statements = (expression.body as estree.BlockStatement).body;

        let explored = [] as string[];
        // eslint-disable-next-line no-return-assign
        statements.forEach(statement => (explored = [...explored, ...this.exploreExpression(statement)]));
        return explored;
    }

    exploreArrowFunctionExpression(expression: estree.ArrowFunctionExpression): string[] {
        if (expression.body.type === 'BlockStatement')
            return this.exploreBlockStatement(<estree.BlockStatement>expression.body);

        return this.exploreExpression(<estree.Expression>expression.body);
    }

    exploreFunction(fn: () => any): string[] {
        let str = removeCodeCoverageInstrumentation(fn.toString());

        if (str.startsWith('async ')) {
            str = str.replace(/^async (\w+)/, 'async function');
            /* istanbul ignore next */
        } else if (!str.startsWith('function ') && !str.startsWith('()')) {
            str = `function ${str}`;
        }
        str = `(${str})`;

        const parsed = parse(`(${str})`, {
            ecmaVersion: 2022,
        });

        if (parsed.type !== 'Program') throw new ExplorationError(parsed, `expected Program, got ${parsed.type}`);
        const body = (<estree.Program>parsed).body[0];
        if (!body) throw new ExplorationError(parsed, 'no program body');
        if (body.type !== 'ExpressionStatement')
            throw new ExplorationError(parsed, `expected ExpressionStatement, got ${body.type}`);

        const allowedExpressions = ['FunctionExpression', 'ArrowFunctionExpression'];
        const expression = (<estree.ExpressionStatement>body).expression;
        if (!allowedExpressions.includes(expression.type)) {
            throw new ExplorationError(parsed, `expected ${allowedExpressions.join(' or ')}, got ${expression.type}`);
        }

        let explored =
            expression.type === 'ArrowFunctionExpression'
                ? this.exploreArrowFunctionExpression(<estree.ArrowFunctionExpression>expression)
                : this.exploreFunctionExpression(<estree.FunctionExpression>expression);

        const filtered = new Set<string>();
        explored
            .filter(e => e.length && e.startsWith('this.') && e.indexOf('.$.') < 0 && e.indexOf('._') < 0)
            .forEach(dep => filtered.add(dep));
        explored = [...filtered.values()];
        return explored;
    }
}
