import { Application, Logger, LogicError, NodeFactory, Property } from '@sage/xtrem-core';
import { Explorer } from './explorer';

const logger = Logger.getLogger(__filename, 'verifyDependsOn');

export function verifyDependsOn(application: Application): void {
    application
        .getAllFactories()
        .filter(factory => !factory.isAbstract)
        // Limit controls to the package which is being built:
        .filter(factory => factory.package.name === application.mainPackage.name)
        .forEach(factory => {
            factory.properties.forEach(property => {
                verifyPropertyDependsOn(factory, property);
            });
        });
}

export function fixDependsOn(dependsOn: any[], propertyPaths: string[]): any[] {
    propertyPaths.forEach(propertyPath => {
        const tokens = propertyPath.split('.');
        const key = tokens[0];
        if (tokens.length === 1) {
            // Let's add a string:
            if (dependsOn.indexOf(key) < 0) dependsOn.push(key);
        } else {
            // Let's remove the element present as a string:
            // eslint-disable-next-line no-param-reassign
            dependsOn = dependsOn.filter(dep => dep !== key);

            // Let's add an object
            if (
                !dependsOn.find(dep => {
                    return Object.keys(dep).find(k => {
                        if (k === key) {
                            dep[key] = fixDependsOn(dep[key], [tokens.slice(1).join('.')]);
                            return true;
                        }
                        return false;
                    });
                })
            ) {
                const newDependency = { [key]: [] };
                dependsOn.push(newDependency);
                fixDependsOn(newDependency[key], [tokens.slice(1).join('.')]);
            }
        }
    });
    return dependsOn;
}

const excludeFunctionNames = ['today', 'nanoid'];

export function verifyPropertyDependsOn(factory: NodeFactory, property: Property): void {
    if (typeof property.defaultValue === 'function' || typeof property.updatedValue === 'function') {
        // TODO: create a new jira ticket to handle chained functions more appropriately
        if (typeof property.defaultValue === 'function' && property.defaultValue.name !== 'chained') {
            verifyRule(factory, property, 'defaultValue');
        }
        if (typeof property.updatedValue === 'function' && property.updatedValue.name !== 'chained') {
            verifyRule(factory, property, 'updatedValue');
        }
    }
}

export function sanitizeCollectedDependencyPaths(arr: string[]) {
    const ignore = ['this', '_super', '_id'];
    const cleanArray: string[] = [];
    arr.forEach(el => {
        const newEl = el
            .split('.')
            .filter(val => !ignore.includes(val))
            .join('.');
        if (newEl.length) cleanArray.push(newEl);
    });
    return [...new Set(cleanArray)];
}

export function sanitizeDependsOn(value: any, path: string[] = [], results: string[] = []): string[] {
    if (Array.isArray(value)) {
        value.forEach(v => sanitizeDependsOn(v, path, results));
    } else if (value && typeof value === 'object') {
        Object.keys(value).forEach(key => {
            const v = value[key];
            sanitizeDependsOn(v, [...path, key], results);
        });
    } else if (typeof value === 'string') {
        results.push([...path, value].join('.'));
    } else {
        throw new LogicError(`Invalid type: ${typeof value}`);
    }
    return results;
}

function collectDependencyPaths(
    factory: NodeFactory,
    property: Property,
    ruleName: 'defaultValue' | 'updatedValue',
): string[] {
    const explorer = new Explorer();
    const dependencyPaths = explorer
        .exploreFunction(property[ruleName] as () => any)
        .filter(dep => dep !== `this.${property.name}`)
        .map(dep => {
            const splittedDep = dep.split('.');
            if (splittedDep.length >= 3 && splittedDep[0] === 'this') {
                const fromProperty = factory.findProperty(splittedDep[1]);
                if (
                    fromProperty &&
                    (fromProperty.isReferenceProperty() || fromProperty.isCollectionProperty()) &&
                    fromProperty.isVital === false
                ) {
                    // Replace the dependency on a reference's property with a dependency on the reference:
                    return `this.${splittedDep[1]}`;
                }
            }
            return dep;
        });
    return dependencyPaths;
}

function verifyRule(factory: NodeFactory, property: Property, ruleName: 'defaultValue' | 'updatedValue'): void {
    if (excludeFunctionNames.includes(property.name)) return;
    try {
        const collectedDependencyPaths = collectDependencyPaths(factory, property, ruleName);
        if (collectedDependencyPaths.length === 0) return;

        const sanitizedCollectedDependencyPaths = sanitizeCollectedDependencyPaths(collectedDependencyPaths);
        if (sanitizedCollectedDependencyPaths.length === 0) return;
        if (!property.dependsOn) {
            logger.warn(
                `<${property.factory.name}, '${
                    property.name
                }'> ${ruleName}: the dependsOn attribute should be equal to ${JSON.stringify(
                    fixDependsOn([], sanitizedCollectedDependencyPaths),
                )}`,
            );
            return;
        }

        const dependsOn = sanitizeDependsOn(property.dependsOn);
        const dependencyDifference = sanitizedCollectedDependencyPaths
            .filter(dependency => !dependsOn.find(dep => dep.startsWith(dependency)))
            // Let's exclude elements that ends with '.length' because Explorer isn't able to make the difference between a property'name and a property's attribute:
            .filter(dep => !dep.endsWith('.length'));

        if (dependencyDifference.length > 0) {
            logger.warn(
                `<${property.factory.name}, '${
                    property.name
                }'> ${ruleName}: the dependsOn attribute should be equal to ${JSON.stringify(
                    fixDependsOn(property.dependsOn, sanitizedCollectedDependencyPaths),
                )}`,
            );
        }
    } catch (e) {
        if (ruleName === 'updatedValue')
            logger.warn(
                `${property.factory.name}.${property.name}.${ruleName}: Dependency is too complex to be verified (${e.message}) ${e.stack}`,
            );
    }
}
