import { expect } from 'chai';
import * as path from 'path';
import * as sinon from 'sinon';
import * as dt from '../lib/transformers/data-type-name-extractor';
import * as transformerUtils from '../lib/transformers/transformer-utils';
import { transpileFixture } from './test-helper';

const FIXTURE_DATA_TYPE = path.resolve('./test/fixtures/test-app/lib/data-types/test-data-type.ts');
const FIXTURE_ENUM = path.resolve('./test/fixtures/test-app/lib/enums/saleable-product-line-origin.ts');

describe('DataTypes Transformer', () => {
    let writeLiteralsStub: sinon.SinonStub<any>;

    beforeEach(() => {
        writeLiteralsStub = sinon.stub(transformerUtils, 'writeStringLiteralsToBase');
    });

    afterEach(() => {
        writeLiteralsStub.restore();
    });

    describe('data-types', () => {
        it('should extract data types from /data-types', () => {
            transpileFixture(FIXTURE_DATA_TYPE, [dt.dataTypesNameExtractor]);
            const call = writeLiteralsStub.getCall(0);
            const dictionary = call.args[0];
            expect(Object.keys(dictionary).length).to.eq(2);
            expect(dictionary['@sage/test-cli-transformers-app/data_types__string_data_type__name']).to.be.eq(
                'String data type',
            );
            expect(dictionary['@sage/test-cli-transformers-app/data_types__decimal_data_type__name']).to.be.eq(
                'Decimal data type',
            );
        });
        it('should extract enums data types from /enums', () => {
            transpileFixture(FIXTURE_ENUM, [dt.dataTypesNameExtractor]);
            const call = writeLiteralsStub.getCall(0);
            const dictionary = call.args[0];
            expect(Object.keys(dictionary).length).to.eq(1);
            expect(
                dictionary['@sage/test-cli-transformers-app/data_types__saleable_product_line_origin_enum__name'],
            ).to.be.eq('Saleable product line origin enum');
        });
    });
});
