import { expect } from 'chai';
import * as fs from 'fs';
import * as path from 'path';
import * as sinon from 'sinon';
import * as clt from '../lib/transformers/client-list-transformer';
import { transpileFixture } from './test-helper';

const FIXTURE_PAGE_DECORATED = path.resolve('./test/fixtures/test-app/lib/pages/action-buttons-decorated.ts');

const FIXTURE_NODE = path.resolve('./test/fixtures/test-app/lib/nodes/address.ts');

describe('Client List Transformer', () => {
    let visitorSpy: sinon.SinonSpy<any>;
    let writeFileSpy: sinon.SinonSpy<any>;

    beforeEach(() => {
        visitorSpy = sinon.spy(clt, 'visitor');
        writeFileSpy = sinon.spy(fs, 'writeFileSync');
    });

    afterEach(() => {
        visitorSpy.restore();
        writeFileSpy.restore();
    });

    describe('execution', () => {
        it('should check if visitor is not null when passing through FIXTURE_NODE', () => {
            transpileFixture(FIXTURE_NODE, [clt.clientListTransformer]);
            expect(visitorSpy.getCalls().length).to.be.equal(209);
        });

        it('should check if visitor is not null when passing through FIXTURE_PAGE_DECORATED', () => {
            transpileFixture(FIXTURE_PAGE_DECORATED, [clt.clientListTransformer]);
            expect(visitorSpy.getCalls().length).to.be.equal(599);
        });
    });

    describe('client list', () => {
        it('should extract client list members', () => {
            transpileFixture(FIXTURE_PAGE_DECORATED, [clt.clientListTransformer]);
            expect(visitorSpy.getCalls().length).to.be.equal(599);
        });

        it('should extract the list of used strings', () => {
            transpileFixture(FIXTURE_PAGE_DECORATED, [clt.clientListTransformer]);
            const extractedData = JSON.parse(writeFileSpy.getCall(0).args[1]);
            expect(extractedData.literals.strings).to.deep.eq([
                '@sage/test-cli-transformers-app/pages__action_buttons__decorator__title',
                '@sage/test-cli-transformers-app/pages__action_buttons__decorator__onLoad',
                '@sage/test-cli-transformers-app/pages__action_buttons__saveCrudAction__decorator__onClick',
                '@sage/test-cli-transformers-app/pages__action_buttons__createCrudAction__decorator__onClick',
                '@sage/test-cli-transformers-app/pages__action_buttons__createCrudAction__decorator__onClick__2',
                '@sage/test-cli-transformers-app/pages__action_buttons__createCrudAction__decorator__onClick__3',
                '@sage/test-cli-transformers-app/pages__action_buttons__deleteCrudAction__decorator__onClick',
                '@sage/test-cli-transformers-app/pages__action_buttons__closeCrudAction__decorator__onClick',
                '@sage/test-cli-transformers-app/pages__action_buttons__businessAction1__decorator__title',
                '@sage/test-cli-transformers-app/pages__action_buttons__businessAction1__decorator__onClick',
                '@sage/test-cli-transformers-app/pages__action_buttons__businessAction2__decorator__title',
                '@sage/test-cli-transformers-app/pages__action_buttons__businessAction2__decorator__onClick',
                '@sage/test-cli-transformers-app/pages__action_buttons__mainSection__decorator__title',
                '@sage/test-cli-transformers-app/pages__action_buttons__mainBlock__decorator__title',
                '@sage/test-cli-transformers-app/pages__action_buttons__resultField__decorator__title',
                '@sage/test-cli-transformers-app/pages__action_buttons__controlSection__decorator__title',
                '@sage/test-cli-transformers-app/pages__action_buttons__controlBlock__decorator__title',
                '@sage/test-cli-transformers-app/pages__action_buttons__disableBusinessAction1__decorator__title',
                '@sage/test-cli-transformers-app/pages__action_buttons__hideBusinessAction2__decorator__title',
                '@sage/test-cli-transformers-app/pages__action_buttons__disableSaveCrudAction__decorator__title',
            ]);
        });

        it('should extract the list of used enums', () => {
            transpileFixture(FIXTURE_PAGE_DECORATED, [clt.clientListTransformer]);
            expect(writeFileSpy.getCalls().length).to.eq(1);
            const extractedData = JSON.parse(writeFileSpy.getCall(0).args[1]);
            expect(extractedData.literals.enums).to.deep.eq([
                '@sage/test-cli-transformers-app/MyTestEnum',
                '@sage/xtrem-i18n-app/EnumUsedAsOption',
            ]);
        });
    });
});
