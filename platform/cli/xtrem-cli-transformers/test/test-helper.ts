/* eslint-disable no-console */
import * as fs from 'fs';
import * as path from 'path';
import * as ts from 'typescript';

export const transpileFixture = (
    fileName: string,
    beforeTransformers: (ts.TransformerFactory<ts.SourceFile> | ts.CustomTransformerFactory)[],
) => {
    const source = fs.readFileSync(fileName, 'utf-8');
    const result = ts.transpileModule(source, {
        fileName,
        compilerOptions: { module: ts.ModuleKind.CommonJS },
        transformers: { before: beforeTransformers },
    });
    return result.outputText;
};

export const transpileFixtures = (
    files: string[],
    beforeTransformers: (ts.TransformerFactory<ts.SourceFile> | ts.CustomTransformerFactory)[],
) => {
    const program = ts.createProgram(
        [path.join(__dirname, '../../../front-end/xtrem-ui/lib/module-types.d.ts'), ...files],
        {
            baseUrl: path.join(__dirname, '..'),
            outDir: path.join(__dirname, './fixtures/test-app/build'),
            module: ts.ModuleKind.ESNext,
            moduleResolution: ts.ModuleResolutionKind.NodeJs,
            experimentalDecorators: true,
            paths: {
                '@sage/xtrem-ui': ['../../front-end/xtrem-ui/lib'],
                '@sage/xtrem-core': ['../../back-end/xtrem-core'],
            },
            lib: ['lib.es2022.d.ts', 'lib.esnext.d.ts', 'lib.dom.d.ts'],
            target: ts.ScriptTarget.ES2022,
            esModuleInterop: true,
            resolveJsonModule: true,
            jsx: ts.JsxEmit.React,
            ignoreDeprecations: '5.0',
            allowSyntheticDefaultImports: true,
            skipLibCheck: true,
            skipDefaultLibCheck: true,
        },
    );
    const emitResult = program.emit(undefined, undefined, undefined, undefined, {
        before: beforeTransformers,
    });
    const allDiagnostics = ts.getPreEmitDiagnostics(program).concat(emitResult.diagnostics);

    allDiagnostics.forEach(printCompilerError);
    return emitResult.emittedFiles;
};

const printCompilerError = (diagnostic: ts.Diagnostic) => {
    if (diagnostic.file && diagnostic.start) {
        const { line, character } = diagnostic.file.getLineAndCharacterOfPosition(diagnostic.start);
        const message = ts.flattenDiagnosticMessageText(diagnostic.messageText, '\n').trim();
        console.log(`${diagnostic.file.fileName} (${line + 1},${character + 1}): ${message}`);
    } else {
        console.log(`${ts.flattenDiagnosticMessageText(diagnostic.messageText, '\n').trim()}`);
    }
};
