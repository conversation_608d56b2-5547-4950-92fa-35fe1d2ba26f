import * as fs from 'fs';
import * as path from 'path';
import * as ts from 'typescript';
import { decimalTransformer } from '../lib/transformers';

const options: ts.TranspileOptions = {
    transformers: { before: [decimalTransformer] },
    compilerOptions: ts.convertCompilerOptionsFromJson(
        fs.readFileSync('tsconfig.json', 'utf-8'),
        __dirname,
        'tsconfig.json',
    ).options,
    fileName: 'decimal-tests.ts',
};
const result = ts.transpileModule(fs.readFileSync(path.resolve('./test/decimal-tests.ts'), 'utf-8'), options);
// This line (with the export) has to be removed to allow to be loaded in this context:
const source = result.outputText.replace('exports.__esModule = true;', '');
// eslint-disable-next-line no-eval
eval(source);
