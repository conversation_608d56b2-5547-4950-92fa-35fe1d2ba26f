/* eslint-disable @typescript-eslint/no-unused-expressions */
import { expect } from 'chai';
import * as fs from 'fs';
import * as path from 'path';
import * as sinon from 'sinon';
import { decoratorTransformer, messageTransformer } from '../lib/transformers';
import { transpileFixtures } from './test-helper';

const FIXTURE_PAGE = path.resolve('./test/fixtures/test-app/lib/pages/action-buttons.ts');
const FIXTURE_NODE = path.resolve('./test/fixtures/test-app/lib/nodes/address.ts');

function removeFolder(p: string) {
    if (fs.existsSync(p)) {
        fs.readdirSync(p).forEach(file => {
            const curPath = `${p}/${file}`;
            if (fs.lstatSync(curPath).isDirectory()) {
                removeFolder(curPath);
            } else {
                fs.unlinkSync(curPath);
            }
        });
        fs.rmdirSync(p);
    }
}

describe('Transformers', () => {
    let writeFileStub: sinon.SinonStub<any>;
    beforeEach(() => {
        removeFolder(path.join(__dirname, 'fixtures/test-app/build/lib/pages'));
        removeFolder(path.join(__dirname, 'fixtures/test-app/build/lib/nodes'));
        removeFolder(path.join(__dirname, 'fixtures/test-app/lib/i18n'));
        writeFileStub = (() => {
            const original = fs.writeFileSync;
            return sinon
                .stub(fs, 'writeFileSync')
                .callsFake((filePath: fs.PathLike | number, data: any, options?: fs.WriteFileOptions): void => {
                    if (typeof filePath !== 'string' || /platform\/cli\/xtrem-cli-transformers/.test(filePath)) {
                        original(filePath, data, options);
                    }
                });
        })();
    });

    afterEach(() => {
        writeFileStub.restore();
    });

    after(() => {
        removeFolder(path.join(__dirname, 'fixtures/test-app/build/lib/pages'));
        removeFolder(path.join(__dirname, 'fixtures/test-app/build/lib/nodes'));
        removeFolder(path.join(__dirname, 'fixtures/test-app/lib/i18n'));
    });

    it('should transpile page with decorator & message transformer', () => {
        transpileFixtures([FIXTURE_PAGE], [decoratorTransformer, messageTransformer]);
        expect(
            fs.existsSync(
                path.join(
                    __dirname,
                    'fixtures/test-app/build/cli/xtrem-cli-transformers/test/fixtures/test-app/lib/pages/action-buttons.js',
                ),
            ),
        ).to.be.true;
    });

    xit('should generate dictionary containing entries from both pages and nodes', () => {
        transpileFixtures([FIXTURE_PAGE, FIXTURE_NODE], [decoratorTransformer, messageTransformer]);
        expect(
            fs.existsSync(
                path.join(
                    __dirname,
                    'fixtures/test-app/build/cli/xtrem-cli-transformers/test/fixtures/test-app/lib/pages/action-buttons.js',
                ),
            ),
        ).to.be.true;
        expect(
            fs.existsSync(
                path.join(
                    __dirname,
                    'fixtures/test-app/build/cli/xtrem-cli-transformers/test/fixtures/test-app/lib/nodes/address.js',
                ),
            ),
        ).to.be.true;
        const baseDictionary = JSON.parse(
            fs.readFileSync(path.join(__dirname, 'fixtures/test-app/lib/i18n/base.json'), 'utf8'),
        );
        expect(baseDictionary).to.include({ '@sage/test-cli-transformers-app/customKey': 'custom value' });
    });
});
