import { Dict, supportedLocales } from '@sage/xtrem-shared';
import { expect } from 'chai';
import * as fs from 'fs';
import * as path from 'path';
import * as sinon from 'sinon';
import * as mt from '../lib/transformers/message-transformer';
import { transpileFixture } from './test-helper';

const FIXTURE_PAGE = path.resolve('./test/fixtures/test-app/lib/pages/action-buttons.ts');
const FIXTURE_PAGE_DECORATED = path.resolve('./test/fixtures/test-app/lib/pages/action-buttons-decorated.ts');
const FIXTURE_NODE_INDEX = path.resolve('./test/fixtures/test-app/lib/nodes/_index.ts');
const FIXTURE_NODE = path.resolve('./test/fixtures/test-app/lib/nodes/address.ts');
const FIXTURE_PAGE_INDEX = path.resolve('./test/fixtures/test-app/lib/pages/index.ts');

function checkLanguageFiles(stub: sinon.SinonStub<any>, dictionaryContent?: { [key: string]: Dict<string> }) {
    const callNumber = stub.callCount;
    for (let i = 0; i < callNumber; i += 1) {
        const callArguments: string[] = stub.getCall(i).args;
        const filePath = callArguments[0].replace(/\\/g, '/'); // CC Necessary for Windows
        const fileName = filePath.split('/')!.pop()!.replace('.json', '');
        if (fileName !== 'base') {
            expect(supportedLocales).contains(fileName);
        }
        expect(filePath).match(new RegExp(`test-app/lib/i18n/${fileName}.json$`));
        const dictionary: Dict<string> = JSON.parse(callArguments[1]);
        if (dictionaryContent) {
            expect(dictionary).to.include(dictionaryContent[fileName]);
        } else {
            expect(Object.keys(dictionary)).to.have.length(22);
        }
    }
}

function checkLanguageFilesUpdate(
    initialDictionary: Dict<Dict<string>>,
    expectedBaseContent: Dict<string>,
    expectedLanguageContent: Dict<string>,
    writeFileStub: sinon.SinonStub<any, any>,
) {
    const mergedDictionary = {} as any;
    Object.keys(initialDictionary).forEach(k => {
        const targetObj = k === 'base' ? expectedBaseContent : expectedLanguageContent;
        Object.keys(targetObj).forEach(dictionaryKey => {
            mergedDictionary[k] = {
                ...mergedDictionary[k],
                [dictionaryKey]: targetObj[dictionaryKey],
            };
        });
    });
    checkLanguageFiles(writeFileStub, mergedDictionary);
}

describe('Message Transformer', () => {
    describe('execution', () => {
        let visitorSpy: sinon.SinonSpy<any>;

        beforeEach(() => {
            visitorSpy = sinon.spy(mt, 'messageTransformVisitor');
        });

        afterEach(() => {
            visitorSpy.restore();
        });

        it('should include node files', () => {
            transpileFixture(FIXTURE_NODE_INDEX, [mt.messageTransformer]);
            transpileFixture(FIXTURE_NODE, [mt.messageTransformer]);
            expect(visitorSpy.getCalls().length).to.be.equal(213);
        });

        it('should include page files', () => {
            transpileFixture(FIXTURE_PAGE, [mt.messageTransformer]);
            transpileFixture(FIXTURE_PAGE_INDEX, [mt.messageTransformer]);
            expect(visitorSpy.getCalls().length).to.be.equal(463);
        });
    });

    xdescribe('file generation', () => {
        let dictionary: Dict<Dict<string>>;
        beforeEach(() => {
            readFileStub = (() => {
                const original = fs.readFileSync;
                return sinon
                    .stub(fs, 'readFileSync')
                    .callsFake(
                        (
                            filePath: fs.PathLike | number,
                            options: { encoding: BufferEncoding; flag?: string } | BufferEncoding,
                        ): string => {
                            if (typeof filePath !== 'string') {
                                return original(filePath, options);
                            }
                            const fileName = path.basename(filePath, '.json');
                            const result =
                                dictionary && fileName in dictionary
                                    ? JSON.stringify(dictionary[fileName])
                                    : original(filePath, options);
                            return result;
                        },
                    );
            })();
            writeFileStub = sinon.stub(fs, 'writeFileSync');
        });

        afterEach(() => {
            writeFileStub.restore();
            readFileStub.restore();
        });

        it('should create language files', () => {
            transpileFixture(FIXTURE_PAGE_DECORATED, [mt.messageTransformer]);
            expect(readFileStub.callCount).to.equal(14);
            expect(writeFileStub.callCount).to.equal(12);
            checkLanguageFiles(writeFileStub);
        });

        let writeFileStub: sinon.SinonStub<any>;
        let readFileStub: sinon.SinonStub<any>;

        it('should update language files', () => {
            dictionary = supportedLocales.reduce(
                (r, l) => {
                    r[l] = {};
                    return r;
                },
                {} as Dict<Dict<string>>,
            );
            transpileFixture(FIXTURE_PAGE_DECORATED, [mt.messageTransformer]);
            expect(readFileStub.callCount).to.equal(14);
            expect(writeFileStub.callCount).to.equal(12);
            const expectedLanguageFile = {
                '@sage/test-cli-transformers-app/pages__action_buttons__decorator__title': '',
                '@sage/test-cli-transformers-app/pages__action_buttons__decorator__onLoad': '',
                '@sage/test-cli-transformers-app/pages__action_buttons__saveCrudAction__decorator__onClick': '',
                '@sage/test-cli-transformers-app/pages__action_buttons__createCrudAction__decorator__onClick': '',
                '@sage/test-cli-transformers-app/pages__action_buttons__createCrudAction__decorator__onClick__2': '',
                '@sage/test-cli-transformers-app/pages__action_buttons__createCrudAction__decorator__onClick__3': '',
                '@sage/test-cli-transformers-app/pages__action_buttons__deleteCrudAction__decorator__onClick': '',
                '@sage/test-cli-transformers-app/pages__action_buttons__closeCrudAction__decorator__onClick': '',
                '@sage/test-cli-transformers-app/pages__action_buttons__businessAction1__decorator__title': '',
                '@sage/test-cli-transformers-app/pages__action_buttons__businessAction1__decorator__onClick': '',
                '@sage/test-cli-transformers-app/pages__action_buttons__businessAction2__decorator__title': '',
                '@sage/test-cli-transformers-app/pages__action_buttons__businessAction2__decorator__onClick': '',
                '@sage/test-cli-transformers-app/pages__action_buttons__mainSection__decorator__title': '',
                '@sage/test-cli-transformers-app/pages__action_buttons__mainBlock__decorator__title': '',
                '@sage/test-cli-transformers-app/pages__action_buttons__resultField__decorator__title': '',
                '@sage/test-cli-transformers-app/pages__action_buttons__controlSection__decorator__title': '',
                '@sage/test-cli-transformers-app/pages__action_buttons__controlBlock__decorator__title': '',
                '@sage/test-cli-transformers-app/pages__action_buttons__disableBusinessAction1__decorator__title': '',
                '@sage/test-cli-transformers-app/pages__action_buttons__hideBusinessAction2__decorator__title': '',
                '@sage/test-cli-transformers-app/pages__action_buttons__disableSaveCrudAction__decorator__title': '',
            };
            const expectedBaseFile = {
                '@sage/test-cli-transformers-app/pages__action_buttons__businessAction1__decorator__onClick':
                    'Business action 1',
                '@sage/test-cli-transformers-app/pages__action_buttons__businessAction1__decorator__title':
                    'Business action 1',
                '@sage/test-cli-transformers-app/pages__action_buttons__businessAction2__decorator__onClick':
                    'Business action 2',
                '@sage/test-cli-transformers-app/pages__action_buttons__businessAction2__decorator__title':
                    'Business action 2',
                '@sage/test-cli-transformers-app/pages__action_buttons__closeCrudAction__decorator__onClick':
                    'Close CRUD Button',
                '@sage/test-cli-transformers-app/pages__action_buttons__controlBlock__decorator__title': '',
                '@sage/test-cli-transformers-app/pages__action_buttons__controlSection__decorator__title':
                    'Controlling Buttons',
                '@sage/test-cli-transformers-app/pages__action_buttons__createCrudAction__decorator__onClick':
                    'Create CRUD Button',
                '@sage/test-cli-transformers-app/pages__action_buttons__createCrudAction__decorator__onClick__2':
                    'Second string literal',
                '@sage/test-cli-transformers-app/pages__action_buttons__createCrudAction__decorator__onClick__3':
                    'Third string literal',
                '@sage/test-cli-transformers-app/pages__action_buttons__decorator__onLoad':
                    'This page show cases the CRUD and business actions.\\nThe actions are located outside the page body, on the title line and on the right-side panel.',
                '@sage/test-cli-transformers-app/pages__action_buttons__decorator__title': 'Action Buttons',
                '@sage/test-cli-transformers-app/pages__action_buttons__deleteCrudAction__decorator__onClick':
                    'Delete CRUD Button',
                '@sage/test-cli-transformers-app/pages__action_buttons__disableBusinessAction1__decorator__title':
                    'Toggle Business Action 1 disabled',
                '@sage/test-cli-transformers-app/pages__action_buttons__disableSaveCrudAction__decorator__title':
                    'Toggle Save CRUD action disabled',
                '@sage/test-cli-transformers-app/pages__action_buttons__hideBusinessAction2__decorator__title':
                    'Toggle Business Action 1 hidden',
                '@sage/test-cli-transformers-app/pages__action_buttons__mainBlock__decorator__title': '',
                '@sage/test-cli-transformers-app/pages__action_buttons__mainSection__decorator__title':
                    'About this page',
                '@sage/test-cli-transformers-app/pages__action_buttons__resultField__decorator__title':
                    'Selected action',
                '@sage/test-cli-transformers-app/pages__action_buttons__saveCrudAction__decorator__onClick':
                    'Save CRUD Button',
            };
            checkLanguageFilesUpdate(dictionary, expectedBaseFile, expectedLanguageFile, writeFileStub);
        });

        it('should delete orphan keys from language files', () => {
            dictionary = supportedLocales.reduce(
                (r, l) => {
                    r[l] = l === 'base' ? {} : { hello: 'world' };
                    return r;
                },
                {} as Dict<Dict<string>>,
            );

            transpileFixture(FIXTURE_PAGE_DECORATED, [mt.messageTransformer]);
            expect(readFileStub.callCount).to.equal(14);
            expect(writeFileStub.callCount).to.equal(12);
            const expectedLanguageFile = {
                '@sage/test-cli-transformers-app/pages__action_buttons__decorator__title': '',
                '@sage/test-cli-transformers-app/pages__action_buttons__decorator__onLoad': '',
                '@sage/test-cli-transformers-app/pages__action_buttons__saveCrudAction__decorator__onClick': '',
                '@sage/test-cli-transformers-app/pages__action_buttons__createCrudAction__decorator__onClick': '',
                '@sage/test-cli-transformers-app/pages__action_buttons__createCrudAction__decorator__onClick__2': '',
                '@sage/test-cli-transformers-app/pages__action_buttons__createCrudAction__decorator__onClick__3': '',
                '@sage/test-cli-transformers-app/pages__action_buttons__deleteCrudAction__decorator__onClick': '',
                '@sage/test-cli-transformers-app/pages__action_buttons__closeCrudAction__decorator__onClick': '',
                '@sage/test-cli-transformers-app/pages__action_buttons__businessAction1__decorator__title': '',
                '@sage/test-cli-transformers-app/pages__action_buttons__businessAction1__decorator__onClick': '',
                '@sage/test-cli-transformers-app/pages__action_buttons__businessAction2__decorator__title': '',
                '@sage/test-cli-transformers-app/pages__action_buttons__businessAction2__decorator__onClick': '',
                '@sage/test-cli-transformers-app/pages__action_buttons__mainSection__decorator__title': '',
                '@sage/test-cli-transformers-app/pages__action_buttons__mainBlock__decorator__title': '',
                '@sage/test-cli-transformers-app/pages__action_buttons__resultField__decorator__title': '',
                '@sage/test-cli-transformers-app/pages__action_buttons__controlSection__decorator__title': '',
                '@sage/test-cli-transformers-app/pages__action_buttons__controlBlock__decorator__title': '',
                '@sage/test-cli-transformers-app/pages__action_buttons__disableBusinessAction1__decorator__title': '',
                '@sage/test-cli-transformers-app/pages__action_buttons__hideBusinessAction2__decorator__title': '',
                '@sage/test-cli-transformers-app/pages__action_buttons__disableSaveCrudAction__decorator__title': '',
            };
            const expectedBaseFile = {
                '@sage/test-cli-transformers-app/pages__action_buttons__businessAction1__decorator__onClick':
                    'Business action 1',
                '@sage/test-cli-transformers-app/pages__action_buttons__businessAction1__decorator__title':
                    'Business action 1',
                '@sage/test-cli-transformers-app/pages__action_buttons__businessAction2__decorator__onClick':
                    'Business action 2',
                '@sage/test-cli-transformers-app/pages__action_buttons__businessAction2__decorator__title':
                    'Business action 2',
                '@sage/test-cli-transformers-app/pages__action_buttons__closeCrudAction__decorator__onClick':
                    'Close CRUD Button',
                '@sage/test-cli-transformers-app/pages__action_buttons__controlBlock__decorator__title': '',
                '@sage/test-cli-transformers-app/pages__action_buttons__controlSection__decorator__title':
                    'Controlling Buttons',
                '@sage/test-cli-transformers-app/pages__action_buttons__createCrudAction__decorator__onClick':
                    'Create CRUD Button',
                '@sage/test-cli-transformers-app/pages__action_buttons__createCrudAction__decorator__onClick__2':
                    'Second string literal',
                '@sage/test-cli-transformers-app/pages__action_buttons__createCrudAction__decorator__onClick__3':
                    'Third string literal',
                '@sage/test-cli-transformers-app/pages__action_buttons__decorator__onLoad':
                    'This page show cases the CRUD and business actions.\\nThe actions are located outside the page body, on the title line and on the right-side panel.',
                '@sage/test-cli-transformers-app/pages__action_buttons__decorator__title': 'Action Buttons',
                '@sage/test-cli-transformers-app/pages__action_buttons__deleteCrudAction__decorator__onClick':
                    'Delete CRUD Button',
                '@sage/test-cli-transformers-app/pages__action_buttons__disableBusinessAction1__decorator__title':
                    'Toggle Business Action 1 disabled',
                '@sage/test-cli-transformers-app/pages__action_buttons__disableSaveCrudAction__decorator__title':
                    'Toggle Save CRUD action disabled',
                '@sage/test-cli-transformers-app/pages__action_buttons__hideBusinessAction2__decorator__title':
                    'Toggle Business Action 1 hidden',
                '@sage/test-cli-transformers-app/pages__action_buttons__mainBlock__decorator__title': '',
                '@sage/test-cli-transformers-app/pages__action_buttons__mainSection__decorator__title':
                    'About this page',
                '@sage/test-cli-transformers-app/pages__action_buttons__resultField__decorator__title':
                    'Selected action',
                '@sage/test-cli-transformers-app/pages__action_buttons__saveCrudAction__decorator__onClick':
                    'Save CRUD Button',
            };
            checkLanguageFilesUpdate(dictionary, expectedBaseFile, expectedLanguageFile, writeFileStub);
        });
    });
});
