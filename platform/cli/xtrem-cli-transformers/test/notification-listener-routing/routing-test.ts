import { assert } from 'chai';
import * as routing from '../../lib';

describe('Notification listener routing', () => {
    describe('Merge routing data', () => {
        it('No common packageNames', () => {
            const routing1: routing.Routing = {
                packageName1: [{ queue: 'queue1', topic: 'topic1', sourceFileName: __filename }],
            };
            const routing2: routing.Routing = {
                packageName2: [{ queue: 'queue2', topic: 'topic2', sourceFileName: __filename }],
            };
            const mergedRouting = routing.mergeRoutings(routing1, routing2);
            assert.deepEqual(mergedRouting, {
                packageName1: [{ queue: 'queue1', topic: 'topic1', sourceFileName: __filename }],
                packageName2: [{ queue: 'queue2', topic: 'topic2', sourceFileName: __filename }],
            });
        });

        it('Common packageNames', () => {
            const routing1: routing.Routing = {
                packageName1: [{ queue: 'queue1', topic: 'topic1', sourceFileName: __filename }],
            };
            const routing2: routing.Routing = {
                packageName1: [{ queue: 'queue2', topic: 'topic2', sourceFileName: __filename }],
                packageName2: [{ queue: 'queue3', topic: 'topic3', sourceFileName: __filename }],
            };
            const mergedRouting = routing.mergeRoutings(routing1, routing2);
            assert.deepEqual(mergedRouting, {
                packageName1: [
                    { queue: 'queue1', topic: 'topic1', sourceFileName: __filename },
                    { queue: 'queue2', topic: 'topic2', sourceFileName: __filename },
                ],
                packageName2: [{ queue: 'queue3', topic: 'topic3', sourceFileName: __filename }],
            });
        });

        it('Common packageNames with duplicate values', () => {
            const routing1: routing.Routing = {
                packageName1: [{ queue: 'queue1', topic: 'topic1', sourceFileName: __filename }],
            };
            const routing2: routing.Routing = {
                packageName1: [
                    { queue: 'queue1', topic: 'topic1', sourceFileName: __filename },
                    { queue: 'queue2', topic: 'topic2', sourceFileName: __filename },
                ],
                packageName2: [{ queue: 'queue3', topic: 'topic3', sourceFileName: __filename }],
            };
            const mergedRouting = routing.mergeRoutings(routing1, routing2);
            assert.deepEqual(mergedRouting, {
                packageName1: [
                    { queue: 'queue1', topic: 'topic1', sourceFileName: __filename },
                    { queue: 'queue2', topic: 'topic2', sourceFileName: __filename },
                ],
                packageName2: [{ queue: 'queue3', topic: 'topic3', sourceFileName: __filename }],
            });
        });
    });
});
