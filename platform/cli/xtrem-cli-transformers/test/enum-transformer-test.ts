import { expect } from 'chai';
import * as path from 'path';
import * as sinon from 'sinon';
import * as et from '../lib/transformers/enum-transformer';
import * as transformerUtils from '../lib/transformers/transformer-utils';
import { transpileFixture } from './test-helper';

const FIXTURE_ENUM = path.resolve('./test/fixtures/test-app/lib/enums/saleable-product-line-origin.ts');
const FIXTURE_ENUM_INDEX = path.resolve('./test/fixtures/test-app/lib/enums/_index.ts');
const FIXTURE_NODE = path.resolve('./test/fixtures/test-app/lib/nodes/address.ts');

describe('Enum Transformer', () => {
    let writeLiteralsStub: sinon.SinonStub<any>;

    beforeEach(() => {
        writeLiteralsStub = sinon.stub(transformerUtils, 'writeStringLiteralsToBase');
    });

    afterEach(() => {
        writeLiteralsStub.restore();
    });

    describe('execution', () => {
        it('should exclude node files', () => {
            transpileFixture(FIXTURE_NODE, [et.enumTransformer]);
            expect(writeLiteralsStub.getCalls().length).to.be.equal(0);
        });

        it('should exclude index files inside pages', () => {
            transpileFixture(FIXTURE_ENUM_INDEX, [et.enumTransformer]);
            expect(writeLiteralsStub.getCalls().length).to.be.equal(0);
        });
    });

    describe('enums', () => {
        it('should extract enum members', () => {
            transpileFixture(FIXTURE_ENUM, [et.enumTransformer]);
            expect(writeLiteralsStub.getCalls().length).to.be.equal(1);

            const call = writeLiteralsStub.getCall(0);
            const dictionary = call.args[0];
            expect(dictionary).to.be.instanceOf(Object);
            expect(Object.keys(dictionary).length).to.eq(4);
            expect(dictionary['@sage/test-cli-transformers-app/enums__saleable_product_line_origin__manual']).to.be.eq(
                'Manual',
            );
            expect(
                dictionary['@sage/test-cli-transformers-app/enums__saleable_product_line_origin__operations'],
            ).to.be.eq('Operations');
            expect(
                dictionary['@sage/test-cli-transformers-app/enums__saleable_product_line_origin__productRequirements'],
            ).to.be.eq('Product requirements');
            expect(
                dictionary['@sage/test-cli-transformers-app/enums__saleable_product_line_origin__budgetLines'],
            ).to.be.eq('Budget lines');
        });
    });
});
