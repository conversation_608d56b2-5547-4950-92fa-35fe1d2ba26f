import { expect } from 'chai';
import * as path from 'path';
import * as sinon from 'sinon';
import * as dt from '../lib/transformers/decorator-transformer';
import * as transformerUtils from '../lib/transformers/transformer-utils';
import { transpileFixture } from './test-helper';

const FIXTURE_PAGE = path.resolve('./test/fixtures/test-app/lib/pages/action-buttons.ts');
const FIXTURE_NODE_INDEX = path.resolve('./test/fixtures/test-app/lib/nodes/_index.ts');
const FIXTURE_NODE = path.resolve('./test/fixtures/test-app/lib/nodes/address.ts');
const FIXTURE_PAGE_INDEX = path.resolve('./test/fixtures/test-app/lib/pages/index.ts');

describe('Decorator Transformer', () => {
    let visitorStub: sinon.SinonStub<any>;
    let writeLiteralsStub: sinon.SinonStub<any>;

    beforeEach(() => {
        writeLiteralsStub = sinon.stub(transformerUtils, 'writeStringLiteralsToBase');
    });

    afterEach(() => {
        writeLiteralsStub.restore();
    });

    describe('execution', () => {
        beforeEach(() => {
            visitorStub = sinon.stub(dt, 'visitor');
        });

        afterEach(() => {
            visitorStub.restore();
        });

        it('should exclude node files', () => {
            transpileFixture(FIXTURE_NODE_INDEX, [dt.decoratorTransformer]);
            transpileFixture(FIXTURE_NODE, [dt.decoratorTransformer]);
            expect(visitorStub.getCalls().length).to.be.equal(0);
        });

        it('should exclude index files inside pages', () => {
            transpileFixture(FIXTURE_PAGE_INDEX, [dt.decoratorTransformer]);
            expect(visitorStub.getCalls().length).to.be.equal(0);
        });
    });

    describe('pages', () => {
        it('should wrap strings and template literals', () => {
            const result = transpileFixture(FIXTURE_PAGE, [dt.decoratorTransformer]);
            expect(result).to.contain(
                'ui.localize("@sage/test-cli-transformers-app/pages__action_buttons____title", "Action Buttons")',
            );
            expect(result).to.contain(
                'ui.localize("@sage/test-cli-transformers-app/pages__action_buttons__businessAction1____title", "Business action 1")',
            );
            expect(result).to.contain(
                'ui.localize("@sage/test-cli-transformers-app/pages__action_buttons__businessAction2____title", "Business action 2")',
            );
            expect(result).to.contain(
                'ui.localize("@sage/test-cli-transformers-app/pages__action_buttons__mainSection____title", "About this page")',
            );
            expect(result).to.contain(
                'ui.localize("@sage/test-cli-transformers-app/pages__action_buttons__mainBlock____title", "")',
            );
            expect(result).to.contain(
                'ui.localize("@sage/test-cli-transformers-app/pages__action_buttons__resultField____title", "Selected action")',
            );
            expect(result).to.contain(
                'ui.localize("@sage/test-cli-transformers-app/pages__action_buttons__resultField____helperText", "I am helping here")',
            );
            expect(result).to.contain(
                'ui.localize("@sage/test-cli-transformers-app/pages__action_buttons__controlSection____title", "Controlling Buttons")',
            );
            expect(result).to.contain(
                'ui.localize("@sage/test-cli-transformers-app/pages__action_buttons__controlBlock____title", "")',
            );
            expect(result).to.contain(
                'ui.localize("@sage/test-cli-transformers-app/pages__action_buttons__disableBusinessAction1____title", "Toggle Business Action 1 disabled")',
            );
            expect(result).to.contain(
                'ui.localize("@sage/test-cli-transformers-app/pages__action_buttons__hideBusinessAction2____title", "Toggle Business Action 1 hidden")',
            );
            expect(result).to.contain(
                'ui.localize("@sage/test-cli-transformers-app/pages__action_buttons__disableSaveCrudAction____title", "Toggle Save CRUD action disabled")',
            );
        });

        it('should not remove excluded properties', () => {
            const result = transpileFixture(FIXTURE_PAGE, [dt.decoratorTransformer]);
            expect(result).to.contain("authorizationCode: 'ACTINBTNS',");
        });
    });
});
