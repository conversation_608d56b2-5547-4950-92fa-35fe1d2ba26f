import * as ui from '@sage/xtrem-ui';

@ui.decorators.page<ActionButtons>({
    authorizationCode: 'ACTINBTNS',
    title: ui.localize('@sage/test-cli-transformers-app/pages__action_buttons__decorator__title', 'Action Buttons'),
    isTransient: true,
    createAction() {
        return this.createCrudAction;
    },
    headerDropDownActions() {
        return [this.deleteCrudAction];
    },
    businessActions() {
        return [this.businessAction1, this.businessAction2, this.saveCrudAction];
    },
    onLoad() {
        ui.localizeEnumMember('@sage/test-cli-transformers-app/MyTestEnum', 'test1');
        this.descriptionField.value = ui.localize(
            '@sage/test-cli-transformers-app/pages__action_buttons__decorator__onLoad',
            'This page show cases the CRUD and business actions.\\nThe actions are located outside the page body, on the title line and on the right-side panel.',
        );
    },
})
export class ActionButtons extends ui.Page {
    @ui.decorators.pageAction<ActionButtons>({
        onClick() {
            this.resultField.value = ui.localize(
                '@sage/test-cli-transformers-app/pages__action_buttons__saveCrudAction__decorator__onClick',
                'Save CRUD Button',
            );
        },
    })
    saveCrudAction: ui.PageAction;

    @ui.decorators.pageAction<ActionButtons>({
        onClick() {
            this.resultField.value = ui.localize(
                '@sage/test-cli-transformers-app/pages__action_buttons__createCrudAction__decorator__onClick',
                'Create CRUD Button',
            );
            this.resultField.value = ui.localize(
                '@sage/test-cli-transformers-app/pages__action_buttons__createCrudAction__decorator__onClick__2',
                'Second string literal',
            );
            this.resultField.value = ui.localize(
                '@sage/test-cli-transformers-app/pages__action_buttons__createCrudAction__decorator__onClick__3',
                'Third string literal',
            );
        },
    })
    createCrudAction: ui.PageAction;

    @ui.decorators.pageAction<ActionButtons>({
        onClick() {
            this.resultField.value = ui.localize(
                '@sage/test-cli-transformers-app/pages__action_buttons__deleteCrudAction__decorator__onClick',
                'Delete CRUD Button',
            );
        },
        isHidden: true,
    })
    deleteCrudAction: ui.PageAction;

    @ui.decorators.pageAction<ActionButtons>({
        onClick() {
            this.resultField.value = ui.localize(
                '@sage/test-cli-transformers-app/pages__action_buttons__closeCrudAction__decorator__onClick',
                'Close CRUD Button',
            );
        },
    })
    closeCrudAction: ui.PageAction;

    @ui.decorators.pageAction<ActionButtons>({
        title: ui.localize(
            '@sage/test-cli-transformers-app/pages__action_buttons__businessAction1__decorator__title',
            'Business action 1',
        ),
        onClick() {
            this.resultField.value = ui.localize(
                '@sage/test-cli-transformers-app/pages__action_buttons__businessAction1__decorator__onClick',
                'Business action 1',
            );
        },
    })
    businessAction1: ui.PageAction;

    @ui.decorators.pageAction<ActionButtons>({
        title: ui.localize(
            '@sage/test-cli-transformers-app/pages__action_buttons__businessAction2__decorator__title',
            'Business action 2',
        ),
        onClick() {
            this.resultField.value = ui.localize(
                '@sage/test-cli-transformers-app/pages__action_buttons__businessAction2__decorator__onClick',
                'Business action 2',
            );
        },
    })
    businessAction2: ui.PageAction;

    @ui.decorators.section<ActionButtons>({
        title: ui.localize(
            '@sage/test-cli-transformers-app/pages__action_buttons__mainSection__decorator__title',
            'About this page',
        ),
    })
    mainSection: ui.containers.Section;

    @ui.decorators.block<ActionButtons>({
        parent() {
            return this.mainSection;
        },
        title: ui.localize('@sage/test-cli-transformers-app/pages__action_buttons__mainBlock__decorator__title', ''),
    })
    mainBlock: ui.containers.Block;

    @ui.decorators.textAreaField<ActionButtons>({
        parent() {
            return this.mainBlock;
        },
        isReadOnly: true,
        isFullWidth: true,
        rows: 4,
    })
    descriptionField: ui.fields.TextArea;

    @ui.decorators.textField<ActionButtons>({
        title: ui.localize(
            '@sage/test-cli-transformers-app/pages__action_buttons__resultField__decorator__title',
            'Selected action',
        ),
        parent() {
            return this.mainBlock;
        },
        isReadOnly: true,
    })
    resultField: ui.fields.Text;

    @ui.decorators.section<ActionButtons>({
        title: ui.localize(
            '@sage/test-cli-transformers-app/pages__action_buttons__controlSection__decorator__title',
            'Controlling Buttons',
        ),
    })
    controlSection: ui.containers.Section;

    @ui.decorators.block<ActionButtons>({
        parent() {
            return this.controlSection;
        },
        title: ui.localize('@sage/test-cli-transformers-app/pages__action_buttons__controlBlock__decorator__title', ''),
    })
    controlBlock: ui.containers.Block;

    @ui.decorators.buttonField<ActionButtons>({
        title: ui.localize(
            '@sage/test-cli-transformers-app/pages__action_buttons__disableBusinessAction1__decorator__title',
            'Toggle Business Action 1 disabled',
        ),
        map() {
            return 'Click me';
        },
        parent() {
            return this.controlBlock;
        },
        onClick() {
            this.businessAction1.isDisabled = !this.businessAction1.isDisabled;
        },
    })
    disableBusinessAction1: ui.fields.Button;

    @ui.decorators.buttonField<ActionButtons>({
        title: ui.localize(
            '@sage/test-cli-transformers-app/pages__action_buttons__hideBusinessAction2__decorator__title',
            'Toggle Business Action 1 hidden',
        ),
        map() {
            return 'Click me';
        },
        parent() {
            return this.controlBlock;
        },
        onClick() {
            this.businessAction2.isHidden = !this.businessAction2.isHidden;
        },
    })
    hideBusinessAction2: ui.fields.Button;

    @ui.decorators.buttonField<ActionButtons>({
        title: ui.localize(
            '@sage/test-cli-transformers-app/pages__action_buttons__disableSaveCrudAction__decorator__title',
            'Toggle Save CRUD action disabled',
        ),
        map() {
            return 'Click me';
        },
        parent() {
            return this.controlBlock;
        },
        onClick() {
            this.saveCrudAction.isDisabled = !this.saveCrudAction.isDisabled;
        },
    })
    disableSaveCrudAction: ui.fields.Button;

    @ui.decorators.selectField<ActionButtons>({
        optionType: '@sage/xtrem-i18n-app/EnumUsedAsOption',
        parent() {
            return this.controlBlock;
        },
        onClick() {
            this.saveCrudAction.isDisabled = !this.saveCrudAction.isDisabled;
        },
    })
    sampleSelect: ui.fields.Button;
}
