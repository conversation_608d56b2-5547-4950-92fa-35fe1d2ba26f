{"compilerOptions": {"experimentalDecorators": true, "outDir": "build", "rootDir": ".", "baseUrl": "."}, "include": ["index.ts", "application.ts", "lib/**/*", "lib-generated/**/*", "api/api.d.ts", "prepublish.ts"], "exclude": ["lib/pages/**/*", "lib/widgets/**/*", "lib/stickers/**/*", "**/*.feature", "**/*.png"], "references": [{"path": "../../../../../back-end/xtrem-core"}, {"path": "../../../../../front-end/xtrem-ui"}, {"path": "api"}]}