import { expect } from 'chai';
import * as path from 'path';
import * as sinon from 'sinon';
import * as mi from '../lib/transformers/menu-item-transformer';
import { transpileFixtures } from './test-helper';

const FIXTURE_PAGE = path.resolve('./test/fixtures/test-app/lib/pages/action-buttons.ts');
const FIXTURE_MENU_ITEMS = path.resolve('./test/fixtures/test-app/lib/menu-items/show-case.ts');

// eslint-disable-next-line func-names
describe('Menu Item Transformer', function () {
    // Set a timeout for the entire suite (investigate why it's so slow: can be more than 3mn)
    this.timeout(300000); // 5 minutes

    let visitorSpy: sinon.SinonSpy<any>;

    beforeEach(() => {
        visitorSpy = sinon.spy(mi, 'visitor');
    });

    afterEach(() => {
        visitorSpy.restore();
    });

    describe('test menuItemTransformer', () => {
        it('make sure visitor spy is not null when visiting action-buttons.ts', () => {
            transpileFixtures([FIXTURE_PAGE], [mi.menuItemTransformer]);
            expect(visitorSpy.getCalls().length).to.be.equal(0);
        });

        it('make sure visitor spy is not null when visiting show-case.ts', () => {
            transpileFixtures([FIXTURE_MENU_ITEMS], [mi.menuItemTransformer]);
            expect(visitorSpy.getCalls().length).to.be.equal(28);
        });
    });
});
