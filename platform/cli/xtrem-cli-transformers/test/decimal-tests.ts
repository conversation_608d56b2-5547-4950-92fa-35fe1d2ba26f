import { DateValue } from '@sage/xtrem-date-time';
import { Decimal } from '@sage/xtrem-decimal';
import { assert } from 'chai';

function toString(v: any): number {
    return v !== undefined ? v.toString() : v;
}

const mapArguments = (a: any) => Object.keys(a).reduce((r, k) => ((r[k] = toString(r[k])), r), a);

const assertEqual = assert.equal;
assert.equal = function (a: any, b: any, message?: string) {
    assertEqual.apply(assert, mapArguments(arguments));
};

const assertStrictEqual = assert.strictEqual;
assert.strictEqual = function (a: any, b: any, message?: string) {
    assertStrictEqual.apply(assert, mapArguments(arguments));
};

describe('Instantiation', function () {
    it('Instantiation', function () {
        assert.strictEqual(Decimal.make('123.456'), <any>123.456);
    });
});

describe('Literals', function () {
    it('literals', function () {
        let val = 0.1;
        assert.strictEqual(1, 1);
        assert.strictEqual(0.1, 0.1);
        assert.strictEqual(val, 0.1);
        assert.strictEqual(0.1, val);
        assert.equal(val, 0.1);
        assert.equal(0.1, val);
        val = -0.1;
        assert.strictEqual(-0.1, -0.1);
        assert.strictEqual(val, -0.1);
        assert.strictEqual(-0.1, val);
        val = 12345678901234567890.123456789012;
        assert.strictEqual(val, 12345678901234567890.123456789012);
        assert.strictEqual(12345678901234567890.123456789012, val);
        assert.strictEqual(12345678901234567890.123456789012, 12345678901234567890.123456789012);

        let val2 = 1;
        assert.strictEqual(val2, 1);
        val2 = -1;
        assert.strictEqual(val2, -1);
    });
    it('literal is Decimal', function () {
        assert.ok(Decimal.isDecimal(Decimal.make(123.456)));
        assert.ok(Decimal.isDecimal(Decimal.make('123.456')));
        assert.ok(Decimal.isDecimal(123.456));
        assert.ok(!Decimal.isDecimal(1));
        assert.ok(!Decimal.isDecimal(+1));
        assert.ok(!Decimal.isDecimal(-1));
        assert.ok(!Decimal.isDecimal(1e10));
        assert.ok(!Decimal.isDecimal(1e-10));
        assert.ok(!Decimal.isDecimal(+1e10));
        assert.ok(!Decimal.isDecimal(-1e10));
    });
});

describe('Add', function () {
    it('basic additions', function () {
        const val1 = 0.1;
        const val2 = 0.2;
        let val3 = val1 + val2;
        val3 = 0.1 + 0.2;
        assert.strictEqual(val3, 0.3);
        const val4 = val1 + val2 + val3;
        assert.strictEqual(val4, 0.6);
        const val5 = 0.4 + val4;
        assert.strictEqual(val5, 1);
        const val6 = 1 + val5;
        assert.strictEqual(val6, 2);
        const val7 = val6 + 1;
        assert.strictEqual(val7, 3);

        assert.strictEqual(0.1 + 0.2, 0.3);
        assert.strictEqual(1 + 0.3, 1.3);
        assert.strictEqual(0.3 + 1, 1.3);

        assert.strictEqual(0.000000000000001 + 0.000000000000002, 0.000000000000003);
        assert.strictEqual(-0.000000000000001 + -0.000000000000002, -0.000000000000003);
        assert.strictEqual(0 + 0, 0);
        assert.strictEqual(0 + 1, 1);
        assert.strictEqual(0 + -1, -1);

        const val8 = 123;
        const val9 = 456;
        const val10 = `${val8}.${val9}`;
        const val11 = 0.0 + val10;
        assert.strictEqual(val11, '0123.456');
        assert.strictEqual('0' + 123.456, '0123.456');

        const s1 = 's1';
        const s2 = 's2';
        assert.strictEqual(s1 + s2, 's1s2');
    });
});

describe('Subtract', function () {
    it('basic subtractions', function () {
        const val1 = 0.2;
        const val2 = 0.1;
        const val3 = val1 - val2;
        assert.strictEqual(val3, 0.1);
        const val4 = val2 - val1;
        assert.strictEqual(val4, -0.1);

        assert.strictEqual(0.4 - 0.1, 0.3);
        assert.strictEqual(1 - 0.7, 0.3);
        assert.strictEqual(1.3 - 1, 0.3);
        assert.strictEqual(0.0 - <any>'123.456', -123.456);
        assert.strictEqual(0 - <any>'123.456', -123.456);
        assert.strictEqual(<any>'123.456' - 3.4, 120.056);
        assert.strictEqual(<any>'123.456' - 3, 120.456);
    });
});

describe('Multiply', function () {
    it('basic multiplications', function () {
        const val1 = 0.2;
        const val2 = 0.3;
        const val3 = val1 * val2;
        assert.strictEqual(val3, 0.06);

        assert.strictEqual(0.2 * 0.3, 0.06);
        assert.strictEqual(2 * 0.3, 0.6);
        assert.strictEqual(0.3 * 2, 0.6);
        assert.strictEqual(10 * 0.123456789012, 1.23456789012);
        assert.strictEqual(0.123456789012 * 10, 1.23456789012);
        assert.strictEqual(10 * 0.1234567890123, 1.234567890123);
        assert.strictEqual(0.1234567890123 * 10, 1.234567890123);
        assert.strictEqual(10 * 0.12345678901234, 1.2345678901234);
        assert.strictEqual(0.12345678901234 * 10, 1.2345678901234);
        assert.strictEqual(2.0 * <any>'123.456', 246.912);
        assert.strictEqual(2 * <any>'123.456', 246.912);

        const val4 = 10;
        assert.strictEqual(4 * val4, 40);
    });
});

describe('Divide', function () {
    it('basic divisions', function () {
        const val1 = 0.6;
        const val2 = 0.2;
        const val3 = val1 / val2;
        assert.strictEqual(val3, 3);

        assert.strictEqual(0.6 / 0.2, 3);
        assert.strictEqual(0.6 / 2, 0.3);
        assert.strictEqual(6 / 0.6, 10);
        assert.strictEqual(12.0 / <any>'2.5', 4.8);
        assert.strictEqual(12 / <any>'2.5', 4.8);
    });
});

describe('tests on compound assignment operators', function () {
    it('tests on compound assignment operators', function () {
        let val1 = 0.8;
        assert.strictEqual(val1, 0.8);
        val1 += 1;
        assert.strictEqual(val1, 1.8);
        val1 /= 2;
        assert.strictEqual(val1, 0.9);
        val1 *= 2;
        assert.strictEqual(val1, 1.8);

        let d = 1;
        d += 2;
        assert.ok(!Decimal.isDecimal(d));
        assert.strictEqual(d, 3);
        d -= 3;
        assert.ok(!Decimal.isDecimal(d));
        assert.strictEqual(d, 0);
    });
});

describe('tests on unary expressions', function () {
    it('tests on unary expressions', function () {
        const val2 = '1';
        assert.strictEqual(+val2, 1);
        const val3 = '1';
        assert.strictEqual(1, +val3);

        const val4 = 1;
        assert.strictEqual(+val4, 1);
        const val5 = 1;
        assert.strictEqual(1, +val5);
    });
});

describe('conditions', function () {
    it('basic conditions', function () {
        const val1 = 0.1;
        const val2 = 0.2;
        assert.equal(val1, val1);
        assert.strictEqual(val1, val1);
        assert.equal(val1, 0.1);
        assert.strictEqual(val1, 0.1);
        assert.strictEqual(0.1, val1);
        // tslint:disable-next-line
        assert.notStrictEqual(1.0, val1);
        // tslint:disable-next-line
        assert.notStrictEqual(val1, 1.0);
        // tslint:disable-next-line
        assert.notStrictEqual(0, 0.0);
        assert.notStrictEqual(1, 1.0);
        // tslint:disable-next-line
        assert.equal(<any>'0', 0.0);
        // tslint:disable-next-line
        assert.equal(<any>'1', 1.0);

        // tslint:disable-next-line
        assert.notOk(0.1 != val1);
        assert.notOk(0.1 !== val1);
        assert.notOk(val1 !== val1);
        assert.ok(val1 < val2);
        assert.ok(val1 <= val2);
        assert.notOk(val1 > val2);
        assert.notOk(val1 >= val2);
        assert.ok(0.1 < 0.2);
        assert.notOk(0.2 < 0.1);

        assert.equal(0, 0);
        assert.equal(0, <any>'0');
        assert.equal(<any>'0', 0.0);
        assert.equal(0, 0.0);
        assert.ok(0 === 0);
        assert.equal(0, 0.0);
        assert.ok(0 === 0.0);
        assert.equal(0.0, 0.0);
        assert.ok(0.0 === 0.0);

        assert.notEqual(0, 1);
        assert.notOk(<any>0 === '0');
        assert.notEqual(0, 1.0);
        assert.notOk(<any>0 === 1.0);
        assert.notEqual(0.0, 1);
        assert.notOk(0.0 === <any>1);
        assert.notEqual(0.0, 1.0);
        assert.notOk(<any>0.0 === 1.0);

        const one = 1.0;
        assert.equal(1, 1);
        assert.equal(1, <any>'1');
        assert.ok(1 === 1);
        assert.equal(1, 1.0);
        assert.ok(1 === 1.0);
        assert.equal(1, one);
        assert.ok(1 === one);
        assert.equal(1, ((<any>one) as Decimal).toNumber());
        assert.ok(1 === ((<any>one) as Decimal).toNumber());
        assert.equal(1.0, 1.0);
        assert.ok(1.0 === 1.0);

        assert.notEqual(1, 3);
        assert.notOk(<any>1 === 3);
        assert.notEqual(1, 3.0);
        assert.notOk(<any>1 === 3.0);
        assert.notEqual(1.0, 3.0);
        assert.notOk(<any>1.0 === 3.0);

        assert.equal(3, 3);
        assert.equal(3, <any>'3');
        assert.ok(3 === 3);
        assert.equal(3.0, 3);
        assert.ok(3.0 === 3);
        assert.equal(3.0, 3.0);
        assert.ok(3.0 === 3.0);

        assert.notEqual(4, 3);
        assert.notOk(<any>4 === 3);
        assert.notEqual(4, <any>'3');
        assert.notOk(4 === <any>'3');
        assert.notEqual(<any>4, 3.0);
        assert.notOk(<any>4 === 3.0);
        assert.notEqual(4, <any>'3.0');
        assert.notEqual(4.0, 3.0);
        assert.notOk(<any>4.0 === 3.0);

        assert.ok(1 < 2);
        assert.ok(1 < 2.0);
        assert.ok(1.0 < 2.0);

        assert.notOk(2 < 1);
        assert.notOk(2 < 1.0);
        assert.notOk(2.0 < 1.0);
        assert.ok(1 <= 2);
        assert.ok(1 <= 2.0);
        assert.ok(1.0 <= 2.0);
        assert.ok(1 <= 1);
        assert.ok(1 <= 1.0);
        assert.ok(1.0 <= 1.0);
        assert.notOk(2 <= 1);
        assert.notOk(2 <= 1.0);
        assert.notOk(2.0 <= 1.0);
        assert.ok(3 > 2);
        assert.ok(3 > 2.0);
        assert.ok(3.0 > 2.0);
        assert.notOk(2 > 3);
        assert.notOk(2 > 3.0);
        assert.notOk(2.0 > 3.0);
        assert.ok(3 >= 2);
        assert.ok(3 >= 2.0);
        assert.ok(3.0 >= 2.0);
        assert.ok(3 >= 3);
        assert.ok(3 >= 3.0);
        assert.ok(3.0 >= 3.0);
        assert.notOk(2 >= 3);
        assert.notOk(2 >= 3.0);
        assert.notOk(2.0 >= 3.0);

        // tslint:disable-next-line
        let undefined1: number | undefined;
        // tslint:disable-next-line
        let undefined2: number | undefined;
        assert.equal(undefined1, undefined2);
        assert.strictEqual(undefined1, undefined2);
        // tslint:disable-next-line
        assert.notOk(undefined1 != undefined2);
        // tslint:disable-next-line
        assert.notOk(undefined1 !== undefined2);

        const obj = { value: 'string' };
        // tslint:disable-next-line
        assert.ok(obj != undefined);
        assert.ok(obj !== undefined);

        // tslint:disable-next-line
        assert.ok(obj['value'] != undefined);
        assert.ok(obj['value'] != null);
        // tslint:disable-next-line
        assert.ok(undefined != obj['value']);
        assert.ok(null != obj['value']);

        assert.ok(obj['value'] !== undefined);
        assert.ok(undefined !== obj['value']);
        assert.ok(obj['value'] !== null);
        assert.ok(null !== obj['value']);
    });
});

describe('Objects', function () {
    it('tests with Objects', function () {
        const val = { value: 0.1 };
        assert.strictEqual(val.value, 0.1);
    });
});

describe('unary expressions', function () {
    it('prefix unary expressions block 1', function () {
        let val1 = 1.8;
        let val2 = 0;
        val2 = ++val1;
        assert.strictEqual(val2, 2.8);
        assert.strictEqual(val1, 2.8);

        val2 = --val1;
        assert.strictEqual(val2, 1.8);
        assert.strictEqual(val1, 1.8);

        let dec = 1.0;
        assert.ok(Decimal.isDecimal(dec));
        dec = ((<any>dec) as Decimal).toNumber();
        assert.ok(typeof dec === 'number');
        assert.notOk(Decimal.isDecimal(dec));
        dec = +dec;
        assert.ok(typeof dec === 'number');
        assert.notOk(Decimal.isDecimal(dec));
        dec = -dec;
        assert.ok(typeof dec === 'number');
        assert.notOk(Decimal.isDecimal(dec));
    });

    it('postfix unary expressions block 1', function () {
        let val1 = 1.8;
        let val2 = 0;

        val2 = val1++;
        assert.strictEqual(val2, 1.8);
        assert.strictEqual(val1, 2.8);

        val2 = val1--;
        assert.strictEqual(val2, 2.8);
        assert.strictEqual(val1, 1.8);
    });

    it('postfix unary expressions block 2', function () {
        let val1 = 1.8;
        let val2 = 0;
        val2 = val1++;
        assert.strictEqual(val2, 1.8);
        assert.strictEqual(val1, 2.8);

        val2 = val1--;
        assert.strictEqual(val2, 2.8);
        assert.strictEqual(val1, 1.8);
    });
});

function round(value: number, decimals: number) {
    const factor = 1 / decimals;
    return Math.round(value * factor) / factor;
}

function round2(value: number, decimals: number) {
    const factor = Math.pow(10, decimals);
    return Math.round(value * factor) / factor;
}

describe('Functions', function () {
    const f1 = (_a: any) => _a;
    const f2 = (_a: any, _b: any) => _a + _b;
    const parseNumber = (value: string | DateValue) => {
        if (DateValue.isDate(value)) return value.value;
        return +value;
    };

    it('functions', function () {
        let val1 = 0.1;
        const val2 = 0.2;
        assert.strictEqual(f1(val1), val1);
        val1 = 12345678901234567890.123456789012;
        assert.strictEqual(f1(val1), val1);
        val1 = 0.1;
        assert.strictEqual(f2(val1, val2), 0.3);
    });

    it('dates', function () {
        const year = 2017;
        const val1 = DateValue.make(year + 1, 1, 1);
        assert.strictEqual(val1.year, year + 1);
        assert.strictEqual(val1.year, 2018);
        assert.strictEqual(val1.month, 1);
        assert.strictEqual(val1.day, 1);
        const val2 = val1.addDays(-1);
        assert.strictEqual(val2.year, 2017);
        //const one = 1.0;
        //const val3 = val2.addDays(one);
        //console.log(val2.valueOf());
        //console.log(val3.valueOf());
        //assert.strictEqual(val3.year , 2018);

        const today = DateValue.today();
        // tslint:disable-next-line
        assert.ok(parseNumber(today) != 0);
        assert.ok(parseNumber(today) !== 0);
    });
});

describe('Blocks', function () {
    it('Block if 1', function () {
        let i;
        let first = true;
        for (i = 0; i < 2; i++, first = false) {
            if (first) {
                assert.strictEqual(i, 0);
            } else {
                assert.strictEqual(i, 1);
            }
        }
        assert.strictEqual(i, 2);
    });

    it('Block if 2', function () {
        for (let i = 0, first = true; i < 2; i++, first = false) {
            if (first) {
                assert.strictEqual(i, 0);
            } else {
                assert.strictEqual(i, 1);
            }
        }
    });

    it('Block if 3', function () {
        for (let first = true, i = 0; i < 2; first = false) {
            if (first) {
                assert.strictEqual(i, 0);
            } else {
                assert.strictEqual(i, 1);
            }
            i++;
        }
    });
});

describe('Spreads', function () {
    it('spread', function () {
        const obj1 = {
            key1: 1,
        };
        const obj2 = {
            ...obj1,
        };
        assert.strictEqual(obj2.key1, 1);
    });
});

describe('Math', function () {
    it('max', function () {
        assert.isTrue(Decimal.isDecimal(Math.max(1, 2.01)));
        assert.strictEqual(typeof Math.max(1, 2), 'number');

        assert.strictEqual(Math.max(1, 2), 2);
        assert.strictEqual(Math.max(1, 2.01), 2.01);
        assert.strictEqual(Math.max(1, 3.01, 2), 3.01);
        assert.strictEqual(Math.max(2.01, 1), 2.01);
        assert.strictEqual(Math.max(0.1, 0.2), 0.2);

        const decimal = new Decimal('5.03248572345834856933174533434346346912144534543e+24');
        assert.strictEqual(Math.max(1, <any>decimal), <any>decimal);

        const max = Math.max(1, 2);
        assert.isTrue(typeof max === 'number');
        assertStrictEqual(max, 2);
    });

    it('min', function () {
        assert.isTrue(Decimal.isDecimal(Math.min(1, 2.01)));
        assert.strictEqual(typeof Math.min(1, 2), 'number');

        assert.strictEqual(Math.min(1, 2), 1);
        assert.strictEqual(Math.min(1, 2.01), 1);
        assert.strictEqual(Math.min(2.01, 1), 1);
        assert.strictEqual(Math.min(2.01, 1, 0.5), 0.5);
        assert.strictEqual(Math.min(0.1, 0.2), 0.1);

        const decimal = new Decimal('5.03248572345834856933174533434346346912144534543e+24');
        assert.strictEqual(Math.min(1, <any>decimal), 1);

        const min = Math.min(1, 2);
        assert.isTrue(typeof min === 'number');
        assertStrictEqual(min, 1);
    });

    it('round', function () {
        assert.strictEqual(typeof Math.round(0.9), 'number');
        assert.strictEqual(typeof Math.round(0), 'number');
        assert.strictEqual(Math.round(0.9), 1);

        assert.equal(round(30.4166667, 0.01), 30.42);
        assert.equal(round2(0.73213, 5), 0.73213);
        assert.equal(round2(0.732131, 5), 0.73213);
        assert.equal(round2(0.732138, 5), 0.73214);
        assert.equal(round2(1, 5), 1.0);

        assert.equal(round2(1, 5), Decimal.toNumber(1.0));
        const a = Math.round(0.73213 * Math.pow(10, 5)) / Math.pow(10, 5);
        const b = 0.73213;
        // assert.isTrue(Decimal.isDecimal(a));
        assert.strictEqual(a, b);

        assert.strictEqual(Math.round(1.9), 2);
        assert.strictEqual(Math.round(1.4), 1);

        const decimal1 = new Decimal('5032485723458348569331745334343463469121445345.43');
        assert.strictEqual(Math.round(<any>decimal1), 5.032485723458349e45);

        const rounded = Math.round(1);
        assert.strictEqual(typeof rounded, 'number');
        assertStrictEqual(rounded, 1);
    });

    it('floor', function () {
        assert.strictEqual(typeof Math.floor(0.9), 'number');
        assert.strictEqual(typeof Math.floor(0), 'number');

        assert.strictEqual(Math.floor(0.9), 0);
        assert.strictEqual(Math.floor(0.1), 0);
        assert.strictEqual(Math.floor(1.9), 1);
        assert.strictEqual(Math.floor(1.1), 1);

        const decimal1 = new Decimal('5032485723458348569331745334343463469121445345.43');
        assert.strictEqual(Math.floor(<any>decimal1), 5.032485723458349e45);
    });

    it('ceil', function () {
        assert.strictEqual(typeof Math.ceil(0), 'number');
        assert.strictEqual(typeof Math.ceil(0.9), 'number');

        assert.strictEqual(Math.ceil(0.9), 1);
        assert.strictEqual(Math.ceil(0.1), 1);
        assert.strictEqual(Math.ceil(1.9), 2);
        assert.strictEqual(Math.ceil(1.1), 2);

        const decimal1 = new Decimal('5032485723458348569331745334343463469121445345.43');
        assert.strictEqual(Math.ceil(<any>decimal1), 5.032485723458349e45);
    });

    it('abs', function () {
        assert.isTrue(Decimal.isDecimal(Math.abs(-0.9)));
        assert.strictEqual(typeof Math.abs(-1), 'number');
        assert.strictEqual(Math.abs(-0.9), 0.9);

        const decimal1 = new Decimal('-5032485723458348569331745334343463469121445345.43');
        const decimal2 = new Decimal('5032485723458348569331745334343463469121445345.43');
        assert.strictEqual(Math.abs(<any>decimal1), <any>decimal2);

        const abs = Math.abs(-1);
        assert.strictEqual(typeof abs, 'number');
        assertStrictEqual(abs, 1);
    });

    it('pow', function () {
        assert.isTrue(Decimal.isDecimal(Math.pow(2.01, 2)));
        assert.strictEqual(typeof Math.pow(2, 2), 'number');
        assert.strictEqual(Math.pow(2, 2), 4);
        assert.strictEqual(Math.pow(2.01, 2), 4.0401);

        const decimal1 = new Decimal('5.0324857234583485693e+45');
        const decimal2 = new Decimal('2.5325912556812097992e+91');
        const decimal3 = Math.pow(<any>decimal1, 2);
        assert.strictEqual(decimal3, <any>decimal2);
    });
});
