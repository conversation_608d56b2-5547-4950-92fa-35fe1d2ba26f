import { getPackageNameAndRoot } from '@sage/xtrem-i18n';
import { Dict } from '@sage/xtrem-shared';
import { trim } from 'lodash';
import * as ts from 'typescript';
import { writeStringLiteralsToBase } from './transformer-utils';

const excludedAstTypes = new Set([
    ts.SyntaxKind.ImportDeclaration,
    ts.SyntaxKind.TypePredicate,
    ts.SyntaxKind.TypeReference,
]);
const serverLocalizableValidationErrorCalls = ['businessRuleError', 'authorizationError', 'dataInputError'];
const straightforwardLocalizationMethods = ['localize', 'addLocalized', 'withMessage'];

/**
 * Determines whether the given file is a source file
 * @param file the TS file to be checked
 * @returns true if file is a source file
 */
function isSourceFile(file: ts.SourceFile): boolean {
    return file.fileName.indexOf('node_modules') === -1 && <PERSON><PERSON>an(file.fileName.match(/(\/|\\)lib(\/|\\)/));
}

/**
 * Adds an entry to the dictionary
 * @param key the dictionary key
 * @param value the dictionary value
 */
function addDictionaryEntry(dictionary: Dict<string>, key: string, value: string) {
    if (dictionary[key] !== undefined && dictionary[key] !== value) {
        throw new Error(`Duplicated key '${key}' with different values: '${dictionary[key]}' and '${value}'`);
    }

    dictionary[key] = value;
}

/**
 * Typescript visitor
 * @param ctx the transformation context
 */
export const messageTransformVisitor =
    (dictionary: Dict<string>, pkgName: string, fileName: string, ctx: ts.TransformationContext, skipPackageValidation = false) =>
    (node: ts.Node): ts.Node => {
        if (excludedAstTypes.has(node.kind)) {
            return node;
        }
        // Methods taking straightforward arguments: localize(key, template, data)
        if (
            ts.isCallExpression(node) &&
            (((node.expression as any).name &&
                straightforwardLocalizationMethods.includes((node.expression as any).name.text)) ||
                ((node.expression as any).text &&
                    straightforwardLocalizationMethods.includes((node.expression as any).text)))
        ) {
            const key: string = (node.arguments[0] as any).text;
            if (key != null) {
                // Key is null if we parse something like `cx.localize(node.getLocalizedTitleKey(), ...)`
                if (!skipPackageValidation && key.indexOf(`${pkgName}/`) === -1) {
                    throw new Error(`'${key}' in '${fileName}' should be prefixed with '${pkgName}/'`);
                }
                const value = (node.arguments[1] as any).text;
                addDictionaryEntry(dictionary, key, value);
            }
        }

        // Methods taking object arguments: localize({key, template, data})
        if (
            ts.isCallExpression(node) &&
            (((node.expression as any).name &&
                serverLocalizableValidationErrorCalls.includes((node.expression as any).name.text)) ||
                ((node.expression as any).text &&
                    serverLocalizableValidationErrorCalls.includes((node.expression as any).text) &&
                    ts.isObjectLiteralExpression(node.arguments[0])))
        ) {
            const objectLiteralExpression = node.arguments[0] as ts.ObjectLiteralExpression;
            const arg = objectLiteralExpression.properties.reduce(
                (prevValue: Dict<string>, element: ts.ObjectLiteralElementLike) => {
                    if (
                        ts.isPropertyAssignment(element) &&
                        element.initializer &&
                        ts.isStringLiteral(element.initializer)
                    ) {
                        const name = element.name?.getText(node.getSourceFile());
                        const value = trim(element.initializer.getText(node.getSourceFile()), '\'"');
                        prevValue[name] = value;
                    }

                    return prevValue;
                },
                {} as Dict<string>,
            );

            if (arg.key && arg.message) {
                addDictionaryEntry(dictionary, arg.key, arg.message);
            }
        }

        return ts.visitEachChild(node, messageTransformVisitor(dictionary, pkgName, fileName, ctx, skipPackageValidation), ctx);
    };

/**
 * This transformer is meant to be run AFTER the 'decorator-transformer' for pages/stickers &
 * for every source file of an em-core package
 * @param ctx the transformation context
 * @returns the transformed file
 */
export function messageTransformer(ctx: ts.TransformationContext): ts.Transformer<ts.SourceFile> {
    return (file: ts.SourceFile) => {
        if (!isSourceFile(file)) {
            return file;
        }

        try {
            const dictionary = {} as Dict<string>;
            const nameAndRoot = getPackageNameAndRoot(file.fileName);
            const packageName = nameAndRoot.name;
            const result = ts.visitNode(file, messageTransformVisitor(dictionary, packageName, file.fileName, ctx));
            writeStringLiteralsToBase(dictionary, nameAndRoot.root);
            return result as ts.SourceFile;
        } catch (err) {
            throw new Error(`Message transformer failed. ${err}`);
        }
    };
}

export function tsPatchMessageTransformer() {
    return messageTransformer;
}
