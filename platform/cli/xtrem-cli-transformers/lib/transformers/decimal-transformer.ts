import * as ts from 'typescript';

const tsf = ts.factory;

function isDecimalLiteral(literal: string) {
    return /^[+-]?(?:\.\d+|\d+\.\d*)$/.test(literal);
}

export function decimalTransformer(ctx: ts.TransformationContext): ts.Transformer<ts.SourceFile> {
    let typesLib: ts.Identifier | null = null;

    function manageBinaryExpression(
        binary: ts.BinaryExpression,
    ): ts.CallExpression | ts.AssignmentExpression<ts.EqualsToken> | undefined {
        let fn = createFunction(binary.operatorToken.kind);
        if (fn) {
            return tsf.createCallExpression(fn, undefined, [binary.left, binary.right]);
        }
        fn = fromAssignment(binary.operatorToken.kind);
        if (fn) {
            return tsf.createAssignment(
                tsf.createIdentifier(binary.left.getText()),
                tsf.createCallExpression(fn, undefined, [binary.left, binary.right]),
            );
        }

        return undefined;
    }

    function manageNumericLiteral(literal: ts.NumericLiteral): ts.CallExpression | undefined {
        if (isDecimalLiteral(literal.getText())) {
            return tsf.createCallExpression(createIdentifier('newDecimal'), undefined, [
                tsf.createStringLiteral(`${literal.getText()}`),
            ]);
        }
        return undefined;
    }

    function managePrefixUnaryExpression(
        expression: ts.PrefixUnaryExpression,
    ): ts.CallExpression | ts.ParenthesizedExpression | undefined {
        if (
            !(
                (expression.operator === ts.SyntaxKind.MinusToken || expression.operator === ts.SyntaxKind.PlusToken) &&
                expression.operand.kind === ts.SyntaxKind.NumericLiteral &&
                !isDecimalLiteral(expression.operand.getText())
            )
        ) {
            let fn = createFunction(expression.operator, true);
            if (fn) {
                return tsf.createCallExpression(fn, undefined, [expression.operand]);
            }
            fn = fromAssignment(expression.operator);
            if (fn) {
                return tsf.createParenthesizedExpression(
                    tsf.createAssignment(
                        tsf.createIdentifier(expression.operand.getText()),
                        tsf.createCallExpression(fn, undefined, [expression.operand, tsf.createNumericLiteral(1)]),
                    ),
                );
            }
        }
        return undefined;
    }

    function manageBlock(block: ts.Block): ts.Block {
        const declaration = tsf.createExpressionStatement(tsf.createIdentifier('var _v'));
        return tsf.updateBlock(block, tsf.createNodeArray([declaration, ...block.statements]));
    }

    function managePostUnaryExpression(
        expression: ts.PostfixUnaryExpression,
        nodeKey: (n: ts.Node) => string,
    ): ts.BinaryExpression | ts.ParenthesizedExpression | undefined {
        const fn = fromAssignment(expression.operator);
        if (fn) {
            let block = expression.parent as ts.Block;
            while (block) {
                if (block.kind === ts.SyntaxKind.Block) {
                    blocks[nodeKey(block)] = true;
                    return tsf.createComma(
                        tsf.createComma(
                            tsf.createAssignment(
                                tsf.createIdentifier('_v'),
                                tsf.createIdentifier(expression.operand.getText()),
                            ),
                            tsf.createAssignment(
                                tsf.createIdentifier(expression.operand.getText()),
                                tsf.createCallExpression(fn, undefined, [
                                    expression.operand,
                                    tsf.createNumericLiteral(1),
                                ]),
                            ),
                        ),
                        tsf.createIdentifier('_v'),
                    );
                }
                block = block.parent as ts.Block;
            }
        }
        return undefined;
    }

    function manageIdentifier(identifier: ts.Identifier): ts.Identifier | undefined {
        if (identifier.getText() === 'Math') {
            const parentKey = identifier.parent.getText();
            if (/Math\.(?:abs|ceil|floor|max|min|pow|round)$/.test(parentKey)) {
                return createIdentifier();
            }
        }
        return undefined;
    }

    function createIdentifier(name?: string) {
        typesLib = typesLib || tsf.createIdentifier('typesLib');
        return tsf.createIdentifier(name ? `${typesLib.text}.${name}` : typesLib.text);
    }

    function createFunction(kind: ts.SyntaxKind, isUnary = false): ts.Identifier | undefined {
        switch (kind) {
            case ts.SyntaxKind.LessThanToken:
                return createIdentifier('lt');
            case ts.SyntaxKind.GreaterThanToken:
                return createIdentifier('gt');
            case ts.SyntaxKind.LessThanEqualsToken:
                return createIdentifier('lte');
            case ts.SyntaxKind.GreaterThanEqualsToken:
                return createIdentifier('gte');
            case ts.SyntaxKind.EqualsEqualsToken:
                return createIdentifier('eq');
            case ts.SyntaxKind.EqualsEqualsEqualsToken:
                return createIdentifier('strictEq');
            case ts.SyntaxKind.ExclamationEqualsToken:
                return createIdentifier('ne');
            case ts.SyntaxKind.ExclamationEqualsEqualsToken:
                return createIdentifier('strictNe');
            case ts.SyntaxKind.PlusToken:
                return createIdentifier(isUnary ? 'plus' : 'add');
            case ts.SyntaxKind.MinusToken:
                return createIdentifier(isUnary ? 'negated' : 'sub');
            case ts.SyntaxKind.AsteriskToken:
                return createIdentifier('mul');
            case ts.SyntaxKind.SlashToken:
                return createIdentifier('div');
            default:
                return undefined;
        }
    }

    function fromAssignment(kind: ts.SyntaxKind): ts.Identifier | undefined {
        switch (kind) {
            case ts.SyntaxKind.PlusEqualsToken:
                return createFunction(ts.SyntaxKind.PlusToken);
            case ts.SyntaxKind.MinusEqualsToken:
                return createFunction(ts.SyntaxKind.MinusToken);
            case ts.SyntaxKind.AsteriskEqualsToken:
                return createFunction(ts.SyntaxKind.AsteriskToken);
            case ts.SyntaxKind.SlashEqualsToken:
                return createFunction(ts.SyntaxKind.SlashToken);
            case ts.SyntaxKind.PlusPlusToken:
                return createFunction(ts.SyntaxKind.PlusToken);
            case ts.SyntaxKind.MinusMinusToken:
                return createFunction(ts.SyntaxKind.MinusToken);
            default:
                return undefined;
        }
    }

    let blocks: { [key: string]: boolean };

    const visitor: ts.Visitor = function visit(node: ts.Node): ts.Node {
        const sourceFile = node.getSourceFile();
        const commentRanges = ts.getLeadingCommentRanges(
            sourceFile.getFullText(),
            node.getFullStart());
        if (commentRanges?.length) {
            const commentStrings =
                commentRanges.map(r => sourceFile.getFullText().slice(r.pos, r.end)).join('\n');
            if (commentStrings.includes('@xtrem-decimal-ignore')) {
                // If the node has a comment with @xtrem-decimal-ignore, skip transformation
                return node;
            }
        }
        const nodeKey = (n: ts.Node) => `${n.pos}~${n.end}`;
        // console.log(`${node.kind} ${node.pos}~${node.end} ${node.getText()}`);
        const newNode = ts.visitEachChild(node, visit, ctx);
        if (newNode.kind === ts.SyntaxKind.BinaryExpression) {
            const binaryExpressionResult = manageBinaryExpression(newNode as ts.BinaryExpression);
            if (binaryExpressionResult) {
                return binaryExpressionResult;
            }
        } else if (newNode.kind === ts.SyntaxKind.NumericLiteral) {
            const numericLiteralResult = manageNumericLiteral(newNode as ts.NumericLiteral);
            if (numericLiteralResult) {
                return numericLiteralResult;
            }
        } else if (newNode.kind === ts.SyntaxKind.PrefixUnaryExpression) {
            const prefixUnaryExpressionResult = managePrefixUnaryExpression(newNode as ts.PrefixUnaryExpression);
            if (prefixUnaryExpressionResult) {
                return prefixUnaryExpressionResult;
            }
        } else if (newNode.kind === ts.SyntaxKind.Block && blocks[nodeKey(newNode)]) {
            return manageBlock(newNode as ts.Block);
        } else if (newNode.kind === ts.SyntaxKind.PostfixUnaryExpression) {
            const postUnaryExpressionResult = managePostUnaryExpression(newNode as ts.PostfixUnaryExpression, nodeKey);
            if (postUnaryExpressionResult) {
                return postUnaryExpressionResult;
            }
        } else if (newNode.kind === ts.SyntaxKind.Identifier) {
            const identifierResult = manageIdentifier(newNode as ts.Identifier);

            if (identifierResult) {
                return identifierResult;
            }
        }
        return newNode;
    };

    return (file: ts.SourceFile) => {
        typesLib = null;
        blocks = {};
        const updated = ts.visitNode(file, visitor) as ts.SourceFile;
        if (typesLib == null) return updated;

        const importDecl = tsf.createImportDeclaration(
            undefined,
            tsf.createImportClause(false, undefined, tsf.createNamespaceImport(typesLib)),
            tsf.createStringLiteral('@sage/xtrem-decimal'),
        );
        return tsf.updateSourceFile(updated, [importDecl, ...updated.statements]);
    };
}

/* The default export is important for webpack */
export default decimalTransformer;
