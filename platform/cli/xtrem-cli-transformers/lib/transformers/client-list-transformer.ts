import { ClientArtifactUsedLiterals, getPackageNameAndRoot } from '@sage/xtrem-i18n';
import * as fs from 'fs';
import * as path from 'path';
import * as ts from 'typescript';
import { getDirName, getFileName, save<PERSON><PERSON>son } from '../utils';

const excludedAstTypes = new Set([
    ts.SyntaxKind.ImportDeclaration,
    ts.SyntaxKind.TypePredicate,
    ts.SyntaxKind.TypeReference,
]);

/**
 * Determines whether the given file is a source file
 * @param file the TS file to be checked
 * @returns true if file is a source file
 */
function isSourceFile(file: ts.SourceFile): boolean {
    const fileName = path.parse(file.fileName).base;
    return (
        !['index.ts', '_index.ts'].includes(fileName) &&
        file.fileName.indexOf('node_modules') === -1 &&
        Boolean(file.fileName.match(/(\/|\\)lib(\/|\\)/))
    );
}

/**
 * Typescript visitor
 * @param ctx the transformation context
 */
export const visitor =
    (usedStrings: string[], usedEnums: string[], file: ts.SourceFile, ctx: ts.TransformationContext) =>
    (node: ts.Node): ts.Node => {
        if (excludedAstTypes.has(node.kind)) {
            return node;
        }

        // Check strings
        if (
            ts.isCallExpression(node) &&
            (((node.expression as any).name && (node.expression as any).name.text === 'localize') ||
                ((node.expression as any).text && (node.expression as any).text === 'localize'))
        ) {
            const key: string = (node.arguments[0] as any).text;
            usedStrings.push(key);
        }

        // Check enum localize calls
        if (
            ts.isCallExpression(node) &&
            (((node.expression as any).name && (node.expression as any).name.text === 'localizeEnumMember') ||
                ((node.expression as any).text && (node.expression as any).text === 'localizeEnumMember'))
        ) {
            const key: string = (node.arguments[0] as any).text;
            if (!usedEnums.includes(key)) usedEnums.push(key);
        }

        // Check enums
        if (ts.isPropertyAssignment(node) && node.name.getText(file) === 'optionType') {
            const initializer = node.initializer;
            if (ts.isStringLiteral(initializer)) {
                usedEnums.push(initializer.text);
            }
        }

        return ts.visitEachChild(node, visitor(usedStrings, usedEnums, file, ctx), ctx);
    };

/**
 * This transformer is meant to be run AFTER the 'decorator-transformer' and 'message-transformer' for pages/stickers &
 * for every source file of an xtrem application package. For all client side artifacts it lists the strings that the
 * page uses so the server can send the right strings to the client.
 *
 * @param ctx the transformation context
 * @returns the transformed file
 */
export function clientListTransformer(ctx: ts.TransformationContext): ts.Transformer<ts.SourceFile> {
    return (file: ts.SourceFile) => {
        if (isSourceFile(file)) {
            const strings: string[] = [];
            const enums: string[] = [];
            const nameAndRoot = getPackageNameAndRoot(file.fileName);
            const artifactType = getDirName(file);
            const artifactName = getFileName(file);
            ts.visitNode(file, visitor(strings, enums, file, ctx));

            const buildDir = path.resolve(nameAndRoot.root, 'build');
            safeMkdir(buildDir);

            const buildLibDir = path.resolve(buildDir, 'lib');
            safeMkdir(buildLibDir);

            const artifactDir = path.resolve(buildLibDir, artifactType);
            safeMkdir(artifactDir);

            const metadataFilePath = path.resolve(artifactDir, `${artifactName}.meta.json`);
            const artifactMetaData: { literals: ClientArtifactUsedLiterals } = {
                literals: { strings: Array.from(new Set(strings)), enums: Array.from(new Set(enums)) },
            };
            const dataToSave = fs.existsSync(metadataFilePath)
                ? { ...JSON.parse(fs.readFileSync(metadataFilePath, 'utf-8')), ...artifactMetaData }
                : artifactMetaData;
            saveAsJson(metadataFilePath, dataToSave);
        }
        return file;
    };
}

// Normal and Binary build sometime reports EEXIST error. It might be due to a concurrency issue.
// It is easier to ignore EEXIST error and it avoids race conditions.
function safeMkdir(dir: string): void {
    try {
        fs.mkdirSync(dir);
    } catch (e) {
        if (e.code !== 'EEXIST') {
            throw e;
        }
    }
}
