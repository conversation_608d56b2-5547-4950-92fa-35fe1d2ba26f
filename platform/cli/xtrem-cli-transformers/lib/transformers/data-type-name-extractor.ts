import { getPackageNameAndRoot } from '@sage/xtrem-i18n';
import { createDictionary, Dict, titleCase } from '@sage/xtrem-shared';
import { snakeCase } from 'lodash';
import * as ts from 'typescript';
import { getDirName } from '../utils';
import { createDictionaryKey, isDataTypeFile, isEnumFile, writeStringLiteralsToBase } from './transformer-utils';

const excludedAstTypes = new Set([
    ts.SyntaxKind.ImportDeclaration,
    ts.SyntaxKind.TypePredicate,
    ts.SyntaxKind.TypeReference,
]);

/**
 * Top-level TS visitor
 * @param prefix prefix for translation keys
 */
export const visitor =
    (
        dictionary: Dict<string>,
        packageName: string,
        dirName: string,
        file: ts.SourceFile,
        ctx: ts.TransformationContext,
    ) =>
    (node: ts.Node): ts.Node => {
        if (excludedAstTypes.has(node.kind)) {
            return node;
        }

        if (isEnumFile(file)) {
            const enumDeclaration = file.statements.find(ts.isEnumDeclaration);
            if (enumDeclaration) {
                const dataTypeName = enumDeclaration?.name?.getText();
                const key = `${packageName}/${createDictionaryKey('data_types', snakeCase(dataTypeName))}__name`;
                dictionary[key] = titleCase(dataTypeName);
            }
        } else if (
            ts.isVariableDeclaration(node) &&
            node.initializer &&
            ts.isNewExpression(node.initializer) &&
            ts.isIdentifier(node.initializer.expression) &&
            node.name &&
            ts.isIdentifier(node.name) &&
            node.initializer.expression.text.endsWith('DataType')
        ) {
            const dataTypeName = node.name.text;
            const key = `${packageName}/${createDictionaryKey('data_types', snakeCase(dataTypeName))}__name`;
            dictionary[key] = titleCase(dataTypeName);
        }

        return ts.visitEachChild(node, visitor(dictionary, packageName, dirName, file, ctx), ctx);
    };

/**
 * This transformer extract data-types to create literals key/value for them
 *
 * @param ctx transformation context
 * @returns the transformed file
 */
export function dataTypesNameExtractor(ctx: ts.TransformationContext): ts.Transformer<ts.SourceFile> {
    return (file: ts.SourceFile) => {
        if (!isDataTypeFile(file)) {
            return file;
        }

        const filename = file.fileName;
        try {
            const dictionary = createDictionary<string>();
            const dirName = getDirName(file, true);
            const nameAndRoot = getPackageNameAndRoot(filename);
            const packageName = nameAndRoot.name;
            const result = ts.visitNode(file, visitor(dictionary, packageName, dirName, file, ctx));
            writeStringLiteralsToBase(dictionary, nameAndRoot.root);
            return result as ts.SourceFile;
        } catch (err) {
            throw new Error(`${filename}: dataType transformer failed: ${err.message}`);
        }
    };
}
