import { getPackageNameAndRoot } from '@sage/xtrem-i18n';
import { createDictionary, Dict, titleCase } from '@sage/xtrem-shared';
import { snakeCase } from 'lodash';
import * as ts from 'typescript';
import { getDirName } from '../utils';
import { createDictionaryKey, isActivityFile, isTestFile, writeStringLiteralsToBase } from './transformer-utils';

const excludedAstTypes = new Set([
    ts.SyntaxKind.ImportDeclaration,
    ts.SyntaxKind.TypePredicate,
    ts.SyntaxKind.TypeReference,
]);

/**
 * Top-level TS visitor
 * @param prefix prefix for translation keys
 */
export const visitor =
    (dictionary: Dict<string>, packageName: string, dirName: string, filename: string, ctx: ts.TransformationContext) =>
    (node: ts.Node): ts.Node => {
        if (excludedAstTypes.has(node.kind)) {
            return node;
        }

        if (ts.isVariableStatement(node) && dirName === 'activities') {
            const declaration = node.declarationList.declarations.find(
                decl =>
                    decl.initializer &&
                    ts.isNewExpression(decl.initializer) &&
                    ts.isIdentifier(decl.initializer.expression) &&
                    decl.initializer.expression.text === 'Activity',
            );
            if (declaration && ts.isIdentifier(declaration.name)) {
                const activityName = declaration.name.text;
                const key = `${packageName}/${createDictionaryKey('activity', snakeCase(activityName))}__name`;
                dictionary[key] = titleCase(activityName);

                if (
                    declaration.initializer &&
                    ts.isNewExpression(declaration.initializer) &&
                    declaration.initializer.arguments?.[0] &&
                    ts.isObjectLiteralExpression(declaration.initializer.arguments?.[0])
                ) {
                    const properties = declaration.initializer.arguments[0].properties;
                    const permissionsProperty = properties.find(
                        arg =>
                            ts.isPropertyAssignment(arg) &&
                            ts.isIdentifier(arg.name) &&
                            arg.name.text === 'permissions',
                    ) as ts.PropertyAssignment;
                    if (ts.isArrayLiteralExpression(permissionsProperty.initializer)) {
                        const values = permissionsProperty.initializer.elements.map(e => e.getText());
                        values.forEach(permission => {
                            const permissionKey = `${packageName}/${createDictionaryKey(
                                'permission',
                                snakeCase(permission),
                            )}__name`;
                            dictionary[permissionKey] = titleCase(permission);
                        });
                    }
                }
                return node;
            }
        }

        return ts.visitEachChild(node, visitor(dictionary, packageName, dirName, filename, ctx), ctx);
    };

/**
 * This transformer extract activities to create literals key/value for them
 *
 * @param ctx transformation context
 * @returns the transformed file
 */
export function activityNameExtractor(ctx: ts.TransformationContext): ts.Transformer<ts.SourceFile> {
    return (file: ts.SourceFile) => {
        if (!isActivityFile(file) || isTestFile(file)) {
            return file;
        }

        const filename = file.fileName;
        try {
            const dictionary = createDictionary<string>();
            const dirName = getDirName(file, true);
            const nameAndRoot = getPackageNameAndRoot(filename);
            const packageName = nameAndRoot.name;
            const result = ts.visitNode(file, visitor(dictionary, packageName, dirName, filename, ctx));
            writeStringLiteralsToBase(dictionary, nameAndRoot.root);
            return result as ts.SourceFile;
        } catch (err) {
            throw new Error(`${filename}: activity transformer failed: ${err.message}`);
        }
    };
}
