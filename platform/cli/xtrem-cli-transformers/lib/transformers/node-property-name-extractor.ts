import { getPackageNameAndRoot } from '@sage/xtrem-i18n';
import { Logger } from '@sage/xtrem-log';
import { Dict, titleCase } from '@sage/xtrem-shared';
import { differenceWith, isEmpty, isEqual, snakeCase, trim } from 'lodash';
import * as ts from 'typescript';
import {
    canExportNodeDecorator,
    getCrudBulkMutationNames,
    getDecoratorArguments,
    getDirName,
    getNodeStorageType,
    isAbstractNodeDecorator,
    isNodeExtensionDecorator,
    isSubNodeDecorator,
} from '../utils';
import {
    addDictionaryEntry,
    createDictionaryKey,
    isNodeLikeArtifactFile,
    isTestFile,
    writeStringLiteralsToBase,
} from './transformer-utils';

const excludedAstTypes = new Set([
    ts.SyntaxKind.ImportDeclaration,
    ts.SyntaxKind.TypePredicate,
    ts.SyntaxKind.TypeReference,
    ts.SyntaxKind.PropertyAssignment,
]);
const includedOperations = ['mutation', 'asyncMutation', 'bulkMutation', 'query'];

const logger = Logger.getLogger(__filename, 'i18n-resolver');

/**
 * Top-level TS visitor
 * @param prefix prefix for translation keys
 */
export const visitor =
    (
        dictionary: Dict<string>,
        packageName: string,
        dirName: string,
        filename: string,
        ctx: ts.TransformationContext,
        nodePrefix?: string,
    ) =>
    (node: ts.Node): ts.Node => {
        if (excludedAstTypes.has(node.kind)) {
            return node;
        }

        let prefix = nodePrefix;

        if (ts.isClassDeclaration(node)) {
            const nodeName = node.name?.getText().trim();
            prefix = `${packageName}/${createDictionaryKey(dirName, snakeCase(nodeName))}`;
            if (dirName === 'nodes') dictionary[`${prefix}__node_name`] = titleCase(nodeName);
            const decorator = ts.getDecorators(node)?.[0];
            if (decorator) {
                const bulkMutationNames = getCrudBulkMutationNames(filename, decorator);
                bulkMutationNames.forEach(bulkMutationName => {
                    dictionary[`${prefix}__bulkMutation__${bulkMutationName}`] = titleCase(bulkMutationName);
                });
                const isAbstract = isAbstractNodeDecorator(filename, decorator);
                const isSubNode = isSubNodeDecorator(decorator);
                const isNodeExtension = isNodeExtensionDecorator(decorator);
                const canExport = canExportNodeDecorator(filename, decorator);
                const storage = getNodeStorageType(filename, decorator);
                if (!isAbstract && canExport && !isNodeExtension && (storage === "'sql'" || isSubNode)) {
                    dictionary[`${prefix}__asyncMutation__asyncExport`] = 'Export';
                    dictionary[`${prefix}__asyncMutation__asyncExport__parameter__id`] = 'ID';
                    dictionary[`${prefix}__asyncMutation__asyncExport__parameter__filter`] = 'Filter';
                }
            }
        }

        if (ts.isPropertyDeclaration(node) && !isEmpty(ts.getDecorators(node))) {
            const propertyName = node.name.getText().trim();
            addDictionaryEntry(dictionary, `${prefix}__property__${propertyName}`, titleCase(propertyName));
            return node;
        }

        if (ts.isMethodDeclaration(node)) {
            const decorator = ts.getDecorators(node)?.[0];
            if (
                decorator?.expression &&
                ts.isCallExpression(decorator.expression) &&
                ts.isPropertyAccessExpression(decorator.expression?.expression)
            ) {
                const decoratorName = decorator.expression.expression.name?.getText().trim();
                if (includedOperations.includes(decoratorName)) {
                    const nodeName = node.name?.getText().trim();
                    const operationEntry = `${prefix}__${decoratorName}__${nodeName}`;
                    const parametersFromDecorator = getOperationDecoratorParameters(filename, decorator) || [];

                    logger.verbose(() => `------Extract i18n entry from ${filename}`);
                    logger.verbose(() => `   operationEntry ${operationEntry}`);
                    logger.verbose(() => `       parametersFromDecorator ${parametersFromDecorator}`);

                    const parameters = node.parameters
                        .map(parameter => {
                            return parameter.name?.getText().trim();
                        })
                        .filter((_name, idx) => {
                            return (
                                // for bulkMutation, skip the second parameter
                                !(decoratorName === 'bulkMutation' && idx === 1) &&
                                // context parameter position (first position) is skipped
                                idx !== 0
                            );
                        });

                    logger.verbose(() => `       parameters ${parameters}`);
                    if (!isEqual(parametersFromDecorator, parameters)) {
                        let difference = differenceWith(parameters, parametersFromDecorator, isEqual);
                        if (difference.length === 0)
                            difference = differenceWith(parametersFromDecorator, parameters, isEqual);
                        throw new Error(
                            `There is a difference between parameters in the decorator and parameters in the function declaration of '${nodeName}' operation: '${difference}'`,
                        );
                    }
                    addDictionaryEntry(dictionary, operationEntry, titleCase(nodeName));
                    addDictionaryEntry(dictionary, `${operationEntry}__failed`, `${titleCase(nodeName)} failed.`);
                    parameters.forEach(parameter => {
                        addDictionaryEntry(
                            dictionary,
                            `${operationEntry}__parameter__${parameter}`,
                            titleCase(parameter),
                        );
                    });

                    return node;
                }
            }
        }

        return ts.visitEachChild(node, visitor(dictionary, packageName, dirName, filename, ctx, prefix), ctx);
    };

export function getOperationDecoratorParameters(filename: string, node: ts.Decorator): any[] | undefined {
    const decoratorArguments = getDecoratorArguments(filename, node);

    const parameters = decoratorArguments.find(
        argument =>
            ts.isPropertyAssignment(argument) &&
            argument.initializer &&
            argument.initializer.kind === ts.SyntaxKind.ArrayLiteralExpression &&
            ts.isIdentifier(argument.name) &&
            argument.name.escapedText.toString() === 'parameters',
    );

    if (
        parameters &&
        ts.isPropertyAssignment(parameters) &&
        parameters.initializer &&
        parameters.initializer.kind === ts.SyntaxKind.ArrayLiteralExpression &&
        ts.isArrayLiteralExpression(parameters.initializer)
    )
        return parameters.initializer.elements.map(param => {
            const nameProperty =
                ts.isObjectLiteralExpression(param) && param.properties
                    ? param.properties?.find(prop => prop.name?.getText() === 'name')
                    : undefined;
            if (!nameProperty) logger.warn('"name" property undefined');

            return nameProperty && ts.isPropertyAssignment(nameProperty) && ts.isStringLiteral(nameProperty.initializer)
                ? trim(nameProperty.initializer.getText(), '"\'')
                : undefined;
        });
    return undefined;
}
/**
 * This transformer is meant to be run BEFORE the 'message-transformer' and its purpose is
 * to wrap some page/sticker decorator properties (see 'includedProperties') with a call to the 'ui.localize' function.
 *
 * @param ctx transformation context
 * @returns the transformed file
 */
export function nodePropertyNameExtractor(ctx: ts.TransformationContext): ts.Transformer<ts.SourceFile> {
    return (file: ts.SourceFile) => {
        if (!isNodeLikeArtifactFile(file) || isTestFile(file)) {
            return file;
        }

        const filename = file.fileName;
        try {
            const dictionary = {} as Dict<string>;
            const dirName = getDirName(file, true);
            const nameAndRoot = getPackageNameAndRoot(filename);
            const packageName = nameAndRoot.name;
            const result = ts.visitNode(file, visitor(dictionary, packageName, dirName, filename, ctx));
            const key = `${packageName}/package__name`;
            dictionary[key] = titleCase(packageName);
            writeStringLiteralsToBase(dictionary, nameAndRoot.root);
            return result as ts.SourceFile;
        } catch (err) {
            throw new Error(`${filename}: decorator transformer failed: ${err.message}`);
        }
    };
}
