import * as fs from 'fs';
import * as glob from 'glob';
import * as path from 'path';
import * as rimraf from 'rimraf';
import { mergeTranslationFiles } from './merge-translation-files';

/**
 * This file is used as a post build step for packages that uses localization but not application packages such as
 * xtrem-document-editor and xtrem-ui-components
 */

const libI18nDir = path.resolve(process.cwd(), 'lib', 'i18n');
const buildDir = path.resolve(process.cwd(), 'build');
const buildLibDir = path.resolve(buildDir, 'lib');
const buildI18nDir = path.resolve(buildLibDir, 'i18n');

rimraf.sync(buildI18nDir);
mergeTranslationFiles(process.cwd());
if (!fs.existsSync(buildDir)) {
    fs.mkdirSync(buildDir);
}
if (!fs.existsSync(buildLibDir)) {
    fs.mkdirSync(buildLibDir);
}
if (!fs.existsSync(buildI18nDir)) {
    fs.mkdirSync(buildI18nDir);
}
glob.sync('*.json', { cwd: libI18nDir }).forEach(f => {
    fs.cpSync(path.resolve(libI18nDir, f), path.resolve(buildI18nDir, f));
});
