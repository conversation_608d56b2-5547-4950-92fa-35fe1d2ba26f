import { getPackageNameAndRoot } from '@sage/xtrem-i18n';
import { Dict, titleCase } from '@sage/xtrem-shared';
import { kebabCase, trim } from 'lodash';
import * as ts from 'typescript';
import { getDirName, getFileName } from '../utils';
import { createDictionaryKey, isEnumFile, writeStringLiteralsToBase } from './transformer-utils';

/**
 * This transformer is meant to be run AFTER the 'message-transformer' for pages/stickers &
 * for every source file of a xtrem-services package
 * @param ctx the transformation context
 * @returns the transformed file
 */
export function enumTransformer(): ts.Transformer<ts.SourceFile> {
    return (file: ts.SourceFile) => {
        if (isEnumFile(file)) {
            const enumDeclaration = file.statements.find(ts.isEnumDeclaration);
            const enumTypeDeclaration = file.statements.find(
                node => ts.isTypeAliasDeclaration(node) && node.modifiers?.[0].kind === ts.SyntaxKind.ExportKeyword,
            ) as ts.TypeAliasDeclaration;

            if (enumDeclaration) {
                const nameAndRoot = getPackageNameAndRoot(file.fileName);
                const packageName = nameAndRoot.name;
                const fileName = getFileName(file);
                const dirName = getDirName(file);
                const fileKey = `${packageName}/${createDictionaryKey(dirName, fileName)}`;
                const dictionary: Dict<string> = {};
                const enumName = enumDeclaration.name.getText();
                if (!enumTypeDeclaration)
                    throw new Error(`Enum name ${enumName} must declare a type in file ${dirName}/${fileName}.ts`);
                const enumTypeName = enumTypeDeclaration.name.getText();
                const expectedFilename = kebabCase(enumTypeName);
                if (expectedFilename !== fileName)
                    throw new Error(
                        `Invalid file name for enum type '${enumTypeName}'. Got ${fileName}.ts, expected ${expectedFilename}.ts`,
                    );
                enumDeclaration.members.forEach(m => {
                    const memberName = trim(m.name.getText(), "'");
                    dictionary[`${fileKey}__${memberName}`] = titleCase(memberName);
                });
                writeStringLiteralsToBase(dictionary, nameAndRoot.root);
            }
        }

        return file;
    };
}
