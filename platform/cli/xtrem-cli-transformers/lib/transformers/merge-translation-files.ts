import { Dict, supportedLocales } from '@sage/xtrem-shared';
import * as fs from 'fs';
import * as glob from 'glob';
import { without } from 'lodash';
import { saveAsJson } from '../utils';
import { excludeProperties, literalsCache, merge, sortByKey } from './transformer-utils';

export const mergeTranslationFiles = (dir: string) => {
    const currents = literalsCache[dir] || {};
    const baseFilePath = `${dir}/lib/i18n/base.json`;
    const originalBaseFileContent = sortByKey(
        fs.existsSync(baseFilePath) ? JSON.parse(fs.readFileSync(baseFilePath, 'utf-8')) : {},
    );
    const baseFiles = glob.sync('./lib/i18n/base.*.json', { cwd: dir, absolute: true, realpath: true });
    const baseKeys = baseFiles.reduce<Dict<string>>((previousValue, filePath) => {
        const fileContent = fs.readFileSync(filePath, 'utf-8');
        fs.unlinkSync(filePath);
        return merge(previousValue, JSON.parse(fileContent) as Dict<string>);
    }, currents);
    const updatedBaseContent = sortByKey(baseKeys);
    const stringsToRemove = without(Object.keys(originalBaseFileContent), ...Object.keys(updatedBaseContent));
    const stringsToUpdate = Object.keys(updatedBaseContent).filter(
        key => updatedBaseContent[key] !== originalBaseFileContent[key],
    );

    supportedLocales
        .filter(locale => locale !== 'base')
        .forEach(locale => {
            const localFilePath = `${dir}/lib/i18n/${locale}.json`;
            const localeFileContent: Dict<string> = fs.existsSync(localFilePath)
                ? JSON.parse(fs.readFileSync(localFilePath, 'utf-8'))
                : {};

            const nextLocaleContent = excludeProperties(localeFileContent, stringsToRemove);
            stringsToUpdate.forEach(key => {
                nextLocaleContent[key] = '';
            });

            saveAsJson(localFilePath, sortByKey(nextLocaleContent));
        });

    saveAsJson(baseFilePath, sortByKey(updatedBaseContent));
};
