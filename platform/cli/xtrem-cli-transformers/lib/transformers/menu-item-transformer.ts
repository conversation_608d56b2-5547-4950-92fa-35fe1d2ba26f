import { getPackageNameAndRoot, menuItemIdToStringKey } from '@sage/xtrem-i18n';
import { Dict } from '@sage/xtrem-shared';
import { trim } from 'lodash';
import * as ts from 'typescript';
import { writeStringLiteralsToBase } from './transformer-utils';

/**
 * Determines whether the given file is a menu item definition file
 * @param file the TS file to be checked
 * @returns true if file is a source file
 */
function isMenuItemFile(file: ts.SourceFile): boolean {
    return (
        file.fileName.indexOf('node_modules') === -1 &&
        !file.fileName.endsWith('index.ts') &&
        Boolean(file.fileName.match(/(\/|\\)lib(\/|\\)menu-items(\/|\\)/))
    );
}

/**
 * Adds an entry to the dictionary
 * @param key the dictionary key
 * @param value the dictionary value
 */
function addDictionaryEntry(dictionary: Dict<string>, key: string, value: string) {
    if (dictionary[key] !== undefined && dictionary[key] !== value) {
        throw new Error(`Duplicated key '${key}' with different values: '${dictionary[key]}' and '${value}'`);
    }

    dictionary[key] = value;
}

/**
 * Typescript visitor
 * @param ctx the transformation context
 */
export const visitor =
    (dictionary: Dict<string>, pkgName: string, fileName: string, ctx: ts.TransformationContext) =>
    (node: ts.Node): ts.Node => {
        if (
            ts.isVariableStatement(node) &&
            node.declarationList.declarations[0] &&
            ts.isVariableDeclaration(node.declarationList.declarations[0]) &&
            node.declarationList.declarations[0].initializer &&
            ts.isObjectLiteralExpression(node.declarationList.declarations[0].initializer)
        ) {
            const objectLiteral = node.declarationList.declarations[0].initializer;
            const id = objectLiteral.properties.find(
                p => ts.isPropertyAssignment(p) && p.name?.getText(node.getSourceFile()) === 'id',
            ) as ts.PropertyAssignment;
            const title = objectLiteral.properties.find(
                p => ts.isPropertyAssignment(p) && p.name?.getText(node.getSourceFile()) === 'title',
            ) as ts.PropertyAssignment;
            if (id && title) {
                const idContent = trim(id.initializer.getText(node.getSourceFile()), "'");
                const titleContent = trim(title.initializer.getText(node.getSourceFile()), "'");
                if (idContent && !idContent.startsWith(pkgName)) {
                    throw new Error(`Menu item ID '${idContent}' must start with the package name '${pkgName}'`);
                }

                if (idContent && titleContent) {
                    const key = menuItemIdToStringKey(idContent);
                    addDictionaryEntry(dictionary, key, titleContent);
                }
            }
        }

        return ts.visitEachChild(node, visitor(dictionary, pkgName, fileName, ctx), ctx);
    };

/**
 * This transformer extract menu item labels for translations from `menu-items` artifacts.
 * @param ctx the transformation context
 * @returns the transformed file
 */
export function menuItemTransformer(ctx: ts.TransformationContext): ts.Transformer<ts.SourceFile> {
    return (file: ts.SourceFile) => {
        if (!isMenuItemFile(file)) {
            return file;
        }

        try {
            const dictionary = {} as Dict<string>;
            const nameAndRoot = getPackageNameAndRoot(file.fileName);
            const packageName = nameAndRoot.name;
            ts.visitNode(file, visitor(dictionary, packageName, file.fileName, ctx));
            writeStringLiteralsToBase(dictionary, nameAndRoot.root);
            return file;
        } catch (err) {
            throw new Error(`Menu item transformer failed. ${err}`);
        }
    };
}
