import { getPackageNameAndRoot } from '@sage/xtrem-i18n';
import { LogicError, Maybe } from '@sage/xtrem-shared';
import * as fs from 'fs';
import { pick, trim, uniq } from 'lodash';
import * as path from 'path';
import * as ts from 'typescript';
import { getMetadataFilePathForClientFile, hashFile } from '../utils';

interface AccessBinding {
    node?: string;
    bind?: string;
}

interface ComponentAttributes extends AccessBinding {
    access?: AccessBinding;
    extensionAccess?: AccessBinding;
    isTransient?: boolean;
    valueField?: string;
    tunnelPage?: string;
    fragment?: string;
    helperTextField?: string;
}

type LiteralValueType = string | number | boolean;

const isPageAction = (typeName: string): boolean => typeName === 'ui.PageAction';

function mkdirp(p: string) {
    try {
        fs.mkdirSync(p, { recursive: true });
    } catch (e) {
        if (e.code !== 'EEXIST') {
            throw e;
        }
    }
}

export abstract class PageMetadataBuilder {
    /**
     * Determines whether the given file is a source file
     * @param file the TS file to be checked
     * @returns true if file is a source file
     */
    static isSourceFile(file: ts.SourceFile): boolean {
        const fileName = path.parse(file.fileName).base;
        return (
            !['index.ts', '_index.ts'].includes(fileName) &&
            file.fileName.indexOf('node_modules') === -1 &&
            Boolean(file.fileName.match(/(\/|\\)lib(\/|\\)/))
        );
    }

    /**
     * Retrieve a value from an typescript object literal
     * @param root
     * @param objectLiteralExp
     * @param key
     * @returns
     */
    static getValueFromObjectLiteralByKey = <T extends LiteralValueType>(
        root: ts.SourceFile,
        objectLiteralExp: ts.ObjectLiteralExpression,
        key: string,
    ): T | undefined => {
        const propertyAssignment =
            objectLiteralExp.properties &&
            (objectLiteralExp.properties.find((p: ts.PropertyAssignment) => p.name && p.name.getText(root) === key) as
                | ts.PropertyAssignment
                | undefined);

        const initializer = propertyAssignment?.initializer;
        if (initializer) {
            if (initializer.kind === ts.SyntaxKind.TrueKeyword) return true as T;
            if (initializer.kind === ts.SyntaxKind.FalseKeyword) return false as T;
            if (ts.isStringLiteral(initializer)) return initializer.text as T;
            if (ts.isNumericLiteral(initializer)) return Number(initializer.text) as T;
        }

        return undefined;
    };

    /**
     *
     * @param root
     * @param objectLiteralExp
     * @param key
     * @returns
     */
    static getStringValueFromObjectLiteralByKey = (
        root: ts.SourceFile,
        objectLiteralExp: ts.ObjectLiteralExpression,
        key: string,
    ): Maybe<string> => PageMetadataBuilder.getValueFromObjectLiteralByKey(root, objectLiteralExp, key);

    /**
     *
     * @param root
     * @param objectLiteralExp
     * @param key
     * @returns
     */
    static getBooleanValueFromObjectLiteralByKey = (
        root: ts.SourceFile,
        objectLiteralExp: ts.ObjectLiteralExpression,
        key: string,
    ): Maybe<boolean> => PageMetadataBuilder.getValueFromObjectLiteralByKey(root, objectLiteralExp, key);

    /**
     *
     * @param root
     * @param objectLiteralExp
     * @param key
     * @returns
     */
    static getNumberValueFromObjectLiteralByKey = (
        root: ts.SourceFile,
        objectLiteralExp: ts.ObjectLiteralExpression,
        key: string,
    ): Maybe<number> => PageMetadataBuilder.getValueFromObjectLiteralByKey(root, objectLiteralExp, key);

    /**
     * Get the text of a source file element
     * @param root
     * @param element
     * @returns
     */
    static getTsNodeText = (root: ts.SourceFile, element: ts.Node): string => {
        return element.getText(root);
    };

    static getLineAndCharacterOfTsNode(root: ts.SourceFile, element: ts.Node): ts.LineAndCharacter {
        return root.getLineAndCharacterOfPosition(element.pos);
    }

    static getLocation(root: ts.SourceFile, element: ts.Node): string {
        return `${root.fileName}:${PageMetadataBuilder.getLineAndCharacterOfTsNode(root, element).line + 1}`;
    }

    /**
     * get
     * @param root
     * @param objectLiteralExp
     * @param defaultNodeName
     * @returns
     */
    static getAccessBindingFromObjectLiteralExpression = (
        root: ts.SourceFile,
        objectLiteralExp: ts.ObjectLiteralExpression,
        defaultNodeName?: string,
    ): Maybe<AccessBinding> => {
        const node =
            PageMetadataBuilder.getStringValueFromObjectLiteralByKey(root, objectLiteralExp, 'node') || defaultNodeName;
        const bind = PageMetadataBuilder.getStringValueFromObjectLiteralByKey(root, objectLiteralExp, 'bind');
        return { node, bind };
    };

    static getComponentAttributes = (
        root: ts.SourceFile,
        objectLiteralExp: ts.ObjectLiteralExpression,
        extendedNode?: string,
    ): ComponentAttributes => {
        const findAccessBindingProperty = (propertyName: string): ts.PropertyAssignment | undefined => {
            if (!objectLiteralExp.properties) return undefined;
            return objectLiteralExp.properties.find(p => p.name && p.name.getText(root) === propertyName) as
                | ts.PropertyAssignment
                | undefined;
        };

        // Get the access property
        const accessProperty = findAccessBindingProperty('access');
        // If this is a page extension, get the extension access binding
        const extensionAccessBindingProperty = findAccessBindingProperty('extensionAccessBinding');

        return {
            access:
                (accessProperty &&
                    accessProperty.initializer &&
                    ts.isObjectLiteralExpression(accessProperty.initializer) &&
                    PageMetadataBuilder.getAccessBindingFromObjectLiteralExpression(
                        root,
                        accessProperty.initializer,
                        extendedNode,
                    )) ||
                undefined,
            extensionAccess:
                (extensionAccessBindingProperty &&
                    extensionAccessBindingProperty.initializer &&
                    ts.isObjectLiteralExpression(extensionAccessBindingProperty.initializer) &&
                    PageMetadataBuilder.getAccessBindingFromObjectLiteralExpression(
                        root,
                        extensionAccessBindingProperty.initializer,
                        extendedNode,
                    )) ||
                undefined,
            node:
                PageMetadataBuilder.getStringValueFromObjectLiteralByKey(root, objectLiteralExp, 'node') ||
                extendedNode,
            bind: PageMetadataBuilder.getStringValueFromObjectLiteralByKey(root, objectLiteralExp, 'bind'),
            valueField: PageMetadataBuilder.getStringValueFromObjectLiteralByKey(root, objectLiteralExp, 'valueField'),
            tunnelPage: PageMetadataBuilder.getStringValueFromObjectLiteralByKey(root, objectLiteralExp, 'tunnelPage'),
            fragment: PageMetadataBuilder.getStringValueFromObjectLiteralByKey(root, objectLiteralExp, 'fragment'),
            helperTextField: PageMetadataBuilder.getStringValueFromObjectLiteralByKey(
                root,
                objectLiteralExp,
                'helperTextField',
            ),
            isTransient: PageMetadataBuilder.getBooleanValueFromObjectLiteralByKey(
                root,
                objectLiteralExp,
                'isTransient',
            ),
        };
    };

    static resolvedBinding = (component: ComponentAttributes, nodeName?: string, pageNode?: string): AccessBinding => {
        return {
            node: component.access?.node || nodeName || pageNode,
            bind: component.access?.bind || component.bind || '$read',
        };
    };

    static getAuthorizationCodeFromObjectLiteralExpression = (
        root: ts.SourceFile,
        objectLiteralExp: ts.ObjectLiteralExpression,
    ): string | undefined =>
        PageMetadataBuilder.getStringValueFromObjectLiteralByKey(root, objectLiteralExp, 'authorizationCode');

    static getCategoryFromObjectLiteralExpression = (
        root: ts.SourceFile,
        objectLiteralExp: ts.ObjectLiteralExpression,
    ): string | undefined =>
        PageMetadataBuilder.getStringValueFromObjectLiteralByKey(root, objectLiteralExp, 'category');

    static getGroupFromObjectLiteralExpression = (
        root: ts.SourceFile,
        objectLiteralExp: ts.ObjectLiteralExpression,
    ): string | undefined => PageMetadataBuilder.getStringValueFromObjectLiteralByKey(root, objectLiteralExp, 'group');

    static getDescriptionFromObjectLiteralExpression = (
        root: ts.SourceFile,
        objectLiteralExp: ts.ObjectLiteralExpression,
    ): string | undefined =>
        PageMetadataBuilder.getStringValueFromObjectLiteralByKey(root, objectLiteralExp, 'description');

    static getListIconFromObjectLiteralExpression = (
        root: ts.SourceFile,
        objectLiteralExp: ts.ObjectLiteralExpression,
    ): string | undefined =>
        PageMetadataBuilder.getStringValueFromObjectLiteralByKey(root, objectLiteralExp, 'listIcon');

    static getExtendsFromObjectLiteralExpression = (
        root: ts.SourceFile,
        objectLiteralExp: ts.ObjectLiteralExpression,
    ): string | undefined =>
        PageMetadataBuilder.getStringValueFromObjectLiteralByKey(root, objectLiteralExp, 'extends');

    static getPriorityFromObjectLiteralExpression = (
        root: ts.SourceFile,
        objectLiteralExp: ts.ObjectLiteralExpression,
    ): number | undefined =>
        PageMetadataBuilder.getNumberValueFromObjectLiteralByKey(root, objectLiteralExp, 'priority');

    static resolveMenuItemId = (
        root: ts.SourceFile,
        packageRoot: string,
        buildDir: string,
        importPath: string,
        importedVariable: string,
    ) => {
        // Temporary hack for ESM - will need to be solidified later
        // ESM cannot require the menu-item js file so, instead of requiring the file
        // we read the file and extract the id of the menu item id with a regex.
        // This is easy as we only need the id of the menu item.
        const parseMenuItemId = (filePath: string): string => {
            const contents = fs.readFileSync(/\.c?js$/.test(filePath) ? filePath : `${filePath}.js`, 'utf8');
            const matches = /(?:export const |exports\.)(\w+) = \{\n\s+id: '([\w/@-]+)'/.exec(contents);
            if (matches?.length !== 3) throw new LogicError(`${filePath}: menu variable not found`);
            if (matches[1] !== importedVariable)
                throw new LogicError(`${filePath}: bad menu variable: expected ${importedVariable}, got ${matches[1]}`);
            return matches[2];
        };
        if (importPath.startsWith('.')) {
            const resolvedPath = path.resolve(path.dirname(root.fileName), importPath);
            const pathWithinPackage = resolvedPath.replace(`${packageRoot}${path.sep}`, '');
            const importedFilePath = path.resolve(buildDir, pathWithinPackage);
            return parseMenuItemId(importedFilePath);
        }

        return parseMenuItemId(require.resolve(importPath, { paths: [packageRoot] }));
    };

    /**
     * Typescript visitor
     * @param ctx the transformation context
     */
    static visitor = (
        root: ts.SourceFile,
        pageNode: string,
        pageAttributes: ComponentAttributes,
        nodeName: string | undefined,
        plugins: string[],
        fragments: string[],
        nodes: string[],
        ctx: ts.TransformationContext,
    ): ((node: ts.Node) => ts.Node) => {
        return (node: ts.Node) => {
            const attributes = PageMetadataBuilder.getComponentAttributes(root, node as ts.ObjectLiteralExpression);

            if (attributes?.node) {
                nodes.push(attributes?.node);
            }

            if (attributes?.access?.node) {
                nodes.push(attributes?.access?.node);
            }

            // we are declaring a field
            if (ts.isPropertyDeclaration(node)) {
                // this is a decorated property
                const decorator = ts.getDecorators(node)?.[0];
                const decoratorCallExpression =
                    decorator && ts.isCallExpression(decorator.expression) ? decorator.expression : undefined;

                // get the decorator argument
                const fieldPropertiesObjectExpression =
                    decoratorCallExpression && ts.isObjectLiteralExpression(decoratorCallExpression.arguments[0])
                        ? (decoratorCallExpression.arguments[0] as ts.ObjectLiteralExpression)
                        : undefined;

                // get the field access attributes
                const fieldAttributes = fieldPropertiesObjectExpression
                    ? PageMetadataBuilder.getComponentAttributes(root, fieldPropertiesObjectExpression)
                    : undefined;

                if (fieldAttributes?.node) {
                    nodes.push(fieldAttributes?.node);
                }

                if (fieldAttributes?.access?.node) {
                    nodes.push(fieldAttributes?.access?.node);
                }

                // We know that the node is a property declaration so we can get text of the name
                const bind = PageMetadataBuilder.getTsNodeText(root, node.name);

                const typeName =
                    (node.type &&
                        ts.isTypeReferenceNode(node.type) &&
                        PageMetadataBuilder.getTsNodeText(root, node.type.typeName)) ||
                    '';

                // verification of page action names
                if (bind && isPageAction(typeName)) {
                    // If a page action end with `Action` then we print a warning that it should not
                    if (/Action$/.test(bind)) {
                        throw new LogicError(
                            `Page action name '${bind}' must not end with 'Action'. Do you mean '${bind.slice(
                                0,
                                -6,
                            )}'? at ${PageMetadataBuilder.getLocation(root, node.name)}`,
                        );
                    }
                }

                // Extract plugins from pluginField decorator
                if (
                    decoratorCallExpression &&
                    fieldPropertiesObjectExpression &&
                    decoratorCallExpression.getText(root).includes('.pluginField<')
                ) {
                    fieldPropertiesObjectExpression.properties
                        .filter(p => p.name?.getText(root) === 'pluginPackage')
                        .forEach((p: ts.PropertyAssignment) => {
                            if (ts.isStringLiteral(p.initializer) && !plugins.includes(p.initializer.text)) {
                                plugins.push(p.initializer.text);
                            }
                        });
                }
            }

            if (attributes?.fragment) {
                fragments.push(attributes.fragment);
            }

            return ts.visitEachChild(
                node,
                PageMetadataBuilder.visitor(
                    root,
                    pageNode || '',
                    pageAttributes || {},
                    attributes?.node || nodeName || '',
                    plugins,
                    fragments,
                    nodes,
                    ctx,
                ),
                ctx,
            );
        };
    };
}

/**
 * This transformer is meant to be run AFTER the 'decorator-transformer' for pages/stickers &
 * for every source file of an xtrem package
 * @param ctx the transformation context
 * @returns the transformed file
 */
export function pageMetadataTransformer(ctx: ts.TransformationContext): ts.Transformer<ts.SourceFile> {
    return file => {
        if (!PageMetadataBuilder.isSourceFile(file)) {
            return file;
        }
        const nodes: string[] = [];
        const members = (file.statements.filter(ts.isClassDeclaration) || []) as ts.ClassDeclaration[];
        const classDeclaration = members.pop();

        // If the source file does not contain a decorated class return the file
        if (!classDeclaration) return file;

        const decorator = ts.getDecorators(classDeclaration)?.[0];
        if (!(decorator && ts.isCallExpression(decorator.expression))) {
            return file;
        }

        try {
            // decorator call expression
            const callExpression = decorator.expression as ts.CallExpression;
            let pageNode: string | undefined;

            // decorator has at least one argument
            if (
                callExpression.arguments &&
                callExpression.arguments.length > 0 &&
                ts.isObjectLiteralExpression(callExpression.arguments[0])
            ) {
                // page decorator first argument
                const decoratorObject = callExpression.arguments[0] as ts.ObjectLiteralExpression;

                // name of the page class
                const className = classDeclaration.name?.getText(file);
                if (!className) throw new LogicError('page class does not have a name');

                // authorizationCode of the page from the page decorator argument
                const authorizationCode = PageMetadataBuilder.getAuthorizationCodeFromObjectLiteralExpression(
                    file,
                    decoratorObject,
                );
                // category of the page from the page decorator argument
                const category = PageMetadataBuilder.getCategoryFromObjectLiteralExpression(file, decoratorObject);
                const group = PageMetadataBuilder.getGroupFromObjectLiteralExpression(file, decoratorObject);
                // name of node being extended from the page decorator argument
                const extendedPage = PageMetadataBuilder.getExtendsFromObjectLiteralExpression(file, decoratorObject);
                // category of the page from the page decorator argument
                const listIcon = PageMetadataBuilder.getListIconFromObjectLiteralExpression(file, decoratorObject);
                // name of node being extended from the page decorator argument
                const description = PageMetadataBuilder.getDescriptionFromObjectLiteralExpression(
                    file,
                    decoratorObject,
                );

                // List of used plugins that will be extracted from page definition
                const plugins: string[] = [];

                const fragments: string[] = [];

                // page access attributes
                const pageAttributes = PageMetadataBuilder.getComponentAttributes(file, decoratorObject);

                // resolve page access
                const resolved = PageMetadataBuilder.resolvedBinding(
                    pageAttributes,
                    pageAttributes.node,
                    pageAttributes.node,
                );

                if (pageAttributes.node) {
                    nodes.push(pageAttributes.node);
                }

                pageNode = resolved.node;
                let pageAccess;

                const extensionAccess = pageAttributes.extensionAccess;

                if (resolved.node) {
                    nodes.push(resolved.node);
                    pageAccess = resolved;
                }

                // Walk souce file tree extracting access bindings and plugins
                ts.visitNode(
                    file,
                    PageMetadataBuilder.visitor(
                        file,
                        pageNode || '',
                        pageAttributes,
                        '',
                        plugins,
                        fragments,
                        nodes,
                        ctx,
                    ),
                );

                // build page metadata
                const artifactMetaData: any = {
                    authorizationCode,
                    category,
                    className,
                    description,
                    extends: extendedPage,
                    extensionAccess,
                    fragments,
                    group,
                    listIcon,
                    md5: hashFile(file.fileName),
                    nodes: uniq(nodes),
                    pageAccess,
                    pageNode: pageAttributes.node,
                    plugins,
                };

                if (ts.isCallExpression(callExpression) && ts.isPropertyAccessExpression(callExpression.expression)) {
                    artifactMetaData.type = callExpression.expression.name.getText(file);
                }

                // Root folder of the package the page belongs to
                const nameAndRoot = getPackageNameAndRoot(file.fileName);
                // path to package.json in package root
                const packageJsonFilePath: string = path.resolve(nameAndRoot.root, 'package.json');
                // package.json content
                // eslint-disable-next-line import/no-dynamic-require,global-require
                const packageFileContent = require(packageJsonFilePath);
                // extract relevant package info
                const packageInfo = pick(packageFileContent, 'main', 'name');
                //  derive build directory for package root and main folder
                const buildDir = path.dirname(path.resolve(nameAndRoot.root, packageInfo.main || './build/index.js'));

                // Extract parent menu item
                const menuItem = decoratorObject.properties.find(
                    (p: ts.PropertyAssignment) => p.name.getText(file) === 'menuItem',
                ) as ts.PropertyAssignment | undefined;

                if (menuItem) {
                    const importedVariable = menuItem?.initializer.getText(file);
                    const importClause = file.statements.find(
                        s =>
                            ts.isImportDeclaration(s) &&
                            s.importClause?.namedBindings &&
                            ts.isNamedImports(s.importClause?.namedBindings) &&
                            s.importClause?.namedBindings.elements.find(n => n.getText(file) === importedVariable),
                    ) as ts.ImportDeclaration;
                    const importText = importClause.moduleSpecifier.getText(file);
                    const importPath = trim(importText, "'");
                    if (!importPath.endsWith('/_index')) {
                        const menuItemId = PageMetadataBuilder.resolveMenuItemId(
                            file,
                            nameAndRoot.root,
                            buildDir,
                            importPath,
                            importedVariable,
                        );
                        artifactMetaData.parentMenuItem = menuItemId;
                        artifactMetaData.priority = PageMetadataBuilder.getPriorityFromObjectLiteralExpression(
                            file,
                            decoratorObject,
                        );
                    }
                }

                // Write extracted data to the build folder
                const metadataFilePath = getMetadataFilePathForClientFile(nameAndRoot.root, file.fileName);
                mkdirp(path.dirname(metadataFilePath));
                const dataToSave = fs.existsSync(metadataFilePath)
                    ? { ...JSON.parse(fs.readFileSync(metadataFilePath, 'utf-8')), ...artifactMetaData }
                    : artifactMetaData;
                //  write back meta.json file
                fs.writeFileSync(metadataFilePath, JSON.stringify(dataToSave, null, 4), 'utf-8');
            }

            return file;
        } catch (err) {
            throw new Error(`${file.fileName}: \n ${err.stack}`);
        }
    };
}
