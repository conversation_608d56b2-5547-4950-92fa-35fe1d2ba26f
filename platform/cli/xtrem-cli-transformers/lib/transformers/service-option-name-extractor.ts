import { getPackageNameAndRoot } from '@sage/xtrem-i18n';
import { Dict, titleCase } from '@sage/xtrem-shared';
import { snakeCase } from 'lodash';
import * as ts from 'typescript';
import { getDirName } from '../utils';
import { createDictionaryKey, isServiceOptionFile, isTestFile, writeStringLiteralsToBase } from './transformer-utils';

const excludedAstTypes = new Set([
    ts.SyntaxKind.ImportDeclaration,
    ts.SyntaxKind.TypePredicate,
    ts.SyntaxKind.TypeReference,
]);

/**
 * Top-level TS visitor
 * @param prefix prefix for translation keys
 */
export const visitor =
    (dictionary: Dict<string>, packageName: string, dirName: string, filename: string, ctx: ts.TransformationContext) =>
    (node: ts.Node): ts.Node => {
        if (excludedAstTypes.has(node.kind)) {
            return node;
        }

        if (ts.isVariableStatement(node) && dirName === 'service-options') {
            const declaration = node.declarationList.declarations.find(
                decl =>
                    decl.initializer &&
                    ts.isNewExpression(decl.initializer) &&
                    ts.isIdentifier(decl.initializer.expression) &&
                    decl.initializer.expression.text === 'ServiceOption',
            );
            if (declaration && ts.isIdentifier(declaration.name)) {
                const serviceOptionName = declaration.name.text;
                const key = `${packageName}/${createDictionaryKey(
                    'service_options',
                    snakeCase(serviceOptionName),
                )}__name`;
                dictionary[key] = titleCase(serviceOptionName);

                return node;
            }
        }

        return ts.visitEachChild(node, visitor(dictionary, packageName, dirName, filename, ctx), ctx);
    };

/**
 * This transformer is meant to be run BEFORE the 'message-transformer' and its purpose is
 * to wrap some page/sticker decorator properties (see 'includedProperties') with a call to the 'ui.localize' function.
 *
 * @param ctx transformation context
 * @returns the transformed file
 */
export function serviceOptionNameExtractor(ctx: ts.TransformationContext): ts.Transformer<ts.SourceFile> {
    return (file: ts.SourceFile) => {
        if (!isServiceOptionFile(file) || isTestFile(file)) {
            return file;
        }

        const filename = file.fileName;
        try {
            const dictionary = {} as Dict<string>;
            const dirName = getDirName(file, true);
            const nameAndRoot = getPackageNameAndRoot(filename);
            const result = ts.visitNode(file, visitor(dictionary, nameAndRoot.name, dirName, filename, ctx));
            writeStringLiteralsToBase(dictionary, nameAndRoot.root);
            return result as ts.SourceFile;
        } catch (err) {
            throw new Error(`${filename}: decorator transformer failed: ${err.message}`);
        }
    };
}
