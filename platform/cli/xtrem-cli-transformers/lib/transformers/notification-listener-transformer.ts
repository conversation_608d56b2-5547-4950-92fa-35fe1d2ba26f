import * as path from 'path';
import * as ts from 'typescript';
import { isSubNodeDecorator } from '..';
import * as logger from './notification-listener-routing/logger';
import { RoutingBuilder } from './notification-listener-routing/routing-builder';

const notificationListenerTransformer: ts.TransformerFactory<ts.SourceFile> = (
    context: ts.TransformationContext,
): ts.Transformer<ts.SourceFile> => {
    const rootDir = context.getCompilerOptions().rootDir;
    const routingBuilder = new RoutingBuilder(rootDir);

    return sourceFile => {
        if (!isNodeLikeArtifactFile(sourceFile)) {
            return sourceFile;
        }

        routingBuilder.setVisitingFile(sourceFile.fileName);

        const visitor = (node: ts.Node): ts.Node => {
            let visitingNodeDecorator = false;
            try {
                if (isListenerDecorator(node, 'notificationListener')) {
                    const listener = routingBuilder.createListener(node);
                    routingBuilder.updateRouting(listener);
                    routingBuilder.writeRoutingData();
                } else if (isListenerDecorator(node, 'asyncMutation') || isListenerDecorator(node, 'bulkMutation')) {
                    const listener = routingBuilder.createAsyncMutationListener(node);
                    routingBuilder.updateRouting(listener);
                    routingBuilder.writeRoutingData();
                } else if (isNodeDecorator(node) || isSubNodeDecorator(node)) {
                    visitingNodeDecorator = true;
                    routingBuilder.setVisitingNodeDecorator(node);
                    routingBuilder.createListeners();
                }
                return ts.visitEachChild(node, visitor, context);
            } catch (err) {
                const { line, character } = sourceFile.getLineAndCharacterOfPosition(node.getStart());
                logger.error(err);
                logger.error(`fileName: ${sourceFile.fileName}:${line + 1}:${character + 1}`);
                logger.error(`nodeText: ${node.getText()}`);
                throw err;
            } finally {
                if (visitingNodeDecorator) {
                    routingBuilder.setVisitingNodeDecorator(undefined);
                }
            }
        };

        return ts.visitNode(sourceFile, visitor) as ts.SourceFile;
    };
};

export function isNodeLikeArtifactFile(file: ts.SourceFile): boolean {
    const filePath = file.fileName;
    const fileName = path.basename(filePath, '.ts');
    return (
        fileName !== 'index' &&
        fileName !== '_index' &&
        !/\/(x3-services|wh-services|wms|test|fixtures)\//.test(filePath) &&
        /[/\\]lib[/\\](nodes|node-extensions)/.test(filePath)
    );
}

function isListenerDecorator(
    node: ts.Node,
    name: 'notificationListener' | 'asyncMutation' | 'bulkMutation',
): node is ts.CallExpression {
    return (
        ts.isCallExpression(node) &&
        ts.isDecorator(node.parent) &&
        ts.isPropertyAccessExpression(node.expression) &&
        node.expression.name.escapedText === name
    );
}

function isNodeDecorator(node: ts.Node): node is ts.Decorator {
    return (
        ts.isDecorator(node) &&
        ts.isClassDeclaration(node.parent) &&
        ts.isCallExpression(node.expression) &&
        ts.isPropertyAccessExpression(node.expression.expression) &&
        node.expression.expression.getText() === 'decorators.node'
    );
}

export { notificationListenerTransformer };
