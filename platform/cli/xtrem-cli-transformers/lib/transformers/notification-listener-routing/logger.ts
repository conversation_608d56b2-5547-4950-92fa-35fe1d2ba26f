const chalk = require('chalk');

function printInfoMessage(message: string) {
    console.log(chalk.bold.blue('[Notification Listener Routing]'), message);
}

function printErrorMessage(message: string) {
    console.error(chalk.bold.blue('[Notification Listener Routing]'), message);
}

export { printInfoMessage as info };
export { printErrorMessage as error };

if (require.main === module) {
    const message = process.argv[2];
    if (message) {
        printInfoMessage(message);
    }
}
