import { Dict, LogicError, limitSqsQueueName } from '@sage/xtrem-shared';
import * as fs from 'fs';
import * as _ from 'lodash';
import * as os from 'os';
import * as fsp from 'path';
import * as logger from './logger';

export interface Route {
    queue: string;
    topic: string;
    sourceFileName: string;
}

export interface Routing {
    [packageName: string]: Route[];
}

export function mergeRoutings(routing1: Routing, routing2: Routing): Routing {
    return sortRoutesByTopic(_.mergeWith(routing1, routing2, mergeRoutes));
}

function mergeRoutes(routes1: Route[], routes2: Route[]): Route[] {
    return _.unionWith(routes1, routes2, _.isEqual);
}

export function sortRoutesByTopic(routing: Routing): Routing {
    // Sort each route array by topic
    Object.keys(routing).forEach(packageName => {
        routing[packageName].sort((a, b) => a.topic.localeCompare(b.topic));
    });
    return routing;
}

export function getRoutingData(routingFile: string) {
    logger.info(`Getting data from file ${routingFile}`);

    const rawRoutingData = fs.readFileSync(routingFile, 'utf-8');
    return JSON.parse(rawRoutingData);
}

export function getRoutingFilePath(packageDir: string) {
    return fsp.join(packageDir, 'routing.json');
}

function getQueues(routing: Routing): string[] {
    const queueList = new Set<string>();
    Object.values(routing).forEach(routes => {
        const queues = _.uniq(routes.map(route => route.queue));
        queues.forEach(queue => queueList.add(queue));
    });

    return [...queueList];
}

export interface QueueConfig {
    deadLetterQueue?: { name: string; maxReceiveCount: number };
    defaultVisibilityTimeout: string;
    delay: string;
    fifo?: string;
    receiveMessageWait: string;
}

const defaultVisibilityTimeout = '30 seconds';
const delay = '0 seconds';
const receiveMessageWait = '0 seconds';
const fifo = 'true';
const deadLetterQueueName = 'dev-dead-letter-queue';

/**
 * Generates the Elastic Queue configuration for a given queue name.
 * @param queueName - The name of the queue.
 * @returns The Elastic Queue configuration.
 */
function elasticQueueConfig(queueName: string): Dict<QueueConfig> {
    const deadLetterQueue =
        queueName === deadLetterQueueName ? undefined : { name: deadLetterQueueName, maxReceiveCount: 5 };

    return {
        [queueName]: {
            defaultVisibilityTimeout,
            delay,
            receiveMessageWait,
            fifo,
            deadLetterQueue,
        },
    };
}

/**
 * Retrieves all ElasticMQ configurations based on the provided application and routing.
 * Optionally, a test package name can be specified to generate unit test queues.
 *
 * @param application - The application object.
 * @param routing - The routing object.
 * @param testPackageName - The name of the optional test package.
 * @returns A dictionary of queue configurations.
 */
export function getAllElasticMqConfigs(
    dir: string,
    routing: Routing,
    testPackageName?: string,
    additionalQueues?: Dict<QueueConfig>,
): Dict<QueueConfig> {
    const queues = {
        ...getQueues(routing).reduce((r, k) => {
            // Prefix with test-${testPackageName} to generate the unit test queues
            // This name must match what we generate in application.sqsQueueName when application.applicationType is 'test'
            // we cannot use that method here because application.applicationType is 'dev-tool'.
            const queueConfig = elasticQueueConfig(
                limitSqsQueueName(testPackageName ? `test-${testPackageName}-${k}` : k),
            );
            const queueName = Object.keys(queueConfig)[0];
            if (r[queueName]) {
                throw new LogicError(`Duplicate queue name ${queueName} in routing file`);
            }
            if (additionalQueues && additionalQueues[queueName]) {
                throw new LogicError(`Duplicate queue name ${queueName} in additional queues`);
            }
            r[queueName] = queueConfig[queueName];
            return r;
        }, {} as Dict<QueueConfig>),
        ...additionalQueues,
    };

    if (!testPackageName) {
        // Add queue variants prefixed by the app name.
        // We have to generate all the queue names regardless of whether the 'app' key is present in the config file.
        let appName = dir.split('/').at(-3);
        // Special hacks to guess the app name from the directory structure.
        if (appName === 'services') appName = 'sdmo';
        else if (appName === 'tools') appName = dir.split('/').at(-2);

        Object.keys(queues).forEach(queueName => {
            if (!appName) throw new LogicError('Application directory does not match expected format');
            if (!queueName.startsWith(`${appName}--`)) {
                queues[limitSqsQueueName(`${appName}--${queueName}`)] = queues[queueName];
            }
        });
    }
    return queues;
}

/**
 * Sorts the ElasticMQ configurations based on their keys in ascending order.
 *
 * @param queues - The dictionary of queues to be sorted.
 * @returns The sorted dictionary of queues.
 */
function sortElasticMqConfigs(queues: Dict<QueueConfig>): Dict<QueueConfig> {
    const sortedKeys = Object.keys(queues).sort((a, b) => a.localeCompare(b));
    return _.pick(queues, sortedKeys);
}

/**
 * Writes the ElasticMQ configuration to a file.
 *
 * @param filePath - The path of the file to write the configuration to.
 * @param queues - The dictionary of queue configurations.
 */
function writeElasticMqConfigFile(filePath: string, queues: Dict<QueueConfig>): void {
    const include = 'include classpath("application.conf")';
    const queuesStr = JSON.stringify(sortElasticMqConfigs(queues), null, '\t')
        .replace(/: \{/g, ' {')
        .replace(/:/g, ' =')
        .replace(/[",]/g, '');
    const text = `${include}\n\nqueues ${queuesStr}\n`;
    fs.writeFileSync(filePath, text, 'utf-8');
}

/**
 * Reads the ElasticMQ configuration from the specified root path.
 * @param path The path of the configuration file.
 * @returns A dictionary of queue configurations.
 */
function readElasticMqConfigFile(path: string): Dict<QueueConfig> {
    if (!fs.existsSync(path)) return {};
    const text = fs.readFileSync(path, 'utf-8');
    let json = text
        .replace(/^include.*/, '')
        .replace(/\nqueues\s+{/, '{')
        .replace(/(\n\t+)((?:\w|-)+) \{/g, `$1"$2": {`)
        .replace(/(\n\t+)((?:\w|-)+) = (.*)/g, `$1"$2": "$3",`)
        .replace(/([}"])\n/g, `$1,\n`)
        .replace(/,(\n\t*)\}/g, '$1}')
        .trimEnd();
    if (json.slice(-1) === ',') json = json.slice(0, -1);
    return JSON.parse(json);
}

/**
 * Updates the ElasticMQ configuration with the provided queues.
 *
 * @param {string} projectConfigDir - The path to the project root dir where the local ElasticMq configuration file will be saved.
 * @param {Dict<QueueConfig>} queues - The queues to be added or updated in the configuration.
 * @returns {void}
 */
export function updateElasticMqConfigFile(
    workspaceId: string,
    projectConfigDir: string,
    queues: Dict<QueueConfig>,
): void {
    if (!fs.existsSync(projectConfigDir)) {
        throw new LogicError(
            `Local conf dir ${projectConfigDir} does not exist. It should be the root of the project.`,
        );
    }

    // Merge the queues with the existing ones in the project config dir
    // Required for CI and local dev when we get builds from the cache to ensure we have all queues
    const projectConfigFile = fsp.join(projectConfigDir, 'elasticmq.conf');
    const oldProjectQueues = readElasticMqConfigFile(projectConfigFile);
    writeElasticMqConfigFile(projectConfigFile, _.merge(oldProjectQueues, queues));
    updateSharedElasticMqConfigFile(workspaceId, queues);
}

function updateSharedElasticMqConfigFile(workspaceId: string, queues: Dict<QueueConfig>): void {
    // The shared conf dir is used to store the queues for all workspaces to use a single conf file
    // for multi apps/repos hosted on the same machine
    const sharedConfDir = fsp.join(os.homedir(), '.cache', 'elasticmq');
    if (!fs.existsSync(sharedConfDir)) {
        fs.mkdirSync(sharedConfDir, { recursive: true });
    }
    // create a new dict of queues prefixed with the workspaceId
    const newQueues: Dict<QueueConfig> = {};
    Object.keys(queues).forEach(sqsName => {
        const queueConfig = queues[sqsName];
        if (queueConfig) {
            const newSqsName = limitSqsQueueName(`${workspaceId}--${sqsName}`);
            newQueues[newSqsName] = queueConfig;
        }
    });

    const sharedConfFile = fsp.join(sharedConfDir, 'elasticmq.conf');
    const oldSharedQueues = readElasticMqConfigFile(sharedConfFile);
    writeElasticMqConfigFile(sharedConfFile, _.merge(oldSharedQueues, newQueues));
}

export function syncSharedElasticMqConfigFile(workspaceId: string, projectConfigDir: string): void {
    const projectQueues = readElasticMqConfigFile(fsp.join(projectConfigDir, 'elasticmq.conf'));
    updateSharedElasticMqConfigFile(workspaceId, projectQueues);
}

export const requiredQueues = ['infrastructure-event', 'intacctReceive'].reduce(
    (acc, queueName) => ({ ...acc, ...elasticQueueConfig(queueName) }),
    {} as Dict<QueueConfig>,
);

export const defaultElasticMq = {
    queues: {
        ...elasticQueueConfig(deadLetterQueueName),
        ...elasticQueueConfig('demo-queue'),
        ...requiredQueues,
        ...elasticQueueConfig('unit-test-message-queue'),
        ...elasticQueueConfig('unit-test-notification-queue'),
        ...elasticQueueConfig('unit-test-visibility-multiple-listeners-queue'),
    },
};
