import { PackageJsonFile, getPackageName, getPackageQueueName } from '@sage/xtrem-shared';
import * as fs from 'fs';
import * as _ from 'lodash';
import * as path from 'path';
import * as ts from 'typescript';
import { canExportNodeDecorator, isAbstractNodeDecorator } from '../../utils';
import * as logger from './logger';
import { Route, Routing, getRoutingFilePath, sortRoutesByTopic } from './routing';

export interface Listener {
    fileName: string;
    packageName: string;
    queue: string;
    topic: string;
}

export class RoutingBuilder {
    public readonly routingFilename: string;

    public readonly packageJson: PackageJsonFile = this.getPackageJson();

    private readonly mutationNames = [] as string[];

    readonly #routing: Routing;

    #packageQueueName?: string;

    #nodeQueueName?: string;

    #currentFilePath = '';

    #currentFilename = '';

    #nodeDecorator?: ts.Decorator;

    #dirty = false;

    constructor(private readonly rootDir: string | undefined) {
        const routingFileDir = this.rootDir || process.cwd();
        this.routingFilename = getRoutingFilePath(routingFileDir);
        this.#routing = {
            [this.packageName]: RoutingBuilder.readRoutingData(this.routingFilename)[this.packageName] || [],
        };
    }

    get packageName(): string {
        return getPackageName(this.packageJson);
    }

    get queue(): string {
        if (!this.#packageQueueName) {
            this.#packageQueueName = this.getPackageQueueName();
        }
        return this.#nodeQueueName ?? this.#packageQueueName;
    }

    get routing(): Routing {
        return this.#routing;
    }

    get filename(): string {
        return this.#currentFilename;
    }

    get filePath(): string {
        return this.#currentFilePath;
    }

    setVisitingFile(filePath: string) {
        this.#currentFilePath = filePath;
        this.#currentFilename = path.basename(filePath);
        this.setVisitingNodeDecorator(undefined);
    }

    get nodeQueueName(): string | undefined {
        return this.#nodeQueueName;
    }

    set nodeQueueName(queueName: string | undefined) {
        this.#nodeQueueName = queueName;
    }

    get node(): ts.Decorator | undefined {
        return this.#nodeDecorator;
    }

    setVisitingNodeDecorator(node: ts.Decorator | undefined) {
        this.#nodeDecorator = node;
        this.#nodeQueueName = undefined;
        const { filename, mutationNames } = this;
        if (!node) {
            return;
        }
        if (!ts.isCallExpression(node.expression)) {
            throw new Error(`${filename}: expected CallExpression, got ${ts.SyntaxKind[node.expression.kind]}`);
        }
        const decoratorArgument = node.expression.arguments[0];
        if (!decoratorArgument || !ts.isObjectLiteralExpression(decoratorArgument)) {
            throw new Error(
                `${filename}: expected ObjectLiteralExpression, got ${
                    decoratorArgument && ts.SyntaxKind[decoratorArgument.kind]
                }`,
            );
        }

        decoratorArgument.properties.forEach(property => {
            if (ts.isPropertyAssignment(property) && ts.isIdentifier(property.name)) {
                const propertyName = property.name;
                if (property.initializer.kind === ts.SyntaxKind.TrueKeyword) {
                    const m = /^canBulk(Delete|Update)$/.exec(propertyName.escapedText.toString());
                    if (m) {
                        mutationNames.push(`bulk${m[1]}`);
                    }
                }
                if (ts.isStringLiteral(property.initializer) && propertyName.escapedText === 'queue') {
                    this.#nodeQueueName = property.initializer.text;
                }
            }
        });
    }

    private static readRoutingData(routingFileName: string) {
        if (!fs.existsSync(routingFileName)) {
            return {};
        }
        const routingRawData = fs.readFileSync(routingFileName, 'utf-8');
        const routing: Routing = JSON.parse(routingRawData);
        return routing;
    }

    private getPackageQueueName(): string {
        return getPackageQueueName(this.packageJson);
    }

    private getPackageJson(): PackageJsonFile {
        const { rootDir, routingFilename: routingFileName } = this;
        const packageRootDir = rootDir || RoutingBuilder.getPackageRootDir(routingFileName);
        const packageJsonPath = path.join(packageRootDir, 'package.json');
        if (!fs.existsSync(packageJsonPath)) {
            throw new Error(`package.json not found, rootDir: ${rootDir}, fileName: ${routingFileName}`);
        }
        const packageJsonData = fs.readFileSync(packageJsonPath, 'utf-8');
        return JSON.parse(packageJsonData);
    }

    private static getPackageRootDir(fileName: string): string {
        let packageRootDir = path.dirname(fileName);
        let packageJsonPath = path.join(packageRootDir, 'package.json');
        while (packageRootDir !== '/' && !fs.existsSync(packageJsonPath)) {
            packageRootDir = path.join(packageRootDir, '..');
            packageJsonPath = path.join(packageRootDir, 'package.json');
        }
        return packageRootDir;
    }

    createListener(node: ts.CallExpression) {
        const { packageName, filePath, queue } = this;
        const listener: Listener = {
            fileName: filePath,
            packageName,
            // keep this consistent with xtrem-communication/lib/core-extension/listeners.ts
            queue,
            topic: '',
        };
        const argument = node.arguments[0] as ts.ObjectLiteralExpression;
        const properties = argument.properties;
        properties.forEach(property => {
            const propertyName = property.name as ts.Identifier;
            if (propertyName.escapedText === 'queue') {
                listener.queue = RoutingBuilder.getQueue(property);
            } else if (propertyName.escapedText === 'topic') {
                listener.topic = RoutingBuilder.getTopic(property);
            }
        });
        return listener;
    }

    createAsyncMutationListener(node: ts.CallExpression): Listener {
        const { filePath } = this;
        const method = node.parent.parent;
        if (!ts.isMethodDeclaration(method))
            throw new Error(`${filePath}: expected MethodDeclaration, got ${ts.SyntaxKind[method.kind]}`);
        if (!ts.isIdentifier(method.name))
            throw new Error(`${filePath}: expected Identifier, got ${ts.SyntaxKind[method.name.kind]}`);
        const classDeclaration = method.parent;
        if (!ts.isClassDeclaration(classDeclaration))
            throw new Error(`${filePath}: expected ClassDeclaration, got ${ts.SyntaxKind[classDeclaration.kind]}`);

        return this.createAsyncMutationListenerObject(
            classDeclaration,
            method.name.text,
            RoutingBuilder.getAsyncMutationQueueName(method),
        );
    }

    createAsyncMutationListenerObject(
        classDeclaration: ts.ClassDeclaration,
        name: string,
        /**
         * Override of the default queue name (by default, the queue name is based on the package name)
         */
        queueNameOverride?: string,
    ): Listener {
        const { packageName, filePath } = this;
        return {
            fileName: filePath,
            packageName,
            // keep this consistent with xtrem-communication/lib/core-extension/listeners.ts
            queue: queueNameOverride ?? this.queue,
            topic: `${RoutingBuilder.getAsyncMutationClassName(filePath, classDeclaration)}/${name}/start`,
        };
    }

    private static getQueue(property: ts.ObjectLiteralElementLike): string {
        if (ts.isPropertyAssignment(property) && ts.isArrowFunction(property.initializer)) {
            return property.initializer.body.getText().split('.').pop() as string;
        }
        return '';
    }

    private static getTopic(property: ts.ObjectLiteralElementLike): string {
        if (ts.isPropertyAssignment(property) && ts.isStringLiteral(property.initializer)) {
            return property.initializer.text;
        }
        return '';
    }

    updateRouting(listener: Listener): void {
        const { routing, filename } = this;
        const route: Route = { ..._.pick(listener, ['topic', 'queue']), sourceFileName: filename };
        const packageName = listener.packageName;
        if (!routing[packageName] || !RoutingBuilder.isRouteInList(route, routing[packageName])) {
            logger.info(`New route found: (topic: ${route.topic}, queue: ${route.queue})`);
            routing[packageName].push(route);
            this.#dirty = true;
        }
    }

    private static isRouteInList(route: Route, routeList: Route[]) {
        const entryIndex = _.find(routeList, route);
        return entryIndex != null;
    }

    writeRoutingData() {
        if (!this.#dirty) return;
        const { routing, routingFilename: routingFileName } = this;
        sortRoutesByTopic(routing);
        fs.writeFileSync(routingFileName, JSON.stringify(routing, null, 4), 'utf-8');
        this.#dirty = false;
    }

    // Returns the topic of an async mutation listener.
    // This is a little tricky if the listener is defined in a node extension.
    // In this case we need to get the class name from the `extends` attribute of the class decorator.
    private static getAsyncMutationClassName(fileName: string, classDeclaration: ts.ClassDeclaration) {
        if (!classDeclaration.name) throw new Error(`${fileName}: no name on node class declaration`);

        const classDecorator = ts.getDecorators(classDeclaration)?.[0]?.expression;
        if (
            !(
                classDecorator &&
                ts.isCallExpression(classDecorator) &&
                ts.isObjectLiteralExpression(classDecorator.arguments[0])
            )
        ) {
            throw new Error(`${fileName}: invalid node class: decorator not found`);
        }

        const classDecoratorArg = classDecorator.arguments[0];
        const extendsAtb = classDecoratorArg.properties.find(
            prop => prop.name && ts.isIdentifier(prop.name) && prop.name.text === 'extends',
        );

        // If the class does not extend another class, return its name
        if (!extendsAtb) return classDeclaration.name.text;
        // If the class is a subclass (not an extension), return its name
        if (
            ts.isPropertyAccessExpression(classDecorator.expression) &&
            classDecorator.expression.name.text === 'subNode'
        )
            return classDeclaration.name.text;

        // The class is an extension of another node, we have to return the other node's name
        const extendsArrow =
            extendsAtb &&
            ts.isPropertyAssignment(extendsAtb) &&
            ts.isArrowFunction(extendsAtb.initializer) &&
            extendsAtb.initializer;
        if (!extendsArrow) throw new Error(`${fileName}: extends attribute is not an arrow function`);

        // Handle the case where extends is () => SomeNode
        if (ts.isIdentifier(extendsArrow.body)) return extendsArrow.body.text;
        // Handle the case where extends is () => somePackage.nodes.SomeNode
        if (ts.isPropertyAccessExpression(extendsArrow.body) && ts.isIdentifier(extendsArrow.body.name))
            return extendsArrow.body.name.text;
        // No luck, throw an error
        throw new Error(`${fileName}: unexpected expression in extends decorator attribute`);
    }

    /**
     * Retrieve (if defined) the 'queue' parameter set in the decorator of the asyncMutation
     */
    private static getAsyncMutationQueueName(method: ts.MethodDeclaration): string | undefined {
        if (!method.modifiers || method.modifiers.length === 0) return undefined;
        const queueNames = method.modifiers
            .map(mod => {
                if (!ts.isDecorator(mod)) return null;
                if (!ts.isCallExpression(mod.expression)) return null;
                if (!ts.isPropertyAccessExpression(mod.expression.expression)) return null;
                if (!/asyncMutation|bulkMutation/.test(mod.expression.expression.name.text)) return null;
                if (mod.expression.arguments.length !== 1) return null;
                const decoratorArg = mod.expression.arguments[0];
                if (!ts.isObjectLiteralExpression(decoratorArg)) return null;
                const queueProps = decoratorArg.properties
                    .map(prop => {
                        if (!ts.isPropertyAssignment(prop)) return null;
                        if (!prop.name) return null;
                        if (!ts.isIdentifier(prop.name)) return null;
                        if (prop.name.text !== 'queue') return null;
                        if (!ts.isStringLiteral(prop.initializer)) return null;
                        return prop.initializer.text;
                    })
                    .filter(q => q);
                if (queueProps.length === 0) return null;
                return queueProps[0];
            })
            .filter(q => q);
        if (queueNames.length === 0) return undefined;
        return queueNames[0] as string;
    }

    createListeners(): void {
        this.mutationNames.forEach(name => {
            const listener = this.createStandardBulkMutationListener(name);
            this.updateRouting(listener);
        });
        const asyncExportListener = this.createAsyncExportMutationListener();
        if (asyncExportListener) {
            this.updateRouting(asyncExportListener);
        }
        this.writeRoutingData();
    }

    createStandardBulkMutationListener(name: string) {
        const { node, filePath } = this;
        if (!node) {
            throw new Error(`${filePath}: no current node decorator`);
        }
        const classDeclaration = node.parent;
        if (!ts.isClassDeclaration(classDeclaration))
            throw new Error(`${filePath}: expected ClassDeclaration, got ${ts.SyntaxKind[classDeclaration.kind]}`);

        return this.createAsyncMutationListenerObject(classDeclaration, name, this.nodeQueueName);
    }

    createAsyncExportMutationListener() {
        const { node, filePath } = this;
        if (!node) {
            throw new Error(`${filePath}: no current node decorator`);
        }
        const fileNameBase = this.filename;
        const canExport = canExportNodeDecorator(filePath, node);
        if (!isAbstractNodeDecorator(fileNameBase, node) && canExport) {
            const classDeclaration = node.parent;
            if (!ts.isClassDeclaration(classDeclaration))
                throw new Error(`${filePath}: expected ClassDeclaration, got ${ts.SyntaxKind[classDeclaration.kind]}`);

            return this.createAsyncMutationListenerObject(classDeclaration, 'asyncExport', 'import-export');
        }
        return undefined;
    }
}
