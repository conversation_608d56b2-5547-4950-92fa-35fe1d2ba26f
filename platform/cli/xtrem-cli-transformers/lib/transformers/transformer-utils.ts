import { Dict } from '@sage/xtrem-shared';
import * as fs from 'fs';
import { omit } from 'lodash';
import * as path from 'path';
import * as ts from 'typescript';
import { saveAsJson } from '../utils';

export const literalsCache = {} as Dict<any>;

/**
 * Turn nested bind property reference object to dot-notion reference. For example, it converts `{this:{is:{a:{nested:{bind:true}}}}}`
 * to `this__is__a__nested__bind`.
 * @param bind string or deep bind object
 * @returns dot notion deep reference string
 */
export const convertDeepBindToPath = (bind?: any): string | null => {
    if (bind === null || bind === undefined || typeof bind === 'boolean') {
        return null;
    }
    if (typeof bind === 'string') {
        return bind;
    }

    if (typeof bind === 'object' && Object.keys(bind).length === 1) {
        const childKeys = Object.keys(bind);
        const childPath = convertDeepBindToPath(bind[childKeys[0]]);
        if (childPath) {
            return `${childKeys[0]}__${childPath}`;
        }
        return childKeys[0];
    }

    throw new Error(`Unsupported bind value: ${JSON.stringify(bind)}`);
};

/**
 * Creates a dictionary key
 * @param prefix prefix for the generated key
 * @param key translation key
 * @returns dictionary key
 */
export const createDictionaryKey = (prefix: string, key: string, decoratorContext?: Dict<any>): string => {
    let dictionaryKey = `${prefix}__${key.replace(/[.-]/g, '_')}`;

    const contextKey = decoratorContext?.bind ?? decoratorContext?.id;
    const validSuffixes = ['columns', 'dropdownActions', 'inlineActions'];
    if (validSuffixes.some(suffix => prefix.endsWith(suffix)) && contextKey) {
        dictionaryKey = `${dictionaryKey}__${convertDeepBindToPath(contextKey)}`;
        if (decoratorContext?.valueField) {
            dictionaryKey = `${dictionaryKey}__${convertDeepBindToPath(decoratorContext.valueField)}`;
        }
    }

    return dictionaryKey;
};

/**
 * Determines whether the given file is a page or a sticker
 * @param file
 * @returns true if file is a page or a sticker
 */
export function isClientArtifactFile(fileName: string): boolean {
    const fileNameNoExtension = path.basename(fileName, '.ts');
    return (
        fileNameNoExtension !== 'index' &&
        fileNameNoExtension !== '_index' &&
        Boolean(
            fileName.match(
                /(\/|\\)lib(\/|\\)(pages|stickers|page-extensions|page-fragments|sticker-extensions|widgets)/,
            ),
        )
    );
}
/**
 * Determines whether the given file is a node or node-extension
 * @param file
 * @returns true if file is a node or node-extension
 */
export function isNodeLikeArtifactFile(file: ts.SourceFile): boolean {
    const fileName = path.basename(file.fileName, '.ts');
    return (
        fileName !== 'index' &&
        fileName !== '_index' &&
        Boolean(file.fileName.match(/(\/|\\)lib(\/|\\)(nodes|node-extensions)/))
    );
}

export function isNodeArtifactFile(file: ts.SourceFile): boolean {
    const fileName = path.basename(file.fileName, '.ts');
    return fileName !== 'index' && fileName !== '_index' && Boolean(file.fileName.match(/(\/|\\)lib(\/|\\)nodes/));
}

/**
 * Determines whether the given file is a service option
 * @param file
 * @returns true if file is a service option
 */
export function isServiceOptionFile(file: ts.SourceFile): boolean {
    const fileName = path.basename(file.fileName, '.ts');
    return (
        fileName !== 'index' &&
        fileName !== '_index' &&
        Boolean(file.fileName.match(/(\/|\\)lib(\/|\\)service-options(\/|\\)/))
    );
}

/**
 * Determines whether the given file is an activity
 * @param file
 * @returns true if file is an activity
 */
export function isActivityFile(file: ts.SourceFile): boolean {
    const fileName = path.basename(file.fileName, '.ts');
    return (
        fileName !== 'index' &&
        fileName !== '_index' &&
        Boolean(file.fileName.match(/(\/|\\)lib(\/|\\)activities(\/|\\)/))
    );
}

/**
 * Determines whether the given file is a dataType
 * @param file
 * @returns true if file is an dataType
 */
export function isDataTypeFile(file: ts.SourceFile): boolean {
    return Boolean(file.fileName.match(/(\/|\\)lib(\/|\\)data-types(\/|\\)/)) || isEnumFile(file);
}

/**
 * Determines whether the given file is a test file
 * @param file
 * @returns true if file is a test file
 */
export function isTestFile(file: ts.SourceFile): boolean {
    return (
        file.fileName.includes('test') && path.normalize(path.dirname(file.fileName)).split(path.sep).includes('test')
    );
}

/**
 * Determines whether the given file is an enum file
 * @param file
 * @returns true if file is an enum file
 */
export function isEnumFile(file: ts.SourceFile): boolean {
    const fileName = path.basename(file.fileName, '.ts');
    return fileName !== 'index' && fileName !== '_index' && Boolean(file.fileName.match(/(\/|\\)lib(\/|\\)(enums)/));
}

/**
 * Returns a copy of the target without the properties received as second parameter
 * @returns a copy of target without the given properties
 */
export const excludeProperties = (target: Dict<string>, properties: string[]): Dict<string> => {
    return !properties.length ? target : omit(target, properties);
};

export const sortByKey = (dict: Dict<string>): Dict<string> => {
    const sortedDict: Dict<string> = {};
    Object.keys(dict)
        .sort((a: string, b: string) => a.localeCompare(b))
        .forEach(key => {
            sortedDict[key] = dict[key];
        });
    return sortedDict;
};

export const getLocaleFilePath = (dir: string, locale: string, isBuild = false) => {
    const targetDir = isBuild ? path.join(dir, 'build', 'lib', 'i18n') : path.join(dir, 'lib', 'i18n');

    if (!fs.existsSync(targetDir)) {
        fs.mkdirSync(targetDir);
    }

    return path.resolve(targetDir, `${locale}.json`);
};

export function merge(target: Dict<string>, source: Dict<string>): Dict<string> {
    // eslint-disable-next-line no-restricted-syntax
    for (const [key, value] of Object.entries(source)) {
        if (value != null) target[key] = value;
    }
    return target;
}

// When the transformers are called from a ts-loader worker, we cannot use the literals cache because they are in different processes
// We may investigate a better way to identify this but for now, this is the easiest I found.
const mainFilename = (require.main?.filename || '').replace(/\\/g, '/');
const isWorker = /\/thread-loader\/dist\/worker.js$/.test(mainFilename);
const isTscp = /\/tspc.js$/.test(mainFilename);

export function writeStringLiteralsToBase(currentLiterals: Dict<string>, dir: string): void {
    const basePath = getLocaleFilePath(dir, `base.${process.pid}`);
    // we can use cache only for non-workers process
    // XTREM_I18N_TO_BASE is used to force the writing of the base file
    if (!isWorker && !isTscp && process.env.XTREM_I18N_TO_BASE !== '1') {
        literalsCache[dir] = literalsCache[dir] ?? {};
        merge(literalsCache[dir], currentLiterals);
        return;
    }
    const baseFileContent: Dict<string> = fs.existsSync(basePath) ? JSON.parse(fs.readFileSync(basePath, 'utf-8')) : {};
    const fileContent = merge(baseFileContent, currentLiterals);
    // First write the updated base file.
    saveAsJson(basePath, sortByKey(fileContent));
}

/**
 * Adds dictionary entry
 * @param key the translation key
 * @param value the translation value
 * @returns the newly created key
 */
export function addDictionaryEntry(dictionary: Dict<string>, key: string, value: string): string {
    if (dictionary[key] === undefined) {
        dictionary[key] = value;
        return key;
    }
    const latestDuplicateKey =
        Object.keys(dictionary)
            .filter(k => k.startsWith(key))
            .pop() || '';
    const counterRegexMatch = latestDuplicateKey.match(/__(\d*)$/);
    const counter = counterRegexMatch ? parseInt(counterRegexMatch[1], 10) + 1 : 2;
    const updatedKey = `${key}__${counter}`;
    dictionary[updatedKey] = value;
    return updatedKey;
}
