import { getPackageNameAndRoot } from '@sage/xtrem-i18n';
import { createDictionary, Dict } from '@sage/xtrem-shared';
import * as ts from 'typescript';
import { getDirName, getFileName, parseObjectLiteralToObject } from '../utils';
import { addDictionaryEntry, createDictionaryKey, isClientArtifactFile } from './transformer-utils';

const includedAstTypes: Set<ts.SyntaxKind> = new Set([ts.SyntaxKind.Decorator]);
const excludedAstTypes = new Set([
    ts.SyntaxKind.ImportDeclaration,
    ts.SyntaxKind.TypePredicate,
    ts.SyntaxKind.TypeReference,
]);
const includedProperties = new Set<string>([
    'addButtonText',
    'content',
    'createTunnelLinkText',
    'description',
    'emptyStateClickableText',
    'emptyStateText',
    'helperText',
    'indicatorContent',
    'infoMessage',
    'lookupDialogTitle',
    'objectTypePlural',
    'objectTypeSingular',
    'placeholder',
    'postfix',
    'prefix',
    'subtitle',
    'title',
    'warningMessage',
    'wizardNextButtonLabel',
    'wizardPreviousButtonLabel',
]);

/**
 * Creates a localization wrapper
 * @param key the translation key
 * @returns a Typescript Identifier, i.e. the translation wrapper
 */
function createLocalizeWrapper(key: string, value: string) {
    return ts.factory.createCallExpression(
        ts.factory.createPropertyAccessExpression(
            ts.factory.createIdentifier('ui'),
            ts.factory.createIdentifier('localize'),
        ),
        undefined,
        [ts.factory.createStringLiteral(key), ts.factory.createStringLiteral(value)],
    );
}

/**
 * Gets a node's key, i.e. the node's text
 * @param node Typescript node
 * @returns node key or undefined
 */
function getNodeKey(node: ts.Node): string | undefined {
    if (!node) {
        return undefined;
    }
    switch (node.kind) {
        case ts.SyntaxKind.PropertyAccessExpression: {
            const expression = (node as ts.PropertyAccessExpression).expression;
            if (!expression) {
                return undefined;
            }
            const name = (expression as ts.Identifier).text;
            if (!name) {
                return undefined;
            }
            return name.indexOf('\n') === -1 ? name : undefined;
        }
        case ts.SyntaxKind.Decorator:
            return '';
        case ts.SyntaxKind.MethodDeclaration:
            return (node as ts.MethodDeclaration).name.getText();
        case ts.SyntaxKind.PropertyAssignment:
            return (node as ts.PropertyAssignment).name.getText();
        case ts.SyntaxKind.PropertyDeclaration:
            return (node as ts.PropertyDeclaration).name.getText();
        case ts.SyntaxKind.Identifier:
            return (node as ts.Identifier).text;
        case ts.SyntaxKind.StringLiteral:
            return (node as ts.StringLiteral).text;
        case ts.SyntaxKind.NoSubstitutionTemplateLiteral:
            return (node as ts.NoSubstitutionTemplateLiteral).text;
        default:
            return undefined;
    }
}

/**
 * Top-level TS visitor
 * @param prefix prefix for translation keys
 */
export const visitor =
    (dictionary: Dict<string>, prefix: string, ctx: ts.TransformationContext) =>
        (node: ts.Node): ts.Node => {
            if (excludedAstTypes.has(node.kind)) {
                return node;
            }

            const localKey = getNodeKey(node);
            // Note that undefined is there on purpose because an empty string is falsy
            const localPrefix = localKey !== undefined ? createDictionaryKey(prefix, localKey) : prefix;

            return includedAstTypes.has(node.kind)
                ? // If type is of interest, recur on children with 'decoratorVisitor'
                ts.visitEachChild(node, decoratorVisitor(dictionary, localPrefix, ctx, createDictionary()), ctx)
                : // Otherwise, recur on children
                ts.visitEachChild(node, visitor(dictionary, localPrefix, ctx), ctx);
        };

/**
 * Decorator visitor
 * @param prefix prefix for translation keys
 */
const decoratorVisitor =
    (dictionary: Dict<string>, prefix: string, ctx: ts.TransformationContext, decoratorContext: Dict<any>) =>
        (node: ts.Node): ts.Node => {
            if (excludedAstTypes.has(node.kind) || ts.isFunctionLike(node)) {
                return node;
            }

            const localKey = getNodeKey(node);
            // If node is not of interest recur on children
            // Note that undefined is there on purpose because an empty string is falsy
            const nextDecoratorContext = ts.isObjectLiteralExpression(node)
                ? parseObjectLiteralToObject(node)
                : decoratorContext;
            if (localKey === undefined) {
                return ts.visitEachChild(node, decoratorVisitor(dictionary, prefix, ctx, nextDecoratorContext), ctx);
            }

            const key = createDictionaryKey(prefix, localKey, decoratorContext);

            return includedProperties.has(localKey)
                ? // If property is of interest visit it with 'stringVisitor'
                ts.visitEachChild(
                    node,
                    stringVisitor(dictionary, key, ctx, localKey, nextDecoratorContext, node.kind),
                    ctx,
                )
                : // Otherwise, recur on children
                ts.visitEachChild(node, decoratorVisitor(dictionary, key, ctx, nextDecoratorContext), ctx);
        };

/**
 * String visitor
 * @param prefix prefix for translation keys
 */
const stringVisitor =
    (
        dictionary: Dict<string>,
        prefix: string,
        ctx: ts.TransformationContext,
        parentKey: string,
        decoratorContext: Dict<any>,
        parentKind: ts.SyntaxKind,
    ) =>
        (node: ts.Node): ts.Node => {
            if (excludedAstTypes.has(node.kind) || ts.isFunctionLike(node)) {
                return node;
            }

            const localKey = getNodeKey(node);
            // If node is not of interest recur on children
            // Note that undefined is there on purpose because an empty string is falsy
            if (localKey === undefined) {
                return ts.visitEachChild(
                    node,
                    stringVisitor(dictionary, prefix, ctx, parentKey, decoratorContext, node.kind),
                    ctx,
                );
            }

            const key = createDictionaryKey(prefix, localKey, decoratorContext);
            if (
                includedProperties.has(parentKey) &&
                ts.SyntaxKind.PropertyAssignment === parentKind &&
                (ts.isStringLiteral(node) || ts.isNoSubstitutionTemplateLiteral(node))
            ) {
                // Discard if not a string
                const dictionaryKey = addDictionaryEntry(dictionary, prefix, localKey);
                return createLocalizeWrapper(dictionaryKey, localKey);
            }

            // We need to keep the key of nested field node or event handlers (e.g. title for onClick)
            const correspondingKey = node.kind === ts.SyntaxKind.MethodDeclaration ? parentKey : localKey;
            return ts.visitEachChild(
                node,
                stringVisitor(dictionary, key, ctx, correspondingKey, decoratorContext, node.kind),
                ctx,
            );
        };
/**
 * This transformer is meant to be run BEFORE the 'message-transformer' and its purpose is
 * to wrap some page/sticker decorator properties (see 'includedProperties') with a call to the 'ui.localize' function.
 *
 * @param ctx transformation context
 * @returns the transformed file
 */
export function decoratorTransformer(ctx: ts.TransformationContext): ts.Transformer<ts.SourceFile> {
    return (file: ts.SourceFile) => {
        if (!isClientArtifactFile(file.fileName)) {
            return file;
        }

        try {
            const dictionary = {} as Dict<string>;
            const fileName = getFileName(file);
            const dirName = getDirName(file);
            const nameAndRoot = getPackageNameAndRoot(file.fileName);
            const fileKey = `${nameAndRoot.name}/${createDictionaryKey(dirName, fileName)}`;
            const result = ts.visitNode(file, visitor(dictionary, fileKey, ctx));
            return result as ts.SourceFile;
        } catch (err) {
            throw new Error(`Decorator transformer failed due to the following error: ${err}`);
        }
    };
}
