import { Dict } from '@sage/xtrem-shared';
import * as crypto from 'crypto';
import * as fs from 'fs';
import { trim } from 'lodash';
import * as path from 'path';
import * as ts from 'typescript';

/**
 * Gets the directory a file belongs to
 * @param file Typescript source file
 * @param relativeToLib Should we get directory relative to the lib folder in the path
 *                      or do we get that last directory in the path. Default to false
 * @returns name of th directory the given file belongs to
 */
export function getDirName(file: ts.SourceFile, relativeToLib = false) {
    const pathElements = path.normalize(path.dirname(file.fileName)).split(path.sep);
    const indexOfLib = pathElements.indexOf('lib');
    if (relativeToLib && indexOfLib >= 0 && indexOfLib < pathElements.length - 1) {
        return pathElements[indexOfLib + 1];
    }
    return pathElements.pop() as string;
}

/**
 * Gets the base name of a file
 * @param file Typescript source file
 * @returns base name of the given file without any extension
 */
export function getFileName(file: ts.SourceFile): string {
    return path.basename(file.fileName, '.ts');
}

export const parseObjectLiteralToObject = (objectLiteral: ts.ObjectLiteralExpression): Dict<any> => {
    const targetObject: Dict<any> = {};
    objectLiteral.properties.forEach(p => {
        if (ts.isPropertyAssignment(p) && p.name && p.initializer) {
            const name = p.name.getText();
            if (ts.isStringLiteral(p.initializer)) {
                targetObject[name] = trim(p.initializer.getText(), '"\'');
            } else if (ts.isNumericLiteral(p.initializer)) {
                targetObject[name] = Number(p.initializer.getText());
            } else if (p.initializer.kind === ts.SyntaxKind.TrueKeyword) {
                targetObject[name] = true;
            } else if (p.initializer.kind === ts.SyntaxKind.FalseKeyword) {
                targetObject[name] = false;
            } else if (ts.isObjectLiteralExpression(p.initializer)) {
                targetObject[name] = parseObjectLiteralToObject(p.initializer);
            }
        }
    });

    return targetObject;
};

export function saveAsJson(filename: string, obj: object): void {
    fs.writeFileSync(filename, `${JSON.stringify(obj, null, 4)}\n`, 'utf-8');
}

export function getCrudBulkMutationNames(filename: string, node: ts.Decorator): string[] {
    const names = [] as string[];
    getDecoratorArguments(filename, node).forEach(property => {
        if (
            ts.isPropertyAssignment(property) &&
            property.initializer &&
            property.initializer.kind === ts.SyntaxKind.TrueKeyword &&
            ts.isIdentifier(property.name)
        ) {
            const propertyName = property.name as ts.Identifier;
            const m = /^canBulk(Delete|Update)$/.exec(propertyName.escapedText.toString());
            if (m) {
                names.push(`bulk${m[1]}`);
            }
        }
    });
    return names;
}

export function getDecoratorArguments(filename: string, node: ts.Decorator): ts.NodeArray<ts.ObjectLiteralElementLike> {
    if (!ts.isCallExpression(node.expression)) {
        throw new Error(`${filename}: expected CallExpression, got ${ts.SyntaxKind[node.expression.kind]}`);
    }
    const decoratorArgument = node.expression.arguments[0];
    if (!decoratorArgument || !ts.isObjectLiteralExpression(decoratorArgument)) {
        throw new Error(
            `${filename}: expected ObjectLiteralExpression, got ${
                decoratorArgument && ts.SyntaxKind[decoratorArgument.kind]
            }`,
        );
    }
    return decoratorArgument.properties;
}

export function getNodeStorageType(filename: string, node: ts.Decorator): string | undefined {
    const storage = getDecoratorArguments(filename, node).find(
        property =>
            ts.isPropertyAssignment(property) &&
            property.initializer &&
            property.initializer.kind === ts.SyntaxKind.StringLiteral &&
            ts.isIdentifier(property.name) &&
            property.name.escapedText.toString() === 'storage',
    );

    if (
        storage &&
        ts.isPropertyAssignment(storage) &&
        storage.initializer &&
        storage.initializer.kind === ts.SyntaxKind.StringLiteral
    )
        return storage.initializer.getText();
    return undefined;
}

export function isSubNodeDecorator(node: ts.Node): node is ts.Decorator {
    return (
        ts.isDecorator(node) &&
        ts.isCallExpression(node.expression) &&
        ts.isPropertyAccessExpression(node.expression.expression) &&
        node.expression.expression.getText() === 'decorators.subNode'
    );
}

export function isNodeExtensionDecorator(node: ts.Node): node is ts.Decorator {
    return (
        ts.isDecorator(node) &&
        ts.isCallExpression(node.expression) &&
        ts.isPropertyAccessExpression(node.expression.expression) &&
        node.expression.expression.getText() === 'decorators.nodeExtension'
    );
}

export function isAbstractNodeDecorator(filename: string, node: ts.Decorator): boolean {
    if (!ts.isCallExpression(node.expression)) {
        throw new Error(`${filename}: expected CallExpression, got ${ts.SyntaxKind[node.expression.kind]}`);
    }
    const decoratorArgument = node.expression.arguments[0];
    if (!decoratorArgument || !ts.isObjectLiteralExpression(decoratorArgument)) {
        throw new Error(
            `${filename}: expected ObjectLiteralExpression, got ${
                decoratorArgument && ts.SyntaxKind[decoratorArgument.kind]
            }`,
        );
    }

    return decoratorArgument.properties.some(property => {
        if (
            ts.isPropertyAssignment(property) &&
            property.initializer.kind === ts.SyntaxKind.TrueKeyword &&
            ts.isIdentifier(property.name)
        ) {
            const propertyName = property.name;
            if (propertyName.escapedText.toString() === 'isAbstract') {
                return true;
            }
        }
        return false;
    });
}

export function canExportNodeDecorator(filename: string, node: ts.Decorator): boolean {
    if (!ts.isCallExpression(node.expression)) {
        throw new Error(`${filename}: expected CallExpression, got ${ts.SyntaxKind[node.expression.kind]}`);
    }
    const decoratorArgument = node.expression.arguments[0];
    if (!decoratorArgument || !ts.isObjectLiteralExpression(decoratorArgument)) {
        throw new Error(
            `${filename}: expected ObjectLiteralExpression, got ${
                decoratorArgument && ts.SyntaxKind[decoratorArgument.kind]
            }`,
        );
    }

    return decoratorArgument.properties.every(property => {
        if (
            ts.isPropertyAssignment(property) &&
            property.initializer.kind === ts.SyntaxKind.FalseKeyword &&
            ts.isIdentifier(property.name)
        ) {
            const propertyName = property.name;
            if (propertyName.escapedText.toString() === 'canExport') {
                return false;
            }
        }
        return true;
    });
}

export function hashFile(filePath: string) {
    return crypto.createHash('md5').update(fs.readFileSync(filePath, 'utf-8')).digest('hex');
}

export function getMetadataFilePathForClientFile(dir: string, filePath: string) {
    const artifactFolderName = path.basename(path.dirname(filePath));
    const buildLibArtifactDir = path.resolve(dir, `build/lib/${artifactFolderName}`);
    const metaFilename = path.basename(filePath).replace(/\.ts$/, '.meta.json');
    return path.resolve(buildLibArtifactDir, metaFilename);
}
