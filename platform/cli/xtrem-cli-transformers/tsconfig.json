{"extends": "../../tsconfig-package.json", "include": ["index.ts", "lib/**/*", "test/**/*"], "exclude": ["test/fixtures/test-app/**/*"], "compilerOptions": {"outDir": "build", "rootDir": ".", "baseUrl": "."}, "references": [{"path": "../../shared/xtrem-i18n"}, {"path": "../../back-end/xtrem-log"}, {"path": "../../shared/xtrem-shared"}, {"path": "../../shared/xtrem-date-time"}, {"path": "../../shared/xtrem-decimal"}, {"path": "../../back-end/xtrem-dts-bundle"}]}