{"extends": "../../tsconfig-package.json", "include": ["index.ts", "lib/**/*", "test/**/*"], "compilerOptions": {"baseUrl": ".", "outDir": "build", "rootDir": ".", "skipLibCheck": true, "types": ["node"]}, "references": [{"path": "../xtrem-cli-lib"}, {"path": "../../back-end/xtrem-config"}, {"path": "../../back-end/xtrem-core"}, {"path": "../../back-end/xtrem-log"}, {"path": "../../shared/xtrem-shared"}, {"path": "../../back-end/eslint-plugin-xtrem"}, {"path": "../../back-end/xtrem-dts-bundle"}, {"path": "../../back-end/xtrem-minify"}]}