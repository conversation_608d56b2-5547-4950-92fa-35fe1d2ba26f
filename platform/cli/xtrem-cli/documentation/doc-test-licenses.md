PATH: XTREEM/Test+data+management/Licenses

Sage license files contain, among others, information about **languages**, **legislations** and **service options**.

Any configuration of the previous three variables can be achieved with a layer-based approach: all information regarding languages and legislations will be found in the same file - **folder.csv** - which represents a layer.

Here is how that will look like:

| folder  | referenceFolder | defaultLanguage | languages_languages_0 | languages_languages_1 | languages_languages_2 | legislations_legislations_0 | legislations_legislations_1 |
| ------- | --------------- | --------------- | --------------------- | --------------------- | --------------------- | --------------------------- | --------------------------- |
| REPOSX3 | REPOSX3         | ENG             |                       |                       |                       | ENG                         |                             |
| TEST    | REPOSX3         | ENG             | ENG                   | GER                   |                       | ENG                         | FRA                         |

To override any language or legislation simply create a new layer specifying your customizations along with the referenced folder like the following:

| folder  | legislations_legislations_0 |
| ------- | --------------------------- |
| REPOSX3 | **FRA**                     |
| TEST    |                             |

which **changes** _legislation_0_ for REPOSX3 and **removes** it from TEST.

On the other hand, all service options will be found in **service-options.csv**, which will look like the following:

| code  | description | module | activeFlag | \_updateTick | \_createStamp | \_updateStamp | \_createUser | \_updateUser | screenSize | databaseMinimumSize | order | maximumSize | formula | sequence | ServiceOption |
| ----- | ----------- | ------ | ---------- | ------------ | ------------- | ------------- | ------------ | ------------ | ---------- | ------------------- | ----- | ----------- | ------- | -------- | ------------- |
| ABI   | 30839       | 1      | Y          | 2            | 2007-01-03    | 2016-08-19    | MB           | JOJAC        | 0          | 0                   | 0     | 0           |         | 0        |
| AD5   | 649         | 1      | Y          | 2            | 2003-09-08    | 2017-03-08    | BY           | DLE          | 0          | 0                   | 0     | 0           |         | 0        |
| ADBL  | 49784       | 1      | Y          | 1            | 2012-10-01    | 2014-05-15    | VPO          | ADMIN        | 2000       | 100                 | 0     | 0           |         | 0        |
| ADBP  | 49785       | 1      | Y          | 1            | 2012-10-01    | 2014-05-15    | VPO          | ADMIN        | 2000       | 100                 | 0     | 0           |         | 0        |
| ADC   | 31912       | 7      | Y          | 1            | 2007-01-11    | 2014-05-15    | TS           | ADMIN        | 0          | 0                   | 5000  | 0           |         | 0        |
| ADI   | 26960       | 1      | Y          | 1            | 2005-05-23    | 2014-05-15    | MB           | ADMIN        | 2000       | 300                 | 0     | 9999        |         | 0        |
| ANA   | 17944       | 9      | Y          | 1            | 2013-10-31    | 2014-05-15    | ADMIN        | ADMIN        | 20         | 20                  | 1000  | 20          |         | 0        |
| ANGMI | 55346       | 9      | Y          | 1            | 2018-10-08    | 2018-10-08    | MABON        | MABON        | 0          | 0                   | 1000  | 0           |         | 0        |
| APL   | 19971       | 1      | Y          | 1            | 2003-12-08    | 2014-05-15    | JPJ1         | ADMIN        | 0          | 0                   | 0     | 0           |         | 0        |
| APRX3 | 67          | 9      | Y          | 1            | 2008-01-11    | 2014-05-15    | MB           | ADMIN        | 0          | 0                   | 1000  | 0           |         | 0        |
| APS   | 40333       | 8      | Y          | 1            | 2009-10-22    | 2014-05-15    | VPO          | ADMIN        | 0          | 0                   | 8000  | 0           | ORP     | ORO      | 0             |
| ARCH  | 42334       | 1      | Y          | 1            | 2010-10-04    | 2014-05-15    | ELA          | ADMIN        | 0          | 0                   | 0     | 0           |         | 0        |
| ASD   | 42043       | 1      | Y          | 1            | 2010-03-11    | 2014-05-15    | JPJ          | ADMIN        | 0          | 0                   | 10000 | 0           |         | 0        |

**By default all service options are enabled**. To disable one or more simply create a new layer and set the "Active Flag" column to "N" (you will need to specify the "code" in order for the join operation to work).

Here is an example:

| code | Active Flag |
| ---- | ----------- |
| ABI  | N           |
| AD5  | N           |

which **disables** service options "ABI" and "AD5".

## Notes

-   See how layers work in the "Layers" sections.
-   Don't forget to set the desired "folder" inside the configuration file, i.e. "parameters.json".
-   Don't forget to add your layers to the configuration file, i.e. "parameters.json".
