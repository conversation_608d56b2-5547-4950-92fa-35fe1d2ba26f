{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "type": "node",
            "request": "launch",
            "name": "Run show-case integration tests in CI mode",
            "program": "${workspaceFolder}/build/lib/cli.js",
            "cwd": "${workspaceFolder}/../../show-case/xtrem-show-case",
            "args": ["test", "--integration", "--ci"],
            "preLaunchTask": "build",
        },
        {
            "type": "node",
            "request": "launch",
            "name": "Run show-case integration tests with browser",
            "program": "${workspaceFolder}/build/lib/cli.js",
            "cwd": "${workspaceFolder}/../../show-case/xtrem-show-case",
            "args": ["test", "visual", "--integration", "--browser"],
            "preLaunchTask": "build",
        },
        {
            "type": "node",
            "request": "launch",
            "name": "Build show-case with instruments",
            "program": "${workspaceFolder}/build/lib/cli.js",
            "cwd": "${workspaceFolder}/../../show-case/xtrem-show-case",
            "args": ["build", "--instrumented"],
            "preLaunchTask": "build",
        },
        {
            "type": "node",
            "request": "launch",
            "name": "Build show-case with only changed client",
            "program": "${workspaceFolder}/build/lib/cli.js",
            "cwd": "${workspaceFolder}/../xtrem-show-case",
            "args": ["build", "--only-changed"],
            "preLaunchTask": "build",
        }
    ]
}
