/* eslint-disable global-require */
import { globalRunningContext } from '@sage/xtrem-core';
import { sourceMapSetup } from '@sage/xtrem-log';
import { loadConfig } from './config';
import { PluginLoader, logger } from './plugin-loader';

function exitOnUnhandledError(cb: () => string): void {
    if (globalRunningContext.isServicesMode) {
        // in services mode, errors are handled by the UnhandledErrorMonitor
        return;
    }
    logger.error(cb());
    process.exit(1);
}

process
    .on('uncaughtException', err => {
        exitOnUnhandledError(() => `Uncaught exception: ${err.stack}`);
    })
    .on('unhandledRejection', (reason, promise) => {
        exitOnUnhandledError(() => `Unhandled rejection: ${reason} at ${promise}`);
    });

sourceMapSetup();

(async () => {
    const yargs = require('yargs/yargs')(
        // keep only real arguments excluding '--' separators
        process.argv.slice(2).filter(p => p !== '--'),
        process.cwd(),
    );
    const config = loadConfig();
    const loader = await PluginLoader.getInstance();
    await loader.loadPlugins(yargs, config);

    await yargs
        .wrap(yargs.terminalWidth())
        .scriptName('xtrem')
        .usage('Usage: $0 <command> [options]')
        .demandCommand(1, 'You need at least one command before moving on')
        .option('verbose', {
            alias: 'd',
            desc: 'Verbose output',
            default: false,
            global: true,
        })
        .env('XTREM')
        .help('h')
        .alias('h', 'help')
        .alias('v', 'version')
        .epilog('use $0 <command> --help for help on <command>')
        .middleware((argv: any) => {
            globalRunningContext.init({ argv });
        })
        .command({
            command: '*',
            handler: (argv: any) => {
                logger.error(
                    `Unhandled command '${argv._}' from '${process.cwd()}'. Please be sure to run this command from an application directory and that the expected cli plugins are referenced in the package.json`,
                );
                if (argv.$cli?.plugins?.length === 0) {
                    logger.error('No plugins found');
                } else {
                    console.log('');
                    yargs.showHelp();
                }
                process.exit(1);
            },
        })
        .parse();
})().catch(err => {
    logger.error(err.stack);
    process.exit(1);
});
