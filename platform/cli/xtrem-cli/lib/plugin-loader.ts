import type { CliPlugin } from '@sage/xtrem-cli-lib';
import { pluginRegistry } from '@sage/xtrem-cli-lib';
import { Logger } from '@sage/xtrem-log';
import type { Config } from '@sage/xtrem-shared';
import { findUp } from 'find-up';
import * as fs from 'fs';
import * as _ from 'lodash';
import * as path from 'path';
import type { Argv } from 'yargs';
import glob = require('glob');

interface CliPluginPackage {
    name: string;
    dependencies?: Record<string, string>;
    peerDependencies?: Record<string, string>;
}

export const logger = new Logger(__filename, 'cli');

export class PluginLoader {
    private static instance: PluginLoader;

    #resolvePaths: string[] = [];

    #currentModuleRoot = '';

    // eslint-disable-next-line @typescript-eslint/no-empty-function
    private constructor() {}

    public static getInstance(): Promise<PluginLoader> {
        if (!PluginLoader.instance) {
            PluginLoader.instance = new PluginLoader();
        }
        return PluginLoader.instance.init();
    }

    private async init() {
        const rootPackageJsonPath = await findUp('package.json');
        this.#currentModuleRoot = rootPackageJsonPath
            ? `${path.dirname(rootPackageJsonPath)}/node_modules`
            : './node_modules';
        this.#resolvePaths = [this.#currentModuleRoot];
        return this;
    }

    private autoDiscoverPlugins(): string[] {
        logger.info(`Auto-discovering plugins from ${process.cwd()} in ${this.#currentModuleRoot}...`);
        const mainPath = require.main?.path ?? '';
        const globs: string[] = [];
        globs.push(`${this.#currentModuleRoot}/@sage/*-cli-*/package.json`);

        const packagePaths = glob.sync(globs);
        logger.debug(() => `found packages: ${JSON.stringify(packagePaths, null, 2)}`);

        const packages = packagePaths
            .map(p => this.tryReadPackage(p))
            .filter(p => p != null && p.name) as CliPluginPackage[];
        const bundles = packages.filter(p => p.name.includes('-cli-bundle-'));
        if (bundles.length > 0) {
            logger.debug(() => `bundles: ${bundles.map(b => b.name)}`);
            // find all plugins in bundles
            const bundlePackages = bundles
                .map(b => this.tryRequirePackage(`${b.name}/package.json`))
                .filter(p => p != null) as CliPluginPackage[];

            // add the bundle node_modules to the resolve paths
            const bundlePaths = bundlePackages.map(b => path.join(this.#currentModuleRoot, b.name, 'node_modules'));
            this.#resolvePaths = _.uniq([...bundlePaths, ...this.#resolvePaths, ...module.paths]);

            // make them unique
            const bundlePluginNames = _.uniq(
                bundlePackages.map(p => Object.keys(p.dependencies ?? {}).filter(k => k.includes('-cli-'))).flat(),
            );
            logger.debug(() => `bundle packages: ${bundlePluginNames}`);
            const bundlePlugins = bundlePluginNames
                .map(d => this.tryRequirePackage(`${d}/package.json`))
                .filter(p => p != null) as CliPluginPackage[];

            bundlePlugins.forEach(pack => {
                if (pack && !packages.find(p => p.name === pack.name)) {
                    logger.debug(() => `add extra pack: ${pack?.name}`);
                    packages.push(pack);
                }
            });
        }
        return _.uniq(
            packages
                .filter(
                    p =>
                        // do not include the bundles themselves
                        !p.name.includes('-cli-bundle-') &&
                        // is not xtrem-cli itself
                        !mainPath.includes(`/${p.name}/`) &&
                        // a plugin necessarily has a dependency on @sage/xtrem-cli-lib
                        Object.keys({ ...p.dependencies, ...p.peerDependencies }).includes('@sage/xtrem-cli-lib'),
                )
                .map(p => p.name),
        );
    }

    // eslint-disable-next-line class-methods-use-this
    private tryReadPackage(file: string): CliPluginPackage | null {
        try {
            const json = fs.readFileSync(file, 'utf8');
            return JSON.parse(json);
        } catch (e) {
            if (e.code !== 'ENOENT') {
                throw e;
            }
            return null;
        }
    }

    private tryRequire(name: string, options?: { cannotResolve?: string; cannotRequire?: string }): any {
        try {
            const mod = this.#resolvePaths.reduce((m, p) => {
                if (m) return m;
                return require.resolve(name, { paths: [p] });
            }, null);
            if (mod == null) {
                logger.error(`${options?.cannotResolve ?? 'Cannot resolve package'} '${name}': Not Found`);
                return null;
            }
            // eslint-disable-next-line import/no-dynamic-require, global-require
            return require(mod);
        } catch (e) {
            if (e.code !== 'MODULE_NOT_FOUND') {
                throw e;
            }
            logger.error(`${options?.cannotRequire ?? 'Cannot require package'} '${name}': ${e.message}`);
            return null;
        }
    }

    private tryRequirePackage(name: string): any {
        return this.tryRequire(name, {
            cannotResolve: 'Cannot resolve package',
            cannotRequire: 'Cannot require package',
        });
    }

    private tryLoadPlugin(name: string): any {
        const mod = this.tryRequire(name, {
            cannotResolve: 'Cannot resolve plugin',
            cannotRequire: 'Cannot load plugin',
        });
        return { name, exports: mod ?? {} };
    }

    loadPlugins(yargs: Argv<any>, config: Config): any {
        let pluginNames = config?.cli?.plugins;

        // auto-discover plugins if cli config is not set
        if (pluginNames == null) {
            pluginNames = this.autoDiscoverPlugins();
        }
        logger.info(`Plugins to load: [${pluginNames}]`);
        pluginNames
            ?.map((plugin: string) => this.tryLoadPlugin(plugin))
            .filter((p: CliPlugin) => pluginNames?.includes(p.name))
            .forEach((p: CliPlugin) => {
                const plugin = p.exports.plugin;
                if (typeof plugin === 'function') {
                    pluginRegistry.add(p);
                    logger.verbose(() => `Try loading plugin... ${p.name}`);
                    try {
                        plugin(yargs);
                    } catch (err) {
                        logger.warn(`Failed to load plugin ${p.name}, reason = ${err.message}`);
                    }
                }
            });
        const plugins = pluginRegistry.plugins;
        yargs.config({ $cli: { plugins } });
        logger.info(`Loaded plugins [${plugins.map(p => p.name)}]`);
    }
}
