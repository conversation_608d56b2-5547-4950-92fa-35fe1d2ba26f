#!/usr/bin/env node

Error.stackTraceLimit = 100;

if (process.env.XTREM_APP_DIR) {
    console.warn(
        '⚠️ Deprecated: XTREM_APP_DIR environment variable was used to force the application folder to ' +
            process.env.XTREM_APP_DIR,
    );
    process.chdir(process.env.XTREM_APP_DIR);
}

// command is the first arg that is not a '--' separator
const command = process.argv.slice(2).find(a => a !== '--');
console.log(`Running ${command} in ${process.cwd()}`);
console.log(`          NODE_ENV=${process.env.NODE_ENV}`);
console.log(`      NODE_OPTIONS=${process.env.NODE_OPTIONS}`);
console.log(`  XTREM_V8_OPTIONS=${process.env.XTREM_V8_OPTIONS}`);

if (command === 'start') {
    if (!process.env.NEW_RELIC_ENABLED) {
        console.log(
            'Warning: New Relic is disabled. To enable it set the NEW_RELIC_ENABLED environment variable to "true"',
        );
        process.env.NEW_RELIC_NO_CONFIG_FILE = 'true';
        process.env.NEW_RELIC_ENABLED = 'false';
    } else {
        // Ensure we can load the module for later use in custom instrumentation
        require('newrelic');
    }
}
const path = require('node:path');
const fs = require('node:fs');
const os = require('node:os');
const util = require('node:util');

const root = os.platform() === 'win32' ? process.cwd().split(path.sep)[0] + '\\' : '/';

function requireFrom(fromDir) {
    while (fromDir !== root) {
        const localExecutable = path.resolve(fromDir, 'node_modules', '@sage', 'xtrem-cli', 'build', 'lib', 'cli.js');

        if (fs.existsSync(localExecutable)) {
            require(localExecutable);
            return true;
        } else {
            fromDir = path.dirname(fromDir);
        }
    }
    return false;
}

if (requireFrom(__dirname)) {
    return;
}
if (requireFrom(process.cwd())) {
    return;
}

console.log(util.styleText('yellow', '🤕 You are running globally installed version.'));
console.log(
    util.styleText(
        'yellow',
        'Please consider installing xtrem-cli locally too in order to prevent potential dependency version mismatch issues.',
    ),
);
require('../build/lib/cli');
