# xtrem-cli

xtrem-cli is a set of tools to assist the development of Xtrem artifacts (e.g. nodes, pages, service options, etc.) by providing mechanisms to create, compile, test and run a given Xtrem module. It can either be used as a command line utility or imported as a dependency to integrate it with your application code.

This is how to use it from the command line:

```
xtrem <command>

Commands:
    - init
    Creates an empty xtrem package. It prompts for installation details.

    - start [--channels <comma seperated list of channels>] [--services <comma seperated list of services>] [--watch-client] [--watch-server] [--watch-all] [--debug] [--cluster]
    Deploys the xtrem package. This deployment is only for development purposes, for production deployment options, please refer to the official Sage guidelines.
        --channels:         This option sets the list of comma separated channels to be active on the server.
                            The allowed channels are graphql, routing and listeners.
                            `routing`:          Starts the communication routing services.
                            `listeners`:        Starts the communication listeners.
                            `graphql`:          Starts the server in interactive mode. Endpoints are active by default.
                            If the --channels option is not supplied, then all the channels are active by default.
        --services:         This option sets the list of comma separated packages to be started as a service, for example
                            `xtrem start --services=@sage/xtrem-communication` or xtrem `start --services=communication`
                            (the prefix `@sage/xtrem-` can be omitted). In the absence of the --services option xtrem start
                            loads all the packages and run all their startService methods. This allows to easily start all
                            services in development mode.
        --watch-client:     Watches client side artifacts (pages and stickers) and recompiles them when changes
                            detected.
        --watch-server:     Watches server side artifacts and recompiles them when changes detected and then
                            rebounces the server.
        --watch-all:        Both of the options combined.
        --cluster:          Cluster mode. Creates a forked process for the number provided in the config cluster->numberOfForkedProcesses,
                            if numberOfForkedProcesses is not provided then a fork is created for each available CPU.

    - lint [--fix] [path]
    Lints the source-code with ts-lint and prettier.
        --fix:          Fixes auto-fixable issues.
        path:           Optional. Relative path of files inside the project to linted, it can be used with
                        standard file matchers.

    - test [pattern] [--unit] [--integration] [--ci] [--graphql] [--browser]
    Executes tests.
        pattern:        Optional. Pattern which is used to filter the test files.
        --integration:  Executes the BDD Cucumber integration tests of the current package.
        --unit:         Executes the Mocha unit tests of the package in an f-stream context.
        --ci:           Provides CI context functionality: JUnit reports and code coverage support
        --graphql:      Executes the Graphql tests.
        --browser:      Makes the browser visible when running the integration tests (not consider when using --ci flag).
        --service-options:   service options that should be active for unit and integration tests.

    - compile-plugin
    Compiles an Xtrem UI Plugin.

    - compile [--skip-client] [--skip-cop] [--skip-server] [--skip-dts] [--binary] [--references] [--skip-clean] [--skip-api-client] [--instrumented]
    Creates a ready-to-use Xtrem package.
        --skip-client:        Skips the compilation of client side artifacts.
        --skip-cop:           Skips code verification.
        --skip-server:        Skips the compilation of server side artifacts.
        --skip-dts:           Skips the creation of a single package TS declaration file.
        --skip-api-client:    Skips the generation of a single package API reference TS declaration package.
        --only-api-client:    Generations a single package API reference TS declaration package without building the rest.
        --only-changed:       Compiles only changed client artifacts, prevents the build folder from being cleared.
        --skip-clean:         Skips removal of existing build output.
        --references:         Compiles all project's references.
        --binary:             Compiles the server side artifacts into a closed-source binary format.
        --instrumented:       Instruments the compiled codebase

    - layers -- [--load {layers}] [--extract {layer}] [--tenant {tenantId}];
    Load or extract CSV data
        --load:        Loads the CSV files of the supplied layers into the database.
        --extract:      Extracts the supplied CSV layer. All the CSV files of this layer will be regenerated.
        --tenant:             Optional tenant id, 777...777 by default

    - manage -- [--init-tenant <base64> [--layers]] [--update-tenant <base64>] [--delete-tenant <tenantId>]
    Manage the Xtrem application

        Note: This command is deprecated in favor of the 'tenant' command.

    - tenant -- [--init <base64> [--layers]] [--update <base64>] [--delete <tenantId>] [--export <tenantSelector> [options...]]
    Manage a tenant
            --init:          Will initialize a new tenant, loading all the default setup data and creating an admin user of the current application for a given tenant Id. Data must be provided as a JSON object encoded in base64. All data in the JSON object must be provided and will be validated.
                Data has to contain:
                * The tenant's id and name ,
                * The customer's id and name the tenant belongs to,
                * The administer's email, firstname, lastname and locale.

                Data can also contain a list of service options that has to be activated.

                These information have to be set as in the following example:
                {
                    "customer": {
                        "id": "00000000000000000001",
                        "name": "acme"
                    },
                    "tenant": {
                        "id": "000000000000000000000",
                        "name": "dev"
                    },
                    "adminUser": {
                        "email": "<EMAIL>",
                        "firstName": "John",
                        "lastName": "Doe",
                        "locale": "en-US"
                    },
                    "serviceOptions": {
                        "@sage/xtrem-show-case/show-case-discount-option": true,
                        "@sage/xtrem-show-case/show-case-experimental-option": false,
                        "@sage/xtrem-show-case/show-case-option-high-level": true,
                        "@sage/xtrem-show-case/show-case-work-in-progress-option": false
                    }
                }

                Once encoded in base 64 the previous JSON becomes:
                ********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

            --update:       Will update an existing tenant, loading all the default setup data. Data must be provided as a JSON object encoded in base64. All data in the JSON object must be provided and will be validated.
                Data has to contain:
                * The tenant's id and name ,
                * Its can also contain a list of service options that has to be activated.

                Data can also contain a list of service options that has to be activated

                These information have to be set as in the following example:
                {
                    "tenant": {
                        "id": "000000000000000000000"
                    },
                    "packages": {
                        "@sage/xtrem-show-case": true
                    },
                    "serviceOptions": {
                        "showCaseDiscountOption": true,
                        "showCaseExperimentalOption": true,
                        "showCaseOptionHighLevel": true,
                        "showCaseWorkInProgressOption": false

                    },
                    "packs": {
                        "@sage/xtrem-show-case": true,
                        "@sage/xtrem-system": true
                    }
                }

                Once encoded in base 64 the previous JSON becomes:
                ewoJInRlbmFudCI6IHsKCQkiaWQiOiAiMDAwMDAwMDAwMDAwMDAwMDAwMDAwIgoJfSwKCSJwYWNrYWdlcyI6IHsKCQkiQHNhZ2UveHRyZW0tc2hvdy1jYXNlIjogdHJ1ZQoJfSwKCSJzZXJ2aWNlT3B0aW9ucyI6IHsKCQkiQHNhZ2UveHRyZW0tc2hvdy1jYXNlL3Nob3dDYXNlRGlzY291bnRPcHRpb24iOiB0cnVlLAoJCSJAc2FnZS94dHJlbS1zaG93LWNhc2Uvc2hvd0Nhc2VFeHBlcmltZW50YWxPcHRpb24iOiB0cnVlLAoJCSJAc2FnZS94dHJlbS1zaG93LWNhc2Uvc2hvd0Nhc2VPcHRpb25IaWdoTGV2ZWwiOiB0cnVlLAoJCSJAc2FnZS94dHJlbS1zaG93LWNhc2Uvc2hvd0Nhc2VXb3JrSW5Qcm9ncmVzc09wdGlvbiI6IGZhbHNlCgoJfSwKCSJwYWNrcyI6IHsKCQkiQHNhZ2UveHRyZW0tc2hvdy1jYXNlIjogdHJ1ZSwKCQkiQHNhZ2UveHRyZW0tc3lzdGVtIjogdHJ1ZQoJfQp9


            *    --layers:          A list of layers to load the initial data of the tenant can be provided to the init command. If no layer are provided, the 'setup' layer will be loaded by default. If a list of layers are provided, it must be formatted with a comma separator and no space. For instance: --layers=setup,demo-data1,demo-data2

                example:
                xtrem tenant --init 'eyJjdXN0b21lciI6eyJpZCI6IjAwMDAwMDAwMDAwMDAwMDAwMDAxIiwibmFtZSI6ImFjbWUifSwidGVuYW50Ijp7ImlkIjoiMDAwMDAwMDAwMDAwMDAwMDAwMDAwIiwibmFtZSI6ImRldiJ9LCJhZG1pblVzZXIiOnsiZW1haWwiOiJqb2huLmRvZUBhY21lLmNvbSIsImZpcnN0TmFtZSI6IkpvaG4iLCJsYXN0TmFtZSI6IkRvZSIsImxvY2FsZSI6ImVuLVVTIn19='

            --delete:          Will delete all tenant data of the current application for a given tenant Id.

                example:
                xtrem tenant --delete "000000000000000000000"

            --export:          Export all tenant data of a given <tenantSelector> in the current database schema.
                               <tenantSelector> can be a tenant's id, directoryName or directoryName's glob..
                               It will generate 2 csv files per table, one for the non nullable data and one for nullable, then zip all of them
                               in data/exports/<tenant-id>/<export-id>.zip file.
                               The zip will be uploaded to a S3 bucket if a location is provided and the s3 config is set

            *    --export-id:       An Id to identify this export. This id is used to generate a zip file with the name <export-id>.zip. This option
                                    cannot be set if we have multiple tenant exports.
            *    --location:        The location is in the form s3://<bucket-name>/[path-prefix]
                                    If no path-prefix is provided, the default is to put the <export-id>.zip file on s3://<bucket-name>/exports/<tenant-id>
            *    --keep-all-values: If specified, sensitive data flagged on properties with the exportValue decorator set, will export the actual value
                                    contained in the database. The default is not to export these and export the exportValue instead.

            example:
            xtrem tenant --export "000000000000000000000" --export-id "my-id" --location s3://my-bucket-name
            xtrem tenant --export "000000000000000000000" --export-id "my-id" --location s3://my-bucket-name/my/specific/path
            xtrem tenant --export "000000000000000000000" --keep-all-values
            xtrem tenant --export "reference-*" --location s3://my-bucket-name

        --import:   Import all tenant data of a given tenantId from the location provided. If the location is an S3 Uri, the zip file is downloaded, extracted and the extracted csv files are then imported. If the --location is a path to a local zip, then similarly it is extracted and the extracted csv files are then imported. If the import fails in the middle of an import the tenant must be deleted before attempting the import again.

        If the location is an S3 Uri, the zip file is downloaded, extracted and the extracted csv files are then imported.
        If the location is a path to a local zip, then similarly it is extracted and the extracted csv files are then imported.
        If the userAdmin is provided, the it will be created at the end of the import.
        If the import fails in the middle of an import the tenant must be deleted before attempting the import again.

        Data has to contain:
        * The tenant's id and name,
        * The customer's id and name the tenant belongs to,
        * The location: S3Uri (looks like s3://<bucket-name>/exports/<tenant-id>) or path on the server were the zip of export data is located.

        Data can also contain:
        * (optional) The administrator's email, firstname, lastname and locale.

        These information have to be set as in the following example:

        {
            "customer": {
                "id": "00000000000000000001",
                "name": "acme"
            },
            "tenant": {
                "id": "000000000000000000000",
                "name": "dev"
            },
            "location": "s3://xtrem-dev-eu-global/tenants/export_id.tgz",
            "adminUser": {
                "email": "<EMAIL>",
                "firstName": "John",
                "lastName": "Doe",
                "locale": "en-US"
            }
        }
        example:
            xtrem tenant --import '************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************'


            Note: --init, --update, --delete, --export and --import are mutually exclusive.

    - schema -- [--create [--reset-database] [--reset-schema]]  [--upgrade [--post-process]] [--dump-to-s3] [restore-from-s3 [version]] [test-upgrade [version]] [--reset [--layers]] [--fix-column-order]
    Manage the application schema
            --create:               Will create the SQL schema of the current application. This method will iterate over all the persistent nodes and create their tables, indexes and foreign keys.
            *    --force:           Obsolete. Use --reset-database instead.
            *    --reset-database:  Will force reset of database if it already exists. It only applies to --create.
            *    --reset-schema:    Will force reset of schema if it already exists. It only applies to --create.
            --upgrade:              Will upgrade all tables of the current application. This method will iterate over all the persistent nodes and create their tables, indexes and foreign keys. If any version moves backwards the operation will abort.
            *  --post-process       Should be called after the upgrade: post processes.
            --dump-to-s3:           Will dump the current schema to a S3 bucket.
                example:
                xtrem schema --dump-to-s3
            --restore-from-s3:      Will restore the given version of the current schema from a S3 bucket.
            -    --s3ConfigType:    The S3 configuration type is an optional argument that can be specified as either dbDumps (default value), upgradeBundles, clusterCuBackup.
            --list-s3-versions:     Will list all the available versions.
            --test-upgrade:         Will restore the given version of the current schema from a S3 bucket and run the upgrade process.
            *    --force:           (Only valid when called from CI): will force a reset of the database
            --reset:                Will back up current application users, recreate SQL schema, initialize tenant data of tenant passed and then recreate users. WARNING: this is not a permanent command and must be used with great care, all data on your schema will be lost when running this command, only users will be recreated.
            *    --layers:          A list of layers to load the initial data of the tenant can be provided to the reset    command. If no layers are provided, the 'setup' layer will be loaded by default. If a list of layers is provided, it must be formatted with a comma separator and no space. For instance: --layers setup,demo-data1,demo-data2
                example:
                xtrem schema --reset --layers setup,demo
            *   --fix-column-order:  Special command to the correct the ordering of the columns in tables. Take care that a backup of the
                                     database is done prior to executing this command.

    - extract-data -- [--destDirectory] [--splitPerTenant]  [--tenant]
    Extract consolidated data for all or a specified tenant, to a single specified destination directory, with a csv per table
            --destDirectory:        Destination directory where data should be exported
            --splitPerTenant:       Data will be split into sub-directories, one per tenant, named as the Tenant ID, otherwise all data will be extracted into a single csv per table
            --tenant:               Supplied if a specific tenant should be extracted

```
