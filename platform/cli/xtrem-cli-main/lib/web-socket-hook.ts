import { Application } from '@sage/xtrem-core';
import { WebSocketManager, WebSocketNotificationManager } from '@sage/xtrem-infrastructure-adapter';
import { GraphqlEndpointHooks } from '@sage/xtrem-service';

/**
 * Install the web socket hook into @sage/xtrem-service.
 * This hook allows us to have a leaner packaging of X3 Services.
 * It eliminates a direct dependency between @sage/xtrem-service and @sage/xtrem-infrastructure-adapter,
 * which would bring a lot of heavy dependencies (@aws-sdk) into the service package.
 */
export function initializeWebSocketHook(): void {
    GraphqlEndpointHooks.startWebSocketServices = (application: Application) => {
        WebSocketManager.startService(application);
        const httpServer = application.graphqlHttpServer;
        if (httpServer) {
            WebSocketNotificationManager.startDevelopmentService(httpServer);
        }
    };
}
