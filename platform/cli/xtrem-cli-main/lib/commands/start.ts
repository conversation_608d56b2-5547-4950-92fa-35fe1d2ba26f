import { cli<PERSON>ontext, pluggable<PERSON>uilder, pluggableHandler, printWarning } from '@sage/xtrem-cli-lib';
import { SystemError, allowedStartChannels } from '@sage/xtrem-core';
import type { Argv } from 'yargs';
import { StartOptions, createStartContext, start } from './handlers/start';

/**
 * The command name and its aliases
 */
export const command = ['start', 's'];

/**
 * The command description
 */
export const desc = 'Start an xtrem application';

/**
 * The command builder that defines the syntax of this command
 */
export const builder = (yargs: Argv) =>
    pluggableBuilder(
        command,
        yargs
            .version(false)
            .option('channels', {
                desc:
                    'Comma separated list of channels to be active on the server. The allowed channels are:\n' +
                    "'routing':      Starts the communication routing services.\n" +
                    "'listeners':    Starts the communication listeners.\n" +
                    "'graphql':      Starts the server in interactive mode. Endpoints are active by default. " +
                    'If the --channels option is not supplied, then all the channels are active by default.',
                type: 'array',
                default: allowedStartChannels,
                choices: allowedStartChannels,
                coerce: (options: string[]) => {
                    // Note: the content of options may vary, depending on the syntax used to run the command;
                    // - '--channels listeners graphql': options = ['listeners', 'graphql']
                    // - '--channels listeners,graphql': options = ['listeners,graphql'] (1st item contains the string with ',' as a delimiter)
                    let result: string[] = [];
                    options.forEach((option: string) => {
                        result = [...result, ...option.split(',')];
                    });
                    return result;
                },
            })
            .option('services', {
                desc:
                    'Comma separated list of packages to be started as a service. ' +
                    'If the --services option is ommitted the start command loads all the packages and run all their startService methods. ' +
                    'This allows to easily start all services in development mode',
                type: 'string',
            })
            .option('web-sockets', {
                desc: 'TBD',
                type: 'string',
            })
            .option('cluster', {
                desc:
                    'Cluster mode. Creates a forked process given the config cluster.numberOfForkedProcesses, ' +
                    'if numberOfForkedProcesses is not provided then a fork is created for each available CPU',
                type: 'boolean',
            })
            .option('config-extension-location', {
                desc: 'Location on the file system where extensions to the config can be found.',
                type: 'string',
            })
            .option('references', {
                desc: 'TBD',
                type: 'boolean',
            })
            .option('queues', {
                desc: 'Comma separated list of queues to explicitly start',
                type: 'string',
                default: undefined,
                coerce: (options?: string) => {
                    if (options == null) return options;
                    // options is a string, but we want a list
                    return options.split(',').filter(q => q);
                },
            })
            // TODO: investigate `any` type introduced during typescript 4.6 upgrade
            .middleware((argv: any) => {
                // create internal aliases to be used by the command options
                argv.isUsingReferences = argv.references;
                return argv;
            })
            .example('$0 start', 'Start all services and channels'),
    );

// [
//     ['$0 start', 'Start all services and channels'],
//     ["$0 start --channels 'graphql'", 'Start just the graphql channel'],
//     ['$0 start --services=@sage/xtrem-communication', 'Start the communication service'],
//     [
//         '$0 start --services=communication',
//         "Start the communication service ommitting the '@sage/xtrem-' default prefix",
//     ],
// ]

/**
 * The command handler called to execute the actions of this command
 */
export const handler = (argv: Argv<StartOptions>) =>
    (async () => {
        const options = argv as StartOptions;
        const startContext = createStartContext(options);
        const pluggableResult = await pluggableHandler(command, argv, startContext);
        if (pluggableResult.skipDefault) {
            printWarning(cliContext.executionMode, 'skip default main start');
            return;
        }
        if (!startContext) {
            throw new SystemError('Failed to create start context');
        }
        await start(startContext);
    })();
