import { Config<PERSON>anager, Dict, Logger, funnel, graphQlTimeLimitInSeconds } from '@sage/xtrem-core';
import { GraphqlEndpointHooks } from '@sage/xtrem-service';
import { Cluster, Worker } from 'cluster';
import { promisify } from 'util';

const logger = new Logger(__filename, 'cli');
export class XtremWorker {
    #port: number | undefined;

    #worker: Worker;

    #isReady = false;

    requestKeys: string[] = [];

    promise: Promise<void>;

    #resolve: () => void;

    #reject: (reason?: any) => void;

    readonly #workerId: string;

    /**
     *
     * @param workerId the identifier of the worker
     * @param cluster the cluster instance used to manage(fork/spawn) a worker
     */
    constructor(
        readonly initWorkerId: string,
        readonly cluster: Cluster,
    ) {
        this.#workerId = initWorkerId;
    }

    get workerId(): string {
        return this.#workerId;
    }

    init(): this {
        this.fork();
        return this;
    }

    /**
     * reset the current worker instance with newly forked one.
     */
    fork(requestKey?: string): void {
        this.#isReady = false;
        this.#port = undefined;
        this.#worker = this.cluster.fork({ XTREM_WORKER_ID: this.workerId });
        this.promise = new Promise<void>((resolve, reject) => {
            this.#resolve = resolve;
            this.#reject = reject;
        });

        const requestTimeout = graphQlTimeLimitInSeconds() * 1000 - 500;

        const timeout = setTimeout(() => {
            this.#reject(new Error(`Failed to start worker with worker id ${this.workerId}`));
        }, requestTimeout);

        logger.info(`Forked worker for ${this.workerId}(pid: ${this.#worker.process.pid})`);
        // We listen to the 'message' event. When the worker starts the xtrem server we use process.send to send
        // a message back to the main process with information like the port allocated to the service
        // When we receive the message we set the port on the worker, now we have the port to route the requests to.
        this.#worker.on('message', async (message: any) => {
            if (message.event === 'listening' && message.workerId === this.initWorkerId) {
                this.#port = message.port;
                this.#isReady = true;
                clearTimeout(timeout);
                this.#resolve();
            }
            if (message.event === 'killWorkers') {
                await XtremWorker.killWorkers(message.requestSource);
            }
        });

        if (requestKey && !this.requestKeys.includes(requestKey)) this.requestKeys.push(requestKey);
    }

    /**
     * The current Worker instance that is alive
     */
    get worker(): Worker {
        if (this.workerExists) return this.#worker;

        this.fork();

        return this.#worker;
    }

    /**
     * The index of the worker in the cluster instance workers dictionary
     */
    get index(): number {
        if (!this.worker) return -1;
        return this.worker.id;
    }

    /**
     * Is the worker ready to use, this set when the worker process sends a message back to the main process with the port
     */
    get isReady(): boolean {
        return this.#isReady;
    }

    /**
     * Is the worker set and is the worker alive
     */
    get workerExists(): boolean {
        return this.#worker != null && !this.#worker.isDead();
    }

    /**
     * Port of the current worker
     */
    get port(): number {
        if (!this.#port) throw new Error(`[${this.worker}]: port not set.`);
        return this.#port;
    }

    static workers: Dict<XtremWorker[]> = {};

    static counters: Dict<number> = {};

    /**
     * Add a worker to the workers list
     * @param cluster
     * @param startIndex
     * @param stopIndex
     */
    static addWorkers(cluster: Cluster, requestSource: string, startIndex: number, stopIndex: number): void {
        if (!this.workers[requestSource]) {
            this.workers[requestSource] = [];
            this.counters[requestSource] = 0;
        }
        for (let i = startIndex; i < stopIndex; i += 1) {
            this.workers[requestSource].push(new XtremWorker(requestSource, cluster).init());
        }
    }

    /**
     * Seed a worker for the request source, if the worker pool does not have enough workers
     * @param cluster
     * @param requestSource
     * @param maxWorkers
     */
    static seedWorker(cluster: Cluster, requestSource: string, maxWorkers = 2): void {
        const workers = this.workers[requestSource] ?? [];
        if (workers.length < maxWorkers) {
            const numWorkers = maxWorkers - workers.length;
            logger.info(`Seeding workers for ${requestSource} with ${numWorkers} workers`);
            this.addWorkers(cluster, requestSource, 0, numWorkers);
        }
    }

    static defaultSeedInterval: NodeJS.Timeout;

    static deferredWorkerTimeout: Dict<NodeJS.Timeout> = {};

    static seeded = false;

    static getMaxWorkers(requestSource: string): number {
        return (
            GraphqlEndpointHooks.getSourceMaxWorkers(requestSource) ??
            ConfigManager.current.server?.worker?.workersPerRequestSource ??
            2
        );
    }

    /**
     * Spawn workers for the known sources as far as possible
     * @param cluster
     */
    static seedWorkers(cluster: Cluster): void {
        const requestSources = GraphqlEndpointHooks.getSourcesToSeed().filter(
            source => !this.workers[source] || this.workers[source].length === 0,
        );

        requestSources.forEach(requestSource => {
            const maxWorkers = this.getMaxWorkers(requestSource);

            this.seedWorker(cluster, requestSource, maxWorkers);
        });

        this.seeded = true;
    }

    /**
     * Initialize the list of workers
     * @param cluster
     * @param maxWorkers
     */
    static initializeWorkers(cluster: Cluster, requestSource: string, maxWorkers: number): void {
        this.addWorkers(cluster, requestSource, 0, maxWorkers);
    }

    /**
     * sleep for the supplied number of ms
     */
    static async sleep(ms: number): Promise<void> {
        await promisify(setTimeout)(ms);
    }

    private static async fillRequestSource(cluster: Cluster, requestSource: string): Promise<boolean> {
        const maxWorkers = this.getMaxWorkers(requestSource);

        // If the worker pool has reached the maximum number of workers for the requestSource, return true
        if (this.workers[requestSource] && this.workers[requestSource].length === maxWorkers) {
            return true;
        }

        // Wait until worker seeding is complete or return false and the request will result in a 503
        let seededCounter = 0;
        while (!this.seeded) {
            await this.sleep(100);
            seededCounter += 1;

            if (seededCounter > 10) {
                return false;
            }
        }

        // We do not have a worker pool for the current requestSource or the pool does not have any workers
        // We initialize 1 worker for the request source. This is the minimum number of workers required to serve the request quickly
        if (!this.workers[requestSource] || this.workers[requestSource].length === 0) {
            this.initializeWorkers(cluster, requestSource, 1);
        }

        // If the worker pool does not have enough workers, we defer the forking of the missing workers to the next event loop cycle
        if (this.workers[requestSource].length < maxWorkers) {
            // Deferring the fork to the next event loop cycle to avoid blocking the current request
            if (!this.deferredWorkerTimeout[requestSource]) {
                const numWorkers = maxWorkers - this.workers[requestSource].length;
                this.deferredWorkerTimeout[requestSource] = setTimeout(() => {
                    // Initialize the workers
                    XtremWorker.initializeWorkers(cluster, requestSource, numWorkers);
                    // remove timeout
                    delete XtremWorker.deferredWorkerTimeout[requestSource];
                });
            }
        }

        return true;
    }

    /**
     * Get the next worker that is ready from the workers list
     * @param cluster
     * @param requestSource
     * @param count
     * @returns
     */
    static async getNextWorker(
        cluster: Cluster,
        requestSource: string,
        requestKey: string,
    ): Promise<XtremWorker | undefined> {
        if (this.killWorkersPromises[requestSource] != null) await this.killWorkersPromises[requestSource];

        if (!(await this.fillRequestSource(cluster, requestSource))) {
            return undefined;
        }

        // Route the request to the worker allocated to the request key
        let worker: XtremWorker | undefined = this.workers[requestSource].find(w => w.requestKeys.includes(requestKey));

        // if there is no worker allocated to the request key, get the next using round-robin
        if (worker == null) {
            worker = this.workers[requestSource][this.counters[requestSource]];
            if (!worker) {
                if (!(await this.fillRequestSource(cluster, requestSource))) {
                    return undefined;
                }
            }
            // Increment the round-robin counter for the relevant request source
            this.counters[requestSource] = (this.counters[requestSource] + 1) % this.workers[requestSource].length;

            if (requestKey && !worker.requestKeys.includes(requestKey)) worker.requestKeys.push(requestKey);
        }

        // Worker was killed fork a new one
        if (!worker.workerExists) {
            // if the worker is dead, fork a new one
            worker.fork(requestKey);
        }

        if (!worker.isReady) {
            return worker.promise
                .then(() => {
                    return worker;
                })
                .catch(error => {
                    logger.error(error);
                    return undefined;
                });
        }

        return Promise.resolve(worker);
    }

    static killWorkersPromises: Dict<Promise<void>> = {};

    static workerKillFunnel = funnel(1);

    /**
     * Kill all workers for the request source
     * On the next worker request, new workers will be forked
     * @param requestSource
     */
    static async killWorkers(requestSource: string): Promise<void> {
        if (this.killWorkersPromises[requestSource] != null) await this.killWorkersPromises[requestSource];
        this.killWorkersPromises[requestSource] = this.workerKillFunnel(() => {
            if (this.workers[requestSource]) {
                const copyWorkers = this.workers[requestSource].slice();
                this.workers[requestSource] = [];

                copyWorkers.forEach(worker => {
                    logger.info(`killing worker: workerId=${worker.#workerId}, pid = ${worker.#worker.process.pid}`);
                    if (worker.workerExists) {
                        try {
                            if (worker.#worker.process.pid) {
                                process.kill(worker.#worker.process.pid);
                                logger.info(
                                    `killed worker workerId=${worker.#workerId}, pid = ${worker.#worker.process.pid}`,
                                );
                            }
                        } catch (error) {
                            logger.error(`Error killing worker ${worker.workerId} ${error.message}`);
                        }
                    }
                });
            }
            return Promise.resolve();
        });

        await this.killWorkersPromises[requestSource];
    }
}
