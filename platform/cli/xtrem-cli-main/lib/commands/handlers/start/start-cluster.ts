import { ApplicationStartServicesOptions, ConfigManager, Logger } from '@sage/xtrem-core';
import { Cluster } from 'cluster';
import * as os from 'os';
import { startServices } from './start-server';

const cluster: Cluster = require('cluster');

const logger = new Logger(__filename, 'cli');

export const startCluster = async (dir: string, options: ApplicationStartServicesOptions): Promise<void> => {
    if (cluster.isPrimary) {
        logger.info(`Master ${process.pid} is running`);

        const config = ConfigManager.load(dir, 'start', options.configExtensionLocation);

        // Fork workers.
        const numForks = config.cluster?.numberOfForkedProcesses || os.cpus().length;
        for (let i = 0; i < numForks; i += 1) {
            cluster.fork({ XTREM_CLUSTER_FORK: '1' });
        }
        let numWorkersExited = 0;
        cluster.on('exit', (worker: any, code: number, signal: string) => {
            logger.info(`Worker ${worker.process.pid} died (code ${code}, signal ${signal})`);
            numWorkersExited += 1;
            // exit main process if all workers have exited
            if (numWorkersExited === numForks) {
                process.exit(code);
            }
        });
    } else {
        logger.info(`Worker ${process.pid} started`);
        await startServices(dir, options);
    }
};
