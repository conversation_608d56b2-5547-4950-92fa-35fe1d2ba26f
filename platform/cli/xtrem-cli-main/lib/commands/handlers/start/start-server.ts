import {
    Application,
    ApplicationManager,
    ApplicationStartServicesOptions,
    ConfigManager,
    Context,
    Logger,
    allowedStartChannels,
    registerTlsChangeListener,
} from '@sage/xtrem-core';
import { GraphqlEndpointHooks, startApplication } from '@sage/xtrem-service';
import { Cluster, Worker } from 'cluster';
import * as http from 'http';
import * as https from 'https';
import { XtremWorker } from './worker';

const logger = new Logger(__filename, 'cli');

/**
 * Start a service for the current process
 * @param dir
 * @param options
 * @returns
 */
export const startWorkerServices = async (application: Application): Promise<void> => {
    await application.packageManager.validatePackageVersions();
    await Context.subscribeToCacheNotifications(application);

    // resolve the permissions for all activities of the application once
    application.activityManager.resolvePermissions();
    application.validateActivities();

    await startApplication(application);
};

/**
 * Manage the start command, if multi-worker service are enabled then start the proxy service, otherwise start a single process as normal
 * @param dir
 * @param options
 * @returns
 */
export const startServices = async (dir: string, options: ApplicationStartServicesOptions): Promise<void> => {
    if (options.configExtensionLocation) {
        logger.info(`Config extension provided, ${options.configExtensionLocation}`);
        ConfigManager.load(dir, 'start', options.configExtensionLocation);
    }

    // eslint-disable-next-line global-require
    const cluster: Cluster = require('cluster');

    const config = ConfigManager.current;

    const deploymentMode = config.deploymentMode;
    if (
        allowedStartChannels.every(allowedChannel => options.channels.includes(allowedChannel)) &&
        deploymentMode === 'production'
    )
        logger.warn(`Starting the server with all channels active (${allowedStartChannels}).`);

    // We need to load the application here so that the cli hooks are loaded early enough
    const application = await ApplicationManager.getApplication(dir, {
        applicationType: 'service',
        startOptions: options,
    });

    const useMultiWorkerService = process.env.XTREM_USE_MULTI_WORKER === 'true';

    if (process.env.XTREM_CLUSTER_FORK === '1' && useMultiWorkerService) {
        throw new Error(`Cluster mode cannot be used with multi-service workers.`);
    }

    // We are in the primary process, direct from the start handler
    if (cluster.isPrimary) {
        // If multi-workers service is enabled then start the proxy service, the server will listen to the configured/default port
        // and then forward the request to the relevant worker process
        if (useMultiWorkerService) {
            return startProxyService(cluster);
        }
    }

    if (cluster.isWorker) {
        // We are in a forked worker process and multi-worker services is enabled, the XTREM_WORKER_ID must be set in a process env variable. If it is not set throw an error.
        if (useMultiWorkerService && !process.env.XTREM_WORKER_ID) {
            throw new Error(`Worker ID not supplied for worker ${process.pid}`);
        }
    }

    // we are in a single service environment or a multi-worker service worker, start the service
    return startWorkerServices(application);
};

const httpProxy = require('http-proxy');

function getFullUrl(req: http.IncomingMessage, config: any): string {
    return `${req.headers['x-forwarded-proto'] || config?.server?.ssl ? 'https' : 'http'}://${req.headers.host}${
        req.url
    }`;
}

function isAggregateError(err: Error): err is AggregateError {
    return (err as AggregateError).errors !== undefined;
}

/**
 * Start the proxy service that will route requests to the relevant worker/forked process
 * @param cluster
 * @returns
 */
function startProxyService(cluster: Cluster): Promise<undefined> {
    const config = ConfigManager.current;

    cluster.on('exit', (worker: Worker, code: number, signal: string): void => {
        if (signal) {
            logger.info(`worker ${worker.process.pid} was killed by signal: ${signal}`);
        } else if (code !== 0) {
            logger.info(`worker ${worker.process.pid} exited with error code: ${code}`);
        } else {
            logger.info(`worker ${worker.process.pid} successfully exited!`);
        }
    });

    // We create the proxy server as an http server, i.e. we do not provided any ssl config, inter service communication will not be with https
    // eslint-disable-next-line new-cap
    const proxy = new httpProxy.createProxyServer();

    proxy.on('error', (error: any, req: http.IncomingMessage, res: http.ServerResponse) => {
        // Error handler for the proxy server
        // If this listener is not implemented, these errors will be uncaught and will eventually force the service to stop.
        const fullUrl = getFullUrl(req, config);
        if (isAggregateError(error) && Array.isArray(error.errors)) {
            error.errors.forEach((e: any) => {
                logger.info(`Proxy error: ${e.stack} ${e.errno}, ${e.code}, ${e.address},${e.port})}`);
            });
        } else {
            logger.info(`Proxy error: ${error.stack} ${error.errno}, ${error.code}, ${error.address},${error.port})}`);
        }

        res.setHeader('Refresh', `5;url=${fullUrl}`);
        res.writeHead(503, { 'Content-Type': 'text/plain' });
        res.end('Service unavailable');
    });

    let proxyHttpServer: http.Server | https.Server;
    const proxyListener: http.RequestListener<typeof http.IncomingMessage, typeof http.ServerResponse> = async (
        req,
        res,
    ) => {
        // ping route should always return a response for health check
        if (req.url && /^\/ping$/.test(req.url)) {
            res.writeHead(200, { 'Content-Type': 'text/plain' });
            res.end('Service available');
            return;
        }
        const protocol = config.server?.ssl ? 'https' : 'http';
        // Get the worker id from the request and it's headers
        const requestSource = await GraphqlEndpointHooks.getRequestSource(req);
        const requestKey = await GraphqlEndpointHooks.getRequestRoutingKey(req);

        const worker = await XtremWorker.getNextWorker(cluster, requestSource, requestKey);

        // A worker could not be found return a 503 status
        if (!worker) {
            const fullUrl = getFullUrl(req, config);
            logger.info(`Proxy error: Failed to find worker for request to ${requestSource}`);
            // Set Refresh header to reload the page after 5 seconds
            res.setHeader('Refresh', `5;url=${fullUrl}`);
            res.writeHead(503, { 'Content-Type': 'text/plain' });
            res.end('Failed to find worker');
            return;
        }

        const profiler = logger.info(`proxy request for ${requestSource}`);

        profiler.success(`proxy request for ${requestSource} forwarded to ${worker.port}`);

        proxy.web(req, res, {
            target: `${protocol}://localhost:${worker.port}`,
        });
    };

    if (config?.server?.ssl) {
        proxyHttpServer = https.createServer(config.server.ssl, proxyListener);
        registerTlsChangeListener(proxyHttpServer as https.Server, 'server.ssl', 'HTTPS proxy');
    } else {
        proxyHttpServer = http.createServer(proxyListener);
    }

    //
    // Listen to the `upgrade` event and proxy the
    // WebSocket requests as well.
    //
    // We don't need it for now
    /* proxyHttpServer.on('upgrade', (req, socket, head) => {
        proxy.ws(req, socket, head);
    }); */

    const serverPort = config.server?.port || 8240;
    proxyHttpServer.listen(serverPort);
    const tlsMesage = config.server?.ssl ? ' over TLS ' : '';

    logger.info(`Proxy service is listening on port ${serverPort}${tlsMesage}`);

    XtremWorker.seedWorkers(cluster);

    Application.emitter.on('killWorkers', async (requestSource: string) => {
        await XtremWorker.killWorkers(requestSource);
    });

    return Promise.resolve(undefined);
}
