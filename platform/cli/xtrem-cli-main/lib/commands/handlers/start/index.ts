import { cliContext, quitWithError } from '@sage/xtrem-cli-lib';
import { ApplicationStartServicesOptions, StartChannel } from '@sage/xtrem-core';
import { startCluster } from './start-cluster';
import { startServices } from './start-server';

export { startServices };

export interface StartOptions {
    isUsingReferences?: boolean;
    cluster?: boolean;
    channels?: StartChannel[];
    services?: string;
    webSockets?: string;
    configExtensionLocation?: string;
    /**
     * Names of queues to explicitly start
     * If not set, all the queues will be started
     */
    queues?: string[];
}

export interface StartContext {
    dir: string;
    deployedApp: any;
    options: StartOptions;
    startServerOptions: ApplicationStartServicesOptions;
}

export const scriptName = '.cli-start.js';

export function createStartContext(options: StartOptions): StartContext {
    const { executionMode, dir } = cliContext;
    try {
        const deployedApp = { app: null };
        const webSockets = options.webSockets ? options.webSockets.split(',') : [];
        const startServerOptions: ApplicationStartServicesOptions = {
            channels: options.channels!, // ! is OK as there is a default on the option
            services: options.services?.split(',') || [],
            webSockets,
            configExtensionLocation: options.configExtensionLocation,
            queues: options.queues,
        };

        return {
            dir,
            deployedApp,
            options,
            startServerOptions,
        };
    } catch (e) {
        quitWithError(executionMode, e);
        return {} as never;
    }
}

export const start = (startContext: StartContext) =>
    (async () => {
        try {
            const { options, dir, startServerOptions } = startContext;

            if (options.cluster) {
                await startCluster(dir, startServerOptions);
            } else {
                await startServices(dir, startServerOptions);
            }
        } catch (e) {
            quitWithError(cliContext.executionMode, e);
        }
    })();
