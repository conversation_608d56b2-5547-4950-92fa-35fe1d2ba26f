# Xtrem-cli-atp TypeScript Style Guide

This style guide is based on our experience with XTREM-CLI-ATP source code, the various issues and bugs we have encountered in the past, and our desire to make the code more robust and maintainable. By following these guidelines, we aim to keep our code clean, consistent, and easy to understand.

Sources of inspiration:

-   [Clean Code for JavaScript](https://github.com/ryanmcdermott/clean-code-javascript)
-   [Clean Code for TypeScript](https://github.com/labs42io/clean-code-typescript)
-   [Airbnb JavaScript Style Guide](https://github.com/airbnb/javascript)

## Table of Contents

1. [Function Arguments](#function-arguments)
2. [Types](#types)

## Function Arguments

If your function has **more than 2 arguments**, it might be doing too much. Consider consolidating them into an object.

Using an object for multiple arguments has several benefits:

-   Clearly indicates the expected properties in the function signature, simulating named parameters and enhancing readability.
-   Prevents mixing up the order of arguments.

**Bad:**

```typescript
function createMenu(title: string, body: string, buttonText: string, cancellable: boolean) {
    // ...
}
createMenu('Foo', 'Bar', 'Baz', true);
```

**Good:**

```typescript
interface MenuOptions {
    title: string;
    body: string;
    buttonText: string;
    cancellable: boolean;
}

function createMenu({ title, body, buttonText, cancellable }: MenuOptions) {
    // ...
}
createMenu({
    title: 'Foo',
    body: 'Bar',
    buttonText: 'Baz',
    cancellable: true,
});
```

## Types

Avoid using the `any` type for variable and function return types. Always specify the return type explicitly.

If several functions return the same type, consider creating a generic type for it.

**Bad:**

```typescript
let items: any[] = [];
await browser.waitUntil(async () => {
    items = await browser.$$('ul li');
    return items.length > 1;
});
```

**Good:**

```typescript
let items: WebdriverIO[] = [];
```

**Bad:**

```typescript
export interface waitForDisplayedAndGetElementOptions {
    parent?: any;
    reverse?: boolean;
    ignoreContext?: boolean;
    selector: string;
    interval?: number;
    timeout?: number;
    timeoutMsg?: (selector: Selector) => string;
}

  async waitForDisplayedAndGetElement({
        parent,
        reverse = false,
        ignoreContext = false,
        selector,
        interval,
        timeout,
        timeoutMsg,
    }: waitForDisplayedAndGetElementOptions): Promise<any> {
        const getElement = () =>
            parent ? parent.$(selector) : $(ignoreContext ? selector : `${this.cssSelector} ${selector}`);
        const element = await getElement();
        return getElement();
    }
```

**Good:**

```typescript
  async waitForDisplayedAndGetElement({
        parent,
        reverse = false,
        ignoreContext = false,
        selector,
        interval,
        timeout,
        timeoutMsg,
    }: waitForDisplayedAndGetElementOptions): Promise<WebdriverIO.Element> {
        //...
    }
```
