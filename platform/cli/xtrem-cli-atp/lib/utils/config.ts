import { URL } from 'node:url';

const localHosts: string[] = ['xtrem.localhost.dev-sagextrem.com', 'sdmo-xtrem.localhost.dev-sagextrem.com', 'localhost'];

export const targetUrl = () => {
    if (process.env.TARGET_URL) {
        return new URL(process.env.TARGET_URL);
    }
    return undefined;
};

export const targetUrlOrigin = () => {
    const url = targetUrl();
    return url?.origin;
};

export const targetBaseUrl = () => {
    const url = targetUrl();
    if (url?.pathname === '/unsecuredevlogin') {
        return targetUrlOrigin();
    }
    return url?.href;
};

export const isLocalTargetUrl = () => {
    const host = targetUrl()?.hostname;
    return host && localHosts.includes(host);
};

export const isRemoteTargetUrl = () => {
    const origin = targetUrlOrigin();
    const localTarget = isLocalTargetUrl();
    return origin && !localTarget;
};
