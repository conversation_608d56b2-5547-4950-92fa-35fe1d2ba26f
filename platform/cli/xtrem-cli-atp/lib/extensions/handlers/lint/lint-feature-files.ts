import { <PERSON><PERSON><PERSON><PERSON><PERSON>, GherkinClassic<PERSON><PERSON><PERSON><PERSON><PERSON>, Parse<PERSON> } from '@cucumber/gherkin';
import { FeatureChild, GherkinDocument, IdGenerator, Step } from '@cucumber/messages';
import { ICucumberStepDetails, getFileCucumberSentences } from '@sage/cucumber-steps-parser';
import type { ESLint, Linter } from 'eslint';
import * as fs from 'fs';
import { glob } from 'glob';
import { reverse, trimEnd, trimStart } from 'lodash';
import * as path from 'path';

interface CucumberStepDetailsWithRegexp extends ICucumberStepDetails {
    regExp: RegExp;
}

const generateStepsFromOutline = (isOutline: boolean, scenario: FeatureChild, step: Step): string[] => {
    let headers: string[] = [];
    let exampleValues: string[][] | undefined = [];
    const example = scenario.scenario?.examples?.[0];
    if (isOutline && example) {
        headers = example.tableHeader?.cells.map(c => c.value) || [];
        exampleValues = example.tableBody.map(tb => tb.cells.map(c => c.value));
    } else {
        return [step.text];
    }

    return isOutline && !!exampleValues && !!headers
        ? exampleValues.map(v =>
              headers.reduce((prevValue: string, key: string, index: number) => {
                  return prevValue.replace(`<${key}>`, v[index]);
              }, step.text),
          )
        : [step.text];
};

const createDeprecationWarning = (step: Step): Linter.LintMessage => ({
    column: step.location.column || 1,
    line: step.location.line,
    message: 'Deprecated step definition used.',
    ruleId: 'no-deprecated',
    severity: 1,
});

const createParserError = (): Linter.LintMessage => ({
    line: 1,
    ruleId: 'parsing',
    message: 'Cannot parse feature file',
    severity: 2,
    column: 1,
});

const createMissingDefinitionStepError = (step: Step): Linter.LintMessage => ({
    column: step.location.column || 1,
    line: step.location.line,
    message: 'No matching step definition',
    ruleId: 'step-definition-missing',
    severity: 2,
});

// Deactivated following XT-63069 - to reactivate and adapt if certain characters have to be avoided in scenario title
// const createForbiddenCharacterError = (scenario: Scenario): Linter.LintMessage => ({
//     column: scenario.location.column || 1,
//     line: scenario.location.line,
//     message: 'Invalid scenario title using "," or "/"',
//     ruleId: 'forbidden-characters',
//     severity: 2,
// });

const createFileLintResult = (
    filePath: string,
    errors: Linter.LintMessage[],
    warnings: Linter.LintMessage[],
    fixableWarningCount: number,
) => ({
    warningCount: warnings.length,
    errorCount: errors.length,
    fatalErrorCount: 0,
    fixableErrorCount: 0,
    fixableWarningCount,
    usedDeprecatedRules: [],
    messages: [...errors, ...warnings],
    filePath,
    suppressedMessages: [],
});

const fixStepDefinition = (fileLines: string[], step: Step, stepDefinition: CucumberStepDetailsWithRegexp) => {
    if (step.location.column) {
        const indentation = ' '.repeat(step.location.column - 1);
        const linesToInsert = `${step.keyword}${step.text
            .replace(stepDefinition.regExp, stepDefinition.tagDetails.deprecated)
            .split('\\n')
            .join('\nAnd ')}`
            .split('\n')
            .map(l => `${indentation}${l}`);
        fileLines.splice(step.location.line - 1, 1, ...linesToInsert);
    }
};

/**
 * Lint cucumber feature files.
 * For now it just checks whether any step in the feature files uses a deprecated step definition. Only warnings.
 * @returns the lint result
 */
export const lintFeatureFiles = (dir: string, fix = false): ESLint.LintResult[] => {
    const featureFilePattern = `${dir}${path.sep}@(test|test-features)/**/*.feature`;
    const parser = new Parser<any>(new AstBuilder(IdGenerator.incrementing()), new GherkinClassicTokenMatcher());

    // Load available step definitions for the extracted cucumber step list
    const stepDefinitionsSearchDir = path.join(__dirname, '..', '..', '..', '..', 'cucumber-steps.js');

    // Compile them into regular expressions once
    const availableStepDefinitions = getFileCucumberSentences(
        stepDefinitionsSearchDir,
    ).map<CucumberStepDetailsWithRegexp>(s => ({
        ...s,
        regExp: new RegExp(trimEnd(trimStart(s.rawText, '/'), '/')),
    }));

    const featureFileResults: ESLint.LintResult[] = [];

    // Loop through all feature files
    glob.sync(featureFilePattern).forEach((filePath: string) => {
        let fixableWarningCount = 0;
        const warnings: Linter.LintMessage[] = [];
        const errors: Linter.LintMessage[] = [];
        let ast: GherkinDocument;
        const fileContent = fs.readFileSync(filePath, 'utf-8');
        const fileLines = fileContent.split('\n');
        try {
            ast = parser.parse(fileContent);
        } catch (e) {
            errors.push(createParserError());
            return;
        }

        if (!ast?.feature?.children) {
            return;
        }

        const featureChildren = ast.feature?.children ? reverse([...ast.feature.children]) : [];

        // We need to reverse all AST components so we can update file in reverse order so we don't mess up the line numbers by introducing additional lines where applicable
        featureChildren.forEach(scenario => {
            if (!scenario.scenario) {
                return;
            }

            const isOutline = scenario.scenario.keyword === 'Scenario Outline';

            // Deactivated following XT-63069 - to reactivate and adapt if certain characters have to be avoided in scenario title
            // if (
            //     !filePath.includes('node_modules') &&
            //     !filePath.includes('tmp') &&
            //     !filePath.includes('functional-tests')
            // ) {
            //     // Check if the scenario name contains a forbidden character
            //     if (scenario.scenario.name?.match(/,|\//)) {
            //         errors.push(createForbiddenCharacterError(scenario.scenario));
            //     }
            // }

            const featureSteps = scenario.scenario?.steps ? reverse([...scenario.scenario.steps]) : [];

            featureSteps.forEach(step => {
                // If we have a scenario outline we need to populate each line with the variables and then individually validate them
                const steps = generateStepsFromOutline(isOutline, scenario, step);
                let isFixed = false;
                reverse(steps).forEach(stepText => {
                    // Try to match the step against any known pattern
                    // eslint-disable-next-line no-restricted-syntax
                    for (const stepDefinition of availableStepDefinitions) {
                        if (stepText.match(stepDefinition.regExp)) {
                            if (stepDefinition.tags.indexOf('deprecated') !== -1) {
                                warnings.push(createDeprecationWarning(step));
                                if (fix && !isFixed && stepDefinition.tagDetails.deprecated && step.location.column) {
                                    fixableWarningCount += 1;
                                    fixStepDefinition(fileLines, step, stepDefinition);
                                    isFixed = true;
                                }
                            }
                            return;
                        }
                    }

                    // If the code gets here, it means no step definition was found
                    errors.push(createMissingDefinitionStepError(step));
                });
            });
        });

        if (warnings.length > 0 || errors.length > 0) {
            featureFileResults.push(createFileLintResult(filePath, errors, warnings, fixableWarningCount));
        }

        if (fix) {
            fs.writeFileSync(filePath, fileLines.join('\n'), 'utf-8');
        }
    });

    return featureFileResults;
};
