import { LintOptions } from '@sage/xtrem-cli-dev';
import { cliContext, printError, printInfo, printSuccess, printWarning, quitWithError } from '@sage/xtrem-cli-lib';
import * as chalk from 'chalk';
import { lintFeatureFiles } from './lint-feature-files';

export const lint = (options: LintOptions) => {
    const { executionMode, dir } = cliContext;
    const { fix } = options;

    const featureFileResults = lintFeatureFiles(dir, fix);

    if (featureFileResults.some(r => r.messages.some(m => m.severity === 2))) {
        featureFileResults.forEach(r => {
            r.messages
                .filter(m => m.severity === 1 && m.ruleId)
                .forEach(m => {
                    printWarning(executionMode, `${r.filePath}:${m.line}:${m.column}: ${m.message} (${m.ruleId})`);
                });
            r.messages
                .filter(m => m.severity === 2)
                .forEach(m => {
                    printError(executionMode, `${r.filePath}:${m.line}:${m.column}: ${m.message} (${m.ruleId})`);
                });
        });

        if (!fix) {
            printInfo(executionMode, `Run again with ${chalk.bold('--fix')} to try to fix the issues above.`);
        }

        quitWithError(executionMode, "Some files didn't pass the linter.");
    } else {
        featureFileResults.forEach(r => {
            r.messages
                .filter(m => m.severity === 1 && m.ruleId)
                .forEach(m => {
                    printWarning(executionMode, `${r.filePath}:${m.line}:${m.column}: ${m.message} (${m.ruleId})`);
                });
        });
        printSuccess(executionMode, 'All feature files are good!');
    }
};
