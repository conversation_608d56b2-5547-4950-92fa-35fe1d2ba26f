import { loadTestDatabase } from '@sage/xtrem-cli-dev';
import { ExecutionMode, printError, printInfo, printSuccess, quitWithError, safeRmSync } from '@sage/xtrem-cli-lib';
import {
    AnyValue,
    ApplicationManager,
    AsyncResponse,
    ConfigManager,
    Dict,
    Test,
    date,
    datetime,
} from '@sage/xtrem-core';
import { startApplication } from '@sage/xtrem-service';
import { Launcher } from '@wdio/cli';
import axios from 'axios';
import * as glob from 'glob';
import * as istanbulLibCoverage from 'istanbul-lib-coverage';
import * as istanbulLibReport from 'istanbul-lib-report';
import * as istanbulLibSourceMaps from 'istanbul-lib-source-maps';
import * as istanbulReports from 'istanbul-reports';
import { trim } from 'lodash';
import { spawnSync } from 'node:child_process';
import * as fs from 'node:fs';
import * as os from 'node:os';
import * as path from 'node:path';
import { isLocalTargetUrl, targetUrlOrigin } from '../../../utils/config';
import { ConfigWebdriverIO } from './wdio.conf';

const parse = require('cucumber-tag-expressions').default;

interface WdioConfig {
    config: ConfigWebdriverIO;
    cfgBaseUrl: string;
}

const calculateCoverage = async (sourceDirectories: string[], isCi: boolean, dir: string) => {
    if (!isCi) {
        return;
    }
    if (!sourceDirectories || sourceDirectories.length === 0) {
        return;
    }
    const rawCoverageInformation = (global as any).__coverage__ as istanbulLibCoverage.CoverageMapData | null;

    if (!rawCoverageInformation) {
        return;
    }

    let coverageMap = istanbulLibCoverage.createCoverageMap(rawCoverageInformation);

    /**
     *  Creating an empty entry for all artifacts so they are included to the report even if they were
     *  not loaded at all, the reduces false high coverage.
     * */
    glob.sync(`${dir}/build/**/*.cov`).forEach(c => {
        coverageMap.addFileCoverage(JSON.parse(fs.readFileSync(c, 'utf-8')));
    });

    glob.sync(`${dir}/coverage/*.json`).forEach(f => {
        const content: istanbulLibCoverage.CoverageMapData = JSON.parse(fs.readFileSync(f, 'utf-8'));
        coverageMap.merge(content);
    });

    const sourceMapStore = istanbulLibSourceMaps.createSourceMapStore({
        sourceStore: 'memory',
        baseDir: dir,
    });

    // Load source maps of instrumentation
    sourceDirectories.forEach(sourceDirectory => {
        glob.sync(`${sourceDirectory}/build/**/*.instrumented.map`).forEach(f => {
            const testDir = path.join(sourceDirectory, 'build', 'test');
            if (!f.includes(testDir)) {
                const content: any = JSON.parse(fs.readFileSync(f, 'utf-8'));
                sourceMapStore.registerMap(content.sources[0], content);
            }
        });
    });

    if (Object.keys(sourceMapStore.data).length > 0) {
        coverageMap = await sourceMapStore.transformCoverage(coverageMap);
    }

    const reportContext = istanbulLibReport.createContext({
        coverageMap,
        defaultSummarizer: 'nested',
        dir: path.resolve(dir, 'coverage'),
        sourceFinder: (file: string) => {
            const mappedFile = path.normalize(file.replace('build', '').replace('.js', '.ts'));
            return fs.readFileSync(mappedFile, 'utf-8');
        },
    });

    (istanbulReports.create('lcov') as any).execute(reportContext);
    (istanbulReports.create('text') as any).execute(reportContext);
    (istanbulReports.create('clover') as any).execute(reportContext);
    (istanbulReports.create('json') as any).execute(reportContext);
    (istanbulReports.create('cobertura') as any).execute(reportContext);
    (istanbulReports.create('text-summary') as any).execute(reportContext);
    (istanbulReports.create('text-lcov') as any).execute(reportContext);
};

const cleanUpReports = (dir: string) => {
    // Cleanup
    const allureReportDir = path.resolve(dir, 'allure-results');
    if (fs.existsSync(allureReportDir)) {
        safeRmSync(allureReportDir, { recursive: true });
    }

    const testReportDir = path.resolve(dir, 'test-report');
    if (fs.existsSync(testReportDir)) {
        safeRmSync(testReportDir, { recursive: true });
    }

    const coverageDir = path.resolve(dir, 'coverage');
    glob.sync(`${coverageDir}/*.json`).forEach(f => fs.unlinkSync(f));
};

/**
 * The JSON format of our cached result file when some tests did not pass.
 * If all tests passed, the file contains only two characters: ok
 */
interface CachedResult {
    // passed is a map from feature name -> timestamp of success (as a string)
    // We could use a boolean but timestamp can help troubleshoot
    passed: Dict<string>;
}

/**
 * The content of a cucumber JSON report file.
 * This is only a very partial definition, with only the fields that we need for our cache.
 */
type CucumberReport = {
    id: string;
    elements: {
        steps: { result: { status: 'passed' | 'failed' | 'skipped' } }[];
    }[];
}[];

/** Loads the cached result file given by the XTREM_CACHE_FILE environment variable */
const loadCacheFile = (executionMode: ExecutionMode): CachedResult => {
    if (!process.env.XTREM_CACHE_FILE || !fs.existsSync(process.env.XTREM_CACHE_FILE)) return { passed: {} };
    try {
        return JSON.parse(fs.readFileSync(process.env.XTREM_CACHE_FILE, 'utf-8'));
    } catch (err) {
        printError(executionMode, `error reading ${process.env.XTREM_CACHE_FILE}: ${err.message}`);
        return { passed: {} };
    }
};

/**
 * Returns the list of features that have been recorded as passing the test in the cache file.
 * Returns an empty array if there is no cache file
 */
const getCachedSuccessfulFeatures = (executionMode: ExecutionMode): string[] => {
    const cachedResult = loadCacheFile(executionMode);
    return Object.keys(cachedResult.passed);
};

/**
 * Updates a cached result from one of the report files produced during the test run
 */
const updatePassedFeatures = (cachedResult: CachedResult, report: CucumberReport, stamp: string): void => {
    report.forEach(feature => {
        if (feature.elements.every(scenario => scenario.steps.every(step => step.result.status === 'passed'))) {
            cachedResult.passed[feature.id] = stamp;
        }
    });
};

// /**
//  * Updates PROGRESS
//  */

const createProgressReport = (dir: string, featureFiles: string[]): void => {
    const jsonDir = path.join(dir, 'test-report/cucumber');
    const resultFilenames: { status: string; file: string }[] = [];

    featureFiles.forEach(element => {
        let fName = element.toString();
        fName = fName.substring(fName.lastIndexOf(path.sep) + 1, fName.length).replace('.feature', '');
        resultFilenames.push({ status: 'skipped', file: fName });
    });

    fs.readdirSync(jsonDir)
        .filter(f => f.endsWith('.json'))
        .forEach(f => {
            const report = JSON.parse(fs.readFileSync(path.join(jsonDir, f), 'utf-8'));
            updateProgress(resultFilenames, report);
        });

    const cucumberProgressResults = path.resolve(`${dir}/test-report/cucumber-progress-results`);
    if (!fs.existsSync(cucumberProgressResults)) {
        fs.mkdirSync(cucumberProgressResults);
    }
    fs.writeFileSync(
        path.join(cucumberProgressResults, '/cucumber-progress-results.json'),
        JSON.stringify(resultFilenames, null, 4),
    );
};

const updateProgress = (resultFilenames: { status: string; file: string }[], report: CucumberReport): void => {
    let stepCount = 0;
    let passCount = 0;

    resultFilenames.forEach(element => {
        const currentReport = report.filter(feature => feature.id === element.file);
        if (currentReport.length > 0) {
            currentReport.forEach(feature => {
                feature.elements.forEach(scenario => {
                    stepCount += scenario.steps.length;
                    scenario.steps.forEach(step => {
                        if (step.result.status === 'passed') {
                            passCount += 1;
                        }
                    });
                });
            });

            if (stepCount === passCount) {
                element.status = 'passed';
            } else {
                element.status = 'failed';
            }
        }
    });
};

/**
 * Updates a cached result by analyzing all report files produced during the test run
 */
const updateCachedResult = (executionMode: ExecutionMode, dir: string): CachedResult => {
    const cachedResult = loadCacheFile(executionMode);
    const now = datetime.now();
    const jsonDir = path.join(dir, 'test-report/cucumber');
    fs.readdirSync(jsonDir)
        .filter(f => f.endsWith('.json'))
        .forEach(f => {
            const report = JSON.parse(fs.readFileSync(path.join(jsonDir, f), 'utf-8'));
            updatePassedFeatures(cachedResult, report, now.toJSON());
        });
    return cachedResult;
};

/** Creates or updates the cache file with the results of the test run */
const saveCachedResult = (executionMode: ExecutionMode, dir: string, ok: boolean): void => {
    if (!process.env.XTREM_CACHE_FILE) return;
    const contents = ok ? 'ok' : JSON.stringify(updateCachedResult(executionMode, dir), null, 4);
    fs.writeFileSync(process.env.XTREM_CACHE_FILE, contents);
};

// Verifies that the feature name found inside a feature file matches the filename
// This check avoids duplicate feature names inside feature files and allows us to test filenames
// against the names of successful features that we record in the cache file.
const checkFeatureNames = (executionMode: ExecutionMode, featurePaths: string[]): void => {
    featurePaths.forEach(f => {
        const matched = fs.readFileSync(f, 'utf8').match(/Feature:(.*)/);
        if (!matched || !matched[1]) quitWithError(executionMode, `${f}: Feature name not found`);
        const kebabName = matched[1].toLowerCase().trim().replace(/ /g, '-');
        const basename = path.basename(f, '.feature');
        if (kebabName !== basename)
            quitWithError(executionMode, `${f}: mismatch with feature name: ${kebabName} !== ${basename}`);
    });
};

const lookUpFeatureFiles = (executionMode: ExecutionMode, dir: string, pattern?: string) => {
    let filteredFeatures;
    const trimmedPattern = trim(pattern, '"');
    const featureFiles = glob.sync(trimmedPattern ? `${dir}/**/**${trimmedPattern}**` : `${dir}/**/*.feature`);
    const alreadyOk = getCachedSuccessfulFeatures(executionMode);
    filteredFeatures = featureFiles
        .filter(f => f.endsWith('.feature') && f.indexOf('node_modules') === -1)
        .filter(f => !alreadyOk.some(okFeature => path.basename(f, '.feature') === okFeature))
        // since globs V10 the file order is not guaranteed
        .sort();
    const cucumberTags = ('CUCUMBER_TAGS' in process.env && process.env.CUCUMBER_TAGS) || '';
    if (trimmedPattern) {
        printInfo(executionMode, `Pattern: ${trimmedPattern}`);
    }

    checkFeatureNames(executionMode, filteredFeatures);

    if (cucumberTags) {
        printInfo(executionMode, `Searching for following tags in feature files: ${cucumberTags}`);
        const featureFilesWithTags = (features: any[], tags: any) => {
            const expressionNode = parse(tags);
            return features.filter(featureFile => {
                const content = fs.readFileSync(featureFile, 'utf8');
                if (content.length > 0) {
                    const tagsInFile = content.match(/(@\w+)/g) || [];
                    if (expressionNode.evaluate(tagsInFile)) {
                        return true;
                    }
                }
                return false;
            });
        };
        filteredFeatures = featureFilesWithTags(filteredFeatures, cucumberTags);
    }
    const featuresExecuted = `The following feature files will be executed:\n${filteredFeatures
        .map(f => ` - ${f.replace(dir + path.sep, '')}`)
        .join('\n')}`;
    printInfo(executionMode, featuresExecuted);
    return filteredFeatures;
};

async function checkServiceReady(executionMode: ExecutionMode, dir: string, configFile: WdioConfig) {
    // eslint-disable-next-line
    const packageDetails = require(`${dir}/package.json`);

    const startTime = new Date().getTime();
    printInfo(executionMode, 'Pinging backend...');

    await axios.post(`${configFile.cfgBaseUrl}/metadata`, {
        query: `{
        stickers { key, title }
        pages { key, title }
        strings(filter: { packageOrPage: "${packageDetails.name}, @sage/xtrem-ui" }) { key }
    }`,
    });

    await axios.post(`${configFile.cfgBaseUrl}/api`, {
        query: '{__schema{types { name, description }}}',
    });
    const endTime = new Date().getTime();
    printSuccess(executionMode, `The Xtrem service is ready! (${endTime - startTime} ms)`);
}

/**
 * Retrieves the URL of the current app.
 * @returns The URL of the current app, or undefined the app key was not found in the config file.
 */
function getAppUrl(): string | undefined {
    const app = ConfigManager.current.app;
    return app && ConfigManager.current.apps?.[app]?.appUrl;
}

export async function executeIntegrationTest(
    executionMode: ExecutionMode,
    dir: string,
    isCi: boolean,
    browser = false,
    serviceOptions?: string,
    pattern?: string,
): Promise<void> {
    let newDir;
    newDir = Test.getDirectory(dir, 'package.json');
    if (newDir === '') {
        newDir = Test.getDirectory(path.join(dir, '../'), 'package.json');
    }
    if (newDir === '') {
        newDir = Test.getDirectory(path.join(dir, '../../'), 'package.json');
    }

    cleanUpReports(newDir);

    // Load config file so we can override some properties
    // eslint-disable-next-line global-require
    const configFile: WdioConfig = { ...require('./wdio.conf') };
    if (isCi || !browser) {
        configFile.config.capabilities[0]['goog:chromeOptions']!.args!.push('--headless');
    }

    const featureFiles = lookUpFeatureFiles(executionMode, dir, pattern);
    if (featureFiles.length === 0) {
        printInfo(
            executionMode,
            pattern ? `No feature files found with pattern: ${pattern}` : 'No feature files found.',
        );
        process.exit(0);
    }

    configFile.config.specs = featureFiles;

    printInfo(executionMode, `Tests are executed against the following URL: ${configFile.cfgBaseUrl}`);

    let sourceDirectories: string[] = [];
    if (isCi) {
        if (targetUrlOrigin() && !isLocalTargetUrl()) {
            if (
                !process.env.SCOPE ||
                (process.env.SCOPE !== 'services' && process.env.SCOPE !== 'tools' && process.env.SCOPE !== 'shopfloor')
            ) {
                await checkServiceReady(executionMode, dir, configFile);
                sourceDirectories = [process.cwd()];
            }
        } else {
            // Deploying the application
            // @todo bundle layer is removed -> foreign key issue
            const application = await ApplicationManager.getApplication(dir, {
                applicationType: 'admin-tool',
                startOptions: { channels: ['graphql', 'listeners'], services: [] },
            });
            await loadTestDatabase(executionMode, application, 'test', {
                layersAsString: 'setup,test',
                serviceOptionsAsString: serviceOptions,
            });

            printInfo(executionMode, 'Start the service...');
            try {
                await startApplication(application);
            } catch (error) {
                printError(executionMode, error.stack);
            }

            if (!isLocalTargetUrl()) {
                const appUrl = getAppUrl();
                if (appUrl) {
                    // Override configFile.cfgBaseUrl so that checkServiceReady connects to the right port
                    configFile.cfgBaseUrl = appUrl;
                    // Set TARGET_URL so that wdio picks the correct port too.
                    process.env.TARGET_URL = appUrl;
                }
                await checkServiceReady(executionMode, dir, configFile);
            }
            sourceDirectories = application.getPackages().map(d => d.dir);
        }
    }

    const configFilePath = require.resolve(`${__dirname}/wdio.conf`);
    const wdio = new Launcher(configFilePath, configFile as any);
    function createCustomTimeout(seconds: number) {
        return new Promise<void>(resolve => {
            setTimeout(() => {
                resolve();
            }, seconds * 1000);
        });
    }

    await createCustomTimeout(5);

    try {
        const result = await withWorkDaySystemDate(executionMode, () => wdio.run());
        printInfo(executionMode, String(result));
        createProgressReport(newDir, featureFiles);
        await calculateCoverage(sourceDirectories, isCi, dir);
        saveCachedResult(executionMode, dir, result === 0);
        if (result !== 0) {
            quitWithError(executionMode, 'The test failed, please see above for more details.');
        }
    } catch (error) {
        createProgressReport(newDir, featureFiles);
        await calculateCoverage(sourceDirectories, isCi, dir);
        saveCachedResult(executionMode, dir, false);
        // eslint-disable-next-line no-console
        console.log(error);
        quitWithError(executionMode, error);
    }
}

const withWorkDaySystemDate = async <T extends AnyValue>(
    executionMode: ExecutionMode,
    body: () => AsyncResponse<T>,
): Promise<T> => {
    const today = date.today();

    // if this is a workday, execute directly
    if (today.weekDay > 0 && today.weekDay < 6) return body();

    const changeDateCmd = (ndays: number) => {
        const pad2 = (n: number) => `${'0'.repeat(2 - String(n).length)}${n}`;

        switch (os.platform()) {
            case 'linux':
                return `sudo date --s='${ndays} days'`;
            case 'darwin': {
                const dt = datetime.now().addDays(ndays);
                const components = [dt.month, dt.day, dt.hour, dt.minute, dt.year % 100];
                const cmd = `sudo date ${components.map(pad2).join('')}`;
                return cmd;
            }
            default:
                throw new Error(`unsupported platform: ${os.platform()}`);
        }
    };

    const changeDate = (ndays: number) => spawnSync('sh', ['-c', changeDateCmd(ndays)], { encoding: 'utf-8' });

    const daysDiff = 2;
    let result = changeDate(-daysDiff);
    if (result.status !== 0) {
        printError(
            executionMode,
            `Could not change the system date. Some tests may fail because we are not on a workday. Error: ${result.error?.message}`,
        );
        return body();
    }

    // Use error rather than warning to make these messages very visible
    printError(executionMode, '!!! SYSTEM DATE HAS CHANGED to execute tests on a workday !!!');
    printError(executionMode, `!!! NEW DATE: ${datetime.now()}                    !!!`);
    printError(executionMode, '!!! CHECK YOUR SYSTEM DATE IF YOU INTERRUPT THE PROCESS   !!!');
    try {
        return await body();
    } finally {
        result = changeDate(+daysDiff);
        if (result.status !== 0)
            printError(executionMode, `Could not restore ththe date! Error: ${result.error?.message}`);
        else printError(executionMode, `!!! SYSTEM DATE RESTORED to ${datetime.now()} !!!`);
    }
};
