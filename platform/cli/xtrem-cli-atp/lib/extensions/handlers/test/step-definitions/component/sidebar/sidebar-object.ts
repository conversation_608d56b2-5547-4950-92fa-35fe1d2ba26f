import * as utils from '../../step-definitions-utils';
import AbstractPageObject from '../abstract-page-object';
import { DropDownMenuObject } from '../dropdown-menu-object';
import { waitForPromises } from '../field/wait-util';

export class Sidebar extends AbstractPageObject {
    public readonly dropDownMenu: DropDownMenuObject;

    constructor() {
        super('[data-state="open"] div[data-component="sidebar"]');
        this.dropDownMenu = new DropDownMenuObject(this.cssSelector);
    }

    // eslint-disable-next-line class-methods-use-this
    async clickActionButton(expectedAction: string) {
        const container = utils.getDataTestIdSelector({
            domSelector: 'div',
            dataTestIdValue: 'e-sidebar-quick-action-container',
        });

        const actionButton = await browser.$(`${container} button[aria-label="${expectedAction}"]`);

        try {
            await actionButton.click();
            await waitForPromises(500, 'Action button click');
        } catch (error) {
            throw new Error(
                `Expected element could not be found: ${expectedAction} \nSelector: ${actionButton.selector}`,
            );
        }
    }
}
