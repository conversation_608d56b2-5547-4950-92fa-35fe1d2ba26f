import * as StaticStore from '../../../static-store';

export const SCROLL_STEP = 100;

export const scrollToLeft = async (selector: string, amount: number) => {
    await browser.execute(
        `var elements = document.querySelectorAll('${selector}');
         for (let i = 0; i < elements.length; i++) {
             if (elements[i]) elements[i].scrollLeft = ${amount};
         }`,
    );
};

export const getRowAttributes = () => {
    const storedRowAttributes = StaticStore.getStoredObject<{
        rowId: string;
        rowIndex: string;
        ariaRowIndex: string;
    }>(StaticStore.StoredKeys.NESTED_GRID_ROW_ATTRIBUTES);
    return `.ag-row[row-id="${storedRowAttributes.rowId}"]`;
};

export const getMobileRowAttributes = () => {
    const storedMobileRowAttributes = StaticStore.getStoredObject<string>(
        StaticStore.StoredKeys.MOBILE_NESTED_GRID_ROW_SELECTOR,
    );
    return storedMobileRowAttributes;
};
