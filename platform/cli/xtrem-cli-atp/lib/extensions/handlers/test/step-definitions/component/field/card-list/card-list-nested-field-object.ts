/* eslint-disable @typescript-eslint/naming-convention */
import * as utils from '../../../step-definitions-utils';
import AbstractPageObject from '../../abstract-page-object';
import { expectImageStatus } from '../table-card-view/card-utils';

export interface ICardListNestedFieldObject {
    identifier: string;
    lookupStrategy: utils.LookupStrategy;
    fieldType: utils.NestedFieldTypes;
    rowNumber: number;
    context?: utils.ElementContext;
}

export class CardListNestedFieldObject extends AbstractPageObject {
    constructor({ identifier, lookupStrategy, fieldType, rowNumber, context }: ICardListNestedFieldObject) {
        super(
            `${context ? `${utils.getContextSelector(context)} ` : ''} [data-testid="e-card"]:nth-child(${rowNumber})
          ${utils.getLookupStrategySelector({ fieldType, lookupStrategy, identifier })}`,
        );
    }

    async expectRowImageStatus(identifier: string, status: 'defined' | 'undefined') {
        await expectImageStatus({ selector: this.cssSelector, identifier, status });
    }
}
