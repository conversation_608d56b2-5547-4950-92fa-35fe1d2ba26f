import * as utils from '../../../step-definitions-utils';
import { FieldObject } from '../field-object';
import { waitForPromises } from '../wait-util';

export class NodeBrowserTreeObject extends FieldObject {
    constructor({
        identifier,
        lookupStrategy,
        context,
    }: {
        identifier: string;
        lookupStrategy: utils.LookupStrategy;
        context?: utils.ElementContext;
    }) {
        super({ fieldType: 'node-browser-tree', identifier, lookupStrategy, context });
    }

    async searchTreeElement(value: string) {
        const searchInput = `${utils.getDataTestIdSelector({ domSelector: 'input', dataTestIdValue: 'e-node-browser-filter' })}`;
        const referenceFilterElement = await this.find(
            utils.getDataTestIdSelector({ domSelector: 'input', dataTestIdValue: 'e-node-browser-filter' }),
        );
        try {
            await referenceFilterElement.isDisplayed();
            await this.write({ content: value, cssSelector: searchInput, ignoreContext: true });
        } catch (error) {
            throw new Error(
                `Expected element could not be found: "${value}" node-browser-tree field. \nSelector: ${this.cssSelector}`,
            );
        }
    }

    async clearTreeElement() {
        const clearInput = `button[aria-label="Clear filter text"]`;
        await (await $(`${this.cssSelector} ${clearInput}`)).click();
    }

    async getNodeElement(nodeLevel: string, nodeIdentifier: string) {
        let selectorToUse = `.e-tree-view-element-level-${nodeLevel} > .e-tree-view-element-container > .e-tree-view-icon-container > label`;
        let selectorToUse1 = `.e-tree-view-element-level-${nodeLevel} > .e-tree-view-element-container > .e-tree-view-element-node-container > label`;

        if (await (await $(`${selectorToUse} span[class="e-tree-view-underlined"]`)).isExisting()) {
            selectorToUse = `${selectorToUse} span[class="e-tree-view-underlined"]`;
        }

        if (await (await $(`${selectorToUse1} > span[class="e-tree-view-underlined"]`)).isExisting()) {
            selectorToUse1 = `${selectorToUse1} span[class="e-tree-view-underlined"]`;
        }

        let sectionElement: WebdriverIO.Element;
        let values = [];

        const nodeElements = await this.findAll(selectorToUse, true);

        const nodeCollapseElements = await this.findAll(selectorToUse1, true);
        values = await Promise.all(
            nodeElements.map(async el => {
                const val = await el.getText();
                return val;
            }),
        );
        let index = values.findIndex(val => val === nodeIdentifier);
        if (index >= 0) {
            sectionElement = nodeElements[index];
            if (index < 0) {
                throw new Error(
                    `Expected element could not be found: "${nodeIdentifier}" node-browser-tree element of level "${nodeLevel}".\nSelector: ${this.cssSelector}`,
                );
            }
        } else {
            values = await Promise.all(
                nodeCollapseElements.map(async el => {
                    const val = await el.getText();
                    return val;
                }),
            );

            index = values.findIndex(val => val === nodeIdentifier);
            sectionElement = nodeCollapseElements[index];
            if (index < 0) {
                throw new Error(
                    `Expected element could not be found:"${nodeIdentifier}" node-browser-tree element of level "${nodeLevel}".\nSelector: ${this.cssSelector}`,
                );
            }
        }
        await waitForPromises(500, 'Filtering table');
        return sectionElement;
    }

    expandCollapse = async (toggleState: utils.ExpandedOrCollapsed, sectionElement: WebdriverIO.Element) => {
        const nodeElement = await sectionElement.parentElement();
        const selectorToUse = nodeElement.$(`[data-testid~="e-tree-view-switcher-icon-container"] span`);
        const actualState = await selectorToUse.getAttribute('type');
        const toggle = toggleState === utils.ExpandedOrCollapsed.EXPANDED ? 'minus' : 'plus';

        if (toggleState !== utils.ExpandedOrCollapsed.EXPANDED && toggle !== actualState) {
            await (await nodeElement.$(`button[data-testid~="e-tree-view-switcher-icon-container"]`)).click();
        } else if (toggleState !== utils.ExpandedOrCollapsed.COLLAPSED && toggle !== actualState) {
            await (await nodeElement.$(`button[data-testid~="e-tree-view-switcher-icon-container"]`)).click();
        }

        await waitForPromises(500, this.cssSelector);
    };

    selectNodeElementWithText = async (toggleState: string, sectionElement: WebdriverIO.Element) => {
        const nodeElement = await sectionElement.parentElement();
        const el = await nodeElement.parentElement();
        const selectorToUse = el.$(`input[data-testid~="e-tree-view-checkbox"]`);
        const toggle = toggleState === 'ticks';

        if (sectionElement.selector.toString().includes(`e-tree-view-underlined`)) {
            if (sectionElement.selector.toString().includes(`e-tree-view-element-node-container`)) {
                const selectNodeElement = await $(
                    `.e-tree-view-element-container > .e-tree-view-element-node-container > .e-tree-view-switcher-container button[data-testid~="e-tree-view-switcher-icon-container"]`,
                );
                await selectNodeElement.click();
            } else {
                const selectNodeElement = await $(
                    `.e-tree-view-element > .e-tree-view-element-container > .e-tree-view-checkbox-container input`,
                );
                await selectNodeElement.click();
            }
        } else {
            const selectionState = await selectorToUse.isSelected();
            if (toggle !== selectionState) {
                await selectorToUse.click();
            }

            await waitForPromises(500, `${this.cssSelector}`);
        }
    };

    expectExpandedCollapsed = async (toggleState: string, sectionElement: WebdriverIO.Element) => {
        const nodeElement = await sectionElement.parentElement();
        const selectorToUse = nodeElement.$(`[data-testid~="e-tree-view-switcher-icon-container"] span`);

        const actualState = await selectorToUse.getAttribute('type');
        const nodeState =
            toggleState === 'expanded' ? utils.ExpandedOrCollapsed.EXPANDED : utils.ExpandedOrCollapsed.COLLAPSED;
        const toggle = nodeState === utils.ExpandedOrCollapsed.EXPANDED ? 'minus' : 'plus';

        if (toggle !== actualState) {
            throw new Error(`Expected element to be ${toggleState}.\nSelector: ${this.cssSelector}`);
        }
    };

    expectTickUntick = async (toggleState: string, sectionElement: WebdriverIO.Element) => {
        const nodeElement = await sectionElement.parentElement();
        const el = await nodeElement.parentElement();
        const selectorToUse = el.$(`input[data-testid~="e-tree-view-checkbox"]`);
        const toggle = toggleState === 'ticked';

        const selectionState = await selectorToUse.isSelected();

        if (toggle !== selectionState) {
            throw new Error(`Expected element to be ${toggleState}.\nSelector: ${this.cssSelector}`);
        }
        await waitForPromises(500, 'Check status');
    };

    override async expectToBeReadOnly() {
        const element = await this.get();
        try {
            await browser.waitUntil(
                async () => {
                    const classes = await element.getAttribute('class');
                    const readOnly = classes.includes('e-read-only');
                    return readOnly;
                },
                { timeout: this.valueCheckTimeout },
            );
        } catch (error) {
            throw new Error(`Expected Element to be read-only.\nSelector: ${this.cssSelector}`);
        }
    }
}
