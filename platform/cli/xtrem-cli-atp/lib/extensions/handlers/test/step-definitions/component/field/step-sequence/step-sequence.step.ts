import { Then } from '@cucumber/cucumber';
import { ElementContext, LookupStrategy } from '../../../step-definitions-utils';
import * as StaticStore from '../../static-store';
import { StepSequenceFieldObject } from './step-sequence.object';

Then(
    /^the user selects the "(.*)" (bound|labelled) step-sequence field on (the main page|a modal|a full width modal|the detail panel|the sidebar)$/,
    async (identifier: string, lookupStrategy: LookupStrategy, elementContext: ElementContext) => {
        const stepSequence = new StepSequenceFieldObject({ context: elementContext, lookupStrategy, identifier });
        await stepSequence.dismissAllNotification();
        await stepSequence.select(identifier);
    },
);

Then(/^the title of the step-sequence field is "(.*)"$/, async (title: string) => {
    const stepSequence = <StepSequenceFieldObject>StaticStore.getStoredObject(StaticStore.StoredKeys.STEPSEQUENCE);
    await stepSequence.expectTitle(title);
});

Then(/^the helper text of the step-sequence field is "(.*)"$/, async (helperText: string) => {
    const stepSequence = <StepSequenceFieldObject>StaticStore.getStoredObject(StaticStore.StoredKeys.STEPSEQUENCE);
    await stepSequence.expectHelperText(helperText);
});

Then(/^the title of the step-sequence field is (displayed|hidden)$/, async (cssState: 'displayed' | 'hidden') => {
    const stepSequence = <StepSequenceFieldObject>StaticStore.getStoredObject(StaticStore.StoredKeys.STEPSEQUENCE);
    await stepSequence.expectTitleState(cssState === 'hidden');
});

Then(/^the helper text of the step-sequence field is (displayed|hidden)$/, async (cssState: 'displayed' | 'hidden') => {
    const stepSequence = <StepSequenceFieldObject>StaticStore.getStoredObject(StaticStore.StoredKeys.STEPSEQUENCE);
    await stepSequence.expectHelperTextState(cssState === 'hidden');
});

Then(
    /^the status of the "(.*)" item of the step-sequence is (complete|current|incomplete)$/,
    async (stepItem: string, status: 'complete' | 'current' | 'incomplete') => {
        const stepSequence = <StepSequenceFieldObject>StaticStore.getStoredObject(StaticStore.StoredKeys.STEPSEQUENCE);
        await stepSequence.statusOfStepItem(stepItem, status);
    },
);

Then(
    /^the orientation of the step-sequence field is (horizontal|vertical)$/,
    async (orientation: 'horizontal' | 'vertical') => {
        const stepSequence = <StepSequenceFieldObject>StaticStore.getStoredObject(StaticStore.StoredKeys.STEPSEQUENCE);
        await stepSequence.orientationOfStepItem(orientation);
    },
);

Then(
    /^the "(.*)" (bound|labelled) step-sequence field on (the main page|a modal|a full width modal|the detail panel|the sidebar) is (displayed|hidden)$/,
    async (
        identifier: string,
        lookupStrategy: LookupStrategy,
        elementContext: ElementContext,
        cssState: 'displayed' | 'hidden',
    ) => {
        const stepSequence = new StepSequenceFieldObject({ context: elementContext, lookupStrategy, identifier });
        await stepSequence.expectToBeDisplayed(stepSequence.cssSelector, cssState === 'hidden');
    },
);
