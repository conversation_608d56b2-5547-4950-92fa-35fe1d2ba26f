import { Then, When } from '@cucumber/cucumber';
import { LookupStrategy } from '../../step-definitions-utils';
import * as StaticStore from '../static-store';
import { DashboardEditor } from './dashboard-editor-object';
import { Dashboard } from './dashboard-object';
import { Alignment, DashboardWidget, WidgetType } from './dashboard-widget-object';

/**
 * Dashboard
 */

Then(/^the user clicks the create button on the dashboard$/, async () => {
    const dashboard = new Dashboard();
    await dashboard.clickCreateDashboard();
});

Then('the dashboard creation dialog is displayed', async () => {
    const dashboard = new Dashboard();
    await dashboard.expectCreationDialogToAppear();
});

Then(/^the dashboard creation dialog description is "(.*)"$/, async (dialogDescription: string) => {
    const dashboard = new Dashboard();
    await dashboard.expectDialogDescription(dialogDescription);
});

Then(
    /^the "(.*)" button in the dashboard creation dialog is (enabled|disabled)$/,
    async (buttonName: string, cssState: 'enabled' | 'disabled') => {
        const dashboard = new Dashboard();
        await dashboard.expectCreationDialogButtonEnabled(buttonName, cssState === 'disabled');
    },
);

Then(/^the user clicks the "(.*)" button in the dashboard creation dialog$/, async (buttonName: string) => {
    const dashboard = new Dashboard();
    await dashboard.clickCreationDialogButton(buttonName);
});

Then(/^the "(.*)" template in the dashboard creation dialog is displayed$/, async (dashboardTemplate: string) => {
    const dashboard = new Dashboard();
    await dashboard.expectBlankDashboardTemplate(dashboardTemplate);
});

Then(/^the user selects the template ([0-9]*) in the dashboard creation dialog$/, async (templateIndex: number) => {
    const dashboard = new Dashboard();
    await dashboard.selectTemplate(templateIndex);
});

Then(
    /^the user selects the template with title "(.*)" in the dashboard creation dialog$/,
    async (templateTitle: string) => {
        const dashboard = new Dashboard();
        await dashboard.selectTemplateByTitle(templateTitle);
    },
);

Then(/^the template ([0-9]*) in the dashboard creation dialog is selected$/, async (templateIndex: number) => {
    const dashboard = new Dashboard();
    await dashboard.expectTemplateSelected(templateIndex);
});

Then(/^the template with title "(.*)" in the dashboard creation dialog is selected$/, async (templateTitle: string) => {
    const dashboard = new Dashboard();
    await dashboard.expectTemplateSelectedByTitle(templateTitle);
});

Then('the dashboard page is displayed', async () => {
    const dashboard = new Dashboard();
    await dashboard.expectDashboardToBeDisplayed();
});

Then(/^the "(.*)" titled empty dashboard is displayed$/, async (textContent: string) => {
    const dashboard = new Dashboard();
    await dashboard.expectDashboardTitle(textContent, true);
});

Then(/^the "(.*)" titled dashboard is displayed$/, async (textContent: string) => {
    const dashboard = new Dashboard();
    await dashboard.expectDashboardTitle(textContent, false);
});

Then(/^the "(.*)" subtitled empty dashboard is displayed$/, async (textContent: string) => {
    const dashboard = new Dashboard();
    await dashboard.expectDashboardSubtitle(textContent, true);
});

Then(/^the "(.*)" subtitled dashboard is displayed$/, async (textContent: string) => {
    const dashboard = new Dashboard();
    await dashboard.expectDashboardSubtitle(textContent, false);
});

Then(/^the "(.*)" subtitled blank dashboard is displayed$/, async (textContent: string) => {
    const dashboard = new Dashboard();
    await dashboard.expectBlankDashboardSubtitle(textContent);
});

Then(
    /^the "(.*)" labelled tab in the dashboard is (displayed|hidden)$/,
    async (tabName: string, cssState: 'displayed' | 'hidden') => {
        const dashboard = new Dashboard();
        await dashboard.expectTabToBeDisplayed(tabName, cssState === 'hidden');
    },
);

Then(/^the user selects the "(.*)" labelled tab in the dashboard/, async (tabName: string) => {
    const dashboard = new Dashboard();
    await dashboard.clickTab(tabName);
});

Then(/^the user clicks the "(.*)" labelled CRUD button in the dashboard action menu$/, async (buttonName: string) => {
    const dashboard = new Dashboard();
    await dashboard.openActionMenu();
    await dashboard.clickCrudButton(buttonName);
});

Then(
    /^the "(.*)" titled widget in the dashboard is (displayed|hidden)$/,
    async (widget: string, cssState: 'displayed' | 'hidden') => {
        const dashboard = new Dashboard();
        await dashboard.expectAddedDashboardWidget(widget, cssState === 'hidden');
    },
);

/**
 * Dashboard editor
 */

Then(/^the user clicks the edit dashboard title icon$/, async () => {
    const dashboardEditor = new DashboardEditor();
    await dashboardEditor.clickTitleIcon();
});

Then(/^the user writes "(.*)" in the dashboard title$/, async (content: string) => {
    const dashboardEditor = new DashboardEditor();
    await dashboardEditor.editDashboardTitle(content);
});

Then(/^the user selects the "(.*)" labelled tab in dashboard edit mode/, async (tabName: string) => {
    const dashboardEditor = new DashboardEditor();
    await dashboardEditor.clickTabInDashboardEditor(tabName);
});

Then(/^the "(.*)" titled dashboard in the dashboard editor is displayed$/, async (dashboardTitle: string) => {
    const dashboardEditor = new DashboardEditor();
    await dashboardEditor.expectDashboardEditorTitle(dashboardTitle);
});

Then(
    /^the "(.*)" button in the dashboard editor footer is (enabled|disabled)$/,
    async (buttonName: string, cssState: 'enabled' | 'disabled') => {
        const dashboardEditor = new DashboardEditor();
        await dashboardEditor.expectDashboardEditorFooterButtonEnabled(buttonName, cssState === 'disabled');
    },
);

Then(
    /^the "(.*)" button in the dashboard editor is (enabled|disabled)$/,
    async (buttonName: string, cssState: 'enabled' | 'disabled') => {
        const dashboardEditor = new DashboardEditor();
        await dashboardEditor.expectDashboardEditorButtonEnabled(buttonName, cssState === 'disabled');
    },
);

Then(/^the user clicks the "(.*)" button in the dashboard editor$/, async (buttonName: string) => {
    const dashboardEditor = new DashboardEditor();
    await dashboardEditor.clickDashboardEditorButton(buttonName);
});

Then(/^the user clicks the "(.*)" button in the dashboard editor footer$/, async (buttonName: string) => {
    const dashboardEditor = new DashboardEditor();
    await dashboardEditor.clickDashboardEditorFooterButton(buttonName);
});

Then(/^the "(.*)" titled header in the dashboard editor navigation panel is displayed$/, async (title: string) => {
    const dashboardEditor = new DashboardEditor();
    await dashboardEditor.expectNavigationPanelTitle(title);
});

Then(
    /^the "(.*)" titled category in the dashboard editor navigation panel is displayed$/,
    async (categoryName: string) => {
        const dashboardEditor = new DashboardEditor();
        await dashboardEditor.expectNavigationPanelCategory(categoryName);
    },
);

Then(/^the "(.*)" titled widget in the dashboard editor is displayed$/, async (widget: string) => {
    const dashboardEditor = new DashboardEditor();
    await dashboardEditor.expectDashboardWidget(widget);
});

Then(
    /^the user clicks the Add button of the "(.*)" titled widget card in the dashboard editor navigation panel$/,
    async (title: string) => {
        const dashboardEditor = new DashboardEditor();
        await dashboardEditor.clickAdd(title);
    },
);

Then(
    /^the user selects the "(.*)" titled (basic|bar-chart|gauge|line-chart|table|tile-indicator|visual-process|contact-card|pie-chart) widget field in the (dashboard|dashboard editor)$/,
    async (name: string, type: WidgetType, context: 'dashboard' | 'dashboard editor') => {
        const dashboardWidget = new DashboardWidget({ context, type, name });
        await dashboardWidget.storeWidget();
    },
);

Then(
    /^the user clicks the "(.*)" (labelled|bound) button in the dashboard editor navigation panel$/,
    async (identifier: string, lookupStrategy: LookupStrategy) => {
        const dashboardEditor = new DashboardEditor();
        await dashboardEditor.clickNavigationPanelButton(identifier, lookupStrategy);
    },
);

Then(/^the "(.*)" titled widget editor dialog is displayed$/, async (title: string) => {
    const dashboardEditor = new DashboardEditor();
    await dashboardEditor.expectWidgetEditorDialogTitle(title);
});

Then(/^the value of the step title of the widget editor dialog is "(.*)"$/, async (value: string) => {
    const dashboardEditor = new DashboardEditor();
    await dashboardEditor.expectWidgetEditorStepTitle(value);
});

Then(/^the value of the step subtitle of the widget editor dialog is "(.*)"$/, async (value: string) => {
    const dashboardEditor = new DashboardEditor();
    await dashboardEditor.expectWidgetEditorStepSubtitle(value);
});

Then(
    /^the user writes "([^"\n\r]*)" in the "([^"\n\r]*)" (dropdown|text) field in the widget editor dialog$/,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    async (value: string, fieldName: string, fieldType: 'dropdown' | 'text') => {
        const dashboardEditor = new DashboardEditor();
        await dashboardEditor.editWidgetTitle(value, fieldName);
    },
);

Then(
    /^the "(.*)" (dropdown|text) field in the widget editor dialog is (displayed|hidden)$/,

    async (name: string, fieldType: 'dropdown' | 'text', cssState: 'displayed' | 'hidden') => {
        const dashboardEditor = new DashboardEditor();
        await dashboardEditor.expectWidgetEditorFieldDisplayed(name, cssState === 'hidden');
    },
);

Then(
    /^the "(.*)" (dropdown|text) field in the widget editor dialog is (enabled|disabled)$/,

    async (name: string, fieldType: 'dropdown' | 'text', cssState: 'enabled' | 'disabled') => {
        const dashboardEditor = new DashboardEditor();
        await dashboardEditor.expectWidgetEditorFieldEnabled(name, cssState === 'disabled');
    },
);

Then(
    /^the value of the "([^"\n\r]*)" (text|dropdown) field in the widget editor dialog is "(.*)"$/,
    async (name: string, fieldType: 'dropdown' | 'text', value: string) => {
        const dashboardEditor = new DashboardEditor();
        await dashboardEditor.expectWidgetFieldContent(name, value);
    },
);

Then(/^the user selects the "(.*)" widget card in the widget editor dialog$/, async (title: string) => {
    const dashboardEditor = new DashboardEditor();
    await dashboardEditor.selectWidget(title);
});

Then(
    /^the "(.*)" widget card in the widget editor dialog is (selected|unselected)$/,
    async (name: string, cssState: 'selected' | 'unselected') => {
        const dashboardEditor = new DashboardEditor();
        await dashboardEditor.expectWidgetCardSelected(name, cssState === 'unselected');
    },
);

Then(
    /^the "(.*)" widget card in the widget editor dialog is (displayed|hidden)$/,
    async (name: string, cssState: 'displayed' | 'hidden') => {
        const dashboardEditor = new DashboardEditor();
        await dashboardEditor.expectWidgetCardDisplayed(name, cssState === 'hidden');
    },
);

Then(
    /^the "(.*)" button in the widget editor dialog is (displayed|hidden)$/,
    async (buttonName: string, cssState: 'displayed' | 'hidden') => {
        const dashboardEditor = new DashboardEditor();
        await dashboardEditor.expectButtonDisplayed(buttonName, cssState === 'hidden');
    },
);

Then(
    /^the "(.*)" button in the widget editor dialog is (enabled|disabled)$/,
    async (buttonName: string, cssState: 'enabled' | 'disabled') => {
        const dashboardEditor = new DashboardEditor();
        await dashboardEditor.expectButtonEnabled(buttonName, cssState === 'disabled');
    },
);

Then(/^the user clicks the "(.*)" button in the widget editor dialog$/, async (buttonName: string) => {
    const dashboardEditor = new DashboardEditor();
    await dashboardEditor.clickWidgetEditorButton(buttonName);
});

Then(/^the user searches for "(.*)" in the widget editor dialog$/, async (value: string) => {
    const dashboardEditor = new DashboardEditor();
    await dashboardEditor.searchTreeElement(value);
});

Then('the user clears the search field in the widget editor dialog', async () => {
    const dashboardEditor = new DashboardEditor();
    await dashboardEditor.clearSearch();
});

Then(
    /^the user (selects|unselects) the "(.*)" tree-view element in the widget editor dialog$/,
    async (state: string, name: string) => {
        const dashboardEditor = new DashboardEditor();
        await dashboardEditor.selectUnselectWidgetData(name, state);
    },
);

Then(
    /^the "(.*)" tree-view element in the widget editor dialog is (displayed|hidden)$/,
    async (label: string, cssState: 'displayed' | 'hidden') => {
        const dashboardEditor = new DashboardEditor();
        await dashboardEditor.expectWidgetDataFound(label, cssState === 'hidden');
    },
);

Then(
    /^the "(.*)" tree-view element in the widget editor dialog is (checked|unchecked)$/,
    async (label: string, cssState: 'checked' | 'unchecked') => {
        const dashboardEditor = new DashboardEditor();
        await dashboardEditor.expectWidgetDataSelected(label, cssState === 'unchecked');
    },
);

Then(
    /^the "(.*)" table button in the widget editor dialog is (displayed|hidden)$/,
    async (buttonName: string, cssState: 'displayed' | 'hidden') => {
        const dashboardEditor = new DashboardEditor();
        await dashboardEditor.expectWidgetEditorTableButton(buttonName, cssState === 'hidden');
    },
);

Then(/^the user clicks the "(.*)" table button in the widget editor dialog$/, async (buttonName: string) => {
    const dashboardEditor = new DashboardEditor();
    await dashboardEditor.clickWidgetEditorTableButton(buttonName);
});

Then(
    /^the user clicks the "([^"\n\r]*)" action button of row "([^"\n\r]*)" in the widget editor dialog$/,
    async (buttonName: string, rowId: number) => {
        const dashboardEditor = new DashboardEditor();
        await dashboardEditor.clickWidgetEditorActionButton(buttonName, rowId);
    },
);

Then(
    /^the "([^"\n\r]*)" action button of row "([^"\n\r]*)" in the widget editor dialog is (displayed|hidden)$/,
    async (buttonName: string, rowId: number, cssState: 'displayed' | 'hidden') => {
        const dashboardEditor = new DashboardEditor();
        await dashboardEditor.expectWidgetEditorActionButtonDisplayed({
            buttonName,
            rowId,
            reverse: cssState === 'hidden',
        });
    },
);

Then(
    /^the "([^"\n\r]*)" validation error of row "([^"\n\r]*)" in the widget editor dialog is (displayed|hidden)$/,
    async (errorText: string, rowId: number, cssState: 'displayed' | 'hidden') => {
        const dashboardEditor = new DashboardEditor();
        await dashboardEditor.expectValidationError({ value: errorText, rowId, reverse: cssState === 'hidden' });
    },
);

Then(
    /^the "([^"\n\r]*)" date field of row "([^"\n\r]*)" in the widget editor dialog is (displayed|hidden)$/,
    async (name: string, rowId: number, cssState: 'displayed' | 'hidden') => {
        const dashboardEditor = new DashboardEditor();
        await dashboardEditor.expectDateFieldToBeDisplayed({ name, rowId, reverse: cssState === 'hidden' });
    },
);
Then(
    /^the user writes "([^"\n\r]*)" in the "([^"\n\r]*)" date field of row "([^"\n\r]*)" in the widget editor dialog$/,
    async (date: string, name: string, rowId: number) => {
        const dashboardEditor = new DashboardEditor();
        await dashboardEditor.editDateField({ date, name, rowId });
    },
);

Then(
    /^the user writes "([^"\n\r]*)" in the "([^"\n\r]*)" (dropdown|text) field of row "([^"\n\r]*)" in the widget editor dialog$/,
    async (value: string, name: string, fieldType: 'dropdown' | 'text', rowId: number) => {
        const dashboardEditor = new DashboardEditor();
        await dashboardEditor.setFieldValue({ value, name, rowId });
    },
);

Then(
    /^the value of the "([^"\n\r]*)" (dropdown|text) field of row "([^"\n\r]*)" in the widget editor dialog is "(.*)"$/,
    async (fieldName: string, fieldType: 'dropdown' | 'text', rowNumber: number, value: string) => {
        const dashboardEditor = new DashboardEditor();
        await dashboardEditor.expectFieldValueInRow({ name: fieldName, rowNumber, value });
    },
);

Then(
    /^the "([^"\n\r]*)" (dropdown|text) field of row "([^"\n\r]*)" in the widget editor dialog is (displayed|hidden)$/,
    async (name: string, fieldType: 'dropdown' | 'text' | 'date', rowId: number, cssState: 'displayed' | 'hidden') => {
        const dashboardEditor = new DashboardEditor();
        await dashboardEditor.expectRowFieldDisplayed({ name, rowId, reverse: cssState === 'hidden' });
    },
);

Then(
    /^the "([^"\n\r]*)" (dropdown|text) field of row "([^"\n\r]*)" in the widget editor dialog is (enabled|disabled)$/,
    async (name: string, fieldType: 'dropdown' | 'text', rowId: number, cssState: 'enabled' | 'disabled') => {
        const dashboardEditor = new DashboardEditor();
        await dashboardEditor.expectRowFieldEnabled({ name, rowId, reverse: cssState === 'disabled' });
    },
);

Then(
    /^the value of the "([^"\n\r]*)" paragraph field of row "([^"\n\r]*)" in the widget editor dialog is "(.*)"$/,
    async (name: string, rowId: number, value: string) => {
        const dashboardEditor = new DashboardEditor();
        await dashboardEditor.expectPFieldContent({ name, rowId, value });
    },
);

Then(
    /^the user (ticks|unticks) the "(.*)" checkbox field in the widget editor dialog$/,
    async (state: string, value: string) => {
        const dashboardEditor = new DashboardEditor();
        await dashboardEditor.tickUntickCheckboxField(value, state);
    },
);

Then(
    /^the "(.*)" preview button in the widget editor dialog is (displayed|hidden)$/,
    async (buttonName: string, cssState: 'displayed' | 'hidden') => {
        const dashboardEditor = new DashboardEditor();
        await dashboardEditor.expectWidgetEditorPreviewButton(buttonName, cssState === 'hidden');
    },
);

Then(
    /^the "(.*)" checkbox field in the widget editor dialog is (checked|unchecked)$/,
    async (fieldName: string, state: 'checked' | 'unchecked') => {
        const dashboardEditor = new DashboardEditor();
        await dashboardEditor.expectCheckboxFieldChecked(fieldName, state === 'unchecked');
    },
);

// widget steps
Then(
    /^the "(.*)" more actions button in the header of the (basic|bar-chart|gauge|line-chart|table|tile-indicator|visual-process|contact-card)? widget field is (enabled|disabled)$/,
    async (name: string, type: WidgetType, cssState: 'enabled' | 'disabled') => {
        const widget = <DashboardWidget>StaticStore.getStoredObject(StaticStore.StoredKeys.DASHBOARD_WIDGET);
        await widget.openHeaderActionMenu(type);
        await widget.expectActionButtonToBeEnabled(name, cssState === 'disabled');
        await widget.loseFocus();
    },
);

Then(
    /^the "(.*)" more actions button in the header of the (basic|bar-chart|gauge|line-chart|table|tile-indicator|visual-process|contact-card)? widget field is (displayed|hidden)$/,
    async (name: string, type: WidgetType, cssState: 'displayed' | 'hidden') => {
        const widget = <DashboardWidget>StaticStore.getStoredObject(StaticStore.StoredKeys.DASHBOARD_WIDGET);
        await widget.openHeaderActionMenu(type);
        await widget.expectActionButtonToBeDisplayed(name, cssState === 'hidden');
        await widget.loseFocus();
    },
);

Then(
    /^the user clicks the "(.*)"(?: more actions)? button in the header of the (basic|bar-chart|gauge|line-chart|table|tile-indicator|visual-process|contact-card)? widget field$/,
    async (btnName: string, type: WidgetType) => {
        const widget = <DashboardWidget>StaticStore.getStoredObject(StaticStore.StoredKeys.DASHBOARD_WIDGET);
        await widget.openHeaderActionMenu(type);
        await widget.clickButtonOnHeader(btnName);
    },
);

Then(/^the value of the basic widget field contains$/, async (expectedValue: any) => {
    const widget = <DashboardWidget>StaticStore.getStoredObject(StaticStore.StoredKeys.DASHBOARD_WIDGET);
    await widget.expectWidgetBasicValue(expectedValue);
});

Then(
    /^the user clicks the "(.*)" button of the (basic|bar-chart|gauge|line-chart|table|tile-indicator|visual-process)? widget field$/,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    async (name: string, _type: WidgetType) => {
        const dashboardWidget = <DashboardWidget>StaticStore.getStoredObject(StaticStore.StoredKeys.DASHBOARD_WIDGET);
        await dashboardWidget.clickButtonOnFooter(name);
    },
);

Then(
    /^the "(.*)" button of the (basic|bar-chart|gauge|line-chart|table|tile-indicator|visual-process)? widget field is (enabled|disabled)$/,
    async (name: string, _type: WidgetType, cssState: 'enabled' | 'disabled') => {
        const dashboardWidget = <DashboardWidget>StaticStore.getStoredObject(StaticStore.StoredKeys.DASHBOARD_WIDGET);
        await dashboardWidget.expectDashboardWidgetButtonEnabled(name, cssState === 'disabled');
        await dashboardWidget.loseFocus();
    },
);

Then(
    /^the "(.*)" button of the (basic|bar-chart|gauge|line-chart|table|tile-indicator|visual-process)? widget field is (displayed|hidden)$/,
    async (name: string, _type: WidgetType, cssState: 'displayed' | 'hidden') => {
        const dashboardWidget = <DashboardWidget>StaticStore.getStoredObject(StaticStore.StoredKeys.DASHBOARD_WIDGET);
        await dashboardWidget.expectDashboardWidgetButtonToBeDisplayed(name, cssState === 'hidden');
        await dashboardWidget.loseFocus();
    },
);

Then(/^the user selects the card ([0-9]*) of the table widget field$/, async (rowId: string) => {
    const dashboardWidget = <DashboardWidget>StaticStore.getStoredObject(StaticStore.StoredKeys.DASHBOARD_WIDGET);
    await dashboardWidget.selectCard(rowId);
});

Then(/^the user (ticks|unticks) the card of the table widget field$/, async (state: string) => {
    const dashboardWidget = <DashboardWidget>StaticStore.getStoredObject(StaticStore.StoredKeys.DASHBOARD_WIDGET);
    await dashboardWidget.tickUntickSelectedCard(state);
});

Then(
    /^the user clicks row actions button of the selected (card|row) of the table widget field$/,
    async (tableMode: string) => {
        const dashboardWidget = <DashboardWidget>StaticStore.getStoredObject(StaticStore.StoredKeys.DASHBOARD_WIDGET);
        await dashboardWidget.clickRowActionsButton(tableMode);
    },
);

Then(
    /^the user clicks the "([^"\n\r]*)" dropdown action of the selected (card|row) of the table widget field$/,
    async (dropdownAction: string, tableMode: string) => {
        const dashboardWidget = <DashboardWidget>StaticStore.getStoredObject(StaticStore.StoredKeys.DASHBOARD_WIDGET);
        await dashboardWidget.clickRowActionsButton(tableMode);
        await dashboardWidget.clickRowActionsDropdownButton(dropdownAction);
    },
);

Then(
    /^the user scrolls to the (first|last) (card|row) of the table widget field$/,
    async (scrollOption: string, name: string) => {
        const dashboardWidget = <DashboardWidget>StaticStore.getStoredObject(StaticStore.StoredKeys.DASHBOARD_WIDGET);
        await dashboardWidget.scrollFirstLast(scrollOption, name);
    },
);
Then(
    /^the user scrolls down to the (card|row) with text "([^"\n\r]*)" and (column header|card section) "([^"\n\r]*)" of the table widget field$/,
    async (representationName: string, rowText: string, headerOrSection: string, headerOrSectionName: string) => {
        const dashboardWidget = <DashboardWidget>StaticStore.getStoredObject(StaticStore.StoredKeys.DASHBOARD_WIDGET);
        await dashboardWidget.scrollDownToItem({ representationName, rowText, headerOrSection, headerOrSectionName });
    },
);

Then(/^the user (ticks|unticks) all the cards of the table widget field$/, async (state: string) => {
    const dashboardWidget = <DashboardWidget>StaticStore.getStoredObject(StaticStore.StoredKeys.DASHBOARD_WIDGET);
    await dashboardWidget.tickUntickAll(state, 'card');
});

Then(
    /^the value of the row ([0-9]*) on the (left|right) of the card of the table widget field is "(.*)"$/,
    async (rowId: string, alignment: Alignment, value: string) => {
        const dashboardWidget = <DashboardWidget>StaticStore.getStoredObject(StaticStore.StoredKeys.DASHBOARD_WIDGET);
        await dashboardWidget.expectedValue({ rowId, alignment, value });
    },
);

Then(
    /^the user stores the value of the row ([0-9]*) on the (left|right) of the card of the table widget with key "(.*)"$/,
    async (rowId: string, alignment: Alignment, key: string) => {
        const dashboardWidget = <DashboardWidget>StaticStore.getStoredObject(StaticStore.StoredKeys.DASHBOARD_WIDGET);
        const elem = await $(dashboardWidget.getCorrectSelectorperAlignment(rowId, alignment));
        StaticStore.storeObject(key, await elem.getText());
    },
);

Then(
    /^the user clicks the link of the row ([0-9]*) on the (left|right) of the card of the table widget field$/,
    async (rowId: string, alignment: Alignment) => {
        const dashboardWidget = <DashboardWidget>StaticStore.getStoredObject(StaticStore.StoredKeys.DASHBOARD_WIDGET);
        await dashboardWidget.selectLink(rowId, alignment);
    },
);

Then(
    /^the user selects the row with text "([^"\n\r]*)" and column header "([^"\n\r]*)" of the table widget field$/,
    async (rowText: string, headerValue: string) => {
        const dashboardWidget = <DashboardWidget>StaticStore.getStoredObject(StaticStore.StoredKeys.DASHBOARD_WIDGET);
        await dashboardWidget.selectSwitchTableRow(rowText, headerValue);
    },
);

Then(/^the user (ticks|unticks) the row of the table widget field$/, async (state: string) => {
    const dashboardWidget = <DashboardWidget>StaticStore.getStoredObject(StaticStore.StoredKeys.DASHBOARD_WIDGET);
    await dashboardWidget.tickUntickSelectedTableRow(state);
});

Then(/^the user (ticks|unticks) all the rows of the table widget field$/, async (state: string) => {
    const dashboardWidget = <DashboardWidget>StaticStore.getStoredObject(StaticStore.StoredKeys.DASHBOARD_WIDGET);
    await dashboardWidget.tickUntickAll(state, 'table');
});

Then(/^the user (expands|collapses) the row of the table widget field$/, async (state: string) => {
    const dashboardWidget = <DashboardWidget>StaticStore.getStoredObject(StaticStore.StoredKeys.DASHBOARD_WIDGET);
    await dashboardWidget.expandCollapseSelectedTableRow(state);
});

Then(
    /^the value of the cell with column header "([^"\n\r]*)" of the row of the table widget field is "(.*)"$/,
    async (headerValue: string, value: string) => {
        const storeValue = value.startsWith('[ENV_') ? StaticStore.getUserdefinedKeyValueFromStore(value) : value;
        const dashboardWidget = <DashboardWidget>StaticStore.getStoredObject(StaticStore.StoredKeys.DASHBOARD_WIDGET);
        await dashboardWidget.expectTableWidgetTableValue(storeValue, headerValue);
    },
);

Then(
    /^the user clicks the link of the cell with column header "(.*)" of the row of the table widget field$/,
    async (headerValue: string) => {
        const dashboardWidget = <DashboardWidget>StaticStore.getStoredObject(StaticStore.StoredKeys.DASHBOARD_WIDGET);
        await dashboardWidget.clickTableWidgetTableRowColumn(headerValue);
    },
);

Then(/^the value of the gauge widget field is "(.*)"$/, async (value: string) => {
    const dashboardWidget = <DashboardWidget>StaticStore.getStoredObject(StaticStore.StoredKeys.DASHBOARD_WIDGET);
    await dashboardWidget.expectWidgetGaugeValue(value);
});

Then(/^the value of the tile-indicator widget field is "(.*)"$/, async (value: string) => {
    const dashboardWidget = <DashboardWidget>StaticStore.getStoredObject(StaticStore.StoredKeys.DASHBOARD_WIDGET);
    await dashboardWidget.expectWidgetTileIndicatorValue(value);
});

Then(/^the number of records in the table widget fields is "(.*)"$/, async (value: string) => {
    const dashboardWidget = <DashboardWidget>StaticStore.getStoredObject(StaticStore.StoredKeys.DASHBOARD_WIDGET);
    await dashboardWidget.expectPillValue(value);
});

Then(/^the total count of records in the table widget fields is "(.*)"$/, async (value: string) => {
    const dashboardWidget = <DashboardWidget>StaticStore.getStoredObject(StaticStore.StoredKeys.DASHBOARD_WIDGET);
    await dashboardWidget.expectTotalCountValue(value);
});

Then(
    /^the "([^"\n\r]*)" filter dropdown of the (contact-card|table) widget field is (displayed|hidden)$/,
    async (filterName: string, _type: WidgetType, cssState: 'displayed' | 'hidden') => {
        const dashboardWidget = <DashboardWidget>StaticStore.getStoredObject(StaticStore.StoredKeys.DASHBOARD_WIDGET);
        await dashboardWidget.waitForFilterDropdownToBeDisplayed(filterName, cssState === 'hidden');
    },
);

Then(
    /^the user clicks the "([^"\n\r]*)" toggle button in the header of the (basic|bar-chart|gauge|line-chart|table|tile-indicator|visual-process|contact-card|pie-chart)? widget field$/,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    async (input: string, _type: WidgetType) => {
        const dashboardWidget = <DashboardWidget>StaticStore.getStoredObject(StaticStore.StoredKeys.DASHBOARD_WIDGET);
        await dashboardWidget.toggleHeaderButton(input);
    },
);

Then(
    /^the user clears the value of the "([^"\n\r]*)" filter dropdown of the (contact-card|table) widget field$/,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    async (filterName: string, _type: WidgetType) => {
        const dashboardWidget = <DashboardWidget>StaticStore.getStoredObject(StaticStore.StoredKeys.DASHBOARD_WIDGET);
        await dashboardWidget.waitForFilterDropdownToBeDisplayed(filterName);
        await dashboardWidget.clearFilterDropdownValue();
    },
);

Then(
    /^the value of the "([^"\n\r]*)" filter dropdown of the (contact-card|table) widget field is "([^"\n\r]*)"$/,
    async (filterName: string, _type: WidgetType, value: string) => {
        const dashboardWidget = <DashboardWidget>StaticStore.getStoredObject(StaticStore.StoredKeys.DASHBOARD_WIDGET);
        await dashboardWidget.waitForFilterDropdownToBeDisplayed(filterName);
        await dashboardWidget.expectFilterDropdownValue(filterName, value);
    },
);

Then(
    /^the user selects "([^"\n\r]*)" in the "([^"\n\r]*)" filter dropdown of the (contact-card|table) widget field$/,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    async (value: string, filterName: string, _type: WidgetType) => {
        const dashboardWidget = <DashboardWidget>StaticStore.getStoredObject(StaticStore.StoredKeys.DASHBOARD_WIDGET);
        await dashboardWidget.waitForFilterDropdownToBeDisplayed(filterName);
        await dashboardWidget.clickFilterDropdown(filterName);
        await dashboardWidget.selectFilterDropdownValue(value);
    },
);

Then(
    /^the user (increases|decreases) the widget field by (-?\d+),(-?\d+) pixels$/,
    async (signString: 'increases' | 'decreases', xStep: number, yStep: number) => {
        const dashboardWidget = <DashboardWidget>StaticStore.getStoredObject(StaticStore.StoredKeys.DASHBOARD_WIDGET);
        const sign = signString === 'increases' ? 1 : -1;
        await dashboardWidget.resize(sign * xStep, sign * yStep);
    },
);

When(
    /^the user clicks the "(previous|next)" period button in the (table) widget$/,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    async (direction: 'previous' | 'next', _type: WidgetType) => {
        const dashboardWidget = <DashboardWidget>StaticStore.getStoredObject(StaticStore.StoredKeys.DASHBOARD_WIDGET);
        await dashboardWidget.clickPeriodButton(direction);
    },
);

When(
    /^the user selects the "(.*)" period type toggle button in the (table) widget$/,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    async (period: 'day' | 'week' | 'month', _type: WidgetType) => {
        const dashboardWidget = <DashboardWidget>StaticStore.getStoredObject(StaticStore.StoredKeys.DASHBOARD_WIDGET);
        await dashboardWidget.selectPeriodType(period);
    },
);

When(
    /^the user selects the "(.*)" date period in the (table) widget$/,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    async (period: string, _type: WidgetType) => {
        const dashboardWidget = <DashboardWidget>StaticStore.getStoredObject(StaticStore.StoredKeys.DASHBOARD_WIDGET);
        await dashboardWidget.selectDatePeriod(period);
    },
);
