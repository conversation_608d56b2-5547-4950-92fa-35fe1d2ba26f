import { ElementContext, LookupStrategy } from '../../../step-definitions-utils';
import { FieldObject } from '../field-object';

export class LinkFieldObject extends FieldObject {
    constructor({
        identifier,
        lookupStrategy,
        context,
    }: {
        identifier: string;
        lookupStrategy: LookupStrategy;
        context?: ElementContext;
    }) {
        super({ fieldType: 'link', identifier, lookupStrategy, context });
    }

    override async click() {
        await browser.waitUntil(
            async () => {
                const aElement = await (await this.find('a')).isExisting();
                const buttonElement = await (await this.find('button')).isExisting();

                if (aElement) {
                    await super.click('a');
                    return true;
                }

                if (buttonElement) {
                    await super.click('button');
                    return true;
                }

                return false;
            },
            { timeout: this.timeoutWaitFor, timeoutMsg: 'Cannot click on link' },
        );
    }
}
