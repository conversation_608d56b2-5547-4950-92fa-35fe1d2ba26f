import * as utils from '../../../step-definitions-utils';
import AbstractPageObject from '../../abstract-page-object';
import { DropDownMenuObject } from '../../dropdown-menu-object';
import * as StaticStore from '../../static-store';
import { testGlobals } from '../../test-globals';

export enum DialogDisplayTypes {
    mainpage = ' on the main page',
    fullscreen = ' on a full width modal',
    sidebar = ' on the sidebar',
}

export type DialogTypes = 'Message' | 'Confirm' | 'Custom' | 'external link confirmation' | 'Lookup';

export type InputDialogLevel = 'an error' | 'an info' | 'a success' | 'a warn';

export type DialogSectionTypes = 'header' | 'header pill label' | 'body';

export type DialogButtonTypes = 'Accept' | 'Cancel' | 'OK' | 'Yes' | 'No';

const getDialogSelector = (dialogDisplayType?: DialogDisplayTypes) => {
    if (testGlobals.device === 'mobile' || dialogDisplayType === DialogDisplayTypes.fullscreen) {
        return 'div[data-component="dialog-full-screen"][data-state="open"]';
    }
    if (dialogDisplayType === DialogDisplayTypes.sidebar) {
        return '[data-state="open"] div[data-component="sidebar"]';
    }
    return 'div.carbon-portal:not([aria-hidden]) [data-state="open"] div[role="dialog"]';
};

const getDialogSectionSelector = async (dialogSectionType: DialogSectionTypes, inputDialogLevel?: InputDialogLevel) => {
    const browserURL = await browser.getUrl();
    if (dialogSectionType === 'header') {
        if (utils.regex_handheld(browserURL)) {
            return '[data-component="navigation-bar"] .se-header-title';
        }
        return 'h1';
    }
    if (dialogSectionType === 'header pill label') {
        return 'h1 [data-component="pill"]';
    }

    let dialogSelectorToUse = '';
    if (inputDialogLevel === 'an error') {
        dialogSelectorToUse = `${utils.getDataTestIdSelector({ domSelector: 'div', dataTestIdValue: 'e-dialog-body' })} .e-dialog-error-content`;
    } else {
        dialogSelectorToUse = `${utils.getDataTestIdSelector({ domSelector: 'div', dataTestIdValue: 'e-dialog-body' })} .e-dialog-text-content`;
    }

    return dialogSelectorToUse;
};
export class DialogObject extends AbstractPageObject {
    public readonly dropDownMenu: DropDownMenuObject;

    constructor(dialogDisplayType?: DialogDisplayTypes) {
        super(getDialogSelector(dialogDisplayType));
        this.dropDownMenu = new DropDownMenuObject(this.cssSelector);
    }

    getButtonSelector(dialogButtonType: DialogButtonTypes | string) {
        const buttonPosition = dialogButtonType === 'Accept' ? 1 : 2;
        return this.getSelectorForOperation(`.e-dialog-body button:nth-child(${buttonPosition})`);
    }

    async expectTitleDisplayed(expectedTitle: string) {
        await browser.waitUntil(
            async () => {
                const actualTitle = await this.getTitle();
                return expectedTitle === actualTitle;
            },
            {
                timeout: this.valueCheckTimeout,
                timeoutMsg: `Expected dialog title "${expectedTitle}" was not displayed within the "${this.valueCheckTimeout}"`,
            },
        );
    }

    async getDialogButton(buttonText: string, dialogType?: string) {
        if (dialogType === ' external link confirmation') {
            const dialog = await this.find('[data-component="form"]');
            await utils.waitForElementToBeDisplayed({ name: 'dialog', selector: dialog.selector.toString() });

            const dialogButton = await dialog.$(
                `[data-testid="xe-external-link-confirmation-dialog-action-${buttonText.toLowerCase()}-button"]`,
            );
            if (await dialogButton.isExisting()) {
                return dialogButton;
            }
        } else {
            const dialog = await this.find('.e-dialog-body');
            await utils.waitForElementToBeDisplayed({ name: 'dialog', selector: dialog.selector.toString() });

            const dialogButtons = await this.findAll('.e-dialog-body button');
            // eslint-disable-next-line no-restricted-syntax
            for (const dialogButton of dialogButtons) {
                if (buttonText.toLowerCase() === (await dialogButton.getText()).trim().toLowerCase()) {
                    return dialogButton;
                }
            }
        }

        throw new Error(`Button with ${buttonText} label was not found`);
    }

    async getCloseButton() {
        const browserURL = await browser.getUrl();
        if (utils.regex_handheld_settings(browserURL)) {
            return this.find('[data-component="navigation-bar"] [data-element="close"]', true);
        }
        return this.find('span[type="close"]');
    }

    // eslint-disable-next-line class-methods-use-this
    async getTitle() {
        const titleElement = await browser.$(
            '[data-component|="dialog"] [data-component="heading"] [data-element="header-container"] [data-element="title"], [data-component|="sidebar"] [data-component="heading"] [data-element="header-container"] [data-element="title"]',
        );
        return titleElement.getText();
    }

    // eslint-disable-next-line class-methods-use-this
    async getSubtitle() {
        const titleElement = await browser.$(
            '[data-component="heading"] [data-element="header-container"] [data-element="subtitle"]',
        );
        return titleElement.getText();
    }

    async expectDialogButtonToBeDisplayed(dialogButtonType: string, reverse = false) {
        const buttonSelector = this.getButtonSelector(dialogButtonType);
        await this.expectToBeDisplayed(buttonSelector, reverse);
    }

    async expectDialogButtonToBeEnabled(dialogButtonType: string, reverse = false) {
        const buttonSelector = await this.getDialogButton(dialogButtonType);
        if (reverse) {
            return !buttonSelector.isEnabled();
        }
        return buttonSelector.isEnabled();
    }

    async expectDialogText({
        dialogText,
        dialogSectionType,
        inputDialogLevel,
    }: {
        dialogText: string;
        dialogSectionType: DialogSectionTypes;
        inputDialogLevel?: InputDialogLevel;
    }) {
        const browserURL = await browser.getUrl();
        const ignoreContext = utils.regex_handheld(browserURL) != null;
        const dialogSelector = await getDialogSectionSelector(dialogSectionType, inputDialogLevel);
        await this.expectTextContent({
            toBe: dialogText,
            ignoreCase: false,
            cssSelector: dialogSelector,
            ignoreContext,
        });
    }

    async expectTextRex({
        toBe,
        dialogSectionType,
        inputDialogLevel,
    }: {
        toBe: string;
        dialogSectionType: DialogSectionTypes;
        inputDialogLevel?: InputDialogLevel;
    }) {
        const browserURL = await browser.getUrl();
        const ignoreContext = utils.regex_handheld(browserURL) != null;
        const cssSelector = await getDialogSectionSelector(dialogSectionType, inputDialogLevel);

        const selectorToUse = this.getSelectorForOperation(cssSelector, ignoreContext);
        const element = await browser.$(selectorToUse);
        const actualMessage: string[] = [];
        await element.waitForExist({ timeoutMsg: `Element does not exist.\nSelector: ${selectorToUse}` });
        await element.waitForDisplayed({
            timeoutMsg: `Element not displayed.\nSelector: ${selectorToUse}`,
        });
        const textContent: string[] = [];
        try {
            await element.moveTo();
            await browser.waitUntil(
                async () => {
                    textContent.push(await element.getText());
                    if (textContent.length > 0) {
                        const messageStr = textContent.toString();
                        actualMessage.push(messageStr);
                    }

                    const regexp = new RegExp(toBe);
                    return textContent.some(message => {
                        return Boolean(message.match(regexp));
                    });
                },
                { timeout: this.valueCheckTimeout },
            );
        } catch (error) {
            await this.takePuppeteerPageScreenshot({ captureBeyondViewport: false });
            throw new Error(`Expected value: "${toBe}", actual: "${textContent[0]}".\nSelector: ${selectorToUse}`);
        }
    }

    async dialogTextContain(dialogText: string, inputDialogLevel?: InputDialogLevel) {
        const browserURL = await browser.getUrl();
        const ignoreContext = utils.regex_handheld(browserURL) != null;
        const dialogSelector = await getDialogSectionSelector('body', inputDialogLevel);
        await this.expectTextContains({
            toBe: dialogText,
            ignoreCase: false,
            cssSelector: dialogSelector,
            ignoreContext,
        });
    }

    async dialogExtractCharacter(
        startPosition: number,
        numberOfCharacters: number,
        keyValue: string,
        inputDialogLevel?: InputDialogLevel,
    ) {
        try {
            const text = await this.getDialogText(inputDialogLevel);
            const extractText = this.extractCharacters(text, startPosition, numberOfCharacters);
            StaticStore.storeObject(keyValue, extractText);
        } catch (error) {
            throw new Error(error);
        }
    }

    private async getDialogText(inputDialogLevel?: InputDialogLevel): Promise<string> {
        const browserURL = await browser.getUrl();
        const ignoreContext = utils.regex_handheld(browserURL) != null;
        const dialogSelector = await getDialogSectionSelector('body', inputDialogLevel);
        const selectorToUse = this.getSelectorForOperation(dialogSelector, ignoreContext);
        const element = await browser.$(selectorToUse);
        await element.waitForExist({ timeoutMsg: `Element does not exist.\nSelector: ${selectorToUse}` });
        await element.waitForDisplayed();
        return element.getText();
    }

    // eslint-disable-next-line class-methods-use-this
    private extractCharacters(text: string, startPosition: number, numberOfCharacters: number): string {
        if (startPosition < 0 || numberOfCharacters <= 0 || startPosition >= text.length) {
            throw new Error(
                `Please ensure that the required values for extraction are provided: Position:"${startPosition}", Number of characters:"${numberOfCharacters}"`,
            );
        }
        // Extract substring
        return text.substr(startPosition, numberOfCharacters);
    }

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    async expectDialogToAppear(dialogLevel: any) {
        await this.expectToAppear();
        await this.find(utils.getDataTestIdSelector({ domSelector: 'div', dataTestIdValue: 'e-dialog-body' }));
    }

    async expectDialogToDisappear() {
        await this.expectToDisappear();
    }

    async searchFor(query: string) {
        const selectorToUse = `${utils.getDataTestIdSelector({ domSelector: 'div', dataTestIdValue: 'e-table-field-mobile-search' })} input`;
        await this.clearInput({ cssSelector: selectorToUse });
        await this.write({ content: query, cssSelector: selectorToUse });
    }

    async clickBreadcrumbWithText(text: string) {
        const selectorToUse = '[aria-label="breadcrumbs"] span';
        const spans = await this.findAll(selectorToUse);

        // eslint-disable-next-line no-restricted-syntax
        for (const span of spans) {
            const spanWithText = await span.getText();
            if (text === spanWithText) {
                await browser.pause(200);
                await span.click();
                return;
            }
        }
        throw new Error(`Expected breadcrumb with text: "${text}" could not be found`);
    }

    async clickCreateButton() {
        const createItemButton = await this.find('[data-testid="e-lookup-dialog-create-new-item-button"]');
        await createItemButton.click();
    }

    async isErrorMessage() {
        return (await this.find('div.e-dialog-type-error')).isExisting();
    }

    async getErrorMessageText() {
        let errorMessage = '';
        const browserURL = await browser.getUrl();
        const ignoreContext = utils.regex_handheld(browserURL) != null;
        let cssSelector = await getDialogSectionSelector('body', 'an error');

        let selectorToUse = this.getSelectorForOperation(cssSelector, ignoreContext);
        let element = await browser.$(selectorToUse);

        // For errors displayed when there is no page content the .e-dialog-text-content is used not .e-dialog-error-content
        if (await element.isExisting()) {
            errorMessage = await element.getText();
        } else {
            cssSelector = await getDialogSectionSelector('body', 'a warn');
            selectorToUse = this.getSelectorForOperation(cssSelector, ignoreContext);
            element = await browser.$(selectorToUse);
            errorMessage = await element.getText();
        }

        return errorMessage;
    }

    // eslint-disable-next-line class-methods-use-this
    getDialogTypeClassName(type: DialogTypes): string {
        switch (type) {
            case 'Confirm':
                return '.e-dialog-content.e-dialog-type-confirmation';
            case 'Custom':
                return '.e-dialog-content.e-dialog-type-custom';
            case 'Message':
                return '.e-dialog-content.e-dialog-type-message';
            case 'Lookup':
                return '.e-dialog-content.e-dialog-type-lookup';
            default:
                throw new Error(`Expected unknown dialog type "${type}" to exist.`);
        }
    }

    async expectDialogTypeToExist(type: DialogTypes, isReversed: boolean): Promise<void> {
        const selectorToUse = `[data-component="dialog"] ${this.getDialogTypeClassName(type)}`;
        // INFO: Using $() instead of this.find() to avoid the test to wait for element to be displayed.
        const isExistingElement = await $(selectorToUse).isExisting();

        if (isReversed && isExistingElement) {
            throw new Error(`Expected dialog of type: "${type}" to not exist. Selector: ${selectorToUse}`);
        }

        if (!isReversed && !isExistingElement) {
            throw new Error(`Expected dialog of type: "${type}" to exist. Selector: ${selectorToUse}`);
        }
    }
}
