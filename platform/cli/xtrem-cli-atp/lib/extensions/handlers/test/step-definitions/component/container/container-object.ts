import { ElementContext, getContextSelector } from '../../step-definitions-utils';
import AbstractPageObject from '../abstract-page-object';

export abstract class ContainerObject extends AbstractPageObject {
    protected containerType: string;

    constructor({ identifier, context }: { containerType: string; identifier: string; context?: ElementContext }) {
        super((context ? `${getContextSelector(context)} ` : '') + identifier);
    }
}
