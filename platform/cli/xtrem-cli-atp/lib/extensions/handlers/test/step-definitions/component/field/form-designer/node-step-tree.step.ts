import { When } from '@cucumber/cucumber';
import { ElementContext, ExpandedOrCollapsed, fieldTypes } from '../../../step-definitions-utils';
import pageModel from '../../main-page';
import * as StaticStore from '../../static-store';
import { NodeStepTreeObject } from './node-step-tree-object';

When(
    /^the user selects the node-step-tree in the form designer on (the main page|a modal)$/,
    async (context: ElementContext) => {
        const field = new NodeStepTreeObject(context);
        await field.dismissAllNotification();
        await (
            await field.get()
        ).waitForExist({
            timeoutMsg: `Expected node step tree could not be found.\nSelector: ${field.cssSelector}`,
        });
        await StaticStore.storeField(fieldTypes.nodeStepTree, field);
    },
);

When(
    /^the user selects the tree-view element of level "([^"\n\r]*)" with text "([^"\n\r]*)" in the node-step-tree of the form designer$/,
    async (nodeLevel: string, nodeElementText: string) => {
        const field = <NodeStepTreeObject>StaticStore.getStoredField(fieldTypes.nodeStepTree);
        const storeValue = StaticStore.getUserdefinedKeyValueFromStore(nodeElementText);
        const nodeElementID = await field.getNodeElement(nodeLevel, storeValue);
        StaticStore.storeObject<WebdriverIO.Element>(StaticStore.StoredKeys.NODE_STEP_TREE_ELEMENT, nodeElementID);
    },
);

When(
    /^the user (expands|collapses) the tree-view element in the node-step-tree of the form designer$/,
    async (toggle: ExpandedOrCollapsed) => {
        const field = <NodeStepTreeObject>StaticStore.getStoredField(fieldTypes.nodeStepTree);
        const treeElement = StaticStore.getStoredObject<WebdriverIO.Element>(
            StaticStore.StoredKeys.NODE_STEP_TREE_ELEMENT,
        );
        await field.expandCollapse(toggle, treeElement);
    },
);

When(
    /^the user (ticks|unticks) the tree-view element in the node-step-tree of the form designer$/,
    async (toggleState: string) => {
        const field = <NodeStepTreeObject>StaticStore.getStoredField(fieldTypes.nodeStepTree);
        await pageModel.waitForFinishLoading();
        const treeElement = StaticStore.getStoredObject<WebdriverIO.Element>(
            StaticStore.StoredKeys.NODE_STEP_TREE_ELEMENT,
        );
        await field.selectNodeElementWithText(toggleState, treeElement);
    },
);

When(/^the user clicks the tree-view element in the node-step-tree of the form designer$/, async () => {
    const field = <NodeStepTreeObject>StaticStore.getStoredField(fieldTypes.nodeStepTree);
    await pageModel.waitForFinishLoading();
    const treeElement = StaticStore.getStoredObject<WebdriverIO.Element>(StaticStore.StoredKeys.NODE_STEP_TREE_ELEMENT);
    await field.selectNodeElementWithText('any', treeElement);
});
