import { Then, When } from '@cucumber/cucumber';
import {
    fieldTypes,
    LookupStrategy,
    NestedFieldTypes,
    takeScreenshot,
    waitForElementToBeFound,
} from '../../../../step-definitions-utils';
import * as StaticStore from '../../../static-store';
import { waitForPromises } from '../../wait-util';
import { scrollToTableColumn, scrollToTableRow, waitForTableToBePopulated } from '../tableUtils';
import { NestedFieldObject } from './table-nested-field-object';
import {
    getSelectedRow,
    getTableObject,
    handleScrollError,
    rowIndexSelector,
    SelectedRowDetails,
} from './table-nested-field-utils';

When(
    /^the user selects the row with text "([^"\n\r]*)" in the "([^"\n\r]*)" (bound|labelled) column header of the table field$/,
    async (rowId: string, columnName: string, lookupStrategy: LookupStrategy) => {
        const table = getTableObject();
        await table.expect.waitForTableStopLoading();
        await scrollToTableColumn({ tableSelector: table.cssSelector, columnName, lookupStrategy });
        const rowIdentifier = StaticStore.getUserdefinedKeyValueFromStore(rowId);
        const rowNumber = await table.rows.getRowIndex({ rowIdentifier, columnName, lookupStrategy });
        const selectedRow: SelectedRowDetails = {
            isFloatingRow: false,
            rowNumber,
        };
        StaticStore.storeObject<SelectedRowDetails>(StaticStore.StoredKeys.ROW, selectedRow);
    },
);

When(
    /^the user selects the row with text "([^"\n\r]*)" in the "([^"\n\r]*)" (bound|labelled) column header of the summary table field$/,
    async (rowId: string, columnName: string, lookupStrategy: LookupStrategy) => {
        const table = getTableObject();
        await table.expect.waitForTableStopLoading();
        const selectedRowIdentifier = await table.rows.getUniqueID({
            rowIdentifier: StaticStore.getUserdefinedKeyValueFromStore(rowId),
            columnName,
            lookupStrategy,
        });
        StaticStore.storeObject<string>(StaticStore.StoredKeys.ROW, selectedRowIdentifier);
    },
);

When(
    /^the user selects the (?:(?:row ([0-9]*))|(floating row)) of the table field$/,
    async (rowNumber: number, floatingRow: string | null) => {
        const table = getTableObject();
        await table.expect.waitForTableStopLoading();
        if (rowNumber !== null) await waitForTableToBePopulated(table.cssSelector);
        await scrollToTableRow(`${table.cssSelector} ${rowIndexSelector(rowNumber, floatingRow !== null)}`, rowNumber);
        await waitForElementToBeFound({
            name: 'row to select',
            selector: `${table.cssSelector} ${rowIndexSelector(rowNumber, floatingRow !== null)}`,
        });
        const selectedRow: SelectedRowDetails = {
            isFloatingRow: floatingRow !== null,
            rowNumber: rowNumber ?? 0, // Provide a default value for floating rows
        };
        StaticStore.storeObject<SelectedRowDetails>(StaticStore.StoredKeys.ROW, selectedRow);
    },
);

// To deprecate
// When(
//     /^the user selects (the row|the floating row) with text "([^"\n\r]*)" in the "([^"\n\r]*)" (bound|labelled) column header of the table field$/,
//     async (
//         rowType: 'the row' | 'the floating row',
//         rowId: string,
//         columnName: string,
//         lookupStrategy: LookupStrategy,
//     ) => {
//         const table = getTableObject();
//         await table.expect.waitForTableStopLoading();
//         await scrollToTableColumn({ tableSelector: table.cssSelector, columnName, lookupStrategy });
//         const rowIdentifier = StaticStore.getUserdefinedKeyValueFromStore(rowId);
//         const rowIndex = await table.rows.getRowIndex({ rowType, rowIdentifier, columnName, lookupStrategy });
//         StaticStore.storeObject(StaticStore.StoredKeys.ROW, rowIndex);
//     },
// );

When(
    /^the user writes "([^"\n\r]*)" in the "([^"\n\r]*)" (bound|labelled) nested (date|dropdown-list|numeric|reference|select|text|filter select) field of the selected row in the table field$/,
    async (value: string, columnName: string, nestedLookupStrategy: LookupStrategy, fieldType: NestedFieldTypes) => {
        const { cssSelector: tableSelector } = getTableObject();
        const { isFloatingRow, rowNumber } = getSelectedRow();

        const field = new NestedFieldObject({
            tableSelector,
            columnName,
            nestedLookupStrategy,
            rowNumber,
            isFloatingRow,
        });
        await field.waitForTableStopLoading();

        await scrollToTableColumn({ tableSelector, columnName, lookupStrategy: nestedLookupStrategy });
        await scrollToTableRow(field.cssSelector, rowNumber);

        const storeValue = StaticStore.getUserdefinedKeyValueFromStore(value);
        await field.writeTableNestedFieldValue({ value: storeValue, fieldType });
        await waitForPromises(300, 'Waiting for value to be entered');
    },
);

When(
    /^the user writes '(.*)' in the "([^"\n\r]*)" (bound|labelled) nested (text) field of the selected row in the table field$/,
    async (value: string, columnName: string, nestedLookupStrategy: LookupStrategy, fieldType: NestedFieldTypes) => {
        const { cssSelector: tableSelector } = getTableObject();
        const { isFloatingRow, rowNumber } = getSelectedRow();

        const field = new NestedFieldObject({
            tableSelector,
            columnName,
            nestedLookupStrategy,
            rowNumber,
            isFloatingRow,
        });
        await field.waitForTableStopLoading();

        await scrollToTableColumn({ tableSelector, columnName, lookupStrategy: nestedLookupStrategy });
        await scrollToTableRow(field.cssSelector, rowNumber);

        const storeValue = StaticStore.getUserdefinedKeyValueFromStore(value);
        await field.writeTableNestedFieldValue({ value: storeValue, fieldType });
        await waitForPromises(300, 'Waiting for value to be entered');
    },
);

Then(
    /^the value of the "([^"\n\r]*)" (bound|labelled) nested (aggregate|checkbox|date|relative date|dropdown-list|filter select|icon|image|label|link|numeric|progress|reference|select|text) field of the selected row in the table field is "(.*)"$/,
    async (
        columnName: string,
        nestedLookupStrategy: LookupStrategy,
        fieldType: NestedFieldTypes,
        expectedValue: string,
    ) => {
        const { cssSelector: tableSelector } = getTableObject();
        const { isFloatingRow, rowNumber } = getSelectedRow();

        const field = new NestedFieldObject({
            tableSelector,
            columnName,
            nestedLookupStrategy,
            rowNumber,
            isFloatingRow,
        });
        await field.waitForTableStopLoading();

        await scrollToTableColumn({ tableSelector, columnName, lookupStrategy: nestedLookupStrategy });
        await scrollToTableRow(field.cssSelector, rowNumber);

        const storeValue = StaticStore.getUserdefinedKeyValueFromStore(expectedValue);
        await field.expectNestedValue({ toBe: storeValue, fieldType, columnName });
    },
);

When(
    /^the user selects "([^"\n\r]*)" in the "([^"\n\r]*)" (bound|labelled) nested field of the selected row in the table field$/,
    async (value: string, columnName: string, nestedLookupStrategy: LookupStrategy) => {
        const { cssSelector: tableSelector } = getTableObject();
        const { isFloatingRow, rowNumber } = getSelectedRow();

        const field = new NestedFieldObject({
            tableSelector,
            columnName,
            nestedLookupStrategy,
            rowNumber,
            isFloatingRow,
        });
        await field.waitForTableStopLoading();

        const storeValue = StaticStore.getUserdefinedKeyValueFromStore(value);
        await field.selectValue(storeValue, columnName);
    },
);

When(
    /^the user clicks the "(.*)" (bound|labelled) nested field of the selected row in the table field$/,
    async (columnName: string, nestedLookupStrategy: LookupStrategy) => {
        const { cssSelector: tableSelector } = getTableObject();
        const { isFloatingRow, rowNumber } = getSelectedRow();

        const field = new NestedFieldObject({
            tableSelector,
            columnName,
            nestedLookupStrategy,
            rowNumber,
            isFloatingRow,
        });
        await field.waitForTableStopLoading();

        await scrollToTableColumn({ tableSelector, columnName, lookupStrategy: nestedLookupStrategy });
        await scrollToTableRow(field.cssSelector, rowNumber);

        await field.clickNestedTableField();
    },
);

When(
    /^the "([^"\n\r]*)" (bound|labelled) nested (switch) field of the selected row in the table field is set to "(.*)"$/,
    async (columnName: string, nestedLookupStrategy: LookupStrategy, fieldType: NestedFieldTypes, value: string) => {
        const { cssSelector: tableSelector } = getTableObject();
        const { isFloatingRow, rowNumber } = getSelectedRow();

        const field = new NestedFieldObject({
            tableSelector,
            columnName,
            nestedLookupStrategy,
            rowNumber,
            isFloatingRow,
        });
        await field.waitForTableStopLoading();

        await scrollToTableColumn({ tableSelector, columnName, lookupStrategy: nestedLookupStrategy });
        await scrollToTableRow(field.cssSelector, rowNumber);

        await field.expectNestedValue({ toBe: value, fieldType, columnName });
    },
);

When(
    /^the user opens the lookup dialog in the "(.*)" (bound|labelled) nested reference field of the selected row in the table field$/,
    async (columnName: string, nestedLookupStrategy: LookupStrategy) => {
        const { cssSelector: tableSelector } = getTableObject();
        const { isFloatingRow, rowNumber } = getSelectedRow();

        const field = new NestedFieldObject({
            tableSelector,
            columnName,
            nestedLookupStrategy,
            rowNumber,
            isFloatingRow,
        });
        await field.waitForTableStopLoading();

        await field.openLookupDialog(columnName);
    },
);

Then(
    /^the user clicks on the tunnel link in the "([^"\n\r]*)" (bound|labelled) nested reference field of the selected row in the table field$/,
    async (columnName: string, nestedLookupStrategy: LookupStrategy) => {
        const { cssSelector: tableSelector } = getTableObject();
        const { isFloatingRow, rowNumber } = getSelectedRow();

        const field = new NestedFieldObject({
            tableSelector,
            columnName,
            nestedLookupStrategy,
            rowNumber,
            isFloatingRow,
        });
        await field.waitForTableStopLoading();

        await scrollToTableColumn({ tableSelector, columnName, lookupStrategy: nestedLookupStrategy });
        await scrollToTableRow(field.cssSelector, rowNumber);

        await field.clickTunnelLink(columnName);
        await waitForPromises(300, 'Waiting for tunnel link to be clicked');
    },
);

When(
    /^the user writes a generated date with value "([^"\n\r]*)" in the "([^"\n\r]*)" (bound|labelled) nested date field of the selected row in the table field$/,
    async (dateString: string, columnName: string, nestedLookupStrategy: LookupStrategy) => {
        const { cssSelector: tableSelector } = getTableObject();
        const { isFloatingRow, rowNumber } = getSelectedRow();

        const field = new NestedFieldObject({
            tableSelector,
            columnName,
            nestedLookupStrategy,
            rowNumber,
            isFloatingRow,
        });
        await field.waitForTableStopLoading();

        await scrollToTableColumn({ tableSelector, columnName, lookupStrategy: nestedLookupStrategy });
        await scrollToTableRow(field.cssSelector, rowNumber);

        await field.writeTableNestedFieldValue({
            value: dateString,
            fieldType: fieldTypes.date,
        });
        await waitForPromises(300, 'Waiting for date to be generated');
    },
);

When(
    /^the user writes a generated date with value "([^"\n\r]*)" from today in the "([^"\n\r]*)" (bound|labelled) nested date field of the selected row in the table field$/,
    async (dateString: string, columnName: string, nestedLookupStrategy: LookupStrategy) => {
        const { cssSelector: tableSelector } = getTableObject();
        const { isFloatingRow, rowNumber } = getSelectedRow();

        const field = new NestedFieldObject({
            tableSelector,
            columnName,
            nestedLookupStrategy,
            rowNumber,
            isFloatingRow,
        });
        await field.waitForTableStopLoading();

        try {
            await scrollToTableColumn({ tableSelector, columnName, lookupStrategy: nestedLookupStrategy });
            await scrollToTableRow(field.cssSelector, rowNumber);
        } catch (error) {
            handleScrollError({ error, rowNum: rowNumber, columnName, field });
        }

        await field.writeTableNestedFieldValue({ value: dateString, fieldType: fieldTypes.date });
        await waitForPromises(300, 'Waiting for date to be generated');
    },
);

Then(
    /^the value of the "([^"\n\r]*)" (bound|labelled) nested date field of the selected row in the table field is a generated date from today with value "(.*)"$/,
    async (columnName: string, nestedLookupStrategy: LookupStrategy, dateString: string) => {
        const { cssSelector: tableSelector } = getTableObject();
        const { isFloatingRow, rowNumber } = getSelectedRow();

        const field = new NestedFieldObject({
            tableSelector,
            columnName,
            nestedLookupStrategy,
            rowNumber,
            isFloatingRow,
        });
        await field.waitForTableStopLoading();

        try {
            await scrollToTableColumn({ tableSelector, columnName, lookupStrategy: nestedLookupStrategy });
            await scrollToTableRow(field.cssSelector, rowNumber);
        } catch (error) {
            handleScrollError({ error, rowNum: rowNumber, columnName, field });
        }

        await field.expectNestedValue({
            toBe: dateString,
            fieldType: fieldTypes.date,
            columnName,
            dateIsGenerated: true,
        });
    },
);

Then(
    /^the "(.*)" (bound|labelled) nested (aggregate|checkbox|date|relative date|dropdown-list|filter select|icon|image|label|link|numeric|progress|reference|select|text) field of the selected row in the table field contains (errors|no error)$/,
    async (
        columnName: string,
        nestedLookupStrategy: LookupStrategy,
        fieldType: NestedFieldTypes,
        expectedValue: string,
    ) => {
        const { cssSelector: tableSelector } = getTableObject();
        const { isFloatingRow, rowNumber } = getSelectedRow();

        const field = new NestedFieldObject({
            tableSelector,
            columnName,
            nestedLookupStrategy,
            rowNumber,
            isFloatingRow,
        });
        await field.waitForTableStopLoading();

        try {
            await scrollToTableColumn({ tableSelector, columnName, lookupStrategy: nestedLookupStrategy });
            await scrollToTableRow(field.cssSelector, rowNumber);
            await field.expectNestedError({ toBe: expectedValue === 'errors', fieldType, columnName });
        } catch (error) {
            handleScrollError({ error, rowNum: rowNumber, columnName, field });
        }
    },
);

When(
    /^the user stores the value of the "([^"\n\r]*)" (bound|labelled) nested (aggregate|checkbox|date|relative date|dropdown-list|filter select|icon|image|label|link|numeric|progress|reference|select|text) field of the selected row in the table field with the key "(.*)"$/,
    async (columnName: string, nestedLookupStrategy: LookupStrategy, fieldType: NestedFieldTypes, storeKey: string) => {
        const { cssSelector: tableSelector } = getTableObject();
        const { isFloatingRow, rowNumber } = getSelectedRow();

        const field = new NestedFieldObject({
            tableSelector,
            columnName,
            nestedLookupStrategy,
            rowNumber,
            isFloatingRow,
        });
        await field.waitForTableStopLoading();

        await scrollToTableColumn({ tableSelector, columnName, lookupStrategy: nestedLookupStrategy });
        await scrollToTableRow(field.cssSelector, rowNumber);

        const elementValue = await field.getNestedValue(fieldType);
        StaticStore.storeObject(storeKey, elementValue);
        await takeScreenshot();
    },
);

Then(
    /^the value of the "([^"\n\r]*)" (bound|labelled) nested date field of the selected row in the table field is a generated date with value "(.*)"$/,
    async (columnName: string, nestedLookupStrategy: LookupStrategy, dateString: string) => {
        const { cssSelector: tableSelector } = getTableObject();
        const { isFloatingRow, rowNumber } = getSelectedRow();

        const field = new NestedFieldObject({
            tableSelector,
            columnName,
            nestedLookupStrategy,
            rowNumber,
            isFloatingRow,
        });
        await field.waitForTableStopLoading();

        await scrollToTableColumn({ tableSelector, columnName, lookupStrategy: nestedLookupStrategy });
        await scrollToTableRow(field.cssSelector, rowNumber);

        await field.expectNestedValue({
            toBe: dateString,
            fieldType: fieldTypes.date,
            columnName,
            dateIsGenerated: true,
        });
    },
);

Then(
    /^the value of the "([^"\n\r]*)" (bound|labelled) nested text field of the selected row in the table field contains the pattern$/,
    async (columnName: string, nestedLookupStrategy: LookupStrategy, expectedValue: string) => {
        const { cssSelector: tableSelector } = getTableObject();
        const { isFloatingRow, rowNumber } = getSelectedRow();

        const field = new NestedFieldObject({
            tableSelector,
            columnName,
            nestedLookupStrategy,
            rowNumber,
            isFloatingRow,
        });
        await field.waitForTableStopLoading();

        const storeValue = StaticStore.getUserdefinedKeyValueFromStore(expectedValue);

        await scrollToTableColumn({
            tableSelector,
            columnName,
            lookupStrategy: nestedLookupStrategy,
        });
        await scrollToTableRow(field.cssSelector, rowNumber);
        await field.expectNestedValueContainsPattern({ toBe: storeValue, columnName });
    },
);

Then(
    /^the value of the "([^"\n\r]*)" (bound|labelled) nested text field of the selected row in the table field is a non locale date with value "(.*)"$/,
    async (columnName: string, nestedLookupStrategy: LookupStrategy, expectedValue: string) => {
        const { cssSelector: tableSelector } = getTableObject();
        const { isFloatingRow, rowNumber } = getSelectedRow();

        const field = new NestedFieldObject({
            tableSelector,
            columnName,
            nestedLookupStrategy,
            rowNumber,
            isFloatingRow,
        });
        await field.waitForTableStopLoading();
        const storeValue = StaticStore.getUserdefinedKeyValueFromStore(expectedValue);
        await scrollToTableColumn({
            tableSelector,
            columnName,
            lookupStrategy: nestedLookupStrategy,
        });
        await scrollToTableRow(field.cssSelector, rowNumber);

        await field.expectNestedTimeStamp({ toBe: storeValue, columnName });
    },
);
