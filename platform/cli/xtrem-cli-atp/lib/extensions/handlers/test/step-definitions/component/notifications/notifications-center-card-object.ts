import AbstractPageObject from '../abstract-page-object';
import { waitForPromises } from '../field/wait-util';
import * as StaticStore from '../static-store';

export class NotificationsCenterCard extends AbstractPageObject {
    constructor() {
        super('[data-testid="xe-notification-content"] [data-testid="xe-notification-card"]');
    }

    async expectNotificationTitleDisplayed(title: string, reverse: boolean = false) {
        const notifications = await this.findAll('[data-testid="xe-notification-title"]');
        let notification;
        let counter = 0;
        // eslint-disable-next-line no-restricted-syntax
        for (const n of notifications) {
            counter += 1;
            if (counter >= 100) {
                break;
            }
            const notificationTitle = await n.getText();
            if (notificationTitle === title) {
                notification = n;
                break;
            }
        }

        if ((!notification && !reverse) || (notification && reverse)) {
            throw new Error(
                `Expected element to be: ${reverse ? 'hidden' : 'displayed'}.\nSelector: ${this.cssSelector}`,
            );
        }
    }

    async selectNotification(title: string) {
        const notifications = await $$('[data-testid="xe-notification-card"]');
        try {
            // eslint-disable-next-line no-restricted-syntax
            for (const notification of notifications) {
                const notificationTitle = await notification.$('[data-testid="xe-notification-title"]').getText();
                if (notificationTitle === title) {
                    await notification.scrollIntoView();
                    StaticStore.storeObject<WebdriverIO.Element>(StaticStore.StoredKeys.NOTIFICATION, notification);
                    return;
                }
            }

            throw new Error(
                `Expected element could not be found: notification card with title "${title}".\nSelector: ${this.cssSelector}`,
            );
        } catch (error) {
            throw new Error(
                `Expected element could not be found: notification card with title "${title}".\nSelector: ${this.cssSelector}`,
            );
        }
    }

    async clickAction(actionValue: string, type: 'header' | 'body') {
        const selectorToUse = `[data-testid="xe-notification-action-${actionValue.replace(/\s/g, '-').toLowerCase()}"]`;
        const storeNotification = StaticStore.getStoredObject<WebdriverIO.Element>(StaticStore.StoredKeys.NOTIFICATION);

        if (type === 'header') {
            const moreActbutton = await storeNotification.$(
                '[data-testid="xe-notification-body-header"] [data-element="action-popover-button"]',
            );
            if (await moreActbutton.isExisting()) {
                await moreActbutton.click();
                await waitForPromises(1200, 'click action');

                const moreActbuttonnotificationButton = await $(`[data-component="action-popover"] ${selectorToUse}`);
                await moreActbuttonnotificationButton.waitForExist({
                    timeout: this.timeoutWaitFor,
                    timeoutMsg: `Expected element could not be found "${actionValue}" notification card action.\nSelector: ${moreActbuttonnotificationButton.selector})`,
                });
                await moreActbuttonnotificationButton.click();
                await waitForPromises(1200, 'click action');
            } else {
                const notificationButton = await storeNotification.$(
                    `[data-testid="xe-notification-body-header"] ${selectorToUse}`,
                );
                await notificationButton.waitForExist({
                    timeout: this.timeoutWaitFor,
                    timeoutMsg: `Expected element could not be found "${actionValue}" notification card action.\nSelector: ${notificationButton.selector})`,
                });
                await notificationButton.click();
                await waitForPromises(1200, 'click action');
            }
        } else {
            const notificationButton = await storeNotification.$(
                `[data-testid="xe-notification-actions"] ${selectorToUse}`,
            );
            await notificationButton.waitForExist({
                timeout: this.timeoutWaitFor,
                timeoutMsg: `Expected element could not be found "${actionValue}" notification card action.\nSelector: ${notificationButton.selector})`,
            });
            await notificationButton.click();
            await waitForPromises(1200, 'click action');
        }
    }

    override async expectValue({ toBe, selectorToUse }: { toBe: string; selectorToUse: string }) {
        const storeNotification = StaticStore.getStoredObject<WebdriverIO.Element>(StaticStore.StoredKeys.NOTIFICATION);
        const element = await storeNotification.$(selectorToUse);
        await element.waitForExist({ timeoutMsg: `Element does not exist.\nSelector: ${selectorToUse}` });
        await element.waitForDisplayed({
            timeoutMsg: `Element not displayed.\nSelector: ${selectorToUse}`,
        });
        let textContent = '';
        try {
            await browser.waitUntil(
                async () => {
                    textContent = (await element.getText()).trim();
                    await waitForPromises(500, 'get text');
                    return (textContent || '').replace(/…/g, '...') === (toBe || '');
                },
                {
                    timeout: this.valueCheckTimeout,
                },
            );
        } catch (error) {
            await this.takePuppeteerPageScreenshot({ captureBeyondViewport: false });
            throw new Error(`Expected value: "${toBe}", actual: "${textContent}".\nSelector: ${selectorToUse}`);
        }
    }
}
