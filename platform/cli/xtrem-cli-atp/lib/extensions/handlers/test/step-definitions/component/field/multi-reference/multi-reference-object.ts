import { Key } from 'webdriverio';
import { ElementContext, fieldTypes, getElementTypeSelector, LookupStrategy } from '../../../step-definitions-utils';
import { ReferenceFieldObject } from '../reference/reference-object';

export class MultiReferenceFieldObject extends ReferenceFieldObject {
    constructor({
        identifier,
        lookupStrategy,
        context,
    }: {
        identifier: string;
        lookupStrategy: LookupStrategy;
        context?: ElementContext;
    }) {
        super({ identifier, lookupStrategy, context, fieldType: 'multi-reference' });
    }

    override async expectValue({
        toBe,
        cssSelector,
        ignoreContext = false,
    }: {
        toBe: string;
        cssSelector?: string;
        ignoreContext?: boolean;
    }) {
        const selectorToUse = this.getSelectorForOperation(cssSelector, ignoreContext);
        let value = '';
        try {
            await browser.waitUntil(
                async () => {
                    const element = await $(selectorToUse);
                    value = (await Promise.all((await element.$$('.e-ui-select-label')).map(e => e.getText())))
                        .map(t => t.trim())
                        .join('|');
                    return (value || '') === (toBe || '');
                },
                { timeout: this.valueCheckTimeout },
            );
        } catch (error) {
            await browser.takeScreenshot();
            throw new Error(`Expected value: ${toBe}, actual: ${value}.\nSelector: ${selectorToUse}`);
        }
    }

    async expectValueToContain(toBe: string, reverse: boolean) {
        const selectorToUse = this.getSelectorForOperation(getElementTypeSelector(fieldTypes.multiReference));
        await (
            await $(selectorToUse).$(`//*[@class='e-ui-select-label'][.//*[contains(text(),'${toBe}')]]`)
        ).waitForDisplayed({ reverse });
    }

    override async clearInput({ ignoreContext }: { ignoreContext?: boolean }) {
        const cssSelector = 'div[role=presentation]';
        await this.expectToAppear({ cssSelector, ignoreContext });
        // eslint-disable-next-line no-await-in-loop
        while ((await (await this.find(cssSelector)).$$('.e-ui-select-label')).length > 0) {
            // eslint-disable-next-line no-await-in-loop
            await this.click('input', ignoreContext);
            // eslint-disable-next-line no-await-in-loop
            await browser.keys(Key.Backspace);
            // eslint-disable-next-line no-await-in-loop
        }
    }
}
