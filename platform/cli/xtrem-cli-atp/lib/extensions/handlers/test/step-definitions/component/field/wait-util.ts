import { Dict } from '@sage/xtrem-core';
import { atpEnv } from '../../step-definitions-utils';

let i = 0;
const trace = !!process.env.XTREM_CUCUMBER_DEBUG;
const slowDownFactor = 1;

const maxPromisesWaits: Dict<number> = {};

export const waitMillis = async (millis: number, reason: string): Promise<void> => {
    i += 1;
    if (trace) console.log(i, 'WAITING MILLIS', millis, 'for', reason);
    await browser.pause(slowDownFactor * millis);
};

export const waitForPromises = async (millis: number, reason: string) => {
    if (trace) console.log(i, 'WAITING FOR PROMISES', millis, 'for', reason);
    await browser.pause(slowDownFactor * millis);

    const t0 = Date.now();
    await (
        await browser.$('#has-pending-promises')
    ).waitForExist({ timeout: atpEnv.timeoutWaitForLoading, reverse: true });
    const waited = Date.now() - t0;
    if (trace) console.log(i, `PROMISES DONE (${waited} ms)`);
    if (reason) maxPromisesWaits[reason] = Math.max(maxPromisesWaits[reason] ?? 0, waited);
};

const dumpWaitTimes = () => {
    console.log('<<<<<<<< PROMISES WAIT TIMES');
    Object.keys(maxPromisesWaits).forEach(key => console.log('WAIT TIME:', key, maxPromisesWaits[key]));
    console.log('>>>>>>>> PROMISES WAIT TIMES');
};

if (trace) process.on('exit', dumpWaitTimes);
