import { Then, When } from '@cucumber/cucumber';
import { ElementContext, LookupStrategy } from '../../../step-definitions-utils';
import { HeaderActionObject, HeaderActionStatus } from './header-action-object';

const getHeaderAction = (identifier: string, lookupStrategy: LookupStrategy, context: ElementContext) =>
    new HeaderActionObject({ identifier, lookupStrategy, context });

When(
    /^the user clicks the "([A-Za-z0-9\s]*)" (bound|labelled) header action button on (the main page|a modal header)$/,
    async (headerActionId: string, lookupStrategy: LookupStrategy, context: ElementContext) => {
        const headerAction = getHeaderAction(headerActionId, lookupStrategy, context);
        await headerAction.click();
    },
);

Then(
    /^the "([A-Za-z0-9\s]*)" (bound|labelled) header action button on (the main page|a modal header) is (disabled|enabled)$/,
    async (
        headerActionId: string,
        lookupStrategy: LookupStrategy,
        context: ElementContext,
        status: HeaderActionStatus,
    ) => {
        const headerAction = getHeaderAction(headerActionId, lookupStrategy, context);
        await headerAction.click();
        try {
            await headerAction.expectStatusToBe(status);
        } catch (error) {
            // eslint-disable-next-line no-console
            console.error(error);
            throw error;
        }
    },
);
