import { Then, When } from '@cucumber/cucumber';
import { LookupStrategy, NestedFieldTypes, takeScreenshot } from '../../../step-definitions-utils';
import pageModel from '../../main-page';
import * as StaticStore from '../../static-store';
import { NestedCellObject } from './nested-cell-object';

When(
    /^the user writes "([^"\n\r]*)" in the "([^"\n\r]*)" (bound|labelled) nested (date|dropdown-list|numeric|reference|select|text) field of the selected (row|floating row) in the nested grid field$/,
    async (
        value: string,
        columnName: string,
        lookupStrategy: LookupStrategy,
        fieldType: NestedFieldTypes,
        rowType: 'row' | 'floating row',
    ) => {
        await pageModel.waitForFinishLoading();
        const storeValue = StaticStore.getUserdefinedKeyValueFromStore(value);
        if (rowType === 'row') {
            const cell = new NestedCellObject({ columnName, lookupStrategy });
            await cell.scrollUntilExists();
            await cell.setNestedCellValue({ value: storeValue, fieldType, columnName });
        } else {
            const cell = new NestedCellObject({ columnName, lookupStrategy, isPinnedToTop: true });
            await cell.scrollUntilExists();
            await cell.setNestedCellValue({ value: storeValue, fieldType, columnName });
        }
    },
);

Then(
    /^the user stores the value of the "([^"\n\r]*)" (bound|labelled) nested (checkbox|date|dropdown-list|numeric|progress|reference|select|text) field of the selected row in the nested grid field with key "([^"\n\r]*)"$/,
    async (columnName: string, lookupStrategy: LookupStrategy, fieldType: NestedFieldTypes, storeKey: string) => {
        await pageModel.waitForFinishLoading();
        const cell = new NestedCellObject({ columnName, lookupStrategy });
        await cell.scrollUntilExists();
        const elementValue = await cell.getNestedValue(fieldType);
        StaticStore.storeObject(storeKey, elementValue);
        await takeScreenshot();
    },
);

Then(
    /^the value of the "([^"\n\r]*)" (bound|labelled) nested (checkbox|date|dropdown-list|label|numeric|progress|reference|select|text) field of the selected row in the nested grid field is "(.*)"$/,
    async (columnName: string, lookupStrategy: LookupStrategy, fieldType: NestedFieldTypes, expectedValue: string) => {
        await pageModel.waitForFinishLoading();
        const field = new NestedCellObject({ columnName, lookupStrategy });
        await field.scrollUntilExists();
        const storeValue = StaticStore.getUserdefinedKeyValueFromStore(expectedValue);
        await field.expectNestedCellValue({ expectedValue: storeValue, fieldType, columnName });
    },
);
