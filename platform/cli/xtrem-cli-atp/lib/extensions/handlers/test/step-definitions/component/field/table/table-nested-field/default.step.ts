/* -----------------------------file to deprecate ------------------------------*/

// import { Then, When } from '@cucumber/cucumber';
// import { LookupStrategy, NestedFieldTypes } from '../../../../step-definitions-utils';

// /**
//  * @deprecated the user selects the row $5 of the table field\nthe user writes "$1" in the "$2" $3 nested $4 field of the selected row in the table field
//  */
// When(
//     /^the user writes "([^"\n\r]*)" in the "([^"\n\r]*)" (bound|labelled) nested (date|dropdown-list|numeric|reference|select|text|filter select) field of (?:(?:row ([0-9]*))|(the floating row)) in the table field?$/,
//     async (
//         value: string,
//         columnName: string,
//         nestedLookupStrategy: LookupStrategy,
//         fieldType: NestedFieldTypes,
//         rowNum: number | null,
//         floatingRow: string | null,
//     ) => {
//         // const { cssSelector: tableSelector } = getTableObject();
//         // const { rowNumber, isPinnedToTop } = getRowDetails({ rowNum, floatingRow });
//         // const field = new NestedFieldObject({
//         //     tableSelector,
//         //     columnName,
//         //     nestedLookupStrategy,
//         //     rowNumber,
//         //     isPinnedToTop,
//         // });
//         // await field.waitForTableStopLoading();
//         // const storeValue = StaticStore.getUserdefinedKeyValueFromStore(value);
//         // await scrollToTableColumn({ tableSelector, columnName, lookupStrategy: nestedLookupStrategy });
//         // await scrollToTableRow(field.cssSelector, rowNum);
//         // await field.writeTableNestedFieldValue({
//         //     value: runtimeParameters.getStringOrParameter(storeValue),
//         //     fieldType,
//         //     floatingRow,
//         // });
//         // await waitForPromises(300, 'Waiting for value to be entered');
//     },
// );

// /**
//  * @deprecated the user selects the row $4 of the table field\nthe value of the "$1" $2 nested $3 field of the selected row in the table field is "$6"
//  */
// Then(
//     /^the value of the "([^"\n\r]*)" (bound|labelled) nested (filter select|icon|image|label|link|numeric|progress|reference|select|text) field of (?:(?:row (\d+))|(the floating row)) in the table field is "([^"\n\r]*)"$/,
//     async (
//         columnName: string,
//         nestedLookupStrategy: LookupStrategy,
//         fieldType: NestedFieldTypes,
//         rowNum: number | null,
//         floatingRow: string | null,
//         expectedValue: string,
//     ) => {
//         // const { cssSelector: tableSelector } = getTableObject();
//         // const { rowNumber, isPinnedToTop } = getRowDetails({ rowNum, floatingRow });
//         // const field = new NestedFieldObject({
//         //     tableSelector,
//         //     columnName,
//         //     nestedLookupStrategy,
//         //     rowNumber,
//         //     isPinnedToTop,
//         // });
//         // await field.waitForTableStopLoading();
//         // const storeValue = StaticStore.getUserdefinedKeyValueFromStore(expectedValue);
//         // await scrollToTableColumn({ tableSelector, columnName, lookupStrategy: nestedLookupStrategy });
//         // await field.expectNestedValue({ toBe: storeValue, fieldType, columnName, rowNumber: rowNum });
//     },
// );

// /**
//  * @deprecated the user selects the row $4 of the table field\nthe value of the "$1" $2 nested $3 field of the selected row in the table field is "$6"
//  */
// Then(
//     /^the value of the "([^"\n\r]*)" (bound|labelled) nested (aggregate|checkbox|date|relative date|dropdown-list) field of (?:(?:row (\d+))|(the floating row)) in the table field is "([^"\n\r]*)"$/,
//     async (
//         columnName: string,
//         nestedLookupStrategy: LookupStrategy,
//         fieldType: NestedFieldTypes,
//         rowNum: number | null,
//         floatingRow: string | null,
//         expectedValue: string,
//     ) => {
//         // const { cssSelector: tableSelector } = getTableObject();
//         // const { rowNumber, isPinnedToTop } = getRowDetails({ rowNum, floatingRow });
//         // const tableElem = await $(tableSelector);
//         // await browser.execute(elem => elem.scrollIntoView(), tableElem);
//         // const field = new NestedFieldObject({
//         //     tableSelector,
//         //     columnName,
//         //     nestedLookupStrategy,
//         //     rowNumber,
//         //     isPinnedToTop,
//         // });
//         // await field.waitForTableStopLoading();
//         // await scrollToTableColumn({ tableSelector, columnName, lookupStrategy: nestedLookupStrategy });
//         // const storeValue = StaticStore.getUserdefinedKeyValueFromStore(expectedValue);
//         // await field.expectNestedValue({
//         //     toBe: storeValue,
//         //     fieldType,
//         //     columnName,
//         //     rowNumber: rowNum,
//         //     cssSelector: fieldType === fieldTypes.checkbox ? 'input' : 'div',
//         // });
//     },
// );

// /**
//  * @deprecated the user selects the row $4 of the table field\nthe user selects "$1" in the "$2" $3 nested field of the selected row in the table field
//  */
// When(
//     /^the user selects "([^"\n\r]*)" in the "([^"\n\r]*)" (bound|labelled) nested field of (?:(?:row ([0-9]*))|(the floating row)) in the table field?$/,
//     async (
//         value: string,
//         columnName: string,
//         nestedLookupStrategy: LookupStrategy,
//         rowNum: number | null,
//         floatingRow: string | null,
//     ) => {
//         // const { cssSelector: tableSelector } = getTableObject();
//         // const { rowNumber, isPinnedToTop } = getRowDetails({ rowNum, floatingRow });
//         // const field = new NestedFieldObject({
//         //     tableSelector,
//         //     columnName,
//         //     nestedLookupStrategy,
//         //     rowNumber,
//         //     isPinnedToTop,
//         // });
//         // await field.waitForTableStopLoading();
//         // const storeValue = StaticStore.getUserdefinedKeyValueFromStore(value);
//         // await field.selectValue(storeValue, columnName);
//     },
// );

// /**
//  * @deprecated the user selects the row $3 of the table field\nthe user clicks the "$1" $2 nested field of the selected row in the table field
//  */
// When(
//     /^the user clicks the "([^"\n\r]*)" (bound|labelled) nested field of (?:(?:row ([0-9]*))|(the floating row)) in the table field$/,
//     async (
//         columnName: string,
//         nestedLookupStrategy: LookupStrategy,
//         rowNum: number | null,
//         floatingRow: string | null,
//     ) => {
//         // const { cssSelector: tableSelector } = getTableObject();
//         // const { rowNumber, isPinnedToTop } = getRowDetails({ rowNum, floatingRow });
//         // const field = new NestedFieldObject({
//         //     tableSelector,
//         //     columnName,
//         //     nestedLookupStrategy,
//         //     rowNumber,
//         //     isPinnedToTop,
//         // });
//         // await field.waitForTableStopLoading();
//         // await scrollToTableColumn({ tableSelector, columnName, lookupStrategy: nestedLookupStrategy });
//         // await scrollToTableRow(field.cssSelector, rowNum);
//         // await field.clickNestedTableField();
//     },
// );

// /**
//  * @deprecated the user selects the row $4 of the table field\nthe "$1" $2 nested $3 field of the selected row in the table field is set to "$6"
//  */
// When(
//     /^the "([^"\n\r]*)" (bound|labelled) nested (switch) field of (?:(?:row ([0-9]*))|(the floating row)) in the table field is set to "(.*)"$/,
//     async (
//         columnName: string,
//         nestedLookupStrategy: LookupStrategy,
//         fieldType: NestedFieldTypes,
//         rowNum: number | null,
//         floatingRow: string | null,
//         value: string,
//     ) => {
//         // const { cssSelector: tableSelector } = getTableObject();
//         // const { rowNumber, isPinnedToTop } = getRowDetails({ rowNum, floatingRow });
//         // const field = new NestedFieldObject({
//         //     tableSelector,
//         //     columnName,
//         //     nestedLookupStrategy,
//         //     rowNumber,
//         //     isPinnedToTop,
//         // });
//         // await field.waitForTableStopLoading();
//         // await scrollToTableColumn({ tableSelector, columnName, lookupStrategy: nestedLookupStrategy });
//         // await scrollToTableRow(field.cssSelector, rowNum);
//         // await field.expectNestedValue({ toBe: value, fieldType, columnName });
//     },
// );

// /**
//  * @deprecated the user selects the row $3 of the table field\nthe user opens the lookup dialog in the "$1" $2 nested reference field of the selected row in the table field
//  */
// When(
//     /^the user opens the lookup dialog in the "([^"\n\r]*)" (bound|labelled) nested reference field of (?:(?:row ([0-9]*))|(the floating row)) in the table field$/,
//     async (
//         columnName: string,
//         nestedLookupStrategy: LookupStrategy,
//         rowNum: number | null,
//         floatingRow: string | null,
//     ) => {
//         // const { cssSelector: tableSelector } = getTableObject();
//         // const { rowNumber, isPinnedToTop } = getRowDetails({ rowNum, floatingRow });
//         // const field = new NestedFieldObject({
//         //     tableSelector,
//         //     columnName,
//         //     nestedLookupStrategy,
//         //     rowNumber,
//         //     isPinnedToTop,
//         // });
//         // await field.waitForTableStopLoading();
//         // await field.openLookupDialog(columnName);
//     },
// );

// /**
//  * @deprecated the user selects the row $3 of the table field\nthe user clicks on the tunnel link in the "$1" $2 nested reference field of the selected row in the table field
//  */
// When(
//     /^the user clicks on the tunnel link in the "([^"\n\r]*)" (bound|labelled) nested reference field of (?:(?:row ([0-9]*))|(the floating row)) in the table field$/,
//     async (
//         columnName: string,
//         nestedLookupStrategy: LookupStrategy,
//         rowNum: number | null,
//         floatingRow: string | null,
//     ) => {
//         // const { cssSelector: tableSelector } = getTableObject();
//         // const { rowNumber, isPinnedToTop } = getRowDetails({ rowNum, floatingRow });
//         // const field = new NestedFieldObject({
//         //     tableSelector,
//         //     columnName,
//         //     nestedLookupStrategy,
//         //     rowNumber,
//         //     isPinnedToTop,
//         // });
//         // await field.waitForTableStopLoading();
//         // await field.clickTunnelLink(columnName);
//     },
// );

// /**
//  * @deprecated the user selects the row $4 of the table field\nthe user writes a generated date with value "$1" in the "$2" $3 nested date field of the selected row in the table field
//  */
// When(
//     /^the user writes a generated date with value "([^"\n\r]*)" in the "([^"\n\r]*)" (bound|labelled) nested date field of row ([0-9]*) in the table field$/,
//     async (dateString: string, columnName: string, nestedLookupStrategy: LookupStrategy, rowNum: number) => {
//         // const { cssSelector: tableSelector } = getTableObject();
//         // const { rowNumber } = getRowDetails({ rowNum });
//         // const field = new NestedFieldObject({
//         //     tableSelector,
//         //     columnName,
//         //     nestedLookupStrategy,
//         //     rowNumber,
//         // });
//         // await field.waitForTableStopLoading();
//         // await scrollToTableColumn({ tableSelector, columnName, lookupStrategy: nestedLookupStrategy });
//         // await scrollToTableRow(field.cssSelector, rowNum);
//         // await field.writeTableNestedFieldValue({ value: dateString, fieldType: fieldTypes.date });
//         // await waitForPromises(300, 'Waiting for date to be generated');
//     },
// );

// /**
//  * @deprecated the user selects the row $4 of the table field\nthe user writes a generated date with value "$1" from today in the "$2" $3 nested date field of the selected row in the table field
//  */
// When(
//     /^the user writes a generated date with value "([^"\n\r]*)" from today in the "([^"\n\r]*)" (bound|labelled) nested date field of row ([0-9]*) in the table field$/,
//     async (dateString: string, columnName: string, nestedLookupStrategy: LookupStrategy, rowNum: number) => {
//         // const { cssSelector: tableSelector } = getTableObject();
//         // const { rowNumber } = getRowDetails({ rowNum });
//         // const field = new NestedFieldObject({
//         //     tableSelector,
//         //     columnName,
//         //     nestedLookupStrategy,
//         //     rowNumber,
//         // });
//         // await field.waitForTableStopLoading();
//         // try {
//         //     await scrollToTableColumn({ tableSelector, columnName, lookupStrategy: nestedLookupStrategy });
//         //     await scrollToTableRow(field.cssSelector, rowNum);
//         // } catch (error) {
//         //     handleScrollError({ error, rowNum, columnName, field });
//         // }
//         // await field.writeTableNestedFieldValue({
//         //     value: dateString,
//         //     fieldType: fieldTypes.date,
//         // });
//         // await waitForPromises(300, 'Waiting for date to be generated');
//     },
// );

// /**
//  * @deprecated the user selects the row $3 of the table field\nthe value of the "$1" $2 nested date field of the selected row in the table field is a generated date from today with value "$4"
//  */
// Then(
//     /^the value of the "([^"\n\r]*)" (bound|labelled) nested date field of row ([0-9]*) in the table field is a generated date from today with value "(.*)"$/,
//     async (columnName: string, nestedLookupStrategy: LookupStrategy, rowNum: number, dateString: string) => {
//         // const { cssSelector: tableSelector } = getTableObject();
//         // const { rowNumber } = getRowDetails({ rowNum });
//         // const field = new NestedFieldObject({
//         //     tableSelector,
//         //     columnName,
//         //     nestedLookupStrategy,
//         //     rowNumber,
//         // });
//         // await field.waitForTableStopLoading();
//         // try {
//         //     await scrollToTableColumn({ tableSelector, columnName, lookupStrategy: nestedLookupStrategy });
//         //     await scrollToTableRow(field.cssSelector, rowNum);
//         // } catch (error) {
//         //     handleScrollError({ error, rowNum, columnName, field });
//         // }
//         // await field.expectNestedValue({
//         //     toBe: dateString,
//         //     fieldType: fieldTypes.date,
//         //     columnName,
//         //     dateIsGenerated: true,
//         // });
//     },
// );

// /**
//  * @deprecated the user selects the row $4 of the table field\nthe "$1" $2 nested $3 field of the selected row in the table field contains $5
//  */
// Then(
//     /^the "(.*)" (bound|labelled) nested (aggregate|checkbox|date|relative date|dropdown-list|filter select|icon|image|label|link|numeric|progress|reference|select|text) field of (?:(?:row ([0-9]*))|(the floating row)) in the table field contains (errors|no error)$/,
//     async (
//         columnName: string,
//         nestedLookupStrategy: LookupStrategy,
//         fieldType: NestedFieldTypes,
//         rowNum: number | null,
//         floatingRow: string | null,
//         expectedValue: string,
//     ) => {
//         // const { cssSelector: tableSelector } = getTableObject();
//         // const { rowNumber, isPinnedToTop } = getRowDetails({ rowNum, floatingRow });
//         // const field = new NestedFieldObject({
//         //     tableSelector,
//         //     columnName,
//         //     nestedLookupStrategy,
//         //     rowNumber,
//         //     isPinnedToTop,
//         // });
//         // await field.waitForTableStopLoading();
//         // try {
//         //     await scrollToTableColumn({ tableSelector, columnName, lookupStrategy: nestedLookupStrategy });
//         //     await scrollToTableRow(field.cssSelector, rowNum);
//         //     await field.expectNestedError({
//         //         toBe: expectedValue === 'errors',
//         //         fieldType,
//         //         columnName,
//         //         rowNumber: rowNum,
//         //     });
//         // } catch (error) {
//         //     handleScrollError({ error, rowNum, columnName, field });
//         // }
//     },
// );

// /**
//  * @deprecated the user selects the row $4 of the table field\nthe user stores the value of the "$1" $2 nested $3 field of the selected row in the table field with the key "$5"
//  */
// Then(
//     /^the user stores the value of the "([^"\n\r]*)" (bound|labelled) nested (aggregate|checkbox|date|relative date|dropdown-list|filter select|icon|image|label|link|numeric|progress|reference|select|text) field of (?:(?:row ([0-9]*))|(the floating row)) in the table field with the key "(.*)"$/,
//     async (
//         columnName: string,
//         nestedLookupStrategy: LookupStrategy,
//         fieldType: NestedFieldTypes,
//         rowNum: number | null,
//         floatingRow: string | null,
//         storeKey: string,
//     ) => {
//         // const { cssSelector: tableSelector } = getTableObject();
//         // const { rowNumber, isPinnedToTop } = getRowDetails({ rowNum, floatingRow });
//         // const field = new NestedFieldObject({
//         //     tableSelector,
//         //     columnName,
//         //     nestedLookupStrategy,
//         //     rowNumber,
//         //     isPinnedToTop,
//         // });
//         // await field.waitForTableStopLoading();
//         // await scrollToTableColumn({ tableSelector, columnName, lookupStrategy: nestedLookupStrategy });
//         // if (rowNum) await scrollToTableRow(field.cssSelector, rowNum);
//         // const elementValue = await field.getNestedValue(fieldType);
//         // StaticStore.storeObject(storeKey, elementValue);
//         // await takeScreenshot();
//     },
// );

// /**
//  * @deprecated the user selects the row $3 of the table field\nthe value of the "$1" $2 nested date field of the selected row in the table field is a generated date with value "$4"
//  */
// Then(
//     /^the value of the "([^"\n\r]*)" (bound|labelled) nested date field of row ([0-9]*) in the table field is a generated date with value "(.*)"$/,
//     async (columnName: string, nestedLookupStrategy: LookupStrategy, rowNum: number, dateString: string) => {
//         // const { cssSelector: tableSelector } = getTableObject();
//         // const { rowNumber } = getRowDetails({ rowNum });
//         // const field = new NestedFieldObject({
//         //     tableSelector,
//         //     columnName,
//         //     nestedLookupStrategy,
//         //     rowNumber,
//         // });
//         // await field.waitForTableStopLoading();
//         // await scrollToTableColumn({ tableSelector, columnName, lookupStrategy: nestedLookupStrategy });
//         // await scrollToTableRow(field.cssSelector, rowNum);
//         // await field.expectNestedValue({
//         //     toBe: dateString,
//         //     fieldType: fieldTypes.date,
//         //     columnName,
//         //     dateIsGenerated: true,
//         // });
//     },
// );
