import * as utils from '../step-definitions-utils';
import AbstractPageObject from './abstract-page-object';
import { waitForPromises } from './field/wait-util';
import { testGlobals } from './test-globals';

export class DropDownMenuObject extends AbstractPageObject {
    private readonly moreActionsBtn: string = '[data-component="action-popover-button"]';

    private readonly actionPopOver: string = '[data-component="action-popover"]';

    private readonly popOverMenuItem: string = `${this.actionPopOver} button[type="button"]`;

    // eslint-disable-next-line
    closePopOverMobile = async () => {
        const mobileBg = '.e-action-popover-mobile-background';
        const mobileBgElement = await $(mobileBg);
        const isMobileBgDisplayed = await mobileBgElement.isDisplayed();
        if (isMobileBgDisplayed) {
            await mobileBgElement.click({ x: 10, y: -10 });
        }
    };

    async clickMoreActionsMenu() {
        const moreActionsElt = await this.find(this.moreActionsBtn);
        await utils.waitForElementToExist({ name: 'More Actions Button', selector: `${moreActionsElt.selector}` });
        await utils.waitForElementToBeDisplayed({
            name: 'More Actions Button',
            selector: `${moreActionsElt.selector}`,
        });
        await moreActionsElt.click();
        await waitForPromises(100, 'Waiting for click on More Actions button');
        await utils.waitForElementToBeDisplayed({ name: 'Action PopOver', selector: this.actionPopOver });
    }

    async selectMenuAction(actionName: string, menuContext: string = '.e-header-dropdown-action-container') {
        // Single Action
        const singleAction = await this.find(`${menuContext} > button[data-testid="e-popover-action-single-button"]`);
        const isSingleAction = await singleAction.isExisting();

        if (isSingleAction) {
            const singleActionLabel = await singleAction.getAttribute('aria-label');
            if (utils.formatString(singleActionLabel) === utils.formatString(actionName)) {
                await singleAction.click();
                return;
            }
            throw new Error(`Expected ${actionName} dropdown action label, but got ${singleActionLabel}`);
        }

        // More Actions
        const moreActionsElt = await this.find(this.moreActionsBtn);

        await utils.waitForElementToExist({ name: 'More Actions Button', selector: `${moreActionsElt.selector}` });
        await utils.waitForElementToBeDisplayed({
            name: 'More Actions Button',
            selector: `${moreActionsElt.selector}`,
        });

        await moreActionsElt.click();
        await waitForPromises(100, 'Waiting for click on More Actions button');

        await utils.waitForElementToBeDisplayed({ name: 'Action PopOver', selector: this.actionPopOver });

        const menuItems = await this.findAll(this.popOverMenuItem, true);

        // eslint-disable-next-line no-restricted-syntax
        for (const item of menuItems) {
            const itemName = await item.getText();
            if (utils.formatString(itemName) === utils.formatString(actionName)) {
                await browser.pause(200);
                await item.click();
                return;
            }
        }
        throw new Error(
            `Expected element could not be found: "${actionName}" action.\nSelector: ${moreActionsElt.selector.toString()}`,
        );
    }

    async expectDropdownMenuActionToBeEnabled(actionLabel: string, expectedState: utils.EnabledState) {
        await this.loseFocus();
        await this.click(this.moreActionsBtn);
        await waitForPromises(300, 'Wait for the robot to click the more action button');
        const menuItems = await this.findAll(this.popOverMenuItem, true);
        let found = false;
        // eslint-disable-next-line no-restricted-syntax
        for (const item of menuItems) {
            const actionText = await item.getText();
            // Check if the action text is the same as the action label
            if (utils.formatString(actionText) === utils.formatString(actionLabel)) {
                found = true;
                const ariaDisabled = await item.getAttribute('aria-disabled');
                const disabled = await item.getAttribute('disabled');
                const currentState: utils.EnabledState =
                    ariaDisabled === 'true' || disabled === '' ? 'disabled' : 'enabled';
                // Check if the current state is the same as the expected state
                if (currentState !== expectedState) {
                    throw new Error(`Expected element to be ${expectedState}.\nSelector: ${item.selector.toString()}`);
                }
                if (testGlobals.device === 'mobile') {
                    await this.closePopOverMobile();
                    await waitForPromises(100, 'Wait for the robot to close the menu more action');
                } else {
                    // close the popup desktop
                    await this.click(this.moreActionsBtn);
                    await waitForPromises(300, 'Wait for the robot to click the more action button');
                }
                return;
            }
        }
        if (found === false) {
            throw new Error(`Expected element could not be found: "${actionLabel}"`);
        }
    }

    async expectDropdownMenuActionToBeDisplayed(actionLabel: string, reverse: boolean) {
        await this.loseFocus();

        await waitForPromises(100, 'Wait for the robot to click the more action button');
        const $moreActionsBtn = await this.find(this.moreActionsBtn);
        const $popOver = await this.find(this.actionPopOver, true);
        const $popOverExists = await $popOver.isExisting();
        if (!$popOverExists) {
            await $moreActionsBtn.moveTo();
            await $moreActionsBtn.click();
            await waitForPromises(300, 'Click more action button');
        }

        const menuItems = await this.findAll(this.popOverMenuItem, true);

        await utils.expectElementToBeDisplayed({
            selector: menuItems.selector.toString(),
            reverse: false,
            name: 'Menu Items',
        });

        // eslint-disable-next-line no-restricted-syntax
        for (const item of menuItems) {
            const actionText = await item.getText();
            await waitForPromises(500, 'Wait for the robot to get the text of the menu item');

            if (utils.formatString(actionText) === utils.formatString(actionLabel)) {
                if (reverse) {
                    throw new Error(
                        `Expected "${actionLabel}" dropdown action to be ${reverse ? 'hidden' : 'displayed'}\nSelector: ${this.cssSelector}`,
                    );
                }
                if (testGlobals.device === 'mobile') {
                    await this.closePopOverMobile();
                    await waitForPromises(300, 'Close mobile popover');
                    return;
                }
                await browser.execute(`document.querySelector('${$moreActionsBtn.selector}').click()`);

                await utils.waitForElementNotToExist('Action PopOver', this.actionPopOver);

                return;
            }
        }

        if (!reverse) {
            throw new Error(`Expected element could not be found: ${actionLabel}\nSelector: ${this.cssSelector}`);
        }

        if (testGlobals.device === 'mobile') {
            await this.closePopOverMobile();
        }
    }
}
