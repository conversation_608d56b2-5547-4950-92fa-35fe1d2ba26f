import { Then, When } from '@cucumber/cucumber';
import { fieldTypes } from '../../../step-definitions-utils';
import * as StaticStore from '../../static-store';
import { CalendarFieldObject } from './calendar-object';

// ----------
// Static store field steps
// ----------
Then(/^the user switches to the "(Day|Week|Month|3 days)" view on the calendar field$/, async (view: string) => {
    const field = <CalendarFieldObject>StaticStore.getStoredField(fieldTypes.calendar);
    await field.selectView(view === '3 days' ? 'Three' : view);
});

Then(
    /^the calendar field in "(Day|Week|Month|3 days)" view is set to "(.*)"$/,
    async (view: string, expectedDate: string) => {
        const field = <CalendarFieldObject>StaticStore.getStoredField(fieldTypes.calendar);
        await field.checkView(view === '3 days' ? 'Three' : view, expectedDate);
    },
);

Then(/^the user steps to "(.*)" on the calendar field$/, async (targetDate: string) => {
    const field = <CalendarFieldObject>StaticStore.getStoredField(fieldTypes.calendar);
    await field.stepToDate(targetDate);
});

When(
    /^the user navigates "(.*)" time (backward|forward) on the calendar field$/,
    async (steps: number, direction: string) => {
        const field = <CalendarFieldObject>StaticStore.getStoredField(fieldTypes.calendar);
        await field.stepInDirection(steps, direction === 'backward' ? 'prev' : 'next');
    },
);

Then(/^the user checks if there are events for "(.*)" on the calendar field$/, async (targetDate: string) => {
    const field = <CalendarFieldObject>StaticStore.getStoredField(fieldTypes.calendar);
    await field.checkEvents(targetDate);
    await browser.takeScreenshot();
});

Then(
    /^the value of event number "([^"\n\r]*)" for "([^"\n\r]*)" on the calendar field is "(.*)"$/,
    async (eventNumber: string, targetDate: string, eventValue: string) => {
        const field = <CalendarFieldObject>StaticStore.getStoredField(fieldTypes.calendar);
        await field.checkEventValue({ eventNumber, expectedValue: eventValue, targetDate });
        await browser.takeScreenshot();
    },
);
