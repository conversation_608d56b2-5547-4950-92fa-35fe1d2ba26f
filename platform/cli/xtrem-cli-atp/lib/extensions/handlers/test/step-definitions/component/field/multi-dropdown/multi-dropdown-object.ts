import { ElementContext, FieldTypes, LookupStrategy } from '../../../step-definitions-utils';
import { FieldObject } from '../field-object';
import { waitMillis } from '../wait-util';

export class MultiDropdownFieldObject extends FieldObject {
    constructor({
        identifier,
        lookupStrategy,
        context,
        fieldType,
    }: {
        identifier: string;
        lookupStrategy: LookupStrategy;
        context?: ElementContext;
        fieldType?: string;
    }) {
        super({ fieldType: fieldType ?? 'multi-dropdown', identifier, lookupStrategy, context });
    }

    override async select(value: string) {
        await super.select(value);
    }

    override async expectHelperTextToBeDisplayed(reverse = false) {
        await this.expectToBeDisplayed(`${this.cssSelector} ${this.helperTextSelector}`, reverse);
    }

    override async write({
        content,
        cssSelector = 'input',
        ignoreContext = false,
    }: {
        content: string;
        cssSelector?: string;
        ignoreContext?: boolean;
    }) {
        await super.write({ content, cssSelector, ignoreContext });
        await wait<PERSON><PERSON><PERSON>(2000, 'after multi-dropdown input');
    }

    override async expectValue({
        toBe,
        cssSelector = 'input',
        ignoreContext = false,
    }: {
        toBe: string;
        cssSelector?: string;
        ignoreContext?: boolean;
    }) {
        const selectorToUse = `${cssSelector} input`;
        await super.expectValue({ toBe, cssSelector: selectorToUse, ignoreContext });
    }

    override async setElementValueToStore({
        keyValue,
        fieldType,
        TypeSelector,
    }: {
        keyValue: string;
        fieldType: FieldTypes;
        TypeSelector: string;
    }) {
        const selectorToUse = `${TypeSelector} input`;
        await super.setElementValueToStore({ keyValue, fieldType, TypeSelector: selectorToUse });
    }
}
