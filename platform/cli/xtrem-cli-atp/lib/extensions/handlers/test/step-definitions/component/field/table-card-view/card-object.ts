/* eslint-disable @typescript-eslint/naming-convention */
import { EnabledState, takeScreenshot } from '../../../step-definitions-utils';
import AbstractPageObject from '../../abstract-page-object';
import * as StaticStore from '../../static-store';
import { testGlobals } from '../../test-globals';
import { NestedGridObject } from '../nested-grid/nested-grid-object';
import { TableObject } from '../table/table-object';
import { TableExpect } from '../table/tableMethods/table-expect';
import { waitForPromises } from '../wait-util';

const getCardSelector = (cardNumber: any | undefined, label?: string) => {
    if (cardNumber !== undefined) {
        return `div:nth-child(${cardNumber})>[data-testid="e-card"]`;
    }
    const labelText = StaticStore.getUserdefinedKeyValueFromStore(label as string);
    return `[data-label="${labelText}"][data-testid="e-card"]`;
};

export interface ICardObject {
    parent: TableObject | NestedGridObject;
    cardNumber?: any | undefined;
    label?: string;
}

export class CardObject extends AbstractPageObject {
    private cardNumber: any | undefined;

    private label?: string;

    constructor({ parent, cardNumber, label }: ICardObject) {
        super(`${parent.cssSelector} ${getCardSelector(cardNumber, label)}`);
        this.cardNumber = cardNumber;
        this.label = label;
    }

    override async click() {
        const element = await this.get();
        await element.waitForExist({
            timeout: this.timeoutWaitFor,
            timeoutMsg: `Expected card "${this.cardNumber}" could not be found.\nSelector: ${this.cssSelector})`,
        });

        await element.scrollIntoView();
        await element.click();
        await waitForPromises(500, 'Waiting for click on card');
    }

    async tickCheckbox(selection: 'ticks' | 'unticks') {
        const element = await this.get();

        if (this.cardNumber !== undefined) {
            await element.waitForExist({
                timeout: this.timeoutWaitFor,
                timeoutMsg: `Expected card "${this.cardNumber}" could not be found.\nSelector: ${this.cssSelector})`,
            });
        } else {
            await element.waitForExist({
                timeout: this.timeoutWaitFor,
                timeoutMsg: `Expected card with text "${this.label}" could not be found.\nSelector: ${this.cssSelector})`,
            });
        }

        const inputElt = await $(`${this.cssSelector} input`);
        await inputElt.waitForExist({
            timeout: this.timeoutWaitFor,
            timeoutMsg: `Expected checkbox could not be found.\nSelector: ${this.cssSelector} input`,
        });
        await inputElt.scrollIntoView();

        const result = await inputElt.isSelected();

        if ((selection === 'ticks') !== result) {
            await inputElt.click();
            await waitForPromises(500, 'Waiting for click on card');
        } else {
            throw new Error(
                `Checkbox is already ${
                    selection === 'ticks' ? 'ticked' : 'unticked'
                }.\nSelector: ${inputElt.selector.toString()}`,
            );
        }
    }

    async clickButtonAction(actionName: string) {
        // Check card existence
        await this.checkCardExistence();

        // find and click the action button
        const actionSelector = `${this.cssSelector} button[aria-label="${actionName}"]`;
        const actionBtn = await this.find(actionSelector, true);

        if ((await actionBtn.isExisting()) && (await actionBtn.isDisplayed())) {
            await actionBtn.click();
            await waitForPromises(500, 'popover button click');
        } else {
            throw new Error(`Expected element could not be found: "${actionName}" .\nSelector: ${actionSelector}`);
        }
    }

    async checkCardExistence() {
        const element = await this.get();

        if (this.cardNumber !== undefined) {
            await element.waitForExist({
                timeout: this.timeoutWaitFor,
                timeoutMsg: `Expected card "${this.cardNumber}" could not be found.\nSelector: ${this.cssSelector}`,
            });
        } else {
            await element.waitForExist({
                timeout: this.timeoutWaitFor,
                timeoutMsg: `Expected card with text "${this.label}" could not be found.\nSelector: ${this.cssSelector}`,
            });
        }
    }

    async expectActionButtonIsDisplayed(actionName: string, reverse = false) {
        // Check card existence
        await this.checkCardExistence();

        // find and check state of the action button
        const actionButtonSelector = `${this.cssSelector} button[aria-label="${actionName}"]`;
        await this.expectToBeDisplayed(actionButtonSelector, reverse);
    }

    async clickDropdownAction(actionName: string) {
        // Check card existence
        await this.checkCardExistence();
        await this.scrollTo({ selector: this.cssSelector, ignoreContext: true });

        const popoverContainerSelector =
            testGlobals.device === 'mobile'
                ? '[data-element="action-popover-button"]'
                : '[data-component="action-popover-button"]';

        const actionPopOverBtnSelector = await this.find(`${this.cssSelector} ${popoverContainerSelector}`, true);

        if ((await actionPopOverBtnSelector.isExisting()) && (await actionPopOverBtnSelector.isDisplayed())) {
            const popOver = '[data-component="action-popover"]';
            const popOverElt = await browser.$(popOver);
            if (!(await popOverElt.isExisting())) {
                await actionPopOverBtnSelector.click();
                await waitForPromises(500, 'popover button click');
            }

            await popOverElt.waitForDisplayed({
                timeout: this.timeoutWaitFor,
                timeoutMsg: `Expected element to be displayed: ${popOver}.\nSelector: ${popOverElt.selector.toString()}`,
            });

            const popOverBtns = await this.findAll(`${popOver} [type="button"]`, true);
            await popOverElt.scrollIntoView();
            const btnTitles = await Promise.all(popOverBtns.map(btn => btn.getText()));
            const index = btnTitles.findIndex(title => title.toLowerCase() === actionName.toLowerCase());

            if (index !== -1) {
                await TableExpect.expectButtonEnabled(popOverBtns[index]);
                await popOverBtns[index].click();
            } else {
                throw new Error(
                    `Expected dropdown action "${actionName}" could not be found.\nSelector: ${actionPopOverBtnSelector.selector.toString()}`,
                );
            }
        } else {
            const actionSelector = `${this.cssSelector} button[aria-label="${actionName}"]`;
            const actionBtn = await this.find(actionSelector, true);
            await actionBtn.scrollIntoView();

            if ((await actionBtn.isExisting()) && (await actionBtn.isDisplayed())) {
                await TableExpect.expectButtonEnabled(actionBtn);
                await actionBtn.parentElement().click();
                await waitForPromises(500, 'popover button click');
            } else {
                throw new Error(
                    `Expected dropdown action "${actionName}" could not be found.\nSelector: ${actionSelector}`,
                );
            }
        }
    }

    async expectDropdownActionToBeEnabled(actionName: string, expectedState: EnabledState) {
        const element = await this.get();

        if (this.cardNumber !== undefined) {
            await element.waitForExist({
                timeout: this.valueCheckTimeout,
                timeoutMsg: `Expected card "${this.cardNumber}" could not be found.\nSelector: ${this.cssSelector})`,
            });
        } else {
            await element.waitForExist({
                timeout: this.valueCheckTimeout,
                timeoutMsg: `Expected card with text "${this.label}" could not be found.\nSelector: ${this.cssSelector})`,
            });
        }

        await this.scrollTo({ selector: this.cssSelector, ignoreContext: true });

        const popoverContainerSelector =
            testGlobals.device === 'mobile'
                ? '[data-element="action-popover-button"]'
                : '[data-component="action-popover-button"]';

        const actionPopOverBtnSelector = await $(`${this.cssSelector} ${popoverContainerSelector}`);
        if (await actionPopOverBtnSelector.isClickable()) {
            const popOver = '[data-component="action-popover"]';
            const $popOver = await browser.$(popOver);

            await browser.waitUntil(
                async () => {
                    if (!((await $popOver.isExisting()) && (await $popOver.isDisplayed()))) {
                        await actionPopOverBtnSelector.click();
                        await waitForPromises(500, 'popover button click');
                    }
                    const isDisplayed = await $popOver.isDisplayed();
                    return isDisplayed;
                },
                {
                    timeout: this.timeoutWaitFor,
                    timeoutMsg: `Expected element to be displayed: ${popOver}.\nSelector: ${popOver}`,
                },
            );

            const popOverBtns = await this.findAll(`${popOver} [type="button"]`, true);
            const btnTitles = await Promise.all(popOverBtns.map(btn => btn.getText()));
            const index = btnTitles.findIndex(title => title.toLowerCase() === actionName.toLowerCase());

            if (index !== -1) {
                const ariaDisabled = await popOverBtns[index].getAttribute('aria-disabled');
                const currentState: EnabledState = ariaDisabled && ariaDisabled === 'true' ? 'disabled' : 'enabled';
                if (currentState !== expectedState) {
                    await takeScreenshot();
                    throw new Error(
                        `Expected dropdown action to be ${expectedState}.\nSelector: ${popOverBtns[
                            index
                        ].selector.toString()}`,
                    );
                }
            } else {
                await takeScreenshot();
                throw new Error(`Expected dropdown action "${actionName}" could not be found`);
            }
        }
    }
}
