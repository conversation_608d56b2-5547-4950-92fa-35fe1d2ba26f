import { ElementContext, ExpandedOrCollapsed, getContextSelector } from '../../../step-definitions-utils';
import AbstractPageObject from '../../abstract-page-object';

export class AccordionObject extends AbstractPageObject {
    constructor(context: ElementContext) {
        super(getContextSelector(context));
    }

    async setAccordionSelectorByTitle(title: string) {
        const accordionTitleSelector = `${(await this.get()).selector.toString()} h3[data-element="accordion-title"]`;
        const accordionTitleElements = await this.findAll(accordionTitleSelector, true);
        let parentSelector = '';
        let sectionElement: WebdriverIO.Element;

        const values = await Promise.all(
            accordionTitleElements.map(async el => {
                const val = await el.getText();
                return val;
            }),
        );

        const index = values.findIndex(val => val === title);
        if (index < 0) {
            throw new Error(
                `Expected element could not be found: "${title}" accordion.\nSelector: ${accordionTitleSelector}`,
            );
        }
        sectionElement = await accordionTitleElements[index].parentElement();

        await browser.waitUntil(
            async () => {
                if ((await sectionElement.getAttribute('data-component')) === 'accordion') {
                    parentSelector = ` div[id="${await sectionElement.getAttribute('id')}"]`;
                    return true;
                }
                sectionElement = await sectionElement.parentElement();
                return false;
            },
            { timeout: this.valueCheckTimeout },
        );

        this.cssSelector += parentSelector;
    }

    async expectAccordionExpandedCollapsed(toggleStatus: 'expanded' | 'collapsed') {
        const accordionTitleSelector = `${(
            await this.get()
        ).selector.toString()} div[data-element="accordion-title-container"]`;
        const accordionTitleElement = await this.find(accordionTitleSelector, true);
        if ((await accordionTitleElement.getAttribute('aria-expanded')) !== (toggleStatus === 'expanded').toString()) {
            throw new Error(`Expected element to be ${toggleStatus}.\nSelector: ${accordionTitleSelector}`);
        }
    }

    async toggle(toggle: ExpandedOrCollapsed) {
        const accordionTitleElement = await this.find(
            `${(await this.get()).selector.toString()} div[data-element="accordion-title-container"]`,
            true,
        );
        if (
            (await accordionTitleElement.getAttribute('aria-expanded')) ===
            (!(toggle === ExpandedOrCollapsed.EXPANDED)).toString()
        ) {
            await accordionTitleElement.scrollIntoView();
            await browser.pause(100);
            await accordionTitleElement.click();
        }
    }
}
