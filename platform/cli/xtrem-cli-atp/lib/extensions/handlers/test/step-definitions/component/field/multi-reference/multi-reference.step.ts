import { Then, When } from '@cucumber/cucumber';
import { fieldTypes } from '../../../step-definitions-utils';
import * as StaticStore from '../../static-store';
import { testGlobals } from '../../test-globals';
import { MultiReferenceFieldObject } from './multi-reference-object';

When(/^the user clicks the lookup button of the multi reference field$/, async () => {
    const field = <MultiReferenceFieldObject>StaticStore.getStoredField(fieldTypes.multiReference);
    await field.clickLookupButton();
    await field.expectLookupDialogToAppear();
});

Then(
    /^at least the following list of options is displayed for the multi reference field: "(.*)"$/,
    async (value: string) => {
        if (testGlobals.device === 'mobile' || testGlobals.device === 'tablet') {
            throw new Error(
                'Comparing a list of options for a multi reference field is not currently supported in mobile/tablet mode.',
            );
        }
        const field = <MultiReferenceFieldObject>StaticStore.getStoredField(fieldTypes.multiReference);
        await field.selectDropDown.expectOptionsToBe(value);
    },
);

Then(
    /^the value of the multi reference field (contains|does not contain) the following: "(.*)"$/,
    async (contains: string, value: string) => {
        const field = <MultiReferenceFieldObject>StaticStore.getStoredField(fieldTypes.multiReference);
        await field.expectValueToContain(value, contains === 'does not contain');
    },
);
